/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: test cases for yang diff feature
 * Author: f00633892
 * Create: 2022-9-19
 */

#include "yang/yang_common_st.h"

static const char *g_vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch.json";
static const char *g_edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch.json";

static const char *g_baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData.json";

static const char *g_diffCfgJsonTree = R"({"max_record_count":1000, "auto_increment":101, "yang_model":1})";

static const char *g_lltJsonTree = R"({"yang_model":1})";

static void InitPartialDiffBasicDataAsync(
    GmcConnT *conn, GmcStmtT *root_stmt, const char *vertexLabel, const char *edgeLabel, const char *jsonFile)
{
    if (edgeLabel == NULL) {
        LltCreateVertexLabelAsync(root_stmt, vertexLabel, g_diffCfgJsonTree);
    } else {
        LltCreateVertexAndEdgeLabelAsync(root_stmt, vertexLabel, edgeLabel, g_diffCfgJsonTree);
    }
    std::atomic_uint32_t step{0};
    uint32_t expectStep = 0;
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    ASSERT_EQ(GMERR_OK, GmcTransStartAsync(conn, &config, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, ++expectStep);
    LltBatchExecuteByJsonAsync(conn, root_stmt, "T0", (GetFileContext(jsonFile)).c_str(), g_lltJsonTree);
    ASSERT_EQ(GMERR_OK, GmcTransCommitAsync(conn, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, ++expectStep);
}

TEST_F(StYang, PartialDiffBaseTest1Async)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");

    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(InitPartialDiffBasicDataAsync(
        asyncConn, asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_baseDataJsonTree));

    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_delete_root.json";
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    static vector<string> treeExpectDiff = {
        "T0:remove[(NULL),(priKey(ID:1))]\n"
        "T0.F0:remove(1)\n"
        "T0.F1:remove(3)\n"
        "T0.nodeName:remove(T0-00)\n"
        "T0.T0::T1:remove\n"
        "T0::T1.F0:remove(1)\n"
        "T0::T1.F1:remove(8)\n"
        "T0::T1.nodeName:remove(T1-00)\n"
        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:1))]\n"
        "T0::T2.ID:remove(1)\n"
        "T0::T2.F1:remove(3)\n"
        "T0::T2.F2:remove(5)\n"
        "T0::T2.nodeName:remove(T2-00)\n"
        "T0::T2.T0::T2::T3:remove\n"
        "T0::T2::T3.F0:remove(1)\n"
        "T0::T2::T3.F1:remove(3)\n"
        "T0::T2::T3.F2:remove(9)\n"
        "T0::T2::T3.nodeName:remove(T3-00)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:1,F0:1))]\n"
        "T0::T2::T4.ID:remove(1)\n"
        "T0::T2::T4.F1:remove(3)\n"
        "T0::T2::T4.F2:remove(1)\n"
        "T0::T2::T4.F3:remove(8)\n"
        "T0::T2::T4.nodeName:remove(T4-00)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0::T2::T4.ID:remove(2)\n"
        "T0::T2::T4.F1:remove(3)\n"
        "T0::T2::T4.F2:remove(2)\n"
        "T0::T2::T4.F3:remove(2)\n"
        "T0::T2::T4.nodeName:remove(T4-01)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T0::T2::T4.ID:remove(3)\n"
        "T0::T2::T4.F1:remove(3)\n"
        "T0::T2::T4.F2:remove(3)\n"
        "T0::T2::T4.F3:remove(9)\n"
        "T0::T2::T4.nodeName:remove(T4-02)\n"
        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0::T2.ID:remove(2)\n"
        "T0::T2.F1:remove(4)\n"
        "T0::T2.F2:remove(6)\n"
        "T0::T2.nodeName:remove(T2-01)\n"
        "T0::T2.T0::T2::T3:remove\n"
        "T0::T2::T3.F0:remove(1)\n"
        "T0::T2::T3.F1:remove(4)\n"
        "T0::T2::T3.F2:remove(9)\n"
        "T0::T2::T3.nodeName:remove(T3-01)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:2,F0:1))]\n"
        "T0::T2::T4.ID:remove(4)\n"
        "T0::T2::T4.F1:remove(4)\n"
        "T0::T2::T4.F2:remove(1)\n"
        "T0::T2::T4.F3:remove(18)\n"
        "T0::T2::T4.nodeName:remove(T4-03)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:2,F0:2), preKey(PID:2,F0:1))]\n"
        "T0::T2::T4.ID:remove(5)\n"
        "T0::T2::T4.F1:remove(4)\n"
        "T0::T2::T4.F2:remove(2)\n"
        "T0::T2::T4.F3:remove(81)\n"
        "T0::T2::T4.nodeName:remove(T4-04)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:2,F0:3), preKey(PID:2,F0:2))]\n"
        "T0::T2::T4.ID:remove(6)\n"
        "T0::T2::T4.F1:remove(4)\n"
        "T0::T2::T4.F2:remove(3)\n"
        "T0::T2::T4.F3:remove(22)\n"
        "T0::T2::T4.nodeName:remove(T4-05)\n"
        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T0::T2.ID:remove(3)\n"
        "T0::T2.F1:remove(5)\n"
        "T0::T2.F2:remove(7)\n"
        "T0::T2.nodeName:remove(T2-02)\n"
        "T0::T2.T0::T2::T3:remove\n"
        "T0::T2::T3.F0:remove(1)\n"
        "T0::T2::T3.F1:remove(5)\n"
        "T0::T2::T3.F2:remove(9)\n"
        "T0::T2::T3.nodeName:remove(T3-02)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:3,F0:1))]\n"
        "T0::T2::T4.ID:remove(7)\n"
        "T0::T2::T4.F1:remove(5)\n"
        "T0::T2::T4.F2:remove(1)\n"
        "T0::T2::T4.F3:remove(5)\n"
        "T0::T2::T4.nodeName:remove(T4-06)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:3,F0:2), preKey(PID:3,F0:1))]\n"
        "T0::T2::T4.ID:remove(8)\n"
        "T0::T2::T4.F1:remove(5)\n"
        "T0::T2::T4.F2:remove(2)\n"
        "T0::T2::T4.F3:remove(6)\n"
        "T0::T2::T4.nodeName:remove(T4-07)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:3,F0:3), preKey(PID:3,F0:2))]\n"
        "T0::T2::T4.ID:remove(9)\n"
        "T0::T2::T4.F1:remove(5)\n"
        "T0::T2::T4.F2:remove(3)\n"
        "T0::T2::T4.F3:remove(7)\n"
        "T0::T2::T4.nodeName:remove(T4-08)\n"
        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
        "T0::T2.ID:remove(4)\n"
        "T0::T2.F1:remove(6)\n"
        "T0::T2.F2:remove(8)\n"
        "T0::T2.nodeName:remove(T2-03)\n"
        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
        "T0::T2.ID:remove(5)\n"
        "T0::T2.F1:remove(7)\n"
        "T0::T2.F2:remove(9)\n"
        "T0::T2.nodeName:remove(T2-04)\n"
        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:6), preKey(PID:1,F0:5))]\n"
        "T0::T2.ID:remove(6)\n"
        "T0::T2.F1:remove(8)\n"
        "T0::T2.F2:remove(10)\n"
        "T0::T2.nodeName:remove(T2-05)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
    // 校验审计日志
    StCommonVerifyLogContent("./log/secure/sgmserver/sgmserver.log", "fetch diff", 1);

    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

// 测试插入多个vertex生成diff
TEST_F(StYang, PartialDiffMultiVertexCreateOperationAsync)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");

    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(InitPartialDiffBasicDataAsync(
        asyncConn, asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_baseDataJsonTree));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_create_diff.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> treeExpectDiff = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:7), preKey(PID:1,F0:6)),(NULL)]\n"
                                     "T0::T2.ID:create(7)\n"
                                     "T0::T2.F1:create(9)\n"
                                     "T0::T2.F2:create(11)\n"
                                     "T0::T2.nodeName:create(T2-06)\n"
                                     "T0::T2.T0::T2::T3:create\n"
                                     "T0::T2::T3.F0:create(1)\n"
                                     "T0::T2::T3.F1:create(9)\n"
                                     "T0::T2::T3.F2:create(11)\n"
                                     "T0::T2::T3.nodeName:create(T3-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:7,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(10)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(1)\n"
                                     "T0::T2::T4.nodeName:create(T4-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:7,F0:2), preKey(PID:7,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(11)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(2)\n"
                                     "T0::T2::T4.nodeName:create(T4-04)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:7,F0:3), preKey(PID:7,F0:2)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(12)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(3)\n"
                                     "T0::T2::T4.nodeName:create(T4-05)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:8), preKey(PID:1,F0:7)),(NULL)]\n"
                                     "T0::T2.ID:create(8)\n"
                                     "T0::T2.F1:create(10)\n"
                                     "T0::T2.F2:create(12)\n"
                                     "T0::T2.nodeName:create(T2-07)\n"
                                     "T0::T2.T0::T2::T3:create\n"
                                     "T0::T2::T3.F0:create(1)\n"
                                     "T0::T2::T3.F1:create(9)\n"
                                     "T0::T2::T3.F2:create(11)\n"
                                     "T0::T2::T3.nodeName:create(T3-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:8,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(13)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(1)\n"
                                     "T0::T2::T4.nodeName:create(T4-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:8,F0:2), preKey(PID:8,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(14)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(2)\n"
                                     "T0::T2::T4.nodeName:create(T4-04)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:8,F0:3), preKey(PID:8,F0:2)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(15)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(3)\n"
                                     "T0::T2::T4.nodeName:create(T4-05)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:9), preKey(PID:1,F0:8)),(NULL)]\n"
                                     "T0::T2.ID:create(9)\n"
                                     "T0::T2.F1:create(11)\n"
                                     "T0::T2.F2:create(13)\n"
                                     "T0::T2.nodeName:create(T2-08)\n"
                                     "T0::T2.T0::T2::T3:create\n"
                                     "T0::T2::T3.F0:create(1)\n"
                                     "T0::T2::T3.F1:create(9)\n"
                                     "T0::T2::T3.F2:create(11)\n"
                                     "T0::T2::T3.nodeName:create(T3-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:9,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(16)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(1)\n"
                                     "T0::T2::T4.nodeName:create(T4-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:9,F0:2), preKey(PID:9,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(17)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(2)\n"
                                     "T0::T2::T4.nodeName:create(T4-04)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:9,F0:3), preKey(PID:9,F0:2)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(18)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(3)\n"
                                     "T0::T2::T4.nodeName:create(T4-05)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:10), preKey(PID:1,F0:9)),(NULL)]\n"
                                     "T0::T2.ID:create(10)\n"
                                     "T0::T2.F1:create(12)\n"
                                     "T0::T2.F2:create(14)\n"
                                     "T0::T2.nodeName:create(T2-09)\n"
                                     "T0::T2.T0::T2::T3:create\n"
                                     "T0::T2::T3.F0:create(1)\n"
                                     "T0::T2::T3.F1:create(9)\n"
                                     "T0::T2::T3.F2:create(11)\n"
                                     "T0::T2::T3.nodeName:create(T3-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:10,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(19)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(1)\n"
                                     "T0::T2::T4.nodeName:create(T4-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:10,F0:2), preKey(PID:10,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(20)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(2)\n"
                                     "T0::T2::T4.nodeName:create(T4-04)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:10,F0:3), preKey(PID:10,F0:2)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(21)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(3)\n"
                                     "T0::T2::T4.nodeName:create(T4-05)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:11), preKey(PID:1,F0:10)),(NULL)]\n"
                                     "T0::T2.ID:create(11)\n"
                                     "T0::T2.F1:create(13)\n"
                                     "T0::T2.F2:create(15)\n"
                                     "T0::T2.nodeName:create(T2-10)\n"
                                     "T0::T2.T0::T2::T3:create\n"
                                     "T0::T2::T3.F0:create(1)\n"
                                     "T0::T2::T3.F1:create(9)\n"
                                     "T0::T2::T3.F2:create(11)\n"
                                     "T0::T2::T3.nodeName:create(T3-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:11,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(22)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(1)\n"
                                     "T0::T2::T4.nodeName:create(T4-03)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:11,F0:2), preKey(PID:11,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(23)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(2)\n"
                                     "T0::T2::T4.nodeName:create(T4-04)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:11,F0:3), preKey(PID:11,F0:2)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(24)\n"
                                     "T0::T2::T4.F1:create(9)\n"
                                     "T0::T2::T4.F2:create(3)\n"
                                     "T0::T2::T4.nodeName:create(T4-05)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);

    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

// 测试多个点的merge操作
TEST_F(StYang, PartialDiffMultiVertexMergeOperationAsync)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");

    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(InitPartialDiffBasicDataAsync(
        asyncConn, asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_baseDataJsonTree));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_merge_diff.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> treeExpectDiff = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T0::T1:update\n"
        "T0::T1.F1:update(1000,8)\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:1)),(priKey(PID:1,F0:1))]\n"
        "T0::T2.F1:update(9,3)\n"
        "T0::T2.T0::T2::T3:update\n"
        "T0::T2::T3.F1:update(9,3)\n"
        "T0::T2.T0::T2::T4:update[(priKey(PID:1,F0:1)),(priKey(PID:1,F0:1))]\n"
        "T0::T2::T4.F1:update(9,3)\n"
        "T0::T2.T0::T2::T4:update[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(priKey(PID:1,F0:2), "
        "preKey(PID:1,F0:1))]\n"
        "T0::T2::T4.F1:update(100,3)\n"
        "T0::T2::T4.F2:update(1,2)\n"
        "T0::T2.T0::T2::T4:update[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(priKey(PID:1,F0:3), "
        "preKey(PID:1,F0:2))]\n"
        "T0::T2::T4.F1:update(300,3)\n"
        "T0::T2::T4.F2:update(1,3)\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0::T2.F1:update(10,4)\n"
        "T0::T2.T0::T2::T3:update\n"
        "T0::T2::T3.F1:update(9,4)\n"
        "T0::T2.T0::T2::T4:update[(priKey(PID:2,F0:1)),(priKey(PID:2,F0:1))]\n"
        "T0::T2::T4.F1:update(9,4)\n"
        "T0::T2.T0::T2::T4:update[(priKey(PID:2,F0:2), preKey(PID:2,F0:1)),(priKey(PID:2,F0:2), "
        "preKey(PID:2,F0:1))]\n"
        "T0::T2::T4.F1:update(100,4)\n"
        "T0::T2::T4.F2:update(1,2)\n"
        "T0::T2.T0::T2::T4:update[(priKey(PID:2,F0:3), preKey(PID:2,F0:2)),(priKey(PID:2,F0:3), "
        "preKey(PID:2,F0:2))]\n"
        "T0::T2::T4.F1:update(300,4)\n"
        "T0::T2::T4.F2:update(1,3)\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T0::T2.F1:update(11,5)\n"
        "T0::T2.F2:update(100,7)\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
        "T0::T2.F1:update(12,6)\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
        "T0::T2.F1:update(13,7)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);

    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

// 测试对list进行多次移动
TEST_F(StYang, PartialDiffMultiListMoveAsync)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");

    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(InitPartialDiffBasicDataAsync(
        asyncConn, asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_baseDataJsonTree));

    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_list_move_diff.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> treeExpectDiff = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:5)),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
        "T0::T2.F1:update(6,7)\n"
        "T0::T2.F2:update(7,9)\n"
        "T0::T2.nodeName:update(T2-16,T2-04)\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:1), preKey(PID:1,F0:5)),(priKey(PID:1,F0:1))]\n"
        "T0::T2.F1:update(8,3)\n"
        "T0::T2.F2:update(7,5)\n"
        "T0::T2.nodeName:update(T2-18,T2-00)\n"
        "T0::T2.T0::T2::T4:update[(priKey(PID:1,F0:1), preKey(PID:1,F0:3)),(priKey(PID:1,F0:1))]\n"
        "T0::T2::T4.F1:update(4,3)\n"
        "T0::T2::T4.F3:update(19,8)\n"
        "T0::T2::T4.nodeName:update(T2-17,T4-00)\n"
        "T0::T2.T0::T2::T4:update[(priKey(PID:1,F0:2)),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:4), preKey(PID:1,F0:1)),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:3), preKey(PID:1,F0:4)),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T0::T2.F1:update(6,5)\n"
        "T0::T2.nodeName:update(T2-19,T2-02)\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:6), preKey(PID:1,F0:3)),(priKey(PID:1,F0:6), preKey(PID:1,F0:5))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:2), preKey(PID:1,F0:6)),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0::T2.F1:update(7,4)\n"
        "T0::T2.F2:update(7,6)\n"
        "T0::T2.nodeName:update(T2-17,T2-01)\n"
        "T0::T2.T0::T2::T4:update[(priKey(PID:2,F0:2)),(priKey(PID:2,F0:2), preKey(PID:2,F0:1))]\n"
        "T0::T2::T4.F1:update(5,4)\n"
        "T0::T2::T4.F2:update(3,2)\n"
        "T0::T2::T4.F3:update(100,81)\n"
        "T0::T2::T4.nodeName:update(T2-29,T4-04)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:2,F0:1))]\n"
        "T0::T2::T4.ID:remove(4)\n"
        "T0::T2::T4.F1:remove(4)\n"
        "T0::T2::T4.F2:remove(1)\n"
        "T0::T2::T4.F3:remove(18)\n"
        "T0::T2::T4.nodeName:remove(T4-03)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);

    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

// 测试对list进行增删改
TEST_F(StYang, PartialDiffMultiListMove2Async)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(InitPartialDiffBasicDataAsync(
        asyncConn, asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_baseDataJsonTree));

    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_list_multi_diff.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> treeExpectDiff = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:2)),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:1), preKey(PID:1,F0:2)),(priKey(PID:1,F0:1))]\n"
        "T0::T2.F1:update(10,3)\n"
        "T0::T2.F2:update(9,5)\n"
        "T0::T2.nodeName:update(T2-180,T2-00)\n"
        "T0::T2.T0::T2::T4:update[(priKey(PID:1,F0:1)),(priKey(PID:1,F0:1))]\n"
        "T0::T2::T4.F1:update(400,3)\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:4), preKey(PID:1,F0:1)),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:3), preKey(PID:1,F0:4)),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T0::T2.F1:update(6,5)\n"
        "T0::T2.nodeName:update(T2-19,T2-02)\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:5), preKey(PID:1,F0:3)),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
        "T0::T2.T0::T2::T4:create[(priKey(PID:5,F0:6)),(NULL)]\n"
        "T0::T2::T4.ID:create(10)\n"
        "T0::T2::T4.F1:create(5)\n"
        "T0::T2::T4.F2:create(3)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
}

// 测试对list进行增删改
TEST_F(StYang, PartialDiffMultiListMove3Async)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(InitPartialDiffBasicDataAsync(
        asyncConn, asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_baseDataJsonTree));

    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_list_multi_diff_1.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> treeExpectDiff = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:3)),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:1), preKey(PID:1,F0:6)),(priKey(PID:1,F0:1))]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
}

TEST_F(StYang, PartialDiffMultiVertexReplaceOptionAsync)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(InitPartialDiffBasicDataAsync(
        asyncConn, asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_baseDataJsonTree));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_replace_diff.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> treeExpectDiff = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T0::T1:update\n"
        "T0::T1.F0:update(100,1)\n"
        "T0::T1.F1:remove(8)\n"
        "T0::T1.nodeName:remove(T1-00)\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
        "T0::T2.T0::T2::T4:create[(priKey(PID:5,F0:1)),(NULL)]\n"
        "T0::T2::T4.ID:create(10)\n"
        "T0::T2::T4.F1:create(500)\n"
        "T0::T2::T4.F2:create(1)\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:1)),(priKey(PID:1,F0:1))]\n"
        "T0::T2.F1:update(300,3)\n"
        "T0::T2.F2:remove(5)\n"
        "T0::T2.nodeName:remove(T2-00)\n"
        "T0::T2.T0::T2::T3:remove\n"
        "T0::T2::T3.F0:remove(1)\n"
        "T0::T2::T3.F1:remove(3)\n"
        "T0::T2::T3.F2:remove(9)\n"
        "T0::T2::T3.nodeName:remove(T3-00)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:1,F0:1))]\n"
        "T0::T2::T4.ID:remove(1)\n"
        "T0::T2::T4.F1:remove(3)\n"
        "T0::T2::T4.F2:remove(1)\n"
        "T0::T2::T4.F3:remove(8)\n"
        "T0::T2::T4.nodeName:remove(T4-00)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0::T2::T4.ID:remove(2)\n"
        "T0::T2::T4.F1:remove(3)\n"
        "T0::T2::T4.F2:remove(2)\n"
        "T0::T2::T4.F3:remove(2)\n"
        "T0::T2::T4.nodeName:remove(T4-01)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T0::T2::T4.ID:remove(3)\n"
        "T0::T2::T4.F1:remove(3)\n"
        "T0::T2::T4.F2:remove(3)\n"
        "T0::T2::T4.F3:remove(9)\n"
        "T0::T2::T4.nodeName:remove(T4-02)\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0::T2.F1:update(400,4)\n"
        "T0::T2.F2:remove(6)\n"
        "T0::T2.nodeName:remove(T2-01)\n"
        "T0::T2.T0::T2::T3:remove\n"
        "T0::T2::T3.F0:remove(1)\n"
        "T0::T2::T3.F1:remove(4)\n"
        "T0::T2::T3.F2:remove(9)\n"
        "T0::T2::T3.nodeName:remove(T3-01)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:2,F0:1))]\n"
        "T0::T2::T4.ID:remove(4)\n"
        "T0::T2::T4.F1:remove(4)\n"
        "T0::T2::T4.F2:remove(1)\n"
        "T0::T2::T4.F3:remove(18)\n"
        "T0::T2::T4.nodeName:remove(T4-03)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:2,F0:2), preKey(PID:2,F0:1))]\n"
        "T0::T2::T4.ID:remove(5)\n"
        "T0::T2::T4.F1:remove(4)\n"
        "T0::T2::T4.F2:remove(2)\n"
        "T0::T2::T4.F3:remove(81)\n"
        "T0::T2::T4.nodeName:remove(T4-04)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:2,F0:3), preKey(PID:2,F0:2))]\n"
        "T0::T2::T4.ID:remove(6)\n"
        "T0::T2::T4.F1:remove(4)\n"
        "T0::T2::T4.F2:remove(3)\n"
        "T0::T2::T4.F3:remove(22)\n"
        "T0::T2::T4.nodeName:remove(T4-05)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
}

/*
 * 测试场景：有预置数据，先全部删除再全部重新插入
 */
TEST_F(StYang, PartialDiffMultiOptionAsync1)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(InitPartialDiffBasicDataAsync(
        asyncConn, asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_baseDataJsonTree));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_delete_root.json";
    const char *editStr2 = "006_graph/yang/edit_with_tree_diff/basicPartialData.json";
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr2)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> expectDiffPartial = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
}

/*
 * 测试场景：无预置数据，先插入数据再全部删除
 */
TEST_F(StYang, PartialDiffMultiOptionAsync2)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree););
    const char *editStr2 = "006_graph/yang/edit_with_tree_diff/tree_delete_root.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "T0",
        (GetFileContext(g_baseDataJsonTree)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr2)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree));
}

/*
 * 测试场景：有预置数据，直接replace根节点
 */
TEST_F(StYang, PartialDiffMultiOptionAsync3)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(InitPartialDiffBasicDataAsync(
        asyncConn, asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_baseDataJsonTree));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_replace_root.json";
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.F1:remove(3)\n"
                                        "T0.nodeName:remove(T0-00)\n"
                                        "T0.T0::T1:remove\n"
                                        "T0::T1.F0:remove(1)\n"
                                        "T0::T1.F1:remove(8)\n"
                                        "T0::T1.nodeName:remove(T1-00)\n"
                                        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:1))]\n"
                                        "T0::T2.ID:remove(1)\n"
                                        "T0::T2.F1:remove(3)\n"
                                        "T0::T2.F2:remove(5)\n"
                                        "T0::T2.nodeName:remove(T2-00)\n"
                                        "T0::T2.T0::T2::T3:remove\n"
                                        "T0::T2::T3.F0:remove(1)\n"
                                        "T0::T2::T3.F1:remove(3)\n"
                                        "T0::T2::T3.F2:remove(9)\n"
                                        "T0::T2::T3.nodeName:remove(T3-00)\n"
                                        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:1,F0:1))]\n"
                                        "T0::T2::T4.ID:remove(1)\n"
                                        "T0::T2::T4.F1:remove(3)\n"
                                        "T0::T2::T4.F2:remove(1)\n"
                                        "T0::T2::T4.F3:remove(8)\n"
                                        "T0::T2::T4.nodeName:remove(T4-00)\n"
                                        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
                                        "T0::T2::T4.ID:remove(2)\n"
                                        "T0::T2::T4.F1:remove(3)\n"
                                        "T0::T2::T4.F2:remove(2)\n"
                                        "T0::T2::T4.F3:remove(2)\n"
                                        "T0::T2::T4.nodeName:remove(T4-01)\n"
                                        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
                                        "T0::T2::T4.ID:remove(3)\n"
                                        "T0::T2::T4.F1:remove(3)\n"
                                        "T0::T2::T4.F2:remove(3)\n"
                                        "T0::T2::T4.F3:remove(9)\n"
                                        "T0::T2::T4.nodeName:remove(T4-02)\n"
                                        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
                                        "T0::T2.ID:remove(2)\n"
                                        "T0::T2.F1:remove(4)\n"
                                        "T0::T2.F2:remove(6)\n"
                                        "T0::T2.nodeName:remove(T2-01)\n"
                                        "T0::T2.T0::T2::T3:remove\n"
                                        "T0::T2::T3.F0:remove(1)\n"
                                        "T0::T2::T3.F1:remove(4)\n"
                                        "T0::T2::T3.F2:remove(9)\n"
                                        "T0::T2::T3.nodeName:remove(T3-01)\n"
                                        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:2,F0:1))]\n"
                                        "T0::T2::T4.ID:remove(4)\n"
                                        "T0::T2::T4.F1:remove(4)\n"
                                        "T0::T2::T4.F2:remove(1)\n"
                                        "T0::T2::T4.F3:remove(18)\n"
                                        "T0::T2::T4.nodeName:remove(T4-03)\n"
                                        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:2,F0:2), preKey(PID:2,F0:1))]\n"
                                        "T0::T2::T4.ID:remove(5)\n"
                                        "T0::T2::T4.F1:remove(4)\n"
                                        "T0::T2::T4.F2:remove(2)\n"
                                        "T0::T2::T4.F3:remove(81)\n"
                                        "T0::T2::T4.nodeName:remove(T4-04)\n"
                                        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:2,F0:3), preKey(PID:2,F0:2))]\n"
                                        "T0::T2::T4.ID:remove(6)\n"
                                        "T0::T2::T4.F1:remove(4)\n"
                                        "T0::T2::T4.F2:remove(3)\n"
                                        "T0::T2::T4.F3:remove(22)\n"
                                        "T0::T2::T4.nodeName:remove(T4-05)\n"
                                        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
                                        "T0::T2.ID:remove(3)\n"
                                        "T0::T2.F1:remove(5)\n"
                                        "T0::T2.F2:remove(7)\n"
                                        "T0::T2.nodeName:remove(T2-02)\n"
                                        "T0::T2.T0::T2::T3:remove\n"
                                        "T0::T2::T3.F0:remove(1)\n"
                                        "T0::T2::T3.F1:remove(5)\n"
                                        "T0::T2::T3.F2:remove(9)\n"
                                        "T0::T2::T3.nodeName:remove(T3-02)\n"
                                        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:3,F0:1))]\n"
                                        "T0::T2::T4.ID:remove(7)\n"
                                        "T0::T2::T4.F1:remove(5)\n"
                                        "T0::T2::T4.F2:remove(1)\n"
                                        "T0::T2::T4.F3:remove(5)\n"
                                        "T0::T2::T4.nodeName:remove(T4-06)\n"
                                        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:3,F0:2), preKey(PID:3,F0:1))]\n"
                                        "T0::T2::T4.ID:remove(8)\n"
                                        "T0::T2::T4.F1:remove(5)\n"
                                        "T0::T2::T4.F2:remove(2)\n"
                                        "T0::T2::T4.F3:remove(6)\n"
                                        "T0::T2::T4.nodeName:remove(T4-07)\n"
                                        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:3,F0:3), preKey(PID:3,F0:2))]\n"
                                        "T0::T2::T4.ID:remove(9)\n"
                                        "T0::T2::T4.F1:remove(5)\n"
                                        "T0::T2::T4.F2:remove(3)\n"
                                        "T0::T2::T4.F3:remove(7)\n"
                                        "T0::T2::T4.nodeName:remove(T4-08)\n"
                                        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
                                        "T0::T2.ID:remove(4)\n"
                                        "T0::T2.F1:remove(6)\n"
                                        "T0::T2.F2:remove(8)\n"
                                        "T0::T2.nodeName:remove(T2-03)\n"
                                        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
                                        "T0::T2.ID:remove(5)\n"
                                        "T0::T2.F1:remove(7)\n"
                                        "T0::T2.F2:remove(9)\n"
                                        "T0::T2.nodeName:remove(T2-04)\n"
                                        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:6), preKey(PID:1,F0:5))]\n"
                                        "T0::T2.ID:remove(6)\n"
                                        "T0::T2.F1:remove(8)\n"
                                        "T0::T2.F2:remove(10)\n"
                                        "T0::T2.nodeName:remove(T2-05)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
}

/*
 * 测试场景：有预置数据，先插入数据再全部删除
 */
TEST_F(StYang, PartialDiffMultiOptionAsync4)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_1.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_1.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_1.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    const char *editStr2 = "006_graph/yang/edit_with_tree_diff/tree_delete_root.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr2)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:remove[(NULL),(priKey(ID:1))]\n"
        "T0.F0:remove(1)\n"
        "T0.nodeName:remove(T0-00)\n"
        "T0.T1:remove\n"
        "T1.F0:remove(1)\n"
        "T1.nodeName:remove(T1-00)\n"
        "T1.T0::T3:remove[(NULL),(priKey(PID:1,F0:1))]\n"
        "T0::T3.ID:remove(1)\n"
        "T0::T3.nodeName:remove(T3-00)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:1,F0:1))]\n"
        "T0::T3::T5.ID:remove(1)\n"
        "T0::T3::T5.nodeName:remove(T5-00)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0::T3::T5.ID:remove(2)\n"
        "T0::T3::T5.nodeName:remove(T5-01)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T0::T3::T5.ID:remove(3)\n"
        "T0::T3::T5.nodeName:remove(T5-02)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
        "T0::T3::T5.ID:remove(4)\n"
        "T0::T3::T5.nodeName:remove(T5-03)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
        "T0::T3::T5.ID:remove(5)\n"
        "T0::T3::T5.nodeName:remove(T5-04)\n"
        "T1.T0::T3:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0::T3.ID:remove(2)\n"
        "T0::T3.nodeName:remove(T3-01)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:2,F0:1))]\n"
        "T0::T3::T5.ID:remove(6)\n"
        "T0::T3::T5.nodeName:remove(T5-00)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:2,F0:2), preKey(PID:2,F0:1))]\n"
        "T0::T3::T5.ID:remove(7)\n"
        "T0::T3::T5.nodeName:remove(T5-01)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:2,F0:3), preKey(PID:2,F0:2))]\n"
        "T0::T3::T5.ID:remove(8)\n"
        "T0::T3::T5.nodeName:remove(T5-02)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:2,F0:4), preKey(PID:2,F0:3))]\n"
        "T0::T3::T5.ID:remove(9)\n"
        "T0::T3::T5.nodeName:remove(T5-03)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:2,F0:5), preKey(PID:2,F0:4))]\n"
        "T0::T3::T5.ID:remove(10)\n"
        "T0::T3::T5.nodeName:remove(T5-04)\n"
        "T1.T0::T3:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T0::T3.ID:remove(3)\n"
        "T0::T3.nodeName:remove(T3-02)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:3,F0:1))]\n"
        "T0::T3::T5.ID:remove(11)\n"
        "T0::T3::T5.nodeName:remove(T5-00)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:3,F0:2), preKey(PID:3,F0:1))]\n"
        "T0::T3::T5.ID:remove(12)\n"
        "T0::T3::T5.nodeName:remove(T5-01)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:3,F0:3), preKey(PID:3,F0:2))]\n"
        "T0::T3::T5.ID:remove(13)\n"
        "T0::T3::T5.nodeName:remove(T5-02)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:3,F0:4), preKey(PID:3,F0:3))]\n"
        "T0::T3::T5.ID:remove(14)\n"
        "T0::T3::T5.nodeName:remove(T5-03)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:3,F0:5), preKey(PID:3,F0:4))]\n"
        "T0::T3::T5.ID:remove(15)\n"
        "T0::T3::T5.nodeName:remove(T5-04)\n"
        "T1.T0::T3:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
        "T0::T3.ID:remove(4)\n"
        "T0::T3.nodeName:remove(T3-03)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:4,F0:1))]\n"
        "T0::T3::T5.ID:remove(16)\n"
        "T0::T3::T5.nodeName:remove(T5-00)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:4,F0:2), preKey(PID:4,F0:1))]\n"
        "T0::T3::T5.ID:remove(17)\n"
        "T0::T3::T5.nodeName:remove(T5-01)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:4,F0:3), preKey(PID:4,F0:2))]\n"
        "T0::T3::T5.ID:remove(18)\n"
        "T0::T3::T5.nodeName:remove(T5-02)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:4,F0:4), preKey(PID:4,F0:3))]\n"
        "T0::T3::T5.ID:remove(19)\n"
        "T0::T3::T5.nodeName:remove(T5-03)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:4,F0:5), preKey(PID:4,F0:4))]\n"
        "T0::T3::T5.ID:remove(20)\n"
        "T0::T3::T5.nodeName:remove(T5-04)\n"
        "T1.T0::T3:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
        "T0::T3.ID:remove(5)\n"
        "T0::T3.nodeName:remove(T3-04)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:5,F0:1))]\n"
        "T0::T3::T5.ID:remove(21)\n"
        "T0::T3::T5.nodeName:remove(T5-00)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:5,F0:2), preKey(PID:5,F0:1))]\n"
        "T0::T3::T5.ID:remove(22)\n"
        "T0::T3::T5.nodeName:remove(T5-01)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:5,F0:3), preKey(PID:5,F0:2))]\n"
        "T0::T3::T5.ID:remove(23)\n"
        "T0::T3::T5.nodeName:remove(T5-02)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:5,F0:4), preKey(PID:5,F0:3))]\n"
        "T0::T3::T5.ID:remove(24)\n"
        "T0::T3::T5.nodeName:remove(T5-03)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:5,F0:5), preKey(PID:5,F0:4))]\n"
        "T0::T3::T5.ID:remove(25)\n"
        "T0::T3::T5.nodeName:remove(T5-04)\n"
        "T1.T0::T3:remove[(NULL),(priKey(PID:1,F0:6), preKey(PID:1,F0:5))]\n"
        "T0::T3.ID:remove(6)\n"
        "T0::T3.nodeName:remove(T3-05)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:6,F0:1))]\n"
        "T0::T3::T5.ID:remove(26)\n"
        "T0::T3::T5.nodeName:remove(T5-00)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:6,F0:2), preKey(PID:6,F0:1))]\n"
        "T0::T3::T5.ID:remove(27)\n"
        "T0::T3::T5.nodeName:remove(T5-01)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:6,F0:3), preKey(PID:6,F0:2))]\n"
        "T0::T3::T5.ID:remove(28)\n"
        "T0::T3::T5.nodeName:remove(T5-02)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:6,F0:4), preKey(PID:6,F0:3))]\n"
        "T0::T3::T5.ID:remove(29)\n"
        "T0::T3::T5.nodeName:remove(T5-03)\n"
        "T0::T3.T0::T3::T5:remove[(NULL),(priKey(PID:6,F0:5), preKey(PID:6,F0:4))]\n"
        "T0::T3::T5.ID:remove(30)\n"
        "T0::T3::T5.nodeName:remove(T5-04)\n"
        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:1))]\n"
        "T0::T2.ID:remove(1)\n"
        "T0::T2.nodeName:remove(T2-00)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:1,F0:1))]\n"
        "T0::T2::T4.ID:remove(1)\n"
        "T0::T2::T4.nodeName:remove(T4-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:1,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(1)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(2)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T0::T2::T4::T6.ID:remove(3)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-02)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0::T2::T4.ID:remove(2)\n"
        "T0::T2::T4.nodeName:remove(T4-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:2,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(4)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:2,F0:2), preKey(PID:2,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(5)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:2,F0:3), preKey(PID:2,F0:2))]\n"
        "T0::T2::T4::T6.ID:remove(6)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-02)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T0::T2::T4.ID:remove(3)\n"
        "T0::T2::T4.nodeName:remove(T4-02)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:3,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(7)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:3,F0:2), preKey(PID:3,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(8)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:3,F0:3), preKey(PID:3,F0:2))]\n"
        "T0::T2::T4::T6.ID:remove(9)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-02)\n"
        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0::T2.ID:remove(2)\n"
        "T0::T2.nodeName:remove(T2-01)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:2,F0:1))]\n"
        "T0::T2::T4.ID:remove(4)\n"
        "T0::T2::T4.nodeName:remove(T4-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:4,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(10)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:4,F0:2), preKey(PID:4,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(11)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:4,F0:3), preKey(PID:4,F0:2))]\n"
        "T0::T2::T4::T6.ID:remove(12)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-02)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:2,F0:2), preKey(PID:2,F0:1))]\n"
        "T0::T2::T4.ID:remove(5)\n"
        "T0::T2::T4.nodeName:remove(T4-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:5,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(13)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:5,F0:2), preKey(PID:5,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(14)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:5,F0:3), preKey(PID:5,F0:2))]\n"
        "T0::T2::T4::T6.ID:remove(15)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-02)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:2,F0:3), preKey(PID:2,F0:2))]\n"
        "T0::T2::T4.ID:remove(6)\n"
        "T0::T2::T4.nodeName:remove(T4-02)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:6,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(16)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:6,F0:2), preKey(PID:6,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(17)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:6,F0:3), preKey(PID:6,F0:2))]\n"
        "T0::T2::T4::T6.ID:remove(18)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-02)\n"
        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T0::T2.ID:remove(3)\n"
        "T0::T2.nodeName:remove(T2-02)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:3,F0:1))]\n"
        "T0::T2::T4.ID:remove(7)\n"
        "T0::T2::T4.nodeName:remove(T4-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:7,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(19)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:7,F0:2), preKey(PID:7,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(20)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:7,F0:3), preKey(PID:7,F0:2))]\n"
        "T0::T2::T4::T6.ID:remove(21)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-02)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:3,F0:2), preKey(PID:3,F0:1))]\n"
        "T0::T2::T4.ID:remove(8)\n"
        "T0::T2::T4.nodeName:remove(T4-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:8,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(22)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:8,F0:2), preKey(PID:8,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(23)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:8,F0:3), preKey(PID:8,F0:2))]\n"
        "T0::T2::T4::T6.ID:remove(24)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-02)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:3,F0:3), preKey(PID:3,F0:2))]\n"
        "T0::T2::T4.ID:remove(9)\n"
        "T0::T2::T4.nodeName:remove(T4-02)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:9,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(25)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:9,F0:2), preKey(PID:9,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(26)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:9,F0:3), preKey(PID:9,F0:2))]\n"
        "T0::T2::T4::T6.ID:remove(27)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-02)\n"
        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
        "T0::T2.ID:remove(4)\n"
        "T0::T2.nodeName:remove(T2-03)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:4,F0:1))]\n"
        "T0::T2::T4.ID:remove(10)\n"
        "T0::T2::T4.nodeName:remove(T4-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:10,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(28)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:10,F0:2), preKey(PID:10,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(29)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:10,F0:3), preKey(PID:10,F0:2))]\n"
        "T0::T2::T4::T6.ID:remove(30)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-02)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:4,F0:2), preKey(PID:4,F0:1))]\n"
        "T0::T2::T4.ID:remove(11)\n"
        "T0::T2::T4.nodeName:remove(T4-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:11,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(31)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:11,F0:2), preKey(PID:11,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(32)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:11,F0:3), preKey(PID:11,F0:2))]\n"
        "T0::T2::T4::T6.ID:remove(33)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-02)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:4,F0:3), preKey(PID:4,F0:2))]\n"
        "T0::T2::T4.ID:remove(12)\n"
        "T0::T2::T4.nodeName:remove(T4-02)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:12,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(34)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:12,F0:2), preKey(PID:12,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(35)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:12,F0:3), preKey(PID:12,F0:2))]\n"
        "T0::T2::T4::T6.ID:remove(36)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-02)\n"
        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
        "T0::T2.ID:remove(5)\n"
        "T0::T2.nodeName:remove(T2-04)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:5,F0:1))]\n"
        "T0::T2::T4.ID:remove(13)\n"
        "T0::T2::T4.nodeName:remove(T4-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:13,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(37)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:13,F0:2), preKey(PID:13,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(38)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:13,F0:3), preKey(PID:13,F0:2))]\n"
        "T0::T2::T4::T6.ID:remove(39)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-02)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:5,F0:2), preKey(PID:5,F0:1))]\n"
        "T0::T2::T4.ID:remove(14)\n"
        "T0::T2::T4.nodeName:remove(T4-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:14,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(40)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:14,F0:2), preKey(PID:14,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(41)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:14,F0:3), preKey(PID:14,F0:2))]\n"
        "T0::T2::T4::T6.ID:remove(42)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-02)\n"
        "T0::T2.T0::T2::T4:remove[(NULL),(priKey(PID:5,F0:3), preKey(PID:5,F0:2))]\n"
        "T0::T2::T4.ID:remove(15)\n"
        "T0::T2::T4.nodeName:remove(T4-02)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:15,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(43)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-00)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:15,F0:2), preKey(PID:15,F0:1))]\n"
        "T0::T2::T4::T6.ID:remove(44)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-01)\n"
        "T0::T2::T4.T0::T2::T4::T6:remove[(NULL),(priKey(PID:15,F0:3), preKey(PID:15,F0:2))]\n"
        "T0::T2::T4::T6.ID:remove(45)\n"
        "T0::T2::T4::T6.nodeName:remove(T6-02)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree));
}

/* container--container 树模型示例
                            container(root)
                                 |
   ┌--------------┬--------------┼--------------┬--------------┐
   |              |              |              |              |
container(P3) container(P2) container(P1) container(P4) container(P5)
                                 |
                          ┌------┼-------┐
                          |              |
                     container(A1)  container(A2)
*/
/*
 * 测试场景：无预置数据，全部插入
 */
TEST_F(StYang, PartialDiffMultiOptionAsync5)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_2.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_2.json";

    ASSERT_NO_FATAL_FAILURE(LltCreateVertexLabelAsync(asyncStmt, vertexLabelFileTree, g_lltJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "T0",
        (GetFileContext(baseDataJsonTree)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                        "T0.F0:create(1)\n"
                                        "T0.nodeName:create(T0-00)\n"
                                        "T0.T1:create\n"
                                        "T1.F0:create(1)\n"
                                        "T1.nodeName:create(T1-00)\n"
                                        "T0.T2:create\n"
                                        "T2.F0:create(1)\n"
                                        "T2.nodeName:create(T2-00)\n"
                                        "T0.T3:create\n"
                                        "T3.F0:create(1)\n"
                                        "T3.nodeName:create(T3-00)\n"
                                        "T3.T31:create\n"
                                        "T31.F0:create(1)\n"
                                        "T31.nodeName:create(T31-00)\n"
                                        "T3.T32:create\n"
                                        "T32.F0:create(1)\n"
                                        "T32.nodeName:create(T32-00)\n"
                                        "T0.T4:create\n"
                                        "T4.F0:create(1)\n"
                                        "T4.nodeName:create(T4-00)\n"
                                        "T0.T5:create\n"
                                        "T5.F0:create(1)\n"
                                        "T5.nodeName:create(T5-00)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

/* container--container 树模型示例
                            container(root)
                                 |
   ┌--------------┬--------------┼--------------┬--------------┐
   |              |              |              |              |
container(P3) container(P2) container(P1) container(P4) container(P5)
                                 |
                          ┌------┼-------┐
                          |              |
                     container(A1)  container(A2)
*/
/*
 * 测试场景：有预置数据，先插入数据再全部删除
 */
TEST_F(StYang, PartialDiffMultiOptionAsync6)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_2.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_2.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_delete_root.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, NULL, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:remove[(NULL),(priKey(ID:1))]\n"
                                        "T0.F0:remove(1)\n"
                                        "T0.nodeName:remove(T0-00)\n"
                                        "T0.T1:remove\n"
                                        "T1.F0:remove(1)\n"
                                        "T1.nodeName:remove(T1-00)\n"
                                        "T0.T2:remove\n"
                                        "T2.F0:remove(1)\n"
                                        "T2.nodeName:remove(T2-00)\n"
                                        "T0.T3:remove\n"
                                        "T3.F0:remove(1)\n"
                                        "T3.nodeName:remove(T3-00)\n"
                                        "T3.T31:remove\n"
                                        "T31.F0:remove(1)\n"
                                        "T31.nodeName:remove(T31-00)\n"
                                        "T3.T32:remove\n"
                                        "T32.F0:remove(1)\n"
                                        "T32.nodeName:remove(T32-00)\n"
                                        "T0.T4:remove\n"
                                        "T4.F0:remove(1)\n"
                                        "T4.nodeName:remove(T4-00)\n"
                                        "T0.T5:remove\n"
                                        "T5.F0:remove(1)\n"
                                        "T5.nodeName:remove(T5-00)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

/* container--container 树模型示例
                            container(root)
                                 |
   ┌--------------┬--------------┼--------------┬--------------┐
   |              |              |              |              |
container(P3) container(P2) container(P1) container(P4) container(P5)
                                 |
                          ┌------┼-------┐
                          |              |
                     container(A1)  container(A2)
*/
/*
 * 测试场景：有预置数据，先插入数据再全部删除
 */
TEST_F(StYang, PartialDiffMultiOptionAsync7)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_2.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_2.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_delete_root.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexLabelAsync(asyncStmt, vertexLabelFileTree, g_lltJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "T0",
        (GetFileContext(baseDataJsonTree)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

/* container--container 树模型示例
                            container(root)
                                 |
   ┌--------------┬--------------┼--------------┬--------------┐
   |              |              |              |              |
container(P3) container(P2) container(P1) container(P4) container(P5)
                                 |
                          ┌------┼-------┐
                          |              |
                     container(A1)  container(A2)
*/
/*
 * 测试场景：有预置数据，先插入数据再全部删除
 */
TEST_F(StYang, PartialDiffMultiOptionAsync8)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_2.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_2.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_delete_root.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, NULL, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "T0",
        (GetFileContext(baseDataJsonTree)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

/* container--container 树模型示例
                            container(root)
                                 |
   ┌--------------┬--------------┼--------------┬--------------┐
   |              |              |              |              |
container(P3) container(P2) container(P1) container(P4) container(P5)
                                 |
                          ┌------┼-------┐
                          |              |
                     container(A1)  container(A2)
*/
/*
 * 测试场景：有预置数据，对node节点进行DML操作
 */
TEST_F(StYang, PartialDiffMultiOptionAsync9)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_2.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_2.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_multi_op.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, NULL, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.T1:update\n"
                                        "T1.F0:update(5,1)\n"
                                        "T0.T2:update\n"
                                        "T2.F0:update(10,1)\n"
                                        "T2.nodeName:remove(T2-00)\n"
                                        "T0.T3:remove\n"
                                        "T3.F0:remove(1)\n"
                                        "T3.nodeName:remove(T3-00)\n"
                                        "T3.T31:remove\n"
                                        "T31.F0:remove(1)\n"
                                        "T31.nodeName:remove(T31-00)\n"
                                        "T3.T32:remove\n"
                                        "T32.F0:remove(1)\n"
                                        "T32.nodeName:remove(T32-00)\n"
                                        "T0.T4:remove\n"
                                        "T4.F0:remove(1)\n"
                                        "T4.nodeName:remove(T4-00)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

/* container--container 树模型示例
                            container(root)
                                 |
   ┌--------------┬--------------┼--------------┬--------------┐
   |              |              |              |              |
container(P3) container(P2) choice(P1)   container(P4) container(P5)
                                 |
                          ┌------┼-------┐
                          |              |
                        case(A1)      case(A2)
                          |               |
                       choice(B1)     choice(B2)
                          |               |
                     |---------|     |-----------|
                   case(C1) case(C2) case(C3)  case(C4)

 * 测试场景：有预置数据，对case类型的node节点进行插入 ，已有case删除
 */
TEST_F(StYang, PartialDiffMultiOptionAsync10)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_3.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_3.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_multi_op_1.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, NULL, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.T3Choice:update\n"
                                        "T3Choice.T31Case:remove\n"
                                        "T31Case.F0:remove(1)\n"
                                        "T31Case.nodeName:remove(T31-00)\n"
                                        "T31Case.T311Choice:remove\n"
                                        "T311Choice.T3111Case:remove\n"
                                        "T3111Case.F0:remove(1)\n"
                                        "T3111Case.nodeName:remove(T3111-00)\n"
                                        "T3Choice.T32Case:create\n"
                                        "T32Case.F0:create(1)\n"
                                        "T32Case.nodeName:create(T32-00)\n"
                                        "T32Case.T321Choice:create\n"
                                        "T321Choice.T3211Case:create\n"
                                        "T3211Case.F0:create(1)\n"
                                        "T3211Case.nodeName:create(T3211-00)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

// container-container-list场景 replace list的父亲
TEST_F(StYang, PartialDiffMultiOptionAsync11)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_4.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_4.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_4.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_multi_op_2.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.T1:remove\n"
                                        "T1.F0:remove(1)\n"
                                        "T1.T2:remove[(NULL),(priKey(PID:1,F0:1))]\n"
                                        "T2.ID:remove(1)\n"
                                        "T2.P1:remove\n"
                                        "P1.F0:remove(1)\n"
                                        "T1.T2:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
                                        "T2.ID:remove(2)\n"
                                        "T2.P1:remove\n"
                                        "P1.F0:remove(1)\n"
                                        "T1.T2:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
                                        "T2.ID:remove(3)\n"
                                        "T2.P1:remove\n"
                                        "P1.F0:remove(1)\n"
                                        "T1.T3:remove[(NULL),(priKey(PID:1,F0:1))]\n"
                                        "T3.ID:remove(1)\n"
                                        "T3.P2:remove\n"
                                        "P2.F0:remove(1)\n"
                                        "T1.T3:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
                                        "T3.ID:remove(2)\n"
                                        "T3.P2:remove\n"
                                        "P2.F0:remove(1)\n"
                                        "T1.T3:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
                                        "T3.ID:remove(3)\n"
                                        "T3.P2:remove\n"
                                        "P2.F0:remove(1)\n"
                                        "T1.T4:remove[(NULL),(priKey(PID:1,F0:1))]\n"
                                        "T4.ID:remove(1)\n"
                                        "T4.P3:remove\n"
                                        "P3.F0:remove(1)\n"
                                        "T1.T4:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
                                        "T4.ID:remove(2)\n"
                                        "T4.P3:remove\n"
                                        "P3.F0:remove(1)\n"
                                        "T1.T4:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
                                        "T4.ID:remove(3)\n"
                                        "T4.P3:remove\n"
                                        "P3.F0:remove(1)\n"
                                        "T1.T5:remove[(NULL),(priKey(PID:1,F0:1))]\n"
                                        "T5.ID:remove(1)\n"
                                        "T5.P4:remove\n"
                                        "P4.F0:remove(1)\n"
                                        "T1.T5:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
                                        "T5.ID:remove(2)\n"
                                        "T5.P4:remove\n"
                                        "P4.F0:remove(1)\n"
                                        "T1.T5:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
                                        "T5.ID:remove(3)\n"
                                        "T5.P4:remove\n"
                                        "P4.F0:remove(1)\n"
                                        "T1.T6:remove[(NULL),(priKey(PID:1,F0:1))]\n"
                                        "T6.ID:remove(1)\n"
                                        "T6.P5:remove\n"
                                        "P5.F0:remove(1)\n"
                                        "T1.T6:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
                                        "T6.ID:remove(2)\n"
                                        "T6.P5:remove\n"
                                        "P5.F0:remove(1)\n"
                                        "T1.T6:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
                                        "T6.ID:remove(3)\n"
                                        "T6.P5:remove\n"
                                        "P5.F0:remove(1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree));
}

// container-container-list场景 replace list的父亲
TEST_F(StYang, PartialDiffMultiOptionAsync12)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_5.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_5.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_5.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_multi_op_3.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.T1:update\n"
                                        "T1.T2:update[(priKey(PID:1,F0:1)),(priKey(PID:1,F0:1))]\n"
                                        "T2.P1:update\n"
                                        "P1.T3:update[(priKey(PID:1,F0:1)),(priKey(PID:1,F0:1))]\n"
                                        "T3.P2:update\n"
                                        "P2.T4:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
                                        "T4.ID:remove(3)\n"
                                        "P2.T4:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
                                        "T4.ID:remove(2)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree));
}

TEST_F(StYang, PartialDiffMultiOptionAsync13)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_7.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_7.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_7.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_multi_op_5.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T0Node:update\n"
        "T0Node.T1:update[(priKey(PID:1,F0:3)),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T1.P1:remove\n"
        "P1.F0:remove(3)\n"
        "T0Node.T1:update[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
        "T1.P1:remove\n"
        "P1.F0:remove(4)\n"
        "T0Node.T1:update[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
        "T1.P1:remove\n"
        "P1.F0:remove(5)\n"
        "T0Node.T1:update[(priKey(PID:1,F0:1), preKey(PID:1,F0:5)),(priKey(PID:1,F0:1))]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree));
}

// 测试savePoint回滚后生成diff
TEST_F(StYang, PartialDiffAnonymousSavePointRollbackAsync)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, NULL));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> treeExpectDiff = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                     "T0.F0:create(1)\n"
                                     "T0.F1:create(3)\n"
                                     "T0.nodeName:create(T0-00)\n"
                                     "T0.T0::T1:create\n"
                                     "T0::T1.F0:create(1)\n"
                                     "T0::T1.F1:create(8)\n"
                                     "T0::T1.nodeName:create(T1-00)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                     "T0::T2.ID:create(1)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-00)\n"
                                     "T0::T2.T0::T2::T3:create\n"
                                     "T0::T2::T3.F0:create(1)\n"
                                     "T0::T2::T3.F1:create(3)\n"
                                     "T0::T2::T3.F2:create(9)\n"
                                     "T0::T2::T3.nodeName:create(T3-00)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(1)\n"
                                     "T0::T2::T4.F1:create(3)\n"
                                     "T0::T2::T4.F2:create(1)\n"
                                     "T0::T2::T4.F3:create(8)\n"
                                     "T0::T2::T4.nodeName:create(T4-00)\n"};
    vector<string> treeExpectDiff_bak;
    treeExpectDiff_bak.push_back(treeExpectDiff[0]);
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, NULL));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff1.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff1 = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                      "T0.F0:create(1)\n"
                                      "T0.F1:create(3)\n"
                                      "T0.nodeName:create(T0-00)\n"
                                      "T0.T0::T1:create\n"
                                      "T0::T1.F0:create(1)\n"
                                      "T0::T1.F1:create(10)\n"
                                      "T0::T1.nodeName:create(T1-00)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2.ID:create(1)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-00)\n"
                                      "T0::T2.T0::T2::T3:create\n"
                                      "T0::T2::T3.F0:create(1)\n"
                                      "T0::T2::T3.F1:create(3)\n"
                                      "T0::T2::T3.F2:create(9)\n"
                                      "T0::T2::T3.nodeName:create(T3-00)\n"
                                      "T0::T2.T0::T2::T4:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2::T4.ID:create(1)\n"
                                      "T0::T2::T4.F1:create(3)\n"
                                      "T0::T2::T4.F2:create(1)\n"
                                      "T0::T2::T4.F3:create(8)\n"
                                      "T0::T2::T4.nodeName:create(T4-00)\n"
                                      "T0::T2.T0::T2::T4:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2::T4.ID:create(2)\n"
                                      "T0::T2::T4.F1:create(3)\n"
                                      "T0::T2::T4.F2:create(2)\n"
                                      "T0::T2::T4.F3:create(2)\n"
                                      "T0::T2::T4.nodeName:create(T4-01)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff1, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, NULL));
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff_bak, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
}

// 测试savePoint回滚后生成diff
TEST_F(StYang, PartialDiffSavePointRollbackAsync)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> treeExpectDiff = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                     "T0.F0:create(1)\n"
                                     "T0.F1:create(3)\n"
                                     "T0.nodeName:create(T0-00)\n"
                                     "T0.T0::T1:create\n"
                                     "T0::T1.F0:create(1)\n"
                                     "T0::T1.F1:create(8)\n"
                                     "T0::T1.nodeName:create(T1-00)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                     "T0::T2.ID:create(1)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-00)\n"
                                     "T0::T2.T0::T2::T3:create\n"
                                     "T0::T2::T3.F0:create(1)\n"
                                     "T0::T2::T3.F1:create(3)\n"
                                     "T0::T2::T3.F2:create(9)\n"
                                     "T0::T2::T3.nodeName:create(T3-00)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(1)\n"
                                     "T0::T2::T4.F1:create(3)\n"
                                     "T0::T2::T4.F2:create(1)\n"
                                     "T0::T2::T4.F3:create(8)\n"
                                     "T0::T2::T4.nodeName:create(T4-00)\n"};
    vector<string> treeExpectDiff_bak;
    treeExpectDiff_bak.push_back(treeExpectDiff[0]);
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff1.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff1 = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                      "T0.F0:create(1)\n"
                                      "T0.F1:create(3)\n"
                                      "T0.nodeName:create(T0-00)\n"
                                      "T0.T0::T1:create\n"
                                      "T0::T1.F0:create(1)\n"
                                      "T0::T1.F1:create(10)\n"
                                      "T0::T1.nodeName:create(T1-00)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2.ID:create(1)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-00)\n"
                                      "T0::T2.T0::T2::T3:create\n"
                                      "T0::T2::T3.F0:create(1)\n"
                                      "T0::T2::T3.F1:create(3)\n"
                                      "T0::T2::T3.F2:create(9)\n"
                                      "T0::T2::T3.nodeName:create(T3-00)\n"
                                      "T0::T2.T0::T2::T4:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2::T4.ID:create(1)\n"
                                      "T0::T2::T4.F1:create(3)\n"
                                      "T0::T2::T4.F2:create(1)\n"
                                      "T0::T2::T4.F3:create(8)\n"
                                      "T0::T2::T4.nodeName:create(T4-00)\n"
                                      "T0::T2.T0::T2::T4:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2::T4.ID:create(2)\n"
                                      "T0::T2::T4.F1:create(3)\n"
                                      "T0::T2::T4.F2:create(2)\n"
                                      "T0::T2::T4.F3:create(2)\n"
                                      "T0::T2::T4.nodeName:create(T4-01)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff1, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, "one"));
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff_bak, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
}

// 测试多个savePoint回滚后生成diff
TEST_F(StYang, PartialDiffMultiSavePointRollbackAsync)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> treeExpectDiff = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                     "T0.F0:create(1)\n"
                                     "T0.F1:create(3)\n"
                                     "T0.nodeName:create(T0-00)\n"
                                     "T0.T0::T1:create\n"
                                     "T0::T1.F0:create(1)\n"
                                     "T0::T1.F1:create(8)\n"
                                     "T0::T1.nodeName:create(T1-00)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                     "T0::T2.ID:create(1)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-00)\n"
                                     "T0::T2.T0::T2::T3:create\n"
                                     "T0::T2::T3.F0:create(1)\n"
                                     "T0::T2::T3.F1:create(3)\n"
                                     "T0::T2::T3.F2:create(9)\n"
                                     "T0::T2::T3.nodeName:create(T3-00)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(1)\n"
                                     "T0::T2::T4.F1:create(3)\n"
                                     "T0::T2::T4.F2:create(1)\n"
                                     "T0::T2::T4.F3:create(8)\n"
                                     "T0::T2::T4.nodeName:create(T4-00)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff1.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff1 = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                      "T0.F0:create(1)\n"
                                      "T0.F1:create(3)\n"
                                      "T0.nodeName:create(T0-00)\n"
                                      "T0.T0::T1:create\n"
                                      "T0::T1.F0:create(1)\n"
                                      "T0::T1.F1:create(10)\n"
                                      "T0::T1.nodeName:create(T1-00)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2.ID:create(1)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-00)\n"
                                      "T0::T2.T0::T2::T3:create\n"
                                      "T0::T2::T3.F0:create(1)\n"
                                      "T0::T2::T3.F1:create(3)\n"
                                      "T0::T2::T3.F2:create(9)\n"
                                      "T0::T2::T3.nodeName:create(T3-00)\n"
                                      "T0::T2.T0::T2::T4:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2::T4.ID:create(1)\n"
                                      "T0::T2::T4.F1:create(3)\n"
                                      "T0::T2::T4.F2:create(1)\n"
                                      "T0::T2::T4.F3:create(8)\n"
                                      "T0::T2::T4.nodeName:create(T4-00)\n"
                                      "T0::T2.T0::T2::T4:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2::T4.ID:create(2)\n"
                                      "T0::T2::T4.F1:create(3)\n"
                                      "T0::T2::T4.F2:create(2)\n"
                                      "T0::T2::T4.F3:create(2)\n"
                                      "T0::T2::T4.nodeName:create(T4-01)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff1, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "two"));
    const char *editStr2 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff2.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr2)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, "one"));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr2)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff2 = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                      "T0.F0:create(1)\n"
                                      "T0.F1:create(3)\n"
                                      "T0.nodeName:create(T0-00)\n"
                                      "T0.T0::T1:create\n"
                                      "T0::T1.F0:create(1)\n"
                                      "T0::T1.F1:create(8)\n"
                                      "T0::T1.nodeName:create(T1-00)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2.ID:create(1)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-00)\n"
                                      "T0::T2.T0::T2::T3:create\n"
                                      "T0::T2::T3.F0:create(1)\n"
                                      "T0::T2::T3.F1:create(3)\n"
                                      "T0::T2::T3.F2:create(9)\n"
                                      "T0::T2::T3.nodeName:create(T3-00)\n"
                                      "T0::T2.T0::T2::T4:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2::T4.ID:create(1)\n"
                                      "T0::T2::T4.F1:create(3)\n"
                                      "T0::T2::T4.F2:create(1)\n"
                                      "T0::T2::T4.F3:create(8)\n"
                                      "T0::T2::T4.nodeName:create(T4-00)\n"
                                      "T0::T2.T0::T2::T4:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2::T4.ID:create(4)\n"
                                      "T0::T2::T4.F1:create(3)\n"
                                      "T0::T2::T4.F2:create(2)\n"
                                      "T0::T2::T4.F3:create(3)\n"
                                      "T0::T2::T4.nodeName:create(T4-02)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff2, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
}

// 测试删除savePoint后创建同名savePoint回滚后生成diff
TEST_F(StYang, PartialDiffReleaseSavePointRollbackAsync)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff1.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "two"));
    const char *editStr2 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff2.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr2)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltTransReleaseSavepointAsync(asyncConn, "one"));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr3 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff3.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr3)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, "one"));
    vector<string> treeExpectDiff3 = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                      "T0.F0:create(1)\n"
                                      "T0.F1:create(3)\n"
                                      "T0.nodeName:create(T0-00)\n"
                                      "T0.T0::T1:create\n"
                                      "T0::T1.F0:create(1)\n"
                                      "T0::T1.F1:create(10)\n"
                                      "T0::T1.nodeName:create(T1-00)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2.ID:create(1)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-00)\n"
                                      "T0::T2.T0::T2::T3:create\n"
                                      "T0::T2::T3.F0:create(1)\n"
                                      "T0::T2::T3.F1:create(3)\n"
                                      "T0::T2::T3.F2:create(9)\n"
                                      "T0::T2::T3.nodeName:create(T3-00)\n"
                                      "T0::T2.T0::T2::T4:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2::T4.ID:create(1)\n"
                                      "T0::T2::T4.F1:create(3)\n"
                                      "T0::T2::T4.F2:create(1)\n"
                                      "T0::T2::T4.F3:create(8)\n"
                                      "T0::T2::T4.nodeName:create(T4-00)\n"
                                      "T0::T2.T0::T2::T4:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2::T4.ID:create(2)\n"
                                      "T0::T2::T4.F1:create(3)\n"
                                      "T0::T2::T4.F2:create(2)\n"
                                      "T0::T2::T4.F3:create(2)\n"
                                      "T0::T2::T4.nodeName:create(T4-01)\n"
                                      "T0::T2.T0::T2::T4:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                      "T0::T2::T4.ID:create(3)\n"
                                      "T0::T2::T4.F1:create(3)\n"
                                      "T0::T2::T4.F2:create(2)\n"
                                      "T0::T2::T4.F3:create(3)\n"
                                      "T0::T2::T4.nodeName:create(T4-02)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff3, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
}

// 测试先创建savePoint再开启diff后创建savePoint回滚后生成diff
TEST_F(StYang, PartialDiffSavePointBeforeDiffRollbackAsync)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff1.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "two"));
    const char *editStr2 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff2.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr2)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltTransReleaseSavepointAsync(asyncConn, "one"));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr3 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff3.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr3)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, "one"));
    vector<string> treeExpectDiff3 = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                      "T0.F0:create(1)\n"
                                      "T0.F1:create(3)\n"
                                      "T0.nodeName:create(T0-00)\n"
                                      "T0.T0::T1:create\n"
                                      "T0::T1.F0:create(1)\n"
                                      "T0::T1.F1:create(10)\n"
                                      "T0::T1.nodeName:create(T1-00)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2.ID:create(1)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-00)\n"
                                      "T0::T2.T0::T2::T3:create\n"
                                      "T0::T2::T3.F0:create(1)\n"
                                      "T0::T2::T3.F1:create(3)\n"
                                      "T0::T2::T3.F2:create(9)\n"
                                      "T0::T2::T3.nodeName:create(T3-00)\n"
                                      "T0::T2.T0::T2::T4:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2::T4.ID:create(1)\n"
                                      "T0::T2::T4.F1:create(3)\n"
                                      "T0::T2::T4.F2:create(1)\n"
                                      "T0::T2::T4.F3:create(8)\n"
                                      "T0::T2::T4.nodeName:create(T4-00)\n"
                                      "T0::T2.T0::T2::T4:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2::T4.ID:create(2)\n"
                                      "T0::T2::T4.F1:create(3)\n"
                                      "T0::T2::T4.F2:create(2)\n"
                                      "T0::T2::T4.F3:create(2)\n"
                                      "T0::T2::T4.nodeName:create(T4-01)\n"
                                      "T0::T2.T0::T2::T4:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                      "T0::T2::T4.ID:create(3)\n"
                                      "T0::T2::T4.F1:create(3)\n"
                                      "T0::T2::T4.F2:create(2)\n"
                                      "T0::T2::T4.F3:create(3)\n"
                                      "T0::T2::T4.nodeName:create(T4-02)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff3, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
}

/**
 * 测试场景：NP T0 -> List T2 -> List T4
 * 2、创建savepoint1
 * 3、编辑数据
 * 4、创建savepoint2
 * 5、先删除后创建同样数据
 * 6、回滚savepoint2
 * 7、创建savepoint3
 * 8、先删除后创建同样数据
 */
TEST_F(StYang, PartialDiffSavePointBugFix1)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "two"));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff4.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, "two"));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "three"));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff3 = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                      "T0.F0:create(1)\n"
                                      "T0.F1:create(3)\n"
                                      "T0.nodeName:create(T0-00)\n"
                                      "T0.T0::T1:create\n"
                                      "T0::T1.F0:create(1)\n"
                                      "T0::T1.F1:create(8)\n"
                                      "T0::T1.nodeName:create(T1-00)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2.ID:create(1)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-00)\n"
                                      "T0::T2.T0::T2::T3:create\n"
                                      "T0::T2::T3.F0:create(1)\n"
                                      "T0::T2::T3.F1:create(3)\n"
                                      "T0::T2::T3.F2:create(9)\n"
                                      "T0::T2::T3.nodeName:create(T3-00)\n"
                                      "T0::T2.T0::T2::T4:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2::T4.ID:create(1)\n"
                                      "T0::T2::T4.F1:create(3)\n"
                                      "T0::T2::T4.F2:create(1)\n"
                                      "T0::T2::T4.F3:create(8)\n"
                                      "T0::T2::T4.nodeName:create(T4-00)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff3, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
}

/**
 * 测试场景：NP T0 -> List T2 -> List T4
 * 1、创建T2（1）和T2（2），对T2（1）和T2（2）做none原语
 * 2、创建savepoint
 * 3、对T2（1）做none原语，同时插入T4
 * 4、回滚savepoint
 * 5、对T2（2）做none原语，同时插入T4
 */
TEST_F(StYang, PartialDiffSavePointBugFix2)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff5.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff6.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr2 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff2.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr2)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, "one"));

    const char *editStr3 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff7.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr3)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> treeExpectDiff = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0::T2.T0::T2::T4:create[(priKey(PID:2,F0:3)),(NULL)]\n"
        "T0::T2::T4.ID:create(2)\n"
        "T0::T2::T4.F1:create(3)\n"
        "T0::T2::T4.F2:create(2)\n"
        "T0::T2::T4.F3:create(3)\n"
        "T0::T2::T4.nodeName:create(T4-02)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
}

/*
 * 测试场景：开启事务，执行不开diff的DML操作，再执行开diff的DML操作，预期报错（不允许在开启事务后、开启diff前进行DML操作）
 */
TEST_F(StYang, PartialDiffNoDmlBetweenTransAndDiff)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    // 开启事务，不开 diff，插入数据
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(g_baseDataJsonTree)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_OFF));

    // 执行带 diff 的删除操作，预期报错
    const char *diffEditStr = "006_graph/yang/edit_with_tree_diff/tree_delete_root.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync<GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE>(
        asyncConn, asyncStmt, "T0", (GetFileContext(diffEditStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    // 不开 diff，继续进行各种 DML 操作，预期失败，GMERR_INVALID_OPTION
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_list_multi_diff.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync<GMERR_INVALID_OPTION>(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_OFF));

    //  执行带 diff 的删除操作，预期报错
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync<GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE>(
        asyncConn, asyncStmt, "T0", (GetFileContext(diffEditStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
}

TEST_F(StYang, PartialDiffDefaultNPNodeCreate1)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_8.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_1.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexLabelAsync(asyncStmt, vertexLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultNPNodeCreate2)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_8.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_2.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexLabelAsync(asyncStmt, vertexLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.F0:create(1)\n"
                                        "T0.T0Node:update\n"
                                        "T0Node.T00Node:update\n"
                                        "T00Node.F0:update(1,0)\n"
                                        "T00Node.F1:update(cba,abc)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultNPNodeCreate3)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_15.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_0.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexLabelAsync(asyncStmt, vertexLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultNPNodeDedelete1)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_8.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_8.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_3.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, NULL, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.F0:remove(1)\n"
                                        "T0.T0Node:update\n"
                                        "T0Node.T00Node:update\n"
                                        "T00Node.F0:update(0,1)\n"
                                        "T00Node.F1:update(abc,cba)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultNPNodeDedelete2)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_8.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_9.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_3.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, NULL, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.F0:remove(1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultNPNodeMerge1)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_8.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_10.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_4.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, NULL, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultNPNodeMerge2)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_8.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_10.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_5.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, NULL, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.T0Node:update\n"
                                        "T0Node.T00Node:update\n"
                                        "T00Node.F0:update(1,0)\n"
                                        "T00Node.F1:update(cba,abc)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultPNodeCreate1)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_8.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_6.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexLabelAsync(asyncStmt, vertexLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.F0:create(1)\n"
                                        "T0.T1Node:create\n"
                                        "T1Node.F0:create(1)\n"
                                        "T1Node.F1:create(abc)\n"
                                        "T1Node.T11Node:create\n"
                                        "T11Node.F0:create(0)\n"
                                        "T11Node.F1:create(abc)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultPNodeCreate2)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_16.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_35.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexLabelAsync(asyncStmt, vertexLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:create[(priKey(ID:1)),(NULL)]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultPNodeCreate3)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_16.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_36.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexLabelAsync(asyncStmt, vertexLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                        "T0.T1Node:create\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultPNodeDelete1)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_8.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_11.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_7.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, NULL, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.T1Node:remove\n"
                                        "T1Node.F0:remove(1)\n"
                                        "T1Node.F1:remove(abc)\n"
                                        "T1Node.T11Node:remove\n"
                                        "T11Node.F0:remove(0)\n"
                                        "T11Node.F1:remove(abc)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate1)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_9.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_8.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexLabelAsync(asyncStmt, vertexLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.F0:create(1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate2)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_9.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_9.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexLabelAsync(asyncStmt, vertexLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.F0:create(1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate3)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_9.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_10.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexLabelAsync(asyncStmt, vertexLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.F0:create(1)\n"
                                        "T0.T1Choice:update\n"
                                        "T1Choice.T11Case:update\n"
                                        "T11Case.F0:update(1,0)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate4)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_9.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_11.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexLabelAsync(asyncStmt, vertexLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.F0:create(1)\n"
                                        "T0.T1Choice:update\n"
                                        "T1Choice.T11Case:remove\n"
                                        "T11Case.F0:remove(0)\n"
                                        "T1Choice.T12Case:create\n"
                                        "T12Case.F0:create(1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate5)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_9.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_12.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexLabelAsync(asyncStmt, vertexLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.F0:create(1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate6)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_9.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_12.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_13.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, NULL, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.T1Choice:update\n"
                                        "T1Choice.T12Case:remove\n"
                                        "T12Case.F0:remove(1)\n"
                                        "T1Choice.T13Case:create\n"
                                        "T13Case.T2Choice:create\n"
                                        "T2Choice.T22Case:create\n"
                                        "T22Case.F0:create(1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultChoiceCaseDelete1)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_9.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_12.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_3.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, NULL, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.F0:remove(1)\n"
                                        "T0.T1Choice:update\n"
                                        "T1Choice.T11Case:create\n"
                                        "T11Case.F0:create(0)\n"
                                        "T1Choice.T12Case:remove\n"
                                        "T12Case.F0:remove(1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultChoiceCaseDelete2)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_9.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_13.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_3.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, NULL, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.F0:remove(1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultChoiceCaseDelete3)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_12.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_15.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_18.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, NULL, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

static const char *g_defaultChoiceCaseDel4 = R"(
    {
        "op": "none",
        "key_name": "PK",
        "key_value": [
            1
        ],
        "T1Choice": {
            "op": "none",
            "T13Case": {
                "op": "none",
                "T2Choice": {
                    "op": "none",
                    "T21Case": {
                        "op": "none",
                        "T3": {
                            "op": "none",
                            "F0": 1
                        }
                    }
                }
            }
        }
    }
)";

static const char *g_defaultChoiceCaseDel4Cfg = R"(
{
    "yang_model":1,
    "T1Choice": {
        "T13Case": {
            "T2Choice": {
                "T21Case": {
                    "T3": {
                        "F0": {"propOp":"delete"}
                    }
                }
            }
        }
    }
}
)";

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case3下一个字段设值
 * case3字段删除，变为空节点
 * 预期diff case3删除 case1创建
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseDelete4)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_21.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", g_defaultChoiceCaseDel4, g_defaultChoiceCaseDel4Cfg, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T1Choice:update\n"
        "T1Choice.T11Case:create\n"
        "T11Case.F0:create(0)\n"
        "T11Case.T1:create\n"
        "T1.F0:create(0)\n"
        "T1Choice.T13Case:remove\n"
        "T13Case.T2Choice:remove\n"
        "T2Choice.T21Case:remove\n"
        "T21Case.F0:remove(0)\n"
        "T21Case.T3:remove\n"
        "T3.F0:remove(1)\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:0))]\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:1), preKey(PID:1,F0:0))]\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

static const char *g_defaultChoiceCaseDel5 = R"(
    {
        "op": "none",
        "key_name": "PK",
        "key_value": [
            1
        ],
        "T1Choice": {
            "op": "none",
            "T11Case": {
                "op": "none",
                "T1": {
                    "op": "none",
                    "F0": 1
                }
            }
        }
    }
)";

static const char *g_defaultChoiceCaseDel5Cfg = R"(
{
    "yang_model":1,
    "T1Choice": {
        "T11Case": {
            "T1": {
                "F0": {"propOp":"delete"}
            }
        }
    }
}
)";

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case1下有一个字段
 * case1字段删除，变为空节点
 * 预期diff case1 update
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseDelete5)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_22.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", g_defaultChoiceCaseDel5, g_defaultChoiceCaseDel5Cfg, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.T1Choice:update\n"
                                        "T1Choice.T11Case:update\n"
                                        "T11Case.T1:update\n"
                                        "T1.F0:update(0,1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case3下带有一个空np节点
 * 根节点删除
 * 预期diff case1 remove
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseDelete6)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_23.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_delete_root.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:remove[(NULL),(priKey(ID:1))]\n"
                                        "T0.F0:remove(1)\n"
                                        "T0.T1Choice:remove\n"
                                        "T1Choice.T11Case:remove\n"
                                        "T11Case.F0:remove(0)\n"
                                        "T11Case.T1:remove\n"
                                        "T1.F0:remove(0)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case3下带有一个空p节点
 * 根节点删除
 * 预期diff case3 remove
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseDelete7)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_24.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_delete_root.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:remove[(NULL),(priKey(ID:1))]\n"
        "T0.F0:remove(1)\n"
        "T0.T1Choice:remove\n"
        "T1Choice.T13Case:remove\n"
        "T13Case.T2Choice:remove\n"
        "T2Choice.T21Case:remove\n"
        "T21Case.F0:remove(0)\n"
        "T21Case.T2:remove\n"
        "T2.F0:remove(0)\n"
        "T21Case.T3:remove\n"
        "T3.F0:remove(0)\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:0))]\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:1), preKey(PID:1,F0:0))]\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case1下带有一个空np节点
 * 根节点删除
 * 预期diff case1 remove
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseDelete8)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_25.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_delete_root.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:remove[(NULL),(priKey(ID:1))]\n"
                                        "T0.F0:remove(1)\n"
                                        "T0.T1Choice:remove\n"
                                        "T1Choice.T11Case:remove\n"
                                        "T11Case.F0:remove(0)\n"
                                        "T11Case.T1:remove\n"
                                        "T1.F0:remove(0)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case3下只带有一个list节点
 * case3只删除list节点
 * 预期diff case3 remove case1 create
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseDelete9)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_26.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_41.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T1Choice:update\n"
        "T1Choice.T11Case:create\n"
        "T11Case.F0:create(0)\n"
        "T11Case.T1:create\n"
        "T1.F0:create(0)\n"
        "T1Choice.T13Case:remove\n"
        "T13Case.T2Choice:remove\n"
        "T2Choice.T21Case:remove\n"
        "T21Case.F0:remove(0)\n"
        "T21Case.T3:remove\n"
        "T3.F0:remove(0)\n"
        "T21Case.T21Case::List:remove[(NULL),(priKey(PID:1,F0:1))]\n"
        "T21Case::List.ID:remove(1)\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:0))]\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:1), preKey(PID:1,F0:0))]\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case3下只带有一个leaflist默认值节点
 * case3删除默认值leaflist节点
 * 预期diff case3 remove case1 create
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseDelete10)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_28.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_50.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T1Choice:update\n"
        "T1Choice.T11Case:create\n"
        "T11Case.F0:create(0)\n"
        "T11Case.T1:create\n"
        "T1.F0:create(0)\n"
        "T1Choice.T13Case:remove\n"
        "T13Case.T2Choice:remove\n"
        "T2Choice.T21Case:remove\n"
        "T21Case.F0:remove(0)\n"
        "T21Case.T3:remove\n"
        "T3.F0:remove(0)\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:0))]\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:1), preKey(PID:1,F0:0))]\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case3下只带有一个默认和非默认值leaflist节点
 * case3删除部分leaflist节点
 * 预期diff case3 update
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseDelete11)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_29.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_51.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T1Choice:update\n"
        "T1Choice.T13Case:update\n"
        "T13Case.T2Choice:update\n"
        "T2Choice.T21Case:update\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T21Case.T21Case::LeafList:update[(priKey(PID:1,F0:4), preKey(PID:1,F0:2)),(priKey(PID:1,F0:4), "
        "preKey(PID:1,F0:3))]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate7)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_10.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_8.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexLabelAsync(asyncStmt, vertexLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                        "T0.F0:create(1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate8)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_12.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_16.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_17.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, NULL, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate9)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_12.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_16.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_19.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, NULL, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.T1Choice:update\n"
                                        "T1Choice.T11Case:update\n"
                                        "T11Case.T112Node:create\n"
                                        "T112Node.F0:create(0)\n"
                                        "T112Node.F1:create(abc)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case1创建空的np节点
 * 预期diff case1 create
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate10)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree, g_diffCfgJsonTree));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_37.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                        "T0.F0:create(1)\n"
                                        "T0.T1Choice:create\n"
                                        "T1Choice.T11Case:create\n"
                                        "T11Case.F0:create(0)\n"
                                        "T11Case.T1:create\n"
                                        "T1.F0:create(0)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case3创建空的np节点
 * 预期diff case1 create
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate11)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree, g_diffCfgJsonTree));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_38.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                        "T0.F0:create(1)\n"
                                        "T0.T1Choice:create\n"
                                        "T1Choice.T11Case:create\n"
                                        "T11Case.F0:create(0)\n"
                                        "T11Case.T1:create\n"
                                        "T1.F0:create(0)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case3创建空的p节点
 * 预期diff case3 create
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate12)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree, g_diffCfgJsonTree));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_39.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:create[(priKey(ID:1)),(NULL)]\n"
        "T0.F0:create(1)\n"
        "T0.T1Choice:create\n"
        "T1Choice.T13Case:create\n"
        "T13Case.T2Choice:create\n"
        "T2Choice.T21Case:create\n"
        "T21Case.F0:create(0)\n"
        "T21Case.T2:create\n"
        "T2.F0:create(0)\n"
        "T21Case.T3:create\n"
        "T3.F0:create(0)\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:0)),(NULL)]\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:1), preKey(PID:1,F0:0)),(NULL)]\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case3创建list节点
 * 预期diff case3 create
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate13)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree, g_diffCfgJsonTree));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_40.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:create[(priKey(ID:1)),(NULL)]\n"
        "T0.F0:create(1)\n"
        "T0.T1Choice:create\n"
        "T1Choice.T13Case:create\n"
        "T13Case.T2Choice:create\n"
        "T2Choice.T21Case:create\n"
        "T21Case.F0:create(0)\n"
        "T21Case.T3:create\n"
        "T3.F0:create(0)\n"
        "T21Case.T21Case::List:create[(priKey(PID:1,F0:1)),(NULL)]\n"
        "T21Case::List.ID:create(1)\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:0)),(NULL)]\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:1), preKey(PID:1,F0:0)),(NULL)]\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case3是空的分支
 * 插入case1带字段分支
 * 预期diff case1 update
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate14)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_23.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_42.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.T1Choice:update\n"
                                        "T1Choice.T11Case:update\n"
                                        "T11Case.T1:update\n"
                                        "T1.F0:update(1,0)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case3是空的分支
 * 插入case1空分支
 * 预期diff none
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate15)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_23.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_43.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case1是空的分支
 * 插入case3带字段分支
 * 预期diff case3 create case1 remove
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate16)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_25.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_44.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T1Choice:update\n"
        "T1Choice.T11Case:remove\n"
        "T11Case.F0:remove(0)\n"
        "T11Case.T1:remove\n"
        "T1.F0:remove(0)\n"
        "T1Choice.T13Case:create\n"
        "T13Case.T2Choice:create\n"
        "T2Choice.T21Case:create\n"
        "T21Case.F0:create(1)\n"
        "T21Case.T3:create\n"
        "T3.F0:create(0)\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:0)),(NULL)]\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:1), preKey(PID:1,F0:0)),(NULL)]\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case1是空的分支
 * 插入case3空分支
 * 预期diff none
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate17)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_25.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_45.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case1是空的分支
 * 插入case3 空p节点
 * 预期diff case3 create case1 remove
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate18)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_27.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_46.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T1Choice:update\n"
        "T1Choice.T11Case:remove\n"
        "T11Case.F0:remove(0)\n"
        "T11Case.T1:remove\n"
        "T1.F0:remove(0)\n"
        "T1Choice.T13Case:create\n"
        "T13Case.T2Choice:create\n"
        "T2Choice.T21Case:create\n"
        "T21Case.F0:create(0)\n"
        "T21Case.T2:create\n"
        "T2.F0:create(0)\n"
        "T21Case.T3:create\n"
        "T3.F0:create(0)\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:0)),(NULL)]\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:1), preKey(PID:1,F0:0)),(NULL)]\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case1是空的分支
 * 插入case3 空np节点
 * 预期diff none
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate19)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_27.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_47.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * 只插入case3 默认值leaflst
 * 预期diff case3 create
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate20)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree, g_diffCfgJsonTree));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_48.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:create[(priKey(ID:1)),(NULL)]\n"
        "T0.F0:create(1)\n"
        "T0.T1Choice:create\n"
        "T1Choice.T13Case:create\n"
        "T13Case.T2Choice:create\n"
        "T2Choice.T21Case:create\n"
        "T21Case.F0:create(0)\n"
        "T21Case.T3:create\n"
        "T3.F0:create(0)\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:0)),(NULL)]\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:1), preKey(PID:1,F0:0)),(NULL)]\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * 插入case3 默认值和非默认值leaflst
 * 预期diff case3 create
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate21)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree, g_diffCfgJsonTree));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_49.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:create[(priKey(ID:1)),(NULL)]\n"
        "T0.F0:create(1)\n"
        "T0.T1Choice:create\n"
        "T1Choice.T13Case:create\n"
        "T13Case.T2Choice:create\n"
        "T2Choice.T21Case:create\n"
        "T21Case.F0:create(0)\n"
        "T21Case.T3:create\n"
        "T3.F0:create(0)\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:0)),(NULL)]\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:1), preKey(PID:1,F0:0)),(NULL)]\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case3 带leaflist
 * 插入case1 空分支
 * 预期diff case3 remove case1 create
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate22)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_29.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_52.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T1Choice:update\n"
        "T1Choice.T11Case:create\n"
        "T11Case.F0:create(0)\n"
        "T11Case.T1:create\n"
        "T1.F0:create(0)\n"
        "T1Choice.T13Case:remove\n"
        "T13Case.T2Choice:remove\n"
        "T2Choice.T21Case:remove\n"
        "T21Case.F0:remove(0)\n"
        "T21Case.T3:remove\n"
        "T3.F0:remove(0)\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:0))]\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:1), preKey(PID:1,F0:0))]\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case3 带leaflist的分支
 * none原语插入case2带字段
 * 预期diff case3 remove case2 create
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate23)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_29.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_53.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T1Choice:update\n"
        "T1Choice.T12Case:create\n"
        "T12Case.F0:create(1)\n"
        "T1Choice.T13Case:remove\n"
        "T13Case.T2Choice:remove\n"
        "T2Choice.T21Case:remove\n"
        "T21Case.F0:remove(0)\n"
        "T21Case.T3:remove\n"
        "T3.F0:remove(0)\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:0))]\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:1), preKey(PID:1,F0:0))]\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T21Case.T21Case::LeafList:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case1 空的分支
 * none原语插入case2带字段
 * 预期diff case1 remove case2 create
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate24)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_25.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_53.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.T1Choice:update\n"
                                        "T1Choice.T11Case:remove\n"
                                        "T11Case.F0:remove(0)\n"
                                        "T11Case.T1:remove\n"
                                        "T1.F0:remove(0)\n"
                                        "T1Choice.T12Case:create\n"
                                        "T12Case.F0:create(1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case3 带空的np节点
 * none原语插入case2带字段
 * 预期diff case1 remove case2 create
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate25)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_23.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_53.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.T1Choice:update\n"
                                        "T1Choice.T11Case:remove\n"
                                        "T11Case.F0:remove(0)\n"
                                        "T11Case.T1:remove\n"
                                        "T1.F0:remove(0)\n"
                                        "T1Choice.T12Case:create\n"
                                        "T12Case.F0:create(1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case3 空分支
 * none原语插入case3 list和leaflist
 * 预期diff case3 create case1 remove
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate26)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_23.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_54.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T1Choice:update\n"
        "T1Choice.T11Case:remove\n"
        "T11Case.F0:remove(0)\n"
        "T11Case.T1:remove\n"
        "T1.F0:remove(0)\n"
        "T1Choice.T13Case:create\n"
        "T13Case.T2Choice:create\n"
        "T2Choice.T21Case:create\n"
        "T21Case.F0:create(0)\n"
        "T21Case.T3:create\n"
        "T3.F0:create(0)\n"
        "T21Case.T21Case::List:create[(priKey(PID:1,F0:0)),(NULL)]\n"
        "T21Case::List.ID:create(1)\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:0)),(NULL)]\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:1), preKey(PID:1,F0:0)),(NULL)]\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case3 空分支
 * merge原语插入case3 list和leaflist
 * 预期diff case3 create case1 remove
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate27)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_23.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_55.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T1Choice:update\n"
        "T1Choice.T11Case:remove\n"
        "T11Case.F0:remove(0)\n"
        "T11Case.T1:remove\n"
        "T1.F0:remove(0)\n"
        "T1Choice.T13Case:create\n"
        "T13Case.T2Choice:create\n"
        "T2Choice.T21Case:create\n"
        "T21Case.F0:create(0)\n"
        "T21Case.T3:create\n"
        "T3.F0:create(0)\n"
        "T21Case.T21Case::List:create[(priKey(PID:1,F0:0)),(NULL)]\n"
        "T21Case::List.ID:create(1)\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:0)),(NULL)]\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:1), preKey(PID:1,F0:0)),(NULL)]\n"
        "T21Case.T21Case::LeafList:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：choice case生成diff
 * choice case1(default) case2 case3
 * case3 带空的np节点
 * none原语插入case2 空节点
 * 预期diff none
 */
TEST_F(StYang, PartialDiffDefaultChoiceCaseCreate28)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_18.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_10.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_23.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_56.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

TEST_F(StYang, PartialDiffDefaultListCreate1)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_11.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_8.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_14.json";
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                        "T0.F0:create(1)\n"
                                        "T0.T1:create[(priKey(PID:1,F0:0)),(NULL)]\n"
                                        "T1.ID:create(1)\n"
                                        "T1.F1:create(0)\n"
                                        "T1.T11Node:create\n"
                                        "T11Node.F0:create(0)\n"
                                        "T1.T13Choice:create\n"
                                        "T13Choice.T131Case:create\n"
                                        "T131Case.F0:create(0)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

TEST_F(StYang, PartialDiffDefaultListRemove1)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_11.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_8.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_14.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_15.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.T1:remove[(NULL),(priKey(PID:1,F0:0))]\n"
                                        "T1.ID:remove(1)\n"
                                        "T1.F1:remove(0)\n"
                                        "T1.T11Node:remove\n"
                                        "T11Node.F0:remove(0)\n"
                                        "T1.T13Choice:remove\n"
                                        "T13Choice.T131Case:remove\n"
                                        "T131Case.F0:remove(0)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

TEST_F(StYang, PartialDiffDefaultListMerge1)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_11.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_8.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_14.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_16.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.T1:update[(priKey(PID:1,F0:0)),(priKey(PID:1,F0:0))]\n"
                                        "T1.T13Choice:update\n"
                                        "T13Choice.T131Case:remove\n"
                                        "T131Case.F0:remove(0)\n"
                                        "T13Choice.T132Case:create\n"
                                        "T132Case.F0:create(1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

TEST_F(StYang, PartialDiffDefaultListMerge2)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_23.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_8.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_14.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_61.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.T1:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                        "T1.ID:create(2)\n"
                                        "T1.F1:create(0)\n"
                                        "T1.T11Node:create\n"
                                        "T11Node.F0:create(0)\n"
                                        "T11Node.F1:create(enable)\n"
                                        "T0.T1:update[(priKey(PID:1,F0:0), preKey(PID:1,F0:1)),(priKey(PID:1,F0:0))]\n"
                                        "T1.T11Node:update\n"
                                        "T11Node.F1:update(enable,default)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：部分打散模型下字段五元语操作生成diff
 * 1、字段存在，删除字段
 * 2、字段不存在，创建字段
 * 3、字段存在，更新字段
 * 4、字段存在，替换字段
 */
TEST_F(StYang, PartialDiffPropertyMultiDiff)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(InitPartialDiffBasicDataAsync(
        asyncConn, asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_baseDataJsonTree));

    string editStr = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op.json");
    string editStrCfg = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_cfg.json");
    // T0删除F0字段
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), editStrCfg.c_str(), GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> expectDiff = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                 "T0.F0:remove(1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // T0创建F0字段
    editStr = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_1.json");
    editStrCfg = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_cfg_1.json");

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), editStrCfg.c_str(), GMC_YANG_DIFF_DELAY_READ_ON));
    expectDiff = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                  "T0.F0:create(100)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // T0更新F0字段
    editStr = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_2.json");
    editStrCfg = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_cfg_2.json");

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), editStrCfg.c_str(), GMC_YANG_DIFF_DELAY_READ_ON));
    expectDiff = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                  "T0.F0:update(1000,100)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // T0replace F0字段
    editStr = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_3.json");
    editStrCfg = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_cfg_3.json");

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), editStrCfg.c_str(), GMC_YANG_DIFF_DELAY_READ_ON));
    expectDiff = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                  "T0.F0:update(14000,1000)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
}

/**
 * 测试场景：部分打散模型下字符串字段五元语操作生成diff
 * 1、字符字段存在，删除字段
 * 2、字符字段不存在，创建字段
 * 3、字符字段存在，更新字段
 * 4、字符字段存在，替换字段
 */
TEST_F(StYang, PartialDiffPropertyMultiDiff2)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(InitPartialDiffBasicDataAsync(
        asyncConn, asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_baseDataJsonTree));

    string editStr = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_4.json");
    string editStrCfg = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_cfg_4.json");
    // T0删除nodeName字段
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), editStrCfg.c_str(), GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> expectDiff = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                 "T0.nodeName:remove(T0-00)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // T0创建nodeName字段
    editStr = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_5.json");
    editStrCfg = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_cfg_5.json");

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), editStrCfg.c_str(), GMC_YANG_DIFF_DELAY_READ_ON));
    expectDiff = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                  "T0.nodeName:create(test)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // T0更新nodeName字段
    editStr = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_6.json");
    editStrCfg = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_cfg_6.json");

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), editStrCfg.c_str(), GMC_YANG_DIFF_DELAY_READ_ON));
    expectDiff = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                  "T0.nodeName:update(test_property_merge,test)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // T0replace nodeName字段
    editStr = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_7.json");
    editStrCfg = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_cfg_7.json");

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), editStrCfg.c_str(), GMC_YANG_DIFF_DELAY_READ_ON));
    expectDiff = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                  "T0.nodeName:update(test_property_replace,test_property_merge)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
}

/*
下面用例的schema对应的树结构如图所示
               T0(NP节点)
          /        |       \
  T2(leaflist) T1(p节点) T4(list)
                   |        |
             T3(leaflist) T5(leaflist)
*/
// 测试场景：leaf生成diff， 测试只插入根节点场景diff生成
TEST_F(StYang, PartialDiffDefaultLeafListCreate1)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_13.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_8.json";
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.F0:create(1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * 1、只插入P节点，默认生成相连所有默认leaflist默认值
 * 2、插入NP节点所有leaflist默认值，不产生diff
 */
TEST_F(StYang, PartialDiffDefaultLeafListCreate2)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_13.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_20.json";
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.F0:create(1)\n"
                                        "T0.T0::T1:create\n"
                                        "T0::T1.F0:create(0)\n"
                                        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:0)),(NULL)]\n"
                                        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:1), preKey(PID:1,F0:0)),(NULL)]\n"
                                        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * 1、没有leaflist数据前提下，插入P节点的孩子leaflist节点，乱序
 * 2、没有leaflist数据前提下，插入NP节点的孩子leaflist节点，乱序
 */
TEST_F(StYang, PartialDiffDefaultLeafListCreate3)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_13.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_21.json";
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.F0:create(1)\n"
        "T0.T0::T1:create\n"
        "T0::T1.F0:create(0)\n"
        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:4)),(NULL)]\n"
        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:0), preKey(PID:1,F0:4)),(NULL)]\n"
        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:0)),(NULL)]\n"
        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:5)),(NULL)]\n"
        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:1), preKey(PID:1,F0:2)),(NULL)]\n"
        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:1)),(NULL)]\n"
        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:3)),(NULL)]\n"
        "T0.T0::T2:create[(priKey(PID:1,F0:4)),(NULL)]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:0), preKey(PID:1,F0:4)),(priKey(PID:1,F0:0))]\n"
        "T0.T0::T2:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:0)),(NULL)]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:2), preKey(PID:1,F0:5)),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:1), preKey(PID:1,F0:2)),(priKey(PID:1,F0:1), preKey(PID:1,F0:0))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:3), preKey(PID:1,F0:1)),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T0.T0::T2:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:3)),(NULL)]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * 1、对P节点进行none原语，插入默认leaflist节点，不产生diff
 * 2、对P节点进行none原语，插入默认leaflist节点，不产生diff
 */
TEST_F(StYang, PartialDiffDefaultLeafListCreate4)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_13.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_17.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_22.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * 1、有leaflist数据前提下，插入P节点的孩子leaflist节点，乱序
 * 2、有leaflist数据前提下，插入NP节点的孩子leaflist节点，乱序
 */
TEST_F(StYang, PartialDiffDefaultLeafListCreate5)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_13.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_17.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_23.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T0::T1:update\n"
        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:4)),(NULL)]\n"
        "T0::T1.T0::T1::T3:update[(priKey(PID:1,F0:0), preKey(PID:1,F0:4)),(priKey(PID:1,F0:0))]\n"
        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:0)),(NULL)]\n"
        "T0::T1.T0::T1::T3:update[(priKey(PID:1,F0:2), preKey(PID:1,F0:5)),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0::T1.T0::T1::T3:update[(priKey(PID:1,F0:1), preKey(PID:1,F0:2)),(priKey(PID:1,F0:1), preKey(PID:1,F0:0))]\n"
        "T0::T1.T0::T1::T3:update[(priKey(PID:1,F0:3), preKey(PID:1,F0:1)),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:3)),(NULL)]\n"
        "T0.T0::T2:create[(priKey(PID:1,F0:4)),(NULL)]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:0), preKey(PID:1,F0:4)),(priKey(PID:1,F0:0))]\n"
        "T0.T0::T2:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:0)),(NULL)]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:2), preKey(PID:1,F0:5)),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:1), preKey(PID:1,F0:2)),(priKey(PID:1,F0:1), preKey(PID:1,F0:0))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:3), preKey(PID:1,F0:1)),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T0.T0::T2:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:3)),(NULL)]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * 1、插入list节点，默认生成默认值leaflist节点
 * 2、插入list节点，同时生成默认值leaflist节点
 * 3、插入list节点，同时生成leaflist节点，乱序
 */
TEST_F(StYang, PartialDiffDefaultLeafListCreate6)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_13.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_28.json";
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.F0:create(1)\n"
                                        "T0.T0::T4:create[(priKey(PID:1,F0:0)),(NULL)]\n"
                                        "T0::T4.ID:create(1)\n"
                                        "T0::T4.T0::T4::T5:create[(priKey(PID:1,F0:0)),(NULL)]\n"
                                        "T0::T4.T0::T4::T5:create[(priKey(PID:1,F0:1), preKey(PID:1,F0:0)),(NULL)]\n"
                                        "T0::T4.T0::T4::T5:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                        "T0::T4.T0::T4::T5:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                        "T0.T0::T4:create[(priKey(PID:1,F0:1), preKey(PID:1,F0:0)),(NULL)]\n"
                                        "T0::T4.ID:create(2)\n"
                                        "T0::T4.T0::T4::T5:create[(priKey(PID:2,F0:0)),(NULL)]\n"
                                        "T0::T4.T0::T4::T5:create[(priKey(PID:2,F0:1), preKey(PID:2,F0:0)),(NULL)]\n"
                                        "T0::T4.T0::T4::T5:create[(priKey(PID:2,F0:2), preKey(PID:2,F0:1)),(NULL)]\n"
                                        "T0::T4.T0::T4::T5:create[(priKey(PID:2,F0:3), preKey(PID:2,F0:2)),(NULL)]\n"
                                        "T0.T0::T4:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                        "T0::T4.ID:create(3)\n"
                                        "T0::T4.T0::T4::T5:create[(priKey(PID:3,F0:0)),(NULL)]\n"
                                        "T0::T4.T0::T4::T5:create[(priKey(PID:3,F0:1), preKey(PID:3,F0:0)),(NULL)]\n"
                                        "T0::T4.T0::T4::T5:create[(priKey(PID:3,F0:2), preKey(PID:3,F0:1)),(NULL)]\n"
                                        "T0::T4.T0::T4::T5:create[(priKey(PID:3,F0:3), preKey(PID:3,F0:2)),(NULL)]\n"
                                        "T0::T4.T0::T4::T5:create[(priKey(PID:3,F0:4), preKey(PID:3,F0:3)),(NULL)]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * 1、leaflist表没有默认值的创建场景下生成diff
 */
TEST_F(StYang, PartialDiffDefaultLeafListCreate7)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_14.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_20.json";
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                        "T0.F0:create(1)\n"
                                        "T0.T0::T1:create\n"
                                        "T0.T0::T2:create[(priKey(PID:1,F0:0)),(NULL)]\n"
                                        "T0.T0::T2:create[(priKey(PID:1,F0:1), preKey(PID:1,F0:0)),(NULL)]\n"
                                        "T0.T0::T2:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                        "T0.T0::T2:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * 1、只插入P节点没有孩子leaflist节点，删除根节点
 * 2、NP节点下只有部分默认leaflist节点，删除根节点
 */
TEST_F(StYang, PartialDiffDefaultLeafListRemove1)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_13.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_17.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_24.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.F0:remove(1)\n"
                                        "T0.T0::T1:remove\n"
                                        "T0::T1.F0:remove(0)\n"
                                        "T0::T1.T0::T1::T3:remove[(NULL),(priKey(PID:1,F0:0))]\n"
                                        "T0::T1.T0::T1::T3:remove[(NULL),(priKey(PID:1,F0:1), preKey(PID:1,F0:0))]\n"
                                        "T0::T1.T0::T1::T3:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
                                        "T0::T1.T0::T1::T3:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * 1、P节点下有孩子leaflist节点，乱序，删除根节点
 * 2、NP节点下有孩子leaflist节点，乱序，删除根节点
 */
TEST_F(StYang, PartialDiffDefaultLeafListRemove2)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_13.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_18.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_24.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.F0:remove(1)\n"
        "T0.T0::T1:remove\n"
        "T0::T1.F0:remove(0)\n"
        "T0::T1.T0::T1::T3:remove[(NULL),(priKey(PID:1,F0:4))]\n"
        "T0::T1.T0::T1::T3:remove[(NULL),(priKey(PID:1,F0:0), preKey(PID:1,F0:4))]\n"
        "T0::T1.T0::T1::T3:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:0))]\n"
        "T0::T1.T0::T1::T3:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:5))]\n"
        "T0::T1.T0::T1::T3:remove[(NULL),(priKey(PID:1,F0:1), preKey(PID:1,F0:2))]\n"
        "T0::T1.T0::T1::T3:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:1))]\n"
        "T0::T1.T0::T1::T3:remove[(NULL),(priKey(PID:1,F0:6), preKey(PID:1,F0:3))]\n"
        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:4))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:0)),(priKey(PID:1,F0:0), preKey(PID:1,F0:4))]\n"
        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:0))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(priKey(PID:1,F0:2), preKey(PID:1,F0:5))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:1), preKey(PID:1,F0:0)),(priKey(PID:1,F0:1), preKey(PID:1,F0:2))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(priKey(PID:1,F0:3), preKey(PID:1,F0:1))]\n"
        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:6), preKey(PID:1,F0:3))]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * 1、只插入P节点没有孩子leaflist节点，删除根节点
 * 2、NP节点下包含所有默认leaflist节点，删除根节点
 */
TEST_F(StYang, PartialDiffDefaultLeafListRemove3)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_13.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_19.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_24.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.F0:remove(1)\n"
                                        "T0.T0::T1:remove\n"
                                        "T0::T1.F0:remove(0)\n"
                                        "T0::T1.T0::T1::T3:remove[(NULL),(priKey(PID:1,F0:0))]\n"
                                        "T0::T1.T0::T1::T3:remove[(NULL),(priKey(PID:1,F0:1), preKey(PID:1,F0:0))]\n"
                                        "T0::T1.T0::T1::T3:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
                                        "T0::T1.T0::T1::T3:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * 1、在有leaflist数据前提下，对NP节点进行none原语，删除默认值节点和非默认值节点
 */
TEST_F(StYang, PartialDiffDefaultLeafListRemove4)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_13.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_18.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_25.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:4))]\n"
        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:0))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:0)),(priKey(PID:1,F0:0), preKey(PID:1,F0:4))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:2), preKey(PID:1,F0:0)),(priKey(PID:1,F0:2), preKey(PID:1,F0:5))]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * 1、list节点下没有leaflist节点，删除该list节点
 * 2、list节点下只有默认值leaflist节点，删除该list节点
 * 3、list节点下有leaflist节点，乱序，删除该list节点
 */
TEST_F(StYang, PartialDiffDefaultLeafListRemove5)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_13.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_20.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_24.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.F0:remove(1)\n"
                                        "T0.T0::T4:remove[(NULL),(priKey(PID:1,F0:0))]\n"
                                        "T0::T4.ID:remove(1)\n"
                                        "T0::T4.T0::T4::T5:remove[(NULL),(priKey(PID:1,F0:0))]\n"
                                        "T0::T4.T0::T4::T5:remove[(NULL),(priKey(PID:1,F0:1), preKey(PID:1,F0:0))]\n"
                                        "T0::T4.T0::T4::T5:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
                                        "T0::T4.T0::T4::T5:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
                                        "T0.T0::T4:remove[(NULL),(priKey(PID:1,F0:1), preKey(PID:1,F0:0))]\n"
                                        "T0::T4.ID:remove(2)\n"
                                        "T0::T4.T0::T4::T5:remove[(NULL),(priKey(PID:2,F0:0))]\n"
                                        "T0::T4.T0::T4::T5:remove[(NULL),(priKey(PID:2,F0:1), preKey(PID:2,F0:0))]\n"
                                        "T0::T4.T0::T4::T5:remove[(NULL),(priKey(PID:2,F0:2), preKey(PID:2,F0:1))]\n"
                                        "T0::T4.T0::T4::T5:remove[(NULL),(priKey(PID:2,F0:3), preKey(PID:2,F0:2))]\n"
                                        "T0.T0::T4:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
                                        "T0::T4.ID:remove(3)\n"
                                        "T0::T4.T0::T4::T5:remove[(NULL),(priKey(PID:3,F0:0))]\n"
                                        "T0::T4.T0::T4::T5:remove[(NULL),(priKey(PID:3,F0:1), preKey(PID:3,F0:0))]\n"
                                        "T0::T4.T0::T4::T5:remove[(NULL),(priKey(PID:3,F0:2), preKey(PID:3,F0:1))]\n"
                                        "T0::T4.T0::T4::T5:remove[(NULL),(priKey(PID:3,F0:3), preKey(PID:3,F0:2))]\n"
                                        "T0::T4.T0::T4::T5:remove[(NULL),(priKey(PID:3,F0:4), preKey(PID:3,F0:3))]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * 1、leaflist表没有默认值的删除场景下生成diff
 */
TEST_F(StYang, PartialDiffDefaultLeafListRemove6)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_14.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_19.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_24.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:remove[(NULL),(priKey(ID:1))]\n"
                                        "T0.F0:remove(1)\n"
                                        "T0.T0::T1:remove\n"
                                        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:0))]\n"
                                        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:1), preKey(PID:1,F0:0))]\n"
                                        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
                                        "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * 在P节点下没有任何leaflist节点下进行带位置merge操作
 */
TEST_F(StYang, PartialDiffDefaultLeafListMerge1)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_13.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_17.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_26.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T0::T1:update\n"
        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:4)),(NULL)]\n"
        "T0::T1.T0::T1::T3:update[(priKey(PID:1,F0:3), preKey(PID:1,F0:4)),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T0::T1.T0::T1::T3:update[(priKey(PID:1,F0:1), preKey(PID:1,F0:3)),(priKey(PID:1,F0:1), preKey(PID:1,F0:0))]\n"
        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:1)),(NULL)]\n"
        "T0::T1.T0::T1::T3:update[(priKey(PID:1,F0:2), preKey(PID:1,F0:5)),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0::T1.T0::T1::T3:update[(priKey(PID:1,F0:0), preKey(PID:1,F0:2)),(priKey(PID:1,F0:0))]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * 在P节点下有leaflist节点下进行带位置merge操作
 */
TEST_F(StYang, PartialDiffDefaultLeafListMerge2)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_13.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_18.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_27.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T0::T1:update\n"
        "T0::T1.T0::T1::T3:update[(priKey(PID:1,F0:0), preKey(PID:1,F0:3)),(priKey(PID:1,F0:0), preKey(PID:1,F0:4))]\n"
        "T0::T1.T0::T1::T3:update[(priKey(PID:1,F0:6), preKey(PID:1,F0:0)),(priKey(PID:1,F0:6), preKey(PID:1,F0:3))]\n"
        "T0::T1.T0::T1::T3:update[(priKey(PID:1,F0:4), preKey(PID:1,F0:6)),(priKey(PID:1,F0:4))]\n"
        "T0::T1.T0::T1::T3:update[(priKey(PID:1,F0:2)),(priKey(PID:1,F0:2), preKey(PID:1,F0:5))]\n"
        "T0::T1.T0::T1::T3:update[(priKey(PID:1,F0:5), preKey(PID:1,F0:2)),(priKey(PID:1,F0:5), preKey(PID:1,F0:0))]\n"
        "T0::T1.T0::T1::T3:update[(priKey(PID:1,F0:1), preKey(PID:1,F0:5)),(priKey(PID:1,F0:1), "
        "preKey(PID:1,F0:2))]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * 在list节点下有leaflist节点场景下下进行带位置merge操作
 */
TEST_F(StYang, PartialDiffDefaultLeafListMerge3)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_13.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_20.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_29.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T0::T4:update[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0::T4.T0::T4::T5:update[(priKey(PID:3,F0:4)),(priKey(PID:3,F0:4), preKey(PID:3,F0:3))]\n"
        "T0::T4.T0::T4::T5:update[(priKey(PID:3,F0:3), preKey(PID:3,F0:4)),(priKey(PID:3,F0:3), "
        "preKey(PID:3,F0:2))]\n"
        "T0::T4.T0::T4::T5:update[(priKey(PID:3,F0:1), preKey(PID:3,F0:3)),(priKey(PID:3,F0:1), "
        "preKey(PID:3,F0:0))]\n"
        "T0::T4.T0::T4::T5:create[(priKey(PID:3,F0:5), preKey(PID:3,F0:1)),(NULL)]\n"
        "T0::T4.T0::T4::T5:update[(priKey(PID:3,F0:2), preKey(PID:3,F0:5)),(priKey(PID:3,F0:2), "
        "preKey(PID:3,F0:1))]\n"
        "T0::T4.T0::T4::T5:update[(priKey(PID:3,F0:0), preKey(PID:3,F0:2)),(priKey(PID:3,F0:0))]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * 在P节点带leaflist节点场景下进行带位置merge操作
 * 在P节点带leaflist节点场景下进行带位置merge操作
 */
TEST_F(StYang, PartialDiffDefaultLeafListMerge4)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_13.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_19.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_30.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * leaflist表没有默认值场景下带位置merge操作
 */
TEST_F(StYang, PartialDiffDefaultLeafListMerge5)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_14.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_19.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_30.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.T0::T1:update\n"
                                        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:0)),(NULL)]\n"
                                        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:1), preKey(PID:1,F0:0)),(NULL)]\n"
                                        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

TEST_F(StYang, PartialDiffDefaultLeafListReplace1)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_13.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_35.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_60.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T0::T1:update\n"
        "T0::T1.T0::T1::T3:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:0))]\n"
        "T0::T1.T0::T1::T3:update[(priKey(PID:1,F0:1), preKey(PID:1,F0:0)),(priKey(PID:1,F0:1), preKey(PID:1,F0:4))]\n"
        "T0::T1.T0::T1::T3:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:1))]\n"
        "T0::T1.T0::T1::T3:update[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(priKey(PID:1,F0:2), "
        "preKey(PID:1,F0:5))]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * 1、默认值节点先删除后创建场景下diff生成
 * 2、非默认值节点先删除后创建场景下diff生成
 */
TEST_F(StYang, PartialDiffDefaultLeafListComplex1)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_13.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_18.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_31.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * 1、默认值节点先创建后删除场景下diff生成
 * 2、非默认值节点先创建后删除场景下diff生成
 */
TEST_F(StYang, PartialDiffDefaultLeafListComplex2)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_13.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_32.json";
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.F0:create(1)\n"
                                        "T0.T0::T1:create\n"
                                        "T0::T1.F0:create(0)\n"
                                        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:0)),(NULL)]\n"
                                        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:1), preKey(PID:1,F0:0)),(NULL)]\n"
                                        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                        "T0::T1.T0::T1::T3:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：leaf生成diff
 * 1、对P节点进行none原语，默认值节点先创建后删除场景下diff生成
 * 2、对P节点进行none原语，非默认值节点先创建后删除场景下diff生成
 */
TEST_F(StYang, PartialDiffDefaultLeafListComplex3)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_13.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_9.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_17.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_33.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, edgeLabelFileTree, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
}

/**
 * 测试场景：NP T0 -> NP C5 -> leaf v4(deafult: yellow)
 * 1、对T0做replace操作，事务提交
 * 2、对T0->C5->v4 做merge操作，读diff。预期生成diff
 */
TEST_F(StYang, PartialDiffBugFix_01)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/schema/NPContainerLeaf_VertexLable.json";
    const char *replaceJsonTree = "006_graph/yang/edit_with_tree_diff/NPContainerLeaf_replace_1.json";
    const char *mergeJsonTree = "006_graph/yang/edit_with_tree_diff/NPContainerLeaf_insert_2.json";
    // 第一次，replace对T0删除
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexLabelAsync(asyncStmt, vertexLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(replaceJsonTree)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_OFF));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 第二次，对T0->C5->v4 做merge操作，读diff。
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "T0",
        (GetFileContext(mergeJsonTree)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.C5:update\n"
                                        "C5.v4:update(red,yellow)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

/**
 * 测试场景：huawei-aaa:aaa(NP) -> lam(NP) -> users(NP) -> huawei-aaa:aaa::lam::users::user(list)
 * 1、开启事务，插入base数据，huawei-aaa:aaa->lam->users->huawei-aaa:aaa::lam::users::user(key("test1"))，事务提交。
 * 2、开启事务，删除huawei-aaa:aaa；再次插入数据huawei-aaa:aaa->lam->users->huawei-aaa:aaa::lam::users::user(key("test2"))，读diff并事务提交。
 */
TEST_F(StYang, PartialDiffBugFix_02)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/SOHO_S380/SOHO_S380_VertexLabel.json";
    const char *edgeLabelFileTree = "006_graph/yang/SOHO_S380/SOHO_S380_EdgeLabel.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/SOHO_S380_huawei-aaa_aaa_base.json";
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree, g_diffCfgJsonTree));
    // 1、开启事务，插入base数据，事务提交。
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "huawei-aaa:aaa",
        (GetFileContext(baseDataJsonTree)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_OFF));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 2、开启事务，删除huawei-aaa:aaa；再次插入数据，读diff并事务提交。
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *deleteJsonTree = "006_graph/yang/edit_with_tree_diff/SOHO_S380_huawei-aaa_aaa_remove.json";
    const char *updateJsonTree = "006_graph/yang/edit_with_tree_diff/SOHO_S380_huawei-aaa_aaa_update1.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "huawei-aaa:aaa",
        (GetFileContext(deleteJsonTree)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "huawei-aaa:aaa",
        (GetFileContext(updateJsonTree)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    // 3、校验diff，事务提交。
    vector<string> expectDiffPartial = {
        "huawei-aaa:aaa:update[(priKey(:id:1)),(priKey(:id:1))]\n"
        "huawei-aaa:aaa.lam:update\n"
        "lam.users:update\n"
        "users.huawei-aaa:aaa::lam::users::user:create[(priKey(:pid:1,name:test2), preKey(:pid:1,name:test1)),(NULL)]\n"
        "huawei-aaa:aaa::lam::users::user.:id:create(2)\n"
        "huawei-aaa:aaa::lam::users::user.group-name:create(admin)\n"
        "huawei-aaa:aaa::lam::users::user.service-terminal:create(false)\n"
        "huawei-aaa:aaa::lam::users::user.service-api:create(false)\n"
        "huawei-aaa:aaa::lam::users::user.password-force-change:create(true)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree));
}

/**
 * 测试场景：创建空NP节点，该节点不带默认值，diff不返回该节点
 */
TEST_F(StYang, PartialDiffBugFix_03)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_7.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_7.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_34.json";
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree, g_diffCfgJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                        "T0.F0:create(1)\n"
                                        "T0.T0Node:create\n"
                                        "T0Node.F0:create(100)\n"
                                        "T0Node.T1:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                        "T1.ID:create(1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree));
}

/**
 * 测试场景：先创建后删除再创建同一主键数据
 */
TEST_F(StYang, PartialDiffBugFix_04)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_7.json";
    const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_7.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_34.json";
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_default_op_24.json";
    const char *editStr2 = "006_graph/yang/edit_with_tree_diff/tree_default_op_34.json";
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree, g_diffCfgJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr2)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                        "T0.F0:create(1)\n"
                                        "T0.T0Node:create\n"
                                        "T0Node.F0:create(100)\n"
                                        "T0Node.T1:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                        "T1.ID:create(1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree));
}

/**
 * 测试场景：NP T0 -> List T2 -> List T4
 * 1、对T2做merge
 * 2、对T2子树删除
 * 3、重新插入T2子树
 */
TEST_F(StYang, PartialDiffBugFix_05)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff.json";

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_merge_delete_diff.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    const char *editStr2 = "006_graph/yang/edit_with_tree_diff/tree_merge_delete_diff1.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr2)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    const char *editStr3 = "006_graph/yang/edit_with_tree_diff/tree_merge_delete_diff2.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr3)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> treeExpectDiff = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
}

TEST_F(StYang, PartialDiffBugFix_06)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff5.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff9.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                     "T0.T0::T1:update\n"
                                     "T0::T1.F0:update(2,1)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                     "T0::T2.ID:create(3)\n"
                                     "T0::T2.T0::T2::T4:create[(priKey(PID:3,F0:3)),(NULL)]\n"
                                     "T0::T2::T4.ID:create(1)\n"
                                     "T0::T2::T4.F1:create(3)\n"
                                     "T0::T2::T4.F2:create(2)\n"
                                     "T0::T2::T4.F3:create(3)\n"
                                     "T0::T2::T4.nodeName:create(T4-02)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));

    const char *editStr2 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff8.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr2)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff1 = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                      "T0.T0::T1:update\n"
                                      "T0::T1.F0:update(2,1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff1, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

TEST_F(StYang, PartialDiffBugFix_08)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff12.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff13.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                     "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
                                     "T0::T2.ID:remove(3)\n"
                                     "T0::T2.F1:remove(3)\n"
                                     "T0::T2.F2:remove(5)\n"
                                     "T0::T2.nodeName:remove(T2-01)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, "one"));

    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr2 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff14.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr2)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff1 = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:1)),(priKey(PID:1,F0:1), preKey(PID:1,F0:0))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:0), preKey(PID:1,F0:1)),(priKey(PID:1,F0:0))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:2), preKey(PID:1,F0:0)),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T0.T0::T2:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
        "T0::T2.ID:create(4)\n"
        "T0::T2.F1:create(3)\n"
        "T0::T2.F2:create(5)\n"
        "T0::T2.nodeName:create(T2-02)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff1, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

TEST_F(StYang, PartialDiffBugFix_10)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff12.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff13.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                     "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
                                     "T0::T2.ID:remove(3)\n"
                                     "T0::T2.F1:remove(3)\n"
                                     "T0::T2.F2:remove(5)\n"
                                     "T0::T2.nodeName:remove(T2-01)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, "one"));

    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr2 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff17.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr2)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff1 = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T0::T2:update[(priKey(PID:1,F0:1), preKey(PID:1,F0:0)),(priKey(PID:1,F0:1), preKey(PID:1,F0:0))]\n"
        "T0::T2.F1:update(10,3)\n"
        "T0.T0::T2:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
        "T0::T2.ID:create(4)\n"
        "T0::T2.F1:create(3)\n"
        "T0::T2.F2:create(5)\n"
        "T0::T2.nodeName:create(T2-02)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff1, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

/**
 * 测试场景：NP T0 -> List T2
 * 1、插入6条T2
 * 2、查询diff
 * 3、移动T2（5）到T2（3）之前
 * 4、查询diff
 */
TEST_F(StYang, PartialDiffBugFix_07)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff10.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                     "T0.F0:create(1)\n"
                                     "T0.F1:create(3)\n"
                                     "T0.nodeName:create(T0-00)\n"
                                     "T0.T0::T1:create\n"
                                     "T0::T1.F0:create(1)\n"
                                     "T0::T1.F1:create(8)\n"
                                     "T0::T1.nodeName:create(T1-00)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                     "T0::T2.ID:create(1)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-00)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                     "T0::T2.ID:create(2)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                     "T0::T2.ID:create(3)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
                                     "T0::T2.ID:create(4)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
                                     "T0::T2.ID:create(5)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:5)),(NULL)]\n"
                                     "T0::T2.ID:create(6)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff11.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff1 = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                      "T0.F0:create(1)\n"
                                      "T0.F1:create(3)\n"
                                      "T0.nodeName:create(T0-00)\n"
                                      "T0.T0::T1:create\n"
                                      "T0::T1.F0:create(1)\n"
                                      "T0::T1.F1:create(8)\n"
                                      "T0::T1.nodeName:create(T1-00)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2.ID:create(1)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-00)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2.ID:create(2)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:2)),(NULL)]\n"
                                      "T0::T2.ID:create(6)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:6)),(NULL)]\n"
                                      "T0::T2.ID:create(3)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
                                      "T0::T2.ID:create(4)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
                                      "T0::T2.ID:create(5)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff1, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

/**
 * DTS2023042314623
 * 测试场景：NP T0 -> List T2
 * 1、插入6条T2
 * 2、查询diff
 * 3、移动T2（5）到T2（6）之后
 * 4、查询diff
 */
TEST_F(StYang, PartialDiffBugFix_07_01)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff10.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                     "T0.F0:create(1)\n"
                                     "T0.F1:create(3)\n"
                                     "T0.nodeName:create(T0-00)\n"
                                     "T0.T0::T1:create\n"
                                     "T0::T1.F0:create(1)\n"
                                     "T0::T1.F1:create(8)\n"
                                     "T0::T1.nodeName:create(T1-00)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                     "T0::T2.ID:create(1)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-00)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                     "T0::T2.ID:create(2)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                     "T0::T2.ID:create(3)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
                                     "T0::T2.ID:create(4)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
                                     "T0::T2.ID:create(5)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:5)),(NULL)]\n"
                                     "T0::T2.ID:create(6)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff20.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff1 = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                      "T0.F0:create(1)\n"
                                      "T0.F1:create(3)\n"
                                      "T0.nodeName:create(T0-00)\n"
                                      "T0.T0::T1:create\n"
                                      "T0::T1.F0:create(1)\n"
                                      "T0::T1.F1:create(8)\n"
                                      "T0::T1.nodeName:create(T1-00)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2.ID:create(1)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-00)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2.ID:create(2)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                      "T0::T2.ID:create(3)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
                                      "T0::T2.ID:create(4)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:4)),(NULL)]\n"
                                      "T0::T2.ID:create(6)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:6)),(NULL)]\n"
                                      "T0::T2.ID:create(5)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff1, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

/**
 * DTS2023042314623
 * 测试场景：NP T0 -> List T2
 * 1、插入6条T2
 * 2、查询diff
 * 3、移动T2（5）到T2（1）之前
 * 4、查询diff
 */
TEST_F(StYang, PartialDiffBugFix_07_02)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff10.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                     "T0.F0:create(1)\n"
                                     "T0.F1:create(3)\n"
                                     "T0.nodeName:create(T0-00)\n"
                                     "T0.T0::T1:create\n"
                                     "T0::T1.F0:create(1)\n"
                                     "T0::T1.F1:create(8)\n"
                                     "T0::T1.nodeName:create(T1-00)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                     "T0::T2.ID:create(1)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-00)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                     "T0::T2.ID:create(2)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                     "T0::T2.ID:create(3)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
                                     "T0::T2.ID:create(4)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
                                     "T0::T2.ID:create(5)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:5)),(NULL)]\n"
                                     "T0::T2.ID:create(6)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff21.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff1 = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                      "T0.F0:create(1)\n"
                                      "T0.F1:create(3)\n"
                                      "T0.nodeName:create(T0-00)\n"
                                      "T0.T0::T1:create\n"
                                      "T0::T1.F0:create(1)\n"
                                      "T0::T1.F1:create(8)\n"
                                      "T0::T1.nodeName:create(T1-00)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:5)),(NULL)]\n"
                                      "T0::T2.ID:create(5)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:1), preKey(PID:1,F0:5)),(NULL)]\n"
                                      "T0::T2.ID:create(1)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-00)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2.ID:create(2)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                      "T0::T2.ID:create(3)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
                                      "T0::T2.ID:create(4)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:4)),(NULL)]\n"
                                      "T0::T2.ID:create(6)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff1, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

/**
 * DTS2023122213978:根节点rowID被list节点复用
 * 测试场景：创建两个连接分别开始事务
 * 1、在namespace下创建两个yang树模型：
      Con_List_root   Con_List_root1
        \                \
        Con_List_Child   Con_List_Child1
 * 2、conn1和conn2各自创建savepoint
 * 3、conn1在Con_List_root插入数据，Con_List_Child插入10条数据；conn2在Con_List_root1只插入一个根节点
 * 4、conn1和conn2各自获取diff
 * 5、conn1回滚savepoint，conn2也回滚savepoint
 * 6、conn1插入步骤3相同的数据
 * 7、conn1获取diff数据，diff获取失败
 * 根因：步骤3插入数据后，Con_List_root 根节点addr为1，Con_List_Child为addr2，Con_List_root1为addr3。回滚savepoint后
 * addr1,2，3都释放了。此时conn1再次进行DML操作时，Con_List_root根节点就复用了addr3，Con_List_Child复用了addr1，
 * 在diff的filterMap是根据addr为key复用节点的，此时就会把根节点Con_List_root复用给Con_List_Child节点
 * 而Con_List_root里面有上次获取diff树残留的内容，导致后续获取diff时有问题
 */
TEST_F(StYang, PartialDiffSavepointRollback)
{
    GmcConnT *asyncConn = conn;
    GmcConnT *asyncConn1 = NULL;
    GmcStmtT *asyncStmt = stmt;
    GmcStmtT *asyncStmt1 = NULL;
    ASSERT_NO_FATAL_FAILURE(CreateAsyncConnAndStmt(&asyncConn1, &asyncStmt1, false));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt1, nspName));
    static const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/Con_List.json";
    static const char *vertexLabelFileTree1 = "006_graph/yang/edit_with_tree_diff/Con_List1.json";
    static const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/Con_List_Edge.json";
    static const char *edgeLabelFileTree1 = "006_graph/yang/edit_with_tree_diff/Con_List_Edge1.json";
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree1, edgeLabelFileTree1, g_diffCfgJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn1));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn1, "one"));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/Con_List_Edit.json";
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/Con_List_Edit1.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "Con_List_root",
        (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn1, asyncStmt1, "Con_List_root1",
        (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff;
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK, false));
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt1, treeExpectDiff, GMERR_OK, false));

    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, "one"));
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn1, "one"));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "Con_List_root",
        (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK, false));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn1));
}

/**
 * DTS2024011201988：list节点rowID被根节点复用
 * 测试场景：创建两个连接分别开始事务
 * 1、在namespace下创建两个yang树模型：
      Con_List_root   Con_List_root1
        \                \
        Con_List_Child   Con_List_Child1
 * 2、conn1和conn2各自创建savepoint
 * 3、conn1在Con_List_root插入数据，Con_List_Child插入10条数据；conn2在Con_List_root1只插入一个根节点
 * 4、conn1和conn2各自获取diff
 * 5、conn2回滚savepoint，conn1接着回滚savepoint
 * 6、conn2插入步骤3相同的数据，conn1接着插入步骤3相同数据
 * 7、conn1获取diff数据，diff获取失败
 * 根因：步骤3插入数据后，Con_List_root 根节点addr为1，Con_List_Child为addr2，Con_List_root1为addr3。回滚savepoint后
 * addr1,2，3都释放了。此时conn2再次进行DML操作时，Con_List_root1根节点就复用了addr1，Con_List_root复用了addr2，
 * 在diff的filterMap是根据addr为key复用节点的，此时就会把根节点Con_List_Child复用给Con_List_root节点
 * 而Con_List_Child里面有上次获取diff树残留的内容nextTree，导致后续获取diff时有问题
 */
TEST_F(StYang, PartialDiffSavepointRollback1)
{
    GmcConnT *asyncConn = conn;
    GmcConnT *asyncConn1 = NULL;
    GmcStmtT *asyncStmt = stmt;
    GmcStmtT *asyncStmt1 = NULL;
    ASSERT_NO_FATAL_FAILURE(CreateAsyncConnAndStmt(&asyncConn1, &asyncStmt1, false));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt1, nspName));
    static const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/Con_List.json";
    static const char *vertexLabelFileTree1 = "006_graph/yang/edit_with_tree_diff/Con_List1.json";
    static const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/Con_List_Edge.json";
    static const char *edgeLabelFileTree1 = "006_graph/yang/edit_with_tree_diff/Con_List_Edge1.json";
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree1, edgeLabelFileTree1, g_diffCfgJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn1));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn1, "one"));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/Con_List_Edit.json";
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/Con_List_Edit1.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "Con_List_root",
        (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn1, asyncStmt1, "Con_List_root1",
        (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff;
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK, false));
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt1, treeExpectDiff, GMERR_OK, false));

    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn1, "one"));
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, "one"));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn1, asyncStmt1, "Con_List_root1",
        (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "Con_List_root",
        (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK, false));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn1));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree1, edgeLabelFileTree1);
}

TEST_F(StYang, PartialDiffSavepointRollback2)
{
    GmcConnT *asyncConn = conn;
    GmcConnT *asyncConn1 = NULL;
    GmcStmtT *asyncStmt = stmt;
    GmcStmtT *asyncStmt1 = NULL;
    ASSERT_NO_FATAL_FAILURE(CreateAsyncConnAndStmt(&asyncConn1, &asyncStmt1, false));
    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(asyncStmt1, nspName));
    static const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/Con_List.json";
    static const char *vertexLabelFileTree1 = "006_graph/yang/edit_with_tree_diff/Con_List1.json";
    static const char *edgeLabelFileTree = "006_graph/yang/edit_with_tree_diff/Con_List_Edge.json";
    static const char *edgeLabelFileTree1 = "006_graph/yang/edit_with_tree_diff/Con_List_Edge1.json";
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree, g_diffCfgJsonTree));
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree1, edgeLabelFileTree1, g_diffCfgJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn1));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn1, "one"));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/Con_List_Edit.json";
    const char *editStr2 = "006_graph/yang/edit_with_tree_diff/Con_List_Edit_1.json";
    const char *editStr3 = "006_graph/yang/edit_with_tree_diff/Con_List_Edit1.json";
    const char *editStr4 = "006_graph/yang/edit_with_tree_diff/Con_List_Edit1_1.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "Con_List_root",
        (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn1, asyncStmt1, "Con_List_root1",
        (GetFileContext(editStr3)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff;

    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "two"));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn1, "two"));
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK, false));
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt1, treeExpectDiff, GMERR_OK, false));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "Con_List_root",
        (GetFileContext(editStr2)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn1, asyncStmt1, "Con_List_root1",
        (GetFileContext(editStr4)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn1, "two"));
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, "two"));
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK, false));
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt1, treeExpectDiff, GMERR_OK, false));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "Con_List_root",
        (GetFileContext(editStr2)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn1, asyncStmt1, "Con_List_root1",
        (GetFileContext(editStr4)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn1, "two"));
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, "two"));
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt1, treeExpectDiff, GMERR_OK, false));
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK, false));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn1, asyncStmt1, "Con_List_root1",
        (GetFileContext(editStr4)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "Con_List_root",
        (GetFileContext(editStr2)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn1));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree, edgeLabelFileTree);
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFileTree1, edgeLabelFileTree1);
}

/**
 * DTS2023042314623
 * 测试场景：NP T0 -> List T2
 * 1、插入6条T2
 * 2、查询diff
 * 3、移动T2（5）到T2（6）之前
 * 4、查询diff
 */
TEST_F(StYang, PartialDiffBugFix_07_03)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff10.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                     "T0.F0:create(1)\n"
                                     "T0.F1:create(3)\n"
                                     "T0.nodeName:create(T0-00)\n"
                                     "T0.T0::T1:create\n"
                                     "T0::T1.F0:create(1)\n"
                                     "T0::T1.F1:create(8)\n"
                                     "T0::T1.nodeName:create(T1-00)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                     "T0::T2.ID:create(1)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-00)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                     "T0::T2.ID:create(2)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                     "T0::T2.ID:create(3)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
                                     "T0::T2.ID:create(4)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
                                     "T0::T2.ID:create(5)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:5)),(NULL)]\n"
                                     "T0::T2.ID:create(6)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff22.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff1 = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                      "T0.F0:create(1)\n"
                                      "T0.F1:create(3)\n"
                                      "T0.nodeName:create(T0-00)\n"
                                      "T0.T0::T1:create\n"
                                      "T0::T1.F0:create(1)\n"
                                      "T0::T1.F1:create(8)\n"
                                      "T0::T1.nodeName:create(T1-00)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2.ID:create(1)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-00)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2.ID:create(2)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                      "T0::T2.ID:create(3)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
                                      "T0::T2.ID:create(4)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
                                      "T0::T2.ID:create(5)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:5)),(NULL)]\n"
                                      "T0::T2.ID:create(6)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff1, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

/**
 * DTS2023042314623
 * 测试场景：NP T0 -> List T2
 * 1、插入6条T2
 * 2、查询diff
 * 3、移动T2（5）到T2（1）之后
 * 4、查询diff
 */
TEST_F(StYang, PartialDiffBugFix_07_04)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff10.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                     "T0.F0:create(1)\n"
                                     "T0.F1:create(3)\n"
                                     "T0.nodeName:create(T0-00)\n"
                                     "T0.T0::T1:create\n"
                                     "T0::T1.F0:create(1)\n"
                                     "T0::T1.F1:create(8)\n"
                                     "T0::T1.nodeName:create(T1-00)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                     "T0::T2.ID:create(1)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-00)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                     "T0::T2.ID:create(2)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                     "T0::T2.ID:create(3)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
                                     "T0::T2.ID:create(4)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
                                     "T0::T2.ID:create(5)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:5)),(NULL)]\n"
                                     "T0::T2.ID:create(6)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff23.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff1 = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                      "T0.F0:create(1)\n"
                                      "T0.F1:create(3)\n"
                                      "T0.nodeName:create(T0-00)\n"
                                      "T0.T0::T1:create\n"
                                      "T0::T1.F0:create(1)\n"
                                      "T0::T1.F1:create(8)\n"
                                      "T0::T1.nodeName:create(T1-00)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2.ID:create(1)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-00)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2.ID:create(5)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:5)),(NULL)]\n"
                                      "T0::T2.ID:create(2)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                      "T0::T2.ID:create(3)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
                                      "T0::T2.ID:create(4)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:4)),(NULL)]\n"
                                      "T0::T2.ID:create(6)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff1, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

/**
 * DTS2023042314623
 * 测试场景：NP T0 -> List T2
 * 1、插入6条T2
 * 2、查询diff
 * 3、移动T2（6）到T2（1）之前
 * 4、查询diff
 */
TEST_F(StYang, PartialDiffBugFix_07_05)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff10.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                     "T0.F0:create(1)\n"
                                     "T0.F1:create(3)\n"
                                     "T0.nodeName:create(T0-00)\n"
                                     "T0.T0::T1:create\n"
                                     "T0::T1.F0:create(1)\n"
                                     "T0::T1.F1:create(8)\n"
                                     "T0::T1.nodeName:create(T1-00)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
                                     "T0::T2.ID:create(1)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-00)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                     "T0::T2.ID:create(2)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                     "T0::T2.ID:create(3)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
                                     "T0::T2.ID:create(4)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
                                     "T0::T2.ID:create(5)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"
                                     "T0.T0::T2:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:5)),(NULL)]\n"
                                     "T0::T2.ID:create(6)\n"
                                     "T0::T2.F1:create(3)\n"
                                     "T0::T2.F2:create(5)\n"
                                     "T0::T2.nodeName:create(T2-01)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff24.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff1 = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                      "T0.F0:create(1)\n"
                                      "T0.F1:create(3)\n"
                                      "T0.nodeName:create(T0-00)\n"
                                      "T0.T0::T1:create\n"
                                      "T0::T1.F0:create(1)\n"
                                      "T0::T1.F1:create(8)\n"
                                      "T0::T1.nodeName:create(T1-00)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:6)),(NULL)]\n"
                                      "T0::T2.ID:create(6)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:1), preKey(PID:1,F0:6)),(NULL)]\n"
                                      "T0::T2.ID:create(1)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-00)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
                                      "T0::T2.ID:create(2)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
                                      "T0::T2.ID:create(3)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
                                      "T0::T2.ID:create(4)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"
                                      "T0.T0::T2:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
                                      "T0::T2.ID:create(5)\n"
                                      "T0::T2.F1:create(3)\n"
                                      "T0::T2.F2:create(5)\n"
                                      "T0::T2.nodeName:create(T2-01)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff1, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

/**
 * 测试场景：NP T0 -> T1
 * 1、插入T0 -> T1
 * 2、fetch diff
 * 3、删除 T1
 * 4、fetch diff
 */
TEST_F(StYang, PartialDiffBugFix_09)
{
    // 开启追踪调试日志
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_17.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexLabelAsync(asyncStmt, vertexLabelFileTree, g_diffCfgJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff19.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                     "T0.F0:create(1)\n"
                                     "T0.F1:create(3)\n"
                                     "T0.nodeName:create(T0-00)\n"
                                     "T0.T0::T1:create\n"
                                     "T0::T1.F0:create(1)\n"
                                     "T0::T1.F1:create(8)\n"
                                     "T0::T1.nodeName:create(T1-00)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff18.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff1 = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                      "T0.F0:create(1)\n"
                                      "T0.F1:create(3)\n"
                                      "T0.nodeName:create(T0-00)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff1, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
    // 关闭调试日志
}

// 修复savePoint 在第三次写数据时报9012问题
TEST_F(StYang, PartialDiffErrorTest)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");

    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff.json";

    // 1. start trans
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));

    // 2. create savepoint0
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    // 3. write data
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    const char *deleteStr = "006_graph/yang/edit_with_tree_diff/tree_remove_root.json";
    // 4. remove all data
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(deleteStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    // 5. release savepoint0
    ASSERT_NO_FATAL_FAILURE(LltTransReleaseSavepointAsync(asyncConn, "one"));

    // 6. create savepoint0
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));

    // 7. remove all data
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(deleteStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    // 8. rewrite data
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    // 9. rollback savepoint0
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, "one"));
    // 10. release savepoint0
    ASSERT_NO_FATAL_FAILURE(LltTransReleaseSavepointAsync(asyncConn, "one"));

    // 11. create savepoint0
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    // 12. remove all data
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(deleteStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    // 13. rewrite data --
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);

    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

// 测试场景：字段五元语DML操作
TEST_F(StYang, PartialDiffMultiPropOp1)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");

    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(InitPartialDiffBasicDataAsync(
        asyncConn, asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_baseDataJsonTree));

    // 1. start trans
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));

    string editStr = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_8.json");
    string editStrCfg = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_cfg_8.json");

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), editStrCfg.c_str(), GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> expectDiff = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                 "T0.F0:update(100,1)\n"
                                 "T0.F1:update(1000,3)\n"
                                 "T0.nodeName:remove(T0-00)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);

    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

TEST_F(StYang, PartialDiffMultiPropOp2)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");

    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(InitPartialDiffBasicDataAsync(
        asyncConn, asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_baseDataJsonTree));

    // 1. start trans
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));

    string editStr = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_9.json");
    string editStrCfg = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_cfg_9.json");

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), editStrCfg.c_str(), GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> expectDiff = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                 "T0.F0:update(100,1)\n"
                                 "T0.F1:remove(3)\n"
                                 "T0.nodeName:remove(T0-00)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);

    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

TEST_F(StYang, PartialDiffMultiPropOp3)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");

    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(InitPartialDiffBasicDataAsync(
        asyncConn, asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_baseDataJsonTree));

    // 1. start trans
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));

    string editStr = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_10.json");
    string editStrCfg = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_cfg_10.json");
    string editStrCfg1 = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_cfg_10_1.json");

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), editStrCfg.c_str(), GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), editStrCfg1.c_str(), GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> expectDiff = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                 "T0.F0:remove(1)\n"
                                 "T0.F1:update(1000,3)\n"
                                 "T0.nodeName:update(test,T0-00)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);

    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

TEST_F(StYang, PartialDiffMultiPropOp4)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");

    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(InitPartialDiffBasicDataAsync(
        asyncConn, asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_baseDataJsonTree));

    // 1. start trans
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));

    string editStr = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_11.json");
    string editStrCfg = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_cfg_11.json");

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), editStrCfg.c_str(), GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> expectDiff = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                 "T0.F0:update(100,1)\n"
                                 "T0.F1:remove(3)\n"
                                 "T0.nodeName:remove(T0-00)\n"
                                 "T0.T0::T1:update\n"
                                 "T0::T1.F0:remove(1)\n"
                                 "T0::T1.F1:update(300,8)\n"
                                 "T0::T1.nodeName:update(testT1,T1-00)\n"
                                 "T0.T0::T2:update[(priKey(PID:1,F0:1)),(priKey(PID:1,F0:1))]\n"
                                 "T0::T2.F1:update(400,3)\n"
                                 "T0::T2.nodeName:update(testT2,T2-00)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);

    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

// DTS2023091503942 补充用例：查一次有内容的 diff 后回滚到 DML 操作之前的状态再查 diff，会在打印调试日志的地方 core
TEST_F(StYang, PartialDiffDebugLogWhenNoDiff)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    // 先插入一些数据
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff12.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 开启新事务，创建 savepoint，删除数据，查 diff
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "one"));
    const char *editStr1 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff13.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr1)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> treeExpectDiff = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                     "T0.T0::T2:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
                                     "T0::T2.ID:remove(3)\n"
                                     "T0::T2.F1:remove(3)\n"
                                     "T0::T2.F2:remove(5)\n"
                                     "T0::T2.nodeName:remove(T2-01)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, treeExpectDiff, GMERR_OK));

    // 回滚到删除数据之前，查 diff，预期为空
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, "one"));
    vector<string> expectDiff = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

// DTS2023122113224 补充用例：创建同名savepoint，
// savepoint1 -> insert root -> savepoint2 -> remove root -> savepoint3 -> insert root
// 三个 savepoint 逐个回滚
TEST_F(StYang, PartialDiffMultiSavePointWithSameName)
{
    // 开启追踪调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p gmserver -l 4");

    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;

    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));

    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "sp1"));
    // 插入root
    const char *insertEditStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff25.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "T0",
        (GetFileContext(insertEditStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> expectDiff1 = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                  "T0.F0:create(1)\n"
                                  "T0.F1:create(3)\n"
                                  "T0.nodeName:create(T0-00)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff1, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "sp1"));
    // 删除root
    const char *removeEditStr = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff26.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "T0",
        (GetFileContext(removeEditStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> expectDiff2 = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff2, GMERR_OK));

    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, "sp1"));
    // 再次插入root
    const char *insertEditStr2 = "006_graph/yang/edit_with_tree_diff/tree_save_point_diff27.json";
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "T0",
        (GetFileContext(insertEditStr2)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiff3 = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                  "T0.F0:create(2)\n"
                                  "T0.F1:create(4)\n"
                                  "T0.nodeName:create(T0-01)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff3, GMERR_OK));

    // 第一次回滚，回滚到第二次插入前
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, "sp1"));
    vector<string> expectDiff4 = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff4, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltTransReleaseSavepointAsync(asyncConn, "sp1"));

    // 第二次回滚，回滚到删除前
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, "sp1"));
    vector<string> expectDiff5 = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                  "T0.F0:create(1)\n"
                                  "T0.F1:create(3)\n"
                                  "T0.nodeName:create(T0-00)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff5, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltTransReleaseSavepointAsync(asyncConn, "sp1"));

    // 第三次回滚，回滚到第一次插入前
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, "sp1"));
    vector<string> expectDiff6 = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff6, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltTransReleaseSavepointAsync(asyncConn, "sp1"));

    // 清理环境
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
    // 关闭调试日志
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmlog -p gmserver -l 3");
}

static const char *g_removeNPPropData = R"(
{
    "op": "none",
    "T1": {
        "op": "none",
        "F0": 0,
        "nodeName": "removed"
    }
}
)";
static const char *g_removeNPPropCfg = R"(
{
    "yang_model":1,
    "T1": {
        "F0": {"propOp":"remove"},
        "nodeName": {"propOp":"remove"}
    }
}
)";
// T1 为 NP 节点，删除 T1 所有属性，且 T1 没有默认值，则其变为无意义的空节点，应视为不可见，diff 为 remove
TEST_F(StYang, PartialDiffEmptyNP)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFileTree = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_2.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_2.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFileTree, NULL, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", g_removeNPPropData, g_removeNPPropCfg, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                        "T0.T1:remove\n"
                                        "T1.F0:remove(1)\n"
                                        "T1.nodeName:remove(T1-00)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(asyncStmt, vertexLabelFileTree));
}

static const char *g_simpleRootData = R"(
    {
        "F0": 1,
        "F1": 3,
        "nodeName": "T0-00"
    }
)";

// 测试根节点可见性不同的情况下多次获取 diff
TEST_F(StYang, PartialDiffMultiDiffWithVisibilityChange)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree, g_diffCfgJsonTree););

    // 插入数据
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", g_simpleRootData, g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));

    // 修改根节点数据，获取 diff
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    string editStr = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_9.json");
    string editStrCfg = GetFileContext("006_graph/yang/edit_with_diff/prop_multi_op_cfg_9.json");
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), editStrCfg.c_str(), GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> expectDiff1 = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                  "T0.F0:update(100,1)\n"
                                  "T0.F1:remove(3)\n"
                                  "T0.nodeName:remove(T0-00)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff1, GMERR_OK));

    // 删除根节点数据，获取 diff
    string editStr2 = GetFileContext("006_graph/yang/edit_with_tree_diff/tree_delete_root.json");
    ASSERT_NO_FATAL_FAILURE(
        LltBatchExecuteByJsonAsync(asyncConn, asyncStmt, "T0", editStr2.c_str(), NULL, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> expectDiff2 = {"T0:remove[(NULL),(priKey(ID:1))]\n"
                                  "T0.F0:remove(1)\n"
                                  "T0.F1:remove(3)\n"
                                  "T0.nodeName:remove(T0-00)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff2, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, g_vertexLabelFileTree, g_edgeLabelFileTree);
}

// 多个 list 元素下带有 P 或用户 case，原本有数据，后来因 replace 父节点而被删，每个元素都应该生成正确的 remove 的 diff
TEST_F(StYang, PartialDiffRemoveListChildUserInstance)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    const char *vertexLabelFile = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_11.json";
    const char *edgeLabelFile = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_8.json";
    const char *baseDataJsonTree = "006_graph/yang/edit_with_tree_diff/basicPartialData_30.json";
    const char *editStr = "006_graph/yang/edit_with_tree_diff/tree_default_op_57.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFile, edgeLabelFile, baseDataJsonTree));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", (GetFileContext(editStr)).c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));

    vector<string> expectDiffPartial = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.T1:update[(priKey(PID:1,F0:1)),(priKey(PID:1,F0:1))]\n"
        "T1.F1:update(101,1)\n"
        "T1.T12Node:remove\n"
        "T12Node.F0:remove(1)\n"
        "T1.T13Choice:update\n"
        "T13Choice.T131Case:create\n"
        "T131Case.F0:create(0)\n"
        "T13Choice.T132Case:remove\n"
        "T132Case.F0:remove(1)\n"
        "T0.T1:update[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "T1.F1:update(102,2)\n"
        "T1.T12Node:remove\n"
        "T12Node.F0:remove(2)\n"
        "T1.T13Choice:update\n"
        "T13Choice.T131Case:create\n"
        "T131Case.F0:create(0)\n"
        "T13Choice.T132Case:remove\n"
        "T132Case.F0:remove(2)\n"
        "T0.T1:update[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
        "T1.F1:update(103,3)\n"
        "T1.T12Node:remove\n"
        "T12Node.F0:remove(3)\n"
        "T1.T13Choice:update\n"
        "T13Choice.T131Case:create\n"
        "T131Case.F0:create(0)\n"
        "T13Choice.T132Case:remove\n"
        "T132Case.F0:remove(3)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiffPartial, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFile, edgeLabelFile);
}

// 测试node类型NP节点被删除了属性和一部分孩子节点，但有其他无diff的子节点保证其非空
TEST_F(StYang, PartialDiffNPNotEmptyByNoDiffChild)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    // 插入数据
    const char *vertexLabelFile = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_19.json";
    const char *edgeLabelFile = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_11.json";
    const char *baseDataJson = "006_graph/yang/edit_with_tree_diff/basicPartialData_31.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFile, edgeLabelFile, baseDataJson));

    // 删除 NP1 的子节点 L1，但因为 NP1 的孩子 NP2 底下还有一个默认值，因此 NP1 不会被判定为空，其 diff 状态为 update
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    string editStr = GetFileContext("006_graph/yang/edit_with_tree_diff/tree_default_op_58.json");
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> expectDiff1 = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                  "T0.NP1:update\n"
                                  "NP1.L1:remove[(NULL),(priKey(PID:1,F0:1))]\n"
                                  "L1.ID:remove(1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff1, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFile, edgeLabelFile);
}

// 测试node类型NP节点被删除了属性和一部分孩子节点，其他无diff的子节点不能保证其非空
TEST_F(StYang, PartialDiffNPEmptyByNoDiffChild)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    // 插入数据
    const char *vertexLabelFile = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_20.json";
    const char *edgeLabelFile = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_11.json";
    const char *baseDataJson = "006_graph/yang/edit_with_tree_diff/basicPartialData_31.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFile, edgeLabelFile, baseDataJson));

    // 删除 NP1 的子节点 L1，NP1 的孩子 P1 底下还有一个默认值，但 P1 是 P container，该默认值不可见
    // 因此 NP1 会被判定为空，其 diff 状态为 remove。根节点也被判定为空，状态为 remove。
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    string editStr = GetFileContext("006_graph/yang/edit_with_tree_diff/tree_default_op_58.json");
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> expectDiff1 = {"T0:remove[(NULL),(priKey(ID:1))]\n"
                                  "T0.NP1:remove\n"
                                  "NP1.L1:remove[(NULL),(priKey(PID:1,F0:1))]\n"
                                  "L1.ID:remove(1)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff1, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFile, edgeLabelFile);
}

// 测试NP根节点被删除了属性和一部分孩子节点，但有其他无diff的子节点保证其非空
TEST_F(StYang, PartialDiffNPRootNotEmptyByNoDiffChild)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    // 插入数据
    const char *vertexLabelFile = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_21.json";
    const char *edgeLabelFile = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_12.json";
    const char *baseDataJson = "006_graph/yang/edit_with_tree_diff/basicPartialData_32.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFile, edgeLabelFile, baseDataJson));

    // 删除根节点的子节点 P1，但因为根节点下还有 L1 和默认 LF1，故根节点不会被判定为空，其 diff 状态为 update
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    string editStr = GetFileContext("006_graph/yang/edit_with_tree_diff/tree_merge_delete_diff3.json");
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> expectDiff1 = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                  "T0.P1:remove\n"
                                  "P1.F0:remove(100)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff1, GMERR_OK));

    // 删除根节点的子节点 L1，但因为根节点下还有默认 LF1，故根节点不会被判定为空，其 diff 状态为 update
    string editStr2 = GetFileContext("006_graph/yang/edit_with_tree_diff/tree_merge_delete_diff4.json");
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr2.c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> expectDiff2 = {"T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
                                  "T0.P1:remove\n"
                                  "P1.F0:remove(100)\n"
                                  "T0.L1:remove[(NULL),(priKey(PID:1,F0:1))]\n"
                                  "L1.ID:remove(1)\n"
                                  "T0.L1:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
                                  "L1.ID:remove(2)\n"
                                  "T0.L1:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
                                  "L1.ID:remove(3)\n"};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff2, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFile, edgeLabelFile);
}

// 测试 DTS2024032205083 difftree被回滚复用，多次fetch预期不符bug
TEST_F(StYang, PartialDiffMultiFetchBug)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    // 插入数据
    const char *vertexLabelFile = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_19.json";
    const char *edgeLabelFile = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_11.json";
    const char *baseDataJson = "006_graph/yang/edit_with_tree_diff/basicPartialData_33.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFile, edgeLabelFile, baseDataJson));

    // 删除 NP1 的子节点 L1，但因为 NP1 的孩子 NP2 底下还有一个默认值，因此 NP1 不会被判定为空，其 diff 状态为 update
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, NULL));
    string editStr = GetFileContext("006_graph/yang/edit_with_tree_diff/tree_save_point_diff28.json");
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    vector<string> expectDiff1 = {
        "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
        "T0.NP1:update\n"
        "NP1.L1:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
        "L1.ID:remove(2)\n"
        "NP1.L1:update[(priKey(PID:1,F0:3), preKey(PID:1,F0:1)),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"};
    vector<string> expectDiff1_bak;
    expectDiff1_bak.push_back(expectDiff1[0]);
    vector<string> expectDiff1_bak1;
    expectDiff1_bak1.push_back(expectDiff1[0]);
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff1, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, NULL));

    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, NULL));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff1_bak, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff1_bak1, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFile, edgeLabelFile);
}

// 测试none节点不做操作
TEST_F(StYang, PartialDiffNoneAndFetchDiff)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    // 插入数据
    const char *vertexLabelFile = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_22.json";
    const char *edgeLabelFile = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_13.json";
    // const char *baseDataJson = "006_graph/yang/edit_with_tree_diff/basicPartialData_34.json";

    ASSERT_NO_FATAL_FAILURE(
        LltCreateVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFile, edgeLabelFile, g_diffCfgJsonTree););
    GmcValidateResT res = {.validateRes = true, .failCount = 0};
    ASSERT_NO_FATAL_FAILURE(LltCheckValidateModelAsync(asyncStmt, res));
    // 插入数据
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    string initData = GetFileContext("006_graph/yang/edit_with_tree_diff/basicPartialData_34.json");
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", initData.c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    // ASSERT_NO_FATAL_FAILURE(
    //     InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFile, edgeLabelFile, baseDataJson));

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, NULL));
    string editStr = GetFileContext("006_graph/yang/edit_with_tree_diff/tree_default_op_59.json");
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    res = {.validateRes = true, .failCount = 0};
    ASSERT_NO_FATAL_FAILURE(LltCheckValidateAsync(stmt, GMC_YANG_VALIDATION_WHEN, res, NULL));
    vector<string> expectDiff1 = {};
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(asyncStmt, expectDiff1, GMERR_OK));
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, NULL));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFile, edgeLabelFile);
}

// DTS2024062421959 同一事务里不允许diff先开后关，添加代码拦截
TEST_F(StYang, PartialDiffOnToOff)
{
    GmcConnT *asyncConn = conn;
    GmcStmtT *asyncStmt = stmt;
    // 插入数据
    const char *vertexLabelFile = "006_graph/yang/edit_with_tree_diff/tree_diff_vertexLabelBatch_22.json";
    const char *edgeLabelFile = "006_graph/yang/edit_with_tree_diff/tree_diff_edgeLabelBatch_13.json";
    const char *baseDataJson = "006_graph/yang/edit_with_tree_diff/basicPartialData_34.json";
    ASSERT_NO_FATAL_FAILURE(
        InitPartialDiffBasicDataAsync(asyncConn, asyncStmt, vertexLabelFile, edgeLabelFile, baseDataJson));
    string editStr = GetFileContext("006_graph/yang/edit_with_tree_diff/tree_default_op_59.json");

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(asyncConn));
    ASSERT_NO_FATAL_FAILURE(LltTransCreateSavepointAsync(asyncConn, NULL));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(
        asyncConn, asyncStmt, "T0", editStr.c_str(), g_lltJsonTree, GMC_YANG_DIFF_DELAY_READ_ON));
    ASSERT_NO_FATAL_FAILURE(LltTransRollbackSavepointAsync(asyncConn, NULL));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync<GMERR_INVALID_OPTION>(
        asyncConn, asyncStmt, "T0", editStr.c_str(), g_lltJsonTree, GMC_YANG_DIFF_OFF));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(asyncConn));
    LltDropVertexAndEdgeLabelAsync(asyncStmt, vertexLabelFile, edgeLabelFile);
}
