/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: yang_st_tree_dml.cc
 * Description: yang tree dml, insert, merge, replace, delete, remove, none
 * Author: wuyongzheng
 * Create: 2022-8-23
 */
#include "yang/yang_common_st.h"

constexpr int32_t MAX_STR_LEN = 128;
constexpr int32_t MAX_OP_STAT_SIZE = 10;
typedef struct OpStat {
    const char **labelNames;
    uint64_t *count;
    uint64_t *expectCount;
} OpStatT;

static void CheckOpStat(OpStatT *opStat, uint32_t labelCnt)
{
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(StYang::CreateConn(&syncConn, GMC_CONN_TYPE_SYNC));
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(syncConn, &syncStmt));
    ASSERT_EQ(GMERR_OK, GmcUseNamespace(syncStmt, StYang::nspName));
    ASSERT_EQ(GMERR_OK,
        GmcGetOperStatsCnt(syncStmt, opStat->labelNames, GMC_STATISTICS_TYPE_INSERT, opStat->count, labelCnt));
    for (uint32_t i = 0; i < labelCnt; i++) {
        EXPECT_EQ(opStat->expectCount[i], opStat->count[i]) << "index: " << i << ", name:" << opStat->labelNames[i];
    }
}

static void YangSetNodeProperty(GmcNodeT *node, const char *nodeName)
{
    static int32_t iValue = 5;
    static bool bValue = false;
    static double dValue = 3.141597;
    static float fValue = 3.156;
    GmcPropValueT propValue = {0};
    LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, nodeName, strlen(nodeName));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "int32", GMC_DATATYPE_INT32, &iValue, sizeof(iValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "double", GMC_DATATYPE_DOUBLE, &dValue, sizeof(dValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "boolean", GMC_DATATYPE_BOOL, &bValue, sizeof(bValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "float", GMC_DATATYPE_FLOAT, &fValue, sizeof(fValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
}

static void YangSetListPosKey(GmcStmtT *stmt, const char *key, GmcYangListPositionE pos)
{
    GmcPropValueT posKey;
    posKey.propertyId = 1;
    posKey.propertyName[0] = '\0';
    posKey.type = GMC_DATATYPE_STRING;
    posKey.size = (uint32_t)strlen(key);
    posKey.value = (void *)key;
    GmcPropValueT *refKeys[1] = {&posKey};
    GmcYangListLocatorT listLocator = {
        .position = GMC_YANG_LIST_POSITION_BUTT, .refKeyFields = refKeys, .refKeyFieldsCount = DM_MAX_KEY_PROPE_NUM};
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcYangSetListLocator(stmt, NULL));
    ASSERT_EQ(GMERR_INVALID_VALUE, GmcYangSetListLocator(stmt, &listLocator));
    listLocator.position = pos;
    ASSERT_EQ(GMERR_INVALID_VALUE, GmcYangSetListLocator(stmt, &listLocator));
    posKey.propertyId = 2;
    listLocator.refKeyFieldsCount = 1;
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcYangSetListLocator(stmt, &listLocator));
    posKey.propertyId = 1;
    ASSERT_EQ(GMERR_OK, GmcYangSetListLocator(stmt, &listLocator));
}

void CheckEditExpect(GmcNodeT *node, GmcPropValueT *propValue)
{
    bool isExpect = false;
    ASSERT_EQ(GMERR_OK, GmcNodeExptFieldValue(node, propValue, &isExpect));
    EXPECT_TRUE(isExpect);
}

/* 插入树模型数据 v 后面数字代表插入顶点的个数，n代表node node都只有一条
                   root(v1)
                /     \     \
        company(n)  c1(n)   netconf(v5)
            /      /     \
      employee(v5) c1_1(n) c1_2(n)
        /                  /     \
    project(v5)         c1_2_1(n) c1_2_2(n)
 */
// 插入表模型定义的数据，employee 顶点的父节点是root下的子node company
static void YangInsertPartialTreeData(GmcConnT *conn, GmcOperationTypeE rootOp = GMC_OPERATION_INSERT)
{
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    GmcStmtT *employeeStmt = NULL;
    GmcStmtT *projectStmt = NULL;
    GmcStmtT *netconfStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    // 插入一个root顶点
    const char *rootName = "root";
    char const *nodeName[] = {"c1", "company"};
    char const *nodeName1[] = {"c1_1", "c1_2"};
    char const *nodeName2[] = {"c1_2_1", "c1_2_2"};
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, rootOp));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));

    GmcClientMemCtxStatInfoT memCtxInfo = {0};
    GmcGetClientMemCtxAllocSize(rootStmt->conn, rootStmt, &memCtxInfo);
    uint64_t expected = memCtxInfo.stmtOpSize;

    GmcNodeT *rootNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));
    ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(rootNode, rootName));
    GmcNodeT *node = NULL;
    for (int i = 0; i < int(sizeof(nodeName) / sizeof(nodeName[0])); ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, nodeName[i], GMC_OPERATION_INSERT, &node))
            << "node name:" << nodeName[i] << endl;
        ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(node, nodeName[i]));
        if (strcmp(nodeName[i], "c1") != 0) {
            continue;
        }
        GmcNodeT *c1 = node;
        for (int j = 0; j < int(sizeof(nodeName1) / sizeof(nodeName1[0])); ++j) {
            ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1, nodeName1[j], GMC_OPERATION_INSERT, &node))
                << "node name:" << nodeName1[j] << endl;
            ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(node, nodeName1[j]));
            if (strcmp(nodeName1[j], "c1_2") != 0) {
                continue;
            }
            GmcNodeT *c1_2 = node;
            for (int k = 0; k < int(sizeof(nodeName2) / sizeof(nodeName2[0])); ++k) {
                ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1_2, nodeName2[k], GMC_OPERATION_INSERT, &node))
                    << "node name:" << nodeName[k] << endl;
                ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(node, nodeName2[k]));
            }
        }
    }
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));
    // 测试插入数据时使用的root node
    ASSERT_EQ(GMERR_OK, GmcFreeNode(rootNode));
    GmcGetClientMemCtxAllocSize(rootStmt->conn, rootStmt, &memCtxInfo);
    EXPECT_EQ(expected, memCtxInfo.stmtOpSize);

    char employee[MAX_STR_LEN] = "root::company::employee";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, employee, GMC_OPERATION_INSERT));
    int32_t age = 10;
    GmcNodeT *employeeNode = NULL;
    GmcPropValueT propValue = {0};
    for (int i = 0; i < 5; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
        sprintf_s(employee, MAX_STR_LEN, "root::company::employee_%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, employee, strlen(employee));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        LltInitPropValue(&propValue, "age", GMC_DATATYPE_INT32, &age, sizeof(age));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        ASSERT_NO_FATAL_FAILURE(CheckEditExpect(employeeNode, &propValue));
        age++;
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));
    }

    char project[MAX_STR_LEN] = "root::company::employee::project";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &projectStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(projectStmt, project, GMC_OPERATION_INSERT));
    GmcNodeT *projectNode = NULL;
    for (int i = 0; i < 5; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, employeeStmt, projectStmt));
        sprintf_s(project, MAX_STR_LEN, "root::company::employee::project_%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(projectStmt, &projectNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, project, strlen(project));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(projectNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, projectStmt));
    }

    char netconf[MAX_STR_LEN] = "root::netconf";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &netconfStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(netconfStmt, netconf, GMC_OPERATION_INSERT));
    GmcNodeT *netconfNode = NULL;
    for (int i = 0; i < 5; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, netconfStmt));
        sprintf_s(netconf, MAX_STR_LEN, "root::netconf_%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(netconfStmt, &netconfNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, netconf, strlen(netconf));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(netconfNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        LltInitPropValue(&propValue, "type_uint8", GMC_DATATYPE_STRING, netconf, strlen(netconf));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(netconfNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, netconfStmt));
    }

    ASSERT_NO_FATAL_FAILURE(LltBatchExecute(batch));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(projectStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(employeeStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
}

/* 对company节点执行merge操作 修改节点company属性，并添加5条employee子树记录
                   root(v1)
                /     \     \
        company(n+)  c1(n)   netconf(v5)
            /      /     \
      employee(v5+5) c1_1(n) c1_2(n)
        /                  /     \
    project(v5)         c1_2_1(n) c1_2_2(n)
 */
static void YangMergePartialTreeData(GmcConnT *conn, GmcOperationTypeE rootOp = GMC_OPERATION_MERGE)
{
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    GmcStmtT *employeeStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    const char *rootName = "root";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, rootOp));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));

    int32_t iValue = 6;
    bool bValue = true;
    GmcNodeT *root = NULL;
    GmcPropValueT propValue = {0};
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &root));
    // 对已经存在的节点进行merge动作
    GmcNodeT *node = NULL;
    const char *company = "company";
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, company, GMC_OPERATION_MERGE, &node));
    LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, company, strlen(company));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "int32", GMC_DATATYPE_INT32, &iValue, sizeof(iValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "boolean", GMC_DATATYPE_BOOL, &bValue, sizeof(bValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    ASSERT_NO_FATAL_FAILURE(CheckEditExpect(node, &propValue));

    GmcNodeT *c1 = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, "c1", GMC_OPERATION_MERGE, &c1));
    LltInitPropValue(&propValue, "boolean", GMC_DATATYPE_BOOL, &bValue, sizeof(bValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    GmcNodeT *c1_2 = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1, "c1_2", GMC_OPERATION_MERGE, &c1_2));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1_2, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    // 不存在的节点merge
    GmcNodeT *c3Node = NULL;
    const char *c3 = "c3";
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, "c3", GMC_OPERATION_MERGE, &c3Node));
    LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, c3, strlen(c3));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c3Node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "boolean", GMC_DATATYPE_BOOL, &bValue, sizeof(bValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c3Node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    char employee[MAX_STR_LEN] = "root::company::employee";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, employee, GMC_OPERATION_INSERT));
    int32_t age = 30;
    GmcNodeT *employeeNode = NULL;
    for (int i = 0; i < 5; ++i) {
        ASSERT_NO_FATAL_FAILURE(
            YangSetListPosKey(employeeStmt, "root::company::employee_2", GMC_YANG_LIST_POSITION_AFTER));
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
        sprintf_s(employee, MAX_STR_LEN, "root::company::employee_merge%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, employee, strlen(employee));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        LltInitPropValue(&propValue, "age", GMC_DATATYPE_INT32, &age, sizeof(age));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        age++;
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));
    }

    ASSERT_NO_FATAL_FAILURE(LltBatchExecute(batch));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(employeeStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
}

/* 对company节点执行merge操作 修改节点company属性，并添加5条employee子树记录
                   root(v1)
                /     \     \
        company(n+)  c1(n)   netconf(v5)
            /      /     \
      employee(v5+5) c1_1(n) c1_2(n)
        /                  /     \
    project(v5)         c1_2_1(n) c1_2_2(n)
 */
static void YangMergePartialTreeDataAfterDropEdge(GmcConnT *conn, GmcStmtT *stmt, const char *edgeFile)
{
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    GmcStmtT *employeeStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    const char *rootName = "root";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));

    bool bValue = true;
    GmcNodeT *root = NULL;
    GmcPropValueT propValue = {0};
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &root));
    // 进行merge动作
    GmcNodeT *node = NULL;
    const char *company = "company";
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, company, GMC_OPERATION_MERGE, &node));
    LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, company, strlen(company));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "boolean", GMC_DATATYPE_BOOL, &bValue, sizeof(bValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    char employee[MAX_STR_LEN] = "root::company::employee";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, employee, GMC_OPERATION_INSERT));
    int32_t age = 30;
    GmcNodeT *employeeNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
    LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, employee, strlen(employee));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "age", GMC_DATATYPE_INT32, &age, sizeof(age));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));

    // 删除边
    ASSERT_NO_FATAL_FAILURE(LltDropEdgeLabelAsync(stmt, edgeFile));
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    // 执行DML操作
    ASSERT_NO_FATAL_FAILURE(LltBatchExecute<GMERR_DATA_EXCEPTION>(batch));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn, GMERR_DATA_EXCEPTION));

    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(employeeStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
}

// 对company节点执行none操作
static void YangNonePartialTreeData(GmcConnT *conn, GmcOperationTypeE rootOp = GMC_OPERATION_MERGE)
{
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    GmcStmtT *employeeStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    const char *rootName = "root";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, rootOp));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));

    GmcNodeT *root = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &root));
    GmcNodeT *node = NULL;
    const char *company = "company";
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, company, GMC_OPERATION_NONE, &node));
    GmcPropValueT propValue = {0};

    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    char employee[MAX_STR_LEN] = "root::company::employee";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, employee, GMC_OPERATION_INSERT));
    int32_t age = 40;
    GmcNodeT *employeeNode = NULL;
    for (int i = 0; i < 5; ++i) {
        ASSERT_NO_FATAL_FAILURE(
            YangSetListPosKey(employeeStmt, "root::company::employee_4", GMC_YANG_LIST_POSITION_BEFORE));
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
        sprintf_s(employee, MAX_STR_LEN, "root::company::employee_none%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, employee, strlen(employee));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        LltInitPropValue(&propValue, "age", GMC_DATATYPE_INT32, &age, sizeof(age));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        age++;
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));
    }

    ASSERT_NO_FATAL_FAILURE(LltBatchExecute(batch));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(employeeStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
}

/* 对company节点执行repalce操作 设置节点company属性，写入5条employee子树记录，company节点原有的属性和子图都被替换
                   root(v1)
                /     \     \
        company(n~)  c1(n)   netconf(v5)
            /      /     \
employee(v+5) c1_1(n) c1_2(n)
                       /     \
                    c1_2_1(n) c1_2_2(n)
 */
static void YangReplacePartialTreeData(GmcConnT *conn, GmcOperationTypeE rootOp = GMC_OPERATION_MERGE)
{
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    GmcStmtT *employeeStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    const char *rootName = "root";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, rootOp));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));

    GmcNodeT *root = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &root));
    // 对已经存在的树节点进行repalce操作
    GmcNodeT *node = NULL;
    const char *company = "company";
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, company, GMC_OPERATION_REPLACE_GRAPH, &node));
    int32_t iValue = 1000;
    GmcPropValueT propValue = {0};
    LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, company, strlen(company));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "int32", GMC_DATATYPE_INT32, &iValue, sizeof(iValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    // 对不存在的树节点进行repalce操作
    GmcNodeT *c2Node = NULL;
    const char *c2 = "c2";
    iValue++;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, c2, GMC_OPERATION_REPLACE_GRAPH, &c2Node));
    LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, c2, strlen(c2));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c2Node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "int32", GMC_DATATYPE_INT32, &iValue, sizeof(iValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c2Node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    // 对父节点进行replace操，对子节点也进行replace操作
    GmcNodeT *c1Node = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, "c1", GMC_OPERATION_REPLACE_GRAPH, &c1Node));
    LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, "c1", strlen("c1"));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1Node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    GmcNodeT *c12Node = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1Node, "c1_2", GMC_OPERATION_REPLACE_GRAPH, &c12Node));
    LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, "c1_2", strlen("c1_2"));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c12Node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    char employee[MAX_STR_LEN] = "root::company::employee";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, employee, GMC_OPERATION_REPLACE_GRAPH));
    int32_t age = 40;
    GmcNodeT *employeeNode = NULL;
    for (int i = 0; i < 5; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
        sprintf_s(employee, MAX_STR_LEN, "root::company::employee_replace%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, employee, strlen(employee));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        LltInitPropValue(&propValue, "age", GMC_DATATYPE_INT32, &age, sizeof(age));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        age++;
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));
    }

    ASSERT_NO_FATAL_FAILURE(LltBatchExecute(batch));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(employeeStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
}

/* 对company节点执行delete操作 company节点和子图都被删除
                   root(v1)
                     \     \
                    c1(n)   netconf(v5)
                   /     \
                 c1_1(n) c1_2(n)
                       /     \
                    c1_2_1(n) c1_2_2(n)
 */
static void YangDeletePartialTreeData(GmcConnT *conn, GmcOperationTypeE rootOp = GMC_OPERATION_MERGE)
{
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    const char *rootName = "root";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, rootOp));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));

    GmcNodeT *root = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &root));
    GmcNodeT *node = NULL;
    const char *company = "company";
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, company, GMC_OPERATION_DELETE_GRAPH, &node));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, "c2", GMC_OPERATION_DELETE_GRAPH, &node));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, "c3", GMC_OPERATION_DELETE_GRAPH, &node));
    GmcNodeT *c1 = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, "c1", GMC_OPERATION_MERGE, &c1));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1, "c1_2", GMC_OPERATION_DELETE_GRAPH, &node));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecute(batch));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
}

/* 对company节点和c1_1节点执行remove操作
                   root(v1)
                     \     \
                    c1(n)   netconf(v5)
                        \
                       c1_2(n)
                       /     \
                    c1_2_1(n) c1_2_2(n)
 */
static void YangRemovePartialTreeData(GmcConnT *conn, GmcOperationTypeE rootOp = GMC_OPERATION_MERGE)
{
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    const char *rootName = "root";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, rootOp));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));

    GmcNodeT *root = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &root));
    GmcNodeT *node = NULL;
    const char *company = "company";
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, company, GMC_OPERATION_REMOVE_GRAPH, &node));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, "c1", GMC_OPERATION_MERGE, &node));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(node, "c1_1", GMC_OPERATION_REMOVE_GRAPH, &node));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecute(batch));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
}

static void YangPartialTreeOperationNode(GmcConnT *conn, GmcOperationTypeE nodeOp, Status expectRet)
{
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    // MERGR一个root顶点
    const char *rootName = "root";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));

    GmcNodeT *root = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &root));
    GmcNodeT *node = NULL;
    const char *company = "company";
    // 对node进行操作，根据服务端node是否存在的状态返回结果
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, company, nodeOp, &node));
    int32_t iValue = 6;
    bool bValue = true;
    GmcPropValueT propValue = {0};
    if (nodeOp != GMC_OPERATION_NONE && nodeOp != GMC_OPERATION_DELETE_GRAPH && nodeOp != GMC_OPERATION_REMOVE_GRAPH) {
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, company, strlen(company));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        LltInitPropValue(&propValue, "int32", GMC_DATATYPE_INT32, &iValue, sizeof(iValue));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        LltInitPropValue(&propValue, "boolean", GMC_DATATYPE_BOOL, &bValue, sizeof(bValue));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    }

    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    if (expectRet != GMERR_OK) {
        ASSERT_NO_FATAL_FAILURE(LltBatchExecute<GMERR_SYNTAX_ERROR>(batch));
    } else {
        ASSERT_NO_FATAL_FAILURE(LltBatchExecute(batch));
    }
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn, expectRet));

    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
}

static void YangPartialTreeInsertChild(GmcConnT *conn, Status expectRet)
{
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    GmcStmtT *employeeStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    // MERGR一个root顶点
    const char *rootName = "root";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    char employee[MAX_STR_LEN] = "root::company::employee";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, employee, GMC_OPERATION_INSERT));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, GmcYangBindChild(batch, rootStmt, employeeStmt));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn, expectRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
}

static void YangPartialTreeMultiMergeOperation(GmcConnT *conn, GmcOperationTypeE rootOp = GMC_OPERATION_MERGE)
{
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    GmcStmtT *employeeStmt = NULL;
    GmcStmtT *countryStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    const char *rootName = "root";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, rootOp));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));

    int32_t iValue = 6;
    bool bValue = true;
    GmcNodeT *root = NULL;
    GmcPropValueT propValue = {0};
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &root));
    // 1. 对company节点先进行delete操作，再进行merge操作
    GmcNodeT *node = NULL;
    const char *company = "company";
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, company, GMC_OPERATION_DELETE_GRAPH, &node));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, company, GMC_OPERATION_MERGE, &node));
    LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, company, strlen(company));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "int32", GMC_DATATYPE_INT32, &iValue, sizeof(iValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "boolean", GMC_DATATYPE_BOOL, &bValue, sizeof(bValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    // 2. 对c1以及其他子节点c1_1,c1_2 进行两次merge操作，每次操作个写入一个字段，预期结果每个节点有两个字段
    GmcNodeT *c1 = NULL;
    GmcNodeT *c1_1 = NULL;
    GmcNodeT *c1_2 = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, "c1", GMC_OPERATION_MERGE, &c1));
    LltInitPropValue(&propValue, "boolean", GMC_DATATYPE_BOOL, &bValue, sizeof(bValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    // 对c1_1进行merge操作，写入一个字段数据
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1, "c1_1", GMC_OPERATION_MERGE, &c1_1));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1_1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    // 对c1_2进行merge操作，写入一个字段数据
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1, "c1_2", GMC_OPERATION_MERGE, &c1_2));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1_2, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    // 对c1再进行一次merge操作
    iValue += 100;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, "c1", GMC_OPERATION_MERGE, &c1));
    LltInitPropValue(&propValue, "int32", GMC_DATATYPE_INT32, &iValue, sizeof(iValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    iValue += 100;
    // 对c1_1 和c1_2又进行一次merge操作，再写入一个字段数据
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1, "c1_1", GMC_OPERATION_MERGE, &c1_1));
    LltInitPropValue(&propValue, "int32", GMC_DATATYPE_INT32, &iValue, sizeof(iValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1_1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1, "c1_2", GMC_OPERATION_MERGE, &c1_2));
    LltInitPropValue(&propValue, "int32", GMC_DATATYPE_INT32, &iValue, sizeof(iValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1_2, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    // 3.对原本不存在的进行节点merge
    GmcNodeT *c3Node = NULL;
    const char *c3 = "c3";
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, "c3", GMC_OPERATION_MERGE, &c3Node));
    LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, c3, strlen(c3));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c3Node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "boolean", GMC_DATATYPE_BOOL, &bValue, sizeof(bValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c3Node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    // 4.list节点以及其node插入数据
    char employee[MAX_STR_LEN] = "root::company::employee";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &employeeStmt));
    int32_t age = 30;
    GmcNodeT *employeeNode = NULL;
    for (int i = 0; i < 5; ++i) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, "root::company::employee", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
        sprintf_s(employee, MAX_STR_LEN, "root::company::employee_merge%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, employee, strlen(employee));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        LltInitPropValue(&propValue, "age", GMC_DATATYPE_INT32, &age, sizeof(age));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        age++;
        ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(employeeNode, "c1", GMC_OPERATION_INSERT, &c1));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, "employee_c1", strlen("employee_c1"));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1, "c1_2", GMC_OPERATION_INSERT, &c1_2));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, "employee_c1_2", strlen("employee_c1_2"));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1_2, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));
    }

    // 5.嵌套list节点及其node插入数据
    char country[MAX_STR_LEN] = "root::company::employee::country";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &countryStmt));
    GmcNodeT *countryNode = NULL;
    int32_t addr = 1001;
    ASSERT_EQ(
        GMERR_OK, GmcPrepareStmtByLabelName(countryStmt, "root::company::employee::country", GMC_OPERATION_INSERT));
    for (int i = 0; i < 5; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, employeeStmt, countryStmt));
        sprintf_s(country, MAX_STR_LEN, "root::company::employee::country_insert%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(countryStmt, &countryNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, country, strlen(country));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(countryNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        LltInitPropValue(&propValue, "addr", GMC_DATATYPE_INT32, &addr, sizeof(addr));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(countryNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        addr++;
        ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(countryNode, "c1", GMC_OPERATION_MERGE, &c1));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, "country_c1", strlen("country_c1"));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, countryStmt));
    }

    // 6. 对list最后一个点进行merge操作，追加一个字段数据
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, "root::company::employee", GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(employeeStmt, 1, GMC_DATATYPE_STRING, employee, strlen(employee)));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
    age += 100;
    LltInitPropValue(&propValue, "age", GMC_DATATYPE_INT32, &age, sizeof(age));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "sex", GMC_DATATYPE_STRING, "man", strlen("man"));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecute(batch));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(countryStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(employeeStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
}

static void YangPartialTreeMultiOperation(GmcConnT *conn, GmcOperationTypeE rootOp = GMC_OPERATION_MERGE)
{
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    GmcStmtT *employeeStmt = NULL;
    GmcStmtT *countryStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    const char *rootName = "root";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, rootOp));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));

    int32_t iValue = 10;
    bool bValue = true;
    GmcNodeT *root = NULL;
    GmcPropValueT propValue = {0};
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &root));
    // 1. 对company节点先进行remove操作，再进行insert操作
    GmcNodeT *node = NULL;
    const char *company = "company";
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, company, GMC_OPERATION_REMOVE_GRAPH, &node));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, company, GMC_OPERATION_INSERT, &node));
    LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, company, strlen(company));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "int32", GMC_DATATYPE_INT32, &iValue, sizeof(iValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "boolean", GMC_DATATYPE_BOOL, &bValue, sizeof(bValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    // 2. 对c1进行(replace-merge-delete-remove-create-none)组合操作
    GmcNodeT *c1 = NULL;
    GmcNodeT *c1_1 = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, "c1", GMC_OPERATION_REPLACE_GRAPH, &c1));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, "c1", GMC_OPERATION_MERGE, &c1));

    LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, "root_c1", strlen("root_c1"));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "boolean", GMC_DATATYPE_BOOL, &bValue, sizeof(bValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1, "c1_1", GMC_OPERATION_INSERT, &c1_1));
    LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, "root_c1_1", strlen("root_c1_1"));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1_1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "boolean", GMC_DATATYPE_BOOL, &bValue, sizeof(bValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1_1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, "c1", GMC_OPERATION_DELETE_GRAPH, &c1));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, "c1", GMC_OPERATION_REMOVE_GRAPH, &c1));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, "c1", GMC_OPERATION_INSERT, &c1));
    LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, "root_c1", strlen("root_c1"));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "int32", GMC_DATATYPE_INT32, &iValue, sizeof(iValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, "c1", GMC_OPERATION_NONE, &c1));

    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    // 3.嵌套list节点及其node节点插入数据
    char employee[MAX_STR_LEN] = "root::company::employee";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, employee, GMC_OPERATION_REPLACE_GRAPH));
    int32_t age = 30;
    GmcNodeT *employeeNode = NULL;
    GmcNodeT *c1_2 = NULL;

    char country[MAX_STR_LEN] = "root::company::employee::country";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &countryStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(countryStmt, country, GMC_OPERATION_REPLACE_GRAPH));
    GmcNodeT *countryNode = NULL;
    int32_t addr = 1001;

    for (int i = 0; i < 2; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
        sprintf_s(employee, MAX_STR_LEN, "root::company::employee_multi%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, employee, strlen(employee));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        LltInitPropValue(&propValue, "age", GMC_DATATYPE_INT32, &age, sizeof(age));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        age++;
        ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(employeeNode, "c1", GMC_OPERATION_MERGE, &c1));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, "employee_c1", strlen("employee_c1"));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1, "c1_2", GMC_OPERATION_MERGE, &c1_2));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, "employee_c1_2", strlen("employee_c1_2"));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1_2, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));
        for (int j = 0; j < 2; ++j) {
            ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, employeeStmt, countryStmt));
            sprintf_s(country, MAX_STR_LEN, "root::company::employee::country_multi%d%d", i, j);
            ASSERT_EQ(GMERR_OK, GmcGetRootNode(countryStmt, &countryNode));
            LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, country, strlen(country));
            ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(countryNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
            LltInitPropValue(&propValue, "addr", GMC_DATATYPE_INT32, &addr, sizeof(addr));
            ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(countryNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
            addr++;
            ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(countryNode, "c1", GMC_OPERATION_MERGE, &c1));
            LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, "country_c1", strlen("country_c1"));
            ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
            ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, countryStmt));
        }
    }

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, "root::company::employee", GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(employeeStmt, "PK"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(employeeStmt, 1, GMC_DATATYPE_STRING, employee, strlen(employee)));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(employeeNode, "c1", GMC_OPERATION_DELETE_GRAPH, &c1));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecute(batch));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(countryStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(employeeStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
}

void PrintIndexBuff(FixBufferT *buff)
{
    uint32_t indexLen;

    // get index buff
    Status ret = SecureFixBufGetUint32(buff, &indexLen);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get objNum when PrintIndexBuff.");
    }
    void *indexBuff = SecureFixBufGetData(buff, indexLen);
    if (indexBuff == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "get indexBuff unsuccessful when PrintIndexBuff.");
    }
    (void)printf("INDEX: indexLen: %d, ", indexLen);
}

void PrintVertexBuff(FixBufferT *buff)
{
    uint32_t vertexLen;

    // get Vertex buff
    Status ret = SecureFixBufGetUint32(buff, &vertexLen);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get vertexLen when PrintVertexBuff.");
    }
    void *vertex = SecureFixBufGetData(buff, vertexLen + sizeof(uint32_t));
    if (vertex == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "get vertex unsuccessful when PrintVertexBuff.");
    }
    (void)printf("VERTEX_BODY: vertexLen: %d, ", vertexLen);
}

void PrintYangBuff(FixBufferT *buff)
{
    // get Yang buff
    uint32_t yangTmpId;
    uint32_t isListFlag;
    Status ret = SecureFixBufGetUint32(buff, &yangTmpId);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get yangTmpId when PrintYangBuff.");
    }
    ret = SecureFixBufGetUint32(buff, &isListFlag);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get isListFlag when PrintYangBuff.");
    }
    (void)printf("YANG_BODY: YangTmpId: %d, isList: %d\n", yangTmpId, isListFlag);
    if (isListFlag == 1) {
        uint32_t pos;
        TextT text;
        ret = SecureFixBufGetUint32(buff, &pos);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "get pos when PrintYangBuff.");
        }
        ret = SecureFixBufGetObjectNullable(buff, &text);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "get text when PrintYangBuff.");
        }
    }
}

void PrintBatchSendBuf(GmcBatchT *batch)
{
    FixBufferT *buff = &batch->batchSendBuf;
    uint32_t origPos = buff->seekPos;
    FixBufSeek(buff, 0);
    (void)printf("-----------------------------START-----------------------------\n");
    RpcSeekFirstOpMsg(buff);
    BatchHeaderT *batchHeader = (BatchHeaderT *)SecureFixBufGetData(buff, sizeof(BatchHeaderT));
    if (batchHeader == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "get batchHeader unsuccessful when PrintMsgBuff.");
        return;
    }
    (void)printf("BATCH_HEADER: totalNum %d, totalOpNum %d, batchOrder %d\n", batchHeader->totalNum,
        batchHeader->totalOpNum, batchHeader->batchOrder);

    uint32_t totalNum = batchHeader->totalNum;
    for (uint32_t i = 0; i < totalNum; i++) {
        BatchCmdHeaderT cmdHeader;
        Status ret = GetBatchHeader(buff, &cmdHeader);
        if (ret != GMERR_OK) {
            return;
        }

        if (cmdHeader.opCode == MSG_OP_RPC_NONE_VERTEX) {
            (void)printf("BATCH_CMD_HEADER: opCode %d, vertexLabelId %d, version %d, indexId:%d, objNum %d\n",
                cmdHeader.opCode, cmdHeader.vertexLabelId, cmdHeader.version, cmdHeader.indexId, cmdHeader.objNum);
        } else {
            (void)printf("BATCH_CMD_HEADER: opCode %d, vertexLabelId %d, version %d, objNum %d\n", cmdHeader.opCode,
                cmdHeader.vertexLabelId, cmdHeader.version, cmdHeader.objNum);
        }
        for (uint32_t j = 0; j < cmdHeader.objNum; j++) {
            if (cmdHeader.opCode == MSG_OP_RPC_NONE_VERTEX) {
                PrintIndexBuff(buff);
            }
            PrintVertexBuff(buff);
            PrintYangBuff(buff);
        }
    }
    (void)printf("-----------------------------END-----------------------------\n");
    buff->seekPos = origPos;
}

static void PrintDebugInfo(GmcBatchT *batch, bool doPrint)
{
    if (doPrint) {
        PrintBatchSendBuf(batch);
    }
    return;
}

/* 插入树模型数据 v 后面数字代表插入顶点的个数，n代表node node都只有一条
                   root(v1)
                /     \     \
        company(n)  c1(n)   netconf(v5)
            /      /     \
      employee(v5) c1_1(n) c1_2(n)
        /                  /     \
    project(v5)         c1_2_1(n) c1_2_2(n)
 */
// 插入表模型定义的数据，employee 顶点的父节点是root下的子node company
// 对插入的批量数据调用GmcBatchMergeOps对数据做合并
static void YangInsertAndSortPartialTreeData(
    GmcConnT *conn, GmcOperationTypeE rootOp = GMC_OPERATION_INSERT, bool doPrint = false)
{
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    GmcStmtT *employeeStmt = NULL;
    GmcStmtT *projectStmt = NULL;
    GmcStmtT *netconfStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    // 插入一个root顶点
    const char *rootName = "root";
    char const *nodeName[] = {"c1", "company"};
    char const *nodeName1[] = {"c1_1", "c1_2"};
    char const *nodeName2[] = {"c1_2_1", "c1_2_2"};
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, rootOp));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));

    GmcNodeT *rootNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));
    ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(rootNode, rootName));
    GmcNodeT *node = NULL;
    for (int i = 0; i < int(sizeof(nodeName) / sizeof(nodeName[0])); ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, nodeName[i], GMC_OPERATION_INSERT, &node))
            << "node name:" << nodeName[i] << endl;
        ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(node, nodeName[i]));
        if (strcmp(nodeName[i], "c1") != 0) {
            continue;
        }
        GmcNodeT *c1 = node;
        for (int j = 0; j < int(sizeof(nodeName1) / sizeof(nodeName1[0])); ++j) {
            ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1, nodeName1[j], GMC_OPERATION_INSERT, &node))
                << "node name:" << nodeName1[j] << endl;
            ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(node, nodeName1[j]));
            if (strcmp(nodeName1[j], "c1_2") != 0) {
                continue;
            }
            GmcNodeT *c1_2 = node;
            for (int k = 0; k < int(sizeof(nodeName2) / sizeof(nodeName2[0])); ++k) {
                ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1_2, nodeName2[k], GMC_OPERATION_INSERT, &node))
                    << "node name:" << nodeName[k] << endl;
                ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(node, nodeName2[k]));
            }
        }
    }
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    char employee[MAX_STR_LEN] = "root::company::employee";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, employee, GMC_OPERATION_INSERT));
    int32_t age = 10;
    GmcNodeT *employeeNode = NULL;
    GmcPropValueT propValue = {0};
    for (int i = 0; i < 5; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
        sprintf_s(employee, MAX_STR_LEN, "root::company::employee_%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, employee, strlen(employee));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        LltInitPropValue(&propValue, "age", GMC_DATATYPE_INT32, &age, sizeof(age));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        age++;
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));
    }

    char project[MAX_STR_LEN] = "root::company::employee::project";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &projectStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(projectStmt, project, GMC_OPERATION_INSERT));
    GmcNodeT *projectNode = NULL;
    for (int i = 0; i < 5; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, employeeStmt, projectStmt));
        sprintf_s(project, MAX_STR_LEN, "root::company::employee::project_%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(projectStmt, &projectNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, project, strlen(project));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(projectNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, projectStmt));
    }

    PrintDebugInfo(batch, doPrint);
    if (rootOp == GMC_OPERATION_INSERT || rootOp == GMC_OPERATION_NONE) {
        ASSERT_EQ(GMERR_OK, GmcBatchMergeOps(batch));
    } else {
        ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcBatchMergeOps(batch));
    }
    PrintDebugInfo(batch, doPrint);

    char netconf[MAX_STR_LEN] = "root::netconf";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &netconfStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(netconfStmt, netconf, GMC_OPERATION_INSERT));
    GmcNodeT *netconfNode = NULL;
    for (int i = 0; i < 5; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, netconfStmt));
        sprintf_s(netconf, MAX_STR_LEN, "root::netconf_%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(netconfStmt, &netconfNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, netconf, strlen(netconf));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(netconfNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        LltInitPropValue(&propValue, "type_uint8", GMC_DATATYPE_STRING, netconf, strlen(netconf));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(netconfNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, netconfStmt));
    }

    PrintDebugInfo(batch, doPrint);
    if (rootOp == GMC_OPERATION_INSERT || rootOp == GMC_OPERATION_NONE) {
        ASSERT_EQ(GMERR_OK, GmcBatchMergeOps(batch));
    } else {
        ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcBatchMergeOps(batch));
    }
    PrintDebugInfo(batch, doPrint);

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(projectStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(employeeStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
}

// 对Batch批量数据做GMC_OPERATION_NONE再做GMC_OPERATION_INSERT，在PrepareStmt前、GmcBatchAddDML前、
// GmcBatchAddDML后分别调用GmcBatchMergeOps对batch数据排序
static void YangNoneAndSortPartialTreeData(
    GmcConnT *conn, GmcOperationTypeE rootOp = GMC_OPERATION_NONE, bool doPrint = false)
{
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    GmcStmtT *employeeStmt = NULL;
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcBatchMergeOps(batch));
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    const char *rootName = "root";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, rootOp));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcBatchMergeOps(batch));

    GmcNodeT *root = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &root));
    GmcNodeT *node = NULL;
    const char *company = "company";
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(root, company, GMC_OPERATION_NONE, &node));
    GmcPropValueT propValue = {0};

    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    PrintDebugInfo(batch, doPrint);
    ASSERT_EQ(GMERR_OK, GmcBatchMergeOps(batch));
    PrintDebugInfo(batch, doPrint);

    char employee[MAX_STR_LEN] = "root::company::employee";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, employee, GMC_OPERATION_INSERT));
    ASSERT_NO_FATAL_FAILURE(
        YangSetListPosKey(employeeStmt, "root::company::employee_4", GMC_YANG_LIST_POSITION_BEFORE));
    int32_t age = 40;
    GmcNodeT *employeeNode = NULL;
    for (int i = 0; i < 5; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
        sprintf_s(employee, MAX_STR_LEN, "root::company::employee_none%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, employee, strlen(employee));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        LltInitPropValue(&propValue, "age", GMC_DATATYPE_INT32, &age, sizeof(age));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        age++;
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));
    }

    PrintDebugInfo(batch, doPrint);
    ASSERT_EQ(GMERR_OK, GmcBatchMergeOps(batch));
    PrintDebugInfo(batch, doPrint);

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(employeeStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
}

// 对Batch批量数据做GMC_OPERATION_MERGE、GMC_OPERATION_INSERT、GMC_OPERATION_REPLACE_GRAPH、GMC_OPERATION_NONE等操作
// 并调用GmcBatchMergeOps对批量数据合并
static void YangMultiAndSortPartialTreeData(
    GmcConnT *conn, GmcOperationTypeE rootOp = GMC_OPERATION_INSERT, bool doPrint = false)
{
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    GmcStmtT *employeeStmt = NULL;
    GmcStmtT *projectStmt = NULL;
    GmcStmtT *netconfStmt = NULL;
    GmcStmtT *countryStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    // 插入一个root顶点
    const char *rootName = "root";
    char const *nodeName[] = {"c1", "company"};
    char const *nodeName1[] = {"c1_1", "c1_2"};
    char const *nodeName2[] = {"c1_2_1", "c1_2_2"};
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, rootOp));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));

    GmcNodeT *rootNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));
    ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(rootNode, rootName));
    GmcNodeT *node = NULL;
    for (int i = 0; i < int(sizeof(nodeName) / sizeof(nodeName[0])); ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, nodeName[i], GMC_OPERATION_INSERT, &node))
            << "node name:" << nodeName[i] << endl;
        ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(node, nodeName[i]));
        if (strcmp(nodeName[i], "c1") != 0) {
            continue;
        }
        GmcNodeT *c1 = node;
        for (int j = 0; j < int(sizeof(nodeName1) / sizeof(nodeName1[0])); ++j) {
            ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1, nodeName1[j], GMC_OPERATION_INSERT, &node))
                << "node name:" << nodeName1[j] << endl;
            ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(node, nodeName1[j]));
            if (strcmp(nodeName1[j], "c1_2") != 0) {
                continue;
            }
            GmcNodeT *c1_2 = node;
            for (int k = 0; k < int(sizeof(nodeName2) / sizeof(nodeName2[0])); ++k) {
                ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1_2, nodeName2[k], GMC_OPERATION_INSERT, &node))
                    << "node name:" << nodeName[k] << endl;
                ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(node, nodeName2[k]));
            }
        }
    }
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    char employee[MAX_STR_LEN] = "root::company::employee";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, employee, GMC_OPERATION_INSERT));
    int32_t age = 10;
    GmcNodeT *employeeNode = NULL;
    GmcPropValueT propValue = {0};
    for (int i = 0; i < 5; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
        sprintf_s(employee, MAX_STR_LEN, "root::company::employee_%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, employee, strlen(employee));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        LltInitPropValue(&propValue, "age", GMC_DATATYPE_INT32, &age, sizeof(age));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        age++;
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));
    }

    char project[MAX_STR_LEN] = "root::company::employee::project";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &projectStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(projectStmt, project, GMC_OPERATION_INSERT));
    GmcNodeT *projectNode = NULL;
    for (int i = 0; i < 5; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, employeeStmt, projectStmt));
        sprintf_s(project, MAX_STR_LEN, "root::company::employee::project_%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(projectStmt, &projectNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, project, strlen(project));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(projectNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, projectStmt));
    }

    PrintDebugInfo(batch, doPrint);
    if (rootOp == GMC_OPERATION_INSERT || rootOp == GMC_OPERATION_NONE) {
        ASSERT_EQ(GMERR_OK, GmcBatchMergeOps(batch));
    } else {
        ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcBatchMergeOps(batch));
    }
    PrintDebugInfo(batch, doPrint);

    char netconf[MAX_STR_LEN] = "root::netconf";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &netconfStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(netconfStmt, netconf, GMC_OPERATION_INSERT));
    GmcNodeT *netconfNode = NULL;
    for (int i = 0; i < 5; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, netconfStmt));
        sprintf_s(netconf, MAX_STR_LEN, "root::netconf_%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(netconfStmt, &netconfNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, netconf, strlen(netconf));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(netconfNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        LltInitPropValue(&propValue, "type_uint8", GMC_DATATYPE_STRING, netconf, strlen(netconf));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(netconfNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, netconfStmt));
    }

    PrintDebugInfo(batch, doPrint);
    if (rootOp == GMC_OPERATION_INSERT || rootOp == GMC_OPERATION_NONE) {
        ASSERT_EQ(GMERR_OK, GmcBatchMergeOps(batch));
    } else {
        ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcBatchMergeOps(batch));
    }
    PrintDebugInfo(batch, doPrint);

    GmcNodeT *c1 = NULL;
    GmcNodeT *c1_2 = NULL;
    char country[MAX_STR_LEN] = "root::company::employee::country";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &countryStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(countryStmt, country, GMC_OPERATION_REPLACE_GRAPH));
    GmcNodeT *countryNode = NULL;
    int32_t addr = 1001;

    for (int i = 0; i < 2; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
        sprintf_s(employee, MAX_STR_LEN, "root::company::employee_multi%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, employee, strlen(employee));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        LltInitPropValue(&propValue, "age", GMC_DATATYPE_INT32, &age, sizeof(age));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        age++;
        ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(employeeNode, "c1", GMC_OPERATION_MERGE, &c1));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, "employee_c1", strlen("employee_c1"));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1, "c1_2", GMC_OPERATION_MERGE, &c1_2));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, "employee_c1_2", strlen("employee_c1_2"));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1_2, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));
        for (int j = 0; j < 2; ++j) {
            ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, employeeStmt, countryStmt));
            sprintf_s(country, MAX_STR_LEN, "root::company::employee::country_multi%d%d", i, j);
            ASSERT_EQ(GMERR_OK, GmcGetRootNode(countryStmt, &countryNode));
            LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, country, strlen(country));
            ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(countryNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
            LltInitPropValue(&propValue, "addr", GMC_DATATYPE_INT32, &addr, sizeof(addr));
            ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(countryNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
            addr++;
            ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(countryNode, "c1", GMC_OPERATION_MERGE, &c1));
            LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, "country_c1", strlen("country_c1"));
            ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
            ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, countryStmt));
        }
    }

    PrintDebugInfo(batch, doPrint);
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcBatchMergeOps(batch));
    PrintDebugInfo(batch, doPrint);

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, "root::company::employee", GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(employeeStmt, "PK"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(employeeStmt, 1, GMC_DATATYPE_STRING, employee, strlen(employee)));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(employeeNode, "c1", GMC_OPERATION_DELETE_GRAPH, &c1));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(projectStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(employeeStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
}

// 对Batch批量数据做超过8次GMC_OPERATION_INSERT、GMC_OPERATION_NONE等操作
// 并调用GmcBatchMergeOps对批量数据合并
static void YangMultiTrigExpandAndSortPartialTreeData(
    GmcConnT *conn, GmcOperationTypeE rootOp = GMC_OPERATION_INSERT, bool doPrint = false)
{
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    GmcStmtT *employeeStmt = NULL;
    GmcStmtT *projectStmt = NULL;
    GmcStmtT *netconfStmt = NULL;
    GmcStmtT *countryStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    // 插入一个root顶点
    const char *rootName = "root";
    char const *nodeName[] = {"c1", "company"};
    char const *nodeName1[] = {"c1_1", "c1_2"};
    char const *nodeName2[] = {"c1_2_1", "c1_2_2"};
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, rootOp));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));

    GmcNodeT *rootNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));
    ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(rootNode, rootName));
    GmcNodeT *node = NULL;
    for (int i = 0; i < int(sizeof(nodeName) / sizeof(nodeName[0])); ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, nodeName[i], GMC_OPERATION_INSERT, &node))
            << "node name:" << nodeName[i] << endl;
        ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(node, nodeName[i]));
        if (strcmp(nodeName[i], "c1") != 0) {
            continue;
        }
        GmcNodeT *c1 = node;
        for (int j = 0; j < int(sizeof(nodeName1) / sizeof(nodeName1[0])); ++j) {
            ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1, nodeName1[j], GMC_OPERATION_INSERT, &node))
                << "node name:" << nodeName1[j] << endl;
            ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(node, nodeName1[j]));
            if (strcmp(nodeName1[j], "c1_2") != 0) {
                continue;
            }
            GmcNodeT *c1_2 = node;
            for (int k = 0; k < int(sizeof(nodeName2) / sizeof(nodeName2[0])); ++k) {
                ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1_2, nodeName2[k], GMC_OPERATION_INSERT, &node))
                    << "node name:" << nodeName[k] << endl;
                ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(node, nodeName2[k]));
            }
        }
    }
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    char employee[MAX_STR_LEN] = "root::company::employee";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, employee, GMC_OPERATION_INSERT));
    int32_t age = 10;
    GmcNodeT *employeeNode = NULL;
    GmcPropValueT propValue = {0};
    for (int i = 0; i < 5; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
        sprintf_s(employee, MAX_STR_LEN, "root::company::employee_%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, employee, strlen(employee));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        LltInitPropValue(&propValue, "age", GMC_DATATYPE_INT32, &age, sizeof(age));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        age++;
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));
    }

    char project[MAX_STR_LEN] = "root::company::employee::project";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &projectStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(projectStmt, project, GMC_OPERATION_INSERT));
    GmcNodeT *projectNode = NULL;
    for (int i = 0; i < 5; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, employeeStmt, projectStmt));
        sprintf_s(project, MAX_STR_LEN, "root::company::employee::project_%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(projectStmt, &projectNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, project, strlen(project));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(projectNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, projectStmt));
    }

    PrintDebugInfo(batch, doPrint);
    if (rootOp == GMC_OPERATION_INSERT || rootOp == GMC_OPERATION_NONE) {
        ASSERT_EQ(GMERR_OK, GmcBatchMergeOps(batch));
    } else {
        ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcBatchMergeOps(batch));
    }
    PrintDebugInfo(batch, doPrint);

    char netconf[MAX_STR_LEN] = "root::netconf";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &netconfStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(netconfStmt, netconf, GMC_OPERATION_INSERT));
    GmcNodeT *netconfNode = NULL;
    for (int i = 0; i < 5; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, netconfStmt));
        sprintf_s(netconf, MAX_STR_LEN, "root::netconf_%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(netconfStmt, &netconfNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, netconf, strlen(netconf));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(netconfNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        LltInitPropValue(&propValue, "type_uint8", GMC_DATATYPE_STRING, netconf, strlen(netconf));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(netconfNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, netconfStmt));
    }

    PrintDebugInfo(batch, doPrint);
    if (rootOp == GMC_OPERATION_INSERT || rootOp == GMC_OPERATION_NONE) {
        ASSERT_EQ(GMERR_OK, GmcBatchMergeOps(batch));
    } else {
        ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcBatchMergeOps(batch));
    }
    PrintDebugInfo(batch, doPrint);

    GmcNodeT *c1 = NULL;
    GmcNodeT *c1_2 = NULL;
    char country[MAX_STR_LEN] = "root::company::employee::country";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &countryStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(countryStmt, country, GMC_OPERATION_INSERT));
    GmcNodeT *countryNode = NULL;
    int32_t addr = 1001;

    for (int i = 0; i < 2; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
        sprintf_s(employee, MAX_STR_LEN, "root::company::employee_multi%d", i);
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, employee, strlen(employee));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        LltInitPropValue(&propValue, "age", GMC_DATATYPE_INT32, &age, sizeof(age));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        age++;
        ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(employeeNode, "c1", GMC_OPERATION_MERGE, &c1));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, "employee_c1", strlen("employee_c1"));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1, "c1_2", GMC_OPERATION_MERGE, &c1_2));
        LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, "employee_c1_2", strlen("employee_c1_2"));
        ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1_2, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));
        for (int j = 0; j < 2; ++j) {
            ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, employeeStmt, countryStmt));
            sprintf_s(country, MAX_STR_LEN, "root::company::employee::country_multi%d%d", i, j);
            ASSERT_EQ(GMERR_OK, GmcGetRootNode(countryStmt, &countryNode));
            LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, country, strlen(country));
            ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(countryNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
            LltInitPropValue(&propValue, "addr", GMC_DATATYPE_INT32, &addr, sizeof(addr));
            ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(countryNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
            addr++;
            ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(countryNode, "c1", GMC_OPERATION_MERGE, &c1));
            LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, "country_c1", strlen("country_c1"));
            ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
            ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, countryStmt));
        }
    }

    PrintDebugInfo(batch, doPrint);
    ASSERT_EQ(GMERR_OK, GmcBatchMergeOps(batch));
    PrintDebugInfo(batch, doPrint);

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, GMC_OPERATION_NONE));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, "company", GMC_OPERATION_NONE, &node));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, countryStmt));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, "root::company::employee", GMC_OPERATION_NONE));
    ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(employeeStmt, "PK"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(employeeStmt, 1, GMC_DATATYPE_STRING, employee, strlen(employee)));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(employeeNode, "c1", GMC_OPERATION_DELETE_GRAPH, &c1));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));

    PrintDebugInfo(batch, doPrint);
    ASSERT_EQ(GMERR_OK, GmcBatchMergeOps(batch));
    PrintDebugInfo(batch, doPrint);

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, "root::company::employee", GMC_OPERATION_NONE));
    ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(employeeStmt, "PK"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(employeeStmt, 1, GMC_DATATYPE_STRING, employee, strlen(employee)));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(employeeNode, "c1", GMC_OPERATION_DELETE_GRAPH, &c1));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));

    PrintDebugInfo(batch, doPrint);
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcBatchMergeOps(batch));
    PrintDebugInfo(batch, doPrint);

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, "root::company::employee", GMC_OPERATION_NONE));
    ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(employeeStmt, "PK"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(employeeStmt, 1, GMC_DATATYPE_STRING, employee, strlen(employee)));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(employeeNode, "c1", GMC_OPERATION_DELETE_GRAPH, &c1));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));

    PrintDebugInfo(batch, doPrint);
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcBatchMergeOps(batch));
    PrintDebugInfo(batch, doPrint);

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, "root::company::employee", GMC_OPERATION_NONE));
    ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(employeeStmt, "PK"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(employeeStmt, 1, GMC_DATATYPE_STRING, employee, strlen(employee)));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(employeeNode, "c1", GMC_OPERATION_DELETE_GRAPH, &c1));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));

    PrintDebugInfo(batch, doPrint);
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcBatchMergeOps(batch));
    PrintDebugInfo(batch, doPrint);

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, "root::company::employee", GMC_OPERATION_NONE));
    ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(employeeStmt, "PK"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(employeeStmt, 1, GMC_DATATYPE_STRING, employee, strlen(employee)));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(employeeNode, "c1", GMC_OPERATION_DELETE_GRAPH, &c1));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, employeeStmt));

    PrintDebugInfo(batch, doPrint);
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcBatchMergeOps(batch));
    PrintDebugInfo(batch, doPrint);

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(projectStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(employeeStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
}

static void YangInsertLargeKeyData(GmcConnT *conn, GmcOperationTypeE rootOp = GMC_OPERATION_INSERT)
{
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    GmcStmtT *employeeStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    // 插入一个root顶点
    const char *rootName = "root";
    char const *nodeName[] = {"c1", "company"};
    char const *nodeName1[] = {"c1_1", "c1_2"};
    char const *nodeName2[] = {"c1_2_1", "c1_2_2"};
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, rootOp));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));

    GmcNodeT *rootNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));
    ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(rootNode, rootName));
    GmcNodeT *node = NULL;
    for (int i = 0; i < int(sizeof(nodeName) / sizeof(nodeName[0])); ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, nodeName[i], GMC_OPERATION_INSERT, &node))
            << "node name:" << nodeName[i] << endl;
        ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(node, nodeName[i]));
        if (strcmp(nodeName[i], "c1") != 0) {
            continue;
        }
        GmcNodeT *c1 = node;
        for (int j = 0; j < int(sizeof(nodeName1) / sizeof(nodeName1[0])); ++j) {
            ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1, nodeName1[j], GMC_OPERATION_INSERT, &node))
                << "node name:" << nodeName1[j] << endl;
            ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(node, nodeName1[j]));
            if (strcmp(nodeName1[j], "c1_2") != 0) {
                continue;
            }
            GmcNodeT *c1_2 = node;
            for (int k = 0; k < int(sizeof(nodeName2) / sizeof(nodeName2[0])); ++k) {
                ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1_2, nodeName2[k], GMC_OPERATION_INSERT, &node))
                    << "node name:" << nodeName[k] << endl;
                ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(node, nodeName2[k]));
            }
        }
    }
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    char employee[MAX_STR_LEN] = "root::company::employee";
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, employee, GMC_OPERATION_INSERT));
    int32_t age = 10;
    GmcNodeT *employeeNode = NULL;
    GmcPropValueT propValue = {0};
    ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(employeeStmt, &employeeNode));
    // 插入一个employeeName长度超过keybuf限制
    string employeeName = GetFileContext("006_graph/yang/read_by_dml/large_string");
    LltInitPropValue(&propValue, "name", GMC_DATATYPE_STRING, employeeName.c_str(), strlen(employeeName.c_str()));

    // name 是主键字段，key字段设置长度超过DM_MAX_INDEX_KEY_SIZE，报错
    ASSERT_EQ(
        GMERR_DATA_EXCEPTION, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "age", GMC_DATATYPE_INT32, &age, sizeof(age));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(employeeNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    age++;
    ASSERT_EQ(GMERR_DATA_EXCEPTION, GmcBatchAddDML(batch, employeeStmt));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecute(batch));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(employeeStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
}

static void CheckReplyJson(GmcStmtT *stmt, GmcSubtreeFilterT &filters, const char *replyFileName)
{
    string replyFileJson = GetFileContext(replyFileName);
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeJsonAsync(stmt, filters, replyFileJson.c_str()));
}

const char *g_cfgJson = R"({"auto_increment": 1, "isFastReadUncommitted": 0, "yang_model": 1})";
// 测试场景：异步执行部分打散树模型的六原语操作
TEST_F(StYangTreeDml, PartialTreeDmlAsync)
{
    // 1.建表
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabelBatch.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edgeLabelBatch.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));

    // 1.1.设置subtree查询条件
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "root";
    filter.subtree.json = "{}";
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };

    // 2.树节点insert操作
    ASSERT_NO_FATAL_FAILURE(YangInsertPartialTreeData(conn, GMC_OPERATION_MERGE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/insert_reply.json"));

    // 2.1校验操作统计信息
    const char *labelNames[MAX_OP_STAT_SIZE] = {
        "root", "root::company::employee", "root::company::employee::project", "root::netconf"};
    uint64_t count[MAX_OP_STAT_SIZE] = {0};
    uint64_t expectCount[MAX_OP_STAT_SIZE] = {0, 5, 5, 5};
    OpStatT opStat = {labelNames, count, expectCount};
    CheckOpStat(&opStat, 4);

    // 3.树节点merge操作
    ASSERT_NO_FATAL_FAILURE(YangMergePartialTreeData(conn, GMC_OPERATION_MERGE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/merge_reply.json"));

    // 4.树节点none操作
    ASSERT_NO_FATAL_FAILURE(YangNonePartialTreeData(conn, GMC_OPERATION_MERGE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/none_reply.json"));

    // 5.树节点replace操作
    ASSERT_NO_FATAL_FAILURE(YangReplacePartialTreeData(conn, GMC_OPERATION_MERGE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/replace_reply.json"));

    // 6.树节点delete操作
    ASSERT_NO_FATAL_FAILURE(YangDeletePartialTreeData(conn, GMC_OPERATION_MERGE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/delete_reply.json"));

    // 7.树节点remove操作
    ASSERT_NO_FATAL_FAILURE(YangRemovePartialTreeData(conn, GMC_OPERATION_MERGE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/remove_reply.json"));

    // 删除所有数据
    ASSERT_NO_FATAL_FAILURE(YangRemovePartialTreeData(conn, GMC_OPERATION_MERGE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/remove_reply.json"));

    // 清理
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
}

// 测试场景：在删边后执行DML操作报错
TEST_F(StYangTreeDml, PartialTreeDmlDDLErrorAsync)
{
    // 1.建表
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabelBatch.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edgeLabelBatch.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));

    // 2.删边后进行DML操作报错
    ASSERT_NO_FATAL_FAILURE(YangMergePartialTreeDataAfterDropEdge(conn, stmt, edgelabelFile));

    // 清理顶点表
    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(stmt, vertexlabelFile));
}

TEST_F(StYangTreeDml, PartialTreeDmlWithTablespaceAsync)
{
    char tableSpace[MAX_TABLE_SPACE_LENGTH] = "tsp1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tableSpace;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 0;
    std::atomic_uint32_t step{0};
    EXPECT_EQ(GMERR_OK, GmcCreateTablespaceAsync(stmt, &tspCfg, AsyncOperationCb, (void *)&step));
    EpollWaitAndCheck(step, 1);
    GmcNspCfgT nspCfg = {.namespaceName = "YangTspTest",
        .userName = NULL,
        .tablespaceName = tableSpace,
        .trxCfg = {.trxType = GMC_OPTIMISTIC_TRX, .isolationLevel = GMC_TX_ISOLATION_REPEATABLE}};
    EXPECT_EQ(GMERR_OK, GmcCreateNamespaceWithCfgAsync(stmt, &nspCfg, AsyncOperationCb, (void *)&step));
    EpollWaitAndCheck(step, 2);
    EXPECT_EQ(GMERR_OK, GmcUseNamespaceAsync(stmt, nspCfg.namespaceName, AsyncOperationCb, (void *)&step));
    EpollWaitAndCheck(step, 3);
    // 1.建表
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabelBatch.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edgeLabelBatch.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));

    // 1.1.设置subtree查询条件
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "root";
    filter.subtree.json = "{}";
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };

    // 2.树节点insert操作
    ASSERT_NO_FATAL_FAILURE(YangInsertPartialTreeData(conn, GMC_OPERATION_MERGE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/insert_reply.json"));

    // 3.树节点merge操作
    ASSERT_NO_FATAL_FAILURE(YangMergePartialTreeData(conn, GMC_OPERATION_MERGE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/merge_reply.json"));

    // 4.树节点none操作
    ASSERT_NO_FATAL_FAILURE(YangNonePartialTreeData(conn, GMC_OPERATION_MERGE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/none_reply.json"));

    // 5.树节点replace操作
    ASSERT_NO_FATAL_FAILURE(YangReplacePartialTreeData(conn, GMC_OPERATION_MERGE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/replace_reply.json"));

    // 6.树节点delete操作
    ASSERT_NO_FATAL_FAILURE(YangDeletePartialTreeData(conn, GMC_OPERATION_MERGE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/delete_reply.json"));

    // 7.树节点remove操作
    ASSERT_NO_FATAL_FAILURE(YangRemovePartialTreeData(conn, GMC_OPERATION_MERGE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/remove_reply.json"));

    // 删除所有数据
    ASSERT_NO_FATAL_FAILURE(YangRemovePartialTreeData(conn, GMC_OPERATION_MERGE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/remove_reply.json"));
    // tablespace下存在表，删除失败
    EXPECT_EQ(GMERR_OK,
        GmcDropTablespaceAsync(stmt, tspCfg.tablespaceName, AsyncOperationCb<GMERR_RESTRICT_VIOLATION>, (void *)&step));
    EpollWaitAndCheck(step, 4);
    // 清理
    ASSERT_NO_FATAL_FAILURE(LltClearNamespaceAsync(stmt, nspCfg.namespaceName));
    // tablespace下的表已被清楚，删除成功
    EXPECT_EQ(GMERR_OK, GmcDropNamespaceAsync(stmt, nspCfg.namespaceName, AsyncOperationCb, (void *)&step));
    EpollWaitAndCheck(step, 5);
    EXPECT_EQ(GMERR_OK, GmcDropTablespaceAsync(stmt, tspCfg.tablespaceName, AsyncOperationCb, (void *)&step));
    EpollWaitAndCheck(step, 6);
}

// 测试场景：异步执行部分打散树模型顶点none操作下树节点六原语操作
TEST_F(StYangTreeDml, PartialTreeDmlNoneAsync)
{
    // 1.建表
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabelBatch.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edgeLabelBatch.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));

    // 1.1.编辑subtree查询条件
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "root";
    filter.subtree.json = "{}";
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    // 2.插入数据
    ASSERT_NO_FATAL_FAILURE(YangInsertPartialTreeData(conn));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/insert_reply.json"));

    // 3.树节点merge操作
    ASSERT_NO_FATAL_FAILURE(YangMergePartialTreeData(conn, GMC_OPERATION_NONE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/merge_reply.json"));

    // 4.树节点none操作
    ASSERT_NO_FATAL_FAILURE(YangNonePartialTreeData(conn, GMC_OPERATION_NONE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/none_reply.json"));

    // 5.操作replace操作
    ASSERT_NO_FATAL_FAILURE(YangReplacePartialTreeData(conn, GMC_OPERATION_NONE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/replace_reply.json"));

    // 6.树节点delete操作
    ASSERT_NO_FATAL_FAILURE(YangDeletePartialTreeData(conn, GMC_OPERATION_NONE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/delete_reply.json"));

    // 7.树节点remove操作
    ASSERT_NO_FATAL_FAILURE(YangRemovePartialTreeData(conn, GMC_OPERATION_NONE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/remove_reply.json"));

    // 清理
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
}

/* ×为需要报错的场景
       子节点
父节点	merge	replace	create	delete	remove	none
merge	√	    √	    √	    √	    √	    √
replace	√	    √	    √	    ×	    ×	    ×
create	√	    √	    √	    ×	    ×	    ×
delete	×	    ×	    ×	    ×	    ×	    ×
remove	×	    ×	    ×	    ×	    ×	    ×
none	√	    √	    √	    √	    √	    √
 */

// 测试场景：部分打散树模型原语类型、多操作的合法性校验
TEST_F(StYangTreeDml, PartialTreeDmlCheckTypeAndMultiOp)
{
    // 1.建表
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabelBatch.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edgeLabelBatch.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));

    // 2.校验node操作的合法性，只支持六原语（此处父节点不是 GMC_OPERATION_SUBTREE_FILTER
    // 故不支持子树过滤），其他操作报错
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_MERGE));
    GmcOperationTypeE opType[] = {
        GMC_OPERATION_INSERT, GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_DELETE_GRAPH, GMC_OPERATION_REMOVE_GRAPH,  // 有效操作
        GMC_OPERATION_INSERT_WITH_RESOURCE, GMC_OPERATION_REPLACE, GMC_OPERATION_REPLACE_WITH_RESOURCE,
        GMC_OPERATION_DELETE, GMC_OPERATION_UPDATE, GMC_OPERATION_UPDATE_VERSION, GMC_OPERATION_SCAN,
        GMC_OPERATION_CREATE_VERTEX_LABEL, GMC_OPERATION_CREATE_EDGE_LABEL, GMC_OPERATION_DROP_VERTEX_LABEL,
        GMC_OPERATION_DROP_EDGE_LABEL, GMC_OPERATION_CREATE_KV_TABLE, GMC_OPERATION_DROP_KV_TABLE,
        GMC_OPERATION_BUTT,  // 无效操作
    };
    const uint32_t validIndex = 5;
    GmcNodeT *rootNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(stmt, &rootNode));
    GmcNodeT *node = NULL;
    for (uint32_t i = 0; i < sizeof(opType) / sizeof(opType[0]); ++i) {
        if (i <= validIndex) {
            ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, "c1", opType[i], &node));
        } else {
            ASSERT_EQ(GMERR_INVALID_VALUE, GmcYangEditChildNode(rootNode, "c1", opType[i], &node));
        }
    }

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(stmt, &rootNode));
    GmcNodeT *c1 = NULL;
    // 3.节点多操作叠加合法性校验
    // merge-create create-create  replace-insert remove-delete delete-delete
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, "c1", GMC_OPERATION_MERGE, &c1));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, GmcYangEditChildNode(rootNode, "c1", GMC_OPERATION_INSERT, &c1));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, GmcYangEditChildNode(rootNode, "c1", GMC_OPERATION_INSERT, &c1));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, "c1", GMC_OPERATION_REPLACE_GRAPH, &c1));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, GmcYangEditChildNode(rootNode, "c1", GMC_OPERATION_INSERT, &c1));
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, "c1", GMC_OPERATION_REMOVE_GRAPH, &c1));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, GmcYangEditChildNode(rootNode, "c1", GMC_OPERATION_DELETE_GRAPH, &c1));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, GmcYangEditChildNode(rootNode, "c1", GMC_OPERATION_DELETE_GRAPH, &c1));

    // 清理
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
}

static bool IsIllegalTreeNodeOperation(GmcOperationTypeE parentOp, GmcOperationTypeE childOp)
{
    if (parentOp == GMC_OPERATION_DELETE_GRAPH || parentOp == GMC_OPERATION_REMOVE_GRAPH) {
        return true;
    }
    if (parentOp == GMC_OPERATION_INSERT || parentOp == GMC_OPERATION_REPLACE_GRAPH) {
        if (childOp == GMC_OPERATION_DELETE_GRAPH || childOp == GMC_OPERATION_REMOVE_GRAPH ||
            childOp == GMC_OPERATION_NONE) {
            return true;
        }
    }
    return false;
}

static GmcOperationTypeE g_treeNodeOpType[] = {GMC_OPERATION_INSERT, GMC_OPERATION_REPLACE_GRAPH,
    GMC_OPERATION_DELETE_GRAPH, GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_MERGE, GMC_OPERATION_NONE};

// 测试场景：部分打散树模型父子节点原语操作合法性校验（父节点为 vertex 或 node，子节点为 node）
TEST_F(StYangTreeDml, PartialTreeDmlCheckNodeToNode)
{
    // 1. 建表
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabelBatch.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edgeLabelBatch.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));
    GmcStmtT *rootStmt = NULL;

    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    GmcNodeT *rootNode = NULL;
    uint32_t nodeOpNum = sizeof(g_treeNodeOpType) / sizeof(g_treeNodeOpType[0]);
    GmcNodeT *c1Node = NULL;

    // 2. 校验节点 root （vertex 类型）及其子节点 c1 之间的原语操作合法性
    for (uint32_t i = 0; i < nodeOpNum; ++i) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, "root", g_treeNodeOpType[i]));
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));
        for (uint32_t j = 0; j < nodeOpNum; ++j) {
            if (IsIllegalTreeNodeOperation(g_treeNodeOpType[i], g_treeNodeOpType[j])) {
                EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcYangEditChildNode(rootNode, "c1", g_treeNodeOpType[j], &c1Node))
                    << "Parent op: " << g_treeNodeOpType[i] << "\nProp op: " << g_treeNodeOpType[j] << endl;
            } else {
                EXPECT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, "c1", g_treeNodeOpType[j], &c1Node))
                    << "Parent op: " << g_treeNodeOpType[i] << "\nProp op: " << g_treeNodeOpType[j] << endl;
            }
        }
    }

    // 3. 校验节点 c1 （node 类型）及其子节点 c1_1 之间的原语操作合法性
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, "root", GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));
    GmcNodeT *c11Node = NULL;
    for (uint32_t i = 0; i < nodeOpNum; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, "c1", g_treeNodeOpType[i], &c1Node));
        for (uint32_t j = 0; j < nodeOpNum; ++j) {
            if (IsIllegalTreeNodeOperation(g_treeNodeOpType[i], g_treeNodeOpType[j])) {
                ASSERT_EQ(GMERR_SYNTAX_ERROR, GmcYangEditChildNode(c1Node, "c1_1", g_treeNodeOpType[j], &c11Node))
                    << "Parent op: " << g_treeNodeOpType[i] << "\nChild op: " << g_treeNodeOpType[j] << endl;
            } else {
                ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1Node, "c1_1", g_treeNodeOpType[j], &c11Node))
                    << "Parent op: " << g_treeNodeOpType[i] << "\nChild op: " << g_treeNodeOpType[j] << endl;
            }
        }
    }

    // 清理
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
}

// 测试场景：部分打散树模型父子节点原语操作合法性校验（父节点为 node，子节点为 vertex）
TEST_F(StYangTreeDml, PartialTreeDmlCheckNodeToVertex)
{
    // 1.建表
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabelBatch.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edgeLabelBatch.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    GmcStmtT *employeeStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, "root", GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));

    GmcNodeT *rootNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));

    // 2. 校验父节点 company （node 类型）和子节点 employee（vertex 类型）之间的操作合法性
    GmcNodeT *companyNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &employeeStmt));
    uint32_t opNum = sizeof(g_treeNodeOpType) / sizeof(g_treeNodeOpType[0]);
    for (uint32_t i = 0; i < opNum; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, "company", g_treeNodeOpType[i], &companyNode));
        for (uint32_t j = 0; j < opNum; ++j) {
            ASSERT_EQ(
                GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, "root::company::employee", g_treeNodeOpType[j]));
            if (IsIllegalTreeNodeOperation(g_treeNodeOpType[i], g_treeNodeOpType[j])) {
                EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcYangBindChild(batch, rootStmt, employeeStmt))
                    << "Parent op: " << g_treeNodeOpType[i] << "\nChild op: " << g_treeNodeOpType[j] << endl;
            } else {
                EXPECT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt))
                    << "Parent op: " << g_treeNodeOpType[i] << "\nChild op: " << g_treeNodeOpType[j] << endl;
            }
        }
    }

    // 清理
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(employeeStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
}

// 测试场景：部分打散树模型父子节点原语操作合法性校验（父子节点均为 vertex）
TEST_F(StYangTreeDml, PartialTreeDmlCheckVertexToVertex)
{
    // 1.建表
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabelBatch.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edgeLabelBatch.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    GmcStmtT *employeeStmt = NULL;
    GmcStmtT *projectStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, "root", GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));

    GmcNodeT *rootNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));
    GmcNodeT *companyNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, "company", GMC_OPERATION_MERGE, &companyNode));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    // 2. 校验父节点 employee 和子节点 project（均为 vertex 类型）之间的操作合法性
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &projectStmt));
    uint32_t opNum = sizeof(g_treeNodeOpType) / sizeof(g_treeNodeOpType[0]);
    for (uint32_t i = 0; i < opNum; ++i) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, "root::company::employee", g_treeNodeOpType[i]));
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, employeeStmt));
        for (uint32_t j = 0; j < opNum; ++j) {
            ASSERT_EQ(GMERR_OK,
                GmcPrepareStmtByLabelName(projectStmt, "root::company::employee::project", g_treeNodeOpType[j]));
            if (IsIllegalTreeNodeOperation(g_treeNodeOpType[i], g_treeNodeOpType[j])) {
                EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcYangBindChild(batch, employeeStmt, projectStmt))
                    << "Parent op: " << g_treeNodeOpType[i] << "\nChild op: " << g_treeNodeOpType[j] << endl;
            } else {
                EXPECT_EQ(GMERR_OK, GmcYangBindChild(batch, employeeStmt, projectStmt))
                    << "Parent op: " << g_treeNodeOpType[i] << "\nChild op: " << g_treeNodeOpType[j] << endl;
            }
        }
    }

    // 清理
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(projectStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(employeeStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
}

static bool IsIllegalPropertyOperation(GmcOperationTypeE parentOp, GmcYangPropOpTypeE childOp)
{
    if (parentOp == GMC_OPERATION_DELETE_GRAPH || parentOp == GMC_OPERATION_REMOVE_GRAPH) {
        return true;
    }
    if (parentOp == GMC_OPERATION_INSERT || parentOp == GMC_OPERATION_REPLACE_GRAPH) {
        if (childOp == GMC_YANG_PROPERTY_OPERATION_DELETE || childOp == GMC_YANG_PROPERTY_OPERATION_REMOVE ||
            childOp == GMC_YANG_PROPERTY_OPERATION_NONE) {
            return true;
        }
    }
    return false;
}

static GmcYangPropOpTypeE g_propOpType[] = {GMC_YANG_PROPERTY_OPERATION_CREATE, GMC_YANG_PROPERTY_OPERATION_MERGE,
    GMC_YANG_PROPERTY_OPERATION_REPLACE, GMC_YANG_PROPERTY_OPERATION_DELETE, GMC_YANG_PROPERTY_OPERATION_REMOVE};

// 测试场景：部分打散树模型父子节点原语操作合法性校验（父节点为 vertex 或 node，子节点为字段）
TEST_F(StYangTreeDml, PartialTreeDmlCheckNodeToProperty)
{
    // 1.建表
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabelBatch.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edgeLabelBatch.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    GmcStmtT *employeeStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    GmcNodeT *rootNode = NULL;
    char propValue[50] = {0};
    GmcPropValueT prop = {0};
    sprintf_s(propValue, sizeof(propValue), "testStr");
    LltInitPropValue(&prop, "name", GMC_DATATYPE_STRING, propValue, strlen(propValue));
    uint32_t nodeOpNum = sizeof(g_treeNodeOpType) / sizeof(g_treeNodeOpType[0]);
    uint32_t propOpNum = sizeof(g_propOpType) / sizeof(g_propOpType[0]);

    // 2. 校验节点 root （vertex 类型）及其字段之间的原语操作合法性， 使用 GmcYangSetVertexProperty 接口
    for (uint32_t i = 0; i < nodeOpNum; ++i) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, "root", g_treeNodeOpType[i]));
        for (uint32_t j = 0; j < propOpNum; ++j) {
            if (IsIllegalPropertyOperation(g_treeNodeOpType[i], g_propOpType[j])) {
                EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcYangSetVertexProperty(rootStmt, &prop, g_propOpType[j]))
                    << "Parent op: " << g_treeNodeOpType[i] << "\nProp op: " << g_propOpType[j] << endl;
            } else {
                EXPECT_EQ(GMERR_OK, GmcYangSetVertexProperty(rootStmt, &prop, g_propOpType[j]))
                    << "Parent op: " << g_treeNodeOpType[i] << "\nProp op: " << g_propOpType[j] << endl;
            }
        }
    }

    // 3. 校验节点 root （vertex 类型）及其字段之间的原语操作合法性，使用 GmcYangSetNodeProperty 接口
    for (uint32_t i = 0; i < nodeOpNum; ++i) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, "root", g_treeNodeOpType[i]));
        ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));
        for (uint32_t j = 0; j < propOpNum; ++j) {
            if (IsIllegalPropertyOperation(g_treeNodeOpType[i], g_propOpType[j])) {
                EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcYangSetNodeProperty(rootNode, &prop, g_propOpType[j]))
                    << "Parent op: " << g_treeNodeOpType[i] << "\nProp op: " << g_propOpType[j] << endl;
            } else {
                EXPECT_EQ(GMERR_OK, GmcYangSetNodeProperty(rootNode, &prop, g_propOpType[j]))
                    << "Parent op: " << g_treeNodeOpType[i] << "\nProp op: " << g_propOpType[j] << endl;
            }
        }
    }

    // 4. 校验节点 company （node 类型）及其字段之间的原语操作合法性
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, "root", GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));
    GmcNodeT *companyNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &employeeStmt));

    for (uint32_t i = 0; i < nodeOpNum; ++i) {
        ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, "company", g_treeNodeOpType[i], &companyNode));
        for (uint32_t j = 0; j < propOpNum; ++j) {
            if (IsIllegalPropertyOperation(g_treeNodeOpType[i], g_propOpType[j])) {
                EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcYangSetNodeProperty(companyNode, &prop, g_propOpType[j]))
                    << "Parent op: " << g_treeNodeOpType[i] << "\nProp op: " << g_propOpType[j] << endl;
            } else {
                EXPECT_EQ(GMERR_OK, GmcYangSetNodeProperty(companyNode, &prop, g_propOpType[j]))
                    << "Parent op: " << g_treeNodeOpType[i] << "\nProp op: " << g_propOpType[j] << endl;
            }
        }
    }

    // 清理
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
}

/* ×为需要报错的场景
        节点
操作	存在	不存在
merge	√	    √
replace	√	    √
create	×	    √
delete	√	    ×
remove	√	    √
none	√	    ×
另外：建边时在srcVertex 上父node没有创建则报错
 */
// 测试场景：部分打散树模型节点存在与否和操作类型之间的约束验证
TEST_F(StYangTreeDml, PartialTreeDmlCheckDataState)
{
    // 1.建表
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabelBatch.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edgeLabelBatch.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));

    // 2.插入数据
    ASSERT_NO_FATAL_FAILURE(YangInsertPartialTreeData(conn));

    // 2.对已经存在的node company进行插入操作，预计报错
    ASSERT_NO_FATAL_FAILURE(YangPartialTreeOperationNode(conn, GMC_OPERATION_INSERT, GMERR_SYNTAX_ERROR));

    // 3.对已经存在的node进行delete操作，预计成功
    ASSERT_NO_FATAL_FAILURE(YangPartialTreeOperationNode(conn, GMC_OPERATION_DELETE_GRAPH, GMERR_OK));

    // 4.对已经删除的node进行delete操作，预计失败报错
    ASSERT_NO_FATAL_FAILURE(YangPartialTreeOperationNode(conn, GMC_OPERATION_DELETE_GRAPH, GMERR_SYNTAX_ERROR));

    // 5.对已经删除的node进行none操作，预计失败报错
    ASSERT_NO_FATAL_FAILURE(YangPartialTreeOperationNode(conn, GMC_OPERATION_NONE, GMERR_OK));

    // 6.对已经删除的node进行remove操作，预计不报错
    ASSERT_NO_FATAL_FAILURE(YangPartialTreeOperationNode(conn, GMC_OPERATION_REMOVE_GRAPH, GMERR_OK));

    // 7.插入子图时src顶点上的对应父node没有创建，预计报错
    ASSERT_NO_FATAL_FAILURE(YangPartialTreeInsertChild(conn, GMERR_SYNTAX_ERROR));

    // 清理
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
}

// 测试场景：树节点支持多操作，一个节点可以先delete，然后再进行create操作
TEST_F(StYangTreeDml, PartialTreeDmlMultiOperationAsync)
{
    // 1.建表
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabelBatch.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edgeLabelBatch.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));

    // 1.1.编辑subtree查询条件
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "root";
    filter.subtree.json = "{}";
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    // 2.插入数据
    ASSERT_NO_FATAL_FAILURE(YangInsertPartialTreeData(conn));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/insert_reply.json"));

    // 3.树节点支持多merge操作
    ASSERT_NO_FATAL_FAILURE(YangPartialTreeMultiMergeOperation(conn, GMC_OPERATION_MERGE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/multi_merge_op_reply.json"));

    // 4.树节点支持多原语操作组合
    ASSERT_NO_FATAL_FAILURE(YangPartialTreeMultiOperation(conn, GMC_OPERATION_MERGE));
    ASSERT_NO_FATAL_FAILURE(CheckReplyJson(stmt, filters, "006_graph/yang/read_by_dml/multi_op_reply.json"));

    // 清理
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
}

// 测试场景：list的自增ID作为索引key字段，预计建表失败
TEST_F(StYangTreeDml, IDisIndePropOfList)
{
    const char *vertexLabelErr0 =
        R"([
            {
                "type":"list",
                "name":"T0",
                "fields":[
                {"name":"ID", "type":"uint32", "nullable":false, "auto_increment": true},
                {"name":"PID", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false}
                ],
                "keys":[
                    {
                        "node":"T0",
                        "name":"T0.pk",
                        "fields":["PID", "ID"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ])";
    const char *vertexLabelErr1 =
        R"([
            {
                "type":"list",
                "name":"T0",
                "fields":[
                    {"name":"ID", "type":"uint32", "nullable":false, "auto_increment": true},
                    {"name":"PID", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint32", "nullable":false}
                ],
                "keys":[
                    {
                        "node":"T0",
                        "name":"T0.pk",
                        "fields":["PID", "F1"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"T0",
                        "name":"T0.lk",
                        "fields":["ID"],
                        "index":{"type":"local"},
                        "constraints":{"unique":true}
                    }
                ]
            }
        ])";
    const char *vertexLabelErr2 =
        R"([
            {
                "type":"list",
                "name":"T0",
                "fields":[
                    {"name":"ID", "type":"uint32", "nullable":false, "auto_increment": true},
                    {"name":"PID", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint32", "nullable":false},
                    {"name": "C1",
                    "type": "container", "fields": [{"name":"F1", "type":"uint32", "nullable":false}]}
                ],
                "keys":[
                    {
                        "node":"T0",
                        "name":"T0.pk",
                        "fields":["PID", "F1"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "name": "T0.UK.1",
                        "index": {"type": "list_localhash"},
                        "node": "T0",
                        "fields": ["F1", "C1/F1"],
                        "constraints": {"unique": true, "null_check": true}
                    },
                    {
                        "name": "T0.UK.2",
                        "index": {"type": "list_localhash"},
                        "node": "T0",
                        "fields": ["F1", "ID"],
                        "constraints": {"unique": true, "null_check": true}
                    }
                ]
            }
        ])";
    // test point: 创建vertex lable
    std::atomic_uint32_t step{0};
    GmcCreateVertexLabelDoneT userCb = AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>;
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, vertexLabelErr0, g_cfgJson, userCb, &step));
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, vertexLabelErr1, g_cfgJson, userCb, &step));
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, vertexLabelErr2, g_cfgJson, userCb, &step));
    EpollWaitAndCheck(step, 3);
}

// 测试场景：部分打散树模型设置属性接口返回lastError信息校验
TEST_F(StYangTreeDml, PartialTreeDmlArgumentCheck)
{
    // 1.建表
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabelBatch.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edgeLabelBatch.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));

    // 2.校验node设置属性时参数报错场景，包含lastError
    GmcNodeT *rootNode = NULL;
    GmcNodeT *c1Node = NULL;
    GmcPropValueT propValue = {0};
    bool bValue = true;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(stmt, &rootNode));

    const char *errorMsg = "Not normal parameter value. Set property value";

    GmcYangPropOpTypeE propOp = GMC_YANG_PROPERTY_OPERATION_MERGE;
    LltInitPropValue(&propValue, "boolean", GMC_DATATYPE_BUTT, &bValue, sizeof(bValue));
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcYangSetNodeProperty(rootNode, &propValue, propOp));
    ASSERT_STREQ(GmcGetLastError(), errorMsg);
    LltInitPropValue(&propValue, "boolean", GMC_DATATYPE_BOOL, &bValue, 0);
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcYangSetNodeProperty(rootNode, &propValue, propOp));
    ASSERT_STREQ(GmcGetLastError(), errorMsg);

    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, "c1", GMC_OPERATION_MERGE, &c1Node));
    LltInitPropValue(&propValue, "boolean", GMC_DATATYPE_BUTT, &bValue, sizeof(bValue));
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcYangSetNodeProperty(c1Node, &propValue, propOp));
    LltInitPropValue(&propValue, "boolean", GMC_DATATYPE_BOOL, &bValue, 0);
    ASSERT_STREQ(GmcGetLastError(), errorMsg);
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcYangSetNodeProperty(c1Node, &propValue, propOp));
    ASSERT_STREQ(GmcGetLastError(), errorMsg);

    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK,
        GmcDeleteAllFastAsync(stmt, "root::company::employee", AsyncOperationCb<GMERR_FEATURE_NOT_SUPPORTED>, &step));
    EpollWaitAndCheck(step, 1);
    // 清理
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
}

// 测试场景：插入key字段数据超过keyBuf限制，报错
TEST_F(StYangTreeDml, PartialTreeDmlInsertLargeKeyDataAsync)
{
    // 1.建表
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabelBatch.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edgeLabelBatch.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));

    // 2.插入大的key字段数据
    ASSERT_NO_FATAL_FAILURE(YangInsertLargeKeyData(conn));

    // 清理
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
}

// 测试场景：六原语操作审计日志检查
TEST_F(StYangTreeDml, PartialTreeDmlAuditLogCheck)
{
    // 清理日志
    system("rm -rf ./log");
    // 1.建表
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabelBatch.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edgeLabelBatch.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));

    static const char *dataConfig = R"({"yang_model":1})";
    // 2.对节点进行六原语操作
    string dataJson = GetFileContext("006_graph/yang/read_by_dml/dml_data.json");
    ASSERT_NO_FATAL_FAILURE(LltBatchExecuteByJsonAsync(conn, stmt, "root", dataJson.c_str(), dataConfig));

    /// 3.校验审计日志
    ASSERT_NO_FATAL_FAILURE(StCommonVerifyLogContent("./log/secure/sgmserver/sgmserver.log", "INSERT VERTEX", 1));
    ASSERT_NO_FATAL_FAILURE(StCommonVerifyLogContent("./log/secure/sgmserver/sgmserver.log", "MERGE VERTEX", 1));
    ASSERT_NO_FATAL_FAILURE(StCommonVerifyLogContent("./log/secure/sgmserver/sgmserver.log", "NONE VERTEX", 1));
    ASSERT_NO_FATAL_FAILURE(StCommonVerifyLogContent("./log/secure/sgmserver/sgmserver.log", "REPLACE GRAPH", 1));
    ASSERT_NO_FATAL_FAILURE(StCommonVerifyLogContent("./log/secure/sgmserver/sgmserver.log", "DELETE GRAPH", 1));
    ASSERT_NO_FATAL_FAILURE(StCommonVerifyLogContent("./log/secure/sgmserver/sgmserver.log", "REMOVE GRAPH", 1));

    // 4.清理
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
}

// 测试场景：树节点支持多操作，一个节点可以先delete，然后再进行create操作
TEST_F(StYangTreeDml, PartialTreeDmlMultiOperationMergeOps)
{
    // 1.建表
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabelBatch.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edgeLabelBatch.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));

    bool doPrint = false;
    ASSERT_NO_FATAL_FAILURE(YangInsertAndSortPartialTreeData(conn, GMC_OPERATION_INSERT, doPrint));
    ASSERT_NO_FATAL_FAILURE(YangNoneAndSortPartialTreeData(conn, GMC_OPERATION_NONE, doPrint));
    ASSERT_NO_FATAL_FAILURE(YangInsertAndSortPartialTreeData(conn, GMC_OPERATION_MERGE, doPrint));
    ASSERT_NO_FATAL_FAILURE(YangMultiAndSortPartialTreeData(conn, GMC_OPERATION_INSERT, doPrint));
    ASSERT_NO_FATAL_FAILURE(YangMultiTrigExpandAndSortPartialTreeData(conn, GMC_OPERATION_INSERT, doPrint));

    // 清理
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
}
// 测试场景：yang操作，stmt混用、错用
TEST_F(StYang, YangVertexUseStmtIllegal)
{
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabelBatch.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edgeLabelBatch.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));
    GmcStmtT *rootStmt = NULL;
    GmcStmtT *employeeStmt = NULL;
    GmcStmtT *countryStmt = NULL;
    GmcStmtT *nullStmt = NULL;
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &employeeStmt));
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &countryStmt));
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &nullStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, "root", GMC_OPERATION_INSERT));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(employeeStmt, "root::company::employee", GMC_OPERATION_INSERT));
    ASSERT_EQ(
        GMERR_OK, GmcPrepareStmtByLabelName(countryStmt, "root::company::employee::country", GMC_OPERATION_INSERT));

    GmcBatchT *batch = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));
    // 测试点：非根节点，set root，预计报错
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, GmcYangSetRoot(batch, employeeStmt));
    // 测试点：空stmt， set root，预计报错
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, GmcYangSetRoot(batch, countryStmt));
    // 测试点：空stmt，bind  child，预计报错
    ASSERT_EQ(GMERR_UNEXPECTED_NULL_VALUE, GmcYangBindChild(batch, rootStmt, nullStmt));
    ASSERT_EQ(GMERR_UNEXPECTED_NULL_VALUE, GmcYangBindChild(batch, nullStmt, rootStmt));
    // 测试点：父子stmt用反，bind child，预计报错
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcYangBindChild(batch, employeeStmt, rootStmt));
    // 测试点：非父子stmt，bind child，预计报错
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcYangBindChild(batch, rootStmt, countryStmt));

    GmcNodeT *rootNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));

    // 测试点：设置其它stmt的属性，预计报错
    GmcPropValueT propValue = {0};
    const char *str = "yang";
    LltInitPropValue(&propValue, "addr", GMC_DATATYPE_STRING, str, strlen(str));
    ASSERT_EQ(
        GMERR_INVALID_PROPERTY, GmcYangSetVertexProperty(rootStmt, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE));
    ASSERT_EQ(GMERR_INVALID_PROPERTY, GmcYangSetNodeProperty(rootNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    const char *nodeName = "choice";
    // 获取其它stmt的node，预计报错
    GmcNodeT *childNode = NULL;
    ASSERT_EQ(GMERR_INVALID_NAME, GmcYangEditChildNode(rootNode, nodeName, GMC_OPERATION_INSERT, &childNode));
    // 测试点：空stmt，batch add，预计报错
    ASSERT_EQ(GMERR_WRONG_STMT_OBJECT, GmcBatchAddDML(batch, nullStmt));
    GmcYangListLocatorT listProp1;
    ASSERT_NO_FATAL_FAILURE(LltInitListProperty(&listProp1, GMC_YANG_LIST_POSITION_FIRST, 0, NULL));
    // 测试点：根节点set list locator，，预计报错
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcYangSetListLocator(rootStmt, &listProp1));
    // 测试点：正常set list locator，，预计成功
    ASSERT_EQ(GMERR_OK, GmcYangSetListLocator(employeeStmt, &listProp1));
    // 测试点：空stmt，set list locator，预计报错
    ASSERT_EQ(GMERR_WRONG_STMT_OBJECT, GmcYangSetListLocator(nullStmt, &listProp1));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = NULL},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    static const char *replyJson = R"({})";
    // 测试点：未设置查询条件的stmt，subtree查询，预计报错
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeJsonAsync(rootStmt, filters, replyJson, GMERR_NULL_VALUE_NOT_ALLOWED));
    // 测试点：空stmt，subtree查询，预计报错
    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeJsonAsync(nullStmt, filters, replyJson, GMERR_NULL_VALUE_NOT_ALLOWED));

    vector<string> expectDiff = {""};
    // 测试点：未设置查询条件的stmt，diff查询，预计报错
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(rootStmt, expectDiff, GMERR_DATA_EXCEPTION));
    // 测试点：空stmt，diff查询，预计报错
    ASSERT_NO_FATAL_FAILURE(LltCheckDiffReplyAsync(nullStmt, expectDiff, GMERR_DATA_EXCEPTION));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(countryStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(employeeStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(nullStmt));
}

// 测试场景：fix:DTS2023032003089 插入数据不带MK字段，预期报错
TEST_F(StYang, YangVertexInsertDataWithoutMkError)
{
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabelBatch.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edgeLabelBatch.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    GmcStmtT *rootStmt = NULL;
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, "root", GMC_OPERATION_INSERT));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    GmcStmtT *netConfStmt = NULL;
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &netConfStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(netConfStmt, "root::netconf", GMC_OPERATION_INSERT));
    ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, netConfStmt));
    ASSERT_EQ(GMERR_DATA_EXCEPTION, GmcBatchAddDML(batch, netConfStmt));

    ASSERT_NO_FATAL_FAILURE(LltBatchExecute(batch));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(netConfStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
}
// 测试场景：非yang表使用yang相关dml接口，预计报错3000
TEST_F(StYang, NotYangVertexWithYangInterface)
{
    const char *labelLson = R"([{
        "type":"record",
        "name":"root",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"kid", "type":"record", "fields":
                    [
                        {"name":"F0", "type":"int32", "nullable":false}
                    ]
                }

            ],
        "keys":
            [
                {
                    "node":"root",
                    "name":"root_PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
    }])";
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, labelLson, cfgJson, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 1);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_INSERT));
    GmcPropValueT propValue = {0};
    int f0Value = 1;
    ASSERT_NO_FATAL_FAILURE(LltInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(f0Value)));
    ASSERT_EQ(
        GMERR_FEATURE_NOT_SUPPORTED, GmcYangSetVertexProperty(stmt, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    GmcNodeT *rootNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(stmt, &rootNode));
    ASSERT_EQ(
        GMERR_FEATURE_NOT_SUPPORTED, GmcYangSetNodeProperty(rootNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    GmcNodeT *childNode = NULL;
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcYangEditChildNode(rootNode, "kid", GMC_OPERATION_MERGE, &childNode));
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabelAsync(stmt, "root", AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 2);
}

// 测试场景：yang表使用非yang相关dml接口，预计报错3000
TEST_F(StYang, YangVertexWithNotYangInterface)
{
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabelBatch.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edgeLabelBatch.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_INSERT));
    int f0Value = 1;
    ASSERT_EQ(
        GMERR_FEATURE_NOT_SUPPORTED, GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(f0Value)));
    GmcNodeT *rootNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(stmt, &rootNode));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED,
        GmcNodeSetPropertyByName(rootNode, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(f0Value)));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
}

// 测试场景：fix:DTSxxx，对根节点merge，对P节点company进行remove， subtree report-all 预期查询无数据
TEST_F(StYang, YangVertexDmlTest1)
{
    // 建表
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabel_dts2023051008583.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexLabelAsync(stmt, vertexlabelFile, g_cfgJson));

    // 开事务
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));

    // 开启批操作
    GmcBatchT *batch = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    // 对根root执行merge操作，并且插入数据
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, stmt));
    GmcNodeT *rootNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(stmt, &rootNode));
    ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(rootNode, "root"));

    // 对company节点执行remove操作
    GmcNodeT *companyNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, "company", GMC_OPERATION_REMOVE_GRAPH, &companyNode));

    // 对ompany2节点执行merge操作
    GmcNodeT *company2Node = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, "company2", GMC_OPERATION_MERGE, &company2Node));

    // 对ompany22节点执行remove操作
    GmcNodeT *company22Node = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(company2Node, "company22", GMC_OPERATION_REMOVE_GRAPH, &company22Node));

    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcBatchExecuteAsync(batch, AsyncBatchOperationCb<GMERR_OK>, &step));
    EpollWaitAndCheck(step, 1);

    // 构造查询条件 根节点容器过滤
    GmcNodeT *rootNodeFilter;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_SUBTREE_FILTER));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(stmt, &rootNodeFilter));

    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = rootNodeFilter},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = DM_DEFAULT_FILTER_REPORT_ALL,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };

    // 预期返回的查询结果
    static const char *expectedJson = R"(
        {
            "name": "root",
            "int32": 5,
            "double": 3.141597,
            "boolean": false,
            "float": 3.1559998989105225,
            "company2": {
                "name": "company1_name"
            }
        }
    )";

    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeJsonAsync(stmt, filters, expectedJson));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(stmt, vertexlabelFile));
}

// 测试场景：fix:DTSxxx，对根节点merge，对P节点company和company22进行none操作， subtree report-all
// 预期查询不到company和company22的默认值
TEST_F(StYang, YangVertexDmlTest2)
{
    // 建表
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabel_dts2023051008583.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexLabelAsync(stmt, vertexlabelFile, g_cfgJson));

    // 开事务
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));

    // 开启批操作
    GmcBatchT *batch = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    // 对根root执行merge操作，并且插入数据
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, stmt));
    GmcNodeT *rootNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(stmt, &rootNode));
    ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(rootNode, "root"));

    // 对company节点执行none操作
    GmcNodeT *companyNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, "company", GMC_OPERATION_NONE, &companyNode));

    // 对ompany2节点执行none操作
    GmcNodeT *company2Node = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, "company2", GMC_OPERATION_NONE, &company2Node));

    // 对ompany22节点执行none操作
    GmcNodeT *company22Node = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(company2Node, "company22", GMC_OPERATION_NONE, &company22Node));

    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcBatchExecuteAsync(batch, AsyncBatchOperationCb<GMERR_OK>, &step));
    EpollWaitAndCheck(step, 1);

    // 构造查询条件 根节点容器过滤
    GmcNodeT *rootNodeFilter;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_SUBTREE_FILTER));
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(stmt, &rootNodeFilter));

    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = rootNodeFilter},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = DM_DEFAULT_FILTER_REPORT_ALL,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };

    // 预期返回的查询结果
    static const char *expectedJson = R"(
        {
            "name": "root",
            "int32": 5,
            "double": 3.141597,
            "boolean": false,
            "float": 3.1559998989105225,
            "company2": {
                "name": "company1_name"
            }
        }
    )";

    ASSERT_NO_FATAL_FAILURE(LltCheckSubtreeJsonAsync(stmt, filters, expectedJson));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_NO_FATAL_FAILURE(LltDropVertexLabelAsync(stmt, vertexlabelFile));
}

// 测试场景：fix:DTS2023042706026 无字段node节点数据 DML操作
TEST_F(StYang, YangVertexDmlTest3)
{
    // 建表
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabel_dts2023042706026.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edge_dts2023042706026.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));

    // 开事务
    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));

    // 开启批操作
    GmcBatchT *batch = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    // 对根root执行merge操作，并且插入数据
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, stmt));
    GmcNodeT *rootNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(stmt, &rootNode));
    uint32_t fieldValue = 100;
    GmcPropValueT propValue = {0};
    LltInitPropValue(&propValue, "F0", GMC_DATATYPE_UINT32, &fieldValue, sizeof(fieldValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(rootNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "P2", GMC_DATATYPE_UINT32, &fieldValue, sizeof(fieldValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(rootNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    GmcNodeT *childNode1 = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, "choice", GMC_OPERATION_MERGE, &childNode1));
    GmcNodeT *childNode2 = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(childNode1, "case1", GMC_OPERATION_MERGE, &childNode2));

    uint32_t valueF0 = 100;
    LltInitPropValue(&propValue, "F0", GMC_DATATYPE_UINT32, &valueF0, sizeof(valueF0));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(childNode2, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "F1", GMC_DATATYPE_UINT32, &valueF0, sizeof(valueF0));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(childNode2, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));

    GmcStmtT *listStmt = NULL;
    GmcNodeT *listRoot = NULL;
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &listStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(listStmt, "list", GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, stmt, listStmt));

    ASSERT_EQ(GMERR_OK, GmcGetRootNode(listStmt, &listRoot));

    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(listStmt, 1, GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t)));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(listStmt, "PK"));

    LltInitPropValue(&propValue, "F0", GMC_DATATYPE_UINT32, &valueF0, sizeof(valueF0));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(listRoot, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "F1", GMC_DATATYPE_UINT32, &valueF0, sizeof(valueF0));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(listRoot, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    GmcNodeT *listChildNode1 = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(listRoot, "listchoice", GMC_OPERATION_MERGE, &listChildNode1));
    GmcNodeT *listChildNode2 = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(listChildNode1, "listcase1", GMC_OPERATION_MERGE, &listChildNode2));

    LltInitPropValue(&propValue, "F0", GMC_DATATYPE_UINT32, &valueF0, sizeof(valueF0));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(listChildNode2, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "F1", GMC_DATATYPE_UINT32, &valueF0, sizeof(valueF0));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(listChildNode2, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(listRoot, "listchoice", GMC_OPERATION_MERGE, &listChildNode1));
    GmcNodeT *listChildNode3 = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(listChildNode1, "listcase2", GMC_OPERATION_MERGE, &listChildNode3));

    LltInitPropValue(&propValue, "F0", GMC_DATATYPE_UINT32, &valueF0, sizeof(valueF0));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(listChildNode3, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "F1", GMC_DATATYPE_UINT32, &valueF0, sizeof(valueF0));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(listChildNode3, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    GmcNodeT *listChildNode4 = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(listChildNode1, "listcase2", GMC_OPERATION_MERGE, &listChildNode4));

    LltInitPropValue(&propValue, "F0", GMC_DATATYPE_UINT32, &valueF0, sizeof(valueF0));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(listChildNode4, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    LltInitPropValue(&propValue, "F1", GMC_DATATYPE_UINT32, &valueF0, sizeof(valueF0));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(listChildNode4, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, listStmt));

    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcBatchExecuteAsync(batch, AsyncBatchOperationCb<GMERR_OK>, &step));
    EpollWaitAndCheck(step, 1);

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
}

// 测试场景：DTS2023060108381 对不存在的父节点node进行replace操作的同时对子节点node进行merge，并对字段做create
TEST_F(StYang, PartialTreeDmlReplaceNotExistParentNodeAndMergeChild)
{
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/tree_dml_vertexLabelBatch.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/tree_dml_edgeLabelBatch.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    // 对根节点传入 merge 操作，会变成创建
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, "root", GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));
    GmcNodeT *rootNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));

    // 对 c1 传入 replace 操作，会变成创建
    GmcNodeT *c1Node = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, "c1", GMC_OPERATION_REPLACE_GRAPH, &c1Node));
    ASSERT_NO_FATAL_FAILURE(YangSetNodeProperty(c1Node, "c1"));

    // 对 c1_1 传入 merge 操作，会变成创建
    GmcNodeT *c11Node = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangEditChildNode(c1Node, "c1_1", GMC_OPERATION_MERGE, &c11Node));
    // 对 c1_1 的字段传入 create 操作，预期能创建成功
    char propValue[50] = {0};
    GmcPropValueT prop = {0};
    sprintf_s(propValue, sizeof(propValue), "testStr");
    LltInitPropValue(&prop, "name", GMC_DATATYPE_STRING, propValue, strlen(propValue));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(c11Node, &prop, GMC_YANG_PROPERTY_OPERATION_CREATE));

    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));
    ASSERT_NO_FATAL_FAILURE(LltBatchExecute(batch));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    // 清理
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
}

// 测试场景：merge时传入的keyValue比实际索引字段个数多
TEST_F(StYang, PartialTreeDmlKeyNumInValid)
{
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/vertexLabelLocal.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/edgeLabelLocal.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    GmcStmtT *listStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    // 对根节点传入 merge 操作，会变成创建
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &listStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, "main_label", GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));
    GmcNodeT *rootNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(listStmt, "list_label_1", GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, listStmt));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(listStmt, "local"));
    uint32_t keyValue = 1;
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(listStmt, 0, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t)));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(listStmt, 1, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t)));
    ASSERT_EQ(GMERR_DATA_EXCEPTION, GmcBatchAddDML(batch, listStmt));
    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    // 清理
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(listStmt));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
}

// 测试场景：DTS2024103004755
TEST_F(StYang, FixDTS2024103004755)
{
    const char *vertexlabelFile = "006_graph/yang/yang_st_data/vertexLabelLocal.json";
    const char *edgelabelFile = "006_graph/yang/yang_st_data/edgeLabelLocal.json";
    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile, g_cfgJson));

    ASSERT_NO_FATAL_FAILURE(LltStartTrans(conn));
    GmcBatchT *batch = NULL;
    GmcStmtT *rootStmt = NULL;
    GmcStmtT *listStmt = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch));

    // 对根节点传入 merge 操作，会变成创建
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &listStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, "main_label", GMC_OPERATION_INSERT));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));
    GmcNodeT *rootNode = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(listStmt, "list_label_1", GMC_OPERATION_INSERT));
    ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, listStmt));
    GmcNodeT *listRoot = NULL;
    ASSERT_EQ(GMERR_OK, GmcGetRootNode(listStmt, &listRoot));
    GmcPropValueT propValue = {0};
    uint32_t valueF1 = 1;
    LltInitPropValue(&propValue, "list_1_F1", GMC_DATATYPE_UINT32, &valueF1, sizeof(valueF1));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(listRoot, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    int32_t valueF2 = 2;
    LltInitPropValue(&propValue, "list_1_F2", GMC_DATATYPE_INT32, &valueF2, sizeof(valueF2));
    ASSERT_EQ(GMERR_OK, GmcYangSetNodeProperty(listRoot, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    ASSERT_EQ(GMERR_DATA_EXCEPTION, GmcBatchAddDML(batch, listStmt));

    ASSERT_NO_FATAL_FAILURE(LltEndTrans(conn));

    // 清理
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(rootStmt));
    ASSERT_NO_FATAL_FAILURE(GmcFreeStmt(listStmt));
    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexlabelFile, edgelabelFile));
}
