{"groups": {"huawei-acl:acl2::groups::group": [{"identity": "2000", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3000", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2001", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3001", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2002", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3002", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2003", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3003", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2004", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3004", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2005", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3005", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2006", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3006", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2007", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3007", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2008", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3008", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2009", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3009", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2010", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3010", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2011", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3011", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2012", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3012", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2013", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3013", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2014", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3014", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2015", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3015", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2016", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3016", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2017", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3017", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2018", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3018", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2019", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3019", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2020", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3020", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2021", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3021", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2022", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3022", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2023", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3023", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2024", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3024", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2025", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3025", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2026", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3026", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2027", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3027", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2028", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3028", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2029", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3029", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2030", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3030", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2031", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3031", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2032", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3032", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2033", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3033", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2034", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3034", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2035", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3035", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2036", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3036", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2037", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3037", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2038", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3038", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2039", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3039", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2040", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3040", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2041", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3041", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2042", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3042", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2043", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3043", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2044", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3044", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2045", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3045", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2046", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3046", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2047", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3047", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2048", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3048", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2049", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3049", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2050", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3050", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2051", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3051", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2052", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3052", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2053", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3053", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2054", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3054", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2055", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3055", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2056", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3056", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2057", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3057", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2058", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3058", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2059", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3059", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2060", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3060", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2061", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3061", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2062", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3062", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2063", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3063", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2064", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3064", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2065", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3065", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2066", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3066", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2067", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3067", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2068", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3068", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2069", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3069", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2070", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3070", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2071", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3071", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2072", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3072", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2073", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3073", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2074", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3074", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2075", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3075", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2076", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3076", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2077", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3077", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2078", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3078", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2079", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3079", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2080", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3080", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2081", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3081", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2082", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3082", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2083", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3083", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2084", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3084", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2085", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3085", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2086", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3086", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2087", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3087", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2088", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3088", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2089", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3089", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2090", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3090", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2091", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3091", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2092", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3092", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2093", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3093", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2094", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3094", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2095", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3095", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2096", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3096", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2097", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3097", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2098", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3098", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2099", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3099", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2100", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3100", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2101", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3101", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2102", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3102", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2103", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3103", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2104", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3104", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2105", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3105", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2106", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3106", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2107", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3107", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2108", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3108", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2109", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3109", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2110", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3110", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2111", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3111", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2112", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3112", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2113", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3113", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2114", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3114", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2115", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3115", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2116", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3116", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2117", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3117", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2118", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3118", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2119", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3119", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2120", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3120", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2121", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3121", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2122", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3122", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2123", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3123", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2124", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3124", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2125", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3125", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2126", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3126", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}, {"identity": "2127", "rule-basics": {"huawei-acl:acl2::groups::group::rule-basics::rule-basic": [{"name": "rule1", "id": 5, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule2", "id": 10, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule3", "id": 15, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}, {"name": "rule4", "id": 20, "action": "permit", "source-ipaddr": "***********", "source-wild": "0.0.0.0"}]}}, {"identity": "3127", "rule-advances": {"huawei-acl:acl2::groups::group::rule-advances::rule-advance": [{"name": "rule1", "id": 5, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule2", "id": 10, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule3", "id": 15, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}, {"name": "rule4", "id": 20, "action": "permit", "protocol": 1, "source-ipaddr": "***********", "source-wild": "0.0.0.0", "dest-ipaddr": "***********", "dest-wild": "*********"}]}}]}}