[{"name": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "type": "list", "np_access": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "labelId", "type": "uint32", "nullable": false}, {"name": "nodeId", "type": "uint16", "nullable": false}, {"name": "propeId", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":id", ":pid", "labelId", "nodeId", "propeId"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "yang", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ds", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ietf-yang-library:yang-library", "type": "container", "is_config": false, "fields": [{"name": "content-id", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::module", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "revision", "type": "string", "is_config": false}, {"name": "namespace", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::module::submodule", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "revision", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::module::feature", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "feature", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "feature"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::module::deviation", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "deviation", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "deviation"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "revision", "type": "string", "is_config": false, "nullable": false}, {"name": "namespace", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name", "revision"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::location", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "location", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "location"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::submodule", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "revision", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::submodule::location", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "location", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "location"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::schema", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::schema::module-set", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "module-set", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "module-set"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::datastore", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "schema", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "notifications", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "notifications:create-subscription", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "stream", "type": "string", "is_config": false, "default": "NETCONF"}, {"name": "startTime", "type": "string", "is_config": false}, {"name": "stopTime", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "nc-notifications", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "nc-notifications:netconf", "type": "container", "is_config": false, "fields": [{"name": "streams", "type": "container", "is_config": false, "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "nc-notifications:netconf::streams::stream", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "description", "type": "string", "is_config": false}, {"name": "replaySupport", "type": "boolean", "is_config": false}, {"name": "replayLogCreationTime", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "openconfig-telemetry", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "openconfig-telemetry:telemetry-system", "type": "container", "fields": [{"name": "sensor-groups", "type": "container", "fields": []}, {"name": "destination-groups", "type": "container", "fields": []}, {"name": "subscriptions", "type": "container", "fields": [{"name": "persistent", "type": "container", "fields": []}]}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group", "type": "list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "sensor-group-id", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "../config/sensor-group-id"}]}, {"name": "config", "type": "container", "fields": [{"name": "sensor-group-id", "type": "string"}]}, {"name": "sensor-paths", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "sensor-group-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group::sensor-paths::sensor-path", "type": "list", "max-elements": 64, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "path", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "../config/path"}]}, {"name": "config", "type": "container", "fields": [{"name": "path", "type": "string"}]}], "keys": [{"name": "k0", "fields": [":pid", "path"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "openconfig-telemetry:telemetry-system::destination-groups::destination-group", "type": "list", "max-elements": 2, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "group-id", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "../config/group-id"}]}, {"name": "config", "type": "container", "fields": [{"name": "group-id", "type": "string"}]}, {"name": "destinations", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "group-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "openconfig-telemetry:telemetry-system::destination-groups::destination-group::destinations::destination", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "destination-address", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "../config/destination-address"}]}, {"name": "destination-port", "type": "uint16", "nullable": false, "clause": [{"type": "leafref", "formula": "../config/destination-port"}]}, {"name": "config", "type": "container", "fields": [{"name": "destination-address", "type": "string"}, {"name": "destination-port", "type": "uint16"}, {"name": "huawei-openconfig-telemetry-ext-lite:ssl-policy-name", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-ssl/huawei-ssl:ssl/ssl-policys/huawei-ssl:ssl::ssl-policys::ssl-policy/policy-name"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "destination-address", "destination-port"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription", "type": "list", "max-elements": 2, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "subscription-name", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "../config/subscription-name"}]}, {"name": "config", "type": "container", "fields": [{"name": "subscription-name", "type": "string"}, {"name": "local-source-address", "type": "string"}]}, {"name": "sensor-profiles", "type": "container", "fields": []}, {"name": "destination-groups", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "subscription-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::sensor-profiles::sensor-profile", "type": "list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "sensor-group", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "../config/sensor-group"}]}, {"name": "config", "type": "container", "fields": [{"name": "sensor-group", "type": "string", "clause": [{"type": "leafref", "formula": "../../../../../../../sensor-groups/openconfig-telemetry:telemetry-system::sensor-groups::sensor-group/config/sensor-group-id"}]}, {"name": "sample-interval", "type": "uint64", "default": 60000}]}], "keys": [{"name": "k0", "fields": [":pid", "sensor-group"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::destination-groups::destination-group", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "group-id", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "../config/group-id"}]}, {"name": "config", "type": "container", "fields": [{"name": "group-id", "type": "string", "clause": [{"type": "leafref", "formula": "../../../../../../../destination-groups/openconfig-telemetry:telemetry-system::destination-groups::destination-group/group-id"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "group-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-wired-port-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-wired-port-profile:wlan-wired-port-profile", "type": "container", "fields": [{"name": "wiredport-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-wired-port-profile:wlan-wired-port-profile::wiredport-profiles::wiredport-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "user-isolate", "type": "string", "default": "disable"}, {"name": "pvid", "type": "uint16"}, {"name": "tagged-vlan", "type": "string"}, {"name": "untagged-vlan", "type": "string", "default": "1"}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "hua<PERSON>-vlan", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-vlan:vlan", "type": "container", "fields": [{"name": "vlans", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-vlan:vlan::vlans::vlan", "type": "list", "max-elements": 4094, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "id", "type": "uint16", "nullable": false}, {"name": "name", "type": "string"}, {"name": "description", "type": "string"}, {"name": "type", "type": "string", "default": "common"}, {"name": "member-ports", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-arp:arp-security", "type": "container", "fields": [{"name": "l2proxy-enable", "type": "boolean", "default": false}]}, {"name": "huawei-mac:mac-addresss", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-vlan:vlan::vlans::vlan::member-ports::member-port", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interface-name", "type": "string", "is_config": false, "nullable": false}, {"name": "access-type", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "tag-mode", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "interface-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-vlan:vlan::vlans::vlanhuawei-mac:mac-addresss::mac-address", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "address", "type": "string", "nullable": false}, {"name": "mac-type", "type": "choice", "nullable": false, "fields": [{"name": "black-hole", "type": "case", "fields": [{"name": "black-hole", "type": "string", "nullable": false}]}, {"name": "static", "type": "case", "fields": [{"name": "out-interface-name", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface/name"}]}]}]}], "keys": [{"name": "k0", "fields": [":pid", "address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-arp", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-arp:arp", "type": "container", "fields": [{"name": "global", "type": "container", "fields": [{"name": "strict-learn-enable", "type": "boolean", "default": false}, {"name": "expire-time", "type": "uint32", "default": 1200}, {"name": "gateway-dup-enable", "type": "boolean", "default": false}]}, {"name": "query-entries", "type": "container", "is_config": false, "fields": []}, {"name": "statistics", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-arp:arp-entry-clear", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "clear-methods", "type": "choice", "is_config": false, "nullable": false, "fields": [{"name": "clear-types", "type": "case", "is_config": false, "fields": [{"name": "clear-type", "type": "string", "is_config": false}]}, {"name": "if-names", "type": "case", "is_config": false, "fields": [{"name": "if-name", "type": "string", "is_config": false, "nullable": false}, {"name": "ip-addr", "type": "string", "is_config": false}]}]}]}, {"name": "huawei-arp:arp-send-detect", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ipv4-address", "type": "string", "is_config": false, "nullable": false}, {"name": "mac-address", "type": "string", "is_config": false, "nullable": false}, {"name": "if-name", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-arp:arp::query-entries::query-entry", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ni-name", "type": "string", "is_config": false, "nullable": false}, {"name": "ip-addr", "type": "string", "is_config": false, "nullable": false}, {"name": "mac-addr", "type": "string", "is_config": false}, {"name": "style-type", "type": "string", "is_config": false}, {"name": "if-name", "type": "string", "is_config": false}, {"name": "pe-vlan", "type": "uint16", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "ni-name", "ip-addr"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-arp:arp::statistics::statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "packets-received", "type": "uint32", "is_config": false}, {"name": "miss-received", "type": "uint32", "is_config": false}, {"name": "learn-count", "type": "uint32", "is_config": false}, {"name": "packets-drop-limit", "type": "uint32", "is_config": false}, {"name": "packets-drop-other", "type": "uint32", "is_config": false}, {"name": "miss-drop-limit", "type": "uint32", "is_config": false}, {"name": "miss-drop-other", "type": "uint32", "is_config": false}, {"name": "packets-drop-speedlmt", "type": "uint32", "is_config": false}, {"name": "packets-proxy-supp", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "hua<PERSON>-mac", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-mac:mac", "type": "container", "fields": [{"name": "global-attribute", "type": "container", "fields": [{"name": "aging-time", "type": "uint32", "default": 300}]}, {"name": "global-mac-usage", "type": "container", "fields": [{"name": "mac-threshold", "type": "uint32", "default": 90}]}, {"name": "vlan-dynamic-macs", "type": "container", "is_config": false, "fields": []}, {"name": "mac-statistics", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-mac:reset-dynamic-macs-by-interface", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "interface-name", "type": "string", "is_config": false, "nullable": false}]}, {"name": "huawei-mac:reset-vlan-dynamic-macs", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "vlan-id", "type": "uint16", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-mac:mac::vlan-dynamic-macs::vlan-dynamic-mac", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "vlan-id", "type": "uint16", "is_config": false, "nullable": false}, {"name": "address", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false}, {"name": "out-interface-name", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot-id", "vlan-id", "address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-mac:mac::mac-statistics::mac-statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "total", "type": "uint32", "is_config": false}, {"name": "black-hole", "type": "uint32", "is_config": false}, {"name": "static", "type": "uint32", "is_config": false}, {"name": "dynamic", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-ifm:ifm", "type": "container", "fields": [{"name": "interfaces", "type": "container", "fields": []}]}, {"name": "huawei-ifm:reset-if-counters-by-name", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "if-name", "type": "string", "is_config": false, "nullable": false}]}, {"name": "huawei-ifm:reset-if-mib-counters-by-name", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "if-name", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string", "default": "main-interface"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string", "default": "up"}, {"name": "link-protocol", "type": "string"}, {"name": "bandwidth-type", "type": "choice", "fields": [{"name": "bandwidth-mbps", "type": "case", "fields": [{"name": "bandwidth", "type": "uint32"}]}, {"name": "bandwidth-kbps", "type": "case", "fields": [{"name": "bandwidth-kbps", "type": "uint32"}]}]}, {"name": "mtu", "type": "uint32", "clause": [{"type": "must", "formula": "not(../mtu and /yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface[name = current()/../name]/huawei-ethernet:ethernet/main-interface/l2-attribute)"}]}, {"name": "vrf-name", "type": "string", "default": "_public_", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-network-instance/huawei-network-instance:network-instance/instances/huawei-network-instance:network-instance::instances::instance/name"}]}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "dynamic", "type": "container", "is_config": false, "fields": [{"name": "oper-status", "type": "string", "is_config": false}, {"name": "physical-status", "type": "string", "is_config": false}, {"name": "link-status", "type": "string", "is_config": false}, {"name": "mtu", "type": "uint32", "is_config": false}, {"name": "bandwidth", "type": "uint64", "is_config": false}, {"name": "ipv4-status", "type": "string", "is_config": false}, {"name": "ipv6-status", "type": "string", "is_config": false}, {"name": "is-control-flap-damp", "type": "boolean", "is_config": false}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "is-offline", "type": "boolean", "is_config": false}, {"name": "link-quality-grade", "type": "string", "is_config": false}, {"name": "sub-if-counts", "type": "uint32", "is_config": false}]}, {"name": "mib-statistics", "type": "container", "is_config": false, "fields": [{"name": "receive-byte", "type": "uint64", "is_config": false}, {"name": "send-byte", "type": "uint64", "is_config": false}, {"name": "receive-packet", "type": "uint64", "is_config": false}, {"name": "send-packet", "type": "uint64", "is_config": false}, {"name": "receive-unicast-packet", "type": "uint64", "is_config": false}, {"name": "receive-multicast-packet", "type": "uint64", "is_config": false}, {"name": "receive-broad-packet", "type": "uint64", "is_config": false}, {"name": "send-unicast-packet", "type": "uint64", "is_config": false}, {"name": "send-multicast-packet", "type": "uint64", "is_config": false}, {"name": "send-broad-packet", "type": "uint64", "is_config": false}, {"name": "receive-error-packet", "type": "uint64", "is_config": false}, {"name": "receive-drop-packet", "type": "uint64", "is_config": false}, {"name": "send-error-packet", "type": "uint64", "is_config": false}, {"name": "send-drop-packet", "type": "uint64", "is_config": false}, {"name": "huawei-pic:eth-port-err-sts", "type": "container", "is_config": false, "fields": [{"name": "rx-crc", "type": "uint64", "is_config": false, "default": 0}]}]}, {"name": "common-statistics", "type": "container", "is_config": false, "fields": [{"name": "stati-interval", "type": "uint32", "is_config": false, "default": 300}, {"name": "in-byte-rate", "type": "uint64", "is_config": false}, {"name": "in-bit-rate", "type": "uint64", "is_config": false}, {"name": "in-packet-rate", "type": "uint64", "is_config": false}, {"name": "in-use-rate", "type": "string", "is_config": false}, {"name": "out-byte-rate", "type": "uint64", "is_config": false}, {"name": "out-bit-rate", "type": "uint64", "is_config": false}, {"name": "out-packet-rate", "type": "uint64", "is_config": false}, {"name": "out-use-rate", "type": "string", "is_config": false}, {"name": "receive-byte", "type": "uint64", "is_config": false}, {"name": "send-byte", "type": "uint64", "is_config": false}, {"name": "receive-packet", "type": "uint64", "is_config": false}, {"name": "send-packet", "type": "uint64", "is_config": false}, {"name": "receive-unicast-packet", "type": "uint64", "is_config": false}, {"name": "receive-multicast-packet", "type": "uint64", "is_config": false}, {"name": "receive-broad-packet", "type": "uint64", "is_config": false}, {"name": "send-unicast-packet", "type": "uint64", "is_config": false}, {"name": "send-multicast-packet", "type": "uint64", "is_config": false}, {"name": "send-broad-packet", "type": "uint64", "is_config": false}, {"name": "receive-error-packet", "type": "uint64", "is_config": false}, {"name": "receive-drop-packet", "type": "uint64", "is_config": false}, {"name": "send-error-packet", "type": "uint64", "is_config": false}, {"name": "send-drop-packet", "type": "uint64", "is_config": false}, {"name": "send-unicast-bit-rate", "type": "uint64", "is_config": false}, {"name": "receive-unicast-bit-rate", "type": "uint64", "is_config": false}, {"name": "send-multicast-bit-rate", "type": "uint64", "is_config": false}, {"name": "receive-multicast-bit-rate", "type": "uint64", "is_config": false}, {"name": "send-broad-bit-rate", "type": "uint64", "is_config": false}, {"name": "receive-broad-bit-rate", "type": "uint64", "is_config": false}, {"name": "send-unicast-packet-rate", "type": "uint64", "is_config": false}, {"name": "receive-unicast-packet-rate", "type": "uint64", "is_config": false}, {"name": "send-multicast-packet-rate", "type": "uint64", "is_config": false}, {"name": "receive-multicast-packet-rate", "type": "uint64", "is_config": false}, {"name": "send-broadcast-packet-rate", "type": "uint64", "is_config": false}, {"name": "receive-broadcast-packet-rate", "type": "uint64", "is_config": false}]}, {"name": "huawei-arp:arp-entry", "type": "container", "fields": [{"name": "expire-time", "type": "uint32"}, {"name": "arp-learn-disable", "type": "boolean"}, {"name": "route-proxy-enable", "type": "boolean"}, {"name": "inner-proxy-enable", "type": "boolean"}, {"name": "dest-mac-check", "type": "boolean"}, {"name": "src-mac-check", "type": "boolean"}]}, {"name": "huawei-dhcp:interface-ip-pool", "type": "container", "fields": [{"name": "select-type", "type": "choice", "fields": [{"name": "interface", "type": "case", "fields": [{"name": "select-interface", "type": "container", "presence": true, "fields": [{"name": "server-name", "type": "string"}, {"name": "domain-name", "type": "string"}, {"name": "next-server", "type": "string"}, {"name": "lease", "type": "container", "fields": [{"name": "time-type", "type": "choice", "fields": [{"name": "limited", "type": "case", "fields": [{"name": "day", "type": "uint16", "default": 1}, {"name": "hour", "type": "uint8", "default": 0}, {"name": "minute", "type": "uint8", "default": 0}]}, {"name": "unlimited", "type": "case", "fields": [{"name": "unlimited", "type": "string"}]}]}], "clause": [{"type": "must", "formula": "not(./time-type/limited/day = 0 and ./time-type/limited/hour = 0 and ./time-type/limited/minute = 0)"}]}, {"name": "dns-list", "type": "container", "fields": []}, {"name": "sip-server", "type": "container", "fields": [{"name": "sip-server-format", "type": "choice", "fields": [{"name": "ip-format", "type": "case", "fields": [{"name": "sip-server-ip1", "type": "string", "nullable": false}, {"name": "sip-server-ip2", "type": "string", "clause": [{"type": "must", "formula": "(../../../sip-server-format/ip-format/sip-server-ip1 != ../../../sip-server-format/ip-format/sip-server-ip2)"}]}]}, {"name": "string-format", "type": "case", "fields": [{"name": "sip-server-name1", "type": "string", "nullable": false}, {"name": "sip-server-name2", "type": "string", "clause": [{"type": "must", "formula": "(../../../sip-server-format/string-format/sip-server-name1 != ../../../sip-server-format/string-format/sip-server-name2)"}]}]}]}]}, {"name": "excluded-ip-addresses", "type": "container", "fields": []}, {"name": "static-binds", "type": "container", "fields": []}, {"name": "options", "type": "container", "fields": []}, {"name": "auto-recycle", "type": "container", "fields": [{"name": "day", "type": "uint16", "default": 0}, {"name": "hour", "type": "uint8", "default": 0}, {"name": "minute", "type": "uint8", "default": 0}]}, {"name": "ip-pool-statistics", "type": "container", "is_config": false, "fields": [{"name": "used-ip-count", "type": "uint32", "is_config": false}, {"name": "idle-ip-count", "type": "uint32", "is_config": false}, {"name": "expired-ip-count", "type": "uint32", "is_config": false}, {"name": "conflict-ip-count", "type": "uint32", "is_config": false}, {"name": "disable-ip-count", "type": "uint32", "is_config": false}, {"name": "total-ip-count", "type": "uint32", "is_config": false}, {"name": "start-ip", "type": "string", "is_config": false}, {"name": "end-ip", "type": "string", "is_config": false}]}], "clause": [{"type": "when", "formula": "../../../../huawei-ip:ipv4/address/common-address/addresses/huawei-ifm:ifm::interfaces::interfacehuawei-ip:ipv4::address::common-address::addresses::address/ip"}]}]}]}]}, {"name": "huawei-dhcp:dhcp-client-if", "type": "container", "fields": [{"name": "address-allocation", "type": "string"}, {"name": "client-status", "type": "container", "is_config": false, "fields": [{"name": "fsm-state", "type": "string", "is_config": false}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "ip-address", "type": "string", "is_config": false}, {"name": "ip-mask", "type": "string", "is_config": false}, {"name": "server-address", "type": "string", "is_config": false}, {"name": "lease-obtained-time", "type": "string", "is_config": false}, {"name": "lease-expire-time", "type": "string", "is_config": false}, {"name": "lease-renew-time", "type": "string", "is_config": false}, {"name": "lease-rebind-time", "type": "string", "is_config": false}]}, {"name": "client-statistics", "type": "container", "is_config": false, "fields": [{"name": "total-packets-received", "type": "uint32", "is_config": false}, {"name": "bootp-reply-packets-received", "type": "uint32", "is_config": false}, {"name": "offer-packets-received", "type": "uint32", "is_config": false}, {"name": "ack-packets-received", "type": "uint32", "is_config": false}, {"name": "nak-packets-received", "type": "uint32", "is_config": false}, {"name": "total-packets-send", "type": "uint32", "is_config": false}, {"name": "bootp-request-packets-send", "type": "uint32", "is_config": false}, {"name": "discovery-packets-send", "type": "uint32", "is_config": false}, {"name": "request-packets-send", "type": "uint32", "is_config": false}, {"name": "reboot-request-packets-send", "type": "uint32", "is_config": false}, {"name": "select-request-packets-send", "type": "uint32", "is_config": false}, {"name": "renew-request-packets-send", "type": "uint32", "is_config": false}, {"name": "rebind-request-packets-send", "type": "uint32", "is_config": false}, {"name": "decline-packets-send", "type": "uint32", "is_config": false}, {"name": "release-packets-send", "type": "uint32", "is_config": false}]}], "clause": [{"type": "must", "formula": "not(./address-allocation and /yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface[name = current()/../name]/huawei-ethernet:ethernet/main-interface/l2-attribute)"}, {"type": "must", "formula": "not(./address-allocation and count(/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface[name = current()/../name]/huawei-ip:ipv4/address/common-address/addresses/huawei-ifm:ifm::interfaces::interfacehuawei-ip:ipv4::address::common-address::addresses::address) > 0)"}]}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-vlan/huawei-vlan:vlan/vlans/huawei-vlan:vlan::vlans::vlan/id"}]}, {"name": "trunk-vlans", "type": "string", "clause": [{"type": "when", "formula": "../link-type = 'trunk' or ../link-type = 'hybrid'"}]}, {"name": "untag-vlans", "type": "string", "clause": [{"type": "when", "formula": "../link-type = 'hybrid'"}]}, {"name": "huawei-mstp:mstp-attribute", "type": "container", "fields": [{"name": "enable", "type": "boolean", "default": true}, {"name": "no-agreement-check", "type": "boolean", "default": false}, {"name": "root-protection", "type": "boolean", "default": false}, {"name": "bpdu-filter", "type": "string", "default": "default"}, {"name": "edge-port", "type": "string", "default": "default"}]}], "clause": [{"type": "when", "formula": "not(/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface/huawei-ifm-trunk:trunk/members/huawei-ifm:ifm::interfaces::interfacehuawei-ifm-trunk:trunk::members::member[name = current()/../../../name])"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}], "clause": [{"type": "when", "formula": "../../class = 'main-interface'"}]}]}, {"name": "huawei-if-standby:standby", "type": "container", "fields": [{"name": "primary-interface", "type": "container", "presence": true, "fields": [{"name": "switch-delay", "type": "container", "fields": [{"name": "primary-to-standby", "type": "uint16", "default": 5}, {"name": "standby-to-primary", "type": "uint16", "default": 5}]}, {"name": "loadbalance-threshold", "type": "container", "fields": [{"name": "upper-threshold", "type": "uint8"}, {"name": "lower-threshold", "type": "uint8"}], "clause": [{"type": "must", "formula": "(upper-threshold and lower-threshold and upper-threshold > lower-threshold) or (not(upper-threshold) and not(lower-threshold))"}]}, {"name": "backup-state", "type": "string", "is_config": false}, {"name": "standby-interfaces", "type": "container", "fields": []}]}], "clause": [{"type": "must", "formula": "not(./primary-interface and /yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface[name = current()/../name]/huawei-ethernet:ethernet/main-interface/l2-attribute)"}, {"type": "when", "formula": "../type = 'Ethernet' or ../type = 'GigabitEthernet' or ../type = '100GE' or ../type = '200GE' or ../type = '40GE' or ../type = '10GE' or ../type = 'GEBrief' or ../type = 'MultiGE' or ../type = '4x10GE' or ../type = '10x10GE' or ../type = '3x40GE' or ../type = '4x25GE' or ../type = '25GE' or ../type = 'XGigabitEthernet' or ../type = 'FlexE' or ../type = '50|100GE' or ../type = '50GE' or ../type = '400GE' or ../type = '1200GE' or ../type = 'Cellular' or ../type = 'Serial' or ../type = 'Dialer' or ../type = 'Tunnel'"}]}, {"name": "huawei-ifm-trunk:trunk", "type": "container", "fields": [{"name": "members", "type": "container", "fields": []}]}, {"name": "huawei-ip:ipv4", "type": "container", "fields": [{"name": "address", "type": "choice", "fields": [{"name": "common-address", "type": "case", "fields": [{"name": "addresses", "type": "container", "fields": [], "clause": [{"type": "must", "formula": "count(huawei-ifm:ifm::interfaces::interfacehuawei-ip:ipv4::address::common-address::addresses::address) = 0 or count(huawei-ifm:ifm::interfaces::interfacehuawei-ip:ipv4::address::common-address::addresses::address[type = 'main']) = 1"}, {"type": "must", "formula": "not(count(huawei-ifm:ifm::interfaces::interfacehuawei-ip:ipv4::address::common-address::addresses::address) > 0 and /yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface[name = current()/../../../../name]/huawei-ethernet:ethernet/main-interface/l2-attribute)"}]}]}, {"name": "negotiate-address", "type": "case", "fields": [{"name": "negotiation-address", "type": "container", "fields": [{"name": "negotiation-type", "type": "string", "nullable": false}]}]}]}, {"name": "state", "type": "container", "is_config": false, "fields": [{"name": "addresses", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-arp:static-arps", "type": "container", "fields": []}]}, {"name": "huawei-lldp:lldp", "type": "container", "fields": [{"name": "session", "type": "container", "fields": [{"name": "admin-status", "type": "string", "default": "tx-rx"}, {"name": "tlv-enable", "type": "container", "fields": [{"name": "management-address", "type": "boolean", "default": true}, {"name": "port-description", "type": "boolean", "default": true}, {"name": "system-capability", "type": "boolean", "default": true}, {"name": "system-description", "type": "boolean", "default": true}, {"name": "system-name", "type": "boolean", "default": true}, {"name": "mac-physic", "type": "boolean", "default": true, "clause": [{"type": "when", "formula": "(../../../../type != 'Pos') and (../../../../type != 'FlexE-50G') and (../../../../type != 'FlexE-100G') and (../../../../type != 'FlexE-200GE') and (../../../../type != 'FlexE-50|100G') and (../../../../type != 'FlexE-400G') and (../../../../type != 'FlexE-10G')"}]}, {"name": "link-aggregation", "type": "boolean", "default": true, "clause": [{"type": "when", "formula": "(../../../../type != 'Pos') and (../../../../type != 'FlexE-50G') and (../../../../type != 'FlexE-100G') and (../../../../type != 'FlexE-200GE') and (../../../../type != 'FlexE-50|100G') and (../../../../type != 'FlexE-400G') and (../../../../type != 'FlexE-10G')"}]}, {"name": "max-frame-size", "type": "boolean", "default": true, "clause": [{"type": "when", "formula": "(../../../../type != 'Pos') and (../../../../type != 'FlexE-50G') and (../../../../type != 'FlexE-100G') and (../../../../type != 'FlexE-200GE') and (../../../../type != 'FlexE-50|100G') and (../../../../type != 'FlexE-400G') and (../../../../type != 'FlexE-10G')"}]}], "clause": [{"type": "when", "formula": "not(../admin-status = 'disabled')"}]}, {"name": "local-info", "type": "container", "is_config": false, "fields": [{"name": "port-id-sub-type", "type": "string", "is_config": false}, {"name": "port-id", "type": "string", "is_config": false}, {"name": "port-description", "type": "string", "is_config": false}, {"name": "auto-negotiation-supported", "type": "string", "is_config": false}, {"name": "auto-negotiation-enabled", "type": "string", "is_config": false}, {"name": "auto-negotiation-capability", "type": "string", "is_config": false}, {"name": "oper-mau-type", "type": "string", "is_config": false}, {"name": "link-aggregation-supported", "type": "string", "is_config": false}, {"name": "link-aggregation-enabled", "type": "string", "is_config": false}, {"name": "aggregation-port-id", "type": "int32", "is_config": false}, {"name": "maximum-frame-size", "type": "int32", "is_config": false}]}, {"name": "neighbors", "type": "container", "is_config": false, "fields": []}, {"name": "statistics", "type": "container", "is_config": false, "fields": [{"name": "total-neighbors", "type": "uint32", "is_config": false}, {"name": "transmitted-frames", "type": "uint32", "is_config": false}, {"name": "received-frames", "type": "uint32", "is_config": false}, {"name": "discarded-frames", "type": "uint32", "is_config": false}, {"name": "error-frames", "type": "uint32", "is_config": false}, {"name": "discarded-tlvs", "type": "uint32", "is_config": false}, {"name": "unrecognized-tlvs", "type": "uint32", "is_config": false}, {"name": "expired-neighbors", "type": "uint32", "is_config": false}, {"name": "received-dcbx-tlvs", "type": "uint32", "is_config": false}, {"name": "received-med-tlvs", "type": "uint32", "is_config": false}, {"name": "received-network-card-id-tlvs", "type": "uint32", "is_config": false}]}]}], "clause": [{"type": "when", "formula": "/yang/ds[name=$DS_NAME]/huawei-lldp/huawei-lldp:lldp and ../class = 'main-interface' and (../type = 'MultiGE' or ../type = 'Ethernet' or ../type = 'GigabitEthernet' or ../type = '10GE')"}]}, {"name": "huawei-loadbalance:loadbalance", "type": "container", "fields": [{"name": "ucmp", "type": "container", "presence": true, "fields": [], "clause": [{"type": "when", "formula": "../../type = 'GigabitEthernet' or ../../type = 'Pos' or ../../type = '40GE' or ../../type = '100GE' or ../../type = 'XGigabitEthernet' or ../../type = 'ATM' or ../../type = '50|100GE' or ../../type = '50GE' or ../../type = '400GE' or ../../type = '25GE' or ../../type = '200GE' or ../../type = '10GE' or ../../type = 'FlexE' or ../../type = '4x10GE' or ../../type = '10x10GE' or ../../type = '3x40GE' or ../../type = '4x25GE' or ../../type = 'Ethernet'"}]}, {"name": "ucmp-weight", "type": "container", "fields": [{"name": "weight", "type": "uint32"}], "clause": [{"type": "when", "formula": "../../type = 'GigabitEthernet' or ../../type = 'Pos' or ../../type = '40GE' or ../../type = '100GE' or ../../type = 'XGigabitEthernet' or ../../type = 'ATM' or ../../type = '50|100GE' or ../../type = '50GE' or ../../type = '400GE' or ../../type = '25GE' or ../../type = '200GE' or ../../type = '10GE' or ../../type = 'FlexE' or ../../type = '4x10GE' or ../../type = '10x10GE' or ../../type = '3x40GE' or ../../type = '4x25GE' or ../../type = 'Ethernet' or ../../type = 'Vlanif' or ../../type = 'Eth-Trunk' or ../../type = 'Vbdif'"}]}], "clause": [{"type": "must", "formula": "not((./ucmp or ./ucmp-weight/weight) and /yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface[name = current()/../name]/huawei-ethernet:ethernet/main-interface/l2-attribute)"}, {"type": "must", "formula": "not(./ucmp or ./ucmp-weight/weight) or /yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface[name = current()/../name]/type = 'GigabitEthernet'"}]}, {"name": "huawei-nat:nat", "type": "container", "fields": [{"name": "nat-enable", "type": "boolean", "default": false}]}, {"name": "huawei-ppp-net:ppp-net", "type": "container", "fields": [{"name": "ppp", "type": "container", "fields": [{"name": "ppp-base", "type": "container", "fields": [{"name": "pap-authen-flag", "type": "string", "default": "disable", "clause": [{"type": "when", "formula": "../../../../type = 'Dialer' or ../../../../type = 'Serial' or ../../../../type = 'Pos' or ../../../../type = 'Pos-Trunk' or ../../../../type = 'Trunk-Serial' or ../../../../type = 'Lmpif' or ../../../../type = 'GigabitEthernet'"}]}, {"name": "chap-authen-flag", "type": "string", "default": "disable", "clause": [{"type": "when", "formula": "../../../../type = 'Dialer' or ../../../../type = 'Serial' or ../../../../type = 'Pos' or ../../../../type = 'Pos-Trunk' or ../../../../type = 'Trunk-Serial' or ../../../../type = 'Lmpif' or ../../../../type = 'GigabitEthernet'"}]}, {"name": "pap-user-name", "type": "string", "clause": [{"type": "must", "formula": "../pap-user-name and ../pap-password"}, {"type": "when", "formula": "../../../../type = 'Dialer' or ../../../../type = 'Serial' or ../../../../type = 'Pos' or ../../../../type = 'Pos-Trunk' or ../../../../type = 'Trunk-Serial' or ../../../../type = 'Lmpif' or ../../../../type = 'GigabitEthernet'"}]}, {"name": "pap-password", "type": "string", "clause": [{"type": "must", "formula": "../pap-user-name and ../pap-password"}, {"type": "when", "formula": "../../../../type = 'Dialer' or ../../../../type = 'Serial' or ../../../../type = 'Pos' or ../../../../type = 'Pos-Trunk' or ../../../../type = 'Trunk-Serial' or ../../../../type = 'Lmpif' or ../../../../type = 'GigabitEthernet'"}]}, {"name": "chap-user-name", "type": "string", "clause": [{"type": "when", "formula": "../../../../type = 'Dialer' or ../../../../type = 'Serial' or ../../../../type = 'Pos' or ../../../../type = 'Pos-Trunk' or ../../../../type = 'Trunk-Serial' or ../../../../type = 'Lmpif' or ../../../../type = 'GigabitEthernet'"}]}, {"name": "chap-password", "type": "string", "clause": [{"type": "when", "formula": "../../../../type = 'Dialer' or ../../../../type = 'Serial' or ../../../../type = 'Pos' or ../../../../type = 'Pos-Trunk' or ../../../../type = 'Trunk-Serial' or ../../../../type = 'Lmpif' or ../../../../type = 'GigabitEthernet'"}]}], "clause": [{"type": "must", "formula": "/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface[name = current()/../../../name]/link-protocol = 'ppp' and not(/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface[name = current()/../../../name]/huawei-dhcp:dhcp-client-if/address-allocation)"}, {"type": "must", "formula": "/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface[name = current()/../../../name]/link-protocol = 'ppp' and not(/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface[name = current()/../../../name]/huawei-ethernet:ethernet/main-interface/l2-attribute)"}, {"type": "must", "formula": "/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface[name = current()/../../../name]/link-protocol = 'ppp' and not(/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface[name = current()/../../../name]/type = 'Vlanif')"}, {"type": "must", "formula": "/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface[name = current()/../../../name]/link-protocol = 'ppp' and not(count(/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface[name = current()/../../../name]/huawei-ip:ipv4/address/common-address/addresses/huawei-ifm:ifm::interfaces::interfacehuawei-ip:ipv4::address::common-address::addresses::address) > 0)"}]}, {"name": "ppp-link-info", "type": "container", "is_config": false, "fields": [{"name": "peer-mru", "type": "uint32", "is_config": false}, {"name": "magic-number", "type": "uint32", "is_config": false}, {"name": "local-ip-address", "type": "string", "is_config": false}, {"name": "peer-ip-address", "type": "string", "is_config": false}, {"name": "phase", "type": "string", "is_config": false}, {"name": "primary-dns-address", "type": "string", "is_config": false}, {"name": "second-dns-address", "type": "string", "is_config": false}, {"name": "terminate-cause", "type": "string", "is_config": false}, {"name": "lcp-sent", "type": "uint32", "is_config": false}, {"name": "lcp-recieved", "type": "uint32", "is_config": false}, {"name": "ipcp-sent", "type": "uint32", "is_config": false}, {"name": "ipcp-recieved", "type": "uint32", "is_config": false}, {"name": "ip6cp-sent", "type": "uint32", "is_config": false}, {"name": "ip6cp-recieved", "type": "uint32", "is_config": false}, {"name": "drop-packets", "type": "uint32", "is_config": false}, {"name": "keepalive-packets", "type": "uint32", "is_config": false}]}], "clause": [{"type": "when", "formula": "../../link-protocol = 'ppp'"}]}]}, {"name": "huawei-pppoe-client:pppoe-client-session-summarys", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-sacl:traffic-filter-applys", "type": "container", "fields": []}, {"name": "huawei-sacl:traffic-statistics-applys", "type": "container", "fields": []}, {"name": "huawei-sacl:traffic-remark-applys", "type": "container", "fields": []}, {"name": "huawei-sacl:traffic-limit-applys", "type": "container", "fields": []}, {"name": "huawei-sacl:traffic-redirect-applys", "type": "container", "fields": []}, {"name": "huawei-storm-control:storm-control", "type": "container", "fields": [{"name": "storm-rates", "type": "container", "fields": []}, {"name": "storm-control-action", "type": "container", "fields": [{"name": "action", "type": "string"}]}]}, {"name": "huawei-storm-control:storm-suppress", "type": "container", "fields": [{"name": "storm-rates", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::gateway-list", "type": "leaf-list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "gateway-list", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "gateway-list"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::dns-list::ip-address", "type": "leaf-list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ip-address", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ip-address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::excluded-ip-addresses::excluded-ip-address", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "start-ip-address", "type": "string", "nullable": false}, {"name": "end-ip-address", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "start-ip-address", "end-ip-address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::static-binds::static-bind", "type": "list", "max-elements": 512, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "static-bind-ip", "type": "string", "nullable": false}, {"name": "static-bind-mac", "type": "string", "nullable": false}, {"name": "description", "type": "string"}], "keys": [{"name": "k0", "fields": [":pid", "static-bind-ip"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "type": "list", "max-elements": 32, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "option-code", "type": "uint32", "nullable": false}, {"name": "option-format", "type": "choice", "fields": [{"name": "ip-format", "type": "case", "fields": []}, {"name": "ascii-format", "type": "case", "fields": [{"name": "ascii-string", "type": "string"}]}, {"name": "hex-format", "type": "case", "fields": [{"name": "hex-string", "type": "string"}]}, {"name": "sub-options-format", "type": "case", "fields": [{"name": "sub-options", "type": "container", "fields": []}]}]}], "keys": [{"name": "k0", "fields": [":pid", "option-code"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::ip-format::ip-addresses", "type": "leaf-list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ip-addresses", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ip-addresses"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option", "type": "list", "max-elements": 32, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "sub-option-code", "type": "uint32", "nullable": false}, {"name": "option-format", "type": "choice", "fields": [{"name": "sub-ip-format", "type": "case", "fields": []}, {"name": "sub-ascii-format", "type": "case", "fields": [{"name": "ascii-string", "type": "string"}]}, {"name": "sub-hex-format", "type": "case", "fields": [{"name": "hex-string", "type": "string"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "sub-option-code"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option::option-format::sub-ip-format::ip-addresses", "type": "leaf-list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ip-addresses", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ip-addresses"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-ethernet:ethernet::main-interface::port-isolate-groups::port-isolate-group", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "group-id", "type": "int32", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "group-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}], "clause": [{"type": "must", "formula": "../../l2-attribute"}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-if-standby:standby::primary-interface::standby-interfaces::standby-interface", "type": "list", "min-elements": 1, "max-elements": 3, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface/name"}]}, {"name": "priority", "type": "uint8"}, {"name": "backup-state", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}], "clause": [{"type": "must", "formula": "not(./name and /yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface[name = current()/name]/huawei-ethernet:ethernet/main-interface/l2-attribute)"}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-ifm-trunk:trunk::members::member", "type": "list", "max-elements": 256, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface/name"}]}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-ip:ipv4::address::common-address::addresses::address", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ip", "type": "string", "nullable": false}, {"name": "mask", "type": "string", "nullable": false}, {"name": "type", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ip"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-ip:ipv4::state::addresses::address", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ip", "type": "string", "is_config": false, "nullable": false}, {"name": "mask", "type": "string", "is_config": false}, {"name": "type", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "ip"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-ip:ipv4huawei-arp:static-arps::static-arp", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ip-addr", "type": "string", "nullable": false}, {"name": "mac-addr", "type": "string", "nullable": false}, {"name": "pevid", "type": "uint16"}], "keys": [{"name": "k0", "fields": [":pid", "ip-addr"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint32", "is_config": false, "nullable": false}, {"name": "chassis-id-sub-type", "type": "string", "is_config": false}, {"name": "chassis-id", "type": "string", "is_config": false}, {"name": "port-id-sub-type", "type": "string", "is_config": false}, {"name": "port-id", "type": "string", "is_config": false}, {"name": "port-description", "type": "string", "is_config": false}, {"name": "system-name", "type": "string", "is_config": false}, {"name": "system-description", "type": "string", "is_config": false}, {"name": "system-capabilities-enabled", "type": "string", "is_config": false}, {"name": "system-capabilities-supported", "type": "string", "is_config": false}, {"name": "expired-time", "type": "int32", "is_config": false}, {"name": "port-vlan-id", "type": "int32", "is_config": false}, {"name": "protocol-identity", "type": "string", "is_config": false}, {"name": "auto-negotiation-supported", "type": "string", "is_config": false}, {"name": "auto-negotiation-enabled", "type": "string", "is_config": false}, {"name": "auto-negotiation-capability", "type": "string", "is_config": false}, {"name": "oper-mau-type", "type": "string", "is_config": false}, {"name": "link-aggregation-supported", "type": "string", "is_config": false}, {"name": "link-aggregation-enabled", "type": "string", "is_config": false}, {"name": "aggregation-port-id", "type": "int32", "is_config": false}, {"name": "maximum-frame-size", "type": "int32", "is_config": false}, {"name": "discovered-time", "type": "string", "is_config": false}, {"name": "management-addresss", "type": "container", "is_config": false, "fields": []}, {"name": "protocol-vlans", "type": "container", "is_config": false, "fields": []}, {"name": "vlan-names", "type": "container", "is_config": false, "fields": []}, {"name": "unknown-tlvs", "type": "container", "is_config": false, "fields": []}, {"name": "unknown-organizationally-defined-tlvs", "type": "container", "is_config": false, "fields": []}, {"name": "power", "type": "container", "is_config": false, "fields": [{"name": "port-class", "type": "string", "is_config": false}, {"name": "pse-support", "type": "string", "is_config": false}, {"name": "pse-state", "type": "string", "is_config": false}, {"name": "pse-pairs-control-ability", "type": "string", "is_config": false}, {"name": "pse-pairs", "type": "string", "is_config": false}, {"name": "classification", "type": "string", "is_config": false}, {"name": "type", "type": "string", "is_config": false}, {"name": "pd-source", "type": "string", "is_config": false}, {"name": "pse-source", "type": "string", "is_config": false}, {"name": "priority", "type": "string", "is_config": false}, {"name": "pd-requested-value", "type": "string", "is_config": false}, {"name": "pse-allocated-value", "type": "string", "is_config": false}, {"name": "pd-requested-mode-a-value", "type": "string", "is_config": false}, {"name": "pd-requested-mode-b-value", "type": "string", "is_config": false}, {"name": "pse-allocated-mode-a-value", "type": "string", "is_config": false}, {"name": "pse-allocated-mode-b-value", "type": "string", "is_config": false}, {"name": "pse-powering-status", "type": "string", "is_config": false}, {"name": "pd-powered-status", "type": "string", "is_config": false}, {"name": "pairsx", "type": "string", "is_config": false}, {"name": "class", "type": "string", "is_config": false}, {"name": "class-ext-mode-a", "type": "string", "is_config": false}, {"name": "class-ext-mode-b", "type": "string", "is_config": false}, {"name": "class-ext", "type": "string", "is_config": false}, {"name": "typex", "type": "string", "is_config": false}, {"name": "type-ext", "type": "string", "is_config": false}, {"name": "pd-4pid", "type": "string", "is_config": false}, {"name": "pd-load", "type": "string", "is_config": false}, {"name": "pse-max-available", "type": "string", "is_config": false}, {"name": "pse-autoclass-support", "type": "string", "is_config": false}, {"name": "autoclass-completed", "type": "string", "is_config": false}, {"name": "autoclass-request", "type": "string", "is_config": false}, {"name": "power-down", "type": "boolean", "is_config": false}, {"name": "power-down-time", "type": "uint32", "is_config": false}]}, {"name": "identity-tlv", "type": "container", "is_config": false, "fields": [{"name": "identity", "type": "string", "is_config": false}]}, {"name": "med-tlv", "type": "container", "is_config": false, "fields": [{"name": "capability", "type": "container", "is_config": false, "fields": [{"name": "device-type", "type": "string", "is_config": false}]}, {"name": "network-policys", "type": "container", "is_config": false, "fields": []}, {"name": "extended-power", "type": "container", "is_config": false, "fields": [{"name": "type", "type": "string", "is_config": false}, {"name": "pse-source", "type": "string", "is_config": false}, {"name": "pd-source", "type": "string", "is_config": false}, {"name": "priority", "type": "string", "is_config": false}, {"name": "value", "type": "string", "is_config": false}]}]}, {"name": "legacy-power-capability", "type": "container", "is_config": false, "fields": [{"name": "mode", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::management-addresss::management-address", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "value", "type": "string", "is_config": false, "nullable": false}, {"name": "length", "type": "int32", "is_config": false, "nullable": false}, {"name": "if-sub-type", "type": "string", "is_config": false}, {"name": "if-id", "type": "int32", "is_config": false}, {"name": "oid", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "type", "value", "length"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::protocol-vlans::protocol-vlan", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "id", "type": "int32", "is_config": false, "nullable": false}, {"name": "supported", "type": "string", "is_config": false}, {"name": "enabled", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::vlan-names::vlan-name", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "id", "type": "int32", "is_config": false, "nullable": false}, {"name": "value", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::unknown-tlvs::unknown-tlv", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "int32", "is_config": false, "nullable": false}, {"name": "info", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::unknown-organizationally-defined-tlvs::unknown-organizationally-defined-tlv", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "oui", "type": "string", "is_config": false, "nullable": false}, {"name": "sub-type", "type": "int32", "is_config": false, "nullable": false}, {"name": "index", "type": "int32", "is_config": false, "nullable": false}, {"name": "info", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "oui", "sub-type", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::med-tlv::capability::capabilities", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "capabilities", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "capabilities"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::med-tlv::network-policys::network-policy", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "unknown-policy", "type": "string", "is_config": false}, {"name": "vlan-tagged", "type": "boolean", "is_config": false}, {"name": "vlan-id", "type": "uint16", "is_config": false}, {"name": "cos", "type": "uint8", "is_config": false}, {"name": "dscp", "type": "uint8", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::legacy-power-capability::capability", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "capability", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "capability"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-pppoe-client:pppoe-client-session-summarys::pppoe-client-session-summary", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "session-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "if-name", "type": "string", "is_config": false}, {"name": "client-mac", "type": "string", "is_config": false}, {"name": "server-mac", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "session-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-filter-applys::traffic-filter-apply", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "acl-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "direction"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "type": "list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "acl", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-acl/huawei-acl:acl/groups/huawei-acl:acl::groups::group/identity"}]}, {"name": "enable-statistic", "type": "boolean", "default": false}, {"name": "statistics", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "acl"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance::statistics::statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "rule", "type": "string", "is_config": false, "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "apply-status", "type": "string", "is_config": false}, {"name": "match-permit-packet", "type": "uint64", "is_config": false}, {"name": "match-permit-byte", "type": "uint64", "is_config": false}, {"name": "match-discarded-packet", "type": "uint64", "is_config": false}, {"name": "match-discarded-byte", "type": "uint64", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "rule", "slot"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-statistics-applys::traffic-statistics-apply", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "acl-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "direction"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance", "type": "list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "acl", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-acl/huawei-acl:acl/groups/huawei-acl:acl::groups::group/identity"}]}, {"name": "statistics", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "acl"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance::statistics::statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "rule", "type": "string", "is_config": false, "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "apply-status", "type": "string", "is_config": false}, {"name": "match-packet", "type": "uint64", "is_config": false}, {"name": "match-byte", "type": "uint64", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "rule", "slot"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-remark-applys::traffic-remark-apply", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "acl-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "direction"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "type": "list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "acl", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-acl/huawei-acl:acl/groups/huawei-acl:acl::groups::group/identity"}]}, {"name": "dot1p-value", "type": "uint8", "clause": [{"type": "must", "formula": "not(../dscp-value)"}]}, {"name": "dscp-value", "type": "uint8", "clause": [{"type": "must", "formula": "not(../dot1p-value)"}]}, {"name": "statuses", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "acl"], "index": {"type": "primary"}, "constraints": {"unique": true}}], "clause": [{"type": "must", "formula": "(dscp-value or dot1p-value)"}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance::statuses::status", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "rule", "type": "string", "is_config": false, "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "apply-status", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "rule", "slot"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-limit-applys::traffic-limit-apply", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "acl-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "direction"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance", "type": "list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "acl", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-acl/huawei-acl:acl/groups/huawei-acl:acl::groups::group/identity"}]}, {"name": "cir", "type": "uint32", "nullable": false}, {"name": "pir", "type": "uint32", "clause": [{"type": "must", "formula": "../pir >= ../cir"}]}, {"name": "cbs", "type": "uint32"}, {"name": "pbs", "type": "uint32"}, {"name": "green", "type": "container", "fields": [{"name": "action", "type": "choice", "fields": [{"name": "drop", "type": "case", "fields": [{"name": "drop", "type": "container", "presence": true, "fields": []}]}, {"name": "pass", "type": "case", "fields": [{"name": "pass", "type": "container", "presence": true, "fields": [{"name": "remark-dscp", "type": "uint8"}]}]}]}]}, {"name": "yellow", "type": "container", "fields": [{"name": "action", "type": "choice", "fields": [{"name": "drop", "type": "case", "fields": [{"name": "drop", "type": "container", "presence": true, "fields": []}]}, {"name": "pass", "type": "case", "fields": [{"name": "pass", "type": "container", "presence": true, "fields": [{"name": "remark-dscp", "type": "uint8"}]}]}]}]}, {"name": "red", "type": "container", "fields": [{"name": "action", "type": "choice", "fields": [{"name": "drop", "type": "case", "fields": [{"name": "drop", "type": "container", "presence": true, "fields": []}]}, {"name": "pass", "type": "case", "fields": [{"name": "pass", "type": "container", "presence": true, "fields": [{"name": "remark-dscp", "type": "uint8"}]}]}]}]}, {"name": "statuses", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "acl"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance::statuses::status", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "rule", "type": "string", "is_config": false, "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "apply-status", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "rule", "slot"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-redirect-applys::traffic-redirect-apply", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "acl-instances", "type": "container", "fields": [], "clause": [{"type": "must", "formula": "(../direction = 'inbound')"}]}], "keys": [{"name": "k0", "fields": [":pid", "direction"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "type": "list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "acl", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-acl/huawei-acl:acl/groups/huawei-acl:acl::groups::group/identity"}]}, {"name": "direction", "type": "choice", "fields": [{"name": "interface", "type": "case", "fields": [{"name": "interface-name", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface/name"}]}]}, {"name": "nexthop", "type": "case", "fields": [{"name": "ip-nexthop", "type": "string"}]}]}, {"name": "statuses", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "acl"], "index": {"type": "primary"}, "constraints": {"unique": true}}], "clause": [{"type": "must", "formula": "(direction/nexthop/ip-nexthop or direction/interface/interface-name)"}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance::statuses::status", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "rule", "type": "string", "is_config": false, "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "apply-status", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "rule", "slot"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-storm-control:storm-control::storm-rates::storm-rate", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "packet-type", "type": "string", "nullable": false, "clause": [{"type": "must", "formula": "../rate-type/packet/pps-min-rate or ../rate-type/percent/per-min-rate or ../rate-type/cir/cir-min-rate"}]}, {"name": "rate-type", "type": "choice", "fields": [{"name": "packet", "type": "case", "fields": [{"name": "pps-min-rate", "type": "uint32", "clause": [{"type": "must", "formula": "../../../rate-type/packet/pps-max-rate and (../../../rate-type/packet/pps-min-rate < ../../../rate-type/packet/pps-max-rate)"}]}, {"name": "pps-max-rate", "type": "uint32", "clause": [{"type": "must", "formula": "../../../rate-type/packet/pps-min-rate and (../../../rate-type/packet/pps-max-rate > ../../../rate-type/packet/pps-min-rate)"}]}]}, {"name": "cir", "type": "case", "fields": [{"name": "cir-min-rate", "type": "uint32", "clause": [{"type": "must", "formula": "../../../rate-type/cir/cir-max-rate and (../../../rate-type/cir/cir-min-rate < ../../../rate-type/cir/cir-max-rate)"}]}, {"name": "cir-max-rate", "type": "uint32", "clause": [{"type": "must", "formula": "(../../../rate-type/cir/cir-max-rate > ../../../rate-type/cir/cir-min-rate) and ../../../rate-type/cir/cir-min-rate"}]}]}, {"name": "percent", "type": "case", "fields": [{"name": "per-min-rate", "type": "uint32", "clause": [{"type": "must", "formula": "../../../rate-type/percent/per-max-rate and (../../../rate-type/percent/per-min-rate < ../../../rate-type/percent/per-max-rate)"}]}, {"name": "per-max-rate", "type": "uint32", "clause": [{"type": "must", "formula": "../../../rate-type/percent/per-min-rate and (../../../rate-type/percent/per-max-rate > ../../../rate-type/percent/per-min-rate)"}]}]}]}], "keys": [{"name": "k0", "fields": [":pid", "packet-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-storm-control:storm-suppress::storm-rates::storm-rate", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "packet-type", "type": "string", "nullable": false, "clause": [{"type": "must", "formula": "../rate-type/packet/pps or ../rate-type/rate/cir or ../rate-type/percent/percent or (../rate-type/rate/cir and ../rate-type/rate/cbs)"}]}, {"name": "rate-type", "type": "choice", "fields": [{"name": "packet", "type": "case", "fields": [{"name": "pps", "type": "uint32"}]}, {"name": "rate", "type": "case", "fields": [{"name": "cir", "type": "uint32"}, {"name": "cbs", "type": "uint32", "clause": [{"type": "must", "formula": "../../../rate-type/rate/cir"}]}]}, {"name": "percent", "type": "case", "fields": [{"name": "percent", "type": "uint32"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "packet-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-dhcp", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-dhcp:dhcp", "type": "container", "fields": [{"name": "common", "type": "container", "fields": [{"name": "global", "type": "container", "fields": [{"name": "enable", "type": "boolean", "default": false}]}]}, {"name": "server", "type": "container", "fields": [{"name": "common", "type": "container", "fields": [{"name": "ping-packet-nub", "type": "uint32", "default": 2}, {"name": "ping-packet-timeout", "type": "uint32", "default": 500}, {"name": "bootp-enable", "type": "boolean", "default": true}, {"name": "bootp-auto-enable", "type": "boolean", "default": true}]}, {"name": "packet-statistics", "type": "container", "is_config": false, "fields": [{"name": "client-request-count", "type": "uint32", "is_config": false}, {"name": "discover-count", "type": "uint32", "is_config": false}, {"name": "request-count", "type": "uint32", "is_config": false}, {"name": "decline-count", "type": "uint32", "is_config": false}, {"name": "release-count", "type": "uint32", "is_config": false}, {"name": "inform-count", "type": "uint32", "is_config": false}, {"name": "server-reply-count", "type": "uint32", "is_config": false}, {"name": "offer-count", "type": "uint32", "is_config": false}, {"name": "ack-count", "type": "uint32", "is_config": false}, {"name": "nak-count", "type": "uint32", "is_config": false}, {"name": "force-renew-count", "type": "uint32", "is_config": false}, {"name": "bad-message-count", "type": "uint32", "is_config": false}, {"name": "bootp-request-count", "type": "uint32", "is_config": false}, {"name": "bootp-reply-count", "type": "uint32", "is_config": false}]}, {"name": "ip-pool-querys", "type": "container", "is_config": false, "fields": []}]}]}, {"name": "huawei-dhcp:reset-client-statistics", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "if-name", "type": "string", "is_config": false}]}, {"name": "huawei-dhcp:reset-server-statistics", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "huawei-dhcp:reset-interface-pool-address-state", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "if-name", "type": "string", "is_config": false, "nullable": false}, {"name": "start-ip-address", "type": "string", "is_config": false, "nullable": false, "clause": [{"type": "when", "formula": "../reset-flag = 'ip'"}]}, {"name": "end-ip-address", "type": "string", "is_config": false, "clause": [{"type": "when", "formula": "../reset-flag = 'ip'"}]}, {"name": "reset-flag", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-dhcp:dhcp::server::ip-pool-querys::ip-pool-query", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "pool-name", "type": "string", "is_config": false, "nullable": false}, {"name": "ip-address", "type": "string", "is_config": false, "nullable": false}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "lease-time", "type": "uint32", "is_config": false}, {"name": "status", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "pool-name", "ip-address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-mstp", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-mstp:mstp", "type": "container", "fields": [{"name": "global", "type": "container", "fields": [{"name": "bpdu-filter", "type": "boolean", "default": false}, {"name": "edge-port", "type": "boolean", "default": false}]}, {"name": "default-process", "type": "container", "fields": [{"name": "enable", "type": "boolean", "default": true}, {"name": "mode", "type": "string", "default": "rstp"}, {"name": "path-cost-standard", "type": "string", "default": "dot1t"}, {"name": "timer-factor", "type": "uint8", "default": 3}, {"name": "forward-delay", "type": "uint16", "default": 1500, "clause": [{"type": "must", "formula": "(../forward-delay) mod 100 = 0"}]}, {"name": "hello-time", "type": "uint16", "default": 200, "clause": [{"type": "must", "formula": "(../hello-time) mod 100 = 0"}]}, {"name": "max-age", "type": "uint16", "default": 2000, "clause": [{"type": "must", "formula": "(../max-age) mod 100 = 0"}]}, {"name": "default-instance", "type": "container", "fields": [{"name": "priority", "type": "uint32", "default": 32768, "clause": [{"type": "must", "formula": "(../priority) mod 4096 = 0"}, {"type": "when", "formula": "../root-type = 'normal'"}]}, {"name": "root-type", "type": "string", "default": "normal"}]}, {"name": "cist-info", "type": "container", "is_config": false, "fields": [{"name": "bridge-id", "type": "string", "is_config": false}, {"name": "root-id", "type": "string", "is_config": false}, {"name": "erpc-cost", "type": "uint32", "is_config": false}, {"name": "regroot-id", "type": "string", "is_config": false}, {"name": "irpc-cost", "type": "uint32", "is_config": false}, {"name": "active-hello-time", "type": "uint16", "is_config": false}, {"name": "active-max-age", "type": "uint16", "is_config": false}, {"name": "active-forward-delay", "type": "uint16", "is_config": false}, {"name": "active-max-hops", "type": "uint16", "is_config": false}, {"name": "root-port-id", "type": "string", "is_config": false}, {"name": "tc-tcn-receive-num", "type": "uint32", "is_config": false}, {"name": "tc-count-pre-hello", "type": "uint32", "is_config": false}, {"name": "last-change-interval", "type": "string", "is_config": false}, {"name": "tc-number", "type": "uint32", "is_config": false}, {"name": "is-topo-changed", "type": "boolean", "is_config": false}, {"name": "cist-port-infos", "type": "container", "is_config": false, "fields": []}]}, {"name": "interface-bpdu-statistics", "type": "container", "is_config": false, "fields": []}]}, {"name": "error-packet-statistic", "type": "container", "is_config": false, "fields": [{"name": "time", "type": "string", "is_config": false}, {"name": "count", "type": "uint32", "is_config": false}, {"name": "content", "type": "string", "is_config": false}]}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-mstp:mstp::default-process::cist-info::cist-port-infos::cist-port-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interface-name", "type": "string", "is_config": false, "nullable": false}, {"name": "port-role", "type": "string", "is_config": false}, {"name": "port-state", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "interface-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-mstp:mstp::default-process::interface-bpdu-statistics::interface-bpdu-statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "instance-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "port-name", "type": "string", "is_config": false, "nullable": false}, {"name": "tc-send", "type": "uint32", "is_config": false}, {"name": "tc-receive", "type": "uint32", "is_config": false}, {"name": "tc-discard", "type": "uint32", "is_config": false}, {"name": "tcn-send", "type": "uint32", "is_config": false}, {"name": "tcn-receive", "type": "uint32", "is_config": false}, {"name": "tcn-discard", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "instance-id", "port-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm-trunk", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-ifm-trunk:ifm-trunk", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-lldp", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-lldp:lldp", "type": "container", "presence": true, "fields": [{"name": "global-attribute", "type": "container", "fields": [{"name": "message-transmission-interval", "type": "int32", "default": 30, "clause": [{"type": "must", "formula": "../message-transmission-interval >= (../message-transmission-delay * 4)"}]}, {"name": "message-transmission-delay", "type": "int32", "default": 2, "clause": [{"type": "must", "formula": "../message-transmission-interval >= (../message-transmission-delay * 4)"}]}, {"name": "message-transmission-hold-multiplier", "type": "int32", "default": 4}, {"name": "restart-delay", "type": "int32", "default": 2}]}, {"name": "local-info", "type": "container", "is_config": false, "fields": [{"name": "chassis-id-sub-type", "type": "string", "is_config": false}, {"name": "chassis-id", "type": "string", "is_config": false}, {"name": "system-name", "type": "string", "is_config": false}, {"name": "system-description", "type": "string", "is_config": false}, {"name": "up-time", "type": "string", "is_config": false}, {"name": "management-addresss", "type": "container", "is_config": false, "fields": []}]}]}, {"name": "huawei-lldp:clear-lldp-neighbor", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "if-name", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-lldp:lldp::local-info::system-capabilities-supported", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "system-capabilities-supported", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "system-capabilities-supported"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-lldp:lldp::local-info::system-capabilities-enabled", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "system-capabilities-enabled", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "system-capabilities-enabled"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-lldp:lldp::local-info::management-addresss::management-address", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "value", "type": "string", "is_config": false, "nullable": false}, {"name": "length", "type": "int32", "is_config": false, "nullable": false}, {"name": "if-sub-type", "type": "string", "is_config": false}, {"name": "if-id", "type": "int32", "is_config": false}, {"name": "oid", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "type", "value", "length"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-loadbalance", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-loadbalance:loadbalance", "type": "container", "fields": [{"name": "global-attribute", "type": "container", "fields": [{"name": "unequal-cost", "type": "choice", "fields": [{"name": "ucmp", "type": "case", "default": true, "fields": [{"name": "ucmp-enable", "type": "boolean", "default": false}]}, {"name": "ucmp-bandwidth-config", "type": "case", "fields": [{"name": "ucmp-bandwidth-config-enable", "type": "string"}]}]}]}, {"name": "ip-load-balance", "type": "container", "fields": [{"name": "mode", "type": "string", "default": "flow"}, {"name": "src-ip", "type": "boolean", "default": true, "clause": [{"type": "when", "formula": "(../mode = 'flow')"}]}, {"name": "dst-ip", "type": "boolean", "default": true, "clause": [{"type": "when", "formula": "(../mode = 'flow')"}]}, {"name": "src-port", "type": "boolean", "default": false, "clause": [{"type": "when", "formula": "(../mode = 'flow')"}]}, {"name": "dst-port", "type": "boolean", "default": false, "clause": [{"type": "when", "formula": "(../mode = 'flow')"}]}], "clause": [{"type": "must", "formula": "mode = 'flow' and not(src-ip = false() and dst-ip = false() and src-port = false() and dst-port = false())"}]}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pppoe-client", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-pppoe-client:reset-pppoe-client-session", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "session", "type": "choice", "is_config": false, "fields": [{"name": "all-session", "type": "case", "is_config": false, "fields": [{"name": "all", "type": "boolean", "is_config": false, "nullable": false}]}, {"name": "specified-interface-session", "type": "case", "is_config": false, "fields": [{"name": "interface", "type": "string", "is_config": false, "nullable": false}]}]}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sacl", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-sacl:sacl", "type": "container", "fields": [{"name": "traffic-filter-applys", "type": "container", "fields": []}, {"name": "traffic-remark-applys", "type": "container", "fields": []}, {"name": "traffic-limit-applys", "type": "container", "fields": []}, {"name": "traffic-statistics-applys", "type": "container", "fields": []}, {"name": "traffic-redirect-applys", "type": "container", "fields": []}]}, {"name": "huawei-sacl:clear-traffic-apply-statistic", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "acl", "type": "string", "is_config": false, "nullable": false}, {"name": "rule", "type": "string", "is_config": false}, {"name": "application-type", "type": "string", "is_config": false, "nullable": false}, {"name": "application-view", "type": "choice", "is_config": false, "fields": [{"name": "interface", "type": "case", "is_config": false, "fields": [{"name": "interface-name", "type": "string", "is_config": false, "nullable": false}]}, {"name": "global", "type": "case", "is_config": false, "fields": [{"name": "global", "type": "string", "is_config": false, "nullable": false}]}]}, {"name": "direction", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sacl:sacl::traffic-filter-applys::traffic-filter-apply", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "acl-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "direction"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sacl:sacl::traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "type": "list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "acl", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-acl/huawei-acl:acl/groups/huawei-acl:acl::groups::group/identity"}]}, {"name": "enable-statistic", "type": "boolean", "default": false}, {"name": "statistics", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "acl"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sacl:sacl::traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance::statistics::statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "rule", "type": "string", "is_config": false, "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "apply-status", "type": "string", "is_config": false}, {"name": "match-permit-packet", "type": "uint64", "is_config": false}, {"name": "match-permit-byte", "type": "uint64", "is_config": false}, {"name": "match-discarded-packet", "type": "uint64", "is_config": false}, {"name": "match-discarded-byte", "type": "uint64", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "rule", "slot"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sacl:sacl::traffic-remark-applys::traffic-remark-apply", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "acl-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "direction"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sacl:sacl::traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "type": "list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "acl", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-acl/huawei-acl:acl/groups/huawei-acl:acl::groups::group/identity"}]}, {"name": "dot1p-value", "type": "uint8", "clause": [{"type": "must", "formula": "not(../dscp-value)"}]}, {"name": "dscp-value", "type": "uint8", "clause": [{"type": "must", "formula": "not(../dot1p-value)"}]}, {"name": "statuses", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "acl"], "index": {"type": "primary"}, "constraints": {"unique": true}}], "clause": [{"type": "must", "formula": "(dscp-value or dot1p-value)"}]}, {"name": "huawei-sacl:sacl::traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance::statuses::status", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "rule", "type": "string", "is_config": false, "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "apply-status", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "rule", "slot"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sacl:sacl::traffic-limit-applys::traffic-limit-apply", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "acl-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "direction"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sacl:sacl::traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance", "type": "list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "acl", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-acl/huawei-acl:acl/groups/huawei-acl:acl::groups::group/identity"}]}, {"name": "cir", "type": "uint32", "nullable": false}, {"name": "pir", "type": "uint32", "clause": [{"type": "must", "formula": "../pir >= ../cir"}]}, {"name": "cbs", "type": "uint32"}, {"name": "pbs", "type": "uint32"}, {"name": "green", "type": "container", "fields": [{"name": "action", "type": "choice", "fields": [{"name": "drop", "type": "case", "fields": [{"name": "drop", "type": "container", "presence": true, "fields": []}]}, {"name": "pass", "type": "case", "fields": [{"name": "pass", "type": "container", "presence": true, "fields": [{"name": "remark-dscp", "type": "uint8"}]}]}]}]}, {"name": "yellow", "type": "container", "fields": [{"name": "action", "type": "choice", "fields": [{"name": "drop", "type": "case", "fields": [{"name": "drop", "type": "container", "presence": true, "fields": []}]}, {"name": "pass", "type": "case", "fields": [{"name": "pass", "type": "container", "presence": true, "fields": [{"name": "remark-dscp", "type": "uint8"}]}]}]}]}, {"name": "red", "type": "container", "fields": [{"name": "action", "type": "choice", "fields": [{"name": "drop", "type": "case", "fields": [{"name": "drop", "type": "container", "presence": true, "fields": []}]}, {"name": "pass", "type": "case", "fields": [{"name": "pass", "type": "container", "presence": true, "fields": [{"name": "remark-dscp", "type": "uint8"}]}]}]}]}, {"name": "statuses", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "acl"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sacl:sacl::traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance::statuses::status", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "rule", "type": "string", "is_config": false, "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "apply-status", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "rule", "slot"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sacl:sacl::traffic-statistics-applys::traffic-statistics-apply", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "acl-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "direction"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sacl:sacl::traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance", "type": "list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "acl", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-acl/huawei-acl:acl/groups/huawei-acl:acl::groups::group/identity"}]}, {"name": "statistics", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "acl"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sacl:sacl::traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance::statistics::statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "rule", "type": "string", "is_config": false, "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "apply-status", "type": "string", "is_config": false}, {"name": "match-packet", "type": "uint64", "is_config": false}, {"name": "match-byte", "type": "uint64", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "rule", "slot"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sacl:sacl::traffic-redirect-applys::traffic-redirect-apply", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "acl-instances", "type": "container", "fields": [], "clause": [{"type": "must", "formula": "(../direction = 'inbound')"}]}], "keys": [{"name": "k0", "fields": [":pid", "direction"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sacl:sacl::traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "type": "list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "acl", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-acl/huawei-acl:acl/groups/huawei-acl:acl::groups::group/identity"}]}, {"name": "direction", "type": "choice", "fields": [{"name": "interface", "type": "case", "fields": [{"name": "interface-name", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface/name"}]}]}, {"name": "nexthop", "type": "case", "fields": [{"name": "ip-nexthop", "type": "string"}]}]}, {"name": "statuses", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "acl"], "index": {"type": "primary"}, "constraints": {"unique": true}}], "clause": [{"type": "must", "formula": "(direction/nexthop/ip-nexthop or direction/interface/interface-name)"}]}, {"name": "huawei-sacl:sacl::traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance::statuses::status", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "rule", "type": "string", "is_config": false, "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "apply-status", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "rule", "slot"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-network-instance:network-instance", "type": "container", "fields": [{"name": "instances", "type": "container", "fields": [], "clause": [{"type": "must", "formula": "count(huawei-network-instance:network-instance::instances::instance) = 0 or (count(huawei-network-instance:network-instance::instances::instance) = 1 and count(huawei-network-instance:network-instance::instances::instance[name = '_public_']) = 1)"}]}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "huawei-l3vpn:afs", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::af", "type": "list", "max-elements": 2, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "string", "nullable": false}, {"name": "huawei-routing:routing", "type": "container", "fields": [{"name": "routing-manage", "type": "container", "fields": [{"name": "topologys", "type": "container", "fields": [], "clause": [{"type": "must", "formula": "count(huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::routing-manage::topologys::topology) = 0 or (count(huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::routing-manage::topologys::topology) = 1 and count(huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::routing-manage::topologys::topology[name = 'base']) = 1)"}]}]}, {"name": "static-routing", "type": "container", "fields": [{"name": "unicast-route2s", "type": "container", "fields": []}, {"name": "ipv4-routes", "type": "container", "is_config": false, "fields": []}]}]}], "keys": [{"name": "k0", "fields": [":pid", "type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::routing-manage::topologys::topology", "type": "list", "max-elements": 32, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "clause": [{"type": "must", "formula": "../../../../../../../name = '_public_' or ../name = 'base'"}]}, {"name": "routes", "type": "container", "is_config": false, "fields": [{"name": "ipv4-unicast-routes", "type": "container", "is_config": false, "fields": []}, {"name": "ipv4-route-statistics", "type": "container", "is_config": false, "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-unicast-routes::ipv4-unicast-route", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "prefix", "type": "string", "is_config": false, "nullable": false}, {"name": "mask-length", "type": "uint8", "is_config": false, "nullable": false}, {"name": "protocol-type", "type": "string", "is_config": false, "nullable": false}, {"name": "interface-name", "type": "string", "is_config": false, "nullable": false}, {"name": "process-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "direct-nexthop", "type": "string", "is_config": false, "nullable": false}, {"name": "indirect-id", "type": "string", "is_config": false, "nullable": false}, {"name": "nexthop", "type": "string", "is_config": false}, {"name": "preference", "type": "uint32", "is_config": false}, {"name": "cost", "type": "uint32", "is_config": false}, {"name": "flag", "type": "string", "is_config": false}, {"name": "active", "type": "boolean", "is_config": false, "default": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "age", "type": "string", "is_config": false}, {"name": "relay-nexthop", "type": "string", "is_config": false}, {"name": "nexthop-interface-name", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "prefix", "mask-length", "protocol-type", "interface-name", "process-id", "direct-nexthop", "indirect-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-route-statistics::ipv4-route-statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "protocol-type", "type": "string", "is_config": false, "nullable": false}, {"name": "total-num", "type": "uint32", "is_config": false}, {"name": "active-num", "type": "uint32", "is_config": false}, {"name": "added-num", "type": "uint32", "is_config": false}, {"name": "deleted-num", "type": "uint32", "is_config": false}, {"name": "freed-num", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "protocol-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "topology-name", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-network-instance/huawei-network-instance:network-instance/instances/huawei-network-instance:network-instance::instances::instance/huawei-l3vpn:afs/huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::af/huawei-routing:routing/routing-manage/topologys/huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::routing-manage::topologys::topology/name"}, {"type": "must", "formula": "(../../../../../../../name = '_public_' or ../topology-name = 'base') and (count(../nexthop-interfaces/huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interfaces::nexthop-interface) > 0 or count(../nexthop-interface-addresses/huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address) > 0 or count(../nexthop-addresses/huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-addresses::nexthop-address) > 0)"}]}, {"name": "prefix", "type": "string", "nullable": false}, {"name": "mask-length", "type": "uint8", "nullable": false}, {"name": "nexthop-interfaces", "type": "container", "fields": []}, {"name": "nexthop-interface-addresses", "type": "container", "fields": []}, {"name": "nexthop-addresses", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "topology-name", "prefix", "mask-length"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interfaces::nexthop-interface", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interface-name", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface/name"}]}, {"name": "preference", "type": "int32", "default": 60}, {"name": "cost", "type": "uint32"}, {"name": "dhcp-enable", "type": "boolean", "default": false}], "keys": [{"name": "k0", "fields": [":pid", "interface-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interface-name", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface/name"}]}, {"name": "address", "type": "string", "nullable": false, "clause": [{"type": "must", "formula": "not(../address = '0.0.0.0')"}]}, {"name": "preference", "type": "int32", "default": 60}, {"name": "cost", "type": "uint32"}], "keys": [{"name": "k0", "fields": [":pid", "interface-name", "address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-addresses::nexthop-address", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "address", "type": "string", "nullable": false, "clause": [{"type": "must", "formula": "not(../address = '0.0.0.0')"}]}, {"name": "preference", "type": "int32", "default": 60}, {"name": "inherit-cost", "type": "boolean", "default": false}], "keys": [{"name": "k0", "fields": [":pid", "address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::ipv4-routes::ipv4-route", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "topology-name", "type": "string", "is_config": false, "nullable": false}, {"name": "prefix", "type": "string", "is_config": false, "nullable": false}, {"name": "mask-length", "type": "uint8", "is_config": false, "nullable": false}, {"name": "interface-name", "type": "string", "is_config": false, "nullable": false}, {"name": "vpn-destination-name", "type": "string", "is_config": false, "nullable": false}, {"name": "next-hop", "type": "string", "is_config": false, "nullable": false}, {"name": "relay-next-hop", "type": "string", "is_config": false, "nullable": false}, {"name": "relay-interface-name", "type": "string", "is_config": false, "nullable": false}, {"name": "tunnel-id", "type": "string", "is_config": false, "nullable": false}, {"name": "color", "type": "uint32", "is_config": false, "nullable": false}, {"name": "preference", "type": "uint32", "is_config": false}, {"name": "cost", "type": "uint32", "is_config": false}, {"name": "interface-state", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "inherit-cost", "type": "boolean", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "topology-name", "prefix", "mask-length", "interface-name", "vpn-destination-name", "next-hop", "relay-next-hop", "relay-interface-name", "tunnel-id", "color"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-routing", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-routing:reset-all-route-statistics", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "address-family", "type": "string", "is_config": false, "nullable": false}, {"name": "protocol-type", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-ap:wlan-ap", "type": "container", "fields": [{"name": "ap-auth-mode", "type": "string", "default": "mac"}, {"name": "ap-mac-whitelists", "type": "container", "fields": []}, {"name": "ap-types", "type": "container", "fields": []}, {"name": "temp-mgmt-psk", "type": "string"}, {"name": "ap-instances", "type": "container", "fields": []}]}, {"name": "huawei-wlan-ap:reset-ap", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "reset-info", "type": "choice", "is_config": false, "fields": [{"name": "ap-mac-info", "type": "case", "is_config": false, "fields": [{"name": "ap-mac", "type": "string", "is_config": false}]}, {"name": "ap-id-info", "type": "case", "is_config": false, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false}]}, {"name": "ap-group-info", "type": "case", "is_config": false, "fields": [{"name": "ap-group", "type": "string", "is_config": false}]}, {"name": "ap-type-info", "type": "case", "is_config": false, "fields": [{"name": "ap-type", "type": "string", "is_config": false}]}]}]}, {"name": "huawei-wlan-ap:reset-ap-with-factory-configuration", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "reset-info", "type": "choice", "is_config": false, "fields": [{"name": "ap-mac-info", "type": "case", "is_config": false, "fields": [{"name": "ap-mac", "type": "string", "is_config": false}]}, {"name": "ap-id-info", "type": "case", "is_config": false, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false}]}]}]}, {"name": "huawei-wlan-ap:led-blink-time", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "oper-type", "type": "string", "is_config": false, "nullable": false}, {"name": "time", "type": "uint32", "is_config": false, "nullable": false, "clause": [{"type": "when", "formula": "../oper-type = 'blink'"}]}, {"name": "ap-info", "type": "choice", "is_config": false, "nullable": false, "fields": [{"name": "ap-id", "type": "case", "is_config": false, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false}]}, {"name": "ap-mac", "type": "case", "is_config": false, "fields": [{"name": "ap-mac", "type": "string", "is_config": false}]}, {"name": "all", "type": "case", "is_config": false, "fields": [{"name": "ap-all", "type": "string", "is_config": false, "clause": [{"type": "when", "formula": "../../../oper-type = 'cancel'"}]}]}]}]}, {"name": "huawei-wlan-ap:get-ap-connect-fail-reason", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-instances", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-wlan-ap:reset-ap-connect-fail-reason", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-mac", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-mac-whitelists::ap-mac-whitelist", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-mac", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-mac"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-types::ap-type", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type-name", "type": "string", "nullable": false}, {"name": "type-id", "type": "uint8", "nullable": false}, {"name": "state", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "type-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-id", "type": "uint32", "nullable": false}, {"name": "ap-mac", "type": "string"}, {"name": "ap-sn", "type": "string"}, {"name": "ap-type", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-ap/huawei-wlan-ap:wlan-ap/ap-types/huawei-wlan-ap:wlan-ap::ap-types::ap-type/type-name"}]}, {"name": "ap-name", "type": "string"}, {"name": "ip-address", "type": "string", "is_config": false}, {"name": "vendor", "type": "string", "is_config": false}, {"name": "version", "type": "string", "is_config": false}, {"name": "patch-version", "type": "string", "is_config": false}, {"name": "run-state", "type": "string", "is_config": false}, {"name": "huawei-wlan-ap-group-profile:binding-group", "type": "container", "fields": [{"name": "binding-group", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-ap-group-profile/huawei-wlan-ap-group-profile:wlan-ap-group-profile/ap-group-profiles/huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile/ap-group-name"}]}]}, {"name": "huawei-wlan-ap-radio:radio-instances", "type": "container", "fields": []}, {"name": "huawei-wlan-reg-dom-profile:ap-binding-regular-domain-profile", "type": "container", "fields": [{"name": "binding-regular-domain-profile", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-reg-dom-profile/huawei-wlan-reg-dom-profile:wlan-reg-dom-profile/regulatory-domain-profiles/huawei-wlan-reg-dom-profile:wlan-reg-dom-profile::regulatory-domain-profiles::regulatory-domain-profile/profile-name"}]}]}, {"name": "huawei-wlan-system-profile:binding-system-profile", "type": "container", "fields": [{"name": "binding-system-profile", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-system-profile/huawei-wlan-system-profile:wlan-system-profile/system-profiles/huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile/profile-name"}]}]}, {"name": "huawei-wlan-wired-port-profile:ge-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "ap-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}], "clause": [{"type": "must", "formula": "(./ap-mac) or (./ap-sn)"}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instancehuawei-wlan-ap-radio:radio-instances::radio-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "radio-id", "type": "uint8", "nullable": false}, {"name": "radio-switch", "type": "string"}, {"name": "eirp", "type": "uint8", "default": 255}, {"name": "channel-info", "type": "container", "presence": true, "fields": [{"name": "bandwidth", "type": "choice", "nullable": false, "fields": [{"name": "bandwidth-20mhz", "type": "case", "fields": [{"name": "bandwidth-20mhz", "type": "string"}]}, {"name": "bandwidth-40mhz-minus", "type": "case", "fields": [{"name": "bandwidth-40mhz-minus", "type": "string"}]}, {"name": "bandwidth-40mhz-plus", "type": "case", "fields": [{"name": "bandwidth-40mhz-plus", "type": "string"}]}, {"name": "bandwidth-80mhz", "type": "case", "fields": [{"name": "bandwidth-80mhz", "type": "string"}], "clause": [{"type": "when", "formula": "(../../../radio-id = 1) or (../../../radio-id = 2) or ((../../../radio-id = 0) and (../../../frequency = '5G'))"}]}, {"name": "bandwidth-80plus80mhz", "type": "case", "fields": [], "clause": [{"type": "when", "formula": "(../../../radio-id = 1) or (../../../radio-id = 2) or ((../../../radio-id = 0) and (../../../frequency = '5G'))"}]}, {"name": "bandwidth-160mhz", "type": "case", "fields": [{"name": "bandwidth-160mhz", "type": "string"}], "clause": [{"type": "when", "formula": "(../../../radio-id = 1) or (../../../radio-id = 2) or ((../../../radio-id = 0) and (../../../frequency = '5G'))"}]}]}, {"name": "channel", "type": "uint8", "nullable": false}]}, {"name": "auto-channel-select", "type": "string", "default": "nocfg"}, {"name": "auto-txpower-select", "type": "string", "default": "nocfg"}, {"name": "frequency", "type": "string", "default": "none", "clause": [{"type": "when", "formula": "(../radio-id != 1)"}]}, {"name": "auto-bandwidth-select", "type": "string", "default": "none"}, {"name": "huawei-wlan-radio-2g-profile:binding-radio-2g-instance", "type": "container", "fields": [{"name": "binding-radio-2g-profile", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-radio-2g-profile/huawei-wlan-radio-2g-profile:wlan-radio-2g-profile/radio-2g-profiles/huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile/profile-name"}]}], "clause": [{"type": "when", "formula": "(../radio-id != 1)"}]}, {"name": "huawei-wlan-radio-5g-profile:binding-radio-5g-instance", "type": "container", "fields": [{"name": "binding-radio-5g-profile", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-radio-5g-profile/huawei-wlan-radio-5g-profile:wlan-radio-5g-profile/radio-5g-profiles/huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile/profile-name"}]}]}, {"name": "huawei-wlan-vap-inst:vap-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "radio-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instancehuawei-wlan-ap-radio:radio-instances::radio-instancehuawei-wlan-vap-inst:vap-instances::vap-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "wlan-id", "type": "uint8", "nullable": false}, {"name": "binding-vap-profile", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-vap-profile/huawei-wlan-vap-profile:wlan-vap-profile/vap-profiles/huawei-wlan-vap-profile:wlan-vap-profile::vap-profiles::vap-profile/profile-name"}]}, {"name": "service-vlan", "type": "container", "fields": [{"name": "service-vlan-id", "type": "uint16"}]}], "keys": [{"name": "k0", "fields": [":pid", "wlan-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instancehuawei-wlan-wired-port-profile:ge-instances::ge-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint8", "nullable": false}, {"name": "bind-wiredport-profile", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-wired-port-profile/huawei-wlan-wired-port-profile:wlan-wired-port-profile/wiredport-profiles/huawei-wlan-wired-port-profile:wlan-wired-port-profile::wiredport-profiles::wiredport-profile/profile-name"}]}], "keys": [{"name": "k0", "fields": [":pid", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap:get-ap-connect-fail-reason::ap-instances::ap-instance", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-mac", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-mac"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap-group-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile", "type": "container", "fields": [{"name": "ap-group-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-group-name", "type": "string", "nullable": false}, {"name": "huawei-wlan-group-radio:radio-instances", "type": "container", "fields": []}, {"name": "huawei-wlan-reg-dom-profile:grp-binding-regular-domain-profile", "type": "container", "fields": [{"name": "binding-regular-domain-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-reg-dom-profile/huawei-wlan-reg-dom-profile:wlan-reg-dom-profile/regulatory-domain-profiles/huawei-wlan-reg-dom-profile:wlan-reg-dom-profile::regulatory-domain-profiles::regulatory-domain-profile/profile-name"}]}]}, {"name": "huawei-wlan-system-profile:binding-system-profile", "type": "container", "fields": [{"name": "binding-system-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-system-profile/huawei-wlan-system-profile:wlan-system-profile/system-profiles/huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile/profile-name"}]}]}, {"name": "huawei-wlan-wired-port-profile:ge-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "ap-group-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profilehuawei-wlan-group-radio:radio-instances::radio-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "radio-id", "type": "uint8", "nullable": false}, {"name": "radio-switch", "type": "string", "default": "enable"}, {"name": "channel-info", "type": "container", "presence": true, "fields": [{"name": "bandwidth", "type": "choice", "nullable": false, "fields": [{"name": "bandwidth-20mhz", "type": "case", "fields": [{"name": "bandwidth-20mhz", "type": "string"}]}, {"name": "bandwidth-40mhz-minus", "type": "case", "fields": [{"name": "bandwidth-40mhz-minus", "type": "string"}]}, {"name": "bandwidth-40mhz-plus", "type": "case", "fields": [{"name": "bandwidth-40mhz-plus", "type": "string"}]}, {"name": "bandwidth-80mhz", "type": "case", "fields": [{"name": "bandwidth-80mhz", "type": "string"}], "clause": [{"type": "when", "formula": "(../../../radio-id = 1) or (../../../radio-id = 2) or ((../../../radio-id = 0) and (../../../frequency = '5G'))"}]}, {"name": "bandwidth-160mhz", "type": "case", "fields": [{"name": "bandwidth-160mhz", "type": "string"}], "clause": [{"type": "when", "formula": "(../../../radio-id = 1) or (../../../radio-id = 2) or ((../../../radio-id = 0) and (../../../frequency = '5G'))"}]}]}, {"name": "channel", "type": "uint8", "nullable": false}]}, {"name": "eirp", "type": "uint8", "default": 127}, {"name": "auto-channel-select", "type": "string", "default": "enable"}, {"name": "auto-txpower-select", "type": "string", "default": "enable"}, {"name": "frequency", "type": "string", "clause": [{"type": "when", "formula": "(../radio-id != 1)"}]}, {"name": "auto-bandwidth-select", "type": "string", "default": "disable"}, {"name": "huawei-wlan-radio-2g-profile:binding-radio-2g-instance", "type": "container", "fields": [{"name": "binding-radio-2g-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-radio-2g-profile/huawei-wlan-radio-2g-profile:wlan-radio-2g-profile/radio-2g-profiles/huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile/profile-name"}]}], "clause": [{"type": "when", "formula": "(../radio-id != 1)"}]}, {"name": "huawei-wlan-radio-5g-profile:binding-radio-5g-instance", "type": "container", "fields": [{"name": "binding-radio-5g-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-radio-5g-profile/huawei-wlan-radio-5g-profile:wlan-radio-5g-profile/radio-5g-profiles/huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile/profile-name"}]}]}, {"name": "huawei-wlan-vap-inst:vap-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "radio-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profilehuawei-wlan-group-radio:radio-instances::radio-instancehuawei-wlan-vap-inst:vap-instances::vap-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "wlan-id", "type": "uint8", "nullable": false}, {"name": "binding-vap-profile", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-vap-profile/huawei-wlan-vap-profile:wlan-vap-profile/vap-profiles/huawei-wlan-vap-profile:wlan-vap-profile::vap-profiles::vap-profile/profile-name"}]}, {"name": "service-vlan", "type": "container", "fields": [{"name": "service-vlan-id", "type": "uint16"}]}], "keys": [{"name": "k0", "fields": [":pid", "wlan-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profilehuawei-wlan-wired-port-profile:ge-instances::ge-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint8", "nullable": false}, {"name": "bind-wiredport-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-wired-port-profile/huawei-wlan-wired-port-profile:wlan-wired-port-profile/wiredport-profiles/huawei-wlan-wired-port-profile:wlan-wired-port-profile::wiredport-profiles::wiredport-profile/profile-name"}]}], "keys": [{"name": "k0", "fields": [":pid", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-2g-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile", "type": "container", "fields": [{"name": "radio-2g-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "dot11bg-basic-rate-lists", "type": "container", "fields": []}, {"name": "dot11bg-support-rate-lists", "type": "container", "fields": []}, {"name": "radio-type", "type": "string", "default": "dot11ax"}, {"name": "huawei-wlan-airscan-profile:radio-2g-profile-binding-airscan", "type": "container", "fields": [{"name": "binding-airscan-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-airscan-profile/huawei-wlan-airscan-profile:wlan-airscan-profile/airscan-profiles/huawei-wlan-airscan-profile:wlan-airscan-profile::airscan-profiles::airscan-profile/profile-name"}]}]}, {"name": "huawei-wlan-rrm-profile:radio-2g-binding-rrm", "type": "container", "fields": [{"name": "binding-rrm-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-rrm-profile/huawei-wlan-rrm-profile:wlan-rrm-profile/rrm-profiles/huawei-wlan-rrm-profile:wlan-rrm-profile::rrm-profiles::rrm-profile/profile-name"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile::dot11bg-basic-rate-lists::dot11bg-basic-rate-list", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "basic-rate", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "basic-rate"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile::dot11bg-support-rate-lists::dot11bg-support-rate-list", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "support-rate", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "support-rate"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-5g-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "hua<PERSON>-wlan-radio-5g-profile:wlan-radio-5g-profile", "type": "container", "fields": [{"name": "radio-5g-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "dot11a-basic-rate-lists", "type": "container", "fields": []}, {"name": "dot11a-support-rate-lists", "type": "container", "fields": []}, {"name": "radio-type", "type": "string", "default": "dot11ax"}, {"name": "huawei-wlan-airscan-profile:radio-5g-profile-binding-airscan", "type": "container", "fields": [{"name": "binding-airscan-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-airscan-profile/huawei-wlan-airscan-profile:wlan-airscan-profile/airscan-profiles/huawei-wlan-airscan-profile:wlan-airscan-profile::airscan-profiles::airscan-profile/profile-name"}]}]}, {"name": "huawei-wlan-rrm-profile:radio-5g-binding-rrm", "type": "container", "fields": [{"name": "binding-rrm-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-rrm-profile/huawei-wlan-rrm-profile:wlan-rrm-profile/rrm-profiles/huawei-wlan-rrm-profile:wlan-rrm-profile::rrm-profiles::rrm-profile/profile-name"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile::dot11a-basic-rate-lists::dot11a-basic-rate-list", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "basic-rate", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "basic-rate"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile::dot11a-support-rate-lists::dot11a-support-rate-list", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "support-rate", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "support-rate"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-reg-dom-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-reg-dom-profile:wlan-reg-dom-profile", "type": "container", "fields": [{"name": "regulatory-domain-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-reg-dom-profile:wlan-reg-dom-profile::regulatory-domain-profiles::regulatory-domain-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "country-code", "type": "string", "default": "CN"}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-system-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-system-profile:wlan-system-profile", "type": "container", "fields": [{"name": "system-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "stelnet-server", "type": "boolean", "default": true}, {"name": "telnet", "type": "boolean", "default": false}, {"name": "led", "type": "container", "fields": [{"name": "led-switch", "type": "string", "default": "turn-on"}, {"name": "turn-off-by-time", "type": "string", "clause": [{"type": "must", "formula": "../turn-off-by-time != 'all'"}, {"type": "when", "formula": "../led-switch = 'turn-off'"}]}]}, {"name": "traffic-optimize", "type": "container", "fields": [{"name": "arp-threshold", "type": "uint32", "default": 256}, {"name": "igmp-threshold", "type": "uint32", "default": 256}, {"name": "nd-threshold", "type": "uint32", "default": 256}, {"name": "dhcp-threshold", "type": "uint32", "default": 256}, {"name": "dhcpv6-threshold", "type": "uint32", "default": 256}, {"name": "mdns-threshold", "type": "uint32", "default": 256}, {"name": "other-broadcast-threshold", "type": "uint32", "default": 16}, {"name": "other-multicast-threshold", "type": "uint32", "default": 16}, {"name": "arp", "type": "boolean", "default": true}, {"name": "igmp", "type": "boolean", "default": true}, {"name": "nd", "type": "boolean", "default": true}, {"name": "dhcp", "type": "boolean", "default": true}, {"name": "dhcpv6", "type": "boolean", "default": true}, {"name": "mdns", "type": "boolean", "default": true}, {"name": "other-broadcast", "type": "boolean", "default": true}, {"name": "other-multicast", "type": "boolean", "default": true}]}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-vap-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-vap-profile:wlan-vap-profile", "type": "container", "fields": [{"name": "vap-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-vap-profile:wlan-vap-profile::vap-profiles::vap-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "service-vlan", "type": "uint16", "default": 1}, {"name": "ip-source-check-user-bind-switch", "type": "boolean", "default": false}, {"name": "arp-anti-attack-check-user-bind-switch", "type": "boolean", "default": false}, {"name": "dhcp-option82", "type": "container", "fields": [{"name": "insert-switch", "type": "boolean", "default": false}, {"name": "circuit-id", "type": "container", "fields": [{"name": "dhcp-option82-format-type", "type": "string", "default": "ap-mac"}, {"name": "ap-mac-format", "type": "string", "clause": [{"type": "when", "formula": "../dhcp-option82-format-type = 'ap-mac'"}]}, {"name": "ap-mac-ssid-format", "type": "string", "clause": [{"type": "when", "formula": "../dhcp-option82-format-type = 'ap-mac-ssid'"}]}, {"name": "user-defined", "type": "string"}, {"name": "option82-pattern-semicolon", "type": "string", "default": "disable"}], "clause": [{"type": "must", "formula": "(./dhcp-option82-format-type = 'user-defined' and ./user-defined) or (./dhcp-option82-format-type != 'user-defined')"}]}, {"name": "remote-id", "type": "container", "fields": [{"name": "dhcp-option82-format-type", "type": "string", "default": "ap-mac"}, {"name": "ap-mac-format", "type": "string", "clause": [{"type": "when", "formula": "../dhcp-option82-format-type = 'ap-mac'"}]}, {"name": "ap-mac-ssid-format", "type": "string", "clause": [{"type": "when", "formula": "../dhcp-option82-format-type = 'ap-mac-ssid'"}]}, {"name": "user-defined", "type": "string"}, {"name": "option82-pattern-semicolon", "type": "string", "default": "disable"}], "clause": [{"type": "must", "formula": "(./dhcp-option82-format-type = 'user-defined' and ./user-defined) or (./dhcp-option82-format-type != 'user-defined')"}]}]}, {"name": "huawei-wlan-security-profile:binding-security-profile", "type": "container", "fields": [{"name": "binding-security-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-security-profile/huawei-wlan-security-profile:wlan-security-profile/security-profiles/huawei-wlan-security-profile:wlan-security-profile::security-profiles::security-profile/profile-name"}]}]}, {"name": "huawei-wlan-ssid-profile:binding-ssid-profile", "type": "container", "fields": [{"name": "binding-ssid-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-ssid-profile/huawei-wlan-ssid-profile:wlan-ssid-profile/ssid-profiles/huawei-wlan-ssid-profile:wlan-ssid-profile::ssid-profiles::ssid-profile/profile-name"}]}]}, {"name": "huawei-wlan-traffic-profile:binding-traffic-profile", "type": "container", "fields": [{"name": "binding-traffic-profile", "type": "string", "default": "default", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-wlan-traffic-profile/huawei-wlan-traffic-profile:wlan-traffic-profile/traffic-profiles/huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile/profile-name"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "hua<PERSON>-wlan-security-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "hua<PERSON>-wlan-security-profile:wlan-security-profile", "type": "container", "fields": [{"name": "security-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-security-profile:wlan-security-profile::security-profiles::security-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "policy", "type": "choice", "fields": [{"name": "open", "type": "case", "fields": [{"name": "open-policy", "type": "string"}]}, {"name": "wpa", "type": "case", "fields": [{"name": "wpa-parameter", "type": "container", "fields": [{"name": "wpa-type", "type": "string", "nullable": false}, {"name": "authentication-method", "type": "choice", "nullable": false, "fields": [{"name": "psk", "type": "case", "fields": [{"name": "pass-type", "type": "string", "nullable": false}, {"name": "psk", "type": "string", "nullable": false}]}]}, {"name": "encrption-method", "type": "string", "nullable": false, "clause": [{"type": "must", "formula": "not(../encrption-method = 'tkip-aes' and not(../wpa-type = 'wpa-wpa2'))"}]}]}]}]}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ssid-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-ssid-profile:wlan-ssid-profile", "type": "container", "fields": [{"name": "ssid-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-ssid-profile:wlan-ssid-profile::ssid-profiles::ssid-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "ssid", "type": "string", "default": "HUAWEI-WLAN"}, {"name": "max-sta-number", "type": "uint16", "default": 64}, {"name": "ofdma-policy", "type": "container", "fields": [{"name": "ofdma-downlink-switch", "type": "boolean", "default": true}, {"name": "ofdma-uplink-switch", "type": "boolean", "default": true}]}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-traffic-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-traffic-profile:wlan-traffic-profile", "type": "container", "fields": [{"name": "traffic-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "user-isolate", "type": "string"}, {"name": "rate-limit", "type": "container", "fields": [{"name": "client-up", "type": "uint32", "default": 4294967295}, {"name": "client-down", "type": "uint32", "default": 4294967295}, {"name": "vap-up", "type": "uint32", "default": 4294967295}, {"name": "vap-down", "type": "uint32", "default": 4294967295}]}, {"name": "time-range-up-rate-limits", "type": "container", "fields": []}, {"name": "time-range-down-rate-limits", "type": "container", "fields": []}, {"name": "traffic-optimize", "type": "container", "fields": [{"name": "dynamic-rate-limit", "type": "boolean", "default": true}, {"name": "broadcast-suppression", "type": "uint32"}, {"name": "multicast-suppression", "type": "uint32"}, {"name": "unicast-suppression", "type": "uint32"}, {"name": "multicast-unicast", "type": "boolean", "default": false}, {"name": "bcmc-unicast-arp", "type": "boolean", "default": true}, {"name": "bcmc-unicast-dhcp", "type": "boolean", "default": true}, {"name": "bcmc-unicast-nd", "type": "boolean", "default": true}, {"name": "bcmc-unicast-mismatch-drop", "type": "boolean", "default": true}, {"name": "bcmc-deny", "type": "container", "fields": [{"name": "deny-all", "type": "boolean", "default": false}, {"name": "except-mdns", "type": "boolean", "default": false, "clause": [{"type": "when", "formula": "../deny-all = true()"}]}]}, {"name": "tcp-adjust-mss", "type": "uint16"}]}, {"name": "traffic-remarks", "type": "container", "presence": true, "fields": []}, {"name": "traffic-filters", "type": "container", "presence": true, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile::time-range-up-rate-limits::time-range-up-rate-limit", "type": "list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "time-range", "type": "string", "nullable": false}, {"name": "client-up", "type": "uint32"}], "keys": [{"name": "k0", "fields": [":pid", "time-range"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile::time-range-down-rate-limits::time-range-down-rate-limit", "type": "list", "max-elements": 8, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "time-range", "type": "string", "nullable": false}, {"name": "client-down", "type": "uint32"}], "keys": [{"name": "k0", "fields": [":pid", "time-range"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile::traffic-remarks::traffic-remark", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "ipv4-acl-number", "type": "uint32", "nullable": false}, {"name": "l2-acl-number", "type": "uint32", "nullable": false}, {"name": "ipv6-acl-number", "type": "uint32", "nullable": false}, {"name": "dscp-or-dot11e", "type": "string"}, {"name": "dscp", "type": "uint16", "clause": [{"type": "when", "formula": "../dscp-or-dot11e = 'dscp'"}]}, {"name": "dot11e", "type": "uint16", "clause": [{"type": "when", "formula": "../dscp-or-dot11e = 'dot11e'"}]}], "keys": [{"name": "k0", "fields": [":pid", "direction", "ipv4-acl-number", "l2-acl-number", "ipv6-acl-number"], "index": {"type": "primary"}, "constraints": {"unique": true}}], "clause": [{"type": "must", "formula": "((./ipv4-acl-number != 0 or ./l2-acl-number != 0) and ./ipv6-acl-number = 0) or (./ipv4-acl-number = 0 and ./l2-acl-number = 0 and ipv6-acl-number != 0)"}]}, {"name": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile::traffic-filters::traffic-filter", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "ipv4-acl-number", "type": "uint32", "nullable": false}, {"name": "l2-acl-number", "type": "uint32", "nullable": false}, {"name": "ipv6-acl-number", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "direction", "ipv4-acl-number", "l2-acl-number", "ipv6-acl-number"], "index": {"type": "primary"}, "constraints": {"unique": true}}], "clause": [{"type": "must", "formula": "((./ipv4-acl-number != 0 or ./l2-acl-number != 0) and ./ipv6-acl-number = 0) or (./ipv4-acl-number = 0 and ./l2-acl-number = 0 and ipv6-acl-number != 0)"}]}, {"name": "huawei-wlan-rrm-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-rrm-profile:wlan-rrm-profile", "type": "container", "fields": [{"name": "rrm-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-rrm-profile:wlan-rrm-profile::rrm-profiles::rrm-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "high-density-amc-optimize", "type": "string", "default": "disable"}, {"name": "dynamic-edca", "type": "container", "fields": [{"name": "dynamic-edca", "type": "string", "default": "disable"}, {"name": "dynamic-edca-threshold", "type": "uint16", "default": 6}]}, {"name": "antenna-mode", "type": "string", "default": "auto"}, {"name": "multimedia-air-optimize", "type": "string", "default": "enable"}, {"name": "multimedia-tcp-window-tuning", "type": "string", "default": "enable"}, {"name": "multimedia-uplink-delay-guarantee", "type": "string", "default": "enable"}, {"name": "downlink-delay-guarantee", "type": "container", "fields": [{"name": "voice-guarantee-level", "type": "string", "default": "medium"}, {"name": "video-guarantee-level", "type": "string", "default": "medium"}, {"name": "best-effort-guarantee-level", "type": "string", "default": "medium"}, {"name": "background-guarantee-level", "type": "string", "default": "medium"}]}, {"name": "downlink-slice-ratio", "type": "container", "fields": [{"name": "voice-slice-ratio-level", "type": "string", "default": "medium"}, {"name": "video-slice-ratio-level", "type": "string", "default": "medium"}, {"name": "voice-slice-ratio-value", "type": "uint8", "nullable": false, "clause": [{"type": "when", "formula": "../voice-slice-ratio-level = 'user-defined'"}]}, {"name": "video-slice-ratio-value", "type": "uint8", "nullable": false, "clause": [{"type": "when", "formula": "../video-slice-ratio-level = 'user-defined'"}]}]}, {"name": "multimedia-air-optimize-threshold", "type": "container", "fields": [{"name": "video-threshold", "type": "uint16", "default": 100}, {"name": "voice-threshold", "type": "uint16", "default": 30}]}, {"name": "smart-roam", "type": "container", "fields": [{"name": "smart-roam-switch", "type": "string", "default": "enable"}, {"name": "snr-smart-roam-threshold", "type": "uint8", "default": 20}, {"name": "quick-kickoff-threshold", "type": "string", "default": "enable"}, {"name": "snr-quick-kickoff-threshold", "type": "uint8", "default": 15}, {"name": "high-level-margin", "type": "uint16", "default": 12}, {"name": "low-level-margin", "type": "uint16", "default": 10}, {"name": "unable-roam-exp-time", "type": "uint16", "default": 30}]}, {"name": "calibrate-retransmission-rate-check", "type": "container", "fields": [{"name": "interval", "type": "uint8", "default": 1}, {"name": "traffic-threshold", "type": "uint16", "default": 1250}]}, {"name": "dfs-recover-delay", "type": "uint16", "default": 0}, {"name": "uac", "type": "container", "fields": [{"name": "uac-client-snr", "type": "string", "default": "disable"}, {"name": "uac-client-number", "type": "string", "default": "disable"}, {"name": "snr-threshold", "type": "uint8", "default": 15}, {"name": "number-threshold", "type": "container", "fields": [{"name": "access", "type": "uint16", "default": 64}, {"name": "roam", "type": "uint16", "default": 64}], "clause": [{"type": "must", "formula": "./access <= ./roam"}]}, {"name": "reach-access-threshold", "type": "string"}]}, {"name": "load-balance", "type": "container", "fields": [{"name": "load-balance-switch", "type": "string", "default": "enable"}, {"name": "probe-report-intvl", "type": "uint16", "default": 120}, {"name": "sta-start-threshold", "type": "uint16", "default": 10}, {"name": "sta-gap-threshold", "type": "container", "fields": [{"name": "gap-type", "type": "string", "default": "number"}, {"name": "number", "type": "uint16", "default": 3, "clause": [{"type": "when", "formula": "../gap-type = 'number'"}]}, {"name": "percentage", "type": "uint16", "nullable": false, "clause": [{"type": "when", "formula": "../gap-type = 'percentage'"}]}]}, {"name": "rssi-threshold", "type": "int16", "default": -65}, {"name": "rssi-diff-gap", "type": "uint16", "default": 5}, {"name": "de<PERSON>h-fail-times", "type": "uint16", "default": 0}]}, {"name": "calibrate-para", "type": "container", "fields": [{"name": "min-tx-power-2g", "type": "uint16", "default": 9}, {"name": "max-tx-power-2g", "type": "uint16", "default": 127}, {"name": "min-tx-power-5g", "type": "uint16", "default": 12}, {"name": "max-tx-power-5g", "type": "uint16", "default": 127}, {"name": "min-tx-power-6g", "type": "uint16", "default": 12}, {"name": "max-tx-power-6g", "type": "uint16", "default": 127}, {"name": "clb-noise-flr-thrd", "type": "int32", "default": -75}, {"name": "clbt-tpc-thrd", "type": "int32", "default": -60}, {"name": "clbt-restran-rate-thrd", "type": "int16", "default": 60}], "clause": [{"type": "must", "formula": "./min-tx-power-2g <= ./max-tx-power-2g and ./min-tx-power-5g <= ./max-tx-power-5g and ./min-tx-power-6g <= ./max-tx-power-6g"}]}, {"name": "clb-grp-itf-thrld", "type": "int32", "default": -127}, {"name": "spatial-reuse", "type": "container", "fields": [{"name": "bss-color-switch", "type": "boolean", "default": true}, {"name": "spatial-reuse-switch", "type": "boolean", "default": true}, {"name": "co-sr", "type": "boolean", "default": true}], "clause": [{"type": "must", "formula": "(spatial-reuse-switch = false()) or (spatial-reuse-switch = true() and bss-color-switch = true())"}]}, {"name": "band-steer", "type": "container", "fields": [{"name": "start-thrd", "type": "uint16", "default": 100}, {"name": "gap-thrd", "type": "uint16", "default": 90}, {"name": "snr-thrd", "type": "uint16", "default": 20}, {"name": "deny-thrd", "type": "uint16", "default": 0}, {"name": "client-band-expire", "type": "uint32", "default": 35}]}, {"name": "amc-policy", "type": "string", "default": "auto-balance"}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-airscan-profile", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-airscan-profile:wlan-airscan-profile", "type": "container", "fields": [{"name": "airscan-profiles", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-airscan-profile:wlan-airscan-profile::airscan-profiles::airscan-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "profile-name", "type": "string", "nullable": false}, {"name": "scan", "type": "string", "default": "enable"}, {"name": "scan-enhancement", "type": "string", "default": "disable"}, {"name": "scan-channel-set", "type": "string", "default": "country-channel"}, {"name": "scan-interval", "type": "uint32", "default": 10000}, {"name": "scan-period", "type": "uint32", "default": 60}], "keys": [{"name": "k0", "fields": [":pid", "profile-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-calibrate", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-radio-calibrate:wlan-radio-calibrate", "type": "container", "fields": [{"name": "calibrate", "type": "container", "fields": [{"name": "calibrate-mode", "type": "string", "default": "auto"}, {"name": "auto-mode-para", "type": "container", "fields": [{"name": "auto-interval", "type": "int32", "default": 1440}, {"name": "auto-start-time", "type": "string", "default": "03:00:00"}], "clause": [{"type": "when", "formula": "../calibrate-mode = 'auto'"}]}, {"name": "schedule-mode-para", "type": "container", "presence": true, "fields": [{"name": "time-mode", "type": "string", "nullable": false}, {"name": "time-range", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-time-range/huawei-time-range:time-range/time-range-instances/huawei-time-range:time-range::time-range-instances::time-range-instance/name"}, {"type": "when", "formula": "../time-mode = 'schedule-time-range'"}]}, {"name": "specific-time", "type": "string", "nullable": false, "clause": [{"type": "when", "formula": "../time-mode = 'schedule-specific-time'"}]}], "clause": [{"type": "when", "formula": "../calibrate-mode = 'schedule'"}]}, {"name": "calibrate-policy", "type": "container", "fields": [{"name": "non-wifi", "type": "string"}]}, {"name": "process", "type": "uint32", "is_config": false}, {"name": "sensitivitys", "type": "container", "fields": []}]}]}, {"name": "huawei-wlan-radio-calibrate:calibrate-manual", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "choice", "is_config": false, "fields": [{"name": "ap-list", "type": "case", "is_config": false, "fields": [{"name": "ap-lists", "type": "container", "is_config": false, "fields": []}]}, {"name": "ap-group-list", "type": "case", "is_config": false, "fields": [{"name": "ap-group-lists", "type": "container", "is_config": false, "fields": []}]}]}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-calibrate:wlan-radio-calibrate::calibrate::sensitivitys::sensitivity", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "freq", "type": "string", "nullable": false}, {"name": "sensitivity-type", "type": "string", "default": "medium"}, {"name": "custom-percent", "type": "uint32", "nullable": false, "clause": [{"type": "when", "formula": "../sensitivity-type = 'custom-percent'"}]}], "keys": [{"name": "k0", "fields": [":pid", "freq"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-calibrate:calibrate-manual::type::ap-list::ap-lists::ap-list", "type": "list", "is_config": false, "max-elements": 50, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-id", "type": "uint32", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-radio-calibrate:calibrate-manual::type::ap-group-list::ap-group-lists::ap-group-list", "type": "list", "is_config": false, "max-elements": 4, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-group", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-group"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-time-range", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-time-range:time-range", "type": "container", "fields": [{"name": "time-range-instances", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance", "type": "list", "max-elements": 256, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "absolute-ranges", "type": "container", "fields": []}, {"name": "period-ranges", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance::absolute-ranges::absolute-range", "type": "list", "max-elements": 16, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "start-time", "type": "string", "nullable": false}, {"name": "end-time", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "start-time", "end-time"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance::period-ranges::period-range", "type": "list", "max-elements": 32, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "day-of-week", "type": "string", "nullable": false}, {"name": "start-time", "type": "string", "nullable": false}, {"name": "end-time", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "day-of-week", "start-time", "end-time"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "hua<PERSON>-wlan-management", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-management:wlan-management", "type": "container", "fields": [{"name": "ap-user-password", "type": "container", "presence": true, "fields": [{"name": "user-name", "type": "string", "nullable": false}, {"name": "password", "type": "string", "nullable": false}]}, {"name": "ap-password-policy", "type": "container", "presence": true, "fields": [{"name": "pwd-expire-time", "type": "uint32", "default": 90}, {"name": "pwd-alert-before-expire", "type": "uint32", "default": 30}, {"name": "pwd-history-record-num", "type": "uint8", "default": 5}, {"name": "pwd-alert-original", "type": "boolean", "default": true}]}, {"name": "ap-update-mode", "type": "container", "fields": [{"name": "update-mode", "type": "string", "default": "ac-mode"}]}, {"name": "ap-type-update-files", "type": "container", "fields": []}, {"name": "ap-type-group-update-files", "type": "container", "fields": []}, {"name": "ap-type-update-patchs", "type": "container", "fields": []}, {"name": "ap-type-group-update-patchs", "type": "container", "fields": []}, {"name": "ap-update-ftp-server", "type": "container", "presence": true, "fields": [{"name": "ip-address", "type": "string", "nullable": false}, {"name": "port", "type": "uint32"}, {"name": "user-name", "type": "string", "nullable": false}, {"name": "password", "type": "string", "nullable": false}, {"name": "max-connect-number", "type": "uint32", "default": 50}]}, {"name": "ap-update-sftp-server", "type": "container", "presence": true, "fields": [{"name": "ip-address", "type": "string", "nullable": false}, {"name": "port", "type": "uint32"}, {"name": "user-name", "type": "string", "nullable": false}, {"name": "password", "type": "string", "nullable": false}, {"name": "max-connect-number", "type": "uint32", "default": 50}]}, {"name": "ap-update-ftp-server-v6", "type": "container", "presence": true, "fields": [{"name": "ip-address", "type": "string", "nullable": false}, {"name": "port", "type": "uint32"}, {"name": "user-name", "type": "string", "nullable": false}, {"name": "password", "type": "string", "nullable": false}, {"name": "max-connect-number", "type": "uint32", "default": 50}]}, {"name": "ap-update-sftp-server-v6", "type": "container", "presence": true, "fields": [{"name": "ip-address", "type": "string", "nullable": false}, {"name": "port", "type": "uint32"}, {"name": "user-name", "type": "string", "nullable": false}, {"name": "password", "type": "string", "nullable": false}, {"name": "max-connect-number", "type": "uint32", "default": 50}]}, {"name": "ap-update-capability", "type": "container", "is_config": false, "fields": [{"name": "query-is-support-update-url", "type": "container", "is_config": false, "presence": true, "fields": []}]}]}, {"name": "huawei-wlan-management:load-ap", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "oper-info", "type": "container", "is_config": false, "fields": [{"name": "mode", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "ap-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "file-name", "type": "string", "is_config": false, "nullable": false, "clause": [{"type": "when", "formula": "../mode = 'load'"}]}, {"name": "next-startup", "type": "string", "is_config": false, "clause": [{"type": "when", "formula": "(../mode = 'load') and (../type = 'patch')"}]}]}]}, {"name": "huawei-wlan-management:batch-load-ap", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "oper-info", "type": "container", "is_config": false, "fields": [{"name": "mode", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "type-or-group", "type": "choice", "is_config": false, "fields": [{"name": "ap-type-and-ap-group", "type": "case", "is_config": false, "fields": [{"name": "ap-type", "type": "uint32", "is_config": false, "nullable": false}, {"name": "ap-group", "type": "string", "is_config": false}]}, {"name": "only-ap-group", "type": "case", "is_config": false, "fields": [{"name": "only-ap-group", "type": "string", "is_config": false, "nullable": false}]}]}, {"name": "next-startup", "type": "string", "is_config": false, "clause": [{"type": "when", "formula": "(../mode = 'load') and (../type = 'patch')"}]}]}]}, {"name": "huawei-wlan-management:batch-update-reset-ap", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "oper-info", "type": "container", "is_config": false, "fields": [{"name": "type-or-group", "type": "choice", "is_config": false, "fields": [{"name": "ap-type-and-ap-group", "type": "case", "is_config": false, "fields": [{"name": "ap-type", "type": "uint32", "is_config": false, "nullable": false}, {"name": "ap-group", "type": "string", "is_config": false}]}, {"name": "only-ap-group", "type": "case", "is_config": false, "fields": [{"name": "only-ap-group", "type": "string", "is_config": false}]}]}]}]}, {"name": "huawei-wlan-management:delete-ap-patch", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "oper-info", "type": "container", "is_config": false, "fields": [{"name": "type-or-group", "type": "choice", "is_config": false, "fields": [{"name": "ap-type-and-ap-group", "type": "case", "is_config": false, "fields": [{"name": "ap-type", "type": "uint32", "is_config": false, "nullable": false}, {"name": "ap-group", "type": "string", "is_config": false}]}, {"name": "only-ap-group", "type": "case", "is_config": false, "fields": [{"name": "only-ap-group", "type": "string", "is_config": false, "nullable": false}]}, {"name": "ap-id", "type": "case", "is_config": false, "fields": [{"name": "ap-id", "type": "string", "is_config": false, "nullable": false}]}]}, {"name": "next-startup", "type": "string", "is_config": false}]}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-management:wlan-management::ap-type-update-files::ap-type-update-file", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-type", "type": "uint32", "nullable": false}, {"name": "file-name", "type": "string", "nullable": false}, {"name": "url", "type": "string", "clause": [{"type": "when", "formula": "../../../ap-update-mode/update-mode = 'web-mode'"}]}], "keys": [{"name": "k0", "fields": [":pid", "ap-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-management:wlan-management::ap-type-group-update-files::ap-type-group-update-file", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-type", "type": "uint32", "nullable": false}, {"name": "ap-group", "type": "string", "nullable": false}, {"name": "file-name", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-type", "ap-group"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-management:wlan-management::ap-type-update-patchs::ap-type-update-patch", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-type", "type": "uint32", "nullable": false}, {"name": "patch-name", "type": "string", "nullable": false}, {"name": "url", "type": "string", "clause": [{"type": "when", "formula": "../../../ap-update-mode/update-mode = 'web-mode'"}]}], "keys": [{"name": "k0", "fields": [":pid", "ap-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-management:wlan-management::ap-type-group-update-patchs::ap-type-group-update-patch", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ap-type", "type": "uint32", "nullable": false}, {"name": "ap-group", "type": "string", "nullable": false}, {"name": "patch-name", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ap-type", "ap-group"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-wlan-capwap", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-wlan-capwap:wlan-capwap", "type": "container", "fields": [{"name": "capwap-source", "type": "container", "fields": [{"name": "source-type", "type": "string", "default": "ip-address"}, {"name": "source-ip-version", "type": "string", "default": "ipv4"}, {"name": "source-ips", "type": "container", "fields": [{"name": "ipv4-address", "type": "string", "clause": [{"type": "when", "formula": "../../source-ip-version != 'ipv6'"}]}], "clause": [{"type": "when", "formula": "../source-type = 'ip-address'"}]}]}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-tm", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-tm:tm", "type": "container", "fields": [{"name": "timezone-configuration", "type": "container", "fields": [{"name": "timezone-name", "type": "string", "default": "DefaultZoneName"}, {"name": "option", "type": "string", "default": "add"}, {"name": "timezone-offset", "type": "string", "default": "00:00:00"}]}, {"name": "dst-configuration", "type": "container", "fields": [{"name": "dst-name", "type": "string"}, {"name": "type", "type": "string", "clause": [{"type": "when", "formula": "../dst-name"}]}, {"name": "start-year", "type": "uint16", "default": 2000, "clause": [{"type": "when", "formula": "../dst-name"}]}, {"name": "start-month", "type": "string", "clause": [{"type": "when", "formula": "../dst-name"}]}, {"name": "start-day", "type": "string", "clause": [{"type": "when", "formula": "../type = 'OneYear' and ../dst-name or ../type = 'Repeat-date' and ../dst-name"}]}, {"name": "start-weeknum", "type": "string", "clause": [{"type": "when", "formula": "../type = 'Repeat-week' and ../dst-name"}]}, {"name": "start-weekday", "type": "string", "clause": [{"type": "when", "formula": "../type = 'Repeat-week' and ../dst-name"}]}, {"name": "start-time", "type": "string", "clause": [{"type": "when", "formula": "../dst-name"}]}, {"name": "end-year", "type": "uint16", "default": 2037, "clause": [{"type": "when", "formula": "../dst-name"}]}, {"name": "end-month", "type": "string", "clause": [{"type": "when", "formula": "../dst-name"}]}, {"name": "end-day", "type": "string", "clause": [{"type": "when", "formula": "../type = 'OneYear' and ../dst-name or ../type = 'Repeat-date' and ../dst-name"}]}, {"name": "end-weeknum", "type": "string", "clause": [{"type": "when", "formula": "../type = 'Repeat-week' and ../dst-name"}]}, {"name": "end-weekday", "type": "string", "clause": [{"type": "when", "formula": "../type = 'Repeat-week' and ../dst-name"}]}, {"name": "end-time", "type": "string", "clause": [{"type": "when", "formula": "../dst-name"}]}, {"name": "dst-offset", "type": "string", "clause": [{"type": "when", "formula": "../dst-name"}]}], "clause": [{"type": "must", "formula": "((type = 'OneYear' and start-year and start-month and start-day and start-time and end-year and end-month and end-day and end-time and dst-offset) or (type = 'Repeat-date' and start-month and start-day and start-time and end-month and end-day and end-time and dst-offset) or (type = 'Repeat-week' and start-month and start-weeknum and start-weekday and start-time and end-month and end-weeknum and end-weekday and end-time and dst-offset) or not(dst-name))"}]}, {"name": "date-and-time", "type": "container", "is_config": false, "fields": [{"name": "current-time", "type": "string", "is_config": false}, {"name": "weekday", "type": "string", "is_config": false}]}, {"name": "local-time", "type": "container", "is_config": false, "fields": [{"name": "current-time", "type": "string", "is_config": false}, {"name": "weekday", "type": "string", "is_config": false}]}]}, {"name": "huawei-tm:date-time", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "date-time", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-terminal-identify", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-terminal-identify:terminal-identify", "type": "container", "fields": [{"name": "terminals", "type": "container", "is_config": false, "fields": []}, {"name": "monitoring-scan", "type": "container", "fields": [{"name": "vlans", "type": "container", "fields": []}], "clause": [{"type": "must", "formula": "count(/yang/ds[name=$DS_NAME]/huawei-terminal-identify/huawei-terminal-identify:terminal-identify/monitoring-scan/vlans/huawei-terminal-identify:terminal-identify::monitoring-scan::vlans::vlan) <= 16"}]}, {"name": "periodic-scan", "type": "container", "fields": [{"name": "vlans", "type": "container", "fields": []}], "clause": [{"type": "must", "formula": "count(/yang/ds[name=$DS_NAME]/huawei-terminal-identify/huawei-terminal-identify:terminal-identify/periodic-scan/vlans/huawei-terminal-identify:terminal-identify::periodic-scan::vlans::vlan) <= 16"}]}, {"name": "identified-terminals", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-terminal-identify:clear-terminal", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "huawei-terminal-identify:clear-terminal-by-mac", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "mac", "type": "string", "is_config": false, "nullable": false}]}, {"name": "huawei-terminal-identify:start-immediate-scan", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "scopes", "type": "container", "is_config": false, "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-terminal-identify:terminal-identify::terminals::terminal", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "mac", "type": "string", "is_config": false, "nullable": false}, {"name": "ip-address", "type": "string", "is_config": false}, {"name": "hostname", "type": "string", "is_config": false}, {"name": "update-time", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "mac"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-terminal-identify:terminal-identify::monitoring-scan::vlans::vlan", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "id", "type": "uint16", "nullable": false}, {"name": "src-ip", "type": "string"}], "keys": [{"name": "k0", "fields": [":pid", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-terminal-identify:terminal-identify::periodic-scan::vlans::vlan", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "id", "type": "uint16", "nullable": false}, {"name": "start-time", "type": "string", "nullable": false}, {"name": "src-ip", "type": "string"}, {"name": "interval", "type": "uint16", "default": 1440}], "keys": [{"name": "k0", "fields": [":pid", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-terminal-identify:terminal-identify::identified-terminals::identified-terminal", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "mac", "type": "string", "is_config": false, "nullable": false}, {"name": "ip-address", "type": "string", "is_config": false}, {"name": "category", "type": "string", "is_config": false}, {"name": "vendor", "type": "string", "is_config": false}, {"name": "model", "type": "string", "is_config": false}, {"name": "update-time", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "mac"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-terminal-identify:start-immediate-scan::scopes::scope", "type": "list", "is_config": false, "min-elements": 1, "max-elements": 512, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "src-ip", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "type", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-system:system", "type": "container", "fields": [{"name": "system-info", "type": "container", "fields": [{"name": "sys-name", "type": "string", "default": "HUAWEI"}, {"name": "sys-contact", "type": "string", "default": "R&D Beijing, Huawei Technologies co.,Ltd."}, {"name": "sys-location", "type": "string", "default": "Beijing China"}, {"name": "sys-desc", "type": "string", "is_config": false}, {"name": "sys-object-id", "type": "string", "is_config": false}, {"name": "system-gmt-time", "type": "uint32", "is_config": false}, {"name": "platform-name", "type": "string", "is_config": false}, {"name": "platform-version", "type": "string", "is_config": false}, {"name": "product-name", "type": "string", "is_config": false}, {"name": "product-version", "type": "string", "is_config": false}, {"name": "uname", "type": "string", "is_config": false}, {"name": "hardware-model", "type": "string", "is_config": false}, {"name": "patch-version", "type": "string", "is_config": false}, {"name": "esn", "type": "string", "is_config": false}, {"name": "mac", "type": "string", "is_config": false}, {"name": "software-name", "type": "string", "is_config": false}, {"name": "boot-time", "type": "string", "is_config": false}, {"name": "configuration-restoring", "type": "boolean", "is_config": false}, {"name": "huawei-system-controller:upstream-info", "type": "container", "is_config": false, "fields": [{"name": "ip-address", "type": "string", "is_config": false}]}]}, {"name": "security-risks", "type": "container", "is_config": false, "fields": []}, {"name": "weak-passwords", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-system:load-weak-password-dictionary", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "filename", "type": "string", "is_config": false}]}, {"name": "huawei-system:unload-weak-password-dictionary", "type": "container", "is_config": false, "presence": true, "fields": []}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system:system::system-infohuawei-system-controller:upstream-info::if-name", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "if-name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "if-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system:system::security-risks::security-risk", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "level", "type": "string", "is_config": false, "nullable": false}, {"name": "feature-name", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "content", "type": "string", "is_config": false, "nullable": false}, {"name": "repair-action", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "level", "feature-name", "type", "content"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system:system::weak-passwords::weak-password", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "password", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "password"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system-controller", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-system-controller:system-controller", "type": "container", "fields": [{"name": "offline-records", "type": "container", "is_config": false, "fields": []}, {"name": "register-fail-records", "type": "container", "is_config": false, "fields": []}, {"name": "register-info", "type": "container", "is_config": false, "fields": [{"name": "register-phase", "type": "string", "is_config": false}]}, {"name": "online-robustness-enhancement", "type": "container", "fields": [{"name": "enabled", "type": "boolean", "default": false}, {"name": "stable-time", "type": "uint32", "default": 8, "clause": [{"type": "when", "formula": "../enabled = true()"}]}, {"name": "retry-time", "type": "uint32", "default": 5, "clause": [{"type": "when", "formula": "../enabled = true()"}]}]}]}, {"name": "huawei-system-controller:set-register-status", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "register-status", "type": "string", "is_config": false}, {"name": "register-fail-reason", "type": "uint32", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system-controller:system-controller::offline-records::offline-record", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "time", "type": "string", "is_config": false}, {"name": "reason", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system-controller:system-controller::register-fail-records::register-fail-record", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "time", "type": "string", "is_config": false}, {"name": "reason", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-syslog", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-syslog:syslog", "type": "container", "fields": [{"name": "log-switch-list", "type": "container", "fields": []}, {"name": "logfiles", "type": "container", "is_config": false, "fields": []}, {"name": "collect-log-process", "type": "container", "is_config": false, "fields": [{"name": "state", "type": "string", "is_config": false}, {"name": "operate-progress", "type": "uint8", "is_config": false}]}, {"name": "log-storage-time", "type": "container", "fields": [{"name": "storage-time", "type": "uint32"}]}]}, {"name": "huawei-syslog:collect-log", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "server-type", "type": "choice", "is_config": false, "nullable": false, "fields": [{"name": "http", "type": "case", "is_config": false, "fields": [{"name": "http-url", "type": "string", "is_config": false}]}]}, {"name": "ssl-policy-name", "type": "string", "is_config": false, "clause": [{"type": "must", "formula": "../server-type/http/http-url"}]}]}, {"name": "huawei-syslog:check-logfile", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "file-path", "type": "string", "is_config": false, "nullable": false}]}, {"name": "huawei-syslog:save-logfile", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "log-type", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-syslog:syslog::log-switch-list::log-switch", "type": "list", "max-elements": 65535, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "feature-name", "type": "string", "nullable": false}, {"name": "log-name", "type": "string", "nullable": false}, {"name": "security-log-flag", "type": "boolean", "is_config": false}, {"name": "log-type", "type": "string", "is_config": false}, {"name": "suppress", "type": "boolean", "default": false}], "keys": [{"name": "k0", "fields": [":pid", "feature-name", "log-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-syslog:syslog::logfiles::logfile", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "logfile-type", "type": "string", "is_config": false, "nullable": false}, {"name": "latest-logs", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "logfile-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-syslog:syslog::logfiles::logfile::latest-logs::latest-log", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32", "is_config": false, "nullable": false}, {"name": "content", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "sequence"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ssl", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-ssl:ssl", "type": "container", "fields": [{"name": "ssl-policys", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ssl:ssl::ssl-policys::ssl-policy", "type": "list", "max-elements": 6, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "policy-name", "type": "string", "nullable": false}, {"name": "pki-realm", "type": "string"}], "keys": [{"name": "k0", "fields": [":pid", "policy-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sshs", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-sshs:sshs", "type": "container", "fields": [{"name": "server", "type": "container", "fields": [{"name": "pki-domain", "type": "string"}]}, {"name": "users", "type": "container", "fields": []}, {"name": "server-enable", "type": "container", "fields": [{"name": "stelnet-ipv4-enable", "type": "string", "default": "disable"}]}, {"name": "server-port", "type": "container", "fields": [{"name": "ipv4-port-number", "type": "int32", "default": 22}]}, {"name": "ipv4-server-sources", "type": "container", "fields": []}, {"name": "call-homes", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sshs:sshs::users::user", "type": "list", "max-elements": 200, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-aaa/huawei-aaa:aaa/lam/users/huawei-aaa:aaa::lam::users::user/name"}]}, {"name": "key-name", "type": "string", "clause": [{"type": "must", "formula": "../pub-key-type = 'PKI'"}, {"type": "when", "formula": "../pub-key-type"}]}, {"name": "pub-key-type", "type": "string", "default": "not-set"}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sshs:sshs::ipv4-server-sources::ipv4-server-source", "type": "list", "max-elements": 5, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "src-interface", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "src-interface"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sshs:sshs::call-homes::call-home", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "call-home-name", "type": "string", "nullable": false}, {"name": "end-points", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "call-home-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sshs:sshs::call-homes::call-home::end-points::end-point", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "end-point-name", "type": "string", "nullable": false}, {"name": "address-hostname", "type": "choice", "fields": [{"name": "ip-address", "type": "case", "fields": [{"name": "address", "type": "string", "clause": [{"type": "must", "formula": "../../../port"}]}]}, {"name": "host-name", "type": "case", "fields": [{"name": "host-name", "type": "string", "clause": [{"type": "must", "formula": "../../../port"}]}]}]}, {"name": "port", "type": "uint16"}, {"name": "connection-status", "type": "string", "is_config": false}, {"name": "enabled", "type": "boolean", "default": true}], "keys": [{"name": "k0", "fields": [":pid", "end-point-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-aaa", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-aaa:aaa", "type": "container", "fields": [{"name": "alive-user-qrys", "type": "container", "is_config": false, "fields": []}, {"name": "lam", "type": "container", "fields": [{"name": "password-policy", "type": "container", "fields": [{"name": "complexity-check-three", "type": "boolean", "default": true}]}, {"name": "users", "type": "container", "fields": []}]}]}, {"name": "huawei-aaa:cut-user-by-user-name", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "user-name", "type": "string", "is_config": false, "nullable": false}]}, {"name": "huawei-aaa:change-my-password", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "old-password", "type": "string", "is_config": false, "nullable": false}, {"name": "new-password", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-aaa:aaa::alive-user-qrys::alive-user-qry", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "user-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "user-name", "type": "string", "is_config": false}, {"name": "ip", "type": "string", "is_config": false}, {"name": "access-time", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "user-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-aaa:aaa::lam::users::user", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "password-expire-in-days", "type": "uint32"}, {"name": "group-name", "type": "string", "default": "admin"}, {"name": "password", "type": "string"}, {"name": "level", "type": "uint32"}, {"name": "service-terminal", "type": "boolean", "default": false}, {"name": "service-api", "type": "boolean", "default": false}, {"name": "password-force-change", "type": "boolean", "default": true}, {"name": "pass-modify-time", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sshc", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-sshc:sshc", "type": "container", "fields": [{"name": "client", "type": "container", "fields": [{"name": "first-time-enable", "type": "string", "default": "enable"}]}, {"name": "transfer-results", "type": "container", "is_config": false, "fields": []}, {"name": "transfer-tasks", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-sshc:ssh-transfer-file", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "server-port", "type": "uint32", "is_config": false, "default": 22}, {"name": "host-addr-ipv4", "type": "string", "is_config": false}, {"name": "command-type", "type": "string", "is_config": false, "nullable": false}, {"name": "user-name", "type": "string", "is_config": false, "nullable": false}, {"name": "password", "type": "string", "is_config": false, "nullable": false}, {"name": "local-file-name", "type": "string", "is_config": false}, {"name": "remote-file-name", "type": "string", "is_config": false}, {"name": "async", "type": "boolean", "is_config": false, "default": true}, {"name": "cancel", "type": "boolean", "is_config": false, "default": false}]}, {"name": "huawei-sshc:ssh-cancel-transfer-task", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "transfer-id", "type": "uint32", "is_config": false, "nullable": false}]}, {"name": "huawei-sshc:tcp-port-forwarding", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "operator", "type": "string", "is_config": false, "nullable": false}, {"name": "forwarding-type", "type": "string", "is_config": false, "default": "remote"}, {"name": "server-host-name", "type": "string", "is_config": false, "nullable": false}, {"name": "server-port", "type": "uint32", "is_config": false, "nullable": false}, {"name": "server-binding-port", "type": "uint32", "is_config": false, "nullable": false}, {"name": "remote-host-name", "type": "string", "is_config": false, "nullable": false}, {"name": "remote-port", "type": "uint32", "is_config": false, "nullable": false}, {"name": "server-user-name", "type": "string", "is_config": false, "nullable": false}, {"name": "authentication-type", "type": "choice", "is_config": false, "fields": [{"name": "pki", "type": "case", "is_config": false, "fields": [{"name": "pki-domain", "type": "string", "is_config": false, "clause": [{"type": "when", "formula": "../../../operator = 'create'"}]}]}]}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sshc:sshc::transfer-results::transfer-result", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "command-type", "type": "string", "is_config": false, "nullable": false}, {"name": "host-addr", "type": "string", "is_config": false, "nullable": false}, {"name": "server-port", "type": "uint32", "is_config": false, "nullable": false}, {"name": "vpn-name", "type": "string", "is_config": false, "nullable": false}, {"name": "local-file-name", "type": "string", "is_config": false, "nullable": false}, {"name": "remote-file-name", "type": "string", "is_config": false, "nullable": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "percentage", "type": "uint32", "is_config": false}, {"name": "error-tag", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "command-type", "host-addr", "server-port", "vpn-name", "local-file-name", "remote-file-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sshc:sshc::transfer-tasks::transfer-task", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "transfer-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "command-type", "type": "string", "is_config": false}, {"name": "host-addr", "type": "string", "is_config": false}, {"name": "server-port", "type": "uint32", "is_config": false}, {"name": "local-file-name", "type": "string", "is_config": false}, {"name": "remote-file-name", "type": "string", "is_config": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "percentage", "type": "uint32", "is_config": false}, {"name": "error-code", "type": "uint32", "is_config": false}, {"name": "error-message", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "transfer-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-software:software", "type": "container", "fields": [{"name": "versions", "type": "container", "is_config": false, "fields": []}, {"name": "startup-packages", "type": "container", "is_config": false, "fields": []}, {"name": "packages", "type": "container", "is_config": false, "fields": []}, {"name": "operation-schedules", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-software:startup-by-mode", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "name", "type": "string", "is_config": false, "nullable": false}]}, {"name": "huawei-software:clean-discarded-package", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "clean-type", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software:software::versions::version", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "base", "type": "string", "is_config": false, "nullable": false}, {"name": "patch", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "base"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software:software::startup-packages::startup-package", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "current-package", "type": "string", "is_config": false}, {"name": "next-package", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software:software::packages::package", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "software-name", "type": "string", "is_config": false}, {"name": "version", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software:software::operation-schedules::operation-schedule", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "operation-type", "type": "string", "is_config": false, "nullable": false}, {"name": "file-name", "type": "string", "is_config": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "fail-reason", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "operation-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software-fwd", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-software-fwd:software-fwd", "type": "container", "fields": [{"name": "fwd-statistics-infos", "type": "container", "is_config": false, "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software-fwd:software-fwd::fwd-statistics-infos::fwd-statistics-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "cpu-id", "type": "string", "is_config": false, "nullable": false}, {"name": "fwd-memory-usage", "type": "uint8", "is_config": false}, {"name": "packet-drop-nobuff", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot-id", "cpu-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-socket", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-socket:socket", "type": "container", "fields": [{"name": "tcp-global", "type": "container", "fields": [{"name": "tcp-max-mss", "type": "uint32"}]}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-smart-upgrade", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-smart-upgrade:smart-upgrade", "type": "container", "presence": true, "fields": [{"name": "global", "type": "container", "fields": [{"name": "check-new-version-interval", "type": "uint32", "default": 0}, {"name": "transport", "type": "choice", "fields": [{"name": "http", "type": "case", "fields": [{"name": "http-url", "type": "string"}, {"name": "http-port", "type": "uint32", "default": 443}, {"name": "ssl-policy", "type": "string"}]}]}]}, {"name": "smart-upgrade-info", "type": "container", "is_config": false, "fields": [{"name": "version-info", "type": "container", "is_config": false, "fields": [{"name": "refresh-time", "type": "string", "is_config": false}, {"name": "check-version", "type": "string", "is_config": false}, {"name": "upgrade-description", "type": "string", "is_config": false}, {"name": "recommended-software-version", "type": "string", "is_config": false}, {"name": "recommended-patch-version", "type": "string", "is_config": false}]}, {"name": "upgrade-info", "type": "container", "is_config": false, "fields": [{"name": "upgrade-status", "type": "string", "is_config": false}]}, {"name": "download-infos", "type": "container", "is_config": false, "fields": []}, {"name": "local-info", "type": "container", "is_config": false, "fields": [{"name": "product-group-type", "type": "string", "is_config": false}, {"name": "device-name", "type": "string", "is_config": false}, {"name": "device-esn", "type": "string", "is_config": false}, {"name": "software-version", "type": "string", "is_config": false}, {"name": "patch-version", "type": "string", "is_config": false}]}]}, {"name": "scheduled-upgrade-info", "type": "container", "is_config": false, "fields": [{"name": "scheduled-time", "type": "string", "is_config": false}, {"name": "software-version", "type": "string", "is_config": false}, {"name": "patch-version", "type": "string", "is_config": false}]}]}, {"name": "huawei-smart-upgrade:upgrade-right-now", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "choice", "is_config": false, "fields": [{"name": "upgrade-software", "type": "case", "is_config": false, "fields": [{"name": "recheck-new-version", "type": "boolean", "is_config": false, "default": true}]}, {"name": "specify-software", "type": "case", "is_config": false, "fields": [{"name": "base-version", "type": "string", "is_config": false}, {"name": "base-name", "type": "string", "is_config": false}, {"name": "patch-version", "type": "string", "is_config": false}, {"name": "patch-name", "type": "string", "is_config": false}]}]}]}, {"name": "huawei-smart-upgrade:check-new-version", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "huawei-smart-upgrade:download-software", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "choice", "is_config": false, "fields": [{"name": "specify-software", "type": "case", "is_config": false, "fields": [{"name": "version-lists", "type": "container", "is_config": false, "fields": []}]}, {"name": "specify-product", "type": "case", "is_config": false, "fields": [{"name": "product-type", "type": "string", "is_config": false}]}]}]}, {"name": "huawei-smart-upgrade:create-scheduled-upgrade", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "scheduled-time", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "choice", "is_config": false, "fields": [{"name": "specify-software", "type": "case", "is_config": false, "fields": [{"name": "software-version", "type": "string", "is_config": false}, {"name": "patch-version", "type": "string", "is_config": false}]}]}]}, {"name": "huawei-smart-upgrade:delete-scheduled-upgrade", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "huawei-smart-upgrade:check-agent-product-new-version", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "product-family", "type": "string", "is_config": false, "nullable": false}, {"name": "product-group-type", "type": "string", "is_config": false}, {"name": "product-type", "type": "string", "is_config": false, "nullable": false}, {"name": "device-esn", "type": "string", "is_config": false}, {"name": "software-version", "type": "string", "is_config": false}, {"name": "patch-version", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "product-type", "type": "string", "is_config": false, "nullable": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "failed-reason", "type": "string", "is_config": false}, {"name": "total-download-progress", "type": "uint32", "is_config": false}, {"name": "download-lists", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "product-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info::download-lists::download-list", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "version", "type": "string", "is_config": false, "nullable": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "download-file-name", "type": "string", "is_config": false}, {"name": "download-file-size", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "version"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-smart-upgrade:download-software::type::specify-software::version-lists::version-list", "type": "list", "is_config": false, "min-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "version", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "version"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sec-session-mgmt", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-sec-session-mgmt:sec-session-mgmt", "type": "container", "fields": [{"name": "protocol-ttls", "type": "container", "fields": []}, {"name": "application-ttls", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sec-session-mgmt:sec-session-mgmt::protocol-ttls::protocol-ttl", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "protocol", "type": "string", "nullable": false}, {"name": "aging-time", "type": "uint16", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "protocol"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sec-session-mgmt:sec-session-mgmt::application-ttls::application-ttl", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "application", "type": "string", "nullable": false}, {"name": "aging-time", "type": "uint16", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "application"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-acl", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-acl:acl", "type": "container", "fields": [{"name": "groups", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-acl:acl::groups::group", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "identity", "type": "string", "nullable": false}, {"name": "type", "type": "string"}, {"name": "step", "type": "uint32", "default": 5}, {"name": "description", "type": "string"}, {"name": "number", "type": "uint32"}, {"name": "rule-basics", "type": "container", "fields": []}, {"name": "rule-advances", "type": "container", "fields": []}, {"name": "rule-ethernets", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "identity"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-acl:acl::groups::group::rule-basics::rule-basic", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "id", "type": "uint32"}, {"name": "action", "type": "string", "nullable": false}, {"name": "active-status", "type": "string", "is_config": false, "default": "not-ready"}, {"name": "source-ipaddr", "type": "string", "clause": [{"type": "must", "formula": "(../source-ipaddr and ../source-wild) or (not(../source-ipaddr) and not(../source-wild))"}]}, {"name": "source-wild", "type": "string", "clause": [{"type": "must", "formula": "(../source-ipaddr and ../source-wild) or (not(../source-ipaddr) and not(../source-wild))"}]}, {"name": "fragment-type", "type": "string"}, {"name": "time-range-name", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-time-range/huawei-time-range:time-range/time-range-instances/huawei-time-range:time-range::time-range-instances::time-range-instance/name"}]}, {"name": "description", "type": "string"}, {"name": "priority", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-acl:acl::groups::group::rule-advances::rule-advance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "id", "type": "uint32"}, {"name": "action", "type": "string", "nullable": false}, {"name": "active-status", "type": "string", "is_config": false, "default": "not-ready"}, {"name": "protocol-type", "type": "choice", "nullable": false, "fields": [{"name": "single", "type": "case", "fields": [{"name": "protocol", "type": "uint8"}]}, {"name": "zero", "type": "case", "fields": [{"name": "protocol-zero", "type": "string"}]}]}, {"name": "source", "type": "choice", "fields": [{"name": "ip", "type": "case", "fields": [{"name": "source-ipaddr", "type": "string", "clause": [{"type": "must", "formula": "(../../../source/ip/source-ipaddr and ../../../source/ip/source-wild) or (not(../../../source/ip/source-ipaddr) and not(../../../source/ip/source-wild))"}]}, {"name": "source-wild", "type": "string", "clause": [{"type": "must", "formula": "(../../../source/ip/source-ipaddr and ../../../source/ip/source-wild) or (not(../../../source/ip/source-ipaddr) and not(../../../source/ip/source-wild))"}]}]}]}, {"name": "dest", "type": "choice", "fields": [{"name": "ip", "type": "case", "fields": [{"name": "dest-ipad<PERSON>", "type": "string", "clause": [{"type": "must", "formula": "(../../../dest/ip/dest-ipaddr and ../../../dest/ip/dest-wild) or (not(../../../dest/ip/dest-ipaddr) and not(../../../dest/ip/dest-wild))"}]}, {"name": "dest-wild", "type": "string", "clause": [{"type": "must", "formula": "(../../../dest/ip/dest-ipaddr and ../../../dest/ip/dest-wild) or (not(../../../dest/ip/dest-ipaddr) and not(../../../dest/ip/dest-wild))"}]}]}]}, {"name": "packets-priority", "type": "choice", "fields": [{"name": "tos", "type": "case", "fields": [{"name": "precedence", "type": "uint8"}, {"name": "tos", "type": "uint8"}]}, {"name": "dscp", "type": "case", "fields": [{"name": "dscp", "type": "uint8"}]}]}, {"name": "ttl-expired", "type": "boolean", "default": false}, {"name": "tcp-flag", "type": "choice", "fields": [{"name": "mask", "type": "case", "fields": [{"name": "tcp-flag-value", "type": "uint8", "nullable": false, "clause": [{"type": "when", "formula": "../../../protocol-type/single/protocol = 6"}]}]}]}, {"name": "source-port", "type": "choice", "fields": [{"name": "range", "type": "case", "fields": [{"name": "source-port-begin", "type": "uint16", "clause": [{"type": "must", "formula": "((../../../source-port/range/source-port-begin and ../../../source-port/range/source-port-end) and (../../../source-port/range/source-port-begin <= ../../../source-port/range/source-port-end) and not(../../../source-port/range/source-port-begin = 0 and ../../../source-port/range/source-port-end = 65535)) or (not(../../../source-port/range/source-port-begin) and not(../../../source-port/range/source-port-end))"}, {"type": "must", "formula": "(not(../../../fragment-type) or (../../../fragment-type != 'fragment' and ../../../fragment-type != 'fragment-subseq'))"}, {"type": "when", "formula": "../../../protocol-type/single/protocol = 17 or ../../../protocol-type/single/protocol = 6"}]}, {"name": "source-port-end", "type": "uint16", "clause": [{"type": "must", "formula": "((../../../source-port/range/source-port-begin and ../../../source-port/range/source-port-end) and (../../../source-port/range/source-port-begin <= ../../../source-port/range/source-port-end) and not(../../../source-port/range/source-port-begin = 0 and ../../../source-port/range/source-port-end = 65535)) or (not(../../../source-port/range/source-port-begin) and not(../../../source-port/range/source-port-end))"}, {"type": "must", "formula": "(not(../../../fragment-type) or (../../../fragment-type != 'fragment' and ../../../fragment-type != 'fragment-subseq'))"}, {"type": "when", "formula": "../../../protocol-type/single/protocol = 17 or ../../../protocol-type/single/protocol = 6"}]}]}]}, {"name": "dest-port", "type": "choice", "fields": [{"name": "range", "type": "case", "fields": [{"name": "dest-port-begin", "type": "uint16", "clause": [{"type": "must", "formula": "((../../../dest-port/range/dest-port-begin and ../../../dest-port/range/dest-port-end) and (../../../dest-port/range/dest-port-begin <= ../../../dest-port/range/dest-port-end) and not(../../../dest-port/range/dest-port-begin = 0 and ../../../dest-port/range/dest-port-end = 65535)) or (not(../../../dest-port/range/dest-port-begin) and not(../../../dest-port/range/dest-port-end))"}, {"type": "must", "formula": "(not(../../../fragment-type) or (../../../fragment-type != 'fragment' and ../../../fragment-type != 'fragment-subseq'))"}, {"type": "when", "formula": "../../../protocol-type/single/protocol = 17 or ../../../protocol-type/single/protocol = 6"}]}, {"name": "dest-port-end", "type": "uint16", "clause": [{"type": "must", "formula": "((../../../dest-port/range/dest-port-begin and ../../../dest-port/range/dest-port-end) and (../../../dest-port/range/dest-port-begin <= ../../../dest-port/range/dest-port-end) and not(../../../dest-port/range/dest-port-begin = 0 and ../../../dest-port/range/dest-port-end = 65535)) or (not(../../../dest-port/range/dest-port-begin) and not(../../../dest-port/range/dest-port-end))"}, {"type": "must", "formula": "(not(../../../fragment-type) or (../../../fragment-type != 'fragment' and ../../../fragment-type != 'fragment-subseq'))"}, {"type": "when", "formula": "../../../protocol-type/single/protocol = 17 or ../../../protocol-type/single/protocol = 6"}]}]}]}, {"name": "description", "type": "string"}, {"name": "fragment-type", "type": "string", "clause": [{"type": "must", "formula": "(../fragment-type != 'fragment' and ../fragment-type != 'fragment-subseq') or ((../fragment-type = 'fragment' or ../fragment-type = 'fragment-subseq') and (not(../dest-port/range/dest-port-begin) and not(../source-port/range/source-port-begin) and not(../dest-port/range/dest-port-end) and not(../source-port/range/source-port-end)))"}]}, {"name": "igmp-type", "type": "uint16", "clause": [{"type": "when", "formula": "../protocol-type/single/protocol = 2"}]}, {"name": "icmp-type", "type": "uint16", "clause": [{"type": "when", "formula": "../protocol-type/single/protocol = 1"}]}, {"name": "icmp-code", "type": "uint16", "clause": [{"type": "when", "formula": "../protocol-type/single/protocol = 1 and ../icmp-type"}]}, {"name": "priority", "type": "uint32", "is_config": false}, {"name": "time-range-name", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-time-range/huawei-time-range:time-range/time-range-instances/huawei-time-range:time-range::time-range-instances::time-range-instance/name"}]}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-acl:acl::groups::group::rule-ethernets::rule-ethernet", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "id", "type": "uint32"}, {"name": "action", "type": "string", "nullable": false}, {"name": "active-status", "type": "string", "is_config": false, "default": "not-ready"}, {"name": "frame-type", "type": "string"}, {"name": "frame-mask", "type": "string", "default": "0xffff", "clause": [{"type": "when", "formula": "../frame-type"}]}, {"name": "source-mac", "type": "string"}, {"name": "source-mac-mask", "type": "string", "default": "ffff-ffff-ffff", "clause": [{"type": "when", "formula": "../source-mac"}]}, {"name": "dest-mac", "type": "string"}, {"name": "dest-mac-mask", "type": "string", "default": "ffff-ffff-ffff", "clause": [{"type": "when", "formula": "../dest-mac"}]}, {"name": "vlan-id", "type": "uint16"}, {"name": "vlan-id-mask", "type": "string", "default": "0xfff", "clause": [{"type": "when", "formula": "../vlan-id"}]}, {"name": "value-8021p", "type": "uint8"}, {"name": "time-range-name", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-time-range/huawei-time-range:time-range/time-range-instances/huawei-time-range:time-range::time-range-instances::time-range-instance/name"}]}, {"name": "description", "type": "string"}, {"name": "priority", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-devm", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-devm:devm", "type": "container", "fields": [{"name": "physical-entitys", "type": "container", "is_config": false, "fields": []}, {"name": "mpu-boards", "type": "container", "is_config": false, "fields": []}, {"name": "ports", "type": "container", "fields": []}, {"name": "schedule-reboot", "type": "container", "is_config": false, "fields": [{"name": "datetime", "type": "string", "is_config": false}, {"name": "delay-time", "type": "string", "is_config": false}]}]}, {"name": "huawei-devm:reboot", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "save-flag", "type": "boolean", "is_config": false, "default": true}]}, {"name": "huawei-devm:schedule-reboot-at-time", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "schedule-date", "type": "string", "is_config": false, "nullable": false}, {"name": "schedule-datetime", "type": "string", "is_config": false, "nullable": false}, {"name": "save-flag", "type": "boolean", "is_config": false, "default": true}]}, {"name": "huawei-devm:schedule-reboot-delay-time", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "delay-time", "type": "string", "is_config": false, "nullable": false}, {"name": "save-flag", "type": "boolean", "is_config": false, "default": true}]}, {"name": "huawei-devm:undo-schedule-reboot", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "huawei-devm:get-reboot-info", "type": "container", "is_config": false, "presence": true, "fields": []}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-devm:devm::physical-entitys::physical-entity", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "class", "type": "string", "is_config": false, "nullable": false}, {"name": "position", "type": "string", "is_config": false, "nullable": false}, {"name": "serial-number", "type": "string", "is_config": false, "nullable": false}, {"name": "name", "type": "string", "is_config": false}, {"name": "entity-description", "type": "string", "is_config": false}, {"name": "alias", "type": "string", "is_config": false}, {"name": "vendor-type", "type": "string", "is_config": false}, {"name": "hardware-version", "type": "string", "is_config": false}, {"name": "software-version", "type": "string", "is_config": false}, {"name": "module-name", "type": "string", "is_config": false}, {"name": "esn", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "class", "position", "serial-number"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-devm:devm::mpu-boards::mpu-board", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "position", "type": "string", "is_config": false, "nullable": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "board-type", "type": "string", "is_config": false}, {"name": "up-time", "type": "uint32", "is_config": false}, {"name": "sdram-size", "type": "uint32", "is_config": false}, {"name": "flash-size", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "position"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-devm:devm::ports::port", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "position", "type": "string", "nullable": false}, {"name": "loopback-mode", "type": "string", "default": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "huawei-pic:ethernet", "type": "container", "fields": [{"name": "speed", "type": "string"}, {"name": "duplex", "type": "string"}, {"name": "negotiation", "type": "string", "default": "enabled"}]}, {"name": "huawei-pic:optical-module", "type": "container", "fields": [{"name": "rx-high-power-warn-en", "type": "boolean", "default": true}, {"name": "rx-low-power-warn-en", "type": "boolean", "default": true}, {"name": "tx-high-power-warn-en", "type": "boolean", "default": true}, {"name": "tx-low-power-warn-en", "type": "boolean", "default": true}, {"name": "vendor-name", "type": "string", "is_config": false}, {"name": "vendor-pn", "type": "string", "is_config": false}, {"name": "tx-power", "type": "string", "is_config": false}, {"name": "rx-power", "type": "string", "is_config": false}, {"name": "rx-high-warn-power", "type": "string", "is_config": false}, {"name": "rx-low-warn-power", "type": "string", "is_config": false}, {"name": "tx-high-warn-power", "type": "string", "is_config": false}, {"name": "tx-low-warn-power", "type": "string", "is_config": false}, {"name": "bias", "type": "int32", "is_config": false}, {"name": "temperature", "type": "int32", "is_config": false}, {"name": "voltage-float", "type": "string", "is_config": false}, {"name": "serial-number", "type": "string", "is_config": false}, {"name": "certified-state", "type": "string", "is_config": false}, {"name": "bias-high-threshold", "type": "string", "is_config": false}, {"name": "bias-low-threshold", "type": "string", "is_config": false}, {"name": "voltage-high-threshold-float", "type": "string", "is_config": false}, {"name": "voltage-low-threshold-float", "type": "string", "is_config": false}, {"name": "temperature-high-threshold-float", "type": "string", "is_config": false}, {"name": "temperature-low-threshold-float", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid", "position"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-pki:pki", "type": "container", "fields": [{"name": "global", "type": "container", "fields": [{"name": "certificate-check", "type": "container", "fields": [{"name": "validate-method", "type": "string", "default": "crl-none"}]}]}, {"name": "entitys", "type": "container", "fields": []}, {"name": "domains", "type": "container", "fields": []}, {"name": "certificate-infos", "type": "container", "is_config": false, "fields": []}, {"name": "preset-certificate-infos", "type": "container", "is_config": false, "fields": []}, {"name": "crl-infos", "type": "container", "is_config": false, "fields": []}, {"name": "key-pair-infos", "type": "container", "is_config": false, "fields": []}, {"name": "cert-key-pair-infos", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-pki:key-pair-create", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "key-pairs", "type": "container", "is_config": false, "fields": []}]}, {"name": "hua<PERSON>-pki:key-pair-destroy", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "key-pairs", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-pki:key-pair-import", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "key-pairs", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-pki:certificate-import", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "certificates", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-pki:certificate-delete", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "certificates", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-pki:certificate-delete-by-domain", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "domain-certificates", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-pki:certificate-replace", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "certificates", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-pki:csr-generate", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "csrs", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-pki:crl-import", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "crls", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-pki:crl-delete", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "crls", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-pki:crl-delete-by-domain", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "crls", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-pki:cert-validate-by-domain", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "domain-name", "type": "string", "is_config": false, "nullable": false}]}, {"name": "huawei-pki:preset-cert-validate", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false, "clause": [{"type": "must", "formula": "current() = 'rsa' or current() = 'ecc'"}]}]}, {"name": "huawei-pki:reset-factory-setting", "type": "container", "is_config": false, "presence": true, "fields": []}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::entitys::entity", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "common-name", "type": "string"}, {"name": "fqdn", "type": "string"}, {"name": "department", "type": "string"}, {"name": "organization", "type": "string"}, {"name": "locality", "type": "string"}, {"name": "state", "type": "string"}, {"name": "country", "type": "string"}, {"name": "email", "type": "string"}, {"name": "address", "type": "choice", "fields": [{"name": "ip-address", "type": "case", "fields": [{"name": "ip-address", "type": "string"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::domains::domain", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "entity", "type": "string"}, {"name": "digest-algorithm", "type": "string", "default": "sha-256"}, {"name": "key-pair", "type": "container", "presence": true, "fields": [{"name": "name", "type": "string", "nullable": false}, {"name": "type", "type": "string", "nullable": false}]}, {"name": "key-usage", "type": "string"}, {"name": "validate-method", "type": "string"}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::certificate-infos::certificate-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "domain-name", "type": "string", "is_config": false, "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "certificates", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "type", "domain-name", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::certificate-infos::certificate-info::certificates::certificate", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "issuer", "type": "string", "is_config": false, "nullable": false}, {"name": "subject", "type": "string", "is_config": false, "nullable": false}, {"name": "serial-number", "type": "string", "is_config": false}, {"name": "version", "type": "string", "is_config": false}, {"name": "not-before", "type": "string", "is_config": false}, {"name": "not-after", "type": "string", "is_config": false}, {"name": "signature-algorithm", "type": "string", "is_config": false}, {"name": "fingerprint", "type": "string", "is_config": false}, {"name": "key-pair-type", "type": "string", "is_config": false}, {"name": "key-size", "type": "uint32", "is_config": false}, {"name": "curve-type", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "issuer", "subject"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "cert-type", "type": "string", "is_config": false, "nullable": false}, {"name": "key-type", "type": "string", "is_config": false, "nullable": false}, {"name": "certificates", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "slot", "cert-type", "key-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info::certificates::certificate", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "issuer", "type": "string", "is_config": false, "nullable": false}, {"name": "subject", "type": "string", "is_config": false, "nullable": false}, {"name": "serial-number", "type": "string", "is_config": false}, {"name": "version", "type": "string", "is_config": false}, {"name": "not-before", "type": "string", "is_config": false}, {"name": "not-after", "type": "string", "is_config": false}, {"name": "signature-algorithm", "type": "string", "is_config": false}, {"name": "fingerprint", "type": "string", "is_config": false}, {"name": "key-pair-type", "type": "string", "is_config": false}, {"name": "key-size", "type": "uint32", "is_config": false}, {"name": "curve-type", "type": "string", "is_config": false}, {"name": "device-info", "type": "container", "is_config": false, "fields": [{"name": "cpu-id", "type": "string", "is_config": false}, {"name": "np-id", "type": "string", "is_config": false}, {"name": "htm-id", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid", "issuer", "subject"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::crl-infos::crl-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "domain-name", "type": "string", "is_config": false, "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "crl", "type": "container", "is_config": false, "fields": [{"name": "version", "type": "string", "is_config": false}, {"name": "issuer", "type": "string", "is_config": false}, {"name": "last-update", "type": "string", "is_config": false}, {"name": "next-update", "type": "string", "is_config": false}, {"name": "signature-algorithm", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid", "domain-name", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::key-pair-infos::key-pair-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "key-size", "type": "uint32", "is_config": false}, {"name": "curve-type", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name", "type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "cert-name", "type": "string", "is_config": false, "nullable": false}, {"name": "cert-key-pairs", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "cert-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info::cert-key-pairs::cert-key-pair", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "issuer", "type": "string", "is_config": false, "nullable": false}, {"name": "subject", "type": "string", "is_config": false, "nullable": false}, {"name": "key-type", "type": "string", "is_config": false, "nullable": false}, {"name": "key-name", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "issuer", "subject", "key-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:key-pair-create::key-pairs::key-pair", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "key-size", "type": "uint32", "is_config": false}, {"name": "curve-type", "type": "string", "is_config": false, "nullable": false, "clause": [{"type": "when", "formula": "../type = 'ecc'"}]}], "keys": [{"name": "k0", "fields": [":pid", "name", "type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:key-pair-destroy::key-pairs::key-pair", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name", "type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:key-pair-import::key-pairs::key-pair", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "format", "type": "string", "is_config": false, "nullable": false}, {"name": "file-name", "type": "string", "is_config": false, "nullable": false}, {"name": "password", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name", "type"], "index": {"type": "primary"}, "constraints": {"unique": true}}], "clause": [{"type": "must", "formula": "(type = 'rsa' and (format = 'pem' or format = 'pkcs12')) or (type = 'ecc' and (format = 'pem' or format = 'pkcs12')) or (type = 'sm2' and (format = 'pem' or format = 'der'))"}]}, {"name": "huawei-pki:certificate-import::certificates::certificate", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "domain-name", "type": "string", "is_config": false, "nullable": false}, {"name": "format", "type": "string", "is_config": false}, {"name": "password", "type": "string", "is_config": false, "nullable": false, "clause": [{"type": "when", "formula": "../format = 'pkcs12'"}]}], "keys": [{"name": "k0", "fields": [":pid", "name", "type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:certificate-delete::certificates::certificate", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name", "type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:certificate-delete-by-domain::domain-certificates::domain-certificate", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "domain-name", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "domain-name", "type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:certificate-replace::certificates::certificate", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "file-name", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "domain-name", "type": "string", "is_config": false, "nullable": false}, {"name": "password", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "file-name", "type", "domain-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:csr-generate::csrs::csr", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "domain-name", "type": "string", "is_config": false, "nullable": false}, {"name": "file-name", "type": "string", "is_config": false, "nullable": false}, {"name": "password", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "domain-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:crl-import::crls::crl", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "file-name", "type": "string", "is_config": false, "nullable": false}, {"name": "domain-name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "file-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:crl-delete::crls::crl", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:crl-delete-by-domain::crls::crl", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "domain-name", "type": "string", "is_config": false, "nullable": false}, {"name": "name", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "domain-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "<PERSON><PERSON><PERSON>-driver", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "<PERSON><PERSON><PERSON>-driver:driver", "type": "container", "fields": [{"name": "global-attribute", "type": "container", "fields": [{"name": "system-mac-address", "type": "string", "is_config": false}, {"name": "system-mac-number", "type": "uint32", "is_config": false}, {"name": "factory-configuration-button-switch", "type": "string", "default": "enable"}, {"name": "huawei-pic:global", "type": "container", "fields": [{"name": "non-certified-optical-status-alarm", "type": "boolean", "default": true}]}]}, {"name": "temperature2s", "type": "container", "is_config": false, "fields": []}, {"name": "device-health-checks", "type": "container", "is_config": false, "fields": []}, {"name": "electronic-labels", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-driver:reboot-with-factory-configuration", "type": "container", "is_config": false, "presence": true, "fields": []}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-driver:driver::temperature2s::temperature2", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "position", "type": "string", "is_config": false, "nullable": false}, {"name": "sensor-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "sensor-name", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "current-temperature", "type": "int32", "is_config": false}, {"name": "minor-threshold", "type": "int32", "is_config": false}, {"name": "major-threshold", "type": "int32", "is_config": false}, {"name": "fatal-threshold", "type": "int32", "is_config": false}, {"name": "low-threshold", "type": "int32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "position", "sensor-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-driver:driver::device-health-checks::device-health-check", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "item", "type": "string", "is_config": false, "nullable": false}, {"name": "status", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "item"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-driver:driver::electronic-labels::electronic-label", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "entity-class", "type": "string", "is_config": false, "nullable": false}, {"name": "position", "type": "string", "is_config": false, "nullable": false}, {"name": "entity-serial-number", "type": "uint32", "is_config": false, "nullable": false}, {"name": "entity-bomid", "type": "string", "is_config": false}, {"name": "barcode", "type": "string", "is_config": false}, {"name": "bom-english-desc", "type": "string", "is_config": false}, {"name": "manufacturer-name", "type": "string", "is_config": false}, {"name": "manufacturer-date", "type": "string", "is_config": false}, {"name": "manufacturer-code", "type": "string", "is_config": false}, {"name": "board-type", "type": "string", "is_config": false}, {"name": "entity-clei-code", "type": "string", "is_config": false}, {"name": "entity-open-bomid", "type": "string", "is_config": false}, {"name": "entity-issue-number", "type": "string", "is_config": false}, {"name": "entity-model", "type": "string", "is_config": false}, {"name": "entity-elabel-version", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "entity-class", "position", "entity-serial-number"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-patch", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-patch:patch", "type": "container", "fields": [{"name": "patch-infos", "type": "container", "is_config": false, "fields": []}, {"name": "next-startup-patchs", "type": "container", "is_config": false, "fields": []}, {"name": "operation-schedules", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-patch:load-patch", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "load-type", "type": "string", "is_config": false, "nullable": false}]}, {"name": "huawei-patch:startup-next-patch", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "name", "type": "string", "is_config": false, "nullable": false}]}, {"name": "huawei-patch:delete-patch", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "delete-type", "type": "string", "is_config": false, "default": "all"}]}, {"name": "huawei-patch:reset-startup-patch", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "delete-type", "type": "string", "is_config": false, "default": "all"}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-patch:patch::patch-infos::patch-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "version", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "runtime", "type": "string", "is_config": false}, {"name": "path", "type": "string", "is_config": false}, {"name": "operations", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-patch:patch::patch-infos::patch-info::operations::operation", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "position", "type": "string", "is_config": false, "nullable": false}, {"name": "position-type", "type": "string", "is_config": false}, {"name": "upgrade-mode", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "position"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-patch:patch::next-startup-patchs::next-startup-patch", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-patch:patch::operation-schedules::operation-schedule", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "phase", "type": "string", "is_config": false, "nullable": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "schedule", "type": "uint8", "is_config": false}, {"name": "fail-reason", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "phase"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-file-operation", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-file-operation:file-operation", "type": "container", "is_config": false, "fields": [{"name": "dirs", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-file-operation:delete-file", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "file-name", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-file-operation:file-operation::dirs::dir", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "file-name", "type": "string", "is_config": false, "nullable": false}, {"name": "dir-name", "type": "string", "is_config": false, "nullable": false}, {"name": "attribute", "type": "string", "is_config": false}, {"name": "modify-time", "type": "string", "is_config": false}, {"name": "size", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "file-name", "dir-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-openconfig-telemetry-ext", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-openconfig-telemetry-ext:resync-subscription", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "subscription", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-openconfig-telemetry-ext:resync-subscription::sensor-profile", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "sensor-profile", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "sensor-profile"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ntp", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-ntp:ntp", "type": "container", "fields": [{"name": "unicasts", "type": "container", "fields": []}, {"name": "authentications", "type": "container", "fields": []}, {"name": "status", "type": "container", "is_config": false, "fields": [{"name": "clock-status", "type": "string", "is_config": false}, {"name": "stratum", "type": "int32", "is_config": false}, {"name": "source", "type": "string", "is_config": false}, {"name": "precision", "type": "string", "is_config": false}, {"name": "offset", "type": "string", "is_config": false}, {"name": "nominal-frequence", "type": "string", "is_config": false}, {"name": "actual-frequence", "type": "string", "is_config": false}, {"name": "root-delay", "type": "string", "is_config": false}, {"name": "root-dispersion", "type": "string", "is_config": false}, {"name": "peer-dispersion", "type": "string", "is_config": false}, {"name": "reference-time", "type": "string", "is_config": false}, {"name": "sync-state", "type": "string", "is_config": false}]}, {"name": "full-sessions", "type": "container", "is_config": false, "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ntp:ntp::unicasts::unicast", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ip-address", "type": "string", "nullable": false}, {"name": "type", "type": "string", "nullable": false}, {"name": "vpn-name", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-network-instance/huawei-network-instance:network-instance/instances/huawei-network-instance:network-instance::instances::instance/name"}]}, {"name": "is-preferred", "type": "boolean", "default": false}, {"name": "ifname", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface/name"}, {"type": "must", "formula": "/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface[name = current()]/vrf-name = ../vpn-name"}]}, {"name": "key-id", "type": "uint32", "clause": [{"type": "leafref", "formula": "../../../authentications/huawei-ntp:ntp::authentications::authentication/key-id"}]}, {"name": "max-poll-interval", "type": "uint8", "default": 10}, {"name": "min-poll-interval", "type": "uint8", "default": 6}], "keys": [{"name": "k0", "fields": [":pid", "ip-address", "type", "vpn-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ntp:ntp::authentications::authentication", "type": "list", "max-elements": 6, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "key-id", "type": "uint32", "nullable": false}, {"name": "mode", "type": "string", "nullable": false}, {"name": "key-value", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "key-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ntp:ntp::full-sessions::full-session", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "source", "type": "string", "is_config": false, "nullable": false}, {"name": "local-mode", "type": "string", "is_config": false, "nullable": false}, {"name": "vpn-name", "type": "string", "is_config": false, "nullable": false}, {"name": "ifname", "type": "string", "is_config": false, "nullable": false}, {"name": "stratum", "type": "uint8", "is_config": false}, {"name": "current-poll", "type": "string", "is_config": false}, {"name": "offset", "type": "string", "is_config": false}, {"name": "delay", "type": "string", "is_config": false}, {"name": "reachable", "type": "uint8", "is_config": false}, {"name": "when", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "source", "local-mode", "vpn-name", "ifname"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-netconf-sync", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-netconf-sync:sync-increment", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "target", "type": "container", "is_config": false, "fields": [{"name": "flow-id", "type": "uint32", "is_config": false, "nullable": false}]}, {"name": "source", "type": "container", "is_config": false, "fields": [{"name": "flow-id", "type": "uint32", "is_config": false, "nullable": false, "clause": [{"type": "must", "formula": "not(../../target/flow-id) or (../../target/flow-id and ../flow-id < ../../target/flow-id)"}]}]}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-server", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-nat-server:nat-server", "type": "container", "fields": [{"name": "server-mappings", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-server:nat-server::server-mappings::server-mapping", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "protocol", "type": "string"}, {"name": "global", "type": "container", "fields": [{"name": "global-element", "type": "choice", "nullable": false, "fields": [{"name": "global-ip", "type": "case", "fields": [{"name": "start-ip", "type": "string", "nullable": false}, {"name": "end-ip", "type": "string"}]}]}]}, {"name": "global-port", "type": "container", "fields": [{"name": "start-port", "type": "uint16", "clause": [{"type": "must", "formula": "../../protocol"}]}, {"name": "end-port", "type": "uint16"}]}, {"name": "inside", "type": "container", "fields": [{"name": "start-ip", "type": "string", "nullable": false}, {"name": "end-ip", "type": "string"}]}, {"name": "inside-port", "type": "container", "fields": [{"name": "start-port", "type": "uint16", "clause": [{"type": "must", "formula": "../../protocol"}]}, {"name": "end-port", "type": "uint16"}]}, {"name": "reverse-enable", "type": "boolean", "default": true}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-nat-policy:nat-policy", "type": "container", "fields": [{"name": "rules", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "action", "type": "container", "fields": [{"name": "action", "type": "choice", "fields": [{"name": "no-nat", "type": "case", "fields": [{"name": "no-nat", "type": "string"}]}, {"name": "do-nat", "type": "case", "fields": [{"name": "source-nat", "type": "container", "fields": [{"name": "mode", "type": "choice", "fields": [{"name": "address-group", "type": "case", "fields": [{"name": "address-group-name", "type": "string"}]}, {"name": "easy-ip", "type": "case", "fields": [{"name": "easy-ip", "type": "string"}]}]}]}]}]}]}, {"name": "egress", "type": "choice", "fields": [{"name": "interfaces", "type": "case", "fields": []}]}, {"name": "source-address", "type": "container", "fields": [{"name": "address-ipv4s", "type": "container", "fields": []}, {"name": "address-ipv4-ranges", "type": "container", "fields": []}, {"name": "address-ipv4-excludes", "type": "container", "fields": []}, {"name": "address-ipv4-range-excludes", "type": "container", "fields": []}]}, {"name": "destination-address", "type": "container", "fields": [{"name": "address-ipv4s", "type": "container", "fields": []}, {"name": "address-ipv4-ranges", "type": "container", "fields": []}, {"name": "address-ipv4-excludes", "type": "container", "fields": []}, {"name": "address-ipv4-range-excludes", "type": "container", "fields": []}]}, {"name": "service", "type": "container", "fields": [{"name": "service-items", "type": "container", "fields": [{"name": "protocol-and-ports", "type": "container", "fields": []}, {"name": "icmpv4s", "type": "container", "fields": []}, {"name": "protocol", "type": "container", "fields": []}]}, {"name": "service-items-exclude", "type": "container", "fields": [{"name": "protocol-and-ports", "type": "container", "fields": []}, {"name": "icmpv4s", "type": "container", "fields": []}, {"name": "protocol", "type": "container", "fields": []}]}]}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::egress::interfaces::egress-interface", "type": "leaf-list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "egress-interface", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-ifm/huawei-ifm:ifm/interfaces/huawei-ifm:ifm::interfaces::interface/name"}]}], "keys": [{"name": "k0", "fields": [":pid", "egress-interface"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4s::address-ipv4", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ipv4", "type": "string", "nullable": false}, {"name": "mask", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ipv4", "mask"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-ranges::address-ipv4-range", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "start-ipv4", "type": "string", "nullable": false}, {"name": "end-ipv4", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "start-ipv4", "end-ipv4"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-excludes::address-ipv4-exclude", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ipv4", "type": "string", "nullable": false}, {"name": "mask", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ipv4", "mask"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-range-excludes::address-ipv4-range-exclude", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "start-ipv4", "type": "string", "nullable": false}, {"name": "end-ipv4", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "start-ipv4", "end-ipv4"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4s::address-ipv4", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ipv4", "type": "string", "nullable": false}, {"name": "mask", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ipv4", "mask"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-ranges::address-ipv4-range", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "start-ipv4", "type": "string", "nullable": false}, {"name": "end-ipv4", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "start-ipv4", "end-ipv4"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-excludes::address-ipv4-exclude", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ipv4", "type": "string", "nullable": false}, {"name": "mask", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ipv4", "mask"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-range-excludes::address-ipv4-range-exclude", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "start-ipv4", "type": "string", "nullable": false}, {"name": "end-ipv4", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "start-ipv4", "end-ipv4"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::protocol-and-ports::protocol-and-port", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "protocol", "type": "string", "nullable": false}, {"name": "source-port", "type": "string", "nullable": false}, {"name": "dest-port", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "protocol", "source-port", "dest-port"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::icmpv4s::icmpv4", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "uint8", "nullable": false}, {"name": "code", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "type", "code"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::protocol::protocol-id", "type": "leaf-list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "protocol-id", "type": "uint8", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "protocol-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::protocol-and-ports::protocol-and-port", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "protocol", "type": "string", "nullable": false}, {"name": "source-port", "type": "string", "nullable": false}, {"name": "dest-port", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "protocol", "source-port", "dest-port"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::icmpv4s::icmpv4", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "uint8", "nullable": false}, {"name": "code", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "type", "code"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::protocol::protocol-id", "type": "leaf-list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "protocol-id", "type": "uint8", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "protocol-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-address-group", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-nat-address-group:nat-address-group", "type": "container", "fields": [{"name": "snat-address-groups", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "mode", "type": "string", "default": "pat"}, {"name": "sections", "type": "container", "fields": []}, {"name": "exclude-ips", "type": "container", "fields": []}, {"name": "exclude-ports", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::sections::section", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "id", "type": "uint16", "nullable": false}, {"name": "start-ip", "type": "string", "nullable": false}, {"name": "end-ip", "type": "string"}], "keys": [{"name": "k0", "fields": [":pid", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::exclude-ips::exclude-ip", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "start-ip", "type": "string", "nullable": false}, {"name": "end-ip-or-mask", "type": "choice", "fields": [{"name": "end-ip", "type": "case", "fields": [{"name": "end-ip", "type": "string"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "start-ip"], "index": {"type": "primary"}, "constraints": {"unique": true}}], "clause": [{"type": "must", "formula": "(../../sections/huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::sections::section/id)"}]}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::exclude-ports::exclude-port", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "start-port", "type": "uint16", "nullable": false}, {"name": "end-port", "type": "uint16"}], "keys": [{"name": "k0", "fields": [":pid", "start-port"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-module-management", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-module-management:module-management", "type": "container", "is_config": false, "fields": [{"name": "module-infos", "type": "container", "is_config": false, "fields": []}, {"name": "operation-schedules", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-module-management:install-module", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "name", "type": "string", "is_config": false, "nullable": false}]}, {"name": "huawei-module-management:uninstall-module", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "action-type", "type": "string", "is_config": false, "default": "single"}, {"name": "name", "type": "string", "is_config": false, "clause": [{"type": "when", "formula": "../action-type != 'all'"}]}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-module-management:module-management::module-infos::module-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "package-name", "type": "string", "is_config": false, "nullable": false}, {"name": "version", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "runtime", "type": "string", "is_config": false}, {"name": "path", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "package-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-module-management:module-management::operation-schedules::operation-schedule", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "phase", "type": "string", "is_config": false, "nullable": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "schedule", "type": "uint8", "is_config": false}, {"name": "fail-reason", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "phase"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "hua<PERSON>-masterkey", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-masterkey:masterkey", "type": "container", "fields": [{"name": "current-type", "type": "container", "is_config": false, "fields": [{"name": "type", "type": "string", "is_config": false}]}, {"name": "modify-result", "type": "container", "is_config": false, "fields": [{"name": "modify-start-time", "type": "string", "is_config": false}, {"name": "modify-end-time", "type": "string", "is_config": false}, {"name": "modify-result", "type": "string", "is_config": false}, {"name": "modify-error-reason", "type": "string", "is_config": false}]}, {"name": "auto-update", "type": "container", "fields": [{"name": "interval", "type": "uint32"}]}, {"name": "current-master<PERSON>", "type": "container", "is_config": false, "fields": [{"name": "domain-id", "type": "uint32", "is_config": false}, {"name": "key-id", "type": "uint32", "is_config": false}, {"name": "create-time", "type": "string", "is_config": false}, {"name": "expired-time", "type": "string", "is_config": false}]}]}, {"name": "huawei-masterkey:set-masterkey", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "new-masterkey", "type": "string", "is_config": false}]}, {"name": "huawei-masterkey:clear-masterkey", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "huawei-masterkey:check-masterkey-sync-status", "type": "container", "is_config": false, "presence": true, "fields": []}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-iot-pnp", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-iot-pnp:iot-pnp", "type": "container", "fields": [{"name": "device-type", "type": "string"}, {"name": "domains", "type": "container", "fields": []}, {"name": "statistics", "type": "container", "is_config": false, "fields": [{"name": "non-ip-packets", "type": "uint32", "is_config": false}, {"name": "non-udp-packets", "type": "uint32", "is_config": false}, {"name": "non-target-proto-packets", "type": "uint32", "is_config": false}, {"name": "unicast-packets", "type": "uint32", "is_config": false}, {"name": "ssdp-packets", "type": "uint32", "is_config": false}, {"name": "coap-packets", "type": "uint32", "is_config": false}]}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-iot-pnp:iot-pnp::domains::domain", "type": "list", "max-elements": 16, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "domain-name", "type": "string", "nullable": false}, {"name": "vlans", "type": "container", "fields": []}, {"name": "protocols", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "domain-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-iot-pnp:iot-pnp::domains::domain::vlans::vlan", "type": "leaf-list", "max-elements": 16, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "vlan", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "vlan"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-iot-pnp:iot-pnp::domains::domain::protocols::protocol", "type": "leaf-list", "max-elements": 2, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "protocol", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "protocol"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-http", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-http:http", "type": "container", "fields": [{"name": "httpc-transfer-results", "type": "container", "is_config": false, "fields": []}, {"name": "transfer-tasks", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-http:httpc-transfer-file", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "operation-type", "type": "string", "is_config": false, "nullable": false}, {"name": "file-url", "type": "string", "is_config": false, "nullable": false}, {"name": "file-full-path", "type": "string", "is_config": false, "nullable": false}, {"name": "ssl-policy-name", "type": "string", "is_config": false}, {"name": "ssl-verify", "type": "boolean", "is_config": false, "default": true, "clause": [{"type": "must", "formula": "(current() != true()) or (../ssl-policy-name)"}]}, {"name": "async", "type": "boolean", "is_config": false, "default": true}, {"name": "cancel", "type": "boolean", "is_config": false, "default": false}]}, {"name": "huawei-http:httpc-cancel-transfer-task", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "transfer-id", "type": "uint32", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-http:http::httpc-transfer-results::httpc-transfer-result", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "operation-type", "type": "string", "is_config": false, "nullable": false}, {"name": "file-url", "type": "string", "is_config": false, "nullable": false}, {"name": "transfer-status", "type": "string", "is_config": false}, {"name": "percentage", "type": "uint32", "is_config": false}, {"name": "error-tag", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "operation-type", "file-url"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-http:http::transfer-tasks::transfer-task", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "transfer-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "operation-type", "type": "string", "is_config": false}, {"name": "file-url", "type": "string", "is_config": false}, {"name": "file-full-path", "type": "string", "is_config": false}, {"name": "transfer-status", "type": "string", "is_config": false}, {"name": "percentage", "type": "uint32", "is_config": false}, {"name": "error-code", "type": "uint32", "is_config": false}, {"name": "error-message", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "transfer-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "hua<PERSON>-host-security", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-host-security:host-security", "type": "container", "fields": [{"name": "anti-attacks", "type": "container", "fields": []}, {"name": "host-cars", "type": "container", "fields": [{"name": "enable", "type": "boolean", "default": false}, {"name": "cir", "type": "uint32"}]}, {"name": "packet-statistics", "type": "container", "is_config": false, "fields": []}, {"name": "adjust-car", "type": "container", "fields": [{"name": "enable", "type": "boolean", "default": false}]}, {"name": "policys", "type": "container", "fields": []}]}, {"name": "huawei-host-security:clear-packet-statistic", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "packet-type", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-host-security:host-security::anti-attacks::anti-attack", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "anti-attack-type", "type": "string", "nullable": false}, {"name": "enable", "type": "boolean"}, {"name": "para", "type": "uint32"}], "keys": [{"name": "k0", "fields": [":pid", "anti-attack-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}], "clause": [{"type": "must", "formula": "(((anti-attack-type = 'icmp-flood' or anti-attack-type = 'tcp-syn' or anti-attack-type = 'udp-flood') and (not(not(para) and enable = true()))) or ((anti-attack-type = 'fragment' or anti-attack-type = 'abnormal') and (not(para))))"}]}, {"name": "huawei-host-security:host-security::host-cars::host-protocol-type", "type": "leaf-list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "host-protocol-type", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "host-protocol-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-host-security:host-security::packet-statistics::packet-statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "packet-type", "type": "string", "is_config": false, "nullable": false}, {"name": "pass-packets", "type": "uint64", "is_config": false}, {"name": "drop-packets", "type": "uint64", "is_config": false}, {"name": "pass-bytes", "type": "uint64", "is_config": false}, {"name": "drop-bytes", "type": "uint64", "is_config": false}, {"name": "protocol-status", "type": "container", "is_config": false, "fields": [{"name": "status", "type": "boolean", "is_config": false}, {"name": "car-value", "type": "uint32", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid", "slot", "packet-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-host-security:host-security::adjust-car::adjust-protocol-type", "type": "leaf-list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "adjust-protocol-type", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "adjust-protocol-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-host-security:host-security::policys::policy", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "filters", "type": "container", "fields": []}, {"name": "cpcars", "type": "container", "fields": []}, {"name": "auto-defend", "type": "container", "fields": [{"name": "enable", "type": "boolean", "default": true}, {"name": "alarm-enable", "type": "boolean", "default": true}, {"name": "alarm-threshold", "type": "uint32", "default": 128}, {"name": "penalty-enable", "type": "boolean", "default": false}, {"name": "penalty-threshold", "type": "uint32", "default": 128}]}, {"name": "applied-policys", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-host-security:host-security::policys::policy::filters::filter", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "filter-id", "type": "uint32", "nullable": false}, {"name": "acl-type", "type": "choice", "nullable": false, "fields": [{"name": "ipv4", "type": "case", "fields": [{"name": "ipv4-acl", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-acl/huawei-acl:acl/groups/huawei-acl:acl::groups::group/identity"}]}]}]}], "keys": [{"name": "k0", "fields": [":pid", "filter-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-host-security:host-security::policys::policy::cpcars::cpcar", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "protocol-type", "type": "string", "nullable": false}, {"name": "cir", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "protocol-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-host-security:host-security::policys::policy::auto-defend::defend-type", "type": "leaf-list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "defend-type", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "defend-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-host-security:host-security::policys::policy::applied-policys::applied-policy", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "applied-type", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "applied-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ftpc", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-ftpc:ftpc", "type": "container", "fields": [{"name": "client", "type": "container", "fields": [{"name": "enabled", "type": "boolean", "default": false}]}, {"name": "transfer-tasks", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-ftpc:ftpc-transfer-file", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "server-port", "type": "uint32", "is_config": false}, {"name": "server-ipv4-address", "type": "string", "is_config": false}, {"name": "command-type", "type": "string", "is_config": false, "nullable": false}, {"name": "user-name", "type": "string", "is_config": false, "nullable": false}, {"name": "password", "type": "string", "is_config": false, "nullable": false}, {"name": "local-file-name", "type": "string", "is_config": false}, {"name": "remote-file-name", "type": "string", "is_config": false}]}, {"name": "huawei-ftpc:ftpc-cancel-transfer-task", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "transfer-id", "type": "uint32", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ftpc:ftpc::transfer-tasks::transfer-task", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "transfer-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "command-type", "type": "string", "is_config": false}, {"name": "server-address", "type": "string", "is_config": false}, {"name": "server-port", "type": "uint16", "is_config": false}, {"name": "local-file-name", "type": "string", "is_config": false}, {"name": "remote-file-name", "type": "string", "is_config": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "percentage", "type": "uint32", "is_config": false}, {"name": "error-message", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "transfer-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-fm", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-fm:fm", "type": "container", "fields": [{"name": "alarms", "type": "container", "is_config": false, "fields": []}, {"name": "active-alarms", "type": "container", "is_config": false, "fields": []}, {"name": "history-alarms", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-fm:clear-all-history-alarm", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "huawei-fm:clear-history-alarm", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "alarm-sequence", "type": "uint32", "is_config": false, "nullable": false}]}, {"name": "huawei-fm:clear-all-active-alarm", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "huawei-fm:clear-active-alarm", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "alarm-sequence", "type": "uint32", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-fm:fm::alarms::alarm", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "alarm-name", "type": "string", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false}, {"name": "clear-suppress-time", "type": "uint32", "is_config": false}, {"name": "cause-suppress-time", "type": "uint32", "is_config": false}, {"name": "module-name", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "alarm-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-fm:fm::active-alarms::active-alarm", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32", "is_config": false, "nullable": false}, {"name": "alarm-name", "type": "string", "is_config": false}, {"name": "alarm-id", "type": "uint32", "is_config": false}, {"name": "level", "type": "string", "is_config": false}, {"name": "generated-time", "type": "string", "is_config": false}, {"name": "description", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "sequence"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-fm:fm::history-alarms::history-alarm", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "alarm-sequence", "type": "uint32", "is_config": false, "nullable": false}, {"name": "alarm-name", "type": "string", "is_config": false}, {"name": "alarm-id", "type": "uint32", "is_config": false}, {"name": "level", "type": "string", "is_config": false}, {"name": "generated-time", "type": "string", "is_config": false}, {"name": "cleared-time", "type": "string", "is_config": false}, {"name": "description", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "alarm-sequence"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt", "type": "container", "fields": [{"name": "network", "type": "container", "fields": [{"name": "working-mode", "type": "string"}, {"name": "online-upgrade-enable", "type": "boolean", "default": false}, {"name": "account", "type": "container", "presence": true, "fields": [{"name": "name", "type": "string", "nullable": false}, {"name": "password", "type": "string", "nullable": false}]}, {"name": "device-granteds", "type": "container", "fields": []}, {"name": "timer", "type": "container", "fields": [{"name": "upgrade-datetime", "type": "string", "clause": [{"type": "must", "formula": "../../online-upgrade-enable = true()"}]}, {"name": "schedule-reboot", "type": "container", "fields": [{"name": "schedule-type", "type": "choice", "fields": [{"name": "once", "type": "case", "fields": [{"name": "reboot-datetime", "type": "string"}]}, {"name": "weekly", "type": "case", "fields": [{"name": "weekday", "type": "string"}, {"name": "weekday-time", "type": "string", "nullable": false, "clause": [{"type": "when", "formula": "../../../schedule-type/weekly/weekday"}]}]}, {"name": "monthly", "type": "case", "fields": [{"name": "day", "type": "string"}, {"name": "day-time", "type": "string", "nullable": false, "clause": [{"type": "when", "formula": "../../../schedule-type/monthly/day"}]}]}]}]}]}, {"name": "networks", "type": "container", "fields": [{"name": "vlans", "type": "container", "fields": []}, {"name": "wired-net-devices", "type": "container", "fields": []}, {"name": "wlan-ssids", "type": "container", "fields": []}]}]}, {"name": "network-state", "type": "container", "is_config": false, "fields": [{"name": "device-count", "type": "container", "is_config": false, "fields": [{"name": "ungranted-device-count", "type": "uint16", "is_config": false}, {"name": "device-type-counts", "type": "container", "is_config": false, "fields": []}]}, {"name": "device-infos", "type": "container", "is_config": false, "fields": []}, {"name": "device-upgrade-infos", "type": "container", "is_config": false, "fields": []}, {"name": "device-neighbor-infos", "type": "container", "is_config": false, "fields": []}, {"name": "device-port-infos", "type": "container", "is_config": false, "fields": []}, {"name": "device-alarms", "type": "container", "is_config": false, "fields": []}]}]}, {"name": "huawei-easyweb-netmgmt:upgrade", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "devices", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-easyweb-netmgmt:reboot-device", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "devices", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-easyweb-netmgmt:restore-factory", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "devices", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-easyweb-netmgmt:discover-device", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "huawei-easyweb-netmgmt:change-netmgmt-password", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "old-password", "type": "string", "is_config": false, "nullable": false}, {"name": "new-password", "type": "string", "is_config": false, "nullable": false}]}, {"name": "huawei-easyweb-netmgmt:resend-config", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "xpath", "type": "string", "is_config": false, "nullable": false}]}, {"name": "huawei-easyweb-netmgmt:get-async-exec-result", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "xpath", "type": "string", "is_config": false, "nullable": false}]}, {"name": "huawei-easyweb-netmgmt:send-to-device", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "identifier", "type": "container", "is_config": false, "fields": [{"name": "mac-address", "type": "string", "is_config": false}]}, {"name": "embedded-cmd", "type": "container", "is_config": false, "fields": [{"name": "url", "type": "string", "is_config": false}, {"name": "page-url", "type": "string", "is_config": false}, {"name": "refer-url", "type": "string", "is_config": false}, {"name": "cmd-type", "type": "string", "is_config": false}, {"name": "cmd-paras", "type": "container", "is_config": false, "fields": [{"name": "cmd-type", "type": "choice", "is_config": false, "fields": [{"name": "ping", "type": "case", "is_config": false, "fields": [{"name": "ping-test-name", "type": "string", "is_config": false, "nullable": false}, {"name": "ping-oper-type", "type": "string", "is_config": false, "nullable": false}, {"name": "ping-dest-addr", "type": "string", "is_config": false}, {"name": "ping-packet-size", "type": "uint32", "is_config": false, "default": 56}, {"name": "ping-packet-count", "type": "uint32", "is_config": false, "default": 5}]}, {"name": "trace", "type": "case", "is_config": false, "fields": [{"name": "trace-test-name", "type": "string", "is_config": false, "nullable": false}, {"name": "trace-oper-type", "type": "string", "is_config": false, "nullable": false}, {"name": "trace-dest-addr", "type": "string", "is_config": false}, {"name": "trace-max-ttl", "type": "uint8", "is_config": false, "default": 30}]}, {"name": "domain-resolve", "type": "case", "is_config": false, "fields": [{"name": "host-name", "type": "string", "is_config": false, "nullable": false}]}]}]}]}]}, {"name": "huawei-easyweb-netmgmt:trigger-network-diagnostic", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "huawei-easyweb-netmgmt:get-network-diagnostic-result", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "vlan", "type": "uint16", "is_config": false}]}, {"name": "huawei-easyweb-netmgmt:sync-neighbor-info", "type": "container", "is_config": false, "presence": true, "fields": []}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::device-granteds::device-granted", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "mac-address", "type": "string", "nullable": false}, {"name": "device-name", "type": "string"}], "keys": [{"name": "k0", "fields": [":pid", "mac-address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::networks::vlans::vlan", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "id", "type": "uint16", "nullable": false}, {"name": "description", "type": "string", "nullable": false}, {"name": "gateway-configurations", "type": "container", "fields": [{"name": "gateway", "type": "string"}, {"name": "ip-address", "type": "container", "fields": [{"name": "ip", "type": "string"}, {"name": "mask", "type": "string"}], "clause": [{"type": "when", "formula": "../gateway"}]}, {"name": "dhcp-lease", "type": "container", "fields": [{"name": "time-type", "type": "choice", "fields": [{"name": "limited", "type": "case", "fields": [{"name": "day", "type": "uint16", "default": 1}, {"name": "hour", "type": "uint8", "default": 0}, {"name": "minute", "type": "uint8", "default": 0}]}]}], "clause": [{"type": "when", "formula": "../gateway"}]}, {"name": "dns-list", "type": "container", "fields": [], "clause": [{"type": "when", "formula": "../gateway"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::networks::vlans::vlan::gateway-configurations::dns-list::ip-address", "type": "leaf-list", "max-elements": 2, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ip-address", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ip-address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::networks::wired-net-devices::wired-net-device", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "mac-address", "type": "string", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "mac-address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::networks::wired-net-devices::wired-net-device::interfaces::interface", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "if-name", "type": "string", "nullable": false}, {"name": "pvid", "type": "uint16", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "if-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::networks::wlan-ssids::wlan-ssid", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ssid", "type": "string", "nullable": false}, {"name": "psk", "type": "string"}, {"name": "service-vlan", "type": "uint16", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ssid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-count::device-type-counts::device-type-count", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "device-type", "type": "string", "is_config": false, "nullable": false}, {"name": "count", "type": "uint16", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "device-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-infos::device-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "mac-address", "type": "string", "is_config": false, "nullable": false}, {"name": "gateway-capable", "type": "boolean", "is_config": false}, {"name": "device-type", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "management-status", "type": "string", "is_config": false}, {"name": "model", "type": "string", "is_config": false}, {"name": "sn", "type": "string", "is_config": false}, {"name": "ip", "type": "string", "is_config": false}, {"name": "software-version", "type": "string", "is_config": false}, {"name": "role", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "mac-address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-upgrade-infos::device-upgrade-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "mac-address", "type": "string", "is_config": false, "nullable": false}, {"name": "upgrade-state", "type": "string", "is_config": false}, {"name": "target-software-version", "type": "string", "is_config": false}, {"name": "target-patch-version", "type": "string", "is_config": false}, {"name": "upgrade-fail-reason", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "mac-address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-neighbor-infos::device-neighbor-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "mac-address", "type": "string", "is_config": false, "nullable": false}, {"name": "interfaces", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "mac-address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-neighbor-infos::device-neighbor-info::interfaces::interface", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "if-name", "type": "string", "is_config": false, "nullable": false}, {"name": "neighbor-info", "type": "container", "is_config": false, "fields": [{"name": "neighbor-devices", "type": "container", "is_config": false, "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid", "if-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-neighbor-infos::device-neighbor-info::interfaces::interface::neighbor-info::neighbor-devices::neighbor-device", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "mac-address", "type": "string", "is_config": false, "nullable": false}, {"name": "model", "type": "string", "is_config": false}, {"name": "neighbor-ifs", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "mac-address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-neighbor-infos::device-neighbor-info::interfaces::interface::neighbor-info::neighbor-devices::neighbor-device::neighbor-ifs::neighbor-if", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "if-name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "if-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-port-infos::device-port-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "mac-address", "type": "string", "is_config": false, "nullable": false}, {"name": "interfaces", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "mac-address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-port-infos::device-port-info::interfaces::interface", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "if-name", "type": "string", "is_config": false, "nullable": false}, {"name": "if-status", "type": "string", "is_config": false}, {"name": "if-mode", "type": "string", "is_config": false}, {"name": "admin-status", "type": "string", "is_config": false}, {"name": "loop-status", "type": "string", "is_config": false}, {"name": "allowed-vlans", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "if-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-alarms::device-alarm", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "mac-address", "type": "string", "is_config": false, "nullable": false}, {"name": "device-type", "type": "string", "is_config": false}, {"name": "model", "type": "string", "is_config": false}, {"name": "alarms", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "mac-address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-alarms::device-alarm::alarms::alarm", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32", "is_config": false, "nullable": false}, {"name": "alarm-name", "type": "string", "is_config": false}, {"name": "alarm-id", "type": "uint32", "is_config": false}, {"name": "level", "type": "string", "is_config": false}, {"name": "generated-time", "type": "string", "is_config": false}, {"name": "description", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "sequence"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:upgrade::devices::device", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "mac-address", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "mac-address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:reboot-device::devices::device", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "mac-address", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "mac-address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-easyweb-netmgmt:restore-factory::devices::device", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "mac-address", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "mac-address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-dns", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-dns:dns", "type": "container", "fields": [{"name": "domains", "type": "container", "fields": []}, {"name": "ipv4-hosts", "type": "container", "fields": []}, {"name": "ipv4-servers", "type": "container", "fields": []}, {"name": "dns-relay", "type": "container", "fields": [{"name": "enable", "type": "boolean", "default": false}, {"name": "local-ips", "type": "container", "fields": []}]}, {"name": "query-host-ips", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-dns:clear-ipv4-entry", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "vpn", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-dns:dns::domains::domain", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "vpn", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-network-instance/huawei-network-instance:network-instance/instances/huawei-network-instance:network-instance::instances::instance/name"}]}, {"name": "name", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "vpn", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-dns:dns::ipv4-hosts::ipv4-host", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "vpn", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-network-instance/huawei-network-instance:network-instance/instances/huawei-network-instance:network-instance::instances::instance/name"}]}, {"name": "host", "type": "string", "nullable": false}, {"name": "address", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "vpn", "host"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-dns:dns::ipv4-servers::ipv4-server", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "vpn", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-network-instance/huawei-network-instance:network-instance/instances/huawei-network-instance:network-instance::instances::instance/name"}]}, {"name": "address", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "vpn", "address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-dns:dns::dns-relay::local-ips::local-ip", "type": "list", "max-elements": 10, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ip-address", "type": "string", "nullable": false}, {"name": "vpn-name", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-network-instance/huawei-network-instance:network-instance/instances/huawei-network-instance:network-instance::instances::instance/name"}]}], "keys": [{"name": "k0", "fields": [":pid", "ip-address", "vpn-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-dns:dns::query-host-ips::query-host-ip", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "host-name", "type": "string", "is_config": false, "nullable": false}, {"name": "ip-address", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "host-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnostic-tools", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-diagnostic-tools:diagnostic-tools", "type": "container", "is_config": false, "fields": [{"name": "ipv4", "type": "container", "is_config": false, "fields": [{"name": "ping-results", "type": "container", "is_config": false, "fields": []}, {"name": "trace-results", "type": "container", "is_config": false, "fields": []}]}]}, {"name": "huawei-diagnostic-tools:ipv4-start-ip-ping", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "test-name", "type": "string", "is_config": false, "nullable": false}, {"name": "dest-addr", "type": "string", "is_config": false, "nullable": false}, {"name": "source-address", "type": "string", "is_config": false}, {"name": "packet-size", "type": "uint32", "is_config": false}, {"name": "packet-count", "type": "uint32", "is_config": false, "default": 5}, {"name": "interval", "type": "uint32", "is_config": false, "default": 500}, {"name": "timeout", "type": "uint32", "is_config": false, "default": 2000}, {"name": "ttl", "type": "uint8", "is_config": false, "default": 255}, {"name": "routing-mode", "type": "choice", "is_config": false, "fields": [{"name": "outbound", "type": "case", "is_config": false, "fields": [{"name": "if-name", "type": "string", "is_config": false}]}]}]}, {"name": "huawei-diagnostic-tools:ipv4-stop-ip-ping", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "test-name", "type": "string", "is_config": false, "nullable": false}]}, {"name": "huawei-diagnostic-tools:ipv4-start-ip-trace", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "test-name", "type": "string", "is_config": false, "nullable": false}, {"name": "dest-ip-addr", "type": "string", "is_config": false, "nullable": false}, {"name": "source-address", "type": "string", "is_config": false}, {"name": "first-ttl", "type": "uint8", "is_config": false, "default": 1}, {"name": "max-ttl", "type": "uint8", "is_config": false, "default": 30}, {"name": "if-name", "type": "string", "is_config": false}, {"name": "timeout", "type": "uint32", "is_config": false, "default": 5000}, {"name": "udp-port", "type": "uint32", "is_config": false, "default": 33434}, {"name": "count", "type": "uint32", "is_config": false, "default": 3}, {"name": "packet-size", "type": "uint32", "is_config": false, "default": 46}]}, {"name": "huawei-diagnostic-tools:ipv4-stop-ip-trace", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "test-name", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "test-name", "type": "string", "is_config": false, "nullable": false}, {"name": "packet-recv", "type": "uint32", "is_config": false}, {"name": "packet-send", "type": "uint32", "is_config": false}, {"name": "loss-ratio", "type": "uint8", "is_config": false}, {"name": "rtt-min", "type": "uint32", "is_config": false}, {"name": "rtt-max", "type": "uint32", "is_config": false}, {"name": "average-rtt", "type": "uint32", "is_config": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "error-type", "type": "string", "is_config": false}, {"name": "details", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "test-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result::details::detail", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint32", "is_config": false, "nullable": false}, {"name": "packet-size", "type": "uint32", "is_config": false}, {"name": "ttl", "type": "uint8", "is_config": false}, {"name": "rtt", "type": "uint32", "is_config": false}, {"name": "result-type", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "test-name", "type": "string", "is_config": false, "nullable": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "error-type", "type": "string", "is_config": false}, {"name": "details", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "test-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result::details::detail", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "hop-index", "type": "uint32", "is_config": false, "nullable": false}, {"name": "ttl", "type": "uint8", "is_config": false}, {"name": "rtt", "type": "uint32", "is_config": false}, {"name": "ds-ip-addr", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "hop-index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-diagnose:diagnose", "type": "container", "is_config": false, "fields": [{"name": "terminal", "type": "container", "is_config": false, "fields": [{"name": "monitor", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "enabled", "type": "boolean", "is_config": false, "nullable": false}]}, {"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enabled", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}, {"name": "duration", "type": "uint32", "is_config": false, "default": 60}]}]}, {"name": "huawei-diagnose-acl:acl", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "huawei-diagnose-arp:arp", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "huawei-diagnose-device-info:driver-dfx-infos", "type": "container", "is_config": false, "fields": [{"name": "driver-dfx-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "info", "type": "string", "is_config": false}]}]}, {"name": "huawei-diagnose-device-info:device-management", "type": "container", "is_config": false, "fields": [{"name": "clear-user-information", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enabled", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "huawei-diagnose-dhcp:dhcp", "type": "container", "is_config": false, "fields": [{"name": "am", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "dhcp-stack", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "dhcp-client", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "dhcp-server", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}]}, {"name": "huawei-diagnose-dns:dns4c", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "huawei-diagnose-hpe:hpe", "type": "container", "is_config": false, "fields": [{"name": "query-forward", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "cmdline", "type": "string", "is_config": false, "nullable": false}]}, {"name": "query-channel-all", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-channel", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "id", "type": "uint32", "is_config": false, "nullable": false}]}, {"name": "query-mbuf", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-memory", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "string", "is_config": false, "nullable": false}]}, {"name": "query-hpk-interrupts", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-hpk-task", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "dump-hpk-task", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "start-trace", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "size", "type": "uint32", "is_config": false}, {"name": "pid", "type": "uint32", "is_config": false}, {"name": "tid", "type": "uint32", "is_config": false}, {"name": "name", "type": "string", "is_config": false}, {"name": "vcpu", "type": "uint32", "is_config": false}]}, {"name": "stop-trace", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "dump-trace", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-nd", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-vpoll", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-kbox", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "dump-kbox", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "region", "type": "string", "is_config": false}]}, {"name": "clear-kbox", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "region", "type": "string", "is_config": false}]}, {"name": "set-klog", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "level", "type": "uint32", "is_config": false}]}, {"name": "query-coredump", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "set-coredump", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "size", "type": "int32", "is_config": false}]}, {"name": "query-hpk-swt", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-log-level", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "id", "type": "uint32", "is_config": false, "default": 16}]}, {"name": "query-log-size", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-log-module-id", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "name", "type": "string", "is_config": false}]}, {"name": "set-log-level", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "value", "type": "uint32", "is_config": false, "nullable": false}, {"name": "id", "type": "uint32", "is_config": false}]}, {"name": "set-log-size", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "value", "type": "uint32", "is_config": false, "nullable": false}]}, {"name": "dump-log", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "clear-log", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "reset-counter-value", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "id", "type": "uint32", "is_config": false, "nullable": false}]}, {"name": "read-counter-value", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "id", "type": "uint32", "is_config": false}, {"name": "start-id", "type": "uint32", "is_config": false}, {"name": "end-id", "type": "uint32", "is_config": false}]}, {"name": "query-counter-id", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "name", "type": "string", "is_config": false, "nullable": false}]}, {"name": "query-patch", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "app-name", "type": "string", "is_config": false}]}, {"name": "query-rcu", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-timer-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "app-name", "type": "string", "is_config": false, "nullable": false}, {"name": "timer-type", "type": "uint32", "is_config": false, "nullable": false}]}, {"name": "query-kpatch", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "string", "is_config": false, "nullable": false}]}, {"name": "query-hpk-error-info", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-shared-queue", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "id", "type": "uint32", "is_config": false}]}]}, {"name": "huawei-diagnose-hpf:hpf", "type": "container", "is_config": false, "fields": [{"name": "query-forward", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "cmdline", "type": "string", "is_config": false, "nullable": false}]}, {"name": "query-session", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "protocol", "type": "uint8", "is_config": false}, {"name": "global-source-ip", "type": "string", "is_config": false}, {"name": "global-destination-ip", "type": "string", "is_config": false}, {"name": "inside-source-ip", "type": "string", "is_config": false}, {"name": "inside-destination-ip", "type": "string", "is_config": false}, {"name": "global-source-port", "type": "uint16", "is_config": false}, {"name": "global-destination-port", "type": "uint16", "is_config": false}, {"name": "inside-source-port", "type": "uint16", "is_config": false}, {"name": "inside-destination-port", "type": "uint16", "is_config": false}, {"name": "create-in", "type": "uint8", "is_config": false}, {"name": "session-number", "type": "uint8", "is_config": false, "default": 10}, {"name": "verbose", "type": "boolean", "is_config": false, "default": false}]}, {"name": "reset-session", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "protocol", "type": "uint8", "is_config": false}, {"name": "global-source-ip", "type": "string", "is_config": false}, {"name": "global-destination-ip", "type": "string", "is_config": false}, {"name": "inside-source-ip", "type": "string", "is_config": false}, {"name": "inside-destination-ip", "type": "string", "is_config": false}, {"name": "global-source-port", "type": "uint16", "is_config": false}, {"name": "global-destination-port", "type": "uint16", "is_config": false}, {"name": "inside-source-port", "type": "uint16", "is_config": false}, {"name": "inside-destination-port", "type": "uint16", "is_config": false}, {"name": "create-in", "type": "uint8", "is_config": false}]}, {"name": "query-session-statistics", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-fwd-statistics", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "string", "is_config": false, "nullable": false}]}, {"name": "reset-fwd-statistics", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "string", "is_config": false, "nullable": false}]}, {"name": "debugging-packet", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "packet-number", "type": "uint8", "is_config": false, "default": 5}, {"name": "ingress-ifindex", "type": "uint32", "is_config": false}, {"name": "source-mac", "type": "string", "is_config": false}, {"name": "destination-mac", "type": "string", "is_config": false}, {"name": "vlan-id", "type": "string", "is_config": false}, {"name": "eth-type", "type": "string", "is_config": false}, {"name": "source-ip", "type": "string", "is_config": false}, {"name": "destination-ip", "type": "string", "is_config": false}, {"name": "source-port", "type": "uint16", "is_config": false}, {"name": "destination-port", "type": "uint16", "is_config": false}, {"name": "protocol", "type": "uint8", "is_config": false}, {"name": "tcp-flag-value", "type": "uint8", "is_config": false}, {"name": "max-packet-len", "type": "uint16", "is_config": false}, {"name": "min-packet-len", "type": "uint16", "is_config": false}, {"name": "packet-from-cp", "type": "boolean", "is_config": false}, {"name": "fragment-type", "type": "string", "is_config": false}, {"name": "fragment-protocol", "type": "string", "is_config": false, "default": "ip"}, {"name": "packet-type", "type": "string", "is_config": false}, {"name": "inner", "type": "boolean", "is_config": false}, {"name": "invert", "type": "boolean", "is_config": false, "default": false}, {"name": "time-out", "type": "uint32", "is_config": false, "default": 300}, {"name": "discard-type", "type": "string", "is_config": false}]}, {"name": "set-performance-mode", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}]}, {"name": "set-trace-packet", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "index", "type": "uint16", "is_config": false, "nullable": false}, {"name": "time-out", "type": "uint32", "is_config": false, "default": 30}, {"name": "source-mac", "type": "string", "is_config": false}, {"name": "destination-mac", "type": "string", "is_config": false}, {"name": "eth-type", "type": "string", "is_config": false}, {"name": "source-ip", "type": "string", "is_config": false}, {"name": "destination-ip", "type": "string", "is_config": false}, {"name": "source-port", "type": "uint16", "is_config": false}, {"name": "destination-port", "type": "uint16", "is_config": false}, {"name": "protocol", "type": "uint8", "is_config": false}]}, {"name": "query-trace-packet", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "index", "type": "uint16", "is_config": false, "nullable": false}]}, {"name": "query-nat-address-group-usage", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "id", "type": "uint32", "is_config": false, "nullable": false}]}, {"name": "query-server-map", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "string", "is_config": false, "default": "all"}, {"name": "ip", "type": "string", "is_config": false}]}, {"name": "reset-server-map", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ip", "type": "string", "is_config": false}]}, {"name": "query-table-usage", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-fragment-reassemble-session", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "source-ip", "type": "string", "is_config": false}, {"name": "destination-ip", "type": "string", "is_config": false}]}, {"name": "query-arp-miss-cache", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "next-hop", "type": "string", "is_config": false}]}, {"name": "query-nd-miss-cache", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "next-hop", "type": "string", "is_config": false}]}, {"name": "query-perf-task", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "sort-by-cycles", "type": "string", "is_config": false}]}, {"name": "reset-perf-task", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "set-perf-task", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "trace-mode", "type": "string", "is_config": false, "nullable": false}]}]}, {"name": "huawei-diagnose-hps:hps", "type": "container", "is_config": false, "fields": [{"name": "query-ip-socket", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "pid", "type": "uint32", "is_config": false}, {"name": "fd", "type": "uint32", "is_config": false}, {"name": "type", "type": "string", "is_config": false}]}, {"name": "query-user-statistics", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "pid", "type": "uint32", "is_config": false}]}, {"name": "query-tcp-status", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "control-block-id", "type": "uint32", "is_config": false}, {"name": "pid", "type": "uint32", "is_config": false}, {"name": "fd", "type": "uint32", "is_config": false}, {"name": "source-ip", "type": "string", "is_config": false}, {"name": "destination-ip", "type": "string", "is_config": false}, {"name": "source-port", "type": "uint16", "is_config": false}, {"name": "destination-port", "type": "uint16", "is_config": false}]}, {"name": "query-udp-status", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "pid", "type": "uint32", "is_config": false}, {"name": "fd", "type": "uint32", "is_config": false}, {"name": "source-ip", "type": "string", "is_config": false}, {"name": "destination-ip", "type": "string", "is_config": false}, {"name": "source-port", "type": "uint16", "is_config": false}, {"name": "destination-port", "type": "uint16", "is_config": false}]}, {"name": "query-rawip-status", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "pid", "type": "uint32", "is_config": false}, {"name": "fd", "type": "uint32", "is_config": false}, {"name": "source-ip", "type": "string", "is_config": false}, {"name": "destination-ip", "type": "string", "is_config": false}]}, {"name": "query-rawlink-status", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "pid", "type": "uint32", "is_config": false}, {"name": "fd", "type": "uint32", "is_config": false}]}, {"name": "query-tcp-control-block", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "source-ip", "type": "string", "is_config": false}, {"name": "destination-ip", "type": "string", "is_config": false}, {"name": "source-port", "type": "uint16", "is_config": false}, {"name": "destination-port", "type": "uint16", "is_config": false}]}, {"name": "query-statistics", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "string", "is_config": false, "nullable": false}]}, {"name": "reset-statistics", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "string", "is_config": false, "nullable": false}]}, {"name": "debugging-tcp-packet", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "packet-number", "type": "uint32", "is_config": false, "default": 10}, {"name": "direction", "type": "string", "is_config": false}, {"name": "source-or-destination-ip", "type": "string", "is_config": false}, {"name": "source-or-destination-port", "type": "uint16", "is_config": false}, {"name": "source-ip", "type": "string", "is_config": false}, {"name": "destination-ip", "type": "string", "is_config": false}, {"name": "source-port", "type": "uint16", "is_config": false}, {"name": "destination-port", "type": "uint16", "is_config": false}]}, {"name": "debugging-udp-packet", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "packet-number", "type": "uint32", "is_config": false, "default": 10}, {"name": "direction", "type": "string", "is_config": false}, {"name": "source-or-destination-ip", "type": "string", "is_config": false}, {"name": "source-or-destination-port", "type": "uint16", "is_config": false}, {"name": "source-ip", "type": "string", "is_config": false}, {"name": "destination-ip", "type": "string", "is_config": false}, {"name": "source-port", "type": "uint16", "is_config": false}, {"name": "destination-port", "type": "uint16", "is_config": false}]}, {"name": "debugging-rawip-packet", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "packet-number", "type": "uint32", "is_config": false, "default": 10}, {"name": "source-or-destination-ip", "type": "string", "is_config": false}, {"name": "source-ip", "type": "string", "is_config": false}, {"name": "destination-ip", "type": "string", "is_config": false}, {"name": "protocol", "type": "uint32", "is_config": false}, {"name": "direction", "type": "string", "is_config": false}]}, {"name": "debugging-rawlink-packet", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "packet-number", "type": "uint32", "is_config": false, "default": 10}, {"name": "source-or-destination-mac", "type": "string", "is_config": false}, {"name": "source-mac", "type": "string", "is_config": false}, {"name": "destination-mac", "type": "string", "is_config": false}, {"name": "verbose", "type": "boolean", "is_config": false, "default": false}, {"name": "direction", "type": "string", "is_config": false}]}, {"name": "debugging-ip-packet", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "packet-number", "type": "uint32", "is_config": false, "default": 10}, {"name": "ifindex", "type": "uint32", "is_config": false}, {"name": "source-or-destination-ip", "type": "string", "is_config": false}, {"name": "source-ip", "type": "string", "is_config": false}, {"name": "destination-ip", "type": "string", "is_config": false}, {"name": "verbose", "type": "boolean", "is_config": false, "default": false}, {"name": "protocol", "type": "uint32", "is_config": false}, {"name": "direction", "type": "string", "is_config": false}]}, {"name": "debugging-tcp-event", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "packet-number", "type": "uint32", "is_config": false, "default": 10}, {"name": "source-or-destination-ip", "type": "string", "is_config": false}, {"name": "source-or-destination-port", "type": "uint16", "is_config": false}, {"name": "source-ip", "type": "string", "is_config": false}, {"name": "destination-ip", "type": "string", "is_config": false}, {"name": "source-port", "type": "uint16", "is_config": false}, {"name": "destination-port", "type": "uint16", "is_config": false}]}, {"name": "query-unix-status", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-netlink-status", "type": "container", "is_config": false, "presence": true, "fields": []}]}, {"name": "huawei-diagnose-hsec:hsec", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "huawei-diagnose-icmp:ping", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "huawei-diagnose-icmp:traceroute", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "huawei-diagnose-if-standby:if-standby", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "huawei-diagnose-ifm:ifm", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "huawei-diagnose-iot-pnp:iot-pnp", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "huawei-diagnose-iotbus:iotbus", "type": "container", "is_config": false, "fields": [{"name": "discovery-devices", "type": "container", "is_config": false, "fields": []}, {"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enabled", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "huawei-diagnose-ip:ipv4", "type": "container", "is_config": false, "fields": [{"name": "addresses", "type": "container", "is_config": false, "fields": []}, {"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "huawei-diagnose-lldp:lldp", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enabled", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "nullable": false}, {"name": "if-name", "type": "string", "is_config": false}]}, {"name": "trouble-shooting", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "inner-data", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "error-packet", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "if-name", "type": "string", "is_config": false, "nullable": false}]}]}, {"name": "huawei-diagnose-loadbalance:loadbalance", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "huawei-diagnose-lsw-chip:lsw-chip", "type": "container", "is_config": false, "fields": [{"name": "mac-learnings", "type": "container", "is_config": false, "fields": []}, {"name": "vlan-infos", "type": "container", "is_config": false, "fields": []}, {"name": "car-infos", "type": "container", "is_config": false, "fields": []}, {"name": "acl-ranges", "type": "container", "is_config": false, "fields": []}, {"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}, {"name": "cmdinfo", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "cmd", "type": "string", "is_config": false, "nullable": false}]}]}, {"name": "huawei-diagnose-mac:mac", "type": "container", "is_config": false, "fields": [{"name": "mac-syn", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "default": true}, {"name": "interval", "type": "uint32", "is_config": false, "default": 10}]}]}, {"name": "huawei-diagnose-mstp:mstp", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enabled", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "nullable": false}, {"name": "if-name", "type": "string", "is_config": false}]}, {"name": "inner-data", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "info-type", "type": "string", "is_config": false, "nullable": false}, {"name": "process-id", "type": "uint32", "is_config": false}, {"name": "instance-id", "type": "uint32", "is_config": false}, {"name": "if-name", "type": "string", "is_config": false}]}, {"name": "state-change-packet", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "process-id", "type": "uint32", "is_config": false, "nullable": false}]}, {"name": "topology-change-verbose", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "history", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "process-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "instance-id", "type": "uint32", "is_config": false, "nullable": false}]}]}, {"name": "huawei-diagnose-nat:nat", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "huawei-diagnose-nctl:nctl", "type": "container", "is_config": false, "fields": [{"name": "innertables", "type": "container", "is_config": false, "fields": []}, {"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "huawei-diagnose-ndb:ndb", "type": "container", "is_config": false, "fields": [{"name": "query-db-server-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "instance", "type": "string", "is_config": false, "nullable": false}, {"name": "command", "type": "string", "is_config": false, "nullable": false}]}]}, {"name": "huawei-diagnose-nfc:nfc", "type": "container", "is_config": false, "fields": [{"name": "processes", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-diagnose-omu:omu", "type": "container", "is_config": false, "fields": [{"name": "yangdb", "type": "container", "is_config": false, "fields": []}, {"name": "notif", "type": "container", "is_config": false, "fields": [{"name": "sensor-paths", "type": "container", "is_config": false, "fields": []}, {"name": "subscriptions", "type": "container", "is_config": false, "fields": []}, {"name": "collect-tasks", "type": "container", "is_config": false, "fields": []}, {"name": "notification-subscriptions", "type": "container", "is_config": false, "fields": []}, {"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enabled", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "mini-system", "type": "container", "is_config": false, "fields": [{"name": "mini-system-file", "type": "string", "is_config": false}, {"name": "current-mini-system", "type": "boolean", "is_config": false}, {"name": "next-mini-system", "type": "boolean", "is_config": false}]}, {"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enabled", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "huawei-diagnose-patch:patch", "type": "container", "is_config": false, "fields": [{"name": "patch-unit-infos", "type": "container", "is_config": false, "fields": []}, {"name": "force-load", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "name", "type": "string", "is_config": false, "nullable": false}]}, {"name": "force-delete", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "delete-type", "type": "string", "is_config": false, "default": "all"}]}]}, {"name": "huawei-diagnose-pki:pki", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "module", "type": "string", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "info"}, {"name": "enable", "type": "boolean", "is_config": false, "nullable": false}]}]}, {"name": "huawei-diagnose-poe:poe", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}, {"name": "cmdinfo", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "cmd", "type": "string", "is_config": false, "nullable": false}]}]}, {"name": "huawei-diagnose-ppp:ppp", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "huawei-diagnose-pppoe-client:pppoe-client", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "huawei-diagnose-routing-lite:routing", "type": "container", "is_config": false, "fields": [{"name": "routing-manage", "type": "container", "is_config": false, "fields": [{"name": "routes", "type": "container", "is_config": false, "fields": []}, {"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "direct-routing", "type": "container", "is_config": false, "fields": [{"name": "routes", "type": "container", "is_config": false, "fields": []}, {"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "static-routing", "type": "container", "is_config": false, "fields": [{"name": "routes", "type": "container", "is_config": false, "fields": []}, {"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "user-network-routing", "type": "container", "is_config": false, "fields": [{"name": "routes", "type": "container", "is_config": false, "fields": []}, {"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}]}, {"name": "huawei-diagnose-sacl:sacl", "type": "container", "is_config": false, "fields": [{"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enable", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}]}, {"name": "huawei-diagnose-system:sysdiag", "type": "container", "is_config": false, "fields": [{"name": "service-infos", "type": "container", "is_config": false, "fields": []}, {"name": "get-task-deadloop-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "slot-id", "type": "string", "is_config": false, "default": "0"}, {"name": "cpu-id", "type": "string", "is_config": false, "default": "0"}]}, {"name": "get-task-track-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "slot-id", "type": "string", "is_config": false, "default": "0"}, {"name": "cpu-id", "type": "string", "is_config": false, "default": "0"}, {"name": "process-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "show-num", "type": "uint32", "is_config": false, "nullable": false}]}, {"name": "get-thread-cpu-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "slot-id", "type": "string", "is_config": false, "default": "0"}, {"name": "cpu-id", "type": "string", "is_config": false, "default": "0"}, {"name": "process-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "top", "type": "uint32", "is_config": false, "default": 3}]}, {"name": "get-process-memory-handle-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "slot-id", "type": "string", "is_config": false, "default": "0"}, {"name": "cpu-id", "type": "string", "is_config": false, "default": "0"}, {"name": "process-id", "type": "uint32", "is_config": false, "nullable": false}]}, {"name": "get-deadloop-record-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "slot-id", "type": "string", "is_config": false, "default": "0"}, {"name": "cpu-id", "type": "string", "is_config": false, "default": "0"}]}, {"name": "get-deadloop-configuration-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "slot-id", "type": "string", "is_config": false, "default": "0"}, {"name": "cpu-id", "type": "string", "is_config": false, "default": "0"}, {"name": "process-id", "type": "uint32", "is_config": false, "nullable": false}]}, {"name": "get-exception-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "slot-id", "type": "string", "is_config": false, "default": "0"}, {"name": "cpu-id", "type": "string", "is_config": false, "default": "0"}, {"name": "show-num", "type": "uint32", "is_config": false, "nullable": false}, {"name": "begin-num", "type": "uint32", "is_config": false, "default": 0}]}, {"name": "get-assert-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "slot-id", "type": "string", "is_config": false, "default": "0"}, {"name": "cpu-id", "type": "string", "is_config": false, "default": "0"}, {"name": "show-num", "type": "uint32", "is_config": false, "nullable": false}, {"name": "begin-num", "type": "uint32", "is_config": false, "default": 0}]}, {"name": "reset-exception", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "slot-id", "type": "string", "is_config": false, "default": "0"}, {"name": "cpu-id", "type": "string", "is_config": false, "default": "0"}]}, {"name": "reset-assert", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "slot-id", "type": "string", "is_config": false, "default": "0"}, {"name": "cpu-id", "type": "string", "is_config": false, "default": "0"}]}, {"name": "reset-deadloop", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "slot-id", "type": "string", "is_config": false, "default": "0"}, {"name": "cpu-id", "type": "string", "is_config": false, "default": "0"}]}, {"name": "get-memory-leak-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "slot-id", "type": "string", "is_config": false, "default": "0"}, {"name": "cpu-id", "type": "string", "is_config": false, "default": "0"}, {"name": "process-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "occuppied-time", "type": "uint32", "is_config": false}]}, {"name": "get-inspect-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "slot-id", "type": "string", "is_config": false, "default": "0"}, {"name": "cpu-id", "type": "string", "is_config": false, "default": "0"}, {"name": "process-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "sub-module", "type": "choice", "is_config": false, "nullable": false, "fields": [{"name": "memory", "type": "case", "is_config": false, "fields": [{"name": "memory", "type": "string", "is_config": false, "nullable": false}]}, {"name": "cros", "type": "case", "is_config": false, "fields": [{"name": "cros", "type": "string", "is_config": false, "nullable": false}]}, {"name": "handle", "type": "case", "is_config": false, "fields": [{"name": "handle", "type": "string", "is_config": false, "nullable": false}]}, {"name": "mesh", "type": "case", "is_config": false, "fields": [{"name": "mesh", "type": "string", "is_config": false, "nullable": false}]}, {"name": "patchinfo", "type": "case", "is_config": false, "fields": [{"name": "patchinfo", "type": "string", "is_config": false, "nullable": false}]}, {"name": "procinfo", "type": "case", "is_config": false, "fields": [{"name": "procinfo", "type": "string", "is_config": false, "nullable": false}]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "case", "is_config": false, "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "is_config": false, "nullable": false}]}, {"name": "queueinfo", "type": "case", "is_config": false, "fields": [{"name": "queueinfo", "type": "string", "is_config": false, "nullable": false}]}, {"name": "semaphore", "type": "case", "is_config": false, "fields": [{"name": "semaphore", "type": "uint32", "is_config": false, "nullable": false}]}, {"name": "vrptimer", "type": "case", "is_config": false, "fields": [{"name": "vrptimer", "type": "string", "is_config": false, "nullable": false}]}]}, {"name": "cmd-para1", "type": "uint64", "is_config": false}, {"name": "cmd-para2", "type": "uint64", "is_config": false, "clause": [{"type": "must", "formula": "(../cmd-para1)"}]}, {"name": "cmd-para3", "type": "uint64", "is_config": false, "clause": [{"type": "must", "formula": "(../cmd-para2)"}]}, {"name": "cmd-para4", "type": "uint64", "is_config": false, "clause": [{"type": "must", "formula": "(../cmd-para3)"}]}]}, {"name": "get-udf-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "process-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "cmd", "type": "string", "is_config": false, "nullable": false}]}, {"name": "debugging-udf-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "process-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "cmd", "type": "string", "is_config": false, "nullable": false}]}, {"name": "get-system-cpu-stat-info", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "get-system-memory-stat-info", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "reset-system-cpu-runtime", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "get-kernel-pagecache-info", "type": "container", "is_config": false, "presence": true, "fields": []}]}, {"name": "huawei-diagnose-tid:tid", "type": "container", "is_config": false, "fields": [{"name": "terminals", "type": "container", "is_config": false, "fields": []}, {"name": "return-packets", "type": "container", "is_config": false, "fields": []}, {"name": "monitoring-scan-records", "type": "container", "is_config": false, "fields": []}, {"name": "statistics", "type": "container", "is_config": false, "fields": []}, {"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "enabled", "type": "boolean", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false, "default": "warning"}]}, {"name": "clear-fingerprint", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "clear-fingerprint-by-mac", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "mac", "type": "string", "is_config": false, "nullable": false}]}]}, {"name": "huawei-diagnose-wmp:wmp", "type": "container", "is_config": false, "fields": [{"name": "capwap", "type": "container", "is_config": false, "fields": [{"name": "query-capwap-link", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "link", "type": "string", "is_config": false}]}, {"name": "query-capwap-linkinfo", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "process", "type": "uint32", "is_config": false}, {"name": "link", "type": "string", "is_config": false}]}, {"name": "query-capwap-reginfo", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "msgtype", "type": "uint32", "is_config": false}]}, {"name": "query-capwap-msg-age-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "msgtype", "type": "uint32", "is_config": false}]}, {"name": "query-capwap-msg-drop-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "msgtype", "type": "uint32", "is_config": false}]}, {"name": "query-capwap-control-packets-statistics", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "process", "type": "uint32", "is_config": false}, {"name": "link", "type": "string", "is_config": false}]}, {"name": "query-capwap-keepalive-packets-statistics", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-capwap-datalink", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "process", "type": "uint32", "is_config": false}, {"name": "link", "type": "string", "is_config": false}]}, {"name": "query-capwap-dtls-session", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "process", "type": "uint32", "is_config": false}, {"name": "session", "type": "uint32", "is_config": false}]}, {"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "switch", "type": "string", "is_config": false, "nullable": false}, {"name": "enabled", "type": "boolean", "is_config": false, "nullable": false}, {"name": "filter-linkid", "type": "uint32", "is_config": false}, {"name": "filter-mac", "type": "string", "is_config": false}]}]}, {"name": "wlan", "type": "container", "is_config": false, "fields": [{"name": "timer", "type": "container", "is_config": false, "fields": [{"name": "query-timer", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "process", "type": "uint32", "is_config": false, "default": 0}]}, {"name": "query-timer-statistics", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "process", "type": "uint32", "is_config": false, "default": 0}]}]}, {"name": "wmng", "type": "container", "is_config": false, "fields": [{"name": "query-spec", "type": "container", "is_config": false, "presence": true, "fields": []}]}, {"name": "wdbm", "type": "container", "is_config": false, "fields": [{"name": "query-configuration", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "tabletype", "type": "uint32", "is_config": false, "nullable": false}, {"name": "elem", "type": "string", "is_config": false, "nullable": false}, {"name": "attrib", "type": "uint32", "is_config": false, "nullable": false}, {"name": "datatype", "type": "string", "is_config": false, "nullable": false}]}]}, {"name": "profile", "type": "container", "is_config": false, "fields": [{"name": "query-binding-relationship", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "uint32", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}]}]}, {"name": "wvap", "type": "container", "is_config": false, "fields": [{"name": "query-vap-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "radio-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "wlan-id", "type": "uint32", "is_config": false, "nullable": false}]}, {"name": "query-vap-static-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "radio-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "wlan-id", "type": "uint32", "is_config": false, "nullable": false}]}]}, {"name": "wsta", "type": "container", "is_config": false, "fields": [{"name": "query-record", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "string", "is_config": false, "nullable": false}]}, {"name": "reset-record", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "string", "is_config": false, "nullable": false}]}, {"name": "query-record-statistics", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "string", "is_config": false, "nullable": false}]}, {"name": "reset-record-statistics", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "string", "is_config": false, "nullable": false}]}, {"name": "query-online-track", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "reset-online-track", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-sta-number", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false, "clause": [{"type": "must", "formula": "../radio-id"}]}, {"name": "radio-id", "type": "uint32", "is_config": false, "clause": [{"type": "must", "formula": "../ap-id"}]}]}, {"name": "query-sta-online-state", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "mac", "type": "string", "is_config": false, "nullable": false}]}, {"name": "query-online-statistics", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "string", "is_config": false, "nullable": false}]}, {"name": "reset-online-statistics", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "string", "is_config": false, "nullable": false}]}, {"name": "query-sta-data", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "mac", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "process", "type": "uint32", "is_config": false}]}, {"name": "query-block-head-data", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "index", "type": "uint32", "is_config": false}, {"name": "process", "type": "uint32", "is_config": false}]}, {"name": "query-block-sta-number", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "process", "type": "uint32", "is_config": false}]}, {"name": "query-sta-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false}, {"name": "ssid", "type": "string", "is_config": false}]}, {"name": "query-station", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false}]}, {"name": "query-peak-statistics", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "reset-peak-statistics", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-sta-record-track", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "mac", "type": "string", "is_config": false}]}]}, {"name": "wdev", "type": "container", "is_config": false, "fields": [{"name": "query-ap-information", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false}]}, {"name": "query-used-link-num", "type": "container", "is_config": false, "presence": true, "fields": []}]}, {"name": "wcfg", "type": "container", "is_config": false, "fields": [{"name": "query-ap-group-cfg-tree", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false}]}, {"name": "query-ap-cfg-tree", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false}]}, {"name": "query-object-table-attr", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "object-type", "type": "uint32", "is_config": false}, {"name": "object-id", "type": "uint32", "is_config": false}, {"name": "attr-id", "type": "uint32", "is_config": false}]}, {"name": "query-object-attr", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "obj-type", "type": "string", "is_config": false}]}, {"name": "query-config-errors", "type": "container", "is_config": false, "presence": true, "fields": []}]}, {"name": "wlbm", "type": "container", "is_config": false, "fields": [{"name": "query-control-queue-info", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-fsm-pause-queue-info", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-fsm-queue-info", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-recent-detail", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "detail", "type": "uint32", "is_config": false}]}]}, {"name": "wrfm", "type": "container", "is_config": false, "fields": [{"name": "calibrate", "type": "container", "is_config": false, "fields": [{"name": "query-channel-inherit", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false, "nullable": false}]}, {"name": "query-channel-set", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "radio-id", "type": "uint32", "is_config": false, "nullable": false}]}, {"name": "query-history-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false}, {"name": "radio-id", "type": "uint32", "is_config": false}, {"name": "global", "type": "string", "is_config": false}]}, {"name": "query-bss-color", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-env-deteriorate-channel-blacklist", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false, "nullable": false}]}, {"name": "query-report", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-local-analysis", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "analysis-type", "type": "string", "is_config": false}]}, {"name": "query-status", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "device-list", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "device-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "device-id", "type": "uint32", "is_config": false, "nullable": false}]}]}, {"name": "query-radio-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false}, {"name": "radio-id", "type": "uint32", "is_config": false}]}, {"name": "query-radio-reload-record", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "ap-id", "type": "uint32", "is_config": false}, {"name": "reset", "type": "boolean", "is_config": false}]}]}, {"name": "web", "type": "container", "is_config": false, "fields": [{"name": "query-web-tables", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "query-web-data", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "table-name", "type": "string", "is_config": false, "nullable": false}, {"name": "key1", "type": "string", "is_config": false}, {"name": "key2", "type": "string", "is_config": false}, {"name": "key3", "type": "string", "is_config": false}, {"name": "key4", "type": "string", "is_config": false}, {"name": "key5", "type": "string", "is_config": false}]}]}, {"name": "query-debuginfo", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "process", "type": "uint32", "is_config": false, "default": 0}, {"name": "type", "type": "uint32", "is_config": false, "nullable": false}]}, {"name": "debugging", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "module", "type": "string", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false}]}, {"name": "trace", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "mac-address", "type": "string", "is_config": false}, {"name": "level", "type": "string", "is_config": false}]}]}]}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-iotbus:iotbus::discovery-devices::discovery-device", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "sn", "type": "string", "is_config": false, "nullable": false}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "device-type", "type": "string", "is_config": false}, {"name": "model", "type": "string", "is_config": false}, {"name": "ip", "type": "string", "is_config": false}, {"name": "vendor", "type": "string", "is_config": false}, {"name": "software-version", "type": "string", "is_config": false}, {"name": "local", "type": "boolean", "is_config": false}, {"name": "role", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "time", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "sn"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-ip:ipv4::addresses::address", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "if-index", "type": "uint32", "is_config": false, "nullable": false}, {"name": "if-name", "type": "string", "is_config": false, "nullable": false}, {"name": "ip", "type": "string", "is_config": false, "nullable": false}, {"name": "mask-length", "type": "uint8", "is_config": false, "nullable": false}, {"name": "type", "type": "uint8", "is_config": false}, {"name": "input-time", "type": "uint32", "is_config": false}, {"name": "output-time", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "if-index", "if-name", "ip", "mask-length"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::mac-learnings::mac-learning", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "if-name", "type": "string", "is_config": false, "nullable": false}, {"name": "enable-status", "type": "boolean", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "if-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "if-name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "if-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info::vlan-id", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "vlan-id", "type": "uint32", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "vlan-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::car-infos::car-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "unit", "type": "uint32", "is_config": false, "nullable": false}, {"name": "car-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "cbs", "type": "uint32", "is_config": false}, {"name": "cir", "type": "uint32", "is_config": false}, {"name": "pbs", "type": "uint32", "is_config": false}, {"name": "pir", "type": "uint32", "is_config": false}, {"name": "car-type", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "unit", "car-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "unit", "type": "uint32", "is_config": false, "nullable": false}, {"name": "begin-index", "type": "uint32", "is_config": false, "nullable": false}, {"name": "end-index", "type": "uint32", "is_config": false, "nullable": false}, {"name": "acl-infos", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "unit", "begin-index", "end-index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range::acl-infos::acl-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "acl-index", "type": "uint32", "is_config": false, "nullable": false}, {"name": "key-info", "type": "string", "is_config": false}, {"name": "action-info", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "acl-index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-nctl:nctl::innertables::innertable", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "table-type", "type": "string", "is_config": false, "nullable": false}, {"name": "operation-type", "type": "string", "is_config": false, "nullable": false}, {"name": "last-time", "type": "uint32", "is_config": false}, {"name": "total-cnt", "type": "uint32", "is_config": false}, {"name": "fail-cnt", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name", "table-type", "operation-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-nfc:nfc::processes::process", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "process-id", "type": "int32", "is_config": false, "nullable": false}, {"name": "get-ipc-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "string", "is_config": false, "nullable": false}]}, {"name": "get-rpc-info", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "type", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid", "process-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::sensor-paths::sensor-path", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::sensor-paths::sensor-path::path", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "path", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "path"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "trigger-send-count", "type": "uint32", "is_config": false}, {"name": "trigger-send-error", "type": "uint32", "is_config": false}, {"name": "sensor-groups", "type": "container", "is_config": false, "fields": []}, {"name": "destination-groups", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "group-id", "type": "string", "is_config": false, "nullable": false}, {"name": "collect-count", "type": "uint32", "is_config": false}, {"name": "receive-count", "type": "uint32", "is_config": false}, {"name": "receive-error-count", "type": "uint32", "is_config": false}, {"name": "sensor-paths", "type": "container", "is_config": false, "fields": []}, {"name": "collect-tasks", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "group-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "path", "type": "string", "is_config": false, "nullable": false}, {"name": "indicators", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "path"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path::indicators::indicator", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "path", "type": "string", "is_config": false, "nullable": false}, {"name": "collect-count", "type": "uint32", "is_config": false}, {"name": "receive-instance-count", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "path"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::collect-tasks::collect-task", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "task-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "related-publish-task", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "task-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "group-id", "type": "string", "is_config": false, "nullable": false}, {"name": "destinations", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "group-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group::destinations::destination", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "address", "type": "string", "is_config": false, "nullable": false}, {"name": "port", "type": "uint16", "is_config": false, "nullable": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "send-count", "type": "uint32", "is_config": false}, {"name": "drop-count", "type": "uint32", "is_config": false}, {"name": "last-send-time", "type": "string", "is_config": false}, {"name": "publish-task-id", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "address", "port"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::collect-tasks::collect-task", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "task-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false}, {"name": "path", "type": "string", "is_config": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "collect-queue-length", "type": "uint32", "is_config": false}, {"name": "dispatch-queue-length", "type": "uint32", "is_config": false}, {"name": "receive-count", "type": "uint32", "is_config": false}, {"name": "receive-error-count", "type": "uint32", "is_config": false}, {"name": "drop-count", "type": "uint32", "is_config": false}, {"name": "last-collect-time", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "task-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::notification-subscriptions::notification-subscription", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "session-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "stream", "type": "string", "is_config": false}, {"name": "start-time", "type": "string", "is_config": false}, {"name": "stop-time", "type": "string", "is_config": false}, {"name": "send-count", "type": "uint32", "is_config": false}, {"name": "wrap-count", "type": "uint32", "is_config": false}, {"name": "error-count", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "session-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-patch:patch::patch-unit-infos::patch-unit-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "process-name", "type": "string", "is_config": false, "nullable": false}, {"name": "patch-unit-name", "type": "string", "is_config": false, "nullable": false}, {"name": "patch-file-name", "type": "string", "is_config": false}, {"name": "patch-type", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "valid", "type": "string", "is_config": false}, {"name": "runtime", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "process-name", "patch-unit-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-routing-lite:routing::routing-manage::routes::route", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "prefix", "type": "string", "is_config": false, "nullable": false}, {"name": "mask-length", "type": "uint8", "is_config": false, "nullable": false}, {"name": "nexthop-interface-name", "type": "string", "is_config": false, "nullable": false}, {"name": "nexthop-address", "type": "string", "is_config": false, "nullable": false}, {"name": "protocol-type", "type": "uint8", "is_config": false, "nullable": false}, {"name": "preference", "type": "uint32", "is_config": false}, {"name": "cost", "type": "uint32", "is_config": false}, {"name": "relay-nexthop", "type": "string", "is_config": false}, {"name": "direct-nexthop-address", "type": "string", "is_config": false}, {"name": "direct-nexthop-interface-name", "type": "string", "is_config": false}, {"name": "flag", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "indirect-id", "type": "string", "is_config": false}, {"name": "input-time", "type": "uint32", "is_config": false}, {"name": "output-time", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "prefix", "mask-length", "nexthop-interface-name", "nexthop-address", "protocol-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-routing-lite:routing::direct-routing::routes::route", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "prefix", "type": "string", "is_config": false, "nullable": false}, {"name": "mask-length", "type": "uint8", "is_config": false, "nullable": false}, {"name": "nexthop-interface-name", "type": "string", "is_config": false, "nullable": false}, {"name": "nexthop-address", "type": "string", "is_config": false, "nullable": false}, {"name": "protocol-type", "type": "uint8", "is_config": false, "nullable": false}, {"name": "preference", "type": "uint32", "is_config": false}, {"name": "cost", "type": "uint32", "is_config": false}, {"name": "relay-nexthop", "type": "string", "is_config": false}, {"name": "direct-nexthop-address", "type": "string", "is_config": false}, {"name": "direct-nexthop-interface-name", "type": "string", "is_config": false}, {"name": "flag", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "indirect-id", "type": "string", "is_config": false}, {"name": "input-time", "type": "uint32", "is_config": false}, {"name": "output-time", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "prefix", "mask-length", "nexthop-interface-name", "nexthop-address", "protocol-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-routing-lite:routing::static-routing::routes::route", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "prefix", "type": "string", "is_config": false, "nullable": false}, {"name": "mask-length", "type": "uint8", "is_config": false, "nullable": false}, {"name": "nexthop-interface-name", "type": "string", "is_config": false, "nullable": false}, {"name": "nexthop-address", "type": "string", "is_config": false, "nullable": false}, {"name": "protocol-type", "type": "uint8", "is_config": false, "nullable": false}, {"name": "preference", "type": "uint32", "is_config": false}, {"name": "cost", "type": "uint32", "is_config": false}, {"name": "relay-nexthop", "type": "string", "is_config": false}, {"name": "direct-nexthop-address", "type": "string", "is_config": false}, {"name": "direct-nexthop-interface-name", "type": "string", "is_config": false}, {"name": "flag", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "indirect-id", "type": "string", "is_config": false}, {"name": "input-time", "type": "uint32", "is_config": false}, {"name": "output-time", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "prefix", "mask-length", "nexthop-interface-name", "nexthop-address", "protocol-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-routing-lite:routing::user-network-routing::routes::route", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "prefix", "type": "string", "is_config": false, "nullable": false}, {"name": "mask-length", "type": "uint8", "is_config": false, "nullable": false}, {"name": "nexthop-interface-name", "type": "string", "is_config": false, "nullable": false}, {"name": "nexthop-address", "type": "string", "is_config": false, "nullable": false}, {"name": "protocol-type", "type": "uint8", "is_config": false, "nullable": false}, {"name": "preference", "type": "uint32", "is_config": false}, {"name": "cost", "type": "uint32", "is_config": false}, {"name": "relay-nexthop", "type": "string", "is_config": false}, {"name": "direct-nexthop-address", "type": "string", "is_config": false}, {"name": "direct-nexthop-interface-name", "type": "string", "is_config": false}, {"name": "flag", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "indirect-id", "type": "string", "is_config": false}, {"name": "input-time", "type": "uint32", "is_config": false}, {"name": "output-time", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "prefix", "mask-length", "nexthop-interface-name", "nexthop-address", "protocol-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-system:sysdiag::service-infos::service-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "service-name", "type": "string", "is_config": false, "nullable": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "start-times", "type": "uint32", "is_config": false}, {"name": "stop-times", "type": "uint32", "is_config": false}, {"name": "memory-peak", "type": "uint32", "is_config": false}, {"name": "running-duration", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "service-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-tid:tid::terminals::terminal", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "mac", "type": "string", "is_config": false, "nullable": false}, {"name": "fingerprints", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "mac"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-tid:tid::terminals::terminal::fingerprints::fingerprint", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "value", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "type", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-tid:tid::return-packets::return-packet", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint16", "is_config": false, "nullable": false}, {"name": "mac", "type": "string", "is_config": false}, {"name": "ip-address", "type": "string", "is_config": false}, {"name": "src-port", "type": "uint16", "is_config": false}, {"name": "dst-port", "type": "uint16", "is_config": false}, {"name": "layer", "type": "string", "is_config": false}, {"name": "ether-type", "type": "uint16", "is_config": false}, {"name": "transport-protocol", "type": "string", "is_config": false}, {"name": "app-protocol", "type": "string", "is_config": false}, {"name": "count", "type": "uint32", "is_config": false}, {"name": "payload-length", "type": "uint32", "is_config": false}, {"name": "time", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-tid:tid::monitoring-scan-records::monitoring-scan-record", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "mac", "type": "string", "is_config": false, "nullable": false}, {"name": "ip-address", "type": "string", "is_config": false}, {"name": "time", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "mac"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-tid:tid::statistics::statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "total-num", "type": "uint64", "is_config": false}, {"name": "error-num", "type": "uint64", "is_config": false}, {"name": "dropped-num", "type": "uint64", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose-omu", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-diagnose-omu:set-startup-mini-system-config", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "filename", "type": "string", "is_config": false, "nullable": false}]}, {"name": "huawei-diagnose-omu:clear-startup-mini-system-config", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "huawei-diagnose-omu:set-mini-system-startup", "type": "container", "is_config": false, "presence": true, "fields": []}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-devm-poe", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-devm-poe:devm-poe", "type": "container", "fields": [{"name": "global", "type": "container", "fields": [{"name": "anti-interference-freq", "type": "string", "default": "50Hz"}]}, {"name": "poes", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-devm-poe:devm-poe::poes::poe", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "position", "type": "string", "nullable": false}, {"name": "threshold-power", "type": "uint32", "default": 90}, {"name": "supply-power", "type": "uint32", "is_config": false}, {"name": "available-power", "type": "uint32", "is_config": false}, {"name": "consumption-power", "type": "uint32", "is_config": false}, {"name": "peak-power", "type": "uint32", "is_config": false}, {"name": "support-flag", "type": "boolean", "is_config": false}, {"name": "ports", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "position"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-devm-poe:devm-poe::poes::poe::ports::port", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interface-name", "type": "string", "nullable": false}, {"name": "power-off-time-range", "type": "string", "clause": [{"type": "leafref", "formula": "/yang/ds[name=$DS_NAME]/huawei-time-range/huawei-time-range:time-range/time-range-instances/huawei-time-range:time-range::time-range-instances::time-range-instance/name"}]}, {"name": "power-enable", "type": "boolean", "default": true}, {"name": "power-status", "type": "string", "is_config": false}, {"name": "port-power", "type": "uint32", "is_config": false}, {"name": "port-peak-power", "type": "uint32", "is_config": false}, {"name": "port-average-power", "type": "uint32", "is_config": false}, {"name": "trans-actual-power-enable", "type": "boolean", "default": false}], "keys": [{"name": "k0", "fields": [":pid", "interface-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-cpu-memory", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-cpu-memory:cpu-memory", "type": "container", "fields": [{"name": "board-cpu-infos", "type": "container", "is_config": false, "fields": []}, {"name": "board-memory-infos", "type": "container", "is_config": false, "fields": []}, {"name": "board-storage-partition-infos", "type": "container", "is_config": false, "fields": []}, {"name": "board-memory-top-infos", "type": "container", "is_config": false, "fields": []}, {"name": "board-cpu-top-infos", "type": "container", "is_config": false, "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-cpu-memory:cpu-memory::board-cpu-infos::board-cpu-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "cpu-id", "type": "string", "is_config": false, "nullable": false}, {"name": "overload-threshold", "type": "uint32", "is_config": false, "default": 90}, {"name": "unoverload-threshold", "type": "uint32", "is_config": false, "default": 75}, {"name": "system-cpu-usage", "type": "uint32", "is_config": false, "default": 0}], "keys": [{"name": "k0", "fields": [":pid", "slot-id", "cpu-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-cpu-memory:cpu-memory::board-memory-infos::board-memory-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "cpu-id", "type": "string", "is_config": false, "nullable": false}, {"name": "overload-threshold", "type": "uint32", "is_config": false}, {"name": "unoverload-threshold", "type": "uint32", "is_config": false}, {"name": "os-memory-total", "type": "uint32", "is_config": false}, {"name": "os-memory-use", "type": "uint32", "is_config": false}, {"name": "os-memory-free", "type": "uint32", "is_config": false}, {"name": "os-memory-usage", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot-id", "cpu-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-cpu-memory:cpu-memory::board-storage-partition-infos::board-storage-partition-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "cpu-id", "type": "string", "is_config": false, "nullable": false}, {"name": "partition-name", "type": "string", "is_config": false, "nullable": false}, {"name": "partition-size-all", "type": "uint64", "is_config": false}, {"name": "partition-size-use", "type": "uint64", "is_config": false}, {"name": "partition-memory-free", "type": "uint64", "is_config": false}, {"name": "partition-memory-usage", "type": "uint32", "is_config": false}, {"name": "partition-mounted-info", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot-id", "cpu-id", "partition-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-cpu-memory:cpu-memory::board-memory-top-infos::board-memory-top-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "process-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "process-name", "type": "string", "is_config": false}, {"name": "memory-usage", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot-id", "process-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-cpu-memory:cpu-memory::board-cpu-top-infos::board-cpu-top-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "thread-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "thread-name", "type": "string", "is_config": false}, {"name": "process-id", "type": "uint32", "is_config": false}, {"name": "process-name", "type": "string", "is_config": false}, {"name": "thread-cpu-usage", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot-id", "thread-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-codesign", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-codesign:codesign", "type": "container", "is_config": false, "fields": [{"name": "software-crls", "type": "container", "is_config": false, "fields": []}, {"name": "crl-names", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-codesign:software-crl-load", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "name", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-codesign:codesign::software-crls::software-crl", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "publisher", "type": "string", "is_config": false, "nullable": false}, {"name": "date", "type": "string", "is_config": false}, {"name": "valid-type", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot", "publisher"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-codesign:codesign::crl-names::crl-name", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-cli", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-cli:cli", "type": "container", "fields": [{"name": "header", "type": "container", "fields": [{"name": "login-information", "type": "choice", "fields": [{"name": "head-login-text", "type": "case", "fields": [{"name": "login-text", "type": "string"}]}]}]}, {"name": "query-diagnose-information-generate-process", "type": "container", "is_config": false, "fields": [{"name": "processsing-status", "type": "string", "is_config": false}, {"name": "fail-reason", "type": "string", "is_config": false}, {"name": "last-generate-time", "type": "string", "is_config": false}]}, {"name": "huawei-cli-lite:terminal", "type": "container", "fields": [{"name": "history-cmd-size", "type": "uint16", "default": 10}, {"name": "idle-timeout", "type": "uint32", "default": 300}, {"name": "split-screen", "type": "boolean", "default": true}]}]}, {"name": "huawei-cli:generate-diagnose-information", "type": "container", "is_config": false, "presence": true, "fields": []}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-cfg", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-cfg:cfg", "type": "container", "fields": [{"name": "autosave", "type": "container", "presence": true, "fields": [{"name": "interval-time", "type": "uint32", "default": 30, "clause": [{"type": "must", "formula": "../interval-time > ../delay-time"}]}, {"name": "delay-time", "type": "uint32", "default": 5, "clause": [{"type": "must", "formula": "../interval-time > ../delay-time"}]}]}, {"name": "startup-infos", "type": "container", "is_config": false, "fields": []}, {"name": "cfg-files", "type": "container", "is_config": false, "fields": []}, {"name": "config-change", "type": "container", "is_config": false, "fields": [{"name": "flow-id", "type": "uint32", "is_config": false}, {"name": "baseline-time", "type": "string", "is_config": false}]}]}, {"name": "huawei-cfg:save", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "filename", "type": "string", "is_config": false, "nullable": false}]}, {"name": "huawei-cfg:clear-startup", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "huawei-cfg:set-startup", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "filename", "type": "string", "is_config": false, "nullable": false}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-cfg:cfg::startup-infos::startup-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "position", "type": "string", "is_config": false, "nullable": false}, {"name": "configed-system-software", "type": "string", "is_config": false}, {"name": "current-system-software", "type": "string", "is_config": false}, {"name": "next-system-software", "type": "string", "is_config": false}, {"name": "current-cfg-file", "type": "string", "is_config": false}, {"name": "next-cfg-file", "type": "string", "is_config": false}, {"name": "current-patch-file", "type": "string", "is_config": false}, {"name": "next-patch-file", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "position"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-cfg:cfg::cfg-files::cfg-file", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "filename", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "filename"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-capture", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-capture:capture-packet", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "local-host", "type": "boolean", "is_config": false, "default": false}, {"name": "tunnel", "type": "choice", "is_config": false, "fields": [{"name": "normal", "type": "case", "is_config": false, "fields": [{"name": "vlan-id", "type": "uint16", "is_config": false}]}]}, {"name": "acl", "type": "choice", "is_config": false, "fields": [{"name": "acl", "type": "case", "is_config": false, "fields": [{"name": "acl-name", "type": "string", "is_config": false}]}]}, {"name": "packet-number", "type": "uint32", "is_config": false, "default": 100}, {"name": "packet-length", "type": "uint16", "is_config": false, "default": 64}, {"name": "timeout", "type": "uint32", "is_config": false, "default": 60}, {"name": "file-name", "type": "string", "is_config": false, "default": "capture.cap"}, {"name": "direction", "type": "string", "is_config": false}]}, {"name": "huawei-capture:stop-capture", "type": "container", "is_config": false, "presence": true, "fields": [{"name": "mode", "type": "choice", "is_config": false, "nullable": false, "fields": [{"name": "all", "type": "case", "is_config": false, "fields": [{"name": "all", "type": "string", "is_config": false}]}, {"name": "index", "type": "case", "is_config": false, "fields": [{"name": "capture-index", "type": "uint32", "is_config": false}]}]}]}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-capture:capture-packet::interface-name", "type": "leaf-list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interface-name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "interface-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "hua<PERSON>-aspf", "type": "list", "max-elements": 1, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "huawei-aspf:aspf", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-aspf:aspf::protocol", "type": "leaf-list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "protocol", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "protocol"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]