[{"name": "hw-res-pool-an:resource-pool-esn.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "type", "type": "string", "nullable": false, "length": "1..32"}, {"name": "enabled", "type": "boolean", "nullable": true}, {"name": "cfg-net-id", "type": "string", "nullable": true, "length": "0..64"}, {"name": "consumptions", "type": "container", "fields": []}, {"name": "rtu-consumptions", "type": "container", "fields": []}], "keys": [{"name": "hw-res-pool-an:resource-pool-esn.1.PK", "index": {"type": "primary"}, "node": "hw-res-pool-an:resource-pool-esn.1", "fields": ["PID", "type"], "constraints": {"unique": true}}]}, {"name": "hw-res-pool-an:consumption.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "esn", "type": "string", "nullable": false, "length": "1..32"}], "keys": [{"name": "hw-res-pool-an:consumption.1.PK", "index": {"type": "primary"}, "node": "hw-res-pool-an:consumption.1", "fields": ["PID", "esn"], "constraints": {"unique": true}}]}, {"name": "hw-res-pool-an:rtu-consumption.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "esn", "type": "string", "nullable": false, "length": "1..32"}, {"name": "net-id", "type": "string", "nullable": false, "length": "0..64"}], "keys": [{"name": "hw-res-pool-an:rtu-consumption.1.PK", "index": {"type": "primary"}, "node": "hw-res-pool-an:rtu-consumption.1", "fields": ["PID", "esn", "net-id"], "constraints": {"unique": true}}]}, {"name": "hw-res-pool-an:resource-pool.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "resource-name", "type": "identity", "enumerate": [{"name": "huawei-resource-pool-an:dhcpv4s", "value": 0, "derived-paths": [{"derived-path": "huawei-resource-pool-an:license-type/huawei-resource-pool-an:dhcpv4s"}]}, {"name": "huawei-resource-pool-an:dhcpv6s", "value": 1, "derived-paths": [{"derived-path": "huawei-resource-pool-an:license-type/huawei-resource-pool-an:dhcpv6s"}]}, {"name": "huawei-resource-pool-an:bw40Gto80G", "value": 2, "derived-paths": [{"derived-path": "huawei-resource-pool-an:license-type/huawei-resource-pool-an:bw40Gto80G"}]}, {"name": "huawei-resource-pool-an:bw50Gto100G", "value": 3, "derived-paths": [{"derived-path": "huawei-resource-pool-an:license-type/huawei-resource-pool-an:bw50Gto100G"}]}, {"name": "huawei-resource-pool-an:bw80Gto160G", "value": 4, "derived-paths": [{"derived-path": "huawei-resource-pool-an:license-type/huawei-resource-pool-an:bw80Gto160G"}]}, {"name": "huawei-resource-pool-an:bw100Gto200G", "value": 5, "derived-paths": [{"derived-path": "huawei-resource-pool-an:license-type/huawei-resource-pool-an:bw100Gto200G"}]}, {"name": "huawei-resource-pool-an:third-party-gpon-onu", "value": 6, "derived-paths": [{"derived-path": "huawei-resource-pool-an:license-type/huawei-resource-pool-an:third-party-gpon-onu"}]}, {"name": "huawei-resource-pool-an:third-party-10gpon-onu", "value": 7, "derived-paths": [{"derived-path": "huawei-resource-pool-an:license-type/huawei-resource-pool-an:third-party-10gpon-onu"}]}, {"name": "huawei-resource-pool-an:third-party-50gpon-onu", "value": 8, "derived-paths": [{"derived-path": "huawei-resource-pool-an:license-type/huawei-resource-pool-an:third-party-50gpon-onu"}]}, {"name": "huawei-resource-pool-an:flex-Level1-port", "value": 9, "derived-paths": [{"derived-path": "huawei-resource-pool-an:license-type/huawei-resource-pool-an:flex-Level1-port"}]}, {"name": "huawei-resource-pool-an:flex-Level2-port", "value": 10, "derived-paths": [{"derived-path": "huawei-resource-pool-an:license-type/huawei-resource-pool-an:flex-Level2-port"}]}, {"name": "huawei-resource-pool-an:onu-vxlan-feature", "value": 11, "derived-paths": [{"derived-path": "huawei-resource-pool-an:license-type/huawei-resource-pool-an:onu-vxlan-feature"}]}, {"name": "huawei-resource-pool-an:onu-vpn-feature", "value": 12, "derived-paths": [{"derived-path": "huawei-resource-pool-an:license-type/huawei-resource-pool-an:onu-vpn-feature"}]}, {"name": "huawei-resource-pool-an:onu-multi-burst", "value": 13, "derived-paths": [{"derived-path": "huawei-resource-pool-an:license-type/huawei-resource-pool-an:onu-multi-burst"}]}], "nullable": false}, {"name": "enabled", "type": "boolean", "nullable": true}], "keys": [{"name": "hw-res-pool-an:resource-pool.1.PK", "index": {"type": "primary"}, "node": "hw-res-pool-an:resource-pool.1", "fields": ["PID", "resource-name"], "constraints": {"unique": true}}]}, {"name": "hw:component.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "identity", "enumerate": [{"name": "iana-hardware:unknown", "value": 0, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:unknown"}]}, {"name": "iana-hardware:chassis", "value": 1, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:chassis"}]}, {"name": "iana-hardware:backplane", "value": 2, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:backplane"}]}, {"name": "iana-hardware:container", "value": 3, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:container"}]}, {"name": "bbf-hardware-types:slot", "value": 4, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:container/bbf-hardware-types:slot"}]}, {"name": "bbf-hardware-types:cage", "value": 5, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:container/bbf-hardware-types:cage"}]}, {"name": "iana-hardware:power-supply", "value": 6, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:power-supply"}]}, {"name": "iana-hardware:fan", "value": 7, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:fan"}]}, {"name": "iana-hardware:sensor", "value": 8, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:sensor"}]}, {"name": "iana-hardware:module", "value": 9, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:module"}]}, {"name": "bbf-hardware-types:board", "value": 10, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:module/bbf-hardware-types:board"}]}, {"name": "bbf-hardware-types:transceiver", "value": 11, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:module/bbf-hardware-types:transceiver"}]}, {"name": "iana-hardware:port", "value": 12, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:port"}]}, {"name": "bbf-hardware-types:transceiver-link", "value": 13, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:port/bbf-hardware-types:transceiver-link"}]}, {"name": "bbf-hardware-types:rj45", "value": 14, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:port/bbf-hardware-types:rj45"}]}, {"name": "bbf-hardware-types:fastdsl-tp", "value": 15, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:port/bbf-hardware-types:fastdsl-tp"}]}, {"name": "bbf-hardware-types:fastdsl-coax", "value": 16, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:port/bbf-hardware-types:fastdsl-coax"}]}, {"name": "iana-hardware:stack", "value": 17, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:stack"}]}, {"name": "iana-hardware:cpu", "value": 18, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:cpu"}]}, {"name": "iana-hardware:energy-object", "value": 19, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:energy-object"}]}, {"name": "iana-hardware:battery", "value": 20, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:battery"}]}, {"name": "iana-hardware:storage-drive", "value": 21, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/iana-hardware:storage-drive"}]}, {"name": "huawei-energy-management-an:channel", "value": 22, "derived-paths": [{"derived-path": "iana-hardware:hardware-class/huawei-energy-management-an:channel"}]}], "nullable": true}, {"name": "parent", "type": "string", "nullable": true}, {"name": "parent-rel-pos", "type": "int32", "nullable": true, "range": "0..2147483647"}, {"name": "model-name", "type": "string", "nullable": true}, {"name": "alias", "type": "string", "nullable": true}, {"name": "asset-id", "type": "string", "nullable": true}, {"name": "transceiver", "type": "container", "fields": [{"name": "identifiers-and-codes", "type": "container", "fields": [{"name": "compliance-codes", "type": "container", "fields": []}]}, {"name": "link-length", "type": "container", "fields": []}, {"name": "diagnostics", "type": "container", "fields": []}, {"name": "thresholds", "type": "container", "fields": []}], "clause": [{"type": "when", "formula": "derived-from-or-self(../class,'bbf-hardware-types:transceiver')"}]}, {"name": "transceiver-link", "type": "container", "fields": [{"name": "wavelength-or-specification-compliance", "type": "choice", "fields": [{"name": "wavelength", "type": "case", "fields": []}]}, {"name": "diagnostics", "type": "container", "fields": []}], "clause": [{"type": "when", "formula": "derived-from-or-self(../class,'bbf-hardware-types:transceiver-link')"}]}, {"name": "energy-saving", "type": "container", "fields": [{"name": "enabled", "type": "boolean", "nullable": true, "default": true}]}, {"name": "warranty", "type": "container", "fields": [{"name": "support-info", "type": "enum", "enumerate": [{"name": "support", "value": 0}, {"name": "notsupport", "value": 1}], "nullable": true}, {"name": "start-point-date", "type": "string", "nullable": true}, {"name": "service-session", "type": "uint32", "nullable": true}, {"name": "first-activate-date", "type": "string", "nullable": true}, {"name": "mfg-date", "type": "string", "nullable": true}]}, {"name": "service-cfg-type", "type": "container", "fields": [{"name": "cfg-type", "type": "enum", "enumerate": [{"name": "port-vlan", "value": 0}, {"name": "service-port", "value": 1}], "nullable": true}]}], "keys": [{"name": "hw:component.1.PK", "index": {"type": "primary"}, "node": "hw:component.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "hw:uri.1", "type": "leaf-list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "uri", "type": "string", "nullable": false}], "keys": [{"name": "hw:uri.1.PK", "index": {"type": "primary"}, "node": "hw:uri.1", "fields": ["PID", "uri"], "constraints": {"unique": true}}]}, {"name": "if:interface.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "length": "1..63"}, {"name": "description", "type": "string", "nullable": true, "length": "0..255"}, {"name": "type", "type": "identity", "enumerate": [{"name": "iana-if-type:iana-interface-type", "value": 0, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type"}]}, {"name": "iana-if-type:other", "value": 1, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:other"}]}, {"name": "iana-if-type:regular1822", "value": 2, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:regular1822"}]}, {"name": "iana-if-type:hdh1822", "value": 3, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:hdh1822"}]}, {"name": "iana-if-type:ddnX25", "value": 4, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ddnX25"}]}, {"name": "iana-if-type:rfc877x25", "value": 5, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:rfc877x25"}]}, {"name": "iana-if-type:ethernetCsmacd", "value": 6, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernetCsmacd"}]}, {"name": "huawei-tc-an:eth-sub-type-fe-elc", "value": 7, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernetCsmacd/huawei-tc-an:eth-sub-type-fe-elc"}]}, {"name": "huawei-tc-an:eth-sub-type-fe-opt", "value": 8, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernetCsmacd/huawei-tc-an:eth-sub-type-fe-opt"}]}, {"name": "huawei-tc-an:eth-sub-type-ge-elc", "value": 9, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernetCsmacd/huawei-tc-an:eth-sub-type-ge-elc"}]}, {"name": "huawei-tc-an:eth-sub-type-ge-opt", "value": 10, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernetCsmacd/huawei-tc-an:eth-sub-type-ge-opt"}]}, {"name": "huawei-tc-an:eth-sub-type-10ge-opt", "value": 11, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernetCsmacd/huawei-tc-an:eth-sub-type-10ge-opt"}]}, {"name": "huawei-tc-an:eth-sub-type-100ge-opt", "value": 12, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernetCsmacd/huawei-tc-an:eth-sub-type-100ge-opt"}]}, {"name": "huawei-tc-an:eth-sub-type-40ge-opt", "value": 13, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernetCsmacd/huawei-tc-an:eth-sub-type-40ge-opt"}]}, {"name": "huawei-tc-an:eth-sub-type-25ge-opt", "value": 14, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernetCsmacd/huawei-tc-an:eth-sub-type-25ge-opt"}]}, {"name": "iana-if-type:iso88023Csmacd", "value": 15, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:iso88023Csmacd"}]}, {"name": "iana-if-type:iso88024TokenBus", "value": 16, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:iso88024TokenBus"}]}, {"name": "iana-if-type:iso88025TokenRing", "value": 17, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:iso88025TokenRing"}]}, {"name": "iana-if-type:iso88026Man", "value": 18, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:iso88026Man"}]}, {"name": "iana-if-type:star<PERSON>an", "value": 19, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:starLan"}]}, {"name": "iana-if-type:proteon10Mbit", "value": 20, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:proteon10Mbit"}]}, {"name": "iana-if-type:proteon80Mbit", "value": 21, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:proteon80Mbit"}]}, {"name": "iana-if-type:hyperchannel", "value": 22, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:hyperchannel"}]}, {"name": "iana-if-type:fddi", "value": 23, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:fddi"}]}, {"name": "iana-if-type:lapb", "value": 24, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:lapb"}]}, {"name": "iana-if-type:sdlc", "value": 25, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sdlc"}]}, {"name": "iana-if-type:ds1", "value": 26, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ds1"}]}, {"name": "iana-if-type:e1", "value": 27, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:e1"}]}, {"name": "iana-if-type:basicISDN", "value": 28, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:basicISDN"}]}, {"name": "iana-if-type:primaryISDN", "value": 29, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:primaryISDN"}]}, {"name": "iana-if-type:propPointToPointSerial", "value": 30, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propPointToPointSerial"}]}, {"name": "iana-if-type:ppp", "value": 31, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ppp"}]}, {"name": "iana-if-type:softwareLoopback", "value": 32, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:softwareLoopback"}]}, {"name": "iana-if-type:eon", "value": 33, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:eon"}]}, {"name": "iana-if-type:ethernet3Mbit", "value": 34, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ethernet3Mbit"}]}, {"name": "iana-if-type:nsip", "value": 35, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:nsip"}]}, {"name": "iana-if-type:slip", "value": 36, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:slip"}]}, {"name": "iana-if-type:ultra", "value": 37, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ultra"}]}, {"name": "iana-if-type:ds3", "value": 38, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ds3"}]}, {"name": "iana-if-type:sip", "value": 39, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sip"}]}, {"name": "iana-if-type:frame<PERSON><PERSON>y", "value": 40, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:frameRelay"}]}, {"name": "iana-if-type:rs232", "value": 41, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:rs232"}]}, {"name": "iana-if-type:para", "value": 42, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:para"}]}, {"name": "iana-if-type:arcnet", "value": 43, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:arcnet"}]}, {"name": "iana-if-type:arcnetPlus", "value": 44, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:arcnetPlus"}]}, {"name": "iana-if-type:atm", "value": 45, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atm"}]}, {"name": "iana-if-type:miox25", "value": 46, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:miox25"}]}, {"name": "iana-if-type:sonet", "value": 47, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sonet"}]}, {"name": "iana-if-type:x25ple", "value": 48, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:x25ple"}]}, {"name": "iana-if-type:iso88022llc", "value": 49, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:iso88022llc"}]}, {"name": "iana-if-type:localTalk", "value": 50, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:localTalk"}]}, {"name": "iana-if-type:smdsDxi", "value": 51, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:smdsDxi"}]}, {"name": "iana-if-type:frameRelayService", "value": 52, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:frameRelayService"}]}, {"name": "iana-if-type:v35", "value": 53, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:v35"}]}, {"name": "iana-if-type:hssi", "value": 54, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:hssi"}]}, {"name": "iana-if-type:hippi", "value": 55, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:hippi"}]}, {"name": "iana-if-type:modem", "value": 56, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:modem"}]}, {"name": "iana-if-type:aal5", "value": 57, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aal5"}]}, {"name": "iana-if-type:sonetPath", "value": 58, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sonetPath"}]}, {"name": "iana-if-type:sonetVT", "value": 59, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sonetVT"}]}, {"name": "iana-if-type:smdsIcip", "value": 60, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:smdsIcip"}]}, {"name": "iana-if-type:propVirtual", "value": 61, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propVirtual"}]}, {"name": "iana-if-type:propMultiplexor", "value": 62, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propMultiplexor"}]}, {"name": "iana-if-type:ieee80212", "value": 63, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ieee80212"}]}, {"name": "iana-if-type:fibreChannel", "value": 64, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:fibreChannel"}]}, {"name": "iana-if-type:hippiInterface", "value": 65, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:hippiInterface"}]}, {"name": "iana-if-type:frameRelayInterconnect", "value": 66, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:frameRelayInterconnect"}]}, {"name": "iana-if-type:aflane8023", "value": 67, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aflane8023"}]}, {"name": "iana-if-type:aflane8025", "value": 68, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aflane8025"}]}, {"name": "iana-if-type:cctEmul", "value": 69, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:cctEmul"}]}, {"name": "iana-if-type:fastEther", "value": 70, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:fastEther"}]}, {"name": "iana-if-type:isdn", "value": 71, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:isdn"}]}, {"name": "iana-if-type:v11", "value": 72, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:v11"}]}, {"name": "iana-if-type:v36", "value": 73, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:v36"}]}, {"name": "iana-if-type:g703at64k", "value": 74, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:g703at64k"}]}, {"name": "iana-if-type:g703at2mb", "value": 75, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:g703at2mb"}]}, {"name": "iana-if-type:qllc", "value": 76, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:qllc"}]}, {"name": "iana-if-type:fastEtherFX", "value": 77, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:fastEtherFX"}]}, {"name": "iana-if-type:channel", "value": 78, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:channel"}]}, {"name": "iana-if-type:ieee80211", "value": 79, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ieee80211"}]}, {"name": "iana-if-type:ibm370parChan", "value": 80, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ibm370parChan"}]}, {"name": "iana-if-type:escon", "value": 81, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:escon"}]}, {"name": "iana-if-type:dlsw", "value": 82, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dlsw"}]}, {"name": "iana-if-type:isdns", "value": 83, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:isdns"}]}, {"name": "iana-if-type:isdnu", "value": 84, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:isdnu"}]}, {"name": "iana-if-type:lapd", "value": 85, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:lapd"}]}, {"name": "iana-if-type:ipSwitch", "value": 86, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ipSwitch"}]}, {"name": "iana-if-type:rsrb", "value": 87, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:rsrb"}]}, {"name": "iana-if-type:atmLogical", "value": 88, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atmLogical"}]}, {"name": "iana-if-type:ds0", "value": 89, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ds0"}]}, {"name": "iana-if-type:ds0Bundle", "value": 90, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ds0Bundle"}]}, {"name": "iana-if-type:bsc", "value": 91, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:bsc"}]}, {"name": "iana-if-type:async", "value": 92, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:async"}]}, {"name": "iana-if-type:cnr", "value": 93, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:cnr"}]}, {"name": "iana-if-type:iso88025Dtr", "value": 94, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:iso88025Dtr"}]}, {"name": "iana-if-type:eplrs", "value": 95, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:eplrs"}]}, {"name": "iana-if-type:arap", "value": 96, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:arap"}]}, {"name": "iana-if-type:propCnls", "value": 97, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propCnls"}]}, {"name": "iana-if-type:hostPad", "value": 98, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:hostPad"}]}, {"name": "iana-if-type:termPad", "value": 99, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:termPad"}]}, {"name": "iana-if-type:frameRelayMPI", "value": 100, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:frameRelayMPI"}]}, {"name": "iana-if-type:x213", "value": 101, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:x213"}]}, {"name": "iana-if-type:adsl", "value": 102, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:adsl"}]}, {"name": "iana-if-type:radsl", "value": 103, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:radsl"}]}, {"name": "iana-if-type:sdsl", "value": 104, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sdsl"}]}, {"name": "iana-if-type:vdsl", "value": 105, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:vdsl"}]}, {"name": "iana-if-type:iso88025CRFPInt", "value": 106, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:iso88025CRFPInt"}]}, {"name": "iana-if-type:myrinet", "value": 107, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:myrinet"}]}, {"name": "iana-if-type:voiceEM", "value": 108, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceEM"}]}, {"name": "iana-if-type:voiceFXO", "value": 109, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceFXO"}]}, {"name": "iana-if-type:voiceFXS", "value": 110, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceFXS"}]}, {"name": "iana-if-type:voiceEncap", "value": 111, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceEncap"}]}, {"name": "iana-if-type:voiceOverIp", "value": 112, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceOverIp"}]}, {"name": "iana-if-type:atmDxi", "value": 113, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atmDxi"}]}, {"name": "iana-if-type:atm<PERSON>uni", "value": 114, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atmFuni"}]}, {"name": "iana-if-type:atmIma", "value": 115, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atmIma"}]}, {"name": "iana-if-type:pppMultilinkBundle", "value": 116, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:pppMultilinkBundle"}]}, {"name": "iana-if-type:ipOverCdlc", "value": 117, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ipOverCdlc"}]}, {"name": "iana-if-type:ipOverClaw", "value": 118, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ipOverClaw"}]}, {"name": "iana-if-type:stackToStack", "value": 119, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:stackToStack"}]}, {"name": "iana-if-type:virtualIpAddress", "value": 120, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:virtualIpAddress"}]}, {"name": "iana-if-type:mpc", "value": 121, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:mpc"}]}, {"name": "iana-if-type:ipOverAtm", "value": 122, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ipOverAtm"}]}, {"name": "iana-if-type:iso88025Fiber", "value": 123, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:iso88025Fiber"}]}, {"name": "iana-if-type:tdlc", "value": 124, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:tdlc"}]}, {"name": "iana-if-type:gigabitEthernet", "value": 125, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:gigabitEthernet"}]}, {"name": "iana-if-type:hdlc", "value": 126, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:hdlc"}]}, {"name": "iana-if-type:lapf", "value": 127, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:lapf"}]}, {"name": "iana-if-type:v37", "value": 128, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:v37"}]}, {"name": "iana-if-type:x25mlp", "value": 129, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:x25mlp"}]}, {"name": "iana-if-type:x25huntGroup", "value": 130, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:x25huntGroup"}]}, {"name": "iana-if-type:transpHdlc", "value": 131, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:transpHdlc"}]}, {"name": "iana-if-type:interleave", "value": 132, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:interleave"}]}, {"name": "iana-if-type:fast", "value": 133, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:fast"}]}, {"name": "iana-if-type:ip", "value": 134, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ip"}]}, {"name": "iana-if-type:docsCableMaclayer", "value": 135, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableMaclayer"}]}, {"name": "iana-if-type:docsCableDownstream", "value": 136, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableDownstream"}]}, {"name": "iana-if-type:docsCableUpstream", "value": 137, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableUpstream"}]}, {"name": "iana-if-type:a12MppSwitch", "value": 138, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:a12MppSwitch"}]}, {"name": "iana-if-type:tunnel", "value": 139, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:tunnel"}]}, {"name": "iana-if-type:coffee", "value": 140, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:coffee"}]}, {"name": "iana-if-type:ces", "value": 141, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ces"}]}, {"name": "iana-if-type:atmSubInterface", "value": 142, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atmSubInterface"}]}, {"name": "iana-if-type:l2vlan", "value": 143, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:l2vlan"}]}, {"name": "iana-if-type:l3ipvlan", "value": 144, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:l3ipvlan"}]}, {"name": "iana-if-type:l3ipxvlan", "value": 145, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:l3ipxvlan"}]}, {"name": "iana-if-type:digitalPowerline", "value": 146, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:digitalPowerline"}]}, {"name": "iana-if-type:mediaMailOverIp", "value": 147, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:mediaMailOverIp"}]}, {"name": "iana-if-type:dtm", "value": 148, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dtm"}]}, {"name": "iana-if-type:dcn", "value": 149, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dcn"}]}, {"name": "iana-if-type:ipForward", "value": 150, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ipForward"}]}, {"name": "iana-if-type:msdsl", "value": 151, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:msdsl"}]}, {"name": "iana-if-type:ieee1394", "value": 152, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ieee1394"}]}, {"name": "iana-if-type:if-gsn", "value": 153, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:if-gsn"}]}, {"name": "iana-if-type:dvbRccMacLayer", "value": 154, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dvbRccMacLayer"}]}, {"name": "iana-if-type:dvbRccDownstream", "value": 155, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dvbRccDownstream"}]}, {"name": "iana-if-type:dvbRccUpstream", "value": 156, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dvbRccUpstream"}]}, {"name": "iana-if-type:atmVirtual", "value": 157, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atmVirtual"}]}, {"name": "iana-if-type:mplsTunnel", "value": 158, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:mplsTunnel"}]}, {"name": "iana-if-type:srp", "value": 159, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:srp"}]}, {"name": "iana-if-type:voiceOverAtm", "value": 160, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceOverAtm"}]}, {"name": "iana-if-type:voiceOverFrameRelay", "value": 161, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceOverFrameRelay"}]}, {"name": "iana-if-type:idsl", "value": 162, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:idsl"}]}, {"name": "iana-if-type:compositeLink", "value": 163, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:compositeLink"}]}, {"name": "iana-if-type:ss7SigLink", "value": 164, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ss7SigLink"}]}, {"name": "iana-if-type:propWirelessP2P", "value": 165, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propWirelessP2P"}]}, {"name": "iana-if-type:frForward", "value": 166, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:frForward"}]}, {"name": "iana-if-type:rfc1483", "value": 167, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:rfc1483"}]}, {"name": "iana-if-type:usb", "value": 168, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:usb"}]}, {"name": "iana-if-type:ieee8023adLag", "value": 169, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ieee8023adLag"}]}, {"name": "iana-if-type:bgppolicyaccounting", "value": 170, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:bgppolicyaccounting"}]}, {"name": "iana-if-type:frf16MfrBundle", "value": 171, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:frf16MfrBundle"}]}, {"name": "iana-if-type:h323Gatekeeper", "value": 172, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:h323Gatekeeper"}]}, {"name": "iana-if-type:h323Proxy", "value": 173, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:h323Proxy"}]}, {"name": "iana-if-type:mpls", "value": 174, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:mpls"}]}, {"name": "iana-if-type:mfSigLink", "value": 175, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:mfSigLink"}]}, {"name": "iana-if-type:hdsl2", "value": 176, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:hdsl2"}]}, {"name": "iana-if-type:shdsl", "value": 177, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:shdsl"}]}, {"name": "iana-if-type:ds1FDL", "value": 178, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ds1FDL"}]}, {"name": "iana-if-type:pos", "value": 179, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:pos"}]}, {"name": "iana-if-type:dvbAsiIn", "value": 180, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dvbAsiIn"}]}, {"name": "iana-if-type:dvbAsiOut", "value": 181, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dvbAsiOut"}]}, {"name": "iana-if-type:plc", "value": 182, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:plc"}]}, {"name": "iana-if-type:nfas", "value": 183, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:nfas"}]}, {"name": "iana-if-type:tr008", "value": 184, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:tr008"}]}, {"name": "iana-if-type:gr303RDT", "value": 185, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:gr303RDT"}]}, {"name": "iana-if-type:gr303IDT", "value": 186, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:gr303IDT"}]}, {"name": "iana-if-type:isup", "value": 187, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:isup"}]}, {"name": "iana-if-type:propDocsWirelessMaclayer", "value": 188, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propDocsWirelessMaclayer"}]}, {"name": "iana-if-type:propDocsWirelessDownstream", "value": 189, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propDocsWirelessDownstream"}]}, {"name": "iana-if-type:propDocsWirelessUpstream", "value": 190, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propDocsWirelessUpstream"}]}, {"name": "iana-if-type:hiperlan2", "value": 191, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:hiperlan2"}]}, {"name": "iana-if-type:propBWAp2Mp", "value": 192, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propBWAp2Mp"}]}, {"name": "iana-if-type:sonetOverheadChannel", "value": 193, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sonetOverheadChannel"}]}, {"name": "iana-if-type:digitalWrapperOverheadChannel", "value": 194, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:digitalWrapperOverheadChannel"}]}, {"name": "iana-if-type:aal2", "value": 195, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aal2"}]}, {"name": "iana-if-type:radioMAC", "value": 196, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:radioMAC"}]}, {"name": "iana-if-type:atmRadio", "value": 197, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atmRadio"}]}, {"name": "iana-if-type:imt", "value": 198, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:imt"}]}, {"name": "iana-if-type:mvl", "value": 199, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:mvl"}]}, {"name": "iana-if-type:reachDSL", "value": 200, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:reachDSL"}]}, {"name": "iana-if-type:frDlciEndPt", "value": 201, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:frDlciEndPt"}]}, {"name": "iana-if-type:atmVciEndPt", "value": 202, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atmVciEndPt"}]}, {"name": "iana-if-type:opticalChannel", "value": 203, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:opticalChannel"}]}, {"name": "iana-if-type:opticalTransport", "value": 204, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:opticalTransport"}]}, {"name": "iana-if-type:propAtm", "value": 205, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:propAtm"}]}, {"name": "iana-if-type:voiceOverCable", "value": 206, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceOverCable"}]}, {"name": "iana-if-type:infiniband", "value": 207, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:infiniband"}]}, {"name": "iana-if-type:teLink", "value": 208, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:teLink"}]}, {"name": "iana-if-type:q2931", "value": 209, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:q2931"}]}, {"name": "iana-if-type:virtualTg", "value": 210, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:virtualTg"}]}, {"name": "iana-if-type:sipTg", "value": 211, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sipTg"}]}, {"name": "iana-if-type:sipSig", "value": 212, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sipSig"}]}, {"name": "iana-if-type:docsCableUpstreamChannel", "value": 213, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableUpstreamChannel"}]}, {"name": "iana-if-type:econet", "value": 214, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:econet"}]}, {"name": "iana-if-type:pon155", "value": 215, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:pon155"}]}, {"name": "iana-if-type:pon622", "value": 216, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:pon622"}]}, {"name": "iana-if-type:bridge", "value": 217, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:bridge"}]}, {"name": "iana-if-type:linegroup", "value": 218, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:linegroup"}]}, {"name": "iana-if-type:voiceEMFGD", "value": 219, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceEMFGD"}]}, {"name": "iana-if-type:voiceFGDEANA", "value": 220, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceFGDEANA"}]}, {"name": "iana-if-type:voiceDID", "value": 221, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceDID"}]}, {"name": "iana-if-type:mpegTransport", "value": 222, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:mpegTransport"}]}, {"name": "iana-if-type:sixToFour", "value": 223, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sixToFour"}]}, {"name": "iana-if-type:gtp", "value": 224, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:gtp"}]}, {"name": "iana-if-type:pdnEtherLoop1", "value": 225, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:pdnEtherLoop1"}]}, {"name": "iana-if-type:pdnEtherLoop2", "value": 226, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:pdnEtherLoop2"}]}, {"name": "iana-if-type:opticalChannelGroup", "value": 227, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:opticalChannelGroup"}]}, {"name": "iana-if-type:homepna", "value": 228, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:homepna"}]}, {"name": "iana-if-type:gfp", "value": 229, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:gfp"}]}, {"name": "iana-if-type:ciscoISLvlan", "value": 230, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ciscoISLvlan"}]}, {"name": "iana-if-type:actelisMetaLOOP", "value": 231, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:actelisMetaLOOP"}]}, {"name": "iana-if-type:fcipLink", "value": 232, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:fcipLink"}]}, {"name": "iana-if-type:rpr", "value": 233, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:rpr"}]}, {"name": "iana-if-type:qam", "value": 234, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:qam"}]}, {"name": "iana-if-type:lmp", "value": 235, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:lmp"}]}, {"name": "iana-if-type:cblVectaStar", "value": 236, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:cblVectaStar"}]}, {"name": "iana-if-type:docsCableMCmtsDownstream", "value": 237, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableMCmtsDownstream"}]}, {"name": "iana-if-type:adsl2", "value": 238, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:adsl2"}]}, {"name": "iana-if-type:macSecControlledIF", "value": 239, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:macSecControlledIF"}]}, {"name": "iana-if-type:macSecUncontrolledIF", "value": 240, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:macSecUncontrolledIF"}]}, {"name": "iana-if-type:aviciOpticalEther", "value": 241, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aviciOpticalEther"}]}, {"name": "iana-if-type:atmbond", "value": 242, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:atmbond"}]}, {"name": "iana-if-type:voiceFGDOS", "value": 243, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceFGDOS"}]}, {"name": "iana-if-type:mocaVersion1", "value": 244, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:mocaVersion1"}]}, {"name": "iana-if-type:ieee80216WMAN", "value": 245, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ieee80216WMAN"}]}, {"name": "iana-if-type:adsl2plus", "value": 246, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:adsl2plus"}]}, {"name": "iana-if-type:dvbRcsMacLayer", "value": 247, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dvbRcsMacLayer"}]}, {"name": "iana-if-type:dvbTdm", "value": 248, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dvbTdm"}]}, {"name": "iana-if-type:dvbRcsTdma", "value": 249, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:dvbRcsTdma"}]}, {"name": "iana-if-type:x86Laps", "value": 250, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:x86Laps"}]}, {"name": "iana-if-type:wwan<PERSON>", "value": 251, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:wwanPP"}]}, {"name": "iana-if-type:wwanPP2", "value": 252, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:wwanPP2"}]}, {"name": "iana-if-type:voiceEBS", "value": 253, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:voiceEBS"}]}, {"name": "iana-if-type:ifPwType", "value": 254, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ifPwType"}]}, {"name": "iana-if-type:ilan", "value": 255, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ilan"}]}, {"name": "iana-if-type:pip", "value": 256, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:pip"}]}, {"name": "iana-if-type:aluELP", "value": 257, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aluELP"}]}, {"name": "iana-if-type:gpon", "value": 258, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:gpon"}]}, {"name": "iana-if-type:vdsl2", "value": 259, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:vdsl2"}]}, {"name": "iana-if-type:capwapDot11Profile", "value": 260, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:capwapDot11Profile"}]}, {"name": "iana-if-type:capwapDot11Bss", "value": 261, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:capwapDot11Bss"}]}, {"name": "iana-if-type:capwapWtpVirtualRadio", "value": 262, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:capwapWtpVirtualRadio"}]}, {"name": "iana-if-type:bits", "value": 263, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:bits"}]}, {"name": "iana-if-type:docsCableUpstreamRfPort", "value": 264, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableUpstreamRfPort"}]}, {"name": "iana-if-type:cableDownstreamRfPort", "value": 265, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:cableDownstreamRfPort"}]}, {"name": "iana-if-type:vmwareVirtualNic", "value": 266, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:vmwareVirtualNic"}]}, {"name": "iana-if-type:ieee802154", "value": 267, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ieee802154"}]}, {"name": "iana-if-type:otnOdu", "value": 268, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:otnOdu"}]}, {"name": "iana-if-type:otnOtu", "value": 269, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:otnOtu"}]}, {"name": "iana-if-type:ifVfiType", "value": 270, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ifVfiType"}]}, {"name": "iana-if-type:g9981", "value": 271, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:g9981"}]}, {"name": "iana-if-type:g9982", "value": 272, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:g9982"}]}, {"name": "iana-if-type:g9983", "value": 273, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:g9983"}]}, {"name": "iana-if-type:alu<PERSON><PERSON>n", "value": 274, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aluEpon"}]}, {"name": "iana-if-type:alu<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 275, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aluEponOnu"}]}, {"name": "iana-if-type:aluEponPhysicalUni", "value": 276, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aluEponPhysicalUni"}]}, {"name": "iana-if-type:aluEponLogicalLink", "value": 277, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aluEponLogicalLink"}]}, {"name": "iana-if-type:alu<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 278, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aluGponOnu"}]}, {"name": "iana-if-type:aluGponPhysicalUni", "value": 279, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:aluGponPhysicalUni"}]}, {"name": "iana-if-type:vmwareNicTeam", "value": 280, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:vmwareNicTeam"}]}, {"name": "iana-if-type:docsOfdmDownstream", "value": 281, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsOfdmDownstream"}]}, {"name": "iana-if-type:docsOfdmaUpstream", "value": 282, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsOfdmaUpstream"}]}, {"name": "iana-if-type:gfast", "value": 283, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:gfast"}]}, {"name": "iana-if-type:sdci", "value": 284, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:sdci"}]}, {"name": "iana-if-type:xboxWireless", "value": 285, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:xboxWireless"}]}, {"name": "iana-if-type:fastdsl", "value": 286, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:fastdsl"}]}, {"name": "iana-if-type:docsCableScte55d1FwdOob", "value": 287, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableScte55d1FwdOob"}]}, {"name": "iana-if-type:docsCableScte55d1RetOob", "value": 288, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableScte55d1RetOob"}]}, {"name": "iana-if-type:docsCableScte55d2DsOob", "value": 289, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableScte55d2DsOob"}]}, {"name": "iana-if-type:docsCableScte55d2UsOob", "value": 290, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableScte55d2UsOob"}]}, {"name": "iana-if-type:docsCableNdf", "value": 291, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableNdf"}]}, {"name": "iana-if-type:docsCableNdr", "value": 292, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:docsCableNdr"}]}, {"name": "iana-if-type:ptm", "value": 293, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/iana-if-type:iana-interface-type/iana-if-type:ptm"}]}, {"name": "bbf-if-type:bbf-interface-type", "value": 294, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type"}]}, {"name": "bbf-if-type:sub-interface", "value": 295, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-if-type:sub-interface"}]}, {"name": "bbf-if-type:vlan-sub-interface", "value": 296, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-if-type:sub-interface/bbf-if-type:vlan-sub-interface"}]}, {"name": "bbf-if-type-enhance:vlan-connect-sub-interface", "value": 297, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-if-type:sub-interface/bbf-if-type:vlan-sub-interface/bbf-if-type-enhance:vlan-connect-sub-interface"}]}, {"name": "bbf-if-type:l2-termination", "value": 298, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-if-type:l2-termination"}]}, {"name": "bbf-if-type:ethernet-like", "value": 299, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-if-type:ethernet-like"}]}, {"name": "bbf-xpon-if-type:onu-v-enet", "value": 300, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-if-type:ethernet-like/bbf-xpon-if-type:onu-v-enet"}]}, {"name": "bbf-xpon-if-type:olt-v-enet", "value": 301, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-if-type:ethernet-like/bbf-xpon-if-type:olt-v-enet"}]}, {"name": "bbf-xpon-if-type:onu-v-vrefpoint", "value": 302, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-if-type:ethernet-like/bbf-xpon-if-type:onu-v-vrefpoint"}]}, {"name": "bbf-xpon-if-type:bbf-xpon-interface-type", "value": 303, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-xpon-if-type:bbf-xpon-interface-type"}]}, {"name": "bbf-xpon-if-type:channel-group", "value": 304, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-xpon-if-type:bbf-xpon-interface-type/bbf-xpon-if-type:channel-group"}]}, {"name": "bbf-xpon-if-type:channel-partition", "value": 305, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-xpon-if-type:bbf-xpon-interface-type/bbf-xpon-if-type:channel-partition"}]}, {"name": "bbf-xpon-if-type:channel-pair", "value": 306, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-xpon-if-type:bbf-xpon-interface-type/bbf-xpon-if-type:channel-pair"}]}, {"name": "bbf-xpon-if-type:channel-termination", "value": 307, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-xpon-if-type:bbf-xpon-interface-type/bbf-xpon-if-type:channel-termination"}]}, {"name": "bbf-xpon-if-type:ani", "value": 308, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-xpon-if-type:bbf-xpon-interface-type/bbf-xpon-if-type:ani"}]}, {"name": "bbf-xpon-if-type:v-ani", "value": 309, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/bbf-if-type:bbf-interface-type/bbf-xpon-if-type:bbf-xpon-interface-type/bbf-xpon-if-type:v-ani"}]}, {"name": "huawei-tc-an:auto", "value": 310, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/huawei-tc-an:auto"}]}, {"name": "huawei-tc-an:vport", "value": 311, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/huawei-tc-an:vport"}]}, {"name": "huawei-tc-an:lport", "value": 312, "derived-paths": [{"derived-path": "ietf-interfaces:interface-type/huawei-tc-an:lport"}]}], "nullable": true}, {"name": "enabled", "type": "boolean", "nullable": true, "default": true}, {"name": "ingress-qos-policy-profile", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/bbf-qos-policies:qos-policy-profiles/bbf-qos-pol:policy-profile.1/name"}, {"type": "when", "formula": "derived-from-or-self(../type, 'iana-if-type:ethernetCsmacd') or derived-from-or-self(../type, 'iana-if-type:ieee8023adLag') or derived-from-or-self(../type, 'iana-if-type:ptm') or derived-from-or-self(../type, 'bbf-if-type:vlan-sub-interface') or derived-from-or-self(../type, 'bbf-if-type:l2-termination') or derived-from(../type, 'bbf-if-type:ethernet-like')"}]}, {"name": "egress-qos-policy-profile", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/bbf-qos-policies:qos-policy-profiles/bbf-qos-pol:policy-profile.1/name"}, {"type": "when", "formula": "derived-from-or-self(../type, 'iana-if-type:ethernetCsmacd') or derived-from-or-self(../type, 'iana-if-type:ieee8023adLag') or derived-from-or-self(../type, 'iana-if-type:ptm') or derived-from-or-self(../type, 'bbf-if-type:vlan-sub-interface') or derived-from-or-self(../type, 'bbf-if-type:l2-termination') or derived-from(../type, 'bbf-if-type:ethernet-like')"}]}, {"name": "default-vlan", "type": "uint16", "nullable": true, "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'iana-if-type:ethernetCsmacd')"}], "range": "1..4094"}, {"name": "ani", "type": "container", "fields": [{"name": "upstream-fec", "type": "boolean", "nullable": true, "default": false}, {"name": "management-gemport-aes-indicator", "type": "boolean", "nullable": true, "default": true}, {"name": "isolate-switch", "type": "enum", "enumerate": [{"name": "isolate", "value": 1}, {"name": "unisolate", "value": 2}], "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}]}, {"name": "tr069-mngt-enable", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}], "default": false}, {"name": "tr069-ip-index", "type": "uint8", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}], "range": "0..7", "default": 0}, {"name": "transparent-enable", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}], "default": false}, {"name": "multicast-mode", "type": "enum", "enumerate": [{"name": "igmp-snooping", "value": 1}, {"name": "olt-control", "value": 2}, {"name": "unconcern", "value": 3}], "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}], "default": 3}, {"name": "mac-learning-enable", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}], "default": true}, {"name": "power-management", "type": "container", "fields": [{"name": "power-management-profile-ref", "type": "string", "nullable": true}]}, {"name": "multicast-forward", "type": "choice", "fields": [{"name": "untag", "type": "case", "fields": [{"name": "untag", "type": "uint8", "nullable": true}]}, {"name": "tag", "type": "case", "fields": [{"name": "tag", "type": "choice", "fields": [{"name": "translation", "type": "case", "fields": [{"name": "translation", "type": "choice", "fields": [{"name": "vlan-id", "type": "case", "fields": [{"name": "vlan-id", "type": "uint16", "nullable": true}]}, {"name": "igmp-user-vlan", "type": "case", "fields": [{"name": "igmp-user-vlan", "type": "uint8", "nullable": true}]}]}]}, {"name": "transparent", "type": "case", "fields": [{"name": "transparent", "type": "uint8", "nullable": true}]}]}]}, {"name": "unconcern", "type": "case", "default": true, "fields": [{"name": "unconcern", "type": "uint8", "nullable": true}]}], "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}]}, {"name": "snmp-route", "type": "container", "fields": [], "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}]}, {"name": "ont-port-bundle", "type": "container", "fields": [], "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:ani'"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-xpon-if-type:ani')"}]}, {"name": "onu-v-enet", "type": "container", "fields": [{"name": "ani", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/name"}, {"type": "must", "formula": "derived-from-or-self(/root/ietf-interfaces:interfaces/if:interface.1[name = current()]/type,'bbf-xpon-if-type:ani')"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-xpon-if-type:onu-v-enet')"}]}, {"name": "onu-v-vrefpoint", "type": "container", "fields": [], "clause": [{"type": "when", "formula": "derived-from-or-self(../type,'bbf-xpon-if-type:onu-v-vrefpoint')"}]}, {"name": "channel-group", "type": "container", "fields": [{"name": "polling-period", "type": "uint32", "nullable": true, "range": "1..864000", "default": 100}], "clause": [{"type": "when", "formula": "../type = 'bbf-xpon-if-type:channel-group'"}]}, {"name": "channel-partition", "type": "container", "fields": [{"name": "channel-group-ref", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/name"}, {"type": "must", "formula": "derived-from-or-self(/root/ietf-interfaces:interfaces/if:interface.1[name=current()]/type,'bbf-xpon-if-type:channel-group')"}]}, {"name": "channel-partition-index", "type": "uint8", "nullable": true, "range": "0..15"}, {"name": "downstream-fec", "type": "boolean", "nullable": true, "default": true}, {"name": "closest-onu-distance", "type": "uint16", "nullable": true, "range": "0..40", "default": 0}, {"name": "maximum-differential-xpon-distance", "type": "uint16", "nullable": true, "range": "20|40", "default": 20}, {"name": "multicast-aes-indicator", "type": "boolean", "nullable": true, "default": false}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-xpon-if-type:channel-partition')"}]}, {"name": "channel-pair", "type": "container", "fields": [{"name": "channel-group-ref", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/name"}, {"type": "must", "formula": "derived-from-or-self(/root/ietf-interfaces:interfaces/if:interface.1[name=current()]/type,'bbf-xpon-if-type:channel-group')"}]}, {"name": "channel-partition-ref", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/name"}, {"type": "must", "formula": "derived-from-or-self(/root/ietf-interfaces:interfaces/if:interface.1[name=current()]/type, 'bbf-xpon-if-type:channel-partition') and /root/ietf-interfaces:interfaces/if:interface.1[name=current()]/channel-partition/channel-group-ref=../channel-group-ref"}]}, {"name": "channel-pair-type", "type": "identity", "enumerate": [{"name": "bbf-xpon-types:ngpon2-twdm", "value": 0, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:ngpon2-twdm"}]}, {"name": "bbf-xpon-types:ngpon2-ptp", "value": 1, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:ngpon2-ptp"}]}, {"name": "bbf-xpon-types:xgs", "value": 2, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:xgs"}]}, {"name": "bbf-xpon-types:xgpon", "value": 3, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:xgpon"}]}, {"name": "bbf-xpon-types:gpon", "value": 4, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:gpon"}]}, {"name": "huawei-xpon-types:hspon", "value": 5, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/huawei-xpon-types:hspon"}]}, {"name": "huawei-xpon-types:xgt", "value": 6, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/huawei-xpon-types:xgt"}]}], "nullable": false}, {"name": "tcont-groups", "type": "container", "fields": [], "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:channel-pair'"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-xpon-if-type:channel-pair')"}]}, {"name": "channel-termination", "type": "container", "fields": [{"name": "channel-pair-ref", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/name"}, {"type": "must", "formula": "/root/ietf-interfaces:interfaces/if:interface.1[name=current()]/enabled = ../../enabled"}]}, {"name": "channel-termination-type", "type": "identity", "enumerate": [{"name": "bbf-xpon-types:ngpon2-twdm", "value": 0, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:ngpon2-twdm"}]}, {"name": "bbf-xpon-types:ngpon2-ptp", "value": 1, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:ngpon2-ptp"}]}, {"name": "bbf-xpon-types:xgs", "value": 2, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:xgs"}]}, {"name": "bbf-xpon-types:xgpon", "value": 3, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:xgpon"}]}, {"name": "bbf-xpon-types:gpon", "value": 4, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/bbf-xpon-types:gpon"}]}, {"name": "huawei-xpon-types:hspon", "value": 5, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/huawei-xpon-types:hspon"}]}, {"name": "huawei-xpon-types:xgt", "value": 6, "derived-paths": [{"derived-path": "bbf-xpon-types:channel-pair-type-base/huawei-xpon-types:xgt"}]}], "nullable": false}, {"name": "laser-switch", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:channel-termination'"}], "default": true}, {"name": "multicast-encrypt-mode", "type": "enum", "enumerate": [{"name": "disable", "value": 1}, {"name": "enable", "value": 2}, {"name": "adaptive", "value": 3}], "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:channel-termination'"}], "default": 1}, {"name": "multicast-encrypt-algorithm", "type": "enum", "enumerate": [{"name": "aes128", "value": 1}, {"name": "aes256", "value": 2}, {"name": "sm4128", "value": 3}], "nullable": true, "clause": [{"type": "when", "formula": "../multicast-encrypt-mode = 'enable'"}], "default": 1}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-xpon-if-type:channel-termination')"}]}, {"name": "tm-root", "type": "container", "fields": [{"name": "tc-id-2-queue-id-mapping-profile-name", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/bbf-qos-traffic-mngt:tm-profiles/bbf-qos-tm:tc-id-2-queue-id-mapping-profile.1/name"}]}, {"name": "shaper-name", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/bbf-qos-traffic-mngt:tm-profiles/bbf-qos-shap:shaper-profile.1/name"}]}, {"name": "children-type", "type": "choice", "fields": [{"name": "queues", "type": "case", "fields": []}, {"name": "by-queue-policy-profile", "type": "case", "fields": [{"name": "queue-policy-profile", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/bbf-qos-traffic-mngt-profiles:queue-policy-profiles/bbf-qos-tm-prof:queue-policy-profile.1/name"}]}], "clause": [{"type": "when", "formula": "(derived-from-or-self(../../../type, 'iana-if-type:ethernetCsmacd') or derived-from-or-self(../../../type, 'bbf-xpon-if-type:v-ani') or derived-from-or-self(../../../type, 'iana-if-type:aluEpon') or derived-from(../../../type, 'iana-if-type:gpon')) and not(../../tc-id-2-queue-id-mapping-profile-name)"}]}]}, {"name": "product-data", "type": "bytes", "nullable": true}]}, {"name": "v-ani", "type": "container", "fields": [{"name": "channel-partition", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/name"}, {"type": "must", "formula": "derived-from-or-self(/root/ietf-interfaces:interfaces/if:interface.1[name=current()]/type,'bbf-xpon-if-type:channel-partition')"}]}, {"name": "expected-serial-number", "type": "string", "nullable": true}, {"name": "expected-registration-id", "type": "string", "nullable": true, "length": "0..72"}, {"name": "preferred-channel-pair", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/name"}, {"type": "must", "formula": "derived-from-or-self(/root/ietf-interfaces:interfaces/if:interface.1[name=current()]/type, 'bbf-xpon-if-type:channel-pair') and /root/ietf-interfaces:interfaces/if:interface.1[name=current()]/channel-pair/channel-partition-ref=../channel-partition and(not(../protection-channel-pair) or(current() != ../protection-channel-pair))"}]}, {"name": "protection-channel-pair", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/name"}, {"type": "must", "formula": "derived-from-or-self(/root/ietf-interfaces:interfaces/if:interface.1[name=current()]/type, 'bbf-xpon-if-type:channel-pair') and /root/ietf-interfaces:interfaces/if:interface.1[name=current()]/channel-pair/channel-partition-ref=../channel-partition"}]}, {"name": "upstream-channel-speed", "type": "uint64", "nullable": true}, {"name": "ont-mngt-mode", "type": "enum", "enumerate": [{"name": "omci", "value": 1}, {"name": "stand-alone", "value": 2}], "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "default": 1}, {"name": "qos-mode", "type": "enum", "enumerate": [{"name": "priority-queue", "value": 1}, {"name": "gem-car", "value": 2}, {"name": "flow-car", "value": 3}], "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "default": 1}, {"name": "loid", "type": "string", "nullable": true, "length": "1..24"}, {"name": "check-code", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "../loid"}], "length": "1..12"}, {"name": "mapping-mode", "type": "enum", "enumerate": [{"name": "vlan", "value": 1}, {"name": "priority", "value": 2}, {"name": "vlan-priority", "value": 3}, {"name": "port", "value": 4}, {"name": "port-vlan", "value": 5}, {"name": "port-priority", "value": 6}, {"name": "port-vlan-priority", "value": 7}], "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "default": 3}, {"name": "gem-encrypt-mode", "type": "enum", "enumerate": [{"name": "aes128", "value": 1}, {"name": "aes256", "value": 2}, {"name": "sm4128", "value": 3}], "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "default": 1}, {"name": "tcont-group-index-ref", "type": "uint8", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "range": "0..7"}, {"name": "access-scenario", "type": "enum", "enumerate": [{"name": "auto-negotiation", "value": 0}, {"name": "p2mp", "value": 1}, {"name": "common", "value": 2}], "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}]}, {"name": "optic-alm-threshold-prof", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "length": "0..32"}, {"name": "ont-alarm-policy-prof", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "length": "0..32"}, {"name": "ont-dot1x-prof", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "length": "0..32"}, {"name": "onu-tr069-profile-ref", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}]}, {"name": "onu-power-shedding-profile-ref", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-xpon-if-type:v-ani')"}]}, {"name": "olt-v-enet", "type": "container", "fields": [{"name": "lower-layer-interface", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/name"}, {"type": "must", "formula": "derived-from-or-self(/root/ietf-interfaces:interfaces/if:interface.1[name = current()]/type,'bbf-xpon-if-type:v-ani')"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-xpon-if-type:olt-v-enet')"}]}, {"name": "ethernet", "type": "container", "fields": [{"name": "duplex", "type": "enum", "enumerate": [{"name": "full", "value": 0}, {"name": "half", "value": 1}, {"name": "unknown", "value": 2}], "nullable": true, "default": 0}, {"name": "speed", "type": "double", "nullable": true}, {"name": "auto-negotiation", "type": "container", "fields": [{"name": "enable", "type": "boolean", "nullable": true, "default": true}]}, {"name": "flow-control", "type": "container", "fields": [{"name": "force-flow-control", "type": "boolean", "nullable": true, "default": false}]}, {"name": "physical", "type": "container", "fields": [{"name": "loopback-mode", "type": "enum", "enumerate": [{"name": "no-loopback", "value": 0}, {"name": "inner-loopback", "value": 1}, {"name": "outer-loopback", "value": 2}], "nullable": true}, {"name": "mdi", "type": "enum", "enumerate": [{"name": "auto", "value": 1}, {"name": "normal", "value": 2}, {"name": "across", "value": 3}], "nullable": true}, {"name": "port-type", "type": "enum", "enumerate": [{"name": "FE", "value": 1}, {"name": "GE", "value": 2}, {"name": "10GE", "value": 3}, {"name": "100GE", "value": 4}, {"name": "40GE", "value": 5}, {"name": "25GE", "value": 6}], "nullable": true}], "clause": [{"type": "when", "formula": "../../type = 'iana-if-type:ethernetCsmacd'"}]}, {"name": "logical", "type": "container", "fields": [{"name": "tx-auto-off", "type": "container", "fields": [{"name": "enabled", "type": "boolean", "nullable": true, "default": false}, {"name": "detect-time", "type": "int32", "nullable": true, "clause": [{"type": "when", "formula": "../enabled = true()"}], "range": "1..60", "default": 10}, {"name": "resume-detect-mode", "type": "enum", "enumerate": [{"name": "manual", "value": 0}, {"name": "auto", "value": 1}], "nullable": true, "clause": [{"type": "when", "formula": "../enabled = true()"}], "default": 0}, {"name": "resume-detect-interval", "type": "int32", "nullable": true, "clause": [{"type": "when", "formula": "../enabled = true() and ../resume-detect-mode = 'auto'"}], "range": "100..300", "default": 100}, {"name": "resume-detect-duration", "type": "int32", "nullable": true, "clause": [{"type": "when", "formula": "../enabled = true()"}], "range": "100..3000", "default": 2000}]}], "clause": [{"type": "when", "formula": "../../type = 'iana-if-type:ethernetCsmacd'"}]}, {"name": "ethernet-frame", "type": "container", "fields": [{"name": "jumbo-frame", "type": "boolean", "nullable": true, "default": false}, {"name": "mtu", "type": "uint16", "nullable": true, "range": "1519..9280", "default": 2052}]}, {"name": "optical-module", "type": "container", "fields": [{"name": "optical-alarm-profile", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/bbf-xpon:xpon/optic-alm-threshold-profiles/alm-threshold-profile:optic-alm-threshold-profile.1/name"}]}], "clause": [{"type": "when", "formula": "../../type = 'iana-if-type:ethernetCsmacd'"}]}, {"name": "fec-mode", "type": "container", "fields": [{"name": "config-fec-mode", "type": "enum", "enumerate": [{"name": "unconcern", "value": 0}, {"name": "off", "value": 1}, {"name": "base-r", "value": 2}, {"name": "rs", "value": 3}], "nullable": true}], "clause": [{"type": "when", "formula": "../../type = 'iana-if-type:ethernetCsmacd'"}]}, {"name": "stream-car-traffic-profile", "type": "container", "fields": [{"name": "upstream", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/bbf-qos-policies:qos-policy-profiles/bbf-qos-pol:policy-profile.1/name"}]}, {"name": "downstream", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/bbf-qos-policies:qos-policy-profiles/bbf-qos-pol:policy-profile.1/name"}]}], "clause": [{"type": "when", "formula": "../../type = 'iana-if-type:ethernetCsmacd'"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'iana-if-type:ethernetCsmacd')"}]}, {"name": "tx-auto-off-config", "type": "container", "fields": [], "clause": [{"type": "when", "formula": "../type = 'iana-if-type:ethernetCsmacd'"}]}, {"name": "ipv4", "type": "container", "fields": []}, {"name": "subif-lower-layer", "type": "container", "fields": [{"name": "interface", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/name"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-if-type:sub-interface')"}]}, {"name": "frame-processing", "type": "choice", "fields": [{"name": "inline-frame-processing", "type": "case", "fields": [{"name": "inline-frame-processing", "type": "container", "fields": [{"name": "uplink-interface", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/name"}, {"type": "when", "formula": "derived-from-or-self(../../../../type, 'bbf-if-type-enhance:vlan-connect-sub-interface')"}, {"type": "must", "formula": "/root/ietf-interfaces:interfaces/if:interface.1[name = current()]/type='iana-if-type:ethernetCsmacd'"}]}, {"name": "ingress-rule", "type": "container", "fields": []}]}]}, {"name": "frame-processing-profile", "type": "case", "fields": [{"name": "frame-processing-profile", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/bbf-frame-processing-profiles:frame-processing-profiles/bbf-fpprof:frame-processing-profile.1/name"}]}, {"name": "tag-0", "type": "container", "fields": [{"name": "vlan-id", "type": "uint16", "nullable": false, "clause": [{"type": "when", "formula": "/root/bbf-frame-processing-profiles:frame-processing-profiles/bbf-fpprof:frame-processing-profile.1[name = current()/../../frame-processing-profile]/match-criteria/vlans/vlan-tag-match-type/vlan-tagged/bbf-fpprof:tag.1[index = 0]/vlan-id = 'vlan-id-is-a-parameter' "}], "range": "0..4094"}]}, {"name": "tag-1", "type": "container", "fields": [{"name": "vlan-id", "type": "uint16", "nullable": false, "clause": [{"type": "when", "formula": "/root/bbf-frame-processing-profiles:frame-processing-profiles/bbf-fpprof:frame-processing-profile.1[name = current()/../../frame-processing-profile]/match-criteria/vlans/vlan-tag-match-type/vlan-tagged/bbf-fpprof:tag.1[index = 1]/vlan-id = 'vlan-id-is-a-parameter' "}], "range": "0..4094"}]}, {"name": "ingress-rewrite-tag-0", "type": "container", "fields": [{"name": "vlan-id", "type": "uint16", "nullable": false, "clause": [{"type": "when", "formula": "/root/bbf-frame-processing-profiles:frame-processing-profiles/bbf-fpprof:frame-processing-profile.1[name = current()/../../frame-processing-profile]/ingress-rewrite/bbf-fpprof:push-tag.1[index = 0]/vlan-id = 'vlan-id-is-a-parameter' "}], "range": "0..4094"}]}, {"name": "ingress-rewrite-tag-1", "type": "container", "fields": [{"name": "vlan-id", "type": "uint16", "nullable": false, "clause": [{"type": "when", "formula": "/root/bbf-frame-processing-profiles:frame-processing-profiles/bbf-fpprof:frame-processing-profile.1[name = current()/../../frame-processing-profile]/ingress-rewrite/bbf-fpprof:push-tag.1[index = 1]/vlan-id = 'vlan-id-is-a-parameter' "}], "range": "0..4094"}]}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-if-type:sub-interface')"}]}, {"name": "interface-usage", "type": "container", "fields": [{"name": "interface-usage", "type": "enum", "enumerate": [{"name": "user-port", "value": 0}, {"name": "network-port", "value": 1}, {"name": "subtended-node-port", "value": 2}, {"name": "inherit", "value": 3}, {"name": "dpu-port", "value": 4}], "nullable": true}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'iana-if-type:ethernetCsmacd') or derived-from-or-self(../type, 'iana-if-type:ieee8023adLag') or derived-from-or-self(../type, 'iana-if-type:ptm') or derived-from-or-self(../type, 'bbf-if-type:vlan-sub-interface') or derived-from-or-self(../type, 'bbf-if-type:l2-termination') or derived-from(../type, 'bbf-if-type:ethernet-like')"}]}, {"name": "mac-learning", "type": "container", "fields": [{"name": "max-number-mac-addresses", "type": "uint32", "nullable": true, "default": **********}, {"name": "number-committed-mac-addresses", "type": "uint32", "nullable": true, "default": 1}, {"name": "mac-learning-enable", "type": "boolean", "nullable": true, "default": true}, {"name": "mac-learning-failure-action", "type": "enum", "enumerate": [{"name": "forward", "value": 0}, {"name": "discard", "value": 1}], "nullable": true, "default": 0}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'iana-if-type:ethernetCsmacd') or derived-from-or-self(../type, 'iana-if-type:ieee8023adLag') or derived-from-or-self(../type, 'iana-if-type:ptm') or derived-from-or-self(../type, 'bbf-if-type:vlan-sub-interface') or derived-from-or-self(../type, 'bbf-if-type:l2-termination') or derived-from(../type, 'bbf-if-type:ethernet-like')"}]}, {"name": "l2-dhcpv4-relay", "type": "container", "fields": [{"name": "enable", "type": "boolean", "nullable": true, "default": true}, {"name": "trusted-port", "type": "string", "nullable": true, "default": "false"}, {"name": "profile-ref", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/bbf-l2-dhcpv4-relay:l2-dhcpv4-relay-profiles/bbf-l2-d4r:l2-dhcpv4-relay-profile.1/name"}, {"type": "when", "formula": "../enable = true()"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type,'bbf-if-type:vlan-sub-interface')"}]}, {"name": "dhcpv6-ldra", "type": "container", "fields": [{"name": "enable", "type": "boolean", "nullable": true, "default": true}, {"name": "trusted-port", "type": "boolean", "nullable": true, "default": true}, {"name": "profile-ref", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/bbf-ldra:dhcpv6-ldra-profiles/bbf-ldra:dhcpv6-ldra-profile.1/name"}, {"type": "when", "formula": "../enable = true()"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type,'bbf-if-type:vlan-sub-interface')"}]}, {"name": "pppoe", "type": "container", "fields": [{"name": "enable", "type": "boolean", "nullable": true, "default": false}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type,'bbf-if-type:vlan-sub-interface')"}]}, {"name": "qos-policies", "type": "container", "fields": [{"name": "policing", "type": "container", "fields": [{"name": "statistics", "type": "container", "fields": [{"name": "enabled", "type": "boolean", "nullable": true, "default": false}]}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'iana-if-type:ethernetCsmacd') or derived-from-or-self(../type, 'iana-if-type:ieee8023adLag') or derived-from-or-self(../type, 'iana-if-type:ptm') or derived-from-or-self(../type, 'bbf-if-type:vlan-sub-interface') or derived-from-or-self(../type, 'bbf-if-type:l2-termination') or derived-from(../type, 'bbf-if-type:ethernet-like')"}]}, {"name": "subscriber-profile", "type": "container", "fields": [{"name": "profile", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/bbf-subscriber-profiles:subscriber-profiles/bbf-subprof:subscriber-profile.1/name"}]}]}, {"name": "vsi-profile", "type": "container", "fields": [{"name": "vsi-profile", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/bbf-vlan-sub-interface-profiles:vsi-profiles/bbf-vsi-prof:vsi-profile.1/name"}]}, {"name": "tag-0", "type": "container", "fields": [{"name": "vlan-id", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "/root/bbf-vlan-sub-interface-profiles:vsi-profiles/bbf-vsi-prof:vsi-profile.1[name = current()/../../vsi-profile]/frame-processing/inline-multiple-rules-with-parameters/frame-processing-rules/ingress-rule/bbf-vsi-prof-fp:rule.1/match-criteria/vlans/vlan-tag-match-type/vlan-tagged/bbf-vsi-prof-fp:tag.1[index = 0]/vlan-id = 'vlan-id-is-a-parameter' "}]}]}, {"name": "tag-1", "type": "container", "fields": [{"name": "vlan-id", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "/root/bbf-vlan-sub-interface-profiles:vsi-profiles/bbf-vsi-prof:vsi-profile.1[name = current()/../../vsi-profile]/frame-processing/inline-multiple-rules-with-parameters/frame-processing-rules/ingress-rule/bbf-vsi-prof-fp:rule.1/match-criteria/vlans/vlan-tag-match-type/vlan-tagged/bbf-vsi-prof-fp:tag.1[index = 1]/vlan-id = 'vlan-id-is-a-parameter' "}]}]}, {"name": "ingress-rewrite-tag-0", "type": "container", "fields": [{"name": "vlan-id", "type": "uint16", "nullable": true, "clause": [{"type": "when", "formula": "/root/bbf-vlan-sub-interface-profiles:vsi-profiles/bbf-vsi-prof:vsi-profile.1[name = current()/../../vsi-profile]/frame-processing/inline-multiple-rules-with-parameters/frame-processing-rules/ingress-rule/bbf-vsi-prof-fp:rule.1/ingress-rewrite/bbf-vsi-prof-fp:push-tag.1[index = 0]/vlan-id = 'vlan-id-is-a-parameter' "}], "range": "0..4094"}]}, {"name": "ingress-rewrite-tag-1", "type": "container", "fields": [{"name": "vlan-id", "type": "uint16", "nullable": true, "clause": [{"type": "when", "formula": "/root/bbf-vlan-sub-interface-profiles:vsi-profiles/bbf-vsi-prof:vsi-profile.1[name = current()/../../vsi-profile]/frame-processing/inline-multiple-rules-with-parameters/frame-processing-rules/ingress-rule/bbf-vsi-prof-fp:rule.1/ingress-rewrite/bbf-vsi-prof-fp:push-tag.1[index = 1]/vlan-id = 'vlan-id-is-a-parameter' "}], "range": "0..4094"}]}, {"name": "egress-rewrite-tag-0", "type": "container", "fields": [{"name": "vlan-id", "type": "string", "nullable": true, "default": "vlan-id-from-match"}]}, {"name": "egress-rewrite-tag-1", "type": "container", "fields": [{"name": "vlan-id", "type": "string", "nullable": true, "default": "vlan-id-from-match"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'bbf-if-type:vlan-sub-interface')"}]}, {"name": "bridge-port", "type": "container", "fields": [{"name": "component-name", "type": "string", "nullable": true}, {"name": "port-type", "type": "identity", "enumerate": [{"name": "ieee802-dot1q-bridge:c-vlan-bridge-port", "value": 0, "derived-paths": [{"derived-path": "ieee802-dot1q-bridge:type-of-port/ieee802-dot1q-bridge:c-vlan-bridge-port"}]}, {"name": "ieee802-dot1q-bridge:provider-network-port", "value": 1, "derived-paths": [{"derived-path": "ieee802-dot1q-bridge:type-of-port/ieee802-dot1q-bridge:provider-network-port"}]}, {"name": "ieee802-dot1q-bridge:customer-network-port", "value": 2, "derived-paths": [{"derived-path": "ieee802-dot1q-bridge:type-of-port/ieee802-dot1q-bridge:customer-network-port"}]}, {"name": "ieee802-dot1q-bridge:customer-edge-port", "value": 3, "derived-paths": [{"derived-path": "ieee802-dot1q-bridge:type-of-port/ieee802-dot1q-bridge:customer-edge-port"}]}, {"name": "ieee802-dot1q-bridge:d-bridge-port", "value": 4, "derived-paths": [{"derived-path": "ieee802-dot1q-bridge:type-of-port/ieee802-dot1q-bridge:d-bridge-port"}]}, {"name": "ieee802-dot1q-bridge:remote-customer-access-port", "value": 5, "derived-paths": [{"derived-path": "ieee802-dot1q-bridge:type-of-port/ieee802-dot1q-bridge:remote-customer-access-port"}]}], "nullable": true}, {"name": "pvid", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "../component-name != 'd-bridge-component'"}], "range": "1..4094|4096..**********", "default": 1}, {"name": "default-priority", "type": "uint8", "nullable": true, "range": "0..7", "default": 0}, {"name": "pcp-selection", "type": "enum", "enumerate": [{"name": "8P0D", "value": 0}, {"name": "7P1D", "value": 1}, {"name": "6P2D", "value": 2}, {"name": "5P3D", "value": 3}], "nullable": true, "default": 0}, {"name": "use-dei", "type": "boolean", "nullable": true, "default": false}, {"name": "drop-encoding", "type": "boolean", "nullable": true, "default": false}, {"name": "service-access-priority-selection", "type": "boolean", "nullable": true, "default": false}, {"name": "acceptable-frame", "type": "enum", "enumerate": [{"name": "admit-only-VLAN-tagged-frames", "value": 0}, {"name": "admit-only-untagged-and-priority-tagged", "value": 1}, {"name": "admit-all-frames", "value": 2}], "nullable": true, "clause": [{"type": "when", "formula": "../component-name != 'd-bridge-component'"}], "default": 2}, {"name": "enable-ingress-filtering", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../component-name != 'd-bridge-component'"}], "default": false}, {"name": "enable-restricted-vlan-registration", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../component-name != 'd-bridge-component'"}], "default": false}, {"name": "enable-vid-translation-table", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../component-name != 'd-bridge-component'"}], "default": false}, {"name": "enable-egress-vid-translation-table", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../component-name != 'd-bridge-component'"}], "default": false}, {"name": "admin-point-to-point", "type": "enum", "enumerate": [{"name": "force-true", "value": 1}, {"name": "force-false", "value": 2}, {"name": "auto", "value": 3}], "nullable": true}, {"name": "priority-regeneration", "type": "container", "fields": [{"name": "priority0", "type": "uint8", "nullable": true, "range": "0..7", "default": 0}, {"name": "priority1", "type": "uint8", "nullable": true, "range": "0..7", "default": 1}, {"name": "priority2", "type": "uint8", "nullable": true, "range": "0..7", "default": 2}, {"name": "priority3", "type": "uint8", "nullable": true, "range": "0..7", "default": 3}, {"name": "priority4", "type": "uint8", "nullable": true, "range": "0..7", "default": 4}, {"name": "priority5", "type": "uint8", "nullable": true, "range": "0..7", "default": 5}, {"name": "priority6", "type": "uint8", "nullable": true, "range": "0..7", "default": 6}, {"name": "priority7", "type": "uint8", "nullable": true, "range": "0..7", "default": 7}]}, {"name": "pcp-decoding-table", "type": "container", "fields": []}, {"name": "pcp-encoding-table", "type": "container", "fields": []}, {"name": "service-access-priority", "type": "container", "fields": [{"name": "priority0", "type": "uint8", "nullable": true, "range": "0..7", "default": 0}, {"name": "priority1", "type": "uint8", "nullable": true, "range": "0..7", "default": 1}, {"name": "priority2", "type": "uint8", "nullable": true, "range": "0..7", "default": 2}, {"name": "priority3", "type": "uint8", "nullable": true, "range": "0..7", "default": 3}, {"name": "priority4", "type": "uint8", "nullable": true, "range": "0..7", "default": 4}, {"name": "priority5", "type": "uint8", "nullable": true, "range": "0..7", "default": 5}, {"name": "priority6", "type": "uint8", "nullable": true, "range": "0..7", "default": 6}, {"name": "priority7", "type": "uint8", "nullable": true, "range": "0..7", "default": 7}]}, {"name": "traffic-class", "type": "container", "fields": []}], "clause": [{"type": "when", "formula": "../type = 'iana-if-type:bridge' or ../type = 'iana-if-type:ethernetCsmacd' or ../type = 'iana-if-type:ieee8023adLag'or ../type = 'iana-if-type:ilan'"}]}, {"name": "aggregator", "type": "container", "fields": [{"name": "agg-system-name", "type": "string", "nullable": true}, {"name": "work-mode", "type": "enum", "enumerate": [{"name": "static", "value": 0}, {"name": "lacp", "value": 1}], "nullable": true, "default": 0}, {"name": "fast-period", "type": "uint32", "nullable": true, "range": "1..10", "default": 1}, {"name": "slow-period", "type": "uint32", "nullable": true, "range": "20..40", "default": 30}, {"name": "max-link-number", "type": "string", "nullable": true, "default": "no-limit"}, {"name": "least-link-number", "type": "string", "nullable": true, "default": "no-limit"}, {"name": "forward-mode", "type": "enum", "enumerate": [{"name": "ingress", "value": 0}, {"name": "egress-ingress", "value": 1}, {"name": "mpls-label", "value": 3}, {"name": "src-dst-ip", "value": 4}, {"name": "ip-enhance", "value": 5}], "nullable": true, "default": 0}, {"name": "preempt-enabled", "type": "boolean", "nullable": true, "default": false}, {"name": "preempt-delay", "type": "uint16", "nullable": true, "range": "0..180", "default": 0}, {"name": "aggregator-lacp", "type": "container", "fields": [{"name": "actor-admin-key", "type": "uint16", "nullable": true, "range": "1..65535"}]}, {"name": "ports", "type": "container", "fields": []}], "clause": [{"type": "when", "formula": "../type = 'iana-if-type:ieee8023adLag' or ../type = 'iana-if-type:ethernetCsmacd' or ../type = 'iana-if-type:bridge'"}]}, {"name": "aggregation-port", "type": "container", "fields": [{"name": "aggregation-port-lacp", "type": "container", "fields": [{"name": "actor-port-priority", "type": "uint16", "nullable": true, "default": 16384}, {"name": "actor-admin-state", "type": "string", "nullable": true}]}], "clause": [{"type": "when", "formula": "../type = 'iana-if-type:ethernetCsmacd' or ../type = 'iana-if-type:bridge'"}]}, {"name": "tpid", "type": "container", "fields": [{"name": "s-vlan-tpid", "type": "enum", "enumerate": [{"name": "dot1q", "value": 1}, {"name": "dot1ad", "value": 2}], "nullable": true}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'iana-if-type:ethernetCsmacd')"}]}, {"name": "xpon-port", "type": "container", "fields": [{"name": "ont-auto-find-switch", "type": "boolean", "nullable": true, "default": false}, {"name": "optic-alarm-profile", "type": "string", "nullable": true, "length": "0..32"}, {"name": "ont-password-renew-interval", "type": "string", "nullable": true, "default": "1440"}, {"name": "multi-channel-low-latency-switch", "type": "boolean", "nullable": true}, {"name": "asymmetric-onu-up-rate", "type": "enum", "enumerate": [{"name": "12dot5g-rate", "value": 1}, {"name": "25g-rate", "value": 2}], "nullable": true}, {"name": "dba-bandwidth-assignment-mode", "type": "enum", "enumerate": [{"name": "max-bandwidth-usage", "value": 1}, {"name": "min-loop-delay", "value": 2}, {"name": "default", "value": 3}, {"name": "assigned-period", "value": 4}], "nullable": true}, {"name": "multicast-encrypt-mode", "type": "enum", "enumerate": [{"name": "disable", "value": 1}, {"name": "enable", "value": 2}, {"name": "adaptive", "value": 3}], "nullable": true, "default": 1}, {"name": "dba-surplus-assign", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "default": 2}, {"name": "max-guaranteed-bandwidth", "type": "string", "nullable": true, "default": "unlimited"}, {"name": "dba-low-latency", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "default": 2}, {"name": "ont-online-power-threshold", "type": "string", "nullable": true, "default": "unlimited"}, {"name": "multicast-encrypt-algorithm", "type": "enum", "enumerate": [{"name": "aes128", "value": 1}, {"name": "aes256", "value": 2}, {"name": "sm4128", "value": 3}], "nullable": true, "clause": [{"type": "when", "formula": "../multicast-encrypt-mode = 'enable'"}], "default": 1}, {"name": "traffic-alarm-profile-ref", "type": "string", "nullable": true}], "clause": [{"type": "when", "formula": "../type = 'iana-if-type:gpon'"}]}, {"name": "product-data", "type": "bytes", "nullable": true}], "keys": [{"name": "if:interface.1.PK", "index": {"type": "primary"}, "node": "if:interface.1", "fields": ["PID", "name"], "constraints": {"unique": true}}, {"name": "if:interface.1.SK", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "if:interface.1", "fields": ["product-data"]}]}, {"name": "bbf-if-port-ref:port-layer-if.1", "type": "leaf-list", "config": {"check_validity": true}, "max-elements": 1, "min-elements": 1, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "port-layer-if", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/root/ietf-hardware:hardware/hw:component.1/name"}]}], "keys": [{"name": "bbf-if-port-ref:port-layer-if.1.PK", "index": {"type": "primary"}, "node": "bbf-if-port-ref:port-layer-if.1", "fields": ["PID", "port-layer-if"], "constraints": {"unique": true}}]}, {"name": "bbf-qos-tm:queue.2", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "local-queue-id", "type": "uint32", "nullable": false, "range": "0..7"}, {"name": "bac-name", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/bbf-qos-traffic-mngt:tm-profiles/bbf-qos-tm:bac-entry.1/name"}]}, {"name": "shaper-name", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/bbf-qos-traffic-mngt:tm-profiles/bbf-qos-shap:shaper-profile.1/name"}]}, {"name": "queue-scheduling-cfg-type", "type": "choice", "fields": [{"name": "inline", "type": "case", "fields": [{"name": "weight", "type": "uint8", "nullable": true}]}]}], "keys": [{"name": "bbf-qos-tm:queue.2.PK", "index": {"type": "primary"}, "node": "bbf-qos-tm:queue.2", "fields": ["PID", "local-queue-id"], "constraints": {"unique": true}}]}, {"name": "bbf-subif:rule.1", "type": "list", "config": {"check_validity": true}, "min-elements": 1, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "length": "1..64"}, {"name": "priority", "type": "uint16", "nullable": false, "range": "1..max"}, {"name": "ingress-rewrite", "type": "container", "fields": [{"name": "pop-tags", "type": "uint8", "nullable": true, "clause": [{"type": "when", "formula": "derived-from-or-self(../../../../../../../type,'bbf-if-type:vlan-sub-interface')"}], "range": "0..2", "default": 0}, {"name": "tpid-index", "type": "container", "fields": [{"name": "uni-tpid-index", "type": "uint8", "nullable": true, "range": "0..3|255", "default": 255}, {"name": "nni-tpid-index", "type": "uint8", "nullable": true, "range": "0..3|255", "default": 255}], "clause": [{"type": "when", "formula": "derived-from-or-self(../../../../../../../type, 'bbf-if-type:vlan-sub-interface')"}]}]}, {"name": "flexible-match", "type": "container", "fields": [{"name": "match-criteria", "type": "container", "fields": [{"name": "ethernet-frame-type", "type": "string", "nullable": true, "default": "any"}, {"name": "frame-filter", "type": "choice", "fields": [{"name": "any-frame", "type": "case", "fields": [{"name": "any-frame", "type": "uint8", "nullable": true}]}]}, {"name": "vlan-tag-match-type", "type": "choice", "fields": [{"name": "untagged", "type": "case", "fields": [{"name": "untagged", "type": "uint8", "nullable": true}, {"name": "untag-flow-car", "type": "container", "fields": [{"name": "flow-car-traffic", "type": "string", "nullable": true, "length": "1..64"}], "clause": [{"type": "when", "formula": "derived-from-or-self(../../../../../../../../../../type,'bbf-if-type:vlan-sub-interface')"}]}]}, {"name": "vlan-tagged", "type": "case", "fields": [{"name": "tag-flow-car", "type": "container", "fields": [{"name": "flow-car-traffic", "type": "string", "nullable": true, "length": "1..64"}], "clause": [{"type": "when", "formula": "derived-from-or-self(../../../../../../../../../../type,'bbf-if-type:vlan-sub-interface')"}]}, {"name": "ont-port", "type": "container", "fields": [{"name": "type", "type": "enum", "enumerate": [{"name": "eth", "value": 47}], "nullable": true}, {"name": "index", "type": "uint8", "nullable": true, "range": "1..28"}], "clause": [{"type": "when", "formula": "derived-from-or-self(../../../../../../../../../../type,'bbf-if-type:vlan-sub-interface')"}]}, {"name": "eth-bundle", "type": "container", "fields": [{"name": "bundle-index", "type": "uint8", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/ani/ont-port-bundle/bbf-xpon-ani:eth-bundle.1/bundle-index"}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../../../../../../../../../../type,'bbf-if-type:vlan-sub-interface')"}]}]}, {"name": "match-all", "type": "case", "fields": [{"name": "match-all", "type": "uint8", "nullable": true}]}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../../../../../../../type,'bbf-if-type:vlan-sub-interface')"}]}]}], "keys": [{"name": "bbf-subif:rule.1.PK", "index": {"type": "primary"}, "node": "bbf-subif:rule.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "bbf-subif-tag:tag.1", "type": "list", "config": {"check_validity": true}, "max-elements": 2, "min-elements": 1, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint8", "nullable": false, "clause": [{"type": "must", "formula": "count(../../bbf-subif-tag:tag.1[index = 0]) > 0"}], "range": "0..1"}, {"name": "dot1q-tag", "type": "container", "fields": [{"name": "tag-type", "type": "string", "nullable": false}, {"name": "vlan-id", "type": "string", "nullable": false}, {"name": "pbit", "type": "string", "nullable": true, "default": "any"}, {"name": "dei", "type": "string", "nullable": true, "default": "any"}]}], "keys": [{"name": "bbf-subif-tag:tag.1.PK", "index": {"type": "primary"}, "node": "bbf-subif-tag:tag.1", "fields": ["PID", "index"], "constraints": {"unique": true}}]}, {"name": "bbf-subif-tag:push-tag.1", "type": "list", "config": {"check_validity": true}, "max-elements": 2, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint8", "nullable": false, "clause": [{"type": "must", "formula": "(count(../../bbf-subif-tag:push-tag.1[index = 0]) > 0)"}], "range": "0..1"}, {"name": "dot1q-tag", "type": "container", "fields": [{"name": "tag-type", "type": "string", "nullable": false}, {"name": "vlan-id", "type": "string", "nullable": false}, {"name": "vlan-id-from-tag-index-or-discard", "type": "uint8", "nullable": false, "clause": [{"type": "when", "formula": "../vlan-id = 'vlan-id-from-tag-index'"}], "range": "0..1"}, {"name": "pbit", "type": "choice", "fields": [{"name": "write-pbit-0", "type": "case", "fields": [{"name": "write-pbit-0", "type": "uint8", "nullable": true}]}, {"name": "copy-pbit-from-input-or-0", "type": "case", "fields": [{"name": "pbit-from-tag-index", "type": "uint8", "nullable": true, "range": "0..1"}]}, {"name": "write-pbit-value", "type": "case", "fields": [{"name": "write-pbit", "type": "uint8", "nullable": true, "range": "0..7"}]}, {"name": "generate-pbit-from-marking-list-or-0", "type": "case", "fields": [{"name": "pbit-marking-index", "type": "uint8", "nullable": true}]}, {"name": "generate-pbit-via-profile-or-0", "type": "case", "fields": [{"name": "pbit-marking-index", "type": "uint8", "nullable": true}]}]}]}], "clause": [{"type": "when", "formula": "derived-from-or-self(../../../../../../../type,'bbf-if-type:vlan-sub-interface')"}], "keys": [{"name": "bbf-subif-tag:push-tag.1.PK", "index": {"type": "primary"}, "node": "bbf-subif-tag:push-tag.1", "fields": ["PID", "index"], "constraints": {"unique": true}}]}, {"name": "bbf-xpon-channel:tcont-group.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint8", "nullable": false, "range": "0..7"}, {"name": "traffic-descriptor-profile-ref", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/root/bbf-xpongemtcont:xpongemtcont/traffic-descriptor-profiles/bbf-xpongemtcont:traffic-descriptor-profile.1/name"}]}], "keys": [{"name": "bbf-xpon-channel:tcont-group.1.PK", "index": {"type": "primary"}, "node": "bbf-xpon-channel:tcont-group.1", "fields": ["PID", "index"], "constraints": {"unique": true}}]}, {"name": "bbf-xpon-ani:eth-bundle.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "bundle-index", "type": "uint8", "nullable": false, "range": "0..7"}, {"name": "master-port", "type": "uint8", "nullable": true, "range": "1..32"}, {"name": "port-list", "type": "container", "fields": []}], "keys": [{"name": "bbf-xpon-ani:eth-bundle.1.PK", "index": {"type": "primary"}, "node": "bbf-xpon-ani:eth-bundle.1", "fields": ["PID", "bundle-index"], "constraints": {"unique": true}}]}, {"name": "bbf-xpon-ani:port.1", "type": "leaf-list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "port", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/name"}]}], "clause": [{"type": "must", "formula": "derived-from-or-self(/root/ietf-interfaces:interfaces/if:interface.1[name = current()/port]/type,'iana-if-type:ethernetCsmacd')"}], "keys": [{"name": "bbf-xpon-ani:port.1.PK", "index": {"type": "primary"}, "node": "bbf-xpon-ani:port.1", "fields": ["PID", "port"], "constraints": {"unique": true}}]}, {"name": "bbf-xpon-ani:onu-snmp-route.1", "type": "list", "config": {"check_validity": true}, "max-elements": 8, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "url", "type": "string", "nullable": false}, {"name": "mask", "type": "string", "nullable": false}, {"name": "output-port", "type": "uint32", "nullable": false, "range": "0..254"}], "keys": [{"name": "bbf-xpon-ani:onu-snmp-route.1.PK", "index": {"type": "primary"}, "node": "bbf-xpon-ani:onu-snmp-route.1", "fields": ["PID", "url", "mask", "output-port"], "constraints": {"unique": true}}]}, {"name": "bbf-xpon-v-ani:tpid.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint8", "nullable": false, "range": "0..3"}, {"name": "val", "type": "uint16", "nullable": true, "range": "1536..65535"}], "clause": [{"type": "when", "formula": "../../type = 'bbf-xpon-if-type:v-ani'"}], "keys": [{"name": "bbf-xpon-v-ani:tpid.1.PK", "index": {"type": "primary"}, "node": "bbf-xpon-v-ani:tpid.1", "fields": ["PID", "index"], "constraints": {"unique": true}}]}, {"name": "an-dot1ax:port.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "port-if", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/name"}, {"type": "must", "formula": "/root/ietf-interfaces:interfaces/if:interface.1[name = current()]/type='iana-if-type:ethernetCsmacd'"}]}, {"name": "aggregator-role", "type": "enum", "enumerate": [{"name": "master", "value": 0}, {"name": "slave", "value": 1}], "nullable": true}], "keys": [{"name": "an-dot1ax:port.1.PK", "index": {"type": "primary"}, "node": "an-dot1ax:port.1", "fields": ["PID", "port-if"], "constraints": {"unique": true}}]}, {"name": "dot1q:egress-vid-translations.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "relay-vid", "type": "uint16", "nullable": false, "range": "1..4094"}, {"name": "local-vid", "type": "uint16", "nullable": true, "range": "1..4094"}], "clause": [{"type": "when", "formula": "../component-name != 'd-bridge-component'"}], "keys": [{"name": "dot1q:egress-vid-translations.1.PK", "index": {"type": "primary"}, "node": "dot1q:egress-vid-translations.1", "fields": ["PID", "relay-vid"], "constraints": {"unique": true}}]}, {"name": "dot1q:pcp-decoding-map.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "pcp", "type": "enum", "enumerate": [{"name": "8P0D", "value": 0}, {"name": "7P1D", "value": 1}, {"name": "6P2D", "value": 2}, {"name": "5P3D", "value": 3}], "nullable": false}], "keys": [{"name": "dot1q:pcp-decoding-map.1.PK", "index": {"type": "primary"}, "node": "dot1q:pcp-decoding-map.1", "fields": ["PID", "pcp"], "constraints": {"unique": true}}]}, {"name": "dot1q:priority-map.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "priority-code-point", "type": "uint8", "nullable": false, "range": "0..7"}, {"name": "priority", "type": "uint8", "nullable": true, "range": "0..7"}, {"name": "drop-eligible", "type": "boolean", "nullable": true}], "keys": [{"name": "dot1q:priority-map.1.PK", "index": {"type": "primary"}, "node": "dot1q:priority-map.1", "fields": ["PID", "priority-code-point"], "constraints": {"unique": true}}]}, {"name": "dot1q:pcp-encoding-map.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "pcp", "type": "enum", "enumerate": [{"name": "8P0D", "value": 0}, {"name": "7P1D", "value": 1}, {"name": "6P2D", "value": 2}, {"name": "5P3D", "value": 3}], "nullable": false}], "keys": [{"name": "dot1q:pcp-encoding-map.1.PK", "index": {"type": "primary"}, "node": "dot1q:pcp-encoding-map.1", "fields": ["PID", "pcp"], "constraints": {"unique": true}}]}, {"name": "dot1q:priority-map.2", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "priority", "type": "uint8", "nullable": false, "range": "0..7"}, {"name": "dei", "type": "boolean", "nullable": false}, {"name": "priority-code-point", "type": "uint8", "nullable": true, "range": "0..7"}], "keys": [{"name": "dot1q:priority-map.2.PK", "index": {"type": "primary"}, "node": "dot1q:priority-map.2", "fields": ["PID", "priority", "dei"], "constraints": {"unique": true}}]}, {"name": "dot1q:protocol-group-vid-set.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "group-id", "type": "uint32", "nullable": false}], "clause": [{"type": "when", "formula": "../component-name != 'd-bridge-component'"}], "keys": [{"name": "dot1q:protocol-group-vid-set.1.PK", "index": {"type": "primary"}, "node": "dot1q:protocol-group-vid-set.1", "fields": ["PID", "group-id"], "constraints": {"unique": true}}]}, {"name": "dot1q:vid.2", "type": "leaf-list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "vid", "type": "uint16", "nullable": false, "range": "1..4094"}], "keys": [{"name": "dot1q:vid.2.PK", "index": {"type": "primary"}, "node": "dot1q:vid.2", "fields": ["PID", "vid"], "constraints": {"unique": true}}]}, {"name": "dot1q:traffic-class-map.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "priority", "type": "uint8", "nullable": false, "range": "0..7"}], "keys": [{"name": "dot1q:traffic-class-map.1.PK", "index": {"type": "primary"}, "node": "dot1q:traffic-class-map.1", "fields": ["PID", "priority"], "constraints": {"unique": true}}]}, {"name": "dot1q:available-traffic-class.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "num-traffic-class", "type": "uint8", "nullable": false, "range": "1..8"}, {"name": "traffic-class", "type": "uint8", "nullable": true, "range": "0..7"}], "keys": [{"name": "dot1q:available-traffic-class.1.PK", "index": {"type": "primary"}, "node": "dot1q:available-traffic-class.1", "fields": ["PID", "num-traffic-class"], "constraints": {"unique": true}}]}, {"name": "dot1q:vid-translations.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "local-vid", "type": "uint16", "nullable": false, "range": "1..4094"}, {"name": "relay-vid", "type": "uint16", "nullable": true, "range": "1..4094"}], "clause": [{"type": "when", "formula": "../component-name != 'd-bridge-component'"}], "keys": [{"name": "dot1q:vid-translations.1.PK", "index": {"type": "primary"}, "node": "dot1q:vid-translations.1", "fields": ["PID", "local-vid"], "constraints": {"unique": true}}]}, {"name": "ip:address.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "ip", "type": "string", "nullable": false}, {"name": "subnet", "type": "choice", "fields": [{"name": "netmask", "type": "case", "fields": [{"name": "netmask", "type": "string", "nullable": true}]}]}], "keys": [{"name": "ip:address.1.PK", "index": {"type": "primary"}, "node": "ip:address.1", "fields": ["PID", "ip"], "constraints": {"unique": true}}]}, {"name": "ks:asymmetric-key.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "algorithm", "type": "identity", "enumerate": [{"name": "ietf-crypto-types:rsa1024", "value": 0, "derived-paths": [{"derived-path": "ietf-crypto-types:asymmetric-key-encryption-algorithm/ietf-crypto-types:rsa1024"}]}, {"name": "ietf-crypto-types:rsa2048", "value": 1, "derived-paths": [{"derived-path": "ietf-crypto-types:asymmetric-key-encryption-algorithm/ietf-crypto-types:rsa2048"}]}, {"name": "ietf-crypto-types:rsa3072", "value": 2, "derived-paths": [{"derived-path": "ietf-crypto-types:asymmetric-key-encryption-algorithm/ietf-crypto-types:rsa3072"}]}, {"name": "ietf-crypto-types:rsa4096", "value": 3, "derived-paths": [{"derived-path": "ietf-crypto-types:asymmetric-key-encryption-algorithm/ietf-crypto-types:rsa4096"}]}, {"name": "ietf-crypto-types:rsa7680", "value": 4, "derived-paths": [{"derived-path": "ietf-crypto-types:asymmetric-key-encryption-algorithm/ietf-crypto-types:rsa7680"}]}, {"name": "ietf-crypto-types:rsa15360", "value": 5, "derived-paths": [{"derived-path": "ietf-crypto-types:asymmetric-key-encryption-algorithm/ietf-crypto-types:rsa15360"}]}], "nullable": true}, {"name": "public-key", "type": "string", "nullable": true}, {"name": "private-key", "type": "string", "nullable": true}, {"name": "certificates", "type": "container", "fields": []}], "clause": [{"type": "must", "formula": "(algorithm and public-key and private-key) or not(algorithm or public-key or private-key)"}], "keys": [{"name": "ks:asymmetric-key.1.PK", "index": {"type": "primary"}, "node": "ks:asymmetric-key.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "ks:certificate.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "cert", "type": "string", "nullable": true}], "keys": [{"name": "ks:certificate.1.PK", "index": {"type": "primary"}, "node": "ks:certificate.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "lldp:port.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/name"}]}, {"name": "dest-mac-address", "type": "string", "nullable": false}, {"name": "admin-status", "type": "enum", "enumerate": [{"name": "tx-only", "value": 1}, {"name": "rx-only", "value": 2}, {"name": "tx-and-rx", "value": 3}, {"name": "disabled", "value": 4}], "nullable": true, "default": 3}, {"name": "notification-enable", "type": "boolean", "nullable": true, "default": false}, {"name": "tlvs-tx-enable", "type": "string", "nullable": true}, {"name": "message-fast-tx", "type": "uint32", "nullable": true, "range": "1..3600", "default": 1}, {"name": "message-tx-hold-multiplier", "type": "uint32", "nullable": true, "range": "2..10", "default": 4}, {"name": "message-tx-interval", "type": "uint32", "nullable": true, "range": "1..3600", "default": 30}, {"name": "reinit-delay", "type": "uint32", "nullable": true, "range": "1..10", "default": 2}, {"name": "tx-credit-max", "type": "uint32", "nullable": true, "range": "1..10", "default": 5}, {"name": "tx-fast-init", "type": "uint32", "nullable": true, "range": "1..8", "default": 4}, {"name": "notification-interval", "type": "uint32", "nullable": true, "range": "1..3600", "default": 30}], "keys": [{"name": "lldp:port.1.PK", "index": {"type": "primary"}, "node": "lldp:port.1", "fields": ["PID", "name", "dest-mac-address"], "constraints": {"unique": true}}]}, {"name": "lldp:management-address-tx-port.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "address-subtype", "type": "identity", "enumerate": [{"name": "ietf-routing:ipv4", "value": 0, "derived-paths": [{"derived-path": "ietf-routing:address-family/ietf-routing:ipv4"}]}, {"name": "ietf-ipv4-unicast-routing:ipv4-unicast", "value": 1, "derived-paths": [{"derived-path": "ietf-routing:address-family/ietf-routing:ipv4/ietf-ipv4-unicast-routing:ipv4-unicast"}]}, {"name": "ietf-routing:ipv6", "value": 2, "derived-paths": [{"derived-path": "ietf-routing:address-family/ietf-routing:ipv6"}]}], "nullable": false}, {"name": "man-address", "type": "string", "nullable": false, "length": "1..31"}, {"name": "tx-enable", "type": "boolean", "nullable": true, "default": false}], "keys": [{"name": "lldp:management-address-tx-port.1.PK", "index": {"type": "primary"}, "node": "lldp:management-address-tx-port.1", "fields": ["PID", "address-subtype", "man-address"], "constraints": {"unique": true}}]}, {"name": "nacm:group.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "length": "1..max"}], "keys": [{"name": "nacm:group.1.PK", "index": {"type": "primary"}, "node": "nacm:group.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "nacm:user-name.1", "type": "leaf-list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "user-name", "type": "string", "nullable": false, "length": "1..max"}], "keys": [{"name": "nacm:user-name.1.PK", "index": {"type": "primary"}, "node": "nacm:user-name.1", "fields": ["PID", "user-name"], "constraints": {"unique": true}}]}, {"name": "nacm:rule-list.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "length": "1..max"}], "keys": [{"name": "nacm:rule-list.1.PK", "index": {"type": "primary"}, "node": "nacm:rule-list.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "nacm:group.2", "type": "leaf-list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "group", "type": "string", "nullable": false}], "keys": [{"name": "nacm:group.2.PK", "index": {"type": "primary"}, "node": "nacm:group.2", "fields": ["PID", "group"], "constraints": {"unique": true}}]}, {"name": "nacm:rule.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "length": "1..max"}, {"name": "module-name", "type": "string", "nullable": true, "default": "*"}, {"name": "access-operations", "type": "string", "nullable": true, "default": "*"}, {"name": "action", "type": "enum", "enumerate": [{"name": "permit", "value": 0}, {"name": "deny", "value": 1}], "nullable": false}, {"name": "comment", "type": "string", "nullable": true}, {"name": "rule-type", "type": "choice", "fields": [{"name": "protocol-operation", "type": "case", "fields": [{"name": "rpc-name", "type": "string", "nullable": true}]}, {"name": "notification", "type": "case", "fields": [{"name": "notification-name", "type": "string", "nullable": true}]}, {"name": "data-node", "type": "case", "fields": [{"name": "path", "type": "string", "nullable": false}]}]}], "keys": [{"name": "nacm:rule.1.PK", "index": {"type": "primary"}, "node": "nacm:rule.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "ni:instance.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "length": "1..31"}, {"name": "description", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "not(../name='_public_')"}], "length": "1..242"}], "keys": [{"name": "ni:instance.1.PK", "index": {"type": "primary"}, "node": "ni:instance.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "oc-telemetry:destination-group.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "group-id", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "../config/group-id"}]}, {"name": "config", "type": "container", "fields": [{"name": "group-id", "type": "string", "nullable": true}]}, {"name": "destinations", "type": "container", "fields": []}], "keys": [{"name": "oc-telemetry:destination-group.1.PK", "index": {"type": "primary"}, "node": "oc-telemetry:destination-group.1", "fields": ["PID", "group-id"], "constraints": {"unique": true}}]}, {"name": "oc-telemetry:destination.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "destination-address", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "../config/destination-address"}]}, {"name": "destination-port", "type": "uint16", "nullable": false, "clause": [{"type": "leafref", "formula": "../config/destination-port"}]}, {"name": "config", "type": "container", "fields": [{"name": "destination-address", "type": "string", "nullable": true}, {"name": "destination-port", "type": "uint16", "nullable": true}]}], "keys": [{"name": "oc-telemetry:destination.1.PK", "index": {"type": "primary"}, "node": "oc-telemetry:destination.1", "fields": ["PID", "destination-address", "destination-port"], "constraints": {"unique": true}}]}, {"name": "oc-telemetry:sensor-group.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sensor-group-id", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "../config/sensor-group-id"}]}, {"name": "config", "type": "container", "fields": [{"name": "sensor-group-id", "type": "string", "nullable": true}]}, {"name": "sensor-paths", "type": "container", "fields": []}], "keys": [{"name": "oc-telemetry:sensor-group.1.PK", "index": {"type": "primary"}, "node": "oc-telemetry:sensor-group.1", "fields": ["PID", "sensor-group-id"], "constraints": {"unique": true}}]}, {"name": "oc-telemetry:sensor-path.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "path", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "../config/path"}]}, {"name": "config", "type": "container", "fields": [{"name": "path", "type": "string", "nullable": true}, {"name": "exclude-filter", "type": "string", "nullable": true}]}], "keys": [{"name": "oc-telemetry:sensor-path.1.PK", "index": {"type": "primary"}, "node": "oc-telemetry:sensor-path.1", "fields": ["PID", "path"], "constraints": {"unique": true}}]}, {"name": "oc-telemetry:subscription.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "subscription-name", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "../config/subscription-name"}]}, {"name": "config", "type": "container", "fields": [{"name": "subscription-name", "type": "string", "nullable": true}, {"name": "local-source-address", "type": "string", "nullable": true}, {"name": "originated-qos-marking", "type": "uint8", "nullable": true, "range": "0..63"}, {"name": "protocol", "type": "identity", "enumerate": [{"name": "openconfig-telemetry-types:STREAM_SSH", "value": 0, "derived-paths": [{"derived-path": "openconfig-telemetry-types:STREAM_PROTOCOL/openconfig-telemetry-types:STREAM_SSH"}]}, {"name": "openconfig-telemetry-types:STREAM_GRPC", "value": 1, "derived-paths": [{"derived-path": "openconfig-telemetry-types:STREAM_PROTOCOL/openconfig-telemetry-types:STREAM_GRPC"}]}, {"name": "openconfig-telemetry-types:STREAM_JSON_RPC", "value": 2, "derived-paths": [{"derived-path": "openconfig-telemetry-types:STREAM_PROTOCOL/openconfig-telemetry-types:STREAM_JSON_RPC"}]}, {"name": "openconfig-telemetry-types:STREAM_THRIFT_RPC", "value": 3, "derived-paths": [{"derived-path": "openconfig-telemetry-types:STREAM_PROTOCOL/openconfig-telemetry-types:STREAM_THRIFT_RPC"}]}, {"name": "openconfig-telemetry-types:STREAM_WEBSOCKET_RPC", "value": 4, "derived-paths": [{"derived-path": "openconfig-telemetry-types:STREAM_PROTOCOL/openconfig-telemetry-types:STREAM_WEBSOCKET_RPC"}]}], "nullable": true}, {"name": "encoding", "type": "identity", "enumerate": [{"name": "openconfig-telemetry-types:ENC_XML", "value": 0, "derived-paths": [{"derived-path": "openconfig-telemetry-types:DATA_ENCODING_METHOD/openconfig-telemetry-types:ENC_XML"}]}, {"name": "openconfig-telemetry-types:ENC_JSON_IETF", "value": 1, "derived-paths": [{"derived-path": "openconfig-telemetry-types:DATA_ENCODING_METHOD/openconfig-telemetry-types:ENC_JSON_IETF"}]}, {"name": "openconfig-telemetry-types:ENC_PROTO3", "value": 2, "derived-paths": [{"derived-path": "openconfig-telemetry-types:DATA_ENCODING_METHOD/openconfig-telemetry-types:ENC_PROTO3"}]}], "nullable": true}]}, {"name": "sensor-profiles", "type": "container", "fields": []}, {"name": "destination-groups", "type": "container", "fields": []}], "keys": [{"name": "oc-telemetry:subscription.1.PK", "index": {"type": "primary"}, "node": "oc-telemetry:subscription.1", "fields": ["PID", "subscription-name"], "constraints": {"unique": true}}]}, {"name": "oc-telemetry:destination-group.2", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "group-id", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "../config/group-id"}]}, {"name": "config", "type": "container", "fields": [{"name": "group-id", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "../../../../../../../destination-groups/oc-telemetry:destination-group.1/group-id"}]}]}], "keys": [{"name": "oc-telemetry:destination-group.2.PK", "index": {"type": "primary"}, "node": "oc-telemetry:destination-group.2", "fields": ["PID", "group-id"], "constraints": {"unique": true}}]}, {"name": "oc-telemetry:sensor-profile.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sensor-group", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "../config/sensor-group"}]}, {"name": "config", "type": "container", "fields": [{"name": "sensor-group", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "../../../../../../../sensor-groups/oc-telemetry:sensor-group.1/config/sensor-group-id"}]}, {"name": "sample-interval", "type": "uint64", "nullable": true}, {"name": "heartbeat-interval", "type": "uint64", "nullable": true}, {"name": "suppress-redundant", "type": "boolean", "nullable": true}]}], "keys": [{"name": "oc-telemetry:sensor-profile.1.PK", "index": {"type": "primary"}, "node": "oc-telemetry:sensor-profile.1", "fields": ["PID", "sensor-group"], "constraints": {"unique": true}}]}, {"name": "ontload:ont-select.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "v-ani-if-name", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/name"}, {"type": "must", "formula": "/root/ietf-interfaces:interfaces/if:interface.1[name=current()]/type='bbf-xpon-if-type:v-ani'"}]}, {"name": "priority", "type": "enum", "enumerate": [{"name": "priority-right-now", "value": 0}, {"name": "priority-first", "value": 1}, {"name": "priority-second", "value": 2}, {"name": "priority-third", "value": 3}, {"name": "priority-fourth", "value": 4}], "nullable": true, "default": 4}, {"name": "active-mode", "type": "enum", "enumerate": [{"name": "next-startup", "value": 1}, {"name": "immediate", "value": 2}, {"name": "graceful", "value": 3}], "nullable": true}], "keys": [{"name": "ontload:ont-select.1.PK", "index": {"type": "primary"}, "node": "ontload:ont-select.1", "fields": ["PID", "v-ani-if-name"], "constraints": {"unique": true}}]}, {"name": "ps:protection-group.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "groupid", "type": "uint32", "nullable": false, "range": "0..1999"}, {"name": "protect-object", "type": "enum", "enumerate": [{"name": "main-board", "value": 0}, {"name": "main-board-port", "value": 1}, {"name": "main-board-lag", "value": 2}, {"name": "eth-nni-port", "value": 3}, {"name": "eth-nni-lagmember", "value": 4}, {"name": "epon-uni-port", "value": 5}, {"name": "gpon-uni-port", "value": 6}, {"name": "stm-nni-port", "value": 7}, {"name": "service-process-board", "value": 8}, {"name": "epon-uni-ont", "value": 9}, {"name": "gpon-uni-ont", "value": 10}, {"name": "twdm-channel-group", "value": 11}, {"name": "otn-vport", "value": 12}, {"name": "xpon-uni-port", "value": 13}], "nullable": true}, {"name": "work-mode", "type": "enum", "enumerate": [{"name": "time-delay", "value": 0}, {"name": "port-state", "value": 1}, {"name": "smartlink", "value": 2}, {"name": "uni-direction", "value": 3}, {"name": "bidirection", "value": 4}, {"name": "smartlink-balance", "value": 5}, {"name": "board-state", "value": 6}, {"name": "dual-parenting", "value": 7}, {"name": "dual-parenting-load-balance", "value": 8}], "nullable": true}, {"name": "reversion-time", "type": "uint32", "nullable": true, "range": "1..1440", "default": 720}, {"name": "reversion-switch", "type": "enum", "enumerate": [{"name": "enable", "value": 0}, {"name": "disable", "value": 1}], "nullable": true}, {"name": "freeze", "type": "enum", "enumerate": [{"name": "freeze", "value": 0}, {"name": "undo-freeze", "value": 1}], "nullable": true}, {"name": "admin-switch", "type": "enum", "enumerate": [{"name": "enable", "value": 0}, {"name": "disable", "value": 1}], "nullable": true}, {"name": "description", "type": "string", "nullable": true, "length": "0..64"}], "keys": [{"name": "ps:protection-group.1.PK", "index": {"type": "primary"}, "node": "ps:protection-group.1", "fields": ["PID", "groupid"], "constraints": {"unique": true}}]}, {"name": "ps:member.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "memberid", "type": "uint32", "nullable": false, "range": "0..3999"}, {"name": "role", "type": "enum", "enumerate": [{"name": "work", "value": 0}, {"name": "protect", "value": 1}], "nullable": true}, {"name": "lock-status", "type": "enum", "enumerate": [{"name": "lockout", "value": 0}, {"name": "unlockout", "value": 1}], "nullable": true}, {"name": "member-object", "type": "choice", "fields": [{"name": "board", "type": "case", "fields": [{"name": "board", "type": "container", "fields": [{"name": "hw-name", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/ietf-hardware:hardware/hw:component.1/name"}]}]}]}, {"name": "port", "type": "case", "fields": [{"name": "port", "type": "container", "fields": [{"name": "if-name", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/name"}]}]}]}, {"name": "ont", "type": "case", "fields": [{"name": "ont", "type": "container", "fields": [{"name": "if-name", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/name"}]}]}]}]}], "keys": [{"name": "ps:member.1.PK", "index": {"type": "primary"}, "node": "ps:member.1", "fields": ["PID", "memberid"], "constraints": {"unique": true}}]}, {"name": "rt:control-plane-protocol.2", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "type", "type": "identity", "enumerate": [{"name": "ietf-routing:routing-protocol", "value": 0, "derived-paths": [{"derived-path": "ietf-routing:control-plane-protocol/ietf-routing:routing-protocol"}]}, {"name": "ietf-routing:direct", "value": 1, "derived-paths": [{"derived-path": "ietf-routing:control-plane-protocol/ietf-routing:routing-protocol/ietf-routing:direct"}]}, {"name": "ietf-routing:static", "value": 2, "derived-paths": [{"derived-path": "ietf-routing:control-plane-protocol/ietf-routing:routing-protocol/ietf-routing:static"}]}], "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "static-routes", "type": "container", "fields": [{"name": "ipv4", "type": "container", "fields": []}], "clause": [{"type": "when", "formula": "derived-from-or-self(../type, 'ietf-routing:static')"}]}], "keys": [{"name": "rt:control-plane-protocol.2.PK", "index": {"type": "primary"}, "node": "rt:control-plane-protocol.2", "fields": ["PID", "type", "name"], "constraints": {"unique": true}}]}, {"name": "v4ur:route.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "destination-prefix", "type": "string", "nullable": false}, {"name": "next-hop", "type": "container", "fields": [{"name": "next-hop-options", "type": "choice", "fields": [{"name": "next-hop-list", "type": "case", "fields": [{"name": "next-hop-list", "type": "container", "fields": []}]}]}]}], "keys": [{"name": "v4ur:route.1.PK", "index": {"type": "primary"}, "node": "v4ur:route.1", "fields": ["PID", "destination-prefix"], "constraints": {"unique": true}}]}, {"name": "v4ur:next-hop.2", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "index", "type": "string", "nullable": false}, {"name": "next-hop-address", "type": "string", "nullable": true}], "keys": [{"name": "v4ur:next-hop.2.PK", "index": {"type": "primary"}, "node": "v4ur:next-hop.2", "fields": ["PID", "index"], "constraints": {"unique": true}}]}, {"name": "snmp:community.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "index", "type": "string", "nullable": false, "length": "1..32"}, {"name": "security-name", "type": "string", "nullable": false, "length": "1..32"}, {"name": "engine-id", "type": "string", "nullable": true}, {"name": "context", "type": "string", "nullable": true, "length": "0..32", "default": ""}, {"name": "target-tag", "type": "string", "nullable": true, "length": "0..255"}, {"name": "name", "type": "choice", "fields": [{"name": "text-name", "type": "case", "fields": [{"name": "text-name", "type": "string", "nullable": true}]}, {"name": "binary-name", "type": "case", "fields": [{"name": "binary-name", "type": "string", "nullable": true}]}]}], "keys": [{"name": "snmp:community.1.PK", "index": {"type": "primary"}, "node": "snmp:community.1", "fields": ["PID", "index"], "constraints": {"unique": true}}]}, {"name": "snmp:listen.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "length": "1..32"}, {"name": "transport", "type": "choice", "fields": [{"name": "udp", "type": "case", "fields": [{"name": "udp", "type": "container", "fields": [{"name": "ip", "type": "string", "nullable": false}, {"name": "port", "type": "uint16", "nullable": true, "range": "0..65535"}]}]}, {"name": "tls", "type": "case", "fields": [{"name": "tls", "type": "container", "fields": [{"name": "ip", "type": "string", "nullable": false}, {"name": "port", "type": "uint16", "nullable": true, "range": "0..65535"}]}]}, {"name": "dtls", "type": "case", "fields": [{"name": "dtls", "type": "container", "fields": [{"name": "ip", "type": "string", "nullable": false}, {"name": "port", "type": "uint16", "nullable": true, "range": "0..65535"}]}]}, {"name": "ssh", "type": "case", "fields": [{"name": "ssh", "type": "container", "fields": [{"name": "ip", "type": "string", "nullable": false}, {"name": "port", "type": "uint16", "nullable": true, "range": "0..65535"}]}]}]}], "keys": [{"name": "snmp:listen.1.PK", "index": {"type": "primary"}, "node": "snmp:listen.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "snmp:notify.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "length": "1..32"}, {"name": "tag", "type": "string", "nullable": false, "length": "0..255"}, {"name": "type", "type": "enum", "enumerate": [{"name": "trap", "value": 1}, {"name": "inform", "value": 2}], "nullable": true, "default": 1}], "keys": [{"name": "snmp:notify.1.PK", "index": {"type": "primary"}, "node": "snmp:notify.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "snmp:notify-filter-profile.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "length": "1..32"}], "keys": [{"name": "snmp:notify-filter-profile.1.PK", "index": {"type": "primary"}, "node": "snmp:notify-filter-profile.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "snmp:exclude.1", "type": "leaf-list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "exclude", "type": "string", "nullable": false}], "keys": [{"name": "snmp:exclude.1.PK", "index": {"type": "primary"}, "node": "snmp:exclude.1", "fields": ["PID", "exclude"], "constraints": {"unique": true}}]}, {"name": "snmp:include.1", "type": "leaf-list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "include", "type": "string", "nullable": false}], "keys": [{"name": "snmp:include.1.PK", "index": {"type": "primary"}, "node": "snmp:include.1", "fields": ["PID", "include"], "constraints": {"unique": true}}]}, {"name": "snmp:proxy.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "length": "1..32"}, {"name": "type", "type": "enum", "enumerate": [{"name": "read", "value": 1}, {"name": "write", "value": 2}, {"name": "trap", "value": 3}, {"name": "inform", "value": 4}], "nullable": false}, {"name": "context-engine-id", "type": "string", "nullable": false}, {"name": "context-name", "type": "string", "nullable": true, "length": "0..32"}, {"name": "target-params-in", "type": "string", "nullable": true, "length": "1..32"}, {"name": "single-target-out", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "../type = 'read' or ../type = 'write'"}], "length": "1..32"}, {"name": "multiple-target-out", "type": "string", "nullable": true, "clause": [{"type": "when", "formula": "../type = 'trap' or ../type = 'inform'"}], "length": "0..255"}], "keys": [{"name": "snmp:proxy.1.PK", "index": {"type": "primary"}, "node": "snmp:proxy.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "snmp:target.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "length": "1..32"}, {"name": "timeout", "type": "uint32", "nullable": true, "default": 1500}, {"name": "retries", "type": "uint8", "nullable": true, "default": 3}, {"name": "target-params", "type": "string", "nullable": false, "length": "1..32"}, {"name": "mms", "type": "string", "nullable": true, "default": "484"}, {"name": "transport", "type": "choice", "fields": [{"name": "udp", "type": "case", "fields": [{"name": "udp", "type": "container", "fields": [{"name": "ip", "type": "string", "nullable": false}, {"name": "port", "type": "uint16", "nullable": true, "range": "0..65535", "default": 162}, {"name": "prefix-length", "type": "uint8", "nullable": true}]}]}, {"name": "tls", "type": "case", "fields": [{"name": "tls", "type": "container", "fields": [{"name": "ip", "type": "string", "nullable": false}, {"name": "port", "type": "uint16", "nullable": true, "range": "0..65535", "default": 10161}, {"name": "client-fingerprint", "type": "string", "nullable": true}, {"name": "server-fingerprint", "type": "string", "nullable": true}, {"name": "server-identity", "type": "string", "nullable": true, "length": "0..255"}]}]}, {"name": "dtls", "type": "case", "fields": [{"name": "dtls", "type": "container", "fields": [{"name": "ip", "type": "string", "nullable": false}, {"name": "port", "type": "uint16", "nullable": true, "range": "0..65535", "default": 10161}, {"name": "client-fingerprint", "type": "string", "nullable": true}, {"name": "server-fingerprint", "type": "string", "nullable": true}, {"name": "server-identity", "type": "string", "nullable": true, "length": "0..255"}]}]}, {"name": "ssh", "type": "case", "fields": [{"name": "ssh", "type": "container", "fields": [{"name": "ip", "type": "string", "nullable": false}, {"name": "port", "type": "uint16", "nullable": true, "range": "0..65535", "default": 5161}, {"name": "username", "type": "string", "nullable": true}]}]}]}], "keys": [{"name": "snmp:target.1.PK", "index": {"type": "primary"}, "node": "snmp:target.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "snmp:target-params.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "length": "1..32"}, {"name": "notify-filter-profile", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/ietf-snmp:snmp/snmp:notify-filter-profile.1/name"}]}, {"name": "params", "type": "choice", "fields": [{"name": "v1", "type": "case", "fields": [{"name": "v1", "type": "container", "fields": [{"name": "security-name", "type": "string", "nullable": false, "length": "1..32"}]}]}, {"name": "v2c", "type": "case", "fields": [{"name": "v2c", "type": "container", "fields": [{"name": "security-name", "type": "string", "nullable": false, "length": "1..32"}]}]}, {"name": "usm", "type": "case", "fields": [{"name": "usm", "type": "container", "fields": [{"name": "user-name", "type": "string", "nullable": false, "length": "1..32"}, {"name": "security-level", "type": "enum", "enumerate": [{"name": "no-auth-no-priv", "value": 1}, {"name": "auth-no-priv", "value": 2}, {"name": "auth-priv", "value": 3}], "nullable": false}]}]}, {"name": "tsm", "type": "case", "fields": [{"name": "tsm", "type": "container", "fields": [{"name": "security-name", "type": "string", "nullable": false, "length": "1..32"}, {"name": "security-level", "type": "enum", "enumerate": [{"name": "no-auth-no-priv", "value": 1}, {"name": "auth-no-priv", "value": 2}, {"name": "auth-priv", "value": 3}], "nullable": false}]}]}]}], "keys": [{"name": "snmp:target-params.1.PK", "index": {"type": "primary"}, "node": "snmp:target-params.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "snmp:tag.1", "type": "leaf-list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "tag", "type": "string", "nullable": false, "length": "0..255"}], "keys": [{"name": "snmp:tag.1.PK", "index": {"type": "primary"}, "node": "snmp:tag.1", "fields": ["PID", "tag"], "constraints": {"unique": true}}]}, {"name": "snmp:cert-to-name.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "id", "type": "uint32", "nullable": false}, {"name": "fingerprint", "type": "string", "nullable": false}, {"name": "map-type", "type": "identity", "enumerate": [{"name": "ietf-x509-cert-to-name:specified", "value": 0, "derived-paths": [{"derived-path": "ietf-x509-cert-to-name:cert-to-name/ietf-x509-cert-to-name:specified"}]}, {"name": "ietf-x509-cert-to-name:san-rfc822-name", "value": 1, "derived-paths": [{"derived-path": "ietf-x509-cert-to-name:cert-to-name/ietf-x509-cert-to-name:san-rfc822-name"}]}, {"name": "ietf-x509-cert-to-name:san-dns-name", "value": 2, "derived-paths": [{"derived-path": "ietf-x509-cert-to-name:cert-to-name/ietf-x509-cert-to-name:san-dns-name"}]}, {"name": "ietf-x509-cert-to-name:san-ip-address", "value": 3, "derived-paths": [{"derived-path": "ietf-x509-cert-to-name:cert-to-name/ietf-x509-cert-to-name:san-ip-address"}]}, {"name": "ietf-x509-cert-to-name:san-any", "value": 4, "derived-paths": [{"derived-path": "ietf-x509-cert-to-name:cert-to-name/ietf-x509-cert-to-name:san-any"}]}, {"name": "ietf-x509-cert-to-name:common-name", "value": 5, "derived-paths": [{"derived-path": "ietf-x509-cert-to-name:cert-to-name/ietf-x509-cert-to-name:common-name"}]}], "nullable": false}, {"name": "name", "type": "string", "nullable": false, "clause": [{"type": "when", "formula": "../map-type = 'ietf-x509-cert-to-name:specified'"}]}], "keys": [{"name": "snmp:cert-to-name.1.PK", "index": {"type": "primary"}, "node": "snmp:cert-to-name.1", "fields": ["PID", "id"], "constraints": {"unique": true}}]}, {"name": "snmp:user.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "length": "1..32"}, {"name": "auth", "type": "container", "fields": [{"name": "protocol", "type": "choice", "fields": [{"name": "md5", "type": "case", "fields": [{"name": "md5", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false}]}]}, {"name": "sha", "type": "case", "fields": [{"name": "sha", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false}]}]}, {"name": "sha2-224", "type": "case", "fields": [{"name": "sha2-224", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false, "length": "2..max"}]}]}, {"name": "sha2-256", "type": "case", "fields": [{"name": "sha2-256", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false, "length": "2..max"}]}]}, {"name": "sha2-384", "type": "case", "fields": [{"name": "sha2-384", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false, "length": "2..max"}]}]}, {"name": "sha2-512", "type": "case", "fields": [{"name": "sha2-512", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false, "length": "2..max"}]}]}]}]}, {"name": "priv", "type": "container", "fields": [{"name": "protocol", "type": "choice", "fields": [{"name": "des", "type": "case", "fields": [{"name": "des", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false}]}]}, {"name": "aes", "type": "case", "fields": [{"name": "aes", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false}]}]}, {"name": "aes192", "type": "case", "fields": [{"name": "aes192", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false, "length": "2..max"}]}]}, {"name": "aes256", "type": "case", "fields": [{"name": "aes256", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false, "length": "2..max"}]}]}]}], "clause": [{"type": "must", "formula": "../auth"}]}], "keys": [{"name": "snmp:user.1.PK", "index": {"type": "primary"}, "node": "snmp:user.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "snmp:remote.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "engine-id", "type": "string", "nullable": false}], "keys": [{"name": "snmp:remote.1.PK", "index": {"type": "primary"}, "node": "snmp:remote.1", "fields": ["PID", "engine-id"], "constraints": {"unique": true}}]}, {"name": "snmp:user.2", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "length": "1..32"}, {"name": "auth", "type": "container", "fields": [{"name": "protocol", "type": "choice", "fields": [{"name": "md5", "type": "case", "fields": [{"name": "md5", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false}]}]}, {"name": "sha", "type": "case", "fields": [{"name": "sha", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false}]}]}, {"name": "sha2-224", "type": "case", "fields": [{"name": "sha2-224", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false, "length": "2..max"}]}]}, {"name": "sha2-256", "type": "case", "fields": [{"name": "sha2-256", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false, "length": "2..max"}]}]}, {"name": "sha2-384", "type": "case", "fields": [{"name": "sha2-384", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false, "length": "2..max"}]}]}, {"name": "sha2-512", "type": "case", "fields": [{"name": "sha2-512", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false, "length": "2..max"}]}]}]}]}, {"name": "priv", "type": "container", "fields": [{"name": "protocol", "type": "choice", "fields": [{"name": "des", "type": "case", "fields": [{"name": "des", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false}]}]}, {"name": "aes", "type": "case", "fields": [{"name": "aes", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false}]}]}, {"name": "aes192", "type": "case", "fields": [{"name": "aes192", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false, "length": "2..max"}]}]}, {"name": "aes256", "type": "case", "fields": [{"name": "aes256", "type": "container", "fields": [{"name": "key", "type": "string", "nullable": false, "length": "2..max"}]}]}]}], "clause": [{"type": "must", "formula": "../auth"}]}], "keys": [{"name": "snmp:user.2.PK", "index": {"type": "primary"}, "node": "snmp:user.2", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "snmp:group.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "length": "1..32"}], "keys": [{"name": "snmp:group.1.PK", "index": {"type": "primary"}, "node": "snmp:group.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "snmp:access.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "context", "type": "string", "nullable": false, "length": "0..32"}, {"name": "context-match", "type": "enum", "enumerate": [{"name": "exact", "value": 1}, {"name": "prefix", "value": 2}], "nullable": true, "default": 1}, {"name": "security-model", "type": "string", "nullable": false}, {"name": "security-level", "type": "enum", "enumerate": [{"name": "no-auth-no-priv", "value": 1}, {"name": "auth-no-priv", "value": 2}, {"name": "auth-priv", "value": 3}], "nullable": false}, {"name": "read-view", "type": "string", "nullable": true, "length": "1..32"}, {"name": "write-view", "type": "string", "nullable": true, "length": "1..32"}, {"name": "notify-view", "type": "string", "nullable": true, "length": "1..32"}], "keys": [{"name": "snmp:access.1.PK", "index": {"type": "primary"}, "node": "snmp:access.1", "fields": ["PID", "context", "security-model", "security-level"], "constraints": {"unique": true}}]}, {"name": "snmp:member.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "security-name", "type": "string", "nullable": false, "length": "1..32"}], "keys": [{"name": "snmp:member.1.PK", "index": {"type": "primary"}, "node": "snmp:member.1", "fields": ["PID", "security-name"], "constraints": {"unique": true}}]}, {"name": "snmp:security-model.1", "type": "leaf-list", "config": {"check_validity": true}, "min-elements": 1, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "security-model", "type": "string", "nullable": false}], "keys": [{"name": "snmp:security-model.1.PK", "index": {"type": "primary"}, "node": "snmp:security-model.1", "fields": ["PID", "security-model"], "constraints": {"unique": true}}]}, {"name": "snmp:view.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "length": "1..32"}], "keys": [{"name": "snmp:view.1.PK", "index": {"type": "primary"}, "node": "snmp:view.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "snmp:exclude.2", "type": "leaf-list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "exclude", "type": "string", "nullable": false}], "keys": [{"name": "snmp:exclude.2.PK", "index": {"type": "primary"}, "node": "snmp:exclude.2", "fields": ["PID", "exclude"], "constraints": {"unique": true}}]}, {"name": "snmp:include.2", "type": "leaf-list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "include", "type": "string", "nullable": false}], "keys": [{"name": "snmp:include.2.PK", "index": {"type": "primary"}, "node": "snmp:include.2", "fields": ["PID", "include"], "constraints": {"unique": true}}]}, {"name": "bbf-ldra-ext:dhcpv6-option-permit-forwarding.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "ifname", "type": "string", "nullable": false, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/name"}, {"type": "must", "formula": "derived-from-or-self(/root/ietf-interfaces:interfaces/if:interface.1[name = current()]/type,'iana-if-type:ethernetCsmacd')"}]}, {"name": "switch", "type": "boolean", "nullable": true, "default": true}], "keys": [{"name": "bbf-ldra-ext:dhcpv6-option-permit-forwarding.1.PK", "index": {"type": "primary"}, "node": "bbf-ldra-ext:dhcpv6-option-permit-forwarding.1", "fields": ["PID", "ifname"], "constraints": {"unique": true}}]}, {"name": "bbf-raioprof:aggregation-circuit-id-format.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "subscriber-object", "type": "enum", "enumerate": [{"name": "pitp-pmode", "value": 1}, {"name": "dhcp-option82", "value": 2}, {"name": "dhcpv6-option", "value": 3}, {"name": "radius-nas-port-id", "value": 4}, {"name": "ancp", "value": 5}, {"name": "pitp-vmode", "value": 10}], "nullable": false}, {"name": "string-type", "type": "enum", "enumerate": [{"name": "ascii", "value": 1}, {"name": "binary", "value": 2}], "nullable": true}, {"name": "access-type", "type": "enum", "enumerate": [{"name": "xpon", "value": 1}, {"name": "ont", "value": 2}, {"name": "mxu", "value": 3}], "nullable": true}, {"name": "format-string", "type": "string", "nullable": false, "length": "0..127"}], "keys": [{"name": "bbf-raioprof:aggregation-circuit-id-format.1.PK", "index": {"type": "primary"}, "node": "bbf-raioprof:aggregation-circuit-id-format.1", "fields": ["PID", "format-string", "subscriber-object"], "constraints": {"unique": true}}]}, {"name": "bbf-raioprof:sub-option-switch.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "access-loop-suboptions", "type": "string", "nullable": false}, {"name": "subscriber-object", "type": "enum", "enumerate": [{"name": "pitp-pmode", "value": 1}, {"name": "dhcp-option82", "value": 2}, {"name": "dhcpv6-option", "value": 3}, {"name": "radius-nas-port-id", "value": 4}, {"name": "ancp", "value": 5}, {"name": "pitp-vmode", "value": 10}], "nullable": true}, {"name": "enabled", "type": "boolean", "nullable": true}], "keys": [{"name": "bbf-raioprof:sub-option-switch.1.PK", "index": {"type": "primary"}, "node": "bbf-raioprof:sub-option-switch.1", "fields": ["PID", "access-loop-suboptions"], "constraints": {"unique": true}}]}, {"name": "sys:user.1", "type": "list", "config": {"check_validity": true}, "max-elements": 128, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "password", "type": "string", "nullable": true}, {"name": "service-type", "type": "enum", "enumerate": [{"name": "cli", "value": 0}, {"name": "netconf", "value": 1}], "nullable": true, "default": 1}, {"name": "authentication-type", "type": "enum", "enumerate": [{"name": "password", "value": 1}, {"name": "rsa", "value": 2}, {"name": "sm2", "value": 9}, {"name": "x509v3-rsa", "value": 11}, {"name": "password-x509v3-rsa", "value": 12}, {"name": "ed25519", "value": 15}], "nullable": true, "default": 1}, {"name": "reenter-num", "type": "uint32", "nullable": true, "range": "0..20", "default": 1}, {"name": "level", "type": "enum", "enumerate": [{"name": "common", "value": 1}, {"name": "operator", "value": 2}, {"name": "administrator", "value": 3}, {"name": "super", "value": 4}], "nullable": true, "default": "common"}, {"name": "description", "type": "string", "nullable": true, "length": "0..100", "default": "-----"}, {"name": "profile", "type": "string", "nullable": true, "length": "1..15", "default": "root"}, {"name": "is-emergency-account", "type": "boolean", "nullable": true, "clause": [{"type": "must", "formula": "../is-emergency-account=false() or(../is-emergency-account=true() and ../service-type='cli')"}], "default": false}], "keys": [{"name": "sys:user.1.PK", "index": {"type": "primary"}, "node": "sys:user.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "sys:authorized-key.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "algorithm", "type": "string", "nullable": false}, {"name": "key-data", "type": "string", "nullable": false, "length": "1..4096"}], "keys": [{"name": "sys:authorized-key.1.PK", "index": {"type": "primary"}, "node": "sys:authorized-key.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "system-ext:user-client.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "id", "type": "int32", "nullable": false, "range": "1..255"}], "keys": [{"name": "system-ext:user-client.1.PK", "index": {"type": "primary"}, "node": "system-ext:user-client.1", "fields": ["PID", "id"], "constraints": {"unique": true}}]}, {"name": "sys:search.1", "type": "leaf-list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "search", "type": "string", "nullable": false, "length": "1..253"}], "keys": [{"name": "sys:search.1.PK", "index": {"type": "primary"}, "node": "sys:search.1", "fields": ["PID", "search"], "constraints": {"unique": true}}]}, {"name": "sys:server.2", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "transport", "type": "choice", "fields": [{"name": "udp-and-tcp", "type": "case", "fields": [{"name": "udp-and-tcp", "type": "container", "fields": [{"name": "address", "type": "string", "nullable": false}, {"name": "port", "type": "uint16", "nullable": true, "range": "0..65535", "default": 53}]}]}]}], "keys": [{"name": "sys:server.2.PK", "index": {"type": "primary"}, "node": "sys:server.2", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "sys:server.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "association-type", "type": "enum", "enumerate": [{"name": "server", "value": 0}, {"name": "peer", "value": 1}, {"name": "pool", "value": 2}], "nullable": true, "default": 0}, {"name": "iburst", "type": "boolean", "nullable": true, "default": false}, {"name": "prefer", "type": "boolean", "nullable": true, "default": false}, {"name": "transport", "type": "choice", "fields": [{"name": "udp", "type": "case", "fields": [{"name": "udp", "type": "container", "fields": [{"name": "address", "type": "string", "nullable": false}, {"name": "port", "type": "uint16", "nullable": true, "range": "0..65535", "default": 123}]}]}]}], "keys": [{"name": "sys:server.1.PK", "index": {"type": "primary"}, "node": "sys:server.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "sys:server.3", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "authentication-type", "type": "identity", "enumerate": [{"name": "ietf-system:radius-pap", "value": 0, "derived-paths": [{"derived-path": "ietf-system:radius-authentication-type/ietf-system:radius-pap"}]}, {"name": "ietf-system:radius-chap", "value": 1, "derived-paths": [{"derived-path": "ietf-system:radius-authentication-type/ietf-system:radius-chap"}]}], "nullable": true, "default": "ietf-system:radius-pap"}, {"name": "transport", "type": "choice", "fields": [{"name": "udp", "type": "case", "fields": [{"name": "udp", "type": "container", "fields": [{"name": "address", "type": "string", "nullable": false}, {"name": "authentication-port", "type": "uint16", "nullable": true, "range": "0..65535", "default": 1812}, {"name": "shared-secret", "type": "string", "nullable": false}]}]}]}], "keys": [{"name": "sys:server.3.PK", "index": {"type": "primary"}, "node": "sys:server.3", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "system-ext:auto-save-policy.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "auto-save-type", "type": "enum", "enumerate": [{"name": "database", "value": 1}, {"name": "configuration", "value": 2}], "nullable": false}, {"name": "auto-save-interval-switch", "type": "enum", "enumerate": [{"name": "on", "value": 1}, {"name": "off", "value": 2}], "nullable": true, "default": 2}, {"name": "auto-save-interval", "type": "uint32", "nullable": true, "range": "60000..60480000"}, {"name": "auto-save-time-switch", "type": "enum", "enumerate": [{"name": "on", "value": 1}, {"name": "off", "value": 2}], "nullable": true, "default": 1}, {"name": "auto-save-time", "type": "uint32", "nullable": true}, {"name": "auto-save-change-interval", "type": "uint32", "nullable": true, "range": "12000..8640000", "default": 180000}], "keys": [{"name": "system-ext:auto-save-policy.1.PK", "index": {"type": "primary"}, "node": "system-ext:auto-save-policy.1", "fields": ["PID", "auto-save-type"], "constraints": {"unique": true}}]}, {"name": "system-ext:management-mode.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "configuration-type", "type": "identity", "enumerate": [{"name": "huawei-ietf-system-ext:service-provisioning", "value": 0, "derived-paths": [{"derived-path": "huawei-ietf-system-ext:configuration-type/huawei-ietf-system-ext:service-provisioning"}]}], "nullable": false}, {"name": "mode", "type": "enum", "enumerate": [{"name": "default", "value": 0}, {"name": "netconf", "value": 1}], "nullable": false}], "keys": [{"name": "system-ext:management-mode.1.PK", "index": {"type": "primary"}, "node": "system-ext:management-mode.1", "fields": ["PID", "configuration-type"], "constraints": {"unique": true}}]}, {"name": "system-ext:network-server-source-interface.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "protocol-type", "type": "enum", "enumerate": [{"name": "ssh", "value": 1}, {"name": "snmp", "value": 2}, {"name": "netconf", "value": 3}, {"name": "telnet-proxy", "value": 4}, {"name": "trace", "value": 5}, {"name": "telnet", "value": 6}, {"name": "twamp", "value": 7}, {"name": "ancp-proxy", "value": 8}, {"name": "web-proxy", "value": 9}, {"name": "ipdr", "value": 10}, {"name": "dhcp-relay", "value": 11}, {"name": "portal", "value": 12}, {"name": "capwap", "value": 13}, {"name": "mqtt", "value": 14}], "nullable": false}, {"name": "source-interface", "type": "string", "nullable": false, "length": "1..63"}], "keys": [{"name": "system-ext:network-server-source-interface.1.PK", "index": {"type": "primary"}, "node": "system-ext:network-server-source-interface.1", "fields": ["PID", "protocol-type", "source-interface"], "constraints": {"unique": true}}]}, {"name": "system-ext:network-server-source-ip.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "protocol-type", "type": "enum", "enumerate": [{"name": "ssh", "value": 1}, {"name": "snmp", "value": 2}, {"name": "netconf", "value": 3}, {"name": "telnet-proxy", "value": 4}, {"name": "trace", "value": 5}, {"name": "telnet", "value": 6}, {"name": "twamp", "value": 7}, {"name": "ancp-proxy", "value": 8}, {"name": "web-proxy", "value": 9}, {"name": "ipdr", "value": 10}, {"name": "dhcp-relay", "value": 11}, {"name": "portal", "value": 12}, {"name": "capwap", "value": 13}, {"name": "mqtt", "value": 14}], "nullable": false}, {"name": "source-ip", "type": "string", "nullable": false}, {"name": "vrf-name", "type": "string", "nullable": true, "length": "1..31"}], "keys": [{"name": "system-ext:network-server-source-ip.1.PK", "index": {"type": "primary"}, "node": "system-ext:network-server-source-ip.1", "fields": ["PID", "protocol-type", "source-ip"], "constraints": {"unique": true}}]}, {"name": "syslog-ext:severity-filter.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "severity", "type": "string", "nullable": false}, {"name": "action", "type": "enum", "enumerate": [{"name": "block", "value": 0}, {"name": "log", "value": 1}], "nullable": true}], "keys": [{"name": "syslog-ext:severity-filter.1.PK", "index": {"type": "primary"}, "node": "syslog-ext:severity-filter.1", "fields": ["PID", "severity"], "constraints": {"unique": true}}]}, {"name": "syslog-ext:log.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "log-type", "type": "enum", "enumerate": [{"name": "operation", "value": 0}, {"name": "alarm-event", "value": 1}, {"name": "igmp", "value": 2}, {"name": "security", "value": 3}, {"name": "ssa", "value": 4}, {"name": "all", "value": 5}], "nullable": false}, {"name": "facility", "type": "identity", "enumerate": [{"name": "ietf-syslog:kern", "value": 0, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:kern"}]}, {"name": "ietf-syslog:user", "value": 1, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:user"}]}, {"name": "ietf-syslog:mail", "value": 2, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:mail"}]}, {"name": "ietf-syslog:daemon", "value": 3, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:daemon"}]}, {"name": "ietf-syslog:auth", "value": 4, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:auth"}]}, {"name": "ietf-syslog:syslog", "value": 5, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:syslog"}]}, {"name": "ietf-syslog:lpr", "value": 6, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:lpr"}]}, {"name": "ietf-syslog:news", "value": 7, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:news"}]}, {"name": "ietf-syslog:uucp", "value": 8, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:uucp"}]}, {"name": "ietf-syslog:cron", "value": 9, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:cron"}]}, {"name": "ietf-syslog:authpriv", "value": 10, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:authpriv"}]}, {"name": "ietf-syslog:ftp", "value": 11, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:ftp"}]}, {"name": "ietf-syslog:ntp", "value": 12, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:ntp"}]}, {"name": "ietf-syslog:audit", "value": 13, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:audit"}]}, {"name": "ietf-syslog:console", "value": 14, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:console"}]}, {"name": "ietf-syslog:cron2", "value": 15, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:cron2"}]}, {"name": "ietf-syslog:local0", "value": 16, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local0"}]}, {"name": "ietf-syslog:local1", "value": 17, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local1"}]}, {"name": "ietf-syslog:local2", "value": 18, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local2"}]}, {"name": "ietf-syslog:local3", "value": 19, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local3"}]}, {"name": "ietf-syslog:local4", "value": 20, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local4"}]}, {"name": "ietf-syslog:local5", "value": 21, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local5"}]}, {"name": "ietf-syslog:local6", "value": 22, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local6"}]}, {"name": "ietf-syslog:local7", "value": 23, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local7"}]}], "nullable": true}, {"name": "severity", "type": "enum", "enumerate": [{"name": "emergency", "value": 0}, {"name": "alert", "value": 1}, {"name": "critical", "value": 2}, {"name": "error", "value": 3}, {"name": "warning", "value": 4}, {"name": "notice", "value": 5}, {"name": "info", "value": 6}, {"name": "debug", "value": 7}], "nullable": true}, {"name": "enabled", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../log-type != 'operation'"}]}], "keys": [{"name": "syslog-ext:log.1.PK", "index": {"type": "primary"}, "node": "syslog-ext:log.1", "fields": ["PID", "log-type"], "constraints": {"unique": true}}]}, {"name": "syslog:facility-list.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "facility", "type": "string", "nullable": false}, {"name": "severity", "type": "string", "nullable": false}, {"name": "advanced-compare", "type": "container", "fields": [{"name": "compare", "type": "enum", "enumerate": [{"name": "equals", "value": 0}, {"name": "equals-or-higher", "value": 1}], "nullable": true, "default": 1}, {"name": "action", "type": "enum", "enumerate": [{"name": "log", "value": 0}, {"name": "block", "value": 1}], "nullable": true, "default": 0}], "clause": [{"type": "when", "formula": "../severity != 'all' and ../severity != 'none'"}]}], "keys": [{"name": "syslog:facility-list.1.PK", "index": {"type": "primary"}, "node": "syslog:facility-list.1", "fields": ["PID", "facility", "severity"], "constraints": {"unique": true}}]}, {"name": "syslog:log-file.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "pattern-match", "type": "string", "nullable": true}, {"name": "structured-data", "type": "boolean", "nullable": true, "default": false}, {"name": "facility-filter", "type": "container", "fields": []}, {"name": "file-rotation", "type": "container", "fields": [{"name": "number-of-files", "type": "uint32", "nullable": true, "default": 1}, {"name": "max-file-size", "type": "uint32", "nullable": true}, {"name": "rollover", "type": "uint32", "nullable": true}, {"name": "retention", "type": "uint32", "nullable": true}]}], "keys": [{"name": "syslog:log-file.1.PK", "index": {"type": "primary"}, "node": "syslog:log-file.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "syslog:facility-list.2", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "facility", "type": "string", "nullable": false}, {"name": "severity", "type": "string", "nullable": false}, {"name": "advanced-compare", "type": "container", "fields": [{"name": "compare", "type": "enum", "enumerate": [{"name": "equals", "value": 0}, {"name": "equals-or-higher", "value": 1}], "nullable": true, "default": 1}, {"name": "action", "type": "enum", "enumerate": [{"name": "log", "value": 0}, {"name": "block", "value": 1}], "nullable": true, "default": 0}], "clause": [{"type": "when", "formula": "../severity != 'all' and ../severity != 'none'"}]}], "keys": [{"name": "syslog:facility-list.2.PK", "index": {"type": "primary"}, "node": "syslog:facility-list.2", "fields": ["PID", "facility", "severity"], "constraints": {"unique": true}}]}, {"name": "syslog:destination.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "pattern-match", "type": "string", "nullable": true}, {"name": "structured-data", "type": "boolean", "nullable": true, "default": false}, {"name": "facility-override", "type": "identity", "enumerate": [{"name": "ietf-syslog:kern", "value": 0, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:kern"}]}, {"name": "ietf-syslog:user", "value": 1, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:user"}]}, {"name": "ietf-syslog:mail", "value": 2, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:mail"}]}, {"name": "ietf-syslog:daemon", "value": 3, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:daemon"}]}, {"name": "ietf-syslog:auth", "value": 4, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:auth"}]}, {"name": "ietf-syslog:syslog", "value": 5, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:syslog"}]}, {"name": "ietf-syslog:lpr", "value": 6, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:lpr"}]}, {"name": "ietf-syslog:news", "value": 7, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:news"}]}, {"name": "ietf-syslog:uucp", "value": 8, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:uucp"}]}, {"name": "ietf-syslog:cron", "value": 9, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:cron"}]}, {"name": "ietf-syslog:authpriv", "value": 10, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:authpriv"}]}, {"name": "ietf-syslog:ftp", "value": 11, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:ftp"}]}, {"name": "ietf-syslog:ntp", "value": 12, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:ntp"}]}, {"name": "ietf-syslog:audit", "value": 13, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:audit"}]}, {"name": "ietf-syslog:console", "value": 14, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:console"}]}, {"name": "ietf-syslog:cron2", "value": 15, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:cron2"}]}, {"name": "ietf-syslog:local0", "value": 16, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local0"}]}, {"name": "ietf-syslog:local1", "value": 17, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local1"}]}, {"name": "ietf-syslog:local2", "value": 18, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local2"}]}, {"name": "ietf-syslog:local3", "value": 19, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local3"}]}, {"name": "ietf-syslog:local4", "value": 20, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local4"}]}, {"name": "ietf-syslog:local5", "value": 21, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local5"}]}, {"name": "ietf-syslog:local6", "value": 22, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local6"}]}, {"name": "ietf-syslog:local7", "value": 23, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local7"}]}], "nullable": true}, {"name": "source-interface", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/root/ietf-interfaces:interfaces/if:interface.1/name"}]}, {"name": "vrf-name", "type": "string", "nullable": true, "length": "1..31"}, {"name": "activated", "type": "boolean", "nullable": true, "default": false}, {"name": "transport", "type": "choice", "fields": [{"name": "udp", "type": "case", "fields": [{"name": "udp", "type": "container", "fields": [{"name": "address", "type": "string", "nullable": true}, {"name": "port", "type": "uint16", "nullable": true, "range": "0..65535", "default": 514}]}]}, {"name": "tls", "type": "case", "fields": [{"name": "tls", "type": "container", "fields": [{"name": "address", "type": "string", "nullable": true}, {"name": "port", "type": "uint16", "nullable": true, "range": "0..65535", "default": 6514}, {"name": "client-identity", "type": "container", "fields": [{"name": "auth-type", "type": "choice", "fields": [{"name": "certificate", "type": "case", "fields": [{"name": "certificate", "type": "container", "fields": []}]}]}]}, {"name": "server-auth", "type": "container", "fields": [{"name": "pinned-ca-certs", "type": "string", "nullable": true}, {"name": "pinned-server-certs", "type": "string", "nullable": true}], "clause": [{"type": "must", "formula": "pinned-ca-certs or pinned-server-certs"}]}, {"name": "hello-params", "type": "container", "fields": [{"name": "tls-versions", "type": "container", "fields": []}, {"name": "cipher-suites", "type": "container", "fields": []}]}]}]}, {"name": "tcp", "type": "case", "fields": [{"name": "tcp", "type": "container", "fields": [{"name": "address", "type": "string", "nullable": true}, {"name": "port", "type": "uint16", "nullable": true, "range": "0..65535", "default": 601}]}]}]}, {"name": "facility-filter", "type": "container", "fields": []}, {"name": "signing", "type": "container", "fields": [{"name": "cert-signers", "type": "container", "fields": [{"name": "cert-initial-repeat", "type": "uint32", "nullable": true, "default": 3}, {"name": "cert-resend-delay", "type": "uint32", "nullable": true, "default": 3600}, {"name": "cert-resend-count", "type": "uint32", "nullable": true, "default": 0}, {"name": "sig-max-delay", "type": "uint32", "nullable": true, "default": 60}, {"name": "sig-number-resends", "type": "uint32", "nullable": true, "default": 0}, {"name": "sig-resend-delay", "type": "uint32", "nullable": true, "default": 5}, {"name": "sig-resend-count", "type": "uint32", "nullable": true, "default": 0}]}]}, {"name": "syslog-filter", "type": "container", "fields": []}], "keys": [{"name": "syslog:destination.1.PK", "index": {"type": "primary"}, "node": "syslog:destination.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "syslog-ext:facility-list.1", "type": "leaf-list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "facility-list", "type": "identity", "enumerate": [{"name": "ietf-syslog:kern", "value": 0, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:kern"}]}, {"name": "ietf-syslog:user", "value": 1, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:user"}]}, {"name": "ietf-syslog:mail", "value": 2, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:mail"}]}, {"name": "ietf-syslog:daemon", "value": 3, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:daemon"}]}, {"name": "ietf-syslog:auth", "value": 4, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:auth"}]}, {"name": "ietf-syslog:syslog", "value": 5, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:syslog"}]}, {"name": "ietf-syslog:lpr", "value": 6, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:lpr"}]}, {"name": "ietf-syslog:news", "value": 7, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:news"}]}, {"name": "ietf-syslog:uucp", "value": 8, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:uucp"}]}, {"name": "ietf-syslog:cron", "value": 9, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:cron"}]}, {"name": "ietf-syslog:authpriv", "value": 10, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:authpriv"}]}, {"name": "ietf-syslog:ftp", "value": 11, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:ftp"}]}, {"name": "ietf-syslog:ntp", "value": 12, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:ntp"}]}, {"name": "ietf-syslog:audit", "value": 13, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:audit"}]}, {"name": "ietf-syslog:console", "value": 14, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:console"}]}, {"name": "ietf-syslog:cron2", "value": 15, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:cron2"}]}, {"name": "ietf-syslog:local0", "value": 16, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local0"}]}, {"name": "ietf-syslog:local1", "value": 17, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local1"}]}, {"name": "ietf-syslog:local2", "value": 18, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local2"}]}, {"name": "ietf-syslog:local3", "value": 19, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local3"}]}, {"name": "ietf-syslog:local4", "value": 20, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local4"}]}, {"name": "ietf-syslog:local5", "value": 21, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local5"}]}, {"name": "ietf-syslog:local6", "value": 22, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local6"}]}, {"name": "ietf-syslog:local7", "value": 23, "derived-paths": [{"derived-path": "ietf-syslog:syslog-facility/ietf-syslog:local7"}]}], "nullable": false}], "keys": [{"name": "syslog-ext:facility-list.1.PK", "index": {"type": "primary"}, "node": "syslog-ext:facility-list.1", "fields": ["PID", "facility-list"], "constraints": {"unique": true}}]}, {"name": "syslog-ext:severity-list.1", "type": "leaf-list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "severity-list", "type": "enum", "enumerate": [{"name": "emergency", "value": 0}, {"name": "alert", "value": 1}, {"name": "critical", "value": 2}, {"name": "error", "value": 3}, {"name": "warning", "value": 4}, {"name": "notice", "value": 5}, {"name": "info", "value": 6}, {"name": "debug", "value": 7}], "nullable": false}], "keys": [{"name": "syslog-ext:severity-list.1.PK", "index": {"type": "primary"}, "node": "syslog-ext:severity-list.1", "fields": ["PID", "severity-list"], "constraints": {"unique": true}}]}, {"name": "syslog:facility-list.3", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "facility", "type": "string", "nullable": false}, {"name": "severity", "type": "string", "nullable": false}, {"name": "advanced-compare", "type": "container", "fields": [{"name": "compare", "type": "enum", "enumerate": [{"name": "equals", "value": 0}, {"name": "equals-or-higher", "value": 1}], "nullable": true, "default": 1}, {"name": "action", "type": "enum", "enumerate": [{"name": "log", "value": 0}, {"name": "block", "value": 1}], "nullable": true, "default": 0}], "clause": [{"type": "when", "formula": "../severity != 'all' and ../severity != 'none'"}]}], "keys": [{"name": "syslog:facility-list.3.PK", "index": {"type": "primary"}, "node": "syslog:facility-list.3", "fields": ["PID", "facility", "severity"], "constraints": {"unique": true}}]}, {"name": "syslog:cert-signer.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "hash-algorithm", "type": "enum", "enumerate": [{"name": "SHA1", "value": 1}, {"name": "SHA256", "value": 2}], "nullable": true}, {"name": "cert", "type": "container", "fields": []}], "keys": [{"name": "syslog:cert-signer.1.PK", "index": {"type": "primary"}, "node": "syslog:cert-signer.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "syslog:cipher-suite.1", "type": "leaf-list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "cipher-suite", "type": "identity", "enumerate": [{"name": "ietf-tls-common:rsa-with-aes-128-cbc-sha", "value": 0, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:rsa-with-aes-128-cbc-sha"}]}, {"name": "ietf-tls-common:rsa-with-aes-256-cbc-sha", "value": 1, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:rsa-with-aes-256-cbc-sha"}]}, {"name": "ietf-tls-common:rsa-with-aes-128-cbc-sha256", "value": 2, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:rsa-with-aes-128-cbc-sha256"}]}, {"name": "ietf-tls-common:rsa-with-aes-256-cbc-sha256", "value": 3, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:rsa-with-aes-256-cbc-sha256"}]}, {"name": "ietf-tls-common:dhe-rsa-with-aes-128-cbc-sha", "value": 4, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:dhe-rsa-with-aes-128-cbc-sha"}]}, {"name": "ietf-tls-common:dhe-rsa-with-aes-256-cbc-sha", "value": 5, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:dhe-rsa-with-aes-256-cbc-sha"}]}, {"name": "ietf-tls-common:dhe-rsa-with-aes-128-cbc-sha256", "value": 6, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:dhe-rsa-with-aes-128-cbc-sha256"}]}, {"name": "ietf-tls-common:dhe-rsa-with-aes-256-cbc-sha256", "value": 7, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:dhe-rsa-with-aes-256-cbc-sha256"}]}, {"name": "ietf-tls-common:ecdhe-ecdsa-with-aes-128-cbc-sha256", "value": 8, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:ecdhe-ecdsa-with-aes-128-cbc-sha256"}]}, {"name": "ietf-tls-common:ecdhe-ecdsa-with-aes-256-cbc-sha384", "value": 9, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:ecdhe-ecdsa-with-aes-256-cbc-sha384"}]}, {"name": "ietf-tls-common:ecdhe-rsa-with-aes-128-cbc-sha256", "value": 10, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:ecdhe-rsa-with-aes-128-cbc-sha256"}]}, {"name": "ietf-tls-common:ecdhe-rsa-with-aes-256-cbc-sha384", "value": 11, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:ecdhe-rsa-with-aes-256-cbc-sha384"}]}, {"name": "ietf-tls-common:ecdhe-ecdsa-with-aes-128-gcm-sha256", "value": 12, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:ecdhe-ecdsa-with-aes-128-gcm-sha256"}]}, {"name": "ietf-tls-common:ecdhe-ecdsa-with-aes-256-gcm-sha384", "value": 13, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:ecdhe-ecdsa-with-aes-256-gcm-sha384"}]}, {"name": "ietf-tls-common:ecdhe-rsa-with-aes-128-gcm-sha256", "value": 14, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:ecdhe-rsa-with-aes-128-gcm-sha256"}]}, {"name": "ietf-tls-common:ecdhe-rsa-with-aes-256-gcm-sha384", "value": 15, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:ecdhe-rsa-with-aes-256-gcm-sha384"}]}, {"name": "ietf-tls-common:rsa-with-3des-ede-cbc-sha", "value": 16, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:rsa-with-3des-ede-cbc-sha"}]}, {"name": "ietf-tls-common:ecdhe-rsa-with-3des-ede-cbc-sha", "value": 17, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:ecdhe-rsa-with-3des-ede-cbc-sha"}]}, {"name": "ietf-tls-common:ecdhe-rsa-with-aes-128-cbc-sha", "value": 18, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:ecdhe-rsa-with-aes-128-cbc-sha"}]}, {"name": "ietf-tls-common:ecdhe-rsa-with-aes-256-cbc-sha", "value": 19, "derived-paths": [{"derived-path": "ietf-tls-common:cipher-suite-base/ietf-tls-common:ecdhe-rsa-with-aes-256-cbc-sha"}]}], "nullable": false}], "keys": [{"name": "syslog:cipher-suite.1.PK", "index": {"type": "primary"}, "node": "syslog:cipher-suite.1", "fields": ["PID", "cipher-suite"], "constraints": {"unique": true}}]}, {"name": "syslog:tls-version.1", "type": "leaf-list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "tls-version", "type": "identity", "enumerate": [{"name": "ietf-tls-common:tls-1.0", "value": 0, "derived-paths": [{"derived-path": "ietf-tls-common:tls-version-base/ietf-tls-common:tls-1.0"}]}, {"name": "ietf-tls-common:tls-1.1", "value": 1, "derived-paths": [{"derived-path": "ietf-tls-common:tls-version-base/ietf-tls-common:tls-1.1"}]}, {"name": "ietf-tls-common:tls-1.2", "value": 2, "derived-paths": [{"derived-path": "ietf-tls-common:tls-version-base/ietf-tls-common:tls-1.2"}]}], "nullable": false}], "keys": [{"name": "syslog:tls-version.1.PK", "index": {"type": "primary"}, "node": "syslog:tls-version.1", "fields": ["PID", "tls-version"], "constraints": {"unique": true}}]}, {"name": "ta:pinned-certificates.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "description", "type": "string", "nullable": true}], "keys": [{"name": "ta:pinned-certificates.1.PK", "index": {"type": "primary"}, "node": "ta:pinned-certificates.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "ta:pinned-certificate.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "cert", "type": "string", "nullable": true}], "keys": [{"name": "ta:pinned-certificate.1.PK", "index": {"type": "primary"}, "node": "ta:pinned-certificate.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "ta:pinned-host-keys.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "description", "type": "string", "nullable": true}], "keys": [{"name": "ta:pinned-host-keys.1.PK", "index": {"type": "primary"}, "node": "ta:pinned-host-keys.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "ta:pinned-host-key.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "host-key", "type": "string", "nullable": false}], "keys": [{"name": "ta:pinned-host-key.1.PK", "index": {"type": "primary"}, "node": "ta:pinned-host-key.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}, {"name": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "type": "list", "np_access": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "labelId", "type": "uint32", "nullable": false}, {"name": "nodeId", "type": "uint16", "nullable": false}, {"name": "propeId", "type": "uint32", "nullable": false}], "keys": [{"node": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "name": "k0", "fields": [":id", ":pid", "labelId", "nodeId", "propeId"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]