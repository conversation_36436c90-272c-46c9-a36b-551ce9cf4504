[{"name": "ietf-hardware_hw:component.1", "source_vertex_label": "ietf-hardware", "dest_vertex_label": "hw:component.1", "source_node_path": "/hardware", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "hw:component.1_hw:uri.1", "source_vertex_label": "hw:component.1", "dest_vertex_label": "hw:uri.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-alarms_al:shelf.1", "source_vertex_label": "ietf-alarms", "dest_vertex_label": "al:shelf.1", "source_node_path": "/alarms/control/alarm-shelving", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "al:shelf.1_al:resource.1", "source_vertex_label": "al:shelf.1", "dest_vertex_label": "al:resource.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "al:shelf.1_al:alarm-type.1", "source_vertex_label": "al:shelf.1", "dest_vertex_label": "al:alarm-type.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-alarms_al:alarm-profile.1", "source_vertex_label": "ietf-alarms", "dest_vertex_label": "al:alarm-profile.1", "source_node_path": "/alarms", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "al:alarm-profile.1_al:severity-level.2", "source_vertex_label": "al:alarm-profile.1", "dest_vertex_label": "al:severity-level.2", "source_node_path": "/alarm-severity-assignment-profile", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-interfaces_if:interface.1", "source_vertex_label": "ietf-interfaces", "dest_vertex_label": "if:interface.1", "source_node_path": "/interfaces", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "an-protection-group_an-pg:protection-group.1", "source_vertex_label": "an-protection-group", "dest_vertex_label": "an-pg:protection-group.1", "source_node_path": "/protection-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "an-pg:protection-group.1_an-pg:member.1", "source_vertex_label": "an-pg:protection-group.1", "dest_vertex_label": "an-pg:member.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-device_dev:agile-controller.1", "source_vertex_label": "huawei-device", "dest_vertex_label": "dev:agile-controller.1", "source_node_path": "/device/agile-controllers", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-device_dev:agile-controller-whitelist.1", "source_vertex_label": "huawei-device", "dest_vertex_label": "dev:agile-controller-whitelist.1", "source_node_path": "/device/agile-controller-whitelists", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-device_dev:item-list.2", "source_vertex_label": "huawei-device", "dest_vertex_label": "dev:item-list.2", "source_node_path": "/config-virtual-license", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-device_dev:alarm-threshold.1", "source_vertex_label": "huawei-device", "dest_vertex_label": "dev:alarm-threshold.1", "source_node_path": "/set-alarm-threshold", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-energy-management-an_hw-energy-mgnt-an:energy-saving-mode.1", "source_vertex_label": "huawei-energy-management-an", "dest_vertex_label": "hw-energy-mgnt-an:energy-saving-mode.1", "source_node_path": "/energy-management-an/energy-saving-modes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "hw-energy-mgnt-an:energy-saving-mode.1_hw-energy-mgnt-an:method.1", "source_vertex_label": "hw-energy-mgnt-an:energy-saving-mode.1", "dest_vertex_label": "hw-energy-mgnt-an:method.1", "source_node_path": "/methods", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "hw-energy-mgnt-an:method.1_hw-energy-mgnt-an:parameter.1", "source_vertex_label": "hw-energy-mgnt-an:method.1", "dest_vertex_label": "hw-energy-mgnt-an:parameter.1", "source_node_path": "/parameters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_ip:address.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "ip:address.1", "source_node_path": "/ipv4", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-routing_rt:control-plane-protocol.2", "source_vertex_label": "ietf-routing", "dest_vertex_label": "rt:control-plane-protocol.2", "source_node_path": "/routing/control-plane-protocols", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-snmp_snmp:listen.1", "source_vertex_label": "ietf-snmp", "dest_vertex_label": "snmp:listen.1", "source_node_path": "/snmp/engine", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-snmp_snmp:target.1", "source_vertex_label": "ietf-snmp", "dest_vertex_label": "snmp:target.1", "source_node_path": "/snmp", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "snmp:target.1_snmp:tag.1", "source_vertex_label": "snmp:target.1", "dest_vertex_label": "snmp:tag.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-snmp_snmp:target-params.1", "source_vertex_label": "ietf-snmp", "dest_vertex_label": "snmp:target-params.1", "source_node_path": "/snmp", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-snmp_snmp:notify.1", "source_vertex_label": "ietf-snmp", "dest_vertex_label": "snmp:notify.1", "source_node_path": "/snmp", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-snmp_snmp:notify-filter-profile.1", "source_vertex_label": "ietf-snmp", "dest_vertex_label": "snmp:notify-filter-profile.1", "source_node_path": "/snmp", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "snmp:notify-filter-profile.1_snmp:include.1", "source_vertex_label": "snmp:notify-filter-profile.1", "dest_vertex_label": "snmp:include.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "snmp:notify-filter-profile.1_snmp:exclude.1", "source_vertex_label": "snmp:notify-filter-profile.1", "dest_vertex_label": "snmp:exclude.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-snmp_snmp:proxy.1", "source_vertex_label": "ietf-snmp", "dest_vertex_label": "snmp:proxy.1", "source_node_path": "/snmp", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-netconf-acm_nacm:group.1", "source_vertex_label": "ietf-netconf-acm", "dest_vertex_label": "nacm:group.1", "source_node_path": "/nacm/groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "nacm:group.1_nacm:user-name.1", "source_vertex_label": "nacm:group.1", "dest_vertex_label": "nacm:user-name.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-netconf-acm_nacm:rule-list.1", "source_vertex_label": "ietf-netconf-acm", "dest_vertex_label": "nacm:rule-list.1", "source_node_path": "/nacm", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "nacm:rule-list.1_nacm:group.2", "source_vertex_label": "nacm:rule-list.1", "dest_vertex_label": "nacm:group.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "nacm:rule-list.1_nacm:rule.1", "source_vertex_label": "nacm:rule-list.1", "dest_vertex_label": "nacm:rule.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-snmp_snmp:community.1", "source_vertex_label": "ietf-snmp", "dest_vertex_label": "snmp:community.1", "source_node_path": "/snmp", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-snmp_snmp:user.1", "source_vertex_label": "ietf-snmp", "dest_vertex_label": "snmp:user.1", "source_node_path": "/snmp/usm/local", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-snmp_snmp:remote.1", "source_vertex_label": "ietf-snmp", "dest_vertex_label": "snmp:remote.1", "source_node_path": "/snmp/usm", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "snmp:remote.1_snmp:user.2", "source_vertex_label": "snmp:remote.1", "dest_vertex_label": "snmp:user.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-snmp_snmp:group.1", "source_vertex_label": "ietf-snmp", "dest_vertex_label": "snmp:group.1", "source_node_path": "/snmp/vacm", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "snmp:group.1_snmp:member.1", "source_vertex_label": "snmp:group.1", "dest_vertex_label": "snmp:member.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "snmp:member.1_snmp:security-model.1", "source_vertex_label": "snmp:member.1", "dest_vertex_label": "snmp:security-model.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "snmp:group.1_snmp:access.1", "source_vertex_label": "snmp:group.1", "dest_vertex_label": "snmp:access.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-snmp_snmp:view.1", "source_vertex_label": "ietf-snmp", "dest_vertex_label": "snmp:view.1", "source_node_path": "/snmp/vacm", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "snmp:view.1_snmp:include.2", "source_vertex_label": "snmp:view.1", "dest_vertex_label": "snmp:include.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "snmp:view.1_snmp:exclude.2", "source_vertex_label": "snmp:view.1", "dest_vertex_label": "snmp:exclude.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-snmp_snmp:cert-to-name.1", "source_vertex_label": "ietf-snmp", "dest_vertex_label": "snmp:cert-to-name.1", "source_node_path": "/snmp/tlstm", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-network-instance_ni:instance.1", "source_vertex_label": "huawei-network-instance", "dest_vertex_label": "ni:instance.1", "source_node_path": "/network-instance/instances", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-protection-group_ps:protection-group.1", "source_vertex_label": "huawei-protection-group", "dest_vertex_label": "ps:protection-group.1", "source_node_path": "/protection-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ps:protection-group.1_ps:member.1", "source_vertex_label": "ps:protection-group.1", "dest_vertex_label": "ps:member.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-resource-pool-an_hw-res-pool-an:resource-pool-esn.1", "source_vertex_label": "huawei-resource-pool-an", "dest_vertex_label": "hw-res-pool-an:resource-pool-esn.1", "source_node_path": "/resource-pool-an/resource-pool-esns", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "hw-res-pool-an:resource-pool-esn.1_hw-res-pool-an:consumption.1", "source_vertex_label": "hw-res-pool-an:resource-pool-esn.1", "dest_vertex_label": "hw-res-pool-an:consumption.1", "source_node_path": "/consumptions", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "hw-res-pool-an:resource-pool-esn.1_hw-res-pool-an:rtu-consumption.1", "source_vertex_label": "hw-res-pool-an:resource-pool-esn.1", "dest_vertex_label": "hw-res-pool-an:rtu-consumption.1", "source_node_path": "/rtu-consumptions", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-resource-pool-an_hw-res-pool-an:resource-pool.1", "source_vertex_label": "huawei-resource-pool-an", "dest_vertex_label": "hw-res-pool-an:resource-pool.1", "source_node_path": "/resource-pool-an/resource-pools", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-system_sys:server.1", "source_vertex_label": "ietf-system", "dest_vertex_label": "sys:server.1", "source_node_path": "/system/ntp", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-system_sys:search.1", "source_vertex_label": "ietf-system", "dest_vertex_label": "sys:search.1", "source_node_path": "/system/dns-resolver", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-system_sys:server.2", "source_vertex_label": "ietf-system", "dest_vertex_label": "sys:server.2", "source_node_path": "/system/dns-resolver", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-system_sys:server.3", "source_vertex_label": "ietf-system", "dest_vertex_label": "sys:server.3", "source_node_path": "/system/radius", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-system_sys:user.1", "source_vertex_label": "ietf-system", "dest_vertex_label": "sys:user.1", "source_node_path": "/system/authentication", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "sys:user.1_sys:authorized-key.1", "source_vertex_label": "sys:user.1", "dest_vertex_label": "sys:authorized-key.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-trust-anchors_ta:pinned-certificates.1", "source_vertex_label": "ietf-trust-anchors", "dest_vertex_label": "ta:pinned-certificates.1", "source_node_path": "/trust-anchors", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ta:pinned-certificates.1_ta:pinned-certificate.1", "source_vertex_label": "ta:pinned-certificates.1", "dest_vertex_label": "ta:pinned-certificate.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-trust-anchors_ta:pinned-host-keys.1", "source_vertex_label": "ietf-trust-anchors", "dest_vertex_label": "ta:pinned-host-keys.1", "source_node_path": "/trust-anchors", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ta:pinned-host-keys.1_ta:pinned-host-key.1", "source_vertex_label": "ta:pinned-host-keys.1", "dest_vertex_label": "ta:pinned-host-key.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-syslog_syslog:facility-list.1", "source_vertex_label": "ietf-syslog", "dest_vertex_label": "syslog:facility-list.1", "source_node_path": "/syslog/actions/console/facility-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-syslog_syslog:log-file.1", "source_vertex_label": "ietf-syslog", "dest_vertex_label": "syslog:log-file.1", "source_node_path": "/syslog/actions/file", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "syslog:log-file.1_syslog:facility-list.2", "source_vertex_label": "syslog:log-file.1", "dest_vertex_label": "syslog:facility-list.2", "source_node_path": "/facility-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-syslog_syslog:destination.1", "source_vertex_label": "ietf-syslog", "dest_vertex_label": "syslog:destination.1", "source_node_path": "/syslog/actions/remote", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "syslog:destination.1_syslog:tls-version.1", "source_vertex_label": "syslog:destination.1", "dest_vertex_label": "syslog:tls-version.1", "source_node_path": "/transport/tls/tls/hello-params/tls-versions", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "syslog:destination.1_syslog:cipher-suite.1", "source_vertex_label": "syslog:destination.1", "dest_vertex_label": "syslog:cipher-suite.1", "source_node_path": "/transport/tls/tls/hello-params/cipher-suites", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "syslog:destination.1_syslog:facility-list.3", "source_vertex_label": "syslog:destination.1", "dest_vertex_label": "syslog:facility-list.3", "source_node_path": "/facility-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "syslog:destination.1_syslog:cert-signer.1", "source_vertex_label": "syslog:destination.1", "dest_vertex_label": "syslog:cert-signer.1", "source_node_path": "/signing/cert-signers", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "syslog:destination.1_syslog-ext:facility-list.1", "source_vertex_label": "syslog:destination.1", "dest_vertex_label": "syslog-ext:facility-list.1", "source_node_path": "/syslog-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "syslog:destination.1_syslog-ext:severity-list.1", "source_vertex_label": "syslog:destination.1", "dest_vertex_label": "syslog-ext:severity-list.1", "source_node_path": "/syslog-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-syslog_syslog-ext:severity-filter.1", "source_vertex_label": "ietf-syslog", "dest_vertex_label": "syslog-ext:severity-filter.1", "source_node_path": "/syslog/actions/global-params/severity-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-syslog_syslog-ext:log.1", "source_vertex_label": "ietf-syslog", "dest_vertex_label": "syslog-ext:log.1", "source_node_path": "/syslog/actions/logs", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-system_system-ext:user-client.1", "source_vertex_label": "ietf-system", "dest_vertex_label": "system-ext:user-client.1", "source_node_path": "/system/authentication/user-clients", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-system_system-ext:network-server-source-interface.1", "source_vertex_label": "ietf-system", "dest_vertex_label": "system-ext:network-server-source-interface.1", "source_node_path": "/system/network-server/network-server-source-interfaces", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-system_system-ext:network-server-source-ip.1", "source_vertex_label": "ietf-system", "dest_vertex_label": "system-ext:network-server-source-ip.1", "source_node_path": "/system/network-server/network-server-source-ips", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-system_system-ext:management-mode.1", "source_vertex_label": "ietf-system", "dest_vertex_label": "system-ext:management-mode.1", "source_node_path": "/system/management-modes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-system_system-ext:auto-save-policy.1", "source_vertex_label": "ietf-system", "dest_vertex_label": "system-ext:auto-save-policy.1", "source_node_path": "/system/auto-save-policys", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_bbf-subif:rule.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "bbf-subif:rule.1", "source_node_path": "/frame-processing/inline-frame-processing/inline-frame-processing/ingress-rule", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-subif:rule.1_bbf-subif-tag:tag.1", "source_vertex_label": "bbf-subif:rule.1", "dest_vertex_label": "bbf-subif-tag:tag.1", "source_node_path": "/flexible-match/match-criteria/vlan-tag-match-type/vlan-tagged", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-subif:rule.1_bbf-subif-tag:push-tag.1", "source_vertex_label": "bbf-subif:rule.1", "dest_vertex_label": "bbf-subif-tag:push-tag.1", "source_node_path": "/ingress-rewrite", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-frame-processing-profiles_bbf-fpprof:frame-processing-profile.1", "source_vertex_label": "bbf-frame-processing-profiles", "dest_vertex_label": "bbf-fpprof:frame-processing-profile.1", "source_node_path": "/frame-processing-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:tag.1", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:tag.1", "source_node_path": "/match-criteria/vlans/vlan-tag-match-type/vlan-tagged", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:destination-mac-address.1", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:destination-mac-address.1", "source_node_path": "/match-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:destination-ipv4-address.1", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:destination-ipv4-address.1", "source_node_path": "/match-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:destination-ipv6-address.1", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:destination-ipv6-address.1", "source_node_path": "/match-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:ethernet-frame-type.1", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:ethernet-frame-type.1", "source_node_path": "/match-criteria", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:protocol.1", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:protocol.1", "source_node_path": "/match-criteria", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:destination-mac-address.2", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:destination-mac-address.2", "source_node_path": "/exclude-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:destination-ipv4-address.2", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:destination-ipv4-address.2", "source_node_path": "/exclude-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:destination-ipv6-address.2", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:destination-ipv6-address.2", "source_node_path": "/exclude-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:ethernet-frame-type.2", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:ethernet-frame-type.2", "source_node_path": "/exclude-criteria", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:protocol.2", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:protocol.2", "source_node_path": "/exclude-criteria", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:copy-from-tags-to-marking-list.1", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:copy-from-tags-to-marking-list.1", "source_node_path": "/ingress-rewrite", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:push-tag.1", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:push-tag.1", "source_node_path": "/ingress-rewrite", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-fpprof:frame-processing-profile.1_bbf-fpprof:push-tag.2", "source_vertex_label": "bbf-fpprof:frame-processing-profile.1", "dest_vertex_label": "bbf-fpprof:push-tag.2", "source_node_path": "/egress-rewrite", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_bbf-if-port-ref:port-layer-if.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "bbf-if-port-ref:port-layer-if.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-forwarding_bbf-l2-fwd:forwarder.1", "source_vertex_label": "bbf-l2-forwarding", "dest_vertex_label": "bbf-l2-fwd:forwarder.1", "source_node_path": "/forwarding/forwarders", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:forwarder.1_bbf-l2-fwd:port.1", "source_vertex_label": "bbf-l2-fwd:forwarder.1", "dest_vertex_label": "bbf-l2-fwd:port.1", "source_node_path": "/ports", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:forwarder.1_bbf-l2-fwd:port-group.1", "source_vertex_label": "bbf-l2-fwd:forwarder.1", "dest_vertex_label": "bbf-l2-fwd:port-group.1", "source_node_path": "/port-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:port-group.1_bbf-l2-fwd:port.2", "source_vertex_label": "bbf-l2-fwd:port-group.1", "dest_vertex_label": "bbf-l2-fwd:port.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-forwarding_bbf-l2-fwd:flooding-policies-profile.1", "source_vertex_label": "bbf-l2-forwarding", "dest_vertex_label": "bbf-l2-fwd:flooding-policies-profile.1", "source_node_path": "/forwarding/flooding-policies-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:flooding-policies-profile.1_bbf-l2-fwd:flooding-policy.1", "source_vertex_label": "bbf-l2-fwd:flooding-policies-profile.1", "dest_vertex_label": "bbf-l2-fwd:flooding-policy.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:flooding-policy.1_bbf-l2-fwd:interface-usages.1", "source_vertex_label": "bbf-l2-fwd:flooding-policy.1", "dest_vertex_label": "bbf-l2-fwd:interface-usages.1", "source_node_path": "/in-interface-usages", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:flooding-policy.1_bbf-l2-fwd:interface-usages.2", "source_vertex_label": "bbf-l2-fwd:flooding-policy.1", "dest_vertex_label": "bbf-l2-fwd:interface-usages.2", "source_node_path": "/frame-forwarding/forward/out-interface-usages", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:forwarder.1_bbf-l2-fwd:flooding-policy.2", "source_vertex_label": "bbf-l2-fwd:forwarder.1", "dest_vertex_label": "bbf-l2-fwd:flooding-policy.2", "source_node_path": "/flooding-policies/flooding-policy-type/forwarder-specific", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-forwarding_bbf-l2-fwd:forwarding-database.1", "source_vertex_label": "bbf-l2-forwarding", "dest_vertex_label": "bbf-l2-fwd:forwarding-database.1", "source_node_path": "/forwarding/forwarding-databases", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:forwarding-database.1_bbf-l2-fwd:static-mac-address.1", "source_vertex_label": "bbf-l2-fwd:forwarding-database.1", "dest_vertex_label": "bbf-l2-fwd:static-mac-address.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-forwarding_bbf-l2-fwd:split-horizon-profile.1", "source_vertex_label": "bbf-l2-forwarding", "dest_vertex_label": "bbf-l2-fwd:split-horizon-profile.1", "source_node_path": "/forwarding/split-horizon-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:split-horizon-profile.1_bbf-l2-fwd:split-horizon.1", "source_vertex_label": "bbf-l2-fwd:split-horizon-profile.1", "dest_vertex_label": "bbf-l2-fwd:split-horizon.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:split-horizon.1_bbf-l2-fwd:out-interface-usage.1", "source_vertex_label": "bbf-l2-fwd:split-horizon.1", "dest_vertex_label": "bbf-l2-fwd:out-interface-usage.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-forwarding_bbf-l2-fwd:mac-learning-control-profile.1", "source_vertex_label": "bbf-l2-forwarding", "dest_vertex_label": "bbf-l2-fwd:mac-learning-control-profile.1", "source_node_path": "/forwarding/mac-learning-control-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:mac-learning-control-profile.1_bbf-l2-fwd:mac-learning-rule.1", "source_vertex_label": "bbf-l2-fwd:mac-learning-control-profile.1", "dest_vertex_label": "bbf-l2-fwd:mac-learning-rule.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-fwd:mac-learning-rule.1_bbf-l2-fwd:mac-can-not-move-to.1", "source_vertex_label": "bbf-l2-fwd:mac-learning-rule.1", "dest_vertex_label": "bbf-l2-fwd:mac-can-not-move-to.1", "source_node_path": "/mac-learning-action/learn-but-do-not-move", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-dhcpv4-relay_bbf-l2-d4r:l2-dhcpv4-relay-profile.1", "source_vertex_label": "bbf-l2-dhcpv4-relay", "dest_vertex_label": "bbf-l2-d4r:l2-dhcpv4-relay-profile.1", "source_node_path": "/l2-dhcpv4-relay-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-d4r:l2-dhcpv4-relay-profile.1_bbf-l2-d4r:suboptions.1", "source_vertex_label": "bbf-l2-d4r:l2-dhcpv4-relay-profile.1", "dest_vertex_label": "bbf-l2-d4r:suboptions.1", "source_node_path": "/option82-format", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-ldra_bbf-ldra:dhcpv6-ldra-profile.1", "source_vertex_label": "bbf-ldra", "dest_vertex_label": "bbf-ldra:dhcpv6-ldra-profile.1", "source_node_path": "/dhcpv6-ldra-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-ldra:dhcpv6-ldra-profile.1_bbf-ldra:option.1", "source_vertex_label": "bbf-ldra:dhcpv6-ldra-profile.1", "dest_vertex_label": "bbf-ldra:option.1", "source_node_path": "/options", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-link-table_bbf-lt:link-table.2", "source_vertex_label": "bbf-link-table", "dest_vertex_label": "bbf-lt:link-table.2", "source_node_path": "/link-table", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-lt:link-table.2_bbf-lt:to-interface.1", "source_vertex_label": "bbf-lt:link-table.2", "dest_vertex_label": "bbf-lt:to-interface.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd_bbf-mgmd:preview-parameters-profile.1", "source_vertex_label": "bbf-mgmd", "dest_vertex_label": "bbf-mgmd:preview-parameters-profile.1", "source_node_path": "/multicast/mgmd", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd_bbf-mgmd:multicast-snoop-transparent-profile.1", "source_vertex_label": "bbf-mgmd", "dest_vertex_label": "bbf-mgmd:multicast-snoop-transparent-profile.1", "source_node_path": "/multicast/mgmd", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd_bbf-mgmd:multicast-snoop-with-proxy-reporting-profile.1", "source_vertex_label": "bbf-mgmd", "dest_vertex_label": "bbf-mgmd:multicast-snoop-with-proxy-reporting-profile.1", "source_node_path": "/multicast/mgmd", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd_bbf-mgmd:multicast-proxy-profile.1", "source_vertex_label": "bbf-mgmd", "dest_vertex_label": "bbf-mgmd:multicast-proxy-profile.1", "source_node_path": "/multicast/mgmd", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd_bbf-mgmd:multicast-vpn.1", "source_vertex_label": "bbf-mgmd", "dest_vertex_label": "bbf-mgmd:multicast-vpn.1", "source_node_path": "/multicast/mgmd", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd:multicast-vpn.1_bbf-mgmd:multicast-interface-to-host.1", "source_vertex_label": "bbf-mgmd:multicast-vpn.1", "dest_vertex_label": "bbf-mgmd:multicast-interface-to-host.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd:multicast-interface-to-host.1_bbf-mgmd:multicast-package.1", "source_vertex_label": "bbf-mgmd:multicast-interface-to-host.1", "dest_vertex_label": "bbf-mgmd:multicast-package.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd:multicast-vpn.1_bbf-mgmd:multicast-network-interface.1", "source_vertex_label": "bbf-mgmd:multicast-vpn.1", "dest_vertex_label": "bbf-mgmd:multicast-network-interface.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd:multicast-vpn.1_bbf-mgmd:multicast-channel.1", "source_vertex_label": "bbf-mgmd:multicast-vpn.1", "dest_vertex_label": "bbf-mgmd:multicast-channel.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd:multicast-channel.1_bbf-mgmd:interface-to-host.1", "source_vertex_label": "bbf-mgmd:multicast-channel.1", "dest_vertex_label": "bbf-mgmd:interface-to-host.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd:multicast-vpn.1_bbf-mgmd:multicast-package.2", "source_vertex_label": "bbf-mgmd:multicast-vpn.1", "dest_vertex_label": "bbf-mgmd:multicast-package.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd:multicast-package.2_bbf-mgmd:multicast-channel-admission-control.1", "source_vertex_label": "bbf-mgmd:multicast-package.2", "dest_vertex_label": "bbf-mgmd:multicast-channel-admission-control.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-pppoe-intermediate-agent_bbf-pppoe-ia:pppoe-profile.1", "source_vertex_label": "bbf-pppoe-intermediate-agent", "dest_vertex_label": "bbf-pppoe-ia:pppoe-profile.1", "source_node_path": "/pppoe-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-pppoe-ia:pppoe-profile.1_bbf-pppoe-ia:subtag.1", "source_vertex_label": "bbf-pppoe-ia:pppoe-profile.1", "dest_vertex_label": "bbf-pppoe-ia:subtag.1", "source_node_path": "/pppoe-vendor-specific-tag", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-pppoe-ia:pppoe-profile.1_bbf-pppoe-ia:padding.1", "source_vertex_label": "bbf-pppoe-ia:pppoe-profile.1", "dest_vertex_label": "bbf-pppoe-ia:padding.1", "source_node_path": "/pppoe-vendor-specific-tag", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-classifiers_bbf-qos-cls:classifier-entry.1", "source_vertex_label": "bbf-qos-classifiers", "dest_vertex_label": "bbf-qos-cls:classifier-entry.1", "source_node_path": "/classifiers", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cls:classifier-entry.1_bbf-qos-cls:classifier-action-entry-cfg.1", "source_vertex_label": "bbf-qos-cls:classifier-entry.1", "dest_vertex_label": "bbf-qos-cls:classifier-action-entry-cfg.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cls:classifier-action-entry-cfg.1_bbf-qos-cls:pbit-marking-list.1", "source_vertex_label": "bbf-qos-cls:classifier-action-entry-cfg.1", "dest_vertex_label": "bbf-qos-cls:pbit-marking-list.1", "source_node_path": "/action-cfg-params/pbit-marking/pbit-marking-cfg", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-composite-filters_bbf-qos-cpsfilt:composite-filter.1", "source_vertex_label": "bbf-qos-composite-filters", "dest_vertex_label": "bbf-qos-cpsfilt:composite-filter.1", "source_node_path": "/composite-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cpsfilt:composite-filter.1_bbf-qos-cpsfilt:filter.1", "source_vertex_label": "bbf-qos-cpsfilt:composite-filter.1", "dest_vertex_label": "bbf-qos-cpsfilt:filter.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cpsfilt:filter.1_bbf-qos-cpsfilt:tag.1", "source_vertex_label": "bbf-qos-cpsfilt:filter.1", "dest_vertex_label": "bbf-qos-cpsfilt:tag.1", "source_node_path": "/filter-method/inline/vlans/vlan-match/match-vlan-tagged", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cpsfilt:filter.1_bbf-qos-cpsfilt:ethernet-frame-type.1", "source_vertex_label": "bbf-qos-cpsfilt:filter.1", "dest_vertex_label": "bbf-qos-cpsfilt:ethernet-frame-type.1", "source_node_path": "/filter-method/inline", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cpsfilt:filter.1_bbf-qos-cpsfilt:protocol.1", "source_vertex_label": "bbf-qos-cpsfilt:filter.1", "dest_vertex_label": "bbf-qos-cpsfilt:protocol.1", "source_node_path": "/filter-method/inline", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cpsfilt:filter.1_bbf-qos-cpsfilt:pbit-marking-list.1", "source_vertex_label": "bbf-qos-cpsfilt:filter.1", "dest_vertex_label": "bbf-qos-cpsfilt:pbit-marking-list.1", "source_node_path": "/filter-method/inline", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cpsfilt:pbit-marking-list.1_bbf-qos-cpsfilt:pbit-value.1", "source_vertex_label": "bbf-qos-cpsfilt:pbit-marking-list.1", "dest_vertex_label": "bbf-qos-cpsfilt:pbit-value.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cpsfilt:filter.1_bbf-qos-cpsfilt:dei-marking-list.1", "source_vertex_label": "bbf-qos-cpsfilt:filter.1", "dest_vertex_label": "bbf-qos-cpsfilt:dei-marking-list.1", "source_node_path": "/filter-method/inline", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cpsfilt:filter.1_bbf-qos-cpsfilt:flow-color.1", "source_vertex_label": "bbf-qos-cpsfilt:filter.1", "dest_vertex_label": "bbf-qos-cpsfilt:flow-color.1", "source_node_path": "/filter-method/inline", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cls:classifier-entry.1_bbf-qos-cpsfilt:pbit-marking-list.2", "source_vertex_label": "bbf-qos-cls:classifier-entry.1", "dest_vertex_label": "bbf-qos-cpsfilt:pbit-marking-list.2", "source_node_path": "/filter-method/enhanced-classifier", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-cpsfilt:pbit-marking-list.2_bbf-qos-cpsfilt:pbit-value.2", "source_vertex_label": "bbf-qos-cpsfilt:pbit-marking-list.2", "dest_vertex_label": "bbf-qos-cpsfilt:pbit-value.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-traffic-mngt_bbf-qos-tm:tc-id-2-queue-id-mapping-profile.1", "source_vertex_label": "bbf-qos-traffic-mngt", "dest_vertex_label": "bbf-qos-tm:tc-id-2-queue-id-mapping-profile.1", "source_node_path": "/tm-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-tm:tc-id-2-queue-id-mapping-profile.1_bbf-qos-tm:mapping-entry.1", "source_vertex_label": "bbf-qos-tm:tc-id-2-queue-id-mapping-profile.1", "dest_vertex_label": "bbf-qos-tm:mapping-entry.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-traffic-mngt_bbf-qos-tm:bac-entry.1", "source_vertex_label": "bbf-qos-traffic-mngt", "dest_vertex_label": "bbf-qos-tm:bac-entry.1", "source_node_path": "/tm-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_bbf-qos-tm:queue.2", "source_vertex_label": "if:interface.1", "dest_vertex_label": "bbf-qos-tm:queue.2", "source_node_path": "/tm-root/children-type/queues", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-traffic-mngt_bbf-qos-shap:shaper-profile.1", "source_vertex_label": "bbf-qos-traffic-mngt", "dest_vertex_label": "bbf-qos-shap:shaper-profile.1", "source_node_path": "/tm-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-filters_bbf-qos-filt:filter.1", "source_vertex_label": "bbf-qos-filters", "dest_vertex_label": "bbf-qos-filt:filter.1", "source_node_path": "/filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-policies_bbf-qos-pol:policy.1", "source_vertex_label": "bbf-qos-policies", "dest_vertex_label": "bbf-qos-pol:policy.1", "source_node_path": "/policies", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pol:policy.1_bbf-qos-pol:classifiers.1", "source_vertex_label": "bbf-qos-pol:policy.1", "dest_vertex_label": "bbf-qos-pol:classifiers.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-policies_bbf-qos-pol:policy-profile.1", "source_vertex_label": "bbf-qos-policies", "dest_vertex_label": "bbf-qos-pol:policy-profile.1", "source_node_path": "/qos-policy-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pol:policy-profile.1_bbf-qos-pol:policy-list.1", "source_vertex_label": "bbf-qos-pol:policy-profile.1", "dest_vertex_label": "bbf-qos-pol:policy-list.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-policing_bbf-qos-plc:policing-profile.1", "source_vertex_label": "bbf-qos-policing", "dest_vertex_label": "bbf-qos-plc:policing-profile.1", "source_node_path": "/policing-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-subscriber-profiles_bbf-subprof:subscriber-profile.1", "source_vertex_label": "bbf-subscriber-profiles", "dest_vertex_label": "bbf-subprof:subscriber-profile.1", "source_node_path": "/subscriber-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vlan-sub-interface-profiles_bbf-vsi-prof:vsi-profile.1", "source_vertex_label": "bbf-vlan-sub-interface-profiles", "dest_vertex_label": "bbf-vsi-prof:vsi-profile.1", "source_node_path": "/vsi-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof:vsi-profile.1_bbf-vsi-prof-fp:rule.1", "source_vertex_label": "bbf-vsi-prof:vsi-profile.1", "dest_vertex_label": "bbf-vsi-prof-fp:rule.1", "source_node_path": "/frame-processing/inline-multiple-rules-with-parameters/frame-processing-rules/ingress-rule", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:destination-mac-address.1", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:destination-mac-address.1", "source_node_path": "/match-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:destination-ipv4-address.1", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:destination-ipv4-address.1", "source_node_path": "/match-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:destination-ipv6-address.1", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:destination-ipv6-address.1", "source_node_path": "/match-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:tag.1", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:tag.1", "source_node_path": "/match-criteria/vlans/vlan-tag-match-type/vlan-tagged", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:ethernet-frame-type.1", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:ethernet-frame-type.1", "source_node_path": "/match-criteria", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:protocol.1", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:protocol.1", "source_node_path": "/match-criteria", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:destination-mac-address.2", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:destination-mac-address.2", "source_node_path": "/exclude-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:destination-ipv4-address.2", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:destination-ipv4-address.2", "source_node_path": "/exclude-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:destination-ipv6-address.2", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:destination-ipv6-address.2", "source_node_path": "/exclude-criteria/frame-destination-filter", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:ethernet-frame-type.2", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:ethernet-frame-type.2", "source_node_path": "/exclude-criteria", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:protocol.2", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:protocol.2", "source_node_path": "/exclude-criteria", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof-fp:rule.1_bbf-vsi-prof-fp:push-tag.1", "source_vertex_label": "bbf-vsi-prof-fp:rule.1", "dest_vertex_label": "bbf-vsi-prof-fp:push-tag.1", "source_node_path": "/ingress-rewrite", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-vsi-prof:vsi-profile.1_bbf-vsi-prof-fp:push-tag.2", "source_vertex_label": "bbf-vsi-prof:vsi-profile.1", "dest_vertex_label": "bbf-vsi-prof-fp:push-tag.2", "source_node_path": "/frame-processing/inline-multiple-rules-with-parameters/frame-processing-rules/egress-rewrite", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xpon_bbf-xpon:proxy.1", "source_vertex_label": "bbf-xpon", "dest_vertex_label": "bbf-xpon:proxy.1", "source_node_path": "/xpon/ictp/all-ictp-proxies-all-channel-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xpon:proxy.1_bbf-xpon:channel-termination.1", "source_vertex_label": "bbf-xpon:proxy.1", "dest_vertex_label": "bbf-xpon:channel-termination.1", "source_node_path": "/all-channel-terminations-proxied-by-this-proxy", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xpon_bbf-xpon:wavelength-profile.1", "source_vertex_label": "bbf-xpon", "dest_vertex_label": "bbf-xpon:wavelength-profile.1", "source_node_path": "/xpon/wavelength-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xpon_bbf-xpon:multicast-gemport.1", "source_vertex_label": "bbf-xpon", "dest_vertex_label": "bbf-xpon:multicast-gemport.1", "source_node_path": "/xpon/multicast-gemports", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xponani-power-management_bbf-xponani-pwr:power-management-profile.1", "source_vertex_label": "bbf-xponani-power-management", "dest_vertex_label": "bbf-xponani-pwr:power-management-profile.1", "source_node_path": "/xponani-power-management-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xponani-pwr:power-management-profile.1_bbf-xponani-pwr:mode.1", "source_vertex_label": "bbf-xponani-pwr:power-management-profile.1", "dest_vertex_label": "bbf-xponani-pwr:mode.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xpongemtcont_bbf-xpongemtcont:traffic-descriptor-profile.1", "source_vertex_label": "bbf-xpongemtcont", "dest_vertex_label": "bbf-xpongemtcont:traffic-descriptor-profile.1", "source_node_path": "/xpongemtcont/traffic-descriptor-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xpongemtcont_bbf-xpongemtcont:tcont.2", "source_vertex_label": "bbf-xpongemtcont", "dest_vertex_label": "bbf-xpongemtcont:tcont.2", "source_node_path": "/xpongemtcont/tconts", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xpongemtcont_bbf-xpongemtcont:gemport.2", "source_vertex_label": "bbf-xpongemtcont", "dest_vertex_label": "bbf-xpongemtcont:gemport.2", "source_node_path": "/xpongemtcont/gemports", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rt:control-plane-protocol.2_v4ur:route.1", "source_vertex_label": "rt:control-plane-protocol.2", "dest_vertex_label": "v4ur:route.1", "source_node_path": "/static-routes/ipv4", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "v4ur:route.1_v4ur:next-hop.2", "source_vertex_label": "v4ur:route.1", "dest_vertex_label": "v4ur:next-hop.2", "source_node_path": "/next-hop/next-hop-options/next-hop-list/next-hop-list", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ieee802-dot1q-bridge_dot1q:bridge.1", "source_vertex_label": "ieee802-dot1q-bridge", "dest_vertex_label": "dot1q:bridge.1", "source_node_path": "/bridges", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:bridge.1_dot1q:component.1", "source_vertex_label": "dot1q:bridge.1", "dest_vertex_label": "dot1q:component.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:filtering-entry.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:filtering-entry.1", "source_node_path": "/filtering-database", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:filtering-entry.1_dot1q:port-map.1", "source_vertex_label": "dot1q:filtering-entry.1", "dest_vertex_label": "dot1q:port-map.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:vlan-registration-entry.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:vlan-registration-entry.1", "source_node_path": "/filtering-database", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:vlan-registration-entry.1_dot1q:port-map.2", "source_vertex_label": "dot1q:vlan-registration-entry.1", "dest_vertex_label": "dot1q:port-map.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:filtering-entry.2", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:filtering-entry.2", "source_node_path": "/permanent-database", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:filtering-entry.2_dot1q:port-map.3", "source_vertex_label": "dot1q:filtering-entry.2", "dest_vertex_label": "dot1q:port-map.3", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:vlan.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:vlan.1", "source_node_path": "/bridge-vlan", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:protocol-group-database.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:protocol-group-database.1", "source_node_path": "/bridge-vlan", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:vid-to-fid-allocation.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:vid-to-fid-allocation.1", "source_node_path": "/bridge-vlan", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:fid-to-vid-allocation.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:fid-to-vid-allocation.1", "source_node_path": "/bridge-vlan", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:vid-to-fid.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:vid-to-fid.1", "source_node_path": "/bridge-vlan", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:mstid.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:mstid.1", "source_node_path": "/bridge-mst", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:fid-to-mstid.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:fid-to-mstid.1", "source_node_path": "/bridge-mst", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:component.1_dot1q:fid-to-mstid-allocation.1", "source_vertex_label": "dot1q:component.1", "dest_vertex_label": "dot1q:fid-to-mstid-allocation.1", "source_node_path": "/bridge-mst", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_dot1q:pcp-decoding-map.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "dot1q:pcp-decoding-map.1", "source_node_path": "/bridge-port/pcp-decoding-table", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:pcp-decoding-map.1_dot1q:priority-map.1", "source_vertex_label": "dot1q:pcp-decoding-map.1", "dest_vertex_label": "dot1q:priority-map.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_dot1q:pcp-encoding-map.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "dot1q:pcp-encoding-map.1", "source_node_path": "/bridge-port/pcp-encoding-table", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:pcp-encoding-map.1_dot1q:priority-map.2", "source_vertex_label": "dot1q:pcp-encoding-map.1", "dest_vertex_label": "dot1q:priority-map.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_dot1q:traffic-class-map.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "dot1q:traffic-class-map.1", "source_node_path": "/bridge-port/traffic-class", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:traffic-class-map.1_dot1q:available-traffic-class.1", "source_vertex_label": "dot1q:traffic-class-map.1", "dest_vertex_label": "dot1q:available-traffic-class.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_dot1q:protocol-group-vid-set.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "dot1q:protocol-group-vid-set.1", "source_node_path": "/bridge-port", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "dot1q:protocol-group-vid-set.1_dot1q:vid.2", "source_vertex_label": "dot1q:protocol-group-vid-set.1", "dest_vertex_label": "dot1q:vid.2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_dot1q:vid-translations.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "dot1q:vid-translations.1", "source_node_path": "/bridge-port", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_dot1q:egress-vid-translations.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "dot1q:egress-vid-translations.1", "source_node_path": "/bridge-port", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ieee802-dot1ab-lldp_lldp:port.1", "source_vertex_label": "ieee802-dot1ab-lldp", "dest_vertex_label": "lldp:port.1", "source_node_path": "/lldp", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ieee802-dot1ax_dot1ax:aggregating-system.1", "source_vertex_label": "ieee802-dot1ax", "dest_vertex_label": "dot1ax:aggregating-system.1", "source_node_path": "/lag-system", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-alarms_x733:x733-mapping.1", "source_vertex_label": "ietf-alarms", "dest_vertex_label": "x733:x733-mapping.1", "source_node_path": "/alarms/control", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-network-instance_ni:network-instance.2", "source_vertex_label": "huawei-network-instance", "dest_vertex_label": "ni:network-instance.2", "source_node_path": "/network-instances", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-subscribed-notifications_sn:stream.1", "source_vertex_label": "ietf-subscribed-notifications", "dest_vertex_label": "sn:stream.1", "source_node_path": "/streams", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-subscribed-notifications_sn:stream-filter.1", "source_vertex_label": "ietf-subscribed-notifications", "dest_vertex_label": "sn:stream-filter.1", "source_node_path": "/filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-subscribed-notifications_sn:subscription.1", "source_vertex_label": "ietf-subscribed-notifications", "dest_vertex_label": "sn:subscription.1", "source_node_path": "/subscriptions", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "sn:subscription.1_sn:receiver.1", "source_vertex_label": "sn:subscription.1", "dest_vertex_label": "sn:receiver.1", "source_node_path": "/receivers", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-subscribed-notifications_yp:selection-filter.1", "source_vertex_label": "ietf-subscribed-notifications", "dest_vertex_label": "yp:selection-filter.1", "source_node_path": "/filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "sn:subscription.1_yp:excluded-change.1", "source_vertex_label": "sn:subscription.1", "dest_vertex_label": "yp:excluded-change.1", "source_node_path": "/update-trigger/on-change/on-change", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-keystore_ks:asymmetric-key.1", "source_vertex_label": "ietf-keystore", "dest_vertex_label": "ks:asymmetric-key.1", "source_node_path": "/keystore/asymmetric-keys", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ks:asymmetric-key.1_ks:certificate.1", "source_vertex_label": "ks:asymmetric-key.1", "dest_vertex_label": "ks:certificate.1", "source_node_path": "/certificates", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-subscribed-notifications_snr:receiver-instance.1", "source_vertex_label": "ietf-subscribed-notifications", "dest_vertex_label": "snr:receiver-instance.1", "source_node_path": "/subscriptions/receiver-instances", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "sn:subscription.1_ypr:module-version-config.1", "source_vertex_label": "sn:subscription.1", "dest_vertex_label": "ypr:module-version-config.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "openconfig-telemetry_oc-telemetry:sensor-group.1", "source_vertex_label": "openconfig-telemetry", "dest_vertex_label": "oc-telemetry:sensor-group.1", "source_node_path": "/telemetry-system/sensor-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "oc-telemetry:sensor-group.1_oc-telemetry:sensor-path.1", "source_vertex_label": "oc-telemetry:sensor-group.1", "dest_vertex_label": "oc-telemetry:sensor-path.1", "source_node_path": "/sensor-paths", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "openconfig-telemetry_oc-telemetry:destination-group.1", "source_vertex_label": "openconfig-telemetry", "dest_vertex_label": "oc-telemetry:destination-group.1", "source_node_path": "/telemetry-system/destination-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "oc-telemetry:destination-group.1_oc-telemetry:destination.1", "source_vertex_label": "oc-telemetry:destination-group.1", "dest_vertex_label": "oc-telemetry:destination.1", "source_node_path": "/destinations", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "openconfig-telemetry_oc-telemetry:subscription.1", "source_vertex_label": "openconfig-telemetry", "dest_vertex_label": "oc-telemetry:subscription.1", "source_node_path": "/telemetry-system/subscriptions/persistent", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "oc-telemetry:subscription.1_oc-telemetry:sensor-profile.1", "source_vertex_label": "oc-telemetry:subscription.1", "dest_vertex_label": "oc-telemetry:sensor-profile.1", "source_node_path": "/sensor-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "oc-telemetry:subscription.1_oc-telemetry:destination-group.2", "source_vertex_label": "oc-telemetry:subscription.1", "dest_vertex_label": "oc-telemetry:destination-group.2", "source_node_path": "/destination-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "an-protection-group_an-pon-pg:local-node-ports.1", "source_vertex_label": "an-protection-group", "dest_vertex_label": "an-pon-pg:local-node-ports.1", "source_node_path": "/protection-groups/dual-parenting-sync", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "an-protection-group_an-pon-pg:dual-parenting-peer-node.1", "source_vertex_label": "an-protection-group", "dest_vertex_label": "an-pon-pg:dual-parenting-peer-node.1", "source_node_path": "/protection-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xpon_bbf-ani-profile:ani-profile.1", "source_vertex_label": "bbf-xpon", "dest_vertex_label": "bbf-ani-profile:ani-profile.1", "source_node_path": "/xpon/ani-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-ani-profile:ani-profile.1_bbf-ani-profile:tcont.1", "source_vertex_label": "bbf-ani-profile:ani-profile.1", "dest_vertex_label": "bbf-ani-profile:tcont.1", "source_node_path": "/tconts", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-ani-profile:ani-profile.1_bbf-ani-profile:gemport.1", "source_vertex_label": "bbf-ani-profile:ani-profile.1", "dest_vertex_label": "bbf-ani-profile:gemport.1", "source_node_path": "/gemports", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-ani-profile:gemport.1_bbf-ani-profile:rule.1", "source_vertex_label": "bbf-ani-profile:gemport.1", "dest_vertex_label": "bbf-ani-profile:rule.1", "source_node_path": "/frame-processing", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-ani-profile:ani-profile.1_bbf-ani-profile:gem-bundle.1", "source_vertex_label": "bbf-ani-profile:ani-profile.1", "dest_vertex_label": "bbf-ani-profile:gem-bundle.1", "source_node_path": "/gem-bundles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-ani-profile:gem-bundle.1_bbf-ani-profile:slave-gem.1", "source_vertex_label": "bbf-ani-profile:gem-bundle.1", "dest_vertex_label": "bbf-ani-profile:slave-gem.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-ani-profile:ani-profile.1_bbf-ani-profile:port-bundle.1", "source_vertex_label": "bbf-ani-profile:ani-profile.1", "dest_vertex_label": "bbf-ani-profile:port-bundle.1", "source_node_path": "/port-bundles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-ani-profile:port-bundle.1_bbf-ani-profile:port-list.1", "source_vertex_label": "bbf-ani-profile:port-bundle.1", "dest_vertex_label": "bbf-ani-profile:port-list.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-ani-profile:ani-profile.1_bbf-ani-profile:link-aggregation.1", "source_vertex_label": "bbf-ani-profile:ani-profile.1", "dest_vertex_label": "bbf-ani-profile:link-aggregation.1", "source_node_path": "/link-aggregations", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-ani-profile:link-aggregation.1_bbf-ani-profile:slave-port.1", "source_vertex_label": "bbf-ani-profile:link-aggregation.1", "dest_vertex_label": "bbf-ani-profile:slave-port.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xpon_bbf-uni-profile:uni-profile.1", "source_vertex_label": "bbf-xpon", "dest_vertex_label": "bbf-uni-profile:uni-profile.1", "source_node_path": "/xpon/uni-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-uni-profile:uni-profile.1_bbf-uni-profile:packet-forward.1", "source_vertex_label": "bbf-uni-profile:uni-profile.1", "dest_vertex_label": "bbf-uni-profile:packet-forward.1", "source_node_path": "/packet-forwards", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-uni-profile:uni-profile.1_bbf-uni-profile:tpid.1", "source_vertex_label": "bbf-uni-profile:uni-profile.1", "dest_vertex_label": "bbf-uni-profile:tpid.1", "source_node_path": "/tpids", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-uni-profile:uni-profile.1_bbf-uni-profile:ont-port.1", "source_vertex_label": "bbf-uni-profile:uni-profile.1", "dest_vertex_label": "bbf-uni-profile:ont-port.1", "source_node_path": "/ont-ports", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-uni-profile:ont-port.1_bbf-uni-profile:rule.1", "source_vertex_label": "bbf-uni-profile:ont-port.1", "dest_vertex_label": "bbf-uni-profile:rule.1", "source_node_path": "/frame-processing", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xpon_onu-power-shedding-profile:onu-power-shedding-profile.1", "source_vertex_label": "bbf-xpon", "dest_vertex_label": "onu-power-shedding-profile:onu-power-shedding-profile.1", "source_node_path": "/xpon/onu-power-shedding-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xpon_onu-tr069-profile:onu-tr069-server-profile.1", "source_vertex_label": "bbf-xpon", "dest_vertex_label": "onu-tr069-profile:onu-tr069-server-profile.1", "source_node_path": "/xpon/onu-tr069-server-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xpon_traffic-alarm-profile:traffic-alarm-profile.1", "source_vertex_label": "bbf-xpon", "dest_vertex_label": "traffic-alarm-profile:traffic-alarm-profile.1", "source_node_path": "/xpon/traffic-alarm-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-ifm_ifm:auto-recovery-time.1", "source_vertex_label": "huawei-ifm", "dest_vertex_label": "ifm:auto-recovery-time.1", "source_node_path": "/ifm/auto-recovery-times", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-ifm_ifm:interface.1", "source_vertex_label": "huawei-ifm", "dest_vertex_label": "ifm:interface.1", "source_node_path": "/ifm/interfaces", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-ifm_ifm:if-group-policy.1", "source_vertex_label": "huawei-ifm", "dest_vertex_label": "ifm:if-group-policy.1", "source_node_path": "/ifm/if-group-policys", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-tunnel-management_tnlm:tunnel-policy.1", "source_vertex_label": "huawei-tunnel-management", "dest_vertex_label": "tnlm:tunnel-policy.1", "source_node_path": "/tunnel-management/tunnel-policys", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "tnlm:tunnel-policy.1_tnlm:select-tunnel-type.1", "source_vertex_label": "tnlm:tunnel-policy.1", "dest_vertex_label": "tnlm:select-tunnel-type.1", "source_node_path": "/ipv4-set/policy-type/select-sequences/select-sequence/select-tunnel-types", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "tnlm:tunnel-policy.1_tnlm:nexthop.1", "source_vertex_label": "tnlm:tunnel-policy.1", "dest_vertex_label": "tnlm:nexthop.1", "source_node_path": "/ipv4-set/policy-type/binding/nexthops", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "tnlm:nexthop.1_tnlm:tunnel-name.1", "source_vertex_label": "tnlm:nexthop.1", "dest_vertex_label": "tnlm:tunnel-name.1", "source_node_path": "/tunnel-names", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "tnlm:nexthop.1_tnlm:auto-name.1", "source_vertex_label": "tnlm:nexthop.1", "dest_vertex_label": "tnlm:auto-name.1", "source_node_path": "/auto-names", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "tnlm:tunnel-policy.1_tnlm:select-tunnel-type.2", "source_vertex_label": "tnlm:tunnel-policy.1", "dest_vertex_label": "tnlm:select-tunnel-type.2", "source_node_path": "/ipv6-set/policy-type/select-sequences/select-sequence/select-tunnel-types", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:ext-community-soo-filter.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:ext-community-soo-filter.1", "source_node_path": "/routing-policy/ext-community-soo-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:ext-community-soo-filter.1_rtp:basic-node.1", "source_vertex_label": "rtp:ext-community-soo-filter.1", "dest_vertex_label": "rtp:basic-node.1", "source_node_path": "/basic-nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:basic-node.1_rtp:community-member.1", "source_vertex_label": "rtp:basic-node.1", "dest_vertex_label": "rtp:community-member.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:ext-community-soo-filter.1_rtp:advanced-node.1", "source_vertex_label": "rtp:ext-community-soo-filter.1", "dest_vertex_label": "rtp:advanced-node.1", "source_node_path": "/advanced-nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:community-filter.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:community-filter.1", "source_node_path": "/routing-policy/community-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:community-filter.1_rtp:basic-node.2", "source_vertex_label": "rtp:community-filter.1", "dest_vertex_label": "rtp:basic-node.2", "source_node_path": "/basic-nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:basic-node.2_rtp:community-member.2", "source_vertex_label": "rtp:basic-node.2", "dest_vertex_label": "rtp:community-member.2", "source_node_path": "/community-members", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:community-filter.1_rtp:advanced-node.2", "source_vertex_label": "rtp:community-filter.1", "dest_vertex_label": "rtp:advanced-node.2", "source_node_path": "/advanced-nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:ext-community-filter.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:ext-community-filter.1", "source_node_path": "/routing-policy/ext-community-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:ext-community-filter.1_rtp:basic-node.3", "source_vertex_label": "rtp:ext-community-filter.1", "dest_vertex_label": "rtp:basic-node.3", "source_node_path": "/basic-nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:basic-node.3_rtp:ext-community-member.1", "source_vertex_label": "rtp:basic-node.3", "dest_vertex_label": "rtp:ext-community-member.1", "source_node_path": "/ext-community-members", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:ext-community-filter.1_rtp:advanced-node.3", "source_vertex_label": "rtp:ext-community-filter.1", "dest_vertex_label": "rtp:advanced-node.3", "source_node_path": "/advanced-nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:ipv4-prefix-filter.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:ipv4-prefix-filter.1", "source_node_path": "/routing-policy/ipv4-prefix-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:ipv4-prefix-filter.1_rtp:node.1", "source_vertex_label": "rtp:ipv4-prefix-filter.1", "dest_vertex_label": "rtp:node.1", "source_node_path": "/nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:ipv6-prefix-filter.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:ipv6-prefix-filter.1", "source_node_path": "/routing-policy/ipv6-prefix-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:ipv6-prefix-filter.1_rtp:node.2", "source_vertex_label": "rtp:ipv6-prefix-filter.1", "dest_vertex_label": "rtp:node.2", "source_node_path": "/nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:ext-community-segmented-nexthop-filter.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:ext-community-segmented-nexthop-filter.1", "source_node_path": "/routing-policy/ext-community-segmented-nexthop-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:ext-community-segmented-nexthop-filter.1_rtp:basic-node.4", "source_vertex_label": "rtp:ext-community-segmented-nexthop-filter.1", "dest_vertex_label": "rtp:basic-node.4", "source_node_path": "/basic-nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:basic-node.4_rtp:ext-community-segmented-nexthop-member.1", "source_vertex_label": "rtp:basic-node.4", "dest_vertex_label": "rtp:ext-community-segmented-nexthop-member.1", "source_node_path": "/ext-community-segmented-nexthop-members", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:ext-community-segmented-nexthop-filter.1_rtp:advanced-node.4", "source_vertex_label": "rtp:ext-community-segmented-nexthop-filter.1", "dest_vertex_label": "rtp:advanced-node.4", "source_node_path": "/advanced-nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:as-path-filter.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:as-path-filter.1", "source_node_path": "/routing-policy/as-path-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:as-path-filter.1_rtp:node.3", "source_vertex_label": "rtp:as-path-filter.1", "dest_vertex_label": "rtp:node.3", "source_node_path": "/nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:community-list.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:community-list.1", "source_node_path": "/routing-policy/community-lists", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:community-list.1_rtp:community-member.3", "source_vertex_label": "rtp:community-list.1", "dest_vertex_label": "rtp:community-member.3", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:community-list.1_rtp:community-attribute.1", "source_vertex_label": "rtp:community-list.1", "dest_vertex_label": "rtp:community-attribute.1", "source_node_path": "/community-attributes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:rd-filter.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:rd-filter.1", "source_node_path": "/routing-policy/rd-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:rd-filter.1_rtp:node.4", "source_vertex_label": "rtp:rd-filter.1", "dest_vertex_label": "rtp:node.4", "source_node_path": "/nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:node.4_rtp:rd-string.1", "source_vertex_label": "rtp:node.4", "dest_vertex_label": "rtp:rd-string.1", "source_node_path": "/rd-strings", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:vni-filter.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:vni-filter.1", "source_node_path": "/routing-policy/vni-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:vni-filter.1_rtp:vni-member.1", "source_vertex_label": "rtp:vni-filter.1", "dest_vertex_label": "rtp:vni-member.1", "source_node_path": "/vni-members", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:mac-filter.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:mac-filter.1", "source_node_path": "/routing-policy/mac-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:mac-filter.1_rtp:mac-member.1", "source_vertex_label": "rtp:mac-filter.1", "dest_vertex_label": "rtp:mac-member.1", "source_node_path": "/mac-members", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:eth-tag-filter.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:eth-tag-filter.1", "source_node_path": "/routing-policy/eth-tag-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:eth-tag-filter.1_rtp:eth-tag-member.1", "source_vertex_label": "rtp:eth-tag-filter.1", "dest_vertex_label": "rtp:eth-tag-member.1", "source_node_path": "/eth-tag-members", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:ipv4-prefix-list.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:ipv4-prefix-list.1", "source_node_path": "/routing-policy/ipv4-prefix-lists", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:ipv4-prefix-list.1_rtp:prefix-member.1", "source_vertex_label": "rtp:ipv4-prefix-list.1", "dest_vertex_label": "rtp:prefix-member.1", "source_node_path": "/prefix-members", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:ipv6-prefix-list.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:ipv6-prefix-list.1", "source_node_path": "/routing-policy/ipv6-prefix-lists", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:ipv6-prefix-list.1_rtp:prefix-member.2", "source_vertex_label": "rtp:ipv6-prefix-list.1", "dest_vertex_label": "rtp:prefix-member.2", "source_node_path": "/prefix-members", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:large-community-filter.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:large-community-filter.1", "source_node_path": "/routing-policy/large-community-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:large-community-filter.1_rtp:basic-node.5", "source_vertex_label": "rtp:large-community-filter.1", "dest_vertex_label": "rtp:basic-node.5", "source_node_path": "/basic-nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:basic-node.5_rtp:large-community-member.1", "source_vertex_label": "rtp:basic-node.5", "dest_vertex_label": "rtp:large-community-member.1", "source_node_path": "/large-community-members", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:large-community-filter.1_rtp:advanced-node.5", "source_vertex_label": "rtp:large-community-filter.1", "dest_vertex_label": "rtp:advanced-node.5", "source_node_path": "/advanced-nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:large-community-list.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:large-community-list.1", "source_node_path": "/routing-policy/large-community-lists", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:large-community-list.1_rtp:large-community-member.2", "source_vertex_label": "rtp:large-community-list.1", "dest_vertex_label": "rtp:large-community-member.2", "source_node_path": "/large-community-members", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:ext-community-priority-color-filter.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:ext-community-priority-color-filter.1", "source_node_path": "/routing-policy/ext-community-priority-color-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:ext-community-priority-color-filter.1_rtp:basic-node.6", "source_vertex_label": "rtp:ext-community-priority-color-filter.1", "dest_vertex_label": "rtp:basic-node.6", "source_node_path": "/filter-type/basic/basic-nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:basic-node.6_rtp:community-member.4", "source_vertex_label": "rtp:basic-node.6", "dest_vertex_label": "rtp:community-member.4", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:ext-community-priority-color-filter.1_rtp:advanced-node.6", "source_vertex_label": "rtp:ext-community-priority-color-filter.1", "dest_vertex_label": "rtp:advanced-node.6", "source_node_path": "/filter-type/advanced/advanced-nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:ext-community-bandwidth-filter.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:ext-community-bandwidth-filter.1", "source_node_path": "/routing-policy/ext-community-bandwidth-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:ext-community-bandwidth-filter.1_rtp:basic-node.7", "source_vertex_label": "rtp:ext-community-bandwidth-filter.1", "dest_vertex_label": "rtp:basic-node.7", "source_node_path": "/filter-type/basic/basic-nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:basic-node.7_rtp:community-member.5", "source_vertex_label": "rtp:basic-node.7", "dest_vertex_label": "rtp:community-member.5", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:ext-community-bandwidth-filter.1_rtp:advanced-node.7", "source_vertex_label": "rtp:ext-community-bandwidth-filter.1", "dest_vertex_label": "rtp:advanced-node.7", "source_node_path": "/filter-type/advanced/advanced-nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:ext-community-encapsulation-filter.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:ext-community-encapsulation-filter.1", "source_node_path": "/routing-policy/ext-community-encapsulation-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:ext-community-encapsulation-filter.1_rtp:basic-node.8", "source_vertex_label": "rtp:ext-community-encapsulation-filter.1", "dest_vertex_label": "rtp:basic-node.8", "source_node_path": "/filter-type/basic/basic-nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:basic-node.8_rtp:community-member.6", "source_vertex_label": "rtp:basic-node.8", "dest_vertex_label": "rtp:community-member.6", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:ext-community-encapsulation-filter.1_rtp:advanced-node.8", "source_vertex_label": "rtp:ext-community-encapsulation-filter.1", "dest_vertex_label": "rtp:advanced-node.8", "source_node_path": "/filter-type/advanced/advanced-nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:ext-community-compress-algorithm-filter.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:ext-community-compress-algorithm-filter.1", "source_node_path": "/routing-policy/ext-community-compress-algorithm-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:ext-community-compress-algorithm-filter.1_rtp:basic-node.9", "source_vertex_label": "rtp:ext-community-compress-algorithm-filter.1", "dest_vertex_label": "rtp:basic-node.9", "source_node_path": "/filter-type/basic/basic-nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:basic-node.9_rtp:ext-community-member.2", "source_vertex_label": "rtp:basic-node.9", "dest_vertex_label": "rtp:ext-community-member.2", "source_node_path": "/ext-community-members", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:policy-definition.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:policy-definition.1", "source_node_path": "/routing-policy/policy-definitions", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:policy-definition.1_rtp:node.5", "source_vertex_label": "rtp:policy-definition.1", "dest_vertex_label": "rtp:node.5", "source_node_path": "/nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:node.5_rtp:match-ext-community-filter.1", "source_vertex_label": "rtp:node.5", "dest_vertex_label": "rtp:match-ext-community-filter.1", "source_node_path": "/conditions/match-ext-community-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:node.5_rtp:match-community-filter.1", "source_vertex_label": "rtp:node.5", "dest_vertex_label": "rtp:match-community-filter.1", "source_node_path": "/conditions/match-community-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:node.5_rtp:match-as-path-filter.1", "source_vertex_label": "rtp:node.5", "dest_vertex_label": "rtp:match-as-path-filter.1", "source_node_path": "/conditions/match-as-path-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:node.5_rtp:match-protocol.1", "source_vertex_label": "rtp:node.5", "dest_vertex_label": "rtp:match-protocol.1", "source_node_path": "/conditions/match-protocols", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:node.5_rtp:match-interface.1", "source_vertex_label": "rtp:node.5", "dest_vertex_label": "rtp:match-interface.1", "source_node_path": "/conditions/match-interfaces", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:node.5_rtp:match-route-type.1", "source_vertex_label": "rtp:node.5", "dest_vertex_label": "rtp:match-route-type.1", "source_node_path": "/conditions/match-route-types", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:node.5_rtp:match-large-community-filter.1", "source_vertex_label": "rtp:node.5", "dest_vertex_label": "rtp:match-large-community-filter.1", "source_node_path": "/conditions/match-large-community-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:node.5_rtp:communities.1", "source_vertex_label": "rtp:node.5", "dest_vertex_label": "rtp:communities.1", "source_node_path": "/actions/apply-community/set-community/inline", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:node.5_rtp:communities.2", "source_vertex_label": "rtp:node.5", "dest_vertex_label": "rtp:communities.2", "source_node_path": "/actions/apply-ext-community-soo", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:node.5_rtp:ext-community-member.3", "source_vertex_label": "rtp:node.5", "dest_vertex_label": "rtp:ext-community-member.3", "source_node_path": "/actions/apply-ext-community/ext-community-members", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:node.5_rtp:large-community-member.3", "source_vertex_label": "rtp:node.5", "dest_vertex_label": "rtp:large-community-member.3", "source_node_path": "/actions/apply-large-community/set-large-community/inline/large-community-members", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:node.5_rtp:communities.3", "source_vertex_label": "rtp:node.5", "dest_vertex_label": "rtp:communities.3", "source_node_path": "/actions/apply-ext-community-priority-color", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-routing-policy_rtp:tunnel-selector.1", "source_vertex_label": "huawei-routing-policy", "dest_vertex_label": "rtp:tunnel-selector.1", "source_node_path": "/routing-policy/tunnel-selectors", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:tunnel-selector.1_rtp:node.6", "source_vertex_label": "rtp:tunnel-selector.1", "dest_vertex_label": "rtp:node.6", "source_node_path": "/nodes", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "rtp:node.6_rtp:match-community-filter.2", "source_vertex_label": "rtp:node.6", "dest_vertex_label": "rtp:match-community-filter.2", "source_node_path": "/conditions/match-community-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-xpl_xpl:route-flow-group-list.1", "source_vertex_label": "huawei-xpl", "dest_vertex_label": "xpl:route-flow-group-list.1", "source_node_path": "/xpl/route-flow-group-lists", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-xpl_xpl:as-path-list.1", "source_vertex_label": "huawei-xpl", "dest_vertex_label": "xpl:as-path-list.1", "source_node_path": "/xpl/as-path-lists", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-xpl_xpl:community-list.1", "source_vertex_label": "huawei-xpl", "dest_vertex_label": "xpl:community-list.1", "source_node_path": "/xpl/community-lists", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-xpl_xpl:ext-community-rt-list.1", "source_vertex_label": "huawei-xpl", "dest_vertex_label": "xpl:ext-community-rt-list.1", "source_node_path": "/xpl/ext-community-rt-lists", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-xpl_xpl:ext-community-soo-list.1", "source_vertex_label": "huawei-xpl", "dest_vertex_label": "xpl:ext-community-soo-list.1", "source_node_path": "/xpl/ext-community-soo-lists", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-xpl_xpl:ipv4-prefix-list.1", "source_vertex_label": "huawei-xpl", "dest_vertex_label": "xpl:ipv4-prefix-list.1", "source_node_path": "/xpl/ipv4-prefix-lists", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-xpl_xpl:ipv6-prefix-list.1", "source_vertex_label": "huawei-xpl", "dest_vertex_label": "xpl:ipv6-prefix-list.1", "source_node_path": "/xpl/ipv6-prefix-lists", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-xpl_xpl:rd-list.1", "source_vertex_label": "huawei-xpl", "dest_vertex_label": "xpl:rd-list.1", "source_node_path": "/xpl/rd-lists", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-xpl_xpl:large-community-list.1", "source_vertex_label": "huawei-xpl", "dest_vertex_label": "xpl:large-community-list.1", "source_node_path": "/xpl/large-community-lists", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-xpl_xpl:interface-list.1", "source_vertex_label": "huawei-xpl", "dest_vertex_label": "xpl:interface-list.1", "source_node_path": "/xpl/interface-lists", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "xpl:interface-list.1_xpl:interface-set.1", "source_vertex_label": "xpl:interface-list.1", "dest_vertex_label": "xpl:interface-set.1", "source_node_path": "/interface-sets", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-xpl_xpl:route-filter.1", "source_vertex_label": "huawei-xpl", "dest_vertex_label": "xpl:route-filter.1", "source_node_path": "/xpl/route-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ni:instance.1_l3vpn:af.1", "source_vertex_label": "ni:instance.1", "dest_vertex_label": "l3vpn:af.1", "source_node_path": "/afs", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "l3vpn:af.1_l3vpn:vpn-target.1", "source_vertex_label": "l3vpn:af.1", "dest_vertex_label": "l3vpn:vpn-target.1", "source_node_path": "/vpn-targets", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-time-range_timerange:time-range-instance.1", "source_vertex_label": "huawei-time-range", "dest_vertex_label": "timerange:time-range-instance.1", "source_node_path": "/time-range/time-range-instances", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "timerange:time-range-instance.1_timerange:absolute-range.1", "source_vertex_label": "timerange:time-range-instance.1", "dest_vertex_label": "timerange:absolute-range.1", "source_node_path": "/absolute-ranges", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "timerange:time-range-instance.1_timerange:period-range.1", "source_vertex_label": "timerange:time-range-instance.1", "dest_vertex_label": "timerange:period-range.1", "source_node_path": "/period-ranges", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-acl_acl:group.1", "source_vertex_label": "huawei-acl", "dest_vertex_label": "acl:group.1", "source_node_path": "/acl/groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl:group.1_acl:rule-basic.1", "source_vertex_label": "acl:group.1", "dest_vertex_label": "acl:rule-basic.1", "source_node_path": "/rule-basics", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl:group.1_acl:rule-advance.1", "source_vertex_label": "acl:group.1", "dest_vertex_label": "acl:rule-advance.1", "source_node_path": "/rule-advances", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl:group.1_acl:rule-ethernet.1", "source_vertex_label": "acl:group.1", "dest_vertex_label": "acl:rule-ethernet.1", "source_node_path": "/rule-ethernets", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl:group.1_acl:rule-interface.1", "source_vertex_label": "acl:group.1", "dest_vertex_label": "acl:rule-interface.1", "source_node_path": "/rule-interfaces", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl:group.1_acl:rule-mpls.1", "source_vertex_label": "acl:group.1", "dest_vertex_label": "acl:rule-mpls.1", "source_node_path": "/rule-mplss", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl:rule-mpls.1_acl:lable.1", "source_vertex_label": "acl:rule-mpls.1", "dest_vertex_label": "acl:lable.1", "source_node_path": "/lables", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl:group.1_acl:rule-user.1", "source_vertex_label": "acl:group.1", "dest_vertex_label": "acl:rule-user.1", "source_node_path": "/rule-users", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl:group.1_acl:rule-arp.1", "source_vertex_label": "acl:group.1", "dest_vertex_label": "acl:rule-arp.1", "source_node_path": "/rule-arps", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-acl_acl:group.2", "source_vertex_label": "huawei-acl", "dest_vertex_label": "acl:group.2", "source_node_path": "/acl/access", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-acl_acl:group6.1", "source_vertex_label": "huawei-acl", "dest_vertex_label": "acl:group6.1", "source_node_path": "/acl/group6s", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl:group6.1_acl:rule-basic.2", "source_vertex_label": "acl:group6.1", "dest_vertex_label": "acl:rule-basic.2", "source_node_path": "/rule-basics", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl:group6.1_acl:rule-advance.2", "source_vertex_label": "acl:group6.1", "dest_vertex_label": "acl:rule-advance.2", "source_node_path": "/rule-advances", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl:group6.1_acl:rule-interface.2", "source_vertex_label": "acl:group6.1", "dest_vertex_label": "acl:rule-interface.2", "source_node_path": "/rule-interfaces", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-acl_acl:ip-pool.5", "source_vertex_label": "huawei-acl", "dest_vertex_label": "acl:ip-pool.5", "source_node_path": "/acl/ip-pools", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl:ip-pool.5_acl:ipaddr.1", "source_vertex_label": "acl:ip-pool.5", "dest_vertex_label": "acl:ipaddr.1", "source_node_path": "/apply-type/apply-ip/ipaddrs", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl:ip-pool.5_acl:host-name.1", "source_vertex_label": "acl:ip-pool.5", "dest_vertex_label": "acl:host-name.1", "source_node_path": "/apply-type/apply-ip/host-names", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-acl_acl:ip-pool6.1", "source_vertex_label": "huawei-acl", "dest_vertex_label": "acl:ip-pool6.1", "source_node_path": "/acl/ip-pool6s", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl:ip-pool6.1_acl:ipv6-address.1", "source_vertex_label": "acl:ip-pool6.1", "dest_vertex_label": "acl:ipv6-address.1", "source_node_path": "/apply-type/apply-ip/ipv6-addresses", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-acl_acl:port-pool.3", "source_vertex_label": "huawei-acl", "dest_vertex_label": "acl:port-pool.3", "source_node_path": "/acl/port-pools", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl:port-pool.3_acl:port.1", "source_vertex_label": "acl:port-pool.3", "dest_vertex_label": "acl:port.1", "source_node_path": "/ports", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-software-management_hw-sm:file-server.1", "source_vertex_label": "huawei-software-management", "dest_vertex_label": "hw-sm:file-server.1", "source_node_path": "/software-operation/operation-config/file-servers", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-vlan_vlan:vlan.2", "source_vertex_label": "hua<PERSON>-vlan", "dest_vertex_label": "vlan:vlan.2", "source_node_path": "/vlan/vlans", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "vlan:vlan.2_vlan:ip-subnet-vlan.1", "source_vertex_label": "vlan:vlan.2", "dest_vertex_label": "vlan:ip-subnet-vlan.1", "source_node_path": "/ip-subnet-vlans", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-vlan_vlan:instance.1", "source_vertex_label": "hua<PERSON>-vlan", "dest_vertex_label": "vlan:instance.1", "source_node_path": "/vlan/instances", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ifm-trunk:member.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ifm-trunk:member.1", "source_node_path": "/trunk/members", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:trunk-vlan-id-list.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:trunk-vlan-id-list.1", "source_node_path": "/ethernet/main-interface/l2-attribute/set-trunk-vlans/vlan-id-list", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:vlan-stacking.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:vlan-stacking.1", "source_node_path": "/ethernet/main-interface/l2-attribute/vlan-stackings", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:vlan-stacking-remark.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:vlan-stacking-remark.1", "source_node_path": "/ethernet/main-interface/l2-attribute/vlan-stacking-remarks", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:vlan-stacking-tag.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:vlan-stacking-tag.1", "source_node_path": "/ethernet/main-interface/l2-attribute/vlan-stacking-tags", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:vlan-stacking-remark-tag.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:vlan-stacking-remark-tag.1", "source_node_path": "/ethernet/main-interface/l2-attribute/vlan-stacking-remark-tags", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:vlan-stacking-8021p-filter.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:vlan-stacking-8021p-filter.1", "source_node_path": "/ethernet/main-interface/l2-attribute/vlan-stacking-8021p-filters", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:vlan-stacking-8021p-remark.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:vlan-stacking-8021p-remark.1", "source_node_path": "/ethernet/main-interface/l2-attribute/vlan-stacking-8021p-remarks", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:vlan-mapping.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:vlan-mapping.1", "source_node_path": "/ethernet/main-interface/l2-attribute/vlan-mappings", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:vlan-mapping-remark.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:vlan-mapping-remark.1", "source_node_path": "/ethernet/main-interface/l2-attribute/vlan-mapping-remarks", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:vlan-mapping-single.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:vlan-mapping-single.1", "source_node_path": "/ethernet/main-interface/l2-attribute/vlan-mapping-singles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:vlan-mapping-double.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:vlan-mapping-double.1", "source_node_path": "/ethernet/main-interface/l2-attribute/vlan-mapping-doubles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:vlan-mapping-double-to-single.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:vlan-mapping-double-to-single.1", "source_node_path": "/ethernet/main-interface/l2-attribute/vlan-mapping-double-to-singles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:port-isolate-group.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:port-isolate-group.1", "source_node_path": "/ethernet/main-interface/port-isolate-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:port-am-isolate.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:port-am-isolate.1", "source_node_path": "/ethernet/main-interface/port-am-isolates", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:vlan-group.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:vlan-group.1", "source_node_path": "/ethernet/l3-sub-interface/vlan-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:dot1q-vlans-group.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:dot1q-vlans-group.1", "source_node_path": "/ethernet/l3-sub-interface/flow-type/dot1q-termination/dot1q-termination/dot1q-vlans", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:policy-vlan.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:policy-vlan.1", "source_node_path": "/ethernet/l3-sub-interface/flow-type/dot1q-termination/dot1q-termination/dot1q-vlans-policy/policy-vlans", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:policy-vlan-group.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:policy-vlan-group.1", "source_node_path": "/ethernet/l3-sub-interface/flow-type/dot1q-termination/dot1q-termination/dot1q-vlans-policy/policy-vlan-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:vrrp.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:vrrp.1", "source_node_path": "/ethernet/l3-sub-interface/flow-type/dot1q-termination/dot1q-termination/vrrps", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:qinq-vid.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:qinq-vid.1", "source_node_path": "/ethernet/l3-sub-interface/flow-type/qinq-termination/qinq-termination/qinq-vids", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:vrrp.2", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:vrrp.2", "source_node_path": "/ethernet/l3-sub-interface/flow-type/qinq-termination/qinq-termination/vrrps", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:stacking-vid.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:stacking-vid.1", "source_node_path": "/ethernet/l3-sub-interface/flow-type/qinq-stacking/qinq-stacking", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:qinq-stacking-vid-pevid.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:qinq-stacking-vid-pevid.1", "source_node_path": "/ethernet/l3-sub-interface/flow-type/qinq-stacking/qinq-stacking/qinq-stacking-vid-pevids", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:mapping-vid.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:mapping-vid.1", "source_node_path": "/ethernet/l3-sub-interface/flow-type/qinq-mapping/qinq-mapping/mapping-vids", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:mapping-single.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:mapping-single.1", "source_node_path": "/ethernet/l3-sub-interface/flow-type/qinq-mapping/qinq-mapping/mapping-singles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:mapping-double-to-single.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:mapping-double-to-single.1", "source_node_path": "/ethernet/l3-sub-interface/flow-type/qinq-mapping/qinq-mapping/mapping-double-to-singles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:policy-vlan.2", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:policy-vlan.2", "source_node_path": "/ethernet/l3-sub-interface/flow-type/qinq-stacking-policy/stacking-policy/policy-vlans", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:policy-vlan-group.2", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:policy-vlan-group.2", "source_node_path": "/ethernet/l3-sub-interface/flow-type/qinq-stacking-policy/stacking-policy/policy-vlan-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:user-vlan-qinq.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:user-vlan-qinq.1", "source_node_path": "/ethernet/l3-sub-interface/flow-type/user-vlan-comm/user-vlan-common/user-vlan-qinqs", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:description.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:description.1", "source_node_path": "/ethernet/l3-sub-interface/flow-type/user-vlan-comm/user-vlan-common/user-vlan-qinqs/descriptions", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:description.2", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:description.2", "source_node_path": "/ethernet/l3-sub-interface/flow-type/user-vlan-comm/user-vlan-common/user-vlan-dot1q/descriptions", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:policy-vlan.3", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:policy-vlan.3", "source_node_path": "/ethernet/l2-sub-interface/flow-type/dot1q/dot1q/policy/policy/policy-vlans", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:qinq-vid.2", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:qinq-vid.2", "source_node_path": "/ethernet/l2-sub-interface/flow-type/qinq/qinqs/qinq-vids", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ifm:interface.1_ethernet:qinq-with-pe-segment.1", "source_vertex_label": "ifm:interface.1", "dest_vertex_label": "ethernet:qinq-with-pe-segment.1", "source_node_path": "/ethernet/l2-sub-interface/flow-type/qinq/qinqs/qinq-with-pe-segments", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-bd_bd:instance.1", "source_vertex_label": "hua<PERSON>-bd", "dest_vertex_label": "bd:instance.1", "source_node_path": "/bd/instances", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bd:instance.1_bd:service-point.1", "source_vertex_label": "bd:instance.1", "dest_vertex_label": "bd:service-point.1", "source_node_path": "/service-points", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-bd_bd:profile.1", "source_vertex_label": "hua<PERSON>-bd", "dest_vertex_label": "bd:profile.1", "source_node_path": "/bd/profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-ntp_ntp:disable.1", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "ntp:disable.1", "source_node_path": "/ntp/disables", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-ntp_ntp:unicast.1", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "ntp:unicast.1", "source_node_path": "/ntp/unicasts", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-ntp_ntp:broadcast.1", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "ntp:broadcast.1", "source_node_path": "/ntp/broadcasts", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-ntp_ntp:multicast.1", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "ntp:multicast.1", "source_node_path": "/ntp/multicasts", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-ntp_ntp:manycast.1", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "ntp:manycast.1", "source_node_path": "/ntp/manycasts", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-ntp_ntp:authentication.1", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "ntp:authentication.1", "source_node_path": "/ntp/authentications", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-ntp_ntp:access-control.1", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "ntp:access-control.1", "source_node_path": "/ntp/access-controls", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-ntp_ntp:clock.1", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "ntp:clock.1", "source_node_path": "/ntp/clocks", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-ntp_ntp:in-interface-disable.1", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "ntp:in-interface-disable.1", "source_node_path": "/ntp/in-interface-disables", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-ntp_ntp:source-interface.1", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "ntp:source-interface.1", "source_node_path": "/ntp/source-interfaces", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-ntp_ntp:server-source-interface.1", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "ntp:server-source-interface.1", "source_node_path": "/ntp/server-source-interfaces", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-ntp_ntp:server-isolate-source-interface.1", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "ntp:server-isolate-source-interface.1", "source_node_path": "/ntp/server-isolate-source-interfaces", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-ntp_ntp:server-source-ipv6-address.1", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "ntp:server-source-ipv6-address.1", "source_node_path": "/ntp/server-source-ipv6-addresses", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-ntp_ntp:server-isolate-source-ipv6-interface.1", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "ntp:server-isolate-source-ipv6-interface.1", "source_node_path": "/ntp/server-isolate-source-ipv6-interfaces", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-ntp_ntp:server-source-all-interface.1", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "ntp:server-source-all-interface.1", "source_node_path": "/ntp/server-source-all-interfaces", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei-ntp_ntp:server-enable.1", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "ntp:server-enable.1", "source_node_path": "/ntp/server-enables", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_an-dot1ax:port.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "an-dot1ax:port.1", "source_node_path": "/aggregator/ports", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-forwarding_bbf-fwd-prof:forwarder-policy-profile.1", "source_vertex_label": "bbf-l2-forwarding", "dest_vertex_label": "bbf-fwd-prof:forwarder-policy-profile.1", "source_node_path": "/forwarding/forwarder-policy-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-l2-forwarding_bbf-fwd-prof:forwarder-policy.1", "source_vertex_label": "bbf-l2-forwarding", "dest_vertex_label": "bbf-fwd-prof:forwarder-policy.1", "source_node_path": "/forwarding/forwarders", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-priority-profiles_bbf-qos-pri-prof:cos-group-profile.1", "source_vertex_label": "bbf-qos-priority-profiles", "dest_vertex_label": "bbf-qos-pri-prof:cos-group-profile.1", "source_node_path": "/cos-group-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pri-prof:cos-group-profile.1_bbf-qos-pri-prof:cos-group.1", "source_vertex_label": "bbf-qos-pri-prof:cos-group-profile.1", "dest_vertex_label": "bbf-qos-pri-prof:cos-group.1", "source_node_path": "/cos-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-priority-profiles_bbf-qos-pri-prof:pbit-to-pbit-mapping-profile.1", "source_vertex_label": "bbf-qos-priority-profiles", "dest_vertex_label": "bbf-qos-pri-prof:pbit-to-pbit-mapping-profile.1", "source_node_path": "/pbit-to-pbit-mapping-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pri-prof:pbit-to-pbit-mapping-profile.1_bbf-qos-pri-prof:pbit.1", "source_vertex_label": "bbf-qos-pri-prof:pbit-to-pbit-mapping-profile.1", "dest_vertex_label": "bbf-qos-pri-prof:pbit.1", "source_node_path": "/pbits", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pri-prof:pbit.1_bbf-qos-pri-prof:color.1", "source_vertex_label": "bbf-qos-pri-prof:pbit.1", "dest_vertex_label": "bbf-qos-pri-prof:color.1", "source_node_path": "/colors", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-priority-profiles_bbf-qos-pri-prof:ipprec-to-pbit-mapping-profile.1", "source_vertex_label": "bbf-qos-priority-profiles", "dest_vertex_label": "bbf-qos-pri-prof:ipprec-to-pbit-mapping-profile.1", "source_node_path": "/ipprec-to-pbit-mapping-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pri-prof:ipprec-to-pbit-mapping-profile.1_bbf-qos-pri-prof:ipprec.1", "source_vertex_label": "bbf-qos-pri-prof:ipprec-to-pbit-mapping-profile.1", "dest_vertex_label": "bbf-qos-pri-prof:ipprec.1", "source_node_path": "/ipprecs", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pri-prof:ipprec.1_bbf-qos-pri-prof:color.2", "source_vertex_label": "bbf-qos-pri-prof:ipprec.1", "dest_vertex_label": "bbf-qos-pri-prof:color.2", "source_node_path": "/colors", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-priority-profiles_bbf-qos-pri-prof:dscp-to-pbit-mapping-profile.1", "source_vertex_label": "bbf-qos-priority-profiles", "dest_vertex_label": "bbf-qos-pri-prof:dscp-to-pbit-mapping-profile.1", "source_node_path": "/dscp-to-pbit-mapping-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pri-prof:dscp-to-pbit-mapping-profile.1_bbf-qos-pri-prof:dscp.1", "source_vertex_label": "bbf-qos-pri-prof:dscp-to-pbit-mapping-profile.1", "dest_vertex_label": "bbf-qos-pri-prof:dscp.1", "source_node_path": "/dscps", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pri-prof:dscp.1_bbf-qos-pri-prof:color.3", "source_vertex_label": "bbf-qos-pri-prof:dscp.1", "dest_vertex_label": "bbf-qos-pri-prof:color.3", "source_node_path": "/colors", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-priority-profiles_bbf-qos-pri-prof:dscp-to-dscp-mapping-profile.1", "source_vertex_label": "bbf-qos-priority-profiles", "dest_vertex_label": "bbf-qos-pri-prof:dscp-to-dscp-mapping-profile.1", "source_node_path": "/dscp-to-dscp-mapping-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pri-prof:dscp-to-dscp-mapping-profile.1_bbf-qos-pri-prof:dscp.2", "source_vertex_label": "bbf-qos-pri-prof:dscp-to-dscp-mapping-profile.1", "dest_vertex_label": "bbf-qos-pri-prof:dscp.2", "source_node_path": "/dscps", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-pri-prof:dscp.2_bbf-qos-pri-prof:color.4", "source_vertex_label": "bbf-qos-pri-prof:dscp.2", "dest_vertex_label": "bbf-qos-pri-prof:color.4", "source_node_path": "/colors", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-priority-profiles_bbf-qos-pri-prof:queue-mapping-profile.1", "source_vertex_label": "bbf-qos-priority-profiles", "dest_vertex_label": "bbf-qos-pri-prof:queue-mapping-profile.1", "source_node_path": "/queue-mapping-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-policing-profiles_bbf-qos-plc-prof:priority-group-policing-profile.1", "source_vertex_label": "bbf-qos-policing-profiles", "dest_vertex_label": "bbf-qos-plc-prof:priority-group-policing-profile.1", "source_node_path": "/priority-group-policing-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-plc-prof:priority-group-policing-profile.1_bbf-qos-plc-prof:priority-group.1", "source_vertex_label": "bbf-qos-plc-prof:priority-group-policing-profile.1", "dest_vertex_label": "bbf-qos-plc-prof:priority-group.1", "source_node_path": "/priority-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-policing-profiles_bbf-qos-plc-prof:car-threshold-profile.1", "source_vertex_label": "bbf-qos-policing-profiles", "dest_vertex_label": "bbf-qos-plc-prof:car-threshold-profile.1", "source_node_path": "/car-threshold-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-traffic-mngt-profiles_bbf-qos-tm-prof:queue.1", "source_vertex_label": "bbf-qos-traffic-mngt-profiles", "dest_vertex_label": "bbf-qos-tm-prof:queue.1", "source_node_path": "/queue-wred/queues", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-traffic-mngt-profiles_bbf-qos-tm-prof:queue-policy-profile.1", "source_vertex_label": "bbf-qos-traffic-mngt-profiles", "dest_vertex_label": "bbf-qos-tm-prof:queue-policy-profile.1", "source_node_path": "/queue-policy-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-tm-prof:queue-policy-profile.1_bbf-qos-tm-prof:queue.2", "source_vertex_label": "bbf-qos-tm-prof:queue-policy-profile.1", "dest_vertex_label": "bbf-qos-tm-prof:queue.2", "source_node_path": "/queue-wred/queues", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-qos-tm-prof:queue-policy-profile.1_bbf-qos-tm-prof:queue.3", "source_vertex_label": "bbf-qos-tm-prof:queue-policy-profile.1", "dest_vertex_label": "bbf-qos-tm-prof:queue.3", "source_node_path": "/queue-buffer-max-size/queues", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd:multicast-vpn.1_bbf-mgmd-channel:channel-match-range.1", "source_vertex_label": "bbf-mgmd:multicast-vpn.1", "dest_vertex_label": "bbf-mgmd-channel:channel-match-range.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd_bbf-mgmd-if:interface-to-cascade.1", "source_vertex_label": "bbf-mgmd", "dest_vertex_label": "bbf-mgmd-if:interface-to-cascade.1", "source_node_path": "/multicast/mgmd", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-mgmd:multicast-channel.1_bbf-mgmd-if:interface-to-cascades.1", "source_vertex_label": "bbf-mgmd:multicast-channel.1", "dest_vertex_label": "bbf-mgmd-if:interface-to-cascades.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-system_bbf-ldra-ext:dhcpv6-option-permit-forwarding.1", "source_vertex_label": "ietf-system", "dest_vertex_label": "bbf-ldra-ext:dhcpv6-option-permit-forwarding.1", "source_node_path": "/system/dhcpv6-global", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-subprof:subscriber-profile.1_bbf-raioprof:subscriber-list.1", "source_vertex_label": "bbf-subprof:subscriber-profile.1", "dest_vertex_label": "bbf-raioprof:subscriber-list.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-system_bbf-raioprof:aggregation-circuit-id-format.1", "source_vertex_label": "ietf-system", "dest_vertex_label": "bbf-raioprof:aggregation-circuit-id-format.1", "source_node_path": "/system/subscriber-management", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ietf-system_bbf-raioprof:sub-option-switch.1", "source_vertex_label": "ietf-system", "dest_vertex_label": "bbf-raioprof:sub-option-switch.1", "source_node_path": "/system/subscriber-management", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xpongemtcont_bbf-gpon-gemport:gem-bundle.2", "source_vertex_label": "bbf-xpongemtcont", "dest_vertex_label": "bbf-gpon-gemport:gem-bundle.2", "source_node_path": "/xpongemtcont/gem-bundle", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-gpon-gemport:gem-bundle.2_bbf-gpon-gemport:slave-gem.2", "source_vertex_label": "bbf-gpon-gemport:gem-bundle.2", "dest_vertex_label": "bbf-gpon-gemport:slave-gem.2", "source_node_path": "/slave-gem", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xpon_alm-policy-profile:onu-alm-policy-profile.1", "source_vertex_label": "bbf-xpon", "dest_vertex_label": "alm-policy-profile:onu-alm-policy-profile.1", "source_node_path": "/xpon/onu-alm-policy-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "alm-policy-profile:onu-alm-policy-profile.1_alm-policy-profile:alarm-lists.1", "source_vertex_label": "alm-policy-profile:onu-alm-policy-profile.1", "dest_vertex_label": "alm-policy-profile:alarm-lists.1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xpon_alm-threshold-profile:optic-alm-threshold-profile.1", "source_vertex_label": "bbf-xpon", "dest_vertex_label": "alm-threshold-profile:optic-alm-threshold-profile.1", "source_node_path": "/xpon/optic-alm-threshold-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_bbf-xpon-ani:onu-snmp-route.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "bbf-xpon-ani:onu-snmp-route.1", "source_node_path": "/ani/snmp-route", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_bbf-xpon-ani:eth-bundle.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "bbf-xpon-ani:eth-bundle.1", "source_node_path": "/ani/ont-port-bundle", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xpon-ani:eth-bundle.1_bbf-xpon-ani:port.1", "source_vertex_label": "bbf-xpon-ani:eth-bundle.1", "dest_vertex_label": "bbf-xpon-ani:port.1", "source_node_path": "/port-list", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_bbf-xpon-channel:tcont-group.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "bbf-xpon-channel:tcont-group.1", "source_node_path": "/channel-pair/tcont-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "bbf-xpon-ont-load_ontload:ont-select.1", "source_vertex_label": "bbf-xpon-ont-load", "dest_vertex_label": "ontload:ont-select.1", "source_node_path": "/xpon-ont-load/ont-selects", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "if:interface.1_bbf-xpon-v-ani:tpid.1", "source_vertex_label": "if:interface.1", "dest_vertex_label": "bbf-xpon-v-ani:tpid.1", "source_node_path": "/v-ani", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]