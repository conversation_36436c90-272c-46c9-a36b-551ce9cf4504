{"ds": [{"name": "running", "huawei-vlan": [{"huawei-vlan:vlan": {"vlans": {"vlan": [{"id": 1, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "GE0/0/8", "access-type": "hybrid", "state": "down", "tag-mode": "untag"}, {"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "untag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "untag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "untag"}, {"interface-name": "MultiGE0/0/2", "access-type": "hybrid", "state": "down", "tag-mode": "untag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "untag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "untag"}, {"interface-name": "Eth-Trunk4", "access-type": "hybrid", "state": "down", "tag-mode": "untag"}, {"interface-name": "Eth-Trunk5", "access-type": "hybrid", "state": "down", "tag-mode": "untag"}, {"interface-name": "Eth-Trunk6", "access-type": "hybrid", "state": "down", "tag-mode": "untag"}, {"interface-name": "Eth-Trunk7", "access-type": "hybrid", "state": "down", "tag-mode": "untag"}, {"interface-name": "Eth-Trunk8", "access-type": "hybrid", "state": "down", "tag-mode": "untag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "untag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}, "vlan-trusts": {"vlan-trust": [{"if-name": "MultiGE0/0/1"}]}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "interface-groups": {"interface-group": [{"interface": "10GE0/0/2", "groups": {"group": [{"group-address": "*********", "up-time": 116, "expire-time": 14, "group-timer": "exist", "retran-count": 0, "last-member-query": false, "filter-mode": "exclude", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist"}, {"group-address": "*********", "up-time": 116, "expire-time": 14, "group-timer": "exist", "retran-count": 0, "last-member-query": false, "filter-mode": "exclude", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist"}, {"group-address": "*********", "up-time": 116, "expire-time": 14, "group-timer": "exist", "retran-count": 0, "last-member-query": false, "filter-mode": "exclude", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist"}, {"group-address": "*********", "up-time": 116, "expire-time": 14, "group-timer": "exist", "retran-count": 0, "last-member-query": false, "filter-mode": "exclude", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist"}, {"group-address": "*********", "up-time": 116, "expire-time": 14, "group-timer": "exist", "retran-count": 0, "last-member-query": false, "filter-mode": "exclude", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist"}, {"group-address": "*********", "up-time": 116, "expire-time": 14, "group-timer": "exist", "retran-count": 0, "last-member-query": false, "filter-mode": "exclude", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist"}, {"group-address": "*********", "up-time": 116, "expire-time": 1879048190, "group-timer": "not-exist", "retran-count": 0, "last-member-query": false, "filter-mode": "include", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist", "sources": {"source": [{"source-address": "***************", "up-time": 116, "expire-time": 14, "source-timer": "exist", "retran-count": 0, "last-member-query": false}]}}, {"group-address": "*********", "up-time": 116, "expire-time": 14, "group-timer": "exist", "retran-count": 0, "last-member-query": false, "filter-mode": "exclude", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist"}]}}]}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9852, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9851, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9851, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9851, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9851, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9851, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "***************", "up-time": 9851, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9851, "entry-flag": "active", "source-flag": "active"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 15, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 44, "forwarded-report-count": 44, "forwarded-leave-count": 0, "forwarded-query-count": 15}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}, "huawei-mac:mac-addresss": {"mac-address": [{"address": "0001-1110-0001", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0002", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0003", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0004", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0005", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0006", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0007", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0008", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0009", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-000a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-000b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-000c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-000d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-000e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-000f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0010", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0011", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0012", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0013", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0014", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0015", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0016", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0017", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0018", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0019", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-001a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-001b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-001c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-001d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-001e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-001f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0020", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0021", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0022", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0023", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0024", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0025", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0026", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0027", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0028", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0029", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-002a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-002b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-002c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-002d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-002e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-002f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0030", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0031", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0032", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0033", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0034", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0035", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0036", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0037", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0038", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0039", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-003a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-003b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-003c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-003d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-003e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-003f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0040", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0041", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0042", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0043", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0044", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0045", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0046", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0047", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0048", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0049", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-004a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-004b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-004c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-004d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-004e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-004f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0050", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0051", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0052", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0053", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0054", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0055", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0056", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0057", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0058", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0059", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-005a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-005b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-005c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-005d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-005e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-005f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0060", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0061", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0062", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0063", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0064", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0065", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0066", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0067", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0068", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0069", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-006a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-006b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-006c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-006d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-006e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-006f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0070", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0071", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0072", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0073", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0074", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0075", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0076", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0077", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0078", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0079", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-007a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-007b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-007c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-007d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-007e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-007f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0080", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0081", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0082", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0083", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0084", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0085", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0086", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0087", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0088", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0089", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-008a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-008b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-008c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-008d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-008e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-008f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0090", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0091", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0092", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0093", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0094", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0095", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0096", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0097", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0098", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0099", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-009a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-009b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-009c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-009d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-009e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-009f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00a0", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00a1", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00a2", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00a3", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00a4", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00a5", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00a6", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00a7", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00a8", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00a9", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00aa", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00ab", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00ac", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00ad", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00ae", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00af", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00b0", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00b1", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00b2", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00b3", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00b4", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00b5", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00b6", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00b7", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00b8", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00b9", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00ba", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00bb", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00bc", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00bd", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00be", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00bf", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00c0", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00c1", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00c2", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00c3", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00c4", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00c5", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00c6", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00c7", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00c8", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00c9", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00ca", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00cb", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00cc", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00cd", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00ce", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00cf", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00d0", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00d1", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00d2", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00d3", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00d4", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00d5", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00d6", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00d7", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00d8", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00d9", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00da", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00db", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00dc", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00dd", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00de", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00df", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00e0", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00e1", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00e2", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00e3", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00e4", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00e5", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00e6", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00e7", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00e8", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00e9", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00ea", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00eb", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00ec", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00ed", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00ee", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00ef", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00f0", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00f1", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00f2", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00f3", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00f4", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00f5", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00f6", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00f7", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00f8", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00f9", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00fa", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00fb", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00fc", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00fd", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00fe", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-00ff", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0100", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0101", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0102", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0103", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0104", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0105", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0106", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0107", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0108", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0109", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-010a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-010b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-010c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-010d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-010e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-010f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0110", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0111", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0112", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0113", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0114", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0115", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0116", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0117", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0118", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0119", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-011a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-011b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-011c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-011d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-011e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-011f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0120", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0121", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0122", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0123", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0124", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0125", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0126", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0127", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0128", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0129", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-012a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-012b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-012c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-012d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-012e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-012f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0130", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0131", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0132", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0133", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0134", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0135", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0136", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0137", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0138", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0139", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-013a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-013b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-013c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-013d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-013e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-013f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0140", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0141", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0142", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0143", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0144", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0145", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0146", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0147", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0148", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0149", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-014a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-014b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-014c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-014d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-014e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-014f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0150", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0151", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0152", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0153", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0154", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0155", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0156", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0157", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0158", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0159", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-015a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-015b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-015c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-015d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-015e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-015f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0160", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0161", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0162", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0163", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0164", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0165", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0166", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0167", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0168", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0169", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-016a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-016b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-016c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-016d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-016e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-016f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0170", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0171", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0172", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0173", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0174", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0175", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0176", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0177", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0178", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0179", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-017a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-017b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-017c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-017d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-017e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-017f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0180", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0181", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0182", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0183", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0184", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0185", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0186", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0187", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0188", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0189", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-018a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-018b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-018c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-018d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-018e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-018f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0190", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0191", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0192", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0193", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0194", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0195", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0196", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0197", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0198", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-0199", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-019a", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-019b", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-019c", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-019d", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-019e", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-019f", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01a0", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01a1", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01a2", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01a3", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01a4", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01a5", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01a6", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01a7", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01a8", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01a9", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01aa", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01ab", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01ac", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01ad", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01ae", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01af", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01b0", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01b1", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01b2", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01b3", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01b4", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01b5", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01b6", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01b7", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01b8", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01b9", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01ba", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01bb", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01bc", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01bd", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01be", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01bf", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01c0", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01c1", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01c2", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01c3", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01c4", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01c5", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01c6", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01c7", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01c8", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01c9", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01ca", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01cb", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01cc", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01cd", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01ce", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01cf", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01d0", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01d1", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01d2", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01d3", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01d4", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01d5", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01d6", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01d7", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01d8", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01d9", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01da", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01db", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01dc", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01dd", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01de", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01df", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1110-01e0", "mac-type": {"static": {"out-interface-name": "10GE0/0/1"}}}, {"address": "0001-1111-0001", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-0002", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-0003", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-0004", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-0005", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-0006", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-0007", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-0008", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-0009", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-000a", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-000b", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-000c", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-000d", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-000e", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-000f", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-0010", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-0011", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-0012", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-0013", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-0014", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-0015", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-0016", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-0017", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-0018", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-0019", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-001a", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-001b", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-001c", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-001d", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-001e", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-001f", "mac-type": {"black-hole": {"black-hole": ""}}}, {"address": "0001-1111-0020", "mac-type": {"black-hole": {"black-hole": ""}}}]}}, {"id": 2, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "GE0/0/5", "access-type": "access", "state": "up", "tag-mode": "untag"}, {"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9852, "entry-flag": "protocol-active", "source-flag": "igmp-active", "ports": {"port": [{"interface": "10GE0/0/2", "up-time": 1, "expire-time": 129, "port-flag": "-D-"}]}}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 21, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 31, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 28, "forwarded-report-count": 28, "forwarded-leave-count": 0, "forwarded-query-count": 21}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 50, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9835, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9835, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9835, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9835, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9842, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9835, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "***************", "up-time": 9835, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9835, "entry-flag": "active", "source-flag": "active"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 21, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 31, "forwarded-report-count": 31, "forwarded-leave-count": 0, "forwarded-query-count": 21}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 3, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9852, "entry-flag": "active", "source-flag": "active"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 19, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 27, "forwarded-report-count": 27, "forwarded-leave-count": 0, "forwarded-query-count": 19}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 4, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9852, "entry-flag": "active", "source-flag": "active"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 19, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 21, "forwarded-report-count": 21, "forwarded-leave-count": 0, "forwarded-query-count": 19}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 5, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9852, "entry-flag": "protocol-active", "source-flag": "igmp-active", "ports": {"port": [{"interface": "10GE0/0/2", "up-time": 0, "expire-time": 130, "port-flag": "-D-"}]}}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 18, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 22, "forwarded-report-count": 22, "forwarded-leave-count": 0, "forwarded-query-count": 18}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 6, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "interface-groups": {"interface-group": [{"interface": "10GE0/0/2", "groups": {"group": [{"group-address": "*********", "up-time": 115, "expire-time": 15, "group-timer": "exist", "retran-count": 0, "last-member-query": false, "filter-mode": "exclude", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist"}]}}, {"interface": "MultiGE0/0/1", "groups": {"group": [{"group-address": "*********", "up-time": 115, "expire-time": 15, "group-timer": "exist", "retran-count": 0, "last-member-query": false, "filter-mode": "exclude", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist"}]}}]}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9852, "entry-flag": "protocol-active", "source-flag": "igmp-active", "ports": {"port": [{"interface": "10GE0/0/2", "up-time": 0, "expire-time": 130, "port-flag": "-D-"}]}}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 17, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 28, "forwarded-report-count": 28, "forwarded-leave-count": 0, "forwarded-query-count": 17}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 7, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9850, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9850, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9850, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9850, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9850, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9850, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "***************", "up-time": 9852, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9850, "entry-flag": "active", "source-flag": "active"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 17, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 23, "forwarded-report-count": 23, "forwarded-leave-count": 0, "forwarded-query-count": 17}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 8, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "interface-groups": {"interface-group": [{"interface": "10GE0/0/2", "groups": {"group": [{"group-address": "*********", "up-time": 115, "expire-time": 15, "group-timer": "exist", "retran-count": 0, "last-member-query": false, "filter-mode": "exclude", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist"}, {"group-address": "*********", "up-time": 115, "expire-time": 15, "group-timer": "exist", "retran-count": 0, "last-member-query": false, "filter-mode": "exclude", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist"}, {"group-address": "*********", "up-time": 115, "expire-time": 15, "group-timer": "exist", "retran-count": 0, "last-member-query": false, "filter-mode": "exclude", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist"}, {"group-address": "*********", "up-time": 115, "expire-time": 15, "group-timer": "exist", "retran-count": 0, "last-member-query": false, "filter-mode": "exclude", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist"}, {"group-address": "*********", "up-time": 115, "expire-time": 15, "group-timer": "exist", "retran-count": 0, "last-member-query": false, "filter-mode": "exclude", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist"}, {"group-address": "*********", "up-time": 115, "expire-time": 15, "group-timer": "exist", "retran-count": 0, "last-member-query": false, "filter-mode": "exclude", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist"}, {"group-address": "*********", "up-time": 115, "expire-time": 1879048190, "group-timer": "not-exist", "retran-count": 0, "last-member-query": false, "filter-mode": "include", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist", "sources": {"source": [{"source-address": "***************", "up-time": 115, "expire-time": 15, "source-timer": "exist", "retran-count": 0, "last-member-query": false}]}}, {"group-address": "*********", "up-time": 115, "expire-time": 15, "group-timer": "exist", "retran-count": 0, "last-member-query": false, "filter-mode": "exclude", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist"}]}}]}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9847, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9847, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9847, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9847, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9847, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9847, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "***************", "up-time": 9847, "entry-flag": "active", "source-flag": "active"}, {"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9852, "entry-flag": "active", "source-flag": "active"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 14, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 26, "forwarded-report-count": 26, "forwarded-leave-count": 0, "forwarded-query-count": 14}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 9, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9852, "entry-flag": "active", "source-flag": "active"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 16, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 24, "forwarded-report-count": 24, "forwarded-leave-count": 0, "forwarded-query-count": 16}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 10, "type": "common", "mac-learning": "disable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9851, "entry-flag": "active", "source-flag": "active"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 17, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 22, "forwarded-report-count": 22, "forwarded-leave-count": 0, "forwarded-query-count": 17}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 11, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9851, "entry-flag": "protocol-active", "source-flag": "igmp-active", "ports": {"port": [{"interface": "10GE0/0/2", "up-time": 0, "expire-time": 130, "port-flag": "-D-"}]}}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 21, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 23, "forwarded-report-count": 23, "forwarded-leave-count": 0, "forwarded-query-count": 21}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 13, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9851, "entry-flag": "active", "source-flag": "active"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 17, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 31, "forwarded-report-count": 31, "forwarded-leave-count": 0, "forwarded-query-count": 17}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 14, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9851, "entry-flag": "active", "source-flag": "active"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 9, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 28, "forwarded-report-count": 28, "forwarded-leave-count": 0, "forwarded-query-count": 9}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 15, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9851, "entry-flag": "active", "source-flag": "active"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 16, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 23, "forwarded-report-count": 23, "forwarded-leave-count": 0, "forwarded-query-count": 16}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 16, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "***************", "up-time": 9850, "entry-flag": "active", "source-flag": "active"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 18, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 24, "forwarded-report-count": 24, "forwarded-leave-count": 0, "forwarded-query-count": 18}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 17, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9847, "entry-flag": "protocol-active", "source-flag": "igmp-active", "ports": {"port": [{"interface": "10GE0/0/2", "up-time": 0, "expire-time": 130, "port-flag": "-D-"}]}}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 14, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 29, "forwarded-report-count": 29, "forwarded-leave-count": 0, "forwarded-query-count": 14}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 18, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9847, "entry-flag": "active", "source-flag": "active"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 13, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 30, "forwarded-report-count": 30, "forwarded-leave-count": 0, "forwarded-query-count": 13}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 19, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9847, "entry-flag": "active", "source-flag": "active"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 16, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 28, "forwarded-report-count": 28, "forwarded-leave-count": 0, "forwarded-query-count": 16}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 20, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9847, "entry-flag": "active", "source-flag": "active"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 16, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 28, "forwarded-report-count": 28, "forwarded-leave-count": 0, "forwarded-query-count": 16}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 21, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "interface-groups": {"interface-group": [{"interface": "10GE0/0/2", "groups": {"group": [{"group-address": "*********", "up-time": 113, "expire-time": 17, "group-timer": "exist", "retran-count": 0, "last-member-query": false, "filter-mode": "exclude", "compat-mode": "V3", "source-last-member-query": false, "last-member-query-timer": "not-exist"}]}}]}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9847, "entry-flag": "protocol-active", "source-flag": "igmp-active", "ports": {"port": [{"interface": "10GE0/0/2", "up-time": 0, "expire-time": 130, "port-flag": "-D-"}]}}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 16, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 26, "forwarded-report-count": 26, "forwarded-leave-count": 0, "forwarded-query-count": 16}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 22, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9845, "entry-flag": "active", "source-flag": "active"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 13, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 22, "forwarded-report-count": 22, "forwarded-leave-count": 0, "forwarded-query-count": 13}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}, {"id": 23, "type": "common", "@mac-learning": "enable", "member-ports": {"member-port": [{"interface-name": "10GE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "10GE0/0/2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "MultiGE0/0/1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk1", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk2", "access-type": "trunk", "state": "up", "tag-mode": "tag"}, {"interface-name": "Eth-Trunk0", "access-type": "trunk", "state": "up", "tag-mode": "tag"}]}, "huawei-arp:arp-security": {"l2proxy-enable": true}, "huawei-dhcp:dhcp-snooping-vlan": {"basic-attribute": {"enable": true, "@alarm-reply-enable": false, "@check-mac-enable": false, "@alarm-mac-enable": false, "@check-user-bind-enable": false, "@alarm-user-bind-enable": false}}, "huawei-igmp-mld-snooping:igmp-snooping": {"global": {"version": 3, "enable": true, "@router-port-discard": false, "@immediately-leave": false, "@proxy-enable": false, "@querier-enable": false, "@query-interval": 60, "@router-aging-time": 180, "querier": {"querier-state": false, "querier-address": ""}}, "group-ports": {"group-port": [{"group-address": "*********", "source-address": "0.0.0.0", "up-time": 9845, "entry-flag": "active", "source-flag": "active"}]}, "statistics": {"send-source-group-query-count": 0, "v1-report-count": 0, "v2-report-count": 0, "v3-report-count": 0, "v1-query-count": 0, "v2-query-count": 0, "v3-query-cnt": 11, "leave-count": 0, "hello-count": 0, "s-flag-query-count": 25, "no-s-flag-query-count": 0, "send-general-query-count": 0, "send-group-query-count": 0, "invalid-count": 0, "ignored-count": 30, "forwarded-report-count": 30, "forwarded-leave-count": 0, "forwarded-query-count": 11}, "invalid-packet": {"general-count": {"fault-length": 0, "invalid-type-count": 0, "bad-checksum": 0, "fault-router-alert": 0}, "invalid-query-count": {"invalid-multicast-source": 0, "invalid-multicast-group": 0}, "invalid-report-count": {"invalid-multicast-group": 0, "invalid-version": 0, "invalid-multicast-source": 0}, "invalid-leave-count": {"invalid-multicast-group": 0, "invalid-version": 0}, "invalid-hello-count": {"invalid-pim-version": 0, "fault-length": 0, "bad-holdtime-length": 0, "bad-dr-priority-length": 0, "bad-checksum": 0, "bad-generation-id-length": 0, "bad-lan-prune-delay-length": 0}}}}]}}}]}]}