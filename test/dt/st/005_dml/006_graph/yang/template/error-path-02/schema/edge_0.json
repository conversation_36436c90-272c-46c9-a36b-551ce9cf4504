[{"name": "ds", "source_vertex_label": "error-path-01", "dest_vertex_label": "ds", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library", "source_vertex_label": "ds", "dest_vertex_label": "ietf-yang-library", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set", "source_vertex_label": "ietf-yang-library", "dest_vertex_label": "ietf-yang-library:yang-library::module-set", "source_node_path": "/ietf-yang-library:yang-library", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module", "source_vertex_label": "ietf-yang-library:yang-library::module-set", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module::submodule", "source_vertex_label": "ietf-yang-library:yang-library::module-set::module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module::submodule", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module::feature", "source_vertex_label": "ietf-yang-library:yang-library::module-set::module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module::feature", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module::deviation", "source_vertex_label": "ietf-yang-library:yang-library::module-set::module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module::deviation", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module", "source_vertex_label": "ietf-yang-library:yang-library::module-set", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::location", "source_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::location", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::submodule", "source_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::submodule", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::submodule::location", "source_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::submodule", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::submodule::location", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::schema", "source_vertex_label": "ietf-yang-library", "dest_vertex_label": "ietf-yang-library:yang-library::schema", "source_node_path": "/ietf-yang-library:yang-library", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::schema::module-set", "source_vertex_label": "ietf-yang-library:yang-library::schema", "dest_vertex_label": "ietf-yang-library:yang-library::schema::module-set", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::datastore", "source_vertex_label": "ietf-yang-library", "dest_vertex_label": "ietf-yang-library:yang-library::datastore", "source_node_path": "/ietf-yang-library:yang-library", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "notifications", "source_vertex_label": "ds", "dest_vertex_label": "notifications", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "nc-notifications", "source_vertex_label": "ds", "dest_vertex_label": "nc-notifications", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "nc-notifications:netconf::streams::stream", "source_vertex_label": "nc-notifications", "dest_vertex_label": "nc-notifications:netconf::streams::stream", "source_node_path": "/nc-notifications:netconf/streams", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry", "source_vertex_label": "ds", "dest_vertex_label": "openconfig-telemetry", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group", "source_vertex_label": "openconfig-telemetry", "dest_vertex_label": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group", "source_node_path": "/openconfig-telemetry:telemetry-system/sensor-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group::sensor-paths::sensor-path", "source_vertex_label": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group", "dest_vertex_label": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group::sensor-paths::sensor-path", "source_node_path": "/sensor-paths", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::destination-groups::destination-group", "source_vertex_label": "openconfig-telemetry", "dest_vertex_label": "openconfig-telemetry:telemetry-system::destination-groups::destination-group", "source_node_path": "/openconfig-telemetry:telemetry-system/destination-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::destination-groups::destination-group::destinations::destination", "source_vertex_label": "openconfig-telemetry:telemetry-system::destination-groups::destination-group", "dest_vertex_label": "openconfig-telemetry:telemetry-system::destination-groups::destination-group::destinations::destination", "source_node_path": "/destinations", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription", "source_vertex_label": "openconfig-telemetry", "dest_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription", "source_node_path": "/openconfig-telemetry:telemetry-system/subscriptions/persistent", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::sensor-profiles::sensor-profile", "source_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription", "dest_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::sensor-profiles::sensor-profile", "source_node_path": "/sensor-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::destination-groups::destination-group", "source_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription", "dest_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::destination-groups::destination-group", "source_node_path": "/destination-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-wired-port-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-wired-port-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-wired-port-profile:wlan-wired-port-profile::wiredport-profiles::wiredport-profile", "source_vertex_label": "huawei-wlan-wired-port-profile", "dest_vertex_label": "huawei-wlan-wired-port-profile:wlan-wired-port-profile::wiredport-profiles::wiredport-profile", "source_node_path": "/huawei-wlan-wired-port-profile:wlan-wired-port-profile/wiredport-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-vlan", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-vlan", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan", "source_vertex_label": "hua<PERSON>-vlan", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan", "source_node_path": "/huawei-vlan:vlan/vlans", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::member-ports::member-port", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::member-ports::member-port", "source_node_path": "/member-ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlanhuawei-mac:mac-addresss::mac-address", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlanhuawei-mac:mac-addresss::mac-address", "source_node_path": "/huawei-mac:mac-addresss", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-arp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-arp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-arp:arp::query-entries::query-entry", "source_vertex_label": "huawei-arp", "dest_vertex_label": "huawei-arp:arp::query-entries::query-entry", "source_node_path": "/huawei-arp:arp/query-entries", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-arp:arp::statistics::statistic", "source_vertex_label": "huawei-arp", "dest_vertex_label": "huawei-arp:arp::statistics::statistic", "source_node_path": "/huawei-arp:arp/statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-mac", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-mac", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mac:mac::vlan-dynamic-macs::vlan-dynamic-mac", "source_vertex_label": "hua<PERSON>-mac", "dest_vertex_label": "huawei-mac:mac::vlan-dynamic-macs::vlan-dynamic-mac", "source_node_path": "/huawei-mac:mac/vlan-dynamic-macs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mac:mac::mac-statistics::mac-statistic", "source_vertex_label": "hua<PERSON>-mac", "dest_vertex_label": "huawei-mac:mac::mac-statistics::mac-statistic", "source_node_path": "/huawei-mac:mac/mac-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm", "source_vertex_label": "ds", "dest_vertex_label": "huawei-ifm", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface", "source_vertex_label": "huawei-ifm", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface", "source_node_path": "/huawei-ifm:ifm/interfaces", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::gateway-list", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::gateway-list", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::dns-list::ip-address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::dns-list::ip-address", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface/dns-list", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::excluded-ip-addresses::excluded-ip-address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::excluded-ip-addresses::excluded-ip-address", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface/excluded-ip-addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::static-binds::static-bind", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::static-binds::static-bind", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface/static-binds", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface/options", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::ip-format::ip-addresses", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::ip-format::ip-addresses", "source_node_path": "/option-format/ip-format", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option", "source_node_path": "/option-format/sub-options-format/sub-options", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option::option-format::sub-ip-format::ip-addresses", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option::option-format::sub-ip-format::ip-addresses", "source_node_path": "/option-format/sub-ip-format", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-ethernet:ethernet::main-interface::port-isolate-groups::port-isolate-group", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-ethernet:ethernet::main-interface::port-isolate-groups::port-isolate-group", "source_node_path": "/huawei-ethernet:ethernet/main-interface/port-isolate-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-if-standby:standby::primary-interface::standby-interfaces::standby-interface", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-if-standby:standby::primary-interface::standby-interfaces::standby-interface", "source_node_path": "/huawei-if-standby:standby/primary-interface/standby-interfaces", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-ifm-trunk:trunk::members::member", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-ifm-trunk:trunk::members::member", "source_node_path": "/huawei-ifm-trunk:trunk/members", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-ip:ipv4::address::common-address::addresses::address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-ip:ipv4::address::common-address::addresses::address", "source_node_path": "/huawei-ip:ipv4/address/common-address/addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-ip:ipv4::state::addresses::address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-ip:ipv4::state::addresses::address", "source_node_path": "/huawei-ip:ipv4/state/addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-ip:ipv4huawei-arp:static-arps::static-arp", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-ip:ipv4huawei-arp:static-arps::static-arp", "source_node_path": "/huawei-ip:ipv4/huawei-arp:static-arps", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor", "source_node_path": "/huawei-lldp:lldp/session/neighbors", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::management-addresss::management-address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::management-addresss::management-address", "source_node_path": "/management-addresss", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::protocol-vlans::protocol-vlan", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::protocol-vlans::protocol-vlan", "source_node_path": "/protocol-vlans", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::vlan-names::vlan-name", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::vlan-names::vlan-name", "source_node_path": "/vlan-names", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::unknown-tlvs::unknown-tlv", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::unknown-tlvs::unknown-tlv", "source_node_path": "/unknown-tlvs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::unknown-organizationally-defined-tlvs::unknown-organizationally-defined-tlv", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::unknown-organizationally-defined-tlvs::unknown-organizationally-defined-tlv", "source_node_path": "/unknown-organizationally-defined-tlvs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::med-tlv::capability::capabilities", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::med-tlv::capability::capabilities", "source_node_path": "/med-tlv/capability", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::med-tlv::network-policys::network-policy", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::med-tlv::network-policys::network-policy", "source_node_path": "/med-tlv/network-policys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::legacy-power-capability::capability", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-lldp:lldp::session::neighbors::neighbor::legacy-power-capability::capability", "source_node_path": "/legacy-power-capability", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-pppoe-client:pppoe-client-session-summarys::pppoe-client-session-summary", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-pppoe-client:pppoe-client-session-summarys::pppoe-client-session-summary", "source_node_path": "/huawei-pppoe-client:pppoe-client-session-summarys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-filter-applys::traffic-filter-apply", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-filter-applys::traffic-filter-apply", "source_node_path": "/huawei-sacl:traffic-filter-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-filter-applys::traffic-filter-apply", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance::statistics::statistic", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance::statistics::statistic", "source_node_path": "/statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-statistics-applys::traffic-statistics-apply", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-statistics-applys::traffic-statistics-apply", "source_node_path": "/huawei-sacl:traffic-statistics-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-statistics-applys::traffic-statistics-apply", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance::statistics::statistic", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance::statistics::statistic", "source_node_path": "/statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-remark-applys::traffic-remark-apply", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-remark-applys::traffic-remark-apply", "source_node_path": "/huawei-sacl:traffic-remark-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-remark-applys::traffic-remark-apply", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance::statuses::status", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance::statuses::status", "source_node_path": "/statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-limit-applys::traffic-limit-apply", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-limit-applys::traffic-limit-apply", "source_node_path": "/huawei-sacl:traffic-limit-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-limit-applys::traffic-limit-apply", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance::statuses::status", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance::statuses::status", "source_node_path": "/statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-redirect-applys::traffic-redirect-apply", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-redirect-applys::traffic-redirect-apply", "source_node_path": "/huawei-sacl:traffic-redirect-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-redirect-applys::traffic-redirect-apply", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance::statuses::status", "source_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance::statuses::status", "source_node_path": "/statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-storm-control:storm-control::storm-rates::storm-rate", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-storm-control:storm-control::storm-rates::storm-rate", "source_node_path": "/huawei-storm-control:storm-control/storm-rates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interfacehuawei-storm-control:storm-suppress::storm-rates::storm-rate", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interfacehuawei-storm-control:storm-suppress::storm-rates::storm-rate", "source_node_path": "/huawei-storm-control:storm-suppress/storm-rates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-dhcp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp:dhcp::server::ip-pool-querys::ip-pool-query", "source_vertex_label": "huawei-dhcp", "dest_vertex_label": "huawei-dhcp:dhcp::server::ip-pool-querys::ip-pool-query", "source_node_path": "/huawei-dhcp:dhcp/server/ip-pool-querys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mstp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-mstp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mstp:mstp::default-process::cist-info::cist-port-infos::cist-port-info", "source_vertex_label": "huawei-mstp", "dest_vertex_label": "huawei-mstp:mstp::default-process::cist-info::cist-port-infos::cist-port-info", "source_node_path": "/huawei-mstp:mstp/default-process/cist-info/cist-port-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mstp:mstp::default-process::interface-bpdu-statistics::interface-bpdu-statistic", "source_vertex_label": "huawei-mstp", "dest_vertex_label": "huawei-mstp:mstp::default-process::interface-bpdu-statistics::interface-bpdu-statistic", "source_node_path": "/huawei-mstp:mstp/default-process/interface-bpdu-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm-trunk", "source_vertex_label": "ds", "dest_vertex_label": "huawei-ifm-trunk", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-lldp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-lldp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-lldp:lldp::local-info::system-capabilities-supported", "source_vertex_label": "huawei-lldp", "dest_vertex_label": "huawei-lldp:lldp::local-info::system-capabilities-supported", "source_node_path": "/huawei-lldp:lldp/local-info", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-lldp:lldp::local-info::system-capabilities-enabled", "source_vertex_label": "huawei-lldp", "dest_vertex_label": "huawei-lldp:lldp::local-info::system-capabilities-enabled", "source_node_path": "/huawei-lldp:lldp/local-info", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-lldp:lldp::local-info::management-addresss::management-address", "source_vertex_label": "huawei-lldp", "dest_vertex_label": "huawei-lldp:lldp::local-info::management-addresss::management-address", "source_node_path": "/huawei-lldp:lldp/local-info/management-addresss", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-loadbalance", "source_vertex_label": "ds", "dest_vertex_label": "huawei-loadbalance", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pppoe-client", "source_vertex_label": "ds", "dest_vertex_label": "huawei-pppoe-client", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl", "source_vertex_label": "ds", "dest_vertex_label": "huawei-sacl", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-filter-applys::traffic-filter-apply", "source_vertex_label": "huawei-sacl", "dest_vertex_label": "huawei-sacl:sacl::traffic-filter-applys::traffic-filter-apply", "source_node_path": "/huawei-sacl:sacl/traffic-filter-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-sacl:sacl::traffic-filter-applys::traffic-filter-apply", "dest_vertex_label": "huawei-sacl:sacl::traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance::statistics::statistic", "source_vertex_label": "huawei-sacl:sacl::traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-sacl:sacl::traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance::statistics::statistic", "source_node_path": "/statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-remark-applys::traffic-remark-apply", "source_vertex_label": "huawei-sacl", "dest_vertex_label": "huawei-sacl:sacl::traffic-remark-applys::traffic-remark-apply", "source_node_path": "/huawei-sacl:sacl/traffic-remark-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-sacl:sacl::traffic-remark-applys::traffic-remark-apply", "dest_vertex_label": "huawei-sacl:sacl::traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance::statuses::status", "source_vertex_label": "huawei-sacl:sacl::traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-sacl:sacl::traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance::statuses::status", "source_node_path": "/statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-limit-applys::traffic-limit-apply", "source_vertex_label": "huawei-sacl", "dest_vertex_label": "huawei-sacl:sacl::traffic-limit-applys::traffic-limit-apply", "source_node_path": "/huawei-sacl:sacl/traffic-limit-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-sacl:sacl::traffic-limit-applys::traffic-limit-apply", "dest_vertex_label": "huawei-sacl:sacl::traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance::statuses::status", "source_vertex_label": "huawei-sacl:sacl::traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-sacl:sacl::traffic-limit-applys::traffic-limit-apply::acl-instances::acl-instance::statuses::status", "source_node_path": "/statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-statistics-applys::traffic-statistics-apply", "source_vertex_label": "huawei-sacl", "dest_vertex_label": "huawei-sacl:sacl::traffic-statistics-applys::traffic-statistics-apply", "source_node_path": "/huawei-sacl:sacl/traffic-statistics-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-sacl:sacl::traffic-statistics-applys::traffic-statistics-apply", "dest_vertex_label": "huawei-sacl:sacl::traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance::statistics::statistic", "source_vertex_label": "huawei-sacl:sacl::traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-sacl:sacl::traffic-statistics-applys::traffic-statistics-apply::acl-instances::acl-instance::statistics::statistic", "source_node_path": "/statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-redirect-applys::traffic-redirect-apply", "source_vertex_label": "huawei-sacl", "dest_vertex_label": "huawei-sacl:sacl::traffic-redirect-applys::traffic-redirect-apply", "source_node_path": "/huawei-sacl:sacl/traffic-redirect-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-sacl:sacl::traffic-redirect-applys::traffic-redirect-apply", "dest_vertex_label": "huawei-sacl:sacl::traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sacl:sacl::traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance::statuses::status", "source_vertex_label": "huawei-sacl:sacl::traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-sacl:sacl::traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance::statuses::status", "source_node_path": "/statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance", "source_vertex_label": "ds", "dest_vertex_label": "huawei-network-instance", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance", "source_vertex_label": "huawei-network-instance", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance", "source_node_path": "/huawei-network-instance:network-instance/instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::af", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::af", "source_node_path": "/huawei-l3vpn:afs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::routing-manage::topologys::topology", "source_vertex_label": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::af", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::routing-manage::topologys::topology", "source_node_path": "/huawei-routing:routing/routing-manage/topologys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-unicast-routes::ipv4-unicast-route", "source_vertex_label": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::routing-manage::topologys::topology", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-unicast-routes::ipv4-unicast-route", "source_node_path": "/routes/ipv4-unicast-routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-route-statistics::ipv4-route-statistic", "source_vertex_label": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::routing-manage::topologys::topology", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-route-statistics::ipv4-route-statistic", "source_node_path": "/routes/ipv4-route-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "source_vertex_label": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::af", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "source_node_path": "/huawei-routing:routing/static-routing/unicast-route2s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interfaces::nexthop-interface", "source_vertex_label": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interfaces::nexthop-interface", "source_node_path": "/nexthop-interfaces", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address", "source_vertex_label": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address", "source_node_path": "/nexthop-interface-addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-addresses::nexthop-address", "source_vertex_label": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-addresses::nexthop-address", "source_node_path": "/nexthop-addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::ipv4-routes::ipv4-route", "source_vertex_label": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::af", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instancehuawei-l3vpn:afs::afhuawei-routing:routing::static-routing::ipv4-routes::ipv4-route", "source_node_path": "/huawei-routing:routing/static-routing/ipv4-routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-routing", "source_vertex_label": "ds", "dest_vertex_label": "huawei-routing", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-ap", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:wlan-ap::ap-mac-whitelists::ap-mac-whitelist", "source_vertex_label": "huawei-wlan-ap", "dest_vertex_label": "huawei-wlan-ap:wlan-ap::ap-mac-whitelists::ap-mac-whitelist", "source_node_path": "/huawei-wlan-ap:wlan-ap/ap-mac-whitelists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:wlan-ap::ap-types::ap-type", "source_vertex_label": "huawei-wlan-ap", "dest_vertex_label": "huawei-wlan-ap:wlan-ap::ap-types::ap-type", "source_node_path": "/huawei-wlan-ap:wlan-ap/ap-types", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance", "source_vertex_label": "huawei-wlan-ap", "dest_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance", "source_node_path": "/huawei-wlan-ap:wlan-ap/ap-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instancehuawei-wlan-ap-radio:radio-instances::radio-instance", "source_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance", "dest_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instancehuawei-wlan-ap-radio:radio-instances::radio-instance", "source_node_path": "/huawei-wlan-ap-radio:radio-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instancehuawei-wlan-ap-radio:radio-instances::radio-instancehuawei-wlan-vap-inst:vap-instances::vap-instance", "source_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instancehuawei-wlan-ap-radio:radio-instances::radio-instance", "dest_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instancehuawei-wlan-ap-radio:radio-instances::radio-instancehuawei-wlan-vap-inst:vap-instances::vap-instance", "source_node_path": "/huawei-wlan-vap-inst:vap-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instancehuawei-wlan-wired-port-profile:ge-instances::ge-instance", "source_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instance", "dest_vertex_label": "huawei-wlan-ap:wlan-ap::ap-instances::ap-instancehuawei-wlan-wired-port-profile:ge-instances::ge-instance", "source_node_path": "/huawei-wlan-wired-port-profile:ge-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap:get-ap-connect-fail-reason::ap-instances::ap-instance", "source_vertex_label": "huawei-wlan-ap", "dest_vertex_label": "huawei-wlan-ap:get-ap-connect-fail-reason::ap-instances::ap-instance", "source_node_path": "/huawei-wlan-ap:get-ap-connect-fail-reason/ap-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap-group-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-ap-group-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile", "source_vertex_label": "huawei-wlan-ap-group-profile", "dest_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile", "source_node_path": "/huawei-wlan-ap-group-profile:wlan-ap-group-profile/ap-group-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profilehuawei-wlan-group-radio:radio-instances::radio-instance", "source_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile", "dest_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profilehuawei-wlan-group-radio:radio-instances::radio-instance", "source_node_path": "/huawei-wlan-group-radio:radio-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profilehuawei-wlan-group-radio:radio-instances::radio-instancehuawei-wlan-vap-inst:vap-instances::vap-instance", "source_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profilehuawei-wlan-group-radio:radio-instances::radio-instance", "dest_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profilehuawei-wlan-group-radio:radio-instances::radio-instancehuawei-wlan-vap-inst:vap-instances::vap-instance", "source_node_path": "/huawei-wlan-vap-inst:vap-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profilehuawei-wlan-wired-port-profile:ge-instances::ge-instance", "source_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profile", "dest_vertex_label": "huawei-wlan-ap-group-profile:wlan-ap-group-profile::ap-group-profiles::ap-group-profilehuawei-wlan-wired-port-profile:ge-instances::ge-instance", "source_node_path": "/huawei-wlan-wired-port-profile:ge-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-2g-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-radio-2g-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile", "source_vertex_label": "huawei-wlan-radio-2g-profile", "dest_vertex_label": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile", "source_node_path": "/huawei-wlan-radio-2g-profile:wlan-radio-2g-profile/radio-2g-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile::dot11bg-basic-rate-lists::dot11bg-basic-rate-list", "source_vertex_label": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile", "dest_vertex_label": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile::dot11bg-basic-rate-lists::dot11bg-basic-rate-list", "source_node_path": "/dot11bg-basic-rate-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile::dot11bg-support-rate-lists::dot11bg-support-rate-list", "source_vertex_label": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile", "dest_vertex_label": "huawei-wlan-radio-2g-profile:wlan-radio-2g-profile::radio-2g-profiles::radio-2g-profile::dot11bg-support-rate-lists::dot11bg-support-rate-list", "source_node_path": "/dot11bg-support-rate-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-5g-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-radio-5g-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile", "source_vertex_label": "huawei-wlan-radio-5g-profile", "dest_vertex_label": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile", "source_node_path": "/huawei-wlan-radio-5g-profile:wlan-radio-5g-profile/radio-5g-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile::dot11a-basic-rate-lists::dot11a-basic-rate-list", "source_vertex_label": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile", "dest_vertex_label": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile::dot11a-basic-rate-lists::dot11a-basic-rate-list", "source_node_path": "/dot11a-basic-rate-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile::dot11a-support-rate-lists::dot11a-support-rate-list", "source_vertex_label": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile", "dest_vertex_label": "huawei-wlan-radio-5g-profile:wlan-radio-5g-profile::radio-5g-profiles::radio-5g-profile::dot11a-support-rate-lists::dot11a-support-rate-list", "source_node_path": "/dot11a-support-rate-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-reg-dom-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-reg-dom-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-reg-dom-profile:wlan-reg-dom-profile::regulatory-domain-profiles::regulatory-domain-profile", "source_vertex_label": "huawei-wlan-reg-dom-profile", "dest_vertex_label": "huawei-wlan-reg-dom-profile:wlan-reg-dom-profile::regulatory-domain-profiles::regulatory-domain-profile", "source_node_path": "/huawei-wlan-reg-dom-profile:wlan-reg-dom-profile/regulatory-domain-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-system-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-system-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile", "source_vertex_label": "huawei-wlan-system-profile", "dest_vertex_label": "huawei-wlan-system-profile:wlan-system-profile::system-profiles::system-profile", "source_node_path": "/huawei-wlan-system-profile:wlan-system-profile/system-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-vap-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-vap-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-vap-profile:wlan-vap-profile::vap-profiles::vap-profile", "source_vertex_label": "huawei-wlan-vap-profile", "dest_vertex_label": "huawei-wlan-vap-profile:wlan-vap-profile::vap-profiles::vap-profile", "source_node_path": "/huawei-wlan-vap-profile:wlan-vap-profile/vap-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-wlan-security-profile", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-wlan-security-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-security-profile:wlan-security-profile::security-profiles::security-profile", "source_vertex_label": "hua<PERSON>-wlan-security-profile", "dest_vertex_label": "huawei-wlan-security-profile:wlan-security-profile::security-profiles::security-profile", "source_node_path": "/huawei-wlan-security-profile:wlan-security-profile/security-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ssid-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-ssid-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-ssid-profile:wlan-ssid-profile::ssid-profiles::ssid-profile", "source_vertex_label": "huawei-wlan-ssid-profile", "dest_vertex_label": "huawei-wlan-ssid-profile:wlan-ssid-profile::ssid-profiles::ssid-profile", "source_node_path": "/huawei-wlan-ssid-profile:wlan-ssid-profile/ssid-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-traffic-profile", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-traffic-profile", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile", "source_vertex_label": "huawei-wlan-traffic-profile", "dest_vertex_label": "huawei-wlan-traffic-profile:wlan-traffic-profile::traffic-profiles::traffic-profile", "source_node_path": "/huawei-wlan-traffic-profile:wlan-traffic-profile/traffic-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-calibrate", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-radio-calibrate", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-calibrate:wlan-radio-calibrate::calibrate::sensitivitys::sensitivity", "source_vertex_label": "huawei-wlan-radio-calibrate", "dest_vertex_label": "huawei-wlan-radio-calibrate:wlan-radio-calibrate::calibrate::sensitivitys::sensitivity", "source_node_path": "/huawei-wlan-radio-calibrate:wlan-radio-calibrate/calibrate/sensitivitys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-calibrate:calibrate-manual::type::ap-list::ap-lists::ap-list", "source_vertex_label": "huawei-wlan-radio-calibrate", "dest_vertex_label": "huawei-wlan-radio-calibrate:calibrate-manual::type::ap-list::ap-lists::ap-list", "source_node_path": "/huawei-wlan-radio-calibrate:calibrate-manual/type/ap-list/ap-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-radio-calibrate:calibrate-manual::type::ap-group-list::ap-group-lists::ap-group-list", "source_vertex_label": "huawei-wlan-radio-calibrate", "dest_vertex_label": "huawei-wlan-radio-calibrate:calibrate-manual::type::ap-group-list::ap-group-lists::ap-group-list", "source_node_path": "/huawei-wlan-radio-calibrate:calibrate-manual/type/ap-group-list/ap-group-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-time-range", "source_vertex_label": "ds", "dest_vertex_label": "huawei-time-range", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance", "source_vertex_label": "huawei-time-range", "dest_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance", "source_node_path": "/huawei-time-range:time-range/time-range-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance::absolute-ranges::absolute-range", "source_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance", "dest_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance::absolute-ranges::absolute-range", "source_node_path": "/absolute-ranges", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance::period-ranges::period-range", "source_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance", "dest_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance::period-ranges::period-range", "source_node_path": "/period-ranges", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-wlan-management", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-wlan-management", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-management:wlan-management::ap-type-update-files::ap-type-update-file", "source_vertex_label": "hua<PERSON>-wlan-management", "dest_vertex_label": "huawei-wlan-management:wlan-management::ap-type-update-files::ap-type-update-file", "source_node_path": "/huawei-wlan-management:wlan-management/ap-type-update-files", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-management:wlan-management::ap-type-group-update-files::ap-type-group-update-file", "source_vertex_label": "hua<PERSON>-wlan-management", "dest_vertex_label": "huawei-wlan-management:wlan-management::ap-type-group-update-files::ap-type-group-update-file", "source_node_path": "/huawei-wlan-management:wlan-management/ap-type-group-update-files", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-management:wlan-management::ap-type-update-patchs::ap-type-update-patch", "source_vertex_label": "hua<PERSON>-wlan-management", "dest_vertex_label": "huawei-wlan-management:wlan-management::ap-type-update-patchs::ap-type-update-patch", "source_node_path": "/huawei-wlan-management:wlan-management/ap-type-update-patchs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-management:wlan-management::ap-type-group-update-patchs::ap-type-group-update-patch", "source_vertex_label": "hua<PERSON>-wlan-management", "dest_vertex_label": "huawei-wlan-management:wlan-management::ap-type-group-update-patchs::ap-type-group-update-patch", "source_node_path": "/huawei-wlan-management:wlan-management/ap-type-group-update-patchs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-wlan-capwap", "source_vertex_label": "ds", "dest_vertex_label": "huawei-wlan-capwap", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-tm", "source_vertex_label": "ds", "dest_vertex_label": "huawei-tm", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-terminal-identify", "source_vertex_label": "ds", "dest_vertex_label": "huawei-terminal-identify", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-terminal-identify:terminal-identify::terminals::terminal", "source_vertex_label": "huawei-terminal-identify", "dest_vertex_label": "huawei-terminal-identify:terminal-identify::terminals::terminal", "source_node_path": "/huawei-terminal-identify:terminal-identify/terminals", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-terminal-identify:terminal-identify::monitoring-scan::vlans::vlan", "source_vertex_label": "huawei-terminal-identify", "dest_vertex_label": "huawei-terminal-identify:terminal-identify::monitoring-scan::vlans::vlan", "source_node_path": "/huawei-terminal-identify:terminal-identify/monitoring-scan/vlans", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-terminal-identify:terminal-identify::periodic-scan::vlans::vlan", "source_vertex_label": "huawei-terminal-identify", "dest_vertex_label": "huawei-terminal-identify:terminal-identify::periodic-scan::vlans::vlan", "source_node_path": "/huawei-terminal-identify:terminal-identify/periodic-scan/vlans", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-terminal-identify:terminal-identify::identified-terminals::identified-terminal", "source_vertex_label": "huawei-terminal-identify", "dest_vertex_label": "huawei-terminal-identify:terminal-identify::identified-terminals::identified-terminal", "source_node_path": "/huawei-terminal-identify:terminal-identify/identified-terminals", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-terminal-identify:start-immediate-scan::scopes::scope", "source_vertex_label": "huawei-terminal-identify", "dest_vertex_label": "huawei-terminal-identify:start-immediate-scan::scopes::scope", "source_node_path": "/huawei-terminal-identify:start-immediate-scan/scopes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system", "source_vertex_label": "ds", "dest_vertex_label": "huawei-system", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system:system::system-infohuawei-system-controller:upstream-info::if-name", "source_vertex_label": "huawei-system", "dest_vertex_label": "huawei-system:system::system-infohuawei-system-controller:upstream-info::if-name", "source_node_path": "/huawei-system:system/system-info/huawei-system-controller:upstream-info", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system:system::security-risks::security-risk", "source_vertex_label": "huawei-system", "dest_vertex_label": "huawei-system:system::security-risks::security-risk", "source_node_path": "/huawei-system:system/security-risks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system:system::weak-passwords::weak-password", "source_vertex_label": "huawei-system", "dest_vertex_label": "huawei-system:system::weak-passwords::weak-password", "source_node_path": "/huawei-system:system/weak-passwords", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system-controller", "source_vertex_label": "ds", "dest_vertex_label": "huawei-system-controller", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system-controller:system-controller::offline-records::offline-record", "source_vertex_label": "huawei-system-controller", "dest_vertex_label": "huawei-system-controller:system-controller::offline-records::offline-record", "source_node_path": "/huawei-system-controller:system-controller/offline-records", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system-controller:system-controller::register-fail-records::register-fail-record", "source_vertex_label": "huawei-system-controller", "dest_vertex_label": "huawei-system-controller:system-controller::register-fail-records::register-fail-record", "source_node_path": "/huawei-system-controller:system-controller/register-fail-records", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-syslog", "source_vertex_label": "ds", "dest_vertex_label": "huawei-syslog", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-syslog:syslog::log-switch-list::log-switch", "source_vertex_label": "huawei-syslog", "dest_vertex_label": "huawei-syslog:syslog::log-switch-list::log-switch", "source_node_path": "/huawei-syslog:syslog/log-switch-list", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-syslog:syslog::logfiles::logfile", "source_vertex_label": "huawei-syslog", "dest_vertex_label": "huawei-syslog:syslog::logfiles::logfile", "source_node_path": "/huawei-syslog:syslog/logfiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-syslog:syslog::logfiles::logfile::latest-logs::latest-log", "source_vertex_label": "huawei-syslog:syslog::logfiles::logfile", "dest_vertex_label": "huawei-syslog:syslog::logfiles::logfile::latest-logs::latest-log", "source_node_path": "/latest-logs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ssl", "source_vertex_label": "ds", "dest_vertex_label": "huawei-ssl", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ssl:ssl::ssl-policys::ssl-policy", "source_vertex_label": "huawei-ssl", "dest_vertex_label": "huawei-ssl:ssl::ssl-policys::ssl-policy", "source_node_path": "/huawei-ssl:ssl/ssl-policys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshs", "source_vertex_label": "ds", "dest_vertex_label": "huawei-sshs", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshs:sshs::users::user", "source_vertex_label": "huawei-sshs", "dest_vertex_label": "huawei-sshs:sshs::users::user", "source_node_path": "/huawei-sshs:sshs/users", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshs:sshs::ipv4-server-sources::ipv4-server-source", "source_vertex_label": "huawei-sshs", "dest_vertex_label": "huawei-sshs:sshs::ipv4-server-sources::ipv4-server-source", "source_node_path": "/huawei-sshs:sshs/ipv4-server-sources", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshs:sshs::call-homes::call-home", "source_vertex_label": "huawei-sshs", "dest_vertex_label": "huawei-sshs:sshs::call-homes::call-home", "source_node_path": "/huawei-sshs:sshs/call-homes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshs:sshs::call-homes::call-home::end-points::end-point", "source_vertex_label": "huawei-sshs:sshs::call-homes::call-home", "dest_vertex_label": "huawei-sshs:sshs::call-homes::call-home::end-points::end-point", "source_node_path": "/end-points", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa", "source_vertex_label": "ds", "dest_vertex_label": "huawei-aaa", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::alive-user-qrys::alive-user-qry", "source_vertex_label": "huawei-aaa", "dest_vertex_label": "huawei-aaa:aaa::alive-user-qrys::alive-user-qry", "source_node_path": "/huawei-aaa:aaa/alive-user-qrys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::lam::users::user", "source_vertex_label": "huawei-aaa", "dest_vertex_label": "huawei-aaa:aaa::lam::users::user", "source_node_path": "/huawei-aaa:aaa/lam/users", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshc", "source_vertex_label": "ds", "dest_vertex_label": "huawei-sshc", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshc:sshc::transfer-results::transfer-result", "source_vertex_label": "huawei-sshc", "dest_vertex_label": "huawei-sshc:sshc::transfer-results::transfer-result", "source_node_path": "/huawei-sshc:sshc/transfer-results", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshc:sshc::transfer-tasks::transfer-task", "source_vertex_label": "huawei-sshc", "dest_vertex_label": "huawei-sshc:sshc::transfer-tasks::transfer-task", "source_node_path": "/huawei-sshc:sshc/transfer-tasks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software", "source_vertex_label": "ds", "dest_vertex_label": "huawei-software", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software:software::versions::version", "source_vertex_label": "huawei-software", "dest_vertex_label": "huawei-software:software::versions::version", "source_node_path": "/huawei-software:software/versions", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software:software::startup-packages::startup-package", "source_vertex_label": "huawei-software", "dest_vertex_label": "huawei-software:software::startup-packages::startup-package", "source_node_path": "/huawei-software:software/startup-packages", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software:software::packages::package", "source_vertex_label": "huawei-software", "dest_vertex_label": "huawei-software:software::packages::package", "source_node_path": "/huawei-software:software/packages", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software:software::operation-schedules::operation-schedule", "source_vertex_label": "huawei-software", "dest_vertex_label": "huawei-software:software::operation-schedules::operation-schedule", "source_node_path": "/huawei-software:software/operation-schedules", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-socket", "source_vertex_label": "ds", "dest_vertex_label": "huawei-socket", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-smart-upgrade", "source_vertex_label": "ds", "dest_vertex_label": "huawei-smart-upgrade", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info", "source_vertex_label": "huawei-smart-upgrade", "dest_vertex_label": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info", "source_node_path": "/huawei-smart-upgrade:smart-upgrade/smart-upgrade-info/download-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info::download-lists::download-list", "source_vertex_label": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info", "dest_vertex_label": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info::download-lists::download-list", "source_node_path": "/download-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-smart-upgrade:download-software::type::specify-software::version-lists::version-list", "source_vertex_label": "huawei-smart-upgrade", "dest_vertex_label": "huawei-smart-upgrade:download-software::type::specify-software::version-lists::version-list", "source_node_path": "/huawei-smart-upgrade:download-software/type/specify-software/version-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sec-session-mgmt", "source_vertex_label": "ds", "dest_vertex_label": "huawei-sec-session-mgmt", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sec-session-mgmt:sec-session-mgmt::protocol-ttls::protocol-ttl", "source_vertex_label": "huawei-sec-session-mgmt", "dest_vertex_label": "huawei-sec-session-mgmt:sec-session-mgmt::protocol-ttls::protocol-ttl", "source_node_path": "/huawei-sec-session-mgmt:sec-session-mgmt/protocol-ttls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sec-session-mgmt:sec-session-mgmt::application-ttls::application-ttl", "source_vertex_label": "huawei-sec-session-mgmt", "dest_vertex_label": "huawei-sec-session-mgmt:sec-session-mgmt::application-ttls::application-ttl", "source_node_path": "/huawei-sec-session-mgmt:sec-session-mgmt/application-ttls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl", "source_vertex_label": "ds", "dest_vertex_label": "huawei-acl", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::groups::group", "source_vertex_label": "huawei-acl", "dest_vertex_label": "huawei-acl:acl::groups::group", "source_node_path": "/huawei-acl:acl/groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::groups::group::rule-basics::rule-basic", "source_vertex_label": "huawei-acl:acl::groups::group", "dest_vertex_label": "huawei-acl:acl::groups::group::rule-basics::rule-basic", "source_node_path": "/rule-basics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::groups::group::rule-advances::rule-advance", "source_vertex_label": "huawei-acl:acl::groups::group", "dest_vertex_label": "huawei-acl:acl::groups::group::rule-advances::rule-advance", "source_node_path": "/rule-advances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::groups::group::rule-ethernets::rule-ethernet", "source_vertex_label": "huawei-acl:acl::groups::group", "dest_vertex_label": "huawei-acl:acl::groups::group::rule-ethernets::rule-ethernet", "source_node_path": "/rule-ethernets", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm", "source_vertex_label": "ds", "dest_vertex_label": "huawei-devm", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm:devm::physical-entitys::physical-entity", "source_vertex_label": "huawei-devm", "dest_vertex_label": "huawei-devm:devm::physical-entitys::physical-entity", "source_node_path": "/huawei-devm:devm/physical-entitys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm:devm::mpu-boards::mpu-board", "source_vertex_label": "huawei-devm", "dest_vertex_label": "huawei-devm:devm::mpu-boards::mpu-board", "source_node_path": "/huawei-devm:devm/mpu-boards", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm:devm::ports::port", "source_vertex_label": "huawei-devm", "dest_vertex_label": "huawei-devm:devm::ports::port", "source_node_path": "/huawei-devm:devm/ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki", "source_vertex_label": "ds", "dest_vertex_label": "huawei-pki", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::entitys::entity", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::entitys::entity", "source_node_path": "/huawei-pki:pki/entitys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::domains::domain", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::domains::domain", "source_node_path": "/huawei-pki:pki/domains", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::certificate-infos::certificate-info", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::certificate-infos::certificate-info", "source_node_path": "/huawei-pki:pki/certificate-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::certificate-infos::certificate-info::certificates::certificate", "source_vertex_label": "huawei-pki:pki::certificate-infos::certificate-info", "dest_vertex_label": "huawei-pki:pki::certificate-infos::certificate-info::certificates::certificate", "source_node_path": "/certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info", "source_node_path": "/huawei-pki:pki/preset-certificate-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info::certificates::certificate", "source_vertex_label": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info", "dest_vertex_label": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info::certificates::certificate", "source_node_path": "/certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::crl-infos::crl-info", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::crl-infos::crl-info", "source_node_path": "/huawei-pki:pki/crl-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::key-pair-infos::key-pair-info", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::key-pair-infos::key-pair-info", "source_node_path": "/huawei-pki:pki/key-pair-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info", "source_node_path": "/huawei-pki:pki/cert-key-pair-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info::cert-key-pairs::cert-key-pair", "source_vertex_label": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info", "dest_vertex_label": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info::cert-key-pairs::cert-key-pair", "source_node_path": "/cert-key-pairs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:key-pair-create::key-pairs::key-pair", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:key-pair-create::key-pairs::key-pair", "source_node_path": "/huawei-pki:key-pair-create/key-pairs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:key-pair-destroy::key-pairs::key-pair", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:key-pair-destroy::key-pairs::key-pair", "source_node_path": "/huawei-pki:key-pair-destroy/key-pairs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:key-pair-import::key-pairs::key-pair", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:key-pair-import::key-pairs::key-pair", "source_node_path": "/huawei-pki:key-pair-import/key-pairs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:certificate-import::certificates::certificate", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:certificate-import::certificates::certificate", "source_node_path": "/huawei-pki:certificate-import/certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:certificate-delete::certificates::certificate", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:certificate-delete::certificates::certificate", "source_node_path": "/huawei-pki:certificate-delete/certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:certificate-delete-by-domain::domain-certificates::domain-certificate", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:certificate-delete-by-domain::domain-certificates::domain-certificate", "source_node_path": "/huawei-pki:certificate-delete-by-domain/domain-certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:certificate-replace::certificates::certificate", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:certificate-replace::certificates::certificate", "source_node_path": "/huawei-pki:certificate-replace/certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:csr-generate::csrs::csr", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:csr-generate::csrs::csr", "source_node_path": "/huawei-pki:csr-generate/csrs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:crl-import::crls::crl", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:crl-import::crls::crl", "source_node_path": "/huawei-pki:crl-import/crls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:crl-delete-by-domain::crls::crl", "source_vertex_label": "huawei-pki", "dest_vertex_label": "huawei-pki:crl-delete-by-domain::crls::crl", "source_node_path": "/huawei-pki:crl-delete-by-domain/crls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "<PERSON><PERSON><PERSON>-driver", "source_vertex_label": "ds", "dest_vertex_label": "<PERSON><PERSON><PERSON>-driver", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-driver:driver::temperature2s::temperature2", "source_vertex_label": "<PERSON><PERSON><PERSON>-driver", "dest_vertex_label": "huawei-driver:driver::temperature2s::temperature2", "source_node_path": "/huawei-driver:driver/temperature2s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-driver:driver::device-health-checks::device-health-check", "source_vertex_label": "<PERSON><PERSON><PERSON>-driver", "dest_vertex_label": "huawei-driver:driver::device-health-checks::device-health-check", "source_node_path": "/huawei-driver:driver/device-health-checks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-driver:driver::electronic-labels::electronic-label", "source_vertex_label": "<PERSON><PERSON><PERSON>-driver", "dest_vertex_label": "huawei-driver:driver::electronic-labels::electronic-label", "source_node_path": "/huawei-driver:driver/electronic-labels", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-patch", "source_vertex_label": "ds", "dest_vertex_label": "huawei-patch", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-patch:patch::patch-infos::patch-info", "source_vertex_label": "huawei-patch", "dest_vertex_label": "huawei-patch:patch::patch-infos::patch-info", "source_node_path": "/huawei-patch:patch/patch-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-patch:patch::patch-infos::patch-info::operations::operation", "source_vertex_label": "huawei-patch:patch::patch-infos::patch-info", "dest_vertex_label": "huawei-patch:patch::patch-infos::patch-info::operations::operation", "source_node_path": "/operations", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-patch:patch::next-startup-patchs::next-startup-patch", "source_vertex_label": "huawei-patch", "dest_vertex_label": "huawei-patch:patch::next-startup-patchs::next-startup-patch", "source_node_path": "/huawei-patch:patch/next-startup-patchs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-patch:patch::operation-schedules::operation-schedule", "source_vertex_label": "huawei-patch", "dest_vertex_label": "huawei-patch:patch::operation-schedules::operation-schedule", "source_node_path": "/huawei-patch:patch/operation-schedules", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-file-operation", "source_vertex_label": "ds", "dest_vertex_label": "huawei-file-operation", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-file-operation:file-operation::dirs::dir", "source_vertex_label": "huawei-file-operation", "dest_vertex_label": "huawei-file-operation:file-operation::dirs::dir", "source_node_path": "/huawei-file-operation:file-operation/dirs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ntp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-ntp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ntp:ntp::unicasts::unicast", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "huawei-ntp:ntp::unicasts::unicast", "source_node_path": "/huawei-ntp:ntp/unicasts", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ntp:ntp::authentications::authentication", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "huawei-ntp:ntp::authentications::authentication", "source_node_path": "/huawei-ntp:ntp/authentications", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ntp:ntp::full-sessions::full-session", "source_vertex_label": "huawei-ntp", "dest_vertex_label": "huawei-ntp:ntp::full-sessions::full-session", "source_node_path": "/huawei-ntp:ntp/full-sessions", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-netconf-sync", "source_vertex_label": "ds", "dest_vertex_label": "huawei-netconf-sync", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-server", "source_vertex_label": "ds", "dest_vertex_label": "huawei-nat-server", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-server:nat-server::server-mappings::server-mapping", "source_vertex_label": "huawei-nat-server", "dest_vertex_label": "huawei-nat-server:nat-server::server-mappings::server-mapping", "source_node_path": "/huawei-nat-server:nat-server/server-mappings", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy", "source_vertex_label": "ds", "dest_vertex_label": "huawei-nat-policy", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule", "source_vertex_label": "huawei-nat-policy", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "source_node_path": "/huawei-nat-policy:nat-policy/rules", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::egress::interfaces::egress-interface", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::egress::interfaces::egress-interface", "source_node_path": "/egress/interfaces", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4s::address-ipv4", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4s::address-ipv4", "source_node_path": "/source-address/address-ipv4s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-ranges::address-ipv4-range", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-ranges::address-ipv4-range", "source_node_path": "/source-address/address-ipv4-ranges", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-excludes::address-ipv4-exclude", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-excludes::address-ipv4-exclude", "source_node_path": "/source-address/address-ipv4-excludes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-range-excludes::address-ipv4-range-exclude", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-range-excludes::address-ipv4-range-exclude", "source_node_path": "/source-address/address-ipv4-range-excludes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4s::address-ipv4", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4s::address-ipv4", "source_node_path": "/destination-address/address-ipv4s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-ranges::address-ipv4-range", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-ranges::address-ipv4-range", "source_node_path": "/destination-address/address-ipv4-ranges", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-excludes::address-ipv4-exclude", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-excludes::address-ipv4-exclude", "source_node_path": "/destination-address/address-ipv4-excludes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-range-excludes::address-ipv4-range-exclude", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-range-excludes::address-ipv4-range-exclude", "source_node_path": "/destination-address/address-ipv4-range-excludes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::protocol-and-ports::protocol-and-port", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::protocol-and-ports::protocol-and-port", "source_node_path": "/service/service-items/protocol-and-ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::icmpv4s::icmpv4", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::icmpv4s::icmpv4", "source_node_path": "/service/service-items/icmpv4s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::protocol::protocol-id", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::protocol::protocol-id", "source_node_path": "/service/service-items/protocol", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::protocol-and-ports::protocol-and-port", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::protocol-and-ports::protocol-and-port", "source_node_path": "/service/service-items-exclude/protocol-and-ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::icmpv4s::icmpv4", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::icmpv4s::icmpv4", "source_node_path": "/service/service-items-exclude/icmpv4s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::protocol::protocol-id", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::protocol::protocol-id", "source_node_path": "/service/service-items-exclude/protocol", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-address-group", "source_vertex_label": "ds", "dest_vertex_label": "huawei-nat-address-group", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group", "source_vertex_label": "huawei-nat-address-group", "dest_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group", "source_node_path": "/huawei-nat-address-group:nat-address-group/snat-address-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::sections::section", "source_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group", "dest_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::sections::section", "source_node_path": "/sections", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::exclude-ips::exclude-ip", "source_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group", "dest_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::exclude-ips::exclude-ip", "source_node_path": "/exclude-ips", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::exclude-ports::exclude-port", "source_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group", "dest_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::exclude-ports::exclude-port", "source_node_path": "/exclude-ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-module-management", "source_vertex_label": "ds", "dest_vertex_label": "huawei-module-management", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-module-management:module-management::module-infos::module-info", "source_vertex_label": "huawei-module-management", "dest_vertex_label": "huawei-module-management:module-management::module-infos::module-info", "source_node_path": "/huawei-module-management:module-management/module-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-module-management:module-management::operation-schedules::operation-schedule", "source_vertex_label": "huawei-module-management", "dest_vertex_label": "huawei-module-management:module-management::operation-schedules::operation-schedule", "source_node_path": "/huawei-module-management:module-management/operation-schedules", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-masterkey", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-masterkey", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-iot-pnp", "source_vertex_label": "ds", "dest_vertex_label": "huawei-iot-pnp", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-iot-pnp:iot-pnp::domains::domain", "source_vertex_label": "huawei-iot-pnp", "dest_vertex_label": "huawei-iot-pnp:iot-pnp::domains::domain", "source_node_path": "/huawei-iot-pnp:iot-pnp/domains", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-iot-pnp:iot-pnp::domains::domain::vlans::vlan", "source_vertex_label": "huawei-iot-pnp:iot-pnp::domains::domain", "dest_vertex_label": "huawei-iot-pnp:iot-pnp::domains::domain::vlans::vlan", "source_node_path": "/vlans", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-iot-pnp:iot-pnp::domains::domain::protocols::protocol", "source_vertex_label": "huawei-iot-pnp:iot-pnp::domains::domain", "dest_vertex_label": "huawei-iot-pnp:iot-pnp::domains::domain::protocols::protocol", "source_node_path": "/protocols", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-http", "source_vertex_label": "ds", "dest_vertex_label": "huawei-http", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-http:http::httpc-transfer-results::httpc-transfer-result", "source_vertex_label": "huawei-http", "dest_vertex_label": "huawei-http:http::httpc-transfer-results::httpc-transfer-result", "source_node_path": "/huawei-http:http/httpc-transfer-results", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-http:http::transfer-tasks::transfer-task", "source_vertex_label": "huawei-http", "dest_vertex_label": "huawei-http:http::transfer-tasks::transfer-task", "source_node_path": "/huawei-http:http/transfer-tasks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-host-security", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-host-security", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::anti-attacks::anti-attack", "source_vertex_label": "hua<PERSON>-host-security", "dest_vertex_label": "huawei-host-security:host-security::anti-attacks::anti-attack", "source_node_path": "/huawei-host-security:host-security/anti-attacks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::host-cars::host-protocol-type", "source_vertex_label": "hua<PERSON>-host-security", "dest_vertex_label": "huawei-host-security:host-security::host-cars::host-protocol-type", "source_node_path": "/huawei-host-security:host-security/host-cars", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::packet-statistics::packet-statistic", "source_vertex_label": "hua<PERSON>-host-security", "dest_vertex_label": "huawei-host-security:host-security::packet-statistics::packet-statistic", "source_node_path": "/huawei-host-security:host-security/packet-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::adjust-car::adjust-protocol-type", "source_vertex_label": "hua<PERSON>-host-security", "dest_vertex_label": "huawei-host-security:host-security::adjust-car::adjust-protocol-type", "source_node_path": "/huawei-host-security:host-security/adjust-car", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy", "source_vertex_label": "hua<PERSON>-host-security", "dest_vertex_label": "huawei-host-security:host-security::policys::policy", "source_node_path": "/huawei-host-security:host-security/policys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy::filters::filter", "source_vertex_label": "huawei-host-security:host-security::policys::policy", "dest_vertex_label": "huawei-host-security:host-security::policys::policy::filters::filter", "source_node_path": "/filters", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy::cpcars::cpcar", "source_vertex_label": "huawei-host-security:host-security::policys::policy", "dest_vertex_label": "huawei-host-security:host-security::policys::policy::cpcars::cpcar", "source_node_path": "/cpcars", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy::auto-defend::defend-type", "source_vertex_label": "huawei-host-security:host-security::policys::policy", "dest_vertex_label": "huawei-host-security:host-security::policys::policy::auto-defend::defend-type", "source_node_path": "/auto-defend", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy::applied-policys::applied-policy", "source_vertex_label": "huawei-host-security:host-security::policys::policy", "dest_vertex_label": "huawei-host-security:host-security::policys::policy::applied-policys::applied-policy", "source_node_path": "/applied-policys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ftpc", "source_vertex_label": "ds", "dest_vertex_label": "huawei-ftpc", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ftpc:ftpc::transfer-tasks::transfer-task", "source_vertex_label": "huawei-ftpc", "dest_vertex_label": "huawei-ftpc:ftpc::transfer-tasks::transfer-task", "source_node_path": "/huawei-ftpc:ftpc/transfer-tasks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-fm", "source_vertex_label": "ds", "dest_vertex_label": "huawei-fm", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-fm:fm::alarms::alarm", "source_vertex_label": "huawei-fm", "dest_vertex_label": "huawei-fm:fm::alarms::alarm", "source_node_path": "/huawei-fm:fm/alarms", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-fm:fm::active-alarms::active-alarm", "source_vertex_label": "huawei-fm", "dest_vertex_label": "huawei-fm:fm::active-alarms::active-alarm", "source_node_path": "/huawei-fm:fm/active-alarms", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-fm:fm::history-alarms::history-alarm", "source_vertex_label": "huawei-fm", "dest_vertex_label": "huawei-fm:fm::history-alarms::history-alarm", "source_node_path": "/huawei-fm:fm/history-alarms", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt", "source_vertex_label": "ds", "dest_vertex_label": "huawei-easyweb-netmgmt", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::device-granteds::device-granted", "source_vertex_label": "huawei-easyweb-netmgmt", "dest_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::device-granteds::device-granted", "source_node_path": "/huawei-easyweb-netmgmt:easyweb-netmgmt/network/device-granteds", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::networks::vlans::vlan", "source_vertex_label": "huawei-easyweb-netmgmt", "dest_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::networks::vlans::vlan", "source_node_path": "/huawei-easyweb-netmgmt:easyweb-netmgmt/network/networks/vlans", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::networks::vlans::vlan::gateway-configurations::gateway-configuration", "source_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::networks::vlans::vlan", "dest_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::networks::vlans::vlan::gateway-configurations::gateway-configuration", "source_node_path": "/gateway-configurations", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::networks::wired-net-devices::wired-net-device", "source_vertex_label": "huawei-easyweb-netmgmt", "dest_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::networks::wired-net-devices::wired-net-device", "source_node_path": "/huawei-easyweb-netmgmt:easyweb-netmgmt/network/networks/wired-net-devices", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::networks::wired-net-devices::wired-net-device::interfaces::interface", "source_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::networks::wired-net-devices::wired-net-device", "dest_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::networks::wired-net-devices::wired-net-device::interfaces::interface", "source_node_path": "/interfaces", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::networks::wlan-ssids::wlan-ssid", "source_vertex_label": "huawei-easyweb-netmgmt", "dest_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network::networks::wlan-ssids::wlan-ssid", "source_node_path": "/huawei-easyweb-netmgmt:easyweb-netmgmt/network/networks/wlan-ssids", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-count::device-type-counts::device-type-count", "source_vertex_label": "huawei-easyweb-netmgmt", "dest_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-count::device-type-counts::device-type-count", "source_node_path": "/huawei-easyweb-netmgmt:easyweb-netmgmt/network-state/device-count/device-type-counts", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-infos::device-info", "source_vertex_label": "huawei-easyweb-netmgmt", "dest_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-infos::device-info", "source_node_path": "/huawei-easyweb-netmgmt:easyweb-netmgmt/network-state/device-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-upgrade-infos::device-upgrade-info", "source_vertex_label": "huawei-easyweb-netmgmt", "dest_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-upgrade-infos::device-upgrade-info", "source_node_path": "/huawei-easyweb-netmgmt:easyweb-netmgmt/network-state/device-upgrade-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-neighbor-infos::device-neighbor-info", "source_vertex_label": "huawei-easyweb-netmgmt", "dest_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-neighbor-infos::device-neighbor-info", "source_node_path": "/huawei-easyweb-netmgmt:easyweb-netmgmt/network-state/device-neighbor-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-neighbor-infos::device-neighbor-info::interfaces::interface", "source_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-neighbor-infos::device-neighbor-info", "dest_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-neighbor-infos::device-neighbor-info::interfaces::interface", "source_node_path": "/interfaces", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-neighbor-infos::device-neighbor-info::interfaces::interface::neighbor-info::neighbor-devices::neighbor-device", "source_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-neighbor-infos::device-neighbor-info::interfaces::interface", "dest_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-neighbor-infos::device-neighbor-info::interfaces::interface::neighbor-info::neighbor-devices::neighbor-device", "source_node_path": "/neighbor-info/neighbor-devices", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-neighbor-infos::device-neighbor-info::interfaces::interface::neighbor-info::neighbor-devices::neighbor-device::neighbor-ifs::neighbor-if", "source_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-neighbor-infos::device-neighbor-info::interfaces::interface::neighbor-info::neighbor-devices::neighbor-device", "dest_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-neighbor-infos::device-neighbor-info::interfaces::interface::neighbor-info::neighbor-devices::neighbor-device::neighbor-ifs::neighbor-if", "source_node_path": "/neighbor-ifs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-port-infos::device-port-info", "source_vertex_label": "huawei-easyweb-netmgmt", "dest_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-port-infos::device-port-info", "source_node_path": "/huawei-easyweb-netmgmt:easyweb-netmgmt/network-state/device-port-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-port-infos::device-port-info::interfaces::interface", "source_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-port-infos::device-port-info", "dest_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-port-infos::device-port-info::interfaces::interface", "source_node_path": "/interfaces", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-alarms::device-alarm", "source_vertex_label": "huawei-easyweb-netmgmt", "dest_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-alarms::device-alarm", "source_node_path": "/huawei-easyweb-netmgmt:easyweb-netmgmt/network-state/device-alarms", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-alarms::device-alarm::alarms::alarm", "source_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-alarms::device-alarm", "dest_vertex_label": "huawei-easyweb-netmgmt:easyweb-netmgmt::network-state::device-alarms::device-alarm::alarms::alarm", "source_node_path": "/alarms", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:upgrade::devices::device", "source_vertex_label": "huawei-easyweb-netmgmt", "dest_vertex_label": "huawei-easyweb-netmgmt:upgrade::devices::device", "source_node_path": "/huawei-easyweb-netmgmt:upgrade/devices", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:reboot-device::devices::device", "source_vertex_label": "huawei-easyweb-netmgmt", "dest_vertex_label": "huawei-easyweb-netmgmt:reboot-device::devices::device", "source_node_path": "/huawei-easyweb-netmgmt:reboot-device/devices", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-easyweb-netmgmt:restore-factory::devices::device", "source_vertex_label": "huawei-easyweb-netmgmt", "dest_vertex_label": "huawei-easyweb-netmgmt:restore-factory::devices::device", "source_node_path": "/huawei-easyweb-netmgmt:restore-factory/devices", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns", "source_vertex_label": "ds", "dest_vertex_label": "huawei-dns", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::domains::domain", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::domains::domain", "source_node_path": "/huawei-dns:dns/domains", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::ipv4-hosts::ipv4-host", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::ipv4-hosts::ipv4-host", "source_node_path": "/huawei-dns:dns/ipv4-hosts", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::ipv4-servers::ipv4-server", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::ipv4-servers::ipv4-server", "source_node_path": "/huawei-dns:dns/ipv4-servers", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::dns-relay::local-ips::local-ip", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::dns-relay::local-ips::local-ip", "source_node_path": "/huawei-dns:dns/dns-relay/local-ips", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::query-host-ips::query-host-ip", "source_vertex_label": "huawei-dns", "dest_vertex_label": "huawei-dns:dns::query-host-ips::query-host-ip", "source_node_path": "/huawei-dns:dns/query-host-ips", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools", "source_vertex_label": "ds", "dest_vertex_label": "huawei-diagnostic-tools", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result", "source_vertex_label": "huawei-diagnostic-tools", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result", "source_node_path": "/huawei-diagnostic-tools:diagnostic-tools/ipv4/ping-results", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result::details::detail", "source_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result::details::detail", "source_node_path": "/details", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result", "source_vertex_label": "huawei-diagnostic-tools", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result", "source_node_path": "/huawei-diagnostic-tools:diagnostic-tools/ipv4/trace-results", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result::details::detail", "source_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result::details::detail", "source_node_path": "/details", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose", "source_vertex_label": "ds", "dest_vertex_label": "huawei-diagnose", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-ip:ipv4::addresses::address", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-ip:ipv4::addresses::address", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-ip:ipv4/addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::mac-learnings::mac-learning", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::mac-learnings::mac-learning", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lsw-chip:lsw-chip/mac-learnings", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lsw-chip:lsw-chip/vlan-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info::vlan-id", "source_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info::vlan-id", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::car-infos::car-info", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::car-infos::car-info", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lsw-chip:lsw-chip/car-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-lsw-chip:lsw-chip/acl-ranges", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range::acl-infos::acl-info", "source_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range::acl-infos::acl-info", "source_node_path": "/acl-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-nctl:nctl::innertables::innertable", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-nctl:nctl::innertables::innertable", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-nctl:nctl/innertables", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-nfc:nfc::processes::process", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-nfc:nfc::processes::process", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-nfc:nfc/processes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::sensor-paths::sensor-path", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::sensor-paths::sensor-path", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-omu:omu/notif/sensor-paths", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::sensor-paths::sensor-path::path", "source_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::sensor-paths::sensor-path", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::sensor-paths::sensor-path::path", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-omu:omu/notif/subscriptions", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group", "source_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group", "source_node_path": "/sensor-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path", "source_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path", "source_node_path": "/sensor-paths", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path::indicators::indicator", "source_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path::indicators::indicator", "source_node_path": "/indicators", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::collect-tasks::collect-task", "source_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::collect-tasks::collect-task", "source_node_path": "/collect-tasks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group", "source_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group", "source_node_path": "/destination-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group::destinations::destination", "source_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group::destinations::destination", "source_node_path": "/destinations", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::collect-tasks::collect-task", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::collect-tasks::collect-task", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-omu:omu/notif/collect-tasks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::notification-subscriptions::notification-subscription", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-omu:omu::notif::notification-subscriptions::notification-subscription", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-omu:omu/notif/notification-subscriptions", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-routing-lite:routing::routing-manage::routes::route", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-routing-lite:routing::routing-manage::routes::route", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-routing-lite:routing/routing-manage/routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-routing-lite:routing::direct-routing::routes::route", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-routing-lite:routing::direct-routing::routes::route", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-routing-lite:routing/direct-routing/routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-routing-lite:routing::static-routing::routes::route", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-routing-lite:routing::static-routing::routes::route", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-routing-lite:routing/static-routing/routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-routing-lite:routing::user-network-routing::routes::route", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-routing-lite:routing::user-network-routing::routes::route", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-routing-lite:routing/user-network-routing/routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-system:sysdiag::service-infos::service-info", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-system:sysdiag::service-infos::service-info", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-system:sysdiag/service-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-tid:tid::terminals::terminal", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-tid:tid::terminals::terminal", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-tid:tid/terminals", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-tid:tid::terminals::terminal::fingerprints::fingerprint", "source_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-tid:tid::terminals::terminal", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-tid:tid::terminals::terminal::fingerprints::fingerprint", "source_node_path": "/fingerprints", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-tid:tid::return-packets::return-packet", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-tid:tid::return-packets::return-packet", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-tid:tid/return-packets", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-tid:tid::monitoring-scan-records::monitoring-scan-record", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-tid:tid::monitoring-scan-records::monitoring-scan-record", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-tid:tid/monitoring-scan-records", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnosehuawei-diagnose-tid:tid::statistics::statistic", "source_vertex_label": "huawei-diagnose", "dest_vertex_label": "huawei-diagnose:diagnosehuawei-diagnose-tid:tid::statistics::statistic", "source_node_path": "/huawei-diagnose:diagnose/huawei-diagnose-tid:tid/statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose-omu", "source_vertex_label": "ds", "dest_vertex_label": "huawei-diagnose-omu", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm-poe", "source_vertex_label": "ds", "dest_vertex_label": "huawei-devm-poe", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm-poe:devm-poe::poes::poe", "source_vertex_label": "huawei-devm-poe", "dest_vertex_label": "huawei-devm-poe:devm-poe::poes::poe", "source_node_path": "/huawei-devm-poe:devm-poe/poes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm-poe:devm-poe::poes::poe::ports::port", "source_vertex_label": "huawei-devm-poe:devm-poe::poes::poe", "dest_vertex_label": "huawei-devm-poe:devm-poe::poes::poe::ports::port", "source_node_path": "/ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cpu-memory", "source_vertex_label": "ds", "dest_vertex_label": "huawei-cpu-memory", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cpu-memory:cpu-memory::board-cpu-infos::board-cpu-info", "source_vertex_label": "huawei-cpu-memory", "dest_vertex_label": "huawei-cpu-memory:cpu-memory::board-cpu-infos::board-cpu-info", "source_node_path": "/huawei-cpu-memory:cpu-memory/board-cpu-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cpu-memory:cpu-memory::board-memory-infos::board-memory-info", "source_vertex_label": "huawei-cpu-memory", "dest_vertex_label": "huawei-cpu-memory:cpu-memory::board-memory-infos::board-memory-info", "source_node_path": "/huawei-cpu-memory:cpu-memory/board-memory-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cpu-memory:cpu-memory::board-storage-partition-infos::board-storage-partition-info", "source_vertex_label": "huawei-cpu-memory", "dest_vertex_label": "huawei-cpu-memory:cpu-memory::board-storage-partition-infos::board-storage-partition-info", "source_node_path": "/huawei-cpu-memory:cpu-memory/board-storage-partition-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-codesign", "source_vertex_label": "ds", "dest_vertex_label": "huawei-codesign", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-codesign:codesign::software-crls::software-crl", "source_vertex_label": "huawei-codesign", "dest_vertex_label": "huawei-codesign:codesign::software-crls::software-crl", "source_node_path": "/huawei-codesign:codesign/software-crls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-codesign:codesign::crl-names::crl-name", "source_vertex_label": "huawei-codesign", "dest_vertex_label": "huawei-codesign:codesign::crl-names::crl-name", "source_node_path": "/huawei-codesign:codesign/crl-names", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cli", "source_vertex_label": "ds", "dest_vertex_label": "huawei-cli", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cfg", "source_vertex_label": "ds", "dest_vertex_label": "huawei-cfg", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cfg:cfg::startup-infos::startup-info", "source_vertex_label": "huawei-cfg", "dest_vertex_label": "huawei-cfg:cfg::startup-infos::startup-info", "source_node_path": "/huawei-cfg:cfg/startup-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cfg:cfg::cfg-files::cfg-file", "source_vertex_label": "huawei-cfg", "dest_vertex_label": "huawei-cfg:cfg::cfg-files::cfg-file", "source_node_path": "/huawei-cfg:cfg/cfg-files", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-capture", "source_vertex_label": "ds", "dest_vertex_label": "huawei-capture", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-capture:capture-packet::interface-name", "source_vertex_label": "huawei-capture", "dest_vertex_label": "huawei-capture:capture-packet::interface-name", "source_node_path": "/huawei-capture:capture-packet", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "hua<PERSON>-aspf", "source_vertex_label": "ds", "dest_vertex_label": "hua<PERSON>-aspf", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aspf:aspf::protocol", "source_vertex_label": "hua<PERSON>-aspf", "dest_vertex_label": "huawei-aspf:aspf::protocol", "source_node_path": "/huawei-aspf:aspf", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}]