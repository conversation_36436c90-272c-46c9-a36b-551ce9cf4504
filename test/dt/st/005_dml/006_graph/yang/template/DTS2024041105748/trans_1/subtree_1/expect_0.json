{"ds": [{"name": "running", "huawei-wlan-wired-port-profile": [{"huawei-wlan-wired-port-profile:wlan-wired-port-profile": {"wiredport-profiles": {"wiredport-profile": [{"profile-name": "default", "@user-isolate": "disable", "@untagged-vlan": "1"}]}}}], "huawei-vlan": [{"huawei-vlan:vlan": {"vlans": {"vlan": [{"id": 1, "@type": "common", "huawei-arp:arp-security": {"@l2proxy-enable": false}}]}}}], "huawei-ifm": [{"huawei-ifm:ifm": {"interfaces": {"interface": [{"name": "Vlanif1", "class": "main-interface", "type": "<PERSON><PERSON><PERSON>", "@admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-dhcp:interface-ip-pool": {"select-type": {"interface": {"select-interface": {"dns-list": {"ip-address": [{"ip-address": "***************"}]}, "auto-recycle": {"@day": 0, "@hour": 0, "@minute": 0}}}}}, "huawei-ip:ipv4": {"address": {"common-address": {"addresses": {"address": [{"ip": "*************", "mask": "*************", "type": "main"}]}}}}, "huawei-nat:nat": {"@nat-enable": false}}, {"name": "GE0/0/1", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": 1, "untag-vlans": "1", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-nat:nat": {"@nat-enable": false}}, {"name": "GE0/0/2", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": 1, "untag-vlans": "1", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-nat:nat": {"@nat-enable": false}}, {"name": "GE0/0/3", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": 1, "untag-vlans": "1", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-nat:nat": {"@nat-enable": false}}, {"name": "GE0/0/4", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": 1, "untag-vlans": "1", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-nat:nat": {"@nat-enable": false}}, {"name": "GE0/0/5", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": 1, "untag-vlans": "1", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-nat:nat": {"@nat-enable": false}}, {"name": "GE0/0/6", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": 1, "untag-vlans": "1", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-nat:nat": {"@nat-enable": false}}, {"name": "GE0/0/7", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": 1, "untag-vlans": "1", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-nat:nat": {"@nat-enable": false}}, {"name": "GE0/0/8", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": 1, "untag-vlans": "1", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-nat:nat": {"@nat-enable": false}}, {"name": "GE0/0/9", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-dhcp:dhcp-client-if": {"address-allocation": ""}, "huawei-nat:nat": {"nat-enable": true}}, {"name": "GE0/0/10", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-ip:ipv4": {"address": {"common-address": {"addresses": {"address": [{"ip": "**********00", "mask": "*************", "type": "main"}]}}}}, "huawei-nat:nat": {"nat-enable": true}}, {"name": "GE0/0/11", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-dhcp:dhcp-client-if": {"address-allocation": ""}, "huawei-nat:nat": {"nat-enable": true}}, {"name": "NULL", "class": "main-interface", "type": "NULL", "@admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-nat:nat": {"@nat-enable": false}}, {"name": "lo", "class": "main-interface", "type": "LoopBack", "@admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-nat:nat": {"@nat-enable": false}}, {"name": "Wlan-Bss26", "@class": "main-interface", "@admin-status": "up", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-nat:nat": {"@nat-enable": false}}]}}}], "huawei-dhcp": [{"huawei-dhcp:dhcp": {"common": {"global": {"enable": true}}, "server": {"common": {"@ping-packet-nub": 2, "@ping-packet-timeout": 500, "@bootp-enable": true, "@bootp-auto-enable": true}}}}], "huawei-network-instance": [{"huawei-network-instance:network-instance": {"instances": {"instance": [{"name": "_public_", "huawei-l3vpn:afs": {"af": [{"type": "ipv4-unicast", "huawei-routing:routing": {"routing-manage": {"topologys": {"topology": [{"name": "base"}]}}, "static-routing": {"unicast-route2s": {"unicast-route2": [{"topology-name": "base", "prefix": "0.0.0.0", "mask-length": 0, "nexthop-interface-addresses": {"nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "**********", "preference": 60}]}}]}}}}]}}]}}}], "huawei-wlan-ap": [{"huawei-wlan-ap:wlan-ap": {"@ap-auth-mode": "mac", "ap-types": {"ap-type": [{"type-name": "AP210", "type-id": 125}, {"type-name": "AP230", "type-id": 126}, {"type-name": "AP310", "type-id": 127}, {"type-name": "AP330", "type-id": 128}, {"type-name": "AP350", "type-id": 129}, {"type-name": "AirEngine5761RS-11", "type-id": 162}, {"type-name": "AP163", "type-id": 180}, {"type-name": "AP165", "type-id": 181}, {"type-name": "AP263", "type-id": 182}, {"type-name": "AP363", "type-id": 183}, {"type-name": "AP365", "type-id": 184}, {"type-name": "AP161", "type-id": 190}, {"type-name": "AP162", "type-id": 191}, {"type-name": "AP362", "type-id": 192}, {"type-name": "AP361", "type-id": 205}, {"type-name": "AP160", "type-id": 206}, {"type-name": "AP661", "type-id": 207}, {"type-name": "AP761", "type-id": 208}, {"type-name": "AP371", "type-id": 232}, {"type-name": "AP673", "type-id": 238}]}}}], "huawei-wlan-ap-group-profile": [{"huawei-wlan-ap-group-profile:wlan-ap-group-profile": {"ap-group-profiles": {"ap-group-profile": [{"ap-group-name": "default", "huawei-wlan-reg-dom-profile:grp-binding-regular-domain-profile": {"@binding-regular-domain-profile": "default"}, "huawei-wlan-system-profile:binding-system-profile": {"@binding-system-profile": "default"}}]}}}], "huawei-wlan-radio-2g-profile": [{"huawei-wlan-radio-2g-profile:wlan-radio-2g-profile": {"radio-2g-profiles": {"radio-2g-profile": [{"profile-name": "default", "@radio-type": "dot11ax", "huawei-wlan-airscan-profile:radio-2g-profile-binding-airscan": {"@binding-airscan-profile": "default"}, "huawei-wlan-rrm-profile:radio-2g-binding-rrm": {"@binding-rrm-profile": "default"}}]}}}], "huawei-wlan-radio-5g-profile": [{"huawei-wlan-radio-5g-profile:wlan-radio-5g-profile": {"radio-5g-profiles": {"radio-5g-profile": [{"profile-name": "default", "@radio-type": "dot11ax", "huawei-wlan-airscan-profile:radio-5g-profile-binding-airscan": {"@binding-airscan-profile": "default"}, "huawei-wlan-rrm-profile:radio-5g-binding-rrm": {"@binding-rrm-profile": "default"}}]}}}], "huawei-wlan-reg-dom-profile": [{"huawei-wlan-reg-dom-profile:wlan-reg-dom-profile": {"regulatory-domain-profiles": {"regulatory-domain-profile": [{"profile-name": "default", "@country-code": "CN"}]}}}], "huawei-wlan-system-profile": [{"huawei-wlan-system-profile:wlan-system-profile": {"system-profiles": {"system-profile": [{"profile-name": "default", "@stelnet-server": true, "@telnet": false, "led": {"@led-switch": "turn-on"}, "traffic-optimize": {"@arp-threshold": 256, "@igmp-threshold": 256, "@nd-threshold": 256, "@dhcp-threshold": 256, "@dhcpv6-threshold": 256, "@mdns-threshold": 256, "@other-broadcast-threshold": 16, "@other-multicast-threshold": 16, "@arp": true, "@igmp": true, "@nd": true, "@dhcp": true, "@dhcpv6": true, "@mdns": true, "@other-broadcast": true, "@other-multicast": true}}]}}}], "huawei-wlan-vap-profile": [{"huawei-wlan-vap-profile:wlan-vap-profile": {"vap-profiles": {"vap-profile": [{"profile-name": "default", "@service-vlan": 1, "@ip-source-check-user-bind-switch": false, "@arp-anti-attack-check-user-bind-switch": false, "dhcp-option82": {"@insert-switch": false, "circuit-id": {"@dhcp-option82-format-type": "ap-mac", "@option82-pattern-semicolon": "disable"}, "remote-id": {"@dhcp-option82-format-type": "ap-mac", "@option82-pattern-semicolon": "disable"}}, "huawei-wlan-security-profile:binding-security-profile": {"@binding-security-profile": "default"}, "huawei-wlan-ssid-profile:binding-ssid-profile": {"@binding-ssid-profile": "default"}, "huawei-wlan-traffic-profile:binding-traffic-profile": {"@binding-traffic-profile": "default"}}]}}}], "huawei-wlan-security-profile": [{"huawei-wlan-security-profile:wlan-security-profile": {"security-profiles": {"security-profile": [{"profile-name": "default"}]}}}], "huawei-wlan-ssid-profile": [{"huawei-wlan-ssid-profile:wlan-ssid-profile": {"ssid-profiles": {"ssid-profile": [{"profile-name": "default", "@ssid": "HUAWEI-WLAN", "@max-sta-number": 64, "ofdma-policy": {"@ofdma-downlink-switch": true, "@ofdma-uplink-switch": true}}]}}}], "huawei-wlan-traffic-profile": [{"huawei-wlan-traffic-profile:wlan-traffic-profile": {"traffic-profiles": {"traffic-profile": [{"profile-name": "default", "rate-limit": {"@client-up": 4294967295, "@client-down": 4294967295, "@vap-up": 4294967295, "@vap-down": 4294967295}, "traffic-optimize": {"@dynamic-rate-limit": true, "@multicast-unicast": false, "@bcmc-unicast-arp": true, "@bcmc-unicast-dhcp": true, "@bcmc-unicast-nd": true, "@bcmc-unicast-mismatch-drop": true, "bcmc-deny": {"@deny-all": false}}}]}}}], "huawei-wlan-rrm-profile": [{"huawei-wlan-rrm-profile:wlan-rrm-profile": {"rrm-profiles": {"rrm-profile": [{"profile-name": "default", "@high-density-amc-optimize": "disable", "@antenna-mode": "auto", "@multimedia-air-optimize": "enable", "@multimedia-tcp-window-tuning": "enable", "@multimedia-uplink-delay-guarantee": "enable", "@dfs-recover-delay": 0, "@clb-grp-itf-thrld": -127, "@amc-policy": "auto-balance", "dynamic-edca": {"@dynamic-edca": "disable", "@dynamic-edca-threshold": 6}, "downlink-delay-guarantee": {"@voice-guarantee-level": "medium", "@video-guarantee-level": "medium", "@best-effort-guarantee-level": "medium", "@background-guarantee-level": "medium"}, "downlink-slice-ratio": {"@voice-slice-ratio-level": "medium", "@video-slice-ratio-level": "medium"}, "multimedia-air-optimize-threshold": {"@video-threshold": 100, "@voice-threshold": 30}, "smart-roam": {"@smart-roam-switch": "enable", "@snr-smart-roam-threshold": 20, "@quick-kickoff-threshold": "enable", "@snr-quick-kickoff-threshold": 15, "@high-level-margin": 12, "@low-level-margin": 10, "@unable-roam-exp-time": 30}, "calibrate-retransmission-rate-check": {"@interval": 1, "@traffic-threshold": 1250}, "uac": {"@uac-client-snr": "disable", "@uac-client-number": "disable", "@snr-threshold": 15, "number-threshold": {"@access": 64, "@roam": 64}}, "load-balance": {"@load-balance-switch": "enable", "@probe-report-intvl": 120, "@sta-start-threshold": 10, "@rssi-threshold": -65, "@rssi-diff-gap": 5, "@deauth-fail-times": 0, "sta-gap-threshold": {"@gap-type": "number", "@number": 3}}, "calibrate-para": {"@min-tx-power-2g": 9, "@max-tx-power-2g": 127, "@min-tx-power-5g": 12, "@max-tx-power-5g": 127, "@min-tx-power-6g": 12, "@max-tx-power-6g": 127, "@clb-noise-flr-thrd": -75, "@clbt-tpc-thrd": -60, "@clbt-restran-rate-thrd": 60}, "spatial-reuse": {"@bss-color-switch": true, "@spatial-reuse-switch": true, "@co-sr": true}, "band-steer": {"@start-thrd": 100, "@gap-thrd": 90, "@snr-thrd": 20, "@deny-thrd": 0, "@client-band-expire": 35}}]}}}], "huawei-wlan-radio-calibrate": [{"huawei-wlan-radio-calibrate:wlan-radio-calibrate": {"calibrate": {"calibrate-mode": "auto", "auto-mode-para": {"@auto-interval": 1440, "@auto-start-time": "03:00:00"}}}}], "huawei-system": [{"huawei-system:system": {"system-info": {"sys-name": "hua<PERSON>", "@sys-contact": "R&D Beijing, Huawei Technologies co.,Ltd.", "@sys-location": "Beijing China"}}}], "huawei-ssl": [{"huawei-ssl:ssl": {"ssl-policys": {"ssl-policy": [{"policy-name": "default", "pki-realm": "default"}, {"policy-name": "test1", "pki-realm": "default"}, {"policy-name": "test2", "pki-realm": "default"}, {"policy-name": "policy1", "pki-realm": "http_transfer"}]}}}], "huawei-sshs": [{"huawei-sshs:sshs": {"server": {"pki-domain": "default"}, "users": {"user": [{"name": "hua<PERSON>", "key-name": "default", "pub-key-type": "PKI"}]}, "server-enable": {"stelnet-ipv4-enable": "enable"}, "server-port": {"@ipv4-port-number": 22}, "ipv4-server-sources": {"ipv4-server-source": [{"src-interface": "Vlanif1"}, {"src-interface": "GE0/0/10"}]}, "call-homes": {"call-home": [{"call-home-name": "QiankunCloudService", "end-points": {"end-point": [{"end-point-name": "DefaultEndPoint", "enabled": true}]}}]}}}], "huawei-aaa": [{"huawei-aaa:aaa": {"lam": {"password-policy": {"@complexity-check-three": true}, "users": {"user": [{"name": "hua<PERSON>", "group-name": "admin", "@service-terminal": false, "service-api": true, "@password-force-change": true}, {"name": "admin", "group-name": "admin", "password": "$6$.cNkSK0twDHcsdNi$1zLjGz7cJwitSVyFvycytuxPCcidZwlcx4qdokgiQwBe8sVup81ubIbwvtD/tTzf/TKsX.33bKh4FKbTewMGq1", "service-terminal": true, "service-api": true, "password-force-change": false}, {"name": "u1", "@group-name": "admin", "password": "$6$jCYeRBRZxyqjZvlj$JmUkU8u3Gf/lomlzY.BNaK8HNokd9TsBiJGORxiQ0K/Mfa41Ga8Zl0N.UbGHdGbsi3sUzJsep9ih3HJ7wOg9z0", "service-terminal": true, "@service-api": false, "@password-force-change": true}, {"name": "test", "@group-name": "admin", "password": "$6$XgjJZtuTLKjsmk7k$DsKNhjFZC.xZUUjA37YJtE8fxD6jUsgLpZIoRdi.qb.5w5Y.gDw0Ttrgp5ZJJWAF3USOrmVNYnvQP0Od2hQyb.", "@service-terminal": false, "@service-api": false, "@password-force-change": true}, {"name": "sshd", "group-name": "admin", "password": "$6$nY0l0.Tx5SP.qk6m$abOkW3Rw.kuUkQY0XMQ8h4vMmZE8T6WCHyd8d9lGa7ErinSWuptlucnNykSWOLkLIN8Cg2eF2lSQpYysCsFEl0", "service-terminal": true, "service-api": true, "@password-force-change": true}, {"name": "test111", "group-name": "admin", "password": "$6$ChhFxLpgk8krCSF9$hlQCpZsPQbWYihX00MBQEqD7JTFfgPmnaEQiXe260vzU3lPDX161.rDgGOQrFYRJqUOSJQSlUNa3ant53xnX41", "service-terminal": true, "service-api": false, "password-force-change": false}, {"name": "huawei-xcl", "group-name": "admin", "password": "$6$sy4gRkHRb2J3BePh$mD6K2qreFAqZr5gk/eBZZ.9WP9PxihAlO37ynxNO54NOFkbsz50ejIAYNzXH4Qp8AmyZvdbP1VQibdiuGY5L7/", "service-terminal": true, "@service-api": false, "password-force-change": true}, {"name": "test123", "group-name": "admin", "password": "$6$CQkJCQkJCQkJCQkJ$DLvW56/FAfUbaR9atVSmA2nWa0DUZVB4smZgtwuTWtoEo22k1Ys.ON/GDm8qvdqTdp9D9DCxlgmDlNxUFeKQh1", "service-terminal": true, "@service-api": false, "@password-force-change": true}, {"name": "upgradeTestSuccess", "@group-name": "admin", "@service-terminal": false, "@service-api": false, "@password-force-change": true}]}}}}], "huawei-smart-upgrade": [{"huawei-smart-upgrade:smart-upgrade": {"global": {"@check-new-version-interval": 0, "transport": {"http": {"http-url": "https://houp.huawei.com", "http-port": 443}}}}}], "huawei-acl": [{"huawei-acl:acl": {"groups": {"group": [{"identity": "2129", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2129", "id": 2129, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2130", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2130", "id": 2130, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2131", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2131", "id": 2131, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2132", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2132", "id": 2132, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2133", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2133", "id": 2133, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2136", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2136", "id": 2136, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2137", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2137", "id": 2137, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2138", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2138", "id": 2138, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2139", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2139", "id": 2139, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2140", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2140", "id": 2140, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2141", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2141", "id": 2141, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2142", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2142", "id": 2142, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2143", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2143", "id": 2143, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2144", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2144", "id": 2144, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2145", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2145", "id": 2145, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2146", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2146", "id": 2146, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2147", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2147", "id": 2147, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2148", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2148", "id": 2148, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2149", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2149", "id": 2149, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2150", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2150", "id": 2150, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2151", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2151", "id": 2151, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2152", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2152", "id": 2152, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2153", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2153", "id": 2153, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2154", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2154", "id": 2154, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2155", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2155", "id": 2155, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2156", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2156", "id": 2156, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2157", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2157", "id": 2157, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2158", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2158", "id": 2158, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2159", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2159", "id": 2159, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2160", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2160", "id": 2160, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2161", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2161", "id": 2161, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2162", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2162", "id": 2162, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2163", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2163", "id": 2163, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2164", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2164", "id": 2164, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2165", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2165", "id": 2165, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2166", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2166", "id": 2166, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2167", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2167", "id": 2167, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2168", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2168", "id": 2168, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2169", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2169", "id": 2169, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2170", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2170", "id": 2170, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2171", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2171", "id": 2171, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2172", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2172", "id": 2172, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2173", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2173", "id": 2173, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2174", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2174", "id": 2174, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2175", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2175", "id": 2175, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2176", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2176", "id": 2176, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2177", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2177", "id": 2177, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2178", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2178", "id": 2178, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2179", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2179", "id": 2179, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2180", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2180", "id": 2180, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2181", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2181", "id": 2181, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2182", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2182", "id": 2182, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2183", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2183", "id": 2183, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2184", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2184", "id": 2184, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2185", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2185", "id": 2185, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2186", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2186", "id": 2186, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2187", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2187", "id": 2187, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2188", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2188", "id": 2188, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2189", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2189", "id": 2189, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2190", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2190", "id": 2190, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2191", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2191", "id": 2191, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2192", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2192", "id": 2192, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2193", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2193", "id": 2193, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2194", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2194", "id": 2194, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2195", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2195", "id": 2195, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2196", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2196", "id": 2196, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2197", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2197", "id": 2197, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2198", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2198", "id": 2198, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2199", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2199", "id": 2199, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2200", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2200", "id": 2200, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2201", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2201", "id": 2201, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2202", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2202", "id": 2202, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2203", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2203", "id": 2203, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2204", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2204", "id": 2204, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2205", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2205", "id": 2205, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2206", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2206", "id": 2206, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2207", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2207", "id": 2207, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2208", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2208", "id": 2208, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2209", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2209", "id": 2209, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2210", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2210", "id": 2210, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2211", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2211", "id": 2211, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2212", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2212", "id": 2212, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2213", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2213", "id": 2213, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2214", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2214", "id": 2214, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2215", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2215", "id": 2215, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2216", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2216", "id": 2216, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2217", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2217", "id": 2217, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2218", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2218", "id": 2218, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2219", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2219", "id": 2219, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2220", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2220", "id": 2220, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2221", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2221", "id": 2221, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2222", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2222", "id": 2222, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2223", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2223", "id": 2223, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2224", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2224", "id": 2224, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2225", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2225", "id": 2225, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2226", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2226", "id": 2226, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2227", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2227", "id": 2227, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2228", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2228", "id": 2228, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2229", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2229", "id": 2229, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2230", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2230", "id": 2230, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2231", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2231", "id": 2231, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2232", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2232", "id": 2232, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2233", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2233", "id": 2233, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2234", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2234", "id": 2234, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2235", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2235", "id": 2235, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2236", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2236", "id": 2236, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2237", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2237", "id": 2237, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2238", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2238", "id": 2238, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2239", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2239", "id": 2239, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2240", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2240", "id": 2240, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2241", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2241", "id": 2241, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2242", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2242", "id": 2242, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2243", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2243", "id": 2243, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2244", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2244", "id": 2244, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2245", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2245", "id": 2245, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2246", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2246", "id": 2246, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2247", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2247", "id": 2247, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2248", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2248", "id": 2248, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2249", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2249", "id": 2249, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2250", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2250", "id": 2250, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2251", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2251", "id": 2251, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2252", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2252", "id": 2252, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2253", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2253", "id": 2253, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2254", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2254", "id": 2254, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2255", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2255", "id": 2255, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2256", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2256", "id": 2256, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2128", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2128", "id": 2128, "action": "deny", "@active-status": "not-ready"}]}}, {"identity": "2127", "@step": 5, "rule-basics": {"rule-basic": [{"name": "2127", "id": 2127, "action": "deny", "@active-status": "not-ready"}]}}]}}}], "huawei-pki": [{"huawei-pki:pki": {"global": {"certificate-check": {"@validate-method": "crl-none"}}, "domains": {"domain": [{"name": "default", "@digest-algorithm": "sha-256"}]}}}], "huawei-ntp": [{"huawei-ntp:ntp": {"unicasts": {"unicast": [{"ip-address": "***********", "type": "Server", "vpn-name": "_public_", "@is-preferred": false, "ifname": "Vlanif1", "key-id": 1, "@max-poll-interval": 10, "@min-poll-interval": 6}]}, "authentications": {"authentication": [{"key-id": 1, "mode": "HMAC-SHA256", "key-value": "%+%##!!!!!!!!!\"!!!!%!!!!*!!!!gAS&:MZ=9GI@im;%#seI\\dCT-#>ZW,8d37:!!!!!2jp5!!!!!!=!!!!ck!-!;flQH#Bg^A*^N4JI.\\N#_fwVG8KGSN!!!!!%+%#"}]}}}], "huawei-nat-policy": [{"huawei-nat-policy:nat-policy": {"rules": {"rule": [{"name": "default", "action": {"action": {"do-nat": {"source-nat": {"mode": {"easy-ip": {"easy-ip": ""}}}}}}, "egress": {"interfaces": {"egress-interface": [{"egress-interface": "GE0/0/9"}, {"egress-interface": "GE0/0/10"}, {"egress-interface": "GE0/0/11"}]}}}]}}}], "huawei-ftpc": [{"huawei-ftpc:ftpc": {"client": {"enabled": true}}}], "huawei-easyweb-netmgmt": [{"huawei-easyweb-netmgmt:easyweb-netmgmt": {"network": {"@online-upgrade-enable": false, "device-granteds": {"device-granted": [{"mac-address": "981a-3507-e0c0"}]}}}}], "huawei-cli": [{"huawei-cli:cli": {"header": {"login-information": {"head-login-text": {"login-text": "jjj"}}}, "huawei-cli-lite:terminal": {"@history-cmd-size": 10, "idle-timeout": 0, "split-screen": true}}}], "huawei-cfg": [{"huawei-cfg:cfg": {"autosave": {"interval-time": 1008, "@delay-time": 5}}}], "intended-test": [{"intended-test:container1": {"running-leaf": "1"}}]}, {"name": "intended", "intended-test": [{"intended-test:container1": {"running-leaf": "1"}}]}]}