[{"type": "container", "name": "root", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"type": "container", "name": "company", "fields": [{"name": "comment", "type": "string"}, {"name": "name", "type": "string"}]}], "keys": [{"node": "root", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::L0", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"type": "container", "name": "c0", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c1", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c2", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c3", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c4", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c5", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c6", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c7", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c8", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c9", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c10", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c11", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c12", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c13", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c14", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c15", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c16", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c17", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c18", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c19", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c20", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c21", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c22", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c23", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c24", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c25", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c26", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c27", "fields": [{"name": "F1", "type": "uint32"}, {"type": "container", "name": "c28", "fields": [{"name": "F2", "type": "string"}, {"type": "container", "name": "c29", "fields": [{"name": "F3", "type": "string"}, {"type": "container", "name": "c30", "fields": [{"name": "F4", "type": "uint32"}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}], "keys": [{"node": "root::L0", "name": "PK", "fields": ["PID", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "root::L0", "name": "UK", "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}, "fields": ["PID", "c0/F1", "c0/c1/F1", "c0/c1/c2/F1", "c0/c1/c2/c3/F1", "c0/c1/c2/c3/c4/F1", "c0/c1/c2/c3/c4/c5/F1", "c0/c1/c2/c3/c4/c5/c6/F1", "c0/c1/c2/c3/c4/c5/c6/c7/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12/c13/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12/c13/c14/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12/c13/c14/c15/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12/c13/c14/c15/c16/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12/c13/c14/c15/c16/c17/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12/c13/c14/c15/c16/c17/c18/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12/c13/c14/c15/c16/c17/c18/c19/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12/c13/c14/c15/c16/c17/c18/c19/c20/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12/c13/c14/c15/c16/c17/c18/c19/c20/c21/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12/c13/c14/c15/c16/c17/c18/c19/c20/c21/c22/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12/c13/c14/c15/c16/c17/c18/c19/c20/c21/c22/c23/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12/c13/c14/c15/c16/c17/c18/c19/c20/c21/c22/c23/c24/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12/c13/c14/c15/c16/c17/c18/c19/c20/c21/c22/c23/c24/c25/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12/c13/c14/c15/c16/c17/c18/c19/c20/c21/c22/c23/c24/c25/c26/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12/c13/c14/c15/c16/c17/c18/c19/c20/c21/c22/c23/c24/c25/c26/c27/F1", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12/c13/c14/c15/c16/c17/c18/c19/c20/c21/c22/c23/c24/c25/c26/c27/c28/F2", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12/c13/c14/c15/c16/c17/c18/c19/c20/c21/c22/c23/c24/c25/c26/c27/c28/c29/F3", "c0/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12/c13/c14/c15/c16/c17/c18/c19/c20/c21/c22/c23/c24/c25/c26/c27/c28/c29/c30/F4"]}]}, {"name": "root::L1", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}, {"type": "container", "name": "c0", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}, {"type": "container", "name": "c0_0", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}, {"type": "container", "name": "c0_0_0", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}]}]}, {"type": "container", "name": "c0_1", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}, {"type": "container", "name": "c0_1_0", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}]}]}]}, {"type": "container", "name": "c1", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}, {"type": "container", "name": "c1_0", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}]}]}], "keys": [{"node": "root::L1", "name": "PK", "fields": ["PID", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "root::L1", "name": "UK", "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}, "fields": ["PID", "F1", "F2", "F3", "F4", "c0/F1", "c0/F2", "c0/F3", "c0/F4", "c0/c0_0/F1", "c0/c0_0/F2", "c0/c0_0/F3", "c0/c0_0/F4", "c0/c0_0/c0_0_0/F1", "c0/c0_0/c0_0_0/F2", "c0/c0_0/c0_0_0/F3", "c0/c0_0/c0_0_0/F4", "c0/c0_1/F1", "c0/c0_1/F2", "c0/c0_1/F3", "c0/c0_1/F4", "c0/c0_1/c0_1_0/F1", "c0/c0_1/c0_1_0/F2", "c0/c0_1/c0_1_0/F3", "c0/c0_1/c0_1_0/F4", "c1/F1", "c1/F2", "c1/F3", "c1/F4", "c1/c1_0/F1", "c1/c1_0/F2", "c1/c1_0/F3"]}]}]