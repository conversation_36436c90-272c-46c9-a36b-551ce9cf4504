{"instances": {"huawei-network-instance:network-instance::instances::instance": [{"name": "_public_", "huawei-l3vpn:afs": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af": [{"type": "ipv4-unicast", "huawei-routing:routing": {"routing-manage": {"topologys": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology": [{"name": "base"}]}}, "static-routing": {"unicast-route2s": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2": [{"topology-name": "base", "prefix": "*******", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******0", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******1", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******2", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******3", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******4", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******5", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******6", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******7", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******8", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******9", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******0", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******1", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******2", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******3", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******4", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******5", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******8", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******9", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******0", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******1", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******2", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******3", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******4", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******5", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******6", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******7", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******8", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******9", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******0", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******1", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******2", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******3", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******4", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******5", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******6", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******7", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******8", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******9", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******0", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******1", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******2", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******3", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******4", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******5", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******6", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******7", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******8", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "*******9", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}, {"topology-name": "base", "prefix": "********", "mask-length": 32, "nexthop-interface-addresses": {"huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address": [{"interface-name": "GE0/0/10", "address": "***********"}]}}]}}}}]}}]}}