[{"name": "huawei-ifm:ifm0", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm0", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface0", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface0", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm1", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm1", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface1", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface1", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm2", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm2", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface2", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface2", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm3", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm3", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface3", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface3", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm4", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm4", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface4", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface4", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm5", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm5", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface5", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface5", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm6", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm6", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface6", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface6", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm7", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm7", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface7", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface7", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm8", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm8", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface8", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface8", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm9", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm9", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface9", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface9", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm10", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm10", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface10", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface10", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm11", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm11", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface11", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface11", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm12", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm12", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface12", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface12", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm13", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm13", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface13", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface13", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm14", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm14", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface14", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface14", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm15", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm15", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface15", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface15", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm16", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm16", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface16", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface16", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm17", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm17", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface17", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface17", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm18", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm18", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface18", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface18", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm19", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm19", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface19", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface19", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm20", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm20", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface20", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface20", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm21", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm21", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface21", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface21", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm22", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm22", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface22", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface22", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm23", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm23", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface23", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface23", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm24", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm24", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface24", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface24", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm25", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm25", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface25", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface25", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm26", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm26", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface26", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface26", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm27", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm27", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface27", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface27", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm28", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm28", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface28", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface28", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm29", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm29", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface29", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface29", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm30", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm30", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface30", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface30", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm31", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm31", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface31", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface31", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm32", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm32", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface32", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface32", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm33", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm33", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface33", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface33", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm34", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm34", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface34", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface34", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm35", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm35", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface35", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface35", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm36", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm36", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface36", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface36", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm37", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm37", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface37", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface37", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm38", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm38", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface38", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface38", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm39", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm39", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface39", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface39", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm40", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm40", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface40", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface40", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm41", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm41", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface41", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface41", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm42", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm42", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface42", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface42", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm43", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm43", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface43", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface43", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm44", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm44", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface44", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface44", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm45", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm45", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface45", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface45", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm46", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm46", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface46", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface46", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm47", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm47", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface47", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface47", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm48", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm48", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface48", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface48", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm49", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"node": "huawei-ifm:ifm49", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface49", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}], "keys": [{"node": "huawei-ifm:ifm::interfaces::interface49", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]