# -*- coding: utf-8 -*-
# Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
# Description: convert yang json to txt
# Author: wangsi
# Create: 2024-1-11
from io import StringIO
import json
import sys
import os

# 使用方法
# 将vertex.json（必须）和edge.json（可选）放到yangJson2txt.py同一目录下
# 执行：python3 yangJson2txt.py vertex.json edge.json > description.txt
# 也可以执行 python3 yangJson2txt.py jsonFilePath 批量处理
#           若 vertex.json和edge.json文件在 006_graph/yang/template_autoindex/XXX/schema/，
#           则 jsonFilePath 为 006_graph/yang/template_autoindex/XXX/schema/description.txt，
#           输出结果则在 006_graph/yang/template_autoindex/
# 转换结果示例（可能有多棵树，每棵树对应一个yang模型。本例只有一颗树）：
# └── L1 [list] [when 1 + 1 = 2; must ./F1 != 7]
#     ├── F1 [uint32] nullable: True
#     ├── F2 [uint32] default: 3
#     └── T1 [NP]
#         ├── F1 [uint32]
#         ├── T2 [P] [when 1 + 1 = 2; must ../F1 >= 5]
#         │   ├── F1 [uint32]
#         │   ├── F2 [uint32] default: 6
#         │   └── L2 [list]
#         │       └── F1 [uint32] nullable: True
#         └── F2 [uint32] default: 6

# 可以修改成自己想要的分隔符
_branchExtend = '│   '
_branchMid    = '├── '
_branchLast   = '└── '
_spacing      = '    '
_lBracket = " ["
_rBracket = "]"

# 打印阶段需要获取的额外信息，包括节点的原名、类型、校验语句等
_extraInfoKeys = ["name", "type", "presence", "default", "nullable", "clause", "alias"]

# 脚本添加的辅助信息，不参与打印
_ignoreKeys = ["isRoot"]

# 忽略Field中自动生成的id和pid，可以在这里添加其它想要忽略的字段
_ignoreFields = [":id", ":pid", "ID", "PID"]

# prepare阶段，为每个节点生成全局nodeId，从初始值开始递增
_nodeId = 0

def _genUniqueNodeName():
    global _nodeId
    _nodeId += 1
    return "_Node_" + str(_nodeId)

def _getExtraInfo(val):
    info = ""
    if isinstance(val, dict):
        if "alias" in val.keys():
            info += "(" + str(val["alias"]) + ")"
        if "type" in val.keys():
            if val["type"] == "container":
                if "presence" in val.keys() and val["presence"]:
                    val["type"] = "P"
                else:
                    val["type"] = "NP"
            info += _lBracket + val["type"] + _rBracket
        if "default" in val.keys():
            info += " default: " + str(val["default"])
        if "nullable" in val.keys():
            info += " nullable: " +str(val["nullable"])
        if "clause" in val.keys():
            info += _lBracket
            for clause in val["clause"]:
                info += clause["type"] + " " + clause["formula"] + "; "
            info = info[:-2]  # 移除最后的"; "
            info += _rBracket
    else:
        info = val
    return info

# dfs遍历整个树
def _printJson(vertexJson, name='', file=None, prefix='', isLast=True, level=0):
    if isinstance(vertexJson, dict):  # object类型
        prefix += _spacing if isLast else _branchExtend
        for i, key in enumerate(vertexJson.keys()):
            if key in _extraInfoKeys or key in _ignoreKeys:  # 额外信息会整合到一起，不单独打印。这里过滤之后，key应该只剩下nodeName
                continue
            isLast = i == (len(vertexJson) - 1)
            labelName = vertexJson[key]["name"]  # key形如_Node_5，需要转换为原名
            extInfo = _getExtraInfo(vertexJson[key])
            print(prefix, _branchLast if isLast else _branchMid, labelName, extInfo, sep="", file=file)
            level += 1
            _printJson(vertexJson[key], key, file, prefix, isLast, level)

def _buildChildren(jsonIn, jsonOut, level=0):
    # 后续需要打印的附加信息要带上，包括节点的原名、类型、校验语句等
    for key in _extraInfoKeys:
        if key in jsonIn.keys():
            jsonOut[key] = jsonIn[key]

    if "fields" not in jsonIn:  # 叶子节点
        return

    # 构造孩子节点
    for field in jsonIn["fields"]:
        fieldName = field["name"]  # :id/:pid/F1/T1/L1等
        if (fieldName in _ignoreFields):  # :id等字段不处理
            continue
        nodeName = _genUniqueNodeName()
        child = {nodeName:{}}
        _buildChildren(field, child[nodeName], level)
        jsonOut.update(child)

# 根据edge.json中定义的xpath定位到源节点，并将dstNode挂到srcNode上
def _addChild(srcNode, dstNode, xpath):
    if xpath == "":  # source_node_path为空，dstNode直接挂在srcNode上
        srcNode[_genUniqueNodeName()] = dstNode
        dstNode["isRoot"] = False
        return True

    if xpath[-1] != '/':
        xpath += "/"  # 确保xpath以"/"结尾
    labelNames = xpath.split("/")
    del labelNames[0]  # 跳过开头的空字符
    for labelName in labelNames:
        if labelName == "":  # 匹配上结尾的空字符，说明xpath已经走完，找到了目标节点
            srcNode[_genUniqueNodeName()] = dstNode  # 将子节点挂到父节点上
            dstNode["isRoot"] = False
            return True
        nextNodeFound = False
        for val in srcNode.values():
            if isinstance(val, dict) and "name" in val.keys() and val["name"] == labelName:
                srcNode = val
                nextNodeFound = True
        if not nextNodeFound:  # 当前层级的xpath没有匹配上的节点，xpath有误，直接退出
            return False
    return False

# 将原始json转换为方便打印的jsonOut，不需要的信息（例如主键）不保留
# 如果还输入了边信息，则将相关的节点挂到父节点上
def _prepareJson(jsonIns, edgeJsons, jsonOuts):
    for jsonIn in jsonIns:
        rootName = _genUniqueNodeName()
        jsonOut = {rootName:{"isRoot": True}}
        _buildChildren(jsonIn, jsonOut[rootName])
        jsonOuts.append(jsonOut)

    for edgeJson in edgeJsons:
        src = edgeJson["source_vertex_label"]
        dst = edgeJson["dest_vertex_label"]
        xpath = edgeJson["source_node_path"] if "source_node_path" in edgeJson.keys() else ""
        srcNode = {}
        dstNode = {}
        for i, label in enumerate(jsonOuts):
            for val in label.values():  # 预期label里面只有1个键值对
                if val["name"] == src:
                    srcNode = val
                elif val["name"] == dst:
                    dstNode = val
            if srcNode != {} and dstNode != {}:
                break
        if srcNode != {} and dstNode != {}:  # 找到了匹配的两个节点
            _addChild(srcNode, dstNode, xpath)

# 将yang的建表json转换为树形信息（文本形式）
def yangJson2txt(vertexJson, edgeJson=None):
    messageFile = StringIO()
    jsonOuts = []
    _prepareJson(vertexJson, edgeJson, jsonOuts)
    for jsonOut in jsonOuts:
        if list(jsonOut.values())[0]["isRoot"]:
            _printJson(jsonOut, file=messageFile)
    message = messageFile.getvalue()
    messageFile.close()
    return message

def yangJson2txtWithFileName(vertexJsonFile, edgeJsonFile):
    vertexJson = []
    edgeJson = []
    with open(vertexJsonFile, 'r', encoding='UTF-8') as f0:
        vertexJson = json.load(f0)

    try:
        with open(edgeJsonFile, 'r', encoding='UTF-8') as f1:
            edgeJson = json.load(f1)
    except IOError:
        # 允许不指定edge.json，这种情况下vertex之间没有任何联系
        print("Notice: edge info not found")

    return "// 本文件使用yangJson2txt.py生成\n\n"+yangJson2txt(vertexJson, edgeJson)

def yangJson2txtWithFilePath(jsonFilePath):
    for fileName in os.listdir(jsonFilePath):
        file_path = os.path.join(jsonFilePath, fileName)
        file_path = os.path.join(file_path, "schema")
        if not os.path.isdir(file_path):
            continue
        vertexJsonFile = os.path.join(file_path, "vertex_0.json")
        edgeJsonFile = os.path.join(file_path, "vertex_0.json")
        ooutputFile = os.path.join(file_path, "description.txt")
        for fileName2 in os.listdir(file_path):
            if "vertex" in fileName2 and fileName2.endswith(".json"):
                vertexJsonFile = os.path.join(file_path, fileName2)
            elif "edge" in fileName2 and fileName2.endswith(".json"):
                edgeJsonFile = os.path.join(file_path, fileName2)
        parseResult = yangJson2txtWithFileName(vertexJsonFile, edgeJsonFile)
        fo = open(ooutputFile, "w+")
        fo.write(parseResult)
        fo.close()


if __name__ == '__main__':
    vertexJsonFile = "vertex.json"
    edgeJsonFile = "edge.json"

    if len(sys.argv) == 2:
        vertexJsonFile = str(sys.argv[1])
    elif len(sys.argv) > 2:
        vertexJsonFile = str(sys.argv[1])
        edgeJsonFile = str(sys.argv[2])

    if os.path.isfile(vertexJsonFile) and os.path.isfile(edgeJsonFile):
        parseResult = yangJson2txtWithFileName(vertexJsonFile, edgeJsonFile)
        print(parseResult)
    elif os.path.isdir(vertexJsonFile):
        ## if json file path is:    test/dt/st/005_dml/006_graph/yang/template_autoindex/index-test-must-9/schema/edge_0.json
        ## vertexJsonFile is:       test/dt/st/005_dml/006_graph/yang/template_autoindex/
        yangJson2txtWithFilePath(vertexJsonFile)



