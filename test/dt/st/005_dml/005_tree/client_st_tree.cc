#include <jansson.h>
#include "client_common_st.h"
#include "client_option.h"
typedef struct sp_test_1 {
    uint32_t h1;
    uint32_t h2;
} sp_test_1_t;

struct TEST_T4 {
    int64_t A0;
};
Status SeriMemberKey(void *seri, uint8_t *destBuf, GmcStructureResvT *reservedSize)
{
    GmcSeriT *s = (GmcSeriT *)seri;
    *destBuf = 1;
    uint32_t offset = 1;
    uint8_t *srcBuf = s->obj;
    memcpy(destBuf + offset, srcBuf, sizeof(TEST_T4));
    return GMERR_OK;
}

class StClientTree : public StClientCheckCatalog {};

// 该用例补充了GmcFreeNode释放
TEST_F(StClientTree, testManipulateTreeModelNode1)
{
    const auto labelName = "vertexLabelTest1";
    const auto labelJson = vertexLabelTest1Schema;

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    uint32_t value;
    GmcNodeT *rootNode;
    GmcNodeT *node;

    /* insert data */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcClientMemCtxStatInfoT memCtxInfo = {0};
    ret = GmcGetClientMemCtxAllocSize(syncStmt->conn, syncStmt, &memCtxInfo);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetClientMemCtxPhySize(syncStmt->conn, syncStmt, &memCtxInfo);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t expected = memCtxInfo.stmtOpSize;
    ret = GmcGetRootNode(syncStmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(rootNode, "c0", GMC_DATATYPE_UINT32, &(value = 131313), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFreeNode(node);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    for (value = 3; value <= 5; ++value) {
        ret = GmcReuseNodeAppendElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFreeNode(node);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
        ret = GmcNodeSetPropertyByName(node, "b1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcFreeNode(rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetClientMemCtxAllocSize(syncStmt->conn, syncStmt, &memCtxInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expected, memCtxInfo.stmtOpSize);
    ret = GmcGetChildNode(syncStmt, "c4", &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (value = 0; value <= 2; ++value) {
        ret = GmcNodeSetPropertyByName(node, "t1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "t2", GMC_DATATYPE_STRING, "huawei", strlen("huawei"));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    /* verify data */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &(value = 131313), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, "table_pk");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof, &null = eof;
    ret = GmcFetch(syncStmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetClientMemCtxAllocSize(syncStmt->conn, syncStmt, &memCtxInfo);
    EXPECT_EQ(GMERR_OK, ret);
    expected = memCtxInfo.stmtOpSize;
    ret = GmcGetChildNode(syncStmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementCount(node, &value);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);
    ret = GmcFreeNode(node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetClientMemCtxAllocSize(syncStmt->conn, syncStmt, &memCtxInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expected, memCtxInfo.stmtOpSize);

    ret = GmcGetChildNode(syncStmt, "c4", &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementCount(rootNode, &value);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(512u, value);
    node = rootNode;
    for (char i = 2, b2[6 + 1]; i-- > 0;) {
        ret = GmcNodeGetElementByIndex(node, i, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFreeNode(node);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
        ret = GmcNodeGetPropertyByName(node, "t1", &value, sizeof(uint32_t), &null);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ((uint32_t)i, value);
        EXPECT_FALSE(null);
        ret = GmcNodeGetPropertyByName(node, "t2", b2, sizeof(b2) - 1, &null);
        EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);  // not enough space
        ret = GmcNodeGetPropertyByName(node, "t2", b2, sizeof(b2), &null);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ("huawei", b2);
        EXPECT_FALSE(null);
    }
    ret = GmcFreeNode(rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetClientMemCtxAllocSize(syncStmt->conn, syncStmt, &memCtxInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expected, memCtxInfo.stmtOpSize);
    /* update data */

    char *json;
    ret = GmcDumpVertexToJson(syncStmt, 0, &json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexByJson(syncStmt, 0, json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(syncStmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeRemoveElementByIndex(node, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    /* verify data */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &(value = 131313), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, "table_pk");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(syncStmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(syncStmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementCount(node, &value);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, value);

    /* delta update data */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &(value = 131313), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, "table_pk");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(syncStmt, "c4", &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeClear(node);
    EXPECT_EQ(GMERR_OK, ret);

    GmcGetClientMemCtxAllocSize(syncStmt->conn, syncStmt, &memCtxInfo);
    expected = memCtxInfo.stmtOpSize;

    GmcIndexKeyT *key;
    ret = GmcGetChildNode(syncStmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(node, "member_key", &key);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *el1;
    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_UINT32, &(value = 4), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByKey(node, key, &el1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFreeNode(el1);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    GmcNodeT *el2;
    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_UINT32, &(value = 5), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByKey(node, key, &el2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(el1, "b2", GMC_DATATYPE_UINT32, &(value = 123456789), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(el2, "b2", GMC_DATATYPE_UINT32, &(value = 123456789), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeFreeKey(key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFreeNode(node);
    EXPECT_EQ(GMERR_OK, ret);

    GmcGetClientMemCtxAllocSize(syncStmt->conn, syncStmt, &memCtxInfo);
    EXPECT_EQ(expected, memCtxInfo.stmtOpSize);

    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    /* verify data */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(syncStmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(eof);

    ret = GmcGetChildNode(syncStmt, "c4", &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 1; i <= 512; ++i) {
        ret = GmcNodeGetPropertyByName(node, "t1", &value, sizeof(uint32_t), &null);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_TRUE(null);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ((i != 512) ? GMERR_OK : GMERR_NO_DATA, ret);
        ret = GmcFreeNode(node);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    }

    ret = GmcGetChildNode(syncStmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(node, "member_key", &key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_UINT32, &(value = 4), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByKey(node, key, &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 4; i <= 5; ++i) {
        ret = GmcNodeGetPropertyByName(node, "b1", &value, sizeof(uint32_t), &null);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(null);
        EXPECT_EQ(i, value);
        ret = GmcNodeGetPropertyByName(node, "b2", &value, sizeof(uint32_t), &null);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(null);
        EXPECT_EQ(123456789u, value);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ((i != 5) ? GMERR_OK : GMERR_NO_DATA, ret);
    }

    ret = GmcFetch(syncStmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_TRUE(eof);

    /* delete data */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &(value = 131313), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, "table_pk");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(syncStmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_TRUE(eof);
}

TEST_F(StClientTree, testManipulateTreeModelNode1_1)
{
    const auto labelName = "vertexLabelTest1";
    const auto labelJson = vertexLabelTest4Schema;

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    uint32_t value;
    GmcNodeT *rootNode;
    GmcNodeT *node;

    /* insert data */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcClientMemCtxStatInfoT memCtxInfo = {0};
    ret = GmcGetClientMemCtxAllocSize(syncStmt->conn, syncStmt, &memCtxInfo);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetClientMemCtxPhySize(syncStmt->conn, syncStmt, &memCtxInfo);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t expected = memCtxInfo.stmtOpSize;
    ret = GmcGetRootNode(syncStmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(rootNode, "c0", GMC_DATATYPE_UINT32, &(value = 131313), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFreeNode(node);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    for (value = 3; value <= 5; ++value) {
        ret = GmcReuseNodeAppendElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFreeNode(node);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
        ret = GmcNodeSetPropertyByName(node, "b1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcFreeNode(rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetClientMemCtxAllocSize(syncStmt->conn, syncStmt, &memCtxInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expected, memCtxInfo.stmtOpSize);
    ret = GmcGetChildNode(syncStmt, "c4", &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (value = 0; value <= 2; ++value) {
        ret = GmcNodeSetPropertyByName(node, "t1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "t2", GMC_DATATYPE_STRING, "huawei", strlen("huawei"));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    /* verify data */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &(value = 131313), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, "table_pk");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof, &null = eof;
    ret = GmcFetch(syncStmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetClientMemCtxAllocSize(syncStmt->conn, syncStmt, &memCtxInfo);
    EXPECT_EQ(GMERR_OK, ret);
    expected = memCtxInfo.stmtOpSize;
    ret = GmcGetChildNode(syncStmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementCount(node, &value);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);
    ret = GmcFreeNode(node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetClientMemCtxAllocSize(syncStmt->conn, syncStmt, &memCtxInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expected, memCtxInfo.stmtOpSize);

    ret = GmcGetChildNode(syncStmt, "c4", &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementCount(rootNode, &value);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(512u, value);
    node = rootNode;
    for (char i = 2, b2[6 + 1]; i-- > 0;) {
        ret = GmcNodeGetElementByIndex(node, i, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFreeNode(node);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
        ret = GmcNodeGetPropertyByName(node, "t1", &value, sizeof(uint32_t), &null);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ((uint32_t)i, value);
        EXPECT_FALSE(null);
        ret = GmcNodeGetPropertyByName(node, "t2", b2, sizeof(b2) - 1, &null);
        EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);  // not enough space
        ret = GmcNodeGetPropertyByName(node, "t2", b2, sizeof(b2), &null);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ("huawei", b2);
        EXPECT_FALSE(null);
    }
    ret = GmcFreeNode(rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetClientMemCtxAllocSize(syncStmt->conn, syncStmt, &memCtxInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expected, memCtxInfo.stmtOpSize);
    /* update data */

    char *json;
    ret = GmcDumpVertexToJson(syncStmt, 0, &json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexByJson(syncStmt, 0, json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(syncStmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeRemoveElementByIndex(node, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    /* verify data */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &(value = 131313), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, "table_pk");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(syncStmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(syncStmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementCount(node, &value);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, value);

    /* delta update data */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &(value = 131313), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, "table_pk");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(syncStmt, "c4", &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeClear(node);
    EXPECT_EQ(GMERR_OK, ret);

    GmcGetClientMemCtxAllocSize(syncStmt->conn, syncStmt, &memCtxInfo);
    expected = memCtxInfo.stmtOpSize;

    GmcIndexKeyT *key;
    ret = GmcGetChildNode(syncStmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(node, "member_key", &key);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        GmcNodeT *node1 = NULL;
        GmcIndexKeyT *key1 = NULL;
        GmcIndexKeyT *key2 = NULL;
        GmcIndexKeyT *key3 = NULL;
        ret = GmcGetChildNode(syncStmt, "c2", &node1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAllocKeyById(node1, 2, &key1);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, key1->indexLabel->idxLabelBase.indexId);
        ret = GmcNodeAllocKeyById(node1, 3, &key2);
        EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
        ret = GmcNodeAllocKeyById(node1, 4, &key3);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, key3->indexLabel->idxLabelBase.indexId);

        ret = GmcNodeFreeKey(key1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeFreeKey(key3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFreeNode(node1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    GmcNodeT *el1;
    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_UINT32, &(value = 4), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByKey(node, key, &el1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFreeNode(el1);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    GmcNodeT *el2;
    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_UINT32, &(value = 5), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByKey(node, key, &el2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(el1, "b2", GMC_DATATYPE_UINT32, &(value = 123456789), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(el2, "b2", GMC_DATATYPE_UINT32, &(value = 123456789), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeFreeKey(key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFreeNode(node);
    EXPECT_EQ(GMERR_OK, ret);

    GmcGetClientMemCtxAllocSize(syncStmt->conn, syncStmt, &memCtxInfo);
    EXPECT_EQ(expected, memCtxInfo.stmtOpSize);

    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    /* verify data */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(syncStmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(eof);

    ret = GmcGetChildNode(syncStmt, "c4", &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 1; i <= 512; ++i) {
        ret = GmcNodeGetPropertyByName(node, "t1", &value, sizeof(uint32_t), &null);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_TRUE(null);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ((i != 512) ? GMERR_OK : GMERR_NO_DATA, ret);
        ret = GmcFreeNode(node);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    }

    ret = GmcGetChildNode(syncStmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(node, "member_key", &key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_UINT32, &(value = 4), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByKey(node, key, &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 4; i <= 5; ++i) {
        ret = GmcNodeGetPropertyByName(node, "b1", &value, sizeof(uint32_t), &null);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(null);
        EXPECT_EQ(i, value);
        ret = GmcNodeGetPropertyByName(node, "b2", &value, sizeof(uint32_t), &null);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(null);
        EXPECT_EQ(123456789u, value);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ((i != 5) ? GMERR_OK : GMERR_NO_DATA, ret);
    }

    ret = GmcFetch(syncStmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_TRUE(eof);

    /* delete data */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &(value = 131313), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, "table_pk");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(syncStmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_TRUE(eof);
}

TEST_F(StClientTree, testManipulateTreeModelNode2)
{
    const auto labelName = "vertexLabelTest1";
    const auto labelJson = vertexLabelTest1Schema;

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t value;
    GmcNodeT *node;
    ret = GmcGetRootNode(syncStmt, &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "c0", GMC_DATATYPE_UINT32, &(value = 131313), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetChild(node, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (value = 3; value <= 5; ++value) {
        ret = GmcNodeAppendElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &(value = 131313), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, "table_pk");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(syncStmt, &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAppendElement(node, &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "b1", GMC_DATATYPE_UINT32, &(value = 4), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_MEMBER_KEY_VIOLATION, ret);

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &(value = 131313), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, "table_pk");
    EXPECT_EQ(GMERR_OK, ret);

    GmcIndexKeyT *key;
    ret = GmcGetChildNode(syncStmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(node, "member_key", &key);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_UINT32, &(value = 4), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByKey(node, key, &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "b2", GMC_DATATYPE_UINT32, &(value = 123456789), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_UINT32, &(value = 5), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByKey(node, key, &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "b1", GMC_DATATYPE_UINT32, &(value = 4), sizeof(uint32_t));  // duplicated b1
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "b2", GMC_DATATYPE_UINT32, &(value = 123456789), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_MEMBER_KEY_VIOLATION, ret);
    GmcNodeFreeKey(key);
}

TEST_F(StClientTree, testCreateVertexLabelTreeModel)
{
    const char *g_label_name = "sysModel";
    const char *test_delat_config_json = R"({"max_record_count":1000})";
    GmcStmtT *stmt = syncStmt;
    // create table
    int ret = GmcCreateVertexLabel(stmt, sysModelSchema, test_delat_config_json);
    ASSERT_EQ(GMERR_OK, ret);
    // insert data
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    /* 测试获取Tree Node，并通过获取Node的属性 */
    GmcNodeT *treenode;
    char spVal[8] = {};

    // c0为root结点
    uint32_t F100, size = 0;
    bool isNull100;
    ret = GmcGetRootNode(stmt, &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(treenode, "c0", GMC_DATATYPE_UINT32, &size, sizeof(size));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(treenode, "c0", &F100, sizeof(uint32_t), &isNull100);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertySizeByName(treenode, "c0", &F100);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByIdWithoutType(treenode, 0, NULL, sizeof(size));
    EXPECT_NE(GMERR_OK, ret);
    ret = GmcNodeSetPropertyById(treenode, 0, GMC_DATATYPE_UINT32, &size, sizeof(size));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertySizeById(NULL, 1, &size);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcNodeGetPropertyById(treenode, 0, &size, sizeof(size), &isNull100);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetSuperfieldSizeByName(treenode, (char *)"superfiled0", NULL);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcNodeSetSuperfieldByName(treenode, (char *)"superfiled0", NULL, 26);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    ret = GmcNodeGetSuperfieldById(NULL, 0, spVal, 8);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    sp_test_1_t sp_2_2;
    sp_2_2 = {1000, 1101};
    ret = GmcNodeSetSuperfieldById(NULL, 1, &sp_2_2, sizeof(sp_2_2));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcNodeClear(treenode);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetElementCount(treenode, &F100);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetElementByIndex(treenode, 0, &treenode);
    EXPECT_EQ(GMERR_OK, ret);

    // c0非node结点，报错
    ret = GmcGetChildNode(stmt, "c0", &treenode);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    // c1.f1非node结点，报错
    ret = GmcGetChildNode(stmt, "c1/f1", &treenode);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);

    ret = GmcGetChildNode(stmt, "c1", &treenode);
    EXPECT_EQ(GMERR_OK, ret);

    const char *nodeName = NULL;
    ret = GmcNodeGetName(treenode, &nodeName);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_STREQ("c1", nodeName);

    GmcTreeNodeTypeE nodeType = GMC_NODE_BUTT;
    ret = GmcNodeGetType(treenode, &nodeType);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMC_NODE_RECORD, nodeType);

    ret = GmcGetChildNode(stmt, "c6/t2", &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetType(treenode, &nodeType);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMC_NODE_ARRAY, nodeType);

    ret = GmcNodeGetName(treenode, &nodeName);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_STREQ("t2", nodeName);

    ret = GmcGetChildNode(stmt, "c4", &treenode);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetType(treenode, &nodeType);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMC_NODE_VECTOR, nodeType);
    /* End 获取TreeNode测试 */

    // test point: 顶点句柄待插入的值(int32_t)，与实际类型符合, 预期成功
    uint32_t uint32_tmp = 131313;
    ret = GmcGetRootNode(stmt, &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(treenode, "c0", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    // 对变长类型进行测试
    char c3Value[] = "huawei";
    ret = GmcNodeSetPropertyByName(treenode, "c3", GMC_DATATYPE_STRING, c3Value, strlen(c3Value));
    EXPECT_EQ(GMERR_OK, ret);

    GmcPropValueT fieldValue;
    fieldValue.type = GMC_DATATYPE_STRING;
    fieldValue.size = strlen(c3Value);
    fieldValue.value = c3Value;
    memcpy_s(fieldValue.propertyName, GMC_PROPERTY_NAME_MAX_LEN, "c3", strlen("c3") + 1);

    bool asExpt;
    ret = GmcVertexExptFieldValue(stmt, &fieldValue, &asExpt);
    EXPECT_TRUE(asExpt);

    ret = GmcNodeExptFieldValue(treenode, &fieldValue, &asExpt);
    EXPECT_TRUE(asExpt);

    ret = GmcGetChildNode(stmt, "c1", &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(treenode, "f1", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(stmt, "c4", &treenode);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 3; i <= 5; i++) {
        ret = GmcNodeAppendElement(treenode, &treenode);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(treenode, "b1", GMC_DATATYPE_UINT32, &i, sizeof(i));
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcNodeSetPropertyByName(treenode, "b2", GMC_DATATYPE_UINT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcGetChildNode(stmt, "c6/t2", &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i <= 2; i++) {
        ret = GmcNodeSetPropertyByName(treenode, "h1", GMC_DATATYPE_UINT32, &i, sizeof(i));
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcNodeSetPropertyByName(treenode, "h2", GMC_DATATYPE_UINT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeGetNextElement(treenode, &treenode);
    }

    for (uint32_t i = 3; i <= 5; i++) {
        sp_test_1_t sp_1 = {i, i + 1};
        ret = GmcNodeSetSuperfieldByName(treenode, "superfield0", &sp_1, sizeof(sp_1));
        EXPECT_EQ(ret, GMERR_OK);
        GmcNodeGetNextElement(treenode, &treenode);
    }

    ret = GmcGetChildNode(stmt, "c6", &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(treenode, "t3", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 查询属性值
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "table_pk");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t F0;
    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt, "c0", &F0, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(uint32_tmp, F0);

    ret = GmcGetChildNode(stmt, "c1", &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(treenode, "f1", &F0, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(uint32_tmp, F0);

    ret = GmcGetChildNode(stmt, "c4", &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByIndex(treenode, 0, &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 3; i <= 5; i++) {
        ret = GmcNodeGetPropertyByName(treenode, "b1", &F0, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(i, F0);
        GmcNodeGetNextElement(treenode, &treenode);
    }

    ret = GmcGetChildNode(stmt, "c6/t2", &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i <= 2; i++) {
        ret = GmcNodeGetPropertyByName(treenode, "h2", &F0, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(i, F0);
        GmcNodeGetNextElement(treenode, &treenode);
    }

    for (uint32_t i = 3; i <= 5; i++) {
        uint32_t length;
        ret = GmcNodeGetSuperfieldSizeByName(treenode, "superfield0", &length);
        EXPECT_EQ(8u, length);

        uint32_t superFieldSize = 0;
        ret = GmcNodeGetSuperfieldSizeById(treenode, 0, &superFieldSize);
        EXPECT_EQ(superFieldSize, 8);

        sp_test_1_t sp_1_get = {};
        ret = GmcNodeGetSuperfieldByName(treenode, "superfield0", &sp_1_get, sizeof(sp_test_1_t));
        EXPECT_EQ(i, sp_1_get.h1);
        EXPECT_EQ(i + 1, sp_1_get.h2);
        GmcNodeGetNextElement(treenode, &treenode);
    }

    /* 测试获取Tree Node 为array/vector时的排序功能 */
    // 升序排序
    ret = GmcGetChildNode(stmt, "c1", &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSortElement(treenode, "f1", GMC_ORDER_ASC);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, "c4", &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSortElement(treenode, "b2", GMC_ORDER_ASC);
    EXPECT_EQ(GMERR_OK, ret);
    // 排序数据含空
    ret = GmcGetChildNode(stmt, "c6/t2", &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSortElement(treenode, "h2", GMC_ORDER_ASC);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(stmt, "c4", &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSortElement(treenode, "b3", GMC_ORDER_ASC);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    // 降序排序
    GmcGetChildNode(stmt, "c1", &treenode);
    ret = GmcNodeSortElement(treenode, "f1", GMC_ORDER_DESC);
    EXPECT_EQ(GMERR_OK, ret);
    GmcGetChildNode(stmt, "c4", &treenode);
    ret = GmcNodeSortElement(treenode, "b2", GMC_ORDER_DESC);
    EXPECT_EQ(GMERR_OK, ret);
    // 排序数据含空
    GmcGetChildNode(stmt, "c6/t2", &treenode);
    ret = GmcNodeSortElement(treenode, "h2", GMC_ORDER_DESC);
    EXPECT_EQ(GMERR_OK, ret);
    GmcGetChildNode(stmt, "c4", &treenode);
    ret = GmcNodeSortElement(treenode, "b3", GMC_ORDER_DESC);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    /* Node 为array/vector时的排序 End */
    GmcGetChildNode(stmt, "c6", &treenode);
    ret = GmcNodeGetPropertyByName(treenode, "t3", &F0, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(uint32_tmp, F0);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StClientTree, testClearRecordNodeTreeModel)
{
    const char *g_label_name = "sysModel";
    const char *test_delat_config_json = R"({"max_record_count":1000})";
    GmcStmtT *stmt = syncStmt;
    // create table
    int ret = GmcCreateVertexLabel(stmt, sysModelSchema, test_delat_config_json);
    ASSERT_EQ(GMERR_OK, ret);
    // insert data
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    /* 测试获取Tree Node，并通过获取Node的属性 */
    GmcNodeT *treenode;

    // c0为root结点
    uint32_t size = 111;
    ret = GmcGetRootNode(stmt, &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(treenode, "c0", GMC_DATATYPE_UINT32, &size, sizeof(size));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t uint32_tmp = 111;
    ret = GmcGetChildNode(stmt, "c1", &treenode);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(treenode, "f1", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(treenode, "f2", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(stmt, "c6/t2", &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i <= 2; i++) {
        ret = GmcNodeSetPropertyByName(treenode, "h1", GMC_DATATYPE_UINT32, &i, sizeof(i));
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcNodeSetPropertyByName(treenode, "h2", GMC_DATATYPE_UINT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeGetNextElement(treenode, &treenode);
    }

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: clear record,成功
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(stmt, "c1", &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeClear(treenode);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(stmt, "c6/t2", &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeClear(treenode);
    EXPECT_EQ(GMERR_OK, ret);
    // clear之后写入数据不生效
    for (uint32_t i = 2; i <= 4; i++) {
        ret = GmcNodeSetPropertyByName(treenode, "h1", GMC_DATATYPE_UINT32, &i, sizeof(i));
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcNodeSetPropertyByName(treenode, "h2", GMC_DATATYPE_UINT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeGetNextElement(treenode, &treenode);
    }

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool eof;
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(eof);

    GmcNodeT *root;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);

    // 无数据，获取报错GMERR_NO_DATA
    ret = GmcGetNodeWithKeyBuf(root, "c1", 0, NULL, &treenode);
    EXPECT_EQ(GMERR_NO_DATA, ret);

    ret = GmcGetNodeWithKeyBuf(root, "c6", 0, NULL, &treenode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetNodeWithKeyBuf(treenode, "t2", 0, NULL, &treenode);
    EXPECT_EQ(GMERR_NO_DATA, ret);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StClientTree, testUpdateNode)
{
    const auto labelName = "sysErr";
    const auto labelJson = sysErrSchema;

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    GmcNodeT *node, *root;
    uint32_t keyId = 0;

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(syncStmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, "c0", GMC_DATATYPE_UINT32, &keyId, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(syncStmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "c1", &node);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f1 = 10;
    ret = GmcNodeSetPropertyByName(node, "f1", GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    // scan
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof, &isNull = eof;
    ret = GmcFetch(syncStmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(eof);
    ret = GmcGetRootNode(syncStmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetNodeWithKeyBuf(root, "c1", keyId, NULL, &node);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "b1", &f1, sizeof(uint32_t), &isNull);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(f1, 10u);
}

TEST_F(StClientTree, testCreateVertexLabelTreeModelErr)
{
    const auto labelName = "sysErr";
    const auto labelJson = sysErrSchema;

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    GmcNodeT *node, *root;
    uint32_t keyId = 0;

    GmcClientMemCtxStatInfoT memCtxInfo = {0};
    GmcGetClientMemCtxAllocSize(syncStmt->conn, syncStmt, &memCtxInfo);
    uint64_t expected = memCtxInfo.stmtOpSize;
    ret = GmcGetRootNode(syncStmt, &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetNodeWithKeyBuf(node, "c1", keyId, NULL, &root);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFreeNode(root);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcNodeSetPropertyByName(node, "c3", GMC_DATATYPE_NULL, NULL, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFreeNode(node);
    EXPECT_EQ(GMERR_OK, ret);
    GmcGetClientMemCtxAllocSize(syncStmt->conn, syncStmt, &memCtxInfo);
    EXPECT_EQ(expected, memCtxInfo.stmtOpSize);

    ret = GmcGetChildNode(syncStmt, "c1", &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetNodeWithKeyBuf(node, "c3", keyId, NULL, &root);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);
    GmcSeriT keyBuf = (GmcSeriT){0};
    keyBuf.bufSize = 1;
    uint8_t key_obj = {0};
    keyBuf.obj = (uint8_t *)&key_obj;
    keyBuf.seriFunc = SeriMemberKey;
    ret = GmcGetNodeWithKeyBuf(node, "f2", keyId, &keyBuf, &root);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);
    ret = GmcNodeSetPropertyByName(node, "f2", GMC_DATATYPE_CHAR, NULL, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    // no longer supports path
    uint32_t uint32_tmp = 131313;
    ret = GmcNodeSetPropertyByName(node, "c1/f1", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = GmcSetVertexProperty(syncStmt, "c1/f1", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
}

TEST_F(StClientTree, testCreateVertexLabelTreeModel1)
{
    const auto labelName = "sys1";
    const auto labelJson = sys1Schema;

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    GmcNodeT *node;
    ret = GmcGetRootNode(syncStmt, &node);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t uint32_tmp = 131313;
    ret = GmcNodeSetPropertyByName(node, "c0", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetChild(node, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i <= 7; i++) {
        ret = GmcNodeAppendElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcNodeSetPropertyByName(node, "b2", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcGetChildNode(syncStmt, "c4", &node);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i <= 2; i++) {
        ret = GmcNodeSetPropertyByName(node, "t1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcNodeSetPropertyByName(node, "t2", GMC_DATATYPE_STRING, "huawei", strlen("huawei"));
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeGetNextElement(node, &node);
    }

    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(syncStmt, "c2", &node);

    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 5; i <= 7; i++) {
        ret = GmcNodeGetElementByIndex(node, i, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b1", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_t));
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcNodeSetPropertyByName(node, "b2", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcGetChildNode(syncStmt, "c4", &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 3; i <= 5; i++) {
        ret = GmcNodeSetPropertyByName(node, "t1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcNodeSetPropertyByName(node, "t2", GMC_DATATYPE_STRING, "", strlen(""));
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeGetNextElement(node, &node);
    }

    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, "table_pk");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    // scan扫描
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof, &isNull = eof;
    ret = GmcFetch(syncStmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(eof);

    ret = GmcGetChildNode(syncStmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 5, v; i <= 7; i++) {
        ret = GmcNodeGetElementByIndex(node, i, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "b1", &v, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(uint32_tmp, v);
    }

    ret = GmcGetChildNode(syncStmt, "c4", &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 3, v; i <= 5; i++) {
        ret = GmcNodeGetPropertyByName(node, "t1", &v, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(i, v);
        ret = GmcNodeGetPropertySizeByName(node, "t2", &v);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(v, sizeof(""));
        GmcNodeGetNextElement(node, &node);
    }
}

TEST_F(StClientTree, testTreeModelVertexWithCondition)
{
    const auto labelName = "sysModel2";
    const auto labelJson = sysModel2Schema;

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    GmcNodeT *node;
    GmcGetRootNode(syncStmt, &node);
    const uint32_t c0 = 131313;
    ret = GmcNodeSetPropertyByName(node, "c0", GMC_DATATYPE_UINT32, &c0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    GmcGetChildNode(syncStmt, "c1", &node);
    ret = GmcNodeSetPropertyByName(node, "f1", GMC_DATATYPE_UINT32, &c0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    GmcGetChildNode(syncStmt, "c6/t2", &node);
    for (uint32_t i = 0; i <= 2; i++) {
        ret = GmcNodeSetPropertyByName(node, "h1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcNodeSetPropertyByName(node, "h2", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeGetNextElement(node, &node);
    }
    for (uint32_t i = 3; i <= 5; i++) {
        sp_test_1_t sp_1 = {i, i + 1};
        ret = GmcNodeSetSuperfieldByName(node, "superfield0", &sp_1, sizeof(sp_1));
        EXPECT_EQ(ret, GMERR_OK);
        GmcNodeGetNextElement(node, &node);
    }

    GmcGetChildNode(syncStmt, "c6", &node);
    ret = GmcNodeSetPropertyByName(node, "t3", GMC_DATATYPE_UINT32, &c0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    // update by condition
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcGetChildNode(syncStmt, "c1", &node);
    uint32_t res_val = 10;
    ret = GmcNodeSetPropertyByName(node, "f2", GMC_DATATYPE_UINT32, &res_val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(syncStmt, "sysModel2.c1/f1 = 131313");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t affectRows, value;
    ret = GmcGetStmtAttr(syncStmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, affectRows);

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &c0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, "table_pk");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    ret = GmcFetch(syncStmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);

    GmcGetChildNode(syncStmt, "c1", &node);
    bool isNull;
    ret = GmcNodeGetPropertyByName(node, "f2", &value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(10u, value);

    // delete by condition
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(syncStmt, "sysModel2.c1/f1 = 131313");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(syncStmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, affectRows);

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &c0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, "table_pk");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(syncStmt, &isFinish);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(isFinish, true);
}

/*
 sysModel3{f1 f2 c1{f1 f2 c2{f1 c3{f1 f2}}} c4{f1}}
*/
TEST_F(StClientTree, testTreeModelVertexWithCondition2)
{
    const auto labelName = "sysModel3";
    const auto labelJson = sysModel3Schema;
    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 3; i++) {
        GmcNodeT *node;
        ret = GmcGetRootNode(syncStmt, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "f1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "f2", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(syncStmt, "c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "f1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(node, "c2", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "f1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // update node not exist
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t res_val = 10;
    ret = GmcSetVertexProperty(syncStmt, "f1", GMC_DATATYPE_UINT32, &res_val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(syncStmt, "sysModel3.c5/f1 = 1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    const char *lastErrorStr = GmcGetLastError();
    EXPECT_STREQ("Not normal name. Node name is illegal, name: c5.", lastErrorStr);
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(syncStmt, "sysModel3.c5/f1 = 1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ("Not normal name. Node name is illegal, name: c5.", lastErrorStr);
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(syncStmt, "sysModel3.c5/f1 = 1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ("Not normal name. Node name is illegal, name: c5.", lastErrorStr);

    // update by condition 非首节点
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(syncStmt, "f1", GMC_DATATYPE_UINT32, &res_val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(syncStmt, "sysModel3.c2/f1 = 1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t affectRows, value;
    ret = GmcGetStmtAttr(syncStmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, affectRows);

    // update by condition 首节点
    res_val = 11;
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(syncStmt, "f1", GMC_DATATYPE_UINT32, &res_val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(syncStmt, "sysModel3.sysModel3/f1 = 10");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(syncStmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, affectRows);

    // condition scan
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(syncStmt, "sysModel3.c2/f1 = 1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t cnt = 0;
    while (true) {
        bool eof;
        ret = GmcFetch(syncStmt, &eof);
        ASSERT_EQ(GMERR_OK, ret);
        cnt++;
        if (eof || cnt > 5) {
            break;
        }
        bool isNull;
        ret = GmcGetVertexPropertyByName(syncStmt, "f1", &value, sizeof(uint32_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(false, isNull);
        EXPECT_EQ(res_val, value);
    }
    // delete by condition
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(syncStmt, "sysModel3.c2/f1 = 1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(syncStmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, affectRows);

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t count;
    ret = GmcGetVertexCount(syncStmt, labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, count);
}

TEST_F(StClientTree, testTreeModelVertexWithCondition3)
{
    const auto labelName = "sysModel3";
    const auto labelJson = sysModel3Schema;

    uint32_t value;
    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 10000; i++) {
        GmcNodeT *node;
        ret = GmcGetRootNode(syncStmt, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "f1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "f2", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(syncStmt, "c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "f1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(node, "c2", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "f1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // condition scan
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t filter_value = 100;
    GmcFilterStructT filter = {
        .fieldId = 0, .nodeName = "c2", .compOp = GMC_OP_LARGE, .value = &filter_value, .valueLen = sizeof(uint32_t)};
    ret = GmcSetFilterStructure(syncStmt, &filter);
    EXPECT_EQ(GMERR_OK, ret);

    filter_value = 1000;
    GmcFilterStructT filter2 = {.fieldId = 0,
        .nodeName = "c2",
        .compOp = GMC_OP_SMALL_EQUAL,
        .value = &filter_value,
        .valueLen = sizeof(uint32_t)};
    ret = GmcSetFilterStructure(syncStmt, &filter2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t cnt = 0;
    while (true) {
        bool eof;
        ret = GmcFetch(syncStmt, &eof);
        ASSERT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }
        cnt++;
        bool isNull;
        ret = GmcGetVertexPropertyByName(syncStmt, "f1", &value, sizeof(uint32_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(false, isNull);
        EXPECT_EQ(cnt + 100, value);
    }
    ASSERT_EQ(cnt, 900U);
}

TEST_F(StClientTree, testMemberKeyUniqueness)
{
    // 持久化当前不支持member key
    PERSISTENCE_NOT_SUPPORT;
    const auto labelName = "vertexLabelTest2";
    const auto labelJson = R"([{
      "version": "2.0", "type": "record", "name": "vertexLabelTest2",
      "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c2", "type": "record",
          "vector": true, "size": 1024,
          "fields": [
            { "name": "b1", "type": "uint32" },
            { "name": "b2", "type": "uint32" }
          ]
        },
        { "name": "c4", "type": "record",
          "fixed_array": true, "size": 3,
          "fields": [
            { "name": "t2", "type": "string", "size": 8 }
          ]
        }
      ],
      "keys": [
        { "name": "table_pk", "index": { "type": "primary" },
          "node": "vertexLabelTest2",
          "fields": [ "c0" ],
          "constraints": { "unique": true }
        },
        { "name": "member_key1", "index": { "type": "none" },
          "node": "c2",
          "fields": [ "b1", "b2" ],
          "constraints": { "unique": true }
        },
        { "name": "member_key2", "index": { "type": "none" },
          "node": "c4",
          "fields": [ "t2"],
          "constraints": { "unique": false }
        }
      ]
    }])";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    GmcNodeT *node;
    ret = GmcGetRootNode(syncStmt, &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 1; ++i) {
        ret = GmcNodeSetPropertyByName(node, "c0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcGetChildNode(syncStmt, "c4", &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 3; ++i) {
        ret = GmcNodeGetElementByIndex(node, i, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "t2", GMC_DATATYPE_STRING, "val", strlen("val"));
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcGetChildNode(syncStmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0, v = 0; i < 2; ++i) {
        ret = GmcNodeAppendElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b1", GMC_DATATYPE_UINT32, &v, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b2", GMC_DATATYPE_UINT32, &v, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 备份数据
    char *json;
    ret = GmcDumpVertexToJson(syncStmt, 0, &json);
    EXPECT_EQ(GMERR_OK, ret);

    // c2 member key 冲突
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_MEMBER_KEY_VIOLATION, ret);

    // 恢复数据
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexByJson(syncStmt, 0, json);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(syncStmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0, v = 1; i < 1; ++i) {
        ret = GmcNodeGetElementByIndex(node, i, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b1", GMC_DATATYPE_UINT32, &v, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 解决冲突后成功
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StClientTree, testJsonImportAndRemoveByKey)
{
    const auto labelName = "access_list";
    const auto labelJson = R"(
    [{
        "name": "access_list",
        "version": "2.0",
        "type": "record",
        "fields": [
            { "name": "nftable_table_name", "type": "string", "size": 128 },
            { "name": "nftable_table_family", "type": "string", "size": 128 },
            { "name": "nftable_table_flags", "type": "uint32" },
            { "name": "nftable_table_use", "type": "uint32" },
            {
                "type": "record",
                "name": "nftable_chain",
                "nullable": true,
                "vector": true,
                "size": 2,
                "fields": [
                    { "name": "nftable_chain_name", "type": "string", "size": 128 },
                    { "name": "nftable_chain_type", "type": "string", "size": 128 },
                    { "name": "nftable_chain_table", "type": "string", "size": 128 },
                    { "name": "nftable_chain_family", "type": "string", "size": 128 },
                    { "name": "nftable_chain_policy", "type": "uint32" },
                    { "name": "nftable_chain_hooknum", "type": "uint32" },
                    { "name": "nftable_chain_prio", "type": "uint32" },
                    { "name": "nftable_chain_use", "type": "uint32" },
                    { "name": "nftable_chain_handle", "type": "uint64" },
                    { "name": "nftable_chain_basechainflag", "type": "uint8" },
                    { "name": "nftable_chain_policyflag", "type": "uint8" },
                    { "name": "nftable_chain_devflag", "type": "uint8" },
                    {
                        "type": "record",
                        "name": "nftable_rule",
                        "nullable": true,
                        "vector": true,
                        "size": 3,
                        "fields": [
                            { "name": "nftable_rule_handle", "type": "uint32" },
                            { "name": "nftable_rule_position", "type": "uint32" },
                            { "name": "nftable_rule_table", "type": "string", "size": 128 },
                            { "name": "nftable_rule_chain", "type": "string", "size": 128 },
                            { "name": "nftable_rule_description", "type": "string", "size": 128 }
                        ]
                    }
                ]
            }
        ],
        "keys": [
            {
                "name": "access_control_list_key",
                "node": "access_list",
                "fields": ["nftable_table_name"],
                "index": { "type": "primary" }
            },
            {
                "name": "nftable_chain_key",
                "node": "nftable_chain",
                "fields": ["nftable_chain_name"],
                "index": { "type": "none" },
                "constraints": { "unique": true }
            },
            {
                "name": "nftable_rule_key",
                "node": "nftable_chain/nftable_rule",
                "fields": ["nftable_rule_handle"],
                "index": { "type": "none" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* test import json */

    const auto json = R"(
    {
        "nftable_table_name": "test1",
        "nftable_table_family": "",
        "nftable_table_flags": 0,
        "nftable_table_use": 0,
        "nftable_chain": [
            {
                "nftable_chain_name": "chain1",
                "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;",
                "nftable_chain_table": "test1",
                "nftable_chain_family": "",
                "nftable_chain_policy": 0,
                "nftable_chain_hooknum": 0,
                "nftable_chain_prio": 0,
                "nftable_chain_use": 0,
                "nftable_chain_handle": "0",
                "nftable_chain_basechainflag": 0,
                "nftable_chain_policyflag": 0,
                "nftable_chain_devflag": 0,
                "nftable_rule": [
                    {
                        "nftable_rule_handle": 33,
                        "nftable_rule_position": 0,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                    },
                    {
                        "nftable_rule_handle": 34,
                        "nftable_rule_position": 1,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                    },
                    {
                        "nftable_rule_handle": 35,
                        "nftable_rule_position": 2,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                    }
                ]
            },
            {
                "nftable_chain_name": "chain2",
                "nftable_chain_type": "type filter hook prerouting priority 20; policy accept;",
                "nftable_chain_table": "test1",
                "nftable_chain_family": "",
                "nftable_chain_policy": 0,
                "nftable_chain_hooknum": 0,
                "nftable_chain_prio": 0,
                "nftable_chain_use": 0,
                "nftable_chain_handle": "0",
                "nftable_chain_basechainflag": 0,
                "nftable_chain_policyflag": 0,
                "nftable_chain_devflag": 0,
                "nftable_rule": [
                    {
                        "nftable_rule_handle": 33,
                        "nftable_rule_position": 0,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                    },
                    {
                        "nftable_rule_handle": 34,
                        "nftable_rule_position": 1,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                    },
                    {
                        "nftable_rule_handle": 35,
                        "nftable_rule_position": 35,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                    }
                ]
            }
        ]
    }
    )";

    ret = GmcSetVertexByJson(syncStmt, GMC_JSON_REJECT_DUPLICATES, json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    // fetch data
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof, &null = eof;
    ret = GmcFetch(syncStmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(eof);

    // get nodes
    GmcNodeT *node, *chain, *rule;
    ret = GmcGetChildNode(syncStmt, "nftable_chain", &chain);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetChildNode(syncStmt, "nftable_chain/nftable_rule", &rule);
    EXPECT_EQ(GMERR_OK, ret);

    // alloc key
    GmcIndexKeyT *key_chain, *key_rule;
    ret = GmcNodeAllocKey(chain, "nftable_chain_key", &key_chain);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key_chain, 0, GMC_DATATYPE_STRING, "chain2", strlen("chain2"));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(rule, "nftable_rule_key", &key_rule);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key_rule, 0, GMC_DATATYPE_UINT32, &literal<uint32_t, 35>::value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // get by key on incompatible node
    ret = GmcNodeGetElementByKey(chain, key_rule, &node);
    EXPECT_NE(GMERR_OK, ret);
    ret = GmcNodeGetElementByKey(rule, key_chain, &node);
    EXPECT_NE(GMERR_OK, ret);

    // get by key
    ret = GmcNodeGetElementByKey(chain, key_chain, &chain);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(chain, "nftable_rule", &rule);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByKey(rule, key_rule, &rule);
    EXPECT_EQ(GMERR_OK, ret);

    // validate data
    uint32_t value;
    ret = GmcNodeGetPropertyByName(rule, "nftable_rule_position", &value, sizeof(value), &null);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(null);
    EXPECT_EQ(35u, value);

    // parepare replace data
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexByJson(syncStmt, 0, json);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(syncStmt, "nftable_chain", &chain);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByIndex(chain, 1, &chain);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(chain, "nftable_rule", &rule);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(rule, "nftable_rule_key", &key_rule);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key_rule, 0, GMC_DATATYPE_UINT32, &(value = 35), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByKey(rule, key_rule, &rule);
    EXPECT_EQ(GMERR_OK, ret);

    // replace data
    ret = GmcNodeSetPropertyByName(rule, "nftable_rule_position", GMC_DATATYPE_UINT32, &(value = 11), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    // fetch data
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(syncStmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(eof);

    // get nodes again by index
    ret = GmcGetChildNode(syncStmt, "nftable_chain", &chain);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByIndex(chain, 1, &chain);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(chain, "nftable_rule", &rule);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByIndex(rule, 2, &rule);
    EXPECT_EQ(GMERR_OK, ret);

    // get property
    ret = GmcNodeGetPropertyByName(rule, "nftable_rule_position", &value, sizeof(value), &null);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(null);
    EXPECT_EQ(11u, value);

    // check element count before remove
    ret = GmcNodeGetElementCount(rule, &value);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);

    // remove by non existent key
    ret = GmcNodeAllocKey(rule, "nftable_rule_key", &key_rule);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key_rule, 0, GMC_DATATYPE_UINT32, &literal<uint32_t, 11>::value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeRemoveElementByKey(rule, key_rule);
    EXPECT_NE(GMERR_OK, ret);

    // no element removed
    ret = GmcNodeGetElementCount(rule, &value);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);

    // remove by correct key
    ret = GmcNodeSetKeyValue(key_rule, 0, GMC_DATATYPE_UINT32, &literal<uint32_t, 35>::value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeRemoveElementByKey(rule, key_rule);
    EXPECT_EQ(GMERR_OK, ret);

    // element removed
    ret = GmcNodeGetElementCount(rule, &value);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, value);

    // delete data
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_STRING, "test1", strlen("test1"));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, "access_control_list_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    // delete success
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(syncStmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_TRUE(eof);
}

TEST_F(StClientTree, testManipulateNoNestingDeltaVertex)
{
    const char *labelName = "access_list";
    const char *configJson = R"({"max_record_count":1000})";
    auto labelJson = R"([{
    "name":"access_list", "version":"2.0", "type":"record",
    "fields":[
        { "name":"nftable_table_name", "type":"string", "size":128 },
        { "name":"nftable_table_family", "type":"string", "size":128, "nullable": true },
        { "name":"nftable_table_flags", "type":"uint32", "nullable": true },
        { "name":"nftable_table_use", "type":"uint32", "nullable": true },
        { "type":"record", "name":"nftable_chain", "nullable": true,"vector":true, "size":20, "fields":[
            { "name":"nftable_chain_name", "type":"string", "size":128, "nullable": true},
            { "name":"nftable_chain_type", "type":"string", "size":128, "nullable": true },
            { "name":"nftable_chain_table", "type":"string", "size":128, "nullable": true },
            { "name":"nftable_chain_family", "type":"string", "size":128, "nullable": true },
            { "name":"nftable_chain_policy", "type":"uint32", "nullable": true },
            { "name":"nftable_chain_hooknum", "type":"uint32", "nullable": true },
            { "name":"nftable_chain_prio", "type":"uint32", "nullable": true },
            { "name":"nftable_chain_use", "type":"uint32", "nullable": true },
            { "name":"nftable_chain_handle", "type":"uint64", "nullable": true },
            { "name":"nftable_chain_basechainflag", "type":"uint8", "nullable": true },
            { "name":"nftable_chain_policyflag", "type":"uint8", "nullable": true },
            { "name":"nftable_chain_devflag", "type":"uint8", "nullable": true }
            ]
        } ],
        "keys":[
            { "name":"access_control_list_key", "node":"access_list", "fields":["nftable_table_name"],
                "index": { "type":"primary" }},
            { "name": "nftable_chain_key", "node": "nftable_chain", "fields": ["nftable_chain_name"],
                "index": { "type": "none" },"constraints": {"unique": true}}
            ]
        }])";

    GmcStmtT *stmt = syncStmt;
    int ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    auto jsonData = R"(
    [
    {
        "nftable_table_name": "test1",
        "nftable_table_family": "",
        "nftable_table_flags": 0,
        "nftable_table_use": 0,
        "nftable_chain": [
            {
                "nftable_chain_name": "chain1",
                "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;",
                "nftable_chain_table": "test1",
                "nftable_chain_family": "",
                "nftable_chain_policy": 0,
                "nftable_chain_hooknum": 0,
                "nftable_chain_prio": 0,
                "nftable_chain_use": 0,
                "nftable_chain_handle": "0",
                "nftable_chain_basechainflag": 0,
                "nftable_chain_policyflag": 0,
                "nftable_chain_devflag": 0
            },
            {
                "nftable_chain_name": "chain2",
                "nftable_chain_type": "type filter hook prerouting priority 20; policy accept;",
                "nftable_chain_table": "test2",
                "nftable_chain_family": "",
                "nftable_chain_policy": 0,
                "nftable_chain_hooknum": 0,
                "nftable_chain_prio": 0,
                "nftable_chain_use": 0,
                "nftable_chain_handle": "0",
                "nftable_chain_basechainflag": 0,
                "nftable_chain_policyflag": 0,
                "nftable_chain_devflag": 0
            }
        ]
    }
    ]
    )";
    json_error_t jsonError;
    json_t *arrayJson = json_loads(jsonData, JSON_REJECT_DUPLICATES, &jsonError);
    size_t arraySize = json_array_size(arrayJson);
    size_t i = 0;
    while (i < arraySize) {
        json_t *itemJson = json_array_get(arrayJson, i);
        char *jStr = json_dumps(itemJson, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(jStr);
        i++;
    }

    json_decref(arrayJson);

    // vector 增加 add，remove，根据索引update， 根据member key update
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    GmcNodeT *chainItemNode;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAppendElement(chainNode, &chainItemNode);
    EXPECT_EQ(GMERR_OK, ret);

    ret =
        GmcNodeSetPropertyByName(chainItemNode, "nftable_chain_name", GMC_DATATYPE_STRING, "chain4", strlen("chain4"));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(chainItemNode, "nftable_chain_table", GMC_DATATYPE_STRING, "test4", strlen("test4"));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetElementByIndex(chainNode, 0, &chainItemNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret =
        GmcNodeSetPropertyByName(chainItemNode, "nftable_chain_name", GMC_DATATYPE_STRING, "chain3", strlen("chain4"));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(chainItemNode, "nftable_chain_table", GMC_DATATYPE_STRING, "test3", strlen("test4"));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, "test1", strlen("test1"));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "access_control_list_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isFinish);
    if (!isFinish) {
        char buf[128] = {};
        bool isNull;
        GmcNodeT *rootNode;
        GmcNodeT *chainNode;
        ret = GmcGetRootNode(stmt, &rootNode);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(chainNode, "nftable_chain_name", &buf, sizeof(buf), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ("chain3", buf);
        ret = GmcNodeGetPropertyByName(chainNode, "nftable_chain_table", &buf, sizeof(buf), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ("test3", buf);
        GmcNodeT *nextEleNode;
        ret = GmcNodeGetNextElement(chainNode, &nextEleNode);
        EXPECT_EQ(GMERR_OK, ret);
        chainNode = nextEleNode;
        ret = GmcNodeGetNextElement(chainNode, &nextEleNode);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(nextEleNode, "nftable_chain_name", &buf, sizeof(buf), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ("chain4", buf);
        ret = GmcNodeGetPropertyByName(nextEleNode, "nftable_chain_table", &buf, sizeof(buf), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ("test4", buf);
    }

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StClientTree, testManipulateNestingVectorDeltaVertex)
{
    const char *labelName = "access_list";
    const char *configJson = R"({"max_record_count":1000})";
    auto labelJson = R"([{
    "name":"access_list", "version":"2.0", "type":"record",
    "fields":[
        { "name":"nftable_table_name", "type":"string", "size":128 },
        { "name":"nftable_table_family", "type":"string", "size":128, "nullable": true },
        { "name":"nftable_table_flags", "type":"uint32", "nullable": true },
        { "name":"nftable_table_use", "type":"uint32", "nullable": true },
        { "type":"record", "name":"nftable_chain", "nullable": true,"vector":true, "size":3, "fields":[
            { "name":"nftable_chain_name", "type":"string", "size":128, "nullable": true},
            { "name":"nftable_chain_type", "type":"string", "size":128, "nullable": true },
            { "name":"nftable_chain_table", "type":"string", "size":128, "nullable": true },
            { "name":"nftable_chain_family", "type":"string", "size":128, "nullable": true },
            { "name":"nftable_chain_policy", "type":"uint32", "nullable": true },
            { "name":"nftable_chain_hooknum", "type":"uint32", "nullable": true },
            { "name":"nftable_chain_prio", "type":"uint32", "nullable": true },
            { "name":"nftable_chain_use", "type":"uint32", "nullable": true },
            { "name":"nftable_chain_handle", "type":"uint64", "nullable": true },
            { "name":"nftable_chain_basechainflag", "type":"uint8", "nullable": true },
            { "name":"nftable_chain_policyflag", "type":"uint8", "nullable": true },
            { "name":"nftable_chain_devflag", "type":"uint8", "nullable": true },
            { "type":"record", "name":"nftable_rule", "nullable":true,"vector":true, "size":4, "fields":[
                { "name":"nftable_rule_handle", "type":"uint32", "nullable": true },
                { "name":"nftable_rule_position", "type":"uint32", "nullable": true },
                { "name":"nftable_rule_table", "type":"string", "size":128, "nullable": true },
                { "name":"nftable_rule_chain", "type":"string", "size":128, "nullable": true },
                { "name":"nftable_rule_description", "type":"string", "size":128, "nullable": true }
                ]
            },
            { "type":"record", "name":"nftable_rule1", "nullable":true, "fields":[
                { "name":"nftable_rule_handle", "type":"uint32", "nullable": true },
                { "name":"nftable_rule_position", "type":"uint32", "nullable": true },
                { "name":"nftable_rule_table", "type":"string", "size":128, "nullable": true },
                { "name":"nftable_rule_chain", "type":"string", "size":128 , "nullable": true},
                { "name":"nftable_rule_description", "type":"string", "size":128, "nullable": true }
                ]
            }
            ]
        },
        { "type":"record", "name":"nftable_rule2", "nullable":true, "fields":[
            { "name":"nftable_rule_handle", "type":"uint32", "nullable": true },
            { "name":"nftable_rule_position", "type":"uint32", "nullable": true },
            { "name":"nftable_rule_table", "type":"string", "size":128, "nullable": true },
            { "name":"nftable_rule_chain", "type":"string", "size":128, "nullable": true },
            { "name":"nftable_rule_description", "type":"string", "size":128 , "nullable": true}
            ]
        } ],
        "keys":[
            { "name":"access_control_list_key", "node":"access_list", "fields":["nftable_table_name"],
                "index": { "type":"primary" }},
            { "name": "nftable_chain_key", "node": "nftable_chain", "fields": ["nftable_chain_name"],
                "index": { "type": "none" },"constraints": {"unique": true}},
            { "name": "nftable_rule_key", "node": "nftable_chain/nftable_rule",  "fields": ["nftable_rule_handle"],
                "index": { "type": "none" },"constraints": {"unique": false}}
            ]
        }])";

    GmcStmtT *stmt = syncStmt;

    int ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    auto jsonData = R"(
    [
    {
        "nftable_table_name": "test1",
        "nftable_table_family": "",
        "nftable_table_flags": 0,
        "nftable_table_use": 0,
        "nftable_chain": [
            {
                "nftable_chain_name": "chain1",
                "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;",
                "nftable_chain_table": "test1",
                "nftable_chain_family": "",
                "nftable_chain_policy": 0,
                "nftable_chain_hooknum": 0,
                "nftable_chain_prio": 0,
                "nftable_chain_use": 0,
                "nftable_chain_handle": "0",
                "nftable_chain_basechainflag": 0,
                "nftable_chain_policyflag": 0,
                "nftable_chain_devflag": 0,
                "nftable_rule": [
                    {
                        "nftable_rule_handle": 33,
                        "nftable_rule_position": 0,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                    },
                    {
                        "nftable_rule_handle": 34,
                        "nftable_rule_position": 1,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                    },
                    {
                        "nftable_rule_handle": 35,
                        "nftable_rule_position": 2,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                    }
                ]
            },
            {
                "nftable_chain_name": "chain2",
                "nftable_chain_type": "type filter hook prerouting priority 20; policy accept;",
                "nftable_chain_table": "test1",
                "nftable_chain_family": "",
                "nftable_chain_policy": 0,
                "nftable_chain_hooknum": 0,
                "nftable_chain_prio": 0,
                "nftable_chain_use": 0,
                "nftable_chain_handle": "0",
                "nftable_chain_basechainflag": 0,
                "nftable_chain_policyflag": 0,
                "nftable_chain_devflag": 0,
                "nftable_rule": [
                    {
                        "nftable_rule_handle": 33,
                        "nftable_rule_position": 0,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                    },
                    {
                        "nftable_rule_handle": 34,
                        "nftable_rule_position": 1,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                    },
                    {
                        "nftable_rule_handle": 35,
                        "nftable_rule_position": 35,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                    }
                ]
            },
            {
                "nftable_chain_name": "chain3",
                "nftable_chain_type": "type filter hook prerouting priority 20; policy accept;",
                "nftable_chain_table": "test1",
                "nftable_chain_family": "",
                "nftable_chain_policy": 0,
                "nftable_chain_hooknum": 0,
                "nftable_chain_prio": 0,
                "nftable_chain_use": 0,
                "nftable_chain_handle": "0",
                "nftable_chain_basechainflag": 0,
                "nftable_chain_policyflag": 0,
                "nftable_chain_devflag": 0,
                "nftable_rule": [
                    {
                        "nftable_rule_handle": 33,
                        "nftable_rule_position": 0,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                    },
                    {
                        "nftable_rule_handle": 34,
                        "nftable_rule_position": 1,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                    },
                    {
                        "nftable_rule_handle": 35,
                        "nftable_rule_position": 35,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                    }
                ],
                "nftable_rule1":
                 {
                        "nftable_rule_handle": 33,
                        "nftable_rule_position": 0,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                 }
            }
        ],
        "nftable_rule2":
         {
                "nftable_rule_handle": 33,
                "nftable_rule_position": 0,
                "nftable_rule_table": "test3",
                "nftable_rule_chain": "chain30",
                "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
         }
    }
    ]
    )";
    json_error_t jsonError;
    json_t *arrayJson = json_loads(jsonData, JSON_REJECT_DUPLICATES, &jsonError);
    size_t arraySize = json_array_size(arrayJson);
    size_t i = 0;
    while (i < arraySize) {
        json_t *itemJson = json_array_get(arrayJson, i);
        char *jStr = json_dumps(itemJson, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(jStr);
        i++;
    }

    json_decref(arrayJson);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    GmcNodeT *ruleNode;
    GmcNodeT *ruleItem;

    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(chainNode, "nftable_rule", &ruleNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAppendElement(ruleNode, &ruleItem);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(ruleItem, "nftable_rule_table", GMC_DATATYPE_STRING, "test5", strlen("test5"));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByIndex(ruleNode, 1, &ruleItem);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(ruleItem, "nftable_rule_table", GMC_DATATYPE_STRING, "test6", strlen("test6"));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, "test1", strlen("test1"));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "access_control_list_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isFinish);

    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(chainNode, "nftable_rule", &ruleNode);
    EXPECT_EQ(GMERR_OK, ret);
    if (!isFinish) {
        char buf[128] = {};
        bool isNull;
        ret = GmcNodeGetElementByIndex(ruleNode, 1, &ruleItem);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(ruleItem, "nftable_rule_table", &buf, sizeof(buf), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ("test6", buf);
        ret = GmcNodeGetElementByIndex(ruleNode, 3, &ruleItem);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(ruleItem, "nftable_rule_table", &buf, sizeof(buf), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ("test5", buf);

        uint32_t nodeSize;
        ret = GmcNodeGetElementCount(ruleNode, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4u, nodeSize);
        GmcNodeT *rule1Node;
        ret = GmcNodeGetChild(chainNode, "nftable_rule1", &rule1Node);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StClientTree, testManipulateNestingArrayDeltaVertex)
{
    const char *labelName = "access_list";
    const char *configJson = R"({"max_record_count":1000})";
    auto labelJson = R"([{
    "name":"access_list", "version":"2.0", "type":"record",
    "fields":[
        { "name":"nftable_table_name", "type":"string", "size":128 },
        { "name":"nftable_table_family", "type":"string", "size":128, "nullable": true },
        { "name":"nftable_table_flags", "type":"uint32", "nullable": true },
        { "name":"nftable_table_use", "type":"uint32", "nullable": true },
        { "type":"record", "name":"nftable_chain", "nullable": true,"fixed_array":true, "size":1, "fields":[
            { "name":"nftable_chain_name", "type":"string", "size":128, "nullable": true},
            { "name":"nftable_chain_type", "type":"string", "size":128, "nullable": true },
            { "name":"nftable_chain_table", "type":"string", "size":128, "nullable": true },
            { "name":"nftable_chain_family", "type":"string", "size":128, "nullable": true },
            { "name":"nftable_chain_policy", "type":"uint32", "nullable": true },
            { "name":"nftable_chain_hooknum", "type":"uint32", "nullable": true },
            { "name":"nftable_chain_prio", "type":"uint32", "nullable": true },
            { "name":"nftable_chain_use", "type":"uint32" , "nullable": true},
            { "name":"nftable_chain_handle", "type":"uint64", "nullable": true },
            { "name":"nftable_chain_basechainflag", "type":"uint8" , "nullable": true},
            { "name":"nftable_chain_policyflag", "type":"uint8", "nullable": true },
            { "name":"nftable_chain_devflag", "type":"uint8", "nullable": true },
            { "type":"record", "name":"nftable_rule", "nullable":true,"fixed_array":true, "size":4, "fields":[
                { "name":"nftable_rule_handle", "type":"uint32", "nullable": true },
                { "name":"nftable_rule_position", "type":"uint32", "nullable": true },
                { "name":"nftable_rule_table", "type":"string", "size":128, "nullable": true },
                { "name":"nftable_rule_chain", "type":"string", "size":128, "nullable": true },
                { "name":"nftable_rule_description", "type":"string", "size":128 , "nullable": true}
                ]
            },
            { "type":"record", "name":"nftable_rule1", "nullable":true, "fields":[
                { "name":"nftable_rule_handle", "type":"uint32", "nullable": true },
                { "name":"nftable_rule_position", "type":"uint32", "nullable": true },
                { "name":"nftable_rule_table", "type":"string", "size":128, "nullable": true },
                { "name":"nftable_rule_chain", "type":"string", "size":128 , "nullable": true},
                { "name":"nftable_rule_description", "type":"string", "size":128, "nullable": true }
                ]
            }
            ]
        },
        { "type":"record", "name":"nftable_rule2", "nullable":true, "fields":[
            { "name":"nftable_rule_handle", "type":"uint32", "nullable": true },
            { "name":"nftable_rule_position", "type":"uint32" , "nullable": true},
            { "name":"nftable_rule_table", "type":"string", "size":128 , "nullable": true},
            { "name":"nftable_rule_chain", "type":"string", "size":128 , "nullable": true},
            { "name":"nftable_rule_description", "type":"string", "size":128 , "nullable": true}
            ]
        } ],
        "keys":[
            { "name":"access_control_list_key", "node":"access_list", "fields":["nftable_table_name"],
                "index": { "type":"primary" }},
            { "name": "nftable_chain_key", "node": "nftable_chain", "fields": ["nftable_chain_name"],
                "index": { "type": "none" },"constraints": {"unique": true}},
            { "name": "nftable_rule_key", "node": "nftable_chain/nftable_rule",  "fields": ["nftable_rule_handle"],
                "index": { "type": "none" },"constraints": {"unique": true}}
            ]
        }])";

    GmcStmtT *stmt = syncStmt;

    int ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    auto jsonData = R"(
    [
    {
        "nftable_table_name": "test1",
        "nftable_table_family": "",
        "nftable_table_flags": 0,
        "nftable_table_use": 0,
        "nftable_chain": [
            {
                "nftable_chain_name": "chain1",
                "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;",
                "nftable_chain_table": "test1",
                "nftable_chain_family": "",
                "nftable_chain_policy": 0,
                "nftable_chain_hooknum": 0,
                "nftable_chain_prio": 0,
                "nftable_chain_use": 0,
                "nftable_chain_handle": "0",
                "nftable_chain_basechainflag": 0,
                "nftable_chain_policyflag": 0,
                "nftable_chain_devflag": 0,
                "nftable_rule": [
                    {
                        "nftable_rule_handle": 33,
                        "nftable_rule_position": 0,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                    },
                    {
                        "nftable_rule_handle": 34,
                        "nftable_rule_position": 1,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                    },
                    {
                        "nftable_rule_handle": 35,
                        "nftable_rule_position": 2,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                    }
                ],
                "nftable_rule1":
                 {
                        "nftable_rule_handle": 33,
                        "nftable_rule_position": 0,
                        "nftable_rule_table": "test3",
                        "nftable_rule_chain": "chain30",
                        "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
                 }
            }
        ],
        "nftable_rule2":
         {
                "nftable_rule_handle": 33,
                "nftable_rule_position": 0,
                "nftable_rule_table": "test3",
                "nftable_rule_chain": "chain30",
                "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"
         }
    }
    ]
    )";
    json_error_t jsonError;
    json_t *arrayJson = json_loads(jsonData, JSON_REJECT_DUPLICATES, &jsonError);
    size_t arraySize = json_array_size(arrayJson);
    size_t i = 0;
    while (i < arraySize) {
        json_t *itemJson = json_array_get(arrayJson, i);
        char *jStr = json_dumps(itemJson, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(jStr);
        i++;
    }

    json_decref(arrayJson);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    GmcNodeT *ruleNode;
    GmcNodeT *ruleItem;

    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(chainNode, "nftable_rule", &ruleNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByIndex(ruleNode, 3, &ruleItem);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(ruleItem, "nftable_rule_table", GMC_DATATYPE_STRING, "test5", strlen("test5"));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByIndex(ruleNode, 1, &ruleItem);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(ruleItem, "nftable_rule_table", GMC_DATATYPE_STRING, "test6", strlen("test6"));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, "test1", strlen("test1"));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "access_control_list_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isFinish);

    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(chainNode, "nftable_rule", &ruleNode);
    EXPECT_EQ(GMERR_OK, ret);
    if (!isFinish) {
        char buf[128] = {};
        bool isNull;
        ret = GmcNodeGetElementByIndex(ruleNode, 1, &ruleItem);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(ruleItem, "nftable_rule_table", &buf, sizeof(buf), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ("test6", buf);
        ret = GmcNodeGetElementByIndex(ruleNode, 3, &ruleItem);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(ruleItem, "nftable_rule_table", &buf, sizeof(buf), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ("test5", buf);

        uint32_t nodeSize;
        ret = GmcNodeGetElementCount(ruleNode, &nodeSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4u, nodeSize);
        GmcNodeT *rule1Node;
        GmcNodeT *rule2Node;
        ret = GmcNodeGetChild(chainNode, "nftable_rule1", &rule1Node);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetPropertyByName(rule1Node, "nftable_rule_table", &buf, sizeof(buf), &isNull);
        EXPECT_FALSE(isNull);
        ret = GmcNodeGetChild(rootNode, "nftable_rule2", &rule2Node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(rule2Node, "nftable_rule_table", &buf, sizeof(buf), &isNull);
        EXPECT_FALSE(isNull);
    }

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

static void InsertData(GmcStmtT *stmt, const char *labelName)
{
    auto ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    auto table_name = "huawei";
    uint32_t table_flag = 1;
    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(rootNode, "nftable_table_name", GMC_DATATYPE_STRING, table_name, strlen(table_name));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(rootNode, "nftable_table_flags", GMC_DATATYPE_UINT32, &table_flag, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        GmcNodeT *chainEleNode;
        ret = GmcNodeAppendElement(chainNode, &chainEleNode);
        EXPECT_EQ(GMERR_OK, ret);
        const char *chain_name = "chain";
        uint64_t temp = (uint64_t)i;
        ret = GmcNodeSetPropertyByName(
            chainEleNode, "nftable_chain_name", GMC_DATATYPE_STRING, chain_name, strlen(chain_name));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(
            chainEleNode, "nftable_chain_handle", GMC_DATATYPE_UINT64, &temp, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *ruleNode;
        GmcNodeT *ruleEleNode;
        ret = GmcNodeGetChild(chainEleNode, "nftable_rule", &ruleNode);
        for (int j = 0; j < 1024; j++) {
            const char *rule_name = "rule";
            uint64_t temp2 = (uint64_t)j;
            ret = GmcNodeAppendElement(ruleNode, &ruleEleNode);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(
                ruleEleNode, "nftable_rule_handle", GMC_DATATYPE_UINT64, &temp2, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(
                ruleEleNode, "nftable_rule_chain", GMC_DATATYPE_STRING, rule_name, strlen(rule_name));
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    // 插入
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

static void UpdateData1(GmcStmtT *stmt, const char *labelName)
{
    auto ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    ret = GmcGetRootNode(stmt, &rootNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeClear(chainNode);
    ASSERT_EQ(GMERR_OK, ret);
    // clear 之后append 第一条元素
    GmcNodeT *chainEleNode;
    ret = GmcNodeAppendElement(chainNode, &chainEleNode);
    ASSERT_EQ(GMERR_OK, ret);
    const char *chain_name = "chain";
    uint64_t temp = 10;
    ret = GmcNodeSetPropertyByName(
        chainEleNode, "nftable_chain_name", GMC_DATATYPE_STRING, chain_name, strlen(chain_name));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(chainEleNode, "nftable_chain_handle", GMC_DATATYPE_UINT64, &temp, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    // clear 之后append第二条元素
    GmcNodeT *chainEleNode2;
    ret = GmcNodeAppendElement(chainNode, &chainEleNode2);
    ASSERT_EQ(GMERR_OK, ret);
    const char *chain_name2 = "chain2";
    uint64_t temp2 = 189;
    ret = GmcNodeSetPropertyByName(
        chainEleNode2, "nftable_chain_name", GMC_DATATYPE_STRING, chain_name2, strlen(chain_name2));
    EXPECT_EQ(GMERR_OK, ret);
    ret =
        GmcNodeSetPropertyByName(chainEleNode2, "nftable_chain_handle", GMC_DATATYPE_UINT64, &temp2, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 更新
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

static void CheckData(GmcStmtT *stmt, const char *labelName)
{
    auto ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取Vertex
    const char *keyValue = "huawei";
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, keyValue, strlen(keyValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "access_control_list_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    uint64_t chain_handle;
    bool isNull;
    char buf[128] = {};
    GmcNodeT *chainEleNode;

    // case: get vector size
    uint32_t size;
    ret = GmcNodeGetElementCount(chainNode, &size);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, size);

    // case: get vector item by index
    ret = GmcNodeGetElementByIndex(chainNode, 0, &chainEleNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(chainEleNode, "nftable_chain_handle", &chain_handle, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, chain_handle);
    ret = GmcNodeGetPropertyByName(chainEleNode, "nftable_chain_name", buf, sizeof(buf), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ASSERT_STREQ("chain", buf);
    ret = GmcNodeGetPropertySizeByName(chainEleNode, "nftable_chain_name", &size);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(6u, size);

    // case: get vector item by member key
    GmcIndexKeyT *keyHandle;
    ret = GmcNodeAllocKey(chainNode, "nftable_chain_key", &keyHandle);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t temp = 1;
    ret = GmcNodeSetKeyValue(keyHandle, 0, GMC_DATATYPE_UINT64, &temp, sizeof(temp));
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *chainEleNode2;
    ret = GmcNodeGetElementByKey(chainNode, keyHandle, &chainEleNode2);
    EXPECT_EQ(GMERR_OK, ret);

    // case: get next vector item
    GmcNodeT *nextEleNode;
    ret = GmcNodeGetNextElement(chainEleNode2, &nextEleNode);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetPropertyByName(nextEleNode, "nftable_chain_handle", &chain_handle, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, chain_handle);

    // case: get child node
    GmcNodeT *ruleNode;
    GmcNodeT *ruleEleNode;
    ret = GmcNodeGetChild(nextEleNode, "nftable_rule", &ruleNode);

    ret = GmcNodeGetElementByIndex(ruleNode, 3, &ruleEleNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(ruleEleNode, "nftable_rule_handle", &chain_handle, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, chain_handle);

    // case: remove item from vector
    ret = GmcNodeRemoveElementByIndex(ruleNode, 3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementCount(ruleNode, &size);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1023u, size);
}

TEST_F(StClientTree, testTreeModelNodeFullVertex)
{
    const char *configJson = R"({"max_record_count":1000})";
    const char *labelName = "accessList";
    GmcStmtT *stmt = syncStmt;

    int ret = GmcCreateVertexLabel(stmt, accessListSchema, configJson);
    ASSERT_EQ(GMERR_OK, ret);

    InsertData(stmt, labelName);

    CheckData(stmt, labelName);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

static void CheckDataWithClearOp(GmcStmtT *stmt, const char *labelName)
{
    auto ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取Vertex
    const char *keyValue = "huawei";
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, keyValue, strlen(keyValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "access_control_list_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t chain_handle;
    bool isNull;
    char buf[128] = {};
    GmcNodeT *chainEleNode;
    // case: get vector size
    uint32_t size;
    ret = GmcNodeGetElementCount(chainNode, &size);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, size);  // clear之后append了2条元素

    // 获取第二个元素
    ret = GmcNodeGetElementByIndex(chainNode, 1, &chainEleNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(chainEleNode, "nftable_chain_handle", &chain_handle, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(189u, chain_handle);
    ret = GmcNodeGetPropertyByName(chainEleNode, "nftable_chain_name", buf, sizeof(buf), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ASSERT_STREQ("chain2", buf);

    // case: get vector item by member key
    ret = GmcNodeGetElementByIndex(chainNode, 0, &chainEleNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(chainEleNode, "nftable_chain_handle", &chain_handle, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(10u, chain_handle);
    ret = GmcNodeGetPropertyByName(chainEleNode, "nftable_chain_name", buf, sizeof(buf), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ASSERT_STREQ("chain", buf);
}

TEST_F(StClientTree, testVectorUpdateAppend1)
{
    const char *configJson = R"({"max_record_count":1000})";
    const char *labelName = "accessListop";
    GmcStmtT *stmt = syncStmt;

    int ret = GmcCreateVertexLabel(stmt, accessListSchemaop, configJson);
    ASSERT_EQ(GMERR_OK, ret);

    InsertData(stmt, labelName);

    UpdateData1(stmt, labelName);

    CheckDataWithClearOp(stmt, labelName);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

static void testClearNode(GmcStmtT *stmt, const char *labelName)
{
    auto ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    ret = GmcGetRootNode(stmt, &rootNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeClear(chainNode);
    ASSERT_EQ(GMERR_OK, ret);
    // 更新
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// clear node，再scan
TEST_F(StClientTree, testVectorUpdateAppend2)
{
    const char *configJson = R"({"max_record_count":1000})";
    const char *labelName = "accessListop";
    GmcStmtT *stmt = syncStmt;
    int ret = GmcCreateVertexLabel(stmt, accessListSchemaop, configJson);
    ASSERT_EQ(GMERR_OK, ret);
    InsertData(stmt, labelName);

    testClearNode(stmt, labelName);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取Vertex
    const char *keyValue = "huawei";
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, keyValue, strlen(keyValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "access_control_list_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetNodeWithKeyBuf(rootNode, "nftable_chain", 0, NULL, &chainNode);
    EXPECT_EQ(GMERR_NO_DATA, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

static void UpdateData3(GmcStmtT *stmt, const char *labelName)
{
    auto ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *rootNode;
    GmcNodeT *chainNode;

    // 测试点1：node nftable_rule先clear再写满1024
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(stmt, &rootNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);

    GmcNodeT *chainEleNode;
    GmcNodeT *ruleNode;
    GmcNodeT *ruleEleNode;
    for (int i = 0; i < 3; i++) {
        ret = GmcNodeGetElementByIndex(chainNode, i, &chainEleNode);
        ASSERT_EQ(GMERR_OK, ret);

        // 更新 nftable_rule 节点
        ret = GmcNodeGetChild(chainEleNode, "nftable_rule", &ruleNode);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(ruleNode);
        ASSERT_EQ(GMERR_OK, ret);
        for (int j = 0; j < 1024; j++) {
            const char *rule_name = "rule";
            uint64_t temp2 = (uint64_t)j;
            ret = GmcNodeAppendElement(ruleNode, &ruleEleNode);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(
                ruleEleNode, "nftable_rule_handle", GMC_DATATYPE_UINT64, &temp2, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(
                ruleEleNode, "nftable_rule_chain", GMC_DATATYPE_STRING, rule_name, strlen(rule_name));
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    // 更新
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 测试点2：node nftable_rule逐个remove再append写满
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(stmt, &rootNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 3; i++) {
        ret = GmcNodeGetElementByIndex(chainNode, i, &chainEleNode);
        ASSERT_EQ(GMERR_OK, ret);

        // 更新 nftable_rule 节点
        ret = GmcNodeGetChild(chainEleNode, "nftable_rule", &ruleNode);
        ASSERT_EQ(GMERR_OK, ret);
        for (int j = 0; j < 1024; j++) {
            ret = GmcNodeRemoveElementByIndex(ruleNode, j);
            ASSERT_EQ(GMERR_OK, ret);
            const char *rule_name = "rule";
            uint64_t temp2 = (uint64_t)j;
            ret = GmcNodeAppendElement(ruleNode, &ruleEleNode);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(
                ruleEleNode, "nftable_rule_handle", GMC_DATATYPE_UINT64, &temp2, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(
                ruleEleNode, "nftable_rule_chain", GMC_DATATYPE_STRING, rule_name, strlen(rule_name));
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StClientTree, testVectorUpdateAppend3)
{
    const char *configJson = R"({"max_record_count":1000})";
    const char *labelName = "accessListop";
    GmcStmtT *stmt = syncStmt;
    int ret = GmcCreateVertexLabel(stmt, accessListSchemaop, configJson);
    ASSERT_EQ(GMERR_OK, ret);
    InsertData(stmt, labelName);

    UpdateData3(stmt, labelName);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

static void UpdateData4(GmcStmtT *stmt, const char *labelName)
{
    auto ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    ret = GmcGetRootNode(stmt, &rootNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);

    GmcIndexKeyT *keyHandle;
    ret = GmcNodeAllocKey(chainNode, "nftable_chain_key", &keyHandle);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 3; i++) {
        uint64_t temp = i;
        ret = GmcNodeSetKeyValue(keyHandle, 0, GMC_DATATYPE_UINT64, &temp, sizeof(temp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByKey(chainNode, keyHandle);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 更新
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 根据memberkey进行update
TEST_F(StClientTree, testVectorUpdateAppend4)
{
    const char *configJson = R"({"max_record_count":1000})";
    const char *labelName = "accessListop";
    GmcStmtT *stmt = syncStmt;
    int ret = GmcCreateVertexLabel(stmt, accessListSchemaop, configJson);
    ASSERT_EQ(GMERR_OK, ret);

    InsertData(stmt, labelName);

    UpdateData4(stmt, labelName);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

static void UpdateData5(GmcStmtT *stmt, const char *labelName)
{
    auto ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    ret = GmcGetRootNode(stmt, &rootNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);

    GmcIndexKeyT *keyHandle;
    ret = GmcNodeAllocKey(chainNode, "nftable_chain_key", &keyHandle);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 1; i < 5; i++) {
        uint64_t temp = i;
        ret = GmcNodeSetKeyValue(keyHandle, 0, GMC_DATATYPE_UINT64, &temp, sizeof(temp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByKey(chainNode, keyHandle);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 测试点1：通过不存在的memberkey进行remove
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 测试点2：不存在的index
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(stmt, &rootNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeRemoveElementByIndex(chainNode, 5);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);

    // 测试点3：append元素超过schema上界
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *chainElementNode;
    ret = GmcGetRootNode(stmt, &rootNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);

    // 已有3个element，再append三个，超过5的上界
    for (int i = 0; i < 3; i++) {
        ret = GmcNodeAppendElement(chainNode, &chainElementNode);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 异常场景测试
TEST_F(StClientTree, testVectorUpdateAppend5)
{
    const char *configJson = R"({"max_record_count":1000})";
    const char *labelName = "accessListop";
    GmcStmtT *stmt = syncStmt;
    int ret = GmcCreateVertexLabel(stmt, accessListSchemaop, configJson);
    ASSERT_EQ(GMERR_OK, ret);

    InsertData(stmt, labelName);

    UpdateData5(stmt, labelName);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StClientTree, testVectorUpdateAppend6)
{
    const char *configJson = R"({"max_record_count":1000})";
    const char *labelName = "accessListop";
    GmcStmtT *stmt = syncStmt;
    int ret = GmcCreateVertexLabel(stmt, accessListSchemaop1, configJson);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    auto table_name = "huawei";
    uint32_t table_flag = 1;
    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    GmcNodeT *chainEleNode;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(rootNode, "nftable_table_name", GMC_DATATYPE_STRING, table_name, strlen(table_name));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(rootNode, "nftable_table_flags", GMC_DATATYPE_UINT32, &table_flag, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1; i++) {
        ret = GmcNodeAppendElement(chainNode, &chainEleNode);
        EXPECT_EQ(GMERR_OK, ret);
        const char *chain_name = "chain";
        uint64_t temp = (uint64_t)i;
        ret = GmcNodeSetPropertyByName(
            chainEleNode, "nftable_chain_name", GMC_DATATYPE_STRING, chain_name, strlen(chain_name));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(
            chainEleNode, "nftable_chain_handle", GMC_DATATYPE_UINT64, &temp, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(chainEleNode, "F1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 插入
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(stmt, &rootNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t temp;
    for (int i = 0; i < 20; i++) {
        temp = (uint64_t)i + 10;
        ret = GmcNodeAppendElement(chainNode, &chainEleNode);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(
            chainEleNode, "nftable_chain_handle", GMC_DATATYPE_UINT64, &temp, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcNodeGetElementByIndex(chainNode, 0, &chainEleNode);
    EXPECT_EQ(GMERR_OK, ret);
    temp = 66;
    ret = GmcNodeSetPropertyByName(chainEleNode, "nftable_chain_handle", GMC_DATATYPE_UINT64, &temp, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 更新
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // scan
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取Vertex
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(eof, false);

    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    EXPECT_EQ(GMERR_OK, ret);

    bool isNull;
    // case: get vector size
    uint32_t size;
    ret = GmcNodeGetElementCount(chainNode, &size);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(21u, size);  // clear之后append了2条元素

    // 获取第二个元素
    uint64_t chain_handle;
    uint32_t f1;
    uint64_t chain_handle_value = 66;
    uint32_t f1_value = 0;
    for (uint32_t i = 0; i < size; i++) {
        ret = GmcNodeGetElementByIndex(chainNode, i, &chainEleNode);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(chainEleNode, "nftable_chain_handle", &chain_handle, sizeof(uint64_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(chain_handle_value, chain_handle);
        ret = GmcNodeGetPropertyByName(chainEleNode, "F1", &f1, sizeof(uint64_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(f1_value, f1);
        chain_handle_value = (uint64_t)(10 + i);
        f1_value = 7777;
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StClientTree, testVectorUpdateAppend7)
{
    const char *configJson = R"({"max_record_count":1000})";
    const char *labelName = "vectorTree";
    GmcStmtT *stmt = syncStmt;
    int ret = GmcCreateVertexLabel(stmt, vectorTreeSchema, configJson);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t table_flag = 1;
    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    GmcNodeT *element;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(rootNode, "c0", GMC_DATATYPE_UINT32, &table_flag, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetChild(rootNode, "c1", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeAppendElement(chainNode, &element);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t temp = 1;
    ret = GmcNodeSetPropertyByName(element, "b1", GMC_DATATYPE_UINT32, &temp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(element, "b2", GMC_DATATYPE_UINT32, &temp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 插入
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "table_pk");
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(stmt, &rootNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "c1", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);
    // 测试点：append一个空的element，里面包含必填字段
    ret = GmcNodeAppendElement(chainNode, &element);
    EXPECT_EQ(GMERR_OK, ret);
    // 更新
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

static void CheckData2(GmcStmtT *stmt, const char *labelName)
{
    auto ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取Vertex
    const char *keyValue = "huawei";
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, keyValue, strlen(keyValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "access_control_list_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t *vertexBuf = NULL;
    uint32_t vertexBufLen;
    uint8_t *nullInfoBuf = NULL;
    uint32_t nullInfoBufLen;

    // 测试点1, GmcVertexGetDataBuf获取第一层buff
    ret = GmcVertexGetDataBuf(stmt, &vertexBuf, &vertexBufLen, &nullInfoBuf, &nullInfoBufLen);
    EXPECT_EQ(GMERR_OK, ret);
    printf("vertexBuf is %p, vertexBufLen is %d, nullInfoBuf is %p, nullInfoBufLen is %d\n", vertexBuf, vertexBufLen,
        nullInfoBuf, nullInfoBufLen);
    EXPECT_EQ(true, *(bool *)vertexBuf);
    EXPECT_EQ(4u, nullInfoBufLen);
    EXPECT_EQ(1u, *nullInfoBuf);
    EXPECT_EQ(1u, *(nullInfoBuf + 1));
    EXPECT_EQ(0u, *(nullInfoBuf + 2));
    EXPECT_EQ(0u, *(nullInfoBuf + 3));

    // 测试点2, GmcNodeGetNodeDataBuf获取第一层buff
    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t *nodeBuf = NULL;
    uint32_t nodeBufLen;
    uint8_t *nodeNullInfoBuf = NULL;
    uint32_t nodeNullInfoBufLen;
    ret = GmcNodeGetNodeDataBuf(rootNode, &nodeBuf, &nodeBufLen, &nodeNullInfoBuf, &nodeNullInfoBufLen);
    EXPECT_EQ(GMERR_OK, ret);
    printf("nodeBuf is %p, nodeBufLen is %d, nodeNullInfoBuf is %p, nodeNullInfoBufLen is %d\n", nodeBuf, nodeBufLen,
        nodeNullInfoBuf, nodeNullInfoBufLen);
    EXPECT_EQ(true, *(bool *)nodeBuf);
    EXPECT_EQ(4u, nodeNullInfoBufLen);
    EXPECT_EQ(1u, *nodeNullInfoBuf);
    EXPECT_EQ(1u, *(nodeNullInfoBuf + 1));
    EXPECT_EQ(0u, *(nodeNullInfoBuf + 2));
    EXPECT_EQ(0u, *(nodeNullInfoBuf + 3));

    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    uint64_t chain_handle;
    GmcNodeT *chainEleNode;

    // case: get vector size
    uint32_t size;
    ret = GmcNodeGetElementCount(chainNode, &size);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, size);

    // case: get vector item by index
    bool isNull;
    ret = GmcNodeGetElementByIndex(chainNode, 0, &chainEleNode);
    EXPECT_EQ(GMERR_OK, ret);

    // 测试点3, GmcNodeGetNodeDataBuf获取第二层buff
    ret = GmcNodeGetNodeDataBuf(chainEleNode, &nodeBuf, &nodeBufLen, &nodeNullInfoBuf, &nodeNullInfoBufLen);
    printf("nodeBuf is %p, nodeBufLen is %d, nodeNullInfoBuf is %p, nodeNullInfoBufLen is %d\n", nodeBuf, nodeBufLen,
        nodeNullInfoBuf, nodeNullInfoBufLen);
    EXPECT_EQ(3u, nodeNullInfoBufLen);

    // case: get vector item by member key
    GmcIndexKeyT *keyHandle;
    ret = GmcNodeAllocKey(chainNode, "nftable_chain_key", &keyHandle);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t temp = 1;
    ret = GmcNodeSetKeyValue(keyHandle, 0, GMC_DATATYPE_UINT64, &temp, sizeof(temp));
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *chainEleNode2;
    ret = GmcNodeGetElementByKey(chainNode, keyHandle, &chainEleNode2);
    EXPECT_EQ(GMERR_OK, ret);

    // case: get next vector item
    GmcNodeT *nextEleNode;
    ret = GmcNodeGetNextElement(chainEleNode2, &nextEleNode);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetPropertyByName(nextEleNode, "nftable_chain_handle", &chain_handle, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, chain_handle);

    // case: get child node
    GmcNodeT *ruleNode;
    GmcNodeT *ruleEleNode;
    ret = GmcNodeGetChild(nextEleNode, "nftable_rule", &ruleNode);

    ret = GmcNodeGetElementByIndex(ruleNode, 3, &ruleEleNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(ruleEleNode, "nftable_rule_handle", &chain_handle, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, chain_handle);

    // case: remove item from vector
    ret = GmcNodeRemoveElementByIndex(ruleNode, 3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementCount(ruleNode, &size);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1023u, size);
}

void ConvertVarintToUint32(uint32_t input, uint32_t *output, uint32_t *byteNum)
{
    uint32_t varintType = (input & 0x000000C0) >> 6;  // 小端在存储时是低到高，但是在运算时依然是高到低，故右移6位即可
    if (varintType == 0) {
        *output = input & 0x0000003F;
    } else if (varintType == 1) {  // 移动运算与大小端无关，左移是往高位移动，右移是往低位移动
        // 不能优化((input & 0x0000FF00) >> 8) -> (input >> 8)，因为高两个字节序列化了数据
        *output = (((input & 0x000000FF) << 8) | ((input & 0x0000FF00) >> 8)) & 0x00003FFF;
    } else if (varintType == 2) {  // type为2，表示占用3字节
        // 不能优化((input & 0x00FF0000) >> 16) -> (input >> 16)，因为高一个字节序列化了数据
        *output = (((input & 0x000000FF) << 16) | (input & 0x0000FF00) | ((input & 0x00FF0000) >> 16)) & 0x003FFFFF;
    } else {  // (varintType == 3)
        // 24: 将4字节的前后字节序逆转
        *output = ((input << 24) | (input & 0x00FFFF00) | (input >> 24)) & 0x3FFFFFFF;
    }
    *byteNum = varintType + 1;
}

static void RecordBufCheck(uint8_t *vertexBuf, uint32_t vertexBufLen, uint32_t realFixLen)
{
    // 读recordLen
    uint32_t varintByteNum, recordLen, fixLen;
    ConvertVarintToUint32(*(uint32_t *)vertexBuf, &recordLen, &varintByteNum);
    EXPECT_EQ(recordLen, vertexBufLen);
    vertexBuf += varintByteNum;

    // 读fixLen
    ConvertVarintToUint32(*(uint32_t *)vertexBuf, &fixLen, &varintByteNum);
    EXPECT_EQ(fixLen, realFixLen);
}

static void CheckData3(GmcStmtT *stmt, const char *labelName)
{
    auto ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    bool useNewSeri = true;
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_USE_NEW_DESERI, &useNewSeri, sizeof(useNewSeri));
    EXPECT_EQ(GMERR_OK, ret);

    // 获取Vertex
    const char *keyValue = "huawei";
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, keyValue, strlen(keyValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "access_control_list_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t *vertexBuf = NULL;
    uint32_t vertexBufLen;

    // 测试点1, GmcVertexGetDataBuf获取第一层buff
    ret = GmcVertexGetRecordBuf(stmt, &vertexBuf, &vertexBufLen);
    EXPECT_EQ(GMERR_OK, ret);
    RecordBufCheck(vertexBuf, vertexBufLen, 5);

    // 测试点2, GmcNodeGetNodeDataBuf获取第一层buff
    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t *nodeBuf = NULL;
    uint32_t nodeBufLen;
    ret = GmcNodeGetNodeRecordBuf(rootNode, &nodeBuf, &nodeBufLen);
    EXPECT_EQ(GMERR_OK, ret);
    RecordBufCheck(nodeBuf, nodeBufLen, 5);

    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    GmcNodeT *chainEleNode;

    // case: get vector size
    uint32_t size;
    ret = GmcNodeGetElementCount(chainNode, &size);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, size);

    // case: get vector item by index
    ret = GmcNodeGetElementByIndex(chainNode, 0, &chainEleNode);
    EXPECT_EQ(GMERR_OK, ret);

    // 测试点3, GmcNodeGetNodeDataBuf获取第二层buff
    ret = GmcNodeGetNodeRecordBuf(chainEleNode, &nodeBuf, &nodeBufLen);
    EXPECT_EQ(GMERR_OK, ret);
    RecordBufCheck(nodeBuf, nodeBufLen, 8);
}
// 测试GmcVertexGetDataBuf和GmcNodeGetNodeDataBuf接口
TEST_F(StClientTree, testTreeModelNodeFullVertexWithBuf)
{
    const char *configJson = R"({"max_record_count":1000})";
    const char *labelName = "accessList";
    GmcStmtT *stmt = syncStmt;

    int ret = GmcCreateVertexLabel(stmt, accessListSchema, configJson);
    ASSERT_EQ(GMERR_OK, ret);

    InsertData(stmt, labelName);

    CheckData2(stmt, labelName);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

const char *treeSchema2 = (char *)R"([{
    "type": "record",
    "name": "treeSchema2",
    "schema_version": 1,
    "fields":[
        {"name": "F0", "type": "uint32", "nullable":false},
        {"name": "F1", "type": "uint32", "nullable":true},
        {"name": "F2", "type": "uint64", "nullable":true},
        { "type":"record", "name":"new","vector":true, "size":4, "nullable": true, 
        "fields":[
            { "name":"t0", "type":"uint32", "nullable":true },
            { "name":"t1", "type":"string", "size":128, "nullable":true}
            ]
        }
    ],

    "keys":[
       {
            "node": "treeSchema2",
            "name": "primary_key",
            "fields":["F0"],
            "index":{"type": "primary"},
            "constraints":{"unique":true}
        }
    ]
}])";

static void WriteRunningBuf(uint8_t *runningBuf, uint32_t i, uint8_t *nullInfoBuf, uint32_t nullInfoBufLen)
{
    uint8_t *runningBufCursor = runningBuf;
    *(uint32_t *)runningBufCursor = i;  // F0
    runningBufCursor += sizeof(uint32_t);
    *(uint32_t *)runningBufCursor = i;  // F1
    runningBufCursor += sizeof(uint32_t);
    *(uint64_t *)runningBufCursor = i;  // F2
    runningBufCursor += sizeof(uint64_t);
    // 将nullInfo设为1
    (void)memset_s(nullInfoBuf, nullInfoBufLen, 1, 3);
}

static void InsertWithGetDataBuf(GmcStmtT *stmt, const char *labelName)
{
    auto ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t *runningBuf = NULL;
    uint32_t runningBufLen;
    uint8_t *nullInfoBuf = NULL;
    uint32_t nullInfoBufLen;

    // 测试点1, GmcVertexGetDataBuf获取第一层runningBuf
    ret = GmcVertexGetDataBuf(stmt, &runningBuf, &runningBufLen, &nullInfoBuf, &nullInfoBufLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(runningBufLen, 17u);
    EXPECT_EQ(nullInfoBufLen, 5u);

    // 写数据
    WriteRunningBuf(runningBuf, 1, nullInfoBuf, nullInfoBufLen);

    // 测试点2, GmcNodeGetNodeDataBuf获取node节点对应的runningBuf
    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "new", &chainNode);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *elementNode;

    for (uint32_t i = 0; i < 3; i++) {
        // 新增element
        ret = GmcNodeAppendElement(chainNode, &elementNode);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNodeDataBuf(elementNode, &runningBuf, &runningBufLen, &nullInfoBuf, &nullInfoBufLen);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(runningBufLen, 4u);
        EXPECT_EQ(nullInfoBufLen, 2u);
        *(uint32_t *)runningBuf = i;
        *nullInfoBuf = 1;
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

static void CheckDataForTreeSchema2(GmcStmtT *stmt, const char *labelName)
{
    auto ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取Vertex
    uint32_t key = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &key, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, eof);

    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验根节点的值
    uint32_t fValue = 1u;
    uint32_t fGetValue;
    bool isNull;
    ret = GmcNodeGetPropertyByName(rootNode, (char *)"F0", &fGetValue, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(fValue, fGetValue);

    ret = GmcNodeGetPropertyByName(rootNode, (char *)"F1", &fGetValue, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(fValue, fGetValue);

    uint64_t f3Value = 1u;
    uint64_t f3GetValue;
    ret = GmcNodeGetPropertyByName(rootNode, (char *)"F2", &f3GetValue, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f3Value, f3GetValue);

    // 校验node上的属性
    ret = GmcNodeGetChild(rootNode, "new", &chainNode);
    char buf[128] = {};
    GmcNodeT *chainEleNode;

    // case: get vector size
    uint32_t size;
    ret = GmcNodeGetElementCount(chainNode, &size);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, size);

    // case: get vector item by index
    for (uint32_t i = 0; i < 3; i++) {
        ret = GmcNodeGetElementByIndex(chainNode, i, &chainEleNode);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(chainEleNode, "t0", &fGetValue, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(false, isNull);
        EXPECT_EQ(fGetValue, i);

        ret = GmcNodeGetPropertyByName(chainEleNode, "t1", buf, sizeof(buf), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(true, isNull);
    }
}

// GmcVertexGetDataBuf，GmcNodeGetNodeDataBuf接口写数据
TEST_F(StClientTree, testInsertWithGetRecordBuf)
{
    const char *configJson = R"({"max_record_count":200000})";
    const char *labelName = "treeSchema2";
    GmcStmtT *stmt = syncStmt;

    int ret = GmcCreateVertexLabel(stmt, treeSchema2, configJson);
    ASSERT_EQ(GMERR_OK, ret);

    InsertWithGetDataBuf(stmt, labelName);

    CheckDataForTreeSchema2(stmt, labelName);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 测试GmcVertexGetRecordBuf和GmcNodeGetNodeRecordBuf接口
TEST_F(StClientTree, testTreeModelNodeGetNodeRrcordBuf)
{
    const char *configJson = R"({"max_record_count":1000})";
    const char *labelName = "accessList";
    GmcStmtT *stmt = syncStmt;

    int ret = GmcCreateVertexLabel(stmt, accessListSchema, configJson);
    ASSERT_EQ(GMERR_OK, ret);

    InsertData(stmt, labelName);

    CheckData3(stmt, labelName);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

void CheckGetVertexPropertyByName(GmcStmtT *stmt)
{
    // 测试点一：GmcSetStmtAttr设置新的序列化函数，调用GmcGetVertexPropertySizeByName和GmcGetVertexPropertyByName报错。
    // 设置使用新的序列化函数
    bool useNewSeri = true;
    auto ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_USE_NEW_DESERI, &useNewSeri, sizeof(useNewSeri));
    EXPECT_EQ(GMERR_OK, ret);

    // 通过vertex获取第一层数据的size,预期报错
    uint32_t size = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "nftable_table_name", &size);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 测试点1：设置使用老的序列化函数,预期报错，只支持设置为true.
    useNewSeri = false;
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_USE_NEW_DESERI, &useNewSeri, sizeof(useNewSeri));
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    // 设置使用新的序列化函数，预期报错
    useNewSeri = true;
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_USE_NEW_DESERI, &useNewSeri, sizeof(useNewSeri));
    EXPECT_EQ(GMERR_OK, ret);

    bool isNull;
    char name[size + 1];
    ret = GmcGetVertexPropertyByName(stmt, "nftable_table_name", &name, size, &isNull);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
}

void CheckGetVertexPropertyById(GmcStmtT *stmt)
{
    // 测试点一：GmcSetStmtAttr设置新的序列化函数，调用GmcGetVertexPropertySizeById和GmcGetVertexPropertyById报错。
    // 设置使用新的序列化函数
    bool useNewSeri = true;
    auto ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_USE_NEW_DESERI, &useNewSeri, sizeof(useNewSeri));
    EXPECT_EQ(GMERR_OK, ret);

    // 通过vertex获取第一层数据的size,预期报错
    uint32_t size = 0;
    ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 设置使用老的序列化函数
    useNewSeri = false;
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_USE_NEW_DESERI, &useNewSeri, sizeof(useNewSeri));
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 设置使用新的序列化函数，get预期报错
    useNewSeri = true;
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_USE_NEW_DESERI, &useNewSeri, sizeof(useNewSeri));
    EXPECT_EQ(GMERR_OK, ret);

    bool isNull;
    char name[size + 1];
    ret = GmcGetVertexPropertyById(stmt, 0, &name, size, &isNull);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
}

void CheckGetNodePropertyByNameAndId(GmcStmtT *stmt)
{
    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    auto ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    uint64_t chain_handle;
    bool isNull;
    char buf[128] = {};
    GmcNodeT *chainEleNode;

    // case: get vector size
    uint32_t size;
    ret = GmcNodeGetElementCount(chainNode, &size);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, size);

    // case: get vector item by index
    ret = GmcNodeGetElementByIndex(chainNode, 0, &chainEleNode);
    EXPECT_EQ(GMERR_OK, ret);

    // 测试点三：GmcSetStmtAttr设置新的序列化函数，调用GmcNodeGetPropertyByName和GmcNodeGetPropertyById报错。
    bool useNewSeri = true;
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_USE_NEW_DESERI, &useNewSeri, sizeof(useNewSeri));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(chainEleNode, "nftable_chain_handle", &chain_handle, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcNodeGetPropertySizeByName(chainEleNode, "nftable_chain_name", &size);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcNodeGetPropertyByName(chainEleNode, "nftable_chain_name", buf, sizeof(buf), &isNull);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // GmcSetStmtAttr设置老的序列化函数，预期报错
    useNewSeri = false;
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_USE_NEW_DESERI, &useNewSeri, sizeof(useNewSeri));
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    useNewSeri = true;
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_USE_NEW_DESERI, &useNewSeri, sizeof(useNewSeri));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyById(chainEleNode, 1, &chain_handle, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcNodeGetPropertySizeById(chainEleNode, 0, &size);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcNodeGetPropertyById(chainEleNode, 0, buf, sizeof(buf), &isNull);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // GmcSetStmtAttr设置老的序列化函数，调用GmcNodeGetPropertyById和GmcNodeGetPropertySizeById成功。
    useNewSeri = false;
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_USE_NEW_DESERI, &useNewSeri, sizeof(useNewSeri));
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
}

static void CheckData4Error(GmcStmtT *stmt, const char *labelName)
{
    auto ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取Vertex
    const char *keyValue = "huawei";
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, keyValue, strlen(keyValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "access_control_list_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);

    // 测试点一：GmcSetStmtAttr设置新的序列化函数，调用GmcGetVertexPropertySizeByName和GmcGetVertexPropertyByName报错。
    CheckGetVertexPropertyByName(stmt);
    // 测试点二：GmcSetStmtAttr设置新的序列化函数，调用GmcGetVertexPropertySizeById和GmcGetVertexPropertyById报错。
    CheckGetVertexPropertyById(stmt);
    // 测试点三：GmcSetStmtAttr设置新的序列化函数，调用GmcNodeGetPropertyByName和GmcNodeGetPropertyById报错。
    CheckGetNodePropertyByNameAndId(stmt);
}

TEST_F(StClientTree, testTreeModelNodeRecordBufCheck)
{
    const char *configJson = R"({"max_record_count":1000})";
    const char *labelName = "accessList";
    GmcStmtT *stmt = syncStmt;

    int ret = GmcCreateVertexLabel(stmt, accessListSchema, configJson);
    ASSERT_EQ(GMERR_OK, ret);

    InsertData(stmt, labelName);

    CheckData4Error(stmt, labelName);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StClientTree, testTreeModelNodeDeltaVertex)
{
    const char *configJson = R"({"max_record_count":1000})";
    const char *labelName = "accessList";
    GmcStmtT *stmt = syncStmt;

    int ret = GmcCreateVertexLabel(stmt, accessListSchema, configJson);
    ASSERT_EQ(GMERR_OK, ret);

    InsertData(stmt, labelName);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    ret = GmcGetRootNode(stmt, &rootNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *chainEleNode;
    ret = GmcNodeAppendElement(chainNode, &chainEleNode);

    ASSERT_EQ(GMERR_OK, ret);
    const char *keyValue = "huawei";
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, keyValue, strlen(keyValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "access_control_list_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取Vertex
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, keyValue, strlen(keyValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "access_control_list_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(stmt, &rootNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);

    // case: get vector size
    uint32_t size;
    ret = GmcNodeGetElementCount(chainNode, &size);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(4u, size);

    // case: update item 1
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t temp = 100;
    ret = GmcNodeGetElementByIndex(chainNode, 1, &chainEleNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(chainEleNode, "nftable_chain_handle", GMC_DATATYPE_UINT64, &temp, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, keyValue, strlen(keyValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "access_control_list_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取Vertex
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "access_control_list_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, keyValue, strlen(keyValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetElementByIndex(chainNode, 1, &chainEleNode);
    EXPECT_EQ(GMERR_OK, ret);
    bool isNull;
    ret = GmcNodeGetPropertyByName(chainEleNode, "nftable_chain_handle", &temp, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100u, temp);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StClientTree, testManipulateTreeModelNode2WithNullMemberKey)
{
    const auto labelName = "vertexLabelTest1";
    const auto labelJson = vertexLabelTest3Schema;
    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    uint32_t value;
    GmcNodeT *node;
    GmcIndexKeyT *key;
    bool eof, &null = eof;

    /* insert data */
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(syncStmt, &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "c0", GMC_DATATYPE_UINT32, &(value = 131313), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(node, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    bool b9Value = false;
    for (value = 3; value <= 5; ++value) {
        ret = GmcNodeAppendElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b9", GMC_DATATYPE_BOOL, &b9Value, sizeof(bool));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    /* delta update data */
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(syncStmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(node, "member_key", &key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_UINT32, &(value = 4), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 7, GMC_DATATYPE_BOOL, &b9Value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByKey(node, key, &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "b3", GMC_DATATYPE_UINT32, &(value = 123456789), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &(value = 131313), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, "table_pk");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    /* verify data */
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(syncStmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(eof);

    ret = GmcGetChildNode(syncStmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAllocKey(node, "member_key", &key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 0, GMC_DATATYPE_UINT32, &(value = 4), sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetKeyValue(key, 7, GMC_DATATYPE_BOOL, &b9Value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByKey(node, key, &node);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetPropertyByName(node, "b1", &value, sizeof(uint32_t), &null);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(null);
    EXPECT_EQ(4u, value);
    ret = GmcNodeGetPropertyByName(node, "b3", &value, sizeof(uint32_t), &null);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(null);
    EXPECT_EQ(123456789u, value);
}

TEST_F(StClientTree, testExecuteSetPropertyById)
{
    // 持久化当前不支持super fields
    PERSISTENCE_NOT_SUPPORT;
    const char *cfgJson = R"({"max_record_count":2000})";
    const char *labelName = "sysModel";
    const char *labelJson =
        R"([{
        "version": "2.0", "type": "record", "name": "sysModel",
        "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "uint32", "nullable":true },
        { "name": "c2", "type": "record",
            "fields": [
            { "name": "f1", "type": "uint32", "nullable":true },
            { "name": "f2", "type": "uint32", "nullable":true }
            ]
        },
        { "name": "c3", "type": "uint32", "nullable":true },
        { "name": "c4", "type": "record",
            "vector": true, "size": 1024,
            "fields": [
            { "name": "b1", "type": "uint32", "nullable":true },
            { "name": "b2", "type": "uint32", "nullable":true }
            ]
        },
        { "name": "c5", "type": "bytes", "size": 128, "nullable":true },
        { "name": "c6", "type": "record",
            "fields": [
            { "name": "t1", "type": "fixed", "size": 6, "nullable":true },
            { "name": "t2", "type": "record",
                "fixed_array": true, "size": 512,
                "fields": [
                { "name": "h1", "type": "uint32", "nullable":true },
                { "name": "h2", "type": "uint32", "nullable":true },
                { "name": "h3", "type": "uint32", "nullable":true },
                { "name": "h4", "type": "uint32", "nullable":true }
                ],
                "super_fields":
                [
                    {
                        "name":"superfield1",
                        "comment":"test1",
                        "fields":{"begin":"h1","end":"h2"}
                    },
                    {
                        "name":"superfield2",
                        "comment":"test2",
                        "fields":{"begin":"h3","end":"h4"}
                    }
                ]
            },
            { "name": "t3", "type": "uint32", "nullable":true}
            ]
        }
        ],
        "super_fields":
        [
            {
                "name":"superfield0",
                "comment":"test",
                "fields":{"begin":"c0","end":"c1"}
            }
        ],
        "keys": [
        { "name": "sysModel_pk",
        "index": { "type": "primary" },
            "node": "sysModel",
            "fields": [ "c0" ],
            "constraints": { "unique": true }
        }
        ]
    }])";

    GmcStmtT *stmt = syncStmt;

    // create table
    int32_t ret = GmcCreateVertexLabel(stmt, labelJson, cfgJson);
    ASSERT_EQ(GMERR_OK, ret);

    // insert data
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t c0Value, c1Value, h1Value, h2Value;
    GmcNodeT *node = NULL;
    for (uint32_t i = 0; i < 1024; i++) {
        c0Value = i, c1Value = i + 100;
        ret = GmcSetVertexPropertyById(stmt, 0, GMC_DATATYPE_UINT32, &c0Value, sizeof(c0Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexPropertyById(stmt, 1, GMC_DATATYPE_UINT32, &c1Value, sizeof(c1Value));
        EXPECT_EQ(GMERR_OK, ret);
        // tree node
        ret = GmcGetChildNode(stmt, "c6/t2", &node);
        ASSERT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            h1Value = j, h2Value = j + 100;
            ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Value, sizeof(h1Value));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByIdWithoutType(node, 1, &h2Value, sizeof(h2Value));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // update data
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);

    c1Value = 1024;
    ret = GmcSetVertexPropertyById(stmt, 1, GMC_DATATYPE_UINT32, &c1Value, sizeof(c1Value));
    EXPECT_EQ(GMERR_OK, ret);
    // tree node
    ret = GmcGetChildNode(stmt, "c6/t2", &node);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        h2Value = j + 1000;
        ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Value, sizeof(h2Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
    }
    c0Value = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &c0Value, sizeof(c0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // scan data
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    c0Value = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &c0Value, sizeof(c0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    bool isFinish, isNull;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t propVal = 0;
        ret = GmcGetVertexPropertyById(stmt, 0, &propVal, sizeof(propVal), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(0u, propVal);
        uint32_t size = 0;
        ret = GmcGetVertexPropertySizeByName(stmt, "c1", &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4u, size);
        size = 0;
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4u, size);
        ret = GmcGetVertexPropertyByName(stmt, "c1", &propVal, sizeof(propVal), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(1024u, propVal);
        propVal = 0;
        ret = GmcGetVertexPropertyById(stmt, 1, &propVal, sizeof(propVal), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(1024u, propVal);
        // tree node
        ret = GmcGetChildNode(stmt, "c6/t2", &node);
        ASSERT_EQ(GMERR_OK, ret);
        // get array 0
        ret = GmcNodeGetPropertyById(node, 0, &propVal, sizeof(propVal), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(0u, propVal);
        size = 0;
        ret = GmcNodeGetPropertySizeByName(node, "h2", &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4u, size);
        size = 0;
        ret = GmcNodeGetPropertySizeById(node, 1, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4u, size);
        ret = GmcNodeGetPropertyByName(node, "h2", &propVal, sizeof(propVal), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(1000u, propVal);
        ret = GmcNodeGetPropertyById(node, 1, &propVal, sizeof(propVal), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(1000u, propVal);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
        // get array 0
        ret = GmcNodeGetPropertyById(node, 0, &propVal, sizeof(propVal), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(1u, propVal);
        ret = GmcNodeGetPropertyByName(node, "h2", &propVal, sizeof(propVal), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(1001u, propVal);
        ret = GmcNodeGetPropertyById(node, 1, &propVal, sizeof(propVal), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(1001u, propVal);
    }

    // delete data
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);

    c0Value = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &c0Value, sizeof(c0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // scan after delete
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t count = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        count++;
    }
    ASSERT_EQ(1023u, count);

    // drop table
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
#ifndef RTOSV2X
TEST_F(StClientTree, testExecuteSetSuperfieldById)
{
    const char *cfgJson = R"({"max_record_count":2000})";
    const char *labelName = "sysModel";
    const char *labelJson =
        R"([{
        "version": "2.0", "type": "record", "name": "sysModel",
        "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "uint32","nullable":true },
        { "name": "c2", "type": "record",
            "fields": [
            { "name": "f1", "type": "uint32","nullable":true },
            { "name": "f2", "type": "uint32","nullable":true }
            ]
        },
        { "name": "c3", "type": "uint32", "nullable":true },
        { "name": "c4", "type": "record",
            "vector": true, "size": 1024,
            "fields": [
            { "name": "b1", "type": "uint32","nullable":true },
            { "name": "b2", "type": "uint32","nullable":true }
            ]
        },
        { "name": "c5", "type": "bytes", "size": 128, "nullable":true },
        { "name": "c6", "type": "record",
            "fields": [
            { "name": "t1", "type": "fixed", "size": 6, "nullable":true },
            { "name": "t2", "type": "record",
                "fixed_array": true, "size": 512,
                "fields": [
                { "name": "h1", "type": "uint32","nullable":true },
                { "name": "h2", "type": "uint32","nullable":true },
                { "name": "h3", "type": "uint32","nullable":true },
                { "name": "h4", "type": "uint32","nullable":true }
                ],
                "super_fields":
                [
                    {
                        "name":"superfield1",
                        "comment":"test1",
                        "fields":{"begin":"h1","end":"h2"}
                    },
                    {
                        "name":"superfield2",
                        "comment":"test2",
                        "fields":{"begin":"h3","end":"h4"}
                    }
                ]
            },
            { "name": "t3", "type": "uint32","nullable":true}
            ]
        }
        ],
        "super_fields":
        [
            {
                "name":"superfield0",
                "comment":"test",
                "fields":{"begin":"c0","end":"c1"}
            }
        ],
        "keys": [
        { "name": "sysModel_pk",
        "index": { "type": "local" },
            "node": "sysModel",
            "fields": [ "c0" ],
            "constraints": { "unique": true }
        }
        ]
    }])";

    typedef struct sp_test_1 {
        uint32_t h1;
        uint32_t h2;
    } sp_test_1_t;

    GmcStmtT *stmt = syncStmt;

    // create table
    int32_t ret = GmcCreateVertexLabel(stmt, labelJson, cfgJson);
    ASSERT_EQ(GMERR_OK, ret);

    // insert data
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    sp_test_1_t sp_1, sp_2;
    GmcNodeT *node = NULL;
    for (uint32_t i = 0; i < 1024; i++) {
        sp_1 = {i, i + 100};
        ret = GmcSetSuperfieldById(stmt, 0, &sp_1, sizeof(sp_1));
        EXPECT_EQ(GMERR_OK, ret);

        // tree node
        ret = GmcGetChildNode(stmt, "c6/t2", &node);
        ASSERT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 2; j++) {
            sp_1 = {j, j + 100};
            ret = GmcNodeSetSuperfieldById(node, 0, &sp_1, sizeof(sp_1));
            EXPECT_EQ(GMERR_OK, ret);
            sp_2 = {j + 1, j + 101};
            ret = GmcNodeSetSuperfieldById(node, 1, &sp_2, sizeof(sp_2));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetNextElement(node, &node);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // update data
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);

    sp_1 = {0, 1124};
    ret = GmcSetSuperfieldById(stmt, 0, &sp_1, sizeof(sp_1));
    EXPECT_EQ(GMERR_OK, ret);
    // tree node
    ret = GmcGetChildNode(stmt, "c6/t2", &node);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < 2; j++) {
        sp_2 = {j + 1000, j + 1101};
        ret = GmcNodeSetSuperfieldById(node, 1, &sp_2, sizeof(sp_2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint32_t c0Value = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &c0Value, sizeof(c0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // scan data
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    c0Value = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &c0Value, sizeof(c0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    bool isFinish;
    char *spVal = (char *)malloc(8);
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        memset(spVal, 0, 8);
        ret = GmcGetSuperfieldById(stmt, 0, spVal, 8);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0u, *(uint32_t *)spVal);
        EXPECT_EQ(1124u, *(uint32_t *)(spVal + 4));
        memset(spVal, 0, 8);
        ret = GmcGetSuperfieldByName(stmt, "superfield0", 8, spVal);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0u, *(uint32_t *)spVal);
        EXPECT_EQ(1124u, *(uint32_t *)(spVal + 4));
        // tree node
        ret = GmcGetChildNode(stmt, "c6/t2", &node);
        ASSERT_EQ(GMERR_OK, ret);
        // get array 0
        memset(spVal, 0, 8);
        ret = GmcNodeGetSuperfieldById(node, 0, spVal, 8);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0u, *(uint32_t *)spVal);
        EXPECT_EQ(100u, *(uint32_t *)(spVal + 4));
        memset(spVal, 0, 8);
        ret = GmcNodeGetSuperfieldByName(node, "superfield2", spVal, 8);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1000u, *(uint32_t *)spVal);
        EXPECT_EQ(1101u, *(uint32_t *)(spVal + 4));
        memset(spVal, 0, 8);
        ret = GmcNodeGetSuperfieldById(node, 1, spVal, 8);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1000u, *(uint32_t *)spVal);
        EXPECT_EQ(1101u, *(uint32_t *)(spVal + 4));
        ret = GmcNodeGetNextElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
        // get array 1
        memset(spVal, 0, 8);
        ret = GmcNodeGetSuperfieldById(node, 0, spVal, 8);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1u, *(uint32_t *)spVal);
        EXPECT_EQ(101u, *(uint32_t *)(spVal + 4));
        memset(spVal, 0, 8);
        ret = GmcNodeGetSuperfieldByName(node, "superfield2", spVal, 8);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1001u, *(uint32_t *)spVal);
        EXPECT_EQ(1102u, *(uint32_t *)(spVal + 4));
        memset(spVal, 0, 8);
        ret = GmcNodeGetSuperfieldById(node, 1, spVal, 8);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1001u, *(uint32_t *)spVal);
        EXPECT_EQ(1102u, *(uint32_t *)(spVal + 4));
    }
    free(spVal);
    // delete data
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);

    c0Value = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &c0Value, sizeof(c0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // scan after delete
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t count = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        count++;
    }
    ASSERT_EQ(1023u, count);

    // drop table
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

TEST_F(StClientTree, testAbnormalSceneSetfieldById)
{
    const char *cfgJson = R"({"max_record_count":2000})";
    const char *labelName = "sysModel";
    const char *labelJson =
        R"([{
        "version": "2.0", "type": "record", "name": "sysModel",
        "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "uint32","nullable":true },
        { "name": "c2", "type": "record",
            "fields": [
            { "name": "f1", "type": "uint32","nullable":true },
            { "name": "f2", "type": "uint32","nullable":true }
            ]
        },
        { "name": "c3", "type": "uint32", "nullable":true },
        { "name": "c4", "type": "record",
            "vector": true, "size": 1024,
            "fields": [
            { "name": "b1", "type": "uint32","nullable":true },
            { "name": "b2", "type": "uint32","nullable":true }
            ]
        },
        { "name": "c5", "type": "bytes", "size": 128, "nullable":true },
        { "name": "c6", "type": "record",
            "fields": [
            { "name": "t1", "type": "fixed", "size": 6, "nullable":true },
            { "name": "t2", "type": "record",
                "fixed_array": true, "size": 512,
                "fields": [
                { "name": "h1", "type": "uint32","nullable":true },
                { "name": "h2", "type": "uint32","nullable":true },
                { "name": "h3", "type": "uint32","nullable":true },
                { "name": "h4", "type": "uint32" ,"nullable":true}
                ],
                "super_fields":
                [
                    {
                        "name":"superfield1",
                        "comment":"test1",
                        "fields":{"begin":"h1","end":"h2"}
                    },
                    {
                        "name":"superfield2",
                        "comment":"test2",
                        "fields":{"begin":"h3","end":"h4"}
                    }
                ]
            },
            { "name": "t3", "type": "uint32","nullable":true}
            ]
        }
        ],
        "super_fields":
        [
            {
                "name":"superfield0",
                "comment":"test",
                "fields":{"begin":"c0","end":"c1"}
            }
        ],
        "keys": [
        { "name": "sysModel_pk",
        "index": { "type": "local" },
            "node": "sysModel",
            "fields": [ "c0" ],
            "constraints": { "unique": true }
        },
        { "name": "sysModel_lochash",
        "index": { "type": "localhash" },
            "node": "sysModel",
            "fields": [ "c1" ],
            "constraints": { "unique": true }
        }
        ]
    }])";

    typedef struct sp_test_1 {
        uint32_t h1;
        uint32_t h2;
    } sp_test_1_t;

    const char *lastErrorStr = NULL;
    GmcStmtT *stmt = syncStmt;

    // create table
    int32_t ret = GmcCreateVertexLabel(stmt, labelJson, cfgJson);
    ASSERT_EQ(GMERR_OK, ret);

    // insert data
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t c0Value, c1Value, h1Value, h2Value;
    GmcNodeT *node = NULL;
    c0Value = 0, c1Value = 100;
    sp_test_1_t sp_1, sp_2;
    // normal
    ret = GmcSetVertexPropertyByIdWithoutType(stmt, 0, &c0Value, sizeof(c0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexPropertyById(stmt, 1, GMC_DATATYPE_UINT32, &c1Value, sizeof(c1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexPropertyById(stmt, 3, GMC_DATATYPE_UINT32, NULL, 0);
    EXPECT_EQ(GMERR_OK, ret);
    sp_1 = {1, 101};
    ret = GmcSetSuperfieldById(stmt, 0, &sp_1, sizeof(sp_1));
    EXPECT_EQ(GMERR_OK, ret);
    // abnormal
    ret = GmcSetVertexPropertyById(NULL, 1, GMC_DATATYPE_UINT32, &c1Value, sizeof(c1Value));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcSetVertexPropertyById(stmt, 7, GMC_DATATYPE_UINT32, &c1Value, sizeof(c1Value));
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ("Not normal property. Set prop by inv propId: 7, propeNum: 7.", lastErrorStr);
    ret = GmcSetSuperfieldById(NULL, 0, &sp_1, sizeof(sp_1));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcSetSuperfieldById(stmt, 0, NULL, 0);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ("Null value is not allowed. Parameter value of 'value'", lastErrorStr);
    ret = GmcSetSuperfieldById(stmt, 3, &sp_1, sizeof(sp_1));
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ("Not normal property. Record set superField by invId(3)", lastErrorStr);
    // tree node
    ret = GmcGetChildNode(stmt, "c6/t2", &node);
    ASSERT_EQ(GMERR_OK, ret);
    h1Value = 0, h2Value = 100;
    ret = GmcNodeSetPropertyById(node, 0, GMC_DATATYPE_UINT32, &h1Value, sizeof(h1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyById(node, 1, GMC_DATATYPE_UINT32, &h2Value, sizeof(h2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByIdWithoutType(node, 2, NULL, 0);
    EXPECT_EQ(GMERR_OK, ret);
    sp_2 = {1000, 1101};
    ret = GmcNodeSetSuperfieldById(node, 1, &sp_2, sizeof(sp_2));
    EXPECT_EQ(GMERR_OK, ret);
    // abnormal
    ret = GmcNodeSetPropertyById(NULL, 0, GMC_DATATYPE_UINT32, &h1Value, sizeof(h1Value));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcNodeSetPropertyByIdWithoutType(node, 4, &h1Value, sizeof(h1Value));
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeSetSuperfieldById(NULL, 1, &sp_2, sizeof(sp_2));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcNodeSetSuperfieldById(node, 1, NULL, 0);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcNodeSetSuperfieldById(node, 2, &sp_2, sizeof(sp_2));
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // update
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    sp_1 = {0, 1124};
    ret = GmcSetSuperfieldById(stmt, 0, &sp_1, sizeof(sp_1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // scan data
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    c0Value = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    EXPECT_EQ(GMERR_OK, ret);
    c1Value = 1124;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &c1Value, sizeof(c1Value));
    EXPECT_EQ(GMERR_OK, ret);
    // 覆盖设置的key id
    ret = GmcSetIndexKeyName(stmt, "sysModel_lochash");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    bool isFinish, isNull;
    uint32_t propVal = 0;
    char *spVal = (char *)malloc(8);
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        memset(spVal, 0, 8);
        ret = GmcGetVertexPropertyById(stmt, 0, &propVal, sizeof(propVal), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(0u, propVal);
        uint32_t size = 0;
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4u, size);
        ret = GmcGetVertexPropertyById(stmt, 1, &propVal, sizeof(propVal), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(1124u, propVal);
        ret = GmcGetSuperfieldById(stmt, 0, spVal, 8);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0u, *(uint32_t *)spVal);
        EXPECT_EQ(1124u, *(uint32_t *)(spVal + 4));
        memset(spVal, 0, 8);
        // abnromal
        ret = GmcGetVertexPropertySizeById(NULL, 1, &size);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

        ret = GmcGetVertexPropertySizeById(stmt, 7, &size);
        EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
        lastErrorStr = GmcGetLastError();
        EXPECT_STREQ("Not normal property. Get prop size by inv  id: 7, propeNum: 7.", lastErrorStr);

        ret = GmcGetVertexPropertySizeById(stmt, 1, NULL);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        lastErrorStr = GmcGetLastError();
        EXPECT_STREQ("Null value is not allowed. Parameter value of 'size'", lastErrorStr);

        ret = GmcGetVertexPropertyById(NULL, 0, &propVal, sizeof(propVal), &isNull);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

        ret = GmcGetVertexPropertyById(stmt, 7, &propVal, sizeof(propVal), &isNull);
        EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
        lastErrorStr = GmcGetLastError();
        EXPECT_STREQ("Not normal property. Get prop by Inv propId: 7, propeNum: 7.", lastErrorStr);

        ret = GmcGetVertexPropertyById(stmt, 0, NULL, 0, &isNull);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        lastErrorStr = GmcGetLastError();
        EXPECT_STREQ("Null value is not allowed. Parameter value of 'value'", lastErrorStr);

        ret = GmcGetVertexPropertyById(stmt, 0, &propVal, sizeof(propVal), NULL);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        lastErrorStr = GmcGetLastError();
        EXPECT_STREQ("Null value is not allowed. Parameter value of 'isNull'", lastErrorStr);

        ret = GmcGetSuperfieldById(NULL, 0, spVal, 8);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = GmcGetSuperfieldById(stmt, 0, NULL, 0);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        lastErrorStr = GmcGetLastError();
        EXPECT_STREQ("Null value is not allowed. Parameter value of 'value'", lastErrorStr);

        // tree node
        ret = GmcGetChildNode(stmt, "c6/t2", &node);
        ASSERT_EQ(GMERR_OK, ret);
        memset(spVal, 0, 8);
        ret = GmcNodeGetPropertyById(node, 0, &propVal, sizeof(propVal), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(0u, propVal);
        size = 0;
        ret = GmcNodeGetPropertySizeById(node, 1, &size);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4u, size);
        ret = GmcNodeGetPropertyById(node, 1, &propVal, sizeof(propVal), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(100u, propVal);
        ret = GmcNodeGetSuperfieldById(node, 1, spVal, 8);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1000u, *(uint32_t *)spVal);
        EXPECT_EQ(1101u, *(uint32_t *)(spVal + 4));
        // abnormal
        ret = GmcNodeGetPropertySizeById(NULL, 1, &size);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = GmcNodeGetPropertySizeById(NULL, 4, &size);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = GmcNodeGetPropertySizeById(NULL, 1, NULL);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = GmcNodeGetPropertyById(NULL, 0, &propVal, sizeof(propVal), &isNull);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = GmcNodeGetPropertyById(node, 4, &propVal, sizeof(propVal), &isNull);
        EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
        ret = GmcNodeGetPropertyById(node, 0, NULL, 0, &isNull);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = GmcNodeGetPropertyById(node, 0, &propVal, sizeof(propVal), NULL);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = GmcNodeGetSuperfieldById(NULL, 1, spVal, 8);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = GmcNodeGetSuperfieldById(node, 2, spVal, 8);
        EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
        ret = GmcNodeGetSuperfieldById(node, 1, NULL, 0);
        EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    }
    free(spVal);
    // delete all data
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // scan after delete
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t count = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        count++;
    }
    ASSERT_EQ(0u, count);

    // drop table
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

TEST_F(StClientTree, testGetPropType)
{
    const char *cfgJson = R"({"max_record_count":2000})";
    const char *labelName = "sysModel";
    const char *labelJson =
        R"([{
        "version": "2.0", "type": "record", "name": "sysModel",
        "fields": [
        { "name": "c0", "type": "uint32" },
        { "name": "c1", "type": "uint32" },
        { "name": "c3", "type": "uint32", "nullable":false },
        { "name": "c2", "type": "record",
            "fields": [
            { "name": "f1", "type": "uint32" },
            { "name": "f2", "type": "uint64" }
            ]
        },
        { "name": "c4", "type": "record",
            "vector": true, "size": 1024,
            "fields": [
            { "name": "b1", "type": "uint32" },
            { "name": "b2", "type": "uint32" }
            ]
        },
        { "name": "c5", "type": "bytes", "size": 128, "nullable":true },
        { "name": "c6", "type": "record",
            "fields": [
            { "name": "t1", "type": "fixed", "size": 6, "nullable":true },
            { "name": "t2", "type": "record",
                "fixed_array": true, "size": 512,
                "fields": [
                { "name": "h1", "type": "uint32" },
                { "name": "h2", "type": "uint32" },
                { "name": "h3", "type": "uint32" },
                { "name": "h4", "type": "uint32" }
                ],
                "super_fields":
                [
                    {
                        "name":"superfield1",
                        "comment":"test1",
                        "fields":{"begin":"h1","end":"h2"}
                    },
                    {
                        "name":"superfield2",
                        "comment":"test2",
                        "fields":{"begin":"h3","end":"h4"}
                    }
                ]
            },
            { "name": "t3", "type": "string"}
            ]
        }
        ],
        "super_fields":
        [
            {
                "name":"superfield0",
                "comment":"test",
                "fields":{"begin":"c0","end":"c1"}
            }
        ],
        "keys": [
        { "name": "sysModel_pk",
        "index": { "type": "primary" },
            "node": "sysModel",
            "fields": [ "c0","c1","c3" ],
            "constraints": { "unique": true }
        },
        {
            "name": "memberkey_test",
            "node": "t2",
            "fields": [ "h1", "h3"]
        }
        ]
    }])";

    GmcStmtT *stmt = syncStmt;
    // create table
    int32_t ret = GmcCreateVertexLabel(stmt, labelJson, cfgJson);
    ASSERT_EQ(GMERR_OK, ret);
    // insert data
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // nodename+fieldname  && 根节点+fieldname
    uint32_t propType = -1;
    uint32_t fieldId = -1;
    ret = GmcGetPropTypeByNodeNamePropName(stmt, "c2", "f2", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(DB_DATATYPE_UINT64, propType);
    ASSERT_EQ(1, (int32_t)fieldId);

    ret = GmcGetPropTypeByNodeNamePropName(stmt, "c2", "f3", &fieldId, &propType);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);

    propType = -1;
    fieldId = -1;
    ret = GmcGetPropTypeByNodeNamePropName(stmt, "t2", "h1", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(DB_DATATYPE_UINT32, propType);
    ASSERT_EQ(0, (int32_t)fieldId);

    propType = -1;
    fieldId = -1;
    ret = GmcGetPropTypeByNodeNamePropName(stmt, "sysModel", "c0", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(DB_DATATYPE_UINT32, propType);
    ASSERT_EQ(0, (int32_t)fieldId);

    // 根节点下node
    propType = -1;
    fieldId = -1;
    ret = GmcGetPropTypeByNodeNamePropName(stmt, "sysModel", "c6", &fieldId, &propType);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);

    // nodename+fileldid  && 根节点+fieldId
    propType = -1;
    char fieldName[128];
    GmcNodeNameAndPropIdT nodeNameAndPropId = {"t2", 2};
    ret = GmcGetPropTypeByNodeNamePropId(stmt, &nodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(DB_DATATYPE_UINT32, propType);
    ASSERT_STREQ("h3", fieldName);

    propType = -1;
    char fieldName1[128];
    GmcNodeNameAndPropIdT nodeNameAndPropId1 = {"sysModel", 5};
    ret = GmcGetPropTypeByNodeNamePropId(stmt, &nodeNameAndPropId1, fieldName1, 128, &propType);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(DB_DATATYPE_BYTES, propType);
    ASSERT_STREQ("c5", fieldName1);

    // fieldId越界检查
    propType = -1;
    char fieldName2[128];
    GmcNodeNameAndPropIdT nodeNameAndPropId2 = {"t2", 4};
    ret = GmcGetPropTypeByNodeNamePropId(stmt, &nodeNameAndPropId2, fieldName2, 128, &propType);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);

    // 根节点下Node检查
    propType = -1;
    char fieldName3[128];
    GmcNodeNameAndPropIdT nodeNameAndPropId3 = {"sysModel", 3};
    ret = GmcGetPropTypeByNodeNamePropId(stmt, &nodeNameAndPropId3, fieldName3, 128, &propType);
    ASSERT_EQ(GMERR_DATA_EXCEPTION, ret);

    // keyname+ order
    propType = -1;
    const char *keyName = "sysModel_pk";
    uint32_t labelId = 2;
    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    char fieldName4[128];
    ret = GmcGetPropTypeByKeyNamePropOrder(stmt, &keyNameAndOrder, fieldName4, 128, &propType);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(DB_DATATYPE_UINT32, propType);
    ASSERT_STREQ("c3", fieldName4);

    // test_memberkey
    propType = -1;
    const char *mkeyName = "memberkey_test";
    labelId = 1;
    GmcKeyNameAndOrderT keyNameAndOrder1 = {mkeyName, labelId};
    ret = GmcGetPropTypeByKeyNamePropOrder(stmt, &keyNameAndOrder1, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 测试非法输入
    ret = GmcGetPropTypeByNodeNamePropName(stmt, "c6", "t2", &fieldId, &propType);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);

    ret = GmcGetPropTypeByNodeNamePropName(stmt, "c2", NULL, &fieldId, &propType);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    ret = GmcGetPropTypeByNodeNamePropName(stmt, "c2", "", &fieldId, &propType);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    propType = -1;
    const char *keyName2 = "";
    uint32_t labelId2 = 2;
    GmcKeyNameAndOrderT keyNameAndOrder2 = {keyName2, labelId2};
    ret = GmcGetPropTypeByKeyNamePropOrder(stmt, &keyNameAndOrder2, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    // drop table
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

#endif

TEST_F(StClientTree, testFreeNodeAPI)
{
    Status ret = GmcFreeNode(NULL);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
}

TEST_F(StClientTree, testRecordNodeClear)
{
    const char *configJson = R"({"max_record_count":1000})";
    const char *labelName = "fixedTree";
    GmcStmtT *stmt = syncStmt;
    int ret = GmcCreateVertexLabel(stmt, fixedTreeSchema, configJson);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t table_flag = 1;
    GmcNodeT *rootNode;
    GmcNodeT *chainNode;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(rootNode, "c0", GMC_DATATYPE_UINT32, &table_flag, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeGetChild(rootNode, "c1", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t temp = 1;
    ret = GmcNodeSetPropertyByName(chainNode, "b1", GMC_DATATYPE_UINT32, &temp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(chainNode, "b2", GMC_DATATYPE_UINT32, &temp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 插入
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "table_pk");
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(stmt, &rootNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "c1", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeClear(chainNode);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "c1", &chainNode);
    ASSERT_EQ(GMERR_OK, ret);
    // 更新
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // scan
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取Vertex
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(eof, false);

    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "c1", &chainNode);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t size;
    ret = GmcNodeGetElementCount(chainNode, &size);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, size);

    uint32_t expect = 0;
    bool isNull;
    ret = GmcNodeGetPropertyByName(chainNode, "b1", &expect, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isNull, true);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}
