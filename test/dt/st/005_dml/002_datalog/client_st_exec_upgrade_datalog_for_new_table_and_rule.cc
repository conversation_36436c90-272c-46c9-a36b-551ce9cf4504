/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Datalog ST for upgrade.
 * Author: GMDBv5 EE Team
 * Create: 2023-07-25
 */

#include <iostream>
#include <climits>
#include <string>
#include <pthread.h>
#include <fstream>
#include "client_common_st.h"
#include "client_st_exec_datalog.h"
#include "client_st_exec_datalog_common.h"

#include "st_common.h"
#define CONCURRENT_NUM 1
#define DEFAULT_NSP "ylog"

using namespace std;

class ClientStExecUpgradeDatalogForNewTableAndNewRule : public ClientStExecDatalog {
public:
    Status GetUpgradeVersionOfVertexLabel(GmcStmtT *syncStmt, char *labelName, int *version)
    {
        Status ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = GmcGetUpgradeVersion(syncStmt, version);
        if (ret != GMERR_OK) {
            return ret;
        }
        return GMERR_OK;
    }

    static void SetUpTestCase()
    {
        StartDbServerWithConfig(
            "\"DBA=root:gmrule\" \"udfEnable=1\" \"isFastReadUncommitted=0\" \"schemaLoader=2\" "
            "\"datalogUpgradeFetchSize=1\" \"workerHungThreshold=30,299,300\" "
            "\"schemaPath=./002_datalog/st_schema;./002_datalog/st_schema;./002_datalog/st_schema;\" ");

        st_clt_init();
        st_connect();
        CreateAndStartEpoll(&responseEpollThreadId, &responseEpollFd);
        CreateAndStartEpoll(&timeoutEpollThreadId, &timeoutEpollFd);
        EXPECT_EQ(GMERR_OK, GmcRegTimeoutEpollFunc(TimeoutEpollReg));
        // 此处心跳定时器与TimeOut定时器使用同一个Epoll，可设置为不同Epoll
        EXPECT_EQ(GMERR_OK, GmcRegHeartBeatEpollFunc(TimeoutEpollReg));
        if (IsEulerEnv()) {
            DbSleep(1000);
        } else {
            st_check_hpe_server_running();
        }
        printf("start response epoll and timeout epoll thread\n");
        printf("response epoll fd: %d, timeout epoll fd: %d\n", responseEpollFd, timeoutEpollFd);
    }

    static void *ConcurrentView4Rollback(void *arg)
    {
        // 用于测试并发场景
        int *signalPara = (int *)arg;
        const char *sysViewCmd1 = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
        const char *sysViewCmd2 = "gmsysview count";
        const char *matchStr1[] = {"finish"};
        const char *matchStr2[] = {"count"};
        while (*signalPara == 1) {
            usleep(1000);
            (void)StExecuteCommandWithMatch((char *)sysViewCmd1, matchStr1, ELEMENT_COUNT(matchStr1));
            (void)StExecuteCommandWithMatch((char *)sysViewCmd2, matchStr2, ELEMENT_COUNT(matchStr2));
        }
        printf(" =================== \n ConcurrentView4Rollback down \n =================== \n");
        return NULL;
    }

    void SetUp()
    {
        system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");
        signal = 1;
        pthread_create(&selfTid, NULL, ConcurrentView4Rollback, &signal);
        printf(" =================== \n SetUp \n =================== \n");
    }

    void TearDown()
    {
        system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
        signal = 0;
        pthread_join(selfTid, NULL);
        printf(" =================== \n TearDown \n =================== \n");
        Status ret = CheckRefCount();
        EXPECT_EQ(GMERR_OK, ret);
    }

protected:
    int signal = 0;
    pthread_t selfTid;
};
INSTANTIATE_TEST_CASE_P(ExecUpgradeDatalog, ClientStExecUpgradeDatalogForNewTableAndNewRule,
    ::testing::Values(
        StClientTestData{.isAsync = false, .intValue = 0}, StClientTestData{.isAsync = true, .intValue = 1}));

// 使用%block 1和%block 0交替连续升级，每次升级都包含跨Topo Join
// %block 0、%block 1、%block 0、%block 1，无法join出数据
TEST_F(ClientStExecUpgradeDatalogForNewTableAndNewRule, ReplaceRuleWithNewTable1)
{
    char *fileName = (char *)"client_st_upgrade_topo_join_test1";
    char *defaultNsp = (char *)"public";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    // // insert
    int32_t obj[][5] = {{1, 1, 1, 1, 1}, {1, 1, 2, 2, 2}, {1, 1, 3, 3, 3}};
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "A1", *obj, ELEMENT_COUNT(obj), false));

    int32_t obj4[][5] = {{1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}};
    ScanAndCheckTable(syncStmt, "D1", (int32_t *)obj4, ELEMENT_COUNT(obj4));
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");

    char *deltaFileName = (char *)"client_st_upgrade_topo_join_test1_patch1";
    char *upgradeSoName = (char *)"client_st_upgrade_topo_join_test1_patch1";
    char *rollbackSoName = (char *)"client_st_upgrade_topo_join_test1_rollback1";
    char *fileWithRuleName = (char *)"client_st_upgrade_topo_join_test1_rule";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    char *deltaFileName1 = (char *)"client_st_upgrade_topo_join_test1_patch";
    char *upgradeSoName1 = (char *)"client_st_upgrade_topo_join_test1_patch2";
    char *rollbackSoName1 = (char *)"client_st_upgrade_topo_join_test1_rollback2";
    char *fileWithRuleName1 = (char *)"client_st_upgrade_topo_join_test1_patch1_full";
    ExecPrepare4Upgrade(fileWithRuleName1, deltaFileName1, upgradeSoName1);
    ExecPrepare4RollbackUpgrade(fileWithRuleName1, deltaFileName1, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName1, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    system("gmsysview record D1");

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// AGG 规则右表是中间表
TEST_F(ClientStExecUpgradeDatalogForNewTableAndNewRule, ReplaceRuleWithNewTable2)
{
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char *fileName = (char *)"client_st_upgrade_topo_join_agg";
    char *udfFileName = (char *)"client_st_upgrade_add_udf";
    char *defaultNsp = (char *)"public";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));

    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 2}, {1, 0, 1, 1, 3}, {1, 0, 2, 2, 2}, {1, 0, 2, 2, 4},
        {1, 0, 2, 2, 5}, {1, 0, 3, 3, 1}, {1, 0, 3, 3, 3}, {1, 0, 3, 3, 9}, {1, 0, 4, 5, 1}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inpA1", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t records2[][6] = {{1, 0, 5, 2, 2, 2}, {1, 0, 1, 1, 4, 5}, {1, 0, 3, 1, 1, 1}, {1, 0, 9, 1, 3, 3}};
    ScanAndCheckTable(syncStmt, "out", (int32_t *)records2, ELEMENT_COUNT(records2));

    // %block 1
    char *deltaFileName = (char *)"client_st_upgrade_topo_join_agg_patch5";
    char *upgradeSoName = (char *)"client_st_upgrade_topo_join_agg_patch5";
    char *rollbackSoName1 = (char *)"client_st_upgrade_topo_join_agg_rollback5";
    char *fileWithRuleName = (char *)"client_st_upgrade_topo_join_agg_rule";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    int32_t records3[][6] = {{1, 0, 5, 2, 2, 2}, {1, 0, 1, 4, 5, 1}, {1, 0, 3, 1, 1, 1}, {1, 0, 9, 3, 3, 1}};
    ScanAndCheckTable(syncStmt, "out", (int32_t *)records3, ELEMENT_COUNT(records3));
    system("gmsysview record out");
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    system("gmsysview record out");
    ScanAndCheckTable(syncStmt, "out", (int32_t *)records2, ELEMENT_COUNT(records2));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// AGG 规则右表是输入表
TEST_F(ClientStExecUpgradeDatalogForNewTableAndNewRule, ReplaceRuleWithNewTable3)
{
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char *fileName = (char *)"client_st_upgrade_topo_join_agg_for_new_agg";
    char *udfFileName = (char *)"client_st_upgrade_add_udf";
    char *defaultNsp = (char *)"public";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));

    int32_t records1[][6] = {{1, 0, 1, 1, 1, 200}, {1, 0, 1, 1, 2, 200}, {1, 0, 1, 1, 3, 200}, {1, 0, 2, 2, 2, 200},
        {1, 0, 2, 2, 4, 200}, {1, 0, 2, 2, 5, 200}, {1, 0, 3, 3, 1, 200}, {1, 0, 3, 3, 3, 200}, {1, 0, 3, 3, 9, 200},
        {1, 0, 4, 5, 1, 200}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t records2[][6] = {{1, 0, 5, 2, 2, 2}, {1, 0, 1, 1, 4, 5}, {1, 0, 3, 1, 1, 1}, {1, 0, 9, 1, 3, 3}};
    ScanAndCheckTable(syncStmt, "out", (int32_t *)records2, ELEMENT_COUNT(records2));

    // %block 1
    char *deltaFileName = (char *)"client_st_upgrade_topo_join_agg_for_new_agg_patch5";
    char *upgradeSoName = (char *)"client_st_upgrade_topo_join_agg_for_new_agg_patch5";
    char *rollbackSoName1 = (char *)"client_st_upgrade_topo_join_agg_for_new_agg_rollback5";
    char *fileWithRuleName = (char *)"client_st_upgrade_topo_join_agg_for_new_agg_rule";
    char *upgradeUdfFileName = (char *)"client_st_upgrade_add_udf2";
    ExecPrepare4UpgradeWithUdf(fileWithRuleName, deltaFileName, upgradeUdfFileName, upgradeSoName);
    ExecPrepare4RollbackUpgradeWithUdf(fileWithRuleName, deltaFileName, upgradeUdfFileName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));
    int32_t records3[][6] = {{1, 0, 2, 2, 2, 5}, {1, 0, 1, 4, 5, 1}, {1, 0, 1, 1, 1, 3}, {1, 0, 1, 3, 3, 9}};
    ScanAndCheckTable(syncStmt, "out", (int32_t *)records3, ELEMENT_COUNT(records3));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));
    ScanAndCheckTable(syncStmt, "out", (int32_t *)records2, ELEMENT_COUNT(records2));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 新增TBM表/transient表/输入表，以及新增规则，不和其他表join
// transient表数据会被清空
// 降级失败，触发回滚，然后再次降级成功
TEST_F(ClientStExecUpgradeDatalogForNewTableAndNewRule, HotPatch_NewTbmRuleWithNewTbmTableAndNewRule1)
{
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 100");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char *fileName = (char *)"client_st_upgrade_tbm";
    char *udfFileName = (char *)"client_st_upgrade_tbm";
    char *defaultNsp = (char *)"public";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));

    int32_t records1[][5] = {{1, 0, 1, 1, 2001}, {1, 0, 1, 2, 2002}, {1, 0, 1, 3, 2003}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);

    char *deltaFileName = (char *)"client_st_upgrade_tbm_patch3";
    char *upgradeSoName = (char *)"client_st_upgrade_tbm_patch3";
    char *rollbackSoName1 = (char *)"client_st_upgrade_tbm_rollback";
    char *fileWithRuleName = (char *)"client_st_upgrade_tbm_rule";
    ExecPrepare4UpgradeWithUdf(fileWithRuleName, deltaFileName, upgradeSoName, upgradeSoName);
    ExecPrepare4RollbackUpgradeWithUdf(fileWithRuleName, deltaFileName, upgradeSoName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");

    ret = InsertInpTableBatch(syncConn, syncStmt, "nsp51.inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t expect[1][5] = {0};
    ScanAndCheckTable(syncStmt, "nsp51.midA", (int32_t *)expect, 0);

    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    const char *expectedResults[] = {
        "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC",
    };
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccessWithExpectedResults(10, expectedResults, ELEMENT_COUNT(expectedResults)));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));

    std::ifstream logFile("./tbmRollBackTest.log");
    std::vector<std::array<int, 6>> records;  //
    std::string content;

    while (std::getline(logFile, content)) {
        std::stringstream ss(content);
        std::array<int, 6> arr;
        ss >> arr[0] >> arr[1] >> arr[2] >> arr[3] >> arr[4] >> arr[5];
        records.push_back(arr);
    }
    int32_t expect2[][6] = {
        {+3, +1, 0, 1, 1, 2001},
        {+3, +1, 0, 1, 2, 2002},
        {+3, +1, 0, 1, 3, 2003},
        {51, +1, 5, 1, 3, 2003},
        {51, +1, 5, 1, 1, 2001},
        {51, +1, 5, 1, 2, 2002},
        {51, -1, 5, 1, 3, 2003},
        {51, -1, 5, 1, 1, 2001},
        {51, -1, 5, 1, 2, 2002},
        {51, +1, 5, 1, 1, 2001},
        {51, +1, 5, 1, 3, 2003},
        {51, -1, 5, 1, 3, 2003},
        {51, -1, 5, 1, 1, 2001},
        {51, -1, 5, 1, 2, 2002},
    };
    EXPECT_EQ(ELEMENT_COUNT(expect2), records.size());
    for (int j = 0; j < records.size(); j++) {
        EXPECT_EQ(expect2[j][0], records[j][0]);
        EXPECT_EQ(expect2[j][1], records[j][1]);
        EXPECT_EQ(expect2[j][3], records[j][3]);
        EXPECT_EQ(expect2[j][4], records[j][4]);
        EXPECT_EQ(expect2[j][5], records[j][5]);
    }

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 用新TBM表+中间表，代替定义有问题的TBM表和规则，并声明precedence oldTbm,newTbm
// transient表数据会被清空
// 规则都有相同触发表
TEST_F(ClientStExecUpgradeDatalogForNewTableAndNewRule, HotPatch_ReplaceTbmTableWithNewTbmTable1)
{
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 100");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char *fileName = (char *)"client_st_upgrade_tbm";
    char *udfFileName = (char *)"client_st_upgrade_tbm";
    char *defaultNsp = (char *)"public";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));

    int32_t records1[][5] = {{1, 0, 1, 1, 2001}, {1, 0, 1, 2, 2002}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);

    char *deltaFileName = (char *)"client_st_upgrade_tbm_patch1";
    char *upgradeSoName = (char *)"client_st_upgrade_tbm_patch1";
    char *upgradeUdfFileName = (char *)"client_st_upgrade_tbm_patch";
    char *rollbackSoName1 = (char *)"client_st_upgrade_tbm_rollback1";
    char *fileWithRuleName = (char *)"client_st_upgrade_tbm_rule";
    ExecPrepare4UpgradeWithUdf(fileWithRuleName, deltaFileName, upgradeUdfFileName, upgradeSoName);
    ExecPrepare4RollbackUpgradeWithUdf(fileWithRuleName, deltaFileName, upgradeUdfFileName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    int32_t expect[1][5] = {0};
    ScanAndCheckTable(syncStmt, "nsp5.midA", (int32_t *)expect, 0);
    const char *expectedResults[] = {
        "name: REDO_TRIGGER_TABLES]\n"
        "    TABLE_NAME: nsp3.outC\n"
        "    ------------------\n"
        "    TABLE_NAME: nsp3.outC\n",
    };
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccessWithExpectedResults(10, expectedResults, ELEMENT_COUNT(expectedResults)));

    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));

    std::ifstream logFile("./tbmRollBackTest.log");
    std::vector<std::array<int, 6>> records;  //
    std::string content;

    while (std::getline(logFile, content)) {
        std::stringstream ss(content);
        std::array<int, 6> arr;
        ss >> arr[0] >> arr[1] >> arr[2] >> arr[3] >> arr[4] >> arr[5];
        records.push_back(arr);
    }
    EXPECT_EQ(10, records.size());
    int i = 0;
    // DML
    for (int j = 0; i < records.size() && j < ELEMENT_COUNT(records1); i++, j++) {
        EXPECT_EQ(3, records[i][0]);
        EXPECT_EQ(1, records[i][1]);
        EXPECT_EQ(0, records[i][2]);
        EXPECT_EQ(records1[j][2], records[i][3]);
        EXPECT_EQ(records1[j][3], records[i][4]);
        EXPECT_EQ(records1[j][4], records[i][5]);
    }
    // upgrade DML
    for (int j = 0; i < records.size() && j < ELEMENT_COUNT(records1); i++, j++) {
        EXPECT_EQ(3, records[i][0]);
        EXPECT_EQ(-1, records[i][1]);
        EXPECT_EQ(0, records[i][2]);
        EXPECT_EQ(records1[j][2], records[i][3]);
        EXPECT_EQ(records1[j][3], records[i][4]);
        EXPECT_EQ(records1[j][4], records[i][5]);
    }
    // upgrade DML
    for (int j = 0; i < records.size() && j < ELEMENT_COUNT(records1); i++, j++) {
        EXPECT_EQ(5, records[i][0]);
        EXPECT_EQ(1, records[i][1]);
        EXPECT_EQ(0, records[i][2]);
        EXPECT_EQ(records1[j][2], records[i][3]);
        EXPECT_EQ(records1[j][3], records[i][4]);
        EXPECT_EQ(records1[j][4], records[i][5]);
    }
    // rollback redo DML nsp3.outB
    for (int j = 0; i < records.size() && j < ELEMENT_COUNT(records1); i++, j++) {
        EXPECT_EQ(5, records[i][0]);
        EXPECT_EQ(-1, records[i][1]);
        EXPECT_EQ(0, records[i][2]);
        EXPECT_EQ(records1[j][2], records[i][3]);
        EXPECT_EQ(records1[j][3], records[i][4]);
        EXPECT_EQ(records1[j][4], records[i][5]);
    }
    // rollback undo DML nsp51.outA
    for (int j = 0; i < records.size() && j < ELEMENT_COUNT(records1); i++, j++) {
        EXPECT_EQ(3, records[i][0]);
        EXPECT_EQ(1, records[i][1]);
        EXPECT_EQ(0, records[i][2]);
        EXPECT_EQ(records1[j][2], records[i][3]);
        EXPECT_EQ(records1[j][3], records[i][4]);
        EXPECT_EQ(records1[j][4], records[i][5]);
    }

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 只新增规则，代替有问题规则，如join错表
// 规则都有相同触发表
TEST_F(ClientStExecUpgradeDatalogForNewTableAndNewRule, HotPatch_ReplaceTbmRuleWithNewRule1)
{
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 100");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char *fileName = (char *)"client_st_upgrade_tbm";
    char *udfFileName = (char *)"client_st_upgrade_tbm";
    char *defaultNsp = (char *)"public";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));

    int32_t records1[][5] = {{1, 0, 1, 1, 2001}, {1, 0, 1, 2, 2002}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t records21[][5] = {{1, 0, 1, 1, 2001}, {1, 0, 1, 2, 2003}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "nsp522.outB", (int32_t *)records21, ELEMENT_COUNT(records21), true);
    EXPECT_EQ(GMERR_OK, ret);

    char *deltaFileName = (char *)"client_st_upgrade_tbm_patch4";
    char *upgradeSoName = (char *)"client_st_upgrade_tbm_patch1";
    char *upgradeUdfFileName = (char *)"client_st_upgrade_tbm_patch";
    char *rollbackSoName1 = (char *)"client_st_upgrade_tbm_rollback1";
    char *fileWithRuleName = (char *)"client_st_upgrade_tbm_rule";
    ExecPrepare4UpgradeWithUdf(fileWithRuleName, deltaFileName, upgradeUdfFileName, upgradeSoName);
    ExecPrepare4RollbackUpgradeWithUdf(fileWithRuleName, deltaFileName, upgradeUdfFileName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    const char *expectedResults[] = {
        "name: REDO_TRIGGER_TABLES]\n"
        "    TABLE_NAME: nsp3.outC\n"
        "    ------------------\n"
        "    TABLE_NAME: nsp3.outC\n",
    };
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccessWithExpectedResults(10, expectedResults, ELEMENT_COUNT(expectedResults)));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));

    std::ifstream logFile("./tbmRollBackTest.log");
    std::vector<std::array<int, 6>> records;  //
    std::string content;

    while (std::getline(logFile, content)) {
        std::stringstream ss(content);
        std::array<int, 6> arr;
        ss >> arr[0] >> arr[1] >> arr[2] >> arr[3] >> arr[4] >> arr[5];
        records.push_back(arr);
    }
    int32_t expectData[][6] = {
        {3, +1, 0, 1, 1, 2001},
        {3, +1, 0, 1, 2, 2002},
        {3, -1, 0, 1, 2, 2002},
        {3, +1, 0, 1, 2, 2002},
    };
    EXPECT_EQ(ELEMENT_COUNT(expectData), records.size());
    for (int j = 0; j < records.size(); j++) {
        EXPECT_EQ(expectData[j][0], records[j][0]);
        EXPECT_EQ(expectData[j][1], records[j][1]);
        EXPECT_EQ(expectData[j][3], records[j][3]);
        EXPECT_EQ(expectData[j][4], records[j][4]);
        EXPECT_EQ(expectData[j][5], records[j][5]);
    }

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 只新增规则，代替有问题规则，如join错表
// 规则都有相同触发表
TEST_F(ClientStExecUpgradeDatalogForNewTableAndNewRule, HotPatch_ReplaceTbmRuleWithNewRule2)
{
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 100");
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char *fileName = (char *)"client_st_upgrade_tbm";
    char *udfFileName = (char *)"client_st_upgrade_tbm2";
    char *defaultNsp = (char *)"public";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));

    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t records2[][5] = {{1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 4, 4, 4}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "nsp51.outB", (int32_t *)records2, ELEMENT_COUNT(records2), true);
    EXPECT_EQ(GMERR_OK, ret);

    char *deltaFileName = (char *)"client_st_upgrade_tbm_patch2";
    char *upgradeSoName = (char *)"client_st_upgrade_tbm_patch2";
    char *upgradeUdfFileName = (char *)"client_st_upgrade_tbm_patch";
    char *rollbackSoName1 = (char *)"client_st_upgrade_tbm_rollback2";
    char *fileWithRuleName = (char *)"client_st_upgrade_tbm_rule";
    ExecPrepare4UpgradeWithUdf(fileWithRuleName, deltaFileName, upgradeUdfFileName, upgradeSoName);
    ExecPrepare4RollbackUpgradeWithUdf(fileWithRuleName, deltaFileName, upgradeUdfFileName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));

    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));

    std::ifstream logFile("./tbmRollBackTest.log");
    std::vector<std::array<int, 6>> records;
    std::string content;

    while (std::getline(logFile, content)) {
        std::stringstream ss(content);
        std::array<int, 6> arr;
        ss >> arr[0] >> arr[1] >> arr[2] >> arr[3] >> arr[4] >> arr[5];
        records.push_back(arr);
    }
    int32_t expectData[][6] = {
        {4, 1, 0, 3, 3, 3},
        {4, 1, 0, 1, 1, 1},
        {4, 1, 0, 2, 2, 2},
        {4, -1, 0, 3, 3, 3},
        {4, 1, 0, 3, 3, 3},
    };
    for (int j = 0; j < records.size(); j++) {
        EXPECT_EQ(expectData[j][0], records[j][0]);
        EXPECT_EQ(expectData[j][1], records[j][1]);
        EXPECT_EQ(expectData[j][3], records[j][3]);
        EXPECT_EQ(expectData[j][4], records[j][4]);
        EXPECT_EQ(expectData[j][5], records[j][5]);
    }

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

void PubsubCBForUpgrade(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    static int cnt = 0;
    cout << "====================== pubsub callback =========================" << endl;
    EXPECT_EQ(1u, info->labelCount);
    int32_t dataNum = *(int32_t *)userData;

    uint16_t failedDataNum = 0;
    uint16_t failedIndexes[128];
    int32_t a, b, c, dtlReservedCount, upgradeVersion;
    int32_t curDataNum = 0;
    for (bool eof, &isNull = eof;; dataNum++, curDataNum++) {
        auto ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }

        ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(a), &isNull));
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "b", &b, sizeof(b), &isNull));
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "c", &c, sizeof(c), &isNull));
        EXPECT_EQ(GMERR_OK,
            GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(dtlReservedCount), &isNull));
        EXPECT_EQ(GMERR_OK,
            GmcGetVertexPropertyByName(stmt, "upgradeVersion", &upgradeVersion, sizeof(upgradeVersion), &isNull));

        FILE *fp = fopen("./pubsubRollBackTest.log", "a+");
        printf("new tuple: a: %d, b: %d, c: %d, dtlReservedCount: %d, upgradeVersion: %d\n", a, b, c, dtlReservedCount,
            upgradeVersion);
        (void)fprintf(fp, "%d %d %d %d %d\n", dtlReservedCount, upgradeVersion, a, b, c);
        (void)fclose(fp);
    }
    if (dtlReservedCount == -1) {
        cnt++;
    }
    if (cnt == 3) {
        cnt++;
        failedDataNum = 1;
        failedIndexes[0] = curDataNum;
    }
    // 用户消息创建
    GmcRespT *response;
    GmcCreateResp(stmt, &response);
    GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
    // 用户处理错误信息回填
    GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
    // 用户信息发送
    GmcSendResp(stmt, response);
    // 用户信息销毁
    GmcDestroyResp(stmt, response);
}

static int g_gmdbTestDataCntForPubsub = 0;
void PubsubCBForUpgradeForReturnError(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    cout << "====================== pubsub callback " << g_gmdbTestDataCntForPubsub
         << " =========================" << endl;
    EXPECT_EQ(1u, info->labelCount);
    int32_t expectReturnErrorNum = *(int32_t *)userData;

    uint16_t failedDataNum = 0;
    uint16_t failedIndexes[128];
    int32_t a, b, c, dtlReservedCount, upgradeVersion;
    int32_t curDataNum = 0;
    for (bool eof, &isNull = eof;; curDataNum++) {
        auto ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }
        g_gmdbTestDataCntForPubsub++;

        ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(a), &isNull));
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "b", &b, sizeof(b), &isNull));
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "c", &c, sizeof(c), &isNull));
        EXPECT_EQ(GMERR_OK,
            GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(dtlReservedCount), &isNull));
        EXPECT_EQ(GMERR_OK,
            GmcGetVertexPropertyByName(stmt, "upgradeVersion", &upgradeVersion, sizeof(upgradeVersion), &isNull));

        FILE *fp = fopen("./pubsubRollBackTest.log", "a+");
        printf("new tuple: a: %d, b: %d, c: %d, dtlReservedCount: %d, upgradeVersion: %d\n", a, b, c, dtlReservedCount,
            upgradeVersion);
        (void)fprintf(fp, "%d %d %d %d %d\n", dtlReservedCount, upgradeVersion, a, b, c);
        (void)fclose(fp);
    }
    if (g_gmdbTestDataCntForPubsub == expectReturnErrorNum) {
        g_gmdbTestDataCntForPubsub++;
        failedDataNum = 1;
        failedIndexes[0] = curDataNum;
    }
    // 用户消息创建
    GmcRespT *response;
    GmcCreateResp(stmt, &response);
    GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
    // 用户处理错误信息回填
    GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
    // 用户信息发送
    GmcSendResp(stmt, response);
    // 用户信息销毁
    GmcDestroyResp(stmt, response);
}

void PubsubCBForUpgradeForSuccess(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    static int cnt = 0;
    cout << "====================== pubsub callback " << cnt << " =========================" << endl;
    EXPECT_EQ(1u, info->labelCount);
    int32_t dataNum = *(int32_t *)userData;

    uint16_t failedDataNum = 0;
    uint16_t failedIndexes[128];
    int32_t a, b, c, dtlReservedCount, upgradeVersion;
    int32_t curDataNum = 0;
    for (bool eof, &isNull = eof;; dataNum++, curDataNum++) {
        auto ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }
        cnt++;

        ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(a), &isNull));
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "b", &b, sizeof(b), &isNull));
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "c", &c, sizeof(c), &isNull));
        EXPECT_EQ(GMERR_OK,
            GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(dtlReservedCount), &isNull));
        EXPECT_EQ(GMERR_OK,
            GmcGetVertexPropertyByName(stmt, "upgradeVersion", &upgradeVersion, sizeof(upgradeVersion), &isNull));

        FILE *fp = fopen("./pubsubRollBackTest.log", "a+");
        printf("new tuple: a: %d, b: %d, c: %d, dtlReservedCount: %d, upgradeVersion: %d\n", a, b, c, dtlReservedCount,
            upgradeVersion);
        (void)fprintf(fp, "%d %d %d %d %d\n", dtlReservedCount, upgradeVersion, a, b, c);
        (void)fclose(fp);
    }
    // 用户消息创建
    GmcRespT *response;
    GmcCreateResp(stmt, &response);
    GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
    // 用户处理错误信息回填
    GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
    // 用户信息发送
    GmcSendResp(stmt, response);
    // 用户信息销毁
    GmcDestroyResp(stmt, response);
}

// 新增输入表/transient中间表，以及新增规则，投影到已有的tbm输出表上
// transient表数据会被清空
// 降级失败，触发回滚，然后再次降级成功
TEST_F(ClientStExecUpgradeDatalogForNewTableAndNewRule, HotPatch_NewIntputAndIntermediateTable1)
{
    system("rm -rf ./tbmRollBackTest.log");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char *fileName = (char *)"client_st_upgrade_tbm";
    char *udfFileName = (char *)"client_st_upgrade_tbm";
    char *defaultNsp = (char *)"public";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));
    Status ret = GMERR_OK;
    int32_t records1[][5] = {{1, 0, 1, 1, 2001}, {1, 0, 1, 2, 2002}, {1, 0, 1, 3, 2003}};
    // 写输入表
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertInpTableBatch(syncConn, syncStmt, "nsp522.outB", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);

    // 新增输入表/transient中间表
    char *deltaFileName = (char *)"client_st_upgrade_tbm_patch6";
    char *upgradeSoName = (char *)"client_st_upgrade_tbm_patch3";
    char *rollbackSoName1 = (char *)"client_st_upgrade_tbm_rollback";
    char *fileWithRuleName = (char *)"client_st_upgrade_tbm_rule";
    ExecPrepare4UpgradeWithUdf(fileWithRuleName, deltaFileName, upgradeSoName, upgradeSoName);
    ExecPrepare4RollbackUpgradeWithUdf(fileWithRuleName, deltaFileName, upgradeSoName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");

    int32_t expect[1][5] = {0};
    // 检查 transient finish 表
    ScanAndCheckTable(syncStmt, "nsp51.midA", (int32_t *)expect, 0);

    // 触发回滚 第1次，预期失败
    const char *expectedResults[] = {
        "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC",
    };
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccessWithExpectedResults(10, expectedResults, ELEMENT_COUNT(expectedResults)));
    // 触发回滚 第2次，预期成功
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));

    std::ifstream logFile("./tbmRollBackTest.log");
    std::vector<std::array<int, 6>> records;  //
    std::string content;

    while (std::getline(logFile, content)) {
        std::stringstream ss(content);
        std::array<int, 6> arr;
        ss >> arr[0] >> arr[1] >> arr[2] >> arr[3] >> arr[4] >> arr[5];
        records.push_back(arr);
    }
    int32_t expect2[][6] = {{3, 1, 0, 1, 1, 2001}, {3, 1, 0, 1, 2, 2002}, {3, 1, 0, 1, 3, 2003},  // DML写入
                                                                                                  // 升级
        {3, -1, 0, 1, 1, 2001}, {3, -1, 0, 1, 2, 2002}, {3, -1, 0, 1, 3, 2003}, {3, 1, 0, 1, 1, 1}, {3, 1, 0, 1, 2, 1},
        {3, 1, 0, 1, 3, 1}, {51, 1, 0, 1, 1, 2001}, {51, 1, 0, 1, 2, 2002}, {51, 1, 0, 1, 3, 2003},
        // 第一次降级
        {3, -1, 0, 1, 1, 1}, {3, -1, 0, 1, 2, 1}, {3, -1, 0, 1, 3, 1},
        // 降级失败回滚
        {51, -1, 0, 1, 1, 2001}, {3, 1, 0, 1, 3, 1}, {3, 1, 0, 1, 2, 1}, {3, 1, 0, 1, 1, 1},
        // 第二次降级
        {3, -1, 0, 1, 1, 1}, {3, -1, 0, 1, 2, 1}, {3, -1, 0, 1, 3, 1}, {51, -1, 0, 1, 1, 2001}, {51, -1, 0, 1, 2, 2002},
        {51, -1, 0, 1, 3, 2003}, {3, 1, 0, 1, 1, 2001}, {3, 1, 0, 1, 2, 2002}, {3, 1, 0, 1, 3, 2003}};
    EXPECT_EQ(ELEMENT_COUNT(expect2), records.size());
    for (int j = 0; j < records.size(); j++) {
        EXPECT_EQ(expect2[j][0], records[j][0]);
        EXPECT_EQ(expect2[j][1], records[j][1]);
        EXPECT_EQ(expect2[j][2], records[j][2]);
        EXPECT_EQ(expect2[j][3], records[j][3]);
        EXPECT_EQ(expect2[j][4], records[j][4]);
        EXPECT_EQ(expect2[j][5], records[j][5]);
    }

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 新增tbm表+新增规则+新增precedence+新增readwrite，代替有问题的表定义
TEST_F(ClientStExecUpgradeDatalogForNewTableAndNewRule, HotPatch_ReplaceTbmRuleWithNewTbmTable1)
{
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char *fileName = (char *)"client_st_upgrade_tbm";
    char *udfFileName = (char *)"client_st_upgrade_tbm2";
    char *defaultNsp = (char *)"public";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));

    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);

    char *deltaFileName = (char *)"client_st_upgrade_tbm_patch7";
    char *upgradeSoName = (char *)"client_st_upgrade_tbm_patch2";
    char *upgradeUdfFileName = (char *)"client_st_upgrade_tbm_patch7";
    char *rollbackSoName1 = (char *)"client_st_upgrade_tbm_rollback2";
    char *fileWithRuleName = (char *)"client_st_upgrade_tbm_rule";
    ExecPrepare4UpgradeWithUdf(fileWithRuleName, deltaFileName, upgradeUdfFileName, upgradeSoName);
    ExecPrepare4RollbackUpgradeWithUdf(fileWithRuleName, deltaFileName, upgradeUdfFileName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));

    // 触发回滚 第1次，预期失败
    const char *expectedResults[] = {
        "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC",
    };
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccessWithExpectedResults(10, expectedResults, ELEMENT_COUNT(expectedResults)));
    // 触发回滚 第2次，预期成功
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));

    int32_t records2[][5] = {{1, 0, 1, 1, 11}, {1, 0, 2, 2, 22}, {1, 0, 3, 3, 33}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records2, ELEMENT_COUNT(records2), true);
    EXPECT_EQ(GMERR_OK, ret);
    // 第二次升级，预期重做失败，触发回滚
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccessWithExpectedResults(10, expectedResults, ELEMENT_COUNT(expectedResults)));

    std::ifstream logFile("./tbmRollBackTest.log");
    std::vector<std::array<int, 6>> records;
    std::string content;

    while (std::getline(logFile, content)) {
        std::stringstream ss(content);
        std::array<int, 6> arr;
        ss >> arr[0] >> arr[1] >> arr[2] >> arr[3] >> arr[4] >> arr[5];
        records.push_back(arr);
    }
    int32_t expectData[][6] = {{4, 1, 0, 3, 3, 3}, {4, 1, 0, 1, 1, 1}, {4, 1, 0, 2, 2, 2},  // 普通DML
                                                                                            // 第1次升级
        {4, -1, 0, 3, 3, 3}, {51, 1, 0, 3, 3, 3}, {4, -1, 0, 1, 1, 1}, {51, 1, 0, 1, 1, 1}, {4, -1, 0, 2, 2, 2},
        {51, 1, 0, 2, 2, 2},
        // 第1次降级
        {51, -1, 0, 1, 1, 1}, {4, 1, 0, 1, 1, 1}, {51, -1, 0, 2, 2, 2}, {4, 1, 0, 2, 2, 2},
        // 第1次降级失败
        {51, -1, 0, 3, 3, 3},
        // 第1次降级失败回滚
        {4, -1, 0, 2, 2, 2}, {51, 1, 0, 2, 2, 2}, {4, -1, 0, 1, 1, 1}, {51, 1, 0, 1, 1, 1},
        // 第2次降级
        {51, -1, 0, 1, 1, 1}, {4, 1, 0, 1, 1, 1}, {51, -1, 0, 2, 2, 2}, {4, 1, 0, 2, 2, 2}, {51, -1, 0, 3, 3, 3},
        {4, 1, 0, 3, 3, 3},
        // 第二次DML
        {4, 1, 0, 3, 3, 33}, {4, 1, 0, 1, 1, 11}, {4, 1, 0, 2, 2, 22},
        // 第二次升级
        {4, -1, 0, 2, 2, 2}, {51, 1, 0, 2, 2, 2}, {4, -1, 0, 3, 3, 3}, {51, 1, 0, 3, 3, 3}, {4, -1, 0, 1, 1, 1},
        {51, 1, 0, 1, 1, 1}, {4, -1, 0, 3, 3, 33}, {51, 1, 0, 3, 33, 3}, {4, -1, 0, 1, 1, 11}, {51, 1, 0, 1, 11, 1},
        {4, -1, 0, 2, 2, 22},
        // 第二次升级失败点
        {51, 1, 0, 2, 22, 2},
        // 第二次升级回滚
        {4, 1, 0, 2, 2, 22}, {51, -1, 0, 3, 3, 3}, {4, 1, 0, 3, 3, 3}, {51, -1, 0, 1, 1, 1}, {4, 1, 0, 1, 1, 1},
        {51, -1, 0, 3, 33, 3}, {4, 1, 0, 3, 3, 33}, {51, -1, 0, 1, 11, 1}, {4, 1, 0, 1, 1, 11}, {51, -1, 0, 2, 2, 2},
        {4, 1, 0, 2, 2, 2}};
    for (int j = 0; j < records.size(); j++) {
        EXPECT_EQ(expectData[j][0], records[j][0]);
        EXPECT_EQ(expectData[j][1], records[j][1]);
        EXPECT_EQ(expectData[j][2], records[j][2]);
        EXPECT_EQ(expectData[j][3], records[j][3]);
        EXPECT_EQ(expectData[j][4], records[j][4]);
        EXPECT_EQ(expectData[j][5], records[j][5]);
    }

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 新增pubsub表/transient表/输入表，以及新增规则，不和其他表join
// transient表数据会被清空
// 降级失败，触发回滚，然后再次降级成功
TEST_F(ClientStExecUpgradeDatalogForNewTableAndNewRule, HotPatch_NewPubsubRuleWithNewPubsubTableAndNewRule1)
{
    system("rm -rf ./pubsubRollBackTest.log");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char *fileName = (char *)"client_st_upgrade_tbm";
    char *udfFileName = (char *)"client_st_upgrade_tbm";
    char *defaultNsp = (char *)"public";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));
    Status ret = GMERR_OK;
    int32_t records1[][5] = {{1, 0, 1, 1, 2001}, {1, 0, 1, 2, 2002}, {1, 0, 1, 3, 2003}};

    // 升级：创建新pubsub表
    char *deltaFileName = (char *)"client_st_upgrade_tbm_patch5";
    char *upgradeSoName = (char *)"client_st_upgrade_tbm_patch3";
    char *rollbackSoName1 = (char *)"client_st_upgrade_tbm_rollback";
    char *fileWithRuleName = (char *)"client_st_upgrade_tbm_rule";
    ExecPrepare4UpgradeWithUdf(fileWithRuleName, deltaFileName, upgradeSoName, upgradeSoName);
    ExecPrepare4RollbackUpgradeWithUdf(fileWithRuleName, deltaFileName, upgradeSoName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");

    // 创建pubsub表订阅
    GmcSubConfigT config;
    config.subsName = "subOutSimple1";
    config.configJson = R"(
    {
        "name": "subOutSimple1",
        "label_name": "nsp51.outD",
        "events": [{ "type": "insert", "msgTypes":["new object"]}],
        "is_reliable": true
    }
    )";

    auto callback = PubsubCBForUpgrade;
    int32_t receivedDataNum = 0;
    EXPECT_EQ(GMERR_OK, GmcSubscribe(g_stmt, &config, subConn, callback, &receivedDataNum));

    // 写输入表
    ret = InsertInpTableBatch(syncConn, syncStmt, "nsp51.inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t expect[1][5] = {0};
    // 检查 transient finish 表
    ScanAndCheckTable(syncStmt, "nsp51.midA", (int32_t *)expect, 0);

    // 触发回滚 第1次，预期失败
    const char *expectedResults[] = {
        "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC",
    };
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccessWithExpectedResults(10, expectedResults, ELEMENT_COUNT(expectedResults)));
    // 触发回滚 第2次，预期成功
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));

    std::ifstream logFile("./pubsubRollBackTest.log");
    std::vector<std::array<int, 6>> records;  //
    std::string content;

    while (std::getline(logFile, content)) {
        std::stringstream ss(content);
        std::array<int, 6> arr;
        ss >> arr[0] >> arr[1] >> arr[2] >> arr[3] >> arr[4] >> arr[5];
        records.push_back(arr);
    }
    int32_t expect2[][5] = {
        // DML
        {1, 5, 1, 3, 2003},
        {1, 5, 1, 1, 2001},
        {1, 5, 1, 2, 2002},
        // degrade
        {-1, 0, 1, 1, 2001},
        {-1, 0, 1, 2, 2002},
        {-1, 0, 1, 3, 2003},  // degrade failed
        // degrade rollback
        {1, 0, 1, 3, 2003},
        {1, 0, 1, 2, 2002},
        {1, 0, 1, 1, 2001},
        // degrade again
        {-1, 0, 1, 1, 2001},
        {-1, 0, 1, 2, 2002},
        {-1, 0, 1, 3, 2003},
    };
    EXPECT_EQ(ELEMENT_COUNT(expect2), records.size());
    for (int j = 0; j < records.size(); j++) {
        EXPECT_EQ(expect2[j][0], records[j][0]);
        EXPECT_EQ(expect2[j][2], records[j][2]);
        EXPECT_EQ(expect2[j][3], records[j][3]);
        EXPECT_EQ(expect2[j][4], records[j][4]);
    }

    EXPECT_EQ(GMERR_OK, GmcUnSubscribe(g_stmt, config.subsName));
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 新增pubsub表/中间表+新增规则+新增precedence+新增readwrite，代替有问题的规则
TEST_F(ClientStExecUpgradeDatalogForNewTableAndNewRule, HotPatch_NewPubsubRuleWithNewPubsubTableAndNewRule2)
{
    GmcConnT *datalogSubConn, *datalogSubConn2;
    auto &datalogSubConnName = "client st sub for datalog upgrade";
    auto &datalogSubConnName2 = "client st sub for datalog upgrade2";
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&datalogSubConn, datalogSubConnName));
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&datalogSubConn2, datalogSubConnName2));
    system("rm -rf ./pubsubRollBackTest.log");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char *fileName = (char *)"client_st_upgrade_pubsub_for_new";
    char *defaultNsp = (char *)"public";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));
    Status ret = GMERR_OK;
    int32_t records1[][5] = {{1, 0, 1, 1, 2001}, {1, 0, 1, 2, 2002}, {1, 0, 1, 3, 2003}};

    // 升级：创建新pubsub表
    char *deltaFileName = (char *)"client_st_upgrade_pubsub_for_new_patch";
    char *upgradeSoName = (char *)"client_st_upgrade_pubsub_for_new_patch";
    char *rollbackSoName1 = (char *)"client_st_upgrade_pubsub_for_new_rollback";
    char *fileWithRuleName = (char *)"client_st_upgrade_pubsub_for_new_rule";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");

    // 创建pubsub表订阅
    GmcSubConfigT config;
    config.subsName = "subOutSimple1";
    config.configJson = R"(
    {
        "name": "subOutSimple1",
        "label_name": "nsp1.outD",
        "events": [{ "type": "insert", "msgTypes":["new object"]}],
        "is_reliable": true
    }
    )";
    GmcSubConfigT config2;
    config2.subsName = "subOutSimple2";
    config2.configJson = R"(
    {
        "name": "subOutSimple2",
        "label_name": "nsp51.outD",
        "events": [{ "type": "insert", "msgTypes":["new object"]}],
        "is_reliable": true
    }
    )";

    g_gmdbTestDataCntForPubsub = 0;
    auto callback = PubsubCBForUpgradeForReturnError;
    int32_t expectReturnErrorNum = 9;
    EXPECT_EQ(GMERR_OK, GmcSubscribe(syncStmt, &config, datalogSubConn, callback, &expectReturnErrorNum));
    EXPECT_EQ(GMERR_OK, GmcSubscribe(syncStmt, &config2, datalogSubConn2, callback, &expectReturnErrorNum));

    // 写输入表
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t expect[1][5] = {0};
    // 检查 transient finish 表
    ScanAndCheckTable(syncStmt, "nsp51.midA", (int32_t *)expect, 0);

    // 触发回滚 第1次，预期失败
    const char *expectedResults[] = {
        "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC",
    };
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccessWithExpectedResults(10, expectedResults, ELEMENT_COUNT(expectedResults)));
    // // 触发回滚 第2次，预期成功
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));

    std::ifstream logFile("./pubsubRollBackTest.log");
    std::vector<std::array<int, 6>> records;  //
    std::string content;

    while (std::getline(logFile, content)) {
        std::stringstream ss(content);
        std::array<int, 6> arr;
        ss >> arr[0] >> arr[1] >> arr[2] >> arr[3] >> arr[4] >> arr[5];
        records.push_back(arr);
    }
    int32_t expect2[][5] = {
        // 升级后的普通DML
        {1, 0, 1, 1, 51},
        {1, 0, 1, 2, 51},
        {1, 0, 1, 3, 51},
        // 第一次降级
        {-1, 0, 1, 1, 51},
        {-1, 0, 1, 2, 51},
        {-1, 0, 1, 3, 51},
        {1, 0, 1, 1, 2001},
        {1, 0, 1, 2, 2002},
        // 构造第一次降级失败
        {1, 0, 1, 3, 2003},
        // 第一次降级回滚
        {-1, 0, 1, 3, 2003},
        {-1, 0, 1, 2, 2002},
        {-1, 0, 1, 1, 2001},
        {1, 0, 1, 3, 51},
        {1, 0, 1, 2, 51},
        {1, 0, 1, 1, 51},
        // 再次降级
        {-1, 0, 1, 1, 51},
        {-1, 0, 1, 2, 51},
        {-1, 0, 1, 3, 51},
        {1, 0, 1, 1, 2001},
        {1, 0, 1, 2, 2002},
        {1, 0, 1, 3, 2003},
    };
    EXPECT_EQ(ELEMENT_COUNT(expect2), records.size());
    for (int j = 0; j < records.size(); j++) {
        EXPECT_EQ(expect2[j][0], records[j][0]);
        EXPECT_EQ(expect2[j][2], records[j][2]);
        EXPECT_EQ(expect2[j][3], records[j][3]);
        EXPECT_EQ(expect2[j][4], records[j][4]);
    }

    EXPECT_EQ(GMERR_OK, GmcUnSubscribe(syncStmt, config2.subsName));
    EXPECT_EQ(GMERR_OK, GmcUnSubscribe(syncStmt, config.subsName));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(datalogSubConn));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(datalogSubConn2));
    DestroyConnectionAndStmt(syncConn, syncStmt);
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

// 新增中间表+新增规则+新增precedence+新增readwrite，代替有问题的规则(join错表)
TEST_F(ClientStExecUpgradeDatalogForNewTableAndNewRule, HotPatch_NewRuleWithNewIntermediateTableAndNewRule1)
{
    GmcConnT *datalogSubConn2;
    auto &datalogSubConnName2 = "client st sub for datalog upgrade2";
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&datalogSubConn2, datalogSubConnName2));
    system("rm -rf ./pubsubRollBackTest.log");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char *fileName = (char *)"client_st_upgrade_pubsub_for_new_intermediate";
    char *defaultNsp = (char *)"public";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));
    Status ret = GMERR_OK;
    int32_t records1[][5] = {{1, 0, 1, 1, 2001}, {1, 0, 1, 2, 2002}, {1, 0, 1, 3, 2003}};

    // 创建pubsub表订阅
    GmcSubConfigT config;
    config.subsName = "subOutSimple1";
    config.configJson = R"(
    {
        "name": "subOutSimple1",
        "label_name": "nsp1.outD",
        "events": [{ "type": "insert", "msgTypes":["new object"]}],
        "is_reliable": true
    }
    )";

    g_gmdbTestDataCntForPubsub = 0;
    auto callback = PubsubCBForUpgradeForReturnError;
    int32_t expectReturnErrorNum = 15;
    EXPECT_EQ(GMERR_OK, GmcSubscribe(syncStmt, &config, datalogSubConn2, callback, &expectReturnErrorNum));

    // 写输入表
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertInpTableBatch(syncConn, syncStmt, "nsp51.outB", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertInpTableBatch(syncConn, syncStmt, "nsp522.outB", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);

    // 升级：创建新pubsub表
    char *deltaFileName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_patch";
    char *upgradeSoName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_patch32";
    char *rollbackSoName1 = (char *)"client_st_upgrade_pubsub_for_new_intermediate_rollback32";
    char *fileWithRuleName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_rule";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");

    int32_t expect[1][5] = {0};
    // 检查 transient finish 表
    ScanAndCheckTable(syncStmt, "nsp522.middleA", (int32_t *)expect, 0);

    // 触发回滚 第1次，预期失败
    const char *expectedResults[] = {
        "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC",
    };
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccessWithExpectedResults(10, expectedResults, ELEMENT_COUNT(expectedResults)));
    ScanAndCheckTable(syncStmt, "nsp522.middleA", (int32_t *)expect, 0);
    // 触发回滚 第2次，预期成功
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));

    std::ifstream logFile("./pubsubRollBackTest.log");
    std::vector<std::array<int, 6>> records;  //
    std::string content;

    while (std::getline(logFile, content)) {
        std::stringstream ss(content);
        std::array<int, 6> arr;
        ss >> arr[0] >> arr[1] >> arr[2] >> arr[3] >> arr[4] >> arr[5];
        records.push_back(arr);
    }
    int32_t expect2[][5] = {{1, 0, 1, 1, 2001}, {1, 0, 1, 2, 2002}, {1, 0, 1, 3, 2003},  // 普通DML
                                                                                         // 升级
        {-1, 0, 1, 1, 2001}, {-1, 0, 1, 2, 2002}, {-1, 0, 1, 3, 2003}, {1, 0, 1, 1, 100}, {1, 0, 1, 2, 100},
        {1, 0, 1, 3, 100},
        // 降级
        {-1, 0, 1, 1, 100}, {-1, 0, 1, 2, 100}, {-1, 0, 1, 3, 100}, {1, 0, 1, 1, 2001}, {1, 0, 1, 2, 2002},
        // 降级 触发回滚数据
        {1, 0, 1, 3, 2003}, {-1, 0, 1, 3, 2003}, {-1, 0, 1, 2, 2002}, {-1, 0, 1, 1, 2001}, {1, 0, 1, 3, 100},
        {1, 0, 1, 2, 100}, {1, 0, 1, 1, 100},
        // 再次降级
        {-1, 0, 1, 1, 100}, {-1, 0, 1, 2, 100}, {-1, 0, 1, 3, 100}, {1, 0, 1, 1, 2001}, {1, 0, 1, 2, 2002},
        {1, 0, 1, 3, 2003}};
    EXPECT_EQ(ELEMENT_COUNT(expect2), records.size());
    for (int j = 0; j < records.size(); j++) {
        EXPECT_EQ(expect2[j][0], records[j][0]);
        EXPECT_EQ(expect2[j][2], records[j][2]);
        EXPECT_EQ(expect2[j][3], records[j][3]);
        EXPECT_EQ(expect2[j][4], records[j][4]);
    }

    EXPECT_EQ(GMERR_OK, GmcUnSubscribe(syncStmt, config.subsName));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(datalogSubConn2));
    DestroyConnectionAndStmt(syncConn, syncStmt);
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

// 新增中间表+新增规则+新增precedence+新增readwrite，代替有问题的规则和表(表定义有问题)
// 主键定义有问题，并且得是transient表
TEST_F(ClientStExecUpgradeDatalogForNewTableAndNewRule, HotPatch_NewRuleWithNewIntermediateTableAndNewRule2)
{
    GmcConnT *datalogSubConn2;
    auto &datalogSubConnName2 = "client st sub for datalog upgrade2";
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&datalogSubConn2, datalogSubConnName2));
    system("rm -rf ./pubsubRollBackTest.log");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char *fileName = (char *)"client_st_upgrade_pubsub_for_new_intermediate";
    char *defaultNsp = (char *)"public";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));
    Status ret = GMERR_OK;
    int32_t records1[][5] = {{1, 0, 1, 1, 2001}, {1, 0, 1, 2, 2002}, {1, 0, 1, 3, 2003}};

    // 创建pubsub表订阅
    GmcSubConfigT config;
    config.subsName = "subOutSimple1";
    config.configJson = R"(
    {
        "name": "subOutSimple1",
        "label_name": "nsp4.outD",
        "events": [{ "type": "insert", "msgTypes":["new object"]}],
        "is_reliable": true
    }
    )";

    auto callback = PubsubCBForUpgradeForSuccess;
    int32_t expectReturnErrorNum = 15;
    EXPECT_EQ(GMERR_OK, GmcSubscribe(syncStmt, &config, datalogSubConn2, callback, &expectReturnErrorNum));

    // 写输入表
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);

    // 升级
    char *deltaFileName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_patch2";
    char *upgradeSoName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_patch2";
    char *rollbackSoName1 = (char *)"client_st_upgrade_pubsub_for_new_intermediate_rollback2";
    char *fileWithRuleName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_rule";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");

    int32_t expect[1][5] = {0};
    // 检查 transient finish 表
    ScanAndCheckTable(syncStmt, "nsp4.middleA", (int32_t *)expect, 0);
    ScanAndCheckTable(syncStmt, "nsp4.outB", (int32_t *)expect, 0);

    // 触发回滚 第1次，预期成功
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));

    std::ifstream logFile("./pubsubRollBackTest.log");
    std::vector<std::array<int, 6>> records;  //
    std::string content;

    while (std::getline(logFile, content)) {
        std::stringstream ss(content);
        std::array<int, 6> arr;
        ss >> arr[0] >> arr[1] >> arr[2] >> arr[3] >> arr[4] >> arr[5];
        records.push_back(arr);
    }
    int32_t expect2[][5] = {
        // 普通DML,后续数据被合并掉，无法触发推送
        {1, 0, 1, 1, 2001},
        {1, 0, 1, 2, 2002},
        {1, 0, 1, 3, 2003},
    };
    EXPECT_EQ(ELEMENT_COUNT(expect2), records.size());
    for (int j = 0; j < records.size(); j++) {
        EXPECT_EQ(expect2[j][0], records[j][0]);
        EXPECT_EQ(expect2[j][2], records[j][2]);
        EXPECT_EQ(expect2[j][3], records[j][3]);
        EXPECT_EQ(expect2[j][4], records[j][4]);
    }

    EXPECT_EQ(GMERR_OK, GmcUnSubscribe(syncStmt, config.subsName));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(datalogSubConn2));
    DestroyConnectionAndStmt(syncConn, syncStmt);
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

void ExternalCallBackFuncForUpgrade(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    bool hasNew, hasOld, isNull, eof = false;
    int32_t a;
    uint32_t b;
    uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }
        if (info->eventType == GMC_SUB_EVENT_INSERT || info->eventType == GMC_SUB_EVENT_MERGE_INSERT) {
            cout << "====================== pubsub External Insert=========================" << endl;
            hasNew = true;
            hasOld = false;
        } else if (info->eventType == GMC_SUB_EVENT_UPDATE || info->eventType == GMC_SUB_EVENT_MERGE_UPDATE) {
            cout << "====================== pubsub External Update=========================" << endl;
            hasNew = true;
            hasOld = true;
        } else if (info->eventType == GMC_SUB_EVENT_MODIFY) {
            cout << "===================== pubsub External modify==========================" << endl;
            hasNew = true;
            hasOld = false;
        } else {
            cout << "====================== pubsub External Delete=========================" << endl;
            hasNew = false;
            hasOld = true;
        }
        if (hasNew) {
            received++;
            EXPECT_EQ(GMERR_OK, GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW));
            EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(a), &isNull));
            EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "b", &b, sizeof(b), &isNull));
        }
        if (hasOld) {
            received++;
            EXPECT_EQ(GMERR_OK, GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD));
            EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(a), &isNull));
            EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "b", &b, sizeof(b), &isNull));
        }

        FILE *fp = fopen("./pubsubExternalTest.log", "a+");
        printf("tuple: a: %d, b: %d, eventType: %d\n", a, b, info->eventType);
        (void)fprintf(fp, "%d %d %d\n", a, b, info->eventType);
        (void)fclose(fp);
    }
}

// 新增外部表，不于其他表join
TEST_F(ClientStExecUpgradeDatalogForNewTableAndNewRule, HotPatch_NewExternalTable)
{
    system("rm -rf pubsubExternalTest.log");
    GmcSubConfigT subConfigExt = {
        .subsName = (char *)"external",
        .configJson = R"(
            {
                "name": "external",
                "label_name": "External",
                "events":
                [
                    { "type": "merge insert", "msgTypes":["new object"]},
                    { "type": "insert", "msgTypes":["new object"]},
                    { "type": "merge update", "msgTypes":["new object", "old object"]},
                    { "type": "update", "msgTypes":["new object", "old object"]},
                    { "type": "delete", "msgTypes":["new object", "old object"]}
                ],
                "is_reliable": true
            }
        )",
    };
    GmcConnT *datalogSubConn2;
    auto &datalogSubConnName2 = "client st sub for datalog upgrade2";
    EXPECT_EQ(GMERR_OK, CreateSubConnection(&datalogSubConn2, datalogSubConnName2));
    system("rm -rf ./pubsubRollBackTest.log");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char *fileName = (char *)"client_st_upgrade_pubsub_for_new_intermediate";
    char *defaultNsp = (char *)"public";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));

    // 升级, 创建外部表
    char *deltaFileName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_patch3";
    char *upgradeSoName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_patch3";
    char *rollbackSoName1 = (char *)"client_st_upgrade_pubsub_for_new_intermediate_rollback3";
    char *fileWithRuleName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_rule";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");

    uint32_t received = 0;
    EXPECT_EQ(
        GMERR_OK, GmcSubscribe(syncStmt, &subConfigExt, datalogSubConn2, ExternalCallBackFuncForUpgrade, &received));

    int32_t record2[][5] = {{1, 0, 1, 1, 1}, {1, 0, 2, 1, 3}, {1, 0, 3, 2, 3}};
    EXPECT_EQ(
        GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "nsp4.A", (int32_t *)record2, ELEMENT_COUNT(record2), true));

    int32_t record3[][5] = {{1, 0, 3, 1, 2}, {1, 0, 4, 1, 2}};
    EXPECT_EQ(
        GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "nsp4.B", (int32_t *)record3, ELEMENT_COUNT(record3), true));

    // 触发回滚 第1次，预期成功
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));

    std::ifstream logFile("./pubsubExternalTest.log");
    std::vector<std::array<int, 6>> records;  //
    std::string content;

    while (std::getline(logFile, content)) {
        std::stringstream ss(content);
        std::array<int, 6> arr;
        ss >> arr[0] >> arr[1] >> arr[2] >> arr[3] >> arr[4] >> arr[5];
        records.push_back(arr);
    }
    int32_t expect2[][5] = {{2, 2, 7}, {1, 2, 7}, {1, 2, 1}, {2, 2, 1}

    };
    EXPECT_EQ(ELEMENT_COUNT(expect2), records.size());
    for (int j = 0; j < records.size(); j++) {
        EXPECT_EQ(expect2[j][0], records[j][0]);
        EXPECT_EQ(expect2[j][1], records[j][1]);
        EXPECT_EQ(expect2[j][2], records[j][2]);
    }

    EXPECT_EQ(GMERR_OK, GmcUnSubscribe(syncStmt, subConfigExt.subsName));
    EXPECT_EQ(GMERR_OK, GmcDisconnect(datalogSubConn2));
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "External"));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// block 1 和 redo off 新增表时，检查upgradeVersion
TEST_F(ClientStExecUpgradeDatalogForNewTableAndNewRule, CheckNewTableUpgradeVersion)
{
    char *fileName = (char *)"client_st_upgrade_pubsub_for_new_intermediate";
    char *defaultNsp = (char *)"public";
    int32_t upVerVal = 0;

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));

    // 升级1：先修改老表的upgradeVersion
    char *deltaFileName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_patch4";
    char *upgradeSoName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_patch4";
    char *rollbackSoName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_rollback4";
    char *fileWithRuleName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_rule";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));

    // 升级2：block 1  新增表
    deltaFileName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_patch5";
    upgradeSoName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_patch5";
    rollbackSoName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_rollback5";
    fileWithRuleName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_patch4_full";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));
    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"nsp4.A", &upVerVal), GMERR_OK);
    EXPECT_EQ(1, upVerVal);
    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"nsp4.B", &upVerVal), GMERR_OK);
    EXPECT_EQ(1, upVerVal);
    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"nsp4.C", &upVerVal), GMERR_OK);
    EXPECT_EQ(1, upVerVal);
    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"nsp4.D", &upVerVal), GMERR_OK);
    EXPECT_EQ(1, upVerVal);

    // 升级3：redo off 新增表
    deltaFileName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_patch6";
    upgradeSoName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_patch6";
    rollbackSoName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_rollback6";
    fileWithRuleName = (char *)"client_st_upgrade_pubsub_for_new_intermediate_patch5_full";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));
    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"nsp4.A1", &upVerVal), GMERR_OK);
    EXPECT_EQ(1, upVerVal);
    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"nsp4.B1", &upVerVal), GMERR_OK);
    EXPECT_EQ(1, upVerVal);

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

TEST_F(ClientStExecUpgradeDatalogForNewTableAndNewRule, ReplaceRule1)
{
    system("rm -rf ./tbmRollBackTest.log");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char *fileName = (char *)"client_st_upgrade_tbm1";
    char *udfFileName = (char *)"client_st_upgrade_tbm1";
    char *defaultNsp = (char *)"public";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));
    Status ret = GMERR_OK;
    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}};
    // 写输入表
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpB", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertInpTableBatch(syncConn, syncStmt, "nsp522.outB", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    char *deltaFileName = (char *)"client_st_upgrade_tbm1_patch1";
    char *upgradeSoName = (char *)"client_st_upgrade_tbm1_patch1";
    char *rollbackSoName1 = (char *)"client_st_upgrade_tbm1_rollback";
    char *fileWithRuleName = (char *)"client_st_upgrade_tbm1_rule";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));

    std::ifstream logFile("./tbmRollBackTest.log");
    std::vector<std::array<int, 6>> records;  //
    std::string content;

    while (std::getline(logFile, content)) {
        std::stringstream ss(content);
        std::array<int, 6> arr;
        ss >> arr[0] >> arr[1] >> arr[2] >> arr[3] >> arr[4] >> arr[5];
        records.push_back(arr);
    }
    int32_t expect2[][6] = {{1, 1, 0, 3, 3, 3}, {1, 1, 0, 1, 1, 1}, {1, 1, 0, 2, 2, 2}, {1, -1, 0, 3, 3, 3},
        {1, -1, 0, 2, 2, 2}, {1, -1, 0, 1, 1, 1}, {1, 1, 0, 1, 1, 1}, {1, 1, 0, 2, 2, 2}, {1, 1, 0, 3, 3, 3}};
    EXPECT_EQ(ELEMENT_COUNT(expect2), records.size());
    for (int j = 0; j < records.size(); j++) {
        EXPECT_EQ(expect2[j][0], records[j][0]);
        EXPECT_EQ(expect2[j][1], records[j][1]);
        EXPECT_EQ(expect2[j][2], records[j][2]);
        EXPECT_EQ(expect2[j][3], records[j][3]);
        EXPECT_EQ(expect2[j][4], records[j][4]);
        EXPECT_EQ(expect2[j][5], records[j][5]);
    }

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

#ifndef ASAN
TEST_F(ClientStExecUpgradeDatalogForNewTableAndNewRule, NewRuleForCheckTopoSort)
{
    system("rm -rf ./tbmRollBackTest.log");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char *fileName = (char *)"client_st_upgrade_for_check_topo_sort";
    char *udfFileName = (char *)"client_st_upgrade_for_check_topo_sort";
    char *defaultNsp = (char *)"public";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));
    Status ret = GMERR_OK;
    int32_t records1[][5] = {{1, 0, 1, 1, 1}};
    // 写输入表
    ret = InsertInpTableBatch(syncConn, syncStmt, "A1", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    char *deltaFileName = (char *)"client_st_upgrade_for_check_topo_sort_patch";
    char *upgradeSoName = (char *)"client_st_upgrade_for_check_topo_sort_patch";
    char *rollbackSoName1 = (char *)"client_st_upgrade_for_check_topo_sort_rollback";
    char *fileWithRuleName = (char *)"client_st_upgrade_for_check_topo_sort_rule";
    ExecPrepare4UpgradeWithUdf(fileWithRuleName, deltaFileName, udfFileName, upgradeSoName);
    ExecPrepare4RollbackUpgradeWithUdf(fileWithRuleName, deltaFileName, udfFileName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));

    std::ifstream logFile("./funcTest.log");
    std::vector<std::array<int, 3>> records;
    std::string content;

    while (std::getline(logFile, content)) {
        std::stringstream ss(content);
        std::array<int, 3> arr;
        ss >> arr[0] >> arr[1] >> arr[2];
        records.push_back(arr);
    }
    int32_t expect2[][3] = {
        {1, 1, 1},
        {2, 1, 1},
        {3, 1, 1},
        {4, 1, 1},
        {1, -1, 1},
        {2, -1, 1},
        {3, -1, 1},
        {4, -1, 1},
        {1, 1, 1},
        {2, 1, 1},
        {5, 1, 1},
        {3, 1, 1},
        {6, 1, 1},
        {4, 1, 1},
        {1, -1, 1},
        {2, -1, 1},
        {5, -1, 1},
        {3, -1, 1},
        {6, -1, 1},
        {4, -1, 1},
        {1, 1, 1},
        {2, 1, 1},
        {3, 1, 1},
        {4, 1, 1},
    };
    EXPECT_EQ(ELEMENT_COUNT(expect2), records.size());
    for (int j = 0; j < records.size(); j++) {
        EXPECT_EQ(expect2[j][0], records[j][0]);
        EXPECT_EQ(expect2[j][1], records[j][1]);
        EXPECT_EQ(expect2[j][2], records[j][2]);
    }

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}
#endif
