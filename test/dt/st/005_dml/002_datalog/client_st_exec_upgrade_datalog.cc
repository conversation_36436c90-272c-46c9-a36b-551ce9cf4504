/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Datalog ST for upgrade.
 * Author: GMDBv5 EE Team
 * Create: 2023-07-25
 */

#include <iostream>
#include <climits>
#include <string>
#include <pthread.h>
#include <fstream>
#include "client_common_st.h"
#include "client_st_exec_datalog.h"
#include "client_st_exec_datalog_common.h"

#include "st_common.h"
#define CONCURRENT_NUM 1
#define DEFAULT_NSP "ylog"

using namespace std;

const string tbmDataFileName = "./tbmData.log";
const string pubsubFileName = "./pubsubFileName.log";

class ClientStExecUpgradeDatalog : public ClientStExecDatalog {
public:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig("\"DBA=root:gmrule\" \"udfEnable=1\" \"isFastReadUncommitted=0\" "
                                "\"datalogUpgradeFetchSize=1\" \"workerHungThreshold=3,4,5\" ");
        st_clt_init();
        st_connect();
        CreateAndStartEpoll(&responseEpollThreadId, &responseEpollFd);
        CreateAndStartEpoll(&timeoutEpollThreadId, &timeoutEpollFd);
        EXPECT_EQ(GMERR_OK, GmcRegTimeoutEpollFunc(TimeoutEpollReg));
        // 此处心跳定时器与TimeOut定时器使用同一个Epoll，可设置为不同Epoll
        EXPECT_EQ(GMERR_OK, GmcRegHeartBeatEpollFunc(TimeoutEpollReg));
        if (IsEulerEnv()) {
            DbSleep(1000);
        } else {
            st_check_hpe_server_running();
        }
        printf("start response epoll and timeout epoll thread\n");
        printf("response epoll fd: %d, timeout epoll fd: %d\n", responseEpollFd, timeoutEpollFd);
    }

    static void *ConcurrentView4Rollback(void *arg)
    {
        // 用于测试并发场景
        int *signalPara = (int *)arg;
        const char *sysViewCmd1 = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
        const char *sysViewCmd2 = "gmsysview count";
        const char *matchStr1[] = {"finish"};
        const char *matchStr2[] = {"count"};
        while (*signalPara == 1) {
            usleep(1000);
            (void)StExecuteCommandWithMatch((char *)sysViewCmd1, matchStr1, ELEMENT_COUNT(matchStr1));
            (void)StExecuteCommandWithMatch((char *)sysViewCmd2, matchStr2, ELEMENT_COUNT(matchStr2));
        }
        printf(" =================== \n ConcurrentView4Rollback down \n =================== \n");
        return NULL;
    }

    void SetUp()
    {
        system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");
        signal = 1;
        pthread_create(&selfTid, NULL, ConcurrentView4Rollback, &signal);
        printf(" =================== \n SetUp \n =================== \n");
    }

    void TearDown()
    {
        system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
        signal = 0;
        pthread_join(selfTid, NULL);
        printf(" =================== \n TearDown \n =================== \n");
        Status ret = CheckRefCount();
        EXPECT_EQ(GMERR_OK, ret);
    }

protected:
    int signal = 0;
    pthread_t selfTid;
    const char *inpA = "inpA";
    const char *inpB = "inpB";
    const char *inpE = "inpE";
    const char *inpC = "inpC";
    const char *inpD = "inpD";
    const char *inpF = "inpF";
    const char *middleB = "middleB";
    const char *middleC = "middleC";
    const char *middleE = "middleE";
    const char *outC = "outC";
    const char *outD = "outD";
    const char *outF = "outF";

    const char *triggerA = "triggerA";
    const char *stateB = "stateB";

    GmcSubConfigT subConfig = {
        .subsName = "outF",
        .configJson = R"(
            {
                "name": "outF",
                "label_name": "outF",
                "events": [{ "type": "insert", "msgTypes":["new object"]}],
                "is_reliable": true
            }
        )",
    };

    GmcSubConfigT subConfigMiddleE = {
        .subsName = "middleE",
        .configJson = R"(
            {
                "name": "middleE",
                "label_name": "middleE",
                "events": [{ "type": "insert", "msgTypes":["new object"]}],
                "is_reliable": true
            }
        )",
    };

    GmcSubConfigT subConfigB = {
        .subsName = "outB",
        .configJson = R"(
            {
                "name": "outB",
                "label_name": "outB",
                "events":
                [
                    {"type": "insert", "msgTypes":["new object"]},
                    {"type": "update", "msgTypes":["new object", "old object"]}
                ],
                "is_reliable": true
            }
        )",
    };

    GmcSubConfigT subConfigC = {
        .subsName = "outC",
        .configJson = R"(
            {
                "name": "outC",
                "label_name": "outC",
                "events": [{ "type": "insert", "msgTypes":["new object"]}],
                "is_reliable": true
            }
        )",
    };

    GmcSubConfigT subConfigRes = {
        .subsName = "subRes",
        .configJson = R"(
            {
                "name": "subRes1",
                "label_name": "rsc1",
                "events": [{ "type": "insert", "msgTypes":["new object"]}],
                "is_reliable": true
            }
        )",
    };

    GmcSubConfigT subConfigExt = {
        .subsName = (char *)"outF_external",
        .configJson = R"(
            {
                "name": "outF_external",
                "label_name": "outF",
                "events":
                [
                    { "type": "merge insert", "msgTypes":["new object"]},
                    { "type": "insert", "msgTypes":["new object"]},
                    { "type": "merge update", "msgTypes":["new object", "old object"]},
                    { "type": "update", "msgTypes":["new object", "old object"]},
                    { "type": "delete", "msgTypes":["new object", "old object"]}
                ],
                "is_reliable": true
            }
        )",
    };

    void CreateExternalVertexLabel(GmcStmtT *syncStmt, const char *defaultNsp)
    {
        const char *testConfigJson = R"({"max_record_count":10000})";
        const char *testNormalLabelJson =
            R"([{
            "type":"record",
            "name":"External",
            "fields":
                [
                    {"name":"a", "type":"int32", "nullable":false},
                    {"name":"b", "type":"int32", "nullable":false},
                    {"name":"c", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"External",
                        "name":"External_K0",
                        "fields":["a"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
        EXPECT_EQ(GMERR_OK, GmcUseNamespace(syncStmt, defaultNsp));
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, testNormalLabelJson, testConfigJson));
    }

    void CreateExternalOutput5(GmcStmtT *syncStmt, const char *defaultNsp)
    {
        const char *testConfigJson = R"({"max_record_count":10000})";
        const char *testNormalLabelJson =
            R"([{
            "type":"record",
            "name":"output5",
            "fields":
                [
                    {"name":"a", "type":"int64", "nullable":false},
                    {"name":"b", "type":"int64", "nullable":false},
                    {"name":"c", "type":"int64", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"output5",
                        "name":"output5_K0",
                        "fields":["a"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
        EXPECT_EQ(GMERR_OK, GmcUseNamespace(syncStmt, defaultNsp));
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, testNormalLabelJson, testConfigJson));
    }

    void CreateExternalVertexLabelAllFieldPK(GmcStmtT *syncStmt, const char *nspName)
    {
        const char *testConfigJson = R"({"max_record_count":10000})";
        const char *testNormalLabelJson =
            R"([{
            "type":"record",
            "name":"External",
            "fields":
                [
                    {"name":"a", "type":"int32", "nullable":false},
                    {"name":"b", "type":"int32", "nullable":false},
                    {"name":"c", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"External",
                        "name":"External_K0",
                        "fields":["a", "b", "c"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
        EXPECT_EQ(GMERR_OK, GmcUseNamespace(syncStmt, nspName));
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, testNormalLabelJson, testConfigJson));
    }

    void CreateExternalCopyVertexLabel(GmcStmtT *syncStmt, const char *nspName)
    {
        const char *testConfigJson = R"({"max_record_count":10000})";
        const char *testNormalLabelJson =
            R"([{
            "type":"record",
            "name":"External_Copy",
            "fields":
                [
                    {"name":"a", "type":"int32", "nullable":false},
                    {"name":"b", "type":"int32", "nullable":false},
                    {"name":"c", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"External_Copy",
                        "name":"External_K0",
                        "fields":["a"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
        EXPECT_EQ(GMERR_OK, GmcUseNamespace(syncStmt, nspName));
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, testNormalLabelJson, testConfigJson));
    }

    void CreateExternalCopyVertexLabelAllFieldPK(GmcStmtT *syncStmt, const char *nspName)
    {
        const char *testConfigJson = R"({"max_record_count":10000})";
        const char *testNormalLabelJson =
            R"([{
            "type":"record",
            "name":"External_Copy",
            "fields":
                [
                    {"name":"a", "type":"int32", "nullable":false},
                    {"name":"b", "type":"int32", "nullable":false},
                    {"name":"c", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"External_Copy",
                        "name":"External_K0",
                        "fields":["a", "b", "c"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
        EXPECT_EQ(GMERR_OK, GmcUseNamespace(syncStmt, nspName));
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(syncStmt, testNormalLabelJson, testConfigJson));
    }

    void VerifyPushData(string fileName, int32_t expect[][5], int32_t expectNum)
    {
        std::ifstream logFile(fileName.c_str());
        std::vector<std::array<int, 5>> records;
        std::string content;

        while (std::getline(logFile, content)) {
            std::stringstream ss(content);
            std::array<int, 5> arr;
            ss >> arr[0] >> arr[1] >> arr[2] >> arr[3] >> arr[4];
            records.push_back(arr);
        }

        EXPECT_EQ((int)records.size(), expectNum);

        EXPECT_NE((int)records.size(), 0);
        uint32_t i = 0;
        for (auto bit = begin(records); bit < end(records); ++bit, ++i) {
            for (int k = 0; k < 5; ++k)
                EXPECT_EQ((*bit)[k], expect[i][k]);
        }
    }
};
INSTANTIATE_TEST_CASE_P(ExecUpgradeDatalog, ClientStExecUpgradeDatalog,
    ::testing::Values(
        StClientTestData{.isAsync = false, .intValue = 0}, StClientTestData{.isAsync = true, .intValue = 1}));

void *UpgradeDmlCanNotConcurrentWithUpgradeThreadCallBack(void *err)
{
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    char *defaultNsp = (char *)"ylog";
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    Status ret = GmcUseNamespace(syncStmt, defaultNsp);
    if (ret != GMERR_OK) {
        *(Status *)err = ret;
        printf("use nsp failed;ret=%d\n", ret);
        return err;
    }

    // 1. insert to inpA
    TableFourFixedFieldV tuplesA[100];
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];

        tuple->dtlReservedCount = 1;
        tuple->upgradeVersion = i;
        tuple->a = i;
        tuple->b = i;
        tuple->c = i;
        tuple->d = i;
    }

    ret = InsertBatchTableWithStruct(syncConn, syncStmt, "inpA", tuplesA, ELEMENT_COUNT(tuplesA), false);
    DestroyConnectionAndStmt(syncConn, syncStmt);
    *(Status *)err = ret;
    return err;
}

void *NormalDMLInABatch(void *err)
{
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    char *defaultNsp = (char *)"ylog";
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    Status ret = GmcUseNamespace(syncStmt, defaultNsp);
    if (ret != GMERR_OK) {
        *(Status *)err = ret;
        printf("use nsp failed;ret=%d\n", ret);
        return err;
    }

    // 1. insert to inpA
    TableFourFixedFieldV tuplesA[100];
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];

        tuple->dtlReservedCount = 1;
        tuple->upgradeVersion = i;
        tuple->a = i;
        tuple->b = i;
        tuple->c = i;
        tuple->d = i;
    }

    ret = InsertBatchTableWithStruct(syncConn, syncStmt, "inpA", tuplesA, ELEMENT_COUNT(tuplesA), false);
    DestroyConnectionAndStmt(syncConn, syncStmt);
    *(Status *)err = ret;
    return err;
}

void PubsubCallBack(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    // cout << "====================== resource pubsub =========================" << endl;
    EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
    EXPECT_EQ(1u, info->labelCount);
    uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

    GmcRespT *resp;
    EXPECT_EQ(GMERR_OK, GmcCreateResp(stmt, &resp));
    EXPECT_EQ(GMERR_OK, GmcSetRespMode(resp, GMC_RESP_SEND_FAILED_INDEX));
    for (bool eof;; ++received) {
        auto ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }

        ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
        EXPECT_EQ(GMERR_OK, ret);

        bool isNull;
        int32_t a, b, dtlReservedCount, upgradeVersion;
        uint32_t cSize;
        char *c;
        uint32_t dSize;
        char *d;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(a), &isNull));
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "b", &b, sizeof(b), &isNull));
        EXPECT_EQ(GMERR_OK,
            GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(dtlReservedCount), &isNull));
        EXPECT_EQ(GMERR_OK,
            GmcGetVertexPropertyByName(stmt, "upgradeVersion", &upgradeVersion, sizeof(upgradeVersion), &isNull));
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "c", &cSize));
        c = (char *)DbDynMemCtxAlloc(stmt->memCtx, cSize);
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "c", c, cSize, &isNull));

        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(stmt, "d", &dSize));
        d = (char *)DbDynMemCtxAlloc(stmt->memCtx, dSize);
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "d", d, dSize, &isNull));

        // printf("pubsub callback a: %d , b: %d , c: %s , d:%s , dtlReservedCount: %d , upgradeVersion: %d\n", a, b, c,
        // d,
        //     dtlReservedCount, upgradeVersion);
    }

    uint16_t failedDataNum = 0;
    uint16_t failedIndexes[GMC_SUB_BATCH_MAX] = {0};
    EXPECT_EQ(GMERR_OK, GmcSetSubFailedIndex(resp, failedDataNum, failedIndexes));

    EXPECT_EQ(GMERR_OK, GmcSendResp(stmt, resp));
    EXPECT_EQ(GMERR_OK, GmcDestroyResp(stmt, resp));
}

void *PatchInfoView(void *arg)
{
    int i = 0;
    while (i < 50) {
        system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
        i++;
    }
    return NULL;
}

// 新增表和修改规则:
// 新增输入表，修改规则 移植为以下用例
// TEST_F(ClientStExecRollbackUpgradeDatalog, AddTableAndAlterRule4RollbackUpgrade)

// 升级多条规则，相互独立的topo图
TEST_P(ClientStExecUpgradeDatalog, AlterTableAndRule1)
{
    pthread_t tid;
    Status ret = pthread_create(&tid, NULL, PatchInfoView, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char *fileName = (char *)"client_st_upgrade_two_seperate_rule_topo_multi";
    EXPECT_EQ(GMERR_OK, execCmd(fileName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateDtlConnectionAndStmt(&syncConn, &syncStmt, GetParam().isAsync);

    int32_t records1[][4] = {
        {+1, 0, 1, 2}, {+1, 0, 2, 3}, {+1, 0, 3, 4}, {+3, 0, 4, 5}, {+2, 0, 5, 6}, {+2, 0, 6, 7}, {+1, 0, 7, 8}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    ScanAndCheckTable(g_stmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1));

    int32_t records2[][5] = {{+1, 0, 2, 3, 4}, {+1, 0, 3, 4, 5}, {+1, 0, 4, 5, 6}, {+3, 0, 5, 6, 7}, {+2, 0, 6, 7, 8},
        {+2, 0, 7, 8, 9}, {+1, 0, 8, 9, 10}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpB", (int32_t *)records2, ELEMENT_COUNT(records2), true);
    EXPECT_EQ(GMERR_OK, ret);
    ScanAndCheckTable(g_stmt, "inpB", (int32_t *)records2, ELEMENT_COUNT(records2));

    int32_t records3[][6] = {{+1, 0, 1, 2, 3, 1}, {+1, 0, 2, 3, 4, 1}, {+1, 0, 3, 4, 5, 1}, {+1, 0, 4, 5, 6, 1},
        {+1, 0, 5, 6, 7, 1}, {+1, 0, 6, 7, 8, 1}, {+1, 0, 7, 8, 9, 1}};
    ScanAndCheckTable(g_stmt, "outTbl", (int32_t *)records3, ELEMENT_COUNT(records3));

    int32_t records4[][5] = {{+1, 0, 1, 1, 1}, {+1, 0, 1, 1, 2}, {+1, 0, 2, 2, 2}, {+3, 0, 2, 2, 3}, {+2, 0, 3, 3, 3},
        {+2, 0, 3, 3, 4}, {+1, 0, 4, 4, 4}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpC", (int32_t *)records4, ELEMENT_COUNT(records4), true);
    EXPECT_EQ(GMERR_OK, ret);
    ScanAndCheckTable(g_stmt, "inpC", (int32_t *)records4, ELEMENT_COUNT(records4));

    int32_t records5[][5] = {{+2, 0, 1, 1, 1}, {+2, 0, 2, 2, 1}, {+2, 0, 3, 3, 1}, {+1, 0, 4, 4, 1}};
    ScanAndCheckTable(g_stmt, "inpD", (int32_t *)records5, ELEMENT_COUNT(records5));
    int32_t records6[][5] = {{+1, 0, 1, 1, 1}, {+1, 0, 1, 1, 2}, {+1, 0, 2, 2, 2}, {+1, 0, 2, 2, 3}, {+1, 0, 3, 3, 3},
        {+1, 0, 3, 3, 4}, {+1, 0, 4, 4, 4}};
    ScanAndCheckTable(g_stmt, "inpE", (int32_t *)records6, ELEMENT_COUNT(records6));

    char *deltaFileName = (char *)"client_st_upgrade_two_seperate_rule_topo_multi_patch";
    char *upgradeSoName = (char *)"client_st_upgrade_two_seperate_rule_topo_multi_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_two_seperate_rule_topo_multi_rule";
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName));
    usleep(100 * 1000);  // 等待升级流程重刷数据完成
    uint32_t maxLen = 128;
    const char *expectedResults[] = {"VERSION: [v1.0.0]->[v2.1.1]", "UPGRADE_VERSION: 1", "PATCH_STATE: SUCCESS"};
    char command[maxLen] = {};
    (void)snprintf_s(command, maxLen, maxLen - 1, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    ret = StExecuteCommandWithMatch(command, expectedResults, ELEMENT_COUNT(expectedResults));
    EXPECT_EQ(ret, GMERR_OK);
    int32_t records7[][6] = {{+1, 1, 1, 2, 3, 4}, {+1, 1, 2, 3, 4, 5}, {+1, 1, 3, 4, 5, 6}, {+1, 1, 4, 5, 6, 7},
        {+1, 1, 5, 6, 7, 8}, {+1, 1, 6, 7, 8, 9}, {+1, 1, 7, 8, 9, 10}};
    ScanAndCheckTable(g_stmt, "outTbl", (int32_t *)records7, ELEMENT_COUNT(records7));
    system("gmsysview record outTbl > outTbl.txt");
    int32_t records8[][5] = {{+1, 1, 1, 1, 1}, {+1, 1, 1, 1, 2}, {+1, 1, 2, 2, 2}, {+1, 1, 2, 2, 3}, {+1, 1, 3, 3, 3},
        {+1, 1, 3, 3, 4}, {+1, 1, 4, 4, 4}};
    ScanAndCheckTable(g_stmt, "inpD", (int32_t *)records8, ELEMENT_COUNT(records8));
    int32_t records9[][5] = {{+1, 1, 1, 1, 1}, {+1, 1, 1, 1, 2}, {+1, 1, 2, 2, 2}, {+1, 1, 2, 2, 3}, {+1, 1, 3, 3, 3},
        {+1, 1, 3, 3, 4}, {+1, 1, 4, 4, 4}};
    ScanAndCheckTable(g_stmt, "inpE", (int32_t *)records9, ELEMENT_COUNT(records9));

    char *rbUpgradSoName = (char *)"client_st_upgrade_two_seperate_rule_topo_multi_rollback";
    EXPECT_EQ(GMERR_OK, execCmd4RollbackUpgrade(fileWithRuleName, deltaFileName, rbUpgradSoName));
    usleep(100 * 1000);  // 等待卸载流程重刷数据完成

    ScanAndCheckTable(g_stmt, "inpC", (int32_t *)records4, ELEMENT_COUNT(records4));
    ScanAndCheckTable(g_stmt, "inpD", (int32_t *)records5, ELEMENT_COUNT(records5));
    ScanAndCheckTable(g_stmt, "inpE", (int32_t *)records6, ELEMENT_COUNT(records6));
    ScanAndCheckTable(g_stmt, "outTbl", (int32_t *)records3, ELEMENT_COUNT(records3));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyDtlConnectionAndStmt(syncConn, syncStmt, GetParam().isAsync);
    pthread_join(tid, NULL);
}

// 新增udf，规则中有agg
TEST_F(ClientStExecUpgradeDatalog, AddUdf)
{
    char *fileName = (char *)"client_st_upgrade_add_udf";
    char *udfFileName = (char *)"client_st_upgrade_add_udf";
    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));
    char *deltaFileName = (char *)"client_st_upgrade_add_udf_patch";
    char *deltaOutputFileName = (char *)"client_st_upgrade_add_udf_patch_full";
    char *newUdfFileName = (char *)"client_st_upgrade_add_udf_upgrade";
    char *upgradeSoName = (char *)"client_st_upgrade_add_udf_patch";
    char *rollbackSoName = (char *)"client_st_upgrade_add_udf_rollback";
    char *fileWithRuleName = (char *)"client_st_upgrade_add_udf_rule";
    ExecPrepare4RollbackUpgradeWithOutputFileAndUdf(
        fileWithRuleName, deltaFileName, deltaOutputFileName, newUdfFileName, upgradeSoName, rollbackSoName);
    CheckMemSizeForMultiLoadHotPatch(defaultNsp, fileName, 10, 2, upgradeSoName, rollbackSoName);

    int32_t records1[][5] = {{1, 0, 1, 2, 1}, {1, 0, 1, 2, 3}, {-1, 0, 1, 2, 2}, {3, 0, 2, 2, 2}, {2, 0, 2, 2, 1},
        {-2, 0, 2, 2, 3}, {1, 0, 3, 3, 1}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    ScanAndCheckTable(syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1));
    system("gmsysview record out -ns ylog");
    int32_t records2[][6] = {{+1, 0, 3, 1, 1, 2}, {+1, 0, 3, 1, 2, 2}, {+1, 0, 1, 1, 3, 3}};
    ScanAndCheckTable(syncStmt, "inpB", (int32_t *)records2, ELEMENT_COUNT(records2));
    ScanAndCheckTable(syncStmt, "out", (int32_t *)records2, ELEMENT_COUNT(records2));

    pthread_t tid;
    ret = pthread_create(&tid, NULL, PatchInfoView, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    EXPECT_NE(GMERR_OK, execUpgrade(upgradeSoName, "public"));
    usleep(100 * 1000);  // 等待升级流程重刷数据完成
    uint32_t maxLen = 128;
    const char *expectedResults[] = {"VERSION: [v2.1.0]->[v4.0.1]", "UPGRADE_VERSION: 1", "PATCH_STATE: SUCCESS"};
    char command[maxLen] = {};
    (void)snprintf_s(command, maxLen, maxLen - 1, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    ret = StExecuteCommandWithMatch(command, expectedResults, ELEMENT_COUNT(expectedResults));
    EXPECT_EQ(ret, GMERR_OK);
    int32_t records3[][6] = {{+1, 1, 3, 1, 1, 2}, {+1, 1, 3, 1, 2, 2}, {+1, 1, 1, 1, 3, 3}};
    ScanAndCheckTable(syncStmt, "inpB", (int32_t *)records3, ELEMENT_COUNT(records3));
    int32_t records4[][6] = {{+1, 1, 3, 1, 1, 2}};
    ScanAndCheckTable(syncStmt, "out", (int32_t *)records4, ELEMENT_COUNT(records4));

    int32_t records5[][5] = {
        {-1, 0, 1, 2, 5}, {+1, 0, 4, 5, 4}, {+2, 0, 4, 5, 6}, {+1, 0, 4, 5, 1}, {-2, 0, 4, 5, 2}, {+1, 0, 4, 5, 3}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records5, ELEMENT_COUNT(records5), true);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t records6[][6] = {{+1, 1, 5, 1, 1, 2}, {+1, 1, 6, 1, 4, 5}};
    ScanAndCheckTable(syncStmt, "out", (int32_t *)records6, ELEMENT_COUNT(records6));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
    pthread_join(tid, NULL);
}

// 修改udf函数体实现:
TEST_F(ClientStExecUpgradeDatalog, AlterUdfImpl)
{
    pthread_t tid;
    Status ret = pthread_create(&tid, NULL, PatchInfoView, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char *fileName = (char *)"client_st_upgrade_alter_udf_impl";
    char *udfFileName = (char *)"client_st_upgrade_alter_udf_impl";
    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));

    int32_t records1[][4] = {{+1, 0, 1, 1}, {+1, 0, 1, 2}, {+1, 0, 2, 1}, {+3, 0, 2, 3}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t expectRecords1[][4] = {{+1, 0, 1, 1}, {+1, 0, 1, 2}, {+1, 0, 2, 1}, {+3, 0, 2, 3}};
    ScanAndCheckTable(syncStmt, "inpA", (int32_t *)expectRecords1, ELEMENT_COUNT(expectRecords1));
    int32_t expectRecordsOut2[][4] = {{+1, 0, 1, 2}, {+1, 0, 2, 3}};
    ScanAndCheckTable(syncStmt, "out2", (int32_t *)expectRecordsOut2, ELEMENT_COUNT(expectRecordsOut2));

    char *deltaFileName = (char *)"client_st_upgrade_alter_udf_impl_patch";
    char *newUdfFileName = (char *)"client_st_upgrade_alter_udf_impl_upgrade";
    char *upgradeSoName = (char *)"client_st_upgrade_alter_udf_impl_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_alter_udf_impl_rule";
    EXPECT_EQ(
        GMERR_OK, execCmd4UpgradeWithUdf(fileWithRuleName, deltaFileName, newUdfFileName, upgradeSoName, defaultNsp));

    usleep(100 * 1000);  // 等待升级流程重刷数据完成
    int32_t expectRecordsOut4[][4] = {{+1, 1, 2, 1}};
    ScanAndCheckTable(syncStmt, "out2", (int32_t *)expectRecordsOut4, ELEMENT_COUNT(expectRecordsOut4));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
    pthread_join(tid, NULL);
}

/*
 * ######################### 聚合算子升级异常场景 ###############################
    异常1：修改左表，影响Agg出参数量。
    异常2：修改右表，影响Agg入参数量。
    异常3：修改右表，导致Agg出参与右表字段重合。
    异常4：修改右表，改变Group-By字段
    异常5：修改右表，影响Agg实际入参
    异常6：修改access_delta表，但Agg无修改
    异常7：修改access_current表，但Agg无修改
    正常8：修改左表、右表，但Agg函数实际无变化

    原So定义：
    %version v1.0.0

    %table inpA(a: int4, b: int4, c: int4)
    %table midB(a: int4, b: int4, c: int4)

    %table midC(a: int4, b: int4, c: int4)
    %table outD(a: int4, b: int4, c: int4)
    %aggregate min(b: int4 -> c: int4){
        ordered
    }

    midB(a, b, c) :- inpA(a, b, c).
    midC(a, b, min) :- midB(a, b, c) GROUP-BY (a, b) min(c, min).
    outD(a, b, min) :- midC(a, b, min).
*/

/*
 * ############################################ 修改聚合函数输出表：异常1 修改UDF定义 ###############################
    %version v1.0.0 -> v2.0.0

    %alter table midC(a:int4, b:int4, c:int4, d:int4)
    %alter aggregate min(b: int4 -> c: int4, d: int4){
        ordered
    }

    %alter rule r1 midC(a, b, min, min2) :- midB(a, b, c) GROUP-BY (a, b) min(c, min, min2).
    %alter rule r2 outD(a, b, min) :- midC(a, b, min, -).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeAlterAggException1)
{
    const char *fileName = (char *)"client_st_upgrade_alter_table_agg_add_field";
    const char *deltaFileName = (char *)"client_st_upgrade_alter_table_agg_add_field_upgrade_exception";
    const char *upgradeSoName = (char *)"client_st_upgrade_alter_table_agg_add_field_patch";
    const char *fileWithRuleName = (char *)"client_st_upgrade_alter_table_agg_add_field_rule";

    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    // 加载原so
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    // 编译异常补丁
    EXPECT_NE(GMERR_OK, ExecPrepare4UpgradeWithRet(fileWithRuleName, deltaFileName, upgradeSoName));

    // 卸载原so
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

/*
 * ############################################ 修改聚合函数输出表：异常2 修改UDF入参 ###############################
    %version v1.0.0 -> v2.0.0

    %alter table midB(a:int4, b:int4, c:int4, d:int4)
    %alter table midC(a:int4, b:int4, c:int4, d:int4)

    midB(a, b, c, c) :- inpA(a, b, c).
    %alter rule r1 midC(a, b, min, 10) :- midB(a, b, -, c) GROUP-BY (a, b) min(c, min).
    %alter rule r2 outD(a, b, min) :- midC(a, b, min, -).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeAlterAggException2)
{
    const char *fileName = (char *)"client_st_upgrade_alter_table_agg_add_field";
    const char *deltaFileName = (char *)"client_st_upgrade_alter_table_agg_add_field_upgrade_exception2";
    const char *upgradeSoName = (char *)"client_st_upgrade_alter_table_agg_add_field_patch";
    const char *fileWithRuleName = (char *)"client_st_upgrade_alter_table_agg_add_field_rule";

    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    // 加载原so
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    // 编译异常补丁
    EXPECT_NE(GMERR_OK, ExecPrepare4UpgradeWithRet(fileWithRuleName, deltaFileName, upgradeSoName));

    // 卸载原so
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

/*
 * ######################### 修改聚合函数输出表：异常3 输入表新增字段与UDF出参重叠 ###############################
    %version v1.0.0 -> v2.0.0

    %alter table midB(a:int4, b:int4, c:int4, d:int4)
    %alter table midC(a:int4, b:int4, c:int4, d:int4)

    midB(a, b, c, c) :- inpA(a, b, c).
    %alter rule r1 midC(a, b, min, 10) :- midB(a, b, min, c) GROUP-BY (a, b) min(c, min).
    %alter rule r2 outD(a, b, min) :- midC(a, b, min, -).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeAlterAggException3)
{
    const char *fileName = (char *)"client_st_upgrade_alter_table_agg_add_field";
    const char *deltaFileName = (char *)"client_st_upgrade_alter_table_agg_add_field_upgrade_exception3";
    const char *upgradeSoName = (char *)"client_st_upgrade_alter_table_agg_add_field_patch";
    const char *fileWithRuleName = (char *)"client_st_upgrade_alter_table_agg_add_field_rule";

    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    // 加载原so
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    // 编译异常补丁
    EXPECT_NE(GMERR_OK, ExecPrepare4UpgradeWithRet(fileWithRuleName, deltaFileName, upgradeSoName));

    // 卸载原so
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

/*
 * ######################### 修改聚合函数输出表：异常4 Group-By字段改变 ###############################
    %version v1.0.0 -> v2.0.0

    %alter table midB(a:int4, b:int4, c:int4, d:int4)
    %alter table midC(a:int4, b:int4, c:int4, d:int4)

    midB(a, b, c, c) :- inpA(a, b, c).
    %alter rule r1 midC(a, b, m, 10) :- midB(a, b, -, c) GROUP-BY (a, b) min(c, m).
    %alter rule r2 outD(a, b, m) :- midC(a, b, m, -).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeAlterAggException4)
{
    const char *fileName = (char *)"client_st_upgrade_alter_table_agg_add_field";
    const char *deltaFileName = (char *)"client_st_upgrade_alter_table_agg_add_field_upgrade_exception4";
    const char *upgradeSoName = (char *)"client_st_upgrade_alter_table_agg_add_field_patch";
    const char *fileWithRuleName = (char *)"client_st_upgrade_alter_table_agg_add_field_rule";

    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    // 加载原so
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    // 编译异常补丁
    EXPECT_NE(GMERR_OK, ExecPrepare4UpgradeWithRet(fileWithRuleName, deltaFileName, upgradeSoName));

    // 卸载原so
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

/*
 * ######################### 修改聚合函数输出表：异常5 修改右表，影响Agg实际入参 ###############################
    %version v1.0.0 -> v2.0.0

    %alter table midB(a:int4, b:int4, c:int4, d:int4)
    %alter table midC(a:int4, b:int4, c:int4, d:int4)

    %alter rule r0 midB(a, b, c, c) :- inpA(a, b, c).
    %alter rule r1 midC(a, b, m, 10) :- midB(a, b, -, c) GROUP-BY (a,b) min(c, m).
    %alter rule r2 outD(a, b, m) :- midC(a, b, m, -).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeAlterAggException5)
{
    const char *fileName = (char *)"client_st_upgrade_alter_table_agg_add_field";
    const char *deltaFileName = (char *)"client_st_upgrade_alter_table_agg_add_field_upgrade_exception5";
    const char *upgradeSoName = (char *)"client_st_upgrade_alter_table_agg_add_field_patch";
    const char *fileWithRuleName = (char *)"client_st_upgrade_alter_table_agg_add_field_rule";

    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    // 加载原so
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    // 编译异常补丁
    EXPECT_NE(GMERR_OK, ExecPrepare4UpgradeWithRet(fileWithRuleName, deltaFileName, upgradeSoName));

    // 卸载原so
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

/*
 * ######################### 修改聚合函数输出表：正常8 修改左表、右表，但Agg函数实际无变化 ########################
    %version v1.0.0 -> v2.0.0

    %alter table midB(a:int4, b:int4, c:int4, d:int4)
    %alter table midC(a:int4, b:int4, c:int4, d:int4)

    %alter rule r0 midB(a, b, c, c) :- inpA(a, b, c).
    %alter rule r1 midC(a, b, m, 10) :- midB(a, b, c, -) GROUP-BY (a,b) min(c, m).
    %alter rule r2 outD(a, b, m) :- midC(a, b, m, -).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeAlterAggException8)
{
    const char *fileName = (char *)"client_st_upgrade_alter_table_agg_add_field";
    const char *deltaFileName = (char *)"client_st_upgrade_alter_table_agg_add_field_upgrade_normal8";
    const char *upgradeSoName = (char *)"client_st_upgrade_alter_table_agg_add_field_patch";
    const char *fileWithRuleName = (char *)"client_st_upgrade_alter_table_agg_add_field_rule";

    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    // 加载原so
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    // 编译异常补丁
    EXPECT_NE(GMERR_OK, ExecPrepare4UpgradeWithRet(fileWithRuleName, deltaFileName, upgradeSoName));

    // 卸载原so
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

/*
 * ######################### 状态机算子升级异常场景 ###############################
    异常1：修改右表，影响Transfer入参数量。
    异常2：修改左表，影响Transfer出参数量。
    异常3：修改右表，导致Transfer出参与右表字段重合。
    异常4：修改右表，影响Transfer实际入参
    异常5：修改access_delta表，但transfer无修改
    异常6：修改access_current表，但transfer无修改

    原So定义：
    %version v1.0.0

    %table inpA(a:int4 , b:int4, c:int4, d:int4) { index(0(a, b)), transient(tuple)}
    %table state(a:int4 , b:int4, p:int4, q:int4) { index(0(a, b)), state }
    %table trigger(a:int4 , b:int4, c:int4, d:int4) { index(0(a, b)), transient(tuple)}
    %function transfer(c:int4, d:int4 -> e:int4, f:int4) { state_transfer }
    %table out(a:int4 , b:int4, c:int4, d:int4) {index(0(a))}

    trigger(a, b, c, d) :- inpA(a, b, c, d)
    state(a, b, e, f) :- trigger(a, b, c, d), transfer(c, d, e, f).
    out(a, b, c, d) :- state(a, b, c, d).
*/

/*
 * ######################### 修改状态机算子：异常1 修改右表，影响Transfer入参数量 ############################
%version v1.0.0 -> v2.0.0

%alter table trigger(a:int4 , b:int4, c:int4, d:int4, e:int4) { index(0(a, b)), transient(tuple)}
%alter function transfer(c:int4, d:int4, e:int4 -> f:int4, g:int4) { state_transfer }

%alter rule r0 trigger(a, b, c, d, d) :- inpA(a, b, c, d).
%alter rule r1 state(a, b, f, g) :- trigger(a, b, c, d, e), transfer(c, d, e, f, g).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeAlterStateException1)
{
    const char *fileName = (char *)"client_st_upgrade_alter_table_state_machine_add_field";
    const char *deltaFileName = (char *)"client_st_upgrade_alter_table_state_machine_add_field_upgrade_exception";
    const char *upgradeSoName = (char *)"client_st_upgrade_alter_table_state_machine_add_field_patch";
    const char *fileWithRuleName = (char *)"client_st_upgrade_alter_table_state_machine_add_field_rule";

    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    // 加载原so
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    // 编译异常补丁
    EXPECT_NE(GMERR_OK, ExecPrepare4UpgradeWithRet(fileWithRuleName, deltaFileName, upgradeSoName));

    // 卸载原so
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

/*
 * ######################### 修改状态机算子：异常2 修改左表，影响Transfer出参数量 ###############################
%version v1.0.0 -> v2.0.0

%alter table state(a:int4 , b:int4, c:int4, d:int4, e:int4) { index(0(a, b)), state }
%alter function transfer(c:int4, d:int4 -> e:int4, f:int4, g:int4) { state_transfer }

%alter rule r1 state(a, b, e, f, g) :- trigger(a, b, c, d), transfer(c, d, e, f, g).
%alter rule r2 out(a, b, c, d) :- state(a, b, c, d, -).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeAlterStateException2)
{
    const char *fileName = (char *)"client_st_upgrade_alter_table_state_machine_add_field";
    const char *deltaFileName = (char *)"client_st_upgrade_alter_table_state_machine_add_field_upgrade_exception2";
    const char *upgradeSoName = (char *)"client_st_upgrade_alter_table_state_machine_add_field_patch";
    const char *fileWithRuleName = (char *)"client_st_upgrade_alter_table_state_machine_add_field_rule";

    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    // 加载原so
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    // 编译异常补丁
    EXPECT_NE(GMERR_OK, ExecPrepare4UpgradeWithRet(fileWithRuleName, deltaFileName, upgradeSoName));

    // 卸载原so
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

/*
 * ##################### 修改状态机算子：异常3：修改右表，导致Transfer出参与右表字段重合 ###################
%version v1.0.0 -> v2.0.0

%alter table trigger(a:int4 , b:int4, c:int4, d:int4, e:int4) { index(0(a, b)), transient(tuple)}

%alter rule r0 trigger(a, b, c, d, d) :- inpA(a, b, c, d).
%alter rule r1 state(a, b, e, f) :- trigger(a, b, c, d, e), transfer(c, d, e, f).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeAlterStateException3)
{
    const char *fileName = (char *)"client_st_upgrade_alter_table_state_machine_add_field";
    const char *deltaFileName = (char *)"client_st_upgrade_alter_table_state_machine_add_field_upgrade_exception3";
    const char *upgradeSoName = (char *)"client_st_upgrade_alter_table_state_machine_add_field_patch";
    const char *fileWithRuleName = (char *)"client_st_upgrade_alter_table_state_machine_add_field_rule";

    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    // 加载原so
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    // 编译异常补丁
    EXPECT_NE(GMERR_OK, ExecPrepare4UpgradeWithRet(fileWithRuleName, deltaFileName, upgradeSoName));

    // 卸载原so
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

/*
 * ##################### 修改状态机算子：异常4：修改右表，影响Transfer实际入参 ###################
%version v1.0.0 -> v2.0.0

%alter table trigger(a:int4 , b:int4, c:int4, d:int4, e:int4) { index(0(a, b)), transient(tuple)}

%alter rule r0 trigger(a, b, c, d, d) :- inpA(a, b, c, d).
%alter rule r1 state(a, b, e, f) :- trigger(a, b, c, -, d), transfer(c, d2, e, f).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeAlterStateException4)
{
    const char *fileName = (char *)"client_st_upgrade_alter_table_state_machine_add_field";
    const char *deltaFileName = (char *)"client_st_upgrade_alter_table_state_machine_add_field_upgrade_exception4";
    const char *upgradeSoName = (char *)"client_st_upgrade_alter_table_state_machine_add_field_patch";
    const char *fileWithRuleName = (char *)"client_st_upgrade_alter_table_state_machine_add_field_rule";

    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    // 加载原so
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    // 编译异常补丁
    EXPECT_NE(GMERR_OK, ExecPrepare4UpgradeWithRet(fileWithRuleName, deltaFileName, upgradeSoName));

    // 卸载原so
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

/*
 * ##################### 修改状态机算子：异常5：修改access_delta表，但transfer无修改 ###################
%version v1.0.0 -> v2.0.0

%alter table midE(a:int4, b:int4, c:int4, d:int4)

%alter rule r3 midE(a, b, c, c) :- inpE(a, b, c).
%alter rule r4 midF(a, b, c) :- midE(a, b, c, -).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeAlterStateException5)
{
    const char *fileName = (char *)"client_st_upgrade_alter_table_state_machine_add_field";
    const char *deltaFileName = (char *)"client_st_upgrade_alter_table_state_machine_add_field_upgrade_exception5";
    const char *upgradeSoName = (char *)"client_st_upgrade_alter_table_state_machine_add_field_patch";
    const char *fileWithRuleName = (char *)"client_st_upgrade_alter_table_state_machine_add_field_rule";

    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    // 加载原so
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    // 编译异常补丁
    EXPECT_NE(GMERR_OK, ExecPrepare4UpgradeWithRet(fileWithRuleName, deltaFileName, upgradeSoName));

    // 卸载原so
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

/*
 * ##################### 修改状态机算子：异常6：修改access_current表，但transfer无修改 ###################
%version v1.0.0 -> v2.0.0

%alter table midF(a:int4, b:int4, c:int4, d:int4)

%alter rule r4 midF(a, b, c, c) :- midE(a, b, c).
%alter rule r5 outF(a, b, c) :- midF(a, b, c, -).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeAlterStateException6)
{
    const char *fileName = (char *)"client_st_upgrade_alter_table_state_machine_add_field";
    const char *deltaFileName = (char *)"client_st_upgrade_alter_table_state_machine_add_field_upgrade_exception6";
    const char *upgradeSoName = (char *)"client_st_upgrade_alter_table_state_machine_add_field_patch";
    const char *fileWithRuleName = (char *)"client_st_upgrade_alter_table_state_machine_add_field_rule";

    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    // 加载原so
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    // 编译异常补丁
    EXPECT_NE(GMERR_OK, ExecPrepare4UpgradeWithRet(fileWithRuleName, deltaFileName, upgradeSoName));

    // 卸载原so
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

/*
 * ##################### 修改INIT/UNINIT：异常 ###################
    %version v1.0.0

    %table inpA(a:int4, b:int4, c:int4)
    %table outB(a:int4, b:int4, c:int4){
        tbm,
        index(0(a))
    }
    %function init()
    %function uninit()
    outB(a, b, c) :- inpA(a, b, c).
    ---->
    %version v1.0.0 -> v2.0.0

    %alter function init()
    %alter function uninit()
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeAlterINITException)
{
    const char *fileName = (char *)"client_st_upgrade_alter_table_init_uninit";
    const char *deltaFileName = (char *)"client_st_upgrade_alter_table_init_uninit_upgrade_exception";
    const char *upgradeSoName = (char *)"client_st_upgrade_alter_table_init_uninit_patch";
    const char *fileWithRuleName = (char *)"client_st_upgrade_alter_table_init_uninit_rule";

    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    // 加载原so
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    // 编译异常补丁
    EXPECT_NE(GMERR_OK, ExecPrepare4UpgradeWithRet(fileWithRuleName, deltaFileName, upgradeSoName));

    // 卸载原so
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

/*
 * ######################### 普通UDF升级异常场景 ###############################
    异常1：修改access_delta表，但function无修改
    异常2：修改access_current表，但function无修改

    原So定义：
    %version v1.0.0

    %table inpA(a:int4 , b:int4, c:int4, d:int4)
    %table state(a:int4 , b:int4, p:int4, q:int4)
    %table trigger(a:int4 , b:int4, c:int4, d:int4)
    %table out(a:int4 , b:int4, c:int4, d:int4)

    %function transfer(c:int4, d:int4 -> e:int4, f:int4) {
        access_delta(midF),
        access_current(midE)
    }


    %table inpE(a: int4, b: int4, c: int4)
    %table midE(a: int4, b: int4, c: int4)
    %table midF(a: int4, b: int4, c: int4)
    %table outF(a: int4, b: int4, c: int4)

    trigger(a, b, c, d) :- inpA(a, b, c, d).
    state(a, b, e, f) :- trigger(a, b, c, d), transfer(c, d, e, f).
    out(a, b, c, d) :- state(a, b, c, d).

    midE(a, b, c) :- inpE(a, b, c).
    midF(a, b, c) :- midE(a, b, c).
    outF(a, b, c) :- midF(a, b, c).
*/

/*
 * ######################### 修改普通UDF：异常1 修改access_delta表，但function无修改 ############################
    %version v1.0.0 -> v2.0.0

    %alter table midE(a:int4, b:int4, c:int4, d:int4)

    %alter function transfer(c:int4, d:int4 -> e:int4, f:int4) {
        access_delta(midF),
        access_current(midE)
    }

    %alter rule r3 midE(a, b, c, c) :- inpE(a, b, c).
    %alter rule r4 midF(a, b, c) :- midE(a, b, c, -).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeAlterFunctionException1)
{
    const char *fileName = (char *)"client_st_upgrade_alter_table_function_add_field";
    const char *deltaFileName = (char *)"client_st_upgrade_alter_table_function_add_field_upgrade_exception";
    const char *upgradeSoName = (char *)"client_st_upgrade_alter_table_function_add_field_patch";

    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    // 加载原so
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    // 编译异常补丁
    EXPECT_NE(GMERR_OK, execCmd4UpgradeWithUdf(fileName, deltaFileName, fileName, upgradeSoName));

    // 卸载原so
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

/*
 * ################ 修改普通UDF：异常2 修改access_current表，但function无修改 #######################
    %version v1.0.0 -> v2.0.0

    %alter table state(a:int4 , b:int4, c:int4, d:int4, e:int4) { index(0(a, b)), state }
    %alter function transfer(c:int4, d:int4 -> e:int4, f:int4, g:int4) { state_transfer }

    %alter rule r1 state(a, b, e, f, g) :- trigger(a, b, c, d), transfer(c, d, e, f, g).
    %alter rule r2 out(a, b, c, d) :- state(a, b, c, d, -).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeAlterFunctionException2)
{
    const char *fileName = (char *)"client_st_upgrade_alter_table_function_add_field";
    const char *deltaFileName = (char *)"client_st_upgrade_alter_table_function_add_field_upgrade_exception2";
    const char *upgradeSoName = (char *)"client_st_upgrade_alter_table_function_add_field_patch";
    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    // 加载原so
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName));

    // 编译异常补丁
    EXPECT_NE(GMERR_OK, execCmd4UpgradeWithUdf(fileName, deltaFileName, fileName, upgradeSoName));

    // 卸载原so
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
}

#ifndef ASAN

/*
 * ####### DML线程和升级线程不可以并行做,升级线程在做的时候,DML请求被阻塞 #######
%version v3.0.0

%table inpA(a:int4 , b:int4, c:int4, d:int4) { index(0(a, b)),update_partial}
%table inpB(a:int4 , b:int4, c:int4, d:int4) { index(0(a, b)),update_partial}
%table outC(a:int4 , b:int4, c:int4, d:int4, e:int4 , f:int4) {index(0(a , b)) , tbm}

%function init()
%function uninit()

outC(a, b, c, d , e , f) :- inpA(a, b, c, d), inpB(c, d, e, f).

--->
outC(a, b, c, d , 100 , 100) :- inpA(a, b, c, d), inpB(c, d, -, -).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeDmlCanNotConcurrentWithUpgradeThread)
{
    const char *fileName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_block";
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    system("gmsysview -q V\\$DATALOG_PLAN_EXPLAIN_INFO");
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");

    // 1. insert to inpA
    TableFourFixedFieldV tuplesA[100];
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];

        tuple->dtlReservedCount = 1;
        tuple->upgradeVersion = i;
        tuple->a = i;
        tuple->b = i;
        tuple->c = i;
        tuple->d = i;
    }

    EXPECT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpA, tuplesA, ELEMENT_COUNT(tuplesA), false));

    // 2. insert to inpB
    EXPECT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpB, tuplesA, ELEMENT_COUNT(tuplesA), false));

    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];
        tuple->upgradeVersion = 0;
    }
    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));
    ScanAndCheckTable(syncStmt, inpB, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    // 3. upgrade rule , modify join condition
    char *deltaFileName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_block_upgrade";
    char *upgradeSoName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_block_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_block_rule";
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName, defaultNsp));

    pthread_t tid;
    Status tErr;
    pthread_create(&tid, NULL, UpgradeDmlCanNotConcurrentWithUpgradeThreadCallBack, &tErr);
    usleep(1 * 1000 * 1000);  // 等待升级流程重刷数据完成
    EXPECT_EQ(GMERR_OK, tErr);
    EXPECT_EQ(true, tid != 0);

    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];
        tuple->upgradeVersion = 1;
    }
    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));
    ScanAndCheckTable(syncStmt, inpB, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

/*
 * ####### DML线程和升级线程不可以并行做,升级规则中只有一张新增的输入表,升级线程在做的时候(约等于没有做),DML线程阻塞
####### %version v3.0.0

%table inpA(a:int4 , b:int4, c:int4, d:int4) { index(0(a, b)),update_partial}
%table inpB(a:int4 , b:int4, c:int4, d:int4) { index(0(a, b)),update_partial}
%table outC(a:int4 , b:int4, c:int4, d:int4, e:int4 , f:int4) {index(0(a , b)) , tbm}

%function init()
%function uninit()

outC(a, b, c, d , e , f) :- inpA(a, b, c, d), inpB(c, d, e, f).

--->
%table inpD(a:int4 , b:int4, c:int4, d:int4) { index(0(a, b)),update_partial}

null(0) :- inpD(-,-,-,-).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeDmlCanNotConcurrentWithUpgradeThreadOneTable)
{
    const char *fileName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_add_one_table";
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    system("gmsysview -q V\\$DATALOG_PLAN_EXPLAIN_INFO");

    // 1. insert to inpA
    TableFourFixedFieldV tuplesA[100];
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];

        tuple->dtlReservedCount = 1;
        tuple->upgradeVersion = i;
        tuple->a = i;
        tuple->b = i;
        tuple->c = i;
        tuple->d = i;
    }

    EXPECT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpA, tuplesA, ELEMENT_COUNT(tuplesA), false));

    // 2. insert to inpB
    EXPECT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpB, tuplesA, ELEMENT_COUNT(tuplesA), false));

    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];
        tuple->upgradeVersion = 0;
    }
    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));
    ScanAndCheckTable(syncStmt, inpB, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    // 3. upgrade rule , modify join condition
    char *deltaFileName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_add_one_table_upgrade";
    char *upgradeSoName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_add_one_table_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_add_one_table_rule";
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName, defaultNsp));

    pthread_t tid;
    Status tErr;
    pthread_create(&tid, NULL, UpgradeDmlCanNotConcurrentWithUpgradeThreadCallBack, &tErr);
    usleep(1 * 1000 * 1000);  // 等待升级流程重刷数据完成
    EXPECT_EQ(GMERR_OK, tErr);
    EXPECT_EQ(true, tid != 0);

    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];
        tuple->upgradeVersion = 0;
    }
    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));
    ScanAndCheckTable(syncStmt, inpB, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

/*
 * ####### DML线程和升级线程不可以并行做,升级后台线程重做5S,DML重试2次(阻塞5S)可以加上锁 #####
%version v3.0.0

%table inpA(a:int4 , b:int4, c:int4, d:int4) { index(0(a, b)),index(1(a)),update_partial}
%table outA(a:int4 , b:int4, c:int4, d:int4) {index(0(a , b)) , tbm}

%function init()
%function uninit()

outA(a, b, c, d) :- inpA(a, b, c, d).

--->
outA(a, b, 100 , 100) :- inpA(a, b, -, -).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeDmlCanNotConcurrentWithUpgradeThreadBlock5S)
{
    const char *fileName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_timeout_thread_block";
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    system("gmsysview -q V\\$DATALOG_PLAN_EXPLAIN_INFO");

    // 1. insert to inpA
    TableFourFixedFieldV tuplesA[5];
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];

        tuple->dtlReservedCount = 1;
        tuple->upgradeVersion = i;
        tuple->a = i;
        tuple->b = i;
        tuple->c = i;
        tuple->d = i;
    }

    EXPECT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpA, tuplesA, ELEMENT_COUNT(tuplesA), false));

    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];
        tuple->upgradeVersion = 0;
    }
    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    // 3. upgrade rule , modify projection condition
    char *deltaFileName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_timeout_thread_block_upgrade";
    char *upgradeSoName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_timeout_thread_block_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_timeout_thread_block_rule";
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName, defaultNsp));

    // 4. insert inpA , expect block 5S
    EXPECT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpA, tuplesA, ELEMENT_COUNT(tuplesA), false));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    const char *expectedResults[] = {
        "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0",
    };

    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccessWithExpectedResults(3, expectedResults, ELEMENT_COUNT(expectedResults)));

    // 5. check result
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];
        tuple->upgradeVersion = 1;
    }

    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

/*
 * ####### DML线程和升级线程不可以并行做,升级后台线程重做14S,DML重试5次(阻塞14S)可以加上锁 #####
%version v3.0.0

%table inpA(a:int4 , b:int4, c:int4, d:int4) { index(0(a, b)),index(1(a)),update_partial}
%table outA(a:int4 , b:int4, c:int4, d:int4) {index(0(a , b)) , tbm}

%function init()
%function uninit()

outA(a, b, c, d) :- inpA(a, b, c, d).

--->
outA(a, b, 100 , 100) :- inpA(a, b, -, -).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeDmlCanNotConcurrentWithUpgradeThreadBlock14S)
{
    const char *fileName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_timeout_thread_block";
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    system("gmsysview -q V\\$DATALOG_PLAN_EXPLAIN_INFO");
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");

    // 1. insert to inpA
    TableFourFixedFieldV tuplesA[14];
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];

        tuple->dtlReservedCount = 1;
        tuple->upgradeVersion = i;
        tuple->a = i;
        tuple->b = i;
        tuple->c = i;
        tuple->d = i;
    }

    EXPECT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpA, tuplesA, ELEMENT_COUNT(tuplesA), false));

    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];
        tuple->upgradeVersion = 0;
    }
    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    // 3. upgrade rule , modify projection condition
    char *deltaFileName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_timeout_thread_block_upgrade";
    char *upgradeSoName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_timeout_thread_block_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_timeout_thread_block_rule";
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName, defaultNsp));

    // 4. insert inpA , expect block 5S
    EXPECT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpA, tuplesA, ELEMENT_COUNT(tuplesA), false));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    // 5. check result
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];
        tuple->upgradeVersion = 1;
    }

    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

/*
 * ####### DML线程和升级线程不可以并行做,升级后台线程重做17S,DML重试5次(阻塞15S),超时 #####
%version v3.0.0

%table inpA(a:int4 , b:int4, c:int4, d:int4) { index(0(a, b)),index(1(a)),update_partial}
%table outA(a:int4 , b:int4, c:int4, d:int4) {index(0(a , b)) , tbm}

%function init()
%function uninit()

outA(a, b, c, d) :- inpA(a, b, c, d).

--->
outA(a, b, 100 , 100) :- inpA(a, b, -, -).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeDmlCanNotConcurrentWithUpgradeThreadBlock17S)
{
    const char *fileName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_timeout_thread_block";
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    system("gmsysview -q V\\$DATALOG_PLAN_EXPLAIN_INFO");

    // 1. insert to inpA
    TableFourFixedFieldV tuplesA[17];
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];

        tuple->dtlReservedCount = 1;
        tuple->upgradeVersion = i;
        tuple->a = i;
        tuple->b = i;
        tuple->c = i;
        tuple->d = i;
    }

    EXPECT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpA, tuplesA, ELEMENT_COUNT(tuplesA), false));

    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];
        tuple->upgradeVersion = 0;
    }
    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    // 3. upgrade rule , modify projection condition
    char *deltaFileName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_timeout_thread_block_upgrade_17s";
    char *upgradeSoName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_timeout_thread_block_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_timeout_thread_block_rule";
    EXPECT_EQ(
        GMERR_OK, execCmd4UpgradeWithUdf(fileWithRuleName, deltaFileName, deltaFileName, upgradeSoName, defaultNsp));

    usleep(100 * 1000);
    // 4. insert inpA , expect block 15S
    EXPECT_EQ(GMERR_LOCK_NOT_AVAILABLE,
        InsertBatchTableWithStruct(syncConn, syncStmt, inpA, tuplesA, ELEMENT_COUNT(tuplesA), false));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    // 5. check result
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];
        tuple->upgradeVersion = 1;
    }
    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

/*
 * ####### DML线程和升级线程可以并行做,升级线程在做的时候,DML请求返回GMERR_OK #######
%version v3.0.0

%table inpA(a:int4 , b:int4, c:int4, d:int4) { index(0(a, b)),update_partial}
%table inpB(a:int4 , b:int4, c:int4, d:int4) { index(0(a, b)),update_partial}
%table outC(a:int4 , b:int4, c:int4, d:int4, e:int4 , f:int4) {index(0(a , b)) , tbm}

%function init()
%function uninit()

outC(a, b, c, d , e , f) :- inpA(a, b, c, d), inpB(c, d, e, f).

--->
outC(a, b, c, d , 100 , 100) :- inpA(a, b, c, d), inpB(c, d, -, -).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeDmlConcurrentWithUpgradeThread)
{
    pthread_t tid1;
    Status ret = pthread_create(&tid1, NULL, PatchInfoView, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    const char *fileName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread";
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    system("gmsysview -q V\\$DATALOG_PLAN_EXPLAIN_INFO");
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");

    // 1. insert to inpA
    TableFourFixedFieldV tuplesA[100];
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];

        tuple->dtlReservedCount = 1;
        tuple->upgradeVersion = i;
        tuple->a = i;
        tuple->b = i;
        tuple->c = i;
        tuple->d = i;
    }

    EXPECT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpA, tuplesA, ELEMENT_COUNT(tuplesA), false));

    // 1. insert to inpB
    EXPECT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpB, tuplesA, ELEMENT_COUNT(tuplesA), false));

    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];
        tuple->upgradeVersion = 0;
    }
    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));
    ScanAndCheckTable(syncStmt, inpB, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    // 2. upgrade rule , modify join condition
    char *deltaFileName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_upgrade";
    char *upgradeSoName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_rule";
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName, defaultNsp));

    pthread_t tid;
    Status tErr;
    pthread_create(&tid, NULL, UpgradeDmlCanNotConcurrentWithUpgradeThreadCallBack, &tErr);
    usleep(1 * 1000 * 1000);  // 等待升级流程重刷数据完成
    EXPECT_EQ(GMERR_OK, tErr);
    EXPECT_EQ(true, tid != 0);
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];
        tuple->upgradeVersion = 1;
    }
    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));
    ScanAndCheckTable(syncStmt, inpB, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
    pthread_join(tid1, NULL);
}

/*
 * ####### 升级线程完成之后,即使配置项为不可并行做,DML请求也要返回GMERR_OK #######
%version v3.0.0

%table inpA(a:int4 , b:int4, c:int4, d:int4) { index(0(a, b)),index(1(a)),update_partial}
%table inpB(a:int4 , b:int4, c:int4, d:int4) { index(0(a, b)),index(1(a)),update_partial}
%table outC(a:int4 , b:int4, c:int4, d:int4, e:int4 , f:int4) {index(0(a , b)) , tbm}

%function init()
%function uninit()

outC(a, b, c, d , e , f) :- inpA(a, b, c, d), inpB(c, d, e, f).

--->
outC(a, b, c, d , 100 , 100) :- inpA(a, b, c, d), inpB(c, d, -, -).
 */
TEST_F(ClientStExecUpgradeDatalog, UpgradeDmlRunAfterUpgradeThread)
{
    const char *fileName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_block";
    const char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));
    system("gmsysview -q V\\$DATALOG_PLAN_EXPLAIN_INFO");
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");

    // 1. insert to inpA
    TableFourFixedFieldV tuplesA[100];
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];

        tuple->dtlReservedCount = 1;
        tuple->upgradeVersion = i;
        tuple->a = i;
        tuple->b = i;
        tuple->c = i;
        tuple->d = i;
    }

    EXPECT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpA, tuplesA, ELEMENT_COUNT(tuplesA), false));

    // 1. insert to inpB
    EXPECT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpB, tuplesA, ELEMENT_COUNT(tuplesA), false));

    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];
        tuple->upgradeVersion = 0;
    }
    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));
    ScanAndCheckTable(syncStmt, inpB, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    // 2. upgrade rule , modify join condition
    char *deltaFileName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_block_upgrade";
    char *upgradeSoName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_block_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_block_rule";
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName, defaultNsp));

    usleep(1 * 1000 * 1000);  // 等待升级流程重刷数据完成

    // 3. insert to inpA
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];

        int32_t data = 100 + i;
        tuple->dtlReservedCount = 1;
        tuple->upgradeVersion = data;
        tuple->a = data;
        tuple->b = data;
        tuple->c = data;
        tuple->d = data;
    }

    EXPECT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpA, tuplesA, ELEMENT_COUNT(tuplesA), false));

    // 4. insert to inpB
    EXPECT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpB, tuplesA, ELEMENT_COUNT(tuplesA), false));

    TableFourFixedFieldV tuplesResult[200];
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesResult); i++) {
        TableFourFixedFieldV *tuple = &tuplesResult[i];
        tuple->dtlReservedCount = 1;
        tuple->upgradeVersion = 1;
        tuple->a = i;
        tuple->b = i;
        tuple->c = i;
        tuple->d = i;
    }

    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesResult, ELEMENT_COUNT(tuplesResult));
    ScanAndCheckTable(syncStmt, inpB, (int32_t *)tuplesResult, ELEMENT_COUNT(tuplesResult));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

/*
 * ####### 升级线程完成之后,根据主键/非主键删除 #######
%version v3.0.0

%table inpA(a:int4 , b:int4, c:int4, d:int4) { index(0(a, b)),index(1(a)),update_partial}
%table inpB(a:int4 , b:int4, c:int4, d:int4) { index(0(a, b)),index(1(a)),update_partial}
%table outC(a:int4 , b:int4, c:int4, d:int4, e:int4 , f:int4) {index(0(a , b)) , tbm}

%function init()
%function uninit()

outC(a, b, c, d , e , f) :- inpA(a, b, c, d), inpB(c, d, e, f).

--->
outC(a, b, c, d , 100 , 100) :- inpA(a, b, c, d), inpB(c, d, -, -).
 */
TEST_F(ClientStExecUpgradeDatalog, OnlineDeleteAfterUpgradeThread)
{
    const char *fileName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread";
    const char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    system("gmsysview -q V\\$DATALOG_PLAN_EXPLAIN_INFO");
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");

    // 1. insert to inpA
    TableFourFixedFieldV tuplesA[100];
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];

        tuple->dtlReservedCount = 1;
        tuple->upgradeVersion = i;
        tuple->a = i;
        tuple->b = i;
        tuple->c = i;
        tuple->d = i;
    }

    EXPECT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpA, tuplesA, ELEMENT_COUNT(tuplesA), false));

    // 2. insert to inpB
    EXPECT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpB, tuplesA, ELEMENT_COUNT(tuplesA), false));

    // 3. check inpA and inpB's result as upgradeVersion = 0
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];
        tuple->upgradeVersion = 0;
    }

    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));
    ScanAndCheckTable(syncStmt, inpB, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    // 记录DFX视图
    char *commandView1 = (char *)"gmsysview -q V\\$PTL_DATALOG_SO_INFO | grep MAX*";
    const char *expectedView1[] = {"MAX_MEM_INCREASE_RULE_NAME_TOP: r0,inpA,inpB",
        "  MAX_TRIGGER_RULE_NUM_INPUT_TABLE_TOP: inpA,inpB", "  MAX_TRIGGER_RULE_NUM_TOP: 2,2,0"};
    EXPECT_EQ(GMERR_OK, StExecuteCommandWithMatch(commandView1, expectedView1, ELEMENT_COUNT(expectedView1)));

    // 4. upgrade rule , modify join condition
    char *deltaFileName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_upgrade";
    char *upgradeSoName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_rule";
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName, defaultNsp));

    usleep(1 * 1000 * 1000);  // 等待升级流程重刷数据完成
    EXPECT_EQ(GMERR_OK, StExecuteCommandWithMatch(commandView1, expectedView1, ELEMENT_COUNT(expectedView1)));
    // 5. check inpA and inpB's result as upgradeVersion = 1
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];
        tuple->upgradeVersion = 1;
    }

    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));
    ScanAndCheckTable(syncStmt, inpB, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    // 6. delete inpA by primary index
    int32_t upgradeVersion = 100;
    int32_t a = 98;
    int32_t b = 98;
    int affectRows = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(syncStmt, inpA, GMC_OPERATION_DELETE));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyId(syncStmt, 0));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(upgradeVersion)));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(syncStmt, 1, GMC_DATATYPE_INT32, &a, sizeof(a)));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(syncStmt, 2, GMC_DATATYPE_INT32, &b, sizeof(b)));
    EXPECT_EQ(GMERR_OK, GmcExecute(syncStmt));
    EXPECT_EQ(GMERR_OK, GmcGetStmtAttr(syncStmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows)));
    EXPECT_EQ(affectRows, 1);
    checkVertexRecordCount(syncStmt, inpA, 99);
    checkVertexRecordCount(syncStmt, inpB, 100);

    // 7. delete inpA by secondary index
    a = 97;
    b = 97;
    affectRows = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(syncStmt, inpA, GMC_OPERATION_DELETE));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyId(syncStmt, 1));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_INT32, &a, sizeof(a)));
    EXPECT_EQ(GMERR_OK, GmcExecute(syncStmt));
    EXPECT_EQ(GMERR_OK, GmcGetStmtAttr(syncStmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows)));
    EXPECT_EQ(affectRows, 1);
    checkVertexRecordCount(syncStmt, inpA, 98);
    checkVertexRecordCount(syncStmt, inpB, 100);

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

/*
 * ####### 升级线程完成之后,根据主键/非主键更新;更新的值是不是upgradeVersion #######
%version v3.0.0

%table inpA(a:int4 , b:int4, c:int4, d:int4) { index(0(a, b)),index(1(a)),update_partial}
%table inpB(a:int4 , b:int4, c:int4, d:int4) { index(0(a, b)),index(1(a)),update_partial}
%table outC(a:int4 , b:int4, c:int4, d:int4, e:int4 , f:int4) {index(0(a , b)) , tbm}

%function init()
%function uninit()

outC(a, b, c, d , e , f) :- inpA(a, b, c, d), inpB(c, d, e, f).

--->
outC(a, b, c, d , 100 , 100) :- inpA(a, b, c, d), inpB(c, d, -, -).
 */
TEST_F(ClientStExecUpgradeDatalog, OnlineUpdateAfterUpgradeThread)
{
    char *fileName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread";
    const char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    // 编译升降级so
    char *upgradeSoName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_patch";
    char *rollbackSoName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_rollback";
    char *deltaFileName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_upgrade";
    char *deltaOutputFileName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_full";
    char *fileWithRuleName = (char *)"client_st_upgrade_dml_concurrent_with_upgrade_thread_rule";
    ExecPrepare4RollbackUpgradeWithOutputFile(
        fileWithRuleName, deltaFileName, deltaOutputFileName, upgradeSoName, rollbackSoName);
    CheckMemSizeForMultiLoadHotPatch(defaultNsp, fileName, 10, 2, upgradeSoName, rollbackSoName);

    system("gmsysview -q V\\$DATALOG_PLAN_EXPLAIN_INFO");
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");

    // 1. insert to inpA
    TableFourFixedFieldV tuplesA[100];
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];

        tuple->dtlReservedCount = 1;
        tuple->upgradeVersion = i;
        tuple->a = i;
        tuple->b = i;
        tuple->c = i;
        tuple->d = i;
    }

    EXPECT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpA, tuplesA, ELEMENT_COUNT(tuplesA), false));

    // 2. insert to inpB
    EXPECT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpB, tuplesA, ELEMENT_COUNT(tuplesA), false));

    // 3. check inpA and inpB's result as upgradeVersion = 0
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];
        tuple->upgradeVersion = 0;
    }

    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));
    ScanAndCheckTable(syncStmt, inpB, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    // 4. upgrade rule , modify join condition
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));

    usleep(1 * 1000 * 1000);  // 等待升级流程重刷数据完成
                              // 5. check inpA and inpB's result as upgradeVersion = 1
    for (uint32_t i = 0; i < ELEMENT_COUNT(tuplesA); i++) {
        TableFourFixedFieldV *tuple = &tuplesA[i];
        tuple->upgradeVersion = 1;
    }

    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));
    ScanAndCheckTable(syncStmt, inpB, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    // 6. update field c by primary key
    int32_t upgradeVersion = 100;
    int32_t a = 98;
    int32_t b = 98;
    int32_t fieldC = 99;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(syncStmt, inpA, GMC_OPERATION_UPDATE));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyId(syncStmt, 0));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(upgradeVersion)));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(syncStmt, 1, GMC_DATATYPE_INT32, &a, sizeof(a)));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(syncStmt, 2, GMC_DATATYPE_INT32, &b, sizeof(b)));

    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(syncStmt, "c", GMC_DATATYPE_INT32, &fieldC, sizeof(fieldC)));
    EXPECT_EQ(GMERR_OK, GmcExecute(syncStmt));
    checkVertexRecordCount(syncStmt, inpA, 100);
    checkVertexRecordCount(syncStmt, inpB, 100);

    TableFourFixedFieldV *tuple = &tuplesA[98];
    tuple->c = 99;
    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));
    tuple->c = 98;
    ScanAndCheckTable(syncStmt, inpB, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    // 7. update field upgradeVersion by primary key is not allowed
    upgradeVersion = 100;
    a = 98;
    b = 98;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(syncStmt, inpA, GMC_OPERATION_UPDATE));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyId(syncStmt, 0));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(upgradeVersion)));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(syncStmt, 1, GMC_DATATYPE_INT32, &a, sizeof(a)));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(syncStmt, 2, GMC_DATATYPE_INT32, &b, sizeof(b)));

    EXPECT_EQ(GMERR_OK,
        GmcSetVertexProperty(syncStmt, "upgradeVersion", GMC_DATATYPE_INT32, &upgradeVersion, sizeof(upgradeVersion)));
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcExecute(syncStmt));
    checkVertexRecordCount(syncStmt, inpA, 100);
    checkVertexRecordCount(syncStmt, inpB, 100);
    tuple->c = 99;
    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));
    tuple->c = 98;
    ScanAndCheckTable(syncStmt, inpB, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    // 8. update fieldC by secondary index
    a = 98;
    fieldC = 100;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(syncStmt, inpA, GMC_OPERATION_UPDATE));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyId(syncStmt, 1));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_INT32, &a, sizeof(a)));

    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(syncStmt, "c", GMC_DATATYPE_INT32, &fieldC, sizeof(fieldC)));
    EXPECT_EQ(GMERR_OK, GmcExecute(syncStmt));
    checkVertexRecordCount(syncStmt, inpA, 100);
    checkVertexRecordCount(syncStmt, inpB, 100);
    tuple->c = 100;
    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));
    tuple->c = 98;
    ScanAndCheckTable(syncStmt, inpB, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    // 8. update upgradeVersion by secondary index is not allowed
    a = 98;
    upgradeVersion = 100;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(syncStmt, inpA, GMC_OPERATION_UPDATE));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyId(syncStmt, 1));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_INT32, &a, sizeof(a)));

    EXPECT_EQ(GMERR_OK,
        GmcSetVertexProperty(syncStmt, "upgradeVersion", GMC_DATATYPE_INT32, &upgradeVersion, sizeof(upgradeVersion)));
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcExecute(syncStmt));
    checkVertexRecordCount(syncStmt, inpA, 100);
    checkVertexRecordCount(syncStmt, inpB, 100);
    tuple->c = 100;
    ScanAndCheckTable(syncStmt, inpA, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));
    tuple->c = 98;
    ScanAndCheckTable(syncStmt, inpB, (int32_t *)tuplesA, ELEMENT_COUNT(tuplesA));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 修改规则fun实现，涉及重做的表包含timeout表（不包含过期函数），测试timeout与后台redo线程并发
TEST_F(ClientStExecUpgradeDatalog, Redo_Timeout1)
{
    char *fileName = (char *)"client_st_upgrade_redo_timeout";
    char *udfFileName = (char *)"client_st_upgrade_redo_timeout";
    const char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));

    // 1.构造10条过期数据，每隔1秒过期一条
    TableOneFixedFieldTimeout records[5];
    for (uint32_t i = 0; i < ELEMENT_COUNT(records); i++) {
        TableOneFixedFieldTimeout *record = &records[i];
        record->a = i;
        record->t = (int64_t)i * 1000;
        record->dtlReservedCount = 1;
        record->upgradeVersion = 0;
    }
    Status ret = InsertBatchTableWithStruct(syncConn, syncStmt, "inpA", (void *)records, ELEMENT_COUNT(records), true);
    EXPECT_EQ(GMERR_OK, ret);
    // 2.期间补丁升级so
    char *deltaFileName = (char *)"client_st_upgrade_redo_timeout_patch";
    char *upgradeUdfFileName = (char *)"client_st_upgrade_redo_timeout_patch";
    char *upgradeSoName = (char *)"client_st_upgrade_redo_timeout_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_redo_timeout_rule";
    EXPECT_EQ(GMERR_OK,
        execCmd4UpgradeWithUdf(fileWithRuleName, deltaFileName, upgradeUdfFileName, upgradeSoName, defaultNsp));
    // 3.升级期间无数据过期（通过join fun控制redo表时间，每条数据redo耗时1s）
    uint64_t recordCnt = 0;
    // 接下来的recordCnt-1秒都在redo数据（默认一次redo一条）
    EXPECT_EQ(GMERR_OK, GetTableCount(syncStmt, "inpA", &recordCnt));
    // GetTableCount可能阻塞1s，期间可能已完成一条数据的redo
    int32_t checkCnt = recordCnt - 2;
    int32_t sleepCnt = 9;
    while (checkCnt > 0 && !IsUpgradeSuccess(fileName)) {
        uint64_t recordCnt1 = 0;
        // GetTableCount可能阻塞1s
        EXPECT_EQ(GMERR_OK, GetTableCount(syncStmt, "inpA", &recordCnt1));
        // 最后一条数据redo，概率性出现进入循环后马上完成了redo，正在进行过期，GetTableCount被阻塞，过期完成过期后查到数据为0
        if (recordCnt1 == 0) {
            EXPECT_EQ(true, IsUpgradeSuccess(fileName));
            goto Release;
        }
        // 重做期间，删除旧数据，插入新数据是两个事务，则重做期间数据条数可能出现1的波动
        EXPECT_EQ(true, recordCnt == recordCnt1 || recordCnt == recordCnt1 + 1 || recordCnt == recordCnt1 - 1);
        checkCnt--;
        sleep(1);
    }
    // 4.到指定时间数据全部过期
    CheckUpgradeSuccess(fileName, 5);
    // 正在过期时获取recordCnt可能超时
    ret = GetTableCount(syncStmt, "inpA", &recordCnt);
    while (sleepCnt > 0 && (ret != GMERR_OK || (int32_t)recordCnt != 0)) {
        sleep(1);
        ret = GetTableCount(syncStmt, "inpA", &recordCnt);
        sleepCnt--;
    }
Release:
    EXPECT_EQ(GMERR_OK, GetTableCount(syncStmt, "inpA", &recordCnt));
    EXPECT_EQ(0, (int32_t)recordCnt);
    EXPECT_EQ(GMERR_OK, GetTableCount(syncStmt, "outA", &recordCnt));
    EXPECT_EQ(0, (int32_t)recordCnt);

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 测试timeout与升级请求并发
TEST_F(ClientStExecUpgradeDatalog, Redo_Timeout2)
{
    char *fileName = (char *)"client_st_upgrade_redo_timeout_withudf";
    char *udfFileName = (char *)"client_st_upgrade_redo_timeout_withudf";
    const char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));

    // 1. 编译升降级so
    char *deltaFileName = (char *)"client_st_upgrade_redo_timeout_withudf_patch";
    char *deltaOutputFileName = (char *)"client_st_upgrade_redo_timeout_withudf_full";
    char *upgradeUdfFileName = (char *)"client_st_upgrade_redo_timeout_withudf_patch";
    char *upgradeSoName = (char *)"client_st_upgrade_redo_timeout_withudf_patch";
    char *rollbackSoName = (char *)"client_st_upgrade_redo_timeout_withudf_rollback";
    char *fileWithRuleName = (char *)"client_st_upgrade_redo_timeout_withudf_rule";
    ExecPrepare4RollbackUpgradeWithOutputFileAndUdf(
        fileWithRuleName, deltaFileName, deltaOutputFileName, upgradeUdfFileName, upgradeSoName, rollbackSoName);
    CheckMemSizeForMultiLoadHotPatch(defaultNsp, fileName, 10, 2, upgradeSoName, rollbackSoName);

    // 2.构造n条过期数据，插入后立马过期，过期函数中每条数据sleep(1)
    uint32_t cnt = 7;
    TableOneFixedFieldTimeout records[cnt];
    for (uint32_t i = 0; i < ELEMENT_COUNT(records); i++) {
        TableOneFixedFieldTimeout *record = &records[i];
        record->a = i;
        record->t = (int64_t)0;
        record->dtlReservedCount = 1;
        record->upgradeVersion = 0;
    }
    Status ret = InsertBatchTableWithStruct(syncConn, syncStmt, "inpA", (void *)records, ELEMENT_COUNT(records), true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(4);

    // 3.过期期间补丁升级so，预期失败
    EXPECT_NE(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    ret = execUninstallCmd(fileName, defaultNsp);
    while (cnt-- != 0 && ret != GMERR_OK) {
        sleep(2);
        ret = execUninstallCmd(fileName, defaultNsp);
    }
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 测试dml与redo请求并发,输入表主键冲突的场景，升级过程中，写入新版本数据与将要过期的数据发生主键冲突, 预期redo失败
// 冲突数据循环单写
TEST_F(ClientStExecUpgradeDatalog, DISABLED_Redo_Dml_Concurrent)
{
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");
    char *fileName = (char *)"client_st_upgrade_redo_dml_samekey";
    char *udfFileName = (char *)"client_st_upgrade_redo_dml_samekey";
    char *defaultNsp = (char *)"ylog";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName));

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, GmcUseNamespace(syncStmt, defaultNsp));

    // 1.编译升级so
    char *deltaOutputFileName = (char *)"client_st_upgrade_redo_dml_samekey_full";
    char *deltaFileName = (char *)"client_st_upgrade_redo_dml_samekey_patch";
    char *upgradeUdfFileName = (char *)"client_st_upgrade_redo_dml_samekey_patch";
    char *upgradeSoName = (char *)"client_st_upgrade_redo_dml_samekey_patch";
    char *rollbackSoName = (char *)"client_st_upgrade_redo_dml_samekey_rollback";
    char *fileWithRuleName = (char *)"client_st_upgrade_redo_dml_samekey_rule";
    ExecPrepare4RollbackUpgradeWithOutputFileAndUdf(
        fileWithRuleName, deltaFileName, deltaOutputFileName, upgradeUdfFileName, upgradeSoName, rollbackSoName);
    // 循环升降级，check内存泄漏
    CheckMemSizeForMultiLoadHotPatch(defaultNsp, fileName, 10, 2, upgradeSoName, rollbackSoName);

    // 2.构造10条过期数据，插入后立马过期，过期函数中每条数据sleep(1)
    int32_t recordsA[][4] = {{1, 0, 1, 1}, {1, 0, 0, 2}, {1, 0, 1, 3}};
    int32_t recordsB[][5] = {{1, 0, 1, 1, 1}, {1, 0, 3, 1, 1}, {1, 0, 4, 1, 2}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)recordsA, ELEMENT_COUNT(recordsA), true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpB", (int32_t *)recordsB, ELEMENT_COUNT(recordsB), true);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName));
    // 3.升级期间写入之前主键冲突的数据
    int32_t recordsB2[][5] = {{1, 0, 1, 1, 2}, {1, 0, 3, 1, 2}, {1, 0, 4, 1, 3}};
    for (int i = 0; i < 3; i++) {
        // 写成功一条冲突数据即将导致升级失败
        ret = InsertInpTableBatch(syncConn, syncStmt, "inpB", (int32_t *)recordsB2[i], 1, false);
        if (ret == GMERR_OK) {
            break;
        }
    }
    EXPECT_EQ(GMERR_OK, ret);
    // 4.升级完成校验所有表数据数量, 查看升级是否失败
    CheckUpgradeSuccess(fileName, 3);
    const char *sysviewCmd1 = "gmsysview -q  V\\$PTL_DATALOG_PATCH_INFO";
    const char *matchStr1[] = {"PATCH_STATE: REDO_FAIL"};
    ret = StExecuteCommandWithMatch((char *)sysviewCmd1, matchStr1, ELEMENT_COUNT(matchStr1));
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

#endif

// 修改AGG所在规则的左表投影条件
// group by 字段投影到输出表的顺便不允许修改
TEST_F(ClientStExecUpgradeDatalog, AlterAggRule1)
{
    char *fileName = (char *)"client_st_upgrade_alter_agg_rule1";
    char *udfFileName = (char *)"client_st_upgrade_alter_agg_rule1";
    const char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));

    int32_t records1[][5] = {{1, 0, 1, 2, 2}, {1, 0, 1, 3, 2}, {1, 0, 2, 3, 2}, {1, 0, 2, 3, 4}, {1, 0, 3, 4, 3},
        {1, 0, 4, 5, 4}, {1, 0, 4, 5, 8}, {1, 0, 8, 9, 8}};
    int32_t records2[][6] = {{2, 0, 1, 3, 2, 1}, {2, 0, 1, 2, 2, 1}, {1, 0, 2, 3, 2, 1}, {1, 0, 2, 3, 4, 1},
        {1, 0, 4, 5, 4, 1}, {1, 0, 4, 5, 8, 1}, {2, 0, 3, 4, 3, 1}, {2, 0, 8, 9, 8, 1}};
    int32_t records3[][5] = {{1, 0, 1, 3, 2}, {1, 0, 1, 2, 2}, {1, 0, 2, 3, 2}, {1, 0, 4, 5, 4}, {1, 0, 3, 4, 3},
        {1, 0, 8, 9, 8}, {1, 0, 4, 5, 8}, {1, 0, 2, 3, 4}};

    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inp6", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    ScanAndCheckTable(syncStmt, "inp6", (int32_t *)records1, ELEMENT_COUNT(records1));
    ScanAndCheckTable(syncStmt, "mid4", (int32_t *)records2, ELEMENT_COUNT(records2));
    ScanAndCheckTable(syncStmt, "out6", (int32_t *)records3, ELEMENT_COUNT(records3));

    char *deltaFileName = (char *)"client_st_upgrade_alter_agg_rule1_patch";
    char *upgradeSoName = (char *)"client_st_upgrade_alter_agg_rule1_patch";
    char *fileNameWithRuleName = (char *)"client_st_upgrade_alter_agg_rule1_rule";
    // 编译异常补丁
    char cmd[DB_INVALID_UINT16];
    int cmdLength = sprintf_s(cmd, DB_INVALID_UINT16,
        "source ../../../../scripts/build_scripts/asan_clean_env.sh && ../../../../build/bin/gmprecompiler -u "
        "./002_datalog/client_st_dot_datalog/%s.d "
        "./002_datalog/client_st_dot_datalog/%s.d "
        "./002_datalog/client_st_dot_datalog/%s.c ./002_datalog/client_st_dot_datalog/%s_full.d && gcc -fPIC "
        "--shared ./002_datalog/client_st_dot_datalog/%s.c -o ./002_datalog/client_st_dot_datalog/%s.so",
        fileNameWithRuleName, deltaFileName, deltaFileName, deltaFileName, deltaFileName, upgradeSoName);
    EXPECT_GT(cmdLength, 0);
    ret = system(cmd);
    EXPECT_NE(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 修改投影字段的顺序，字段不是group by字段
// datalogUpgradeFetchSize == 1
TEST_P(ClientStExecUpgradeDatalog, AlterAggRule2)
{
    ClearGmserverLog();
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
    char *fileName = (char *)"client_st_upgrade_alter_agg_rule2";
    char *udfFileName = (char *)"client_st_upgrade_alter_agg_rule2";
    const char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));
    char *deltaFileName = (char *)"client_st_upgrade_alter_agg_rule2_patch";
    char *deltaOutputFileName = (char *)"client_st_upgrade_alter_agg_rule2_full";
    char *upgradeSoName = (char *)"client_st_upgrade_alter_agg_rule2_patch";
    char *rollbackSoName = (char *)"client_st_upgrade_alter_agg_rule2_rollback";
    char *fileWithRuleName = (char *)"client_st_upgrade_alter_agg_rule2_rule";
    int param = GetParam().intValue == 0 ? 0 : 1;
    if (param == 1) {
        RecoverBlockStmtInPatchFile(deltaFileName);
    }
    ExecPrepare4RollbackUpgradeWithOutputFile(
        fileWithRuleName, deltaFileName, deltaOutputFileName, upgradeSoName, rollbackSoName);
    CheckMemSizeForMultiLoadHotPatch(defaultNsp, fileName, 10, 2, upgradeSoName, rollbackSoName);

    int32_t records1[][5] = {{1, 0, 1, 2, 2}, {1, 0, 1, 3, 2}, {1, 0, 2, 3, 2}, {1, 0, 2, 3, 4}, {1, 0, 3, 4, 3},
        {1, 0, 4, 5, 4}, {1, 0, 4, 5, 8}, {1, 0, 8, 9, 8}};
    int32_t records2[][6] = {{2, 0, 1, 2, 102, 12}, {2, 0, 1, 3, 102, 12}, {1, 0, 2, 3, 102, 12}, {1, 0, 2, 3, 104, 14},
        {2, 0, 3, 4, 103, 13}, {1, 0, 4, 5, 104, 14}, {1, 0, 4, 5, 108, 18}, {2, 0, 8, 9, 108, 18}};
    int32_t records3[][5] = {{1, 0, 1, 2, 102}, {1, 0, 1, 3, 102}, {1, 0, 2, 3, 102}, {1, 0, 2, 3, 104},
        {1, 0, 3, 4, 103}, {1, 0, 4, 5, 104}, {1, 0, 4, 5, 108}, {1, 0, 8, 9, 108}};

    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inp6", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    ScanAndCheckTable(syncStmt, "inp6", (int32_t *)records1, ELEMENT_COUNT(records1));
    ScanAndCheckTable(syncStmt, "mid4", (int32_t *)records2, ELEMENT_COUNT(records2));
    ScanAndCheckTable(syncStmt, "out6", (int32_t *)records3, ELEMENT_COUNT(records3));

    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));
    EXPECT_EQ(GMERR_OK, CheckBlockMode(param == 1));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");

    int32_t upgradeVersion = param == 1 ? 0 : 1;
    int32_t records4[][5] = {{1, upgradeVersion, 1, 2, 2}, {1, upgradeVersion, 1, 3, 2}, {1, upgradeVersion, 2, 3, 2},
        {1, upgradeVersion, 2, 3, 4}, {1, upgradeVersion, 3, 4, 3}, {1, upgradeVersion, 4, 5, 4},
        {1, upgradeVersion, 4, 5, 8}, {1, upgradeVersion, 8, 9, 8}};
    int32_t records5[][6] = {{2, upgradeVersion, 1, 2, 12, 102}, {2, upgradeVersion, 1, 3, 12, 102},
        {1, upgradeVersion, 2, 3, 12, 102}, {1, upgradeVersion, 2, 3, 14, 104}, {2, upgradeVersion, 3, 4, 13, 103},
        {1, upgradeVersion, 4, 5, 14, 104}, {1, upgradeVersion, 4, 5, 18, 108}, {2, upgradeVersion, 8, 9, 18, 108}};
    int32_t records6[][5] = {{1, upgradeVersion, 1, 2, 12}, {1, upgradeVersion, 1, 3, 12},
        {1, upgradeVersion, 2, 3, 12}, {1, upgradeVersion, 2, 3, 14}, {1, upgradeVersion, 3, 4, 13},
        {1, upgradeVersion, 4, 5, 14}, {1, upgradeVersion, 4, 5, 18}, {1, upgradeVersion, 8, 9, 18}};
    ScanAndCheckTable(syncStmt, "inp6", (int32_t *)records4, ELEMENT_COUNT(records4));
    ScanAndCheckTable(syncStmt, "mid4", (int32_t *)records5, ELEMENT_COUNT(records5));
    ScanAndCheckTable(syncStmt, "out6", (int32_t *)records6, ELEMENT_COUNT(records6));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));
    ScanAndCheckTable(syncStmt, "inp6", (int32_t *)records1, ELEMENT_COUNT(records1));
    ScanAndCheckTable(syncStmt, "mid4", (int32_t *)records2, ELEMENT_COUNT(records2));
    ScanAndCheckTable(syncStmt, "out6", (int32_t *)records3, ELEMENT_COUNT(records3));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
    if (param == 1) {
        RemoveBlockStmtInPatchFile(deltaFileName);
    }
}

// 修改投影字段的顺序，字段不是group by字段
// datalogUpgradeFetchSize == 20
TEST_P(ClientStExecUpgradeDatalog, AlterAggRule3)
{
    ClearGmserverLog();
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 20");
    char *fileName = (char *)"client_st_upgrade_alter_agg_rule2";
    char *udfFileName = (char *)"client_st_upgrade_alter_agg_rule2";
    const char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));
    char *deltaFileName = (char *)"client_st_upgrade_alter_agg_rule2_patch";
    char *deltaOutputFileName = (char *)"client_st_upgrade_alter_agg_rule2_full";
    char *upgradeSoName = (char *)"client_st_upgrade_alter_agg_rule2_patch";
    char *rollbackSoName = (char *)"client_st_upgrade_alter_agg_rule2_rollback";
    char *fileWithRuleName = (char *)"client_st_upgrade_alter_agg_rule2_rule";
    int param = GetParam().intValue == 0 ? 0 : 1;
    if (param == 1) {
        RecoverBlockStmtInPatchFile(deltaFileName);
    }
    ExecPrepare4RollbackUpgradeWithOutputFile(
        fileWithRuleName, deltaFileName, deltaOutputFileName, upgradeSoName, rollbackSoName);
    CheckMemSizeForMultiLoadHotPatch(defaultNsp, fileName, 10, 2, upgradeSoName, rollbackSoName);

    int32_t records1[][5] = {{1, 0, 1, 2, 2}, {1, 0, 1, 3, 2}, {1, 0, 2, 3, 2}, {1, 0, 2, 3, 4}, {1, 0, 3, 4, 3},
        {1, 0, 4, 5, 4}, {1, 0, 4, 5, 8}, {1, 0, 8, 9, 8}};
    int32_t records2[][6] = {{2, 0, 1, 2, 102, 12}, {2, 0, 1, 3, 102, 12}, {1, 0, 2, 3, 102, 12}, {1, 0, 2, 3, 104, 14},
        {2, 0, 3, 4, 103, 13}, {1, 0, 4, 5, 104, 14}, {1, 0, 4, 5, 108, 18}, {2, 0, 8, 9, 108, 18}};
    int32_t records3[][5] = {{1, 0, 1, 2, 102}, {1, 0, 1, 3, 102}, {1, 0, 2, 3, 102}, {1, 0, 2, 3, 104},
        {1, 0, 3, 4, 103}, {1, 0, 4, 5, 104}, {1, 0, 4, 5, 108}, {1, 0, 8, 9, 108}};

    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inp6", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    ScanAndCheckTable(syncStmt, "inp6", (int32_t *)records1, ELEMENT_COUNT(records1));
    ScanAndCheckTable(syncStmt, "mid4", (int32_t *)records2, ELEMENT_COUNT(records2));
    ScanAndCheckTable(syncStmt, "out6", (int32_t *)records3, ELEMENT_COUNT(records3));

    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    EXPECT_EQ(GMERR_OK, CheckBlockMode(param == 1));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");

    int32_t upgradeVersion = param == 1 ? 0 : 1;
    int32_t records4[][5] = {{1, upgradeVersion, 1, 2, 2}, {1, upgradeVersion, 1, 3, 2}, {1, upgradeVersion, 2, 3, 2},
        {1, upgradeVersion, 2, 3, 4}, {1, upgradeVersion, 3, 4, 3}, {1, upgradeVersion, 4, 5, 4},
        {1, upgradeVersion, 4, 5, 8}, {1, upgradeVersion, 8, 9, 8}};
    int32_t records5[][6] = {{2, upgradeVersion, 1, 2, 12, 102}, {2, upgradeVersion, 1, 3, 12, 102},
        {1, upgradeVersion, 2, 3, 12, 102}, {1, upgradeVersion, 2, 3, 14, 104}, {2, upgradeVersion, 3, 4, 13, 103},
        {1, upgradeVersion, 4, 5, 14, 104}, {1, upgradeVersion, 4, 5, 18, 108}, {2, upgradeVersion, 8, 9, 18, 108}};
    int32_t records6[][5] = {{1, upgradeVersion, 1, 2, 12}, {1, upgradeVersion, 1, 3, 12},
        {1, upgradeVersion, 2, 3, 12}, {1, upgradeVersion, 2, 3, 14}, {1, upgradeVersion, 3, 4, 13},
        {1, upgradeVersion, 4, 5, 14}, {1, upgradeVersion, 4, 5, 18}, {1, upgradeVersion, 8, 9, 18}};
    ScanAndCheckTable(syncStmt, "inp6", (int32_t *)records4, ELEMENT_COUNT(records4));
    ScanAndCheckTable(syncStmt, "mid4", (int32_t *)records5, ELEMENT_COUNT(records5));
    ScanAndCheckTable(syncStmt, "out6", (int32_t *)records6, ELEMENT_COUNT(records6));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));
    ScanAndCheckTable(syncStmt, "inp6", (int32_t *)records1, ELEMENT_COUNT(records1));
    ScanAndCheckTable(syncStmt, "mid4", (int32_t *)records2, ELEMENT_COUNT(records2));
    ScanAndCheckTable(syncStmt, "out6", (int32_t *)records3, ELEMENT_COUNT(records3));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
    if (param == 1) {
        RemoveBlockStmtInPatchFile(deltaFileName);
    }
}

// 修改规则join条件，涉及重做的表包含作为中间表的transient tuple表
TEST_F(ClientStExecUpgradeDatalog, Redo_mid_transientTuple)
{
    char *fileName = (char *)"client_st_upgrade_redo_mid_transient";
    char *afterUpgradeFileName = (char *)"client_st_upgrade_redo_mid_transient_after";
    const char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmd(afterUpgradeFileName, defaultNsp));

    Status ret = GMERR_OK;
    // 1.向v0版本和升级后版本中插入n条相同随机数据
    int32_t records[][5] = {{6, 0, 6, 2, 6}, {1, 0, 7, 9, 2}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records, 2, true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA_Copy", (int32_t *)records, 2, true);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t afterInsertCnt = 0;
    EXPECT_EQ(GMERR_OK, GetTableCount(syncStmt, "inpA", &afterInsertCnt));

    // 2.手动删除v0版本数据，模拟redo第一步，无法恢复out表状态（transient（finish）、transient（tuple）无法确保回滚后数据一致
    int32_t records2[][5] = {{-6, 0, 6, 2, 6}, {-1, 0, 7, 9, 2}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records2, 2, true);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t afterDeleteCnt = 0;
    EXPECT_EQ(GMERR_OK, GetTableCount(syncStmt, "outA", &afterDeleteCnt));
    EXPECT_NE((int32_t)afterInsertCnt, (int32_t)afterDeleteCnt);

    // 3.升级v0到v1
    char *deltaFileName = (char *)"client_st_upgrade_redo_mid_transient_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_redo_mid_transient_rule";
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, deltaFileName, defaultNsp));

    // 4.手动插入v1版本数据，模拟redo第二步，由于无法恢复out表状态（transient（finish）、transient（tuple）无法确保回滚后数据一致
    CheckUpgradeSuccess(fileName, 10);
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records, 2, true);
    EXPECT_EQ(GMERR_OK, ret);

    // 5.与直接向升级后的.d写入数据，输出表数据并不一致
    EXPECT_EQ(GMERR_OK, CheckTwoTableDataCntEqual(syncStmt, "inpA", "inpA_Copy", defaultNsp));
    EXPECT_NE(GMERR_OK, CheckTwoTableDataCntEqual(syncStmt, "outA", "outA_Copy", defaultNsp));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    EXPECT_EQ(GMERR_OK, execUninstallCmd(afterUpgradeFileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 修改规则join条件，涉及重做的表包含作为中间表的transient finish表，检查重做后finish表的数据是否为0
TEST_F(ClientStExecUpgradeDatalog, Redo_mid_transientFinish)
{
    char *fileName = (char *)"client_st_upgrade_redo_mid_transient_finish";
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));
    Status ret = GMERR_OK;
    // 1.向v0版本和升级后版本中插入n条相同随机数据
    int32_t records[][5] = {{1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 4}, {1, 0, 5, 5, 5}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records, 2, true);
    EXPECT_EQ(GMERR_OK, ret);

    // 2.升级v0到v1
    char *deltaFileName = (char *)"client_st_upgrade_redo_mid_transient_finish_patch";
    char *deltaOutputFileName = (char *)"client_st_upgrade_redo_mid_transient_finish_full";
    char *fileWithRuleName = (char *)"client_st_upgrade_redo_mid_transient_finish_rule";
    char *upgradeSoName = (char *)"client_st_upgrade_redo_mid_transient_finish_patch";
    char *rollbackSoName = (char *)"client_st_upgrade_redo_mid_transient_finish_rollback";
    ExecPrepare4RollbackUpgradeWithOutputFile(
        fileWithRuleName, deltaFileName, deltaOutputFileName, upgradeSoName, rollbackSoName);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));

    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));
    // 3.redo完成finsh表数据为空
    uint64_t afterRedoCnt = 0;
    EXPECT_EQ(GMERR_OK, GetTableCount(syncStmt, "midA", &afterRedoCnt));
    EXPECT_EQ((uint64_t)0, afterRedoCnt);

    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));

    int32_t recordsDel[][5] = {
        {-1, 0, 1, 1, 1}, {-1, 0, 2, 2, 2}, {-1, 0, 3, 3, 3}, {-1, 0, 4, 4, 4}, {-1, 0, 5, 5, 5}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)recordsDel, 2, true);
    EXPECT_EQ(GMERR_OK, ret);
    CheckMemSizeForMultiLoadHotPatch(defaultNsp, fileName, 10, 2, upgradeSoName, rollbackSoName);

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 外部表重做
// 修改规则join条件，涉及重做的表包含外部表，随机写入数据，redo完成后，与直接写升级后的so预期结果一致
// 随机数据确实出现过redo失败，换成预期冲突的数据无失败。。。。因为执行redo的删除事务时删除了外部表中的那条冲突数据
TEST_P(ClientStExecUpgradeDatalog, Redo_Output_External)
{
    // block配置 0:block, 1:unblock
    int para = GetParam().intValue == 0 ? 0 : 1;
    char blockCmd[1024] = {0};
    (void)sprintf_s(blockCmd, sizeof(blockCmd), "gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", para);
    system(blockCmd);

    char *fileName = (char *)"client_st_upgrade_redo_external";
    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    // 0.创建外部表,加载so
    CreateExternalVertexLabel(syncStmt, defaultNsp);
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");

    // 1.插入数据,预期重做过程发生主键冲突
    int32_t recordsA[][5] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 1}};
    int32_t recordsB[][5] = {{1, 0, 1, 1, 1}, {1, 0, 1, 3, 1}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)recordsA, ELEMENT_COUNT(recordsA), true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpB", (int32_t *)recordsB, ELEMENT_COUNT(recordsB), true);
    EXPECT_EQ(GMERR_OK, ret);
    printf("=================before: record External================\n");
    system("gmsysview record External");
    // 3.升级v0到v1
    char *deltaFileName = (char *)"client_st_upgrade_redo_external_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_redo_external_rule";
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, deltaFileName, defaultNsp));

    // 4.重做将会出现主键冲突，导致重做失败
    CheckUpgradeSuccess(fileName, 5);
    const char *sysviewCmd1 = "gmsysview -q  V\\$PTL_DATALOG_PATCH_INFO";
    const char *matchStr1[] = {"PATCH_STATE: REDO_FAIL"};
    ret = StExecuteCommandWithMatch((char *)sysviewCmd1, matchStr1, ELEMENT_COUNT(matchStr1));
    EXPECT_EQ(GMERR_OK, ret);
    system("gmsysview -q  V\\$PTL_DATALOG_PATCH_INFO");

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "External"));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 外部表重做，修改规则join条件，无数据冲突的场景下redo成功，预期数据正确，外部表fake表无法查询upgradeversion，对应的db表也没有upgradeversion字段
TEST_P(ClientStExecUpgradeDatalog, Redo_Output_External_Success)
{
    // block配置 0:block, 1:unblock
    int para = GetParam().intValue == 0 ? 0 : 1;
    char blockCmd[1024] = {0};
    (void)sprintf_s(blockCmd, sizeof(blockCmd), "gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", para);
    system(blockCmd);

    char *fileName = (char *)"client_st_upgrade_redo_external";
    char *afterUpgradeFileName = (char *)"client_st_upgrade_redo_external_after";
    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    // 0.创建外部表,加载so
    CreateExternalVertexLabel(syncStmt, defaultNsp);
    CreateExternalCopyVertexLabel(syncStmt, defaultNsp);
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmd(afterUpgradeFileName, defaultNsp));

    // 1.向v0版本和升级后版本中插入相同数据
    int32_t recordsA[][5] = {{1, 0, 1, 1, 1}, {1, 0, 0, 2, 2}, {1, 0, 2, 3, 3}};
    int32_t recordsB[][5] = {{1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)recordsA, ELEMENT_COUNT(recordsA), true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpB", (int32_t *)recordsB, ELEMENT_COUNT(recordsB), true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA_Copy", (int32_t *)recordsA, ELEMENT_COUNT(recordsA), true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpB_Copy", (int32_t *)recordsB, ELEMENT_COUNT(recordsB), true);
    EXPECT_EQ(GMERR_OK, ret);
    // 3.升级v0到v1
    char *deltaFileName = (char *)"client_st_upgrade_redo_external_patch";
    char *deltaOutputFileName = (char *)"client_st_upgrade_redo_external_full";
    char *fileWithRuleName = (char *)"client_st_upgrade_redo_external_rule";
    char *upgradeSoName = (char *)"client_st_upgrade_redo_external_patch";
    char *rollbackSoName = (char *)"client_st_upgrade_redo_external_rollback";
    ExecPrepare4RollbackUpgradeWithOutputFile(
        fileWithRuleName, deltaFileName, deltaOutputFileName, upgradeSoName, rollbackSoName);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));

    // 4.重做完成比较数据cnt是否一致
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 10));
    system("gmsysview -q  V\\$PTL_DATALOG_PATCH_INFO");
    EXPECT_EQ(GMERR_OK, CheckTwoTableDataCntEqual(syncStmt, "inpA", "inpA_Copy", defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckTwoTableDataCntEqual(syncStmt, "inpB", "inpB_Copy", defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckTwoTableDataCntEqual(syncStmt, "External", "External_Copy", defaultNsp));

    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));
    int32_t recordsDelA[][5] = {{-1, 0, 1, 1, 1}, {-1, 0, 0, 2, 2}, {-1, 0, 2, 3, 3}};
    int32_t recordsDelB[][5] = {{-1, 0, 1, 1, 1}, {-1, 0, 2, 2, 2}, {-1, 0, 3, 3, 3}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)recordsDelA, ELEMENT_COUNT(recordsDelA), true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpB", (int32_t *)recordsDelB, ELEMENT_COUNT(recordsDelB), true);
    EXPECT_EQ(GMERR_OK, ret);
    CheckMemSizeForMultiLoadHotPatch(defaultNsp, fileName, 10, 2, upgradeSoName, rollbackSoName);

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    EXPECT_EQ(GMERR_OK, execUninstallCmd(afterUpgradeFileName, defaultNsp));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "External"));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "External_Copy"));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

struct TableInputA {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
};

TEST_P(ClientStExecUpgradeDatalog, Redo_Update_Pubsub)
{
    ClearGmserverLog();
    // block配置 0:unblock, 1:block
    int para = GetParam().intValue == 0 ? 0 : 1;

    char *fileName = (char *)"client_st_upgrade_redo_update_pubsub";
    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));
    // 创建订阅
    GmcSubConfigT config;
    config.subsName = "outC";
    config.configJson = R"(
    {
        "name": "outC",
        "label_name": "outC",
        "events": [{ "type": "insert", "msgTypes":["new object"]}],
        "is_reliable": true
    }
    )";
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        uint16_t failedDataNum = 0;
        uint16_t failedIndexes[128];
        for (bool eof, &isNull = eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            TableInputA input;
            ret = GmcGetVertexPropertyByName(stmt, "a", &input.a, sizeof(input.a), &isNull);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertyByName(stmt, "b", &input.b, sizeof(input.b), &isNull);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertyByName(
                stmt, "dtlReservedCount", &input.dtlReservedCount, sizeof(input.dtlReservedCount), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(
                stmt, "upgradeVersion", &input.upgradeVersion, sizeof(input.upgradeVersion), &isNull);
            EXPECT_EQ(GMERR_OK, ret);

            printf("-----------------------------count: %d, version: %d, a: %d, b: %d\n", input.dtlReservedCount,
                input.upgradeVersion, input.a, input.b);
        }
        GmcRespT *response;
        GmcCreateResp(stmt, &response);
        GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
        GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
        GmcSendResp(stmt, response);
        GmcDestroyResp(stmt, response);
    };
    // 普通DML执行完后，received1次数 +2， block 升级后 +1 +2 ， unblock升级后 +2 +4 +1
    uint32_t received1 = 0;
    Status ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received1);
    EXPECT_EQ(GMERR_OK, ret);

    // 1.插入数据
    int32_t recordsA[][4] = {{1, 0, 1, 1}, {1, 0, 1, 2}};
    int32_t recordsB[][4] = {{1, 0, 1, 1}, {1, 0, 1, 3}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)recordsA, ELEMENT_COUNT(recordsA), true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpB", (int32_t *)recordsB, ELEMENT_COUNT(recordsB), true);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t recordsA1[][4] = {{1, 0, 2, 2}, {1, 0, 2, 3}};
    int32_t recordsB1[][4] = {{1, 0, 2, 2}, {1, 0, 2, 4}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA1", (int32_t *)recordsA1, ELEMENT_COUNT(recordsA1), true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpB1", (int32_t *)recordsB1, ELEMENT_COUNT(recordsB1), true);
    EXPECT_EQ(GMERR_OK, ret);
    // 3.升级v0到v1
    char *deltaFileName = (char *)"client_st_upgrade_redo_update_pubsub_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_redo_update_pubsub_rule";
    if (para == 1) {
        RecoverBlockStmtInPatchFile(deltaFileName);
    }
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, deltaFileName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckBlockMode(para == 1));

    // 4.重做完成
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));
    uint32_t expectReceived = para == 0 ? 9 : 4;
    EXPECT_EQ(received1, expectReceived);
    ret = GmcUnSubscribe(syncStmt, config.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
    if (para == 1) {
        RemoveBlockStmtInPatchFile(deltaFileName);
    }
}

TEST_P(ClientStExecUpgradeDatalog, Redo_Transient_Field)
{
    // block配置 0:block, 1:unblock
    int para = GetParam().intValue == 0 ? 0 : 1;
    char blockCmd[1024] = {0};
    (void)sprintf_s(blockCmd, sizeof(blockCmd), "gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", para);
    system(blockCmd);
    char *fileName = (char *)"client_st_upgrade_redo_transient_field";
    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));

    // 1.插入数据
    int32_t recordsA[][4] = {{1, 0, 1, 1}, {1, 0, 1, 2}, {1, 0, 2, 1}, {1, 0, 2, 2}};
    int32_t recordsB[][4] = {{1, 0, 1, 1}, {1, 0, 1, 2}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)recordsA, ELEMENT_COUNT(recordsA), true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpB", (int32_t *)recordsB, ELEMENT_COUNT(recordsB), true);
    EXPECT_EQ(GMERR_OK, ret);
    // 3.升级v0到v1
    char *deltaFileName = (char *)"client_st_upgrade_redo_transient_field_patch";
    char *deltaOutputFileName = (char *)"client_st_upgrade_redo_transient_field_full";
    char *fileWithRuleName = (char *)"client_st_upgrade_redo_transient_field_rule";
    char *upgradeSoName = (char *)"client_st_upgrade_redo_transient_field_patch";
    char *rollbackSoName = (char *)"client_st_upgrade_redo_transient_field_rollback";
    ExecPrepare4RollbackUpgradeWithOutputFile(
        fileWithRuleName, deltaFileName, deltaOutputFileName, upgradeSoName, rollbackSoName);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));

    // 4.重做将会出现主键冲突，重做成功
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));

    int32_t recordsDeleteA[][4] = {{-1, 0, 1, 1}, {-1, 0, 1, 2}, {-1, 0, 2, 1}, {-1, 0, 2, 2}};
    int32_t recordsDeleteB[][4] = {{-1, 0, 1, 1}, {-1, 0, 1, 2}};
    ret =
        InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)recordsDeleteA, ELEMENT_COUNT(recordsDeleteA), true);
    EXPECT_EQ(GMERR_OK, ret);
    ret =
        InsertInpTableBatch(syncConn, syncStmt, "inpB", (int32_t *)recordsDeleteB, ELEMENT_COUNT(recordsDeleteB), true);
    EXPECT_EQ(GMERR_OK, ret);

    CheckMemSizeForMultiLoadHotPatch(defaultNsp, fileName, 10, 2, upgradeSoName, rollbackSoName);

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

TEST_F(ClientStExecUpgradeDatalog, Redo_Update_Pubsub2)
{
    ClearGmserverLog();

    char *fileName = (char *)"client_st_upgrade_redo_update_pubsub";
    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));
    // 创建订阅
    GmcSubConfigT config;
    config.subsName = "outC";
    config.configJson = R"(
    {
        "name": "outC",
        "label_name": "outC",
        "events": [{ "type": "insert", "msgTypes":["new object"]}],
        "is_reliable": true
    }
    )";
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        uint16_t failedDataNum = 0;
        uint16_t failedIndexes[128];
        for (bool eof, &isNull = eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            TableInputA input;
            ret = GmcGetVertexPropertyByName(stmt, "a", &input.a, sizeof(input.a), &isNull);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertyByName(stmt, "b", &input.b, sizeof(input.b), &isNull);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertyByName(
                stmt, "dtlReservedCount", &input.dtlReservedCount, sizeof(input.dtlReservedCount), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(
                stmt, "upgradeVersion", &input.upgradeVersion, sizeof(input.upgradeVersion), &isNull);
            EXPECT_EQ(GMERR_OK, ret);

            printf("-----------------------------count: %d, version: %d, a: %d, b: %d\n", input.dtlReservedCount,
                input.upgradeVersion, input.a, input.b);
        }
        GmcRespT *response;
        GmcCreateResp(stmt, &response);
        GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
        GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
        GmcSendResp(stmt, response);
        GmcDestroyResp(stmt, response);
    };
    // 普通DML执行完后，received1次数 +2， block 升级后 +1 +2 ， unblock升级后 +2 +4 +1
    uint32_t received1 = 0;
    Status ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received1);
    EXPECT_EQ(GMERR_OK, ret);

    // 1.插入数据
    int32_t recordsA[][4] = {{1, 0, 1, 1}, {1, 0, 1, 2}};
    int32_t recordsB[][4] = {{1, 0, 1, 1}, {1, 0, 1, 3}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)recordsA, ELEMENT_COUNT(recordsA), true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpB", (int32_t *)recordsB, ELEMENT_COUNT(recordsB), true);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t recordsA1[][4] = {{1, 0, 2, 2}, {1, 0, 2, 3}};
    int32_t recordsB1[][4] = {{1, 0, 2, 2}, {1, 0, 2, 4}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpA1", (int32_t *)recordsA1, ELEMENT_COUNT(recordsA1), true);
    EXPECT_EQ(GMERR_OK, ret);
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpB1", (int32_t *)recordsB1, ELEMENT_COUNT(recordsB1), true);
    EXPECT_EQ(GMERR_OK, ret);
    // 3.升级v0到v1
    char *deltaFileName = (char *)"client_st_upgrade_redo_update_pubsub_patch2";
    char *fileWithRuleName = (char *)"client_st_upgrade_redo_update_pubsub_rule";
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, deltaFileName, defaultNsp));

    // 4.重做完成
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));
    EXPECT_EQ((int)received1, 2);
    ret = GmcUnSubscribe(syncStmt, config.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // redo item check
    const char *matchCmd =
        "cat ./002_datalog/client_st_dot_datalog/client_st_upgrade_redo_update_pubsub_patch2_redoItems.txt";
    const char *matchStr[] = {"r0"};
    ret = StExecuteCommandWithMatch((char *)matchCmd, matchStr, ELEMENT_COUNT(matchStr));
    EXPECT_EQ(GMERR_OK, ret);
    const char *nMatchStr1[] = {"inpA"};
    ret = StExecuteCommandWithMatch((char *)matchCmd, nMatchStr1, ELEMENT_COUNT(nMatchStr1));
    EXPECT_NE(GMERR_OK, ret);
    const char *nMatchStr2[] = {"inpB"};
    ret = StExecuteCommandWithMatch((char *)matchCmd, nMatchStr2, ELEMENT_COUNT(nMatchStr2));
    EXPECT_NE(GMERR_OK, ret);
    const char *nMatchStr3[] = {"outC"};
    ret = StExecuteCommandWithMatch((char *)matchCmd, nMatchStr3, ELEMENT_COUNT(nMatchStr3));
    EXPECT_NE(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 重做期间查试图
TEST_P(ClientStExecUpgradeDatalog, Redo_Gmsysview_Concurrent)
{
    // block配置 0:block, 1:unblock
    int para = GetParam().intValue == 0 ? 0 : 1;
    char blockCmd[1024] = {0};
    (void)sprintf_s(blockCmd, sizeof(blockCmd), "gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", para);
    system(blockCmd);

    char *fileName = (char *)"client_st_redo_gmsysview_concurrent";
    char *udfFileName = (char *)"client_st_redo_gmsysview_concurrent";
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));

    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}};

    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    // 修改udf，每条数据sleep(2)
    char *deltaFileName = (char *)"client_st_redo_gmsysview_concurrent_patch";
    char *upgradeUdfFileName = (char *)"client_st_redo_gmsysview_concurrent_patch";
    char *upgradeSoName = (char *)"client_st_redo_gmsysview_concurrent_patch";
    char *fileWithRuleName = (char *)"client_st_redo_gmsysview_concurrent_rule";
    EXPECT_EQ(GMERR_OK,
        execCmd4UpgradeWithUdf(fileWithRuleName, deltaFileName, upgradeUdfFileName, upgradeSoName, defaultNsp));
    // 确保进入重做
    usleep(200 * 1000);
    // 重做期间查视图成功或获取锁超时,无hung死
    system("gmsysview count -ns ylog");
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 6));
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    const char *matchCmd =
        "cat ./002_datalog/client_st_dot_datalog/client_st_redo_gmsysview_concurrent_patch_redoItems.txt";
    const char *matchStr[] = {"inpA", "inpB", "inpC", "inpD", "inpE", "outF", "r4"};
    ret = StExecuteCommandWithMatch((char *)matchCmd, matchStr, ELEMENT_COUNT(matchStr));
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

/*
%version v0.0.0

%table A(a: int8, b: int8, c: int8)
%table B(a: int8, b: int8, c: int8)
%table C(a: int8, b: int8, c: int8)
%table D(a: int8, b: int8, c: int8)
%table E(a: int8, b: int8, c: int8)


B(a, b, c) :- A(a, b, c).
C(a, b, c) :- B(a, b, c).
D(a, b, c) :- C(a, b, c).
E(a, b, c) :- D(a, b, c).

-->
%block 1
%alter rule r0 B(a, b, 1) :- A(a, b, 1).
%alter rule r2 D(a, b, 1) :- C(a, b, 1).
*/
TEST_F(ClientStExecUpgradeDatalog, UpgradeSeriesRuleWithBlockMode)
{
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    char *fileName = (char *)"client_st_upgrade_gmsysview_alter_rule_with_block";
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));

    char *deltaFileName = (char *)"client_st_upgrade_gmsysview_alter_rule_with_block_patch";
    char *upgradeSoName = (char *)"client_st_upgrade_gmsysview_alter_rule_with_block_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_gmsysview_alter_rule_with_block_rule";
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName, defaultNsp));
    // 确保进入重做
    usleep(200 * 1000);
    // 重做期间查视图成功或获取锁超时,无hung死
    system("gmsysview count -ns ylog");
    const char *expectedResults[] = {
        "PATCH_STATE: SUCCESS",
        "  Node[id: 21, name: REDO_TRIGGER_TABLES]\n"
        "    TABLE_NAME: A\n",
    };

    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccessWithExpectedResults(10, expectedResults, ELEMENT_COUNT(expectedResults)));
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 测试用户指定规则名，自动生成规则名会跳过已定义规则名，不影响升级
TEST_F(ClientStExecUpgradeDatalog, UserDefineRuleName)
{
    char *fileName = (char *)"client_st_upgrade_redo_rule_name";
    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    // 创建外部表,加载so
    CreateExternalVertexLabel(syncStmt, defaultNsp);

    // old_file.d -> rule_file.d
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));

    // 插入数据,预期重做过程发生主键冲突
    int32_t recordsA[][5] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 1}};
    int32_t recordsB[][5] = {{1, 0, 1, 1, 1}, {1, 0, 1, 3, 1}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)recordsA, ELEMENT_COUNT(recordsA), true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = InsertInpTableBatch(syncConn, syncStmt, "inpB", (int32_t *)recordsB, ELEMENT_COUNT(recordsB), true);
    EXPECT_EQ(GMERR_OK, ret);
    printf("=================before: record External================\n");
    system("gmsysview record External -ns ylog");

    // 升级v0到v1
    char *deltaFileName = (char *)"client_st_upgrade_redo_rule_name_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_redo_rule_name_rule";
    // rule_file.d + patch.d -> full_file.d
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, deltaFileName, defaultNsp));

    // 重做将会出现主键冲突，导致重做失败
    CheckUpgradeSuccess(fileName, 5);
    const char *sysviewCmd1 = "gmsysview -q  V\\$PTL_DATALOG_PATCH_INFO";
    const char *matchStr1[] = {"PATCH_STATE: REDO_FAIL"};
    ret = StExecuteCommandWithMatch((char *)sysviewCmd1, matchStr1, ELEMENT_COUNT(matchStr1));
    EXPECT_EQ(GMERR_OK, ret);
    system("gmsysview -q  V\\$PTL_DATALOG_PATCH_INFO");

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "External"));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 定义1000条规则，测试指定规则名r999，自动生成规则名是否符合预期
TEST_F(ClientStExecUpgradeDatalog, UserDefineRuleName1000)
{
    char *fileName = (char *)"client_st_upgrade_1000_rules";
    const char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    DtlCreateAndUseNamespace(syncStmt, defaultNsp);
    // old_file.d -> rule_file.d
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));

    // 升级v0到v1
    char *deltaFileName = (char *)"client_st_upgrade_1000_rules_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_1000_rules_rule";
    // rule_file.d + patch.d -> full_file.d
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, deltaFileName, defaultNsp));

    const char *sysviewCmd1 = "gmsysview -q  V\\$PTL_DATALOG_PATCH_INFO";
    const char *matchStr1[] = {"PATCH_STATE: SUCCESS"};
    Status ret = StExecuteCommandWithMatch((char *)sysviewCmd1, matchStr1, ELEMENT_COUNT(matchStr1));
    EXPECT_EQ(GMERR_OK, ret);
    system("gmsysview -q  V\\$PTL_DATALOG_PATCH_INFO");

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 修改AGG所在规则的左表投影条件
TEST_F(ClientStExecUpgradeDatalog, AlterAggRuleWithBlock)
{
    char *fileName = (char *)"client_st_upgrade_alter_agg_rule1";
    char *udfFileName = (char *)"client_st_upgrade_alter_agg_rule1";
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));

    int32_t records1[][5] = {{1, 0, 1, 2, 2}, {1, 0, 1, 3, 2}, {1, 0, 2, 3, 2}, {1, 0, 2, 3, 4}, {1, 0, 3, 4, 3},
        {1, 0, 4, 5, 4}, {1, 0, 4, 5, 8}, {1, 0, 8, 9, 8}};
    int32_t records2[][6] = {{2, 0, 1, 3, 2, 1}, {2, 0, 1, 2, 2, 1}, {1, 0, 2, 3, 2, 1}, {1, 0, 2, 3, 4, 1},
        {1, 0, 4, 5, 4, 1}, {1, 0, 4, 5, 8, 1}, {2, 0, 3, 4, 3, 1}, {2, 0, 8, 9, 8, 1}};
    int32_t records3[][5] = {{1, 0, 1, 3, 2}, {1, 0, 1, 2, 2}, {1, 0, 2, 3, 2}, {1, 0, 4, 5, 4}, {1, 0, 3, 4, 3},
        {1, 0, 8, 9, 8}, {1, 0, 4, 5, 8}, {1, 0, 2, 3, 4}};

    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inp6", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    ScanAndCheckTable(syncStmt, "inp6", (int32_t *)records1, ELEMENT_COUNT(records1));
    ScanAndCheckTable(syncStmt, "mid4", (int32_t *)records2, ELEMENT_COUNT(records2));
    ScanAndCheckTable(syncStmt, "out6", (int32_t *)records3, ELEMENT_COUNT(records3));

    char *deltaFileName = (char *)"client_st_upgrade_alter_agg_rule1_patch2";
    char *deltaOutputFileName = (char *)"client_st_upgrade_alter_agg_rule1_full2";
    char *upgradeSoName = (char *)"client_st_upgrade_alter_agg_rule1_patch2";
    char *rollbackSoName = (char *)"client_st_upgrade_alter_agg_rule1_rollback2";
    char *fileWithRuleName = (char *)"client_st_upgrade_alter_agg_rule1_rule";

    ExecPrepare4RollbackUpgradeWithOutputFile(
        fileWithRuleName, deltaFileName, deltaOutputFileName, upgradeSoName, rollbackSoName);
    CheckMemSizeForMultiLoadHotPatch(defaultNsp, fileName, 10, 2, upgradeSoName, rollbackSoName);
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));
    //  声明block 1时，upgradeVersion不变
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");

    int32_t records4[][6] = {{2, 0, 1, 3, 2, 4}, {2, 0, 1, 2, 2, 4}, {1, 0, 2, 3, 2, 4}, {1, 0, 2, 3, 4, 4},
        {1, 0, 4, 5, 4, 4}, {1, 0, 4, 5, 8, 4}, {2, 0, 3, 4, 3, 4}, {2, 0, 8, 9, 8, 4}};
    ScanAndCheckTable(syncStmt, "inp6", (int32_t *)records1, ELEMENT_COUNT(records1));
    ScanAndCheckTable(syncStmt, "mid4", (int32_t *)records4, ELEMENT_COUNT(records4));
    ScanAndCheckTable(syncStmt, "out6", (int32_t *)records3, ELEMENT_COUNT(records3));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));
    ScanAndCheckTable(syncStmt, "inp6", (int32_t *)records1, ELEMENT_COUNT(records1));
    ScanAndCheckTable(syncStmt, "mid4", (int32_t *)records2, ELEMENT_COUNT(records2));
    ScanAndCheckTable(syncStmt, "out6", (int32_t *)records3, ELEMENT_COUNT(records3));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

void PubsubWriteFileCB(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    cout << "====================== pubsub callback =========================" << endl;
    EXPECT_EQ(1u, info->labelCount);
    uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

    uint16_t failedDataNum = 0;
    uint16_t failedIndexes[128];
    int32_t a, b, c, dtlReservedCount, upgradeVersion;
    for (bool eof, &isNull = eof;; ++received) {
        auto ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }

        ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
        EXPECT_EQ(GMERR_OK, ret);

        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(a), &isNull));

        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "b", &b, sizeof(b), &isNull));

        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "c", &c, sizeof(c), &isNull));

        EXPECT_EQ(GMERR_OK,
            GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(dtlReservedCount), &isNull));

        EXPECT_EQ(GMERR_OK,
            GmcGetVertexPropertyByName(stmt, "upgradeVersion", &upgradeVersion, sizeof(upgradeVersion), &isNull));

        printf("new tuple: a: %d, b: %d, c: %d dtlReservedCount: %d, upgradeVersion: %d\n", a, b, c, dtlReservedCount,
            upgradeVersion);

        FILE *fp = fopen(pubsubFileName.c_str(), "a+");
        if (fp == NULL) {
            printf("open %s error\n", pubsubFileName.c_str());
            break;
        }
        (void)fprintf(fp, "%d %d %d %d %d\n", dtlReservedCount, upgradeVersion, a, b, c);
        (void)fclose(fp);
    }
    // 用户消息创建
    GmcRespT *response;
    GmcCreateResp(stmt, &response);
    GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
    // 用户处理错误信息回填
    GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
    // 用户信息发送
    GmcSendResp(stmt, response);
    // 用户信息销毁
    GmcDestroyResp(stmt, response);
}

/* description:新增udf 修改规则 block/Unblock模式下触发undo&redo 测试数据合并
 * source .d:
 *  outB(a, b, c) :- inpA(a, b, c).
 *  outC(a, b, c) :- inpA(a, b, c).
 *  External(a, b, c) :- inpA(a, b, c).
 *  External_Copy(a, b, c) :- inpA(a, b, c).
 *
 * patch .d
 *  %alter rule r0 outB(a, b, c) :- inpA(a, b, c), doNothing(a, b, c).
 *  %alter rule r1 outC(a, b, c) :- inpA(a, b, c), doNothing(a, b, c).
 *  %alter rule r2 External(a, b, c) :- inpA(a, b, c), doNothing(a, b, c).
 */
TEST_P(ClientStExecUpgradeDatalog, UndoAndRedoDataCombinationByAddFunc)
{
    std::remove(tbmDataFileName.c_str());
    std::remove(pubsubFileName.c_str());
    char *fileName = (char *)"client_st_upgrade_data_combination";
    char *udfFileName = (char *)"client_st_upgrade_data_combination";
    char *defaultNsp = (char *)"public";
    char command[512] = {0};

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    DtlCreateAndUseNamespace(syncStmt, defaultNsp);

    GmcConnT *mySubConn;
    auto &datalogSubConnName = "client st sub for datalog";
    CreateSubConnection(&mySubConn, datalogSubConnName);

    // 创建外部表
    CreateExternalVertexLabel(syncStmt, defaultNsp);
    CreateExternalCopyVertexLabel(syncStmt, defaultNsp);

    // 加载源.d
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));

    // 订阅pubsub表
    auto callback = PubsubWriteFileCB;
    uint32_t received = 0;
    EXPECT_EQ(GMERR_OK, GmcSubscribe(syncStmt, &subConfigC, mySubConn, callback, &received));

    EXPECT_EQ(GMERR_OK, GmcUseNamespace(syncStmt, defaultNsp));

    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    ScanAndCheckTable(syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1));

    char *deltaFileName1 = (char *)"client_st_upgrade_data_combination_patch_byFunc";
    char *deltaOutputFileName1 = (char *)"client_st_upgrade_data_combination_patch_byFunc_full";
    char *newUdfFileName1 = (char *)"client_st_upgrade_data_combination_upgrade_byFunc";
    char *upgradeSoName1 = (char *)"client_st_upgrade_data_combination_patch";
    char *rollbackSoName1 = (char *)"client_st_upgrade_data_combination_rollback_byFunc";
    char *fileWithRuleName1 = (char *)"client_st_upgrade_data_combination_rule";

    // block配置 0:block, 1:unblock
    if (GetParam().intValue == 0) {
        (void)sprintf_s(command, sizeof(command),
            "sed -i 's/%%block 1/%%block 0/g' ./002_datalog/client_st_dot_datalog/%s.d", deltaFileName1);
    } else {
        (void)sprintf_s(command, sizeof(command),
            "sed -i 's/%%block 0/%%block 1/g' ./002_datalog/client_st_dot_datalog/%s.d", deltaFileName1);
    }
    system(command);

    ExecPrepare4RollbackUpgradeWithOutputFileAndUdf(
        fileWithRuleName1, deltaFileName1, deltaOutputFileName1, newUdfFileName1, upgradeSoName1, rollbackSoName1);

    // redo item check
    const char *checkCmd =
        "cat ./002_datalog/client_st_dot_datalog/client_st_upgrade_data_combination_patch_byFunc_redoItems.txt";
    const char *checkStr[] = {"outB", "outC", "fake_External", "r0", "r1", "r2"};
    ret = StExecuteCommandWithMatch((char *)checkCmd, checkStr, ELEMENT_COUNT(checkStr));
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName1, defaultNsp));

    usleep(100 * 1000);  // 等待升级流程重刷数据完成

    // system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));

    // Verify post process push data
    int32_t expectData1[][5] = {{1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}, {1, 0, 1, 1, 1}};
    VerifyPushData(tbmDataFileName, expectData1, ELEMENT_COUNT(expectData1));
    int32_t expectData2[][5] = {{1, 0, 3, 3, 3}, {1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}};
    VerifyPushData(pubsubFileName, expectData2, ELEMENT_COUNT(expectData2));
    EXPECT_EQ(GMERR_OK, CheckTwoTableDataCntEqual(syncStmt, "External", "External_Copy"));

    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));

    // Verify post process push data
    VerifyPushData(tbmDataFileName, expectData1, ELEMENT_COUNT(expectData1));
    VerifyPushData(pubsubFileName, expectData2, ELEMENT_COUNT(expectData2));
    EXPECT_EQ(GMERR_OK, CheckTwoTableDataCntEqual(syncStmt, "External", "External_Copy"));

    char *deltaFileName2 = (char *)"client_st_upgrade_block_data_combination_patch_abnormal_func";
    char *deltaOutputFileName2 = (char *)"client_st_upgrade_block_data_combination_patch_abnormal_func_full";
    char *newUdfFileName2 = (char *)"client_st_upgrade_block_data_combination_abnormal_func_upgrade";
    char *upgradeSoName2 = (char *)"client_st_upgrade_block_data_combination_patch_abnormal_function";
    char *rollbackSoName2 = (char *)"client_st_upgrade_block_data_combination_abnormal_function";

    ExecPrepare4RollbackUpgradeWithOutputFileAndUdf(
        fileWithRuleName1, deltaFileName2, deltaOutputFileName2, newUdfFileName2, upgradeSoName2, rollbackSoName2);

    EXPECT_NE(GMERR_OK, execUpgrade(upgradeSoName2, defaultNsp));

    usleep(100 * 1000);  // 等待升级流程结束
    const char *sysviewCmd1 = "gmsysview -q  V\\$PTL_DATALOG_PATCH_INFO";
    const char *matchStr1[] = {"PATCH_STATE: REDO_FAIL"};
    ret = StExecuteCommandWithMatch((char *)sysviewCmd1, matchStr1, ELEMENT_COUNT(matchStr1));

    // Verify post process push data
    VerifyPushData(tbmDataFileName, expectData1, ELEMENT_COUNT(expectData1));
    VerifyPushData(pubsubFileName, expectData2, ELEMENT_COUNT(expectData2));
    EXPECT_EQ(GMERR_OK, CheckTwoTableDataCntEqual(syncStmt, "External", "External_Copy"));

    EXPECT_EQ(GMERR_OK, GmcUnSubscribe(syncStmt, subConfigC.subsName));
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "External"));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "External_Copy"));
    DestroyConnectionAndStmt(syncConn, syncStmt);
    ASSERT_EQ(GMERR_OK, GmcDisconnect(mySubConn));
    sleep(5);
    std::remove(tbmDataFileName.c_str());
    std::remove(pubsubFileName.c_str());
}

/* description:新增表 修改规则 unblock模式下触发undo&redo 测试数据合并
 * source .d:
 *  outB(a, b, c) :- inpA(a, b, c).
 *  outC(a, b, c) :- inpA(a, b, c).
 *  External(a, b, c) :- inpA(a, b, c).
 *  External_Copy(a, b, c) :- inpA(a, b, c).
 *
 * patch1 .d
 * add %table inpD(a:int4, b:int4, c:int4)
 *
 * patch2 .d
 *  %alter rule r0 outB(a, b, c) :- inpA(a, b, c), inpD(a, b, c).
 *
 * patch3.d
 *  %alter rule r1 outC(a, b, c) :- inpA(a, b, c), inpD(a, b, c).
 */
TEST_F(ClientStExecUpgradeDatalog, UndoAndRedoDataCombinationByAddInputTblInUnBlockMode)
{
    std::remove(tbmDataFileName.c_str());
    std::remove(pubsubFileName.c_str());
    char *fileName = (char *)"client_st_upgrade_unblock_data_combination";
    char *udfFileName = (char *)"client_st_upgrade_unblock_data_combination";
    char *defaultNsp = (char *)"public";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    DtlCreateAndUseNamespace(syncStmt, defaultNsp);
    GmcConnT *mySubConn;
    auto &datalogSubConnName = "client st sub for datalog";
    CreateSubConnection(&mySubConn, datalogSubConnName);

    // 创建外部表
    CreateExternalVertexLabel(syncStmt, defaultNsp);
    CreateExternalCopyVertexLabel(syncStmt, defaultNsp);

    // 加载源.d
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));

    // 订阅pubsub表
    auto callback = PubsubWriteFileCB;
    uint32_t received = 0;
    EXPECT_EQ(GMERR_OK, GmcSubscribe(syncStmt, &subConfigC, mySubConn, callback, &received));

    EXPECT_EQ(GMERR_OK, GmcUseNamespace(syncStmt, defaultNsp));

    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    ScanAndCheckTable(g_stmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1));

    char *deltaFileName1 = (char *)"client_st_upgrade_unblock_data_combination_byAddTblV1_patch";
    char *deltaOutputFileName1 = (char *)"client_st_upgrade_unblock_data_combination_byAddTblV1_patch_full";
    char *upgradeSoName1 = (char *)"client_st_upgrade_unblock_data_combination_patch_v1";
    char *rollbackSoName1 = (char *)"client_st_upgrade_unblock_data_combination_byAddTblV1_rollback";
    char *fileWithRuleName1 = (char *)"client_st_upgrade_unblock_data_combination_rule";

    ExecPrepare4RollbackUpgradeWithOutputFile(
        fileWithRuleName1, deltaFileName1, deltaOutputFileName1, upgradeSoName1, rollbackSoName1);

    // patch1升级 新增表
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName1, defaultNsp));
    usleep(100 * 1000);  // 等待升级流程重刷数据完成
    // system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));

    int32_t records2[][5] = {{1, 1, 1, 1, 1}, {1, 1, 2, 2, 2}, {1, 1, 3, 3, 3}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "inpD", (int32_t *)records2, ELEMENT_COUNT(records2), true);
    EXPECT_EQ(GMERR_OK, ret);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview record inpD -ns public");

    char *deltaFileName2 = (char *)"client_st_upgrade_unblock_data_combination_byAddTblV2_patch";
    char *deltaOutputFileName2 = (char *)"client_st_upgrade_unblock_data_combination_byAddTblV2_patch_full";
    char *upgradeSoName2 = (char *)"client_st_upgrade_unblock_data_combination_patch_V2";
    char *rollbackSoName2 = (char *)"client_st_upgrade_unblock_data_combination_rollback_byAddTblV2";

    ExecPrepare4RollbackUpgradeWithOutputFile(
        deltaOutputFileName1, deltaFileName2, deltaOutputFileName2, upgradeSoName2, rollbackSoName2);

    // patch2升级 修改规则 触发undo&redo
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName2, defaultNsp));
    usleep(100 * 1000);  // 等待升级流程重刷数据完成
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));

    // Verify post process push data
    /* 数据解析：
     * {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}, {1, 0, 1, 1, 1}：
     * 来自第一次dml
     * {-1, 0, 3, 3, 3}, {-1, 0, 1, 1, 1}, {-1, 0, 2, 2, 2}：
     * 来自 inpA重做的Undo, inpA Redo由于inpD的数据未重做导致无法join出数据
     * {1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}：
     * 来自 inpD重做的redo，inpD Undo由于inpA的数据已经是新版本数据无法join出数据
     */
    int32_t expectData1[][5] = {{1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}, {1, 0, 1, 1, 1}, {-1, 0, 3, 3, 3}, {-1, 0, 1, 1, 1},
        {-1, 0, 2, 2, 2}, {1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}};
    VerifyPushData(tbmDataFileName, expectData1, ELEMENT_COUNT(expectData1));
    /*
     * block 0模式下重做规则：outC(a, b, c) :- inpA(a, b, c).
     * 数据合并未触发推送
     */
    int32_t expectData2[][5] = {{1, 0, 3, 3, 3}, {1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}};
    VerifyPushData(pubsubFileName, expectData2, ELEMENT_COUNT(expectData2));
    EXPECT_EQ(GMERR_OK, CheckTwoTableDataCntEqual(syncStmt, "External", "External_Copy"));

    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName2, defaultNsp));
    sleep(2);
    std::remove(tbmDataFileName.c_str());
    std::remove(pubsubFileName.c_str());

    char *deltaFileName3 = (char *)"client_st_upgrade_unblock_data_combination_byAddTblV3_patch";
    char *deltaOutputFileName3 = (char *)"client_st_upgrade_unblock_data_combination_byAddTblV3_patch_full";
    char *upgradeSoName3 = (char *)"client_st_upgrade_unblock_data_combination_patch_V3";
    char *rollbackSoName3 = (char *)"client_st_upgrade_unblock_data_combination_byAddTblV3_rollback";

    ExecPrepare4RollbackUpgradeWithOutputFile(
        deltaOutputFileName1, deltaFileName3, deltaOutputFileName3, upgradeSoName3, rollbackSoName3);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName3, defaultNsp));
    usleep(100 * 1000);  // 等待升级流程重刷数据完成
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));

    /*
     * block 0模式下tbm数据未推送
     */
    EXPECT_EQ(DbFileExist(tbmDataFileName.c_str()), false);
    int32_t expectData3[][5] = {
        {-1, 0, 2, 2, 2}, {-1, 0, 3, 3, 3}, {-1, 0, 1, 1, 1}, {1, 0, 3, 3, 3}, {1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}};
    VerifyPushData(pubsubFileName, expectData3, ELEMENT_COUNT(expectData3));

    EXPECT_EQ(GMERR_OK, GmcUnSubscribe(syncStmt, subConfigC.subsName));
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "External"));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "External_Copy"));
    DestroyConnectionAndStmt(syncConn, syncStmt);
    ASSERT_EQ(GMERR_OK, GmcDisconnect(mySubConn));
    sleep(5);
    std::remove(tbmDataFileName.c_str());
    std::remove(pubsubFileName.c_str());
}

TEST_P(ClientStExecUpgradeDatalog, UndoAndRedoDataCombinationByAlterAgg)
{
    std::remove(tbmDataFileName.c_str());
    std::remove(pubsubFileName.c_str());
    char *fileName = (char *)"client_st_upgrade_data_combination_with_agg";
    char *udfFileName = (char *)"client_st_upgrade_data_combination_with_agg";
    char *defaultNsp = (char *)"public";
    char command[512] = {0};

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    DtlCreateAndUseNamespace(syncStmt, defaultNsp);

    GmcConnT *mySubConn;
    auto &datalogSubConnName = "client st sub for datalog";
    CreateSubConnection(&mySubConn, datalogSubConnName);

    // 创建外部表
    CreateExternalVertexLabelAllFieldPK(syncStmt, defaultNsp);
    CreateExternalCopyVertexLabelAllFieldPK(syncStmt, defaultNsp);

    // 加载源.d
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));

    // 订阅pubsub表
    auto callback = PubsubWriteFileCB;
    uint32_t received = 0;
    EXPECT_EQ(GMERR_OK, GmcSubscribe(syncStmt, &subConfigC, mySubConn, callback, &received));

    EXPECT_EQ(GMERR_OK, GmcUseNamespace(syncStmt, defaultNsp));

    // tbm不会触发数据合并 pubsub会触发数据合并
    int32_t records1[][5] = {{1, 0, 1, 2, 2}, {1, 0, 1, 3, 2}, {1, 0, 2, 3, 2}, {1, 0, 2, 3, 4}, {1, 0, 4, 5, 4},
        {1, 0, 4, 5, 8}, {1, 0, 6, 6, 1}, {1, 0, 6, 6, 2}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    ScanAndCheckTable(syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1));

    system("gmsysview record mid -ns ylog");

    char *deltaFileName1 = (char *)"client_st_upgrade_data_combination_with_agg_patch";
    char *deltaOutputFileName1 = (char *)"client_st_upgrade_data_combination_with_agg_patch_full";
    char *upgradeSoName1 = (char *)"client_st_upgrade_data_combination_with_agg_patch";
    char *rollbackSoName1 = (char *)"client_st_upgrade_data_combination_with_agg_patch_rollback";
    char *fileWithRuleName1 = (char *)"client_st_upgrade_data_combination_with_agg_rule";

    // block配置 0:block, 1:unblock
    if (GetParam().intValue == 0) {
        (void)sprintf_s(command, sizeof(command),
            "sed -i 's/%%block 1/%%block 0/g' ./002_datalog/client_st_dot_datalog/%s.d", deltaFileName1);
    } else {
        (void)sprintf_s(command, sizeof(command),
            "sed -i 's/%%block 0/%%block 1/g' ./002_datalog/client_st_dot_datalog/%s.d", deltaFileName1);
    }
    system(command);

    ExecPrepare4RollbackUpgradeWithOutputFile(
        fileWithRuleName1, deltaFileName1, deltaOutputFileName1, upgradeSoName1, rollbackSoName1);

    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 65");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    // patch1升级 新增表
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName1, defaultNsp));
    usleep(100 * 1000);  // 等待升级流程重刷数据完成
    // system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));
    // redo item check
    const char *checkCmd = "cat "
                           "./002_datalog/client_st_dot_datalog/"
                           "client_st_upgrade_data_combination_with_agg_patch_redoItems.txt";
    const char *matchStr1[] = {"mid", "outB", "outC", "fake_External", "fake_External_Copy", "r0"};
    ret = StExecuteCommandWithMatch((char *)checkCmd, matchStr1, ELEMENT_COUNT(matchStr1));
    EXPECT_EQ(GMERR_OK, ret);

    // Verify post process push data
    // pubsub data combination when upgrading
    int32_t expectData1[][5] = {{1, 0, 4, 5, 4}, {1, 0, 4, 5, 8}, {1, 0, 2, 3, 4}, {1, 0, 2, 3, 2}, {1, 0, 1, 3, 2},
        {1, 0, 6, 6, 1}, {1, 0, 6, 6, 2}, {1, 0, 1, 2, 2}};
    VerifyPushData(pubsubFileName, expectData1, ELEMENT_COUNT(expectData1));
    EXPECT_EQ(GMERR_OK, CheckTwoTableDataCntEqual(syncStmt, "External", "External_Copy"));

    EXPECT_EQ(GMERR_OK, GmcUnSubscribe(syncStmt, subConfigC.subsName));
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "External"));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "External_Copy"));
    DestroyConnectionAndStmt(syncConn, syncStmt);
    ASSERT_EQ(GMERR_OK, GmcDisconnect(mySubConn));
    sleep(5);
    std::remove(tbmDataFileName.c_str());
    std::remove(pubsubFileName.c_str());
}

TEST_P(ClientStExecUpgradeDatalog, UndoAndRedoDataCombinationTransientFinish)
{
    std::remove(tbmDataFileName.c_str());
    std::remove(pubsubFileName.c_str());
    char *fileName = (char *)"client_st_upgrade_data_combination_with_transient_finish";
    char *udfFileName = (char *)"client_st_upgrade_data_combination";
    char *defaultNsp = (char *)"public";
    char command[512] = {0};

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);

    GmcConnT *mySubConn;
    auto &datalogSubConnName = "client st sub for datalog";
    CreateSubConnection(&mySubConn, datalogSubConnName);

    // 创建外部表
    CreateExternalVertexLabel(syncStmt, defaultNsp);
    CreateExternalCopyVertexLabel(syncStmt, defaultNsp);

    // 加载源.d
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName));

    // 订阅pubsub表
    auto callback = PubsubWriteFileCB;
    uint32_t received = 0;
    EXPECT_EQ(GMERR_OK, GmcSubscribe(syncStmt, &subConfigC, mySubConn, callback, &received));

    EXPECT_EQ(GMERR_OK, GmcUseNamespace(syncStmt, defaultNsp));

    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    ScanAndCheckTable(g_stmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1));

    char *deltaFileName1 = (char *)"client_st_upgrade_data_combination_with_transient_finish_patch";
    char *deltaOutputFileName1 = (char *)"client_st_upgrade_data_combination_with_transient_finish_patch_full";
    char *upgradeSoName1 = (char *)"client_st_upgrade_data_combination_with_transient_finish_patch";
    char *rollbackSoName1 = (char *)"client_st_upgrade_data_combination_with_transient_finish_patch_rollback";
    char *fileWithRuleName1 = (char *)"client_st_upgrade_data_combination_with_transient_finish_rule";
    // block配置 0:block, 1:unblock
    if (GetParam().intValue == 0) {
        (void)sprintf_s(command, sizeof(command),
            "sed -i 's/%%block 1/%%block 0/g' ./002_datalog/client_st_dot_datalog/%s.d", deltaFileName1);
    } else {
        (void)sprintf_s(command, sizeof(command),
            "sed -i 's/%%block 0/%%block 1/g' ./002_datalog/client_st_dot_datalog/%s.d", deltaFileName1);
    }
    system(command);

    ExecPrepare4RollbackUpgradeWithOutputFile(
        fileWithRuleName1, deltaFileName1, deltaOutputFileName1, upgradeSoName1, rollbackSoName1);

    // patch1升级 新增表
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName1));
    usleep(100 * 1000);  // 等待升级流程重刷数据完成
    // system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));

    // redo item check
    const char *checkCmd = "cat "
                           "./002_datalog/client_st_dot_datalog/"
                           "client_st_upgrade_data_combination_with_transient_finish_patch_redoItems.txt";
    const char *matchStr1[] = {"transient_finish", "outB", "outC", "fake_External", "fake_External_Copy", "r0"};
    ret = StExecuteCommandWithMatch((char *)checkCmd, matchStr1, ELEMENT_COUNT(matchStr1));
    EXPECT_EQ(GMERR_OK, ret);

    // Verify post process push data
    int32_t expectData1[][5] = {{1, 0, 2, 2, 2}, {1, 0, 1, 1, 1}, {1, 0, 3, 3, 3}, {1, 0, 3, 3, 1}, {1, 0, 2, 2, 1}};
    VerifyPushData(tbmDataFileName, expectData1, ELEMENT_COUNT(expectData1));
    int32_t expectData2[][5] = {{1, 0, 3, 3, 3}, {1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {-1, 0, 3, 3, 3}, {1, 0, 3, 3, 1},
        {-1, 0, 2, 2, 2}, {1, 0, 2, 2, 1}};
    VerifyPushData(pubsubFileName, expectData2, ELEMENT_COUNT(expectData2));
    EXPECT_EQ(GMERR_OK, CheckTwoTableDataCntEqual(syncStmt, "External", "External_Copy"));

    EXPECT_EQ(GMERR_OK, GmcUnSubscribe(g_stmt, subConfigC.subsName));
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "External"));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "External_Copy"));
    DestroyConnectionAndStmt(syncConn, syncStmt);
    ASSERT_EQ(GMERR_OK, GmcDisconnect(mySubConn));
    sleep(5);
    // std::remove(tbmDataFileName.c_str());
    // std::remove(pubsubFileName.c_str());
}

/*
%version v0.0.0

%table A(a: int8, b: int8, c: int8)
%table B(a: int8, b: int8, c: int8)
%table C(a: int8, b: int8, c: int8)
%table D(a: int8, b: int8, c: int8)


C(a, b, c) :- A(a, b, c).
C(a, b, c) :- B(a, b, c).
D(a, b, c) :- C(a, b, c).

-->
%block 1
%alter rule r0 C(10, b, c) :- A(1, b, c).
*/
TEST_F(ClientStExecUpgradeDatalog, UpgradeRelatedRuleWithBlockMode)
{
    const char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    DtlCreateAndUseNamespace(syncStmt, defaultNsp);

    char *fileName = (char *)"client_st_upgrade_alter_related_rule_with_block";
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));

    int32_t records1[][5] = {{1, 0, 1, 1, 1}};

    Status ret = InsertInpTableBatch(syncConn, syncStmt, "A", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t records2[][5] = {{1, 0, 2, 2, 2}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "B", (int32_t *)records2, ELEMENT_COUNT(records2), true);
    EXPECT_EQ(GMERR_OK, ret);

    char *deltaFileName = (char *)"client_st_upgrade_alter_related_rule_with_block_patch";
    char *upgradeSoName = (char *)"client_st_upgrade_alter_related_rule_with_block_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_alter_related_rule_with_block_rule";
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName, defaultNsp));
    const char *expectedResults[] = {
        "PATCH_STATE: SUCCESS",
        "  Node[id: 21, name: REDO_TRIGGER_TABLES]\n"
        "    TABLE_NAME: A\n",
    };

    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccessWithExpectedResults(10, expectedResults, ELEMENT_COUNT(expectedResults)));
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);

    // redo item check
    const char *checkCmd =
        "cat ./002_datalog/client_st_dot_datalog/client_st_upgrade_alter_related_rule_with_block_patch_redoItems.txt";
    const char *matchStr1[] = {"C", "D", "r0"};
    ret = StExecuteCommandWithMatch((char *)checkCmd, matchStr1, ELEMENT_COUNT(matchStr1));
    EXPECT_EQ(GMERR_OK, ret);
}

/*
 * ### 非阻塞升级模式下 , 普通函数中访问access_delta/access_current , undo/redo读取不同的的版本记录 #########
 * %version v0.0.0
 * %table inpA(a: int4, b: int4)
 * %table middleB(a: int4, b: int4)
 * %table middleC(a: int4, b: int4, c: int4)
 * %table outD(a: int4, b: int4, c: int4)
 * %function func(a:int4, b:int4 -> c:int4){access_current(inpA) , access_delta(outD)}
 *
 * middleB(a, b) :- inpA(a, b).
 * middleC(a, b, c) :- middleB(a, b) , func(a , b, c).
 * outD(a, b, c) :- middleC(a, b , c).
 *
 * ----升级为---->
 * block = 0
 * middleB(a, b) :- inpA(a, b).
 * middleC(a, b, c) :- middleB(a, b) , func(a , b, c).
 * outD(a, b, c) :- middleC(a, b , c).
 */
TEST_F(ClientStExecUpgradeDatalog, DISABLED_UpgradeFuncAccessCurrentWithUnblockMode)
{
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 100");
    char *fileName = (char *)"client_st_upgrade_func_access_current_unblock";
    char *defaultNsp = (char *)"public";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    ASSERT_EQ(GMERR_OK, GmcUseNamespace(syncStmt, defaultNsp));

    ASSERT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName));

    uint32_t tupleCnt = 200;
    TableTwoField tuples[tupleCnt];
    for (uint32_t i = 0; i < tupleCnt; i++) {
        TableTwoField *tuple = &tuples[i];

        tuple->dtlReservedCount = 1;
        tuple->upgradeVersion = i;
        tuple->a = i;
        tuple->b = i;
    }

    // insert basic datas
    ASSERT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpA, (void *)tuples, tupleCnt, false));

    // cout << "before upgrade inpA" << endl;
    // ScanOutTable(syncStmt , inpA);

    // cout << "before upgrade outD" << endl;
    // ScanOutTable(syncStmt , outD);

    // upgrade
    char *deltaFileName = (char *)"client_st_upgrade_func_access_current_unblock_upgrade";
    char *upgradeSoName = (char *)"client_st_upgrade_func_access_current_unblock_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_func_access_current_unblock_rule";
    ASSERT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    // cout << "after upgrade inpA" << endl;
    // ScanOutTable(syncStmt , inpA);

    // cout << "after upgrade outD" << endl;
    // ScanOutTable(syncStmt , outD);

    ASSERT_EQ(GMERR_OK, execUninstallCmd(fileName));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

/*
 * ### 非阻塞升级模式下 , 聚合多对一中访问access_delta/access_current , undo/redo读取不同的的版本记录 #########
 * %version v0.0.0
 * %table inpA(a: int4, b: int4)
 * %table middleB(a: int4, b: int4)
 * %table middleC(a: int4, count: int4)
 * %table outD(a: int4, count: int4)
 * %aggregate agg_many_to_one(b:int4 -> count:int4){access_current(inpA) , access_delta(outD)}
 *
 * middleB(a, b) :- inpA(a, b).
 * middleC(a, count) :- middleB(a, b) GROUP-BY(a) agg_many_to_one(b, count).
 * outD(a, count) :- middleC(a, count).
 *
 * ----升级为---->
 *
 * block = 0
 * middleB(a, b) :- inpA(a, b).
 * middleC(a, count) :- middleB(a, b) GROUP-BY(a) agg_many_to_one(b, count).
 * outD(a, count) :- middleC(a, count).
 */
TEST_F(ClientStExecUpgradeDatalog, DISABLED_UpgradeAggManyToOneAccessCurrentWithUnblockMode)
{
    char *fileName = (char *)"client_st_upgrade_agg_many_to_one_access_current_unblock";
    char *defaultNsp = (char *)"public";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    ASSERT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    ASSERT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    // insert basic datas
    uint32_t tupleCnt = 200;
    TableTwoField tuples[tupleCnt];
    for (uint32_t i = 0; i < tupleCnt; i++) {
        TableTwoField *tuple = &tuples[i];

        tuple->dtlReservedCount = 1;
        tuple->upgradeVersion = i;
        tuple->a = i;
        tuple->b = i;
    }

    ASSERT_EQ(GMERR_OK, InsertBatchTableWithStruct(syncConn, syncStmt, inpA, (void *)tuples, tupleCnt, false));
    // cout << "======================before upgrade inpA =========================" << endl;
    // ScanOutTable(syncStmt, inpA);

    // cout << "======================before upgrade outD =========================" << endl;
    // ScanOutTable(syncStmt, outD);

    // upgrade
    char *deltaFileName = (char *)"client_st_upgrade_agg_many_to_one_access_current_unblock_upgrade";
    char *upgradeSoName = (char *)"client_st_upgrade_agg_many_to_one_access_current_unblock_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_agg_many_to_one_access_current_unblock_rule";
    ASSERT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    // cout << "======================after upgrade inpA =========================" << endl;
    // ScanOutTable(syncStmt, inpA);

    // cout << "======================after upgrade outD =========================" << endl;
    // ScanOutTable(syncStmt, outD);

    ASSERT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 无access delta和access current语法的function
TEST_F(ClientStExecUpgradeDatalog, Udf_WithOut_Access_Delta_And_Access_Current)
{
    ClearGmserverLog();
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    char *fileName = (char *)"client_st_upgrade_udf_without_access";
    char *udfName = (char *)"client_st_upgrade_udf_without_access_udf";
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfName, defaultNsp));

    char *deltaFileName = (char *)"client_st_upgrade_udf_without_access_patch1";
    char *upgradSoName = (char *)"client_st_upgrade_udf_without_access_patch1";
    char *fileWithRuleName = (char *)"client_st_upgrade_udf_without_access_rule";
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, upgradSoName, defaultNsp));
    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 5));
    EXPECT_EQ(GMERR_OK, CheckBlockMode(false));
    const char *expectedResults[] = {
        "PATCH_STATE: SUCCESS",
        "  Node[id: 21, name: REDO_TRIGGER_TABLES]\n"
        "    TABLE_NAME: A1\n"
        "  Node[id: 22, name: REDO_RELATED_TABLES]\n"
        "    TABLE_NAME: A1\n"
        "    ------------------\n"
        "    TABLE_NAME: B1\n",
    };

    EXPECT_EQ(GMERR_OK, CheckUpgradeSuccessWithExpectedResults(10, expectedResults, ELEMENT_COUNT(expectedResults)));
    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

#ifndef ASAN
static void *InsertLabelDml(void *arg)
{
    char *params = (char *)arg;
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    DtlCreateAndUseNamespace(syncStmt, defaultNsp);
    int32_t records[][5] = {
        {1, 0, 7, 7, 7}, {1, 0, 8, 8, 8}, {1, 0, 9, 9, 9}, {1, 0, 10, 10, 10}, {1, 0, 11, 11, 11}, {1, 0, 12, 12, 12}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, params, (int32_t *)records, ELEMENT_COUNT(records), true);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, syncStmt);
    return NULL;
}

static void *DeleteLabelDml(void *arg)
{
    char *params = (char *)arg;
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    DtlCreateAndUseNamespace(syncStmt, defaultNsp);
    int32_t records[][5] = {
        {-1, 0, 7, 7, 7}, {-1, 0, 8, 8, 8}, {-1, 0, 9, 9, 9}, {-1, 0, 10, 10, 10}, {-1, 0, 11, 11, 11}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, params, (int32_t *)records, ELEMENT_COUNT(records), true);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, syncStmt);
    return NULL;
}

TEST_F(ClientStExecUpgradeDatalog, UnBlockConcurrentUpgradeGetLock)
{
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");
    char *fileName = (char *)"client_st_upgrade_unblock_concurrent_get_lock";
    char *udfFileName = (char *)"client_st_upgrade_unblock_concurrent_get_lock";
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));

    char *inputTableName1 = (char *)"inpA";
    char *inputTableName2 = (char *)"inpB";

    int32_t records[][5] = {
        {1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 4}, {1, 0, 5, 5, 5}, {1, 0, 6, 6, 6}};
    Status ret =
        InsertInpTableBatch(syncConn, syncStmt, inputTableName1, (int32_t *)records, ELEMENT_COUNT(records), true);
    EXPECT_EQ(GMERR_OK, ret);
    const char *command1 = "gmadmin -cfgName workerHungThreshold";
    const char *matchStr1[] = {"config current value: 3,4,5"};
    ret = StExecuteCommandWithMatch((char *)command1, matchStr1, ELEMENT_COUNT(matchStr1));

    char *deltaFileName1 = (char *)"client_st_upgrade_unblock_concurrent_get_lock_patchV1";
    char *deltaOutputFileName1 = (char *)"client_st_upgrade_unblock_concurrent_get_lock_patchV1_full";
    char *deltaUdfFileName1 = (char *)"client_st_upgrade_unblock_concurrent_get_lock_patchV1";
    char *upgradeSoName1 = (char *)"client_st_upgrade_unblock_concurrent_get_lock_patchV1";
    char *fileWithRuleName1 = (char *)"client_st_upgrade_unblock_concurrent_get_lock_rule";
    char *rollbackSoName1 = (char *)"client_st_upgrade_unblock_concurrent_get_lock_rollback";

    ExecPrepare4RollbackUpgradeWithOutputFileAndUdf(
        fileWithRuleName1, deltaFileName1, deltaOutputFileName1, deltaUdfFileName1, upgradeSoName1, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName1, defaultNsp));
    sleep(1);

    // block = 0 && enableDatalogDmlWhenUpgrading = 0升级其中一个topo 并发对另一个topo进行DML 预期DML和升级都成功
    pthread_t insertDml;
    EXPECT_EQ(GMERR_OK, pthread_create(&insertDml, NULL, InsertLabelDml, (void *)inputTableName2));
    pthread_join(insertDml, NULL);
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    int32_t expectData1[][5] = {
        {1, 1, 1, 1, 1}, {1, 1, 2, 2, 2}, {1, 1, 3, 3, 3}, {1, 1, 4, 4, 4}, {1, 1, 5, 5, 5}, {1, 1, 6, 6, 6}};
    int32_t expectData2[][5] = {
        {1, 0, 7, 7, 7}, {1, 0, 8, 8, 8}, {1, 0, 9, 9, 9}, {1, 0, 10, 10, 10}, {1, 0, 11, 11, 11}, {1, 0, 12, 12, 12}};
    ScanAndCheckTable(syncStmt, inputTableName1, (int32_t *)expectData1, ELEMENT_COUNT(expectData1));
    ScanAndCheckTable(syncStmt, inputTableName2, (int32_t *)expectData2, ELEMENT_COUNT(expectData2));

    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");

    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    sleep(2);

    // block = 0 && enableDatalogDmlWhenUpgrading = 1升级其中一个topo 并发对另一个topo进行DML 预期DML和升级都成功
    pthread_t deleteDml;
    EXPECT_EQ(GMERR_OK, pthread_create(&deleteDml, NULL, DeleteLabelDml, (void *)inputTableName2));
    pthread_join(deleteDml, NULL);
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    int32_t expectData3[][5] = {{1, 0, 12, 12, 12}};
    ScanAndCheckTable(syncStmt, inputTableName1, (int32_t *)records, ELEMENT_COUNT(records));
    ScanAndCheckTable(syncStmt, inputTableName2, (int32_t *)expectData3, ELEMENT_COUNT(expectData3));

    // block = 0 && enableDatalogDmlWhenUpgrading = 1 升级和DML并发，升级sleep 1s 预期升级和DML都成功
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName1, defaultNsp));
    sleep(1);
    EXPECT_EQ(GMERR_OK, pthread_create(&insertDml, NULL, InsertLabelDml, (void *)inputTableName1));
    pthread_join(insertDml, NULL);
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    int32_t expectData4[][5] = {{1, 1, 1, 1, 1}, {1, 1, 2, 2, 2}, {1, 1, 3, 3, 3}, {1, 1, 4, 4, 4}, {1, 1, 5, 5, 5},
        {1, 1, 6, 6, 6}, {1, 1, 7, 7, 7}, {1, 1, 8, 8, 8}, {1, 1, 9, 9, 9}, {1, 1, 10, 10, 10}, {1, 1, 11, 11, 11},
        {1, 1, 12, 12, 12}};
    ScanAndCheckTable(syncStmt, inputTableName1, (int32_t *)expectData4, ELEMENT_COUNT(expectData4));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}
#endif

TEST_F(ClientStExecUpgradeDatalog, UnBlockUpgradeWithPrecedenceAndAccessDelta)
{
    char *fileName = (char *)"client_st_upgrade_unblock_with_precedence_and_access";
    char *udfFileName = (char *)"client_st_upgrade_unblock_with_precedence_and_access";
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    DtlCreateAndUseNamespace(syncStmt, defaultNsp);
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));

    char *inputTableName1 = (char *)"inpA";
    char *inputTableName2 = (char *)"inpB";
    char *outputTableName1 = (char *)"outB";
    int32_t records1[][5] = {{1, 0, 1, 1, 1}};
    Status ret =
        InsertInpTableBatch(syncConn, syncStmt, inputTableName1, (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = InsertInpTableBatch(syncConn, syncStmt, inputTableName2, (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);

    char *deltaFileName1 = (char *)"client_st_upgrade_unblock_with_precedence_and_access_patchV1";
    char *deltaOutputFileName1 = (char *)"client_st_upgrade_unblock_with_precedence_and_access_patchV1_full";
    char *deltaUdfFileName1 = (char *)"client_st_upgrade_unblock_with_precedence_and_access_patchV1";
    char *upgradeSoName1 = (char *)"client_st_upgrade_unblock_with_precedence_and_access_patchV1";
    char *fileWithRuleName1 = (char *)"client_st_upgrade_unblock_with_precedence_and_access_rule";
    char *rollbackSoName1 = (char *)"client_st_upgrade_unblock_with_precedence_and_access_rollback";

    ExecPrepare4RollbackUpgradeWithOutputFileAndUdf(
        fileWithRuleName1, deltaFileName1, deltaOutputFileName1, deltaUdfFileName1, upgradeSoName1, rollbackSoName1);

    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 65");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName1, defaultNsp));

    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    int32_t expectData1[][5] = {{1, 1, 1, 1, 1}};
    ScanAndCheckTable(syncStmt, inputTableName1, (int32_t *)expectData1, ELEMENT_COUNT(expectData1));
    ScanAndCheckTable(syncStmt, outputTableName1, (int32_t *)expectData1, ELEMENT_COUNT(expectData1));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

TEST_F(ClientStExecUpgradeDatalog, ShowRedoItems)
{
    char *fileName = (char *)"client_st_upgrade_show_redo";
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    DtlCreateAndUseNamespace(syncStmt, defaultNsp);
    ASSERT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    // upgrade
    char *deltaFileName = (char *)"client_st_upgrade_show_redo_patch";
    char *newUdfFileName = (char *)"client_st_upgrade_show_redo_patch";
    char *upgradeSoName = (char *)"client_st_upgrade_show_redo_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_show_redo_rule";
    EXPECT_EQ(
        GMERR_OK, execCmd4UpgradeWithUdf(fileWithRuleName, deltaFileName, newUdfFileName, upgradeSoName, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    // redo item check
    const char *checkCmd = "cat ./002_datalog/client_st_dot_datalog/client_st_upgrade_show_redo_patch_redoItems.txt";
    const char *matchStr1[] = {"tb1 (new table)", "inp5", "inp2", "inp1", "inp4", "inp3", "inp6", "mid3", "mid1",
        "mid2", "rs1", "mid4", "out1", "out2", "out3", "out4", "out5", "out6", "r0", "r2", "r4", "r7"};
    Status ret = StExecuteCommandWithMatch((char *)checkCmd, matchStr1, ELEMENT_COUNT(matchStr1));
    EXPECT_EQ(GMERR_OK, ret);

    ASSERT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

struct TableInputA0 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
};

// 升级中间表顺序，查看pubsub输出数据
TEST_F(ClientStExecUpgradeDatalog, UpgradePrecedence)
{
    char *fileName = (char *)"client_st_pubsub_precedence";
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    DtlCreateAndUseNamespace(syncStmt, defaultNsp);
    ASSERT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));

    GmcSubConfigT config;
    config.subsName = "Pubsub";
    config.configJson = R"(
    {
        "name": "Pubsub1",
        "label_name": "nsp.outSimpleA",
        "events": [{ "type": "insert", "msgTypes":["new object"]}],
        "is_reliable": true
    }
    )";

    GmcSubConfigT config1;
    config1.subsName = "Pubsub1";
    config1.configJson = R"(
    {
        "name": "Pubsub2",
        "label_name": "nsp.outSimpleB",
        "events": [{ "type": "insert", "msgTypes":["new object"]}],
        "is_reliable": true
    }
    )";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        uint16_t failedDataNum = 0;
        uint16_t failedIndexes[128];
        for (bool eof, &isNull = eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            TableInputA0 input;
            ret = GmcGetVertexPropertyByName(stmt, "a", &input.a, sizeof(input.a), &isNull);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertyByName(stmt, "b", &input.b, sizeof(input.b), &isNull);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertyByName(
                stmt, "dtlReservedCount", &input.dtlReservedCount, sizeof(input.dtlReservedCount), &isNull);
            EXPECT_EQ(GMERR_OK, ret);

            printf("a: %d, b: %d, count: %d\n", input.a, input.b, input.dtlReservedCount);
        }
        GmcRespT *response;
        GmcCreateResp(stmt, &response);
        GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
        GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
        GmcSendResp(stmt, response);
        GmcDestroyResp(stmt, response);
    };

    uint32_t received1 = 0;
    Status ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received1);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t received2 = 0;
    EXPECT_EQ(GMERR_OK, GmcSubscribe(syncStmt, &config1, subConn, callback, &received2));

    // insert，此时先打印1，再打印2
    int32_t obj[][5] = {{1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "nsp.inpSimple", *obj, ELEMENT_COUNT(obj), false);
    EXPECT_EQ(GMERR_OK, ret);

    // upgrade
    char *deltaFileName = (char *)"client_st_pubsub_precedence_patch";
    char *upgradeSoName = (char *)"client_st_pubsub_precedence_patch";
    char *fileWithRuleName = (char *)"client_st_pubsub_precedence_rule";
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    // insert after upgrade，此时先打印2，再打印1
    int32_t obj1[][5] = {{1, 0, 10, 1, 1}, {1, 0, 11, 2, 2}, {1, 0, 12, 3, 3}};
    ret = InsertInpTableBatch(syncConn, syncStmt, "nsp.inpSimple", *obj1, ELEMENT_COUNT(obj1), false);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(syncStmt, config.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(syncStmt, config1.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = execUninstallCmd(fileName, defaultNsp);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 新增+修改precedence
TEST_F(ClientStExecUpgradeDatalog, UpgradePrecedenceComplex)
{
    char *fileName = (char *)"client_st_upgrade_precedence_complex";
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    DtlCreateAndUseNamespace(syncStmt, defaultNsp);
    ASSERT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));

    // upgrade
    char *deltaFileName = (char *)"client_st_upgrade_precedence_complex_patch";
    char *upgradeSoName = (char *)"client_st_upgrade_precedence_complex_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_precedence_complex_rule";
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    ASSERT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

void ExecCmd4Upgrade(const char *oldfileName, const char *deltaFileName, const char *soFile, bool isSuccess)
{
    char cmd[DB_INVALID_UINT16];
    int cmdLength = sprintf_s(cmd, DB_INVALID_UINT16,
        "source ../../../../scripts/build_scripts/asan_clean_env.sh && ../../../../build/bin/gmprecompiler -u "
        "./002_datalog/client_st_dot_datalog/%s.d "
        "./002_datalog/client_st_dot_datalog/%s.d "
        "./002_datalog/client_st_dot_datalog/%s.c ./002_datalog/client_st_dot_datalog/%s_full.d && gcc -fPIC "
        "--shared ./002_datalog/client_st_dot_datalog/%s.c -o ./002_datalog/client_st_dot_datalog/%s.so",
        oldfileName, deltaFileName, deltaFileName, deltaFileName, deltaFileName, soFile);
    EXPECT_GT(cmdLength, 0);
    Status ret = system(cmd);
    if (!isSuccess) {
        // 编译失败
        EXPECT_NE(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 新增precedence访问其他nsp中表
TEST_F(ClientStExecUpgradeDatalog, UpgradePrecedenceNsp)
{
    char *fileName = (char *)"HotPatchPre";
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    DtlCreateAndUseNamespace(syncStmt, defaultNsp);
    ASSERT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));

    // upgrade
    char *deltaFileName = (char *)"HotPatchPre_patch";
    char *upgradeSoName = (char *)"HotPatchPre_patch";
    char *fileWithRuleName = (char *)"HotPatchPre_rule";
    ExecCmd4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName, false);

    ASSERT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

// 热升级编译precedence使用外部表
TEST_F(ClientStExecUpgradeDatalog, UpgradePrecedenceExtern)
{
    // upgrade
    char *deltaFileName = (char *)"HotPatchPre_extern_patch";
    char *upgradeSoName = (char *)"HotPatchPre_extern_patch";
    char *fileWithRuleName = (char *)"HotPatchPre_extern_rule";
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    DtlCreateAndUseNamespace(syncStmt, defaultNsp);
    ExecCmd4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName, true);
}

// 热升级加载precedence使用外部表
TEST_P(ClientStExecUpgradeDatalog, UpgradePrecedenceExtern1)
{
    // block配置 0:block, 1:unblock
    int para = GetParam().intValue == 0 ? 0 : 1;
    char blockCmd[1024] = {0};
    (void)sprintf_s(blockCmd, sizeof(blockCmd), "gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", para);
    system(blockCmd);

    char *fileName = (char *)"client_st_upgrade_precedence_external";
    char *defaultNsp = (char *)"ylog";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));
    // 0.创建外部表,加载so
    CreateExternalVertexLabel(syncStmt, defaultNsp);
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");

    printf("=================before: record External================\n");
    system("gmsysview record External -ns ylog");
    // 3.升级v0到v1
    char *deltaFileName = (char *)"client_st_upgrade_precedence_external_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_precedence_external_rule";
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, deltaFileName, defaultNsp));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "External"));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

TEST_F(ClientStExecUpgradeDatalog, UpgradeReadonly)
{
    char *fileName = (char *)"client_st_upgrade_readonly";
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    DtlCreateAndUseNamespace(syncStmt, defaultNsp);
    ASSERT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));

    // upgrade
    char *deltaFileName = (char *)"client_st_upgrade_readonly_patch";
    char *upgradeSoName = (char *)"client_st_upgrade_readonly_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_readonly_rule";
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    ASSERT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

TEST_F(ClientStExecUpgradeDatalog, UpgradeReadonlySingle)
{
    char *fileName = (char *)"client_st_upgrade_readonly_single";
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    DtlCreateAndUseNamespace(syncStmt, defaultNsp);
    ASSERT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));

    // upgrade
    char *deltaFileName = (char *)"client_st_upgrade_readonly_single_patch";
    char *upgradeSoName = (char *)"client_st_upgrade_readonly_single_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_readonly_single_rule";
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    ASSERT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

TEST_F(ClientStExecUpgradeDatalog, UpgradeReadonlyComplex)
{
    char *fileName = (char *)"client_st_upgrade_readonly_complex";
    char *defaultNsp = (char *)"ylog";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    DtlCreateAndUseNamespace(syncStmt, defaultNsp);
    ASSERT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));

    // upgrade
    char *deltaFileName = (char *)"client_st_upgrade_readonly_complex_patch";
    char *upgradeSoName = (char *)"client_st_upgrade_readonly_complex_patch";
    char *fileWithRuleName = (char *)"client_st_upgrade_readonly_complex_rule";
    EXPECT_EQ(GMERR_OK, execCmd4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    ASSERT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
}

// 使用抑制选项，使用%redo REDO_OFF连续升级，每次升级都包含跨Topo Join
TEST_F(ClientStExecUpgradeDatalog, TopoJoinWithSuppressUpgradeError1)
{
    char *fileName = (char *)"client_st_upgrade_topo_join";
    char *defaultNsp = (char *)"public";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));

    // upgrade
    char *deltaFileName = (char *)"client_st_upgrade_topo_join_patch1";
    char *upgradeSoName = (char *)"client_st_upgrade_topo_join_patch1";
    char *rollbackSoName1 = (char *)"client_st_upgrade_topo_join_rollback1";
    char *fileWithRuleName = (char *)"client_st_upgrade_topo_join_rule";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));

    deltaFileName = (char *)"client_st_upgrade_topo_join_patch2";
    upgradeSoName = (char *)"client_st_upgrade_topo_join_patch2";
    char *rollbackSoName2 = (char *)"client_st_upgrade_topo_join_rollback2";
    fileWithRuleName = (char *)"client_st_upgrade_topo_join_patch1_full";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName2);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));

    deltaFileName = (char *)"client_st_upgrade_topo_join_patch3";
    upgradeSoName = (char *)"client_st_upgrade_topo_join_patch3";
    char *rollbackSoName3 = (char *)"client_st_upgrade_topo_join_rollback3";
    fileWithRuleName = (char *)"client_st_upgrade_topo_join_patch2_full";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName3);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));

    // insert
    int32_t obj[][5] = {{1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}};
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "A1", *obj, ELEMENT_COUNT(obj), false));
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "C1", *obj, ELEMENT_COUNT(obj), false));
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "A2", *obj, ELEMENT_COUNT(obj), false));
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "C2", *obj, ELEMENT_COUNT(obj), false));
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "newTable", *obj, ELEMENT_COUNT(obj), false));
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "newTable2", *obj, ELEMENT_COUNT(obj), false));

    ScanAndCheckTable(syncStmt, "B1", (int32_t *)obj, ELEMENT_COUNT(obj));
    ScanAndCheckTable(syncStmt, "B2", (int32_t *)obj, ELEMENT_COUNT(obj));
    ScanAndCheckTable(syncStmt, "D1", (int32_t *)obj, ELEMENT_COUNT(obj));
    ScanAndCheckTable(syncStmt, "D2", (int32_t *)obj, ELEMENT_COUNT(obj));

    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName3, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName2, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    ScanAndCheckTable(syncStmt, "B1", (int32_t *)obj, ELEMENT_COUNT(obj));
    ScanAndCheckTable(syncStmt, "B2", (int32_t *)obj, ELEMENT_COUNT(obj));
    ScanAndCheckTable(syncStmt, "D1", (int32_t *)obj, ELEMENT_COUNT(obj));
    ScanAndCheckTable(syncStmt, "D2", (int32_t *)obj, ELEMENT_COUNT(obj));

    ASSERT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 使用%block 1和%block 0交替连续升级，每次升级都包含跨Topo Join
// 先使用 %block 0 后面都是 %block 1，能够join出数据
TEST_F(ClientStExecUpgradeDatalog, TopoJoinWithSuppressUpgradeError2)
{
    char *fileName = (char *)"client_st_upgrade_topo_join";
    char *defaultNsp = (char *)"public";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));

    // upgrade
    // block 0
    char *deltaFileName = (char *)"client_st_upgrade_topo_join_patch7";
    char *upgradeSoName = (char *)"client_st_upgrade_topo_join_patch7";
    char *rollbackSoName1 = (char *)"client_st_upgrade_topo_join_rollback7";
    char *fileWithRuleName = (char *)"client_st_upgrade_topo_join_rule";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));

    // block 1
    deltaFileName = (char *)"client_st_upgrade_topo_join_patch4";
    upgradeSoName = (char *)"client_st_upgrade_topo_join_patch4";
    char *rollbackSoName2 = (char *)"client_st_upgrade_topo_join_rollback4";
    fileWithRuleName = (char *)"client_st_upgrade_topo_join_patch7_full";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName2);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));

    // block 1
    deltaFileName = (char *)"client_st_upgrade_topo_join_patch5";
    upgradeSoName = (char *)"client_st_upgrade_topo_join_patch5";
    char *rollbackSoName3 = (char *)"client_st_upgrade_topo_join_rollback5";
    fileWithRuleName = (char *)"client_st_upgrade_topo_join_patch4_full";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName3);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));

    // block 1
    deltaFileName = (char *)"client_st_upgrade_topo_join_patch6";
    upgradeSoName = (char *)"client_st_upgrade_topo_join_patch6";
    char *rollbackSoName4 = (char *)"client_st_upgrade_topo_join_rollback6";
    fileWithRuleName = (char *)"client_st_upgrade_topo_join_patch5_full";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName4);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));

    // insert
    int32_t obj[][5] = {{1, 1, 1, 1, 1}, {1, 1, 2, 2, 2}, {1, 1, 3, 3, 3}};
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "A1", *obj, ELEMENT_COUNT(obj), false));
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "C1", *obj, ELEMENT_COUNT(obj), false));
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "A2", *obj, ELEMENT_COUNT(obj), false));
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "C2", *obj, ELEMENT_COUNT(obj), false));
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "newTable", *obj, ELEMENT_COUNT(obj), false));
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "newTable2", *obj, ELEMENT_COUNT(obj), false));

    ScanAndCheckTable(syncStmt, "B1", (int32_t *)obj, ELEMENT_COUNT(obj));
    ScanAndCheckTable(syncStmt, "B2", (int32_t *)obj, ELEMENT_COUNT(obj));
    ScanAndCheckTable(syncStmt, "D1", (int32_t *)obj, ELEMENT_COUNT(obj));
    ScanAndCheckTable(syncStmt, "D2", (int32_t *)obj, ELEMENT_COUNT(obj));

    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName4, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName3, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName2, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    int32_t obj2[][5] = {{1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}};
    ScanAndCheckTable(syncStmt, "B1", (int32_t *)obj2, ELEMENT_COUNT(obj2));
    ScanAndCheckTable(syncStmt, "B2", (int32_t *)obj2, ELEMENT_COUNT(obj2));
    ScanAndCheckTable(syncStmt, "D1", (int32_t *)obj2, ELEMENT_COUNT(obj2));
    ScanAndCheckTable(syncStmt, "D2", (int32_t *)obj2, ELEMENT_COUNT(obj2));

    ASSERT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 使用%block 1和%block 0交替连续升级，每次升级都包含跨Topo Join
// %block 0、%block 1、%block 0、%block 1，无法join出数据
TEST_F(ClientStExecUpgradeDatalog, TopoJoinWithSuppressUpgradeError3)
{
    char *fileName = (char *)"client_st_upgrade_topo_join";
    char *defaultNsp = (char *)"public";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));

    // upgrade
    // %block 0
    char *deltaFileName = (char *)"client_st_upgrade_topo_join_patch7";
    char *upgradeSoName = (char *)"client_st_upgrade_topo_join_patch7";
    char *rollbackSoName1 = (char *)"client_st_upgrade_topo_join_rollback7";
    char *fileWithRuleName = (char *)"client_st_upgrade_topo_join_rule";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));

    // %block 1
    deltaFileName = (char *)"client_st_upgrade_topo_join_patch4";
    upgradeSoName = (char *)"client_st_upgrade_topo_join_patch4";
    char *rollbackSoName2 = (char *)"client_st_upgrade_topo_join_rollback4";
    fileWithRuleName = (char *)"client_st_upgrade_topo_join_patch7_full";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName2);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));

    // %block 0
    deltaFileName = (char *)"client_st_upgrade_topo_join_patch8";
    upgradeSoName = (char *)"client_st_upgrade_topo_join_patch8";
    char *rollbackSoName3 = (char *)"client_st_upgrade_topo_join_rollback8";
    fileWithRuleName = (char *)"client_st_upgrade_topo_join_patch4_full";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName3);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));

    // %block 1
    deltaFileName = (char *)"client_st_upgrade_topo_join_patch6";
    upgradeSoName = (char *)"client_st_upgrade_topo_join_patch6";
    char *rollbackSoName4 = (char *)"client_st_upgrade_topo_join_rollback6";
    fileWithRuleName = (char *)"client_st_upgrade_topo_join_patch8_full";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName4);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));

    // insert
    int32_t obj[][5] = {{1, 1, 1, 1, 1}, {1, 1, 2, 2, 2}, {1, 1, 3, 3, 3}};
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "A1", *obj, ELEMENT_COUNT(obj), false));
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "C1", *obj, ELEMENT_COUNT(obj), false));
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "A2", *obj, ELEMENT_COUNT(obj), false));
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "C2", *obj, ELEMENT_COUNT(obj), false));
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "newTable", *obj, ELEMENT_COUNT(obj), false));
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "newTable2", *obj, ELEMENT_COUNT(obj), false));

    int32_t obj2[][5] = {0};
    int32_t obj3[][5] = {{1, 2, 1, 1, 1}, {1, 2, 2, 2, 2}, {1, 2, 3, 3, 3}};
    ScanAndCheckTable(syncStmt, "B1", (int32_t *)obj2, 0);
    ScanAndCheckTable(syncStmt, "B2", (int32_t *)obj, ELEMENT_COUNT(obj));
    ScanAndCheckTable(syncStmt, "D1", (int32_t *)obj3, ELEMENT_COUNT(obj3));
    ScanAndCheckTable(syncStmt, "D2", (int32_t *)obj, ELEMENT_COUNT(obj));

    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName4, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName3, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName2, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    int32_t obj4[][5] = {{1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}};
    ScanAndCheckTable(syncStmt, "B1", (int32_t *)obj4, ELEMENT_COUNT(obj4));
    ScanAndCheckTable(syncStmt, "B2", (int32_t *)obj4, ELEMENT_COUNT(obj4));
    ScanAndCheckTable(syncStmt, "D1", (int32_t *)obj4, ELEMENT_COUNT(obj4));
    ScanAndCheckTable(syncStmt, "D2", (int32_t *)obj4, ELEMENT_COUNT(obj4));

    ASSERT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// block1% 规则变更，两个namespace下不同topo输入表相互join，规格含规则含function，agg
TEST_F(ClientStExecUpgradeDatalog, TopoJoinWithSuppressUpgradeError4)
{
    char *fileName = (char *)"client_st_upgrade_topo_join_agg";
    char *udfFileName = (char *)"client_st_upgrade_add_udf";
    char *defaultNsp = (char *)"public";

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, DtlCreateAndUseNamespace(syncStmt, defaultNsp));

    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, udfFileName, defaultNsp));

    int32_t records1[][5] = {{1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}, {1, 0, 4, 5, 1}};
    Status ret = InsertInpTableBatch(syncConn, syncStmt, "inpA1", (int32_t *)records1, ELEMENT_COUNT(records1), true);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t records2[][6] = {{1, 0, 1, 1, 1, 1}, {1, 0, 2, 2, 2, 2}, {1, 0, 3, 3, 3, 3}, {1, 0, 1, 1, 4, 5}};
    ScanAndCheckTable(syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1));
    ScanAndCheckTable(syncStmt, "inpB", (int32_t *)records2, ELEMENT_COUNT(records2));
    ScanAndCheckTable(syncStmt, "out", (int32_t *)records2, ELEMENT_COUNT(records2));

    // %block 1
    char *deltaFileName = (char *)"client_st_upgrade_topo_join_agg_patch1";
    char *upgradeSoName = (char *)"client_st_upgrade_topo_join_agg_patch1";
    char *rollbackSoName1 = (char *)"client_st_upgrade_topo_join_agg_rollback1";
    char *fileWithRuleName = (char *)"client_st_upgrade_topo_join_agg_rule";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    // %block 1
    deltaFileName = (char *)"client_st_upgrade_topo_join_agg_patch2";
    upgradeSoName = (char *)"client_st_upgrade_topo_join_agg_patch2";
    char *rollbackSoName2 = (char *)"client_st_upgrade_topo_join_agg_rollback2";
    fileWithRuleName = (char *)"client_st_upgrade_topo_join_agg_patch1_full";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName2);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    // %block 0 + REDO_OFF
    deltaFileName = (char *)"client_st_upgrade_topo_join_agg_patch3";
    upgradeSoName = (char *)"client_st_upgrade_topo_join_agg_patch3";
    char *rollbackSoName3 = (char *)"client_st_upgrade_topo_join_agg_rollback3";
    fileWithRuleName = (char *)"client_st_upgrade_topo_join_agg_patch2_full";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName3);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    // %block 0 + REDO_OFF
    deltaFileName = (char *)"client_st_upgrade_topo_join_agg_patch4";
    upgradeSoName = (char *)"client_st_upgrade_topo_join_agg_patch4";
    char *rollbackSoName4 = (char *)"client_st_upgrade_topo_join_agg_rollback4";
    fileWithRuleName = (char *)"client_st_upgrade_topo_join_agg_patch3_full";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName4);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "inpA1", (int32_t *)records1, ELEMENT_COUNT(records1)));

    int32_t records12[][5] = {0};
    int32_t records13[][6] = {0};
    ScanAndCheckTable(syncStmt, "inpA", (int32_t *)records12, 0);
    ScanAndCheckTable(syncStmt, "inpB", (int32_t *)records13, 0);
    ScanAndCheckTable(syncStmt, "out", (int32_t *)records13, 0);

    EXPECT_EQ(
        GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "nsp3.A2", (int32_t *)records1, ELEMENT_COUNT(records1)));
    EXPECT_EQ(
        GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "nsp1.A1", (int32_t *)records1, ELEMENT_COUNT(records1)));
    EXPECT_EQ(
        GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "nsp2.C1", (int32_t *)records1, ELEMENT_COUNT(records1)));
    EXPECT_EQ(
        GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "nsp4.C2", (int32_t *)records1, ELEMENT_COUNT(records1)));

    ScanAndCheckTable(syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1));
    ScanAndCheckTable(syncStmt, "inpB", (int32_t *)records2, ELEMENT_COUNT(records2));
    ScanAndCheckTable(syncStmt, "out", (int32_t *)records2, ELEMENT_COUNT(records2));

    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName4, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName3, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName2, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    ScanAndCheckTable(syncStmt, "inpA", (int32_t *)records1, ELEMENT_COUNT(records1));
    ScanAndCheckTable(syncStmt, "inpB", (int32_t *)records2, ELEMENT_COUNT(records2));
    ScanAndCheckTable(syncStmt, "out", (int32_t *)records2, ELEMENT_COUNT(records2));

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

// 使用%block 1和%block 0交替连续升级，每次升级都包含跨Topo Join
// %block 0、%block 1、%block 0、%block 1，无法join出数据
TEST_F(ClientStExecUpgradeDatalog, TopoJoinWithSuppressUpgradeError5)
{
    char *fileName = (char *)"client_st_upgrade_topo_join";
    char *defaultNsp = (char *)"public";
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, execCmd(fileName, defaultNsp));

    // upgrade
    // %block 0
    char *deltaFileName = (char *)"client_st_upgrade_topo_join_patch9";
    char *upgradeSoName = (char *)"client_st_upgrade_topo_join_patch9";
    char *rollbackSoName1 = (char *)"client_st_upgrade_topo_join_rollback9";
    char *fileWithRuleName = (char *)"client_st_upgrade_topo_join_rule";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName1);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));

    // %block 1
    deltaFileName = (char *)"client_st_upgrade_topo_join_patch10";
    upgradeSoName = (char *)"client_st_upgrade_topo_join_patch10";
    char *rollbackSoName2 = (char *)"client_st_upgrade_topo_join_rollback10";
    fileWithRuleName = (char *)"client_st_upgrade_topo_join_patch9_full";
    ExecPrepare4Upgrade(fileWithRuleName, deltaFileName, upgradeSoName);
    ExecPrepare4RollbackUpgrade(fileWithRuleName, deltaFileName, rollbackSoName2);
    EXPECT_EQ(GMERR_OK, execUpgrade(upgradeSoName, defaultNsp));

    // insert
    int32_t obj[][5] = {{1, 1, 1, 1, 1}, {1, 1, 2, 2, 2}, {1, 1, 3, 3, 3}};
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "A2", *obj, ELEMENT_COUNT(obj), false));
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "C2", *obj, ELEMENT_COUNT(obj), false));
    EXPECT_EQ(GMERR_OK, InsertInpTableBatch(syncConn, syncStmt, "newTable", *obj, ELEMENT_COUNT(obj), false));

    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName2, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));
    EXPECT_EQ(GMERR_OK, execRollbackUpgrade(rollbackSoName1, defaultNsp));
    ASSERT_EQ(GMERR_OK, CheckUpgradeSuccess(fileName, 100));

    int32_t obj4[][5] = {{1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}};
    ScanAndCheckTable(syncStmt, "B2", (int32_t *)obj4, ELEMENT_COUNT(obj4));
    ScanAndCheckTable(syncStmt, "D2", (int32_t *)obj4, ELEMENT_COUNT(obj4));

    ASSERT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    DestroyConnectionAndStmt(syncConn, syncStmt);
}

Status GetUpgradeVersionOfVertexLabel(GmcStmtT *syncStmt, char *labelName, int *version)
{
    Status ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcGetUpgradeVersion(syncStmt, version);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

TEST_F(ClientStExecUpgradeDatalog, DefinedTableWithUpgradeVersionOption)
{
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    char *fileName = (char *)"client_st_defined_table_with_upgrade_version_option";
    char *defaultNsp = (char *)"public";
    CreateExternalOutput5(syncStmt, defaultNsp);
    EXPECT_EQ(GMERR_OK, execCmdWithUdf(fileName, fileName, defaultNsp));

    int32_t upVerVal = 0;
    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"input1", &upVerVal), GMERR_OK);
    EXPECT_EQ(5, upVerVal);
    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"input2", &upVerVal), GMERR_OK);
    EXPECT_EQ(5, upVerVal);
    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"input3", &upVerVal), GMERR_OK);
    EXPECT_EQ(5, upVerVal);
    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"input4", &upVerVal), GMERR_OK);
    EXPECT_EQ(5, upVerVal);
    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"input5", &upVerVal), GMERR_OK);
    EXPECT_EQ(5, upVerVal);
    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"input6", &upVerVal), GMERR_OK);
    EXPECT_EQ(5, upVerVal);

    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"mid1", &upVerVal), GMERR_OK);
    EXPECT_EQ(5, upVerVal);

    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"mid2", &upVerVal), GMERR_OK);
    EXPECT_EQ(5, upVerVal);

    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"mid3", &upVerVal), GMERR_OK);
    EXPECT_EQ(5, upVerVal);

    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"mid4", &upVerVal), GMERR_OK);
    EXPECT_EQ(5, upVerVal);

    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"mid5", &upVerVal), GMERR_OK);
    EXPECT_EQ(5, upVerVal);

    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"mid6", &upVerVal), GMERR_OK);
    EXPECT_EQ(5, upVerVal);

    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"A", &upVerVal), GMERR_OK);
    EXPECT_EQ(5, upVerVal);
    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"B", &upVerVal), GMERR_OK);
    EXPECT_EQ(5, upVerVal);
    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"C", &upVerVal), GMERR_OK);
    EXPECT_EQ(5, upVerVal);
    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"output1", &upVerVal), GMERR_OK);
    EXPECT_EQ(5, upVerVal);
    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"output6", &upVerVal), GMERR_OK);
    EXPECT_EQ(5, upVerVal);
    EXPECT_EQ(GetUpgradeVersionOfVertexLabel(syncStmt, (char *)"output7", &upVerVal), GMERR_OK);
    EXPECT_EQ(5, upVerVal);

    EXPECT_EQ(GMERR_OK, execUninstallCmd(fileName, defaultNsp));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(syncStmt, "output5"));
    DestroyConnectionAndStmt(syncConn, syncStmt);
    sleep(3);
}
