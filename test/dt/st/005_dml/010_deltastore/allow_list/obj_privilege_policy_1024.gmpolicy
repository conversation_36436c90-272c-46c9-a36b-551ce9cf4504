{"object_privilege_config": [{"obj_name": "T0", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T1", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T2", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T3", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T4", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T5", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T6", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T7", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T8", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T9", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T10", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T11", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T12", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T13", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T14", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T15", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T16", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T17", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T18", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T19", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T20", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T21", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T22", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T23", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T24", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T25", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T26", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T27", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T28", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T29", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T30", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T31", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T32", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T33", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T34", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T35", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T36", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T37", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T38", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T39", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T40", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T41", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T42", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T43", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T44", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T45", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T46", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T47", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T48", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T49", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T50", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T51", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T52", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T53", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T54", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T55", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T56", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T57", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T58", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T59", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T60", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T61", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T62", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T63", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T64", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T65", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T66", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T67", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T68", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T69", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T70", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T71", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T72", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T73", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T74", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T75", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T76", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T77", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T78", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T79", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T80", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T81", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T82", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T83", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T84", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T85", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T86", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T87", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T88", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T89", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T90", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T91", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T92", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T93", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T94", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T95", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T96", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T97", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T98", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T99", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T100", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T101", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T102", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T103", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T104", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T105", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T106", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T107", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T108", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T109", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T110", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T111", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T112", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T113", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T114", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T115", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T116", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T117", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T118", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T119", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T120", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T121", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T122", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T123", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T124", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T125", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T126", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T127", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T128", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T129", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T130", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T131", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T132", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T133", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T134", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T135", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T136", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T137", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T138", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T139", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T140", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T141", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T142", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T143", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T144", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T145", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T146", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T147", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T148", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T149", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T150", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T151", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T152", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T153", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T154", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T155", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T156", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T157", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T158", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T159", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T160", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T161", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T162", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T163", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T164", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T165", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T166", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T167", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T168", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T169", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T170", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T171", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T172", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T173", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T174", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T175", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T176", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T177", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T178", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T179", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T180", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T181", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T182", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T183", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T184", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T185", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T186", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T187", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T188", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T189", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T190", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T191", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T192", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T193", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T194", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T195", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T196", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T197", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T198", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T199", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T200", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T201", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T202", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T203", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T204", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T205", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T206", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T207", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T208", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T209", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T210", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T211", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T212", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T213", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T214", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T215", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T216", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T217", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T218", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T219", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T220", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T221", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T222", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T223", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T224", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T225", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T226", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T227", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T228", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T229", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T230", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T231", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T232", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T233", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T234", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T235", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T236", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T237", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T238", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T239", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T240", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T241", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T242", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T243", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T244", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T245", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T246", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T247", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T248", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T249", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T250", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T251", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T252", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T253", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T254", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T255", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T256", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T257", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T258", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T259", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T260", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T261", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T262", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T263", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T264", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T265", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T266", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T267", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T268", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T269", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T270", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T271", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T272", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T273", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T274", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T275", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T276", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T277", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T278", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T279", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T280", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T281", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T282", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T283", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T284", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T285", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T286", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T287", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T288", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T289", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T290", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T291", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T292", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T293", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T294", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T295", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T296", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T297", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T298", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T299", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T300", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T301", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T302", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T303", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T304", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T305", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T306", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T307", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T308", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T309", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T310", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T311", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T312", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T313", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T314", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T315", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T316", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T317", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T318", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T319", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T320", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T321", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T322", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T323", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T324", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T325", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T326", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T327", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T328", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T329", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T330", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T331", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T332", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T333", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T334", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T335", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T336", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T337", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T338", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T339", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T340", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T341", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T342", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T343", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T344", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T345", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T346", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T347", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T348", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T349", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T350", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T351", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T352", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T353", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T354", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T355", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T356", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T357", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T358", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T359", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T360", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T361", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T362", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T363", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T364", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T365", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T366", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T367", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T368", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T369", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T370", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T371", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T372", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T373", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T374", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T375", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T376", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T377", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T378", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T379", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T380", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T381", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T382", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T383", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T384", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T385", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T386", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T387", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T388", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T389", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T390", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T391", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T392", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T393", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T394", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T395", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T396", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T397", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T398", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T399", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T400", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T401", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T402", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T403", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T404", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T405", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T406", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T407", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T408", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T409", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T410", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T411", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T412", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T413", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T414", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T415", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T416", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T417", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T418", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T419", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T420", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T421", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T422", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T423", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T424", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T425", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T426", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T427", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T428", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T429", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T430", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T431", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T432", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T433", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T434", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T435", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T436", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T437", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T438", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T439", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T440", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T441", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T442", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T443", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T444", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T445", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T446", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T447", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T448", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T449", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T450", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T451", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T452", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T453", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T454", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T455", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T456", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T457", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T458", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T459", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T460", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T461", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T462", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T463", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T464", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T465", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T466", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T467", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T468", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T469", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T470", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T471", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T472", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T473", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T474", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T475", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T476", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T477", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T478", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T479", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T480", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T481", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T482", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T483", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T484", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T485", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T486", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T487", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T488", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T489", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T490", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T491", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T492", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T493", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T494", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T495", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T496", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T497", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T498", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T499", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T500", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T501", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T502", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T503", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T504", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T505", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T506", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T507", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T508", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T509", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T510", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T511", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T512", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T513", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T514", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T515", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T516", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T517", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T518", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T519", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T520", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T521", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T522", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T523", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T524", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T525", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T526", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T527", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T528", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T529", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T530", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T531", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T532", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T533", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T534", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T535", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T536", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T537", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T538", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T539", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T540", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T541", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T542", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T543", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T544", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T545", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T546", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T547", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T548", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T549", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T550", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T551", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T552", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T553", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T554", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T555", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T556", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T557", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T558", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T559", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T560", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T561", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T562", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T563", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T564", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T565", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T566", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T567", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T568", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T569", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T570", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T571", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T572", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T573", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T574", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T575", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T576", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T577", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T578", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T579", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T580", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T581", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T582", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T583", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T584", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T585", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T586", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T587", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T588", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T589", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T590", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T591", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T592", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T593", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T594", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T595", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T596", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T597", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T598", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T599", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T600", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T601", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T602", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T603", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T604", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T605", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T606", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T607", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T608", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T609", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T610", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T611", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T612", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T613", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T614", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T615", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T616", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T617", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T618", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T619", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T620", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T621", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T622", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T623", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T624", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T625", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T626", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T627", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T628", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T629", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T630", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T631", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T632", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T633", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T634", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T635", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T636", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T637", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T638", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T639", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T640", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T641", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T642", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T643", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T644", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T645", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T646", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T647", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T648", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T649", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T650", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T651", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T652", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T653", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T654", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T655", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T656", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T657", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T658", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T659", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T660", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T661", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T662", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T663", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T664", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T665", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T666", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T667", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T668", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T669", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T670", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T671", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T672", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T673", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T674", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T675", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T676", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T677", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T678", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T679", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T680", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T681", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T682", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T683", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T684", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T685", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T686", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T687", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T688", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T689", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T690", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T691", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T692", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T693", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T694", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T695", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T696", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T697", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T698", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T699", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T700", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T701", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T702", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T703", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T704", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T705", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T706", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T707", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T708", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T709", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T710", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T711", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T712", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T713", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T714", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T715", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T716", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T717", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T718", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T719", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T720", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T721", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T722", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T723", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T724", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T725", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T726", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T727", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T728", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T729", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T730", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T731", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T732", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T733", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T734", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T735", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T736", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T737", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T738", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T739", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T740", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T741", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T742", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T743", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T744", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T745", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T746", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T747", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T748", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T749", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T750", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T751", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T752", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T753", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T754", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T755", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T756", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T757", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T758", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T759", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T760", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T761", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T762", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T763", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T764", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T765", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T766", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T767", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T768", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T769", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T770", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T771", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T772", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T773", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T774", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T775", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T776", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T777", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T778", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T779", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T780", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T781", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T782", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T783", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T784", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T785", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T786", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T787", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T788", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T789", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T790", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T791", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T792", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T793", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T794", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T795", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T796", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T797", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T798", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T799", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T800", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T801", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T802", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T803", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T804", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T805", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T806", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T807", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T808", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T809", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T810", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T811", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T812", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T813", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T814", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T815", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T816", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T817", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T818", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T819", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T820", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T821", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T822", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T823", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T824", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T825", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T826", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T827", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T828", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T829", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T830", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T831", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T832", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T833", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T834", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T835", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T836", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T837", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T838", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T839", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T840", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T841", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T842", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T843", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T844", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T845", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T846", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T847", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T848", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T849", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T850", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T851", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T852", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T853", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T854", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T855", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T856", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T857", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T858", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T859", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T860", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T861", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T862", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T863", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T864", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T865", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T866", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T867", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T868", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T869", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T870", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T871", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T872", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T873", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T874", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T875", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T876", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T877", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T878", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T879", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T880", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T881", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T882", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T883", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T884", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T885", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T886", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T887", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T888", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T889", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T890", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T891", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T892", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T893", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T894", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T895", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T896", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T897", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T898", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T899", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T900", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T901", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T902", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T903", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T904", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T905", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T906", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T907", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T908", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T909", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T910", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T911", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T912", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T913", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T914", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T915", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T916", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T917", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T918", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T919", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T920", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T921", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T922", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T923", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T924", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T925", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T926", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T927", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T928", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T929", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T930", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T931", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T932", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T933", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T934", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T935", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T936", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T937", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T938", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T939", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T940", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T941", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T942", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T943", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T944", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T945", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T946", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T947", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T948", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T949", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T950", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T951", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T952", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T953", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T954", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T955", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T956", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T957", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T958", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T959", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T960", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T961", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T962", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T963", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T964", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T965", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T966", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T967", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T968", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T969", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T970", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T971", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T972", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T973", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T974", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T975", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T976", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T977", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T978", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T979", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T980", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T981", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T982", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T983", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T984", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T985", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T986", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T987", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T988", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T989", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T990", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T991", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T992", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T993", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T994", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T995", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T996", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T997", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T998", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}, {"obj_name": "T999", "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "st_005_dml", "privs_type": ["DELETE", "INSERT", "UPDATE", "SELECT", "MERGE", "REPLACE"]}]}]}