/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: st for direct write dml operations
 * Author: maojinyong
 * Create: 2023-03-16
 */

#include <string.h>
#include <mutex>
#include <math.h>
#include "client_common_st.h"
#include "storage_st_common.h"
#include "se_spacemgr.h"
#include "se_page_mgr.h"
#include "stub.h"
#include "clt_da_write.h"
#include "clt_da_write_pubsub_status_merge.h"
#include "clt_da_write_respool.h"
#include "clt_da_handle.h"
#include "jansson.h"

#define AUDIT_LOG_ENABLE_DML_ORIGIN_VALUE 0u
#define AUDIT_LOG_ENABLE_DML_AFTER_VALUE 1u

#define ENABLE_DML_OPER_STAT_ORIGIN_VALUE 1u
#define ENABLE_DML_OPER_STAT_AFTER_VALUE 1u  // enableDmlOperStatb不允许在线修改，after和origin值一致

#define ENABLE_DML_PERF_STAT_ORIGIN_VALUE 1u
#define ENABLE_DML_PERF_STAT_AFTER_VALUE 0u

#define COMPATIBLE_V3_ORIGIN_VALUE 0u
#define COMPATIBLE_V3_AFTER_VALUE 0u  // compatibleV3不允许在线修改，after和origin值一致

#define DIRECT_WRITE_ORIGIN_VALUE 1u

class StClientDirectWrite : public StClient {
public:
    static void SetUpTestCase();
};
void StClientDirectWrite::SetUpTestCase()
{
    // 确保打开enableDmlOperStat和enableDmlPerfStat，用于测试dml stat视图
    // 最新代码默认打开聚簇容器，此处将其设置为关闭状态，聚簇容器用例在其他文件中维护
    StartDbServerWithConfig(
        "\"enableDmlOperStat=1\" \"enableDmlPerfStat=1\" \"enableClusterHash=0\" "
        "\"trxMonitorEnable=0\" \"workerHungThreshold=6,200,300\" \"maxSysDynSize=1024\" \"directWrite=1\" "
        "\"auditLogEnableDML=0\" \"userPolicyMode=0\" \"compatibleV3=0\" \"schemaLoader=2\" "
        "\"schemaPath=./011_direct_write/st_schema;./011_direct_write/st_schema;./011_direct_write/st_schema;\"");
    st_clt_init();
    GmcSignalRegisterNotify();
    CreateAndStartEpoll(&responseEpollThreadId, &responseEpollFd);
    CreateAndStartEpoll(&timeoutEpollThreadId, &timeoutEpollFd);
    EXPECT_EQ(GMERR_OK, GmcRegTimeoutEpollFunc(TimeoutEpollReg));
    // 此处心跳定时器与TimeOut定时器使用同一个Epoll，可设置为不同Epoll
    EXPECT_EQ(GMERR_OK, GmcRegHeartBeatEpollFunc(TimeoutEpollReg));
    if (IsEulerEnv()) {
        DbSleep(1000);
    } else {
        st_check_hpe_server_running();
    }
    printf("start response epoll and timeout epoll thread\n");
    printf("response epoll fd: %d, timeout epoll fd: %d\n", responseEpollFd, timeoutEpollFd);
    st_connect();
}

static const char *g_label_config = R"({"max_record_count":100000})";
// 简单表
static const char *g_label_name = "T39";
static const char *g_label_schema =
    R"([{
        "type":"record",
        "name":"T39",
        "config": {
            "direct_write": true
        },
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"int32", "nullable":true},
                {"name": "vr_id", "type": "uint32", "comment": "Vs索引", "nullable":false },
                {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引", "nullable":false },
                {"name": "dest_ip_addr", "type": "uint32", "comment": "目的地址", "nullable":false},
                {"name": "mask_len", "type": "uint8", "comment": "掩码长度", "nullable":false }
            ],
        "keys":
            [
                {
                    "node":"T39",
                    "name":"T39_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                },
                {
                    "node":"T39",
                    "name":"T39_K1",
                    "fields":["F1"],
                    "index":{ "type":"localhash" },
                    "constraints": { "unique": true }
                },
                {
                    "node":"T39",
                    "name":"T39_K2",
                    "fields":["F2"],
                    "index":{ "type":"hashcluster" },
                    "constraints": { "unique": false }
                },
                {
                    "node":"T39",
                    "name":"T39_K3",
                    "fields":["F3"],
                    "index":{ "type":"local" },
                    "constraints": { "unique": false }
                },
                {
                    "node": "T39",
                    "name": "T39_K4",
                    "fields": [ "vr_id", "vrf_index", "dest_ip_addr", "mask_len" ],
                    "index": { "type": "lpm4_tree_bitmap" },
                    "constraints": { "unique": true }
                }
            ]
        }])";

// 树模型表，即特殊复杂表
static const char *g_tree_label_name = "access_list";
static const char *g_tree_label_schema = R"([{
    "name":"access_list", "version":"2.0", "type":"record",
    "config": {
        "direct_write": true
    },
    "fields":[
        { "name":"nftable_table_name", "type":"string", "size":128 },
        { "name":"nftable_table_family", "type":"string", "size":128, "nullable": true },
        { "name":"nftable_table_flags", "type":"uint32", "nullable": true },
        { "name":"nftable_table_use", "type":"uint32", "nullable": true },
        { "type":"record", "name":"nftable_chain", "nullable": true, "vector":true, "size":20, "fields":[
            { "name":"nftable_chain_name", "type":"string", "size":128, "nullable": true},
            { "name":"nftable_chain_type", "type":"string", "size":128, "nullable": true },
            { "name":"nftable_chain_table", "type":"string", "size":128, "nullable": true },
            { "name":"nftable_chain_family", "type":"string", "size":128, "nullable": true },
            { "name":"nftable_chain_policy", "type":"uint32", "nullable": true },
            { "name":"nftable_chain_hooknum", "type":"uint32", "nullable": true },
            { "name":"nftable_chain_prio", "type":"uint32", "nullable": true },
            { "name":"nftable_chain_use", "type":"uint32", "nullable": true },
            { "name":"nftable_chain_handle", "type":"uint64", "nullable": true },
            { "name":"nftable_chain_basechainflag", "type":"uint8", "nullable": true },
            { "name":"nftable_chain_policyflag", "type":"uint8", "nullable": true },
            { "name":"nftable_chain_devflag", "type":"uint8", "nullable": true }
            ]
        } ],
        "keys":[
            { "name":"access_control_list_key", "node":"access_list", "fields":["nftable_table_name"],
                "index": { "type":"primary" }},
            { "name": "nftable_chain_key", "node": "nftable_chain", "fields": ["nftable_chain_name"],
                "index": { "type": "none" }, "constraints": {"unique": true}}
            ]
        }])";

static const char *g_all_type_label_name = "T39_all_type";
static const char *g_all_type_label_schema =
    R"([{
        "type":"record",
        "name":"T39_all_type",
        "config": {
            "direct_write": true
        },
        "fields":[
            {"name":"F0", "type":"uint32", "nullable":false},
            {"name":"F1", "type":"char", "default":"a", "nullable":true},
            {"name":"F2", "type":"uchar", "default":"f", "nullable":true},
            {"name":"F3", "type":"int8", "default":-8, "nullable":true},
            {"name":"F4", "type":"uint8", "default":8, "nullable":true},
            {"name":"F5", "type":"int16", "default":-16, "nullable":true},
            {"name":"F6", "type":"uint16", "default":16, "nullable":true},
            {"name":"F7", "type":"int32", "default":-32, "nullable":true},
            {"name":"F8", "type":"boolean", "default":true, "nullable":true},
            {"name":"F9", "type":"int64", "default":-64, "nullable":true},
            {"name":"F10", "type":"uint64", "default":64, "nullable":true},
            {"name":"F11", "type":"float", "default":3.14, "nullable":true},
            {"name":"F12", "type":"double", "default":3.14, "nullable":true},
            {"name":"F13", "type":"time", "default":1000, "nullable":true},
            {"name":"F14", "type":"string", "default":"test_string", "size":100, "nullable":true},
            {"name":"F15", "type":"bytes", "default":"test_bytes", "size":100, "nullable":true},
            {"name":"F16", "type":"fixed", "default":"test_fixed", "size":10, "nullable":true}
        ],
        "keys":[
            {
                "node":"T39_all_type",
                "name":"K0",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
        }])";

// 一般复杂表，含有变长字段
static const char *g_general_label_name = "T90";
static const char *g_general_label_schema =
    R"([{
    "type":"record",
    "name":"T90",
    "config": {
        "direct_write": true
    },
    "fields":[
        {"name":"F0", "type":"uint32", "nullable":false},
        {"name":"F1", "type":"string", "size":100, "nullable":false}
    ],
    "keys":[
       {
            "node":"T90",
            "name":"T90_PK",
            "fields":["F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
}])";

// the following are expected values of your given test
typedef struct TestExpectedArgs {
    int32_t f0, f1, f2, f3;
    uint32_t vrId, vrfIndex, ipAddr;
    uint8_t maskLen;
} ExpectedArgsT;

typedef struct Lpm4Key {
    uint32_t vrId, vrfIndex, ipAddr;
    uint8_t maskLen;
} Lpm4KeyT;

static void fetchAndCompare(GmcStmtT *stmt, char *keyName, void *keyValue, bool isLpm4, ExpectedArgsT args)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    // 如果设置了key，则基于索引进行查询；否则，进行全量扫描
    if (keyName != NULL) {
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        if (isLpm4) {
            Lpm4KeyT *lpmKey = (Lpm4KeyT *)keyValue;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &lpmKey->vrId, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &lpmKey->vrfIndex, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &lpmKey->ipAddr, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &lpmKey->maskLen, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, keyValue, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        int32_t f0 = 0, f1 = 0, f2 = 0, f3 = 0;
        uint32_t vrId = 0, vrfIndex = 0, ipAddr = 0;
        uint8_t maskLen = 0;
        bool isNull = false;
        ret = GmcGetVertexPropertyByName(stmt, "F0", &f0, sizeof(f0), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(args.f0, f0);

        ret = GmcGetVertexPropertyByName(stmt, "F1", &f1, sizeof(f1), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(args.f1, f1);

        ret = GmcGetVertexPropertyByName(stmt, "F2", &f2, sizeof(f2), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(args.f2, f2);

        ret = GmcGetVertexPropertyByName(stmt, "F3", &f3, sizeof(f3), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(args.f3, f3);

        ret = GmcGetVertexPropertyByName(stmt, "vr_id", &vrId, sizeof(vrId), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(args.vrId, vrId);

        ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &vrfIndex, sizeof(vrfIndex), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(args.vrfIndex, vrfIndex);

        ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", &ipAddr, sizeof(ipAddr), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(args.ipAddr, ipAddr);

        ret = GmcGetVertexPropertyByName(stmt, "mask_len", &maskLen, sizeof(maskLen), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(args.maskLen, maskLen);
    }
}

// 设置表'T39'各个字段的值
static void SetVertexProperty(GmcStmtT *stmt, uint32_t val)
{
    int32_t f0 = val;
    Status ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &f0, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f1 = val;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f2 = val;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2, sizeof(int32_t));
    EXPECT_EQ(ret, GMERR_OK);
    int32_t f3 = val;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 设置lpm索引相关字段
    uint32_t vrID = val % 16;  // vr_id字段的取值范围是[0,15]，这里注意一下
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vrID, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t vrfIndex = val % 1024;  // vrf_index字段的取值范围是[0,1023]，这里注意一下
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t ipAddr = val;
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &ipAddr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t maskLen = val % 33;  // 掩码长度范围[0,32]
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
}

// 校验表'T39'各个字段的值，args表示预期值
static void VerifyVertexProperty(GmcStmtT *stmt, ExpectedArgsT args)
{
    fetchAndCompare(stmt, (char *)"T39_K0", &args.f0, false, args);  // 基于主键查询
    fetchAndCompare(stmt, (char *)"T39_K1", &args.f1, false, args);  // 基于localhash索引查询
    fetchAndCompare(stmt, (char *)"T39_K2", &args.f2, false, args);  // 基于hashcluster索引查询
    fetchAndCompare(stmt, (char *)"T39_K3", &args.f3, false, args);  // 基于local索引查询
    Lpm4KeyT lpmKeys = {.vrId = args.vrId, .vrfIndex = args.vrfIndex, .ipAddr = args.ipAddr, .maskLen = args.maskLen};
    fetchAndCompare(stmt, (char *)"T39_K4", &lpmKeys, true, args);  // 基于lpm索引查询
}

static void SetExpectedArgs(ExpectedArgsT *args, uint32_t val)
{
    args->f0 = (int32_t)val;
    args->f1 = (int32_t)val;
    args->f2 = (int32_t)val;
    args->f3 = (int32_t)val;
    args->vrId = val;
    args->vrfIndex = val;
    args->ipAddr = val;
    args->maskLen = (uint8_t)val;
}

typedef struct ElementArgs {
    char nftableChainName[128];
    char nftableChainTable[128];
} ElementArgsT;
typedef struct TestTreeArgs {
    char *keyValue;
    uint32_t eleCount;       // element count of 'nftable_chain'
    ElementArgsT *eleArray;  // element array of 'nftable_chain'
} TestTreeArgsT;

static void fetchAndCompareForTreeVertex(GmcStmtT *stmt, TestTreeArgsT args)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, g_tree_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "access_control_list_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, args.keyValue, strlen(args.keyValue));
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isFinish);
    if (!isFinish) {
        char buf[128] = {};
        bool isNull;
        GmcNodeT *rootNode;
        GmcNodeT *chainNode;
        GmcNodeT *nextEleNode;
        ret = GmcGetRootNode(stmt, &rootNode);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t count = 0;
        ret = GmcNodeGetElementCount(chainNode, &count);
        EXPECT_EQ(GMERR_OK, ret);
        ASSERT_EQ(args.eleCount, count);
        for (uint32_t i = 0; i < count; i++) {
            ret = GmcNodeGetElementByIndex(chainNode, i, &nextEleNode);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetPropertyByName(nextEleNode, "nftable_chain_name", &buf, sizeof(buf), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_STREQ(args.eleArray[i].nftableChainName, buf);
            ret = GmcNodeGetPropertyByName(nextEleNode, "nftable_chain_table", &buf, sizeof(buf), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_STREQ(args.eleArray[i].nftableChainTable, buf);
        }
    }
}

/**
 * @brief compare single property value according to the data type
 * @param ttype:          data type
 * @param expectValue:    data value that expected
 * @param queryValue:     data value that query actually
 * @param size:           data size
 * @return 错误码
 */
static int32_t compareVertexPropertyValue(
    GmcDataTypeE ttype, void *expectValue, void *queryValue, unsigned int size = 0)
{
    int32_t ret = 0;
    if (expectValue == NULL || queryValue == NULL) {
        return 1;
    }
    switch (ttype) {
        case GMC_DATATYPE_CHAR:
            if ((*(char *)expectValue) == (*(char *)queryValue)) {
                ret = 0;
            } else {
                ret = 2;
                printf("expectValue:%c queryValue:%c\n", *(char *)expectValue, *(char *)queryValue);
            }
            break;
        case GMC_DATATYPE_UCHAR:
            if ((*(unsigned char *)expectValue) == (*(unsigned char *)queryValue)) {
                ret = 0;
            } else {
                ret = 3;
                printf("expectValue:%c queryValue:%c\n", *(unsigned char *)expectValue, *(unsigned char *)queryValue);
            }
            break;
        case GMC_DATATYPE_INT8:
            if ((*(int8_t *)expectValue) == (*(int8_t *)queryValue)) {
                ret = 0;
            } else {
                ret = 4;
                printf("expectValue:%d queryValue:%d\n", *(int8_t *)expectValue, *(int8_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_UINT8:
            if ((*(uint8_t *)expectValue) == (*(uint8_t *)queryValue)) {
                ret = 0;
            } else {
                ret = 5;
                printf("expectValue:%u queryValue:%u\n", *(uint8_t *)expectValue, *(uint8_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_INT16:
            if ((*(int16_t *)expectValue == *(int16_t *)queryValue)) {
                ret = 0;
            } else {
                ret = 6;
                printf("expectValue:%d queryValue:%d\n", *(int16_t *)expectValue, *(int16_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_UINT16:
            if ((*(uint16_t *)expectValue == *(uint16_t *)queryValue)) {
                ret = 0;
            } else {
                ret = 7;
                printf("expectValue:%u queryValue:%u\n", *(uint16_t *)expectValue, *(uint16_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_INT32:
            if ((*(int32_t *)expectValue) == (*(int32_t *)queryValue)) {
                ret = 0;
            } else {
                ret = 8;
                printf("expectValue:%d queryValue:%d\n", *(int32_t *)expectValue, *(int32_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_UINT32:
            if ((*(uint32_t *)expectValue) == (*(uint32_t *)queryValue)) {
                ret = 0;
            } else {
                ret = 9;
                printf("expectValue:%u queryValue:%u\n", *(uint32_t *)expectValue, *(uint32_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_BOOL:
            if ((*(bool *)expectValue) == (*(bool *)queryValue)) {
                ret = 0;
            } else {
                ret = 10;
            }
            break;
        case GMC_DATATYPE_INT64:
            if ((*(int64_t *)expectValue) == (*(int64_t *)queryValue)) {
                ret = 0;
            } else {
                ret = 11;
            }
            break;
        case GMC_DATATYPE_UINT64:
            if ((*(uint64_t *)expectValue) == (*(uint64_t *)queryValue)) {
                ret = 0;
            } else {
                ret = 12;
            }
            break;
        case GMC_DATATYPE_FLOAT:
            if (fabs((*(float *)expectValue) - (*(float *)queryValue)) < __FLT_EPSILON__) {
                ret = 0;
            } else {
                printf("expectValue:%lf queryValue:%lf\n", *(float *)expectValue, *(float *)queryValue);
                ret = 13;
            }
            break;
        case GMC_DATATYPE_DOUBLE:
            if (fabs((*(double *)expectValue) - (*(double *)queryValue)) < __DBL_EPSILON__) {
                ret = 0;
            } else {
                printf("expectValue:%lf queryValue:%lf\n", *(double *)expectValue, *(double *)queryValue);
                ret = 14;
            }
            break;
        case GMC_DATATYPE_TIME:
            if ((*(uint64_t *)expectValue) == (*(uint64_t *)queryValue)) {
                ret = 0;
            } else {
                ret = 15;
            }
            break;
        case GMC_DATATYPE_BITFIELD8:
            if ((*(uint8_t *)expectValue) == (*(uint8_t *)queryValue)) {
                ret = 0;
            } else {
                ret = 16;
                printf("expectValue:%u queryValue:%u\n", *(uint8_t *)expectValue, *(uint8_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_BITFIELD16:
            if ((*(uint16_t *)expectValue == *(uint16_t *)queryValue)) {
                ret = 0;
            } else {
                ret = 17;
                printf("expectValue:%u queryValue:%u\n", *(uint16_t *)expectValue, *(uint16_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_BITFIELD32:
            if ((*(uint32_t *)expectValue) == (*(uint32_t *)queryValue)) {
                ret = 0;
            } else {
                ret = 18;
                printf("expectValue:%u queryValue:%u\n", *(uint32_t *)expectValue, *(uint32_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_BITFIELD64:
            if ((*(uint64_t *)expectValue) == (*(uint64_t *)queryValue)) {
                ret = 0;
            } else {
                ret = 19;
                printf("expectValue:%u queryValue:%u\n", *(uint32_t *)expectValue, *(uint32_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_PARTITION:
            if ((*(uint8_t *)expectValue) == (*(uint8_t *)queryValue)) {
                ret = 0;
            } else {
                ret = 20;
                printf("expectValue:%u queryValue:%u\n", *(uint8_t *)expectValue, *(uint8_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_STRING:
            ret = strcmp((char *)expectValue, (char *)queryValue);
            if (ret != 0) {
                printf("expectValue: %s, queryValue: %s\n", (char *)expectValue, (char *)queryValue);
                ret = 21;
            }
            break;
        case GMC_DATATYPE_BYTES:
            ret = memcmp((char *)expectValue, (char *)queryValue, size);
            if (ret != 0) {
                printf("expectValue:%s queryValue:%s\n", (char *)expectValue, (char *)queryValue);
                ret = 22;
            }
            break;
        case GMC_DATATYPE_FIXED:
            ret = memcmp((char *)expectValue, (char *)queryValue, size);
            if (ret != 0) {
                printf("expectValue:%s queryValue:%s\n", (char *)expectValue, (char *)queryValue);
                ret = 23;
            }
            break;
        case GMC_DATATYPE_BITMAP:
            ret = memcmp((char *)expectValue, (char *)queryValue, size / 8);
            if (ret != 0) {
                printf("expectValue:%s queryValue:%s\n", (char *)expectValue, (char *)queryValue);
                ret = 24;
            }
            break;
        default:
            printf("the type %d is not existence!\n", ttype);
            ret = 100;
            break;
    }
    return ret;
}

static int queryPropertyAndCompare(GmcStmtT *stmt, const char *propertyName, GmcDataTypeE ttype, void *expectValue)
{
    int ret = 0;
    unsigned int valSize = 0;
    bool isNull = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, propertyName, &valSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    char *pValue = (char *)malloc(valSize);
    if (pValue == NULL) {
        return 1;
    }
    ret = GmcGetVertexPropertyByName(stmt, propertyName, pValue, valSize, &isNull);
    if (ret != GMERR_OK) {
        free(pValue);
        return ret;
    }
    if (!isNull) {
        ret = compareVertexPropertyValue(ttype, expectValue, pValue, valSize);
    } else {
        if (expectValue == NULL) {
            ret = 0;
        } else {
            ret = 1;
        }
    }
    free(pValue);
    return ret;
}

typedef struct TestAllTypeArgs {
    char *keyName;
    uint32_t keyValue;
    // expected values
    uint32_t f0;
    char f1;
    unsigned char f2;
    int8_t f3;
    uint8_t f4;
    int16_t f5;
    uint16_t f6;
    int32_t f7;
    bool f8;
    int64_t f9;
    uint64_t f10;
    float f11;
    double f12;
    uint64_t f13;  // data type: time
    char *f14;     // data type: string
    char *f15;     // data type: bytes
    char *f16;     // data type: fixed
} TestAllTypeArgsT;

static Status CheckCltCfgValue(DbCfgItemT *cfgForClient, bool isSet)
{
    if (!isSet) {
        if (cfgForClient[CLT_CFG_AUDIT_LOG_DML_ENABLE].value.int32Val != AUDIT_LOG_ENABLE_DML_ORIGIN_VALUE) {
            return GMERR_INVALID_VALUE;
        }
        if (cfgForClient[CLT_CFG_DML_OPER_STAT_IS_ENABLED].value.int32Val != ENABLE_DML_OPER_STAT_ORIGIN_VALUE) {
            return GMERR_INVALID_VALUE;
        }
        if (cfgForClient[CLT_CFG_DML_PERF_STAT_IS_ENABLED].value.int32Val != ENABLE_DML_PERF_STAT_ORIGIN_VALUE) {
            return GMERR_INVALID_VALUE;
        }
        if (cfgForClient[CLT_CFG_COMPATIBLE_V3].value.int32Val != COMPATIBLE_V3_ORIGIN_VALUE) {
            return GMERR_INVALID_VALUE;
        }
    } else {
        if (cfgForClient[CLT_CFG_AUDIT_LOG_DML_ENABLE].value.int32Val != AUDIT_LOG_ENABLE_DML_AFTER_VALUE) {
            return GMERR_INVALID_VALUE;
        }
        if (cfgForClient[CLT_CFG_DML_OPER_STAT_IS_ENABLED].value.int32Val != ENABLE_DML_OPER_STAT_AFTER_VALUE) {
            return GMERR_INVALID_VALUE;
        }
        if (cfgForClient[CLT_CFG_DML_PERF_STAT_IS_ENABLED].value.int32Val != ENABLE_DML_PERF_STAT_AFTER_VALUE) {
            return GMERR_INVALID_VALUE;
        }
        if (cfgForClient[CLT_CFG_COMPATIBLE_V3].value.int32Val != COMPATIBLE_V3_AFTER_VALUE) {
            return GMERR_INVALID_VALUE;
        }
    }
    if (cfgForClient[CLT_CFG_DIRECT_WRITE].value.int32Val != DIRECT_WRITE_ORIGIN_VALUE) {
        return GMERR_INVALID_VALUE;
    }
    return GMERR_OK;
}

TEST_F(StClientDirectWrite, dw_replace_vertex_basic)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows = 0;
    ExpectedArgsT args;
    // 首次执行replace，表中无数据，实际表现为insert操作
    for (uint32_t i = 1; i <= 10; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t val = i;
        SetVertexProperty(stmt, val);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);

        args = {};
        SetExpectedArgs(&args, val);       // 设置预期值
        VerifyVertexProperty(stmt, args);  // 查询验证
    }

    // 第二次执行replace，表中存在数据，实际表现为全量替换
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t val = 11;
    SetVertexProperty(stmt, val);
    int32_t f0 = 10;  // 主键的值确保和原来的保持一致
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &f0, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2, affectRows);  // 记录存在，replace的逻辑是先删除再插入，affectRows会统计删除的行，即为2

    args = {.f0 = 10, .f1 = 11, .f2 = 11, .f3 = 11, .vrId = 11, .vrfIndex = 11, .ipAddr = 11, .maskLen = 11};
    VerifyVertexProperty(stmt, args);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 一般复杂表的字段验证
TEST_F(StClientDirectWrite, dw_replace_general_vertex_property_not_set)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_general_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    // normal
    ret = GmcPrepareStmtByLabelName(stmt, g_general_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f0 = 123;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    char *f1 = (char *)"hello, world!";
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, f1, (uint32_t)strlen(f1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // primary key not set, 预期失败
    ret = GmcPrepareStmtByLabelName(stmt, g_general_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    char *f1Str = (char *)"hello, world!";
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, f1Str, (uint32_t)strlen(f1Str));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_NE(GMERR_OK, ret);
    affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);

    // field with 'nullable=false' not set, 预期失败
    ret = GmcPrepareStmtByLabelName(stmt, g_general_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    f0 = 123;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_NE(GMERR_OK, ret);
    affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);

    ret = system("gmsysview -q V\\$QRY_DML_OPER_STATIS");
    EXPECT_EQ(GMERR_OK, ret);
    ret = system("gmsysview -q V\\$QRY_DML_INFO");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_general_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_insert_vertex_basic)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t val = 10;
    SetVertexProperty(stmt, val);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // 查询验证
    ExpectedArgsT args = {};
    SetExpectedArgs(&args, val);
    VerifyVertexProperty(stmt, args);
    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_insert_vertex_multi_set_property)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t val1 = 10;
    SetVertexProperty(stmt, val1);
    uint32_t val2 = 11;
    SetVertexProperty(stmt, val2);
    uint32_t val3 = 12;
    SetVertexProperty(stmt, val3);  // 反复set属性值，以最后一次set的为准
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // 查询验证
    ExpectedArgsT args = {};
    SetExpectedArgs(&args, val3);
    VerifyVertexProperty(stmt, args);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_insert_vertex_pk_violation)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t val = 10;
    SetVertexProperty(stmt, val);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // 查询验证
    ExpectedArgsT args = {};
    SetExpectedArgs(&args, val);
    VerifyVertexProperty(stmt, args);

    // 再次插入数据，primary key相同，出现冲突
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    SetVertexProperty(stmt, val);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_insert_vertex_sec_key_unique_violation)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t val = 10;
    SetVertexProperty(stmt, val);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // 查询验证
    ExpectedArgsT args = {};
    SetExpectedArgs(&args, val);
    VerifyVertexProperty(stmt, args);

    // 再次插入数据，构造UNIQUE_VIOLATION冲突
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    val = 20;
    SetVertexProperty(stmt, val);
    int32_t f1 = 10;  // 字段F1约束"unique"为true。设置该字段的值与前一条数据一致，从而构造唯一性冲突
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f2 = 10;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2, sizeof(int32_t));
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 说明
// 若vertex属于(1)一般复杂表，且checkValidity字段为false；(2)简单表和特殊复杂表
// 则仅对主键/资源字段/分区字段进行非空检查，不会对所有属性进行非空校验
TEST_F(StClientDirectWrite, dw_insert_simple_vertex_property_not_set)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // primary key not set
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t int32_tmp = 1001;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    int32_tmp = 1002;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_NE(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);

    // field with 'nullable=false' not set
    // 对于简单表，不会校验，预期正常
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int32_tmp = 1000;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    int32_tmp = 1002;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 说明
// 若vertex属于一般复杂表，且checkValidity字段为true，则对所有属性进行非空校验
TEST_F(StClientDirectWrite, dw_insert_general_vertex_property_not_set)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_general_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    // normal
    ret = GmcPrepareStmtByLabelName(stmt, g_general_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f0 = 123;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    char *f1 = (char *)"hello, world!";
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, f1, (uint32_t)strlen(f1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // primary key not set
    ret = GmcPrepareStmtByLabelName(stmt, g_general_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    char *f1Str = (char *)"hello, world!";
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, f1Str, (uint32_t)strlen(f1Str));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_NE(GMERR_OK, ret);
    affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);

    // field with 'nullable=false' not set
    ret = GmcPrepareStmtByLabelName(stmt, g_general_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    f0 = 123;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_NE(GMERR_OK, ret);
    affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);

    ret = GmcDropVertexLabel(stmt, g_general_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

static void checkAllProperties(GmcStmtT *stmt, TestAllTypeArgsT args)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, g_all_type_label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, args.keyName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &args.keyValue, sizeof(args.keyValue));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    int cacheRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_CACHE_ROWS, &cacheRows, sizeof(cacheRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, cacheRows);
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_UINT32, &args.f0);
        EXPECT_EQ(ret, GMERR_OK);
        ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_CHAR, &args.f1);
        EXPECT_EQ(ret, GMERR_OK);
        ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_UCHAR, &args.f2);
        EXPECT_EQ(ret, GMERR_OK);
        ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_INT8, &args.f3);
        EXPECT_EQ(ret, GMERR_OK);
        ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_UINT8, &args.f4);
        EXPECT_EQ(ret, GMERR_OK);
        ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_INT16, &args.f5);
        EXPECT_EQ(ret, GMERR_OK);
        ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_UINT16, &args.f6);
        EXPECT_EQ(ret, GMERR_OK);
        ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_INT32, &args.f7);
        EXPECT_EQ(ret, GMERR_OK);
        ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &args.f8);
        EXPECT_EQ(ret, GMERR_OK);
        ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &args.f9);
        EXPECT_EQ(ret, GMERR_OK);
        ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &args.f10);
        EXPECT_EQ(ret, GMERR_OK);
        ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &args.f11);
        EXPECT_EQ(ret, GMERR_OK);
        ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &args.f12);
        EXPECT_EQ(ret, GMERR_OK);
        ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &args.f13);
        EXPECT_EQ(ret, GMERR_OK);
        ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, args.f14);
        EXPECT_EQ(ret, GMERR_OK);
        ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, args.f15);
        EXPECT_EQ(ret, GMERR_OK);
        ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, args.f16);
        EXPECT_EQ(ret, GMERR_OK);
    }
}

TEST_F(StClientDirectWrite, dw_insert_vertex_all_types_with_default)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_all_type_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入数据，只set主键，其他字段使用默认值
    ret = GmcPrepareStmtByLabelName(stmt, g_all_type_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkVal = 10;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &pkVal, sizeof(pkVal));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // 基于主键进行查询验证
    TestAllTypeArgsT args = {.keyName = (char *)"K0",
        .keyValue = pkVal,
        .f0 = 10,
        .f1 = 'a',
        .f2 = 'f',
        .f3 = -8,
        .f4 = 8,
        .f5 = -16,
        .f6 = 16,
        .f7 = -32,
        .f8 = true,
        .f9 = -64,
        .f10 = 64,
        .f11 = (float)3.14,
        .f12 = (double)3.14,
        .f13 = (uint64_t)1000,
        .f14 = (char *)"test_string",
        .f15 = (char *)"test_bytes",
        .f16 = (char *)"test_fixed"};
    checkAllProperties(stmt, args);

    // 插入数据，set所有字段类型
    ret = GmcPrepareStmtByLabelName(stmt, g_all_type_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f0 = 20;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    char f1 = 'x';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_CHAR, &f1, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char f2 = 'y';
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UCHAR, &f2, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t f3 = 3;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT8, &f3, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t f4 = 4;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT8, &f4, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t f5 = 5;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_INT16, &f5, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t f6 = 6;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT16, &f6, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f7 = 7;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_INT32, &f7, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool f8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t f9 = 9;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &f9, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t f10 = 10;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &f10, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float f11 = (float)1.11;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &f11, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double f12 = 1.12;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &f12, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t f13 = 2000;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &f13, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr14[] = "new_string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr15[10] = "new_bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, (strlen(teststr15)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr16[11] = "aaaa_fixed";
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, (strlen(teststr16)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 查询验证
    args = {.keyName = (char *)"K0",
        .keyValue = f0,
        .f0 = f0,
        .f1 = f1,
        .f2 = f2,
        .f3 = f3,
        .f4 = f4,
        .f5 = f5,
        .f6 = f6,
        .f7 = f7,
        .f8 = f8,
        .f9 = f9,
        .f10 = f10,
        .f11 = f11,
        .f12 = f12,
        .f13 = f13,
        .f14 = teststr14,
        .f15 = teststr15,
        .f16 = teststr16};
    checkAllProperties(stmt, args);

    ret = GmcDropVertexLabel(stmt, g_all_type_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_update_vertex_basic)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t val = 10;
    SetVertexProperty(stmt, val);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // 查询验证
    ExpectedArgsT args = {};
    SetExpectedArgs(&args, val);
    VerifyVertexProperty(stmt, args);

    // 基于主键更新数据，主键存在
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t pkVal = 10;
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkVal, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f1 = 100;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f2 = 200;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2, sizeof(int32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);  // 主键存在，则正常更新，值为1；若主键不存在，则什么都不做，值为0

    // 查询验证
    args = {.f0 = 10, .f1 = f1, .f2 = f2, .f3 = 10, .vrId = 10, .vrfIndex = 10, .ipAddr = 10, .maskLen = 10};
    VerifyVertexProperty(stmt, args);

    // 基于主键更新数据，主键不存在
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t nonExistVal = 1234;
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &nonExistVal, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    f1 = 100;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    f2 = 200;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2, sizeof(int32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);  // 预期正常，但affectRows为0
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);  // 主键存在，则正常更新，值为1；若主键不存在，则什么都不做，值为0

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_update_vertex_multi_set_property)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t val = 10;
    SetVertexProperty(stmt, val);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // 更新数据，对字段F1反复set，更新时，以最后set的数据为准
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t pkVal = 10;
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkVal, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f1 = 100;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    f1 = 200;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    ExpectedArgsT args = {
        .f0 = 10, .f1 = 200, .f2 = 10, .f3 = 10, .vrId = 10, .vrfIndex = 10, .ipAddr = 10, .maskLen = 10};
    VerifyVertexProperty(stmt, args);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_update_vertex_key_wrong)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t val = 10;
    SetVertexProperty(stmt, val);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // 更新数据，key设置为空
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t pkVal = 10;
    ret = GmcSetIndexKeyName(stmt, "");  // 将keyName设置为空
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkVal, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f1 = 100;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f2 = 200;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2, sizeof(int32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 更新数据，key设置为一个不存在的值
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    pkVal = 10;
    ret = GmcSetIndexKeyName(stmt, "T39_K0_XXX");
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkVal, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2, sizeof(int32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_update_and_delete_vertex_32keys)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    const char *labelName = (char *)"test_100_fields_schema";
    const char *labelFilePath = "011_direct_write/st_data/test_directwrite_100_fields_label.gmjson";
    string labelSchema = GetFileContext(labelFilePath);
    Status ret = GmcCreateVertexLabel(stmt, labelSchema.c_str(), g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t val = 123;
    uint32_t len = 10;
    char field_name[len];
    for (uint32_t i = 0; i < 100; i++) {
        sprintf_s(field_name, len, "F%04u", i);
        ret = GmcSetVertexProperty(stmt, field_name, GMC_DATATYPE_UINT32, &val, sizeof(val));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新数据
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "K0");
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 32; i++) {
        ret = GmcSetIndexKeyValue(stmt, i, GMC_DATATYPE_UINT32, &val, sizeof(val));
        ASSERT_EQ(GMERR_OK, ret);
    }
    uint32_t newVal = 456;
    for (uint32_t i = 32; i < 100; i++) {
        sprintf_s(field_name, len, "F%04u", i);
        ret = GmcSetVertexProperty(stmt, field_name, GMC_DATATYPE_UINT32, &newVal, sizeof(newVal));
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除数据
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "K0");
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 32; i++) {
        ret = GmcSetIndexKeyValue(stmt, i, GMC_DATATYPE_UINT32, &val, sizeof(val));
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 验证场景：更新lpm索引。预期结果：lpm索引不允许更新，执行失败
TEST_F(StClientDirectWrite, dw_update_vertex_lpm)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t val = 10;
    SetVertexProperty(stmt, val);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // 基于主键更新数据
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t pkVal = 10;
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkVal, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f1 = 100;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f2 = 200;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2, sizeof(int32_t));
    EXPECT_EQ(ret, GMERR_OK);
    // 设置lpm索引相关字段
    uint32_t vrID = 15;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vrID, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t vrfIndex = 1023;
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t ipAddr = 15;
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &ipAddr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t maskLen = 32;
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);  // lmp索引不支持更新
    const char *lastErrInfo1 = GmcGetLastError();
    char buf[512] = {0};
    ret = strcpy_s(buf, sizeof(buf), lastErrInfo1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);

    // 通过CS再执行一遍，比较两者的last error信息是否一致
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkVal, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 设置lpm索引相关字段
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vrID, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &ipAddr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);  // lmp索引不支持更新
    const char *lastErrInfo2 = GmcGetLastError();
    EXPECT_EQ(0, strcmp(lastErrInfo2, buf));

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

void testMultiOpsInSameStmt(GmcStmtT *stmt)
{
    // Insert
    Status ret;
    uint64_t insertNums = 100;
    int32_t int32_tmp;
    for (uint64_t i = 0; i < insertNums; i++) {
        GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
        SetVertexProperty(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        int affectRows = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t actualNums = 0;
    ret = GmcGetVertexRecordCount(stmt, &actualNums);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(insertNums, actualNums);

    // update 直连写仅支持基于主键更新
    for (uint64_t i = 0; i < insertNums; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_UPDATE);
        int32_t pkVal = i;
        ret = GmcSetIndexKeyName(stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkVal, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 更新F1, F2, F3字段的值
        int32_tmp = i + 1000;  // F1需满足唯一索引要求
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        int affectRows = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    // 随机查询一条数据进行验证
    ExpectedArgsT args = {
        .f0 = 10, .f1 = 1010, .f2 = 1010, .f3 = 1010, .vrId = 10, .vrfIndex = 10, .ipAddr = 10, .maskLen = 10};
    VerifyVertexProperty(stmt, args);

    // replace 对于表中存在的记录：即更新逻辑；对于表中不存在的记录：即插入逻辑
    uint64_t start = 90, end = 110;  // 主键范围，[90, 99]存在，[100, 109]不存在
    for (uint64_t i = start; i < end; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
        int32_tmp = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_tmp = i + 2000;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // 设置lpm索引相关字段
        uint32_t vrID = int32_tmp % 16;
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vrID, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t vrfIndex = int32_tmp % 1024;
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t ipAddr = int32_tmp;
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &ipAddr, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t maskLen = int32_tmp % 33;
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        int affectRows = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        if (i < 100) {
            EXPECT_EQ(2, affectRows);  // 表中存在的记录，先删除再插入，affectRows会统计删除的行，即为2
        } else {
            EXPECT_EQ(1, affectRows);  // 表中不存在的记录，相当于插入操作，affectRows即为1
        }
    }
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    actualNums = 0;
    ret = GmcGetVertexRecordCount(stmt, &actualNums);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(insertNums + 10, actualNums);

    // delete 直连写仅支持基于主键来删除数据
    for (uint64_t i = 0; i < end; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
        int32_tmp = i;
        ret = GmcSetIndexKeyName(stmt, "T39_K0");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t affectRows = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1u, affectRows);
    }
    // 删除完成后再次查询，全表扫描，此时记录已经被删完了
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    actualNums = 0;
    ret = GmcGetVertexRecordCount(stmt, &actualNums);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, actualNums);
}

TEST_F(StClientDirectWrite, dw_multi_operation)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    testMultiOpsInSameStmt(stmt);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_multi_operation_with_namespace)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    const char *nsp = "test_namespace";
    Status ret = GmcCreateNamespace(stmt, nsp, "user");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, nsp);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    testMultiOpsInSameStmt(stmt);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt, nsp);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_tree_vertex)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_tree_label_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    // 1.insert
    ret = GmcPrepareStmtByLabelName(stmt, g_tree_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // 待插入表中的数据
    auto jsonData = R"([{
        "nftable_table_name": "table_name1",
        "nftable_table_family": "",
        "nftable_table_flags": 0,
        "nftable_table_use": 0,
        "nftable_chain": [
            {
                "nftable_chain_name": "chain1",
                "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;",
                "nftable_chain_table": "test1",
                "nftable_chain_family": "",
                "nftable_chain_policy": 0,
                "nftable_chain_hooknum": 0,
                "nftable_chain_prio": 0,
                "nftable_chain_use": 0,
                "nftable_chain_handle": "0",
                "nftable_chain_basechainflag": 0,
                "nftable_chain_policyflag": 0,
                "nftable_chain_devflag": 0
            },
            {
                "nftable_chain_name": "chain2",
                "nftable_chain_type": "type filter hook prerouting priority 20; policy accept;",
                "nftable_chain_table": "test2",
                "nftable_chain_family": "",
                "nftable_chain_policy": 0,
                "nftable_chain_hooknum": 0,
                "nftable_chain_prio": 0,
                "nftable_chain_use": 0,
                "nftable_chain_handle": "0",
                "nftable_chain_basechainflag": 0,
                "nftable_chain_policyflag": 0,
                "nftable_chain_devflag": 0
            }
        ]
    }])";
    json_error_t jsonError;
    json_t *arrayJson = json_loads(jsonData, JSON_REJECT_DUPLICATES, &jsonError);
    size_t arraySize = json_array_size(arrayJson);
    size_t i = 0;
    while (i < arraySize) {
        json_t *itemJson = json_array_get(arrayJson, i);
        char *jStr = json_dumps(itemJson, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(jStr);
        i++;
    }
    json_decref(arrayJson);

    // 查询验证
    uint32_t eleCount = 2;
    uint32_t memSize = sizeof(ElementArgsT) * eleCount;
    ElementArgsT *eleArrayPtr = (ElementArgsT *)malloc(memSize);
    EXPECT_TRUE(eleArrayPtr != NULL);
    (void)memset_s(eleArrayPtr, memSize, 0x00, memSize);
    (void)strcpy_s(eleArrayPtr[0].nftableChainName, strlen("chain1") + 1, "chain1");
    (void)strcpy_s(eleArrayPtr[0].nftableChainTable, strlen("test1") + 1, "test1");
    (void)strcpy_s(eleArrayPtr[1].nftableChainName, strlen("chain2") + 1, "chain2");
    (void)strcpy_s(eleArrayPtr[1].nftableChainTable, strlen("test2") + 1, "test2");
    TestTreeArgsT args = {.keyValue = (char *)"table_name1", .eleCount = eleCount, .eleArray = eleArrayPtr};
    fetchAndCompareForTreeVertex(stmt, args);
    free(eleArrayPtr);
    eleArrayPtr = NULL;

    // 2. update
    ret = GmcPrepareStmtByLabelName(stmt, g_tree_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *chainNode = NULL;
    GmcNodeT *eleItemNode = NULL;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    EXPECT_EQ(GMERR_OK, ret);
    // 向"nftable_chain"节点增加元素
    ret = GmcNodeAppendElement(chainNode, &eleItemNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(eleItemNode, "nftable_chain_name", GMC_DATATYPE_STRING, "chain4", strlen("chain4"));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(eleItemNode, "nftable_chain_table", GMC_DATATYPE_STRING, "test4", strlen("test4"));
    EXPECT_EQ(GMERR_OK, ret);
    // 基于下标获取元素，并修改这个元素中的值
    ret = GmcNodeGetElementByIndex(chainNode, 0, &eleItemNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(eleItemNode, "nftable_chain_name", GMC_DATATYPE_STRING, "chain3", strlen("chain3"));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(eleItemNode, "nftable_chain_table", GMC_DATATYPE_STRING, "test3", strlen("test3"));
    EXPECT_EQ(GMERR_OK, ret);
    // 基于主键进行更新
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, "table_name1", strlen("table_name1"));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "access_control_list_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 查询验证
    eleCount = 3;
    memSize = sizeof(ElementArgsT) * eleCount;
    eleArrayPtr = (ElementArgsT *)malloc(memSize);
    EXPECT_TRUE(eleArrayPtr != NULL);
    (void)memset_s(eleArrayPtr, memSize, 0x00, memSize);
    (void)strcpy_s(eleArrayPtr[0].nftableChainName, strlen("chain3") + 1, "chain3");
    (void)strcpy_s(eleArrayPtr[0].nftableChainTable, strlen("test3") + 1, "test3");
    (void)strcpy_s(eleArrayPtr[1].nftableChainName, strlen("chain2") + 1, "chain2");
    (void)strcpy_s(eleArrayPtr[1].nftableChainTable, strlen("test2") + 1, "test2");
    (void)strcpy_s(eleArrayPtr[2].nftableChainName, strlen("chain4") + 1, "chain4");
    (void)strcpy_s(eleArrayPtr[2].nftableChainTable, strlen("test4") + 1, "test4");
    args = {.keyValue = (char *)"table_name1", .eleCount = eleCount, .eleArray = eleArrayPtr};
    fetchAndCompareForTreeVertex(stmt, args);
    free(eleArrayPtr);
    eleArrayPtr = NULL;

    char *vertexJson;
    ret = GmcDumpVertexToJson(stmt, 0, &vertexJson);  // 备份数据，后续使用

    // 3. replace
    ret = GmcPrepareStmtByLabelName(stmt, g_tree_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(
        rootNode, "nftable_table_name", GMC_DATATYPE_STRING, "table_name2", strlen("table_name2"));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeAppendElement(chainNode, &eleItemNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(eleItemNode, "nftable_chain_name", GMC_DATATYPE_STRING, "chain5", strlen("chain5"));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(eleItemNode, "nftable_chain_table", GMC_DATATYPE_STRING, "test5", strlen("test5"));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  // 由于key="table_name2"的记录不存在，因此会插入
    EXPECT_EQ(GMERR_OK, ret);
    // 查询验证
    eleCount = 1;
    memSize = sizeof(ElementArgsT) * eleCount;
    eleArrayPtr = (ElementArgsT *)malloc(memSize);
    EXPECT_TRUE(eleArrayPtr != NULL);
    (void)memset_s(eleArrayPtr, memSize, 0x00, memSize);
    (void)strcpy_s(eleArrayPtr[0].nftableChainName, strlen("chain5") + 1, "chain5");
    (void)strcpy_s(eleArrayPtr[0].nftableChainTable, strlen("test5") + 1, "test5");
    args = {.keyValue = (char *)"table_name2", .eleCount = eleCount, .eleArray = eleArrayPtr};
    fetchAndCompareForTreeVertex(stmt, args);
    free(eleArrayPtr);
    eleArrayPtr = NULL;

    // 3. replace - remove element
    ret = GmcPrepareStmtByLabelName(stmt, g_tree_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexByJson(stmt, 0, vertexJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "nftable_chain", &chainNode);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t count = 0;
    ret = GmcNodeGetElementCount(chainNode, &count);
    EXPECT_EQ(GMERR_OK, ret);
    ASSERT_EQ(3u, count);
    // 移除元素
    GmcIndexKeyT *idxKey = NULL;
    ret = GmcNodeAllocKey(chainNode, "nftable_chain_key", &idxKey);
    EXPECT_EQ(GMERR_OK, ret);
    // 元素key值不存在
    ret = GmcNodeSetKeyValue(idxKey, 0, GMC_DATATYPE_STRING, "chainxxx", strlen("chainxxx"));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeRemoveElementByKey(chainNode, idxKey);
    EXPECT_EQ(GMERR_NO_DATA, ret);
    ret = GmcNodeGetElementCount(chainNode, &count);
    EXPECT_EQ(GMERR_OK, ret);
    ASSERT_EQ(3u, count);  // 无变化
    // 元素key值存在
    ret = GmcNodeSetKeyValue(idxKey, 0, GMC_DATATYPE_STRING, "chain4", strlen("chain4"));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeRemoveElementByKey(chainNode, idxKey);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementCount(chainNode, &count);
    EXPECT_EQ(GMERR_OK, ret);
    ASSERT_EQ(2u, count);
    ret = GmcNodeRemoveElementByIndex(chainNode, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementCount(chainNode, &count);
    EXPECT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1u, count);
    ret = GmcNodeSetPropertyByName(
        rootNode, "nftable_table_name", GMC_DATATYPE_STRING, "table_name1", strlen("table_name1"));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 4. delete 删除主键为"table_name2"的记录
    ret = GmcPrepareStmtByLabelName(stmt, g_tree_label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    const char *keyName = "access_control_list_key";
    const char *keyValue = "table_name2";
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, keyValue, strlen(keyValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    ret = GmcDropVertexLabel(stmt, g_tree_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_big_object)
{
#define QRY_BIG_TEST_STR_LEN 4096
    const char *big_object_label_name = "BIG_OBJECT_LABEL";
    const char *test_big_object_label_json =
        R"([{
            "type":"record",
            "config": {
                "direct_write": true
            },
            "name":"BIG_OBJECT_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"string"},
                    {"name":"F2", "type":"string"},
                    {"name":"F3", "type":"string"},
                    {"name":"F4", "type":"string"},
                    {"name":"F5", "type":"string"},
                    {"name":"F6", "type":"string"},
                    {"name":"F7", "type":"string"},
                    {"name":"F8", "type":"string"},
                    {"name":"F9", "type":"string"},
                    {"name":"F10", "type":"string"},
                    {"name":"F11", "type":"string"}
                ],
            "keys":
                [
                    {
                        "node":"BIG_OBJECT_LABEL",
                        "name":"BIG_OBJECT_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"}
                    }
                ]
            }])";
    int32_t ret;
    GmcConnT *conn;
    GmcStmtT *stmt;

    auto fetchData = [&stmt, &conn, &big_object_label_name](int pkIdxValue, const char *f1Value) {
        // 基于主键索引查询数据
        Status ret = GmcPrepareStmtByLabelName(stmt, big_object_label_name, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        int pkValue = pkIdxValue;
        ret = GmcSetIndexKeyName(stmt, "BIG_OBJECT_INDEX");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkValue, sizeof(int));
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
        while (true) {
            bool isFinish;
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            int32_t f0 = 0;
            char f1[QRY_BIG_TEST_STR_LEN];
            bool isNull = false;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &f0, sizeof(f0), &isNull);
            EXPECT_EQ(ret, GMERR_OK);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(pkIdxValue, f0);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &f1, sizeof(f1), &isNull);
            EXPECT_EQ(ret, GMERR_OK);
            EXPECT_FALSE(isNull);
            int isEQ = memcmp(f1Value, f1, (size_t)sizeof(f1Value));
            EXPECT_EQ(isEQ, 0);
        }
    };
    auto makeStr = [](int length, char *str) {
        memset_s(str, QRY_BIG_TEST_STR_LEN - 1, 0x00, QRY_BIG_TEST_STR_LEN - 1);
        for (int i = 0; i < length - 1; i++) {
            str[i] = 'x';
        }
        str[length - 1] = '\0';
    };

    CreateSyncConnectionAndStmt(&conn, &stmt);
    ret = GmcCreateVertexLabel(stmt, test_big_object_label_json, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    char insertStr[QRY_BIG_TEST_STR_LEN];
    char replaceStr[QRY_BIG_TEST_STR_LEN];
    char updateStr[QRY_BIG_TEST_STR_LEN];
    makeStr(1024, insertStr);
    makeStr(2048, replaceStr);
    makeStr(QRY_BIG_TEST_STR_LEN, updateStr);

    for (int32_t i = 0; i < 50; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, big_object_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, insertStr, strlen(insertStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_STRING, insertStr, strlen(insertStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, insertStr, strlen(insertStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, insertStr, strlen(insertStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, insertStr, strlen(insertStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, insertStr, strlen(insertStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_STRING, insertStr, strlen(insertStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, insertStr, strlen(insertStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_STRING, insertStr, strlen(insertStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_STRING, insertStr, strlen(insertStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_STRING, insertStr, strlen(insertStr)));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    }
    fetchData(1, insertStr);

    for (int32_t i = 0; i < 50; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, big_object_label_name, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, replaceStr, strlen(replaceStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_STRING, replaceStr, strlen(replaceStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, replaceStr, strlen(replaceStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, replaceStr, strlen(replaceStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, replaceStr, strlen(replaceStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, replaceStr, strlen(replaceStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_STRING, replaceStr, strlen(replaceStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, replaceStr, strlen(replaceStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_STRING, replaceStr, strlen(replaceStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_STRING, replaceStr, strlen(replaceStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_STRING, replaceStr, strlen(replaceStr)));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    }
    fetchData(1, replaceStr);

    for (int i = 0; i < 50; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, big_object_label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "BIG_OBJECT_INDEX"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(i)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, updateStr, strlen(updateStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_STRING, updateStr, strlen(updateStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, updateStr, strlen(updateStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, updateStr, strlen(updateStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, updateStr, strlen(updateStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, updateStr, strlen(updateStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_STRING, updateStr, strlen(updateStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, updateStr, strlen(updateStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_STRING, updateStr, strlen(updateStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_STRING, updateStr, strlen(updateStr)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_STRING, updateStr, strlen(updateStr)));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    }
    fetchData(1, updateStr);

    ret = GmcDropVertexLabel(stmt, big_object_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 验证带有分区字段的表
TEST_F(StClientDirectWrite, dw_multi_operation_veretx_with_partition)
{
    static const char *label_name_partition = "label_with_partition";
    static const char *label_schema_partition =
        R"([{
        "type":"record",
        "config": {
            "direct_write": true
        },
        "name":"label_with_partition",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"uint32: 8", "nullable":false},
                {"name":"F2", "type":"partition", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"label_with_partition",
                    "name":"K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, label_schema_partition, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    // 1. PARTITION字段不设置，预期错误
    ret = GmcPrepareStmtByLabelName(stmt, label_name_partition, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t value = 16;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_BITFIELD32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  // PARTITION字段不能为空，在约束性校验中会被拦截
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);

    // 2. PARTITION字段设置一个非法值，预期错误
    ret = GmcPrepareStmtByLabelName(stmt, label_name_partition, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_BITFIELD32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t f2Value = 16;  // PARTITION字段的取值范围是[0,15]
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    // 3. PARTITION字段正常设置，此时执行预期成功
    f2Value = 0;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // replace partition字段的值不支持更新
    // 如果是replace-update，对于partition字段的约束应和update操作保持一致
    ret = GmcPrepareStmtByLabelName(stmt, label_name_partition, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f1Value = 14;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_BITFIELD32, &f1Value, sizeof(f1Value));
    EXPECT_EQ(GMERR_OK, ret);
    f2Value = 15;  // 由0更新至15，预期失败
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_INTERNAL_ERROR, ret);

    // update partition字段的值不支持更新
    // part 1: 当partition字段设置不一样的值时，预期失败
    ret = GmcPrepareStmtByLabelName(stmt, label_name_partition, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "K0");
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t f2ValueTmp = 11;  // 由0更新至11，预期失败
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f2ValueTmp, sizeof(f2ValueTmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_INTERNAL_ERROR, ret);

    // part 2: 当partition字段设置一样的值时，预期成功
    ret = GmcPrepareStmtByLabelName(stmt, label_name_partition, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "K0");
    EXPECT_EQ(GMERR_OK, ret);
    f2ValueTmp = 0;  // 值仍然为0，预期成功
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f2ValueTmp, sizeof(f2ValueTmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, label_name_partition);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// xxxLimitCheck为直连写约束场景验证
TEST_F(StClientDirectWrite, dw_mergeOperation_LimitCheck)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_concurrencyControlType_LimitCheck)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 默认创建的表就是READ_UNCOMMIT模式，即isFastReadUncommitted为1，enableTableLock为0
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcResetStmt(stmt);
    ret = GmcDropVertexLabel(stmt, g_label_name);
    ASSERT_EQ(GMERR_OK, ret);

    // NORMAL模式
    const char *config_normal = R"({"isFastReadUncommitted":false})";
    ret = GmcCreateVertexLabel(stmt, g_label_schema, config_normal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcResetStmt(stmt);
    ret = GmcDropVertexLabel(stmt, g_label_name);
    ASSERT_EQ(GMERR_OK, ret);

    // LABEL_LATCH模式
    const char *config_label_latch = R"({"isFastReadUncommitted":true, "enableTableLock":true})";
    ret = GmcCreateVertexLabel(stmt, g_label_schema, config_label_latch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_label_name);
    ASSERT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_autoInc_LimitCheck)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // Label包含自增列
    const char *label_name = "auto_inc_test";
    const char *auto_inc_config = R"({"max_record_count":1000, "auto_increment":100})";
    const char *auto_inc_label_schema =
        R"([{
        "type":"record",
        "config": {
            "direct_write": true
        },
        "name":"auto_inc_test",
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false, "auto_increment":true},
                {"name":"F1", "type":"uint32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"auto_inc_test",
                    "name":"auto_inc_test_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";
    Status ret = GmcCreateVertexLabel(stmt, auto_inc_label_schema, auto_inc_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, label_name);
    ASSERT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_resPool_LimitCheck)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // Label包含资源字段
    const char *res_label_name = "dw_resource_label";
    const char *res_label_schema = R"([{
        "type": "record",
        "config": {
            "direct_write": true
        },
        "name": "dw_resource_label",
        "fields": [
            { "name": "F0", "type": "int32" },
            { "name": "R0", "type": "resource", "nullable": false },
            { "name": "R1", "type": "resource", "nullable": false }
        ],
        "keys": [
            {
                "node": "dw_resource_label",
                "name": "resource_label_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }])";
    const char *res_pool_name = "dw_res_pool";
    const char *res_pool_schema = R"({
        "name": "dw_res_pool",
        "pool_id": 0,
        "start_id": 0,
        "capacity": 10,
        "order": 0,
        "alloc_type": 0
    })";

    Status ret = GmcCreateVertexLabel(stmt, res_label_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建并绑定资源池到表
    ret = GmcCreateResPool(stmt, res_pool_schema);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBindResPoolToLabel(stmt, res_pool_name, res_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    // 获取资源池json
    char *resPoolJson;
    ret = GmcGetResPool(stmt, res_pool_name, &resPoolJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, res_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(stmt, res_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(stmt, res_pool_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, res_label_name);
    ASSERT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

// 对于这种场景，必须开启isFastReadUncommitted=0，即Normal模式
TEST_F(StClientDirectWrite, dw_edgeLabel_LimitCheck)
{
    // edgeLabel
    const char *edgeLabelName = "autoEdgelable";
    const char *cfgJson = R"({"max_record_count":1000, "isFastReadUncommitted":false})";
    const char *labelJson =
        R"([{
            "name":"autoEdgelable",
            "source_vertex_label":"edgeAutoSrcVertex", "comment":"the edge 7 to 8",
            "dest_vertex_label":"edgeAutoDstVertex",
            "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {
                            "source_property": "F2",
                            "dest_property": "F2"
                        },
                        {
                            "source_property": "F3",
                            "dest_property": "F3"
                        }
                    ]
            }
        }])";
    // srcVertexLabel
    const char *edgeSrcVertexLabelName = "edgeAutoSrcVertex";
    const char *srccfgJson1 = R"({"max_record_count":1000, "isFastReadUncommitted":false})";
    const char *edgeSrcVertexLabel =
        R"([{
            "type":"record",
            "config": {
                "direct_write": true
            },
            "name":"edgeAutoSrcVertex",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"edgeAutoSrcVertex",
                        "name":"edgeAutoSrcVertex_PK0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    // destVertexLabel
    const char *edgeDstVertexLabelName = "edgeAutoDstVertex";
    const char *dstcfgJson1 = R"({"max_record_count":1000, "isFastReadUncommitted":false})";
    const char *edgeDstVertexLabel =
        R"([{
            "type":"record",
            "config": {
                "direct_write": true
            },
            "name":"edgeAutoDstVertex",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"edgeAutoDstVertex",
                        "name":"edgeAutoDstVertex_PK0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 设置自动建出入边
    int createEdgeDirection = GMC_EDGE_DIRECTION_BOTH;
    Status ret =
        GmcSetStmtAttr(stmt, GMC_STMT_ATTR_AUTO_CREATE_EDGE, &createEdgeDirection, sizeof(createEdgeDirection));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned int tmpEdgeDirection = 9999;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AUTO_CREATE_EDGE, &tmpEdgeDirection, sizeof(tmpEdgeDirection));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((unsigned int)GMC_EDGE_DIRECTION_BOTH, tmpEdgeDirection);
    // 创建 src vertexLable
    ret = GmcCreateVertexLabel(stmt, edgeSrcVertexLabel, srccfgJson1);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建 dest vertexLable
    ret = GmcCreateVertexLabel(stmt, edgeDstVertexLabel, dstcfgJson1);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建 edgeLable
    ret = GmcCreateEdgeLabel(stmt, labelJson, cfgJson);
    ASSERT_EQ(GMERR_OK, ret);

    // 向srcVertex插入数据。直连写流程不支持涉及边表的场景，路由至CS路径执行
    ret = GmcPrepareStmtByLabelName(stmt, edgeSrcVertexLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropEdgeLabel(stmt, edgeLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, edgeSrcVertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, edgeDstVertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_bitmap_LimitCheck)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    const char *labelName = "label_with_bitmap";
    const char *labelWithBitmap =
        R"([{
            "type":"record",
            "config": {
                "direct_write": true
            },
            "name":"label_with_bitmap",
            "fields":[
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"bitmap", "size":128}
            ],
            "keys":[
                {
                    "node":"label_with_bitmap",
                    "name":"pk",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";
    Status ret = GmcCreateVertexLabel(stmt, labelWithBitmap, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_delete_vertex_basic)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t insertNums = 100;
    uint64_t affectRows = 0;
    for (uint64_t i = 0; i < insertNums; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        SetVertexProperty(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1u, affectRows);
    }
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t actualNums = 0;
    ret = GmcGetVertexRecordCount(stmt, &actualNums);
    EXPECT_EQ(GMERR_OK, ret);
    ASSERT_EQ(insertNums, actualNums);

    // 删除数据(基于主键删除)，被删除的记录存在，正常删除
    int32_t pkVal;
    for (uint64_t i = 0; i < insertNums; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        pkVal = i;
        ret = GmcSetIndexKeyName(stmt, "T39_K0");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkVal, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        affectRows = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1u, affectRows);
    }
    // 删除后，表中的记录数为0
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(stmt, &actualNums);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, actualNums);

    // 删除数据，被删除的记录不存在，GmcExecute返回值为GMERR_OK，但是affectedRows为0
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    pkVal = 9999;
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkVal, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, affectRows);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_delete_vertex_no_pk)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t val = 10;
    SetVertexProperty(stmt, val);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // 删除时未指定主键，即全量删除数据，直连写不支持，路由至CS路径来执行
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_delete_vertex_cond_sec_index)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t val = 10;
    SetVertexProperty(stmt, val);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // 删除数据，基于二级索引删除
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    val = 1002;
    ret = GmcSetIndexKeyName(stmt, "T39_K2");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &val, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  // 此处走CS
    EXPECT_EQ(GMERR_OK, ret);

    // 删除数据，基于过滤条件
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "T39.F0=1000");  // 若过滤条件为NULL，则表示全表扫描
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  // 此处走CS
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_delete_vertex_wrong_keyname)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除数据，主键的keyName是一个不存在的值
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t int32_tmp = 1000;
    ret = GmcSetIndexKeyName(stmt, "nonexist_key_name");
    ASSERT_EQ(GMERR_INVALID_NAME, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);
    // 删除数据，主键的keyName为NULL
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, NULL);
    ASSERT_EQ(GMERR_OK, ret);  // 注意，这里返回是 GMERR_OK，而不是 GMERR_INVALID_NAME
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

/*
当GmcPrepareStmtByLabelName和GmcPrepareStmtByLabelName接口统一后，
就无法通过这两个接口来区分直连写和C/S写，任意操作，无论使用哪一个接口，只要满足直连写的使用条件，就都会走直连写。
这个用例可以考虑下架
*/
// 验证场景：直连写接口插入，C/S接口删除
TEST_F(StClientDirectWrite, dw_insert_cs_delete)
{
    GmcConnT *csConn = NULL, *dwConn = NULL;
    GmcStmtT *csStmt = NULL, *dwStmt = NULL;
    CreateSyncConnectionAndStmt(&dwConn, &dwStmt);
    CreateSyncConnectionAndStmt(&csConn, &csStmt);

    Status ret = GmcCreateVertexLabel(dwStmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t affectRows = 0, insertNums = 1;
    for (uint32_t i = 0; i < insertNums; i++) {
        // 通过直连写流程写入
        ret = GmcPrepareStmtByLabelName(dwStmt, g_label_name, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        SetVertexProperty(dwStmt, i);
        ret = GmcExecute(dwStmt);
        EXPECT_EQ(GMERR_OK, ret);
        affectRows = 0;
        ret = GmcGetStmtAttr(dwStmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1u, affectRows);
    }

    ret = GmcPrepareStmtByLabelName(dwStmt, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t actualNums = 0;
    ret = GmcGetVertexRecordCount(dwStmt, &actualNums);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(insertNums, actualNums);

    // 通过C/S接口删除数据
    ret = GmcPrepareStmtByLabelName(csStmt, g_label_name, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < insertNums; i++) {
        int32_t int32_tmp = i;
        ret = GmcSetIndexKeyName(csStmt, "T39_K0");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(csStmt, 0, GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(csStmt);
        EXPECT_EQ(GMERR_OK, ret);
        affectRows = 0;
        ret = GmcGetStmtAttr(csStmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1u, affectRows);
    }

    // 删除完成后再次查询，值不存在
    ret = GmcPrepareStmtByLabelName(csStmt, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(csStmt, &actualNums);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, actualNums);

    ret = GmcDropVertexLabel(dwStmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(dwConn, dwStmt);
    DestroyConnectionAndStmt(csConn, csStmt);
}

// 验证场景：C/S接口插入，直连写接口删除
TEST_F(StClientDirectWrite, dw_cs_insert_dw_delete)
{
    GmcConnT *csConn = NULL, *dwConn = NULL;
    GmcStmtT *csStmt = NULL, *dwStmt = NULL;
    CreateSyncConnectionAndStmt(&dwConn, &dwStmt);
    CreateSyncConnectionAndStmt(&csConn, &csStmt);

    Status ret = GmcCreateVertexLabel(csStmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 通过C/S接口插入数据
    ret = GmcPrepareStmtByLabelName(csStmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t int32_tmp;
    uint32_t affectRows = 0, insertNums = 1;
    for (uint32_t i = 0; i < insertNums; i++) {
        SetVertexProperty(csStmt, i);
        ret = GmcExecute(csStmt);
        EXPECT_EQ(GMERR_OK, ret);
        affectRows = 0;
        ret = GmcGetStmtAttr(csStmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1u, affectRows);
    }

    ret = GmcPrepareStmtByLabelName(csStmt, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t actualNums = 0;
    ret = GmcGetVertexRecordCount(csStmt, &actualNums);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(insertNums, actualNums);

    for (uint32_t i = 0; i < insertNums; i++) {
        // 通过直连写接口删除数据
        ret = GmcPrepareStmtByLabelName(dwStmt, g_label_name, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
        int32_tmp = i;
        ret = GmcSetIndexKeyName(dwStmt, "T39_K0");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(dwStmt, 0, GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(dwStmt);
        EXPECT_EQ(GMERR_OK, ret);
        affectRows = 0;
        ret = GmcGetStmtAttr(dwStmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1u, affectRows);
    }

    // 删除完成后再次查询，值不存在
    ret = GmcPrepareStmtByLabelName(dwStmt, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(dwStmt, &actualNums);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, actualNums);

    ret = GmcDropVertexLabel(dwStmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(dwConn, dwStmt);
    DestroyConnectionAndStmt(csConn, csStmt);
}

// 验证场景：显式开启事务场景，不支持操作配置了轻量化事务的表项
// 对于直连写，若识别到是显式开启事务场景，统一导流至C/S路径
TEST_F(StClientDirectWrite, dw_not_support_explicit_trx)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    const char *label_config = R"({"isFastReadUncommitted":1, "enableTableLock":0})";
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务开启
    ret = GmcTransStart(conn, NULL);  // 使用默认配置项
    ASSERT_EQ(GMERR_OK, ret);

    // 插入数据
    uint32_t recordNums = 2;
    for (uint32_t i = 1; i <= recordNums; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        SetVertexProperty(stmt, i);
        ret = GmcExecute(stmt);  // 走C/S路径
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    }

    // 事务提交
    ret = GmcTransCommit(conn);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_multi_prepare_1)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t val = 10;
    SetVertexProperty(stmt, val);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    ExpectedArgsT args = {};
    SetExpectedArgs(&args, val);       // 设置预期值
    VerifyVertexProperty(stmt, args);  // 查询验证

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_multi_prepare_2)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t val = 10;
    SetVertexProperty(stmt, val);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    ExpectedArgsT args = {};
    SetExpectedArgs(&args, val);       // 设置预期值
    VerifyVertexProperty(stmt, args);  // 查询验证

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_multi_prepare_3)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t val = 10;
    SetVertexProperty(stmt, val);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    ExpectedArgsT args = {};
    SetExpectedArgs(&args, val);       // 设置预期值
    VerifyVertexProperty(stmt, args);  // 查询验证

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_multi_prepare_4)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    GmcAllocStmt(conn, &stmt);
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t val = 10;
    SetVertexProperty(stmt, val);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    ExpectedArgsT args = {};
    SetExpectedArgs(&args, val);       // 设置预期值
    VerifyVertexProperty(stmt, args);  // 查询验证

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDirectWrite, dw_multi_prepare_5)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcResetStmt(stmt);
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t val = 10;
    SetVertexProperty(stmt, val);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    ExpectedArgsT args = {};
    SetExpectedArgs(&args, val);       // 设置预期值
    VerifyVertexProperty(stmt, args);  // 查询验证

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 一个conn下包含多个stmt
TEST_F(StClientDirectWrite, dw_same_conn_multi_stmt)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt1 = NULL, *stmt2 = NULL;
    const char *userName = "XXuser";
#if (defined RTOSV2 || defined RTOSV2X)
    const char *serverLocator = "channel:";
#else
    const char *serverLocator = "usocket:/run/verona/unix_emserver";
#endif
    Status ret;

    ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt1, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    testMultiOpsInSameStmt(stmt1);
    testMultiOpsInSameStmt(stmt2);

    ret = GmcDropVertexLabel(stmt1, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt1);
    GmcFreeStmt(stmt2);
    ASSERT_EQ(GMERR_OK, GmcDisconnect(conn));
}

/******* lasterr问题看护 *******/
/*
当GmcPrepareStmtByLabelName和GmcPrepareStmtByLabelName接口统一后，
就无法通过这两个接口来区分直连写和C/S写，任意操作，无论使用哪一个接口，只要满足直连写的使用条件，就都会走直连写。
因此无法再通过用例来看护直连写和C/S写的lasterr是否一致的问题
*/
// 场景1. 主键冲突
TEST_F(StClientDirectWrite, dw_lasterr_pk)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t val = 10;
    SetVertexProperty(stmt, val);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // 通过直连写再次插入数据，primary key相同，出现冲突
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    SetVertexProperty(stmt, val);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
    const char *lastErrInfo1 = GmcGetLastError();
    char buf[512] = {0};
    ret = strcpy_s(buf, sizeof(buf), lastErrInfo1);
    // 通过CS写再次插入数据，primary key相同，出现冲突
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    SetVertexProperty(stmt, val);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
    const char *lastErrInfo2 = GmcGetLastError();
    // 比较直连写和CS写在主键冲突场景下的lasterr信息是否一致
    EXPECT_EQ(0, strcmp(buf, lastErrInfo2));

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 场景2. 权限不足。位于 client_st_directwrite_priv.cc

// 场景3. 更新LPM索引。详见用例 StClientDirectWrite.dw_update_vertex_lpm
/********************************/

#define TEST_RECORD_NUMS 1000
void *DoInsert(void *args)
{
    GmcStmtT *stmt = (GmcStmtT *)args;
    Status ret;
    uint32_t begin = 0, end = TEST_RECORD_NUMS;
    for (uint32_t val = begin; val < end; val++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        SetVertexProperty(stmt, val);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK || ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_UNIQUE_VIOLATION) {
            continue;
        } else {
            printf("[insert] ret: %d\n", ret);
            break;
        }
    }
    int32_t *thread_ret = (int32_t *)malloc(sizeof(int32_t));
    *thread_ret = ret;
    pthread_exit(thread_ret);
}

void *DoUpdate(void *args)
{
    GmcStmtT *stmt = (GmcStmtT *)args;
    Status ret;
    int32_t begin = 0, end = TEST_RECORD_NUMS;
    for (int32_t pkVal = begin; pkVal < end; pkVal++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkVal, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t f1 = 100;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t f2 = 200;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2, sizeof(int32_t));
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK || ret == GMERR_UNIQUE_VIOLATION) {
            continue;
        } else {
            printf("[update] ret: %d\n", ret);
            break;
        }
    }
    int32_t *thread_ret = (int32_t *)malloc(sizeof(int32_t));
    *thread_ret = ret;
    pthread_exit(thread_ret);
}

void *DoReplace(void *args)
{
    GmcStmtT *stmt = (GmcStmtT *)args;
    Status ret;
    uint32_t begin = 0, end = TEST_RECORD_NUMS;
    for (uint32_t val = begin; val < end; val++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        SetVertexProperty(stmt, val + 1);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            printf("[replace] ret: %d\n", ret);
            break;
        }
    }
    int32_t *thread_ret = (int32_t *)malloc(sizeof(int32_t));
    *thread_ret = ret;
    pthread_exit(thread_ret);
}

void *DoDelete(void *args)
{
    GmcStmtT *stmt = (GmcStmtT *)args;
    Status ret;
    int32_t begin = 0, end = TEST_RECORD_NUMS;
    for (int32_t pkVal = begin; pkVal < end; pkVal++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkVal, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            printf("[delete] ret: %d\n", ret);
            break;
        }
    }
    // The value pointed to by retval should not be located on the calling thread's stack,
    // since the contents of that stack are undefined after the thread terminates.
    int32_t *thread_ret = (int32_t *)malloc(sizeof(int32_t));
    *thread_ret = ret;
    pthread_exit(thread_ret);
}

void *DoInsertCS(void *args)
{
    GmcStmtT *stmt = (GmcStmtT *)args;
    Status ret;
    uint32_t begin = 0, end = TEST_RECORD_NUMS;
    for (uint32_t val = begin; val < end; val++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        SetVertexProperty(stmt, val);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK || ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_UNIQUE_VIOLATION) {
            continue;
        } else {
            printf("[insert] ret: %d\n", ret);
            break;
        }
    }
    int32_t *thread_ret = (int32_t *)malloc(sizeof(int32_t));
    *thread_ret = ret;
    pthread_exit(thread_ret);
}

void *DoUpdateCS(void *args)
{
    GmcStmtT *stmt = (GmcStmtT *)args;
    Status ret;
    int32_t begin = 0, end = TEST_RECORD_NUMS;
    for (int32_t pkVal = begin; pkVal < end; pkVal++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkVal, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t f1 = 100;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t f2 = 200;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2, sizeof(int32_t));
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OK || ret == GMERR_UNIQUE_VIOLATION) {
            continue;
        } else {
            printf("[update] ret: %d\n", ret);
            break;
        }
    }
    int32_t *thread_ret = (int32_t *)malloc(sizeof(int32_t));
    *thread_ret = ret;
    pthread_exit(thread_ret);
}

void *DoReplaceCS(void *args)
{
    GmcStmtT *stmt = (GmcStmtT *)args;
    Status ret;
    uint32_t begin = 0, end = TEST_RECORD_NUMS;
    for (uint32_t val = begin; val < end; val++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        SetVertexProperty(stmt, val + 1);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            printf("[replace] ret: %d\n", ret);
            break;
        }
    }
    int32_t *thread_ret = (int32_t *)malloc(sizeof(int32_t));
    *thread_ret = ret;
    pthread_exit(thread_ret);
}

void *DoDeleteCS(void *args)
{
    GmcStmtT *stmt = (GmcStmtT *)args;
    Status ret;
    int32_t begin = 0, end = TEST_RECORD_NUMS;
    for (int32_t pkVal = begin; pkVal < end; pkVal++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkVal, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            printf("[delete] ret: %d\n", ret);
            break;
        }
    }
    // The value pointed to by retval should not be located on the calling thread's stack,
    // since the contents of that stack are undefined after the thread terminates.
    int32_t *thread_ret = (int32_t *)malloc(sizeof(int32_t));
    *thread_ret = ret;
    pthread_exit(thread_ret);
}

// 验证场景：C/S接口和直连写接口并发场景
TEST_F(StClientDirectWrite, dw_multi_thread_dml)
{
    Status ret = GMERR_OK;
    uint32_t opsNum = 4;              // insert,update,replace,delete
    uint32_t threadNum = opsNum * 2;  // do every dml operation in both CS and DW conn

    GmcConnT *csConn[opsNum] = {NULL}, *dwConn[opsNum] = {NULL};
    GmcStmtT *csStmt[opsNum] = {NULL}, *dwStmt[opsNum] = {NULL};
    for (uint32_t i = 0; i < opsNum; i++) {
        CreateSyncConnectionAndStmt(&csConn[i], &csStmt[i]);
        CreateSyncConnectionAndStmt(&dwConn[i], &dwStmt[i]);
    }
    ret = GmcCreateVertexLabel(csStmt[0], g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t *thread_ret;
    pthread_t tid[threadNum];
    pthread_create(&tid[0], NULL, DoInsertCS, csStmt[0]);
    pthread_create(&tid[1], NULL, DoInsert, dwStmt[0]);
    pthread_create(&tid[2], NULL, DoUpdateCS, csStmt[1]);
    pthread_create(&tid[3], NULL, DoUpdate, dwStmt[1]);
    pthread_create(&tid[4], NULL, DoReplaceCS, csStmt[2]);
    pthread_create(&tid[5], NULL, DoReplace, dwStmt[2]);
    pthread_create(&tid[6], NULL, DoDeleteCS, csStmt[3]);
    pthread_create(&tid[7], NULL, DoDelete, dwStmt[3]);

    for (uint32_t i = 0; i < threadNum; i++) {
        pthread_join(tid[i], (void **)&thread_ret);
        printf("[thread %u] thread ret: %d\n", i, *thread_ret);
    }

    ret = GmcDropVertexLabel(csStmt[0], g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < opsNum; i++) {
        DestroyConnectionAndStmt(csConn[i], csStmt[i]);
        DestroyConnectionAndStmt(dwConn[i], dwStmt[i]);
    }
}

/****************聚簇容器多线程用例***************/
class StClientDirectWriteCh : public StClient {
public:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig("\"enableDmlOperStat=1\" \"enableDmlPerfStat=1\" \"enableClusterHash=1\" "
                                "\"trxMonitorEnable=0\" \"workerHungThreshold=6,200,300\" "
                                "\"maxSysDynSize=1024\" \"auditLogEnableDML=0\" "
                                "\"userPolicyMode=0\" \"compatibleV3=0\" \"directWrite=1\" ");
        st_clt_init();
        CreateAndStartEpoll(&responseEpollThreadId, &responseEpollFd);
        CreateAndStartEpoll(&timeoutEpollThreadId, &timeoutEpollFd);
        EXPECT_EQ(GMERR_OK, GmcRegTimeoutEpollFunc(TimeoutEpollReg));
        // 此处心跳定时器与TimeOut定时器使用同一个Epoll，可设置为不同Epoll
        EXPECT_EQ(GMERR_OK, GmcRegHeartBeatEpollFunc(TimeoutEpollReg));
        if (IsEulerEnv()) {
            DbSleep(1000);
        } else {
            st_check_hpe_server_running();
        }
        printf("start response epoll and timeout epoll thread\n");
        printf("response epoll fd: %d, timeout epoll fd: %d\n", responseEpollFd, timeoutEpollFd);
        st_connect();
    }
};

TEST_F(StClientDirectWriteCh, dw_ch_multi_thread_dml)
{
    Status ret = GMERR_OK;
    uint32_t opsNum = 4;              // insert,update,replace,delete
    uint32_t threadNum = opsNum * 2;  // do every dml operation in both CS and DW conn

    GmcConnT *csConn[opsNum] = {NULL}, *dwConn[opsNum] = {NULL};
    GmcStmtT *csStmt[opsNum] = {NULL}, *dwStmt[opsNum] = {NULL};
    for (uint32_t i = 0; i < opsNum; i++) {
        CreateSyncConnectionAndStmt(&csConn[i], &csStmt[i]);
        CreateSyncConnectionAndStmt(&dwConn[i], &dwStmt[i]);
    }
    ret = GmcCreateVertexLabel(csStmt[0], g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t *thread_ret;
    pthread_t tid[threadNum];
    pthread_create(&tid[0], NULL, DoInsert, csStmt[0]);
    pthread_create(&tid[1], NULL, DoInsert, dwStmt[0]);
    pthread_create(&tid[2], NULL, DoUpdate, csStmt[1]);
    pthread_create(&tid[3], NULL, DoUpdate, dwStmt[1]);
    pthread_create(&tid[4], NULL, DoReplace, csStmt[2]);
    pthread_create(&tid[5], NULL, DoReplace, dwStmt[2]);
    pthread_create(&tid[6], NULL, DoDelete, csStmt[3]);
    pthread_create(&tid[7], NULL, DoDelete, dwStmt[3]);

    for (uint32_t i = 0; i < threadNum; i++) {
        pthread_join(tid[i], (void **)&thread_ret);
        printf("[thread %u] thread ret: %d\n", i, *thread_ret);
    }

    ret = GmcDropVertexLabel(csStmt[0], g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < opsNum; i++) {
        DestroyConnectionAndStmt(csConn[i], csStmt[i]);
        DestroyConnectionAndStmt(dwConn[i], dwStmt[i]);
    }
}

// 一个conn下包含多个stmt
TEST_F(StClientDirectWriteCh, dw_same_conn_multi_stmt)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt1 = NULL, *stmt2 = NULL;
    const char *userName = "XXuser";
#if (defined RTOSV2 || defined RTOSV2X)
    const char *serverLocator = "channel:";
#else
    const char *serverLocator = "usocket:/run/verona/unix_emserver";
#endif
    Status ret;

    ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt1, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    testMultiOpsInSameStmt(stmt1);
    testMultiOpsInSameStmt(stmt2);

    ret = GmcDropVertexLabel(stmt1, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt1);
    GmcFreeStmt(stmt2);
    ASSERT_EQ(GMERR_OK, GmcDisconnect(conn));
}

TEST_F(StClientDirectWrite, dw_wrong_versionID)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据，指定错误版本号，预期报错（非直连写流程）
    uint32_t versionId = 3;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_label_name, versionId, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 测试用例起server的时候直接改了gmserver.ini里的配置项
TEST_F(StClientDirectWrite, dw_client_cfg_change_and_read)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, CheckCltCfgValue(conn->cfgShmConfig, false));
    EXPECT_EQ(GMERR_OK, CheckCltCfgValue(stmt->conn->cfgShmConfig, false));
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    // 首次执行replace，表中无数据，实际表现为insert操作
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    // 校验客户端可读配置项值是否正确
    EXPECT_EQ(GMERR_OK, CheckCltCfgValue(stmt->dwRunCtx->cfgShmConfig, false));
    uint32_t auditLog = 1;
    uint32_t perfStat = 0;
    uint32_t operStat = 0;
    uint32_t V3 = 1;
    GmcSetCfg(stmt, "auditLogEnableDML", GMC_DATATYPE_INT32, &auditLog, sizeof(uint32_t));
    EXPECT_NE(GMERR_OK, GmcSetCfg(stmt, "enableDmlOperStat", GMC_DATATYPE_INT32, &operStat, sizeof(uint32_t)));
    GmcSetCfg(stmt, "enableDmlPerfStat", GMC_DATATYPE_INT32, &perfStat, sizeof(uint32_t));
    EXPECT_NE(GMERR_OK, GmcSetCfg(stmt, "compatibleV3", GMC_DATATYPE_INT32, &V3, sizeof(uint32_t)));
    EXPECT_EQ(GMERR_OK, CheckCltCfgValue(conn->cfgShmConfig, true));
    EXPECT_EQ(GMERR_OK, CheckCltCfgValue(stmt->conn->cfgShmConfig, true));
    EXPECT_EQ(GMERR_OK, CheckCltCfgValue(stmt->dwRunCtx->cfgShmConfig, true));
    auditLog = 0;
    perfStat = 1;
    operStat = 1;
    V3 = 0;
    GmcSetCfg(stmt, "auditLogEnableDML", GMC_DATATYPE_INT32, &auditLog, sizeof(uint32_t));
    EXPECT_NE(GMERR_OK, GmcSetCfg(stmt, "enableDmlOperStat", GMC_DATATYPE_INT32, &operStat, sizeof(uint32_t)));
    GmcSetCfg(stmt, "enableDmlPerfStat", GMC_DATATYPE_INT32, &perfStat, sizeof(uint32_t));
    EXPECT_NE(GMERR_OK, GmcSetCfg(stmt, "compatibleV3", GMC_DATATYPE_INT32, &V3, sizeof(uint32_t)));
    EXPECT_EQ(GMERR_OK, CheckCltCfgValue(conn->cfgShmConfig, false));
    EXPECT_EQ(GMERR_OK, CheckCltCfgValue(stmt->conn->cfgShmConfig, false));
    EXPECT_EQ(GMERR_OK, CheckCltCfgValue(stmt->dwRunCtx->cfgShmConfig, false));
}

// 开启按需建表开关，测试基本直连写场景
TEST_F(StClientDirectWrite, dw_replace_vertex_basic_create_table_on_demand)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    int affectRows = 0;
    ExpectedArgsT args;
    GmcDropVertexLabel(stmt, "T39");
    // 首次执行replace，表中无数据，实际表现为insert操作
    for (uint32_t i = 1; i <= 10; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, "T39", GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t val = i;
        SetVertexProperty(stmt, val);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);

        args = {};
        SetExpectedArgs(&args, val);       // 设置预期值
        VerifyVertexProperty(stmt, args);  // 查询验证
    }

    // 第二次执行replace，表中存在数据，实际表现为全量替换
    ret = GmcPrepareStmtByLabelName(stmt, "T39", GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t val = 11;
    SetVertexProperty(stmt, val);
    int32_t f0 = 10;  // 主键的值确保和原来的保持一致
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &f0, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2, affectRows);  // 记录存在，replace的逻辑是先删除再插入，affectRows会统计删除的行，即为2

    args = {.f0 = 10, .f1 = 11, .f2 = 11, .f3 = 11, .vrId = 11, .vrfIndex = 11, .ipAddr = 11, .maskLen = 11};
    VerifyVertexProperty(stmt, args);

    ret = GmcDropVertexLabel(stmt, "T39");
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

static void DwReleaseLatchForVertexLabelStub(
    LabelRWLatchT *labelRWLatch, DbSessionCtxT *sessionCtx, ConcurrencyControlE ccType)
{
    DB_POINTER2(labelRWLatch, sessionCtx);
    DB_ASSERT(ccType == CONCURRENCY_CONTROL_READ_UNCOMMIT || ccType == CONCURRENCY_CONTROL_LABEL_LATCH);
    if (ccType == CONCURRENCY_CONTROL_READ_UNCOMMIT) {
        if (labelRWLatch->isAddHcLatch) {
            LabelWHcLatchReleaseWithSession(labelRWLatch, sessionCtx);
        }
        LabelRLatchReleaseWithSession(labelRWLatch, sessionCtx);
        LabelLatchAtomicClearRuMode(labelRWLatch);
        return;
    }
    if (ccType == CONCURRENCY_CONTROL_LABEL_LATCH) {
        LabelWLatchReleaseWithSession(labelRWLatch, sessionCtx);
        return;
    }
    DB_LOG_ERROR(
        GMERR_FEATURE_NOT_SUPPORTED, "Concurrency type %" PRId32 " is not supported by direct write", (int32_t)ccType);
}

static std::mutex mtx;
pthread_t t;

static Status DwEndTrxStub(GmcStmtT *stmt, DwRunCtxT *dwRunCtx, Status opStatus, ConcurrencyControlE ccType)
{
    mtx.unlock();
    pthread_join(t, NULL);
    DB_POINTER(dwRunCtx);
    // 只实现autoCommit && isLiteTrx (RU)
    Status ret = opStatus;
    SeRunCtxHdT seRunCtx = (SeRunCtxHdT)dwRunCtx->base.seRunCtxHandle;
    LabelRWLatchT *labelRWLatch = (LabelRWLatchT *)dwRunCtx->base.labelLatch;
    DbSessionCtxT *sessionCtx = dwRunCtx->base.sessionCtx;
    if (SECUREC_UNLIKELY(dwRunCtx->stmgSubData.isStMgLabel)) {
        DwSetTrxCommitCallBack(seRunCtx, dwRunCtx);
    }

    if (SECUREC_LIKELY(opStatus == GMERR_OK)) {
        if (SECUREC_UNLIKELY((ret = SeTransCommit(seRunCtx)) != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Unable to commit transaction in directwrite.");
            DwReleaseStMgSubNode(&dwRunCtx->stmgSubData);
            DwResetDwRunCtx4Stmt(dwRunCtx, stmt->memCtx);
            DwReleaseLatchForVertexLabelStub(labelRWLatch, sessionCtx, ccType);
            DwReleaseLatchForResPool(dwRunCtx, sessionCtx);
            DwCloseRes(dwRunCtx);
            return ret;
        }

        // 对于状态合并表，无论是否有订阅关系，都需要维护推送消息，所以没必要判断subscriptionNum
        if (SECUREC_UNLIKELY(dwRunCtx->stmgSubData.isStMgLabel)) {
            DwPushStMgDataAfterCommit(&dwRunCtx->stmgSubData);
        }
    } else {
        Status retTmp = SeTransRollback(seRunCtx, false);
        if (SECUREC_UNLIKELY(retTmp != GMERR_OK)) {
            DB_LOG_ERROR(retTmp, "Unable to rollback transaction in directwrite.");
        }
        DwReleaseStMgSubNode(&dwRunCtx->stmgSubData);
        DwResetResInfo(stmt);
    }
    DwResetDwRunCtx4Stmt(dwRunCtx, stmt->memCtx);
    DwReleaseLatchForVertexLabelStub(labelRWLatch, sessionCtx, ccType);
    DwReleaseLatchForResPool(dwRunCtx, sessionCtx);
    DwCloseRes(dwRunCtx);
    return ret;
}

void *sysviewFunc(void *args)
{
    (void)args;
    mtx.lock();
    system("gmsysview -q V\\$STORAGE_UNDO_STAT 1>&2 > /dev/null");
    mtx.unlock();
    return nullptr;
}

// 直连写事务未提交时，查询STORAGE_UNDO_STAT
TEST_F(StClientDirectWrite, dw_query_trx_sysview_before_commit)
{
    init();
    (void)setStubC((void *)DwEndTrx, (void *)DwEndTrxStub);
    mtx.lock();
    ASSERT_EQ(pthread_create(&t, nullptr, sysviewFunc, nullptr), 0);
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows = 0;
    // insert
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t val = 1;
    SetVertexProperty(stmt, val);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
    clearAllStub();
}
