/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: st for multi version of direct write dml operations
 * Author: dongjiepeng
 * Create: 2023-05-10
 */

#include <string.h>
#include <math.h>
#include "client_common_st.h"

static DbMemCtxT *alterMemCtx = NULL;

class StClientDWMultiVersion : public StClient {
public:
    static void SetUpTestCase();

    StClientDWMultiVersion()
    {
        DbMemCtxArgsT args = {0};
        // client st 会初始化g_gmdbCltInstance.cltCommCtx，这里直接使用
        alterMemCtx = DbCreateDynMemCtx(g_gmdbCltInstance.cltCommCtx, false, "StClientDWMultiVersion st", &args);
    }

    ~StClientDWMultiVersion() override
    {
        // 统一回收中间申请的内存
        DbDeleteDynMemCtx(alterMemCtx);
    }
};

void StClientDWMultiVersion::SetUpTestCase()
{
    StartDbServerWithConfig("\"enableClusterHash=0\" \"trxMonitorEnable=0\" \"workerHungThreshold=6,200,300\" "
                            "\"maxSysDynSize=1024\" \"auditLogEnableDML=0\" "
                            "\"userPolicyMode=0\" \"compatibleV3=0\" \"directWrite=1\" ");
    st_clt_init();
    GmcSignalRegisterNotify();
    CreateAndStartEpoll(&responseEpollThreadId, &responseEpollFd);
    CreateAndStartEpoll(&timeoutEpollThreadId, &timeoutEpollFd);
    EXPECT_EQ(GMERR_OK, GmcRegTimeoutEpollFunc(TimeoutEpollReg));
    // 此处心跳定时器与TimeOut定时器使用同一个Epoll，可设置为不同Epoll
    EXPECT_EQ(GMERR_OK, GmcRegHeartBeatEpollFunc(TimeoutEpollReg));
    if (IsEulerEnv()) {
        DbSleep(1000);
    } else {
        st_check_hpe_server_running();
    }
    printf("start response epoll and timeout epoll thread\n");
    printf("response epoll fd: %d, timeout epoll fd: %d\n", responseEpollFd, timeoutEpollFd);
    st_connect();
}

static const char *g_label_config = R"({"max_record_count":100000})";

typedef struct LabelOpCfg {
    uint32_t start;
    uint32_t end;
    GmcOperationTypeE optType;
    uint32_t deltaData = 0;
    uint32_t versionId;
    Status expExecuteStatus;
    uint32_t expAffectRows = 0;
    bool isSecIdxUpdate;
} LabelOpCfgT;

void WaitingDegradeEnd(GmcStmtT *stmt, const char *labelName)
{
    Status ret;
    uint32_t degradeProcess;
    while ((ret = GmcGetVertexLabelDegradeProgress(stmt, labelName, &degradeProcess)) == GMERR_OK) {
    };
    EXPECT_EQ(GMERR_NO_DATA, ret);
}

static Status TestGetAffectedRows(GmcStmtT *stmt, int32_t expectValue)
{
    int32_t affectRows = 0;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expectValue, affectRows);
    return expectValue == affectRows ? GMERR_OK : 1;
}

static void SimpleLabelWriteData(GmcStmtT *stmt, const char *vertexLabelName, LabelOpCfgT *cfg)
{
    uint32_t start = cfg->start;
    uint32_t end = cfg->end;
    uint32_t versionId = cfg->versionId;
    for (uint32_t i = start; i <= end; i++) {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, versionId, cfg->optType));
        int64_t f0Value = (int64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t)));
        uint64_t f1Value = (uint64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t)));
        int32_t f2Value = (int32_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t)));
        if (versionId == 2) {
            uint32_t f3Value = (uint32_t)i;
            EXPECT_EQ(
                GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t)));
        }
        if (versionId == 3) {
            uint32_t f3Value = (uint32_t)i;
            EXPECT_EQ(
                GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t)));
            int16_t f4Value = (int16_t)i;
            EXPECT_EQ(
                GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t)));
        }
        EXPECT_EQ(cfg->expExecuteStatus, GmcExecute(stmt));
        EXPECT_EQ(GMERR_OK, TestGetAffectedRows(stmt, cfg->expAffectRows));
    }
}

static void SimpleLabelUpdateData(GmcStmtT *stmt, const char *vertexLabelName, LabelOpCfgT *cfg)
{
    uint32_t start = cfg->start;
    uint32_t end = cfg->end;
    uint32_t versionId = cfg->versionId;
    for (uint32_t i = start; i <= end; i++) {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 0xFFFFFFFF, cfg->optType));
        int64_t keyValue = (int64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &keyValue, sizeof(int64_t)));
        uint64_t f1Value = (uint64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t)));
        int32_t f2Value = (int32_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t)));
        if (versionId == 2) {
            uint32_t f3Value = (uint32_t)i;
            EXPECT_EQ(
                GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t)));
        }
        if (versionId == 3) {
            uint32_t f3Value = (uint32_t)i;
            EXPECT_EQ(
                GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t)));
            int16_t f4Value = (int16_t)i;
            EXPECT_EQ(
                GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t)));
        }
        EXPECT_EQ(cfg->expExecuteStatus, GmcExecute(stmt));
        EXPECT_EQ(GMERR_OK, TestGetAffectedRows(stmt, cfg->expAffectRows));
    }
    return;
}

static void SimpleLabelDeleteData(GmcStmtT *stmt, const char *vertexLabelName, LabelOpCfgT *cfg)
{
    uint32_t start = cfg->start;
    uint32_t end = cfg->end;
    for (uint32_t i = start; i <= end; i++) {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 0xFFFFFFFF, cfg->optType));
        int64_t keyValue = (int64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &keyValue, sizeof(int64_t)));
        EXPECT_EQ(cfg->expExecuteStatus, GmcExecute(stmt));
        EXPECT_EQ(GMERR_OK, TestGetAffectedRows(stmt, cfg->expAffectRows));
    }
    return;
}

static void SimpleLableReadAndCompareCommonData(GmcStmtT *stmt, const char *vertexLabelName, LabelOpCfgT *cfg)
{
    uint32_t start = cfg->start;
    uint32_t end = cfg->end;
    uint32_t versionId = cfg->versionId;
    bool isFinish = false;
    for (uint32_t i = start; i <= end; i++) {
        Status ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, versionId, cfg->optType);
        EXPECT_EQ(cfg->expExecuteStatus, ret);
        if (ret != GMERR_OK) {
            continue;
        }
        int64_t keyValue = (int64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &keyValue, sizeof(int64_t)));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));
        bool isNull = true;
        int64_t f0Value = (int64_t)i;
        int64_t f0GetValue;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, (char *)"F0", &f0GetValue, sizeof(int64_t), &isNull));
        EXPECT_FALSE(isNull);
        EXPECT_EQ(f0Value, f0GetValue);

        int64_t f1Value = (int64_t)i;
        int64_t f1GetValue;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, (char *)"F1", &f1GetValue, sizeof(int64_t), &isNull));
        EXPECT_FALSE(isNull);
        EXPECT_EQ(f1Value, f1GetValue);

        int32_t f2Value = (int32_t)i;
        int32_t f2GetValue;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, (char *)"F2", &f2GetValue, sizeof(int32_t), &isNull));
        EXPECT_FALSE(isNull);
        EXPECT_EQ(f2Value, f2GetValue);
    }
}

void SimpleLabelDmlExecute(GmcStmtT *stmt, const char *vertexLabelName, LabelOpCfgT *cfg)
{
    switch (cfg->optType) {
        case GMC_OPERATION_INSERT:
        case GMC_OPERATION_REPLACE:
            SimpleLabelWriteData(stmt, vertexLabelName, cfg);
            break;
        case GMC_OPERATION_UPDATE:
            SimpleLabelUpdateData(stmt, vertexLabelName, cfg);
            break;
        case GMC_OPERATION_DELETE:
            SimpleLabelDeleteData(stmt, vertexLabelName, cfg);
            break;
        case GMC_OPERATION_SCAN:
            SimpleLableReadAndCompareCommonData(stmt, vertexLabelName, cfg);
            break;
        default:
            break;
    }
}

TEST_F(StClientDWMultiVersion, nullableConstrain)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");
    string schemaV3_wrong = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v3_wrong.gmjson");
    string schemaV3_default_value =
        GetFileContext("011_direct_write/st_data/simple_vertexlabel_v3_default_value.gmjson");

    // 建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), g_label_config));

    // 版本1，插入1-10
    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg1);

    // 用v3版错误schema升级，nullable=false且没有默认值，升级失败
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE,
        GmcAlterVertexLabelWithName(stmt, schemaV3_wrong.c_str(), true, vertexLabelName));

    // 用v3版正确schema升级，nullable=false且有默认值，升级成功
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV3_default_value.c_str(), true, vertexLabelName));

    // 版本3，插入11-20
    LabelOpCfgT cfg2;
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_INSERT;
    cfg2.versionId = 3;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg2);

    // 用版本1查数据成功
    LabelOpCfgT cfg3;
    cfg3.start = 1;
    cfg3.end = 20;
    cfg3.optType = GMC_OPERATION_SCAN;
    cfg3.versionId = 1;
    cfg3.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg3);

    // 版本3查数据
    for (uint32_t i = 1; i <= 20; i++) {
        bool isFinish;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 3, GMC_OPERATION_SCAN));

        int64_t keyValue = (int64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &keyValue, sizeof(int64_t)));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));
        bool isNull = true;
        int64_t f0Value = (int64_t)i;
        int64_t f0GetValue;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, (char *)"F0", &f0GetValue, sizeof(int64_t), &isNull));
        EXPECT_FALSE(isNull);
        EXPECT_EQ(f0Value, f0GetValue);

        int64_t f1Value = (int64_t)i;
        int64_t f1GetValue;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, (char *)"F1", &f1GetValue, sizeof(int64_t), &isNull));
        EXPECT_FALSE(isNull);
        EXPECT_EQ(f1Value, f1GetValue);

        int32_t f2Value = (int32_t)i;
        int32_t f2GetValue;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, (char *)"F2", &f2GetValue, sizeof(int32_t), &isNull));
        EXPECT_FALSE(isNull);
        EXPECT_EQ(f2Value, f2GetValue);

        if (i <= 10) {
            // f3默认为null
            int32_t f3GetValue;
            EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, (char *)"F3", &f3GetValue, sizeof(int32_t), &isNull));
            EXPECT_TRUE(isNull);
            // 123456是默认值
            int16_t f4Value = (int16_t)12345;
            int16_t f4GetValue;
            EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, (char *)"F4", &f4GetValue, sizeof(int16_t), &isNull));
            EXPECT_FALSE(isNull);
            EXPECT_EQ(f4Value, f4GetValue);
        } else {
            int32_t f3Value = (int32_t)i;
            int32_t f3GetValue;
            EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, (char *)"F3", &f3GetValue, sizeof(int32_t), &isNull));
            EXPECT_FALSE(isNull);
            EXPECT_EQ(f3Value, f3GetValue);

            int16_t f4Value = (int16_t)i;
            int16_t f4GetValue;
            EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, (char *)"F4", &f4GetValue, sizeof(int16_t), &isNull));
            EXPECT_FALSE(isNull);
            EXPECT_EQ(f4Value, f4GetValue);
        }
    }

    GmcDropVertexLabel(stmt, vertexLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDWMultiVersion, DwInvalidVersion)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");
    string schemaV3 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v3.gmjson");

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), g_label_config));
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, vertexLabelName));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 0xFFFFFFFF, GMC_OPERATION_INSERT));

    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 1));
    WaitingDegradeEnd(stmt, vertexLabelName);

    uint32_t i = 100;
    int64_t f0Value = (int64_t)i;
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t)));
    uint64_t f1Value = (uint64_t)i;
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t)));
    int32_t f2Value = (int32_t)i;
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t)));
    uint32_t f3Value = (uint32_t)i;
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t)));
    int16_t f4Value = (int16_t)i;
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t)));

    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcExecute(stmt));

    // 删除表
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, vertexLabelName));
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDWMultiVersion, DwInvalidUuid)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");
    string schemaV3 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v3.gmjson");
    string schemaV3_F5 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v3_F5.gmjson");

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), g_label_config));
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, vertexLabelName));

    // 版本1，插入1-10
    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 3;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg1);

    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 1));
    WaitingDegradeEnd(stmt, vertexLabelName);

    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV3_F5.c_str(), true, vertexLabelName));

    uint32_t i = 100;
    int64_t f0Value = (int64_t)i;
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t)));
    uint64_t f1Value = (uint64_t)i;
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t)));
    int32_t f2Value = (int32_t)i;
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t)));
    uint32_t f3Value = (uint32_t)i;
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t)));
    int16_t f4Value = (int16_t)i;
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t)));

    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcExecute(stmt));

    // 删除表
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, vertexLabelName));
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDWMultiVersion, DwInsertSimpleVertexLabel)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");
    string schemaV2 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v2.gmjson");
    string schemaV3 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v3.gmjson");

    // 建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), g_label_config));

    // 版本1，插入1-10
    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg1);

    // 升级 v1->v2
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV2.c_str(), true, vertexLabelName));
    // 插入 11-20 成功
    LabelOpCfgT cfg2;
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_INSERT;
    cfg2.versionId = 1;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    // 再次插入 11-20 失败
    cfg2.start = 1;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_INSERT;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_PRIMARY_KEY_VIOLATION;
    cfg2.expAffectRows = 0;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg2);

    // 升级v2->v3
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, vertexLabelName));
    // 插入 21-30
    LabelOpCfgT cfg3;
    cfg3.start = 21;
    cfg3.end = 30;
    cfg3.optType = GMC_OPERATION_INSERT;
    cfg3.versionId = 3;
    cfg3.expExecuteStatus = GMERR_OK;
    cfg3.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg3);

    // 用版本3查数据成功
    LabelOpCfgT cfg4;
    cfg4.start = 1;
    cfg4.end = 30;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 降级v3->v2
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 2));
    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 30;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 版本2，插入31-40成功
    LabelOpCfgT cfg5;
    cfg5.start = 31;
    cfg5.end = 40;
    cfg5.optType = GMC_OPERATION_INSERT;
    cfg5.versionId = 2;
    cfg5.expExecuteStatus = GMERR_OK;
    cfg5.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg5);

    // 降级v2->v1
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 1));

    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 40;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据失败
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    GmcDropVertexLabel(stmt, vertexLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDWMultiVersion, DwReplaceSimpleVertexLabel)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");
    string schemaV2 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v2.gmjson");
    string schemaV3 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v3.gmjson");

    // 建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), g_label_config));

    // 版本1，插入1-10
    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg1);
    // 版本1，replace 1-10成功
    cfg1.optType = GMC_OPERATION_REPLACE;
    cfg1.expAffectRows = 2;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg1);

    // 升级 v1->v2
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV2.c_str(), true, vertexLabelName));
    // 插入 11-20 成功
    LabelOpCfgT cfg2;
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_REPLACE;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg2);

    // 升级v2->v3
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, vertexLabelName));
    // 插入 21-30
    LabelOpCfgT cfg3;
    cfg3.start = 21;
    cfg3.end = 30;
    cfg3.optType = GMC_OPERATION_REPLACE;
    cfg3.versionId = 3;
    cfg3.expExecuteStatus = GMERR_OK;
    cfg3.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg3);

    // 用版本3查数据成功
    LabelOpCfgT cfg4;
    cfg4.start = 1;
    cfg4.end = 30;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 降级v3->v2
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 2));
    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 30;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 版本2，插入31-40成功
    LabelOpCfgT cfg5;
    cfg5.start = 31;
    cfg5.end = 40;
    cfg5.optType = GMC_OPERATION_REPLACE;
    cfg5.versionId = 2;
    cfg5.expExecuteStatus = GMERR_OK;
    cfg5.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg5);

    // 降级v2->v1
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 1));

    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 40;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据失败
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    GmcDropVertexLabel(stmt, vertexLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDWMultiVersion, DwUpdateSimpleVertexLabel)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");
    string schemaV2 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v2.gmjson");
    string schemaV3 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v3.gmjson");

    // 建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), g_label_config));

    // 版本1，插入1-10
    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg1);
    // 版本1，update 1-10成功
    cfg1.optType = GMC_OPERATION_UPDATE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg1);

    // 升级 v1->v2
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV2.c_str(), true, vertexLabelName));
    // 插入 11-20 成功
    LabelOpCfgT cfg2;
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_INSERT;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    // 版本2，update 11-20成功
    cfg2.optType = GMC_OPERATION_UPDATE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    // 升级v2->v3
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, vertexLabelName));
    // update 21-30 失败
    LabelOpCfgT cfg3;
    cfg3.start = 21;
    cfg3.end = 30;
    cfg3.optType = GMC_OPERATION_UPDATE;
    cfg3.versionId = 3;
    // execute返回GMERR_OK，但是affectRows为0
    cfg3.expExecuteStatus = GMERR_OK;
    cfg3.expAffectRows = 0;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg3);

    // 用版本3查数据成功
    LabelOpCfgT cfg4;
    cfg4.start = 1;
    cfg4.end = 20;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 降级v3->v2
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 2));

    // update 11-20 成功
    LabelOpCfgT cfg5;
    cfg5.start = 11;
    cfg5.end = 20;
    cfg5.optType = GMC_OPERATION_UPDATE;
    cfg5.versionId = 2;
    cfg5.expExecuteStatus = GMERR_OK;
    cfg5.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg5);

    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 20;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 降级v2->v1
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 1));

    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 20;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据失败
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    GmcDropVertexLabel(stmt, vertexLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDWMultiVersion, DwDeleteSimpleVertexLabel)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");
    string schemaV2 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v2.gmjson");
    string schemaV3 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v3.gmjson");

    // 建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), g_label_config));

    // 版本1，插入1-10
    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg1);
    // 版本1，delete 6-10成功
    cfg1.start = 6;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_DELETE;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg1);

    // 升级 v1->v2
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV2.c_str(), true, vertexLabelName));
    // 插入 11-20 成功
    LabelOpCfgT cfg2;
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_INSERT;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    // 版本2，delete 11-20成功
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_DELETE;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    cfg2.expAffectRows = 0;

    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    // 升级v2->v3
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, vertexLabelName));

    // 版本3，插入 11-20 成功
    LabelOpCfgT cfg3;
    cfg3.start = 11;
    cfg3.end = 20;
    cfg3.optType = GMC_OPERATION_INSERT;
    cfg3.versionId = 2;
    cfg3.expExecuteStatus = GMERR_OK;
    cfg3.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg3);
    // 版本3，delete 11-20成功
    cfg3.start = 11;
    cfg3.end = 20;
    cfg3.optType = GMC_OPERATION_DELETE;
    cfg3.versionId = 2;
    cfg3.expExecuteStatus = GMERR_OK;
    cfg3.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg3);

    // 用版本3查数据成功
    LabelOpCfgT cfg4;
    cfg4.start = 1;
    cfg4.end = 5;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 降级v3->v2
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 2));

    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 5;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // delete 1-2 成功
    LabelOpCfgT cfg5;
    cfg5.start = 1;
    cfg5.end = 2;
    cfg5.optType = GMC_OPERATION_DELETE;
    cfg5.versionId = 2;
    cfg5.expExecuteStatus = GMERR_OK;
    cfg5.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg5);

    // 降级v2->v1
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 1));

    // 用版本3查数据失败
    cfg4.start = 3;
    cfg4.end = 5;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据失败
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    GmcDropVertexLabel(stmt, vertexLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

struct ConcurrencyPara {
    GmcStmtT *stmt;
    const char *vertexLabelName;
    LabelOpCfgT *cfg = NULL;
};

static void *DoInsert(void *args)
{
    GmcStmtT *stmt = ((ConcurrencyPara *)args)->stmt;
    const char *vertexLabelName = ((ConcurrencyPara *)args)->vertexLabelName;
    LabelOpCfgT *cfg = ((ConcurrencyPara *)args)->cfg;
    Status ret;
    uint32_t start = cfg->start;
    uint32_t end = cfg->end;
    for (uint32_t i = start; i <= end; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 0xFFFFFFFF, cfg->optType);
        // 极端情况下，在该线程通过C/S访问拿到元数据后，并打算存入到客户端cache前，元数据仍然可能被其他线程删除、或升降级
        // 因此prepare有可能返回错误。具体逻辑详见 CltCataSaveLabel() -> ... -> RecvGetLabelCasSet()
        if (ret != GMERR_OK) {
            break;
        }
        int64_t f0Value = (int64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t)));
        uint64_t f1Value = (uint64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t)));
        int32_t f2Value = (int32_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t)));
        Status ret = GmcExecute(stmt);
        EXPECT_EQ(ret == GMERR_OK || ret == GMERR_INVALID_PARAMETER_VALUE || ret == GMERR_UNDEFINED_TABLE, true);
        if (ret == GMERR_OK) {
            EXPECT_EQ(GMERR_OK, TestGetAffectedRows(stmt, cfg->expAffectRows));
        } else {
            EXPECT_EQ(GMERR_OK, TestGetAffectedRows(stmt, 0));
        }
    }

    int32_t *thread_ret = (int32_t *)malloc(sizeof(int32_t));
    *thread_ret = 0;
    pthread_exit(thread_ret);
}

static void *DoUpdate(void *args)
{
    GmcStmtT *stmt = ((ConcurrencyPara *)args)->stmt;
    const char *vertexLabelName = ((ConcurrencyPara *)args)->vertexLabelName;
    LabelOpCfgT *cfg = ((ConcurrencyPara *)args)->cfg;
    Status ret;
    uint32_t start = cfg->start;
    uint32_t end = cfg->end;
    for (uint32_t i = start; i <= end; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 0xFFFFFFFF, cfg->optType);
        // 极端情况下，在该线程通过C/S访问拿到元数据后，并打算存入到客户端cache前，元数据仍然可能被其他线程删除、或升降级
        // 因此prepare有可能返回错误。具体逻辑详见 CltCataSaveLabel() -> ... -> RecvGetLabelCasSet()
        if (ret != GMERR_OK) {
            break;
        }
        int64_t keyValue = (int64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &keyValue, sizeof(int64_t)));
        uint64_t f1Value = (uint64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t)));
        int32_t f2Value = (int32_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t)));
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret == GMERR_OK || ret == GMERR_INVALID_PARAMETER_VALUE || ret == GMERR_UNDEFINED_TABLE, true);
        if (ret == GMERR_OK) {
            EXPECT_EQ(GMERR_OK, TestGetAffectedRows(stmt, cfg->expAffectRows));
        } else {
            EXPECT_EQ(GMERR_OK, TestGetAffectedRows(stmt, 0));
        }
    }

    int32_t *thread_ret = (int32_t *)malloc(sizeof(int32_t));
    *thread_ret = 0;
    pthread_exit(thread_ret);
}

static void *DoReplace(void *args)
{
    GmcStmtT *stmt = ((ConcurrencyPara *)args)->stmt;
    const char *vertexLabelName = ((ConcurrencyPara *)args)->vertexLabelName;
    LabelOpCfgT *cfg = ((ConcurrencyPara *)args)->cfg;
    Status ret;
    uint32_t start = cfg->start;
    uint32_t end = cfg->end;
    for (uint32_t i = start; i <= end; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 0xFFFFFFFF, cfg->optType);
        // 极端情况下，在该线程通过C/S访问拿到元数据后，并打算存入到客户端cache前，元数据仍然可能被其他线程删除、或升降级
        // 因此prepare有可能返回错误。具体逻辑详见 CltCataSaveLabel() -> ... -> RecvGetLabelCasSet()
        if (ret != GMERR_OK) {
            break;
        }
        int64_t f0Value = (int64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t)));
        uint64_t f1Value = (uint64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t)));
        int32_t f2Value = (int32_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t)));
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret == GMERR_OK || ret == GMERR_INVALID_PARAMETER_VALUE || ret == GMERR_UNDEFINED_TABLE, true);
        if (ret == GMERR_OK) {
            EXPECT_EQ(GMERR_OK, TestGetAffectedRows(stmt, cfg->expAffectRows));
        } else {
            EXPECT_EQ(GMERR_OK, TestGetAffectedRows(stmt, 0));
        }
    }

    int32_t *thread_ret = (int32_t *)malloc(sizeof(int32_t));
    *thread_ret = 0;
    pthread_exit(thread_ret);
}

static void *DoDelete(void *args)
{
    GmcStmtT *stmt = ((ConcurrencyPara *)args)->stmt;
    const char *vertexLabelName = ((ConcurrencyPara *)args)->vertexLabelName;
    LabelOpCfgT *cfg = ((ConcurrencyPara *)args)->cfg;
    Status ret;
    uint32_t start = cfg->start;
    uint32_t end = cfg->end;
    for (uint32_t i = start; i <= end; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 0xFFFFFFFF, cfg->optType);
        // 极端情况下，在该线程通过C/S访问拿到元数据后，并打算存入到客户端cache前，元数据仍然可能被其他线程删除、或升降级
        // 因此prepare有可能返回错误。具体逻辑详见 CltCataSaveLabel() -> ... -> RecvGetLabelCasSet()
        if (ret != GMERR_OK) {
            break;
        }
        int64_t keyValue = (int64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &keyValue, sizeof(int64_t)));
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret == GMERR_OK || ret == GMERR_INVALID_PARAMETER_VALUE || ret == GMERR_UNDEFINED_TABLE, true);
        if (ret == GMERR_OK) {
            EXPECT_EQ(GMERR_OK, TestGetAffectedRows(stmt, cfg->expAffectRows));
        } else {
            EXPECT_EQ(GMERR_OK, TestGetAffectedRows(stmt, 0));
        }
    }

    int32_t *thread_ret = (int32_t *)malloc(sizeof(int32_t));
    *thread_ret = 0;
    pthread_exit(thread_ret);
}

static void *DoDML(void *args)
{
    GmcStmtT *stmt = ((ConcurrencyPara *)args)->stmt;
    const char *vertexLabelName = ((ConcurrencyPara *)args)->vertexLabelName;
    Status ret;
    for (uint32_t i = 1; i <= 100; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 0xFFFFFFFF, GMC_OPERATION_INSERT);
        // 极端情况下，在该线程通过C/S访问拿到元数据后，并打算存入到客户端cache前，元数据仍然可能被其他线程删除、或升降级
        // 因此prepare有可能返回错误。具体逻辑详见 CltCataSaveLabel() -> ... -> RecvGetLabelCasSet()
        if (ret != GMERR_OK) {
            break;
        }
        int64_t f0Value = (int64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t)));
        uint64_t f1Value = (uint64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t)));
        int32_t f2Value = (int32_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t)));
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret == GMERR_OK || ret == GMERR_INVALID_PARAMETER_VALUE || ret == GMERR_UNDEFINED_TABLE, true);
        if (ret == GMERR_OK) {
            EXPECT_EQ(GMERR_OK, TestGetAffectedRows(stmt, 1));
        } else {
            EXPECT_EQ(GMERR_OK, TestGetAffectedRows(stmt, 0));
        }
    }

    for (uint32_t i = 1; i <= 100; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 0xFFFFFFFF, GMC_OPERATION_REPLACE);
        if (ret != GMERR_OK) {
            break;
        }
        int64_t f0Value = (int64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t)));
        uint64_t f1Value = (uint64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t)));
        int32_t f2Value = (int32_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t)));
        Status ret = GmcExecute(stmt);
        EXPECT_EQ(ret == GMERR_OK || ret == GMERR_INVALID_PARAMETER_VALUE || ret == GMERR_UNDEFINED_TABLE, true);
    }

    for (uint32_t i = 1; i <= 100; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 0xFFFFFFFF, GMC_OPERATION_UPDATE);
        if (ret != GMERR_OK) {
            break;
        }
        int64_t keyValue = (int64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &keyValue, sizeof(int64_t)));
        uint64_t f1Value = (uint64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t)));
        int32_t f2Value = (int32_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t)));
        Status ret = GmcExecute(stmt);
        EXPECT_EQ(ret == GMERR_OK || ret == GMERR_INVALID_PARAMETER_VALUE || ret == GMERR_UNDEFINED_TABLE, true);
    }

    for (uint32_t i = 1; i <= 100; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 0xFFFFFFFF, GMC_OPERATION_DELETE);
        if (ret != GMERR_OK) {
            break;
        }
        int64_t keyValue = (int64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &keyValue, sizeof(int64_t)));
        Status ret = GmcExecute(stmt);
        EXPECT_EQ(ret == GMERR_OK || ret == GMERR_INVALID_PARAMETER_VALUE || ret == GMERR_UNDEFINED_TABLE, true);
    }

    int32_t *thread_ret = (int32_t *)malloc(sizeof(int32_t));
    *thread_ret = 0;
    pthread_exit(thread_ret);
}

static void *DoUpgradeAndDownGrade(void *args)
{
    GmcStmtT *stmt = ((ConcurrencyPara *)args)->stmt;
    const char *vertexLabelName = ((ConcurrencyPara *)args)->vertexLabelName;
    string schemaV2 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v2.gmjson");
    string schemaV3 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v3.gmjson");
    Status ret;
    for (int i = 0; i < 50; i++) {
        ret = GmcAlterVertexLabelWithName(stmt, schemaV2.c_str(), true, vertexLabelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, vertexLabelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDegradeVertexLabel(stmt, vertexLabelName, 2);
        EXPECT_EQ(GMERR_OK, ret);
        WaitingDegradeEnd(stmt, vertexLabelName);
        ret = GmcDegradeVertexLabel(stmt, vertexLabelName, 1);
        EXPECT_EQ(GMERR_OK, ret);
        WaitingDegradeEnd(stmt, vertexLabelName);
    }
    int32_t *thread_ret = (int32_t *)malloc(sizeof(int32_t));
    *thread_ret = ret;
    pthread_exit(thread_ret);
}

TEST_F(StClientDWMultiVersion, DwMultiVersionInsertConcurrency)
{
    GmcConnT *dmlConn;
    GmcStmtT *dmlStmt;

    CreateSyncConnectionAndStmt(&dmlConn, &dmlStmt);

    GmcConnT *upAndDownConn;
    GmcStmtT *upAndDownStmt;

    CreateSyncConnectionAndStmt(&upAndDownConn, &upAndDownStmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(dmlStmt, schemaV1.c_str(), g_label_config));

    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 100;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;

    ConcurrencyPara dmlArgs;
    dmlArgs.stmt = dmlStmt;
    dmlArgs.vertexLabelName = vertexLabelName;
    dmlArgs.cfg = &cfg1;

    ConcurrencyPara upAndDownArgs;
    upAndDownArgs.stmt = upAndDownStmt;
    upAndDownArgs.vertexLabelName = vertexLabelName;

    int32_t *thread_ret;
    uint32_t threadNum = 2;
    pthread_t tid[threadNum];
    pthread_create(&tid[0], NULL, DoUpgradeAndDownGrade, &upAndDownArgs);
    pthread_create(&tid[1], NULL, DoInsert, &dmlArgs);

    for (uint32_t i = 0; i < threadNum; i++) {
        pthread_join(tid[i], (void **)&thread_ret);
        printf("[thread %u] thread ret: %d\n", i, *thread_ret);
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dmlStmt, vertexLabelName));

    DestroyConnectionAndStmt(upAndDownConn, upAndDownStmt);
    DestroyConnectionAndStmt(dmlConn, dmlStmt);
}

TEST_F(StClientDWMultiVersion, DwMultiVersionReplaceConcurrency)
{
    GmcConnT *dmlConn;
    GmcStmtT *dmlStmt;

    CreateSyncConnectionAndStmt(&dmlConn, &dmlStmt);

    GmcConnT *upAndDownConn;
    GmcStmtT *upAndDownStmt;

    CreateSyncConnectionAndStmt(&upAndDownConn, &upAndDownStmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(dmlStmt, schemaV1.c_str(), g_label_config));

    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 100;
    cfg1.optType = GMC_OPERATION_REPLACE;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;

    ConcurrencyPara dmlArgs;
    dmlArgs.stmt = dmlStmt;
    dmlArgs.vertexLabelName = vertexLabelName;
    dmlArgs.cfg = &cfg1;

    ConcurrencyPara upAndDownArgs;
    upAndDownArgs.stmt = upAndDownStmt;
    upAndDownArgs.vertexLabelName = vertexLabelName;

    int32_t *thread_ret;
    uint32_t threadNum = 2;
    pthread_t tid[threadNum];
    pthread_create(&tid[0], NULL, DoUpgradeAndDownGrade, &upAndDownArgs);
    pthread_create(&tid[1], NULL, DoReplace, &dmlArgs);

    for (uint32_t i = 0; i < threadNum; i++) {
        pthread_join(tid[i], (void **)&thread_ret);
        printf("[thread %u] thread ret: %d\n", i, *thread_ret);
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dmlStmt, vertexLabelName));

    DestroyConnectionAndStmt(upAndDownConn, upAndDownStmt);
    DestroyConnectionAndStmt(dmlConn, dmlStmt);
}

TEST_F(StClientDWMultiVersion, DwMultiVersionUpdateConcurrency)
{
    GmcConnT *dmlConn;
    GmcStmtT *dmlStmt;

    CreateSyncConnectionAndStmt(&dmlConn, &dmlStmt);

    GmcConnT *upAndDownConn;
    GmcStmtT *upAndDownStmt;

    CreateSyncConnectionAndStmt(&upAndDownConn, &upAndDownStmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(dmlStmt, schemaV1.c_str(), g_label_config));

    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 1000;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    SimpleLabelDmlExecute(dmlStmt, vertexLabelName, &cfg1);
    cfg1.optType = GMC_OPERATION_UPDATE;

    ConcurrencyPara dmlArgs;
    dmlArgs.stmt = dmlStmt;
    dmlArgs.vertexLabelName = vertexLabelName;
    dmlArgs.cfg = &cfg1;

    ConcurrencyPara upAndDownArgs;
    upAndDownArgs.stmt = upAndDownStmt;
    upAndDownArgs.vertexLabelName = vertexLabelName;

    int32_t *thread_ret;
    uint32_t threadNum = 2;
    pthread_t tid[threadNum];
    pthread_create(&tid[0], NULL, DoUpgradeAndDownGrade, &upAndDownArgs);
    pthread_create(&tid[1], NULL, DoUpdate, &dmlArgs);

    for (uint32_t i = 0; i < threadNum; i++) {
        pthread_join(tid[i], (void **)&thread_ret);
        printf("[thread %u] thread ret: %d\n", i, *thread_ret);
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dmlStmt, vertexLabelName));

    DestroyConnectionAndStmt(upAndDownConn, upAndDownStmt);
    DestroyConnectionAndStmt(dmlConn, dmlStmt);
}

TEST_F(StClientDWMultiVersion, DwMultiVersionDeleteConcurrency)
{
    GmcConnT *dmlConn;
    GmcStmtT *dmlStmt;

    CreateSyncConnectionAndStmt(&dmlConn, &dmlStmt);

    GmcConnT *upAndDownConn;
    GmcStmtT *upAndDownStmt;

    CreateSyncConnectionAndStmt(&upAndDownConn, &upAndDownStmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(dmlStmt, schemaV1.c_str(), g_label_config));

    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 1000;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    SimpleLabelDmlExecute(dmlStmt, vertexLabelName, &cfg1);
    cfg1.optType = GMC_OPERATION_DELETE;

    ConcurrencyPara dmlArgs;
    dmlArgs.stmt = dmlStmt;
    dmlArgs.vertexLabelName = vertexLabelName;
    dmlArgs.cfg = &cfg1;

    ConcurrencyPara upAndDownArgs;
    upAndDownArgs.stmt = upAndDownStmt;
    upAndDownArgs.vertexLabelName = vertexLabelName;

    int32_t *thread_ret;
    uint32_t threadNum = 2;
    pthread_t tid[threadNum];
    pthread_create(&tid[0], NULL, DoUpgradeAndDownGrade, &upAndDownArgs);
    pthread_create(&tid[1], NULL, DoDelete, &dmlArgs);

    for (uint32_t i = 0; i < threadNum; i++) {
        pthread_join(tid[i], (void **)&thread_ret);
        printf("[thread %u] thread ret: %d\n", i, *thread_ret);
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dmlStmt, vertexLabelName));

    DestroyConnectionAndStmt(upAndDownConn, upAndDownStmt);
    DestroyConnectionAndStmt(dmlConn, dmlStmt);
}

TEST_F(StClientDWMultiVersion, DwMultiVersionDMLConcurrency)
{
    GmcConnT *dmlConn;
    GmcStmtT *dmlStmt;

    CreateSyncConnectionAndStmt(&dmlConn, &dmlStmt);

    GmcConnT *upAndDownConn;
    GmcStmtT *upAndDownStmt;

    CreateSyncConnectionAndStmt(&upAndDownConn, &upAndDownStmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(dmlStmt, schemaV1.c_str(), g_label_config));

    ConcurrencyPara dmlArgs;
    dmlArgs.stmt = dmlStmt;
    dmlArgs.vertexLabelName = vertexLabelName;

    ConcurrencyPara upAndDownArgs;
    upAndDownArgs.stmt = upAndDownStmt;
    upAndDownArgs.vertexLabelName = vertexLabelName;

    int32_t *thread_ret;
    uint32_t threadNum = 2;
    pthread_t tid[threadNum];
    pthread_create(&tid[0], NULL, DoUpgradeAndDownGrade, &upAndDownArgs);
    pthread_create(&tid[1], NULL, DoDML, &dmlArgs);

    for (uint32_t i = 0; i < threadNum; i++) {
        pthread_join(tid[i], (void **)&thread_ret);
        printf("[thread %u] thread ret: %d\n", i, *thread_ret);
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dmlStmt, vertexLabelName));

    DestroyConnectionAndStmt(upAndDownConn, upAndDownStmt);
    DestroyConnectionAndStmt(dmlConn, dmlStmt);
}

TEST_F(StClientDWMultiVersion, DwMultiVersionCSConcurrency)
{
    GmcConnT *dmlConn;
    GmcStmtT *dmlStmt;

    CreateSyncConnectionAndStmt(&dmlConn, &dmlStmt);

    GmcConnT *CSupAndDownConn;
    GmcStmtT *CSupAndDownStmt;

    CreateSyncConnectionAndStmt(&CSupAndDownConn, &CSupAndDownStmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(dmlStmt, schemaV1.c_str(), g_label_config));

    ConcurrencyPara dmlArgs;
    dmlArgs.stmt = dmlStmt;
    dmlArgs.vertexLabelName = vertexLabelName;

    ConcurrencyPara upAndDownArgs;
    upAndDownArgs.stmt = CSupAndDownStmt;
    upAndDownArgs.vertexLabelName = vertexLabelName;

    int32_t *thread_ret;
    uint32_t threadNum = 2;
    pthread_t tid[threadNum];
    pthread_create(&tid[0], NULL, DoUpgradeAndDownGrade, &upAndDownArgs);
    pthread_create(&tid[1], NULL, DoDML, &dmlArgs);

    for (uint32_t i = 0; i < threadNum; i++) {
        pthread_join(tid[i], (void **)&thread_ret);
        printf("[thread %u] thread ret: %d\n", i, *thread_ret);
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dmlStmt, vertexLabelName));

    DestroyConnectionAndStmt(CSupAndDownConn, CSupAndDownStmt);
    DestroyConnectionAndStmt(dmlConn, dmlStmt);
}

static void SetPropertyForV1(GmcStmtT *stmt, uint32_t value, uint32_t opCode, bool isSecIdxUpdate)
{
    Status ret;
    if (opCode != GMC_OPERATION_UPDATE) {
        int64_t f0Value = (int64_t)value;
        ret = GmcSetVertexProperty(stmt, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint64_t f1Value = (uint64_t)value;
    ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f2Value = (int32_t)value;
    ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    if (!(opCode == GMC_OPERATION_UPDATE && isSecIdxUpdate)) {
        uint32_t f3Value = (uint32_t)value;
        ret = GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    int16_t f4Value = (int16_t)value;
    ret = GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t f5Value = (uint16_t)value;
    ret = GmcSetVertexProperty(stmt, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t f6Value = (int8_t)value;
    ret = GmcSetVertexProperty(stmt, (char *)"F6", GMC_DATATYPE_INT8, &f6Value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t f7Value = (uint8_t)value;
    ret = GmcSetVertexProperty(stmt, (char *)"F7", GMC_DATATYPE_UINT8, &f7Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool f8Value = false;
    ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_BOOL, &f8Value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    float f9Value = (float)value;
    ret = GmcSetVertexProperty(stmt, (char *)"F9", GMC_DATATYPE_FLOAT, &f9Value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double f10Value = (double)value;
    ret = GmcSetVertexProperty(stmt, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10Value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t f11Value = (uint64_t)value;
    ret = GmcSetVertexProperty(stmt, (char *)"F11", GMC_DATATYPE_TIME, &f11Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char f12Value = 'a';
    ret = GmcSetVertexProperty(stmt, (char *)"F12", GMC_DATATYPE_CHAR, &f12Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    char f13Value = 'a';
    ret = GmcSetVertexProperty(stmt, (char *)"F13", GMC_DATATYPE_UCHAR, &f13Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    const char *f14Value = "stringtype";
    ret = GmcSetVertexProperty(stmt, (char *)"F14", GMC_DATATYPE_STRING, f14Value, strlen(f14Value));
    EXPECT_EQ(GMERR_OK, ret);
    const char *f15Value = "ABCDEFGHIJKLMNOP";
    ret = GmcSetVertexProperty(stmt, (char *)"F15", GMC_DATATYPE_FIXED, f15Value, strlen(f15Value));
    EXPECT_EQ(GMERR_OK, ret);
    char f16Value[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, (char *)"F16", GMC_DATATYPE_BYTES, f16Value, sizeof(f16Value));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t f17Value = 1;
    ret = GmcSetVertexProperty(stmt, (char *)"F17", GMC_DATATYPE_BITFIELD8, &f17Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f18Value = 1;
    ret = GmcSetVertexProperty(stmt, (char *)"F18", GMC_DATATYPE_BITFIELD32, &f18Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f20Value = (uint32_t)value;
    ret = GmcSetVertexProperty(stmt, (char *)"F20", GMC_DATATYPE_UINT32, &f20Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f21Value = (uint32_t)value;
    ret = GmcSetVertexProperty(stmt, (char *)"F21", GMC_DATATYPE_UINT32, &f21Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置所有子节点属性
    GmcNodeT *childNode = NULL;
    ret = GmcGetChildNode(stmt, "T1", &childNode);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *elementNode = NULL;
    ret = GmcNodeAppendElement(childNode, &elementNode);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t v0Value = (int64_t)value;
    ret = GmcNodeSetPropertyByName(elementNode, "V0", GMC_DATATYPE_INT64, &v0Value, sizeof(v0Value));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t v1Value = (uint64_t)value;
    ret = GmcNodeSetPropertyByName(elementNode, "V1", GMC_DATATYPE_UINT64, &v1Value, sizeof(v1Value));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t v2Value = (int32_t)value;
    ret = GmcNodeSetPropertyByName(elementNode, "V2", GMC_DATATYPE_INT32, &v2Value, sizeof(v2Value));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t v3Value = (uint32_t)value;
    ret = GmcNodeSetPropertyByName(elementNode, "V3", GMC_DATATYPE_UINT32, &v3Value, sizeof(v3Value));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t v4Value = (int16_t)value;
    ret = GmcNodeSetPropertyByName(elementNode, "V4", GMC_DATATYPE_INT16, &v4Value, sizeof(v4Value));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t v5Value = (uint16_t)value;
    ret = GmcNodeSetPropertyByName(elementNode, "V5", GMC_DATATYPE_UINT16, &v5Value, sizeof(v5Value));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t v6Value = (int8_t)value;
    ret = GmcNodeSetPropertyByName(elementNode, "V6", GMC_DATATYPE_INT8, &v6Value, sizeof(v6Value));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t v7Value = (uint8_t)value;
    ret = GmcNodeSetPropertyByName(elementNode, "V7", GMC_DATATYPE_UINT8, &v7Value, sizeof(v7Value));
    EXPECT_EQ(GMERR_OK, ret);
    bool v8Value = false;
    ret = GmcNodeSetPropertyByName(elementNode, "V8", GMC_DATATYPE_BOOL, &v8Value, sizeof(v8Value));
    EXPECT_EQ(GMERR_OK, ret);
    float v9Value = (float)value;
    ret = GmcNodeSetPropertyByName(elementNode, "V9", GMC_DATATYPE_FLOAT, &v9Value, sizeof(v9Value));
    EXPECT_EQ(GMERR_OK, ret);
    double v10Value = (double)value;
    ret = GmcNodeSetPropertyByName(elementNode, "V10", GMC_DATATYPE_DOUBLE, &v10Value, sizeof(v10Value));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t v11Value = (uint64_t)value;
    ret = GmcNodeSetPropertyByName(elementNode, "V11", GMC_DATATYPE_TIME, &v11Value, sizeof(v11Value));
    EXPECT_EQ(GMERR_OK, ret);
    char v12Value = 'a';
    ret = GmcNodeSetPropertyByName(elementNode, "V12", GMC_DATATYPE_CHAR, &v12Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    char v13Value = 'a';
    ret = GmcNodeSetPropertyByName(elementNode, "V13", GMC_DATATYPE_UCHAR, &v13Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);

    const char *v14Value = "stringtype";
    ret = GmcNodeSetPropertyByName(elementNode, (char *)"V14", GMC_DATATYPE_STRING, v14Value, strlen(v14Value));
    EXPECT_EQ(GMERR_OK, ret);
    const char *v15Value = "ABCDEFGHI";
    ret = GmcNodeSetPropertyByName(elementNode, (char *)"V15", GMC_DATATYPE_FIXED, v15Value, strlen(v15Value));
    EXPECT_EQ(GMERR_OK, ret);
    char v16Value[10] = "bytes";
    ret = GmcNodeSetPropertyByName(elementNode, (char *)"V16", GMC_DATATYPE_BYTES, v16Value, sizeof(v16Value));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t v17Value = 1;
    ret = GmcNodeSetPropertyByName(elementNode, (char *)"V17", GMC_DATATYPE_BITFIELD8, &v17Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t v18Value = 1;
    ret = GmcNodeSetPropertyByName(elementNode, (char *)"V18", GMC_DATATYPE_BITFIELD16, &v18Value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t v19Value = 1;
    ret = GmcNodeSetPropertyByName(elementNode, (char *)"V19", GMC_DATATYPE_BITFIELD32, &v19Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t v20Value = 1;
    ret = GmcNodeSetPropertyByName(elementNode, (char *)"V20", GMC_DATATYPE_BITFIELD64, &v20Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
}

static void SetPropertyForV1V2Delta(GmcStmtT *stmt, uint32_t value)
{
    Status ret;
    GmcNodeT *childNode = NULL;
    ret = GmcGetChildNode(stmt, "T2", &childNode);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, ret);
    int8_t v0Value = (int8_t)value;
    ret = GmcNodeSetPropertyByName(childNode, "V0", GMC_DATATYPE_INT8, &v0Value, sizeof(v0Value));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t v1Value = (uint8_t)value;
    ret = GmcNodeSetPropertyByName(childNode, "V1", GMC_DATATYPE_UINT8, &v1Value, sizeof(v1Value));
    EXPECT_EQ(GMERR_OK, ret);
    bool v2Value = false;
    ret = GmcNodeSetPropertyByName(childNode, "V2", GMC_DATATYPE_BOOL, &v2Value, sizeof(v2Value));
    EXPECT_EQ(GMERR_OK, ret);
    float v3Value = (float)value;
    ret = GmcNodeSetPropertyByName(childNode, "V3", GMC_DATATYPE_FLOAT, &v3Value, sizeof(v3Value));
    EXPECT_EQ(GMERR_OK, ret);
    double v4Value = (double)value;
    ret = GmcNodeSetPropertyByName(childNode, "V4", GMC_DATATYPE_DOUBLE, &v4Value, sizeof(v4Value));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t v5Value = (uint64_t)value;
    ret = GmcNodeSetPropertyByName(childNode, "V5", GMC_DATATYPE_TIME, &v5Value, sizeof(v5Value));
    EXPECT_EQ(GMERR_OK, ret);
    char v6Value = 'a';
    ret = GmcNodeSetPropertyByName(childNode, "V6", GMC_DATATYPE_CHAR, &v6Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    char v7Value = 'a';
    ret = GmcNodeSetPropertyByName(childNode, "V7", GMC_DATATYPE_UCHAR, &v7Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    const char *v8Value = "stringtype";
    ret = GmcNodeSetPropertyByName(childNode, (char *)"V8", GMC_DATATYPE_STRING, v8Value, strlen(v8Value));
    EXPECT_EQ(GMERR_OK, ret);
    const char *v9Value = "ABCDEFGHI";
    ret = GmcNodeSetPropertyByName(childNode, (char *)"V9", GMC_DATATYPE_FIXED, v9Value, strlen(v9Value));
    EXPECT_EQ(GMERR_OK, ret);
    char v10Value[10] = "bytes";
    ret = GmcNodeSetPropertyByName(childNode, (char *)"V10", GMC_DATATYPE_BYTES, v10Value, sizeof(v10Value));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t v11Value = 1;
    ret = GmcNodeSetPropertyByName(childNode, (char *)"V11", GMC_DATATYPE_BITFIELD8, &v11Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t v12Value = 1;
    ret = GmcNodeSetPropertyByName(childNode, (char *)"V12", GMC_DATATYPE_BITFIELD16, &v12Value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t v13Value = 1;
    ret = GmcNodeSetPropertyByName(childNode, (char *)"V13", GMC_DATATYPE_BITFIELD32, &v13Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t v14Value = 1;
    ret = GmcNodeSetPropertyByName(childNode, (char *)"V14", GMC_DATATYPE_BITFIELD64, &v14Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
}

static void SetPropertyForV2V3Delta(GmcStmtT *stmt, uint32_t value)
{
    Status ret;
    GmcNodeT *childNode = NULL;
    ret = GmcGetChildNode(stmt, "T3", &childNode);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, ret);
    int8_t v0Value = (int8_t)value;
    ret = GmcNodeSetPropertyByName(childNode, "V0", GMC_DATATYPE_INT8, &v0Value, sizeof(v0Value));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t v1Value = (uint8_t)value;
    ret = GmcNodeSetPropertyByName(childNode, "V1", GMC_DATATYPE_UINT8, &v1Value, sizeof(v1Value));
    EXPECT_EQ(GMERR_OK, ret);
    bool v2Value = false;
    ret = GmcNodeSetPropertyByName(childNode, "V2", GMC_DATATYPE_BOOL, &v2Value, sizeof(v2Value));
    EXPECT_EQ(GMERR_OK, ret);
    float v3Value = (float)value;
    ret = GmcNodeSetPropertyByName(childNode, "V3", GMC_DATATYPE_FLOAT, &v3Value, sizeof(v3Value));
    EXPECT_EQ(GMERR_OK, ret);
    double v4Value = (double)value;
    ret = GmcNodeSetPropertyByName(childNode, "V4", GMC_DATATYPE_DOUBLE, &v4Value, sizeof(v4Value));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t v5Value = (uint64_t)value;
    ret = GmcNodeSetPropertyByName(childNode, "V5", GMC_DATATYPE_TIME, &v5Value, sizeof(v5Value));
    EXPECT_EQ(GMERR_OK, ret);
    char v6Value = 'a';
    ret = GmcNodeSetPropertyByName(childNode, "V6", GMC_DATATYPE_CHAR, &v6Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    char v7Value = 'a';
    ret = GmcNodeSetPropertyByName(childNode, "V7", GMC_DATATYPE_UCHAR, &v7Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    const char *v8Value = "stringtype";
    ret = GmcNodeSetPropertyByName(childNode, (char *)"V8", GMC_DATATYPE_STRING, v8Value, strlen(v8Value));
    EXPECT_EQ(GMERR_OK, ret);
    const char *v9Value = "ABCDEFGHI";
    ret = GmcNodeSetPropertyByName(childNode, (char *)"V9", GMC_DATATYPE_FIXED, v9Value, strlen(v9Value));
    EXPECT_EQ(GMERR_OK, ret);
    char v10Value[10] = "bytes";
    ret = GmcNodeSetPropertyByName(childNode, (char *)"V10", GMC_DATATYPE_BYTES, v10Value, sizeof(v10Value));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t v11Value = 1;
    ret = GmcNodeSetPropertyByName(childNode, (char *)"V11", GMC_DATATYPE_BITFIELD8, &v11Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t v12Value = 1;
    ret = GmcNodeSetPropertyByName(childNode, (char *)"V12", GMC_DATATYPE_BITFIELD16, &v12Value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t v13Value = 1;
    ret = GmcNodeSetPropertyByName(childNode, (char *)"V13", GMC_DATATYPE_BITFIELD32, &v13Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t v14Value = 1;
    ret = GmcNodeSetPropertyByName(childNode, (char *)"V14", GMC_DATATYPE_BITFIELD64, &v14Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
}

static void GeneralComplexLabelWriteData(GmcStmtT *stmt, const char *vertexlabelName, LabelOpCfgT *cfg)
{
    uint32_t start = cfg->start;
    uint32_t end = cfg->end;
    uint32_t versionId = cfg->versionId;
    Status ret;
    for (uint32_t i = start; i <= end; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexlabelName, 0xFFFFFFFF, cfg->optType);
        EXPECT_EQ(GMERR_OK, ret);
        if (cfg->optType == GMC_OPERATION_UPDATE) {
            if (cfg->isSecIdxUpdate) {
                uint32_t keyValue = i;
                EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "unique_secIdx"));
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t));
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                int64_t keyValue = (int64_t)i;
                EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &keyValue, sizeof(int64_t));
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        uint32_t value = i + cfg->deltaData;
        SetPropertyForV1(stmt, value, cfg->optType, cfg->isSecIdxUpdate);
        if (versionId == 2) {
            SetPropertyForV1V2Delta(stmt, value);
        }
        if (versionId == 3) {
            SetPropertyForV1V2Delta(stmt, value);
            SetPropertyForV2V3Delta(stmt, value);
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(cfg->expExecuteStatus, ret);
        ret = TestGetAffectedRows(stmt, cfg->expAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

static void GeneralComplexLabelDeleteData(GmcStmtT *stmt, const char *vertexlabelName, LabelOpCfgT *cfg)
{
    uint32_t start = cfg->start;
    uint32_t end = cfg->end;
    for (uint32_t i = start; i <= end; i++) {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithVersion(stmt, vertexlabelName, 0xFFFFFFFF, cfg->optType));
        int64_t keyValue = (int64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &keyValue, sizeof(int64_t)));
        EXPECT_EQ(cfg->expExecuteStatus, GmcExecute(stmt));
        EXPECT_EQ(GMERR_OK, TestGetAffectedRows(stmt, cfg->expAffectRows));
    }
    return;
}

static void ReadAndCompareV1Data(GmcNodeT *node, uint32_t value, bool isSecIdxUpdate)
{
    Status ret;
    bool isNull;
    if (!isSecIdxUpdate) {
        int64_t f0Value = (int64_t)value;
        int64_t f0GetValue;
        ret = GmcNodeGetPropertyByName(node, (char *)"F0", &f0GetValue, sizeof(int64_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(f0Value, f0GetValue);
    }

    uint64_t f1Value = (uint64_t)value;
    uint64_t f1GetValue;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1GetValue, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(f1Value, f1GetValue);

    int32_t f2Value = (int32_t)value;
    int32_t f2GetValue;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2GetValue, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(f2Value, f2GetValue);

    if (!isSecIdxUpdate) {
        uint32_t f3Value = (uint32_t)value;
        uint32_t f3GetValue;
        ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3GetValue, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(f3Value, f3GetValue);
    }

    int16_t f4Value = (int16_t)value;
    int16_t f4GetValue;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4GetValue, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(f4Value, f4GetValue);

    uint16_t f5Value = (uint16_t)value;
    uint16_t f5GetValue;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5GetValue, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(f5Value, f5GetValue);

    int8_t f6Value = (int8_t)value;
    int8_t f6GetValue;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &f6GetValue, sizeof(int8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(f6Value, f6GetValue);

    uint8_t f7Value = (uint8_t)value;
    uint8_t f7GetValue;
    ret = GmcNodeGetPropertyByName(node, (char *)"F7", &f7GetValue, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(f7Value, f7GetValue);

    bool f8Value = false;
    bool f8GetValue;
    ret = GmcNodeGetPropertyByName(node, (char *)"F8", &f8GetValue, sizeof(bool), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(f8Value, f8GetValue);

    float f9Value = (float)value;
    float f9GetValue;
    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9GetValue, sizeof(float), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(f9Value, f9GetValue);

    double f10Value = (double)value;
    double f10GetValue;
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10GetValue, sizeof(double), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(f10Value, f10GetValue);

    uint64_t f11Value = (uint64_t)value;
    uint64_t f11GetValue;
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f11GetValue, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(f11Value, f11GetValue);

    char f12Value = 'a';
    char f12GetValue;
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &f12GetValue, sizeof(char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(f12Value, f12GetValue);

    char f13Value = 'a';
    char f13GetValue;
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13GetValue, sizeof(char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(f13Value, f13GetValue);

    const char *f14Value = "stringtype";
    char f14GetValue[100];
    uint32_t f14ValueLen = 0;
    ret = GmcNodeGetPropertySizeByName(node, "F14", &f14ValueLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", f14GetValue, f14ValueLen, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(strcmp(f14Value, f14GetValue), 0);

    const char *f15Value = "ABCDEFGHIJKLMNOP";
    char f15GetValue[17];
    uint32_t f15ValueLen = 0;
    ret = GmcNodeGetPropertySizeByName(node, "F15", &f15ValueLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, (char *)"F15", f15GetValue, f15ValueLen, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    f15GetValue[16] = '\0';
    EXPECT_EQ(strcmp(f15Value, f15GetValue), 0);

    char f16Value[10] = "bytes";
    char f16GetValue[10];
    ret = GmcNodeGetPropertyByName(node, (char *)"F16", f16GetValue, sizeof(f16Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(strcmp(f16Value, f16GetValue), 0);

    uint8_t f17Value = 1;
    uint8_t f17GetValue;
    ret = GmcNodeGetPropertyByName(node, (char *)"F17", &f17GetValue, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(f17Value, f17GetValue);

    uint32_t f18Value = 1;
    uint32_t f18GetValue;
    ret = GmcNodeGetPropertyByName(node, (char *)"F18", &f18GetValue, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(f18Value, f18GetValue);

    uint32_t f20Value = (uint32_t)value;
    uint32_t f20GetValue;
    ret = GmcNodeGetPropertyByName(node, (char *)"F20", &f20GetValue, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(f20Value, f20GetValue);

    uint32_t f21Value = (uint32_t)value;
    uint32_t f21GetValue;
    ret = GmcNodeGetPropertyByName(node, (char *)"F21", &f21GetValue, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(f21Value, f21GetValue);

    GmcNodeT *childNode = NULL;
    GmcNodeT *elementNode = NULL;
    ret = GmcNodeGetChild(node, "T1", &childNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetElementByIndex(childNode, 0, &elementNode);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t v0Value = (int64_t)value;
    int64_t v0GetValue;
    ret = GmcNodeGetPropertyByName(elementNode, "V0", &v0GetValue, sizeof(v0Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(v0Value, v0GetValue);

    uint64_t v1Value = (uint64_t)value;
    uint64_t v1GetValue;
    ret = GmcNodeGetPropertyByName(elementNode, "V1", &v1GetValue, sizeof(v1Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(v1Value, v1GetValue);

    int32_t v2Value = (int32_t)value;
    int32_t v2GetValue;
    ret = GmcNodeGetPropertyByName(elementNode, "V2", &v2GetValue, sizeof(v2Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(v2Value, v2GetValue);

    uint32_t v3Value = (uint32_t)value;
    uint32_t v3GetValue;
    ret = GmcNodeGetPropertyByName(elementNode, "V3", &v3GetValue, sizeof(v3Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(v3Value, v3GetValue);

    int16_t v4Value = (int16_t)value;
    int16_t v4GetValue;
    ret = GmcNodeGetPropertyByName(elementNode, "V4", &v4GetValue, sizeof(v4Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(v4Value, v4GetValue);

    uint16_t v5Value = (uint16_t)value;
    uint16_t v5GetValue;
    ret = GmcNodeGetPropertyByName(elementNode, "V5", &v5GetValue, sizeof(v5Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(v5Value, v5GetValue);

    int8_t v6Value = (int8_t)value;
    int8_t v6GetValue;
    ret = GmcNodeGetPropertyByName(elementNode, "V6", &v6GetValue, sizeof(v6Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(v6Value, v6GetValue);

    uint8_t v7Value = (uint8_t)value;
    uint8_t v7GetValue;
    ret = GmcNodeGetPropertyByName(elementNode, "V7", &v7GetValue, sizeof(v7Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(v7Value, v7GetValue);

    bool v8Value = false;
    bool v8GetValue;
    ret = GmcNodeGetPropertyByName(elementNode, "V8", &v8GetValue, sizeof(v8Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(v8Value, v8GetValue);

    float v9Value = (float)value;
    float v9GetValue;
    ret = GmcNodeGetPropertyByName(elementNode, "V9", &v9GetValue, sizeof(v9Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(v9Value, v9GetValue);

    double v10Value = (double)value;
    double v10GetValue;
    ret = GmcNodeGetPropertyByName(elementNode, "V10", &v10GetValue, sizeof(v10Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(v10Value, v10GetValue);

    uint64_t v11Value = (uint64_t)value;
    uint64_t v11GetValue;
    ret = GmcNodeGetPropertyByName(elementNode, "V11", &v11GetValue, sizeof(v11Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(v11Value, v11GetValue);

    char v12Value = 'a';
    char v12GetValue;
    ret = GmcNodeGetPropertyByName(elementNode, "V12", &v12GetValue, sizeof(char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(v12Value, v12GetValue);

    char v13Value = 'a';
    char v13GetValue;
    ret = GmcNodeGetPropertyByName(elementNode, "V13", &v13GetValue, sizeof(char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(v13Value, v13GetValue);

    const char *v14Value = "stringtype";
    char v14GetValue[100];
    uint32_t v14ValueLen = 0;
    ret = GmcNodeGetPropertySizeByName(elementNode, "V14", &v14ValueLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(elementNode, (char *)"V14", v14GetValue, v14ValueLen, &isNull);
    v14GetValue[10] = '\0';
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(strcmp(v14Value, v14GetValue), 0);

    const char *v15Value = "ABCDEFGHI";
    char v15GetValue[100];
    uint32_t v15ValueLen = 0;
    ret = GmcNodeGetPropertySizeByName(elementNode, "V15", &v15ValueLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(elementNode, (char *)"V15", v15GetValue, v15ValueLen, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    v15GetValue[9] = '\0';
    EXPECT_EQ(strcmp(v15Value, v15GetValue), 0);

    char v16Value[10] = "bytes";
    char v16GetValue[10];
    ret = GmcNodeGetPropertyByName(elementNode, (char *)"V16", v16GetValue, sizeof(v16Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(strcmp(v16Value, v16GetValue), 0);

    uint8_t v17Value = 1;
    uint8_t v17GetValue;
    ret = GmcNodeGetPropertyByName(elementNode, (char *)"V17", &v17GetValue, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(v17Value, v17GetValue);

    uint16_t v18Value = 1;
    uint16_t v18GetValue;
    ret = GmcNodeGetPropertyByName(elementNode, (char *)"V18", &v18GetValue, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(v18Value, v18GetValue);

    uint32_t v19Value = 1;
    uint32_t v19GetValue;
    ret = GmcNodeGetPropertyByName(elementNode, (char *)"V19", &v19GetValue, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(v19Value, v19GetValue);

    uint64_t v20Value = 1;
    uint64_t v20GetValue;
    ret = GmcNodeGetPropertyByName(elementNode, (char *)"V20", &v20GetValue, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(v20Value, v20GetValue);
}

static void GeneralComplexLabelReadAndCompareCommonData(GmcStmtT *stmt, const char *vertexLabelName, LabelOpCfgT *cfg)
{
    uint32_t start = cfg->start;
    uint32_t end = cfg->end;
    bool isFinish = false;
    uint32_t versionId = cfg->versionId;
    Status ret;
    for (uint32_t i = start; i <= end; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, versionId, cfg->optType);
        EXPECT_EQ(cfg->expExecuteStatus, ret);
        if (ret != GMERR_OK) {
            continue;
        }
        // 设置主键
        int64_t keyValue = (int64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &keyValue, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // 取值然后进行比较
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *rootNode;
        // get根节点
        ret = GmcGetRootNode(stmt, &rootNode);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t value = i + cfg->deltaData;
        ReadAndCompareV1Data(rootNode, value, cfg->isSecIdxUpdate);
    }
}

void GeneralComplexLabelDmlExecute(GmcStmtT *stmt, const char *vertexLabelName, LabelOpCfgT *cfg)
{
    switch (cfg->optType) {
        case GMC_OPERATION_INSERT:
        case GMC_OPERATION_REPLACE:
        case GMC_OPERATION_UPDATE:
            GeneralComplexLabelWriteData(stmt, vertexLabelName, cfg);
            break;
        case GMC_OPERATION_DELETE:
            GeneralComplexLabelDeleteData(stmt, vertexLabelName, cfg);
            break;
        case GMC_OPERATION_SCAN:
            GeneralComplexLabelReadAndCompareCommonData(stmt, vertexLabelName, cfg);
            break;
        default:
            break;
    }
    return;
}

static bool ByteCompare(uint8_t *array1, uint8_t *array2)
{
    for (int i = 0; i < 10; i++) {
        if (array1[i] != array2[i]) {
            return false;
        }
    }
    return true;
}

static void ReadAndCompareV1V2DeltaData(GmcNodeT *node, uint32_t value, bool isDefaultValue)
{
    GmcNodeT *childNode = NULL;
    Status ret;
    bool isNull;
    // 设置所有子节点属性
    ret = GmcNodeGetChild(node, "T2", &childNode);
    EXPECT_EQ(GMERR_OK, ret);
    int8_t v0Value = (int8_t)value;
    int8_t v0GetValue = 0;
    ret = GmcNodeGetPropertyByName(childNode, "V0", &v0GetValue, sizeof(v0Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    if (isDefaultValue) {
        EXPECT_TRUE(isNull);
    } else {
        EXPECT_FALSE(isNull);
        EXPECT_EQ(v0Value, v0GetValue);
    }

    uint8_t v1Value = (uint8_t)value;
    uint8_t v1GetValue;
    ret = GmcNodeGetPropertyByName(childNode, "V1", &v1GetValue, sizeof(v1Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    if (isDefaultValue) {
        EXPECT_TRUE(isNull);
    } else {
        EXPECT_FALSE(isNull);
        EXPECT_EQ(v1Value, v1GetValue);
    }

    bool v2Value = false;
    bool v2GetValue;
    ret = GmcNodeGetPropertyByName(childNode, "V2", &v2GetValue, sizeof(v2Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    if (isDefaultValue) {
        EXPECT_TRUE(isNull);
    } else {
        EXPECT_FALSE(isNull);
        EXPECT_EQ(v2Value, v2GetValue);
    }

    float v3Value = (float)value;
    float v3GetValue;
    ret = GmcNodeGetPropertyByName(childNode, "V3", &v3GetValue, sizeof(v3Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    if (isDefaultValue) {
        EXPECT_TRUE(isNull);
    } else {
        EXPECT_FALSE(isNull);
        EXPECT_EQ(v3Value, v3GetValue);
    }
    double v4Value = (double)value;
    double v4GetValue;
    ret = GmcNodeGetPropertyByName(childNode, "V4", &v4GetValue, sizeof(v4Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    if (isDefaultValue) {
        EXPECT_TRUE(isNull);
    } else {
        EXPECT_FALSE(isNull);
        EXPECT_EQ(v4Value, v4GetValue);
    }

    uint64_t v5Value = (uint64_t)value;
    uint64_t v5GetValue;
    ret = GmcNodeGetPropertyByName(childNode, "V5", &v5GetValue, sizeof(v5Value), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    if (isDefaultValue) {
        EXPECT_TRUE(isNull);
    } else {
        EXPECT_FALSE(isNull);
        EXPECT_EQ(v5Value, v5GetValue);
    }

    char v6Value = 'a';
    char v6GetValue;
    ret = GmcNodeGetPropertyByName(childNode, "V6", &v6GetValue, sizeof(char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    if (isDefaultValue) {
        EXPECT_TRUE(isNull);
    } else {
        EXPECT_FALSE(isNull);
        EXPECT_EQ(v6Value, v6GetValue);
    }

    char v7Value = 'a';
    char v7GetValue;
    ret = GmcNodeGetPropertyByName(childNode, "V7", &v7GetValue, sizeof(char), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    if (isDefaultValue) {
        EXPECT_TRUE(isNull);
    } else {
        EXPECT_FALSE(isNull);
        EXPECT_EQ(v7Value, v7GetValue);
    }

    const char *v8Value = "stringtype";
    char v8GetValue[100];
    uint32_t v8ValueLen = 0;
    ret = GmcNodeGetPropertySizeByName(childNode, "V8", &v8ValueLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(childNode, (char *)"V8", v8GetValue, v8ValueLen, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    v8GetValue[10] = '\0';
    if (isDefaultValue) {
        EXPECT_FALSE(isNull);
        EXPECT_EQ(strcmp(v8GetValue, (char *)"djp"), 0);
    } else {
        EXPECT_FALSE(isNull);
        EXPECT_EQ(strcmp(v8Value, v8GetValue), 0);
    }

    const char *v9Value = "ABCDEFGHI";
    char v9GetValue[100];
    uint32_t v9ValueLen = 0;
    ret = GmcNodeGetPropertySizeByName(childNode, "V9", &v9ValueLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(childNode, (char *)"V9", v9GetValue, v9ValueLen, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    v9GetValue[9] = '\0';
    if (isDefaultValue) {
        EXPECT_TRUE(isNull);
    } else {
        EXPECT_FALSE(isNull);
        EXPECT_EQ(strcmp(v9Value, v9GetValue), 0);
    }

    uint8_t default10Value[10] = {255, 255, 255, 255, 255, 255, 255, 255, 255, 255};
    char v10GetValue[10];
    ret = GmcNodeGetPropertyByName(childNode, (char *)"V10", v10GetValue, 10, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    if (isDefaultValue) {
        EXPECT_EQ(ByteCompare(default10Value, (uint8_t *)v10GetValue), true);
    } else {
        EXPECT_EQ(ByteCompare((uint8_t *)"bytes", (uint8_t *)v10GetValue), true);
    }

    uint8_t default11Value = 31;
    uint8_t v11GetValue;
    ret = GmcNodeGetPropertyByName(childNode, (char *)"V11", &v11GetValue, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    if (isDefaultValue) {
        EXPECT_EQ(default11Value, v11GetValue);
    } else {
        EXPECT_EQ((uint8_t)1, v11GetValue);
    }

    uint16_t default12Value = 1023;
    uint16_t v12GetValue;
    ret = GmcNodeGetPropertyByName(childNode, (char *)"V12", &v12GetValue, sizeof(uint16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    if (isDefaultValue) {
        EXPECT_EQ(default12Value, v12GetValue);
    } else {
        EXPECT_EQ((uint16_t)1, v12GetValue);
    }

    uint32_t default13Value = 131071;
    uint32_t v13GetValue;
    ret = GmcNodeGetPropertyByName(childNode, (char *)"V13", &v13GetValue, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    if (isDefaultValue) {
        EXPECT_EQ(default13Value, v13GetValue);
    } else {
        EXPECT_EQ((uint32_t)1, v13GetValue);
    }

    uint64_t default14Value = 34359738367;
    uint64_t v14GetValue;
    ret = GmcNodeGetPropertyByName(childNode, (char *)"V14", &v14GetValue, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    if (isDefaultValue) {
        EXPECT_EQ(default14Value, v14GetValue);
    } else {
        EXPECT_EQ((uint64_t)1, v14GetValue);
    }
}

TEST_F(StClientDWMultiVersion, GeneralComplexVertexLabelConstrain)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "generalComplexLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v1.gmjson");
    string schemaV2 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v2.gmjson");

    // 建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), g_label_config));

    // 版本1，插入1-10
    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg1);

    // 用v2schema升级,升级成功
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV2.c_str(), true, vertexLabelName));

    // 版本2，插入11-20
    LabelOpCfgT cfg2;
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_INSERT;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg2);

    // 用版本1查数据成功
    LabelOpCfgT cfg3;
    cfg3.start = 1;
    cfg3.end = 20;
    cfg3.optType = GMC_OPERATION_SCAN;
    cfg3.versionId = 1;
    cfg3.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg3);

    bool isFinish = true;
    // 版本2查数据
    for (uint32_t i = 1; i <= 20; i++) {
        Status ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 2, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        // 设置主键
        int64_t keyValue = (int64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &keyValue, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // 取值然后进行比较
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *rootNode;
        // get根节点
        ret = GmcGetRootNode(stmt, &rootNode);
        EXPECT_EQ(GMERR_OK, ret);
        ReadAndCompareV1Data(rootNode, i, false);

        if (i <= 10) {
            ReadAndCompareV1V2DeltaData(rootNode, i, true);
        } else {
            ReadAndCompareV1V2DeltaData(rootNode, i, false);
        }
    }

    GmcDropVertexLabel(stmt, vertexLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDWMultiVersion, DwInsertGeneralComplexVertexLabel)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "generalComplexLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v1.gmjson");
    string schemaV2 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v2.gmjson");
    string schemaV3 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v3.gmjson");

    // 建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), g_label_config));

    // 版本1，插入1-10
    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg1);

    // 升级 v1->v2
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV2.c_str(), true, vertexLabelName));
    // 插入 11-20 成功
    LabelOpCfgT cfg2;
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_INSERT;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    // 再次插入 11-20 失败
    cfg2.start = 1;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_INSERT;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_PRIMARY_KEY_VIOLATION;
    cfg2.expAffectRows = 0;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg2);

    // 升级v2->v3
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, vertexLabelName));
    // 插入 21-30
    LabelOpCfgT cfg3;
    cfg3.start = 21;
    cfg3.end = 30;
    cfg3.optType = GMC_OPERATION_INSERT;
    cfg3.versionId = 3;
    cfg3.expExecuteStatus = GMERR_OK;
    cfg3.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg3);

    // 用版本3查数据成功
    LabelOpCfgT cfg4;
    cfg4.start = 1;
    cfg4.end = 30;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 降级v3->v2
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 2));
    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 30;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 版本2，插入31-40成功
    LabelOpCfgT cfg5;
    cfg5.start = 31;
    cfg5.end = 40;
    cfg5.optType = GMC_OPERATION_INSERT;
    cfg5.versionId = 2;
    cfg5.expExecuteStatus = GMERR_OK;
    cfg5.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg5);

    // 降级v2->v1
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 1));

    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 40;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据失败
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    GmcDropVertexLabel(stmt, vertexLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDWMultiVersion, DwReplaceGeneralComplexVertexLabel)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "generalComplexLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v1.gmjson");
    string schemaV2 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v2.gmjson");
    string schemaV3 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v3.gmjson");

    // 建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), g_label_config));

    // 版本1，插入1-10
    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg1);
    // 版本1，replace 1-10成功
    cfg1.optType = GMC_OPERATION_REPLACE;
    cfg1.expAffectRows = 2;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg1);

    // 升级 v1->v2
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV2.c_str(), true, vertexLabelName));
    // 插入 11-20 成功
    LabelOpCfgT cfg2;
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_REPLACE;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg2);

    // 升级v2->v3
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, vertexLabelName));
    // 插入 21-30
    LabelOpCfgT cfg3;
    cfg3.start = 21;
    cfg3.end = 30;
    cfg3.optType = GMC_OPERATION_REPLACE;
    cfg3.versionId = 3;
    cfg3.expExecuteStatus = GMERR_OK;
    cfg3.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg3);

    // 用版本3查数据成功
    LabelOpCfgT cfg4;
    cfg4.start = 1;
    cfg4.end = 30;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 降级v3->v2
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 2));
    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 30;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 版本2，插入31-40成功
    LabelOpCfgT cfg5;
    cfg5.start = 31;
    cfg5.end = 40;
    cfg5.optType = GMC_OPERATION_REPLACE;
    cfg5.versionId = 2;
    cfg5.expExecuteStatus = GMERR_OK;
    cfg5.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg5);

    // 降级v2->v1
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 1));

    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 40;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据失败
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    GmcDropVertexLabel(stmt, vertexLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDWMultiVersion, DwUpdateGeneralComplexVertexLabel)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "generalComplexLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v1.gmjson");
    string schemaV2 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v2.gmjson");
    string schemaV3 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v3.gmjson");

    // 建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), g_label_config));

    // 版本1，插入1-10
    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg1);
    // 版本1，update 1-10成功
    cfg1.optType = GMC_OPERATION_UPDATE;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg1);

    // 升级 v1->v2
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV2.c_str(), true, vertexLabelName));
    // 插入 11-20 成功
    LabelOpCfgT cfg2;
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_INSERT;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    // 版本2，update 11-20成功
    cfg2.optType = GMC_OPERATION_UPDATE;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    // 升级v2->v3
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, vertexLabelName));
    // update 21-30 失败
    LabelOpCfgT cfg3;
    cfg3.start = 21;
    cfg3.end = 30;
    cfg3.optType = GMC_OPERATION_UPDATE;
    cfg3.versionId = 3;
    // execute返回GMERR_OK，但是affectRows为0
    cfg3.expExecuteStatus = GMERR_OK;
    cfg3.expAffectRows = 0;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg3);

    // 用版本3查数据成功
    LabelOpCfgT cfg4;
    cfg4.start = 1;
    cfg4.end = 20;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 降级v3->v2
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 2));

    // update 11-20 成功
    LabelOpCfgT cfg5;
    cfg5.start = 11;
    cfg5.end = 20;
    cfg5.optType = GMC_OPERATION_UPDATE;
    cfg5.versionId = 2;
    cfg5.expExecuteStatus = GMERR_OK;
    cfg5.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg5);

    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 20;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 降级v2->v1
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 1));

    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 20;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据失败
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    GmcDropVertexLabel(stmt, vertexLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDWMultiVersion, DwSecIdxUpdateGeneralComplexVertexLabel)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "generalComplexLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v1.gmjson");
    string schemaV2 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v2.gmjson");
    string schemaV3 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v3.gmjson");

    // 建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), g_label_config));

    // 版本1，插入1-10
    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    cfg1.deltaData = 0;
    cfg1.isSecIdxUpdate = true;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg1);
    // 版本1，update 1-10成功
    cfg1.optType = GMC_OPERATION_UPDATE;
    cfg1.deltaData = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg1);

    // 升级 v1->v2
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV2.c_str(), true, vertexLabelName));
    // 插入 11-20 成功
    LabelOpCfgT cfg2;
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_INSERT;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    cfg2.deltaData = 0;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    // 版本2，update 11-20成功
    cfg2.optType = GMC_OPERATION_UPDATE;
    cfg2.deltaData = 1;
    cfg2.isSecIdxUpdate = true;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    // 升级v2->v3
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, vertexLabelName));
    // update 21-30 失败
    LabelOpCfgT cfg3;
    cfg3.start = 21;
    cfg3.end = 30;
    cfg3.optType = GMC_OPERATION_UPDATE;
    cfg3.deltaData = 1;
    cfg3.isSecIdxUpdate = true;
    cfg3.versionId = 3;
    // execute返回GMERR_OK，但是affectRows为0
    cfg3.expExecuteStatus = GMERR_OK;
    cfg3.expAffectRows = 0;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg3);

    // 降级v3->v2
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 2));

    // update 11-20 成功
    LabelOpCfgT cfg5;
    cfg5.start = 11;
    cfg5.end = 20;
    cfg5.optType = GMC_OPERATION_UPDATE;
    cfg5.deltaData = 1;
    cfg5.isSecIdxUpdate = true;
    cfg5.versionId = 2;
    cfg5.expExecuteStatus = GMERR_OK;
    cfg5.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg5);

    GmcDropVertexLabel(stmt, vertexLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDWMultiVersion, DwDeleteGeneralComplexVertexLabel)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "generalComplexLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v1.gmjson");
    string schemaV2 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v2.gmjson");
    string schemaV3 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v3.gmjson");

    // 建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), g_label_config));

    // 版本1，插入1-10
    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg1);
    // 版本1，delete 6-10成功
    cfg1.start = 6;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_DELETE;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg1);

    // 升级 v1->v2
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV2.c_str(), true, vertexLabelName));
    // 插入 11-20 成功
    LabelOpCfgT cfg2;
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_INSERT;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    // 版本2，delete 11-20成功
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_DELETE;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    cfg2.expAffectRows = 0;

    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    // 升级v2->v3
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, vertexLabelName));

    // 版本3，插入 11-20 成功
    LabelOpCfgT cfg3;
    cfg3.start = 11;
    cfg3.end = 20;
    cfg3.optType = GMC_OPERATION_INSERT;
    cfg3.versionId = 2;
    cfg3.expExecuteStatus = GMERR_OK;
    cfg3.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg3);
    // 版本3，delete 11-20成功
    cfg3.start = 11;
    cfg3.end = 20;
    cfg3.optType = GMC_OPERATION_DELETE;
    cfg3.versionId = 2;
    cfg3.expExecuteStatus = GMERR_OK;
    cfg3.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg3);

    // 用版本3查数据成功
    LabelOpCfgT cfg4;
    cfg4.start = 1;
    cfg4.end = 5;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 降级v3->v2
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 2));

    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 5;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // delete 1-2 成功
    LabelOpCfgT cfg5;
    cfg5.start = 1;
    cfg5.end = 2;
    cfg5.optType = GMC_OPERATION_DELETE;
    cfg5.versionId = 2;
    cfg5.expExecuteStatus = GMERR_OK;
    cfg5.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg5);

    // 降级v2->v1
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 1));

    // 用版本3查数据失败
    cfg4.start = 3;
    cfg4.end = 5;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据失败
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    GmcDropVertexLabel(stmt, vertexLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

#define STRUCT_VAR_SIZE (sizeof(uint16_t) + sizeof(uint8_t *))
const uint32_t MAX_KEY_FIELD_NUM = 8;
const uint32_t INIT_STACK_SIZE = 20;
const uint32_t DEFAULT_STR_LEN = 8;

struct AlterStructTestCtx {
    bool isKey;
    DmVertexTypeE type;
    int value;
    int vertexSize;
    char *objAddr;
    int objSize;
    GmcStmtT *stmt;
};

struct StructTestCtx {
    bool haveBitmap;
    bool isFixKey;
    bool useExternalMem;
    int value;
    uint32_t keyId;
    uint32_t vertexSize;
    uint32_t keySize;
    uint32_t idx;
    uint32_t useSize;
    uint32_t totalSize;
    uint32_t memMax;
    uint32_t memSize;
    void **memAddr;
    GmcStmtT *stmt;
    uint32_t *lenStack;
    uint32_t initStack[INIT_STACK_SIZE];
    bool *keyNullInfo;
};

#pragma pack(1)
struct Ip4forwordT2 {
    uint32_t vr_id;
    uint32_t vrf_index;
    uint32_t dest_ip_addr;
    uint16_t strSize;
    char *originString;
};
#pragma pack()

#pragma pack(1)
struct Ip4forwordTUpd {
    uint32_t vr_id;
    uint32_t vrf_index;
    uint32_t dest_ip_addr;
    uint16_t strSize;
    char *originString;
    uint16_t newStrSize;
    char *newString;
};
#pragma pack()

static void SetValueIntoStruct(Ip4forwordT2 *d, int value)
{
    d->vr_id = 0;
    d->vrf_index = 1;
    d->dest_ip_addr = value;

    d->strSize = DEFAULT_STR_LEN;
    char *buf = (char *)DbDynMemCtxAlloc(alterMemCtx, DEFAULT_STR_LEN);
    (void)memset_s(buf, DEFAULT_STR_LEN, 0, DEFAULT_STR_LEN);
    d->originString = buf;
    char str[] = "1234567";
    (void)memcpy_s(d->originString, DEFAULT_STR_LEN, str, DEFAULT_STR_LEN);
}

static void SetValueIntoUpdStruct(Ip4forwordTUpd *d, int value)
{
    d->vr_id = 2;
    d->vrf_index = 3;
    d->dest_ip_addr = value;

    d->strSize = DEFAULT_STR_LEN;
    char *buf = (char *)DbDynMemCtxAlloc(alterMemCtx, DEFAULT_STR_LEN);
    (void)memset_s(buf, DEFAULT_STR_LEN, 0, DEFAULT_STR_LEN);
    d->originString = buf;
    char str[] = "abc1234";
    (void)memcpy_s(d->originString, DEFAULT_STR_LEN, str, DEFAULT_STR_LEN);

    d->newStrSize = DEFAULT_STR_LEN;
    char *buf2 = (char *)DbDynMemCtxAlloc(alterMemCtx, DEFAULT_STR_LEN);
    (void)memset_s(buf2, DEFAULT_STR_LEN, 0, DEFAULT_STR_LEN);
    d->newString = buf2;
    char value2[] = "abcdefg";
    (void)memcpy_s(d->newString, DEFAULT_STR_LEN, value2, DEFAULT_STR_LEN);
}

static void SetValueIntoUpdStruct2(Ip4forwordTUpd *d, int value)
{
    d->vr_id = 0;
    d->vrf_index = 77;
    d->dest_ip_addr = value;

    d->strSize = DEFAULT_STR_LEN;
    char *buf = (char *)DbDynMemCtxAlloc(alterMemCtx, DEFAULT_STR_LEN);
    (void)memset_s(buf, DEFAULT_STR_LEN, 0, DEFAULT_STR_LEN);
    d->originString = buf;
    char str[] = "aaaaaaa";
    (void)memcpy_s(d->originString, DEFAULT_STR_LEN, str, DEFAULT_STR_LEN);

    d->newStrSize = DEFAULT_STR_LEN;
    char *buf2 = (char *)DbDynMemCtxAlloc(alterMemCtx, DEFAULT_STR_LEN);
    (void)memset_s(buf2, DEFAULT_STR_LEN, 0, DEFAULT_STR_LEN);
    d->newString = buf2;
    char value2[] = "bbbbbbb";
    (void)memcpy_s(d->newString, DEFAULT_STR_LEN, value2, DEFAULT_STR_LEN);
}

static int GetBitFieldSize(DmPropertySchemaT *p, uint32_t *begin, uint32_t end)
{
    uint32_t index = *begin;
    int sum = 0;
    if (p[index].dataType == DB_DATATYPE_BITFIELD8) {
        sum = 1;
    } else if (p[index].dataType == DB_DATATYPE_BITFIELD16) {
        sum = 2;
    } else if (p[index].dataType == DB_DATATYPE_BITFIELD32) {
        sum = 4;
    } else if (p[index].dataType == DB_DATATYPE_BITFIELD64) {
        sum = 8;
    } else {
        DB_ASSERT(0);
    }
    uint8_t bitfieldOffset = p[index].bitfieldOffset;
    DbDataTypeE dataType = p[index].dataType;
    for (index = index + 1; index < end; ++index) {
        if (p[index].dataType == dataType && p[index].bitfieldOffset > bitfieldOffset) {
            bitfieldOffset = p[index].bitfieldOffset;
            continue;
        }
        break;
    }
    *begin = index - 1;
    return sum;
}

static void SeriVertexRecord(
    GmcSeriT *s, DmSchemaT *schema, uint8_t *srcBuf, uint8_t **destBuf, bool isRoot = true, uint32_t *size = NULL)
{
    StructTestCtx *c = (StructTestCtx *)s->userData;
    uint32_t newVal, newSize;
    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(c->stmt);
    if (cltCataLabel->vertexLabel->vertexDesc->vertexType != DM_FIXED_VERTEX) {
        DmConvertUint32ToVarint(c->lenStack[c->idx++], &newVal, &newSize);
        *(uint32_t *)*destBuf = newVal;
        *destBuf += newSize;
    }
    // fixed propertires
    int fixedLen = c->lenStack[c->idx++];
    DmConvertUint32ToVarint(fixedLen, &newVal, &newSize);
    *(uint32_t *)*destBuf = newVal;
    *destBuf += newSize;
    uint32_t propNum = schema->propeNum;
    if (isRoot) {
        if (fixedLen > 1) {
            (void)memcpy_s(*destBuf, fixedLen - 1, srcBuf, fixedLen - 1);
        }
        *(*destBuf + fixedLen - 1) = 0;
        propNum -= 1;
    } else {
        if (fixedLen > 0) {
            (void)memcpy_s(*destBuf, fixedLen, srcBuf, fixedLen);
        }
    }
    *destBuf += fixedLen;
    // nullable
    DmConvertUint32ToVarint(schema->propeNum, &newVal, &newSize);
    *(uint32_t *)*destBuf = newVal;
    *destBuf += newSize;
    uint32_t index;
    uint32_t index1;
    uint32_t index2 = (schema->propeNum + 7) / 8;
    uint8_t *nullableInfo = *destBuf;
    // init, all field are valid
    for (index = 0; index < propNum; ++index) {
        DmSetBitTrueIntoUint8Arr(*destBuf, index);
    }
    *destBuf += index2;
    if (cltCataLabel->vertexLabel->vertexDesc->vertexType == DM_FIXED_VERTEX) {
        return;
    }
    // var fields
    if (isRoot) {
        --fixedLen;
    }
    int structSize = fixedLen;
    int32_t nodeId = -1;
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    for (index = 0; index < schema->propeNum; ++index) {
        if (properties[index].dataType == DB_DATATYPE_BITFIELD8 ||
            properties[index].dataType == DB_DATATYPE_BITFIELD16 ||
            properties[index].dataType == DB_DATATYPE_BITFIELD32 ||
            properties[index].dataType == DB_DATATYPE_BITFIELD64) {
            continue;
        }
        if (!properties[index].isFixed) {
            break;
        }
        if (!properties[index].isValid) {
            nodeId = index;
            break;
        }
    }
    uint8_t **varAddr = NULL;
    for (index1 = index; index1 < schema->propeNum; ++index1) {
        if (!properties[index1].isValid) {
            if (nodeId == -1) {
                nodeId = index1;
            }
            break;
        }
        if (properties[index1].isFixed) {
            break;
        }
        structSize += STRUCT_VAR_SIZE;
        index2 = *(uint16_t *)(srcBuf + fixedLen + (index1 - index) * STRUCT_VAR_SIZE);
        DmConvertUint32ToVarint(index2, &newVal, &newSize);
        *(uint32_t *)*destBuf = newVal;
        *destBuf += newSize;
        if (index2 > 0) {
            varAddr = (uint8_t **)(srcBuf + fixedLen + (index1 - index) * STRUCT_VAR_SIZE + sizeof(uint16_t));
            (void)memcpy_s(*destBuf, index2, *varAddr, index2);
            *destBuf += index2;
        }
    }
    // nullable for node
    if (nodeId == -1 && schema->nodeNum > 0) {
        printf("[SeriVertexRecord] warn: schema->nodeNum = %d, but none node found.\n", schema->nodeNum);
    }
    uint8_t *nodeBase = srcBuf + fixedLen + (index1 - index) * STRUCT_VAR_SIZE;
    uint8_t *nodeLen;
    for (index = 0; index < schema->nodeNum; ++index) {
        nodeLen = nodeBase + index * STRUCT_VAR_SIZE;
        varAddr = (uint8_t **)(nodeBase + index * STRUCT_VAR_SIZE + sizeof(uint16_t));
        if (*(uint16_t *)nodeLen == 0 || *varAddr == NULL) {
            nullableInfo[nodeId >> 3] |=
                (uint8_t)(1 << (7 - (nodeId & 7)));  // 将空的node的nullableInfo位置0, 实际此处置1, 待修改
            ++nodeId;
        }
    }
    if (size) {
        *size = structSize + STRUCT_VAR_SIZE * schema->nodeNum;
    }
}

static void SeriVertexSubNode(GmcSeriT *s, DmSchemaT *schema, uint8_t *srcBuf, uint8_t **destBuf, bool isRoot = true)
{
    uint32_t i, j, newValue, newSize, elementNum, offset = 0;
    // nodeNum
    DmConvertUint32ToVarint(schema->nodeNum, &newValue, &newSize);
    *(uint32_t *)*destBuf = newValue;
    *destBuf += newSize;
    if (schema->nodeNum == 0) {
        return;
    }
    uint8_t *nodeBase = srcBuf;
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    for (i = 0; i < schema->propeNum; ++i) {
        if (properties[i].dataType == DB_DATATYPE_BITFIELD8 || properties[i].dataType == DB_DATATYPE_BITFIELD16 ||
            properties[i].dataType == DB_DATATYPE_BITFIELD32 || properties[i].dataType == DB_DATATYPE_BITFIELD64) {
            nodeBase += GetBitFieldSize(properties, &i, schema->propeNum);
            continue;
        }
        if (properties[i].dataType == DB_DATATYPE_BITMAP) {
            nodeBase += (properties[i].size / 8);
            continue;
        }
        if (!properties[i].isValid) {
            break;
        }
        if (properties[i].isFixed) {
            nodeBase += properties[i].size;
        } else {
            nodeBase += STRUCT_VAR_SIZE;
        }
    }
    // nodes
    uint8_t *nodeLen = NULL;
    uint8_t **nodeAddr = NULL;
    DmNodeSchemaT *nodes = MEMBER_PTR(schema, nodes);
    for (i = 0; i < schema->nodeNum; i++) {
        *(uint32_t *)*destBuf = 0xff;
        *destBuf += 1;  // isCreated标记
        nodeLen = nodeBase + i * STRUCT_VAR_SIZE;
        elementNum = *(uint16_t *)nodeLen;
        nodeAddr = (uint8_t **)(nodeLen + sizeof(uint16_t));
        DmConvertUint32ToVarint(elementNum, &newValue, &newSize);
        *(uint32_t *)*destBuf = newValue;
        *destBuf += newSize;
        if (*nodeAddr == NULL) {
            continue;
        }
        for (j = 0; j < elementNum; ++j) {
            DmSchemaT *nodeSchema = MEMBER_PTR(&nodes[i], schema);
            SeriVertexRecord(s, nodeSchema, *nodeAddr + offset * j, destBuf, false, &offset);
            SeriVertexSubNode(s, nodeSchema, *nodeAddr + offset * j, destBuf, false);
        }
    }
    return;
}

static Status SeriStructVertex(void *seri, uint8_t *destBuf, GmcStructureResvT *reservedSize)
{
    GmcSeriT *s = (GmcSeriT *)seri;
    StructTestCtx *c = (StructTestCtx *)s->userData;
    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(c->stmt);
    DmSchemaT *schema = MEMBER_PTR(cltCataLabel->vertexLabel->metaVertexLabel, schema);
    c->idx = 0;
    // vertex type
    *(uint8_t *)destBuf = cltCataLabel->vertexLabel->vertexDesc->vertexType;
    if (c->haveBitmap) {
        *(uint8_t *)destBuf = *(uint8_t *)destBuf | (uint8_t)0x10;
    }
    // tree, vertext length
    uint8_t *buf = (uint8_t *)destBuf + 1;
    uint32_t newVal, newSize;
    if (cltCataLabel->vertexLabel->vertexDesc->vertexType == DM_TREE_VERTEX) {
        DmConvertUint32ToVarint(s->bufSize, &newVal, &newSize);
        *(uint32_t *)buf = newVal;
        buf += newSize;
    }
    SeriVertexRecord(s, schema, s->obj, &buf);
    if (cltCataLabel->vertexLabel->vertexDesc->vertexType == DM_FIXED_VERTEX ||
        cltCataLabel->vertexLabel->vertexDesc->vertexType == DM_FLAT_VERTEX) {
        return GMERR_OK;
    }
    SeriVertexSubNode(s, schema, s->obj, &buf, false);
    return GMERR_OK;
}

static void DeSeriFreeDynMem(StructTestCtx *c, bool isAll = false)
{
    if (c->totalSize > INIT_STACK_SIZE) {
        if (c->lenStack) {
            DbDynMemCtxFree(alterMemCtx, c->lenStack);
        }
        c->lenStack = c->initStack;
        c->totalSize = INIT_STACK_SIZE;
    }
    if (c->useExternalMem) {
        return;
    }
    if (c->memAddr == NULL) {
        c->memMax = 0;
        c->memSize = 0;
        return;
    }
    for (uint32_t i = 0; i < c->memSize; ++i) {
        if (c->memAddr[i]) {
            DbDynMemCtxFree(alterMemCtx, c->memAddr[i]);
            c->memAddr[i] = NULL;
        }
    }
    c->memSize = 0;
    if (isAll) {
        DbDynMemCtxFree(alterMemCtx, c->memAddr);
        c->memAddr = NULL;
        c->memMax = 0;
    }
}

static void ExtendLenStack(StructTestCtx *c)
{
    if (c->totalSize == 0) {
        c->totalSize = INIT_STACK_SIZE;
        c->lenStack = c->initStack;
        return;
    }
    if (c->useSize < c->totalSize) {
        return;
    }
    uint32_t stepSize;
    if (c->totalSize < 20000) {
        stepSize = c->totalSize * 10;
    } else {
        stepSize = c->totalSize + 10000;
    }
    uint32_t *newVal = (uint32_t *)DbDynMemCtxAlloc(alterMemCtx, stepSize * sizeof(uint32_t));
    if (newVal == NULL) {
        printf("ExtendLenStack(from %u to %u) failed.\n", c->totalSize, stepSize);
        DB_ASSERT(0);
    }
    (void)memset_s(newVal, sizeof(uint32_t) * stepSize, 0, sizeof(uint32_t) * stepSize);
    (void)memcpy_s(newVal, sizeof(uint32_t) * c->totalSize, c->lenStack, sizeof(uint32_t) * c->totalSize);
    if (c->totalSize > INIT_STACK_SIZE) {  // 第一次扩展时, 使用的是栈变量, 不需要free
        DbDynMemCtxFree(alterMemCtx, c->lenStack);  // 释放上一次内存, 最后一次要用户手动调用deSeriFreeDynMem释放
    }
    c->lenStack = newVal;
    c->totalSize = stepSize;
}

static int GetRecordSize(GmcSeriT *s, DmSchemaT *schema, uint8_t *addr, bool isRoot = true, uint32_t *size = NULL)
{
    StructTestCtx *c = (StructTestCtx *)s->userData;
    uint32_t newVal, newSize;
    int id = -1;
    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(c->stmt);
    if (cltCataLabel->vertexLabel->vertexDesc->vertexType != DM_FIXED_VERTEX) {
        ExtendLenStack(c);
        id = c->useSize++;
    }
    // fixed properties
    uint32_t i, fieldLen, fixedLen = 0;
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    for (i = 0; i < schema->propeNum; ++i) {
        if (properties[i].dataType == DB_DATATYPE_BITFIELD8 || properties[i].dataType == DB_DATATYPE_BITFIELD16 ||
            properties[i].dataType == DB_DATATYPE_BITFIELD32 || properties[i].dataType == DB_DATATYPE_BITFIELD64) {
            fixedLen += GetBitFieldSize(properties, &i, schema->propeNum);
            continue;
        }
        if (properties[i].dataType == DB_DATATYPE_BITMAP) {
            c->haveBitmap = true;
            fixedLen += (properties[i].size / 8);
            continue;
        }
        if (!properties[i].isFixed || !properties[i].isValid) {
            break;
        }
        fixedLen += properties[i].size;
    }
    fieldLen = fixedLen;
    if (isRoot) {
        if (i < schema->propeNum) {
            fieldLen += 1;  // sys_version
        } else {
            fixedLen -= 1;  // the offset of struct
        }
    }
    uint32_t structSize = fixedLen;
    // the length of fixed properties
    DmConvertUint32ToVarint(fieldLen, &newVal, &newSize);
    int sum = fieldLen + newSize;
    ExtendLenStack(c);
    c->lenStack[c->useSize++] = fieldLen;  // fixedLen
    // propNum
    DmConvertUint32ToVarint(schema->propeNum, &newVal, &newSize);
    sum += newSize;
    // nullable
    sum += (schema->propeNum + 7) / 8;
    if (cltCataLabel->vertexLabel->vertexDesc->vertexType == DM_FIXED_VERTEX) {
        return sum;
    }
    int j = i;

    for (; i < schema->propeNum; ++i) {
        if (!properties[i].isValid || properties[i].isFixed) {
            break;
        }
        structSize += STRUCT_VAR_SIZE;
        fieldLen = *(uint16_t *)(addr + fixedLen + (i - j) * STRUCT_VAR_SIZE);
        DmConvertUint32ToVarint(fieldLen, &newVal, &newSize);
        sum += newSize;
        sum += fieldLen;
    }
    if (size) {
        // 结构体偏移一整个数组元素(定长 + 变长 + node), 偏移到下一个元素开头
        *size = structSize + STRUCT_VAR_SIZE * schema->nodeNum;
    }
    DmConvertUint32ToVarint(sum, &newVal, &newSize);
    sum += newSize;
    c->lenStack[id] = sum;  // 定长 + 变长 + recordLen
    return sum;
}

// ------>> inner funtion
static int GetSubNodeSize(GmcSeriT *s, DmSchemaT *schema, uint8_t *addr)
{
    uint32_t i, j, newSize, offset = 0;
    // nodeNum
    DmGetVarintLength(schema->nodeNum, &newSize);
    int sum = newSize;
    if (schema->nodeNum == 0) {
        return sum;
    }
    uint8_t *nodeBase = addr;
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    for (i = 0; i < schema->propeNum; ++i) {
        if (properties[i].dataType == DB_DATATYPE_BITFIELD8 || properties[i].dataType == DB_DATATYPE_BITFIELD16 ||
            properties[i].dataType == DB_DATATYPE_BITFIELD32 || properties[i].dataType == DB_DATATYPE_BITFIELD64) {
            nodeBase += GetBitFieldSize(properties, &i, schema->propeNum);
            continue;
        }
        if (properties[i].dataType == DB_DATATYPE_BITMAP) {
            nodeBase += (properties[i].size / 8);
            continue;
        }
        if (!properties[i].isValid) {
            break;
        }
        if (properties[i].isFixed) {
            nodeBase += properties[i].size;
        } else {
            nodeBase += STRUCT_VAR_SIZE;
        }
    }
    uint32_t elementNum;
    uint8_t *buf = NULL;
    uint8_t **nodeAddr = NULL;
    DmNodeSchemaT *nodes = MEMBER_PTR(schema, nodes);
    for (i = 0; i < schema->nodeNum; i++) {
        buf = nodeBase + i * STRUCT_VAR_SIZE;
        elementNum = *(uint16_t *)buf;
        nodeAddr = (uint8_t **)(buf + sizeof(uint16_t));
        DmGetVarintLength(elementNum, &newSize);
        sum += newSize;
        if (*nodeAddr == NULL) {
            continue;
        }
        for (j = 0; j < elementNum; ++j) {
            DmSchemaT *nodeSchema = MEMBER_PTR(&nodes[i], schema);
            sum += GetRecordSize(s, nodeSchema, *nodeAddr + offset * j, false, &offset);
            sum += GetSubNodeSize(s, nodeSchema, *nodeAddr + offset * j);
        }
        sum++;  // isCreated标记
    }
    return sum;
}

// ######## Entry funtion 3: GetSerialVertexLength
static void GetSerialVertexLength(GmcSeriT *s)
{
    StructTestCtx *c = (StructTestCtx *)s->userData;
    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(c->stmt);
    if (cltCataLabel->vertexLabel->vertexDesc->vertexType == DM_FIXED_VERTEX && c->vertexSize > 0) {
        s->bufSize = c->vertexSize;
        return;
    }
    c->useSize = 0;
    DmSchemaT *schema = MEMBER_PTR(cltCataLabel->vertexLabel->metaVertexLabel, schema);
    uint8_t *addr = s->obj;
    // vertextType
    c->vertexSize = 1;
    // root node
    c->vertexSize += GetRecordSize(s, schema, addr);
    s->bufSize = c->vertexSize;
    if (cltCataLabel->vertexLabel->vertexDesc->vertexType == DM_FIXED_VERTEX ||
        cltCataLabel->vertexLabel->vertexDesc->vertexType == DM_FLAT_VERTEX) {
        return;
    }
    // leaflist, for yangdb, vertex->vertexDesc->listPropeNum = 0
    c->vertexSize += 1;
    // sub nodes
    c->vertexSize += GetSubNodeSize(s, schema, addr);
    uint32_t newSize;
    DmGetVarintLength(c->vertexSize, &newSize);
    c->vertexSize += newSize;
    s->bufSize = c->vertexSize;
}

static void ExtendDynArray(StructTestCtx *c)
{
    if (c->memSize < c->memMax) {
        return;
    }
    uint32_t stepSize;
    if (c->memMax == 0) {
        stepSize = 10;
    } else {
        stepSize = c->memMax * 2;
    }
    if (stepSize <= 0) {
        return;
    }
    void **newAddr = (void **)DbDynMemCtxAlloc(alterMemCtx, sizeof(void *) * stepSize);
    if (newAddr == NULL) {
        printf("ExtendDynArray(from %u to %u) failed.\n", c->memMax, stepSize);
        DB_ASSERT(0);
    }
    (void)memset_s(newAddr + c->memMax, sizeof(void *) * c->memMax, 0, sizeof(void *) * c->memMax);
    if (c->memMax > 0) {
        (void)memcpy_s(newAddr, sizeof(void *) * c->memMax, c->memAddr, sizeof(void *) * c->memMax);
        DbDynMemCtxFree(alterMemCtx, c->memAddr);
    }
    c->memAddr = newAddr;
    c->memMax = stepSize;
}

static void DeSeriStructRecord(GmcDeseriT *d, DmSchemaT *schema, const uint8_t *srcBuf, uint32_t *sum, uint8_t *data)
{
    StructTestCtx *c = (StructTestCtx *)d->userData;
    uint32_t i, totalLen, fixedLen, length, offset;
    DmConvertVarintToUint32(*(uint32_t *)(srcBuf + *sum), &totalLen, &offset);
    const uint8_t *addrEnd = srcBuf + *sum + totalLen;
    *sum += offset;
    DmConvertVarintToUint32(*(uint32_t *)(srcBuf + *sum), &fixedLen, &offset);
    *sum += offset;
    if (fixedLen > 0) {
        (void)memcpy_s(data, fixedLen, srcBuf + *sum, fixedLen);
        *sum += fixedLen;
    }
    DmConvertVarintToUint32(*(uint32_t *)(srcBuf + *sum), &length, &offset);
    *sum += offset;
    *sum += (length + 7) / 8;
    uint8_t *addrLen = NULL;
    uint8_t **addrVal = NULL;
    i = 0;
    while (srcBuf + *sum < addrEnd) {
        DmConvertVarintToUint32(*(uint32_t *)(srcBuf + *sum), &length, &offset);
        *sum += offset;
        addrLen = data + fixedLen + i * STRUCT_VAR_SIZE;
        *(uint16_t *)addrLen = length;
        addrVal = (uint8_t **)(addrLen + sizeof(uint16_t));
        if (!c->useExternalMem) {
            if ((length + 1) < 1) {
                return;
            }
            *addrVal = (uint8_t *)DbDynMemCtxAlloc(alterMemCtx, length + 1);
            if (*addrVal == NULL) {
                printf("[DeSeriStructRecord] warn: DbMalloc memory(%u) failed.\n", length + 1);
                return;
            }
            ExtendDynArray(c);
            c->memAddr[c->memSize++] = *addrVal;
        }
        (void)memcpy_s(*addrVal, length, srcBuf + *sum, length);
        *(*addrVal + length) = 0;
        *sum += length;
        ++i;
    }
}

static void DeSeriStructSubNode(GmcDeseriT *d, DmSchemaT *schema, const uint8_t *srcBuf, uint32_t *sum, uint8_t *data)
{
    StructTestCtx *c = (StructTestCtx *)d->userData;
    uint32_t i, j, size, nodeNum, elementNum, newSize;
    DmConvertVarintToUint32(*(uint32_t *)(srcBuf + *sum), &nodeNum, &newSize);
    *sum += newSize;
    if (nodeNum != schema->nodeNum) {
        printf("[DeSeriStructSubNode] warn: schema->nodeNum = %" PRIu32 ", msg->nodeNum = %" PRIu32 ".\n", nodeNum,
            schema->nodeNum);
    }
    if (nodeNum == 0) {
        return;
    }
    uint8_t *nodeBase = data;
    for (i = 0; i < schema->propeNum; ++i) {
        if (!schema->properties[i].isValid) {
            break;
        }
        if (schema->properties[i].dataType == DB_DATATYPE_BITFIELD8 ||
            schema->properties[i].dataType == DB_DATATYPE_BITFIELD16 ||
            schema->properties[i].dataType == DB_DATATYPE_BITFIELD32 ||
            schema->properties[i].dataType == DB_DATATYPE_BITFIELD64) {
            nodeBase += GetBitFieldSize(schema->properties, &i, schema->propeNum);
            continue;
        }
        if (schema->properties[i].dataType == DB_DATATYPE_BITMAP) {
            nodeBase += (schema->properties[i].size / 8);
            continue;
        }
        if (schema->properties[i].isFixed) {
            nodeBase += schema->properties[i].size;
        } else {
            nodeBase += STRUCT_VAR_SIZE;
        }
    }
    uint8_t *nodeLen;
    uint8_t **nodeAddr;
    DmSchemaT *s;
    for (i = 0; i < nodeNum; ++i) {
        DmConvertVarintToUint32(*(uint32_t *)(srcBuf + *sum + 1), &elementNum, &newSize);
        *sum += newSize;
        *sum += 1;  // isCreated标记
        nodeLen = nodeBase + i * STRUCT_VAR_SIZE;
        nodeAddr = (uint8_t **)(nodeLen + sizeof(uint16_t));
        *(uint16_t *)nodeLen = elementNum;
        if (elementNum == 0) {
            *nodeAddr = NULL;
            continue;
        }
        s = schema->nodes[i].schema;
        size = 0;
        for (j = 0; j < s->propeNum; ++j) {
            if (!s->properties[j].isValid) {
                size += STRUCT_VAR_SIZE;
            }
            if (s->properties[j].dataType == DB_DATATYPE_BITFIELD8 ||
                s->properties[j].dataType == DB_DATATYPE_BITFIELD16 ||
                s->properties[j].dataType == DB_DATATYPE_BITFIELD32 ||
                s->properties[j].dataType == DB_DATATYPE_BITFIELD64) {
                size += GetBitFieldSize(s->properties, &j, s->propeNum);
                continue;
            }
            if (s->properties[j].dataType == DB_DATATYPE_BITMAP) {
                size += (s->properties[j].size / 8);
                continue;
            }
            if (s->properties[j].isFixed) {
                size += s->properties[j].size;
            } else {
                size += STRUCT_VAR_SIZE;
            }
        }
        if (!c->useExternalMem) {
            if ((size * elementNum) <= 0) {
                return;
            }
            *nodeAddr = (uint8_t *)DbDynMemCtxAlloc(alterMemCtx, size * elementNum);
            if (*nodeAddr == NULL) {
                printf(
                    "[DeSeriStructSubNode] warn: DbMalloc memory for nodeAddr failed, elementNum = %u, elementSize = "
                    "%u.\n",
                    elementNum, size);
                return;
            }
            ExtendDynArray(c);
            c->memAddr[c->memSize++] = *nodeAddr;
        }
        for (j = 0; j < elementNum; ++j) {
            DeSeriStructRecord(d, s, srcBuf, sum, *nodeAddr + size * j);
            DeSeriStructSubNode(d, s, srcBuf, sum, *nodeAddr + size * j);
        }
    }
}

// ######## Entry funtion 6: DeSeriStructVertex 该函数只适合非Tree表
static Status DeSeriStructVertex(void *deSeri, const uint8_t *srcBuf, uint32_t srcLen, GmcStructureResvT *reservedSize)
{
    GmcDeseriT *d = (GmcDeseriT *)deSeri;
    StructTestCtx *c = (StructTestCtx *)d->userData;
    uint32_t i, length, offset, totalLen, fixedLen;
    DmVertexT *vertex = CltGetVertexInStmt(c->stmt);
    if (vertex->vertexDesc->vertexType == DM_TREE_VERTEX) {
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    if (vertex->vertexDesc->vertexType == DM_FIXED_VERTEX) {
        DmConvertVarintToUint32(*(uint32_t *)(srcBuf + 1), &length, &offset);
        (void)memcpy_s(d->obj, length - 1, srcBuf + offset + 1, length - 1);
        return GMERR_OK;
    }
    // free dynamic memory
    DeSeriFreeDynMem(c);
    // var fields, tree model
    uint8_t *addrLen = NULL;
    uint8_t **addrVal = NULL;
    const uint8_t *addrEnd = NULL;
    uint32_t sum = 1;
    DmConvertVarintToUint32(*(uint32_t *)(srcBuf + sum), &totalLen, &offset);
    addrEnd = srcBuf + sum + totalLen;
    sum += offset;
    DmConvertVarintToUint32(*(uint32_t *)(srcBuf + sum), &fixedLen, &offset);
    sum += offset;
    if (fixedLen > 1) {
        (void)memcpy_s(d->obj, fixedLen - 1, srcBuf + sum, fixedLen - 1);
    }
    sum += fixedLen;
    --fixedLen;
    DmConvertVarintToUint32(*(uint32_t *)(srcBuf + sum), &length, &offset);
    sum += offset;
    sum += (length + 7) / 8;
    i = 0;
    while (srcBuf + sum < addrEnd) {
        DmConvertVarintToUint32(*(uint32_t *)(srcBuf + sum), &length, &offset);
        sum += offset;
        // 老版本读新版本数据时，需要终止往后读。
        // 当前只考虑了定长+变长的情况，不包含子节点，如果后续加了子节点，需要重新计算objSize，适配反序列化逻辑。
        if (d->objSize <= fixedLen + i * STRUCT_VAR_SIZE) {
            break;
        }
        addrLen = d->obj + fixedLen + i * STRUCT_VAR_SIZE;
        *(uint16_t *)addrLen = (uint16_t)length;
        addrVal = (uint8_t **)(addrLen + sizeof(uint16_t));
        if (!c->useExternalMem) {
            if ((length + 1) <= 0) {
                return GMERR_DATA_EXCEPTION;
            }
            *addrVal = (uint8_t *)DbDynMemCtxAlloc(alterMemCtx, length + 1);
            if (*addrVal == NULL) {
                printf("[DeSeriStructVertex] warn: DbMalloc memory(%u) failed.\n", length + 1);
                return GMERR_DATA_EXCEPTION;
            }
            ExtendDynArray(c);
            c->memAddr[c->memSize++] = *addrVal;
        }
        (void)memcpy_s(*addrVal, length, srcBuf + sum, length);
        *(*addrVal + length) = 0;
        sum += length;
        ++i;
    }
    return GMERR_OK;
}

// ######## Entry funtion 4: SeriPrimaryKeySctruct
static Status SeriPrimaryKeySctruct(void *seri, uint8_t *destBuf, GmcStructureResvT *reservedSize)
{
    GmcSeriT *s = (GmcSeriT *)seri;
    StructTestCtx *c = (StructTestCtx *)s->userData;
    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(c->stmt);
    DmSchemaT *schema = MEMBER_PTR(cltCataLabel->vertexLabel->metaVertexLabel, schema);
    DmVlIndexLabelT *pk = NULL;
    uint32_t i, k, j = 0;
    if (c->keyId == 0) {
        *destBuf = 0xff;  // 主键 不允许为空
        pk = MEMBER_PTR(cltCataLabel->vertexLabel->metaVertexLabel, pkIndex);
    } else {
        if (c->keyId < 0 || c->keyId > cltCataLabel->vertexLabel->metaVertexLabel->secIndexNum) {
            DB_ASSERT(0);
        } else {
            pk = MEMBER_PTR(cltCataLabel->vertexLabel->metaVertexLabel, secIndexes) + c->keyId - 1;
            uint8_t keyInfo = 0;
            if (c->keyNullInfo) {  // 用户有传 keyNullInfo 数组, 按用户传的来处理 (暂不支持)
                bool *keyNullInfo = c->keyNullInfo;
                for (i = 0; i < MAX_KEY_FIELD_NUM; ++i) {
                    keyInfo = keyInfo << 1;
                    if (keyNullInfo[i] != 0) {
                        keyInfo |= 1;
                    }
                }
            } else {  // 用户没有传 keyNullInfo 数组, 默认按 索引字段全不为空 来处理
                keyInfo = ((uint32_t)1 << (pk->propeNum)) - 1;
            }
            *destBuf = keyInfo;

            // lpm 不允许为空; 所有索引字段的 nullable 都为 false 时, 置为 0xff
            if (pk->idxLabelBase.indexType == LPM4_INDEX || pk->idxLabelBase.indexType == LPM6_INDEX ||
                !pk->isNullable) {
                *destBuf = 0xff;
            }
        }
    }
    uint32_t offset = 1;
    uint8_t *srcBuf = s->obj;
    uint8_t **newAddr = NULL;
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    DmPropertySchemaT *pkproperties = MEMBER_PTR(pk, properties);
    uint32_t *propIds = MEMBER_PTR(pk, propIds);
    for (i = 0; i < pk->propeNum; ++i) {
        srcBuf = s->obj;
        for (j = 0; j < schema->propeNum; ++j) {
            char *pkName = MEMBER_PTR(&pkproperties[propIds[i]], name);
            char *name2 = MEMBER_PTR(&properties[j], name);
            if (0 == strcmp(pkName, name2)) {
                break;
            }
            if (properties[j].isFixed) {
                if (properties[j].dataType == DB_DATATYPE_BITFIELD8 ||
                    properties[j].dataType == DB_DATATYPE_BITFIELD16 ||
                    properties[j].dataType == DB_DATATYPE_BITFIELD32 ||
                    properties[j].dataType == DB_DATATYPE_BITFIELD64) {
                    srcBuf += GetBitFieldSize(properties, &j, schema->propeNum);
                    continue;
                }
                if (properties[j].dataType == DB_DATATYPE_BITMAP) {
                    c->haveBitmap = true;
                    srcBuf += (properties[j].size / 8);
                    continue;
                }
                srcBuf += properties[j].size;
            } else {
                srcBuf += STRUCT_VAR_SIZE;
            }
        }
        ++j;
        DmPropertySchemaT *pkproperties = MEMBER_PTR(pk, properties);
        if (pkproperties[propIds[i]].isFixed) {
            (void)memcpy_s(destBuf + offset, pkproperties[propIds[i]].size, srcBuf, pkproperties[propIds[i]].size);
            offset += pkproperties[propIds[i]].size;
            srcBuf += pkproperties[propIds[i]].size;
        } else {
            k = *(uint16_t *)srcBuf;
            *(uint16_t *)(destBuf + offset) = k;
            offset += sizeof(uint16_t);
            newAddr = (uint8_t **)(srcBuf + sizeof(uint16_t));
            (void)memcpy_s(destBuf + offset, k, *newAddr, k);
            offset += k;
            srcBuf += STRUCT_VAR_SIZE;
        }
    }
    return GMERR_OK;
}

// ######## Entry funtion 5: GetSerialKeyLength
static void GetSerialKeyLength(GmcSeriT *s)
{
    StructTestCtx *c = (StructTestCtx *)s->userData;
    if (c->isFixKey && c->keySize > 0) {
        s->bufSize = c->keySize;
        return;
    }
    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(c->stmt);
    DmSchemaT *schema = MEMBER_PTR(cltCataLabel->vertexLabel->metaVertexLabel, schema);
    DmVlIndexLabelT *pk = NULL;
    if (c->keyId == 0) {
        pk = MEMBER_PTR(cltCataLabel->vertexLabel->metaVertexLabel, pkIndex);
    } else {
        if (c->keyId < 0 || c->keyId > cltCataLabel->vertexLabel->metaVertexLabel->secIndexNum) {
            DB_ASSERT(0);
        } else {
            pk = MEMBER_PTR(cltCataLabel->vertexLabel->metaVertexLabel, secIndexes) + c->keyId - 1;
        }
    }
    uint8_t *srcBuf = s->obj;
    uint32_t i, j = 0;
    c->keySize = 1;
    c->isFixKey = true;
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    DmPropertySchemaT *pkproperties = MEMBER_PTR(pk, properties);
    uint32_t *propIds = MEMBER_PTR(pk, propIds);
    for (i = 0; i < pk->propeNum; ++i) {
        srcBuf = s->obj;
        for (j = 0; j < schema->propeNum; ++j) {
            char *pkName = MEMBER_PTR(&pkproperties[propIds[i]], name);
            char *name2 = MEMBER_PTR(&properties[j], name);
            if (0 == strcmp(pkName, name2)) {
                break;
            }
            if (properties[j].isFixed) {
                if (properties[j].dataType == DB_DATATYPE_BITFIELD8 ||
                    properties[j].dataType == DB_DATATYPE_BITFIELD16 ||
                    properties[j].dataType == DB_DATATYPE_BITFIELD32 ||
                    properties[j].dataType == DB_DATATYPE_BITFIELD64) {
                    srcBuf += GetBitFieldSize(properties, &j, schema->propeNum);
                    continue;
                }
                if (properties[j].dataType == DB_DATATYPE_BITMAP) {
                    c->haveBitmap = true;
                    srcBuf += (properties[j].size / 8);
                    continue;
                }
                srcBuf += properties[j].size;
            } else {
                srcBuf += STRUCT_VAR_SIZE;
            }
        }
        ++j;
        if (j >= schema->propeNum) {
            printf("[GetSerialKeyLength] invalid field:%s of key:%s.\n", properties[j].name, pk->indexName);
            DB_ASSERT(0);
        }
        if (pkproperties[propIds[i]].isFixed) {
            c->keySize += pkproperties[propIds[i]].size;
            srcBuf += pkproperties[propIds[i]].size;
        } else {
            c->keySize += *(uint16_t *)srcBuf;
            srcBuf += STRUCT_VAR_SIZE;
            c->isFixKey = false;
        }
    }
    s->bufSize = c->keySize;
}

template <typename StructObjT>
static void SetKeySeri(
    GmcStmtT *stmt, StructObjT *structObj, GmcSeriT *seri, StructTestCtx *seriCtx, uint32_t keyId, bool *keyNullInfo)
{
    seriCtx->stmt = stmt;
    seriCtx->keyId = keyId;
    seriCtx->keyNullInfo = keyNullInfo;
    seri->seriFunc = SeriPrimaryKeySctruct;
    seri->version = GMC_SERI_VERSION_DEFAULT;
    seri->obj = (uint8_t *)structObj;
    seri->userData = seriCtx;
    GetSerialKeyLength(seri);
}

TEST_F(StClientDWMultiVersion, DwSimpleLabelStrcutDMLMultiversion)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "ip4forward00001";
    Status ret;
    GmcDropVertexLabel(stmt, vertexLabelName);
    // 创建一个schema_v0
    string schema_v0 = GetFileContext("011_direct_write/st_data/spec_complex_flat_schema_v0.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schema_v0.c_str(), NULL));

    Ip4forwordT2 obj;
    StructTestCtx ctx = (StructTestCtx){0};
    ctx.stmt = stmt;
    GmcSeriT serStr;
    serStr.seriFunc = SeriStructVertex;
    serStr.version = GMC_SERI_VERSION_DEFAULT;
    serStr.obj = (uint8_t *)&obj;
    serStr.userData = &ctx;
    for (uint32_t i = 0; i < 1; ++i) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 0xFFFFFFFF, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        SetValueIntoStruct(&obj, i);
        GetSerialVertexLength(&serStr);
        ret = GmcSetVertexWithBuf(stmt, &serStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    DbDynMemCtxFree(alterMemCtx, obj.originString);
    // schema 升级
    string schema_v20 = GetFileContext("011_direct_write/st_data/spec_complex_flat_schema_v20.gmjson");
    ret = GmcAlterVertexLabelWithName(stmt, schema_v20.c_str(), true, (char *)vertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // 结构化写新版本
    Ip4forwordTUpd updateObj;
    StructTestCtx updCtx = (StructTestCtx){0};
    updCtx.stmt = stmt;
    GmcSeriT updSerStr;
    updSerStr.seriFunc = SeriStructVertex;
    updSerStr.version = GMC_SERI_VERSION_DEFAULT;
    updSerStr.obj = (uint8_t *)&updateObj;
    updSerStr.userData = &updCtx;
    for (uint32_t i = 0; i < 1; ++i) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 0xFFFFFFFF, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        SetValueIntoUpdStruct(&updateObj, i);
        GetSerialVertexLength(&updSerStr);
        ret = GmcSetVertexWithBuf(stmt, &updSerStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    DbDynMemCtxFree(alterMemCtx, updateObj.originString);
    DbDynMemCtxFree(alterMemCtx, updateObj.newString);
    // 普通读
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 20, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t vr_id = 2;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, "ip4_key");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    char newString[9] = {0};
    bool isNull;
    ret = GmcFetch(stmt, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t propSize = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "newString", &propSize);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertyByName(stmt, "newString", newString, propSize, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(strcmp(newString, "abcdefg"), 0);

    // 结构化新版本读新版本数据
    Ip4forwordTUpd newObj = (Ip4forwordTUpd){0};
    StructTestCtx deseriCtx = (StructTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    deseriCtx.useExternalMem = false;
    deseriCtx.stmt = stmt;
    deseri.deseriFunc = DeSeriStructVertex;
    deseri.objSize = sizeof(Ip4forwordTUpd);
    deseri.version = GMC_SERI_VERSION_DEFAULT;
    deseri.obj = (uint8_t *)&newObj;
    deseri.userData = &deseriCtx;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 20, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1; i++) {
        newObj.vr_id = 2;
        StructTestCtx seriCtx0 = (StructTestCtx){0};
        GmcSeriT seri = (GmcSeriT){0};
        SetKeySeri(stmt, &newObj, &seri, &seriCtx0, 0, NULL);
        ret = GmcSetIndexKeyWithBuf(stmt, 0, &seri);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = GmcGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
        }
        EXPECT_EQ(strcmp(newObj.newString, "abcdefg"), 0);
        EXPECT_EQ(newObj.vr_id == 2, true);
    }
    DeSeriFreeDynMem(&deseriCtx, true);
    // 结构化老版本读新版本数据
    Ip4forwordT2 newObj1 = (Ip4forwordT2){0};
    StructTestCtx deseriCtx1 = (StructTestCtx){0};
    GmcDeseriT deseri1 = (GmcDeseriT){0};
    deseriCtx1.useExternalMem = false;
    deseriCtx1.stmt = stmt;
    deseri1.deseriFunc = DeSeriStructVertex;
    deseri1.objSize = sizeof(Ip4forwordT2);
    deseri1.version = GMC_SERI_VERSION_DEFAULT;
    deseri1.obj = (uint8_t *)&newObj1;
    deseri1.userData = &deseriCtx1;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 20, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1; i++) {
        newObj1.vr_id = 2;
        StructTestCtx seriCtx0 = (StructTestCtx){0};
        GmcSeriT seri = (GmcSeriT){0};
        SetKeySeri(stmt, &newObj1, &seri, &seriCtx0, 0, NULL);
        ret = GmcSetIndexKeyWithBuf(stmt, 0, &seri);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = GmcGetVertexDeseri(stmt, &deseri1);
            EXPECT_EQ(GMERR_OK, ret);
        }
        EXPECT_EQ(strcmp(newObj1.originString, "abc1234"), 0);
        EXPECT_EQ(newObj1.vr_id == 2, true);
    }
    DeSeriFreeDynMem(&deseriCtx1, true);

    // 结构化新版本读老版本数据
    Ip4forwordTUpd newObj2 = (Ip4forwordTUpd){0};
    StructTestCtx deseriCtx2 = (StructTestCtx){0};
    GmcDeseriT deseri2 = (GmcDeseriT){0};
    deseriCtx2.useExternalMem = false;
    deseriCtx2.stmt = stmt;
    deseri2.deseriFunc = DeSeriStructVertex;
    deseri2.objSize = sizeof(Ip4forwordTUpd);
    deseri2.version = GMC_SERI_VERSION_DEFAULT;
    deseri2.obj = (uint8_t *)&newObj2;
    deseri2.userData = &deseriCtx2;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 0, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1; i++) {
        newObj2.vr_id = 0;
        StructTestCtx seriCtx0 = (StructTestCtx){0};
        GmcSeriT seri = (GmcSeriT){0};
        SetKeySeri(stmt, &newObj2, &seri, &seriCtx0, 0, NULL);
        ret = GmcSetIndexKeyWithBuf(stmt, 0, &seri);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = GmcGetVertexDeseri(stmt, &deseri2);
            EXPECT_EQ(GMERR_OK, ret);
        }
        EXPECT_EQ(strcmp(newObj2.originString, "1234567"), 0);
        EXPECT_EQ(newObj2.vr_id == 0, true);
    }
    DeSeriFreeDynMem(&deseriCtx2, true);

    Ip4forwordTUpd updateObj3;
    StructTestCtx updCtx3 = (StructTestCtx){0};
    updCtx3.stmt = stmt;
    GmcSeriT updSerStr3 = (GmcSeriT){0};
    updSerStr3.seriFunc = SeriStructVertex;
    updSerStr3.version = GMC_SERI_VERSION_DEFAULT;
    updSerStr3.obj = (uint8_t *)&updateObj3;
    updSerStr3.userData = &updCtx3;
    int affectRows;
    for (uint32_t i = 0; i < 1; ++i) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 0xFFFFFFFF, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        SetValueIntoUpdStruct2(&updateObj3, i);
        GetSerialVertexLength(&updSerStr3);
        ret = GmcSetVertexWithBuf(stmt, &updSerStr3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(affectRows, 2);
    EXPECT_EQ(strcmp(updateObj3.originString, "aaaaaaa"), 0);
    DbDynMemCtxFree(alterMemCtx, updateObj3.originString);
    DbDynMemCtxFree(alterMemCtx, updateObj3.newString);
    ret = GmcDropVertexLabel(stmt, vertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 版本1
#pragma pack(1)
typedef struct TagSpeciallabelVertexT1V2 {
    int64_t v0;
} GtSpeciallabelVertexT1V2T;
#pragma pack()

#pragma pack(1)
typedef struct TagSpeciallabelVertexV2 {
    int64_t f0;
    uint64_t f1;
    uint16_t f2Len;
    char *f2;
    uint16_t t1VCount;
    GtSpeciallabelVertexT1V2T *t1;
} GtSpeciallabelVertexV2T;
#pragma pack()

// 版本2
#pragma pack(1)
typedef struct TagSpeciallabelVertexT1V3 {
    int64_t v0;
    int64_t v1;
} GtSpeciallabelVertexT1V3T;
#pragma pack()

#pragma pack(1)
typedef struct TagSpeciallabelVertexT2V3 {
    int64_t v0;
    int64_t v1;
} GtSpeciallabelVertexT2V3T;
#pragma pack()

#pragma pack(1)
typedef struct TagSpeciallabelVertexV3 {
    int64_t f0;
    uint64_t f1;
    uint16_t f2Len;
    char *f2;
    uint16_t t1VCount;
    GtSpeciallabelVertexT1V3T *t1;
    uint16_t t2VCount;
    GtSpeciallabelVertexT2V3T *t2;
} GtSpeciallabelVertexV3T;
#pragma pack()

static void GtSpeciallabel2GetNodeT1(GmcStmtT *stmt, GmcNodeT **root, GmcNodeT **t1V)
{
    GmcNodeT *Root, *t1;
    int32_t ret = GmcGetRootNode(stmt, &Root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    *root = Root;
    *t1V = t1;
}

static void GtSpeciallabel2GetNodeT2(GmcStmtT *stmt, GmcNodeT **root, GmcNodeT **t2V)
{
    GmcNodeT *Root, *t2;
    int32_t ret = GmcGetRootNode(stmt, &Root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "T2", &t2);
    EXPECT_EQ(GMERR_OK, ret);
    *root = Root;
    *t2V = t2;
}

// 版本1序列化写入一条数据
static void WriteDataForSpecialV2(GmcStmtT *stmt, uint32_t index, const char *name, GmcOperationTypeE opCode)
{
    Status ret = GmcPrepareStmtByLabelNameWithVersion(stmt, name, 0xFFFFFFFF, opCode);
    EXPECT_EQ(GMERR_OK, ret);
    GtSpeciallabelVertexV2T *v1 = (GtSpeciallabelVertexV2T *)malloc(sizeof(GtSpeciallabelVertexV2T));
    EXPECT_EQ(true, v1 != NULL);
    (void)memset_s(v1, sizeof(GtSpeciallabelVertexV2T), 0x00, sizeof(GtSpeciallabelVertexV2T));
    v1->f0 = index;
    v1->f1 = index;
    uint32_t len = 8;
    char *f2Value = (char *)malloc(len);
    (void)snprintf_s((char *)f2Value, len, len - 1, "%s", "abc");
    v1->f2Len = strlen(f2Value);
    v1->f2 = f2Value;
    v1->t1VCount = 1;

    GtSpeciallabelVertexT1V2T *t1 = (GtSpeciallabelVertexT1V2T *)malloc(sizeof(GtSpeciallabelVertexT1V2T));
    EXPECT_EQ(true, t1 != NULL);
    (void)memset_s(t1, sizeof(GtSpeciallabelVertexT1V2T), 0x00, sizeof(GtSpeciallabelVertexT1V2T));
    t1->v0 = index;
    v1->t1 = t1;

    // 结构化写数据
    StructTestCtx updCtx = (StructTestCtx){0};
    updCtx.stmt = stmt;
    GmcSeriT updSerStr;
    updSerStr.seriFunc = SeriStructVertex;
    updSerStr.version = GMC_SERI_VERSION_DEFAULT;
    updSerStr.obj = (uint8_t *)v1;
    updSerStr.userData = &updCtx;

    GetSerialVertexLength(&updSerStr);
    ret = GmcSetVertexWithBuf(stmt, &updSerStr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    free(v1);
    free(f2Value);
    free(t1);
}

// 版本3序列化写入一条数据
static void WriteDataForSpecialV3(GmcStmtT *stmt, uint32_t index, const char *name, GmcOperationTypeE opCode)
{
    Status ret = GmcPrepareStmtByLabelNameWithVersion(stmt, name, 0xFFFFFFFF, opCode);
    EXPECT_EQ(GMERR_OK, ret);
    GtSpeciallabelVertexV3T *v1 = (GtSpeciallabelVertexV3T *)malloc(sizeof(GtSpeciallabelVertexV3T));
    EXPECT_EQ(true, v1 != NULL);
    (void)memset_s(v1, sizeof(GtSpeciallabelVertexV3T), 0x00, sizeof(GtSpeciallabelVertexV3T));
    v1->f0 = index;
    v1->f1 = index;
    uint32_t len = 8;
    char *f2Value = (char *)malloc(len);
    (void)snprintf_s((char *)f2Value, len, len - 1, "%s", "abc");
    v1->f2Len = strlen(f2Value);
    v1->f2 = f2Value;
    v1->t1VCount = 1;
    v1->t2VCount = 1;

    GtSpeciallabelVertexT1V3T *t1 = (GtSpeciallabelVertexT1V3T *)malloc(sizeof(GtSpeciallabelVertexT1V3T));
    EXPECT_EQ(true, t1 != NULL);
    (void)memset_s(t1, sizeof(GtSpeciallabelVertexT1V3T), 0x00, sizeof(GtSpeciallabelVertexT1V3T));
    t1->v0 = index;
    v1->t1 = t1;

    GtSpeciallabelVertexT2V3T *t2 = (GtSpeciallabelVertexT2V3T *)malloc(sizeof(GtSpeciallabelVertexT2V3T));
    EXPECT_EQ(true, t2 != NULL);
    (void)memset_s(t2, sizeof(GtSpeciallabelVertexT2V3T), 0x00, sizeof(GtSpeciallabelVertexT2V3T));
    t2->v0 = index;
    v1->t2 = t2;

    // 结构化写数据
    StructTestCtx updCtx = (StructTestCtx){0};
    updCtx.stmt = stmt;
    GmcSeriT updSerStr;
    updSerStr.seriFunc = SeriStructVertex;
    updSerStr.version = GMC_SERI_VERSION_DEFAULT;
    updSerStr.obj = (uint8_t *)v1;
    updSerStr.userData = &updCtx;

    GetSerialVertexLength(&updSerStr);
    ret = GmcSetVertexWithBuf(stmt, &updSerStr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    free(v1);
    free(f2Value);
    free(t1);
    free(t2);
}

static void UpdateDataForSpecialLabel(
    GmcStmtT *stmt, uint32_t index, uint32_t delta, uint32_t version, const char *name, GmcOperationTypeE opCode)
{
    Status ret = GmcPrepareStmtByLabelNameWithVersion(stmt, name, 0xFFFFFFFF, opCode);
    EXPECT_EQ(GMERR_OK, ret);
    GtSpeciallabelVertexV2T *v1 = (GtSpeciallabelVertexV2T *)malloc(sizeof(GtSpeciallabelVertexV2T));
    EXPECT_EQ(true, v1 != NULL);
    (void)memset_s(v1, sizeof(GtSpeciallabelVertexV2T), 0x00, sizeof(GtSpeciallabelVertexV2T));
    // 序列化key buf
    StructTestCtx seriCtx0 = (StructTestCtx){0};
    GmcSeriT seri = (GmcSeriT){0};
    v1->f0 = index;
    SetKeySeri(stmt, v1, &seri, &seriCtx0, 0, NULL);
    ret = GmcSetIndexKeyWithBuf(stmt, 0, &seri);
    EXPECT_EQ(GMERR_OK, ret);
    // 设置属性
    index += delta;
    uint64_t f1Value = index;
    ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    const char *f2Value = "abcde";
    ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_STRING, f2Value, strlen(f2Value));
    EXPECT_EQ(GMERR_OK, ret);
    // 更新t1节点
    GmcNodeT *root = NULL;
    GmcNodeT *t1 = NULL;
    GmcNodeT *t2 = NULL;
    GtSpeciallabel2GetNodeT1(stmt, &root, &t1);
    int64_t t1Value = index;
    ret = GmcNodeSetPropertyByName(t1, (char *)"V0", GMC_DATATYPE_INT64, &t1Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    if (version == 3) {
        GtSpeciallabel2GetNodeT2(stmt, &root, &t2);
        int64_t t2Value = index;
        ret = GmcNodeSetPropertyByName(t2, (char *)"V0", GMC_DATATYPE_INT64, &t2Value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(affectRows, (uint32_t)1);
    free(v1);
}

static void DeleteDataForSpecialLabel(
    GmcStmtT *stmt, uint32_t index, uint32_t delta, uint32_t version, const char *name, GmcOperationTypeE opCode)
{
    Status ret = GmcPrepareStmtByLabelNameWithVersion(stmt, name, 0xFFFFFFFF, opCode);
    EXPECT_EQ(GMERR_OK, ret);
    GtSpeciallabelVertexV2T *v1 = (GtSpeciallabelVertexV2T *)malloc(sizeof(GtSpeciallabelVertexV2T));
    EXPECT_EQ(true, v1 != NULL);
    (void)memset_s(v1, sizeof(GtSpeciallabelVertexV2T), 0x00, sizeof(GtSpeciallabelVertexV2T));
    // 序列化key buf
    StructTestCtx seriCtx0 = (StructTestCtx){0};
    GmcSeriT seri = (GmcSeriT){0};
    v1->f0 = index;
    SetKeySeri(stmt, v1, &seri, &seriCtx0, 0, NULL);
    ret = GmcSetIndexKeyWithBuf(stmt, 0, &seri);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(affectRows, (uint32_t)1);
    free(v1);
}

// 读取数据进行校验
static void ReadDataAndCompare(
    GmcStmtT *stmt, uint32_t index, uint32_t version, uint32_t delta, bool dataIsNull, GmcOperationTypeE opt)
{
    const char *specialName = "specialLabel";
    Status ret = GmcPrepareStmtByLabelNameWithVersion(stmt, specialName, version, GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        return;
    }
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    int64_t f0Value = (int64_t)index;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isFinish);
    GmcNodeT *root = NULL, *t1Node = NULL, *t2Node = NULL;
    EXPECT_EQ(true, version == 2 || version == 3);
    // 校验公共属性
    GtSpeciallabel2GetNodeT1(stmt, &root, &t1Node);
    int64_t f0GetValue;
    ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0GetValue, sizeof(int64_t), &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(f0GetValue, index);  // 主键不会更新
    uint64_t f1GetValue;
    ret = GmcNodeGetPropertyByName(root, (char *)"F1", &f1GetValue, sizeof(uint64_t), &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(index + delta, f1GetValue);
    if (opt == GMC_OPERATION_INSERT) {
        const char *f2Value = "abc";
        char f2GetValue[100];
        uint32_t f2ValueLen = 0;
        ret = GmcNodeGetPropertySizeByName(root, "F2", &f2ValueLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(root, (char *)"F2", f2GetValue, f2ValueLen, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isFinish, false);
        f2GetValue[f2ValueLen] = '\0';
        EXPECT_EQ(strcmp(f2Value, f2GetValue), 0);
    } else {
        const char *f2Value = "abcde";
        char f2GetValue[100];
        uint32_t f2ValueLen = 0;
        ret = GmcNodeGetPropertySizeByName(root, "F2", &f2ValueLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(root, (char *)"F2", f2GetValue, f2ValueLen, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isFinish, false);
        f2GetValue[f2ValueLen] = '\0';
        EXPECT_EQ(strcmp(f2Value, f2GetValue), 0);
    }

    int64_t t1GetValue;
    ret = GmcNodeGetPropertyByName(t1Node, (char *)"V0", &t1GetValue, sizeof(int64_t), &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isFinish, false);
    EXPECT_EQ(index + delta, t1GetValue);

    if (version == 3) {
        GtSpeciallabel2GetNodeT2(stmt, &root, &t2Node);
        int64_t t2GetValue;
        ret = GmcNodeGetPropertyByName(t2Node, (char *)"V0", &t2GetValue, sizeof(int64_t), &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (dataIsNull) {
            EXPECT_EQ(isFinish, true);
        } else {
            EXPECT_EQ(isFinish, false);
            EXPECT_EQ(true, (t2GetValue == index + delta || t2GetValue == index));
        }
    }
}

TEST_F(StClientDWMultiVersion, DwSpecialLabelStrcutUpateMultiversion)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);
    const char *specialName = "specialLabel";
    GmcDropVertexLabel(stmt, specialName);
    Status ret;
    // 创建简单表schema_v2
    string schemaV1 = GetFileContext("011_direct_write/st_data/special_v2.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), NULL));

    // 版本2 结构化写入100条数据
    for (uint32_t i = 0; i < 1; i++) {
        WriteDataForSpecialV2(stmt, i, specialName, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本2来读
        ReadDataAndCompare(stmt, i, 2, 0, false, GMC_OPERATION_INSERT);
    }

    // V2->v3
    string schemaV3 = GetFileContext("011_direct_write/st_data/special_v3.gmjson");
    ret = GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, specialName);
    EXPECT_EQ(GMERR_OK, ret);

    // 版本3 结构化写入一条数据
    for (uint32_t i = 1; i < 2; i++) {
        WriteDataForSpecialV3(stmt, i, specialName, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本3来读
        ReadDataAndCompare(stmt, i, 3, 0, false, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本2来读
        ReadDataAndCompare(stmt, i, 2, 0, false, GMC_OPERATION_INSERT);
    }
    // update : v3更新数据
    for (uint32_t i = 0; i < 2; i++) {
        uint32_t delta = 10;
        UpdateDataForSpecialLabel(stmt, i, delta, 3, specialName, GMC_OPERATION_UPDATE);
        // 版本3校验数据
        ReadDataAndCompare(stmt, i, 3, delta, false, GMC_OPERATION_UPDATE);
        // 版本2校验数据
        ReadDataAndCompare(stmt, i, 2, delta, false, GMC_OPERATION_UPDATE);
    }
    // 删除表
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, specialName));
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDWMultiVersion, DwSpecialLabelStrcutDeleteMultiversion)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);
    const char *specialName = "specialLabel";
    GmcDropVertexLabel(stmt, specialName);
    Status ret;
    // 创建简单表schema_v2
    string schemaV1 = GetFileContext("011_direct_write/st_data/special_v2.gmjson");
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), NULL));

    // 版本2 结构化写入10条数据
    for (uint32_t i = 0; i < 100; i++) {
        WriteDataForSpecialV2(stmt, i, specialName, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本2来读
        ReadDataAndCompare(stmt, i, 2, 0, false, GMC_OPERATION_INSERT);
    }

    // V2->v3
    string schemaV3 = GetFileContext("011_direct_write/st_data/special_v3.gmjson");
    ret = GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, specialName);
    EXPECT_EQ(GMERR_OK, ret);

    // 版本3 结构化写入一条数据
    for (uint32_t i = 100; i < 200; i++) {
        WriteDataForSpecialV3(stmt, i, specialName, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本3来读
        ReadDataAndCompare(stmt, i, 3, 0, false, GMC_OPERATION_INSERT);
        // 读取数据进行校验，版本2来读
        ReadDataAndCompare(stmt, i, 2, 0, false, GMC_OPERATION_INSERT);
    }
    // update : v2更新数据
    for (uint32_t i = 0; i < 200; i++) {
        uint32_t delta = 10;
        DeleteDataForSpecialLabel(stmt, i, delta, 2, specialName, GMC_OPERATION_DELETE);
    }
    // 删除表
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, specialName));
    DestroyConnectionAndStmt(conn, stmt);
}

static const uint32_t MAX_CMD_SIZE = 1024;
static char g_cmd[MAX_CMD_SIZE];

static bool CheckCommand(const char *command, const char *expectedStr)
{
    char buf[10000] = {};
    FILE *file = popen(command, "r");
    fread(buf, sizeof(buf) - 1, 1, file);
    pclose(file);
    if (expectedStr != NULL) {
        if (strstr(buf, expectedStr)) {
            printf("Print Info:\n%s\n", buf);
            return false;
        }
    } else {
        if (strstr(buf, "[ERROR]") || strstr(buf, "[WARN]")) {
            printf("Print Info:\n%s\n", buf);
            return false;
        }
    }
    return true;
}

static Status TestGmDDLAlter(const char *expectedStr, const char *param = "")
{
    sprintf_s(g_cmd, MAX_CMD_SIZE, "gmddl -c alter %s", param);
    Status ret = CheckCommand(g_cmd, expectedStr) ? GMERR_OK : GMERR_DATA_EXCEPTION;
    memset_s(g_cmd, MAX_CMD_SIZE, 0, MAX_CMD_SIZE);
    return ret;
}

static void *DoUpgradeAndDownGradeByTool(void *args)
{
    GmcStmtT *stmt = ((ConcurrencyPara *)args)->stmt;
    const char *vertexLabelName = ((ConcurrencyPara *)args)->vertexLabelName;
    string schemaV2 = GetFileContext("011_directwrite/st_data/simple_vertexlabel_v2.gmjson");
    string schemaV3 = GetFileContext("011_directwrite/st_data/simple_vertexlabel_v3.gmjson");
    Status ret;
    for (int i = 0; i < 100; i++) {
        ret = GmcAlterVertexLabelWithName(stmt, schemaV2.c_str(), true, vertexLabelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, vertexLabelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDegradeVertexLabel(stmt, vertexLabelName, 2);
        EXPECT_EQ(GMERR_OK, ret);
        WaitingDegradeEnd(stmt, vertexLabelName);
        ret = GmcDegradeVertexLabel(stmt, vertexLabelName, 1);
        EXPECT_EQ(GMERR_OK, ret);
        WaitingDegradeEnd(stmt, vertexLabelName);
    }
    int32_t *thread_ret = (int32_t *)malloc(sizeof(int32_t));
    *thread_ret = ret;
    pthread_exit(thread_ret);
}

static void *DoUpgradeByToolRollBack(void *args)
{
    const char *simpleLabelName = "simpleLabel";
    const char *vertexLabelName = "generalComplexLabel";

    GmcStmtT *stmt = ((ConcurrencyPara *)args)->stmt;

    EXPECT_EQ(GMERR_OK, TestGmDDLAlter(NULL, "-f 011_direct_write/st_data/batch_upgrade_success.txt -u batch"));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithVersion(stmt, simpleLabelName, 2, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 2, GMC_OPERATION_SCAN));

    EXPECT_EQ(GMERR_DATA_EXCEPTION,
        TestGmDDLAlter(
            "Unsuccess upgrade vertexLabel: generalComplexLabel,begin downgrade vertexLabel in upgradeTaskList",
            "-f 011_direct_write/st_data/batch_upgrade_rollback.txt -u batch"));
    WaitingDegradeEnd(stmt, simpleLabelName);
    EXPECT_NE(GMERR_OK, GmcPrepareStmtByLabelNameWithVersion(stmt, simpleLabelName, 3, GMC_OPERATION_SCAN));
    int32_t *thread_ret = (int32_t *)malloc(sizeof(int32_t));
    *thread_ret = 0;
    pthread_exit(thread_ret);
}

TEST_F(StClientDWMultiVersion, DwDMLDoUpgradeByToolRollBackCurrency)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcConnT *upAndDownConn;
    GmcStmtT *upAndDownStmt;

    CreateSyncConnectionAndStmt(&upAndDownConn, &upAndDownStmt);

    const char *simpleLabelName = "simpleLabel";
    string simpleSchemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");
    // 建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, simpleSchemaV1.c_str(), g_label_config));

    const char *vertexLabelName = "generalComplexLabel";
    string generalComplexSchemaV1 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v1.gmjson");
    // 建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, generalComplexSchemaV1.c_str(), g_label_config));

    ConcurrencyPara dmlArgs;
    dmlArgs.stmt = stmt;
    dmlArgs.vertexLabelName = simpleLabelName;

    ConcurrencyPara upAndDownArgs;
    upAndDownArgs.stmt = upAndDownStmt;
    upAndDownArgs.vertexLabelName = simpleLabelName;

    int32_t *thread_ret;
    uint32_t threadNum = 2;
    pthread_t tid[threadNum];
    pthread_create(&tid[0], NULL, DoUpgradeByToolRollBack, &upAndDownArgs);
    pthread_create(&tid[1], NULL, DoDML, &dmlArgs);

    for (uint32_t i = 0; i < threadNum; i++) {
        pthread_join(tid[i], (void **)&thread_ret);
        printf("[thread %u] thread ret: %d\n", i, *thread_ret);
    }

    GmcDropVertexLabel(stmt, simpleLabelName);
    GmcDropVertexLabel(stmt, vertexLabelName);
    DestroyConnectionAndStmt(conn, stmt);
    DestroyConnectionAndStmt(upAndDownConn, upAndDownStmt);
}

// 直连写模式下，使用聚簇容器后，进行升降级并dml
class StClientDWClusterHashMultiVersion : public StClient {
public:
    static void SetUpTestCase();
};

void StClientDWClusterHashMultiVersion::SetUpTestCase()
{
    StartDbServerWithConfig("\"enableClusterHash=1\" \"trxMonitorEnable=0\" \"workerHungThreshold=6,200,300\" "
                            "\"maxSysDynSize=1024\" \"auditLogEnableDML=0\" \"directWrite=1\" "
                            "\"userPolicyMode=0\" \"compatibleV3=0\"");
    st_clt_init();
    GmcSignalRegisterNotify();
    CreateAndStartEpoll(&responseEpollThreadId, &responseEpollFd);
    CreateAndStartEpoll(&timeoutEpollThreadId, &timeoutEpollFd);
    EXPECT_EQ(GMERR_OK, GmcRegTimeoutEpollFunc(TimeoutEpollReg));
    // 此处心跳定时器与TimeOut定时器使用同一个Epoll，可设置为不同Epoll
    EXPECT_EQ(GMERR_OK, GmcRegHeartBeatEpollFunc(TimeoutEpollReg));
    if (IsEulerEnv()) {
        DbSleep(1000);
    } else {
        st_check_hpe_server_running();
    }
    printf("start response epoll and timeout epoll thread\n");
    printf("response epoll fd: %d, timeout epoll fd: %d\n", responseEpollFd, timeoutEpollFd);
    st_connect();
}

TEST_F(StClientDWClusterHashMultiVersion, nullableConstrainCH)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");
    string schemaV3_wrong = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v3_wrong.gmjson");
    string schemaV3_default_value =
        GetFileContext("011_direct_write/st_data/simple_vertexlabel_v3_default_value.gmjson");

    // 建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), g_label_config));

    // 版本1，插入1-10
    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg1);

    // 用v3版错误schema升级，nullable=false且没有默认值，升级失败
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE,
        GmcAlterVertexLabelWithName(stmt, schemaV3_wrong.c_str(), true, vertexLabelName));

    // 用v3版正确schema升级，nullable=false且有默认值，升级成功
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV3_default_value.c_str(), true, vertexLabelName));

    // 版本3，插入11-20
    LabelOpCfgT cfg2;
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_INSERT;
    cfg2.versionId = 3;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg2);

    // 用版本1查数据成功
    LabelOpCfgT cfg3;
    cfg3.start = 1;
    cfg3.end = 20;
    cfg3.optType = GMC_OPERATION_SCAN;
    cfg3.versionId = 1;
    cfg3.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg3);

    // 版本3查数据
    for (uint32_t i = 1; i <= 20; i++) {
        bool isFinish;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, 3, GMC_OPERATION_SCAN));

        int64_t keyValue = (int64_t)i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &keyValue, sizeof(int64_t)));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));
        bool isNull = true;
        int64_t f0Value = (int64_t)i;
        int64_t f0GetValue;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, (char *)"F0", &f0GetValue, sizeof(int64_t), &isNull));
        EXPECT_FALSE(isNull);
        EXPECT_EQ(f0Value, f0GetValue);

        int64_t f1Value = (int64_t)i;
        int64_t f1GetValue;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, (char *)"F1", &f1GetValue, sizeof(int64_t), &isNull));
        EXPECT_FALSE(isNull);
        EXPECT_EQ(f1Value, f1GetValue);

        int32_t f2Value = (int32_t)i;
        int32_t f2GetValue;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, (char *)"F2", &f2GetValue, sizeof(int32_t), &isNull));
        EXPECT_FALSE(isNull);
        EXPECT_EQ(f2Value, f2GetValue);

        if (i <= 10) {
            // f3默认为null
            int32_t f3GetValue;
            EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, (char *)"F3", &f3GetValue, sizeof(int32_t), &isNull));
            EXPECT_TRUE(isNull);
            // 123456是默认值
            int16_t f4Value = (int16_t)12345;
            int16_t f4GetValue;
            EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, (char *)"F4", &f4GetValue, sizeof(int16_t), &isNull));
            EXPECT_FALSE(isNull);
            EXPECT_EQ(f4Value, f4GetValue);
        } else {
            int32_t f3Value = (int32_t)i;
            int32_t f3GetValue;
            EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, (char *)"F3", &f3GetValue, sizeof(int32_t), &isNull));
            EXPECT_FALSE(isNull);
            EXPECT_EQ(f3Value, f3GetValue);

            int16_t f4Value = (int16_t)i;
            int16_t f4GetValue;
            EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, (char *)"F4", &f4GetValue, sizeof(int16_t), &isNull));
            EXPECT_FALSE(isNull);
            EXPECT_EQ(f4Value, f4GetValue);
        }
    }

    GmcDropVertexLabel(stmt, vertexLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDWClusterHashMultiVersion, DwCHInsertSimpleVertexLabel)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");
    string schemaV2 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v2.gmjson");
    string schemaV3 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v3.gmjson");

    // 建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), g_label_config));

    // 版本1，插入1-10
    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg1);

    // 升级 v1->v2
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV2.c_str(), true, vertexLabelName));
    // 插入 11-20 成功
    LabelOpCfgT cfg2;
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_INSERT;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    // 再次插入 11-20 失败
    cfg2.start = 1;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_INSERT;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_PRIMARY_KEY_VIOLATION;
    cfg2.expAffectRows = 0;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg2);

    // 升级v2->v3
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, vertexLabelName));
    // 插入 21-30
    LabelOpCfgT cfg3;
    cfg3.start = 21;
    cfg3.end = 30;
    cfg3.optType = GMC_OPERATION_INSERT;
    cfg3.versionId = 3;
    cfg3.expExecuteStatus = GMERR_OK;
    cfg3.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg3);

    // 用版本3查数据成功
    LabelOpCfgT cfg4;
    cfg4.start = 1;
    cfg4.end = 30;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 降级v3->v2
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 2));
    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 30;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 版本2，插入31-40成功
    LabelOpCfgT cfg5;
    cfg5.start = 31;
    cfg5.end = 40;
    cfg5.optType = GMC_OPERATION_INSERT;
    cfg5.versionId = 2;
    cfg5.expExecuteStatus = GMERR_OK;
    cfg5.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg5);

    // 降级v2->v1
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 1));

    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 40;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据失败
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    GmcDropVertexLabel(stmt, vertexLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDWClusterHashMultiVersion, DwCHReplaceSimpleVertexLabel)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");
    string schemaV2 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v2.gmjson");
    string schemaV3 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v3.gmjson");

    // 建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), g_label_config));

    // 版本1，插入1-10
    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg1);
    // 版本1，replace 1-10成功
    cfg1.optType = GMC_OPERATION_REPLACE;
    cfg1.expAffectRows = 2;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg1);

    // 升级 v1->v2
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV2.c_str(), true, vertexLabelName));
    // 插入 11-20 成功
    LabelOpCfgT cfg2;
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_REPLACE;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg2);

    // 升级v2->v3
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, vertexLabelName));
    // 插入 21-30
    LabelOpCfgT cfg3;
    cfg3.start = 21;
    cfg3.end = 30;
    cfg3.optType = GMC_OPERATION_REPLACE;
    cfg3.versionId = 3;
    cfg3.expExecuteStatus = GMERR_OK;
    cfg3.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg3);

    // 用版本3查数据成功
    LabelOpCfgT cfg4;
    cfg4.start = 1;
    cfg4.end = 30;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 降级v3->v2
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 2));
    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 30;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 版本2，插入31-40成功
    LabelOpCfgT cfg5;
    cfg5.start = 31;
    cfg5.end = 40;
    cfg5.optType = GMC_OPERATION_REPLACE;
    cfg5.versionId = 2;
    cfg5.expExecuteStatus = GMERR_OK;
    cfg5.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg5);

    // 降级v2->v1
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 1));

    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 40;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据失败
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    GmcDropVertexLabel(stmt, vertexLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDWClusterHashMultiVersion, DwCHUpdateSimpleVertexLabel)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");
    string schemaV2 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v2.gmjson");
    string schemaV3 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v3.gmjson");

    // 建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), g_label_config));

    // 版本1，插入1-10
    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg1);
    // 版本1，update 1-10成功
    cfg1.optType = GMC_OPERATION_UPDATE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg1);

    // 升级 v1->v2
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV2.c_str(), true, vertexLabelName));
    // 插入 11-20 成功
    LabelOpCfgT cfg2;
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_INSERT;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    // 版本2，update 11-20成功
    cfg2.optType = GMC_OPERATION_UPDATE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    // 升级v2->v3
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, vertexLabelName));
    // update 21-30 失败
    LabelOpCfgT cfg3;
    cfg3.start = 21;
    cfg3.end = 30;
    cfg3.optType = GMC_OPERATION_UPDATE;
    cfg3.versionId = 3;
    // execute返回GMERR_OK，但是affectRows为0
    cfg3.expExecuteStatus = GMERR_OK;
    cfg3.expAffectRows = 0;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg3);

    // 用版本3查数据成功
    LabelOpCfgT cfg4;
    cfg4.start = 1;
    cfg4.end = 20;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 降级v3->v2
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 2));

    // update 11-20 成功
    LabelOpCfgT cfg5;
    cfg5.start = 11;
    cfg5.end = 20;
    cfg5.optType = GMC_OPERATION_UPDATE;
    cfg5.versionId = 2;
    cfg5.expExecuteStatus = GMERR_OK;
    cfg5.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg5);

    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 20;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 降级v2->v1
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 1));

    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 20;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据失败
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    GmcDropVertexLabel(stmt, vertexLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDWClusterHashMultiVersion, DwSecIdxUpdateGeneralComplexVertexLabel)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "generalComplexLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v1.gmjson");
    string schemaV2 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v2.gmjson");
    string schemaV3 = GetFileContext("011_direct_write/st_data/generalComplexLabel_v3.gmjson");

    // 建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), g_label_config));

    // 版本1，插入1-10
    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    cfg1.deltaData = 0;
    cfg1.isSecIdxUpdate = true;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg1);
    // 版本1，update 1-10成功
    cfg1.optType = GMC_OPERATION_UPDATE;
    cfg1.deltaData = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg1);

    // 升级 v1->v2
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV2.c_str(), true, vertexLabelName));
    // 插入 11-20 成功
    LabelOpCfgT cfg2;
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_INSERT;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    cfg2.deltaData = 0;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    // 版本2，update 11-20成功
    cfg2.optType = GMC_OPERATION_UPDATE;
    cfg2.deltaData = 1;
    cfg2.isSecIdxUpdate = true;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    // 升级v2->v3
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, vertexLabelName));
    // update 21-30 失败
    LabelOpCfgT cfg3;
    cfg3.start = 21;
    cfg3.end = 30;
    cfg3.optType = GMC_OPERATION_UPDATE;
    cfg3.deltaData = 1;
    cfg3.isSecIdxUpdate = true;
    cfg3.versionId = 3;
    // execute返回GMERR_OK，但是affectRows为0
    cfg3.expExecuteStatus = GMERR_OK;
    cfg3.expAffectRows = 0;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg3);

    // 降级v3->v2
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 2));

    // update 11-20 成功
    LabelOpCfgT cfg5;
    cfg5.start = 11;
    cfg5.end = 20;
    cfg5.optType = GMC_OPERATION_UPDATE;
    cfg5.deltaData = 1;
    cfg5.isSecIdxUpdate = true;
    cfg5.versionId = 2;
    cfg5.expExecuteStatus = GMERR_OK;
    cfg5.expAffectRows = 1;
    GeneralComplexLabelDmlExecute(stmt, vertexLabelName, &cfg5);

    GmcDropVertexLabel(stmt, vertexLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDWClusterHashMultiVersion, DwCHDeleteSimpleVertexLabel)
{
    GmcConnT *conn;
    GmcStmtT *stmt;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");
    string schemaV2 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v2.gmjson");
    string schemaV3 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v3.gmjson");

    // 建表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, schemaV1.c_str(), g_label_config));

    // 版本1，插入1-10
    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg1);
    // 版本1，delete 6-10成功
    cfg1.start = 6;
    cfg1.end = 10;
    cfg1.optType = GMC_OPERATION_DELETE;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg1);

    // 升级 v1->v2
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV2.c_str(), true, vertexLabelName));
    // 插入 11-20 成功
    LabelOpCfgT cfg2;
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_INSERT;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    // 版本2，delete 11-20成功
    cfg2.start = 11;
    cfg2.end = 20;
    cfg2.optType = GMC_OPERATION_DELETE;
    cfg2.versionId = 2;
    cfg2.expExecuteStatus = GMERR_OK;
    cfg2.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    cfg2.expAffectRows = 0;

    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg2);
    // 升级v2->v3
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, schemaV3.c_str(), true, vertexLabelName));

    // 版本3，插入 11-20 成功
    LabelOpCfgT cfg3;
    cfg3.start = 11;
    cfg3.end = 20;
    cfg3.optType = GMC_OPERATION_INSERT;
    cfg3.versionId = 3;
    cfg3.expExecuteStatus = GMERR_OK;
    cfg3.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg3);
    // 版本3，delete 11-20成功
    cfg3.start = 11;
    cfg3.end = 20;
    cfg3.optType = GMC_OPERATION_DELETE;
    cfg3.versionId = 3;
    cfg3.expExecuteStatus = GMERR_OK;
    cfg3.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg3);

    // 用版本3查数据成功
    LabelOpCfgT cfg4;
    cfg4.start = 1;
    cfg4.end = 5;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // 降级v3->v2
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 2));

    // 用版本3查数据失败
    cfg4.start = 1;
    cfg4.end = 5;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据成功
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    // delete 1-2 成功
    LabelOpCfgT cfg5;
    cfg5.start = 1;
    cfg5.end = 2;
    cfg5.optType = GMC_OPERATION_DELETE;
    cfg5.versionId = 2;
    cfg5.expExecuteStatus = GMERR_OK;
    cfg5.expAffectRows = 1;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg5);

    // 降级v2->v1
    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, vertexLabelName, 1));

    // 用版本3查数据失败
    cfg4.start = 3;
    cfg4.end = 5;
    cfg4.optType = GMC_OPERATION_SCAN;
    cfg4.versionId = 3;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本2查数据失败
    cfg4.versionId = 2;
    cfg4.expExecuteStatus = GMERR_UNDEFINED_TABLE;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);
    // 用版本1查数据成功
    cfg4.versionId = 1;
    cfg4.expExecuteStatus = GMERR_OK;
    SimpleLabelDmlExecute(stmt, vertexLabelName, &cfg4);

    GmcDropVertexLabel(stmt, vertexLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientDWClusterHashMultiVersion, DwCHMultiVersionInsertConcurrency)
{
    GmcConnT *dmlConn;
    GmcStmtT *dmlStmt;

    CreateSyncConnectionAndStmt(&dmlConn, &dmlStmt);

    GmcConnT *upAndDownConn;
    GmcStmtT *upAndDownStmt;

    CreateSyncConnectionAndStmt(&upAndDownConn, &upAndDownStmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(dmlStmt, schemaV1.c_str(), g_label_config));

    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 100;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;

    ConcurrencyPara dmlArgs;
    dmlArgs.stmt = dmlStmt;
    dmlArgs.vertexLabelName = vertexLabelName;
    dmlArgs.cfg = &cfg1;

    ConcurrencyPara upAndDownArgs;
    upAndDownArgs.stmt = upAndDownStmt;
    upAndDownArgs.vertexLabelName = vertexLabelName;

    int32_t *thread_ret;
    uint32_t threadNum = 2;
    pthread_t tid[threadNum];
    pthread_create(&tid[0], NULL, DoUpgradeAndDownGrade, &upAndDownArgs);
    pthread_create(&tid[1], NULL, DoInsert, &dmlArgs);

    for (uint32_t i = 0; i < threadNum; i++) {
        pthread_join(tid[i], (void **)&thread_ret);
        printf("[thread %u] thread ret: %d\n", i, *thread_ret);
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dmlStmt, vertexLabelName));

    DestroyConnectionAndStmt(upAndDownConn, upAndDownStmt);
    DestroyConnectionAndStmt(dmlConn, dmlStmt);
}

TEST_F(StClientDWClusterHashMultiVersion, DwCHMultiVersionReplaceConcurrency)
{
    GmcConnT *dmlConn;
    GmcStmtT *dmlStmt;

    CreateSyncConnectionAndStmt(&dmlConn, &dmlStmt);

    GmcConnT *upAndDownConn;
    GmcStmtT *upAndDownStmt;

    CreateSyncConnectionAndStmt(&upAndDownConn, &upAndDownStmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(dmlStmt, schemaV1.c_str(), g_label_config));

    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 100;
    cfg1.optType = GMC_OPERATION_REPLACE;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;

    ConcurrencyPara dmlArgs;
    dmlArgs.stmt = dmlStmt;
    dmlArgs.vertexLabelName = vertexLabelName;
    dmlArgs.cfg = &cfg1;

    ConcurrencyPara upAndDownArgs;
    upAndDownArgs.stmt = upAndDownStmt;
    upAndDownArgs.vertexLabelName = vertexLabelName;

    int32_t *thread_ret;
    uint32_t threadNum = 2;
    pthread_t tid[threadNum];
    pthread_create(&tid[0], NULL, DoUpgradeAndDownGrade, &upAndDownArgs);
    pthread_create(&tid[1], NULL, DoReplace, &dmlArgs);

    for (uint32_t i = 0; i < threadNum; i++) {
        pthread_join(tid[i], (void **)&thread_ret);
        printf("[thread %u] thread ret: %d\n", i, *thread_ret);
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dmlStmt, vertexLabelName));

    DestroyConnectionAndStmt(upAndDownConn, upAndDownStmt);
    DestroyConnectionAndStmt(dmlConn, dmlStmt);
}

TEST_F(StClientDWClusterHashMultiVersion, DwCHMultiVersionUpdateConcurrency)
{
    GmcConnT *dmlConn;
    GmcStmtT *dmlStmt;

    CreateSyncConnectionAndStmt(&dmlConn, &dmlStmt);

    GmcConnT *upAndDownConn;
    GmcStmtT *upAndDownStmt;

    CreateSyncConnectionAndStmt(&upAndDownConn, &upAndDownStmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(dmlStmt, schemaV1.c_str(), g_label_config));

    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 1000;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    SimpleLabelDmlExecute(dmlStmt, vertexLabelName, &cfg1);
    cfg1.optType = GMC_OPERATION_UPDATE;

    ConcurrencyPara dmlArgs;
    dmlArgs.stmt = dmlStmt;
    dmlArgs.vertexLabelName = vertexLabelName;
    dmlArgs.cfg = &cfg1;

    ConcurrencyPara upAndDownArgs;
    upAndDownArgs.stmt = upAndDownStmt;
    upAndDownArgs.vertexLabelName = vertexLabelName;

    int32_t *thread_ret;
    uint32_t threadNum = 2;
    pthread_t tid[threadNum];
    pthread_create(&tid[0], NULL, DoUpgradeAndDownGrade, &upAndDownArgs);
    pthread_create(&tid[1], NULL, DoUpdate, &dmlArgs);

    for (uint32_t i = 0; i < threadNum; i++) {
        pthread_join(tid[i], (void **)&thread_ret);
        printf("[thread %u] thread ret: %d\n", i, *thread_ret);
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dmlStmt, vertexLabelName));

    DestroyConnectionAndStmt(upAndDownConn, upAndDownStmt);
    DestroyConnectionAndStmt(dmlConn, dmlStmt);
}

TEST_F(StClientDWClusterHashMultiVersion, DwCHMultiVersionDeleteConcurrency)
{
    GmcConnT *dmlConn;
    GmcStmtT *dmlStmt;

    CreateSyncConnectionAndStmt(&dmlConn, &dmlStmt);

    GmcConnT *upAndDownConn;
    GmcStmtT *upAndDownStmt;

    CreateSyncConnectionAndStmt(&upAndDownConn, &upAndDownStmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(dmlStmt, schemaV1.c_str(), g_label_config));

    LabelOpCfgT cfg1;
    cfg1.start = 1;
    cfg1.end = 1000;
    cfg1.optType = GMC_OPERATION_INSERT;
    cfg1.versionId = 1;
    cfg1.expExecuteStatus = GMERR_OK;
    cfg1.expAffectRows = 1;
    SimpleLabelDmlExecute(dmlStmt, vertexLabelName, &cfg1);
    cfg1.optType = GMC_OPERATION_DELETE;

    ConcurrencyPara dmlArgs;
    dmlArgs.stmt = dmlStmt;
    dmlArgs.vertexLabelName = vertexLabelName;
    dmlArgs.cfg = &cfg1;

    ConcurrencyPara upAndDownArgs;
    upAndDownArgs.stmt = upAndDownStmt;
    upAndDownArgs.vertexLabelName = vertexLabelName;

    int32_t *thread_ret;
    uint32_t threadNum = 2;
    pthread_t tid[threadNum];
    pthread_create(&tid[0], NULL, DoUpgradeAndDownGrade, &upAndDownArgs);
    pthread_create(&tid[1], NULL, DoDelete, &dmlArgs);

    for (uint32_t i = 0; i < threadNum; i++) {
        pthread_join(tid[i], (void **)&thread_ret);
        printf("[thread %u] thread ret: %d\n", i, *thread_ret);
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dmlStmt, vertexLabelName));

    DestroyConnectionAndStmt(upAndDownConn, upAndDownStmt);
    DestroyConnectionAndStmt(dmlConn, dmlStmt);
}

TEST_F(StClientDWClusterHashMultiVersion, DwCHMultiVersionDMLConcurrency)
{
    GmcConnT *dmlConn;
    GmcStmtT *dmlStmt;

    CreateSyncConnectionAndStmt(&dmlConn, &dmlStmt);

    GmcConnT *upAndDownConn;
    GmcStmtT *upAndDownStmt;

    CreateSyncConnectionAndStmt(&upAndDownConn, &upAndDownStmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(dmlStmt, schemaV1.c_str(), g_label_config));

    ConcurrencyPara dmlArgs;
    dmlArgs.stmt = dmlStmt;
    dmlArgs.vertexLabelName = vertexLabelName;

    ConcurrencyPara upAndDownArgs;
    upAndDownArgs.stmt = upAndDownStmt;
    upAndDownArgs.vertexLabelName = vertexLabelName;

    int32_t *thread_ret;
    uint32_t threadNum = 2;
    pthread_t tid[threadNum];
    pthread_create(&tid[0], NULL, DoUpgradeAndDownGrade, &upAndDownArgs);
    pthread_create(&tid[1], NULL, DoDML, &dmlArgs);

    for (uint32_t i = 0; i < threadNum; i++) {
        pthread_join(tid[i], (void **)&thread_ret);
        printf("[thread %u] thread ret: %d\n", i, *thread_ret);
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dmlStmt, vertexLabelName));

    DestroyConnectionAndStmt(upAndDownConn, upAndDownStmt);
    DestroyConnectionAndStmt(dmlConn, dmlStmt);
}

TEST_F(StClientDWClusterHashMultiVersion, DwCHMultiVersionCSConcurrency)
{
    GmcConnT *dmlConn;
    GmcStmtT *dmlStmt;

    CreateSyncConnectionAndStmt(&dmlConn, &dmlStmt);

    GmcConnT *CSupAndDownConn;
    GmcStmtT *CSupAndDownStmt;

    CreateSyncConnectionAndStmt(&CSupAndDownConn, &CSupAndDownStmt);

    const char *vertexLabelName = "simpleLabel";

    string schemaV1 = GetFileContext("011_direct_write/st_data/simple_vertexlabel_v1.gmjson");

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(dmlStmt, schemaV1.c_str(), g_label_config));

    ConcurrencyPara dmlArgs;
    dmlArgs.stmt = dmlStmt;
    dmlArgs.vertexLabelName = vertexLabelName;

    ConcurrencyPara upAndDownArgs;
    upAndDownArgs.stmt = CSupAndDownStmt;
    upAndDownArgs.vertexLabelName = vertexLabelName;

    int32_t *thread_ret;
    uint32_t threadNum = 2;
    pthread_t tid[threadNum];
    pthread_create(&tid[0], NULL, DoUpgradeAndDownGrade, &upAndDownArgs);
    pthread_create(&tid[1], NULL, DoDML, &dmlArgs);

    for (uint32_t i = 0; i < threadNum; i++) {
        pthread_join(tid[i], (void **)&thread_ret);
        printf("[thread %u] thread ret: %d\n", i, *thread_ret);
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(dmlStmt, vertexLabelName));

    DestroyConnectionAndStmt(CSupAndDownConn, CSupAndDownStmt);
    DestroyConnectionAndStmt(dmlConn, dmlStmt);
}
