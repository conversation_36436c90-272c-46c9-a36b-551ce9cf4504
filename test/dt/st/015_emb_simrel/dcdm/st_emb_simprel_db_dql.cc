/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: st_emb_simprel_ddl.cc
 * Description:
 * Create:
 */
#include "st_emb_simprel_com.h"
#include "gme_sql_api.h"

using namespace std;

class StSimpRelDBDQL : public StSimpRelDB {};

#define TEST_DQL_NOPRINT_RET
#define F1_LEN (16)
#define REC_LEN (27)
#define REC_NUM (150)
#define MAX_BUFLEN (REC_NUM * REC_LEN)

static void DBDqlTestDqlCheckSelectCtxPrepareTable(uint32_t *ulDbId, uint16_t *usRelId)
{
    CommonDBCreateDB4Test(NULL, ulDbId);
    DB_REL_DEF_STRU stRelDef = {0};
    ASSERT_EQ(GMERR_OK, StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/dql/testDqlCheckSelectCtx.json", &stRelDef, NULL));
    ASSERT_EQ(GMERR_OK, DB_CreateTbl(*ulDbId, &stRelDef, usRelId));
    StUtilFreeAllRelDef(&stRelDef);
}

inline static void DBDqlTestDqlCleanData(uint32_t ulDbId, void *selBuff, bool isDsBuf = false)
{
    if (selBuff != NULL) {
        if (isDsBuf) {
            free(((DB_DSBUF_STRU *)selBuff)->StdBuf.pucData);
            ((DB_DSBUF_STRU *)selBuff)->StdBuf.pucData = NULL;
        } else {
            free(((DB_BUF_STRU *)selBuff)->pBuf);
            ((DB_BUF_STRU *)selBuff)->pBuf = NULL;
        }
    }
    CommonDBDropDB4Test(NULL, ulDbId);
}

// DB ID无效或表ID无效，表中无数据
TEST_F(StSimpRelDBDQL, DBDqlTestInvalidSelectCtx1)
{
    uint32_t ulDbId = 10;
    uint16_t usRelId = 10;

    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, 2000, 0);
    DB_COND_STRU stCond = {.usCondNum = 0};

    // 还未创建DB
    ASSERT_EQ(VOS_ERRNO_DB_INVALID_DATABASE, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &stFldFilter, &selBuff));

    // 建DB建表
    DBDqlTestDqlCheckSelectCtxPrepareTable(&ulDbId, &usRelId);

    // 未Open DB
    ASSERT_EQ(GMERR_OK, DB_CloseDB(ulDbId));
    ASSERT_EQ(VOS_ERRNO_DB_DATABASE_NOT_OPENED, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &stFldFilter, &selBuff));

    // 无效的表ID
    ASSERT_EQ(GMERR_OK, DB_OpenDB(NULL, (uint8_t *)COMMON_DBNAME, &ulDbId));
    ASSERT_EQ(VOS_ERRNO_DB_INVALIDREL, DB_SelectAllRecEx(ulDbId, 20, &stCond, &stFldFilter, &selBuff));

    // 表中无数据，查询不到数据
    ASSERT_EQ(VOS_ERRNO_DB_RECNOTEXIST, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &stFldFilter, &selBuff));

    DBDqlTestDqlCleanData(ulDbId, &selBuff);
}

// select buf无效
TEST_F(StSimpRelDBDQL, DBDqlTestInvalidSelectCtx2)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlCheckSelectCtxPrepareTable(&ulDbId, &usRelId);

    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_COND_STRU stCond = {.usCondNum = 0};

    ASSERT_EQ(VOS_ERRNO_DB_NULLPTR, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &stFldFilter, NULL));

    DB_BUF_STRU selBuff = {0};
    ASSERT_EQ(VOS_ERRNO_DB_NULLPTR, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &stFldFilter, &selBuff));

    CommonDBDropDB4Test(NULL, ulDbId);
}

// 查询cond无效
TEST_F(StSimpRelDBDQL, DBDqlTestInvalidSelectCtx3)
{
    Status ret = GMERR_OK;
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlCheckSelectCtxPrepareTable(&ulDbId, &usRelId);

    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, 2000, DB_SELECT_ALL);
    ret = DB_SelectAllRecEx(ulDbId, usRelId, NULL, &stFldFilter, &selBuff);
    ASSERT_EQ(VOS_ERRNO_DB_NULLPTR, ret);

    // 条件数量超过上限
    DB_COND_STRU stCond = {.usCondNum = 21};
    ret = DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &stFldFilter, &selBuff);
    ASSERT_EQ(VOS_ERRNO_DB_INVALIDCONDITION, ret);

    // 字段ID超过表字段ID上限，表中有27个字段，对应ID 0-26
    stCond.usCondNum = 1;
    uint32_t condV = 0;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 27, DB_OP_LARGER, (uint8_t *)&condV, 4);
    ret = DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &stFldFilter, &selBuff);
    ASSERT_EQ(VOS_ERRNO_DB_INVALIDFIELD, ret);

    CommonDBDropDB4Test(NULL, ulDbId);
}

// 查询cond字段操作类型不支持
TEST_F(StSimpRelDBDQL, DBDqlTestInvalidSelectCtx4)
{
    Status ret = GMERR_OK;
    uint32_t ulDbId;
    uint16_t usRelId;
    // 表结构见testDqlCheckSelectCtx.json文件
    DBDqlTestDqlCheckSelectCtxPrepareTable(&ulDbId, &usRelId);

    DB_COND_STRU stCond = {.usCondNum = 1};
    uint32_t condV = 0;

    // EQUAL ~ LARGEREQUAL
    for (uint32_t op = (uint32_t)DB_OP_EQUAL; op <= (uint32_t)DB_OP_LARGEREQUAL; op++) {
        for (uint8_t fldId = 0; fldId < 27; fldId++) {
            DBDqlTestDqlInitOneCond(&stCond.aCond[0], fldId, (DB_OPTYPE_ENUM)op, (uint8_t *)&condV, 4);
            ret = DB_RecordExist(ulDbId, usRelId, &stCond);
            if (fldId == 3) {
                ASSERT_EQ(VOS_ERRNO_DB_NOTSUPPORT, ret);  // bit类型均不支持
            } else {
                ASSERT_NE(VOS_ERRNO_DB_NOTSUPPORT, ret);  // 其他类型都支持
            }
        }
    }

    // LIKE & NOTLIKE & POSTFIX21 & MAX_POSTFIX21
    DB_OPTYPE_ENUM stringOps[4] = {DB_OP_LIKE, DB_OP_NOTLIKE, DB_OP_POSTFIX21, DB_OP_MAX_POSTFIX21};
    for (uint32_t opIdx = 0; opIdx < 4; opIdx++) {
        for (uint8_t fldId = 0; fldId < 27; fldId++) {
            DBDqlTestDqlInitOneCond(&stCond.aCond[0], fldId, stringOps[opIdx], (uint8_t *)&condV, 4);
            ret = DB_RecordExist(ulDbId, usRelId, &stCond);
            if (fldId == 7 || fldId == 25) {
                ASSERT_NE(VOS_ERRNO_DB_NOTSUPPORT, ret);  // 只有string类型支持
            } else {
                ASSERT_EQ(VOS_ERRNO_DB_NOTSUPPORT, ret);
            }
        }
    }

    // HAVEPREFIX ~ MIN_PREFIX21
    for (uint32_t op = (uint32_t)DB_OP_HAVEPREFIX; op <= (uint32_t)DB_OP_MIN_PREFIX21; op++) {
        for (uint8_t fldId = 0; fldId < 27; fldId++) {
            if (op == DB_OP_LIKE || op == DB_OP_NOTLIKE) {
                continue;
            }
            DBDqlTestDqlInitOneCond(&stCond.aCond[0], fldId, (DB_OPTYPE_ENUM)op, (uint8_t *)&condV, 4);
            ret = DB_RecordExist(ulDbId, usRelId, &stCond);
            if (fldId == 0 || fldId == 7 || fldId == 25) {
                ASSERT_NE(VOS_ERRNO_DB_NOTSUPPORT, ret);  // bcd和string类型支持
            } else {
                ASSERT_EQ(VOS_ERRNO_DB_NOTSUPPORT, ret);
            }
        }
    }

    // MAX_LESS ~ MIN_LARGEREQUAL
    for (uint32_t op = (uint32_t)DB_OP_MAX_LESS; op <= (uint32_t)DB_OP_MIN_LARGER_EQUAL; op++) {
        for (uint8_t fldId = 0; fldId < 27; fldId++) {
            DBDqlTestDqlInitOneCond(&stCond.aCond[0], fldId, (DB_OPTYPE_ENUM)op, (uint8_t *)&condV, 4);
            ret = DB_RecordExist(ulDbId, usRelId, &stCond);
            if (fldId == 3 || fldId == 5 || fldId == 26) {
                ASSERT_EQ(VOS_ERRNO_DB_NOTSUPPORT, ret);  // bit bytes类型不支持
            } else {
                ASSERT_NE(VOS_ERRNO_DB_NOTSUPPORT, ret);
            }
        }
    }

    // BUTT
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_BUTT, (uint8_t *)&condV, 4);
    ret = DB_RecordExist(ulDbId, usRelId, &stCond);
    ASSERT_EQ(VOS_ERRNO_DB_INVALIDOPTYPE, ret);

    CommonDBDropDB4Test(NULL, ulDbId);
}

// 查询字段投影无效
TEST_F(StSimpRelDBDQL, DBDqlTestInvalidSelectCtx5)
{
    Status ret = GMERR_OK;
    uint32_t ulDbId;
    uint16_t usRelId;
    // 表结构见testDqlCheckSelectCtx.json文件
    DBDqlTestDqlCheckSelectCtxPrepareTable(&ulDbId, &usRelId);
    DB_COND_STRU stCond = {.usCondNum = 0};

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, 2000, DB_SELECT_ALL);
    ret = DB_SelectAllRecEx(ulDbId, usRelId, &stCond, NULL, &selBuff);
    ASSERT_EQ(VOS_ERRNO_DB_NULLPTR, ret);

    DB_FIELDFILTER_STRU stFldFilter;
    // 设置的投影字段数量超过表字段数量，表中有27个字段
    stFldFilter.ucFieldNum = 27;
    ret = DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &stFldFilter, &selBuff);
    ASSERT_NE(VOS_ERRNO_DB_INVALIDFILTER, ret);  // 设置27不报错VOS_ERRNO_DB_INVALIDFILTER
    stFldFilter.ucFieldNum = 28;
    ret = DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &stFldFilter, &selBuff);
    ASSERT_EQ(VOS_ERRNO_DB_INVALIDFILTER, ret);

    // 设置的投影字段超过表字段ID上限
    stFldFilter.ucFieldNum = 1;
    stFldFilter.aucField[0] = 27;
    ret = DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &stFldFilter, &selBuff);
    ASSERT_EQ(VOS_ERRNO_DB_INVALIDFIELD, ret);

    CommonDBDropDB4Test(NULL, ulDbId);
}

// 查询排序结构无效
TEST_F(StSimpRelDBDQL, DBDqlTestInvalidSelectCtx6)
{
    Status ret = GMERR_OK;
    uint32_t ulDbId;
    uint16_t usRelId;
    // 表结构见testDqlCheckSelectCtx.json文件
    DBDqlTestDqlCheckSelectCtxPrepareTable(&ulDbId, &usRelId);
    DB_COND_STRU stCond = {.usCondNum = 0};
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, 2000, DB_SELECT_ALL);
    ret = DB_SelectAllRecByOrderEx(ulDbId, usRelId, NULL, &stCond, &stFldFilter, &selBuff);
    ASSERT_EQ(VOS_ERRNO_DB_NULLPTR, ret);

    DB_SORT_STRU stSort = {.ucSortNum = 1};
    // 设置有排序但是排序字段为null
    ret = DB_SelectAllRecByOrderEx(ulDbId, usRelId, &stSort, &stCond, &stFldFilter, &selBuff);
    ASSERT_EQ(VOS_ERRNO_DB_INVALIDSORTER, ret);

    // 排序字段数量超过表字段数量
    T_FIELD sortFields[28] = {0};
    stSort.ucSortNum = 28;
    stSort.pSortFields = sortFields;
    ret = DB_SelectAllRecByOrderEx(ulDbId, usRelId, &stSort, &stCond, &stFldFilter, &selBuff);
    ASSERT_EQ(VOS_ERRNO_DB_INVALIDSORTER, ret);

    // 排序字段ID超过表字段ID上限
    stSort.ucSortNum = 1;
    sortFields[0] = 28;
    ret = DB_SelectAllRecByOrderEx(ulDbId, usRelId, &stSort, &stCond, &stFldFilter, &selBuff);
    ASSERT_EQ(VOS_ERRNO_DB_INVALIDFIELD, ret);

    // 无效的排序类型
    stSort.ucSortNum = 1;
    sortFields[0] = 0;
    stSort.enSortType = 3;
    ret = DB_SelectAllRecByOrderEx(ulDbId, usRelId, &stSort, &stCond, &stFldFilter, &selBuff);
    ASSERT_EQ(VOS_ERRNO_DB_INVALIDSORTER, ret);

    CommonDBDropDB4Test(NULL, ulDbId);
}

typedef struct DBDqlTestDqlRecord {
    uint32_t v0;
    char v1[16];
    uint32_t v2;
    uint16_t v3;
    uint8_t v4;
} DBDqlTestDqlRecordT;

static void DBDqlTestDqlInitOneIdx(DB_INDEX_DEF_STRU *idx, uint8_t fldNum, uint8_t *flds, uint8_t type, bool unique)
{
    idx->enIndexType = (DBDDL_INDEXTYPE_ENUM)type;
    idx->ucIdxFldNum = fldNum;
    idx->ucUniqueFlag = unique ? 1 : 0;
    for (uint32_t i = 0; i < fldNum; i++) {
        idx->aucFieldID[i] = flds[i];
    }
}

void DBDqlTestDqlInitRelDef(DB_REL_DEF_STRU *stRelDef, char *tblName)
{
    const char tableName[] = "testDql";
    const uint32_t fldNum = 5;
    DB_FIELD_DEF_STRU *astFlds = (DB_FIELD_DEF_STRU *)malloc(fldNum * sizeof(DB_FIELD_DEF_STRU));
    const DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_UINT32, DBT_STRING, DBT_UINT32, DBT_UINT16, DBT_UINT8};
    const uint32_t fldSizes[fldNum] = {4, 15, 4, 2, 1};
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
        astFlds[i].ulDefVal = 0xffffffff;
    }

    const uint32_t idxNum = 4;
    DB_INDEX_DEF_STRU *idxList = (DB_INDEX_DEF_STRU *)malloc(idxNum * sizeof(DB_INDEX_DEF_STRU));
    (void)memset_s(idxList, idxNum * sizeof(DB_INDEX_DEF_STRU), 0, idxNum * sizeof(DB_INDEX_DEF_STRU));
    for (uint32_t i = 0; i < idxNum; i++) {
        (void)sprintf_s((char *)idxList[i].aucIndexName, DB_NAME_LEN, "Idx%d", i);
    }

    uint8_t idx0Flds[] = {0};  // uint32_t
    DBDqlTestDqlInitOneIdx(&idxList[0], (uint8_t)sizeof(idx0Flds), idx0Flds, 2, true);
    uint8_t idx1Flds[] = {0, 2, 4};  // uint32_t - uint32_t - uint8_t
    DBDqlTestDqlInitOneIdx(&idxList[1], (uint8_t)sizeof(idx1Flds), idx1Flds, 2, false);
    uint8_t idx2Flds[] = {1, 3};  // string - uint16_t
    DBDqlTestDqlInitOneIdx(&idxList[2], (uint8_t)sizeof(idx2Flds), idx2Flds, 2, false);
    uint8_t idx3Flds[] = {1, 4};  // string - uint8_t
    DBDqlTestDqlInitOneIdx(&idxList[3], (uint8_t)sizeof(idx3Flds), idx3Flds, 2, false);

    tblName == NULL ? (void)strncpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN) :
                      (void)strncpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tblName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 500;
    stRelDef->ulMaxSize = 500;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = idxNum;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = idxList;
    return;
}

void DBDqlTestDqlCreateTbl(uint32_t dbId, uint16_t *relId)
{
    DB_REL_DEF_STRU pstRelDef = {0};
    DBDqlTestDqlInitRelDef(&pstRelDef, NULL);
    ASSERT_EQ(GMERR_OK, DB_CreateTbl(dbId, &pstRelDef, relId));
    TestFreeRelDef(&pstRelDef);
}

void DBDqlTestDqlPrepareData(uint32_t *dbId, uint16_t *relId)
{
    CommonDBCreateDB4Test(NULL, dbId);
    DBDqlTestDqlCreateTbl(*dbId, relId);

    /*
        v0 :  1 ~ 150
        v1 :  xxyz%ucba(1~50 % 20)  abccc%u(51~100 % 30)  aac%uxx(101~150 % 40)
        v2 :  149 ~ 0
        v3 :  10 ~ 249
        v4 :  1~49(1~49)  0~49(50~99)  0~49(100~149)  0(150)
    */
    for (uint32_t i = 1; i <= REC_NUM; i++) {
        DBDqlTestDqlRecordT record = {
            .v0 = i, .v1 = {0}, .v2 = 150 - i, .v3 = (uint16_t)(i + 99), .v4 = (uint8_t)(i % 50)};
        if (i <= 50) {
            (void)sprintf_s(record.v1, F1_LEN, "xxyz%ucba", i % 20);
        } else if (i <= 100) {
            (void)sprintf_s(record.v1, F1_LEN, "abccc%u", i % 30);
        } else {
            (void)sprintf_s(record.v1, F1_LEN, "aac%uxx", i % 40);
        }

        DB_DSBUF_STRU dsBuf = {.usRecLen = REC_LEN,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = REC_LEN, .ulActLen = REC_LEN, .pucData = (uint8_t *)&record}};

        ASSERT_EQ(GMERR_OK, DB_InsertRec(*dbId, *relId, &dsBuf));
    }
}

inline static void DBDqlTestDqlGetSelectBufValue(
    void *selBuff, bool isDsBuf, uint32_t *recNum, uint32_t *recLen, uint8_t **bufCursor)
{
    if (isDsBuf) {
        *recNum = ((DB_DSBUF_STRU *)selBuff)->usRecNum;
        *recLen = ((DB_DSBUF_STRU *)selBuff)->usRecLen;
        *bufCursor = (uint8_t *)((DB_DSBUF_STRU *)selBuff)->StdBuf.pucData;
    } else {
        *recNum = ((DB_BUF_STRU *)selBuff)->ulRecNum;
        *recLen = ((DB_BUF_STRU *)selBuff)->usRecLen;
        *bufCursor = (uint8_t *)((DB_BUF_STRU *)selBuff)->pBuf;
    }
}

void DBDqlTestDqlPrintSelectResult(DB_COND_STRU *stCond, void *selBuff, bool isDsBuf = false)
{
#ifdef TEST_DQL_NOPRINT_RET
    return;
#endif
    const char ops[][16] = {"=", "!=", "<", "<=", ">", ">=", "have prefix", "no prefix", "like", "not like", "prefix",
        "prefix12", "prefix21", "max prefix12", "max prefix21", "min prefix12", "min prefix21", "max LT(<)",
        "max LE(<=)", "min GT(>)", "min GE(>=)", "postfix21", "max postfix21"};

    printf("+========== Scan Conditions ==========+\n");
    for (uint32_t i = 0; i < stCond->usCondNum; i++) {
        DB_CONDITEM_STRU cond = stCond->aCond[i];
        printf("|COND%u :  F%u %s ", i + 1, cond.ucFieldId, ops[cond.enOp]);
        if (cond.ucFieldId == 1) {
            printf("\"%s\"\n", (char *)cond.aucValue);
        } else {
            printf("%u\n", *(uint32_t *)cond.aucValue);
        }
    }
    uint32_t recNum = 0, recLen = 0;
    uint8_t *bufCursor = NULL;
    DBDqlTestDqlGetSelectBufValue(selBuff, isDsBuf, &recNum, &recLen, &bufCursor);
    if (recNum == 0) {
        printf("---- Scan Conditions Match 0 Record.\n");
        return;
    }
    printf("+=============== Scan Result List ===============+\n");
    printf("|%6s|%4s  |%8s     |%4s  |%4s  |%4s  |\n", "Rec No", "F0", "F1", "F2", "F3", "F4");
    printf("|------|------|-------------|------|------|------|\n");

    for (uint32_t i = 0; i < recNum; i++) {
        DBDqlTestDqlRecordT *rec = (DBDqlTestDqlRecordT *)bufCursor;
        printf("|NO.%-3u|  %-4u|  %-11s|  %-4u|  %-4u|  %-4u|\n", i + 1, rec->v0, rec->v1, rec->v2, rec->v3, rec->v4);
        bufCursor += recLen;
    }
    printf("+================================================+\n\n");
}

// recNo为从结果buf取对应第几条数据(全字段获取)(0 < recNo && recNo <= selBuf->ulRecNum)
inline static void DBDqlTestDqlGetOneRecFromBuf(
    DBDqlTestDqlRecordT *rec, void *selBuff, uint32_t recNo, bool isDsBuf = false)
{
    uint32_t recNum = 0, recLen = 0;
    uint8_t *bufCursor = NULL;
    DBDqlTestDqlGetSelectBufValue(selBuff, isDsBuf, &recNum, &recLen, &bufCursor);
    ASSERT_NE(0, recNo);
    ASSERT_LE(recNo, recNum);
    uint8_t *buffer = (uint8_t *)bufCursor + ((recNo - 1) * recLen);
    errno_t err = memcpy_s(rec, recLen, buffer, recLen);
    ASSERT_EQ(err, EOK);
}

// 非索引查询 大于\等于\小于\大于等于\小于等于，查询所有数据，无字段投影
TEST_F(StSimpRelDBDQL, DBDqlTestSelectAllRec1)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 1};                          // 条件数量
    DBDqlTestDqlRecordT rec[3];                                      // 用于查询结果校验

    /*  test >  */
    uint32_t condV = 140;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 2, DB_OP_LARGER, (uint8_t *)&condV, 4);  // F2 >= 140
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);                   // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(9, selBuff.ulRecNum);

    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);  // 第1条
    DBDqlTestDqlGetOneRecFromBuf(&rec[1], &selBuff, 9);  // 第9条
    ASSERT_EQ(1, rec[0].v0);
    ASSERT_EQ(141, rec[1].v2);

    /*  test =  */
    condV = 130;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 2, DB_OP_EQUAL, (uint8_t *)&condV, 4);  // F2 = 130
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);                  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(1, selBuff.ulRecNum);

    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);  // 第1条
    ASSERT_EQ(130, rec[0].v2);

    /*  test <  */
    condV = 20;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 2, DB_OP_LESS, (uint8_t *)&condV, 4);  // F2 < 20
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);                 // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(20, selBuff.ulRecNum);

    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);   // 第1条
    DBDqlTestDqlGetOneRecFromBuf(&rec[1], &selBuff, 15);  // 第15条
    ASSERT_EQ(19, rec[0].v2);
    ASSERT_EQ(5, rec[1].v2);

    /*  test >=  */
    condV = 145;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 2, DB_OP_LARGEREQUAL, (uint8_t *)&condV, 4);  // F2 >= 145
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(5, selBuff.ulRecNum);

    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 3);  // 第3条
    ASSERT_EQ(147, rec[0].v2);

    /*  test <=  */
    condV = 3;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 4, DB_OP_LESSEQUAL, (uint8_t *)&condV, 1);  // F4 <= 3
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);                      // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(12, selBuff.ulRecNum);

    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 4);  // 第4条
    ASSERT_EQ(0, rec[0].v4);

    DBDqlTestDqlCleanData(ulDbId, &selBuff);
}

// 非索引查询 max_LT\max_LE\min_GT\min_GE，查询所有数据，无字段投影
TEST_F(StSimpRelDBDQL, DBDqlTestSelectAllRec2)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 1};                          // 条件数量
    uint32_t condV;                                                  // 条件值
    DBDqlTestDqlRecordT rec[3];                                      // 用于查询结果校验

    /*  test max_LT  */
    condV = 32;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 4, DB_OP_MAX_LESS, (uint8_t *)&condV, 1);  // F4 max_LT 32
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);                     // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(3, selBuff.ulRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 2);  // 第2条
    ASSERT_EQ(31, rec[0].v4);

    /*  test max_LE  */
    condV = 48;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 4, DB_OP_MAX_LESS_EQUAL, (uint8_t *)&condV, 1);  // F4 max_LE 48
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(3, selBuff.ulRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 2);  // 第2条
    ASSERT_EQ(48, rec[0].v4);

    /*  test min_GE  */
    condV = 0;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 4, DB_OP_MIN_LARGER_EQUAL, (uint8_t *)&condV, 1);  // F4 min_GE 0
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(3, selBuff.ulRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 2);  // 第2条
    ASSERT_EQ(0, rec[0].v4);

    /*  test min_GT  */
    condV = 35;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 2, DB_OP_MIN_LARGER, (uint8_t *)&condV, 4);  // F2 min_GT 35
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);                       // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(1, selBuff.ulRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);  // 第1条
    ASSERT_EQ(14, rec[0].v4);

    DBDqlTestDqlCleanData(ulDbId, &selBuff);
}

// 索引查询所有数据，无字段投影
TEST_F(StSimpRelDBDQL, DBDqlTestSelectAllRec3)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 3};                          // 条件数量
    DBDqlTestDqlRecordT rec[3];                                      // 用于查询结果校验
    uint32_t condV0, condV2, condV3, condV4;

    /*  匹配第1个索引  */
    condV0 = 20;
    condV2 = 120;
    condV4 = 27;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGER, (uint8_t *)&condV0, 4);
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 2, DB_OP_LARGER, (uint8_t *)&condV2, 4);
    DBDqlTestDqlInitOneCond(&stCond.aCond[2], 4, DB_OP_LESS, (uint8_t *)&condV4, 1);
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(6, selBuff.ulRecNum);

    /*  匹配第2个索引  */
    condV0 = 21;
    condV2 = 129;
    condV4 = 30;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_EQUAL, (uint8_t *)&condV0, 4);
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 2, DB_OP_EQUAL, (uint8_t *)&condV2, 4);
    DBDqlTestDqlInitOneCond(&stCond.aCond[2], 4, DB_OP_LESS, (uint8_t *)&condV4, 1);
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(1, selBuff.ulRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);  // 第1条
    ASSERT_EQ(21, rec[0].v0);
    ASSERT_EQ(129, rec[0].v2);

    /*  匹配第3个索引  */
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_EQUAL, (uint8_t *)"abccc6", strlen("abccc6"));
    condV3 = 200;
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 3, DB_OP_LESS, (uint8_t *)&condV3, 2);
    condV3 = 100;
    DBDqlTestDqlInitOneCond(&stCond.aCond[2], 3, DB_OP_LARGEREQUAL, (uint8_t *)&condV3, 2);
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);

    ASSERT_EQ(2, selBuff.ulRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);  // 第1条
    ASSERT_EQ(96, rec[0].v0);

    /*  匹配第4个索引  */
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_EQUAL, (uint8_t *)"abccc8", strlen("abccc8"));
    condV4 = 25;
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 4, DB_OP_LESS, (uint8_t *)&condV4, 1);
    condV4 = 18;
    DBDqlTestDqlInitOneCond(&stCond.aCond[2], 4, DB_OP_LARGEREQUAL, (uint8_t *)&condV4, 1);
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);

    ASSERT_EQ(1, selBuff.ulRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);  // 第1条
    ASSERT_EQ(68, rec[0].v0);

    DBDqlTestDqlCleanData(ulDbId, &selBuff);
}

// 索引（int max min）查询所有数据，无字段投影
TEST_F(StSimpRelDBDQL, DBDqlTestSelectAllRec4)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 2};                          // 条件数量
    DBDqlTestDqlRecordT rec[2];                                      // 用于查询结果校验
    uint32_t condV0, condV4;

    /*  匹配第1个索引  */
    condV0 = 20;
    condV4 = 15;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_MIN_LARGER_EQUAL, (uint8_t *)&condV0, 4);
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 4, DB_OP_EQUAL, (uint8_t *)&condV4, 1);
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(1, selBuff.ulRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);  // 第1条
    ASSERT_EQ(65, rec[0].v0);
    ASSERT_EQ(15, rec[0].v4);

    condV0 = 140;
    condV4 = 15;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_MAX_LESS, (uint8_t *)&condV0, 4);
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 4, DB_OP_EQUAL, (uint8_t *)&condV4, 1);
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(1, selBuff.ulRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);  // 第1条
    ASSERT_EQ(115, rec[0].v0);
    ASSERT_EQ(15, rec[0].v4);

    DBDqlTestDqlCleanData(ulDbId, &selBuff);
}

// string like / not like 查询所有数据，无字段投影
TEST_F(StSimpRelDBDQL, DBDqlTestSelectAllRec5)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 2};                          // 条件数量
    uint32_t condV0;

    condV0 = 20; /* DB_OP_LIKE */
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGEREQUAL, (uint8_t *)&condV0, 4);
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 1, DB_OP_LIKE, (uint8_t *)"%1cba", strlen("%1cba"));
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(3, selBuff.ulRecNum);

    condV0 = 90; /* DB_OP_NOTLIKE */
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGEREQUAL, (uint8_t *)&condV0, 4);
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 1, DB_OP_NOTLIKE, (uint8_t *)"%xx", strlen("%xx"));
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(11, selBuff.ulRecNum);

    DBDqlTestDqlCleanData(ulDbId, &selBuff);
}

// string prefix / prefix12 / prefix21 查询所有数据，无字段投影
TEST_F(StSimpRelDBDQL, DBDqlTestSelectAllRec6)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 2};                          // 条件数量
    uint32_t condV0;

    condV0 = 20; /* DB_OP_HAVEPREFIX */
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGEREQUAL, (uint8_t *)&condV0, 4);
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 1, DB_OP_HAVEPREFIX, (uint8_t *)"aac1", strlen("aac1"));
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(11, selBuff.ulRecNum);

    condV0 = 0; /* DB_OP_PREFIX12 */
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGEREQUAL, (uint8_t *)&condV0, 4);
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 1, DB_OP_PREFIX12, (uint8_t *)"xxyz2", strlen("xxyz2"));
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(3, selBuff.ulRecNum);

    condV0 = 0; /* DB_OP_PREFIX21 */
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGEREQUAL, (uint8_t *)&condV0, 4);
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 1, DB_OP_PREFIX21, (uint8_t *)"xxyz10cbazz", strlen("xxyz10cbazz"));
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(3, selBuff.ulRecNum);

    condV0 = 0; /* DB_OP_PREFIX */
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGEREQUAL, (uint8_t *)&condV0, 4);
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 1, DB_OP_PREFIX, (uint8_t *)"xxyz10cbazz", strlen("xxyz10cbazz"));
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(3, selBuff.ulRecNum);

    condV0 = 0; /* DB_OP_PREFIX */
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGEREQUAL, (uint8_t *)&condV0, 4);
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 1, DB_OP_PREFIX, (uint8_t *)"xxyz2", strlen("xxyz2"));
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(3, selBuff.ulRecNum);

    DBDqlTestDqlCleanData(ulDbId, &selBuff);
}

// string (min max) prefix12 / prefix21
TEST_F(StSimpRelDBDQL, DBDqlTestSelectAllRec7)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 2};                          // 条件数量
    DBDqlTestDqlRecordT rec[3];                                      // 用于查询结果校验
    uint32_t condV4;

    /*  DB_OP_MAX_PREFIX12  */
    condV4 = 40;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_MAX_PREFIX12, (uint8_t *)"xxyz1", strlen("xxyz1"));
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 4, DB_OP_LESSEQUAL, (uint8_t *)&condV4, 1);
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(2, selBuff.ulRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);  // 第1条
    EXPECT_STREQ("xxyz1cba", rec[0].v1);

    /*  DB_OP_MIN_PREFIX12  */
    condV4 = 40;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_MIN_PREFIX12, (uint8_t *)"xxyz1", strlen("xxyz1"));
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 4, DB_OP_LESSEQUAL, (uint8_t *)&condV4, 1);
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(3, selBuff.ulRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);  // 第1条
    EXPECT_STREQ("xxyz10cba", rec[0].v1);
    EXPECT_EQ(10, rec[0].v0);

    /*  DB_OP_MAX_PREFIX21  */
    condV4 = 10;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_MAX_PREFIX21, (uint8_t *)"abccc3zz", strlen("abccc3zz"));
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 4, DB_OP_LARGEREQUAL, (uint8_t *)&condV4, 1);
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(2, selBuff.ulRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);  // 第1条
    EXPECT_STREQ("abccc3", rec[0].v1);
    EXPECT_EQ(13, rec[0].v4);

    /*  DB_OP_MIN_PREFIX21  */
    condV4 = 50;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_MIN_PREFIX21, (uint8_t *)"aac10xxzz", strlen("aac10xxzz"));
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 4, DB_OP_LESSEQUAL, (uint8_t *)&condV4, 1);
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(1, selBuff.ulRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);  // 第1条
    EXPECT_STREQ("aac10xx", rec[0].v1);
    EXPECT_EQ(30, rec[0].v4);

    DBDqlTestDqlCleanData(ulDbId, &selBuff);
}

// string post prefix21 查询所有数据，无字段投影
TEST_F(StSimpRelDBDQL, DBDqlTestSelectAllRec8)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 2};                          // 条件数量
    DBDqlTestDqlRecordT rec[3];                                      // 用于查询结果校验
    uint32_t condV0;

    /*  匹配第1个索引  */
    condV0 = 0;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGEREQUAL, (uint8_t *)&condV0, 4);
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 1, DB_OP_POSTFIX21, (uint8_t *)"xxaac3xx", strlen("xxaac3xx"));
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(1, selBuff.ulRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);  // 第1条
    ASSERT_STREQ("aac3xx", rec[0].v1);

    condV0 = 100;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_MAX_LESS_EQUAL, (uint8_t *)&condV0, 4);
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 1, DB_OP_MAX_POSTFIX21, (uint8_t *)"xxxxyz14cba", strlen("xxxxyz14cba"));
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(1, selBuff.ulRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);  // 第1条
    ASSERT_EQ(34, rec[0].v0);
    ASSERT_STREQ("xxyz14cba", rec[0].v1);

    condV0 = 10;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_MIN_LARGER_EQUAL, (uint8_t *)&condV0, 4);
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 1, DB_OP_MAX_POSTFIX21, (uint8_t *)"xxxxyz14cba", strlen("xxxxyz14cba"));
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(1, selBuff.ulRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);  // 第1条
    ASSERT_EQ(14, rec[0].v0);
    ASSERT_STREQ("xxyz14cba", rec[0].v1);

    DBDqlTestDqlCleanData(ulDbId, &selBuff);
}

void DBDqlTestDqlPrintResultInfo(Status ret, DB_BUF_STRU *pstBufData)
{
#ifdef TEST_DQL_NOPRINT_RET
    return;
#endif
    printf("===================================\n");
    printf("exec return : %#x\n", ret);
    printf("rec num     : %u\n", pstBufData->ulRecNum);
    printf("record len  : %u\n", pstBufData->usRecLen);
    printf("buffer len  : %u\n", pstBufData->ulBufLen);
}

// 全表扫描查询异常场景
TEST_F(StSimpRelDBDQL, DBDqlTestSelectAbnormalScenario1)
{
    Status ret = GMERR_OK;
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_FIELDFILTER_STRU pstFldFilter = {.ucFieldNum = DB_FIELD_ALL};

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 1};                          // 条件数量
    uint32_t condV4;

    // 1. 预期查不到数据
    condV4 = 50;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 4, DB_OP_LARGEREQUAL, (uint8_t *)&condV4, 1);
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ret = DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &pstFldFilter, &selBuff);
    DBDqlTestDqlPrintResultInfo(ret, &selBuff);
    ASSERT_EQ(VOS_ERRNO_DB_RECNOTEXIST, ret);

    // 2. 预期查询到6条数据
    condV4 = 48;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 4, DB_OP_LARGEREQUAL, (uint8_t *)&condV4, 1);

    // 2.1 limit cnt设置为DB_SELECT_ALL，预期能够返回符合的数据量，预期6条
    DBDqlTestDqlResetSelectBuf(&selBuff, 100, DB_SELECT_ALL);  // 需要buf长度6*27，只设置100，buf长度不够
    ret = DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &pstFldFilter, &selBuff);
    DBDqlTestDqlPrintResultInfo(ret, &selBuff);
    ASSERT_EQ(VOS_ERRNO_DB_BUFNOTENOUGH, ret);
    ASSERT_EQ(6, selBuff.ulRecNum);  // 预期能够返回所有满足数据的条数

    // 2.2 limit cnt不设置为DB_SELECT_ALL
    uint32_t limitCnt = 20;                               // 需大于 100/27 才会报错
    DBDqlTestDqlResetSelectBuf(&selBuff, 100, limitCnt);  // buf长度不够
    ret = DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &pstFldFilter, &selBuff);
    DBDqlTestDqlPrintResultInfo(ret, &selBuff);
    ASSERT_EQ(VOS_ERRNO_DB_BUFNOTENOUGH, ret);
    ASSERT_EQ(selBuff.ulRecNum, limitCnt);  // 预期不修改入参的查询数据量

    // 3. limit record num设置为0
    DBDqlTestDqlResetSelectBuf(&selBuff, 100, 0);
    ret = DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &pstFldFilter, &selBuff);
    DBDqlTestDqlPrintResultInfo(ret, &selBuff);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, selBuff.ulRecNum);

    DBDqlTestDqlCleanData(ulDbId, &selBuff);
}

// 索引查询异常场景
TEST_F(StSimpRelDBDQL, DBDqlTestSelectAbnormalScenario2)
{
    Status ret = GMERR_OK;
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_FIELDFILTER_STRU pstFldFilter = {.ucFieldNum = DB_FIELD_ALL};

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 1};                          // 条件数量
    uint32_t condV0;

    // 1.预期查不到数据
    condV0 = 200;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGEREQUAL, (uint8_t *)&condV0, 4);
    DBDqlTestDqlResetSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 查询前重置 result buf
    ret = DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &pstFldFilter, &selBuff);
    DBDqlTestDqlPrintResultInfo(ret, &selBuff);
    ASSERT_EQ(VOS_ERRNO_DB_RECNOTEXIST, ret);

    // 2.预期查询到10条数据
    condV0 = 141;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGEREQUAL, (uint8_t *)&condV0, 4);

    // 2.1 limit cnt设置为DB_SELECT_ALL，预期能够返回符合的数据量
    DBDqlTestDqlResetSelectBuf(&selBuff, 200, DB_SELECT_ALL);  // 需要270，只设置200，buf长度不够
    ret = DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &pstFldFilter, &selBuff);
    DBDqlTestDqlPrintResultInfo(ret, &selBuff);
    ASSERT_EQ(VOS_ERRNO_DB_BUFNOTENOUGH, ret);
    ASSERT_EQ(10, selBuff.ulRecNum);  // 预期能够

    // 2.2 limit cnt不为DB_SELECT_ALL
    uint32_t limitCnt = 100;                              // 需大于 200/27 才会报错
    DBDqlTestDqlResetSelectBuf(&selBuff, 200, limitCnt);  // buf长度不够
    ret = DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &pstFldFilter, &selBuff);
    DBDqlTestDqlPrintResultInfo(ret, &selBuff);
    ASSERT_EQ(VOS_ERRNO_DB_BUFNOTENOUGH, ret);
    ASSERT_EQ(selBuff.ulRecNum, limitCnt);  // 预期不更改入参查询数据量

    // 3. limit record num设置为0
    DBDqlTestDqlResetSelectBuf(&selBuff, 200, 0);
    ret = DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &pstFldFilter, &selBuff);
    DBDqlTestDqlPrintResultInfo(ret, &selBuff);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, selBuff.ulRecNum);

    DBDqlTestDqlCleanData(ulDbId, &selBuff);
}

// DB_CountMatchingRecs 没有满足条件的数据
TEST_F(StSimpRelDBDQL, DBDqlTestCountMatchingRecs1)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_COND_STRU stCond = {.usCondNum = 3};  // 条件数量
    uint32_t condV0, condV2, condV4;

    /*  匹配第2个索引  */
    condV0 = 21;
    condV2 = 129;
    condV4 = 30;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 2, DB_OP_EQUAL, (uint8_t *)&condV2, 4);
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 4, DB_OP_EQUAL, (uint8_t *)&condV4, 1);
    DBDqlTestDqlInitOneCond(&stCond.aCond[2], 0, DB_OP_EQUAL, (uint8_t *)&condV0, 4);
    uint32_t matchCnt = 0;
    Status ret = DB_CountMatchingRecs(ulDbId, usRelId, &stCond, &matchCnt);
    ASSERT_EQ(VOS_ERRNO_DB_RECNOTEXIST, ret);

    DBDqlTestDqlCleanData(ulDbId, NULL);
}

// DB_CountMatchingRecs 正常获取
TEST_F(StSimpRelDBDQL, DBDqlTestCountMatchingRecs2)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_COND_STRU stCond = {.usCondNum = 1};  // 条件数量
    uint32_t condV0 = 21;                    // F1 >= 21
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGEREQUAL, (uint8_t *)&condV0, 4);
    uint32_t matchCnt = 0;
    Status ret = DB_CountMatchingRecs(ulDbId, usRelId, &stCond, &matchCnt);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(130, matchCnt);

    DBDqlTestDqlCleanData(ulDbId, NULL);
}

// DB_CountMatchingRecs 复杂查询
TEST_F(StSimpRelDBDQL, DBDqlTestCountMatchingRecs3)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_COND_STRU stCond = {.usCondNum = 1};  // 条件数量
    uint8_t condV4 = 23;
    // F4 (min) >= 23，大于23的有81条，等于23的有3条，最后预期结果为3
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 4, DB_OP_MIN_LARGER_EQUAL, (uint8_t *)&condV4, 1);
    uint32_t matchCnt = 0;
    Status ret = DB_CountMatchingRecs(ulDbId, usRelId, &stCond, &matchCnt);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(3, matchCnt);

    DBDqlTestDqlCleanData(ulDbId, NULL);
}

// DB_RecordExist 接口
TEST_F(StSimpRelDBDQL, DBDqlTestRecordExist1)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_COND_STRU stCond = {.usCondNum = 1};  // 条件数量
    uint8_t condV4 = 23;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 4, DB_OP_MIN_LARGER_EQUAL, (uint8_t *)&condV4, 1);
    Status ret = DB_RecordExist(ulDbId, usRelId, &stCond);
    ASSERT_EQ(GMERR_OK, ret);

    condV4 = 50;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 4, DB_OP_LARGEREQUAL, (uint8_t *)&condV4, 1);
    ret = DB_RecordExist(ulDbId, usRelId, &stCond);
    ASSERT_EQ(VOS_ERRNO_DB_RECNOTEXIST, ret);

    DBDqlTestDqlCleanData(ulDbId, NULL);
}

// DB_SelectAllRecByOrderEx 接口，正常查询
TEST_F(StSimpRelDBDQL, DBDqlTestSelectAllRecByOrder1)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    const uint8_t sortNum = 2;
    T_FIELD sortFields[sortNum] = {4, 0};
    DB_SORT_STRU stSort = {
        .ucSortNum = sortNum, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND, .pSortFields = sortFields};

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 1};                          // 条件数量

    uint32_t condV = 95;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGER, (uint8_t *)&condV, 4);  // F0 >= 95, 预期55条
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecByOrderEx(ulDbId, usRelId, &stSort, &stCond, &g_stFldFilter, &selBuff));
    ASSERT_EQ(55, selBuff.ulRecNum);
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    DBDqlTestDqlRecordT rec[1];  // 用于查询结果校验
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);
    ASSERT_EQ(49, rec[0].v4);

    DBDqlTestDqlCleanData(ulDbId, NULL);
}

// DB_SelectAllRecByOrderEx 接口，带复杂查询
TEST_F(StSimpRelDBDQL, DBDqlTestSelectAllRecByOrder2)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    const uint8_t sortNum = 2;
    T_FIELD sortFields[sortNum] = {4, 0};
    DB_SORT_STRU stSort = {
        .ucSortNum = sortNum, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND, .pSortFields = sortFields};

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 1};                          // 条件数量

    uint32_t condV = 30;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 4, DB_OP_MIN_LARGER, (uint8_t *)&condV, 1);  // F4 (min)> 30, 预期3条
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecByOrderEx(ulDbId, usRelId, &stSort, &stCond, &g_stFldFilter, &selBuff));
    ASSERT_EQ(3, selBuff.ulRecNum);

    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    DBDqlTestDqlRecordT rec[2];  // 用于查询结果校验
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);
    ASSERT_EQ(131, rec[0].v0);
    ASSERT_EQ(31, rec[0].v4);
    DBDqlTestDqlGetOneRecFromBuf(&rec[1], &selBuff, 2);
    ASSERT_EQ(81, rec[1].v0);
    ASSERT_EQ(31, rec[1].v4);

    DBDqlTestDqlCleanData(ulDbId, NULL);
}

// DB_SelectAllRecByOrderEx 接口，同一字段多个条件
TEST_F(StSimpRelDBDQL, DBDqlTestSelectAllRecByOrder3)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    const uint8_t sortNum = 2;
    T_FIELD sortFields[sortNum] = {4, 0};
    DB_SORT_STRU stSort = {
        .ucSortNum = sortNum, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND, .pSortFields = sortFields};

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 5};                          // 条件数量

    uint32_t condV0a = 30;
    uint32_t condV0b = 85;
    uint32_t condV2 = 100;
    uint32_t condV4a = 30;
    uint32_t condV4b = 40;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGER, (uint8_t *)&condV0a, 4);     // F0 > 30
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 0, DB_OP_LESSEQUAL, (uint8_t *)&condV0b, 4);  // F0 <= 85
    DBDqlTestDqlInitOneCond(&stCond.aCond[2], 2, DB_OP_LARGER, (uint8_t *)&condV2, 4);      // F2 > 100
    DBDqlTestDqlInitOneCond(&stCond.aCond[3], 4, DB_OP_LARGER, (uint8_t *)&condV4a, 1);     // F4 > 30
    DBDqlTestDqlInitOneCond(&stCond.aCond[4], 4, DB_OP_LESS, (uint8_t *)&condV4b, 1);       // F4 < 40
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecByOrderEx(ulDbId, usRelId, &stSort, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(9, selBuff.ulRecNum);

    DBDqlTestDqlRecordT rec[2];  // 用于查询结果校验
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);
    ASSERT_EQ(39, rec[0].v4);
    DBDqlTestDqlGetOneRecFromBuf(&rec[1], &selBuff, 9);
    ASSERT_EQ(31, rec[1].v4);

    DBDqlTestDqlCleanData(ulDbId, NULL);
}

// DB_SelectAllRecByOrderEx接口，同一字段多个条件
TEST_F(StSimpRelDBDQL, DBDqlTestSelectAllRecByOrder4)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    const uint8_t sortNum = 2;
    T_FIELD sortFields[sortNum] = {4, 0};
    DB_SORT_STRU stSort = {
        .ucSortNum = sortNum, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND, .pSortFields = sortFields};

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, MAX_BUFLEN, DB_SELECT_ALL);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 5};                          // 条件数量

    uint32_t condV0a = 30;
    uint32_t condV0b = 85;
    uint32_t condV2 = 100;
    uint32_t condV4a = 30;
    uint32_t condV4b = 40;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGER, (uint8_t *)&condV0a, 4);     // F0 > 30
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 0, DB_OP_LESSEQUAL, (uint8_t *)&condV0b, 4);  // F0 <= 85
    DBDqlTestDqlInitOneCond(&stCond.aCond[2], 2, DB_OP_LARGER, (uint8_t *)&condV2, 4);      // F2 > 100
    DBDqlTestDqlInitOneCond(&stCond.aCond[3], 4, DB_OP_LARGER, (uint8_t *)&condV4a, 1);     // F4 > 30
    DBDqlTestDqlInitOneCond(&stCond.aCond[4], 4, DB_OP_LESS, (uint8_t *)&condV4b, 1);       // F4 < 40
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecByOrderEx(ulDbId, usRelId, &stSort, &stCond, &g_stFldFilter, &selBuff));
    DBDqlTestDqlPrintSelectResult(&stCond, &selBuff);
    ASSERT_EQ(9, selBuff.ulRecNum);

    DB_DSBUF_STRU dsBuf;
    DBDqlTestDqlInitSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 初始化存储结果buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRecByOrder(ulDbId, usRelId, &stSort, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(9, dsBuf.usRecNum);

    ASSERT_EQ(0, memcmp(selBuff.pBuf, dsBuf.StdBuf.pucData, dsBuf.StdBuf.ulActLen));

    DBDqlTestDqlRecordT rec[2];  // 用于查询结果校验
    DBDqlTestDqlGetOneRecFromBuf(&rec[0], &selBuff, 1);
    ASSERT_EQ(39, rec[0].v4);
    DBDqlTestDqlGetOneRecFromBuf(&rec[1], &selBuff, 9);
    ASSERT_EQ(31, rec[1].v4);

    free(dsBuf.StdBuf.pucData);
    DBDqlTestDqlCleanData(ulDbId, &selBuff);
}

// DB_SelectFirstRec接口
TEST_F(StSimpRelDBDQL, DBDqlTestSelectFirstRec1)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_DSBUF_STRU dsBuf;
    DBDqlTestDqlInitSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 4};                              // 条件数量

    uint32_t condV0a = 30;
    uint32_t condV0b = 100;
    uint32_t condV2 = 100;
    uint32_t condV4 = 35;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGER, (uint8_t *)&condV0a, 4);     // F0 > 30
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 0, DB_OP_LESSEQUAL, (uint8_t *)&condV0b, 4);  // F0 <= 100
    DBDqlTestDqlInitOneCond(&stCond.aCond[2], 2, DB_OP_LARGER, (uint8_t *)&condV2, 4);      // F2 > 100
    DBDqlTestDqlInitOneCond(&stCond.aCond[3], 4, DB_OP_LARGER, (uint8_t *)&condV4, 1);      // F4 > 35

    ASSERT_EQ(GMERR_OK, DB_SelectFirstRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(1, dsBuf.usRecNum);

    uint32_t recNum = 0;  // 预期14条
    ASSERT_EQ(GMERR_OK, DB_CountMatchingRecs(ulDbId, usRelId, &stCond, &recNum));
    ASSERT_EQ(14, recNum);
    DBDqlTestDqlCleanData(ulDbId, &dsBuf, true);
}

// DB_SelectFirstRec接口，添加投影
TEST_F(StSimpRelDBDQL, DBDqlTestSelectFirstRec2)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_DSBUF_STRU dsBuf;
    DBDqlTestDqlInitSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 2};                              // 条件数量

    // 设置投影1，2，4三个字段
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = 3};
    stFldFilter.aucField[0] = 1;
    stFldFilter.aucField[1] = 2;
    stFldFilter.aucField[2] = 4;

    uint32_t condV2 = 100;
    uint32_t condV4 = 35;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 2, DB_OP_LARGER, (uint8_t *)&condV2, 4);  // F2 > 100
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 4, DB_OP_LARGER, (uint8_t *)&condV4, 1);  // F4 > 35

    ASSERT_EQ(GMERR_OK, DB_SelectFirstRec(ulDbId, usRelId, &stCond, &stFldFilter, &dsBuf));
    uint8_t *bufCursor = dsBuf.StdBuf.pucData;
    printf("| F1 = %s | F2 = %u | F4 = %u |\n", (char *)bufCursor, *(uint32_t *)(bufCursor + 16),
        *(uint8_t *)(bufCursor + 20));
    ASSERT_EQ(1, dsBuf.usRecNum);
    ASSERT_EQ(21, dsBuf.usRecLen);                  // 3个字段长度为21(string 16, uint32_t 4, uint8_t 1)
    ASSERT_STREQ("xxyz16cba", (char *)bufCursor);   // F1
    ASSERT_EQ(114, *(uint32_t *)(bufCursor + 16));  // F2

    DBDqlTestDqlCleanData(ulDbId, &dsBuf, true);
}

// DB_SelectFirstRec接口, postfix, like, notlike
TEST_F(StSimpRelDBDQL, DBDqlTestSelectFirstRec3)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_DSBUF_STRU dsBuf;
    DBDqlTestDqlInitSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 1};                              // 条件数量

    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_POSTFIX21, (uint8_t *)"zzabccc3", strlen("zzabccc3"));
    ASSERT_EQ(GMERR_OK, DB_SelectFirstRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(1, dsBuf.usRecNum);

    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_LIKE, (uint8_t *)"%8%", strlen("%8%"));
    DBDqlTestDqlResetSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 重置结果buf
    ASSERT_EQ(GMERR_OK, DB_SelectFirstRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(1, dsBuf.usRecNum);

    DBDqlTestDqlResetSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 重置结果buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(15, dsBuf.usRecNum);  // 预期15条

    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_NOTLIKE, (uint8_t *)"%xx%", strlen("%xx%"));
    DBDqlTestDqlResetSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 重置结果buf
    ASSERT_EQ(GMERR_OK, DB_SelectFirstRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(1, dsBuf.usRecNum);

    DBDqlTestDqlResetSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 重置结果buf
    ASSERT_EQ(GMERR_OK, DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(50, dsBuf.usRecNum);  // 预期50条

    DBDqlTestDqlCleanData(ulDbId, &dsBuf, true);
}

TEST_F(StSimpRelDBDQL, DBDqlTestSelectFirstRec4)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_DSBUF_STRU dsBuf;
    DBDqlTestDqlInitSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 1};                              // 条件数量

    uint8_t condV0a = 30;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 4, DB_OP_MAX_LESS, (uint8_t *)&condV0a, 1);

    ASSERT_EQ(GMERR_OK, DB_SelectFirstRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(1, dsBuf.usRecNum);
    DBDqlTestDqlCleanData(ulDbId, &dsBuf, true);
}

void DBDqlTestDqlSelectStringOpPrepareData(uint32_t *dbId, uint16_t *relId)
{
    CommonDBCreateDB4Test(NULL, dbId);
    DBDqlTestDqlCreateTbl(*dbId, relId);

    const uint32_t recNum = 12;
    const uint32_t base[recNum] = {59, 39, 23, 12, 35, 90, 21, 17, 77, 8, 22, 66};
    const char v1s[recNum][16] = {"AB%%CC", "XY%ZZ", "ABCDEFGHIJK", "ABCDZFGHIJ", "ABCDEFGH", "ABCDEFG", "APQRS",
        "XY__Z", "MNOPQRSTU", "GHIJKLMNOP", "MNOP", "ABCKKMNOP"};
    /*
        |Rec No|  F0  |      F1     |  F2  |  F3  |  F4  |
        |------|------|-------------|------|------|------|
        |NO.1  |  59  |  AB%%CC     |  59  |  66  |  19  |
        |NO.2  |  39  |  XY%ZZ      |  39  |  22  |  19  |
        |NO.3  |  23  |  ABCDEFGHIJK|  23  |  8   |  3   |
        |NO.4  |  12  |  ABCDZFGHIJ |  12  |  77  |  12  |
        |NO.5  |  35  |  ABCDEFGH   |  35  |  17  |  15  |
        |NO.6  |  90  |  ABCDEFG    |  90  |  21  |  10  |
        |NO.7  |  21  |  APQRS      |  59  |  90  |  1   |
        |NO.8  |  17  |  XY__Z      |  39  |  35  |  17  |
        |NO.9  |  77  |  MNOPQRSTU  |  23  |  12  |  17  |
        |NO.10 |  8   |  GHIJKLMNOP |  12  |  23  |  8   |
        |NO.11 |  22  |  MNOP       |  35  |  39  |  2   |
        |NO.12 |  66  |  ABCKKMNOP  |  90  |  59  |  6   |
    */
    for (uint32_t i = 0; i < recNum; i++) {
        DBDqlTestDqlRecordT record = {.v0 = base[i],
            .v1 = {0},
            .v2 = base[i % 6],
            .v3 = (uint16_t)base[recNum - (i + 1)],
            .v4 = (uint8_t)(base[i] % 20)};
        (void)memcpy_s(record.v1, F1_LEN, v1s[i], F1_LEN);
        DB_DSBUF_STRU insertBuf = {.usRecLen = 0,
            .usRecNum = 0,
            .StdBuf = {.ulBufLen = REC_LEN, .ulActLen = REC_LEN, .pucData = (uint8_t *)&record}};
        ASSERT_EQ(GMERR_OK, DB_InsertRec(*dbId, *relId, &insertBuf));
    }
}

// string like & notlike
TEST_F(StSimpRelDBDQL, DBDqlTestSelectStringOp1)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlSelectStringOpPrepareData(&ulDbId, &usRelId);

    DB_DSBUF_STRU dsBuf;
    DBDqlTestDqlInitSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 1};                              // 条件数量

    DBDqlTestDqlResetSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 重置结果buf
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_LIKE, (uint8_t *)"%CD%I%", strlen("%CD%I%"));
    ASSERT_EQ(GMERR_OK, DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(2, dsBuf.usRecNum);

    DBDqlTestDqlResetSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 重置结果buf
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_NOTLIKE, (uint8_t *)"%CD%I%", strlen("%CD%I%"));
    ASSERT_EQ(GMERR_OK, DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(10, dsBuf.usRecNum);

    DBDqlTestDqlResetSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 重置结果buf
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_LIKE, (uint8_t *)"_BC%", strlen("_BC%"));
    ASSERT_EQ(GMERR_OK, DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(5, dsBuf.usRecNum);

    DBDqlTestDqlResetSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 重置结果buf
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_NOTLIKE, (uint8_t *)"_BC%", strlen("_BC%"));
    ASSERT_EQ(GMERR_OK, DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(7, dsBuf.usRecNum);

    DBDqlTestDqlResetSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 重置结果buf
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_LIKE, (uint8_t *)"%%%", strlen("%%%"));
    ASSERT_EQ(GMERR_OK, DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(12, dsBuf.usRecNum);

    DBDqlTestDqlResetSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 重置结果buf
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_LIKE, (uint8_t *)"%__%", strlen("%__%"));
    ASSERT_EQ(GMERR_OK, DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(12, dsBuf.usRecNum);

    DBDqlTestDqlCleanData(ulDbId, &dsBuf, true);
}

// string haveprefix & prefix12 & prefix21 & prefix & postfix21
TEST_F(StSimpRelDBDQL, DBDqlTestSelectStringOp2)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlSelectStringOpPrepareData(&ulDbId, &usRelId);

    DB_DSBUF_STRU dsBuf;
    DBDqlTestDqlInitSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 1};                              // 条件数量

    DBDqlTestDqlResetSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 重置结果buf
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_HAVEPREFIX, (uint8_t *)"ABCD", strlen("ABCD"));
    ASSERT_EQ(GMERR_OK, DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(4, dsBuf.usRecNum);

    DBDqlTestDqlResetSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 重置结果buf
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_PREFIX12, (uint8_t *)"ABCD", strlen("ABCD"));
    ASSERT_EQ(GMERR_OK, DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(4, dsBuf.usRecNum);

    DBDqlTestDqlResetSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 重置结果buf
    DBDqlTestDqlInitOneCond(
        &stCond.aCond[0], 1, DB_OP_PREFIX21, (uint8_t *)"ABCDEFGHIJKLMNOP", strlen("ABCDEFGHIJKLMNOP"));
    ASSERT_EQ(GMERR_OK, DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(3, dsBuf.usRecNum);

    DBDqlTestDqlResetSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 重置结果buf
    DBDqlTestDqlInitOneCond(
        &stCond.aCond[0], 1, DB_OP_PREFIX, (uint8_t *)"ABCDEFGHIJKLMNOP", strlen("ABCDEFGHIJKLMNOP"));
    ASSERT_EQ(GMERR_OK, DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(3, dsBuf.usRecNum);

    DBDqlTestDqlResetSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 重置结果buf
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_POSTFIX21, (uint8_t *)"ABGHIJKLMNOP", strlen("ABGHIJKLMNOP"));
    ASSERT_EQ(GMERR_OK, DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(2, dsBuf.usRecNum);

    DBDqlTestDqlCleanData(ulDbId, &dsBuf, true);
}

// string max (prefix12 prefix21) & min (prefix12 prefix21) & max postfix21
TEST_F(StSimpRelDBDQL, DBDqlTestSelectStringOp3)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlSelectStringOpPrepareData(&ulDbId, &usRelId);

    DB_DSBUF_STRU dsBuf;
    DBDqlTestDqlInitSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 1};                              // 条件数量
    DBDqlTestDqlRecordT rec;                                             // 用于查询结果校验

    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_MAX_PREFIX12, (uint8_t *)"ABCD", strlen("ABCD"));
    ASSERT_EQ(GMERR_OK, DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(1, dsBuf.usRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec, &dsBuf, 1, true);
    ASSERT_STREQ("ABCDZFGHIJ", rec.v1);

    DBDqlTestDqlResetSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 重置结果buf
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_MIN_PREFIX12, (uint8_t *)"ABCD", strlen("ABCD"));
    ASSERT_EQ(GMERR_OK, DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(1, dsBuf.usRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec, &dsBuf, 1, true);
    ASSERT_STREQ("ABCDEFG", rec.v1);

    DBDqlTestDqlResetSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 重置结果buf
    DBDqlTestDqlInitOneCond(
        &stCond.aCond[0], 1, DB_OP_MAX_PREFIX21, (uint8_t *)"ABCDEFGHIJKLMNOP", strlen("ABCDEFGHIJKLMNOP"));
    ASSERT_EQ(GMERR_OK, DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(1, dsBuf.usRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec, &dsBuf, 1, true);
    ASSERT_STREQ("ABCDEFGHIJK", rec.v1);

    DBDqlTestDqlResetSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 重置结果buf
    DBDqlTestDqlInitOneCond(
        &stCond.aCond[0], 1, DB_OP_MIN_PREFIX21, (uint8_t *)"ABCDEFGHIJKLMNOP", strlen("ABCDEFGHIJKLMNOP"));
    ASSERT_EQ(GMERR_OK, DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(1, dsBuf.usRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec, &dsBuf, 1, true);
    ASSERT_STREQ("ABCDEFG", rec.v1);

    DBDqlTestDqlResetSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 重置结果buf
    DBDqlTestDqlInitOneCond(
        &stCond.aCond[0], 1, DB_OP_MAX_POSTFIX21, (uint8_t *)"ABGHIJKLMNOP", strlen("ABGHIJKLMNOP"));
    ASSERT_EQ(GMERR_OK, DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(1, dsBuf.usRecNum);
    DBDqlTestDqlGetOneRecFromBuf(&rec, &dsBuf, 1, true);
    ASSERT_STREQ("MNOP", rec.v1);

    DBDqlTestDqlCleanData(ulDbId, &dsBuf, true);
}

void DBDqlTestDqlCompareIdxAndSequencePrapreData(uint32_t *dbId, uint16_t *relId, uint32_t *bases, uint32_t recNum)
{
    CommonDBCreateDB4Test(NULL, dbId);
    DB_REL_DEF_STRU stRelDef;
    ASSERT_EQ(GMERR_OK,
        StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/dql/testDqlCompareIdxAndSequence.json", &stRelDef, NULL));
    ASSERT_EQ(GMERR_OK, DB_CreateTbl(*dbId, &stRelDef, relId));
    uint32_t recLen = sizeof(uint32_t) * stRelDef.ulNCols;
    StUtilFreeAllRelDef(&stRelDef);

    srand((unsigned)time(NULL));
    for (uint32_t i = 0; i < recNum; i++) {
        uint32_t base = rand() % 200000;
        uint32_t record[8] = {i, base, base, base + 10000, base, base % 10000, base, base % 3};

        DB_DSBUF_STRU insertBuf = {
            .usRecLen = 0, .usRecNum = 0, .StdBuf = {.ulBufLen = 0, .ulActLen = recLen, .pucData = (uint8_t *)&record}};
        ASSERT_EQ(GMERR_OK, DB_InsertRec(*dbId, *relId, &insertBuf));
        bases[i] = base;
    }
}

TEST_F(StSimpRelDBDQL, DBDqlTestDqlCompareIdxAndSequence)
{
    Status ret = GMERR_OK;
    const uint32_t recNum = 10000;
    uint32_t bases[recNum] = {0};
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlCompareIdxAndSequencePrapreData(&ulDbId, &usRelId, bases, recNum);

    const uint32_t bufLen = 3200;
    DB_DSBUF_STRU dsBuf;
    DBDqlTestDqlInitSelectBuf(&dsBuf, bufLen, DB_SELECT_ALL, true);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 1};                          // 条件数量

    PerfStruT idxPerf;
    TestInitPerfStru(&idxPerf);
    for (uint32_t i = 0; i < recNum; i++) {
        DBDqlTestDqlResetSelectBuf(&dsBuf, bufLen, DB_SELECT_ALL, true);  // 重置结果buf
        DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_EQUAL, (uint8_t *)&bases[i], sizeof(uint32_t));
        GET_START_TIME(idxPerf);
        ret = DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf);
        GET_CYCLE_TIME(idxPerf);
        ASSERT_EQ(GMERR_OK, ret);
    }
    printf("Index scan cost time     : %.4f ms\n", GET_TOTAL_TIME(idxPerf));

    PerfStruT sequencePerf;
    TestInitPerfStru(&sequencePerf);
    for (uint32_t i = 0; i < recNum; i++) {
        DBDqlTestDqlResetSelectBuf(&dsBuf, bufLen, DB_SELECT_ALL, true);  // 重置结果buf
        DBDqlTestDqlInitOneCond(&stCond.aCond[0], 4, DB_OP_EQUAL, (uint8_t *)&bases[i], sizeof(uint32_t));
        GET_START_TIME(sequencePerf);
        ret = DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf);
        GET_CYCLE_TIME(sequencePerf);
        ASSERT_EQ(GMERR_OK, ret);
    }
    printf("Sequence scan cost time : %.4f ms\n", GET_TOTAL_TIME(sequencePerf));

    DBDqlTestDqlCleanData(ulDbId, &dsBuf, true);
}

TEST_F(StSimpRelDBDQL, DBDqlTestStUtilSelectIndex)
{
    DB_REL_DEF_STRU stRelDef;
    ASSERT_EQ(GMERR_OK,
        StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/dql/testDqlCompareIdxAndSequence.json", &stRelDef, NULL));

    uint32_t bases[10] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    StIdxMatchStruT idxMatchStru = {0};
    DB_COND_STRU stCond = {.usCondNum = 1};  // 条件数量
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 4, DB_OP_EQUAL, (uint8_t *)&bases[1], sizeof(uint32_t));
    StUtilSelectBestIndexByCond(&stRelDef, &stCond, &idxMatchStru);
    StUtilPrintIdxMatchStru(&idxMatchStru);

    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_EQUAL, (uint8_t *)&bases[1], sizeof(uint32_t));
    StUtilSelectBestIndexByCond(&stRelDef, &stCond, &idxMatchStru);
    StUtilPrintIdxMatchStru(&idxMatchStru);

    stCond.usCondNum = 3;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 6, DB_OP_EQUAL, (uint8_t *)&bases[1], sizeof(uint32_t));
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 3, DB_OP_EQUAL, (uint8_t *)&bases[3], sizeof(uint32_t));
    DBDqlTestDqlInitOneCond(&stCond.aCond[2], 2, DB_OP_LARGER, (uint8_t *)&bases[9], sizeof(uint32_t));
    StUtilSelectBestIndexByCond(&stRelDef, &stCond, &idxMatchStru);
    StUtilPrintIdxMatchStru(&idxMatchStru);
    StUtilFreeAllRelDef(&stRelDef);
}

TEST_F(StSimpRelDBDQL, DBDqlTestIdxSelectAllRecLimitCnt)
{
    Status ret = GMERR_OK;
    const uint32_t recNum = 10;
    uint32_t bases[recNum] = {0};
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlCompareIdxAndSequencePrapreData(&ulDbId, &usRelId, bases, recNum);

    const uint32_t bufLen = 160;
    DB_DSBUF_STRU dsBuf;
    DBDqlTestDqlInitSelectBuf(&dsBuf, bufLen, 5, true);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 1};              // 条件数量

    uint32_t data = 0;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_LARGEREQUAL, (uint8_t *)&data, sizeof(uint32_t));
    ret = DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf);
    ASSERT_EQ(GMERR_OK, ret);

    DBDqlTestDqlCleanData(ulDbId, &dsBuf, true);
}

typedef struct DBDqlStLongStringRec {
    uint32_t f0;
    char f1[300];
} DBDqlStLongStringRecT;

void DBDqlTestDqlLongStringPrepareData(uint32_t *dbId, uint16_t *relId, uint32_t recNum)
{
    CommonDBCreateDB4Test(NULL, dbId);
    DB_REL_DEF_STRU stRelDef;
    ASSERT_EQ(GMERR_OK, StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/dql/testDqlLongString.json", &stRelDef, NULL));
    ASSERT_EQ(GMERR_OK, DB_CreateTbl(*dbId, &stRelDef, relId));
    StUtilFreeAllRelDef(&stRelDef);

    uint32_t recLen = sizeof(DBDqlStLongStringRecT);
    const uint32_t f1DefLen = 299;
    for (uint32_t i = 0; i < recNum; i++) {
        DBDqlStLongStringRecT record = {.f0 = i};
        char value = (char)(i % 26 + 'a');
        (void)memset_s(record.f1, sizeof(record.f1), value, sizeof(record.f1));
        record.f1[f1DefLen] = '\0';
        DB_DSBUF_STRU insertBuf = {
            .usRecLen = 0, .usRecNum = 0, .StdBuf = {.ulBufLen = 0, .ulActLen = recLen, .pucData = (uint8_t *)&record}};
        ASSERT_EQ(GMERR_OK, DB_InsertRec(*dbId, *relId, &insertBuf));
    }
}

// 长string类型数据查询
TEST_F(StSimpRelDBDQL, DBDqlTestLongStringSelect)
{
    Status ret = GMERR_OK;
    const uint32_t recNum = 50;
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlLongStringPrepareData(&ulDbId, &usRelId, recNum);
    DB_BUF_STRU selBuff;
    uint32_t limitCnt = 100;
    DBDqlTestDqlInitSelectBuf(&selBuff, 4000, limitCnt);

    DB_COND_STRU stCond = {.usCondNum = 1};
    stCond.aCond[0].ucFieldId = 1;
    stCond.aCond[0].enOp = DB_OP_EQUAL;
    uint8_t condValue[300];
    (void)memset_s(condValue, sizeof(condValue), 'a', sizeof(condValue));
    condValue[299] = '\0';
    *(VOS_UINTPTR *)(stCond.aCond[0].aucValue) = (VOS_UINTPTR)condValue;

    ret = DB_SelectAllRecEx(ulDbId, usRelId, &stCond, &g_stFldFilter, &selBuff);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(2, selBuff.ulRecNum);
    DBDqlTestDqlCleanData(ulDbId, &selBuff);
}

// 长string类型数据fetch查询
TEST_F(StSimpRelDBDQL, DBDqlTestLongStringFetchSelect)
{
    Status ret = GMERR_OK;
    const uint32_t recNum = 50;
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlLongStringPrepareData(&ulDbId, &usRelId, recNum);

    DB_COND_STRU stCond = {.usCondNum = 2};
    stCond.aCond[0].ucFieldId = 0;
    stCond.aCond[0].enOp = DB_OP_LARGEREQUAL;
    *(uint32_t *)stCond.aCond[0].aucValue = 0;  // f0 >= 0

    stCond.aCond[1].ucFieldId = 1;
    stCond.aCond[1].enOp = DB_OP_LESSEQUAL;
    uint8_t cond1Value[300];
    (void)memset_s(cond1Value, sizeof(cond1Value), 'e', sizeof(cond1Value));
    *(VOS_UINTPTR *)(stCond.aCond[1].aucValue) = (VOS_UINTPTR)cond1Value;  // f1 <= "eeeee...[299]"

    DB_SELHANDLE phSelect;
    ret = DB_BeginSelect(ulDbId, usRelId, &stCond, &g_stFldFilter, &phSelect);
    ASSERT_EQ(GMERR_OK, ret);

    DB_DSBUF_STRU dsBuff;
    uint32_t limitCnt = 1;
    DBDqlTestDqlInitSelectBuf(&dsBuff, 500, limitCnt, true);  // 一批查询一条

    uint32_t fetchCnt = 0;
    while (ret == GMERR_OK) {
        ret = DB_FetchSelectRec(phSelect, &dsBuff);
        if (ret == GMERR_OK) {
            DBDqlStLongStringRecT *rec = (DBDqlStLongStringRecT *)dsBuff.StdBuf.pucData;
            printf("------- Fetch%u -----\n", fetchCnt);
            printf("f0 = %d\nf1 = %s\n", rec->f0, rec->f1);
            if (fetchCnt < 5) {
                ASSERT_EQ(fetchCnt, rec->f0);
            }
            fetchCnt++;
        }
    }
    ASSERT_EQ(10, fetchCnt);

    ret = DB_EndSelect(phSelect);
    ASSERT_EQ(GMERR_OK, ret);
    DBDqlTestDqlCleanData(ulDbId, &dsBuff, true);
}

// query plan 无效参数
TEST_F(StSimpRelDBDQL, DBDqlTestDqlPrepareQueryPlan1)
{
    Status ret = GMERR_OK;
    const uint32_t recNum = 100;
    uint32_t bases[recNum] = {0};
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlCompareIdxAndSequencePrapreData(&ulDbId, &usRelId, bases, recNum);

    uint32_t condV = 0;
    DB_COND_STRU stCond = {0};
    DB_QUERY_PLAN_STRU queryPlan = {.iIndexId = 0xFF};

    ret = DB_PrepareQueryPlan(10, usRelId, &stCond, &queryPlan);
    ASSERT_EQ(VOS_ERRNO_DB_INVALID_DATABASE, ret);

    ASSERT_EQ(GMERR_OK, DB_CloseDB(ulDbId));
    ret = DB_PrepareQueryPlan(ulDbId, usRelId, &stCond, &queryPlan);
    ASSERT_EQ(VOS_ERRNO_DB_DATABASE_NOT_OPENED, ret);

    ASSERT_EQ(GMERR_OK, DB_OpenDB(NULL, (uint8_t *)COMMON_DBNAME, &ulDbId));
    ret = DB_PrepareQueryPlan(ulDbId, 10, &stCond, &queryPlan);
    ASSERT_EQ(VOS_ERRNO_DB_INVALIDREL, ret);

    ret = DB_PrepareQueryPlan(ulDbId, usRelId, NULL, &queryPlan);
    ASSERT_EQ(VOS_ERRNO_DB_NULLPTR, ret);

    ret = DB_PrepareQueryPlan(ulDbId, usRelId, &stCond, NULL);
    ASSERT_EQ(VOS_ERRNO_DB_NULLPTR, ret);

    stCond.usCondNum = 21;
    ret = DB_PrepareQueryPlan(ulDbId, usRelId, &stCond, &queryPlan);
    ASSERT_EQ(VOS_ERRNO_DB_INVALIDCONDITION, ret);

    stCond.usCondNum = 1;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 8, DB_OP_EQUAL, (uint8_t *)&condV, sizeof(uint32_t));
    ret = DB_PrepareQueryPlan(ulDbId, usRelId, &stCond, &queryPlan);
    ASSERT_EQ(VOS_ERRNO_DB_INVALIDFIELD, ret);

    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 5, DB_OP_BUTT, (uint8_t *)&condV, sizeof(uint32_t));
    ret = DB_PrepareQueryPlan(ulDbId, usRelId, &stCond, &queryPlan);
    ASSERT_EQ(VOS_ERRNO_DB_INVALIDOPTYPE, ret);

    DBDqlTestDqlCleanData(ulDbId, NULL);
}

// 正常匹配索引
TEST_F(StSimpRelDBDQL, DBDqlTestDqlPrepareQueryPlan2)
{
    Status ret = GMERR_OK;
    const uint32_t recNum = 100;
    uint32_t bases[recNum] = {0};
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlCompareIdxAndSequencePrapreData(&ulDbId, &usRelId, bases, recNum);

    uint32_t condV = 0;
    DB_COND_STRU stCond = {0};
    DB_QUERY_PLAN_STRU queryPlan = {.iIndexId = 0xFF};

    stCond.usCondNum = 1;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_EQUAL, (uint8_t *)&condV, sizeof(uint32_t));
    ret = DB_PrepareQueryPlan(ulDbId, usRelId, &stCond, &queryPlan);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, queryPlan.iIndexId);

    stCond.usCondNum = 4;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_EQUAL, (uint8_t *)&condV, sizeof(uint32_t));
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 4, DB_OP_EQUAL, (uint8_t *)&condV, sizeof(uint32_t));
    DBDqlTestDqlInitOneCond(&stCond.aCond[2], 3, DB_OP_EQUAL, (uint8_t *)&condV, sizeof(uint32_t));
    DBDqlTestDqlInitOneCond(&stCond.aCond[3], 5, DB_OP_EQUAL, (uint8_t *)&condV, sizeof(uint32_t));
    ret = DB_PrepareQueryPlan(ulDbId, usRelId, &stCond, &queryPlan);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, queryPlan.aucCondRef[0]);
    ASSERT_EQ(0xFF, queryPlan.aucCondRef[1]);
    ASSERT_EQ(1, queryPlan.aucCondRef[2]);
    ASSERT_EQ(2, queryPlan.aucCondRef[3]);
    DBDqlTestDqlCleanData(ulDbId, NULL);
}

// 某字段设置多个条件匹配索引
TEST_F(StSimpRelDBDQL, DBDqlTestDqlPrepareQueryPlan3)
{
    Status ret = GMERR_OK;
    const uint32_t recNum = 100;
    uint32_t bases[recNum] = {0};
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlCompareIdxAndSequencePrapreData(&ulDbId, &usRelId, bases, recNum);

    uint32_t condV = 0;
    DB_COND_STRU stCond = {0};
    DB_QUERY_PLAN_STRU queryPlan = {.iIndexId = 0xFF};

    stCond.usCondNum = 3;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 1, DB_OP_EQUAL, (uint8_t *)&condV, sizeof(uint32_t));
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 3, DB_OP_LARGEREQUAL, (uint8_t *)&condV, sizeof(uint32_t));
    DBDqlTestDqlInitOneCond(&stCond.aCond[2], 3, DB_OP_EQUAL, (uint8_t *)&condV, sizeof(uint32_t));
    ret = DB_PrepareQueryPlan(ulDbId, usRelId, &stCond, &queryPlan);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, queryPlan.aucCondRef[0]);
    ASSERT_EQ(0xFF, queryPlan.aucCondRef[1]);
    ASSERT_EQ(1, queryPlan.aucCondRef[2]);
    ASSERT_EQ(0xFF, queryPlan.aucCondRef[3]);
    DBDqlTestDqlCleanData(ulDbId, NULL);
}

// 没有匹配索引
TEST_F(StSimpRelDBDQL, DBDqlTestDqlPrepareQueryPlan4)
{
    Status ret = GMERR_OK;
    const uint32_t recNum = 100;
    uint32_t bases[recNum] = {0};
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlCompareIdxAndSequencePrapreData(&ulDbId, &usRelId, bases, recNum);

    uint32_t condV = 0;
    DB_COND_STRU stCond = {0};
    DB_QUERY_PLAN_STRU queryPlan = {.iIndexId = 0xFF};

    stCond.usCondNum = 1;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 4, DB_OP_EQUAL, (uint8_t *)&condV, sizeof(uint32_t));
    ret = DB_PrepareQueryPlan(ulDbId, usRelId, &stCond, &queryPlan);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0xFF, queryPlan.iIndexId);
    for (uint32_t i = 0; i < DB_COND_MAX; i++) {
        ASSERT_EQ(queryPlan.aucCondRef[i], 0xFF);
    }
    DBDqlTestDqlCleanData(ulDbId, NULL);
}

// 索引部分字段相同但是顺序不同索引匹配
TEST_F(StSimpRelDBDQL, DBDqlTestDqlPrepareQueryPlan5)
{
    uint32_t ulDbId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    DB_REL_DEF_STRU stRelDef;
    ASSERT_EQ(
        GMERR_OK, StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/dql/testDqlCompareIdxAndSequence.json", &stRelDef));
    uint16_t usRelId;
    // 修改两个索引部分字段相同，但是顺序不同 索引及对应字段改为：{{0}, {1, 3, 5}, {2, 7}, {2, 6, 4}, {6, 2, 3}}
    stRelDef.pstIdxLst[3].ucIdxFldNum = 3;
    stRelDef.pstIdxLst[3].aucFieldID[0] = 2;
    stRelDef.pstIdxLst[3].aucFieldID[1] = 6;
    stRelDef.pstIdxLst[3].aucFieldID[2] = 4;
    ASSERT_EQ(GMERR_OK, DB_CreateTbl(ulDbId, &stRelDef, &usRelId));
    StUtilFreeAllRelDef(&stRelDef);

    uint32_t condV = 0;
    DB_COND_STRU stCond = {0};
    DB_QUERY_PLAN_STRU queryPlan = {.iIndexId = 0xFF};

    stCond.usCondNum = 2;  // 设置条件 F6 = ? && f2 = ?  预期匹配索引3
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 6, DB_OP_EQUAL, (uint8_t *)&condV, sizeof(uint32_t));
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 2, DB_OP_EQUAL, (uint8_t *)&condV, sizeof(uint32_t));
    Status ret = DB_PrepareQueryPlan(ulDbId, usRelId, &stCond, &queryPlan);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(queryPlan.iIndexId, 3);
    ASSERT_EQ(queryPlan.aucCondRef[0], 1);
    ASSERT_EQ(queryPlan.aucCondRef[1], 0);
    ASSERT_EQ(queryPlan.aucCondRef[2], 0xFF);
    DBDqlTestDqlCleanData(ulDbId, NULL);
}

// simple select查询无效条件
TEST_F(StSimpRelDBDQL, DBDqlTestDqlSimpleSelect1)
{
    Status ret = GMERR_OK;
    const uint32_t recNum = 100;
    uint32_t bases[recNum] = {0};
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlCompareIdxAndSequencePrapreData(&ulDbId, &usRelId, bases, recNum);

    uint32_t condV = 50;
    DB_COND_STRU stCond = {0};
    DB_QUERY_PLAN_STRU queryPlan = {.iIndexId = 0xFF};

    stCond.usCondNum = 1;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_EQUAL, (uint8_t *)&condV, sizeof(uint32_t));
    ret = DB_PrepareQueryPlan(ulDbId, usRelId, &stCond, &queryPlan);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, queryPlan.iIndexId);

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, 200, DB_SELECT_ALL);  // 初始化存储结果buf

    // 不支持排序
    DB_SORT_STRU stSort = {.ucSortNum = 1};
    DB_SELECT_STRU stSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = ulDbId,
        .usRelNo = usRelId,
        .pstCond = &stCond,
        .pstFldFilter = &g_stFldFilter,
        .pstSort = &stSort,
        .pstQueryPlan = &queryPlan,
        .pfGetBuf = NULL,
        .pvArg = NULL,
        .pstDataBuf = &selBuff};
    ret = DB_SimpleSelectAllRec(&stSelect);
    ASSERT_EQ(VOS_ERRNO_DB_NOTSUPPORT, ret);

    // 不支持排序的查询条件
    stSort.ucSortNum = 0;
    for (uint32_t i = DB_OP_HAVEPREFIX; i < DB_OP_BUTT; i++) {
        DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, (DB_OPTYPE_ENUM)i, (uint8_t *)&condV, sizeof(uint32_t));
        ret = DB_SimpleSelectAllRec(&stSelect);
        ASSERT_EQ(VOS_ERRNO_DB_NOTSUPPORT, ret);
    }

    DBDqlTestDqlCleanData(ulDbId, &selBuff);
}

// simple select 等值查询数据
TEST_F(StSimpRelDBDQL, DBDqlTestDqlSimpleSelect2)
{
    Status ret = GMERR_OK;
    const uint32_t recNum = 100;
    uint32_t bases[recNum] = {0};
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlCompareIdxAndSequencePrapreData(&ulDbId, &usRelId, bases, recNum);

    uint32_t condV = 50;
    DB_COND_STRU stCond = {0};
    DB_QUERY_PLAN_STRU queryPlan = {.iIndexId = 0xFF};

    stCond.usCondNum = 1;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_EQUAL, (uint8_t *)&condV, sizeof(uint32_t));
    ret = DB_PrepareQueryPlan(ulDbId, usRelId, &stCond, &queryPlan);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, queryPlan.iIndexId);

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, 200, DB_SELECT_ALL);  // 初始化存储结果buf
    DB_SORT_STRU stSort = {0};

    DB_SELECT_STRU stSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = ulDbId,
        .usRelNo = usRelId,
        .pstCond = &stCond,
        .pstFldFilter = &g_stFldFilter,
        .pstSort = &stSort,
        .pstQueryPlan = &queryPlan,
        .pfGetBuf = NULL,
        .pvArg = NULL,
        .pstDataBuf = &selBuff};
    ret = DB_SimpleSelectAllRec(&stSelect);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(*(uint32_t *)selBuff.pBuf, condV);

    stSelect.pstQueryPlan = NULL;
    ret = DB_SimpleSelectAllRec(&stSelect);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(*(uint32_t *)selBuff.pBuf, condV);
    DBDqlTestDqlCleanData(ulDbId, &selBuff);
}

// simple select 范围查询数据
TEST_F(StSimpRelDBDQL, DBDqlTestDqlSimpleSelect3)
{
    Status ret = GMERR_OK;
    const uint32_t recNum = 100;
    uint32_t bases[recNum] = {0};
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlCompareIdxAndSequencePrapreData(&ulDbId, &usRelId, bases, recNum);

    DB_COND_STRU stCond = {0};
    DB_QUERY_PLAN_STRU queryPlan = {.iIndexId = 0xFF};

    stCond.usCondNum = 1;
    uint32_t condV = 80;  // F0 >= 80
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGEREQUAL, (uint8_t *)&condV, sizeof(uint32_t));
    ret = DB_PrepareQueryPlan(ulDbId, usRelId, &stCond, &queryPlan);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, queryPlan.iIndexId);

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, 200, DB_SELECT_ALL);  // 初始化存储结果buf

    DB_FIELDFILTER_STRU fldFilter = {.ucFieldNum = 1};
    fldFilter.aucField[0] = 0;
    DB_SORT_STRU stSort = {.ucSortNum = 0};
    DB_SELECT_STRU stSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = ulDbId,
        .usRelNo = usRelId,
        .pstCond = &stCond,
        .pstFldFilter = &fldFilter,
        .pstSort = &stSort,
        .pstQueryPlan = &queryPlan,
        .pfGetBuf = NULL,
        .pvArg = NULL,
        .pstDataBuf = &selBuff};
    ret = DB_SimpleSelectAllRec(&stSelect);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(20, selBuff.ulRecNum);
    uint32_t *bufCursor = (uint32_t *)(void *)selBuff.pBuf;
    qsort(bufCursor, selBuff.ulRecNum, sizeof(uint32_t), Uint32Ascnd);  // 索引查询顺序不是插入顺序
    for (uint32_t i = 0; i < selBuff.ulRecNum; i++) {
        ASSERT_EQ(*bufCursor, i + 80);
        bufCursor++;
    }
    DBDqlTestDqlCleanData(ulDbId, &selBuff);
}

// selectAllRec2查询
TEST_F(StSimpRelDBDQL, DBDqlTestDqlSelectAllRec2_1)
{
    Status ret = GMERR_OK;
    const uint32_t recNum = 100;
    uint32_t bases[recNum] = {0};
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlCompareIdxAndSequencePrapreData(&ulDbId, &usRelId, bases, recNum);

    uint32_t condV = 50;
    DB_COND_STRU stCond = {0};
    DB_QUERY_PLAN_STRU queryPlan = {.iIndexId = 0xFF};

    stCond.usCondNum = 1;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_MAX_LESS, (uint8_t *)&condV, sizeof(uint32_t));
    ret = DB_PrepareQueryPlan(ulDbId, usRelId, &stCond, &queryPlan);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, queryPlan.iIndexId);

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, 200, DB_SELECT_ALL);  // 初始化存储结果buf
    DB_SORT_STRU stSort = {0};

    DB_SELECT_STRU stSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = ulDbId,
        .usRelNo = usRelId,
        .pstCond = &stCond,
        .pstFldFilter = &g_stFldFilter,
        .pstSort = &stSort,
        .pstQueryPlan = &queryPlan,
        .pfGetBuf = NULL,
        .pvArg = NULL,
        .pstDataBuf = &selBuff};
    ret = DB_SelectAllRec2(&stSelect);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(49, *(uint32_t *)selBuff.pBuf);
    DBDqlTestDqlCleanData(ulDbId, &selBuff);
}

typedef struct DBDqlListBuf {
    VOS_UINT32 ulCnt;
    VOS_UINT32 ulSize;
    VOS_UINT32 idx;
    DB_BUF_STRU stBuf;
} DBDqlListBufT;

DB_BUF_STRU *DBDqlTestDynMemCallback(void *pArg)
{
    DBDqlListBufT *pstList = (DBDqlListBuf *)pArg;
    uint32_t idx = pstList[0].idx;
    pstList[idx].ulCnt = pstList[0].ulCnt;
    pstList[idx].ulSize = pstList[0].ulSize;
    pstList[idx].stBuf.ulBufLen = pstList[idx].ulCnt * pstList[idx].ulSize;
    pstList[idx].stBuf.pBuf = malloc(pstList[idx].stBuf.ulBufLen);
    if (pstList[idx].stBuf.pBuf == NULL) {
        return NULL;
    }
    (void)memset_s(pstList[idx].stBuf.pBuf, pstList[idx].stBuf.ulBufLen, 0x00, pstList[idx].stBuf.ulBufLen);
    pstList[0].idx++;
    return &pstList[idx].stBuf;
}

// buf不足够场景下selectAllRec2查询
TEST_F(StSimpRelDBDQL, DBDqlTestDqlSelectAllRec2_2)
{
    Status ret = GMERR_OK;
    const uint32_t recNum = 100;
    uint32_t bases[recNum] = {0};
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlCompareIdxAndSequencePrapreData(&ulDbId, &usRelId, bases, recNum);

    uint32_t condV = 10;
    DB_COND_STRU stCond = {0};
    DB_QUERY_PLAN_STRU queryPlan = {.iIndexId = 0xFF};

    stCond.usCondNum = 1;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_MAX_LESS, (uint8_t *)&condV, sizeof(uint32_t));
    ret = DB_PrepareQueryPlan(ulDbId, usRelId, &stCond, &queryPlan);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, queryPlan.iIndexId);

    DB_BUF_STRU selBuff;
    DBDqlTestDqlInitSelectBuf(&selBuff, 200, DB_SELECT_ALL);  // 初始化存储结果buf
    DB_SORT_STRU stSort = {0};
    DBDqlListBufT astDBDqlListBuf[5];
    astDBDqlListBuf[0].idx = 0;
    astDBDqlListBuf[0].ulCnt = 100;
    astDBDqlListBuf[0].ulSize = 32;

    DB_SELECT_STRU stSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = ulDbId,
        .usRelNo = usRelId,
        .pstCond = &stCond,
        .pstFldFilter = &g_stFldFilter,
        .pstSort = &stSort,
        .pstQueryPlan = &queryPlan,
        .pfGetBuf = DBDqlTestDynMemCallback,
        .pvArg = (void *)astDBDqlListBuf,
        .pstDataBuf = &selBuff};
    ret = DB_SelectAllRec2(&stSelect);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(9, *(uint32_t *)selBuff.pBuf);
    DBDqlTestDqlCleanData(ulDbId, &selBuff);
}

TEST_F(StSimpRelDBDQL, DBDqlTestDqlSelectAllRecWithWildCard)
{
    uint32_t ulDbId = 0;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ASSERT_EQ(GMERR_OK,
        StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/dql/testDqlCompareIdxAndSequence.json", &stRelDef, NULL));
    for (uint32_t i = 0; i < 8; i++) {
        stRelDef.pstFldLst[i].ulDefVal = 0xffffffff;
    }
    stRelDef.pstFldLst[4].ulDefVal = 5;
    ASSERT_EQ(GMERR_OK, DB_CreateTbl(ulDbId, &stRelDef, &usRelId));
    uint32_t recLen = sizeof(uint32_t) * stRelDef.ulNCols;
    StUtilFreeAllRelDef(&stRelDef);

    uint32_t recCnt = 10;
    for (uint32_t i = 0; i < recCnt; i++) {  // 插入数据 0 - 9
        uint32_t record[8] = {i, i, i, i, i, i, i, i};
        DB_DSBUF_STRU insertBuf = {
            .usRecLen = 0, .usRecNum = 0, .StdBuf = {.ulBufLen = 0, .ulActLen = recLen, .pucData = (uint8_t *)&record}};
        ASSERT_EQ(GMERR_OK, DB_InsertRec(ulDbId, usRelId, &insertBuf));
    }

    const uint32_t bufLen = 160;
    DB_DSBUF_STRU dsBuf;
    DBDqlTestDqlInitSelectBuf(&dsBuf, bufLen, 5, true);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 1};              // 条件数量

    uint32_t data = 150;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 4, DB_OP_EQUAL, (uint8_t *)&data, sizeof(uint32_t));  // 查询条件F4 = 150
    Status ret = DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1, dsBuf.usRecNum);
    ASSERT_EQ(5, *(uint32_t *)dsBuf.StdBuf.pucData);  // 预期最终查询到数据为5一条

    dsBuf.usRecNum = 10;
    DB_SORT_STRU stSort = {.ucSortNum = 1};
    stSort.enSortType = DB_SORTTYPE_ASCEND;
    T_FIELD sortFields[] = {1};
    stSort.pSortFields = sortFields;
    ret = DB_SelectAllRecByOrder(ulDbId, usRelId, &stSort, &stCond, &g_stFldFilter, &dsBuf);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1, dsBuf.usRecNum);
    ASSERT_EQ(5, *(uint32_t *)dsBuf.StdBuf.pucData);  // 预期最终查询到数据为5一条
    DBDqlTestDqlCleanData(ulDbId, &dsBuf, true);
}

TEST_F(StSimpRelDBDQL, DBDqlTestDqlSelectAllRecWithWildCard2)
{
    uint32_t ulDbId = 0;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef = {0};
    ASSERT_EQ(GMERR_OK, StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/dql/testDqlWildCardScan.json", &stRelDef, NULL));
    stRelDef.pstFldLst[0].ulDefVal = 0xffffffff;
    stRelDef.pstFldLst[1].ulDefVal = 20;
    stRelDef.pstFldLst[2].ulDefVal = 25;
    stRelDef.pstFldLst[3].ulDefVal = 0xffffffff;
    ASSERT_EQ(GMERR_OK, DB_CreateTbl(ulDbId, &stRelDef, &usRelId));
    uint32_t recLen = sizeof(uint32_t) * stRelDef.ulNCols;
    StUtilFreeAllRelDef(&stRelDef);

    uint32_t insertValues[][4] = {{0, 10, 15, 10}, {1, 10, 16, 20}, {2, 10, 17, 30}, {3, 10, 18, 40}, {4, 10, 25, 50},
        {5, 20, 28, 60}, {6, 40, 25, 70}, {7, 20, 29, 80}, {8, 20, 28, 90}, {9, 20, 30, 99}};

    uint32_t recCnt = 10;
    for (uint32_t i = 0; i < recCnt; i++) {  // 插入10条数据
        DB_DSBUF_STRU insertBuf = {.usRecLen = 0,
            .usRecNum = 0,
            .StdBuf = {.ulBufLen = 0, .ulActLen = recLen, .pucData = (uint8_t *)&insertValues[i]}};
        ASSERT_EQ(GMERR_OK, DB_InsertRec(ulDbId, usRelId, &insertBuf));
    }

    const uint32_t bufLen = 160;
    DB_DSBUF_STRU dsBuf;
    DBDqlTestDqlInitSelectBuf(&dsBuf, bufLen, 10, true);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 3};               // 条件数量

    uint32_t data = 4;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGEREQUAL, (uint8_t *)&data, sizeof(uint32_t));
    data = 30;
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 1, DB_OP_LARGER, (uint8_t *)&data, sizeof(uint32_t));
    data = 27;
    DBDqlTestDqlInitOneCond(&stCond.aCond[2], 2, DB_OP_LARGEREQUAL, (uint8_t *)&data, sizeof(uint32_t));
    Status ret = DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1, dsBuf.usRecNum);
    ASSERT_EQ(*(uint32_t *)dsBuf.StdBuf.pucData, 6);  // 预期最终查询到数据为6一条

    dsBuf.usRecNum = 10;
    data = 50;
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 1, DB_OP_LARGER, (uint8_t *)&data, sizeof(uint32_t));
    ret = DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(4, dsBuf.usRecNum);  // 预期 5,7,8,9 四条

    DBDqlTestDqlCleanData(ulDbId, &dsBuf, true);
}

TEST_F(StSimpRelDBDQL, DBDqlTestDMLNoInsert)
{
    uint32_t ulDbId = 0;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    uint16_t usRelId;
    uint16_t usRelId2;
    DB_REL_DEF_STRU stRelDef;
    ASSERT_EQ(GMERR_OK, StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/dql/testDqlNoInsert.json", &stRelDef, NULL));
    for (uint32_t i = 0; i < 8; i++) {
        stRelDef.pstFldLst[i].ulDefVal = 0xffffffff;
    }
    stRelDef.pstFldLst[4].ulDefVal = 5;
    ASSERT_EQ(GMERR_OK, DB_CreateTbl(ulDbId, &stRelDef, &usRelId));
    StUtilFreeAllRelDef(&stRelDef);

    ASSERT_EQ(GMERR_OK, StUtilGetRelDefFromJsonFile("./dcdm/rel_defs/dql/testDqlNoInsertTbl2.json", &stRelDef, NULL));
    for (uint32_t i = 0; i < 8; i++) {
        stRelDef.pstFldLst[i].ulDefVal = 0xffffffff;
    }
    stRelDef.pstFldLst[4].ulDefVal = 5;
    ASSERT_EQ(GMERR_OK, DB_CreateTbl(ulDbId, &stRelDef, &usRelId2));
    StUtilFreeAllRelDef(&stRelDef);

    const uint32_t bufLen = 160;
    DB_DSBUF_STRU dsBuf;
    DBDqlTestDqlInitSelectBuf(&dsBuf, bufLen, 5, true);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 1};              // 条件数量

    uint32_t data = 150;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 4, DB_OP_EQUAL, (uint8_t *)&data, sizeof(uint32_t));  // 查询条件F4 = 150
    Status ret = DB_SelectAllRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf);
    ASSERT_EQ(VOS_ERRNO_DB_RECNOTEXIST, ret);
    ASSERT_EQ(0, dsBuf.usRecNum);

    DB_DSBUF_STRU udpBuf = {
        .usRecLen = 0, .usRecNum = 1, .StdBuf = {.ulBufLen = 32, .ulActLen = 32, .pucData = (uint8_t *)malloc(32)}};
    uint32_t udpRecNum = 0;
    ret = DB_UpdateRec(ulDbId, usRelId2, &stCond, &g_stFldFilter, &udpBuf, &udpRecNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, udpRecNum);
    free(udpBuf.StdBuf.pucData);
    DBDqlTestDqlCleanData(ulDbId, &dsBuf, true);
}

// 9个字段1个索引表结构初始化
void DBDqlTestCreateTblInitRelDefFetch(DB_REL_DEF_STRU *stRelDef, const char *tblName)
{
    const char tableName[] = "nameindex";
    const char indexName[] = "index";
    const uint32_t fldNum = 9;
    const uint32_t idxNum = 1;

    DB_FIELD_DEF_STRU *astFlds = (DB_FIELD_DEF_STRU *)malloc(fldNum * sizeof(DB_FIELD_DEF_STRU));
    const DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {
        DBT_STRING, DBT_UINT8, DBT_UINT16, DBT_UINT32, DBT_SINT8, DBT_SINT16, DBT_UINT32, DBT_INT64, DBT_UINT64};
    const uint32_t fldSizes[fldNum] = {16, 1, 2, 4, 1, 2, 4, 8, 8};
    for (uint32_t i = 0; i < fldNum; i++) {
        (void)sprintf_s((char *)astFlds[i].aucFieldName, DB_NAME_LEN, "F%d", i);
        astFlds[i].enDataType = dataTypes[i];
        astFlds[i].usSize = fldSizes[i];
    }

    DB_INDEX_DEF_STRU *astIdx = (DB_INDEX_DEF_STRU *)malloc(fldNum * sizeof(DB_INDEX_DEF_STRU));
    astIdx[0].ucUniqueFlag = 1;
    (void)strncpy_s((char *)astIdx[0].aucIndexName, DB_NAME_LEN, indexName, DB_NAME_LEN);
    astIdx[0].ucIdxFldNum = 1;
    astIdx[0].aucFieldID[0] = 3;
    astIdx[0].enIndexType = DBDDL_INDEXTYPE_TTREE;

    tblName == NULL ? (void)strncpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tableName, DB_NAME_LEN) :
                      (void)strncpy_s((char *)stRelDef->aucRelName, DB_NAME_LEN, tblName, DB_NAME_LEN);
    stRelDef->enTableType = DB_TABLE_NORMAL;
    stRelDef->ulIntialSize = 100000;
    stRelDef->ulMaxSize = 100000;
    stRelDef->ulNCols = fldNum;
    stRelDef->ulNIdxs = idxNum;
    stRelDef->pstFldLst = astFlds;
    stRelDef->pstIdxLst = astIdx;
    TestSetInvalidDefaultValue4RelDef(stRelDef);
}

// 9个字段1个索引初始化一条数据
void DBDqlTestInitOneRec(uint8_t *recBuf, DB_FIELD_DEF_STRU *astFlds, uint32_t base, uint32_t bufLen)
{
    (void)memset_s(recBuf, bufLen, 0x00, bufLen);
    uint32_t index = 0;
    const uint32_t v0Len = 17;
    char v0[v0Len];
    memset_s(v0, v0Len, 0x00, v0Len);
    (void)sprintf_s(v0, v0Len, "rec%u", base);
    uint8_t v1 = base % 256;
    uint16_t v2 = base + 10;
    uint32_t v3 = base + 100;
    int8_t v4 = base % 128;
    int16_t v5 = base + 20;
    int32_t v6 = base + 200;
    int64_t v7 = base + 1000;
    uint64_t v8 = base + 2000;
    uint8_t *temp = recBuf;
    (void)memcpy_s(temp, astFlds[index].usSize, &v0, astFlds[index].usSize);
    (void)memcpy_s(temp += (astFlds[index].usSize + 1), astFlds[index + 1].usSize, &v1, astFlds[index + 1].usSize);
    ++index;
    (void)memcpy_s(temp += astFlds[index].usSize, astFlds[index + 1].usSize, &v2, astFlds[index + 1].usSize);
    ++index;
    (void)memcpy_s(temp += astFlds[index].usSize, astFlds[index + 1].usSize, &v3, astFlds[index + 1].usSize);
    ++index;
    (void)memcpy_s(temp += astFlds[index].usSize, astFlds[index + 1].usSize, &v4, astFlds[index + 1].usSize);
    ++index;
    (void)memcpy_s(temp += astFlds[index].usSize, astFlds[index + 1].usSize, &v5, astFlds[index + 1].usSize);
    ++index;
    (void)memcpy_s(temp += astFlds[index].usSize, astFlds[index + 1].usSize, &v6, astFlds[index + 1].usSize);
    ++index;
    (void)memcpy_s(temp += astFlds[index].usSize, astFlds[index + 1].usSize, &v7, astFlds[index + 1].usSize);
    ++index;
    (void)memcpy_s(temp += astFlds[index].usSize, astFlds[index + 1].usSize, &v8, astFlds[index + 1].usSize);
}

static void DBDqlTestInitBaseData4Select(uint32_t *ulDbId, uint16_t *usRelId, uint32_t insertCnt)
{
    char dir[10] = "";
    const char *dbNameTmp = COMMON_DBNAME;
    DB_INST_CONFIG_STRU pstCfg = {0};
    ASSERT_EQ(GMERR_OK, DB_CreateDB((uint8_t *)dbNameTmp, NULL, &pstCfg));
    int32_t ret = DB_OpenDB((VOS_UINT8 *)dir, (VOS_UINT8 *)dbNameTmp, ulDbId);
    EXPECT_EQ(GMERR_OK, ret);

    DB_REL_DEF_STRU stRelDef = {0};
    DBDqlTestCreateTblInitRelDefFetch(&stRelDef, NULL);
    ret = DB_CreateTbl(*ulDbId, &stRelDef, usRelId);
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t recLen = TestGetRecLen(&stRelDef);
    uint8_t *recBuf = (uint8_t *)malloc(recLen);
    // 插入 insertCnt 条
    for (uint32_t i = 1; i <= insertCnt; i++) {
        DBDqlTestInitOneRec(recBuf, stRelDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = DB_InsertRec(*ulDbId, *usRelId, &dsBuf);
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(recBuf);
    TestFreeRelDef(&stRelDef);
}

inline static void TestUnInitBaseData4Select(uint32_t ulDbId)
{
    Status ret = DB_CloseDB(ulDbId);
    EXPECT_EQ(GMERR_OK, ret);
    ret = DB_DropDB((VOS_UINT8 *)COMMON_DBNAME, 0);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StSimpRelDBDQL, DBDqlTestBeginSelect)
{
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId = 0;
    DBDqlTestInitBaseData4Select(&ulDbId, &usRelId, 10);

    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};

    // f3 <= 106 （f3数据 101-110）, 预期查询6条
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = DB_OP_LESSEQUAL;
    stCond.aCond[0].ucFieldId = 3;
    *(uint32_t *)stCond.aCond[0].aucValue = 106;

    DB_SELHANDLE selectHdl;
    EXPECT_EQ(GMERR_OK, DB_BeginSelect(ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl));
    EXPECT_EQ(selectHdl, 1);
    for (int i = 2; i <= 65534; i++) {
        EXPECT_EQ(GMERR_OK, DB_BeginSelect(ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl));
        EXPECT_EQ(selectHdl, i);
    }

    EXPECT_EQ(VOS_ERRNO_DB_NOHANDLEBLK, DB_BeginSelect(ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl));

    uint16_t bufLen = 100;
    uint8_t *recBuf = (VOS_UINT8 *)malloc(bufLen);
    DB_DSBUF_STRU queryBuf = {
        .usRecLen = bufLen, .usRecNum = 10, .StdBuf = {.ulBufLen = bufLen, .ulActLen = bufLen, .pucData = recBuf}};
    EXPECT_EQ(GMERR_OK, DB_EndSelect(100));
    EXPECT_EQ(VOS_ERRNO_DB_INVALIDHANDLE, DB_FetchSelectRec(100, &queryBuf));
    free(recBuf);
    EXPECT_EQ(GMERR_OK, DB_BeginSelect(ulDbId, usRelId, &stCond, &stFldFilter, &selectHdl));
    EXPECT_EQ(100, selectHdl);
    EXPECT_EQ(GMERR_OK, DB_EndSelect(100));
    EXPECT_EQ(GMERR_OK, DB_CloseAllHandles(ulDbId));
    TestUnInitBaseData4Select(ulDbId);
}

TEST_F(StSimpRelDBDQL, DBDqlTestBeginIdxSelectByOrder)
{
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId = 0;
    DBDqlTestInitBaseData4Select(&ulDbId, &usRelId, 10);

    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_COND_STRU stCond = {.usCondNum = 1};
    stCond.aCond[0].enOp = DB_OP_LESS;
    stCond.aCond[0].ucFieldId = 1;
    *(uint32_t *)stCond.aCond[0].aucValue = 5;

    // 排序字段与索引序不同
    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {1};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    DB_SELHANDLE selectHdl;
    // 匹配不到索引报错
    EXPECT_EQ(VOS_ERRNO_DB_INVALIDIDX_ON_SORTFIELD,
        DB_BeginIdxSelectByOrder(ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl));

    // 匹配到索引
    sortFlds[0] = {3};
    pstSort.pSortFields = (T_FIELD *)sortFlds;
    EXPECT_EQ(GMERR_OK, DB_BeginIdxSelectByOrder(ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, &selectHdl));

    EXPECT_EQ(GMERR_OK, DB_EndSelect(selectHdl));
    TestUnInitBaseData4Select(ulDbId);
}

TEST_F(StSimpRelDBDQL, DBDqlTestBeginIdxSelectByOrderArgs)
{
    VOS_UINT32 ulDbId = 0;
    uint16_t usRelId = 0;
    DBDqlTestInitBaseData4Select(&ulDbId, &usRelId, 10);

    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_COND_STRU stCond = {.usCondNum = 1};
    stCond.aCond[0].enOp = DB_OP_LESS;
    stCond.aCond[0].ucFieldId = 1;
    *(uint32_t *)stCond.aCond[0].aucValue = 5;

    DB_SORT_STRU pstSort = {.ucSortNum = 1, .aucReserve = {0}, .enSortType = DB_SORTTYPE_DESCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    sortFlds[0] = {3};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    DB_SELHANDLE hdl;
    EXPECT_EQ(VOS_ERRNO_DB_NULLPTR, DB_BeginIdxSelectByOrder(ulDbId, usRelId, NULL, &stCond, &stFldFilter, &hdl));
    EXPECT_EQ(VOS_ERRNO_DB_NULLPTR, DB_BeginIdxSelectByOrder(ulDbId, usRelId, &pstSort, NULL, &stFldFilter, &hdl));
    EXPECT_EQ(VOS_ERRNO_DB_NULLPTR, DB_BeginIdxSelectByOrder(ulDbId, usRelId, &pstSort, &stCond, NULL, &hdl));
    EXPECT_EQ(VOS_ERRNO_DB_NULLPTR, DB_BeginIdxSelectByOrder(ulDbId, usRelId, &pstSort, &stCond, &stFldFilter, NULL));
    EXPECT_EQ(
        VOS_ERRNO_DB_INVALID_DATABASE, DB_BeginIdxSelectByOrder(10, usRelId, &pstSort, &stCond, &stFldFilter, &hdl));
    EXPECT_EQ(VOS_ERRNO_DB_INVALIDREL, DB_BeginIdxSelectByOrder(ulDbId, 20, &pstSort, &stCond, &stFldFilter, &hdl));
    TestUnInitBaseData4Select(ulDbId);
}

TEST_F(StSimpRelDBDQL, DBDqlTestDBSGetRelAllRecords)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);
    DB_DSBUF_STRU dsBuf;
    DBDqlTestDqlInitSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 初始化存储结果buf
    ASSERT_EQ(VOS_ERRNO_DB_INVALID_DATABASE, DBS_GetRelAllRecords(10, usRelId, &dsBuf));
    ASSERT_EQ(VOS_ERRNO_DB_INVALIDREL, DBS_GetRelAllRecords(ulDbId, 10, &dsBuf));

    ASSERT_EQ(GMERR_OK, DBS_GetRelAllRecords(ulDbId, usRelId, &dsBuf));
    ASSERT_EQ(dsBuf.usRecNum, REC_NUM);
    DBDqlTestDqlCleanData(ulDbId, &dsBuf, true);
}

TEST_F(StSimpRelDBDQL, DBDqlTestDBSGetRelAllRecords2)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    CommonDBCreateDB4Test(NULL, &ulDbId);
    DBDqlTestDqlCreateTbl(ulDbId, &usRelId);

    DB_DSBUF_STRU dsBuf;
    DBDqlTestDqlInitSelectBuf(&dsBuf, 50 * REC_LEN, DB_SELECT_ALL, true);  // 初始化存储结果buf
    ASSERT_EQ(GMERR_OK, DBS_GetRelAllRecords(ulDbId, usRelId, &dsBuf));
    ASSERT_EQ(dsBuf.usRecNum, 0);
    ASSERT_EQ(dsBuf.usRecLen, REC_LEN);

    for (uint32_t i = 1; i <= 100; i++) {
        DBDqlTestDqlRecordT record = {
            .v0 = i, .v1 = {0}, .v2 = 150 - i, .v3 = (uint16_t)(i + 99), .v4 = (uint8_t)(i % 50)};
        (void)sprintf_s(record.v1, F1_LEN, "xxyz%ucba", i % 20);
        DB_DSBUF_STRU insertBuf = {0};
        insertBuf.StdBuf.ulActLen = REC_LEN;
        insertBuf.StdBuf.pucData = (uint8_t *)&record;
        ASSERT_EQ(GMERR_OK, DB_InsertRec(ulDbId, usRelId, &insertBuf));
    }

    dsBuf.usRecNum = 0xFFFF;
    ASSERT_EQ(VOS_ERRNO_DB_INVALIDBUFLEN, DBS_GetRelAllRecords(ulDbId, usRelId, &dsBuf));

    ASSERT_EQ(GMERR_OK, DBS_DeleteRelAllRecords(ulDbId, usRelId));

    ASSERT_EQ(GMERR_OK, DBS_GetRelAllRecords(ulDbId, usRelId, &dsBuf));
    ASSERT_EQ(dsBuf.usRecNum, 0);
    ASSERT_EQ(dsBuf.usRecLen, REC_LEN);

    dsBuf.usRecNum = 50;
    dsBuf.usRecLen = 0xFFF;
    DB_COND_STRU stCond = {.usCondNum = 0};
    DB_FIELDFILTER_STRU stFldFilter = {.ucFieldNum = DB_FIELD_ALL};
    ASSERT_EQ(VOS_ERRNO_DB_RECNOTEXIST, DB_SelectAllRec(ulDbId, usRelId, &stCond, &stFldFilter, &dsBuf));
    ASSERT_EQ(dsBuf.usRecNum, 0);
    ASSERT_EQ(dsBuf.usRecLen, 0xFFF);  // 报错不变更这个值

    DBDqlTestDqlCleanData(ulDbId, &dsBuf, true);
}

TEST_F(StSimpRelDBDQL, DBDqlTestDBSLocalExistRecordForDatabase)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_COND_STRU stCond = {.usCondNum = 3};  // 条件数量
    uint32_t condV0, condV2, condV4;

    /*  匹配第2个索引  */
    condV0 = 21;
    condV2 = 129;
    condV4 = 30;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 2, DB_OP_EQUAL, (uint8_t *)&condV2, 4);
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 4, DB_OP_EQUAL, (uint8_t *)&condV4, 1);
    DBDqlTestDqlInitOneCond(&stCond.aCond[2], 0, DB_OP_EQUAL, (uint8_t *)&condV0, 4);
    uint32_t matchCnt = 0;
    Status ret = DBS_LocalExistRecordForDatabase(ulDbId, usRelId, &stCond, &matchCnt);
    ASSERT_EQ(VOS_ERRNO_DB_RECNOTEXIST, ret);

    DB_DSBUF_STRU dsBuf;
    DBDqlTestDqlInitSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 初始化存储结果buf
    stCond.usCondNum = 4;                                                // 条件数量

    uint32_t condV0a = 30;
    uint32_t condV0b = 100;
    condV2 = 100;
    condV4 = 35;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGER, (uint8_t *)&condV0a, 4);     // F0 > 30
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 0, DB_OP_LESSEQUAL, (uint8_t *)&condV0b, 4);  // F0 <= 100
    DBDqlTestDqlInitOneCond(&stCond.aCond[2], 2, DB_OP_LARGER, (uint8_t *)&condV2, 4);      // F2 > 100
    DBDqlTestDqlInitOneCond(&stCond.aCond[3], 4, DB_OP_LARGER, (uint8_t *)&condV4, 1);      // F4 > 35

    ASSERT_EQ(GMERR_OK, DB_SelectFirstRec(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(1, dsBuf.usRecNum);

    uint32_t recNum = 0;  // 预期14条
    ASSERT_EQ(GMERR_OK, DBS_LocalExistRecordForDatabase(ulDbId, usRelId, &stCond, &recNum));
    ASSERT_EQ(14, recNum);

    DBDqlTestDqlCleanData(ulDbId, &dsBuf, true);
}

TEST_F(StSimpRelDBDQL, DBDqlTestDBSLocalGetAllRecordForDatabase)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);
    DB_DSBUF_STRU dsBuf;
    DBDqlTestDqlInitSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 初始化存储结果buf

    DB_COND_STRU stCond = {.usCondNum = 1};  // 条件数量

    uint32_t condV = 95;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGER, (uint8_t *)&condV, 4);  // F0 >= 95, 预期55条

    ASSERT_EQ(
        VOS_ERRNO_DB_INVALID_DATABASE, DBS_LocalGetAllRecordForDatabase(10, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    ASSERT_EQ(VOS_ERRNO_DB_INVALIDREL, DBS_LocalGetAllRecordForDatabase(ulDbId, 10, &stCond, &g_stFldFilter, &dsBuf));
    ASSERT_EQ(VOS_ERRNO_DB_NULLPTR, DBS_LocalGetAllRecordForDatabase(ulDbId, usRelId, NULL, &g_stFldFilter, &dsBuf));
    ASSERT_EQ(VOS_ERRNO_DB_NULLPTR, DBS_LocalGetAllRecordForDatabase(ulDbId, usRelId, &stCond, NULL, &dsBuf));
    ASSERT_EQ(VOS_ERRNO_DB_NULLPTR, DBS_LocalGetAllRecordForDatabase(ulDbId, usRelId, &stCond, &g_stFldFilter, NULL));

    ASSERT_EQ(GMERR_OK, DBS_LocalGetAllRecordForDatabase(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    ASSERT_EQ(55, dsBuf.usRecNum);

    DBDqlTestDqlCleanData(ulDbId, &dsBuf, true);
}

TEST_F(StSimpRelDBDQL, DBDqlTestDBSLocalGetFirstRecordForDatabase)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    DB_DSBUF_STRU dsBuf;
    DBDqlTestDqlInitSelectBuf(&dsBuf, MAX_BUFLEN, DB_SELECT_ALL, true);  // 初始化存储结果buf
    DB_COND_STRU stCond = {.usCondNum = 4};                              // 条件数量

    uint32_t condV0a = 30;
    uint32_t condV0b = 100;
    uint32_t condV2 = 100;
    uint32_t condV4 = 35;
    DBDqlTestDqlInitOneCond(&stCond.aCond[0], 0, DB_OP_LARGER, (uint8_t *)&condV0a, 4);     // F0 > 30
    DBDqlTestDqlInitOneCond(&stCond.aCond[1], 0, DB_OP_LESSEQUAL, (uint8_t *)&condV0b, 4);  // F0 <= 100
    DBDqlTestDqlInitOneCond(&stCond.aCond[2], 2, DB_OP_LARGER, (uint8_t *)&condV2, 4);      // F2 > 100
    DBDqlTestDqlInitOneCond(&stCond.aCond[3], 4, DB_OP_LARGER, (uint8_t *)&condV4, 1);      // F4 > 35

    ASSERT_EQ(GMERR_OK, DBS_LocalGetFirstRecordForDatabase(ulDbId, usRelId, &stCond, &g_stFldFilter, &dsBuf));
    DBDqlTestDqlPrintSelectResult(&stCond, &dsBuf, true);
    ASSERT_EQ(1, dsBuf.usRecNum);

    uint32_t recNum = 0;  // 预期14条
    ASSERT_EQ(GMERR_OK, DB_CountMatchingRecs(ulDbId, usRelId, &stCond, &recNum));
    ASSERT_EQ(14, recNum);
    DBDqlTestDqlCleanData(ulDbId, &dsBuf, true);
}

TEST_F(StSimpRelDBDQL, DBDqlTestDBSLocalGetRelRecNumForDatabase)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    uint32_t recordNum = 0;
    ASSERT_EQ(VOS_ERRNO_DB_INVALID_DATABASE, DBS_LocalGetRelRecNumForDatabase(10, usRelId, &recordNum));
    ASSERT_EQ(VOS_ERRNO_DB_INVALIDREL, DBS_LocalGetRelRecNumForDatabase(ulDbId, 10, &recordNum));

    ASSERT_EQ(GMERR_OK, DBS_LocalGetRelRecNumForDatabase(ulDbId, usRelId, &recordNum));
    ASSERT_EQ(REC_NUM, recordNum);

    DBDqlTestDqlCleanData(ulDbId, NULL, true);
}

TEST_F(StSimpRelDBDQL, DBDqlTestDBSLocalGetRelRecNumOfDatabase)
{
    uint32_t ulDbId;
    uint16_t usRelId;
    DBDqlTestDqlPrepareData(&ulDbId, &usRelId);

    uint32_t recordNum = 0;
    ASSERT_EQ(VOS_ERRNO_DB_INVALID_DATABASE, DBS_LocalGetRelRecNumOfDatabase(10, usRelId, &recordNum));
    ASSERT_EQ(VOS_ERRNO_DB_INVALIDREL, DBS_LocalGetRelRecNumOfDatabase(ulDbId, 10, &recordNum));

    ASSERT_EQ(GMERR_OK, DBS_LocalGetRelRecNumOfDatabase(ulDbId, usRelId, &recordNum));
    ASSERT_EQ(REC_NUM, recordNum);

    DBDqlTestDqlCleanData(ulDbId, NULL, true);
}
