/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: st for ts view while calling gmcXXX interface
 * Author: yangyongji
 * Create: 2024/4/9
 */

#include <gtest/gtest.h>
#include "adpt_sleep.h"
#include "StartDbServer.h"
#include "InitClt.h"
#include "clt_ts.h"
#include "st_common.h"
#include "gmc_sql.h"
#include "srv_data_ts.h"

const uint32_t waitTime = 1000;  // ms

const static char *g_cfgPersist = "ts/gmserver_ts.ini";

class StTsViewGmc : public testing::Test {
protected:
    void SetUp() override
    {
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        // 这里db路径来自配置文件g_cfgPersist中配置项dataFileDirPath
        system("rm -rf /data/gmdb");
        DbSleep(waitTime);
        StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
        DbSleep(waitTime);
    }
    void TearDown() override
    {
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("rm -rf /data/gmdb");
    }
};

// 空表检验视图STORAGE_BTREE_INDEX_STAT，page count
// 总内存，内存利用率MEMORY_UTILIZATION_RATIO，FREE_COUNT等字段
void CheckBtreeViewInNonData(char *indexName, uint32_t pageSize)
{
    char expectPageSizeStr[50];
    auto spRet = snprintf_s(
        expectPageSizeStr, sizeof(expectPageSizeStr), sizeof(expectPageSizeStr) - 1, "PAGE_SIZE: %u", pageSize);
    ASSERT_GT(spRet, 0);
    char viewCmd[512];
    spRet = snprintf_s(viewCmd, sizeof(viewCmd), sizeof(viewCmd) - 1,
        "gmsysview -q V\\$STORAGE_BTREE_INDEX_STAT -f INDEX_NAME=\"%s\"", indexName);
    ASSERT_GT(spRet, 0);

    // 检验的顺序和STORAGE_BTREE_INDEX_STAT schema相关
    const char *matchStr[] = {"RECORD_COUNT: 0", "TREE_HEIGHT: 1", expectPageSizeStr, "PAGE_COUNT: 1",
        "TOTAL_NODE_COUNT: 1", "LEAF_COUNT: 1", "INTERNAL_NODE_COUNT: 0", "INSERT_COUNT: 0", "DELETE_COUNT: 0",
        "SPLIT_COUNT: 0", "FREE_COUNT: 0"};
    // there must be a system view matching `matchStr`
    auto ret = StExecuteCommandWithMatch(viewCmd, matchStr, ELEMENT_COUNT(matchStr));
    ASSERT_EQ(GMERR_OK, ret);
}

static inline int StQueryPageSize(uint32_t &pageSize)
{
    auto cmdRet = StExecuteCommandWlToCount(
        "gmadmin -cfgName pageSize | grep \"config current value: \" | awk '{print $4}'", pageSize);
    pageSize = SIZE_K(pageSize);
    return cmdRet;
}

static inline int StQueryBufferpoolSize(uint32_t &bufferPoolSize)
{
    auto cmdRet = StExecuteCommandWlToCount(
        "gmadmin -cfgName bufferPoolSize | grep \"config current value: \" | awk '{print $4}'", bufferPoolSize);
    return cmdRet;
}

static inline int StQueryBufferpoolRecyclePolicy(uint32_t &bufferPoolRecycPolicy)
{
    auto cmdRet = StExecuteCommandWlToCount(
        "gmadmin -cfgName bufferPoolPolicy | grep \"config current value: \" | awk '{print $4}'",
        bufferPoolRecycPolicy);
    return cmdRet;
}

/**
 * @tc.name: ViewMemOverheadNullTable_01
 * @tc.desc: test view GM_SYS_TS_MAP_PK
 * @tc.type: FUNC
 * @tc.require: DTS2024041116386
 * @tc.author: yangyongji
 */
TEST_F(StTsViewGmc, DISABLED_ViewMemOverheadNullTable_01)
{
    // 获取page size，校验page size
    uint32_t pageSize = 0;
    auto execCmdRet = StQueryPageSize(pageSize);
    EXPECT_EQ(GMERR_OK, execCmdRet);

    // 查询到两个视图 GM_SYS_TS_MAP_PK。如果后续有其他表索引用到btree这里同步修改
    CheckBtreeViewInNonData("GM_SYS_TS_MAP_PK", pageSize);
    CheckBtreeViewInNonData("GM_SYS_TS_MAP_IDX_BOUNDARY_IDX", pageSize);
}

static inline int QueryBtreeIndexShmInServerMemoryOverheadView(uint32_t &occupiedSize)
{
    auto cmdRet = StExecuteCommandWlToCount(
        "gmsysview -q V\\$SERVER_MEMORY_OVERHEAD | grep BTREE_INDEX_SHM | awk '{print $2}'", occupiedSize);
    return cmdRet;
}

static void TsStInitGmcConnection(GmcConnT **conn, GmcStmtT **stmt)
{
    GmcConnOptionsT *connOptions = NULL;
    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);

    ret = GmcAllocStmt(*conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

static void TsStDestroyGmcConnection(GmcConnT *conn, GmcStmtT *stmt)
{
    GmcFreeStmt(stmt);
    auto ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

static void StTsCreateTableAndInsertData(
    GmcStmtT *stmt, const int tableCount, char *tablePrefix, const uint32_t insertRowNum)
{
    int64_t id[] = {1, 5400, 1800};
    // 最大插入三个分区
    int64_t age[] = {29, 30, 19};
    uint32_t rowNum = DB_MIN((uint32_t)(ELEMENT_COUNT(id)), insertRowNum);
    for (int i = 0; i < tableCount; ++i) {
        char tableName[30] = {0};
        auto spRet = snprintf_s(tableName, sizeof(tableName), sizeof(tableName) - 1, "%s%04d", tablePrefix, i);
        ASSERT_GT(spRet, 0);
        char command[200] = {0};
        auto ret = snprintf_s((char *)command, sizeof(command), sizeof(command) - 1,
            "create table %s(age integer, id integer) with (time_col = 'id', "
            "interval = '1 hour');",
            tableName);
        ASSERT_GT(ret, 0);

        ret = GmcExecDirect(stmt, command, sizeof(command));
        ASSERT_EQ(ret, GMERR_OK);

        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }
}

static void StTsViewDropTable(GmcStmtT *stmt, int dropTableCount, char *tablePrefix)
{
    char dropCommand[50] = {0};
    for (int i = 0; i < dropTableCount; ++i) {
        auto ret = snprintf_s(
            (char *)dropCommand, sizeof(dropCommand), sizeof(dropCommand) - 1, "drop table %s%04d", tablePrefix, i);
        ASSERT_GT(ret, 0) << "ret:" << ret;
        ret = GmcExecDirect(stmt, dropCommand, strlen(dropCommand));
        ASSERT_EQ(GMERR_OK, ret);
        // 时序是通过后台线程删表，当前后台线程会存在最大极限限制MAX_BG_WORKER_NUM
        // 这里删表需要保证不超过极限值；
        // st暂无其他白盒方式获取表是否删除成功（可以考虑黑盒检测cuStore方式）
        DbSleep(50);  // 当前并发会存在问题，需要sleep。暂无其他方式
    }
}

// 校验字段，不需校验的字段传递0即可
typedef struct StViewBtreeStatT {
    int recordCount;
    int treeHeight;
    int pageSize;
    int pageCount;
    int totalNodeCount;
    int leafCount;
    int internalNodeCount;
    int insertCount;
    int deleteCount;
    int splitCount;
    int freeCount;
} StViewBtreeStatT;

static void StBtreeViewFillMemStat(const StViewBtreeStatT &stat, vector<string> &matchedStr)
{
    string recordCountStr("RECORD_COUNT: ");
    if (stat.recordCount) {
        recordCountStr += to_string(stat.recordCount);
        matchedStr.push_back(recordCountStr);
    }
    string treeHeightStr("TREE_HEIGHT: ");
    if (stat.treeHeight) {
        treeHeightStr += to_string(stat.treeHeight);
        matchedStr.push_back(treeHeightStr);
    }

    string pageSizeStr("PAGE_SIZE: ");
    if (stat.pageSize) {
        pageSizeStr += to_string(stat.pageSize);
        matchedStr.push_back(pageSizeStr);
    }

    string pageCountStr("PAGE_COUNT: ");
    if (stat.pageSize) {
        pageCountStr += to_string(stat.pageSize);
        matchedStr.push_back(pageCountStr);
    }

    string totalNodeCountStr("TOTAL_NODE_COUNT: ");
    if (stat.totalNodeCount) {
        totalNodeCountStr += to_string(stat.totalNodeCount);
        matchedStr.push_back(totalNodeCountStr);
    }

    string leafCountStr("LEAF_COUNT: ");
    if (stat.leafCount) {
        leafCountStr += to_string(stat.leafCount);
        matchedStr.push_back(leafCountStr);
    }

    string internalNodeCountStr("INTERNAL_NODE_COUNT: ");
    if (stat.internalNodeCount) {
        internalNodeCountStr += to_string(stat.internalNodeCount);
        matchedStr.push_back(internalNodeCountStr);
    }
}

static void StBtreeViewFillPerfStat(const StViewBtreeStatT &stat, vector<string> &matchedStr)
{
    string insertCountStr("INSERT_COUNT: ");
    if (stat.insertCount) {
        insertCountStr += to_string(stat.insertCount);
        matchedStr.push_back(insertCountStr);
    }

    string deleteCountStr("DELETE_COUNT: ");
    if (stat.deleteCount) {
        deleteCountStr += to_string(stat.deleteCount);
        matchedStr.push_back(deleteCountStr);
    }

    string splitCountStr("SPLIT_COUNT: ");
    if (stat.splitCount) {
        splitCountStr += to_string(stat.splitCount);
        matchedStr.push_back(splitCountStr);
    }

    string freeCountStr("FREE_COUNT: ");
    if (stat.freeCount) {
        freeCountStr += to_string(stat.freeCount);
        matchedStr.push_back(freeCountStr);
    }
}

static int CheckBtreeViewFields(char *indexName, const StViewBtreeStatT &stat)
{
    char viewCmd[512] = {0};
    auto spRet = snprintf_s(viewCmd, sizeof(viewCmd), sizeof(viewCmd) - 1,
        "gmsysview -q V\\$STORAGE_BTREE_INDEX_STAT -f INDEX_NAME=\"%s\"", indexName);
    EXPECT_GT(spRet, 0);

    vector<string> matchedStr;

    StBtreeViewFillMemStat(stat, matchedStr);
    StBtreeViewFillPerfStat(stat, matchedStr);
    return StExecuteCommandWithMatchedStringArrays(viewCmd, matchedStr);
}

/**
 * @tc.name: ViewMemOverheadCreateMultipleTableLessData_02
 * @tc.desc: test btree index in GM_SYS_TS_MAP after insert index key data.
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20230516001694.091.002,AR.SR.IR20240326001076.033.002
 * @tc.author: yangyongji
 */
TEST_F(StTsViewGmc, DISABLED_ViewMemOverheadCreateMultipleTableLessData_02)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *tablePrefix = "tableTsView";

    // 申请conn资源
    TsStInitGmcConnection(&conn, &stmt);

    // 1.创建表之后插入和查询数据
    const int tableCount = 1;
    const uint32_t insertRowNum = 1;
    // 此时插入了一张逻辑表和一张物理表（插入数据后产生一张）
    const int expectInsertPhyTableCount = 2;
    StTsCreateTableAndInsertData(stmt, tableCount, tablePrefix, insertRowNum);

    uint32_t shmOccupiedSizeAfterInsert = 0;
    auto execCmdRet = QueryBtreeIndexShmInServerMemoryOverheadView(shmOccupiedSizeAfterInsert);
    EXPECT_EQ(GMERR_OK, execCmdRet);

    // 校验：预期记录数有变化，record count, insert count有变化
    StViewBtreeStatT expectStat = {};
    expectStat.recordCount = expectInsertPhyTableCount;
    expectStat.insertCount = expectInsertPhyTableCount;
    EXPECT_EQ(GMERR_OK, CheckBtreeViewFields("GM_SYS_TS_MAP_PK", expectStat));
    EXPECT_EQ(GMERR_OK, CheckBtreeViewFields("GM_SYS_TS_MAP_IDX_BOUNDARY_IDX", expectStat));

    // 2.测试删除表之后视图数据变化
    StTsViewDropTable(stmt, tableCount, tablePrefix);
    // 检验1，删除之后预期为0，当前btree没有vacuum功能，占用的内存不会回缩，树高不变
    // btree占用预期不变，树占据页没发生变化
    uint32_t shmOccupiedSizeAfterDrop = 0;
    execCmdRet = QueryBtreeIndexShmInServerMemoryOverheadView(shmOccupiedSizeAfterDrop);
    EXPECT_EQ(GMERR_OK, execCmdRet);
    EXPECT_EQ(shmOccupiedSizeAfterInsert, shmOccupiedSizeAfterDrop);

    // 检验2，预期记录数有变化，record count，insert count， delete count有变化
    StViewBtreeStatT expectStatAfterDrop = {};
    expectStatAfterDrop.recordCount = 0;
    expectStatAfterDrop.insertCount = expectInsertPhyTableCount;
    // for each key, there is a mark deletion and a physical deletion
    expectStatAfterDrop.deleteCount = 2 * expectStatAfterDrop.insertCount;
    EXPECT_EQ(GMERR_OK, CheckBtreeViewFields("GM_SYS_TS_MAP_PK", expectStatAfterDrop));

    // 多了一个逻辑表
    expectStatAfterDrop.insertCount = expectInsertPhyTableCount + 1;
    // for each key, there is a mark deletion and a physical deletion
    expectStatAfterDrop.deleteCount = 2 * expectStatAfterDrop.insertCount;
    EXPECT_EQ(GMERR_OK, CheckBtreeViewFields("GM_SYS_TS_MAP_IDX_BOUNDARY_IDX", expectStatAfterDrop));

    // 释放资源
    TsStDestroyGmcConnection(conn, stmt);
}

static inline void ConstructNumVector(vector<int> &insertVec, int startNum, int count, int step = 1)
{
    for (int i = 0; i < count; ++i) {
        insertVec.push_back(startNum);
        startNum += step;
    }
}

static void StTsInsertBulkData(GmcStmtT *stmt, int insertDataCount, char *tableName)
{
    char createTableCommand[200] = {};
    auto spRet = snprintf_s((char *)createTableCommand, sizeof(createTableCommand), sizeof(createTableCommand) - 1,
        "create table %s(age integer, id integer) with (time_col = 'id', "
        "interval = '1 hour');",
        tableName);
    ASSERT_GT(spRet, 0);
    auto ret = GmcExecDirect(stmt, createTableCommand, sizeof(createTableCommand));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &insertDataCount, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    vector<int> insertNum;
    int timeIntervalS = 3600;
    ConstructNumVector(insertNum, 1, insertDataCount, timeIntervalS);
    // 构建数据
    int64_t *id = (int64_t *)malloc(insertDataCount * sizeof(int64_t));
    int64_t *age = (int64_t *)malloc(insertDataCount * sizeof(int64_t));
    for (int i = 0; i < insertDataCount; ++i) {
        id[i] = insertNum[i] + 5;
        age[i] = insertNum[i];
    }
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, &id[0], sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, &age[0], sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    // 释放资源
    free(id);
    free(age);
}

/**
 * @tc.name: ViewMemOverheadCreateMultipleTableLessData_02
 * @tc.desc: test view logic label in ts scenario while the logic label the heap addr is (-1, -1)
 * logic label's data will not be stored in storage engine. DmVertexLabelIsNoHeap--DmVertexLabelIsTsLogical
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20230516001694.091.002,AR.SR.IR20240326001076.033.002
 * @tc.author: yangyongji
 */
TEST_F(StTsViewGmc, DISABLED_ViewTsLogicTableNoHeap_05)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 申请conn资源
    TsStInitGmcConnection(&conn, &stmt);

    // 插入数据，模拟创建出逻辑表和物理表
    char *tableName = "tsViewTsNoHeap";
    const uint32_t insertDataCount = 10;
    StTsInsertBulkData(stmt, insertDataCount, tableName);

    TsStDestroyGmcConnection(conn, stmt);

    // 校验，预期查询heap表相关视图功能正常。此时时序有一个ts逻辑表，属于vl表
    vector<string> queryViewNames;
    queryViewNames.push_back("SERVER_MEMORY_OVERHEAD");
    queryViewNames.push_back("STORAGE_SHMEM_INFO");
    queryViewNames.push_back("STORAGE_HEAP_VERTEX_LABEL_STAT");
    queryViewNames.push_back("STORAGE_TABLE_SHM_INFO");
    queryViewNames.push_back("STORAGE_HEAP_STAT");
    queryViewNames.push_back("STORAGE_FSM_STAT");

    string commonCmd("gmsysview -q V\\$");
    string execCmd;
    vector<string> matchStr;
    matchStr.push_back("fetched all records, finish!");
    for (auto viewNames : queryViewNames) {
        execCmd = commonCmd + viewNames;
        auto cmdExecRet = StExecuteCommandWithMatchedStringArrays(execCmd, matchStr);
        EXPECT_EQ(0, cmdExecRet);
    }
}

TEST_F(StTsViewGmc, ViewTsBufferpool_06)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 申请conn资源
    TsStInitGmcConnection(&conn, &stmt);

    // 插入数据，模拟创建出逻辑表和物理表
    char *tableName = "tsViewTsNoHeap";
    const uint32_t insertDataCount = 10;
    StTsInsertBulkData(stmt, insertDataCount, tableName);

    // 查询bufferpool视图并校验字段
    char viewCmd[512] = {0};

    // 获取page size，校验page size
    uint32_t expPageSize = 0;
    auto execCmdRet = StQueryPageSize(expPageSize);
    EXPECT_EQ(GMERR_OK, execCmdRet);
    auto spRet = snprintf_s(viewCmd, sizeof(viewCmd), sizeof(viewCmd) - 1,
        "gmsysview -q V\\$STORAGE_BUFFERPOOL_STAT | grep PAGE_SIZE  | awk '{print $2}'");
    EXPECT_GT(spRet, GMERR_OK);
    uint32_t findPageSize = 0;
    execCmdRet = StExecuteCommandWlToCount(viewCmd, findPageSize);
    EXPECT_EQ(GMERR_OK, execCmdRet);
    // expPageSize单位是Bytes
    EXPECT_EQ(expPageSize, SIZE_K(findPageSize));

    // 校验bufpool Size
    spRet = snprintf_s(viewCmd, sizeof(viewCmd), sizeof(viewCmd) - 1,
        "gmsysview -q V\\$STORAGE_BUFFERPOOL_STAT | grep BUFFERPOOL_SIZE | awk '{print $2}'");
    EXPECT_GT(spRet, GMERR_OK);
    uint32_t findBufpoolSize = 0;
    execCmdRet = StExecuteCommandWlToCount(viewCmd, findBufpoolSize);
    EXPECT_EQ(GMERR_OK, execCmdRet);
    uint32_t expectBufferpoolSize = 0;
    execCmdRet = StQueryBufferpoolSize(expectBufferpoolSize);
    EXPECT_EQ(GMERR_OK, execCmdRet);
    EXPECT_EQ(expectBufferpoolSize, findBufpoolSize);

    // 校验Bufferpool回收策略
    spRet = snprintf_s(viewCmd, sizeof(viewCmd), sizeof(viewCmd) - 1,
        "gmsysview -q V\\$STORAGE_BUFFERPOOL_STAT | grep RECYCLE_POLICY | awk '{print $2}'");
    EXPECT_GT(spRet, GMERR_OK);
    uint32_t findBufpoolPolicy = 0;
    execCmdRet = StExecuteCommandWlToCount(viewCmd, findBufpoolPolicy);
    EXPECT_EQ(GMERR_OK, execCmdRet);
    uint32_t expRecycPolicy = 0;
    execCmdRet = StQueryBufferpoolRecyclePolicy(expRecycPolicy);
    EXPECT_EQ(GMERR_OK, execCmdRet);
    EXPECT_EQ(expRecycPolicy, findBufpoolPolicy);

    // 初始化一个非0的任意值，校验没用到的scanList字段，预期为0
    spRet = snprintf_s(viewCmd, sizeof(viewCmd), sizeof(viewCmd) - 1,
        "gmsysview -q V\\$STORAGE_BUFFERPOOL_STAT | grep SCAN_LIST_COUNT | awk '{print $2}'");
    EXPECT_GT(spRet, GMERR_OK);
    uint32_t scanCount = 888;
    execCmdRet = StExecuteCommandWlToCount(viewCmd, scanCount);
    EXPECT_EQ(GMERR_OK, execCmdRet);
    uint32_t expScanListCount = 0;
    EXPECT_EQ(expScanListCount, scanCount);

    TsStDestroyGmcConnection(conn, stmt);
}
