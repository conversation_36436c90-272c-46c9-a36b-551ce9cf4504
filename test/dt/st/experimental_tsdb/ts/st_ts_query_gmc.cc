/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: st for ts query
 * Author: lihainuo
 * Create: 2023/9/19
 */

#include <gtest/gtest.h>
#include <vector>
#include <string>
#include <unordered_map>
#include <iostream>
#include "adpt_string.h"
#include "adpt_sleep.h"
#include "StartDbServer.h"
#include "InitClt.h"
#include "gmc_sql.h"
#include "clt_ts.h"
#include "adpt_printf.h"
#include "gmc_sysview.h"
#include "st_common.h"

using namespace std;

const static char *g_cfgPersist = "ts/gmserver_ts.ini";
const static char *g_cfgLargeResult = "ts/gmserver_ts_large_result.ini";
const static char *g_cfgHighCard = "ts_perf_nlog/gmserver_fw_template.ini";
const static char *g_cfgHighCardPersist = "ts_regress/gmserver_ts_perf.ini";

class StTsQueryGmc : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("rm -rf /data/gmdb");
        DbSleep(100);
        StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
        DbSleep(100);
    }
    static void TearDownTestCase()
    {
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("rm -rf /data/gmdb");
    }
};

static void InitConnAndStmt(GmcConnT **conn, GmcStmtT **stmt)
{
    static GmcConnOptionsT *connOptions = NULL;
    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(*conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

static void UnInitConnAndStmt(GmcConnT *conn, GmcStmtT *stmt)
{
    GmcFreeStmt(stmt);
    Status ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsQueryGmc, QueryOpSimple1)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tablequery(age integer, id integer, worktime integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[40] = "select worktime from tablequery;";
    char qryCommand2[50] = "select age from tablequery where age > 29;";
    char qryCommand3[70] = "select * from tablequery where id >= 2 and age = 30;";
    char qryCommand4[70] = "select age, worktime from tablequery order by id desc;";

    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    uint32_t size = 0;
    bool eof = false;
    int64_t id[] = {1, 2, 4};
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    char tableName[30] = "tablequery";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 测试获取表类型的相关接口
    uint32_t labelType = DB_INVALID_UINT32;
    EXPECT_EQ(GMERR_OK, GmcGetLabelTypeByName(stmt, tableName, &labelType));
    EXPECT_EQ(GMC_LABEL_TYPE_VERTEX, labelType);
    GmcVertexLabelTypeE vertexLabelType = GMC_VERTEX_TYPE_BUTT;
    EXPECT_EQ(GMERR_OK, GmcGetVertexLabelTypeByName(stmt, tableName, &vertexLabelType));
    EXPECT_EQ(GMC_VERTEX_TYPE_TS, vertexLabelType);
    GmcDtlLabelTypeE dtlLabelType = GMC_DTL_TYPE_BUTT;
    EXPECT_EQ(GMERR_DATA_EXCEPTION, GmcGetDatalogLabelTypeByName(stmt, tableName, &dtlLabelType));

    // 系统表有专门的namespace，直接查预期查不到
    const char *sysTableName = "GM_SYS_VL";
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcGetLabelTypeByName(stmt, sysTableName, &labelType));
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcGetVertexLabelTypeByName(stmt, sysTableName, &vertexLabelType));
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcGetDatalogLabelTypeByName(stmt, sysTableName, &dtlLabelType));

    ret = GmcGetVertexPropertySizeById(stmt, 0, &size);  // abnormal situation
    EXPECT_NE(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t ageRes = 0;
    int64_t worktimeRes = 0;
    bool isNull = false;

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand1, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)8);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(worktimeRes, worktime[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    // cmd 2
    ret = GmcExecDirect(stmt, qryCommand2, 50);
    EXPECT_EQ(ret, GMERR_OK);
    i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, age[1]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    // cmd 3
    ret = GmcExecDirect(stmt, qryCommand3, 70);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertySizeByName(stmt, "age", &size);
    EXPECT_EQ(size, (uint32_t)8);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertySizeById(stmt, 2, &size);
    EXPECT_EQ(size, (uint32_t)8);
    EXPECT_EQ(ret, GMERR_OK);
    eof = false;
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &ageRes, &propSize, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertySizeByName(stmt, "id", &size);
    EXPECT_EQ(size, (uint32_t)8);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &worktimeRes, &propSize, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(ageRes, age[1]);
    EXPECT_EQ(idRes, id[1]);
    EXPECT_EQ(worktimeRes, worktime[1]);

    // cmd 4
    ret = GmcExecDirect(stmt, qryCommand4, 70);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    i = 0;
    eof = false;
    ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
    EXPECT_EQ(size, (uint32_t)8);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t resIdx = 2;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, age[resIdx]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(worktimeRes, worktime[resIdx]);
        resIdx--;
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    char qryCommand5[256] = "select age, count(id), 42 from tablequery where id >= 1 group by age order by age;";
    ret = GmcExecDirect(stmt, qryCommand5, 256);
    EXPECT_EQ(ret, GMERR_OK);

    char qryCommand6[256] = "select -12, +42 from tablequery;";
    ret = GmcExecDirect(stmt, qryCommand6, 256);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);

    UnInitConnAndStmt(conn, stmt);
}

typedef struct {
    char (*name)[256];
    int64_t *age;
    int64_t *id;
    int64_t *worktime;
    int64_t *salary;
    uint32_t rowNum;
} Data;

typedef struct {
    char (*name)[256];
    char (*ip)[33];
    int64_t *age;
    int64_t *id;
    int64_t *worktime;
    int64_t *salary;
    uint32_t rowNum;
} IpData;

void CreateTableAndBulkInsert(GmcStmtT *stmt, Data data, const char *tableName)
{
    assert(tableName != NULL);

    char ddlCommand[256];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(256), age integer, id integer, worktime integer, salary integer) "
        "with (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        tableName);
    Status ret = GmcExecDirect(stmt, ddlCommand, 256);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 256, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

void BulkInsertWithIp(GmcStmtT *stmt, IpData data, const char *tableName)
{
    assert(tableName != NULL);
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_FIXED, data.ip, 33, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

static void CreateTable(GmcStmtT *stmt, const char *tableName)
{
    assert(tableName != NULL);

    char ddlCommand[256];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(age integer, id integer, worktime integer, salary integer) "
        "with (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        tableName);
    Status ret = GmcExecDirect(stmt, ddlCommand, 256);
    EXPECT_EQ(ret, GMERR_OK);
}

static Status CreateTableWithIp(GmcStmtT *stmt, const char *tableName)
{
    assert(tableName != NULL);

    char ddlCommand[256];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(age integer, id integer, worktime integer, salary integer, ip inet) "
        "with (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        tableName);
    return GmcExecDirect(stmt, ddlCommand, 256);
}

static void InsertIpTestDataWithName(GmcStmtT *stmt, char *name)
{
    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    char ips[][33] = {"FFFFFFFF", "FFFFFFFF", "abcdabcdabcdabcdabcdabcdabcdabcd", "FFFFFFFD", "FFFFFFFE", "00000001"};
    int64_t ages[] = {29, 30, 19, 23, 45, 34};
    int64_t ids[] = {1, 2, 3, 4, 5, 6};
    int64_t worktimes[] = {24, 18, 25, 11, 12, 10};
    int64_t salaries[] = {10000, 20000, 4000, 30000, 31000, 10000};
    uint32_t rowNum = 6;

    IpData data = {
        .name = names, .ip = ips, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    BulkInsertWithIp(stmt, data, name);
}

static void CreateTableWithIpCompression(GmcStmtT *stmt)
{
    char ddlCommand[256] = "create table testCom(age integer, id integer, worktime integer, salary integer, ip inet) "
                           "with (time_col = 'worktime', interval = '1 hour', compression = 'fast(zstar)', ttl = '3 "
                           "hours');";
    Status ret = GmcExecDirect(stmt, ddlCommand, 256);
    EXPECT_EQ(ret, GMERR_OK);
    InsertIpTestDataWithName(stmt, "testCom");

    char dropCommand[60] = "drop table testCom;";
    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char ddlCommand1[256] = "create table testCom1(age integer, id integer, worktime integer, salary integer, ip inet) "
                            "with (time_col = 'worktime', interval = '1 hour', compression = 'no', ttl = '3 "
                            "hours');";
    ret = GmcExecDirect(stmt, ddlCommand1, 256);
    EXPECT_EQ(ret, GMERR_OK);
    InsertIpTestDataWithName(stmt, "testCom1");
    char dropCommand1[60] = "drop table testCom1;";
    ret = GmcExecDirect(stmt, dropCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char ddlCommand2[256] = "create table testCom2(age integer, id integer, worktime integer, salary integer, ip inet) "
                            "with (time_col = 'worktime', interval = '1 hour', compression = 'fast', ttl = '3 "
                            "hours');";
    ret = GmcExecDirect(stmt, ddlCommand2, 256);
    EXPECT_EQ(ret, GMERR_OK);
    InsertIpTestDataWithName(stmt, "testCom2");
    char dropCommand2[60] = "drop table testCom2;";
    ret = GmcExecDirect(stmt, dropCommand2, 200);
    EXPECT_EQ(ret, GMERR_OK);
}

void BulkInsertNoStr(GmcStmtT *stmt, Data data, const char *tableName)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

static Status ExecQuerySql(GmcStmtT *stmt, const char *sql)
{
    assert(sql != NULL);
    return GmcExecDirect(stmt, sql, 150);
}

static Status ExecQuerySqlWithRes(GmcStmtT *stmt, const char *sql, uint32_t sqlLen)
{
    assert(sql != NULL);
    return GmcExecDirect(stmt, sql, sqlLen);
}

TEST_F(StTsQueryGmc, QueryOpSimple2)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    uint32_t propSize = 0;
    bool eof = false;

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, 34};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 18, 25, 11, 12, 10};
    int64_t salaries[] = {10000, 20000, 4000, 30000, 31000, 10000};
    uint32_t rowNum = 6;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery2");

    const char *queryCommand = "select name, id, worktime, salary from tablequery2 "
                               "where worktime <= 12 and salary <= 30000;";

    ret = ExecQuerySql(stmt, queryCommand);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t worktimeRes = 0;
    int64_t salaryRes = 0;
    char nameRes[256] = {0};
    bool isNull = false;

    uint32_t i = 0;
    uint32_t size = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, -1, &size);
        EXPECT_NE(ret, GMERR_OK);
        ret = GmcGetVertexPropertySizeById(stmt, 256, &size);
        EXPECT_NE(ret, GMERR_OK);
        ret = GmcGetVertexPropertySizeByName(stmt, "UNDEFINED", &size);
        EXPECT_NE(ret, GMERR_OK);
        ret = GmcGetVertexPropertySizeByName(stmt, NULL, &size);
        EXPECT_NE(ret, GMERR_OK);
        size = 0;
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &salaryRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)256);
        EXPECT_EQ(ret, GMERR_OK);
        size = 0;
        ret = GmcGetVertexPropertySizeByName(stmt, "name", &size);
        EXPECT_EQ(size, (uint32_t)256);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        switch (i) {
            case 0: {
                EXPECT_EQ(idRes, 9);
                EXPECT_EQ(worktimeRes, 11);
                EXPECT_EQ(salaryRes, 30000);
                EXPECT_EQ(DbStrCmp((const char *)nameRes, "olivia", true), 0);
                break;
            }
            case 1: {
                EXPECT_EQ(idRes, 14);
                EXPECT_EQ(worktimeRes, 10);
                EXPECT_EQ(salaryRes, 10000);
                EXPECT_EQ(DbStrCmp((const char *)nameRes, "lucy", true), 0);
                break;
            }
            default:
                EXPECT_EQ(1, 0);
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpSimple3)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    uint32_t propSize = 0;
    bool eof = false;

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    char ddlCommand[200] = "create table tablequery3(name char(256), age integer, id integer, worktime integer,"
                           " salary integer) with (time_col = 'worktime', interval = '1 hour',"
                           " compression = 'fast(rapidlz)', ttl = '3 hours');";
    char dropCommand[200] = "drop table tablequery3;";
    uint32_t rowNum = 6;
    char name[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int32_t age[] = {29, 30, 19, 23, 45, 34};
    int8_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 18, 25, 11, 12, 10};
    uint32_t salary[] = {10000, 20000, 4000, 30000, 31000, 10000};

    char tableName[35] = "tablequery3";
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT8, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT32, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_UINT32, salary, sizeof(salary[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 256, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    char qryCommand[150] = "select * from tablequery3;";
    ret = GmcExecDirect(stmt, qryCommand, 150);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t worktimeRes = 0;
    int64_t salaryRes = 0;
    int64_t ageRes = 0;
    char nameRes[256] = {0};
    bool isNull = false;

    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &idRes, &propSize, &isNull);
        EXPECT_EQ(idRes, id[i]);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(worktimeRes, worktime[i]);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &salaryRes, &propSize, &isNull);
        EXPECT_EQ(salaryRes, salary[i]);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ageRes, age[i]);
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        EXPECT_EQ(DbStrCmp(nameRes, name[i], false), 0);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)6);

    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpSimpleChinese)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    uint32_t propSize = 0;
    bool eof = false;

    InitConnAndStmt(&conn, &stmt);

    char names[][256] = {"小。、明", "nut", "刚《）}？子", "olivia", "李‘小 ”明", "小，：；红"};
    int64_t ages[] = {29, 30, 19, 23, 45, 34};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 18, 25, 11, 12, 10};
    int64_t salaries[] = {10000, 20000, 4000, 30000, 31000, 10000};
    uint32_t rowNum = 6;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequeryCN");

    const char *queryCommand = "select name, id, worktime, salary from tablequeryCN "
                               "where name LIKE '小%' order by id;";
    system("gmsysview_ts -sql \"select name, id, worktime, salary from tablequeryCN "
           "where name LIKE '小%' order by id;\"");

    Status ret = ExecQuerySql(stmt, queryCommand);
    EXPECT_EQ(ret, GMERR_OK);

    char nameRes[256] = {0};
    bool isNull = false;

    uint32_t i = 0;
    uint32_t size = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        size = 0;
        ret = GmcGetVertexPropertySizeByName(stmt, "name", &size);
        EXPECT_EQ(size, (uint32_t)256);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        switch (i) {
            case 0: {
                EXPECT_EQ(DbStrCmp((const char *)nameRes, "小。、明", true), 0);
                break;
            }
            case 1: {
                EXPECT_EQ(DbStrCmp((const char *)nameRes, "小，：；红", true), 0);
                break;
            }
            default:
                EXPECT_EQ(1, 0);
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    const char *queryCommand2 = "select name, id, worktime, salary from tablequeryCN "
                                "where name LIKE '%子' escape '?' order by id;";
    system("gmsysview_ts -sql \"select name, id, worktime, salary from tablequeryCN "
           "where name LIKE '%子' escape '?' order by id;\"");

    ret = ExecQuerySql(stmt, queryCommand2);
    EXPECT_EQ(ret, GMERR_OK);

    i = 0;
    size = 0;
    eof = false;
    while (!eof) {
        Status ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        size = 0;
        ret = GmcGetVertexPropertySizeByName(stmt, "name", &size);
        EXPECT_EQ(size, (uint32_t)256);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(DbStrCmp((const char *)nameRes, "刚《）}？子", true), 0);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    const char *queryCommand3 = "select name, id, worktime, salary from tablequeryCN "
                                "where name LIKE '%小%' escape '?' order by id;";
    system("gmsysview_ts -sql \"select name, id, worktime, salary from tablequeryCN "
           "where name LIKE '%子' escape '?' order by id;\"");

    ret = ExecQuerySql(stmt, queryCommand3);
    EXPECT_EQ(ret, GMERR_OK);

    i = 0;
    size = 0;
    eof = false;
    while (!eof) {
        Status ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        size = 0;
        ret = GmcGetVertexPropertySizeByName(stmt, "name", &size);
        EXPECT_EQ(size, (uint32_t)256);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        switch (i) {
            case 0: {
                EXPECT_EQ(DbStrCmp((const char *)nameRes, "小。、明", true), 0);
                break;
            }
            case 1: {
                EXPECT_EQ(DbStrCmp((const char *)nameRes, "李‘小 ”明", true), 0);
                break;
            }
            case 2: {
                EXPECT_EQ(DbStrCmp((const char *)nameRes, "小，：；红", true), 0);
                break;
            }
            default:
                EXPECT_EQ(1, 0);
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    GmcExecDirect(stmt, "drop table tablequeryCN;", 70);
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpSimpleCount)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    uint32_t propSize = 0;
    bool eof = false;

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    char ddlCommand[200] = "create table tablequery5(name char(10), age integer, id integer, worktime integer,"
                           " salary integer) with (time_col = 'worktime', interval = '1 hour',"
                           " compression = 'fast(rapidlz)', ttl = '3 hours');";
    uint32_t rowNum = 6;
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int32_t age[] = {29, 30, 19, 23, 45, 34};
    int8_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 18, 25, 11, 12, 10};
    uint32_t salary[] = {10000, 20000, 4000, 30000, 31000, 10000};

    char tableName[35] = "tablequery5";
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT8, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT32, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_UINT32, salary, sizeof(salary[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    int64_t dataCount = 0;

    char qryCommand[150] = "select count(age) from tablequery5;";

    ret = GmcExecDirect(stmt, qryCommand, 150);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t countRes = 0;

    bool isNull = false;

    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &countRes, &propSize, &isNull);
        EXPECT_EQ(countRes, 6);

        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);
    char qryCommand1[150] = "select count(*) from tablequery5 where salary > 10000;";

    ret = GmcExecDirect(stmt, qryCommand1, 150);
    EXPECT_EQ(ret, GMERR_OK);
    countRes = 0;
    int64_t idMinRes = 0;
    int64_t idMaxRes = 0;
    int64_t countRes1 = 0;
    i = 0;
    eof = false;

    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &countRes, &propSize, &isNull);
        EXPECT_EQ(countRes, 3);
        i++;
    }
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(dataCount, 1);
    EXPECT_EQ(i, (uint32_t)1);

    char qryCommand2[150] = "select count(age), count(*) from tablequery5 where salary > 10000;";
    ret = GmcExecDirect(stmt, qryCommand2, 150);
    EXPECT_EQ(ret, GMERR_OK);
    i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &countRes, &propSize, &isNull);
        EXPECT_EQ(countRes, 3);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &countRes1, &propSize, &isNull);
        EXPECT_EQ(countRes1, 3);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(dataCount, 1);

    char qryCommand3[150] = "select count(age), min(id), max(id) from tablequery5 where salary > 10000;";
    ret = GmcExecDirect(stmt, qryCommand3, 150);
    EXPECT_EQ(ret, GMERR_OK);
    i = 0;
    eof = false;
    countRes = 0;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &countRes, &propSize, &isNull);
        EXPECT_EQ(countRes, 3);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idMinRes, &propSize, &isNull);
        EXPECT_EQ(idMinRes, 2);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &idMaxRes, &propSize, &isNull);
        EXPECT_EQ(idMaxRes, 9);
        i++;
    }

    char qryCommand4[150] = "select count(*), avg(id) from tablequery5 where salary > 10000;";
    ret = GmcExecDirect(stmt, qryCommand4, 150);
    EXPECT_EQ(ret, GMERR_OK);

    // 和SQL一样与SQLITE保持一致，认为该语句合法
    char qryCommand5[150] = "select count(*), id from tablequery5 where salary > 10000;";
    ret = GmcExecDirect(stmt, qryCommand5, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(dataCount, 1);

    char qryCommand6[150] = "select count(age) from tablequery5 order by count(age);";
    ret = GmcExecDirect(stmt, qryCommand6, 150);
    EXPECT_NE(ret, GMERR_OK);

    char qryCommand7[150] = "select count(*) from tablequery5 order by count(*);";
    ret = GmcExecDirect(stmt, qryCommand7, 150);
    EXPECT_NE(ret, GMERR_OK);

    char dropCommand[60] = "drop table tablequery5;";
    ret = GmcExecDirect(stmt, dropCommand, 60);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

void TestGroupByWithoutAgg()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);

    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, 34};
    int64_t ids[] = {1, 1, 2, 2, 3, 2};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint32_t rowNum = 6;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery4");

    const char *queryCommand = "select worktime, id from "
                               "tablequery4 group by worktime, id order by worktime;";
    // expact result: 11,2
    //                12,3
    //                24,1
    Status ret = ExecQuerySql(stmt, queryCommand);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    int64_t id = 0;
    bool isNull = false;

    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &id, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        switch (i) {
            case 0: {
                EXPECT_EQ(worktimeRes, 11);
                EXPECT_EQ(id, 2);
                break;
            }
            case 1: {
                EXPECT_EQ(worktimeRes, 12);
                EXPECT_EQ(id, 3);
                break;
            }
            case 2: {
                EXPECT_EQ(worktimeRes, 24);
                EXPECT_EQ(id, 1);
                break;
            }
            default:
                EXPECT_EQ(1, 0);
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    UnInitConnAndStmt(conn, stmt);
}

void TestGroupByWithoutAgg1()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);

    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, 34};
    int64_t ids[] = {1, 1, 2, 2, 3, 2};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint32_t rowNum = 6;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery4");

    const char *queryCommand = "select worktime from "
                               "tablequery4 group by worktime order by worktime;";
    // expact result: 11
    //                12
    //                24
    Status ret = ExecQuerySql(stmt, queryCommand);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    bool isNull = false;

    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        switch (i) {
            case 0: {
                EXPECT_EQ(worktimeRes, 11);
                break;
            }
            case 1: {
                EXPECT_EQ(worktimeRes, 12);
                break;
            }
            case 2: {
                EXPECT_EQ(worktimeRes, 24);
                break;
            }
            default:
                EXPECT_EQ(1, 0);
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    const char *queryCommand1 = "select worktime, name, age, max(salary) from "
                                "tablequery4 group by worktime order by worktime;";
    // expact result: 11  bob    19    30000
    //                12  tim    45    31000
    //                24  david  29    20000
    ret = ExecQuerySql(stmt, queryCommand1);
    EXPECT_EQ(ret, GMERR_OK);

    propSize = 0;
    eof = false;
    worktimeRes = 0;
    int64_t ageRes = 0;
    int64_t salaryRes = 0;
    isNull = false;
    char nameRes[256] = {0};

    i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &salaryRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        switch (i) {
            case 0: {
                EXPECT_EQ(worktimeRes, 11);
                EXPECT_EQ(DbStrCmp(nameRes, "bob", false), 0);
                EXPECT_EQ(ageRes, 19);
                EXPECT_EQ(salaryRes, 30000);
                break;
            }
            case 1: {
                EXPECT_EQ(worktimeRes, 12);
                EXPECT_EQ(DbStrCmp(nameRes, "tim", false), 0);
                EXPECT_EQ(ageRes, 45);
                EXPECT_EQ(salaryRes, 31000);
                break;
            }
            case 2: {
                EXPECT_EQ(worktimeRes, 24);
                EXPECT_EQ(DbStrCmp(nameRes, "david", false), 0);
                EXPECT_EQ(ageRes, 29);
                EXPECT_EQ(salaryRes, 20000);
                break;
            }
            default:
                EXPECT_EQ(1, 0);
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    UnInitConnAndStmt(conn, stmt);
}

void TestColSetAgg()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);

    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy", "lucy1", "lucy2", "lucy3", "lucy4"};
    int64_t ages[] = {1, 1, 29, 30, 19, 23, 45, 34, 34, 34, 34, 34, 99, 99};
    int64_t ids[] = {0, 0, 1, 1, 2, 2, 3, 2, 2, 2, 2, 2, 4, 4};
    int64_t worktimes[] = {0, 0, 24, 24, 11, 11, 12, 11, 11, 11, 11, 11, 0, 0};
    int64_t salaries[] = {0, 0, 20000, 10000, 4000, 30000, 31000, 10000, 10000, 10000, 10000, 10000, 0, 0};
    uint32_t rowNum = 10;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery4");

    const char *queryCommand = "select worktime, COLSET(salary) from "
                               "tablequery4 group by worktime order by worktime;";
    // expect result: 0, "0"
    //                11, "4000, 30000, 10000"
    //                12, "31000"
    //                24, "20000, 10000"
    Status ret = ExecQuerySql(stmt, queryCommand);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    char res[256] = {0};
    char *res0 = "0";
    char *res1 = "4000, 30000, 10000";
    char *res2 = "31000";
    char *res3 = "20000, 10000";

    bool isNull = false;

    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = 19;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &res, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        switch (i) {
            case 0: {
                EXPECT_EQ(worktimeRes, 0);
                EXPECT_STREQ(res, res0);
                break;
            }
            case 1: {
                EXPECT_EQ(worktimeRes, 11);
                EXPECT_STREQ(res, res1);
                break;
            }
            case 2: {
                EXPECT_EQ(worktimeRes, 12);
                EXPECT_STREQ(res, res2);
                break;
            }
            case 3: {
                EXPECT_EQ(worktimeRes, 24);
                EXPECT_STREQ(res, res3);
                break;
            }
            default:
                EXPECT_EQ(1, 0);
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)4);

    printf("*************************语法测试******************************\n");

    // 不能在order by 中使用
    const char *queryCommand2 = "select worktime from "
                                "tablequery4 group by worktime order by COLSET(salary);";
    ret = GmcExecDirect(stmt, queryCommand2, 150);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    // 多次使用
    const char *queryCommand3 = "select worktime, COLSET(salary), COLSET(salary) from "
                                "tablequery4 group by worktime order by worktime;";
    ret = GmcExecDirect(stmt, queryCommand3, 150);
    EXPECT_EQ(ret, GMERR_OK);

    // 非聚合语句使用
    const char *queryCommand4 = "select worktime, colset(salary) from tablequery4 where id > 2;";
    ret = GmcExecDirect(stmt, queryCommand4, 150);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    // 只支持单个列
    const char *queryCommand5 = "select worktime, colset(salary + 1) from tablequery4 where id > 2;";
    ret = GmcExecDirect(stmt, queryCommand5, 150);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    // 基础语法测试
    const char *queryCommand6 = "select worktime, colset() from tablequery4 where id > 2;";
    ret = GmcExecDirect(stmt, queryCommand6, 150);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    const char *queryCommand7 = "select worktime, colset from tablequery4 where id > 2;";
    ret = GmcExecDirect(stmt, queryCommand7, 150);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    const char *queryCommand8 = "select worktime, col_set(id) from tablequery4 where id > 2;";
    ret = GmcExecDirect(stmt, queryCommand8, 150);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    UnInitConnAndStmt(conn, stmt);
}

void TestColSetAgg1()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);

    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy", "lucy1", "lucy2", "lucy3", "lucy4"};
    int64_t ids[] = {0, 0, 1, 1, 2, 2, 3, 2, 2, 2, 2, 2, 4, 4};
    int64_t ages[] = {99, 99, 29, 30, 19, 23, 45, 34, 34, 34, 34, 34, 1, 1};
    int64_t salaries[] = {0, 0, 20000, 10000, 4000, 30000, 31000, 10000, 10000, 10000, 10000, 10000, 1, 1};
    int64_t worktimes[] = {0, 0, 24, 24, 11, 11, 12, 11, 11, 11, 11, 11, 0, 0};
    uint32_t rowNum = 14;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery4");
    // 多个colset
    const char *queryCommandA = "select worktime, COLSET(salary), COLSET(age) from "
                                "tablequery4 group by worktime order by worktime;";
    // expect result: 0, "0"
    //                11, "4000, 30000, 10000", ""
    //                12, "31000"
    //                24, "20000, 10000"
    Status ret = GMERR_OK;
    ret = ExecQuerySql(stmt, queryCommandA);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t propSize1 = 0;
    bool eof1 = false;
    int64_t worktimeRes1 = 0;
    char resA[256] = {0};
    char resB[256] = {0};
    char *resA0 = "0, 1";
    char *resA1 = "4000, 30000, 10000";
    char *resA2 = "31000";
    char *resA3 = "20000, 10000";

    char *resB0 = "99, 1";
    char *resB1 = "19, 23, 34";
    char *resB2 = "45";
    char *resB3 = "29, 30";
    bool isNull1 = false;

    uint32_t i1 = 0;
    eof1 = false;
    while (!eof1) {
        ret = GmcFetch(stmt, &eof1);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof1) {
            break;
        }
        propSize1 = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes1, &propSize1, &isNull1);
        EXPECT_EQ(ret, GMERR_OK);
        propSize1 = 50;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &resA, &propSize1, &isNull1);
        EXPECT_EQ(ret, GMERR_OK);
        propSize1 = 50;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &resB, &propSize1, &isNull1);
        EXPECT_EQ(ret, GMERR_OK);
        switch (i1) {
            case 0: {
                EXPECT_EQ(worktimeRes1, 0);
                EXPECT_STREQ(resA0, resA);
                EXPECT_STREQ(resB0, resB);
                break;
            }
            case 1: {
                EXPECT_EQ(worktimeRes1, 11);
                EXPECT_STREQ(resA1, resA);
                EXPECT_STREQ(resB1, resB);
                break;
            }
            case 2: {
                EXPECT_EQ(worktimeRes1, 12);
                EXPECT_STREQ(resA2, resA);
                EXPECT_STREQ(resB2, resB);
                break;
            }
            case 3: {
                EXPECT_EQ(worktimeRes1, 24);
                EXPECT_STREQ(resA3, resA);
                EXPECT_STREQ(resB3, resB);
                break;
            }
            default:
                EXPECT_EQ(1, 0);
        }
        i1++;
    }
    EXPECT_EQ(i1, (uint32_t)4);

    UnInitConnAndStmt(conn, stmt);
}

// 大数据量超过ts内部字符串最大长度65535
void TestColSetAggBigData()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);

    CreateTable(stmt, "tablequery4");

    uint32_t rowNum = 10000;
    int64_t ages[rowNum];
    int64_t ids[rowNum];
    int64_t worktimes[rowNum];
    int64_t salaries[rowNum];

    for (int i = 0; i < rowNum; i++) {
        ages[i] = 1;
        ids[i] = i;
        worktimes[i] = 1;
        salaries[i] = 100000000 + i;
    }

    Data data = {0};
    data.age = ages;
    data.id = ids;
    data.worktime = worktimes;
    data.salary = salaries;
    data.rowNum = rowNum;

    BulkInsertNoStr(stmt, data, "tablequery4");

    const char *queryCommand = "select age, COLSET(salary) from tablequery4 group by age;";
    Status ret = ExecQuerySql(stmt, queryCommand);
    EXPECT_EQ(ret, GMERR_FIELD_OVERFLOW);

    UnInitConnAndStmt(conn, stmt);
}

// 大数据量ts内部字符串最大长度65535
void TestColSetAggBigDataMaxLen()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);

    CreateTable(stmt, "colset1");

    // rowNum为10949可达到65535
    uint32_t rowNum = 1000;
    int64_t ages[rowNum];
    int64_t ids[rowNum];
    int64_t worktimes[rowNum];
    int64_t salaries[rowNum];

    for (int i = 0; i < rowNum; i++) {
        ages[i] = i % 2;
        ids[i] = i;
        worktimes[i] = 1;
        salaries[i] = i;
    }

    Data data = {0};
    data.age = ages;
    data.id = ids;
    data.worktime = worktimes;
    data.salary = salaries;
    data.rowNum = rowNum;

    BulkInsertNoStr(stmt, data, "colset1");

    const char *queryCommand = "select age, COLSET(salary) from colset1 group by age;";
    Status ret = ExecQuerySql(stmt, queryCommand);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t propSize = 65535;
    bool eof = false;
    bool isNull = false;
    char res[65535] = {0};
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &res, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        printf("colset: %s\n", res);
    }

    UnInitConnAndStmt(conn, stmt);
}

// 大数据量负数超过ts内部字符串最大长度65535
void TestColSetAggBigDataNegative()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);

    CreateTable(stmt, "tablequery4");

    uint32_t rowNum = 10000;
    int64_t ages[rowNum];
    int64_t ids[rowNum];
    int64_t worktimes[rowNum];
    int64_t salaries[rowNum];

    for (int i = 0; i < rowNum; i++) {
        ages[i] = 1;
        ids[i] = i;
        worktimes[i] = 1;
        salaries[i] = -100000000 + i;
    }

    Data data = {0};
    data.age = ages;
    data.id = ids;
    data.worktime = worktimes;
    data.salary = salaries;
    data.rowNum = rowNum;

    BulkInsertNoStr(stmt, data, "tablequery4");

    const char *queryCommand = "select age, COLSET(salary) from tablequery4 group by age;";
    Status ret = ExecQuerySql(stmt, queryCommand);
    EXPECT_EQ(ret, GMERR_FIELD_OVERFLOW);

    UnInitConnAndStmt(conn, stmt);
}

// 大数据量测试算术表达式
void TestArithExprBigData()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);

    CreateTable(stmt, "arith1");

    uint32_t rowNum = 30000;
    int64_t ages[rowNum];
    int64_t ids[rowNum];
    int64_t salaries[rowNum];
    int64_t worktimes[rowNum];

    // 按3-6w, 6-9w, 0-3w顺序插入
    // 3-6w
    for (int i = 30000; i < 60000; i++) {
        ages[i - 30000] = i;
        ids[i - 30000] = i;
        worktimes[i - 30000] = i;
        salaries[i - 30000] = 70000;
    }
    Data data1 = {0};
    data1.age = ages;
    data1.id = ids;
    data1.worktime = worktimes;
    data1.salary = salaries;
    data1.rowNum = rowNum;
    BulkInsertNoStr(stmt, data1, "arith1");

    // 0-30w
    for (int i = 0; i < rowNum; i++) {
        ages[i] = i;
        ids[i] = i;
        worktimes[i] = i;
        salaries[i] = 30000 + i;
    }
    Data data2 = {0};
    data2.age = ages;
    data2.id = ids;
    data2.worktime = worktimes;
    data2.salary = salaries;
    data2.rowNum = rowNum;
    BulkInsertNoStr(stmt, data2, "arith1");

    // 60-90w
    for (int i = 60000; i < 90000; i++) {
        ages[i - 60000] = i;
        ids[i - 60000] = i;
        worktimes[i - 60000] = 0;
        salaries[i - 60000] = i;
    }
    Data data3 = {0};
    data3.age = ages;
    data3.id = ids;
    data3.worktime = worktimes;
    data3.salary = salaries;
    data3.rowNum = rowNum;
    BulkInsertNoStr(stmt, data3, "arith1");

    const char *queryCommand = "select age from arith1 where salary + id >= 120000;";
    Status ret = ExecQuerySql(stmt, queryCommand);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t cnt = 0;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }
        cnt++;
    }

    EXPECT_EQ(cnt, 40000);

    UnInitConnAndStmt(conn, stmt);
}

void TestTopNSortBigData()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);

    CreateTable(stmt, "arith1");

    uint32_t rowNum = 30000;
    int64_t ages[rowNum];
    int64_t ids[rowNum];
    int64_t salaries[rowNum];
    int64_t worktimes[rowNum];

    // 按3-6w, 6-9w, 0-3w顺序插入
    // 3-6w
    for (int i = 30000; i < 60000; i++) {
        ages[i - 30000] = i;
        ids[i - 30000] = i;
        worktimes[i - 30000] = i;
        salaries[i - 30000] = 70000;
    }
    Data data1 = {0};
    data1.age = ages;
    data1.id = ids;
    data1.worktime = worktimes;
    data1.salary = salaries;
    data1.rowNum = rowNum;
    BulkInsertNoStr(stmt, data1, "arith1");

    // 0-30w
    for (int i = 0; i < rowNum; i++) {
        ages[i] = i;
        ids[i] = i;
        worktimes[i] = i;
        salaries[i] = 30000 + i;
    }
    Data data2 = {0};
    data2.age = ages;
    data2.id = ids;
    data2.worktime = worktimes;
    data2.salary = salaries;
    data2.rowNum = rowNum;
    BulkInsertNoStr(stmt, data2, "arith1");

    // 60-90w
    for (int i = 60000; i < 90000; i++) {
        ages[i - 60000] = i;
        ids[i - 60000] = i;
        worktimes[i - 60000] = 0;
        salaries[i - 60000] = i;
    }
    Data data3 = {0};
    data3.age = ages;
    data3.id = ids;
    data3.worktime = worktimes;
    data3.salary = salaries;
    data3.rowNum = rowNum;
    BulkInsertNoStr(stmt, data3, "arith1");

    const char *queryCommand = "select * from arith1 order by worktime asc limit 10;";
    Status ret = ExecQuerySql(stmt, queryCommand);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t cnt = 0;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }
        cnt++;
    }
    EXPECT_EQ(cnt, 10);

    const char *queryCommand2 = "select * from arith1 order by worktime desc limit 10;";
    ret = ExecQuerySql(stmt, queryCommand2);
    EXPECT_EQ(ret, GMERR_OK);

    cnt = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }
        cnt++;
    }
    EXPECT_EQ(cnt, 10);

    UnInitConnAndStmt(conn, stmt);
}

static Status InsertIpTestDataWithInt(GmcStmtT *stmt)
{
    int64_t ages[] = {29, 30, 19, 23, 45, 34};
    int64_t ids[] = {1, 2, 3, 4, 5, 6};
    int64_t worktimes[] = {24, 18, 25, 11, 12, 10};
    int64_t salaries[] = {10000, 20000, 4000, 30000, 31000, 10000};
    int64_t ips[] = {1, 2, 3, 4, 5, 6};
    uint32_t rowNum = 6;

    Status ret = GmcPrepareStmtByLabelName(stmt, "test", GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, ages, sizeof(ages[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, salaries, sizeof(salaries[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_FIXED, ips, 33, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    return GmcExecute(stmt);
}

static void InsertIpTestData(GmcStmtT *stmt)
{
    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    char ips[][33] = {"FFFFFFFF", "FFFFFFFF", "abcdabcdabcdabcdabcdabcdabcdabcd", "FFFFFFFD", "FFFFFFFE", "00000001"};
    int64_t ages[] = {29, 30, 19, 23, 45, 34};
    int64_t ids[] = {1, 2, 3, 4, 5, 6};
    int64_t worktimes[] = {24, 18, 25, 11, 12, 10};
    int64_t salaries[] = {10000, 20000, 4000, 30000, 31000, 10000};
    uint32_t rowNum = 6;

    IpData data = {
        .name = names, .ip = ips, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    BulkInsertWithIp(stmt, data, "test");

    char names1[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    char ips1[][33] = {"FFFFFFFD", "1234567812345678abcdabcdabcdabcd", "abcdabcdabcdabcdabcdabcdabcdabcd", "00000001",
        "00000002", "10101041"};
    int64_t ages1[] = {29, 30, 19, 23, 45, 34};
    int64_t ids1[] = {1, 2, 3, 4, 5, 6};
    int64_t worktimes1[] = {24, 18, 25, 11, 12, 10};
    int64_t salaries1[] = {10000, 20000, 4000, 30000, 31000, 10000};
    uint32_t rowNum1 = 6;

    IpData data1 = {.name = names1,
        .ip = ips1,
        .age = ages1,
        .id = ids1,
        .worktime = worktimes1,
        .salary = salaries1,
        .rowNum = rowNum1};

    BulkInsertWithIp(stmt, data1, "test");
}

static void DropTable(GmcStmtT *stmt, char *tableName)
{
    char ddlCommand[256] = {0};
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand), "drop table %s;", tableName);
    Status ret = GmcExecDirect(stmt, ddlCommand, 60);
    EXPECT_EQ(GMERR_OK, ret);
}

static void InsertIpTestBigData(GmcStmtT *stmt)
{
    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy", "a", "b", "c", "d"};
    char ips[][33] = {"FFFFFFFF", "FFFFFFFF", "abcdabcdabcdabcdabcdabcdabcdabcd", "FFFFFFFD", "FFFFFFFE", "00000001",
        "10101010", "F0F0F0F0", "10F0F0F0", "F0F0F0F0"};
    int64_t ages[] = {29, 30, 19, 23, 45, 34, 19, 23, 45, 34};
    int64_t ids[] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    int64_t worktimes[] = {24, 18, 25, 11, 12, 10, 24, 18, 25, 11};
    int64_t salaries[] = {10000, 20000, 4000, 30000, 31000, 10000, 4000, 30000, 31000, 10000};
    uint32_t rowNum = 10;

    IpData data = {
        .name = names, .ip = ips, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    uint32_t cycCnt = 100;
    for (uint32_t i = 0; i < cycCnt; i++) {
        BulkInsertWithIp(stmt, data, "test");
        usleep(10000);
    }
}

static void InsertIpTestBigDataOnlyIpv4(GmcStmtT *stmt)
{
    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy", "a", "b", "c", "d"};
    char ips[][33] = {"FFFFFFFF", "FFFFFFFF", "20F00101", "FFFFFFFD", "FFFFFFFE", "00000001", "10101010", "F0F0F0F0",
        "10F0F0F0", "F0F0F0F0"};
    int64_t ages[] = {29, 30, 19, 23, 45, 34, 19, 23, 45, 34};
    int64_t ids[] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    int64_t worktimes[] = {24, 18, 25, 11, 12, 10, 24, 18, 25, 11};
    int64_t salaries[] = {10000, 20000, 4000, 30000, 31000, 10000, 4000, 30000, 31000, 10000};
    uint32_t rowNum = 10;

    IpData data = {
        .name = names, .ip = ips, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    uint32_t cycCnt = 100;
    for (uint32_t i = 0; i < cycCnt; i++) {
        BulkInsertWithIp(stmt, data, "test");
        usleep(10000);
    }
}

static void InsertIpTestBigDataOnlyIpv6(GmcStmtT *stmt, const char *tableName)
{
    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy", "a", "b", "c", "d"};
    char ips[][33] = {"abcdabcdabcdabcdabcdabcdabcdabcd", "abcdabcdabcdabcdabcdabcdabcdabcd",
        "abcdabcdabcdabcdabcdabcdabcdabcd", "FFFFFFFDFFFFFFFE0000000110101010", "00000000000000000000000000000000",
        "10101010101010101010101010101010", "F0F0F0F0F0F0F0F0F0F0F0F0F0F0F0F0", "10F0F0F010F0F0F010F0F0F010F0F0F0",
        "F0F0F0F0F0F0F0F0F0F0F0F0F0F0F0F0", "12341234123412341234123412341234"};
    int64_t ages[] = {29, 30, 19, 23, 45, 34, 19, 23, 45, 34};
    int64_t ids[] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    int64_t worktimes[] = {24, 18, 25, 11, 12, 10, 24, 18, 25, 11};
    int64_t salaries[] = {10000, 20000, 4000, 30000, 31000, 10000, 4000, 30000, 31000, 10000};
    uint32_t rowNum = 10;

    IpData data = {
        .name = names, .ip = ips, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    uint32_t cycCnt = 100;
    for (uint32_t i = 0; i < cycCnt; i++) {
        BulkInsertWithIp(stmt, data, tableName);
        usleep(10000);
    }
}

static void CheckIpQueryResult(GmcStmtT *stmt, uint32_t expectResNum)
{
    uint32_t propSize = 0;
    bool eof = false;
    int64_t maxAge = 0;
    int64_t sumSalary = 0;
    char ip[33] = {0};
    bool isNull = false;
    Status ret = GMERR_OK;

    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = 33;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &maxAge, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = 33;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumSalary, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = 33;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &ip, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        printf("id: %d, first:%ld, second:%ld, ip:%s\n", i, maxAge, sumSalary, ip);
        i++;
    }
    EXPECT_EQ(i, expectResNum);
}

static void CheckIpQueryResultWithFunc(GmcStmtT *stmt, uint32_t expectResNum)
{
    uint32_t propSize = 0;
    bool eof = false;
    int64_t maxAge = 0;
    char ipFirst[33] = {0};
    char ipLast[33] = {0};
    bool isNull = false;
    Status ret = GMERR_OK;

    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = 33;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &maxAge, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = 33;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ipFirst, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = 33;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &ipLast, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        printf("id: %d, maxAge:%ld, ipFirst:%s, ipLast:%s\n", i, maxAge, ipFirst, ipLast);
        i++;
    }
    EXPECT_EQ(i, expectResNum);
}

void TestIpTypeCompression()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);
    // 其他压缩类型
    CreateTableWithIpCompression(stmt);
    UnInitConnAndStmt(conn, stmt);
}

void TestWhereLimit()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);

    Status ret = CreateTableWithIp(stmt, "test");
    EXPECT_EQ(ret, GMERR_OK);

    // 128个过滤条件，编译正常
    char *queryCommand1 =
        "select age, ip from test where id = 0 or id = 1 or id = 2 or id = 3 or id = 4 or id = 5 or "
        "id = 6 or id = 7 or id = 8 or id = 9 or id = 10 or id = 11 or id = 12 or id = 13 or id = 14 or id = 15 or id "
        "= 16 or id = 17 or id = 18 or id = 19 or id = 20 or id = 21 or id = 22 or id = 23 or id = 24 or id = 25 or id "
        "= 26 or id = 27 or id = 28 or id = 29 or id = 30 or id = 31 or id > 0 or id > 1 or id > 2 or id > 3 or id > 4 "
        "or id > 5 or id > 6 or id > 7 or id > 8 or id > 9 or id > 10 or id > 11 or id > 12 or id > 13 or id > 14 or "
        "id > 15 or id > 16 or id > 17 or id > 18 or id > 19 or id > 20 or id > 21 or id > 22 or id > 23 or id > 24 or "
        "id > 25 or id > 26 or id > 27 or id > 28 or id > 29 or id > 30 or id > 31 or id < 0 or id < 1 or id < 2 or id "
        "< 3 or id < 4 or id < 5 or id < 6 or id < 7 or id < 8 or id < 9 or id < 10 or id < 11 or id < 12 or id < 13 "
        "or id < 14 or id < 15 or id < 16 or id < 17 or id < 18 or id < 19 or id < 20 or id < 21 or id < 22 or id < 23 "
        "or id < 24 or id < 25 or id < 26 or id < 27 or id < 28 or id < 29 or id < 30 or id < 31 or id != 0 or id != 1 "
        "or id != 2 or id != 3 or id != 4 or id != 5 or id != 6 or id != 7 or id != 8 or id != 9 or id != 10 or id != "
        "11 or id != 12 or id != 13 or id != 14 or id != 15 or id != 16 or id != 17 or id != 18 or id != 19 or id != "
        "20 or id != 21 or id != 22 or id != 23 or id != 24 or id != 25 or id != 26 or id != 27 or id != 28 or id != "
        "29 or id != 30  or id != 31;";
    ret = ExecQuerySqlWithRes(stmt, queryCommand1, 1500);
    EXPECT_EQ(ret, GMERR_OK);

    char gmsysview[] = "gmsysview";
    char sql[] = "-sql";
    char explain[] = "-explain";
    char *argv1[3] = {gmsysview, sql, queryCommand1};
    ret = GmcSysview(3, argv1, DbPrintfDefault);
    EXPECT_EQ(ret, GMERR_OK);
    char *argvv1[3] = {gmsysview, explain, queryCommand1};
    ret = GmcSysview(3, argvv1, DbPrintfDefault);
    EXPECT_EQ(ret, GMERR_OK);

    printf("------query 1 done------\n");

    // 129个过滤条件，编译报错
    const char *queryCommand =
        "select age, ip from test where id = 0 or id = 1 or id = 2 or id = 3 or id = 4 or id = 5 or "
        "id = 6 or id = 7 or id = 8 or id = 9 or id = 10 or id = 11 or id = 12 or id = 13 or id = 14 or id = 15 or id "
        "= 16 or id = 17 or id = 18 or id = 19 or id = 20 or id = 21 or id = 22 or id = 23 or id = 24 or id = 25 or id "
        "= 26 or id = 27 or id = 28 or id = 29 or id = 30 or id = 31 or id > 0 or id > 1 or id > 2 or id > 3 or id > 4 "
        "or id > 5 or id > 6 or id > 7 or id > 8 or id > 9 or id > 10 or id > 11 or id > 12 or id > 13 or id > 14 or "
        "id > 15 or id > 16 or id > 17 or id > 18 or id > 19 or id > 20 or id > 21 or id > 22 or id > 23 or id > 24 or "
        "id > 25 or id > 26 or id > 27 or id > 28 or id > 29 or id > 30 or id > 31 or id < 0 or id < 1 or id < 2 or id "
        "< 3 or id < 4 or id < 5 or id < 6 or id < 7 or id < 8 or id < 9 or id < 10 or id < 11 or id < 12 or id < 13 "
        "or id < 14 or id < 15 or id < 16 or id < 17 or id < 18 or id < 19 or id < 20 or id < 21 or id < 22 or id < 23 "
        "or id < 24 or id < 25 or id < 26 or id < 27 or id < 28 or id < 29 or id < 30 or id < 31 or id != 0 or id != 1 "
        "or id != 2 or id != 3 or id != 4 or id != 5 or id != 6 or id != 7 or id != 8 or id != 9 or id != 10 or id != "
        "11 or id != 12 or id != 13 or id != 14 or id != 15 or id != 16 or id != 17 or id != 18 or id != 19 or id != "
        "20 or id != 21 or id != 22 or id != 23 or id != 24 or id != 25 or id != 26 or id != 27 or id != 28 or id != "
        "29 or id != 30 or id != 31 or id != 32;";
    ret = ExecQuerySqlWithRes(stmt, queryCommand, 1500);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    UnInitConnAndStmt(conn, stmt);
}

void TestIpTypeBase()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);

    Status ret = CreateTableWithIp(stmt, "test");
    EXPECT_EQ(ret, GMERR_OK);
    InsertIpTestData(stmt);
    printf("------------insert done------------\n");

    const char *queryCommand0 = "select id from test group by ip;";
    ret = ExecQuerySql(stmt, queryCommand0);
    EXPECT_EQ(ret, GMERR_OK);
    printf("------------group by with high card done------------\n");

    // 高基数投影列只有count
    const char *queryCommand01 = "select count(ip) from test group by ip;";
    ret = ExecQuerySql(stmt, queryCommand01);
    EXPECT_EQ(ret, GMERR_OK);
    printf("------------group by count with high card done------------\n");

    const char *queryCommand02 = "select count(id), ip from test group by ip;";
    ret = ExecQuerySql(stmt, queryCommand02);
    EXPECT_EQ(ret, GMERR_OK);
    printf("------------group by count with high card 1 done------------\n");

    const char *queryCommand03 = "select count(id) from test group by ip;";
    ret = ExecQuerySql(stmt, queryCommand03);
    EXPECT_EQ(ret, GMERR_OK);
    printf("------------group by count with high card 2 done------------\n");

    const char *queryCommand = "select max(age), sum(salary), ip from test group by ip;";
    ret = ExecQuerySql(stmt, queryCommand);
    EXPECT_EQ(ret, GMERR_OK);
    CheckIpQueryResult(stmt, 8);
    printf("------------group by done------------\n");

    const char *queryCommand1 = "select max(age), sum(salary), ip from test where ip != '10101041' group by ip;";
    ret = ExecQuerySql(stmt, queryCommand1);
    EXPECT_EQ(ret, GMERR_OK);
    CheckIpQueryResult(stmt, 7);
    printf("------------ip base group by with filter done------------\n");

    const char *queryCommandFix1 = "select max(age), sum(salary), ip from test where ip >= '33333333' group by ip;";
    ret = ExecQuerySql(stmt, queryCommandFix1);
    CheckIpQueryResult(stmt, 3);
    printf("------------group by with filter done 1------------\n");

    const char *queryCommandFix2 = "select max(age), sum(salary), ip from test where ip <= '33333333' group by ip;";
    ret = ExecQuerySql(stmt, queryCommandFix2);
    CheckIpQueryResult(stmt, 3);
    printf("------------group by with filter done 2------------\n");

    const char *queryCommandFix3 = "select max(age), sum(salary), ip from test where ip >= '00000000' group by ip;";
    ret = ExecQuerySql(stmt, queryCommandFix3);
    CheckIpQueryResult(stmt, 6);
    printf("------------group by with filter done 3------------\n");

    const char *queryCommandFix4 = "select max(age), sum(salary), ip from test where ip <= '00000000' group by ip;";
    ret = ExecQuerySql(stmt, queryCommandFix4);
    CheckIpQueryResult(stmt, 0);
    printf("------------group by with filter done 4------------\n");

    const char *queryCommandFix5 = "select max(age), sum(salary), ip from test where ip >= '0' group by ip;";
    ret = ExecQuerySql(stmt, queryCommandFix5);
    CheckIpQueryResult(stmt, 0);
    printf("------------group by with filter done 5------------\n");

    const char *queryCommand2 =
        "select max(age), sum(salary), ip from test where ip != '10101043' group by ip order by ip limit 3;";
    ret = ExecQuerySql(stmt, queryCommand2);
    CheckIpQueryResult(stmt, 3);
    printf("------------group by with filter and sort limit done------------\n");

    const char *queryCommand3 = "select max(age), sum(salary), ip from test where ip = "
                                "'abcdabcdabcdabcdabcdabcdabcdabcd' group by ip;";
    ret = ExecQuerySql(stmt, queryCommand3);
    CheckIpQueryResult(stmt, 1);
    printf("------------group by with filter ipv6 done------------\n");

    const char *queryCommand4 = "select max(age), sum(salary), ip from test where ip >= 'FFFFFFFF' group by ip;";
    ret = ExecQuerySql(stmt, queryCommand4);
    CheckIpQueryResult(stmt, 1);
    printf("------------group by with filter ipv4 >= done------------\n");

    const char *queryCommand5 =
        "select max(age), sum(salary), ip from test where ip >= '10101041' and ip <= 'FFFFFFFF' group by ip;";
    ret = ExecQuerySql(stmt, queryCommand5);
    CheckIpQueryResult(stmt, 4);
    printf("------------group by with filter ipv4 done------------\n");

    const char *queryCommand6 =
        "select max(age), sum(salary), ip from test where ip >= '1234567812345678abcdabcdabcdabcd' and ip <= "
        "'abcdabcdabcdabcdabcdabcdabcdabcd' group by ip;";
    ret = ExecQuerySql(stmt, queryCommand6);
    CheckIpQueryResult(stmt, 2);
    printf("------------group by with filter ipv6 done------------\n");

    const char *queryCommand7 = "select max(age), sum(salary), ip from test where ip >= '00000000' and ip <= "
                                "'abcdabcdabcdabcdabcdabcdabcdabcd' group by ip;";
    ret = ExecQuerySql(stmt, queryCommand7);
    CheckIpQueryResult(stmt, 0);
    printf("------------group by with filter ipv6 <= done------------\n");

    const char *queryCommand71 = "select max(age), sum(salary), ip from test where ip <= "
                                 "'abcdabcdabcdabcdabcdabcdabcdabcd' and ip >= '00000000' group by ip;";
    ret = ExecQuerySql(stmt, queryCommand71);
    CheckIpQueryResult(stmt, 0);
    printf("------------group by with filter ipv6 <= done 1------------\n");

    const char *queryCommand8 = "select max(age), sum(salary), ip from test group by ip;";
    ret = ExecQuerySql(stmt, queryCommand8);
    CheckIpQueryResult(stmt, 8);
    printf("------------group by with filter done------------\n");

    const char *queryCommand12 = "select max(age), last(ip), ip from test group by ip;";
    ret = ExecQuerySql(stmt, queryCommand12);
    CheckIpQueryResultWithFunc(stmt, 8);
    printf("------------group by with last(ip) done------------\n");

    const char *queryCommand13 = "select id, ip, ip from test where ip like '*';";
    ret = ExecQuerySql(stmt, queryCommand13);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    printf("------------like done------------\n");

    const char *queryCommand14 = "select id, first(ip), last(ip) from test group by ip, id;";
    ret = ExecQuerySql(stmt, queryCommand14);
    CheckIpQueryResultWithFunc(stmt, 11);
    printf("------------group by with last(ip) done------------\n");

    const char *queryCommand15 = "select id, last(ip), ip from test group by id;";
    ret = ExecQuerySql(stmt, queryCommand15);
    CheckIpQueryResultWithFunc(stmt, 6);
    printf("------------group by id done------------\n");

    char ddlCommand[200] =
        "create table test1(age integer, id integer, worktime integer, salary integer, ip inet) "
        "with (time_col = 'ip', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    ret = GmcExecDirect(stmt, ddlCommand, 256);
    EXPECT_EQ(ret, GMERR_INVALID_PARAMETER_VALUE);

    // fixed insert int
    ret = InsertIpTestDataWithInt(stmt);
    EXPECT_EQ(ret, GMERR_INVALID_PARAMETER_VALUE);
    printf("------------ip use int done------------\n");

    DropTable(stmt, "test");
    printf("------------delete test done------------\n");

    UnInitConnAndStmt(conn, stmt);
}

void TestIpBigData()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);

    Status ret = CreateTableWithIp(stmt, "test");
    EXPECT_EQ(ret, GMERR_OK);

    InsertIpTestBigData(stmt);
    printf("------------insert done------------\n");

    const char *queryCommand0 = "select count(age), id, ip from test group by ip;";
    ret = ExecQuerySql(stmt, queryCommand0);
    CheckIpQueryResult(stmt, 8);
    printf("------------ip big data group by with filter done------------\n");

    const char *queryCommand1 = "select count(age), id, ip from test where ip < 'FFFFFFFF' group by ip, id;";
    ret = ExecQuerySql(stmt, queryCommand1);
    CheckIpQueryResult(stmt, 7);
    printf("------------group by with filter 1 done------------\n");

    const char *queryCommand2 = "select count(age), id, ip from test where ip < 'GFFFFFFF' group by ip, id;";
    ret = ExecQuerySql(stmt, queryCommand2);
    CheckIpQueryResult(stmt, 0);
    printf("------------group by with filter GFFFFFFF done------------\n");

    const char *queryCommand3 = "select count(age), id, ip from test where ip < 'FFFFFFF' group by ip, id;";
    ret = ExecQuerySql(stmt, queryCommand3);
    CheckIpQueryResult(stmt, 0);
    printf("------------group by with filter FFFFFFF done------------\n");

    const char *queryCommand4 = "select count(age), id, ip from test where ip < '' group by ip, id;";
    ret = ExecQuerySql(stmt, queryCommand4);
    CheckIpQueryResult(stmt, 0);
    printf("------------group by with filter empty done------------\n");

    const char *queryCommand5 = "select count(age), id, ip from test group by ip order by ip;";
    ret = ExecQuerySql(stmt, queryCommand5);
    CheckIpQueryResult(stmt, 8);
    printf("------------group by with sort done------------\n");

    DropTable(stmt, "test");
    UnInitConnAndStmt(conn, stmt);
}

void TestIpBigDataIpv4()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);

    Status ret = CreateTableWithIp(stmt, "test");
    EXPECT_EQ(ret, GMERR_OK);

    InsertIpTestBigDataOnlyIpv4(stmt);
    printf("------------insert done------------\n");

    const char *queryCommand0 = "select count(age), id, ip from test group by ip;";
    ret = ExecQuerySql(stmt, queryCommand0);
    CheckIpQueryResult(stmt, 8);
    printf("------------group by with filter done------------\n");

    const char *queryCommand1 = "select count(age), id, ip from test where ip < 'FFFFFFFF' group by ip, id;";
    ret = ExecQuerySql(stmt, queryCommand1);
    CheckIpQueryResult(stmt, 8);
    printf("------------group by with filter 1 done------------\n");

    DropTable(stmt, "test");
    UnInitConnAndStmt(conn, stmt);
}

void TestIpBigDataIpv6()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);

    Status ret = CreateTableWithIp(stmt, "test");
    EXPECT_EQ(ret, GMERR_OK);

    ret = CreateTableWithIp(stmt, "test1");
    EXPECT_EQ(ret, GMERR_OK);

    InsertIpTestBigDataOnlyIpv6(stmt, "test");
    printf("------------insert done------------\n");

    // Insert into select
    char commandInsert[100] = "insert into test1 select * from test where id = 1 group by ip;";
    ret = GmcExecDirect(stmt, commandInsert, 100);
    EXPECT_EQ(ret, GMERR_OK);
    const char *queryCommand = "select count(age), id, ip from test1 group by ip;";
    ret = ExecQuerySql(stmt, queryCommand);
    CheckIpQueryResult(stmt, 1);
    printf("------------select count done------------\n");

    const char *queryCommand0 = "select count(age), id, ip from test group by ip;";
    ret = ExecQuerySql(stmt, queryCommand0);
    CheckIpQueryResult(stmt, 7);
    printf("------------group by with filter done------------\n");

    const char *queryCommand1 =
        "select count(age), id, ip from test where ip = 'F0F0F0F0F0F0F0F0F0F0F0F0F0F0F0F0' group by ip;";
    ret = ExecQuerySql(stmt, queryCommand1);
    CheckIpQueryResult(stmt, 1);
    printf("------------group by with filter 1 done------------\n");

    const char *queryCommand2 =
        "select count(age), id, ip from test where ip < 'F0F0F0F0F0F0F0F0F0F0F0F0F0F0F0F0' group by ip;";
    ret = ExecQuerySql(stmt, queryCommand2);
    CheckIpQueryResult(stmt, 5);
    printf("------------group by with filter 2 done------------\n");

    const char *queryCommand3 = "select count(age), id, ip from test where ip < 'FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF' and "
                                "ip >= '0000FFFF' group by ip;";
    ret = ExecQuerySql(stmt, queryCommand3);
    CheckIpQueryResult(stmt, 0);
    printf("------------group by with filter 3 done------------\n");

    const char *queryCommand4 = "select count(age), id, ip from test where ip >= '0000FFFF' and ip < "
                                "'FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF' group by ip;";
    ret = ExecQuerySql(stmt, queryCommand4);
    CheckIpQueryResult(stmt, 0);
    printf("------------group by with filter 4 done------------\n");

    const char *queryCommand5 =
        "select count(age), id, ip from test where ip != 'F0F0F0F0F0F0F0F0F0F0F0F0F0F0F0F0' group by ip;";
    ret = ExecQuerySql(stmt, queryCommand5);
    CheckIpQueryResult(stmt, 6);
    printf("------------group by with filter 5 done------------\n");

    DropTable(stmt, "test");
    UnInitConnAndStmt(conn, stmt);
}

void TestSensitive()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    // 正常场景
    char *createSql1 = "create table test1(age integer, id integer, name char(256), salary integer, ip inet) "
                       "with (time_col = 'id', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 "
                       "hours', sensitive_col = 'age, id, ip');";
    ret = GmcExecDirect(stmt, createSql1, 256);
    EXPECT_EQ(ret, GMERR_OK);
    DropTable(stmt, "test1");

    // 包含空格
    char *createSql10 = "create table test1(age integer, id integer, name char(256), salary integer, ip inet) "
                        "with (time_col = 'id', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 "
                        "hours', sensitive_col = 'age,          id,         ip');";
    ret = GmcExecDirect(stmt, createSql10, 256);
    EXPECT_EQ(ret, GMERR_OK);
    DropTable(stmt, "test1");

    // 列重复
    char *createSql2 = "create table test2(age integer, id integer, name char(256), salary integer, ip inet) "
                       "with (time_col = 'id', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 "
                       "hours', sensitive_col = 'age, ip, ip');";
    ret = GmcExecDirect(stmt, createSql2, 256);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    // 列不存在
    char *createSql3 = "create table test3(age integer, id integer, name char(256), salary integer, ip inet) "
                       "with (time_col = 'id', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 "
                       "hours', sensitive_col = 'age, worktime, ip');";
    ret = GmcExecDirect(stmt, createSql3, 256);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    // 选项重复
    char *createSql4 = "create table test4(age integer, id integer, name char(256), salary integer, ip inet) "
                       "with (time_col = 'id', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 "
                       "hours', sensitive_col = 'ip', sensitive_col = 'id');";
    ret = GmcExecDirect(stmt, createSql4, 256);
    EXPECT_EQ(ret, GMERR_UNIQUE_VIOLATION);

    printf("--------------empty col---------------\n");
    // 列为空
    char *createSql5 = "create table test4(age integer, id integer, name char(256), salary integer, ip inet) "
                       "with (time_col = 'id', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 "
                       "hours', sensitive_col = 'ip, ,id');";
    ret = GmcExecDirect(stmt, createSql5, 256);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    printf("--------------empty col 1---------------\n");

    char *createSql6 = "create table test4(age integer, id integer, name char(256), salary integer, ip inet) "
                       "with (time_col = 'id', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 "
                       "hours', sensitive_col = ', ,id');";
    ret = GmcExecDirect(stmt, createSql6, 256);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    printf("--------------empty col 2---------------\n");

    char *createSql7 = "create table test4(age integer, id integer, name char(256), salary integer, ip inet) "
                       "with (time_col = 'id', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 "
                       "hours', sensitive_col = 'ip, id, ');";
    ret = GmcExecDirect(stmt, createSql7, 256);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    printf("--------------empty col 3---------------\n");

    char *createSql8 = "create table test4(age integer, id integer, name char(256), salary integer, ip inet) "
                       "with (time_col = 'id', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 "
                       "hours', sensitive_col = '');";
    ret = GmcExecDirect(stmt, createSql8, 256);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    printf("--------------empty col 4---------------\n");

    char *createSql9 = "create table test4(age integer, id integer, name char(256), salary integer, ip inet) "
                       "with (time_col = 'id', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 "
                       "hours', sensitive_col = ',');";
    ret = GmcExecDirect(stmt, createSql9, 256);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    UnInitConnAndStmt(conn, stmt);
}

void TestQueryWithMultiExpr()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;

    InitConnAndStmt(&conn, &stmt);

    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, 34, 19, 23, 45, 34};
    int64_t ids[] = {19999, 19999, 2, 2, 2, 2, 2, 2, 3333333, 3333333};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000, 4000, 30000, 31000, 10000};
    uint32_t rowNum = 10;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery4");

    const char *queryCommand0 = "select max(age), sum(salary), id from tablequery4 group by id;";
    Status ret = ExecQuerySql(stmt, queryCommand0);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t propSize1 = 0;
    bool eof1 = false;
    bool isNull1 = false;
    int64_t age1 = 0;

    eof1 = false;
    while (!eof1) {
        ret = GmcFetch(stmt, &eof1);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof1 || ret != GMERR_OK) {
            break;
        }
        propSize1 = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &age1, &propSize1, &isNull1);
        EXPECT_EQ(ret, GMERR_OK);
        printf("age:%ld\n", age1);
    }

    const char *queryCommand = "select salary, age from tablequery4 where salary + age > 30000;";
    // expect result: 30000, 23
    //                31000, 45
    //                30000, 23
    //                31000, 45
    ret = ExecQuerySql(stmt, queryCommand);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;
    bool isNull = false;
    int64_t salary = 0;
    int64_t age = 0;

    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &salary, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &age, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        switch (i) {
            case 0: {
                EXPECT_EQ(salary, 30000);
                EXPECT_EQ(age, 23);
                break;
            }
            case 1: {
                EXPECT_EQ(salary, 31000);
                EXPECT_EQ(age, 45);
                break;
            }
            case 2: {
                EXPECT_EQ(salary, 30000);
                EXPECT_EQ(age, 23);
                break;
            }
            case 3: {
                EXPECT_EQ(salary, 31000);
                EXPECT_EQ(age, 45);
                break;
            }
            default:
                EXPECT_EQ(1, 0);
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)4);

    UnInitConnAndStmt(conn, stmt);
}

void TestQueryWithMultiExprNegative()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;

    InitConnAndStmt(&conn, &stmt);

    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, 34, 19, 23, 45, 34};
    int64_t ids[] = {1, 1, 2, 2, 3, 2, 2, 2, 3, 2};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000, 4000, 30000, 31000, 10000};
    uint32_t rowNum = 10;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery4");

    const char *queryCommand = "select salary, age from tablequery4 where age - salary > -100000;";
    Status ret = ExecQuerySql(stmt, queryCommand);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t i = 0;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)10);

    const char *queryCommand1 = "select salary, age from tablequery4 where age - salary > +100000;";
    ret = ExecQuerySql(stmt, queryCommand1);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t j = 0;
    bool eof1 = false;
    while (!eof1) {
        ret = GmcFetch(stmt, &eof1);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }
        j++;
    }
    EXPECT_EQ(j, (uint32_t)0);

    UnInitConnAndStmt(conn, stmt);
}

uint32_t QueryResult4ArithExpr(const char *sql, GmcStmtT *stmt)
{
    uint32_t resCnt = 0;
    Status ret = GMERR_OK;
    ret = ExecQuerySql(stmt, sql);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t propSize0 = sizeof(int64_t);
    bool eof0 = false;
    bool isNull0 = false;
    int64_t age0 = 0;
    int64_t salary0 = 0;

    printf("sql:%s\n", sql);

    while (!eof0) {
        ret = GmcFetch(stmt, &eof0);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof0) {
            break;
        }
        resCnt++;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &age0, &propSize0, &isNull0);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &salary0, &propSize0, &isNull0);
        EXPECT_EQ(ret, GMERR_OK);
        printf("cnt: %d, age:%ld, salary:%ld\n", resCnt, age0, salary0);
    }
    return resCnt;
}

void TestQueryWithMultiExprComplex()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, 34, 19, 29, 45, 34};
    int64_t ids[] = {1, 1, 2, 2, 3, 2, 2, 2, 3, 2};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000, 4000, 30000, 31000, 10000};
    uint32_t rowNum = 10;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery4");

    // 30000 23
    // 30000 23
    const char *queryCommand1 = "select salary, age from tablequery4 where (salary + age) > 30000 and age < 30;";
    uint32_t resCnt1 = QueryResult4ArithExpr(queryCommand1, stmt);
    EXPECT_EQ(resCnt1, 2);

    // 30000 23
    const char *queryCommand2 =
        "select salary, age from tablequery4 where (salary + age) >= 30000 and (id + age) <= 30;";
    uint32_t resCnt2 = QueryResult4ArithExpr(queryCommand2, stmt);
    EXPECT_EQ(resCnt2, 1);

    // 7个
    const char *command3 = "select salary, age from tablequery4 where (worktime + age) <= 50 or (id + age) <= 30;";
    uint32_t resCnt3 = QueryResult4ArithExpr(command3, stmt);
    EXPECT_EQ(resCnt3, 7);

    // 1, 29
    // 2, 34
    // 2, 34
    const char *queryCommand4 = "select id, age from tablequery4 where (worktime + age) == 45 or (id + age) == 30;";
    uint32_t resCnt4 = QueryResult4ArithExpr(queryCommand4, stmt);
    EXPECT_EQ(resCnt4, 3);

    const char *queryCommand5 = "select id, age from tablequery4 where (id + age) != 30;";
    uint32_t resCnt5 = QueryResult4ArithExpr(queryCommand5, stmt);
    EXPECT_EQ(resCnt5, 9);

    // 1, 30
    // 2, 97
    // 3, 90
    const char *queryCommand6 = "select id, sum(age) from tablequery4 where (id + age) >= 31 group by id;";
    uint32_t resCnt6 = QueryResult4ArithExpr(queryCommand6, stmt);
    EXPECT_EQ(resCnt6, 3);

    // 相同列运算
    const char *queryCommand7 = "select id, sum(age) from tablequery4 where (id + id) >= 5 group by id;";
    uint32_t resCnt7 = QueryResult4ArithExpr(queryCommand7, stmt);
    EXPECT_EQ(resCnt7, 1);

    printf("-----------------语法校验------------------\n");
    // // 只支持a + b > 3格式
    const char *verify1 = "select id, sum(age) from tablequery4 where 3 <= id + id group by id;";
    ret = ExecQuerySql(stmt, verify1);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    const char *verify2 = "select id, sum(age) from tablequery4 where 3 + age <= id group by id;";
    ret = ExecQuerySql(stmt, verify2);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    const char *verify3 = "select id, sum(age) from tablequery4 where age + 3 > id group by id;";
    ret = ExecQuerySql(stmt, verify3);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    const char *verify4 = "select id, sum(age) from tablequery4 where age + id > id group by id;";
    ret = ExecQuerySql(stmt, verify4);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    const char *verify5 = "select id, sum(age) from tablequery4 where 1 + 1 > id group by id;";
    ret = ExecQuerySql(stmt, verify5);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    const char *verify6 = "select id, sum(age) from tablequery4 where 1 + 2 > 2 group by id;";
    ret = ExecQuerySql(stmt, verify6);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    const char *verify7 = "select id, sum(age) from tablequery4 where age + 2 > 2 group by id;";
    ret = ExecQuerySql(stmt, verify7);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    // 只能int64
    const char *verify8 = "select id, sum(age) from tablequery4 where age + name > 2 group by id;";
    ret = ExecQuerySql(stmt, verify8);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    const char *verify9 = "select id, sum(age) from tablequery4 where age + age > '2' group by id;";
    ret = ExecQuerySql(stmt, verify9);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    const char *verify10 = "select id, sum(age) from tablequery4 where age + age > 1 + age group by id;";
    ret = ExecQuerySql(stmt, verify10);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    UnInitConnAndStmt(conn, stmt);
}

void TestFirstLastAggregate()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, 34};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint32_t rowNum = 6;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery4");

    const char *queryCommandA = "select worktime, first(salary), last(salary), first(name) from "
                                "tablequery4 group by worktime order by worktime;";
    // expact result: 11          4000          10000
    //                12          31000         31000
    //                24          20000         10000
    ret = ExecQuerySql(stmt, queryCommandA);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    int64_t firstSalaryRes = 0;
    int64_t lastSalaryRes = 0;
    bool isNull = false;
    char nameRes[256] = {0};

    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &firstSalaryRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &lastSalaryRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        switch (i) {
            case 0: {
                EXPECT_EQ(worktimeRes, 11);
                EXPECT_EQ(firstSalaryRes, 4000);
                EXPECT_EQ(lastSalaryRes, 10000);
                EXPECT_EQ(DbStrCmp(nameRes, "bob", false), 0);
                break;
            }
            case 1: {
                EXPECT_EQ(worktimeRes, 12);
                EXPECT_EQ(firstSalaryRes, 31000);
                EXPECT_EQ(lastSalaryRes, 31000);
                EXPECT_EQ(DbStrCmp(nameRes, "tim", false), 0);
                break;
            }
            case 2: {
                EXPECT_EQ(worktimeRes, 24);
                EXPECT_EQ(firstSalaryRes, 20000);
                EXPECT_EQ(lastSalaryRes, 10000);
                EXPECT_EQ(DbStrCmp(nameRes, "david", false), 0);
                break;
            }
            default:
                EXPECT_EQ(1, 0);
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    UnInitConnAndStmt(conn, stmt);
}

typedef struct {
    char (*src_ip)[33];
    int64_t *vsys_id;
    int64_t *event_num;
    int64_t *log_time;
    uint32_t rowNum;
} IpData1;

// 修复高基数场景下，只有ipv4数据时cuVector数据类型错误问题
void TestIpTypeFix()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);

    Status ret = GMERR_OK;
    const char *ddlCommand =
        "CREATE TABLE IF NOT EXISTS t_maflog_src_ip_5m (vsys_id INTEGER, src_ip INET, event_num INTEGER, "
        "log_time INTEGER) WITH (time_col = 'log_time', interval = '1 hour', ttl = '8 hour')";
    ret = GmcExecDirect(stmt, ddlCommand, 256);
    EXPECT_EQ(ret, GMERR_OK);

    char ips[][33] = {"1b840164", "1b840164", "1b840164", "1b840164", "1b840164", "1b840164"};
    int64_t vsys_id[] = {29, 30, 19, 23, 45, 34};
    int64_t event_num[] = {1, 1, 1, 1, 1, 1};
    int64_t log_time[] = {24, 18, 25, 11, 12, 10};
    uint32_t rowNum = 6;

    IpData1 data = {.src_ip = ips, .vsys_id = vsys_id, .event_num = event_num, .log_time = log_time, .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "t_maflog_src_ip_5m", GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, data.vsys_id, sizeof(data.vsys_id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_FIXED, data.src_ip, 33, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.event_num, sizeof(data.event_num[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.log_time, sizeof(data.log_time[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    printf("------------insert done------------\n");

    const char *querySql =
        "select src_ip, SUM(event_num) FROM t_maflog_src_ip_5m WHERE vsys_id >= 1 GROUP BY src_ip ORDER "
        "BY SUM(event_num) DESC limit 10;";

    ret = ExecQuerySql(stmt, querySql);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t propSize = 33;
    bool eof = false;
    bool isNull = false;
    char res[33] = {0};
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &res, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_STREQ("1b840164", res);
    }

    DropTable(stmt, "t_maflog_src_ip_5m");
    printf("------------delete test done------------\n");

    UnInitConnAndStmt(conn, stmt);
}

typedef struct {
    int64_t *vsys_id;
    int64_t *app_id;
    int64_t *user_id;
    int64_t *attacker_type;
    int64_t *vctmer_type;
    int64_t *threat_type;
    int64_t *thrt_id;
    int64_t *thrtname_id;
    int64_t *event_num;
    char (*attacker)[33];
    char (*vctmer)[33];
    uint32_t rowNum;
} IpData2;

// 修复高基数场景下，ip列不为聚合列时投影列CuVector数据类型错误问题
void TestIpTypeFix2()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);

    Status ret = GMERR_OK;
    const char *ddlCommand =
        "CREATE TABLE t_thrtlog_mrg_20m(vsys_id INTEGER, app_id INTEGER, user_id INTEGER, attacker_type "
        "INTEGER, vctmer_type INTEGER, threat_type INTEGER, thrt_id INTEGER, thrtname_id INTEGER, event_num  "
        "INTEGER, attacker INET, vctmer INET) WITH (time_col = 'vsys_id', interval = '1 hour',    "
        "ttl = '72 hour')";
    ret = GmcExecDirect(stmt, ddlCommand, 1024);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t vsys_id[] = {29, 30, 19, 23, 45, 34};
    int64_t app_id[] = {1, 1, 1, 1, 1, 1};
    int64_t user_id[] = {24, 18, 25, 11, 12, 10};
    int64_t attacker_type[] = {29, 30, 19, 23, 45, 34};
    int64_t vctmer_type[] = {1, 1, 1, 1, 1, 1};
    int64_t threat_type[] = {2, 2, 2, 2, 2, 2};
    int64_t thrt_id[] = {1, 1, 1, 1, 1, 1};
    int64_t thrtname_id[] = {24, 18, 25, 11, 12, 10};
    int64_t event_num[] = {24, 18, 25, 11, 12, 10};
    char attacker[][33] = {"1b840164", "1b840164", "1b840164", "1b840164", "1b840164", "1b840164"};
    char vctmer[][33] = {"ac192d01", "ac192d01", "ac192d01", "ac192d01", "ac192d01", "ac192d01"};
    uint32_t rowNum = 6;

    IpData2 data = {.vsys_id = vsys_id,
        .app_id = app_id,
        .user_id = user_id,
        .attacker_type = attacker_type,
        .vctmer_type = vctmer_type,
        .threat_type = threat_type,
        .thrt_id = thrt_id,
        .thrtname_id = thrtname_id,
        .event_num = event_num,
        .attacker = attacker,
        .vctmer = vctmer,
        .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "t_thrtlog_mrg_20m", GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, data.vsys_id, sizeof(data.vsys_id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.app_id, sizeof(data.app_id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.user_id, sizeof(data.user_id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.attacker_type, sizeof(data.attacker_type[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.vctmer_type, sizeof(data.vctmer_type[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_INT64, data.threat_type, sizeof(data.threat_type[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, data.thrt_id, sizeof(data.thrt_id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, data.thrtname_id, sizeof(data.thrtname_id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 8, (GmcDataTypeE)DB_DATATYPE_INT64, data.event_num, sizeof(data.event_num[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 9, (GmcDataTypeE)DB_DATATYPE_FIXED, data.attacker, 33, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 10, (GmcDataTypeE)DB_DATATYPE_FIXED, data.vctmer, 33, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    printf("------------insert done------------\n");

    const char *querySql = "SELECT threat_type, SUM(event_num) FROM t_thrtlog_mrg_20m GROUP BY threat_type;";
    ret = ExecQuerySql(stmt, querySql);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t res = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    bool isNull = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &res, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(2, res);
    }

    DropTable(stmt, "t_thrtlog_mrg_20m");
    printf("------------delete test done------------\n");

    UnInitConnAndStmt(conn, stmt);
}

// 聚合查询select没有聚合函数，groupby多个字段
TEST_F(StTsQueryGmc, QueryOpSimple4_0)
{
    TestGroupByWithoutAgg();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestGroupByWithoutAgg();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

// 聚合查询select没有聚合函数，groupby单个字段
TEST_F(StTsQueryGmc, QueryOpSimple4_1)
{
    TestGroupByWithoutAgg1();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestGroupByWithoutAgg1();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

// colset基础测试
TEST_F(StTsQueryGmc, QueryOpSimple4_colset)
{
    TestColSetAgg();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestColSetAgg();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

TEST_F(StTsQueryGmc, QueryOpSimple4_colset_multi)
{
    TestColSetAgg1();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestColSetAgg1();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

// colset大数据量测试
TEST_F(StTsQueryGmc, QueryOpSimple4_colset_bigData)
{
    TestColSetAggBigData();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestColSetAggBigData();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

// colset最大长度测试
TEST_F(StTsQueryGmc, QueryOpSimple4_colset_bigData_maxLen)
{
    TestColSetAggBigDataMaxLen();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestColSetAggBigDataMaxLen();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

// colset大数据量，负数测试
TEST_F(StTsQueryGmc, QueryOpSimple4_colset_bigDataNegative)
{
    TestColSetAggBigDataNegative();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestColSetAggBigDataNegative();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

// 范围查询支持算术表达式
TEST_F(StTsQueryGmc, QueryOpSimple4_filterExpr)
{
    TestQueryWithMultiExpr();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestQueryWithMultiExpr();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

TEST_F(StTsQueryGmc, QueryOpSimple4_filterExpr_negative)
{
    TestQueryWithMultiExprNegative();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestQueryWithMultiExprNegative();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

// 范围查询支持算术表达式
TEST_F(StTsQueryGmc, QueryOpSimple4_filterExpr_complex)
{
    TestQueryWithMultiExprComplex();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestQueryWithMultiExprComplex();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

TEST_F(StTsQueryGmc, QueryOpSimple4_filterExpr_bigdata)
{
    TestArithExprBigData();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestArithExprBigData();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

TEST_F(StTsQueryGmc, QueryOpSimple4_sortExpr_bigdata)
{
    TestTopNSortBigData();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

TEST_F(StTsQueryGmc, QueryOpSimple4_where_limit)
{
    TestWhereLimit();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestWhereLimit();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

TEST_F(StTsQueryGmc, QueryOpSimple4_ip_base)
{
    TestIpTypeBase();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestIpTypeBase();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

TEST_F(StTsQueryGmc, QueryOpSimple4_ip_fix)
{
    TestIpTypeFix();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestIpTypeFix();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

TEST_F(StTsQueryGmc, QueryOpSimple4_ip_fix2)
{
    TestIpTypeFix2();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestIpTypeFix2();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

TEST_F(StTsQueryGmc, QueryOpSimple4_ip_compression)
{
    TestIpTypeCompression();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestIpTypeCompression();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}
TEST_F(StTsQueryGmc, QueryOpSimple4_ip_bigData)
{
    TestIpBigData();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestIpBigData();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

TEST_F(StTsQueryGmc, QueryOpSimple4_ip_bigData_v4)
{
    TestIpBigDataIpv4();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestIpBigDataIpv4();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

TEST_F(StTsQueryGmc, QueryOpSimple4_ip_bigData_v6)
{
    TestIpBigDataIpv6();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

TEST_F(StTsQueryGmc, DISABLED_QueryOpSimple4_sensitive)
{
    TestSensitive();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

TEST_F(StTsQueryGmc, QueryOpSimple4_createFix)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    char *createSql1 = "CREATE TABLE tableA (id integer, category char(8), value integer, temp1 integer) with "
                       "(time_col = 'id',  interval= '1 hour');";
    ret = GmcExecDirect(stmt, createSql1, 256);
    EXPECT_EQ(ret, GMERR_OK);

    char *createSql2 = "CREATE TABLE tableB (id integer, category char(8), value integer, temp1 integer) with "
                       "(time_col = 'id',  interval= '1 hour');";
    ret = GmcExecDirect(stmt, createSql2, 256);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t rowNum = 3;
    int64_t id[] = {1, 2, 4};
    char category[][6] = {"David", "Lucy", "Lin"};
    int64_t value[] = {1, 2, 4};
    int64_t temp1[] = {1, 2, 4};

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tableB", GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_FIXED, category, sizeof(char) * 6, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, value, sizeof(value[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, temp1, sizeof(temp1[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    char *querySql = "INSERT INTO tableA (id , category, value, temp1)  SELECT id, 'a', 1, 2 FROM tableB;";
    ret = GmcExecDirect(stmt, querySql, 256);
    EXPECT_EQ(ret, GMERR_OK);

    printf("--------------------query----------------------\n");

    system("gmsysview -sql \"select * from tableA\";");

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpSimple4_ip_alter)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);

    char command[200] = "create table TABLEBULKALTER(id integer, name char(6)) with (time_col = 'id', "
                        "interval = '1 day', compression = 'fast(rapidlz)', ttl = '3 days');";
    char altercmd1[200] = "alter table tablebulkalter add column ip inet;";
    char queryCommand[200] = "select * from tablebulkalter group by ip;";

    uint32_t rowNum = 3;
    int64_t id[] = {1, 2, 4};
    char name[][6] = {"David", "Lucy", "Lin"};
    char ip[][33] = {"10101010", "abcdabcdabcdabcdabcdabcdabcdabcd", "ffffFFFF"};
    char tableName[40] = "TABLEBULKALTER";

    // create
    Status ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // insert
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_FIXED, name, sizeof(char) * 6, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    // alter
    ret = GmcExecDirect(stmt, altercmd1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // bulk insert first time
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_FIXED, name, sizeof(char) * 6, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, ip, 33, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    // query
    ret = GmcExecDirect(stmt, queryCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t i = 0;
    int64_t idRes = 0;
    char nameRes[6] = {0};
    char ips[33] = {0};
    bool eof = false;
    bool isNull = false;
    uint32_t propSize = 0;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(char) * 6;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = 33;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &ips, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        printf("id:%ld, name:%s, ip:%s\n", idRes, nameRes, ips);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)4);

    char dropCommand[60] = "drop table tablebulkalter;";
    ret = GmcExecDirect(stmt, dropCommand, 60);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpSimple4)
{
    TestFirstLastAggregate();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestFirstLastAggregate();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

TEST_F(StTsQueryGmc, QueryOpSimple5)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    uint32_t propSize = 0;
    bool eof = false;

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    char names[][256] = {"bob", "nut1234567890123456789012345678901234567890",
        "nut1234567890123456789012345678901234567890", "olivia", "tim", "tim"};
    int64_t ages[] = {29, 30, 19, 23, 45, 34};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint32_t rowNum = 6;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery5");

    const char *queryCommand = "select name, id, salary from tablequery5 where name = 'olivia' OR "
                               "name = 'nut1234567890123456789012345678901234567890' order by salary;";
    // expact result: nut1234567890123456789012345678901234567890          4          4000
    //                nut1234567890123456789012345678901234567890          2          10000
    //                olivia                                               9          30000
    ret = ExecQuerySql(stmt, queryCommand);
    EXPECT_EQ(ret, GMERR_OK);

    char nameRes[256] = {0};
    int64_t idRes = 0;
    int64_t salaryRes = 0;
    bool isNull = false;

    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &salaryRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        switch (i) {
            case 0: {
                EXPECT_EQ(DbStrCmp(nameRes, "nut1234567890123456789012345678901234567890", false), 0);
                EXPECT_EQ(idRes, 4);
                EXPECT_EQ(salaryRes, 4000);
                break;
            }
            case 1: {
                EXPECT_EQ(DbStrCmp(nameRes, "nut1234567890123456789012345678901234567890", false), 0);
                EXPECT_EQ(idRes, 2);
                EXPECT_EQ(salaryRes, 10000);
                break;
            }
            case 2: {
                EXPECT_EQ(DbStrCmp(nameRes, "olivia", false), 0);
                EXPECT_EQ(idRes, 9);
                EXPECT_EQ(salaryRes, 30000);
                break;
            }
            default:
                EXPECT_EQ(1, 0);
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    const char *queryCommand1 = "select name, id, salary from tablequery5 where name = '';";
    // expact result:  NULL
    ret = ExecQuerySql(stmt, queryCommand1);
    EXPECT_EQ(ret, GMERR_OK);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(dataCount, 0);

    char dropCommand[60] = "drop table tablequery5;";
    ret = GmcExecDirect(stmt, dropCommand, 60);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpSimple6)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    uint32_t propSize = 0;
    bool eof = false;

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    char names[][256] = {"", "nut1234567890123456789012345678901234567890", "", "olivia", "tim", "tim"};
    int64_t ages[] = {29, 30, 19, 23, 45, 34};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint32_t rowNum = 6;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery6");

    const char *queryCommand = "select name, id, salary from tablequery6 where name = '';";
    // expact result:           1          20000
    //                          4          4000
    ret = ExecQuerySql(stmt, queryCommand);
    EXPECT_EQ(ret, GMERR_OK);

    char nameRes[256] = {0};
    int64_t idRes = 0;
    int64_t salaryRes = 0;
    bool isNull = false;

    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &salaryRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        switch (i) {
            case 0: {
                EXPECT_EQ(DbStrCmp(nameRes, "", false), 0);
                EXPECT_EQ(idRes, 1);
                EXPECT_EQ(salaryRes, 20000);
                break;
            }
            case 1: {
                EXPECT_EQ(DbStrCmp(nameRes, "", false), 0);
                EXPECT_EQ(idRes, 4);
                EXPECT_EQ(salaryRes, 4000);
                break;
            }
            default:
                EXPECT_EQ(1, 0);
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    char dropCommand[60] = "drop table tablequery6;";
    ret = GmcExecDirect(stmt, dropCommand, 60);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryAfterAlterTable)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char command[200] = "create table TABLEBULKALTER(id integer, name char(6)) with (time_col = 'id', "
                        "interval = '1 day', compression = 'fast(rapidlz)', ttl = '3 days');";
    char altercmd1[200] = "alter table tablebulkalter add column salary integer;";
    char altercmd2[200] = "alter table tablebulkalter add column comment char(3);";
    char queryCommand[200] = "select * from tablebulkalter;";
    uint32_t rowNum = 3;
    int64_t id[] = {1, 2, 4};
    char name[][6] = {"David", "Lucy", "Lin"};
    int64_t salary[] = {1000, 2000, 5000};
    char comment[][3] = {"A", "B", "B"};
    char tableName[40] = "TABLEBULKALTER";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_FIXED, name, sizeof(char) * 6, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    // alter first time
    ret = GmcExecDirect(stmt, altercmd1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // bulk insert first time
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_FIXED, name, sizeof(char) * 6, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, queryCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t i = 0;
    int64_t idRes = 0;
    char nameRes[6] = {0};
    int64_t salaryRes = 0;
    char commentRes[3] = {0};
    bool eof = false;
    bool isNull = false;
    uint32_t propSize = 0;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(char) * 6;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &salaryRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        if (i >= 0 && i <= 2) {
            EXPECT_EQ(idRes, id[i]);
            EXPECT_STREQ(nameRes, name[i]);
            EXPECT_EQ(salaryRes, 0);
        } else if (i >= 3 && i <= 5) {
            EXPECT_EQ(idRes, id[i - 3]);
            EXPECT_STREQ(nameRes, name[i - 3]);
            EXPECT_EQ(salaryRes, salary[i - 3]);
        } else {
            EXPECT_EQ(1, 0);
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)6);
    // alter second time
    ret = GmcExecDirect(stmt, altercmd2, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // insert again
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_FIXED, name, sizeof(char) * 6, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, comment, sizeof(char) * 3, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, queryCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(char) * 6;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &salaryRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(char) * 3;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &commentRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        if (i >= 0 && i <= 2) {
            EXPECT_EQ(idRes, id[i]);
            EXPECT_STREQ(nameRes, name[i]);
            EXPECT_EQ(salaryRes, 0);
            EXPECT_STREQ(commentRes, "");
        } else if (i >= 3 && i <= 5) {
            EXPECT_EQ(idRes, id[i - 3]);
            EXPECT_STREQ(nameRes, name[i - 3]);
            EXPECT_EQ(salaryRes, salary[i - 3]);
            EXPECT_STREQ(commentRes, "");
        } else if (i >= 6 && i <= 8) {
            EXPECT_EQ(idRes, id[i - 6]);
            EXPECT_STREQ(nameRes, name[i - 6]);
            EXPECT_EQ(salaryRes, salary[i - 6]);
            EXPECT_STREQ(commentRes, comment[i - 6]);
        } else {
            EXPECT_EQ(1, 0);
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)9);

    char dropCommand[60] = "drop table tablebulkalter;";
    ret = GmcExecDirect(stmt, dropCommand, 60);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpCountStarErr)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] = "create table errquery(age integer, id integer, worktime integer) with"
                           " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',"
                           " ttl = '3 hours');";
    char qryCommand1[60] = "select count(*) from errquery group by age limit 100;";
    uint32_t rowNum = 3;
    int64_t id[] = {1, 2, 4};
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    char tableName[30] = "errquery";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand1, 60);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    char dropCommand[60] = "drop table errquery;";
    ret = GmcExecDirect(stmt, dropCommand, 60);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

static Status BulkInsert(GmcStmtT *stmt, char *tableName, int64_t count, int columnSize, ...)
{
    Status ret;
    va_list varList;
    int64_t *columnArray[columnSize + 1];
    columnArray[0] = nullptr;

    va_start(varList, columnSize);
    for (int i = 0; i < columnSize; i++) {
        columnArray[i] = va_arg(varList, int64_t *);
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    EXPECT_EQ(ret, GMERR_OK);

    for (int n = 0; n < columnSize; n++) {
        ret = GmcBindCol(stmt, n, (GmcDataTypeE)DB_DATATYPE_INT64, columnArray[n], 0, NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    return ret;
}

TEST_F(StTsQueryGmc, QueryOpMassiveDataLimit)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table testdb(id integer, time integer, num integer) with (time_col = 'time', interval= '1 hour');";
    char dropCommand[200] = "drop table testdb;";
    char qryCommand1[60] = "SELECT time FROM testdb order by time ASC limit 1;";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t count = 50000;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t num[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i + 1;
        time[i] = 1695042000 + i;
        num[i] = 50000 - i;
    }
    ret = BulkInsert(stmt, "testdb", count, 3, id, time, num);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 60);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

// 整型不等值测试
TEST_F(StTsQueryGmc, QueryOpNotEqualInteger)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    char ddlCommand[200] = "create table tablequery3(name char(10), age integer, id integer, worktime integer,"
                           " salary integer) with (time_col = 'worktime', interval = '1 hour',"
                           " compression = 'fast(rapidlz)', ttl = '3 hours');";
    char dropCommand[200] = "drop table tablequery3;";
    uint32_t rowNum = 6;
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int32_t age[] = {29, 30, 19, 29, 45, 34};
    int8_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 18, 25, 11, 12, 10};
    uint32_t salary[] = {10000, 20000, 4000, 30000, 31000, 10000};

    char tableName[35] = "tablequery3";
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT8, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT32, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_UINT32, salary, sizeof(salary[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t value = 0;
    char qryCommand1[150] = "select * from tablequery3 where age != 29;";
    ret = GmcExecDirect(stmt, qryCommand1, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);

    char qryCommand3[150] = "select * from tablequery3 where id != 10000;";
    ret = GmcExecDirect(stmt, qryCommand3, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)6);

    char qryCommand4[150] = "select * from tablequery3 where age != '29';";
    ret = GmcExecDirect(stmt, qryCommand4, 150);
    EXPECT_EQ(ret, GMERR_DATATYPE_MISMATCH);

    char qryCommand5[150] = "select * from tablequery3 where worktime != 24;";
    ret = GmcExecDirect(stmt, qryCommand5, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);

    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

// 字符串不等值测试
TEST_F(StTsQueryGmc, QueryOpNotEqualString)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    char ddlCommand[200] = "create table tablequery3(name char(10), age integer, id integer, worktime integer,"
                           " salary integer) with (time_col = 'worktime', interval = '1 hour',"
                           " compression = 'fast(rapidlz)', ttl = '3 hours');";
    char dropCommand[200] = "drop table tablequery3;";
    uint32_t rowNum = 6;
    char name[][10] = {"TIMDavid", "nut", "bob", "olivia", "Tim", "lUcY"};
    int32_t age[] = {29, 30, 19, 23, 45, 34};
    int8_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 18, 25, 11, 12, 10};
    uint32_t salary[] = {10000, 20000, 4000, 30000, 31000, 10000};

    char tableName[35] = "tablequery3";
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT8, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT32, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_UINT32, salary, sizeof(salary[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t value = 0;
    char qryCommand1[150] = "select * from tablequery3 where name != 'nut';";
    ret = GmcExecDirect(stmt, qryCommand1, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ((uint32_t)5, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ((uint32_t)5, value);

    char qryCommand2[150] = "select * from tablequery3 where name != 'abcd';";
    ret = GmcExecDirect(stmt, qryCommand2, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)6);

    char qryCommand3[150] = "select * from tablequery3 where name != '';";
    ret = GmcExecDirect(stmt, qryCommand3, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)6);

    char qryCommand4[150] = "select * from tablequery3 where name < '';";
    ret = GmcExecDirect(stmt, qryCommand4, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ((uint32_t)0, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ((uint32_t)0, value);

    char qryCommand5[150] = "select * from tablequery3 where name < 'TimZ';";
    ret = GmcExecDirect(stmt, qryCommand5, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ((uint32_t)5, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ((uint32_t)2, value);

    char qryCommand6[150] = "select * from tablequery3 where name >= 'oli';";
    ret = GmcExecDirect(stmt, qryCommand6, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ((uint32_t)5, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ((uint32_t)1, value);

    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

// 字符串长度超过min/max索引最大长度
TEST_F(StTsQueryGmc, QueryOpMinMaxNotEqualString)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    char ddlCommand[200] = "create table tablequery3(name char(256), age integer, id integer, worktime integer,"
                           " salary integer) with (time_col = 'worktime', interval = '1 hour',"
                           " compression = 'fast(rapidlz)', ttl = '3 hours');";
    char dropCommand[200] = "drop table tablequery3;";
    uint32_t rowNum = 6;
    char name[][256] = {"yaoyaolingxianyaoyaolingxianyaoyaolingxian", "wenjiem7wenjiem7wenjiem7wenjiem7wenjiem7",
        "ycdnycdnycdnycdnycdnycdnycdnycdnycdn", "shanghaishanghaishanghaishanghaishanghaishanghaishanghaishanghai",
        "wenjiem7wenjiem7wenjiem7wenjiem7wenjiem5", "yaoyaolingxianyaoyaolingxianyaoylingxianlingxian"};
    int32_t age[] = {29, 30, 19, 23, 45, 34};
    int8_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 18, 25, 11, 12, 10};
    uint32_t salary[] = {10000, 20000, 4000, 30000, 31000, 10000};

    char tableName[35] = "tablequery3";
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT8, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT32, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_UINT32, salary, sizeof(salary[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 256, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t value = 0;
    char qryCommand1[150] = "select * from tablequery3 where name < 'yao';";
    ret = GmcExecDirect(stmt, qryCommand1, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ((uint32_t)5, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ((uint32_t)3, value);

    char qryCommand2[150] = "select * from tablequery3 where name >= 'yb';";
    ret = GmcExecDirect(stmt, qryCommand2, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);

    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

// bind column number mismatches with number of properties in table.
TEST_F(StTsQueryGmc, BulkOpt3Query)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char command[300] =
        "create table bulkOpt3(id integer, num integer, name char(6), hobby char(10)) with (time_col = 'id', "
        "interval = '1 day', compression = 'fast(rapidlz)', ttl = '3 days');";
    uint32_t rowNum = 3;
    int64_t id[] = {1, 2, 3};
    char name[][6] = {"David", "Lucy", "Lin"};
    char tableName[40] = "bulkOpt3";
    char qryCommand1[60] = "SELECT * FROM bulkOpt3";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GmcExecDirect(stmt, command, 300);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, name, sizeof(char) * 6, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    // 第一次执行
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    // 第二次执行
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 60);
    EXPECT_EQ(ret, GMERR_OK);

    char hobby[256] = {0};
    int64_t num = 0;
    int64_t id1 = 0;
    bool isNull = false;
    uint32_t propSize = 0;

    uint32_t i = 0;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &hobby, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(strcmp(hobby, ""), 0);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &num, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(num, 0);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id1, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(id1, i + 1);
        i++;
        i = (i == 3) ? 0 : i;
    }

    char dropCommand[60] = "drop table bulkopt3;";
    ret = GmcExecDirect(stmt, dropCommand, 60);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

// like '%' to match "" in logical table
TEST_F(StTsQueryGmc, LikeExprQuery1)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char command[300] =
        "create table like1(id integer, num integer, name char(6), hobby char(10)) with (time_col = 'id', "
        "interval = '1 day', compression = 'fast(rapidlz)', ttl = '3 days');";
    uint32_t rowNum = 3;
    int64_t id[] = {1, 2, 3};
    char name[][6] = {"David", "Lucy", "Lin"};
    char tableName[40] = "like1";
    char qryCommand1[60] = "SELECT * FROM like1 where hobby like '%' order by id";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, command, 300);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, name, sizeof(char) * 6, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 60);
    EXPECT_EQ(ret, GMERR_OK);

    char hobby[256] = {0};
    char nameRes[256] = {0};
    int64_t id1 = 0;
    bool isNull = false;
    uint32_t propSize = 0;

    uint32_t i = 0;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &hobby, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(strcmp(hobby, ""), 0);
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_STREQ(nameRes, name[i]);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id1, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(id1, id[i]);
        i++;
    }
    EXPECT_EQ(i, rowNum);

    char dropCommand[60] = "drop table like1;";
    ret = GmcExecDirect(stmt, dropCommand, 60);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

// like with no escape
TEST_F(StTsQueryGmc, LikeExprQuery2)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char command[300] = "create table like2(id integer, name char(10)) with (time_col = 'id', "
                        "interval = '1 day', compression = 'fast(rapidlz)', ttl = '3 days');";
    uint32_t rowNum = 4;
    uint32_t resultNum = 2;  // 前两个匹配成功
    int64_t id[] = {1, 2, 3, 4};
    char name[][10] = {"Sophia", "Sophra", "Sopha", "SphiiaHap"};
    name[3][9] = 'p';  // 模拟输入不包含结束符, DmCuVariedTypeMatchLike有特殊逻辑处理该情况
    char tableName[40] = "like2";
    char qryCommand1[60] = "SELECT * FROM like2 where name like 'So%ph_a' order by id";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, command, 300);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_FIXED, name, sizeof(char) * 10, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 60);
    EXPECT_EQ(ret, GMERR_OK);

    char nameRes[256] = {0};
    int64_t id1 = 0;
    bool isNull = false;
    uint32_t propSize = 0;

    uint32_t i = 0;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_STREQ(nameRes, name[i]);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id1, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(id1, id[i]);
        i++;
    }
    EXPECT_EQ(i, resultNum);

    // 查询无匹配的模式，结果为空
    char qryCommand2[60] = "SELECT * FROM like2 where name like 'Soph%s' order by id";
    ret = GmcExecDirect(stmt, qryCommand2, 60);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(eof, true);

    char dropCommand[60] = "drop table like2;";
    ret = GmcExecDirect(stmt, dropCommand, 60);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

// like with escape
TEST_F(StTsQueryGmc, LikeExprQuery3)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char command[300] = "create table like3(id integer, name char(10)) with (time_col = 'id', "
                        "interval = '1 day', compression = 'fast(rapidlz)', ttl = '3 days');";
    uint32_t rowNum = 4;
    uint32_t resultNum = 3;  // 前三个匹配成功
    int64_t id[] = {1, 2, 3, 4};
    char name[][10] = {"So#phia", "So_phra", "Sophiia", "So%pha"};
    char tableName[40] = "like3";
    char qryCommand1[150] = "SELECT * FROM like3 where name like 'So##%a' escape '#' or name like 'So@_%a' escape '@' "
                            "or name like 'Soph\\i\\ia' order by id";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, command, 300);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_FIXED, name, sizeof(char) * 10, NULL);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 150);
    EXPECT_EQ(ret, GMERR_OK);

    char nameRes[256] = {0};
    int64_t id1 = 0;
    bool isNull = false;
    uint32_t propSize = 0;

    uint32_t i = 0;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_STREQ(nameRes, name[i]);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id1, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(id1, id[i]);
        i++;
    }
    EXPECT_EQ(i, resultNum);

    // 查询无匹配的模式，结果为空
    char qryCommand2[100] = "SELECT * FROM like3 where name like 'So%ph%a' escape '%' order by id";
    ret = GmcExecDirect(stmt, qryCommand2, 100);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(eof, true);

    char qryCommand3[100] = "SELECT * FROM like3 where name like 'n^^ut' escape '^'";
    ret = GmcExecDirect(stmt, qryCommand3, 100);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(eof, true);

    char dropCommand[60] = "drop table like3;";
    ret = GmcExecDirect(stmt, dropCommand, 60);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

static Status ExecuteDirectWithPrint(GmcStmtT *stmt, const char *command, const char *comment)
{
    cout << comment << endl << command << endl << endl;
    return GmcExecDirect(stmt, command, 150);
}

void TestLengthFunc()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy", "lucy11", "lucy222", "lucy3333", "lucy44444"};
    int64_t ages[] = {1, 1, 29, 30, 19, 23, 45, 34, 34, 34};
    int64_t ids[] = {0, 0, 1, 1, 2, 2, 3, 2, 2, 2};
    int64_t worktimes[] = {0, 0, 24, 24, 11, 11, 12, 11, 11, 11};
    int64_t salaries[] = {0, 0, 20000, 10000, 4000, 30000, 31000, 10000, 10000, 10000};
    uint32_t rowNum = 10;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery4");

    // Length函数放在order by表达式
    const char *queryCommand1 = "select name from "
                                "tablequery4 where length(name) >= 5 order by length(name);";
    cout << "Length函数放在order by表达式. sql: " << queryCommand1 << endl;

    ret = GmcExecDirect(stmt, queryCommand1, 150);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)6);

    bool isNull = false;
    bool eof = false;
    uint32_t i = 0;

    unordered_map<string, bool> expectedRes1{
        {"david", true},
        {"olivia", true},
        {"lucy11", true},
        {"lucy222", true},
        {"lucy3333", true},
        {"lucy44444", true},
    };

    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }

        char nameRes[256] = {0};
        uint32_t propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        printf("select name:%s  propertySize:%u\n", nameRes, propSize);

        EXPECT_NE(expectedRes1.find(nameRes), expectedRes1.end());

        i++;
    }
    EXPECT_EQ(i, (uint32_t)6);

    // Length函数放在Scan算子投影表达式 参数为int类型
    const char *queryCommand2 = "select length(name), age, name from "
                                "tablequery4 where length(age) >= 2;";
    cout << "Length函数放在Scan算子投影表达式 参数为int类型. sql: " << queryCommand2 << endl;

    ret = GmcExecDirect(stmt, queryCommand2, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)8);

    unordered_map<string, int64_t> expectedRes2{
        {"bob", 29},
        {"olivia", 30},
        {"tim", 19},
        {"lucy", 23},
        {"lucy11", 45},
        {"lucy222", 34},
        {"lucy3333", 34},
        {"lucy44444", 34},
    };

    i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }

        int64_t nameLen = 0;
        uint32_t propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameLen, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        int64_t ageRes = 0;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        char nameRes[256] = {0};
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        printf("select name:%s nameLen:%ld ageRes:%ld\n", nameRes, nameLen, ageRes);

        EXPECT_NE(expectedRes2.find(nameRes), expectedRes2.end());
        if (expectedRes2.find(nameRes) != expectedRes2.end()) {
            auto it = expectedRes2.find(nameRes);
            EXPECT_EQ(it->first, nameRes);
            EXPECT_EQ(strlen((it->first).c_str()), nameLen);
            EXPECT_EQ(it->second, ageRes);
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)8);

    // Length函数放在Sort算子投影表达式 length大写
    const char *queryCommand3 = "select length(name), age, name from "
                                "tablequery4 where LENGTH(name) >= 5 order by length(name);";
    cout << "Length函数放在Sort算子投影表达式 length大写. sql: " << queryCommand3 << endl;

    ret = GmcExecDirect(stmt, queryCommand3, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)6);

    vector<pair<string, int64_t>> expectedRes3 = {
        {"david", 1},
        {"olivia", 30},
        {"lucy11", 45},
        {"lucy222", 34},
        {"lucy3333", 34},
        {"lucy44444", 34},
    };

    i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }

        int64_t nameLen = 0;
        uint32_t propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameLen, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        int64_t ageRes = 0;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        char nameRes[256] = {0};
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        printf("select name:%s nameLen:%ld ageRes:%ld\n", nameRes, nameLen, ageRes);

        auto expectedRes = expectedRes3[i];
        EXPECT_EQ(expectedRes.first, nameRes);
        EXPECT_EQ(strlen(expectedRes.first.c_str()), nameLen);
        EXPECT_EQ(expectedRes.second, ageRes);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)6);

    // 语法测试
    cout << "--------------------------语法测试--------------------------" << endl;

    // 字符串常量
    const char *queryCommand4 = "select * from tablequery4 where LENGTH(\"123456\") >= 5;";
    ret = ExecuteDirectWithPrint(stmt, queryCommand4, "length在where子句中 参数为常量字符串");
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    const char *queryCommand5 = "select LENGTH(\"123456\") from tablequery4;";
    ret = ExecuteDirectWithPrint(stmt, queryCommand5, "length在select中 参数为常量字符串");
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    const char *queryCommand6 = "select * from tablequery4 order by LENGTH(\"123456\");";
    ret = ExecuteDirectWithPrint(stmt, queryCommand6, "length在order by中 参数为常量字符串");
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    // 整形常量
    const char *queryCommand7 = "select * from tablequery4 where LENGTH(123456) >= 5;";
    ret = ExecuteDirectWithPrint(stmt, queryCommand7, "length在where子句中 参数为常量整形");
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    const char *queryCommand8 = "select LENGTH(123456) from tablequery4;";
    ret = ExecuteDirectWithPrint(stmt, queryCommand8, "length在select中 参数为常量整形");
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    const char *queryCommand9 = "select * from tablequery4 order by LENGTH(123456);";
    ret = ExecuteDirectWithPrint(stmt, queryCommand9, "length在order by中 参数为常量整形");
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    // 多个参数
    const char *queryCommand10 = "select * from tablequery4 where LENGTH(id, name) >= 5;";
    ret = ExecuteDirectWithPrint(stmt, queryCommand10, "length在where子句中 参数包含多个");
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    const char *queryCommand11 = "select LENGTH(id, name) from tablequery4;";
    ret = ExecuteDirectWithPrint(stmt, queryCommand11, "length在select中 参数包含多个");
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    const char *queryCommand12 = "select * from tablequery4 order by length(id, name);";
    ret = ExecuteDirectWithPrint(stmt, queryCommand12, "length在order by中 参数包含多个");
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    // group by 中使用length
    const char *queryCommand13 =
        "select age, count(id), 42 from tablequery where id >= 1 group by length(age) order by age;";
    ret = ExecuteDirectWithPrint(stmt, queryCommand13, "group by 中使用length");
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    // 聚合函数参数为Length函数
    const char *queryCommand14 =
        "select age, count(length(id)), 42 from tablequery where id >= 1 group by age order by age;";
    ret = ExecuteDirectWithPrint(stmt, queryCommand14, "group by 中使用length");
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    UnInitConnAndStmt(conn, stmt);
}

// length基础测试
TEST_F(StTsQueryGmc, DISABLED_QueryOpSimple7_Length)
{
    TestLengthFunc();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestLengthFunc();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

void TestLengthFuncFilter()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy", "lucy11", "lucy222", "lucy3333", "lucy44444",
        "", "          ", "!@#$%^&*()"};
    int64_t ages[] = {1, 1, 29, 30, 19, 23, 45, 34, 34, 34, 34, 34, 34, 34};
    int64_t ids[] = {0, 0, 1, 1, 2, 2, 3, 2, 2, 2, 2, 2, 2};
    int64_t worktimes[] = {0, 0, 24, 24, 11, 11, 12, 11, 11, 11, 11, 11, 11};
    int64_t salaries[] = {0, 0, 20000, 10000, 4000, 30000, 31000, 10000, 10000, 10000, 10000, 10000, 10000};
    uint32_t rowNum = 13;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery4");

    // 多个Length函数 and
    const char *queryCommand1 =
        "select name, length(name) from tablequery4 where length(name) > 3 and length(name) <= 6;";

    ret = ExecuteDirectWithPrint(stmt, queryCommand1, "Where子句中多个length函数 and条件");
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);

    bool isNull = false;
    bool eof = false;
    uint32_t i = 0;

    unordered_map<string, int64_t> expectedRes1{
        {"david", 5},
        {"olivia", 6},
        {"lucy", 4},
        {"lucy11", 6},
    };

    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }

        char nameRes[256] = {0};
        uint32_t propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        int64_t nameLenRes = 0;
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &nameLenRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        EXPECT_NE(expectedRes1.find(nameRes), expectedRes1.end());
        if (expectedRes1.find(nameRes) != expectedRes1.end()) {
            auto it = expectedRes1.find(nameRes);
            EXPECT_EQ(it->first, nameRes);
            EXPECT_EQ(it->second, nameLenRes);
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)4);

    // 多个Length函数 or
    const char *queryCommand2 =
        "select name, length(name) from tablequery4 where length(name) = 3 or length(name) > 6;";

    ret = ExecuteDirectWithPrint(stmt, queryCommand2, "Where子句中多个length函数 or条件");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)8);

    unordered_map<string, int64_t> expectedRes2{
        {"nut", 3}, {"bob", 3}, {"tim", 3}, {"lucy222", 7}, {"lucy3333", 8}, {"lucy44444", 9},
        {"          ", 10},  // 空格字符串
        {"!@#$%^&*()", 10},  // 特殊字符
    };

    isNull = false;
    eof = false;
    i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }

        char nameRes[256] = {0};
        uint32_t propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        int64_t nameLenRes = 0;
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &nameLenRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        EXPECT_NE(expectedRes2.find(nameRes), expectedRes2.end());
        if (expectedRes2.find(nameRes) != expectedRes2.end()) {
            auto it = expectedRes2.find(nameRes);
            EXPECT_EQ(it->first, nameRes);
            EXPECT_EQ(it->second, nameLenRes);
        } else {
            cout << "name " << nameRes << endl;
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)8);

    // length函数与其他列group by组合使用
    const char *queryCommand3 = "select worktime, name, length(name) from tablequery4 where length(name) = 3 group by "
                                "worktime order by worktime;";
    ret = ExecuteDirectWithPrint(stmt, queryCommand3, "length函数与其他列group by组合使用");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);

    unordered_map<string, int64_t> expectedRes3{
        {"nut", 0},
        {"bob", 24},
        {"tim", 11},
    };

    isNull = false;
    eof = false;
    i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }

        int64_t workTimeRes = 0;
        uint32_t propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &workTimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        char nameRes[256] = {0};
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        int64_t nameLenRes = 0;
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &nameLenRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        EXPECT_NE(expectedRes3.find(nameRes), expectedRes3.end());
        if (expectedRes3.find(nameRes) != expectedRes3.end()) {
            auto it = expectedRes3.find(nameRes);
            EXPECT_EQ(it->first, nameRes);
            EXPECT_EQ(strlen(it->first.c_str()), nameLenRes);
            EXPECT_EQ(it->second, workTimeRes);
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    const char *queryCommand4 = "select name, length(name) from tablequery4 where length(name) = 0;";

    ret = ExecuteDirectWithPrint(stmt, queryCommand4, "空字符串");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);

    isNull = false;
    eof = false;
    i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }

        char nameRes[256] = {0};
        uint32_t propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        int64_t nameLenRes = 0;
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &nameLenRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        EXPECT_EQ(strcmp(nameRes, ""), 0);
        EXPECT_EQ(nameLenRes, 0);

        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    const char *queryCommand5 = "select name, length(name), count(name) from tablequery4 group by id;";

    ret = ExecuteDirectWithPrint(stmt, queryCommand5, "length函数和聚合函数一起使用1");
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);

    unordered_map<string, int64_t> expectedRes4{
        {"bob", 2},
        {"tim", 8},
        {"david", 2},
        {"lucy11", 1},
    };
    isNull = false;
    eof = false;
    i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }

        char nameRes[256] = {0};
        uint32_t propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        int64_t nameLenRes = 0;
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &nameLenRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        int64_t countNameRes = 0;
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &countNameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        EXPECT_NE(expectedRes4.find(nameRes), expectedRes4.end());
        if (expectedRes3.find(nameRes) != expectedRes3.end()) {
            auto it = expectedRes4.find(nameRes);
            EXPECT_EQ(it->first, nameRes);
            EXPECT_EQ(strlen(it->first.c_str()), nameLenRes);
            EXPECT_EQ(it->second, countNameRes);
        }

        i++;
    }
    EXPECT_EQ(i, (uint32_t)4);

    const char *queryCommand6 = "select id, length(name), count(name) from tablequery4 group by id;";

    ret = ExecuteDirectWithPrint(stmt, queryCommand6, "length函数和聚合函数一起使用2");

    const char *queryCommand7 =
        "select id, length(name), count(name) from tablequery4 group by id order by length(name);";

    ret = ExecuteDirectWithPrint(stmt, queryCommand7, "length函数和聚合函数一起使用3");

    UnInitConnAndStmt(conn, stmt);
}

// length过滤测试
TEST_F(StTsQueryGmc, QueryOpSimple7_Length_filter)
{
    TestLengthFuncFilter();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestLengthFuncFilter();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

void TestLengthFuncBigData()
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);

    uint32_t rowNum = 10000;
    char names[rowNum][256];
    int64_t ages[rowNum];
    int64_t ids[rowNum];
    int64_t worktimes[rowNum];
    int64_t salaries[rowNum];

    for (int i = 0; i < rowNum; i++) {
        sprintf_s(names[i], sizeof(names[i]), "%255d", i);
        ages[i] = 1;
        ids[i] = i;
        worktimes[i] = 1;
        salaries[i] = 100000000 + i;
    }

    Data data = {0};
    data.name = names;
    data.age = ages;
    data.id = ids;
    data.worktime = worktimes;
    data.salary = salaries;
    data.rowNum = rowNum;

    CreateTableAndBulkInsert(stmt, data, "tablequery4");

    uint32_t value = 0;
    const char *queryCommand = "select name, length(name), id from tablequery4 where length(name) = 255 order by id;";
    Status ret = ExecuteDirectWithPrint(stmt, queryCommand, "大数据量 & 大字符串");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)rowNum);

    bool isNull = false;
    bool eof = false;
    uint32_t i = 0;

    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }

        char nameRes[256] = {0};
        uint32_t propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        int64_t nameLenRes = 0;
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &nameLenRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        EXPECT_EQ(strcmp(nameRes, data.name[i]), 0);
        EXPECT_EQ(nameLenRes, 255);
        i++;
    }
    EXPECT_EQ(i, rowNum);

    UnInitConnAndStmt(conn, stmt);
}

// length大数据量
TEST_F(StTsQueryGmc, QueryOpSimple7_Length_BigData)
{
    TestLengthFuncBigData();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgHighCard, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
    TestLengthFuncBigData();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(1000);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(1000);
}

TEST_F(StTsQueryGmc, QueryOpAggWithoutGroupByErr)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    InitConnAndStmt(&conn, &stmt);

    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, 34};
    int64_t ids[] = {1, 1, 2, 2, 3, 2};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint32_t rowNum = 6;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery4");

    const char *queryCommand = "select worktime, first(salary), last(salary) from "
                               "tablequery4 order by worktime;";
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsQueryGmc, QueryOpSimpleblob1)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tablequeryblob(age integer, worktime integer, id blob) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[40] = "select id from tablequeryblob;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t size = 0;
    uint32_t propSize = TS_CHAR_MAX_LEN;
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    uint8_t id[][TS_CHAR_MAX_LEN] = {"0071 3520", "0010 0000", ""};
    uint8_t *newId[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newId[j] = id[j];
    }
    char tableName[30] = "tablequeryblob";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newId, sizeof(newId[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    uint8_t idRes[TS_CHAR_MAX_LEN] = {0};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)TS_CHAR_MAX_LEN);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(memcmp(idRes, id[i], TS_CHAR_MAX_LEN), 0);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpSimpleblobErr1)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tablequeryblobErr(age integer, worktime integer, id blob) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[100] = "select id from tablequeryblobErr where id = '';";
    char qryCommand2[100] = "select id from tablequeryblobErr where id like '00%';";
    char qryCommand3[100] = "select id from tablequeryblobErr where id like '#_00%' escape '#';";
    char qryCommand4[100] = "select id from tablequeryblobErr order by id;";
    char qryCommand5[100] = "select id, sum(age) from tablequeryblobErr group by id;";
    char qryCommand6[100] = "select age, sum(id) from tablequeryblobErr group by age;";
    char qryCommand7[100] = "select age, max(id) from tablequeryblobErr group by age;";
    char qryCommand8[100] = "select age, min(id) from tablequeryblobErr group by age;";
    char qryCommand9[100] = "select age, first(id) from tablequeryblobErr group by age;";
    char qryCommand10[100] = "select age, last(id) from tablequeryblobErr group by age;";
    char qryCommand11[100] = "select age, colset(id) from tablequeryblobErr group by age;";
    char qryCommand12[100] = "select age, sum(id + id) from tablequeryblobErr group by age;";
    char qryCommand13[100] = "select age, id, sum(age) from tablequeryblobErr group by age;";
    char qryCommand14[100] = "select age, length(id) from tablequeryblobErr;";

    uint32_t rowNum = 3;
    uint32_t value = 0;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    uint8_t id[][TS_CHAR_MAX_LEN] = {"0071 3520", "0010 0000", ""};
    uint8_t *newId[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newId[j] = id[j];
    }
    char tableName[30] = "tablequeryblobErr";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newId, sizeof(newId[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    ret = GmcExecDirect(stmt, qryCommand2, 100);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    ret = GmcExecDirect(stmt, qryCommand3, 100);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    ret = GmcExecDirect(stmt, qryCommand4, 100);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    ret = GmcExecDirect(stmt, qryCommand5, 100);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    ret = GmcExecDirect(stmt, qryCommand6, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand7, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand8, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand9, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand10, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand11, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand12, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand13, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand14, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpSimpleblobErr2)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tablequeryblobErr2(age integer, worktime integer, id blob(65536)) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpSimpleblobErr3)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tablequeryblobErr3(age integer, worktime integer, id blob) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";

    uint32_t rowNum = 3;
    uint32_t value = 0;
    char id[][TS_CHAR_MAX_LEN] = {"0071 3520", "0010 0000", ""};
    char tableName[30] = "tablequeryblobErr3";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, id, TS_CHAR_MAX_LEN, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_INVALID_PARAMETER_VALUE);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpSimpleblobErr4)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tablequeryblobErr4(age integer, worktime integer, id blob(200)) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";

    uint32_t rowNum = 3;
    uint32_t value = 0;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    uint8_t id[][200] = {"0071 3520", "0010 0000", ""};
    uint8_t *newId[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newId[j] = id[j];
    }
    char tableName[30] = "tablequeryblobErr4";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newId, 300, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_INVALID_PARAMETER_VALUE);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryAfterAlterTableBlob)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char command[200] = "create table TABLEBULKALTERBLOB(id integer, name char(6)) with (time_col = 'id', "
                        "interval = '1 day', compression = 'fast(rapidlz)', ttl = '3 days');";
    char altercmd1[200] = "alter table tablebulkalterblob add column salary blob(1600);";
    char altercmd2[200] = "alter table tablebulkalterblob add column comment blob;";
    char queryCommand[200] = "select * from tablebulkalterblob;";
    uint32_t rowNum = 3;
    int64_t id[] = {1, 2, 4};
    char name[][6] = {"David", "Lucy", "Lin"};
    uint8_t salary[][1600] = {"0071 3520", "0010 0000", ""};
    uint8_t *newSalary[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newSalary[j] = salary[j];
    }
    uint8_t comment[][TS_CHAR_MAX_LEN] = {"?!2@$123", "?!2@$456", ""};
    uint8_t *newComment[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newComment[j] = comment[j];
    }
    char tableName[40] = "TABLEBULKALTERBLOB";

    InitConnAndStmt(&conn, &stmt);

    Status ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_FIXED, name, sizeof(char) * 6, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    // alter first time
    ret = GmcExecDirect(stmt, altercmd1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // bulk insert first time
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_FIXED, name, sizeof(char) * 6, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newSalary, sizeof(newSalary[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, queryCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t i = 0;
    int64_t idRes = 0;
    char nameRes[6] = {0};
    uint8_t salaryRes[1600] = {0};
    bool eof = false;
    bool isNull = false;
    uint32_t propSize = 1600;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(char) * 6;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        propSize = 1600;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &salaryRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        if (i >= 0 && i <= 2) {
            EXPECT_EQ(idRes, id[i]);
            EXPECT_STREQ(nameRes, name[i]);
            EXPECT_EQ(memcmp(salaryRes, "", 0), 0);
        } else if (i >= 3 && i <= 5) {
            EXPECT_EQ(idRes, id[i - 3]);
            EXPECT_STREQ(nameRes, name[i - 3]);
            EXPECT_EQ(memcmp(salaryRes, salary[i - 3], 1600), 0);
        } else {
            EXPECT_EQ(1, 0);
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)6);
    // alter second time
    ret = GmcExecDirect(stmt, altercmd2, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // insert again
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_FIXED, name, sizeof(char) * 6, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newSalary, sizeof(newSalary[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_BYTES, newComment, sizeof(newComment[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, queryCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    i = 0;
    eof = false;
    uint8_t commentRes[TS_CHAR_MAX_LEN] = {0};
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(char) * 6;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = 1600;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &salaryRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = TS_CHAR_MAX_LEN;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &commentRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        if (i >= 0 && i <= 2) {
            EXPECT_EQ(idRes, id[i]);
            EXPECT_STREQ(nameRes, name[i]);
            EXPECT_EQ(memcmp(salaryRes, "", 0), 0);
            EXPECT_EQ(memcmp(commentRes, "", 0), 0);
        } else if (i >= 3 && i <= 5) {
            EXPECT_EQ(idRes, id[i - 3]);
            EXPECT_STREQ(nameRes, name[i - 3]);
            EXPECT_EQ(memcmp(salaryRes, salary[i - 3], 1600), 0);
            EXPECT_EQ(memcmp(commentRes, "", 0), 0);
        } else if (i >= 6 && i <= 8) {
            EXPECT_EQ(idRes, id[i - 6]);
            EXPECT_STREQ(nameRes, name[i - 6]);
            EXPECT_EQ(memcmp(salaryRes, salary[i - 6], 1600), 0);
            EXPECT_EQ(memcmp(commentRes, comment[i - 6], TS_CHAR_MAX_LEN), 0);
        } else {
            EXPECT_EQ(1, 0);
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)9);

    char dropCommand[60] = "drop table tablebulkalterblob";
    ret = GmcExecDirect(stmt, dropCommand, 60);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpSimpleblobNoCompress)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] = "create table tablequeryblobnocompress(age integer, worktime integer, id blob) with"
                           " (time_col = 'worktime', interval = '1 hour', compression = 'no', ttl = '3 hours');";
    char qryCommand1[100] = "select id from tablequeryblobnocompress;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t size = 0;
    uint32_t propSize = TS_CHAR_MAX_LEN;
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    uint8_t id[][TS_CHAR_MAX_LEN] = {"0071 3520", "0010 0000", ""};
    uint8_t *newId[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newId[j] = id[j];
    }
    char tableName[30] = "tablequeryblobnocompress";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newId, sizeof(newId[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    uint8_t idRes[TS_CHAR_MAX_LEN] = {0};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)TS_CHAR_MAX_LEN);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(memcmp(idRes, id[i], TS_CHAR_MAX_LEN), 0);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpSimpleblobNoData)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] = "create table tablequeryblobnodata(age integer, worktime integer, id blob) with"
                           " (time_col = 'worktime', interval = '1 hour', compression = 'fast', ttl = '3 hours');";
    char qryCommand1[100] = "select id from tablequeryblobnodata;";
    uint32_t value = 0;

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpSimpleBigData)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tablequerybigdata(age integer, worktime integer, id blob) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[100] = "select id from tablequerybigdata;";
    uint32_t rowNum = 10;
    uint32_t value = 0;
    uint32_t size = 0;
    uint32_t propSize = TS_CHAR_MAX_LEN;
    bool eof = false;
    int64_t age[] = {29, 30, 19, 1, 2, 3, 4, 5, 6, 7};
    int64_t worktime[] = {24, 24, 25, 1, 2, 3, 4, 5, 6, 7};
    uint8_t id[][TS_CHAR_MAX_LEN] = {"0071 3520", "0010 0000", "1000 0030", "5132 0781", "0000 0000", "1234 5678",
        "3561 2714", "1111 1111", "2342 6790", "9090 1212"};
    uint8_t *newId[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newId[j] = id[j];
    }
    char tableName[30] = "tablequerybigdata";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newId, sizeof(newId[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    for (uint32_t j = 0; j < 5000; j++) {
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)50000);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)TS_CHAR_MAX_LEN);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = TS_CHAR_MAX_LEN;
        uint8_t idRes[TS_CHAR_MAX_LEN] = {0};
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(memcmp(idRes, id[(i % 10)], TS_CHAR_MAX_LEN), 0);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)50000);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpSimpleAllConst)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, 34};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint32_t rowNum = 6;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "const_table");

    const char *queryCommand = "select 'abcd',1,2,3,4 from const_table";
    // expact result: 11,2
    //                12,3
    //                24,1
    ret = ExecQuerySql(stmt, queryCommand);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    int64_t idRes = 0;
    int64_t ageRes = 0;
    int64_t salaryRes = 0;
    char nameRes[256] = {0};
    bool isNull = false;

    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(DbStrCmp((const char *)nameRes, "abcd", true), 0);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &salaryRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, 1);
        EXPECT_EQ(idRes, 2);
        EXPECT_EQ(worktimeRes, 3);
        EXPECT_EQ(salaryRes, 4);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpSimpleTextWithLength)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char command[200] = "create table tableinsert(id integer, name text) with (time_col = 'id', "
                        "interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char dropCommand[70] = "drop table tableinsert";
    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    // 建表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, dropCommand, 70);
    EXPECT_EQ(ret, GMERR_OK);

    // text不支持指定长度
    char invalidCommand[200] = "create table tableinsert(id integer, name text(100)) with (time_col = 'id', "
                               "interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    ret = GmcExecDirect(stmt, invalidCommand, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpSimpleText)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char command[200] = "create table tableinsert(id integer, name text) with (time_col = 'id', "
                        "interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand[70] = "select id, name, length(name) from tableinsert;";
    char qryCommand2[70] = "select * from tableinsert where name > 'Jack';";
    char qryCommand3[70] = "select * from tableinsert where name like 'L%';";
    char qryCommand4[70] = "select * from tableinsert order by name desc;";
    char qryCommand5[70] = "select * from tableinsert order by name asc limit 4;";
    char qryCommand6[70] = "select * from tableinsert where length(name) > 3";
    char qryCommand7[70] = "select * from tableinsert where name like '%小%';";
    char dropCommand[70] = "drop table tableinsert";
    uint32_t rowNum = 10;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    char nameText[][10] = {"Tom", "Bob", "Andy", "李小m", "Lucy", "", "Lin", "Lili", "Judy", ""};
    char *name[rowNum] = {0};
    for (int i = 0; i < rowNum - 1; i++) {
        name[i] = nameText[i];
    }
    name[rowNum - 1] = NULL;

    char tableName[30] = "tableinsert";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    // 建表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, GMC_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, GMC_DATATYPE_STRING, name, sizeof(name[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    // 全量扫描
    ret = GmcExecDirect(stmt, qryCommand, 70);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t colVal = 0;
    uint32_t rowVal = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colVal, sizeof(uint32_t));
    EXPECT_EQ(colVal, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowVal, sizeof(uint32_t));
    EXPECT_EQ(rowVal, (uint32_t)10);
    bool eof = false;
    bool isNull = false;
    char nameRes[10];
    uint32_t propSize;
    uint32_t i = 0;
    uint64_t lengthRes = 0;
    while (!eof) {
        GmcFetch(stmt, &eof);
        if (eof) {
            break;
        }
        propSize = sizeof(nameRes);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_STREQ(nameText[i], nameRes);
        uint32_t sizeById = 0;
        ret = GmcGetVertexPropertySizeById(stmt, 1, &sizeById);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(sizeById, propSize);
        uint32_t sizeByName = 0;
        ret = GmcGetVertexPropertySizeByName(stmt, "name", &sizeByName);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(sizeByName, propSize);
        EXPECT_EQ(sizeById, propSize);
        propSize = sizeof(uint64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &lengthRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(strlen(nameText[i++]), lengthRes);
    }

    // text filter
    ret = GmcExecDirect(stmt, qryCommand2, 70);
    EXPECT_EQ(ret, GMERR_OK);
    colVal = 0;
    rowVal = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colVal, sizeof(uint32_t));
    EXPECT_EQ(colVal, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowVal, sizeof(uint32_t));
    EXPECT_EQ(rowVal, (uint32_t)6);
    eof = false;
    isNull = false;
    i = 0;
    while (!eof) {
        GmcFetch(stmt, &eof);
        if (eof) {
            break;
        }
        propSize = 10;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        while (strcmp(nameText[i], "Jack") <= 0) {
            i++;
        }
        EXPECT_STREQ(nameText[i++], nameRes);
    }

    // text like
    ret = GmcExecDirect(stmt, qryCommand3, 70);
    EXPECT_EQ(ret, GMERR_OK);
    colVal = 0;
    rowVal = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colVal, sizeof(uint32_t));
    EXPECT_EQ(colVal, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowVal, sizeof(uint32_t));
    EXPECT_EQ(rowVal, (uint32_t)3);
    eof = false;
    isNull = false;
    i = 0;
    while (!eof) {
        GmcFetch(stmt, &eof);
        if (eof) {
            break;
        }
        propSize = 10;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        while (nameText[i][0] != 'L') {
            i++;
        }
        EXPECT_STREQ(nameText[i++], nameRes);
    }

    // text order by
    ret = GmcExecDirect(stmt, qryCommand4, 70);
    EXPECT_EQ(ret, GMERR_OK);
    colVal = 0;
    rowVal = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colVal, sizeof(uint32_t));
    EXPECT_EQ(colVal, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowVal, sizeof(uint32_t));
    EXPECT_EQ(rowVal, (uint32_t)10);
    eof = false;
    isNull = false;
    i = 0;
    char *nameDesc[rowNum];
    for (int j = 0; j < rowNum; j++) {
        nameDesc[j] = nameText[j];
    }
    qsort(nameDesc, rowNum, sizeof(char *),
        [](const void *str1, const void *str2) { return strcmp(*(const char **)str2, *(const char **)str1); });
    while (!eof) {
        GmcFetch(stmt, &eof);
        if (eof) {
            break;
        }
        propSize = 10;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_STREQ(nameDesc[i++], nameRes);
    }

    // text order by limit
    ret = GmcExecDirect(stmt, qryCommand5, 70);
    EXPECT_EQ(ret, GMERR_OK);
    colVal = 0;
    rowVal = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colVal, sizeof(uint32_t));
    EXPECT_EQ(colVal, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowVal, sizeof(uint32_t));
    EXPECT_EQ(rowVal, (uint32_t)4);
    eof = false;
    isNull = false;
    i = 0;
    qsort(nameDesc, rowNum, sizeof(char *),
        [](const void *str1, const void *str2) { return strcmp(*(const char **)str1, *(const char **)str2); });
    while (!eof) {
        GmcFetch(stmt, &eof);
        if (eof) {
            break;
        }
        propSize = 10;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_STREQ(nameDesc[i++], nameRes);
    }

    // text length, for cov only
    ret = GmcExecDirect(stmt, qryCommand6, 70);
    EXPECT_EQ(ret, GMERR_OK);
    colVal = 0;
    rowVal = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colVal, sizeof(uint32_t));
    EXPECT_EQ(colVal, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowVal, sizeof(uint32_t));
    EXPECT_EQ(rowVal, (uint32_t)5);

    // text like chinese
    ret = GmcExecDirect(stmt, qryCommand7, 70);
    EXPECT_EQ(ret, GMERR_OK);
    eof = false;
    isNull = false;
    i = 0;
    while (!eof) {
        GmcFetch(stmt, &eof);
        if (eof) {
            break;
        }
        propSize = 10;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, nameRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_STREQ("李小m", nameRes);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    ret = GmcExecDirect(stmt, dropCommand, 70);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpTextGropBy)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char command[200] = "create table tableinsert(id integer, name text, remark text) with (time_col = 'id', "
                        "interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand[100] = "select count(id), remark, max(remark), name from tableinsert group by name;";
    char qryCommand2[100] = "select sum(remark), name from tableinsert group by name;";
    char dropCommand[70] = "drop table tableinsert";
    uint32_t rowNum = 10;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    char nameText[][15] = {"Sun Wukong", "Tang Sanzang", "Zhu Wuneng", "Sha Wujing", "Sun Wukong", "Sun Wukong",
        "Zhu Wuneng", "Sha Wujing", "Tang Sanzang", "Zhu Wuneng"};
    char *name[rowNum] = {0};
    for (int i = 0; i < rowNum; i++) {
        name[i] = nameText[i];
    }
    char remarkText[rowNum][20] = {"Jin Gu Bang", "Jin Guzhou", "Chang E", "Juan Lian Captain", "Bi Ma Wen", "72", "Pa",
        "Liu Sha River", "Tang Seng", "Tian Peng"};
    char *remark[rowNum] = {0};
    for (int i = 0; i < rowNum; i++) {
        remark[i] = remarkText[i];
    }

    char tableName[30] = "tableinsert";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    // 建表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, GMC_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, GMC_DATATYPE_STRING, name, sizeof(name[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, GMC_DATATYPE_STRING, remark, sizeof(remark[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand, 100);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t colVal = 0;
    uint32_t rowVal = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colVal, sizeof(uint32_t));
    EXPECT_EQ(colVal, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowVal, sizeof(uint32_t));
    EXPECT_EQ(rowVal, (uint32_t)4);
    bool eof = false;
    bool isNull = false;
    uint64_t count = 0;
    char stringRes[20];
    uint32_t propSize;
    struct Result {
        int count;
        char remark[20];
        char maxRemark[20];
        char name[15];
    } result[4] = {{3, "Jin Gu Bang", "Jin Gu Bang", "Sun Wukong"}, {2, "Jin Guzhou", "Tang Seng", "Tang Sanzang"},
        {3, "Chang E", "Tian Peng", "Zhu Wuneng"}, {2, "Juan Lian Captain", "Liu Sha River", "Sha Wujing"}};
    while (!eof) {
        GmcFetch(stmt, &eof);
        if (eof) {
            break;
        }
        uint i = UINT_MAX;
        propSize = 20;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, stringRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        for (int j = 0; j < 4; j++) {
            if (!strcmp(stringRes, result[j].name)) {
                i = j;
                break;
            }
        }
        propSize = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &count, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(result[i].count, count);
        propSize = 20;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, stringRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_STREQ(result[i].remark, stringRes);
        propSize = 20;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, stringRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_STREQ(result[i].maxRemark, stringRes);
    }

    ret = GmcExecDirect(stmt, qryCommand2, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcExecDirect(stmt, dropCommand, 70);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryUnbindCol)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tablequery(worktime integer, age integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[50] = "select age from tablequery where age < 10;";

    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    uint32_t size = 0;
    bool eof = false;
    int64_t id[] = {1, 2, 4};
    int64_t worktime[] = {24, 24, 25};
    char tableName[30] = "tablequery";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertySizeById(stmt, 0, &size);  // abnormal situation
    EXPECT_NE(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t ageRes = 0;
    bool isNull = false;

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand1, 50);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, 0);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryCreateTable)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] = "create table tablequeryCustom(age integer, worktime integer, id blob) with"
                           " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 "
                           "hours', table_path = '/home/<USER>/');";
    char qryCommand1[40] = "select id from tablequeryCustom;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t size = 0;
    uint32_t propSize = TS_CHAR_MAX_LEN;
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    uint8_t id[][TS_CHAR_MAX_LEN] = {"0071 3520", "0010 0000", ""};
    uint8_t *newId[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newId[j] = id[j];
    }
    char tableName[30] = "tablequeryCustom";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newId, sizeof(newId[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    uint8_t idRes[TS_CHAR_MAX_LEN] = {0};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)TS_CHAR_MAX_LEN);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(memcmp(idRes, id[i], TS_CHAR_MAX_LEN), 0);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    UnInitConnAndStmt(conn, stmt);
    system("rm -rf /home/<USER>/");
}

static int32_t GetLargeResultFileNum()
{
    char buf[1024] = {0};
    FILE *pf = popen("ls -A /data/gmdb/temp/large_result 2>/dev/null | wc -l", "r");
    if (pf == NULL) {
        printf("popen error./n");
        return -1;
    }
    fgets(buf, 1024, pf);
    if (strlen(buf) == 0) {
        (void)pclose(pf);
        return -1;
    }

    if (pclose(pf) == -1) {
        perror("pclose fail");
    }
    pf = NULL;
    return atoi(buf);
}

TEST_F(StTsQueryGmc, QueryLargeResultWithTempMaxLimit)
{
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(100);
    StartDbServer((char *)g_cfgLargeResult, "usocket:/run/verona/unix_emserver");
    DbSleep(100);

    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[300] =
        "create table tableLargeLimit(age integer, id integer, worktime integer, a integer, b integer, c integer, d "
        "integer, e integer, f integer, g integer, h integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[40] = "select * from tableLargeLimit;";

    uint32_t rowNum = 30000;
    uint32_t batchNum = 10;
    uint32_t value = 0;
    uint32_t size = 0;
    bool eof = false;
    int64_t id[30000] = {0};
    int64_t age[30000] = {0};
    int64_t worktime[30000] = {0};
    char tableName[30] = "tableLargeLimit";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand, 300);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)11);
    for (uint32_t i = 0; i < batchNum; i++) {
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 8, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 9, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 10, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand1, 40);
    EXPECT_EQ(ret, GMERR_FILE_NO_SPACE_ERROR);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)11);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);

    EXPECT_LT(value, (uint32_t)batchNum * rowNum);  // 小于实际存量

    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 2, &size);
        EXPECT_EQ(size, (uint32_t)8);
        EXPECT_EQ(ret, GMERR_OK);

        i++;
    }
    EXPECT_EQ(value, i);
    UnInitConnAndStmt(conn, stmt);

    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(100);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(100);
}

// 测试并发大结果集查询中回收临时文件机制是否会互相影响
TEST_F(StTsQueryGmc, DISABLED_QueryLargeResultCC1)
{
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(100);
    StartDbServer((char *)g_cfgLargeResult, "usocket:/run/verona/unix_emserver");
    DbSleep(100);

    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[300] =
        "create table tableLargeCC1(age integer, id integer, worktime integer, a integer, b integer, c integer, d "
        "integer, e integer, f integer, g integer, h integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[40] = "select * from tableLargeCC1;";

    uint32_t rowNum = 30;
    uint32_t batchNum = 10;
    uint32_t value = 0;
    uint32_t size = 0;
    bool eof = false;
    int64_t id[30000] = {0};
    int64_t age[30000] = {0};
    int64_t worktime[30000] = {0};
    char tableName[30] = "tableLargeCC1";

    // 先做好前置建表和数据
    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand, 300);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)11);
    for (uint32_t i = 0; i < batchNum; i++) {
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 8, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 9, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 10, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    pid_t p1 = fork();
    if (p1 < 0) {
        printf("unable to fork a new process.");
        ASSERT_TRUE(false);
    } else if (p1 == 0) {
        InitConnAndStmt(&conn, &stmt);

        // cmd 1
        ret = GmcExecDirect(stmt, qryCommand1, 40);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(value, (uint32_t)11);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
        EXPECT_EQ(ret, GMERR_OK);
        exit(0);  // 这里进程退出会回收对应的临时文件
    }

    const char *qryCommand2 = "select age from tableLargeCC1 limit 100";
    ret = GmcExecDirect(stmt, qryCommand2, 50);

    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 100);

    wait(NULL);

    EXPECT_GT(GetLargeResultFileNum(), 0);  // 临时文件目录下有数据

    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)8);
        EXPECT_EQ(ret, GMERR_OK);

        i++;
    }
    EXPECT_EQ(value, i);

    EXPECT_EQ(GetLargeResultFileNum(), 0);  // 查询完后临时文件目录下无数据
    UnInitConnAndStmt(conn, stmt);

    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(100);
    StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(100);
}

TEST_F(StTsQueryGmc, QueryOpCol)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tablequeryCol(age integer, worktime integer, id char(10)) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(zstar)', ttl = '3 hours');";
    char qryCommand1[200] = "select sum(age + worktime), age, worktime from tablequeryCol order by age, worktime;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    char id[][1600] = {"test1", "test2", "test3"};
    char tableName[30] = "tablequeryCol";
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, id, 10, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t ageRes = 0;
    int64_t worktimeRes = 0;
    int64_t sumVal = 0;
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &sumVal, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(sumVal, 151);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, 29);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(worktimeRes, 24);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpAdd)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] = "create table tableopadd(id integer, worktime integer, name char(64), ip inet) with"
                           " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[100] = "select id, worktime, length(id), id + worktime from tableopadd order by id;";
    char qryCommand2[100] = "select 1, id, id + worktime from tableopadd order by id;";

    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    uint32_t size = 0;
    bool eof = false;
    int64_t id[] = {1, 2, 4};
    int64_t worktime[] = {24, 24, 25};
    char name[][64] = {"david", "nut", "bob"};
    char ip[][33] = {"1b840164", "1b840164", "1b840164"};
    int64_t addResult[] = {25, 26, 29};
    char tableName[30] = "tableopadd";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertySizeById(stmt, 0, &size);  // abnormal situation
    EXPECT_NE(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 64, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ip, 33, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t worktimeRes = 0;
    int64_t lengthRes = 0;
    int64_t checkRes = 0;
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)8);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(worktimeRes, worktime[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &lengthRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(lengthRes, 1);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &checkRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(checkRes, addResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    ret = GmcExecDirect(stmt, qryCommand2, 100);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    i = 0;
    eof = false;
    int64_t constValRes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)8);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &constValRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(constValRes, 1);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &checkRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(checkRes, addResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpAdd2)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] = "create table tableopadd2(id integer, worktime integer, name char(64), ip inet) with"
                           " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[100] = "select id, worktime, id, id + worktime from tableopadd2;";
    char qryCommand2[100] = "select id, worktime, length(id), id + worktime from tableopadd2;";
    char qryCommand3[100] = "select id, worktime, length(id), id + worktime from tableopadd2 where id > 0 limit 10;";
    char qryCommand4[100] = "select id, worktime, length(id), id + worktime from tableopadd2 group by ip order by id;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    uint32_t size = 0;
    bool eof = false;
    int64_t id[] = {1, 2, 4};
    int64_t worktime[] = {24, 24, 25};
    char name[][64] = {"david", "nut", "bob"};
    char ip[][33] = {"1b840164", "1b840111", "1b840108"};
    int64_t addResult[] = {25, 26, 29};
    char tableName[30] = "tableopadd2";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertySizeById(stmt, 0, &size);  // abnormal situation
    EXPECT_NE(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 64, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ip, 33, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t checkRes = 0;
    int64_t worktimeRes = 0;
    bool isNull = false;

    // cmd1
    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)8);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(worktimeRes, worktime[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &checkRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(checkRes, addResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    // cmd2
    int64_t lengthRes = 0;
    ret = GmcExecDirect(stmt, qryCommand2, 100);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)8);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(worktimeRes, worktime[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &lengthRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(lengthRes, 1);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &checkRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(checkRes, addResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    // cmd3
    ret = GmcExecDirect(stmt, qryCommand3, 100);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)8);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(worktimeRes, worktime[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &lengthRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(lengthRes, 1);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &checkRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(checkRes, addResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    // cmd4
    ret = GmcExecDirect(stmt, qryCommand4, 100);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)8);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(worktimeRes, worktime[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &lengthRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(lengthRes, 1);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &checkRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(checkRes, addResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryRepeatCol)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] = "create table tablerepeatcol(id integer, worktime integer, name char(64), ip inet) with"
                           " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[200] = "select id, worktime, ip, worktime from tablerepeatcol group by ip order by sum(id);";
    char qryCommand2[200] =
        "select id, worktime, ip, worktime from tablerepeatcol group by ip order by sum(id) limit 10;";
    char qryCommand3[200] = "select id, ip, worktime, ip from tablerepeatcol group by ip order by sum(id);";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    uint32_t size = 0;
    bool eof = false;
    int64_t id[] = {1, 2, 4};
    int64_t worktime[] = {24, 24, 25};
    char name[][64] = {"david", "nut", "bob"};
    char ip[][33] = {"1b840164", "1b840111", "1b840172"};
    char tableName[30] = "tablerepeatcol";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetVertexPropertySizeById(stmt, 0, &size);  // abnormal situation
    EXPECT_NE(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 64, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ip, 33, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t worktimeRes = 0;
    char ipRes[33] = {0};
    bool isNull = false;

    // cmd1
    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)8);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(worktimeRes, worktime[i]);
        propSize = 33;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &ipRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(DbStrCmp(ipRes, ip[i], false), 0);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(worktimeRes, worktime[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    // cmd2
    ret = GmcExecDirect(stmt, qryCommand2, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)8);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(worktimeRes, worktime[i]);
        propSize = 33;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &ipRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(DbStrCmp(ipRes, ip[i], false), 0);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(worktimeRes, worktime[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    // cmd3
    ret = GmcExecDirect(stmt, qryCommand3, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)8);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id[i]);
        propSize = 33;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ipRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(DbStrCmp(ipRes, ip[i], false), 0);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(worktimeRes, worktime[i]);
        propSize = 33;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &ipRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(DbStrCmp(ipRes, ip[i], false), 0);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpFuzzErr1)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "CREATE TABLE v0 ( v1 INTEGER , v2 INTEGER , v3 INTEGER , v4 INTEGER , v5 INTEGER , v27 "
                           "INTEGER , v26 INTEGER , v25 INTEGER , v23 INTEGER , v24 INTEGER , v22 INTEGER , v21 "
                           "INTEGER , v19 INTEGER , v20 INTEGER , v18 INTEGER , v17 INTEGER , v15 INTEGER , v16 "
                           "INTEGER , v14 INTEGER , v13 INTEGER , v11 INTEGER , v12 INTEGER , v10 INTEGER , v9 INTEGER "
                           ", v7 INTEGER , v8 INTEGER , v6 INTEGER ) WITH ( TIME_COL = 'v1' , INTERVAL = '1 hour' ) ;";
    char qryCommand1[500] = "SELECT 'v1' FROM v0 WHERE ( v2 > 110 AND v1 >= 'v1' ) OR ( v8 = 'v0' OR v9 = 'test data "
                            "of the text type: 4999' OR v7 = 'v1' OR v3 <= 'v0' OR v10 = 'x' OR v4 = '33333333' OR v6 "
                            "= 'v1' OR v5 = 'x' OR v11 = 'v1' OR 'root:st_client' = '06000043' );";
    char qryCommand2[500] = "SELECT v1 FROM v0 WHERE v1 < -+ 100 limit 10;";
    char qryCommand3[500] = "SELECT v1 FROM v0 WHERE v1 + v1 > ++++- 100 limit 10;";
    char qryCommand4[500] = "SELECT v1 FROM v0 WHERE v1 < ++++- 100 limit 10;";
    char qryCommand5[500] = "SELECT v1 FROM v0 WHERE v1 + v1 > -+ 100 limit 10;";
    char qryCommand6[500] = "SELECT v1 FROM v0 WHERE v1 < ++++ 100 limit 10;";
    char qryCommand7[500] = "SELECT v1 FROM v0 WHERE v1 + v1 > -+ 100 limit 10;";
    char qryCommand8[500] = "SELECT v1 FROM v0 WHERE v1 < - 100 limit 10;";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;

    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd
    ret = GmcExecDirect(stmt, qryCommand1, 500);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    ret = GmcExecDirect(stmt, qryCommand2, 500);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcExecDirect(stmt, qryCommand3, 500);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, qryCommand4, 500);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, qryCommand5, 500);
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);
    ret = GmcExecDirect(stmt, qryCommand6, 500);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, qryCommand7, 500);
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);
    ret = GmcExecDirect(stmt, qryCommand8, 500);
    EXPECT_EQ(GMERR_OK, ret);

    char dropCommand[60] = "drop table v0;";
    ret = GmcExecDirect(stmt, dropCommand, 60);
    EXPECT_EQ(GMERR_OK, ret);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryEmptyTable)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;

    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    uint32_t size = 0;
    bool eof = false;

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    char ddlCommand[200] =
        "create table tablequery(age integer, id integer, worktime integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t result = 0;
    char qryCommand4[200] = "select count(age), max(age), worktime from tablequery order by id desc;";
    ret = GmcExecDirect(stmt, qryCommand4, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t i = 0;
    bool isNull = false;
    eof = false;
    ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
    EXPECT_EQ(size, (uint32_t)8);
    EXPECT_EQ(ret, GMERR_OK);
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &result, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(result, 0);
        EXPECT_EQ(propSize, sizeof(int64_t));
        EXPECT_EQ(isNull, false);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &result, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(result, 0);
        EXPECT_EQ(propSize, 0);
        EXPECT_EQ(isNull, true);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &result, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(result, 0);
        EXPECT_EQ(propSize, 0);
        EXPECT_EQ(isNull, true);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    char qryCommand5[200] = "select count(age) from tablequery;";
    ret = GmcExecDirect(stmt, qryCommand5, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);

    char qryCommand6[200] = "select worktime from tablequery;";
    ret = GmcExecDirect(stmt, qryCommand6, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpOffsetExecption)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    Status ret;
    InitConnAndStmt(&conn, &stmt);

    char dropCommand[200] = "drop table tablequery3;";
    uint32_t rowNum = 6;
    char name[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, 34};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 18, 25, 11, 12, 10};
    int64_t salary[] = {10000, 20000, 4000, 30000, 31000, 10000};

    Data data = {.name = name, .age = age, .id = id, .worktime = worktime, .salary = salary, .rowNum = rowNum};
    CreateTableAndBulkInsert(stmt, data, "tablequery3");

    char qryCommand1[150] = "select id, age from tablequery3 offset 2;";
    ret = GmcExecDirect(stmt, qryCommand1, 150);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    char qryCommand2[150] = "select count(*) from tablequery3 limit 4 offset -2543;";
    ret = GmcExecDirect(stmt, qryCommand2, 150);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    char qryCommand3[150] = "select * from tablequery3 limit 3 offset 4294967300;";
    ret = GmcExecDirect(stmt, qryCommand3, 150);
    EXPECT_EQ(ret, GMERR_FIELD_OVERFLOW);

    char qryCommand4[150] = "select salary, id from tablequery3 limit -434 offset 2;";
    ret = GmcExecDirect(stmt, qryCommand4, 150);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    char qryCommand5[150] = "select sum(id) from tablequery3 limit 3 offset 3 offset 2;";
    ret = GmcExecDirect(stmt, qryCommand5, 150);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    char qryCommand6[150] = "select * from tablequery3 order by id limit 3 offset 3,3;";
    char qryCommand7[150] = "select * from tablequery3 order by id limit 3 offset 3 3;";
    ret = GmcExecDirect(stmt, qryCommand6, 150);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    ret = GmcExecDirect(stmt, qryCommand7, 150);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    char qryCommand8[150] = "select * from tablequery3 where id > 5 limit 3 offset;";
    ret = GmcExecDirect(stmt, qryCommand8, 150);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    char qryCommand9[150] = "select name, id, age from tablequery3 limit 2,3 offset 3;";
    ret = GmcExecDirect(stmt, qryCommand9, 150);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    char qryCommand10[150] = "select name, id, age from tablequery3 limit 2,3,3;";
    ret = GmcExecDirect(stmt, qryCommand10, 150);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    char qryCommand11[150] = "select * from tablequery3 where id > 10 limit 1.5,2.5;";
    ret = GmcExecDirect(stmt, qryCommand11, 150);
    EXPECT_EQ(ret, GMERR_DATATYPE_MISMATCH);

    char qryCommand12[150] = "select age from tablequery3 offset 2 limit 2;";
    ret = GmcExecDirect(stmt, qryCommand12, 150);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    char qryCommand13[150] = "select * from tablequery3 order by id desc limit 4 offset 'a';";
    ret = GmcExecDirect(stmt, qryCommand13, 150);
    EXPECT_EQ(ret, GMERR_DATATYPE_MISMATCH);

    char qryCommand14[150] = "select * from tablequery3 order by id desc limit 4 offset *;";
    ret = GmcExecDirect(stmt, qryCommand14, 150);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    char qryCommand15[150] = "select * from tablequery3 order by id desc limit 4 offset ?;";
    ret = GmcExecDirect(stmt, qryCommand15, 150);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    char qryCommand16[150] = "select * from tablequery3 order by id desc limit ?,4;";
    ret = GmcExecDirect(stmt, qryCommand16, 150);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpOffset0)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    uint32_t propSize = 0;
    bool eof = false;
    Status ret;
    InitConnAndStmt(&conn, &stmt);

    char dropCommand[200] = "drop table tablequery3;";
    uint32_t rowNum = 6;
    char name[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, 34};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 18, 25, 11, 12, 10};
    int64_t salary[] = {10000, 20000, 4000, 30000, 31000, 10000};

    Data data = {.name = name, .age = age, .id = id, .worktime = worktime, .salary = salary, .rowNum = rowNum};
    CreateTableAndBulkInsert(stmt, data, "tablequery3");

    char qryCommand[150] = "select * from tablequery3 LIMIT 3 OFFSET 2;";
    ret = GmcExecDirect(stmt, qryCommand, 150);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t worktimeRes = 0;
    int64_t salaryRes = 0;
    int64_t ageRes = 0;
    char nameRes[256] = {0};
    bool isNull = false;

    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &idRes, &propSize, &isNull);
        EXPECT_EQ(idRes, id[i + 2]);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(worktimeRes, worktime[i + 2]);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &salaryRes, &propSize, &isNull);
        EXPECT_EQ(salaryRes, salary[i + 2]);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ageRes, age[i + 2]);
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        EXPECT_EQ(DbStrCmp(nameRes, name[i + 2], false), 0);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    // command 2
    char qryCommand1[150] = "select * from tablequery3 LIMIT 3 OFFSET 20;";
    uint32_t value = 0;
    ret = GmcExecDirect(stmt, qryCommand1, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 0);

    // command 3
    char qryCommand2[150] = "select * from tablequery3 LIMIT 3 OFFSET 5";
    ret = GmcExecDirect(stmt, qryCommand2, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 1);

    i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &idRes, &propSize, &isNull);
        EXPECT_EQ(idRes, id[rowNum - 1]);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(worktimeRes, worktime[rowNum - 1]);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &salaryRes, &propSize, &isNull);
        EXPECT_EQ(salaryRes, salary[rowNum - 1]);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ageRes, age[rowNum - 1]);
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        EXPECT_EQ(DbStrCmp(nameRes, name[rowNum - 1], false), 0);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    char qryCommand3[150] = "select * from tablequery3 LIMIT 0 OFFSET 4;";
    ret = GmcExecDirect(stmt, qryCommand3, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 0);

    char qryCommand4[150] = "select * from tablequery3 LIMIT 4 OFFSET 0;";
    ret = GmcExecDirect(stmt, qryCommand4, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 4);
    i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &idRes, &propSize, &isNull);
        EXPECT_EQ(idRes, id[i]);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(worktimeRes, worktime[i]);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &salaryRes, &propSize, &isNull);
        EXPECT_EQ(salaryRes, salary[i]);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ageRes, age[i]);
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        EXPECT_EQ(DbStrCmp(nameRes, name[i], false), 0);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)4);

    char qryCommand5[150] = "select sum(1), id, avg(id) from tablequery3 where id > 1.1 order by id limit 10;";
    ret = GmcExecDirect(stmt, qryCommand5, 150);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpOffset1)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    uint32_t propSize = 0;
    bool eof = false;
    Status ret;
    InitConnAndStmt(&conn, &stmt);

    char dropCommand[200] = "drop table tablequery3;";
    uint32_t rowNum = 6;
    char name[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, 34};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 18, 25, 11, 12, 10};
    int64_t salary[] = {10000, 20000, 4000, 30000, 31000, 10000};

    Data data = {.name = name, .age = age, .id = id, .worktime = worktime, .salary = salary, .rowNum = rowNum};
    CreateTableAndBulkInsert(stmt, data, "tablequery3");
    int64_t idRes = 0;
    int64_t worktimeRes = 0;
    int64_t salaryRes = 0;
    int64_t ageRes = 0;
    bool isNull = false;
    uint32_t i = 0;
    isNull = false;
    eof = false;
    uint32_t value = 0;

    char qryCommand1[150] = "select max(age), sum(id), count(worktime), min(salary) from tablequery3 LIMIT 3 OFFSET 0;";
    ret = GmcExecDirect(stmt, qryCommand1, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 1);
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ageRes, 45);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(idRes, 38);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(worktimeRes, 6);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &salaryRes, &propSize, &isNull);
        EXPECT_EQ(salaryRes, 4000);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    char qryCommand2[150] = "select max(age), sum(id), count(worktime), min(salary) from tablequery3 LIMIT 3 OFFSET 1;";
    ret = GmcExecDirect(stmt, qryCommand2, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 0);

    char qryCommand3[150] = "select max(age), sum(id), count(worktime), min(salary) from tablequery3 LIMIT 0 OFFSET 2;";
    ret = GmcExecDirect(stmt, qryCommand3, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 0);

    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpOffset2)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    uint32_t propSize = 0;
    bool eof = false;
    Status ret;
    InitConnAndStmt(&conn, &stmt);

    char dropCommand[200] = "drop table tablequery3;";
    uint32_t rowNum = 9;
    char name[][256] = {"david", "jack", "anna", "nut", "bob", "olivia", "tim", "lucy", "maria"};
    int64_t age[] = {30, 30, 19, 19, 23, 23, 24, 24, 23};
    int64_t id[] = {1, 2, 4, 9, 8, 14, 11, 16, 19};
    int64_t worktime[] = {24, 18, 25, 11, 12, 10, 17, 26, 9};
    int64_t salary[] = {10000, 20000, 4000, 30000, 31000, 10000, 5000, 25000, 31000};

    Data data = {.name = name, .age = age, .id = id, .worktime = worktime, .salary = salary, .rowNum = rowNum};
    CreateTableAndBulkInsert(stmt, data, "tablequery3");
    int64_t idRes = 0;
    int64_t worktimeRes = 0;
    int64_t salaryRes = 0;
    int64_t ageRes = 0;
    bool isNull = false;
    uint32_t i = 0;
    isNull = false;
    eof = false;
    uint32_t value = 0;

    // 25, 34000, 2, 19
    // 12, 72000, 3, 23
    // 26, 30000, 2, 24
    // 24, 30000, 2, 30
    char qryCommand1[150] = "select max(worktime), sum(salary), count(id), age from tablequery3 group by age order by "
                            "age LIMIT 2 OFFSET 1;";
    ret = GmcExecDirect(stmt, qryCommand1, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 2);
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &salaryRes, &propSize, &isNull);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &idRes, &propSize, &isNull);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &ageRes, &propSize, &isNull);
        printf("%ld, %ld, %ld, %ld\n", worktimeRes, salaryRes, idRes, ageRes);
        switch (i) {
            case 0:
                EXPECT_EQ(worktimeRes, 12);
                EXPECT_EQ(salaryRes, 72000);
                EXPECT_EQ(idRes, 3);
                EXPECT_EQ(ageRes, 23);
                break;
            case 1:
                EXPECT_EQ(worktimeRes, 26);
                EXPECT_EQ(salaryRes, 30000);
                EXPECT_EQ(idRes, 2);
                EXPECT_EQ(ageRes, 24);
                break;
            default:
                EXPECT_EQ(1, 0);
                break;
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpOffset3)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    uint32_t propSize = 0;
    bool eof = false;
    Status ret;
    InitConnAndStmt(&conn, &stmt);

    char dropCommand[200] = "drop table tablequery3;";
    uint32_t rowNum = 9;
    char name[][256] = {"david", "jack", "anna", "nut", "bob", "olivia", "tim", "lucy", "maria"};
    int64_t age[] = {30, 30, 19, 19, 23, 23, 24, 24, 23};
    int64_t id[] = {1, 2, 4, 9, 8, 14, 11, 16, 19};
    int64_t worktime[] = {24, 18, 25, 11, 12, 10, 17, 26, 9};
    int64_t salary[] = {10000, 20000, 4000, 30000, 31000, 10000, 5000, 25000, 31000};

    Data data = {.name = name, .age = age, .id = id, .worktime = worktime, .salary = salary, .rowNum = rowNum};
    CreateTableAndBulkInsert(stmt, data, "tablequery3");
    int64_t idRes = 0;
    int64_t worktimeRes = 0;
    int64_t salaryRes = 0;
    int64_t ageRes = 0;
    bool isNull = false;
    uint32_t i = 0;
    isNull = false;
    eof = false;
    uint32_t value = 0;

    // name, age, id, worktime, salary
    // nut, 19, 9, 11, 30000
    // bob, 23, 8, 12, 31000
    // olivia, 23, 14, 10, 10000
    // tim, 24, 11, 17, 5000
    // lucy, 24, 16, 26, 25000
    // maria, 23, 19, 9, 31000
    char qryCommand1[150] = "select worktime, salary, id, age from tablequery3 where id >= 8 LIMIT 2 OFFSET 2;";
    ret = GmcExecDirect(stmt, qryCommand1, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 2);
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &salaryRes, &propSize, &isNull);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &idRes, &propSize, &isNull);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &ageRes, &propSize, &isNull);
        switch (i) {
            case 0:
                EXPECT_EQ(worktimeRes, 10);
                EXPECT_EQ(salaryRes, 10000);
                EXPECT_EQ(idRes, 14);
                EXPECT_EQ(ageRes, 23);
                break;
            case 1:
                EXPECT_EQ(worktimeRes, 17);
                EXPECT_EQ(salaryRes, 5000);
                EXPECT_EQ(idRes, 11);
                EXPECT_EQ(ageRes, 24);
                break;
            default:
                EXPECT_EQ(1, 0);
                break;
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    // name, age, id, worktime, salary
    // lucy, 24, 16, 26, 25000
    // tim, 24, 11, 17, 5000
    // olivia, 23, 14, 10, 10000
    // maria, 23, 19, 9, 31000
    char qryCommand2[150] =
        "select worktime, salary, id, age from tablequery3 where id >= 10 order by worktime desc LIMIT 2 OFFSET 2;";
    ret = GmcExecDirect(stmt, qryCommand2, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 2);
    eof = false;
    i = 0;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &salaryRes, &propSize, &isNull);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &idRes, &propSize, &isNull);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &ageRes, &propSize, &isNull);
        switch (i) {
            case 0:
                EXPECT_EQ(worktimeRes, 10);
                EXPECT_EQ(salaryRes, 10000);
                EXPECT_EQ(idRes, 14);
                EXPECT_EQ(ageRes, 23);
                break;
            case 1:
                EXPECT_EQ(worktimeRes, 9);
                EXPECT_EQ(salaryRes, 31000);
                EXPECT_EQ(idRes, 19);
                EXPECT_EQ(ageRes, 23);
                break;
            default:
                EXPECT_EQ(1, 0);
                break;
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpOffset4)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    uint32_t propSize = 0;
    bool eof = false;
    Status ret;
    InitConnAndStmt(&conn, &stmt);

    char dropCommand[200] = "drop table tablequery3;";
    uint32_t rowNum = 9;
    char name[][256] = {"david", "jack", "anna", "nut", "bob", "olivia", "tim", "lucy", "maria"};
    int64_t age[] = {30, 30, 19, 19, 23, 23, 24, 24, 23};
    int64_t id[] = {1, 2, 4, 9, 8, 14, 11, 16, 19};
    int64_t worktime[] = {24, 18, 25, 11, 12, 10, 17, 26, 9};
    int64_t salary[] = {10000, 20000, 4000, 30000, 31000, 10000, 5000, 25000, 31000};

    Data data = {.name = name, .age = age, .id = id, .worktime = worktime, .salary = salary, .rowNum = rowNum};
    CreateTableAndBulkInsert(stmt, data, "tablequery3");
    int64_t idRes = 0;
    int64_t worktimeRes = 0;
    int64_t salaryRes = 0;
    int64_t ageRes = 0;
    char nameRes[256] = {0};
    bool isNull = false;
    uint32_t i = 0;
    isNull = false;
    eof = false;
    uint32_t value = 0;

    char qryCommand1[150] = "select * from tablequery3 limit 3, 5;";
    ret = GmcExecDirect(stmt, qryCommand1, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);

    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &idRes, &propSize, &isNull);
        EXPECT_EQ(idRes, id[i + 3]);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(worktimeRes, worktime[i + 3]);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &salaryRes, &propSize, &isNull);
        EXPECT_EQ(salaryRes, salary[i + 3]);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ageRes, age[i + 3]);
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        EXPECT_EQ(DbStrCmp(nameRes, name[i + 3], false), 0);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)5);

    // command 2
    char qryCommand2[150] = "select * from tablequery3 LIMIT 20,3;";
    ret = GmcExecDirect(stmt, qryCommand2, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 0);

    // command 3
    char qryCommand3[150] = "select * from tablequery3 LIMIT 7,10;";
    ret = GmcExecDirect(stmt, qryCommand3, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 2);

    char qryCommand4[150] = "select age from tablequery3 LIMIT 0, 6;";
    ret = GmcExecDirect(stmt, qryCommand4, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 6);

    char qryCommand5[150] = "select age from tablequery3 LIMIT 6, 0;";
    ret = GmcExecDirect(stmt, qryCommand5, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 0);

    char qryCommand6[150] = "select max(worktime), sum(salary), count(id), age from tablequery3 group by age order by "
                            "age limit 1,2;";
    ret = GmcExecDirect(stmt, qryCommand6, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 2);
    eof = false;
    i = 0;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &salaryRes, &propSize, &isNull);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &idRes, &propSize, &isNull);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &ageRes, &propSize, &isNull);
        printf("%ld, %ld, %ld, %ld\n", worktimeRes, salaryRes, idRes, ageRes);
        switch (i) {
            case 0:
                EXPECT_EQ(worktimeRes, 12);
                EXPECT_EQ(salaryRes, 72000);
                EXPECT_EQ(idRes, 3);
                EXPECT_EQ(ageRes, 23);
                break;
            case 1:
                EXPECT_EQ(worktimeRes, 26);
                EXPECT_EQ(salaryRes, 30000);
                EXPECT_EQ(idRes, 2);
                EXPECT_EQ(ageRes, 24);
                break;
            default:
                EXPECT_EQ(1, 0);
                break;
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    char qryCommand7[150] = "select worktime, salary, id, age from tablequery3 where id >= 8 LIMIT 2,2;";
    ret = GmcExecDirect(stmt, qryCommand7, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 2);
    i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &salaryRes, &propSize, &isNull);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &idRes, &propSize, &isNull);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &ageRes, &propSize, &isNull);
        switch (i) {
            case 0:
                EXPECT_EQ(worktimeRes, 10);
                EXPECT_EQ(salaryRes, 10000);
                EXPECT_EQ(idRes, 14);
                EXPECT_EQ(ageRes, 23);
                break;
            case 1:
                EXPECT_EQ(worktimeRes, 17);
                EXPECT_EQ(salaryRes, 5000);
                EXPECT_EQ(idRes, 11);
                EXPECT_EQ(ageRes, 24);
                break;
            default:
                EXPECT_EQ(1, 0);
                break;
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    char qryCommand8[150] =
        "select worktime, salary, id, age from tablequery3 where id >= 10 order by worktime desc LIMIT 2,2;";
    ret = GmcExecDirect(stmt, qryCommand8, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, 2);
    eof = false;
    i = 0;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &salaryRes, &propSize, &isNull);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &idRes, &propSize, &isNull);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &ageRes, &propSize, &isNull);
        switch (i) {
            case 0:
                EXPECT_EQ(worktimeRes, 10);
                EXPECT_EQ(salaryRes, 10000);
                EXPECT_EQ(idRes, 14);
                EXPECT_EQ(ageRes, 23);
                break;
            case 1:
                EXPECT_EQ(worktimeRes, 9);
                EXPECT_EQ(salaryRes, 31000);
                EXPECT_EQ(idRes, 19);
                EXPECT_EQ(ageRes, 23);
                break;
            default:
                EXPECT_EQ(1, 0);
                break;
        }
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

void CreateTableForOffset(GmcStmtT *stmt, const char *tableName)
{
    assert(tableName != NULL);

    char ddlCommand[256];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(256), age integer, id integer, worktime integer, salary integer) "
        "with (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        tableName);
    Status ret = GmcExecDirect(stmt, ddlCommand, 256);
    EXPECT_EQ(ret, GMERR_OK);
}

void BulkInsertDataForOffset(GmcStmtT *stmt, Data data, const char *tableName)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 256, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsQueryGmc, QueryOpOffset5)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    uint32_t propSize = 0;
    bool eof = false;
    Status ret;
    InitConnAndStmt(&conn, &stmt);

    CreateTableForOffset(stmt, "tablequery3");
    char dropCommand[200] = "drop table tablequery3;";
    uint32_t rowNum = 1000;
    uint32_t batch = 100;
    char name[rowNum][256];
    int64_t age[rowNum];
    int64_t id[rowNum];
    int64_t worktime[rowNum];
    int64_t salary[rowNum];

    for (int i = 0; i < batch; i++) {
        for (int j = 0; j < rowNum; j++) {
            age[j] = i * rowNum + j;
            id[j] = i * rowNum + j;
            worktime[j] = i * rowNum + j;
            salary[j] = i * rowNum + j;
            snprintf_s(name[j], sizeof(name[j]), sizeof(name[j]) - 1, "name_%d_%d\0", i * rowNum, j);
        }
        Data data = {.name = name, .age = age, .id = id, .worktime = worktime, .salary = salary, .rowNum = rowNum};
        BulkInsertDataForOffset(stmt, data, "tablequery3");
    }
    int64_t idRes = 0;
    int64_t worktimeRes = 0;
    int64_t salaryRes = 0;
    int64_t ageRes = 0;
    char nameRes[256] = {0};
    bool isNull = false;
    uint32_t i = 0;

    isNull = false;
    eof = false;
    uint32_t value = 0;

    char qryCommand1[150] = "select * from tablequery3 LIMIT 100 offset 94930;";
    char nameTarget[256] = {0};
    ret = GmcExecDirect(stmt, qryCommand1, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)100);
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &idRes, &propSize, &isNull);
        EXPECT_EQ(idRes, i + 94930);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(worktimeRes, i + 94930);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &salaryRes, &propSize, &isNull);
        EXPECT_EQ(salaryRes, i + 94930);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ageRes, i + 94930);
        propSize = 256;
        snprintf_s(nameTarget, sizeof(nameTarget), sizeof(nameTarget) - 1, "name_%d_%d\0", ((i + 94930) / 1000) * 1000,
            (i + 94930) % 1000);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        EXPECT_EQ(DbStrCmp(nameRes, nameTarget, false), 0);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)100);

    char qryCommand2[150] = "select * from tablequery3 LIMIT 100,9493;";
    ret = GmcExecDirect(stmt, qryCommand2, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)9493);

    char qryCommand3[150] = "select * from tablequery3 LIMIT 94930,101;";
    ret = GmcExecDirect(stmt, qryCommand3, 150);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)101);

    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpOffset6)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    Status ret;
    InitConnAndStmt(&conn, &stmt);

    CreateTableForOffset(stmt, "tablequery3");
    char dropCommand[200] = "drop table tablequery3;";
    uint32_t rowNum = 1000;
    uint32_t batch = 200;
    char name[rowNum][256];
    int64_t age[rowNum];
    int64_t id[rowNum];
    int64_t worktime[rowNum];
    int64_t salary[rowNum];

    for (int i = 0; i < batch; i++) {
        for (int j = 0; j < rowNum; j++) {
            age[j] = i * rowNum + j;
            id[j] = i * rowNum + j;
            worktime[j] = i * rowNum + j;
            salary[j] = i * rowNum + j;
            snprintf_s(name[j], sizeof(name[j]), sizeof(name[j]) - 1, "name_%d_%d\0", i * rowNum, j);
        }
        Data data = {.name = name, .age = age, .id = id, .worktime = worktime, .salary = salary, .rowNum = rowNum};
        BulkInsertDataForOffset(stmt, data, "tablequery3");
    }
    uint32_t value = 0;

    char qryCommand1[150] = {0};
    for (int j = 0; j < 100; j++) {
        snprintf_s(qryCommand1, sizeof(qryCommand1), sizeof(qryCommand1) - 1,
            "select * from tablequery3 LIMIT 1001 offset %d\0;", j * 1000 + 100000);
        ret = GmcExecDirect(stmt, qryCommand1, 150);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
        EXPECT_EQ(ret, GMERR_OK);
        if (j == 99) {
            EXPECT_EQ(value, (uint32_t)1000);
        } else {
            EXPECT_EQ(value, (uint32_t)1001);
        }
    }

    char qryCommand2[150] = {0};
    for (int j = 0; j < 100; j++) {
        snprintf_s(qryCommand2, sizeof(qryCommand2), sizeof(qryCommand2) - 1,
            "select age, length(name) from tablequery3 LIMIT %d,1001\0;", j * 1000 + 100000);
        ret = GmcExecDirect(stmt, qryCommand2, 150);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
        EXPECT_EQ(ret, GMERR_OK);
        if (j == 99) {
            EXPECT_EQ(value, (uint32_t)1000);
        } else {
            EXPECT_EQ(value, (uint32_t)1001);
        }
    }

    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

Status BuiltInInsert(GmcStmtT *stmt, char *tableName)
{
    uint32_t rowNum = 3;
    int64_t id[] = {29, 30, 19};
    int64_t time[] = {24, 24, 25};
    char name[][64] = {"david", "nut", "bob"};
    char ip[][33] = {"10101040", "10101040", "98765432"};
    uint8_t message[][160] = {"0071 3520", "0010 0000", ""};
    char text[][64] = {"test data of the text type:0", "test data of the text type:1", "test data of the text type:2"};
    char *newText[rowNum];
    uint8_t *newMessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newText[j] = text[j];
        newMessage[j] = message[j];
    }
    int64_t id1[] = {29, 30, 19};
    int64_t time1[] = {24, 24, 25};
    Status ret = GMERR_OK;
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 64, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ip, 33, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, newMessage, sizeof(newMessage[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_STRING, newText, sizeof(newText[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, id1, sizeof(id1[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, time1, sizeof(time1[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    return ret;
}

TEST_F(StTsQueryGmc, BuiltInSample)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tablebuilt1(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, id1 integer, time1 integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char ddlCommand2[500] = "select first(id), last(id), first(name), last(name) from tablebuilt1";
    char tableName[30] = "tablebuilt1";
    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = BuiltInInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand2, 500);
    EXPECT_EQ(ret, GMERR_OK);
    int64_t firstResult = 29;
    int64_t lastResult = 19;
    int64_t firstRes = 0;
    int64_t lastRes = 0;
    bool eof = false;
    bool isNull = false;
    uint32_t propSize = 160;
    char firstName[64] = {0};
    char lastName[64] = {0};
    char firstNameRes[64] = {"david"};
    char lastNameRes[64] = {"bob"};
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &firstRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(firstRes, firstResult);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &lastRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(lastRes, lastResult);
        propSize = 64;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, firstName, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_STREQ(firstName, firstNameRes);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, lastName, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_STREQ(lastName, lastNameRes);
    }
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, BuiltInSample2)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tablebuilt2(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, id1 integer, time1 integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char ddlCommand2[500] = "select first(ip), last(ip) from tablebuilt2";
    char tableName[30] = "tablebuilt2";
    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = BuiltInInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand2, 500);
    EXPECT_EQ(ret, GMERR_OK);
    bool eof = false;
    bool isNull = false;
    uint32_t propSize = 33;
    char firsIp[64] = {0};
    char lastIp[64] = {0};
    char firstIpRes[64] = {"10101040"};
    char lastIpRes[64] = {"98765432"};
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, firsIp, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_STREQ(firsIp, firstIpRes);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, lastIp, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_STREQ(lastIp, lastIpRes);
    }
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, BuiltInSample3)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tablebuilt3(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, id1 integer, time1 integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char ddlCommand2[500] = "select first(description), last(description) from tablebuilt3";
    char tableName[30] = "tablebuilt3";
    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = BuiltInInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand2, 500);
    EXPECT_EQ(ret, GMERR_OK);
    bool eof = false;
    bool isNull = false;
    uint32_t propSize = 33;
    char firstText[64] = {0};
    char lastText[64] = {0};
    char firstTextRes[64] = {"test data of the text type:0"};
    char lastTextRes[64] = {"test data of the text type:2"};
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, firstText, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_STREQ(firstText, firstTextRes);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, lastText, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_STREQ(lastText, lastTextRes);
    }
    UnInitConnAndStmt(conn, stmt);
}

// basic
TEST_F(StTsQueryGmc, QueryOpAvgFuncSuccess1)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    uint32_t propSize = 0;
    bool eof = false;
    Status ret;
    InitConnAndStmt(&conn, &stmt);

    char dropCommand[200] = "drop table tablequery3;";
    uint32_t rowNum = 9;
    char name[][256] = {"david", "jack", "anna", "nut", "bob", "olivia", "tim", "lucy", "maria"};
    int64_t age[] = {30, 30, 19, 19, 23, 23, 24, 24, 23};
    int64_t id[] = {1, 2, 4, 9, 8, 14, 11, 16, 19};
    int64_t worktime[] = {24, 18, 25, 11, 12, 10, 17, 26, 9};
    int64_t salary[] = {10000, 20000, 4000, 30000, 31000, 10000, 5000, 25000, 31000};

    Data data = {.name = name, .age = age, .id = id, .worktime = worktime, .salary = salary, .rowNum = rowNum};
    CreateTableAndBulkInsert(stmt, data, "tablequery3");

    char qryCommand[300] = "select age, worktime, avg(worktime), avg(salary), avg(worktime+salary), avg(worktime + 1), "
                           "avg(1 + worktime), avg(10), AvG(salary), avg(1+3) from "
                           "tablequery3 group by age order by age;";
    ret = GmcExecDirect(stmt, qryCommand, 300);
    EXPECT_EQ(ret, GMERR_OK);

    // age, worktime, avg(worktime), avg(salary), avg(worktime+salary), avg(worktime + 1), avg(10), avg(1+3)
    // 19, 25, 18, 17000, 17018, 19, 10, 4
    // 23, 12, 10.333333, 24000, 24010.333333, 11.333333, 10, 4
    // 24, 17, 21.5, 15000, 15021.5, 22.5, 10, 4
    // 30, 24, 21, 15000, 15021, 22, 10, 4
    int64_t rightAge[] = {19, 23, 24, 30};
    int64_t rightWorktime[] = {25, 12, 17, 24};
    double rightAvgWorktime[] = {18.000000, 10.333333, 21.500000, 21.000000};
    double rightAvgSalary[] = {17000, 24000, 15000, 15000};
    double rightAvgAll[] = {17018.000000, 24010.333333, 15021.500000, 15021.000000};
    double rightAvgPlus[] = {19, 11.333333, 22.5, 22};
    int64_t ageRes = 0;
    int64_t worktimeRes = 0;
    double avgWorTimeRes = 0.0;
    double avgSalaryRes = 0.0;
    double avgSumRes = 0.0;
    double avgSum1Res = 0.0;
    double avg10Res = 0.0;
    double avg1Plus3Res = 0.0;
    bool isNull = false;

    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ageRes, rightAge[i]);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(worktimeRes, rightWorktime[i]);
        propSize = sizeof(double);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &avgWorTimeRes, &propSize, &isNull);
        EXPECT_NEAR(avgWorTimeRes, rightAvgWorktime[i], 1e-4);
        propSize = sizeof(double);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &avgSalaryRes, &propSize, &isNull);
        EXPECT_NEAR(avgSalaryRes, rightAvgSalary[i], 1e-4);
        propSize = sizeof(double);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &avgSumRes, &propSize, &isNull);
        EXPECT_NEAR(avgSumRes, rightAvgAll[i], 1e-4);
        propSize = sizeof(double);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, &avgSum1Res, &propSize, &isNull);
        EXPECT_NEAR(avgSum1Res, rightAvgPlus[i], 1e-4);
        propSize = sizeof(double);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 6, &avgSum1Res, &propSize, &isNull);
        EXPECT_NEAR(avgSum1Res, rightAvgPlus[i], 1e-4);
        propSize = sizeof(double);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 7, &avg10Res, &propSize, &isNull);
        EXPECT_NEAR(avg10Res, 10, 1e-4);
        propSize = sizeof(double);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 8, &avgSalaryRes, &propSize, &isNull);
        EXPECT_NEAR(avgSalaryRes, rightAvgSalary[i], 1e-4);
        propSize = sizeof(double);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 9, &avg1Plus3Res, &propSize, &isNull);
        EXPECT_NEAR(avg1Plus3Res, 4, 1e-4);
        cout << "age = " << ageRes << " worktime = " << worktimeRes << ", avg(worktime) = " << avgWorTimeRes
             << ", avg(salary) = " << avgSalaryRes << ", avg(worktime+salary) = " << avgSumRes
             << ", avg(worktime+1) = " << avgSum1Res << ", avg(10) = " << avg10Res << ", avg(1+3) = " << avg1Plus3Res
             << endl;
        i++;
    }
    EXPECT_EQ(i, (uint32_t)4);

    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

Status AvgInsert(GmcStmtT *stmt, char *tableName)
{
    uint32_t rowNum = 9;
    int64_t id[] = {1, 2, 4, 9, 8, 14, 11, 16, 19};
    int64_t time[] = {30, 30, 19, 19, 23, 23, 24, 24, 23};
    char name[][64] = {"david", "jack", "anna", "nut", "bob", "olivia", "tim", "lucy", "maria"};
    char ip[][33] = {
        "10101040", "10101040", "98765432", "12343454", "12454321", "50680938", "32039203", "23252124", "12215141"};
    uint8_t message[][160] = {
        "0071 3520", "0010 0000", "", "0000 0010", "0012 2331", "0043 2122", "0000 0000", "0001 0001", "1001 0101"};
    char text[][64] = {"test data of the text type:0", "test data of the text type:1", "test data of the text type:2",
        "test data of the text type:3", "test data of the text type:4", "test data of the text type:5",
        "test data of the text type:6", "test data of the text type:7", "test data of the text type:8"};
    char *newText[rowNum];
    uint8_t *newMessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newText[j] = text[j];
        newMessage[j] = message[j];
    }
    int64_t id1[] = {29, 30, 19, 18, 17, 16, 15, 14, 13};
    int64_t time1[] = {24, 18, 25, 11, 12, 10, 17, 26, 9};
    Status ret = GMERR_OK;
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 64, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ip, 33, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, newMessage, sizeof(newMessage[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_STRING, newText, sizeof(newText[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, id1, sizeof(id1[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, time1, sizeof(time1[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    return ret;
}

TEST_F(StTsQueryGmc, QueryOpAvgFuncSuccess2)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tablequery3(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, id1 integer, time1 integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char tableName[30] = "tablequery3";
    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = AvgInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    double avgIdRes = 0.0;
    double avgIdSumRes = 0.0;
    uint32_t propSize = 0;
    bool eof = false;
    bool isNull = false;
    uint32_t i = 0;
    char qryCommand[150] = "select id, avg(id), avg(id + time1) from tablequery3;";
    ret = GmcExecDirect(stmt, qryCommand, 150);
    EXPECT_EQ(ret, GMERR_OK);
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(double);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &avgIdRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(double);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &avgIdSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        switch (i) {
            case 0:
                EXPECT_EQ(idRes, 1);
                EXPECT_NEAR(avgIdRes, 9.33333, 1e-4);
                EXPECT_NEAR(avgIdSumRes, 26.2222, 1e-4);
                cout << "id = " << idRes << ", avg(id) = " << avgIdRes << ", avg(id + time1) = " << avgIdSumRes << endl;
                break;
            default:
                ASSERT_EQ(1, 0);
                break;
        }
        i++;
    }
    EXPECT_EQ(i, 1);

    eof = false;
    isNull = false;
    i = 0;
    char qryCommand2[150] = "select id, avg(id), avg(id + time1) from tablequery3 where id > 10;";
    ret = GmcExecDirect(stmt, qryCommand2, 150);
    EXPECT_EQ(ret, GMERR_OK);
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(double);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &avgIdRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(double);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &avgIdSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        switch (i) {
            case 0:
                EXPECT_EQ(idRes, 14);
                EXPECT_NEAR(avgIdRes, 15, 1e-4);
                EXPECT_NEAR(avgIdSumRes, 30.5, 1e-4);
                cout << "id = " << idRes << ", avg(id) = " << avgIdRes << ", avg(id + time1) = " << avgIdSumRes << endl;
                break;
            default:
                ASSERT_EQ(1, 0);
                break;
        }
        i++;
    }
    EXPECT_EQ(i, 1);
    char dropCommand[200] = "drop table tablequery3;";
    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpAvgFuncSuccess3)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tablequery3(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, id1 integer, time1 integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char tableName[30] = "tablequery3";
    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = AvgInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    double avgIdRes = 0.0;
    double avgIdSumRes = 0.0;
    uint32_t propSize = 0;
    int64_t sumRes = 0;
    int64_t countRes = 0;
    int64_t lengthRes = 0;
    bool eof = false;
    bool isNull = false;
    uint32_t i = 0;
    char qryCommand[150] =
        "select id, avg(id), avg(id + time1), sum(id), count(*), length(description) from tablequery3 where id > 10;";
    ret = GmcExecDirect(stmt, qryCommand, 150);
    EXPECT_EQ(ret, GMERR_OK);
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(double);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &avgIdRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(double);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &avgIdSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &sumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &countRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, &lengthRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        switch (i) {
            case 0:
                EXPECT_EQ(idRes, 14);
                EXPECT_NEAR(avgIdRes, 15, 1e-4);
                EXPECT_NEAR(avgIdSumRes, 30.5, 1e-4);
                EXPECT_EQ(sumRes, 60);
                EXPECT_EQ(countRes, 4);
                EXPECT_EQ(lengthRes, 28);
                break;
            default:
                ASSERT_EQ(1, 0);
                break;
        }
        i++;
    }
    EXPECT_EQ(i, 1);
    char dropCommand[200] = "drop table tablequery3;";
    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpAvgFuncSuccess4)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tablequery3(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, id1 integer, time1 integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char tableName[30] = "tablequery3";
    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = AvgInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t timeRes = 0;
    double avgTimeRes = 0.0;
    double avgTimeSumRes = 0.0;
    uint32_t propSize = 0;
    int64_t sumRes = 0;
    int64_t countRes = 0;
    int64_t lengthRes = 0;
    bool eof = false;
    bool isNull = false;
    uint32_t i = 0;
    char qryCommand[150] = "select time, avg(time), avg(id + time), sum(id), count(ip), length(description) from "
                           "tablequery3 where id > 0 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand, 150);

    // 19, 19, 25.5, 13, 2, 28
    // 23, 23, 36.6666, 41, 3, 28
    // 24, 24, 37.5, 27, 2, 28
    // 30, 30, 31.5, 3, 2, 28
    EXPECT_EQ(ret, GMERR_OK);
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        if (eof || ret != GMERR_OK) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &timeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(double);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &avgTimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(double);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &avgTimeSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &sumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &countRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, &lengthRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        switch (i) {
            case 0:
                EXPECT_EQ(timeRes, 19);
                EXPECT_NEAR(avgTimeRes, 19, 1e-4);
                EXPECT_NEAR(avgTimeSumRes, 25.5, 1e-4);
                EXPECT_EQ(sumRes, 13);
                EXPECT_EQ(countRes, 2);
                EXPECT_EQ(lengthRes, 28);
                break;
            case 1:
                EXPECT_EQ(timeRes, 23);
                EXPECT_NEAR(avgTimeRes, 23, 1e-4);
                EXPECT_NEAR(avgTimeSumRes, 36.6666, 1e-4);
                EXPECT_EQ(sumRes, 41);
                EXPECT_EQ(countRes, 3);
                EXPECT_EQ(lengthRes, 28);
                break;
            case 2:
                EXPECT_EQ(timeRes, 24);
                EXPECT_NEAR(avgTimeRes, 24, 1e-4);
                EXPECT_NEAR(avgTimeSumRes, 37.5, 1e-4);
                EXPECT_EQ(sumRes, 27);
                EXPECT_EQ(countRes, 2);
                EXPECT_EQ(lengthRes, 28);
                break;
            case 3:
                EXPECT_EQ(timeRes, 30);
                EXPECT_NEAR(avgTimeRes, 30, 1e-4);
                EXPECT_NEAR(avgTimeSumRes, 31.5, 1e-4);
                EXPECT_EQ(sumRes, 3);
                EXPECT_EQ(countRes, 2);
                EXPECT_EQ(lengthRes, 28);
                break;
            default:
                ASSERT_EQ(1, 0);
                break;
        }
        i++;
    }
    EXPECT_EQ(i, 4);

    char dropCommand[200] = "drop table tablequery3;";
    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpAvgFuncError1)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tablequery3(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, id1 integer, time1 integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char tableName[30] = "tablequery3";
    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = AvgInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    char qryCommand1[150] = "select avg(ip) from tablequery3";
    ret = GmcExecDirect(stmt, qryCommand1, 150);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    char qryCommand101[150] = "select avg(message) from tablequery3";
    ret = GmcExecDirect(stmt, qryCommand101, 150);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    char qryCommand102[150] = "select avg(description) from tablequery3";
    ret = GmcExecDirect(stmt, qryCommand102, 150);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    char qryCommand2[150] = "select avg(name) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand2, 150);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    char qryCommand3[150] = "select avg(id, time) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand3, 150);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    char qryCommand4[150] = "select avg('a') from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand4, 150);
    EXPECT_EQ(ret, GMERR_DATATYPE_MISMATCH);

    char qryCommand401[150] = "select avg(1) from tablequery3;";
    ret = GmcExecDirect(stmt, qryCommand401, 150);
    EXPECT_EQ(ret, GMERR_OK);

    char qryCommand5[150] = "select avg() from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand5, 150);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    char qryCommand6[150] = "select avg(*) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand6, 150);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    char qryCommand7[150] = "select avg(22.22) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand7, 150);
    EXPECT_EQ(ret, GMERR_DATATYPE_MISMATCH);

    char qryCommand701[150] = "select avg(-11) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand701, 150);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    char qryCommand10[150] = "select avg(id4) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand10, 150);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    char qryCommand11[150] = "select avgavg(id) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand11, 150);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    char qryCommand12[150] = "select avg(id time) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand12, 150);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    char dropCommand[200] = "drop table tablequery3;";
    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpAvgFuncError2)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tablequery3(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, id1 integer, time1 integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char tableName[30] = "tablequery3";
    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = AvgInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    char qryCommand1[150] = "select avg(id + 'aa') from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand1, 150);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    char qryCommand2[150] = "select avg(id + 1.1) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand2, 150);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    char qryCommand3[150] = "select avg(id - id1) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand3, 150);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    char qryCommand4[150] = "select avg(id * id1) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand4, 150);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    char qryCommand5[150] = "select avg(id \% id1) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand5, 150);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    char qryCommand6[150] = "select avg(id / id1) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand6, 150);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    char qryCommand7[150] = "select avg(id + id1 + time1) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand7, 150);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    char qryCommand8[150] = "select avg(id + ip) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand8, 150);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    char qryCommand9[150] = "select avg(id + name) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand9, 150);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    char qryCommand10[150] = "select avg(id + message) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand10, 150);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    char qryCommand11[150] = "select avg(id + description) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand11, 150);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    char qryCommand12[150] = "select avg(id - 1) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand12, 150);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    char dropCommand[200] = "drop table tablequery3;";
    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOpAvgFuncError3)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tablequery3(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, id1 integer, time1 integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char tableName[30] = "tablequery3";
    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = AvgInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    char qryCommand1[150] = "select sum(avg(id)) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand1, 150);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    char qryCommand2[150] = "select max(avg(id)) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand2, 150);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    char qryCommand3[150] = "select count(avg(id)) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand3, 150);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    char qryCommand4[150] = "select first(avg(id)) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand4, 150);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    char qryCommand5[150] = "select length(avg(id)) from tablequery3 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand5, 150);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    char qryCommand6[150] = "select max(id1) from tablequery3 where avg(id) > 0 group by time order by time;";
    ret = GmcExecDirect(stmt, qryCommand6, 150);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    char qryCommand7[150] = "select max(id1) from tablequery3 group by avg(id) order by time;";
    ret = GmcExecDirect(stmt, qryCommand7, 150);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    char dropCommand[200] = "drop table tablequery3;";
    ret = GmcExecDirect(stmt, dropCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOrderByDupRm1)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableOrderByDupRm1(age integer, worktime integer, id blob) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] =
        "select worktime, sum(worktime) from tableOrderByDupRm1 group by worktime order by sum(worktime);";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = TS_CHAR_MAX_LEN;
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    uint8_t id[][TS_CHAR_MAX_LEN] = {"0071 3520", "abc\0def", ""};
    uint8_t *newId[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newId[j] = id[j];
    }
    uint32_t idActLen[rowNum] = {9, 7, 0};
    char tableName[30] = "tableOrderByDupRm1";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newId, sizeof(newId[0]), idActLen);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t workTimeRes = 0;
    int64_t workTimeSum = 0;
    int64_t workTimeResult[] = {25, 24};
    int64_t workTimeSumResult[] = {25, 48};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &workTimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(workTimeRes, workTimeResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &workTimeSum, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(workTimeSum, workTimeSumResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOrderByDupRm2)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableOrderByDupRm2(age integer, worktime integer, id blob) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] =
        "select worktime, sum(worktime) from tableOrderByDupRm2 group by worktime order by sum(age);";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = TS_CHAR_MAX_LEN;
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    uint8_t id[][TS_CHAR_MAX_LEN] = {"0071 3520", "abc\0def", ""};
    uint8_t *newId[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newId[j] = id[j];
    }
    uint32_t idActLen[rowNum] = {9, 7, 0};
    char tableName[30] = "tableOrderByDupRm2";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newId, sizeof(newId[0]), idActLen);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t workTimeRes = 0;
    int64_t workTimeSum = 0;
    int64_t workTimeResult[] = {25, 24};
    int64_t workTimeSumResult[] = {25, 48};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &workTimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(workTimeRes, workTimeResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &workTimeSum, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(workTimeSum, workTimeSumResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOrderByDupRm3)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableOrderByDupRm3(age integer, worktime integer, id blob) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select worktime, sum(worktime) from tableOrderByDupRm3 group by worktime order by age;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = TS_CHAR_MAX_LEN;
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    uint8_t id[][TS_CHAR_MAX_LEN] = {"0071 3520", "abc\0def", ""};
    uint8_t *newId[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newId[j] = id[j];
    }
    uint32_t idActLen[rowNum] = {9, 7, 0};
    char tableName[30] = "tableOrderByDupRm3";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newId, sizeof(newId[0]), idActLen);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t workTimeRes = 0;
    int64_t workTimeSum = 0;
    int64_t workTimeResult[] = {25, 24};
    int64_t workTimeSumResult[] = {25, 48};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &workTimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(workTimeRes, workTimeResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &workTimeSum, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(workTimeSum, workTimeSumResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOrderByDupRm4)
{
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(100);
    StartDbServer((char *)g_cfgHighCardPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(100);
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableOrderByDupRm4(age integer, worktime integer, id blob) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] =
        "select worktime, sum(worktime) from tableOrderByDupRm4 group by worktime order by sum(worktime);";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = TS_CHAR_MAX_LEN;
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    uint8_t id[][TS_CHAR_MAX_LEN] = {"0071 3520", "abc\0def", ""};
    uint8_t *newId[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newId[j] = id[j];
    }
    uint32_t idActLen[rowNum] = {9, 7, 0};
    char tableName[30] = "tableOrderByDupRm4";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newId, sizeof(newId[0]), idActLen);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t workTimeRes = 0;
    int64_t workTimeSum = 0;
    int64_t workTimeResult[] = {25, 24};
    int64_t workTimeSumResult[] = {25, 48};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &workTimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(workTimeRes, workTimeResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &workTimeSum, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(workTimeSum, workTimeSumResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOrderByDupRm5)
{
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(100);
    StartDbServer((char *)g_cfgHighCardPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(100);
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableOrderByDupRm5(age integer, worktime integer, id blob) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] =
        "select worktime, sum(worktime) from tableOrderByDupRm5 group by worktime order by sum(age);";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = TS_CHAR_MAX_LEN;
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    uint8_t id[][TS_CHAR_MAX_LEN] = {"0071 3520", "abc\0def", ""};
    uint8_t *newId[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newId[j] = id[j];
    }
    uint32_t idActLen[rowNum] = {9, 7, 0};
    char tableName[30] = "tableOrderByDupRm5";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newId, sizeof(newId[0]), idActLen);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t workTimeRes = 0;
    int64_t workTimeSum = 0;
    int64_t workTimeResult[] = {25, 24};
    int64_t workTimeSumResult[] = {25, 48};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &workTimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(workTimeRes, workTimeResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &workTimeSum, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(workTimeSum, workTimeSumResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, QueryOrderByDupRm6)
{
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(100);
    StartDbServer((char *)g_cfgHighCardPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(100);
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableOrderByDupRm6(age integer, worktime integer, id blob) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select worktime, sum(worktime) from tableOrderByDupRm6 group by worktime order by age;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = TS_CHAR_MAX_LEN;
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    uint8_t id[][TS_CHAR_MAX_LEN] = {"0071 3520", "abc\0def", ""};
    uint8_t *newId[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newId[j] = id[j];
    }
    uint32_t idActLen[rowNum] = {9, 7, 0};
    char tableName[30] = "tableOrderByDupRm6";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newId, sizeof(newId[0]), idActLen);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t workTimeRes = 0;
    int64_t workTimeSum = 0;
    int64_t workTimeResult[] = {25, 24};
    int64_t workTimeSumResult[] = {25, 48};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &workTimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(workTimeRes, workTimeResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &workTimeSum, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(workTimeSum, workTimeSumResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, BuiltInPushDown)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableBuiltIn1(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select id, sum(age), sum(worktime) from tableBuiltIn1;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = 8;
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {1, 2, 3};
    char tableName[30] = "tableBuiltIn1";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t ageRes = 0;
    int64_t idRes = 0;
    int64_t workRes = 0;
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, 1);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, 78);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &workRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(workRes, 73);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, BuiltInPushDown2)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableBuiltIn2(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select sum(age), sum(worktime) from tableBuiltIn2 order by id;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = 8;
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {1, 2, 3};
    char tableName[30] = "tableBuiltIn2";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t ageRes = 0;
    int64_t workRes = 0;
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, 78);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &workRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(workRes, 73);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, BuiltInPushDown3)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableBuiltIn3(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select sum(age), sum(worktime) from tableBuiltIn3 where id >= 2;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = 8;
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {1, 2, 3};
    char tableName[30] = "tableBuiltIn3";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t ageRes = 0;
    int64_t workRes = 0;
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, 49);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &workRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(workRes, 49);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    DropTable(stmt, tableName);
    UnInitConnAndStmt(conn, stmt);
}

TEST_F(StTsQueryGmc, testTsInterfaceEmpty)
{
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt = NULL;
    GmcBatchRetT *batchRet = NULL;
    GmcConnT *conn = NULL;
    Status ret = GmcBatchAddDML(batch, stmt);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    uint32_t *totalNum = NULL;
    uint32_t *successNum = NULL;
    ret = GmcBatchDeparseRet(batchRet, totalNum, successNum);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcBatchExecute(batch, batchRet);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcBatchExecuteAsync(batch, NULL, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcBatchGetResIdInfo(batchRet, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcBatchGetResIdNum(batchRet, NULL, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcBatchOptionInit(NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcBatchOptionSetBufLimitSize(NULL, 0);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcBatchOptionSetExecOrder(NULL, 0);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcBatchPrepare(conn, NULL, &batch);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcBatchReset(batch);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    GmcBatchUnbindStmt(batch, stmt);

    ret = GmcBindExtResPool(stmt, NULL, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcBindResPoolToLabel(stmt, NULL, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcCreateResPool(stmt, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcDestroyResPool(stmt, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcGetCountResource(0, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcGetPoolIdResource(0, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcGetResIdInfo(stmt, NULL, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcGetResIdNum(stmt, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcGetResPool(stmt, NULL, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcGetStartIdxResource(0, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcSetCountResource(0, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcSetPoolIdResource(0, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcSetStartIdxResource(0, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcUnbindExtResPool(stmt, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcUnbindResPoolFromLabel(stmt, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcCreateNamespace(stmt, NULL, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcDropNamespace(stmt, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcKvCreateTable(stmt, NULL, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcKvDropTable(stmt, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcKvGet(stmt, NULL, 0, NULL, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcKvGetFromStmt(stmt, NULL, NULL, NULL, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcKvIsExist(stmt, NULL, 0, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcKvPrepareStmtByLabelName(stmt, "work work");
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcKvRemove(stmt, NULL, 0);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcKvSet(stmt, NULL, 0, NULL, 0);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcKvTableRecordCount(stmt, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcKvTruncateTable(stmt, "work work");
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    GmcNodeT *node = NULL;
    ret = GmcGetRootNode(stmt, &node);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    GmcIndexKeyT *key = NULL;
    ret = GmcNodeAllocKey(node, "name", &key);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcNodeAppendElement(node, &node);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcNodeClear(node);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcNodeFreeKey(key);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcNodeGetChild(node, "hard hard", &node);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcNodeGetElementByIndex(node, 0, &node);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcNodeGetElementByKey(node, key, &node);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcNodeGetElementCount(node, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    bool isNull = false;
    ret = GmcNodeGetPropertyById(node, 0, NULL, 0, &isNull);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcNodeGetPropertyByName(node, "name", NULL, 0, &isNull);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcNodeGetPropertySizeById(node, 0, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcNodeGetPropertySizeByName(node, "name", NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcNodeRemoveElementByIndex(node, 0);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcNodeRemoveElementByKey(node, key);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    GmcDataTypeE type = GMC_DATATYPE_CHAR;

    ret = GmcNodeSetKeyValue(key, 0, type, NULL, 0);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcNodeSetPropertyById(node, 0, type, NULL, 0);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcNodeSetPropertyByName(node, "name", type, NULL, 0);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    char *jsonStr = NULL;
    ret = GmcDumpVertexToJson(stmt, 0, &jsonStr);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    GmcFreeJsonStr(stmt, jsonStr);

    ret = GmcSetVertexByJson(stmt, 0, jsonStr);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    GmcSeriT *seri = NULL;
    ret = GmcSetVertexWithBuf(stmt, seri);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcGetVertexBuf(stmt, 1, seri, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    GmcDeseriT *deseri = NULL;
    ret = GmcGetVertexDeseri(stmt, deseri);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcSetIndexKeyWithBuf(stmt, 0, seri);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcNodeGetSuperfieldById(node, 1, NULL, 1);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcNodeGetSuperfieldByName(node, "name", NULL, 0);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcReuseNodeAppendElement(node, &node);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    uint8_t *nodeBuf = NULL;
    uint32_t *nodeBufLen = NULL;
    ret = GmcNodeGetNodeDataBuf(node, &nodeBuf, nodeBufLen, &nodeBuf, nodeBufLen);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcGetNodeWithKeyBuf(node, "name", 1, seri, &node);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcNodeGetNodeRecordBuf(node, &nodeBuf, nodeBufLen);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcNodeGetNextElement(node, &node);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcVertexGetRecordBuf(stmt, &nodeBuf, nodeBufLen);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcVertexGetDataBuf(stmt, &nodeBuf, nodeBufLen, &nodeBuf, nodeBufLen);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    const void *value = NULL;
    ret = GmcNodeSetSuperfieldById(node, 1, value, 1);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = GmcClearNamespace(stmt, NULL);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
}

TEST_F(StTsQueryGmc, scanFilterNew)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableBuiltIn3(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select age,worktime from tableBuiltIn3 where worktime >= 3700;";
    char qryCommand2[200] = "select age,worktime from tableBuiltIn3 where age = 30;";
    char qryCommand3[200] = "select age,worktime from tableBuiltIn3 where age = 19;";
    char qryCommand4[200] = "select age,worktime from tableBuiltIn3 where worktime >= 3700 limit 2;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {1, 2, 3};
    char tableName[30] = "tableBuiltIn3";

    InitConnAndStmt(&conn, &stmt);
    Status ret = GMERR_OK;
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    for (uint32_t i = 0; i < 3; ++i) {
        age[i] += 100;
        worktime[i] += 3700;
        id[i] += 10000;
    }
    worktime[0] += 3700;
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    EXPECT_EQ(GmcExecDirect(stmt, qryCommand1, 200), GMERR_OK);
    EXPECT_EQ(GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t)), GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    EXPECT_EQ(GmcExecDirect(stmt, qryCommand2, 200), GMERR_OK);
    EXPECT_EQ(GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t)), GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    EXPECT_EQ(GmcExecDirect(stmt, qryCommand3, 200), GMERR_OK);
    EXPECT_EQ(GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t)), GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    EXPECT_EQ(GmcExecDirect(stmt, qryCommand4, 200), GMERR_OK);
    EXPECT_EQ(GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t)), GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);

    DropTable(stmt, tableName);
    UnInitConnAndStmt(conn, stmt);
}
