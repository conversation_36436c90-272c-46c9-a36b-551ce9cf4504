/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: st for ts col operate
 * Author: chenchengjun
 * Create: 2024/10/23
 */

#include <gtest/gtest.h>
#include "adpt_sleep.h"
#include "StartDbServer.h"
#include "InitClt.h"
#include "gmc.h"
#include "gmc_sql.h"
#include "dm_meta_prop_strudefs.h"
#include "clt_stmt.h"
#include "cpl_ts_build_op.h"
#include "gmc_sysview.h"

const static uint32_t cmdLen = 200;
const static uint32_t waitTime = 1000;  // ms

const static char *g_cfgPersist = "ts/gmserver_ts.ini";
const static char *g_cfgHighCardPersist = "ts_regress/gmserver_ts_perf.ini";

class StTsColOpGmc : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("rm -rf /data/gmdb");
        DbSleep(100);
        StartDbServer((char *)g_cfgPersist, "usocket:/run/verona/unix_emserver");
        DbSleep(100);
    }
    static void TearDownTestCase()
    {
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("rm -rf /data/gmdb");
    }
};

TEST_F(StTsColOpGmc, ColOpAdd1)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOp(age integer, worktime integer, id blob) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select worktime + age, age from tableColOp;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = TS_CHAR_MAX_LEN;
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    uint8_t id[][TS_CHAR_MAX_LEN] = {"0071 3520", "0010 0000", ""};
    uint8_t *newId[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newId[j] = id[j];
    }
    char tableName[30] = "tableColOp";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newId, sizeof(newId[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t ageRes = 0;
    int64_t colSumRes = 0;
    int64_t colSumResult[] = {53, 54, 44};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, age[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAdd2)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOp2(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select worktime + age, age + id, age from tableColOp2;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColOp2";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t ageRes = 0;
    int64_t colSumRes1 = 0;
    int64_t colSumRes2 = 0;
    int64_t ageResult[] = {29, 30, 19};
    int64_t colSumResult1[] = {53, 54, 44};
    int64_t colSumResult2[] = {40, 39, 39};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes1, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes1, colSumResult1[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &colSumRes2, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes2, colSumResult2[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, ageResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAdd3)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOp3(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select worktime + age, age from tableColOp3 where id > 10;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColOp3";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t ageRes = 0;
    int64_t colSumRes1 = 0;
    int64_t ageResult[] = {29, 19};
    int64_t colSumResult1[] = {53, 44};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes1, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes1, colSumResult1[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, ageResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAdd4)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOp4(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select worktime + age, age from tableColOp4 order by age;";
    char qryCommand2[200] = "select worktime + age, age from tableColOp4 order by id;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColOp4";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    // select带a+b列，order by a;
    int64_t ageRes = 0;
    int64_t colSumRes1 = 0;
    int64_t ageResult1[] = {19, 29, 30};
    int64_t colSumResult1[] = {44, 53, 54};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes1, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes1, colSumResult1[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, ageResult1[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    // select带a+b列，order by c;
    int64_t ageResult2[] = {30, 29, 19};
    int64_t colSumResult2[] = {54, 53, 44};
    isNull = false;

    ret = GmcExecDirect(stmt, qryCommand2, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes1, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes1, colSumResult2[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, ageResult2[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAdd5)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOp5(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select worktime + age, id + 1 from tableColOp5;";
    char qryCommand2[200] = "select 1 + 1 from tableColOp5;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColOp5";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t colSumRes1 = 0;
    int64_t colSumRes2 = 0;
    int64_t colSumResult1[] = {53, 54, 44};
    int64_t colSumResult2[] = {12, 10, 21};
    bool isNull = false;

    // qryCommand1
    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes1, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes1, colSumResult1[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &colSumRes2, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes2, colSumResult2[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    // qryCommand2
    ret = GmcExecDirect(stmt, qryCommand2, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    i = 0;
    eof = false;
    int64_t constValue = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &constValue, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(constValue, 2);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAdd6)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOp6(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select worktime + age, sum(age) from tableColOp6 group by age order by age;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    int64_t age[] = {29, 30, 29};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColOp6";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t ageRes = 0;
    int64_t colSumRes1 = 0;
    int64_t ageResult[] = {58, 30};
    int64_t colSumResult1[] = {53, 54};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes1, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes1, colSumResult1[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, ageResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAdd7)
{
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(100);
    StartDbServer((char *)g_cfgHighCardPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(100);
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOp7(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select age + worktime, sum(age) from tableColOp7 group by age order by age;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColOp7";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t checkRes = 0;
    int64_t sumRes = 0;
    int64_t ageResult[] = {19, 29, 30};
    int64_t addResult[] = {44, 53, 54};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &checkRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(checkRes, addResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(sumRes, ageResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAdd8)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOp8(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select worktime + age, age + id, sum(age) from tableColOp8;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColOp8";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t ageRes = 0;
    int64_t colSumRes1 = 0;
    int64_t colSumRes2 = 0;
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes1, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes1, 53);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &colSumRes2, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes2, 40);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, 78);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAdd9)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOp9(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select worktime, age from tableColOp9 where id > 8 and id + age > 39;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColOp9";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t ageRes = 0;
    int64_t colSumRes1 = 0;
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes1, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes1, 24);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, 29);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAdd10)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOp10(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select worktime + age, age from tableColOp10 order by id";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    int64_t addResult[] = {54, 53, 44};
    int64_t ageResult[] = {30, 29, 19};
    char tableName[30] = "tableColOp10";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t ageRes = 0;
    int64_t checkRes = 0;
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &checkRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(checkRes, addResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, ageResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAdd11)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOp11(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[100] = "select first(worktime + age), age from tableColOp11 group by age";
    char qryCommand2[100] = "select last(worktime + age), age from tableColOp11 group by age";
    char qryCommand3[100] = "select max(worktime + age), age from tableColOp11 group by age";
    char qryCommand4[100] = "select min(worktime + age), age from tableColOp11 group by age";
    char qryCommand5[100] = "select colset(worktime + age), age from tableColOp11 group by age";
    char qryCommand6[100] = "select length(worktime + age), age from tableColOp11 group by age";
    char qryCommand7[100] = "select first(worktime + age), age from tableColOp11";
    char qryCommand8[100] = "select last(worktime + age), age from tableColOp11";
    char qryCommand9[100] = "select max(worktime + age), age from tableColOp11";
    char qryCommand10[100] = "select min(worktime + age), age from tableColOp11";
    char qryCommand11[100] = "select colset(worktime + age), age from tableColOp11";
    char qryCommand12[100] = "select length(worktime + age), age from tableColOp11";
    char qryCommand13[100] = "select max(worktime + age + id), age from tableColOp11 group by age";
    char qryCommand14[100] = "select sum(worktime + age + id), age from tableColOp11 group by age";
    char qryCommand15[100] = "select sum(age - worktime), age from tableColOp11 group by age";
    char qryCommand16[100] = "select max(worktime + age + id), age from tableColOp11";
    char qryCommand17[100] = "select sum(worktime + age + id), age from tableColOp11";
    char qryCommand18[100] = "select sum(age - worktime), age from tableColOp11";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    int64_t age[] = {29, 30, 29};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColOp11";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand2, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand3, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand4, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand5, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand6, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand7, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand8, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand9, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand10, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand11, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand12, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand13, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand14, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand15, 100);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    ret = GmcExecDirect(stmt, qryCommand16, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand17, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand18, 100);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAdd12)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOp12(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[100] = "select count(worktime + age), age from tableColOp12";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    int64_t age[] = {29, 30, 29};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColOp12";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t ageRes = 0;
    int64_t countRes = 0;
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &countRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(countRes, 3);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, 29);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAdd13)
{
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(100);
    StartDbServer((char *)g_cfgHighCardPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(100);
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOp13(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] =
        "select age + worktime, sum(age) from tableColOp13  where age + worktime > 50 group by age order by age;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {1, 2, 3};
    char tableName[30] = "tableColOp13";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t checkRes = 0;
    int64_t sumRes = 0;
    int64_t ageResult[] = {29, 30};
    int64_t addResult[] = {53, 54};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &checkRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(checkRes, addResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(sumRes, ageResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAdd14)
{
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(100);
    StartDbServer((char *)g_cfgHighCardPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(100);
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOp14(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select age + worktime, sum(age) from tableColOp14  where age + worktime > 10 group by "
                            "worktime order by worktime;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColOp14";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t checkRes = 0;
    int64_t sumRes = 0;
    int64_t ageResult[] = {59, 19};
    int64_t addResult[] = {53, 44};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &checkRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(checkRes, addResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(sumRes, ageResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpError)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOpError(age integer, worktime integer, id blob) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select worktime - age, age from tableColOpError;";
    char qryCommand2[200] = "select worktime * age, age from tableColOpError;";
    char qryCommand3[200] = "select worktime / age, age from tableColOpError;";
    char qryCommand4[200] = "select worktime + id, age from tableColOpError;";
    char qryCommand5[200] = "select worktime + age + 1, age from tableColOpError;";
    char qryCommand6[200] = "select worktime + sum(age), age from tableColOpError;";
    char qryCommand8[200] = "select worktime + age, sum(age) from tableColOpError group by worktime + age;";
    char qryCommand9[200] = "select age, first(age + id) from tableColOpError group by age;";
    char qryCommand10[200] = "select age, last(age + id) from tableColOpError group by age;";
    char qryCommand11[200] = "select age, max(age + id) from tableColOpError group by age;";
    char qryCommand12[200] = "select age, min(age + id) from tableColOpError group by age;";
    char qryCommand13[200] = "select age, sum(age + id) from tableColOpError group by age;";
    char qryCommand14[200] = "select age, length(age + id) from tableColOpError group by age;";
    char qryCommand15[200] = "select age, colset(age + id) from tableColOpError group by age;";
    char qryCommand16[200] = "select age, length(age + worktime) from tableColOpError;";
    char qryCommand17[200] = "select age, colset(age + worktime) from tableColOpError;";
    char qryCommand18[200] = "select age, age + worktime from tableColOpError where length(age + worktime) like '%';";

    uint32_t rowNum = 3;
    uint32_t value = 0;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    uint8_t id[][TS_CHAR_MAX_LEN] = {"0071 3520", "0010 0000", ""};
    uint8_t *newId[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newId[j] = id[j];
    }
    char tableName[30] = "tableColOpError";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newId, sizeof(newId[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    ret = GmcExecDirect(stmt, qryCommand2, 200);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    ret = GmcExecDirect(stmt, qryCommand3, 200);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    ret = GmcExecDirect(stmt, qryCommand4, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand5, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand6, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand8, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    ret = GmcExecDirect(stmt, qryCommand9, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand10, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand11, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand12, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand13, 200);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    ret = GmcExecDirect(stmt, qryCommand14, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand15, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand16, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand17, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand18, 200);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpError2)
{
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(100);
    StartDbServer((char *)g_cfgHighCardPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(100);
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOpError2(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[100] = "select first(worktime + age), age from tableColOpError2 group by age";
    char qryCommand2[100] = "select last(worktime + age), age from tableColOpError2 group by age";
    char qryCommand3[100] = "select max(worktime + age), age from tableColOpError2 group by age";
    char qryCommand4[100] = "select min(worktime + age), age from tableColOpError2 group by age";
    char qryCommand5[100] = "select colset(worktime + age), age from tableColOpError2 group by age";
    char qryCommand6[100] = "select length(worktime + age), age from tableColOpError2 group by age";
    char qryCommand7[100] = "select max(worktime + age + id), age from tableColOpError2 group by age";
    char qryCommand8[100] = "select sum(worktime + age + id), age from tableColOpError2 group by age";
    char qryCommand9[100] = "select sum(age - worktime), age from tableColOpError2 group by age";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    int64_t age[] = {29, 30, 29};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColOpError2";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand2, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand3, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand4, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand5, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand6, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand7, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand8, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand9, 100);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAddAlter)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOpAlter(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select worktime + addr, age from tableColOpAlter;";
    char altercmd1[200] = "alter table tableColOpAlter add column addr integer;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = TS_CHAR_MAX_LEN;
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {1, 1, 1};
    int64_t addr[] = {11, 9, 20};
    char tableName[30] = "tableColOpAlter";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, altercmd1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, addr, sizeof(addr[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t ageRes = 0;
    int64_t colSumRes = 0;
    int64_t colSumResult[] = {24, 24, 25, 35, 33, 45};
    int64_t ageResult[] = {29, 30, 19, 29, 30, 19};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)6);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, ageResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)6);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAddInsertInto)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char command1[200] = "create table tableColOpSelect(age integer, id integer) with (time_col = 'id', "
                         "interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char command2[200] = "create table tableColOpInsert(age integer, id integer) with (time_col = 'id', "
                         "interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char commandInsert[100] = "insert into tableColOpInsert select age + id, id from tableColOpSelect;";
    char commandQuery[100] = "select * from tableColOpInsert order by id;";
    char dropCmd1[70] = "drop table tableColOpInsert";
    uint32_t rowNum = 6;
    int64_t id[] = {1, 5400, 1800, 3000, 3610, 5000};
    int64_t age[] = {29, 30, 19, 33, 45, 46};
    int64_t idRes[] = {1, 1800, 3000, 3610, 5000, 5400};
    int64_t sumRes[] = {30, 1819, 3033, 3655, 5046, 5430};
    char tableName[30] = "tableColOpSelect";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command2, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, commandInsert, 100);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, commandQuery, 100);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)6);
    uint32_t i = 0;
    bool isNull = false;
    uint32_t propSize = 0;
    int64_t idActual = 0;
    int64_t sumActual = 0;
    bool eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &propSize);
        EXPECT_EQ(propSize, (uint32_t)8);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &sumActual, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idActual, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idActual, idRes[i]);
        EXPECT_EQ(sumActual, sumRes[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)6);
    ret = GmcExecDirect(stmt, dropCmd1, 70);
    EXPECT_EQ(ret, GMERR_OK);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

// a+b使用别名，查询成功，结果正确
TEST_F(StTsColOpGmc, ColOpAddAs)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOpAs(age integer, worktime integer, id blob) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand[200] = "select age + worktime as a from tableColOpAs;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = TS_CHAR_MAX_LEN;
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    uint8_t id[][TS_CHAR_MAX_LEN] = {"0071 3520", "0010 0000", ""};
    uint8_t *newId[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newId[j] = id[j];
    }
    char tableName[30] = "tableColOpAs";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newId, sizeof(newId[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t colSumRes = 0;
    int64_t colSumResult[] = {53, 54, 44};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAddAs2)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOpAs2(age integer, worktime integer, id blob) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand[200] = "select age + worktime as a from tableColOpAs2 group by age, worktime order by age;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = TS_CHAR_MAX_LEN;
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 26, 25};
    uint8_t id[][TS_CHAR_MAX_LEN] = {"0071 3520", "0010 0000", ""};
    uint8_t *newId[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newId[j] = id[j];
    }
    char tableName[30] = "tableColOpAs2";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newId, sizeof(newId[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t colSumRes = 0;
    int64_t colSumResult[] = {44, 53, 56};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAddAsAka)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOpAsAka(age integer, worktime integer, id blob) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select age + worktime as a from tableColOpAsAka where a + 1 > 2;";
    char qryCommand2[200] = "select age + worktime as a, a + 1 from tableColOpAsAka;";
    char qryCommand3[200] = "select age + worktime as '中文' from tableColOpAsAka group by '中文';";
    char qryCommand4[200] = "select age + worktime as a from tableColOpAsAka group by a;";
    char qryCommand5[200] = "select age + worktime as '中文' from tableColOpAsAka;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    uint8_t id[][TS_CHAR_MAX_LEN] = {"0071 3520", "0010 0000", ""};
    uint8_t *newId[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newId[j] = id[j];
    }
    char tableName[30] = "tableColOpAsAka";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newId, sizeof(newId[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    ret = GmcExecDirect(stmt, qryCommand2, 200);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    ret = GmcExecDirect(stmt, qryCommand3, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    ret = GmcExecDirect(stmt, qryCommand4, 200);
    EXPECT_EQ(ret, GMERR_UNDEFINE_COLUMN);
    ret = GmcExecDirect(stmt, qryCommand5, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAddGmcSysview1)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOpSysview1(age integer, worktime integer, id blob) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    uint8_t id[][TS_CHAR_MAX_LEN] = {"0071 3520", "0010 0000", ""};
    uint8_t *newId[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newId[j] = id[j];
    }
    char tableName[30] = "tableColOpSysview1";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, newId, sizeof(newId[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    // 函数内加法
    char *sqlCommand = "select count(age+1), sum(age+worktime) from tableColOpSysview1;";
    char gmsysview[] = "gmsysview";
    char sql[] = "-sql";
    char *argv[3] = {gmsysview, sql, sqlCommand};
    EXPECT_EQ(GmcSysview(3, argv, DbPrintfDefault), GMERR_OK);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAddGmcSysview2)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOpSysview2(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {1, 2, 3};
    char tableName[30] = "tableColOpSysview2";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    // 函数内加法
    char *sqlCommand =
        "select count(age+1), sum(age+worktime) from tableColOpSysview2 where age+worktime > 53 group by id, age, "
        "worktime order by sum(age+worktime), count(age+1);";
    char gmsysview[] = "gmsysview";
    char sql[] = "-sql";
    char *argv[3] = {gmsysview, sql, sqlCommand};
    EXPECT_EQ(GmcSysview(3, argv, DbPrintfDefault), GMERR_OK);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAddWhere1)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOpWhere1(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select worktime, age  from tableColOpWhere1 where id + age > worktime + age;";
    char qryCommand2[200] = "select worktime, age  from tableColOpWhere1 where worktime + age > worktime + age;";
    char qryCommand3[200] = "select worktime, age  from tableColOpWhere1 where 1 > worktime + age;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {25, 25, 26};
    char tableName[30] = "tableColOpWhere1";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    // where id + age > worktime + age;
    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    // where worktime + age >= worktime + age;
    ret = GmcExecDirect(stmt, qryCommand2, 200);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    // where 1 > worktime + age;
    ret = GmcExecDirect(stmt, qryCommand3, 200);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAddWhere2)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOpWhere2(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select worktime, age, id from tableColOpWhere2 where id > 8 or id + age > 39;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColOpWhere2";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t ageRes = 0;
    int64_t worktimeRes = 0;
    int64_t idRes = 0;
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(worktimeRes, worktime[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, age[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

// 对a+b使用参数化查询，使用等值和不等值过滤
TEST_F(StTsColOpGmc, ColOpAddWhere4)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOpWhere4(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select worktime, worktime+age from tableColOpWhere4 where id+age = 39;";
    char qryCommand2[200] = "select worktime, worktime+age from tableColOpWhere4 where id+age != 40;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColOpWhere4";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t worktimeRes = 0;
    int64_t sumRes1 = 0;
    int64_t worktimeColumn[] = {24, 25};
    int64_t sumColumn1[] = {54, 44};
    bool isNull = false;

    // cmd1
    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(worktimeRes, worktimeColumn[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumRes1, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(sumRes1, sumColumn1[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    // cmd2
    ret = GmcExecDirect(stmt, qryCommand2, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(worktimeRes, worktimeColumn[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumRes1, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(sumRes1, sumColumn1[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

// 对a+b使用参数化查询，使用范围查询
TEST_F(StTsColOpGmc, ColOpAddWhere5)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOpWhere5(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select worktime, worktime+age from tableColOpWhere5 where id+age > 38 and id+age<40;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColOpWhere5";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t worktimeRes = 0;
    int64_t sumRes1 = 0;
    int64_t worktimeColumn[] = {24, 25};
    int64_t sumColumn1[] = {54, 44};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(worktimeRes, worktimeColumn[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumRes1, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(sumRes1, sumColumn1[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAddInt64Max)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOp(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand1[200] = "select worktime+age  from tableColOp;";
    uint32_t rowNum = 1;
    uint32_t value = 0;
    int64_t age[] = {INT64_MAX};
    int64_t worktime[] = {1};
    int64_t id[] = {25};
    char tableName[30] = "tableColOp";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    // select INT64_MAX + 1 from tsTable;
    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_FIELD_OVERFLOW);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAddGroupBy1)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOpGroupBy1(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand[200] = "select worktime + age, sum(age) from tableColOpGroupBy1 group by id order by id;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    int64_t age[] = {29, 30, 29};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColOpGroupBy1";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t ageRes = 0;
    int64_t colSumRes1 = 0;
    int64_t ageResult[] = {30, 29, 29};
    int64_t colSumResult1[] = {54, 53, 54};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes1, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes1, colSumResult1[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(ageRes, ageResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAddGroupBy2)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOpGroupBy2(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand[200] = "select worktime + age from tableColOpGroupBy2 group by id, age order by id;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    int64_t age[] = {29, 30, 29};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColOpGroupBy2";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    int64_t colSumRes1 = 0;
    int64_t colSumResult1[] = {54, 53, 54};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes1, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes1, colSumResult1[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAddGroupBy3)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOpGroupBy3(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand[200] = "select worktime + age from tableColOpGroupBy3 group by id, age, worktime order by id;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    int64_t age[] = {29, 30, 29};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColOpGroupBy3";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t colSumRes1 = 0;
    int64_t colSumResult1[] = {54, 53, 54};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes1, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes1, colSumResult1[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAddCopyTo)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColAddCopyTo(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    int64_t age[] = {29, 30, 29};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColAddCopyTo";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    system("touch a.csv");
    char *query = "copy (select count(id), sum(age), age+worktime from tableColAddCopyTo where id+worktime>33 group by "
                  "id) to './a.csv';";
    ret = GmcExecDirect(stmt, query, 200);
    EXPECT_EQ(ret, GMERR_OK);
    system("rm -rf a.csv");

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAddHighCount)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableHighCount(id integer, worktime integer, ip inet) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand[200] = "select id, ip from tableHighCount where id+worktime>25;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    uint32_t charSize = 33;
    bool eof = false;
    int64_t id[] = {1, 2, 4};
    int64_t worktime[] = {24, 24, 25};
    char ip[][33] = {"1234567812345678abcdabcdabcdabcd", "fffffffd", "fffffff1"};
    char tableName[30] = "tableHighCount";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, ip, 33, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    char ipRes[33] = {0};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id[i + 1]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ipRes, &charSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(DbStrCmp(ipRes, ip[i + 1], false), 0);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

// select a+b，其中b为char,inet,blob,text类型，预期失败
TEST_F(StTsColOpGmc, ColOpAddColumnType)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand1[200] = "create table tsdb1(id integer, time integer, name char(64)) "
                            "with(time_col = 'time', interval = '1 day', compression = 'fast(rapidlz)');";
    char ddlCommand2[200] = "create table tsdb2(id integer, time integer, ip inet) "
                            "with(time_col = 'time', interval = '1 day', compression = 'fast(rapidlz)');";
    char ddlCommand3[200] = "create table tsdb3(id integer, time integer, message blob(160)) "
                            "with(time_col = 'time', interval = '1 day', compression = 'fast(rapidlz)');";
    char ddlCommand4[200] = "create table tsdb4(id integer, time integer, ns text) "
                            "with(time_col = 'time', interval = '1 day', compression = 'fast(rapidlz)');";
    char qryCommand1[200] = "select id+name from tsdb1;";
    char qryCommand2[200] = "select id+ip from tsdb2;";
    char qryCommand3[200] = "select id+message from tsdb3;";
    char qryCommand4[200] = "select id+ns from tsdb4;";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand2, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand3, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand4, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // int + char
    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    // int + inet
    ret = GmcExecDirect(stmt, qryCommand2, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    // int + blob
    ret = GmcExecDirect(stmt, qryCommand3, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    // int + text
    ret = GmcExecDirect(stmt, qryCommand4, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAddWhereAdd)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[200] =
        "create table tableColOpAddWhereAdd(age integer, worktime integer, id integer) with"
        " (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand[200] = "select worktime + age from tableColOpAddWhereAdd where worktime + age > 50;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = sizeof(int64_t);
    bool eof = false;
    int64_t age[] = {29, 30, 19};
    int64_t worktime[] = {24, 24, 25};
    int64_t id[] = {11, 9, 20};
    char tableName[30] = "tableColOpAddWhereAdd";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t colSumRes1 = 0;
    int64_t colSumResult1[] = {53, 54};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t i = 0;
    eof = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes1, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes1, colSumResult1[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAddWhere6)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tableColOpWhere6(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description "
                           "text, id1 integer, time1 integer) with"
                           " (time_col = 'time', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand[200] = "select id1 + id1 from tableColOpWhere6 where id + id != 100 order by id;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = 160;
    bool eof = false;
    int64_t id[] = {29, 30, 19};
    int64_t time[] = {24, 24, 25};
    char name[][64] = {"david", "nut", "bob"};
    char ip[][33] = {"10101040", "10101040", "98765432"};
    uint8_t message[][160] = {"0071 3520", "0010 0000", ""};
    char text[][64] = {"test data of the text type:0", "test data of the text type:1", "test data of the text type:2"};
    char *newText[rowNum];
    uint8_t *newMessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newText[j] = text[j];
        newMessage[j] = message[j];
    }
    int64_t id1[] = {29, 30, 19};
    int64_t time1[] = {24, 24, 25};
    char tableName[30] = "tableColOpWhere6";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)8);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 64, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ip, 33, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, newMessage, sizeof(newMessage[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_STRING, newText, sizeof(newText[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, id1, sizeof(id1[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, time1, sizeof(time1[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t colSumRes = 0;
    int64_t colSumResult[] = {38, 58, 60};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpAddWhere7)
{
    static GmcConnOptionsT *connOptions = NULL;
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tableColOpWhere7(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description "
                           "text, id1 integer, time1 integer) with"
                           " (time_col = 'time', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');";
    char qryCommand[200] = "select id + id, time from tableColOpWhere7 where id1 > 20 order by id;";
    uint32_t rowNum = 3;
    uint32_t value = 0;
    uint32_t propSize = 160;
    bool eof = false;
    int64_t id[] = {29, 30, 19};
    int64_t time[] = {24, 24, 25};
    char name[][64] = {"david", "nut", "bob"};
    char ip[][33] = {"10101040", "10101040", "98765432"};
    uint8_t message[][160] = {"0071 3520", "0010 0000", ""};
    char text[][64] = {"test data of the text type:0", "test data of the text type:1", "test data of the text type:2"};
    char *newText[rowNum];
    uint8_t *newMessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newText[j] = text[j];
        newMessage[j] = message[j];
    }
    int64_t id1[] = {29, 30, 19};
    int64_t time1[] = {24, 24, 25};
    char tableName[30] = "tableColOpWhere7";

    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)8);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 64, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ip, 33, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, newMessage, sizeof(newMessage[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_STRING, newText, sizeof(newText[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, id1, sizeof(id1[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, time1, sizeof(time1[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t colSumRes = 0;
    int64_t colSumResult[] = {58, 60};
    int64_t timeRes = 0;
    int64_t timeResult[] = {24, 24};
    bool isNull = false;

    ret = GmcExecDirect(stmt, qryCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &timeRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(timeRes, timeResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
}

Status colOpConnect(GmcConnT **conn, GmcStmtT **stmt)
{
    static GmcConnOptionsT *connOptions = NULL;
    Status ret = GmcInit();
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetRequestTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetMsgReadTimeout(connOptions, 1000000);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnOptionsSetCSRead(connOptions);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, conn);
    EXPECT_EQ(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    ret = GmcAllocStmt(*conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
    return ret;
}

Status colOpDisconnect(GmcConnT *conn, GmcStmtT *stmt)
{
    GmcFreeStmt(stmt);
    Status ret = GmcDisconnect(conn);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcUnInit();
    EXPECT_EQ(ret, GMERR_OK);
    return ret;
}

Status colOpInsert(GmcStmtT *stmt, char *tableName)
{
    uint32_t rowNum = 3;
    int64_t id[] = {29, 30, 19};
    int64_t time[] = {24, 24, 25};
    char name[][64] = {"david", "nut", "bob"};
    char ip[][33] = {"10101040", "10101040", "98765432"};
    uint8_t message[][160] = {"0071 3520", "0010 0000", ""};
    char text[][64] = {"test data of the text type:0", "test data of the text type:1", "test data of the text type:2"};
    char *newText[rowNum];
    uint8_t *newMessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newText[j] = text[j];
        newMessage[j] = message[j];
    }
    int64_t age[] = {1, 10, 100};
    int64_t score[] = {24, 24, 25};
    Status ret = GMERR_OK;
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 64, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ip, 33, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, newMessage, sizeof(newMessage[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_STRING, newText, sizeof(newText[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, score, sizeof(score[0]), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    return ret;
}

TEST_F(StTsColOpGmc, ColOpOrderBy1)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tableColOpOrderBy1(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, age integer, score integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[100] = "select time + id, id from tableColOpOrderBy1 order by time + age";
    char qryCommand2[100] = "select score + age, id from tableColOpOrderBy1 order by time + age";
    char tableName[30] = "tableColOpOrderBy1";
    Status ret = colOpConnect(&conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = colOpInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    // query1
    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t colSumRes = 0;
    int64_t colSumResult[] = {53, 54, 44};
    int64_t id[] = {29, 30, 19};
    bool isNull = false;
    bool eof = false;
    uint32_t propSize = sizeof(int64_t);
    uint32_t i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    // query2
    ret = GmcExecDirect(stmt, qryCommand2, 100);
    EXPECT_EQ(ret, GMERR_OK);

    idRes = 0;
    colSumRes = 0;
    int64_t colSumResult2[] = {25, 34, 125};
    isNull = false;
    eof = false;
    i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult2[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    ret = colOpDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpOrderBy2)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tableColOpOrderBy2(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, age integer, score integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[100] = "select id from tableColOpOrderBy2 order by time + id";
    char tableName[30] = "tableColOpOrderBy2";
    Status ret = colOpConnect(&conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = colOpInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t idResult[] = {19, 29, 30};
    bool isNull = false;
    bool eof = false;
    uint32_t propSize = sizeof(int64_t);
    uint32_t i = 0;

    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, idResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    ret = colOpDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpOrderBy3)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tableColOpOrderBy3(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, age integer, score integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[100] = "select id from tableColOpOrderBy3 group by time order by time + id";
    char tableName[30] = "tableColOpOrderBy3";
    Status ret = colOpConnect(&conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = colOpInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t idResult[] = {19, 29};
    bool isNull = false;
    bool eof = false;
    uint32_t propSize = sizeof(int64_t);
    uint32_t i = 0;

    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, idResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    ret = colOpDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpOrderBy4)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tableColOpOrderBy4(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, age integer, score integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[100] = "select time + id, id from tableColOpOrderBy4 group by time order by time + age";
    char tableName[30] = "tableColOpOrderBy4";
    Status ret = colOpConnect(&conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = colOpInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t colSumRes = 0;
    int64_t colSumResult[] = {53, 44};
    int64_t id[] = {29, 19};
    bool isNull = false;
    bool eof = false;
    uint32_t propSize = sizeof(int64_t);
    uint32_t i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    ret = colOpDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpOrderBy5)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tableColOpOrderBy5(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, age integer, score integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[100] = "select time + id, sum(id) from tableColOpOrderBy5 group by time order by time + age";
    char tableName[30] = "tableColOpOrderBy5";
    Status ret = colOpConnect(&conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = colOpInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t colSumRes = 0;
    int64_t colSumResult[] = {53, 44};
    int64_t idResult[] = {59, 19};
    bool isNull = false;
    bool eof = false;
    uint32_t propSize = sizeof(int64_t);
    uint32_t i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, idResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    ret = colOpDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpOrderBy6)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tableColOpOrderBy6(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, age integer, score integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[100] = "select time + id, sum(id) from tableColOpOrderBy6 order by time + age";
    char tableName[30] = "tableColOpOrderBy6";
    Status ret = colOpConnect(&conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = colOpInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t colSumRes = 0;
    int64_t colSumResult = 53;
    int64_t id = 78;
    bool isNull = false;
    bool eof = false;
    uint32_t propSize = sizeof(int64_t);
    uint32_t i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    ret = colOpDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpOrderBy7)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tableColOpOrderBy7(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, age integer, score integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[100] = "select time + id, id from tableColOpOrderBy7 order by time + id, time + age";
    char tableName[30] = "tableColOpOrderBy7";
    Status ret = colOpConnect(&conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = colOpInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t colSumRes = 0;
    int64_t colSumResult[] = {44, 53, 54};
    int64_t id[] = {19, 29, 30};
    bool isNull = false;
    bool eof = false;
    uint32_t propSize = sizeof(int64_t);
    uint32_t i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    ret = colOpDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpOrderBy8)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tableColOpOrderBy8(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, age integer, score integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[200] =
        "select time + id, sum(id) from tableColOpOrderBy8  where time + id < 50 group by time order by time + age";
    char tableName[30] = "tableColOpOrderBy8";
    Status ret = colOpConnect(&conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = colOpInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t colSumRes = 0;
    int64_t colSumResult = 44;
    int64_t idResult = 19;
    bool isNull = false;
    bool eof = false;
    uint32_t propSize = sizeof(int64_t);
    uint32_t i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, idResult);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    ret = colOpDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpOrderBy9)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tableColOpOrderBy9(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, age integer, score integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[100] =
        "select time + id, sum(id) from tableColOpOrderBy9 where time + id > 50 order by time + age";
    char tableName[30] = "tableColOpOrderBy9";
    Status ret = colOpConnect(&conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = colOpInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t colSumRes = 0;
    int64_t colSumResult = 53;
    int64_t id = 59;
    bool isNull = false;
    bool eof = false;
    uint32_t propSize = sizeof(int64_t);
    uint32_t i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    ret = colOpDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpOrderBy10)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tableColOpOrderBy10(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, age integer, score integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[100] = "select time + id, sum(id) from tableColOpOrderBy10 order by age + 1";
    char qryCommand2[100] = "select time + id, sum(id) from tableColOpOrderBy10 order by 1 + age";
    char qryCommand3[100] = "select time + id, sum(id) from tableColOpOrderBy10 order by 1 + 1, age";
    char tableName[30] = "tableColOpOrderBy10";
    Status ret = colOpConnect(&conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = colOpInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    // query1
    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t colSumRes = 0;
    int64_t colSumResult = 53;
    int64_t id = 78;
    bool isNull = false;
    bool eof = false;
    uint32_t propSize = sizeof(int64_t);
    uint32_t i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    // query2
    ret = GmcExecDirect(stmt, qryCommand2, 100);
    EXPECT_EQ(ret, GMERR_OK);
    idRes = 0;
    colSumRes = 0;
    isNull = false;
    eof = false;
    i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);

    // query3
    ret = GmcExecDirect(stmt, qryCommand3, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = colOpDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpOrderBy11)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tableColOpOrderBy11(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, age integer, score integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[100] = "select time + id, sum(id) from tableColOpOrderBy11 group by time order by age + 1";
    char qryCommand2[100] = "select time + id, sum(id) from tableColOpOrderBy11 group by time order by 1 + age";
    char qryCommand3[100] = "select time + id, sum(id) from tableColOpOrderBy11 group by time order by 1 + 1, age";
    char tableName[30] = "tableColOpOrderBy11";
    Status ret = colOpConnect(&conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = colOpInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    // query1
    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t colSumRes = 0;
    int64_t colSumResult[] = {53, 44};
    int64_t idResult[] = {59, 19};
    bool isNull = false;
    bool eof = false;
    uint32_t propSize = sizeof(int64_t);
    uint32_t i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, idResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    // query2
    ret = GmcExecDirect(stmt, qryCommand2, 100);
    EXPECT_EQ(ret, GMERR_OK);

    idRes = 0;
    colSumRes = 0;
    isNull = false;
    eof = false;
    i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, idResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    // query3
    ret = GmcExecDirect(stmt, qryCommand3, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = colOpDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpOrderBy12)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tableColOpOrderBy12(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, age integer, score integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[100] = "select time + id, id from tableColOpOrderBy12 order by age + 1";
    char qryCommand2[100] = "select time + id, id from tableColOpOrderBy12 order by 1 + age";
    char qryCommand3[100] = "select time + id, id from tableColOpOrderBy12 order by 1 + 1, age";
    char tableName[30] = "tableColOpOrderBy12";
    Status ret = colOpConnect(&conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = colOpInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    // query1
    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t colSumRes = 0;
    int64_t colSumResult[] = {53, 54, 44};
    int64_t id[] = {29, 30, 19};
    bool isNull = false;
    bool eof = false;
    uint32_t propSize = sizeof(int64_t);
    uint32_t i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    // query2
    ret = GmcExecDirect(stmt, qryCommand2, 100);
    EXPECT_EQ(ret, GMERR_OK);

    idRes = 0;
    colSumRes = 0;
    isNull = false;
    eof = false;
    i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, id[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)3);

    // query3
    ret = GmcExecDirect(stmt, qryCommand3, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = colOpDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpOrderBy13)
{
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("rm -rf /data/gmdb");
    DbSleep(100);
    StartDbServer((char *)g_cfgHighCardPersist, "usocket:/run/verona/unix_emserver");
    DbSleep(100);
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tableColOpOrderBy13(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, age integer, score integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[100] = "select time + id, sum(id) from tableColOpOrderBy13 group by time order by time + age";
    char qryCommand2[100] = "select time + id, sum(id) from tableColOpOrderBy13 group by time order by 1 + age";
    char qryCommand3[100] = "select time + id, sum(id) from tableColOpOrderBy13 group by time order by age + 1";
    char qryCommand4[100] = "select time + id, sum(id) from tableColOpOrderBy13 group by time order by 1 + 1, age";
    char tableName[30] = "tableColOpOrderBy13";
    Status ret = colOpConnect(&conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = colOpInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    // query1
    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t colSumRes = 0;
    int64_t colSumResult[] = {53, 44};
    int64_t idResult[] = {59, 19};
    bool isNull = false;
    bool eof = false;
    uint32_t propSize = sizeof(int64_t);
    uint32_t i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, idResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    // query2
    ret = GmcExecDirect(stmt, qryCommand2, 100);
    EXPECT_EQ(ret, GMERR_OK);

    idRes = 0;
    colSumRes = 0;
    isNull = false;
    eof = false;
    i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, idResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    // query3
    ret = GmcExecDirect(stmt, qryCommand3, 100);
    EXPECT_EQ(ret, GMERR_OK);

    idRes = 0;
    colSumRes = 0;
    isNull = false;
    eof = false;
    i = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &colSumRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(colSumRes, colSumResult[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, idResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    // query4
    ret = GmcExecDirect(stmt, qryCommand4, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = colOpDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpOrderBy14)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tableColOpOrderBy14(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, age integer, score integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[100] = "select id from tableColOpOrderBy14 order by id + 1 limit 2";
    char tableName[30] = "tableColOpOrderBy14";
    Status ret = colOpConnect(&conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = colOpInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t idResult[] = {19, 29, 30};
    bool isNull = false;
    bool eof = false;
    uint32_t propSize = sizeof(int64_t);
    uint32_t i = 0;

    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, idResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    ret = colOpDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpOrderBy15)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tableColOpOrderBy15(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, age integer, score integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[100] = "select id from tableColOpOrderBy15 order by id + time limit 2";
    char tableName[30] = "tableColOpOrderBy15";
    Status ret = colOpConnect(&conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = colOpInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t idRes = 0;
    int64_t idResult[] = {19, 29, 30};
    bool isNull = false;
    bool eof = false;
    uint32_t propSize = sizeof(int64_t);
    uint32_t i = 0;

    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &idRes, &propSize, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(idRes, idResult[i]);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)2);

    ret = colOpDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpOrderBy16)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] = "create table tableColOpOrderBy16(id integer, time integer, name char(64), ip inet, message "
                           "blob(160), description text, age integer, score integer) with (time_col = 'time', "
                           "interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[100] = "select id from tableColOpOrderBy16 order by 1 + 1, id limit 2";
    char tableName[30] = "tableColOpOrderBy16";
    Status ret = colOpConnect(&conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = colOpInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 100);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = colOpDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StTsColOpGmc, ColOpOrderByErr1)
{
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    char ddlCommand[500] =
        "create table tableColOpOrderByErr1(id integer, time integer, name char(64), ip inet, message "
        "blob(160), description text, age integer, score integer) with (time_col = 'time', "
        "interval = '1 hour', compression = 'fast(rapidlz)');";
    char qryCommand1[200] = "select age from tableColOpOrderByErr1 order by time - age;";
    char qryCommand2[200] = "select age from tableColOpOrderByErr1 order by time * age;";
    char qryCommand3[200] = "select age from tableColOpOrderByErr1 order by time / age;";
    char qryCommand4[200] = "select age from tableColOpOrderByErr1 order by time + message;";
    char qryCommand5[200] = "select age from tableColOpOrderByErr1 order by time + age + 1;";
    char qryCommand6[200] = "select age from tableColOpOrderByErr1 order by time + sum(age);";
    char qryCommand7[200] = "select age from tableColOpOrderByErr1 order by length(age + time);";
    char qryCommand8[200] = "select age from tableColOpOrderByErr1 order by colset(age + time);";
    char qryCommand9[200] = "select age from tableColOpOrderByErr1 group by age order by first(age + id);";
    char qryCommand10[200] =
        "select age, sum(id) from tableColOpOrderByErr1 group by age order by sum(id) + sum(time);";
    char tableName[30] = "tableColOpOrderByErr1";
    Status ret = colOpConnect(&conn, &stmt);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, ddlCommand, 500);
    EXPECT_EQ(ret, GMERR_OK);
    ret = colOpInsert(stmt, tableName);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, qryCommand1, 200);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    ret = GmcExecDirect(stmt, qryCommand2, 200);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    ret = GmcExecDirect(stmt, qryCommand3, 200);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    ret = GmcExecDirect(stmt, qryCommand4, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand5, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand6, 200);
    ret = GmcExecDirect(stmt, qryCommand7, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand8, 200);
    EXPECT_EQ(ret, GMERR_SEMANTIC_ERROR);
    ret = GmcExecDirect(stmt, qryCommand9, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    ret = GmcExecDirect(stmt, qryCommand10, 200);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);

    ret = colOpDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}
