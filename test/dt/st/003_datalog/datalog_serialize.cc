/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: system test about serializable transactions with datalog
 * Author: liheng
 * Create: 2022-8-10
 */

#include "gtest/gtest.h"
#include "gmc.h"
#include "StartDbServer.h"
#include "InitClt.h"

class DatalogSerialize : public testing::Test {
protected:
    virtual void SetUp()
    {
        StartDbServerWithConfig("");
        st_clt_init();
        st_connect();
    }
    virtual void TearDown()
    {
        st_disconnect();
        st_clt_uninit();
        ShutDownDbServer();
    }
};

const char *g_nameSpaceName = "public";
const char *g_labelName1 = "inpA6";
const char *g_labelName2 = "inpB6";

static void ImportDatalogLabel()
{
    system("source ../../../../scripts/build_scripts/asan_clean_env.sh &&"
           "../../../../build/bin/gmprecompiler -f ./simple_filter_index_scan.d ./simple_filter_index_scan.c &&"
           "gcc ./simple_filter_index_scan.c -fPIC -shared -o ./simple_filter_index_scan.so");
    system("gmimport -c datalog -f ./simple_filter_index_scan.so -ns public");
}

static void StartTrx(GmcConnT *conn)
{
    GmcTxConfigT config = {0};
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_SERIALIZABLE;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    EXPECT_EQ(GMERR_OK, GmcTransStart(conn, &config));
}

static void WriteLabel1(GmcConnT *conn, GmcStmtT *stmt, int expectVal)
{
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT));

    GmcBatchT *batch = nullptr;
    GmcBatchRetT batchRet{};
    GmcBatchOptionT batchOption{};
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchPrepare(conn, &batchOption, &batch));

    int32_t records[][2] = {{1, +1}, {1, +1}, {1, +1}};
    for (uint32_t i = 0; i < sizeof(records) / sizeof(records[0]); ++i) {
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &records[i][0], 4));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &records[i][1], 4));
        GmcBatchAddDML(batch, stmt);
    }

    EXPECT_EQ(expectVal, GmcBatchExecute(batch, &batchRet));
    GmcBatchDestroy(batch);
}

static void WriteLabel2(GmcConnT *conn, GmcStmtT *stmt, int expectVal)
{
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName2, GMC_OPERATION_INSERT));

    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet{};
    GmcBatchOptionT batchOption{};
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchPrepare(conn, &batchOption, &batch));

    int32_t records[][4] = {{1, 2, 1, +1}, {1, 1, 2, +1}, {1, 3, 3, +1}};
    for (uint32_t i = 0; i < sizeof(records) / sizeof(records[0]); ++i) {
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &records[i][0], 4));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &records[i][1], 4));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &records[i][2], 4));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &records[i][3], 4));
        GmcBatchAddDML(batch, stmt);
    }

    EXPECT_EQ(expectVal, GmcBatchExecute(batch, &batchRet));
    GmcBatchDestroy(batch);
}

static void StartTrxAndWrite(GmcConnT *conn, GmcStmtT *stmt)
{
    WriteLabel1(conn, stmt, GMERR_OK);
    WriteLabel2(conn, stmt, GMERR_OK);
}

static void *StartNewTrxAndWrite(void *)
{
    GmcConnT *conn = nullptr;
    GmcStmtT *stmt = nullptr;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, g_nameSpaceName));

    WriteLabel1(conn, stmt, GMERR_OK);
    EXPECT_EQ(GMERR_OK, GmcTransRollBack(conn));
    WriteLabel2(conn, stmt, GMERR_OK);
    EXPECT_EQ(GMERR_OK, GmcTransRollBack(conn));

    return nullptr;
}

TEST_F(DatalogSerialize, SerializeExplicitTrx)
{
    ImportDatalogLabel();

    GmcConnT *conn = nullptr;
    GmcStmtT *stmt = nullptr;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, g_nameSpaceName));

    uint32_t threadNum = 2;
    pthread_t threadId1[threadNum] = {0};
    for (uint32_t i = 0; i < threadNum; ++i) {
        pthread_create(&threadId1[i], nullptr, StartNewTrxAndWrite, nullptr);
    }

    for (uint32_t i = 0; i < threadNum; ++i) {
        pthread_join(threadId1[i], nullptr);
    }
}

static void *WriteLbel1Trx(void *)
{
    GmcConnT *conn = nullptr;
    GmcStmtT *stmt = nullptr;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, g_nameSpaceName));
    WriteLabel1(conn, stmt, GMERR_OK);

    return nullptr;
}

static void *WriteLbel2Trx(void *)
{
    GmcConnT *conn = nullptr;
    GmcStmtT *stmt = nullptr;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, g_nameSpaceName));
    WriteLabel2(conn, stmt, GMERR_OK);

    return nullptr;
}

TEST_F(DatalogSerialize, SerializeTrxWriteLabels)
{
    ImportDatalogLabel();

    pthread_t threadId1{};
    pthread_create(&threadId1, nullptr, WriteLbel1Trx, nullptr);
    pthread_t threadId2{};
    pthread_create(&threadId2, nullptr, WriteLbel2Trx, nullptr);

    pthread_join(threadId1, nullptr);
    pthread_join(threadId2, nullptr);
}

static void *ReadLbel1Trx(void *)
{
    GmcConnT *conn = nullptr;
    GmcStmtT *stmt = nullptr;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, g_nameSpaceName));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    bool isFinish = false;
    while (true) {
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));
        if (isFinish) {
            break;
        }
    }

    return nullptr;
}

static void *ReadLbel2Trx(void *)
{
    GmcConnT *conn = nullptr;
    GmcStmtT *stmt = nullptr;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, g_nameSpaceName));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName2, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));

    bool isFinish = false;
    while (true) {
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));
        if (isFinish) {
            break;
        }
    }

    return nullptr;
}

TEST_F(DatalogSerialize, SerializeTrxReadLabels)
{
    ImportDatalogLabel();

    pthread_t threadId1{};
    pthread_create(&threadId1, nullptr, ReadLbel1Trx, nullptr);
    pthread_t threadId2{};
    pthread_create(&threadId2, nullptr, ReadLbel2Trx, nullptr);

    pthread_join(threadId1, nullptr);
    pthread_join(threadId2, nullptr);
}

void WriteOneElemOnLabel(GmcConnT *conn, GmcStmtT *stmt)
{
    EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, g_nameSpaceName));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT));

    GmcBatchT *batch = nullptr;
    GmcBatchRetT batchRet{};
    GmcBatchOptionT batchOption{};
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchPrepare(conn, &batchOption, &batch));

    int32_t records[][2] = {{1, +1}};
    for (uint32_t i = 0; i < sizeof(records) / sizeof(records[0]); ++i) {
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &records[i][0], 4));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &records[i][1], 4));
        GmcBatchAddDML(batch, stmt);
    }

    EXPECT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    GmcBatchDestroy(batch);
}

void DeleteOneElemOnLabel(GmcConnT *conn, GmcStmtT *stmt)
{
    EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, g_nameSpaceName));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT));

    GmcBatchT *batch = nullptr;
    GmcBatchRetT batchRet{};
    GmcBatchOptionT batchOption{};
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchPrepare(conn, &batchOption, &batch));

    int32_t records[][2] = {{1, -1}};
    for (uint32_t i = 0; i < sizeof(records) / sizeof(records[0]); ++i) {
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &records[i][0], 4));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &records[i][1], 4));
        GmcBatchAddDML(batch, stmt);
    }

    EXPECT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    GmcBatchDestroy(batch);
}

void SearchOneElemOnLabel(GmcConnT *conn, GmcStmtT *stmt)
{
    EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, g_nameSpaceName));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "0"));
    int32_t val = 1;
    int32_t u = 0;
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &u, sizeof(u)));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &val, sizeof(val)));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));

    bool eof;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    int32_t res = 0;
    bool isNull = false;
    EXPECT_EQ(GMERR_NO_DATA, GmcGetVertexPropertyByName(stmt, "a", &res, sizeof(uint32_t), &isNull));
}

TEST_F(DatalogSerialize, SerializeTrxReadView)
{
    ImportDatalogLabel();

    GmcConnT *conn = nullptr;
    GmcStmtT *stmt = nullptr;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, g_nameSpaceName));

    WriteOneElemOnLabel(conn, stmt);
    DeleteOneElemOnLabel(conn, stmt);
    SearchOneElemOnLabel(conn, stmt);
}

TEST_F(DatalogSerialize, SearchElemOnly)
{
    ImportDatalogLabel();

    GmcConnT *conn = nullptr;
    GmcStmtT *stmt = nullptr;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, g_nameSpaceName));

    SearchOneElemOnLabel(conn, stmt);
}
