%function NotEqual1(a: int1, b: int1)
%function NotEqual2(a: int2, b: int2)
%function NotEqual4(a: int4, b: int4)
%function NotEqualbyte16(a: byte16, b: byte16)
%function NotZerobyte200(a: byte200)
%function I1ToI4(a: int1 -> b: int4)
%table state(state: int4)
%table tbl_init(i: int4)
tbl_init(0):- Abstract.a_if_link(-, -, -, -).
tbl_init(0):- state(0).
namespace Ifm {
    %readonly ConfigVapIf, PublishRadioName, ConfigIf, PublishNif, ConfigAttributes, Agg_Attributes
    %readonly ConfigIfIpv4Addr, ConfigIfIpLearn, ConfigProtocolEnable, GetIfShowType, CalculateIfLinkState
    %readonly CalculateIfOperState, CalculateIfIpv4State, ConfigLinkUpDownMode, PortAttrChg, ConfigIfWmpPortal, ConfigIfWmpWebProxy, Pppoe
    %readonly ConfigFreeRuleTemplate, VapAclGroup, VapAclGroupIds
    %readonly EthTrunk, EthTrunkMembers, EthTrunkMembers_Copy
    %table PortAttrChg(ifIndex: int4, attr: int1, value: int4) { index(0(ifIndex)), transient(tuple) }
    %table Pppoe(ifIndex: int4, code: int1, ver: int1, type: int1, sessionId: int2, dmac: byte6) { index(0(ifIndex)), update }
    %table ConfigVapIf(nsId: int4, ifIndex: int4, radioName: byte64, adminState: int1, vapType: int1, vapIndex: int1, linkedId: int1, mac: byte6,
        userIdentify: int1, userService: int1, userAuth: int1, authType: int1, isEapolKeyToCp: int1, protocolTunnelFwd: int1) {
        index(0(nsId, ifIndex)), update_partial
    }
    %table PublishRadioName(radioName: byte64, type: byte16, tp: int4, phy_port: int4, port_id: int4, cpu_type: int1, drvPortType: int1, drvSubType: int1) {
        index(0(radioName)), update_partial
    }
    %table ConfigIf(
        nsId: int4, ifName: byte64, ifIndex: int4, type: int2, phyPort: int4, masterIfIndex: int4, state: int1)
        { index(0(nsId, ifName)), update_partial }
    %table PublishNif(
        ifIndex: int4, ifName: byte64, type: byte16, tp: int4, chip_unit: int1, oda_phy_port: int1,
        state: int1, mac: byte6, dev_id: int4, chassis_id: int4, slot_id: int4, card_id: int4,
        unit_id: int4, port_id: int4, cpu_type: int1, drvPortType: int1, drvSubType: int1)
        { index(0(ifIndex)), update_partial, union_delete(ConfigIfWmpWebProxy, ConfigIfWmpPortal)}
    %table ConfigAttributes(
        nsId: int4, ifIndex: int4, phyState: int1, mac: byte6, mtu: int4, mtu6: int4, autoneg: int1,
        speed: int8, duplex: int1, combo: int1, loopback: int1, hasV4Addr: int1, alias: byte64,
        service_type: int1, autoneg_cap: int1, autoneg_adv_cap: int2, medium: int1, mru: int2,
        link_type: int1, eth_class: int1)
        { index(0(nsId, ifIndex)), update_partial }
    %table ConfigIfIpv4Addr(ifIndex: int4, vrfIndex: int4, address: int4, maskLen: int2, type: int2)
        { index(0(ifIndex, vrfIndex, address, maskLen, type)),
          index(1(ifIndex)),
          update_partial }
    %table ConfigIfIpLearn(ifIndex: int4, ipLearnEnable: int1, dhcpStrict: int1, addBlackList: int1) {
        index(0(ifIndex)), update_partial
    }
    %table ConfigIfWmpPortal(ifIndex: int4, apPortalEn: int1, freeRuleName: byte65) {
        index(0(ifIndex)), update_partial
    }
    %table ConfigFreeRuleTemplate(templateName: byte65, ipv4GroupId: int4, ipv6GroupId: int4, staticGroupName: byte65) {
        index(0(templateName)), update_partial
    }
    %table ConfigIfWmpWebProxy(ifIndex: int4, webProxyPortalParse: int1, proxyPort:int2, url:byte200) {
        index(0(ifIndex)), update_partial
    }
    %table ConfigProtocolEnable(ifIndex: int4, proto: int1, enable: int1) {
        index(0(ifIndex)), update_partial
    }
    %table ConfigLinkUpDownMode(ifIndex: int4, mode: int1) {
        index(0(ifIndex)), update_partial
    }
    %table EthTrunk(ifIndex: int4, nsId: int4, trunkId: int4, domain: int1){
        index(0(ifIndex, nsId)),
        update
    }
    %table EthTrunkMembers(memIfIndex: int4, trunkId: int4, nsId: int4, ifIndex: int4){
        index(0(memIfIndex)),
        index(1(trunkId)),
        update
    }
    %table Agg_Attributes(
        nsId: int4, ifIndex: int4, ifName: byte64, phyState: int1, adminState: int1,
        onboard_state: int1, if_bw: int8, tp: int4, tb: int4, chip_unit: int1, oda_phy_port: int1, mac: byte6,
        autoneg: int1, speed: int8, duplex: int1, combo: int1, loopback: int1, mtu: int4, mtu6: int4,
        type: int2, hasV4Addr: int1, service_type: int1, autoneg_cap: int1, autoneg_adv_cap: int2, medium: int1,
        mru: int2, link_type: int1, eth_class: int1, cpu_type: int1)
    %table EthTrunkMembers_Copy(memIfIndex: int4, trunkId: int4, nsId: int4, ifIndex: int4){
        index(0(memIfIndex)),
        index(1(trunkId)),
        update
    }
    %function GetIfType(ifType: byte16 -> type: int2)
    %function CalculateIfPhyState(type: int2, srcPhyState: int1, adminState: int1 -> phyState: int1)
    %function GetIfShowType(ifName: byte64 -> showType: int2)
    %function CalculateIfLinkState(ifIndex: int4, type: int2, phyState: int1, vlanIfLinkState: int1 -> linkState: int1)
    %function CalculateIfOperState(linkState: int1 -> operState: int1)
    %function CalculateIfIpv4State(type: int2, linkState: int1, hasV4Addr: int1 -> ipv4State: int1)
    %function GetIfMac(initMac: byte6, userMac: byte6 -> mac: byte6)
    Agg_Attributes(
        nsId, ifIndex, ifName, phyState, adminState, 0, 0, tp, 0, 0, 0, mac,
        autoneg, speed, duplex, combo, loopback, mtu, mtu6, type, hasV4Addr, service_type,
        autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class, 0) :-
        ConfigIf(
            nsId, ifName, ifIndex, type,
            tp, - , srcPhyState),
        ConfigAttributes(
            nsId, ifIndex, adminState, mac, mtu, mtu6,
            autoneg, speed, duplex, combo, loopback, hasV4Addr, - , service_type, autoneg_cap,
            autoneg_adv_cap, medium, mru, link_type, eth_class),
        CalculateIfPhyState(type, srcPhyState, adminState, phyState).
    Agg_Attributes(
        0, ifIndex, ifName, phyState, adminState, 0, 0, tp, 0, chip_unit, oda_phy_port, mac,
        autoneg, speed, duplex, combo, loopback, mtu, mtu6, type, hasV4Addr, service_type,
        autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class, cpu_type) :-
        PublishNif(
            ifIndex, ifName, ifType, tp, chip_unit, oda_phy_port, srcPhyState, initMac, -, -, -, -, -, -, cpu_type, -, -),
        ConfigAttributes(
            -, ifIndex, adminState, userMac, mtu, mtu6,
            autoneg, speed, duplex, combo, loopback, hasV4Addr, - , service_type,
            autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class),
        GetIfType(ifType, type),
        CalculateIfPhyState(type, srcPhyState, adminState, phyState),
        GetIfMac(initMac, userMac, mac).
    %table Yif_If_ConfigIf(ifIndex: int4, ifName: byte64, ifType: int2, state: int1)
    {
        notify,
        index(0(ifIndex))
    }
    %table Yif_If_ConfigPhyState1(ifIndex: int4, ifName: byte64, phyState: int1)
    %table Yif_If_ConfigPhyState(ifIndex: int4, ifName: byte64, phyState: int1)
    {
        notify,
        index(0(ifIndex))
    }
    %table Yif_If_ConfigMac(ifIndex: int4, ifName: byte64, mac: byte6)
    {
        notify,
        index(0(ifIndex))
    }
    Yif_If_ConfigIf(ifIndex, ifName, ifType, state) :-
        ConfigIf(-, ifName, ifIndex, ifType, -, -, state).
    Yif_If_ConfigPhyState1(ifIndex, ifName, adminState) :-
        PublishNif(ifIndex, ifName, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
        Agg_Attributes(-, ifIndex, ifName, -, adminState, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -).
    Yif_If_ConfigPhyState(ifIndex, ifName, adminState) :-
        Yif_If_ConfigPhyState1(ifIndex, ifName, adminState).
    Yif_If_ConfigMac(ifIndex, ifName, mac) :-
        PublishNif(ifIndex, ifName, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
        ConfigAttributes(-, ifIndex, -, mac, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -).
    %table VapAclGroupIds(ifIndex: int4, v4Gid: int4, v6Gid: int4, staticGid: int4)
    %table VapAclGroup(gid: int4, type: int4)
    %table VapStaticAclNotPresent(templateName: byte65, v4Gid: int4, v6Gid: int4)
    VapAclGroupIds(ifIndex, v4Gid, v6Gid, 0) :-
        ConfigIfWmpPortal(ifIndex, -, templateName),
        VapStaticAclNotPresent(templateName, v4Gid, v6Gid).
    VapStaticAclNotPresent(templateName, v4Gid, v6Gid) :-
        ConfigFreeRuleTemplate(templateName, v4Gid, v6Gid, staticName),
        NOT Acl.GroupCfg(-, 0, -, -, -, staticName, 1, -).
    VapAclGroupIds(ifIndex, v4Gid, v6Gid, staticGid) :-
        ConfigIfWmpPortal(ifIndex, -, templateName),
        ConfigFreeRuleTemplate(templateName, v4Gid, v6Gid, staticName),
        Acl.GroupCfg(staticGid, 0, -, -, -, staticName, 1, -).
    VapAclGroup(gid, 0) :- VapAclGroupIds(-, gid, -, -), NotEqual4(gid, 0).
    VapAclGroup(gid, 0) :- VapAclGroupIds(-, -, gid, -), NotEqual4(gid, 0).
    VapAclGroup(gid, 0) :- VapAclGroupIds(-, -, -, gid), NotEqual4(gid, 0).
    EthTrunkMembers_Copy(memIfIndex, trunkId, nsId, ifIndex) :- 
    EthTrunkMembers(memIfIndex, trunkId, nsId, ifIndex).
}
namespace Abstract {
    a_vap_if(ifIndex, radioName, adminState, vapType, vapIndex, linkedId, mac,
        userIdentify, userService, userAuth, authType, isEapolKeyToCp, protocolTunnelFwd) :-
        Ifm.ConfigVapIf(-, ifIndex, radioName, adminState, vapType, vapIndex, linkedId, mac,
            userIdentify, userService, userAuth, authType, isEapolKeyToCp, protocolTunnelFwd).
    a_radio_name(radioName, type, tp, phy_port, port_id, cpu_type, drvPortType, drvSubType) :-
        Ifm.PublishRadioName(radioName, type, tp, phy_port, port_id, cpu_type, drvPortType, drvSubType).
    a_if_name(ifIndex, nsId, ifName, type, showType, "00", "00") :-
            Ifm.Agg_Attributes(nsId, ifIndex, ifName, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, type, -, -, -, -, -, -, -, -, -),
            Ifm.GetIfShowType(ifName, showType).
    a_if_phy(
        ifIndex, ifBw, phyState, adminState, onboardState,
        tb, tp, chip_unit, oda_phy_port, mac, autoeng, speed, duplex, combo, loopback, dev_id, chassis_id, slot_id, card_id, unit_id, port_id, service_type,
        autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class, cpu_type, drvPortType, drvSubType) :-
        Ifm.PublishNif(ifIndex, -, -, -, chip_unit, oda_phy_port, -, -, dev_id, chassis_id, slot_id, card_id, unit_id, port_id, -, drvPortType, drvSubType),
        Ifm.Agg_Attributes(
            -, ifIndex, -, phyState, adminState, onboardState, ifBw,
            tp, tb, chip_unit, oda_phy_port, mac, autoeng, speed, duplex, combo, loopback, -, -, -, -, service_type,
            autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class, cpu_type).
    a_if_phy(
        ifIndex, ifBw, phyState, adminState, onboardState,
        tb, tp, 0, 0, mac, autoeng, speed, duplex, combo, loopback, 0, 0, 0, 0, 0, 0, service_type,
        autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class, cpu_type, 0, 0) :-
        Ifm.ConfigIf(-, -, ifIndex, -, -, -, -),
        Ifm.Agg_Attributes(
            -, ifIndex, -, phyState, adminState, onboardState, ifBw,
            tp, tb, -, -, mac, autoeng, speed, duplex, combo, loopback, -, -, -, -, service_type,
            autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class, cpu_type).
    a_if_link(ifIndex, 0, linkState, operState) :-
        Ifm.Agg_Attributes( -, ifIndex, -, phyState, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, type, -, -, -, -, -, -, -, -, -),
        BR.VlanIfLinkState(ifIndex, vlanIfLinkState),
        Ifm.CalculateIfLinkState(ifIndex, type, phyState, vlanIfLinkState, linkState),
        Ifm.CalculateIfOperState(linkState, operState).
    a_if_net(ifIndex, 0, 0, mtu, ipv4State) :-
        Ifm.Agg_Attributes(-, ifIndex, -, phyState, -, -, -, -, -, -, -, -, -, -, -, -, -, mtu, -, type, hasV4Addr, -, -, -, -, -, -, -, -),
        BR.VlanIfLinkState(ifIndex, vlanIfLinkState),
        Ifm.CalculateIfLinkState(ifIndex, type, phyState, vlanIfLinkState, linkState),
        Ifm.CalculateIfIpv4State(type, linkState, hasV4Addr, ipv4State).
    a_if_net6(ifIndex, 0, mtu6, 1) :-
        Ifm.ConfigAttributes(-, ifIndex, -, -, -, mtu6, -, -, -, -, -, -, - , -, -, -, -, -, -, -).
    a_config_if_ipv4_addr(if_index, vrf_index, address, mask_len, type) :-
        Ifm.ConfigIfIpv4Addr(if_index, vrf_index, address, mask_len, type).
    a_if_eth_trunk_member(memIfIndex, trunkId, ifIndex):-
        Ifm.EthTrunkMembers(memIfIndex, trunkId, -, ifIndex),
        Ifm.ConfigAttributes(-, memIfIndex, 1, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -).
    a_if_eth_trunk(trunkId, trunkIndex):-
        Ifm.EthTrunk(trunkIndex, -, trunkId, -).
    a_if_fwdif_trunk_attribute(memIfIndex, trunkId, 1):-
        a_if_eth_trunk_member(memIfIndex, trunkId, -).
    a_if_fwdif_trunk_attribute(ifIndex, trunkId, 0):-
        a_if_eth_trunk(trunkId, ifIndex).
    a_if_fwdif_father(ifIndex, fatherifIndex):-
        a_if_eth_trunk_member(ifIndex, -, fatherifIndex).
}
namespace BR {
%readonly Bridge, BridgeMacAttr
%readonly Vlan, VlanTagPort, VlanUntagPort, VlanPortTagBitmap, VlanPortUnTagBitmap, VlanState, VlanMacAttr
%readonly Port, PortIsolateGrps, PortMacAttr, PortSecurity, PortVlanTagBitmap, PortVlanUntagBitmap, PortPort
%readonly VlanIfLinkState, PortIfPortIf
%readonly MacOper
%table Bridge(ns_id: int4, br_id: int4, br_name: byte16, vlan_filter: int1, br_if_index: int4) {index(0(ns_id, br_id)), update_partial}
%table BridgeMacAttr(ns_id: int4, br_id: int4, mac_age_time: int4, mac_thrd_up: int1, mac_thrd_down: int1, macLimitNum: int4) {index(0(ns_id, br_id)), update_partial}
%table Vlan(ns_id: int4, br_id: int4, vlan_id: int2, vlan_type: int1, name: byte32, desc: byte81, br_vlan_if: int4) {index(0(ns_id, br_id, vlan_id)), update_partial}
%table VlanMacAttr(ns_id: int4, br_id: int4, vlan_id: int2, learn: int1, limit: int4, limit_act: int1, limit_alm: int1) {index(0(ns_id, br_id, vlan_id)), update_partial}
%table VlanTagPort(ns_id: int4, br_id: int4, vlan_id: int2, port_index: int4, if_index: int4) {index(0(ns_id, br_id, vlan_id, port_index)), index(1(if_index)), update_partial}
%table VlanUntagPort(ns_id: int4, br_id: int4, vlan_id: int2, port_index: int4, if_index: int4) {index(0(ns_id, br_id, vlan_id, port_index)), index(1(if_index)), update_partial}
%table Port(if_index: int4, port_index: int4, ns_id: int4, br_id: int4, pvid: int2, use: int1, link_type: int1) {index(0(if_index)), index(1(port_index)), update_partial}
%table PortIsolateGrp(if_index: int4, port_index: int4, grp: int1) {index(0(if_index, port_index, grp)), update_partial}
%table PortMacAttr(if_index: int4, port_index: int4, learn: int1, learn_act: int1, limit: int4, limit_act: int1, limit_alm: int1) {index(0(if_index)), index(1(port_index)), update_partial}
%table PortSecurity(if_index: int4, port_index: int4, mac_sec: int1, mac_sec_max: int4, mac_sec_act: int1, mac_sec_age_time: int4) { index(0(if_index)), update_partial}
%table MacOper(ns_id: int4, br_id: int4, vlan_id: int2, mac: byte6, if_index: int4, type: int1, flag: int4) {index(0(ns_id, br_id, vlan_id, mac)), transient(tuple)}
%table VlanState0(ns_id: int4, br_id: int4, vlan_id: int2, state: int1)
%table VlanState(ns_id: int4, br_id: int4, vlan_id: int2, state: int1)
%table VlanPortTagBitmap(ns_id: int4, br_id: int4, vlan_id: int2, bitmap: byte32)
%table VlanPortUnTagBitmap(ns_id: int4, br_id: int4, vlan_id: int2, bitmap: byte32)
%table PortVlanTagBitmap(port_index: int4, ns_id: int4, br_id: int4, bitmap: byte512)
%table PortVlanUntagBitmap(port_index: int4, ns_id: int4, br_id: int4, bitmap: byte512)
%table PortIsolateGrp0(if_index: int4, port_index: int4, grp: int1)
%table PortIsolateGrps(if_index: int4, port_index: int4, grps: byte64)
%table PortPort(port_index: int4, isolate_port_index: int4)
%table PortIfPortIf(if_index: int4, isolate_if_index: int4)
%table AllIfPhyState(ifIndex: int4, type: int2, phy_state: int1)
%table VlanIfLinkState0(if_index: int4, link_state: int1)
%table VlanIfLinkState(if_index: int4, link_state: int1)
%function IfTypeIsEth(type: int2)
%function CombineVlanIfIndex(ns_id: int4, br_id: int4, vlan_id: int2 -> ifIndex: int4)
%aggregate Tbl2Bitmap32(port_index: int4 -> port_bitmap: byte32)
%aggregate Tbl2Bitmap512(vlan: int2 -> vlan_bitmap: byte512)
%aggregate OrLinkState(link_state: int1 -> vlan_state: int1)
%aggregate MergePortIsolateGrp(grp: int1 -> grps: byte64)
VlanPortTagBitmap (ns_id, br_id, vlan_id, bitmap) :-
    VlanTagPort(ns_id, br_id, vlan_id, port_index, -) GROUP-BY(ns_id, br_id, vlan_id) Tbl2Bitmap32(port_index, bitmap).
VlanPortUnTagBitmap (ns_id, br_id, vlan_id, bitmap) :-
    VlanUntagPort(ns_id, br_id, vlan_id, port_index, -) GROUP-BY(ns_id, br_id, vlan_id) Tbl2Bitmap32(port_index, bitmap).
PortVlanTagBitmap(port_index, ns_id, br_id, bitmap) :-
    VlanTagPort(ns_id, br_id, vlan_id, port_index, -) GROUP-BY(ns_id, br_id, port_index) Tbl2Bitmap512(vlan_id, bitmap).
PortVlanUntagBitmap(port_index, ns_id, br_id, bitmap) :-
    VlanUntagPort(ns_id, br_id, vlan_id, port_index, -) GROUP-BY(ns_id, br_id, port_index) Tbl2Bitmap512(vlan_id, bitmap).
AllIfPhyState(ifIndex, type, phy_state) :-
    Ifm.Agg_Attributes(-, ifIndex, -, phy_state, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, type, -, -, -, -, -, -, -, -, -).
VlanState0 (ns_id, br_id, vlan_id, phy_state) :-
    VlanTagPort(ns_id, br_id, vlan_id, -, if_index), AllIfPhyState(if_index, type, phy_state), IfTypeIsEth(type).
VlanState0 (ns_id, br_id, vlan_id, phy_state) :-
    VlanUntagPort(ns_id, br_id, vlan_id, -, if_index), AllIfPhyState(if_index, type, phy_state), IfTypeIsEth(type).
VlanState0 (ns_id, br_id, vlan_id, 0) :-
    VlanTagPort(ns_id, br_id, vlan_id, -, -1).
VlanState0 (ns_id, br_id, vlan_id, 0) :-
    VlanUntagPort(ns_id, br_id, vlan_id, -, -1).
VlanState (ns_id, br_id, vlan_id, vlan_state) :-
    VlanState0(ns_id, br_id, vlan_id, state) GROUP-BY(ns_id, br_id, vlan_id) OrLinkState(state, vlan_state).
VlanIfLinkState0(ifIndex, state) :-
    VlanState (ns_id, br_id, vlan_id, state),
    CombineVlanIfIndex(ns_id, br_id, vlan_id, ifIndex).
VlanIfLinkState(ifIndex, link_state) :-
    VlanIfLinkState0(ifIndex, link_state).
VlanIfLinkState(ifIndex, 0) :-
  AllIfPhyState(ifIndex, -, -),
  NOT VlanIfLinkState0(ifIndex, -).
PortIsolateGrp0(if_index, port_index, grp) :- PortIsolateGrp(if_index, port_index, grp).
PortIsolateGrps(if_index, port_index, grps) :-
    PortIsolateGrp(if_index, port_index, grp) GROUP-BY(if_index, port_index) MergePortIsolateGrp(grp, grps).
PortPort(port_index, isolate_port_index) :-
    PortIsolateGrp(-, port_index, grps1), PortIsolateGrp0(-, isolate_port_index, grps1),
    NotEqual4(port_index, isolate_port_index),
    NotEqual4(port_index, -1), NotEqual4(isolate_port_index, -1),
    NotEqual1(grps1, 0).
PortIfPortIf(if_index, isolate_if_index) :-
    PortIsolateGrp(if_index, -, grps1), PortIsolateGrp0(isolate_if_index, -, grps1),
    NotEqual4(if_index, isolate_if_index),
    NotEqual4(if_index, -1), NotEqual4(isolate_if_index, -1),
    NotEqual4(if_index, 0), NotEqual4(isolate_if_index, 0),
    NotEqual1(grps1, 0).
}
namespace Abstract {
%readonly Tbl_Bridge, Tbl_Vlan, Tbl_Port, Tbl_Port_VlanTagBmp, Tbl_Port_VlanUntagBmp
%table Tbl_Bridge(ns_id: int4, br_id: int4, br_name: byte16, vlan_filter: int1,
                 mac_age_time: int4, mac_thrd_up: int1, mac_thrd_down: int1,
                 mac_pri1_allow_flapping: int1, mac_pri2_allow_flapping: int1,
                 mac_pri3_allow_flapping: int1, mac_pri4_allow_flapping: int1,
                 br_if_index: int4, macLimitNum: int4)
%table Tbl_Vlan(ns_id: int4, br_id: int4, vlan_id: int2, vlan_type: int1, name: byte32, desc: byte81, mc_id: int4,
               untag_port_bitmap: byte32, tag_port_bitmap: byte32, state: int1,
               mac_learn: int1, mac_limit_max: int4, mac_limit_act: int1, mac_limit_alarm: int1,
               br_vlan_if: int4, roamUserExistFlag: int1)
%table Tbl_Port(if_index: int4, port_index: int4, ns_id: int4, br_id: int4, use: int1, link_type: int1,
               pvid: int2, isolate_grps: byte64,
               mac_learn: int1, mac_learn_act: int1, mac_learn_pri: int1,
               mac_limit_max: int4, mac_limit_act: int1, mac_limit_alarm: int1,
               mac_sec: int1, mac_sec_max: int4, mac_sec_act: int1, mac_sec_age_time: int4)
%table Tbl_Port_VlanTagBmp(if_index: int4, port_index: int4, vlan_tag_bitmap: byte512)
%table Tbl_Port_VlanUntagBmp(if_index: int4, port_index: int4, vlan_untag_bitmap: byte512)
Tbl_Bridge(ns_id, br_id, br_name, vlan_filter, mac_age_time, mac_thrd_up, mac_thrd_down, 0, 0, 0, 0, br_if_index, macLimitNum) :-
    BR.Bridge(ns_id, br_id, br_name, vlan_filter, br_if_index),
    BR.BridgeMacAttr(ns_id, br_id, mac_age_time, mac_thrd_up, mac_thrd_down, macLimitNum).
Tbl_Vlan(ns_id, br_id, vlan_id, vlan_type, name, desc, 0, untag_port_bitmap, tag_port_bitmap, state, mac_learn, mac_limit_max, mac_limit_act, mac_limit_alarm, br_vlan_if, roamUserExistFlag) :-
    BR.Vlan(ns_id, br_id, vlan_id, vlan_type, name, desc, br_vlan_if),
    BR.VlanPortTagBitmap(ns_id, br_id, vlan_id, tag_port_bitmap),
    BR.VlanPortUnTagBitmap(ns_id, br_id, vlan_id, untag_port_bitmap),
    BR.VlanState(ns_id, br_id, vlan_id, state),
    BR.VlanMacAttr(ns_id, br_id, vlan_id, mac_learn, mac_limit_max, mac_limit_act, mac_limit_alarm),
    a_hpf_usf_roam_vlan_flag(ns_id, br_id, vlan_id, roamUserExistFlag),
    NotEqual2(vlan_id, 0).
Tbl_Port(if_index, port_index, ns_id, br_id, use, link_type, pvid, isolate_grps, mac_learn, mac_learn_act, 0, mac_limit_max, mac_limit_act, mac_limit_alarm, mac_sec, mac_sec_max, mac_sec_act, mac_sec_age_time) :-
    BR.Port(if_index, port_index, ns_id, br_id, pvid, use, link_type),
    BR.PortIsolateGrps(if_index, port_index, isolate_grps),
    BR.PortMacAttr(if_index, -, mac_learn, mac_learn_act, mac_limit_max, mac_limit_act, mac_limit_alarm),
    BR.PortSecurity(if_index, -, mac_sec, mac_sec_max, mac_sec_act, mac_sec_age_time),
    NotEqual4(port_index, -1).
Tbl_Port_VlanTagBmp(if_index, port_index, vlan_tag_bitmap) :-
    BR.Port(if_index, port_index, -, -, -, -, -),
    BR.PortVlanTagBitmap(port_index, -, -, vlan_tag_bitmap),
    NotEqual4(port_index, -1).
Tbl_Port_VlanUntagBmp(if_index, port_index, vlan_untag_bitmap) :-
    BR.Port(if_index, port_index, -, -, -, -, -),
    BR.PortVlanUntagBitmap(port_index, -, -, vlan_untag_bitmap),
    NotEqual4(port_index, -1).
}
namespace Capture {
%readonly Config
%table Config(
    id: int1, aclGroupId: int4, direction: int1, localhost: int1, vlanId: int2, ifIndexList: byte32,
    packetNumber: int2, packetLength: int2, timeout: int8 )
    { index(0(id)), update_partial }
}
namespace Sacl{
Hpf_Group(0, downGroupId, captureAclGroupType, flip) :-
    Acl.GroupCfg(aclGroupId, isIpv6, -, aclGroupType, -, -, -, -),
    GroupChange(-, aclGroupId, flip),
    Capture.Config(-, aclGroupId, direction, -, -, -, -, -, -),
    TranslateGroupType(aclGroupType, isIpv6, captureAclGroupType),
    GenDownGroupID(2 , direction, 8 , 0 , aclGroupId, downGroupId).
Hpf_Rule(downGroupId, _1, _2, _3, _4, _5, _6, flip) :-
    Acl.YActiveRule(aclGroupId, _1, _2, _3, _4, _5, _6),
    GroupChange(-, aclGroupId, flip),
    Capture.Config(-, aclGroupId, direction, -, -, -, -, -, -),
    GenDownGroupID(2 , direction, 8 , 0 , aclGroupId, downGroupId).
Hpf_RefreshGroup(vrId, downGroupId, captureAclGroupType, flip) :-
    Acl.GroupCfg(aclGroupId, isIpv6, -, aclGroupType, -, -, -, -),
    GroupChange(vrId, aclGroupId, flip),
    Capture.Config(-, aclGroupId, direction, -, -, -, -, -, -),
    TranslateGroupType(aclGroupType, isIpv6, captureAclGroupType),
    GenDownGroupID(2 , direction, 8 , 0 , aclGroupId, downGroupId).
}
namespace Fib {
    %readonly ConfigIpv4Fwd, ConfigNhpGroup, hpf_get_nhp_index, NhpGroupAggregate
    %readonly hpf_allocated_nhp_index, RouteAttr2Flags, DecideMulNhp, int4_to_byte4, IsMulNhp
    %table ConfigIpv4Fwd(
        nsId: int4, vrfId: int4, dstIp: int4, maskLen: int4,
        nhpGroupFlag: int1, routeAttr: int2, routeFlags: int2, pathFlags: int4,
        nhpGroupId: int4, primaryLabel: int4, attributeId: int4, qosId: int2)
    {
        index(0(nsId, vrfId, dstIp, maskLen)), update
    }
    %table ConfigNhpGroup(
        nsId: int4, nhpGroupId: int4, nhpNum: int4, vrfId: int4)
    {
        index(0(nsId, nhpGroupId)), update
    }
    %table ConfigNhpGroupNode(
        nsId: int4, nhpGroupId: int4, attributeId: int4, primaryNhpId: int4,
        primaryLabel: int4, backupNhpId: int4, backupLabel: int4, vrfId: int4)
    {
        index(0(nsId, nhpGroupId, attributeId, primaryNhpId, primaryLabel, backupNhpId, backupLabel)), update,
        index(1(nsId, nhpGroupId))
    }
    %table ConfigNhpBasic(
        nhpIndex: int4, vrfId: int4, originNhp: int4, iidFlags: int4, nsId: int4)
    {
        index(0(nhpIndex)), update
    }
    %table ConfigNhpStandard(
        nhpIndex: int4, nextHop: int4, outIfIndex: int4, vrfId: int4, iidFlags: int4, nsId: int4)
    {
        index(0(nhpIndex, nextHop, outIfIndex)), update,
        index(1(nhpIndex))
    }
    %function int4_to_byte4(a: int4 -> b: byte4)
    %function RouteAttr2Flags(routeAttr: int2 -> dir_route: int1, def_route: int1)
    %function DecideMulNhp(nhpNum: int1, nhpGroupHpfIndex: int4, nhpHpfIndex: int4 -> mul_nhp: int1, outNhpHpfIndex: int4)
    %function IsMulNhp(nhpNum: int1)
    %resource hpf_allocated_nhp_index(nhpId: int4, nextHop: int4 -> nhpHpfIndex: int4) {
            index(2(nhpId)),
            sequential(max_size(10000))
    }
    %resource hpf_allocated_nhp_group_index(nhpGroupId: int4 -> nhpGroupHpfIndex: int4) { sequential(max_size(10000)) }
    %table hpf_get_nhp_index(nhpId: int4, nextHop: int4, outIfIndex: int4, vrfId: int4, iidFlags: int4, nhpHpfIndex: int4)
    %aggregate NhpHpfIndexArrayAgg(nhpHpfIndex: int4 -> firstNhpHpfIndex: int4, nhpHpfIndexesLen: int1, nhpHpfIndexes: byte64)
    {
        ordered
    }
    %table NhpGroupJoinNhp(nhpGroupId: int4, nhpId: int4, nhpGroupHpfIndex: int4, nhpHpfIndex: int4)
    {
    }
    %table NhpGroupAggregate(nhpGroupId: int4, nhpGroupHpfIndex: int4, firstNhpHpfIndex: int4, nhpHpfIndexesLen: int1, nhpHpfIndexes: byte64)
    {
    }
    hpf_allocated_nhp_group_index(nhpGroupId, -) :-
        ConfigNhpGroup(-, nhpGroupId, -, -),
        ConfigNhpGroupNode(-, nhpGroupId, -, nhpId, -, -, -, -),
        ConfigNhpBasic(nhpId, -, -, -, -),
        ConfigNhpStandard(nhpId, -, -, -, -, -).
    hpf_allocated_nhp_index(nhpId, nextHop, -) :-
        ConfigNhpBasic(nhpId, -, -, -, -),
        ConfigNhpStandard(nhpId, nextHop, -, -, -, -).
    NhpGroupJoinNhp(nhpGroupId, nhpId, nhpGroupHpfIndex, nhpHpfIndex) :-
        ConfigNhpGroup(-, nhpGroupId, -, -),
        ConfigNhpGroupNode(-, nhpGroupId, -, nhpId, -, -, -, -),
        ConfigNhpBasic(nhpId, -, -, -, -),
        ConfigNhpStandard(nhpId, nextHop, -, -, -, -),
        hpf_allocated_nhp_index(nhpId, nextHop, nhpHpfIndex),
        hpf_allocated_nhp_group_index(nhpGroupId, nhpGroupHpfIndex).
    NhpGroupAggregate(nhpGroupId, nhpGroupHpfIndex, firstNhpHpfIndex, nhpHpfIndexesLen, nhpHpfIndexes) :-
        NhpGroupJoinNhp(nhpGroupId, -, nhpGroupHpfIndex, nhpHpfIndex)
        GROUP-BY (nhpGroupId, nhpGroupHpfIndex) NhpHpfIndexArrayAgg(nhpHpfIndex, firstNhpHpfIndex, nhpHpfIndexesLen, nhpHpfIndexes).
    hpf_get_nhp_index(nhpId, nextHop, outIfIndex, vrfId, iidFlags, nhpHpfIndex) :-
        ConfigNhpBasic(nhpId, -, -, -, -),
        ConfigNhpStandard(nhpId, nextHop, outIfIndex, vrfId, iidFlags, -),
        hpf_allocated_nhp_index(nhpId, nextHop, nhpHpfIndex).
}
namespace Abstract {
    a_ip_route4(vrfId, dip, maskLen, outNhpHpfIndex, mulNhp, routeFlags, pathFlags, dir_route, def_route) :-
        Fib.ConfigIpv4Fwd(-, vrfId, dstIp, maskLen, -, routeAttr, routeFlags, pathFlags, nhpGroupId, -, -, -),
        Fib.RouteAttr2Flags(routeAttr, dir_route, def_route),
        Fib.NhpGroupAggregate(nhpGroupId, nhpGroupHpfIndex, firstNhpHpfIndex, nhpNum, -),
        Fib.DecideMulNhp(nhpNum, nhpGroupHpfIndex, firstNhpHpfIndex, mulNhp, outNhpHpfIndex),
        Fib.int4_to_byte4(dstIp, dip).
    a_nhp4(nhpIndex, nextHop, outIfIndex, vrfId, iidFlags, resIndex) :-
        Fib.hpf_get_nhp_index(nhpIndex, nextHop, outIfIndex, vrfId, iidFlags, resIndex).
    a_nhp_group(0, nhpHpfIndexesLen, nhpHpfIndexes, 0, nhpGroupHpfIndex) :-
        Fib.ConfigNhpGroup(-, nhpGroupId, -, -),
        Fib.NhpGroupAggregate(nhpGroupId, nhpGroupHpfIndex, -, nhpHpfIndexesLen, nhpHpfIndexes),
        Fib.IsMulNhp(nhpHpfIndexesLen).
}
namespace Arp {
%readonly ConfigArp, ConvertIpAddress2Bytes
%readonly VlanArpReplay, ArpCfg, ArpGatewayDupBlock
%function ConvertIpAddress2Bytes(addr: int4 -> addr_bytes: byte4)
%table ConfigArp(
    addr: int4, ifIndex: int4, type: int1, mac: byte6,
    fakeFlag: int1, vlanId: int2, workIfIndex: int4, detectCount:int4, agingTime: int8)
{
    index(0(addr, ifIndex)),
    index(1(ifIndex)),
    index(2(ifIndex, type)),
    index(3(addr)),
    index(4(addr, ifIndex, type)),
    index(5(type)),
    update_partial,
    timeout(field(agingTime), state_function)
}
%table AgingDetectMessage(addr: int4, ifIndex: int4, vlanId: int2,
    workIfIndex: int4, count: int4) { notify }
%function CheckSendingArp(count: int4)
AgingDetectMessage(addr, ifIndex, vlanId, workIfIndex, count) :-
    ConfigArp(addr, ifIndex, -, -, -, vlanId, workIfIndex, count, -),
    CheckSendingArp(count).
%table ConfigFakeTime(ifindex: int4, fakeTime: int4)
{
    index(0(ifindex)), update
}
%table VlanArpReplay(ns_id: int4, br_id: int4, vlan_id: int2, arp_reply: int1)
{
    index(0(ns_id, br_id, vlan_id)), update
}
%table ArpCfg(index: int1, arpGatewayDupEn: int1)
{
    index(0(index)), update
}
%table ArpGatewayDupBlock(ifIndex: int4, brId: int4, srcMac: byte6, vlanId: int2, agingTime: int8)
{
    index(0(ifIndex, brId, srcMac, vlanId)),
    update_partial,
    timeout(field(agingTime))
}
%table Db_FakeTime(ifindex: int4, fake_time: int4)
{
}
%table ArpMiss_HPF(
    addr: int4, ifIndex: int4, workIfIndex: int4, agingTime: int8)
{
    transient(tuple)
}
%function ArpMiss_Pr(addr: int4, ifIndex: int4, workIfIndex: int4, agingTime: int8) {
    access_delta(ConfigArp)
}
ArpMiss_To_NCTL(addr, ifIndex, workIfIndex, agingTime) :-
    ArpMiss_HPF(addr, ifIndex, workIfIndex, agingTime),
    ArpMiss_Pr(addr, ifIndex, workIfIndex, agingTime).
%table ArpMiss_To_NCTL(
    addr: int4, ifIndex: int4, workIfIndex: int4, agingTime: int8)
{
    notify
}
Db_FakeTime(ifIndex, fakeTime) :-
    ConfigFakeTime(ifIndex, fakeTime).
}
namespace Abstract {
    a_arp(0, ip_addr, workIfIndex, if_index, type, mac, fake_flag, vlan_id) :-
        Arp.ConfigArp(ip_addr, if_index, type, mac, fake_flag, vlan_id, workIfIndex, -, -).
}
namespace Hsec {
%readonly PolicyFilterCfg, AppliedPolicyCfg, AutoDefend, AttackSource, StormControlRate, StormControlAction, AntiAttack,
    DefaultCpcar, AdjustCpcar, ProtocolCpcar, Applied_PolicyCpcar, Mid_PolicyCpcar, Mid_AttackSource
%table PolicyFilterCfg(
    policyId:int4,
    filterId:int4,
    groupId:int4) {
    index(0(policyId, filterId)),
    update_partial
}
%table PolicyCpcar(
    policyId:int4,
    protocolType:int4,
    flags:int1,
    pps:int4) {
    index(0(policyId, protocolType, flags)),
    update_partial
}
%table ProtocolCpcar(
    protocolType:int4,
    flags:int1,
    ifIndex:int4
    ) {
        index(0(protocolType, flags, ifIndex)),
        update_partial
    }
%table DefaultCpcar(
    protocolType:int4,
    flags:int1,
    priority:int1,
    pps:int4
    ) {
        index(0(protocolType, flags)),
        update_partial
    }
%table AdjustCpcar(
    protocolType:int4,
    flags:int1,
    pps:int4
    ) {
    index(0(protocolType, flags)),
    update_partial
}
%table AppliedPolicyCfg(
    policyId:int4,
    type:int4) {
    index(0(policyId)),
    update_partial
}
%table AntiAttack(
    attackType:int4,
    enable:int1,
    pps:int4) {
    index(0(attackType)),
    update_partial
}
%table StormControlRate(
    ifIndex:int4,
    packetType:int1,
    pps_max_rate:int4) {
    index(0(ifIndex, packetType)),
    update_partial
}
%table StormControlAction(
    ifIndex:int4,
    action:int1) {
    index(0(ifIndex)),
    update_partial
}
%table AutoDefend(
    policyId:int4,
    enable:int1,
    alarmEnable:int1,
    alarmThreshold:int4,
    penaltyEnable:int1,
    penaltyThreshold:int4,
    defendMask:int1) {
    index(0(policyId)),
    update_partial
}
%table AttackSource(
    sourceType:int1,
    ipv4Addr:int4,
    ipv6Addr:byte16,
    mac:byte6,
    isIpv6:int1,
    timeLeft:int8) {
    index(0(sourceType, ipv4Addr, ipv6Addr, mac, isIpv6)),
    update_partial,
    timeout(field(timeLeft))
}
%table Mid_PolicyCpcar( protocolType:int4, flags:int1, pps:int4)
%table DefCpcar( protocolType:int4, flags:int1, pps:int4)
%table Applied_PolicyCpcar( protocolType:int4, flags:int1, pps:int4)
%table Def_Applied_PolicyCpcar( protocolType:int4, flags:int1, pps:int4)
%table Mid_AttackSource(
    policyId:int4,
    sourceType:int1,
    ipv4Addr:int4,
    ipv6Addr:byte16,
    mac:byte6,
    isIpv6:int1)
Mid_AttackSource(policyId, sourceType, ipv4Addr, ipv6Addr, mac, isIpv6) :-
    AttackSource(sourceType, ipv4Addr, ipv6Addr, mac, isIpv6, -),
    AutoDefend(policyId, -, -, -, 1, -, -),
    AppliedPolicyCfg(policyId, -).
DefCpcar(protocolType, flags, pps) :-
    DefaultCpcar(protocolType, flags, -, pps),
    ProtocolCpcar(protocolType, flags, -).
Applied_PolicyCpcar(protocolType, flags, pps) :-
    PolicyCpcar(policy_id, protocolType, flags, pps),
    AppliedPolicyCfg(policy_id, -).
Def_Applied_PolicyCpcar(protocolType, flags, pps) :-
    DefCpcar(protocolType, flags, pps),
    NOT Applied_PolicyCpcar(protocolType, flags, -).
Def_Applied_PolicyCpcar(protocolType, flags, pps) :-
    Applied_PolicyCpcar(protocolType, flags, pps).
Mid_PolicyCpcar(protocolType, flags, pps) :-
    Def_Applied_PolicyCpcar(protocolType, flags, pps),
    NOT AdjustCpcar(protocolType, flags, -).
Mid_PolicyCpcar(protocolType, flags, pps) :-
    AdjustCpcar(protocolType, flags, pps).
}
namespace Sacl{
Hpf_Group(0, downGroupId, downGroupType, flip) :-
    Acl.GroupCfg(aclGroupId, -, -, groupType, -, -, -, -),
    GroupChange(-, aclGroupId, flip),
    Hsec.AppliedPolicyCfg(policy_id, -),
    Hsec.PolicyFilterCfg(policy_id, -, aclGroupId),
    TranslateGroupType(groupType, 0, downGroupType),
    GenDownGroupID(2 , 0 , 7 , 0 , aclGroupId, downGroupId).
Hpf_Rule(downGroupId, _1, _2, _3, _4, _5, _6, flip) :-
    Acl.YActiveRule(aclGroupId, _1, _2, _3, _4, _5, _6),
    GroupChange(-, aclGroupId, flip),
    Hsec.AppliedPolicyCfg(policy_id, -),
    Hsec.PolicyFilterCfg(policy_id, -, aclGroupId),
    GenDownGroupID(2 , 0 , 7 , 0 , aclGroupId, downGroupId).
Hpf_RefreshGroup(vrId, downGroupId, downGroupType, flip) :-
    Acl.GroupCfg(aclGroupId, -, -, groupType, -, -, -, -),
    GroupChange(vrId, aclGroupId, flip),
    Hsec.AppliedPolicyCfg(policy_id, -),
    Hsec.PolicyFilterCfg(policy_id, -, aclGroupId),
    TranslateGroupType(groupType, 0, downGroupType),
    GenDownGroupID(2 , 0 , 7 , 0 , aclGroupId, downGroupId).
}
namespace Mir {
    %readonly ObserverIndex, PortMirror
    %table ObserverIndex(observeIndex: int2, ifIndex: int4)
    {
        index(0(observeIndex)),
        update_partial
    }
    %table PortMirror(ifIndex: int4, observeIndex: int2, direction: int1)
    {
        index(0(ifIndex, observeIndex, direction)),
        update_partial
    }
}
namespace Acl {
    %readonly GroupCfg, YActiveRule, GroupChangeMark, ChangeGroupMark, AdvRule, TimeRangeCfg, EthRule, PortPoolCfg
    %readonly IpPoolCfg
%table GroupCfg(
    aclGroupId:int4, isIPv6:int1, aclVrId: int4, aclGroupType:int4,
    aclNumber:int4, aclName:byte65, isNameAcl:int1, vsysid:int4)
{
    index(0(aclGroupId, isIPv6)),
    update_partial
}
%table AdvRule(
    aclIndex:int4, aclVrId: int4, aclGroupId:int4, aclPriority:int4,
    actionType:int1, logFlag:int1, aclVpnIndex:int4, trngStatus:int1,
    aclCondMask:int4, anyFlag:int1, protocol:int1, fragType:int1,
    tos:int1, srcIpAddr:int4, srcIpMask:int4, dstIpAddr:int4,
    dstIpMask:int4, srcPortBeg:int2, srcPortEnd:int2, dstPortBeg:int2,
    dstPortEnd:int2, srcPortOp:int1, dstPortOp:int1, tcpFlag:int1,
    icmpType:int1, icmpCode:int1, dscp:int1, ipPre:int1,
    trngId:int4, srcIpPool:int4, dstIpPool:int4, srcPortPool:int4,
    dstPortPool:int4, igmpType:int1, pktLenOp:int1, pktLenBgn:int2,
    pktLenEnd:int2, tcpEstablished:int1, ttlOp:int1, ttlBgn:int1,
    ttlEnd:int1, vni:int4, tcpFlagMask:int1, srcUclIndex:int2, dstUclIndex:int2,
    uclSrcType:int1, uclDstType:int1, tableType:int1)
{
    index(0(aclIndex, aclVrId, aclGroupId)),
    index(1(aclVrId, aclGroupId)),
    update_partial
}
%table EthRule(
    aclIndex:int4, aclVrId:int4, aclGroupId:int4,
    actionType:int1,
    frameType:int2, frameMask:int2,
    srcMac:byte6, srcMacMask:byte6,
    dstMac:byte6, dstMacMask:byte6,
    vlanId:int2, vlanIdMask:int2,
    value8021p:int1,
    aclCondMask:int4,
    trngStatus:int1, trngId:int4, aclPriority:int4)
{
    index(0(aclIndex, aclVrId, aclGroupId)),
    update_partial
}
%table TimeRangeCfg(
    trngId:int4, vrId:int4, trngInnerId:int4, trngStatus:int1) {
    index(0(trngId)),
    update_partial
}
%table PortPoolCfg(
    vrId:int4, poolId:int4, portId:int4, rangeOp:int1,
    startPort:int4, endPort:int4) {
    index(0(vrId, poolId, portId)),
    update_partial
}
%table IpPoolCfg(
    vrId:int4, poolId:int4, ipId:int4, ipAddr:int4, ipMask:int4) {
    index(0(vrId, poolId, ipId)),
    update_partial
}
%table GroupChangeMark(vrId: int4, aclGroupId: int4)
     { transient(tuple) }
%aggregate ConvertPort( portBeg: int2, portEnd: int2 -> portValue: int2, portMask: int2)
{ ordered, many_to_many }
%aggregate ConvertAdvRules(
    aclIndex:int4, aclVrId: int4, aclPriority:int4,
    actionType:int1, logFlag:int1, aclVpnIndex:int4, aclCondMask:int4,
    anyFlag:int1, protocol:int1, fragType:int1, tos:int1,
    tcpFlag:int1, icmpType:int1, icmpCode:int1, dscp:int1,
    ipPre:int1,igmpType:int1, pktLenOp:int1, pktLenBgn:int2,
    pktLenEnd:int2, tcpEstablished:int1, ttlOp:int1, ttlBgn:int1,
    ttlEnd:int1, vni:int4, tcpFlagMask:int1, srcUclIndex:int2,
    dstUclIndex:int2, uclSrcType:int1, uclDstType:int1, tableType: int1,
    srcPortValue: int2, srcPortMask: int2, destPortValue: int2, destPortMask: int2,
    srcIpAddr: int4, srcIpMask: int4, trngId: int4, trngStatus: int1,
    dstIpAddr: int4, dstIpMask: int4
    ->
    protoFamily: int4, priority: int4, ifDeny: int1, statId: int4,
    fields_data: byte192, fields_len: int4)
{ ordered, many_to_many }
%aggregate ConvertEthRules(
    aclIndex:int4, aclVrId:int4,
    actionType:int1,
    frameType:int2, frameMask:int2,
    srcMac:byte6, srcMacMask:byte6,
    dstMac:byte6, dstMacMask:byte6,
    vlanId:int2, vlanIdMask:int2,
    value8021p:int1,
    aclCondMask:int4,
    trngStatus:int1, trngId:int4, aclPriority:int4
    ->
    protoFamily: int4, priority: int4, ifDeny: int1, statId: int4,
    fields_data: byte192, fields_len: int4)
{ ordered, many_to_many }
%function ConvertPortRange(aclCondMask:int4, rangeOp: int1, startPort: int4, endPort: int4 -> portBeg: int2, portEnd: int2)
%function ChangeGroupMark(vrId: int4, aclGroupId:int4)
{ access_delta (GroupChangeMark) }
%function CheckCondMark(aclCondMask:int4, bitNum:int1)
%table CommRule (
    aclIndex:int4, aclVrId: int4, aclGroupId:int4, aclPriority:int4,
    actionType:int1, logFlag:int1, aclVpnIndex:int4, aclCondMask:int4,
    anyFlag:int1, protocol:int1, fragType:int1, tos:int1,
    tcpFlag:int1, icmpType:int1, icmpCode:int1, dscp:int1,
    ipPre:int1,igmpType:int1, pktLenOp:int1, pktLenBgn:int2,
    pktLenEnd:int2, tcpEstablished:int1, ttlOp:int1, ttlBgn:int1,
    ttlEnd:int1, vni:int4, tcpFlagMask:int1, srcUclIndex:int2,
    dstUclIndex:int2, uclSrcType:int1, uclDstType:int1)
CommRule(aclIndex, aclVrId, aclGroupId, aclPriority, actionType, logFlag, aclVpnIndex, aclCondMask,
        anyFlag, protocol, fragType, tos, tcpFlag, icmpType, icmpCode, dscp,
        ipPre, igmpType, pktLenOp, pktLenBgn, pktLenEnd, tcpEstablished, ttlOp, ttlBgn,
        ttlEnd, vni, tcpFlagMask, srcUclIndex, dstUclIndex, uclSrcType, uclDstType) :-
    AdvRule(
        aclIndex, aclVrId, aclGroupId, aclPriority, actionType, logFlag, aclVpnIndex, -,
        aclCondMask, anyFlag, protocol, fragType, tos, -, -, -, -, -, -, -, -, -, -, tcpFlag,
        icmpType, icmpCode, dscp, ipPre, -, -, -, -,-, igmpType, pktLenOp, pktLenBgn, pktLenEnd, tcpEstablished, ttlOp, ttlBgn,
        ttlEnd, vni, tcpFlagMask, srcUclIndex, dstUclIndex, uclSrcType, uclDstType, 1).
%table SrcPortRangeRule(aclGroupId:int4, aclIndex:int4, srcPortBeg:int2, srcPortEnd: int2)
SrcPortRangeRule(aclGroupId, aclIndex, srcPortBeg, srcPortEnd):-
    AdvRule(
        aclIndex, -, aclGroupId, -, -, -, -, -,
        aclCondMask, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, srcPortPool,
        -, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -),
    PortPoolCfg(-, srcPortPool, -, rangeOp, startPort, endPort),
    ConvertPortRange(aclCondMask, rangeOp, startPort, endPort, srcPortBeg, srcPortEnd).
SrcPortRangeRule(aclGroupId, aclIndex, srcPortBeg, srcPortEnd):-
    AdvRule(
        aclIndex, -, aclGroupId, -, -, -, -, -,
        -, -, -, -, -, -, -, -,
        -, srcPortBeg, srcPortEnd, -, -, -, -, -,
        -, -, -, -, -, -, -, 0,
        -, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -).
%table SrcPortRule(aclGroupId:int4, aclIndex:int4, srcPortValue:int2, srcPortMask: int2)
SrcPortRule(aclGroupId, aclIndex, srcPortValue, srcPortMask):-
    SrcPortRangeRule(aclGroupId, aclIndex, srcPortBeg, srcPortEnd) GROUP-BY (aclGroupId, aclIndex)
    ConvertPort(srcPortBeg, srcPortEnd, srcPortValue, srcPortMask).
%table DestPortRangeRule(aclGroupId:int4, aclIndex:int4, dstPortBeg:int2, dstPortEnd: int2)
DestPortRangeRule(aclGroupId, aclIndex, dstPortBeg, dstPortEnd):-
    AdvRule(
        aclIndex, -, aclGroupId, -, -, -, -, -,
        aclCondMask, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -,
        dstPortPool, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -),
    PortPoolCfg(-, dstPortPool, -, rangeOp, startPort, endPort),
    ConvertPortRange(aclCondMask, rangeOp, startPort, endPort, dstPortBeg, dstPortEnd).
DestPortRangeRule(aclGroupId, aclIndex, dstPortBeg, dstPortEnd):-
    AdvRule(
        aclIndex, -, aclGroupId, -, -, -, -, -,
        -, -, -, -, -, -, -, -,
        -, -, -, dstPortBeg, dstPortEnd, -, -, -,
        -, -, -, -, -, -, -, -,
        0, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -).
%table DestPortRule(aclGroupId:int4, aclIndex:int4, destPortValue:int2, destPortMask: int2)
DestPortRule(aclGroupId, aclIndex, destPortValue, destPortMask):-
    DestPortRangeRule(aclGroupId, aclIndex, destPortBeg, destPortEnd) GROUP-BY (aclGroupId, aclIndex)
    ConvertPort(destPortBeg, destPortEnd, destPortValue, destPortMask).
%table srcIpRule(aclGroupId:int4, aclIndex:int4, srcIpAddr:int4, srcIpMask: int4)
srcIpRule(aclGroupId, aclIndex, srcIpAddr, srcIpMask):-
    AdvRule(
        aclIndex, -, aclGroupId, -, -, -, -, -,
        aclCondMask, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -,
        -, -, -, -, -, srcIpPool, -, -,
        -, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -),
    IpPoolCfg(-, srcIpPool, -, srcIpAddr, srcIpMask),
    CheckCondMark(aclCondMask, 22).
srcIpRule(aclGroupId, aclIndex, srcIpAddr, srcIpMask):-
    AdvRule(
        aclIndex, -, aclGroupId, -, -, -, -, -,
        -, -, -, -, -, srcIpAddr, srcIpMask, -,
        -, -, -, -, -, -, -, -,
        -, -, -, -, -, 0, -, -,
        -, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -).
%table destIpRule(aclGroupId:int4, aclIndex:int4, dstIpAddr:int4, dstIpMask: int4)
destIpRule(aclGroupId, aclIndex, dstIpAddr, dstIpMask):-
    AdvRule(
        aclIndex, -, aclGroupId, -, -, -, -, -,
        aclCondMask, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -,
        -, -, -, -, -, -, dstIpPool, -,
        -, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -),
    IpPoolCfg(-, dstIpPool, -, dstIpAddr, dstIpMask),
    CheckCondMark(aclCondMask, 23).
destIpRule(aclGroupId, aclIndex, dstIpAddr, dstIpMask):-
    AdvRule(
        aclIndex, -, aclGroupId, -, -, -, -, -,
        -, -, -, -, -, -, -, dstIpAddr,
        dstIpMask, -, -, -, -, -, -, -,
        -, -, -, -, -, -, 0, -,
        -, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -).
%table TimRangeRule(aclGroupId:int4, aclIndex:int4, trngId:int4, trngStatus: int1)
TimRangeRule(aclGroupId, aclIndex, trngId, trngStatus):-
    AdvRule(
        aclIndex, -, aclGroupId, -, -, -, -, -,
        -, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -,
        -, -, -, -, trngId, -, -, -,
        -, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -),
    TimeRangeCfg(trngId, -, -, trngStatus).
TimRangeRule(aclGroupId, aclIndex, 0, trngStatus):-
    AdvRule(
        aclIndex, -, aclGroupId, -, -, -, -, trngStatus,
        -, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -,
        -, -, -, -, 0, -, -, -,
        -, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -).
TimRangeRule(aclGroupId, aclIndex, trngId, trngStatus):-
    EthRule(
        aclIndex, -, aclGroupId,
        -, -, -, -, -, -, -, -, -, -,
        -, -, trngId, -),
    TimeRangeCfg(trngId, -, -, trngStatus).
TimRangeRule(aclGroupId, aclIndex, 0, trngStatus):-
    EthRule(
        aclIndex, -, aclGroupId,
        -, -, -, -, -, -, -, -, -, -,
        -, trngStatus, 0, -).
%table TransedRule (
    aclIndex:int4, aclVrId: int4, aclGroupId:int4, aclPriority:int4,
    actionType:int1, logFlag:int1, aclVpnIndex:int4, aclCondMask:int4,
    anyFlag:int1, protocol:int1, fragType:int1, tos:int1,
    tcpFlag:int1, icmpType:int1, icmpCode:int1, dscp:int1,
    ipPre:int1,igmpType:int1, pktLenOp:int1, pktLenBgn:int2,
    pktLenEnd:int2, tcpEstablished:int1, ttlOp:int1, ttlBgn:int1,
    ttlEnd:int1, vni:int4, tcpFlagMask:int1, srcUclIndex:int2,
    dstUclIndex:int2, uclSrcType:int1, uclDstType:int1, tableType: int1,
    srcPortValue: int2, srcPortMask: int2, destPortValue: int2, destPortMask: int2,
    srcIpAddr: int4, srcIpMask: int4, trngId: int4, trngStatus: int1,
    dstIpAddr: int4, dstIpMask: int4)
{
    index(0(aclIndex, aclVrId, aclGroupId))
}
TransedRule(
        aclIndex, aclVrId, aclGroupId, aclPriority, actionType, logFlag, aclVpnIndex, aclCondMask,
        anyFlag, protocol, fragType, tos, tcpFlag, icmpType, icmpCode, dscp,
        ipPre, igmpType, pktLenOp, pktLenBgn, pktLenEnd, tcpEstablished, ttlOp, ttlBgn,
        ttlEnd, vni, tcpFlagMask, srcUclIndex, dstUclIndex, uclSrcType, uclDstType, 1, srcPortValue, srcPortMask, destPortValue, destPortMask,
        srcIpAddr, srcIpMask, trngId, trngStatus, dstIpAddr, dstIpMask):-
    CommRule(aclIndex, aclVrId, aclGroupId, aclPriority, actionType, logFlag, aclVpnIndex, aclCondMask,
        anyFlag, protocol, fragType, tos, tcpFlag, icmpType, icmpCode, dscp,
        ipPre, igmpType, pktLenOp, pktLenBgn, pktLenEnd, tcpEstablished, ttlOp, ttlBgn,
        ttlEnd, vni, tcpFlagMask, srcUclIndex, dstUclIndex, uclSrcType, uclDstType),
    SrcPortRule(aclGroupId, aclIndex, srcPortValue, srcPortMask),
    DestPortRule(aclGroupId, aclIndex, destPortValue, destPortMask),
    srcIpRule(aclGroupId, aclIndex, srcIpAddr, srcIpMask),
    destIpRule(aclGroupId, aclIndex, dstIpAddr, dstIpMask),
    TimRangeRule(aclGroupId, aclIndex, trngId, trngStatus),
    ChangeGroupMark(aclVrId, aclGroupId).
%table TransedEthRule (
    aclIndex:int4, aclVrId: int4, aclGroupId:int4,
    actionType:int1,
    frameType:int2, frameMask:int2,
    srcMac:byte6, srcMacMask:byte6,
    dstMac:byte6, dstMacMask:byte6,
    vlanId:int2, vlanIdMask:int2,
    value8021p:int1,
    aclCondMask:int4,
    trngStatus:int1, trngId:int4, aclPriority:int4)
TransedEthRule(
        aclIndex, aclVrId, aclGroupId, actionType, frameType, frameMask,
        srcMac, srcMacMask, dstMac, dstMacMask, vlanId, vlanIdMask,
        value8021p, aclCondMask, trngStatus, trngId, aclPriority):-
    EthRule(aclIndex, aclVrId, aclGroupId, actionType, frameType, frameMask,
        srcMac, srcMacMask, dstMac, dstMacMask, vlanId, vlanIdMask,
        value8021p, aclCondMask, trngStatus, trngId, aclPriority),
    TimRangeRule(aclGroupId, aclIndex, trngId, trngStatus),
    ChangeGroupMark(aclVrId, aclGroupId).
%table YActiveRule(
    groupId: int4, protoFamily: int4, priority: int4, ifDeny: int1, statId: int4,
    fields_data: byte192, fields_len: int4)
YActiveRule(aclGroupId, protoFamily, priority, ifDeny, statId, fields_data, fields_len) :-
    TransedRule(
        aclIndex, aclVrId, aclGroupId, aclPriority, actionType, logFlag, aclVpnIndex, aclCondMask,
        anyFlag, protocol, fragType, tos, tcpFlag, icmpType, icmpCode, dscp,
        ipPre, igmpType, pktLenOp, pktLenBgn, pktLenEnd, tcpEstablished, ttlOp, ttlBgn,
        ttlEnd, vni, tcpFlagMask, srcUclIndex, dstUclIndex, uclSrcType, uclDstType, tableType,
        srcPortValue, srcPortMask, destPortValue, destPortMask, srcIpAddr, srcIpMask, trngId,
        trngStatus, dstIpAddr, dstIpMask) GROUP-BY (aclGroupId)
    ConvertAdvRules(
        aclIndex, aclVrId, aclPriority, actionType, logFlag, aclVpnIndex, aclCondMask,
        anyFlag, protocol, fragType, tos, tcpFlag, icmpType, icmpCode, dscp,
        ipPre, igmpType, pktLenOp, pktLenBgn, pktLenEnd, tcpEstablished, ttlOp, ttlBgn,
        ttlEnd, vni, tcpFlagMask, srcUclIndex, dstUclIndex, uclSrcType, uclDstType,
        tableType, srcPortValue, srcPortMask, destPortValue, destPortMask,
        srcIpAddr, srcIpMask, trngId, trngStatus, dstIpAddr, dstIpMask,
        protoFamily, priority, ifDeny, statId, fields_data, fields_len).
YActiveRule(aclGroupId, protoFamily, priority, ifDeny, statId, fields_data, fields_len) :-
    TransedEthRule(
        aclIndex, aclVrId, aclGroupId, actionType, frameType, frameMask,
        srcMac, srcMacMask, dstMac, dstMacMask, vlanId, vlanIdMask,
        value8021p, aclCondMask, trngStatus, trngId, aclPriority) GROUP-BY (aclGroupId)
    ConvertEthRules(
        aclIndex, aclVrId, actionType, frameType, frameMask,
        srcMac, srcMacMask, dstMac, dstMacMask, vlanId, vlanIdMask,
        value8021p, aclCondMask, trngStatus, trngId, aclPriority,
        protoFamily, priority, ifDeny, statId, fields_data, fields_len).
}
namespace Sacl {
%readonly GroupChange, TranslateGroupType, GenDownGroupID
%table Hpf_Group(
    group_id: int4, group_type: int4, instance_id: int4, flip: int4) {
        index(0(group_id, group_type)),
        tbm
    }
%table Hpf_Rule(
    groupId: int4, protoFamily: int4, priority: int4, ifDeny: int1, statId: int4,
    fields_data: byte192, fields_len: int4, flip: int4) {
        index(0(groupId, protoFamily, priority)),
        tbm
    }
%table Hpf_RefreshGroup(groupId: int4, groupType:int4, vrId: int4, flip: int4) {
    index(0(groupId, groupType)),
    tbm
}
%function GenDownGroupID(appType:int1, direction:int1, policyType:int1, appValue:int4, aclGroupId:int4 -> downGroupId: int4)
%function TranslateGroupType(groupType:int4, isIpv6:int1 -> downGroupType: int4)
%table GroupActive(vrId: int4, aclGroupId: int4)
%table GroupChange(vrId: int4, aclGroupId: int4, flip: int4) { transient(field(flip)) }
GroupChange(v, g, 0) :- GroupActive(v, g).
GroupChange(v, g, 1) :- Acl.GroupChangeMark(v, g).
%precedence Acl.YActiveRule, GroupChange
%precedence Hpf_Group, Hpf_Rule, Hpf_RefreshGroup
    GroupActive(0, g) :- Usf.PublicAcl_1(g, -, -, -, -).
    null(0) :- GroupChange(0, g, 1), Usf.AclRuleList(gg, g, -), Acl.ChangeGroupMark(0, gg).
    Hpf_RefreshGroup(_2, 0, _1, _4) :- Usf.UsfGroup(_1, _2, -, _4).
    Hpf_Group(_2, 0, _1, _4) :- Usf.UsfGroup(_1, _2, -, _4).
    Hpf_Rule(downGroupId, protoFamily, prio, _3, _4, _5, _6, flip) :-
        Acl.YActiveRule(gid2, protoFamily, prio2, _3, _4, _5, _6),
        Acl.GroupCfg(gid2, -, -, -, gid1, -, -, -),
        Usf.AclRuleList(gid0, gid1, prio1),
        Usf.ConvertPrio(prio1, prio2, prio),
        GroupChange(-, gid0, flip),
        GenDownGroupID(127, 0, 0, 0, gid0, downGroupId).
    %table VapDownGroup(did: int4, dtype: int4, instance_id: int4, flip: int4)
    VapDownGroup(did, type, 0, flip) :-
        Ifm.VapAclGroup(gid, type), GenDownGroupID(126, 0, 0, 0, gid, did),
        GroupChange(-, gid, flip).
    Hpf_RefreshGroup(_1, _2, _3, _4) :- VapDownGroup(_1, _2, _3, _4).
    Hpf_Group(_1, _2, _3, _4) :- VapDownGroup(_1, _2, _3, _4).
    Hpf_Rule(did, _1, _2, _3, _4, _5, _6, flip) :-
        Acl.YActiveRule(gid, _1, _2, _3, _4, _5, _6),
        GroupChange(-, gid, flip),
        Ifm.VapAclGroup(gid, -),
        GenDownGroupID(126, 0, 0, 0, gid, did).
}
%table arp(
    ip_address: byte4, if_index: int4, arp_type: int1, mac_address: byte6, fake_flag: int1, work_if_index: int4, vlanid: int2)
{
    index(0(ip_address, if_index, arp_type)), external
}
arp(ip_address, if_index, arp_type, mac_address, fake_flag, workIfIndex, vlanid) :-
    Arp.ConfigArp(addr, if_index, arp_type, mac_address, fake_flag, vlanid, workIfIndex, -, -),
    Arp.ConvertIpAddress2Bytes(addr, ip_address).
%table if_base(
    if_index: int4, namespace_id: int4, if_name: byte64,
    if_alias: byte64, if_type: int2, if_cfg_bandwidth: int4, if_show_type: int2, description: byte243) {index(0(if_index)), external}
if_base(if_index, namespace_id, if_name, if_alias, if_type, 0, if_show_type, "00") :-
Abstract.a_if_name(if_index, namespace_id, if_name, if_type, if_show_type, if_alias, -).
%table if_phy(
    if_index: int4, if_bw: int8, phy_state: int1, admin_state: int1, onboard_state: int1,
    tb: int4, tp: int4, phy_mac: byte6, eth_autoneg: int1, eth_speed: int8,
    eth_duplex: int1, eth_combo: int1, eth_loopback: int1, dev_id: int4, chassis_id: int4, slot_id: int4, card_id: int4,
    unit_id: int4, port_id: int4, chip_unit: int1, oda_phy_port: int1, service_type: int1, autoneg_cap: int1,
    autoneg_adv_cap: int2, medium: int1, mru: int2, eth_class: int1, eth_type: int1,
    nat_enable: int1, nat64_enable: int1, stp_enable: int1) {index(0(if_index)), external }
if_phy(
    if_index, if_bw, phy_state, admin_state, onboard_state,
    tb, tp, phy_mac, eth_autoneg, eth_speed,
    eth_duplex, eth_combo, eth_loopback, dev_id, chassis_id, slot_id, card_id,
    unit_id, port_id, chip_unit, oda_phy_port, service_type, autoneg_cap, autoneg_adv_cap, medium,
    mru, eth_class, 0, 0, 0, 0) :-
Abstract.a_if_phy(
    if_index, if_bw, phy_state, admin_state, onboard_state,
    tb, tp, chip_unit, oda_phy_port, phy_mac, eth_autoneg, eth_speed,
    eth_duplex, eth_combo, eth_loopback, dev_id, chassis_id, slot_id, card_id,
    unit_id, port_id, service_type, autoneg_cap, autoneg_adv_cap, medium,
    mru, -, eth_class, -, -, -) .
%table if_link(
    if_index: int4, link_type: int4, link_state: int1, oper_state: int1
) {index(0(if_index)), external}
if_link(if_index, link_type, link_state, oper_state) :-
Abstract.a_if_link(if_index, link_type, link_state, oper_state).
%table if_route(
    if_index: int4, vrf_id: int4, ipv4_state: int1, mtu_v4: int4, df_flag: int4, ipv6_state: int1, mtu_v6: int4
) {index(0(if_index)), external}
if_route(if_index, vrf_id, ipv4_state, mtu_v4, df_flag, ipv6_state, mtu_v6) :-
Abstract.a_if_net(if_index, vrf_id, df_flag, mtu_v4, ipv4_state),
Abstract.a_if_net6(if_index, -, mtu_v6, ipv6_state).
%table bridge(ns_id: int4, br_id: int4, br_name: byte16, vlan_filter: int1,
              mac_age_time: int4, mac_thrd_up: int1, mac_thrd_down: int1,
              mac_pri1_allow_flapping: int1, mac_pri2_allow_flapping: int1, mac_pri3_allow_flapping: int1, mac_pri4_allow_flapping: int1)
{
    index(0(ns_id, br_id)),
    external
}
bridge(ns_id, br_id, br_name, vlan_filter, mac_age_time, mac_thrd_up, mac_thrd_down, mac_pri1_allow_flapping, mac_pri2_allow_flapping, mac_pri3_allow_flapping, mac_pri4_allow_flapping) :-
    Abstract.Tbl_Bridge(ns_id, br_id, br_name, vlan_filter, mac_age_time, mac_thrd_up, mac_thrd_down, mac_pri1_allow_flapping, mac_pri2_allow_flapping, mac_pri3_allow_flapping, mac_pri4_allow_flapping, -, -).
%table vlan(ns_id: int4, br_id: int4, vlan_id: int2, vlan_type: int1, name: byte32, desc: byte81, mc_id: int4,
            untag_port_bitmap: byte32, tag_port_bitmap: byte32, state: int1,
            mac_learn: int1, mac_limit_max: int4, mac_limit_act: int1, mac_limit_alarm: int1)
{
    index(0(ns_id, br_id, vlan_id)),
    external
}
vlan(ns_id, br_id, vlan_id, vlan_type, name, desc, mc_id, untag_port_bitmap, tag_port_bitmap, state, mac_learn, mac_limit_max, mac_limit_act, mac_limit_alarm) :-
    Abstract.Tbl_Vlan(ns_id, br_id, vlan_id, vlan_type, name, desc, mc_id, untag_port_bitmap, tag_port_bitmap, state, mac_learn, mac_limit_max, mac_limit_act, mac_limit_alarm, -, -).
%table port(if_index: int4, port_index: int4, ns_id: int4, br_id: int4, trunk_id: int4, use: int1, link_type: int1,
            pvid: int2, vlan_tag_bitmap: byte512, vlan_untag_bitmap: byte512, isolate_grps: byte64,
            mac_learn: int1, mac_learn_act: int1, mac_learn_pri: int1,
            mac_limit_max: int4, mac_limit_act: int1, mac_limit_alarm: int1,
            mac_sec: int1, mac_sec_max: int4, mac_sec_act: int1, mac_sec_age_time: int4)
{
    index(0(if_index)),
    external
}
port(if_index, port_index, ns_id, br_id, 0, use, link_type, pvid, vlan_tag_bitmap, vlan_untag_bitmap, isolate_grps, mac_learn, mac_learn_act, mac_learn_pri, mac_limit_max, mac_limit_act, mac_limit_alarm, mac_sec, mac_sec_max, mac_sec_act, mac_sec_age_time) :-
    Abstract.Tbl_Port(if_index, port_index, ns_id, br_id, use, link_type, pvid, isolate_grps, mac_learn, mac_learn_act, mac_learn_pri, mac_limit_max, mac_limit_act, mac_limit_alarm, mac_sec, mac_sec_max, mac_sec_act, mac_sec_age_time),
    Abstract.Tbl_Port_VlanTagBmp(if_index, port_index, vlan_tag_bitmap),
    Abstract.Tbl_Port_VlanUntagBmp(if_index, port_index, vlan_untag_bitmap).
%table acl_group(
    acl_groupid:int4, isipv6:int1, vsid:int2, vsysid:int4, acl_number:int4, acl_group_type:int4, acl_name:byte65)
{
    index(0(acl_groupid, isipv6)),
    external
}
acl_group(aclGroupId, isIPv6, 0, vsysid, aclNumber, aclGroupType, aclName) :-
    Acl.GroupCfg(aclGroupId, isIPv6, -, aclGroupType, aclNumber, aclName, -, vsysid).
%table acl_base(
    vrid:int4, acl_index:int4, acl_group_id:int4, vrf_index:int4, acl_priority:int4, cond_mask:int4,
    time_range_id:int4, time_range_status:int1, acl_action_type:int1, any_flag:int1, frag_type:int1, src_ip:int4,
    src_ip_mask:int4, log_flag:int1)
{
    index(0(vrid, acl_index, acl_group_id)),
    external
}
acl_base(vrid, acl_index, acl_group_id, vrf_index, acl_priority, cond_mask,
        0, time_range_status, actionType, anyFlag, fragType, srcIpAddr,
        srcIpMask, logFlag) :-
    Acl.AdvRule(acl_index, vrid, acl_group_id, acl_priority, actionType, logFlag,
                vrf_index, time_range_status, cond_mask, anyFlag, -, fragType,
                -, srcIpAddr, srcIpMask, -, -, -,
                -, -, -, -, -, -,
                -, -, -, -, 0, -,
                -, -, -, -, -, -,
                -, -, -, -, -, -, -, -, -, -, -, 0).
acl_base(vrid, acl_index, acl_group_id, vrf_index, acl_priority, cond_mask,
        time_range_id, time_range_status, actionType, anyFlag, fragType, srcIpAddr,
        srcIpMask, logFlag) :-
    Acl.AdvRule(acl_index, vrid, acl_group_id, acl_priority, actionType, logFlag,
                vrf_index, -, cond_mask, anyFlag, -, fragType,
                -, srcIpAddr, srcIpMask, -, -, -,
                -, -, -, -, -, -,
                -, -, -, -, time_range_id, -,
                -, -, -, -, -, -,
                -, -, -, -, -, -, -, -, -, -, -, 0),
    Acl.TimeRangeCfg(time_range_id, -, -, time_range_status).
%table acl_adv(
    vrid: int4, acl_index:int4, acl_group_id:int4, vrf_index:int4, acl_priority:int4, cond_mask:int4,
    time_range_id:int4, time_range_status:int1, acl_action_type:int1, src_ip:int4, src_ip_mask:int4, dest_ip:int4,
    dest_ip_mask:int4, s_ippool_index:int4, d_ippool_index:int4, b_src_port:int2, e_src_port:int2, b_dst_port:int2,
    e_dst_port:int2, s_port_pool_id:int4, d_port_pool_id:int4, log_flag:int1, any_flag:int1, protocol:int1,
    frag_type:int1, tos:int1, tcp_flag:int1, icmp_type:int1, icmp_code:int1, dscp:int1,
    ip_prec:int1, igmp_type:int1, ttl_expired:int1, established:int1, tcp_flag_mask:int1, tcp_established:int1,
    ttl_op:int1, ttl_bgn:int1, ttl_end:int1)
{
    index(0(vrid, acl_index, acl_group_id)),
    external
}
acl_adv(aclVrId, aclIndex, aclGroupId, aclVpnIndex, aclPriority, aclCondMask,
        0, time_range_status, actionType, srcIpAddr, srcIpMask, dstIpAddr,
        dstIpMask, srcIpPool, dstIpPool, srcPortBeg, srcPortEnd, dstPortBeg,
        dstPortEnd, srcPortPool, dstPortPool, logFlag, anyFlag, protocol,
        fragType, tos, tcpFlag, icmpType, icmpCode, dscp,
        ipPre, igmpType, 0, 0, tcpFlagMask, tcpEstablished,
        ttlOp, ttlBgn, ttlEnd) :-
    Acl.AdvRule(aclIndex, aclVrId, aclGroupId, aclPriority, actionType, logFlag,
                aclVpnIndex, time_range_status, aclCondMask, anyFlag, protocol, fragType,
                tos, srcIpAddr, srcIpMask, dstIpAddr, dstIpMask, srcPortBeg,
                srcPortEnd, dstPortBeg, dstPortEnd, -, -, tcpFlag,
                icmpType, icmpCode, dscp, ipPre, 0, srcIpPool,
                dstIpPool, srcPortPool, dstPortPool, igmpType, -, -,
                -, tcpEstablished, ttlOp, ttlBgn, ttlEnd, -, tcpFlagMask, -, -, -, -, 1).
acl_adv(aclVrId, aclIndex, aclGroupId, aclVpnIndex, aclPriority, aclCondMask,
        time_range_id, time_range_status, actionType, srcIpAddr, srcIpMask, dstIpAddr,
        dstIpMask, srcIpPool, dstIpPool, srcPortBeg, srcPortEnd, dstPortBeg,
        dstPortEnd, srcPortPool, dstPortPool, logFlag, anyFlag, protocol,
        fragType, tos, tcpFlag, icmpType, icmpCode, dscp,
        ipPre, igmpType, 0, 0, tcpFlagMask, tcpEstablished,
        ttlOp, ttlBgn, ttlEnd) :-
    Acl.AdvRule(aclIndex, aclVrId, aclGroupId, aclPriority, actionType, logFlag,
                aclVpnIndex, -, aclCondMask, anyFlag, protocol, fragType,
                tos, srcIpAddr, srcIpMask, dstIpAddr, dstIpMask, srcPortBeg,
                srcPortEnd, dstPortBeg, dstPortEnd, -, -, tcpFlag,
                icmpType, icmpCode, dscp, ipPre, time_range_id, srcIpPool,
                dstIpPool, srcPortPool, dstPortPool, igmpType, -, -,
                -, tcpEstablished, ttlOp, ttlBgn, ttlEnd, -,tcpFlagMask, -, -, -, -, 1),
    Acl.TimeRangeCfg(time_range_id, -, -, time_range_status).
%table acl_eth(
    vrid:int4, acl_index:int4, acl_group_id:int4,
    acl_action_type:int1, frame_type:int2, frame_mask:int2,
    src_mac:byte6, src_mac_mask:byte6, dest_mac:byte6, dest_mac_mask:byte6,
    vlan_id:int2, vlan_id_mask:int2,
    value_8021p:int1,
    cond_mask:int4, time_range_status:int1, time_range_id:int4, acl_priority:int4)
{
    index(0(vrid, acl_index, acl_group_id)),
    external
}
acl_eth(vrid, acl_index, acl_group_id,
        acl_action_type, frame_type, frame_mask,
        src_mac, src_mac_mask, dest_mac, dest_mac_mask,
        vlan_id, vlan_id_mask, value_8021p,
        cond_mask, time_range_status, 0, acl_priority) :-
    Acl.EthRule(acl_index, vrid, acl_group_id,
                acl_action_type, frame_type, frame_mask,
                src_mac, src_mac_mask, dest_mac, dest_mac_mask,
                vlan_id, vlan_id_mask, value_8021p,
                cond_mask, time_range_status, 0, acl_priority).
acl_eth(vrid, acl_index, acl_group_id,
        acl_action_type, frame_type, frame_mask,
        src_mac, src_mac_mask, dest_mac, dest_mac_mask,
        vlan_id, vlan_id_mask, value_8021p,
        cond_mask, time_range_status, time_range_id, acl_priority) :-
    Acl.EthRule(acl_index, vrid, acl_group_id,
                acl_action_type, frame_type, frame_mask,
                src_mac, src_mac_mask, dest_mac, dest_mac_mask,
                vlan_id, vlan_id_mask, value_8021p,
                cond_mask, time_range_status, time_range_id, acl_priority),
    Acl.TimeRangeCfg(time_range_id, -, -, time_range_status).
%table acl_time_range(time_range_id:int4, vrid:int4, inner_id:int4, active_status:int1)
{
    index(0(time_range_id)),
    external
}
acl_time_range(trngId, 0, 0, trngStatus) :-
    Acl.TimeRangeCfg(trngId, -, -, trngStatus).
%table acl_port_pool(
    vrid:int4, port_index:int4, pool_index:int4, port_begin:int4, port_end:int4, app_source_id:int4,
    app_serial_id:int4, app_obj_id:int8, app_version:int4)
{
    index(0(vrid, port_index, pool_index)),
    external
}
acl_port_pool(vrId, portId, poolId, startPort, endPort, 0, 0, 0, 0) :-
    Acl.PortPoolCfg(vrId, poolId, portId, -, startPort, endPort).
%table acl_ip_pool(
    vrid:int4, ip_index:int4, pool_index:int4, pool_ip:int4, ip_mask:int4, app_source_id:int4,
    app_serial_id:int4, app_obj_id:int8, app_version:int4)
{
    index(0(vrid, ip_index, pool_index)),
    external
}
acl_ip_pool(vrId, ipId, poolId, ipAddr, ipMask, 0, 0, 0, 0) :-
    Acl.IpPoolCfg(vrId, poolId, ipId, ipAddr, ipMask).
%table hsec_policy_filter(
    policy_id:int4, filter_id:int4, group_id:int4)
{
    index(0(policy_id, filter_id)),
    external
}
hsec_policy_filter(policy_id, filter_id, group_id) :-
    Hsec.PolicyFilterCfg(policy_id, filter_id, group_id),
    Hsec.AppliedPolicyCfg(policy_id, -).
%table hsec_applied_policy(
    policy_id:int4, applied_type:int4)
{
    index(0(policy_id)),
    external
}
hsec_applied_policy(policy_id, applied_type) :-
   Hsec.AppliedPolicyCfg(policy_id, applied_type).
namespace Usf {
    %readonly PublicAuth, PublicPortal, PublicUsfConfig, PublicSta
    %readonly PublicAcl, AclRuleList, UsfGroup, PublicAcl_1
    %readonly ConvertPrio, IngCar, EgrCar
    %readonly PublicROAM, MakeGroupGroupID, AclGroupResource
    %table IngCar(mac: byte6, pktNumOrPktLen: int1, carType: int1, colorMode: int1,
        cir: int8, cbs: int8, pir: int8, pbsEbs: int8,
        greenPermit: int1, yellowPermit: int1, redPermit: int1)
        {index(0(mac)), update}
    %table EgrCar(mac: byte6, pktNumOrPktLen: int1, carType: int1, colorMode: int1,
        cir: int8, cbs: int8, pir: int8, pbsEbs: int8,
        greenPermit: int1, yellowPermit: int1, redPermit: int1)
        {index(0(mac)), update}
    %table PublicROAM(mac:byte6, roamRole:int1, interIsolate:int1, innerIsolate:int1, tunnelDstRole:int1,
        usrGroupId:int2, homeVapFwdif:int4, foreignVapFwdif:int4, tunnelId:int4, userIsolate:int1, vlanId:int2, ipAddr:byte16, hapMac:byte6)
        {
            index(0(mac)),
            update_partial
        }
    %table PublicSta(staMac:byte6, pktCacheType:int1, isElbMcDupOnly:int1, vapIfIndex:int4)
        {index(0(staMac)), update_partial, union_delete(PublicPortal, PublicAcl, PublicAuth, EgrCar, IngCar, PublicROAM)}
    %table PublicPortal(staMac:byte6, portalPushEn:int1, denyPushEn:int1, httpSendToL4:int1, denyAll:int1, preAuth:int1, portalEscape:int1, checkUserIp:int1,
        userIpv4Addr:int4, denyArpEn:int1)
        {index(0(staMac)), update_partial}
    %table PublicAcl(staMac:byte6,
        ipv4AuthAclGroupIds:byte16, ipv4AuthAclNum:int1,
        ipv4RedirectAclGroupIds:byte16, ipv4RedirectAclNum:int1)
        {index(0(staMac)), update_partial}
    %table PublicAuth(staMac:byte6, authStatus:int1, authVlanEn:int1, authVlan:int2, userVipFlag:int1, ingStatEn:int1,
        egrStatEn:int1, statId:int4, userGroupId:int2, userGroupEn:int1, needSendUpMsg:int1, userPreRoamIpv4Addr:int4)
        {index(0(staMac)), update_partial}
    %table PublicUsfConfig(configIndex:int1, ipv6PortalFreeRuleGid:int2, httpsPortalEn:int1, httpsPortalDstPort:int2,
        daaAclGid:int2, ipv6DaaAclGid:int2, localManageEn:int1, manageIpAddr:int4, manageIpv6Addr:byte16, portalDstIp:int4,
        portalDstPort:int2, portalEnable:int1, portalDnsDisable:int4, httpPortalPort:byte32, httpPortalPortNum:int1, portalFreeRuleAclName:byte65)
        {index(0(configIndex)), update_partial}
    %table PublicAcl_0(mac: byte6, type: int1, gids: byte16, len: int1, enable: int1)
    %table PublicAcl_1(gid0: int4, mac: byte6, type: int1, gids: byte16, len: int1)
    %table AclRuleList(gid0: int4, gid1: int4, priority: int4)
    %table UsfGroup(vr: int4, downGroupId: int4, downGroupType: int4, flip: int4)
    %resource AclGroupResource(mac: byte6, type: int1 -> gid0: int4) { sequential(max_size(10000)) }
    %function MakeGroupGroupID(g: int4 -> gg: int4)
    %function EnableByNum(num: int1 -> enable: int1)
    %function ConvertPrio(prio0: int4, prio1: int4 -> prio: int4)
    %aggregate ConvertArray(gids: byte16, len: int1 -> gid: int4, priority: int4) { many_to_many }
    PublicAcl_0(mac, 1, gid, len, enable) :- PublicAcl(mac, gid, len, - , -), EnableByNum(len, enable).
    PublicAcl_0(mac, 2, gid, len, enable) :- PublicAcl(mac, -, -, gid, len), EnableByNum(len, enable).
    AclGroupResource("00", 0, -) :-
        PublicUsfConfig(0, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
        MakeGroupGroupID(0, gid0),
        Acl.ChangeGroupMark(0 , gid0).
    AclRuleList(gid0, gid1, 1) :-
        AclGroupResource("00", 0, g),
        PublicUsfConfig(0, -, -, -, -, -, -, -, -, -, -, -, -, -, -, gid_name),
        MakeGroupGroupID(g, gid0),
        Acl.GroupCfg(gid1, -, -, -, -, gid_name, 1, -).
    AclGroupResource(mac, type, -) :- PublicAcl_0(mac, type, -, -, 1).
    PublicAcl_1(gid, mac, type, gids, len) :-
        PublicAcl_0(mac, type, gids, len, 1), AclGroupResource(mac, type, gid0),
        MakeGroupGroupID(gid0, gid),
        Acl.ChangeGroupMark(0 , gid0).
    AclRuleList(gid0, gid1, prio) :-
        PublicAcl_1(gid0, -, -, gids, len)
        GROUP-BY(gid0) ConvertArray(gids, len, gid1, prio).
    UsfGroup(0, downGroupId, 500 , flip) :-
        AclRuleList(gid0, -, -),
        Sacl.GroupChange(-, gid0, flip),
        Sacl.GenDownGroupID(127, 0, 0, 0, gid0, downGroupId).
}
namespace Abstract {
    a_hpf_usf_usr_aclid(mac, type, dgid) :-
        Usf.PublicAcl_1(gid0, mac, type, -, -),
        Sacl.GenDownGroupID(127, 0, 0, 0, gid0, dgid).
    a_hpf_usf_usr_aclid("00", 0, dgid) :-
        Usf.AclGroupResource("00", 0, gid),
        Usf.MakeGroupGroupID(gid, gid0),
        Sacl.GenDownGroupID(127, 0, 0, 0, gid0, dgid).
    a_hpf_usf_roam_vlan(vlanId) :-
        Usf.PublicROAM(-, -, -, -, -, -, -, -, -, -, vlanId, -, -),
        NotEqual2(vlanId, 0).
    a_hpf_usf_roam_vlan_flag(0, 0, vlanId, 1) :-
        BR.Vlan(0, 0, vlanId, -, -, -, -),
        a_hpf_usf_roam_vlan(vlanId).
    a_hpf_usf_roam_vlan_flag(0, 0, vlanId, 0) :-
        BR.Vlan(0, 0, vlanId, -, -, -, -),
        NOT a_hpf_usf_roam_vlan(vlanId).
}
namespace Wmp {
    %readonly ConfigCapwapTunnel
    %table ConfigCapwapTunnel(sip: int4, dip: int4, sport: int2, dport: int2, mtu: int2, ifIndex: int4, dtlsIndex: int4,
        preamble: int1, tflag: int1, mflag: int1,
        server_or_client: int1, encrypt: int1, apMac: byte6) {
        index(0(sip, dip, sport, dport)), update_partial
    }
}
namespace Abstract {
    a_capwap_tunnel(sip, dip, sport, dport, mtu, ifIndex, dtlsIndex,
        preamble, tflag, mflag,
        server_or_client, encrypt, apMac) :-
        Wmp.ConfigCapwapTunnel(sip, dip, sport, dport, mtu, ifIndex, dtlsIndex,
        preamble, tflag, mflag,
        server_or_client, encrypt, apMac).
}
namespace Wmp {
    %readonly ConfigMacIp, ConfigMacIpv6
    %table ConfigMacIp(mac: byte6, ipAddr: int4, learnType: int1) {
        index(0(mac, ipAddr)), update_partial
    }
    %table ConfigMacIpv6(mac: byte6, ipv6Addr: byte16, learnType: int1) {
        index(0(mac, ipv6Addr)), update_partial
    }
}
null(0) :- Ifm.PortAttrChg(-,a,-), I1ToI4(a,b).
null(0) :- Mir.PortMirror(-,-,-).
null(0) :- Hsec.StormControlRate(-,-,-).
null(0) :- Hsec.StormControlAction(-,-).
namespace Abstract {
    %readonly a_vap_if, a_radio_name, a_if_name, a_if_phy, a_if_phy_copy, a_if_link, a_if_net, a_if_net6
    %readonly a_ip_route4, a_nhp4, a_nhp_group, a_arp, a_config_if_ipv4_addr, a_capwap_tunnel
    %readonly a_hpf_usf_usr_aclid, a_if_eth_trunk_member, a_if_eth_trunk, a_if_fwdif_trunk_attribute, a_if_fwdif_father
    %table a_vap_if(ifIndex: int4, radioName: byte64, adminState: int1, vapType: int1, vapIndex: int1, linkedId: int1, mac: byte6,
        userIdentify: int1, userService: int1, userAuth: int1, authType: int1, isEapolKeyToCp: int1, protocolTunnelFwd: int1)
    %table a_radio_name(radioName: byte64, type: byte16, tp: int4, phy_port: int4, port_id: int4, cpu_type: int1, drvPortType: int1, drvSubType: int1)
    %table a_if_name(
        if_index: int4, namespace_id: int4, if_name: byte64,
        if_type: int2, if_show_type: int2, if_alias: byte64, description: byte64)
    %table a_if_phy(
        if_index: int4, if_bw: int8, phy_state: int1, admin_state: int1, onboard_state: int1,
        tb: int4, tp: int4, chip_unit: int1, oda_phy_port: int1, phy_mac: byte6, eth_autoneg: int1, eth_speed: int8,
        eth_duplex: int1, eth_combo: int1, eth_loopback: int1, dev_id: int4, chassis_id: int4, slot_id: int4, card_id: int4,
        unit_id: int4, port_id: int4, service_type: int1, autoneg_cap: int1, autoneg_adv_cap: int2, medium: int1,
        mru: int2, link_type: int1, eth_class: int1, cpu_type: int1, drvPortType: int1, drvSubType: int1)
    %table a_if_phy_copy(
        if_index: int4, if_bw: int8, phy_state: int1, admin_state: int1, onboard_state: int1,
        tb: int4, tp: int4, chip_unit: int1, oda_phy_port: int1, phy_mac: byte6, eth_autoneg: int1, eth_speed: int8,
        eth_duplex: int1, eth_combo: int1, eth_loopback: int1, dev_id: int4, chassis_id: int4, slot_id: int4, card_id: int4,
        unit_id: int4, port_id: int4, service_type: int1, autoneg_cap: int1, autoneg_adv_cap: int2, medium: int1,
        mru: int2, link_type: int1, eth_class: int1, cpu_type: int1, drvPortType: int1, drvSubType: int1)
    %table a_if_link(
        if_index: int4, link_type: int4, link_state: int1, oper_state: int1)
    %table a_if_net(if_index: int4, vrf_id: int4, df_flag: int4, mtu_v4: int4, ipv4_state: int1)
    %table a_if_net6(if_index: int4, vrf_id: int4, mtu_v6: int4, ipv6_state: int1)
    %table a_ip_route4(
               vrfId: int4, dstIp: byte4, maskLen: int4, nhpIndex: int4, mulNhp: int1,
               routeFlags: int2, pathFlags: int4, dir_route: int1,
               def_route: int1) {transient(tuple)}
    %table a_nhp4(nhp_index: int4, next_hop: int4, out_if_index: int4, vrf_id: int4, iid_flags: int4, res_index: int4)
    %table a_nhp_group(frr: int1, nhp_num: int1, nhp_indexes: byte64, hash_index: int1, nhp_group_index: int4)
    %table a_arp(vrf_id: int4, ip_addr: int4, if_index: int4, l3if_index: int4, type: int1, mac: byte6, fake_flag: int1, vlan_id: int2)
    %table a_config_if_ipv4_addr(if_index: int4, vrf_index: int4, address: int4, mask_len: int2, type: int2)
    %table a_capwap_tunnel(sip: int4, dip: int4, sport: int2, dport: int2, mtu: int2, ifIndex: int4, dtlsIndex: int4,
        preamble: int1, tflag: int1, mflag: int1,
        server_or_client: int1, encrypt: int1, apMac: byte6)
    %table a_hpf_usf_usr_aclid(mac: byte6, type: int1, gid0: int4)
    %table a_hpf_usf_roam_vlan(vlanId: int2)
    %table a_hpf_usf_roam_vlan_flag(ns_id: int4, br_id: int4, vlanId: int2, roamUserExistFlag:int1)
    %table a_if_eth_trunk_member(memIfIndex: int4, trunkId: int4, trunkIndex: int4 )
    %table a_if_eth_trunk(trunkId: int4, trunkIndex: int4)
    %table a_if_fwdif_trunk_attribute(ifIndex: int4, trunkId: int4, isTrunkMember:int1)
    %table a_if_fwdif_father(ifIndex: int4, fatherifIndex: int4)
}
namespace Hpf {
    %readonly hpf_if_index
    %table CAP_TABLE_FWDIF(fwdIfIdx: int4, ctrlIfIdx: int4, flowIfIdx: int4, ifName: byte64, ifType: int4, tp: int4, cpuType: int1, mtu: int4, mac: byte6, flag: int4) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_index(fwdIfIdx: int4, ctrlIfIdx: int4, flowIfIdx: int4) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_name(fwdIfIdx: int4, namespace_id: int4, if_name: byte64,
        hpf_if_type: int4, if_show_type: int2, if_alias: byte64, description: byte64, virt_if:int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_phy(fwdIfIdx: int4, if_bw: int8, phy_state: int1, admin_state: int1, onboard_state: int1,
        tb: int4, tp: int4, chip_unit: int1, oda_phy_port: int1, phy_mac: byte6, eth_autoneg: int1, eth_speed: int8,
        eth_duplex: int1, eth_combo: int1, eth_loopback: int1, dev_id: int4, chassis_id: int4, slot_id: int4, card_id: int4,
        unit_id: int4, port_id: int4, service_type: int1, autoneg_cap: int1, autoneg_adv_cap: int2, medium: int1,
        mru: int2, link_type: int1, eth_class: int1, cpu_type: int1, drvPortType: int1, drvSubType: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_link(fwdIfIdx: int4, link_type: int4, link_state: int1, oper_state: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_net(fwdIfIdx: int4, vrf_id: int4, df_flag: int4, mtu_v4: int4, ipv4_state: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_net6(fwdIfIdx: int4, vrf_id: int4, mtu_v6: int4, ipv6_state: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_vap(fwdIfIdx: int4, adminState: int1, vapType: int1, vapIndex: int1, linkedId: int1, mac: byte6,
        userIdentify: int1, userService: int1, userAuth: int1, authType: int1, isEapolKeyToCp: int1, protocolTunnelFwd: int1,
        tp: int4, cpu_type: int1, drvPortType: int1, drvSubType: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_capwap(fwdIfIdx: int4, tunnelId: int4) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_ip_learn(fwdIfIdx: int4, ipLearnEnable: int1, dhcpStrict: int1, addBlackList: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_protocol(fwdIfIdx: int4, proto: int1, enable: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_pppoe(fwdIfIdx: int4, code: int1, ver: int1, type: int1, sessionId: int2, dmac: byte6, ori_link_type: int4) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table CAP_TABLE_CTRLIF_MAP_FWDIF(ctrlIfIdx: int4, fwdIfIdx: int4) {
        index(0(ctrlIfIdx)),
        tbm
    }
    %table CAP_TABLE_FLOWIF_MAP_FWDIF(flowIfIdx: int4, fwdIfIdx: int4) {
        index(0(flowIfIdx)),
        tbm
    }
    %table CAP_TABLE_PORT_MAP_TAGTYPE(portId: int4, portEncType: int1) {
        index(0(portId)),
        tbm
    }
    %table CAP_TABLE_LINK_UP_DOWN_MODE(fwdIfIdx: int4, mode: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_wmp_portal(fwdIfIdx: int4, apPortalEn: int1, webProxyPortalParse: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_vap_freerule(fwdIfIdx: int4, ipv4GroupId: int4, ipv6GroupId: int4, staticGroupId: int4) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_wmp_porxy(fwdIfIdx: int4, port: int2, url: byte200) {
        index(0(fwdIfIdx, port, url)),
        tbm
    }
    %table CAP_TABLE_VAP_LOCAL_MNG(fwdIfIdx: int4, localManageEn:int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_trunk_member(memIfIndex: int4, trunkId: int4, trunkIndex: int4) {
        index(0(memIfIndex, trunkId, trunkIndex)),
        tbm
    }
    %table hpf_if_eth_trunk(trunkId: int4, trunkIndex: int4) {
        index(0(trunkId, trunkIndex)),
        tbm
    }
    %table hpf_if_fwdif_trunk_attribute(fwdIfIdx: int4, trunkId: int4, isTrunkMember:int1) {
        index(0(fwdIfIdx, trunkId, isTrunkMember)),
        tbm
    }
    %table hpf_if_fwdif_father(fwdIfIdx: int4, fatherifIndex: int4) {
        index(0(fwdIfIdx, fatherifIndex)),
        tbm
    }
    %function GetFlowIfIndex(if_name: byte64, hpfIfType: int4, tb: int4, tp: int4 -> flowIfIdx: int4)
    %function GetFlowIfIndexForVap(vapIndex: int1, linkedId: int1 -> flowIfIdx: int4)
    %function GetFlowIfIndexForCapwap(tunnelId: int4 -> flowIfIdx: int4)
    %function GetHpfIfType(ifType: int2 -> hpfIfType: int4)
    %function GetHpfIfVirtFlag(ifType: int2 -> virtIf: int1)
    %function IsnotVap(hpfIfType: int4)
    %function IsnotCapwap(hpfIfType: int4)
    %function IsGE(hpfIfType: int4)
    %resource ctrlif_map_fwdif(ctrlIfIdx: int4 -> fwdIfIdx: int4) { sequential(max_size(100)) }
    ctrlif_map_fwdif(ctrlIfIdx, -) :- Abstract.a_if_name(ctrlIfIdx, -, -, -, -, -, -).
    %table ctrlif_map_fwdif_copy(ctrlIfIdx: int4, fwdIfIdx: int4)
    ctrlif_map_fwdif_copy(ctrlIfIdx, fwdIfIdx) :- ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    hpf_if_index(fwdIfIdx, ctrlIfIdx, flowIfIdx) :-
        Abstract.a_if_phy(ctrlIfIdx, -, -, -, -, tb, tp, -, -, -,
            -, -, -, -, -, -, -, -, -, -,
            -, -, -, -, -, -, -, -, -, -, -),
        Abstract.a_if_name(ctrlIfIdx, -, ifName, ifType, -, -, -),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx),
        GetHpfIfType(ifType, hpfIfType),
        IsnotVap(hpfIfType),
        IsnotCapwap(hpfIfType),
        GetFlowIfIndex(ifName, hpfIfType, tb, tp, flowIfIdx).
    hpf_if_index(fwdIfIdx, ifIndex, flowIfIdx) :-
        Abstract.a_vap_if(ifIndex, -, -, -, vapIndex, linkedId, -,
            -, -, -, -, -, -),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx),
        GetFlowIfIndexForVap(vapIndex, linkedId, flowIfIdx).
    hpf_if_index(fwdIfIdx, ifIndex, flowIfIdx) :-
        Abstract.a_capwap_tunnel(sip, dip, sport, dport, -, ifIndex, -,
            -, -, -,
            -, -, -),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx),
        capwap_tunnel_id_pool(sip, dip, sport, dport, tunnelId),
        GetFlowIfIndexForCapwap(tunnelId, flowIfIdx).
    hpf_if_name(fwdIfIdx, namespace_id, if_name, hpf_if_type, if_show_type, if_alias, description, virt_if) :-
        Abstract.a_if_name(if_index, namespace_id, if_name,
            if_type, if_show_type, if_alias, description),
        GetHpfIfType(if_type, hpf_if_type),
        GetHpfIfVirtFlag(if_type, virt_if),
        ctrlif_map_fwdif(if_index, fwdIfIdx).
    hpf_if_phy(fwdIfIdx, if_bw, phy_state, admin_state, onboard_state,
            tb, tp, chip_unit, oda_phy_port, phy_mac, eth_autoneg, eth_speed,
            eth_duplex, eth_combo, eth_loopback, dev_id, chassis_id, slot_id, card_id,
            unit_id, port_id, service_type, autoneg_cap, autoneg_adv_cap, medium,
            mru, link_type, eth_class, cpu_type, drvPortType, drvSubType) :-
        Abstract.a_if_phy(if_index, if_bw, phy_state, admin_state, onboard_state,
            tb, tp, chip_unit, oda_phy_port, phy_mac, eth_autoneg, eth_speed,
            eth_duplex, eth_combo, eth_loopback, dev_id, chassis_id, slot_id, card_id,
            unit_id, port_id, service_type, autoneg_cap, autoneg_adv_cap, medium,
            mru, link_type, eth_class, cpu_type, drvPortType, drvSubType),
        ctrlif_map_fwdif(if_index, fwdIfIdx).
    hpf_if_link(fwdIfIdx, link_type, link_state, oper_state) :-
        Abstract.a_if_link(if_index, link_type, link_state, oper_state),
        ctrlif_map_fwdif(if_index, fwdIfIdx).
    hpf_if_net(fwdIfIdx, vrf_id, df_flag, mtu_v4, ipv4_state) :-
        Abstract.a_if_net(if_index, vrf_id, df_flag, mtu_v4, ipv4_state),
        ctrlif_map_fwdif(if_index, fwdIfIdx).
    hpf_if_net6(fwdIfIdx, vrf_id, mtu_v6, ipv6_state) :-
        Abstract.a_if_net6(if_index, vrf_id, mtu_v6, ipv6_state),
        ctrlif_map_fwdif(if_index, fwdIfIdx).
    hpf_if_vap(fwdIfIdx, adminState, vapType, vapIndex, linkedId, mac,
        userIdentify, userService, userAuth, authType, isEapolKeyToCp, protocolTunnelFwd,
        tp, cpu_type, drvPortType, drvSubType) :-
        Abstract.a_vap_if(ifIndex, radioName, adminState, vapType, vapIndex, linkedId, mac,
            userIdentify, userService, userAuth, authType, isEapolKeyToCp, protocolTunnelFwd),
        Abstract.a_radio_name(radioName, -, tp, -, -, cpu_type, drvPortType, drvSubType),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_if_capwap(fwdIfIdx, tunnelId) :-
        Abstract.a_capwap_tunnel(sip, dip, sport, dport, -, ifIndex, -,
            -, -, -,
            -, -, -),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx),
        capwap_tunnel_id_pool(sip, dip, sport, dport, tunnelId).
    hpf_if_ip_learn(fwdIfIdx, ipLearnEnable, dhcpStrict, addBlackList) :-
        Ifm.ConfigIfIpLearn(ifIndex, ipLearnEnable, dhcpStrict, addBlackList),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_if_wmp_portal(fwdIfIdx, apPortalEn, webProxyPortalParse) :-
        Ifm.ConfigIfWmpPortal(ifIndex, apPortalEn, -),
        Ifm.ConfigIfWmpWebProxy(ifIndex, webProxyPortalParse, -, -),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_if_vap_freerule(fwdIfIdx, d4, d6, ds) :-
        Ifm.VapAclGroupIds(ifIndex, v4Gid, v6Gid, staticGid),
        Sacl.GenDownGroupID(126, 0, 0, 0, v4Gid, d4),
        Sacl.GenDownGroupID(126, 0, 0, 0, v6Gid, d6),
        Sacl.GenDownGroupID(126, 0, 0, 0, staticGid, ds),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_if_wmp_porxy(fwdIfIdx, port, url) :-
        Ifm.ConfigIfWmpWebProxy(ifIndex, -, port, url),
        NotEqual2(port, 0),
        NotZerobyte200(url),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_if_protocol(fwdIfIdx, proto, enable) :-
        Ifm.ConfigProtocolEnable(ifIndex, proto, enable),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_if_pppoe(fwdIfIdx, code, ver, type, sessionId, dmac, ori_link_type) :-
        Ifm.Pppoe(ifIndex, code, ver, type, sessionId, dmac),
        Abstract.a_if_link(ifIndex, ori_link_type, -, -),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    CAP_TABLE_CTRLIF_MAP_FWDIF(ctrlIfIdx, fwdIfIdx) :-
        Abstract.a_if_name(ctrlIfIdx, -, -, -, -, -, -),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_FLOWIF_MAP_FWDIF(flowIfIdx, fwdIfIdx) :-
        Abstract.a_if_phy(ctrlIfIdx, -, -, -, -,
            tb, tp, -, -, -, -, -,
            -, -, -, -, -, -, -,
            -, -, -, -, -, -,
            -, -, -, -, -, -),
        Abstract.a_if_name(ctrlIfIdx, -, if_name, ifType, -, -, -),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx),
        GetHpfIfType(ifType, hpfIfType),
        IsnotVap(hpfIfType),
        GetFlowIfIndex(if_name, hpfIfType, tb, tp, flowIfIdx).
    CAP_TABLE_FLOWIF_MAP_FWDIF(flowIfIdx, fwdIfIdx) :-
        Abstract.a_vap_if(ifIndex, -, -, -, vapIndex, linkedId, -,
            -, -, -, -, -, -),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx),
        GetFlowIfIndexForVap(vapIndex, linkedId, flowIfIdx).
    CAP_TABLE_PORT_MAP_TAGTYPE(tp, portEncType) :-
        Abstract.a_if_phy(ctrlIfIdx, -, -, -, -,
            -, tp, -, -, -, -, -,
            -, -, -, -, -, -, -,
            -, -, -, -, -, -,
            -, -, -, portEncType, -, -),
        Abstract.a_if_name(ctrlIfIdx, -, -, ifType, -, -, -),
        GetHpfIfType(ifType, hpfIfType),
        IsGE(hpfIfType).
    CAP_TABLE_PORT_MAP_TAGTYPE(tp, portEncType) :-
        Abstract.a_radio_name(-, -, tp, -, -, portEncType, -, -).
    CAP_TABLE_LINK_UP_DOWN_MODE(fwdIfIdx, mode) :-
        Ifm.ConfigLinkUpDownMode(ctrlIfIdx, mode),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_VAP_LOCAL_MNG(fwdIfIdx, localManageEn) :-
        Abstract.a_vap_if(ifIndex, -, -, -, -, -, -, -, -, -, -, -, -),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx),
        Usf.PublicUsfConfig(-, -, -, -, -, -, localManageEn, -, -, -, -, -, -, -, -, -).
    ctrlif_map_fwdif(-134217728, -):- tbl_init(-).
    CAP_TABLE_FWDIF(fwdIfIdx, -134217728, -134217728, "abcdef0123456789", 0, 0, 0, 1500, "0x000000000000", 397313) :-
        ctrlif_map_fwdif(-134217728, fwdIfIdx).
    CAP_TABLE_CTRLIF_MAP_FWDIF(-134217728, fwdIfIdx) :-
        ctrlif_map_fwdif(-134217728, fwdIfIdx).
    CAP_TABLE_FLOWIF_MAP_FWDIF(-134217728, fwdIfIdx) :-
        ctrlif_map_fwdif(-134217728, fwdIfIdx).
    hpf_if_trunk_member(memFwdIfIndex, trunkId, trunkFwdIndex):-
        Abstract.a_if_eth_trunk_member(memIfIndex, trunkId, trunkIndex),
        ctrlif_map_fwdif(memIfIndex, memFwdIfIndex),
        ctrlif_map_fwdif_copy(trunkIndex, trunkFwdIndex).
    hpf_if_eth_trunk(trunkId, trunkFwdIndex):-
        ctrlif_map_fwdif(trunkIndex, trunkFwdIndex),
        Abstract.a_if_eth_trunk(trunkId, trunkIndex).
    hpf_if_fwdif_trunk_attribute(fwdIfIndex, trunkId, isTrunkMember):-
        ctrlif_map_fwdif(ifIndex, fwdIfIndex),
        Abstract.a_if_fwdif_trunk_attribute(ifIndex, trunkId, isTrunkMember).
    hpf_if_fwdif_father(memFwdIfIndex, fatherFwdifIndex):-
        Abstract.a_if_fwdif_father(memIfIndex, fatherifIndex),
        ctrlif_map_fwdif(memIfIndex, memFwdIfIndex),
        ctrlif_map_fwdif_copy(fatherifIndex, fatherFwdifIndex).
    %precedence hpf_if_index, CAP_TABLE_LINK_UP_DOWN_MODE
    %precedence hpf_if_vap, CAP_TABLE_VAP_LOCAL_MNG
    %precedence hpf_if_eth_trunk, hpf_if_trunk_member
}
namespace Hpf {
    %table CAP_TABLE_IPV4_FIB(prefix: int4, vpn: int4, dip: byte4, opcode: int2, mulNhp: int1, dirRoute: int1, defRoute: int1, nhpIdx: int4) {
        index(0(prefix, vpn, dip)),
        tbm
    }
    %table CAP_TABLE_IPV4_NHP(nhpIdx: int4, ctrlIfIdx: int4, fwdIfIdx: int4, nextHop: int4) {
        index(0(nhpIdx)),
        tbm
    }
    %table CAP_TABLE_IPV4_NHP_GROUP(nhpGrpIdx: int4, nhpNum: int1, nhpIndexes: byte64) {
        index(0(nhpGrpIdx)),
        tbm
    }
    %table CAP_TABLE_IPV4_ARP(vpn: int4, nextHop: int4, fake: int1, ctrlIfIdx: int4, l3CtrlIfIdx: int4, fwdIfIdx: int4, l3FwdIfIdx: int4, dmac: byte6, vlanId: int2) {
        index(0(vpn, nextHop)),
        tbm
    }
    %table CAP_TABLE_REPLY_ARP(brId: int4, vlanId: int2, arpReplyEn: int1) {
        index(0(brId, vlanId)),
        tbm
    }
    %table CAP_TABLE_GLOBAL_ARP(index: int1, arpGatewayDupEn: int1) {
        index(0(index)),
        tbm
    }
    %table NOTIFY_TABLE_IPV4_ARP(ip: int4, fake: int1) {
        tbm,
        index(0(ip))
    }
    %table CAP_TABLE_ARP_GW_DUP_BLOCK(ifIndex: int4, brId: int4, srcMac: byte6, vlanId: int2, agingTime: int8)
    {
        index(0(ifIndex, brId, srcMac, vlanId)),
        tbm
    }
    %table CAP_TABLE_IPV4_PORTIP(ctrlIfIdx: int4, fwdIfIdx: int4, index: int4) {
        index(0(ctrlIfIdx)),
        tbm
    }
    %table CAP_TABLE_IPV4_PORTIPLIST(index: int4, vrf_index: int4, address: int4, mask_len: int2, type: int2) {
        index(0(index)),
        tbm
    }
    %resource portip_index_pool(ctrlIfIdx: int4 -> index: int4) { sequential(max_size(100)) }
    portip_index_pool(ctrlIfIdx, -) :- Abstract.a_config_if_ipv4_addr(ctrlIfIdx, -, -, -, -).
    CAP_TABLE_IPV4_FIB(prefix, vpn, dip, opcode, mulNhp, dir_route, def_route, nhpIndex) :-
        Abstract.a_ip_route4(vpn, dip, prefix, nhpIndex, mulNhp, opcode, -, dir_route, def_route).
    CAP_TABLE_IPV4_NHP(nhpHpfIndex, ctrlIfIdx, fwdIfIdx, nextHop) :-
        Abstract.a_nhp4(-, nextHop, ctrlIfIdx, -, -, nhpHpfIndex),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_IPV4_NHP_GROUP(nhpGrpIdx, nhpNum, nhpIndexes) :-
        Abstract.a_nhp_group(-, nhpNum, nhpIndexes, -, nhpGrpIdx).
    %precedence CAP_TABLE_IPV4_ARP, NOTIFY_TABLE_IPV4_ARP
    CAP_TABLE_IPV4_ARP(vrf_id, nextHop, fake, if_index, l3if_index, fwdIfIdx, l3FwdIfIdx, mac, vlanId) :-
        Abstract.a_arp(vrf_id, nextHop, if_index, l3if_index, -, mac, fake, vlanId),
        ctrlif_map_fwdif(if_index, fwdIfIdx), ctrlif_map_fwdif_copy(l3if_index, l3FwdIfIdx).
    NOTIFY_TABLE_IPV4_ARP(nextHop, fake) :-
        Abstract.a_arp(-, nextHop, -, -, -, -, fake, -).
    CAP_TABLE_REPLY_ARP(brId, vlanId, arpReplyEn) :-
        Arp.VlanArpReplay(0, brId, vlanId, arpReplyEn).
    CAP_TABLE_GLOBAL_ARP(index, arpGatewayDupEn) :-
        Arp.ArpCfg(index, arpGatewayDupEn).
    CAP_TABLE_ARP_GW_DUP_BLOCK(fwdIfIdx, brId, srcMac, vlanId, agingTime) :-
        Arp.ArpGatewayDupBlock(ifIndex, brId, srcMac, vlanId, agingTime),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    CAP_TABLE_IPV4_PORTIP(ctrlIfIdx, fwdIfIdx, index) :-
        Abstract.a_config_if_ipv4_addr(ctrlIfIdx, -, -, -, -),
        portip_index_pool(ctrlIfIdx, index),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_IPV4_PORTIPLIST(index, vrf_index, address, mask_len, type) :-
        Abstract.a_config_if_ipv4_addr(ctrlIfIdx, vrf_index, address, mask_len, type),
        portip_index_pool(ctrlIfIdx, index).
}
namespace Hpf {
    %table hpf_nhp_ext(hpf_nhp_index: int4, l3if_index: int4, l3FwdIfIdx: int4, vrf_id: int4, nexthop: int4,
        port_id: int4, arp_miss: int1, arp_fake: int1,
        encSize: int2, encap: byte128, fsvcNum: int2, egrFsvc: byte128, fwdIfNum: int2, outFwdIfs: byte64)
    %table hpf_nhp_temp(hpf_nhp_index: int4, l3if_index: int4, l3FwdIfIdx: int4, vrf_id: int4, nexthop: int4)
    %function hpf_nhp_encap(smac: byte6, dmac: byte6, vlan_id: int2 -> encSize: int2, encap: byte128)
    %function hpf_nhp_egrFsvc(fwdIfNum: int2, outFwdIfs: byte64 -> fsvcNum: int2, egrFsvc: byte128)
    %function hpf_nhp_outFwdIfs(fwdIfIdx: int4, l3FwdIfIdx: int4 -> fwdIfNum: int2, outFwdIfs: byte64)
    hpf_nhp_temp(hpf_nhp_index, l3if_index, l3FwdIfIdx, vrf_id, nexthop):-
        Abstract.a_nhp4(-, nexthop, l3if_index, vrf_id, -, hpf_nhp_index),
        ctrlif_map_fwdif(l3if_index, l3FwdIfIdx).
    hpf_nhp_ext(hpf_nhp_index, l3if_index, l3FwdIfIdx, vrf_id, nexthop,
        port_id, 0, arp_fake,
        encSize, encap, fsvcNum, egrFsvc, fwdIfNum, outFwdIfs) :-
        hpf_nhp_temp(hpf_nhp_index, l3if_index, l3FwdIfIdx, vrf_id, nexthop),
        Abstract.a_arp(vrf_id, nexthop, if_index, l3if_index, -, dmac, arp_fake, vlan_id),
        Abstract.a_if_phy(l3if_index, -, -, -, -,
            -, -, -, -, smac, -, -,
            -, -, -, -, -, -, -,
            -, -, -, -, -, -,
            -, -, -, -, -, -),
        Abstract.a_if_phy_copy(if_index, -, -, -, -,
            -, -, -, -, -, -, -,
            -, -, -, -, -, -, -,
            -, port_id, -, -, -, -,
            -, -, -, -, -, -),
        ctrlif_map_fwdif(if_index, fwdIfIdx),
        hpf_nhp_encap(smac, dmac, vlan_id, encSize, encap),
        hpf_nhp_outFwdIfs(fwdIfIdx, l3FwdIfIdx, fwdIfNum, outFwdIfs),
        hpf_nhp_egrFsvc(fwdIfNum, outFwdIfs, fsvcNum, egrFsvc).
    hpf_nhp_ext(hpf_nhp_index, l3if_index, l3FwdIfIdx, vrf_id, nexthop,
        0, 1, 0,
        0, 00, 0, 00, 0, 00) :-
        hpf_nhp_temp(hpf_nhp_index, l3if_index, l3FwdIfIdx, vrf_id, nexthop),
        NOT Abstract.a_arp(vrf_id, nexthop, -, -, -, -, -, -).
}
namespace Hpf {
    %table CAP_TABLE_BRIDGE_INFO(brId: int4, vlanFilter: int1, macLimitEn: int1, macAgeTime: int4, brFwdIfIdx: int4, macLimitNum: int4) {
        index(0(brId)),
        tbm
    }
    %table CAP_TABLE_VLAN(brId: int4, vlanId: int2, bcMid: int2, bcStatMid: int2, macLearnEn: int1, missUcAction: int1,
        vlanifFwdIdx: int4, macLimitEn: int1, macLimitAlarmEn: int1, macLimitNum: int4, macLimitAction: int1,
        ipv4McEn: int1, igmpMode: int1, roamUserExistFlag:int1) {
        index(0(brId, vlanId)),
        tbm
    }
    %table CAP_TABLE_SSW_BC_ELB(brId: int4, vlanId: int2, fwdIfIdx: int4) {
        index(0(brId, vlanId, fwdIfIdx)),
        tbm
    }
    %table CAP_TABLE_VLAN_BITMAP(fwdIfIdx: int4, brId: int4, vlanId: int2) {
        index(0(fwdIfIdx, brId, vlanId)),
        tbm
    }
    %table CAP_TABLE_VLAN_UNTAG_BITMAP(fwdIfIdx: int4, brId: int4, untagVlanId: int2) {
        index(0(fwdIfIdx, brId, untagVlanId)),
        tbm
    }
    %table CAP_TABLE_PORT_ISOLATE(srcFwdIfIndex: int4, dstFwdIfIndex: int4) {
        index(0(srcFwdIfIndex, dstFwdIfIndex)),
        tbm
    }
    %table CAP_TABLE_FWDIF_PORT_MASTER(fwdIfIdx: int4, brId: int4, l2Port: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table CAP_TABLE_FWDIF_PORT_PVID(fwdIfIdx: int4, pvid: int2) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table CAP_TABLE_FWDIF_PORT_MAC_LEARN_LIMIT(fwdIfIdx: int4, learn: int1, learnAct: int1, limit: int4, limitAct: int1, limitAlm: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table CAP_TABLE_SSW_MAC(brId: int4, vlanId: int2, mac: byte6, staticMac: int1, delete: int1, local: int1, sec: int1, fwdIfIdx:int4) {
        index(0(brId, vlanId, mac)),
        tbm
    }
    %table CAP_TABLE_FWDIF_VLANIF(vlanifFwdIdx: int4, brId: int4, vlanId: int2) {
        index(0(vlanifFwdIdx)),
        tbm
    }
    %function GetMacFlag(type: int1, flag: int4 -> staticMac: int1, delete: int1, local: int1, sec: int1)
    CAP_TABLE_BRIDGE_INFO(brId, vlanFilter, 1, macAgeTime, brFwdIfIdx, macLimitNum) :-
        Abstract.Tbl_Bridge(-, brId, -, vlanFilter, macAgeTime, -, -, -, -, -, -, brIfIdx, macLimitNum),
        Hpf.ctrlif_map_fwdif(brIfIdx, brFwdIfIdx).
    CAP_TABLE_BRIDGE_INFO(brId, vlanFilter, 1, macAgeTime, -1, macLimitNum) :-
        Abstract.Tbl_Bridge(-, brId, -, vlanFilter, macAgeTime, -, -, -, -, -, -, -1, macLimitNum).
    CAP_TABLE_VLAN(brId, vlanId, vlanId, vlanId, macLearn, 0, vlanifFwdIdx, 1, macLimitAlarmEn, macLimitNum, macLimitAction, 0, 0, roamUserExistFlag) :-
        Abstract.Tbl_Vlan(0, brId, vlanId, -, -, -, -, -, -, -, macLearn, macLimitNum, macLimitAction, macLimitAlarmEn, brVlanif, roamUserExistFlag),
        Hpf.ctrlif_map_fwdif(brVlanif, vlanifFwdIdx).
    CAP_TABLE_VLAN(brId, vlanId, vlanId, vlanId, macLearn, 0, -1, 1, macLimitAlarmEn, macLimitNum, macLimitAction, 0, 0, roamUserExistFlag) :-
        Abstract.Tbl_Vlan(0, brId, vlanId, -, -, -, -, -, -, -, macLearn, macLimitNum, macLimitAction, macLimitAlarmEn, -1, roamUserExistFlag).
    CAP_TABLE_SSW_BC_ELB(brId, vlanId, fwdIfIdx) :-
        BR.VlanTagPort(0, brId, vlanId, -, ifIndex),
        Abstract.Tbl_Vlan(0, brId, vlanId, -, -, -, -, -, -, -, -, -, -, -, -, -),
        NotEqual2(vlanId, 0),
        NotEqual4(ifIndex, -1),
        Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    CAP_TABLE_SSW_BC_ELB(brId, vlanId, fwdIfIdx) :-
        BR.VlanUntagPort(0, brId, vlanId, -, ifIndex),
        Abstract.Tbl_Vlan(0, brId, vlanId, -, -, -, -, -, -, -, -, -, -, -, -, -),
        NotEqual2(vlanId, 0),
        NotEqual4(ifIndex, -1),
        Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    CAP_TABLE_VLAN_BITMAP(fwdIfIdx, brId, vlanId) :-
        BR.VlanTagPort(0, brId, vlanId, -, ifIndex),
        Abstract.Tbl_Vlan(0, brId, vlanId, -, -, -, -, -, -, -, -, -, -, -, -, -),
        NotEqual2(vlanId, 0),
        NotEqual4(ifIndex, -1),
        Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    CAP_TABLE_VLAN_UNTAG_BITMAP(fwdIfIdx, brId, untagVlanId) :-
        BR.VlanUntagPort(0, brId, untagVlanId, -, ifIndex),
        Abstract.Tbl_Vlan(0, brId, untagVlanId, -, -, -, -, -, -, -, -, -, -, -, -, -),
        NotEqual2(untagVlanId, 0),
        NotEqual4(ifIndex, -1),
        Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    CAP_TABLE_PORT_ISOLATE(srcFwdIfIndex, dstFwdIfIndex) :-
        BR.PortIfPortIf(srcIfIndex, dstIfIndex),
        Hpf.ctrlif_map_fwdif(srcIfIndex, srcFwdIfIndex),
        Hpf.ctrlif_map_fwdif_copy(dstIfIndex, dstFwdIfIndex).
    CAP_TABLE_FWDIF_PORT_MASTER(fwdIfIdx, brId, l2Port) :-
        Abstract.Tbl_Port(ctrlIfIdx, -, -, brId, l2Port, -, -, -, -, -, -, -, -, -, -, -, -, -),
        Hpf.ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_FWDIF_PORT_PVID(fwdIfIdx, pvid) :-
         Abstract.Tbl_Port(ctrlIfIdx, -, -, -, 1, -, pvid, -, -, -, -, -, -, -, -, -, -, -),
         Hpf.ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_FWDIF_PORT_MAC_LEARN_LIMIT(fwdIfIdx, learn, learnAct, limit, limitAct, limitAlm) :-
        BR.PortMacAttr(ctrlIfIdx, -, learn, learnAct, limit, limitAct, limitAlm),
        Hpf.ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_FWDIF_VLANIF(vlanifFwdIdx, brId, vlanId) :-
        Abstract.Tbl_Vlan(-, brId, vlanId, -, -, -, -, -, -, -, -, -, -, -, brVlanif, -),
        NotEqual4(brVlanif, -1),
        Hpf.ctrlif_map_fwdif(brVlanif, vlanifFwdIdx).
    CAP_TABLE_SSW_MAC(brId, vlanId, mac, staticMac, delete, local, sec, fwdIfIdx) :-
        BR.MacOper(-, brId, vlanId, mac, ctrlIfIdx, type, flag),
        Hpf.ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx),
        GetMacFlag(type, flag, staticMac, delete, local, sec).
    %precedence CAP_TABLE_BRIDGE_INFO, CAP_TABLE_VLAN
    %precedence CAP_TABLE_VLAN, CAP_TABLE_FWDIF_PORT_PVID
    %precedence CAP_TABLE_VLAN, CAP_TABLE_VLAN_BITMAP
    %precedence CAP_TABLE_VLAN, CAP_TABLE_VLAN_UNTAG_BITMAP
    %precedence CAP_TABLE_VLAN, CAP_TABLE_SSW_BC_ELB
    %precedence CAP_TABLE_BRIDGE_INFO, CAP_TABLE_FWDIF_PORT_MASTER
    %precedence CAP_TABLE_FWDIF_PORT_MASTER, CAP_TABLE_FWDIF_PORT_PVID
    %precedence CAP_TABLE_FWDIF_PORT_MASTER, CAP_TABLE_VLAN_BITMAP
    %precedence CAP_TABLE_FWDIF_PORT_MASTER, CAP_TABLE_VLAN_UNTAG_BITMAP
    %precedence CAP_TABLE_FWDIF_PORT_MASTER, CAP_TABLE_PORT_ISOLATE
    %precedence CAP_TABLE_FWDIF_PORT_MASTER, CAP_TABLE_FWDIF_PORT_MAC_LEARN_LIMIT
    %precedence Hpf.hpf_if_index, CAP_TABLE_FWDIF_PORT_PVID
    %precedence Hpf.hpf_if_index, CAP_TABLE_VLAN_BITMAP
    %precedence Hpf.hpf_if_index, CAP_TABLE_FWDIF_PORT_MASTER
    %precedence Hpf.hpf_if_index, CAP_TABLE_FWDIF_PORT_MAC_LEARN_LIMIT
    %precedence Hpf.hpf_if_index, CAP_TABLE_FWDIF_VLANIF
}
namespace Hpf {
    %table CAP_TABLE_USF_STAT(statId:int4) {
        index(0(statId)),
        tbm
    }
    %table CAP_TABLE_USF_AUTH(staMac:byte6, authStatus:int1, authVlanEn:int1, authVlan:int2, userVipFlag:int1, ingStatEn:int1,
        egrStatEn:int1, statId:int4, userGroupId:int2, userGroupEn:int1, needSendUpMsg:int1, userPreRoamIpv4Addr:int4){
        index(0(staMac)),
        tbm
    }
    %table CAP_TABLE_USF_PORTAL(staMac:byte6, portalPushEn:int1, denyPushEn:int1, httpSendToL4:int1, denyAll:int1, preAuth:int1, portalEscape:int1, checkUserIp:int1, userIpv4Addr:int4, denyArpEn:int1){
        index(0(staMac)),
        tbm
    }
    %table CAP_TABLE_USF_GLOBAL(configIndex:int1, ipv6PortalFreeRuleGid:int2, httpsPortalEn:int1, httpsPortalDstPort:int2, daaAclGid:int2, ipv6DaaAclGid:int2, localManageEn:int1, manageIpAddr:int4, manageIpv6Addr:byte16, portalDstIp:int4, portalDstPort:int2, portalEnable:int1, portalDnsDisable:int4, httpPortalPort:byte32, httpPortalPortNum:int1)
        {index(0(configIndex)), tbm}
    %table CAP_TABLE_USF_CAR(staMac:byte6, direction:int1, carId:int4, enable:int1)
        {index(0(staMac)),
        tbm}
    %table CAP_TABLE_USF_ROAM(mac:byte6, roamRole:int1, interIsolate:int1, innerIsolate:int1, tunnelDstRole:int1,
        usrGroupId:int2, homeVapFwdif:int4, foreignVapFwdif:int4, tunnelId:int4, userIsolate:int1)
        {index(0(mac)),
        tbm}
    %table CAP_TABLE_USF_ROAM_AP2STA_ELB( vlanId:int2, ipAddr:byte16, userMac:byte6, userIsolate:int1, hapMac:byte6)
        {
        index(0(vlanId, ipAddr, userMac, userIsolate, hapMac)),
        tbm}
    %table CAP_TABLE_USF_STA(staMac:byte6, pktCacheType:int1, isElbMcDupOnly:int1, vapIfIndex:int4, userRoamFlag: int1)
        {index(0(staMac)), tbm}
    %table hpf_usf_usr_aclid(mac: byte6, type: int1, gid0: int4)
    {index(0(mac)), tbm}
    CAP_TABLE_USF_GLOBAL(_1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15):-
        Usf.PublicUsfConfig(_1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, -).
    CAP_TABLE_USF_AUTH(staMac, authStatus, authVlanEn, authVlan, userVipFlag, ingStatEn, egrStatEn, statId, userGroupId, userGroupEn, needSendUpMsg, userPreRoamIpv4Addr):-
        Usf.PublicAuth(staMac, authStatus, authVlanEn, authVlan, userVipFlag, ingStatEn, egrStatEn, statId, userGroupId, userGroupEn, needSendUpMsg, userPreRoamIpv4Addr).
    CAP_TABLE_USF_PORTAL(staMac, portalPushEn, denyPushEn, httpSendToL4, denyAll, preAuth, portalEscape, checkUserIp, userIpv4Addr, denyArpEn):-
        Usf.PublicPortal(staMac, portalPushEn, denyPushEn, httpSendToL4, denyAll, preAuth, portalEscape, checkUserIp, userIpv4Addr, denyArpEn).
    CAP_TABLE_USF_CAR(staMac, direction, carId, 1):-
        Usf.PublicSta(staMac, -, -, -),
        UsfCarId(staMac,direction,usfCarId),
        CarId(0, usfCarId, carId).
    CAP_TABLE_USF_STAT(statId):-
        Usf.PublicAuth(-, -, -, -, -, 1, 0, statId, -, -, -, -).
    CAP_TABLE_USF_STAT(statId):-
        Usf.PublicAuth(-, -, -, -, -, 0, 1, statId, -, -, -, -).
    CAP_TABLE_USF_STAT(statId):-
        Usf.PublicAuth(-, -, -, -, -, 1, 1, statId, -, -, -, -).
    CAP_TABLE_USF_STA(staMac, pktCacheType, isElbMcDupOnly, vapIfIndex, 1):-
        Usf.PublicSta(staMac, pktCacheType, isElbMcDupOnly, vapIfIndex),
        Usf.PublicROAM(staMac, -, -, -, -, -, -, -, -, -, -, -, -).
    CAP_TABLE_USF_STA(staMac, pktCacheType, isElbMcDupOnly, vapIfIndex, 0):-
        Usf.PublicSta(staMac, pktCacheType, isElbMcDupOnly, vapIfIndex),
        NOT Usf.PublicROAM(staMac, -, -, -, -, -, -, -, -, -, -, -, -).
    CAP_TABLE_USF_ROAM(mac, roamRole, interIsolate, innerIsolate, tunnelDstRole, usrGroupId, homeVapFwdif, foreignVapFwdif, tunnelId, userIsolate):-
        Usf.PublicROAM(mac, roamRole, interIsolate, innerIsolate, tunnelDstRole, usrGroupId, homeVapFwdif, foreignVapFwdif, tunnelId, userIsolate, -, -, -).
    CAP_TABLE_USF_ROAM_AP2STA_ELB(vlanId, ipAddr, mac, userIsolate, hapMac):-
        Usf.PublicROAM(mac, -, -, -, -, -, -, -, -, userIsolate, vlanId, ipAddr, hapMac),
        NotEqual2(vlanId, 0),
        NotEqualbyte16(ipAddr, "0x00000000000000000000000000000000").
    %precedence CAP_TABLE_USF_STA, CAP_TABLE_USF_STAT, CAP_TABLE_USF_AUTH
    hpf_usf_usr_aclid(mac, type, gid) :- Abstract.a_hpf_usf_usr_aclid(mac, type, gid).
}
namespace Hpf {
    %table hpf_capwap_decap(sip: int4, dip: int4, sport: int2, dport: int2, tunnelId: int4) {
        index(0(sip, dip, sport, dport)),
        tbm
    }
    %table hpf_capwap_tunnel(tunnelId: int4, sip: int4, dip: int4, sport: int2, dport: int2, mtu: int2, fwdIfIdx: int4, dtlsIndex: int4,
        preamble: int1, tflag: int1, mflag: int1,
        server_or_client: int1, encrypt: int1, apMac: byte6) {
        index(0(tunnelId)),
        tbm
    }
    %resource capwap_tunnel_id_pool(sip: int4, dip: int4, sport: int2, dport: int2 -> tunnelId: int4) {
        sequential(max_size(128))
    }
    capwap_tunnel_id_pool(sip, dip, sport, dport, -) :-
        Abstract.a_capwap_tunnel(sip, dip, sport, dport, -, -, -,
        -, -, -,
        -, -, -).
    ctrlif_map_fwdif(ifIndex, -) :-
        Abstract.a_capwap_tunnel(-, -, -, -, -, ifIndex, -,
        -, -, -,
        -, -, -).
    hpf_capwap_decap(sip, dip, sport, dport, tunnelId) :-
        Abstract.a_capwap_tunnel(sip, dip, sport, dport, -, -, -,
        -, -, -,
        -, -, -),
        capwap_tunnel_id_pool(sip, dip, sport, dport, tunnelId).
    hpf_capwap_tunnel(tunnelId, sip, dip, sport, dport, mtu, fwdIfIdx, dtlsIndex,
        preamble, tflag, mflag,
        server_or_client, encrypt, apMac) :-
        Abstract.a_capwap_tunnel(sip, dip, sport, dport, mtu, ifIndex, dtlsIndex,
        preamble, tflag, mflag,
        server_or_client, encrypt, apMac),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx),
        capwap_tunnel_id_pool(sip, dip, sport, dport, tunnelId).
    %table hpf_global_capwap_fwdifindex(index:int4, capwapFwdifIndex:int4){
        index(0(index)),
        tbm
    }
    hpf_global_capwap_fwdifindex(0, capwapFwdifIndex):-
        Abstract.a_capwap_tunnel(-, -, -, -, -, ifIndex, -,
        -, -, -,
        -, -, -),
        ctrlif_map_fwdif(ifIndex, capwapFwdifIndex).
}
namespace Hpf {
    %table hpf_mac_ip(mac: byte6, ipAddr: int4, learnType: int1) {
        index(0(mac)),
        tbm
    }
    %table hpf_mac_ipv6(mac: byte6, ipv6Addr: byte16, learnType: int1) {
        index(0(mac)),
        tbm
    }
    hpf_mac_ip(mac, ipAddr, learnType) :-
        Wmp.ConfigMacIp(mac, ipAddr, learnType).
    hpf_mac_ipv6(mac, ipv6Addr, learnType) :-
        Wmp.ConfigMacIpv6(mac, ipv6Addr, learnType).
}
namespace Hpf {
    %table CAP_TABLE_CAR(carId: int4, pktNumOrPktLen: int1, carType: int1, colorMode: int1,
        tc: int8, tpTe: int8, cir: int8, cbs: int8, pir: int8, pbsEbs: int8,
        greenPermit: int1, yellowPermit: int1, redPermit: int1)
        {index(0(carId)), tbm}
    %table CAP_TABLE_CAR_PPS(carId: int4, pps: int4)
        {index(0(carId)), tbm}
    %resource CarId(svc: int4, id: int4 -> carId: int4) { sequential(max_size(32742)) }
    %resource UsfCarId(mac: byte6, direction: int1 -> carId: int4) { sequential(max_size(32742)) }
    UsfCarId(mac, 0, -) :- Usf.IngCar(mac, -, -, -, -, -, -, -, -, -, -).
    UsfCarId(mac, 1, -) :- Usf.EgrCar(mac, -, -, -, -, -, -, -, -, -, -).
    CarId(0, id, -) :- UsfCarId(-, -, id).
    CAP_TABLE_CAR(carId, _1, _2, _3, _5, _7, _4, _5, _6, _7, _8, _9, _10) :-
        Usf.IngCar(mac, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10),
        UsfCarId(mac, 0, id),
        CarId(0, id, carId).
    CAP_TABLE_CAR(carId, _1, _2, _3, _5, _7, _4, _5, _6, _7, _8, _9, _10) :-
        Usf.EgrCar(mac, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10),
        UsfCarId(mac, 1, id),
        CarId(0, id, carId).
}
namespace Hpf {
%table CAP_TABLE_CPCAR_PRO(protocolType: int4, flags: int1, carId: int4, priority: int1)
    { index(0(protocolType, flags)), tbm}
%table CAP_TABLE_HSEC_BLACKLIST(policy_id:int4, filter_id:int4, group_id:int4, group_type:int4)
    { index(0(policy_id, filter_id)), tbm}
%table CAP_TABLE_HSEC_ATKSRC(id:int4, enable:int1, alarmEnable:int1, alarmThreshold:int4, penaltyEnable:int1,
    penaltyThreshold:int4, defendMask:int1)
    { index(0(id)), tbm}
%table CAP_TABLE_SEC_ATK_SIP(ipv4Addr:int4, ipv6Addr:byte16, isIpv6:int1)
    { index(0(ipv4Addr, ipv6Addr, isIpv6)), tbm}
%table CAP_TABLE_SEC_ATK_SMAC(mac:byte6)
    { index(0(mac)), tbm}
%table CAP_TABLE_HSEC_AUTODEF(attackType:int4, enable:int1, carId:int4)
    { index(0(attackType)), tbm}
%resource CpCarId(protocolType: int4, flags: int1 -> carId: int4) { sequential(max_size(32742)) }
CpCarId(protocolType, flags, -) :- Hsec.Mid_PolicyCpcar(protocolType, flags, pps), NotEqual4(pps, 0).
CarId(1, id, -) :- CpCarId(-, -, id).
CAP_TABLE_CAR_PPS(carId, pps) :-
    Hsec.Mid_PolicyCpcar(protocolType, flags, pps),
    CpCarId(protocolType, flags, id),
    CarId(1, id, carId).
CAP_TABLE_CPCAR_PRO(protocolType, flags, carId, priority) :-
    CpCarId(protocolType, flags, id), Hsec.DefaultCpcar(protocolType, flags, priority, -), CarId(1, id, carId).
CAP_TABLE_HSEC_BLACKLIST(policy_id, filter_id, downGroupId, downGroupType) :-
    Hsec.PolicyFilterCfg(policy_id, filter_id, group_id),
    Hsec.AppliedPolicyCfg(policy_id, -),
    Acl.GroupCfg(group_id, -, -, groupType, -, -, -, -),
    Sacl.TranslateGroupType(groupType, 0, downGroupType),
    Sacl.GenDownGroupID(2, 0, 7, 0, group_id, downGroupId).
CAP_TABLE_HSEC_ATKSRC(policyId, _1, _2, _3, _4, _5, _6) :-
    Hsec.AutoDefend(policyId, _1, _2, _3, _4, _5, _6), Hsec.AppliedPolicyCfg(policyId, -).
CAP_TABLE_SEC_ATK_SIP(ipv4Addr, ipv6Addr, isIpv6) :- Hsec.Mid_AttackSource(-, 2, ipv4Addr, ipv6Addr, -, isIpv6).
CAP_TABLE_SEC_ATK_SIP(ipv4Addr, ipv6Addr, isIpv6) :- Hsec.Mid_AttackSource(-, 3, ipv4Addr, ipv6Addr, -, isIpv6).
CAP_TABLE_SEC_ATK_SMAC(mac) :- Hsec.Mid_AttackSource(-, 1, - , -, mac, -).
CAP_TABLE_SEC_ATK_SMAC(mac) :- Hsec.Mid_AttackSource(-, 3, - , -, mac, -).
CarId(2, 3, -) :- Hsec.AntiAttack(3, -, -).
CarId(2, 4, -) :- Hsec.AntiAttack(4, -, -).
CarId(2, 5, -) :- Hsec.AntiAttack(5, -, -).
CAP_TABLE_CAR_PPS(carId, pps) :- Hsec.AntiAttack(attackType, 1, pps), CarId(2, attackType, carId).
CAP_TABLE_HSEC_AUTODEF(1, enable, 0) :- Hsec.AntiAttack(1, enable, -).
CAP_TABLE_HSEC_AUTODEF(2, enable, 0) :- Hsec.AntiAttack(2, enable, -).
CAP_TABLE_HSEC_AUTODEF(3, enable, carId) :- Hsec.AntiAttack(3, enable, -), CarId(2, 3, carId).
CAP_TABLE_HSEC_AUTODEF(4, enable, carId) :- Hsec.AntiAttack(4, enable, -), CarId(2, 4, carId).
CAP_TABLE_HSEC_AUTODEF(5, enable, carId) :- Hsec.AntiAttack(5, enable, -), CarId(2, 5, carId).
}
namespace Hpf {
%table CAP_TABLE_MIRR_OBSERVE(
    observeId: int2, ctrlIfIdx: int4, fwdIfIdx: int4)
{
    index(0(observeId)), tbm
}
CAP_TABLE_MIRR_OBSERVE(observeIndex, ctrlIfIdx, fwdIfIdx) :-
    Mir.ObserverIndex(observeIndex, ctrlIfIdx), ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
}
namespace Hpf {
%table CAP_TABLE_FWD_CAPT(id: int1, aclGroupId: int4, aclGroupType: int4, direction: int1, localhost: int1,
    vlanId: int2, ifIndexList: byte32,
    packetNumber: int2, packetLength: int2, timeout: int8)
    {
        index(0(id)), tbm
    }
CAP_TABLE_FWD_CAPT(id, downGroupId, captureAclGroupType, direction, _1, _2, _3, _4, _5, _6) :-
    Acl.GroupCfg(aclGroupId, isIpv6, -, aclGroupType, -, -, -, -),
    Sacl.TranslateGroupType(aclGroupType, isIpv6, captureAclGroupType),
    Capture.Config(id, aclGroupId, direction, _1, _2, _3, _4, _5, _6),
    Sacl.GenDownGroupID(2 , direction, 8 , 0 , aclGroupId, downGroupId).
}
%function init()
%function uninit()
