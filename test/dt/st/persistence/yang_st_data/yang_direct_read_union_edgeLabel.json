[{"name": "root_L1", "source_vertex_label": "root", "dest_vertex_label": "L1", "source_node_path": "/C1/C2/C3", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_L2", "source_vertex_label": "root", "dest_vertex_label": "L2", "source_node_path": "/C1/C2/C3", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "L1_L3", "source_vertex_label": "L1", "dest_vertex_label": "L3", "source_node_path": "/C5", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_LF1", "source_vertex_label": "root", "dest_vertex_label": "LF1", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]