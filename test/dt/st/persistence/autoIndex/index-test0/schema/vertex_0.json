[{"name": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "alias": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "type": "list", "np_access": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "labelId", "type": "uint32", "nullable": false}, {"name": "nodeId", "type": "uint16", "nullable": false}, {"name": "propeId", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":id", ":pid", "labelId", "nodeId", "propeId"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "index-test0", "type": "container", "config": {"check_validity": true}, "is_root": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "ietf-hardware:hardware", "type": "container", "fields": []}, {"name": "ietf-interfaces:interfaces", "type": "container", "fields": []}, {"name": "huawei-board:boards", "type": "container", "fields": []}, {"name": "ietf-routing:routing", "type": "container", "fields": [{"name": "control-plane-protocols", "type": "container", "fields": []}]}, {"name": "bbf-xponani-power-management:xponani-power-management-profiles", "type": "container", "fields": []}, {"name": "huawei-device:device", "type": "container", "fields": [{"name": "management-ip", "type": "string", "nullable": true}, {"name": "description", "type": "string", "nullable": true, "length": "1..200"}, {"name": "remove-mgmt-port-config", "type": "boolean", "nullable": true, "default": false}, {"name": "user-interface-number", "type": "uint8", "nullable": true, "range": "0..15", "default": 5}, {"name": "agile-controllers", "type": "container", "fields": [{"name": "type", "type": "string", "nullable": true, "length": "1..255"}]}, {"name": "agile-controller-whitelists", "type": "container", "fields": []}, {"name": "console", "type": "container", "fields": [{"name": "ble-config", "type": "container", "fields": [{"name": "mode", "type": "enum", "enumerate": [{"name": "disabled", "value": 1}, {"name": "dynamic", "value": 2}, {"name": "persistent", "value": 3}], "nullable": true, "default": 2}]}, {"name": "uboot", "type": "container", "fields": [{"name": "password", "type": "string", "nullable": true, "length": "8..80"}]}]}, {"name": "config-lock-info", "type": "container", "fields": [{"name": "locked", "type": "boolean", "nullable": true}]}, {"name": "config-header-info", "type": "container", "fields": [{"name": "login", "type": "container", "fields": [{"name": "information-type", "type": "choice", "fields": [{"name": "information-text", "type": "case", "fields": [{"name": "text", "type": "string", "nullable": true, "length": "1..2000"}]}, {"name": "information-file", "type": "case", "fields": [{"name": "file", "type": "string", "nullable": true, "length": "5..64"}]}]}]}, {"name": "shell", "type": "container", "fields": [{"name": "information-type", "type": "choice", "fields": [{"name": "information-text", "type": "case", "fields": [{"name": "text", "type": "string", "nullable": true, "length": "1..2000"}]}, {"name": "information-file", "type": "case", "fields": [{"name": "file", "type": "string", "nullable": true, "length": "5..64"}]}]}]}]}, {"name": "model-info", "type": "container", "fields": [{"name": "mode", "type": "enum", "enumerate": [{"name": "soft-order", "value": 0}, {"name": "hard-order", "value": 1}, {"name": "flow-order", "value": 2}, {"name": "port-order", "value": 3}], "nullable": true}]}]}, {"name": "huawei-device:config-virtual-license", "type": "container", "fields": [{"name": "magic-word", "type": "string", "nullable": true, "length": "1..2048"}]}, {"name": "huawei-device:set-alarm-threshold", "type": "container", "fields": []}, {"name": "huawei-device:country-code-info", "type": "container", "fields": [{"name": "country-code", "type": "string", "nullable": true, "length": "0..5"}]}, {"name": "bbf-xpongemtcont:xpongemtcont", "type": "container", "fields": [{"name": "traffic-descriptor-profiles", "type": "container", "fields": []}, {"name": "tconts", "type": "container", "fields": []}, {"name": "gemports", "type": "container", "fields": []}, {"name": "gem-bundle", "type": "container", "fields": []}]}, {"name": "bbf-qos-traffic-mngt:tm-profiles", "type": "container", "fields": []}, {"name": "huawei-protection-group:protection-groups", "type": "container", "fields": [{"name": "global-configs", "type": "container", "fields": []}]}, {"name": "ietf-alarms:alarms", "type": "container", "fields": [{"name": "control", "type": "container", "fields": [{"name": "max-alarm-status-changes", "type": "string", "nullable": true, "default": "32"}, {"name": "notify-status-changes", "type": "enum", "enumerate": [{"name": "all-state-changes", "value": 0}, {"name": "raise-and-clear", "value": 1}, {"name": "severity-level", "value": 2}], "nullable": true, "clause": [{"type": "must", "formula": ". != 'severity-level' or ../notify-severity-level"}], "default": 0}, {"name": "notify-severity-level", "type": "enum", "enumerate": [{"name": "indeterminate", "value": 2}, {"name": "warning", "value": 3}, {"name": "minor", "value": 4}, {"name": "major", "value": 5}, {"name": "critical", "value": 6}], "nullable": true}, {"name": "alarm-shelving", "type": "container", "fields": []}]}]}, {"name": "ietf-snmp:snmp", "type": "container", "fields": [{"name": "engine", "type": "container", "fields": [{"name": "enabled", "type": "boolean", "nullable": true, "default": false}, {"name": "engine-id", "type": "string", "nullable": true}, {"name": "enable-authen-traps", "type": "boolean", "nullable": true}, {"name": "version", "type": "container", "fields": [{"name": "v1", "type": "uint8", "nullable": true}, {"name": "v2c", "type": "uint8", "nullable": true}, {"name": "v3", "type": "uint8", "nullable": true}]}]}, {"name": "usm", "type": "container", "fields": [{"name": "local", "type": "container", "fields": []}]}, {"name": "tsm", "type": "container", "fields": [{"name": "use-prefix", "type": "boolean", "nullable": true, "default": false}]}, {"name": "vacm", "type": "container", "fields": []}, {"name": "tlstm", "type": "container", "fields": []}]}, {"name": "ietf-netconf-acm:nacm", "type": "container", "fields": [{"name": "enable-nacm", "type": "boolean", "nullable": true, "default": true}, {"name": "read-default", "type": "enum", "enumerate": [{"name": "permit", "value": 0}, {"name": "deny", "value": 1}], "nullable": true, "default": 0}, {"name": "write-default", "type": "enum", "enumerate": [{"name": "permit", "value": 0}, {"name": "deny", "value": 1}], "nullable": true, "default": 1}, {"name": "exec-default", "type": "enum", "enumerate": [{"name": "permit", "value": 0}, {"name": "deny", "value": 1}], "nullable": true, "default": 0}, {"name": "enable-external-groups", "type": "boolean", "nullable": true, "default": true}, {"name": "groups", "type": "container", "fields": []}]}, {"name": "huawei-frame:frames", "type": "container", "fields": []}, {"name": "huawei-network-instance:network-instance", "type": "container", "fields": [{"name": "global", "type": "container", "fields": [{"name": "cfg-router-id", "type": "string", "nullable": true}, {"name": "as-notation-plain", "type": "boolean", "nullable": true, "default": false}, {"name": "route-distinguisher-auto-ip", "type": "string", "nullable": true}, {"name": "vpn-instance-limit", "type": "uint32", "nullable": true, "range": "1..4294967295"}]}, {"name": "instances", "type": "container", "fields": []}]}, {"name": "bbf-xpon:xpon", "type": "container", "fields": [{"name": "ictp", "type": "container", "fields": [{"name": "activated", "type": "boolean", "nullable": true, "default": false}, {"name": "all-ictp-proxies-all-channel-groups", "type": "container", "fields": []}]}, {"name": "wavelength-profiles", "type": "container", "fields": []}, {"name": "multicast-gemports", "type": "container", "fields": []}, {"name": "multicast-distribution-set", "type": "container", "fields": []}, {"name": "onu-tr069-server-profiles", "type": "container", "fields": []}, {"name": "traffic-alarm-profiles", "type": "container", "fields": []}, {"name": "onu-power-shedding-profiles", "type": "container", "fields": []}, {"name": "onu-alm-policy-profiles", "type": "container", "fields": []}, {"name": "global-opers", "type": "container", "fields": [{"name": "dba-bandwidth-assignment-mode", "type": "enum", "enumerate": [{"name": "max-bandwidth-usage", "value": 1}, {"name": "min-loop-delay", "value": 2}, {"name": "manual", "value": 3}], "nullable": true, "default": 2}, {"name": "rogue-ont-detect-switch", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "default": 2}, {"name": "ont-self-detect-switch", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}, {"name": "unconcern", "value": 3}], "nullable": true, "default": 3}, {"name": "anti-weak-light-switch", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "default": 2}, {"name": "ont-authentication-conflict-check-by-sn", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "default": 1}, {"name": "ont-authentication-conflict-check-by-loid", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "default": 1}, {"name": "ont-pq-map-policy", "type": "enum", "enumerate": [{"name": "gem-map", "value": 1}, {"name": "cos-map", "value": 2}], "nullable": true, "default": 1}, {"name": "gpon-multicast-gemport-id", "type": "uint32", "nullable": true, "range": "4000..4095", "default": 4095}, {"name": "xgpon-multicast-gemport-id", "type": "uint32", "nullable": true, "range": "65439..65534", "default": 65534}, {"name": "xpon-rogueont-detect-rssi-threshold", "type": "int32", "nullable": true, "range": "-3500..-1000", "default": -2800}, {"name": "xpon-rogueont-clear-rssi-threshold", "type": "int32", "nullable": true, "range": "-3500..-1000", "default": -2800}, {"name": "xpon-olt-rx-power-mode", "type": "enum", "enumerate": [{"name": "normal", "value": 1}, {"name": "high-precision-mode", "value": 2}], "nullable": true, "default": 2}, {"name": "group-poweroff-control-switch", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "default": 2}, {"name": "suppress-initial-alarm-state", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}, {"name": "enable-except-optical-alarm", "value": 3}], "nullable": true, "default": 1}, {"name": "onu-user-multicast-process", "type": "enum", "enumerate": [{"name": "forward", "value": 1}, {"name": "discard", "value": 2}], "nullable": true, "default": 1}, {"name": "typeb-los-alarm-control-state", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "default": 2}, {"name": "onu-distance-variation-threshold", "type": "string", "nullable": true, "default": "unlimited"}, {"name": "omcc-encrypt", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "default": 1}, {"name": "gemport-encrypt", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "default": 1}, {"name": "optical-info-mode", "type": "enum", "enumerate": [{"name": "normal", "value": 1}, {"name": "dt", "value": 2}], "nullable": true, "default": 1}, {"name": "anti-dos-ploam-switch", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "default": 2}, {"name": "ont-auto-find", "type": "container", "fields": [{"name": "timeout", "type": "string", "nullable": true, "default": "300"}, {"name": "distance-time", "type": "uint8", "nullable": true, "range": "1..10", "default": 5}, {"name": "conflict-check-switch", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "default": 2}, {"name": "additional-notice-switch", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "default": 2}, {"name": "additional-notice-times", "type": "uint8", "nullable": true, "clause": [{"type": "when", "formula": "../additional-notice-switch = 'enable'"}], "range": "1..10", "default": 3}, {"name": "additional-notice-distance-times", "type": "uint8", "nullable": true, "clause": [{"type": "when", "formula": "../additional-notice-switch = 'enable'"}], "range": "30..100", "default": 100}]}, {"name": "ont-interoperability", "type": "container", "fields": [{"name": "tcont-pq-priority-reverse", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "default": 2}, {"name": "onu-tcont-config-order", "type": "enum", "enumerate": [{"name": "omci-first", "value": 1}, {"name": "ploam-first", "value": 2}], "nullable": true, "default": 1}, {"name": "mode", "type": "enum", "enumerate": [{"name": "itu-t", "value": 1}, {"name": "ctc", "value": 2}, {"name": "eric-v1", "value": 3}, {"name": "eric-v2", "value": 4}, {"name": "itu-tg984", "value": 5}, {"name": "itu-tg988", "value": 6}, {"name": "cuc", "value": 7}, {"name": "cmcc", "value": 8}, {"name": "eci", "value": 9}, {"name": "mode1", "value": 10}, {"name": "mode2", "value": 11}, {"name": "mode2-v2", "value": 12}, {"name": "mode3", "value": 13}, {"name": "mode4", "value": 14}, {"name": "mode2-v3", "value": 15}, {"name": "mode2-v4", "value": 16}, {"name": "mode5", "value": 17}, {"name": "eci-v2", "value": 18}, {"name": "mode1-v2", "value": 19}, {"name": "mode2-v5", "value": 20}, {"name": "eric-v3", "value": 21}, {"name": "eric-v4", "value": 22}, {"name": "mode6", "value": 23}, {"name": "eric-v5", "value": 24}, {"name": "mode7", "value": 25}], "nullable": true, "default": 1}, {"name": "active-mode", "type": "enum", "enumerate": [{"name": "immediate", "value": 1}, {"name": "next-startup", "value": 2}], "nullable": true, "default": 2}]}, {"name": "alarm-los-control", "type": "container", "fields": [{"name": "control-switch", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "default": 2}, {"name": "dgi-threshold-time", "type": "uint16", "nullable": true, "range": "6..600", "default": 6}, {"name": "dgi-threshold-ratio", "type": "string", "nullable": true, "default": "unconcern"}]}, {"name": "operator-check", "type": "container", "fields": [{"name": "switch", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "default": 2}, {"name": "code", "type": "string", "nullable": true, "length": "0..16"}, {"name": "history-code-max-number", "type": "uint16", "nullable": true, "range": "0..11", "default": 10}, {"name": "version", "type": "enum", "enumerate": [{"name": "version1", "value": 1}, {"name": "version2", "value": 2}], "nullable": true, "default": 1}]}]}, {"name": "optic-alm-threshold-profiles", "type": "container", "fields": []}]}, {"name": "huawei-energy-management-an:energy-management-an", "type": "container", "fields": [{"name": "energy-saving", "type": "container", "fields": [{"name": "mode", "type": "enum", "enumerate": [{"name": "basic", "value": 1}, {"name": "deep", "value": 2}, {"name": "optimal", "value": 3}, {"name": "standard", "value": 4}, {"name": "custom", "value": 5}], "nullable": true}, {"name": "ont-auto-find-check-free", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}], "nullable": true, "default": 2}]}, {"name": "energy-saving-modes", "type": "container", "fields": []}]}, {"name": "huawei-resource-pool-an:resource-pool-an", "type": "container", "fields": [{"name": "nsi", "type": "container", "fields": [{"name": "nsi-enable", "type": "boolean", "nullable": true}]}, {"name": "resource-pool-esns", "type": "container", "fields": []}, {"name": "resource-pools", "type": "container", "fields": []}]}, {"name": "ietf-system:system", "type": "container", "fields": [{"name": "contact", "type": "string", "nullable": true}, {"name": "hostname", "type": "string", "nullable": true, "length": "1..253"}, {"name": "location", "type": "string", "nullable": true}, {"name": "clock", "type": "container", "fields": [{"name": "timezone", "type": "choice", "fields": [{"name": "timezone-name", "type": "case", "fields": [{"name": "timezone-name", "type": "string", "nullable": true}]}, {"name": "timezone-utc-offset", "type": "case", "fields": [{"name": "timezone-utc-offset", "type": "int16", "nullable": true, "range": "-1500..1500"}]}]}]}, {"name": "ntp", "type": "container", "fields": [{"name": "enabled", "type": "boolean", "nullable": true, "default": true}]}, {"name": "dns-resolver", "type": "container", "fields": [{"name": "options", "type": "container", "fields": [{"name": "timeout", "type": "uint8", "nullable": true, "range": "1..max", "default": 5}, {"name": "attempts", "type": "uint8", "nullable": true, "range": "1..max", "default": 2}]}]}, {"name": "radius", "type": "container", "fields": [{"name": "options", "type": "container", "fields": [{"name": "timeout", "type": "uint8", "nullable": true, "range": "1..max", "default": 5}, {"name": "attempts", "type": "uint8", "nullable": true, "range": "1..max", "default": 2}]}]}, {"name": "authentication", "type": "container", "fields": []}, {"name": "sys-para", "type": "container", "fields": [{"name": "sys-time", "type": "string", "nullable": true}]}, {"name": "dhcpv6-global", "type": "container", "fields": [{"name": "dhcpv6-reverse", "type": "boolean", "nullable": true, "default": false}, {"name": "dhcpv6-packet-max-length", "type": "uint32", "nullable": true, "range": "500..1500", "default": 1500}, {"name": "dhcpv6-option", "type": "container", "fields": [{"name": "action-type", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}, {"name": "rebuild", "value": 3}], "nullable": true, "default": 2}, {"name": "transparent-user-vlan", "type": "container", "fields": [{"name": "user-vlan", "type": "choice", "fields": [{"name": "all-type", "type": "case", "default": true, "fields": [{"name": "all-enable", "type": "boolean", "nullable": true, "default": false}]}, {"name": "other", "type": "case", "fields": [{"name": "untag", "type": "boolean", "nullable": true}, {"name": "multitag", "type": "boolean", "nullable": true}, {"name": "single-vlan-list", "type": "string", "nullable": true}]}]}]}]}, {"name": "dhcpv6-mode", "type": "container", "fields": [{"name": "mode", "type": "enum", "enumerate": [{"name": "layer-2", "value": 1}, {"name": "layer-3", "value": 2}], "nullable": true, "default": 1}, {"name": "option16", "type": "boolean", "nullable": true, "clause": [{"type": "when", "formula": "../mode = 'layer-3'"}], "default": false}]}]}, {"name": "dhcpv4-relay-global", "type": "container", "fields": [{"name": "dhcp-reverse", "type": "boolean", "nullable": true, "default": false}, {"name": "dhcp-relay-mode", "type": "enum", "enumerate": [{"name": "layer2", "value": 1}, {"name": "layer3-standard", "value": 2}, {"name": "layer3-option60", "value": 3}, {"name": "layer3-macrange", "value": 4}], "nullable": true, "default": 1}, {"name": "dhcp-option82", "type": "container", "fields": [{"name": "action-type", "type": "enum", "enumerate": [{"name": "enable", "value": 1}, {"name": "disable", "value": 2}, {"name": "forward", "value": 3}, {"name": "rebuild", "value": 4}], "nullable": true, "default": 2}, {"name": "permit-forwarding-default-policy", "type": "boolean", "nullable": true, "default": false}, {"name": "transparent-user-vlan", "type": "container", "fields": [{"name": "user-vlan", "type": "choice", "fields": [{"name": "all-type", "type": "case", "fields": [{"name": "all-enable", "type": "boolean", "nullable": true}]}, {"name": "other", "type": "case", "fields": [{"name": "untag", "type": "boolean", "nullable": true}, {"name": "multitag", "type": "boolean", "nullable": true}, {"name": "vlan-list", "type": "string", "nullable": true}]}]}]}]}]}, {"name": "pppoe-global", "type": "container", "fields": [{"name": "pitp", "type": "container", "fields": [{"name": "action-type", "type": "enum", "enumerate": [{"name": "disable", "value": 1}, {"name": "vmode", "value": 2}, {"name": "pmode", "value": 3}, {"name": "forward-pmode", "value": 4}, {"name": "rebuild-pmode", "value": 5}], "nullable": true, "default": 1}, {"name": "sub-option-3", "type": "enum", "enumerate": [{"name": "disable", "value": 1}, {"name": "enable", "value": 2}], "nullable": true, "default": 1}, {"name": "sub-option-6", "type": "enum", "enumerate": [{"name": "disable", "value": 1}, {"name": "enable", "value": 2}], "nullable": true, "default": 1}, {"name": "sub-option-90", "type": "enum", "enumerate": [{"name": "disable", "value": 1}, {"name": "enable", "value": 2}], "nullable": true, "default": 1}, {"name": "vmode-ether-type", "type": "string", "nullable": true, "default": "0x8200"}]}, {"name": "transparent-user-vlan", "type": "container", "fields": [{"name": "user-vlan", "type": "choice", "fields": [{"name": "all-type", "type": "case", "default": true, "fields": [{"name": "all-enable", "type": "boolean", "nullable": true, "default": false}]}, {"name": "other", "type": "case", "fields": [{"name": "untag", "type": "boolean", "nullable": true}, {"name": "multitag", "type": "boolean", "nullable": true}, {"name": "single-vlan-list", "type": "string", "nullable": true}]}]}]}, {"name": "pppoe-reverse", "type": "container", "fields": [{"name": "enable", "type": "boolean", "nullable": true, "default": false}]}, {"name": "pppoe-monitor-uplink-port", "type": "container", "fields": [{"name": "action-type", "type": "enum", "enumerate": [{"name": "disable", "value": 1}, {"name": "enable", "value": 2}], "nullable": true, "default": 2}, {"name": "action-time", "type": "string", "nullable": true, "default": "03:00:00-23:59:59"}, {"name": "notify-action", "type": "enum", "enumerate": [{"name": "disable", "value": 1}, {"name": "enable", "value": 2}], "nullable": true, "default": 1}, {"name": "period", "type": "uint32", "nullable": true, "range": "1..60", "default": 3}, {"name": "monitor-threshold", "type": "uint32", "nullable": true, "range": "10..90", "default": 50}, {"name": "active-threshold", "type": "uint32", "nullable": true, "clause": [{"type": "must", "formula": "../monitor-threshold"}], "range": "10..10000", "default": 10}]}]}, {"name": "subscriber-management", "type": "container", "fields": [{"name": "access-node-id", "type": "string", "nullable": true, "length": "0..63", "default": "access-node-not-configured"}, {"name": "actual-data-rate-type", "type": "enum", "enumerate": [{"name": "actual-data-rate", "value": 1}, {"name": "actual-net-data-rate", "value": 2}], "nullable": true, "default": 1}, {"name": "subscriber-backhaul-id", "type": "container", "fields": [{"name": "ifname", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/index-test0/ietf-interfaces:interfaces/if:interface.1/name"}]}, {"name": "access-type", "type": "enum", "enumerate": [{"name": "gpon", "value": 1}, {"name": "xgspon", "value": 2}], "nullable": true}, {"name": "backhaul-id", "type": "string", "nullable": true, "length": "1..16"}]}, {"name": "subscriber-sub-option", "type": "container", "fields": [{"name": "access-loop-suboptions", "type": "string", "nullable": true}, {"name": "access-type", "type": "enum", "enumerate": [{"name": "eth", "value": 1}, {"name": "xpon", "value": 2}], "nullable": true}, {"name": "sub-option-value", "type": "uint32", "nullable": true, "range": "0..16777215"}]}, {"name": "aggregation-circuit-id-format", "type": "container", "fields": [{"name": "subscriber-object", "type": "enum", "enumerate": [{"name": "pitp-pmode", "value": 1}, {"name": "dhcp-option82", "value": 2}, {"name": "dhcpv6-option", "value": 3}, {"name": "radius-nas-port-id", "value": 4}, {"name": "ancp", "value": 5}, {"name": "pitp-vmode", "value": 10}], "nullable": true}, {"name": "string-type", "type": "enum", "enumerate": [{"name": "ascii", "value": 1}, {"name": "binary", "value": 2}], "nullable": true}, {"name": "access-type", "type": "enum", "enumerate": [{"name": "xpon", "value": 1}, {"name": "ont", "value": 2}, {"name": "mxu", "value": 3}], "nullable": true}, {"name": "format-string", "type": "string", "nullable": true, "length": "0..127"}]}]}]}, {"name": "huawei-diag-info-collection-an:diag-info-collection-an", "type": "container", "fields": []}, {"name": "bbf-xpon-ont-load:xpon-ont-load", "type": "container", "fields": [{"name": "global-info", "type": "container", "fields": [{"name": "file-type", "type": "enum", "enumerate": [{"name": "app", "value": 1}, {"name": "cfg", "value": 2}], "nullable": true}, {"name": "server-ip", "type": "string", "nullable": true}, {"name": "transfer-protocol", "type": "enum", "enumerate": [{"name": "tftp", "value": 1}, {"name": "ftp", "value": 2}, {"name": "sftp", "value": 3}], "nullable": true}, {"name": "user-name", "type": "string", "nullable": true, "length": "1..40"}, {"name": "password", "type": "string", "nullable": true, "length": "1..128"}, {"name": "server-port", "type": "uint16", "nullable": true}, {"name": "file-name", "type": "string", "nullable": true, "length": "1..80"}, {"name": "image-active-mode", "type": "enum", "enumerate": [{"name": "next-startup", "value": 1}, {"name": "immediate", "value": 2}, {"name": "graceful", "value": 3}], "nullable": true}, {"name": "aes128-key", "type": "string", "nullable": true, "length": "32"}, {"name": "iv", "type": "string", "nullable": true, "length": "1|16"}]}, {"name": "ont-selects", "type": "container", "fields": []}]}, {"name": "an-protection-group:protection-groups", "type": "container", "fields": [{"name": "dual-parenting-sync", "type": "container", "fields": [{"name": "local-node-ip-addr", "type": "string", "nullable": true}, {"name": "local-node-communication-key", "type": "string", "nullable": true, "length": "0..16"}, {"name": "syn-switch", "type": "enum", "enumerate": [{"name": "enable", "value": 0}, {"name": "disable", "value": 1}], "nullable": true, "default": 1}]}]}, {"name": "bbf-subscriber-profiles:subscriber-profiles", "type": "container", "fields": []}, {"name": "ieee802-dot1ax:lag-system", "type": "container", "fields": []}, {"name": "bbf-l2-forwarding:forwarding", "type": "container", "fields": [{"name": "forwarders", "type": "container", "fields": []}, {"name": "flooding-policies-profiles", "type": "container", "fields": []}, {"name": "forwarding-databases", "type": "container", "fields": []}, {"name": "split-horizon-profiles", "type": "container", "fields": []}, {"name": "mac-learning-control-profiles", "type": "container", "fields": []}, {"name": "forwarder-policy-profiles", "type": "container", "fields": []}]}, {"name": "bbf-qos-priority-profiles:cos-group-profiles", "type": "container", "fields": []}, {"name": "bbf-qos-priority-profiles:pbit-to-pbit-mapping-profiles", "type": "container", "fields": []}, {"name": "bbf-qos-priority-profiles:ipprec-to-pbit-mapping-profiles", "type": "container", "fields": []}, {"name": "bbf-qos-priority-profiles:dscp-to-pbit-mapping-profiles", "type": "container", "fields": []}, {"name": "bbf-qos-priority-profiles:dscp-to-dscp-mapping-profiles", "type": "container", "fields": []}, {"name": "bbf-qos-priority-profiles:queue-mapping-profiles", "type": "container", "fields": []}, {"name": "bbf-qos-classifiers:classifiers", "type": "container", "fields": []}, {"name": "bbf-qos-policies:policies", "type": "container", "fields": []}, {"name": "bbf-qos-policies:qos-policy-profiles", "type": "container", "fields": []}, {"name": "bbf-qos-policing:policing-profiles", "type": "container", "fields": []}, {"name": "bbf-qos-policing-profiles:priority-group-policing-profiles", "type": "container", "fields": []}, {"name": "bbf-qos-policing-profiles:car-threshold-profiles", "type": "container", "fields": []}, {"name": "bbf-qos-traffic-mngt-profiles:cos-queue-map", "type": "container", "fields": [{"name": "cos0", "type": "uint32", "nullable": true, "range": "0..7", "default": 0}, {"name": "cos1", "type": "uint32", "nullable": true, "range": "0..7", "default": 1}, {"name": "cos2", "type": "uint32", "nullable": true, "range": "0..7", "default": 2}, {"name": "cos3", "type": "uint32", "nullable": true, "range": "0..7", "default": 3}, {"name": "cos4", "type": "uint32", "nullable": true, "range": "0..7", "default": 4}, {"name": "cos5", "type": "uint32", "nullable": true, "range": "0..7", "default": 5}, {"name": "cos6", "type": "uint32", "nullable": true, "range": "0..7", "default": 6}, {"name": "cos7", "type": "uint32", "nullable": true, "range": "0..7", "default": 7}]}, {"name": "bbf-qos-traffic-mngt-profiles:queue-scheduler", "type": "container", "fields": [{"name": "queue-scheduling-type", "type": "choice", "fields": [{"name": "strict-priority", "type": "case", "default": true, "fields": [{"name": "strict-priority", "type": "uint8", "nullable": true}]}, {"name": "wrr", "type": "case", "fields": [{"name": "queue0-weight", "type": "uint8", "nullable": false, "range": "0..100|255"}, {"name": "queue1-weight", "type": "uint8", "nullable": false, "range": "0..100|255"}, {"name": "queue2-weight", "type": "uint8", "nullable": false, "range": "0..100|255"}, {"name": "queue3-weight", "type": "uint8", "nullable": false, "range": "0..100|255"}, {"name": "queue4-weight", "type": "uint8", "nullable": false, "range": "0..100|255"}, {"name": "queue5-weight", "type": "uint8", "nullable": false, "range": "0..100|255"}, {"name": "queue6-weight", "type": "uint8", "nullable": false, "range": "0..100|255"}, {"name": "queue7-weight", "type": "uint8", "nullable": true, "range": "0..100|255"}]}]}]}, {"name": "bbf-qos-traffic-mngt-profiles:queue-wred", "type": "container", "fields": [{"name": "queues", "type": "container", "fields": []}]}, {"name": "bbf-qos-traffic-mngt-profiles:early-drop", "type": "container", "fields": [{"name": "mode", "type": "container", "fields": [{"name": "mode", "type": "enum", "enumerate": [{"name": "pri-base", "value": 1}, {"name": "color-base", "value": 2}, {"name": "off", "value": 3}, {"name": "color-wred", "value": 4}], "nullable": true, "default": 2}]}, {"name": "priority", "type": "container", "fields": [{"name": "threshold0", "type": "uint8", "nullable": true, "range": "0..100", "default": 100}, {"name": "threshold1", "type": "uint8", "nullable": true, "range": "0..100", "default": 100}, {"name": "threshold2", "type": "uint8", "nullable": true, "range": "0..100", "default": 100}, {"name": "threshold3", "type": "uint8", "nullable": true, "range": "0..100", "default": 100}, {"name": "threshold4", "type": "uint8", "nullable": true, "range": "0..100", "default": 100}, {"name": "threshold5", "type": "uint8", "nullable": true, "range": "0..100", "default": 100}, {"name": "threshold6", "type": "uint8", "nullable": true, "range": "0..100", "default": 100}, {"name": "threshold7", "type": "uint8", "nullable": true, "range": "0..100", "default": 100}]}, {"name": "drop-level", "type": "container", "fields": [{"name": "cos0", "type": "enum", "enumerate": [{"name": "class0", "value": 1}, {"name": "class1", "value": 2}, {"name": "class2", "value": 3}], "nullable": true, "default": 1}, {"name": "cos1", "type": "enum", "enumerate": [{"name": "class0", "value": 1}, {"name": "class1", "value": 2}, {"name": "class2", "value": 3}], "nullable": true, "default": 1}, {"name": "cos2", "type": "enum", "enumerate": [{"name": "class0", "value": 1}, {"name": "class1", "value": 2}, {"name": "class2", "value": 3}], "nullable": true, "default": 1}, {"name": "cos3", "type": "enum", "enumerate": [{"name": "class0", "value": 1}, {"name": "class1", "value": 2}, {"name": "class2", "value": 3}], "nullable": true, "default": 1}, {"name": "cos4", "type": "enum", "enumerate": [{"name": "class0", "value": 1}, {"name": "class1", "value": 2}, {"name": "class2", "value": 3}], "nullable": true, "default": 1}, {"name": "cos5", "type": "enum", "enumerate": [{"name": "class0", "value": 1}, {"name": "class1", "value": 2}, {"name": "class2", "value": 3}], "nullable": true, "default": 1}, {"name": "cos6", "type": "enum", "enumerate": [{"name": "class0", "value": 1}, {"name": "class1", "value": 2}, {"name": "class2", "value": 3}], "nullable": true, "default": 1}, {"name": "cos7", "type": "enum", "enumerate": [{"name": "class0", "value": 1}, {"name": "class1", "value": 2}, {"name": "class2", "value": 3}], "nullable": true, "default": 1}]}]}, {"name": "bbf-qos-traffic-mngt-profiles:queue-policy-profiles", "type": "container", "fields": []}, {"name": "bbf-tpid:tpid", "type": "container", "fields": [{"name": "dot1ad-tpid", "type": "string", "nullable": true, "default": "0x88a8"}, {"name": "c-vlan-tpid", "type": "string", "nullable": true, "default": "0x8100"}, {"name": "s-vlan-tpid", "type": "string", "nullable": true, "default": "0x8100"}]}, {"name": "bbf-mac:mac", "type": "container", "fields": [{"name": "aging-time", "type": "string", "nullable": true, "default": "300"}, {"name": "aging-mode", "type": "enum", "enumerate": [{"name": "bidirectional", "value": 0}, {"name": "unidirectional", "value": 1}], "nullable": true, "default": "bidirectional"}]}, {"name": "bbf-mgmd:multicast", "type": "container", "fields": [{"name": "mgmd", "type": "container", "fields": [{"name": "multicast-protocol-parameter", "type": "container", "fields": [{"name": "query-interval", "type": "uint32", "nullable": true, "range": "1..31744", "default": 125}, {"name": "query-max-response-time-v2", "type": "uint32", "nullable": true, "range": "0..31744", "default": 100}, {"name": "query-max-response-time-v3", "type": "uint32", "nullable": true, "range": "0..31744", "default": 100}, {"name": "last-member-query-interval", "type": "uint32", "nullable": true, "range": "2..31744", "default": 10}, {"name": "last-member-query-count", "type": "uint32", "nullable": true, "range": "1..255", "default": 2}, {"name": "last-member-query-max-response-time-v2", "type": "uint32", "nullable": true, "range": "1..255", "default": 8}, {"name": "last-member-query-max-response-time-v3", "type": "uint32", "nullable": true, "range": "1..31744", "default": 8}, {"name": "robustness", "type": "uint32", "nullable": true, "range": "1..255", "default": 2}, {"name": "unsolicited-report-interval", "type": "uint32", "nullable": true, "range": "1..10", "default": 1}]}]}]}, {"name": "ietf-trust-anchors:trust-anchors", "type": "container", "fields": []}, {"name": "ietf-syslog:syslog", "type": "container", "fields": [{"name": "actions", "type": "container", "fields": [{"name": "console", "type": "container", "fields": [{"name": "pattern-match", "type": "string", "nullable": true}, {"name": "facility-filter", "type": "container", "fields": []}]}, {"name": "file", "type": "container", "fields": []}, {"name": "remote", "type": "container", "fields": []}]}]}, {"name": "bbf-ldra:dhcpv6-ldra-profiles", "type": "container", "fields": []}, {"name": "bbf-vlan-sub-interface-profiles:vsi-profiles", "type": "container", "fields": []}, {"name": "bbf-qos-filters:filters", "type": "container", "fields": []}, {"name": "bbf-link-table:link-table", "type": "container", "fields": []}, {"name": "bbf-qos-composite-filters:composite-filters", "type": "container", "fields": []}, {"name": "ieee802-dot1q-bridge:bridges", "type": "container", "fields": []}, {"name": "ieee802-dot1ab-lldp:lldp", "type": "container", "fields": [{"name": "bridge", "type": "string", "nullable": true, "clause": [{"type": "leafref", "formula": "/index-test0/ieee802-dot1q-bridge:bridges/dot1q:bridge.1/name"}]}, {"name": "message-fast-tx", "type": "uint32", "nullable": true, "range": "1..3600", "default": 1}, {"name": "message-tx-hold-multiplier", "type": "uint32", "nullable": true, "range": "2..10", "default": 4}, {"name": "message-tx-interval", "type": "uint32", "nullable": true, "range": "1..3600", "default": 30}, {"name": "reinit-delay", "type": "uint32", "nullable": true, "range": "1..10", "default": 2}, {"name": "tx-credit-max", "type": "uint32", "nullable": true, "range": "1..10", "default": 5}, {"name": "tx-fast-init", "type": "uint32", "nullable": true, "range": "1..8", "default": 4}, {"name": "notification-interval", "type": "uint32", "nullable": true, "range": "1..3600", "default": 30}]}, {"name": "bbf-pppoe-intermediate-agent:pppoe-profiles", "type": "container", "fields": []}, {"name": "bbf-frame-processing-profiles:frame-processing-profiles", "type": "container", "fields": []}, {"name": "bbf-l2-dhcpv4-relay:l2-dhcpv4-relay-profiles", "type": "container", "fields": []}, {"name": "openconfig-telemetry:telemetry-system", "type": "container", "fields": [{"name": "sensor-groups", "type": "container", "fields": []}, {"name": "destination-groups", "type": "container", "fields": []}, {"name": "subscriptions", "type": "container", "fields": [{"name": "persistent", "type": "container", "fields": []}, {"name": "dynamic", "type": "container", "fields": []}]}]}, {"name": "ietf-keystore:keystore", "type": "container", "fields": [{"name": "asymmetric-keys", "type": "container", "fields": []}]}], "keys": [{"name": "root.PK", "index": {"type": "primary"}, "node": "index-test0", "fields": ["ID"], "constraints": {"unique": true}}]}, {"name": "al:alarm-profile.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "alarm-type-id", "type": "identity", "enumerate": [{"name": "huawei-alarm-type-an:system", "value": 0, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system"}]}, {"name": "huawei-alarm-type-an:frame", "value": 1, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame"}]}, {"name": "huawei-alarm-type-an:slot", "value": 2, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot"}]}, {"name": "huawei-alarm-type-an:port", "value": 3, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port"}]}, {"name": "huawei-alarm-type-an:odn", "value": 4, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:odn"}]}, {"name": "huawei-alarm-type-an:xpon-distribute-fiber-los", "value": 5, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:odn/huawei-alarm-type-an:xpon-distribute-fiber-los"}]}, {"name": "huawei-alarm-type-an:ont", "value": 6, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont"}]}, {"name": "huawei-alarm-type-an:ont-port", "value": 7, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:ont-port"}]}, {"name": "huawei-alarm-type-an:xpon-ont-eth-port-transceiver-alarm", "value": 8, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:ont-port/huawei-alarm-type-an:xpon-ont-eth-port-transceiver-alarm"}]}, {"name": "huawei-alarm-type-an:gpon-ont-lan-port-carrier-signal-loss", "value": 9, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:ont-port/huawei-alarm-type-an:gpon-ont-lan-port-carrier-signal-loss"}]}, {"name": "huawei-alarm-type-an:xpon-ont-eth-uni-ring-fault", "value": 10, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:ont-port/huawei-alarm-type-an:xpon-ont-eth-uni-ring-fault"}]}, {"name": "huawei-alarm-type-an:xpon-ont-eth-port-broadcast-storm-occur", "value": 11, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:ont-port/huawei-alarm-type-an:xpon-ont-eth-port-broadcast-storm-occur"}]}, {"name": "huawei-alarm-type-an:gpon-ont-ces-uni-los", "value": 12, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:ont-port/huawei-alarm-type-an:gpon-ont-ces-uni-los"}]}, {"name": "huawei-alarm-type-an:gpon-ont-e1t1-status-fault", "value": 13, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:ont-port/huawei-alarm-type-an:gpon-ont-e1t1-status-fault"}]}, {"name": "huawei-alarm-type-an:gpon-ont-video-uni-no-signal", "value": 14, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:ont-port/huawei-alarm-type-an:gpon-ont-video-uni-no-signal"}]}, {"name": "huawei-alarm-type-an:ont-pots-port-signal-loss", "value": 15, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:ont-port/huawei-alarm-type-an:ont-pots-port-signal-loss"}]}, {"name": "huawei-alarm-type-an:gpon-bundle-ont-lan-port-carrier-signal-loss", "value": 16, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:ont-port/huawei-alarm-type-an:gpon-bundle-ont-lan-port-carrier-signal-loss"}]}, {"name": "huawei-alarm-type-an:xpon-bundle-ont-eth-port-broadcast-storm-occur", "value": 17, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:ont-port/huawei-alarm-type-an:xpon-bundle-ont-eth-port-broadcast-storm-occur"}]}, {"name": "huawei-alarm-type-an:gpon-bundle-eth-optical-module-fault", "value": 18, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:ont-port/huawei-alarm-type-an:gpon-bundle-eth-optical-module-fault"}]}, {"name": "huawei-alarm-type-an:gpon-ont-losi", "value": 19, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-losi"}]}, {"name": "huawei-alarm-type-an:epon-ont-losi", "value": 20, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:epon-ont-losi"}]}, {"name": "huawei-alarm-type-an:gpon-ont-lofi", "value": 21, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-lofi"}]}, {"name": "huawei-alarm-type-an:gpon-ont-sfi", "value": 22, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-sfi"}]}, {"name": "huawei-alarm-type-an:gpon-ont-loai", "value": 23, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-loai"}]}, {"name": "huawei-alarm-type-an:gpon-ont-loami", "value": 24, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-loami"}]}, {"name": "huawei-alarm-type-an:gpon-ont-lcdgi", "value": 25, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-lcdgi"}]}, {"name": "huawei-alarm-type-an:gpon-ont-peei", "value": 26, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-peei"}]}, {"name": "huawei-alarm-type-an:gpon-ont-sdi", "value": 27, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-sdi"}]}, {"name": "huawei-alarm-type-an:gpon-ont-dowi-count", "value": 28, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-dowi-count"}]}, {"name": "huawei-alarm-type-an:gpon-ont-dfi", "value": 29, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-dfi"}]}, {"name": "huawei-alarm-type-an:gpon-ont-looci-count", "value": 30, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-looci-count"}]}, {"name": "huawei-alarm-type-an:xpon-rogue-ont-alarm", "value": 31, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-rogue-ont-alarm"}]}, {"name": "huawei-alarm-type-an:epon-ont-line-quality-alarm", "value": 32, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:epon-ont-line-quality-alarm"}]}, {"name": "huawei-alarm-type-an:gpon-ont-temperature-fault", "value": 33, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-temperature-fault"}]}, {"name": "huawei-alarm-type-an:gpon-ont-voltage-fault", "value": 34, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-voltage-fault"}]}, {"name": "huawei-alarm-type-an:gpon-ont-downstream-sd-fault", "value": 35, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-downstream-sd-fault"}]}, {"name": "huawei-alarm-type-an:gpon-ont-downstream-sf-fault", "value": 36, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-downstream-sf-fault"}]}, {"name": "huawei-alarm-type-an:gpon-ont-power-overload", "value": 37, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-power-overload"}]}, {"name": "huawei-alarm-type-an:gpon-ont-mgmt-msg-dos-attack", "value": 38, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-mgmt-msg-dos-attack"}]}, {"name": "huawei-alarm-type-an:gpon-ont-broadcast-ploam-dos-attack", "value": 39, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-broadcast-ploam-dos-attack"}]}, {"name": "huawei-alarm-type-an:epon-ont-statistic-warning", "value": 40, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:epon-ont-statistic-warning"}]}, {"name": "huawei-alarm-type-an:epon-ont-statistic-alarm", "value": 41, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:epon-ont-statistic-alarm"}]}, {"name": "huawei-alarm-type-an:xpon-ont-wifi-sta-number-change", "value": 42, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-wifi-sta-number-change"}]}, {"name": "huawei-alarm-type-an:xpon-ont-wifi-channels-change", "value": 43, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-wifi-channels-change"}]}, {"name": "huawei-alarm-type-an:xpon-ont-wifi-sta-weaken", "value": 44, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-wifi-sta-weaken"}]}, {"name": "huawei-alarm-type-an:gpon-ont-cfgr-fail", "value": 45, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-cfgr-fail"}]}, {"name": "huawei-alarm-type-an:epon-ont-cfgr-fail", "value": 46, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:epon-ont-cfgr-fail"}]}, {"name": "huawei-alarm-type-an:xpon-ont-survival-mode", "value": 47, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-survival-mode"}]}, {"name": "huawei-alarm-type-an:xpon-ont-invalid-auth-info", "value": 48, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-invalid-auth-info"}]}, {"name": "huawei-alarm-type-an:gpon-ont-software-upgrade-success", "value": 49, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-software-upgrade-success"}]}, {"name": "huawei-alarm-type-an:gpon-ont-software-upgrade-fail", "value": 50, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-software-upgrade-fail"}]}, {"name": "huawei-alarm-type-an:epon-ont-software-upgrade-success", "value": 51, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:epon-ont-software-upgrade-success"}]}, {"name": "huawei-alarm-type-an:epon-ont-software-upgrade-fail", "value": 52, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:epon-ont-software-upgrade-fail"}]}, {"name": "huawei-alarm-type-an:gpon-ont-dying-gasp", "value": 53, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-dying-gasp"}]}, {"name": "huawei-alarm-type-an:epon-ont-dying-gasp", "value": 54, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:epon-ont-dying-gasp"}]}, {"name": "huawei-alarm-type-an:xpon-ont-fault", "value": 55, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-fault"}]}, {"name": "huawei-alarm-type-an:xpon-ont-minicube-communication-fault", "value": 56, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-minicube-communication-fault"}]}, {"name": "huawei-alarm-type-an:xpon-ont-minicube-power-fault", "value": 57, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-minicube-power-fault"}]}, {"name": "huawei-alarm-type-an:xpon-ont-minicube-battery-fault", "value": 58, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-minicube-battery-fault"}]}, {"name": "huawei-alarm-type-an:gpon-ont-lose-standby-battery", "value": 59, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-lose-standby-battery"}]}, {"name": "huawei-alarm-type-an:gpon-ont-standby-battery-low-voltage", "value": 60, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-standby-battery-low-voltage"}]}, {"name": "huawei-alarm-type-an:gpon-ont-standby-battery-cannot-be-charged", "value": 61, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-standby-battery-cannot-be-charged"}]}, {"name": "huawei-alarm-type-an:gpon-ont-phy-intrusion", "value": 62, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-phy-intrusion"}]}, {"name": "huawei-alarm-type-an:epon-ont-power-voltage-abnormal", "value": 63, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:epon-ont-power-voltage-abnormal"}]}, {"name": "huawei-alarm-type-an:gpon-ont-reset-by-watchdog", "value": 64, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-reset-by-watchdog"}]}, {"name": "huawei-alarm-type-an:gpon-ont-fec-correctable-code", "value": 65, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-fec-correctable-code"}]}, {"name": "huawei-alarm-type-an:gpon-ont-fec-uncorrectable-code", "value": 66, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-fec-uncorrectable-code"}]}, {"name": "huawei-alarm-type-an:gpon-ont-rdii", "value": 67, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-rdii"}]}, {"name": "huawei-alarm-type-an:xpon-ont-initiative-offline", "value": 68, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-initiative-offline"}]}, {"name": "huawei-alarm-type-an:xpon-ont-abnormal-bw", "value": 69, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-abnormal-bw"}]}, {"name": "huawei-alarm-type-an:xpon-ont-clock-lock-fail", "value": 70, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-clock-lock-fail"}]}, {"name": "huawei-alarm-type-an:xpon-ont-ptp-workmode-fail", "value": 71, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-ptp-workmode-fail"}]}, {"name": "huawei-alarm-type-an:xpon-ont-mass-voip-fail", "value": 72, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-mass-voip-fail"}]}, {"name": "huawei-alarm-type-an:xpon-onts-power-off-in-district", "value": 73, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-onts-power-off-in-district"}]}, {"name": "huawei-alarm-type-an:xpon-ont-sipua-register-timeout", "value": 74, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-sipua-register-timeout"}]}, {"name": "huawei-alarm-type-an:xpon-ont-sipua-register-fail", "value": 75, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-sipua-register-fail"}]}, {"name": "huawei-alarm-type-an:xpon-ont-sipua-register-auth-fail", "value": 76, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-sipua-register-auth-fail"}]}, {"name": "huawei-alarm-type-an:xpon-ont-mg-interface-fault", "value": 77, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-mg-interface-fault"}]}, {"name": "huawei-alarm-type-an:xpon-ont-mass-sipua-register-timeout", "value": 78, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-mass-sipua-register-timeout"}]}, {"name": "huawei-alarm-type-an:xpon-ont-mass-sipua-register-auth-fail", "value": 79, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-mass-sipua-register-auth-fail"}]}, {"name": "huawei-alarm-type-an:xpon-ont-mass-sipua-register-fail", "value": 80, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-mass-sipua-register-fail"}]}, {"name": "huawei-alarm-type-an:xpon-ont-mass-mg-interface-fault", "value": 81, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-mass-mg-interface-fault"}]}, {"name": "huawei-alarm-type-an:gpon-ont-omci-check-enhanced-switch-change", "value": 82, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-omci-check-enhanced-switch-change"}]}, {"name": "huawei-alarm-type-an:gpon-ont-lose-external-power", "value": 83, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-lose-external-power"}]}, {"name": "huawei-alarm-type-an:gpon-ont-auto-discovery", "value": 84, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-auto-discovery"}]}, {"name": "huawei-alarm-type-an:gpon-ont-auto-discovery-delete", "value": 85, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-auto-discovery-delete"}]}, {"name": "huawei-alarm-type-an:epon-optical-transceiver-state-fault", "value": 86, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:epon-optical-transceiver-state-fault"}]}, {"name": "huawei-alarm-type-an:epon-ont-auto-discovery", "value": 87, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:epon-ont-auto-discovery"}]}, {"name": "huawei-alarm-type-an:epon-ont-auto-discovery-delete", "value": 88, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:epon-ont-auto-discovery-delete"}]}, {"name": "huawei-alarm-type-an:gpon-ont-omci-check-enhanced-patch-active", "value": 89, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-omci-check-enhanced-patch-active"}]}, {"name": "huawei-alarm-type-an:gpon-ont-cpu-temperature-alarm", "value": 90, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-cpu-temperature-alarm"}]}, {"name": "huawei-alarm-type-an:gpon-ont-memory-occupation-alarm", "value": 91, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-memory-occupation-alarm"}]}, {"name": "huawei-alarm-type-an:gpon-ont-cpu-occupation-alarm", "value": 92, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-cpu-occupation-alarm"}]}, {"name": "huawei-alarm-type-an:xpon-ont-pos-move", "value": 93, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:xpon-ont-pos-move"}]}, {"name": "huawei-alarm-type-an:ont-fan-fault", "value": 94, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:ont-fan-fault"}]}, {"name": "huawei-alarm-type-an:epon-ont-broadcast-mpcp-dos-attack", "value": 95, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:epon-ont-broadcast-mpcp-dos-attack"}]}, {"name": "huawei-alarm-type-an:ont-dc-power-fault", "value": 96, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:ont-dc-power-fault"}]}, {"name": "huawei-alarm-type-an:gpon-ont-not-in-whitelist", "value": 97, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-not-in-whitelist"}]}, {"name": "huawei-alarm-type-an:ont-single-frame-multiburst-capability-insufficient", "value": 98, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:ont-single-frame-multiburst-capability-insufficient"}]}, {"name": "huawei-alarm-type-an:gpon-ont-ttl-detect-ip-packets-decrease", "value": 99, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:ont/huawei-alarm-type-an:gpon-ont-ttl-detect-ip-packets-decrease"}]}, {"name": "huawei-alarm-type-an:non-unicast-packets-exceed-threshold", "value": 100, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:non-unicast-packets-exceed-threshold"}]}, {"name": "huawei-alarm-type-an:pppoe-echo-packets-monitor", "value": 101, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:pppoe-echo-packets-monitor"}]}, {"name": "huawei-alarm-type-an:data-packet-ipv6-spoofing", "value": 102, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:data-packet-ipv6-spoofing"}]}, {"name": "huawei-alarm-type-an:data-packet-ip-spoofing", "value": 103, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:data-packet-ip-spoofing"}]}, {"name": "huawei-alarm-type-an:data-packet-mac-spoofing", "value": 104, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:data-packet-mac-spoofing"}]}, {"name": "huawei-alarm-type-an:illegal-arp-packet", "value": 105, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:illegal-arp-packet"}]}, {"name": "huawei-alarm-type-an:xpon-transceiver-absent", "value": 106, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-transceiver-absent"}]}, {"name": "huawei-alarm-type-an:xpon-olt-tf", "value": 107, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-olt-tf"}]}, {"name": "huawei-alarm-type-an:gpon-olt-tf", "value": 108, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:gpon-olt-tf"}]}, {"name": "huawei-alarm-type-an:epon-olt-tf", "value": 109, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:epon-olt-tf"}]}, {"name": "huawei-alarm-type-an:xpon-local-transceiver-warning", "value": 110, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-local-transceiver-warning"}]}, {"name": "huawei-alarm-type-an:xpon-local-transceiver-alarm", "value": 111, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-local-transceiver-alarm"}]}, {"name": "huawei-alarm-type-an:xpon-remote-transceiver-warning", "value": 112, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-remote-transceiver-warning"}]}, {"name": "huawei-alarm-type-an:xpon-remote-transceiver-alarm", "value": 113, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-remote-transceiver-alarm"}]}, {"name": "huawei-alarm-type-an:epon-olt-transceiver-mismatch-ont-type", "value": 114, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:epon-olt-transceiver-mismatch-ont-type"}]}, {"name": "huawei-alarm-type-an:xpon-olt-los", "value": 115, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-olt-los"}]}, {"name": "huawei-alarm-type-an:xpon-port-service-fault", "value": 116, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-port-service-fault"}]}, {"name": "huawei-alarm-type-an:xpon-typeb-sd-fault", "value": 117, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-typeb-sd-fault"}]}, {"name": "huawei-alarm-type-an:xpon-typeb-standby-rssi-low", "value": 118, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-typeb-standby-rssi-low"}]}, {"name": "huawei-alarm-type-an:epon-port-statistic-warning", "value": 119, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:epon-port-statistic-warning"}]}, {"name": "huawei-alarm-type-an:epon-port-statistic-alarm", "value": 120, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:epon-port-statistic-alarm"}]}, {"name": "huawei-alarm-type-an:gpon-port-upstream-traffic-warning", "value": 121, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:gpon-port-upstream-traffic-warning"}]}, {"name": "huawei-alarm-type-an:gpon-port-upstream-traffic-alarm", "value": 122, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:gpon-port-upstream-traffic-alarm"}]}, {"name": "huawei-alarm-type-an:gpon-port-downstream-traffic-warning", "value": 123, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:gpon-port-downstream-traffic-warning"}]}, {"name": "huawei-alarm-type-an:gpon-port-downstream-traffic-alarm", "value": 124, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:gpon-port-downstream-traffic-alarm"}]}, {"name": "huawei-alarm-type-an:gpon-port-channel-upstream-traffic-warning", "value": 125, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:gpon-port-channel-upstream-traffic-warning"}]}, {"name": "huawei-alarm-type-an:gpon-port-channel-upstream-traffic-alarm", "value": 126, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:gpon-port-channel-upstream-traffic-alarm"}]}, {"name": "huawei-alarm-type-an:gpon-port-channel-downstream-traffic-warning", "value": 127, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:gpon-port-channel-downstream-traffic-warning"}]}, {"name": "huawei-alarm-type-an:gpon-port-channel-downstream-traffic-alarm", "value": 128, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:gpon-port-channel-downstream-traffic-alarm"}]}, {"name": "huawei-alarm-type-an:epon-port-has-illegal-occupy-llid-ont", "value": 129, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:epon-port-has-illegal-occupy-llid-ont"}]}, {"name": "huawei-alarm-type-an:xpon-port-has-illegal-rogue-ont", "value": 130, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-port-has-illegal-rogue-ont"}]}, {"name": "huawei-alarm-type-an:xpon-port-idle-rssi", "value": 131, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-port-idle-rssi"}]}, {"name": "huawei-alarm-type-an:epon-too-many-ont-register", "value": 132, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:epon-too-many-ont-register"}]}, {"name": "huawei-alarm-type-an:xpon-ont-offline-monitor-alarm", "value": 133, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-ont-offline-monitor-alarm"}]}, {"name": "huawei-alarm-type-an:xpon-olt-bw-no-enough", "value": 134, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-olt-bw-no-enough"}]}, {"name": "huawei-alarm-type-an:gpon-port-ranging-failure", "value": 135, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:gpon-port-ranging-failure"}]}, {"name": "huawei-alarm-type-an:xpon-optical-transceiver-over-temperature-closed-fault", "value": 136, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-optical-transceiver-over-temperature-closed-fault"}]}, {"name": "huawei-alarm-type-an:pon-optical-transceiver-over-temperature-closed-fault", "value": 137, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:pon-optical-transceiver-over-temperature-closed-fault"}]}, {"name": "huawei-alarm-type-an:xpon-optical-module-enhanced-active-fault", "value": 138, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-optical-module-enhanced-active-fault"}]}, {"name": "huawei-alarm-type-an:xpon-olt-rtu-not-enough", "value": 139, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-olt-rtu-not-enough"}]}, {"name": "huawei-alarm-type-an:xpon-port-iic-fault", "value": 140, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-port-iic-fault"}]}, {"name": "huawei-alarm-type-an:xpon-optical-transceiver-over-temperature-risk", "value": 141, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-optical-transceiver-over-temperature-risk"}]}, {"name": "huawei-alarm-type-an:clock-1588-asymmetry-measure-failed", "value": 142, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:clock-1588-asymmetry-measure-failed"}]}, {"name": "huawei-alarm-type-an:xpon-olt-rtu-type-not-enough", "value": 143, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:port/huawei-alarm-type-an:xpon-olt-rtu-type-not-enough"}]}, {"name": "huawei-alarm-type-an:subboard", "value": 144, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:subboard"}]}, {"name": "huawei-alarm-type-an:inconsistent-in-subboards-active-to-standby", "value": 145, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:subboard/huawei-alarm-type-an:inconsistent-in-subboards-active-to-standby"}]}, {"name": "huawei-alarm-type-an:inconsistent-in-powerboards", "value": 146, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:subboard/huawei-alarm-type-an:inconsistent-in-powerboards"}]}, {"name": "huawei-alarm-type-an:protocol-packet-flooding-discard", "value": 147, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:protocol-packet-flooding-discard"}]}, {"name": "huawei-alarm-type-an:access-user-fault-monitor", "value": 148, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:access-user-fault-monitor"}]}, {"name": "huawei-alarm-type-an:autoload-complete", "value": 149, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:autoload-complete"}]}, {"name": "huawei-alarm-type-an:autoload-failed", "value": 150, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:autoload-failed"}]}, {"name": "huawei-alarm-type-an:autoload-starts", "value": 151, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:autoload-starts"}]}, {"name": "huawei-alarm-type-an:board-software-automatic-expansion-failure", "value": 152, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:board-software-automatic-expansion-failure"}]}, {"name": "huawei-alarm-type-an:board-fpga-occurs-software-fault", "value": 153, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:board-fpga-occurs-software-fault"}]}, {"name": "huawei-alarm-type-an:ambient-temperature-too-high", "value": 154, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:ambient-temperature-too-high"}]}, {"name": "huawei-alarm-type-an:ambient-temperature-too-low", "value": 155, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:ambient-temperature-too-low"}]}, {"name": "huawei-alarm-type-an:local-shelf-input-power-fails-other-used", "value": 156, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:local-shelf-input-power-fails-other-used"}]}, {"name": "huawei-alarm-type-an:board-replaced", "value": 157, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:board-replaced"}]}, {"name": "huawei-alarm-type-an:local-shelf-input-power-undervoltage", "value": 158, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:local-shelf-input-power-undervoltage"}]}, {"name": "huawei-alarm-type-an:system-license-capacity-exceed", "value": 159, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:system-license-capacity-exceed"}]}, {"name": "huawei-alarm-type-an:control-board-in-over-temperature-protection-mode", "value": 160, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:control-board-in-over-temperature-protection-mode"}]}, {"name": "huawei-alarm-type-an:inconsistent-in-type-active-to-standby", "value": 161, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:inconsistent-in-type-active-to-standby"}]}, {"name": "huawei-alarm-type-an:local-shelf-input-power-fails", "value": 162, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:local-shelf-input-power-fails"}]}, {"name": "huawei-alarm-type-an:extended-bios-password-unset", "value": 163, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:extended-bios-password-unset"}]}, {"name": "huawei-alarm-type-an:important-file-damaged", "value": 164, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:important-file-damaged"}]}, {"name": "huawei-alarm-type-an:file-system-damaged", "value": 165, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:file-system-damaged"}]}, {"name": "huawei-alarm-type-an:board-authentication-failed", "value": 166, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:board-authentication-failed"}]}, {"name": "huawei-alarm-type-an:board-conmmunication-failed", "value": 167, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:board-conmmunication-failed"}]}, {"name": "huawei-alarm-type-an:board-type-mismatch", "value": 168, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:board-type-mismatch"}]}, {"name": "huawei-alarm-type-an:replace-board-failed", "value": 169, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:replace-board-failed"}]}, {"name": "huawei-alarm-type-an:board-hardware-abnormal", "value": 170, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:board-hardware-abnormal"}]}, {"name": "huawei-alarm-type-an:board-hardware-critical-abnormal", "value": 171, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:board-hardware-critical-abnormal"}]}, {"name": "huawei-alarm-type-an:board-hardware-minor-abnormal", "value": 172, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:board-hardware-minor-abnormal"}]}, {"name": "huawei-alarm-type-an:subboard-replaced", "value": 173, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:subboard-replaced"}]}, {"name": "huawei-alarm-type-an:unknown-subboard-type", "value": 174, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:unknown-subboard-type"}]}, {"name": "huawei-alarm-type-an:actual-subboard-inconsistent-with-configured", "value": 175, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:actual-subboard-inconsistent-with-configured"}]}, {"name": "huawei-alarm-type-an:clock-8k-subcard-work-mode-switch", "value": 176, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:clock-8k-subcard-work-mode-switch"}]}, {"name": "huawei-alarm-type-an:clock-8k-source-switch", "value": 177, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:clock-8k-source-switch"}]}, {"name": "huawei-alarm-type-an:clock-1588-source-switch", "value": 178, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:clock-1588-source-switch"}]}, {"name": "huawei-alarm-type-an:clock-8k-quality-change", "value": 179, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:clock-8k-quality-change"}]}, {"name": "huawei-alarm-type-an:clock-8k-source-lost", "value": 180, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:clock-8k-source-lost"}]}, {"name": "huawei-alarm-type-an:clock-1588-source-lost", "value": 181, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:clock-1588-source-lost"}]}, {"name": "huawei-alarm-type-an:clock-8k-esmc-loss", "value": 182, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:clock-8k-esmc-loss"}]}, {"name": "huawei-alarm-type-an:clock-8k-quality-failed", "value": 183, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:clock-8k-quality-failed"}]}, {"name": "huawei-alarm-type-an:ec-traffic-overload", "value": 184, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:ec-traffic-overload"}]}, {"name": "huawei-alarm-type-an:board-add-replace-success", "value": 185, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:board-add-replace-success"}]}, {"name": "huawei-alarm-type-an:board-add-replace-fail", "value": 186, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:board-add-replace-fail"}]}, {"name": "huawei-alarm-type-an:file-integrity-check-failed", "value": 187, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:file-integrity-check-failed"}]}, {"name": "huawei-alarm-type-an:log-verify-integrity", "value": 188, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:log-verify-integrity"}]}, {"name": "huawei-alarm-type-an:board-software-incompatible", "value": 189, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:board-software-incompatible"}]}, {"name": "huawei-alarm-type-an:tact-switch-open", "value": 190, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:slot/huawei-alarm-type-an:tact-switch-open"}]}, {"name": "huawei-alarm-type-an:xpon-ont-cannot-be-auto-added", "value": 191, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:xpon-ont-cannot-be-auto-added"}]}, {"name": "huawei-alarm-type-an:xpon-iof-file-not-exist", "value": 192, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:xpon-iof-file-not-exist"}]}, {"name": "huawei-alarm-type-an:xpon-iof-file-mismatch", "value": 193, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:xpon-iof-file-mismatch"}]}, {"name": "huawei-alarm-type-an:xpon-twdm-channal-group-wavelen-conflict", "value": 194, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:xpon-twdm-channal-group-wavelen-conflict"}]}, {"name": "huawei-alarm-type-an:xpon-twdm-channal-group-los", "value": 195, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:xpon-twdm-channal-group-los"}]}, {"name": "huawei-alarm-type-an:xpon-psgpononu-member-state-inconsistent", "value": 196, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:xpon-psgpononu-member-state-inconsistent"}]}, {"name": "huawei-alarm-type-an:xpon-psdp-peer-node-key-security", "value": 197, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:xpon-psdp-peer-node-key-security"}]}, {"name": "huawei-alarm-type-an:xpon-psdp-local-node-key-security", "value": 198, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:xpon-psdp-local-node-key-security"}]}, {"name": "huawei-alarm-type-an:xpon-remote-protect-group-aps-fail", "value": 199, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:xpon-remote-protect-group-aps-fail"}]}, {"name": "huawei-alarm-type-an:gpon-service-board-chip-fault", "value": 200, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:gpon-service-board-chip-fault"}]}, {"name": "huawei-alarm-type-an:extend-frame-inconsistent-with-configured", "value": 201, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:extend-frame-inconsistent-with-configured"}]}, {"name": "huawei-alarm-type-an:extend-frame-failed", "value": 202, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:extend-frame-failed"}]}, {"name": "huawei-alarm-type-an:stack-extend-frame-failed", "value": 203, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:stack-extend-frame-failed"}]}, {"name": "huawei-alarm-type-an:force-auth-not-enable", "value": 204, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:force-auth-not-enable"}]}, {"name": "huawei-alarm-type-an:identity-certify-fault", "value": 205, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:identity-certify-fault"}]}, {"name": "huawei-alarm-type-an:version-mismatch-extended-frame-online-offline", "value": 206, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:frame/huawei-alarm-type-an:version-mismatch-extended-frame-online-offline"}]}, {"name": "huawei-alarm-type-an:emu", "value": 207, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:emu"}]}, {"name": "huawei-alarm-type-an:fan", "value": 208, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:emu/huawei-alarm-type-an:fan"}]}, {"name": "huawei-alarm-type-an:fan-blocked", "value": 209, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:emu/huawei-alarm-type-an:fan/huawei-alarm-type-an:fan-blocked"}]}, {"name": "huawei-alarm-type-an:two-or-more-fans-blocked", "value": 210, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:emu/huawei-alarm-type-an:fan/huawei-alarm-type-an:two-or-more-fans-blocked"}]}, {"name": "huawei-alarm-type-an:fan-rotation-speed-deteriorates", "value": 211, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:emu/huawei-alarm-type-an:fan/huawei-alarm-type-an:fan-rotation-speed-deteriorates"}]}, {"name": "huawei-alarm-type-an:fan-status-abnormal", "value": 212, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:emu/huawei-alarm-type-an:fan/huawei-alarm-type-an:fan-status-abnormal"}]}, {"name": "huawei-alarm-type-an:fan-temperature-abnormal", "value": 213, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:emu/huawei-alarm-type-an:fan/huawei-alarm-type-an:fan-temperature-abnormal"}]}, {"name": "huawei-alarm-type-an:emu-communication-abnomal", "value": 214, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:emu/huawei-alarm-type-an:emu-communication-abnomal"}]}, {"name": "huawei-alarm-type-an:emu-hardware-exception", "value": 215, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:emu/huawei-alarm-type-an:emu-hardware-exception"}]}, {"name": "huawei-alarm-type-an:emu-autofind-fail", "value": 216, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:emu/huawei-alarm-type-an:emu-autofind-fail"}]}, {"name": "huawei-alarm-type-an:l2", "value": 217, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2"}]}, {"name": "huawei-alarm-type-an:qos", "value": 218, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2/huawei-alarm-type-an:qos"}]}, {"name": "huawei-alarm-type-an:down-traffic-decrease-exceed-threshold", "value": 219, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2/huawei-alarm-type-an:qos/huawei-alarm-type-an:down-traffic-decrease-exceed-threshold"}]}, {"name": "huawei-alarm-type-an:up-down-traffic-decrease-exceed-threshold", "value": 220, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2/huawei-alarm-type-an:qos/huawei-alarm-type-an:up-down-traffic-decrease-exceed-threshold"}]}, {"name": "huawei-alarm-type-an:up-traffic-decrease-exceed-threshold", "value": 221, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2/huawei-alarm-type-an:qos/huawei-alarm-type-an:up-traffic-decrease-exceed-threshold"}]}, {"name": "huawei-alarm-type-an:mac", "value": 222, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2/huawei-alarm-type-an:mac"}]}, {"name": "huawei-alarm-type-an:flapping-mac-exceed-threshold", "value": 223, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2/huawei-alarm-type-an:mac/huawei-alarm-type-an:flapping-mac-exceed-threshold"}]}, {"name": "huawei-alarm-type-an:mac-exceed-threshold", "value": 224, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2/huawei-alarm-type-an:mac/huawei-alarm-type-an:mac-exceed-threshold"}]}, {"name": "huawei-alarm-type-an:mac-number-decrease", "value": 225, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2/huawei-alarm-type-an:mac/huawei-alarm-type-an:mac-number-decrease"}]}, {"name": "huawei-alarm-type-an:mac-conflict", "value": 226, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2/huawei-alarm-type-an:mac/huawei-alarm-type-an:mac-conflict"}]}, {"name": "huawei-alarm-type-an:service-port", "value": 227, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2/huawei-alarm-type-an:service-port"}]}, {"name": "huawei-alarm-type-an:auto-service-port-create-fail", "value": 228, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2/huawei-alarm-type-an:service-port/huawei-alarm-type-an:auto-service-port-create-fail"}]}, {"name": "huawei-alarm-type-an:auto-service-port-parameter-incorrect", "value": 229, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2/huawei-alarm-type-an:service-port/huawei-alarm-type-an:auto-service-port-parameter-incorrect"}]}, {"name": "huawei-alarm-type-an:l2p", "value": 230, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p"}]}, {"name": "huawei-alarm-type-an:ancp", "value": 231, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ancp"}]}, {"name": "huawei-alarm-type-an:ancp-session-terminated", "value": 232, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ancp/huawei-alarm-type-an:ancp-session-terminated"}]}, {"name": "huawei-alarm-type-an:ethoam", "value": 233, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ethoam"}]}, {"name": "huawei-alarm-type-an:ethoam-all-cc-new-defect", "value": 234, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ethoam/huawei-alarm-type-an:ethoam-all-cc-new-defect"}]}, {"name": "huawei-alarm-type-an:ethoam-efm-event-two", "value": 235, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ethoam/huawei-alarm-type-an:ethoam-efm-event-two"}]}, {"name": "huawei-alarm-type-an:ethoam-errorccm-defect", "value": 236, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ethoam/huawei-alarm-type-an:ethoam-errorccm-defect"}]}, {"name": "huawei-alarm-type-an:ethoam-macstatus-defect", "value": 237, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ethoam/huawei-alarm-type-an:ethoam-macstatus-defect"}]}, {"name": "huawei-alarm-type-an:ethoam-rdi-defect", "value": 238, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ethoam/huawei-alarm-type-an:ethoam-rdi-defect"}]}, {"name": "huawei-alarm-type-an:ethoam-rmep-defect", "value": 239, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ethoam/huawei-alarm-type-an:ethoam-rmep-defect"}]}, {"name": "huawei-alarm-type-an:ethoam-system-ccm-defect", "value": 240, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ethoam/huawei-alarm-type-an:ethoam-system-ccm-defect"}]}, {"name": "huawei-alarm-type-an:ethoam-trigger-ifdown", "value": 241, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ethoam/huawei-alarm-type-an:ethoam-trigger-ifdown"}]}, {"name": "huawei-alarm-type-an:ethoam-xconccm-defect", "value": 242, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ethoam/huawei-alarm-type-an:ethoam-xconccm-defect"}]}, {"name": "huawei-alarm-type-an:lacp", "value": 243, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:lacp"}]}, {"name": "huawei-alarm-type-an:lacp-fiber-connect-err", "value": 244, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:lacp/huawei-alarm-type-an:lacp-fiber-connect-err"}]}, {"name": "huawei-alarm-type-an:lacp-group-macchange", "value": 245, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:lacp/huawei-alarm-type-an:lacp-group-macchange"}]}, {"name": "huawei-alarm-type-an:lacp-port-block", "value": 246, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:lacp/huawei-alarm-type-an:lacp-port-block"}]}, {"name": "huawei-alarm-type-an:lacp-port-forwarding", "value": 247, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:lacp/huawei-alarm-type-an:lacp-port-forwarding"}]}, {"name": "huawei-alarm-type-an:lacp-port-linkfault", "value": 248, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:lacp/huawei-alarm-type-an:lacp-port-linkfault"}]}, {"name": "huawei-alarm-type-an:lacp-port-linkrecover", "value": 249, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:lacp/huawei-alarm-type-an:lacp-port-linkrecover"}]}, {"name": "huawei-alarm-type-an:ringchk", "value": 250, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ringchk"}]}, {"name": "huawei-alarm-type-an:ringchk-atm-port-defect", "value": 251, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ringchk/huawei-alarm-type-an:ringchk-atm-port-defect"}]}, {"name": "huawei-alarm-type-an:ringchk-atm-port-ring-new", "value": 252, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ringchk/huawei-alarm-type-an:ringchk-atm-port-ring-new"}]}, {"name": "huawei-alarm-type-an:ringchk-xpon-port-defect-new", "value": 253, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ringchk/huawei-alarm-type-an:ringchk-xpon-port-defect-new"}]}, {"name": "huawei-alarm-type-an:ringchk-xpon-port-ring-new", "value": 254, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ringchk/huawei-alarm-type-an:ringchk-xpon-port-ring-new"}]}, {"name": "huawei-alarm-type-an:ont-ethoam", "value": 255, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ont-ethoam"}]}, {"name": "huawei-alarm-type-an:ont-ethoam-all-cc-new-defect", "value": 256, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ont-ethoam/huawei-alarm-type-an:ont-ethoam-all-cc-new-defect"}]}, {"name": "huawei-alarm-type-an:ont-ethoam-errorccm-defect", "value": 257, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ont-ethoam/huawei-alarm-type-an:ont-ethoam-errorccm-defect"}]}, {"name": "huawei-alarm-type-an:ont-ethoam-macstatus-defect", "value": 258, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ont-ethoam/huawei-alarm-type-an:ont-ethoam-macstatus-defect"}]}, {"name": "huawei-alarm-type-an:ont-ethoam-rdi-defect", "value": 259, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ont-ethoam/huawei-alarm-type-an:ont-ethoam-rdi-defect"}]}, {"name": "huawei-alarm-type-an:ont-ethoam-rmep-defect", "value": 260, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ont-ethoam/huawei-alarm-type-an:ont-ethoam-rmep-defect"}]}, {"name": "huawei-alarm-type-an:ont-ethoam-system-ccm-defect", "value": 261, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ont-ethoam/huawei-alarm-type-an:ont-ethoam-system-ccm-defect"}]}, {"name": "huawei-alarm-type-an:ont-ethoam-trigger-ifdown", "value": 262, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ont-ethoam/huawei-alarm-type-an:ont-ethoam-trigger-ifdown"}]}, {"name": "huawei-alarm-type-an:ont-ethoam-xconccm-defect", "value": 263, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l2p/huawei-alarm-type-an:ont-ethoam/huawei-alarm-type-an:ont-ethoam-xconccm-defect"}]}, {"name": "huawei-alarm-type-an:ip", "value": 264, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:ip"}]}, {"name": "huawei-alarm-type-an:ip-not-bind-to-user", "value": 265, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:ip/huawei-alarm-type-an:ip-not-bind-to-user"}]}, {"name": "huawei-alarm-type-an:ipv6-conflict", "value": 266, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:ip/huawei-alarm-type-an:ipv6-conflict"}]}, {"name": "huawei-alarm-type-an:ip-conflict", "value": 267, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:ip/huawei-alarm-type-an:ip-conflict"}]}, {"name": "huawei-alarm-type-an:ipv6-not-bind-to-user-port", "value": 268, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:ip/huawei-alarm-type-an:ipv6-not-bind-to-user-port"}]}, {"name": "huawei-alarm-type-an:acd-ip-conflict", "value": 269, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:ip/huawei-alarm-type-an:acd-ip-conflict"}]}, {"name": "huawei-alarm-type-an:dad-ipv6-conflict", "value": 270, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:ip/huawei-alarm-type-an:dad-ipv6-conflict"}]}, {"name": "huawei-alarm-type-an:security", "value": 271, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:security"}]}, {"name": "huawei-alarm-type-an:packet-attack", "value": 272, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:security/huawei-alarm-type-an:packet-attack"}]}, {"name": "huawei-alarm-type-an:dos-attack-appear", "value": 273, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:security/huawei-alarm-type-an:dos-attack-appear"}]}, {"name": "huawei-alarm-type-an:dos-attack-disappear", "value": 274, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:security/huawei-alarm-type-an:dos-attack-disappear"}]}, {"name": "huawei-alarm-type-an:btv", "value": 275, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:btv"}]}, {"name": "huawei-alarm-type-an:btv-multicast-traffic-monitor-defect", "value": 276, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:btv/huawei-alarm-type-an:btv-multicast-traffic-monitor-defect"}]}, {"name": "huawei-alarm-type-an:btv-upport-monitor-deffect", "value": 277, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:btv/huawei-alarm-type-an:btv-upport-monitor-deffect"}]}, {"name": "huawei-alarm-type-an:l3p", "value": 278, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p"}]}, {"name": "huawei-alarm-type-an:ifm", "value": 279, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:ifm"}]}, {"name": "huawei-alarm-type-an:vlanif", "value": 280, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:ifm/huawei-alarm-type-an:vlanif"}]}, {"name": "huawei-alarm-type-an:dhcp4c-ip-conflict", "value": 281, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:ifm/huawei-alarm-type-an:vlanif/huawei-alarm-type-an:dhcp4c-ip-conflict"}]}, {"name": "huawei-alarm-type-an:interface-mac-conflict", "value": 282, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:ifm/huawei-alarm-type-an:interface-mac-conflict"}]}, {"name": "huawei-alarm-type-an:l2vpn", "value": 283, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:l2vpn"}]}, {"name": "huawei-alarm-type-an:tunnel", "value": 284, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:l2vpn/huawei-alarm-type-an:tunnel"}]}, {"name": "huawei-alarm-type-an:event-ip-tunnel", "value": 285, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:l2vpn/huawei-alarm-type-an:tunnel/huawei-alarm-type-an:event-ip-tunnel"}]}, {"name": "huawei-alarm-type-an:event-ipv6-tunnel", "value": 286, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:l2vpn/huawei-alarm-type-an:tunnel/huawei-alarm-type-an:event-ipv6-tunnel"}]}, {"name": "huawei-alarm-type-an:vsi", "value": 287, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:l2vpn/huawei-alarm-type-an:vsi"}]}, {"name": "huawei-alarm-type-an:mpls-vsi-down", "value": 288, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:l2vpn/huawei-alarm-type-an:vsi/huawei-alarm-type-an:mpls-vsi-down"}]}, {"name": "huawei-alarm-type-an:mpls-vsi-up", "value": 289, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:l2vpn/huawei-alarm-type-an:vsi/huawei-alarm-type-an:mpls-vsi-up"}]}, {"name": "huawei-alarm-type-an:mpls-bgpad-pw-down", "value": 290, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:l2vpn/huawei-alarm-type-an:vsi/huawei-alarm-type-an:mpls-bgpad-pw-down"}]}, {"name": "huawei-alarm-type-an:mpls-bgpad-pw-up", "value": 291, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:l2vpn/huawei-alarm-type-an:vsi/huawei-alarm-type-an:mpls-bgpad-pw-up"}]}, {"name": "huawei-alarm-type-an:pw", "value": 292, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:l2vpn/huawei-alarm-type-an:pw"}]}, {"name": "huawei-alarm-type-an:mpls-pw-down", "value": 293, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:l2vpn/huawei-alarm-type-an:pw/huawei-alarm-type-an:mpls-pw-down"}]}, {"name": "huawei-alarm-type-an:mpls-pw-up", "value": 294, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:l2vpn/huawei-alarm-type-an:pw/huawei-alarm-type-an:mpls-pw-up"}]}, {"name": "huawei-alarm-type-an:l3vpn", "value": 295, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:l3vpn"}]}, {"name": "huawei-alarm-type-an:event-vni-conflict", "value": 296, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:l3vpn/huawei-alarm-type-an:event-vni-conflict"}]}, {"name": "huawei-alarm-type-an:dfx", "value": 297, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:dfx"}]}, {"name": "huawei-alarm-type-an:l3-arp-transfer-new", "value": 298, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:dfx/huawei-alarm-type-an:l3-arp-transfer-new"}]}, {"name": "huawei-alarm-type-an:vpls-vsi-mac-full", "value": 299, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:dfx/huawei-alarm-type-an:vpls-vsi-mac-full"}]}, {"name": "huawei-alarm-type-an:vpls-vsi-mac-full-recovery", "value": 300, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:dfx/huawei-alarm-type-an:vpls-vsi-mac-full-recovery"}]}, {"name": "huawei-alarm-type-an:ip-route-hd-exceed-threshold", "value": 301, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:dfx/huawei-alarm-type-an:ip-route-hd-exceed-threshold"}]}, {"name": "huawei-alarm-type-an:ip-tunnel-hd-exceed-threshold", "value": 302, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:dfx/huawei-alarm-type-an:ip-tunnel-hd-exceed-threshold"}]}, {"name": "huawei-alarm-type-an:vpn-hardware-exceed-threshold", "value": 303, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:dfx/huawei-alarm-type-an:vpn-hardware-exceed-threshold"}]}, {"name": "huawei-alarm-type-an:srv6-tunnel-hd-exceed-threshold", "value": 304, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:dfx/huawei-alarm-type-an:srv6-tunnel-hd-exceed-threshold"}]}, {"name": "huawei-alarm-type-an:arp-exceed-threshold", "value": 305, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:dfx/huawei-alarm-type-an:arp-exceed-threshold"}]}, {"name": "huawei-alarm-type-an:arp-exceed-spec", "value": 306, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:dfx/huawei-alarm-type-an:arp-exceed-spec"}]}, {"name": "huawei-alarm-type-an:nd-exceed-threshold", "value": 307, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:dfx/huawei-alarm-type-an:nd-exceed-threshold"}]}, {"name": "huawei-alarm-type-an:qacl", "value": 308, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:qacl"}]}, {"name": "huawei-alarm-type-an:add-acl-traffic-mirror", "value": 309, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:qacl/huawei-alarm-type-an:add-acl-traffic-mirror"}]}, {"name": "huawei-alarm-type-an:del-acl-traffic-mirror", "value": 310, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:l3p/huawei-alarm-type-an:qacl/huawei-alarm-type-an:del-acl-traffic-mirror"}]}, {"name": "huawei-alarm-type-an:eth", "value": 311, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth"}]}, {"name": "huawei-alarm-type-an:ethport-link-down", "value": 312, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethport-link-down"}]}, {"name": "huawei-alarm-type-an:ethport-optical-transceiver-absence", "value": 313, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethport-optical-transceiver-absence"}]}, {"name": "huawei-alarm-type-an:ethport-optical-transceiver-abnormal", "value": 314, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethport-optical-transceiver-abnormal"}]}, {"name": "huawei-alarm-type-an:ethport-optical-transceiver-normal", "value": 315, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethport-optical-transceiver-normal"}]}, {"name": "huawei-alarm-type-an:ethport-upstream-connection-fails", "value": 316, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethport-upstream-connection-fails"}]}, {"name": "huawei-alarm-type-an:ethport-optical-signal-los", "value": 317, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethport-optical-signal-los"}]}, {"name": "huawei-alarm-type-an:ethport-transceiver-type-change", "value": 318, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethport-transceiver-type-change"}]}, {"name": "huawei-alarm-type-an:ethport-activated", "value": 319, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethport-activated"}]}, {"name": "huawei-alarm-type-an:ethport-deactivated", "value": 320, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethport-deactivated"}]}, {"name": "huawei-alarm-type-an:ethport-performance-statistics-exceeds-alarm-threshold", "value": 321, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethport-performance-statistics-exceeds-alarm-threshold"}]}, {"name": "huawei-alarm-type-an:ethport-performance-statistics-exceeds-warning-threshold", "value": 322, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethport-performance-statistics-exceeds-warning-threshold"}]}, {"name": "huawei-alarm-type-an:ethport-optical-transceiver-tx-power-exceeds-threshold", "value": 323, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethport-optical-transceiver-tx-power-exceeds-threshold"}]}, {"name": "huawei-alarm-type-an:ethport-optical-transceiver-rx-power-exceeds-threshold", "value": 324, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethport-optical-transceiver-rx-power-exceeds-threshold"}]}, {"name": "huawei-alarm-type-an:ethport-optical-transceiver-temperature-exceeds-threshold", "value": 325, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethport-optical-transceiver-temperature-exceeds-threshold"}]}, {"name": "huawei-alarm-type-an:ethport-optical-transceiver-current-exceeds-threshold", "value": 326, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethport-optical-transceiver-current-exceeds-threshold"}]}, {"name": "huawei-alarm-type-an:ethport-optical-transceiver-voltage-exceeds-threshold", "value": 327, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethport-optical-transceiver-voltage-exceeds-threshold"}]}, {"name": "huawei-alarm-type-an:ethport-optical-transceiver-tec-current-exceeds-threshold", "value": 328, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethport-optical-transceiver-tec-current-exceeds-threshold"}]}, {"name": "huawei-alarm-type-an:ethopt-optical-laser-temperature-exceeds-threshold", "value": 329, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethopt-optical-laser-temperature-exceeds-threshold"}]}, {"name": "huawei-alarm-type-an:ethport-optical-transceiver-wavelength-exceeds-threshold", "value": 330, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethport-optical-transceiver-wavelength-exceeds-threshold"}]}, {"name": "huawei-alarm-type-an:configure-the-port-mirror", "value": 331, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:configure-the-port-mirror"}]}, {"name": "huawei-alarm-type-an:cancel-the-port-mirror", "value": 332, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:cancel-the-port-mirror"}]}, {"name": "huawei-alarm-type-an:monitor-portvlan-traffic-upstream-decrease", "value": 333, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:monitor-portvlan-traffic-upstream-decrease"}]}, {"name": "huawei-alarm-type-an:monitor-portvlan-traffic-downstream-decrease", "value": 334, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:monitor-portvlan-traffic-downstream-decrease"}]}, {"name": "huawei-alarm-type-an:monitor-portvlan-traffic-updownstream-decrease", "value": 335, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:monitor-portvlan-traffic-updownstream-decrease"}]}, {"name": "huawei-alarm-type-an:ethport-hardware-fault", "value": 336, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:eth/huawei-alarm-type-an:ethport-hardware-fault"}]}, {"name": "huawei-alarm-type-an:pppoe-user-status-monitor", "value": 337, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:pppoe-user-status-monitor"}]}, {"name": "huawei-alarm-type-an:dhcp-user-offline-monitor", "value": 338, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:dhcp-user-offline-monitor"}]}, {"name": "huawei-alarm-type-an:ifnet-interface-up", "value": 339, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:ifnet-interface-up"}]}, {"name": "huawei-alarm-type-an:ifnet-interface-down", "value": 340, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:ifnet-interface-down"}]}, {"name": "huawei-alarm-type-an:software-package-not-activated", "value": 341, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:software-package-not-activated"}]}, {"name": "huawei-alarm-type-an:comp-state-fault", "value": 342, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:comp-state-fault"}]}, {"name": "huawei-alarm-type-an:comp-state-restore", "value": 343, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:comp-state-restore"}]}, {"name": "huawei-alarm-type-an:power-over-threshold", "value": 344, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:power-over-threshold"}]}, {"name": "huawei-alarm-type-an:area-power-over-threshold", "value": 345, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:area-power-over-threshold"}]}, {"name": "huawei-alarm-type-an:auto-backup-succeeded", "value": 346, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:auto-backup-succeeded"}]}, {"name": "huawei-alarm-type-an:virtual-system-auto-backup-succeeded", "value": 347, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:virtual-system-auto-backup-succeeded"}]}, {"name": "huawei-alarm-type-an:auto-backup-failed", "value": 348, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:auto-backup-failed"}]}, {"name": "huawei-alarm-type-an:virtual-system-auto-backup-failed", "value": 349, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:virtual-system-auto-backup-failed"}]}, {"name": "huawei-alarm-type-an:backup-file-starts", "value": 350, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:backup-file-starts"}]}, {"name": "huawei-alarm-type-an:backup-file-succeeded", "value": 351, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:backup-file-succeeded"}]}, {"name": "huawei-alarm-type-an:backup-file-failed", "value": 352, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:backup-file-failed"}]}, {"name": "huawei-alarm-type-an:data-saving-fails", "value": 353, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:data-saving-fails"}]}, {"name": "huawei-alarm-type-an:extend-frames-overflow", "value": 354, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:extend-frames-overflow"}]}, {"name": "huawei-alarm-type-an:shelf-type-inconsistent-with-configured", "value": 355, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:shelf-type-inconsistent-with-configured"}]}, {"name": "huawei-alarm-type-an:system-startup", "value": 356, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:system-startup"}]}, {"name": "huawei-alarm-type-an:system-reset", "value": 357, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:system-reset"}]}, {"name": "huawei-alarm-type-an:system-resource-usage-overload", "value": 358, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:system-resource-usage-overload"}]}, {"name": "huawei-alarm-type-an:manager-will-expire", "value": 359, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:manager-will-expire"}]}, {"name": "huawei-alarm-type-an:manager-password-will-expire", "value": 360, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:manager-password-will-expire"}]}, {"name": "huawei-alarm-type-an:root-user-password-not-changed", "value": 361, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:root-user-password-not-changed"}]}, {"name": "huawei-alarm-type-an:loading-file-starts", "value": 362, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:loading-file-starts"}]}, {"name": "huawei-alarm-type-an:loading-file-succeeded", "value": 363, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:loading-file-succeeded"}]}, {"name": "huawei-alarm-type-an:loading-file-failed", "value": 364, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:loading-file-failed"}]}, {"name": "huawei-alarm-type-an:loading-file-in-standby-channel", "value": 365, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:loading-file-in-standby-channel"}]}, {"name": "huawei-alarm-type-an:duplicating-files-starts", "value": 366, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:duplicating-files-starts"}]}, {"name": "huawei-alarm-type-an:duplicating-files-succeeded", "value": 367, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:duplicating-files-succeeded"}]}, {"name": "huawei-alarm-type-an:duplicating-files-failed", "value": 368, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:duplicating-files-failed"}]}, {"name": "huawei-alarm-type-an:patch-in-activated-too-long-time", "value": 369, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:patch-in-activated-too-long-time"}]}, {"name": "huawei-alarm-type-an:patch-in-deactivated-too-long-time", "value": 370, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:patch-in-deactivated-too-long-time"}]}, {"name": "huawei-alarm-type-an:inconsistent-patch-states", "value": 371, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:inconsistent-patch-states"}]}, {"name": "huawei-alarm-type-an:patch-initialize-failed", "value": 372, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:patch-initialize-failed"}]}, {"name": "huawei-alarm-type-an:patch-initialization-complete", "value": 373, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:patch-initialization-complete"}]}, {"name": "huawei-alarm-type-an:patch-package-file-error", "value": 374, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:patch-package-file-error"}]}, {"name": "huawei-alarm-type-an:patch-package-file-error-restored", "value": 375, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:patch-package-file-error-restored"}]}, {"name": "huawei-alarm-type-an:protect-group-switch-over", "value": 376, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:protect-group-switch-over"}]}, {"name": "huawei-alarm-type-an:no-nms-probe-messages", "value": 377, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:no-nms-probe-messages"}]}, {"name": "huawei-alarm-type-an:important-operation-from-nms", "value": 378, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:important-operation-from-nms"}]}, {"name": "huawei-alarm-type-an:illegal-snmp-message", "value": 379, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:illegal-snmp-message"}]}, {"name": "huawei-alarm-type-an:remove-target-host", "value": 380, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:remove-target-host"}]}, {"name": "huawei-alarm-type-an:ssl-certificate-will-expire", "value": 381, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:ssl-certificate-will-expire"}]}, {"name": "huawei-alarm-type-an:ssl-certificate-expired", "value": 382, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:ssl-certificate-expired"}]}, {"name": "huawei-alarm-type-an:data-changed-during-switchover", "value": 383, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:data-changed-during-switchover"}]}, {"name": "huawei-alarm-type-an:start-batch-hot-backup", "value": 384, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:start-batch-hot-backup"}]}, {"name": "huawei-alarm-type-an:complete-batch-hot-backup", "value": 385, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:complete-batch-hot-backup"}]}, {"name": "huawei-alarm-type-an:control-boards-switchovered", "value": 386, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:control-boards-switchovered"}]}, {"name": "huawei-alarm-type-an:inconsistent-software-version", "value": 387, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:inconsistent-software-version"}]}, {"name": "huawei-alarm-type-an:start-software-upgrade-in-service", "value": 388, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:start-software-upgrade-in-service"}]}, {"name": "huawei-alarm-type-an:auto-deployment-failed", "value": 389, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:auto-deployment-failed"}]}, {"name": "huawei-alarm-type-an:configuration-lock-state-changed", "value": 390, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:configuration-lock-state-changed"}]}, {"name": "huawei-alarm-type-an:statistic-file-created-failed", "value": 391, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:statistic-file-created-failed"}]}, {"name": "huawei-alarm-type-an:system-operation-log-will-full", "value": 392, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:system-operation-log-will-full"}]}, {"name": "huawei-alarm-type-an:managing-user-level-changed", "value": 393, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:managing-user-level-changed"}]}, {"name": "huawei-alarm-type-an:locked-managing-user", "value": 394, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:locked-managing-user"}]}, {"name": "huawei-alarm-type-an:unlocked-managing-user", "value": 395, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:unlocked-managing-user"}]}, {"name": "huawei-alarm-type-an:managing-user-login-logout", "value": 396, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:managing-user-login-logout"}]}, {"name": "huawei-alarm-type-an:emergency-user-login-logout", "value": 397, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:emergency-user-login-logout"}]}, {"name": "huawei-alarm-type-an:export-key-not-configured", "value": 398, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:export-key-not-configured"}]}, {"name": "huawei-alarm-type-an:clock-1588-pdv-state-change", "value": 399, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:clock-1588-pdv-state-change"}]}, {"name": "huawei-alarm-type-an:clock-1588-trace-source-change", "value": 400, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:clock-1588-trace-source-change"}]}, {"name": "huawei-alarm-type-an:clock-8k-no-trace", "value": 401, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:clock-8k-no-trace"}]}, {"name": "huawei-alarm-type-an:clock-1588-source-lock-failed", "value": 402, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:clock-1588-source-lock-failed"}]}, {"name": "huawei-alarm-type-an:clock-8k-output-close", "value": 403, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:clock-8k-output-close"}]}, {"name": "huawei-alarm-type-an:clock-8k-lock-failed", "value": 404, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:clock-8k-lock-failed"}]}, {"name": "huawei-alarm-type-an:e1t1", "value": 405, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:e1t1"}]}, {"name": "huawei-alarm-type-an:e1t1-los-fault", "value": 406, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:e1t1/huawei-alarm-type-an:e1t1-los-fault"}]}, {"name": "huawei-alarm-type-an:e1t1-los-restore", "value": 407, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:e1t1/huawei-alarm-type-an:e1t1-los-restore"}]}, {"name": "huawei-alarm-type-an:e1t1-lfa-fault", "value": 408, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:e1t1/huawei-alarm-type-an:e1t1-lfa-fault"}]}, {"name": "huawei-alarm-type-an:e1t1-lfa-restore", "value": 409, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:e1t1/huawei-alarm-type-an:e1t1-lfa-restore"}]}, {"name": "huawei-alarm-type-an:e1t1-rra-fault", "value": 410, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:e1t1/huawei-alarm-type-an:e1t1-rra-fault"}]}, {"name": "huawei-alarm-type-an:e1t1-rra-restore", "value": 411, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:e1t1/huawei-alarm-type-an:e1t1-rra-restore"}]}, {"name": "huawei-alarm-type-an:e1t1-lmfa-fault", "value": 412, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:e1t1/huawei-alarm-type-an:e1t1-lmfa-fault"}]}, {"name": "huawei-alarm-type-an:e1t1-lmfa-restore", "value": 413, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:e1t1/huawei-alarm-type-an:e1t1-lmfa-restore"}]}, {"name": "huawei-alarm-type-an:e1t1-ais-fault", "value": 414, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:e1t1/huawei-alarm-type-an:e1t1-ais-fault"}]}, {"name": "huawei-alarm-type-an:e1t1-ais-restore", "value": 415, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:e1t1/huawei-alarm-type-an:e1t1-ais-restore"}]}, {"name": "huawei-alarm-type-an:e1t1-crc4-fault", "value": 416, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:e1t1/huawei-alarm-type-an:e1t1-crc4-fault"}]}, {"name": "huawei-alarm-type-an:e1t1-crc4-restore", "value": 417, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:e1t1/huawei-alarm-type-an:e1t1-crc4-restore"}]}, {"name": "huawei-alarm-type-an:aaa-user-acct-fail", "value": 418, "derived-paths": [{"derived-path": "ietf-alarms:alarm-type-id/huawei-alarm-type-an:system/huawei-alarm-type-an:aaa-user-acct-fail"}]}], "nullable": false}, {"name": "alarm-type-qualifier-match", "type": "string", "nullable": false}, {"name": "resource", "type": "string", "nullable": false}, {"name": "description", "type": "string", "nullable": false}, {"name": "alarm-severity-assignment-profile", "type": "container", "fields": []}], "keys": [{"name": "al:alarm-profile.1.PK", "index": {"type": "primary"}, "node": "al:alarm-profile.1", "fields": ["PID", "alarm-type-id", "alarm-type-qualifier-match", "resource"], "constraints": {"unique": true}}]}, {"name": "al:severity-level.2", "type": "leaf-list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "severity-level", "type": "enum", "enumerate": [{"name": "indeterminate", "value": 2}, {"name": "warning", "value": 3}, {"name": "minor", "value": 4}, {"name": "major", "value": 5}, {"name": "critical", "value": 6}], "nullable": false}], "keys": [{"name": "al:severity-level.2.PK", "index": {"type": "primary"}, "node": "al:severity-level.2", "fields": ["PID", "severity-level"], "constraints": {"unique": true}}]}, {"name": "al:shelf.1", "type": "list", "config": {"check_validity": true}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "description", "type": "string", "nullable": true}], "keys": [{"name": "al:shelf.1.PK", "index": {"type": "primary"}, "node": "al:shelf.1", "fields": ["PID", "name"], "constraints": {"unique": true}}]}]