{"op": "merge", "ietf-interfaces:interfaces": {"op": "merge", "if:interface.1": [{"name": "ethernetCsmacd.0.9.0", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "interface-usage": {"interface-usage": "network-port"}, "default-vlan": 1, "aggregator": {"work-mode": "static", "fast-period": 1, "slow-period": 30, "max-link-number": "no-limit", "least-link-number": "no-limit", "forward-mode": "ingress", "preempt-enabled": false, "preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "bridge-port": {"pvid": 1, "default-priority": 0, "priority-regeneration": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "pcp-selection": "8P0D", "use-dei": false, "drop-encoding": false, "service-access-priority-selection": false, "service-access-priority": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "acceptable-frame": "admit-all-frames", "enable-ingress-filtering": false, "enable-restricted-vlan-registration": false, "enable-vid-translation-table": false, "enable-egress-vid-translation-table": false}, "ethernet": {"auto-negotiation": {"enable": false}, "duplex": "full", "speed": 10.0, "flow-control": {"force-flow-control": false}, "physical": {"port-type": "10GE"}, "logical": {"tx-auto-off": {"enabled": false, "detect-time": 10, "resume-detect-mode": "manual", "resume-detect-interval": 100, "resume-detect-duration": 2000}}, "ethernet-frame": {"jumbo-frame": false, "mtu": 2052}}}, {"name": "ethernetCsmacd.0.9.1", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "interface-usage": {"interface-usage": "network-port"}, "default-vlan": 1, "aggregator": {"work-mode": "static", "fast-period": 1, "slow-period": 30, "max-link-number": "no-limit", "least-link-number": "no-limit", "forward-mode": "ingress", "preempt-enabled": false, "preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "bridge-port": {"pvid": 1, "default-priority": 0, "priority-regeneration": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "pcp-selection": "8P0D", "use-dei": false, "drop-encoding": false, "service-access-priority-selection": false, "service-access-priority": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "acceptable-frame": "admit-all-frames", "enable-ingress-filtering": false, "enable-restricted-vlan-registration": false, "enable-vid-translation-table": false, "enable-egress-vid-translation-table": false}, "ethernet": {"auto-negotiation": {"enable": false}, "duplex": "full", "speed": 10.0, "flow-control": {"force-flow-control": false}, "physical": {"port-type": "10GE"}, "logical": {"tx-auto-off": {"enabled": false, "detect-time": 10, "resume-detect-mode": "manual", "resume-detect-interval": 100, "resume-detect-duration": 2000}}, "ethernet-frame": {"jumbo-frame": false, "mtu": 2052}}}, {"name": "ethernetCsmacd.0.9.2", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "interface-usage": {"interface-usage": "network-port"}, "default-vlan": 1, "aggregator": {"work-mode": "static", "fast-period": 1, "slow-period": 30, "max-link-number": "no-limit", "least-link-number": "no-limit", "forward-mode": "ingress", "preempt-enabled": false, "preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "bridge-port": {"pvid": 1, "default-priority": 0, "priority-regeneration": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "pcp-selection": "8P0D", "use-dei": false, "drop-encoding": false, "service-access-priority-selection": false, "service-access-priority": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "acceptable-frame": "admit-all-frames", "enable-ingress-filtering": false, "enable-restricted-vlan-registration": false, "enable-vid-translation-table": false, "enable-egress-vid-translation-table": false}, "ethernet": {"auto-negotiation": {"enable": false}, "duplex": "full", "speed": 10.0, "flow-control": {"force-flow-control": false}, "physical": {"port-type": "10GE"}, "logical": {"tx-auto-off": {"enabled": false, "detect-time": 10, "resume-detect-mode": "manual", "resume-detect-interval": 100, "resume-detect-duration": 2000}}, "ethernet-frame": {"jumbo-frame": false, "mtu": 2052}}}, {"name": "ethernetCsmacd.0.9.3", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "interface-usage": {"interface-usage": "network-port"}, "default-vlan": 1, "aggregator": {"work-mode": "static", "fast-period": 1, "slow-period": 30, "max-link-number": "no-limit", "least-link-number": "no-limit", "forward-mode": "ingress", "preempt-enabled": false, "preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "bridge-port": {"pvid": 1, "default-priority": 0, "priority-regeneration": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "pcp-selection": "8P0D", "use-dei": false, "drop-encoding": false, "service-access-priority-selection": false, "service-access-priority": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "acceptable-frame": "admit-all-frames", "enable-ingress-filtering": false, "enable-restricted-vlan-registration": false, "enable-vid-translation-table": false, "enable-egress-vid-translation-table": false}, "ethernet": {"auto-negotiation": {"enable": false}, "duplex": "full", "speed": 10.0, "flow-control": {"force-flow-control": false}, "physical": {"port-type": "10GE"}, "logical": {"tx-auto-off": {"enabled": false, "detect-time": 10, "resume-detect-mode": "manual", "resume-detect-interval": 100, "resume-detect-duration": 2000}}, "ethernet-frame": {"jumbo-frame": false, "mtu": 2052}}}, {"name": "ethernetCsmacd.0.10.0", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "interface-usage": {"interface-usage": "network-port"}, "default-vlan": 1, "aggregator": {"work-mode": "static", "fast-period": 1, "slow-period": 30, "max-link-number": "no-limit", "least-link-number": "no-limit", "forward-mode": "ingress", "preempt-enabled": false, "preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "bridge-port": {"pvid": 1, "default-priority": 0, "priority-regeneration": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "pcp-selection": "8P0D", "use-dei": false, "drop-encoding": false, "service-access-priority-selection": false, "service-access-priority": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "acceptable-frame": "admit-all-frames", "enable-ingress-filtering": false, "enable-restricted-vlan-registration": false, "enable-vid-translation-table": false, "enable-egress-vid-translation-table": false}, "ethernet": {"auto-negotiation": {"enable": false}, "duplex": "full", "speed": 10.0, "flow-control": {"force-flow-control": false}, "physical": {"port-type": "10GE"}, "logical": {"tx-auto-off": {"enabled": false, "detect-time": 10, "resume-detect-mode": "manual", "resume-detect-interval": 100, "resume-detect-duration": 2000}}, "ethernet-frame": {"jumbo-frame": false, "mtu": 2052}}}, {"name": "ethernetCsmacd.0.10.1", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "interface-usage": {"interface-usage": "network-port"}, "default-vlan": 1, "aggregator": {"work-mode": "static", "fast-period": 1, "slow-period": 30, "max-link-number": "no-limit", "least-link-number": "no-limit", "forward-mode": "ingress", "preempt-enabled": false, "preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "bridge-port": {"pvid": 1, "default-priority": 0, "priority-regeneration": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "pcp-selection": "8P0D", "use-dei": false, "drop-encoding": false, "service-access-priority-selection": false, "service-access-priority": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "acceptable-frame": "admit-all-frames", "enable-ingress-filtering": false, "enable-restricted-vlan-registration": false, "enable-vid-translation-table": false, "enable-egress-vid-translation-table": false}, "ethernet": {"auto-negotiation": {"enable": false}, "duplex": "full", "speed": 10.0, "flow-control": {"force-flow-control": false}, "physical": {"port-type": "10GE"}, "logical": {"tx-auto-off": {"enabled": false, "detect-time": 10, "resume-detect-mode": "manual", "resume-detect-interval": 100, "resume-detect-duration": 2000}}, "ethernet-frame": {"jumbo-frame": false, "mtu": 2052}}}, {"name": "ethernetCsmacd.0.10.2", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "interface-usage": {"interface-usage": "network-port"}, "default-vlan": 1, "aggregator": {"work-mode": "static", "fast-period": 1, "slow-period": 30, "max-link-number": "no-limit", "least-link-number": "no-limit", "forward-mode": "ingress", "preempt-enabled": false, "preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "bridge-port": {"pvid": 1, "default-priority": 0, "priority-regeneration": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "pcp-selection": "8P0D", "use-dei": false, "drop-encoding": false, "service-access-priority-selection": false, "service-access-priority": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "acceptable-frame": "admit-all-frames", "enable-ingress-filtering": false, "enable-restricted-vlan-registration": false, "enable-vid-translation-table": false, "enable-egress-vid-translation-table": false}, "ethernet": {"auto-negotiation": {"enable": false}, "duplex": "full", "speed": 10.0, "flow-control": {"force-flow-control": false}, "physical": {"port-type": "10GE"}, "logical": {"tx-auto-off": {"enabled": false, "detect-time": 10, "resume-detect-mode": "manual", "resume-detect-interval": 100, "resume-detect-duration": 2000}}, "ethernet-frame": {"jumbo-frame": false, "mtu": 2052}}}, {"name": "ethernetCsmacd.0.10.3", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "interface-usage": {"interface-usage": "network-port"}, "default-vlan": 1, "aggregator": {"work-mode": "static", "fast-period": 1, "slow-period": 30, "max-link-number": "no-limit", "least-link-number": "no-limit", "forward-mode": "ingress", "preempt-enabled": false, "preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "bridge-port": {"pvid": 1, "default-priority": 0, "priority-regeneration": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "pcp-selection": "8P0D", "use-dei": false, "drop-encoding": false, "service-access-priority-selection": false, "service-access-priority": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "acceptable-frame": "admit-all-frames", "enable-ingress-filtering": false, "enable-restricted-vlan-registration": false, "enable-vid-translation-table": false, "enable-egress-vid-translation-table": false}, "ethernet": {"auto-negotiation": {"enable": false}, "duplex": "full", "speed": 10.0, "flow-control": {"force-flow-control": false}, "physical": {"port-type": "10GE"}, "logical": {"tx-auto-off": {"enabled": false, "detect-time": 10, "resume-detect-mode": "manual", "resume-detect-interval": 100, "resume-detect-duration": 2000}}, "ethernet-frame": {"jumbo-frame": false, "mtu": 2052}}}, {"name": "gpon.0.1.0", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.1.0", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.1.0", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.1.0", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.1.0.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.1.0", "channel-partition-ref": "channel-partition.0.1.0", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.1.0.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.1.0.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.1.1", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.1.1", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.1.1", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.1.1", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.1.1.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.1.1", "channel-partition-ref": "channel-partition.0.1.1", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.1.1.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.1.1.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.1.2", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.1.2", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.1.2", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.1.2", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.1.2.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.1.2", "channel-partition-ref": "channel-partition.0.1.2", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.1.2.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.1.2.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.1.3", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.1.3", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.1.3", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.1.3", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.1.3.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.1.3", "channel-partition-ref": "channel-partition.0.1.3", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.1.3.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.1.3.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.1.4", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.1.4", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.1.4", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.1.4", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.1.4.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.1.4", "channel-partition-ref": "channel-partition.0.1.4", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.1.4.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.1.4.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.1.5", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.1.5", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.1.5", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.1.5", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.1.5.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.1.5", "channel-partition-ref": "channel-partition.0.1.5", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.1.5.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.1.5.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.1.6", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.1.6", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.1.6", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.1.6", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.1.6.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.1.6", "channel-partition-ref": "channel-partition.0.1.6", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.1.6.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.1.6.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.1.7", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.1.7", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.1.7", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.1.7", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.1.7.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.1.7", "channel-partition-ref": "channel-partition.0.1.7", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.1.7.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.1.7.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.1.8", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.1.8", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.1.8", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.1.8", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.1.8.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.1.8", "channel-partition-ref": "channel-partition.0.1.8", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.1.8.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.1.8.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.1.9", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.1.9", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.1.9", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.1.9", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.1.9.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.1.9", "channel-partition-ref": "channel-partition.0.1.9", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.1.9.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.1.9.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.1.10", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.1.10", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.1.10", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.1.10", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.1.10.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.1.10", "channel-partition-ref": "channel-partition.0.1.10", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.1.10.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.1.10.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.1.11", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.1.11", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.1.11", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.1.11", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.1.11.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.1.11", "channel-partition-ref": "channel-partition.0.1.11", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.1.11.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.1.11.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.1.12", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.1.12", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.1.12", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.1.12", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.1.12.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.1.12", "channel-partition-ref": "channel-partition.0.1.12", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.1.12.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.1.12.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.1.13", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.1.13", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.1.13", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.1.13", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.1.13.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.1.13", "channel-partition-ref": "channel-partition.0.1.13", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.1.13.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.1.13.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.1.14", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.1.14", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.1.14", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.1.14", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.1.14.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.1.14", "channel-partition-ref": "channel-partition.0.1.14", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.1.14.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.1.14.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.1.15", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.1.15", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.1.15", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.1.15", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.1.15.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.1.15", "channel-partition-ref": "channel-partition.0.1.15", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.1.15.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.1.15.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.2.0", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.2.0", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.2.0", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.2.0", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.2.0.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.2.0", "channel-partition-ref": "channel-partition.0.2.0", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.2.0.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.2.0.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.2.1", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.2.1", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.2.1", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.2.1", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.2.1.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.2.1", "channel-partition-ref": "channel-partition.0.2.1", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.2.1.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.2.1.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.2.2", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.2.2", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.2.2", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.2.2", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.2.2.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.2.2", "channel-partition-ref": "channel-partition.0.2.2", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.2.2.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.2.2.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.2.3", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.2.3", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.2.3", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.2.3", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.2.3.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.2.3", "channel-partition-ref": "channel-partition.0.2.3", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.2.3.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.2.3.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.2.4", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.2.4", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.2.4", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.2.4", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.2.4.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.2.4", "channel-partition-ref": "channel-partition.0.2.4", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.2.4.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.2.4.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.2.5", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.2.5", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.2.5", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.2.5", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.2.5.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.2.5", "channel-partition-ref": "channel-partition.0.2.5", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.2.5.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.2.5.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.2.6", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.2.6", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.2.6", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.2.6", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.2.6.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.2.6", "channel-partition-ref": "channel-partition.0.2.6", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.2.6.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.2.6.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.2.7", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.2.7", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.2.7", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.2.7", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.2.7.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.2.7", "channel-partition-ref": "channel-partition.0.2.7", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.2.7.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.2.7.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.2.8", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.2.8", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.2.8", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.2.8", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.2.8.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.2.8", "channel-partition-ref": "channel-partition.0.2.8", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.2.8.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.2.8.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.2.9", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.2.9", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.2.9", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.2.9", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.2.9.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.2.9", "channel-partition-ref": "channel-partition.0.2.9", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.2.9.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.2.9.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.2.10", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.2.10", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.2.10", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.2.10", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.2.10.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.2.10", "channel-partition-ref": "channel-partition.0.2.10", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.2.10.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.2.10.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.2.11", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.2.11", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.2.11", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.2.11", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.2.11.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.2.11", "channel-partition-ref": "channel-partition.0.2.11", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.2.11.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.2.11.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.2.12", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.2.12", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.2.12", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.2.12", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.2.12.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.2.12", "channel-partition-ref": "channel-partition.0.2.12", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.2.12.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.2.12.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.2.13", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.2.13", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.2.13", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.2.13", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.2.13.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.2.13", "channel-partition-ref": "channel-partition.0.2.13", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.2.13.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.2.13.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.2.14", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.2.14", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.2.14", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.2.14", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.2.14.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.2.14", "channel-partition-ref": "channel-partition.0.2.14", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.2.14.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.2.14.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.2.15", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.2.15", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.2.15", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.2.15", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.2.15.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.2.15", "channel-partition-ref": "channel-partition.0.2.15", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.2.15.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.2.15.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.3.0", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.3.0", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.3.0", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.3.0", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.3.0.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.3.0", "channel-partition-ref": "channel-partition.0.3.0", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.3.0.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.3.0.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.3.1", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.3.1", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.3.1", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.3.1", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.3.1.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.3.1", "channel-partition-ref": "channel-partition.0.3.1", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.3.1.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.3.1.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.3.2", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.3.2", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.3.2", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.3.2", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.3.2.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.3.2", "channel-partition-ref": "channel-partition.0.3.2", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.3.2.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.3.2.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.3.3", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.3.3", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.3.3", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.3.3", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.3.3.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.3.3", "channel-partition-ref": "channel-partition.0.3.3", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.3.3.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.3.3.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.3.4", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.3.4", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.3.4", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.3.4", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.3.4.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.3.4", "channel-partition-ref": "channel-partition.0.3.4", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.3.4.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.3.4.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.3.5", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.3.5", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.3.5", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.3.5", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.3.5.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.3.5", "channel-partition-ref": "channel-partition.0.3.5", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.3.5.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.3.5.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.3.6", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.3.6", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.3.6", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.3.6", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.3.6.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.3.6", "channel-partition-ref": "channel-partition.0.3.6", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.3.6.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.3.6.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.3.7", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.3.7", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.3.7", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.3.7", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.3.7.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.3.7", "channel-partition-ref": "channel-partition.0.3.7", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.3.7.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.3.7.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.3.8", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.3.8", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.3.8", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.3.8", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.3.8.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.3.8", "channel-partition-ref": "channel-partition.0.3.8", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.3.8.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.3.8.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.3.9", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.3.9", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.3.9", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.3.9", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.3.9.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.3.9", "channel-partition-ref": "channel-partition.0.3.9", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.3.9.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.3.9.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.3.10", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.3.10", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.3.10", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.3.10", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.3.10.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.3.10", "channel-partition-ref": "channel-partition.0.3.10", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.3.10.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.3.10.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.3.11", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.3.11", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.3.11", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.3.11", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.3.11.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.3.11", "channel-partition-ref": "channel-partition.0.3.11", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.3.11.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.3.11.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.3.12", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.3.12", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.3.12", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.3.12", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.3.12.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.3.12", "channel-partition-ref": "channel-partition.0.3.12", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.3.12.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.3.12.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.3.13", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.3.13", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.3.13", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.3.13", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.3.13.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.3.13", "channel-partition-ref": "channel-partition.0.3.13", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.3.13.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.3.13.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.3.14", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.3.14", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.3.14", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.3.14", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.3.14.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.3.14", "channel-partition-ref": "channel-partition.0.3.14", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.3.14.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.3.14.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.3.15", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.3.15", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.3.15", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.3.15", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.3.15.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.3.15", "channel-partition-ref": "channel-partition.0.3.15", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.3.15.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.3.15.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.4.0", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.4.0", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.4.0", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.4.0", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.4.0.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.4.0", "channel-partition-ref": "channel-partition.0.4.0", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.4.0.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.4.0.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.4.1", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.4.1", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.4.1", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.4.1", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.4.1.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.4.1", "channel-partition-ref": "channel-partition.0.4.1", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.4.1.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.4.1.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.4.2", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.4.2", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.4.2", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.4.2", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.4.2.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.4.2", "channel-partition-ref": "channel-partition.0.4.2", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.4.2.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.4.2.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.4.3", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.4.3", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.4.3", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.4.3", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.4.3.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.4.3", "channel-partition-ref": "channel-partition.0.4.3", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.4.3.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.4.3.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.4.4", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.4.4", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.4.4", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.4.4", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.4.4.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.4.4", "channel-partition-ref": "channel-partition.0.4.4", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.4.4.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.4.4.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.4.5", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.4.5", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.4.5", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.4.5", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.4.5.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.4.5", "channel-partition-ref": "channel-partition.0.4.5", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.4.5.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.4.5.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.4.6", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.4.6", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.4.6", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.4.6", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.4.6.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.4.6", "channel-partition-ref": "channel-partition.0.4.6", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.4.6.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.4.6.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.4.7", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.4.7", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.4.7", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.4.7", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.4.7.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.4.7", "channel-partition-ref": "channel-partition.0.4.7", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.4.7.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.4.7.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.4.8", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.4.8", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.4.8", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.4.8", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.4.8.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.4.8", "channel-partition-ref": "channel-partition.0.4.8", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.4.8.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.4.8.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.4.9", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.4.9", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.4.9", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.4.9", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.4.9.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.4.9", "channel-partition-ref": "channel-partition.0.4.9", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.4.9.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.4.9.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.4.10", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.4.10", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.4.10", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.4.10", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.4.10.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.4.10", "channel-partition-ref": "channel-partition.0.4.10", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.4.10.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.4.10.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.4.11", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.4.11", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.4.11", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.4.11", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.4.11.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.4.11", "channel-partition-ref": "channel-partition.0.4.11", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.4.11.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.4.11.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.4.12", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.4.12", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.4.12", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.4.12", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.4.12.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.4.12", "channel-partition-ref": "channel-partition.0.4.12", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.4.12.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.4.12.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.4.13", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.4.13", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.4.13", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.4.13", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.4.13.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.4.13", "channel-partition-ref": "channel-partition.0.4.13", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.4.13.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.4.13.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.4.14", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.4.14", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.4.14", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.4.14", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.4.14.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.4.14", "channel-partition-ref": "channel-partition.0.4.14", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.4.14.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.4.14.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.4.15", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.4.15", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.4.15", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.4.15", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.4.15.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.4.15", "channel-partition-ref": "channel-partition.0.4.15", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.4.15.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.4.15.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.5.0", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.5.0", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.5.0", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.5.0", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.5.0.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.5.0", "channel-partition-ref": "channel-partition.0.5.0", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.5.0.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.5.0.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.5.1", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.5.1", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.5.1", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.5.1", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.5.1.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.5.1", "channel-partition-ref": "channel-partition.0.5.1", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.5.1.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.5.1.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.5.2", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.5.2", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.5.2", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.5.2", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.5.2.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.5.2", "channel-partition-ref": "channel-partition.0.5.2", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.5.2.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.5.2.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.5.3", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.5.3", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.5.3", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.5.3", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.5.3.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.5.3", "channel-partition-ref": "channel-partition.0.5.3", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.5.3.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.5.3.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.5.4", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.5.4", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.5.4", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.5.4", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.5.4.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.5.4", "channel-partition-ref": "channel-partition.0.5.4", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.5.4.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.5.4.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.5.5", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.5.5", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.5.5", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.5.5", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.5.5.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.5.5", "channel-partition-ref": "channel-partition.0.5.5", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.5.5.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.5.5.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.5.6", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.5.6", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.5.6", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.5.6", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.5.6.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.5.6", "channel-partition-ref": "channel-partition.0.5.6", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.5.6.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.5.6.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.5.7", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.5.7", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.5.7", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.5.7", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.5.7.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.5.7", "channel-partition-ref": "channel-partition.0.5.7", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.5.7.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.5.7.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.5.8", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.5.8", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.5.8", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.5.8", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.5.8.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.5.8", "channel-partition-ref": "channel-partition.0.5.8", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.5.8.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.5.8.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.5.9", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.5.9", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.5.9", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.5.9", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.5.9.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.5.9", "channel-partition-ref": "channel-partition.0.5.9", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.5.9.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.5.9.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.5.10", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.5.10", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.5.10", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.5.10", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.5.10.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.5.10", "channel-partition-ref": "channel-partition.0.5.10", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.5.10.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.5.10.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.5.11", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.5.11", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.5.11", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.5.11", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.5.11.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.5.11", "channel-partition-ref": "channel-partition.0.5.11", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.5.11.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.5.11.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.5.12", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.5.12", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.5.12", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.5.12", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.5.12.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.5.12", "channel-partition-ref": "channel-partition.0.5.12", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.5.12.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.5.12.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.5.13", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.5.13", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.5.13", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.5.13", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.5.13.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.5.13", "channel-partition-ref": "channel-partition.0.5.13", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.5.13.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.5.13.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.5.14", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.5.14", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.5.14", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.5.14", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.5.14.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.5.14", "channel-partition-ref": "channel-partition.0.5.14", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.5.14.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.5.14.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.5.15", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.5.15", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.5.15", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.5.15", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.5.15.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.5.15", "channel-partition-ref": "channel-partition.0.5.15", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.5.15.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.5.15.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.6.0", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.6.0", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.6.0", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.6.0", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.6.0.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.6.0", "channel-partition-ref": "channel-partition.0.6.0", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.6.0.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.6.0.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.6.1", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.6.1", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.6.1", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.6.1", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.6.1.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.6.1", "channel-partition-ref": "channel-partition.0.6.1", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.6.1.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.6.1.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.6.2", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.6.2", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.6.2", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.6.2", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.6.2.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.6.2", "channel-partition-ref": "channel-partition.0.6.2", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.6.2.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.6.2.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.6.3", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.6.3", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.6.3", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.6.3", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.6.3.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.6.3", "channel-partition-ref": "channel-partition.0.6.3", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.6.3.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.6.3.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.6.4", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.6.4", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.6.4", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.6.4", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.6.4.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.6.4", "channel-partition-ref": "channel-partition.0.6.4", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.6.4.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.6.4.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.6.5", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.6.5", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.6.5", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.6.5", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.6.5.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.6.5", "channel-partition-ref": "channel-partition.0.6.5", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.6.5.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.6.5.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.6.6", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.6.6", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.6.6", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.6.6", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.6.6.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.6.6", "channel-partition-ref": "channel-partition.0.6.6", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.6.6.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.6.6.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.6.7", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.6.7", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.6.7", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.6.7", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.6.7.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.6.7", "channel-partition-ref": "channel-partition.0.6.7", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.6.7.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.6.7.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.6.8", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.6.8", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.6.8", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.6.8", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.6.8.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.6.8", "channel-partition-ref": "channel-partition.0.6.8", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.6.8.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.6.8.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.6.9", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.6.9", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.6.9", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.6.9", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.6.9.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.6.9", "channel-partition-ref": "channel-partition.0.6.9", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.6.9.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.6.9.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.6.10", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.6.10", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.6.10", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.6.10", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.6.10.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.6.10", "channel-partition-ref": "channel-partition.0.6.10", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.6.10.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.6.10.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.6.11", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.6.11", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.6.11", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.6.11", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.6.11.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.6.11", "channel-partition-ref": "channel-partition.0.6.11", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.6.11.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.6.11.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.6.12", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.6.12", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.6.12", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.6.12", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.6.12.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.6.12", "channel-partition-ref": "channel-partition.0.6.12", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.6.12.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.6.12.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.6.13", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.6.13", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.6.13", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.6.13", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.6.13.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.6.13", "channel-partition-ref": "channel-partition.0.6.13", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.6.13.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.6.13.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.6.14", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.6.14", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.6.14", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.6.14", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.6.14.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.6.14", "channel-partition-ref": "channel-partition.0.6.14", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.6.14.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.6.14.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.6.15", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.6.15", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.6.15", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.6.15", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.6.15.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.6.15", "channel-partition-ref": "channel-partition.0.6.15", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.6.15.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.6.15.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.7.0", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.7.0", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.7.0", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.7.0", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.7.0.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.7.0", "channel-partition-ref": "channel-partition.0.7.0", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.7.0.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.7.0.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.7.1", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.7.1", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.7.1", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.7.1", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.7.1.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.7.1", "channel-partition-ref": "channel-partition.0.7.1", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.7.1.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.7.1.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.7.2", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.7.2", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.7.2", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.7.2", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.7.2.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.7.2", "channel-partition-ref": "channel-partition.0.7.2", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.7.2.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.7.2.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.7.3", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.7.3", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.7.3", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.7.3", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.7.3.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.7.3", "channel-partition-ref": "channel-partition.0.7.3", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.7.3.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.7.3.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.7.4", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.7.4", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.7.4", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.7.4", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.7.4.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.7.4", "channel-partition-ref": "channel-partition.0.7.4", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.7.4.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.7.4.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.7.5", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.7.5", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.7.5", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.7.5", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.7.5.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.7.5", "channel-partition-ref": "channel-partition.0.7.5", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.7.5.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.7.5.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.7.6", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.7.6", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.7.6", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.7.6", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.7.6.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.7.6", "channel-partition-ref": "channel-partition.0.7.6", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.7.6.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.7.6.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.7.7", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.7.7", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.7.7", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.7.7", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.7.7.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.7.7", "channel-partition-ref": "channel-partition.0.7.7", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.7.7.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.7.7.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.7.8", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.7.8", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.7.8", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.7.8", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.7.8.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.7.8", "channel-partition-ref": "channel-partition.0.7.8", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.7.8.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.7.8.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.7.9", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.7.9", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.7.9", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.7.9", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.7.9.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.7.9", "channel-partition-ref": "channel-partition.0.7.9", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.7.9.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.7.9.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.7.10", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.7.10", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.7.10", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.7.10", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.7.10.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.7.10", "channel-partition-ref": "channel-partition.0.7.10", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.7.10.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.7.10.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.7.11", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.7.11", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.7.11", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.7.11", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.7.11.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.7.11", "channel-partition-ref": "channel-partition.0.7.11", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.7.11.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.7.11.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.7.12", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.7.12", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.7.12", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.7.12", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.7.12.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.7.12", "channel-partition-ref": "channel-partition.0.7.12", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.7.12.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.7.12.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.7.13", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.7.13", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.7.13", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.7.13", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.7.13.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.7.13", "channel-partition-ref": "channel-partition.0.7.13", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.7.13.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.7.13.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.7.14", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.7.14", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.7.14", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.7.14", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.7.14.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.7.14", "channel-partition-ref": "channel-partition.0.7.14", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.7.14.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.7.14.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.7.15", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.7.15", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.7.15", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.7.15", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.7.15.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.7.15", "channel-partition-ref": "channel-partition.0.7.15", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.7.15.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.7.15.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.8.0", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.8.0", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.8.0", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.8.0", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.8.0.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.8.0", "channel-partition-ref": "channel-partition.0.8.0", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.8.0.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.8.0.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.8.1", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.8.1", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.8.1", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.8.1", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.8.1.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.8.1", "channel-partition-ref": "channel-partition.0.8.1", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.8.1.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.8.1.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.8.2", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.8.2", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.8.2", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.8.2", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.8.2.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.8.2", "channel-partition-ref": "channel-partition.0.8.2", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.8.2.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.8.2.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.8.3", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.8.3", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.8.3", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.8.3", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.8.3.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.8.3", "channel-partition-ref": "channel-partition.0.8.3", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.8.3.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.8.3.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.8.4", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.8.4", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.8.4", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.8.4", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.8.4.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.8.4", "channel-partition-ref": "channel-partition.0.8.4", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.8.4.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.8.4.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.8.5", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.8.5", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.8.5", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.8.5", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.8.5.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.8.5", "channel-partition-ref": "channel-partition.0.8.5", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.8.5.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.8.5.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.8.6", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.8.6", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.8.6", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.8.6", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.8.6.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.8.6", "channel-partition-ref": "channel-partition.0.8.6", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.8.6.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.8.6.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.8.7", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.8.7", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.8.7", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.8.7", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.8.7.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.8.7", "channel-partition-ref": "channel-partition.0.8.7", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.8.7.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.8.7.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.8.8", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.8.8", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.8.8", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.8.8", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.8.8.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.8.8", "channel-partition-ref": "channel-partition.0.8.8", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.8.8.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.8.8.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.8.9", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.8.9", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.8.9", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.8.9", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.8.9.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.8.9", "channel-partition-ref": "channel-partition.0.8.9", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.8.9.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.8.9.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.8.10", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.8.10", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.8.10", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.8.10", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.8.10.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.8.10", "channel-partition-ref": "channel-partition.0.8.10", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.8.10.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.8.10.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.8.11", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.8.11", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.8.11", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.8.11", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.8.11.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.8.11", "channel-partition-ref": "channel-partition.0.8.11", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.8.11.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.8.11.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.8.12", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.8.12", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.8.12", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.8.12", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.8.12.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.8.12", "channel-partition-ref": "channel-partition.0.8.12", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.8.12.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.8.12.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.8.13", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.8.13", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.8.13", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.8.13", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.8.13.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.8.13", "channel-partition-ref": "channel-partition.0.8.13", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.8.13.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.8.13.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.8.14", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.8.14", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.8.14", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.8.14", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.8.14.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.8.14", "channel-partition-ref": "channel-partition.0.8.14", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.8.14.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.8.14.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.8.15", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.8.15", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.8.15", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.8.15", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.8.15.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.8.15", "channel-partition-ref": "channel-partition.0.8.15", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.8.15.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.8.15.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "ethernetCsmacd.0.11.0", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "interface-usage": {"interface-usage": "network-port"}, "tpid": {"s-vlan-tpid": "dot1q"}, "default-vlan": 1, "aggregator": {"work-mode": "static", "fast-period": 1, "slow-period": 30, "max-link-number": "no-limit", "least-link-number": "no-limit", "forward-mode": "ingress", "preempt-enabled": false, "preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "bridge-port": {"pvid": 1, "default-priority": 0, "priority-regeneration": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "pcp-selection": "8P0D", "use-dei": false, "drop-encoding": false, "service-access-priority-selection": false, "service-access-priority": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "acceptable-frame": "admit-all-frames", "enable-ingress-filtering": false, "enable-restricted-vlan-registration": false, "enable-vid-translation-table": false, "enable-egress-vid-translation-table": false}, "ethernet": {"auto-negotiation": {"enable": false}, "duplex": "full", "speed": 10.0, "flow-control": {"force-flow-control": false}, "physical": {"port-type": "10GE"}, "logical": {"tx-auto-off": {"enabled": false, "detect-time": 10, "resume-detect-mode": "manual", "resume-detect-interval": 100, "resume-detect-duration": 2000}}, "ethernet-frame": {"jumbo-frame": false, "mtu": 2052}}}, {"name": "ethernetCsmacd.0.11.1", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "interface-usage": {"interface-usage": "network-port"}, "tpid": {"s-vlan-tpid": "dot1q"}, "default-vlan": 1, "aggregator": {"work-mode": "static", "fast-period": 1, "slow-period": 30, "max-link-number": "no-limit", "least-link-number": "no-limit", "forward-mode": "ingress", "preempt-enabled": false, "preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "bridge-port": {"pvid": 1, "default-priority": 0, "priority-regeneration": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "pcp-selection": "8P0D", "use-dei": false, "drop-encoding": false, "service-access-priority-selection": false, "service-access-priority": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "acceptable-frame": "admit-all-frames", "enable-ingress-filtering": false, "enable-restricted-vlan-registration": false, "enable-vid-translation-table": false, "enable-egress-vid-translation-table": false}, "ethernet": {"auto-negotiation": {"enable": false}, "duplex": "full", "speed": 10.0, "flow-control": {"force-flow-control": false}, "physical": {"port-type": "10GE"}, "logical": {"tx-auto-off": {"enabled": false, "detect-time": 10, "resume-detect-mode": "manual", "resume-detect-interval": 100, "resume-detect-duration": 2000}}, "ethernet-frame": {"jumbo-frame": false, "mtu": 2052}}}, {"name": "ethernetCsmacd.0.11.2", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "interface-usage": {"interface-usage": "network-port"}, "tpid": {"s-vlan-tpid": "dot1q"}, "default-vlan": 1, "aggregator": {"work-mode": "static", "fast-period": 1, "slow-period": 30, "max-link-number": "no-limit", "least-link-number": "no-limit", "forward-mode": "ingress", "preempt-enabled": false, "preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "bridge-port": {"pvid": 1, "default-priority": 0, "priority-regeneration": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "pcp-selection": "8P0D", "use-dei": false, "drop-encoding": false, "service-access-priority-selection": false, "service-access-priority": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "acceptable-frame": "admit-all-frames", "enable-ingress-filtering": false, "enable-restricted-vlan-registration": false, "enable-vid-translation-table": false, "enable-egress-vid-translation-table": false}, "ethernet": {"auto-negotiation": {"enable": false}, "duplex": "full", "speed": 10.0, "flow-control": {"force-flow-control": false}, "physical": {"port-type": "10GE"}, "logical": {"tx-auto-off": {"enabled": false, "detect-time": 10, "resume-detect-mode": "manual", "resume-detect-interval": 100, "resume-detect-duration": 2000}}, "ethernet-frame": {"jumbo-frame": false, "mtu": 2052}}}, {"name": "ethernetCsmacd.0.11.3", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "interface-usage": {"interface-usage": "network-port"}, "tpid": {"s-vlan-tpid": "dot1q"}, "default-vlan": 1, "aggregator": {"work-mode": "static", "fast-period": 1, "slow-period": 30, "max-link-number": "no-limit", "least-link-number": "no-limit", "forward-mode": "ingress", "preempt-enabled": false, "preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "bridge-port": {"pvid": 1, "default-priority": 0, "priority-regeneration": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "pcp-selection": "8P0D", "use-dei": false, "drop-encoding": false, "service-access-priority-selection": false, "service-access-priority": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "acceptable-frame": "admit-all-frames", "enable-ingress-filtering": false, "enable-restricted-vlan-registration": false, "enable-vid-translation-table": false, "enable-egress-vid-translation-table": false}, "ethernet": {"auto-negotiation": {"enable": false}, "duplex": "full", "speed": 10.0, "flow-control": {"force-flow-control": false}, "physical": {"port-type": "10GE"}, "logical": {"tx-auto-off": {"enabled": false, "detect-time": 10, "resume-detect-mode": "manual", "resume-detect-interval": 100, "resume-detect-duration": 2000}}, "ethernet-frame": {"jumbo-frame": false, "mtu": 2052}}}, {"name": "ethernetCsmacd.0.11.4", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "interface-usage": {"interface-usage": "network-port"}, "tpid": {"s-vlan-tpid": "dot1q"}, "default-vlan": 1, "aggregator": {"work-mode": "static", "fast-period": 1, "slow-period": 30, "max-link-number": "no-limit", "least-link-number": "no-limit", "forward-mode": "ingress", "preempt-enabled": false, "preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "bridge-port": {"pvid": 1, "default-priority": 0, "priority-regeneration": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "pcp-selection": "8P0D", "use-dei": false, "drop-encoding": false, "service-access-priority-selection": false, "service-access-priority": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "acceptable-frame": "admit-all-frames", "enable-ingress-filtering": false, "enable-restricted-vlan-registration": false, "enable-vid-translation-table": false, "enable-egress-vid-translation-table": false}, "ethernet": {"auto-negotiation": {"enable": false}, "duplex": "full", "speed": 10.0, "flow-control": {"force-flow-control": false}, "physical": {"port-type": "10GE"}, "logical": {"tx-auto-off": {"enabled": false, "detect-time": 10, "resume-detect-mode": "manual", "resume-detect-interval": 100, "resume-detect-duration": 2000}}, "ethernet-frame": {"jumbo-frame": false, "mtu": 2052}}}, {"name": "ethernetCsmacd.0.11.5", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "interface-usage": {"interface-usage": "network-port"}, "tpid": {"s-vlan-tpid": "dot1q"}, "default-vlan": 1, "aggregator": {"work-mode": "static", "fast-period": 1, "slow-period": 30, "max-link-number": "no-limit", "least-link-number": "no-limit", "forward-mode": "ingress", "preempt-enabled": false, "preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "bridge-port": {"pvid": 1, "default-priority": 0, "priority-regeneration": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "pcp-selection": "8P0D", "use-dei": false, "drop-encoding": false, "service-access-priority-selection": false, "service-access-priority": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "acceptable-frame": "admit-all-frames", "enable-ingress-filtering": false, "enable-restricted-vlan-registration": false, "enable-vid-translation-table": false, "enable-egress-vid-translation-table": false}, "ethernet": {"auto-negotiation": {"enable": false}, "duplex": "full", "speed": 10.0, "flow-control": {"force-flow-control": false}, "physical": {"port-type": "10GE"}, "logical": {"tx-auto-off": {"enabled": false, "detect-time": 10, "resume-detect-mode": "manual", "resume-detect-interval": 100, "resume-detect-duration": 2000}}, "ethernet-frame": {"jumbo-frame": false, "mtu": 2052}}}, {"name": "ethernetCsmacd.0.11.6", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "interface-usage": {"interface-usage": "network-port"}, "tpid": {"s-vlan-tpid": "dot1q"}, "default-vlan": 1, "aggregator": {"work-mode": "static", "fast-period": 1, "slow-period": 30, "max-link-number": "no-limit", "least-link-number": "no-limit", "forward-mode": "ingress", "preempt-enabled": false, "preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "bridge-port": {"pvid": 1, "default-priority": 0, "priority-regeneration": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "pcp-selection": "8P0D", "use-dei": false, "drop-encoding": false, "service-access-priority-selection": false, "service-access-priority": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "acceptable-frame": "admit-all-frames", "enable-ingress-filtering": false, "enable-restricted-vlan-registration": false, "enable-vid-translation-table": false, "enable-egress-vid-translation-table": false}, "ethernet": {"auto-negotiation": {"enable": false}, "duplex": "full", "speed": 10.0, "flow-control": {"force-flow-control": false}, "physical": {"port-type": "10GE"}, "logical": {"tx-auto-off": {"enabled": false, "detect-time": 10, "resume-detect-mode": "manual", "resume-detect-interval": 100, "resume-detect-duration": 2000}}, "ethernet-frame": {"jumbo-frame": false, "mtu": 2052}}}, {"name": "ethernetCsmacd.0.11.7", "type": "iana-if-type:ethernetCsmacd", "enabled": true, "interface-usage": {"interface-usage": "network-port"}, "tpid": {"s-vlan-tpid": "dot1q"}, "default-vlan": 1, "aggregator": {"work-mode": "static", "fast-period": 1, "slow-period": 30, "max-link-number": "no-limit", "least-link-number": "no-limit", "forward-mode": "ingress", "preempt-enabled": false, "preempt-delay": 0}, "aggregation-port": {"aggregation-port-lacp": {"actor-port-priority": 16384}}, "bridge-port": {"pvid": 1, "default-priority": 0, "priority-regeneration": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "pcp-selection": "8P0D", "use-dei": false, "drop-encoding": false, "service-access-priority-selection": false, "service-access-priority": {"priority0": 0, "priority1": 1, "priority2": 2, "priority3": 3, "priority4": 4, "priority5": 5, "priority6": 6, "priority7": 7}, "acceptable-frame": "admit-all-frames", "enable-ingress-filtering": false, "enable-restricted-vlan-registration": false, "enable-vid-translation-table": false, "enable-egress-vid-translation-table": false}, "ethernet": {"auto-negotiation": {"enable": false}, "duplex": "full", "speed": 10.0, "flow-control": {"force-flow-control": false}, "physical": {"port-type": "10GE"}, "logical": {"tx-auto-off": {"enabled": false, "detect-time": 10, "resume-detect-mode": "manual", "resume-detect-interval": 100, "resume-detect-duration": 2000}}, "ethernet-frame": {"jumbo-frame": false, "mtu": 2052}}}, {"name": "gpon.0.12.0", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.12.0", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.12.0", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.12.0", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.12.0.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.12.0", "channel-partition-ref": "channel-partition.0.12.0", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.12.0.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.12.0.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.12.1", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.12.1", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.12.1", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.12.1", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.12.1.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.12.1", "channel-partition-ref": "channel-partition.0.12.1", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.12.1.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.12.1.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.12.2", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.12.2", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.12.2", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.12.2", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.12.2.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.12.2", "channel-partition-ref": "channel-partition.0.12.2", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.12.2.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.12.2.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.12.3", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.12.3", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.12.3", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.12.3", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.12.3.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.12.3", "channel-partition-ref": "channel-partition.0.12.3", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.12.3.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.12.3.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.12.4", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.12.4", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.12.4", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.12.4", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.12.4.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.12.4", "channel-partition-ref": "channel-partition.0.12.4", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.12.4.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.12.4.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.12.5", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.12.5", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.12.5", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.12.5", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.12.5.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.12.5", "channel-partition-ref": "channel-partition.0.12.5", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.12.5.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.12.5.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.12.6", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.12.6", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.12.6", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.12.6", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.12.6.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.12.6", "channel-partition-ref": "channel-partition.0.12.6", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.12.6.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.12.6.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.12.7", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.12.7", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.12.7", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.12.7", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.12.7.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.12.7", "channel-partition-ref": "channel-partition.0.12.7", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.12.7.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.12.7.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.12.8", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.12.8", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.12.8", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.12.8", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.12.8.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.12.8", "channel-partition-ref": "channel-partition.0.12.8", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.12.8.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.12.8.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.12.9", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.12.9", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.12.9", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.12.9", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.12.9.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.12.9", "channel-partition-ref": "channel-partition.0.12.9", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.12.9.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.12.9.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.12.10", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.12.10", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.12.10", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.12.10", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.12.10.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.12.10", "channel-partition-ref": "channel-partition.0.12.10", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.12.10.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.12.10.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.12.11", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.12.11", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.12.11", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.12.11", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.12.11.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.12.11", "channel-partition-ref": "channel-partition.0.12.11", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.12.11.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.12.11.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.12.12", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.12.12", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.12.12", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.12.12", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.12.12.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.12.12", "channel-partition-ref": "channel-partition.0.12.12", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.12.12.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.12.12.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.12.13", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.12.13", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.12.13", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.12.13", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.12.13.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.12.13", "channel-partition-ref": "channel-partition.0.12.13", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.12.13.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.12.13.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.12.14", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.12.14", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.12.14", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.12.14", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.12.14.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.12.14", "channel-partition-ref": "channel-partition.0.12.14", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.12.14.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.12.14.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.12.15", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.12.15", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.12.15", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.12.15", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.12.15.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.12.15", "channel-partition-ref": "channel-partition.0.12.15", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.12.15.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.12.15.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.14.0", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.14.0", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.14.0", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.14.0", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.14.0.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.14.0", "channel-partition-ref": "channel-partition.0.14.0", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.14.0.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.14.0.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.14.1", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.14.1", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.14.1", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.14.1", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.14.1.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.14.1", "channel-partition-ref": "channel-partition.0.14.1", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.14.1.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.14.1.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.14.2", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.14.2", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.14.2", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.14.2", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.14.2.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.14.2", "channel-partition-ref": "channel-partition.0.14.2", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.14.2.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.14.2.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.14.3", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.14.3", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.14.3", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.14.3", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.14.3.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.14.3", "channel-partition-ref": "channel-partition.0.14.3", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.14.3.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.14.3.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.14.4", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.14.4", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.14.4", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.14.4", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.14.4.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.14.4", "channel-partition-ref": "channel-partition.0.14.4", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.14.4.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.14.4.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.14.5", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.14.5", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.14.5", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.14.5", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.14.5.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.14.5", "channel-partition-ref": "channel-partition.0.14.5", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.14.5.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.14.5.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.14.6", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.14.6", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.14.6", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.14.6", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.14.6.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.14.6", "channel-partition-ref": "channel-partition.0.14.6", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.14.6.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.14.6.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.14.7", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.14.7", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.14.7", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.14.7", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.14.7.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.14.7", "channel-partition-ref": "channel-partition.0.14.7", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.14.7.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.14.7.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.14.8", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.14.8", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.14.8", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.14.8", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.14.8.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.14.8", "channel-partition-ref": "channel-partition.0.14.8", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.14.8.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.14.8.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.14.9", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.14.9", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.14.9", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.14.9", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.14.9.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.14.9", "channel-partition-ref": "channel-partition.0.14.9", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.14.9.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.14.9.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.14.10", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.14.10", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.14.10", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.14.10", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.14.10.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.14.10", "channel-partition-ref": "channel-partition.0.14.10", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.14.10.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.14.10.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.14.11", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.14.11", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.14.11", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.14.11", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.14.11.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.14.11", "channel-partition-ref": "channel-partition.0.14.11", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.14.11.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.14.11.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.14.12", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.14.12", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.14.12", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.14.12", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.14.12.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.14.12", "channel-partition-ref": "channel-partition.0.14.12", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.14.12.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.14.12.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.14.13", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.14.13", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.14.13", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.14.13", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.14.13.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.14.13", "channel-partition-ref": "channel-partition.0.14.13", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.14.13.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.14.13.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.14.14", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.14.14", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.14.14", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.14.14", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.14.14.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.14.14", "channel-partition-ref": "channel-partition.0.14.14", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.14.14.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.14.14.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.14.15", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.14.15", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.14.15", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.14.15", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.14.15.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.14.15", "channel-partition-ref": "channel-partition.0.14.15", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.14.15.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.14.15.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.15.0", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.15.0", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.15.0", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.15.0", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.15.0.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.15.0", "channel-partition-ref": "channel-partition.0.15.0", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.15.0.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.15.0.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.15.1", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.15.1", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.15.1", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.15.1", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.15.1.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.15.1", "channel-partition-ref": "channel-partition.0.15.1", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.15.1.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.15.1.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.15.2", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.15.2", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.15.2", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.15.2", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.15.2.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.15.2", "channel-partition-ref": "channel-partition.0.15.2", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.15.2.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.15.2.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.15.3", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.15.3", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.15.3", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.15.3", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.15.3.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.15.3", "channel-partition-ref": "channel-partition.0.15.3", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.15.3.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.15.3.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.15.4", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.15.4", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.15.4", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.15.4", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.15.4.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.15.4", "channel-partition-ref": "channel-partition.0.15.4", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.15.4.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.15.4.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.15.5", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.15.5", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.15.5", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.15.5", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.15.5.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.15.5", "channel-partition-ref": "channel-partition.0.15.5", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.15.5.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.15.5.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.15.6", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.15.6", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.15.6", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.15.6", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.15.6.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.15.6", "channel-partition-ref": "channel-partition.0.15.6", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.15.6.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.15.6.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.15.7", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.15.7", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.15.7", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.15.7", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.15.7.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.15.7", "channel-partition-ref": "channel-partition.0.15.7", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.15.7.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.15.7.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.15.8", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.15.8", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.15.8", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.15.8", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.15.8.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.15.8", "channel-partition-ref": "channel-partition.0.15.8", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.15.8.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.15.8.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.15.9", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.15.9", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.15.9", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.15.9", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.15.9.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.15.9", "channel-partition-ref": "channel-partition.0.15.9", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.15.9.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.15.9.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.15.10", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.15.10", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.15.10", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.15.10", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.15.10.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.15.10", "channel-partition-ref": "channel-partition.0.15.10", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.15.10.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.15.10.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.15.11", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.15.11", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.15.11", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.15.11", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.15.11.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.15.11", "channel-partition-ref": "channel-partition.0.15.11", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.15.11.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.15.11.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.15.12", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.15.12", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.15.12", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.15.12", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.15.12.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.15.12", "channel-partition-ref": "channel-partition.0.15.12", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.15.12.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.15.12.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.15.13", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.15.13", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.15.13", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.15.13", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.15.13.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.15.13", "channel-partition-ref": "channel-partition.0.15.13", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.15.13.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.15.13.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.15.14", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.15.14", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.15.14", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.15.14", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.15.14.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.15.14", "channel-partition-ref": "channel-partition.0.15.14", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.15.14.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.15.14.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.15.15", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.15.15", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.15.15", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.15.15", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.15.15.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.15.15", "channel-partition-ref": "channel-partition.0.15.15", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.15.15.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.15.15.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.16.0", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.16.0", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.16.0", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.16.0", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.16.0.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.16.0", "channel-partition-ref": "channel-partition.0.16.0", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.16.0.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.16.0.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.16.1", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.16.1", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.16.1", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.16.1", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.16.1.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.16.1", "channel-partition-ref": "channel-partition.0.16.1", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.16.1.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.16.1.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.16.2", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.16.2", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.16.2", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.16.2", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.16.2.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.16.2", "channel-partition-ref": "channel-partition.0.16.2", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.16.2.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.16.2.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.16.3", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.16.3", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.16.3", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.16.3", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.16.3.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.16.3", "channel-partition-ref": "channel-partition.0.16.3", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.16.3.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.16.3.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.16.4", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.16.4", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.16.4", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.16.4", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.16.4.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.16.4", "channel-partition-ref": "channel-partition.0.16.4", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.16.4.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.16.4.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.16.5", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.16.5", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.16.5", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.16.5", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.16.5.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.16.5", "channel-partition-ref": "channel-partition.0.16.5", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.16.5.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.16.5.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.16.6", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.16.6", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.16.6", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.16.6", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.16.6.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.16.6", "channel-partition-ref": "channel-partition.0.16.6", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.16.6.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.16.6.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.16.7", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.16.7", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.16.7", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.16.7", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.16.7.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.16.7", "channel-partition-ref": "channel-partition.0.16.7", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.16.7.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.16.7.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.16.8", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.16.8", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.16.8", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.16.8", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.16.8.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.16.8", "channel-partition-ref": "channel-partition.0.16.8", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.16.8.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.16.8.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.16.9", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.16.9", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.16.9", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.16.9", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.16.9.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.16.9", "channel-partition-ref": "channel-partition.0.16.9", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.16.9.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.16.9.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.16.10", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.16.10", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.16.10", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.16.10", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.16.10.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.16.10", "channel-partition-ref": "channel-partition.0.16.10", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.16.10.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.16.10.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.16.11", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.16.11", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.16.11", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.16.11", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.16.11.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.16.11", "channel-partition-ref": "channel-partition.0.16.11", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.16.11.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.16.11.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.16.12", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.16.12", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.16.12", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.16.12", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.16.12.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.16.12", "channel-partition-ref": "channel-partition.0.16.12", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.16.12.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.16.12.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.16.13", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.16.13", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.16.13", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.16.13", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.16.13.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.16.13", "channel-partition-ref": "channel-partition.0.16.13", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.16.13.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.16.13.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.16.14", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.16.14", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.16.14", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.16.14", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.16.14.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.16.14", "channel-partition-ref": "channel-partition.0.16.14", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.16.14.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.16.14.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}, {"name": "gpon.0.16.15", "type": "iana-if-type:gpon", "enabled": true, "xpon-port": {"ont-auto-find-switch": false, "ont-password-renew-interval": "1440", "multicast-encrypt-mode": "disable", "dba-surplus-assign": "disable", "multicast-encrypt-algorithm": "aes128"}}, {"name": "channel-group.0.16.15", "type": "bbf-xpon-if-type:channel-group", "enabled": true, "channel-group": {"polling-period": 100}}, {"name": "channel-partition.0.16.15", "type": "bbf-xpon-if-type:channel-partition", "enabled": true, "channel-partition": {"channel-group-ref": "channel-group.0.16.15", "channel-partition-index": 0, "downstream-fec": true, "closest-onu-distance": 0, "maximum-differential-xpon-distance": 20, "multicast-aes-indicator": false}}, {"name": "channel-pair.0.16.15.gpon", "type": "bbf-xpon-if-type:channel-pair", "enabled": true, "channel-pair": {"channel-group-ref": "channel-group.0.16.15", "channel-partition-ref": "channel-partition.0.16.15", "channel-pair-type": "bbf-xpon-types:gpon"}}, {"name": "channel-termination.0.16.15.gpon", "type": "bbf-xpon-if-type:channel-termination", "enabled": true, "channel-termination": {"channel-pair-ref": "channel-pair.0.16.15.gpon", "channel-termination-type": "bbf-xpon-types:gpon", "laser-switch": true}}]}}