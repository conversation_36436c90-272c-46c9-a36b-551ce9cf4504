/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: st for gmscmd
 * Author: hebaisheng
 * Create: 2023-09-07
 */
#include "gtest/gtest.h"
#include "StartDbServer.h"
#include "InitClt.h"
#include "gmc_internal.h"
#include "tools_st_common.h"
#include "tool_main.h"

#define MAX_CMD_SIZE 1024
#define MAX_KV_VALUE_SIZE (1 * 1024 * 1024)  // 1M
using namespace std;

static const char *g_cfgJson = R"({"max_record_count":100})";
static const char *g_kvTableName = "KvTable01";
static const char *g_updateTableName = "settable";
class StGmCmd : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig(NULL);
        st_clt_init();
        ASSERT_EQ(GMERR_OK, st_connect());
        printf("Start Server!\n");
    }
    static void TearDownTestCase()
    {
        st_disconnect();
        st_clt_uninit();
        ShutDownDbServer();
        GmcDetachAllShmSeg();
        system("ipcrm -a");
        printf("Stop Server!\n");
    }
    virtual void SetUp()
    {
        printf("testCase begin \n");
    }
    virtual void TearDown()
    {
        printf("testCase end \n");
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

int executeCommand(const char *cmd, const char *v1 = NULL, const char *v2 = NULL, const char *v3 = NULL,
    const char *v4 = NULL, const char *v5 = NULL);

int executeCommand(const char *cmd, const char *v1, const char *v2, const char *v3, const char *v4, const char *v5)
{
#if defined RUN_DATACOM_HPE
    printf("[executeCommand] popen can not run in hpe env\n");
    return 0;
#else
    const char *argv[6];
    int argc = 0;
    if (v1 != NULL) {
        ++argc;
        argv[0] = v1;
    }
    if (v2 != NULL) {
        ++argc;
        argv[1] = v2;
    }
    if (v3 != NULL) {
        ++argc;
        argv[2] = v3;
    }
    if (v4 != NULL) {
        ++argc;
        argv[3] = v4;
    }
    if (v5 != NULL) {
        ++argc;
        argv[4] = v5;
    }
    argv[argc] = "\0";

    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error.\n", cmd);
        return -1;
    }
    int ret = -1;
    int start = 0;
    int i, len1, len2 = 0;
    char cmdOutput[4096] = {0};
    while (NULL != fgets(cmdOutput + len2, 4000 - len2, pf)) {
        i = 0;
        len2 = strlen(cmdOutput);
        while (start < argc) {
            len1 = strlen(argv[start]);
            for (; i + len1 < len2; ++i) {
                if (strncmp(cmdOutput + i, argv[start], len1) == 0) {
                    i = i + len1;
                    ++start;
                    if (start == argc) {
                        break;
                    }
                    len1 = strlen(argv[start]);
                }
            }
            if (i + len1 >= len2) {
                break;
            }
        }
        if (start == argc) {
            ret = 0;
            break;
        }
        len1 = strlen(argv[start]);
        for (i = 0; i < len1; ++i) {
            if ((len2 - len1 + i) >= 0) {
                cmdOutput[i] = cmdOutput[len2 - len1 + i];
            }
        }
        cmdOutput[i] = '\0';
        len2 = strlen(cmdOutput);
    }
    if (ret == 0) {
        while (NULL != fgets(cmdOutput, 4000, pf)) {
        }
    }
    if (pclose(pf) == -1) {
        perror("pclose fail");
    }
    pf = NULL;
    if (ret == -1) {
        len2 = strlen(cmdOutput);
        printf("cmd: [errno:%d] %s\n", errno, cmd);
        printf("expect:\n");
        for (i = 0; i < argc; ++i) {
            if (i < start) {
                printf("  [%d] [match succ] %s\n", i, argv[i]);
            } else {
                printf("  [%d] [match fail] %s\n", i, argv[i]);
            }
        }
        printf("output: [len:%d] %s\n", len2, cmdOutput);
    }
    return ret;
#endif
}

TEST_F(StGmCmd, TestOptionHelp)
{
    string args;
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        int helpCnt = 0;
        while (fgets(buffer, 1280, fp) != NULL) {
            str = buffer;
            if (str.find("help information:") != std::string::npos) {
                helpCnt++;
            }
            if (str.find("[-h]") != std::string::npos) {
                helpCnt++;
            }
            if (str.find("[-s]") != std::string::npos) {
                helpCnt++;
            }
            if (str.find("[-c]") != std::string::npos) {
                helpCnt++;
            }
        }
        EXPECT_EQ(helpCnt, 4);
        pclose(fp);
    };
    args = string("gmcmd -h;");
    VerifyViewStr();
}

TEST_F(StGmCmd, TestOptionHelpAll)
{
    string args;
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        bool findFlag = false;
        while (fgets(buffer, 1280, fp) != NULL) {
            str = buffer;
            if (str.find("All commands are currently supported as follows") != std::string::npos) {
                findFlag = true;
            }
        }
        EXPECT_EQ(true, findFlag);
        pclose(fp);
    };
    args = string("gmcmd -c help;");
    VerifyViewStr();
}

TEST_F(StGmCmd, TestOptionHelpNoCmd)
{
    string args;
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        bool findFlag = false;
        while (fgets(buffer, 1280, fp) != NULL) {
            str = buffer;
            if (str.find("Unknown help param \"xxx\"") != std::string::npos) {
                findFlag = true;
            }
        }
        EXPECT_EQ(true, findFlag);
        pclose(fp);
    };
    args = string("gmcmd -c help xxx;");
    VerifyViewStr();
}

TEST_F(StGmCmd, TestHelpExit)
{
    string args;
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        bool findFlag = false;
        while (fgets(buffer, 1280, fp) != NULL) {
            str = buffer;
            if (str.find("Exit the DB client") != std::string::npos) {
                findFlag = true;
            }
        }
        EXPECT_EQ(true, findFlag);
        pclose(fp);
    };
    args = string("gmcmd -c help exit;");
    VerifyViewStr();
}

TEST_F(StGmCmd, TestHelpHelp)
{
    string args;
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        bool findFlag = false;
        while (fgets(buffer, 1280, fp) != NULL) {
            str = buffer;
            if (str.find("Show help information") != std::string::npos) {
                findFlag = true;
            }
        }
        EXPECT_EQ(true, findFlag);
        pclose(fp);
    };
    args = string("gmcmd -c help help;");
    VerifyViewStr();
}

TEST_F(StGmCmd, TestHelpQuit)
{
    string args;
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        bool findFlag = false;
        while (fgets(buffer, 1280, fp) != NULL) {
            str = buffer;
            if (str.find("Quit the DB client") != std::string::npos) {
                findFlag = true;
            }
        }
        EXPECT_EQ(true, findFlag);
        pclose(fp);
    };
    args = string("gmcmd -c help quit;");
    VerifyViewStr();
}

TEST_F(StGmCmd, TestHelpDelete)
{
    string args;
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        bool findFlag = false;
        while (fgets(buffer, 1280, fp) != NULL) {
            str = buffer;
            if (str.find("Delete one tree object") != std::string::npos) {
                findFlag = true;
            }
        }
        EXPECT_EQ(true, findFlag);
        pclose(fp);
    };
    args = string("gmcmd -c help delete;");
    VerifyViewStr();
}

TEST_F(StGmCmd, TestHelpDel)
{
    string args;
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        bool findFlag = false;
        while (fgets(buffer, 1280, fp) != NULL) {
            str = buffer;
            if (str.find("Del kvtable Key-Value") != std::string::npos) {
                findFlag = true;
            }
        }
        EXPECT_EQ(true, findFlag);
        pclose(fp);
    };
    args = string("gmcmd -c help del;");
    VerifyViewStr();
}

TEST_F(StGmCmd, TestHelpUpdate)
{
    string args;
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        bool findFlag = false;
        while (fgets(buffer, 1280, fp) != NULL) {
            str = buffer;
            if (str.find("Update one tree object") != std::string::npos) {
                findFlag = true;
            }
        }
        EXPECT_EQ(true, findFlag);
        pclose(fp);
    };
    args = string("gmcmd -c help update;");
    VerifyViewStr();
}

TEST_F(StGmCmd, TestHelpSet)
{
    string args;
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        bool findFlag = false;
        while (fgets(buffer, 1280, fp) != NULL) {
            str = buffer;
            if (str.find("Set one tree object") != std::string::npos) {
                findFlag = true;
            }
        }
        EXPECT_EQ(true, findFlag);
        pclose(fp);
    };
    args = string("gmcmd -c help set;");
    VerifyViewStr();
}

TEST_F(StGmCmd, TestHelpSelect)
{
    string args;
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        bool findFlag = false;
        while (fgets(buffer, 1280, fp) != NULL) {
            str = buffer;
            if (str.find("Select data with conditions") != std::string::npos) {
                findFlag = true;
            }
        }
        EXPECT_EQ(true, findFlag);
        pclose(fp);
    };
    args = string("gmcmd -c help select;");
    VerifyViewStr();
}

TEST_F(StGmCmd, TestHelpGet)
{
    string args;
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        bool findFlag = false;
        while (fgets(buffer, 1280, fp) != NULL) {
            str = buffer;
            if (str.find("Get kv value with key") != std::string::npos) {
                findFlag = true;
            }
        }
        EXPECT_EQ(true, findFlag);
        pclose(fp);
    };
    args = string("gmcmd -c help get;");
    VerifyViewStr();
}

TEST_F(StGmCmd, TestHelpDrop)
{
    string args;
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        bool findFlag = false;
        while (fgets(buffer, 1280, fp) != NULL) {
            str = buffer;
            if (str.find("Drop one table") != std::string::npos) {
                findFlag = true;
            }
        }
        EXPECT_EQ(true, findFlag);
        pclose(fp);
    };
    args = string("gmcmd -c help drop;");
    VerifyViewStr();
}

TEST_F(StGmCmd, TestHelpTruncate)
{
    string args;
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        bool findFlag = false;
        while (fgets(buffer, 1280, fp) != NULL) {
            str = buffer;
            if (str.find("Truncate one table") != std::string::npos) {
                findFlag = true;
            }
        }
        EXPECT_EQ(true, findFlag);
        pclose(fp);
    };
    args = string("gmcmd -c help truncate;");
    VerifyViewStr();
}

TEST_F(StGmCmd, TestHelpGen)
{
    string args;
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        bool findFlag = false;
        while (fgets(buffer, 1280, fp) != NULL) {
            str = buffer;
            if (str.find("Generate tree objects") != std::string::npos) {
                findFlag = true;
            }
        }
        EXPECT_EQ(true, findFlag);
        pclose(fp);
    };
    args = string("gmcmd -c help gen;");
    VerifyViewStr();
}

TEST_F(StGmCmd, TestCommandDropAndTruncateVertex)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/vertex_schema.gmjson -t VertexTable01");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/VertexTable01.vertexdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 定义一些变量用于查表，查记录
    bool vertexFlag = false;
    int vertexRecordCnt = 0;
    int vertexRow = 0;
    int totalCnt;
    auto ResetVar = [&]() {
        vertexFlag = false;
        vertexRecordCnt = 0;
        vertexRow = 0;
        totalCnt = 0;
    };
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        while (fgets(buffer, 1280, fp) != NULL) {
            totalCnt++;
            str = buffer;
            if (str.find("table: VertexTable01") != std::string::npos) {
                vertexFlag = true;
                vertexRow = totalCnt;
            }
            if (str.find("record count:") != std::string::npos) {
                size_t len = str.size();
                string numStr = str.substr(len - 2, len - 1);
                int recordCnt = (int32_t)std::stoi(numStr);
                if (abs(totalCnt - vertexRow) == 1) {
                    vertexRecordCnt = recordCnt;
                }
            }
        }
        printf("vertexFlag=%" PRId32 ", vertexRecordCnt=%" PRId32 " \n", vertexFlag, vertexRecordCnt);
        pclose(fp);
    };
    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ResetVar();
    VerifyViewStr();
    EXPECT_TRUE(vertexFlag);
    EXPECT_EQ(vertexRecordCnt, 4);  // 预期有4条表记录
    // 清空表记录
    args = string("gmcmd -c \"truncate table VertexTable01\";");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 清空后查询表记录，此时应该没有记录了
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ResetVar();
    VerifyViewStr();
    EXPECT_TRUE(vertexFlag);
    EXPECT_EQ(vertexRecordCnt, 0);
    // 删除表
    args = string("gmcmd -c drop table VertexTable01;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 删除表后，应该找不到表
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    vertexFlag = false;
    VerifyViewStr();
    EXPECT_FALSE(vertexFlag);
}

TEST_F(StGmCmd, TestCommandDropAndTruncateKvTable)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcKvCreateTable(stmt, g_kvTableName, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, g_kvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    // 添加记录
    char key[] = "testName";
    int32_t value = 20;
    uint32_t keyLen = strlen(key) + 1;
    ret = GmcKvSet(stmt, key, keyLen, &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 此时记录存在
    bool isExist = true;
    ret = GmcKvIsExist(stmt, key, keyLen, &isExist);
    EXPECT_EQ(true, isExist);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, count);
    // 清空表记录
    string args = string("gmcmd -c truncate kvtable KvTable01;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // truncate后记录不存在
    isExist = true;
    ret = GmcKvIsExist(stmt, key, keyLen, &isExist);
    EXPECT_EQ(false, isExist);
    EXPECT_EQ(GMERR_OK, ret);
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, count);
    // 删除表
    args = string("gmcmd -c drop kvtable KvTable01;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    bool kvFlag = false;
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        while (fgets(buffer, 1280, fp) != NULL) {
            str = buffer;
            if (str.find("table_name: KvTable01") != std::string::npos) {
                kvFlag = true;
            }
        }
        pclose(fp);
    };
    // 删除表后，应该找不到表
    args = string("gmsysview -q V\\$STORAGE_KV_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    VerifyViewStr();
    EXPECT_FALSE(kvFlag);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestDeleteVertexLabel)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/VertexTable02.gmjson -t VertexTable02");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/VertexTable02.vertexdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 定义一些变量用于查表，查记录
    bool vertexFlag = false;
    int vertexRecordCnt = 0;
    int vertexRow = 0;
    int totalCnt;
    auto ResetVar = [&]() {
        vertexFlag = false;
        vertexRecordCnt = 0;
        vertexRow = 0;
        totalCnt = 0;
    };
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        while (fgets(buffer, 1280, fp) != NULL) {
            totalCnt++;
            str = buffer;
            if (str.find("table: VertexTable02") != std::string::npos) {
                vertexFlag = true;
                vertexRow = totalCnt;
            }
            if (str.find("record count:") != std::string::npos) {
                size_t len = str.size();
                string numStr = str.substr(len - 2, len - 1);
                int recordCnt = (int32_t)std::stoi(numStr);
                if (abs(totalCnt - vertexRow) == 1) {
                    vertexRecordCnt = recordCnt;
                }
            }
        }
        printf("vertexFlag=%" PRId32 ", vertexRecordCnt=%" PRId32 " \n", vertexFlag, vertexRecordCnt);
        pclose(fp);
    };
    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ResetVar();
    VerifyViewStr();
    EXPECT_TRUE(vertexFlag);
    EXPECT_EQ(vertexRecordCnt, 4);  // 预期有4条表记录
    // 条件删除表记录
    args = string("gmcmd -c delete from VertexTable02 where F1=0;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 删除后查询表记录，此时应该没有记录了
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ResetVar();
    VerifyViewStr();
    EXPECT_TRUE(vertexFlag);
    EXPECT_EQ(vertexRecordCnt, 3);  // 删除1条，剩下3条
    // 条件删除表记录条件1
    args = string("gmcmd -c delete from VertexTable02 where F3=0 and F4=0;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ResetVar();
    VerifyViewStr();
    EXPECT_TRUE(vertexFlag);
    EXPECT_EQ(vertexRecordCnt, 3);  // 删除2条，剩下2条
    // 条件删除表记录条件2
    args = string("gmcmd -c delete from VertexTable02 where F1=1 or F3=1;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ResetVar();
    VerifyViewStr();
    EXPECT_TRUE(vertexFlag);
    EXPECT_EQ(vertexRecordCnt, 2);  // 删除1条，剩下2条
    char command[MAX_CMD_SIZE] = {0};
    char cmdOutputReal[64] = {0};
    snprintf(command, MAX_CMD_SIZE,
        "gmcmd -c delete from Vertex Table02 where F1=1 or F3=1 | grep -E 'Delete "
        "are not correct' | wc -l");
    printf("%s\n", command);
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error.\n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutputReal, 64, pf)) {
    };
    fclose(pf);
    uint32_t errCnt = atoi(cmdOutputReal);
    EXPECT_EQ(1u, errCnt);
    // 删除表
    args = string("gmcmd -c drop table VertexTable02;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StGmCmd, TestDeleteVertexLabel_Array)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/Vertex_array.gmjson -t Vertex_array");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/Vertex_array.vertexdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 以array字段为filter条件,更新数据
    args = string("gmcmd -c update Vertex_array set Vertex_array.F1=100 where "
                  "Vertex_array.T4/V0=1;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcPrepareStmtByLabelName(stmt, "Vertex_array", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "Vertex_array.F0 = 1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value = 0;
    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(100u, value);

    bool vertexFlag = false;
    int vertexRecordCnt = 0;
    int vertexRow = 0;
    int totalCnt;
    auto ResetVar = [&]() {
        vertexFlag = false;
        vertexRecordCnt = 0;
        vertexRow = 0;
        totalCnt = 0;
    };
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        while (fgets(buffer, 1280, fp) != NULL) {
            totalCnt++;
            str = buffer;
            if (str.find("table: Vertex_array") != std::string::npos) {
                vertexFlag = true;
                vertexRow = totalCnt;
            }
            if (str.find("record count:") != std::string::npos) {
                size_t len = str.size();
                string numStr = str.substr(len - 2, len - 1);
                int recordCnt = (int32_t)std::stoi(numStr);
                if (abs(totalCnt - vertexRow) == 1) {
                    vertexRecordCnt = recordCnt;
                }
            }
        }
        pclose(fp);
    };
    // 以array字段为filter条件,删除数据
    args = string("gmcmd -c delete from Vertex_array where Vertex_array.T4/V0=1;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ResetVar();
    VerifyViewStr();
    EXPECT_EQ(vertexRecordCnt, 6);  // 删除1条，剩下6条

    // 以vector字段为filter条件,删除数据
    args = string("gmcmd -c delete from Vertex_array where Vertex_array.T3/V0=8;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ResetVar();
    VerifyViewStr();
    EXPECT_EQ(vertexRecordCnt, 4);  // 删除2条，剩下4条

    // 以fix_array字段为filter条件,删除数据
    args = string("gmcmd -c delete from Vertex_array where Vertex_array.T1/T2/A0=1004;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ResetVar();
    VerifyViewStr();
    EXPECT_EQ(vertexRecordCnt, 1);  // 删除3条，剩下1条

    // 删除表
    args = string("gmcmd -c drop table Vertex_array;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StGmCmd, TestDeleteKvTable)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcKvCreateTable(stmt, g_kvTableName, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, g_kvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    // 添加记录
    char key[] = "testName";
    int32_t value = 20;
    uint32_t keyLen = strlen(key) + 1;
    ret = GmcKvSet(stmt, key, keyLen, &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 此时记录存在
    bool isExist = true;
    ret = GmcKvIsExist(stmt, key, keyLen, &isExist);
    EXPECT_EQ(true, isExist);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, count);
    // 删除对应key=testName
    string args = string("gmcmd -c del kvtable KvTable01 testName;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, count);
    // 删除表
    args = string("gmcmd -c drop kvtable KvTable01;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabel_uint8)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/UpdateVertex_uint8.vertexdata "
                  "-t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint32_t numValue = 0;
        uint32_t numLen = sizeof(uint8_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f4", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt + 1, numValue);
    }
    args = string("gmcmd -c update settable   set      "
                  "settable.a2/b1/c1/f4 = 100 where settable.a0\\<=2;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c 'update settable   set      "
                  "settable.a2/b1/c1/f4   =   255   where   settable.a0>=5';");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint32_t numValue = 0;
        uint32_t numLen = sizeof(uint8_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f4", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt <= 1) {
            EXPECT_EQ(100u, numValue);
        } else if (cnt >= 4) {
            EXPECT_EQ(255u, numValue);
        } else {
            EXPECT_EQ(cnt + 1, numValue);
        }
    }
    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabel_uchar)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/UpdateVertex_uchar.vertexdata "
                  "-t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint32_t numValue = 0;
        uint32_t numLen = sizeof(uint8_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f2", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt + 1, numValue);
    }
    args = string("gmcmd -c update settable   set      "
                  "settable.a2/b1/c1/f2 = 1 where settable.a0\\<=2;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c 'update settable   set      "
                  "settable.a2/b1/c1/f2   =   2   where   settable.a0>=5';");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint32_t numValue = 0;
        uint32_t numLen = sizeof(uint8_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f2", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt <= 1) {
            EXPECT_EQ(49u, numValue);
        } else if (cnt >= 4) {
            EXPECT_EQ(50u, numValue);
        } else {
            EXPECT_EQ(cnt + 1, numValue);
        }
    }
    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabel_char)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/UpdateVertex_char.vertexdata "
                  "-t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint32_t numValue = 0;
        uint32_t numLen = sizeof(uint8_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f1", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt + 1, numValue);
    }
    // 通过交互式进行设置
    system("sh gmcmd_interact_set.sh");
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint32_t numValue = 0;
        uint32_t numLen = sizeof(uint8_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f1", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt <= 1) {
            EXPECT_EQ(49u, numValue);
        } else if (cnt >= 4) {
            EXPECT_EQ(50u, numValue);
        } else {
            EXPECT_EQ(cnt + 1, numValue);
        }
    }
    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabel_partition)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertexWithPartition.gmjson -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/UpdateVertex_partition.vertexdata "
                  "-t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint32_t numValue = 0;
        uint32_t numLen = sizeof(uint8_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f2", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt + 1, numValue);
    }
    args = string("gmcmd -c update settable   set      "
                  "settable.a3 = 8 where settable.a0\\<=2;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c 'update settable   set      "
                  "settable.a3   =   9   where   settable.a0>=5';");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint32_t numValue = 0;
        uint32_t numLen = sizeof(uint8_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f2", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt + 1, numValue);
    }
    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabel_uint16)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/UpdateVertex_uint16.vertexdata "
                  "-t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint32_t numValue = 0;
        uint32_t numLen = sizeof(uint16_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f6", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt + 1, numValue);
    }
    args = string("gmcmd -c update settable   set      "
                  "settable.a2/b1/c1/f6 = 256 where settable.a0\\<=2;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c 'update settable   set      "
                  "settable.a2/b1/c1/f6   =   65535   where   settable.a0>=5';");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint32_t numValue = 0;
        uint32_t numLen = sizeof(uint16_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f6", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt <= 1) {
            EXPECT_EQ(256u, numValue);
        } else if (cnt >= 4) {
            EXPECT_EQ(65535u, numValue);
        } else {
            EXPECT_EQ(cnt + 1, numValue);
        }
    }
    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabel_int16)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/UpdateVertex_int16.vertexdata "
                  "-t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7, cnt);
            break;
        }
        int32_t numValue = 0;
        uint32_t numLen = sizeof(int16_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f5", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt + 1, numValue);
    }
    args = string("gmcmd -c update settable   set      "
                  "settable.a2/b1/c1/f5 = 100 where settable.a0\\<=2;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c 'update settable   set      "
                  "settable.a2/b1/c1/f5   =   32767   where   settable.a0>=5';");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7, cnt);
            break;
        }
        int32_t numValue = 0;
        uint32_t numLen = sizeof(int16_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f5", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt <= 1) {
            EXPECT_EQ(100, numValue);
        } else if (cnt >= 4) {
            EXPECT_EQ(32767, numValue);
        } else {
            EXPECT_EQ(cnt + 1, numValue);
        }
    }
    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabel_uint32)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/UpdateVertex_uint32.vertexdata "
                  "-t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint32_t numValue = 0;
        uint32_t numLen = sizeof(uint32_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f8", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt + 1, numValue);
    }
    args = string("gmcmd -c update settable   set      "
                  "settable.a2/b1/c1/f8 = 65536 where settable.a0\\<=2;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c 'update settable   set      "
                  "settable.a2/b1/c1/f8   =   4294967295   where   settable.a0>=5';");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint32_t numValue = 0;
        uint32_t numLen = sizeof(uint32_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f8", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt <= 1) {
            EXPECT_EQ(65536u, numValue);
        } else if (cnt >= 4) {
            EXPECT_EQ(4294967295u, numValue);
        } else {
            EXPECT_EQ(cnt + 1, numValue);
        }
    }
    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabel_int32)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/UpdateVertex_int32.vertexdata "
                  "-t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7, cnt);
            break;
        }
        int32_t numValue = 0;
        uint32_t numLen = sizeof(int32_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f7", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt + 1, numValue);
    }
    args = string("gmcmd -c update settable   set      "
                  "settable.a2/b1/c1/f7 = 32768 where settable.a0\\<=2;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c 'update settable   set      "
                  "settable.a2/b1/c1/f7   =   2147483647   where   settable.a0>=5';");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7, cnt);
            break;
        }
        int32_t numValue = 0;
        uint32_t numLen = sizeof(int32_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f7", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt <= 1) {
            EXPECT_EQ(32768, numValue);
        } else if (cnt >= 4) {
            EXPECT_EQ(2147483647, numValue);
        } else {
            EXPECT_EQ(cnt + 1, numValue);
        }
    }
    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabel_int64)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/UpdateVertex_int64.vertexdata "
                  "-t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7, cnt);
            break;
        }
        int64_t numValue = 0;
        uint32_t numLen = sizeof(int64_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f10", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt + 1, numValue);
    }
    args = string("gmcmd -c update settable   set      "
                  "settable.a2/b1/c1/f10 = 32768 where settable.a0\\<=2;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c 'update settable   set      "
                  "settable.a2/b1/c1/f10   =   2147483647   where   settable.a0>=5';");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7, cnt);
            break;
        }
        int64_t numValue = 0;
        uint32_t numLen = sizeof(int64_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f10", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt <= 1) {
            EXPECT_EQ(32768, numValue);
        } else if (cnt >= 4) {
            EXPECT_EQ(2147483647, numValue);
        } else {
            EXPECT_EQ(cnt + 1, numValue);
        }
    }
    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabel_bitfield8)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f "
                  "./gmcmd_test_file/UpdateVertex_bitfield8.vertexdata -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint32_t numValue = 0;
        uint32_t numLen = sizeof(uint8_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f18", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt + 1, numValue);
        ret = GmcNodeGetPropertyByName(node, "f19", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ((cnt + 1), numValue);
    }
    args = string("gmcmd -c update  settable   set      "
                  "settable.a2/b1/c1/f18=6 where settable.a0\\<=2;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c update  settable   set      "
                  "settable.a2/b1/c1/f19=3 where settable.a0\\>=5;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint32_t numValue = 0;
        uint32_t numLen = sizeof(uint8_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f18", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt <= 1) {
            EXPECT_EQ(6u, numValue);
        } else {
            EXPECT_EQ(cnt + 1, numValue);
        }
        ret = GmcNodeGetPropertyByName(node, "f19", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt >= 4) {
            EXPECT_EQ(3u, numValue);
        } else {
            EXPECT_EQ((cnt + 1), numValue);
        }
    }
    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabel_bitfield16)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f "
                  "./gmcmd_test_file/UpdateVertex_bitfield16.vertexdata -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint32_t numValue = 0;
        uint32_t numLen = sizeof(uint16_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f20", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt + 1, numValue);
        ret = GmcNodeGetPropertyByName(node, "f21", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ((cnt + 1) * 11, numValue);
    }
    args = string("gmcmd -c update  settable   set      "
                  "settable.a2/b1/c1/f20=10 where settable.a0\\<=2;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c update  settable   set      "
                  "settable.a2/b1/c1/f21=35 where settable.a0\\>=5;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c update  settable   set      "
                  "settable.a2/b1/c1/f21=x where settable.a0\\>=5;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    system("gmsysview record settable");
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint32_t numValue = 0;
        uint32_t numLen = sizeof(uint16_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f20", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt <= 1) {
            EXPECT_EQ(10u, numValue);
        } else {
            EXPECT_EQ(cnt + 1, numValue);
        }
        ret = GmcNodeGetPropertyByName(node, "f21", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt >= 4) {
            EXPECT_EQ(35u, numValue);
        } else {
            EXPECT_EQ((cnt + 1) * 11, numValue);
        }
    }
    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabel_bitfield32)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f "
                  "./gmcmd_test_file/UpdateVertex_bitfield32.vertexdata -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint32_t numValue = 0;
        uint32_t numLen = sizeof(uint32_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f22", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt + 1, numValue);
        ret = GmcNodeGetPropertyByName(node, "f23", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ((cnt + 1) * 11, numValue);
    }
    args = string("gmcmd -c update  settable   set      "
                  "settable.a2/b1/c1/f22=10 where settable.a0\\<=2;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c update  settable   set      "
                  "settable.a2/b1/c1/f23=35 where settable.a0\\>=5;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint32_t numValue = 0;
        uint32_t numLen = sizeof(uint32_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f22", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt <= 1) {
            EXPECT_EQ(10u, numValue);
        } else {
            EXPECT_EQ(cnt + 1, numValue);
        }
        ret = GmcNodeGetPropertyByName(node, "f23", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt >= 4) {
            EXPECT_EQ(35u, numValue);
        } else {
            EXPECT_EQ((cnt + 1) * 11, numValue);
        }
    }
    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabel_bitfield64)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f "
                  "./gmcmd_test_file/UpdateVertex_bitfield64.vertexdata -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint64_t numValue = 0;
        uint32_t numLen = sizeof(uint64_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f24", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(cnt + 1, numValue);
        ret = GmcNodeGetPropertyByName(node, "f25", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ((cnt + 1) * 11, numValue);
    }
    args = string("gmcmd -c update  settable   set      "
                  "settable.a2/b1/c1/f24=10 where settable.a0\\<=2;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c update  settable   set      "
                  "settable.a2/b1/c1/f25=35 where settable.a0\\>=5;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        uint64_t numValue = 0;
        uint32_t numLen = sizeof(uint64_t);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f24", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt <= 1) {
            EXPECT_EQ(10u, numValue);
        } else {
            EXPECT_EQ(cnt + 1, numValue);
        }
        ret = GmcNodeGetPropertyByName(node, "f25", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt >= 4) {
            EXPECT_EQ(35u, numValue);
        } else {
            EXPECT_EQ((cnt + 1) * 11, numValue);
        }
    }
    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabel_boolean)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f "
                  "./gmcmd_test_file/UpdateVertex_boolean.vertexdata -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        bool numValue = 0;
        uint32_t numLen = sizeof(bool);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f9", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(true, numValue);
    }
    args = string("gmcmd -c update settable   set      "
                  "settable.a2/b1/c1/f9 = 0 where settable.a0\\<=2;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c update settable   set      "
                  "settable.a2/b1/c1/f9 = false where settable.a0\\>=5;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        bool numValue = 0;
        uint32_t numLen = sizeof(bool);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f9", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt <= 1) {
            EXPECT_EQ(false, numValue);
        } else if (cnt >= 4) {
            EXPECT_EQ(false, numValue);
        } else {
            EXPECT_EQ(true, numValue);
        }
    }
    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabel_double)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/UpdateVertex_double.vertexdata "
                  "-t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        double numValue = 0;
        uint32_t numLen = sizeof(double);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f13", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("%.14f\n", numValue);
        EXPECT_EQ(cnt + 1.18446744073709, numValue);
    }
    args = string("gmcmd -c update settable   set      "
                  "settable.a2/b1/c1/f13=15.1844674407370 where settable.a0\\<=2;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c update settable   set      "
                  "settable.a2/b1/c1/f13=35.1844674407370 where settable.a0\\>=5;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        double numValue = 0;
        uint32_t numLen = sizeof(double);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f13", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("%.14f\n", numValue);
        if (cnt <= 1) {
            EXPECT_EQ(15.1844674407370, numValue);
        } else if (cnt >= 4) {
            EXPECT_EQ(35.1844674407370, numValue);
        } else {
            EXPECT_EQ(cnt + 1.18446744073709, numValue);
        }
    }
    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabel_float)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/UpdateVertex_float.vertexdata "
                  "-t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        float numValue = 0;
        uint32_t numLen = sizeof(float);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f12", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("%.14f\n", numValue);
        EXPECT_EQ(cnt + 1.18446 + (numValue - (cnt + 1.18446)), numValue);
    }
    args = string("gmcmd -c update settable   set      "
                  "settable.a2/b1/c1/f12=15.18446 where settable.a0\\<=2;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c update settable   set      "
                  "settable.a2/b1/c1/f12=35.18446 where settable.a0\\>=5;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        float numValue = 0;
        uint32_t numLen = sizeof(float);
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f12", &numValue, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        printf("%.14f\n", numValue);
        if (cnt <= 1) {
            EXPECT_EQ(15.18446 + (numValue - 15.18446), numValue);
        } else if (cnt >= 4) {
            EXPECT_EQ(35.18446 + (numValue - 35.18446), numValue);
        } else {
            EXPECT_EQ(cnt + 1.18446 + (numValue - (cnt + 1.18446)), numValue);
        }
    }
    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabel_bytes)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    ASSERT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/UpdateVertex_bytes.vertexdata "
                  "-t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char bytesStr[10] = "11111111";
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        char value[10];
        char cmpStr[10] = {0};
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f17ValueLen = 0;
        ret = GmcNodeGetPropertySizeByName(node, "f17", &f17ValueLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f17", &value, f17ValueLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint8_t i = 0; i < f17ValueLen; i++) {
            sprintf_s(cmpStr, 30, "%s%0x", cmpStr, value[i]);
        }
        EXPECT_STREQ(cmpStr, bytesStr);
    }
    args = string("gmcmd -c update  settable   set      "
                  "settable.a2/b1/c1/f17=0xabcdef77 where settable.a0\\<=2;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c update settable   set      "
                  "settable.a2/b1/c1/f17=0Xabcdefff where settable.a0\\>=5;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        char value[10];
        char cmpStr[30] = {0};
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f17ValueLen = 0;
        ret = GmcNodeGetPropertySizeByName(node, "f17", &f17ValueLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f17", &value, f17ValueLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint8_t i = 0; i < f17ValueLen; i++) {
            // x86 suse下会按照int打印
            sprintf_s(cmpStr, sizeof(cmpStr), "%s%2x", cmpStr, value[i] & 0xff);
        }
        if (cnt <= 1) {
            EXPECT_STREQ("abcdef77", cmpStr);
        } else if (cnt >= 4) {
            EXPECT_STREQ("abcdefff", cmpStr);
        } else {
            EXPECT_STREQ(bytesStr, cmpStr);
        }
    }
    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabel_fix)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    ASSERT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/UpdateVertex_bytes.vertexdata "
                  "-t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // fixed字段更新长度不对，报错
    args = string("gmcmd -c update  settable   set      "
                  "settable.a2/b1/c1/f16=222 where settable.a0\\<=2;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c update settable   set      "
                  "settable.a2/b1/c1/f16=1111111111 where settable.a0\\>=5;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        char value[11];
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t f16ValueLen = 0;
        ret = GmcNodeGetPropertySizeByName(node, "f16", &f16ValueLen);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(f16ValueLen, 10u);
        const char *f16DefaultValue = "ffffffffff";
        const char *f16Value = "1111111111";
        ret = GmcNodeGetPropertyByName(node, "f16", &value, f16ValueLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        value[10] = '\0';
        if (cnt <= 1) {
            EXPECT_EQ(strcmp(value, f16DefaultValue), 0);
        } else if (cnt >= 4) {
            EXPECT_EQ(strcmp(value, f16Value), 0);
        } else {
            EXPECT_EQ(strcmp(value, f16DefaultValue), 0);
        }
    }
    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabel_time)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/UpdateVertex_time.vertexdata "
                  "-t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef ARM32
    char bytesStr[10] = "cf96ef5d";
#else
    char bytesStr[10] = "4f26ef5d";
#endif
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        char value[30];
        char cmpStr[30] = {0};
        uint32_t numLen = 30;
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f14", &value, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint8_t i = 0; i < strlen(value); i++) {
            sprintf_s(cmpStr, 30, "%s%0x", cmpStr, value[i] & 0xff);
        }
        // 4f26ef5d is the timestap of 2019-12-10 12:59:59
        EXPECT_STREQ(cmpStr, bytesStr);
    }
    args = string("gmcmd -c update settable   set      "
                  "settable.a2/b1/c1/f14=2019-08-26 12:59:59 where settable.a0\\<=2;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c update settable   set      "
                  "settable.a2/b1/c1/f14=2037-08-26 12:59:59 where settable.a0\\>=5;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 <= 7");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        char value[30];
        char cmpStr[30] = {0};
        uint32_t numLen = 30;
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f14", &value, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint8_t i = 0; i < strlen(value); i++) {
            sprintf_s(cmpStr, 30, "%s%0x", cmpStr, value[i] & 0xff);
        }
        if (cnt <= 1) {
            // 4f67635d is the timestap of 2019-08-26 12:59:59
#ifdef ARM32
            EXPECT_STREQ("cfd7635d", cmpStr);
#else
            EXPECT_STREQ("4f67635d", cmpStr);
#endif
        } else if (cnt >= 4) {
            // cf9d3f7f is the timestap of 2037-08-26 12:59:59
#ifdef ARM32
            EXPECT_STREQ("4fe407f", cmpStr);
#else
            EXPECT_STREQ("cf9d3f7f", cmpStr);
#endif
        } else {
            // 4f26ef5d is the timestap of 2019-12-10 12:59:59
            EXPECT_STREQ(bytesStr, cmpStr);
        }
    }

    // 更新time字段, 使用单引号(‘)将”%Y-%m-%d %H:%M:%S”格式字符串包含，更新成功
    args = string("gmcmd -c update settable   set      "
                  "settable.a2/b1/c1/f14=\\'2019-08-26 12:59:59\\' where settable.a0\\>=5;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t cnt = 0;; ++cnt) {
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            EXPECT_EQ(7u, cnt);
            break;
        }
        char value[30];
        char cmpStr[30] = {0};
        uint32_t numLen = 30;
        bool isNull = false;
        GmcNodeT *node = NULL;
        ret = GmcGetChildNode(stmt, "a2/b1/c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, "f14", &value, numLen, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint8_t i = 0; i < strlen(value); i++) {
            sprintf_s(cmpStr, 30, "%s%0x", cmpStr, value[i] & 0xff);
        }
        if (cnt <= 1 || cnt >= 4) {
            // 4f67635d is the timestap of 2019-08-26 12:59:59
#ifdef ARM32
            EXPECT_STREQ("cfd7635d", cmpStr);
#else
            EXPECT_STREQ("4f67635d", cmpStr);
#endif
        } else {
            // 4f26ef5d is the timestap of 2019-12-10 12:59:59
            EXPECT_STREQ(bytesStr, cmpStr);
        }
    }

    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestUpdateVertexLabelWithTimeFilter)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/UpdateVertex_time2.vertexdata "
                  "-t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 以time字段为filter条件,更新数据
    args = string("gmcmd -c update settable set settable.a1=8 where "
                  "settable.a2/b1/c1/f14=\\'1970-01-01 08:00:1\\';");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 = 1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value = 0;
    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt, "a1", &value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(8u, value);

    bool vertexFlag = false;
    int vertexRecordCnt = 0;
    int vertexRow = 0;
    int totalCnt;
    auto ResetVar = [&]() {
        vertexFlag = false;
        vertexRecordCnt = 0;
        vertexRow = 0;
        totalCnt = 0;
    };
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        while (fgets(buffer, 1280, fp) != NULL) {
            totalCnt++;
            str = buffer;
            if (str.find("table: settable") != std::string::npos) {
                vertexFlag = true;
                vertexRow = totalCnt;
            }
            if (str.find("record count:") != std::string::npos) {
                size_t len = str.size();
                string numStr = str.substr(len - 2, len - 1);
                int recordCnt = (int32_t)std::stoi(numStr);
                if (abs(totalCnt - vertexRow) == 1) {
                    vertexRecordCnt = recordCnt;
                }
            }
        }
        pclose(fp);
    };
    // 以time字段为filter条件,删除数据
    args = string("gmcmd -c delete from settable where "
                  "settable.a2/b1/c1/f14\\>=\\'1970-01-01 08:00:4\\';");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ResetVar();
    VerifyViewStr();
    EXPECT_EQ(vertexRecordCnt, 3);  // 删除1条，剩下6条

    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

// 以string字段为filter条件，做更新及删除
TEST_F(StGmCmd, TestUpdateVertexLabelWithStringFilter)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/VertexTable02.gmjson -t VertexTable02");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/VertexTable02.vertexdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 更新string类型的字段, 用单引号将字符串包含时，单引号不会被解析成字符串的一部分
    args = string("gmcmd -c update VertexTable02 set F2=\\'str5\\' where F1=1;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcPrepareStmtByLabelName(stmt, "VertexTable02", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "VertexTable02.F1=1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    char value[30];
    bool isNull;
    char bytesStr[10] = "str5";
    uint32_t numLen = 10;
    ret = GmcGetVertexPropertyByName(stmt, "F2", &value, numLen, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_STREQ(value, bytesStr);

    bool vertexFlag = false;
    int vertexRecordCnt = 0;
    int vertexRow = 0;
    int totalCnt;
    auto ResetVar = [&]() {
        vertexFlag = false;
        vertexRecordCnt = 0;
        vertexRow = 0;
        totalCnt = 0;
    };
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        while (fgets(buffer, 1280, fp) != NULL) {
            totalCnt++;
            str = buffer;
            if (str.find("table: VertexTable02") != std::string::npos) {
                vertexFlag = true;
                vertexRow = totalCnt;
            }
            if (str.find("record count:") != std::string::npos) {
                size_t len = str.size();
                string numStr = str.substr(len - 2, len - 1);
                int recordCnt = (int32_t)std::stoi(numStr);
                if (abs(totalCnt - vertexRow) == 1) {
                    vertexRecordCnt = recordCnt;
                }
            }
        }
        pclose(fp);
    };
    // 以string字段为filter条件,删除数据
    args = string("gmcmd -c delete from VertexTable02 where F2=\\'str1\\';");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ResetVar();
    VerifyViewStr();
    EXPECT_EQ(vertexRecordCnt, 3);  // 删除1条，剩下3条

    // 删除表
    args = string("gmcmd -c drop table VertexTable02;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestDeleteOrUpdateArrayVertex)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/UpdateVertex.gmjson -t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/UpdateVertex_time2.vertexdata "
                  "-t settable");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 以time字段为filter条件,更新数据
    args = string("gmcmd -c update settable set settable.a1=8 where "
                  "settable.a2/b1/c1/f14\\>=\\'1970-01-01 08:00:1\\';");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcPrepareStmtByLabelName(stmt, g_updateTableName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "settable.a0 = 1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value = 0;
    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt, "a1", &value, sizeof(uint64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(8u, value);

    bool vertexFlag = false;
    int vertexRecordCnt = 0;
    int vertexRow = 0;
    int totalCnt;
    auto ResetVar = [&]() {
        vertexFlag = false;
        vertexRecordCnt = 0;
        vertexRow = 0;
        totalCnt = 0;
    };
    auto VerifyViewStr = [&]() {
        char buffer[1280] = {0};
        std::string str;
        FILE *fp = popen(args.c_str(), "r");
        EXPECT_EQ(true, fp != NULL);
        while (fgets(buffer, 1280, fp) != NULL) {
            totalCnt++;
            str = buffer;
            if (str.find("table: settable") != std::string::npos) {
                vertexFlag = true;
                vertexRow = totalCnt;
            }
            if (str.find("record count:") != std::string::npos) {
                size_t len = str.size();
                string numStr = str.substr(len - 2, len - 1);
                int recordCnt = (int32_t)std::stoi(numStr);
                if (abs(totalCnt - vertexRow) == 1) {
                    vertexRecordCnt = recordCnt;
                }
            }
        }
        pclose(fp);
    };
    // 以time字段为filter条件,删除数据
    args = string("gmcmd -c delete from settable where settable.a2/b1/c1/f14\\>=\\'1970-01-01 08:00:7\\';");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ResetVar();
    VerifyViewStr();
    EXPECT_EQ(vertexRecordCnt, 6);  // 删除1条，剩下6条

    // 删除表
    args = string("gmcmd -c drop table settable;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestSetKvTable01)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    Status ret = GmcKvCreateTable(stmt, g_kvTableName, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, g_kvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    // 添加记录
    char key[] = "updateKv";
    int32_t value = 20;
    uint32_t keyLen = strlen(key) + 1;
    ret = GmcKvSet(stmt, key, keyLen, &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 此时记录存在
    bool isExist = true;
    ret = GmcKvIsExist(stmt, key, keyLen, &isExist);
    EXPECT_EQ(true, isExist);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, count);
    // 使用gmcmd工具更新kv表,已存在updateKv字段
    string args = string("gmcmd -c set kvtable KvTable01 updateKv 30;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcKvIsExist(stmt, key, keyLen, &isExist);
    EXPECT_EQ(true, isExist);
    EXPECT_EQ(GMERR_OK, ret);
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, count);
    // 使用gmcmd工具更新kv表,不存在notExist字段
    args = string("gmcmd -c set kvtable KvTable01 notExist 30;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    char key2[] = "notExist";
    uint32_t keyLen2 = strlen(key2) + 1;
    ret = GmcKvIsExist(stmt, key2, keyLen2, &isExist);
    EXPECT_EQ(true, isExist);
    EXPECT_EQ(GMERR_OK, ret);
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, count);
    // 删除表
    args = string("gmcmd -c drop kvtable KvTable01;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

void VerifyViewStr(char *strcmd, const char *expectStr, uint32_t expectCnt)
{
    char buffer[1280] = {0};
    std::string str;
    uint32_t totalCnt = 0;
    FILE *fp = popen(strcmd, "r");
    EXPECT_EQ(true, fp != nullptr);
    if (fgets(buffer, 1280, fp) == NULL) {
        printf("buf is null\n");
    }
    while (fgets(buffer, 1280, fp) != NULL) {
        str = buffer;
        if (str.find(expectStr) != std::string::npos) {
            totalCnt++;
        }
    }
    EXPECT_EQ(totalCnt, expectCnt);
    pclose(fp);
}

TEST_F(StGmCmd, TestSetKvTable02)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    const char *kvName = "haha";
    Status ret = GmcKvCreateTable(stmt, kvName, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    // 添加记录
    char key[] = "hbs";
    int32_t value = 20;
    uint32_t keyLen = strlen(key) + 1;
    ret = GmcKvSet(stmt, key, keyLen, &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 此时记录存在
    bool isExist = true;
    ret = GmcKvIsExist(stmt, key, keyLen, &isExist);
    EXPECT_EQ(true, isExist);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, count);

    // 通过工具查看value
    const uint32_t cmdLen = 1024;
    char strcmd[cmdLen] = {0};
    memset_s(strcmd, cmdLen, 0x00, cmdLen);
    system("gmcmd -c get kvtable haha hbs");
    snprintf_s(strcmd, cmdLen, cmdLen - 1, "gmcmd -c 'get kvtable %s %s'", kvName, key);
    VerifyViewStr(strcmd, "Read Value", 1);
    VerifyViewStr(strcmd, "0x14", 1);  // 写入20，以16进制打印

    // 使用gmcmd工具更新kv表,已存在hbs字段
    string args = string("gmcmd -c set kvtable haha hbs 8899;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcKvIsExist(stmt, key, keyLen, &isExist);
    EXPECT_EQ(true, isExist);
    EXPECT_EQ(GMERR_OK, ret);
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, count);

    // 再通过接口读出来看看
    char *getValue = (char *)malloc(MAX_KV_VALUE_SIZE);
    (void)memset_s(getValue, MAX_KV_VALUE_SIZE, 0x00, MAX_KV_VALUE_SIZE);
    uint32_t getValueLen = MAX_KV_VALUE_SIZE;
    ret = GmcKvGet(stmt, key, keyLen, getValue, &getValueLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_STREQ(getValue, "8899");

    // 使用gmcmd工具更新kv表,不存在notExist字段
    args = string("gmcmd -c 'set kvtable haha notExist 30';");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    char key2[] = "notExist";
    uint32_t keyLen2 = strlen(key2) + 1;
    ret = GmcKvIsExist(stmt, key2, keyLen2, &isExist);
    EXPECT_EQ(true, isExist);
    EXPECT_EQ(GMERR_OK, ret);
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, count);

    // 通过get kvtable 命令查询对应的value信息
    system("gmcmd -c get kvtable haha hbs");
    memset_s(strcmd, cmdLen, 0x00, cmdLen);
    snprintf_s(strcmd, cmdLen, cmdLen - 1, "gmcmd -c 'get kvtable %s %s'", kvName, key);
    VerifyViewStr(strcmd, "Read Value", 1);
    VerifyViewStr(strcmd, "0x38383939", 1);

    // 通过del kvtable 命令删除信息
    memset_s(strcmd, cmdLen, 0x00, cmdLen);
    snprintf_s(strcmd, cmdLen, cmdLen - 1, "gmcmd -c 'del kvtable %s %s'", kvName, key);
    VerifyViewStr(strcmd, "DEL success", 1);
    // 再次get 值
    system("gmcmd -c get kvtable haha hbs");
    memset_s(strcmd, cmdLen, 0x00, cmdLen);
    snprintf_s(strcmd, cmdLen, cmdLen - 1, "gmcmd -c 'get kvtable %s %s'", kvName, key);
    VerifyViewStr(strcmd, "Read Value", 0);
    VerifyViewStr(strcmd, "0x38383939", 0);
    // 通过get kvtable 命令查询对应的value信息
    system("gmcmd -c get kvtable haha notExist");
    memset_s(strcmd, cmdLen, 0x00, cmdLen);
    snprintf_s(strcmd, cmdLen, cmdLen - 1, "gmcmd -c 'get kvtable %s notExist'", kvName);
    VerifyViewStr(strcmd, "0x3330", 1);
    // truncate kv 表
    memset_s(strcmd, cmdLen, 0x00, cmdLen);
    snprintf_s(strcmd, cmdLen, cmdLen - 1, "gmcmd -c \"truncate kvtable %s\";", kvName);
    VerifyViewStr(strcmd, "0x38383939", 0);
    VerifyViewStr(strcmd, "Read Value", 0);
    // 删除表
    args = string("gmcmd -c \"drop kvtable haha\";");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    free(getValue);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestCommandGen_ill)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 创建vertex表
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/test1_number.gmjson -t test1_sequentail");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 非法config json
    args = string("gmcmd -c gen object test1_sequentail newest 15 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_3_bitmap_sequential.json;");
    ret = executeCommand(args.c_str(), "parse cfg json unsuccessfully");
    EXPECT_EQ(GMERR_OK, ret);

    args = string("gmcmd -c gen object test1_sequentail newest 15 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_3_boolean_sequential.json;");
    ret = executeCommand(args.c_str(), "parse cfg json unsuccessfully");
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmcmd -c gen object test1_sequentail newest 15 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_3_byte_sequential.json;");
    ret = executeCommand(args.c_str(), "parse cfg json unsuccessfully");
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmcmd -c gen object test1_sequentail newest 15 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_3_string_sequential.json;");
    ret = executeCommand(args.c_str(), "parse cfg json unsuccessfully");
    EXPECT_EQ(ret, GMERR_OK);

    // cfg_json为不支持的list类型
    args = string("gmcmd -c gen object test1_sequentail newest 1 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_3_fix_value_list.json;");
    ret = executeCommand(args.c_str(), "succeeded 0times");
    EXPECT_EQ(ret, GMERR_OK);

    // cfg_json为无效的abc类型
    args = string("gmcmd -c gen object test1_sequentail newest 1 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_3_fix_value_illagel.json;");
    ret = executeCommand(args.c_str(), "succeeded 0times");
    EXPECT_EQ(ret, GMERR_OK);
}

// mkc add
// 1 vertex_schema.gmjson
TEST_F(StGmCmd, TestCommandGen_1)
{
    // 创建vertex表
    string args = string("gmimport -c vschema -f ./gmcmd_test_file/vertex_schema.gmjson -t VertexTable01");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 生成测试数据
    args = string("gmcmd -c gen object VertexTable01 newest 1 "
                  "./gmcmd_test_file/tmp {};");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/VertexTable01.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 再查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

// 2 VertexTable02.gmjson
TEST_F(StGmCmd, TestCommandGen_2)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/VertexTable02.gmjson -t VertexTable02");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 生成测试数据
    args = string("gmcmd -c gen object VertexTable02 newest 1 "
                  "./gmcmd_test_file/tmp {};");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/VertexTable02.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 再查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

// 3 sys.vertexjson
TEST_F(StGmCmd, TestCommandGen_3)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/gen_gmjson/sys.vertexjson -t sys");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 生成测试数据
    args = string("gmcmd -c gen object sys newest 1 "
                  "./gmcmd_test_file/tmp {};");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/sys.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 再查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

// 测试带位域的schema 没有配置文件
TEST_F(StGmCmd, TestCommandGen_4)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 创建vertex表
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/bitfield_test.gmjson -t bitfiled_label");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 生成测试数据
    args = string("gmcmd -c gen object bitfiled_label newest 1 "
                  "./gmcmd_test_file/tmp {};");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/bitfiled_label.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 再查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

// 测试random配置文件+number（test1_number.gmjson）
TEST_F(StGmCmd, TestCommandGen_5)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 创建vertex表
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/test1_number.gmjson -t test1_random");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 生成测试数据
    args = string("gmcmd -c gen object test1_random newest 10 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_1_random.json;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/test1_random.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 再查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

// 测试sequential配置文件+number（test1_number.gmjson）
TEST_F(StGmCmd, TestCommandGen_6)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 创建vertex表
    args = string("gmcmd -c drop table test1_sequentail;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/test1_number.gmjson -t test1_sequentail");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 生成测试数据
    args = string("gmcmd -c gen object test1_sequentail newest 15 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_2_sequential.json;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/test1_sequentail.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 生成测试数据
    args = string("gmcmd -c gen object test1_sequentail newest 15 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_1.json;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 再查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

// 测试fix value配置文件+number（test1_number.gmjson）
TEST_F(StGmCmd, TestCommandGen_7)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 创建vertex表
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/test1_number.gmjson -t test1_fix_value");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 生成测试数据
    args = string("gmcmd -c gen object test1_fix_value newest 1 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_3_fix_value.json;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/test1_fix_value.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/test1_fix_value.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 再查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

// 测试str类型,random
TEST_F(StGmCmd, TestCommandGen_8)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/gen_gmjson/test2_str.gmjson "
                  "-t test2_str_random");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 生成测试数据
    args = string("gmcmd -c gen object test2_str_random newest 1 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_str_1_random.json;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/test2_str_random.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 再查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

// 测试str(str/byte/bitmap/fixed/time)类型,fix_value/sequential
TEST_F(StGmCmd, TestCommandGen_9)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/gen_gmjson/test2_str.gmjson "
                  "-t test2_str_fix_value");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 生成测试数据
    args = string("gmcmd -c gen object test2_str_fix_value newest 1 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_str_2_fix_value.json;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/test2_str_fix_value.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 再查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

// 测试str(str/byte/bitmap/fixed)schema带size和不带size和size与配置文件冲突等场景
TEST_F(StGmCmd, TestCommandGen_10)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/gen_gmjson/test2_str_size.gmjson -t "
                  "test2_str_size_fix_value");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 生成测试数据
    args = string("gmcmd -c gen object test2_str_size_fix_value newest 1 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_str_2_fix_value.json;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/test2_str_size_fix_value.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 再查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

// 测试bitfiled数据类型
TEST_F(StGmCmd, TestCommandGen_11)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 创建vertex表
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/test3_bitfield.gmjson -t test3_bitfield");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 生成测试数据
    args = string("gmcmd -c gen object test3_bitfield newest 1 "
                  "./gmcmd_test_file/tmp {}");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/test3_bitfield.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 再查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

// 测试char和uchar的数据类型
TEST_F(StGmCmd, TestCommandGen_12)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/gen_gmjson/test4_char.gmjson "
                  "-t test4_char");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 生成测试数据
    args = string("gmcmd -c gen object test4_char newest 1 "
                  "./gmcmd_test_file/tmp {}");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/test4_char.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 再查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

// 测试vector array : test5_vector_array.gmjson
TEST_F(StGmCmd, TestCommandGen_13)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 创建vertex表
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/test5_vector_array.gmjson -t test5_vector_array");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 生成测试数据
    args = string("gmcmd -c gen object test5_vector_array newest 1 "
                  "./gmcmd_test_file/tmp {}");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/test5_vector_array.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 再查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

// // 测试tree多层嵌套: test6_tree.gmjson
TEST_F(StGmCmd, TestCommandGen_14)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/gen_gmjson/test6_tree.gmjson "
                  "-t test6_tree");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 生成测试数据
    args = string("gmcmd -c gen object test6_tree newest 1 "
                  "./gmcmd_test_file/tmp {}");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/test6_tree.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 再查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

// 测试不带配置文件:test1_number
TEST_F(StGmCmd, TestCommandGen_15)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/gen_gmjson/test1_number.gmjson "
                  "-t test1_number_no_cfg");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 生成测试数据
    args = string("gmcmd -c gen object test1_number_no_cfg newest 10 "
                  "./gmcmd_test_file/tmp {}");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/test1_number_no_cfg.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 再查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

// 测试schema中带默认值
TEST_F(StGmCmd, TestCommandGen_16)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 创建vertex表
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/test7_schema_with_default.gmjson -t "
                  "test7_schema_with_default");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 生成测试数据
    args = string("gmcmd -c gen object test7_schema_with_default newest 10 "
                  "./gmcmd_test_file/tmp {}");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/test7_schema_with_default.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 再查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

// 测试tree多层嵌套写10000次: test6_tree.gmjson
TEST_F(StGmCmd, TestCommandGen_17)
{
    // 导入用户
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 授予权限
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 创建vertex表
    args = string("gmimport -c vschema -f ./gmcmd_test_file/gen_gmjson/test6_tree.gmjson "
                  "-t test6_tree_10000");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 生成测试数据
    args = string("gmcmd -c gen object test6_tree_10000 newest 10000 "
                  "./gmcmd_test_file/tmp {}");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 导入vertex表记录
    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/test6_tree_10000.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 再查询vertex表记录信息
    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StGmCmd, TestSelectOption01)
{
    string args = string("gmimport -c vschema -f ./gmcmd_test_file/select_01.gmjson -t select01");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据
    args = string("gmimport -c vdata -f ./gmcmd_test_file/select_01.gmdata -t select01");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    const char *jsonData1 =
        "{"
        "  \"cid\": \"9981\","
        "  \"f_local_key\": \"111122223333\","
        "  \"f_local_hashkey\": \"88880000\","
        "  \"f_string\": \"abcd\","
        "  \"lpm_ip\": \"***********\","
        "  \"lpm_prefix\": 24,"
        "  \"f_time\": \"2019-12-31 12:59:59\","
        "  \"f_boolean\": true,"
        "  \"f_int8\": -111,"
        "  \"f_uint8\": 111,"
        "  \"f_uint16\": 222,"
        "  \"f_int16\": -222,"
        "  \"f_uint32\": 333,"
        "  \"f_int32\": -333,"
        "  \"f_int64\": -444,"
        "  \"f_uint64\": \"444\","
        "  \"f_long\": 555,"
        "  \"f_float\": -1.2339999675750732,"
        "  \"f_double\": -2.3450000000000002,"
        "  \"f_bitmap\": \"1111 0000 1111 0000 1000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000\","
        "  \"f_fixed\": "
        "\"0x123abc0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"
        "0000000000000000000000\","
        "  \"f_bytes\": \"0x1234567890abcdef\","
        "  \"order\": ["
        "{"
        "  \"oid\": 1001,"
        "  \"child\": {"
        "  \"F0\": 100"
        "},"
        "  \"oname\": \"abcdefg\""
        "}"
        "]"
        "}";
    const char *jsonData2 =
        "{"
        "  \"cid\": \"5689\","
        "  \"f_local_key\": \"111122223333\","
        "  \"f_local_hashkey\": \"88880000\","
        "  \"f_string\": \"abcd\","
        "  \"lpm_ip\": \"***********\","
        "  \"lpm_prefix\": 24,"
        "  \"f_time\": \"2019-12-31 12:59:59\","
        "  \"f_boolean\": true,"
        "  \"f_int8\": -111,"
        "  \"f_uint8\": 111,"
        "  \"f_uint16\": 222,"
        "  \"f_int16\": -222,"
        "  \"f_uint32\": 333,"
        "  \"f_int32\": -333,"
        "  \"f_int64\": -444,"
        "  \"f_uint64\": \"444\","
        "  \"f_long\": 555,"
        "  \"f_float\": -1.2339999675750732,"
        "  \"f_double\": -2.3450000000000002,"
        "  \"f_bitmap\": \"1111 0000 1111 0000 1000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000\","
        "  \"f_fixed\": "
        "\"0x123abc0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"
        "0000000000000000000000\","
        "  \"f_bytes\": \"0x1234567890abcdef\","
        "  \"order\": ["
        "{"
        "  \"oid\": 1001,"
        "  \"child\": {"
        "  \"F0\": 100"
        "},"
        "  \"oname\": \"abcdefg\""
        "}"
        "]"
        "}";
    // 使用set 命令设置表数据
    char strcmd[20480] = {0};
    snprintf_s(strcmd, 20480, 20479, "gmcmd -c 'truncate table select01';");
    ret = system(strcmd);
    EXPECT_EQ(ret, GMERR_OK);
    memset_s(strcmd, 20480, 0x00, 20480);

    snprintf_s(strcmd, 20480, 20479, "gmcmd -c 'set object select01 %s'", jsonData1);
    ret = system(strcmd);
    EXPECT_EQ(ret, GMERR_OK);
    memset_s(strcmd, 20480, 0x00, 20480);

    snprintf_s(strcmd, 20480, 20479, "gmcmd -c 'set object select01 %s'", jsonData2);
    ret = system(strcmd);
    EXPECT_EQ(ret, GMERR_OK);
    memset_s(strcmd, 20480, 0x00, 20480);
    // 使用update命令，条件更新
    const char *updateStr = "set f_int8=123 where cid=5689";
    snprintf_s(strcmd, 20480, 20479, "gmcmd -c 'update select01 %s'", updateStr);
    ret = system(strcmd);
    EXPECT_EQ(ret, GMERR_OK);
    auto VerifyViewStr0 = [&]() {
        char buffer[1280] = {0};
        std::string str;
        int totalCnt = 0;
        FILE *fp = popen(strcmd, "r");
        EXPECT_EQ(true, fp != NULL);
        if (fgets(buffer, 1280, fp) == NULL) {
            printf("buf is null\n");
        }
        while (fgets(buffer, 1280, fp) != NULL) {
            str = buffer;
            if (str.find("2 records") != std::string::npos) {
                totalCnt++;
            }
        }
        EXPECT_EQ(totalCnt, 0);
        pclose(fp);
    };
    auto VerifyViewStr1 = [&]() {
        char buffer[1280] = {0};
        std::string str;
        int totalCnt = 0;
        FILE *fp = popen(strcmd, "r");
        EXPECT_EQ(true, fp != NULL);
        if (fgets(buffer, 1280, fp) == NULL) {
            printf("buf is null\n");
        }
        while (fgets(buffer, 1280, fp) != NULL) {
            str = buffer;
            if (str.find("2 records") != std::string::npos) {
                totalCnt++;
            }
        }
        EXPECT_EQ(totalCnt, 1);
        pclose(fp);
    };
    memset_s(strcmd, 20480, 0x00, 20480);
    snprintf_s(strcmd, 20480, 20479, "gmcmd -c 'select * from select01 where cid=5689'; ");
    VerifyViewStr0();
    memset_s(strcmd, 20480, 0x00, 20480);
    snprintf_s(strcmd, 20480, 20479, "gmcmd -c 'select count(*) from select01'; ");
    VerifyViewStr1();
    EXPECT_EQ(ret, GMERR_OK);
    // 检查是否set 和 update 成功
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    ret = GmcPrepareStmtByLabelName(stmt, "select01", GMC_OPERATION_SCAN);
    EXPECT_EQ(ret, GMERR_OK);
    uint64_t cid = 0;
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    bool isFinish = false;
    bool isNull = false;
    uint32_t count = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || isFinish) {
            break;
        }
        // 查看主键的值
        ret = GmcGetVertexPropertyByName(stmt, "cid", &cid, sizeof(uint64_t), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        if (cid == 5689) {  // 如果和更新的一样，证明update命令是生效的
            int8_t fvalue = 0;
            ret = GmcGetVertexPropertyByName(stmt, "f_int8", &fvalue, sizeof(int8_t), &isNull);
            EXPECT_EQ(ret, GMERR_OK);
            EXPECT_EQ(fvalue, 123);  // 证明值更新成功
        }
        count++;  // 共计插入的数据量
    }
    EXPECT_EQ(2u, count);
    args = string("gmcmd -c 'drop table select01';");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestDropDB)
{
    // 先导入几张vertexlabel表
    string args = string("gmimport -c vschema -f ./gmcmd_test_file/select_01.gmjson -t T0");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmimport -c vschema -f ./gmcmd_test_file/select_01.gmjson -t T1");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmimport -c vschema -f ./gmcmd_test_file/select_01.gmjson -t T2");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 再导入几张kv表
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, GmcKvCreateTable(stmt, "KV0", g_cfgJson));
    EXPECT_EQ(GMERR_OK, GmcKvCreateTable(stmt, "KV1", g_cfgJson));
    EXPECT_EQ(GMERR_OK, GmcKvCreateTable(stmt, "KV2", g_cfgJson));

    // 检查下vertexLabel表是否导成功了
    args = string("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    char buffer[1024] = {0};
    std::string str;
    bool findTable0 = false;
    bool findTable1 = false;
    bool findTable2 = false;
    FILE *fp = popen(args.c_str(), "r");
    EXPECT_EQ(true, fp != nullptr);
    while (fgets(buffer, 1024, fp) != NULL) {
        str = buffer;
        if (str.find(" VERTEX_LABEL_NAME: T0") != std::string::npos) {
            findTable0 = true;
        }
        if (str.find(" VERTEX_LABEL_NAME: T1") != std::string::npos) {
            findTable1 = true;
        }
        if (str.find(" VERTEX_LABEL_NAME: T2") != std::string::npos) {
            findTable2 = true;
        }
    }
    pclose(fp);
    EXPECT_TRUE(findTable0);
    EXPECT_TRUE(findTable1);
    EXPECT_TRUE(findTable2);

    // 检查下KV表是否导成功了
    args = string("gmsysview -q V\\$CATA_KV_TABLE_INFO");
    (void)memset_s(buffer, 1024, 0x00, 1024);
    findTable0 = false;
    findTable1 = false;
    findTable2 = false;
    fp = popen(args.c_str(), "r");
    EXPECT_EQ(true, fp != nullptr);
    while (fgets(buffer, 1024, fp) != NULL) {
        str = buffer;
        if (str.find(" LABEL_NAME: KV0") != std::string::npos) {
            findTable0 = true;
        }
        if (str.find(" LABEL_NAME: KV1") != std::string::npos) {
            findTable1 = true;
        }
        if (str.find(" LABEL_NAME: KV2") != std::string::npos) {
            findTable2 = true;
        }
    }
    pclose(fp);
    EXPECT_TRUE(findTable0);
    EXPECT_TRUE(findTable1);
    EXPECT_TRUE(findTable2);

    // 运行命令
    args = string("gmcmd -c drop database;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 再检查一次
    // 检查下vertexLabel表是否删除成功了
    args = string("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    (void)memset_s(buffer, 1024, 0x00, 1024);
    findTable0 = false;
    findTable1 = false;
    findTable2 = false;
    fp = popen(args.c_str(), "r");
    EXPECT_EQ(true, fp != nullptr);
    while (fgets(buffer, 1024, fp) != NULL) {
        str = buffer;
        if (str.find(" VERTEX_LABEL_NAME: T0") != std::string::npos) {
            findTable0 = true;
        }
        if (str.find(" VERTEX_LABEL_NAME: T1") != std::string::npos) {
            findTable1 = true;
        }
        if (str.find(" VERTEX_LABEL_NAME: T2") != std::string::npos) {
            findTable2 = true;
        }
    }
    pclose(fp);
    EXPECT_FALSE(findTable0);
    EXPECT_FALSE(findTable1);
    EXPECT_FALSE(findTable2);

    // 检查下KV表是否删除成功了
    args = string("gmsysview -q V\\$CATA_KV_TABLE_INFO");
    (void)memset_s(buffer, 1024, 0x00, 1024);
    findTable0 = false;
    findTable1 = false;
    findTable2 = false;
    fp = popen(args.c_str(), "r");
    EXPECT_EQ(true, fp != nullptr);
    while (fgets(buffer, 1024, fp) != NULL) {
        str = buffer;
        if (str.find(" LABEL_NAME: KV0") != std::string::npos) {
            findTable0 = true;
        }
        if (str.find(" LABEL_NAME: KV1") != std::string::npos) {
            findTable1 = true;
        }
        if (str.find(" LABEL_NAME: KV2") != std::string::npos) {
            findTable2 = true;
        }
    }
    pclose(fp);
    EXPECT_FALSE(findTable0);
    EXPECT_FALSE(findTable1);
    EXPECT_FALSE(findTable2);
    DestroyConnectionAndStmt(conn, stmt);
}

// 持久化场景不支持边
#ifndef FEATURE_PERSISTENCE
TEST_F(StGmCmd, TestDropDB2)
{
    // 建表，建边
    uint32_t ret;
    // 创建同步连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *cypherLabelCfgJson = R"({"max_record_count":1000, "isFastReadUncommitted":false})";
    const char *cypherLabelJson =
        R"([{
            "type":"record",
            "name":"testCypherPathLabel1",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"testCypherPathLabel1",
                        "name":"cypherLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, cypherLabelJson, cypherLabelCfgJson));
    const char *cypherLabelCfgJson2 = R"({"max_record_count":1000, "isFastReadUncommitted":false})";
    const char *cypherLabelJson2 =
        R"([{
            "type":"record",
            "name":"testCypherPathLabel2",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"testCypherPathLabel2",
                        "name":"cypherLabel2_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, cypherLabelJson2, cypherLabelCfgJson2));

    const char *edgeCfgJson = "{\"max_record_count\":1000}";
    const char *edgeLabelJson = R"([{
        "name":"testCypherPathEdgeLabel1",
        "source_vertex_label":"testCypherPathLabel1",
        "comment":"the edge A to B",
        "dest_vertex_label":"testCypherPathLabel2",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {"source_property":"F1","dest_property":"F1"}
            ]
        }
    }])";
    EXPECT_EQ(GMERR_OK, GmcCreateEdgeLabel(stmt, edgeLabelJson, edgeCfgJson));
    // 查视图
    char buffer[1024] = {0};
    string args = string("gmsysview -q V\\$CATA_EDGE_LABEL_INFO");
    (void)memset_s(buffer, 1024, 0x00, 1024);
    bool findedge = false;
    std::string str;
    FILE *fp = popen(args.c_str(), "r");
    EXPECT_EQ(true, fp != nullptr);
    while (fgets(buffer, 1024, fp) != NULL) {
        str = buffer;
        if (str.find(" EDGE_LABEL_NAME: testCypherPathEdgeLabel1") != std::string::npos) {
            findedge = true;
        }
    }
    EXPECT_TRUE(findedge);
    pclose(fp);

    // 运行命令
    args = string("gmcmd -c drop database;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    // 查视图
    args = string("gmsysview -q V\\$CATA_EDGE_LABEL_INFO");
    (void)memset_s(buffer, 1024, 0x00, 1024);
    findedge = false;
    fp = popen(args.c_str(), "r");
    EXPECT_EQ(true, fp != nullptr);
    while (fgets(buffer, 1024, fp) != NULL) {
        str = buffer;
        if (str.find(" LABEL_NAME: testCypherPathEdgeLabel1") != std::string::npos) {
            findedge = true;
        }
    }
    EXPECT_FALSE(findedge);
    pclose(fp);
    DestroyConnectionAndStmt(conn, stmt);
}
#endif

TEST_F(StGmCmd, TestCommandGen_18)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    (void)GmcDropVertexLabel(stmt, "VertexTable01");
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmimport -c vschema -f ./gmcmd_test_file/vertex_schema.gmjson -t VertexTable01");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmcmd -c gen object VertexTable01 newest 20000 "
                  "./gmcmd_test_file/tmp {};");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/VertexTable01.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
}

#ifdef FEATURE_PERSISTENCE
// 持久化约束，不支持DB表的升降级
TEST_F(StGmCmd, DISABLED_TestCommandGen_19)
#else
TEST_F(StGmCmd, TestCommandGen_19)
#endif
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    (void)GmcDropVertexLabel(stmt, "specialLabel");
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/vertexlabel_version1.gmjson");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    char g_connServer[256] = "usocket:/run/verona/unix_emserver";
    char cmd[256];

    char schema_file2[128] = "./gmcmd_test_file/gen_gmjson/vertexlabel_version2.gmjson";
    snprintf(cmd, MAX_CMD_SIZE, "gmddl  -c alter -u online -f %s -t specialLabel -ns %s -s %s", schema_file2, "public",
        g_connServer);
    printf("%s\n", cmd);
    ret = system(cmd);
    ASSERT_EQ(ret, GMERR_OK);

    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmcmd -c gen object specialLabel 3 1 "
                  "./gmcmd_test_file/tmp {};");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/specialLabel.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}
#ifdef FEATURE_PERSISTENCE
// 持久化约束，不支持DB表的升降级
TEST_F(StGmCmd, DISABLED_TestCommandGen_20)
#else
TEST_F(StGmCmd, TestCommandGen_20)
#endif
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    (void)GmcDropVertexLabel(stmt, "specialLabel");
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/vertexlabel_version1.gmjson");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    char g_connServer[256] = "usocket:/run/verona/unix_emserver";
    char cmd[256];

    char schema_file2[128] = "./gmcmd_test_file/gen_gmjson/vertexlabel_version2.gmjson";
    snprintf(cmd, MAX_CMD_SIZE, "gmddl  -c alter -u online -f %s -t specialLabel -ns %s -s %s", schema_file2, "public",
        g_connServer);
    printf("%s\n", cmd);
    ret = system(cmd);
    ASSERT_EQ(ret, GMERR_OK);

    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmcmd -c gen object specialLabel 2 1 "
                  "./gmcmd_test_file/tmp {};");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/specialLabel.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

// str size冲突 cfg的size更大，报错
TEST_F(StGmCmd, TestCommandGen_21)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    (void)GmcDropVertexLabel(stmt, "gen_all_support_type_label");
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/vertexlabel_conflict.gmjson");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmcmd -c gen object gen_all_support_type_label newest 1 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_conflict_1.json;");

    ret = executeCommand(args.c_str(), "succeeded 0times");
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// str size冲突 cfg的size更小，正常
TEST_F(StGmCmd, TestCommandGen_22)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    (void)GmcDropVertexLabel(stmt, "gen_all_support_type_label");
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/vertexlabel_conflict.gmjson");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmcmd -c gen object gen_all_support_type_label newest 1 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_conflict_2.json;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/gen_all_support_type_label.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/gen_all_support_type_label.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

// bitmap size冲突 cfg的size更大，报错
TEST_F(StGmCmd, TestCommandGen_23)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    (void)GmcDropVertexLabel(stmt, "gen_all_support_type_label");
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/vertexlabel_conflict.gmjson");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmcmd -c gen object gen_all_support_type_label newest 1 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_conflict_3.json;");

    ret = executeCommand(args.c_str(), "succeeded 0times");
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// bitmap size冲突 cfg的size更小，成功
TEST_F(StGmCmd, TestCommandGen_24)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    (void)GmcDropVertexLabel(stmt, "gen_all_support_type_label");
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/vertexlabel_conflict.gmjson");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmcmd -c gen object gen_all_support_type_label newest 1 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_conflict_4.json;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/gen_all_support_type_label.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/gen_all_support_type_label.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

// byte size冲突 cfg的size更大，报错
TEST_F(StGmCmd, TestCommandGen_25)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    (void)GmcDropVertexLabel(stmt, "gen_all_support_type_label");
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/vertexlabel_conflict.gmjson");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmcmd -c gen object gen_all_support_type_label newest 1 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_conflict_5.json;");

    ret = executeCommand(args.c_str(), "succeeded 0times");
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// byte size冲突 cfg的size更小，成功
TEST_F(StGmCmd, TestCommandGen_26)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    (void)GmcDropVertexLabel(stmt, "gen_all_support_type_label");
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/vertexlabel_conflict.gmjson");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmcmd -c gen object gen_all_support_type_label newest 1 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_conflict_6.json;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/gen_all_support_type_label.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmimport -c vdata -f ./gmcmd_test_file/tmp/gen_all_support_type_label.gmdata");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

// fixed size冲突 cfg的size更小，报错
TEST_F(StGmCmd, TestCommandGen_27)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    (void)GmcDropVertexLabel(stmt, "gen_all_support_type_label");
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/vertexlabel_conflict.gmjson");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmcmd -c gen object gen_all_support_type_label newest 1 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_conflict_9.json;");

    ret = executeCommand(args.c_str(), "succeeded 0times");
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// fixed size冲突 cfg的size更大，报错
TEST_F(StGmCmd, TestCommandGen_28)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    (void)GmcDropVertexLabel(stmt, "gen_all_support_type_label");
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/vertexlabel_conflict.gmjson");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmcmd -c gen object gen_all_support_type_label newest 1 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_conflict_10.json;");

    ret = executeCommand(args.c_str(), "succeeded 0times");
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// random minStr > size，报错
TEST_F(StGmCmd, TestCommandGen_29)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    (void)GmcDropVertexLabel(stmt, "gen_all_support_type_label");
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/vertexlabel_conflict.gmjson");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmcmd -c gen object gen_all_support_type_label newest 1 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_conflict_8.json;");

    ret = executeCommand(args.c_str(), "gen verify gen data unsuccessfully, ret is 63001.");
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// random maxStr > size，报错
TEST_F(StGmCmd, TestCommandGen_30)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    (void)GmcDropVertexLabel(stmt, "gen_all_support_type_label");
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/vertexlabel_conflict.gmjson");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmcmd -c gen object gen_all_support_type_label newest 1 "
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_conflict_7.json;");
    ret = executeCommand(args.c_str(), "gen verify gen data unsuccessfully, ret is 63001.");
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 持久化场景不支持资源字段
#ifndef FEATURE_PERSISTENCE
// gen不支持partition和资源字段
TEST_F(StGmCmd, TestCommandGen_31)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    (void)GmcDropVertexLabel(stmt, "vertexLabel_res");
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/vertexLabel_res.gmjson");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmcmd -c gen object vertexLabel_res newest 1"
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_conflict_7.json;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/vertexLabel_partition.gmjson");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmcmd -c gen object vertexLabel_res newest 1"
                  "./gmcmd_test_file/tmp "
                  "./gmcmd_test_file/cfg_path/cfg_conflict_7.json;");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    DestroyConnectionAndStmt(conn, stmt);
}
#endif

// generate_rule异常
TEST_F(StGmCmd, TestCommandGen_32)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    (void)GmcDropVertexLabel(stmt, "gen_all_support_type_label");
    string args = string("gmrule -c import_allowlist -f ./gmcmd_test_file/user.gmuser");
    int ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmrule -c import_policy -f ./gmcmd_test_file/system_privilegel_policy.gmpolicy");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);
    args = string("gmimport -c vschema -f "
                  "./gmcmd_test_file/gen_gmjson/vertexlabel_conflict.gmjson");
    ret = system(args.c_str());
    EXPECT_EQ(ret, GMERR_OK);

    char cmpStr[300];
    for (int i = 1; i <= 49; i++) {
        sprintf_s(cmpStr, 300,
            "gmcmd -c gen object gen_all_support_type_label newest 1 "
            "./gmcmd_test_file/tmp "
            "./gmcmd_test_file/cfg_path/cfg_conflict_error%d.json;",
            i);
        ret = executeCommand(cmpStr, "parse cfg json unsuccessfully");
        EXPECT_EQ(GMERR_OK, ret);
    }
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StGmCmd, TestCommandExpect)
{
    // 执行脚本启动gmcmd交互模式完成help查询后退出 并将打屏信息输出到指定文件下
    system("sh gmcmd_interact.sh > interactHelp.txt");
    int ret = 0;
    char cmd[512] = {0};
    FILE *fp;
    char buffer[2048];
    char finalhelp[2048] = "delete";
    snprintf(cmd, 512, "grep delete interactHelp.txt");
    fp = popen(cmd, "r");
    fgets(buffer, sizeof(buffer), fp);
    if (strstr(buffer, finalhelp)) {
        ret = 0;
    } else {
        ret = 1;
    }
    pclose(fp);
    fp = NULL;
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StGmCmd, TestCommandExpectLong)
{
    // 执行脚本处理超长乱码
    system("sh gmcmd_interact_long.sh > interactLong.txt");
    int ret = 0;
    char cmd[512] = {0};
    FILE *fp;
    char buffer[2048];
    char finalhelp[2048] = "Unsuccessful";
    snprintf(cmd, 512, "grep Unsuccessful interactLong.txt");
    fp = popen(cmd, "r");
    fgets(buffer, sizeof(buffer), fp);
    if (strstr(buffer, finalhelp)) {
        ret = 0;
    } else {
        ret = 1;
    }
    pclose(fp);
    fp = NULL;
    EXPECT_EQ(GMERR_OK, ret);
}

void VertexLabelInsertData(GmcStmtT *stmt, const char *labelName)
{
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));
    for (int i = 0; i < 10; i++) {
        uint32_t f0 = 0 + i;
        uint32_t f1 = 1000 + i;
        uint32_t f2 = 2000 + i;
        uint32_t f3 = 3000 + i;
        uint32_t f4 = 4000 + i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f0", GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f4", GMC_DATATYPE_UINT32, &f4, sizeof(f4)));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    }
}

// update：主键索引四个，where条件覆盖所有四个主键，可以加速
TEST_F(StGmCmd, IndexFilter01)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    const char *labelName = "IndexFilter4";
    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, system("gmimport -c vschema -f ./gmcmd_test_file/IndexFilter4.gmjson"));

    VertexLabelInsertData(stmt, labelName);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c update IndexFilter4 set IndexFilter4.f4=666 where IndexFilter4.f0=2 and "
                               "IndexFilter4.f1=1002 and IndexFilter4.f2=2002 and IndexFilter4.f3=3002;"));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetFilter(stmt, "IndexFilter4.f0=2"));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    bool eof;
    uint32_t value = 0;
    bool isNull;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(false, eof);
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "f4", &value, sizeof(uint32_t), &isNull));
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(666, value);
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(true, eof);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c drop table IndexFilter4;"));
    DestroyConnectionAndStmt(conn, stmt);
}

// update：主键索引四个，where条件覆盖所有3个主键，不可以加速
TEST_F(StGmCmd, IndexFilter02)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    const char *labelName = "IndexFilter4";
    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, system("gmimport -c vschema -f ./gmcmd_test_file/IndexFilter4.gmjson"));

    VertexLabelInsertData(stmt, labelName);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c update IndexFilter4 set IndexFilter4.f4=666 where IndexFilter4.f0=2 and "
                               "IndexFilter4.f1=1002 and IndexFilter4.f2=2002;"));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetFilter(stmt, "IndexFilter4.f0=2"));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    bool eof;
    uint32_t value = 0;
    bool isNull;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(false, eof);
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "f4", &value, sizeof(uint32_t), &isNull));
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(666, value);
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(true, eof);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c drop table IndexFilter4;"));
    DestroyConnectionAndStmt(conn, stmt);
}

// delete：主键索引四个，where条件覆盖所有四个主键，可以加速
TEST_F(StGmCmd, IndexFilter03)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    const char *labelName = "IndexFilter4";
    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, system("gmimport -c vschema -f ./gmcmd_test_file/IndexFilter4.gmjson"));

    VertexLabelInsertData(stmt, labelName);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c delete from IndexFilter4 where IndexFilter4.f0=2 and IndexFilter4.f1=1002 and "
                               "IndexFilter4.f2=2002 and IndexFilter4.f3=3002;"));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetFilter(stmt, "IndexFilter4.f0=2"));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    bool eof;
    uint32_t value = 0;
    bool isNull;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(true, eof);
    EXPECT_EQ(GMERR_NO_DATA, GmcGetVertexPropertyByName(stmt, "f4", &value, sizeof(uint32_t), &isNull));
    EXPECT_EQ(false, isNull);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c drop table IndexFilter4;"));
    DestroyConnectionAndStmt(conn, stmt);
}

// delete：主键索引四个，where条件覆盖所有3个主键，不可以加速
TEST_F(StGmCmd, IndexFilter04)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    const char *labelName = "IndexFilter4";
    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, system("gmimport -c vschema -f ./gmcmd_test_file/IndexFilter4.gmjson"));

    VertexLabelInsertData(stmt, labelName);

    EXPECT_EQ(
        GMERR_OK, system("gmcmd -c delete from IndexFilter4 where IndexFilter4.f1=1002 and IndexFilter4.f2=2002 and "
                         "IndexFilter4.f3=3002;"));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetFilter(stmt, "IndexFilter4.f0=2"));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    bool eof;
    uint32_t value = 0;
    bool isNull;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(true, eof);
    EXPECT_EQ(GMERR_NO_DATA, GmcGetVertexPropertyByName(stmt, "f4", &value, sizeof(uint32_t), &isNull));
    EXPECT_EQ(false, isNull);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c drop table IndexFilter4;"));
    DestroyConnectionAndStmt(conn, stmt);
}

// scan：主键索引四个，where条件覆盖所有四个主键，可以加速
TEST_F(StGmCmd, IndexFilter05)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    const char *labelName = "IndexFilter4";
    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, system("gmimport -c vschema -f ./gmcmd_test_file/IndexFilter4.gmjson"));

    VertexLabelInsertData(stmt, labelName);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    Status ret = GmcSetFilter(
        stmt, "IndexFilter4.f0=3 and IndexFilter4.f1=1003 and IndexFilter4.f2=2003 and IndexFilter4.f3=3003");
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    bool eof;
    uint32_t value = 0;
    bool isNull;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(false, eof);
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "f4", &value, sizeof(uint32_t), &isNull));
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(4003, value);
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(true, eof);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c drop table IndexFilter4;"));
    DestroyConnectionAndStmt(conn, stmt);
}

// scan：主键索引四个，where条件覆盖所有3个主键，不可以加速
TEST_F(StGmCmd, IndexFilter06)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    const char *labelName = "IndexFilter4";
    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, system("gmimport -c vschema -f ./gmcmd_test_file/IndexFilter4.gmjson"));

    VertexLabelInsertData(stmt, labelName);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetFilter(stmt, "IndexFilter4.f0=3 and IndexFilter4.f1=1003 and IndexFilter4.f3=3003"));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    bool eof;
    uint32_t value = 0;
    bool isNull;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(false, eof);
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "f4", &value, sizeof(uint32_t), &isNull));
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(4003, value);
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(true, eof);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c drop table IndexFilter4;"));
    DestroyConnectionAndStmt(conn, stmt);
}

// update：主键索引四个，where条件覆盖所有四个主键，可以加速,update两次，主要测相同过滤条件的缓存复用场景，预期不使用缓存
TEST_F(StGmCmd, IndexFilter07)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    const char *labelName = "IndexFilter4";
    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, system("gmimport -c vschema -f ./gmcmd_test_file/IndexFilter4.gmjson"));

    VertexLabelInsertData(stmt, labelName);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c update IndexFilter4 set IndexFilter4.f4=666 where IndexFilter4.f0=2 and "
                               "IndexFilter4.f1=1002 and IndexFilter4.f2=2002 and IndexFilter4.f3=3002;"));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetFilter(stmt, "IndexFilter4.f0=2"));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    bool eof;
    uint32_t value = 0;
    bool isNull;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(false, eof);
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "f4", &value, sizeof(uint32_t), &isNull));
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(666, value);
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(true, eof);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c update IndexFilter4 set IndexFilter4.f4=777 where IndexFilter4.f0=2 and "
                               "IndexFilter4.f1=1002 and IndexFilter4.f2=2002 and IndexFilter4.f3=3002;"));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetFilter(stmt, "IndexFilter4.f0=2"));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(false, eof);
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "f4", &value, sizeof(uint32_t), &isNull));
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(777, value);
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(true, eof);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c drop table IndexFilter4;"));
    DestroyConnectionAndStmt(conn, stmt);
}

// update：主键索引四个，where条件覆盖所有2个主键，1个非主键，不可以加速
TEST_F(StGmCmd, IndexFilter08)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    const char *labelName = "IndexFilter4";
    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, system("gmimport -c vschema -f ./gmcmd_test_file/IndexFilter4.gmjson"));

    VertexLabelInsertData(stmt, labelName);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c update IndexFilter4 set IndexFilter4.f4=666 where IndexFilter4.f4=4002 and "
                               "IndexFilter4.f2=2002 and IndexFilter4.f3=3002;"));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetFilter(stmt, "IndexFilter4.f0=2"));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    bool eof;
    uint32_t value = 0;
    bool isNull;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(false, eof);
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "f4", &value, sizeof(uint32_t), &isNull));
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(666, value);
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(true, eof);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c drop table IndexFilter4;"));
    DestroyConnectionAndStmt(conn, stmt);
}

// update：主键索引四个，where条件只有1个非主键，不可以加速
TEST_F(StGmCmd, IndexFilter09)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    const char *labelName = "IndexFilter4";
    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, system("gmimport -c vschema -f ./gmcmd_test_file/IndexFilter4.gmjson"));

    VertexLabelInsertData(stmt, labelName);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c update IndexFilter4 set IndexFilter4.f4=666 where IndexFilter4.f4=4002;"));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetFilter(stmt, "IndexFilter4.f0=2"));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    bool eof;
    uint32_t value = 0;
    bool isNull;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(false, eof);
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "f4", &value, sizeof(uint32_t), &isNull));
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(666, value);
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(true, eof);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c drop table IndexFilter4;"));
    DestroyConnectionAndStmt(conn, stmt);
}

// update：主键索引9个，where条件最多写8个条件，无法加速
TEST_F(StGmCmd, IndexFilter10)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    const char *labelName = "IndexFilter9";
    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, system("gmimport -c vschema -f ./gmcmd_test_file/IndexFilter9.gmjson"));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));
    for (int i = 0; i < 10; i++) {
        uint32_t f0 = 0 + i;
        uint32_t f1 = 1000 + i;
        uint32_t f2 = 2000 + i;
        uint32_t f3 = 3000 + i;
        uint32_t f4 = 4000 + i;
        uint32_t f5 = 5000 + i;
        uint32_t f6 = 6000 + i;
        uint32_t f7 = 7000 + i;
        uint32_t f8 = 8000 + i;
        uint32_t f9 = 9000 + i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f0", GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f4", GMC_DATATYPE_UINT32, &f4, sizeof(f4)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f5", GMC_DATATYPE_UINT32, &f5, sizeof(f5)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f6", GMC_DATATYPE_UINT32, &f6, sizeof(f6)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f7", GMC_DATATYPE_UINT32, &f7, sizeof(f7)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f8", GMC_DATATYPE_UINT32, &f8, sizeof(f8)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f9", GMC_DATATYPE_UINT32, &f9, sizeof(f9)));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    }

    EXPECT_EQ(GMERR_OK,
        system(
            "gmcmd -c update IndexFilter9 set IndexFilter9.f9=666 where IndexFilter9.f0=2 and IndexFilter9.f1=1002 and "
            "IndexFilter9.f2=2002 and IndexFilter9.f3=3002 and IndexFilter9.f4=4002 and IndexFilter9.f5=5002 and "
            "IndexFilter9.f6=6002 and IndexFilter9.f7=7002;"));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetFilter(stmt, "IndexFilter9.f0=2"));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    bool eof;
    uint32_t value = 0;
    bool isNull;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(false, eof);
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "f9", &value, sizeof(uint32_t), &isNull));
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(666, value);
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(true, eof);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c drop table IndexFilter9;"));
    DestroyConnectionAndStmt(conn, stmt);
}

// update：主键索引8个，where条件最多写8个条件，临界值加速
TEST_F(StGmCmd, IndexFilter11)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    const char *labelName = "IndexFilter8";
    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, system("gmimport -c vschema -f ./gmcmd_test_file/IndexFilter8.gmjson"));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));
    for (int i = 0; i < 10; i++) {
        uint32_t f0 = 0 + i;
        uint32_t f1 = 1000 + i;
        uint32_t f2 = 2000 + i;
        uint32_t f3 = 3000 + i;
        uint32_t f4 = 4000 + i;
        uint32_t f5 = 5000 + i;
        uint32_t f6 = 6000 + i;
        uint32_t f7 = 7000 + i;
        uint32_t f8 = 8000 + i;
        uint32_t f9 = 9000 + i;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f0", GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f4", GMC_DATATYPE_UINT32, &f4, sizeof(f4)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f5", GMC_DATATYPE_UINT32, &f5, sizeof(f5)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f6", GMC_DATATYPE_UINT32, &f6, sizeof(f6)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f7", GMC_DATATYPE_UINT32, &f7, sizeof(f7)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f8", GMC_DATATYPE_UINT32, &f8, sizeof(f8)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "f9", GMC_DATATYPE_UINT32, &f9, sizeof(f9)));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    }

    EXPECT_EQ(GMERR_OK,
        system(
            "gmcmd -c update IndexFilter8 set IndexFilter8.f9=666 where IndexFilter8.f0=2 and IndexFilter8.f1=1002 and "
            "IndexFilter8.f2=2002 and IndexFilter8.f3=3002 and IndexFilter8.f4=4002 and IndexFilter8.f5=5002 and "
            "IndexFilter8.f6=6002 and IndexFilter8.f7=7002;"));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetFilter(stmt, "IndexFilter8.f0=2"));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    bool eof;
    uint32_t value = 0;
    bool isNull;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(false, eof);
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "f9", &value, sizeof(uint32_t), &isNull));
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(666, value);
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(true, eof);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c drop table IndexFilter8;"));
    DestroyConnectionAndStmt(conn, stmt);
}

// update：主键索引2个，where条件2个非主键，不可以加速
TEST_F(StGmCmd, IndexFilter12)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    const char *labelName = "IndexFilter2";
    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, system("gmimport -c vschema -f ./gmcmd_test_file/IndexFilter2.gmjson"));

    VertexLabelInsertData(stmt, labelName);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c update IndexFilter2 set IndexFilter2.f4=666 where IndexFilter2.f2=2002 and "
                               "IndexFilter2.f3=3002;"));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetFilter(stmt, "IndexFilter2.f0=2"));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    bool eof;
    uint32_t value = 0;
    bool isNull;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(false, eof);
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "f4", &value, sizeof(uint32_t), &isNull));
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(666, value);
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(true, eof);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c drop table IndexFilter2;"));
    DestroyConnectionAndStmt(conn, stmt);
}

// update：主键索引2个，where条件2个主键不为=，不可以加速
TEST_F(StGmCmd, IndexFilter13)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    const char *labelName = "IndexFilter2";
    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, system("gmimport -c vschema -f ./gmcmd_test_file/IndexFilter2.gmjson"));

    VertexLabelInsertData(stmt, labelName);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c update IndexFilter2 set IndexFilter2.f4=666 where IndexFilter2.f0\\<=2 and "
                               "IndexFilter2.f1\\>=1002;"));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetFilter(stmt, "IndexFilter2.f0=2"));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    bool eof;
    uint32_t value = 0;
    bool isNull;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(false, eof);
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "f4", &value, sizeof(uint32_t), &isNull));
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(666, value);
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(true, eof);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c drop table IndexFilter2;"));
    DestroyConnectionAndStmt(conn, stmt);
}

// update：主键索引2个，where条件1个主键为=，另一个不为=，不可以加速
TEST_F(StGmCmd, IndexFilter14)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    const char *labelName = "IndexFilter2";
    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, system("gmimport -c vschema -f ./gmcmd_test_file/IndexFilter2.gmjson"));

    VertexLabelInsertData(stmt, labelName);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c update IndexFilter2 set IndexFilter2.f4=666 where IndexFilter2.f0=2 and "
                               "IndexFilter2.f1\\<1003;"));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetFilter(stmt, "IndexFilter2.f0=2"));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    bool eof;
    uint32_t value = 0;
    bool isNull;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(false, eof);
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "f4", &value, sizeof(uint32_t), &isNull));
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(666, value);
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(true, eof);

    EXPECT_EQ(GMERR_OK, system("gmcmd -c drop table IndexFilter2;"));
    DestroyConnectionAndStmt(conn, stmt);
}
