/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: test fragment interface
 * Author: xujun
 * Create: 2024-03-20
 */

#include "grd_doc_api.h"

#include <string>

#include "gtest/gtest.h"

#include "db_json.h"
#include "db_test_tool.h"
#include "de_equip_info.h"
#include "db_mem_context.h"
#include "grd_db_api.h"
#include "grd_error.h"
#include "grd_kv_api.h"
#include "grd_sync_api.h"
#include "grd_type_export.h"
#include "shared_obj_test_tool.h"
#include "store_limits.h"
#include "store_type.h"
#include "gme_doc_obj.h"
#include "grd_array_api.h"
#include "grd_xml_fragment_api.h"
#include "grd_xml_element_api.h"
#include "grd_text_api.h"
#include "grd_map_api.h"

static const char *g_tableName = "1_array";

using namespace testing::ext;
using namespace shared_obj_test_tool;

class GRDDocArrayApiTest : public testing::Test {
public:
    std::string g_equipId = "A";
    static void SetUpTestCase(void);
    static void TearDownTestCase(void);
    void SetUp();
    void TearDown();
};

void GRDDocArrayApiTest::SetUpTestCase(void)
{}

void GRDDocArrayApiTest::TearDownTestCase(void)
{}

void GRDDocArrayApiTest::SetUp(void)
{
    EXPECT_EQ(DbTestTool::RemoveDir(g_testDb), 0);
    EXPECT_EQ(DbTestTool::MakeDir(g_testDb), 0);
    ASSERT_EQ(GRD_DBOpen(g_testDbFile, g_configStr, GRD_DB_OPEN_CREATE, &g_db), GRD_OK);
    ASSERT_EQ(GRD_SetEquipId(g_db, g_equipId.c_str()), GRD_OK);
}

void GRDDocArrayApiTest::TearDown(void)
{
    ASSERT_EQ(GRD_DBClose(g_db, GRD_DB_CLOSE), GRD_OK);
    EXPECT_EQ(DbTestTool::RemoveDir(g_testDb), 0);
    g_db = nullptr;
}

/**
 * @tc.name: GRDDocArrayInsertAbnormalTest001
 * @tc.desc: test insert array with invalid args
 * @tc.type: FUNC
 * @tc.require: DTS2025032722201
 * @tc.author: liufuchenxing
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArrayInsertAbnormalTest001, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert invalid content
     * @tc.expected: step1. GRD_INVALID_ARGS
     */
    const char *content = "[{\"type\":57, \"value\":\"111\"}]";
    char *value = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = g_tableName, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 0, nullptr, &value), GRD_INVALID_ARGS);
    ASSERT_TRUE(value == nullptr);

    /**
     * @tc.steps: step2. invalid db
     * @tc.expected: step2. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_ArrayInsert(nullptr, &addr, 0, content, &value), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step3. invalid addr
     * @tc.expected: step3. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_ArrayInsert(g_db, nullptr, 0, content, &value), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step4. invalid response value
     * @tc.expected: step4. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 0, content, nullptr), GRD_INVALID_ARGS);
}

/**
 * @tc.name: GRDDocArraySetApiTest001
 * @tc.desc: Get elements of array, and check it
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR-20078075.015.002
 * @tc.author: wangxiangdong
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArraySetApiTest001, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert one item to db
     * @tc.expected: step1. GRD_OK
     */
    const char *content = "[{\"type\":57, \"value\":\"111\"}]";
    char *value = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = g_tableName, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 0, content, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step2. insert one item to db
     * @tc.expected: step2. GRD_OK
     */
    const char *content2 = "[{\"type\":57, \"value\":\"222\"}]";
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 1, content2, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step3. insert one item to db
     * @tc.expected: step3. GRD_OK
     */
    const char *content3 = "[{\"type\":2, \"value\":\"\"}]";
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 2, content3, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step4. ToArray to get all items
     * @tc.expected: step4. GRD_OK
     */
    char expectValue[200] = {0};
    int ret = sprintf_s(expectValue, sizeof(expectValue),
        "[{\"type\":57,\"value\":\"111\"},{\"type\":57,\"value\":\"222\"},{\"type\":2,"
        "\"elementId\":\"%s_6\"}]",
        g_equipId.c_str());
    ASSERT_LT(0, ret);
    ASSERT_EQ(GRD_ArrayToArray(g_db, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, expectValue);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step5. delete one item
     * @tc.expected: step5. GRD_OK
     */
    ASSERT_EQ(GRD_ArrayDelete(g_db, &addr, 1, 1), GRD_OK);
    /**
     * @tc.steps: step6. ToArray to get all items
     * @tc.expected: step6. GRD_OK
     */

    ret = sprintf_s(expectValue, sizeof(expectValue),
        "[{\"type\":57,\"value\":\"111\"},{\"type\":2,\"elementId\":\"%s_6\"}]", g_equipId.c_str());
    ASSERT_LT(0, ret);
    ASSERT_EQ(GRD_ArrayToArray(g_db, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, expectValue);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step7. insert one item to db
     * @tc.expected: step7. GRD_OK
     */
    const char *content6 = "[{\"type\":2, \"value\":\"\"}, {\"type\":57, \"value\":\"4444\"}]";
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 2, content6, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step8. ToArray to get all items
     * @tc.expected: step8. GRD_OK
     */
    ret = sprintf_s(expectValue, sizeof(expectValue),
        "[{\"type\":57,\"value\":\"111\"},{\"type\":2,\"elementId\":\"%s_6\"},{\"type\":2,"
        "\"elementId\":\"%s_7\"},{\"type\":57,\"value\":\"4444\"}]",
        g_equipId.c_str(), g_equipId.c_str());
    ASSERT_LT(0, ret);
    ASSERT_EQ(GRD_ArrayToArray(g_db, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, expectValue);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step9. get index 0 item
     * @tc.expected: step9. GRD_OK
     */
    ASSERT_EQ(GRD_ArrayGet(g_db, &addr, 0, &value), GRD_OK);
    ASSERT_STREQ(value, "{\"type\":57,\"value\":\"111\"}");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: GRDDocArraySetApiTest002
 * @tc.desc: Insert to the last location
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR-20078075.015.002
 * @tc.author: wangxiangdong
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArraySetApiTest002, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert one item to db
     * @tc.expected: step1. GRD_OK
     */
    const char *content = "[{\"type\":57, \"value\":\"111\"}]";
    char *value = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = g_tableName, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, UINT32_MAX, content, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step2. insert one item at last location to db
     * @tc.expected: step2. GRD_OK
     */
    const char *content2 = "[{\"type\":57, \"value\":\"222\"}]";
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, UINT32_MAX, content2, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step3. insert one item to db
     * @tc.expected: step3. GRD_OK
     */
    const char *content3 = "[{\"type\":2, \"value\":\"\"}]";
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, UINT32_MAX, content3, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step4. ToArray to get all items
     * @tc.expected: step4. GRD_OK
     */
    char expectValue[200] = {0};
    int ret = sprintf_s(expectValue, sizeof(expectValue),
        "[{\"type\":57,\"value\":\"111\"},{\"type\":57,\"value\":\"222\"},{\"type\":2,"
        "\"elementId\":\"%s_6\"}]",
        g_equipId.c_str());
    ASSERT_LT(0, ret);
    ASSERT_EQ(GRD_ArrayToArray(g_db, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, expectValue);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: GRDDocArraySetApiTest003
 * @tc.desc: Set array to the Map
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR-20078075.015.002
 * @tc.author: wangxiangdong
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArraySetApiTest003, TestSize.Level0)
{
    const char *tableName = "2_map";
    /**
     * @tc.steps: step1. set key and array type value
     * @tc.expected: step1. GRD_OK
     */
    ASSERT_EQ(GRD_MapSet(g_db, tableName, nullptr, "children", "{\"type\":1}"), GRD_OK);
    char *value = nullptr;
    ASSERT_EQ(GRD_MapRead(g_db, tableName, nullptr, "children", &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step2. insert one item to db
     * @tc.expected: step2. GRD_OK
     */
    const char *content = "[{\"type\":57, \"value\":\"111\"}]";
    char *equipId = nullptr;
    ASSERT_EQ(GRD_GetEquipId(g_db, &equipId), GRD_OK);
    GRD_ElementIdT elementId2 = {.equipId = equipId, .incrClock = 0};
    GRD_XmlOpPositionT addr = {.tableName = tableName, .elementId = &elementId2};
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 0, content, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step3. insert one item at last location to db
     * @tc.expected: step3. GRD_OK
     */
    const char *content2 = "[{\"type\":57, \"value\":\"222\"}]";
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, UINT32_MAX, content2, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step4. insert one item to db
     * @tc.expected: step4. GRD_OK
     */
    const char *content3 = "[{\"type\":2, \"value\":\"\"}]";
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 2, content3, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step5. ToArray to get all items
     * @tc.expected: step5. GRD_OK
     */
    char expectValue[200] = {0};
    int ret = sprintf_s(expectValue, sizeof(expectValue),
        "[{\"type\":57,\"value\":\"111\"},{\"type\":57,\"value\":\"222\"},{\"type\":2,"
        "\"elementId\":\"%s_7\"}]",
        g_equipId.c_str());
    ASSERT_LT(0, ret);
    ASSERT_EQ(GRD_ArrayToArray(g_db, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, expectValue);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(equipId), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: GRDDocArraySetApiTest004
 * @tc.desc: Delete more than exist number
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR-20078075.015.002
 * @tc.author: wangxiangdong
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArraySetApiTest004, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert one item to db
     * @tc.expected: step1. GRD_OK
     */
    const char *content = "[{\"type\":57, \"value\":\"111\"}]";
    char *value = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = g_tableName, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 0, content, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step2. insert one item at last location to db
     * @tc.expected: step2. GRD_OK
     */
    const char *content2 = "[{\"type\":57, \"value\":\"222\"}]";
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, UINT32_MAX, content2, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step3. insert one item to db
     * @tc.expected: step3. GRD_OK
     */
    const char *content3 = "[{\"type\":2, \"value\":\"\"}]";
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 2, content3, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step4. Delete from index 1 delete 10 items
     * @tc.expected: step4. GRD_OK
     */
    ASSERT_EQ(GRD_ArrayDelete(g_db, &addr, 1, 10), GRD_OK);
    /**
     * @tc.steps: step5. ToArray to get all items
     * @tc.expected: step5. GRD_OK
     */
    const char *content4 = "[{\"type\":57,\"value\":\"111\"}]";
    ASSERT_EQ(GRD_ArrayToArray(g_db, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, content4);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step6. Delete from index 1 delete 10 items
     * @tc.expected: step6. GRD_INVALID_VALUE
     */
    ASSERT_EQ(GRD_ArrayDelete(g_db, &addr, 1, 10), GRD_ARRAY_INDEX_NOT_FOUND);
    /**
     * @tc.steps: step7. ToArray to get all items
     * @tc.expected: step7. GRD_OK
     */
    ASSERT_EQ(GRD_ArrayToArray(g_db, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, content4);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: GRDDocArraySetApiTest005
 * @tc.desc: Get more than exist number
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR-20078075.015.002
 * @tc.author: wangxiangdong
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArraySetApiTest005, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert one item to db
     * @tc.expected: step1. GRD_OK
     */
    const char *content = "[{\"type\":57, \"value\":\"111\"}]";
    char *value = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = g_tableName, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 0, content, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step2. insert one item at last location to db
     * @tc.expected: step2. GRD_OK
     */
    const char *content2 = "[{\"type\":57, \"value\":\"222\"}]";
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, UINT32_MAX, content2, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step3. delete one item to db
     * @tc.expected: step3. GRD_OK
     */
    ASSERT_EQ(GRD_ArrayDelete(g_db, &addr, 1, 2), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step4. Get index 1
     * @tc.expected: step4. GRD_OK
     */
    ASSERT_EQ(GRD_ArrayGet(g_db, &addr, 1, &value), GRD_ARRAY_INDEX_NOT_FOUND);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
}

/**
 * @tc.name: GRDDocArraySetApiTest006
 * @tc.desc: toArray get []
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR-20078075.015.002
 * @tc.author: wangxiangdong
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArraySetApiTest006, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert one item to db
     * @tc.expected: step1. GRD_OK
     */
    const char *content = "[{\"type\":57, \"value\":\"111\"}]";
    char *value = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = g_tableName, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 0, content, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step2. insert one item at last location to db
     * @tc.expected: step2. GRD_OK
     */
    const char *content2 = "[{\"type\":57, \"value\":\"222\"}]";
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 1, content2, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step3. delete one item to db
     * @tc.expected: step3. GRD_OK
     */
    ASSERT_EQ(GRD_ArrayDelete(g_db, &addr, 0, 2), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step4. Get index 1
     * @tc.expected: step4. GRD_OK
     */
    ASSERT_EQ(GRD_ArrayToArray(g_db, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
}

/**
 * @tc.name: GRDDocArraySetApiTest007
 * @tc.desc: push muti items to array one time
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR-20078075.015.002
 * @tc.author: wangxiangdong
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArraySetApiTest007, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert muti item to db
     * @tc.expected: step1. GRD_OK
     */
    const char *content =
        "[{\"type\":57, \"value\":\"111\"},{\"type\":57, \"value\":\"222\"},{\"type\":57, \"value\":\"333\"}]";
    char *value = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = g_tableName, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, UINT32_MAX, content, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step2. toarray to check result
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(GRD_ArrayToArray(g_db, &addr, &value), GRD_OK);
    ASSERT_STREQ(
        value, "[{\"type\":57,\"value\":\"111\"},{\"type\":57,\"value\":\"222\"},{\"type\":57,\"value\":\"333\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
}

/**
 * @tc.name: GRDDocArraySetApiTest008
 * @tc.desc: index > itemsCount
 * @tc.type: FUNC
 * @tc.require: DTS2024050814412
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArraySetApiTest008, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert one item to db
     * @tc.expected: step1. GRD_OK
     */
    const char *content1 = "[{\"type\":57, \"value\":\"111\"}]";
    char *value = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = g_tableName, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 0, content1, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    const char *content2 = "[{\"type\":57, \"value\":\"222\"}]";
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 1, content2, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    const char *content3 = "[{\"type\":57, \"value\":\"333\"}]";
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 2, content3, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_ArrayToArray(g_db, &addr, &value), GRD_OK);
    ASSERT_STREQ(
        value, "[{\"type\":57,\"value\":\"111\"},{\"type\":57,\"value\":\"222\"},{\"type\":57,\"value\":\"333\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    const char *content4 = "[{\"type\":57, \"value\":\"444\"}]";
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 4, content4, &value), GRD_ARRAY_INDEX_NOT_FOUND);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_ArrayToArray(g_db, &addr, &value), GRD_OK);
    ASSERT_STREQ(
        value, "[{\"type\":57,\"value\":\"111\"},{\"type\":57,\"value\":\"222\"},{\"type\":57,\"value\":\"333\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
}

/**
 * @tc.name: GRDDocArraySetApiTest009
 * @tc.desc: array is null, index = 1
 * @tc.type: FUNC
 * @tc.require: DTS2024050721907
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArraySetApiTest009, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert one item to db
     * @tc.expected: step1. GRD_OK
     */
    const char *content1 = "[{\"type\":57, \"value\":\"111\"}]";
    char *value = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = g_tableName, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 1, content1, &value), GRD_ARRAY_INDEX_NOT_FOUND);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: GRDDocArraySetApiTest010
 * @tc.desc: op array with invalid types
 * @tc.type: FUNC
 * @tc.require: DTS2024061229017
 * @tc.author: chentingrong
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArraySetApiTest010, TestSize.Level0)
{
    /* @tc.steps: step1. insert text
     * @tc.expected: step1. GRD_OK
     */
    GRD_XmlOpPositionT addr = {.tableName = "0_text", .elementId = nullptr};

    /**
     * @tc.steps: step2.
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(GRD_TextInsert(g_db, &addr, 0, "123abc", nullptr), GRD_OK);  // 123abc

    /**
     * @tc.steps: step3. insert one item to db
     * @tc.expected: step3. GRD_INVALID_ARGS
     */
    const char *content1 = "[{\"type\":57, \"value\":\"111\"}]";
    char *value = nullptr;
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 0, content1, &value), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step4. delete one item to db
     * @tc.expected: step4. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_ArrayDelete(g_db, &addr, 0, 1), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step5. get one item from db
     * @tc.expected: step5. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_ArrayGet(g_db, &addr, 0, &value), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step6. get array
     * @tc.expected: step6. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_ArrayToArray(g_db, &addr, &value), GRD_INVALID_ARGS);
}

/**
 * @tc.name: GRDDocArraySetApiTest011
 * @tc.desc: op array with invalid table name
 * @tc.type: FUNC
 * @tc.require: DTS2024061229017
 * @tc.author: chentingrong
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArraySetApiTest011, TestSize.Level0)
{
    /* @tc.steps: step1.
     * @tc.expected: step1.
     */
    GRD_XmlOpPositionT addr = {.tableName = "0_text", .elementId = nullptr};

    /**
     * @tc.steps: step2. insert one item to db
     * @tc.expected: step2. GRD_INVALID_ARGS
     */
    const char *content1 = "[{\"type\":57, \"value\":\"111\"}]";
    char *value = nullptr;
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 0, content1, &value), GRD_INVALID_ARGS);
}

/**
 * @tc.name: GRDDocArraySetApiTest012
 * @tc.desc: repeated insert and delete
 * @tc.type: FUNC
 * @tc.require: DTS2024061910414
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArraySetApiTest012, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert one item to db
     * @tc.expected: step1. GRD_OK
     */
    const char *content1 = "[{\"type\":57, \"value\":\"111\"},{\"type\":57, \"value\":\"222\"},{\"type\":57, "
                           "\"value\":\"333\"},{\"type\":57,\"value\":\"444\"}]";
    char *value = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = g_tableName, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 0, content1, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    const char *content2 =
        "[{\"type\":57, \"value\":\"a\"},{\"type\":57, \"value\":\"b\"},{\"type\":57, \"value\":\"c\"}]";
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 2, content2, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_ArrayDelete(g_db, &addr, 2, 3), GRD_OK);

    ASSERT_EQ(GRD_ArrayToArray(g_db, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"111\"},{\"type\":57,\"value\":\"222\"},{\"type\":57,\"value\":"
                        "\"333\"},{\"type\":57,\"value\":\"444\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    const char *content3 =
        "[{\"type\":57, \"value\":\"d\"},{\"type\":57, \"value\":\"e\"},{\"type\":57, \"value\":\"f\"}]";
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 3, content3, &value), GRD_OK);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_ArrayDelete(g_db, &addr, 3, 3), GRD_OK);

    ASSERT_EQ(GRD_ArrayToArray(g_db, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"111\"},{\"type\":57,\"value\":\"222\"},{\"type\":57,\"value\":"
                        "\"333\"},{\"type\":57,\"value\":\"444\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: GRDDocArraySetApiTest013
 * @tc.desc: should return GRD_INVALID_ARGS when get element go wrong
 * @tc.type: FUNC
 * @tc.require: DTS2024061916250
 * @tc.author: suyuchen
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArraySetApiTest013, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert a xmlText to the xmlFragment
     * @tc.expected: step1. GRD_OK
     */
    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };
    GRD_XmlOpPosition addr = {.tableName = "5_fragment", .elementId = nullptr};
    GRD_ElementIdT *elementId1 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(g_db, &addr, 0, &nodeXmlText, &elementId1), GRD_OK);

    /**
     * @tc.steps: step2. insert a xmlText to the xmlFragment
     * @tc.expected: step2. GRD_INVALID_ARGS
     */
    const char *content1 = "[{\"type\":57, \"value\":\"111\"}]";
    char *value = nullptr;
    GRD_XmlOpPositionT arrayAddr = {.tableName = "1_array", .elementId = elementId1};
    ASSERT_EQ(GRD_ArrayInsert(g_db, &arrayAddr, 0, content1, &value), GRD_INVALID_ARGS);
    GRD_DocFree(value);
    GRD_XmlFreeElementId(elementId1);
}

/**
 * @tc.name: GRDDocArraySetApiTest014
 * @tc.desc: array insert with OK parameters should success when previous insert failed
 * @tc.type: FUNC
 * @tc.require: DTS2024070416012
 * @tc.author: suyuchen
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArraySetApiTest014, TestSize.Level0)
{
    char *equipId = nullptr;
    ASSERT_EQ(GRD_GetEquipId(g_db, &equipId), GRD_OK);
    GRD_ElementIdT elementId = {.equipId = equipId, .incrClock = 0};
    GRD_XmlOpPosition addr = {.tableName = "1_array", .elementId = &elementId};

    /**
     * @tc.steps: step2 insert array where parent element is not null
     * @tc.expected: step2. GRD_INVALID_ARGS
     */
    char *resp = nullptr;
    const char *content = "[{\"type\":57, \"value\":\"111\"}]";
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 0, content, &resp), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step3 insert array where parent element is null
     * @tc.expected: step3. GRD_OK
     */
    addr.elementId = nullptr;
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 0, content, &resp), GRD_OK);
    GRD_DocFree(resp);
    GRD_DocFree(equipId);
}

/**
 * @tc.name: GRDDocArrayParamTest001
 * @tc.desc: test array insert api when para invalid
 * @tc.type: FUNC
 * @tc.require: DTS2024073106613
 * @tc.author: suyuchen
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArrayParamTest001, TestSize.Level0)
{
    /**
     * @tc.steps: step1. call array insert when tableName is null
     * @tc.expected: step1. GRD_INVALID_ARGS
     */
    GRD_XmlOpPositionT addr = {
        .tableName = nullptr,
        .elementId = nullptr,
    };
    char *rspValue = nullptr;
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 0, nullptr, &rspValue), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step2. call array insert when db is null
     * @tc.expected: step2. GRD_INVALID_ARGS
     */
    addr.tableName = g_tableName;
    ASSERT_EQ(GRD_ArrayInsert(nullptr, &addr, 0, nullptr, &rspValue), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step3. call array insert when equipId is null
     * @tc.expected: step3. GRD_INVALID_ARGS
     */
    GRD_ElementIdT elementId = {.equipId = nullptr, .incrClock = 0};
    addr.elementId = &elementId;
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 0, nullptr, &rspValue), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step4. call array insert when rspValue is null
     * @tc.expected: step4. GRD_INVALID_ARGS
     */
    addr.elementId = nullptr;
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 0, nullptr, nullptr), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step5. call array insert when *rspValue is not null
     * @tc.expected: step5. GRD_INVALID_ARGS
     */
    const size_t size = 8;
    rspValue = static_cast<char *>(malloc(size));
    ASSERT_NE(rspValue, nullptr);
    ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 0, nullptr, &rspValue), GRD_INVALID_ARGS);
    free(rspValue);
    rspValue = nullptr;
}

/**
 * @tc.name: GRDDocArrayParamTest002
 * @tc.desc: test array delete api when para invalid
 * @tc.type: FUNC
 * @tc.require: DTS2024073106613
 * @tc.author: suyuchen
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArrayParamTest002, TestSize.Level0)
{
    /**
     * @tc.steps: step1. call array delete when length is 0
     * @tc.expected: step1. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_ArrayDelete(g_db, nullptr, 0, 0), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step2. call array delete when tableName is null
     * @tc.expected: step2. GRD_INVALID_ARGS
     */
    GRD_XmlOpPositionT addr = {
        .tableName = nullptr,
        .elementId = nullptr,
    };
    ASSERT_EQ(GRD_ArrayDelete(g_db, &addr, 0, 1), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step3. call array delete when db is null
     * @tc.expected: step3. GRD_INVALID_ARGS
     */
    addr.tableName = g_tableName;
    ASSERT_EQ(GRD_ArrayDelete(nullptr, &addr, 0, 1), GRD_INVALID_ARGS);
}

/**
 * @tc.name: GRDDocArrayParamTest003
 * @tc.desc: test array get api when para invalid
 * @tc.type: FUNC
 * @tc.require: DTS2024073106613
 * @tc.author: suyuchen
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArrayParamTest003, TestSize.Level0)
{
    /**
     * @tc.steps: step1. call GRD_ArrayGet when value is null
     * @tc.expected: step1. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_ArrayGet(nullptr, nullptr, 0, nullptr), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step2. call GRD_ArrayGet when *value is not null
     * @tc.expected: step2. GRD_INVALID_ARGS
     */
    const size_t size = 8;
    char *value = static_cast<char *>(malloc(size));
    ASSERT_NE(value, nullptr);
    ASSERT_EQ(GRD_ArrayGet(nullptr, nullptr, 0, &value), GRD_INVALID_ARGS);
    free(value);
    value = nullptr;

    /**
     * @tc.steps: step3. call GRD_ArrayGet when tableName is null
     * @tc.expected: step3. GRD_INVALID_ARGS
     */
    GRD_XmlOpPositionT addr = {
        .tableName = nullptr,
        .elementId = nullptr,
    };
    ASSERT_EQ(GRD_ArrayGet(nullptr, &addr, 0, &value), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step4. call GRD_ArrayGet when db is null
     * @tc.expected: step4. GRD_INVALID_ARGS
     */
    addr.tableName = g_tableName;
    ASSERT_EQ(GRD_ArrayGet(nullptr, &addr, 0, &value), GRD_INVALID_ARGS);
}

/**
 * @tc.name: GRDDocArrayParamTest004
 * @tc.desc: test array to array api when para invalid
 * @tc.type: FUNC
 * @tc.require: DTS2024073106613
 * @tc.author: suyuchen
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArrayParamTest004, TestSize.Level0)
{
    /**
     * @tc.steps: step1. call GRD_ArrayToArray when value is null
     * @tc.expected: step1. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_ArrayToArray(nullptr, nullptr, nullptr), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step2. call GRD_ArrayToArray when *value is not null
     * @tc.expected: step2. GRD_INVALID_ARGS
     */
    const size_t size = 8;
    char *value = static_cast<char *>(malloc(size));
    ASSERT_NE(value, nullptr);
    ASSERT_EQ(GRD_ArrayToArray(nullptr, nullptr, &value), GRD_INVALID_ARGS);
    free(value);
    value = nullptr;

    /**
     * @tc.steps: step3. call GRD_ArrayToArray when tableName is null
     * @tc.expected: step3. GRD_INVALID_ARGS
     */
    GRD_XmlOpPositionT addr = {
        .tableName = nullptr,
        .elementId = nullptr,
    };
    ASSERT_EQ(GRD_ArrayToArray(g_db, &addr, &value), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step4. call GRD_ArrayToArray when db is null
     * @tc.expected: step4. GRD_INVALID_ARGS
     */
    addr.tableName = g_tableName;
    ASSERT_EQ(GRD_ArrayToArray(nullptr, &addr, &value), GRD_INVALID_ARGS);
}

/**
 * @tc.name: GRDDocArrayParamTest005
 * @tc.desc: test array insert invalid string
 * @tc.type: FUNC
 * @tc.require: DTS2024080813393
 * @tc.author: lg
 */
HWTEST_F(GRDDocArrayApiTest, GRDDocArrayParamTest005, TestSize.Level0)
{
    /**
     * @tc.steps: step1 init doc
     * @tc.expected: step1. ok
     */
    char *equipId = nullptr;
    ASSERT_EQ(GRD_GetEquipId(g_db, &equipId), GRD_OK);
    GRD_ElementIdT elementId = {.equipId = equipId, .incrClock = 0};
    GRD_XmlOpPosition addr = {.tableName = "1_array", .elementId = &elementId};

    /**
     * @tc.steps: step2 insert array with invalid string
     * @tc.expected: step2. GRD_INVALID_ARGS
     */
    char *resp = nullptr;
    vector<char> invalidChar;
    for (int i = 128; i <= 191; ++i) {
        invalidChar.push_back(i);
    }
    addr.elementId = nullptr;
    for (auto item : invalidChar) {
        std::string content = "[{\"type\":57, \"value\":\"";
        content.push_back(item);
        content += "\"}]";
        ASSERT_EQ(GRD_ArrayInsert(g_db, &addr, 0, content.c_str(), &resp), GRD_INVALID_ARGS);
    }
    GRD_DocFree(equipId);
}
