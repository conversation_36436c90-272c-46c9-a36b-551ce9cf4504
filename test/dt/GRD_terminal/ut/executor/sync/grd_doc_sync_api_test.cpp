/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "grd_sync_api.h"

#include "gtest/gtest.h"

#include <condition_variable>
#include <mutex>
#include <thread>

#include "cJSON.h"
#include "db_mem_context.h"
#include "db_test_tool.h"
#include "de_equip_info.h"
#include "grd_clouddb_impl.h"
#include "grd_db_api.h"
#include "grd_doc_api.h"
#include "grd_error.h"
#include "grd_kv_api.h"
#include "grd_xml_fragment_api.h"
#include "grd_xml_element_api.h"
#include "grd_text_api.h"
#include "grd_array_api.h"
#include "grd_map_api.h"
#include "grd_type_export.h"
#include "store_limits.h"
#include "store_type.h"
#include "sync_test_tool.h"

using namespace testing::ext;
using namespace std;
using namespace sync_test_tool;

static const char *TEST_DB_PATH = "./equipAData";
static const char *TEST_DB_FILE_A = "./equipAData/testFile";
static const char *TEST_DB_CONFIG = R"({"sharedModeEnable":1,"redoFlushByTrx":1})";
static const char *TEST_DB_B = "./equipBData";
static const char *TEST_DB_FILE_B = "./equipBData/testFile";
static const char *TEST_DB_C = "./equipCData";
static const char *TEST_DB_FILE_C = "./equipCData/testFile";
static const char *TABLE_NAME = "5_fragment";
static const char *ARRAY_TABLE_NAME = "1_array";
static const char *MAP_TABLE_NAME = "2_map";
static char g_equipIdA[] = "A";
static char g_equipIdB[] = "B";

const uint8_t THREAD_NUM = 10;
static const int64_t SYNC_WAIT_TIME = 600;

static std::condition_variable g_processCondition;
static std::mutex g_processMutex;
static GRD_DB *EQUIPA_DB = nullptr;
static GRD_DB *EQUIPB_DB = nullptr;
static GRD_DB *EQUIPC_DB = nullptr;

using updownload = bool;
updownload g_testUpload = true;
updownload g_testDownload = false;

class GRDDocSyncApiTest : public testing::Test {
public:
    static void SetUpTestCase(void)
    {}

    static void TearDownTestCase(void)
    {}

    void SetUp()
    {
        GrdCloudDb::SetUpTestCase();
        EXPECT_EQ(DbTestTool::RemoveDir(TEST_DB_PATH), 0);
        EXPECT_EQ(DbTestTool::MakeDir(TEST_DB_PATH), 0);
        ASSERT_EQ(GRD_DBOpen(TEST_DB_FILE_A, TEST_DB_CONFIG, GRD_DB_OPEN_CREATE, &EQUIPA_DB), GRD_OK);
        ASSERT_EQ(GRD_SetEquipId(EQUIPA_DB, "A"), GRD_OK);

        // EquipB
        EXPECT_EQ(DbTestTool::RemoveDir(TEST_DB_B), 0);
        EXPECT_EQ(DbTestTool::MakeDir(TEST_DB_B), 0);
        ASSERT_EQ(GRD_DBOpen(TEST_DB_FILE_B, TEST_DB_CONFIG, GRD_DB_OPEN_CREATE, &EQUIPB_DB), GRD_OK);
        ASSERT_EQ(GRD_SetEquipId(EQUIPB_DB, "B"), GRD_OK);
        // EquipC
        EXPECT_EQ(DbTestTool::RemoveDir(TEST_DB_C), 0);
        EXPECT_EQ(DbTestTool::MakeDir(TEST_DB_C), 0);
        ASSERT_EQ(GRD_DBOpen(TEST_DB_FILE_C, TEST_DB_CONFIG, GRD_DB_OPEN_CREATE, &EQUIPC_DB), GRD_OK);
        ASSERT_EQ(GRD_SetEquipId(EQUIPC_DB, "C"), GRD_OK);
    }

    void TearDown()
    {
        GrdCloudDb::TearDownTestCase();
        ASSERT_EQ(GRD_DBClose(EQUIPA_DB, GRD_DB_CLOSE), GRD_OK);
        EXPECT_EQ(DbTestTool::RemoveDir(TEST_DB_PATH), 0);
        // remove equip B
        ASSERT_EQ(GRD_DBClose(EQUIPB_DB, GRD_DB_CLOSE), GRD_OK);
        EXPECT_EQ(DbTestTool::RemoveDir(TEST_DB_B), 0);
        // remove equip C
        ASSERT_EQ(GRD_DBClose(EQUIPC_DB, GRD_DB_CLOSE), GRD_OK);
        EXPECT_EQ(DbTestTool::RemoveDir(TEST_DB_C), 0);
        global_status = GRD_SYNC_PROCESS_PREPARED;
        g_uploadRecordSize = 0u;
        g_downloadRecordSize = 0u;
    }
};

static void CheckXmlElement(const char *nodeName, GRD_ElementIdT *elementId, GRD_DocNodeTypeE type, char *respXml)
{
    char excp[200] = {0};
    int ret = 0;
    if (type == 4) {
        ret = sprintf_s(excp, sizeof(excp), "[{\"nodeName\":\"%s\",\"type\":\"%s\",\"elementId\":\"%s_%lu\"}]",
            nodeName, "XML_ELEMENT", elementId->equipId, elementId->incrClock);
    } else if (type == 3) {
        ret = sprintf_s(excp, sizeof(excp), "[{\"type\":\"%s\",\"elementId\":\"%s_%lu\"}]", "XML_TEXT",
            elementId->equipId, elementId->incrClock);
    }
    ASSERT_LT(0, ret);
    // std::cout << excp << std::endl;
    ASSERT_STREQ(excp, respXml);
}

/**
 * @tc.name: GRDDocSyncBasicTest001
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest001, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlElement to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };
    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &elementId), GRD_OK);
    GRD_XmlOpPositionT elemAddr = {.tableName = TABLE_NAME, .elementId = elementId};

    /* @tc.steps: step2. set attribute to XmlElement
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(
        GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "uuid", "602393a0-34a4-44c5-bfbd-fa520bd82df9", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "textAlign", "left", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "indent", "0", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "level", "1", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "number", "", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "id", "heading-1", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "uuid", "12345", 0), GRD_OK);  // same name attr

    /**
     * @tc.steps: step3. read attributes
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr, &value), GRD_OK);
    EXPECT_EQ(
        strcmp(R"({"id":"heading-1","indent":"0","level":"1","number":"","textAlign":"left","uuid":"12345"})", value),
        0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 8u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 8u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr, &value), GRD_OK);
    EXPECT_EQ(
        strcmp(R"({"id":"heading-1","indent":"0","level":"1","number":"","textAlign":"left","uuid":"12345"})", value),
        0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(elementId);
}

/**
 * @tc.name: GRDDocSyncBasicTest002
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest002, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlElement to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };
    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &elementId), GRD_OK);
    GRD_XmlOpPositionT elemAddr = {.tableName = TABLE_NAME, .elementId = elementId};

    /* @tc.steps: step2. set attribute to XmlElement
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(
        GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "uuid", "602393a0-34a4-44c5-bfbd-fa520bd82df9", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "textAlign", "left", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "indent", "0", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "level", "1", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "number", "", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "id", "heading-1", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "uuid", "12345", 0), GRD_OK);  // same name attr

    /**
     * @tc.steps: step3. read attributes
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr, &value), GRD_OK);
    EXPECT_EQ(
        strcmp(R"({"id":"heading-1","indent":"0","level":"1","number":"","textAlign":"left","uuid":"12345"})", value),
        0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. remove attribute
     * @tc.expected: step4. GRD_OK
     */
    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPA_DB, &elemAddr, "textAlign"), GRD_OK);
    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPA_DB, &elemAddr, "uuid"), GRD_OK);

    /**
     * @tc.steps: step5. read attributes
     * @tc.expected: step5. GRD_OK, value is as expected
     */
    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"id":"heading-1","indent":"0","level":"1","number":""})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. call GRD_Sync to upload data, check record size.
     * @tc.expected: step6. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 10u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step7. open equip B and switch to equip B
     * @tc.steps: step7. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 10u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"id":"heading-1","indent":"0","level":"1","number":""})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step8. free elementId
     * @tc.expected: step8. GRD_OK
     */
    GRD_XmlFreeElementId(elementId);
}

/**
 * @tc.name: GRDDocSyncBasicTest003
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest003, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlElement to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };
    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &elementId), GRD_OK);
    GRD_XmlOpPositionT elemAddr = {.tableName = TABLE_NAME, .elementId = elementId};

    /* @tc.steps: step2. set attribute to XmlElement
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(
        GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "uuid", "602393a0-34a4-44c5-bfbd-fa520bd82df9", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "textAlign", "left", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "indent", "0", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "textAlign", "right", 0),
        GRD_OK);  // same name attr
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "number", "", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "id", "heading-1", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "uuid", "12345", 0), GRD_OK);  // same name attr

    /**
     * @tc.steps: step3. read attributes
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"id":"heading-1","indent":"0","number":"","textAlign":"right","uuid":"12345"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. remove attribute
     * @tc.expected: step4. GRD_OK
     */
    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPA_DB, &elemAddr, "textAlign"), GRD_OK);
    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPA_DB, &elemAddr, "uuid"), GRD_OK);

    /**
     * @tc.steps: step5. read attributes
     * @tc.expected: step5. GRD_OK, value is as expected
     */
    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"id":"heading-1","indent":"0","number":""})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step6. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 10u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 10u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"id":"heading-1","indent":"0","number":""})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step8. free elementId
     * @tc.expected: step8. GRD_OK
     */
    GRD_XmlFreeElementId(elementId);
}

/**
 * @tc.name: GRDDocSyncBasicTest004
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest004, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlFragmentElement to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };
    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &elementId), GRD_OK);
    GRD_ElementIdT *elementId1 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 1, &nodeXmlElement, &elementId1), GRD_OK);
    GRD_ElementIdT *elementId2 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 2, &nodeXmlElement, &elementId2), GRD_OK);

    /**
     * @tc.steps: step2. check every XmlElement
     * @tc.expected: step2. GRD_OK
     */
    char *respXml = nullptr;
    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPA_DB, &addr, 0, 1, &respXml), GRD_OK);
    CheckXmlElement(nodeXmlElement.content, elementId, nodeXmlElement.type, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPA_DB, &addr, 1, 1, &respXml), GRD_OK);
    CheckXmlElement(nodeXmlElement.content, elementId1, nodeXmlElement.type, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPA_DB, &addr, 2, 1, &respXml), GRD_OK);
    CheckXmlElement(nodeXmlElement.content, elementId2, nodeXmlElement.type, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPA_DB, &addr, 1, 4294967295, &respXml), GRD_OK);
    char excp[200] = {0};
    int32_t ret = sprintf_s(excp, sizeof(excp),
        "[{\"nodeName\":\"paragraph\",\"type\":\"XML_ELEMENT\",\"elementId\":\"%s_%lu\"},{\"nodeName\":\"paragraph\","
        "\"type\":\"XML_ELEMENT\",\"elementId\":\"%s_%lu\"}]",
        elementId1->equipId, elementId1->incrClock, elementId2->equipId, elementId2->incrClock);
    ASSERT_LT(0, ret);
    // std::cout << excp << std::endl;
    ASSERT_STREQ(excp, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    /**
     * @tc.steps: step3. call GRD_Sync to upload data, check record size.
     * @tc.expected: step3. GRD_OK
     */
    ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 3u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step4. open equip B and switch to equip B
     * @tc.steps: step4. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 3u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    respXml = nullptr;
    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPB_DB, &addr, 0, 1, &respXml), GRD_OK);
    CheckXmlElement(nodeXmlElement.content, elementId, nodeXmlElement.type, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPB_DB, &addr, 1, 1, &respXml), GRD_OK);
    CheckXmlElement(nodeXmlElement.content, elementId1, nodeXmlElement.type, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPB_DB, &addr, 2, 1, &respXml), GRD_OK);
    CheckXmlElement(nodeXmlElement.content, elementId2, nodeXmlElement.type, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPB_DB, &addr, 1, 4294967295, &respXml), GRD_OK);
    ret = sprintf_s(excp, sizeof(excp),
        "[{\"nodeName\":\"paragraph\",\"type\":\"XML_ELEMENT\",\"elementId\":\"%s_%lu\"},{\"nodeName\":\"paragraph\","
        "\"type\":\"XML_ELEMENT\",\"elementId\":\"%s_%lu\"}]",
        elementId1->equipId, elementId1->incrClock, elementId2->equipId, elementId2->incrClock);
    ASSERT_LT(0, ret);
    ASSERT_STREQ(excp, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    /**
     * @tc.steps: step8. free elementId
     * @tc.expected: step8. GRD_OK
     */
    GRD_XmlFreeElementId(elementId);
    GRD_XmlFreeElementId(elementId1);
    GRD_XmlFreeElementId(elementId2);
}

/**
 * @tc.name: GRDDocSyncBasicTest005
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest005, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlElement to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &addr.elementId), GRD_OK);
    ASSERT_EQ(addr.elementId->incrClock, 0);

    /* @tc.steps: step2. set attribute to XmlElement
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "a", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 1, "c", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 1, "b", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "123", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 6, "d", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 1, "4", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 3, "5", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 7, "e", nullptr), GRD_OK);

    /**
     * @tc.steps: step3. read attributes
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"14253abecd"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 9u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 9u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"14253abecd"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(addr.elementId);
}

/**
 * @tc.name: GRDDocSyncBasicTest006
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest006, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlElement to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &addr.elementId), GRD_OK);
    ASSERT_EQ(addr.elementId->incrClock, 0);

    /* @tc.steps: step2. set attribute to XmlElement
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "a", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 1, "c", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 1, "b", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "123", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 6, "d", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 1, "4", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 3, "5", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 7, "e", nullptr), GRD_OK);

    /**
     * @tc.steps: step3. read attributes
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"14253abecd"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 9u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 9u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"14253abecd"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(addr.elementId);
}

/**
 * @tc.name: GRDDocSyncBasicTest007
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest007, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlElement to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &addr.elementId), GRD_OK);
    ASSERT_EQ(addr.elementId->incrClock, 0);

    /* @tc.steps: step2. set attribute to XmlElement
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "a", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 1, "c", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 1, "b", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "123", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 6, "d", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 1, "4", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 3, "5", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 7, "e", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 3, 4), GRD_OK);

    /**
     * @tc.steps: step3. read attributes
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"142ecd"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 10u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 10u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"142ecd"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(addr.elementId);
}

/**
 * @tc.name: GRDDocSyncBasicTest008
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest008, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlElement to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &addr.elementId), GRD_OK);
    ASSERT_EQ(addr.elementId->incrClock, 0);

    /* @tc.steps: step2. set attribute to XmlElement
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "a", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 1, "c", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 1, "b", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "123", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 6, "d", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 1, "4", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 3, "5", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 7, "e", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 0, 4), GRD_OK);

    /**
     * @tc.steps: step3. read attributes
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"3abecd"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 10u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 10u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"3abecd"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(addr.elementId);
}

/**
 * @tc.name: GRDDocSyncBasicTest009
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest009, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlElement to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &addr.elementId), GRD_OK);
    ASSERT_EQ(addr.elementId->incrClock, 0);

    /* @tc.steps: step2. set attribute to XmlElement
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "abcde", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "12345", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 3, 4), GRD_OK);

    /**
     * @tc.steps: step3. read attributes
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"123cde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 4u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 4u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"123cde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(addr.elementId);
}

/**
 * @tc.name: GRDDocSyncBasicTest0010
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest0010, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlElement to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &addr.elementId), GRD_OK);
    ASSERT_EQ(addr.elementId->incrClock, 0);

    /* @tc.steps: step2. set attribute to XmlElement
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "abcde", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "12345", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 3, 4), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 2, 2), GRD_OK);

    /**
     * @tc.steps: step3. read attributes
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"12de"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 5u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 5u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"12de"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(addr.elementId);
}

/**
 * @tc.name: GRDDocSyncBasicTest011
 * @tc.desc: test log write and log apply, A->B->A->B
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest0011, TestSize.Level0)
{
    string cmpstr = "";
    /**
     * @tc.steps: step1. insert XmlElement to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };
    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &elementId), GRD_OK);
    GRD_XmlOpPositionT elemAddr = {.tableName = TABLE_NAME, .elementId = elementId};

    /* @tc.steps: step2. set attribute to XmlElement
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(
        GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "uuid", "602393a0-34a4-44c5-bfbd-fa520bd82df9", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "textAlign", "left", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "indent", "0", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "level", "1", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "number", "", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "id", "heading-1", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "uuid", "12345", 0), GRD_OK);  // same name attr

    /**
     * @tc.steps: step3. read attributes
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr, &value), GRD_OK);
    cmpstr = R"({"id":"heading-1","indent":"0","level":"1","number":"","textAlign":"left","uuid":"12345"})";
    EXPECT_EQ(strcmp(cmpstr.c_str(), value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    EXPECT_EQ(g_uploadRecordSize, 8u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    EXPECT_EQ(g_downloadRecordSize, 8u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr, &value), GRD_OK);
    cmpstr = R"({"id":"heading-1","indent":"0","level":"1","number":"","textAlign":"left","uuid":"12345"})";
    EXPECT_EQ(strcmp(cmpstr.c_str(), value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. equipB set attribute to XmlElement .
     * @tc.expected: step6. expect record size is 4 and value is as expected
     */
    // euqipb set Attribute
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr, "number", "8888888866666666", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr, "textAlign", "right", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPB_DB, &elemAddr, "uuid"), GRD_OK);

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr, &value), GRD_OK);
    cmpstr = R"({"id":"heading-1","indent":"0","level":"1","number":"8888888866666666","textAlign":"right"})";
    EXPECT_EQ(strcmp(cmpstr.c_str(), value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step7. call GRD_Sync to upload data, check record size.
     * @tc.expected: step7. GRD_OK
     */
    ret = GRD_Sync(EQUIPB_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    EXPECT_EQ(g_uploadRecordSize, 3u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step8. equipA call GRD_Sync to download data, check record size.
     * @tc.expected: step8. GRD_OK
     */

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPA_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    EXPECT_EQ(g_downloadRecordSize, 3u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step8. log apply, check element.
     * @tc.expected: step8. GRD_OK
     */
    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr, &value), GRD_OK);
    cmpstr = R"({"id":"heading-1","indent":"0","level":"1","number":"8888888866666666","textAlign":"right"})";
    EXPECT_EQ(strcmp(cmpstr.c_str(), value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step9. equipA set attribute to XmlElement .
     * @tc.expected: step9. expect record size is 5 and value is as expected
     */

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "textAlign", "left", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "indent", "12", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "level", "9", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "number", "9999999999", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "uuid", "88888888888", 0), GRD_OK);

    /**
     * @tc.steps: step10. log apply, check element.
     * @tc.expected: step10. GRD_OK
     */
    cmpstr =
        R"({"id":"heading-1","indent":"12","level":"9","number":"9999999999","textAlign":"left","uuid":"88888888888"})";
    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr, &value), GRD_OK);
    EXPECT_EQ(strcmp(cmpstr.c_str(), value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step11. equipA call GRD_Sync to upload data, check record size.
     * @tc.expected: step11. GRD_OK
     */

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    EXPECT_EQ(g_uploadRecordSize, 5u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step12. equipB call GRD_Sync to download data, check record size.
     * @tc.expected: step12. GRD_OK
     */

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    EXPECT_EQ(g_downloadRecordSize, 5u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step13. log apply, check element.
     * @tc.expected: step13. GRD_OK
     */
    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr, &value), GRD_OK);
    cmpstr =
        R"({"id":"heading-1","indent":"12","level":"9","number":"9999999999","textAlign":"left","uuid":"88888888888"})";
    EXPECT_EQ(strcmp(cmpstr.c_str(), value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step14. free elementId
     * @tc.expected: step14. GRD_OK
     */
    GRD_XmlFreeElementId(elementId);
}

/**
 * @tc.name: GRDDocSyncBasicTest0012
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: zhaoliang
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest0012, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlElement to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &addr.elementId), GRD_OK);
    ASSERT_EQ(addr.elementId->incrClock, 0);

    /* @tc.steps: step2. set attribute to XmlElement
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "abcde", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "12345", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 4, 2), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 1, 2), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 3, 2), GRD_OK);

    /**
     * @tc.steps: step3. read attributes
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"14be"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 6u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 6u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"14be"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /* @tc.steps: step6. operation xmlText form deviceB
     * @tc.expected: step6. GRD_OK
     */
    ASSERT_EQ(GRD_TextInsert(EQUIPB_DB, &addr, 2, "pqrst", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPB_DB, &addr, 0, "xy", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPB_DB, &addr, 11, "6789", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPB_DB, &addr, 7, 5), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPB_DB, &addr, 0, 6), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPB_DB, &addr, 2, 2), GRD_OK);

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"r7"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 6u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPA_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 6u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"r7"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(addr.elementId);
}

/**
 * @tc.name: GRDDocSyncBasicTest0013
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: zhaoliang
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest0013, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlElement to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &addr.elementId), GRD_OK);
    ASSERT_EQ(addr.elementId->incrClock, 0);

    /* @tc.steps: step2. set attribute to XmlElement
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "abcde", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "12345", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 4, 2), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 1, 2), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 3, 2), GRD_OK);

    /**
     * @tc.steps: step3. read attributes
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"14be"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 6u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 6u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"14be"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /* @tc.steps: step6. operation xmlText form deviceB
     * @tc.expected: step6. GRD_OK
     */
    ASSERT_EQ(GRD_TextInsert(EQUIPB_DB, &addr, 2, "pqrst", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPB_DB, &addr, 0, "xy", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPB_DB, &addr, 11, "6789", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPB_DB, &addr, 7, 5), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPB_DB, &addr, 0, 6), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPB_DB, &addr, 2, 2), GRD_OK);

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"r7"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 6u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPA_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 6u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"r7"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /* @tc.steps: step7. the second operation on deviceA
     * @tc.expected: step7. GRD_OK
     */
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 2, "abcde", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "12345", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 4, 2), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 6, 4), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 1, 3), GRD_OK);

    value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"17a"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step8. call GRD_Sync to upload data, check record size.
     * @tc.expected: step8. GRD_OK
     */
    ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 5u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step9. open equip B and switch to equip B
     * @tc.steps: step9. call GRD_Sync to download data, check record size.
     * @tc.expected: step9. expect record size is 5 and value is as expected
     */

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 5u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"17a"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(addr.elementId);
}

/**
 * @tc.name: GRDDocSyncBasicTest0014
 * @tc.desc: test fragment insert in second level between equip A and B
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.004
 * @tc.author: huanghe
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest0014, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlFragmentElement to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };
    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &elementId), GRD_OK);
    GRD_ElementIdT *elementId1 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 1, &nodeXmlElement, &elementId1), GRD_OK);
    GRD_ElementIdT *elementId2 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 2, &nodeXmlElement, &elementId2), GRD_OK);
    GRD_ElementIdT *elementId3 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &elementId3), GRD_OK);
    GRD_ElementIdT *elementId4 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &elementId4), GRD_OK);
    GRD_ElementIdT *elementId5 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &elementId5), GRD_OK);

    /**
     * @tc.steps: step2. check every XmlElement
     * @tc.expected: step2. GRD_OK
     */
    char *respXml = nullptr;
    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPA_DB, &addr, 0, 1, &respXml), GRD_OK);
    CheckXmlElement(nodeXmlElement.content, elementId5, nodeXmlElement.type, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPA_DB, &addr, 1, 1, &respXml), GRD_OK);
    CheckXmlElement(nodeXmlElement.content, elementId4, nodeXmlElement.type, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPA_DB, &addr, 2, 1, &respXml), GRD_OK);
    CheckXmlElement(nodeXmlElement.content, elementId3, nodeXmlElement.type, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPA_DB, &addr, 3, 1, &respXml), GRD_OK);
    CheckXmlElement(nodeXmlElement.content, elementId, nodeXmlElement.type, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPA_DB, &addr, 4, 1, &respXml), GRD_OK);
    CheckXmlElement(nodeXmlElement.content, elementId1, nodeXmlElement.type, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPA_DB, &addr, 5, 1, &respXml), GRD_OK);
    CheckXmlElement(nodeXmlElement.content, elementId2, nodeXmlElement.type, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPA_DB, &addr, 0, 4294967295, &respXml), GRD_OK);
    const char *excp =
        R"([{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"A_5"},{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"A_4"},{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"A_3"},{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"A_0"},{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"A_1"},{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"A_2"}])";
    EXPECT_STREQ(excp, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    /**
     * @tc.steps: step3. call GRD_Sync to upload data, check record size.
     * @tc.expected: step3. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    EXPECT_EQ(g_uploadRecordSize, 6u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step4. open equip B and switch to equip B
     * @tc.steps: step4. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    EXPECT_EQ(g_downloadRecordSize, 6u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    respXml = nullptr;
    EXPECT_EQ(GRD_XmlFragmentGet(EQUIPB_DB, &addr, 0, 4294967295, &respXml), GRD_OK);
    EXPECT_STREQ(excp, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    /**
     * @tc.steps: step5. insert xmlElement in equip B
     * @tc.expected: step5. GRD_OK
     */
    GRD_ElementIdT *elementId6 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPB_DB, &addr, 0, &nodeXmlElement, &elementId6), GRD_OK);
    GRD_ElementIdT *elementId7 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPB_DB, &addr, 4, &nodeXmlElement, &elementId7), GRD_OK);
    GRD_ElementIdT *elementId8 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPB_DB, &addr, 8, &nodeXmlElement, &elementId8), GRD_OK);

    respXml = nullptr;
    EXPECT_EQ(GRD_XmlFragmentGet(EQUIPB_DB, &addr, 0, 4294967295, &respXml), GRD_OK);
    const char *excp2 =
        R"([{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"B_0"},{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"A_5"},{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"A_4"},{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"A_3"},{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"B_1"},{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"A_0"},{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"A_1"},{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"A_2"},{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"B_2"}])";
    EXPECT_STREQ(excp2, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    /**
     * @tc.steps: step6. sync equip B to A
     * @tc.expected: step6. GRD_OK
     */
    ret = GRD_Sync(EQUIPB_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    EXPECT_EQ(g_uploadRecordSize, 3u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPA_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    EXPECT_EQ(g_downloadRecordSize, 3u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    respXml = nullptr;
    EXPECT_EQ(GRD_XmlFragmentGet(EQUIPA_DB, &addr, 0, 4294967295, &respXml), GRD_OK);
    EXPECT_STREQ(excp2, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    GRD_XmlFreeElementId(elementId);
    GRD_XmlFreeElementId(elementId1);
    GRD_XmlFreeElementId(elementId2);
    GRD_XmlFreeElementId(elementId3);
    GRD_XmlFreeElementId(elementId4);
    GRD_XmlFreeElementId(elementId5);
    GRD_XmlFreeElementId(elementId6);
    GRD_XmlFreeElementId(elementId7);
    GRD_XmlFreeElementId(elementId8);
}

/**
 * @tc.name: GRDDocSyncBasicTest0015
 * @tc.desc: test fragment insert in nesting level between equip A and B
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.004
 * @tc.author: huanghe
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest0015, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlFragmentElement to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };
    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };
    GRD_ElementIdT *level1ElemId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &level1ElemId), GRD_OK);

    addr.elementId = level1ElemId;
    GRD_ElementIdT *level2ElemId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &level2ElemId), GRD_OK);

    addr.elementId = level2ElemId;
    GRD_ElementIdT *level3ElemId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &level3ElemId), GRD_OK);

    GRD_ElementIdT *textId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlText, &textId), GRD_OK);

    addr.elementId = level1ElemId;
    GRD_ElementIdT *textId1 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlText, &textId1), GRD_OK);

    addr.elementId = nullptr;
    GRD_ElementIdT *textId2 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlText, &textId2), GRD_OK);

    /**
     * @tc.steps: step2. check every XmlElement
     * @tc.expected: step2. GRD_OK
     */
    char *respXml = nullptr;
    addr.elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPA_DB, &addr, 0, 2, &respXml), GRD_OK);
    // cout << respXml << endl;
    const char *expect0 =
        R"([{"type":"XML_TEXT","elementId":"A_5"},{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"A_0"}])";
    ASSERT_STREQ(expect0, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    addr.elementId = level1ElemId;
    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPA_DB, &addr, 0, 2, &respXml), GRD_OK);
    const char *expect1 =
        R"([{"type":"XML_TEXT","elementId":"A_4"},{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"A_1"}])";
    ASSERT_STREQ(expect1, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    addr.elementId = level2ElemId;
    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPA_DB, &addr, 0, 2, &respXml), GRD_OK);
    const char *expect2 =
        R"([{"type":"XML_TEXT","elementId":"A_3"},{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"A_2"}])";
    ASSERT_STREQ(expect2, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    addr.elementId = nullptr;
    char *replyJson = nullptr;
    ASSERT_EQ(GRD_XmlFragmentToString(EQUIPA_DB, TABLE_NAME, nullptr, &replyJson), GRD_OK);
    const char *expectJson1 =
        R"({"array":[{"text":"[]"},{"ele":{"name":"paragraph","attr":{},"children":[{"text":"[]"},{"ele":{"name":"paragraph","attr":{},"children":[{"text":"[]"},{"ele":{"name":"paragraph","attr":{},"children":[]}}]}}]}}]})";
    ASSERT_STREQ(expectJson1, replyJson);
    ASSERT_EQ(GRD_DocFree(replyJson), GRD_OK);
    /**
     * @tc.steps: step3. call GRD_Sync to upload data, check record size.
     * @tc.expected: step3. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    EXPECT_EQ(g_uploadRecordSize, 6u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step4. open equip B and switch to equip B
     * @tc.steps: step4. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    EXPECT_EQ(g_downloadRecordSize, 6u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    respXml = nullptr;
    addr.elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPB_DB, &addr, 0, 2, &respXml), GRD_OK);
    ASSERT_STREQ(expect0, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    addr.elementId = level1ElemId;
    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPB_DB, &addr, 0, 2, &respXml), GRD_OK);
    ASSERT_STREQ(expect1, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    addr.elementId = level2ElemId;
    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPB_DB, &addr, 0, 2, &respXml), GRD_OK);
    ASSERT_STREQ(expect2, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    addr.elementId = nullptr;
    replyJson = nullptr;
    ASSERT_EQ(GRD_XmlFragmentToString(EQUIPB_DB, TABLE_NAME, nullptr, &replyJson), GRD_OK);
    ASSERT_STREQ(expectJson1, replyJson);
    ASSERT_EQ(GRD_DocFree(replyJson), GRD_OK);

    /**
     * @tc.steps: step5. insert xmlText in equip B
     * @tc.expected: step5. GRD_OK
     */
    GRD_ElementIdT *textId3 = nullptr;
    addr.elementId = level2ElemId;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPB_DB, &addr, 0, &nodeXmlElement, &textId3), GRD_OK);

    addr.elementId = level1ElemId;
    GRD_ElementIdT *textId4 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPB_DB, &addr, 0, &nodeXmlElement, &textId4), GRD_OK);

    addr.elementId = nullptr;
    GRD_ElementIdT *textId5 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPB_DB, &addr, 0, &nodeXmlElement, &textId5), GRD_OK);

    respXml = nullptr;
    addr.elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPB_DB, &addr, 0, 3, &respXml), GRD_OK);
    const char *expect3 =
        R"([{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"B_2"},{"type":"XML_TEXT","elementId":"A_5"},{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"A_0"}])";
    ASSERT_STREQ(expect3, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    addr.elementId = level1ElemId;
    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPB_DB, &addr, 0, 3, &respXml), GRD_OK);
    const char *expect4 =
        R"([{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"B_1"},{"type":"XML_TEXT","elementId":"A_4"},{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"A_1"}])";
    ASSERT_STREQ(expect4, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    addr.elementId = level2ElemId;
    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPB_DB, &addr, 0, 3, &respXml), GRD_OK);
    const char *expect5 =
        R"([{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"B_0"},{"type":"XML_TEXT","elementId":"A_3"},{"nodeName":"paragraph","type":"XML_ELEMENT","elementId":"A_2"}])";
    ASSERT_STREQ(expect5, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    addr.elementId = nullptr;
    replyJson = nullptr;
    ASSERT_EQ(GRD_XmlFragmentToString(EQUIPB_DB, TABLE_NAME, nullptr, &replyJson), GRD_OK);
    const char *expectJson2 =
        R"({"array":[{"ele":{"name":"paragraph","attr":{},"children":[]}},{"text":"[]"},{"ele":{"name":"paragraph","attr":{},"children":[{"ele":{"name":"paragraph","attr":{},"children":[]}},{"text":"[]"},{"ele":{"name":"paragraph","attr":{},"children":[{"ele":{"name":"paragraph","attr":{},"children":[]}},{"text":"[]"},{"ele":{"name":"paragraph","attr":{},"children":[]}}]}}]}}]})";
    ASSERT_STREQ(expectJson2, replyJson);
    ASSERT_EQ(GRD_DocFree(replyJson), GRD_OK);

    /**
     * @tc.steps: step6. sync equip B to A
     * @tc.expected: step6. GRD_OK
     */
    ret = GRD_Sync(EQUIPB_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    EXPECT_EQ(g_uploadRecordSize, 3u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPA_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    EXPECT_EQ(g_downloadRecordSize, 3u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    respXml = nullptr;
    addr.elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPA_DB, &addr, 0, 3, &respXml), GRD_OK);
    ASSERT_STREQ(expect3, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    addr.elementId = level1ElemId;
    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPA_DB, &addr, 0, 3, &respXml), GRD_OK);
    ASSERT_STREQ(expect4, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    addr.elementId = level2ElemId;
    ASSERT_EQ(GRD_XmlFragmentGet(EQUIPA_DB, &addr, 0, 3, &respXml), GRD_OK);
    ASSERT_STREQ(expect5, respXml);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);

    addr.elementId = nullptr;
    replyJson = nullptr;
    ASSERT_EQ(GRD_XmlFragmentToString(EQUIPA_DB, TABLE_NAME, nullptr, &replyJson), GRD_OK);
    ASSERT_STREQ(expectJson2, replyJson);
    ASSERT_EQ(GRD_DocFree(replyJson), GRD_OK);

    GRD_XmlFreeElementId(level1ElemId);
    GRD_XmlFreeElementId(level2ElemId);
    GRD_XmlFreeElementId(level3ElemId);
    GRD_XmlFreeElementId(textId);
    GRD_XmlFreeElementId(textId1);
    GRD_XmlFreeElementId(textId2);
    GRD_XmlFreeElementId(textId3);
    GRD_XmlFreeElementId(textId4);
    GRD_XmlFreeElementId(textId5);
}

/**
 * @tc.name: GRDDocSyncBasicTest0016
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest0016, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlFragment to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeFragment = {
        .type = GRD_XML_FRAGMENT_TYPE,
        .content = nullptr,
    };

    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeFragment, &elementId), GRD_OK);
    GRD_ElementIdT *elementId1 = nullptr;
    addr.elementId = elementId;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlElement, &elementId1), GRD_OK);
    GRD_ElementIdT *elementId2 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &elementId2), GRD_OK);
    addr.elementId = elementId2;

    /* @tc.steps: step2. set data
     * @tc.expected: step2. GRD_OK
     */
    GRD_XmlOpPositionT elemAddr1 = {.tableName = TABLE_NAME, .elementId = elementId1};
    ASSERT_EQ(
        GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "uuid", "602393a0-34a4-44c5-bfbd-fa520bd82df9", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "textAlign", "left", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "indent", "0", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "textAlign", "right", 0),
        GRD_OK);  // same name attr
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "number", "", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "id", "heading-1", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "uuid", "12345", 0), GRD_OK);  // same name attr

    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "abcde", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "12345", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 3, 4), GRD_OK);
    /**
     * @tc.steps: step3. read data
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"id":"heading-1","indent":"0","number":"","textAlign":"right","uuid":"12345"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"123cde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 12u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 12u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"id":"heading-1","indent":"0","number":"","textAlign":"right","uuid":"12345"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"123cde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr1, "textAlign", "left", 0),
        GRD_OK);  // same name attr
    ASSERT_EQ(GRD_TextDelete(EQUIPB_DB, &addr, 2, 2), GRD_OK);

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"id":"heading-1","indent":"0","number":"","textAlign":"left","uuid":"12345"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"12de"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ret = GRD_Sync(EQUIPB_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPA_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"id":"heading-1","indent":"0","number":"","textAlign":"left","uuid":"12345"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"12de"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(elementId);
    GRD_XmlFreeElementId(elementId1);
    GRD_XmlFreeElementId(elementId2);
}

/**
 * @tc.name: GRDDocSyncBasicTest0015
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest0017, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlFragment to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeFragment = {
        .type = GRD_XML_FRAGMENT_TYPE,
        .content = nullptr,
    };

    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeFragment, &elementId), GRD_OK);
    GRD_ElementIdT *elementId1 = nullptr;
    addr.elementId = elementId;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlElement, &elementId1), GRD_OK);
    GRD_ElementIdT *elementId2 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &elementId2), GRD_OK);
    addr.elementId = elementId2;

    /* @tc.steps: step2. set data
     * @tc.expected: step2. GRD_OK
     */
    GRD_XmlOpPositionT elemAddr1 = {.tableName = TABLE_NAME, .elementId = elementId1};
    ASSERT_EQ(
        GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "uuid", "602393a0-34a4-44c5-bfbd-fa520bd82df9", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "textAlign", "left", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "indent", "0", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "textAlign", "right", 0),
        GRD_OK);  // same name attr
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "number", "", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "id", "heading-1", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "uuid", "12345", 0), GRD_OK);  // same name attr

    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "abcde", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "12345", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 3, 4), GRD_OK);
    /**
     * @tc.steps: step3. read data
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"id":"heading-1","indent":"0","number":"","textAlign":"right","uuid":"12345"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"123cde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 12u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 12u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"id":"heading-1","indent":"0","number":"","textAlign":"right","uuid":"12345"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"123cde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr1, "textAlign", "left", 0),
        GRD_OK);  // same name attr
    ASSERT_EQ(GRD_TextDelete(EQUIPB_DB, &addr, 2, 2), GRD_OK);

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"id":"heading-1","indent":"0","number":"","textAlign":"left","uuid":"12345"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"12de"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ret = GRD_Sync(EQUIPB_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPA_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"id":"heading-1","indent":"0","number":"","textAlign":"left","uuid":"12345"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"12de"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "textAlign", "middles", 0),
        GRD_OK);  // same name attr
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 1, 2), GRD_OK);

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"id":"heading-1","indent":"0","number":"","textAlign":"middles","uuid":"12345"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"1e"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"id":"heading-1","indent":"0","number":"","textAlign":"middles","uuid":"12345"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"1e"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(elementId);
    GRD_XmlFreeElementId(elementId1);
    GRD_XmlFreeElementId(elementId2);
}

/**
 * @tc.name: GRDDocSyncBasicTest0018
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest0018, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlFragment to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeFragment = {
        .type = GRD_XML_FRAGMENT_TYPE,
        .content = nullptr,
    };

    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeFragment, &elementId), GRD_OK);
    GRD_ElementIdT *elementId1 = nullptr;
    addr.elementId = elementId;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlElement, &elementId1), GRD_OK);
    GRD_ElementIdT *elementId2 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &elementId2), GRD_OK);
    addr.elementId = elementId2;

    /* @tc.steps: step2. set data
     * @tc.expected: step2. GRD_OK
     */

    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "abcde", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "12345", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 3, 4), GRD_OK);
    /**
     * @tc.steps: step3. read data
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"123cde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 5u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 5u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"123cde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    GRD_XmlOpPositionT elemAddr1 = {.tableName = TABLE_NAME, .elementId = elementId1};
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr1, "textAlign", "left", 0),
        GRD_OK);  // same name attr
    ASSERT_EQ(GRD_TextDelete(EQUIPB_DB, &addr, 2, 2), GRD_OK);

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"textAlign":"left"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"12de"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ret = GRD_Sync(EQUIPB_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPA_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"textAlign":"left"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"12de"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "textAlign", "middles", 0),
        GRD_OK);  // same name attr
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 1, 2), GRD_OK);

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"textAlign":"middles"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"1e"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"textAlign":"middles"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"1e"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(elementId);
    GRD_XmlFreeElementId(elementId1);
    GRD_XmlFreeElementId(elementId2);
}

/**
 * @tc.name: GRDDocSyncBasicTest0019
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest0019, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlFragment to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeFragment = {
        .type = GRD_XML_FRAGMENT_TYPE,
        .content = nullptr,
    };

    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeFragment, &elementId), GRD_OK);
    GRD_ElementIdT *elementId1 = nullptr;
    addr.elementId = elementId;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlElement, &elementId1), GRD_OK);
    GRD_ElementIdT *elementId2 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &elementId2), GRD_OK);
    addr.elementId = elementId2;

    /* @tc.steps: step2. set data
     * @tc.expected: step2. GRD_OK
     */

    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "abcde", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "12345", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 3, 4), GRD_OK);
    /**
     * @tc.steps: step3. read data
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"123cde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 5u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 5u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"123cde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    GRD_XmlOpPositionT elemAddr1 = {.tableName = TABLE_NAME, .elementId = elementId1};
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr1, "textAlign", "left", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr1, "index", "6", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr1, "index", "1", 0), GRD_OK);

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"index":"1","textAlign":"left"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"123cde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ret = GRD_Sync(EQUIPB_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 3u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPA_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 3u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"index":"1","textAlign":"left"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"123cde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "textAlign", "middles", 0),
        GRD_OK);  // same name attr

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"index":"1","textAlign":"middles"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"123cde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"index":"1","textAlign":"middles"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"123cde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(elementId);
    GRD_XmlFreeElementId(elementId1);
    GRD_XmlFreeElementId(elementId2);
}

/**
 * @tc.name: GRDDocSyncBasicTest0020
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest0020, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlFragment to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeFragment = {
        .type = GRD_XML_FRAGMENT_TYPE,
        .content = nullptr,
    };

    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeFragment, &elementId), GRD_OK);
    GRD_ElementIdT *elementId1 = nullptr;
    addr.elementId = elementId;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlElement, &elementId1), GRD_OK);
    GRD_ElementIdT *elementId2 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &elementId2), GRD_OK);
    addr.elementId = elementId2;

    /* @tc.steps: step2. set data
     * @tc.expected: step2. GRD_OK
     */

    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "abcde", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "12345", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 3, 4), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 2, 2), GRD_OK);
    /**
     * @tc.steps: step3. read data
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"12de"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 6u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 6u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"12de"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextInsert(EQUIPB_DB, &addr, 0, "new", nullptr), GRD_OK);

    GRD_XmlOpPositionT elemAddr1 = {.tableName = TABLE_NAME, .elementId = elementId1};
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr1, "textAlign", "left", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr1, "index", "6", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr1, "index", "1", 0), GRD_OK);

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"index":"1","textAlign":"left"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"new12de"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ret = GRD_Sync(EQUIPB_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 4u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPA_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 4u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"index":"1","textAlign":"left"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"new12de"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "textAlign", "middles", 0),
        GRD_OK);  // same name attr

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"index":"1","textAlign":"middles"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"new12de"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"index":"1","textAlign":"middles"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"new12de"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(elementId);
    GRD_XmlFreeElementId(elementId1);
    GRD_XmlFreeElementId(elementId2);
}

/**
 * @tc.name: GRDDocSyncBasicTest0021
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest0021, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlFragment to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeFragment = {
        .type = GRD_XML_FRAGMENT_TYPE,
        .content = nullptr,
    };

    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeFragment, &elementId), GRD_OK);
    GRD_ElementIdT *elementId1 = nullptr;
    addr.elementId = elementId;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlElement, &elementId1), GRD_OK);
    GRD_ElementIdT *elementId2 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &elementId2), GRD_OK);
    addr.elementId = elementId2;

    /* @tc.steps: step2. set data
     * @tc.expected: step2. GRD_OK
     */

    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "abcde", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "12345", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 2, 2), GRD_OK);
    /**
     * @tc.steps: step3. read data
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"125abcde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 5u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 5u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"125abcde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextInsert(EQUIPB_DB, &addr, 0, "head", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPB_DB, &addr, 2, 5), GRD_OK);

    GRD_XmlOpPositionT elemAddr1 = {.tableName = TABLE_NAME, .elementId = elementId1};
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr1, "textAlign", "left", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr1, "index", "6", 0), GRD_OK);

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"index":"6","textAlign":"left"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"heabcde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ret = GRD_Sync(EQUIPB_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 4u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPA_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 4u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"index":"6","textAlign":"left"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"heabcde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "textAlign", "middles", 0),
        GRD_OK);  // same name attr

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"index":"6","textAlign":"middles"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"heabcde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"index":"6","textAlign":"middles"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"heabcde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(elementId);
    GRD_XmlFreeElementId(elementId1);
    GRD_XmlFreeElementId(elementId2);
}

/**
 * @tc.name: GRDDocSyncBasicTest0022
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest0022, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlFragment to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeFragment = {
        .type = GRD_XML_FRAGMENT_TYPE,
        .content = nullptr,
    };

    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeFragment, &elementId), GRD_OK);
    GRD_ElementIdT *elementId1 = nullptr;
    addr.elementId = elementId;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlElement, &elementId1), GRD_OK);
    GRD_ElementIdT *elementId2 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &elementId2), GRD_OK);
    addr.elementId = elementId2;

    /* @tc.steps: step2. set data
     * @tc.expected: step2. GRD_OK
     */

    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "abcde", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "12345", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 3, 4), GRD_OK);
    /**
     * @tc.steps: step3. read data
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"123cde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 5u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 5u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"123cde"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    GRD_XmlOpPositionT elemAddr1 = {.tableName = TABLE_NAME, .elementId = elementId1};
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr1, "textAlign", "left", 0),
        GRD_OK);  // same name attr
    ASSERT_EQ(GRD_TextDelete(EQUIPB_DB, &addr, 2, 2), GRD_OK);

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"textAlign":"left"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"12de"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ret = GRD_Sync(EQUIPB_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPA_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"textAlign":"left"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"12de"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "textAlign", "middles", 0),
        GRD_OK);  // same name attr
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 1, 2), GRD_OK);

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"textAlign":"middles"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"1e"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"textAlign":"middles"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"1e"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(elementId);
    GRD_XmlFreeElementId(elementId1);
    GRD_XmlFreeElementId(elementId2);
}

/**
 * @tc.name: GRDDocSyncBasicTest0023
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: zhaoliang
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest0023, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlElement to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &addr.elementId), GRD_OK);
    ASSERT_EQ(addr.elementId->incrClock, 0);

    /* @tc.steps: step2. set attribute to XmlElement
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "a", nullptr), GRD_OK);

    /**
     * @tc.steps: step3. read attributes
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"a"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 1, "b", nullptr), GRD_OK);

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"ab"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"a"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /* @tc.steps: step6. operation xmlText form deviceB
     * @tc.expected: step6. GRD_OK
     */
    ASSERT_EQ(GRD_TextInsert(EQUIPB_DB, &addr, 1, "c", nullptr), GRD_OK);

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"ac"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_upAndDownConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 1u);
    ASSERT_EQ(g_downloadRecordSize, 0u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPA_DB, &g_upAndDownConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 1u);
    ASSERT_EQ(g_downloadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"abc"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_upAndDownConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 0u);
    ASSERT_EQ(g_downloadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"abc"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(addr.elementId);
}

/**
 * @tc.name: GRDDocSyncBasicTest0024
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: zhaoliang
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest0024, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlElement to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &addr.elementId), GRD_OK);
    ASSERT_EQ(addr.elementId->incrClock, 0);

    /* @tc.steps: step2. set attribute to XmlElement
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "c", nullptr), GRD_OK);

    /**
     * @tc.steps: step3. read attributes
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"c"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "b", nullptr), GRD_OK);

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"bc"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"c"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /* @tc.steps: step6. operation xmlText form deviceB
     * @tc.expected: step6. GRD_OK
     */
    ASSERT_EQ(GRD_TextInsert(EQUIPB_DB, &addr, 0, "a", nullptr), GRD_OK);

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"ac"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_upAndDownConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 1u);
    ASSERT_EQ(g_downloadRecordSize, 0u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPA_DB, &g_upAndDownConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 1u);
    ASSERT_EQ(g_downloadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"bac"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_upAndDownConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 0u);
    ASSERT_EQ(g_downloadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"bac"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(addr.elementId);
}

/**
 * @tc.name: GRDDocSyncBasicTest0025
 * @tc.desc: test log write and log apply
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: zhaoliang
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest0025, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlElement to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &addr.elementId), GRD_OK);
    ASSERT_EQ(addr.elementId->incrClock, 0);

    /* @tc.steps: step2. set attribute to XmlElement
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "ad", nullptr), GRD_OK);

    /**
     * @tc.steps: step3. read attributes
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"ad"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 1, "b", nullptr), GRD_OK);

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"abd"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 2u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"ad"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /* @tc.steps: step6. operation xmlText form deviceB
     * @tc.expected: step6. GRD_OK
     */
    ASSERT_EQ(GRD_TextInsert(EQUIPB_DB, &addr, 1, "c", nullptr), GRD_OK);

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"acd"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_upAndDownConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 1u);
    ASSERT_EQ(g_downloadRecordSize, 0u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPA_DB, &g_upAndDownConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 1u);
    ASSERT_EQ(g_downloadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"abcd"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_upAndDownConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 0u);
    ASSERT_EQ(g_downloadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"abcd"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(addr.elementId);
}

/**
 * @tc.name: GRDDocSyncBasicTest0026
 * @tc.desc: A and B repeat set delete Attribute conflict
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncBasicTest0026, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert XmlFragment to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeFragment = {
        .type = GRD_XML_FRAGMENT_TYPE,
        .content = nullptr,
    };

    GRD_DocNodeInfoT nodeXmlElement1 = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };

    GRD_DocNodeInfoT nodeXmlElement2 = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "heading",
    };

    GRD_DocNodeInfoT nodeXmlText = {.type = GRD_XML_TEXT_TYPE, .content = nullptr};

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_XmlOpPositionT addr1 = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_XmlOpPositionT addr2 = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_XmlOpPositionT addr3 = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_XmlOpPositionT addr4 = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeFragment, &elementId), GRD_OK);
    addr.elementId = elementId;
    GRD_ElementIdT *elementId1 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlElement1, &elementId1), GRD_OK);
    addr1.elementId = elementId1;
    GRD_ElementIdT *elementId2 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr1, 0u, &nodeXmlText, &elementId2), GRD_OK);
    addr2.elementId = elementId2;

    ASSERT_EQ(GRD_TextApplyDelta(EQUIPA_DB, &addr2, 0, "[{\"insert\":\"1\n\"}]"), GRD_OK);
    /**
     * @tc.steps: step3. read data
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr2, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"1"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr2, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"1"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    GRD_ElementIdT *elementId3 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPB_DB, &addr, 0u, &nodeXmlElement2, &elementId3), GRD_OK);
    addr3.elementId = elementId3;
    GRD_ElementIdT *elementId4 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPB_DB, &addr3, 0u, &nodeXmlText, &elementId4), GRD_OK);
    addr4.elementId = elementId4;

    ASSERT_EQ(GRD_TextApplyDelta(EQUIPB_DB, &addr4, 0, "[{\"insert\":\"222\n\"}]"), GRD_OK);

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr2, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"1"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr4, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"222"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ret = GRD_Sync(EQUIPB_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPA_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr2, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"1"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr4, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"222"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr4, 3, "3", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextApplyDelta(EQUIPA_DB, &addr4, 0, "[{\"retain\":4}]"), GRD_OK);

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr4, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"2223"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    GRD_XmlOpPositionT elemAddr1 = {.tableName = TABLE_NAME, .elementId = elementId1};
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "level", "2", 0), GRD_OK);

    ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr2, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"1"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr4, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"2223"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    GRD_XmlOpPositionT elemAddr3 = {.tableName = TABLE_NAME, .elementId = elementId3};
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr3, "level", "3", 0), GRD_OK);

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"level":"2"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr3, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"level":"3"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ret = GRD_Sync(EQUIPB_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPA_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr2, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"1"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr4, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"2223"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"level":"2"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr3, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"level":"3"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(elementId);
    GRD_XmlFreeElementId(elementId1);
    GRD_XmlFreeElementId(elementId2);
    GRD_XmlFreeElementId(elementId3);
    GRD_XmlFreeElementId(elementId4);
}

/**
 * @tc.name: SyncArrayBasicTest001
 * @tc.desc:
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.015.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, SyncArrayBasicTest001, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert item to array
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    /**
     * @tc.steps: step2. insert data
     * @tc.expected: step2. GRD_OK, value is as expected
     */
    char content[] = "[{\"type\":57, \"value\":\"1\"}]";
    char *elemIdValue = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 0, content, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content2[] = "[{\"type\":57, \"value\":\"22\"}]";
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 1, content2, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content3[] = "[{\"type\":57, \"value\":\"333\"}]";
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 2, content3, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    /**
     * @tc.steps: step3. read data
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(
        value, "[{\"type\":57,\"value\":\"1\"},{\"type\":57,\"value\":\"22\"},{\"type\":57,\"value\":\"333\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 3u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 3u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(
        value, "[{\"type\":57,\"value\":\"1\"},{\"type\":57,\"value\":\"22\"},{\"type\":57,\"value\":\"333\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: SyncArrayBasicTest002
 * @tc.desc:
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.015.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, SyncArrayBasicTest002, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert item to array
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    /**
     * @tc.steps: step2. insert data
     * @tc.expected: step2. GRD_OK, value is as expected
     */
    char content[] = "[{\"type\":57, \"value\":\"1\"}]";
    char *elemIdValue = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 0, content, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content2[] = "[{\"type\":57, \"value\":\"22\"}]";
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 1, content2, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content3[] = "[{\"type\":57, \"value\":\"333\"}]";
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 1, content3, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    /**
     * @tc.steps: step3. read data
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(
        value, "[{\"type\":57,\"value\":\"1\"},{\"type\":57,\"value\":\"333\"},{\"type\":57,\"value\":\"22\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 3u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 3u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(
        value, "[{\"type\":57,\"value\":\"1\"},{\"type\":57,\"value\":\"333\"},{\"type\":57,\"value\":\"22\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: SyncArrayBasicTest003
 * @tc.desc:
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.015.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, SyncArrayBasicTest003, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert item to array
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    /**
     * @tc.steps: step2. insert data
     * @tc.expected: step2. GRD_OK, value is as expected
     */
    char content[] = "[{\"type\":57, \"value\":\"1\"}]";
    char *elemIdValue = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 0, content, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content2[] = "[{\"type\":57, \"value\":\"22\"}]";
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 1, content2, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content3[] = "[{\"type\":57, \"value\":\"333\"}]";
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 2, content3, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    ASSERT_EQ(GRD_ArrayDelete(EQUIPA_DB, &addr, 1, 1), GRD_OK);

    /**
     * @tc.steps: step3. read data
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"1\"},{\"type\":57,\"value\":\"333\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 4u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 4u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"1\"},{\"type\":57,\"value\":\"333\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: SyncArrayBasicTest004
 * @tc.desc:
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.015.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, SyncArrayBasicTest004, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert item to array
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    /**
     * @tc.steps: step2. insert data
     * @tc.expected: step2. GRD_OK, value is as expected
     */
    char content[] = "[{\"type\":57, \"value\":\"1\"}]";
    char *elemIdValue = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 0, content, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content2[] = "[{\"type\":57, \"value\":\"22\"}]";
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 1, content2, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content3[] = "[{\"type\":57, \"value\":\"333\"}]";
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 2, content3, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    ASSERT_EQ(GRD_ArrayDelete(EQUIPA_DB, &addr, 1, 2), GRD_OK);

    /**
     * @tc.steps: step3. read data
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"1\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 4u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 4u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"1\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: SyncArrayBasicTest005
 * @tc.desc:
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.015.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, SyncArrayBasicTest005, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert item to array
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    /**
     * @tc.steps: step2. insert data
     * @tc.expected: step2. GRD_OK, value is as expected
     */
    char content[] = "[{\"type\":57, \"value\":\"1\"}]";
    char *elemIdValue = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 0, content, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content2[] = "[{\"type\":57, \"value\":\"22\"}]";
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 0, content2, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content3[] = "[{\"type\":57, \"value\":\"333\"}]";
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 0, content3, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content4[] = "[{\"type\":57, \"value\":\"4444\"}]";
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 3, content4, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    ASSERT_EQ(GRD_ArrayDelete(EQUIPA_DB, &addr, 3, 1), GRD_OK);

    /**
     * @tc.steps: step3. read data
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(
        value, "[{\"type\":57,\"value\":\"333\"},{\"type\":57,\"value\":\"22\"},{\"type\":57,\"value\":\"1\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 5u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 5u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(
        value, "[{\"type\":57,\"value\":\"333\"},{\"type\":57,\"value\":\"22\"},{\"type\":57,\"value\":\"1\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: SyncArrayBasicTest006
 * @tc.desc:
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.015.002
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, SyncArrayBasicTest006, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert item to array
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    /**
     * @tc.steps: step2. insert data
     * @tc.expected: step2. GRD_OK, value is as expected
     */
    char content[] = "[{\"type\":57, \"value\":\"1\"}]";
    char *elemIdValue = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, UINT32_MAX, content, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content2[] = "[{\"type\":57, \"value\":\"22\"}]";
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, UINT32_MAX, content2, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content3[] = "[{\"type\":57, \"value\":\"333\"}]";
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, UINT32_MAX, content3, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content4[] = "[{\"type\":57, \"value\":\"4444\"}]";
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 3, content4, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    ASSERT_EQ(GRD_ArrayDelete(EQUIPA_DB, &addr, 3, 1), GRD_OK);

    /**
     * @tc.steps: step3. read data
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(
        value, "[{\"type\":57,\"value\":\"1\"},{\"type\":57,\"value\":\"22\"},{\"type\":57,\"value\":\"333\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 5u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     * @tc.expected: step5. expect record size is 4 and value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 5u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(
        value, "[{\"type\":57,\"value\":\"1\"},{\"type\":57,\"value\":\"22\"},{\"type\":57,\"value\":\"333\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: SyncArrayBasicTest007
 * @tc.desc:
 * @tc.type: FUNC
 * @tc.require: DTS2024052413305
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, SyncArrayBasicTest007, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert item to array
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    /**
     * @tc.steps: step2. insert data
     * @tc.expected: step2. GRD_OK, value is as expected
     */
    char content[] = "[{\"type\":2, \"value\":\"\"},{\"type\":57, \"value\":\"A1\"},{\"type\":57, "
                     "\"value\":\"A2\"},{\"type\":57, \"value\":\"A3\"}]";
    char *elemIdValue = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 0, content, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    /**
     * @tc.steps: step3. read data
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":2,\"elementId\":\"A_0\"},{\"type\":57,\"value\":\"A1\"},{\"type\":57,\"value\":"
                        "\"A2\"},{\"type\":57,\"value\":\"A3\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.expected: step5. value is as expected
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":2,\"elementId\":\"A_0\"},{\"type\":57,\"value\":\"A1\"},{\"type\":57,\"value\":"
                        "\"A2\"},{\"type\":57,\"value\":\"A3\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. switch to equip B and delete data
     * @tc.expected: step6. GRD_OK, value is as expected
     */

    ASSERT_EQ(GRD_ArrayDelete(EQUIPA_DB, &addr, 0, 3), GRD_OK);
    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"A3\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step7. call GRD_Sync to upload data, check record size.
     * @tc.expected: step7. GRD_OK
     */
    ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step8. open equip B and switch to equip B
     * @tc.expected: step8. expect record size is 4 and value is as expected
     */

    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"A3\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    char content1[] = "[{\"type\":2, \"value\":\"\"},{\"type\":57, \"value\":\"B1\"},{\"type\":57, "
                      "\"value\":\"B2\"},{\"type\":57, \"value\":\"B3\"}]";
    ASSERT_EQ(GRD_ArrayInsert(EQUIPB_DB, &addr, 0, content1, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":2,\"elementId\":\"B_0\"},{\"type\":57,\"value\":\"B1\"},{\"type\":57,\"value\":"
                        "\"B2\"},{\"type\":57,\"value\":\"B3\"},{\"type\":57,\"value\":\"A3\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ret = GRD_Sync(EQUIPB_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step9. open equip A and switch to equip A
     * @tc.expected: step9. expect record size is 4 and value is as expected
     */

    ret = GRD_Sync(EQUIPA_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":2,\"elementId\":\"B_0\"},{\"type\":57,\"value\":\"B1\"},{\"type\":57,\"value\":"
                        "\"B2\"},{\"type\":57,\"value\":\"B3\"},{\"type\":57,\"value\":\"A3\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

void InsertXmlElement(EquipIdE equipType, GRD_DB *equip_DB, GRD_XmlOpPositionT *addr, uint32_t index,
    GRD_DocNodeType nodeType, string content)
{
    GRD_DocNodeInfoT nodeXmlElement = {
        .type = nodeType,
        .content = content.c_str(),
    };
    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(equip_DB, addr, index, &nodeXmlElement, &elementId), GRD_OK);
    GRD_XmlFreeElementId(elementId);
}

string MakeJsonForElementVector(vector<string> nameVector, vector<string> opVector)
{
    string ret = "[";
    string part10 = "{\"nodeName\":\"";
    string part12 = "\",\"type\":\"XML_ELEMENT\",\"elementId\":\"";
    string lastPart = "\"},";

    int32_t index = 0;
    for (auto itor : opVector) {
        ret += part10 + nameVector[index] + part12 + itor + lastPart;
        index++;
    }
    if (ret.back() == ',') {
        ret.pop_back();
    }
    ret += "]";
    return ret;
}

void AddOpElementTVector(EquipIdE equipType, uint32_t clock, uint32_t index, vector<string> &opVector)
{
    string equip = "";
    switch (equipType) {
        case EquipA:
            equip += "A";
            break;
        case EquipB:
            equip += "B";
            break;
        case EquipC:
            equip += "C";
            break;
        case EquipD:
            equip += "D";
            break;
        case EquipE:
            equip += "E";
            break;
        case EquipStr:
            equip += "12345";
            break;
        default:
            equip = "A";
    };
    string str = equip + "_" + std::to_string(clock);
    opVector.insert(opVector.begin() + index, str);
}

void CheckElement(GRD_DB *equip_DB, GRD_XmlOpPositionT *addr, string expect)
{
    char *respXml = nullptr;
    ASSERT_EQ(GRD_XmlFragmentGet(equip_DB, addr, 0, 4294967295, &respXml), GRD_OK);
    string value(respXml);
    ASSERT_EQ(expect, value);
    ASSERT_EQ(GRD_DocFree(respXml), GRD_OK);
}

/**
 * @tc.name: GRDDocSyncConflictXmlElemTest001
 * @tc.desc: A and B insert conflict element between null null
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictXmlElemTest001, TestSize.Level0)
{
    vector<string> opVectorA;
    vector<string> opVectorB;
    vector<string> opVectorAB;
    uint32_t clockA = 0;
    uint32_t clockB = 0;
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeFragment = {
        .type = GRD_XML_FRAGMENT_TYPE,
        .content = nullptr,
    };
    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeFragment, &addr.elementId), GRD_OK);

    InsertXmlElement(EquipA, EQUIPA_DB, &addr, 0u, GRD_XML_ELEMENT_TYPE, "abcde");
    AddOpElementTVector(EquipA, clockA, 0, opVectorA);
    AddOpElementTVector(EquipA, clockA, 0, opVectorAB);
    CheckElement(EQUIPA_DB, &addr, MakeJsonForElementVector({"abcde"}, opVectorA));

    InsertXmlElement(EquipB, EQUIPB_DB, &addr, 0u, GRD_XML_ELEMENT_TYPE, "12345");
    AddOpElementTVector(EquipB, clockB, 0, opVectorB);
    AddOpElementTVector(EquipB, clockB, 1, opVectorAB);
    CheckElement(EQUIPB_DB, &addr, MakeJsonForElementVector({"12345"}, opVectorB));

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 1u);
    CheckElement(EQUIPA_DB, &addr, MakeJsonForElementVector({"abcde", "12345"}, opVectorAB));

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    CheckElement(EQUIPB_DB, &addr, MakeJsonForElementVector({"abcde", "12345"}, opVectorAB));

    GRD_XmlFreeElementId(addr.elementId);
}

/**
 * @tc.name: GRDDocSyncConflictXmlElemTest002
 * @tc.desc: A and B insert conflict element between prev next
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictXmlElemTest002, TestSize.Level0)
{
    vector<string> opVectorA;
    vector<string> opVectorB;
    vector<string> opVectorAB;
    uint32_t clockA = 0;
    uint32_t clockB = 0;
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeFragment = {
        .type = GRD_XML_FRAGMENT_TYPE,
        .content = nullptr,
    };
    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeFragment, &addr.elementId), GRD_OK);

    InsertXmlElement(EquipA, EQUIPA_DB, &addr, 0u, GRD_XML_ELEMENT_TYPE, "abcde");
    AddOpElementTVector(EquipA, clockA, 0, opVectorA);
    AddOpElementTVector(EquipA, clockA, 0, opVectorAB);
    CheckElement(EQUIPA_DB, &addr, MakeJsonForElementVector({"abcde"}, opVectorA));
    clockA++;

    InsertXmlElement(EquipA, EQUIPA_DB, &addr, 1u, GRD_XML_ELEMENT_TYPE, "fgh");
    AddOpElementTVector(EquipA, clockA, 1, opVectorA);
    AddOpElementTVector(EquipA, clockA, 1, opVectorAB);
    CheckElement(EQUIPA_DB, &addr, MakeJsonForElementVector({"abcde", "fgh"}, opVectorA));
    clockA++;

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2u);
    opVectorB = opVectorA;

    InsertXmlElement(EquipA, EQUIPA_DB, &addr, 1u, GRD_XML_ELEMENT_TYPE, "xy");
    AddOpElementTVector(EquipA, clockA, 1, opVectorA);
    AddOpElementTVector(EquipA, clockA, 1, opVectorAB);
    CheckElement(EQUIPA_DB, &addr, MakeJsonForElementVector({"abcde", "xy", "fgh"}, opVectorA));

    InsertXmlElement(EquipB, EQUIPB_DB, &addr, 1u, GRD_XML_ELEMENT_TYPE, "12345");
    AddOpElementTVector(EquipB, clockB, 1, opVectorB);
    AddOpElementTVector(EquipB, clockB, 2, opVectorAB);
    CheckElement(EQUIPB_DB, &addr, MakeJsonForElementVector({"abcde", "12345", "fgh"}, opVectorB));

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 1u);
    CheckElement(EQUIPA_DB, &addr, MakeJsonForElementVector({"abcde", "xy", "12345", "fgh"}, opVectorAB));
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    CheckElement(EQUIPB_DB, &addr, MakeJsonForElementVector({"abcde", "xy", "12345", "fgh"}, opVectorAB));

    GRD_XmlFreeElementId(addr.elementId);
}

/**
 * @tc.name: GRDDocSyncConflictXmlElemTest003
 * @tc.desc: A and B insert conflict element between null next
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictXmlElemTest003, TestSize.Level0)
{
    vector<string> opVectorA;
    vector<string> opVectorB;
    vector<string> opVectorAB;
    uint32_t clockA = 0;
    uint32_t clockB = 0;
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeFragment = {
        .type = GRD_XML_FRAGMENT_TYPE,
        .content = nullptr,
    };
    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeFragment, &addr.elementId), GRD_OK);

    InsertXmlElement(EquipA, EQUIPA_DB, &addr, 0u, GRD_XML_ELEMENT_TYPE, "abcde");
    AddOpElementTVector(EquipA, clockA, 0, opVectorA);
    AddOpElementTVector(EquipA, clockA, 0, opVectorAB);
    CheckElement(EQUIPA_DB, &addr, MakeJsonForElementVector({"abcde"}, opVectorA));
    clockA++;

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    opVectorB = opVectorA;

    InsertXmlElement(EquipA, EQUIPA_DB, &addr, 0u, GRD_XML_ELEMENT_TYPE, "xy");
    AddOpElementTVector(EquipA, clockA, 0, opVectorA);
    AddOpElementTVector(EquipA, clockA, 0, opVectorAB);
    CheckElement(EQUIPA_DB, &addr,
        MakeJsonForElementVector(
            {
                "xy",
                "abcde",
            },
            opVectorA));

    InsertXmlElement(EquipB, EQUIPB_DB, &addr, 0, GRD_XML_ELEMENT_TYPE, "12345");
    AddOpElementTVector(EquipB, clockB, 0, opVectorB);
    AddOpElementTVector(EquipB, clockB, 1, opVectorAB);
    CheckElement(EQUIPB_DB, &addr, MakeJsonForElementVector({"12345", "abcde"}, opVectorB));

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 1u);
    CheckElement(EQUIPA_DB, &addr, MakeJsonForElementVector({"xy", "12345", "abcde"}, opVectorAB));
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    CheckElement(EQUIPB_DB, &addr, MakeJsonForElementVector({"xy", "12345", "abcde"}, opVectorAB));

    GRD_XmlFreeElementId(addr.elementId);
}

/**
 * @tc.name: GRDDocSyncConflictXmlElemTest004
 * @tc.desc: A and B insert conflict element between prev null
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictXmlElemTest004, TestSize.Level0)
{
    vector<string> opVectorA;
    vector<string> opVectorB;
    vector<string> opVectorAB;
    uint32_t clockA = 0;
    uint32_t clockB = 0;
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeFragment = {
        .type = GRD_XML_FRAGMENT_TYPE,
        .content = nullptr,
    };
    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeFragment, &addr.elementId), GRD_OK);

    InsertXmlElement(EquipA, EQUIPA_DB, &addr, 0u, GRD_XML_ELEMENT_TYPE, "abcde");
    AddOpElementTVector(EquipA, clockA, 0, opVectorA);
    AddOpElementTVector(EquipA, clockA, 0, opVectorAB);
    CheckElement(EQUIPA_DB, &addr, MakeJsonForElementVector({"abcde"}, opVectorA));
    clockA++;

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    opVectorB = opVectorA;

    InsertXmlElement(EquipA, EQUIPA_DB, &addr, 1u, GRD_XML_ELEMENT_TYPE, "xy");
    AddOpElementTVector(EquipA, clockA, 1, opVectorA);
    AddOpElementTVector(EquipA, clockA, 1, opVectorAB);
    CheckElement(EQUIPA_DB, &addr, MakeJsonForElementVector({"abcde", "xy"}, opVectorA));

    InsertXmlElement(EquipB, EQUIPB_DB, &addr, 1u, GRD_XML_ELEMENT_TYPE, "12345");
    AddOpElementTVector(EquipB, clockB, 1, opVectorB);
    AddOpElementTVector(EquipB, clockB, 2, opVectorAB);
    CheckElement(EQUIPB_DB, &addr, MakeJsonForElementVector({"abcde", "12345"}, opVectorB));

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 1u);
    CheckElement(EQUIPA_DB, &addr, MakeJsonForElementVector({"abcde", "xy", "12345"}, opVectorAB));
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    CheckElement(EQUIPB_DB, &addr, MakeJsonForElementVector({"abcde", "xy", "12345"}, opVectorAB));

    GRD_XmlFreeElementId(addr.elementId);
}

string GetXmlAttributeValue(GRD_DB *db, GRD_XmlOpPositionT *elemAddr)
{
    char *value = nullptr;
    EXPECT_EQ(GRD_XmlElementGetAttributes(db, elemAddr, &value), GRD_OK);
    string ret(value);
    EXPECT_EQ(GRD_DocFree(value), GRD_OK);
    return ret;
}

/**
 * @tc.name: GRDDocSyncConflictAttributeTest001
 * @tc.desc: A and B set same Attribute conflict
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictAttributeTest001, TestSize.Level0)
{
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };

    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &elementId), GRD_OK);
    GRD_XmlOpPositionT elemAddr = {.tableName = TABLE_NAME, .elementId = elementId};

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "textAlign", "left", 0), GRD_OK);
    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr, "textAlign", "left", 0), GRD_OK);
    // equipB g_testUpload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 1u);

    string expStr = "{\"textAlign\":\"left\"}";
    ASSERT_EQ(expStr, GetXmlAttributeValue(EQUIPA_DB, &elemAddr));

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    ASSERT_EQ(expStr, GetXmlAttributeValue(EQUIPB_DB, &elemAddr));

    GRD_XmlFreeElementId(elementId);
}

/**
 * @tc.name: GRDDocSyncConflictAttributeTest002
 * @tc.desc: A and B set same Attribute diff value conflict
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictAttributeTest002, TestSize.Level0)
{
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };

    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &elementId), GRD_OK);
    GRD_XmlOpPositionT elemAddr = {.tableName = TABLE_NAME, .elementId = elementId};

    // equipA g_testUpload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "textAlign", "left", 0), GRD_OK);
    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr, "textAlign", "right", 0), GRD_OK);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 1u);
    string expStr = "{\"textAlign\":\"right\"}";
    ASSERT_EQ(expStr, GetXmlAttributeValue(EQUIPA_DB, &elemAddr));

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    ASSERT_EQ(expStr, GetXmlAttributeValue(EQUIPB_DB, &elemAddr));

    GRD_XmlFreeElementId(elementId);
}

/**
 * @tc.name: GRDDocSyncConflictAttributeTest003
 * @tc.desc: A and B set same Attribute and delete conflict
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictAttributeTest003, TestSize.Level0)
{
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };

    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &elementId), GRD_OK);
    GRD_XmlOpPositionT elemAddr = {.tableName = TABLE_NAME, .elementId = elementId};

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "textAlign", "left", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "uuid", "12321321321", 0), GRD_OK);
    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2u);

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "textAlign", "right", 0), GRD_OK);
    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);

    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPB_DB, &elemAddr, "textAlign"), GRD_OK);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 1u);
    string expStr = "{\"textAlign\":\"right\",\"uuid\":\"12321321321\"}";
    ASSERT_EQ(expStr, GetXmlAttributeValue(EQUIPA_DB, &elemAddr));

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    ASSERT_EQ(expStr, GetXmlAttributeValue(EQUIPB_DB, &elemAddr));

    GRD_XmlFreeElementId(elementId);
}

/**
 * @tc.name: GRDDocSyncConflictAttributeTest004
 * @tc.desc: A and B set same Attribute conflict
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictAttributeTest004, TestSize.Level0)
{
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };

    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &elementId), GRD_OK);
    GRD_XmlOpPositionT elemAddr = {.tableName = TABLE_NAME, .elementId = elementId};

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "textAlign", "left", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "uuid", "12321321321", 0), GRD_OK);
    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2u);

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "textAlign", "right", 0), GRD_OK);
    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr, "textAlign", "llllllll", 0), GRD_OK);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 1u);
    string expStr = "{\"textAlign\":\"llllllll\",\"uuid\":\"12321321321\"}";
    ASSERT_EQ(expStr, GetXmlAttributeValue(EQUIPA_DB, &elemAddr));

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    ASSERT_EQ(expStr, GetXmlAttributeValue(EQUIPB_DB, &elemAddr));

    GRD_XmlFreeElementId(elementId);
}

/**
 * @tc.name: GRDDocSyncConflictAttributeTest005
 * @tc.desc: A and B change and delete Attribute conflict
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictAttributeTest005, TestSize.Level0)
{
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };

    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &elementId), GRD_OK);
    GRD_XmlOpPositionT elemAddr = {.tableName = TABLE_NAME, .elementId = elementId};

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "textAlign", "left", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "uuid", "12321321321", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "format", "22222", 0), GRD_OK);
    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 3u);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 3u);

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "textAlign", "right", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPA_DB, &elemAddr, "uuid"), GRD_OK);
    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr, "uuid", "llllllll", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPB_DB, &elemAddr, "textAlign"), GRD_OK);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 2u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 2u);
    string expStr = "{\"format\":\"22222\",\"textAlign\":\"right\",\"uuid\":\"llllllll\"}";
    ASSERT_EQ(expStr, GetXmlAttributeValue(EQUIPA_DB, &elemAddr));

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2u);
    ASSERT_EQ(expStr, GetXmlAttributeValue(EQUIPB_DB, &elemAddr));

    GRD_XmlFreeElementId(elementId);
}

/**
 * @tc.name: GRDDocSyncConflictAttributeTest006
 * @tc.desc: A and B delete same Attribute conflict
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictAttributeTest006, TestSize.Level0)
{
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };

    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &elementId), GRD_OK);
    GRD_XmlOpPositionT elemAddr = {.tableName = TABLE_NAME, .elementId = elementId};

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "textAlign", "left", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "uuid", "12321321321", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "format", "22222", 0), GRD_OK);
    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 3u);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 3u);

    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPA_DB, &elemAddr, "textAlign"), GRD_OK);
    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPA_DB, &elemAddr, "uuid"), GRD_OK);
    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);

    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPB_DB, &elemAddr, "uuid"), GRD_OK);
    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPB_DB, &elemAddr, "textAlign"), GRD_OK);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 2u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 2u);
    string expStr = "{\"format\":\"22222\"}";
    ASSERT_EQ(expStr, GetXmlAttributeValue(EQUIPA_DB, &elemAddr));

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2u);
    ASSERT_EQ(expStr, GetXmlAttributeValue(EQUIPB_DB, &elemAddr));

    GRD_XmlFreeElementId(elementId);
}

/**
 * @tc.name: GRDDocSyncConflictAttributeTest007
 * @tc.desc: A and B repeat set delete Attribute conflict
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictAttributeTest007, TestSize.Level0)
{
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };

    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &elementId), GRD_OK);
    GRD_XmlOpPositionT elemAddr = {.tableName = TABLE_NAME, .elementId = elementId};

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "textAlign", "left", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "uuid", "12321321321", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "format", "22222", 0), GRD_OK);
    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 3u);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 3u);

    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPA_DB, &elemAddr, "textAlign"), GRD_OK);
    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPA_DB, &elemAddr, "uuid"), GRD_OK);
    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);

    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPB_DB, &elemAddr, "uuid"), GRD_OK);
    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPB_DB, &elemAddr, "textAlign"), GRD_OK);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 2u);

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "textAlign", "right", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "uuid", "222222222", 0), GRD_OK);
    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr, "textAlign", "left", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr, "uuid", "66666666666", 0), GRD_OK);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 2u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 4u);

    string expStr = "{\"format\":\"22222\",\"textAlign\":\"left\",\"uuid\":\"66666666666\"}";
    ASSERT_EQ(expStr, GetXmlAttributeValue(EQUIPA_DB, &elemAddr));

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 4u);
    ASSERT_EQ(expStr, GetXmlAttributeValue(EQUIPB_DB, &elemAddr));

    GRD_XmlFreeElementId(elementId);
}

/**
 * @tc.name: GRDDocSyncConflictAttributeTest008
 * @tc.desc: A and B repeat set delete Attribute conflict
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictAttributeTest008, TestSize.Level0)
{
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };

    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &elementId), GRD_OK);
    GRD_XmlOpPositionT elemAddr = {.tableName = TABLE_NAME, .elementId = elementId};

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "textAlign", "left", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "uuid", "12321321321", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "format", "22222", 0), GRD_OK);
    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 3u);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 3u);

    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPA_DB, &elemAddr, "textAlign"), GRD_OK);
    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPA_DB, &elemAddr, "uuid"), GRD_OK);
    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);

    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPB_DB, &elemAddr, "uuid"), GRD_OK);
    ASSERT_EQ(GRD_XmlElementRemoveAttribute(EQUIPB_DB, &elemAddr, "textAlign"), GRD_OK);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 2u);

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "uuid", "222222222", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "textAlign", "right", 0), GRD_OK);
    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr, "textAlign", "left", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr, "uuid", "66666666666", 0), GRD_OK);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 2u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 4u);
    string expStr = "{\"format\":\"22222\",\"textAlign\":\"left\",\"uuid\":\"66666666666\"}";
    ASSERT_EQ(expStr, GetXmlAttributeValue(EQUIPA_DB, &elemAddr));

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 4u);
    ASSERT_EQ(expStr, GetXmlAttributeValue(EQUIPB_DB, &elemAddr));

    GRD_XmlFreeElementId(elementId);
}

/**
 * @tc.name: GRDDocSyncMultilingualTest001
 * @tc.desc: test multi language
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: huanghe
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncMultilingualTest001, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert fragment element text to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeFragment = {
        .type = GRD_XML_FRAGMENT_TYPE,
        .content = nullptr,
    };

    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_ElementIdT *elementId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeFragment, &elementId), GRD_OK);
    GRD_ElementIdT *elementId1 = nullptr;
    addr.elementId = elementId;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlElement, &elementId1), GRD_OK);
    GRD_ElementIdT *elementId2 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &elementId2), GRD_OK);
    addr.elementId = elementId2;
    ASSERT_EQ(addr.elementId->incrClock, 1u);

    /* @tc.steps: step2. set Chinese data
     * @tc.expected: step2. GRD_OK
     */

    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "你好，世界", nullptr), GRD_OK);

    char *value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"你好，世界"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);

    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addr, 0, "测试文本：", nullptr), GRD_OK);
    value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"测试文本：你好，世界"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);

    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 3, 4), GRD_OK);
    value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"测试文，世界"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);

    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 2, 2), GRD_OK);
    /**
     * @tc.steps: step3. read data
     * @tc.expected: step3. GRD_OK, value is as expected
     */
    value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, "[{\"insert\":\"测试世界\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step4. call GRD_Sync to upload data, check record size.
     * @tc.expected: step4. GRD_OK
     */
    int32_t ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 6u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    /**
     * @tc.steps: step5. open equip B and switch to equip B
     * @tc.steps: step5. call GRD_Sync to download data, check record size.
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 6u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"测试世界"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextInsert(EQUIPB_DB, &addr, 0, "新的", nullptr), GRD_OK);

    GRD_XmlOpPositionT elemAddr1 = {.tableName = TABLE_NAME, .elementId = elementId1};
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr1, "文本对齐", "左", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr1, "位置", "六", 0), GRD_OK);
    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPB_DB, &elemAddr1, "位置", "一", 0), GRD_OK);

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"位置":"一","文本对齐":"左"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, "[{\"insert\":\"新的测试世界\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ret = GRD_Sync(EQUIPB_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 4u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPA_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 4u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"位置":"一","文本对齐":"左"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"新的测试世界"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr1, "文本对齐", "中间", 0),
        GRD_OK);  // same name attr

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPA_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"位置":"一","文本对齐":"中间"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"新的测试世界"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ret = GRD_Sync(EQUIPA_DB, &g_uploadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_uploadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ResetSyncStatus();
    ret = GRD_Sync(EQUIPB_DB, &g_downloadConfig);
    ASSERT_EQ(ret, GRD_OK);
    if (ret == GRD_OK) {
        WaitForSyncFinish();
    }
    ASSERT_EQ(g_downloadRecordSize, 1u);
    global_status = GRD_SYNC_PROCESS_PREPARED;
    g_downloadRecordSize = 0u;
    g_uploadRecordSize = 0u;

    ASSERT_EQ(GRD_XmlElementGetAttributes(EQUIPB_DB, &elemAddr1, &value), GRD_OK);
    EXPECT_EQ(strcmp(R"({"位置":"一","文本对齐":"中间"})", value), 0);
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addr, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, "[{\"insert\":\"新的测试世界\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
    /**
     * @tc.steps: step6. free elementId
     * @tc.expected: step6. GRD_OK
     */
    GRD_XmlFreeElementId(elementId);
    GRD_XmlFreeElementId(elementId1);
    GRD_XmlFreeElementId(elementId2);
}

/**
 * @tc.name: GRDDocSyncConflictXmlElemTest005
 * @tc.desc: A insert B delete conflict
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.017.003
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictXmlElemTest005, TestSize.Level0)
{
    vector<string> opVectorA;
    vector<string> opVectorB;
    vector<string> opVectorAB;
    uint32_t clockA = 0;
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    InsertXmlElement(EquipA, EQUIPA_DB, &addr, 0u, GRD_XML_ELEMENT_TYPE, "abcde");
    AddOpElementTVector(EquipA, clockA, 0, opVectorA);
    AddOpElementTVector(EquipA, clockA, 0, opVectorAB);
    CheckElement(EQUIPA_DB, &addr, MakeJsonForElementVector({"abcde"}, opVectorA));
    clockA++;

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    opVectorB = opVectorA;

    InsertXmlElement(EquipA, EQUIPA_DB, &addr, 1u, GRD_XML_ELEMENT_TYPE, "xy");
    AddOpElementTVector(EquipA, clockA, 1, opVectorA);
    AddOpElementTVector(EquipA, clockA, 1, opVectorAB);
    CheckElement(EQUIPA_DB, &addr, MakeJsonForElementVector({"abcde", "xy"}, opVectorA));

    InsertXmlElement(EquipB, EQUIPB_DB, &addr, 1u, GRD_XML_ELEMENT_TYPE, "12345");
    ASSERT_EQ(GRD_XmlFragmentDelete(EQUIPB_DB, addr.tableName, nullptr, 1, 1), GRD_OK);
    CheckElement(EQUIPB_DB, &addr,
        MakeJsonForElementVector({"abcde", "12345"}, opVectorB));  // EQUIPB_DB it's the same as doing nothing.

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 2u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 2u);
    CheckElement(EQUIPA_DB, &addr, MakeJsonForElementVector({"abcde", "xy"}, opVectorAB));
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    CheckElement(EQUIPB_DB, &addr, MakeJsonForElementVector({"abcde", "xy"}, opVectorAB));
}

/**
 * @tc.name: GRDDocSyncConflictXmlElemTest006
 * @tc.desc: B insert A delete conflict
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.017.003
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictXmlElemTest006, TestSize.Level0)
{
    vector<string> opVectorA;
    vector<string> opVectorB;
    vector<string> opVectorAB;
    uint32_t clockA = 0;
    uint32_t clockB = 0;
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    InsertXmlElement(EquipA, EQUIPA_DB, &addr, 0u, GRD_XML_ELEMENT_TYPE, "abcde");
    AddOpElementTVector(EquipA, clockA, 0, opVectorA);
    AddOpElementTVector(EquipA, clockA, 0, opVectorAB);
    CheckElement(EQUIPA_DB, &addr, MakeJsonForElementVector({"abcde"}, opVectorA));

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    opVectorB = opVectorA;

    InsertXmlElement(EquipA, EQUIPA_DB, &addr, 1u, GRD_XML_ELEMENT_TYPE, "12345");
    ASSERT_EQ(GRD_XmlFragmentDelete(EQUIPA_DB, addr.tableName, nullptr, 1, 1), GRD_OK);
    CheckElement(EQUIPA_DB, &addr,
        MakeJsonForElementVector({"abcde", "12345"}, opVectorB));  // EQUIPA_DB it's the same as doing nothing.

    InsertXmlElement(EquipB, EQUIPB_DB, &addr, 1u, GRD_XML_ELEMENT_TYPE, "xy");
    AddOpElementTVector(EquipB, clockB, 1, opVectorB);
    AddOpElementTVector(EquipB, clockB, 1, opVectorAB);
    CheckElement(EQUIPB_DB, &addr, MakeJsonForElementVector({"abcde", "xy"}, opVectorB));

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 1u);
    CheckElement(EQUIPA_DB, &addr, MakeJsonForElementVector({"abcde", "xy"}, opVectorAB));
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2u);
    CheckElement(EQUIPB_DB, &addr, MakeJsonForElementVector({"abcde", "xy"}, opVectorAB));
}

// insertList:<index,textstring>
void InsertXmlText(EquipIdE equipType, GRD_DB *db, GRD_XmlOpPositionT *addr, vector<pair<uint32_t, string>> insertList)
{
    for (auto itor : insertList) {
        ASSERT_EQ(GRD_TextInsert(db, addr, itor.first, itor.second.c_str(), nullptr), GRD_OK);
    }
}

void ReadXmlTextInJson(GRD_DB *db, GRD_XmlOpPositionT *addr, string &retstr)
{
    char *strvalue = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(db, addr, nullptr, nullptr, &strvalue), GRD_OK);
    retstr.clear();
    retstr.assign(strvalue);
    ASSERT_EQ(GRD_DocFree(strvalue), GRD_OK);
}

string MakeXmlTextJosn(const string *content)
{
    string ret = "[{\"insert\":\"";
    ret += *content + "\"}]";
    return ret;
}

void TestXmlTextSingleCoflict(string startStr, uint32_t indexA, string strA, uint32_t indexB, string strB)
{
    map<EquipIdE, GRD_DB *> dbPointerMap;
    vector<pair<uint32_t, string>> insertListA;
    vector<pair<uint32_t, string>> insertListB;
    string exceptText = "";
    string localTextEA = "";
    string localTextEB = "";

    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);
    dbPointerMap[EquipA] = EQUIPA_DB;

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    dbPointerMap[EquipB] = EQUIPB_DB;

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };

    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &addr.elementId), GRD_OK);
    ASSERT_EQ(addr.elementId->incrClock, 0);

    // equipA insert index:0 text:startStr
    insertListA.push_back(make_pair(0, startStr));
    InsertXmlText(EquipA, dbPointerMap[EquipA], &addr, insertListA);
    ReadXmlTextInJson(dbPointerMap[EquipA], &addr, localTextEA);
    exceptText += startStr;

    // equipA upload data
    EquipSync(EquipA, dbPointerMap[EquipA], g_testUpload, 2);

    // equipB download data
    EquipSync(EquipB, dbPointerMap[EquipB], g_testDownload, 2);
    ReadXmlTextInJson(dbPointerMap[EquipB], &addr, localTextEB);

    // equipB insert index:indexB text:strB
    insertListB.push_back(make_pair(indexB, strB));
    InsertXmlText(EquipB, dbPointerMap[EquipB], &addr, insertListB);
    ReadXmlTextInJson(dbPointerMap[EquipB], &addr, localTextEB);
    EquipSync(EquipB, dbPointerMap[EquipB], g_testUpload, 1);

    // equipA insert index:indexA text:strA
    insertListA.clear();
    insertListA.push_back(make_pair(indexA, strA));
    InsertXmlText(EquipA, dbPointerMap[EquipA], &addr, insertListA);
    ReadXmlTextInJson(dbPointerMap[EquipA], &addr, localTextEA);
    EquipSync(EquipA, dbPointerMap[EquipA], g_testUpload, 1);

    exceptText.insert(indexA, strA);
    exceptText.insert(indexA + strA.length(), strB);

    // equipB download data and resolving conflicts
    EquipSync(EquipB, dbPointerMap[EquipB], g_testDownload, 1);
    ReadXmlTextInJson(dbPointerMap[EquipB], &addr, localTextEB);
    ASSERT_EQ(MakeXmlTextJosn(&exceptText), localTextEB);

    // equipA download data and resolving conflicts
    EquipSync(EquipA, dbPointerMap[EquipA], g_testDownload, 1);
    ReadXmlTextInJson(dbPointerMap[EquipA], &addr, localTextEA);
    ASSERT_EQ(MakeXmlTextJosn(&exceptText), localTextEA);

    GRD_XmlFreeElementId(addr.elementId);
}

/**
 * @tc.name: GRDDocSyncConflictTextTest001
 * @tc.desc: test Conflict
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.017.003
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictTextTest001, TestSize.Level0)
{
    TestXmlTextSingleCoflict("abcde", 0, "!!!", 0, "213");
}

/**
 * @tc.name: GRDDocSyncConflictTextTest002
 * @tc.desc: test Conflict
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.017.003
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictTextTest002, TestSize.Level0)
{
    string startstr = "abcde";
    TestXmlTextSingleCoflict(startstr, startstr.length(), "!!!", startstr.length(), "213");
}

/**
 * @tc.name: GRDDocSyncConflictTextTest003
 * @tc.desc: test Conflict
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.017.003
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictTextTest003, TestSize.Level0)
{
    string startstr = "abcde";
    TestXmlTextSingleCoflict(startstr, startstr.length() / 2, "!!!", startstr.length() / 2, "213");
}

/**
 * @tc.name: GRDDocSyncConflictTextTest004
 * @tc.desc: test Conflict
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.017.003
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictTextTest004, TestSize.Level0)
{
    vector<pair<uint32_t, string>> insertList;
    string exceptText = "";
    string localTextEA = "";
    string localTextEB = "";

    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    // equipA insert XmlText

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };
    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &addr.elementId), GRD_OK);
    ASSERT_EQ(addr.elementId->incrClock, 0);

    // equipA insert index:0 text:"123456"
    insertList.push_back(make_pair(0, "123456"));
    InsertXmlText(EquipA, EQUIPA_DB, &addr, insertList);
    ReadXmlTextInJson(EQUIPA_DB, &addr, localTextEA);
    exceptText += "123456";

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2);

    insertList.clear();
    // equipA insert index:2 text:"aaa" and Then Delete
    insertList.push_back(make_pair(2, "aaa"));
    InsertXmlText(EquipA, EQUIPA_DB, &addr, insertList);
    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addr, 2, 3), GRD_OK);
    ReadXmlTextInJson(EQUIPA_DB, &addr, localTextEA);
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2);

    insertList.clear();
    // equipB insert index:2 text:"bbb"
    insertList.push_back(make_pair(2, "bbb"));
    InsertXmlText(EquipB, EQUIPB_DB, &addr, insertList);
    ReadXmlTextInJson(EQUIPB_DB, &addr, localTextEB);
    exceptText.insert(2, "bbb");
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1);

    // equipB download data and resolving conflicts
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2);
    ReadXmlTextInJson(EQUIPB_DB, &addr, localTextEB);
    ASSERT_EQ(MakeXmlTextJosn(&exceptText), localTextEB);

    // equipA download data and resolving conflicts
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 1);
    ReadXmlTextInJson(EQUIPA_DB, &addr, localTextEA);
    ASSERT_EQ(MakeXmlTextJosn(&exceptText), localTextEA);

    GRD_XmlFreeElementId(addr.elementId);
}

/**
 * @tc.name: GRDDocSyncConflictTextTest005
 * @tc.desc: test Conflict
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.017.003
 * @tc.author: luoguo
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictTextTest005, TestSize.Level0)
{
    vector<pair<uint32_t, string>> insertList;
    string exceptText = "";
    string localTextEA = "";
    string localTextEB = "";

    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    // equipA insert XmlText

    GRD_DocNodeInfoT nodeXmlText = {
        .type = GRD_XML_TEXT_TYPE,
        .content = nullptr,
    };
    GRD_XmlOpPositionT addr = {.tableName = TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0u, &nodeXmlText, &addr.elementId), GRD_OK);
    ASSERT_EQ(addr.elementId->incrClock, 0);

    // equipA insert index:0 text:"123456"
    insertList.push_back(make_pair(0, "123456"));
    InsertXmlText(EquipA, EQUIPA_DB, &addr, insertList);
    ReadXmlTextInJson(EQUIPA_DB, &addr, localTextEA);
    exceptText += "123456";

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2);
    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2);

    insertList.clear();
    // equipB insert index:2 text:"aaa" and Then Delete
    insertList.push_back(make_pair(2, "aaa"));
    InsertXmlText(EquipB, EQUIPB_DB, &addr, insertList);
    ASSERT_EQ(GRD_TextDelete(EQUIPB_DB, &addr, 2, 3), GRD_OK);
    ReadXmlTextInJson(EQUIPB_DB, &addr, localTextEB);
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 2);

    insertList.clear();
    // equipA insert index:2 text:"bbb"
    insertList.push_back(make_pair(2, "bbb"));
    InsertXmlText(EquipA, EQUIPA_DB, &addr, insertList);
    ReadXmlTextInJson(EQUIPA_DB, &addr, localTextEA);
    exceptText.insert(2, "bbb");
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1);

    // equipB download data and resolving conflicts
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1);
    ReadXmlTextInJson(EQUIPB_DB, &addr, localTextEB);
    ASSERT_EQ(MakeXmlTextJosn(&exceptText), localTextEB);

    // equipA download data and resolving conflicts
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 2);
    ReadXmlTextInJson(EQUIPA_DB, &addr, localTextEA);
    ASSERT_EQ(MakeXmlTextJosn(&exceptText), localTextEA);

    GRD_XmlFreeElementId(addr.elementId);
}

/**
 * @tc.name: GRDDocSyncComplexTest
 * @tc.desc: A and B repeat set delete Attribute conflict
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.012.002
 * @tc.author: huanghe
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncComplexTest001, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert basic node and text to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeParagraph = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };

    GRD_DocNodeInfoT nodeHeading = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "heading",
    };

    GRD_DocNodeInfoT nodeXmlText = {.type = GRD_XML_TEXT_TYPE, .content = nullptr};

    GRD_XmlOpPositionT addrFrag = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_XmlOpPositionT addrPara = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_XmlOpPositionT addrText = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_XmlOpPositionT addrHeading = {.tableName = TABLE_NAME, .elementId = nullptr};
    /**
     * @tc.steps: step2. insert basic node and text in A
     * @tc.expected: step2. GRD_OK
     */
    GRD_ElementIdT *paragraphId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addrFrag, 0u, &nodeParagraph, &paragraphId), GRD_OK);  // A_0

    addrPara.elementId = paragraphId;
    GRD_ElementIdT *xmlTextId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addrPara, 0u, &nodeXmlText, &xmlTextId), GRD_OK);  // A_1

    addrText.elementId = xmlTextId;
    ASSERT_EQ(GRD_TextApplyDelta(EQUIPA_DB, &addrText, 0, "[{\"insert\":\"1\",\"attributes\":{}}]"), GRD_OK);  // A_2
    /**
     * @tc.steps: step3. sync A to B
     * @tc.expected: step3. GRD_OK
     */
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 3u);
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 3u);

    /**
     * @tc.steps: step4. insert basic node and text in B
     * @tc.expected: step4. GRD_OK
     */
    GRD_ElementIdT *paragraphBId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPB_DB, &addrFrag, 1u, &nodeParagraph, &paragraphBId), GRD_OK);  // B_0

    addrPara.elementId = paragraphBId;
    GRD_ElementIdT *xmlTextIdB = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPB_DB, &addrPara, 0u, &nodeXmlText, &xmlTextIdB), GRD_OK);  // B_1
    addrText.elementId = xmlTextIdB;
    ASSERT_EQ(GRD_TextApplyDelta(EQUIPB_DB, &addrText, 0, "[{\"insert\":\"2\",\"attributes\":{}}]"), GRD_OK);  // B_2

    /**
     * @tc.steps: step5. sync B to A
     * @tc.expected: step5. GRD_OK
     */
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 3u);
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 3u);

    GRD_XmlFreeElementId(xmlTextId);

    /**
     * @tc.steps: step6. set attribute to heading in A and sync to B for 4 times
     * @tc.expected: step6. GRD_OK
     */
    char *value = nullptr;
    GRD_ElementIdT *headingId = nullptr;
    for (uint8_t i = 0; i < 4u; i++) {

        ASSERT_EQ(GRD_XmlFragmentDelete(EQUIPA_DB, TABLE_NAME, nullptr, 0, 1), GRD_OK);
        headingId = nullptr;
        ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addrFrag, 0, &nodeHeading, &headingId), GRD_OK);  // A_3
        addrHeading.elementId = headingId;

        GRD_XmlOpPositionT elemAddr = {.tableName = TABLE_NAME, .elementId = headingId};
        ASSERT_EQ(GRD_XmlElementSetAttribute(EQUIPA_DB, &elemAddr, "level", "2", 0), GRD_OK);  // A_4
        xmlTextId = nullptr;
        ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addrHeading, 0, &nodeXmlText, &xmlTextId), GRD_OK);  // A_5
        addrText.elementId = xmlTextId;
        ASSERT_EQ(
            GRD_TextApplyDelta(EQUIPA_DB, &addrText, 0, "[{\"insert\":\"1\",\"attributes\":{}}]"), GRD_OK);  // A_6

        ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addrText, nullptr, nullptr, &value), GRD_OK);
        EXPECT_STREQ(value, "[{\"insert\":\"1\"}]");
        ASSERT_EQ(GRD_DocFree(value), GRD_OK);
        value = nullptr;

        // sync A to B
        EquipSync(EquipA, EQUIPA_DB, g_testUpload, 5u);
        EquipSync(EquipB, EQUIPB_DB, g_testDownload, 5u);
        // read xmlText "paragrapg" in B
        addrText.elementId = xmlTextIdB;
        ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addrText, nullptr, nullptr, &value), GRD_OK);
        EXPECT_STREQ(value, "[{\"insert\":\"2\"}]");
        ASSERT_EQ(GRD_DocFree(value), GRD_OK);
        value = nullptr;
        // read xmlText "heading" in B
        addrText.elementId = xmlTextId;
        ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addrText, nullptr, nullptr, &value), GRD_OK);
        EXPECT_STREQ(value, "[{\"insert\":\"1\"}]");
        ASSERT_EQ(GRD_DocFree(value), GRD_OK);
        value = nullptr;
        // get attributes in A

        string expStr = "{\"level\":\"2\"}";

        ASSERT_EQ(expStr, GetXmlAttributeValue(EQUIPA_DB, &elemAddr));
        string expStr2 = "{}";
        elemAddr.elementId = paragraphBId;
        ASSERT_EQ(expStr2, GetXmlAttributeValue(EQUIPA_DB, &elemAddr));
        // get attributes in B

        elemAddr.elementId = headingId;
        ASSERT_EQ(expStr, GetXmlAttributeValue(EQUIPB_DB, &elemAddr));
        elemAddr.elementId = paragraphBId;
        ASSERT_EQ(expStr2, GetXmlAttributeValue(EQUIPB_DB, &elemAddr));

        GRD_XmlFreeElementId(headingId);
        GRD_XmlFreeElementId(xmlTextId);
        addrHeading.elementId = nullptr;
    }
    /**
     * @tc.steps: step7. free elementId
     * @tc.expected: step7. GRD_OK
     */
    GRD_XmlFreeElementId(paragraphId);
    GRD_XmlFreeElementId(paragraphBId);
    GRD_XmlFreeElementId(xmlTextIdB);
}

/**
 * @tc.name: GRDDocSyncComplexTest
 * @tc.desc: A and B repeat set delete Attribute conflict
 * @tc.type: FUNC
 * @tc.require: DTS2024051615329
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncComplexTest002, TestSize.Level0)
{
    /**
     * @tc.steps: step1. insert basic node and text to db
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    GRD_DocNodeInfoT nodeParagraph = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "paragraph",
    };

    GRD_DocNodeInfoT nodeXmlText = {.type = GRD_XML_TEXT_TYPE, .content = nullptr};
    GRD_XmlOpPositionT addrFrag = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_XmlOpPositionT addrPara = {.tableName = TABLE_NAME, .elementId = nullptr};
    GRD_XmlOpPositionT addrText = {.tableName = TABLE_NAME, .elementId = nullptr};
    /**
     * @tc.steps: step2. insert basic node and text in A
     * @tc.expected: step2. GRD_OK
     */
    GRD_ElementIdT *paragraphId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addrFrag, 0u, &nodeParagraph, &paragraphId), GRD_OK);  // A_0

    addrPara.elementId = paragraphId;
    GRD_ElementIdT *xmlTextId = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addrPara, 0u, &nodeXmlText, &xmlTextId), GRD_OK);  // A_1

    addrText.elementId = xmlTextId;
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addrText, 0, "a", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addrText, 1, "b", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addrText, 2, "c", nullptr), GRD_OK);
    ASSERT_EQ(GRD_TextInsert(EQUIPA_DB, &addrText, 3, "d", nullptr), GRD_OK);
    /**
     * @tc.steps: step3. sync A to B
     * @tc.expected: step3. GRD_OK
     */
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 6u);
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 6u);

    /**
     * @tc.steps: step4. check data in B
     * @tc.expected: step4. GRD_OK
     */
    char *value = nullptr;
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addrText, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"abcd"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step5. delete in A
     * @tc.expected: step5. GRD_OK
     */

    ASSERT_EQ(GRD_TextDelete(EQUIPA_DB, &addrText, 0, 1), GRD_OK);
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addrText, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"bcd"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step6. sync A to B
     * @tc.expected: step6. GRD_OK
     */
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addrText, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"bcd"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step7. delete in B
     * @tc.expected: step7. GRD_OK
     */
    ASSERT_EQ(GRD_TextDelete(EQUIPB_DB, &addrText, 0, 1), GRD_OK);
    ASSERT_EQ(GRD_TextDelete(EQUIPB_DB, &addrText, 0, 1), GRD_OK);
    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPB_DB, &addrText, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"d"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step8. sync B to A
     * @tc.expected: step8. GRD_OK
     */
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 2u);
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 2u);

    ASSERT_EQ(GRD_TextReadInDeltaMode(EQUIPA_DB, &addrText, nullptr, nullptr, &value), GRD_OK);
    EXPECT_STREQ(value, R"([{"insert":"d"}])");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    GRD_XmlFreeElementId(paragraphId);
    GRD_XmlFreeElementId(xmlTextId);
}

/**
 * @tc.name: GRDDocSyncConflictArrayTest001
 * @tc.desc: A and B insert conflict element between null null
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.017.003
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictArrayTest001, TestSize.Level0)
{
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    char content1[] = "[{\"type\":57, \"value\":\"A1\"}]";
    char *elemIdValue = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 0, content1, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char *value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"A1\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    char content2[] = "[{\"type\":57, \"value\":\"B1\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPB_DB, &addr, 0, content2, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"B1\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 1u);
    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"A1\"},{\"type\":57,\"value\":\"B1\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"A1\"},{\"type\":57,\"value\":\"B1\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: GRDDocSyncConflictArrayTest002
 * @tc.desc: A and B insert conflict element between null null
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.017.003
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictArrayTest002, TestSize.Level0)
{
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    char content1[] = "[{\"type\":57, \"value\":\"A1\"}]";
    char *elemIdValue = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 0, content1, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content2[] = "[{\"type\":57, \"value\":\"A2\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 1, content2, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char *value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"A1\"},{\"type\":57,\"value\":\"A2\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    char content3[] = "[{\"type\":57, \"value\":\"B1\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPB_DB, &addr, 0, content3, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content4[] = "[{\"type\":57, \"value\":\"B2\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPB_DB, &addr, 1, content4, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"B1\"},{\"type\":57,\"value\":\"B2\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 2u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 2u);
    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"A1\"},{\"type\":57,\"value\":\"A2\"},{\"type\":57,\"value\":\"B1\"},"
                        "{\"type\":57,\"value\":\"B2\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2u);
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"A1\"},{\"type\":57,\"value\":\"A2\"},{\"type\":57,\"value\":\"B1\"},"
                        "{\"type\":57,\"value\":\"B2\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: GRDDocSyncConflictArrayTest003
 * @tc.desc:
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.017.003
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictArrayTest003, TestSize.Level0)
{
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    char content1[] = "[{\"type\":57, \"value\":\"public\"}]";
    char *elemIdValue = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 0, content1, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    char *value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"public\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    char content2[] = "[{\"type\":57, \"value\":\"B1\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPB_DB, &addr, 0, content2, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"B1\"},{\"type\":57,\"value\":\"public\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    char content3[] = "[{\"type\":57, \"value\":\"A1\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 0, content3, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"A1\"},{\"type\":57,\"value\":\"public\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 1u);
    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(
        value, "[{\"type\":57,\"value\":\"A1\"},{\"type\":57,\"value\":\"B1\"},{\"type\":57,\"value\":\"public\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(
        value, "[{\"type\":57,\"value\":\"A1\"},{\"type\":57,\"value\":\"B1\"},{\"type\":57,\"value\":\"public\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: GRDDocSyncConflictArrayTest004
 * @tc.desc:
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.017.003
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictArrayTest004, TestSize.Level0)
{
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    char content1[] = "[{\"type\":57, \"value\":\"public\"}]";
    char *elemIdValue = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 0, content1, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    char *value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"public\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    char content2[] = "[{\"type\":57, \"value\":\"B1\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPB_DB, &addr, 0, content2, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content3[] = "[{\"type\":57, \"value\":\"B2\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPB_DB, &addr, 1, content3, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(
        value, "[{\"type\":57,\"value\":\"B1\"},{\"type\":57,\"value\":\"B2\"},{\"type\":57,\"value\":\"public\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    char content4[] = "[{\"type\":57, \"value\":\"A1\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 0, content4, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content5[] = "[{\"type\":57, \"value\":\"A2\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 1, content5, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(
        value, "[{\"type\":57,\"value\":\"A1\"},{\"type\":57,\"value\":\"A2\"},{\"type\":57,\"value\":\"public\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 2u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 2u);
    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"A1\"},{\"type\":57,\"value\":\"A2\"},{\"type\":57,\"value\":\"B1\"},"
                        "{\"type\":57,\"value\":\"B2\"},{\"type\":57,\"value\":\"public\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2u);
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"A1\"},{\"type\":57,\"value\":\"A2\"},{\"type\":57,\"value\":\"B1\"},"
                        "{\"type\":57,\"value\":\"B2\"},{\"type\":57,\"value\":\"public\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: GRDDocSyncConflictArrayTest005
 * @tc.desc:
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.017.003
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictArrayTest005, TestSize.Level0)
{
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    char content1[] = "[{\"type\":57, \"value\":\"public_left\"}]";
    char *elemIdValue = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 0, content1, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content2[] = "[{\"type\":57, \"value\":\"public_right\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 1, content2, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2u);
    char *value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"public_left\"},{\"type\":57,\"value\":\"public_right\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    char content3[] = "[{\"type\":57, \"value\":\"B1\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPB_DB, &addr, 1, content3, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"public_left\"},{\"type\":57,\"value\":\"B1\"},{\"type\":57,"
                        "\"value\":\"public_right\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    char content4[] = "[{\"type\":57, \"value\":\"A1\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 1, content4, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"public_left\"},{\"type\":57,\"value\":\"A1\"},{\"type\":57,"
                        "\"value\":\"public_right\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 1u);
    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"public_left\"},{\"type\":57,\"value\":\"A1\"},{\"type\":57,"
                        "\"value\":\"B1\"},{\"type\":57,\"value\":\"public_right\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"public_left\"},{\"type\":57,\"value\":\"A1\"},{\"type\":57,"
                        "\"value\":\"B1\"},{\"type\":57,\"value\":\"public_right\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: GRDDocSyncConflictArrayTest006
 * @tc.desc:
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.017.003
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictArrayTest006, TestSize.Level0)
{
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    char content1[] = "[{\"type\":57, \"value\":\"public_left\"}]";
    char *elemIdValue = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 0, content1, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content2[] = "[{\"type\":57, \"value\":\"public_right\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 1, content2, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2u);
    char *value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"public_left\"},{\"type\":57,\"value\":\"public_right\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    char content3[] = "[{\"type\":57, \"value\":\"B1\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPB_DB, &addr, 1, content3, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content4[] = "[{\"type\":57, \"value\":\"B2\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPB_DB, &addr, 2, content4, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"public_left\"},{\"type\":57,\"value\":\"B1\"},{\"type\":57,"
                        "\"value\":\"B2\"},{\"type\":57,\"value\":\"public_right\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    char content5[] = "[{\"type\":57, \"value\":\"A1\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 1, content5, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content6[] = "[{\"type\":57, \"value\":\"A2\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 2, content6, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"public_left\"},{\"type\":57,\"value\":\"A1\"},{\"type\":57,"
                        "\"value\":\"A2\"},{\"type\":57,\"value\":\"public_right\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 2u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 2u);
    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value,
        "[{\"type\":57,\"value\":\"public_left\"},{\"type\":57,\"value\":\"A1\"},{\"type\":57,\"value\":\"A2\"},{"
        "\"type\":57,\"value\":\"B1\"},{\"type\":57,\"value\":\"B2\"},{\"type\":57,\"value\":\"public_right\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2u);
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value,
        "[{\"type\":57,\"value\":\"public_left\"},{\"type\":57,\"value\":\"A1\"},{\"type\":57,\"value\":\"A2\"},{"
        "\"type\":57,\"value\":\"B1\"},{\"type\":57,\"value\":\"B2\"},{\"type\":57,\"value\":\"public_right\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: GRDDocSyncConflictArrayTest007
 * @tc.desc:
 * @tc.type: FUNC
 * @tc.require: AR.SR.***********.017.003
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictArrayTest007, TestSize.Level0)
{
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    char content1[] = "[{\"type\":57, \"value\":\"public_left\"}]";
    char *elemIdValue = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 0, content1, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    char *value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"public_left\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    char content2[] = "[{\"type\":57, \"value\":\"B1\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPB_DB, &addr, 1, content2, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content3[] = "[{\"type\":57, \"value\":\"B2\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPB_DB, &addr, 2, content3, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content4[] = "[{\"type\":57, \"value\":\"B3\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPB_DB, &addr, 3, content4, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"public_left\"},{\"type\":57,\"value\":\"B1\"},{\"type\":57,"
                        "\"value\":\"B2\"},{\"type\":57,\"value\":\"B3\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    char content5[] = "[{\"type\":57, \"value\":\"A1\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 1, content5, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char content6[] = "[{\"type\":57, \"value\":\"A2\"}]";
    addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 2, content6, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value,
        "[{\"type\":57,\"value\":\"public_left\"},{\"type\":57,\"value\":\"A1\"},{\"type\":57,\"value\":\"A2\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);
    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 3u);

    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 3u);
    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value,
        "[{\"type\":57,\"value\":\"public_left\"},{\"type\":57,\"value\":\"A1\"},{\"type\":57,\"value\":\"A2\"},"
        "{\"type\":57,\"value\":\"B1\"},{\"type\":57,\"value\":\"B2\"},{\"type\":57,\"value\":\"B3\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2u);
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value,
        "[{\"type\":57,\"value\":\"public_left\"},{\"type\":57,\"value\":\"A1\"},{\"type\":57,\"value\":\"A2\"},"
        "{\"type\":57,\"value\":\"B1\"},{\"type\":57,\"value\":\"B2\"},{\"type\":57,\"value\":\"B3\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: GRDDocSyncConflictArrayTest008
 * @tc.desc: fix array head node conflict bug.
 * @tc.type: FUNC
 * @tc.require: DTS2024072406587
 * @tc.author: tankaisheng
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictArrayTest008, TestSize.Level0)
{
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    /**
     * @tc.steps: step1. equipA insert data then delete data
     * @tc.expected: step1. GRD_OK
     */

    char content1[] = "[{\"type\":57, \"value\":\"A\"}]";
    char *elemIdValue = nullptr;
    GRD_XmlOpPositionT addr = {.tableName = ARRAY_TABLE_NAME, .elementId = nullptr};
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 0, content1, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_ArrayDelete(EQUIPA_DB, &addr, 0, 1), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    char *value = nullptr;
    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step2. equipA upload data
     * @tc.expected: step2. GRD_OK
     */
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    /**
     * @tc.steps: step3. equipB download data
     * @tc.expected: step3. GRD_OK
     */
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2u);
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step4. equipA and equipB insert data
     * @tc.expected: step4. GRD_OK
     */

    char content2[] = "[{\"type\":57, \"value\":\"AAA\"}]";
    ASSERT_EQ(GRD_ArrayInsert(EQUIPA_DB, &addr, 0, content2, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"AAA\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    char content3[] = "[{\"type\":57, \"value\":\"BBB\"}]";
    ASSERT_EQ(GRD_ArrayInsert(EQUIPB_DB, &addr, 0, content3, &elemIdValue), GRD_OK);
    ASSERT_EQ(GRD_DocFree(elemIdValue), GRD_OK);
    elemIdValue = nullptr;

    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    ASSERT_STREQ(value, "[{\"type\":57,\"value\":\"BBB\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    /**
     * @tc.steps: step5. equipA and equipB upload data
     * @tc.expected: step5. GRD_OK
     */
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1u);

    /**
     * @tc.steps: step6. equipA and equipB download data then check data
     * @tc.expected: step6. GRD_OK
     */
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 1u);
    ASSERT_EQ(GRD_ArrayToArray(EQUIPA_DB, &addr, &value), GRD_OK);
    EXPECT_STREQ(value, "[{\"type\":57,\"value\":\"AAA\"},{\"type\":57,\"value\":\"BBB\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;

    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    ASSERT_EQ(GRD_ArrayToArray(EQUIPB_DB, &addr, &value), GRD_OK);
    EXPECT_STREQ(value, "[{\"type\":57,\"value\":\"AAA\"},{\"type\":57,\"value\":\"BBB\"}]");
    ASSERT_EQ(GRD_DocFree(value), GRD_OK);
    value = nullptr;
}

/**
 * @tc.name: GRDDocSyncMapTest001
 * @tc.desc: A and B insert no conflict element in one map
 * @tc.type: FUNC
 * @tc.require:
 * @tc.author: zhaoliang
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncMapTest001, TestSize.Level0)
{
    char strValue1[100] = {0};
    char strValue2[100] = {0};
    ASSERT_LT(0, sprintf_s(strValue1, sizeof(strValue1), "{\"type\":%u,\"value\":\"1234\"}", 57));
    ASSERT_LT(0, sprintf_s(strValue2, sizeof(strValue2), "{\"type\":%u,\"value\":\"5678\"}", 57));
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue1), GRD_OK);

    char *value = nullptr;
    GRD_ElementIdT elementId = {.equipId = g_equipIdA, .incrClock = 0};
    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    ASSERT_EQ(GRD_MapSet(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue2), GRD_OK);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    GRD_ElementIdT elementId2 = {.equipId = g_equipIdB, .incrClock = 0};
    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);
}

/**
 * @tc.name: GRDDocSyncMapTest002
 * @tc.desc: A and B insert no conflict element in one map
 * @tc.type: FUNC
 * @tc.require:
 * @tc.author: zhaoliang
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncMapTest002, TestSize.Level0)
{
    char strValue1[100] = {0};
    char strValue2[100] = {0};
    ASSERT_LT(0, sprintf_s(strValue1, sizeof(strValue1), "{\"type\":%u,\"value\":\"1234\"}", 57));
    ASSERT_LT(0, sprintf_s(strValue2, sizeof(strValue2), "{\"type\":%u,\"value\":\"5678\"}", 57));
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue1), GRD_OK);

    char *value = nullptr;
    GRD_ElementIdT elementId = {.equipId = g_equipIdA, .incrClock = 0};
    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    ASSERT_EQ(GRD_MapSet(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key1", (const char *)&strValue2), GRD_OK);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key1", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    GRD_ElementIdT elementId2 = {.equipId = g_equipIdB, .incrClock = 0};
    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);
}

/**
 * @tc.name: GRDDocSyncMapTest003
 * @tc.desc: A and B insert no conflict element in one map
 * @tc.type: FUNC
 * @tc.require:
 * @tc.author: zhaoliang
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncMapTest003, TestSize.Level0)
{
    char strValue1[100] = {0};
    char strValue2[100] = {0};
    ASSERT_LT(0, sprintf_s(strValue1, sizeof(strValue1), "{\"type\":%u,\"value\":\"1234\"}", 57));
    ASSERT_LT(0, sprintf_s(strValue2, sizeof(strValue2), "{\"type\":%u,\"value\":\"5678\"}", 57));
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue1), GRD_OK);

    char *value = nullptr;
    GRD_ElementIdT elementId = {.equipId = g_equipIdA, .incrClock = 0};
    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue2), GRD_OK);

    GRD_ElementIdT elementId2 = {.equipId = g_equipIdA, .incrClock = 4};
    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2u);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    ASSERT_EQ(GRD_MapSet(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key1", (const char *)&strValue2), GRD_OK);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key1", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    GRD_ElementIdT elementId3 = {.equipId = g_equipIdB, .incrClock = 0};
    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId3, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);
}

/**
 * @tc.name: GRDDocSyncMapTest004
 * @tc.desc: A and B delete no conflict element in one map
 * @tc.type: FUNC
 * @tc.require:
 * @tc.author: zhaoliang
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncMapTest004, TestSize.Level0)
{
    char strValue1[100] = {0};
    char strValue2[100] = {0};
    ASSERT_LT(0, sprintf_s(strValue1, sizeof(strValue1), "{\"type\":%u,\"value\":\"1234\"}", 57));
    ASSERT_LT(0, sprintf_s(strValue2, sizeof(strValue2), "{\"type\":%u,\"value\":\"5678\"}", 57));
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue1), GRD_OK);
    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key2", (const char *)&strValue2), GRD_OK);

    char *value = nullptr;
    GRD_ElementIdT elementId = {.equipId = g_equipIdA, .incrClock = 0};
    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    GRD_ElementIdT elementId2 = {.equipId = g_equipIdA, .incrClock = 4};
    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key2", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2u);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key2", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    ASSERT_EQ(GRD_MapDelete(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key"), GRD_OK);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_NO_DATA);
    EXPECT_TRUE(value == nullptr);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key2", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // equipB upload data
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1u);
    // equipA download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_NO_DATA);
    EXPECT_TRUE(value == nullptr);

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key2", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);
}

/**
 * @tc.name: GRDDocSyncConflictMapTest001
 * @tc.desc: A and B insert conflict element in one map
 * @tc.type: FUNC
 * @tc.require:
 * @tc.author: zhaoliang
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictMapTest001, TestSize.Level0)
{
    char strValue1[100] = {0};
    char strValue2[100] = {0};
    ASSERT_LT(0, sprintf_s(strValue1, sizeof(strValue1), "{\"type\":%u,\"value\":\"1234\"}", 57));
    ASSERT_LT(0, sprintf_s(strValue2, sizeof(strValue2), "{\"type\":%u,\"value\":\"5678\"}", 57));
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue1), GRD_OK);

    char *value = nullptr;
    GRD_ElementIdT elementId = {.equipId = g_equipIdA, .incrClock = 0};
    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_MapSet(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue2), GRD_OK);
    GRD_ElementIdT elementId2 = {.equipId = g_equipIdB, .incrClock = 0};
    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // equipA/equipB upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1u);
    // equipA/equipB download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 1u);
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);
}

/**
 * @tc.name: GRDDocSyncConflictMapTest002
 * @tc.desc: A and B insert conflict element in one map
 * @tc.type: FUNC
 * @tc.require:
 * @tc.author: zhaoliang
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictMapTest002, TestSize.Level0)
{
    char strValue1[100] = {0};
    char strValue2[100] = {0};
    ASSERT_LT(0, sprintf_s(strValue1, sizeof(strValue1), "{\"type\":%u,\"value\":\"1234\"}", 57));
    ASSERT_LT(0, sprintf_s(strValue2, sizeof(strValue2), "{\"type\":%u,\"value\":\"5678\"}", 57));
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue1), GRD_OK);

    char *value = nullptr;
    GRD_ElementIdT elementId = {.equipId = g_equipIdA, .incrClock = 0};
    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    ASSERT_EQ(GRD_MapDelete(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key"), GRD_OK);
    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_NO_DATA);
    EXPECT_TRUE(value == nullptr);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_MapSet(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue2), GRD_OK);
    GRD_ElementIdT elementId2 = {.equipId = g_equipIdB, .incrClock = 0};
    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // equipA/equipB upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 2u);
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1u);
    // equipA/equipB download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 1u);
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2u);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);
}

/**
 * @tc.name: GRDDocSyncConflictMapTest003
 * @tc.desc: A and B insert conflict element in one map
 * @tc.type: FUNC
 * @tc.require:
 * @tc.author: zhaoliang
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictMapTest003, TestSize.Level0)
{
    char strValue1[100] = {0};
    ASSERT_LT(0, sprintf_s(strValue1, sizeof(strValue1), "{\"type\":%u,\"value\":\"1234\"}", 57));
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue1), GRD_OK);

    char *value = nullptr;
    GRD_ElementIdT elementId = {.equipId = g_equipIdA, .incrClock = 0};
    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // delete by B
    ASSERT_EQ(GRD_MapDelete(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key"), GRD_OK);
    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_NO_DATA);
    EXPECT_TRUE(value == nullptr);

    // delete by A

    ASSERT_EQ(GRD_MapDelete(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key"), GRD_OK);
    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_NO_DATA);
    EXPECT_TRUE(value == nullptr);

    // equipA/equipB upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1u);
    // equipA/equipB download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 1u);
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_NO_DATA);
    EXPECT_TRUE(value == nullptr);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_NO_DATA);
    EXPECT_TRUE(value == nullptr);
}

/**
 * @tc.name: GRDDocSyncConflictMapTest004
 * @tc.desc: A and B insert conflict element in one map
 * @tc.type: FUNC
 * @tc.require:
 * @tc.author: zhaoliang
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictMapTest004, TestSize.Level0)
{
    char strValue1[100] = {0};
    char strValue2[100] = {0};
    char strValue3[100] = {0};
    ASSERT_LT(0, sprintf_s(strValue1, sizeof(strValue1), "{\"type\":%u,\"value\":\"1234\"}", 57));
    ASSERT_LT(0, sprintf_s(strValue2, sizeof(strValue2), "{\"type\":%u,\"value\":\"5678\"}", 57));
    ASSERT_LT(0, sprintf_s(strValue3, sizeof(strValue3), "{\"type\":%u,\"value\":\"91011\"}", 57));
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue1), GRD_OK);

    char *value = nullptr;
    GRD_ElementIdT elementId = {.equipId = g_equipIdA, .incrClock = 0};
    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // update by B
    GRD_ElementIdT elementId2 = {.equipId = g_equipIdB, .incrClock = 0};
    ASSERT_EQ(GRD_MapSet(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue2), GRD_OK);
    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // update by A

    GRD_ElementIdT elementId3 = {.equipId = g_equipIdA, .incrClock = 4};
    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue3), GRD_OK);
    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue3, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId3, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // equipA/equipB upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1u);
    // equipA/equipB download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 1u);
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);
}

/**
 * @tc.name: GRDDocSyncConflictMapTest005
 * @tc.desc: A and B insert conflict element in one map
 * @tc.type: FUNC
 * @tc.require:
 * @tc.author: zhaoliang
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictMapTest005, TestSize.Level0)
{
    char strValue1[100] = {0};
    char strValue2[100] = {0};
    char strValue3[100] = {0};
    ASSERT_LT(0, sprintf_s(strValue1, sizeof(strValue1), "{\"type\":%u,\"value\":\"1234\"}", 57));
    ASSERT_LT(0, sprintf_s(strValue2, sizeof(strValue2), "{\"type\":%u,\"value\":\"5678\"}", 57));
    ASSERT_LT(0, sprintf_s(strValue3, sizeof(strValue3), "{\"type\":%u,\"value\":\"9101\"}", 57));
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue1), GRD_OK);

    char *value = nullptr;
    GRD_ElementIdT elementId = {.equipId = g_equipIdA, .incrClock = 0};
    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // update 3 times by B
    GRD_ElementIdT elementId2 = {.equipId = g_equipIdB, .incrClock = 8};
    ASSERT_EQ(GRD_MapSet(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue2), GRD_OK);
    ASSERT_EQ(GRD_MapSet(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue2), GRD_OK);
    ASSERT_EQ(GRD_MapSet(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue2), GRD_OK);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // update 3 times by A

    GRD_ElementIdT elementId3 = {.equipId = g_equipIdA, .incrClock = 12};
    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue3), GRD_OK);
    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue3), GRD_OK);
    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue3), GRD_OK);

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue3, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId3, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // equipA/equipB upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 3u);
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 3u);
    // equipA/equipB download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 3u);
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 3u);

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);
}

/**
 * @tc.name: GRDDocSyncConflictMapTest006
 * @tc.desc: A and B insert conflict element in one map
 * @tc.type: FUNC
 * @tc.require:
 * @tc.author: zhaoliang
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictMapTest006, TestSize.Level0)
{
    char strValue1[100] = {0};
    char strValue2[100] = {0};
    char strValue3[100] = {0};
    ASSERT_LT(0, sprintf_s(strValue1, sizeof(strValue1), "{\"type\":%u,\"value\":\"1234\"}", 57));
    ASSERT_LT(0, sprintf_s(strValue2, sizeof(strValue2), "{\"type\":%u,\"value\":\"5678\"}", 57));
    ASSERT_LT(0, sprintf_s(strValue3, sizeof(strValue3), "{\"type\":%u,\"value\":\"9101\"}", 57));
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue1), GRD_OK);

    char *value = nullptr;
    GRD_ElementIdT elementId = {.equipId = g_equipIdA, .incrClock = 0};
    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // update 3 times by B, contains once delete in it
    GRD_ElementIdT elementId2 = {.equipId = g_equipIdB, .incrClock = 8};
    ASSERT_EQ(GRD_MapSet(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue2), GRD_OK);
    ASSERT_EQ(GRD_MapDelete(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key"), GRD_OK);
    ASSERT_EQ(GRD_MapSet(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue2), GRD_OK);
    ASSERT_EQ(GRD_MapSet(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue2), GRD_OK);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // update 3 times by A, contains once delete in it

    GRD_ElementIdT elementId3 = {.equipId = g_equipIdA, .incrClock = 12};
    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue3), GRD_OK);
    ASSERT_EQ(GRD_MapDelete(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key"), GRD_OK);
    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue3), GRD_OK);
    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue3), GRD_OK);

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue3, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId3, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    // equipA/equipB upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 4u);
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 4u);
    // equipA/equipB download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 4u);
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 4u);

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId2, nullptr, &value), GRD_INVALID_ARGS);
    EXPECT_TRUE(value == nullptr);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(strValue2, value), 0);
    GRD_FreeValue(value);
    value = nullptr;
}

/**
 * @tc.name: GRDDocSyncConflictMapTest007
 * @tc.desc: A and B insert conflict map element in one map
 * @tc.type: FUNC
 * @tc.require:
 * @tc.author: zhaoliang
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictMapTest007, TestSize.Level0)
{
    char strValue1[100] = {0};
    char strValue2[100] = {0};
    char strValue3[100] = {0};
    char eleValue1[100] = {0};
    char eleValue2[100] = {0};
    char eleValue3[100] = {0};
    char eleValue4[100] = {0};
    char eleValue5[100] = {0};
    ASSERT_LT(0, sprintf_s(strValue1, sizeof(strValue1), "{\"type\":%u}", 2));
    ASSERT_LT(0, sprintf_s(strValue2, sizeof(strValue2), "{\"type\":%u}", 2));
    ASSERT_LT(0, sprintf_s(strValue3, sizeof(strValue3), "{\"type\":%u}", 2));
    ASSERT_LT(0, sprintf_s(eleValue1, sizeof(eleValue1), "{\"type\":%u,\"value\":\"%s_%d\"}", 2, g_equipIdA, 0));
    ASSERT_LT(0, sprintf_s(eleValue2, sizeof(eleValue2), "{\"type\":%u,\"value\":\"%s_%d\"}", 2, g_equipIdA, 1));
    ASSERT_LT(0, sprintf_s(eleValue4, sizeof(eleValue4), "{\"type\":%u,\"value\":\"%s_%d\"}", 2, g_equipIdA, 3));
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue1), GRD_OK);

    char *value = nullptr;
    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(eleValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ASSERT_LT(0, sprintf_s(eleValue3, sizeof(eleValue3), "{\"type\":%u,\"value\":\"%s_%d\"}", 2, g_equipIdB, 0));
    ASSERT_LT(0, sprintf_s(eleValue5, sizeof(eleValue5), "{\"type\":%u,\"value\":\"%s_%d\"}", 2, g_equipIdB, 2));

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(eleValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    // update 3 times by B, contains once delete in it
    ASSERT_EQ(GRD_MapSet(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue2), GRD_OK);
    ASSERT_EQ(GRD_MapDelete(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key"), GRD_OK);
    ASSERT_EQ(GRD_MapSet(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue2), GRD_OK);
    ASSERT_EQ(GRD_MapSet(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue2), GRD_OK);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(eleValue5, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    // update 3 times by A, contains once delete in it

    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue3), GRD_OK);
    ASSERT_EQ(GRD_MapDelete(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key"), GRD_OK);
    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue3), GRD_OK);
    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue3), GRD_OK);

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(eleValue4, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    // equipA/equipB upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 4u);
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 4u);
    // equipA/equipB download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 4u);
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 4u);

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(eleValue5, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(eleValue5, value), 0);
    GRD_FreeValue(value);
    value = nullptr;
}

/**
 * @tc.name: GRDDocSyncConflictMapTest008
 * @tc.desc: A and B insert conflict array element in one map
 * @tc.type: FUNC
 * @tc.require:
 * @tc.author: zhaoliang
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictMapTest008, TestSize.Level0)
{
    char strValue1[100] = {0};
    char strValue2[100] = {0};
    char strValue3[100] = {0};
    char eleValue1[100] = {0};
    char eleValue2[100] = {0};
    char eleValue3[100] = {0};
    char eleValue4[100] = {0};
    char eleValue5[100] = {0};
    ASSERT_LT(0, sprintf_s(strValue1, sizeof(strValue1), "{\"type\":%u}", 1));
    ASSERT_LT(0, sprintf_s(strValue2, sizeof(strValue2), "{\"type\":%u}", 1));
    ASSERT_LT(0, sprintf_s(strValue3, sizeof(strValue3), "{\"type\":%u}", 1));
    ASSERT_LT(0, sprintf_s(eleValue1, sizeof(eleValue1), "{\"type\":%u,\"value\":\"%s_%d\"}", 1, g_equipIdA, 0));
    ASSERT_LT(0, sprintf_s(eleValue2, sizeof(eleValue2), "{\"type\":%u,\"value\":\"%s_%d\"}", 1, g_equipIdA, 1));
    ASSERT_LT(0, sprintf_s(eleValue4, sizeof(eleValue4), "{\"type\":%u,\"value\":\"%s_%d\"}", 1, g_equipIdA, 3));
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue1), GRD_OK);

    char *value = nullptr;
    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(eleValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    // equipA upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);
    ASSERT_LT(0, sprintf_s(eleValue3, sizeof(eleValue3), "{\"type\":%u,\"value\":\"%s_%d\"}", 1, g_equipIdB, 0));
    ASSERT_LT(0, sprintf_s(eleValue5, sizeof(eleValue5), "{\"type\":%u,\"value\":\"%s_%d\"}", 1, g_equipIdB, 2));

    // equipB download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(eleValue1, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    // update 3 times by B, contains once delete in it
    ASSERT_EQ(GRD_MapSet(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue2), GRD_OK);
    ASSERT_EQ(GRD_MapDelete(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key"), GRD_OK);
    ASSERT_EQ(GRD_MapSet(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue2), GRD_OK);
    ASSERT_EQ(GRD_MapSet(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue2), GRD_OK);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(eleValue5, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    // update 3 times by A, contains once delete in it

    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue3), GRD_OK);
    ASSERT_EQ(GRD_MapDelete(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key"), GRD_OK);
    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue3), GRD_OK);
    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", (const char *)&strValue3), GRD_OK);

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(eleValue4, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    // equipA/equipB upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 4u);
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 4u);
    // equipA/equipB download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 4u);
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 4u);

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(eleValue5, value), 0);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    EXPECT_EQ(strcmp(eleValue5, value), 0);
    GRD_FreeValue(value);
    value = nullptr;
}

/**
 * @tc.name: GRDDocSyncConflictMapTest009
 * @tc.desc: A B and C insert conflict element in one map
 * @tc.type: FUNC
 * @tc.require: DTS2024070425349
 * @tc.author: suyue
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncConflictMapTest009, TestSize.Level0)
{
    /**
     * @tc.steps: step1. A insert key node and read
     * @tc.expected: step1. GRD_OK
     */
    GRD_ThreadPool threadPool = {ScheduleTest};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_OK);
    GRD_ICloudDB cloudDB = {&g_batchDownIndex, &g_assetLoader, BatchInsertTestWrap, QueryTestWrap,
        SendAwarenessDataTest, LockTest, UnLockTest, HeartBeatTest, CloseTest};
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", "{\"type\":2}"), GRD_OK);
    char *value = nullptr;
    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    char eleValue1[100] = {0};
    ASSERT_GE(sprintf_s(eleValue1, sizeof(eleValue1), "{\"type\":%u,\"value\":\"A_0\"}", 2), 0);
    ASSERT_STREQ(eleValue1, value);
    GRD_FreeValue(value);
    value = nullptr;

    /**
     * @tc.steps: step2. sync and read data
     * @tc.expected: step2. GRD_OK
     */

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPB_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPB_DB, &cloudDB), GRD_OK);

    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPC_DB, &threadPool), GRD_OK);
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPC_DB, &cloudDB), GRD_OK);

    // equipAupload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    // equipB/equipC download data
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 1u);
    EquipSync(EquipC, EQUIPC_DB, g_testDownload, 1u);

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    ASSERT_STREQ(eleValue1, value);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPC_DB, MAP_TABLE_NAME, nullptr, "key", &value), GRD_OK);
    ASSERT_STREQ(eleValue1, value);
    GRD_FreeValue(value);
    value = nullptr;

    /**
     * @tc.steps: step3. A B and C update key node and read
     * @tc.expected: step3. GRD_OK
     */

    char strValue1[100] = {0};
    ASSERT_GE(sprintf_s(strValue1, sizeof(strValue1), "{\"type\":%u}", 2), 0);
    GRD_ElementIdT elementId = {.equipId = g_equipIdA, .incrClock = 0};
    ASSERT_EQ(GRD_MapSet(EQUIPA_DB, MAP_TABLE_NAME, &elementId, "key1", (const char *)&strValue1), GRD_OK);

    char eleValue2[100] = {0};
    ASSERT_GE(sprintf_s(eleValue2, sizeof(eleValue2), "{\"type\":%u,\"value\":\"A_1\"}", 2), 0);
    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId, "key1", &value), GRD_OK);
    ASSERT_STREQ(eleValue2, value);
    GRD_FreeValue(value);
    value = nullptr;

    char strValue2[100] = {0};
    ASSERT_GE(sprintf_s(strValue2, sizeof(strValue2), "{\"type\":%u}", 1), 0);
    ASSERT_EQ(GRD_MapSet(EQUIPB_DB, MAP_TABLE_NAME, &elementId, "key1", (const char *)&strValue2), GRD_OK);

    char eleValue3[100] = {0};
    ASSERT_GE(sprintf_s(eleValue3, sizeof(eleValue3), "{\"type\":%u,\"value\":\"B_0\"}", 1), 0);
    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId, "key1", &value), GRD_OK);
    ASSERT_STREQ(eleValue3, value);
    GRD_FreeValue(value);
    value = nullptr;

    char strValue3[100] = {0};
    ASSERT_GE(sprintf_s(strValue3, sizeof(strValue3), "{\"type\":%u,\"value\":\"string\"}", 57), 0);
    ASSERT_EQ(GRD_MapSet(EQUIPC_DB, MAP_TABLE_NAME, &elementId, "key1", (const char *)&strValue3), GRD_OK);
    ASSERT_EQ(GRD_MapRead(EQUIPC_DB, MAP_TABLE_NAME, &elementId, "key1", &value), GRD_OK);
    ASSERT_STREQ(strValue3, value);
    GRD_FreeValue(value);
    value = nullptr;

    /**
     * @tc.steps: step4. sync and read data
     * @tc.expected: step4. GRD_OK
     */
    // equipA/equipB/equipC upload data
    EquipSync(EquipA, EQUIPA_DB, g_testUpload, 1u);
    EquipSync(EquipB, EQUIPB_DB, g_testUpload, 1u);
    EquipSync(EquipC, EQUIPC_DB, g_testUpload, 1u);
    // equipA/equipB/equipC download data
    EquipSync(EquipA, EQUIPA_DB, g_testDownload, 2u);
    EquipSync(EquipB, EQUIPB_DB, g_testDownload, 2u);
    EquipSync(EquipC, EQUIPC_DB, g_testDownload, 2u);

    ASSERT_EQ(GRD_MapRead(EQUIPA_DB, MAP_TABLE_NAME, &elementId, "key1", &value), GRD_OK);
    ASSERT_STREQ(strValue3, value);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPB_DB, MAP_TABLE_NAME, &elementId, "key1", &value), GRD_OK);
    ASSERT_STREQ(strValue3, value);
    GRD_FreeValue(value);
    value = nullptr;

    ASSERT_EQ(GRD_MapRead(EQUIPC_DB, MAP_TABLE_NAME, &elementId, "key1", &value), GRD_OK);
    ASSERT_STREQ(strValue3, value);
    GRD_FreeValue(value);
    value = nullptr;
}

/**
 * @tc.name: GRDDocSyncParamTest001
 * @tc.desc: test sync api when para invalid
 * @tc.type: FUNC
 * @tc.require: DTS2024073106613
 * @tc.author: suyuchen
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncParamTest001, TestSize.Level0)
{
    /**
     * @tc.steps: step1 call GRD_sync when db is null
     * @tc.expected: step1. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_Sync(nullptr, nullptr), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step2 call GRD_sync when Sync config is null
     * @tc.expected: step2. GRD_INVALID_ARGS
     */

    ASSERT_EQ(GRD_Sync(EQUIPA_DB, nullptr), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step3 call GRD_sync when mode out of range
     * @tc.expected: step3. GRD_INVALID_ARGS
     */
    GRD_SyncConfigT config = {
        .mode = GRD_SYNC_MODE_INVALID,
        .equipIds = nullptr,
        .size = 0u,
        .callbackFunc = nullptr,
        .timeout = 0u,
        .syncId = 1u,
    };
    ASSERT_EQ(GRD_Sync(EQUIPA_DB, &config), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step4 call GRD_sync when equipIds is null
     * @tc.expected: step4. GRD_INVALID_ARGS
     */
    config.mode = GRD_SYNC_MODE_DOWNLOAD;
    ASSERT_EQ(GRD_Sync(EQUIPA_DB, &config), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step5 call GRD_sync when size is 0
     * @tc.expected: step5. GRD_INVALID_ARGS
     */
    const size_t size = 8;
    char *equipId = static_cast<char *>(malloc(size));
    ASSERT_NE(equipId, nullptr);
    config.equipIds = &equipId;
    ASSERT_EQ(GRD_Sync(EQUIPA_DB, &config), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step6 call GRD_sync when callback func is null
     * @tc.expected: step6. GRD_INVALID_ARGS
     */
    config.size = 1u;
    config.callbackFunc = nullptr;
    ASSERT_EQ(GRD_Sync(EQUIPA_DB, &config), GRD_INVALID_ARGS);
    free(equipId);
}

/**
 * @tc.name: GRDDocSyncParamTest002
 * @tc.desc: test registry cloud db api when para invalid
 * @tc.type: FUNC
 * @tc.require: DTS2024073106613
 * @tc.author: suyuchen
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncParamTest002, TestSize.Level0)
{
    /**
     * @tc.steps: step1 call GRD_RegistryCloudDB when db is null
     * @tc.expected: step1. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_RegistryCloudDB(nullptr, nullptr), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step2 call GRD_RegistryCloudDB when iCloud is null
     * @tc.expected: step2. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, nullptr), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step3 call GRD_RegistryCloudDB when cloudDb is null
     * @tc.expected: step3. GRD_INVALID_ARGS
     */
    GRD_ICloudDB cloudDb = {
        .cloudDB = nullptr,
        .assetLoader = nullptr,
        .batchInsert = nullptr,
        .query = nullptr,
        .sendAwarenessData = nullptr,
        .lock = nullptr,
        .unLock = nullptr,
        .heartBeat = nullptr,
        .close = nullptr,
    };
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDb), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step4 call GRD_RegistryCloudDB when batchInsert is null
     * @tc.expected: step4. GRD_INVALID_ARGS
     */
    cloudDb.cloudDB = &g_batchDownIndex;
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDb), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step5 call GRD_RegistryCloudDB when query is null
     * @tc.expected: step5. GRD_INVALID_ARGS
     */
    cloudDb.batchInsert = BatchInsertTestWrap;
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDb), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step6 call GRD_RegistryCloudDB when sendAwarenessData is null
     * @tc.expected: step6. GRD_INVALID_ARGS
     */
    cloudDb.query = QueryTestWrap;
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDb), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step7 call GRD_RegistryCloudDB when lock is null
     * @tc.expected: step7. GRD_INVALID_ARGS
     */
    cloudDb.sendAwarenessData = SendAwarenessDataTest;
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDb), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step8 call GRD_RegistryCloudDB when unlock is null
     * @tc.expected: step8. GRD_INVALID_ARGS
     */
    cloudDb.lock = LockTest;
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDb), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step9 call GRD_RegistryCloudDB when heartBeat is null
     * @tc.expected: step9. GRD_INVALID_ARGS
     */
    cloudDb.unLock = UnLockTest;
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDb), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step10 call GRD_RegistryCloudDB when close is null
     * @tc.expected: step10. GRD_INVALID_ARGS
     */
    cloudDb.heartBeat = HeartBeatTest;
    ASSERT_EQ(GRD_RegistryCloudDB(EQUIPA_DB, &cloudDb), GRD_INVALID_ARGS);
}

/**
 * @tc.name: GRDDocSyncParamTest003
 * @tc.desc: test registry thread pool api when para invalid
 * @tc.type: FUNC
 * @tc.require: DTS2024073106613
 * @tc.author: suyuchen
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncParamTest003, TestSize.Level0)
{
    /**
     * @tc.steps: step1. call GRD_RegistryThreadPool when db is null
     * @tc.expected: step1. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_RegistryThreadPool(nullptr, nullptr), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step2. call GRD_RegistryThreadPool when threadPool is null
     * @tc.expected: step2. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, nullptr), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step3. call GRD_RegistryThreadPool when schedule is null
     * @tc.expected: step3. GRD_INVALID_ARGS
     */
    GRD_ThreadPool threadPool = {0};
    ASSERT_EQ(GRD_RegistryThreadPool(EQUIPA_DB, &threadPool), GRD_INVALID_ARGS);
}

/**
 * @tc.name: GRDDocSyncParamTest004
 * @tc.desc: test register equipId api when para invalid
 * @tc.type: FUNC
 * @tc.require: DTS2024073106613
 * @tc.author: suyuchen
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncParamTest004, TestSize.Level0)
{
    /**
     * @tc.steps: step1 call GRD_RegisterEquipId when db is null
     * @tc.expected: step1. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_RegisterEquipId(nullptr, nullptr), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step2 call GRD_RegisterEquipId when threadPool is null
     * @tc.expected: step2. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_RegisterEquipId(EQUIPA_DB, nullptr), GRD_INVALID_ARGS);
}

/**
 * @tc.name: GRDDocSyncParamTest005
 * @tc.desc: test oplog relay apply api when para invalid
 * @tc.type: FUNC
 * @tc.require: DTS2024073106613
 * @tc.author: suyuchen
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncParamTest005, TestSize.Level0)
{
    /**
     * @tc.steps: step1 call GRD_OplogRelayApply when db is null
     * @tc.expected: step1. GRD_INVALID_ARGS
     */
    char *applyInfo = nullptr;
    ASSERT_EQ(GRD_OplogRelayApply(nullptr, nullptr, &applyInfo), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step2 call GRD_OplogRelayApply when equipId is null
     * @tc.expected: step2. GRD_OK
     */
    ASSERT_EQ(GRD_OplogRelayApply(EQUIPA_DB, nullptr, &applyInfo), GRD_OK);

    /**
     * @tc.steps: step3 call GRD_OplogRelayApply when equipId is wrong
     * @tc.expected: step3. GRD_INNER_ERR
     */
    char equipId[] = "equip";
    ASSERT_EQ(GRD_OplogRelayApply(EQUIPA_DB, equipId, &applyInfo), GRD_INNER_ERR);

    /**
     * @tc.steps: step4 call GRD_OplogRelayApply when applyInfo is null
     * @tc.expected: step4. GRD_INVALID_ARGS
     */

    GRD_DocNodeInfoT nodeXmlElement = {
        .type = GRD_XML_ELEMENT_TYPE,
        .content = "elementName1",
    };
    GRD_XmlOpPosition addr = {.tableName = TABLE_NAME, .elementId = nullptr};

    GRD_ElementIdT *elementId1 = nullptr;
    ASSERT_EQ(GRD_XmlFragmentInsert(EQUIPA_DB, &addr, 0, &nodeXmlElement, &elementId1), GRD_OK);
    ASSERT_EQ(GRD_OplogRelayApply(EQUIPA_DB, g_equipIdA, nullptr), GRD_INVALID_ARGS);

    GRD_XmlFreeElementId(elementId1);
}

/**
 * @tc.name: GRDDocSyncParamTest006
 * @tc.desc: test write log with equipId api when para invalid
 * @tc.type: FUNC
 * @tc.require: DTS2024073106613
 * @tc.author: suyuchen
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncParamTest006, TestSize.Level0)
{
    /**
     * @tc.steps: step1 call GRD_WriteLogWithEquipId when db is null
     * @tc.expected: step1. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_WriteLogWithEquipId(nullptr, nullptr, nullptr, 0, nullptr), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step2 call GRD_WriteLogWithEquipId when equipId is null
     * @tc.expected: step2. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_WriteLogWithEquipId(EQUIPA_DB, nullptr, nullptr, 0, nullptr), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step3 call GRD_WriteLogWithEquipId when data is null
     * @tc.expected: step3. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_WriteLogWithEquipId(EQUIPA_DB, g_equipIdA, nullptr, 0, nullptr), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step4 call GRD_WriteLogWithEquipId when size is 0
     * @tc.expected: step4. GRD_INVALID_ARGS
     */
    uint8_t data = 0;
    ASSERT_EQ(GRD_WriteLogWithEquipId(EQUIPA_DB, g_equipIdA, &data, 0, nullptr), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step5 call GRD_WriteLogWithEquipId when size is greater than 10MB
     * @tc.expected: step5. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_WriteLogWithEquipId(EQUIPA_DB, g_equipIdA, &data, 10 * 1024 * 1024 + 1, nullptr), GRD_INVALID_ARGS);
}

/**
 * @tc.name: GRDDocSyncParamTest007
 * @tc.desc: test sync cloud watermark api when para invalid
 * @tc.type: FUNC
 * @tc.require: DTS2024073106613
 * @tc.author: suyuchen
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncParamTest007, TestSize.Level0)
{
    /**
     * @tc.steps: step1 call GRD_GetSyncCloudWatermark when db is null
     * @tc.expected: step1. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_GetSyncCloudWatermark(nullptr, nullptr), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step2 call GRD_GetSyncCloudWatermark when watermark is null
     * @tc.expected: step2. GRD_INVALID_ARGS
     */
    ASSERT_EQ(GRD_GetSyncCloudWatermark(EQUIPA_DB, nullptr), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step3 call GRD_GetSyncCloudWatermark when *watermark is not null
     * @tc.expected: step3. GRD_INVALID_ARGS
     */
    const size_t bufSize = 8;
    char *watermark = static_cast<char *>(malloc(bufSize));
    ASSERT_NE(watermark, nullptr);
    ASSERT_EQ(GRD_GetSyncCloudWatermark(EQUIPA_DB, &watermark), GRD_INVALID_ARGS);
    free(watermark);
}

/**
 * @tc.name: GRDDocSyncEquipIdTest001
 * @tc.desc: test equip set and get for multi dbs
 * @tc.type: FUNC
 * @tc.require: AR.SR.IR20250102001439.001.001
 * @tc.author: huanghe
 */
HWTEST_F(GRDDocSyncApiTest, GRDDocSyncEquipIdTest001, TestSize.Level0)
{
    /**
     * @tc.steps: step1. close db and emtpy equipId
     * @tc.expected: step1. GRD_OK
     */
    ASSERT_EQ(GRD_DBClose(EQUIPA_DB, GRD_DB_CLOSE), GRD_OK);
    EQUIPA_DB = nullptr;
    EXPECT_EQ(DbTestTool::RemoveDir(TEST_DB_PATH), 0);

    ASSERT_EQ(GRD_DBClose(EQUIPB_DB, GRD_DB_CLOSE), GRD_OK);
    EQUIPB_DB = nullptr;
    EXPECT_EQ(DbTestTool::RemoveDir(TEST_DB_B), 0);
    ASSERT_EQ(GRD_DBClose(EQUIPC_DB, GRD_DB_CLOSE), GRD_OK);
    EQUIPC_DB = nullptr;
    EXPECT_EQ(DbTestTool::RemoveDir(TEST_DB_C), 0);

    /**
     * @tc.steps: step2. test invalid equip Id param
     * @tc.expected: step2. not GRD_OK
     */
    char *tmp = nullptr;
    ASSERT_EQ(GRD_GetEquipId(EQUIPB_DB, nullptr), GRD_INVALID_ARGS);
    ASSERT_EQ(GRD_GetEquipId(nullptr, nullptr), GRD_INVALID_ARGS);
    ASSERT_EQ(GRD_GetEquipId(nullptr, &tmp), GRD_INVALID_ARGS);
    ASSERT_EQ(tmp, nullptr);

    ASSERT_EQ(GRD_SetEquipId(EQUIPA_DB, "A"), GRD_INVALID_ARGS);
    ASSERT_EQ(GRD_SetEquipId(EQUIPA_DB, nullptr), GRD_INVALID_ARGS);
    ASSERT_EQ(GRD_SetEquipId(EQUIPA_DB, "!$#$#%"), GRD_INVALID_ARGS);

    /**
     * @tc.steps: step3. open new db
     * @tc.expected: step3. GRD_OK
     */
    EXPECT_EQ(DbTestTool::MakeDir(TEST_DB_PATH), 0);
    ASSERT_EQ(GRD_DBOpen(TEST_DB_FILE_A, TEST_DB_CONFIG, GRD_DB_OPEN_CREATE, &EQUIPA_DB), GRD_OK);
    EXPECT_EQ(DbTestTool::MakeDir(TEST_DB_B), 0);
    ASSERT_EQ(GRD_DBOpen(TEST_DB_FILE_B, TEST_DB_CONFIG, GRD_DB_OPEN_CREATE, &EQUIPB_DB), GRD_OK);
    EXPECT_EQ(DbTestTool::MakeDir(TEST_DB_C), 0);
    ASSERT_EQ(GRD_DBOpen(TEST_DB_FILE_C, TEST_DB_CONFIG, GRD_DB_OPEN_CREATE, &EQUIPC_DB), GRD_OK);

    /**
     * @tc.steps: step4. set equipId and conclict equip for db A and get from db
     * @tc.expected: step4. GRD_OK and GMERR_EQUIP_ID_CONFLICT
     */
    ASSERT_EQ(GRD_GetEquipId(EQUIPA_DB, &tmp), GRD_NOT_SUPPORT);
    ASSERT_EQ(GRD_SetEquipId(EQUIPA_DB, "A"), GRD_OK);
    ASSERT_EQ(GRD_SetEquipId(EQUIPA_DB, "A"), GRD_EQUIP_ID_CONFLICT);
    char *equipIdA = nullptr;
    ASSERT_EQ(GRD_GetEquipId(EQUIPA_DB, &equipIdA), GRD_OK);
    ASSERT_STREQ(equipIdA, "A");
    /**
     * @tc.steps: step5. set equipId for db B and get from db
     * @tc.expected: step5. GRD_OK
     */
    ASSERT_EQ(GRD_SetEquipId(EQUIPB_DB, "B"), GRD_OK);
    ASSERT_EQ(GRD_SetEquipId(EQUIPB_DB, "*&*&&**^%^**^"), GRD_INVALID_EQUIP_ID);
    char *equipIdB = nullptr;
    ASSERT_EQ(GRD_GetEquipId(EQUIPB_DB, &equipIdB), GRD_OK);
    ASSERT_STREQ(equipIdB, "B");
    /**
     * @tc.steps: step6. set equipId for db C and get from db
     * @tc.expected: step6. GRD_OK
     */
    ASSERT_EQ(GRD_SetEquipId(EQUIPC_DB, "C"), GRD_OK);
    ASSERT_EQ(GRD_SetEquipId(EQUIPC_DB, "CCCCC"), GRD_EQUIP_ID_CONFLICT);
    char *equipIdC = nullptr;
    ASSERT_EQ(GRD_GetEquipId(EQUIPC_DB, &equipIdC), GRD_OK);
    ASSERT_STREQ(equipIdC, "C");

    GRD_DocFree(equipIdA);
    equipIdA = nullptr;
    GRD_DocFree(equipIdB);
    equipIdB = nullptr;
    GRD_DocFree(equipIdC);
    equipIdC = nullptr;

    /**
     * @tc.steps: step7. close db and reopen db
     * @tc.expected: step7. GRD_OK
     */
    ASSERT_EQ(GRD_DBClose(EQUIPA_DB, GRD_DB_CLOSE), GRD_OK);
    ASSERT_EQ(GRD_DBClose(EQUIPB_DB, GRD_DB_CLOSE), GRD_OK);
    ASSERT_EQ(GRD_DBClose(EQUIPC_DB, GRD_DB_CLOSE), GRD_OK);
    ASSERT_EQ(GRD_DBOpen(TEST_DB_FILE_A, TEST_DB_CONFIG, GRD_DB_OPEN_CREATE, &EQUIPA_DB), GRD_OK);
    ASSERT_EQ(GRD_DBOpen(TEST_DB_FILE_B, TEST_DB_CONFIG, GRD_DB_OPEN_CREATE, &EQUIPB_DB), GRD_OK);
    ASSERT_EQ(GRD_DBOpen(TEST_DB_FILE_C, TEST_DB_CONFIG, GRD_DB_OPEN_CREATE, &EQUIPC_DB), GRD_OK);

    /**
     * @tc.steps: step8. read equip id from db
     * @tc.expected: step8. GRD_OK
     */
    ASSERT_EQ(GRD_GetEquipId(EQUIPA_DB, &equipIdA), GRD_OK);
    ASSERT_STREQ(equipIdA, "A");
    ASSERT_EQ(GRD_GetEquipId(EQUIPB_DB, &equipIdB), GRD_OK);
    ASSERT_STREQ(equipIdB, "B");
    ASSERT_EQ(GRD_GetEquipId(EQUIPC_DB, &equipIdC), GRD_OK);
    ASSERT_STREQ(equipIdC, "C");

    GRD_DocFree(equipIdA);
    equipIdA = nullptr;
    GRD_DocFree(equipIdB);
    equipIdB = nullptr;
    GRD_DocFree(equipIdC);
    equipIdC = nullptr;
}
