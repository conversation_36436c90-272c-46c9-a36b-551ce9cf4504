{"id": "", "label": "GMDB 503.1.0", "attrs": {}, "children": [{"id": "IR20230206000519", "label": "IR.安全可靠高性能直连写", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "SR.IR20230206000519.001", "label": "SR Node.【直连写】轻量化事务-一写多读模式下直连同步单写基本功能", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230206000519.001.001", "label": "AR Node.Heap容器支持直连写", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.001.002", "label": "AR Node.直连写支持鉴权", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.001.003", "label": "AR Node.直连写支持主键索引", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.001.004", "label": "AR Node.直连写支持hashchluster索引", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.001.005", "label": "AR Node.直连写支持lpm4&lpm6索引", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.001.006", "label": "AR Node.客户端支持共享内存申请（RTOS）", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.001.007", "label": "AR Node.客户端支持共享内存申请（HPE）", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.001.008", "label": "AR Node.提供直连写运行上下文数据结构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.001.009", "label": "AR Node.直连写支持update操作", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.001.010", "label": "AR Node.直连写支持insert操作", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.001.011", "label": "AR Node.直连写支持replace操作", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.001.012", "label": "AR Node.直连写支持共享内存纵向隔离", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.001.013", "label": "AR Node.直连写支持后台缩容", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230206000519.002", "label": "SR Node.【直连写】轻量化事务下直连同步单写功能补齐", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230206000519.002.001", "label": "AR Node.直连写支持delete操作", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.002.005", "label": "AR Node.适配老化对账", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.002.008", "label": "AR Node.localhash索引支持直连写", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.002.009", "label": "AR Node.local索引支持直连写", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.002.010", "label": "AR Node.支持结构化直连写", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.002.011", "label": "AR Node.heap容器适配直连写", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.002.012", "label": "AR Node.索引适配直连写", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230206000519.003", "label": "SR Node.【直连写】轻量化事务下-一写多读下直连同步单写支持聚簇容器", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230206000519.003.001", "label": "AR Node.聚簇容器支持直连写", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230206000519.004", "label": "SR Node.【直连写】共享内存映射虚拟地址空间集中管理", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230206000519.004.001", "label": "AR Node.客户端支持虚拟地址空间预留地址段（RTOS）", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.004.003", "label": "AR Node.客户端预留地址段管理算法", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.004.004", "label": "AR Node.基于虚拟地址空间集中管理实现DB共享内存拉远", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230206000519.005", "label": "SR Node.【直连写】客户端（RTOS）信号处理保证直连写可靠性", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230206000519.005.002", "label": "AR Node.客户端信号处理函数适配", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230206000519.006", "label": "SR Node.【直连写】直连写DFX能力构建", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230206000519.006.001", "label": "AR Node.适配DML信息和DML操作统计视图", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.006.005", "label": "AR Node.全量视图排查", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.26e62c4a", "label": "SR Node.【直连写】直连写适配表升降级", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.26e62c4a.002", "label": "AR Node.直连写适配表升降级", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.26e62c4a.003", "label": "AR Node.表结构升降级适配客户端多版本缓存", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.85d88f21", "label": "SR Node.【直连写】直连写适配老订阅推送", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.85d88f21.002", "label": "AR Node.直连写适配老的订阅推送执行层流程", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.85d88f21.003", "label": "AR Node.runtime适配直连写老订阅推送执行流程", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.06c2c626", "label": "SR Node.【直连写】直连写适配新订阅推送", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.06c2c626.001", "label": "AR Node.直连写适配新订阅推送", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.816abff0", "label": "SR Node.【直连写】直连写聚簇容器相关特性适配", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.816abff0.001", "label": "AR Node.聚簇容器适配表结构升降级", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.816abff0.002", "label": "AR Node.聚簇容器适配老化对账", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.816abff0.003", "label": "AR Node.聚簇容器适配新老订阅", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230206000519.011", "label": "SR Node.【直连写】catalog放共享内存", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230206000519.011.001", "label": "AR Node.元数据VertexLabel放共享内存", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230206000519.012", "label": "SR Node.【直连写】客户端适配catalog放入共享内存", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230206000519.012.001", "label": "AR Node.客户端适配VertexLabel放入共享内存", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.012.002", "label": "AR Node.服务端提供客户端异常断连引用计数清理能力", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.012.003", "label": "AR Node.DM适配catalog放入共享内存", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.012.004", "label": "AR Node.服务端提供定时器扫描并删除可删除的VertexLabel", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.012.006", "label": "AR Node.Yang适配catalog放入共享内存", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.012.007", "label": "AR Node.kvtable放入共享内存", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.012.008", "label": "AR Node.edgelabel放入共享内存", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.012.009", "label": "AR Node.客户端支持缓存多版本vertexlabel元数据", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230206000519.013", "label": "SR Node.【直连写】ip4forward表同步直连单写性能优化", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230206000519.013.001", "label": "AR Node.Ip4forward表同步单写性能优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.013.002", "label": "AR Node.直连写运行上下文复用", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.013.003", "label": "AR Node.结构化写时反序列化优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.013.004", "label": "AR Node.直连写执行流程中内存复用", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230206000519.014", "label": "SR Node.【直连写】直连写安全&可靠性&可用性增强", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230206000519.014.001", "label": "AR Node.直连写支持审计日志", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.014.003", "label": "AR Node.增加直连写系统配置选项", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.014.004", "label": "AR Node.直连写支持获取vertexlabel的truncate状态", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.014.005", "label": "AR Node.客户端支持获取系统配置项", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.014.007", "label": "AR Node.直连写客户端端异常断链事务槽位清理", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.014.009", "label": "AR Node.Transaction Manager", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.014.010", "label": "AR Node.直连写订阅消息池新增视图", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230206000519.014.011", "label": "AR Node.直连写订阅消息池适配HPE", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230206000519.015", "label": "SR Node.【直连写】DB适配OS提供的分域隔离特性", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "SR.IR20230206000519.016", "label": "SR Node.【直连写】DB适配Dopra提供的地址集中管理", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "SR.IR20230206000519.017", "label": "SR Node.【直连写】DB适配OS提供的硬件watchpoint特性", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "IR20230419001695", "label": "IR.支持DB在光启平台配置数据存储和访问", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "SR.IR20230419001695.017", "label": "SR Node.用户态运行环境支持RTOS arm32", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419001695.017.001", "label": "AR Node.用户态运行环境支持RTOS arm32", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.017.002", "label": "AR Node.arm32 dt 用例适配", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419001695.018", "label": "SR Node.用户态运行环境支持支持SUSE x8664", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419001695.018.001", "label": "AR Node.用户态运行环境支持支持SUSE x8664", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419001695.019", "label": "SR Node.服务端部署启动支持和客户端共进程", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419001695.019.001", "label": "AR Node.客户端和服务端资源冲突排查和适配", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.019.002", "label": "AR Node.客户端支持欧拉下的客户端相关视图功能", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.019.003", "label": "AR Node.提供gmserver.so支持gmserver线程启动", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.019.004", "label": "AR Node.内存模块资源冲突排查和适配", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.019.005", "label": "AR Node.内存模块适配底层hpe共享内存引用计数语义变化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.019.006", "label": "AR Node.工具支持embed部署", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.019.007", "label": "AR Node.工具*embed模式部署支持注册打印、通信、日志等功能（同其他客户端注册流程）", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419001695.020", "label": "SR Node.服务端部署启动支持加载dopra的内存管理", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419001695.020.001", "label": "AR Node.客户端支持dopra的内存管理", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.020.002", "label": "AR Node.内存模块支持动态内存的注册", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.020.003", "label": "AR Node.服务端支持dopra的内存管理", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419001695.021", "label": "SR Node.同步通信支持dophi通信", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419001695.021.001", "label": "AR Node.socket通信能力抽取", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419001695.022", "label": "SR Node.告警支持光启平台的告警上报", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "SR.IR20230419001695.023", "label": "SR Node.运行日志支持光启平台的日志打印", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419001695.023.001", "label": "AR Node.运行日志支持光启平台的日志打印", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419001695.024", "label": "SR Node.自研热补丁支持光启平台场景", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419001695.024.002", "label": "AR Node.自研热补丁支持光启平台场景", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419001695.025", "label": "SR Node.支持按需持久化", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419001695.025.001", "label": "AR Node.按需持久化基础功能", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.025.002", "label": "AR Node.索引重建", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.025.003", "label": "AR Node.支持指定路径备份和恢复", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.025.004", "label": "AR Node.表项可配置不持久化数据", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.025.005", "label": "AR Node.事务并发控制", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.025.006", "label": "AR Node.系统表模块", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.025.007", "label": "AR Node.space模块", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.025.008", "label": "AR Node.DurableMemData模块", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.025.009", "label": "AR Node.支持直连读", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419001695.026", "label": "SR Node.支持增量持久化", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419001695.026.001", "label": "AR Node.支持Redo使用保留内存", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.026.002", "label": "AR Node.支持多区备份", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.026.003", "label": "AR Node.redo模块", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.026.004", "label": "AR Node.check point模块", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.026.005", "label": "AR Node.recovery模块", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.026.006", "label": "AR Node.heap持久化适配", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.026.007", "label": "AR Node.fsm 持久化适配", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.026.008", "label": "AR Node.事务持久化适配", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.026.012", "label": "AR Node.持久化数据二进制一致性", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.026.013", "label": "AR Node.持久化数据压缩", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.026.014", "label": "AR Node.门禁用例适配持久化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.026.015", "label": "AR Node.支持表项配置不持久化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419001695.027", "label": "SR Node.支持悲观事务记录级别并发的可重复读隔离级别的DML", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419001695.027.001", "label": "AR Node.DB支持悲观事务可重复读隔离级别可配置", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.027.002", "label": "AR Node.直连读支持显式开启事务并按对应级别读取数据", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.027.003", "label": "AR Node.SE支持按可重复读级别读取可见版本", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.027.005", "label": "AR Node.索引支持悲观事务可重复读隔离级别", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.027.006", "label": "AR Node.服务端EE层支持可重复读隔离级别", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419001695.028", "label": "SR Node.持久化DFX：视图补齐系统表", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "SR.IR20230419001695.029", "label": "SR Node.持久化DFX：视图补齐checkpoint", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "SR.IR20230419001695.030", "label": "SR Node.持久化DFX：视图补齐durableData", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419001695.030.001", "label": "AR Node.durableMemData视图", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419001695.031", "label": "SR Node.持久化场景支持表升级1期：支持兼容表升级约束的持久化", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419001695.031.001", "label": "AR Node.系统表支持表升级", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.031.003", "label": "AR Node.支持临时关闭持久化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419001695.033", "label": "SR Node.系统表基础功能完善", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419001695.026.010", "label": "AR Node.系统表Bootstrap流程", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.026.011", "label": "AR Node.EE适配系统表启动流程", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419001695.033.004", "label": "AR Node.系统表表结构定义和基础接口", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419001695.034", "label": "SR Node.系统表组件化", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419001695.031.002", "label": "AR Node.catalog-系统表组件化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}]}, {"id": "IR20230419002577", "label": "IR.Datalog转发路径推理（含热补丁）", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "SR.IR20230419002577.004", "label": "SR Node.支持规则读取能力集KV表", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419002577.004.002", "label": "AR Node.UDF中支持读取能力集KV表", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.004.003", "label": "AR Node.支持access_kv关键字声明要访问的kv表", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419002577.005", "label": "SR Node.pubsub型资源表支持错误码返回", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419002577.005.001", "label": "AR Node.订阅端支持向响应头写入错误码", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.005.002", "label": "AR Node.runtime解析订阅端返回错误码", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.005.003", "label": "AR Node.同步pubsub支持解析错误码", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.005.004", "label": "AR Node.runtime支持将额外的错误码解析返回", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419002577.006", "label": "SR Node.UDF支持dtlReservedCount值传递", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419002577.006.001", "label": "AR Node.IR-支持udf传入dtlReservedCount", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.006.002", "label": "AR Node.gmconvert工具支持udf生成dtlReservedCount", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419002577.007", "label": "SR Node.支持变长5-10K变长byte字段", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419002577.007.001", "label": "AR Node.datalog新增语法支持变长字段", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.007.002", "label": "AR Node.pubsub支持传递变长字段", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419002577.010", "label": "SR Node.Not join支持多张表", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419002577.010.001", "label": "AR Node.verifier-NOT join支持多张表", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.010.002", "label": "AR Node.analyzer-支持生成多表NOT join的逻辑执行计划", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.010.003", "label": "AR Node.planner-支持生成多表NOT join的物理执行计划", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419002577.014", "label": "SR Node.Datalog支持内存极限场景下可回滚", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419002577.014.001", "label": "AR Node.运行时动态内存超限可回滚", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.014.002", "label": "AR Node.存储逃生内存超限可回滚", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.014.003", "label": "AR Node.内存超限导致加载卸载失败可回滚", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.014.004", "label": "AR Node.pubsub内存超限可回滚", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419002577.015", "label": "SR Node.datalog运行时内存优化", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419002577.015.001", "label": "AR Node.批量删除内存优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.015.002", "label": "AR Node.执行过程内存和增量计算内存分开", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.015.003", "label": "AR Node.聚合算子索引扫描内存复用", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.015.004", "label": "AR Node.中间结果集传递buffer", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.015.005", "label": "AR Node.datalog表定义支持让用户显示设置bucket和max_record_count", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.015.006", "label": "AR Node.执行完一张表的所有plan之后清空该表的delta表", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419002577.018", "label": "SR Node.datalog热补丁支持规则升级", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419002577.018.001", "label": "AR Node.Service-支持规则升级", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.018.002", "label": "AR Node.Planner-支持规则升级", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.018.004", "label": "AR Node.EE-支持后台重刷输出表中的旧数据", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.018.005", "label": "AR Node.gmprecompiler-支持规则升级", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.018.006", "label": "AR Node.gmimport-工具支持加载热补丁so", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.018.007", "label": "AR Node.gmconvert-工具支持生成升级gmjson", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419002577.019", "label": "SR Node.datalog热补丁支持表和udf升级", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419002577.019.001", "label": "AR Node.Service-支持表结构、udf升级", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.019.002", "label": "AR Node.Catalog-udfCache支持udf升级", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.019.003", "label": "AR Node.EE-支持表结构和udf升级", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.019.004", "label": "AR Node.gmprecompiler-支持表和udf升级", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419002577.020", "label": "SR Node.datalog热补丁支持补丁卸载", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419002577.020.001", "label": "AR Node.Service-支持补丁卸载", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.020.002", "label": "AR Node.gmconvert-工具支持生成回退gmjson", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.020.003", "label": "AR Node.gmprecompiler-工具支持生成回退序列化文件", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.020.004", "label": "AR Node.热补丁升级期间，不允许触发表记录过期", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419002577.021", "label": "SR Node.datalog热补丁视图", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419002577.021.001", "label": "AR Node.Service-新增datalog热补丁升级状态的视图", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419002577.022", "label": "SR Node.datalog热补丁支持内存防呆", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419002577.022.001", "label": "AR Node.Service-热补丁支持内存防呆", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419002577.024", "label": "SR Node.支持datalog运行过程链路日志打印", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419002577.024.001", "label": "AR Node.打印关键执行路径耗时和内存开销", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.024.002", "label": "AR Node.一键分析所有文件中的datalog日志", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419002577.025", "label": "SR Node.隔离同步表和异步表订阅连接", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419002577.025.001", "label": "AR Node.隔离datalog表订阅连接和外部表订阅连接", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419002577.026", "label": "SR Node.datalog元数据序列化反序列化只解析业务相关数据", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419002577.026.001", "label": "AR Node.离线编译向文件中写VertexLabel，只写入业务相关数据；在线加载从文件中读取只读取业务相关数据", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.026.002", "label": "AR Node.离线编译向文件中写Udf，只写入业务相关数据；在线加载从文件中读取只读取业务相关数据", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.026.003", "label": "AR Node.离线编译向文件中写ResPool，只写入业务相关数据；在线加载从文件中读取只读取业务相关数据", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.026.004", "label": "AR Node.离线编译向文件中写ResPool绑定关系，只写入业务相关数据；在线加载从文件中读取只读取业务相关数据", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.026.005", "label": "AR Node.离线编译向文件中写Plan，只写入业务相关数据；在线加载从文件中读取只读取业务相关数据", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230419002577.027", "label": "SR Node.transient_tuple表支持写入org并在事务结束时候清空", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230419002577.027.001", "label": "AR Node.编译支撑新增表示transient_tuple字段", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.027.002", "label": "AR Node.执行器支持在事务结束的时候清空transient_tuple表", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230419002577.027.003", "label": "AR Node.支持在DML请求中调用truncate逻辑", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}]}, {"id": "IR20230525000793", "label": "IR.DB可维可测易用性能力提升", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "SR.IR20230525000793.004", "label": "SR Node.对table/namespace/tablespace的删除类DDL操作支持原子性", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230525000793.004.001", "label": "AR Node.Catalog支持对table/namespace/tablespace的删除类DDL操作支持原子性", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230525000793.004.002", "label": "AR Node.索引支持对table的删除类DDL操作支持原子性", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230525000793.004.003", "label": "AR Node.Heap容器支持对table的删除类DDL操作支持原子性", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230525000793.004.004", "label": "AR Node.聚簇容器支持对table的删除类DDL操作支持原子性", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230525000793.004.005", "label": "AR Node.EE支持 对table/namespace/tablespace的删除类DDL操作支持原子性", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230525000793.005", "label": "SR Node.server申请句柄失败可靠性加固", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230525000793.005.001", "label": "AR Node.HPE申请句柄失败DFX", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230525000793.006", "label": "SR Node.Server支持调度统计能力", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230525000793.006.001", "label": "AR Node.Server支持调度统计能力", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230525000793.007", "label": "SR Node.spinlock支持长期获取不到锁时打印ERROR日志", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230525000793.007.001", "label": "AR Node.spinlock支持长期获取不到锁时打印ERROR日志", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230525000793.008", "label": "SR Node.提供内存分组视图查询能力", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "SR.IR20230525000793.009", "label": "SR Node.纵向隔离打开配置", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "SR.IR20230525000793.010", "label": "SR Node.多索引表内存极限场景事务回滚逃生通道优化", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230525000793.010.001", "label": "AR Node.多索引表内存极限场景事务回滚逃生通道优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230525000793.011", "label": "SR Node.ART索引支持多版本", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230525000793.011.001", "label": "AR Node.ART排序索引、LPM索引适配多版本", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230525000793.012", "label": "SR Node.支持在异步回调中调用异步接口", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230525000793.012.001", "label": "AR Node.支持在异步回调中调用异步接口", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230525000793.013", "label": "SR Node.设备侧支持工具客户端日志打印", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230525000793.013.001", "label": "AR Node.设备侧支持工具客户端日志打印", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230525000793.014", "label": "SR Node.老订阅推送通道支持动态可配置", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.85d88f21.004", "label": "AR Node.老订阅推送通道支持动态可配置", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}]}, {"id": "IR20230602001207", "label": "IR.GMDB V5支持ARM32平台（性能、内存）", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "SR.IR20230130003664.002", "label": "SR Node.GMDB V5线程池模式（单线程）下性能持平V3", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230130003664.002.001", "label": "AR Node.GMDBV5支持单线程模式", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230130003664.002.002", "label": "AR Node.GMDBV5单线程模式性能持平V3", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230602001207.002", "label": "SR Node.GMDB V5支持ARM32平台性能达标", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230602001207.002.001", "label": "AR Node.GMDB V5支持ARM32平台性能达标", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230602001207.003", "label": "SR Node.GMDB V5支持ARM32平台功能达标", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230602001207.003.001", "label": "AR Node.GMDB V5支持ARM32平台功能达标", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}]}, {"id": "IR20230602001229", "label": "IR.【性能】AC微观（全量）", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "SR.IR20230130003958.008", "label": "SR Node.基础负载Benchmark适配AC设备", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230130003958.008.001", "label": "AR Node.基础负载Benchmark适配AC设备", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230602001229.001", "label": "SR Node.IF表主键读类性能指标不低于V3", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "SR.IR20230602001229.002", "label": "SR Node.读不到表项场景下主键读性能指标不低于V3", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230602001229.002.001", "label": "AR Node.降低聚簇容器BloomFilter假阳性概率", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230602001229.004", "label": "SR Node.if表单写/批写Replace性能不低于V3", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230602001229.004.001", "label": "AR Node.通信开销优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230602001229.005", "label": "SR Node.对账异步老化时长不超过V3", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230602001229.005.001", "label": "AR Node.执行层基于聚簇容器删除老化plan生成流程", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230602001229.005.002", "label": "AR Node.聚簇容器提供scan和delete合一接口", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230602001229.007", "label": "SR Node.新增性能看护门禁用例", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "SR.IR20230602001229.008", "label": "SR Node.性能门禁用例支持指令级看护", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "SR.IR20230602001229.009", "label": "SR Node.摸底新增性能指标数据", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "SR.IR20230602001229.011", "label": "SR Node.IF表非结构化直连读全量字段指标不低于V3", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230602001229.011.001", "label": "AR Node.客户端新增获取runningbuf和nullInfo信息的接口", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230602001229.011.002", "label": "AR Node.DataModel新增获取runningbuf的接口", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230602001229.011.003", "label": "AR Node.MemCtx适配HPE逻辑地址快速转换", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230602001229.011.004", "label": "AR Node.MemCtx重置流程优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230602001229.011.005", "label": "AR Node.客户端配置项优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230602001229.011.006", "label": "AR Node.DataModel反序列化在字段全量设置情况下避免逐一设置nullinfo", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230602001229.012", "label": "SR Node.聚簇容器逻辑地址、HashCluster索引可维护性增强", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230602001229.012.001", "label": "AR Node.聚簇容器逻辑地址重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230602001229.012.002", "label": "AR Node.聚簇容器更新操作和HashCluster索引操作合并重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230602001229.013", "label": "SR Node.复杂表单写Replace性能不低于V3", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230602001229.013.001", "label": "AR Node.表名：cib_nac_ext_data；指标名：异步单写 Replace（新增写，缓存object），不低于V3", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230602001229.013.002", "label": "AR Node.表名：cib_nac_ext_data；指标名：异步单写 Replace（覆盖写，缓存object，随机key，修改2非索引字段），不低于V3", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230602001229.013.003", "label": "AR Node.表名：trunk_member；指标名：同步单写 Replace（新增写，不缓存object,128个子节点），不低于V3", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230602001229.014", "label": "SR Node.cib_nac_ext_data复杂表主键读性能不低于V3", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230602001229.014.001", "label": "AR Node.表名：cib_nac_ext_data；指标名：主键读（随机key，不缓存object），不低于V3", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230602001229.015", "label": "SR Node.ip4forward表单写/批写Update性能不低于V3", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230602001229.015.001", "label": "AR Node.执行层基于聚簇容器删除update plan生成流程", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230602001229.015.002", "label": "AR Node.聚簇容器提供lookup和update合一接口", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230602001229.016", "label": "SR Node.ip4forward表单写/批写Delete性能不低于V3", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230602001229.016.001", "label": "AR Node.执行层复用delete流程中读取数据的内存", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230602001229.016.002", "label": "AR Node.执行层基于聚簇容器删除delete plan生成流程", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230602001229.026", "label": "SR Node.DB提供获取原始recordBuf能力", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230602001229.026.001", "label": "AR Node.客户端支持提供获取原始recordBuf能力", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230602001229.026.002", "label": "AR Node.DM支持提供获取原始recordBuf能力", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230602001229.027", "label": "SR Node.队列订阅推送方案支持多订阅外带模式", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230602001229.027.001", "label": "AR Node.客户端实现过滤功能", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230602001229.027.002", "label": "AR Node.服务端批写的时候不再过滤分离通道", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230602001229.028", "label": "SR Node.复杂表单写避免反序列化开销", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230602001229.013.004", "label": "AR Node.执行层支持对复杂表不反序列化流程优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230602001229.013.005", "label": "AR Node.client端新增非结构化写对member<PERSON>ey校验（单写replace和insert）", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230602001229.013.006", "label": "AR Node.DM提供根据vertexBuf校验主键字段非空", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230602001229.029", "label": "SR Node.批写update支持单事务处理", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230602001229.029.001", "label": "AR Node.执行层支持单事务处理批写update", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}]}, {"id": "IR20230602001234", "label": "IR.【可信-DFLC】503.1.0 版本软件组件生命周期匹配产品生命周期", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "SR.IR20230602001234.001", "label": "SR Node.开源软件版本配套火车头版本", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "IR20230602001641", "label": "IR.503.1.0版本V3兼容性需求协同", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "SR.IR20230131027242.002", "label": "SR Node.组件XSAN工程支持通过环境变量控制XSAN开关", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230131027242.002.002", "label": "AR Node.支持通过环境变量控制XSAN开关", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230525000793.001", "label": "SR Node.状态合并订阅使用方式兼容V3", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230525000793.001.001", "label": "AR Node.在gmconfig中配置表是否支持状态合并订阅", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230525000793.002", "label": "SR Node.tablespace可配置易用优化", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230525000793.002.001", "label": "AR Node.QE模块支持tableSpace可配置易用优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230525000793.002.002", "label": "AR Node.SE支持tablespace可配置易用优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230525000793.002.003", "label": "AR Node.EE模块支持tablespace可配置易用优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230525000793.003", "label": "SR Node.DB兼容直接删除资源索引表", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230525000793.003.001", "label": "AR Node.EE支持直接删除资源索引表", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230602001641.003", "label": "SR Node.GMDBV5的gmsysview record命令子命令支持的数据类型补齐", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230602001641.003.001", "label": "AR Node.GMDBV5的gmsysview record命令子命令支持的数据类型补齐", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230607001924.001", "label": "SR Node.GMDBV5发布配置文件，标识一键诊断命令行范围", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230607001924.001.001", "label": "AR Node.GMDB V5视图新增一键诊断视图，可以一键采集关键视图信息", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230602001641.005", "label": "SR Node.【小型化】内存占用预分析工具", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "SR.IR20230602001641.006", "label": "SR Node.GMDBv5的gmsysview show 子命令兼容V3实现", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230602001641.006.001", "label": "AR Node.GMDBv5的gmsysview show 子命令兼容V3实现", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}]}, {"id": "IR20230605001912", "label": "IR.GMDB V5支持组件化", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "SR.IR20230605001912.002", "label": "SR Node.组件化构建", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230605001912.002.001", "label": "AR Node.组件化编译", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.002.002", "label": "AR Node.组件化加载", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.002.003", "label": "AR Node.组件化发布", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230605001912.003", "label": "SR Node.TRM容器组件化", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230605001912.003.001", "label": "AR Node.聚簇容器组件化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.003.002", "label": "AR Node.索引组件化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.003.003", "label": "AR Node.respool组件化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.003.004", "label": "AR Node.fixedHeap组件化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230605001912.004", "label": "SR Node.memdata重构和组件化", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230605001912.004.001", "label": "AR Node.memdata重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.004.002", "label": "AR Node.memdata组件化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230605001912.005", "label": "SR Node.runtime组件化", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230605001912.005.001", "label": "AR Node.runtime组件化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230605001912.006", "label": "SR Node.多态存储", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230605001912.006.001", "label": "AR Node.聚簇容器page mgr适配", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.006.002", "label": "AR Node.index page mgr适配", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.006.003", "label": "AR Node.heap page mgr适配", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.006.004", "label": "AR Node.undo page mgr适配", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.006.005", "label": "AR Node.fixedheap page mgr适配", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.006.006", "label": "AR Node.多态存储框架", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.006.007", "label": "AR Node.art索引多态存储适配", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230605001912.007", "label": "SR Node.架构解耦重构", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230605001912.007.001", "label": "AR Node.adapter解耦重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.007.002", "label": "AR Node.client解耦重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.007.003", "label": "AR Node.compiler(parser/analyzer)解耦重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.007.004", "label": "AR Node.compiler(optimization)解耦重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.007.005", "label": "AR Node.datamodel解耦重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.007.006", "label": "AR Node.executor解耦重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.007.007", "label": "AR Node.service解耦重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.007.008", "label": "AR Node.heap解耦重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.007.009", "label": "AR Node.fsm解耦重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.007.010", "label": "AR Node.聚簇容器解耦重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.007.011", "label": "AR Node.index解耦重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.007.012", "label": "AR Node.fixedHeap解耦重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.007.013", "label": "AR Node.runtime解耦重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.007.014", "label": "AR Node.pageMgr解耦重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.007.015", "label": "AR Node.space解耦重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.007.016", "label": "AR Node.common解耦重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230605001912.008", "label": "SR Node.Datalog 组件化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "SR.IR20230605001912.009", "label": "SR Node.Datalog 组件化", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230605001912.009.001", "label": "AR Node.构建Compiler 组件化框架，实现可插拔的datalog compiler", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.009.002", "label": "AR Node.构建service组件化框架，实现可插拔的datalog service", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.009.003", "label": "AR Node.Label AM 支持融合引擎的数据操作能力", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.009.004", "label": "AR Node.创建融合引擎组件 libgmfusion.so (planexec+optimization)", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230605001912.010", "label": "SR Node.组件化白盒安全检视", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230605001912.010.001", "label": "AR Node.组件化白盒安全检视", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}]}, {"id": "IR20230606003145", "label": "IR.【小型化】server空载内存占用指标", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "SR.IR20230606003145.002", "label": "SR Node.FSM内存优化", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230606003145.002.001", "label": "AR Node.FSM内存优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230606003145.003", "label": "SR Node.动态内存支持关闭二次管理算法", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230606003145.003.001", "label": "AR Node.MEM - 动态内存支持关闭二次管理", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230606003145.003.002", "label": "AR Node.Client - 客户端支持关闭内存二次管理", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230606003145.004", "label": "SR Node.datalog静态内存优化", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230606003145.004.002", "label": "AR Node.执行算子内存优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230606003145.004.003", "label": "AR Node.投影表达式内存优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230606003145.004.004", "label": "AR Node.DtlPlanList内存优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230606003145.004.005", "label": "AR Node.Planner结构体字段合并/删除", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230606003145.005", "label": "SR Node.支持pageSize下限调低", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230606003145.005.001", "label": "AR Node.支持pageSize下限调低", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230606003145.006", "label": "SR Node.chainHash能力补齐", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230606003145.006.001", "label": "AR Node.chainHash能力补齐。", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230606003145.007", "label": "SR Node.适配ehlibc malloc_trim能力", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230606003145.007.001", "label": "AR Node.MEM - 适配ehlibc malloc_trim能力", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230606003145.008", "label": "SR Node.支持查看表的元数据和数据内存开销", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230606003145.008.001", "label": "AR Node.支持查看表的元数据和数据内存开销", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230606003145.009", "label": "SR Node.cceh主键字典页slot数组支持动态扩展", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230606003145.009.001", "label": "AR Node.cceh主键字典页slot数组支持动态扩展", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230606003145.010", "label": "SR Node.heap&fixedHeap 支持延迟创建", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230606003145.010.003", "label": "AR Node.顶点表支持延迟初始化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230606003145.010.004", "label": "AR Node.边表支持延迟初始化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}]}, {"id": "IR20230607001912", "label": "IR.【小型化】DB启动时间优化", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "SR.IR20230607001912.001", "label": "SR Node.支持OMU启动时间优化", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230607001912.001.001", "label": "AR Node.客户端支持批量缓存元数据", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230607001912.001.002", "label": "AR Node.客户端支持批量清理元数据缓存", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230607001912.001.003", "label": "AR Node.QE支持解析批量获取元素据缓存", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230607001912.001.004", "label": "AR Node.EE支持批量获取元数据缓存", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230607001912.002", "label": "SR Node.schema_loader阶段操作放到server侧执行", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230607001912.002.001", "label": "AR Node.schema_loader放到server侧执行", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230607001912.003", "label": "SR Node.DB server侧支持数据导入", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230607001912.003.001", "label": "AR Node.DB server侧支持数据导入", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230607001912.004", "label": "SR Node.导表导权限支持文件合并", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230607001912.004.001", "label": "AR Node.gmimport - 导表支持文件合并", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}]}, {"id": "IR20230619000538", "label": "IR.GMDB V5适配HPE 2.0 UM模式", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "SR.IR20230619000538.001", "label": "SR Node.DB适配 HPE 2.0 UM模式", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230619000538.001.001", "label": "AR Node.DB适配 HPE 2.0 UM模式", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230619000538.002", "label": "SR Node.共进程部署客户端安全策略（鉴权）", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230619000538.002.001", "label": "AR Node.EE支持共进程部署客户端安全策略", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230619000538.004", "label": "SR Node.线程池模式增强", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230619000538.004.001", "label": "AR Node.线程池模式增强", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230619000538.005", "label": "SR Node.适配aarch64/arm32下的HPE UM模式编译工具链并新增rpm交付件", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230619000538.005.001", "label": "AR Node.适配HPE UM模式编译工具链", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}]}, {"id": "IR20230619001434", "label": "IR.【性能】LPM4/LPM6性能优化", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "SR.IR20230619001434.001", "label": "SR Node.LPM4/LPM6性能优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "IR20230713002435", "label": "IR.DB支持用户态CFI", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "SR.IR20230713002435.001", "label": "SR Node.DB支持用户态CFI", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "IR20230713002526", "label": "IR.【小型化】客户端空载内存", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "SR.IREQ02872173.001", "label": "SR Node.Yang客户端内存占用不超过600k", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IREQ02872173.001.001", "label": "AR Node.SE支持小型化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IREQ02872173.001.002", "label": "AR Node.EE支持小型化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IREQ02872173.001.003", "label": "AR Node.QE支持小型化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IREQ02872173.001.004", "label": "AR Node.DM支持小型化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IREQ02872173.001.005", "label": "AR Node.Client支持小型化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IREQ02872173.001.006", "label": "AR Node.Runtime支持yang客户端内存小型化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230713002526.001", "label": "SR Node.客户端空载内存", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230713002526.001.001", "label": "AR Node.客户端共享内存优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230713002526.001.002", "label": "AR Node.客户端so数据段优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}]}, {"id": "IR20230717000482", "label": "IR.GMDB 503.1 质量加固与重构", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "SR.IR20230601002823.004", "label": "SR Node.树模型DDL支持YANG语义校验定义", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230601002823.004.005", "label": "AR Node.QE支持YANG语义语法定义的解析", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230601002823.004.006", "label": "AR Node.QE支持YANG语义语法定义的校验", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230601002823.004.007", "label": "AR Node.DM支持YANG语义定义和操作", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230601002823.005", "label": "SR Node.树模型DDL支持XPATH解析", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230601002823.005.001", "label": "AR Node.Catalog支持YANG语义校验元数据缓存", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230601002823.005.002", "label": "AR Node.DM支持YANG语义定义Formula解析", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230601002823.005.003", "label": "AR Node.Client支持YANG语义校验模型解析接口定义和实现", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230601002823.006", "label": "SR Node.树模型DDL支持XPATH合法性校验", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230601002823.006.001", "label": "AR Node.DM支持when、must等规则定义的合法性校验执行", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230601002823.006.002", "label": "AR Node.EE支持when、must等规则定义的合法性校验执行", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230601002823.007", "label": "SR Node.树的DML支持Choice/Case路径提取操作", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230601002823.007.001", "label": "AR Node.客户端支持Choice/Case路径提取操作", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230601002823.008", "label": "SR Node.gmsysview支持YANG表定义视图查询", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230601002823.008.001", "label": "AR Node.gmsysview提供视图查询YANG的表定义视图：", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230601002823.010", "label": "SR Node.树模型DDL支持XPATH解析与校验增强", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230601002823.010.001", "label": "AR Node.Service支持校验接口执行流和日志", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230601002823.010.002", "label": "AR Node.EE支持XPATH函数的解析和校验", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230601002823.010.003", "label": "AR Node.Client支持YANG表定义合法性校验接口", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230601002823.010.004", "label": "AR Node.DM支持XPATH函数", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230717000482.001", "label": "SR Node.【小型化】GMDBv5领域initrd_ext分区包大小优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "SR.IR20230717000482.003", "label": "SR Node.【小型化】内存膨胀率看护", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "SR.IR20230717000482.004", "label": "SR Node.【性能】AP性能基线看护", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "SR.IR20230717000482.006", "label": "SR Node.存储引擎升降级流程质量加固与重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "SR.IR20230717000482.007", "label": "SR Node.客户端建连时序质量加固", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230717000482.007.001", "label": "AR Node.MEM - 共享内存ctxId复用逻辑优化", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230717000482.008", "label": "SR Node.内存定位定界质量加固", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230717000482.008.001", "label": "AR Node.共享内存增加日志定位信息", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230717000482.009", "label": "SR Node.降级、老化和缩容并发加固", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230717000482.009.001", "label": "AR Node.降级、老化和缩容并发加固", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230717000482.010", "label": "SR Node.聚簇容器表升降级重构", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230717000482.010.001", "label": "AR Node.聚簇容器支持multi-fixed-size", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230717000482.010.002", "label": "AR Node.聚簇容器支持降级合并", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230717000482.010.003", "label": "AR Node.[DM]聚簇容器约束变更、升降级约束变更", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230717000482.010.004", "label": "AR Node.【EE】解析逻辑、执行逻辑、gmasst工具适配新聚簇容器约束", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230601002823.026", "label": "SR Node.YANG场景DFX增强", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230601002823.026.001", "label": "AR Node.客户端YangDFX增强", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230601002823.026.002", "label": "AR Node.runtimeYangDFX增强", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230601002823.026.003", "label": "AR Node.执行层YangDFX增强", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230717000482.011", "label": "SR Node.QE/EE Prepare阶段并发重构", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230717000482.011.001", "label": "AR Node.QE/EE Prepare阶段并发重构", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230717000482.012", "label": "SR Node.DM与元数据解耦", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230717000482.012.001", "label": "AR Node.DM与元数据解耦", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230717000482.012.002", "label": "AR Node.Catalog统一封装kv/vertex/edge三种表的元数据结构体", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230717000482.012.003", "label": "AR Node.客户端适配DM与元数据解耦", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230717000482.012.004", "label": "AR Node.Yang适配DM与元数据解耦", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}, {"id": "SR.IR20230717000482.013", "label": "SR Node.DB适配kv/vertex/edge三种元数据封装", "attrs": {"version": "GMDB 503.1.0"}, "children": [{"id": "AR.SR.IR20230717000482.013.001", "label": "AR Node.Client适配kv/vertex/edge三种元数据封装", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230717000482.013.002", "label": "AR Node.Compiler适配kv/vertex/edge三种元数据封装", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230717000482.013.003", "label": "AR Node.catalog适配kv/vertex/edge三种元数据封装", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230717000482.013.004", "label": "AR Node.DM-Yang适配kv/vertex/edge三种元数据封装", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230717000482.013.005", "label": "AR Node.EE适配kv/vertex/edge三种元数据封装", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230717000482.013.006", "label": "AR Node.Index适配kv/vertex/edge三种元数据封装", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230717000482.013.007", "label": "AR Node.SE适配kv/vertex/edge三种元数据封装", "attrs": {"version": "GMDB 503.1.0"}, "children": []}]}]}, {"id": "AR.SR.IR20230605001912.008.001", "label": "AR Node.构建 FastPath 能力的 Executor 组件，实现该组件可插拔", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.008.002", "label": "AR Node.构建 FastPath 能力的 Compiler 组件，实现该组件可插拔", "attrs": {"version": "GMDB 503.1.0"}, "children": []}, {"id": "AR.SR.IR20230605001912.008.003", "label": "AR Node.构建 FastPath 能力的 Services 组件，实现该组件可插拔", "attrs": {"version": "GMDB 503.1.0"}, "children": []}], "sync_type": "requirement"}