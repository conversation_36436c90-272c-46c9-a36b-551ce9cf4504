{"id": "因子树", "label": "因子树", "attrs": {}, "children": [{"id": "因子树", "label": "因子树", "attrs": {}, "children": [{"id": "功能", "label": "功能", "attrs": {}, "children": [{"id": "基础", "label": "基础", "attrs": {}, "children": [{"id": "南北向激励（添加更新删除）", "label": "南北向激励（添加更新删除）", "attrs": {"level": 0}, "children": [], "type": 2}, {"id": "东西向激励", "label": "东西向激励", "attrs": {"level": 0}, "children": [], "type": 2}, {"id": "同一命令激励时序", "label": "同一命令激励时序", "attrs": {"level": 0}, "children": [], "type": 2}, {"id": "不同命令激励时序", "label": "不同命令激励时序", "attrs": {"level": 0}, "children": [], "type": 2}, {"id": "命令和东西向时序", "label": "命令和东西向时序", "attrs": {}, "children": []}, {"id": "外部接口异常返回", "label": "外部接口异常返回", "attrs": {"level": 0}, "children": [], "type": 2}, {"id": "重复下发过滤", "label": "重复下发过滤", "attrs": {"level": 0}, "children": [], "type": 2}, {"id": "业务配置切换", "label": "业务配置切换", "attrs": {"level": 0}, "children": [], "type": 2}]}, {"id": "硬件差异", "label": "硬件差异", "attrs": {}, "children": [{"id": "框表项一致性", "label": "框表项一致性", "attrs": {"level": 0}, "children": [], "type": 2}, {"id": "多芯片", "label": "多芯片", "attrs": {"level": 0}, "children": [], "type": 2}, {"id": "设备形态差异", "label": "设备形态差异", "attrs": {"level": 0}, "children": [], "type": 2}]}, {"id": "组合", "label": "组合", "attrs": {}, "children": [{"id": "与LAG口组合", "label": "与LAG口组合", "attrs": {"level": 1}, "children": [], "type": 2}, {"id": "与VS组合", "label": "与VS组合", "attrs": {"level": 1}, "children": [], "type": 2}, {"id": "与M-LAG组合", "label": "与M-LAG组合", "attrs": {"level": 1}, "children": [], "type": 2}, {"id": "与VLAN的组合不同接入类型", "label": "与VLAN的组合不同接入类型", "attrs": {"level": 1}, "children": [], "type": 2}, {"id": "与ECMP的组合", "label": "与ECMP的组合", "attrs": {"level": 1}, "children": [], "type": 2}]}]}, {"id": "可靠性", "label": "可靠性", "attrs": {}, "children": [{"id": "硬件故障", "label": "硬件故障", "attrs": {}, "children": [{"id": "主备倒换", "label": "主备倒换", "attrs": {"level": 2}, "children": [], "type": 2}, {"id": "SMB进程重启", "label": "SMB进程重启", "attrs": {"level": 2}, "children": [], "type": 2}, {"id": "LPU进程重启", "label": "LPU进程重启", "attrs": {"level": 2}, "children": [], "type": 2}, {"id": "接口UP/Down通知", "label": "接口UP/Down通知", "attrs": {"level": 2}, "children": [], "type": 2}, {"id": "LAG成员口UP/Down通知", "label": "LAG成员口UP/Down通知", "attrs": {"level": 2}, "children": [], "type": 2}, {"id": "单板拔插", "label": "单板拔插", "attrs": {}, "children": []}]}, {"id": "资源超限", "label": "资源超限", "attrs": {}, "children": [{"id": "资源申请满时超限告警", "label": "资源申请满时超限告警", "attrs": {"level": 3}, "children": [], "type": 2}, {"id": "资源全部申请成功时解除超限告警", "label": "资源全部申请成功时解除超限告警", "attrs": {"level": 3}, "children": [], "type": 2}, {"id": "资源释放时超限恢复", "label": "资源释放时超限恢复", "attrs": {"level": 3}, "children": [], "type": 2}]}, {"id": "平滑老化", "label": "平滑老化", "attrs": {}, "children": [{"id": "平滑后版本号的检查和老化处理", "label": "平滑后版本号的检查和老化处理", "attrs": {"level": 3}, "children": [], "type": 2}]}, {"id": "芯片异常", "label": "芯片异常", "attrs": {}, "children": [{"id": "下发芯片返回错误", "label": "下发芯片返回错误", "attrs": {}, "children": []}]}]}]}, {"id": "e9f31283e7c44a288451e772ca2a6528", "label": "待整理", "attrs": {}, "children": [{"id": "cuuo3rpkf540", "label": "xpath表达式语法", "attrs": {}, "type": "2", "children": []}, {"id": "cuuoceenj740", "label": "xpath表达式和模型定义合法性", "attrs": {}, "type": "2", "children": []}, {"id": "cv6pgy4p39k0", "label": "分支主题", "attrs": {}, "type": "2", "children": []}, {"id": "cv6pef2j7js0", "label": "容器过滤", "attrs": {}, "type": "2", "children": []}, {"id": "cv6pfup9leo0", "label": "叶子过滤", "attrs": {}, "type": "2", "children": []}, {"id": "cv6pfxk9n1s0", "label": "内容过滤", "attrs": {}, "type": "2", "children": []}, {"id": "cv6pg37c3rk0", "label": "三种过滤组合嵌套", "attrs": {}, "type": "2", "children": []}, {"id": "cv6pfup9leo0", "label": "选择过滤", "attrs": {}, "type": "2", "children": []}, {"id": "cv6pef2j7js0", "label": "包含节点过滤", "attrs": {}, "type": "2", "children": []}, {"id": "cv6pfup9leo0", "label": "选择节点过滤", "attrs": {}, "type": "2", "children": []}, {"id": "cv6pfxk9n1s0", "label": "匹配过滤", "attrs": {}, "type": "2", "children": []}]}, {"id": "a5ee0733eb479187", "label": "功能交互分析", "children": [{"label": "组件公共能力", "id": "8d2cce4674b5f02a", "children": [{"label": "启动", "id": "4e231c9cfa770bd4", "children": []}, {"label": "重启", "id": "cf5d85e32fef6abd", "children": [{"id": "MCF_4", "label": "重启后业务测试", "type": 2, "attrs": {"description": "重启后业务测试", "steps": [{"step": "1、业务功能正常，进行进程重启，有预期结果1；", "expect": "1、重启后业务恢复正常，配置无丢失，同等业务下CPU和内存比重启前不增加；"}]}}]}, {"label": "升级", "id": "61e1fb8c914cc4b9", "children": [{"id": "MCF_6", "label": "升级后业务测试", "type": 2, "attrs": {"description": "升级后业务测试", "steps": [{"step": "1、业务正常，进行 进程、组件或容器的升级，有预期结果1；", "expect": "1、升级后业务功能正常、CPU和内存较升级前不升高（5%以内）；"}]}}, {"id": "MCF_7", "label": "升级端到端耗时（典配）", "type": 2, "attrs": {"description": "升级端到端耗时（典配）", "steps": [{"step": "1、典型配置下业务正常，进行 进程、组件或容器的升级，有预期结果1；", "expect": "1、升级后业务功能正常、CPU和内存较升级前不升高（5%以内），升级时间在规格以内；"}]}}]}, {"label": "平滑对账", "id": "e726f39260216982", "children": [{"id": "MCF_9", "label": "平滑", "type": 2, "attrs": {"description": "平滑", "steps": [{"step": "1、被测组件配置基础业务（或功能）达到组件规格的80%，触发数据消费者请求平滑，有预期结果1；", "expect": "1、数据平滑结果正确，不会产生CPU冲高；"}]}}, {"id": "MCF_3", "label": "老化", "type": 2, "attrs": {"description": "老化", "steps": [{"step": "1、通过模拟工具触发数据消费者请求平滑，测试全表平滑和按照path平滑，有预期结果1；", "expect": "1、数据平滑结果正确，不会产生CPU冲高；"}]}}, {"id": "MCF_10", "label": "对账", "type": 2, "attrs": {"description": "对账", "steps": [{"step": "1、数据消费者请求对账，测试全表和按照path对账，有预期结果1；", "expect": "1、数据对账结果正确，不会产生CPU冲高；"}]}}, {"id": "MCF_11", "label": "平滑对账并发", "type": 2, "attrs": {"description": "平滑对账并发", "steps": [{"step": "1、数据消费者请求对账，在对账过程中，通过工具注入平滑事件，有预期结果1；", "expect": "1、平滑的优先级高于对账，会中断对账开始平滑，数据平滑正确，不会产生CPU冲高，；"}]}}]}, {"label": "能力定制", "id": "04c8a9859018912c", "children": [{"id": "MCF_12", "label": "能力集生效测试", "type": 2, "attrs": {"description": "能力集生效测试", "steps": [{"step": "1、加载软件包，查看业务功能和规格，有预期结果1；", "expect": "1、业务和规格生效情况和网站上能力集数据一致"}]}}, {"id": "MCF_13", "label": "能力集变更测试", "type": 2, "attrs": {"description": "能力集变更测试", "steps": [{"step": "1、在网站上修改能力集，并重新加载，有预期结果1；", "expect": "1、业务和规格生效情况和网站上能力集数据一致"}]}}]}, {"label": "HA", "id": "5d7cf4971707adf6", "children": []}, {"label": "多实例", "id": "17390d26cd535259", "children": []}, {"label": "分布式", "id": "e91a6bff72168511", "children": []}, {"label": "补丁", "id": "14d4be4da55e8c9d", "children": [{"id": "MCF_16", "label": "补丁加载与卸载", "type": 2, "attrs": {"description": "补丁加载与卸载", "steps": [{"step": "1、测试业务正常，触发补丁加载和卸载动作，有预期结果1；", "expect": "1、补丁可以正常加载和卸载，加载收补丁内容生效，根据补丁合入内容测试相关点，不能导致已有业务异常；补丁加载后CPU和内存无异常、升高不能超过5%，补丁卸载后资源无残留；"}]}}]}]}, {"label": "组件接口", "id": "03fe36c5418fb7dc", "children": [{"label": "通信接口", "id": "e56f99bbde3d9c98", "children": [{"id": "MCF_19", "label": "接收端缓存测试", "type": 2, "attrs": {"description": "接收端缓存测试", "steps": [{"step": "1、故障注入工具模拟 模拟一个发送端朝接收端发限速的流，一个发送burst流，有预期结果1；", "expect": "1、能测试出接收端缓存的大小，或者有命令行或其他方式查看到缓存的内容；"}]}}, {"id": "MCF_20", "label": "接收端通道反压机制测试", "type": 2, "attrs": {"description": "接收端通道反压机制测试", "steps": [{"step": "1、通过发送端发送超过接收端能力的流量，有预期结果1；", "expect": "1、在发送端能收到接收端发过来的反压帧，进行流量调整，直到自动调整到在接收端流量范围内不丢包；"}]}}]}, {"label": "消息接口", "id": "250a6c463fce7ffe", "children": [{"label": "MESH", "id": "cec69c472c3be1d9", "children": [{"id": "MCF_23", "label": "客户端-消息功能测试", "type": 2, "attrs": {"description": "客户端-消息功能测试", "steps": [{"step": "1、测试Mesh客户端消息功能，有预期结果1；", "expect": "1、根据板内共享内存通信、板间socket通信、板间HPP通信等方式完成消息的传递；"}]}}, {"id": "MCF_24", "label": "服务端-消息功能测试", "type": 2, "attrs": {"description": "服务端-消息功能测试", "steps": [{"step": "1、测试Mesh服务端消息功能，有预期结果1；", "expect": "1、可以完成端口分配、注销、鉴权和控制功能，针对Mesh服务器端要单独设计用例（涉及功能点比较多）"}]}}]}, {"label": "PUB/SUB", "id": "d512e0e74ac6ee22", "children": [{"id": "MCF_26", "label": "PUB消息-触发业务逻辑测试", "type": 2, "attrs": {"description": "PUB消息-触发业务逻辑测试", "steps": [{"step": "1、测试PUB消息，有预期结果1；", "expect": "1、业务功能正常；"}]}}, {"id": "MCF_27", "label": "SUB消息-接收处理逻辑测试", "type": 2, "attrs": {"description": "SUB消息-接收处理逻辑测试", "steps": [{"step": "1、测试SUB消息，有预期结果1；", "expect": "1、业务功能正常；"}]}}]}, {"label": "CROS(RPC)", "id": "b0b573de3f9e3e79", "children": [{"id": "MCF_29", "label": "服务端-功能测试", "type": 2, "attrs": {"description": "服务端-功能测试", "steps": [{"step": "1、测试CROS服务端消息，有预期结果1；", "expect": "1、业务功能正常；"}]}}, {"id": "MCF_30", "label": "客户端-消息功能测试", "type": 2, "attrs": {"description": "客户端-消息功能测试", "steps": [{"step": "1、测试CROS客户端消息，有预期结果1；", "expect": "1、业务功能正常；"}]}}]}]}, {"label": "函数接口", "id": "4a558b60d06ac88e", "children": [{"id": "MCF_32", "label": "C-API业务逻辑测试", "type": 2, "attrs": {"description": "C-API业务逻辑测试", "steps": [{"step": "1、通过C-API触发业务逻辑测试", "expect": "1、业务功能正常；"}]}}]}, {"label": "数据接口", "id": "eb404e3ba237619e", "children": [{"label": "命令行", "id": "a95cbe2c7294aa12", "children": [{"id": "MCF_35", "label": "命令行配置生效测试", "type": 2, "attrs": {"description": "命令行配置生效测试", "steps": [{"step": "1、通过命令行下发配置，有预期结果1；", "expect": "1、命令行能正常下发，生成buildrun或DB表项，业务功能正常，没有内存泄漏；"}]}}, {"id": "MCF_34", "label": "命令行依赖测试测试", "type": 2, "attrs": {"description": "命令行依赖测试测试", "steps": [{"step": "1、命令B的前置命令是A,在A未配置的情况下执行B，有结果1", "expect": "1、命令行执行失败，并提示"}, {"step": "2 、命令B的前置命令是A,在A配置的情况下执行B，有结果2", "expect": "2、命令行能正常下发，生成buildrun或DB表项，业务功能正常，没有内存泄漏；"}]}}, {"id": "MCF_36", "label": "命令行查询业务正确性测试", "type": 2, "attrs": {"description": "命令行查询业务正确性测试", "steps": [{"step": "1、通过命令行查询，有预期结果1；", "expect": "1、命令行查询内容正确，查询内容和业务真实情况一致；"}]}}, {"id": "MCF_37", "label": "Buildrun生成测试", "type": 2, "attrs": {"description": "Buildrun生成测试", "steps": [{"step": "1、命令行配置后生成Buildrun，有预期结果1；", "expect": "1、<PERSON><PERSON><PERSON>正确，重启后配置恢复正常；"}]}}]}, {"label": "YANG", "id": "a0d1b24bee9ea478", "children": [{"id": "MCF_39", "label": "配置类-生效测试", "type": 2, "attrs": {"description": "配置类-生效测试", "steps": [{"step": "1、通过yang节点配置业务，有预期结果1；", "expect": "1、yang节点可以正常下发，业务功能生效，没有内存泄漏；"}]}}, {"id": "MCF_40", "label": "查询类-业务正确性测试", "type": 2, "attrs": {"description": "查询类-业务正确性测试", "steps": [{"step": "1、通过yang节点查询业务，有预期结果1；", "expect": "1、yang节点查询返回结果正确，和真实情况一致，没有内存泄漏；"}]}}, {"id": "MCF_41", "label": "Buildrun生成测试", "type": 2, "attrs": {"description": "Buildrun生成测试", "steps": [{"step": "1、yang节点配置后生成Buildrun，有预期结果1；", "expect": "1、<PERSON><PERSON><PERSON>正确，重启后配置恢复正常；"}]}}]}, {"label": "MIB", "id": "a12422ceda16ecd3", "children": [{"id": "MCF_43", "label": "配置类-生效测试", "type": 2, "attrs": {"description": "配置类-生效测试", "steps": [{"step": "1、通过MIB节点配置业务，有预期结果1；", "expect": "1、MIB节点可以正常下发，业务功能生效，没有内存泄漏；"}]}}, {"id": "MCF_44", "label": "查询类-业务正确性测试", "type": 2, "attrs": {"description": "查询类-业务正确性测试", "steps": [{"step": "1、通过MIB节点查询业务，有预期结果1；", "expect": "1、MIB节点查询返回结果正确，和真实情况一致，没有内存泄漏；"}]}}, {"id": "MCF_45", "label": "Buildrun生成测试", "type": 2, "attrs": {"description": "Buildrun生成测试", "steps": [{"step": "1、MIB节点配置后生成Buildrun，有预期结果1；", "expect": "1、<PERSON><PERSON><PERSON>正确，重启后配置恢复正常；"}]}}]}, {"label": "配置文件", "id": "fc1b7df04b2c6302", "children": [{"id": "MCF_47", "label": "功能开关测试", "type": 2, "attrs": {"description": "功能开关测试", "steps": [{"step": "1、测试配置文件中功能开关，既配置文件中配置该业务功能使能，或去使能，查看业务生效情况，有预期结果1；", "expect": "1、业务生效情况和配置文件一致，使能业务生效，去使能业务功能关闭；"}]}}, {"id": "MCF_48", "label": "数值生效测试", "type": 2, "attrs": {"description": "数值生效测试", "steps": [{"step": "1、测试配置文件中数值生效测试，既配置文件中配置具体的数值，如命令行配置ACL 5或心跳时间等，查看业务生效情况，有预期结果1；", "expect": "1、业务生效情况和配置文件一致，修改数值，业务生效情况会随着新的 值变更；"}]}}]}]}]}, {"label": "组件功能", "id": "c541ecb5e6b3c194", "children": [{"label": "组件行为", "id": "f3bf61688ec909ed", "children": [{"label": "输入输出模型", "id": "8feb99669cc89a35", "children": [{"id": "MCF_51", "label": "表项资源管理-增查测试", "type": 2, "attrs": {"description": "增查测试", "steps": [{"step": "1、对表项进行增加操作，查看表项有预期结果1；", "expect": "1、表项可以查询，表项增加正确；"}]}}, {"id": "MCF_52", "label": "表项资源管理-改查测试", "type": 2, "attrs": {"description": "改查测试", "steps": [{"step": "1、对表项进行修改操作，查看表项有预期结果1；", "expect": "1、表项可以查询，表项修改正确；"}]}}, {"id": "MCF_53", "label": "表项资源管理-删查测试", "type": 2, "attrs": {"description": "删查测试", "steps": [{"step": "1、对表项进行删除操作，查看表项有预期结果1；", "expect": "1、表项可以查询，表项删除正确；"}]}}, {"id": "MCF_54", "label": "表项资源管理-重复增测试", "type": 2, "attrs": {"description": "重复增测试", "steps": [{"step": "1、对表项进行重复增加操作，查看表项有预期结果1；", "expect": "1、表项可以查询，表项没有变化；"}]}}, {"id": "MCF_55", "label": "表项资源管理-重复删试", "type": 2, "attrs": {"description": "重复删试", "steps": [{"step": "1、对表项进行重复删除操作，查看表项有预期结果1；", "expect": "1、表项可以查询，表项没有变化，没有内存泄漏；"}]}}]}, {"label": "流程图", "id": "e0499bf2b5ab220d", "children": [{"id": "MCF_59", "label": "路径遍历测试", "type": 2, "attrs": {"description": "路径遍历测试", "steps": [{"step": "1、按照流程图的测试设计方法（判定条件覆盖等）设计用例，遍历所有路径，有预期结果1；", "expect": "1、路径遍历覆盖，功能正常；"}]}}, {"id": "MCF_60", "label": "节点非正常返回测试", "type": 2, "attrs": {"description": "节点非正常返回测试", "steps": [{"step": "1、构造条件，使流程中间节点返回失败，观察处理流程，有预期结果1；", "expect": "1、正确处理下游节点的错误，记录日志、释放资源；"}]}}]}, {"label": "状态机", "id": "5ca3d06ecf5bec93", "children": [{"id": "MCF_61", "label": "状态遍历测试", "type": 2, "attrs": {"description": "状态遍历测试", "steps": [{"step": "1、按照状态机的测试设计方法（x-switch）设计用例，遍历所有状态，有预期结果1；", "expect": "1、根据覆盖深度选择x值，覆盖的状态机结果正常"}]}}]}]}]}, {"label": "组件基础环境", "id": "9aa97be409faf5fc", "children": [{"label": "转发服务", "id": "c8cf16284a032c07", "children": [{"id": "MCF_58", "label": "转发路径覆盖测试", "type": 2, "attrs": {"description": "转发路径覆盖测试", "steps": [{"step": "1、设计用例覆盖转发路径，有预期结果1；", "expect": "1、转发路径覆盖完成，转发路径带宽可以达到设计值，无丢包；"}]}}]}]}]}, {"id": "576411b2c69028b8", "label": "性能交互分析", "children": [{"label": "组件公共能力", "id": "8d2cce4674b5f02a", "children": [{"label": "启动", "id": "4e231c9cfa770bd4", "children": [{"id": "MCP_5", "label": "进程启动时间与资源消耗（空配）", "type": 2, "attrs": {"description": "进程启动时间与资源消耗（空配）", "steps": [{"step": "1、CTP拉起被测试对象容器，容器启动后，xx框架拉起xx组件进程，通过TestApp读取xx组件进程状态，组件可以正常被拉起", "expect": "1、进程拉起时间符合规格要求"}]}}, {"id": "MCP_6", "label": "进程启动时间与资源消耗（典配）", "type": 2, "attrs": {"description": "进程启动时间与资源消耗（典配）", "steps": [{"step": "1、CTP拉起被测试对象容器，容器启动后，xx框架拉起xx组件进程，通过TestApp读取xx组件进程状态，组件可以正常被拉起", "expect": "1、进程拉起时间符合规格要求"}]}}]}, {"label": "重启", "id": "cf5d85e32fef6abd", "children": [{"id": "MCP_8", "label": "重启动的启动时间（空配）", "type": 2, "attrs": {"description": "重启动的启动时间（空配）", "steps": [{"step": "1、业务流正常，重启组件、进程或容器，有预期结果1", "expect": "1、进程、组件或容器重启时间符合规格要求（可通过业务恢复情况测试启动时间）"}]}}, {"id": "MCP_9", "label": "重启动的启动时间（典配）", "type": 2, "attrs": {"description": "重启动的启动时间（典配）", "steps": [{"step": "1、业务流正常，重启组件、进程或容器，有预期结果1", "expect": "1、进程、组件或容器重启时间符合规格要求（可通过业务恢复情况测试启动时间）"}]}}]}, {"label": "升级", "id": "61e1fb8c914cc4b9", "children": [{"id": "MCP_11", "label": "升级端到端耗时（空配）", "type": 2, "attrs": {"description": "升级端到端耗时（空配）", "steps": [{"step": "1、按照版本升级路径，设置下次启动包和下次启动配置文件（可选），进行升级；"}, {"step": "2、组件启动后查询业务恢复情况和CPU和内存值，有预期结果1；", "expect": "1、升级时间符合规格要求，内存和CPU占用比升级前没有明显提升，不能超过5%；"}]}}, {"id": "MCP_12", "label": "升级端到端耗时（典配）", "type": 2, "attrs": {"description": "升级端到端耗时（典配）", "steps": [{"step": "1、按照版本升级路径，设置下次启动包和下次启动配置文件（可选），进行升级；"}, {"step": "2、组件启动后查询业务恢复情况和CPU和内存值，有预期结果1；", "expect": "1、升级时间符合规格要求，内存和CPU占用比升级前没有明显提升，不能超过5%；"}]}}]}, {"label": "平滑对账", "id": "e726f39260216982", "children": [{"id": "MCP_14", "label": "平滑表项数量测试（最小、最大）", "type": 2, "attrs": {"description": "平滑表项数量测试（最小、最大）", "steps": [{"step": "1、构造两个组件数据差异最小，触发数据平滑，检查平滑结果，有预期结果1；", "expect": "1、符合平滑要求的表项被正确同步；"}, {"step": "1、构造两个组件数据差异最大，触发数据平滑，检查平滑结果，有预期结果2；", "expect": "2、符合平滑要求的表项被正确同步；"}]}}, {"id": "MCP_15", "label": "平滑端到端时间测试（最短、最长）", "type": 2, "attrs": {"description": "平滑端到端时间测试（最短、最长）", "steps": [{"step": "1、构造两个组件数据差异最小，触发数据平滑，检查平滑完成时间；", "expect": "1、正确完成表项平滑，平滑时间符合规格要求；"}, {"step": "1、构造两个组件数据差异最大，触发数据平滑，检查平滑完成时间；", "expect": "2、正确完成表项平滑。平滑时间符合规格要求；"}]}}, {"id": "MCP_16", "label": "对账表项数量测试（最小、最大）", "type": 2, "attrs": {"description": "对账表项数量测试（最小、最大）", "steps": [{"step": "1、构造两个组件数据差异最小，触发对账，检查对账完成结果，有预期结果1；", "expect": "1、符合对账要求的表项被正确同步；"}, {"step": "1、构造两个组件数据差异最大，触发对账，检查对账完成结果，有预期结果2；", "expect": "2、符合对账要求的表项被正确同步；"}]}}, {"id": "MCP_17", "label": "对账端到端时间测试（最短、最长）", "type": 2, "attrs": {"description": "对账端到端时间测试（最短、最长）", "steps": [{"step": "1、构造两个组件数据差异最小，触发对账，检查对账完成时间；", "expect": "1、对账时间符合规格要求"}, {"step": "1、构造两个组件数据差异最大，触发对账，检查对账完成时间；", "expect": "2、对账同步时间符合规格要求"}]}}]}, {"label": "能力定制", "id": "04c8a9859018912c", "children": [{"id": "MCP_21", "label": "能力集-数值边界值测试", "type": 2, "attrs": {"description": "能力集-数值边界值测试", "steps": [{"step": "1、加载包含能力集的大包，对能力集边界数值进行覆盖；", "expect": "1、业务实现结果和能力集边界值一致；"}]}}, {"id": "MCP_22", "label": "能力集-开关值测试", "type": 2, "attrs": {"description": "能力集-开关值测试", "steps": [{"step": "1、加载包含能力集的大包，测试能力集中使能的业务，有预期结果1；", "expect": "1、业务实现结果和能力集业务开关一致，打开的功能生效，关闭的业务不生效；"}]}}]}, {"label": "多实例", "id": "17390d26cd535259", "children": []}, {"label": "分布式", "id": "e91a6bff72168511", "children": []}, {"label": "补丁", "id": "14d4be4da55e8c9d", "children": [{"id": "MCP_26", "label": "加载补丁时长（最多补丁）", "type": 2, "attrs": {"description": "加载补丁时长（最多补丁）", "steps": [{"step": "1、加载补丁（补丁包最大字节，可以认为合入问题最多），有预期结果1；", "expect": "1、补丁可以正常加载，补丁功能生效，加载时间符合规格要求；"}]}}, {"id": "MCP_27", "label": "卸载补丁时长", "type": 2, "attrs": {"description": "卸载补丁时长", "steps": [{"step": "1、卸载补丁（补丁包最大字节，可以认为合入问题最多），有预期结果1；", "expect": "1、补丁可以正常卸载，资源无残留，补丁卸载时间符合规格要求；"}]}}]}]}, {"label": "组件功能", "id": "c541ecb5e6b3c194", "children": [{"label": "组件指标", "id": "4896e9ab01c60b24", "children": [{"id": "MCP_31", "label": "空间性能-表项容量测试", "type": 2, "attrs": {"description": "表项容量测试", "steps": [{"step": "1、构造满规格xx表项配置，查看组件的xx表项大小、内存利用率、CPU利用率；", "expect": "1、表项大小，内存利用率和CPU利用率满足规格定义要求；"}, {"step": "2、构造满规格+1xx表项配置，查看组件的xx表项大小、内存利用率、CPU利用率；", "expect": "2、表项大小仍是规格规定的大小，内存利用率和CPU利用率满足规格定义要求；"}]}}, {"id": "MCP_32", "label": "空间性能-连接数测试", "type": 2, "attrs": {"description": "连接数测试", "steps": [{"step": "1、在测试模拟工具上创建连接数的进程，每进程创建x个线程，测试模拟工具调用所有的进程和线程，触发向组件发起建立连接，并观察内存利用率、CPU利用率，有预期结果1", "expect": "1、所有的连接都创建成功，内存利用率和CPU利用率满足规格定义要求；"}, {"step": "2、测试模拟工具向组件再建立一个连接，有预期结果2", "expect": "2、新连接建立失败，且返回相应的错误码；"}, {"step": "3、杀掉所有的创建进程，有预期结果3", "expect": "3、release object成功，无错误码返回，查看整个连接数为初始状态；"}]}}, {"id": "MCP_35", "label": "时间性能-建连速率次测试", "type": 2, "attrs": {"description": "建连速率次测试", "steps": [{"step": "1、测试模拟工具上创建x个进程，每进程创建x个线程，测试模拟工具调用所有的进程和线程，按{{speed}}速率同时触发向组件发起建立支持规格数的连接，记录连接起始时间和所有连接都建立成功时间，计算建连速率，并观察内存利用率、CPU利用率，有预期结果1", "expect": "1、建连速率=连接数/(所有连接都建立成功时间-连接起始时间)，内存利用率和CPU利用率满足规格定义要求；"}, {"step": "2、杀掉所有的创建进程，有预期结果2", "expect": "2、release object成功，无错误码返回，查看整个连接数为初始状态；"}]}}, {"id": "MCP_36", "label": "时间性能-过载-建连速率测试", "type": 2, "attrs": {"description": "过载-建连速率测试", "steps": [{"step": "1、测试模拟工具上创建x个进程，每进程创建x个线程，测试模拟工具调用所有的进程和线程，按{{speed}}*15速率同时触发向组件发起建立支持规格数的连接，记录连接起始时间和所有连接都建立成功时间，计算建连速率，并观察内存利用率、CPU利用率，有预期结果1", "expect": "1、建连速率满足韧性要求，内存利用率和CPU利用率满足规格定义要求"}, {"step": "2、测试模拟工具上创建x个进程，每进程创建x个线程，测试模拟工具调用所有的进程和线程，按{{speed}}*3速率同时触发向组件发起建立支持规格数的连接，记录连接起始时间和所有连接都建立成功时间，计算建连速率，并观察内存利用率、CPU利用率，有预期结果2", "expect": "2、建连速率满足韧性要求，内存利用率和CPU利用率满足规格定义要求"}, {"step": "3、测试模拟工具上创建x个进程，每进程创建x个线程，测试模拟工具调用所有的进程和线程，按{{speed}}*10速率同时触发向组件发起建立支持规格数的连接，记录连接起始时间和所有连接都建立成功时间，计算建连速率，并观察内存利用率、CPU利用率，有预期结果3", "expect": "3、建连速率满足韧性要求，内存利用率和CPU利用率满足规格定义要求"}, {"step": "4、测试模拟工具上创建x个进程，每进程创建x个线程，测试模拟工具调用所有的进程和线程，按{{speed}}*64速率同时触发向组件发起建立支持规格数的连接，记录连接起始时间和所有连接都建立成功时间，计算建连速率，并观察内存利用率、CPU利用率，有预期结果4", "expect": "4、建连速率满足韧性要求，内存利用率和CPU利用率满足规格定义要求"}, {"step": "5、测试模拟工具上创建x个进程，每进程创建x个线程，测试模拟工具调用所有的进程和线程，按{{speed}}*（>64）速率同时触发向组件发起建立支持规格数的连接，记录连接起始时间和所有连接都建立成功时间，计算建连速率，并观察内存利用率、CPU利用率，有预期结果5", "expect": "5、建连速率满足韧性要求，内存利用率和CPU利用率满足规格定义要求"}, {"step": "6、杀掉所有的创建进程，有预期结果6", "expect": "6、release object成功，无错误码返回，查看整个连接数为初始状态"}]}}]}]}, {"label": "组件基础环境", "id": "9aa97be409faf5fc", "children": [{"label": "转发服务", "id": "c8cf16284a032c07", "children": [{"id": "MCP_40", "label": "组件软件吞吐量与时延测试", "type": 2, "attrs": {"description": "组件业务数据吞吐量规格测试", "steps": [{"step": "1、配置组件与测试仪（如vtesgine或其他工具）相连通的配置", "expect": "1、吞吐量、时延、抖动及内存利用率和CPU利用率满足规格定义要求"}, {"step": "2、测试仪执行RFC2544测试套测试典型字节(64,256,512,1024,1280,1518,2000,3000,4000,5000,6000,7000,8000,9000,9600及全部字节按1:1混合）发送规格速率的流量，打流时间60s，并查看吞吐量、时延、抖动，内存利用率、CPU利用率", "expect": "2、收包速率及内存利用率和CPU利用率满足规格定义要求"}]}}, {"id": "MCP_41", "label": "过载-组件软件吞吐量与时延测试", "type": 2, "attrs": {"description": "过载-组件软件吞吐量与时延测试", "steps": [{"step": "1、配置组件与测试仪（如vtesgine或其他工具）相连通的配置", "expect": "1、吞吐量、时延、抖动及内存利用率和CPU利用率满足规格定义要求"}, {"step": "2、测试仪执行RFC2544测试套测试典型字节(64,256,512,1024,1280,1518,2000,3000,4000,5000,6000,7000,8000,9000,9600及全部字节按1:1混合）发送超1.5倍量规格速率的流量，打流时间60s，并查看吞吐量、时延、抖动，内存利用率、CPU利用率", "expect": "2、收包速率及内存利用率和CPU利用率满足规格定义要求"}]}}, {"id": "MCP_42", "label": "转发缓存与时延测试", "type": 2, "attrs": {"description": "转发缓存与时延测试", "steps": [{"step": "1、按照端口缓存大小测试方法，可以设置三个口，A B 入，C出，B持续打入满带宽的流，C端无丢包，A打入burst流，采用二分法，尝试到C端可以接收bust报文数，无丢包，次bust（报文个数*字节）值即为缓存值", "expect": "1、测试出的缓存和时延符合规格"}]}}]}, {"label": "监控诊断", "id": "e75fe2601f3da511", "children": [{"label": "日志服务", "id": "52c1e39026e0f6cd", "children": [{"id": "MCP_45", "label": "日志抑制测试", "type": 2, "attrs": {"description": "日志抑制测试", "steps": [{"step": "1、短时间内同一个模块产生需要大量记录日志的情况，有预期结果1", "expect": "1、根据规格设定的大小对日志进行抑制，不会造成快速将整个日志写满等结果"}]}}, {"id": "MCP_46", "label": "日志写速率测试", "type": 2, "attrs": {"description": "日志写速率测试", "steps": [{"step": "1、触发需要按照x速率写日志，记录日志记录速率，有预期结果1；", "expect": "1、日志记录速率可以达到规格要求，不会造成CPU高"}]}}, {"id": "MCP_47", "label": "日志类文件大小测试", "type": 2, "attrs": {"description": "日志类文件大小测试", "steps": [{"step": "1、触发组件持续写日志，有预期结果1；", "expect": "1、日志文件正常，对超过单日志最大值，日志进行压缩，超过整个日志最大值，按照规格规定情况（对旧日志覆盖或将旧日志删除等）"}]}}, {"id": "MCP_48", "label": "日志类文件数量测试", "type": 2, "attrs": {"description": "日志类文件数量测试", "steps": [{"step": "1、触发组件持续写日志记录到文件下，有预期结果1；", "expect": "1、组件支持写到不同文件的功能，可以成功写入到多个文件下，文件个数达到规格要求；"}]}}]}]}]}, {"label": "组件接口", "id": "03fe36c5418fb7dc", "children": [{"label": "通信接口", "id": "e56f99bbde3d9c98", "children": [{"id": "MCP_51", "label": "接收端缓存大小测试", "type": 2, "attrs": {"description": "接收端缓存大小测试", "steps": [{"step": "1、按照端口缓存大小测试方法，打流超过接收端处理能力，使缓存占满，有预期结果1；", "expect": "1、测试出缓存大小满足规格要求；"}]}}]}, {"label": "消息接口", "id": "250a6c463fce7ffe", "children": [{"label": "MESH", "id": "cec69c472c3be1d9", "children": [{"id": "MCP_54", "label": "消息处理吞吐量与处理时间", "type": 2, "attrs": {"description": "消息处理吞吐量与处理时间", "steps": [{"step": "1、 通过xx（如testapp）给测试组件高速率发送配置类MESH消息，有预期结果1；", "expect": "1、 在规格速率内的，xx（如testapp）在预期时间T内，能收到桩返回结果，并发送DB数据读取，收到DB返回的正确结果，如果该配置有日志记录，同步读取日志结果；"}]}}, {"id": "MCP_55", "label": "过载-消息处理吞吐量与处理时间", "type": 2, "attrs": {"description": "过载-消息处理吞吐量与处理时间", "steps": [{"step": "1、 通过xx（如testapp）给测试组件高速率（规格3倍）发送配置类MESH消息，有预期结果1；", "expect": "1、 在规格速率内的，xx（如testapp）在预期时间T内，能收到桩返回结果，并发送DB数据读取，收到DB返回的正确结果，如果该配置有日志记录，同步读取日志结果；"}]}}]}, {"label": "PUB/SUB", "id": "d512e0e74ac6ee22", "children": [{"id": "MCP_57", "label": "SUB处理吞吐量与处理时间", "type": 2, "attrs": {"description": "SUB处理吞吐量与处理时间", "steps": [{"step": "1、 通过xx（如testapp）给测试组件高速率发送配置类PUB/SUB消息，有预期结果1；", "expect": "1、 在规格速率内的，xx（如testapp）在预期时间T内，能收到桩返回结果，并发送DB数据读取，收到DB返回的正确结果，如果该配置有日志记录，同步读取日志结果；"}]}}, {"id": "MCP_58", "label": "过载-SUB处理吞吐量与处理时间", "type": 2, "attrs": {"description": "过载-SUB处理吞吐量与处理时间", "steps": [{"step": "1、 通过xx（如testapp）给测试组件高速率（规格3倍）发送配置类PUB/SUB消息，有预期结果1；", "expect": "1、 在规格速率内的，xx（如testapp）在预期时间T内，能收到桩返回结果，并发送DB数据读取，收到DB返回的正确结果，如果该配置有日志记录，同步读取日志结果；"}]}}]}, {"label": "CROS(RPC)", "id": "b0b573de3f9e3e79", "children": [{"id": "MCP_60", "label": "消息处理吞吐量与处理时间(增删查改)", "type": 2, "attrs": {"description": "消息处理吞吐量与处理时间(增删查改)", "steps": [{"step": "1、 通过xx（如testapp）给测试组件高速率发送CROS(RPC)消息，有预期结果1；", "expect": "1、 在规格速率内的，xx（如testapp）在预期时间T内，能收到桩返回结果，并发送DB数据读取，收到DB返回的正确结果，如果该配置有日志记录，同步读取日志结果；"}]}}, {"id": "MCP_61", "label": "消息处理时间(增删查改)", "type": 2, "attrs": {"description": "消息处理时间(增删查改)", "steps": [{"step": "1、 通过xx（如testapp）给测试组件高速率（规格3倍）发送配置类MESH消息，有预期结果1；", "expect": "1、 在规格速率内的，xx（如testapp）在预期时间T内，能收到桩返回结果，并发送DB数据读取，收到DB返回的正确结果，如果该配置有日志记录，同步读取日志结果；"}]}}, {"id": "MCP_62", "label": "过载-消息处理吞吐量与处理时间(增删查改)", "type": 2, "attrs": {"description": "过载-消息处理吞吐量与处理时间(增删查改)", "steps": [{"step": "1、 通过xx（如testapp）给测试组件高速率（规格3倍）发送配置类MESH消息，有预期结果1；", "expect": "1、 在规格速率内的，xx（如testapp）在预期时间T内，能收到桩返回结果，并发送DB数据读取，收到DB返回的正确结果，如果该配置有日志记录，同步读取日志结果；"}]}}]}]}, {"label": "函数接口", "id": "4a558b60d06ac88e", "children": [{"id": "MCP_64", "label": "API执行吞吐量与响应时间", "type": 2, "attrs": {"description": "API执行吞吐量与响应时间", "steps": [{"step": "1、通过模拟工具发送流量到API接口，获取API接口返回和处理的数据，有预期结果1；", "expect": "1、API接口的吞吐量和响应时间在规格范围内；"}]}}, {"id": "MCP_65", "label": "过载-API执行吞吐量与响应时间", "type": 2, "attrs": {"description": "过载-API执行吞吐量与响应时间", "steps": [{"step": "1、通过模拟工具发送过载流量到API接口，获取API接口返回和处理的数据，有预期结果1；", "expect": "1、API接口的吞吐量和响应时间在规格范围内；"}]}}]}, {"label": "数据接口", "id": "eb404e3ba237619e", "children": [{"label": "命令行", "id": "a95cbe2c7294aa12", "children": [{"id": "MCP_68", "label": "配置类数量测试", "type": 2, "attrs": {"description": "配置类数量测试", "steps": [{"step": "1、模拟规格支持最大量配置增加下发组件；", "expect": "1、配置可以正常下发，CPU符合规格要求；"}, {"step": "2、模拟规格支持最大量配置删除下发组件；"}]}}, {"id": "MCP_69", "label": "配置类响应时间测试", "type": 2, "attrs": {"description": "配置类响应时间测试", "steps": [{"step": "1、组件下发规格配置类增加命令行，有预期结果1；", "expect": "1、记录配置下发和响应时间，计算出配置响应时间，响应时间符合规格；"}, {"step": "2、组件下发规格配置类删除命令行，有预期结果1；", "expect": "1、记录配置下发和响应时间，计算出配置响应时间，响应时间符合规格；"}]}}, {"id": "MCP_70", "label": "查询类响应时间测试", "type": 2, "attrs": {"description": "查询类响应时间测试", "steps": [{"step": "1、朝组件下发大量回显查询命令行，有预期结果1；", "expect": "1、记录查询命令行下发和响应时间，计算出配置响应时间，响应时间符合规格；"}]}}]}, {"label": "YANG", "id": "a0d1b24bee9ea478", "children": [{"id": "MCP_72", "label": "单客户端-配置类-数量测试", "type": 2, "attrs": {"description": "单客户端-配置类-数量测试", "steps": [{"step": "1、使用yang给组件下发最大量配置增加下发组件；", "expect": "1、新建50个节点符合规格要求，CPU一分钟内需要恢复到配置前状态"}, {"step": "2、使用yang给组件下发最大量配置删除下发组件；", "expect": "2、修改50个节点符合规格要求，CPU一分钟内需要恢复到配置前状态"}]}}, {"id": "MCP_73", "label": "单客户端-配置类-响应时间测试", "type": 2, "attrs": {"description": "单客户端-配置类-响应时间测试", "steps": [{"step": "1、下发配置单个yang节点，有预期结果1；", "expect": "1、记录配置下发和响应时间，计算出配置响应时间，响应时间符合规格；"}, {"step": "2、下发大业务量配置yang节点，有预期结果1；", "expect": "1、记录配置下发和响应时间，计算出配置响应时间，响应时间符合规格；"}, {"step": "3、下发删除大量yang节点，有预期结果1；", "expect": "1、记录配置下发和响应时间，计算出配置响应时间，响应时间符合规格；"}]}}, {"id": "MCP_74", "label": "单客户端-查询类-响应时间测试", "type": 2, "attrs": {"description": "单客户端-查询类-响应时间测试", "steps": [{"step": "1、下发查询yang节点，有预期结果1；", "expect": "1、记录查询命令行下发和响应时间，计算出配置响应时间，响应时间符合规格；"}, {"step": "2、下发查询有大量回显的yang节点，有预期结果1；", "expect": "1、记录查询命令行下发和响应时间，计算出配置响应时间，响应时间符合规格；"}]}}, {"id": "MCP_75", "label": "多客户端并发-查询类-响应时间测试", "type": 2, "attrs": {"description": "多客户端并发-查询类-响应时间测试", "steps": [{"step": "1、多客户端（达到最大规格）和组件建立多个netconf连接，查询yang节点内容，记录报文下发到端返回报文的时间", "expect": "1、查询xx个节点符合规格要求，，CPU一分钟内需要恢复到查询前状态"}]}}]}, {"label": "MIB", "id": "a12422ceda16ecd3", "children": [{"id": "MCP_78", "label": "单客户端-配置类数量测试", "type": 2, "attrs": {"description": "单客户端-配置类数量测试", "steps": [{"step": "1、使用MIB给组件下发最大量配置增加下发组件；", "expect": "1、工具能记录配置下发和响应时间，计算出配置响应时间，响应时间符合规格；"}, {"step": "2、使用MIB朝组件下发最大量配置删除下发组件；"}]}}, {"id": "MCP_79", "label": "单客户端-查询类响应时间测试", "type": 2, "attrs": {"description": "单客户端-查询类响应时间测试", "steps": [{"step": "1、配置和MIB表项相关的业务，配置SNMP V2C，超时时间1s，重传次数3次", "expect": "1、工具能记录查询下发和响应时间，计算出配置响应时间，响应时间符合规格；"}, {"step": "2、按节点做get-next/get-bulk/walk查询操作，查看回显返回时间并在get-next查询前后查询CPU"}]}}, {"id": "MCP_80", "label": "单客户端-配置类响应时间测试", "type": 2, "attrs": {"description": "单客户端-配置类响应时间测试", "steps": [{"step": "1、下发配置单个MIB节点，有预期结果1；", "expect": "1、工具能记录配置命令行下发和响应时间，计算出配置响应时间，响应时间符合规格；"}, {"step": "2、下发大业务量配置MIB节点，有预期结果1；", "expect": "1、工具能记录配置命令行下发和响应时间，计算出配置响应时间，响应时间符合规格；"}, {"step": "3、下发删除大量MIB节点，有预期结果1；", "expect": "1、工具能记录配置命令行下发和响应时间，计算出配置响应时间，响应时间符合规格；"}, {"step": ""}]}}, {"id": "MCP_81", "label": "多客户端并发-查询类-响应时间测试", "type": 2, "attrs": {"description": "多客户端并发-查询类-响应时间测试", "steps": [{"step": "1、使用多客户并发发送MIB get-next/get-bulk查询，有预期结果1；", "expect": "1、x个用户（达到规格最大值）同时做get-next查询操作，查看回显返回时间，查询CPU正常；"}]}}]}, {"label": "配置文件", "id": "fc1b7df04b2c6302", "children": [{"id": "MCP_83", "label": "配置类文件可配置项数量测试", "type": 2, "attrs": {"description": "配置类文件可配置项数量测试", "steps": [{"step": "1、构造配置文件，配置文件的内容达到业务规格可配置的最大值，加载配置文件有预期结果1；", "expect": "1、配置文件可以导入，导入后配置正常下发和生效；"}]}}, {"id": "MCP_84", "label": "配置类文件加载时间测试", "type": 2, "attrs": {"description": "配置类文件加载时间测试", "steps": [{"step": "1、构造配置文件，配置文件的内容达到业务规格可配置的最大值，加载配置文件有预期结果1；", "expect": "1、从配置文件导入到配置下发完成生效的时间在规格范围内；"}]}}]}]}]}]}, {"id": "8952ac0a9eb6473c", "label": "可靠性交互分析", "children": [{"label": "组件运行环境（OS)", "id": "ac1b2c9991bb1c95", "children": [{"label": "进程", "id": "69ee231ac97bbc86", "children": [{"id": "MCR_1", "label": "启动-依赖进程未启动", "type": 2, "attrs": {"description": "启动依赖进程未启动", "steps": [{"step": "1、分析被测试进程的依赖进程有哪些，不启动这些进程，或直接将进程kill掉；"}, {"step": "2、启动被验证进程，有预期结果1；", "expect": "1、被测试进程无法启动，有清晰的提示和日志记录；"}]}}, {"id": "MCR_2", "label": "启动-OS资源不足导致新进程无法启动", "type": 2, "attrs": {"description": "OS资源不足导致新进程无法启动", "steps": [{"step": "1、通过故障注入工具在运行start.sh文件前中将内存资源（大页内存、操作系统内存）可用内存资源小于组件启动所需要的内存资源；"}, {"step": "2、启动被验证进程，有预期结果1；", "expect": "1、被测试进程无法启动，内存资源不足有清晰的提示和日志记录；"}]}}, {"id": "MCR_4", "label": "运行中-进程死循环故障", "type": 2, "attrs": {"description": "进程死循环故障", "steps": [{"step": "1、测试容器中需要集成PSSP，通过故障注入工具，注入组件进程死循环故障，有预期结果1；", "expect": "1、可以监控到进程死循环，记录日志，根据设定的故障处理规则进行相应的操作（进程重启）。"}]}}, {"id": "MCR_5", "label": "运行中-进程D状态故障", "type": 2, "attrs": {"description": "进程D状态故障", "steps": [{"step": "1、测试容器中需要集成PSSP，通过故障注入工具，注入组件进程D状态故障，有预期结果1；", "expect": "1、可以监控到进程死循环，记录日志，根据设定的故障处理规则进行相应的操作（进程重启）。"}]}}, {"id": "MCR_6", "label": "运行中-进程Z状态故障", "type": 2, "attrs": {"description": "进程Z状态故障", "steps": [{"step": "1、测试容器中需要集成PSSP，通过故障注入工具，注入组件进程Z状态故障，有预期结果1；", "expect": "1、可以监控到进程死循环，记录日志，根据设定的故障处理规则进行相应的操作（进程重启）。"}]}}, {"id": "MCR_7", "label": "运行中-进程挂死状态故障", "type": 2, "attrs": {"description": "进程挂死状态故障", "steps": [{"step": "1、测试容器中需要集成PSSP，通过故障注入工具，注入进程挂起故障，有预期结果1；", "expect": "1、可以监控到进程死循环，记录日志，根据设定的故障处理规则进行相应的操作（进程重启）。"}]}}, {"id": "MCR_8", "label": "运行中-进程僵尸状态故障", "type": 2, "attrs": {"description": "进程僵尸状态故障", "steps": [{"step": "1、测试容器中需要集成PSSP，通过故障注入工具，注入进程僵尸状态故障，有预期结果1；", "expect": "1、可以监控到进程死循环，记录日志，根据设定的故障处理规则进行相应的操作（进程重启）。"}]}}, {"id": "MCR_9", "label": "运行中-进程句柄耗尽故障", "type": 2, "attrs": {"description": "进程句柄耗尽故障", "steps": [{"step": "1、测试容器中需要集成PSSP，通过故障注入工具，注入进程句柄耗尽故障，有预期结果1；", "expect": "1、可以监控到进程死循环，记录日志，根据设定的故障处理规则进行相应的操作（进程重启）。"}]}}, {"id": "MCR_12", "label": "退出-运行依赖进程非正常退出", "type": 2, "attrs": {"description": "运行依赖进程非正常退出", "steps": [{"step": "1、通过故障注入工具，或手工kill方式强行将被测试进程依赖的进程退出，有预期结果1；", "expect": "1、检测到依赖进程退出事件，并记录日志。根据架构规则，被测试组件清理已经申请的资源，进程退出。"}]}}, {"id": "MCR_13", "label": "退出-运行依赖进程正常退出", "type": 2, "attrs": {"description": "运行依赖进程正常退出", "steps": [{"step": "1、通过故障注入工具，或手工方式将被测试进程依赖进程退出，有预期结果1；", "expect": "1、检测进程退出事件，并记录日志。根据架构规则，被测试组件清理已经申请的资源，进程退出。"}]}}, {"id": "MCR_15", "label": "重启-自动重启(检测或定时)", "type": 2, "attrs": {"description": "自动重启(检测或定时)", "steps": [{"step": "1、分析被测试进程自动重启条件，用故障注入工具模拟，向故障进程发送重启信号，如task任务堆积、达到自愈条件等，触发进程自动重启，有预期结果1；", "expect": "1、被测试进程可以正常启动，业务正常，进程重启前占用的资源释放无残留，日志记录进程重启原因；"}]}}, {"id": "MCR_16", "label": "重启-手工重启", "type": 2, "attrs": {"description": "手工重启", "steps": [{"step": "1、被测试进程正常运行，通过手工restart该进程，有预期结果1；", "expect": "1、被测试经常可以正常启动，业务正常，进程重启前占用的资源释放无残留，日志记录进程重启原因；"}]}}, {"id": "MCR_17", "label": "重启-升级重启", "type": 2, "attrs": {"description": "升级重启", "steps": [{"step": "1、被测试进程正常运行，升级软件版本或补丁，升级重启后，有预期结果1；", "expect": "1、被测试经常可以正常启动，业务正常，进程重启前占用的资源释放无残留，日志记录进程重启原因；"}]}}, {"id": "MCR_18", "label": "Kill信号量-Kill信号遍历测试", "type": 2, "attrs": {"description": "信号量遍历测试", "steps": [{"step": "1、进程已经启动，按照规格模式，各种模式逐一进行kill测试；", "expect": "1、可以检测到信号被kill事件并记录日志；根据架构规则，触发组件线程重新运行或进程退出。"}]}}]}, {"label": "线程", "id": "3ae03a2a571acd43", "children": [{"id": "MCR_20", "label": "线程异常退出故障", "type": 2, "attrs": {"description": "线程异常退出故障", "steps": [{"step": "1、通过故障注入工具，kill线程，有预期结果1", "expect": "1、线程正常退出，日志记录线程退出原因。根据架构规则，被测试组件清理已经申请的资源，进程退出。"}]}}, {"id": "MCR_21", "label": "线程死循环故障", "type": 2, "attrs": {"description": "线程死循环故障", "steps": [{"step": "1、通过故障注入工具 ，注入线程死循环故障，有预期结果1", "expect": "1、可以检测到线程死循环并记录日志；根据架构规则，触发组件线程重新运行或进程退出。"}]}}, {"id": "MCR_22", "label": "线程挂死故障", "type": 2, "attrs": {"description": "线程挂死故障", "steps": [{"step": "1、通过故障注入工具 ，注入5s线程挂死故障，有预期结果1；", "expect": "1、可以检测到线程挂死并记录日志；根据架构规则，触发组件线程重新运行或进程退出。；"}, {"step": "2、通过故障注入工具 ，注入3分钟线程挂死故障，有预期结果1；", "expect": "1、可以检测到线程挂死并记录日志；根据架构规则，触发组件线程重新运行或进程退出。；"}]}}, {"id": "MCR_23", "label": "线程间死锁", "type": 2, "attrs": {"description": "线程间死锁", "steps": [{"step": "1、通过故障注入工具 ，注入线程间死锁；", "expect": "1、可以检测到线程死锁并记录日志；根据架构规则，触发组件线程重新运行或进程退出。"}]}}, {"id": "MCR_24", "label": "线程句柄耗尽故障", "type": 2, "attrs": {"description": "线程句柄耗尽故障", "steps": [{"step": "1、通过故障注入工具 ，耗尽进程内的线程句柄，创建新线程返回失败，有预期结果1；", "expect": "1、检测到线程句柄耗尽并记录日志，根据架构规则，触发组件线程重新运行或进程退出。"}]}}]}, {"label": "信号量", "id": "fd996550f28859e9", "children": [{"id": "MCR_26", "label": "信号量申请失败", "type": 2, "attrs": {"description": "信号量申请失败", "steps": [{"step": "1、通过故障注入工具，注入信号量值为0，线程申请信号量，有预期结果1", "expect": "1、检测到信号量申请失败并记录日志，根据架构规则，触发组件线程重新运行或进程退出。"}]}}, {"id": "MCR_27", "label": "信号量死锁", "type": 2, "attrs": {"description": "信号量死锁", "steps": [{"step": "1、通过故障注入工具，注入使线程A占用资源x去申请y和线程B占用资源y去申请x，产生信号量死锁，有预期结果1", "expect": "1、检测到信号量死锁并记录日志，根据架构规则，触发组件线程重新运行或进程退出。"}]}}, {"id": "MCR_28", "label": "信号量海量中断", "type": 2, "attrs": {"description": "信号量海量中断", "steps": [{"step": "暂时无需测试", "expect": "暂时无需测试"}]}}]}, {"label": "内存", "id": "ca97042293389e12", "children": [{"id": "MCR_30", "label": "内存泄漏检测", "type": 2, "attrs": {"description": "内存泄漏检测", "steps": [{"step": "1、组件自身有内存管理机制，通过故障注入工具注入使组件内存泄漏，验证内存监测机制是否生效", "expect": "1、检测到内存泄漏并记录日志。根据架构原则，组件内存限制机制生效或重启。"}, {"step": "2、组件利用公共的内存泄漏检查机制，通过故障注入工具注入使组件内存泄漏，验证内存监测机制是否生效"}]}}, {"id": "MCR_31", "label": "组件申请内存失败", "type": 2, "attrs": {"description": "组件申请内存失败", "steps": [{"step": "1、利用故障注入工具接管内存申请、释放的接口，组件申请内存失败，有预期结果1；", "expect": "1、检测到内存申请失败并记录日志。根据架构原则，组件内存限制机制生效或重启。"}]}}, {"id": "MCR_32", "label": "组件申请共享内存失败", "type": 2, "attrs": {"description": "组件申请共享内存失败", "steps": [{"step": "1、利用故障注入工具接管内存申请、释放的接口，组件申请共享内存失败，有预期结果1；", "expect": "1、检测到内存申请失败并记录日志。根据架构原则，组件重启。"}]}}, {"id": "MCR_33", "label": "组件申请私有内存失败", "type": 2, "attrs": {"description": "组件申请私有内存失败", "steps": [{"step": "1、利用故障注入工具接管内存申请、释放的接口，组件申请私有内存失败，有预期结果1；", "expect": "1、检测到内存申请失败并记录日志。根据架构原则，组件重启。"}]}}, {"id": "MCR_35", "label": "组件申请大页内存失败", "type": 2, "attrs": {"description": "组件申请大页内存失败", "steps": [{"step": "1、利用故障注入工具接管内存申请、释放的接口，组件申请大页内存失败，有预期结果1；", "expect": "1、检测到内存申请失败并记录日志。根据架构原则，组件重启。"}]}}, {"id": "MCR_36", "label": "表项耗尽（申请资源失败）", "type": 2, "attrs": {"description": "表项耗尽（申请资源失败）", "steps": [{"step": "1、利用故障注入工具接管表项申请、释放的接口，组件申请表项失败，有预期结果1；", "expect": "1、检测到表项申请资源失败并记录日志。根据架构原则，组件重启。"}]}}]}, {"label": "CPU", "id": "c646ac61543d152d", "children": [{"id": "MCR_38", "label": "CPU过载", "type": 2, "attrs": {"description": "CPU过载", "steps": [{"step": "1、利用故障注入工具注入故障，CPU使用率在<90%波动，运行组件测试用例，对比前后结果", "expect": "1、组件业务运行正常，测试用例能正常通过。"}]}}, {"id": "MCR_39", "label": "核失效", "type": 2, "attrs": {"description": "核失效", "steps": [{"step": "1、未绑定具体CPU核的情况下，通过故障注入工具，注入组件\\进程\\线程使用的CPU核失效，有预期结果1；", "expect": "1、能检测CPU核失效并记录日志，业务正常运行；"}, {"step": "2、绑定具体CPU核的情况下，通过故障注入工具，注入组件\\进程\\线程使用的CPU核失效，有预期结果2；", "expect": "2、能检测CPU核失效并记录日志，根据架构原理通知客户选择相关操作；"}]}}, {"id": "MCR_40", "label": "CPU挂死", "type": 2, "attrs": {"description": "CPU挂死", "steps": [{"step": "1、通过故障注入工具注入cpu单个核挂死5s以内；", "expect": "1、能检测到CPU挂死故障，并记录日志；"}, {"step": "2、通过故障注入工具注入cpu部分核挂死超过3分钟；", "expect": "2、能检测CPU挂死并记录日志，根据架构原理通知客户选择相关操作；"}, {"step": ""}]}}, {"id": "MCR_41", "label": "CPU正弦曲线式过载", "type": 2, "attrs": {"description": "CPU正弦曲线式过载", "steps": [{"step": "1、注入故障，CPU使用率在<90%波动，运行组件测试用例，对比前后结果", "expect": "1、组件业务运行正常，测试用例能正常通过。"}]}}]}, {"label": "定时器", "id": "850d2622f016e8cd", "children": [{"id": "MCR_44", "label": "定时器申请失败", "type": 2, "attrs": {"description": "定时器申请失败", "steps": [{"step": "1、运行xx组件业务，组件进程申请定时器，通过软件故障注入组件申请不到定时器，有预期结果1", "expect": "1、能检测到定时器申请失败，并记录日志；"}]}}, {"id": "MCR_45", "label": "定时器超时未触发", "type": 2, "attrs": {"description": "定时器超时未触发", "steps": [{"step": "1、运行xx组件业务，软件故障注入组件进程的某个定时器超时未触发（未发送定时器超时消息、定时器超时消息丢失、回调函数未被执行），有预期结果1。", "expect": "1、能检测到定时器超时未触发，并记录日志；"}]}}]}, {"label": "时间", "id": "36dfede5f4ec53e8", "children": [{"id": "MCR_47", "label": "夏令时跳变", "type": 2, "attrs": {"description": "夏令时跳变", "steps": [{"step": "1、夏令时跳变，有预期结果1", "expect": "1、能检测到夏令时跳变，并记录日志；组件能定时同步时间 。"}]}}, {"id": "MCR_48", "label": "系统时间跳变", "type": 2, "attrs": {"description": "系统时间跳变", "steps": [{"step": "1、系统时间跳变，有预期结果1", "expect": "1、能检测到系统时间跳变，并记录日志；组件能定时同步时间 。"}]}}]}, {"label": "SELinux", "id": "910f54042cd62998", "children": []}, {"label": "文件", "id": "6049f4610da25400", "children": [{"id": "MCR_51", "label": "文件句柄耗尽", "type": 2, "attrs": {"description": "文件句柄耗尽", "steps": [{"step": "1、通过故障注入工具注入文件句柄耗尽故障，可以分别构造文件句柄泄漏80%、90%和100%情况，有预期结果1；", "expect": "1、能检测到文件句柄耗尽并记录日志，可以通过命令行查询文件句柄的占用率，设计告警阀值，可以是85%，超过阀值时会产生告警,80%无告警、90%告警、100%开始进行自愈如重启进程，可以设置超时回收机制，超时后自动回收文件句柄资源；"}, {"step": "2、恢复故障，再次触发流程；"}]}}, {"id": "MCR_52", "label": "文件不可读", "type": 2, "attrs": {"description": "文件不可读", "steps": [{"step": "1、通过故障注入工具注入xx文件不可读的故障，xx业务读xx文件，有预期结果1；", "expect": "1、文件读取失败，产生xx文件故障的告警，并记录读取失败的记录；"}, {"step": "2、通过故障注入工具撤销文件不可读的故障，xx业务读xx文件，有预期结果2；", "expect": "2、故障撤销后，可以正常读取文件，业务正常，xx文件不可用的告警恢复，并记录日志；"}]}}, {"id": "MCR_53", "label": "文件不可写", "type": 2, "attrs": {"description": "文件不可写", "steps": [{"step": "1、通过故障注入工具注入xx文件不可写的故障，xx业务写xx文件，有预期结果1；", "expect": "1、文件写失败，产生xx文件故障的告警，并记录写失败的记录；"}, {"step": "2、通过故障注入工具撤销文件不可写的故障，xx业务写xx文件，有预期结果2；", "expect": "2、故障撤销后，可以正常写文件，业务正常，xx文件不可用的告警恢复，并记录日志；"}]}}, {"id": "MCR_54", "label": "读数据慢或超时", "type": 2, "attrs": {"description": "读数据慢或超时", "steps": [{"step": "1、xx业务开始读文件，在数据传输过程中（未读完），通过故障注入工具，注入回复消息慢或不回应，有预期结果1；", "expect": "1、读的慢或超时，会有超时重传机制，不能挂死，记录日志和告警。其他业务不受影响，不会有内存泄漏或资源泄漏问题；"}, {"step": "2、撤销该故障注入，重新读该文件，有预期结果2；", "expect": "2、文件可以正常读取；"}]}}, {"id": "MCR_55", "label": "写数据慢或超时", "type": 2, "attrs": {"description": "写数据慢或超时", "steps": [{"step": "1、xx业务开始写文件，在数据传输过程中（未写完），通过故障注入工具，注入回复消息慢或不回应，有预期结果1；", "expect": "1、写的慢或超时，会有超时重传机制，不能挂死，记录日志和告警。其他业务不受影响，不会有内存泄漏或资源泄漏问题；"}, {"step": "2、撤销该故障注入，重新写该文件，有预期结果2；", "expect": "2、文件可以正常写；"}]}}, {"id": "MCR_56", "label": "重命名失败", "type": 2, "attrs": {"description": "重命名失败", "steps": [{"step": "1、通过故障注入工具注入，或将该文件打开，使其该文件不能重命名，对文件进程重命名操作，有预期结果1；", "expect": "1、文件重命名失败，有正确的提升信息和日志记录，无内存等资源泄漏或其他异常；"}, {"step": "2、撤销故障注入，或将该文件关闭，对文件进行重命名，有预期结果2；", "expect": "2、重命名成功；"}]}}, {"id": "MCR_57", "label": "文件丢失", "type": 2, "attrs": {"description": "文件丢失", "steps": [{"step": "1、通过故障注入工具 ，将文件删除，模拟文件丢失，组件运行，需要读写该文件，有预期结果1；", "expect": "1、对启动文件等关键文件有备份机制，主文件丢失无法访问会自动切换到备份文件，对没有备份机制的文件丢失，业务能检测出文件不存在，产生告警和日志，不对已开展的业务产生影响；"}]}}, {"id": "MCR_58", "label": "文件只读", "type": 2, "attrs": {"description": "文件只读", "steps": [{"step": "1、设置文件为只读，被测试组件对文件进行读写，有预期结果1；", "expect": "1、文件可以读成功，不能写，有正确的错误提示，组件能正确处理写失败事件，已有业务运行正常；"}]}}, {"id": "MCR_59", "label": "文件访问死锁", "type": 2, "attrs": {"description": "文件访问死锁", "steps": [{"step": "1、通过故障注入工具，注入文件/目录死锁访问冲突，有预期结果1；", "expect": "1、能够检测出死锁的位置和原因并进行告警和日志记录，系统对访问文件产生死锁的进程进行撤销或重启；"}, {"step": "2、撤销故障注入，解除死锁，有预期结果2；", "expect": "2、文件可以正常访问；"}]}}, {"id": "MCR_60", "label": "文件不可删除", "type": 2, "attrs": {"description": "文件不可删除", "steps": [{"step": "1、通过故障注入工具，将指定文件/目录设置成不可删除，对文件进行删除操作，有预期结果1；", "expect": "1、文件不能删除，有文件删除失败日志记录；"}, {"step": "2、撤销故障注入，有预期结果2", "expect": "2、文件可以正常删除；"}]}}, {"id": "MCR_61", "label": "文件内容随机变异", "type": 2, "attrs": {"description": "文件内容随机变异", "steps": [{"step": "1、通过故障注入工具，随机生成文件内容，并对文件进行 读、写、保存，有预期结果1；", "expect": "1、文件可以正常读、写、保存，业务正常；"}]}}, {"id": "MCR_62", "label": "0字节文件", "type": 2, "attrs": {"description": "0字节文件", "steps": [{"step": "1、通过故障注入工具，注入指定文件为0字节，对该文件进行操作，如打开、读取等，有预期结果1", "expect": "1、可以正常打开和读，进程不产生异常退出等异常，可以正常写入内容；"}]}}, {"id": "MCR_63", "label": "超大文件", "type": 2, "attrs": {"description": "超大文件", "steps": [{"step": "1、获取硬盘或文件存放空间大小，及剩余空间统计，通过故障注入工具模拟创建大文件，占满空间，对文件进行打开、读、写等操作，有预期结果1；", "expect": "1、文件在空间范围内可以创建、打开、读、写，不能出现异常，文件占用率超过百分比（参考版本的规格）进行告警和日志记录；"}]}}, {"id": "MCR_64", "label": "存储满", "type": 2, "attrs": {"description": "存储满", "steps": [{"step": "1、通过故障注入工具，创建多个文件，使存储空间占满，在存储空间满的情况下，创建新的文件及对已经存在的文件进行读、写等操作，有预期结果1", "expect": "1、存储空间满，会产生告警和日志，创建新的文件失败，对已存在文件操作正常，对已有文件写的内容超过存储空间时，写失败，有正确的告警和日志记录；"}]}}]}]}, {"label": "组件接口", "id": "03fe36c5418fb7dc", "children": [{"label": "通信接口", "id": "e56f99bbde3d9c98", "children": [{"id": "MCR_67", "label": "接收端缓存满", "type": 2, "attrs": {"description": "接收端缓存满", "steps": [{"step": "1、使用故障注入工具，朝接收端不断发送消息，使端口缓存占满，有预期结果1；", "expect": "1、端口缓存满，可以通过命令行查看缓存使用情况，会产生缓存满的告警和日志，会发送反压帧给上游；"}, {"step": "2、使用故障注入工具，减少消息发送，是端口缓存不断释放，有预期结果2；", "expect": "2、在消息变少后，缓存可以得到相应的释放，缓存告警恢复，停止发送后，缓存全部释放，没有内存泄漏；"}]}}, {"id": "MCR_68", "label": "发送端收到反压", "type": 2, "attrs": {"description": "发送端收到反压", "steps": [{"step": "1、使用故障注入工具，触发发送端不断发送消息，使接受端口缓存占满并发送反压帧给发送端，给发送有预期结果1；", "expect": "1、接收端口收到反压帧可以正确处理，减少流量发送，直至接收端不再发送反压帧；"}, {"step": ""}]}}]}, {"label": "消息接口", "id": "250a6c463fce7ffe", "children": [{"label": "MESH", "id": "cec69c472c3be1d9", "children": [{"id": "MCR_71", "label": "MESH-消息格式不符合约定", "type": 2, "attrs": {"description": "消息格式不符合约定", "steps": [{"step": "1、分析MESH消息格式，通过故障注入工具发送不符合约定的消息，比如字段大小、多少不一致等，有预期结果1；", "expect": "1、接收端能够检测消息格式的正确性，不符合格式的进行丢弃，有手段查看丢弃计数，有告警和日志记录报文丢弃及丢弃的原因。"}]}}, {"id": "MCR_72", "label": "MESH-发失败", "type": 2, "attrs": {"description": "发失败", "steps": [{"step": "1、通过故障注入，注入消息发送失败故障，有预期结果1；", "expect": "1、消息发送失败的场景下，有重传机制，消息会重新发送，有日志和统计，记录消息发送失败事件。"}]}}, {"id": "MCR_73", "label": "MESH-发超时", "type": 2, "attrs": {"description": "发超时", "steps": [{"step": "1、通过故障注入，注入消息发送超时（发送队列堆积或函数处理慢、消息未及时得到调度等），有预期结果1；", "expect": "1、可以设置消息外发超时时间和重发次数，对发送超时的消息，到达超时时间会重新发送，有命令行可以查看超时消息，有日志记录消息发送超时事件；"}]}}, {"id": "MCR_74", "label": "MESH-发流控", "type": 2, "attrs": {"description": "发流控", "steps": [{"step": "1、通过故障注入工具，将发送端消息发送的速率超过接收端可以处理的速率，有预期结果1；", "expect": "1、接收端通过令牌控制方式，或反压帧方式，是发送端的流量能进行流量控制；"}, {"step": "2、流控方式解除，有预期结果2；", "expect": "2、流控模块会给发送端发送一个拥塞解除的消息，通知发端组件拥塞解除，可以继续发消息通信；"}]}}, {"id": "MCR_75", "label": "MESH-收过载", "type": 2, "attrs": {"description": "收过载", "steps": [{"step": "1、通过故障注入工具，将发送端消息发送的速率超过接收端可以处理的速率，有预期结果1；", "expect": "1、接收端通过令牌控制方式，或反压帧方式，是发送端的流量能进行流量控制，日志记录过载事件；"}, {"step": "2、流控方式解除，有预期结果2；", "expect": "2、流控模块会给发送端发送一个拥塞解除的消息，通知发端组件拥塞解除，可以继续发消息通信；"}]}}, {"id": "MCR_76", "label": "MESH-收错包", "type": 2, "attrs": {"description": "收错包", "steps": [{"step": "1、组件间通过X消息通信，通过故障注入工具 ，注入消息错包，有预期结果1", "expect": "1、错包丢弃，丢包可以查看，记录日志；"}]}}, {"id": "MCR_77", "label": "MESH-收乱序", "type": 2, "attrs": {"description": "收乱序", "steps": [{"step": "1、组件间通过消息通信，通过故障注入工具 ，注入消息乱序，有预期结果1", "expect": "1、接收乱序的报文，可以进行保序，乱序报文个数可以查看"}]}}, {"id": "MCR_78", "label": "MESH-收兼容的消息", "type": 2, "attrs": {"description": "收兼容的消息", "steps": [{"step": "1、收到消息版本号的兼容的消息，有预期结果1；", "expect": "1、可以正确处理版本兼容的消息；"}]}}]}, {"label": "PUB/SUB", "id": "d512e0e74ac6ee22", "children": [{"id": "MCR_80", "label": "PUB/SUB-消息格式不符合约定", "type": 2, "attrs": {"description": "消息格式不符合约定", "steps": [{"step": "1、分析MESH消息格式，通过故障注入工具发送不符合约定的消息，比如字段大小、多少不一致等，有预期结果1；", "expect": "1、接收端能够检测消息格式的正确性，不符合格式的进行丢弃，有手段查看丢弃计数，有告警和日志记录报文丢弃及丢弃的原因。"}]}}, {"id": "MCR_81", "label": "PUB/SUB-收过载", "type": 2, "attrs": {"description": "收过载", "steps": [{"step": "1、通过故障注入工具，将发送端消息发送的速率超过接收端可以处理的速率，有预期结果1；", "expect": "1、接收端通过令牌控制方式，或反压帧方式，是发送端的流量能进行流量控制，日志记录过载事件；"}, {"step": "2、流控方式解除，有预期结果2；", "expect": "2、流控模块会给发送端发送一个拥塞解除的消息，通知发端组件拥塞解除，可以继续发消息通信；"}]}}, {"id": "MCR_82", "label": "PUB/SUB-发失败", "type": 2, "attrs": {"description": "发失败", "steps": [{"step": "1、通过故障注入，注入消息发送失败故障，有预期结果1；", "expect": "1、消息发送失败的场景下，有重传机制，消息会重新发送，有日志和统计，记录消息发送失败事件。"}]}}, {"id": "MCR_83", "label": "PUB/SUB-收错包", "type": 2, "attrs": {"description": "收错包", "steps": [{"step": "1、组件间通过X消息通信，通过故障注入工具 ，注入消息错包，有预期结果1", "expect": "1、错包丢弃，丢包可以查看，记录日志；"}]}}, {"id": "MCR_84", "label": "PUB/SUB-收兼容的消息", "type": 2, "attrs": {"description": "收兼容的消息", "steps": [{"step": "1、收到消息版本号的兼容的消息，有预期结果1；", "expect": "1、可以正确处理版本兼容的消息；"}]}}]}, {"label": "CROS(RPC)", "id": "b0b573de3f9e3e79", "children": [{"id": "MCR_86", "label": "CROS(RPC)-消息格式不符合约定", "type": 2, "attrs": {"description": "消息格式不符合约定", "steps": [{"step": "1、分析MESH消息格式，通过故障注入工具发送不符合约定的消息，比如字段大小、多少不一致等，有预期结果1；", "expect": "1、接收端能够检测消息格式的正确性，不符合格式的进行丢弃，有手段查看丢弃计数，有告警和日志记录报文丢弃及丢弃的原因。"}]}}, {"id": "MCR_87", "label": "CROS(RPC)-发失败", "type": 2, "attrs": {"description": "发失败", "steps": [{"step": "1、通过故障注入，注入消息发送失败故障，有预期结果1；", "expect": "1、消息发送失败的场景下，有重传机制，消息会重新发送，有日志和统计，记录消息发送失败事件。"}]}}, {"id": "MCR_88", "label": "CROS(RPC)-发超时", "type": 2, "attrs": {"description": "发超时", "steps": [{"step": "1、通过故障注入，注入消息发送超时（发送队列堆积或函数处理慢、消息未及时得到调度等），有预期结果1；", "expect": "1、可以设置消息外发超时时间和重发次数，对发送超时的消息，到达超时时间会重新发送，有命令行可以查看超时消息，有日志记录消息发送超时事件；"}]}}, {"id": "MCR_89", "label": "CROS(RPC)-发流控", "type": 2, "attrs": {"description": "发流控", "steps": [{"step": "1、通过故障注入工具，将发送端消息发送的速率超过接收端可以处理的速率，有预期结果1；", "expect": "1、接收端通过令牌控制方式，或反压帧方式，是发送端的流量能进行流量控制；"}, {"step": "2、流控方式解除，有预期结果2；", "expect": "2、流控模块会给发送端发送一个拥塞解除的消息，通知发端组件拥塞解除，可以继续发消息通信；"}]}}, {"id": "MCR_90", "label": "CROS(RPC)-收过载", "type": 2, "attrs": {"description": "收过载", "steps": [{"step": "1、通过故障注入工具，将发送端消息发送的速率超过接收端可以处理的速率，有预期结果1；", "expect": "1、接收端通过令牌控制方式，或反压帧方式，是发送端的流量能进行流量控制，日志记录过载事件；"}, {"step": "2、流控方式解除，有预期结果2；", "expect": "2、流控模块会给发送端发送一个拥塞解除的消息，通知发端组件拥塞解除，可以继续发消息通信；"}]}}, {"id": "MCR_91", "label": "CROS(RPC)-收错包", "type": 2, "attrs": {"description": "收错包", "steps": [{"step": "1、组件间通过X消息通信，通过故障注入工具 ，注入消息错包，有预期结果1", "expect": "1、错包丢弃，丢包可以查看，记录日志；"}]}}, {"id": "MCR_92", "label": "CROS(RPC)-收乱序", "type": 2, "attrs": {"description": "收乱序", "steps": [{"step": "1、组件间通过消息通信，通过故障注入工具 ，注入消息乱序，有预期结果1", "expect": "1、接收乱序的报文，可以进行保序，乱序报文个数可以查看；"}]}}, {"id": "MCR_93", "label": "CROS(RPC)-收超时", "type": 2, "attrs": {"description": "收超时", "steps": [{"step": "1、通过故障注入，注入消息接收超时（发送队列堆积或函数处理慢、消息未及时得到调度等），有预期结果1；", "expect": "1、收消息超时失败，有error信息报错，可以有命令行查看统计并记录日志；对可靠连接，接收端收超时未收到该报文，会通知发端重传；"}]}}, {"id": "MCR_94", "label": "CROS(RPC)-收兼容的消息", "type": 2, "attrs": {"description": "收兼容的消息", "steps": [{"step": "1、收到消息版本号的兼容的消息，有预期结果1；", "expect": "1、可以正确处理版本兼容的消息；"}]}}]}]}, {"label": "函数接口", "id": "4a558b60d06ac88e", "children": [{"id": "MCR_97", "label": "参数异常-空参数", "type": 2, "attrs": {"description": "空参数", "steps": [{"step": "1、通过故障注入 方式，注入传递给函数的参数为空参数", "expect": "1、函数能进行非空判断，对空参数打印日志，返回错误；"}]}}, {"id": "MCR_98", "label": "参数异常-参数越界", "type": 2, "attrs": {"description": "参数越界", "steps": [{"step": "1、通过故障注入 方式，注入传递给函数的参数越界", "expect": "1、函数要对对入参数进行合法性校验，不导致内存越界，对参数越界打印日志和返回错误信息；"}]}}, {"id": "MCR_99", "label": "参数异常-参数格式不符合约定", "type": 2, "attrs": {"description": "参数格式不符合约定", "steps": [{"step": "1、通过故障注入 方式，注入传递给函数的参数格式不符合要求", "expect": "1、对参数格式做校验，对参数不符合规范的，打印日志和返回错误信息；"}]}}, {"id": "MCR_100", "label": "参数异常-长度与实际不符", "type": 2, "attrs": {"description": "长度与实际不符", "steps": [{"step": "1、通过故障注入 方式，注入传递给函数的参数长度与实际长度不符（大于或小于）", "expect": "1、对参数长度做校验，不导致数组或内存越界，对参数越界打印日志和返回错误信息；"}]}}, {"id": "MCR_102", "label": "API处理异常-依赖的外部API处理超时", "type": 2, "attrs": {"description": "依赖的外部API处理超时", "steps": [{"step": "1、同步请求的响应消息超时到达_成功响应，通过测试桩模拟周边组件延迟返回接口响应消息，有预期结果1；", "expect": "1、本地会话或协议不能挂死，记录日志和告警；"}, {"step": ""}]}}, {"id": "MCR_103", "label": "API处理异常-依赖的外部API处理返回错误", "type": 2, "attrs": {"description": "依赖的外部API处理返回错误", "steps": [{"step": "1、同步请求的响应消息超时到达_失败响应，通过测试桩模拟周边组件延迟返回接口响应消息，有预期结果1；", "expect": "1、本地会话或协议不能挂死，再次发起请求成，记录日志和告警；"}]}}, {"id": "MCR_104", "label": "API处理异常-调用过载", "type": 2, "attrs": {"description": "调用过载", "steps": [{"step": "1、通过故障注入 方式，注入调用过载；", "expect": "1、能检测到调用过载事件，并记录日志，根据架构规则做响应的处理"}]}}]}, {"label": "数据接口", "id": "eb404e3ba237619e", "children": [{"label": "命令行", "id": "a95cbe2c7294aa12", "children": [{"id": "MCR_107", "label": "命令行-参数越界", "type": 2, "attrs": {"description": "命令行参数越界", "steps": [{"step": "1、通过故障注入 方式，注入传递命令行的参数越界", "expect": "1、命令行会做合法性校验，返回错误信息；"}]}}, {"id": "MCR_108", "label": "命令行-格式不符约定", "type": 2, "attrs": {"description": "格式不符约定", "steps": [{"step": "1、通过故障注入 方式，注入传递命令行格式不符合约定", "expect": "1、命令行会做合法性校验，返回错误信息；"}]}}, {"id": "MCR_109", "label": "命令行-模式错误", "type": 2, "attrs": {"description": "模式错误", "steps": [{"step": "1、通过故障注入 方式，注入传递命令行模式错误", "expect": "1、命令行会做合法性校验，返回错误信息；"}]}}, {"id": "MCR_110", "label": "命令行-命令行依赖不满足", "type": 2, "attrs": {"description": "命令行依赖不满足", "steps": [{"step": "1、通过故障注入 方式，注入，命令行依赖不满足", "expect": "1、命令行会做合法性校验，返回错误信息，提示需要先配置什么信息；"}]}}, {"id": "MCR_111", "label": "命令行-批量执行阻塞", "type": 2, "attrs": {"description": "批量执行阻塞", "steps": [{"step": "1、通过故障注入 方式，注入批量执行阻塞；", "expect": "1、能检测批量执行受阻事件，并记录日志，根据架构规则做响应的处理"}]}}]}, {"label": "YANG", "id": "a0d1b24bee9ea478", "children": [{"id": "MCR_113", "label": "YANG-参数越界", "type": 2, "attrs": {"description": "参数越界", "steps": [{"step": "1、通过故障注入 方式，注入yang的参数越界", "expect": "1、yang会做合法性校验，返回错误信息；"}]}}, {"id": "MCR_114", "label": "YANG-格式不符约定", "type": 2, "attrs": {"description": "格式不符约定", "steps": [{"step": "1、通过故障注入 方式，注入yang格式不符合约定", "expect": "1、yang会做合法性校验，返回错误信息；"}]}}, {"id": "MCR_115", "label": "YANG-消息过载", "type": 2, "attrs": {"description": "消息过载", "steps": [{"step": "1、通过故障注入 方式，注入yang消息过载；", "expect": "1、能检测到消息过载事件，并记录日志，根据架构规则做响应的处理"}]}}]}, {"label": "MIB", "id": "a12422ceda16ecd3", "children": [{"id": "MCR_117", "label": "MIB-参数越界", "type": 2, "attrs": {"description": "参数越界", "steps": [{"step": "1、通过故障注入 方式，注入MIB的参数越界", "expect": "1、MIB会做合法性校验，返回错误信息；"}]}}, {"id": "MCR_118", "label": "MIB-格式不符约定", "type": 2, "attrs": {"description": "格式不符约定", "steps": [{"step": "1、通过故障注入 方式，注入MIB格式不符合约定", "expect": "1、MIB会做合法性校验，返回错误信息；"}]}}, {"id": "MCR_119", "label": "MIB-消息过载", "type": 2, "attrs": {"description": "消息过载", "steps": [{"step": "1、通过故障注入 方式，注入MIB消息过载；", "expect": "1、能检测到消息过载事件，并记录日志，根据架构规则做响应的处理"}]}}]}, {"label": "配置文件", "id": "fc1b7df04b2c6302", "children": [{"id": "MCR_122", "label": "配置文件-参数值越界", "type": 2, "attrs": {"description": "参数值越界", "steps": [{"step": "1、通过故障注入 方式，注入配置文件的参数越界", "expect": "1、做合法性校验，返回错误信息；"}]}}, {"id": "MCR_121", "label": "配置文件-格式不符约定", "type": 2, "attrs": {"description": "格式不符约定", "steps": [{"step": "1、通过故障注入 方式，注入配置文件格式不符合约定", "expect": "1、做合法性校验，返回错误信息；"}]}}, {"id": "MCR_123", "label": "配置文件-数据完整性校验失败", "type": 2, "attrs": {"description": "数据完整性校验失败", "steps": [{"step": "1、通过故障注入 方式，注入配置文件完整性校验失败故障", "expect": "1、下载配置文件时会对配置文件进行完整性校验，对校验失败的配置文件，进行报错，并下载不成功"}]}}, {"id": "MCR_124", "label": "配置文件-读写权限错误", "type": 2, "attrs": {"description": "读写权限错误", "steps": [{"step": "1、用户无读配置文件的权限，该用户启动进程去读配置文件，有预期结果1；", "expect": "1、不能读取配置文件，如果进程启动时需要读配置文件但无权限读，进程启动失败，有错误信息打印，记录日志；"}, {"step": "2、用户无写配置文件权限，进行写配置文件，有预期结果2；", "expect": "2、写配置文件失败，有错误信息提示，记录日志；"}]}}]}]}]}, {"label": "组件基础环境", "id": "9aa97be409faf5fc", "children": [{"label": "中间件", "id": "e5295fb69a0ca202", "children": [{"label": "DB", "id": "6ac19c303768c7e1", "children": [{"id": "MCR_127", "label": "DB-进程主动重启", "type": 2, "attrs": {"description": "进程重启", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、使用组件cli主动重启组件(或kill、重新启动)，有预期结果1", "expect": "1、被测组件能力处理DB重启的事件，已有业务不受影响"}]}}, {"id": "MCR_126", "label": "DB-进程被动重启", "type": 2, "attrs": {"description": "进程重启", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、强制中断进程（如kill -9)，有预期结果1", "expect": "1、被测组件能力处理DB重启的事件，已有业务不受影响"}]}}, {"id": "MCR_128", "label": "DB-进程挂死", "type": 2, "attrs": {"description": "进程挂死", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具 ，注入组件进程挂起故障，有预期结果1", "expect": "1、DB挂起不会导致被测组件退出，已有业务保持"}]}}, {"id": "MCR_129", "label": "DB-读数据失败", "type": 2, "attrs": {"description": "读数据失败", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具或者软件函数注入，构造DB数据组件无法读故障，有预期结果1", "expect": "1、被测组件能力处理DB的接口失败，已有业务不受影响，进程不会异常退出，并记录读DB失败日志"}]}}, {"id": "MCR_130", "label": "DB-写数据失败", "type": 2, "attrs": {"description": "写数据失败", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具或者软件函数注入，构造DB数据组件无法写故障，有预期结果1", "expect": "1、被测组件能力处理DB的接口失败，已有业务不受影响，进程不会异常退出，并记录写DB失败日志"}]}}]}, {"label": "DOPHI", "id": "fcb4fe799408cbd0", "children": [{"id": "MCR_132", "label": "DOPHI-进程重启", "type": 2, "attrs": {"description": "进程重启", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、使用组件cli主动重启组件(或kill、重新启动)，有预期结果1", "expect": "1、被测组件能力处理DOPHI重启的事件，已有业务不受影响"}]}}, {"id": "MCR_133", "label": "DOPHI-进程挂死", "type": 2, "attrs": {"description": "进程挂死", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具 ，注入组件进程挂起故障，有预期结果1", "expect": "1、DOPHI挂起不会导致被测组件退出，已有业务保持"}]}}, {"id": "MCR_134", "label": "DOPHI-队列反压", "type": 2, "attrs": {"description": "队列反压", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "1、被测试组件发送消息速率大于DOPHI处理能力，有预期结果1；", "expect": "1、DOPHI发送一个反压信息给被测试组件，被测试组件进行流控，DOPHI如果有缓存队列，会把超出部分先放缓存队列，根据接收端能处理的速率进行调度，缓存满了后再进行丢包；"}]}}]}, {"label": "DOCUE", "id": "a20d94f09d2758fd", "children": [{"id": "MCR_136", "label": "DOCUE-进程重启", "type": 2, "attrs": {"description": "进程重启", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、使用组件cli主动重启组件(或kill、重新启动)，有预期结果1", "expect": "1、被测组件能力处理DOCUE重启的事件，已有业务不受影响"}]}}, {"id": "MCR_137", "label": "DOCUE-进程挂死", "type": 2, "attrs": {"description": "进程挂死", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具 ，注入组件进程挂起故障，有预期结果1", "expect": "1、DOCUE挂起不会导致被测组件退出，已有业务保持"}]}}]}]}, {"label": "监控诊断", "id": "e75fe2601f3da511", "children": [{"label": "日志服务", "id": "52c1e39026e0f6cd", "children": [{"id": "MCR_139", "label": "日志-进程重启", "type": 2, "attrs": {"description": "进程重启", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、使用组件cli主动重启组件(或kill、重新启动)，有预期结果1", "expect": "1、被测组件能力处理日志重启的事件，已有业务不受影响"}]}}, {"id": "MCR_140", "label": "日志-进程挂死", "type": 2, "attrs": {"description": "进程挂死", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具 ，注入组件进程挂起故障，有预期结果1", "expect": "1、日志挂起不会导致被测组件退出，已有业务保持"}]}}, {"id": "MCR_141", "label": "日志-写失败", "type": 2, "attrs": {"description": "写失败", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具 ，注入写日志失败，有预期结果1", "expect": "1、被测试组件写失败，有写日志失败告警，被测试组件能处理日志写失败的事件，已有业务不受影响；"}]}}, {"id": "MCR_142", "label": "日志-存储空间满", "type": 2, "attrs": {"description": "存储空间满", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具 ，构造存储空间满，产生新的日志需要记录的场景，有预期结果1 ；", "expect": "1、能对日志内容进行压缩，存储满时，对日志摸了中时间戳最老的压缩文件进行删除。被测试组件已有业务不受影响，可以正常记录日志；"}]}}, {"id": "MCR_143", "label": "日志-海量日志检测", "type": 2, "attrs": {"description": "海量日志检测", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具 ，被测试组件朝日志进程发送海量日志（1s上报2条，1小时50条定义为海量日志，鹰眼上报海量日志错误），有预期结果1", "expect": "1、日志能检测出海量日志，并能抑制，被测试组件能处理日志抑制事件，已有业务不受影响；"}]}}, {"id": "MCR_144", "label": "日志-ERROR日志检测", "type": 2, "attrs": {"description": "ERROR日志检测", "steps": [{"step": "1、准备被测组件基础业务（或功能）", "expect": "1、日志进程记录error日志，被测试组件能处理error事件，已有业务不受影响；"}, {"step": "2、通过故障注入工具 ，被测试进程朝日志进程记录error日志；"}]}}]}, {"label": "SAID", "id": "dc8ac88613ab646e", "children": []}]}]}, {"label": "组件公共能力", "id": "8d2cce4674b5f02a", "children": [{"label": "HA", "id": "5d7cf4971707adf6", "children": [{"id": "MCR_146", "label": "HA-进程重启", "type": 2, "attrs": {"description": "进程重启", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、使用组件cli主动重启组件(或kill、重新启动)，有预期结果1", "expect": "1、被测组件能力处理HA重启的事件，已有业务不受影响"}]}}, {"id": "MCR_147", "label": "HA-进程挂死", "type": 2, "attrs": {"description": "进程挂死", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具 ，注入HA组件进程挂起故障，有预期结果1", "expect": "1、HA挂起不会导致被测组件退出，已有业务保持"}]}}, {"id": "MCR_148", "label": "HA-状态迁移", "type": 2, "attrs": {"description": "HA状态迁移", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具，注入HA状态迁移，有预期结果1", "expect": "1、被测试组件可以正确处理HA状态迁移事件，已有业务不受影响"}]}}, {"id": "MCR_149", "label": "HA-状态震荡", "type": 2, "attrs": {"description": "HA状态震荡", "steps": [{"step": "1、准备被测组件基础业务（或功能）"}, {"step": "2、通过故障注入工具，注入HA状态震荡，有预期结果1", "expect": "1、被测试组件可以正确处理HA状态震荡事件，已有业务不受影响"}]}}]}, {"label": "能力定制", "id": "04c8a9859018912c", "children": [{"id": "MCR_151", "label": "能力集加载失败", "type": 2, "attrs": {"description": "能力集加载失败", "steps": [{"step": "1、通过故障注入工具 ，注入能力集加载失败，有预期结果1", "expect": "1、能力集加载失败有告警和日志记录，无资源残留，再次下载可以成功"}]}}, {"id": "MCR_152", "label": "能力集动态刷新", "type": 2, "attrs": {"description": "能力集动态刷新", "steps": [{"step": "1、通过故障注入工具 ，注入能力集动态刷新，有预期结果1", "expect": "1、检测出能力集动态刷新事件，并记录日志，根据架构规则做对应的处理。"}]}}]}, {"label": "平滑对账", "id": "e726f39260216982", "children": [{"id": "MCR_157", "label": "平滑-传输通道建立失败", "type": 2, "attrs": {"description": "平滑传输通道建立失败", "steps": [{"step": "1、构造平滑传输通道打开失败故障（如对端不回应），有预期结果1；", "expect": "1、有平滑管道打开失败故障告警或日志记录；"}, {"step": "2、构造持续平滑管道打开失败故障，持续xx时间（x分钟以上，触发自愈的时间，如5分钟），有预期结果2；", "expect": "2、长时间打开失败则复位进程恢复触发自愈；"}, {"step": "3、平滑管道可以正常建立，有预期结果3。", "expect": "3、告警消除或日志记录故障消除，业务无异常。"}]}}, {"id": "MCR_158", "label": "平滑-传输通道中断", "type": 2, "attrs": {"description": "平滑传输通道建立失败", "steps": [{"step": "1、在平滑过程中触发传输通道中断，有预期结果1；", "expect": "1、有平滑管道中断故障告警或日志记录；"}, {"step": "2、构造持续平滑管道中断故障，持续xx时间（x分钟以上，触发自愈的时间，如5分钟），有预期结果2；", "expect": "2、长时间中断败则复位进程恢复触发自愈；"}, {"step": "3、平滑管道中断后恢复，有预期结果3。", "expect": "3、告警消除或日志记录故障消除，业务无异常。"}]}}, {"id": "MCR_159", "label": "平滑-过程中消息随机丢失", "type": 2, "attrs": {"description": "平滑过程中消息随机丢失", "steps": [{"step": ""}, {"step": "1、（韧性）构造xx平滑消息延时300s以上（也可构造平滑通道拥塞）或持续丢弃xx平滑消息，有预期结果1；", "expect": "1、有平滑消息不可达故障告警或日志记录，连续一段时间(如5min)没收到平滑的数据，且也没收到平滑结束消息，则尝试再发起一轮平滑；"}, {"step": "2、（韧性）对xx业务撤销xx平滑消息故障，有预期结果2。", "expect": "2、告警消除或日志记录故障消除，业务无异常。"}]}}, {"id": "MCR_160", "label": "平滑-不结束", "type": 2, "attrs": {"description": "平滑不结束", "steps": [{"step": "1、触发平滑结束消息持续丢弃，触发平滑不结束持续x时间（如60s），有预期结果1；", "expect": "1、有平滑长时间不结束告警或日志记录，会尝试再发起一轮平滑；"}]}}, {"id": "MCR_161", "label": "平滑-处理失败", "type": 2, "attrs": {"description": "平滑处理失败", "steps": [{"step": ""}, {"step": "1、收到平滑处理失败消息，有预期结果1；", "expect": "1、能检测出平滑失败事件并记录日志；"}]}}, {"id": "MCR_163", "label": "对账-传输通道建立失败", "type": 2, "attrs": {"description": "对账传输通道建立失败", "steps": [{"step": "1、构造入对账传输通道打开失败故障（如对端不回应），有预期结果1；", "expect": "1、有对账管道打开失败故障告警或日志记录；"}, {"step": "2、构造持续对账管道打开失败故障，持续xx时间（5分钟以上），有预期结果2；", "expect": "2、长时间打开失败则复位进程恢复触发自愈；"}, {"step": "3、对账管道正常建立，有预期结果3。", "expect": "3、告警消除或日志记录故障消除，业务无异常。"}]}}, {"id": "MCR_164", "label": "对账-传输通道中断", "type": 2, "attrs": {"description": "对账传输通道中断", "steps": [{"step": "1、在对账过程中触发对账传输通道中断，有预期结果1；", "expect": "1、有对账通道中断故障告警或日志记录；"}, {"step": "2、构造持续对账管道中断故障，持续xx时间（x分钟以上，触发自愈的时间，如5分钟），有预期结果2；", "expect": "2、长时间中断败则复位进程恢复触发自愈；"}, {"step": "3、对账通道恢复正常，有预期结果3。", "expect": "3、告警消除或日志记录故障消除。"}]}}, {"id": "MCR_165", "label": "对账-过程中消息随机丢失", "type": 2, "attrs": {"description": "对账过程中消息随机丢失", "steps": [{"step": ""}, {"step": "1、（韧性）构造xx对账消息延时300s以上（也可构造对账通道拥塞）或持续丢弃xx平滑消息，有预期结果1；", "expect": "1、有对账消息不可达故障告警或日志记录，连续一段时间(如5min)没收到对账的数据，且也没收到对账结束消息，则尝试再发起一轮对账；"}, {"step": "2、（韧性）对xx业务撤销xx对账消息故障，有预期结果2。", "expect": "2、告警消除或日志记录故障消除，业务无异常。"}]}}, {"id": "MCR_166", "label": "对账-处理时间大于对账周期", "type": 2, "attrs": {"description": "对账处理时间大于对账周期", "steps": [{"step": "1、将一轮对账时间延长到触发下一次对账周期开始，比如对账周期是1小时一次，在新的对账开始时，上一次的对账消息还未处理完，有预期结果1；", "expect": "1、到对账周期开始新的一轮对账，停止上一轮的对账，资源释放，业务无异常"}]}}]}]}]}, {"id": "ed775c53f65d70d1", "label": "安全交互分析", "children": [{"label": "组件接口", "id": "03fe36c5418fb7dc", "children": [{"label": "函数接口", "id": "4a558b60d06ac88e", "children": [{"id": "MCS_1", "label": "函数接口Fuzz测试", "type": 2, "attrs": {"description": "函数接口Fuzz测试", "steps": [{"step": "1、对于xxx组件，使用Fuzz测试工具（如LibFuzzer/SecDIVE/AFL等）对函数API接口开展健壮性测试，并检查组件的运行状态;", "expect": "1、xxx组件无异常、断言、内存错误等告警，在tt时间内有攻击/故障告警或日志记录;"}]}}]}, {"label": "消息接口", "id": "250a6c463fce7ffe", "children": [{"id": "MCS_3", "label": "消息接口泛洪攻击防御有效性测试", "type": 2, "attrs": {"description": "防御消息接口泛洪攻击测试", "steps": [{"step": "1、对于xxx组件，按照xxx组件处理规格使用测试工具（xstream-dos、attacker、THC-IPv6、sockstress等）对组件发起单种xxx消息处理规格量的消息流量攻击，持续xxx分钟（建议5分钟以上）;", "expect": "1、xxx组件无异常、断言、内存错误等告警，在tt时间内有攻击/故障告警或日志记录，防DOS攻击机制默认使能，攻击过程中设备资源消耗可控（攻击时主控CPU上升值不超过10%，接口板CPU上升值不超过20%），;"}, {"step": "2、停止攻击行为x秒，软件系统自动恢复", "expect": "2、xxx组件无异常、断言、内存错误等告警，在tt时间内有告警消除或记录消除日志，CPU/内存等恢复到攻击前水平;"}]}}, {"id": "MCS_6", "label": "组件间接口开放权限最小化测试", "type": 2, "attrs": {"description": "加解密组件间接口开放权限最小化测试", "steps": [{"step": "1、对于xxx组件，登录组件Linux后台，查看根/主秘钥的生成采用的组件/进程，工作秘钥的加解密的组件及其对根秘钥的操作权限，检查其他组件对根密钥的操作权限;", "expect": "1、使用解耦的秘钥管理组件，根/主秘钥的生成采用独立的组件/进程，工作秘钥的加解密采用另外的组件且对根秘钥只有读操作权限，其他组件对根密钥无访问权限;"}]}}]}, {"label": "通信接口", "id": "e56f99bbde3d9c98", "children": [{"id": "MCS_10", "label": "人机接口具备认证机制测试", "type": 2, "attrs": {"description": "人机接口提供认证机制测试", "steps": [{"step": "1、对于xxx组件，对系统管理的人机接口（含物理接口/逻辑接口/合法监听），检查是否有认证机制（口令/公钥/证书/其他）;", "expect": "1、可对系统进行管理的人机接口支持认证（口令/公钥/证书/其他）并默认开启认证机制;"}, {"step": "2、对于xxx组件，对系统管理的人机接口（含物理接口/逻辑接口/合法监听），关闭该接口的认证机制;", "expect": "2、如果支持关闭系统人机接口的认证机制，应在CPI资料中提示风险;"}]}}, {"id": "MCS_11", "label": "机机接口具备认证机制测试", "type": 2, "attrs": {"description": "机机接口提供认证机制测试", "steps": [{"step": "1、对于xxx组件，对系统管理的机机接口（含物理接口/逻辑接口/升级接口），检查是否有认证机制;", "expect": "1、可对系统进行管理的机机接口支持认证并默认开启认证机制;"}, {"step": "2、对于xxx组件，对系统管理的机机接口（含物理接口/逻辑接口/升级接口），关闭该接口的认证机制;", "expect": "2、如果支持关闭系统机机接口的认证机制，应在CPI资料中提示风险;"}]}}, {"id": "MCS_15", "label": "接口服务端认证机制测试", "type": 2, "attrs": {"description": "服务端认证机制测试", "steps": [{"step": "1、对于xxx组件，使用拦截工具（如MITM）工具拦截一次认证请求，修改客户端发往服务端的请求报文，添加认证通过信息（需要根据产品特点添加对应的字段）（如果采用集中认证，那么对登录连接的最终认证就是放在集中认证服务端进行）;", "expect": "1、服务端无法通过认证，认证失败;"}]}}, {"id": "MCS_16", "label": "接口无自动登录机制测试", "type": 2, "attrs": {"description": "客户端无自动登录测试", "steps": [{"step": "1、对于xxx组件，组件作为客户端登录服务器，检查输入认证凭据后是否有“自动登录”、“记住我”、“记住密码”等提示;", "expect": "1、设备作为客户端不支持“自动登录”、“记住我”、“记住密码”等功能;"}, {"step": ""}]}}, {"id": "MCS_17", "label": "接口认证防绕过测试", "type": 2, "attrs": {"description": "接口认证防绕过测试", "steps": [{"step": "1、对于xxx组件，尝试使用组合键、鼠标特殊敲击、连接特定接口、使用特定客户端等方式绕过正常认证机制;", "expect": "1、系统无可绕过正常认证机制的隐秘访问方式;"}]}}, {"id": "MCS_18", "label": "Web应用防绕过攻击测试", "type": 2, "attrs": {"description": "Web应用防绕过攻击测试", "steps": [{"step": "1、对于xxx组件，梳理所有外部接接口，查看是否有认证机制（对于图标、CSS文件、静态页面或JavaScript等，如果公开后没有安全风险，不强制认证鉴权），使用busrpsuite或者postman构造报文发送;", "expect": "1、除图标、CSS文件、静态页面或JavaScript等不需要认证的接口外，其他所有的外部接口都有认证机制;"}, {"step": "2、对于xxx组件，获取产品源代码，查找URL认证鉴权模块，查看是否存在URL未使用全匹配，（如使用contains函数而未使用equal函数），使用简单的包含某个字符串的匹配算法，构造URL发送;（举例：不需要认证鉴权的请求（例如：图标、CSS文件、静态页面或JavaScript等），在需要认证的请求（如https://x.x.x.x/index.asp）前加入/1.CSS//看能否绕过认证，https://x.x.x.x/1.CSS///index.asp（本测试点主要目的是通过一些不需要认证的文件绕过认证机制，产品需要根据实际实现来进行设计，参考案例：http://3ms.huawei.com/hi/group/1007449/thread_4338623.html?mapId=3705787&for_statistic_from=all_group_forum））;", "expect": "2、接口认证不能被绕过;"}]}}, {"id": "MCS_20", "label": "接口防重放攻击测试", "type": 2, "attrs": {"description": "防重放攻击测试", "steps": [{"step": "1、对于xxx组件，使用拦截工具（如MITM）工具拦截一次完整的会话生成销毁过程并进行重放;", "expect": "1、重放报文无法通过认证（协议标准中不能抵御重放攻击的协议除外）;"}]}}, {"id": "MCS_21", "label": "接口防暴力破解攻击测试", "type": 2, "attrs": {"description": "防暴力破解攻击测试", "steps": [{"step": "1、对于xxx组件，使用暴力破解尝试获取合法认证凭据并通过认证;", "expect": "1、系统存在防止被暴力破解的机制并默认使能（例如登录延迟，锁定IP、账号，验证码等）;"}, {"step": "2、对于xxx组件，关闭防暴力破解功能;", "expect": "2、关闭防暴力破解功能有安全风险提示;"}]}}, {"id": "MCS_22", "label": "接口认证防绕过韧性测试", "type": 2, "attrs": {"description": "接口认证防绕过韧性测试", "steps": [{"step": "1、对于xxx组件，攻击者使用正确的认证相关凭据（例如账号、口令、证书、密钥）建立会话，并进行与日常维护操作不同的非常规操作;", "expect": "1、设备检测出此会话可能是非法会话，不影响xxx组件的其他会话，在tt时间内有攻击/故障告警或日志记录;"}, {"step": "2、对于xxx组件，攻击者断开会话;", "expect": "2、不影响xxx组件的其他会话，在tt时间内有告警消除或记录消除日志，消除后需要自适应识别此类攻击或提示用户修改认证机制提升安全防御能力;"}, {"step": "3、对于xxx组件，采用xx方法直接绕过认证机制;（当前无测试方法，暂不落地）;", "expect": "3、设备检测出此会话是非法会话，不影响xxx组件的其他会话，在tt时间内有攻击/故障告警或日志记录; "}]}}, {"id": "MCS_26", "label": "通信接口泛洪攻击防御有效性测试", "type": 2, "attrs": {"description": "防御通信接口泛洪攻击测试", "steps": [{"step": "1、对于xxx组件，按照xxx组件处理规格使用测试工具（xstream-dos、attacker、THC-IPv6、sockstress等）对组件发起单种xxx通信报文处理规格量的消息流量攻击，持续xxx分钟（建议5分钟以上）;", "expect": "1、xxx组件无异常、断言、内存错误等告警，在tt时间内有攻击/故障告警或日志记录，防DOS攻击机制默认使能，攻击过程中设备资源消耗可控（攻击时主控CPU上升值不超过10%，接口板CPU上升值不超过20%），;"}, {"step": "2、停止流量攻击，等待x秒", "expect": "2、xxx组件无异常、断言、内存错误等告警，在tt时间内有告警消除或记录消除日志，CPU/内存等恢复到攻击前水平;"}, {"step": ""}]}}]}, {"label": "数据接口", "id": "eb404e3ba237619e", "children": [{"label": "配置文件", "id": "fc1b7df04b2c6302", "children": [{"id": "MCS_30", "label": "配置文件接口Fuzz测试", "type": 2, "attrs": {"description": "配置文件接口Fuzz测试", "steps": [{"step": "1、对于xxx组件，使用Fuzz测试工具（如LibFuzzer、Peach等）对消费类文件接口开展健壮性测试，并检查组件的运行状态", "expect": "1、xxx组件无异常、断言、内存错误等告警，在tt时间内有攻击/故障告警或日志记录;"}]}}]}, {"label": "命令行", "id": "a95cbe2c7294aa12", "children": [{"id": "MCS_32", "label": "命令行接口Fuzz测试", "type": 2, "attrs": {"description": "命令行接口Fuzz测试", "steps": [{"step": "1、对于xxx组件，使用Fuzz测试工具（如LibFuzzer、Peach等）对命令行API接口健壮性测试，并检查组件的运行状态;", "expect": "1、xxx组件无异常、断言、内存错误等告警，在tt时间内有攻击/故障告警或日志记录;"}]}}]}, {"label": "YANG", "id": "a0d1b24bee9ea478", "children": [{"id": "MCS_34", "label": "YANG接口Fuzz测试", "type": 2, "attrs": {"description": "YANG接口Fuzz测试", "steps": [{"step": "1、对于xxx组件，使用Fuzz测试工具（如LibFuzzer、Peach等）对YANG API接口健壮性测试，并检查组件的运行状态;", "expect": "1、xxx组件无异常、断言、内存错误等告警，在tt时间内有攻击/故障告警或日志记录;"}]}}]}, {"label": "MIB", "id": "a12422ceda16ecd3", "children": [{"id": "MCS_36", "label": "MIB接口Fuzz测试", "type": 2, "attrs": {"description": "MIB接口Fuzz测试", "steps": [{"step": "1、对于xxx组件，使用Fuzz测试工具（如LibFuzzer、Peach等）对MIB API接口健壮性测试，并检查组件的运行状态;", "expect": "1、xxx组件无异常、断言、内存错误等告警，在tt时间内有攻击/故障告警或日志记录;"}]}}]}]}]}, {"label": "组件基础环境", "id": "9aa97be409faf5fc", "children": [{"label": "中间件", "id": "e5295fb69a0ca202", "children": [{"label": "DB", "id": "6ac19c303768c7e1", "children": [{"id": "MCS_40", "label": "数据库操作审计日志测试", "type": 2, "attrs": {"description": "数据库操作记录日志测试", "steps": [{"step": "1、对于xxx数据库，执行数据库的增删改操作;", "expect": "1、日志中有日志记录，并记录安全日志;"}]}}, {"id": "MCS_41", "label": "数据库默认安全测试", "type": 2, "attrs": {"description": "数据库默认安全测试", "steps": [{"step": "1、使用公司安全测试云已提供的SecureCAT工具（参考指导书：http://3ms.huawei.com/hi/group/2034125/wiki_5911668.html）开展数据库配置安全扫描;", "expect": "1、所有的疑似告警均有分析并闭环，禁止生产环境（自运营现网环境）中存在超出修复时间要求的、可利用的已知高危漏洞;"}]}}, {"id": "MCS_42", "label": "数据库已知漏洞发现测试", "type": 2, "attrs": {"description": "数据库已知漏洞发现测试", "steps": [{"step": "1、使用公司安全测试云已提供的xSecure-DBScan工具(参考指导书：http://3ms.huawei.com/hi/group/2034125/wiki_5942082.html)开展数据库扫描;", "expect": "1、所有的疑似告警均有分析并闭环，禁止生产环境（自运营现网环境）中存在超出修复时间要求的、可利用的已知高危漏洞;"}]}}]}, {"label": "DOPHI", "id": "fcb4fe799408cbd0", "children": []}, {"label": "DOCUE", "id": "a20d94f09d2758fd", "children": []}]}, {"label": "监控诊断", "id": "e75fe2601f3da511", "children": [{"label": "SAID", "id": "dc8ac88613ab646e", "children": []}, {"label": "日志服务", "id": "52c1e39026e0f6cd", "children": [{"id": "MCS_48", "label": "用户活动审计日志测试", "type": 2, "attrs": {"description": "用户活动记录日志测试", "steps": [{"step": "1、对于xxx组件，分别执行用户会话登录和注销;", "expect": "1、日志中有日志记录，并记录安全日志;"}, {"step": "2、对于xxx组件，分别执行增加、删除用户和用户属性（帐号、口令等）的变更;", "expect": "2、日志中有日志记录，并记录安全日志;"}, {"step": "3、对于xxx组件，分别执行用户的锁定和解锁，禁用和恢复;", "expect": "3、日志中有日志记录，并记录安全日志;"}, {"step": "4、对于xxx组件，分别执行角色权限变更;", "expect": "4、日志中有日志记录，并记录安全日志;"}, {"step": "5、对于xxx组件，分别执行系统相关安全配置（如安全日志内容配置、审计功能的打开与关闭）的变更;", "expect": "5、日志中有日志记录，并记录安全日志;"}, {"step": "6、对于xxx组件，分别执行重要资源的变更，如某个重要文件（日志文件、证书文件等）的复制、删除等;", "expect": "6、日志中有日志记录，并记录安全日志;"}]}}, {"id": "MCS_49", "label": "操作审计日志测试", "type": 2, "attrs": {"description": "操作指令记录日志测试", "steps": [{"step": "1、对于xxx组件，分别执行对系统配置参数的修改;", "expect": "1、日志中有日志记录，并记录安全日志;"}, {"step": "2、对于xxx组件，分别执行对系统进行启动、关闭、重启、暂停、恢复、倒换;", "expect": "2、日志中有日志记录，并记录安全日志;"}, {"step": "3、对于xxx组件，分别执行对业务的加载、卸载;", "expect": "3、日志中有日志记录，并记录安全日志;"}, {"step": "4、对于xxx组件，分别执行软件的升级操作，包括远程升级和本地升级;", "expect": "4、日志中有日志记录，并记录安全日志;"}, {"step": "5、对于xxx组件，分别执行对重要业务数据（特别是与财务相关的数据，包括：卡号、余额、话单、费率、费用、订单、出货、帐单等）的创建、删除、修改;", "expect": "5、日志中有日志记录，并记录安全日志;"}, {"step": "6、对于xxx组件，分别执行所有帐户的命令行非查询操作命令;", "expect": "6、日志中有日志记录，并记录安全日志;"}, {"step": "7、对于xxx组件，上传/导入文件、下载/导出包含敏感信息的文件及对文件的远程删除操作（如：使用FTP/SFTP）。", "expect": "7、日志中有日志记录，并记录安全日志;"}, {"step": "8、对于只能本地接入的BIOS以及移动终端进行以上操作指令;", "expect": "8、不强制记录日志;"}]}}, {"id": "MCS_51", "label": "审计日志包含完整的关键内容测试", "type": 2, "attrs": {"description": "审计日志记录内容完整测试", "steps": [{"step": "1、对于xxx业务，执行用户活动或操作，获取日志记录并检查日志内容;", "expect": "1、日志记录至少包括事件发生的时间、用户ID、访问发起端地址或标识、事件类型、被访问的资源名称、事件的结果（成功or失败）;"}, {"step": "2、对于xxx业务，进行非法会话请求，获取安全日志并检查日志内容;", "expect": "2、日志记录至少包含非法会话请求发起者的IP、客户端、时间等信息;"}, {"step": ""}]}}, {"id": "MCS_52", "label": "审计日志包含正确的关键内容测试", "type": 2, "attrs": {"description": "审计日志记录的IP防伪造测试", "steps": [{"step": "1、对于xxx组件，使用Burpsuite拦截登录报文，在报文头部中增加X-forward-for：*******,或者WL-Proxy-Client-IP：*******或者Proxy-Client-IP：*******或者x-real-ip：*******发送，查看登录日志;", "expect": "1、登录日志记录的IP为客户端真实IP;"}, {"step": "2、对于xxx组件，使用Burpsuite拦截登录报文，在报文中修改和操作用户ID相关的报文字段，查查登录日志;", "expect": "2、用户ID记录正确;"}, {"step": "3、对于xxx组件，使用Burpsuite拦截登录报文，在报文中修改和事件类型相关的报文字段，查查登录日志;", "expect": "3、时间记录正确;"}, {"step": "4、对于xxx组件，使用Burpsuite拦截登录报文，在报文中修改和访问结果相关的报文字段，查查登录日志;", "expect": "4、事件类型记录正确;"}, {"step": "5、对于xxx组件，使用Burpsuite拦截登录报文，在报文中修改和时间相关的报文字段，查查登录日志;", "expect": "5、被访问资源的名称记录正确;"}, {"step": "6、对于xxx组件，使用Burpsuite拦截登录报文，在报文中修改被访问资源名称字段，查查登录日志;", "expect": "6、访问结果记录正确;"}, {"step": ""}]}}, {"id": "MCS_53", "label": "审计日志记录的内容正确测试", "type": 2, "attrs": {"description": "审计日志记录的内容正确测试", "steps": [{"step": "1、对于xxx组件，执行用户活动或操作，获取日志记录并检查日志内容;", "expect": "1、日志记录的包括事件发生的时间、用户ID、访问发起端地址或标识、事件类型、被访问的资源名称、事件的结果等相关内容记录正确，且日志中没有敏感信息或做脱敏处理;"}, {"step": "2、对于xxx组件，进行非法会话请求，获取安全日志并检查日志内容;", "expect": "2、日志记录的包含非法会话请求发起者的IP、客户端、时间等相关内容记录正确;"}, {"step": "3、对于xxx组件，执行超时退出与主动注销，获取安全日志并检查日志内容;", "expect": "3、超时退出与主动注销日志内容不同"}]}}]}]}, {"label": "转发服务", "id": "c8cf16284a032c07", "children": []}]}, {"label": "组件公共能力", "id": "8d2cce4674b5f02a", "children": [{"label": "启动", "id": "4e231c9cfa770bd4", "children": [{"id": "MCS_57", "label": "组件启动篡改文件测试", "type": 2, "attrs": {"description": "组件启动篡改文件测试", "steps": [{"step": "1、对于xxx组件，使用正常发布的RPM包进行启动操作;", "expect": "1、设备对各类程序文件头部、内容及签名段完成完整性校验，启动成功;"}, {"step": "2、对于xxx组件，采用离线方式，任意修改组件使用的程序文件任意字节，以此程序文件启动;", "expect": "2、程序启动失败"}, {"step": ""}]}}]}, {"label": "重启", "id": "cf5d85e32fef6abd", "children": [{"id": "MCS_59", "label": "组件重启篡改文件测试", "type": 2, "attrs": {"description": "组件重启篡改文件测试", "steps": [{"step": "1、对于xxx组件，采用离线方式，任意修改组件使用的程序文件任意字节，重启改程序", "expect": "1、程序启动失败"}, {"step": ""}]}}]}, {"label": "升级", "id": "61e1fb8c914cc4b9", "children": [{"id": "MCS_61", "label": "组件升级篡改文件测试", "type": 2, "attrs": {"description": "组件升级篡改文件测试", "steps": [{"step": "1、对于xxx组件，安装升级的RPM，修改新RPM任意的程序文件任意字节，以此程序文件启动;", "expect": "1、程序启动失败"}, {"step": ""}]}}]}, {"label": "平滑对账", "id": "e726f39260216982", "children": []}, {"label": "能力定制", "id": "04c8a9859018912c", "children": []}, {"label": "HA", "id": "5d7cf4971707adf6", "children": []}, {"label": "多实例", "id": "17390d26cd535259", "children": []}, {"label": "分布式", "id": "e91a6bff72168511", "children": []}, {"label": "补丁", "id": "14d4be4da55e8c9d", "children": []}]}, {"label": "组件运行环境（OS)", "id": "ac1b2c9991bb1c95", "children": [{"label": "进程", "id": "69ee231ac97bbc86", "children": [{"id": "MCS_71", "label": "进程间通信机制测试", "type": 2, "attrs": {"description": "进程间通信白名单机制测试", "steps": [{"step": "1、对于xxx组件，检查进程间通信规则配置文件;", "expect": "1、进程间通信具备白名单机制，配置文件有进程白名单列表;"}, {"step": "2、对于xxx组件，使用桩组件模拟命令行触发非白名单组件消息;", "expect": "2、进程间无法通信，返回失败，并记录失败日志;"}]}}, {"id": "MCS_72", "label": "进程基于风险等级的隔离测试", "type": 2, "attrs": {"description": "进程基于风险等级的隔离测试", "steps": [{"step": "1、对于xxx组件，依据设计提供对外提供网络服务所涉及功能模块的风险等级标识和归属进程，检查该业务所在进程承载业务的高/低风险模块分类;", "expect": "1、组件所在进程承载业务，不存在高/低风险模块同时包含的情况;"}]}}, {"id": "MCS_73", "label": "进程基于网络平面的隔离测试", "type": 2, "attrs": {"description": "进程基于网络平面的隔离测试", "steps": [{"step": "1、对于xxx组件，依据设计提供产品通讯矩阵中服务端口所属平面和所属进程，对于所属平面检查端口对应网络服务是否绑定多个平面的IP地址;", "expect": "1、端口对应网络服务未绑定多个平面的IP地址;"}, {"step": "2、对于所属针进程，检查进程所承载的外部服务，是否涉及多个平面;", "expect": "2、进程所承载的外部服务不涉及多个平面;"}]}}, {"id": "MCS_74", "label": "进程基于网络特权的隔离测试", "type": 2, "attrs": {"description": "进程基于网络特权的隔离测试", "steps": [{"step": "1、对于xxx组件，检查版本进程设计文档中对于进程所属功能模块的描述;", "expect": "1、对外提供网络服务的进程，只包含网络服务功能模块，不包含其他功能模块;"}, {"step": "2、通过ps命令和cat /proc/[pid]/status | grep Cap命令，查看系统中所有对外提供网络服务进程的Capability信息;", "expect": "2、系统中对外提供网络服务进程的Capability能力与版本进程设计文档一致，且Capability能力限制在网络相关特权范围内;"}]}}, {"id": "MCS_75", "label": "默认进程DAC属主最小化测试", "type": 2, "attrs": {"description": "默认进程DAC属主最小化测试", "steps": [{"step": "1、对于xxx组件，登录组件后台Linux，检查产品业务进程的UID;", "expect": "1、产品该组件进程的UID与版本进程设计文档一致，且不同网络服务进程具有不同的UID（除管理面、内核模块、驱动进程外，其他平面的进程使用非root用户运行）;"}, {"step": "2、对于xxx组件，针对版本进程设计文档中以root权限运行的网络服务进程，检查设计文档中对于高权限需求生命周期划分，高权限需求生命周期结束后，登录组件后台Linux，检查产品业务进程的UID;", "expect": "2、高权限需求生命周期结束后，进程属主切换为非root用户;"}]}}, {"id": "MCS_76", "label": "动态拉起进程DAC属主最小化测试", "type": 2, "attrs": {"description": "动态拉起进程DAC属主最小化测试", "steps": [{"step": "1、对于xxx组件，完成业务配置以拉起业务进程，登录组件后台Linux，检查产品业务进程的UID;", "expect": "1、产品该组件进程的UID与版本进程设计文档一致，且不同网络服务进程具有不同的UID（除管理面、内核模块、驱动进程外，其他平面的进程使用非root用户运行）;"}, {"step": "2、对于xxx组件，针对版本进程设计文档中以root权限运行的网络服务进程，检查设计文档中对于高权限需求生命周期划分，高权限需求生命周期结束后，登录组件后台Linux，检查产品业务进程的UID;", "expect": "2、高权限需求生命周期结束后，进程属主切换为非root用户;"}]}}, {"id": "MCS_77", "label": "默认进程Capability最小化测试", "type": 2, "attrs": {"description": "默认进程Capability最小化测试", "steps": [{"step": "1、对于xxx组件，检查版本进程设计文档的capability特权相关字段，登录组件Linux后台，通过ps命令和cat /proc/[pid]/status | grep Cap命令，查询系统进程的capability能力;", "expect": "1、文档中有capability能力的声明部分，且其所对应的权限主张均有相关功能应用说明，系统进程的capability能力和版本进程设计文档中一致;"}]}}, {"id": "MCS_78", "label": "动态拉起进程Capability最小化测试", "type": 2, "attrs": {"description": "动态拉起进程Capability最小化测试", "steps": [{"step": "1、对于xxx组件，完成业务配置以拉起业务进程，检查版本进程设计文档的capability特权相关字段，登录组件Linux后台，通过ps命令和cat /proc/[pid]/status | grep Cap命令，查询系统进程的capability能力;", "expect": "1、文档中有capability能力的声明部分，且其所对应的权限主张均有相关功能应用说明，系统进程的capability能力和版本进程设计文档中一致;"}]}}, {"id": "MCS_79", "label": "进程DAC属主设定遵从性测试", "type": 2, "attrs": {"description": "进程DAC属主设定测试", "steps": [{"step": "1、对于xxx组件，登录组件后台Linux，检查产品业务进程的UID;", "expect": "1、组件进程的UID为有效UID;"}]}}, {"id": "MCS_80", "label": "进程Capability设定遵从性测试", "type": 2, "attrs": {"description": "进程Capability设定测试", "steps": [{"step": "1、对于xxx组件，检查版本进程设计文档的capability特权相关字段，登录组件Linux后台，通过ps命令和cat /proc/[pid]/status | grep Cap命令，查询系统进程的capability能力;", "expect": "1、查询回显包含capability字段;"}]}}]}, {"label": "线程", "id": "3ae03a2a571acd43", "children": []}, {"label": "信号量", "id": "fd996550f28859e9", "children": []}, {"label": "内存", "id": "ca97042293389e12", "children": [{"id": "MCS_84", "label": "内存访问安全读写测试", "type": 2, "attrs": {"description": "内存访问安全读写测试", "steps": [{"step": "1、对于xxx组件，登录组件查询命令行树并使用关键字进行关联筛选（如：display cli command-tree | include set | include HEX | include register），检查是否有直接指定任意寄存器或者内存地址的方式去读取或修改寄存器或内存的命令;", "expect": "1、产品中不存在直接指定任意寄存器或者内存地址的方式去读取或修改寄存器或内存的命令;"}]}}, {"id": "MCS_85", "label": "安全编译选项堆保护测试", "type": 2, "attrs": {"description": "安全编译选项堆保护测试", "steps": [{"step": "1、针对产品使用glibc堆内存管理器的，对于xxx组件，登录组件操作系统后台，查询glibc的版本（参考查询命令：ldd --version），检查glibc的版本达到2.10及以上;", "expect": "1、glibc的版本达到2.10及以上;"}, {"step": "2、针对产品使用Dopra的堆内存管理机制的，对于xxx组件，登陆组件，查询进程所有分区使用的内存分配算法是否为HIM（参考VRPV8诊断命令：display inspect process [ProcID] memory brief [PtNo]。其中ProcID为进程pid，获取方式 display ha process running-state；PtNo为分片号，获取方式 display inspect process [ProcID] memory summary），检查内存分配算法是否为HIM;", "expect": "2、所有分区使用的内存分配算法为HIM;"}]}}, {"id": "MCS_86", "label": "共享内存敏感信息安全传输测试", "type": 2, "attrs": {"description": "共享内存敏感信息安全传输测试", "steps": [{"step": "1、对于xxx组件，检查通过共享内存等方式直接进行资源共享的各进程的权限设置;", "expect": "1、不同权限进程间不允许通过共享内存等方式直接进行资源共享;"}, {"step": "2、对于xxx组件，检查通过共享内存等方式进行资源共享的敏感数据文件的权限设置;", "expect": "2、共享内存的内容不包含明文的敏感数据,且敏感数据文件有权限控制，文件权限最小化;"}]}}]}, {"label": "CPU", "id": "c646ac61543d152d", "children": []}, {"label": "时间", "id": "36dfede5f4ec53e8", "children": []}, {"label": "SeLinux", "id": "c27b7b8c3bd1c258", "children": [{"id": "MCS_90", "label": "默认进程SELinux权限最小化测试", "type": 2, "attrs": {"description": "默认进程SELinux权限最小化测试", "steps": [{"step": "1、对于xxx组件，登录组件Linux后台，下发命令ps -auxZ | grep <processName> （processName为SELinux策略集中的进程名）查看进程标签并与产品设计输出的SELinux标准策略集进行比对（参考工具http://3ms.huawei.com/hi/group/3195259/wiki_6175774.html）;", "expect": "1、组件进程SELinux标签与SELinux标准策略集中的标签一致;"}, {"step": "2、对于xxx组件，登录组件Linux后台，下发命令sesearch -s <process_label> -t  <file_label> --allow （process_label为进程标签；file_label为文件标签）查看进程访问权限并与产品设计输出的SELinux标准策略集比对（参考工具http://3ms.huawei.com/hi/group/3195259/wiki_6175774.html）;", "expect": "2、组件进程SELinux访问权限与SELinux标准策略集中的访问权限一致;"}]}}, {"id": "MCS_91", "label": "进程SELinux权限设定遵从性测试", "type": 2, "attrs": {"description": "进程SELinux权限设定测试", "steps": [{"step": "1、对于xxx组件，登录组件Linux后台，下发命令ps -auxZ 查看进程标签;", "expect": "1、组件进程均有SELinux标签;"}, {"step": "2、对于xxx组件，登录组件Linux后台，下发命令sesearch -s <process_label> --allow （process_label为进程标签）查看进程访问权限;", "expect": "2、组件进程SELinux访问权限返回值非空;"}]}}, {"id": "MCS_92", "label": "动态拉起进程SELinux权限最小化测试", "type": 2, "attrs": {"description": "动态拉起进程SELinux权限最小化测试", "steps": [{"step": "1、对于xxx组件，完成业务配置以拉起业务进程，登录组件Linux后台，下发命令ps -auxZ | grep <processName> （processName为SELinux策略集中的进程名）查看进程标签并与产品设计输出的SELinux标准策略集进行比对（参考工具http://3ms.huawei.com/hi/group/3195259/wiki_6175774.html）;", "expect": "1、组件进程SELinux标签与SELinux标准策略集中的标签一致;"}, {"step": "2、对于xxx组件，登录组件Linux后台，下发命令sesearch -s <process_label> -t  <file_label> --allow （process_label为进程标签；file_label为文件标签）查看进程访问权限并与产品设计输出的SELinux标准策略集比对（参考工具http://3ms.huawei.com/hi/group/3195259/wiki_6175774.html）;", "expect": "2、组件进程SELinux访问权限与SELinux标准策略集中的访问权限一致;"}]}}]}, {"label": "文件", "id": "6049f4610da25400", "children": [{"id": "MCS_94", "label": "默认程序/配置/日志等文件DAC权限最小化测试", "type": 2, "attrs": {"description": "默认程序/配置/日志等文件DAC权限最小化测试", "steps": [{"step": "1、对于xxx组件，登录组件Linux后台，使用ls命令查看已归档日志文件的权限（参考自动化脚本及指导：http://3ms.huawei.com/hi/group/3195259/wiki_5933786.html?for_statistic_from=all_group_wiki）;", "expect": "1、已归档日志文件的权限与设计一致，不超过440;"}, {"step": "2、对于xxx组件，登录组件Linux后台，使用ls命令查看加解密接口、加解密脚本文件的权限（参考自动化脚本及指导：http://3ms.huawei.com/hi/group/3195259/wiki_5933786.html?for_statistic_from=all_group_wiki）;", "expect": "2、加解密接口、加解密脚本文件的权限与设计一致，不超过500;"}, {"step": "3、对于xxx组件，登录组件Linux后台，使用ls命令查看程序文件的权限（参考自动化脚本及指导：http://3ms.huawei.com/hi/group/3195259/wiki_5933786.html?for_statistic_from=all_group_wiki）;", "expect": "3、程序文件的权限与设计一致，不超过550;"}, {"step": "4、对于xxx组件，登录组件Linux后台，使用ls命令查看程序文件所在目录的权限（参考自动化脚本及指导：http://3ms.huawei.com/hi/group/3195259/wiki_5933786.html?for_statistic_from=all_group_wiki）;", "expect": "4、程序文件所在目录的权限与设计一致，不超过550;"}, {"step": "5、对于xxx组件，登录组件Linux后台，使用ls命令查看密钥组件、私钥、证书、加密密文文件的权限（参考自动化脚本及指导：http://3ms.huawei.com/hi/group/3195259/wiki_5933786.html?for_statistic_from=all_group_wiki）;", "expect": "5、密钥组件、私钥、证书、加密密文文件的权限与设计一致，不超过600;"}, {"step": "6、对于xxx组件，登录组件Linux后台，使用ls命令查看配置文件、正在记录日志文件、Debug文件、业务数据文件的权限（参考自动化脚本及指导：http://3ms.huawei.com/hi/group/3195259/wiki_5933786.html?for_statistic_from=all_group_wiki）;", "expect": "6、配置文件、正在记录日志文件、Debug文件、业务数据文件的权限与设计一致，不超过640;"}, {"step": "7、对于xxx组件，登录组件Linux后台，使用ls命令查看密钥、证书、密文文件所在目录的权限（参考自动化脚本及指导：http://3ms.huawei.com/hi/group/3195259/wiki_5933786.html?for_statistic_from=all_group_wiki）;", "expect": "7、密钥、证书、密文文件所在目录的权限与设计一致，不超过700;"}, {"step": "8、对于xxx组件，登录组件Linux后台，使用ls命令查看用户主目录、配置文件目录、日志文件目录、Debug文件目录、临时文件目录、业务数据文件目录的权限（参考自动化脚本及指导：http://3ms.huawei.com/hi/group/3195259/wiki_5933786.html?for_statistic_from=all_group_wiki）;", "expect": "8、用户主目录、配置文件目录、日志文件目录、Debug文件目录、临时文件目录、业务数据文件目录的权限与设计一致，不超过750;"}, {"step": "9、对于xxx组件，登录组件Linux后台，使用ls命令查看维护升级文件目录的权限（参考自动化脚本及指导：http://3ms.huawei.com/hi/group/3195259/wiki_5933786.html?for_statistic_from=all_group_wiki）;", "expect": "11、维护升级文件目录的权限与设计一致，不超过770;"}, {"step": "10、对于xxx组件，登录组件Linux后台，使用系统查询命令（例如find /opt/xxxX -type f -print0|xargs -0 -i ls -al \"{}\"|awk '{if($3==\"root\"){print $NF}}'）扫描系统运行目录;", "expect": "10、不存在root属主文件放在非root属主文件夹下;"}, {"step": "11、对于xxx组件，登录组件Linux后台，查看业务相关的脚本和程序运行用户，使用非文件属主的账户对高权限运行的脚本或程序进行写操作;", "expect": "11、不存在低权限的脚本或程序以高权限用户运行，且非文件属主用户进行写操作失败;"}]}}, {"id": "MCS_95", "label": "动态创建程序/配置/日志等文件DAC权限最小化测试", "type": 2, "attrs": {"description": "动态创建程序/配置/日志等文件DAC权限最小化测试", "steps": [{"step": "1、对于xxx组件，完成业务配置以创建业务相关文件，登录组件Linux后台，使用ls命令查看已归档日志文件的权限（参考自动化脚本及指导：http://3ms.huawei.com/hi/group/3195259/wiki_5933786.html?for_statistic_from=all_group_wiki）;", "expect": "1、已归档日志文件的权限与设计一致，不超过440;"}, {"step": "2、对于xxx组件，登录组件Linux后台，使用ls命令查看加解密接口、加解密脚本文件的权限（参考自动化脚本及指导：http://3ms.huawei.com/hi/group/3195259/wiki_5933786.html?for_statistic_from=all_group_wiki）;", "expect": "2、加解密接口、加解密脚本文件的权限与设计一致，不超过500;"}, {"step": "3、对于xxx组件，登录组件Linux后台，使用ls命令查看程序文件的权限（参考自动化脚本及指导：http://3ms.huawei.com/hi/group/3195259/wiki_5933786.html?for_statistic_from=all_group_wiki）;", "expect": "3、程序文件的权限与设计一致，不超过550;"}, {"step": "4、对于xxx组件，登录组件Linux后台，使用ls命令查看程序文件所在目录的权限（参考自动化脚本及指导：http://3ms.huawei.com/hi/group/3195259/wiki_5933786.html?for_statistic_from=all_group_wiki）;", "expect": "4、程序文件所在目录的权限与设计一致，不超过550;"}, {"step": "5、对于xxx组件，登录组件Linux后台，使用ls命令查看密钥组件、私钥、证书、加密密文文件的权限（参考自动化脚本及指导：http://3ms.huawei.com/hi/group/3195259/wiki_5933786.html?for_statistic_from=all_group_wiki）;", "expect": "5、密钥组件、私钥、证书、加密密文文件的权限与设计一致，不超过600;"}, {"step": "6、对于xxx组件，登录组件Linux后台，使用ls命令查看配置文件、正在记录日志文件、Debug文件、业务数据文件的权限（参考自动化脚本及指导：http://3ms.huawei.com/hi/group/3195259/wiki_5933786.html?for_statistic_from=all_group_wiki）;", "expect": "6、配置文件、正在记录日志文件、Debug文件、业务数据文件的权限与设计一致，不超过640;"}, {"step": "7、对于xxx组件，登录组件Linux后台，使用ls命令查看密钥、证书、密文文件所在目录的权限（参考自动化脚本及指导：http://3ms.huawei.com/hi/group/3195259/wiki_5933786.html?for_statistic_from=all_group_wiki）;", "expect": "7、密钥、证书、密文文件所在目录的权限与设计一致，不超过700;"}, {"step": "8、对于xxx组件，登录组件Linux后台，使用ls命令查看用户主目录、配置文件目录、日志文件目录、Debug文件目录、临时文件目录、业务数据文件目录的权限（参考自动化脚本及指导：http://3ms.huawei.com/hi/group/3195259/wiki_5933786.html?for_statistic_from=all_group_wiki）;", "expect": "8、用户主目录、配置文件目录、日志文件目录、Debug文件目录、临时文件目录、业务数据文件目录的权限与设计一致，不超过750;"}, {"step": "9、对于xxx组件，登录组件Linux后台，使用ls命令查看维护升级文件目录的权限（参考自动化脚本及指导：http://3ms.huawei.com/hi/group/3195259/wiki_5933786.html?for_statistic_from=all_group_wiki）;", "expect": "11、维护升级文件目录的权限与设计一致，不超过770;"}, {"step": "10、对于xxx组件，登录组件Linux后台，使用系统查询命令（例如find /opt/xxxX -type f -print0|xargs -0 -i ls -al \"{}\"|awk '{if($3==\"root\"){print $NF}}'）扫描系统运行目录;", "expect": "10、不存在root属主文件放在非root属主文件夹下;"}, {"step": "11、对于xxx组件，登录组件Linux后台，查看业务相关的脚本和程序运行用户，使用非文件属主的账户对高权限运行的脚本或程序进行写操作;", "expect": "11、不存在低权限的脚本或程序以高权限用户运行，且非文件属主用户进行写操作失败;"}]}}, {"id": "MCS_96", "label": "文件Capability权限最小化测试", "type": 2, "attrs": {"description": "文件Capability权限最小化测试", "steps": [{"step": "1、对于xxx组件，登录组件Linux后台，使用命令getcap（如getcap -r / 2>/dev/null）查找组件上已设置capability的文件;", "expect": "1、对于所有已设置capability文件，其capabilty与权限最小化的设计一致;"}]}}, {"id": "MCS_97", "label": "文件DAC权限设定遵从性测试", "type": 2, "attrs": {"description": "文件DAC权限设定遵从性测试", "steps": [{"step": "1、对于xxx组件，登录组件Linux后台，使用命令umask查看用户掩码，新建文件并查看文件权限;", "expect": "1、用户掩码不小于027，新建文件权限不高于750;"}, {"step": "2、对于xxx组件，登录组件Linux后台，使用dirFilePermissionScan工具扫描xxx业务领域文件（参考自动化脚本及指导：http://3ms.huawei.com/hi/group/3195259/wiki_5933786.html?for_statistic_from=all_group_wiki）;", "expect": "2、文件可查询到其属主及用户(属主、同组用户、其他用户)的权限，且系统中不存在无属主文件;"}]}}, {"id": "MCS_98", "label": "文件Capability权限设定遵从性测试", "type": 2, "attrs": {"description": "文件Capability权限设定遵从性测试", "steps": [{"step": "1、对于xxx业务，登录组件Linux后台，新建文件temp，使用命令setcap给文件temp添加capability（如setcap CAP_CHOWN,CAP_DAC_OVERRIDE+ep temp），并使用命令getcap查询文件temp的capability;", "expect": "1、使用命令setcap给文件temp添加capability无报错，使用命令getcap查询文件temp的capability与设置的目标capability权限一致;"}]}}, {"id": "MCS_99", "label": "文件禁用公网IP测试", "type": 2, "attrs": {"description": "组件文件禁用公网IP测试", "steps": [{"step": "1、对于xxx组件源码，使用架构测试扫描工具或其他工具（如Seninfo）（参考指导书：http://3ms.huawei.com/documents/docinfo/585569441188220928）扫描源代码", "expect": "1、默认未使用公网IP网段地址（包括公网IP地址、公网URL地址/域名、邮箱地址）;"}]}}, {"id": "MCS_100", "label": "文件敏感数据泄露测试", "type": 2, "attrs": {"description": "组件文件敏感数据泄露测试", "steps": [{"step": "1、对于xxx组件源码，使用架构测试扫描工具或其他工具（如Seninfo）（参考指导书：http://3ms.huawei.com/documents/docinfo/585569441188220928）扫描源代码", "expect": "1、扫描结果中无敏感数据（如口令/密钥/密码/银行账号/用户个人数据/慎用词/禁用词等）;"}]}}, {"id": "MCS_101", "label": "文件Linux平台用户态安全编译选项使用测试", "type": 2, "attrs": {"description": "组件文件Linux平台用户态安全编译选项使用测试", "steps": [{"step": "1、对于xxx组件，获取程序文件（如二进制软件包/补丁包），解压程序文件，使用binscope工具或者安全测试云Secbinarycheck工具中软件包安全扫描功能（参考指导书：http://3ms.huawei.com/km/groups/2034125/blogs/details/5796153）对组件文件进行安全编译选项检查;", "expect": "1、必选的安全编译选项100%落地，如栈保护（SP)、GOT表保护(RELRO)、立即绑定（BIND NOW）、堆栈不可执行（NX）、地址无关(PIC)、随机化(PIE)、禁用动态库搜索路径（NO Rpath/Runpath）、删除符号表（Strip）、Fortify Source（数通产品线内部代码必选，外部平台跟随自己计划实施）;"}]}}, {"id": "MCS_102", "label": "文件Linux平台内核态安全编译选项使用测试", "type": 2, "attrs": {"description": "组件文件Linux平台内核态安全编译选项使用测试", "steps": [{"step": "1、对于xxx组件，登录CMC网站（网址：https://cmc-szv.clouddragon.huawei.com/cmcversion/index/search），搜索产品中使用的RTOS版本，获取RTOS的kernelspace包中.config文件，检查配置CONFIG_CC_STACKPROTECTOR/CONFIG_CC_STACKPROTECTOR_STRONG是否打开;", "expect": "1、内核编译前，内核栈保护配置CONFIG_CC_STACKPROTECTOR、CONFIG_CC_STACKPROTECTOR_STRONG已打开;"}, {"step": "2、对于xxx组件，登录组件操作系统后台，使用head /proc/kallsyms查询内核地址空间分布，重启一次内核（重启命令：echo c > /proc/sysrq-trigger），再次查询内核地址空间分布，比较两次内核地址分布是否发生变化;", "expect": "2、两次查询到的内核地址不同，内核地址随机化KASLR使能;"}]}}, {"id": "MCS_103", "label": "文件防病毒植入测试", "type": 2, "attrs": {"description": "组件文件防病毒植入测试", "steps": [{"step": "1、使用公司已提供的杀毒云（参考指导书：http://w3.huawei.com/vscm/user_login.do#）对产品预置或者对外发布的组件进行病毒扫描;", "expect": "1、所有的疑似告警均有分析并闭环，禁止生产环境（自运营现网环境）中存在超出修复时间要求的、可利用的已知高危漏洞;扫描结果(包括防病毒软件名称及软件版本、病毒库版本、扫描时间、扫描结果等)随软件包/补丁包一起发布;"}]}}, {"id": "MCS_104", "label": "文件已知漏洞发现测试", "type": 2, "attrs": {"description": "组件文件二进制已知漏洞发现测试", "steps": [{"step": "1、使用公司已提供的二进制漏洞扫描工具（参考指导书：http://3ms.huawei.com/hi/group/2034125/wiki_5942150.html）对组件开展漏洞扫描;", "expect": "1、所有的疑似告警均有分析并闭环，禁止生产环境（自运营现网环境）中存在超出修复时间要求的、可利用的已知高危漏洞;"}]}}, {"id": "MCS_106", "label": "配置文件默认安全测试", "type": 2, "attrs": {"description": "配置文件默认安全测试", "steps": [{"step": "1、对于xxx组件，获取产品的默认配置文件，查看产品的默认服务;", "expect": "1、默认使用安全的协议（如SSHv2/TLS1.2/TLS1.3/IPSec/SFTP/SNMPv3等协议，及其业界最新安全版本等），默认关闭不安全的协议（如：Telnet、FTP、TFTP、SSHv1、SSH v1.x、tftp、SNMP v1/v2c、ssl2.0、ssl3.0、TLS1.0、TLS1.1、IKEv1），默认仅开启必要的管理协议，默认使能设备安全功能（如防暴力破解机制、复杂度检查机制、日志审计等），升级场景下可以保留升级前版本配置，升级后需针对版本中启用使用的不安全协议（无论是否开启）生成对应告警或用户提示;"}, {"step": "2、对于xxx组件，加载空的配置文件，组件正常启动后，查看产品的默认服务;", "expect": "2、默认关闭不安全的协议，默认使能设备安全功能，默认使用安全的加密算法;"}, {"step": "3、对于xxx组件，使用老版本的配置升级到当前版本，查看组件配置;", "expect": "3、使用新版本的加密方式重新处理老版本配置的敏感数据;"}]}}, {"id": "MCS_109", "label": "配置文件敏感数据安全存储测试", "type": 2, "attrs": {"description": "配置文件敏感数据安全存储测试", "steps": [{"step": "1、对于xxx组件，获取组件的配置文件，使用架构测试扫描工具或其他工具检查配置文件内容;", "expect": "1、配置文件中无明文的敏感数据（如口令/密钥/密码/银行账号/用户个人数据等）;"}]}}]}]}]}]}