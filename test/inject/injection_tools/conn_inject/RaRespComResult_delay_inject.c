/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 Description  : GMDB 505.0.0 故障注入  数据库连接二次握手服务端发送函数(RaRespComResult)
 Notes        : 1. 故障注入函数需要原函数RaRespComResult对外部可见，需要对原函数部分添加GMDB_EXPORT
                    示例：static void RaRespComResult( XXX )
                    添加后：GMDB_EXPORT void RaRespComResult( XXX )
                2. 编译及命令执行链接可按照以下链接中内容实现：
                https://codehub-y.huawei.com/q00880292/GMDBV5/wiki?categoryId=8&sn=WIKI202405303668936
                3. 由于本函数的特殊性，需要injection_tools目录下的CMakeLists.txt的公共头文件目录里添加gmdb
 History      :
 Author       : qibingsen/q00880292
 Create       : [2024.06.02]
*****************************************************************************/

#include "../inject_common.h"
#include "drt_recv_agent.h"
#include "db_alarm.h"
#include "adpt_process_name.h"
#include "drt_log.h"
#include "db_perf_stat.h"
#include "db_msg_buffer.h"
#include "drt_connection_inner.h"
#include "drt_pipe.h"
#include "adpt_sleep.h"
#include "drt_common.h"
#include "drt_instance_inner.h"
#include "adpt_eventfd.h"

#ifdef __cplusplus
extern "C" {
#endif

#define DELAY_TIME_IN_RAESPCOMRESULTSTUB 6
int count_response = 1;

static void InitComResult(ComInfoT *comInfo, uint8_t result, uint32_t shmCtxId, uint16_t nd)
{
    DB_POINTER(comInfo);
    comInfo->result = result;
    comInfo->endian = CM_IS_BIGENDIUM ? COM_BIG_ENDIAN : COM_LITTLE_ENDIAN,
    comInfo->shmemCtxIdHigh = (uint8_t)(shmCtxId >> DB_8BIT);
    comInfo->shmemCtxIdLow = (uint8_t)shmCtxId;
    comInfo->instanceId = DbGetProcGlobalId();
    comInfo->nd = nd;
}

void DrtUnMonitorPipe(DrtPipeT *pipe)
{
    DB_POINTER(pipe);
    if (pipe->monitor != NULL) {
        DbPipeMonitorDelEvent((DbPipeMonitorT *)pipe->monitor, &pipe->dbPipe);
        pipe->monitor = NULL;
    }
}
static void HandshakeError(DrtConnectionT *conn, const DrtPipeT *pipe, uint8_t result, Status ret, const char *errMsg)
{
    const char *auditUserInfo = (conn != NULL ? conn->auditUserInfo : "");
    DB_LOG_AUDIT(
        auditUserInfo, "connection", DB_AUDIT_DCL, ret, "Client connects unsucc. Unable to send the %s response.",
        errMsg);
    uint16_t connId = (conn == NULL) ? DB_INVALID_ID16 : conn->id;
    uint32_t pid = (conn == NULL) ? DB_INVALID_ID32 : conn->pid;
    DB_LOG_ERROR(ret,
        "connect stage1, handshake, srv send %s handshake response unsucc, conn result:%" PRIu8
        ", peerInfo_connId_pipeId:%" PRIu32 "-%s_%" PRIu16 "_%" PRIu16,
        errMsg, (uint8_t)result, pid, auditUserInfo, connId, pipe->pipeId);
    // 如果连接资源申请成功，需要回收
    if (result == (uint8_t)COM_RESULT_SUCCEED) {
        DB_POINTER(conn);
        DrtUnMonitorPipe(conn->drtPipe);
        DrtSetConnStatus(conn, CONN_STATUS_CLOSING);
    }
}
// 替换原函数RaRespComResult实现需自定义
__attribute__((visibility("default"))) void RaRespComResultStub(
    DrtConnectionT *conn, DrtPipeT *pipe, uint8_t result)
{
    if (count_response >= 1) {
        sleep(DELAY_TIME_IN_RAESPCOMRESULTSTUB);
        printf("sleep %d seconds once.\n", DELAY_TIME_IN_RAESPCOMRESULTSTUB);
        count_response--;
    } else {
        printf("ignore sleep.\n");
    }
    uint32_t shmemCtxId = (conn == NULL) ? DB_INVALID_ID32 : conn->msgMemCtx->ctxId;
    uint16_t connId = (conn == NULL) ? DB_INVALID_ID16 : conn->id;
    ComInfoT comInfo;
#ifdef HPE
    uint16_t nd = (conn == NULL) ? DB_INVALID_ID16 : (uint16_t)conn->connMgr->msgPoolMgr->nd;
    if (SECUREC_UNLIKELY(conn != NULL && conn->connMgr->msgPoolMgr->nd > DB_MAX_UINT16)) {
        DB_LOG_WARN(
            GMERR_INTERNAL_ERROR, "Hpe nd is max than uint16, nd:%" PRIu32, conn->connMgr->msgPoolMgr->nd);
    }
    InitComResult(&comInfo, result, shmemCtxId, nd);
#else
    InitComResult(&comInfo, result, shmemCtxId, 0);
#endif
    PipeBufferT pipeBuf = {0};
    pipeBuf.buf = (uint8_t *)&comInfo;
    pipeBuf.shmPtr = *(ShmemPtrT *)(void *)&comInfo;
    pipeBuf.validSize = (uint32_t)sizeof(ComInfoT);
    DB_LOG_DEBUG("conn %" PRIu16 " send rsp info, shmemCtxId %" PRIu32 " highCtxId %" PRIu8 " lowCtxId %" PRIu8
                       ", endian %" PRIu8 ", result %" PRIu8,
        connId, shmemCtxId, comInfo.shmemCtxIdHigh, comInfo.shmemCtxIdHigh, comInfo.endian, result);
    pipe->handshakeTimePoint.sendSecHandshakeRspBegin = DbRdtsc();
    comInfo.instanceId = 1;
    Status ret = DbAdptPipeSend(&pipe->dbPipe, &pipeBuf);
    pipe->handshakeTimePoint.sendSecHandshakeRspEnd = DbRdtsc();
    if (ret != GMERR_OK) {
        HandshakeError(conn, pipe, result, ret, "second");
        return;
    }
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    ShmemPtrT msgPoolShmPtr = (conn == NULL) ? DB_INVALID_SHMPTR : conn->connMgr->msgPoolMgr->msgPools[conn->id];
    pipeBuf.shmPtr = (ShmemPtrT){.segId = msgPoolShmPtr.segId, .offset = msgPoolShmPtr.offset};
    pipe->handshakeTimePoint.sendThirdHandshakeRspBegin = DbRdtsc();
    ret = DbAdptPipeSend(&pipe->dbPipe, &pipeBuf);
    pipe->handshakeTimePoint.sendThirdHandshakeRspEnd = DbRdtsc();
    if (ret != GMERR_OK) {
        HandshakeError(conn, pipe, result, ret, "third");
    }
#endif
}

__attribute__((visibility("default"))) void GmsInjRunOnce(void)
{
    // 接口预留，后续使用
}

// 注入故障  替换原函数时调用（kill -36）
__attribute__((visibility("default"))) int32_t GmsInjLoad()
{
    init();
    printf("gmserver_inj start stub.\n");
    // 找原函数
    void *handle = dlopen("libgmdb.so.5", RTLD_LAZY);
    if (!handle) {
        printf("fopen failed.\n");
        return -1;
    }
    void *(*stubFunc)(DrtConnectionT *conn, DrtPipeT *pipe, uint8_t result) = dlsym(handle, "RaRespComResult");
    if (stubFunc == NULL) {
        printf("load func RaRespComResult unsuccess.\n");
        return -1;
    } else {
        printf("load func RaRespComResult successfully.\n");
    }
    // 对原始函数进行打桩，返回所打桩的索引号（供清除时使用），负数表示失败
    return setStubC(stubFunc, RaRespComResultStub);
}

// 清除故障 回退函数时调用//（kill -37）
__attribute__((visibility("default"))) int32_t GmsInjUnload(void)
{
    // 清理打桩
    DB_LOG_ERROR_UNFOLD(0, "GmsInjUnload start.");
    int ret = clearAllStub();
    DB_LOG_ERROR_UNFOLD(0, "GmsInjUnload end.");
    system("touch /root/GmsInjUnload.ready");
    return ret;
}

#ifdef __cplusplus
}
#endif
