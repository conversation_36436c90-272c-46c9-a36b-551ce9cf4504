/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: nlog importer
 * Create: 2025-01-23
 */
#include "gmei_utils.h"

// CPU Occupation Start
static bool g_isFirstStart = true;

int32_t GetCpuOccupation(GmeiCpuOccupationT *occ)
{
    FILE *fp;
    char buf[MAX_CPU_REC_BUF_SIZE];
    fp = fopen("/proc/stat", "r");  // 这里只获取cpu总信息
    if (fp == NULL) {
        printf("Open file with problem.\n");
        return -1;
    }
    (void)fgets(buf, sizeof(buf), fp);
    int32_t err =
        sscanf_s(buf, "%s %u %u %u %u", occ->name, MAX_NAME_LEN, &occ->user, &occ->nice, &occ->system, &occ->idle);
    if (err < 0) {
        printf("Get CPU occupation with problem.\n");
        return -1;
    }
    (void)fclose(fp);
    return 0;
}

int32_t CalCpuOccupationPercentage(double *percentage)
{
    GmeiCpuOccupationT occ1 = {0};
    GmeiCpuOccupationT occ2 = {0};
    int32_t ret = GetCpuOccupation(&occ1);
    if (ret != 0) {
        printf("Get CPU occupation 1 with problem.\n");
        return ret;
    }
    usleep(INTERVAL_MILLISECONDS);
    ret = GetCpuOccupation(&occ2);
    if (ret != 0) {
        printf("Get CPU occupation 2 with problem.\n");
        return ret;
    }
    double numerator = (double)(occ2.user) - occ1.user + occ2.system - occ1.system;
    double denominator =
        (occ2.user + occ2.nice + occ2.system + occ2.idle) - (occ1.user + occ1.nice + occ1.system + occ1.idle);
    *percentage = numerator / denominator * PERCENTAGE;
    return 0;
}

void GmeiCheckCpuOccupationAndWaitIfNeeded(void)
{
    double percentage = 0;
    int32_t ret = CalCpuOccupationPercentage(&percentage);
    if (ret != 0) {
        printf("Check CPU occupation with problem.\n");
        return;
    }
    bool isGtLimit = false;
    while (isGtLimit || percentage >= MAX_CPU_OCC_PERCENT || (g_isFirstStart && percentage >= CONT_CPU_OCC_PERCENT)) {
        sleep(SLEEP_SECS);
        isGtLimit = true;
        if (percentage < CONT_CPU_OCC_PERCENT) {
            g_isFirstStart = false;
            return;
        }
        ret = CalCpuOccupationPercentage(&percentage);
        if (ret != 0) {
            printf("Check CPU occupation with problem.\n");
            return;
        }
    }
}
// CPU Occupation End

// File Operation Start
GmeiStringT ExtractNameAndGenerateOffsetFileName(GmeiStringT name)
{
    GmeiStringT realNameStr = {0};
    char *realNameEnd = strrchr(name.data, '.');
    if (realNameEnd == NULL) {
        printf("Find real name end with problem.\n");
        return realNameStr;
    }
    uint32_t realNameSize = realNameEnd - name.data;
    uint32_t offsetFileNameSize = realNameSize + OFFSET_TXT_FILE_NAME_SUFFIX_SIZE + 1;
    char *realName = (char *)GmeiAlloc(offsetFileNameSize * sizeof(char));
    if (realName == NULL) {
        printf("Alloc real name with problem.\n");
        return realNameStr;
    }
    (void)memset_s(realName, offsetFileNameSize, 0, offsetFileNameSize);
    int32_t err = strcpy_s(realName, name.length + 1, name.data);
    if (err != 0) {
        printf("Copy file real name with problem.\n");
        return realNameStr;
    }
    err = strcpy_s(realName + realNameSize, OFFSET_TXT_FILE_NAME_SUFFIX_SIZE, OFFSET_TXT_FILE_NAME_SUFFIX);
    if (err != 0) {
        printf("Copy file suffix with problem.\n");
        return realNameStr;
    }
    realNameStr.data = realName;
    realNameStr.length = offsetFileNameSize - 1;
    return realNameStr;
}

bool IsFileExist(GmeiStringT path)
{
    return access(path.data, 0) == 0;
}

uint32_t GetOffsetFromFile(GmeiStringT fullPath)
{
    FILE *fp = NULL;
    fp = fopen(fullPath.data, "r");
    if (fp == NULL) {
        printf("Open file with problem.\n");
        return -1;
    }
    char buf[MAX_UINT32_LEN] = {0};
    (void)fgets(buf, sizeof(buf), fp);
    (void)fclose(fp);
    return strtoul(buf, NULL, DECIMAL_BASE);
}

int32_t UpdateOffsetToFile(GmeiStringT fullPath, uint32_t offset)
{
    FILE *fp = NULL;
    fp = fopen(fullPath.data, "w");
    if (fp == NULL) {
        printf("Open file with problem.\n");
        return -1;
    }
    char numStr[MAX_UINT32_LEN] = {0};
    int32_t err = sprintf_s(numStr, MAX_UINT32_LEN, "%" PRIu32 "", offset);
    if (err <= 0) {
        printf("Write offset to file with problem.\n");
        return -1;
    }
    (void)fwrite(numStr, sizeof(numStr), 1, fp);  // overwrite
    (void)fclose(fp);
    return 0;
}

void DeleteOriginalDataAndOffsetFile(GmeiDataTableT *data)
{
    (void)remove(data->dataFullFilePath.data);
    (void)remove(data->offsetFullFilePath.data);
    GmeiFree(data->offsetFullFilePath.data);
    data->offsetFullFilePath.data = NULL;
}
// File Operation End
