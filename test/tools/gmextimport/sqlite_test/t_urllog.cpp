#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <sqlite3.h>
#include <arpa/inet.h>
#include "sqlite_table.h"

int main(int argc, char *argv[])
{
    if (argc > MAX_ARG_NUM) {
        printf("Usage: ./t_urllog -p [cmd file path] -n [total row num]\n");
        return -1;
    }
    char *filePath = NULL;
    int num_records = 1500000;
    if (argc > 1) {
        uint32_t i = 1;
        while (i < argc) {
            if (strcmp(argv[i], "-p") == 0 && i + 1 < argc) {
                filePath = argv[i + 1];
                i += 2;
                continue;
            } else if (strcmp(argv[i], "-n") == 0 && i + 1 < argc) {
                num_records = atoi(argv[i + 1]);
                i += 2;
                continue;
            } else {
                printf("Usage: ./t_urllog -p [cmd file path] -n [total row num]\n");
                return -1;
            }
        }
    }
    sqlite3 *db;
    char *err_msg = 0;
    int rc;

    // 打开 SQLite 数据库
    if (filePath == NULL) {
        rc = sqlite3_open("Url_Org.db", &db);
    } else {
        rc = sqlite3_open(filePath, &db);
    }
    if (rc != SQLITE_OK) {
        fprintf(stderr, "无法打开数据库: %s\n", sqlite3_errmsg(db));
        sqlite3_close(db);
        return 1;
    }

    // 创建 t_urllog 表的 SQL 语句
    char *sql_create_table = "CREATE TABLE IF NOT EXISTS t_urllog("
                             "log_id INTEGER PRIMARY KEY,"
                             "log_time INTEGER,"
                             "vsys_id INTEGER,"
                             "src_ip TEXT,"
                             "dst_ip TEXT,"
                             "nat_src_ip INTEGER,"
                             "nat_dst_ip INTEGER,"
                             "src_port INTEGER,"
                             "dst_port INTEGER,"
                             "nat_src_port INTEGER,"
                             "nat_dst_port INTEGER,"
                             "protocol_type INTEGER,"
                             "user_id INTEGER,"
                             "user_group_id INTEGER,"
                             "app_category_id INTEGER,"
                             "sub_app_id INTEGER,"
                             "app_id INTEGER,"
                             "src_zone_id INTEGER,"
                             "dst_zone_id INTEGER,"
                             "sec_policy_id INTEGER,"
                             "event_num INTEGER,"
                             "profile_id INTEGER,"
                             "rule_id INTEGER,"
                             "list_type INTEGER,"
                             "action_type INTEGER,"
                             "url_info TEXT,"
                             "domain_name TEXT,"
                             "url_category_id INTEGER,"
                             "url_sub_category_id INTEGER,"
                             "src_loc_id INTEGER,"
                             "dst_loc_id INTEGER,"
                             "src_mask_id INTEGER,"
                             "dst_mask_id INTEGER,"
                             "referer_name TEXT"
                             ");";

    // 执行创建表的 SQL 语句
    rc = sqlite3_exec(db, sql_create_table, 0, 0, &err_msg);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "SQL 错误: %s\n", err_msg);
        sqlite3_free(err_msg);
        sqlite3_close(db);
        return 1;
    }

    // 准备插入数据的 SQL 语句
    char *sql_insert = "INSERT INTO t_urllog ("
                       "log_time, vsys_id, src_ip, dst_ip, nat_src_ip, nat_dst_ip, src_port, dst_port, "
                       "nat_src_port, nat_dst_port, protocol_type, user_id, user_group_id, app_category_id, "
                       "sub_app_id, app_id, src_zone_id, dst_zone_id, sec_policy_id, event_num, profile_id, "
                       "rule_id, list_type, action_type, url_info, domain_name, url_category_id, url_sub_category_id, "
                       "src_loc_id, dst_loc_id, src_mask_id, dst_mask_id, referer_name"
                       ") VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    sqlite3_exec(db, "begin;", 0, 0, 0);
    sqlite3_stmt *stmt;
    rc = sqlite3_prepare_v2(db, sql_insert, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "无法准备语句: %s\n", sqlite3_errmsg(db));
        sqlite3_close(db);
        return 1;
    }

    // 初始化随机数种子
    srand(time(NULL));

    // 生成随机数据
    int ranVal = rand();
    int log_time = time(NULL);
    int vsys_id = ranVal % 10 + 1;

    char src_ip[16], dst_ip[16];
    generate_random_ip(src_ip, false);  // 生成随机的源 IP
    generate_random_ip(dst_ip, false);  // 生成随机的目的 IP

    char nat_src_ip_str[16], nat_dst_ip_str[16];
    generate_random_ip(nat_src_ip_str, false);  // 生成随机的 NAT 源 IP
    generate_random_ip(nat_dst_ip_str, false);  // 生成随机的 NAT 目的 IP

    unsigned int nat_src_ip = ip_to_int(nat_src_ip_str);
    unsigned int nat_dst_ip = ip_to_int(nat_dst_ip_str);

    int src_port = ranVal % (65535 - 1024 + 1) + 1024;
    int dst_port = ranVal % 65535 + 1;
    int nat_src_port = ranVal % (65535 - 1024 + 1) + 1024;
    int nat_dst_port = ranVal % 65535 + 1;
    int protocol_type = (ranVal % 2) ? 6 : 17;  // TCP 或 UDP

    int user_id = ranVal % 1000 + 1;
    int user_group_id = ranVal % 50 + 1;
    int app_category_id = ranVal % 100 + 1;
    int sub_app_id = ranVal % 500 + 1;
    int app_id = ranVal % 1000 + 1;
    int src_zone_id = ranVal % 5 + 1;
    int dst_zone_id = ranVal % 5 + 1;
    int sec_policy_id = ranVal % 50 + 1;
    int event_num = ranVal % 100 + 1;
    int profile_id = ranVal % 20 + 1;
    int rule_id = ranVal % 100 + 1;
    int list_type = ranVal % 5 + 1;
    int action_type = ranVal % 5 + 1;

    char url_info[101];
    generate_random_string(url_info, sizeof(url_info));  // 生成随机的 URL 信息

    char domain_name[51];
    generate_random_string(domain_name, sizeof(domain_name));  // 生成随机的域名

    int url_category_id = ranVal % 50 + 1;
    int url_sub_category_id = ranVal % 50 + 1;
    int src_loc_id = ranVal % 200 + 1;
    int dst_loc_id = ranVal % 200 + 1;
    int src_mask_id = ranVal % 32 + 1;
    int dst_mask_id = ranVal % 32 + 1;

    char referer_name[101];
    generate_random_string(referer_name, sizeof(referer_name));  // 生成随机的引用者名称

    int time_step = 0;
    // 插入随机数据
    for (int i = 0; i < num_records; i++) {
        // 生成随机数据
        time_step++;
        if (time_step >= 4000) {
            log_time++;
            time_step = 0;
        }
        vsys_id++;
        src_port++;
        dst_port++;
        nat_src_port++;
        ;
        nat_dst_port++;
        protocol_type++;
        user_id++;
        user_group_id++;
        app_category_id++;
        sub_app_id++;
        app_id++;
        src_zone_id++;
        dst_zone_id++;
        sec_policy_id++;
        event_num++;
        profile_id++;
        rule_id++;
        list_type++;
        action_type++;
        url_category_id++;
        url_sub_category_id++;
        src_loc_id++;
        dst_loc_id++;
        src_mask_id++;
        dst_mask_id++;

        // 绑定参数
        sqlite3_bind_int(stmt, 1, log_time);
        sqlite3_bind_int(stmt, 2, vsys_id);
        sqlite3_bind_text(stmt, 3, src_ip, -1, SQLITE_TRANSIENT);
        sqlite3_bind_text(stmt, 4, dst_ip, -1, SQLITE_TRANSIENT);
        sqlite3_bind_int(stmt, 5, nat_src_ip);
        sqlite3_bind_int(stmt, 6, nat_dst_ip);
        sqlite3_bind_int(stmt, 7, src_port);
        sqlite3_bind_int(stmt, 8, dst_port);
        sqlite3_bind_int(stmt, 9, nat_src_port);
        sqlite3_bind_int(stmt, 10, nat_dst_port);
        sqlite3_bind_int(stmt, 11, protocol_type);
        sqlite3_bind_int(stmt, 12, user_id);
        sqlite3_bind_int(stmt, 13, user_group_id);
        sqlite3_bind_int(stmt, 14, app_category_id);
        sqlite3_bind_int(stmt, 15, sub_app_id);
        sqlite3_bind_int(stmt, 16, app_id);
        sqlite3_bind_int(stmt, 17, src_zone_id);
        sqlite3_bind_int(stmt, 18, dst_zone_id);
        sqlite3_bind_int(stmt, 19, sec_policy_id);
        sqlite3_bind_int(stmt, 20, event_num);
        sqlite3_bind_int(stmt, 21, profile_id);
        sqlite3_bind_int(stmt, 22, rule_id);
        sqlite3_bind_int(stmt, 23, list_type);
        sqlite3_bind_int(stmt, 24, action_type);
        sqlite3_bind_text(stmt, 25, url_info, -1, SQLITE_TRANSIENT);
        sqlite3_bind_text(stmt, 26, domain_name, -1, SQLITE_TRANSIENT);
        sqlite3_bind_int(stmt, 27, url_category_id);
        sqlite3_bind_int(stmt, 28, url_sub_category_id);
        sqlite3_bind_int(stmt, 29, src_loc_id);
        sqlite3_bind_int(stmt, 30, dst_loc_id);
        sqlite3_bind_int(stmt, 31, src_mask_id);
        sqlite3_bind_int(stmt, 32, dst_mask_id);
        sqlite3_bind_text(stmt, 33, referer_name, -1, SQLITE_TRANSIENT);

        // 执行插入操作
        rc = sqlite3_step(stmt);
        if (rc != SQLITE_DONE) {
            fprintf(stderr, "执行失败: %s\n", sqlite3_errmsg(db));
        }

        // 重置语句以便下一次插入
        sqlite3_reset(stmt);
    }

    // 结束语句并关闭数据库连接
    sqlite3_finalize(stmt);
    sqlite3_exec(db, "commit;", 0, 0, 0);
    sqlite3_close(db);

    printf("随机数据已成功插入到 t_urllog 表中。\n");

    return 0;
}
