import re
import my_rule as my_ru
from openpyxl import Workbook, load_workbook
from common import tag_commit_id, touch, header_cols_num, get_abs_path
import json
import os
import codecs

def get_code(file_path, line_num):
    line_count = 0
    target_code = ''
    with codecs.open(file_path, 'r', 'utf-8') as f:
        for line in f:
            line_count += 1
            if line_count == line_num:
                target_code = line
                break
    return target_code

class RuleEngine:
    def __init__(self, ruleFile, reportFile, outFile):
        self.ruleFile = ruleFile
        self.reportFile = reportFile
        self.outFile = outFile
        self.codeCache = []
        self.rules = []
        self.book =  []

        self.analyzeRule()
        self.getCodeLine()

    def printJson(self):
        with open(self.ruleFile, 'r') as f:
            rulesJson = json.load(f)
        print(rulesJson)

    def analyzeRule(self):
        with open(self.ruleFile, 'r', encoding='utf-8') as f:
            rulesJson = json.load(f)
        for ruleJson in rulesJson:
            myRule = my_ru.MyRule()
            myRule.name = ruleJson['name']
            # RE = 1, python_module = 2
            if (ruleJson['filter_type'] == "RE"):
                myRule.filterType = 1
            elif (ruleJson['filter_type'] == "python_module"):
                myRule.filterType = 2
            myRule.filterInfo = ruleJson['filter_info']

            actionList = ruleJson['filter_action']
            actionCode = 0
            for actionStr in actionList:
                if (actionStr == "ignore_line"):
                    actionCode += 1
                if (actionStr == "ignore_branch_if_line_hit"):
                    actionCode += 2
                if (actionStr == "dangerous"):
                    actionCode += 4
            myRule.filterAction = actionCode
            myRule.filterComment = ruleJson['filter_comment']
            self.rules.append(myRule)

    def getCodeLine(self):
        # in
        workbook = load_workbook(self.reportFile)
        ws = workbook.active
        # out
        workBookOut = Workbook()
        workSheetOut = workBookOut.active

        fileLine = 1
        # 遍历每一行，获取file_name、line_number、hit_count和iscover字段的值
        for row in ws.iter_rows(min_row=1, values_only=True):
            file_name, line_number, hit_count, is_covered, reason, note = row[:header_cols_num]
            if (file_name.find("File Name") != -1):
                workSheetOut.append([file_name, line_number, hit_count, is_covered, reason, note])
                continue
            file_name = os.path.normpath(file_name)
            codeLine = get_code(get_abs_path(file_name), line_number)
            tmpStr = ""
            for y, rule in enumerate(self.rules):
                if rule.match(codeLine) and hit_count == 0:
                    if self.rules[y].filterAction == 4:
                        hit_count = 0
                        action = "危险"
                        tmpStr += "命中规则：%s, 行为：%s, 描述：%s." % (self.rules[y].name, action, self.rules[y].filterComment)
                        note = note + ";" if note else "" + tmpStr
                    else:
                        hit_count = 1
                        action = "忽略"
                        tmpStr += "命中规则：%s, 行为：%s, 描述：%s." % (self.rules[y].name, action, self.rules[y].filterComment)
                        reason = reason + ";" if reason else "" + tmpStr
            workSheetOut.append([file_name, line_number, hit_count, is_covered, reason, note])
        tag_commit_id(workSheetOut)
        workBookOut.save(self.outFile)
