#include "executor.hpp"
#include "error.hpp"
#include "expectation.hpp"
#include "iequals.hpp"
#include "io_helpers.hpp"
#include "logger.hpp"
#include "parser.hpp"
#include "string_view.hpp"
#include "to_underlying.hpp"
#include <gmc_sql.h>  // GmcExecDirect
#include <algorithm>
#include <atomic>
#include <chrono>
#include <random>
#include <sstream>
#include <string>
#include <string_view>
#include <variant>
#include <error/db_last_error.h>
#include <utils/db_text.h>

namespace chr = std::chrono;

namespace gmdb::test {

[[nodiscard]] constexpr bool ends_with(std::string_view str, char affix) noexcept
{
    if (str.empty())
        return false;
    return std::char_traits<char>::eq(str.back(), affix);
}

static std::atomic<std::uint64_t> __counter{0};

inline std::string generate_alias(std::string_view prefix)
{
    auto v = ++__counter;
    // Use fixed width and fill with zeroes, to avoid cases where e.g. "alias9" > "alias10".
    char buf[std::numeric_limits<decltype(v)>::digits10 + 1];
    std::snprintf(buf, std::size(buf), "%05" PRIu64, v);
    return std::string(prefix) + buf;
}

inline std::string generate_alias()
{
    return generate_alias("alias");
}

template <typename T>
static inline T generate_float_number(T lower_bound, T upper_bound)
{
    static_assert(std::is_floating_point_v<T>, "overload for non-floating-point numbers is not implemented");

    std::uniform_real_distribution<T> dist(lower_bound, upper_bound);
    static std::random_device rd;
    static std::default_random_engine gen(rd());
    return dist(gen);
}

template <typename T>
static inline T generate_float_number()
{
    return generate_float_number<T>(std::numeric_limits<T>::min(), std::numeric_limits<T>::max());
}

template <typename T>
static inline T generate_int_number(T lower_bound, T upper_bound)
{
    static_assert(std::is_integral_v<T>, "overload for non-floating-point numbers is not implemented");

    std::uniform_int_distribution<T> dist(lower_bound, upper_bound);
    static std::random_device rd;
    static std::default_random_engine gen(rd());
    return dist(gen);
}

template <typename T>
static inline T generate_int_number()
{
    return generate_int_number<T>(std::numeric_limits<T>::min(), std::numeric_limits<T>::max());
}

double generate_double()
{
    return generate_float_number<double>();
}

inline std::int64_t generate_int64()
{
    return generate_int_number<std::int64_t>();
}

std::string generate_string(size_t size)
{
    static std::random_device rd;
    static std::default_random_engine gen(rd());
    std::uniform_int_distribution<char> dist('0', 'z');  // Digits, symbols, uppercase and lowercase letters.

    std::string res(static_cast<std::string::size_type>(size), '\0');
    for (size_t i = 0; i != size; ++i) {
        res[i] = dist(gen);
    }
    return res;
}

void split(std::vector<std::string_view> &res, std::string_view s, std::string_view delimiter)
{
    size_t pos_start = 0, pos_end, delim_len = delimiter.length();

    while ((pos_end = s.find(delimiter, pos_start)) != std::string_view::npos) {
        std::string_view token = s.substr(pos_start, pos_end - pos_start);
        pos_start = pos_end + delim_len;
        res.push_back(token);
    }

    res.push_back(s.substr(pos_start));
}

struct ci_messages {
    static std::string write_property(std::string_view name, std::string_view value)
    {
        return " {}" + std::string(name) + "='" + std::string(value) + "'";
    }

    static std::string write_property(std::string_view name, std::chrono::milliseconds value)
    {
        return " {}" + std::string(name) + "='" + std::to_string(value.count()) + "'";
    }

    void write_text_message(std::string_view context, std::string_view name)
    {
        log::emit(log::info, name);
    }

    void write_message(std::string_view context, std::string_view name)
    {
        log::emit(log::info, context, ": ", name);
    }

    void write_message_with_error(
        std::string_view context, std::string_view name, std::string_view message, std::string_view details)
    {
        log::emit(log::error, context, ": ", name, ": ", message);
        if (!details.empty()) {
            log::emit(log::info, "details: ", details);
        }
    }

    void write_message_with_message(std::string_view context, std::string_view name, std::string_view message)
    {
        printf("[%s%s%s]\n", std::string(context).c_str(), write_property("name", name).c_str(),
            write_property("message", message).c_str());
    }

    void write_message_with_duration(
        std::string_view context, std::string_view name, std::chrono::milliseconds duration)
    {
        printf("[%s%s%s]\n", std::string(context).c_str(), write_property("name", name).c_str(),
            write_property("duration", duration).c_str());
    }

    void suiteStarted(std::string_view name)
    {
        write_message("testSuiteStarted", name);
    }

    void suiteFinished(std::string_view name)
    {
        write_message("testSuiteFinished", name);
    }

    void testStarted(std::string_view name = {})
    {
        write_message("[testStarted]", name);
    }

    void testFailed(std::string_view name, std::string_view message, std::string_view details = {})
    {
        write_message_with_error("*** [testFailed]", name, message, details);
    }

    void testIgnored(std::string_view name, std::string_view message = {})
    {
        write_message_with_message("* [testIgnored]", name, message);
    }

    void testFinished(std::string_view name, int durationMs = -1)
    {
        write_message_with_duration("[testFinished]", name, std::chrono::milliseconds{durationMs});
    }

    void contextStarted()
    {
        // write_message("blockOpened", "context");
    }

    void contextMessage(std::string_view value = {})
    {
        write_text_message("message", value);
    }

    void contextFinished()
    {
        // write_message("blockClosed", "context");
    }
};

static constexpr auto npos = std::string_view::npos;

struct substitution {
    std::string_view name;
    std::string_view key;
};

namespace {

substitution find_substitution(std::string_view arg)
{
    // Optional colon.
    size_t colon = arg.find(':');
    size_t key = arg.size();
    if (colon != npos) {
        key = colon + 1;
    }

    return substitution{
        .name = arg.substr(0u, colon),
        .key = arg.substr(key),
    };
}

ci_messages ci;

inline bool should_fail(const fs::path &test_file)
{
    auto marker = test_file.stem().extension().string();
    return iequals(marker, ".fail");
}

[[nodiscard]] bool requirements_satisified(
    span<requirement> requirements, const execution_configuration::features_map &features)
{
    for (const requirement &r : requirements) {
        auto it = features.find(r.name);

        if (it == features.end()) {
            if (r.negated)
                return true;

            log::emit(log::warning, "unknown requirement: ", r);
            return false;
        }

        if (it->second == r.negated) {
            log::emit(log::debug, "requirement not satisfied: ", r);
            return false;
        }
    }

    return true;
}

struct expectation_interpreter {
    expectation_interpreter(detail::output_result &output, const detail::query_result &result,
        const execution_configuration &config, cache_map &replacement_cache)
        : _output{output}, _result{result}, _config{config}, _replacement_cache{replacement_cache}
    {}

    check_result visit(std::string_view query, expectation &exp)
    {
        auto er = std::visit(*this, exp.value);

        if (er.status == check_result::failure) {
            log::emit(log::warning, "failed expectation: ", exp);
            log::emit(log::warning, "- query: ", query);
            for (const auto &ctx : er.context) {
                log::emit(log::warning, "- ", ctx);
            }
        }

        if (er.output) {
            _output = std::move(*(er.output));
        }
        return er.status;
    }

    template <typename Expect>
    expectation_result operator()(Expect &exp)
    {
        using input_type = typename Expect::input_type;

        exp.prepare(_config, _replacement_cache);
        expectation_result res{};
        if constexpr (std::is_same_v<void, input_type>) {
            res = exp.execute();
        } else if constexpr (std::is_same_v<detail::query_result, input_type>) {
            res = exp.execute(_result);
        } else if constexpr (std::is_same_v<detail::output_result, input_type>) {
            res = exp.execute(_output);
        }

        return res;
    }

private:
    detail::output_result &_output;
    const detail::query_result &_result;
    const execution_configuration &_config;
    cache_map &_replacement_cache;
};

std::string get_uri(std::string_view key, const execution_configuration &cfg)
{
    if (key.empty()) {
        // Give the full URI.
        return cfg.uri;
    }

    // Give the URI chosen by key (index).
    auto address = std::string_view{cfg.uri};
    auto pos = address.find_last_of("/") + 1;
    auto proto = (pos == std::string_view::npos) ? std::string_view{} : address.substr(0u, pos);
    address.remove_prefix(pos);

    std::vector<std::string_view> addresses;
    split(addresses, address, ",");

    auto k = std::string{key};
    std::size_t parse_pos = std::string::npos;
    int idx = std::stoi(k, &parse_pos);
    if (parse_pos != k.size()) {
        throw std::runtime_error("URI index parsing failed");
    }

    if (idx < 0) {
        // Index from the end: -1 is the last element, -2 is the penultimate, etc.
        idx = static_cast<int>(addresses.size()) + idx;
        if (idx < 0) {
            throw std::runtime_error("URI index out of range");
        }
    }

    assert(idx >= 0);
    if (idx >= static_cast<int>(addresses.size())) {
        throw std::runtime_error("URI index out of range");
    }

    return std::string(proto) + std::string(addresses[static_cast<size_t>(idx)]);
}

}  // namespace

std::string apply_replacements(const execution_configuration &cfg, cache_map &replacement_cache, std::string_view q)
{
    std::string res;
    res.reserve(q.size());

    const std::array<std::string_view, 8> binary_apps = {
        "qdbd",
        "qdbsh",
        "qdb_import",
        "qdb_export",
        "qdb_max_conn",
        "qdb_dbtool",
        "qdb_user_add",
        "qdb_cluster_keygen",
    };

    size_t pos = 0;
    size_t done_pos = 0;
    while ((pos = q.find("%{", pos)) != npos) {
        assert(pos >= done_pos);
        res += q.substr(done_pos, pos - done_pos);
        if (pos > 0 && q[pos - 1] == '%') {
            // Double percent sign, escape.
            done_pos = pos + 1;
            pos = pos + 2;
            continue;
        }

        pos += 2;
        assert(pos <= q.size());

        // Closing brace.
        size_t end_pos = q.find('}', pos);
        if (end_pos == npos) {
            std::string msg =
                "unterminated replacement in string: " + std::string(q) + " at position " + std::to_string(pos);
            ci.contextMessage(msg);
            throw std::runtime_error(msg);
        }
        done_pos = end_pos + 1;

        auto arg = q.substr(pos, end_pos - pos);
        substitution s = find_substitution(arg);

        static constexpr std::string_view default_timestamp_column = "_timestamp";

        if (s.name == "" || s.name == "s") {
            if (s.key.empty()) {
                res += generate_alias();
            } else {
                // Find in cache or generate.
                auto k = std::string{s.key};
                auto it = replacement_cache.find(k);
                if (it == replacement_cache.end()) {
                    it = replacement_cache.try_emplace(it, std::move(k), generate_alias());
                }
                res += it->second;
            }
        } else if (s.name.front() == '\\') {
            if (s.name.size() != 2) {
                ci.contextMessage("invalid escape in substitution: " + std::string(s.name));
            } else {
                switch (s.name.back()) {
                    case 'n':
                        res += '\n';
                        break;
                    case 'r':
                        res += '\r';
                        break;
                    default:
                        ci.contextMessage("unknown escape in substitution: " + std::string(s.name));
                }
            }
        } else if (s.name == "b") {
            // We could also accept length parameter optionally, if needed.
            res += generate_string(10);
        } else if (s.name == "d") {
            res += std::to_string(generate_int64());
        } else if (s.name == "g") {
            char buf[std::numeric_limits<double>::digits10 + 1];
            snprintf(buf, std::size(buf), "%g", generate_double());
            res += buf;
        } else if (s.name == "compression") {
            res += cfg.compression;
        } else if (s.name == "timestamp-column") {
            res += default_timestamp_column;
        } else if (s.name == "create-timestamp-column") {
            res += "" + std::string(default_timestamp_column) + " INTEGER, ";
        } else if (s.name == "shard size") {
            res += cfg.shard_size.empty() ? "1 hour" : cfg.shard_size;
        } else if (s.name == "create options") {
            res += "WITH (time_col = '" + std::string(default_timestamp_column) + "', interval = '";
            if (std::empty(cfg.shard_size)) {
                res += "1 hour";
            } else {
                res += cfg.shard_size;
            }
            res += "', compression = '" + std::string(cfg.compression) + "')";
        } else if (s.name == "select options") {
            res += cfg.select_options;
        } else if (s.name == "uri") {
            res += get_uri(s.key, cfg);
        } else if (s.name == "address") {
            auto address = std::string_view{cfg.uri};
            address.remove_prefix(address.find_last_of("/") + 1);
            res += std::string(address);
        } else if (s.name == "test-file") {
            res += fs::weakly_canonical(cfg.current_test).generic_string();
        } else if (s.name == "test-dir") {
            res += fs::weakly_canonical(cfg.current_test.parent_path()).generic_string();
        } else if (s.name == "bin") {
            res += cfg.binary_dir.generic_string();
        } else if (s.name == "connect options") {
            // Add connection parameters.
            if (!cfg.uri.empty()) {
                res += " --cluster ";
                res += cfg.uri;
            }

            if (!cfg.cluster_public_key_file.empty()) {
                res += " --cluster-public-key-file ";
                res += cfg.cluster_public_key_file.generic_string();
            }

            if (!cfg.user_security_file.empty()) {
                res += " --user-security-file ";
                res += cfg.user_security_file.generic_string();
            }
        } else if (std::find(binary_apps.begin(), binary_apps.end(), s.name) != binary_apps.end()) {
            fs::path binary = cfg.binary_dir / std::string{s.name};
#ifndef NDEBUG
            binary += "d";
#endif
            res += binary.generic_string();
        } else {
            ci.contextMessage("unknown substitution: " + std::string(s.name));
        }
    }

    res += q.substr(done_pos);
    return res;
}

static detail::query_result execute_query(gmc_connection_handle handle, std::string_view query)
{
    assert(handle.conn);
    assert(handle.stmt);
    GmcStmtT *stmt = handle.stmt;

    GmcResetStmt(stmt);

    Status err = GmcExecDirect(stmt, query.data(), query.size());
    auto res = detail::query_result{handle.conn, err};
    detail::gmdb_query_result gm_result;
    gm_result.stmt = std::move(stmt);
    if (err != GMERR_OK) {
        TextT *err_info = DbGetLastErrorInfo();
        assert(err_info);
        assert(err_info->str);
        if (err_info && err_info->str) {
            gm_result.error_message = err_info->str;
        }
    }
    res.set_result(std::move(gm_result));
    return res;
}

std::error_code test_executor::run_test(
    const fs::path &test_file, const std::string &test_name, std::string_view content)
{
    _config.current_test = test_file;

    const char *p = content.data();
    const char *end = content.data() + content.size();

    auto line_no = [b = content.data(), &e = p]() { return get_line_number(b, e); };

    try {
        auto requirements = parse_requirements(p, end);
        if (!requirements_satisified(requirements, _config.features)) {
            std::stringstream ss;
            ss << "unmet requirements: [";
            join_collections(ss, requirements, " && ");
            ss << "]";

            ci.testIgnored(test_name, ss.str());
            return error::unmet_requirement;
        }
    } catch (const std::exception &ex) {
        auto unparsed = std::string_view{p, static_cast<size_t>(std::distance(p, end))};
        ci.testFailed(test_name, "cannot parse requirements",
            "unparsed " + test_name + ":" + std::to_string(line_no()) + ": " + std::string(unparsed) +
                ", error: " + ex.what());
        return error::unparsed_requirement;
    }

    // The replacement cache lives for the whole execution of a test script.
    cache_map replacement_cache;

    execution_result er;

    std::string details;

    std::string last_query;
    detail::output_result run_output;
    detail::query_result result{_handle.conn, {}};

    std::string query;
    while (p != end) {
        std::string query_part;
        auto maybe_query = parse_query(p, end);
        if (!maybe_query) {
            ci.testFailed(
                test_name, "cannot parse query", "unparsed " + test_name + ":" + std::to_string(line_no()) + ": " + p);
            return error::unparsed_query;
        }

        query_part = *maybe_query;
        if (!query_part.empty()) {
            log::emit(log::detailed, "QUERY: ", query_part);
        }

        expectation exp;
        try {
            // Parse comment.
            auto maybe_exp = parse_query_comment(p, end);
            if (!maybe_exp) {
                ci.testFailed(test_name, "cannot parse comment",
                    "unparsed " + test_name + ":" + std::to_string(line_no()) + ": " + p);
                return error::unparsed_expectation;
            }
            exp = *maybe_exp;
        } catch (const std::exception &ex) {
            auto unparsed = std::string_view{p, static_cast<size_t>(std::distance(p, end))};
            ci.testFailed(test_name, "cannot parse comment",
                "unparsed " + test_name + ":" + std::to_string(line_no()) + ": " + std::string(unparsed) +
                    ", error: " + ex.what());
            return error::unparsed_expectation;
        }

        bool is_end_of_query = ends_with(query_part, ';')  // Queries should be terminated by a semicolon.
                               || !std::holds_alternative<expect_nothing>(exp.value)  // Or an expectation.
            ;

        query += query_part;
        // detail::trim(query); // Trimming should not be necessary.

        if (is_end_of_query && !query.empty()) {
            // Run query.
            if (std::holds_alternative<expect_nothing>(exp.value)) {
                exp.value = expect_success{};
            }

            last_query = query;
            try {
                last_query = apply_replacements(_config, replacement_cache, last_query);
            } catch (const std::exception &ex) {
                ci.contextMessage("cannot apply replacement in " + test_name + ":" + std::to_string(line_no()) + ": '" +
                                  last_query + "' + error: " + ex.what());
                ++er.failures;
                break;
            }
            log::emit(log::debug, "full query: ", last_query);

            result = execute_query(_handle, last_query);

            query = {};
        }

        if (!std::holds_alternative<expect_nothing>(exp.value)) {
            log::emit(log::debug, "expectation: ", exp);
        }

        // Check error code and result.
        // Report error.
        // Retrieve run output
        expectation_interpreter interpreter{run_output, result, _config, replacement_cache};
        check_result cr = interpreter.visit(last_query, exp);
        switch (cr) {
            case check_result::nothing:
                break;
            case check_result::success:
                ++er.successes;
                break;
            case check_result::failure:
                ++er.failures;
                break;
        }
    }

    details += "\n* assertions passed: " + std::to_string(er.successes);
    details += "\n* assertions failed: " + std::to_string(er.failures);

    if (should_fail(test_file)) {
        // We might be stricter and check that only the last expectation failed, not any other.

        if (er.failures == 0u) {
            // But did not fail.
            ci.testFailed(test_name, "unexpected pass", "should have failed according to its extension");
            return error::should_have_failed;
        }
    } else if (er.failures > 0u) {
        ci.testFailed(test_name, "unexpected fail", details);
        return error::should_have_succeeded;
    }

    return {};
}

std::error_code test_executor::run_test(const fs::path &test_file, const std::string &test_name)
{
    try {
        // Read file.
        std::string content;
        if (auto ec = io::read_file_into_string(test_file, content)) {
            ci.testFailed(test_name, "cannot read file", ec.message());
            return ec;
        }

        return run_test(test_file, test_name, content);
    } catch (const std::exception &ex) {
        ci.testFailed(test_name, "caught exception", ex.what());
        return error::unknown;
    }
}

std::error_code test_executor::operator()(const fs::path &test_file)
{
    std::error_code ec;
    const auto &rel_path = fs::relative(test_file, _config.root, ec);
    (void)ec;  // Maybe unused.
    assert(!ec);
    assert(!_config.root.empty());

    std::string test_name = (rel_path.parent_path() / rel_path.stem()).generic_string();
    std::replace(test_name.begin(), test_name.end(), '.', '-');
    test_name = std::string("query tests: ") + test_name;
    std::replace(test_name.begin(), test_name.end(), '/', '.');

    ci.testStarted(test_name);
    ci.contextStarted();

    const auto start = chr::steady_clock::now();

    ec = run_test(test_file, test_name);

    const auto stop = chr::steady_clock::now();
    const auto elapsed = stop - start;

    ci.contextFinished();
    ci.testFinished(test_name, static_cast<int>(chr::duration_cast<chr::milliseconds>(elapsed).count()));
    return ec;
}

bool log_on_error(std::string_view context, Status err, log::level lvl)
{
    if (err != GMERR_OK) {
        TextT *err_info = DbGetLastErrorInfo();
        assert(err_info);
        auto msg = std::string(context) + std::string(" failed: ") + std::string(err_info->str) + " (" +
                   std::to_string(err) + ")";
        log::emit(lvl, msg);
        return false;
    }
    return true;
}

void throw_on_error(std::string_view context, Status err, log::level lvl)
{
    if (err != GMERR_OK) {
        TextT *err_info = DbGetLastErrorInfo();
        assert(err_info);
        auto msg = std::string(context) + std::string(" failed: ") + std::string(err_info->str) + " (" +
                   std::to_string(err) + ")";
        log::emit(lvl, msg);
        throw std::runtime_error(msg);
    }
}

gmc_connection_handle connect(const std::string &uri)
{
    log::emit(log::info, "connecting to cluster ", uri);

    gmc_connection_handle h;
    throw_on_error("GmcConnOptionsCreate", GmcConnOptionsCreate(&h.opts));
    try {
        throw_on_error("GmcConnOptionsSetServerLocator", GmcConnOptionsSetServerLocator(h.opts, uri.data()));
        throw_on_error("GmcConnect", GmcConnect(GMC_CONN_TYPE_SYNC, h.opts, &h.conn));
        // We alloc a statement when connecting and then reset it before executing each query.
        // This is due to a buggy behaviour of GmcFreeStmt (assertion failure).
        throw_on_error("GmcAllocStmt", GmcAllocStmt(h.conn, &h.stmt));
        // Set client-server (CS) mode: disable direct read.
        throw_on_error("GmcConnOptionsSetCSRead", GmcConnOptionsSetCSRead(h.opts));
    } catch (...) {
        // If we throw above, we need to clean up options.
        assert(h.opts);
        GmcConnOptionsDestroy(h.opts);
        throw;
    }

    // Clean options as soon as possible.
    assert(h.opts);
    GmcConnOptionsDestroy(h.opts);

    return h;
}

void disconnect(gmc_connection_handle h)
{
    log::emit(log::info, "disconnecting from cluster");

    assert(h.stmt);
    GmcFreeStmt(h.stmt);

    assert(h.conn);
    log_on_error("GmcDisconnect", GmcDisconnect(h.conn), log::warning);

    // Options are cleaned up in connect().
}

}  // namespace gmdb::test
