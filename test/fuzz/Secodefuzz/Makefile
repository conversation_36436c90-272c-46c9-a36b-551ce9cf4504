#$(warning  $(XXX))

compiler 			= gcc

ifeq ($(compiler) , clang)    			#########################################
CFLAGS_llvm 		= -Wall -g -fPIC -fno-omit-frame-pointer -O0 -fsanitize=address -fsanitize=leak -fsanitize-coverage=trace-pc-guard,indirect-calls,trace-cmp,trace-div,trace-gep
CFLAGS_normal  		= -Wall -g -fPIC -fno-omit-frame-pointer -O0 -fsanitize=address -fsanitize=leak
CFLAGS_libFuzzer 	= -Wall -g -fPIC -fno-omit-frame-pointer -O0 -fsanitize=address -fsanitize=leak -std=c++11#
LDFLAGS_Secodefuzz		= -lm
LDFLAGS_llvm		= -lm -lpthread
CC      			= /usr/lib/llvm-4.0/bin/clang
CXX					= /usr/lib/llvm-4.0/bin/clang++
else ifeq ($(compiler) , gcc)    		#########################################
CFLAGS_llvm			= -Wall -g -fPIC -fno-omit-frame-pointer -O0 -fsanitize=address -fsanitize=leak -fsanitize-coverage=trace-pc
CFLAGS_normal  		= -Wall -g -fPIC -fno-omit-frame-pointer -O0 -fsanitize=address -fsanitize=leak
CFLAGS_libFuzzer 	= -Wall -g -fPIC -fno-omit-frame-pointer -O0 -fsanitize=address -fsanitize=leak -std=c++11#
LDFLAGS_Secodefuzz		= -lm /usr/lib/gcc/aarch64-linux-gnu/7.3.0/libasan.so
LDFLAGS_llvm		= -lm -lpthread -lstdc++  -L /usr/lib/gcc/aarch64-linux-gnu/7.3.0/
CC      			= /usr/bin/gcc
CXX					= /usr/bin/gcc
else ifeq ($(compiler) , gcc_normal) 	######################################### only for example_Secodefuzz_a
CFLAGS_llvm			= -Wall -g -fPIC -fno-omit-frame-pointer -O0
CFLAGS_normal  		= -Wall -g -fPIC -fno-omit-frame-pointer -O0
CFLAGS_libFuzzer 	= -Wall -g -fPIC -fno-omit-frame-pointer -O0 #-std=c++11#
LDFLAGS_Secodefuzz		= -lm
LDFLAGS_llvm		= -lm -lpthread -lstdc++
CC      			= gcc
CXX					= gcc
endif    								#########################################

LIB     			= ar -ru   ## ar -crv
SHELL   			= sh
RM      			= rm -rf
MKDIR   			= mkdir -p


current_dir			=$(shell pwd)

######libfuzz
#SRC_DIR_libFuzzer	= ../Libfuzzer/Fuzzer
#DIR_libFuzzer		= ./obj_libFuzzer
#SRC_libFuzzer	 	= $(wildcard ${SRC_DIR_libFuzzer}/*.cpp)
#OBJ_libFuzzer 		= $(patsubst %.cpp,${DIR_libFuzzer}/%.o,$(notdir ${SRC_libFuzzer}))


######mutators
SRC_DIR_Secodefuzz 	= ./src/mutators
COMMON_SRC_DIR 		= ./src/common
TEST_SRC_DIR 		= ./src/test
DIR_Secodefuzz 		= ./obj_Secodefuzz
LIB_Secodefuzz          = ./src/ex_lib
SRC_Secodefuzz  		= $(wildcard ${SRC_DIR_Secodefuzz}/*.c)
SRC_Secodefuzz  		+= $(wildcard ${COMMON_SRC_DIR}/*.c)
SRC_Secodefuzz  		+= $(wildcard ${TEST_SRC_DIR}/*.c)
OBJ_Secodefuzz 		= $(patsubst %.c,${DIR_Secodefuzz}/%.o,$(notdir ${SRC_Secodefuzz}))

all:Secodefuzz_so Secodefuzz_a Secodepits_so
#Libfuzzer_a Libfuzzer_so example_Secodefuzz_so example_Libfuzzer_so  example_Secodefuzz_a example_Libfuzzer_a example_Secodefuzz_xml_a

########################################target_lib.so
#target_lib_so:
#	@$(CC) $(CFLAGS_llvm) $(LDFLAGS_Secodefuzz) ./target/target_lib/target_lib.c -shared -o target_lib.so
#	@echo "*******************creat target_lib.so success"

xml_so:
	@$(CC) $(CFLAGS_llvm) $(LDFLAGS_Secodefuzz) \
    ./src/xml/Xlib.c \
	./src/xml/XParseXml.c \
	./src/xml/xparsexml/XParseXml_Config.c \
	./src/xml/xparsexml/XParseXml_Test.c \
	./src/xml/xparsexml/XParseXml_StateModel.c \
	./src/xml/xparsexml/XParseXml_DataModel.c \
	./src/xml/XParseDataModel.c \
	./src/xml/XParseStateModel.c \
	./src/xml/XParseTestModel.c \
	./src/xml/XParseAssociated.c \
	./src/xml/XParseBin.c \
	./src/xml/XCommon.c \
	./src/xml/XDebug.c \
	./src/xml/XFileIO.c \
	./src/xml/XPadding.c \
	./src/xml/XRelation.c \
	./src/xml/XFixup.c \
	./src/xml/xfixup/XFixupIcmpChecksum.c \
	./src/xml/xfixup/XFixupUDPChecksum.c \
	./src/xml/XTransformer.c \
	./src/xml/XPublisher.c \
	./src/xml/xpublisher/XPublisher_raw.c \
	./src/xml/xpublisher/XPublisher_udp.c \
	./src/xml/xpublisher/XPublisher_tcp.c \
	./src/xml/xpublisher/XPublisher_debug.c \
	./src/xml/xpublisher/XPublisher_printf.c \
	./src/xml/xpublisher/XPublisher_file.c \
	./src/xml/XAction.c \
	./src/xml/XState.c \
	./src/xml/XGetMutatorValue.c \
	./src/xml/XEXfunction.c \
	-shared -o $(LIB_Secodefuzz)/xml.so
	@echo "*******************creat xml_so.so success"

Secodepits_so:xml_lib_so
	@$(CC) $(CFLAGS_secodefuzz) \
	../Secodepits/xml/Xlib.c \
	../Secodepits/xml/XParseHW.c \
	../Secodepits/xml/XParseXml.c \
	../Secodepits/xml/xparsexml/XParseXml_Config.c \
	../Secodepits/xml/xparsexml/XParseXml_TestModel.c \
	../Secodepits/xml/xparsexml/XParseXml_StateModel.c \
	../Secodepits/xml/xparsexml/XParseXml_DataModel.c \
	../Secodepits/xml/xparsepits/XParsePits_DataModel.c \
	../Secodepits/xml/xparsepits/XParsePits_StateModel.c \
	../Secodepits/xml/xparsepits/XParsePits_TestModel.c \
	../Secodepits/xml/xparsepits/XParsePits_Associated.c \
	../Secodepits/xml/xparsepits/XParsePits_Bin.c \
	../Secodepits/xml/XCommon.c \
	../Secodepits/xml/XDebug.c \
	../Secodepits/xml/XFileIO.c \
	../Secodepits/xml/xdo/XDoPadding.c \
	../Secodepits/xml/xdo/XDoRelation.c \
	../Secodepits/xml/xdo/XDoFixup.c \
	../Secodepits/xml/xdo/XDoAnalyzer.c \
	../Secodepits/xml/xdo/XDoConstraint.c \
	../Secodepits/xml/xdo/XDoTransformer.c \
	../Secodepits/xml/xdo/XDoPublisher.c \
	../Secodepits/xml/xdo/XDoAction.c \
	../Secodepits/xml/xdo/XDoState.c \
	../Secodepits/xml/xself/xfixup/XFixupIcmpChecksum.c \
	../Secodepits/xml/xself/xfixup/XFixupUDPChecksum.c \
	../Secodepits/xml/xself/xfixup/XFixupcrc32.c \
	../Secodepits/xml/xself/xanalyzer/XAnalyaerXml.c \
	../Secodepits/xml/xself/xanalyzer/XAnalyaerJson.c \
	../Secodepits/xml/xself/xanalyzer/cJSON.c \
	../Secodepits/xml/xself/xexpression/XExpression.c \
	../Secodepits/xml/xself/xconstraint/XConstraint.c \
	../Secodepits/xml/xself/xtransformer/XTransformerInserta.c \
	../Secodepits/xml/xself/xpublisher/XPublisher_raw.c \
	../Secodepits/xml/xself/xpublisher/XPublisher_udp.c \
	../Secodepits/xml/xself/xpublisher/XPublisher_tcp.c \
	../Secodepits/xml/xself/xpublisher/XPublisher_debug.c \
	../Secodepits/xml/xself/xpublisher/XPublisher_printf.c \
    ../Secodepits/xml/xself/xpublisher/XPublisher_buf.c \
	../Secodepits/xml/xself/xpublisher/XPublisher_file.c \
	../Secodepits/xml/xmutators/XMutatorElement.c \
	../Secodepits/xml/xmutators/XMutatorPits.c \
	../Secodepits/xml/XExternal.c \
	-fPIC -shared libxml_lib.so -o ${LIB_Secodefuzz}/libSecodepits.so
	@echo "*******************creat Secodepits_so.so success"
	mv libxml_lib.so ${LIB_Secodefuzz}/

xml_lib_so:
	@$(CC) $(CFLAGS_secodefuzz) \
	../Secodepits/ex_lib/xml/Eparser.c \
	../Secodepits/ex_lib/xml/Echvalid.c \
	../Secodepits/ex_lib/xml/Edict.c \
	../Secodepits/ex_lib/xml/Eencoding.c \
	../Secodepits/ex_lib/xml/Eentities.c \
	../Secodepits/ex_lib/xml/Eerror.c \
	../Secodepits/ex_lib/xml/Elist.c \
	../Secodepits/ex_lib/xml/EparserInternals.c \
	../Secodepits/ex_lib/xml/ESAX2.c \
	../Secodepits/ex_lib/xml/Etree.c \
	../Secodepits/ex_lib/xml/Euri.c \
	../Secodepits/ex_lib/xml/Evalid.c \
	../Secodepits/ex_lib/xml/Exmlstring.c \
	../Secodepits/ex_lib/xml/ExmlIO.c \
	-fPIC -shared -o libxml_lib.so
	@echo "*******************creat libxml_lib.so success"

########################################
#example_Secodefuzz_xml_a:Secodefuzz_a xml_so xml_lib_so target_lib_so
#	@$(CC) $(CFLAGS_normal) $(LDFLAGS_Secodefuzz) ./example_Secodefuzz/testcase_Secodefuzz_linux.c ./xml_lib.so ./xml.so ./target_lib.so ./Secodefuzz.a  -o example_Secodefuzz_xml_a
#	@echo "*******************creat example_Secodefuzz_xml_a success"
#
#./secpeach -X  xml_data/JPG_Data.xml  -t test_jpg -C 10000./secpeach -X  xml_data/JPG_Data.xml  -t test_jpg -C 10000
#secpeach:Secodefuzz_a target_lib_so xml_so xml_lib_so
#	@$(CC) $(CFLAGS_normal) $(LDFLAGS_Secodefuzz) ./example_Secodefuzz/secpeach/secpeach.c ./xml_lib.so ./xml.so ./target_lib.so ./Secodefuzz.a -o secpeach
#	@echo "*******************creat secpeach success"

#example_Secodefuzz_tool_a:Secodefuzz_a target_lib_so
#	@$(CC) $(CFLAGS_normal) $(LDFLAGS_Secodefuzz) ./example_Secodefuzz/testcase_tool/testcase_Secodefuzz_tool.c ./target_lib.so ./Secodefuzz.a -o example_Secodefuzz_tool_a
#	@echo "*******************creat example_Secodefuzz_a success"
#
#example_Secodefuzz_a:Secodefuzz_a target_lib_so
#	@$(CC) $(CFLAGS_llvm) $(LDFLAGS_Secodefuzz) ./example_Secodefuzz/testcase_Secodefuzz_linux.c ./target_lib.so ./Secodefuzz.a -o example_Secodefuzz_a
#	@echo "*******************creat example_Secodefuzz_a success"
#
#example_Secodefuzz_so:Secodefuzz_so target_lib_so
#	@$(CC) $(CFLAGS_normal) $(LDFLAGS_Secodefuzz) ./example_Secodefuzz/testcase_Secodefuzz_linux.c  ./target_lib.so ./Secodefuzz.so  -o example_Secodefuzz_so
#	@echo "*******************creat example_Secodefuzz_so success"
#
${DIR_Secodefuzz}/%.o:${SRC_DIR_Secodefuzz}/%.c
	@$(CC) $(CFLAGS_normal) -c $< -o $@

${DIR_Secodefuzz}/%.o:${COMMON_SRC_DIR}/%.c
	@$(CC) $(CFLAGS_normal) -c $< -o $@

${DIR_Secodefuzz}/%.o:${TEST_SRC_DIR}/%.c
	@$(CC) $(CFLAGS_normal) -c $< -o $@

createObj0:
	@mkdir -p ./obj_Secodefuzz

Secodefuzz_so: createObj0 ${OBJ_Secodefuzz}
	@$(CC) $(CFLAGS_normal) $(LDFLAGS_Secodefuzz) ${OBJ_Secodefuzz} -shared -o $(LIB_Secodefuzz)/libSecodefuzz.so
	@echo "*******************creat libSecodefuzz.so success"

Secodefuzz_a:createObj0 ${OBJ_Secodefuzz}
	@$(LIB) $(LIB_Secodefuzz)/libSecodefuzz.a ${OBJ_Secodefuzz}
	@echo "*******************creat libSecodefuzz.a success"


########################################
#
#example_Libfuzzer_a:Libfuzzer_a target_lib_so
#	@$(CXX) $(CFLAGS_llvm) $(LDFLAGS_llvm) ./example_Libfuzzer/testcase_Libfuzzer.c ./target_lib.so  libFuzzer.a -o example_Libfuzzer_a
#	@echo "*******************creat example_Libfuzzer_a success"
#
#example_Libfuzzer_so:Libfuzzer_so target_lib_so
#	@$(CXX) $(CFLAGS_llvm) $(LDFLAGS_llvm) ./example_Libfuzzer/testcase_Libfuzzer.c  ./target_lib.so ./libFuzzer.so -o example_Libfuzzer_so
#	@echo "*******************creat example_Libfuzzer_so success"
#
#${DIR_libFuzzer}/%.o:${SRC_DIR_libFuzzer}/%.cpp
#	@$(CXX) $(CFLAGS_libFuzzer)  -c $< -o $@
#
#createObj2:
#	@mkdir -p ./obj_libFuzzer
#
#Libfuzzer_so:createObj2 ${OBJ_libFuzzer}
#	@$(CXX) $(CFLAGS_normal) $(LDFLAGS_llvm) ${OBJ_libFuzzer}  -shared -o libFuzzer.so
#	@echo "*******************creat libFuzzer.so success"
#
#Libfuzzer_a:createObj2 ${OBJ_libFuzzer}
#	@$(LIB) libFuzzer.a ${OBJ_libFuzzer}
#	@echo "*******************creat libFuzzer.a success"

clean:
	@$(RM) *.o
	@$(RM) $(DIR_Secodefuzz)
	@$(RM) $(DIR_libFuzzer)
	@$(RM) $(LIB_Secodefuzz)/*.a
	@$(RM) $(LIB_Secodefuzz)/*.so
	@$(RM) *.a
	@$(RM) *.so
#	@$(RM) example_Secodefuzz_a
#	@$(RM) example_Secodefuzz_so
#	@$(RM) example_Libfuzzer_a
#	@$(RM) example_Libfuzzer_so
	@$(RM) secpeach

#LD_PRELOAD=/test/mayp/gcc6.1.0/lib64/libasan.so   ./example_Secodefuzz_a
#ifconfig eth0 10.113.214.88 netmask 255.255.254.0
#route add default gw 10.113.214.1
#export ASAN_SYMBOLIZER_PATH=/usr/bin/addr2line
