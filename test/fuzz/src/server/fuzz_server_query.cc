#include <unordered_map>
#include <vector>
#include "clt_fill_msg.h"
#include "clt_proto.h"
#include "clt_stmt.h"
#include "adpt_define.h"
#include "db_dynmem_algo.h"
#include "db_label_latch_mgr.h"
#include "db_mem_context.h"
#include "db_mem_context_pool.h"
#include "db_rpc_conn_msg.h"
#include "db_rpc_msg_op.h"
#include "db_rpc_msg.h"
#include "db_secure_msg_buffer.h"
#include "db_sysapp_context.h"
#include "dm_meta_res_col_pool.h"
#include "dm_data_define.h"
#include "dm_data_topo.h"
#include "dm_meta_kv_label.h"
#include "db_config.h"
#include "db_table_space.h"
#include "drt_connection.h"
#include "drt_connection_inner.h"
#include "dm_cache_multi_ver_mgr.h"
#include "dm_cache_single_ver_mgr.h"
#include "drt_instance.h"
#include "cpl_dtl_parser.h"
#include "ee_cltcache.h"
#include "cpl_fp_verifier_dml.h"
#include "cpl_public_verifier_dcl.h"
#include "cpl_fp_create_plan_dml.h"
#include "db_mem_context.h"
#include "db_dynmem_algo.h"
#include "db_label_latch_mgr.h"
#include "ee_stmt_interface.h"
#include "adpt_define.h"
#include "ee_cmd.h"
#include "ee_stmt.h"
#include "ee_stmt_interface.h"
#include "srv_data_fastpath.h"
#include "srv_data_prepare.h"
#include "fuzz_msg_buffer_public.h"
#include "fuzz_server.h"
#include "se_table_space_pub.h"
#include "secodeFuzz.h"
#include "dm_data_define.h"
#include "cpl_dtl_online_compiler.h"
#include "se_table_space_pub.h"
#include "cpl_dtl_offline_compiler.h"
#include "clt_stmt.h"
#include "db_secure_msg_buffer.h"
#include "cpl_kv_parser.h"
#include "cpl_fp_parser_dml.h"
#include "cpl_public_parser_ddl.h"
#include "srv_data_service.h"
#include "srv_data_public.h"
#include "drt_connection_inner.h"
#include "cpl_kv_verifier.h"
#include "cpl_public_parser_dcl.h"
#include "cpl_public_parser_dql.h"
#include "cpl_public_verifier_dql.h"
#include "cpl_public_verifier_dcl.h"
#include "cpl_public_verifier_ddl.h"
#include "ptl_datalog_service.h"
#include "dm_meta_key_oper.h"
#include "dm_meta_info_shm.h"
#include "cpl_yang_verifier.h"
#include "cpl_yang_parser.h"
#include "dm_meta_vertex_label.h"
#include "db_top_shmem_ctx.h"
#if defined(FEATURE_MINIKV) || defined(FEATURE_SQL)
#include "db_json.h"
#else
#include "db_json_common.h"
#endif

uint32_t g_count = 0;
DbMemCtxT *g_DymCtxMem = NULL;
DbMemCtxT *g_ShmemCtxMem = NULL;
uint32_t g_opCode = 0;  // 起作用于entry
uint32_t g_scheme_json_id = 0;
uint32_t g_config_json_id = 0;
char *g_subtreeJson = NULL;

using std::vector;
typedef Status (*QryFuzzFunc_t)(const SessionT *session);

typedef Status (*QryParseFunc)(QryStmtT *stmt);
typedef Status (*QryVerifyFunc)(QryStmtT *stmt);

#define MAX_STRING_LEN 530  // 为覆盖越界场景，系统中LabelName最大512
#define FUZZ_OFFSET 10
#define MAX_PARA_NUM 256

DmVertexT *g_DmVertex = NULL;
DmValueT *g_DmValue = NULL;
DmVlIndexLabelT *g_DmPkIndex = NULL;
DmVlIndexLabelT *g_DmSecIndex = NULL;
DmKvLabelT *g_DmKvTable = NULL;  // 主要用于query
char *g_kvTableName = NULL;      // 主要用于entry
uint32_t g_nspId = 0;            //  主要用于entry
DmVertexLabelT *g_DmVertexLabel = NULL;
DmEdgeLabelT *g_DmEdgeLabel = NULL;
DmEdgeT *g_DmEdge = NULL;
DmIndexKeyT *g_dmIndexKey = NULL;
IndexKeyT *g_IndexKey = NULL;
DmSubscriptionT *g_dmSubscription = NULL;

const char *g_edgeCfgJson = "{\"max_record_count\":1000}";
const char *g_edgeLabelJson = R"([{
    "name":"testCypherPathEdgeLabel1",
    "source_vertex_label":"VertexLabelTest",
    "comment":"the edge A to B",
    "dest_vertex_label":"VertexLabelTest",
    "constraint":{
        "operator_type":"and",
        "conditions":[
            {"source_property":"c1","dest_property":"c1"}
        ]
    }
}])";

const char *g_simpleSchema = (char *)R"([{
        "type":"record",
        "name":"T39",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"int32", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"T39",
                    "name":"T39_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";
const char *g_queryFuzzConfigJson = R"({"max_record_count":1000})";

const char *g_queryFuzzdeltaStoreJson = R"({
    "delta_stores":
    [{
        "name": "ds1",
        "init_mem_size": 32768,
        "max_mem_size": 131072,
        "extend_mem_size": 8192,
        "page_size": 4096
    }]
})";

const char *g_subConfig = R"({
    "label_name":"VertexLabelTest",
    "comment":"VertexLabel1 subscription",
    "events":
        [
            {"type":"insert", "msgTypes":["new object", "old object"]}
        ],
    "retry":true,
    "is_reliable":true,
    "constraint":
        {
            "operator_type":"and",
            "conditions":
                [
                    {
                        "property":"c1"
                    }
                ]
        }
})";

const char *g_simpleDatalog = R"(
    %table inp(a:int4, b:int8, c:int4)
    %table out(a:int4, b:int8, c:int4)

    %table inpA(a:int4, b:int8)
    %table inpB(a:int8, b:int1, c:int2)
    %table outTbl(a:int4, b:int1)

    out(a, b, c) :- inp(a, b, c).
    outTbl(a, c) :- inpA(a, b), inpB(b, c, -).
)";

// maybe remove
const char *g_queryFuzzindexKey = "test1";

struct QryFuzzHandler {
    QryTypeE qryType;
    QryParseFunc parse;
    QryVerifyFunc verify;
    QryFillReq fillReq;
};

// add fuzz test, need add to target.txt
// clang-format off
std::unordered_map<MsgOpcodeRpcE, QryFuzzHandler> g_fuzzQueryMap = {
    // KV
    {MSG_OP_RPC_CREATE_KV_TABLE, {QRY_TYPE_CREATE_KV_TABLE, QryParseCreateKvTable, QryVerifyCreateKvTable, QryFuzzFillFixFufferCreateKvTable}},
    {MSG_OP_RPC_GET_KV_TABLE, {QRY_TYPE_GET_KV_TABLE, QryParseGetKvTable, NULL, QryFuzzFillFixFufferGetKvTable}},
    {MSG_OP_RPC_SET_KV, {QRY_TYPE_SET_KV, QryParseSetKv, NULL, QryFuzzFillFixBufferSetKv}},
    {MSG_OP_RPC_GET_KV, {QRY_TYPE_GET_KV, QryParseGetKv, NULL, QryFuzzFillFixFufferGetKv}},
    {MSG_OP_RPC_IS_KV_EXIST, {QRY_TYPE_EXIST_KV, QryParseGetKv, NULL, QryFuzzFillFixFufferKvIsExist}},
    {MSG_OP_RPC_GET_KV_RECORD_COUNT, {QRY_TYPE_COUNT_KV, QryParseCountKv, NULL, QryFuzzFillFixFufferKvRecordCount}},
    {MSG_OP_RPC_SCAN_KV_BEGIN, {QRY_TYPE_SCAN_KV, QryParseScanKvBegin, NULL, QryFuzzFillFixFufferScanKvBegin}},
    {MSG_OP_RPC_DELETE_KV, {QRY_TYPE_DELETE_KV, QryParseDeleteKv, NULL, QryFuzzFillFixBufferDeleteKv}},
    {MSG_OP_RPC_TRUNCATE_KV_TABLE, {QRY_TYPE_TRUNCATE_KV_TABLE, QryParseTruncateKvTable, QryVerifyTruncateKvTable, QryFuzzFillFixFufferTruncateKvTable}},
    {MSG_OP_RPC_DROP_KV_TABLE, {QRY_TYPE_DROP_KV_TABLE, QryParseDropKvTable, NULL, QryFuzzFillFixFufferDropKvTable}},
    // Transaction
    {MSG_OP_RPC_TX_START, {QRY_TYPE_BEGIN_TRANS, QryParseBeginTrans, NULL, QryFuzzFillFixFufferBeginTrans}},
    {MSG_OP_RPC_TX_COMMIT, {QRY_TYPE_COMMIT_TRANS, QryParseCommitTrans, NULL, QryFuzzFillFixFufferCommitTrans}},
    {MSG_OP_RPC_TX_ROLLBACK, {QRY_TYPE_ROLLBACK_TRANS, QryParseRollbackTrans, NULL, QryFuzzFillFixFufferRollbackTrans}},
    {MSG_OP_RPC_TX_ABORT, {QRY_TYPE_ABORT_TRANS, QryParseAbortTrans, QryVerifyTransAbort, QryFuzzFillFixFufferAbortTrans}},
    // DDL
    {MSG_OP_RPC_CREATE_VERTEX_LABEL, {QRY_TYPE_CREATE_VERTEX_LABEL, QryParseCreateVertexLabel, QryVerifyCreateVertexLabel, QryFuzzFillFixFufferCreateVertexLabel}},
    {MSG_OP_RPC_CREATE_EDGE_LABEL, {QRY_TYPE_CREATE_EDGE_LABEL, QryParseCreateEdgeLabel, QryVerifyCreateEdgeLabel, QryFuzzFillFixFufferCreateEdgeLabel}},
    {MSG_OP_RPC_CREATE_SUBSCRIPTION, {QRY_TYPE_CREATE_SUBSCRIPTION, QryParseCreateSubscription, QryVerifyCreateSubscription, QryFuzzFillFixFufferCreateSubscription}},
    {MSG_OP_RPC_CREATE_NAMESPACE, {QRY_TYPE_CREATE_NAMESPACE, QryParseCreateNamespace, QryVerifyCreateNamespace, QryFuzzFillFixFufferCreateNamespace}},
    {MSG_OP_RPC_CREATE_TABLESPACE, {QRY_TYPE_CREATE_TABLESPACE, QryParseCreateTablespace, QryVerifyCreateTablespace, QryFuzzFillFixFufferCreateTablespace}},
    {MSG_OP_RPC_CREATE_USER, {QRY_TYPE_CREATE_USER, QryParseCreateUserBatch, QryVerifyCreateOrDropUserBatch, QryFuzzFillFixFufferCreateUser}},
    {MSG_OP_RPC_CREATE_GROUP, {QRY_TYPE_CREATE_GROUP, QryParseCreateGroup, QryVerifyCreateGroup, QryFuzzFillFixFufferCreateRole}},
    {MSG_OP_RPC_ALTER_VERTEX_LABEL, {QRY_TYPE_ALTER_VERTEX_LABEL, QryParseAlterVertexLabel, NULL, QryFuzzFillFixFufferAlterVertexLabel}},
    {MSG_OP_RPC_DROP_VERTEX_LABEL, {QRY_TYPE_DROP_VERTEX_LABEL, QryParseDropVertexLabel, QryVerifyDropVertexLabel, QryFuzzFillFixFufferDropVertexLabel}},
    {MSG_OP_RPC_DEGRADE_VERTEX_LABEL, {QRY_TYPE_DEGRADE_VERTEX_LABEL, QryParseDegradeVertexLabel, QryVerifyDegradeVertexLabel, QryFuzzFillFixFufferDegradeVertexLabel}},
    {MSG_OP_RPC_DROP_EDGE_LABEL, {QRY_TYPE_DROP_EDGE_LABEL, QryParseDropEdgeLabel, QryVerifyDropEdgeLabel, QryFuzzFillFixFufferDropEdgeLabel}},
    {MSG_OP_RPC_DROP_SUBSCRIPTION, {QRY_TYPE_DROP_SUBSCRIPTION, QryParseDropSubscription, QryVerifyDropSubscription, QryFuzzFillFixFufferDropSubscription}},
    {MSG_OP_RPC_DROP_NAMESPACE, {QRY_TYPE_DROP_NAMESPACE, QryParseDropNamespace, QryVerifyDropNamespace, QryFuzzFillFixFufferDropNamespace}},
    {MSG_OP_RPC_DROP_TABLESPACE, {QRY_TYPE_DROP_TABLESPACE, QryParseDropTablespace, QryVerifyDropTablespace, QryFuzzFillFixFufferDropTablespace}},
    {MSG_OP_RPC_DROP_USER, {QRY_TYPE_DROP_USER, QryParseDropUser, QryVerifyCreateOrDropUser, QryFuzzFillFixFufferDropUser}},
    {MSG_OP_RPC_DROP_GROUP, {QRY_TYPE_DROP_GROUP, QryParseDropGroup, QryVerifyDropGroup, QryFuzzFillFixFufferDropRole}},
    {MSG_OP_RPC_TRUNCATE_VERTEX_LABEL, {QRY_TYPE_TRUNCATE_VERTEX_LABEL, QryParseTruncateVertexLabel, QryVerifyTruncateVertexLabel, QryFuzzFillFixFufferTruncateVertexLabel}},
    {MSG_OP_RPC_TRUNCATE_VERTEX_LABEL_BACKGROUND, {QRY_TYPE_TRUNCATE_VERTEX_LABEL_BACKGROUND, QryParseTruncateVertexLabelBackground, NULL, QryFuzzFillFixFufferTruncateVertexLabelBackground}},
    {MSG_OP_RPC_CREATE_RES_POOL, {QRY_TYPE_CREATE_RES_POOL, QryParseCreateResPool, NULL, QryFuzzFillFixBufferParseCreateResPool}},
    {MSG_OP_RPC_DROP_RES_POOL, {QRY_TYPE_DROP_RES_POOL, QryParseDropResPool, NULL, QryFuzzFillFixBufferParseDropResPool}},
    {MSG_OP_RPC_BIND_EXTENDED_RES_POOL, {QRY_TYPE_BIND_EXTEND_RES_POOL, QryParseBindExtendResPool, NULL, QryFuzzFillFixBufferParseBindExtendResPool}},
    {MSG_OP_RPC_UNBIND_EXTENDED_RES_POOL, {QRY_TYPE_UNBIND_EXTEND_RES_POOL, QryParseUnbindExtendResPool, NULL, QryFuzzFillFixBufferParseUnbindExtendResPool}},
    {MSG_OP_RPC_BIND_RES_POOL_TO_VERTEX_LABEL, {QRY_TYPE_BIND_RES_POOL_TO_VERTEX_LABEL, QryParseBindResPoolToVertexLabel, NULL, QryFuzzFillFixBufferParseBindResPoolToVertexLabel}},
    {MSG_OP_RPC_UNBIND_RES_POOL_TO_VERTEX_LABEL, {QRY_TYPE_UNBIND_RES_POOL_TO_VERTEX_LABEL, QryParseUnbindResPoolToVertexLabel, NULL, QryFuzzFillFixBufferParseUnbindResPoolToVertexLabel}},
    {MSG_OP_RPC_BEGIN_CHECK, {QRY_TYPE_BEGIN_CHECK, QryParseBeginCheck, QryVerifyBeginOrEndCheck, QryFuzzFillFixBufferParseBeginCheck}},
    {MSG_OP_RPC_END_CHECK, {QRY_TYPE_END_CHECK, QryParseEndCheck, QryVerifyBeginOrEndCheck, QryFuzzFillFixBufferParseEndCheck}},
    {MSG_OP_RPC_MODIFY_ALARM_THRESHOLD, {QRY_TYPE_MODIFY_ALARM_THRESHOLD, QryParseModifyAlarmThreshold, QryVerifyModifyAlarmThreshold, QryFuzzFillFixBufferChangeAlmThreshold}},
    {MSG_OP_RPC_BIND_NSP_TO_TSP , {QRY_TYPE_BIND_NSP_TO_TSP, QryParseBindNspToTsp, QryVerifyBindNspToTsp, QryFuzzFillFixFufferBindNspToTsp}},
    {MSG_OP_RPC_YANG_VALIDATION_MODEL, {QRY_TYPE_YANG_VALIDATION_MODEL, NULL, NULL, QryFuzzFillFixFufferYangValidationModel}},
#ifndef FEATURE_PERSISTENCE
    {MSG_OP_RPC_DUPLICATE_VERTEX_LABEL, {QRY_TYPE_DUPLICATE_VERTEX_LABEL, QryParseDuplicateVertexLabel, QryVerifyDuplicateVertexLabel, QryFuzzFillFixBufferDuplicateVertexLabel}},
#endif
#ifdef WARM_REBOOT
    {MSG_OP_RPC_END_ALL_PARTITION_CHECK, {QRY_TYPE_END_ALL_PARTITION_CHECK, QryParseEndAllPartitionCheck, QryVerifyEndAllPartitionCheck, QryFuzzFillFixBufferEndAllPartitionCheck}},
#endif
    // DML
    {MSG_OP_RPC_INSERT_VERTEX, {QRY_TYPE_INSERT_VERTEX, QryParseInsertVertex, QryVerifyInsertVertex, QryFuzzFillFixBufferVerifyInsertVertex}},
    {MSG_OP_RPC_UPDATE_VERTEX, {QRY_TYPE_UPDATE_VERTEX, QryParseUpdateVertex, QryVerifyUpdateVertex, QryFuzzFillFixBufferUpdateVertex}},
    {MSG_OP_RPC_DELETE_VERTEX, {QRY_TYPE_DELETE_VERTEX, QryParseDeleteVertex, QryVerifyDeleteVertex, QryFuzzDeleteVertex}},
    {MSG_OP_RPC_REPLACE_VERTEX, {QRY_TYPE_REPLACE_VERTEX, QryParseReplaceVertex, QryVerifyReplaceVertex, QryFuzzFillFixBufferReplaceVertex}},
    {MSG_OP_RPC_MERGE_VERTEX, {QRY_TYPE_MERGE_VERTEX, QryParseMergeVertex, QryVerifyMergeVertex, QryFuzzFillFixBufferMergeVertex}},
    {MSG_OP_RPC_INSERT_EDGE, {QRY_TYPE_INSERT_EDGE, QryParseInsertEdge, NULL, QryFuzzFillFixBufferInsertEdge}},
    {MSG_OP_RPC_DELETE_EDGE, {QRY_TYPE_DELETE_EDGE, QryParseDeleteEdge, NULL, QryFuzzFillFixBufferDeleteEdge}},
    {MSG_OP_RPC_UPDATE_CHECK_VERSION, {QRY_TYPE_UPDATE_CHECK_VERSION, QryParseUpdateCheckVersion, QryVerifyUpdateCheckVersion, QryFuzzFillFixFufferUpdateCheckVersion}},
    {MSG_OP_RPC_CHECK_REPLACE, {QRY_TYPE_CHECK_REPLACE, QryParseCheckReplace, QryVerifyCheckReplace, QryFuzzFillFixFufferCheckReplace}},
    {MSG_OP_RPC_NONE_VERTEX, {QRY_TYPE_NONE_VERTEX, QryParseNoneVertex, QryVerifyNoneVertex, QryFuzzFillFixFufferNoneVertex}},
    // DCL
    {MSG_OP_RPC_USE_NAMESPACE, {QRY_TYPE_USE_NAMESPACE, QryParseUseNamespace, QryVerifyUseNamespace, QryFuzzFillFixBufferParseUseNamespace}},
    {MSG_OP_RPC_GRANT_USER_SYSTEM_PRIVS, {QRY_TYPE_GRANT_SYS_PRIVS, QryParseGrantSysPrivs, QryVerifyGrantSysPrivs, QryFuzzFillFixFufferGrantSysPrivsToUser}},
    {MSG_OP_RPC_REVOKE_USER_SYSTEM_PRIVS, {QRY_TYPE_REVOKE_SYS_PRIVS, QryParseRevokeSysPrivs, QryVerifyRevokeSysPrivs, QryFuzzFillFixFufferRevokeSysPrivsFromUser}},
    {MSG_OP_RPC_GRANT_OBJECT_PRIVS, {QRY_TYPE_GRANT_OBJ_PRIVS, QryParseGrantObjPrivs, QryVerifyGrantObjPrivs, QryFuzzFillFixFufferGrantObjPrivsToUser}},
    {MSG_OP_RPC_REVOKE_OBJECT_PRIVS, {QRY_TYPE_REVOKE_OBJ_PRIVS, QryParseRevokeObjPrivs, QryVerifyRevokeObjPrivs, QryFuzzFillFixFufferRevokeObjPrivsFromUser}},
    {MSG_OP_RPC_SET_CFG, {QRY_TYPE_SET_CFG, QryParseSetCfg, NULL, QryFuzzFillFixFufferSetCfg}},
    {MSG_OP_RPC_FLUSH_DATA, {QRY_TYPE_FLUSH_DATA, QryParseFlushData, NULL, QryFuzzFillFixFufferFlushData}},
    {MSG_OP_RPC_SET_SCHEDULE_TASK_CFG, {QRY_TYPE_SET_SCHEDULE_TASK_CFG, QryParseSetScheduleTaskCfg, NULL, QryFuzzFillFixFufferSetScheduleTaskCfg}},
    {MSG_OP_RPC_SET_USER_REQUEST_WEIGHT, {QRY_TYPE_SET_USER_REQUEST_WEIGHT, QryParseSetUserRequestWeight, QryVerifySetUserRequestWeight, QryFuzzFillFixFufferSetUserRequestWeight}},
    {MSG_OP_RPC_IPS_REJECT_OR_ACCEPT_PROCESS,{QRY_TYPE_IPS_REJECT_OR_ACCEPT_PROCESS,QryParseRejectOrAcceptProcess, QryVerifyRejectOrAcceptProcess, QryFuzzFillFixBufferRejectOrAcceptProcess}},
    {MSG_OP_RPC_IPS_SET_USER_RES_LIMIT, {QRY_TYPE_IPS_SET_USER_RESOURCE_LIMIT, QryParseSetUserResourceLimit, QryVerifySetUserResourceLimit, QryFuzzFillFixBufferSetUserResourceLimit}},
    {MSG_OP_RPC_IPS_CLOSE_CONNECTION, {QRY_TYPE_IPS_CLOSE_CONNECTION, QryParseIpsCloseConnection, QryVerifyIpsCloseConnection, QryFuzzFillFixBufferParseCloseConn}},
    {MSG_OP_RPC_TX_CREATE_SAVEPOINT, {QRY_TYPE_CREATE_SAVEPOINT, QryParseSavepoint, QryVerifySavepoint, QryFuzzFillFixBufferParseCreateSavepoint}},
    {MSG_OP_RPC_TX_RELEASE_SAVEPOINT, {QRY_TYPE_RELEASE_SAVEPOINT, QryParseSavepoint, QryVerifySavepoint, QryFuzzFillFixBufferParseReleaseSavepoint}},
    {MSG_OP_RPC_TX_ROLLBACK_SAVEPOINT, {QRY_TYPE_ROLLBACK_SAVEPOINT, QryParseSavepoint, QryVerifySavepoint, QryFuzzFillFixBufferParseRollbackSavepoint}},
    {MSG_OP_RPC_IPS_SET_USER_MODE, {QRY_TYPE_IPS_SET_USER_MODE, QryParseIpsSetUserMode, QryVerifyIpsSetUserMode, QryFuzzFillFixFufferIpsSetUserMode}},
    {MSG_OP_RPC_IPS_SET_USER_PROTOCOL, {QRY_TYPE_IPS_SET_USER_PROTOCOL, QryParseIpsSetUserProtocol, QryVerifyIpsSetUserProtocol, QryFuzzFillFixFufferIpsSetUserProtocol}},
    {MSG_OP_RPC_SET_LOG_LEVEL, {QRY_TYPE_SET_LOG_LEVEL, QryParseSetLogLevel, QryVerifySetLogLevel, QryFuzzFillFixFufferSetLogLevel}},
    {MSG_OP_RPC_SET_LOG_SWITCH, {QRY_TYPE_SET_LOG_SWITCH, QryParseSetLogSwitch, QryVerifySetLogSwitch, QryFuzzFillFixFufferSetLogSwitch}},
    {MSG_OP_RPC_TX_CHECK_OPTIMISTIC_CONFLICT, {QRY_TYPE_CHECK_TX_OPTIMISTIC_CONFLICT, QryParseCheckTxOptimisticConflict, NULL, QryFuzzFillFixFufferCheckTxOptimisticConflict}},
    // DQL(get metadata)
    {MSG_OP_RPC_GET_VERTEX_LABEL, {QRY_TYPE_GET_VERTEX_LABEL, QryParseGetVertexLabel, NULL, QryFuzzFillFixFufferGetVertexLabel}},
    {MSG_OP_RPC_GET_LABEL, {QRY_TYPE_GET_LABEL, QryParseGetLabel, NULL, QryFuzzFillFixFufferGetLabel}},
    {MSG_OP_RPC_GET_RES_POOL, {QRY_TYPE_GET_RES_POOL, QryParseGetResPool, NULL, QryFuzzFillFixFufferGetResPool}},
    {MSG_OP_RPC_GET_EDGE_LABEL, {QRY_TYPE_GET_EDGE_LABEL, QryParseGetEdgeLabel, QryVerifyGetEdgeLabel, QryFuzzFillFixFufferGetEdgeLabel}},
    {MSG_OP_RPC_GET_CFG, {QRY_TYPE_GET_CFG, QryParseGetCfg, NULL, QryFuzzFillFixFufferGetCfg}},
    {MSG_OP_RPC_GET_CHECK_INFO, {QRY_TYPE_GET_CHECK_INFO, QryParseGetCheckInfo, QryVerifyGetCheckInfo, QryFuzzFillFixFufferGetCheckInfo}},
    {MSG_OP_RPC_GET_ALARM_DATA, {QRY_TYPE_GET_ALARM_DATA, QryParseGetAlarmData, QryVerifyGetAlarmData, QryFuzzFillFixFufferGetAlarmData}},
    {MSG_OP_RPC_GET_ESTIMATE_MEMORY, {QRY_TYPE_ESTIMATE_MEMORY, QryParseGetEstimateMemory, NULL, QryFuzzFillFixFufferGetEstimateMemory}},
    {MSG_OP_RPC_GET_STAT_COUNT, {QRY_TYPE_GET_STAT_COUNT, QryParseGetStatCount, NULL, QryFuzzFillFixFufferGetStatCount}},
    // DQL
    {MSG_OP_RPC_SCAN_VERTEX_BEGIN, {QRY_TYPE_SCAN_VERTEX, QryParseScanVertexBegin, QryVerifyScanVertexBegin, QryFuzzFillFixFufferScanVertexBegin}},
    {MSG_OP_RPC_BEGIN_SCAN_PATH, {QRY_TYPE_SCAN_PATH, QryParseBeginScanPath, QryVerifyBeginScanPath, QryFuzzFillFixFufferBeginScanPath}},
    {MSG_OP_RPC_GET_COUNT, {QRY_TYPE_GET_COUNT, QryParseGetCount, QryVerifyGetCount, QryFuzzFillFixFufferGetCount}},
    {MSG_OP_RPC_SUBTREE_FILTER, {QRY_TYPE_SUBTREE_FILTER, QryParseSubtreeFilter, NULL, QryFuzzFillSubtreeFilter}},
    {MSG_OP_RPC_TRIGGER_SCAN, {QRY_TYPE_TRIGGER_SCAN_VERTEX, QryParseTriggerScan, QryVerifyTriggerScan, QryFuzzFillFixFufferTriggerScan}},
    {MSG_OP_RPC_GET_LABEL_DEGRADE_PROGRESS, {QRY_TYPE_GET_VERTEX_LABEL_DEGRADE_PROGRESS, QryParseGetVertexLabelDegradeProgress, QryVerifyGetVertexLabelDegradeProgress, QryFuzzFillGetVertexLabelDegradeProgress}},
    {MSG_OP_RPC_GET_VERTEX_LABELS, {QRY_TYPE_GET_MULTI_VERTEX_LABEL, QryParsePrefetchLabels, NULL, QryFuzzFillPrefetchLabels}},
#ifdef EXPERIMENTAL_NERGC
    {MSG_OP_RPC_GET_INVALID_LABEL_BY_LABEL_LIST, {QRY_TYPE_GET_INVALID_LABEL_BY_LABEL_LIST, QryParseGetInvalidLabelByLabelList, NULL, QryFuzzFillFixBufferGetInvalidLabelByLabelList}},
#endif
};
// clang-format on

// DmSubscriptionT subTest
DmSubscriptionT *GetDmSubscriptionTForFuzz(uint32_t subsId)
{
    auto &g_subName_name = "subTest";
    auto &g_label_name = "VertexLabelTest";
    DmSubscriptionT *subscription =
        (DmSubscriptionT *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, sizeof(DmSubscriptionT));
    subscription->creator = NULL;
    subscription->metaCommon.refCount = 0;
    subscription->metaCommon.isDeleted = false;
    subscription->metaCommon.metaId = subsId;
    char subsName[DM_MAX_NAME_LENGTH];
    sprintf_s(subsName, DM_MAX_NAME_LENGTH, "%s", g_subName_name);
    uint32_t metaNameLen = strlen(subsName) + 1;
    subscription->metaCommon.metaName = (char *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, metaNameLen);
    errno_t err = strcpy_s(subscription->metaCommon.metaName, metaNameLen, subsName);
    subscription->commentLen = strlen("comment3") + 1;
    subscription->comment = (char *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, subscription->commentLen);
    err = strcpy_s(subscription->comment, subscription->commentLen, "comment3");
    subscription->metaCommon.dbId = 0;
    subscription->metaCommon.namespaceId = 0;
    subscription->labelId = 1;
    subscription->labelNameLen = sizeof(g_label_name);
    subscription->labelName = (char *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, subscription->labelNameLen);
    sprintf_s(subscription->labelName, subscription->labelNameLen, "%s", g_label_name);
    subscription->labelType = VERTEX_LABEL;
    subscription->subsJson.str = NULL;
    subscription->subsJson.len = 0;
    subscription->subMsgTypes[DM_SUBS_EVENT_INSERT] = DM_SUBS_MSG_NEW_DATA;
    DbLinkedListInit(&subscription->channelList);
    char *buf = (char *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, sizeof(DmChannelNodeT) + 5);
    DmChannelNodeT *chanNode = (DmChannelNodeT *)buf;
    chanNode->isUnBind = 0;
    chanNode->channelName.len = 5;
    char *str = (char *)(buf + sizeof(DmChannelNodeT));
    err = strcpy_s(str, 5, "ffff");
    chanNode->channelName.str = str;
    subscription->channelCount++;
    DbLinkedListAppend(&subscription->channelList, &chanNode->linkedNode);
    return subscription;
}

// IndexKeyT
IndexKeyT *GetIndexKeyTForFuzz(void *key, uint32_t keyLen)
{
    IndexKeyT *indexKey = (IndexKeyT *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, sizeof(IndexKeyT));
    indexKey->keyLen = keyLen;
    indexKey->keyData = (uint8_t *)key;
    return indexKey;
}

// DmVertexLabelT
static DmPropertySchemaT *GetDmPropertyForFuzz(uint32_t propertyNum)
{
    if (propertyNum == 0 || propertyNum > 3) {
        printf("When generate DmPropertySchemaT, propertyNum exceed the upper limit.");
    }
    DmPropertySchemaT *property =
        (DmPropertySchemaT *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, sizeof(DmPropertySchemaT) * propertyNum);
    memset_s(property, sizeof(DmPropertySchemaT) * propertyNum, 0, sizeof(DmPropertySchemaT) * propertyNum);
    if (propertyNum >= 1) {
        property[0].isValid = true;
        property[0].dataType = DB_DATATYPE_UINT16;
        property[0].nameLen = DM_STR_LEN("c1");
        property[0].name = (char *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, property[0].nameLen);
        strcpy(property[0].name, "c1");
        property[0].isFixed = true;
        property[0].size = 2;
        property[0].defaultValue = (DmValueT *)DbDynMemCtxAlloc(g_DymCtxMem, sizeof(DmValueT));
        EXPECT_FALSE(property[0].defaultValue == NULL);
        property[0].defaultValue->type = DB_DATATYPE_UINT16;
        property[0].defaultValue->value.ushortValue = 100;
        property[0].propeId = 0;
    }
    if (propertyNum >= 2) {
        property[1].isValid = true;
        property[1].dataType = DB_DATATYPE_STRING;
        property[1].nameLen = DM_STR_LEN("c2");
        property[1].name = (char *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, property[1].nameLen);
        strcpy(property[1].name, "c2");
        property[1].isFixed = false;
        property[1].size = 10;
        property[1].defaultValue = (DmValueT *)DbDynMemCtxAlloc(g_DymCtxMem, sizeof(DmValueT));
        EXPECT_FALSE(property[1].defaultValue == NULL);
        property[1].defaultValue->type = DB_DATATYPE_STRING;
        property[1].defaultValue->value.strAddr = (void *)"abc";
        property[1].defaultValue->value.length = 4;
        property[1].propeId = 1;
    }
    if (propertyNum == 3) {
        property[2].isValid = true;
        property[2].dataType = DB_DATATYPE_UINT32;
        property[2].isNullable = false;
        property[2].nameLen = DM_STR_LEN("c3");
        property[2].name = (char *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, property[2].nameLen);
        strcpy(property[2].name, "c3");
        property[2].isFixed = true;
        property[2].size = 4;
        property[2].defaultValue = (DmValueT *)DbDynMemCtxAlloc(g_DymCtxMem, sizeof(DmValueT));
        EXPECT_FALSE(property[2].defaultValue == NULL);
        property[2].defaultValue->type = DB_DATATYPE_UINT32;
        property[2].defaultValue->value.uintValue = 200;
        property[2].propeId = 2;
    }
    return property;
}

static uint32_t *AllocPropIds(DbMemCtxT *memCtx, uint32_t propertyNum)
{
    if (propertyNum == 0) {
        return NULL;
    }
    size_t size = sizeof(uint32_t) * propertyNum;
    uint32_t *propIds = (uint32_t *)DbDynMemCtxAlloc(memCtx, size);
    DB_ASSERT(propIds != NULL);
    (void)memset_s(propIds, size, 0, size);
    for (uint32_t i = 0; i < propertyNum; ++i) {
        propIds[i] = i;
    }
    return propIds;
}

static DmVlIndexLabelT *GetDmIndexLabelTForFuzz(uint32_t propertyNum, char *indexName, uint32_t indexId)
{
    DmVlIndexLabelT *index = (DmVlIndexLabelT *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, sizeof(DmVlIndexLabelT));
    memset_s(index, sizeof(DmVlIndexLabelT), 0, sizeof(DmVlIndexLabelT));
    (*index).indexNameLen = DM_STR_LEN(indexName);
    (*index).indexName = (char *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, (*index).indexNameLen);
    strcpy((*index).indexName, indexName);
    (*index).idxLabelBase.indexId = indexId;
    (*index).idxLabelBase.dbId = 1;
    (*index).idxLabelBase.srcLabelId = 1;
    (*index).properties = GetDmPropertyForFuzz(propertyNum);
    (*index).propIds = AllocPropIds((DbMemCtxT *)g_DymCtxMem, propertyNum);
    (*index).propeNum = 1;
    uint32_t maxKeyLen = DmGetMaxKeyBufLen4QE(index);
    (*index).maxKeyLen = maxKeyLen;
    (*index).idxLabelBase.indexType = HASH_INDEX;
    (*index).idxLabelBase.indexConstraint = PRIMARY;
    return index;
}

static DmSchemaT *GetDmSchemaTForFuzz(uint32_t propertyNum)
{
    DmSchemaT *schema = (DmSchemaT *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, sizeof(DmSchemaT));
    memset_s(schema, sizeof(DmSchemaT), 0, sizeof(DmSchemaT));
    schema->isFlat = true;
    schema->propeNum = propertyNum;
    schema->properties = GetDmPropertyForFuzz(propertyNum);
    schema->superFieldNum = 0;
    schema->nodeNum = 0;
    schema->nodes = NULL;
    return schema;
}

// 获取DmVertexLabelT
DmVertexLabelT *GetDmVertexLabelTForFuzz()
{
    DmVertexLabelT *vertexLabel = (DmVertexLabelT *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, sizeof(DmVertexLabelT));
    memset_s(vertexLabel, sizeof(DmVertexLabelT), 0, sizeof(DmVertexLabelT));
    vertexLabel->metaVertexLabel =
        (MetaVertexLabelT *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, sizeof(MetaVertexLabelT));
    memset_s(vertexLabel->metaVertexLabel, sizeof(MetaVertexLabelT), 0, sizeof(MetaVertexLabelT));
    vertexLabel->metaCommon.metaId = 1;
    auto &g_label_name = "VertexLabelTest";
    vertexLabel->metaCommon.metaName = (char *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, sizeof(g_label_name));
    strcpy(vertexLabel->metaCommon.metaName, g_label_name);
    vertexLabel->metaCommon.namespaceId = 0;
    vertexLabel->metaCommon.tablespaceId = DB_DEFAULT_TABLE_SPACE_ID;
    vertexLabel->metaCommon.dbId = 0;
    vertexLabel->metaCommon.version = 0;
    vertexLabel->metaVertexLabel->uuid = 0;
    vertexLabel->memCtx = g_DymCtxMem;
    vertexLabel->metaVertexLabel->pkIndex = GetDmIndexLabelTForFuzz(1, (char *)"index1", 1);
    vertexLabel->metaVertexLabel->secIndexNum = 1;
    vertexLabel->metaVertexLabel->secIndexes = GetDmIndexLabelTForFuzz(2, (char *)"index2", 2);
    vertexLabel->metaVertexLabel->schema = GetDmSchemaTForFuzz(3);
    uint32_t topRecordNameLen = sizeof(g_label_name);
    vertexLabel->metaVertexLabel->topRecordName = (char *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, topRecordNameLen);
    strcpy(vertexLabel->metaVertexLabel->topRecordName, g_label_name);
    vertexLabel->commonInfo =
        (VertexLabelCommonInfoT *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, sizeof(VertexLabelCommonInfoT));
    (void)memset_s(vertexLabel->commonInfo, sizeof(VertexLabelCommonInfoT), 0, sizeof(VertexLabelCommonInfoT));
    DmVertexLabel *copyVertexLabel = NULL;
    EXPECT_EQ(GMERR_OK, CopyVertexLabel(g_ShmemCtxMem, vertexLabel, &copyVertexLabel));
    FreePropertiesInFuzz(vertexLabel);
    copyVertexLabel->commonInfo->accCheckAddr =
        (DmAccCheckT *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, sizeof(DmAccCheckT));
    (void)memset_s(copyVertexLabel->commonInfo->accCheckAddr, sizeof(DmAccCheckT), 0, sizeof(DmAccCheckT));
    copyVertexLabel->commonInfo->accCheckAddr->isPartition = true;
    (void)AllocMetaInfo(&copyVertexLabel->commonInfo->metaInfoAddr, copyVertexLabel->metaCommon.metaId, NULL);
    copyVertexLabel->commonInfo->metaInfoAddr->objPrivilege.objPrivVersion = 0;
    copyVertexLabel->commonInfo->metaInfoAddr->objPrivilege.privEntryNum = 0;
    copyVertexLabel->commonInfo->metaInfoAddr->objPrivilege.objPrivEntriesShm = DB_INVALID_SHMPTR;
    copyVertexLabel->commonInfo->metaInfoAddr->objPrivilege.objPrivEntries = NULL;
    (void)DmCreateVertexDesc((DbMemCtxT *)g_DymCtxMem, copyVertexLabel, &copyVertexLabel->vertexDesc);
    return copyVertexLabel;
}

// DatalogVertexLabel
DmVertexLabelT *GetDatalogVertexLabelForFuzz()
{
    DmVertexLabelT *vertexLabel = GetDmVertexLabelTForFuzz();
    uint32_t nsId = 0;
    Status ret = CataGetNamespaceIdByName(NULL, "public", &nsId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Fail to get namespace.");
        return NULL;
    }
    vertexLabel->metaCommon.namespaceId = nsId;
    vertexLabel->metaCommon.metaId = 10086;
    vertexLabel->commonInfo->accCheckAddr = NULL;

    DmDatalogLabelInfoT *datalogLabelInfo =
        (DmDatalogLabelInfoT *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, sizeof(DmDatalogLabelInfoT));
    (void)memset_s(datalogLabelInfo, sizeof(DmDatalogLabelInfoT), 0, sizeof(DmDatalogLabelInfoT));
    datalogLabelInfo->inoutType = DM_DTL_INPUT_LABEL;
    datalogLabelInfo->soId = 0;
    vertexLabel->commonInfo->datalogLabelInfo = datalogLabelInfo;

    return vertexLabel;
}

// DmIndexKeyT
DmValueT *GetDmValueTForFuzz()
{
    DmValueT *propertyValues = (DmValueT *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, sizeof(DmValueT));
    memset_s(propertyValues, sizeof(DmValueT), 0, sizeof(DmValueT));
    propertyValues[0].type = DB_DATATYPE_UINT16;
    propertyValues[0].value.ushortValue = 300;
    return propertyValues;
}

DmIndexKeyT *GetDmIndexKeyTForFuzz()
{
    DmVertexLabel *vertexLabel = GetDmVertexLabelTForFuzz();
    DbLinkedListInit(&vertexLabel->metaVertexLabel->pidRefList);
    DmValueT *propertyValues = GetDmValueTForFuzz();
    DmIndexKeyT *filter = NULL;
    // 索引名正确
    int32_t ret =
        DmCreateIndexKeyByNameWithMemCtx((DbMemCtxT *)g_DymCtxMem, vertexLabel, "index1", propertyValues, 1, &filter);
    if (ret != 0) {
        printf("create DmIndexKeyT failed.");
    }
    return filter;
}

// DmVertexT
DmVertexT *GetDmVertexTForFuzz()
{
    DmVertexLabel *vertexLabel = g_DmVertexLabel;
    DmVertexT *vertex = NULL;
    int32_t ret = DmCreateVertexDesc((DbMemCtxT *)g_DymCtxMem, vertexLabel, &vertexLabel->vertexDesc);
    if (ret != 0) {
        printf("create empty vertex failed.");
        return NULL;
    }
    ret = DmCreateEmptyVertexByDescWithMemCtx((DbMemCtxT *)g_DymCtxMem, vertexLabel->vertexDesc, &vertex);
    if (ret != 0) {
        printf("create empty vertex failed.");
        return NULL;
    }

    DmValueT propertyValue;
    propertyValue.type = DB_DATATYPE_UINT32;
    propertyValue.value.uintValue = 1;
    DmVertexSetPropeByName("c1", propertyValue, vertex);
    propertyValue.type = DB_DATATYPE_STRING;
    propertyValue.value.strAddr = (void *)"zhang";
    propertyValue.value.length = 6;
    DmVertexSetPropeByName("c2", propertyValue, vertex);
    propertyValue.type = DB_DATATYPE_UINT32;
    propertyValue.value.uintValue = 400;
    DmVertexSetPropeByName("c3", propertyValue, vertex);

    return vertex;
}

DmKvIndexLabelT *GetKvIndex(DbMemCtxT *memCtx)
{
    DmKvIndexLabelT *index = (DmKvIndexLabelT *)DbDynMemCtxAlloc(memCtx, sizeof(DmKvIndexLabelT));
    memset_s(index, sizeof(DmKvIndexLabelT), 0, sizeof(DmKvIndexLabelT));
    index->idxLabelBase.dbId = 0;
    index->idxLabelBase.indexId = 12;
    index->idxLabelBase.srcLabelId = 4;
    index->idxLabelBase.srcLabelNameLen = strlen("source label name") + 1;
    index->idxLabelBase.srcLabelName = (char *)DbDynMemCtxAlloc(memCtx, index->idxLabelBase.srcLabelNameLen);
    strcpy_s(index->idxLabelBase.srcLabelName, index->idxLabelBase.srcLabelNameLen, "source label name");
    index->idxLabelBase.indexType = HASH_INDEX;
    return index;
}

// DmKvLabelT
DmKvLabelT *GetDmKvLabelTForFuzz()
{
    DmKvLabelT *label = NULL;
    int32_t ret = DmCreateEmptyKvLabel((DbMemCtxT *)g_DymCtxMem, &label);
    if (ret != 0) {
        printf("create empty kv table failed.");
    }
    DbLinkedListInit(&label->pidRefList);
    label->metaCommon.tablespaceId = DB_DEFAULT_TABLE_SPACE_ID;
    label->metaCommon.metaId = 2;
    auto &g_kv_name = "DmKvTableTest";
    label->metaCommon.metaName = (char *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, sizeof(g_kv_name));
    strcpy(label->metaCommon.metaName, g_kv_name);
    label->index = GetKvIndex((DbMemCtxT *)g_DymCtxMem);
    (void)AllocMetaInfo(&label->metaInfoAddr, label->metaCommon.metaId, NULL);
    label->metaInfoAddr->objPrivilege.objPrivVersion = 0;
    label->metaInfoAddr->objPrivilege.privEntryNum = 0;
    label->metaInfoAddr->objPrivilege.objPrivEntriesShm = DB_INVALID_SHMPTR;
    label->metaInfoAddr->objPrivilege.objPrivEntries = NULL;
    return label;
}

// DmEdgeLabelT
DmEdgeLabelT *GetDmEdgeLabelTForFuzz()
{
    DmEdgeLabelT *edgeLabel = (DmEdgeLabelT *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, sizeof(DmEdgeLabelT));
    memset_s(edgeLabel, sizeof(DmEdgeLabelT), 0, sizeof(DmEdgeLabelT));
    edgeLabel->metaCommon.dbId = 0;
    auto &g_label_name = "testCypherPathEdgeLabel1";
    edgeLabel->metaCommon.metaName = (char *)DbDynMemCtxAlloc((DbMemCtxT *)g_DymCtxMem, sizeof(g_label_name));
    strcpy(edgeLabel->metaCommon.metaName, g_label_name);
    edgeLabel->destVertexLabelId = 2;
    edgeLabel->metaCommon.metaId = 3;
    edgeLabel->isLightEdge = true;
    edgeLabel->sourceVertexLabelId = 1;
    return edgeLabel;
}

// DmEdgeT
DmEdgeT *GetDmEdgeTForFuzz(DmEdgeLabelT *edgeLabel)
{
    DmEdgeT *edge = NULL;
    DmCreateEmptyEdgeWithMemCtx((DbMemCtxT *)g_DymCtxMem, edgeLabel, &edge);

    DmIndexKeyT *srcKey = GetDmIndexKeyTForFuzz();
    DmSetSrcVertexKey(edge, srcKey);
    DmIndexKeyT *destVertexKey = GetDmIndexKeyTForFuzz();
    DmSetDestVertexKey(edge, destVertexKey);
    return edge;
}

static void FuzzParseJsonName(char *jsonName, const char *json, uint32_t maxLength)
{
    DbJsonT *jsonRootLabel;
    DbJsonT *jsonValue;
    char *name;
    uint32_t length;

    jsonRootLabel = DbJsonLoads(json, DB_JSON_REJECT_DUPLICATES);
    if (SECUREC_UNLIKELY(jsonRootLabel == NULL)) {
        jsonName = NULL;
        return;
    }

    jsonValue = DbJsonObjectGet(jsonRootLabel, "name");
    if (jsonValue == NULL) {
        jsonName = NULL;
        goto EXIT;
    }
    name = (char *)DbJsonStringValue(jsonValue);
    length = (uint32_t)strlen(name);
    if (length >= maxLength) {
        jsonName = NULL;
        goto EXIT;
    }
    strcpy(jsonName, name);

EXIT:
    DbJsonDelete(jsonRootLabel);
}

void QryFuzzGetJsonById(uint32_t id, char **jsonStr)
{
    DT_Pits_DoAction(id);
    char *dataout = NULL;
    int lenout = 0;
    DT_Pits_GetMutatorBuf(&dataout, &lenout);
    *jsonStr = malloc(lenout + 1);
    memcpy(*jsonStr, dataout, lenout);
    (*jsonStr)[lenout] = '\0';
    DT_Pits_OneRunFree();
}

void QryFuzzFreeJson(char *jsonStr)
{
    free(jsonStr);
}

// clt message check
void QryFuzzMsgHeader(MsgHeaderT *msgHeader)
{
    msgHeader->flowCtrlLevel = *(uint8_t *)DT_SetGetU8(&g_Element[g_count++], 0);
    msgHeader->priority = MSG_PRIO_NORMAL;

    msgHeader->protocolVersion = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    msgHeader->serviceId = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    msgHeader->opNum = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 1);
    msgHeader->stmtId = *(uint16_t *)DT_SetGetNumberRange(&g_Element[g_count++], 0, 0, MAX_CLIENT_STMT_ID);
    msgHeader->successNum = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);

    msgHeader->serialNumber = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    msgHeader->size = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    msgHeader->reqTimeOut = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    msgHeader->flags = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);

    msgHeader->reqStartTime = *(uint64_t *)DT_SetGetU64(&g_Element[g_count++], 0);
    msgHeader->reqSendStartTime = *(uint64_t *)DT_SetGetU64(&g_Element[g_count++], 0);
}

void QryFuzzMsgHeaderForSubtreeFilter(MsgHeaderT *msgHeader)
{
    msgHeader->flowCtrlLevel = *(uint8_t *)DT_SetGetU8(&g_Element[g_tempElementCount++], 0);
    msgHeader->priority = MSG_PRIO_NORMAL;

    msgHeader->protocolVersion = *(uint16_t *)DT_SetGetU16(&g_Element[g_tempElementCount++], 0);
    msgHeader->serviceId = *(uint16_t *)DT_SetGetU16(&g_Element[g_tempElementCount++], 0);
    msgHeader->opNum = *(uint16_t *)DT_SetGetU16(&g_Element[g_tempElementCount++], 1);
    msgHeader->stmtId = *(uint16_t *)DT_SetGetNumberRange(&g_Element[g_tempElementCount++], 0, 0, MAX_CLIENT_STMT_ID);
    msgHeader->successNum = *(uint16_t *)DT_SetGetU16(&g_Element[g_tempElementCount++], 0);

    msgHeader->serialNumber = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    msgHeader->size = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    msgHeader->reqTimeOut = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    msgHeader->flags = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);

    msgHeader->reqStartTime = *(uint64_t *)DT_SetGetU64(&g_Element[g_tempElementCount++], 0);
    msgHeader->reqSendStartTime = *(uint64_t *)DT_SetGetU64(&g_Element[g_tempElementCount++], 0);
}

void QryFuzzOpHeader(OpHeaderT *opHeader)
{
    opHeader->opCode = g_opCode;
    opHeader->len = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
}

void QryFuzzOpHeaderForSubtreeFilter(OpHeaderT *opHeader)
{
    opHeader->opCode = g_opCode;
    opHeader->len = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
}

Status FillMsgHeader(FixBufferT *buf)
{
    Status ret = FixBufCreate(buf, g_DymCtxMem, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        return ret;
    }
    MsgHeaderT msgHeader;
    QryFuzzMsgHeader(&msgHeader);
    ret = FixBufPutData(buf, (void *)&msgHeader, MSG_HEADER_ALIGN_SIZE);
    if (ret != GMERR_OK) {
        return ret;
    }
    OpHeaderT opHeader;
    QryFuzzOpHeader(&opHeader);
    ret = FixBufPutData(buf, (void *)&opHeader, MSG_OP_HEADER_ALIGN_SIZE);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status FillMsgHeaderForSubtreeFilter(FixBufferT *buf)
{
    Status ret = FixBufCreate(buf, g_DymCtxMem, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        return ret;
    }
    MsgHeaderT msgHeader;
    QryFuzzMsgHeaderForSubtreeFilter(&msgHeader);
    ret = FixBufPutData(buf, (void *)&msgHeader, MSG_HEADER_ALIGN_SIZE);
    if (ret != GMERR_OK) {
        return ret;
    }
    OpHeaderT opHeader;
    QryFuzzOpHeaderForSubtreeFilter(&opHeader);
    ret = FixBufPutData(buf, (void *)&opHeader, MSG_OP_HEADER_ALIGN_SIZE);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

void QryFuzzMsgHeaderWhitFuzzJson(MsgHeaderT *msgHeader)
{
    msgHeader->flowCtrlLevel = *(uint8_t *)DT_SetGetU8(&g_Element[g_tempElementCount++], 0);
    msgHeader->priority = MSG_PRIO_NORMAL;

    msgHeader->protocolVersion = *(uint16_t *)DT_SetGetU16(&g_Element[g_tempElementCount++], 0);
    msgHeader->serviceId = *(uint16_t *)DT_SetGetU16(&g_Element[g_tempElementCount++], 0);
    msgHeader->opNum = *(uint16_t *)DT_SetGetU16(&g_Element[g_tempElementCount++], 1);
    msgHeader->stmtId = *(uint16_t *)DT_SetGetNumberRange(&g_Element[g_tempElementCount++], 0, 0, MAX_CLIENT_STMT_ID);
    msgHeader->successNum = *(uint16_t *)DT_SetGetU16(&g_Element[g_tempElementCount++], 0);

    msgHeader->serialNumber = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    msgHeader->size = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    msgHeader->reqTimeOut = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    msgHeader->flags = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);

    msgHeader->reqStartTime = *(uint64_t *)DT_SetGetU64(&g_Element[g_tempElementCount++], 0);
    msgHeader->reqSendStartTime = *(uint64_t *)DT_SetGetU64(&g_Element[g_tempElementCount++], 0);
}

void QryFuzzOpHeaderWhitFuzzJson(OpHeaderT *opHeader)
{
    opHeader->opCode = g_opCode;
    opHeader->len = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
}

Status FillMsgHeaderWhitFuzzJson(FixBufferT *buf)
{
    Status ret = FixBufCreate(buf, g_DymCtxMem, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        return ret;
    }
    MsgHeaderT msgHeader;
    QryFuzzMsgHeaderWhitFuzzJson(&msgHeader);
    ret = FixBufPutData(buf, (void *)&msgHeader, MSG_HEADER_ALIGN_SIZE);
    if (ret != GMERR_OK) {
        return ret;
    }
    OpHeaderT opHeader;
    QryFuzzOpHeaderWhitFuzzJson(&opHeader);
    ret = FixBufPutData(buf, (void *)&opHeader, MSG_OP_HEADER_ALIGN_SIZE);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferCreateKvTable(FixBufferT *buf)
{

    CreateLabelProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_CREATE_KV_TABLE;

    char *jsonInit1 = NULL;
    QryFuzzGetJsonById(g_scheme_json_id, &jsonInit1);
    proto.labelJson = jsonInit1;

    char *jsonInit2 = NULL;
    QryFuzzGetJsonById(g_config_json_id, &jsonInit2);
    proto.configJson = jsonInit2;

    Status ret = FillMsgHeaderWhitFuzzJson(buf);
    if (ret != GMERR_OK) {
        QryFuzzFreeJson(jsonInit1);
        QryFuzzFreeJson(jsonInit2);
        FixBufRelease(buf);
        return ret;
    }
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    QryFuzzFreeJson(jsonInit1);
    QryFuzzFreeJson(jsonInit2);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferTruncateKvTable(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }

    DropTruncateLabelProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_TRUNCATE_KV_TABLE;
    proto.labelName = DT_SetGetString(
        &g_Element[g_count++], strlen(g_kvTableName) + 1, MAX_TABLE_NAME_LEN + FUZZ_OFFSET, g_kvTableName);
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferGetKvTable(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    GetLabelProtoT proto = {};
    char *kvTableName =
        DT_SetGetString(&g_Element[g_count++], strlen(g_kvTableName) + 1, MAX_STRING_LEN, g_kvTableName);
    DmKvLabelT *kvTable = NULL;
    uint32_t labelId = 0;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, g_nspId, kvTableName);
    ret = CataGetLabelByName(&cataKey, &kvTable, NULL);
    if (ret == GMERR_OK) {
        labelId = kvTable->metaCommon.metaId;
        CataReleaseLabel(kvTable);
    } else {
        labelId = g_DmKvTable->metaCommon.metaId;
    }
    proto.labelName = kvTableName;
    proto.labelId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], labelId);
    proto.isView = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    proto.protoHead.opCode = MSG_OP_RPC_GET_KV_TABLE;
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferDropKvTable(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }

    DropTruncateLabelProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_DROP_KV_TABLE;
    proto.labelName = DT_SetGetString(
        &g_Element[g_count++], strlen(g_kvTableName) + 1, MAX_TABLE_NAME_LEN + FUZZ_OFFSET, g_kvTableName);
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status FuzzFillFixFufferKvProto(FixBufferT *buf, MsgOpcodeRpcE opCode, bool isDQL)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }

    KvProtoT proto = {};

    if (isDQL) {
        proto.prepExecReq.exec.paramsetSize = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
        proto.prepExecReq.exec.preFetchRows = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
        proto.prepExecReq.prep.flags[0] = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
        proto.prepExecReq.prep.flags[1] = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 1);
    }

    char *kvTableName =
        DT_SetGetString(&g_Element[g_count++], strlen(g_kvTableName) + 1, MAX_STRING_LEN, g_kvTableName);
    DmKvLabelT *kvTable = NULL;
    uint32_t labelId = 0;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, g_nspId, kvTableName);
    ret = CataGetLabelByName(&cataKey, &kvTable, NULL);
    if (ret == GMERR_OK) {
        labelId = kvTable->metaCommon.metaId;
        CataReleaseLabel(kvTable);
    } else {
        labelId = g_DmKvTable->metaCommon.metaId;
    }
    proto.kvTableId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], labelId);
    proto.indexKey = *g_IndexKey;
    uint32_t value = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    proto.value = &value;
    proto.valueLen = sizeof(uint32_t);
    proto.limitCount = *(uint64_t *)DT_SetGetU64(&g_Element[g_count++], 0);
    proto.protoHead.opCode = opCode;
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferSetKv(FixBufferT *buf)
{
    return FuzzFillFixFufferKvProto(buf, MSG_OP_RPC_SET_KV, false);
}

Status QryFuzzFillFixFufferGetKv(FixBufferT *buf)
{
    return FuzzFillFixFufferKvProto(buf, MSG_OP_RPC_GET_KV, true);
}

Status QryFuzzFillFixFufferKvIsExist(FixBufferT *buf)
{
    return FuzzFillFixFufferKvProto(buf, MSG_OP_RPC_IS_KV_EXIST, true);
}

Status QryFuzzFillFixFufferKvRecordCount(FixBufferT *buf)
{
    return FuzzFillFixFufferKvProto(buf, MSG_OP_RPC_GET_KV_RECORD_COUNT, true);
}

Status QryFuzzFillFixFufferScanKvBegin(FixBufferT *buf)
{
    return FuzzFillFixFufferKvProto(buf, MSG_OP_RPC_SCAN_KV_BEGIN, true);
}

Status QryFuzzFillFixBufferDeleteKv(FixBufferT *buf)
{
    return FuzzFillFixFufferKvProto(buf, MSG_OP_RPC_DELETE_KV, false);
}

Status QryFuzzFillFixFufferBeginTrans(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    uint32_t isolationType =
        *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0) % (GMC_TX_ISOLATION_BUTT + 2);  // 设置两个超出范围的值
    uint32_t readOnly = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    ret = FixBufPutMultipleUint32(buf, 0x2, isolationType, readOnly);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferCommitTrans(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferRollbackTrans(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferAbortTrans(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    uint64_t transId =
        *(uint32_t *)DT_SetGetU64(&g_Element[g_count++], 0) % (GMC_TX_ISOLATION_BUTT + 2);  // 设置两个超出范围的值
    ret = FixBufPutUint64(buf, transId);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferCreateVertexLabel(FixBufferT *buf)
{
    CreateLabelProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_CREATE_VERTEX_LABEL;

    char *jsonInit1 = NULL;
    QryFuzzGetJsonById(g_scheme_json_id, &jsonInit1);
    proto.labelJson = jsonInit1;

    char *jsonInit2 = NULL;
    QryFuzzGetJsonById(g_config_json_id, &jsonInit2);
    proto.configJson = jsonInit2;

    proto.labelName = DT_SetGetString(
        &g_Element[g_tempElementCount++], strlen("VertexLabelName") + 1, MAX_STRING_LEN, "VertexLabelName");

    Status ret = FillMsgHeaderWhitFuzzJson(buf);
    if (ret != GMERR_OK) {
        QryFuzzFreeJson(jsonInit1);
        QryFuzzFreeJson(jsonInit2);
        FixBufRelease(buf);
        return ret;
    }

    ret = CltFillMsg(buf, 0, &proto.protoHead);
    QryFuzzFreeJson(jsonInit1);
    QryFuzzFreeJson(jsonInit2);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferCreateEdgeLabel(FixBufferT *buf)
{
    CreateLabelProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_CREATE_EDGE_LABEL;

    char *jsonInit1 = NULL;
    QryFuzzGetJsonById(g_scheme_json_id, &jsonInit1);
    proto.labelJson = jsonInit1;

    char *jsonInit2 = NULL;
    QryFuzzGetJsonById(g_config_json_id, &jsonInit2);
    proto.configJson = jsonInit2;

    FuzzParseJsonName(proto.labelName, proto.labelJson, MAX_STRING_LEN);
    if (proto.labelName == NULL) {
        // 此处覆盖proto.labelName与proto.labelJson中name不一致的场景
        proto.labelName = "testCypherPathEdgeLabel1";
    }

    Status ret = FillMsgHeaderWhitFuzzJson(buf);
    if (ret != GMERR_OK) {
        QryFuzzFreeJson(jsonInit1);
        QryFuzzFreeJson(jsonInit2);
        FixBufRelease(buf);
        return ret;
    }
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    QryFuzzFreeJson(jsonInit1);
    QryFuzzFreeJson(jsonInit2);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferCreateSubscription(FixBufferT *buf)
{
    CreateSubProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_CREATE_SUBSCRIPTION;

    DT_Pits_DoAction(g_config_json_id);

    char *jsonInit = NULL;
    QryFuzzGetJsonById(g_config_json_id, &jsonInit);
    proto.configJson = jsonInit;

    proto.subsName =
        DT_SetGetString(&g_Element[g_tempElementCount++], strlen("subVertex") + 1, MAX_STRING_LEN, "subVertex");
    proto.channelName = DT_SetGetString(
        &g_Element[g_tempElementCount++], strlen("subVertexChannel") + 1, MAX_STRING_LEN, "subVertexChannel");

    Status ret = FillMsgHeaderWhitFuzzJson(buf);
    if (ret != GMERR_OK) {
        QryFuzzFreeJson(jsonInit);
        FixBufRelease(buf);
        return ret;
    }
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    QryFuzzFreeJson(jsonInit);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferCreateNamespace(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    NamespaceProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_CREATE_NAMESPACE;
    proto.namespaceName =
        DT_SetGetString(&g_Element[g_count++], strlen("namespaceName") + 1, MAX_STRING_LEN, "namespaceName");
    proto.userName = DT_SetGetString(&g_Element[g_count++], strlen("userName") + 1, MAX_STRING_LEN, "userName");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferCreateTablespace(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    TablespaceProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_CREATE_TABLESPACE;
    proto.tablespaceName =
        DT_SetGetString(&g_Element[g_count++], strlen("tablespaceName") + 1, MAX_STRING_LEN, "tablespaceName");
    proto.initSize = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    proto.stepSize = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    proto.maxSize = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferDropTablespace(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    TablespaceProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_DROP_TABLESPACE;
    proto.tablespaceName =
        DT_SetGetString(&g_Element[g_count++], strlen("tablespaceName") + 1, MAX_STRING_LEN, "tablespaceName");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferCreateUser(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    BatchUserProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_CREATE_USER;
    uint16_t userNum = 2;
    char **userNames = NULL;
    char **processNames = NULL;
    uint16_t *reservedConnNum = NULL;
    userNames = (char **)malloc(sizeof(char *) * userNum);
    processNames = (char **)malloc(sizeof(char *) * userNum);
    reservedConnNum = (uint16_t *)malloc(sizeof(uint16_t) * userNum);
    uint16_t(*userMeta)[USER_METADATA_FILED_NUM] =
        (uint16_t(*)[USER_METADATA_FILED_NUM])malloc(USER_METADATA_FILED_NUM * sizeof(uint16_t) * userNum);
    proto.userMetadataMaxNumArray = userMeta;
    uint16_t i;
    uint16_t tmpConnNum = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    for (i = 0; i < userNum; i++) {
        userNames[i] = DT_SetGetString(&g_Element[g_count++], strlen("userName") + 1, MAX_STRING_LEN, "userName");
        processNames[i] = userNames[i];
        reservedConnNum[i] = tmpConnNum;
        uint16_t j;
        // 对前两个用户的九种元数据配置上限fuzz
        for (j = 0; j < USER_METADATA_FILED_NUM; j++) {
            (userMeta[i])[j] = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
        }
    }
    userNum = i;
    proto.processNames = processNames;
    proto.userNames = userNames;
    proto.reservedConnNumArray = reservedConnNum;
    proto.userNum = userNum;

    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
    }
    free(userMeta);
    free(userNames);
    free(processNames);
    free(reservedConnNum);
    return ret;
}

Status QryFuzzFillFixFufferCreateRole(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    UserOrGrpProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_CREATE_GROUP;
    proto.processName =
        DT_SetGetString(&g_Element[g_count++], strlen("processName") + 1, MAX_STRING_LEN, "processName");
    proto.userOrGrpName = DT_SetGetString(&g_Element[g_count++], strlen("userName") + 1, MAX_STRING_LEN, "userName");
    proto.reservedConnNum = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);

    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferAlterVertexLabel(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    AlterLabelProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_ALTER_VERTEX_LABEL;
    proto.isCompatible = true;
    proto.labelJson =
        DT_SetGetString(&g_Element[g_count++], strlen("vertexLabelTest") + 1, MAX_STRING_LEN, "vertexLabelTest");
    proto.labelName =
        DT_SetGetString(&g_Element[g_count++], strlen("vertexLabelTest") + 1, MAX_STRING_LEN, "vertexLabelTest");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferDuplicateVertexLabel(FixBufferT *buf)
{
    DuplicateLabelProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_DUPLICATE_VERTEX_LABEL;
    proto.originLabelName =
        DT_SetGetString(&g_Element[g_count++], strlen("vertexLabelName") + 1, MAX_STRING_LEN, "vertexLabelName");
    proto.newLabelName =
        DT_SetGetString(&g_Element[g_count++], strlen("newVertexLabelTest") + 1, MAX_STRING_LEN, "newVertexLabelTest");

    char *jsonDup = NULL;
    QryFuzzGetJsonById(g_config_json_id, &jsonDup);
    proto.configJson = jsonDup;

    Status ret = FillMsgHeaderWhitFuzzJson(buf);
    if (ret != GMERR_OK) {
        QryFuzzFreeJson(jsonDup);
        FixBufRelease(buf);
        return ret;
    }

    ret = CltFillMsg(buf, 0, &proto.protoHead);
    QryFuzzFreeJson(jsonDup);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferDropVertexLabel(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    DropTruncateLabelProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_DROP_VERTEX_LABEL;
    proto.labelName =
        DT_SetGetString(&g_Element[g_count++], strlen("vertexLabelTest") + 1, MAX_STRING_LEN, "vertexLabelTest");

    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferDegradeVertexLabel(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    DropTruncateLabelProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_DEGRADE_VERTEX_LABEL;
    proto.labelName =
        DT_SetGetString(&g_Element[g_count++], strlen("vertexLabelTest") + 1, MAX_STRING_LEN, "vertexLabelTest");
    proto.schemaVersion = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 1);
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillGetVertexLabelDegradeProgress(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    GetLabelDegradeProcessProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_GET_LABEL_DEGRADE_PROGRESS;
    proto.labelName =
        DT_SetGetString(&g_Element[g_count++], strlen("vertexLabelTest") + 1, MAX_STRING_LEN, "vertexLabelTest");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}
#define CLT_MAX_PRE_FETCH_LABELS_COUNT 200
Status QryFuzzFillPrefetchLabels(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }
    // 填充opHeader
    FixBufSeek(buf, MSG_HEADER_ALIGN_SIZE);  // seek to opHeader
    OpHeaderT *opHeader = FixBufGetData(buf, MSG_OP_HEADER_ALIGN_SIZE);
    opHeader->opCode = MSG_OP_RPC_GET_VERTEX_LABELS;
    // fuzz label count
    uint32_t count = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 1);
    if (count > CLT_MAX_PRE_FETCH_LABELS_COUNT) {
        count = CLT_MAX_PRE_FETCH_LABELS_COUNT + 1;
    }
    const char *labelName =
        DT_SetGetString(&g_Element[g_count++], strlen("vertexLabelTest") + 1, MAX_STRING_LEN, "vertexLabelTest");
    uint32_t size = CltStrLen(labelName);
    for (uint32_t i = 0; i < count; i++) {
        ret = SecureFixBufPutRawText(buf, size, labelName);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            goto RELEASE;
        }
    }
    return ret;
RELEASE:
    FixBufRelease(buf);
    return ret;
}
#ifdef EXPERIMENTAL_NERGC
Status QryFuzzFillFixBufferGetInvalidLabelByLabelList(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }
    // 填充opHeader
    FixBufSeek(buf, MSG_HEADER_ALIGN_SIZE);  // seek to opHeader
    OpHeaderT *opHeader = FixBufGetData(buf, MSG_OP_HEADER_ALIGN_SIZE);
    opHeader->opCode = MSG_OP_RPC_GET_INVALID_LABEL_BY_LABEL_LIST;
    // fuzz label count
    uint32_t count = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 1);
    ret = FixBufPutUint32(buf, count);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    for (uint32_t i = 0; i < count; i++) {
        ret = FixBufPutUint32(buf, VERTEX_LABEL);
        if (ret != GMERR_OK) {
            FixBufRelease(buf);
            return ret;
        }
        uint32_t vertexLabelId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], g_DmVertexLabel->metaCommon.metaId);
        ret = FixBufPutUint32(buf, vertexLabelId);
        if (ret != GMERR_OK) {
            FixBufRelease(buf);
            return ret;
        }
        uint32_t version = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], g_DmVertexLabel->metaCommon.version);
        ret = FixBufPutUint32(buf, version);
        if (ret != GMERR_OK) {
            FixBufRelease(buf);
            return ret;
        }
    }
    return ret;
RELEASE:
    FixBufRelease(buf);
    return ret;
}
#endif

Status QryFuzzFillFixFufferUpdateCheckVersion(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    UpdateCheckVersionProtoT proto = {};
    uint32_t vertexLabelId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 1);
    proto.protoHead.opCode =
        MSG_OP_RPC_UPDATE_CHECK_VERSION;  // verify报3000： Update check version request index1 should be a primary key.
    proto.vertexLabelId = vertexLabelId;
    proto.indexKey = g_dmIndexKey;

    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferCheckReplace(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    CheckReplaceProtoT proto = {};
    uint32_t vertexLabelId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], g_DmVertexLabel->metaCommon.metaId);
    proto.protoHead.opCode = MSG_OP_RPC_CHECK_REPLACE;
    proto.vertexLabelId = vertexLabelId;
    proto.trxId = 0;
    ret = DmGetKeyBufFromVertex(
        g_DmVertex, g_DmVertexLabel->metaVertexLabel->pkIndex->idxLabelBase.indexId, &proto.keyBuf, &proto.keySize);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferNoneVertex(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    uint32_t vertexLabelId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], g_DmVertexLabel->metaCommon.metaId);
    ret = FixBufPutUint32(buf, vertexLabelId);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    uint32_t version = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], g_DmVertexLabel->metaCommon.version);
    ret = FixBufPutUint32(buf, version);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    uint32_t uuid = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], g_DmVertexLabel->metaVertexLabel->uuid);
    ret = FixBufPutUint32(buf, uuid);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    uint32_t indexId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], g_dmIndexKey->indexId);
    ret = FixBufPutUint32(buf, indexId);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferVerifyInsertVertex(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    InsertVertexProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_INSERT_VERTEX;
    proto.operateEdgeFlag = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 1);
    proto.vertex = g_DmVertex;
    CltCataLabelT cltCataLabel = {};
    cltCataLabel.vertexLabel = g_DmVertexLabel;
    proto.cltCataLabel = &cltCataLabel;
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferUpdateVertex(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    UpdateVertexProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_UPDATE_VERTEX;
    proto.vertex = g_DmVertex;
    proto.uniqueIndexKey = g_dmIndexKey;
    proto.condStr = DT_SetGetString(&g_Element[g_count++], strlen("c1=1") + 1, MAX_STRING_LEN, "c1=1");
    proto.operateEdgeFlag = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    CltCataLabelT cltCataLabel = {};
    cltCataLabel.vertexLabel = g_DmVertexLabel;
    proto.cltCataLabel = &cltCataLabel;
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferReplaceVertex(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    InsertVertexProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_REPLACE_VERTEX;
    proto.vertex = g_DmVertex;
    proto.operateEdgeFlag = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    CltCataLabelT cltCataLabel = {};
    cltCataLabel.vertexLabel = g_DmVertexLabel;
    proto.cltCataLabel = &cltCataLabel;

    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferMergeVertex(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    InsertVertexProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_MERGE_VERTEX;
    proto.vertex = g_DmVertex;
    proto.operateEdgeFlag = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    CltCataLabelT cltCataLabel = {};
    cltCataLabel.vertexLabel = g_DmVertexLabel;
    proto.cltCataLabel = &cltCataLabel;

    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferInsertEdge(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    EdgeProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_INSERT_EDGE;  // 9010: CataCache: the label do not exist in nameMap. Label
                                                      // name:testCypherPathEdgeLabel1

    proto.edgeLabel = g_DmEdgeLabel;
    proto.edge = g_DmEdge;

    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferDeleteEdge(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    EdgeProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_DELETE_EDGE;  // 9010: CataCache: the label do not exist in nameMap. Label
                                                      // name:testCypherPathEdgeLabel1
    proto.edgeLabel = g_DmEdgeLabel;
    proto.edge = g_DmEdge;

    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferChangeAlmThreshold(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    AlarmThresholdProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_MODIFY_ALARM_THRESHOLD;
    proto.alarmId = *(uint32_t *)DT_SetGetNumberRange(&g_Element[g_count++], 0, 0, 9);
    proto.raiseRatio = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    proto.clearRatio = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferEndCheck(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    EndCheckProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_END_CHECK;
    proto.vertexLabelId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 1);
    proto.partitionId = *(uint8_t *)DT_SetGetU8(&g_Element[g_count++], 1);
    proto.isAbnormal = *(uint8_t *)DT_SetGetU8(&g_Element[g_count++], 1);
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzDeleteVertex(FixBufferT *buf)
{
    DeleteVertexProtoT proto = {};
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    proto.protoHead.opCode = MSG_OP_RPC_DELETE_VERTEX;
    proto.vertexLabel = g_DmVertexLabel;
    proto.leftIndexKey = g_dmIndexKey;
    proto.rightIndexKey = g_dmIndexKey;
    proto.condString = DT_SetGetString(&g_Element[g_count++], strlen("c1=1") + 1, MAX_STRING_LEN, "c1=1");
    proto.rangeScanFlag = DT_SetGetU32(&g_Element[g_count++], 0);
    proto.operateEdgeFlag = DT_SetGetU32(&g_Element[g_count++], 0);

    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferDropEdgeLabel(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    DropTruncateLabelProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_DROP_EDGE_LABEL;
    proto.labelName = DT_SetGetString(
        &g_Element[g_count++], strlen("EdgeLabelTest") + 1, MAX_TABLE_NAME_LEN + FUZZ_OFFSET, "EdgeLabelTest");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferDropSubscription(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    DropSubProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_DROP_SUBSCRIPTION;
    proto.subName = DT_SetGetString(
        &g_Element[g_count++], strlen("SubscriptionTest") + 1, DM_MAX_NAME_LENGTH + FUZZ_OFFSET, "SubscriptionTest");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

int g_objectTypeTbl[] = {
    GMC_CREATE_PRIV,
    GMC_DROP_PRIV,
    GMC_ALTER_PRIV,
    GMC_GET_PRIV,
    GMC_GRANT_PRIV,
    GMC_REVOKE_PRIV,
    GMC_BIND_PRIV,
    GMC_UNBIND_PRIV,
    GMC_INSERTANY_PRIV,
    GMC_DELETEANY_PRIV,
    GMC_UPDATEANY_PRIV,
    GMC_SELECTANY_PRIV,
    GMC_REPLACEANY_PRIV,
    GMC_MERGEANY_PRIV,
    GMC_TRUNCATE_PRIV,
    GMC_USE_PRIV,
};

Status QryFuzzFillFixFufferGrantSysPrivsToUser(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    SystemPrivsProtoT systemPrivs;
    systemPrivs.protoHead.opCode = MSG_OP_RPC_GRANT_USER_SYSTEM_PRIVS;
    systemPrivs.objectType =
        *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0) % (GMC_OBJ_TYPE_NUM + 2);  // 设置两个超出范围的值
    systemPrivs.opType = *(uint32_t *)DT_SetGetNumberEnum(&g_Element[g_count++], GMC_CREATE_PRIV, g_objectTypeTbl, 16);
    char *userName = DT_SetGetString(&g_Element[g_count++], sizeof("GMDBV5"), MAX_OS_USER_NAME_LENGTH, "GMDBV5");
    char *processName = DT_SetGetString(&g_Element[g_count++], sizeof("GMDBV5"), DB_MAX_PROC_NAME_LEN, "GMDBV5");
    systemPrivs.userName = userName;
    systemPrivs.processName = processName;
    if (userName == NULL || processName == NULL) {
        FixBufRelease(buf);
        return ret;
    }
    ret = CltFillMsg(buf, 0, &systemPrivs.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferRevokeSysPrivsFromUser(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    SystemPrivsProtoT systemPrivs;
    systemPrivs.protoHead.opCode = MSG_OP_RPC_REVOKE_USER_SYSTEM_PRIVS;
    systemPrivs.objectType =
        *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0) % (GMC_OBJ_TYPE_NUM + 2);  // 设置两个超出范围的值
    systemPrivs.opType = *(uint32_t *)DT_SetGetNumberEnum(&g_Element[g_count++], GMC_CREATE_PRIV, g_objectTypeTbl, 16);
    char *userName = DT_SetGetString(&g_Element[g_count++], sizeof("GMDBV5"), MAX_OS_USER_NAME_LENGTH, "GMDBV5");
    char *processName = DT_SetGetString(&g_Element[g_count++], sizeof("GMDBV5"), DB_MAX_PROC_NAME_LEN, "GMDBV5");
    systemPrivs.userName = userName;
    systemPrivs.processName = processName;
    if (userName == NULL || processName == NULL) {
        FixBufRelease(buf);
        return ret;
    }
    ret = CltFillMsg(buf, 0, &systemPrivs.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferGrantSysPrivsToRole(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    char *userName = DT_SetGetString(&g_Element[g_count++], sizeof("GMDBV5"), MAX_OS_USER_NAME_LENGTH, "GMDBV5");
    char *procName = DT_SetGetString(&g_Element[g_count++], sizeof("GMDBV5"), DB_MAX_PROC_NAME_LEN, "GMDBV5");
    TextT roleName = {.len = strlen(userName) + 1, .str = userName};
    TextT processName = {.len = strlen(procName) + 1, .str = procName};
    FixBufPutText(buf, &roleName);
    FixBufPutText(buf, &processName);
    uint32_t obj1 =
        *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0) % (CATA_OBJ_TYPE_NUM + 2);  // 设置两个超过范围的值
    uint32_t sys1 = *(uint32_t *)DT_SetGetNumberEnum(&g_Element[g_count++], GMC_CREATE_PRIV, g_objectTypeTbl, 16);
    FixBufPutUint32(buf, obj1);
    FixBufPutUint32(buf, sys1);
    return GMERR_OK;
}

Status QryFuzzFillFixFufferRevokeSysPrivsFromRole(FixBufferT *buf)
{
    return QryFuzzFillFixFufferGrantSysPrivsToRole(buf);
}

Status QryFuzzFillFixFufferGrantObjPrivsToUser(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    char *userName = DT_SetGetString(&g_Element[g_count++], sizeof("GMDBV5"), MAX_OS_USER_NAME_LENGTH, "GMDBV5");
    char *processName = DT_SetGetString(&g_Element[g_count++], sizeof("GMDBV5"), DB_MAX_PROC_NAME_LEN, "GMDBV5");
    char *objName = DT_SetGetString(&g_Element[g_count++], sizeof("GMDBV5"), MAX_STRING_LEN, "GMDBV5");
    char *nameSpaceName = DT_SetGetString(&g_Element[g_count++], sizeof("GMDBV5"), MAX_STRING_LEN, "GMDBV5");
    ObjectPrivsProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_GRANT_OBJECT_PRIVS;
    proto.userName = userName;
    proto.processName = processName;
    proto.objName = objName;
    proto.privs = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    proto.objType = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    proto.nameSpaceName = nameSpaceName;
    if (userName == NULL || processName == NULL || objName == NULL || nameSpaceName == NULL) {
        FixBufRelease(buf);
        return ret;
    }
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferRevokeObjPrivsFromUser(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    char *userName = DT_SetGetString(&g_Element[g_count++], sizeof("GMDBV5"), MAX_OS_USER_NAME_LENGTH, "GMDBV5");
    char *processName = DT_SetGetString(&g_Element[g_count++], sizeof("GMDBV5"), DB_MAX_PROC_NAME_LEN, "GMDBV5");
    char *objName = DT_SetGetString(&g_Element[g_count++], sizeof("GMDBV5"), MAX_STRING_LEN, "GMDBV5");
    char *nameSpaceName = DT_SetGetString(&g_Element[g_count++], sizeof("GMDBV5"), MAX_STRING_LEN, "GMDBV5");
    ObjectPrivsProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_REVOKE_OBJECT_PRIVS;
    proto.userName = userName;
    proto.processName = processName;
    proto.objName = objName;
    proto.privs = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    proto.objType = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    proto.nameSpaceName = nameSpaceName;
    if (userName == NULL || processName == NULL || objName == NULL || nameSpaceName == NULL) {
        FixBufRelease(buf);
        return ret;
    }
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }

    return GMERR_OK;
}

Status QryFuzzFillFixFufferSetCfg(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    DmValueT *propertyValue;
    char *confName = DT_SetGetString(&g_Element[g_count++], sizeof("GMDBV5"), MAX_STRING_LEN, "GMDBV5");
    propertyValue = g_DmValue;
    SetCfgProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_SET_CFG;
    proto.configName = confName;
    proto.configValue = propertyValue;
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferFlushData(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    FixBufSeek(buf, MSG_HEADER_ALIGN_SIZE);
    OpHeaderT *opHeader = FixBufGetData(buf, MSG_OP_HEADER_ALIGN_SIZE);
    opHeader->opCode = MSG_OP_RPC_FLUSH_DATA;

    const char *path = DT_SetGetString(&g_Element[g_count++], strlen("/data/gmdb") + 1, DB_MAX_PATH, "/data/gmdb");
    uint32_t size = CltStrLen(path);

    ret = SecureFixBufPutRawText(buf, size, path);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferSetScheduleTaskCfg(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    uint32_t workload = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    uint32_t duration = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    ScheduleTaskProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_SET_SCHEDULE_TASK_CFG;
    proto.workload = workload;
    proto.duration = duration;
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferSetUserRequestWeight(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    uint32_t weight = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    char *userName = DT_SetGetString(&g_Element[g_count++], sizeof("GMDBV5"), MAX_STRING_LEN, "GMDBV5");
    char *processName = DT_SetGetString(&g_Element[g_count++], sizeof("GMDBV5"), MAX_STRING_LEN, "GMDBV5");
    UserRequestWeightProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_SET_USER_REQUEST_WEIGHT;
    proto.weight = weight;
    proto.userName = userName;
    proto.processName = processName;
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferSetUserResourceLimit(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    uint32_t value = *(uint32_t *)DT_SetGetNumberRange(&g_Element[g_count++], 0, 0, 200000);
    GmcIpsUserResTypeE resType = *(uint32_t *)DT_SetGetNumberRange(&g_Element[g_count++], 0, 0, 2);
    char *userName = DT_SetGetString(&g_Element[g_count++], sizeof("GMDBV5"), MAX_STRING_LEN, "GMDBV5");
    char *processName = DT_SetGetString(&g_Element[g_count++], sizeof("GMDBV5"), MAX_STRING_LEN, "GMDBV5");
    IpsUserResourcesLimitProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_IPS_SET_USER_RES_LIMIT;
    proto.resType = resType;
    proto.userName = userName;
    proto.processName = processName;
    proto.value = value;
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferSetConnResourceLimit(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    uint32_t value = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    GmcIpsConnResTypeE resType = *(uint32_t *)DT_SetGetNumberRange(&g_Element[g_count++], 0, 0, 2);

    uint32_t connId = *(uint32_t *)DT_SetGetNumberRange(&g_Element[g_count++], 0, 0, 200000);
    IpsConnResourcesLimitProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_IPS_SET_CONN_RES_LIMIT;
    proto.resType = resType;
    proto.connId = connId;
    proto.value = value;
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferIpsSetUserMode(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    uint32_t userMode = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    IpsSetUserModeProtoT ipsUserModeProto;
    ipsUserModeProto.protoHead.opCode = MSG_OP_RPC_IPS_SET_USER_MODE;
    ipsUserModeProto.userMode = userMode;
    ret = CltFillMsg(buf, 0, &ipsUserModeProto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferIpsSetUserProtocol(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    IpsSetUserProtocolProtoT ipsUserProto;
    ipsUserProto.protoHead.opCode = MSG_OP_RPC_IPS_SET_USER_PROTOCOL;
    const int processLen = 12;
    ipsUserProto.processName =
        DT_SetGetString(&g_Element[g_count++], processLen, DB_MAX_NAME_LEN + FUZZ_OFFSET, "ProcessName");
    const int userLen = 9;
    ipsUserProto.userName = DT_SetGetString(&g_Element[g_count++], userLen, DB_MAX_NAME_LEN + FUZZ_OFFSET, "UserName");
    ipsUserProto.protocolType = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    ipsUserProto.isEnable = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);

    ret = CltFillMsg(buf, 0, &ipsUserProto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

static Status FuzzFillFixFufferSetLogCtrl(FixBufferT *buf, MsgOpcodeRpcE opCode)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    LogCtrlProtoT logCtrlProto;
    logCtrlProto.protoHead.opCode = opCode;
    logCtrlProto.startCycle = *(uint64_t *)DT_SetGetU64(&g_Element[g_count++], 0);
    logCtrlProto.duration = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    logCtrlProto.logCtrlVal = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    logCtrlProto.processName = DT_SetGetString(
        &g_Element[g_count++], strlen("fuzz_server") + 1, DB_MAX_PROC_NAME_LEN + FUZZ_OFFSET, "fuzz_server");
    ret = CltFillMsg(buf, 0, &logCtrlProto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferSetLogLevel(FixBufferT *buf)
{
    return FuzzFillFixFufferSetLogCtrl(buf, MSG_OP_RPC_SET_LOG_LEVEL);
}

Status QryFuzzFillFixFufferSetLogSwitch(FixBufferT *buf)
{
    return FuzzFillFixFufferSetLogCtrl(buf, MSG_OP_RPC_SET_LOG_SWITCH);
}

Status QryFuzzFillFixFufferCheckTxOptimisticConflict(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferDropNamespace(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    NamespaceProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_DROP_NAMESPACE;
    proto.userName = NULL;
    proto.namespaceName = DT_SetGetString(
        &g_Element[g_count++], strlen("NamespaceTest") + 1, DM_MAX_NAME_LENGTH + FUZZ_OFFSET, "NamespaceTest");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferBindNspToTsp(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    BindNspToTspProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_BIND_NSP_TO_TSP;
    const int nspStrLen = 14;
    proto.namespaceName =
        DT_SetGetString(&g_Element[g_count++], nspStrLen, DM_MAX_NAME_LENGTH + FUZZ_OFFSET, "NamespaceTest");
    const int tspStrLen = 15;
    proto.tablespaceName =
        DT_SetGetString(&g_Element[g_count++], tspStrLen, DM_MAX_NAME_LENGTH + FUZZ_OFFSET, "TablespaceTest");
    ret = SecureFixBufPutString(buf, proto.namespaceName);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    ret = SecureFixBufPutString(buf, proto.tablespaceName);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferYangValidationModel(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    ProtoHeadT protoHead = {};
    protoHead.opCode = MSG_OP_RPC_YANG_VALIDATION_MODEL;
    ret = CltFillMsg(buf, 0, &protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferDropUser(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    UserOrGrpProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_DROP_USER;
    proto.reservedConnNum = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    const int processLen = 8;
    proto.processName =
        DT_SetGetString(&g_Element[g_count++], processLen, DB_MAX_PROC_NAME_LEN + FUZZ_OFFSET, "Process");
    const int userLen = 5;
    proto.userOrGrpName = DT_SetGetString(&g_Element[g_count++], userLen, DM_MAX_NAME_LENGTH + FUZZ_OFFSET, "User");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}
Status QryFuzzFillFixFufferDropRole(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    UserOrGrpProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_DROP_GROUP;
    proto.reservedConnNum = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    const int processLen = 8;
    proto.processName =
        DT_SetGetString(&g_Element[g_count++], processLen, DB_MAX_PROC_NAME_LEN + FUZZ_OFFSET, "Process");
    const int userLen = 5;
    proto.userOrGrpName = DT_SetGetString(&g_Element[g_count++], userLen, DM_MAX_NAME_LENGTH + FUZZ_OFFSET, "User");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferTruncateVertexLabel(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    DropTruncateLabelProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_TRUNCATE_VERTEX_LABEL;
    proto.labelName = DT_SetGetString(
        &g_Element[g_count++], strlen("VertexLabelTest") + 1, MAX_TABLE_NAME_LEN + FUZZ_OFFSET, "VertexLabelTest");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}
Status QryFuzzFillFixFufferTruncateVertexLabelBackground(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    DropTruncateLabelProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_TRUNCATE_VERTEX_LABEL_BACKGROUND;
    proto.labelName = DT_SetGetString(
        &g_Element[g_count++], strlen("VertexLabelTest") + 1, MAX_TABLE_NAME_LEN + FUZZ_OFFSET, "VertexLabelTest");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferParseCreateResPool(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    const char *format =
        R"({
        "name" : "%s",
        "pool_id" : %u,
        "start_id" : %u,
        "capacity" : %u,
        "order" : %u,
        "alloc_type" : %u
    })";
    const char *resPoolName =
        DT_SetGetString(&g_Element[g_count++], strlen("resPoolName") + 1, DM_RES_POOL_MAX_NAME_LEN + 2, "resPoolName");
    uint32_t poolId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    uint32_t start_id = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    uint32_t capacity = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 200);
    uint32_t order = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    order = order % (RES_COL_POOL_ALLOC_ORDER_INVALID + 1);
    uint32_t type = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0) % (RES_COL_POOL_ALLOC_TYPE_INVALID + 1);

    char resPoolStr[256] = {0};
    int32_t err = snprintf_s(resPoolStr, sizeof(resPoolStr), sizeof(resPoolStr) - 1, format, resPoolName, poolId,
        start_id, capacity, order, type);
    if (err < 0) {
        printf("create resPool json string failed.\n");
        FixBufRelease(buf);
        return GMERR_DATA_EXCEPTION;
    }
    ResPoolProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_CREATE_RES_POOL;
    proto.content = resPoolStr;
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferParseDropResPool(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    ResPoolProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_DROP_RES_POOL;
    const char *resPoolName =
        DT_SetGetString(&g_Element[g_count++], strlen("resPoolName") + 1, DM_RES_POOL_MAX_NAME_LEN + 2, "resPoolName");
    proto.content = resPoolName;
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferParseBindExtendResPool(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    BindResPoolProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_BIND_EXTENDED_RES_POOL;
    proto.resPoolName =
        DT_SetGetString(&g_Element[g_count++], strlen("resPoolName1") + 1, MAX_STRING_LEN, "resPoolName1");
    proto.bindName = DT_SetGetString(&g_Element[g_count++], strlen("bindName") + 1, MAX_STRING_LEN, "bindName");
    proto.extraName =
        DT_SetGetString(&g_Element[g_count++], strlen("resPoolName2") + 1, MAX_STRING_LEN, "resPoolName2");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferParseUnbindExtendResPool(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    BindResPoolProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_UNBIND_EXTENDED_RES_POOL;
    proto.resPoolName =
        DT_SetGetString(&g_Element[g_count++], strlen("resPoolName1") + 1, MAX_STRING_LEN, "resPoolName1");
    proto.bindName = DT_SetGetString(&g_Element[g_count++], strlen("bindName") + 1, MAX_STRING_LEN, "bindName");
    proto.extraName =
        DT_SetGetString(&g_Element[g_count++], strlen("resPoolName2") + 1, MAX_STRING_LEN, "resPoolName2");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferParseBindResPoolToVertexLabel(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    BindResPoolProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_BIND_RES_POOL_TO_VERTEX_LABEL;
    proto.resPoolName =
        DT_SetGetString(&g_Element[g_count++], strlen("resPoolName") + 1, MAX_STRING_LEN, "resPoolName");
    proto.bindName = DT_SetGetString(&g_Element[g_count++], strlen("bindName") + 1, MAX_STRING_LEN, "bindName");
    proto.extraName =
        DT_SetGetString(&g_Element[g_count++], strlen("vertexLabelName") + 1, MAX_STRING_LEN, "vertexLabelName");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferParseUnbindResPoolToVertexLabel(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    BindResPoolProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_UNBIND_RES_POOL_TO_VERTEX_LABEL;
    proto.resPoolName =
        DT_SetGetString(&g_Element[g_count++], strlen("resPoolName") + 1, MAX_STRING_LEN, "resPoolName");
    proto.bindName = DT_SetGetString(&g_Element[g_count++], strlen("bindName") + 1, MAX_STRING_LEN, "bindName");
    proto.extraName =
        DT_SetGetString(&g_Element[g_count++], strlen("vertexLabelName") + 1, MAX_STRING_LEN, "vertexLabelName");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferParseUseNamespace(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    NamespaceProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_USE_NAMESPACE;
    proto.namespaceName =
        DT_SetGetString(&g_Element[g_count++], strlen("namespaceName") + 1, MAX_STRING_LEN, "namespaceName");
    proto.userName = DT_SetGetString(&g_Element[g_count++], strlen("userName") + 1, MAX_STRING_LEN, "userName");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferParseCloseConn(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    IpsCloseConnectionProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_IPS_CLOSE_CONNECTION;
    proto.connId = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferParseCreateSavepoint(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    SavepointProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_TX_CREATE_SAVEPOINT;
    proto.savepointName =
        DT_SetGetString(&g_Element[g_count++], strlen("savepointName") + 1, MAX_STRING_LEN, "savepointName");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferParseReleaseSavepoint(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    SavepointProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_TX_RELEASE_SAVEPOINT;
    proto.savepointName =
        DT_SetGetString(&g_Element[g_count++], strlen("savepointName") + 1, MAX_STRING_LEN, "savepointName");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferParseRollbackSavepoint(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    SavepointProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_TX_ROLLBACK_SAVEPOINT;
    proto.savepointName =
        DT_SetGetString(&g_Element[g_count++], strlen("savepointName") + 1, MAX_STRING_LEN, "savepointName");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferParseBeginCheck(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    BeginCheckProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_BEGIN_CHECK;
    proto.vertexLabelId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 1);
    proto.partitionId = *(uint8_t *)DT_SetGetU8(&g_Element[g_count++], 0);
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixBufferParseEndCheck(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    EndCheckProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_END_CHECK;
    proto.vertexLabelId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 1);
    proto.partitionId = *(uint8_t *)DT_SetGetU8(&g_Element[g_count++], 0);
    proto.isAbnormal = *(uint8_t *)DT_SetGetU8(&g_Element[g_count++], 0);
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

#ifdef WARM_REBOOT
Status QryFuzzFillFixBufferEndAllPartitionCheck(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    EndAllPartitionCheckProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_END_ALL_PARTITION_CHECK;
    proto.vertexLabelId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 1);
    proto.isAbnormal = *(uint8_t *)DT_SetGetU8(&g_Element[g_count++], 0);
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}
#endif

Status QryFuzzFillFixFufferGetVertexLabel(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    GetLabelProtoT proto = {};
    proto.labelId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    proto.versionId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    proto.isView = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    proto.isCsMode = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    proto.protoHead.opCode = MSG_OP_RPC_GET_VERTEX_LABEL;
    proto.labelName = DT_SetGetString(&g_Element[g_count++], strlen("labelName") + 1, MAX_STRING_LEN, "labelName");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferGetLabel(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    GetLabelTypeProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_GET_LABEL;
    proto.labelId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    proto.labelName = DT_SetGetString(&g_Element[g_count++], strlen("labelName") + 1, MAX_STRING_LEN, "labelName");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferGetResPool(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    ResPoolProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_GET_RES_POOL;
    proto.content = DT_SetGetString(&g_Element[g_count++], strlen("content") + 1, MAX_STRING_LEN, "content");
    // content is respool name,length [1,64]
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferGetEdgeLabel(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    GetLabelProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_GET_EDGE_LABEL;
    proto.labelId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    proto.isView = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    proto.labelName = DT_SetGetString(&g_Element[g_count++], strlen("labelName") + 1, MAX_STRING_LEN, "labelName");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferGetCfg(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    GetCfgProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_GET_CFG;
    proto.configName = DT_SetGetString(&g_Element[g_count++], strlen("configName") + 1, MAX_STRING_LEN, "configName");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferGetCheckInfo(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    GetCheckInfoProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_GET_CHECK_INFO;
    proto.vertexLabelId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 1);
    proto.partitionId = *(uint8_t *)DT_SetGetU8(&g_Element[g_count++], 0);
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferGetAlarmData(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    AlarmDataProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_GET_ALARM_DATA;
    proto.alarmId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferGetEstimateMemory(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    GetMemSizeProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_GET_ESTIMATE_MEMORY;
    proto.labelJson = g_simpleSchema;
    proto.configJson = g_queryFuzzConfigJson;
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferGetStatCount(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    GetStatCountProtoT proto = {};
    const char *batch[1] = {};
    batch[0] =
        DT_SetGetString(&g_Element[g_count++], strlen("VertexLabelTest") + 1, MAX_TABLE_NAME_LEN, "VertexLabelTest");
    proto.protoHead.opCode = MSG_OP_RPC_GET_STAT_COUNT;
    proto.labelCount = 1;  // 类似的count不能随便设置，否则可能踩内存
    proto.labelName = batch;
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferScanVertexBegin(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    CsScanVertexProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_SCAN_VERTEX_BEGIN;
    proto.prepExecReq.exec.paramsetSize = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    proto.prepExecReq.exec.preFetchRows = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    proto.prepExecReq.prep.flags[0] = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    proto.prepExecReq.prep.flags[1] = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 1);
    proto.labelId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 1);
    proto.rangeScanFlag = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    proto.leftIndexKey = g_dmIndexKey;
    proto.rightIndexKey = g_dmIndexKey;
    proto.condStr = DT_SetGetString(&g_Element[g_count++], strlen("c1=1") + 1, MAX_STRING_LEN, "c1=1");
    proto.outputFmtStr = DT_SetGetString(&g_Element[g_count++], strlen("c1") + 1, MAX_STRING_LEN, "c1");
    proto.orderByParamCount = 0;  // 如果为1则Data exception occurs. Fail to get field name when scan sort
    proto.limitCount = *(uint64_t *)DT_SetGetU64(&g_Element[g_count++], 0);
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferBeginScanPath(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    ScanPathProtoT proto = {};
    proto.prepExecReq.exec.paramsetSize = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    proto.prepExecReq.exec.preFetchRows = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 1);
    proto.prepExecReq.prep.flags[0] = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 0);
    proto.prepExecReq.prep.flags[1] = *(uint16_t *)DT_SetGetU16(&g_Element[g_count++], 1);
    proto.direction = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    proto.condStr = DT_SetGetString(&g_Element[g_count++], strlen("c1=1") + 1, MAX_STRING_LEN, "c1=1");
    proto.neighborCount = 1;
    proto.indexKey = g_dmIndexKey;
    proto.srcLabelName =
        DT_SetGetString(&g_Element[g_count++], strlen("VertexLabelTest") + 1, MAX_TABLE_NAME_LEN, "VertexLabelTest");
    proto.protoHead.opCode = MSG_OP_RPC_BEGIN_SCAN_PATH;
    CltNeighborInfoT neighborsInfo;
    neighborsInfo.labelName =
        DT_SetGetString(&g_Element[g_count++], strlen("neighborName") + 1, MAX_TABLE_NAME_LEN, "neighborName");
    neighborsInfo.outputFormat =
        DT_SetGetString(&g_Element[g_count++], strlen("c1,c2") + 1, DM_MAX_NAME_LENGTH, "c1,c2");
    proto.neighborsInfo = &neighborsInfo;
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferGetCount(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    GetCountProtoT proto = {};
    proto.labelId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 1);
    proto.indexKey = g_dmIndexKey;
    proto.protoHead.opCode = MSG_OP_RPC_GET_COUNT;
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillSubtreeFilter(FixBufferT *buf)
{
    Status ret = FillMsgHeaderForSubtreeFilter(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    SubtreeFilterProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_SUBTREE_FILTER;
    GmcSubtreeFilterItemT filter = {0};
    filter.subtree.json = g_subtreeJson;
    filter.rootName =
        DT_SetGetString(&g_Element[g_tempElementCount++], strlen("subTest") + 1, DM_MAX_NAME_LENGTH, "subTest");
    filter.isLocationFilter = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 1);
    filter.jsonFlag = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 1);
    filter.maxDepth = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 1);
    filter.defaultMode = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 1);
    filter.configFlag = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 1);
    GmcSubtreeFilterT filters = {.filterMode = GMC_FETCH_JSON, .filter = &filter};
    proto.filter = &filters;
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzFillFixFufferTriggerScan(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    TriggerSubScanProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_TRIGGER_SCAN;
    proto.subName = DT_SetGetString(&g_Element[g_count++], strlen("subTest") + 1, DM_MAX_NAME_LENGTH, "subTest");
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

int reject_or_accept_table[] = {
    GMC_IPS_REJECT_PROCESS, /**< ips拒绝一个进程 */
    GMC_IPS_ACCEPT_PROCESS, /**< ips接受一个进程 */
    GMC_IPS_OPERATION_BUTT  /**< ips无效的操作类型 */
};

Status QryFuzzFillFixBufferRejectOrAcceptProcess(FixBufferT *buf)
{
    Status ret = FillMsgHeader(buf);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    IpsRejectOrAccepProcessProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_IPS_REJECT_OR_ACCEPT_PROCESS;
    proto.ipsOperationType = (GmcIpsOperationTypeE)(
        *(uint32_t *)DT_SetGetNumberEnum(&g_Element[g_count++], GMC_IPS_REJECT_PROCESS, reject_or_accept_table, 3));
    proto.pid = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    ret = CltFillMsg(buf, 0, &proto.protoHead);
    if (ret != GMERR_OK) {
        FixBufRelease(buf);
        return ret;
    }
    return GMERR_OK;
}

Status QryFuzzTestInner(const SessionT *session, MsgOpcodeRpcE opCode)
{
    g_count = 0;
    FixBufferT *req = QrySessionGetReq(session);
    QryStmtT *stmt = NULL;
    Status ret = QryAllocStmt(session, &stmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    stmt->phase = QRY_PHASE_PREPARE;
    ret = QryAllocCtxFromCtxMemPool(NULL, &stmt->context);
    if (ret != GMERR_OK) {
        FixBufRelease(req);
        QryReleaseStmt(stmt);
        return GMERR_OK;
    }
    QryFuzzHandler handler = g_fuzzQueryMap[opCode];
    stmt->context->type = handler.qryType;
    if (handler.fillReq == NULL) {
        FixBufRelease(req);
        QryReleaseStmt(stmt);
        return GMERR_OK;
    }
    ret = handler.fillReq(req);
    if (ret != GMERR_OK) {
        FixBufRelease(req);
        QryReleaseStmt(stmt);
        return ret;
    }
    if (handler.parse == NULL) {
        FixBufRelease(req);
        QryReleaseStmt(stmt);
        return GMERR_OK;
    }
    if (opCode == MSG_OP_RPC_SCAN_VERTEX_BEGIN || opCode == MSG_OP_RPC_SCAN_KV_BEGIN ||
        opCode == MSG_OP_RPC_BEGIN_SCAN_PATH) {
        FixBufSeek(req, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE + 8);
    } else {
        FixBufSeek(req, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE);
    }
    ret = handler.parse(stmt);
    if (ret != GMERR_OK) {
        FixBufRelease(req);
        QryReleaseStmt(stmt);
        return ret;
    }
    if (handler.verify == NULL) {
        FixBufRelease(req);
        QryReleaseStmt(stmt);
        return GMERR_OK;
    }
    ret = handler.verify(stmt);
    if (ret != GMERR_OK) {
        FixBufRelease(req);
        QryReleaseStmt(stmt);
        return ret;
    }

    ret = QryReadExecParams(stmt);
    FixBufRelease(req);
    QryReleaseStmt(stmt);
    return ret;
}

extern "C" void VertexLabelInitHeapInfo(HeapAccessCfgT *heapCfg, const DmVertexLabelT *vertexLabel);
extern "C" Status CreateIndexWhenLabelCreate(SeRunCtxHdT seRunCtx, const DmVertexLabelT *vertexLabel);
extern "C" Status InitIndex(SeRunCtxHdT seRunCtx, DmKvIndexLabelT *index, uint32_t labelId, uint32_t tspId);

int32_t QueryFuzzDbInitStorage(DbMemCtxT *topShmMemCtx)
{
    // use defalut config without config file
    SeConfigT config = {0};
    config.deviceSize = SE_DEFAULT_DEV_SIZE;
    config.pageSize = SE_DEFAULT_PAGE_SIZE;
    config.maxSeMem = SE_DEFAULT_MAX_MEM / 4;
    config.instanceId = GET_INSTANCE_ID;
#if (defined HPE)
    config.maxTrxNum = 64 + MAX_BG_WORKER_NUM;
#else
    config.maxTrxNum = MAX_CONN_NUM + MAX_BG_WORKER_NUM;
#endif
    printf("config.maxTrxNum:%d----------------------\n", config.maxTrxNum);
    SeCalcHeapTupleAddrMode(&config);
    SeInstanceHdT se = NULL;
    SeClearDevCache();
    return SeCreateInstance(NULL, (DbMemCtxT *)topShmMemCtx, &config, &se);
}

Status TestFuzzQryInit()
{
    (void)system("ipcrm -a");
    DbMemCtxArgsT ctxArgs = {0};
    ctxArgs.ctxSize = sizeof(DbDynamicMemCtxT);
    ctxArgs.memType = DB_DYNAMIC_MEMORY;
    ctxArgs.groupId = ROOT_SHM_GROUP_ID;

    Status ret = CommonInit();
    if (ret != 0) {
        printf("common init failed. ret = %d\n", ret);
        return ret;
    }
    ret = QrySetCfg("instanceId", "1");
    if (ret != 0) {
        printf("QrySetCfg set instanceId failed.\n");
        return ret;
    }
    ret = DrtInstanceInit(false, NULL);
    if (ret != 0) {
        printf("drt instance init failed. ret = %d\n", ret);
        return;
    }
    ret = CataLabelCacheInitWithOutTimer(NULL);
    if (ret != GMERR_OK) {
        printf("catalog cache init failed!\n");
        return ret;
    }
    DbMemCtxT *topShmemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, DbGetProcGlobalId());
    EXPECT_TRUE(topShmemCtx != NULL);
    ret = QueryFuzzDbInitStorage(topShmemCtx);
    if (ret != GMERR_OK) {
        printf("storage init failed!\n");
        return ret;
    }
    ret = QrySetCfg("userPolicyMode", "1");
    if (ret != 0) {
        printf("QrySetCfg set configItem failed.\n");
        return ret;
    }
    ret = PublicServiceInit(NULL);
    if (ret != GMERR_OK) {
        printf("public init failed!\n");
        return ret;
    }
    ret = FastPathServiceInit();
    if (ret != 0) {
        printf("qry init failed. ret = %d\n", ret);
        return ret;
    }

    ret = DbServerInitCltStat();
    if (ret != GMERR_OK) {
        printf("qry clt cache init failed!\n");
        return ret;
    }
    QrySetMemCtxPool(NULL, DbCreateDynMemCtxPool(true, (DbMemCtxT *)DbGetTopDynMemCtx(NULL)));
    if (QryGetMemCtxPool(NULL) == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    DbMemCtxArgsT args = {0};
    g_DymCtxMem = (DbMemCtxT *)DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "fuzzDynMemCtx", &args);
    if (g_DymCtxMem == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    g_ShmemCtxMem = (DbMemCtxT *)DbCreateBlockPoolShmemCtx((DbMemCtxT *)topShmemCtx, "fuzzShmemMemCtx", &args);
    if (g_ShmemCtxMem == NULL) {
        printf("fuzzShmMemCtx init failed!\n");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    ret = QryInitSessionPool(NULL);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

uint32_t g_key = 0;

Status KvTableHeapLabelCreate(DmKvLabelT *label)
{
    HeapAccessCfgT heapCfg = {0};
    // 基于存储层的要求初始化heapCfg
    heapCfg.pageType = HEAP_VAR_LEN_ROW_PAGE;
    heapCfg.tupleType = HEAP_TUPLE_TYPE_KV;
    heapCfg.seInstanceId = GET_INSTANCE_ID;
    heapCfg.labelId = label->metaCommon.metaId;
    heapCfg.ccType = label->ccType;
    heapCfg.isYangBigStore = false;
    heapCfg.isStatusMergeSubs = false;
    if (label->maxRecordNumCheck) {
        heapCfg.maxItemNum = label->maxRecordNum;
    } else {
        heapCfg.maxItemNum = DB_MAX_UINT64;
    }
    heapCfg.tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID;
    heapCfg.tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX;
    ShmemPtrT heapShmAddr = {0, 0};
    Status ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    if (ret != GMERR_OK) {
        RemoveLabelLatchWhenCreateFailed(label->labelLatchId, label->labelLatchVersionId, NULL);
        return ret;
    }
    label->heapShmAddr.segId = heapShmAddr.segId;
    label->heapShmAddr.offset = heapShmAddr.offset;
    return GMERR_OK;
}

Status TestFuzzPrepare(SessionT *session)
{
    Status ret = CataGetNamespaceIdByName(NULL, "public", &g_nspId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Fail to get namespace.");
        return ret;
    }

    g_kvTableName = "KvTableTest";

    g_DmVertexLabel = GetDmVertexLabelTForFuzz();
    DbLinkedListInit(&g_DmVertexLabel->metaVertexLabel->pidRefList);
    ret = InitLabelLatch(&g_DmVertexLabel->commonInfo->vertexLabelLatchId,
        &g_DmVertexLabel->commonInfo->vertexLabelLatchVersionId, &g_DmVertexLabel->commonInfo->labelLatchShmAddr, NULL);
    if (ret != GMERR_OK) {
        printf("InitLabelLatch ret is %d\n", ret);
        return ret;
    }
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(NULL);
    ret = MultiVersionPut(NULL, cataCacheMgr->metaCache[CATA_VL], (DmMetaCommonT *)g_DmVertexLabel);
    if (ret != GMERR_OK) {
        printf("SingleVersionPut ret is %d\n", ret);
        return ret;
    }
    g_DmKvTable = GetDmKvLabelTForFuzz();
    ret = InitLabelLatch(
        &g_DmKvTable->labelLatchId, &g_DmKvTable->labelLatchVersionId, &g_DmKvTable->labelLatchShmAddr, NULL);
    if (ret != GMERR_OK) {
        printf("InitLabelLatch ret is %d\n", ret);
        return ret;
    }
    HeapAccessCfgT heapCfg = {0};
    VertexLabelInitHeapInfo(&heapCfg, g_DmVertexLabel);
    ret = HeapLabelCreate(session->seInstance, &heapCfg, &g_DmVertexLabel->commonInfo->heapInfo.heapShmAddr);
    if (ret != GMERR_OK) {
        printf("HeapLabelCreate ret is %d\n", ret);
        return ret;
    }
    ret = CreateIndexWhenLabelCreate(session->seInstance, g_DmVertexLabel);
    if (ret != GMERR_OK) {
        printf("CreateIndexWhenLabelCreate ret is %d\n", ret);
        return ret;
    }
    // CreateIndexWhenLabelCreate 会修改indexId
    g_DmVertexLabel->metaVertexLabel->pkIndex->idxLabelBase.indexId = 1;
    g_DmVertexLabel->metaVertexLabel->secIndexes->idxLabelBase.indexId = 2;
    ret = KvTableHeapLabelCreate(g_DmKvTable);
    if (ret != GMERR_OK) {
        printf("KvTableHeapLabelCreate ret is %d\n", ret);
        return ret;
    }
    DmKvIndexLabelT *index = g_DmKvTable->index;
    ret = InitIndex(session->seInstance, index, g_DmKvTable->metaCommon.metaId, g_DmKvTable->metaCommon.tablespaceId);
    if (ret != GMERR_OK) {
        printf("InitIndex ret is %d\n", ret);
        return ret;
    }
    ret = SingleVersionPut(NULL, cataCacheMgr->metaCache[CATA_KV], (DmMetaCommonT *)g_DmKvTable);
    if (ret != GMERR_OK) {
        printf("SingleVersionPut ret is %d\n", ret);
        return ret;
    }
    g_dmSubscription = GetDmSubscriptionTForFuzz(5);
    ret = SingleVersionPut(NULL, cataCacheMgr->metaCache[CATA_SUB], (DmMetaCommonT *)g_dmSubscription);
    if (ret != GMERR_OK) {
        printf("SingleVersionPut ret is %d\n", ret);
        return ret;
    }
    g_DmEdgeLabel = GetDmEdgeLabelTForFuzz();
    g_IndexKey = GetIndexKeyTForFuzz(&g_key, sizeof(uint32_t));
    g_DmVertex = GetDmVertexTForFuzz();
    g_DmValue = GetDmValueTForFuzz();
    g_DmPkIndex = GetDmIndexLabelTForFuzz(1, (char *)"index1", 1);
    g_DmSecIndex = GetDmIndexLabelTForFuzz(2, (char *)"index2", 2);
    g_DmEdge = GetDmEdgeTForFuzz(g_DmEdgeLabel);
    g_dmIndexKey = GetDmIndexKeyTForFuzz();
    return GMERR_OK;
}

void TestFuzzQryUninit()
{
    DbDeleteDynMemCtx(g_DymCtxMem);
    DbDeleteShmemCtx(g_ShmemCtxMem);
    FastPathServiceUnInit(NULL);
    PublicServiceUnInit(NULL);
    DrtInstanceDestroy(NULL);
    SeReleasePageMgr((SeInstanceT *)SeGetInstance(GET_INSTANCE_ID));
    DbDestroyTopShmemCtx(GET_INSTANCE_ID);
    CommonRelease();
}

class QueryFuzz : public FuzzServerTest {
protected:
    virtual void SetUp()
    {
        DT_Enable_Leak_Check(0, 0);
        Status ret = QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, NULL, &session);
        ASSERT_EQ(GMERR_OK, ret);

        QrySessionSetReq(QueryFuzz::session, &req);
        QrySessionSetNspId(QueryFuzz::session, g_nspId);
    }
    virtual void TearDown()
    {
        QrySessionRelease(session);
    }
    virtual void FreeObjects()
    {}
    static void SetUpTestCase()
    {
        FuzzSetUp();
        int32_t ret = TestFuzzQryInit();
        ASSERT_EQ(GMERR_OK, ret);

        SessionT *sessionStart = NULL;
        ret = QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, NULL, &sessionStart);
        ASSERT_EQ(GMERR_OK, ret);
        ret = TestFuzzPrepare(sessionStart);
        ASSERT_EQ(GMERR_OK, ret);
        QrySessionRelease(sessionStart);

        g_kvTableName = g_DmKvTable->metaCommon.metaName;
        g_nspId = 0;
    }
    static void TearDownTestCase()
    {
        TestFuzzQryUninit();
    }

protected:
    SessionT *session;
    FixBufferT req;
};

TEST_F(QueryFuzz, QueryFuzzCreateKvTable)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_CREATE_KV_TABLE;

    // json建模准备
    g_scheme_json_id = 0;
    g_config_json_id = 0;
    DT_Pits_SetBinFile("./json/CreateKvTableJson.bin");
    g_scheme_json_id = DT_Pits_ParseDataModel("./json/JsonNormal.xml", "jsona", 1);
    DT_Pits_SetBinFile("./json/CreateKvTableConfigJson.bin");
    g_config_json_id = DT_Pits_ParseDataModel("./json/JsonNormal.xml", "jsona", 0);
    DT_Pits_Enable_AllMutater(1);

    DT_FUZZ_START_CMD("QueryFuzzCreateKvTable")
    {
        g_tempElementCount = 0;
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()

    // 释放json建模工具占用内存
    DT_Pits_ParseFree();
}

TEST_F(QueryFuzz, QueryFuzzGetKvTable)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_GET_KV_TABLE;
    DT_FUZZ_START_CMD("QueryFuzzGetKvTable")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzSetKv)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_SET_KV;
    DT_FUZZ_START_CMD("QueryFuzzSetKv")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzGetKv)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_GET_KV;
    DT_FUZZ_START_CMD("QueryFuzzGetKv")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzIsKvExist)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_IS_KV_EXIST;
    DT_FUZZ_START_CMD("QueryFuzzIsKvExist")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzGetKvRecordCount)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_GET_KV_RECORD_COUNT;
    DT_FUZZ_START_CMD("QueryFuzzGetKvRecordCount")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzScanKvBegin)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_SCAN_KV_BEGIN;
    DT_FUZZ_START_CMD("QueryFuzzScanKvBegin")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzDeleteKv)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_DELETE_KV;
    DT_FUZZ_START_CMD("QueryFuzzDeleteKv")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzTruncateKvTable)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_TRUNCATE_KV_TABLE;
    DT_FUZZ_START_CMD("QueryFuzzTruncateKvTable")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzDropKvTable)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_DROP_KV_TABLE;
    DT_FUZZ_START_CMD("QueryFuzzDropKvTable")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzBeginTrans)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_TX_START;
    DT_FUZZ_START_CMD("QueryFuzzBeginTrans")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzCommitTrans)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_TX_COMMIT;
    DT_FUZZ_START_CMD("QueryFuzzCommitTrans")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzRollBackTrans)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_TX_ROLLBACK;
    DT_FUZZ_START_CMD("QueryFuzzRollBackTrans")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzAbortTrans)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_TX_ABORT;
    DT_FUZZ_START_CMD("QueryFuzzAbortTrans")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzCheckTxOptimisticConflict)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_TX_CHECK_OPTIMISTIC_CONFLICT;
    DT_FUZZ_START_CMD("QueryFuzzCheckTxOptimisticConflict")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzCreateVertexLabel)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_CREATE_VERTEX_LABEL;

    // json建模准备
    g_scheme_json_id = 0;
    g_config_json_id = 0;
    DT_Pits_SetBinFile("./json/CreateVertexLabelJson.bin");
    g_scheme_json_id = DT_Pits_ParseDataModel("./json/JsonNormal.xml", "jsona", 1);
    DT_Pits_SetBinFile("./json/CreateVertexLabelConfigJson.bin");
    g_config_json_id = DT_Pits_ParseDataModel("./json/JsonNormal.xml", "jsona", 0);
    DT_Pits_Enable_AllMutater(1);

    DT_FUZZ_START_CMD("QueryFuzzCreateVertexLabel")
    {
        g_tempElementCount = 0;
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()

    // 释放json建模工具占用内存
    DT_Pits_ParseFree();
}

TEST_F(QueryFuzz, QueryFuzzCreateEdgeLabel)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_CREATE_EDGE_LABEL;
    // json建模准备
    g_scheme_json_id = 0;
    g_config_json_id = 0;
    DT_Pits_SetBinFile("./json/CreateEdgeLabelJson.bin");
    g_scheme_json_id = DT_Pits_ParseDataModel("./json/JsonNormal.xml", "jsona", 1);
    DT_Pits_SetBinFile("./json/CreateEdgeLabelConfigJson.bin");
    g_config_json_id = DT_Pits_ParseDataModel("./json/JsonNormal.xml", "jsona", 0);
    DT_Pits_Enable_AllMutater(1);

    DT_FUZZ_START_CMD("QueryFuzzCreateEdgeLabel")
    {
        g_tempElementCount = 0;
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()

    // 释放json建模工具占用内存
    DT_Pits_ParseFree();
}

TEST_F(QueryFuzz, QueryFuzzCreateSubscription)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_CREATE_SUBSCRIPTION;

    // json建模准备
    g_config_json_id = 0;
    DT_Pits_SetBinFile("./json/CreateSubConfig.bin");
    g_config_json_id = DT_Pits_ParseDataModel("./json/JsonNormal.xml", "jsona", 1);
    DT_Pits_Enable_AllMutater(1);

    DT_FUZZ_START_CMD("QueryFuzzCreateSubscription")
    {
        g_tempElementCount = 0;
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()

    // 释放json建模工具占用内存
    DT_Pits_ParseFree();
}

TEST_F(QueryFuzz, QueryFuzzCreateNamespace)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_CREATE_NAMESPACE;
    DT_FUZZ_START_CMD("QueryFuzzCreateNamespace")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzCreateTablespace)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_CREATE_TABLESPACE;
    DT_FUZZ_START_CMD("QueryFuzzCreateTablespace")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzDropTablespace)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_DROP_TABLESPACE;
    DT_FUZZ_START_CMD("QueryFuzzDropTablespace")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzCreateUser)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_CREATE_USER;
    DT_FUZZ_START_CMD("QueryFuzzCreateUser")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzCreateRole)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_CREATE_GROUP;
    DT_FUZZ_START_CMD("QueryFuzzCreateRole")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzAlterVertexLabel)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_ALTER_VERTEX_LABEL;
    DT_FUZZ_START_CMD("QueryFuzzAlterVertexLabel")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

#ifndef FEATURE_PERSISTENCE
TEST_F(QueryFuzz, QueryFuzzDupVertexLabel)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_DUPLICATE_VERTEX_LABEL;

    // json建模准备
    g_config_json_id = 0;
    DT_Pits_SetBinFile("./json/DupVertexLabelConfigJson.bin");
    g_config_json_id = DT_Pits_ParseDataModel("./json/JsonNormal.xml", "jsona", 1);
    DT_Pits_Enable_AllMutater(1);
    DT_FUZZ_START_CMD("QueryFuzzDupVertexLabel")
    {
        g_tempElementCount = 0;
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()

    // 释放json建模工具占用内存
    DT_Pits_ParseFree();
}
#endif

TEST_F(QueryFuzz, QueryFuzzDropVertexLabel)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_DROP_VERTEX_LABEL;
    DT_FUZZ_START_CMD("QueryFuzzDropVertexLabel")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzDropVertexLabelVersion)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_DEGRADE_VERTEX_LABEL;
    DT_FUZZ_START_CMD("QueryFuzzDropVertexLabelVersion")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzDropEdgeLabel)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_DROP_EDGE_LABEL;
    DT_FUZZ_START_CMD("QueryFuzzDropEdgeLabel")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzDropSubscription)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_DROP_SUBSCRIPTION;
    DT_FUZZ_START_CMD("QueryFuzzDropSubscription")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzDropNamespace)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_DROP_NAMESPACE;
    DT_FUZZ_START_CMD("QueryFuzzDropNamespace")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzDropUser)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_DROP_USER;
    DT_FUZZ_START_CMD("QueryFuzzDropUser")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzDropRole)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_DROP_GROUP;
    DT_FUZZ_START_CMD("QueryFuzzDropRole")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzTruncateVertexLabel)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_TRUNCATE_VERTEX_LABEL;
    DT_FUZZ_START_CMD("QueryFuzzTruncateVertexLabel")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzTruncateVertexLabelBackground)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_TRUNCATE_VERTEX_LABEL_BACKGROUND;
    DT_FUZZ_START_CMD("QueryFuzzTruncateVertexLabelBackground")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzCreateResPool)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_CREATE_RES_POOL;
    DT_FUZZ_START_CMD("QueryFuzzCreateResPool")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzDropResPool)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_DROP_RES_POOL;
    DT_FUZZ_START_CMD("QueryFuzzDropResPool")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzBindExtendedResPool)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_BIND_EXTENDED_RES_POOL;
    DT_FUZZ_START_CMD("QueryBindExtendedResPool")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzUnBindExtendedResPool)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_UNBIND_EXTENDED_RES_POOL;
    DT_FUZZ_START_CMD("QueryUnBindExtendedResPool")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzBindResPoolToVertexLabel)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_BIND_RES_POOL_TO_VERTEX_LABEL;
    DT_FUZZ_START_CMD("QueryBindResPoolToVertexLabel")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzUnBindResPoolToVertexLabel)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_UNBIND_RES_POOL_TO_VERTEX_LABEL;
    DT_FUZZ_START_CMD("QueryUnBindResPoolToVertexLabel")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzUseNamespace)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_USE_NAMESPACE;
    DT_FUZZ_START_CMD("QueryFuzzUseNamespace")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzBeginCheck)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_BEGIN_CHECK;
    DT_FUZZ_START_CMD("QueryFuzzBeginCheck")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzEndCheck)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_END_CHECK;
    DT_FUZZ_START_CMD("QueryFuzzEndCheck")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

#ifdef WARM_REBOOT
TEST_F(QueryFuzz, QueryFuzzEndAllPartitionCheck)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_END_ALL_PARTITION_CHECK;
    DT_FUZZ_START_CMD("QueryFuzzEndAllPartitionCheck")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}
#endif

TEST_F(QueryFuzz, QueryFuzzModifyAlarmThreshold)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_MODIFY_ALARM_THRESHOLD;
    DT_FUZZ_START_CMD("QueryFuzzModifyAlarmThreshold")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzInsertVertex)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_INSERT_VERTEX;
    DT_FUZZ_START_CMD("QueryFuzzInsertVertex")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzUpdateVertex)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_UPDATE_VERTEX;
    DT_FUZZ_START_CMD("QueryFuzzUpdateVertex")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzDeleteVertex)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_DELETE_VERTEX;
    DT_FUZZ_START_CMD("QueryFuzzDeleteVertex")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzReplaceVertex)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_REPLACE_VERTEX;
    DT_FUZZ_START_CMD("QueryFuzzReplaceVertex")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzMergeVertex)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_MERGE_VERTEX;
    DT_FUZZ_START_CMD("QueryFuzzMergeVertex")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzInsertEdge)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_INSERT_EDGE;
    DT_FUZZ_START_CMD("QueryFuzzInsertEdge")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzDeleteEdge)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_DELETE_EDGE;
    DT_FUZZ_START_CMD("QueryFuzzDeleteEdge")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzUpdateCheckVersion)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_UPDATE_CHECK_VERSION;
    DT_FUZZ_START_CMD("QueryFuzzUpdateCheckVersion")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzCheckReplace)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_CHECK_REPLACE;
    DT_FUZZ_START_CMD("QueryFuzzCheckReplace")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzCreateSavepoint)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_TX_CREATE_SAVEPOINT;
    DT_FUZZ_START_CMD("QueryFuzzCreateSavepoint")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzReleaseSavepoint)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_TX_RELEASE_SAVEPOINT;
    DT_FUZZ_START_CMD("QueryFuzzReleaseSavepoint")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzFuzzRollbackSavepoint)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_TX_ROLLBACK_SAVEPOINT;
    DT_FUZZ_START_CMD("QueryFuzzRollbackSavepoint")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzGrantUserSystemPrivs)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_GRANT_USER_SYSTEM_PRIVS;
    DT_FUZZ_START_CMD("QueryFuzzGrantUserSystemPrivs")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzRevokeUserSystemPrivs)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_REVOKE_USER_SYSTEM_PRIVS;
    DT_FUZZ_START_CMD("QueryFuzzRevokeUserSystemPrivs")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzGrantObjectPrivs)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_GRANT_OBJECT_PRIVS;
    DT_FUZZ_START_CMD("QueryFuzzGrantObjectPrivs")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzRevokeObjectPrivs)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_REVOKE_OBJECT_PRIVS;
    DT_FUZZ_START_CMD("QueryFuzzRevokeObjectPrivs")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzSetConfig)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_SET_CFG;
    DT_FUZZ_START_CMD("QueryFuzzSetConfig")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzFlushData)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_FLUSH_DATA;
    DT_FUZZ_START_CMD("QueryFuzzFlushData")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzSetScheduleTaskCfg)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_SET_SCHEDULE_TASK_CFG;
    DT_FUZZ_START_CMD("QueryFuzzSetScheduleTaskCfg")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzSetUserRequestWeight)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_SET_USER_REQUEST_WEIGHT;
    DT_FUZZ_START_CMD("QueryFuzzSetUserRequestWeight")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzSetUserResourceLimit)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_IPS_SET_USER_RES_LIMIT;
    DT_FUZZ_START_CMD("QueryFuzzSetUserResourceLimit")
    {
        QrySetCfg("userPolicyMode", "2");
        QryFuzzTestInner(QueryFuzz::session, opCode);
        QrySetCfg("userPolicyMode", "1");
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzGetVertexLabel)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_GET_VERTEX_LABEL;
    DT_FUZZ_START_CMD("QueryFuzzGetVertexLabel")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzGetLabel)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_GET_LABEL;
    DT_FUZZ_START_CMD("QueryFuzzGetLabel")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzGetResPool)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_GET_RES_POOL;
    DT_FUZZ_START_CMD("QueryFuzzGetResPool")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzGetEdgeLabel)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_GET_EDGE_LABEL;
    DT_FUZZ_START_CMD("QueryFuzzGetEdgeLabel")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzGetConfig)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_GET_CFG;
    DT_FUZZ_START_CMD("QueryFuzzGetConfig")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzGetCheckInfo)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_GET_CHECK_INFO;
    DT_FUZZ_START_CMD("QueryFuzzGetCheckInfo")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzGetAlarmData)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_GET_ALARM_DATA;
    DT_FUZZ_START_CMD("QueryFuzzGetAlarmData")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzGetEstimateMemory)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_GET_ESTIMATE_MEMORY;
    DT_FUZZ_START_CMD("QueryFuzzGetEstimateMemory")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzGetStatCount)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_GET_STAT_COUNT;
    DT_FUZZ_START_CMD("QueryFuzzGetStatCount")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzScanVertexBegin)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_SCAN_VERTEX_BEGIN;
    DT_FUZZ_START_CMD("QueryFuzzScanVertexBegin")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzBeginScanPath)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_BEGIN_SCAN_PATH;
    DT_FUZZ_START_CMD("QueryFuzzBeginScanPath")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzGetCount)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_GET_COUNT;
    DT_FUZZ_START_CMD("QueryFuzzGetCount")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzSubtreeFilter)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_SUBTREE_FILTER;

    // json建模准备
    DT_Pits_SetBinFile("./json/FilterItemSubtreeJson.bin");
    int id = DT_Pits_ParseDataModel("./json/JsonNormal.xml", "jsona", 1);
    DT_Pits_Enable_AllMutater(1);

    DT_FUZZ_START_CMD("QueryFuzzSubtreeFilter")
    {
        DT_Pits_DoAction(id);
        char *dataout = NULL;
        int lenout = 0;
        DT_Pits_GetMutatorBuf(&dataout, &lenout);
        g_subtreeJson = malloc(lenout + 1);
        memcpy(g_subtreeJson, dataout, lenout);
        g_subtreeJson[lenout] = '\0';

        QryFuzzTestInner(QueryFuzz::session, opCode);

        free(g_subtreeJson);
        g_subtreeJson = NULL;
        DT_Pits_OneRunFree();
    }
    DT_FUZZ_END()

    // 释放json建模工具占用内存
    DT_Pits_ParseFree();
}

TEST_F(QueryFuzz, QueryFuzzTriggerScan)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_TRIGGER_SCAN;
    DT_FUZZ_START_CMD("QueryFuzzTriggerScan")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzPrefetchLabels)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_GET_VERTEX_LABELS;
    DT_FUZZ_START_CMD("QueryFuzzPrefetchLabels")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

Status FillDmlInsertHead(FixBufferT *req, uint32_t num)
{
    uint32_t vertexLabelId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 10086);
    uint32_t schemaVersion = 1;
    uint32_t uuid = 0;
    Status ret = FixBufPutUint32(req, vertexLabelId);  // vertexLabelId
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(req, schemaVersion);  // schemaVersion
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(req, uuid);  // uuid
    if (ret != GMERR_OK) {
        return ret;
    }
    return FixBufPutUint32(req, num);  // num
}

Status FillDmlInsertBody(FixBufferT *req, uint32_t num)
{
    Status ret = GMERR_OK;
    // dmlbody
    for (uint32_t i = 0; i < num; i++) {
        char *userName = "GMDBV5";
        TextT labelName = {.len = strlen(userName) + 1, .str = userName};
        ret = FixBufPutText(req, &labelName);
        if (ret != GMERR_OK) {
            return ret;
        }
        uint32_t tmpNum = 0;
        ret = FixBufPutUint32(req, tmpNum);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

// semi模式填充Insert请求消息体
Status FillInsertMsgSemi(FixBufferT *req, uint32_t num)
{
    Status ret = FillDmlInsertHead(req, num);
    if (ret != GMERR_OK) {
        return ret;
    }
    return FillDmlInsertBody(req, num);
}

Status FillDmlDeleteHead(FixBufferT *req, uint32_t num)
{
    uint32_t vertexLabelId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 10086);
    uint32_t schemaVersion = 1;
    uint32_t indexKeyId = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 1);
    uint32_t condStr = 0;
    Status ret = FixBufPutUint32(req, vertexLabelId);  // vertexLabel->metaCommon.metaId
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(req, schemaVersion);  // schemaVersion
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(req, indexKeyId);  // indexKeyId
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(req, condStr);  // condStr
    if (ret != GMERR_OK) {
        return ret;
    }
    return FixBufPutUint32(req, num);  // num
}

Status FillDmlDeleteBody(FixBufferT *req, uint32_t num)
{
    uint32_t rangeScanFlag = 0;
    uint32_t operateEdgeFlag = 0;
    Status ret = GMERR_OK;
    DmIndexKeyT *leftIndexKey = g_dmIndexKey;
    // 获取vertexLabel元数据
    while (num-- > 0) {
        ret = FixBufPutUint32(req, rangeScanFlag);  // rangeScanFlag
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = HelpFillIndexKey(req, (const DmIndexKeyT *)leftIndexKey);  // leftIndexKey
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = HelpFillIndexKey(req, NULL);  // rightIndexKey
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = FixBufPutUint32(req, operateEdgeFlag);  // operateEdgeFlag
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

Status FillDmlUpdateBody(FixBufferT *req, uint32_t num)
{
    uint32_t rangeScanFlag = 0;
    uint32_t operateEdgeFlag = 0;
    Status ret = GMERR_OK;
    DmIndexKeyT *indexKey = g_dmIndexKey;
    DmVertexT *vertex = g_DmVertex;
    while (num-- > 0) {
        ret = HelpFillIndexKey(req, (const DmIndexKeyT *)indexKey);  // indexKey
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = HelpFillVertex(req, (const DmVertexT *)vertex);  // vertex
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = FixBufPutUint32(req, operateEdgeFlag);  // operateEdgeFlag
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

// semi模式填充delete请求消息体
Status FillDeleteMsgSemi(FixBufferT *req, uint32_t num)
{
    Status ret = FillDmlDeleteHead(req, num);
    if (ret != GMERR_OK) {
        return ret;
    }
    return FillDmlDeleteBody(req, num);
}

// 填充update请求消息体
Status FillUpdateMsg(FixBufferT *req)
{
    Status ret = FillDmlDeleteHead(req, 1);
    if (ret != GMERR_OK) {
        return ret;
    }
    return FillDmlUpdateBody(req, 1);
}

static Status FillBatchReqBody(FixBufferT *req, MsgOpcodeRpcE opCode, uint32_t num)
{
    Status ret = FixBufPutUint32(req, opCode);
    if (ret != GMERR_OK) {
        return ret;
    }
    switch (opCode) {
        case MSG_OP_RPC_INSERT_VERTEX:
            return FillInsertMsgSemi(req, num);
        case MSG_OP_RPC_DELETE_VERTEX:
            return FillDeleteMsgSemi(req, num);
        default:
            return GMERR_OK;
    }
}

Status FillDmlMsg(FixBufferT *req, MsgOpcodeRpcE opCode, bool isBatch)
{
    Status ret = FillMsgHeader(req);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 填充opHeader
    FixBufSeek(req, MSG_HEADER_ALIGN_SIZE);  // seek to opHeader
    OpHeaderT *opHeader = FixBufGetData(req, MSG_OP_HEADER_ALIGN_SIZE);
    if (isBatch) {
        opHeader->opCode = MSG_OP_RPC_BATCH;
        BatchHeaderT batchHead = {0};
        batchHead.totalOpNum = (uint32_t) * (uint8_t *)DT_SetGetU8(&g_Element[g_count++], 2);  // 实际不会超过1024
        batchHead.batchErrCtl = 0;
        batchHead.batchOrder = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], GMC_BATCH_ORDER_SEMI);
        // 填充batchHeader
        ret = FixBufPutData(req, &batchHead, sizeof(BatchHeaderT));
        if (ret != GMERR_OK) {
            return ret;
        }
        return FillBatchReqBody(req, opCode, batchHead.totalOpNum);
    }
    opHeader->opCode = opCode;
    switch (opCode) {
        case MSG_OP_RPC_INSERT_VERTEX:
            return FillInsertMsgSemi(req, 1);
        case MSG_OP_RPC_DELETE_VERTEX:
            return FillDeleteMsgSemi(req, 1);
        case MSG_OP_RPC_UPDATE_VERTEX:
            return FillUpdateMsg(req);
        default:
            return GMERR_OK;
    }
    return GMERR_OK;
}

#ifdef FEATURE_DATALOG
Status QryFuzzDatalogTestInner(const SessionT *session, MsgOpcodeRpcE opCode, bool isBatch)
{
    // prepare VertexLabel and insert to catalog
    DmVertexLabelT *vertexLabel = GetDatalogVertexLabelForFuzz();
    DmVertexLabelT *tempLabel = NULL;
    Status ret = CataSaveVertexLabel(vertexLabel, &tempLabel);
    tempLabel->metaCommon.isCreated = true;
    if (ret != GMERR_OK) {
        return ret;
    }

    g_count = 0;
    DtlDmlInfoT dmlParamData = {};
    DtlDmlResInfoT dmlRes = {};
    ret = FillDmlMsg(session->req, opCode, isBatch);
    if (ret != GMERR_OK) {
        DbDynMemFree(dmlParamData.dmlAst);
        FixBufRelease(session->req);
        return ret;
    }
    dmlParamData.dataMemCtx = g_DymCtxMem;
    DtlDmlResInfoT dmlResp = {.soId = 0};
    ret = NewDtlSess(g_DymCtxMem, QrySessionGetServerTimeout(session), &session->datalogCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = DtlDmlParser(session, &dmlParamData, &dmlResp);
    // Req body中填充的信息在反序列化时会返回GMERR_DATA_EXCEPTION但不影响后续流程
    if (ret == GMERR_OK || ret == GMERR_DATA_EXCEPTION) {
        DtlDmlVerify(&dmlParamData, &dmlRes);
    }
    DbDynMemFree(dmlParamData.dmlAst);
    FixBufRelease(session->req);

    // clear VertexLabel
    uint32_t vlId = vertexLabel->metaCommon.metaId;
    CataRemoveVertexLabelById(NULL, vlId);

    return ret;
}

TEST_F(QueryFuzz, QueryFuzzDatalogRTInsert)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_INSERT_VERTEX;
    DT_FUZZ_START_CMD("QueryFuzzDatalogRTInsert")
    {
        QryFuzzDatalogTestInner(QueryFuzz::session, opCode, false);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzDatalogRTBatchInsert)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_INSERT_VERTEX;
    DT_FUZZ_START_CMD("QueryFuzzDatalogRTBatchInsert")
    {
        QryFuzzDatalogTestInner(QueryFuzz::session, opCode, true);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzDatalogRTDelete)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_DELETE_VERTEX;
    DT_FUZZ_START_CMD("QueryFuzzDatalogRTDelete")
    {
        QryFuzzDatalogTestInner(QueryFuzz::session, opCode, false);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzDatalogRTBatchDelete)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_DELETE_VERTEX;
    DT_FUZZ_START_CMD("QueryFuzzDatalogRTBatchDelete")
    {
        QryFuzzDatalogTestInner(QueryFuzz::session, opCode, true);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QueryFuzzDatalogRTUpdate)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_UPDATE_VERTEX;
    DT_FUZZ_START_CMD("QueryFuzzDatalogRTUpdate")
    {
        QryFuzzDatalogTestInner(QueryFuzz::session, opCode, false);
    }
    DT_FUZZ_END()
}
#endif

TEST_F(QueryFuzz, QryFuzzRejectOrAcceptProcess)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_IPS_REJECT_OR_ACCEPT_PROCESS;
    DT_FUZZ_START_CMD("QryFuzzRejectOrAcceptProcess")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}
TEST_F(QueryFuzz, QryFuzzCloseConnect)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_IPS_CLOSE_CONNECTION;

    DT_FUZZ_START_CMD("QryFuzzCloseConnect")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QryFuzzSetIpsUserMode)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_IPS_SET_USER_MODE;
    DT_FUZZ_START_CMD("QryFuzzSetIpsUserMode")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QryFuzzSetIpsUserProtocol)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_IPS_SET_USER_PROTOCOL;
    DT_FUZZ_START_CMD("QryFuzzSetIpsUserProtocol")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QryFuzzGetVertexLabelDegradeProgress)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_GET_LABEL_DEGRADE_PROGRESS;
    DT_FUZZ_START_CMD("QryFuzzGetVertexLabelDegradeProgress")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}

TEST_F(QueryFuzz, QryFuzzNoneVertex)
{
    MsgOpcodeRpcE opCode = MSG_OP_RPC_NONE_VERTEX;
    DT_FUZZ_START_CMD("QryFuzzNoneVertex")
    {
        QryFuzzTestInner(QueryFuzz::session, opCode);
    }
    DT_FUZZ_END()
}
