/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: common_gql.h
 * Author: guopanpan
 * Create: 2024-02-06
 */

#ifndef T_FUZZ_COMMON_GQL_H
#define T_FUZZ_COMMON_GQL_H

#include "fuzz_common_base.h"
#include "grd_db_api.h"
#include "grd_gql_api.h"
#include "grd_error.h"

#define GRD_GQL_LEN 512

struct ColumnDef {
    char name[32];
    GRD_DbDataTypeE type;
    int size;
    GRD_DbValueT val;
};
// 获取一个列的属性，包括名称、类型、值
int32_t GrdGqlColumnGetOne(GRD_StmtT *stmt, uint32_t index, ColumnDef *col);
// 获取一个查询结果的所有列的属性
int32_t GrdGqlColumnGetAll(GRD_StmtT *stmt, ColumnDef *col);
// 检查column：int
int32_t GrdGqlColumnCheckInt(GRD_StmtT *stmt, uint32_t index, int32_t expect);
// 检查column：int64
int32_t GrdGqlColumnCheckInt64(GRD_StmtT *stmt, uint32_t index, int64_t expect);
// 检查column：double
int32_t GrdGqlColumnCheckDouble(GRD_StmtT *stmt, uint32_t index, double expect);
// 检查column：string
int32_t GrdGqlColumnCheckText(GRD_StmtT *stmt, uint32_t index, char *expect);
// 获取column：int
int32_t GrdGqlColumnGetInt(GRD_StmtT *stmt, uint32_t index, int32_t *val);
// 获取column：int64
int32_t GrdGqlColumnGetInt64(GRD_StmtT *stmt, uint32_t index, int64_t *val);
// 获取column：double
int32_t GrdGqlColumnGetDouble(GRD_StmtT *stmt, uint32_t index, double *val);
// 获取column：string
int32_t GrdGqlColumnGetText(GRD_StmtT *stmt, uint32_t index, char *val, int size);

// 执行一个gql语句，打印这个gql语句的执行结果
int32_t GrdGqlPrintGqlRst(GRD_DB *conn, char *gql, uint32_t limit = 10, uint32_t width = 8);
// 查询一个表，全表的数据，并打印出来
int32_t GrdGqlPrintTableRst(GRD_DB *conn, const char *tableName, uint32_t limit = 1000, uint32_t width = 8);

// 检查表是否存在，与预期是否一致
int32_t GrdGqlTableExistCheck(GRD_DB *conn, const char *tableName, bool expectExist);
// 检查视图是否存在，与预期是否一致
int32_t GrdGqlViewExistCheck(GRD_DB *conn, const char *viewName, bool expectExist);
// 检查表是否存在，获取表是否存在信息
int32_t GrdGqlTableExistGet(GRD_DB *conn, const char *tableName, bool *isExist);
// 获取表的记录数
int32_t GrdGqlRecordCountGet(GRD_DB *conn, const char *tableName, uint32_t *count);
// 比较表的记录数
int32_t GrdGqlRecordCountCheck(GRD_DB *conn, const char *tableName, uint32_t count);

typedef std::vector<std::string> GrdGqlRow;
typedef std::vector<std::vector<std::string>> GrdGqlRows;
struct GrdGqlTestCtx {
    std::string gql;
    int32_t status;
    GrdGqlRows output;
};
// gql接口：打印结果信息。调用此接口前，确保已经调用GrdGqlColumnValue。
void GrdGqlPrintRst(GrdGqlRows result, bool haveHead = false, uint32_t width = 6);
// gql接口：缓存查询结果，调用此接口前，先确保执行过select操作。
int32_t GrdGqlColumnValue(GRD_StmtT *stmt, uint32_t columnCount, GrdGqlRows *table);
// gql测试入口函数: GRD_GqlExecute; 对外功能与GrdGqlStepAndCheck相同。
int32_t GrdGqlExecAndCheck(GRD_DB *conn, GrdGqlTestCtx *gqlTests, uint32_t size);
// gql测试入口函数: GRD_GqlPrepare * 1 + GRD_GqlStep * N; 对外功能与GrdGqlExecAndCheck相同。
int32_t GrdGqlStepAndCheck(GRD_DB *conn, GrdGqlTestCtx *gqlTests, uint32_t size, bool haveHeader = false);
// gql测试入口函数内部原子执行函数
int32_t GrdGqlStepAndCheckInner(
    GRD_DB *conn, GrdGqlTestCtx gqlTests, bool haveHeader = false, bool verifyResult = true);
// gql测试入口函数: GRD_GqlPrepare * 1 + GRD_GqlStep * N; 对外功能与GrdGqlExecAndCheck相同, 返回多结果。
int32_t GrdGqlCheckMultiRet(GRD_DB *conn, GrdGqlTestCtx *gqlTests, uint32_t size, bool haveHeader = false);
// 执行单条DML语句。
int GRDExecDML(GRD_DB *conn, std::string gql);
// 检查表的数据量和内容
int32_t GrdGqlCheckTable(GrdGqlRows expect, GrdGqlRows actual, bool haveHeader = false);

/*  通用召回率函数
  sQry: 采用索引近似搜索，如 select id, repr <=> '[2,3,4]' from t WHERE repr <=> '[2,3,4]' ORDER BY repr LIMIT 5;
  sScan：全表扫描搜索，用于生成标准答案，如 select id, repr <=> '[2,3,4]' from t;
*/
struct RecallRst {
    double rRate1;
    double rRate2;
    int64_t total;
    int64_t hit1;
    int64_t hit2;
    int64_t elapse;
    RecallRst()
    {
        rRate1 = 0;
        rRate2 = 0;
        total = 0;
        hit1 = 0;
        hit2 = 0;
        elapse = 0;
    }
    void CalRecall()
    {
        if (total == 0) {
            rRate1 = 0;
            rRate2 = 0;
        } else {
            rRate1 = (double)hit1 * 100 / total;
            rRate2 = (double)(hit1 + hit2) * 100 / total;
        }
    }
    void OutputRst()
    {
        printf("topK:%ld, hit:%ld|%ld, rate:%.1f|%.1f, delay:%ldus.\n", total, hit1, hit2, rRate1, rRate2, elapse);
    }
};
int GrdCalRecallRate(GRD_DB *conn, std::string sQry, std::string sScan, RecallRst *r);

typedef std::vector<std::string> VecList;
// 1 通用数据集(sift_1m,gist_1m)：DML操作（初始向量文件） */
//   获取指定的向量集合，保存在VecList中
int DataSetBaseGet(std::string dataSetName, int begin, int end, VecList *vl);
//   根据指定的向量集合，写入到表中。（约束，表的第一个字段必须为id，第二字段必须为repr）
int DataSetBaseInsert(GRD_DB *conn, std::string dataSetName, int begin, int end, std::string tableName);
//   根据指定的id集合，删除表中数据。（约束，同DataSetBaseInsert）
int DataSetBaseDelete(GRD_DB *conn, std::string dataSetName, int begin, int end, std::string tableName);
struct uptCtxT {
    bool removeRandomVal;  // 随机更新时有效，为真，则下次随机时，不能再使用随机到这个值
    /* 顺序更新  更新列表  id列表    行为
                   10       10       一一对应更新
                    2       10       10个id更新一遍，不同的id会有重复的值
                   10        5       更新5个id共10次，即每个id会被更新2次
    */
    int type;  // 0:顺序更新  1:顺序扫描id,随机更新值  2:顺序扫描更新列表,随机id  3 随机选择id,随机选择更新值，更新
    int posBegin;
    int posEnd;
    int cycle;  // 当type=3时，执行的次数
    uptCtxT(int pType)
    {
        type = pType;
        posBegin = -1;
        posEnd = -1;
        cycle = 1;
    }
    uptCtxT(int pType, int pCycle)
    {
        type = pType;
        posBegin = -1;
        posEnd = -1;
        cycle = pCycle;
    }
    uptCtxT(int pType, int pPosBegin, int pPosEnd)
    {
        type = pType;
        posBegin = pPosBegin;
        posEnd = pPosEnd;
        cycle = (pPosEnd - pPosBegin) * 3;
    }
    uptCtxT(int pType, int pPosBegin, int pPosEnd, int pCycle)
    {
        type = pType;
        posBegin = pPosBegin;
        posEnd = pPosEnd;
        cycle = pCycle;
    }
};
int DataSetBaseUpdate(
    GRD_DB *conn, std::string dataSetName, int begin, int end, std::string tableName, uptCtxT *uptCtx);
// 功能同DataSetBaseUpdate，更新条件列表由外部指定。示例：update t1 set vec = 'xxx'
// where${contition}，参数传入的是${contition}。
int DataSetBaseUpdateByCond(
    GRD_DB *conn, std::string dataSetName, VecList *condition, std::string tableName, uptCtxT *uptCtx);

// 2 根据外部输入向量列表：DML操作
//   读取外部文件的数据，保存在vl向量列表中
int OutsideDataGetFile(std::string fileName, int begin, int end, VecList *vl);
//   生成随机向量：指定维度，列表个数，随机值范围[valBegin, valEnd]，如果valBegin是整数，随机数也是整数，否则为浮点数
int OutsideDataGetRandomInt(int dim, int num, int begin, int end, VecList *vl, bool isFloat = false);
int OutsideDataGetRandomFloat(int dim, int num, int begin, int end, VecList *vl);
int OutsideDataInsert(GRD_DB *conn, VecList *vl, int begin, int end, std::string tableName);
int OutsideDataDelete(GRD_DB *conn, int begin, int end, std::string tableName);
int OutsideDataUpdate(GRD_DB *conn, VecList *vl, int begin, int end, std::string tableName, uptCtxT *uptCtx);
int OutsideDataUpdateByCond(GRD_DB *conn, VecList *vl, VecList *condition, std::string tableName, uptCtxT *uptCtx);

// 近似搜索
/* 通用数据集(sift_1m,gist_1m)：近似搜索 */
// 获取指定的向量集合，保存在VecList中
int DataSetQueryGet(std::string dataSetName, int begin, int end, VecList *vl);
// 近似搜索上下文定义
struct ANNQryContent {
    std::string tableName;
    int disType;  // 0:L2, 1:consine
    int topK;
    RecallRst rec;
    ANNQryContent()
    {
        tableName = std::string("t1");
        disType = 0;
        topK = 10;
        memset(&rec, 0, sizeof(struct RecallRst));
    }
    ANNQryContent(const char *name, int type, int num)
    {
        tableName = name;
        disType = type;
        topK = num;
        memset(&rec, 0, sizeof(struct RecallRst));
    }
};
// 根据外部输入的查询向量集合，执行近似搜索。
int DataSetANNQry1(GRD_DB *conn, VecList vl, ANNQryContent *ctx);
// 根据指定的查询向量集合，执行近似搜索。
int DataSetANNQry2(GRD_DB *conn, std::string dataSetName, int begin, int end, ANNQryContent *ctx);

// 环境初始化
int32_t GrdGqlEnvInit();
// 环境去初始化
int32_t GrdGqlEnvClean();

#endif /* T_COMMON_gql_H */
