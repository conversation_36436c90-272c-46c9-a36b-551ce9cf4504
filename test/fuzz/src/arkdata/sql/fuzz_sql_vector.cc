#include <algorithm>
#include <cmath>
#include <sstream>
#include <vector>

#include "fuzz_sql_test_tool.h"

static const int32_t DISKANN_NUMSAMPLES_3 = 3;
static const int32_t DISKANN_NUMSAMPLES_4 = 4;
static const int32_t DISKANN_NUMSAMPLES_6 = 6;
static const int32_t DISKANN_NUMSAMPLES_9 = 9;
static const int32_t DISKANN_NUMSAMPLES_50 = 50;
static const int32_t DISKANN_NUMSAMPLES_100 = 100;
static const int32_t DISKANN_NUMSAMPLES_200 = 200;
static const int32_t DISKANN_NUMSAMPLES_500 = 500;
static const int32_t DISKANN_NUMSAMPLES_1000 = 1000;
static const int32_t DISKANN_NUMSAMPLES_5000 = 5000;

static const float FLOAT_MAX = static_cast<float>(0x7FFFFFFF);

static const uint32_t T_DEFAULT_BIND_IDX_ID = 1;
static const uint32_t T_DEFAULT_BIND_IDX_REPR = 2;

static const char *g_sqlDatafilePath = "./data/gmdb/";
static const char *g_sqlDbFile = "./data/gmdb/datafile";

static uint16_t g_dim = 8;
static uint32_t g_count = 0;

static char g_cfg[] = R"(
{
    "pageSize": 8,
    "crcCheckEnable": 0,
    "bufferPoolSize": 10240,
    "defaultTransactionType": 0,
    "defaultIsolationLevel": 3,
    "sharedModeEnable" : 1,
    "redoFlushByTrx" : 1
})";

class FuzzGrdSqlVector : public testing::Test {
public:
    static void SetUpTestCase(void);
    static void TearDownTestCase(void);
    void SetUp(void);
    void TearDown(void);
};

void FuzzGrdSqlVector::SetUpTestCase(void)
{
    EXPECT_EQ(FuzzGrdSqlTestTool::RemoveDir(g_sqlDatafilePath), 0);
    EXPECT_EQ(FuzzGrdSqlTestTool::MakeDir(g_sqlDatafilePath), 0);
    int32_t ret = GRD_DBOpen(g_sqlDbFile, g_cfg, GRD_DB_OPEN_CREATE, &g_db);
    EXPECT_EQ(ret, GRD_OK);
}

void FuzzGrdSqlVector::TearDownTestCase(void)
{
    ASSERT_EQ(GRD_OK, GRD_DBClose(g_db, GRD_DB_CLOSE));
}

void FuzzGrdSqlVector::SetUp(void)
{
    DT_Enable_Leak_Check(0, 0);
    const char *sql = "create table t1(id int primary key, repr floatvector(8));";
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    GRD_StmtT *stmt = nullptr;
    const char *unused = nullptr;
    EXPECT_EQ(GRD_SqlPrepare(g_db, sql, len, &stmt, &unused), GRD_OK);
    EXPECT_EQ(GRD_OK, GRD_SqlStep(stmt));
    EXPECT_EQ(GRD_OK, GRD_SqlFinalize(stmt));
}

void FuzzGrdSqlVector::TearDown(void)
{
    const char *sql = "DROP table t1;";
    uint32_t len = (uint32_t)(strlen(sql) + 1);
    GRD_StmtT *stmt = nullptr;
    const char *unused = nullptr;
    EXPECT_EQ(GRD_SqlPrepare(g_db, sql, len, &stmt, &unused), GRD_OK);
    EXPECT_EQ(GRD_OK, GRD_SqlStep(stmt));
    EXPECT_EQ(GRD_OK, GRD_SqlFinalize(stmt));
}

static void UtBuildIndex(bool isIvfIndex)
{
    if (isIvfIndex) {
        printf("开始基于IVFFLAT构建索引\n");
    } else {
        printf("开始基于DiskANN构建索引\n");
    }

    const char *sqlCreateIdx =
        isIvfIndex ? "CREATE INDEX ivfflat_L2_idx ON t1 USING GSIVFFLAT(repr L2) with (enable_adaptive=true);" :
                     "CREATE INDEX diskann_L2_idx ON t1 USING GSDISKANN(repr L2);";
    const char *unused = nullptr;
    GRD_StmtT *stmt = nullptr;
    GRD_SqlPrepare(g_db, sqlCreateIdx, strlen(sqlCreateIdx) + 1, &stmt, &unused);
    GRD_SqlStep(stmt);
    GRD_SqlReset(stmt);
    GRD_SqlFinalize(stmt);
    printf("Index build done\n");
}

static void UtExecuteQuery(const char *querySql, int *resultsId, uint32_t length)
{
    const char *unused = nullptr;
    GRD_StmtT *stmt = nullptr;
    GRD_SqlPrepare(g_db, querySql, strlen(querySql) + 1, &stmt, &unused);
    uint32_t ret = GRD_OK;
    for (uint32_t j = 0; (ret = GRD_SqlStep(stmt)) == GRD_OK; ++j) {
        if (j > length) {
            break;
        }
        resultsId[j] = GRD_SqlColumnInt(stmt, 0);
    }
    GRD_SqlReset(stmt);
    GRD_SqlFinalize(stmt);
}

static std::string UtGenerateIntString(uint32_t number)
{
    std::stringstream ss;
    ss << number;
    return ss.str();
}

static std::string GenerateVectorString(const std::vector<float> &numbers)
{
    std::stringstream ss;
    for (uint32_t i = 0; i < numbers.size(); ++i) {
        ss << numbers[i];
        if (i != numbers.size() - 1) {
            ss << ",";
        }
    }
    return ss.str();
}

static void UtSimQueryVector(const std::vector<float> &findVector, uint32_t topN, int *resultsId)
{
    std::string sqlS = "SELECT * FROM t1 ORDER BY repr <-> '[" + GenerateVectorString(findVector) + "]' LIMIT " +
                       UtGenerateIntString(topN) + " ;";
    const char *querySql = sqlS.data();
    UtExecuteQuery(querySql, resultsId, topN);
}

static void UtExactQuery(std::vector<std::vector<float>> queryDataArray, int *resultIds, uint32_t length)
{
    for (uint32_t i = 0; i < length; i++) {
        std::vector<float> findVector = queryDataArray[i];
        int result[1] = {-1};
        UtSimQueryVector(findVector, 1, result);
    }
}

static void UtQueryByVector(std::vector<float> findVector, int *resultsId)
{
    const char *unused = nullptr;
    GRD_StmtT *stmt = nullptr;
    const char *querySql = "SELECT id FROM t1 where repr <-> ? = 0;";
    GRD_SqlPrepare(g_db, querySql, strlen(querySql) + 1, &stmt, &unused);
    GRD_SqlBindFloatVector(stmt, 1, static_cast<float *>(findVector.data()), g_dim, nullptr);
    uint32_t ret = GRD_OK;
    for (int32_t j = 0; (ret = GRD_SqlStep(stmt)) == GRD_OK; ++j) {
        *resultsId = GRD_SqlColumnInt(stmt, 0);
    }
    GRD_OK, GRD_SqlReset(stmt);
    GRD_OK, GRD_SqlFinalize(stmt);
}

static void UtDeleteVectorById(int id)
{
    const char *unused = nullptr;
    GRD_StmtT *stmt = nullptr;
    const char *deleteSql = "DELETE FROM t1 where id = ?;";
    GRD_SqlPrepare(g_db, deleteSql, strlen(deleteSql) + 1, &stmt, &unused);
    GRD_SqlBindInt(stmt, 1, id);
    GRD_SqlStep(stmt);
    GRD_SqlReset(stmt);
    GRD_SqlFinalize(stmt);
}

static void UtUpdateVectorById(const std::vector<float> &vector, int id)
{
    const char *unused = nullptr;
    GRD_StmtT *stmt = nullptr;
    std::string sqlS = "UPDATE t1 SET repr = '[" + GenerateVectorString(vector) + "]' where id = ?;";
    const char *updateSql = sqlS.data();
    GRD_SqlPrepare(g_db, updateSql, strlen(updateSql) + 1, &stmt, &unused);
    GRD_SqlBindInt(stmt, 1, id);
    GRD_SqlStep(stmt);
    GRD_SqlReset(stmt);
    GRD_SqlFinalize(stmt);
}

static float RoundToPrecision(float var, int precision = 2)
{
    // if precision = 3 then
    // 37.66666 * 10^3 =37666.66
    // 37666.66 + .5 =37667.1    for rounding off value
    // then type cast to <int> so value is 37667
    // then divided by 10^3 so the value converted into 37.667
    if (precision < 0) {
        precision = 0;
    }
    float value = (var >= 0) ? (int)(var * pow(10, precision) + .5) : (int)(var * pow(10, precision) - .5);
    return value / pow(10, precision);
}

typedef struct StParameterTestCtx {
    uint32_t dim;
    uint32_t insertNum;
    std::string errMsg = "";            // 校验出错时打印报错信息
    std::string parameterList = "";     // WITH (*), * 指代的内容
    std::string setParameterList = "";  // SET *, * 指代的内容
    int32_t expectRetForVectorIndex;
    int32_t expectRetForSetParam;
    bool indexBeforeInsert;
    bool indexAfterInsert;
    bool indexBeforeIsDiskAnn;
    bool indexAfterIsDiskAnn;
    bool isCosine;
    bool skipReOpen;
} StParameterTestCtxT;

void FuzzReOpenDb(void)
{
    int32_t ret = GRD_DBClose(g_db, GRD_DB_CLOSE);
    EXPECT_EQ(ret, GRD_OK);
    ret = GRD_DBOpen(g_sqlDbFile, g_cfg, GRD_DB_OPEN_CREATE, &g_db);
    EXPECT_EQ(ret, GRD_OK);
}

float *FuzzGenerateRandomFloatVector(uint32_t dim)
{
    // caller should delete vec
    float *vec = new float[dim];
    for (uint32_t i = 0; i < dim; ++i) {
        // vec[i] = *(float *)DT_SetGetFloat(&g_Element[g_count++], 0);
        vec[i] = 1.1;
    }
    return vec;
}
std::string FuzzFloatVectorToString(const float *vec, uint32_t dim)
{
    std::string str = "'[";
    for (uint32_t i = 0; i < dim - 1; ++i) {
        str += std::to_string(vec[i]) + ",";
    }
    str += std::to_string(vec[dim - 1]) + "]'";
    return str;
}

static void FuzzPrepareDbForParameterTest(GRD_DB *db, StParameterTestCtxT *ctx)
{
    std::string sqlCreateTable = "CREATE TABLE tbl_parameter(id int, name text, score double, repr floatvector(" +
                                 std::to_string(ctx->dim) + "));";
    FuzzGrdSqlTestTool::FuzzExecuteSingleSqlWithoutCheck(db, sqlCreateTable);

    std::string distType = ctx->isCosine ? "COSINE" : "L2";
    std::string createParam = ctx->parameterList.compare("DoNothing") == 0 ? "" : " WITH (" + ctx->parameterList + ")";
    if (ctx->indexBeforeInsert) {
        std::string indexType = ctx->indexBeforeIsDiskAnn ? "GSDISKANN" : "GSIVFFLAT";
        std::string sqlCreateIndex = "CREATE INDEX idx_before_insert  ON tbl_parameter USING " + indexType + "(repr " +
                                     distType + ")" + createParam + ";";
        FuzzGrdSqlTestTool::FuzzExecuteSingleSqlWithoutCheck(db, sqlCreateIndex);
    }

    for (uint16_t i = 0; i < ctx->insertNum; i++) {
        float *vec = FuzzGenerateRandomFloatVector(ctx->dim);
        std::string vectorStr = FuzzFloatVectorToString(vec, ctx->dim);
        delete vec;
        std::string sqlInsert = "INSERT INTO tbl_parameter VALUES(1000000" + std::to_string(i) + ", 'name_" +
                                std::to_string(i) + "', " + std::to_string(i) + ", " + vectorStr + ");";
        FuzzGrdSqlTestTool::FuzzExecuteSingleSqlWithoutCheck(db, sqlInsert);
    }

    if (ctx->indexAfterInsert) {
        std::string indexType = ctx->indexAfterIsDiskAnn ? "GSDISKANN" : "GSIVFFLAT";
        std::string sqlCreateIndex = "CREATE INDEX idx_after_insert  ON tbl_parameter USING " + indexType + "(repr " +
                                     distType + ")" + createParam + ";";
        FuzzGrdSqlTestTool::FuzzExecuteSingleSqlWithoutCheck(db, sqlCreateIndex);
    }
}

static void FuzzQueryForParameterTest(GRD_DB *db, uint32_t dim)
{
    float *vec = FuzzGenerateRandomFloatVector(dim);
    std::string queryVector = FuzzFloatVectorToString(vec, dim);
    delete vec;
    uint32_t limit = 10;
    std::string sql = "SELECT repr <-> " + queryVector + " FROM tbl_parameter order by repr <-> " + queryVector +
                      " limit " + std::to_string(limit) + ";";
    GRD_StmtT *stmt = nullptr;
    int32_t ret = GRD_SqlPrepare(db, sql.c_str(), sql.size(), &stmt, nullptr);
    GRD_SqlStep(stmt);
    GRD_SqlFinalize(stmt);
}

static void FuzzClearDbForParameterTest(GRD_DB *db)
{
    FuzzGrdSqlTestTool::FuzzExecuteSingleSqlWithoutCheck(db, "DROP TABLE tbl_parameter;");
}

void FuzzGrdParameterTestBasic(StParameterTestCtxT *inputCtx)
{
    StParameterTestCtxT ctx = *inputCtx;
    ctx.errMsg = inputCtx->errMsg + inputCtx->parameterList;
    // L2
    ctx.isCosine = false;
    FuzzPrepareDbForParameterTest(g_db, &ctx);
    if (ctx.setParameterList.compare("") != 0) {
        std::string sql = "SET " + ctx.setParameterList + ";";
        FuzzGrdSqlTestTool::FuzzExecuteSingleSqlWithoutCheck(g_db, sql);
    }
    if (!ctx.skipReOpen) {
        FuzzReOpenDb();
    }
    FuzzQueryForParameterTest(g_db, ctx.dim);
    FuzzClearDbForParameterTest(g_db);

    // COSINE
    ctx.isCosine = true;
    FuzzPrepareDbForParameterTest(g_db, &ctx);
    if (ctx.setParameterList.compare("") != 0) {
        std::string sql = "SET " + ctx.setParameterList + ";";
        FuzzGrdSqlTestTool::FuzzExecuteSingleSqlWithoutCheck(g_db, sql);
    }
    if (!ctx.skipReOpen) {
        FuzzReOpenDb();
    }
    FuzzQueryForParameterTest(g_db, ctx.dim);
    FuzzClearDbForParameterTest(g_db);
}

/* begin test */
TEST_F(FuzzGrdSqlVector, FuzzGrdSqlVectorInsert001){
    char func[] = "FuzzGrdSqlVectorInsert001";
    DT_FUZZ_START_CMD(func) {
        g_count = 0;
        uint32_t numSamples = DISKANN_NUMSAMPLES_50;
        uint32_t numPerClass = 10;
        std::vector<std::vector<float>> vectors;
        int dataId[DISKANN_NUMSAMPLES_50] = {0};
        float randomNumber = *(float *)DT_SetGetFloat(&g_Element[g_count++], 0);

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            for (uint16_t j = 0; j < g_dim; j++) {
                float tmpVal = base * 10 + ((randomNumber / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }
        UtBuildIndex(false);

        const char *unused = nullptr;
        GRD_StmtT *stmt = nullptr;
        const char *insertSql = "INSERT INTO t1 VALUES(?, ?);";
        GRD_SqlPrepare(g_db, insertSql, strlen(insertSql) + 1, &stmt, &unused);
        for (uint32_t i = 0; i < numSamples; i++) {
            GRD_SqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, i);
            GRD_SqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vectors[i].data(), g_dim, nullptr);
            GRD_SqlStep(stmt);
            GRD_SqlReset(stmt);
        }
        GRD_SqlFinalize(stmt);

        UtExactQuery(vectors, dataId, numSamples);
    }
    DT_FUZZ_END()
}

TEST_F(FuzzGrdSqlVector, FuzzGrdSqlVectorInsert002) {
    char func[] = "FuzzGrdSqlVectorInsert002";
    DT_FUZZ_START_CMD(func) {
        g_count = 0;
        uint32_t numSamples = DISKANN_NUMSAMPLES_50;
        uint32_t numPerClass = 10;
        std::vector<std::vector<float>> vectors;
        int dataId[DISKANN_NUMSAMPLES_50] = {0};

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            for (uint16_t j = 0; j < g_dim; j++) {
                float regularData = (float)i + (float)j * 0.1;
                float tmpVal = base * 10 + ((regularData / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }
        UtBuildIndex(false);

        const char *unused = nullptr;
        GRD_StmtT *stmt = nullptr;
        const char *insertSql = "INSERT INTO t1 VALUES(?, ?);";
        GRD_SqlPrepare(g_db, insertSql, strlen(insertSql) + 1, &stmt, &unused);
        uint32_t bindData = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
        for (uint32_t i = 0; i < numSamples; i++) {
            GRD_SqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, i + bindData);
            GRD_SqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vectors[i].data(), g_dim, nullptr);
            GRD_SqlStep(stmt);
            GRD_SqlReset(stmt);
        }
        GRD_SqlFinalize(stmt);

        UtExactQuery(vectors, dataId, numSamples);
    }
    DT_FUZZ_END()
}

TEST_F(FuzzGrdSqlVector, FuzzGrdSqlVectorInsert003)
{
    g_dtRunCount = 2;  // 大数据量, 耗时用例,修改随机次数, 减少耗时
    char func[] = "FuzzGrdSqlVectorInsert003";
    DT_FUZZ_START_CMD(func) {
        g_count = 0;
        uint32_t numSamples = DISKANN_NUMSAMPLES_1000;
        uint32_t numPerClass = 10;
        std::vector<std::vector<float>> vectors;
        int dataId[DISKANN_NUMSAMPLES_1000] = {0};
        float randomNumber = *(float *)DT_SetGetFloat(&g_Element[g_count++], 0);

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            for (uint16_t j = 0; j < g_dim; j++) {
                float tmpVal = base * 10 + ((randomNumber / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }
        UtBuildIndex(true);

        const char *unused = nullptr;
        GRD_StmtT *stmt = nullptr;
        const char *insertSql = "INSERT INTO t1 VALUES(?, ?);";
        GRD_SqlPrepare(g_db, insertSql, strlen(insertSql) + 1, &stmt, &unused);
        for (uint32_t i = 0; i < numSamples; i++) {
            GRD_SqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, i);
            GRD_SqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vectors[i].data(), g_dim, nullptr);
            GRD_SqlStep(stmt);
            GRD_SqlReset(stmt);
        }
        GRD_SqlFinalize(stmt);

        UtExactQuery(vectors, dataId, numSamples);
    }
    DT_FUZZ_END()
}

TEST_F(FuzzGrdSqlVector, FuzzGrdSqlVectorInsert004) {
    char func[] = "FuzzGrdSqlVectorInsert004";
    DT_FUZZ_START_CMD(func) {
        g_count = 0;
        uint32_t numSamples = DISKANN_NUMSAMPLES_50;
        uint32_t numPerClass = 10;
        std::vector<std::vector<float>> vectors;
        int dataId[DISKANN_NUMSAMPLES_50] = {0};

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            for (uint16_t j = 0; j < g_dim; j++) {
                float regularData = (float)i + (float)j * 0.1;
                float tmpVal = base * 10 + ((regularData / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }
        UtBuildIndex(true);

        const char *unused = nullptr;
        GRD_StmtT *stmt = nullptr;
        const char *insertSql = "INSERT INTO t1 VALUES(?, ?);";
        GRD_SqlPrepare(g_db, insertSql, strlen(insertSql) + 1, &stmt, &unused);
        uint32_t bindData = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
        for (uint32_t i = 0; i < numSamples; i++) {
            GRD_SqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, i + bindData);
            GRD_SqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vectors[i].data(), g_dim, nullptr);
            GRD_SqlStep(stmt);
            GRD_SqlReset(stmt);
        }
        GRD_SqlFinalize(stmt);
        UtExactQuery(vectors, dataId, numSamples);
    }
    DT_FUZZ_END()
}

TEST_F(FuzzGrdSqlVector, FuzzGrdSqlVectorDelete001) {
    char func[] = "FuzzGrdSqlVectorDelete001";
    DT_FUZZ_START_CMD(func) {
        g_count = 0;
        uint32_t numSamples = DISKANN_NUMSAMPLES_50;
        uint32_t numPerClass = 10;
        std::vector<std::vector<float>> vectors;
        int dataId[DISKANN_NUMSAMPLES_50] = {0};

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            float randomNumber = *(float *)DT_SetGetFloat(&g_Element[g_count++], 0);
            for (uint16_t j = 0; j < g_dim; j++) {
                float tmpVal = base * 10 + ((randomNumber / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }
        UtBuildIndex(false);

        const char *unused = nullptr;
        GRD_StmtT *stmt = nullptr;
        const char *insertSql = "INSERT INTO t1 VALUES(?, ?);";
        GRD_SqlPrepare(g_db, insertSql, strlen(insertSql) + 1, &stmt, &unused);
        for (uint32_t i = 0; i < numSamples; i++) {
            GRD_SqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, i);
            GRD_SqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vectors[i].data(), g_dim, nullptr);
            GRD_SqlStep(stmt);
            GRD_SqlReset(stmt);
        }
        GRD_SqlFinalize(stmt);

        UtExactQuery(vectors, dataId, numSamples);

        for (uint32_t i = 0; i < numSamples; i++) {
            UtDeleteVectorById(dataId[i]);
            std::vector<float> findVector = vectors[i];
            int result = -1;
            UtQueryByVector(findVector, &result);
        }
    }
    DT_FUZZ_END()
}

TEST_F(FuzzGrdSqlVector, FuzzGrdSqlVectorDelete002) {
    char func[] = "FuzzGrdSqlVectorDelete002";
    DT_FUZZ_START_CMD(func) {
        g_count = 0;
        uint32_t numSamples = DISKANN_NUMSAMPLES_50;
        uint32_t numPerClass = 10;
        std::vector<std::vector<float>> vectors;
        int dataId[DISKANN_NUMSAMPLES_50] = {0};

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            for (uint16_t j = 0; j < g_dim; j++) {
                float regularData = (float)i + (float)j * 0.1;
                float tmpVal = base * 10 + ((regularData / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }
        UtBuildIndex(false);

        const char *unused = nullptr;
        GRD_StmtT *stmt = nullptr;
        const char *insertSql = "INSERT INTO t1 VALUES(?, ?);";
        GRD_SqlPrepare(g_db, insertSql, strlen(insertSql) + 1, &stmt, &unused);
        uint32_t bindData = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
        for (uint32_t i = 0; i < numSamples; i++) {
            GRD_SqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, i + bindData);
            GRD_SqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vectors[i].data(), g_dim, nullptr);
            GRD_SqlStep(stmt);
            GRD_SqlReset(stmt);
        }
        GRD_SqlFinalize(stmt);

        UtExactQuery(vectors, dataId, numSamples);

        for (uint32_t i = 0; i < numSamples; i++) {
            UtDeleteVectorById(dataId[i]);
            std::vector<float> findVector = vectors[i];
            int result = -1;
            UtQueryByVector(findVector, &result);
        }
    }
    DT_FUZZ_END()
}

TEST_F(FuzzGrdSqlVector, FuzzGrdSqlVectorDelete003) {
    char func[] = "FuzzGrdSqlVectorDelete003";
    DT_FUZZ_START_CMD(func) {
        g_count = 0;
        uint32_t numSamples = DISKANN_NUMSAMPLES_50;
        uint32_t numPerClass = 10;
        std::vector<std::vector<float>> vectors;
        int dataId[DISKANN_NUMSAMPLES_50] = {0};

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            float randomNumber = *(float *)DT_SetGetFloat(&g_Element[g_count++], 0);
            for (uint16_t j = 0; j < g_dim; j++) {
                float tmpVal = base * 10 + ((randomNumber / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }
        UtBuildIndex(true);

        const char *unused = nullptr;
        GRD_StmtT *stmt = nullptr;
        const char *insertSql = "INSERT INTO t1 VALUES(?, ?);";
        GRD_SqlPrepare(g_db, insertSql, strlen(insertSql) + 1, &stmt, &unused);
        for (uint32_t i = 0; i < numSamples; i++) {
            GRD_SqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, i);
            GRD_SqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vectors[i].data(), g_dim, nullptr);
            GRD_SqlStep(stmt);
            GRD_SqlReset(stmt);
        }
        GRD_SqlFinalize(stmt);

        UtExactQuery(vectors, dataId, numSamples);

        for (uint32_t i = 0; i < numSamples; i++) {
            UtDeleteVectorById(dataId[i]);
            std::vector<float> findVector = vectors[i];
            int result = -1;
            UtQueryByVector(findVector, &result);
        }
    }
    DT_FUZZ_END()
}

TEST_F(FuzzGrdSqlVector, FuzzGrdSqlVectorDelete004) {
    char func[] = "FuzzGrdSqlVectorDelete004";
    DT_FUZZ_START_CMD(func) {
        g_count = 0;
        uint32_t numSamples = DISKANN_NUMSAMPLES_50;
        uint32_t numPerClass = 10;
        std::vector<std::vector<float>> vectors;
        int dataId[DISKANN_NUMSAMPLES_50] = {0};

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            for (uint16_t j = 0; j < g_dim; j++) {
                float regularData = (float)i + (float)j * 0.1;
                float tmpVal = base * 10 + ((regularData / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }
        UtBuildIndex(true);

        const char *unused = nullptr;
        GRD_StmtT *stmt = nullptr;
        const char *insertSql = "INSERT INTO t1 VALUES(?, ?);";
        GRD_SqlPrepare(g_db, insertSql, strlen(insertSql) + 1, &stmt, &unused);
        uint32_t bindData = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
        for (uint32_t i = 0; i < numSamples; i++) {
            GRD_SqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, i + bindData);
            GRD_SqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vectors[i].data(), g_dim, nullptr);
            GRD_SqlStep(stmt);
            GRD_SqlReset(stmt);
        }
        GRD_SqlFinalize(stmt);

        UtExactQuery(vectors, dataId, numSamples);

        for (uint32_t i = 0; i < numSamples; i++) {
            UtDeleteVectorById(dataId[i]);
            std::vector<float> findVector = vectors[i];
            int result = -1;
            UtQueryByVector(findVector, &result);
        }
    }
    DT_FUZZ_END()
}

TEST_F(FuzzGrdSqlVector, FuzzGrdSqlVectorUpdate001) {
    char func[] = "FuzzGrdSqlVectorUpdate001";
    DT_FUZZ_START_CMD(func) {
        g_count = 0;
        uint32_t numSamples = DISKANN_NUMSAMPLES_50;
        uint32_t numPerClass = 10;
        std::vector<std::vector<float>> vectors;
        int dataId[DISKANN_NUMSAMPLES_50] = {0};

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            float randomNumber = *(float *)DT_SetGetFloat(&g_Element[g_count++], 0);
            for (uint16_t j = 0; j < g_dim; j++) {
                float tmpVal = base * 10 + ((randomNumber / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }
        UtBuildIndex(false);

        const char *unused = nullptr;
        GRD_StmtT *stmt = nullptr;
        const char *insertSql = "INSERT INTO t1 VALUES(?, ?);";
        GRD_SqlPrepare(g_db, insertSql, strlen(insertSql) + 1, &stmt, &unused);
        for (uint32_t i = 0; i < numSamples; i++) {
            GRD_SqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, i);
            GRD_SqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vectors[i].data(), g_dim, nullptr);
            GRD_SqlStep(stmt);
            GRD_SqlReset(stmt);
        }
        GRD_SqlFinalize(stmt);

        UtExactQuery(vectors, dataId, numSamples);

        vectors.clear();
        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            for (uint16_t j = 0; j < g_dim; j++) {
                float regularData = (float)i + (float)j * 0.1;
                float tmpVal = base * 10 + ((regularData / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> newVector = vectors[i];
            UtUpdateVectorById(newVector, dataId[i]);

            int resultsId[1] = {0};
            UtSimQueryVector(newVector, 1, resultsId);
        }
        UtExactQuery(vectors, dataId, numSamples);
    }
    DT_FUZZ_END()
}

TEST_F(FuzzGrdSqlVector, FuzzGrdSqlVectorUpdate002) {
    char func[] = "FuzzGrdSqlVectorUpdate002";
    DT_FUZZ_START_CMD(func) {
        g_count = 0;
        uint32_t numSamples = DISKANN_NUMSAMPLES_50;
        uint32_t numPerClass = 10;
        std::vector<std::vector<float>> vectors;
        int dataId[DISKANN_NUMSAMPLES_50] = {0};

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            for (uint16_t j = 0; j < g_dim; j++) {
                float regularData = (float)i + (float)j * 0.1;
                float tmpVal = base * 10 + ((regularData / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }
        UtBuildIndex(false);

        const char *unused = nullptr;
        GRD_StmtT *stmt = nullptr;
        const char *insertSql = "INSERT INTO t1 VALUES(?, ?);";
        GRD_SqlPrepare(g_db, insertSql, strlen(insertSql) + 1, &stmt, &unused);
        uint32_t bindData = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
        for (uint32_t i = 0; i < numSamples; i++) {
            GRD_SqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, i + bindData);
            GRD_SqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vectors[i].data(), g_dim, nullptr);
            GRD_SqlStep(stmt);
            GRD_SqlReset(stmt);
        }
        GRD_SqlFinalize(stmt);

        UtExactQuery(vectors, dataId, numSamples);

        vectors.clear();
        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            for (uint16_t j = 0; j < g_dim; j++) {
                float regularData = (float)i + (float)j * 0.1;
                float tmpVal = base * 10 + ((regularData / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> newVector = vectors[i];
            UtUpdateVectorById(newVector, dataId[i]);

            int resultsId[1] = {0};
            UtSimQueryVector(newVector, 1, resultsId);
        }
        UtExactQuery(vectors, dataId, numSamples);
    }
    DT_FUZZ_END()
}

TEST_F(FuzzGrdSqlVector, FuzzGrdSqlVectorUpdate003) {
    char func[] = "FuzzGrdSqlVectorUpdate003";
    DT_FUZZ_START_CMD(func) {
        g_count = 0;
        uint32_t numSamples = DISKANN_NUMSAMPLES_50;
        uint32_t numPerClass = 10;
        std::vector<std::vector<float>> vectors;
        int dataId[DISKANN_NUMSAMPLES_50] = {0};

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            for (uint16_t j = 0; j < g_dim; j++) {
                float regularData = (float)i + (float)j * 0.1;
                float tmpVal = base * 10 + ((regularData / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }
        UtBuildIndex(false);

        const char *unused = nullptr;
        GRD_StmtT *stmt = nullptr;
        const char *insertSql = "INSERT INTO t1 VALUES(?, ?);";
        GRD_SqlPrepare(g_db, insertSql, strlen(insertSql) + 1, &stmt, &unused);
        for (uint32_t i = 0; i < numSamples; i++) {
            GRD_SqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, i);
            GRD_SqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vectors[i].data(), g_dim, nullptr);
            GRD_SqlStep(stmt);
            GRD_SqlReset(stmt);
        }
        GRD_SqlFinalize(stmt);

        UtExactQuery(vectors, dataId, numSamples);

        vectors.clear();
        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            float randomNumber = *(float *)DT_SetGetFloat(&g_Element[g_count++], 0);
            for (uint16_t j = 0; j < g_dim; j++) {
                float tmpVal = base * 10 + ((randomNumber / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> newVector = vectors[i];
            UtUpdateVectorById(newVector, dataId[i]);

            int resultsId[1] = {0};
            UtSimQueryVector(newVector, 1, resultsId);
        }
        UtExactQuery(vectors, dataId, numSamples);
    }
    DT_FUZZ_END()
}

TEST_F(FuzzGrdSqlVector, FuzzGrdSqlVectorUpdate004) {
    char func[] = "FuzzGrdSqlVectorUpdate004";
    DT_FUZZ_START_CMD(func) {
        g_count = 0;
        uint32_t numSamples = DISKANN_NUMSAMPLES_50;
        uint32_t numPerClass = 10;
        std::vector<std::vector<float>> vectors;
        int dataId[DISKANN_NUMSAMPLES_50] = {0};

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            float randomNumber = *(float *)DT_SetGetFloat(&g_Element[g_count++], 0);
            for (uint16_t j = 0; j < g_dim; j++) {
                float tmpVal = base * 10 + ((randomNumber / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }
        UtBuildIndex(true);

        const char *unused = nullptr;
        GRD_StmtT *stmt = nullptr;
        const char *insertSql = "INSERT INTO t1 VALUES(?, ?);";
        GRD_SqlPrepare(g_db, insertSql, strlen(insertSql) + 1, &stmt, &unused);
        for (uint32_t i = 0; i < numSamples; i++) {
            GRD_SqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, i);
            GRD_SqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vectors[i].data(), g_dim, nullptr);
            GRD_SqlStep(stmt);
            GRD_SqlReset(stmt);
        }
        GRD_SqlFinalize(stmt);

        UtExactQuery(vectors, dataId, numSamples);

        vectors.clear();
        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            for (uint16_t j = 0; j < g_dim; j++) {
                float regularData = (float)i + (float)j * 0.1;
                float tmpVal = base * 10 + ((regularData / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> newVector = vectors[i];
            UtUpdateVectorById(newVector, dataId[i]);

            int resultsId[1] = {0};
            UtSimQueryVector(newVector, 1, resultsId);
        }
        UtExactQuery(vectors, dataId, numSamples);
    }
    DT_FUZZ_END()
}

TEST_F(FuzzGrdSqlVector, FuzzGrdSqlVectorUpdate005) {
    char func[] = "FuzzGrdSqlVectorUpdate005";
    DT_FUZZ_START_CMD(func) {
        g_count = 0;
        uint32_t numSamples = DISKANN_NUMSAMPLES_50;
        uint32_t numPerClass = 10;
        std::vector<std::vector<float>> vectors;
        int dataId[DISKANN_NUMSAMPLES_50] = {0};

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            for (uint16_t j = 0; j < g_dim; j++) {
                float regularData = (float)i + (float)j * 0.1;
                float tmpVal = base * 10 + ((regularData / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }
        UtBuildIndex(true);

        const char *unused = nullptr;
        GRD_StmtT *stmt = nullptr;
        const char *insertSql = "INSERT INTO t1 VALUES(?, ?);";
        GRD_SqlPrepare(g_db, insertSql, strlen(insertSql) + 1, &stmt, &unused);
        uint32_t bindData = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
        for (uint32_t i = 0; i < numSamples; i++) {
            GRD_SqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, i + bindData);
            GRD_SqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vectors[i].data(), g_dim, nullptr);
            GRD_SqlStep(stmt);
            GRD_SqlReset(stmt);
        }
        GRD_SqlFinalize(stmt);

        UtExactQuery(vectors, dataId, numSamples);

        vectors.clear();
        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            for (uint16_t j = 0; j < g_dim; j++) {
                float regularData = (float)i + (float)j * 0.1;
                float tmpVal = base * 10 + ((regularData / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> newVector = vectors[i];
            UtUpdateVectorById(newVector, dataId[i]);

            int resultsId[1] = {0};
            UtSimQueryVector(newVector, 1, resultsId);
        }
        UtExactQuery(vectors, dataId, numSamples);
    }
    DT_FUZZ_END()
}

TEST_F(FuzzGrdSqlVector, FuzzGrdSqlVectorUpdate006) {
    char func[] = "FuzzGrdSqlVectorUpdate006";
    DT_FUZZ_START_CMD(func) {
        g_count = 0;
        uint32_t numSamples = DISKANN_NUMSAMPLES_50;
        uint32_t numPerClass = 10;
        std::vector<std::vector<float>> vectors;
        int dataId[DISKANN_NUMSAMPLES_50] = {0};

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            for (uint16_t j = 0; j < g_dim; j++) {
                float regularData = (float)i + (float)j * 0.1;
                float tmpVal = base * 10 + ((regularData / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }
        UtBuildIndex(true);

        const char *unused = nullptr;
        GRD_StmtT *stmt = nullptr;
        const char *insertSql = "INSERT INTO t1 VALUES(?, ?);";
        GRD_SqlPrepare(g_db, insertSql, strlen(insertSql) + 1, &stmt, &unused);
        for (uint32_t i = 0; i < numSamples; i++) {
            GRD_SqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, i);
            GRD_SqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vectors[i].data(), g_dim, nullptr);
            GRD_SqlStep(stmt);
            GRD_SqlReset(stmt);
        }
        GRD_SqlFinalize(stmt);

        UtExactQuery(vectors, dataId, numSamples);

        vectors.clear();
        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> tmpVector;
            int base = i / numPerClass;
            float randomNumber = *(float *)DT_SetGetFloat(&g_Element[g_count++], 0);
            for (uint16_t j = 0; j < g_dim; j++) {
                float tmpVal = base * 10 + ((randomNumber / FLOAT_MAX));
                tmpVal = RoundToPrecision(tmpVal, 2);
                tmpVector.push_back(tmpVal);
            }
            vectors.push_back(tmpVector);
            dataId[i] = (int)i;
        }

        for (uint32_t i = 0; i < numSamples; i++) {
            std::vector<float> newVector = vectors[i];
            UtUpdateVectorById(newVector, dataId[i]);

            int resultsId[1] = {0};
            UtSimQueryVector(newVector, 1, resultsId);
        }
        UtExactQuery(vectors, dataId, numSamples);
    }
    DT_FUZZ_END()
}

// set 语法
TEST_F(FuzzGrdSqlVector, FuzzGrdSqlVectorSetParameter001)
{
    g_dtRunCount = 1;
    char func[] = "FuzzGrdSqlVectorSetParameter001";
    DT_FUZZ_START_CMD(func) {
        g_count = 0;
        StParameterTestCtxT ctx = {0};
        ctx.dim = 3;
        ctx.insertNum = 3;
        ctx.expectRetForVectorIndex = GRD_OK;
        ctx.expectRetForSetParam = GRD_OK;
        ctx.indexBeforeInsert = true;
        ctx.indexAfterInsert = true;
        ctx.skipReOpen = true;
        ctx.parameterList = "DoNothing";
        ctx.errMsg = "[SetParameter_001], ";

        std::vector<std::vector<std::string>> parameterGroups = {
            {
                "gsivfflat_probes = 1", "GSIVFFLAT_probes = 100", "gsivfflat_PROBES = 32768",
                "diskann_probe_ncandidates = 100"  // 表中只有 IVFFLAT 索引时，不报错也不生效
            },
            {
                "DISKANN_probe_ncandidates = 1", "diskann_PROBE_ncandidates = 100", "diskann_probe_NCANDIDATES = 32768",
                "gsivfflat_probes = 100"  // 表中只有 DISKANN 索引时，不报错也不生效
            },
            {"diskann_probe_ncandidates = 100", "gsivfflat_probes = 100"}};

        // 遍历每组参数并执行测试
        for (const auto &setParameterList : parameterGroups) {
            ctx.indexBeforeIsDiskAnn = (bool)*(uint32_t *)DT_SetGetNumberRange(&g_Element[g_count++], 0, 0, 1);
            ctx.indexAfterIsDiskAnn = (bool)*(uint32_t *)DT_SetGetNumberRange(&g_Element[g_count++], 0, 0, 1);
            for (auto parameter : setParameterList) {
                ctx.setParameterList = parameter;
                FuzzGrdParameterTestBasic(&ctx);
            }
        }
    }
    DT_FUZZ_END()
}

// 优化规则 ApplyRuleImplMixedVectorIndexScan
TEST_F(FuzzGrdSqlVector, DISABLED_FuzzGrdSqlVectorSelectOrderByWhere001)
{
    char func[] = "FuzzGrdSqlVectorSetParameter001";
    DT_FUZZ_START_CMD(func) {
        const char *createTableSql = "CREATE TABLE t0(id int primary key, alter_prop int, repr floatvector(4));";
        FuzzGrdSqlTestTool::FuzzExecuteSingleSql(g_db, createTableSql, GRD_OK, GRD_OK);

        const char *insertDataSql = R"(
                             INSERT INTO t0 VALUES(0, 0, '[1, 2, 3, 4]'),
                             (1, 1, '[2.0, 3.0, 4.0, 5.0]'),
                             (2, 2, '[+3.0, -4., 5, .6]'),
                             (3, 3, '[2.0, 3.0, 4.0, 5.0]');
                             )";
        FuzzGrdSqlTestTool::FuzzExecuteSingleSql(g_db, insertDataSql, GRD_OK, GRD_OK);

        const char *updateSql = "DELETE FROM t0 WHERE repr <=> '[2, 3, 4, 5]' < 0.0001;";
        FuzzGrdSqlTestTool::FuzzExecuteSingleSql(g_db, updateSql, GRD_OK, GRD_OK);

        int64_t num1 = *(int64_t *)DT_SetGetS64(&g_Element[g_count++], 0);
        num1 = 1;
        char selectSql[256] = {};
        snprintf(selectSql, sizeof(selectSql),
            "SELECT repr FROM t0 WHERE repr <-> '[2, 3, 4, 5]' > 1.0 ORDER BY repr <=> '[1, 2, 3, 4]' LIMIT %lld;",
            static_cast<long long>(num1));
        FuzzGrdSqlTestTool::FuzzExecuteSingleSqlWithoutCheck(g_db, selectSql);

        const char *dropSql = "DROP TABLE t0;";
        FuzzGrdSqlTestTool::FuzzExecuteSingleSql(g_db, dropSql, GRD_OK, GRD_OK);
    }
    DT_FUZZ_END()
}
