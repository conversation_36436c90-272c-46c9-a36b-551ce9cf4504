#include "gtest/gtest.h"
#include "secodeFuzz.h"

#include <string>
#include <vector>

#include "de_equip_info.h"
#include "grd_array_api.h"
#include "grd_db_api.h"
#include "grd_doc_api.h"
#include "grd_error.h"
#include "grd_map_api.h"
#include "grd_sync_api.h"
#include "grd_text_api.h"
#include "grd_xml_element_api.h"
#include "grd_xml_fragment_api.h"
#include "sync_common.h"
#include "store_limits.h"

using namespace SharedObjTestTool;

namespace {
const uint32_t MAX_STRING_LEN = 64;
uint32_t g_count = 0;

void RegistryAllSyncInfo(EquipIdE equip, GRD_DB *db, GRD_ThreadPoolT *threadPool, GRD_ICloudDBT *cloudDB)
{
    SyncCommon::SetEquipId(equip);
    GRD_RegistryThreadPool(db, threadPool);
    GRD_RegistryCloudDB(db, cloudDB);
    SyncCommon::SetEquipId(EquipA);
}
}  // namespace

class FuzzSyncTest : public testing::Test {
protected:
    void SetUp() final
    {
        DT_Enable_Leak_Check(0, 0);
        syncCfg_ = new SyncCommon();
        textName_ = "0_text";
        arrayName_ = "1_text";
        mapName_ = "2_text";
        fragmentName_ = "5_text";

        // init first db
        GrdCloudDb::SetUpTestCase();
        syncCfg_->RemoveADir();
        syncCfg_->MakeADir();
        GRD_DBOpen(syncCfg_->firstDbFile_.c_str(), syncCfg_->configStr_.c_str(), GRD_DB_OPEN_CREATE, &syncCfg_->db1_);
        GRD_SetEquipId(syncCfg_->db1_, "A");
        GRD_RegisterEquipId(syncCfg_->db1_, SyncCommon::EquipIdGetFunc);

        // init second db
        syncCfg_->RemoveBDir();
        syncCfg_->MakeBDir();
        GRD_DBOpen(syncCfg_->secondDbFile_.c_str(), syncCfg_->configStr_.c_str(), GRD_DB_OPEN_CREATE, &syncCfg_->db2_);
        GRD_SetEquipId(syncCfg_->db2_, "B");
    }

    void TearDown() final
    {
        GrdCloudDb::TearDownTestCase();

        // clear first db
        GRD_DBClose(syncCfg_->db1_, GRD_DB_CLOSE);
        syncCfg_->RemoveADir();
        syncCfg_->db1_ = nullptr;

        // clear second db
        GRD_DBClose(syncCfg_->db2_, GRD_DB_CLOSE);
        syncCfg_->RemoveBDir();
        syncCfg_->db2_ = nullptr;

        SyncCommon::SetEquipIdNull();
        delete syncCfg_;
        syncCfg_ = nullptr;
    }

    static void SetUpTestCase()
    {}

    static void TearDownTestCase()
    {}

    void GetEquipIdFuzzTest(GRD_DB *db)
    {
        char *equipId = nullptr;
        GRD_GetEquipId(db, &equipId);
        GRD_DocFree(equipId);
    }

    SyncCommon *syncCfg_ = nullptr;
    std::string textName_;
    std::string arrayName_;
    std::string mapName_;
    std::string fragmentName_;
};

TEST_F(FuzzSyncTest, FuzzSyncRegistryCloudDB001){
    DT_FUZZ_START_CMD(const_cast<char *>("FuzzSyncRegistryCloudDB001()")){g_count = 0;
std::vector<GRD_ICloudDBT *> cloudDbs;
cloudDbs.push_back(&SyncCommon::cloudDB_);

GRD_ICloudDBT cloudDb1 = {
    .cloudDB = nullptr,
    .assetLoader = &SyncCommon::assetLoader_,
    .batchInsert = SyncCommon::BatchInsertTestWrap,
    .query = SyncCommon::QueryTestWrap,
    .sendAwarenessData = SyncCommon::SendAwarenessDataTest,
    .lock = SyncCommon::LockTest,
    .unLock = SyncCommon::UnLockTest,
    .heartBeat = SyncCommon::HeartBeatTest,
    .close = SyncCommon::CloseTest,
};
cloudDbs.push_back(&cloudDb1);

GRD_ICloudDBT cloudDb2 = {
    .cloudDB = &SyncCommon::cloud_,
    .assetLoader = nullptr,
    .batchInsert = SyncCommon::BatchInsertTestWrap,
    .query = SyncCommon::QueryTestWrap,
    .sendAwarenessData = SyncCommon::SendAwarenessDataTest,
    .lock = SyncCommon::LockTest,
    .unLock = SyncCommon::UnLockTest,
    .heartBeat = SyncCommon::HeartBeatTest,
    .close = SyncCommon::CloseTest,
};
cloudDbs.push_back(&cloudDb2);

GRD_ICloudDBT cloudDb3 = {
    .cloudDB = &SyncCommon::cloud_,
    .assetLoader = &SyncCommon::assetLoader_,
    .batchInsert = nullptr,
    .query = SyncCommon::QueryTestWrap,
    .sendAwarenessData = SyncCommon::SendAwarenessDataTest,
    .lock = SyncCommon::LockTest,
    .unLock = SyncCommon::UnLockTest,
    .heartBeat = SyncCommon::HeartBeatTest,
    .close = SyncCommon::CloseTest,
};
cloudDbs.push_back(&cloudDb3);

GRD_ICloudDBT cloudDb4 = {
    .cloudDB = &SyncCommon::cloud_,
    .assetLoader = &SyncCommon::assetLoader_,
    .batchInsert = SyncCommon::BatchInsertTestWrap,
    .query = nullptr,
    .sendAwarenessData = SyncCommon::SendAwarenessDataTest,
    .lock = SyncCommon::LockTest,
    .unLock = SyncCommon::UnLockTest,
    .heartBeat = SyncCommon::HeartBeatTest,
    .close = SyncCommon::CloseTest,
};
cloudDbs.push_back(&cloudDb4);

GRD_ICloudDBT cloudDb5 = {
    .cloudDB = &SyncCommon::cloud_,
    .assetLoader = &SyncCommon::assetLoader_,
    .batchInsert = SyncCommon::BatchInsertTestWrap,
    .query = SyncCommon::QueryTestWrap,
    .sendAwarenessData = nullptr,
    .lock = SyncCommon::LockTest,
    .unLock = SyncCommon::UnLockTest,
    .heartBeat = SyncCommon::HeartBeatTest,
    .close = SyncCommon::CloseTest,
};
cloudDbs.push_back(&cloudDb5);

GRD_ICloudDBT cloudDb6 = {
    .cloudDB = &SyncCommon::cloud_,
    .assetLoader = &SyncCommon::assetLoader_,
    .batchInsert = SyncCommon::BatchInsertTestWrap,
    .query = SyncCommon::QueryTestWrap,
    .sendAwarenessData = SyncCommon::SendAwarenessDataTest,
    .lock = nullptr,
    .unLock = SyncCommon::UnLockTest,
    .heartBeat = SyncCommon::HeartBeatTest,
    .close = SyncCommon::CloseTest,
};
cloudDbs.push_back(&cloudDb6);

GRD_ICloudDBT cloudDb7 = {
    .cloudDB = &SyncCommon::cloud_,
    .assetLoader = &SyncCommon::assetLoader_,
    .batchInsert = SyncCommon::BatchInsertTestWrap,
    .query = SyncCommon::QueryTestWrap,
    .sendAwarenessData = SyncCommon::SendAwarenessDataTest,
    .lock = SyncCommon::LockTest,
    .unLock = nullptr,
    .heartBeat = SyncCommon::HeartBeatTest,
    .close = SyncCommon::CloseTest,
};
cloudDbs.push_back(&cloudDb7);

GRD_ICloudDBT cloudDb8 = {
    .cloudDB = &SyncCommon::cloud_,
    .assetLoader = &SyncCommon::assetLoader_,
    .batchInsert = SyncCommon::BatchInsertTestWrap,
    .query = SyncCommon::QueryTestWrap,
    .sendAwarenessData = SyncCommon::SendAwarenessDataTest,
    .lock = SyncCommon::LockTest,
    .unLock = SyncCommon::UnLockTest,
    .heartBeat = nullptr,
    .close = SyncCommon::CloseTest,
};
cloudDbs.push_back(&cloudDb8);

GRD_ICloudDBT cloudDb9 = {
    .cloudDB = &SyncCommon::cloud_,
    .assetLoader = &SyncCommon::assetLoader_,
    .batchInsert = SyncCommon::BatchInsertTestWrap,
    .query = SyncCommon::QueryTestWrap,
    .sendAwarenessData = SyncCommon::SendAwarenessDataTest,
    .lock = SyncCommon::LockTest,
    .unLock = SyncCommon::UnLockTest,
    .heartBeat = SyncCommon::HeartBeatTest,
    .close = nullptr,
};
cloudDbs.push_back(&cloudDb9);

GRD_ICloudDBT cloudDb10 = {
    .cloudDB = nullptr,
    .assetLoader = nullptr,
    .batchInsert = nullptr,
    .query = nullptr,
    .sendAwarenessData = nullptr,
    .lock = nullptr,
    .unLock = nullptr,
    .heartBeat = nullptr,
    .close = nullptr,
};
cloudDbs.push_back(&cloudDb10);

uint32_t funcIndex = (*(reinterpret_cast<uint32_t *>(DT_SetGetNumberRange(&g_Element[g_count++], 0, 0, 10))));
GRD_RegistryCloudDB(syncCfg_->db1_, cloudDbs[funcIndex]);
}
DT_FUZZ_END()
}

TEST_F(FuzzSyncTest, FuzzSyncRegistryThreadPool001){
    DT_FUZZ_START_CMD(const_cast<char *>("FuzzSyncRegistryThreadPool001()")){g_count = 0;
std::vector<GRD_ThreadPoolT *> threadPools;
threadPools.push_back(&SyncCommon::threadPool_);

GRD_ThreadPoolT threadPool1 = {nullptr};
threadPools.push_back(&threadPool1);

uint32_t funcIndex = (*(reinterpret_cast<uint32_t *>(DT_SetGetNumberRange(&g_Element[g_count++], 0, 0, 1))));
GRD_RegistryThreadPool(syncCfg_->db1_, threadPools[funcIndex]);
}
DT_FUZZ_END()
}

TEST_F(FuzzSyncTest, FuzzSyncRegisterEquipId001)
{
    DT_FUZZ_START_CMD(const_cast<char *>("FuzzSyncRegisterEquipId001()"))
    {
        g_count = 0;
        std::vector<GrdEquipIdGetFuncT> getEquipIds;
        getEquipIds.push_back(SyncCommon::EquipIdGetFunc);

        GrdEquipIdGetFuncT getEquipId1 = nullptr;
        getEquipIds.push_back(getEquipId1);

        uint32_t funcIndex = (*(reinterpret_cast<uint32_t *>(DT_SetGetNumberRange(&g_Element[g_count++], 0, 0, 1))));
        GRD_RegisterEquipId(syncCfg_->db1_, getEquipIds[funcIndex]);
    }
    DT_FUZZ_END()
}

namespace {
std::string g_equipId;

const char *GetEquipIdInner()
{
    return g_equipId.c_str();
}
}  // namespace

TEST_F(FuzzSyncTest, FuzzSyncRegisterEquipId002){
    DT_FUZZ_START_CMD(const_cast<char *>("FuzzSyncRegisterEquipId002()")){g_count = 0;
g_equipId =
    std::string(DT_SetGetString(&g_Element[g_count++], strlen("abc") + 1, MAX_STRING_LEN, const_cast<char *>("abc")));

GRD_RegisterEquipId(syncCfg_->db1_, GetEquipIdInner);
GetEquipIdFuzzTest(syncCfg_->db1_);
}
DT_FUZZ_END()
}

TEST_F(FuzzSyncTest, FuzzSyncText001){DT_FUZZ_START_CMD(const_cast<char *>("FuzzSyncText001()")){g_count = 0;
RegistryAllSyncInfo(EquipA, syncCfg_->db1_, &SyncCommon::threadPool_, &SyncCommon::cloudDB_);

GRD_XmlOpPositionT addr = {.tableName = textName_.c_str(), .elementId = nullptr};
for (int i = 0; i < 10; ++i) {
    GRD_TextInsert(syncCfg_->db1_, &addr, i, "text", nullptr);
}

GRD_SyncConfigT syncCfg = {
    .mode = (GRD_SyncModeE)(*(reinterpret_cast<uint32_t *>(DT_SetGetNumberRange(&g_Element[g_count++], 0, 0, 4)))),
    .equipIds = SyncCommon::CloudEquipIdGet(),
    .size = 1u,
    .callbackFunc = SyncCommon::SyncTaskCallbackFuncTest,
    .timeout = 1u,
};
SyncCommon::Sync(syncCfg_->db1_, &syncCfg);
}
DT_FUZZ_END()
}

TEST_F(FuzzSyncTest, FuzzSyncText002){DT_FUZZ_START_CMD(const_cast<char *>("FuzzSyncText002()")){g_count = 0;
RegistryAllSyncInfo(EquipA, syncCfg_->db1_, &SyncCommon::threadPool_, &SyncCommon::cloudDB_);
RegistryAllSyncInfo(EquipB, syncCfg_->db2_, &SyncCommon::threadPool_, &SyncCommon::cloudDB_);

GRD_XmlOpPositionT addr = {.tableName = textName_.c_str(), .elementId = nullptr};
std::string text(DT_SetGetString(&g_Element[g_count++], strlen("abc") + 1, MAX_STRING_LEN, const_cast<char *>("abc")));
for (int i = 0; i < 10; ++i) {
    GRD_TextInsert(syncCfg_->db1_, &addr, i, text.c_str(), nullptr);
}

SyncCommon::EquipSync(EquipA, syncCfg_->db1_, true);
SyncCommon::EquipSync(EquipB, syncCfg_->db2_, false);

GRD_TextDelete(syncCfg_->db2_, &addr, 1, 3);

std::string formatValue(
    DT_SetGetString(&g_Element[g_count++], strlen("abc") + 1, MAX_STRING_LEN, const_cast<char *>("abc")));
std::string format = "{\"attr\": \"" + formatValue + "\"}";
GRD_TextAssignFormats(syncCfg_->db2_, &addr, 2, 4, format.c_str());

std::string formatKey(
    DT_SetGetString(&g_Element[g_count++], strlen("abc") + 1, MAX_STRING_LEN, const_cast<char *>("abc")));
format = "{\"" + formatKey + "\": true}";
GRD_TextAssignFormats(syncCfg_->db2_, &addr, 3, 5, format.c_str());

SyncCommon::EquipSync(EquipB, syncCfg_->db2_, true);
SyncCommon::EquipSync(EquipA, syncCfg_->db1_, false);
}
DT_FUZZ_END()
}

TEST_F(FuzzSyncTest, FuzzSyncFragment001){DT_FUZZ_START_CMD(const_cast<char *>("FuzzSyncFragment001()")){g_count = 0;
RegistryAllSyncInfo(EquipA, syncCfg_->db1_, &SyncCommon::threadPool_, &SyncCommon::cloudDB_);

GRD_XmlOpPositionT addr = {.tableName = fragmentName_.c_str(), .elementId = nullptr};
GRD_ElementIdT *elementId = nullptr;
GRD_DocNodeInfoT nodeXmlElement = {
    .type = GRD_XML_ELEMENT_TYPE,
    .content = "paragraph",
};
GRD_XmlFragmentInsert(syncCfg_->db1_, &addr, 0, &nodeXmlElement, &elementId);

GRD_SyncConfigT syncCfg = {
    .mode = (GRD_SyncModeE)(*(reinterpret_cast<uint32_t *>(DT_SetGetNumberRange(&g_Element[g_count++], 0, 0, 4)))),
    .equipIds = SyncCommon::CloudEquipIdGet(),
    .size = 1u,
    .callbackFunc = SyncCommon::SyncTaskCallbackFuncTest,
    .timeout = 1u,
};
SyncCommon::Sync(syncCfg_->db1_, &syncCfg);
}
DT_FUZZ_END()
}

TEST_F(FuzzSyncTest, FuzzSyncFragment002){DT_FUZZ_START_CMD(const_cast<char *>("FuzzSyncFragment002()")){g_count = 0;
RegistryAllSyncInfo(EquipA, syncCfg_->db1_, &SyncCommon::threadPool_, &SyncCommon::cloudDB_);
RegistryAllSyncInfo(EquipB, syncCfg_->db2_, &SyncCommon::threadPool_, &SyncCommon::cloudDB_);

GRD_XmlOpPositionT addr = {.tableName = fragmentName_.c_str(), .elementId = nullptr};
GRD_ElementIdT *elementId1 = nullptr;
GRD_DocNodeInfoT nodeXmlElement = {
    .type = GRD_XML_ELEMENT_TYPE,
    .content = DT_SetGetString(&g_Element[g_count++], strlen("abc") + 1, MAX_STRING_LEN, const_cast<char *>("abc")),
};
GRD_XmlFragmentInsert(syncCfg_->db1_, &addr, 0, &nodeXmlElement, &elementId1);

GRD_DocNodeInfoT nodeXmlText1 = {
    .type = GRD_XML_TEXT_TYPE,
    .content = nullptr,
};
GRD_ElementIdT *elementId2 = nullptr;
GRD_XmlFragmentInsert(syncCfg_->db1_, &addr, 0, &nodeXmlText1, &elementId2);

GRD_XmlOpPositionT elemAddr = {.tableName = fragmentName_.c_str(), .elementId = elementId1};
GRD_DocNodeInfoT nodeXmlText2 = {
    .type = GRD_XML_TEXT_TYPE,
    .content = nullptr,
};
GRD_ElementIdT *elementId3 = nullptr;
GRD_XmlFragmentInsert(syncCfg_->db1_, &elemAddr, 0, &nodeXmlText2, &elementId3);

std::string attrKey(
    DT_SetGetString(&g_Element[g_count++], strlen("abc") + 1, MAX_STRING_LEN, const_cast<char *>("abc")));
std::string attrValue(
    DT_SetGetString(&g_Element[g_count++], strlen("abc") + 1, MAX_STRING_LEN, const_cast<char *>("abc")));
GRD_XmlElementSetAttribute(syncCfg_->db1_, &elemAddr, "key", attrValue.c_str(), 0);
GRD_XmlElementSetAttribute(syncCfg_->db1_, &elemAddr, attrKey.c_str(), "value", 0);

GRD_XmlOpPositionT textAddr = {.tableName = fragmentName_.c_str(), .elementId = elementId3};
std::string text(DT_SetGetString(&g_Element[g_count++], strlen("abc") + 1, MAX_STRING_LEN, const_cast<char *>("abc")));
GRD_TextInsert(syncCfg_->db1_, &textAddr, 0, text.c_str(), nullptr);

SyncCommon::EquipSync(EquipA, syncCfg_->db1_, true);
SyncCommon::EquipSync(EquipB, syncCfg_->db2_, false);

GRD_TextDelete(syncCfg_->db2_, &textAddr, 1, 3);

std::string formatValue(
    DT_SetGetString(&g_Element[g_count++], strlen("abc") + 1, MAX_STRING_LEN, const_cast<char *>("abc")));
std::string format = "{\"attr\": \"" + formatValue + "\"}";
GRD_TextAssignFormats(syncCfg_->db2_, &textAddr, 2, 4, format.c_str());

std::string formatKey(
    DT_SetGetString(&g_Element[g_count++], strlen("abc") + 1, MAX_STRING_LEN, const_cast<char *>("abc")));
format = "{\"" + formatKey + "\": true}";
GRD_TextAssignFormats(syncCfg_->db2_, &textAddr, 3, 5, format.c_str());

GRD_XmlElementRemoveAttribute(syncCfg_->db2_, &elemAddr, attrKey.c_str());

GRD_XmlFragmentDelete(syncCfg_->db2_, addr.tableName, addr.elementId, 0, 1);

SyncCommon::EquipSync(EquipB, syncCfg_->db2_, true);
SyncCommon::EquipSync(EquipA, syncCfg_->db1_, false);

GRD_XmlFreeElementId(elementId1);
GRD_XmlFreeElementId(elementId2);
GRD_XmlFreeElementId(elementId3);
}
DT_FUZZ_END()
}

TEST_F(FuzzSyncTest, FuzzSyncMap001){DT_FUZZ_START_CMD(const_cast<char *>("FuzzSyncMap001()")){g_count = 0;
RegistryAllSyncInfo(EquipA, syncCfg_->db1_, &SyncCommon::threadPool_, &SyncCommon::cloudDB_);

std::string value = "{\"type\": 57, \"value\": \"1234\"}";
GRD_MapSet(syncCfg_->db1_, mapName_.c_str(), nullptr, "key", value.c_str());

GRD_SyncConfigT syncCfg = {
    .mode = (GRD_SyncModeE)(*(reinterpret_cast<uint32_t *>(DT_SetGetNumberRange(&g_Element[g_count++], 0, 0, 4)))),
    .equipIds = SyncCommon::CloudEquipIdGet(),
    .size = 1u,
    .callbackFunc = SyncCommon::SyncTaskCallbackFuncTest,
    .timeout = 1u,
};
SyncCommon::Sync(syncCfg_->db1_, &syncCfg);
}
DT_FUZZ_END()
}

TEST_F(FuzzSyncTest, FuzzSyncMap002){DT_FUZZ_START_CMD(const_cast<char *>("FuzzSyncMap002()")){g_count = 0;
RegistryAllSyncInfo(EquipA, syncCfg_->db1_, &SyncCommon::threadPool_, &SyncCommon::cloudDB_);
RegistryAllSyncInfo(EquipB, syncCfg_->db2_, &SyncCommon::threadPool_, &SyncCommon::cloudDB_);

for (uint32_t i = 0; i < 10; ++i) {
    std::string key = "key" + std::to_string(i + 1);
    std::string value = "";
    if (i % 5 == 0) {
        value = "{\"type\": 57, \"value\": \"1234\"}";
    } else {
        value = "{\"type\": " + std::to_string(i % 5) + "}";
    }
    GRD_MapSet(syncCfg_->db1_, mapName_.c_str(), nullptr, key.c_str(), value.c_str());
}

SyncCommon::EquipSync(EquipA, syncCfg_->db1_, true);
SyncCommon::EquipSync(EquipB, syncCfg_->db2_, false);

GRD_MapDelete(syncCfg_->db2_, mapName_.c_str(), nullptr, "key1");
GRD_MapDelete(syncCfg_->db2_, mapName_.c_str(), nullptr, "key5");
GRD_MapDelete(syncCfg_->db2_, mapName_.c_str(), nullptr, "key10");

SyncCommon::EquipSync(EquipB, syncCfg_->db2_, true);
SyncCommon::EquipSync(EquipA, syncCfg_->db1_, false);
}
DT_FUZZ_END()
}

TEST_F(FuzzSyncTest, FuzzSyncArray001){DT_FUZZ_START_CMD(const_cast<char *>("FuzzSyncArray001()")){g_count = 0;
RegistryAllSyncInfo(EquipA, syncCfg_->db1_, &SyncCommon::threadPool_, &SyncCommon::cloudDB_);

GRD_XmlOpPositionT addr = {.tableName = arrayName_.c_str(), .elementId = nullptr};
std::string valueStr = "[{\"type\": 57, \"value\": \"1234\"}]";
char *value = nullptr;
GRD_ArrayInsert(syncCfg_->db1_, &addr, 0, valueStr.c_str(), &value);
GRD_DocFree(value);

GRD_SyncConfigT syncCfg = {
    .mode = (GRD_SyncModeE)(*(reinterpret_cast<uint32_t *>(DT_SetGetNumberRange(&g_Element[g_count++], 0, 0, 4)))),
    .equipIds = SyncCommon::CloudEquipIdGet(),
    .size = 1u,
    .callbackFunc = SyncCommon::SyncTaskCallbackFuncTest,
    .timeout = 1u,
};
SyncCommon::Sync(syncCfg_->db1_, &syncCfg);
}
DT_FUZZ_END()
}

TEST_F(FuzzSyncTest, FuzzSyncArray002)
{
    DT_FUZZ_START_CMD(const_cast<char *>("FuzzSyncArray002()"))
    {
        g_count = 0;
        RegistryAllSyncInfo(EquipA, syncCfg_->db1_, &SyncCommon::threadPool_, &SyncCommon::cloudDB_);
        RegistryAllSyncInfo(EquipB, syncCfg_->db2_, &SyncCommon::threadPool_, &SyncCommon::cloudDB_);

        GRD_XmlOpPositionT addr = {.tableName = arrayName_.c_str(), .elementId = nullptr};
        for (uint32_t i = 0; i < 10; ++i) {
            std::string value = "";
            if (i % 5 == 0) {
                value = "[{\"type\": 57, \"value\": \"1234\"}]";
            } else {
                value = "[{\"type\": " + std::to_string(i % 5) + "}]";
            }
            char *outputData = nullptr;
            GRD_ArrayInsert(syncCfg_->db1_, &addr, 0, value.c_str(), &outputData);
            GRD_DocFree(outputData);
        }

        SyncCommon::EquipSync(EquipA, syncCfg_->db1_, true);
        SyncCommon::EquipSync(EquipB, syncCfg_->db2_, false);

        GRD_ArrayDelete(syncCfg_->db2_, &addr, 2, 4);

        SyncCommon::EquipSync(EquipB, syncCfg_->db2_, true);
        SyncCommon::EquipSync(EquipA, syncCfg_->db1_, false);
    }
    DT_FUZZ_END()
}
