#include "fuzz_client.h"
#include "schema.h"
using std::vector;
static uint32_t g_count = 0;

// GmcBatchOptionSetExecOrder
// GmcBatchOptionSetErrCtrl
// GmcBatchOptionSetBufRecycleSize
// GmcBatchOptionSetBufLimitSize
// GmcBatchPrepare
// GmcBatchReset
// GmcBatchDestroy
Status FuzzGmcBatchOperation(GmcConnT *conn, GmcStmtT *stmt)
{
    g_count = 0;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        printf("batch Option Init filed");
        return ret;
    }
    (void)GmcBatchOptionSetExecOrder(&batchOption, *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0));
    (void)GmcBatchOptionSetBufRecycleSize(&batchOption, *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0));
    (void)GmcBatchOptionSetBufLimitSize(&batchOption, *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0));
    GmcBatchT *batch = NULL;
    (void)GmcBatchPrepare(conn, &batchOption, &batch);
    (void)GmcBatchReset(batch);
    (void)GmcBatchDestroy(batch);
    return GMERR_OK;
}

// GmcBatchAddDDL
// GmcBatchExecute
// GmcBatchDestroy
// GmcBatchDeparseRet
Status FuzzGmcBatchAddDDL(GmcConnT *conn, GmcStmtT *stmt)
{
    g_count = 0;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    uint32_t totalNum, successNum, resIdNum;
    Status ret = GmcBatchPrepare(conn, NULL, &batch);
    for (int i = 0; i < 5; i++) {
        int32_t NumberEnum = *(int32_t *)DT_SetGetNumberEnum(
            &g_Element[g_count++], 1, op_type_table, sizeof(op_type_table) / sizeof(op_type_table[0]));
        (void)GmcBatchAddDDL(batch, NumberEnum,
            DT_SetGetString(&g_Element[g_count++], strlen("fuzz_labelName") + 1, MAX_STRING_LEN, "fuzz_labelName"),
            DT_SetGetString(&g_Element[g_count++], strlen("fuzz_labelJson") + 1, MAX_STRING_LEN, "fuzz_labelJson"),
            DT_SetGetString(&g_Element[g_count++], strlen("fuzz_configJson") + 1, MAX_STRING_LEN, "fuzz_configJson"));
    }
    (void)GmcBatchExecute(batch, &batchRet);
    (void)GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcBatchAddKvDML
// GmcKvInputToStmt
// GmcBatchGetResIdNum
// GmcBatchGetResIdInfo
Status FuzzGmcBatchAddKvDML(GmcConnT *conn, GmcStmtT *stmt)
{
    g_count = 0;
    const char *vertexName1 =
        DT_SetGetString(&g_Element[g_count++], strlen("vertexLabelTest1") + 1, MAX_STRING_LEN, "vertexLabelTest1");
    const char *vertexName2 =
        DT_SetGetString(&g_Element[g_count++], strlen("vertexLabelTest2") + 1, MAX_STRING_LEN, "vertexLabelTest2");

    (void)GmcKvCreateTable(stmt, "kvTabelName", g_configJson);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    uint32_t totalNum, successNum, resIdNum;
    int ret = GmcBatchPrepare(conn, NULL, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, "kvTabelName");
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 5; i++) {
        int32_t NumberEnum = *(int32_t *)DT_SetGetNumberEnum(
            &g_Element[g_count++], 1, op_type_table, sizeof(op_type_table) / sizeof(op_type_table[0]));
        char *key = DT_SetGetString(&g_Element[g_count++], strlen("fuzz_keyBuf") + 1, MAX_STRING_LEN, "fuzz_keyBuf");
        uint32_t keylLen = strlen(key) + 1;
        uint32_t value1 = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
        ret = GmcKvInputToStmt(stmt, key, keylLen, &value1, sizeof(value1));
        EXPECT_EQ(GMERR_OK, ret);
        (void)GmcBatchAddKvDML(batch, stmt, NumberEnum);
    }
    (void)GmcBatchExecute(batch, &batchRet);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    // Fuzz框架不支持DT_SetGet函数的随机使用，每次Fuzz次数需要固定，否则会导致Fuzz框架无法释放内存
    if (ret == GMERR_OK) {
        ret = GmcBatchGetResIdNum(&batchRet, vertexName1, &resIdNum);
    }
    if (ret == GMERR_OK) {
        uint64_t resIdBuf[resIdNum];
        (void)GmcBatchGetResIdInfo(&batchRet, vertexName2, resIdBuf, &resIdNum);
    }
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, "kvTabelName");
    EXPECT_EQ(GMERR_OK, ret);
    return GMERR_OK;
}

// GmcBatchGetResIdNum
// GmcBatchGetResIdInfo
Status FuzzGmcBatchGetResIdNum(GmcConnT *conn, GmcStmtT *stmt)
{
    g_count = 0;

    Status ret = GmcCreateVertexLabel(stmt, g_vertexLabelTest1Schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "vertexLabelTest1", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    int insertNum = 5;
    for (int i = 0; i < insertNum; i++) {
        ret = GmcSetVertexProperty(stmt, "c0", GMC_DATATYPE_UINT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t totalNum, successNum, bufLen;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchGetResIdNum(&batchRet,
        DT_SetGetString(&g_Element[g_count++], sizeof("vertexLabelTest1"), MAX_STRING_LEN, "vertexLabelTest1"),
        &bufLen);
    uint64_t batchResId[insertNum];
    bufLen = *(uint32_t *)DT_SetGetU32(&g_Element[g_count++], 0);
    GmcBatchGetResIdInfo(&batchRet,
        DT_SetGetString(&g_Element[g_count++], sizeof("vertexLabelTest1"), MAX_STRING_LEN, "vertexLabelTest1"),
        batchResId, &bufLen);

    ret = GmcDropVertexLabel(stmt, "vertexLabelTest1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
}

class FuzzClientBatch : public FuzzClientTest {
protected:
    virtual void SetUp()
    {
        DT_Enable_Leak_Check(0, 0);
        int32_t ret = CreateSyncConnAndStmt(&conn, &stmt);
        int reconnTimes = 0;
        while (ret != GMERR_OK && reconnTimes <= CLIENT_MAX_RECONN_TIMES) {
            usleep(1000);
            ret = CreateSyncConnAndStmt(&conn, &stmt);
            reconnTimes++;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }
    virtual void TearDown()
    {
        DestroyConnectionAndStmt(conn, stmt);
    }
    virtual void FreeObjects()
    {}
    static void SetUpTestCase()
    {
        FuzzSetUp();
        StartDbServerWithConfig(NULL);
        Status ret = GmcInit();
        ASSERT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        (void)GmcUnInit();
        ShutDownDbServer();
    }

protected:
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
};

TEST_F(FuzzClientBatch, FuzzGmcBatchOperation){
    DT_FUZZ_START_CMD("FuzzBatchOperation"){FuzzGmcBatchOperation(conn, stmt);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientBatch, FuzzGmcBatchAddDDL){DT_FUZZ_START_CMD("FuzzGmcBatchAddDDL"){FuzzGmcBatchAddDDL(conn, stmt);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientBatch, FuzzGmcBatchAddKvDML){
    DT_FUZZ_START_CMD("FuzzGmcBatchAddKvDML"){FuzzGmcBatchAddKvDML(conn, stmt);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientBatch, FuzzGmcBatchGetResIdNum)
{
    DT_FUZZ_START_CMD("FuzzGmcBatchGetResIdNum")
    {
        FuzzGmcBatchGetResIdNum(conn, stmt);
    }
    DT_FUZZ_END()
}
