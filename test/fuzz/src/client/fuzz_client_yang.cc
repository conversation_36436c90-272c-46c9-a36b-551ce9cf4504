#include <chrono>
#include "fuzz_client.h"
#include "schema.h"
#include "gmc_graph.h"
#include "gmc_yang.h"
#include "adpt_sleep.h"
#include "db_mem_context.h"
#include "clt_conn.h"
#include "clt_stmt.h"
#include "clt_yang_common.h"
#include "fuzz_client_async.h"
#include "gmc_internal.h"

// yang特性基于异步来测试，兼容少量同步接口
class FuzzClientYang : public FuzzClientAsync {
protected:
    virtual void SetUp()
    {
        DT_Enable_Leak_Check(0, 0);
        int32_t ret;
        int reconnTimes = 0;
        do {
            usleep(1000);
            ret = CreateSyncConnAndStmt(&conn, &stmt);
            reconnTimes++;
        } while (ret != GMERR_OK && reconnTimes <= CLIENT_MAX_RECONN_TIMES);
        ASSERT_EQ(GMERR_OK, ret);
        do {
            usleep(1000);
            ret = FuzzClientAsync::FuzzCreateAsyncConnAndStmt(&asyncConn, &asyncStmt);
            reconnTimes++;
        } while (ret != GMERR_OK && reconnTimes <= CLIENT_MAX_RECONN_TIMES);
        ASSERT_EQ(GMERR_OK, ret);
    }
    virtual void TearDown()
    {
        DestroyConnectionAndStmt(conn, stmt);
        DestroyConnectionAndStmt(asyncConn, asyncStmt);
    }
    virtual void FreeObjects()
    {}
    static void SetUpTestCase()
    {
        FuzzClientAsync::SetUpTestCase();
    }
    static void TearDownTestCase()
    {
        FuzzClientAsync::TearDownTestCase();
    }

protected:
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcConnT *asyncConn = NULL;
    GmcStmtT *asyncStmt = NULL;
};

const char *g_testRoot = R"({
        "type": "container",
        "name": "root",
        "fields": [
            {
                "name": "ID",
                "type": "uint32",
                "nullable": false
            },
            {
                "name": "PK",
                "type": "uint32",
                "nullable": false
            },
            {
                "name": "comment",
                "type": "string"
            },
            {
                "name": "name",
                "type": "string"
            },
            {
                "name":"root_enum",
                "type":"enum",
                "enumerate_identity":"Enum_1",
                "enumerate": [
                    {
                        "name":"case1",
                        "value":1
                    },
                    {
                        "name":"case2",
                        "value":2
                    },
                    {
                        "name":"case3",
                        "value":3
                    }
                ],
                "default":"case2"
            },
            {
                "type": "container",
                "name": "C1",
                "fields": [
                    {
                        "name": "comment",
                        "type": "string"
                    },
                    {
                        "name": "name",
                        "type": "string"
                    },
                    {
                        "name":"C1_identity",
                        "type":"identity",
                        "enumerate_identity":"identity_1",
                        "enumerate": [
                            {
                                "name":"A",
                                "value":1,
                                "derived-paths": [{"derived-path": "A"}]
                            },
                            {
                                "name":"a",
                                "value":2,
                                "derived-paths": [{"derived-path": "A/a"}]
                            },
                            {
                                "name":"a1",
                                "value":3,
                                "derived-paths": [{"derived-path": "A/a/a1"}]
                            }
                        ],
                        "default":"a"
                    },
                    {
                        "type": "container",
                        "name": "C3",
                        "fields": [
                            {
                                "name": "comment",
                                "type": "string"
                            },
                            {
                                "name": "name",
                                "type": "string"
                            }
                        ]
                    }
                ]
            },
            {
                "type": "container",
                "name": "C2",
                "fields": [
                    {
                        "name": "comment",
                        "type": "string"
                    },
                    {
                        "name": "name",
                        "type": "string"
                    }
                ]
            }
        ],
        "keys": [
            {
                "node": "root",
                "name": "PK",
                "fields": [
                    "ID"
                ],
                "index": {
                    "type": "primary"
                },
                "constraints": {
                    "unique": true
                }
            }
        ]
    })";
const char *g_testList = R"({
        "type": "list",
        "name": "root::L1",
        "fields": [
            {
                "name": "ID",
                "type": "uint32",
                "nullable": false,
                "auto_increment": true
            },
            {
                "name": "PID",
                "type": "uint32",
                "nullable": false
            },
            {
                "name": "name",
                "type": "string"
            },
            {
                "name": "L1Unique",
                "type": "uint32",
                "nullable": false
            },
            {
                "name": "comment",
                "type": "string"
            },
            {
                "type": "container",
                "name": "C4",
                "fields": [
                    {
                        "name": "comment",
                        "type": "string"
                    },
                    {
                        "name": "name",
                        "type": "string"
                    },
                    {
                        "type": "choice",
                        "name": "Cch1",
                        "fields": [
                            {
                                "type": "case",
                                "name": "Cca1",
                                "fields": [
                                    {
                                        "name": "comment",
                                        "type": "string"
                                    },
                                    {
                                        "name": "name",
                                        "type": "string"
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ],
        "keys": [
            {
                "fields": [
                    "PID",
                    "name"
                ],
                "node": "root::L1",
                "name": "PK",
                "index": {
                    "type": "primary"
                },
                "constraints": {
                    "unique": true
                }
            },
            {
                "node": "root::L1",
                "name": "L1.UNIQUE",
                "fields": [
                    "PID",
                    "L1Unique"
                ],
                "index": {
                    "type": "list_localhash"
                },
                "constraints": {
                    "unique": true,
                    "null_check": true
                }
            }
        ]
    })";

const char *g_testRootList = R"([{
        "name":"RootListEdge",
        "source_vertex_label":"root",
        "source_node_path": "/C2",
        "comment": "T0 is root",
        "dest_vertex_label":"root::L1",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {
                    "source_property": "ID",
                    "dest_property": "PID"
                }
            ]
        }
    }])";

const char *g_testTreeNode = R"({
        "type":"container",
        "name":"root",
        "fields":[
            {"name":"ID", "type":"uint32", "nullable":false},
            {"name":"F0", "type":"uint32", "nullable":false},
            {"name":"comment", "type":"string"},
            {
                "type":"container",
                "name":"C1",
                "fields":[
                    {"name":"F0", "type":"uint32"},
                    {"name":"comment", "type":"string"},
                    {
                        "type":"container",
                        "name":"C3",
                        "fields":[
                            {"name":"F0", "type":"uint32"},
                            {"name":"comment", "type":"string"}
                        ]
                    }
                ]
            },
            {
                "type":"container",
                "name":"C12",
                "fields":[
                    {"name":"F0", "type":"uint32"},
                    {"name":"comment", "type":"string"},
                    {
                        "type":"container",
                        "name":"C32",
                        "fields":[
                            {"name":"F0", "type":"uint32"},
                            {"name":"comment", "type":"string"}
                        ]
                    }
                ]
            }
        ],
        "keys":[
            {
                "node":"root",
                "name":"root.PK",
                "fields":["ID"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    })";

const char *g_testChoiceCaseLabel = R"(
    {
        "type":"container",
        "name":"root",
        "fields":[
            {"name":"ID", "type":"uint32", "nullable":false},
            {"name":"F1", "type":"int32", "nullable":false},
            {
                "name":"choice1",
                "type":"choice",
                "fields":[
                    {
                        "name":"case1A",
                        "type":"case",
                        "fields":[
                            {"name":"F1A", "type":"int32"},
                            {
                                "name":"choice2",
                                "type":"choice",
                                "fields":[
                                    {
                                        "name":"case2A",
                                        "type":"case",
                                        "fields":[
                                            {"name":"F2A", "type":"int32"}
                                        ]
                                    },
                                    {
                                        "name":"case2B",
                                        "type":"case",
                                        "fields":[
                                            {"name":"F2B", "type":"int32"}
                                        ]
                                    },
                                    {
                                        "name":"case2C",
                                        "type":"case",
                                        "fields":[
                                            {"name":"F2C", "type":"int32"}
                                        ]
                                    }
                                ]
                            },
                            {
                                "name": "C1",
                                "type": "container",
                                "fields": [
                                    {"name":"FC1", "type":"int32"}
                                ]
                            }
                        ]
                    },
                    {
                        "name":"case1B",
                        "type":"case",
                        "fields":[
                            {"name":"F1B", "type":"int32"},
                            {
                                "name": "C2",
                                "type": "container",
                                "fields": [
                                    {"name":"FC2", "type":"int32"}
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                "name": "C3",
                "type": "container",
                "fields": [
                    {"name":"FC3", "type":"int32"},
                    {
                        "name":"choice3",
                        "type":"choice",
                        "fields":[
                            {
                                "name":"case3A",
                                "type":"case",
                                "fields":[
                                    {"name":"F3A", "type":"int32"},
                                    {
                                        "name": "C4",
                                        "type": "container",
                                        "fields": [
                                            {"name":"FC4", "type":"int32"}
                                        ]
                                    }
                                ]
                            },
                            {
                                "name":"case3B",
                                "type":"case",
                                "fields":[
                                    {"name":"F3B", "type":"int32"}
                                ]
                            }
                        ]
                    }
                ]
            }
        ],
        "keys":[
            {
                "node":"root",
                "name":"root_PK",
                "fields":["ID"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    }
)";

const char *g_testL1 = R"({
        "type":"list",
        "name":"L1",
        "fields":[
            {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
            {"name":"PID", "type":"uint32", "nullable":false},
            {"name":"F0", "type":"uint32", "nullable":false},
            {"name":"comment", "type":"string"}
        ],
        "keys":[
            {
                "node":"L1",
                "name":"L1.PK",
                "fields":["PID", "F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    })";

const char *g_testT0Vertex = R"({
        "type":"container",
        "name":"T0Vertex",
        "fields":[
            {"name":"ID", "type":"uint32", "nullable":false},
            {"name":"F0", "type":"int32", "nullable":false}
        ],
        "keys":[
            {
                "node":"T0",
                "name":"T0.F0",
                "fields":["ID"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    })";

const char *g_testT11Choice = R"({
        "type":"choice",
        "name":"T11Choice",
        "nullable":false,
        "fields":[
            {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
            {"name":"PID", "type":"uint32", "nullable":false}
        ],
        "keys":[
            {
                "node":"T1",
                "name":"T1.PK",
                "fields":["PID"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    })";

const char *g_testT12Choice = R"({
        "type":"choice",
        "name":"T12Choice",
        "nullable":false,
        "fields":[
            {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
            {"name":"PID", "type":"uint32", "nullable":false}
        ],
        "keys":[
            {
                "node":"T1",
                "name":"T1.PK",
                "fields":["PID"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    })";

const char *g_testT21Case = R"({
        "type":"case",
        "name":"T21Case",
        "fields":[
            {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
            {"name":"PID", "type":"uint32", "nullable":false},
            {"name":"F0", "type":"int32", "nullable":false}
        ],
        "keys":[
            {
                "node":"T21",
                "name":"T21.PK",
                "fields":["PID"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    })";

const char *g_testT22Case = R"({
        "type":"case",
        "name":"T22Case",
        "fields":[
            {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
            {"name":"PID", "type":"uint32", "nullable":false},
            {"name":"F0", "type":"int32", "nullable":false}
        ],
        "keys":[
            {
                "node":"T22",
                "name":"T22.PK",
                "fields":["PID"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    })";

const char *g_testT23Case = R"({
        "type":"case",
        "name":"T23Case",
        "fields":[
            {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
            {"name":"PID", "type":"uint32", "nullable":false},
            {"name":"F0", "type":"int32", "nullable":false}
        ],
        "keys":[
            {
                "node":"T23",
                "name":"T23.PK",
                "fields":["PID"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    })";

const char *g_testT0VertexT11ChoiceEdge = R"([{
        "name":"T0VertexT11ChoiceEdge",
        "source_vertex_label":"T0Vertex",
        "comment": "T0 is root",
        "dest_vertex_label":"T11Choice",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {
                    "source_property": "ID",
                    "dest_property": "PID"
                }
            ]
        }
    }])";

const char *g_testT0VertexT12ChoiceEdge = R"([{
        "name":"T0VertexT12ChoiceEdge",
        "source_vertex_label":"T0Vertex",
        "comment": "T0 is root",
        "dest_vertex_label":"T12Choice",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {
                    "source_property": "ID",
                    "dest_property": "PID"
                }
            ]
        }
    }])";

const char *g_testT11ChoiceT21CaseEdge = R"([{
        "name":"T11ChoiceT21CaseEdge",
        "source_vertex_label":"T11Choice",
        "dest_vertex_label":"T21Case",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {
                    "source_property": "ID",
                    "dest_property": "PID"
                }
            ]
        }
    }])";

const char *g_testT11ChoiceT22CaseEdge = R"([{
        "name":"T11ChoiceT22CaseEdge",
        "source_vertex_label":"T11Choice",
        "dest_vertex_label":"T22Case",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {
                    "source_property": "ID",
                    "dest_property": "PID"
                }
            ]
        }
    }])";

const char *g_testT12ChoiceT23CaseEdge = R"([{
        "name":"T12ChoiceT23CaseEdge",
        "source_vertex_label":"T12Choice",
        "dest_vertex_label":"T23Case",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {
                    "source_property": "ID",
                    "dest_property": "PID"
                }
            ]
        }
    }])";

const char *g_cfgGraph = R"({"max_record_count":1000, "auto_increment": 1, "isFastReadUncommitted": 0})";
const char *g_cfgTree =
    R"({"max_record_count":1000, "auto_increment": 1, "isFastReadUncommitted": 0, "yang_model": 1})";
const int g_vertexNum = 7;
const int g_edgeNum = 5;
const uint32_t ST_MAX_LOST_PATH_LEN = 128;

template <Status expectedStatus = GMERR_OK>
static void AsyncOperationCb(void *userData, Status status, const char *errMsg)
{
    if (userData == NULL) {
        ASSERT_EQ(0, 1);
        return;
    }

    EXPECT_EQ(expectedStatus, status) << errMsg;
    uint32_t *step = (uint32_t *)userData;
    (*step)++;
}

template <Status expectedStatus = GMERR_OK>
static void AsyncBatchOperationCb(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg)
{
    EXPECT_EQ(expectedStatus, status) << errMsg;
    uint32_t *step = (uint32_t *)userData;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    Status ret = GmcBatchDeparseRet(batchRet, &totalNum, &successNum);
    EXPECT_EQ(ret, GMERR_OK);
    if (expectedStatus == GMERR_OK) {
        EXPECT_EQ(successNum, totalNum) << "\nsuc num:" << successNum << "\ntotal num:" << totalNum;
    }
    (*step)++;
}

void FuzzYangCreateTreeLabels(GmcStmtT *stmt, GmcConnT *conn)
{
    uint32_t step = 0;

    if (conn->connType == GMC_CONN_TYPE_ASYNC) {
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, g_testTreeNode, g_cfgTree, AsyncOperationCb, &step));
        WAIT_WHILE(step != 1);
    }
}

void FuzzYangDropTreeLabels(GmcStmtT *stmt, GmcConnT *conn)
{
    uint32_t step = 0;

    if (conn->connType == GMC_CONN_TYPE_ASYNC) {
        EXPECT_EQ(GMERR_OK, GmcDropVertexLabelAsync(stmt, "root", AsyncOperationCb, &step));
        WAIT_WHILE(step != 1);
    }
}

void FuzzYangCreateLabels(GmcStmtT *stmt, GmcConnT *conn)
{
    uint32_t step = 0;
    uint32_t nextStep = 0;
    const char *vertexJsonArr[] = {
        g_testT0Vertex, g_testT11Choice, g_testT12Choice, g_testT21Case, g_testT22Case, g_testT23Case, g_testL1};
    const char *edgeJsonArr[] = {g_testT0VertexT11ChoiceEdge, g_testT0VertexT12ChoiceEdge, g_testT11ChoiceT21CaseEdge,
        g_testT11ChoiceT22CaseEdge, g_testT12ChoiceT23CaseEdge};
    if (conn->connType == GMC_CONN_TYPE_SYNC) {
        for (int i = 0; i < g_vertexNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, vertexJsonArr[i], g_cfgGraph));
        }
        for (int i = 0; i < g_edgeNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcCreateEdgeLabel(stmt, edgeJsonArr[i], g_cfgGraph));
        }
    } else if (conn->connType == GMC_CONN_TYPE_ASYNC) {
        for (int i = 0; i < g_vertexNum; i++) {
            nextStep++;
            EXPECT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, vertexJsonArr[i], g_cfgGraph, AsyncOperationCb, &step));
            WAIT_WHILE(step != nextStep);
        }
        for (int i = 0; i < g_edgeNum; i++) {
            nextStep++;
            EXPECT_EQ(GMERR_OK, GmcCreateEdgeLabelAsync(stmt, edgeJsonArr[i], g_cfgGraph, AsyncOperationCb, &step));
            WAIT_WHILE(step != nextStep);
        }
    }
}

void FuzzYangCreatePartialTreeLabels(GmcStmtT *stmt, GmcConnT *conn)
{
    uint32_t step = 0;
    uint32_t nextStep = 0;
    const char *vertexJsonArr[] = {g_testRoot, g_testList};
    const char *edgeJsonArr[] = {g_testRootList};
    uint32_t vertexLabelNum = 2;
    uint32_t edgeLabelNum = 1;
    if (conn->connType == GMC_CONN_TYPE_SYNC) {
        for (int i = 0; i < vertexLabelNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, vertexJsonArr[i], g_cfgTree));
        }
        for (int i = 0; i < edgeLabelNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcCreateEdgeLabel(stmt, edgeJsonArr[i], g_cfgTree));
        }
    } else if (conn->connType == GMC_CONN_TYPE_ASYNC) {
        for (int i = 0; i < vertexLabelNum; i++) {
            nextStep++;
            EXPECT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, vertexJsonArr[i], g_cfgTree, AsyncOperationCb, &step));
            WAIT_WHILE(step != nextStep);
        }
        for (int i = 0; i < edgeLabelNum; i++) {
            nextStep++;
            EXPECT_EQ(GMERR_OK, GmcCreateEdgeLabelAsync(stmt, edgeJsonArr[i], g_cfgTree, AsyncOperationCb, &step));
            WAIT_WHILE(step != nextStep);
        }
    }
}

void FuzzYangDropLabels(GmcStmtT *stmt, GmcConnT *conn)
{
    uint32_t step = 0;
    uint32_t nextStep = 0;
    const char *vertexNameArr[g_vertexNum] = {
        "T0Vertex", "T11Choice", "T12Choice", "T21Case", "T22Case", "T23Case", "L1"};
    const char *edgeNameArr[] = {"T0VertexT11ChoiceEdge", "T0VertexT12ChoiceEdge", "T11ChoiceT21CaseEdge",
        "T11ChoiceT22CaseEdge", "T12ChoiceT23CaseEdge"};
    if (conn->connType == GMC_CONN_TYPE_SYNC) {
        for (int i = 0; i < g_edgeNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcDropEdgeLabel(stmt, edgeNameArr[i]));
        }
        for (int i = 0; i < g_vertexNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, vertexNameArr[i]));
        }
    } else if (conn->connType == GMC_CONN_TYPE_ASYNC) {
        for (int i = 0; i < g_edgeNum; i++) {
            nextStep++;
            EXPECT_EQ(GMERR_OK, GmcDropEdgeLabelAsync(stmt, edgeNameArr[i], AsyncOperationCb, &step));
            WAIT_WHILE(step != nextStep);
        }
        for (int i = 0; i < g_vertexNum; i++) {
            nextStep++;
            EXPECT_EQ(GMERR_OK, GmcDropVertexLabelAsync(stmt, vertexNameArr[i], AsyncOperationCb, &step));
            WAIT_WHILE(step != nextStep);
        }
    }
}

void FuzzYangCreateChoiceCaseLabel(GmcStmtT *stmt, GmcConnT *conn)
{
    uint32_t step = 0;

    if (conn->connType == GMC_CONN_TYPE_ASYNC) {
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, g_testChoiceCaseLabel, g_cfgTree, AsyncOperationCb, &step));
        WAIT_WHILE(step != 1);
    }
}

static GmcNspCfgT g_nspCfg = {.namespaceName = "nsp",
    .userName = NULL,
    .tablespaceName = NULL,
    .trxCfg = {.trxType = GMC_OPTIMISTIC_TRX, .isolationLevel = GMC_TX_ISOLATION_REPEATABLE}};
void FuzzYangCreateAndUseNamespace(GmcStmtT *stmt, GmcConnT *conn)
{
    if (conn->connType == GMC_CONN_TYPE_SYNC) {
        EXPECT_EQ(GMERR_OK, GmcCreateNamespaceWithCfg(stmt, &g_nspCfg));
        EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, g_nspCfg.namespaceName));
        return;
    }
    uint32_t step = 0;
    uint32_t nextStep = 0;
    nextStep++;
    EXPECT_EQ(GMERR_OK, GmcCreateNamespaceWithCfgAsync(stmt, &g_nspCfg, AsyncOperationCb, &step));
    WAIT_WHILE(step != nextStep);
    nextStep++;
    EXPECT_EQ(GMERR_OK, GmcUseNamespaceAsync(stmt, g_nspCfg.namespaceName, AsyncOperationCb, &step));
    WAIT_WHILE(step != nextStep);
}

void FuzzYangCreateAndUseTargetNamespace(GmcStmtT *stmt, GmcConnT *conn, const char *targetNamespace)
{
    GmcNspCfgT nspCfg = {.namespaceName = targetNamespace,
        .userName = NULL,
        .tablespaceName = NULL,
        .trxCfg = {.trxType = GMC_OPTIMISTIC_TRX, .isolationLevel = GMC_TX_ISOLATION_REPEATABLE}};
    if (conn->connType == GMC_CONN_TYPE_SYNC) {
        EXPECT_EQ(GMERR_OK, GmcCreateNamespaceWithCfg(stmt, &nspCfg));
        EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, nspCfg.namespaceName));
        return;
    }
    uint32_t step = 0;
    uint32_t nextStep = 0;
    nextStep++;
    EXPECT_EQ(GMERR_OK, GmcCreateNamespaceWithCfgAsync(stmt, &nspCfg, AsyncOperationCb, &step));
    WAIT_WHILE(step != nextStep);
    nextStep++;
    EXPECT_EQ(GMERR_OK, GmcUseNamespaceAsync(stmt, targetNamespace, AsyncOperationCb, &step));
    WAIT_WHILE(step != nextStep);
}

void FuzzYangUseTargetNamespace(GmcStmtT *stmt, GmcConnT *conn, const char *targetNamespace)
{
    if (conn->connType == GMC_CONN_TYPE_SYNC) {
        EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, targetNamespace));
        return;
    }
    uint32_t step = 0;
    uint32_t nextStep = 0;
    nextStep++;
    EXPECT_EQ(GMERR_OK, GmcUseNamespaceAsync(stmt, targetNamespace, AsyncOperationCb, &step));
    WAIT_WHILE(step != nextStep);
}

void FuzzDropNamespace(GmcStmtT *stmt, GmcConnT *conn)
{
    if (conn->connType == GMC_CONN_TYPE_SYNC) {
        EXPECT_EQ(GMERR_OK, GmcDropNamespace(stmt, g_nspCfg.namespaceName));
        return;
    }
    uint32_t step = 0;
    uint32_t nextStep = 0;
    nextStep++;
    EXPECT_EQ(GMERR_OK, GmcDropNamespaceAsync(stmt, g_nspCfg.namespaceName, AsyncOperationCb, &step));
    WAIT_WHILE(step != nextStep);
}

void FuzzDropTargetNamespace(GmcStmtT *stmt, GmcConnT *conn, const char *targetNamespace)
{
    if (conn->connType == GMC_CONN_TYPE_SYNC) {
        EXPECT_EQ(GMERR_OK, GmcDropNamespace(stmt, targetNamespace));
        return;
    }
    uint32_t step = 0;
    uint32_t nextStep = 0;
    nextStep++;
    EXPECT_EQ(GMERR_OK, GmcDropNamespaceAsync(stmt, targetNamespace, AsyncOperationCb, &step));
    WAIT_WHILE(step != nextStep);
}

void FuzzClearTargetNamespace(GmcStmtT *stmt, GmcConnT *conn, const char *targetNamespace)
{
    if (conn->connType == GMC_CONN_TYPE_SYNC) {
        EXPECT_EQ(GMERR_OK, 1);
        return;
    }
    uint32_t step = 0;
    uint32_t nextStep = 0;
    nextStep++;
    EXPECT_EQ(GMERR_OK, GmcClearNamespaceAsync(stmt, targetNamespace, AsyncOperationCb, &step));
    WAIT_WHILE(step != nextStep);
}

void FuzzYangInitData(GmcStmtT *stmt, GmcConnT *conn)
{
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, 2048));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG));
    EXPECT_EQ(GMERR_OK, GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON));
    EXPECT_EQ(GMERR_OK, GmcBatchPrepare(conn, &batchOption, &batch));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T0Vertex", GMC_OPERATION_MERGE));
    EXPECT_EQ(GMERR_OK, GmcYangSetRoot(batch, stmt));

    uint32_t value = 1;
    GmcPropValueT rootProp = {.propertyId = 0,
        .propertyName = {'F', '0', 0},
        .type = GMC_DATATYPE_INT32,
        .size = (int32_t)sizeof(int32_t),
        .value = &value};
    EXPECT_EQ(GMERR_OK, GmcYangSetVertexProperty(stmt, &rootProp, GMC_YANG_PROPERTY_OPERATION_MERGE));
    EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));

    if (conn->connType == GMC_CONN_TYPE_SYNC) {
        GmcBatchRetT batchRet = {0};
        EXPECT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    } else if (conn->connType == GMC_CONN_TYPE_ASYNC) {
        uint32_t step = 0;
        EXPECT_EQ(GMERR_OK, GmcBatchExecuteAsync(batch, AsyncBatchOperationCb, &step));
        WAIT_WHILE(step != 1);
    }
}

void InitPropValue(GmcPropValueT *propValue, const char *name, GmcDataTypeE type, void *value, uint32_t size)
{
    if (propValue == NULL) {
        return;
    }
    strcpy_s(propValue->propertyName, strlen(name) + 1, name);
    propValue->type = type;
    propValue->size = size;
    propValue->value = (void *)value;
}

void FuzzStartTrans(GmcConnT *conn)
{
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    if (conn->connType == GMC_CONN_TYPE_SYNC) {
        ASSERT_EQ(GMERR_OK, GmcTransStart(conn, &config));
    } else if (conn->connType == GMC_CONN_TYPE_ASYNC) {
        uint32_t step = 0;
        uint32_t nextStep = 0;
        nextStep++;
        ASSERT_EQ(GMERR_OK, GmcTransStartAsync(conn, &config, AsyncOperationCb, &step));
        WAIT_WHILE(step != nextStep);
    }
}

void FuzzEndTrans(GmcConnT *conn)
{
    if (conn->connType == GMC_CONN_TYPE_SYNC) {
        ASSERT_EQ(GMERR_OK, GmcTransCommit(conn));
    } else if (conn->connType == GMC_CONN_TYPE_ASYNC) {
        uint32_t step = 0;
        uint32_t nextStep = 0;
        nextStep++;
        ASSERT_EQ(GMERR_OK, GmcTransCommitAsync(conn, AsyncOperationCb, &step));
        WAIT_WHILE(step != nextStep);
    }
}

void FuzzYangInitPartialTreeData(GmcConnT *conn)
{
    FuzzStartTrans(conn);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, 2048));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG));
    EXPECT_EQ(GMERR_OK, GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON));
    EXPECT_EQ(GMERR_OK, GmcBatchPrepare(conn, &batchOption, &batch));

    // 插入1个root
    GmcStmtT *rootStmt = NULL;
    const char *rootName = "root";

    EXPECT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    FuzzYangUseTargetNamespace(rootStmt, conn, g_nspCfg.namespaceName);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, GMC_OPERATION_INSERT));
    EXPECT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));
    GmcNodeT *rootNode = NULL;
    uint32_t pk = 1;
    EXPECT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));
    GmcPropValueT propValue = {0};
    InitPropValue(&propValue, "PK", GMC_DATATYPE_UINT32, &pk, sizeof(pk));
    EXPECT_EQ(GMERR_OK, GmcYangSetNodeProperty(rootNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    InitPropValue(&propValue, "name", GMC_DATATYPE_STRING, rootName, strlen(rootName));
    EXPECT_EQ(GMERR_OK, GmcYangSetNodeProperty(rootNode, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    GmcNodeT *nodeC1 = NULL;
    const char *nodeNameC1 = "C1";
    EXPECT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, nodeNameC1, GMC_OPERATION_INSERT, &nodeC1));
    InitPropValue(&propValue, "name", GMC_DATATYPE_STRING, nodeNameC1, strlen(nodeNameC1));
    EXPECT_EQ(GMERR_OK, GmcYangSetNodeProperty(nodeC1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

    GmcNodeT *nodeC2 = NULL;
    const char *nodeNameC2 = "C2";
    EXPECT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, nodeNameC2, GMC_OPERATION_INSERT, &nodeC2));
    InitPropValue(&propValue, "name", GMC_DATATYPE_STRING, nodeNameC2, strlen(nodeNameC2));
    EXPECT_EQ(GMERR_OK, GmcYangSetNodeProperty(nodeC2, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));
    EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    // 插入2个L1
    GmcStmtT *stmtL1 = NULL;
    const char *nameL1 = "root::L1";
    EXPECT_EQ(GMERR_OK, GmcAllocStmt(conn, &stmtL1));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmtL1, nameL1, GMC_OPERATION_INSERT));
    uint32_t stringLen = 128;
    char objNameL1[stringLen] = "L1";
    GmcNodeT *nodeL1 = NULL;
    for (int32_t i = 1; i <= 2; i++) {
        EXPECT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, stmtL1));
        sprintf_s(objNameL1, stringLen, "L1_%d", i);
        EXPECT_EQ(GMERR_OK, GmcGetRootNode(stmtL1, &nodeL1));
        InitPropValue(&propValue, "name", GMC_DATATYPE_STRING, objNameL1, strlen(objNameL1));
        EXPECT_EQ(GMERR_OK, GmcYangSetNodeProperty(nodeL1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

        uint32_t L1Unique = 10 + i;
        InitPropValue(&propValue, "L1Unique", GMC_DATATYPE_UINT32, &L1Unique, sizeof(L1Unique));
        EXPECT_EQ(GMERR_OK, GmcYangSetNodeProperty(nodeL1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

        GmcNodeT *nodeC4 = NULL;
        char objNameC4[stringLen] = "C4";
        EXPECT_EQ(GMERR_OK, GmcYangEditChildNode(nodeL1, objNameC4, GMC_OPERATION_MERGE, &nodeC4));
        sprintf_s(objNameC4, stringLen, "C4_%d", i);
        InitPropValue(&propValue, "name", GMC_DATATYPE_STRING, objNameC4, strlen(objNameC4));
        EXPECT_EQ(GMERR_OK, GmcYangSetNodeProperty(nodeC4, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

        GmcNodeT *nodeCch1 = NULL;
        EXPECT_EQ(GMERR_OK, GmcYangEditChildNode(nodeC4, "Cch1", GMC_OPERATION_MERGE, &nodeCch1));

        GmcNodeT *nodeCca1 = NULL;
        EXPECT_EQ(GMERR_OK, GmcYangEditChildNode(nodeCch1, "Cca1", GMC_OPERATION_MERGE, &nodeCca1));
        char objNameCca1[stringLen] = "Cca1";
        sprintf_s(objNameCca1, stringLen, "Cca1_%d", i);
        InitPropValue(&propValue, "name", GMC_DATATYPE_STRING, objNameCca1, strlen(objNameCca1));
        EXPECT_EQ(GMERR_OK, GmcYangSetNodeProperty(nodeCca1, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE));

        EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmtL1));
    }

    if (conn->connType == GMC_CONN_TYPE_SYNC) {
        GmcBatchRetT batchRet = {0};
        EXPECT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    } else if (conn->connType == GMC_CONN_TYPE_ASYNC) {
        uint32_t step = 0;
        EXPECT_EQ(GMERR_OK, GmcBatchExecuteAsync(batch, AsyncBatchOperationCb, &step));
        WAIT_WHILE(step != 1);
    }
    FuzzEndTrans(conn);
    GmcFreeStmt(stmtL1);
    GmcFreeStmt(rootStmt);
}

Status FuzzGmcYangBatchOptionSetDiffType(GmcStmtT *stmt, GmcConnT *conn)
{
    g_tempElementCount = 0;
    GmcBatchOptionT batchOption = {0};
    GmcYangDiffTypeE diffType = *(GmcYangDiffTypeE *)DT_SetGetNumberRange(
        &g_Element[g_tempElementCount++], 0, GMC_YANG_DIFF_OFF, GMC_YANG_DIFF_BUTT);
    GmcYangBatchOptionSetDiffType(&batchOption, diffType);
}

Status FuzzGmcYangBindChild(GmcStmtT *stmt, GmcConnT *conn)
{
    g_tempElementCount = 0;

    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, 2048));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG));
    EXPECT_EQ(GMERR_OK, GmcBatchPrepare(conn, &batchOption, &batch));

    GmcStmtT *childStmt = NULL;
    EXPECT_EQ(GMERR_OK, GmcAllocStmt(conn, &childStmt));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(childStmt, "root::L1", GMC_OPERATION_MERGE));
    childStmt->yangVertexTmpId = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    GmcYangBindChild(batch, stmt, childStmt);
    GmcFreeStmt(childStmt);
}
// GmcYangSetListLocator
Status FuzzGmcYangSetListLocator(GmcStmtT *stmt, GmcConnT *conn)
{
    g_tempElementCount = 0;
    FuzzYangCreateAndUseNamespace(stmt, conn);
    FuzzYangCreatePartialTreeLabels(stmt, conn);
    FuzzYangInitPartialTreeData(conn);

    FuzzStartTrans(conn);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "root::L1", GMC_OPERATION_MERGE));

    GmcYangListLocatorT listLocator = {};
    listLocator.position = *(GmcYangListPositionE *)DT_SetGetNumberRange(
        &g_Element[g_tempElementCount++], 0, 0, GMC_YANG_LIST_POSITION_BUTT);

    // 变异参数个数（由 g_tempElementCount 标识）必须在每次变异中保持一致，故此处需用固定数值，不能变异，
    // 否则下面循环的次数可能在每次变异中都不一样，导致 g_tempElementCount 每次都不一样
    uint32_t refKeyFieldsCount = 1;

    GmcPropValueT *refKeyPropValues = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * refKeyFieldsCount);
    GmcPropValueT **refKeyFields = (GmcPropValueT **)malloc(sizeof(GmcPropValueT *) * refKeyFieldsCount);

    for (int i = 0; i < refKeyFieldsCount; i++) {
        refKeyFields[i] = &(refKeyPropValues[i]);
    }

    for (int i = 0; i < refKeyFieldsCount; i++) {
        refKeyFields[i]->type =
            *(GmcDataTypeE *)DT_SetGetNumberRange(&g_Element[g_tempElementCount++], 0, 0, GMC_DATATYPE_BUTT);
        refKeyFields[i]->propertyId = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
        refKeyFields[i]->value = (uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
        refKeyFields[i]->size = sizeof(uint32_t);
        // 这里将最后一个字节置为0，防止DmValue的type为string，value为u32时，接口内部调用strlen读越界
        ((char *)refKeyFields[i]->value)[refKeyFields[i]->size - 1] = '\0';
        char *propertyName =
            DT_SetGetString(&g_Element[g_tempElementCount++], strlen("F0") + 1, GMC_PROPERTY_NAME_MAX_LEN, "F0");
        errno_t err = strcpy_s(refKeyFields[i]->propertyName, GMC_PROPERTY_NAME_MAX_LEN, propertyName);
        EXPECT_EQ(EOK, err);
    }

    listLocator.refKeyFields = refKeyFields;
    listLocator.refKeyFieldsCount = refKeyFieldsCount;
    GmcYangSetListLocator(stmt, &listLocator);

    GmcYangListLocatorT listLocator2 = {};
    listLocator2.position = *(GmcYangListPositionE *)DT_SetGetNumberRange(&g_Element[g_tempElementCount++], 0, 0, 6);
    listLocator2.refKeyFields = NULL;
    GmcYangSetListLocator(stmt, &listLocator2);

    free(refKeyPropValues);
    free(refKeyFields);
    FuzzEndTrans(conn);

    FuzzClearTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
    FuzzDropTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
}

// GmcYangSetVertexProperty
Status FuzzGmcYangSetVertexProperty(GmcStmtT *stmt, GmcConnT *conn)
{
    g_tempElementCount = 0;
    FuzzYangCreateAndUseNamespace(stmt, conn);
    FuzzYangCreatePartialTreeLabels(stmt, conn);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_INSERT));
    GmcPropValueT propValue = {0};
    propValue.propertyId = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    char *propertyName =
        DT_SetGetString(&g_Element[g_tempElementCount++], strlen("F0") + 1, GMC_PROPERTY_NAME_MAX_LEN, "F0");
    errno_t err = strcpy_s(propValue.propertyName, GMC_PROPERTY_NAME_MAX_LEN, propertyName);
    EXPECT_EQ(EOK, err);
    propValue.type = GMC_DATATYPE_INT32;
    propValue.size = sizeof(int32_t);
    propValue.value = (void *)DT_SetGetS32(&g_Element[g_tempElementCount++], 0);
    GmcYangPropOpTypeE propType =
        *(GmcYangPropOpTypeE *)DT_SetGetNumberRange(&g_Element[g_tempElementCount++], 0, 0, 8);
    GmcYangSetVertexProperty(stmt, &propValue, propType);

    FuzzClearTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
    FuzzDropTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
}

// GmcYangSubtreeFilterExecute
Status FuzzGmcYangSubtreeFilterExecute(GmcStmtT *stmt, GmcConnT *conn, GmcStmtT *asyncStmt, GmcConnT *asyncConn)
{
    // doAction中已经把g_tempElementCount置为0
    FuzzYangCreateAndUseNamespace(stmt, conn);
    FuzzYangCreatePartialTreeLabels(stmt, conn);
    FuzzYangInitPartialTreeData(conn);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = DT_SetGetString(&g_Element[g_tempElementCount++], sizeof("T0Vertex"), MAX_STRING_LEN, "T0Vertex");
    char *dataout = NULL;
    int lenout = 0;
    DT_Pits_GetMutatorBuf(&dataout, &lenout);
    char *subtreeJsonInit = malloc(lenout + 1);
    memcpy(subtreeJsonInit, dataout, lenout);
    subtreeJsonInit[lenout] = '\0';
    filter.subtree.json = subtreeJsonInit;
    filter.jsonFlag = GMC_JSON_EXPORT_NULL_INFO;
    filter.defaultMode = *(GmcSubtreeWithDefaultModeE *)DT_SetGetNumberRange(&g_Element[g_tempElementCount++], 0, 0, 3);
    filter.maxDepth = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    filter.isLocationFilter = *(bool *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    filter.defaultMode = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    filter.configFlag = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    GmcSubtreeFilterT filters = {.filterMode = GMC_FETCH_JSON, .filter = &filter};
    GmcFetchRetT *fetchRet = NULL;
    GmcYangSubtreeFilterExecute(stmt, &filters, &fetchRet);
    const char **jsonReply = NULL;
    uint32_t count = 0;
    bool isEnd;
    GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count);
    GmcYangFreeFetchRet(fetchRet);
    free(subtreeJsonInit);
    DT_Pits_OneRunFree();
    FuzzClearTargetNamespace(asyncStmt, asyncConn, g_nspCfg.namespaceName);
    FuzzDropTargetNamespace(asyncStmt, asyncConn, g_nspCfg.namespaceName);
}

// GmcYangEditChildNode 树模型用，只测异步
Status FuzzGmcYangEditChildNode(GmcStmtT *stmt, GmcConnT *conn)
{
    g_tempElementCount = 0;
    FuzzYangCreateAndUseNamespace(stmt, conn);
    FuzzYangCreateTreeLabels(stmt, conn);
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, 2048));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG));
    EXPECT_EQ(GMERR_OK, GmcBatchPrepare(conn, &batchOption, &batch));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_MERGE));
    EXPECT_EQ(GMERR_OK, GmcYangSetRoot(batch, stmt));

    GmcNodeT *root = NULL;
    GmcGetRootNode(stmt, &root);
    GmcNodeT *node = NULL;
    const char *nodeName =
        DT_SetGetString(&g_Element[g_tempElementCount++], strlen("C1") + 1, GMC_PROPERTY_NAME_MAX_LEN, "C1");
    root->operationType =
        *(GmcOperationTypeE *)DT_SetGetNumberRange(&g_Element[g_tempElementCount++], 0, 0, GMC_OPERATION_BUTT);
    root->stmt->operationType =
        *(GmcOperationTypeE *)DT_SetGetNumberRange(&g_Element[g_tempElementCount++], 0, 0, GMC_OPERATION_BUTT);
    GmcOperationTypeE type =
        *(GmcOperationTypeE *)DT_SetGetNumberRange(&g_Element[g_tempElementCount++], 0, 0, GMC_OPERATION_BUTT);

    GmcYangEditChildNode(root, nodeName, type, &node);

    FuzzYangDropTreeLabels(stmt, conn);
    FuzzDropNamespace(stmt, conn);
}

// GmcYangSetNodeProperty
Status FuzzGmcYangSetNodeProperty(GmcStmtT *stmt, GmcConnT *conn)
{
    g_tempElementCount = 0;
    FuzzYangCreateAndUseNamespace(stmt, conn);
    FuzzYangCreateTreeLabels(stmt, conn);
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, 2048));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG));
    EXPECT_EQ(GMERR_OK, GmcBatchPrepare(conn, &batchOption, &batch));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_MERGE));
    EXPECT_EQ(GMERR_OK, GmcYangSetRoot(batch, stmt));

    GmcNodeT *root = NULL;
    GmcGetRootNode(stmt, &root);
    GmcNodeT *node = NULL;

    GmcYangEditChildNode(root, "C1", GMC_OPERATION_MERGE, &node);

    GmcPropValueT propValue = {0};
    propValue.propertyId = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 1);
    const char *propertyName =
        DT_SetGetString(&g_Element[g_tempElementCount++], strlen("F0") + 1, GMC_PROPERTY_NAME_MAX_LEN, "F0");
    strncpy_s(propValue.propertyName, sizeof(propValue.propertyName), propertyName, sizeof(propValue.propertyName));
    propValue.type = GMC_DATATYPE_INT32;
    propValue.size = sizeof(int32_t);
    propValue.value = DT_SetGetS32(&g_Element[g_tempElementCount++], 1);
    GmcYangPropOpTypeE propOpType = *(GmcYangPropOpTypeE *)DT_SetGetNumberRange(
        &g_Element[g_tempElementCount++], 0, 0, GMC_YANG_PROPERTY_OPERATION_BUTT);

    GmcYangSetNodeProperty(root, &propValue, propOpType);
    GmcYangSetNodeProperty(node, &propValue, propOpType);

    node->operationType =
        *(GmcOperationTypeE *)DT_SetGetNumberRange(&g_Element[g_tempElementCount++], 0, 0, GMC_OPERATION_BUTT);
    GmcYangSetNodeProperty(node, &propValue, propOpType);
    GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_SUBTREE_FILTER);
    GmcGetRootNode(stmt, &root);
    GmcYangEditChildNode(root, "C1", GMC_OPERATION_SUBTREE_FILTER, &node);
    GmcYangSetNodeProperty(node, &propValue, propOpType);

    GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_MERGE);
    GmcGetRootNode(stmt, &root);
    GmcYangEditChildNode(root, "C1", GMC_OPERATION_MERGE, &node);
    GmcYangSetNodeProperty(node, &propValue, propOpType);
    propValue.value = NULL;
    GmcYangSetNodeProperty(node, &propValue, propOpType);

    FuzzYangDropTreeLabels(stmt, conn);
    FuzzDropNamespace(stmt, conn);
}

// diff解析接口
Status FuzzGmcYangNodeGetName(GmcStmtT *stmt, GmcConnT *conn)
{
    g_tempElementCount = 0;
    GmcYangNodeT node = {0};
    node.name =
        DT_SetGetString(&g_Element[g_tempElementCount++], strlen("T0Node") + 1, GMC_PROPERTY_NAME_MAX_LEN, "T0Node");
    node.type = *(GmcYangNodeTypeE *)DT_SetGetNumberRange(&g_Element[g_tempElementCount++], 0, 0, GMC_YANG_LEAFLIST);
    node.opType = *(GmcDiffOpTypeE *)DT_SetGetNumberRange(&g_Element[g_tempElementCount++], 0, 0, GMC_DIFF_OP_CREATE);
    char *name = NULL;
    GmcYangNodeGetName(&node, &name);
    GmcYangNodeTypeE type;
    GmcYangNodeGetType(&node, &type);
    GmcDiffOpTypeE diffOp;
    GmcYangNodeGetDiffOpType(&node, &diffOp);
    GmcYangNodeValueT *value = NULL;

    GmcYangTreeT root = {0};
    root.treeType =
        *(DmYangTreeTypeE *)DT_SetGetNumberRange(&g_Element[g_tempElementCount++], 0, 0, DM_YANG_SERVER_DIFF_NODE_TREE);
    node.root = &root;
    uint32_t num;
    GmcYangNodeGetNewValue(&node, &value);
    GmcYangNodeGetOldValue(&node, &value);
    GmcYangNodeGetKeyPropNum(&node, &num);
    uint32_t propId = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 1);
    GmcYangNodeGetKeyPropValue(&node, propId, &value);
    GmcYangNodeGetNewPrevKey(&node, propId, &value);
    GmcYangNodeGetOldPrevKey(&node, propId, &value);
    bool isExist;
    GmcYangNodeHasNewPrev(&node, &isExist);
    GmcYangNodeHasOldPrev(&node, &isExist);
    GmcYangNodeT *nextNode = NULL;
    GmcYangNodeGetNext(&node, NULL, &nextNode);
    if (nextNode != NULL) {
        free(nextNode);
    }
}

// GmcYangSubtreeFilterExecuteAsync
extern "C" {
typedef struct {
    uint32_t *step;
    GmcStmtT *stmt;
    uint64_t startTime;      // 记录本次查询的起始时间
    uint32_t times;          // 记录分批返回结果时是第几次查询
    Status expectStatus;     // 预期的操作状态
    uint32_t filterMode;     // 过滤模式，使用枚举GmcFetchModeE设置值
    uint32_t lastExpectIdx;  // 分批查询上次查询期望结果的最后索引
    bool isSubTree;
    std::vector<std::string> &expectReply;  // 过滤模式下预期返回的查询结果, 校验用的字符串
    bool printTime;                         // 是否计算打印查询解析时间
} FetchRetCbParam;

typedef struct {
    uint32_t *step;
    uint32_t bufLen;
    uint8_t *buf;
} FetchRetDiffBufCbParam;
}

static void AsyncFetchRetCb(void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    if (userData == NULL) {
        return;
    }
    FetchRetCbParam *param = reinterpret_cast<FetchRetCbParam *>(userData);
    (*(param->step))++;
}

Status FuzzGmcYangSubtreeFilterExecuteAsync(GmcStmtT *stmt, GmcConnT *conn)
{
    // doAction中已经把g_tempElementCount置为0
    FuzzYangCreateAndUseNamespace(stmt, conn);
    FuzzYangCreatePartialTreeLabels(stmt, conn);
    FuzzYangInitPartialTreeData(conn);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = DT_SetGetString(&g_Element[g_tempElementCount++], sizeof("T0Vertex"), MAX_STRING_LEN, "T0Vertex");
    char *dataout = NULL;
    int lenout = 0;
    DT_Pits_GetMutatorBuf(&dataout, &lenout);
    char *subtreeJsonInit = malloc(lenout + 1);
    memcpy(subtreeJsonInit, dataout, lenout);
    subtreeJsonInit[lenout] = '\0';
    filter.subtree.json = subtreeJsonInit;
    filter.jsonFlag = GMC_JSON_EXPORT_NULL_INFO;
    filter.defaultMode = *(GmcSubtreeWithDefaultModeE *)DT_SetGetNumberRange(&g_Element[g_tempElementCount++], 0, 0, 3);
    filter.maxDepth = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    filter.isLocationFilter = *(bool *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    filter.defaultMode = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    filter.configFlag = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);

    GmcSubtreeFilterT filters = {.filterMode = GMC_FETCH_JSON, .filter = &filter};
    GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb, NULL);

    free(subtreeJsonInit);
    DT_Pits_OneRunFree();
    FuzzClearTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
    FuzzDropTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
}

void FuzzGmcYangFetchDiffExtExecuteAsync(GmcStmtT *stmt, GmcConnT *conn)
{
    g_tempElementCount = 0;
    FuzzYangCreateAndUseNamespace(stmt, conn);
    FuzzYangCreatePartialTreeLabels(stmt, conn);
    FuzzYangInitPartialTreeData(conn);

    uint32_t step = 0;
    std::vector<std::string> expectReplys;
    FetchRetCbParam param = {
        .step = &step,
        .stmt = stmt,
        .startTime = 0,
        .times = 1,
        .expectStatus = 0,
        .filterMode = GMC_FETCH_OBJ,
        .lastExpectIdx = 0,
        .isSubTree = false,
        .expectReply = expectReplys,
        .printTime = false,
    };
    param.times = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    param.expectStatus = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    param.filterMode = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    param.lastExpectIdx = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);

    GmcFetchDiffOptionT *option = NULL;
    EXPECT_EQ(GMERR_OK, GmcYangDiffFetchOptionCreate(&option));

    GmcYangDiffFetchOptionSetMode(
        option, (GmcFetchDiffModeE) * (uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0));

    GmcYangFetchDiffExtExecuteAsync(stmt, NULL, option, AsyncFetchRetCb, &param);
    GmcYangDiffFetchOptionDestroy(option);
    FuzzClearTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
    FuzzDropTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
}

Status FuzzGmcYangFetchDiffExecuteAsync(GmcStmtT *stmt, GmcConnT *conn)
{
    g_tempElementCount = 0;
    FuzzYangCreateAndUseNamespace(stmt, conn);
    FuzzYangCreatePartialTreeLabels(stmt, conn);
    FuzzYangInitPartialTreeData(conn);

    uint32_t step = 0;
    std::vector<std::string> expectReplys;
    FetchRetCbParam param = {
        .step = &step,
        .stmt = stmt,
        .startTime = 0,
        .times = 1,
        .expectStatus = 0,
        .filterMode = GMC_FETCH_OBJ,
        .lastExpectIdx = 0,
        .isSubTree = false,
        .expectReply = expectReplys,
        .printTime = false,
    };
    param.times = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    param.expectStatus = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    param.filterMode = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    param.lastExpectIdx = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    GmcYangFetchDiffExecuteAsync(stmt, NULL, AsyncFetchRetCb, &param);

    FuzzClearTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
    FuzzDropTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
}

static void AsyncFetchDiffBufRetCb(void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    if (userData == NULL) {
        return;
    }
    FetchRetDiffBufCbParam *param = reinterpret_cast<FetchRetDiffBufCbParam *>(userData);
    bool isEnd = false;
    GmcYangFetchDiffBufRetDeparse(fetchRet, &isEnd, &param->bufLen);
    param->buf = (uint8_t *)malloc(param->bufLen);
    GmcYangFetchDiffBuf(fetchRet, param->bufLen, param->buf);
    (*(param->step))++;
}

static void FuzzYangGetDiffBuf(GmcStmtT *stmt, FetchRetDiffBufCbParam *param)
{
    uint32_t step = 0;
    param->bufLen = 0;
    param->step = &step;
    param->buf = NULL;
    uint32_t nextStep = 0;
    nextStep++;
    GmcYangFetchDiffBufExecuteAsync(stmt, NULL, AsyncFetchDiffBufRetCb, param);
    WAIT_WHILE(step != nextStep);
}

static void FuzzYangGetDiffBufExt(GmcStmtT *stmt, FetchRetDiffBufCbParam *param)
{
    uint32_t step = 0;
    param->bufLen = 0;
    param->step = &step;
    param->buf = NULL;
    uint32_t nextStep = 0;
    nextStep++;
    GmcFetchDiffOptionT *option = NULL;
    EXPECT_EQ(GMERR_OK, GmcYangDiffFetchOptionCreate(&option));

    GmcYangDiffFetchOptionSetMode(
        option, (GmcFetchDiffModeE) * (uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0));
    GmcYangFetchDiffBufExecuteAsync(stmt, NULL, AsyncFetchDiffBufRetCb, param);
    WAIT_WHILE(step != nextStep);
    GmcYangDiffFetchOptionDestroy(option);
}

Status FuzzGmcYangFetchDiffBufExecuteAsync(GmcStmtT *stmt, GmcConnT *conn, GmcStmtT *syncStmt, GmcConnT *syncConn)
{
    g_tempElementCount = 0;
    const char *dstNamespace = "dstNamespace";
    FuzzYangCreateAndUseTargetNamespace(stmt, conn, dstNamespace);
    FuzzYangCreateAndUseNamespace(stmt, conn);
    FuzzYangCreatePartialTreeLabels(stmt, conn);
    FuzzStartTrans(conn);
    FuzzYangInitPartialTreeData(conn);

    FetchRetDiffBufCbParam param = {0};
    FuzzYangGetDiffBuf(stmt, &param);
    GmcFetchRetT *fetchRet = NULL;
    uint32_t bufLen = param.bufLen;
    param.bufLen = *(uint32_t *)DT_SetGetNumberRange(&g_Element[g_tempElementCount++], 0, 0, bufLen);
    uint8_t *buf = (uint8_t *)DT_SetGetFixBlob(&g_Element[g_tempElementCount++], bufLen, bufLen, (char *)param.buf);
    FuzzYangUseTargetNamespace(stmt, conn, dstNamespace);
    GmcYangDiffFetchRetFromBuf(syncStmt, param.bufLen, buf, &fetchRet);
    free(param.buf);
    FuzzEndTrans(conn);
    FuzzClearTargetNamespace(stmt, conn, dstNamespace);
    FuzzDropTargetNamespace(stmt, conn, dstNamespace);
    FuzzClearTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
    FuzzDropTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
}

void FuzzGmcYangFetchDiffBufExtExecuteAsync(GmcStmtT *stmt, GmcConnT *conn, GmcStmtT *syncStmt, GmcConnT *syncConn)
{
    g_tempElementCount = 0;
    const char *dstNamespace = "dstNamespace";
    FuzzYangCreateAndUseTargetNamespace(stmt, conn, dstNamespace);
    FuzzYangCreateAndUseNamespace(stmt, conn);
    FuzzYangCreatePartialTreeLabels(stmt, conn);
    FuzzStartTrans(conn);
    FuzzYangInitPartialTreeData(conn);

    FetchRetDiffBufCbParam param = {0};
    FuzzYangGetDiffBufExt(stmt, &param);
    GmcFetchRetT *fetchRet = NULL;
    uint32_t bufLen = param.bufLen;
    param.bufLen = *(uint32_t *)DT_SetGetNumberRange(&g_Element[g_tempElementCount++], 0, 0, bufLen);
    uint8_t *buf = (uint8_t *)DT_SetGetFixBlob(&g_Element[g_tempElementCount++], bufLen, bufLen, (char *)param.buf);
    FuzzYangUseTargetNamespace(stmt, conn, dstNamespace);
    GmcYangDiffFetchRetFromBuf(syncStmt, param.bufLen, buf, &fetchRet);
    free(param.buf);
    FuzzEndTrans(conn);
    FuzzClearTargetNamespace(stmt, conn, dstNamespace);
    FuzzDropTargetNamespace(stmt, conn, dstNamespace);
    FuzzClearTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
    FuzzDropTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
}

static void AsyncYangPersistenceRetCb(void *userData, Status status, const char *errMsg)
{
    if (userData == NULL) {
        return;
    }
    uint32_t *step = reinterpret_cast<uint32_t *>(userData);
    (*step)++;
}

Status FuzzGmcYangExportDataAsync(GmcStmtT *stmt, GmcConnT *conn)
{
    g_tempElementCount = 0;
    FuzzYangCreateAndUseNamespace(stmt, conn);
    FuzzYangCreatePartialTreeLabels(stmt, conn);
    FuzzYangInitPartialTreeData(conn);
    uint32_t step = 0;
    GmcPersistenceConfigT config;
    config.algorithm = *(GmcDigestAlgorithmE *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    config.mode = *(GmcPersistenceModeE *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    config.dir = "./yang/testExport/";
    config.password = "testPasswordKey";
    GmcYangExportDataAsync(stmt, &config, AsyncYangPersistenceRetCb, &step);
    FuzzClearTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
    FuzzDropTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
}

void FuzzGmcYangImportDataAsync(GmcStmtT *stmt, GmcConnT *conn)
{
    g_tempElementCount = 0;
    uint32_t step = 0;
    GmcPersistenceConfigT config;
    config.algorithm = *(GmcDigestAlgorithmE *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    config.mode = *(GmcPersistenceModeE *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    config.dir = "./yang/testImport/";
    config.password = "testPasswordKey";
    GmcYangImportDataAsync(stmt, &config, AsyncYangPersistenceRetCb, &step);
    FuzzClearTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
}

// GmcYangConvertNodeProperty
void FuzzGmcYangConvertNodeProperty(GmcStmtT *stmt, GmcConnT *conn)
{
    g_tempElementCount = 0;
    FuzzYangCreateAndUseNamespace(stmt, conn);
    FuzzYangCreatePartialTreeLabels(stmt, conn);
    FuzzYangInitPartialTreeData(conn);
    // root
    GmcStmtT *rootStmt = NULL;
    const char *rootName = "root";

    EXPECT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, GMC_OPERATION_NONE));
    GmcNodeT *rootNode = NULL;
    EXPECT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));

    // 客户端转换枚举类型
    GmcNodeT *nodeC1 = NULL;
    const char *nodeNameC1 = "C1";
    EXPECT_EQ(GMERR_OK, GmcYangEditChildNode(rootNode, nodeNameC1, GMC_OPERATION_INSERT, &nodeC1));

    int32_t identityValue = 3;
    GmcPropValueT rootIdentity = {.propertyId = 0,
        .propertyName = {'C', '1', '_', 'i', 'd', 'e', 'n', 't', 'i', 't', 'y', 0},
        .type = *(GmcDataTypeE *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0),
        .size = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0),
        .value = (void *)&identityValue};
    char enumStr[DM_MAX_NAME_LENGTH];
    GmcPropValueT outPropValue = {
        .propertyId = 0, .propertyName = {0}, .type = GMC_DATATYPE_BUTT, .size = DM_MAX_NAME_LENGTH, .value = enumStr};
    GmcConvertTypeE idenConvertType = *(GmcConvertTypeE *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    GmcYangConvertNodeProperty(nodeC1, idenConvertType, &rootIdentity, &outPropValue);

    GmcFreeStmt(rootStmt);
    FuzzClearTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
    FuzzDropTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
}

// GmcYangConvertVertexProperty
void FuzzGmcYangConvertVertexProperty(GmcStmtT *stmt, GmcConnT *conn)
{
    g_tempElementCount = 0;
    FuzzYangCreateAndUseNamespace(stmt, conn);
    FuzzYangCreatePartialTreeLabels(stmt, conn);
    FuzzYangInitPartialTreeData(conn);
    // root
    GmcStmtT *rootStmt = NULL;
    const char *rootName = "root";

    EXPECT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, GMC_OPERATION_NONE));
    GmcNodeT *rootNode = NULL;
    EXPECT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));

    const char *rootEnumValue = "case1";
    // 客户端转换枚举类型
    GmcPropValueT rootEnum = {.propertyId = 0,
        .propertyName = {'r', 'o', 'o', 't', '_', 'e', 'n', 'u', 'm', 0},
        .type = *(GmcDataTypeE *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0),
        .size = *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0),
        .value = (void *)rootEnumValue};
    char enumStr[DM_MAX_NAME_LENGTH];
    GmcPropValueT outPropValue = {
        .propertyId = 0, .propertyName = {0}, .type = GMC_DATATYPE_BUTT, .size = DM_MAX_NAME_LENGTH, .value = enumStr};
    GmcConvertTypeE enumConvertType = *(GmcConvertTypeE *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0);
    GmcYangConvertVertexProperty(rootStmt, enumConvertType, &rootEnum, &outPropValue);

    GmcFreeStmt(rootStmt);
    FuzzClearTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
    FuzzDropTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
}

// GmcYangValidateAsync
void (*CommonAsyncYangValidateCb)(void *userData, bool validateRes, Status status, const char *errMsg);
void FuzzGmcYangValidateAsync(GmcStmtT *stmt, GmcConnT *conn)
{
    g_tempElementCount = 0;
    bool result;
    GmcValidateConfigT cfg = {
        .type = *(uint32_t *)DT_SetGetNumberRange(&g_Element[g_tempElementCount++], 0, 0, 2), .cfgJson = NULL};
    GmcYangValidateAsync(stmt, &cfg, CommonAsyncYangValidateCb, &result);
}

// GmcYangValidateModelAsync
void FuzzGmcYangValidateModelAsync(GmcStmtT *stmt, GmcConnT *conn)
{
    bool result;
    GmcYangValidateModelAsync(stmt, CommonAsyncYangValidateCb, &result);
}

// GmcYangGetChoiceCasePath 树模型用，只测异步
void FuzzGmcYangGetChoiceCasePath(GmcStmtT *stmt, GmcConnT *conn)
{
    g_tempElementCount = 0;
    FuzzYangCreateAndUseNamespace(stmt, conn);
    FuzzYangCreateChoiceCaseLabel(stmt, conn);
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, 2048));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG));
    EXPECT_EQ(GMERR_OK, GmcBatchPrepare(conn, &batchOption, &batch));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_MERGE));
    EXPECT_EQ(GMERR_OK, GmcYangSetRoot(batch, stmt));
    GmcNodeT *rootNode = NULL;
    EXPECT_EQ(GMERR_OK, GmcGetRootNode(stmt, &rootNode));

    const char *childName =
        DT_SetGetString(&g_Element[g_tempElementCount++], strlen("C1") + 1, GMC_PROPERTY_NAME_MAX_LEN, "C1");
    GmcYangChildElementTypeE childType = *(GmcYangChildElementTypeE *)DT_SetGetNumberRange(
        &g_Element[g_tempElementCount++], 0, 0, GMC_YANG_TYPE_VERTEX + 1);

    char path[ST_MAX_LOST_PATH_LEN] = {0};
    GmcLostPathInfoT pathInfo = {.path = path, .totalPathLen = ST_MAX_LOST_PATH_LEN, .actualPathLen = 0};
    GmcYangGetChoiceCasePath(rootNode, childName, childType, &pathInfo);

    FuzzYangDropTreeLabels(stmt, conn);
    FuzzDropNamespace(stmt, conn);
}

static void AsyncYangGetLeafrefPathCb(void *userData, GmcLeafrefPath leafrefPath, Status status, const char *errMsg)
{
    if (userData == NULL) {
        ASSERT_EQ(0, 1);
        return;
    }

    uint32_t *step = (uint32_t *)userData;
    (*step)++;
}

// GmcYangGetLeafrefPathAsync 树模型用，只测异步
void FuzzGmcYangGetLeafrefPathAsync(GmcStmtT *stmt, GmcConnT *conn)
{
    g_tempElementCount = 0;
    FuzzYangCreateAndUseNamespace(stmt, conn);
    FuzzYangCreatePartialTreeLabels(stmt, conn);
    FuzzYangInitPartialTreeData(conn);
    FuzzStartTrans(conn);

    const char *path =
        DT_SetGetString(&g_Element[g_tempElementCount++], strlen("/root") + 1, DM_MAX_XPATH_LENGTH, "/root");

    uint32_t step = 0;
    GmcYangGetLeafrefPathAsync(stmt, path, NULL, AsyncYangGetLeafrefPathCb, &step);
    WAIT_WHILE(step != 1);
    FuzzEndTrans(conn);

    FuzzClearTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
    FuzzDropTargetNamespace(stmt, conn, g_nspCfg.namespaceName);
}

void FuzzGmcYangGetChildTypeAndNameById(GmcStmtT *stmt, GmcConnT *conn)
{
    g_tempElementCount = 0;

    FuzzStartTrans(conn);
    // root
    GmcStmtT *rootStmt = NULL;
    const char *rootName = "root";

    EXPECT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, GMC_OPERATION_NONE));
    GmcNodeT *rootNode = NULL;
    EXPECT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));

    GmcNodeT *node = NULL;
    char *path = (DT_SetGetString(&g_Element[g_tempElementCount++], strlen("C1") + 1, 128 + 1, "C1"));
    GmcNodeGetChild(rootNode, path, &node);

    bool isProp;
    const char *name;
    GmcYangGetChildTypeAndNameById(
        node, *(uint32_t *)DT_SetGetU32(&g_Element[g_tempElementCount++], 0), &isProp, &name);

    GmcFreeStmt(rootStmt);
    FuzzEndTrans(conn);
}

void FuzzGmcYangGetChildPropAndNodeNumber(GmcStmtT *stmt, GmcConnT *conn)
{
    g_tempElementCount = 0;

    FuzzStartTrans(conn);
    // root
    GmcStmtT *rootStmt = NULL;
    const char *rootName = "root";

    EXPECT_EQ(GMERR_OK, GmcAllocStmt(conn, &rootStmt));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(rootStmt, rootName, GMC_OPERATION_NONE));
    GmcNodeT *rootNode = NULL;
    EXPECT_EQ(GMERR_OK, GmcGetRootNode(rootStmt, &rootNode));

    GmcNodeT *node = NULL;
    char *path = (DT_SetGetString(&g_Element[g_tempElementCount++], strlen("C1") + 1, 128 + 1, "C1"));
    GmcNodeGetChild(rootNode, path, &node);

    uint32_t number;
    GmcYangGetChildPropAndNodeNumber(node, &number);

    GmcFreeStmt(rootStmt);
    FuzzEndTrans(conn);
}

// 测试同步接口
TEST_F(FuzzClientYang, FuzzGmcYangBatchOptionSetDiffType){
    DT_FUZZ_START_CMD("FuzzGmcYangBatchOptionSetDiffType"){FuzzGmcYangBatchOptionSetDiffType(stmt, conn);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientYang, FuzzGmcYangNodeGetName){
    DT_FUZZ_START_CMD("FuzzGmcYangNodeGetName"){FuzzGmcYangNodeGetName(stmt, conn);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientYang, FuzzGmcYangBindChild)
{
    FuzzYangCreateAndUseNamespace(asyncStmt, asyncConn);
    FuzzYangCreatePartialTreeLabels(asyncStmt, asyncConn);
    DT_FUZZ_START_CMD("FuzzGmcYangBindChild")
    {
        FuzzGmcYangBindChild(asyncStmt, asyncConn);
    }
    DT_FUZZ_END()
    FuzzClearTargetNamespace(asyncStmt, asyncConn, g_nspCfg.namespaceName);
    FuzzDropTargetNamespace(asyncStmt, asyncConn, g_nspCfg.namespaceName);
}

TEST_F(FuzzClientYang, FuzzGmcYangSetListLocator){
    DT_FUZZ_START_CMD("FuzzGmcYangSetListLocator"){FuzzGmcYangSetListLocator(asyncStmt, asyncConn);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientYang, FuzzGmcYangSetVertexProperty){
    DT_FUZZ_START_CMD("FuzzGmcYangSetVertexProperty"){FuzzGmcYangSetVertexProperty(asyncStmt, asyncConn);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientYang, FuzzGmcYangSubtreeFilterExecute)
{
    // json建模准备
    DT_Pits_SetBinFile("./json/FilterItemSubtreeJson.bin");
    int id;
    id = DT_Pits_ParseDataModel("./json/JsonNormal.xml", "jsona", 1);
    DT_Pits_Enable_AllMutater(1);

    DT_FUZZ_START_CMD("FuzzGmcYangSubtreeFilterExecute")
    {
        DT_Pits_DoAction(id);
        FuzzGmcYangSubtreeFilterExecute(stmt, conn, asyncStmt, asyncConn);
    }
    DT_FUZZ_END()

    // 释放json建模工具占用内存
    DT_Pits_ParseFree();
}

// 测试异步接口
TEST_F(FuzzClientYang, FuzzGmcYangEditChildNode){
    DT_FUZZ_START_CMD("FuzzGmcYangEditChildNode"){FuzzGmcYangEditChildNode(asyncStmt, asyncConn);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientYang, FuzzGmcYangSetNodeProperty){
    DT_FUZZ_START_CMD("FuzzGmcYangSetNodeProperty"){FuzzGmcYangSetNodeProperty(asyncStmt, asyncConn);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientYang, FuzzGmcYangBindChildAsync)
{
    FuzzYangCreateAndUseNamespace(asyncStmt, asyncConn);
    FuzzYangCreatePartialTreeLabels(asyncStmt, asyncConn);
    FuzzYangInitPartialTreeData(asyncConn);
    DT_FUZZ_START_CMD("FuzzGmcYangBindChildAsync")
    {
        FuzzGmcYangBindChild(asyncStmt, asyncConn);
    }
    DT_FUZZ_END()
    FuzzClearTargetNamespace(asyncStmt, asyncConn, g_nspCfg.namespaceName);
    FuzzDropTargetNamespace(asyncStmt, asyncConn, g_nspCfg.namespaceName);
}

TEST_F(FuzzClientYang, FuzzGmcYangSubtreeFilterExecuteAsync)
{
    // json建模准备
    DT_Pits_SetBinFile("./json/FilterItemSubtreeJson.bin");
    int id;
    id = DT_Pits_ParseDataModel("./json/JsonNormal.xml", "jsona", 1);
    DT_Pits_Enable_AllMutater(1);

    DT_FUZZ_START_CMD("FuzzGmcYangSubtreeFilterExecuteAsync")
    {
        DT_Pits_DoAction(id);
        FuzzGmcYangSubtreeFilterExecuteAsync(asyncStmt, asyncConn);
    }
    DT_FUZZ_END()

    // 释放json建模工具占用内存
    DT_Pits_ParseFree();
}

TEST_F(FuzzClientYang, FuzzGmcYangConvertNodeProperty){
    DT_FUZZ_START_CMD("FuzzGmcYangConvertNodeProperty"){FuzzGmcYangConvertNodeProperty(asyncStmt, asyncConn);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientYang, FuzzGmcYangConvertVertexProperty){
    DT_FUZZ_START_CMD("FuzzGmcYangConvertVertexProperty"){FuzzGmcYangConvertVertexProperty(asyncStmt, asyncConn);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientYang, FuzzGmcYangExportDataAsync){
    DT_FUZZ_START_CMD("FuzzGmcYangExportDataAsync"){FuzzGmcYangExportDataAsync(asyncStmt, asyncConn);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientYang, FuzzGmcYangImportDataAsync)
{
    FuzzYangCreateAndUseNamespace(asyncStmt, asyncConn);
    FuzzYangCreatePartialTreeLabels(asyncStmt, asyncConn);
    FuzzYangInitPartialTreeData(asyncConn);
    uint32_t step = 0;
    GmcPersistenceConfigT config;
    config.algorithm = GMC_DIGEST_ALGORITHM_NONE;
    config.mode = GMC_PERSISTENCE_MODE_YANG;
    config.dir = "./yang/testImport";
    config.password = "testPasswordKey";
    GmcYangExportDataAsync(asyncStmt, &config, AsyncYangPersistenceRetCb, &step);
    WAIT_WHILE(step != 1);
    FuzzClearTargetNamespace(asyncStmt, asyncConn, g_nspCfg.namespaceName);
    DT_FUZZ_START_CMD("FuzzGmcYangImportDataAsync")
    {
        FuzzGmcYangImportDataAsync(asyncStmt, asyncConn);
    }
    DT_FUZZ_END()
    FuzzDropTargetNamespace(asyncStmt, asyncConn, g_nspCfg.namespaceName);
}

TEST_F(FuzzClientYang, FuzzGmcYangFetchDiffExecuteAsync){
    DT_FUZZ_START_CMD("FuzzGmcYangFetchDiffExecuteAsync"){FuzzGmcYangFetchDiffExecuteAsync(asyncStmt, asyncConn);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientYang, FuzzGmcYangFetchDiffExtExecuteAsync){
    DT_FUZZ_START_CMD("FuzzGmcYangFetchDiffExtExecuteAsync"){FuzzGmcYangFetchDiffExtExecuteAsync(asyncStmt, asyncConn);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientYang, FuzzGmcYangFetchDiffBufExecuteAsync){DT_FUZZ_START_CMD("FuzzGmcYangFetchDiffBufExecuteAsync"){
    FuzzGmcYangFetchDiffBufExecuteAsync(asyncStmt, asyncConn, stmt, conn);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientYang, FuzzGmcYangFetchDiffBufExtExecuteAsync){DT_FUZZ_START_CMD(
    "FuzzGmcYangFetchDiffBufExtExecuteAsync"){FuzzGmcYangFetchDiffBufExtExecuteAsync(asyncStmt, asyncConn, stmt, conn);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientYang, FuzzGmcYangValidateAsync){
    DT_FUZZ_START_CMD("FuzzGmcYangValidateAsync"){FuzzGmcYangValidateAsync(asyncStmt, asyncConn);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientYang, FuzzGmcYangValidateModelAsync){
    DT_FUZZ_START_CMD("FuzzGmcYangValidateModelAsync"){FuzzGmcYangValidateModelAsync(asyncStmt, asyncConn);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientYang, FuzzGmcYangGetChoiceCasePath){
    DT_FUZZ_START_CMD("FuzzGmcYangGetChoiceCasePath"){FuzzGmcYangGetChoiceCasePath(asyncStmt, asyncConn);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientYang, FuzzGmcYangGetLeafrefPathAsync){
    DT_FUZZ_START_CMD("FuzzGmcYangGetLeafrefPathAsync"){FuzzGmcYangGetLeafrefPathAsync(asyncStmt, asyncConn);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientYang, FuzzGmcYangGetChildTypeAndNameById)
{
    FuzzYangCreateAndUseNamespace(asyncStmt, asyncConn);
    FuzzYangCreatePartialTreeLabels(asyncStmt, asyncConn);
    FuzzYangInitPartialTreeData(asyncConn);
    DT_FUZZ_START_CMD("FuzzGmcYangGetChildTypeAndNameById")
    {
        FuzzGmcYangGetChildTypeAndNameById(asyncStmt, asyncConn);
    }
    DT_FUZZ_END()
    FuzzClearTargetNamespace(asyncStmt, asyncConn, g_nspCfg.namespaceName);
    FuzzDropTargetNamespace(asyncStmt, asyncConn, g_nspCfg.namespaceName);
}

TEST_F(FuzzClientYang, FuzzGmcYangGetChildPropAndNodeNumber)
{
    FuzzYangCreateAndUseNamespace(asyncStmt, asyncConn);
    FuzzYangCreatePartialTreeLabels(asyncStmt, asyncConn);
    FuzzYangInitPartialTreeData(asyncConn);
    DT_FUZZ_START_CMD("FuzzGmcYangGetChildPropAndNodeNumber")
    {
        FuzzGmcYangGetChildPropAndNodeNumber(asyncStmt, asyncConn);
    }
    DT_FUZZ_END()
    FuzzClearTargetNamespace(asyncStmt, asyncConn, g_nspCfg.namespaceName);
    FuzzDropTargetNamespace(asyncStmt, asyncConn, g_nspCfg.namespaceName);
}
