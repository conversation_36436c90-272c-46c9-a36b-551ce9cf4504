/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Implementation of tree node dml
 * Author: wuyongzheng
 * Create: 2022-08-27
 */

#include "ee_yang_edit_node.h"
#include "ee_diff_common.h"
#include "ee_diff_generate.h"
#include "ee_error_path.h"
#include "ee_dml_edge.h"
#include "ee_dml_vertex.h"
#include "ee_dml_vertex_heap_batch.h"
#include "ee_dml_index.h"
#include "ee_feature_import.h"
#include "ee_yang_id_mapping.h"
#include "ee_yang_edit_common.h"
#include "dm_yang_interface.h"
#include "dm_yang_vertex.h"
#include "dm_yang_common.h"
#include "dm_meta_topo_label.h"

#ifdef __cplusplus
extern "C" {
#endif

static Status CheckNodeOpIsInvalid(QryStmtT *stmt, DmNodeOpTypeE op, bool isCreated, bool isNoneNoExist)
{
    // 不允许对一个已经存在的node进行create操作
    if (op == DM_NODE_INSERT && isCreated) {
        stmt->errorCode = DATA_EXCEPTION_YANG_TARGET_EXIST;
        return GMERR_SYNTAX_ERROR;
    }
    // 不允许对一个不存在的node进行delete操作
    if (op == DM_NODE_DELETE_GRAPH && !isCreated) {
        stmt->errorCode = DATA_EXCEPTION_YANG_INDEX_NOT_EXIST;
        return GMERR_SYNTAX_ERROR;
    }

    // none不存在的父节点后对子节点进行merge|replace|insert|delete操作 预期报错
    if (isNoneNoExist && DmIsYangNoneNoExistIllegalOperation(op)) {
        stmt->errorCode = DATA_EXCEPTION_YANG_INDEX_NOT_EXIST;
        return GMERR_DATA_EXCEPTION;
    }

    return GMERR_OK;
}

static Status QryDeleteNodeChildGraph(QryStmtT *stmt, DmNodeT *node, DmVertexT *oldVertex)
{
    if (!DmNodeIsCreated(node)) {
        return GMERR_OK;
    }
    DmVertexDescT *vertexDesc = oldVertex->vertexDesc;
    for (uint32_t i = 0; i < vertexDesc->edgeLabelNum; i++) {
        uint32_t edgeLabelId = vertexDesc->commonInfo->relatedEdgeLabels[i].edgeLabelId;
        DmEdgeLabelT *edgeLabel = NULL;
        Status ret = CataGetEdgeLabelById(edgeLabelId, (DmEdgeLabelT **)&edgeLabel, NULL);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get eLabel by id, %" PRIu32, edgeLabelId);
            return ret;
        }
        // 如果当前边关联的没有sourceNode，或者关联的sourcenodeId不是当前要删除的node或者该node的孩子则略过
        if (edgeLabel->sourceNodePath == NULL || !DmSourceNodeIdIsInNode(edgeLabel->sourceUniqueNodeId, node)) {
            (void)CataReleaseEdgeLabel(edgeLabel);
            continue;
        }

        EdgeScanDirectionE direction = (edgeLabel->sourceVertexLabelId == vertexDesc->labelId) ? EDGE_OUT : EDGE_IN;
        (void)CataReleaseEdgeLabel(edgeLabel);
        if (direction == EDGE_IN) {
            continue;
        }
        // 对于出边，找到所有孩子节点并删除
        ret = QryExecuteDelVertexForSingleEdge(stmt, edgeLabelId, oldVertex, true);
        if (ret != GMERR_OK) {
            return ret;
        }
        uint64_t oldAddr = 0;
        ret = DmVertexSetFirstEdgeTopoAddrById(oldVertex, edgeLabelId, 0, &oldAddr);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DmVertexSetLastEdgeTopoAddrById(oldVertex, edgeLabelId, 0, &oldAddr);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

// 该函数不必记录LastErr，因为该函数通常是出错后才被调用生成path，如果该函数内记录了LastErr会覆盖初始出错的原因
void QryPackageNodeNamePath(QryStmtT *stmt, DmNodeT *deltaNode)
{
    DB_POINTER2(stmt, deltaNode);
    // 释放原则：通过调用QrySetErrorPathToRsp函数，在此函数中由FreeErrorPath进行内存释放
    // memctx: stmt->session->memCtx
    Status ret = QryAllocErrorPath(stmt->session);
    if (ret != GMERR_OK) {
        return;
    }
    char *tmpNodeNamePath = NULL;
    const char *nodeName = deltaNode->nodeDesc->name;
    if (strlen(stmt->session->errorPath->errorPathBuf) == 0) {
        if (strcat_s(stmt->session->errorPath->errorPathBuf, QRY_ERROR_PATH_PAYLOAD_MAX_LEN, "/") != EOK) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "strcat root path.");
            goto EXIT;
        }
        if (strcat_s(stmt->session->errorPath->errorPathBuf, QRY_ERROR_PATH_PAYLOAD_MAX_LEN, nodeName) != EOK) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "strca node name path.");
            goto EXIT;
        }
        return;
    }
    uint32_t bufLen = (uint32_t)strlen(stmt->session->errorPath->errorPathBuf) + 1;
    ret = QryAllocMem(stmt->session->memCtx, bufLen, &tmpNodeNamePath);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    if (strcpy_s(tmpNodeNamePath, bufLen, stmt->session->errorPath->errorPathBuf) != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy ERPathBuf.");
        goto EXIT;
    }

    if (strcpy_s(stmt->session->errorPath->errorPathBuf, QRY_ERROR_PATH_PAYLOAD_MAX_LEN, "/") != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy root path.");
        goto EXIT;
    }

    if (strcat_s(stmt->session->errorPath->errorPathBuf, QRY_ERROR_PATH_PAYLOAD_MAX_LEN, nodeName) != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "concat node name path.");
        goto EXIT;
    }

    if (strcat_s(stmt->session->errorPath->errorPathBuf, QRY_ERROR_PATH_PAYLOAD_MAX_LEN, tmpNodeNamePath) != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "concat temp node name path.");
        goto EXIT;
    }
    DbDynMemCtxFree(stmt->session->memCtx, tmpNodeNamePath);
    return;

EXIT:
    DbDynMemCtxFree(stmt->session->memCtx, tmpNodeNamePath);  // tmpNodeNamePath 是栈变量不用置空
    FreeErrorPath(stmt->session);
}

static Status QryVerifySinglePropertyOperation(
    QryStmtT *stmt, DmVertexT *deltaVertex, DmVertexT *verifyVertex, bool isCreateNew, uint32_t propId)
{
    char *propeName = NULL;
    Status ret = DmVertexGetPropNameById(deltaVertex, propId, &propeName);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint8_t opType;
    ret = DmVertexGetPropOpTypesById(deltaVertex, propId, &opType);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 如果校验的是新创建的节点，则操作类型不能是删除
    // (如果是删除已经在外层函数continue)
    if (isCreateNew) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "delete a non-existent property, opType = %" PRIu32 ", labelName=%s, propName=%s.", (uint32_t)opType,
            deltaVertex->vertexDesc->labelName, propeName);
        stmt->errorFieldName = propeName;
        stmt->errorCode = DATA_EXCEPTION_YANG_DELETE_NO_EXIST_FIELD;
        return GMERR_DATA_EXCEPTION;
    }
    if (opType == (uint8_t)DM_PROPERTY_OPERATION_CREATE && DmVertexPropeIsSetValue(verifyVertex, propId)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "create an existent property, propeId= %" PRIu32 ", labelName=%s, propName=%s.", propId,
            deltaVertex->vertexDesc->labelName, propeName);
        stmt->errorFieldName = propeName;
        stmt->errorCode = DATA_EXCEPTION_YANG_CREATE_EXIST_FIELD;
        return GMERR_DATA_EXCEPTION;
    }
    if (DmIsPkPropOfDesc(deltaVertex->vertexDesc, propId) &&
        (opType == (uint8_t)DM_PROPERTY_OPERATION_DELETE || opType == (uint8_t)DM_PROPERTY_OPERATION_REMOVE)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "delete key property, propeId= %" PRIu32 ", labelName=%s, propName=%s.", propId,
            deltaVertex->vertexDesc->labelName, propeName);
        stmt->errorFieldName = propeName;
        stmt->errorCode = DATA_EXCEPTION_YANG_INDEX_CONFLICT;
        return GMERR_DATA_EXCEPTION;
    }
    if (opType == (uint8_t)DM_PROPERTY_OPERATION_DELETE && !DmVertexPropeIsSetValue(verifyVertex, propId)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "delete a non-existent property, propeId= %" PRIu32 ", labelName=%s, propName=%s.", propId,
            deltaVertex->vertexDesc->labelName, propeName);
        stmt->errorFieldName = propeName;
        stmt->errorCode = DATA_EXCEPTION_YANG_DELETE_NO_EXIST_FIELD;
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

static Status QryVerifyPropertyOperation4Vertex(
    QryStmtT *stmt, DmVertexT *deltaVertex, DmVertexT *verifyVertex, bool isCreateNew, bool *isAllNoneOperation)
{
    uint32_t propeNum = DmVertexGetPropNum(deltaVertex, true);
    if (!DmIsYangVertexDesc(deltaVertex->vertexDesc)) {
        return GMERR_OK;
    }
    Status ret = GMERR_OK;
    uint8_t opType;
    for (uint32_t i = 0; i < propeNum; i++) {
        ret = DmVertexGetPropOpTypesById(deltaVertex, i, &opType);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (opType == (uint8_t)DM_PROPERTY_OPERATION_NONE) {
            continue;
        }
        *isAllNoneOperation = false;

        // 如果校验的是新创建的节点，则仅需校验删除操作类型不能是删除
        if (isCreateNew && opType != (uint8_t)DM_PROPERTY_OPERATION_DELETE) {
            continue;
        }
        ret = QryVerifySinglePropertyOperation(stmt, deltaVertex, verifyVertex, isCreateNew, i);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status QryVerifyPropertyOperation4NodeNoRecursion(
    QryStmtT *stmt, DmNodeT *deltaNode, DmNodeT *verifyNode, bool *isAllNoneOperation)
{
    char *propeName = NULL;
    uint32_t propeNum = DmNodeGetPropeNum(deltaNode);
    for (uint32_t i = 0; i < propeNum; i++) {
        Status ret = DmNodeGetPropNameById(deltaNode, i, &propeName);
        if (ret != GMERR_OK) {
            return ret;
        }
        uint8_t opType;
        ret = DmNodeGetPropOpTypesById(deltaNode, i, &opType);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (opType == (uint8_t)DM_PROPERTY_OPERATION_NONE) {
            continue;
        }
        *isAllNoneOperation = false;

        // 存在的node不存在的字段的删除报错，none不存在的node删除字段报错
        if (opType == (uint8_t)DM_PROPERTY_OPERATION_DELETE &&
            (!DmNodePropeIsSetValue(verifyNode, i) || !DmNodeIsCreated(verifyNode))) {
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
                "delete a non-existent property, propeId= %" PRIu32 ", nodeName=%s, propName=%s.", i,
                deltaNode->nodeDesc->name, propeName);
            stmt->errorFieldName = propeName;
            stmt->errorCode = DATA_EXCEPTION_YANG_DELETE_NO_EXIST_FIELD;
            QryPackageNodeNamePath(stmt, deltaNode);
            return GMERR_DATA_EXCEPTION;
        }

        // none不存在的node  create|merge|replace 字段报错
        if (deltaNode->deltaOpType == DM_NODE_NONE && !DmNodeIsCreated(verifyNode) &&
            (opType == (uint8_t)DM_PROPERTY_OPERATION_CREATE || opType == (uint8_t)DM_PROPERTY_OPERATION_MERGE ||
                opType == (uint8_t)DM_PROPERTY_OPERATION_REPLACE)) {
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
                "create|merge|replace property when none not exist node, propeId= %" PRIu32 " propType=%" PRIu32
                ", nodeName=%s, propName=%s.",
                i, opType, deltaNode->nodeDesc->name, propeName);
            QryPackageNodeNamePath(stmt, deltaNode);
            return GMERR_DATA_EXCEPTION;
        }

        if (deltaNode->deltaOpType != DM_NODE_REPLACE_GRAPH && opType == (uint8_t)DM_PROPERTY_OPERATION_CREATE &&
            DmNodePropeIsSetValue(verifyNode, i) && DmNodeIsCreated(verifyNode)) {
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
                "create an existent property, propeId= %" PRIu32 ", nodeName=%s, propName=%s.", i,
                deltaNode->nodeDesc->name, propeName);
            stmt->errorFieldName = propeName;
            stmt->errorCode = DATA_EXCEPTION_YANG_CREATE_EXIST_FIELD;
            QryPackageNodeNamePath(stmt, deltaNode);
            return GMERR_DATA_EXCEPTION;
        }
    }
    return GMERR_OK;
}

bool IsValidNoneProperOperation(uint8_t opType)
{
    return opType == (uint8_t)DM_PROPERTY_OPERATION_NONE || opType == (uint8_t)DM_PROPERTY_OPERATION_REMOVE;
}

bool IsValidNoneNodeOperation(DmNodeOpTypeE opType)
{
    return opType == DM_NODE_NONE || opType == DM_NODE_REMOVE_GRAPH;
}

static void QryPackagePathByNodeOperation(QryStmtT *stmt, DmNodeT *deltaNode)
{
    if (DmIsYangNoneNoExistNoPackageOperation(deltaNode->deltaOpType)) {
        return;
    }
    QryPackageNodeNamePath(stmt, deltaNode);
}

static Status QryCheckNoneNodeOperationIsValidInner(QryStmtT *stmt, DmNodeT *deltaNode, uint32_t i)
{
    char *propeName = NULL;
    Status ret = DmNodeGetPropNameById(deltaNode, i, &propeName);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint8_t opType;
    ret = RecordGetPropOpTypesById(deltaNode->currRecord, i, &opType);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (!IsValidNoneProperOperation(opType)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "None property op=%" PRIu32 " is not valid.", (uint32_t)opType);
        if (opType == (uint8_t)DM_PROPERTY_OPERATION_DELETE) {
            stmt->errorFieldName = propeName;
            stmt->errorCode = DATA_EXCEPTION_YANG_DELETE_NO_EXIST_FIELD;
        }
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

Status QryCheckNoneNodeOperationIsValid(QryStmtT *stmt, DmNodeT *deltaNode)
{
    DB_POINTER2(stmt, deltaNode);
    Status ret;
    uint32_t propeNum = DmNodeGetPropeNum(deltaNode);
    for (uint32_t i = 0; i < propeNum; i++) {
        ret = QryCheckNoneNodeOperationIsValidInner(stmt, deltaNode, i);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    for (uint32_t i = 0; i < deltaNode->nodeDesc->nodeNumPerElement; i++) {
        if (!DmNodeIsCreated(deltaNode->currNodes[i])) {
            continue;
        }
        // vector node多操作处理
        for (uint32_t j = 0; j < deltaNode->currNodes[i]->realElementNum; j++) {
            ret = DmNodeSetElementIndex(deltaNode->currNodes[i], j);
            if (ret != GMERR_OK) {
                return ret;
            }
            // 切换到当前element 的操作类型
            deltaNode->currNodes[i]->deltaOpType = deltaNode->currNodes[i]->opArray[j];
            if (!IsValidNoneNodeOperation(deltaNode->currNodes[i]->deltaOpType)) {
                QryPackagePathByNodeOperation(stmt, deltaNode->currNodes[i]);
                DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "None node op=%" PRIu32 " is not valid",
                    (uint32_t)deltaNode->currNodes[i]->deltaOpType);
                return GMERR_DATA_EXCEPTION;
            }
            ret = QryCheckNoneNodeOperationIsValid(stmt, deltaNode->currNodes[i]);
            if (ret != GMERR_OK) {
                QryPackageNodeNamePath(stmt, deltaNode->currNodes[i]);
                return ret;
            }
        }
    }
    return GMERR_OK;
}

static Status QryCheckNoneNodeIsValid(QryStmtT *stmt, DmVertexT *deltaVertex)
{
    for (uint32_t i = 0; i < deltaVertex->vertexDesc->nodeNum; i++) {
        if (!DmNodeIsCreated(deltaVertex->nodes[i])) {
            continue;
        }
        // vector node多操作处理
        for (uint32_t j = 0; j < deltaVertex->nodes[i]->realElementNum; j++) {
            Status ret = DmNodeSetElementIndex(deltaVertex->nodes[i], j);
            if (ret != GMERR_OK) {
                return ret;
            }
            // 切换到当前element 的操作类型
            deltaVertex->nodes[i]->deltaOpType = deltaVertex->nodes[i]->opArray[j];
            if (!IsValidNoneNodeOperation(deltaVertex->nodes[i]->deltaOpType)) {
                QryPackagePathByNodeOperation(stmt, deltaVertex->nodes[i]);
                DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
                    "None node op=%" PRIu32 "is not valid, labelName=%s, propName=%s.",
                    (uint32_t)deltaVertex->nodes[i]->deltaOpType, deltaVertex->vertexDesc->labelName,
                    deltaVertex->nodes[i]->nodeDesc->name);
                return GMERR_DATA_EXCEPTION;
            }
            ret = QryCheckNoneNodeOperationIsValid(stmt, deltaVertex->nodes[i]);
            if (ret != GMERR_OK) {
                QryPackageNodeNamePath(stmt, deltaVertex->nodes[i]);
                return ret;
            }
        }
    }
    return GMERR_OK;
}

Status QryCheckNoneVertexOperationIsValid(QryStmtT *stmt, DmVertexT *deltaVertex, uint8_t *buf, uint32_t len)
{
    DB_POINTER3(stmt, deltaVertex, buf);
    Status ret = DmDeSerialize2ExistsVertex(buf, len, deltaVertex, DmGetCheckMode());
    if (ret != GMERR_OK) {
        return ret;
    }
    DmVertexDescT *vertexDesc = deltaVertex->vertexDesc;
    uint32_t propeNum = DmVertexGetPropNum(deltaVertex, true);
    uint8_t opType;
    char *propeName = NULL;
    for (uint32_t i = 0; i < propeNum; i++) {
        ret = DmVertexGetPropNameById(deltaVertex, i, &propeName);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = RecordGetPropOpTypesById(deltaVertex->record, i, &opType);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (DmIsPkPropOfDesc(vertexDesc, i) && opType == (uint8_t)DM_PROPERTY_OPERATION_REMOVE) {
            stmt->errorFieldName = propeName;
            stmt->errorCode = DATA_EXCEPTION_YANG_DELETE_NO_EXIST_FIELD;
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "None property op is not valid, labelName=%s, propName=%s.",
                deltaVertex->vertexDesc->labelName, propeName);
            return GMERR_DATA_EXCEPTION;
        }
        if (!IsValidNoneProperOperation(opType)) {
            if (opType == (uint8_t)DM_PROPERTY_OPERATION_DELETE) {
                stmt->errorFieldName = propeName;
                stmt->errorCode = DATA_EXCEPTION_YANG_DELETE_NO_EXIST_FIELD;
            }
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "None property op is not valid, labelName=%s, propName=%s.",
                deltaVertex->vertexDesc->labelName, propeName);
            return GMERR_DATA_EXCEPTION;
        }
    }
    return QryCheckNoneNodeIsValid(stmt, deltaVertex);
}

static Status ProcessSingleNodeOperation(QryStmtT *stmt, DmNodeT *deltaNode, DmNodeT *oldNode, DmVertexT *oldVertex)
{
    Status ret;
    if (deltaNode->deltaOpType == DM_NODE_DELETE_GRAPH || deltaNode->deltaOpType == DM_NODE_REMOVE_GRAPH) {
        ret = QryDeleteNodeChildGraph(stmt, oldNode, oldVertex);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "delete node graph! node name:%s", deltaNode->nodeDesc->name);
            return ret;
        }
        // 清除老的node记录
        DmClearNode(oldNode);
        return GMERR_OK;
    }

    // replace 先删除节点子树以及节点本身，之后再把当前层 deltaNode 的属性拷贝过去
    if (deltaNode->deltaOpType == DM_NODE_REPLACE_GRAPH) {
        ret = QryDeleteNodeChildGraph(stmt, oldNode, oldVertex);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "replace node graph! node name:%s", deltaNode->nodeDesc->name);
            return ret;
        }
        DmClearNode(oldNode);
    }

    // 如果对不存在的node节点做none操作，则不合并record属性
    if (deltaNode->deltaOpType == DM_NODE_NONE && !oldNode->isCreated) {
        return GMERR_OK;
    }

    if (deltaNode->deltaOpType == DM_NODE_INSERT || deltaNode->deltaOpType == DM_NODE_MERGE ||
        deltaNode->deltaOpType == DM_NODE_NONE || deltaNode->deltaOpType == DM_NODE_REPLACE_GRAPH) {
        // 此处merge只合并当前层node的record属性，孩子节点的属性不处理，待后面递归进入时处理，因为孩子节点可能为其他的操作。
        ret = DmMergeNodeRecord(deltaNode, oldNode);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "create node! node name:%s", deltaNode->nodeDesc->name);
            return ret;
        }
    }
    return GMERR_OK;
}

static Status ProcessDeleteCaseNode(QryStmtT *stmt, const DmNodeT *oldChoice, DmVertexT *oldVertex, uint32_t caseIndex)
{
    if (!DmIsChoiceYangInfo(oldChoice->nodeDesc->yangInfoDesc)) {
        return GMERR_OK;
    }

    for (uint32_t i = 0; i < oldChoice->nodeDesc->nodeNumPerElement; i++) {
        if (i == caseIndex) {
            continue;
        }
        if (DmNodeIsCreated(oldChoice->currNodes[i])) {
            Status ret = QryDeleteNodeChildGraph(stmt, oldChoice->currNodes[i], oldVertex);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "delete case graph, node name:%s", oldChoice->nodeDesc->name);
                return ret;
            }
            DmClearNode(oldChoice->currNodes[i]);
        }
    }
    return GMERR_OK;
}

typedef struct NodeOperationParam {
    DmNodeT *deltaNode;
    DmNodeT *oldNode;
    DmVertexT *oldVertex;
    bool isExist;
    bool isNoneNoExist;
} NodeOperationParamT;

static Status ProcessTreeNodeOperation(
    QryStmtT *stmt, NodeOperationParamT *opParam, bool *isInvalid, bool *isAllNoneOperation);

static Status ProcessChildTreeNodeOperation(
    QryStmtT *stmt, NodeOperationParamT *opParam, bool *isInvalid, bool *isAllNoneOperation)
{
    // 递归处理子节点的其他操作
    for (uint32_t i = 0; i < opParam->deltaNode->nodeDesc->nodeNumPerElement; i++) {
        if (!DmNodeIsCreated(opParam->deltaNode->currNodes[i])) {
            continue;
        }
        Status ret = GMERR_OK;
        // vector node多操作处理
        for (uint32_t j = 0; j < opParam->deltaNode->currNodes[i]->realElementNum; j++) {
            ret = DmNodeSetElementIndex(opParam->deltaNode->currNodes[i], j);
            if (ret != GMERR_OK) {
                return ret;
            }
            // 切换到当前element 的操作类型
            opParam->deltaNode->currNodes[i]->deltaOpType = opParam->deltaNode->currNodes[i]->opArray[j];

            DmNodeT *node = NULL;
            ret = DmNodeGetChildNodeByIndex(opParam->oldNode, i, &node);
            if (ret != GMERR_OK) {
                return ret;
            }
            NodeOperationParamT childOpParam = {.deltaNode = opParam->deltaNode->currNodes[i],
                .oldNode = node,
                .oldVertex = opParam->oldVertex,
                .isExist = opParam->isExist,
                .isNoneNoExist = opParam->isNoneNoExist};
            bool isInvalidTmp = false;
            ret = ProcessTreeNodeOperation(stmt, &childOpParam, &isInvalidTmp, isAllNoneOperation);
            if (ret != GMERR_OK) {
                QryPackageNodeNamePath(stmt, opParam->deltaNode);
                return ret;
            }
            if (isInvalidTmp && !*isInvalid) {
                QryPackageNodeNamePath(stmt, opParam->deltaNode);
                *isInvalid = true;
            }
        }
        if (DmNodeIsCreated(opParam->oldNode->currNodes[i])) {
            ret = ProcessDeleteCaseNode(stmt, opParam->oldNode, opParam->oldVertex, i);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return GMERR_OK;
}

static Status ProcessTreeNodeOperation(
    QryStmtT *stmt, NodeOperationParamT *opParam, bool *isInvalid, bool *isAllNoneOperation)
{
    Status ret = CheckNodeOpIsInvalid(
        stmt, opParam->deltaNode->deltaOpType, opParam->oldNode->isCreated, opParam->isNoneNoExist);
    if (ret != GMERR_OK) {
        // 如果是none不存在的父节点，对当前节点执行create|merge|replace操作，则当前node的路径不处理
        if (!(opParam->isNoneNoExist && DmIsYangNoneNoExistNoPackageOperation(opParam->deltaNode->deltaOpType))) {
            QryPackageNodeNamePath(stmt, opParam->deltaNode);
        }
        DB_LOG_ERROR(ret, "Node op type is incorrect! labelName:%s nodeName:%s, optype: %" PRIu32 ".",
            opParam->oldVertex->vertexDesc->labelName, opParam->deltaNode->nodeDesc->name,
            (uint32_t)opParam->deltaNode->deltaOpType);
        return ret;
    }

    if (opParam->deltaNode->deltaOpType != DM_NODE_NONE) {
        *isAllNoneOperation = false;
    }

    ret = QryVerifyPropertyOperation4NodeNoRecursion(stmt, opParam->deltaNode, opParam->oldNode, isAllNoneOperation);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = ProcessSingleNodeOperation(stmt, opParam->deltaNode, opParam->oldNode, opParam->oldVertex);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 如果当前节点未被创建，而当前节点的操作类型为none，说明none了不存在的空节点
    opParam->isNoneNoExist = (opParam->deltaNode->deltaOpType == DM_NODE_NONE && !opParam->oldNode->isCreated);
    ret = ProcessChildTreeNodeOperation(stmt, opParam, isInvalid, isAllNoneOperation);
    if (ret != GMERR_OK || *isInvalid) {
        return ret;
    }
    return GMERR_OK;
}

Status QryNoExistNodeMergeOperation(QryStmtT *stmt, DmVertexT *vertex, DmVertexT *newVertex,
    QryVertexDataT *modifedVertexData, DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(stmt, vertex);
    if (!DmIsYangVertexLabel(vertexLabel)) {
        return GMERR_OK;
    }
    bool isAllNoneOperation = true;  // 仅用于 none 操作，加在这里只是为了保证函数正确传参，实际并不会用到
    Status ret = QryProcessVertexAndTreeNodeOperation(stmt, false, vertex, newVertex, &isAllNoneOperation);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 以新的vertex的内容代替merge的内容，modifedVertexData中的vertexBuf不用关注其申请释放，只是做赋值即可
    // seriBuf申请的内存会挂在newVertex上，释放newVertex时释放seriBuf的内存
    uint8_t *seriBuf = NULL;
    uint32_t seriLength = 0;
    ret = DmSerializeVertex(newVertex, &seriBuf, &seriLength);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "serialize vertex, label_name=%s", vertexLabel->metaVertexLabel->topRecordName);
        return ret;
    }
    // 将处理后buf再重新反序列化到vertex中，后续建边流程可能会更新buf
    ret = DmDeSerialize2ExistsVertex(seriBuf, seriLength, vertex, DmGetCheckMode());
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "deSerialize vertex, label_name=%s", vertexLabel->metaVertexLabel->topRecordName);
        return ret;
    }
    modifedVertexData->vertexBuf.str = (char *)seriBuf;
    modifedVertexData->vertexBuf.len = seriLength;
    return GMERR_OK;
}

Status QryProcessVertexAndTreeNodeOperation(
    QryStmtT *stmt, bool isExist, DmVertexT *deltaVertex, DmVertexT *oldVertex, bool *isAllNoneOperation)
{
    DB_POINTER3(stmt, deltaVertex, oldVertex);
    *isAllNoneOperation = true;
    // 校验 vertex 的字段五原语操作有效性
    Status ret = QryVerifyPropertyOperation4Vertex(stmt, deltaVertex, oldVertex, !isExist, isAllNoneOperation);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmMergeVertexRecord(deltaVertex, oldVertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "merge vertex record property, label=%s", oldVertex->vertexDesc->labelName);
        return ret;
    }
    bool isInvalid = false;
    for (uint32_t i = 0; i < deltaVertex->vertexDesc->nodeNum; i++) {
        if (!DmNodeIsCreated(deltaVertex->nodes[i])) {
            continue;
        }
        // vector node多操作处理
        for (uint32_t j = 0; j < deltaVertex->nodes[i]->realElementNum; j++) {
            ret = DmNodeSetElementIndex(deltaVertex->nodes[i], j);
            if (ret != GMERR_OK) {
                return ret;
            }
            // 切换到当前element 的操作类型
            deltaVertex->nodes[i]->deltaOpType = deltaVertex->nodes[i]->opArray[j];

            DmNodeT *node = NULL;
            ret = DmVertexGetChildNodeByIndex(oldVertex, i, &node);
            if (ret != GMERR_OK) {
                return ret;
            }
            // 能走进函数的逻辑中，目前只用merge不存在的点和none存在的节点
            NodeOperationParamT opParam = {.deltaNode = deltaVertex->nodes[i],
                .oldNode = node,
                .oldVertex = oldVertex,
                .isExist = isExist,
                .isNoneNoExist = false};
            ret = ProcessTreeNodeOperation(stmt, &opParam, &isInvalid, isAllNoneOperation);
            if (ret != GMERR_OK) {
                return ret;
            }
            if (isInvalid) {
                DmVertexLabelT *label = ((QryCursorT *)stmt->qryCursor)->labelCursors[0].labelDef.vertexLabel;
                QryBuildErrorPath(stmt, GMERR_DATA_EXCEPTION, NULL, label, QRY_YANG_ERROR_PATH_NOT_EXIST);
            }
        }
    }
    return GMERR_OK;
}

Status QryExecuteNoneOpeationForFound(
    QryStmtT *stmt, const QryLabelT *qryLabel, QryLabelCursorT *labelCursor, QryVertexDataT *vertexData)
{
    TupleBufMove(labelCursor->tupleBuf, &labelCursor->idxCtx->tupleBuf);
    labelCursor->heapTuple.heapTupleBuf = TupleBufGet(labelCursor->tupleBuf);
    Status ret = DmDeSerialize2ExistsVertex((uint8_t *)vertexData->vertexBuf.str, vertexData->vertexBuf.len,
        labelCursor->vertexNode->vertex, DmGetCheckMode());
    if (ret != GMERR_OK) {
        return ret;
    }
    QryObjectMapNodeT *vertexNode = NULL;
    ret = QryAllocAndDeSerializeVertex(stmt->session->objectMap, labelCursor->heapTuple.heapTupleBuf.buf,
        labelCursor->heapTuple.heapTupleBuf.bufSize, qryLabel->def.vertexLabel->metaCommon.metaId, &vertexNode);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = QryGenerateNoneFilterTree(stmt, labelCursor);
    if (ret != GMERR_OK) {
        QryReleaseEmptyVertex(vertexNode);
        return ret;
    }

    bool isAllNoneOperation = true;
    ret = QryProcessVertexAndTreeNodeOperation(
        stmt, true, labelCursor->vertexNode->vertex, vertexNode->vertex, &isAllNoneOperation);
    if (ret != GMERR_OK) {
        QryReleaseEmptyVertex(vertexNode);
        return ret;
    }
    if (isAllNoneOperation) {
        // 所有节点和字段都传的是 none 操作，没有对数据做修改，不用进一步操作
        ret = QryInsertYangMap(stmt, vertexNode->vertex, labelCursor->addr);
        QryReleaseEmptyVertex(vertexNode);
        return ret;
    }
    QryTupleInfoT tupleInfo = {0};
    tupleInfo.vertex = vertexNode->vertex;
    ret = QryUpdateHeapTuple(stmt, labelCursor, &tupleInfo);
    if (ret != GMERR_OK) {
        QryReleaseEmptyVertex(vertexNode);
        return ret;
    }
    // 更新二级索引
    for (uint32_t i = 0; i < labelCursor->secIdxNum; i++) {
        ret = QryUpdateSecondIndex(stmt, labelCursor, tupleInfo.vertex, labelCursor->secIdxCtx[i]);
        if (ret != GMERR_OK) {
            QryReleaseEmptyVertex(vertexNode);
            return ret;
        }
    }
    ret = QryInsertYangMap(stmt, vertexNode->vertex, labelCursor->addr);
    QryReleaseEmptyVertex(vertexNode);
    return ret;
}

static Status QryAllocAndInitInputInfo(QryStmtT *stmt, const QryLabelT *qryLabel, DmYangVertexInputInfoT **inputInfo)
{
    DB_POINTER(stmt->session->yangNoneMap->memCtx);
    Status ret = QryAllocMem(stmt->session->yangNoneMap->memCtx, sizeof(DmYangVertexInputInfoT), (char **)inputInfo);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "input info.");
        return ret;
    }
    (*inputInfo)->tmpVertexId = stmt->yangVertexTmpId;
    (*inputInfo)->tmpVertexPid = stmt->yangVertexTmpPId;
    char *metaName = qryLabel->def.vertexLabel->metaCommon.metaName;
    if (strcpy_s((*inputInfo)->vertexLabelName, DM_YANG_MAX_LABEL_NAME, metaName) != EOK) {
        DbDynMemCtxFree(stmt->session->yangNoneMap->memCtx, inputInfo);
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "copy vertex label name.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static void QryRecordNoneMap(QryStmtT *stmt, const QryLabelT *qryLabel, DmIndexKeyT *indexKey)
{
    if (stmt->session->yangNoneMap == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Yang none map.");
        return;
    }
    DmYangVertexInputInfoT *inputInfo = NULL;
    Status result = QryAllocAndInitInputInfo(stmt, qryLabel, &inputInfo);
    if (result != GMERR_OK) {
        return;
    }
    if (DmIsRootYangVertexLabel(qryLabel->def.vertexLabel)) {
        result = DbOamapInsert(stmt->session->yangNoneMap, qryLabel->def.vertexLabel->metaCommon.metaId,
            &inputInfo->tmpVertexId, inputInfo, NULL);
        if (result != GMERR_OK) {
            DbDynMemCtxFree(stmt->session->yangNoneMap->memCtx, inputInfo);
            DB_LOG_ERROR(result, "Oamap insert at get related label");
        }
        return;
    }

    DmVlIndexLabelT *pkIndex = qryLabel->def.vertexLabel->metaVertexLabel->pkIndex;
    uint8_t *keyData = indexKey->keyBuf;
    DmValueT propertyValues[DM_MAX_KEY_PROPE_NUM] = {0};
    result = DmGetPropeValuesFromKeyBuf(keyData, pkIndex, propertyValues);
    if (result != GMERR_OK) {
        DbDynMemCtxFree(stmt->session->yangNoneMap->memCtx, inputInfo);
        DB_LOG_ERROR(result, "get property values buffer, index = %s.", pkIndex->indexName);
        return;
    }
    result = PackagePropertiesIntoPath(stmt, pkIndex, inputInfo->inputKey, DM_YANG_MAX_INPUT_KEY_SIZE, propertyValues);
    if (result != GMERR_OK) {
        DbDynMemCtxFree(stmt->session->yangNoneMap->memCtx, inputInfo);
        DB_LOG_ERROR(result, "package properties to path");
        return;
    }
    result = DbOamapInsert(stmt->session->yangNoneMap, qryLabel->def.vertexLabel->metaCommon.metaId,
        &inputInfo->tmpVertexId, inputInfo, NULL);
    if (result != GMERR_OK) {
        DbDynMemCtxFree(stmt->session->yangNoneMap->memCtx, inputInfo);
        DB_LOG_ERROR(result, "Oamap insert at get related label");
        return;
    }
}

static Status QryCheckNoneValid(
    QryStmtT *stmt, const QryLabelT *qryLabel, QryLabelCursorT *labelCursor, QryVertexDataT *vertexData)
{
    Status ret = QryCheckNoneVertexOperationIsValid(
        stmt, labelCursor->vertexNode->vertex, (uint8_t *)vertexData->vertexBuf.str, vertexData->vertexBuf.len);
    if (ret == GMERR_OK) {
        // 记录yangNoneMap
        QryRecordNoneMap(stmt, qryLabel, vertexData->indexKey.leftValue.value);
        return GMERR_OK;
    }
    stmt->errorCode = DATA_EXCEPTION_YANG_INDEX_NOT_EXIST;
    DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "execute none op, labelName=%s, key is not found",
        qryLabel->def.vertexLabel->metaVertexLabel->topRecordName);
    return GMERR_DATA_EXCEPTION;
}

Status QryExecuteNoneVertexInner(QryStmtT *stmt, const QryLabelT *qryLabel, QryLabelCursorT *labelCursor)
{
    QryVertexParamT *execParams = (QryVertexParamT *)stmt->execParams;

    QryVertexDataT *vertexData = &execParams->vertexData[0];
    DmIndexKeyT *indexKey = vertexData->indexKey.leftValue.value;
    // yang树模型中用实际的PID值替换临时PID值
    Status ret = QryReplaceYangIndexPid(qryLabel->def.vertexLabel, stmt, indexKey);
    if (ret != GMERR_OK) {
        if (!stmt->isParentNotExisted) {
            return ret;
        }
        return QryCheckNoneValid(stmt, qryLabel, labelCursor, vertexData);
    }

    // 查点
    IndexKeyT hashKey = {0};
    DmIndexKeyGetKeyBuf(indexKey, (uint8_t **)&hashKey.keyData, &hashKey.keyLen);
    bool isFound = false;
    ret = IdxLookup(labelCursor->idxCtx, hashKey, &labelCursor->addr, &isFound);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (!isFound) {
        return QryCheckNoneValid(stmt, qryLabel, labelCursor, vertexData);
    }
    return QryExecuteNoneOpeationForFound(stmt, qryLabel, labelCursor, vertexData);
}

Status QryExecuteNoneVertex(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryNoneVertexDescT *noneDesc = (QryNoneVertexDescT *)stmt->context->entry;
    QryLabelT *qryLabel = noneDesc->qryLabel;
    if (qryLabel->def.vertexLabel == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "execute none op, vLabel is NULL.");
        return GMERR_DATA_EXCEPTION;
    }
    if (stmt->session->yangMap == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "None op no in yang batch.");
        return GMERR_DATA_EXCEPTION;
    }

    QryLabelCursorT *labelCursor = &((QryCursorT *)stmt->qryCursor)->labelCursors[0];
    labelCursor->action = HEAP_OPTYPE_NONE_OP;  // none操作不会影响冲突性校验，使用特殊的枚举值

    Status ret = QryOpenVertexLabelCursor(stmt, qryLabel, labelCursor, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "open vLabelCursor, label index=%" PRIu32 "", qryLabel->labelIdx);
        return ret;
    }

    ret = QryExecuteNoneVertexInner(stmt, qryLabel, labelCursor);
    if (ret != GMERR_OK) {
        DmVertexLabelT *vertexLabel = qryLabel->def.vertexLabel;
        QryBuildErrorPath(stmt, ret, NULL, vertexLabel, QRY_YANG_ERROR_PATH_NOT_EXIST);
    }

    QryCloseVertexLabelCursor(labelCursor);
    return ret;
}

static Status SetLeafListPidValue(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, QryVertexDataT *vertexData, DmVertexT *vertex)
{
    DmValueT vertexPid = {0};
    Status ret = DmVertexBufGetFirstLevelFixedPropById(
        (uint8_t *)(vertexData->vertexBuf.str), vertex->vertexDesc, DM_YANG_PID_PROPE_SUBSCRIPT, &vertexPid);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get yang tmp PID property value, label: %s.",
            labelCursor->labelDef.vertexLabel->metaVertexLabel->topRecordName);
        return ret;
    }
    uint32_t tmpPidValue = 0;
    ret = QryReplaceYangPidPropVal(stmt->session->yangMap, labelCursor->labelDef.vertexLabel, &vertexPid, &tmpPidValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 把临时PID保存起来，之后如果需要建边的话会用到
    stmt->yangVertexTmpPId = tmpPidValue;

    return DmVertexSetPropeById(DM_YANG_PID_PROPE_SUBSCRIPT, vertexPid, vertex);
}

static Status CheckIsDefaultLeafListCreated(QryStmtT *stmt, QryLabelCursorT *labelCursor, QryVertexDataT *vertexData,
    DmPropertySchemaT *property, bool *isInserted)
{
    DmVertexT *vertex = labelCursor->vertexNode->vertex;
    Status ret = SetLeafListPidValue(stmt, labelCursor, vertexData, vertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 用第一个默认值进行查询
    ret = DmVertexSetPropeById(DM_YANG_LEAFLIST_KEYPROP_SUBSCRIPT, property->defaultValue[0], vertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    IndexKeyT hashKey = {0};
    ret =
        DmGetKeyBufFromVertex(vertex, labelCursor->labelDef.vertexLabel->metaVertexLabel->pkIndex->idxLabelBase.indexId,
            &(hashKey.keyData), &(hashKey.keyLen));
    if (ret != GMERR_OK) {
        return ret;
    }
    return IdxLookup(labelCursor->idxCtx, hashKey, &labelCursor->addr, isInserted);
}

static Status GetLeafListVertexBuf(QryStmtT *stmt, DmVertexT *vertex, TextT *cachedVertexBuf, TextT *vertexBuf)
{
    uint32_t len;
    Status ret = DmVertexGetSeriBufLength(vertex, &len);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get vertex seri buf len ");
        return ret;
    }

    if (len > cachedVertexBuf->len) {
        if (cachedVertexBuf->str != NULL) {
            DbDynMemCtxFree(stmt->memCtx, cachedVertexBuf->str);
        }
        // 释放原则：在QryProcessDefaultLeafList，调用QryFreeStmtMem(stmt, cachedVertexBuf.str)释放
        ret = QryAllocMem(stmt->memCtx, len, &(cachedVertexBuf->str));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "allocate vertex buf");
            return ret;
        }
        cachedVertexBuf->len = len;
    }

    vertexBuf->len = len;
    vertexBuf->str = cachedVertexBuf->str;
    return GMERR_OK;
}

static Status CreateAllDefaultLeafListVertexes(QryStmtT *stmt, QryLabelCursorT *labelCursor, DmVertexT *vertex,
    TextT *cachedVertexBuf, DmPropertySchemaT *property)
{
    QryVertexDataT tmpVertexData = {0};
    tmpVertexData.posInfo.pos = DM_LIST_STAY;
    tmpVertexData.autoOperateFlag = QRY_AUTO_INSERT_IN_EDGE;
    labelCursor->posInfo = &(tmpVertexData.posInfo);

    Status ret;
    bool isFixed = labelCursor->labelDef.vertexLabel->metaVertexLabel->labelLevel == VERTEX_LEVEL_SIMPLE;
    if (isFixed) {
        // 简单定长表只需计算和申请一次 vertex buf
        ret = GetLeafListVertexBuf(stmt, vertex, cachedVertexBuf, &(tmpVertexData.vertexBuf));
        if (ret != GMERR_OK) {
            goto EXIT;
        }
    }

    // 构造 leaf-list 默认值节点的三个属性并执行插入，其中 PID 属性已在前面查点时构造好了
    ret = DmSetLeafListDefaultFlagsToVertex(vertex, true, true);  // 构造 ID 属性
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set default flags to vertex");
        goto EXIT;
    }

    // 循环构造除 PID 外另一个主键属性，并执行插入
    for (uint32_t i = 0; i < property->defaultValueNum; ++i) {
        ret = DmVertexSetPropeById(DM_YANG_LEAFLIST_KEYPROP_SUBSCRIPT, property->defaultValue[i], vertex);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "set default property, vertex index=%u.", i);
            goto EXIT;
        }
        if (!isFixed) {
            ret = GetLeafListVertexBuf(stmt, vertex, cachedVertexBuf, &(tmpVertexData.vertexBuf));
            if (ret != GMERR_OK) {
                goto EXIT;
            }
        }
        ret = DmSerializeVertex2InvokerBuf(vertex, tmpVertexData.vertexBuf.len, (uint8_t *)tmpVertexData.vertexBuf.str);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "serialize default vertex, vertex index=%u.", i);
            goto EXIT;
        }
        labelCursor->action = HEAP_OPTYPE_REPLACE_INSERT;
        ret = QryInsertCommonVertex(stmt, labelCursor, &tmpVertexData);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "insert default leaf-list vertex, vertex index=%u.", i);
            goto EXIT;
        }
    }
EXIT:
    labelCursor->posInfo = NULL;
    return ret;
}

Status QryProcessDefaultLeafList(QryStmtT *stmt, QryLabelCursorT *labelCursor, QryVertexDataT *vertexData)
{
    DB_POINTER3(stmt, labelCursor, vertexData);
    DmVertexLabelT *vertexLabel = labelCursor->labelDef.vertexLabel;
    DmPropertySchemaT *property = NULL;
    Status ret =
        DmSchemaGetPropeById(vertexLabel->metaVertexLabel->schema, DM_YANG_LEAFLIST_KEYPROP_SUBSCRIPT, &property);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get property schema, label=%s.", vertexLabel->metaCommon.metaName);
        return ret;
    }
    if (property->defaultValue == NULL) {
        return GMERR_OK;  // 若此 leaf-list 没有默认值定义，则不用自动创建默认值节点
    }

    DmVertexT *vertex = labelCursor->vertexNode->vertex;
    if (vertex == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_UNEXPECTED_NULL_VALUE, "Vertex in labelCursor is NULL, label=%s.", vertexLabel->metaCommon.metaName);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    // 检查是否已创建过默认值节点
    bool isInserted;
    ret = CheckIsDefaultLeafListCreated(stmt, labelCursor, vertexData, property, &isInserted);
    if (ret != GMERR_OK || isInserted) {
        return ret;
    }

    // 若还未创建默认值节点，需要预先创建出所有的默认值节点
    TextT cachedVertexBuf = {0};
    ret = CreateAllDefaultLeafListVertexes(stmt, labelCursor, vertex, &cachedVertexBuf, property);
    QryFreeStmtMem(stmt, cachedVertexBuf.str);
    DmClearVertex(vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create default leaf-list, label=%s.", vertexLabel->metaCommon.metaName);
    }
    return ret;
}

Status QryUpdateDefaultLeafListVertex(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, bool isSetByServer, bool isEqualsDefault)
{
    DmVertexLabelT *vertexLabel = labelCursor->labelDef.vertexLabel;
    HeapTupleBufT *defaultVertexBuf = &(labelCursor->heapTuple.heapTupleBuf);
    DmVertexT *vertex = labelCursor->vertexNode->vertex;
    if (vertex == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_UNEXPECTED_NULL_VALUE, "Vertex in labelCursor is NULL, label=%s.", vertexLabel->metaCommon.metaName);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    // 将旧 vertex buf 中的标志位更新为想要的值
    Status ret =
        DmSetLeafListDefaultFlagsToVertexBuf(defaultVertexBuf->buf, vertex->vertexDesc, isSetByServer, isEqualsDefault);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "set leaf-list default flags, label=%s.", vertexLabel->metaCommon.metaName);
        return ret;
    }

    // 只有在移动位置的场合才需要把旧数据反序列化到 vertex 当中，因为后面会用到 vertex，而不移动位置的话则只会用到 buf
    if (labelCursor->posInfo != NULL && labelCursor->posInfo->pos != DM_LIST_STAY) {
        ret = DmDeSerialize2ExistsVertex(defaultVertexBuf->buf, defaultVertexBuf->bufSize, vertex, DmGetCheckMode());
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "deserialize default leaf-list vertex, label=%s.", vertexLabel->metaCommon.metaName);
            return ret;
        }
    }

    // 直接用设置好新标志位的旧数据 buf 进行更新
    QryTupleInfoT tupleInfo;
    tupleInfo.vertex = vertex;
    tupleInfo.heapTupleBuf.buf = defaultVertexBuf->buf;
    tupleInfo.heapTupleBuf.bufSize = defaultVertexBuf->bufSize;
    MsgOpcodeRpcE opCodeBackUp = stmt->context->opCode;
    stmt->context->opCode = MSG_OP_RPC_MERGE_VERTEX;  // 暂时变更 opCode 为 merge，插边时针对不同 opCode 处理不同
    ret = QryExecuteAutoUpdateEdge(stmt, labelCursor, &tupleInfo, QRY_AUTO_INSERT_IN_EDGE);
    stmt->context->opCode = opCodeBackUp;
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "update default leaf-list vertex, label=%s.", vertexLabel->metaCommon.metaName);
        return ret;
    }
    return QryGenerateMergeFilterTree(stmt, labelCursor, vertex);
}

Status UpdataDefaultLeafVertexInHeap(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, bool isSetByServer, bool isEqualsDefault)
{
    DB_POINTER2(stmt, labelCursor);
    // 插入新增默认值场景
    // isSetByServer 标志位为true，说明已经有该默认值数据了，报错
    if (isSetByServer) {
        DB_LOG_AND_SET_LASERR(GMERR_PRIMARY_KEY_VIOLATION, "The leaf-list vertex already exists, label=%s",
            labelCursor->labelDef.vertexLabel->metaCommon.metaName);
        return GMERR_PRIMARY_KEY_VIOLATION;
    }
    DB_ASSERT(isEqualsDefault == false);
    // isSetByServer标志位为false，说明已有用户插入的数据，转入更新流程isEqualsDefault更新为true
    return QryUpdateDefaultLeafListVertex(stmt, labelCursor, isSetByServer, true);
}

Status UpdataLeafVertexInHeap(QryStmtT *stmt, QryLabelCursorT *labelCursor, const DmVertexT *vertex, bool isDefault)
{
    DB_POINTER3(stmt, labelCursor, vertex);
    // 检查存储层中数据的 isSetByServer 标志位情况
    TupleBufMove(labelCursor->tupleBuf, &labelCursor->idxCtx->tupleBuf);
    labelCursor->heapTuple.heapTupleBuf = TupleBufGet(labelCursor->tupleBuf);
    bool isSetByServer;
    bool isEqualsDefault;
    Status ret = DmGetLeafListDefaultFlagsFromVertexBuf(
        labelCursor->heapTuple.heapTupleBuf.buf, vertex->vertexDesc, &isSetByServer, &isEqualsDefault);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "get leaf-list default flags, label=%s.", labelCursor->labelDef.vertexLabel->metaCommon.metaName);
        return ret;
    }
    if (isDefault) {
        return UpdataDefaultLeafVertexInHeap(stmt, labelCursor, isSetByServer, isEqualsDefault);
    }
    // isSetByServer 标志位为 false，说明已经有一条用户插入的相同主键的数据了
    if (!isSetByServer) {
        DB_LOG_AND_SET_LASERR(GMERR_PRIMARY_KEY_VIOLATION, "The leaf-list vertex already existed, label=%s",
            labelCursor->labelDef.vertexLabel->metaCommon.metaName);
        return GMERR_PRIMARY_KEY_VIOLATION;
    }
    // isSetByServer 标志位为 true，说明有一条自动创建的主键相同的默认数据
    // 此时不报错，并转入更新流程，更新 isSetByServer 标志位为 false
    return QryUpdateDefaultLeafListVertex(stmt, labelCursor, false, isEqualsDefault);
}

Status QryProcessInsertLeafList(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, DmVertexT *vertex, QryVertexDataT *vertexData, bool isDefault)
{
    DB_POINTER4(stmt, labelCursor, vertex, vertexData);

    // 查询存储层中是否已有相同主键的 leaf-list（可能是服务端自动创建，也可能是之前用户创建）
    Status ret =
        DmGetKeyBufFromVertex(vertex, labelCursor->labelDef.vertexLabel->metaVertexLabel->pkIndex->idxLabelBase.indexId,
            (uint8_t **)&labelCursor->pkValue.keyData, &labelCursor->pkValue.keyLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    bool isFound = false;
    ret = IdxLookup(labelCursor->idxCtx, labelCursor->pkValue, &labelCursor->addr, &isFound);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (!isFound) {
        labelCursor->action = HEAP_OPTYPE_REPLACE_INSERT;
        return QryInsertSingleVertexCore(stmt, labelCursor, vertexData, false);  // 存储层无数据直接走原来插入流程
    }
    return UpdataLeafVertexInHeap(stmt, labelCursor, vertex, isDefault);
}

Status QryProcessMergeReplaceLeafList(QryStmtT *stmt, QryLabelCursorT *labelCursor)
{
    DB_POINTER2(stmt, labelCursor);
    bool isSetByServer;
    bool isEqualsDefault;
    Status ret = DmGetLeafListDefaultFlagsFromVertexBuf(labelCursor->heapTuple.heapTupleBuf.buf,
        labelCursor->vertexNode->vertex->vertexDesc, &isSetByServer, &isEqualsDefault);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(
            ret, "get leaf-list default flags, label=%s.", labelCursor->labelDef.vertexLabel->metaCommon.metaName);
        return ret;
    }
    // 将 isSetByServer 标志位更新为 false，isEqualsDefault 标志位需保持原样不能更改
    return QryUpdateDefaultLeafListVertex(stmt, labelCursor, false, isEqualsDefault);
}

Status QryProcessDeleteLeafList4Upgrade(QryStmtT *stmt, QryLabelCursorT *labelCursor)
{
    DB_POINTER2(stmt, labelCursor);
    // 检查要删点的 isSetByServer 标志位情况
    bool isSetByServer;
    bool isEqualsDefault;
    Status ret = DmGetLeafListDefaultFlagsFromVertexBuf(labelCursor->heapTuple.heapTupleBuf.buf,
        labelCursor->vertexNode->vertex->vertexDesc, &isSetByServer, &isEqualsDefault);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "get leaf-list default flags, label=%s.", labelCursor->labelDef.vertexLabel->metaCommon.metaName);
        return ret;
    }
    // 此节点是默认值，走删除流程
    if (isSetByServer) {
        ret = QryExecuteDeleteVertexInfo(stmt, labelCursor);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = QryExecuteAutoDeleteEdge(stmt, labelCursor, QRY_AUTO_DEL_RELATION_VERTEX);
        if (ret != GMERR_OK) {
            return ret;
        }
        return GMERR_OK;
    }

    // isSetByServer为 false，说明此节点是用户创建的，转入更新流程，更新 isEqualsDefault 标志位为 false
    return QryUpdateDefaultLeafListVertex(stmt, labelCursor, isSetByServer, false);
}

Status QryProcessDeleteLeafList(QryStmtT *stmt, QryLabelCursorT *labelCursor, bool *needDelete)
{
    DB_POINTER3(stmt, labelCursor, needDelete);

    // 检查要删点的 isSetByServer 标志位情况
    bool isSetByServer;
    bool isEqualsDefault;
    Status ret = DmGetLeafListDefaultFlagsFromVertexBuf(labelCursor->heapTuple.heapTupleBuf.buf,
        labelCursor->vertexNode->vertex->vertexDesc, &isSetByServer, &isEqualsDefault);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "get leaf-list default flags, label=%s.", labelCursor->labelDef.vertexLabel->metaCommon.metaName);
        return ret;
    }
    // isSetByServer 标志位为 true，说明此节点是服务端自动创建而非用户创建，对这样的点进行 delete 操作需要报错
    if (isSetByServer) {
        if (stmt->context->type == QRY_TYPE_REMOVE_GRAPH) {
            *needDelete = false;
            return GMERR_OK;
        }
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "The leaf-list vertex is not created by user and cannot be deleted, label=%s",
            labelCursor->labelDef.vertexLabel->metaCommon.metaName);
        return GMERR_DATA_EXCEPTION;
    }

    // 检查 isEqualsDefault 标志位情况
    if (!isEqualsDefault) {
        *needDelete = true;
        return GMERR_OK;  // 不等于默认值的节点，继续走原来删除流程
    }

    // 等于默认值的节点，转入更新流程，更新 isSetByServer 标志位为 true
    *needDelete = false;
    return QryUpdateDefaultLeafListVertex(stmt, labelCursor, true, isEqualsDefault);
}

Status InitYangAutoGeneratedProperty(QryStmtT *stmt, DmVertexT *vertex, DmVertexLabelT *vertexLabel)
{
    DB_POINTER3(stmt, vertex, vertexLabel);
    Status ret = GMERR_OK;
    if (DmIsListVertexLabel(vertexLabel) && !DmIsLeafListVertexLabel(vertexLabel)) {
        (void)QryYangAddRecycleTask(vertexLabel);
    }
    if (DmIsLeafListVertexLabel(vertexLabel)) {
        // 初始化 leaf-list 默认值标志位信息（复用 yang 的 ID 字段来表示）
        ret = DmSetLeafListDefaultFlagsToVertex(vertex, false, false);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    if (!(DiffModeIsOpen(stmt) && stmt->session->yangTreeCtx->diffCtx->hasRemoveOp)) {
        return DmVertexSetAutoIncProp(vertex, vertexLabel);
    }
    // diff 先删除后创建场景下需要恢复自增id保证能找到同一个filterTree
    IndexKeyT idx;
    ret = DmGetKeyBufFromVertex(
        vertex, vertexLabel->metaVertexLabel->pkIndex->idxLabelBase.indexId, &idx.keyData, &idx.keyLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t *vertexId = QryFindVertexIdFromRemoveMap(stmt, &idx, vertexLabel->metaCommon.metaId);
    if (vertexId == NULL) {
        return DmVertexSetAutoIncProp(vertex, vertexLabel);
    }
    DmValueT value;
    value.type = DB_DATATYPE_UINT32;
    value.value.uintValue = *vertexId;
    return DmVertexSetPropeById(DM_YANG_ID_PROPE_SUBSCRIPT, value, vertex);
}

static Status InitVertexAutoIncPropByExistVertexBuf(
    QryLabelCursorT *labelCursor, DmVertexT *vertex, const DmVertexLabelT *vertexLabel)
{
    if (vertexLabel->commonInfo->autoIncrPropNum == 0) {
        return GMERR_OK;
    }
    uint8_t *buf = labelCursor->idxCtx->tupleBuf.buf;
    DmValueT vertexId = {0};
    Status ret = DmVertexBufGetFirstLevelFixedPropById(
        buf, labelCursor->labelDef.vertexLabel->vertexDesc, DM_YANG_ID_PROPE_SUBSCRIPT, &vertexId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "get vertex id, vLabel:%s", labelCursor->labelDef.vertexLabel->metaVertexLabel->topRecordName);
        return ret;
    }
    return DmVertexSetPropeById(DM_YANG_ID_PROPE_SUBSCRIPT, vertexId, vertex);
}

Status QryYangReplaceDealWithAutoProp(QryStmtT *stmt, QryLabelCursorT *labelCursor, bool isFound)
{
    DB_POINTER2(stmt, labelCursor);
    Status ret = GMERR_OK;
    DmVertexLabelT *vertexLabel = labelCursor->labelDef.vertexLabel;
    DmVertexT *vertex = labelCursor->vertexNode->vertex;
    // yang场景下为了在replace场景下ID值保持不变，如果已经存在的vertex通过已有的ID设置自增列
    // 如果不存在则查询是否同一个事务已经已经操作过，否则自动生成
    if (DiffModeIsOpen(stmt)) {
        if (!isFound && stmt->session->yangTreeCtx->diffCtx->hasRemoveOp) {
            IndexKeyT idx = {0};
            ret = DmGetKeyBufFromVertex(
                vertex, vertexLabel->metaVertexLabel->pkIndex->idxLabelBase.indexId, &idx.keyData, &idx.keyLen);
            if (ret != GMERR_OK) {
                return ret;
            }
            uint32_t *vertexId = QryFindVertexIdFromRemoveMap(stmt, &idx, vertexLabel->metaCommon.metaId);
            if (vertexId != NULL) {
                DmValueT val;
                val.type = DB_DATATYPE_UINT32;
                val.value.uintValue = *vertexId;
                ret = DmVertexSetPropeById(DM_YANG_ID_PROPE_SUBSCRIPT, val, vertex);
            }
        } else if (isFound) {
            ret = InitVertexAutoIncPropByExistVertexBuf(labelCursor, vertex, vertexLabel);
        }
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

#ifdef __cplusplus
}
#endif
