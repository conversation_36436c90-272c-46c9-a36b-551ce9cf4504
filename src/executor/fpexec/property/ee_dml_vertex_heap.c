/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: Implementation of vertex DML executor
 * Author: lingfeng
 * Create: 2020-11-20
 */

#include "ee_dml_vertex.h"
#include "ee_dml_query.h"
#include "ee_diff_generate.h"
#include "ee_dml_index.h"
#include "ee_key_cmp.h"
#include "ee_dml_subs.h"
#include "ee_check.h"
#include "db_memcpy.h"
#include "ee_tuplebuf_array.h"
#include "ee_dml_vertex_heap_batch.h"
#include "ee_feature_import.h"
#include "ee_statistic.h"
#include "se_heap.h"
#include "ee_error_path.h"
#include "ee_dml_sync.h"
#include "ee_dml_edge.h"
#include "ee_pubsub_merge.h"
#include "ee_dcl_ctrl.h"
#include "dm_meta_topo_label.h"
#include "dm_meta_prop_label.h"
#include "container_access.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

// 校验表锁中是否存在此版本号
bool QrySchemaVersionExistCheck(const DmVertexLabelT *vertexLabel, uint32_t uuid)
{
    DB_POINTER(vertexLabel);
    bool checkSuccess = false;
    (void)DmIsSchemaVersionValid(vertexLabel, uuid, &checkSuccess);
    if (SECUREC_UNLIKELY(!checkSuccess)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_TABLE,
            "The uuid(%" PRIu32 ") can not match an existing vertexLabel, the label version does not exist, label:%s.",
            uuid, vertexLabel->metaCommon.metaName);
    }
    return checkSuccess;
}

Status QryDeleteTupleAndIdx(QryStmtT *stmt, QryLabelCursorT *cursor)
{
    DB_POINTER2(stmt, cursor);
    Status ret = GMERR_OK;
    /* 轻量化事务场景下会先删除HashCluster */
    // 聚簇容器二级索引若为HashCluster由SE删除
    if (!cursor->isChLabel) {
        ret = QryRemoveHcIndexes(stmt, cursor);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }

    ret = QryDeleteHeapTuple(stmt, cursor);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return QryRemoveAllIndexes(stmt, cursor);
}

Status QryExecuteDeleteVertexInfo(QryStmtT *stmt, QryLabelCursorT *cursor)
{
    DB_POINTER2(stmt, cursor);
    Status ret = GMERR_OK;
    if (cursor->labelDef.vertexLabel->commonInfo->resColInfo != NULL) {
        ret = QryInitResData(stmt, cursor->labelDef.vertexLabel);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }

#ifdef FEATURE_REPLICATION
    ret = QryReplicateDeleteVertex(stmt->session->logBuf, cursor->heapTuple.heapTupleBuf, cursor->vertexNode->vertex,
        cursor->labelDef.vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
#endif

    // Generate subscription data before row data is deleted, otherwise, the path cannot be queried.
    DmSubsEventE eventType =
        ((cursor->heapTuple.dataStatus & QRY_OLD_DATA_STATUS_AGE) != 0) ? DM_SUBS_EVENT_AGED : DM_SUBS_EVENT_DELETE;
    const bool isGenerateSubs =
        IsGenerateVertexSubsMessages(cursor->labelDef.vertexLabel, eventType);  // 判断是否需要生成订阅消息
    if (isGenerateSubs) {
        QrySubsRowDataSetT rowDataSet;
        rowDataSet.rowsDataNum = 1;
        rowDataSet.rowsData[0] = (QrySubsRowDataT){.oldTupleBuf = cursor->heapTuple.heapTupleBuf};
        ret = QryGenerateVertexSubsMessageByRow(stmt, cursor->labelDef.vertexLabel, &rowDataSet, eventType);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(
                ret, "Generate subs message, labelName:%s.", cursor->labelDef.vertexLabel->metaCommon.metaName);
            goto EXIT;
        }
    }

    // already release res when mark delete
    if (!IsStatusMergeMarkDelete(
            cursor->labelDef.vertexLabel, &cursor->heapTuple.heapTupleBuf, cursor->labelDef.vertexLabel->vertexDesc)) {
        ret = QryFreeRes(stmt, cursor);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            goto EXIT;
        }
    }

    ret = QryDeleteTupleAndIdx(stmt, cursor);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "delete heap tuple, labelName:%s.", cursor->labelDef.vertexLabel->metaCommon.metaName);
        goto EXIT;
    }

EXIT:
    if (cursor->labelDef.vertexLabel->commonInfo->resColInfo != NULL) {
        QryUnInitResData(stmt, cursor->labelDef.vertexLabel);
    }
    return ret;
}

static Status QryExecuteInsertTupleAndIndexes(
    QryStmtT *stmt, const TextT *vertexBuf, QryLabelCursorT *labelCursor, const bool isReplay, uint8_t *partitionId)
{
    HeapTupleBufT heapTupleBuf = {0};
    heapTupleBuf.bufSize = vertexBuf->len;
    heapTupleBuf.buf = (uint8_t *)vertexBuf->str;
    Status ret;
    // check need maintain object version
    if (SECUREC_UNLIKELY(QryCheckIsSyncVertexLabel(labelCursor->labelDef.vertexLabel))) {
        if (!isReplay) {
            ret = QrySustainDataSyncVersion(stmt, labelCursor->labelDef.vertexLabel, &heapTupleBuf);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }

    ret = QryInsertHeapTuple(stmt, labelCursor, &heapTupleBuf, partitionId);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (labelCursor->isChLabel) {
        ret = QryInsertSecondIndexesByTupleBuf(labelCursor, &heapTupleBuf, labelCursor->addr);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        ret = QryInsertAllIndexesByTupleBuf(labelCursor, &heapTupleBuf, labelCursor->addr);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if (DmVertexLabelIsNormalLabel(labelCursor->labelDef.vertexLabel)) {
        QryUpdateStatics(QRY_TUPLE_NULL, QRY_TUPLE_NORMAL, QryGetStatisticValue(labelCursor, *partitionId));
    }
    return GMERR_OK;
}

Status QryInsertSingleVertexCoreGenerateSubsMessage(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, QryVertexDataT *vertexData, bool isMerge, uint8_t *partitionId)
{
    Status ret =
        QryExecuteInsertTupleAndIndexes(stmt, &vertexData->vertexBuf, labelCursor, vertexData->isReplay, partitionId);
    if (ret != GMERR_OK) {
        return ret;
    }

    DmSubsEventE eventType = DM_SUBS_EVENT_INSERT;
    if (labelCursor->action == HEAP_OPTYPE_REPLACE_INSERT && !labelCursor->isIns2Rep) {
        eventType = isMerge ? DM_SUBS_EVENT_MERGE_INSERT : DM_SUBS_EVENT_REPLACE_INSERT;
    }

    TupleT tuple = {
        .buf = (uint8_t *)vertexData->vertexBuf.str,
        .bufSize = vertexData->vertexBuf.len,
    };

#ifdef FEATURE_REPLICATION
    ret = QryReplicateReplaceVertex(stmt->session->logBuf, tuple, labelCursor->labelDef.vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "insert single vertex in replication.");
        return ret;
    }
#endif

    // 判断是否需要生成订阅消息
    if (IsGenerateVertexSubsMessages(labelCursor->labelDef.vertexLabel, eventType)) {
        QrySubsRowDataSetT rowDataSet;
        rowDataSet.rowsDataNum = 1;
        rowDataSet.rowsData[0] = (QrySubsRowDataT){0};
        rowDataSet.rowsData[0].newTupleBuf = tuple;
        rowDataSet.rowsData[0].syncDataVersionNo = DB_MAX_UINT64;
        return QryGenerateVertexSubsMessageByRow(stmt, labelCursor->labelDef.vertexLabel, &rowDataSet, eventType);
    }
    return GMERR_OK;
}

Status QryInsertSingleVertexCore(QryStmtT *stmt, QryLabelCursorT *labelCursor, QryVertexDataT *vertexData, bool isMerge)
{
    uint8_t partitionId = 0;
    Status ret = QryInsertSingleVertexCoreGenerateSubsMessage(stmt, labelCursor, vertexData, isMerge, &partitionId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    if (labelCursor->labelDef.vertexLabel->commonInfo->edgeLabelNum != 0) {
        ret = ContainerResetOpType(labelCursor->containerHdl, HEAP_OPTYPE_UPDATE, false);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "reset op type, vertexLabel=%s.", labelCursor->labelDef.vertexLabel->metaCommon.metaName);
            return ret;
        }
    }
    // YANG建边场景性能优化，暂时把序列化buf保存到labelCursor上，后续建边时直接拷贝并进行修改，不重新序列化vertex
    bool isBufNull = (labelCursor->heapTuple.heapTupleBuf.buf == NULL);
    if (isBufNull) {
        labelCursor->heapTuple.heapTupleBuf.buf = (uint8_t *)vertexData->vertexBuf.str;
        labelCursor->heapTuple.heapTupleBuf.bufSize = vertexData->vertexBuf.len;
    }

    // 可能在buf里更新了checkVersion，需要重新反序列化
    DmCheckInfoT *checkInfo = QryGetCheckInfoByIdForServer(labelCursor->labelDef.vertexLabel, partitionId);
    if (checkInfo->version.checkStatus == DM_CHECK_STATUS_CHECKING) {
        ret = DmDeSerialize2ExistsVertex(
            (uint8_t *)vertexData->vertexBuf.str, vertexData->vertexBuf.len, labelCursor->vertexNode->vertex, false);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "deserialize, vertexLabel=%s.", labelCursor->labelDef.vertexLabel->metaCommon.metaName);
            return ret;
        }
    }

    ret = QryExecuteAutoInsertEdge(stmt, labelCursor, labelCursor->vertexNode->vertex, vertexData->autoOperateFlag);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (isBufNull) {
        labelCursor->heapTuple.heapTupleBuf.buf = NULL;
    }
    return ret;
}

static Status QryAutoGeneratedPropertiesForNormal(
    DmVertexT *vertex, DmVertexLabelT *vertexLabel, QryVertexDataT *vertexItem)
{
    DB_POINTER3(vertex, vertexLabel, vertexItem);

    Status ret = DmVertexSetAutoIncProp(vertex, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set AutoIncProp, vertexLabel=%s", vertexLabel->metaCommon.metaName);
        return ret;
    }
    if (DmVertexIsDelta(vertex)) {
        ret =
            DmMergeVertexWithDefaultBuffer(vertex, (uint8_t **)&vertexItem->vertexBuf.str, &vertexItem->vertexBuf.len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "merge vertex, vertexLabel=%s", vertexLabel->metaCommon.metaName);
            return ret;
        }
        ret = DmDeSerialize2ExistsVertex(
            (uint8_t *)vertexItem->vertexBuf.str, vertexItem->vertexBuf.len, vertex, DmServerGetCheckMode());
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "serialize vertex, vertexLabel=%s", vertexLabel->metaCommon.metaName);
            return ret;
        }
    } else if (vertex->vertexDesc->hasBitMapProp || vertexLabel->commonInfo->resColInfo != NULL ||
               vertexLabel->commonInfo->autoIncrPropNum > 0) {
        ret = DmSerializeVertex(vertex, (uint8_t **)&vertexItem->vertexBuf.str, &vertexItem->vertexBuf.len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "serialize vertex, vertexLabel=%s", vertexLabel->metaCommon.metaName);
            return ret;
        }
    }
    return GMERR_OK;
}

Status QryFillUpAutoGeneratedProperties(
    QryStmtT *stmt, DmVertexT *vertex, QryLabelCursorT *labelCursor, QryVertexDataT *vertexItem)
{
    Status ret = DmCheckBitMapConstrict(vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "Not support bitmap, vertexLabel=%s.", labelCursor->labelDef.vertexLabel->metaCommon.metaName);
        return ret;
    }
    ret = QryAllocRes(stmt, labelCursor, vertex);
    if (ret != GMERR_OK) {
        return ret;
    }

    DmVertexLabelT *vertexLabel = labelCursor->labelDef.vertexLabel;
    return QryAutoGeneratedPropertiesForNormal(vertex, vertexLabel, vertexItem);
}

Status QryInsertSingleVertexInner(QryStmtT *stmt, QryLabelCursorT *labelCursor, const QryVertexDataT *vertexItem,
    DmVertexT *vertex, DbResAckHeaderT *resAckHeader)
{
    Status ret;

    QryVertexDataT modifedVertexItem = *vertexItem;
    ret = QryFillUpAutoGeneratedProperties(stmt, vertex, labelCursor, &modifedVertexItem);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryInsertSingleVertexCore(stmt, labelCursor, &modifedVertexItem, false);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = QryMakeRespForDmlOperationByRes(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    (resAckHeader->affectRows)++;
    return ret;
}

Status QryInsertSingleVertex(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, QryVertexDataT *vertexItem, DbResAckHeaderT *resAckHeader)
{
    DmVertexT *vertex = labelCursor->vertexNode->vertex;
    Status ret = DmDeSerialize2ExistsVertex(
        (uint8_t *)vertexItem->vertexBuf.str, vertexItem->vertexBuf.len, vertex, DmServerGetCheckMode());
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "deserialize.");
        return ret;
    }
    return QryInsertSingleVertexInner(stmt, labelCursor, vertexItem, vertex, resAckHeader);
}

inline static bool QryIsSupportBatchInsert(QryLabelCursorT *labelCursor, const QryVertexParamT *insertParams)
{
    uint32_t bufSizeLimit = 0;
    for (uint32_t i = 0, cnt = insertParams->count; i < cnt; ++i) {
        if (bufSizeLimit < insertParams->vertexData[i].vertexBuf.len) {
            bufSizeLimit = insertParams->vertexData[i].vertexBuf.len;
        }
    }
    return HeapLabelIsExceedBatchInsertSizeLimit(labelCursor->hpRunHdl, bufSizeLimit);
}

inline static bool QryIsNeedCheckVertex(const DmVertexLabelT *vertexLabel)
{
    return !vertexLabel->metaVertexLabel->schema->isSuperFieldFull;
}

// 传入的buf未进行反序列化则进行buf长度校验
static Status QryCheckVertexLabelLen(QryLabelCursorT *labelCursor, const QryVertexParamT *params)
{
    DmVertexLabelT *vertexLabel = labelCursor->labelDef.vertexLabel;
    DmVertexLabelT *oldVertexLabel = NULL;
    Status ret = CataGetOldestVertexLabelById(vertexLabel->metaCommon.metaId, &oldVertexLabel, NULL);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "obtain the vertex label %s", vertexLabel->metaCommon.metaName);
        return ret;
    }
    for (uint32_t i = 0; i < params->count; i++) {
        QryVertexDataT *vertexItem = &params->vertexData[i];
        if (vertexLabel->metaVertexLabel->isFixedLabel) {
            uint32_t newFixLen = DmGetFixVertexLabelLen(vertexLabel);
            if (SECUREC_UNLIKELY(vertexItem->vertexBuf.len > newFixLen)) {
                ret = GMERR_DATA_EXCEPTION;
                DB_LOG_AND_SET_LASERR(ret,
                    "vertex %s is fixed Label, vertex buf len is %" PRIu32 ", which should be less than %" PRIu32 "",
                    vertexLabel->metaCommon.metaName, vertexItem->vertexBuf.len, newFixLen);
                // 释放该元数据
                (void)CataReleaseVertexLabel(oldVertexLabel);
                return ret;
            }
        }

        if (oldVertexLabel->metaVertexLabel->isFixedLabel) {
            uint32_t oldFixLen = DmGetFixVertexLabelLen(oldVertexLabel);
            if (SECUREC_UNLIKELY(vertexItem->vertexBuf.len < oldFixLen)) {
                ret = GMERR_DATA_EXCEPTION;
                DB_LOG_AND_SET_LASERR(ret,
                    "vertex %s is fixed Label, vertex buf len is %" PRIu32 ", which should be more than %" PRIu32 "",
                    vertexLabel->metaCommon.metaName, vertexItem->vertexBuf.len, oldFixLen);
                // 释放该元数据
                (void)CataReleaseVertexLabel(oldVertexLabel);
                return ret;
            }
        }
    }
    // 释放该元数据
    (void)CataReleaseVertexLabel(oldVertexLabel);
    return GMERR_OK;
}

Status QryCheckInsertVertex(
    QryStmtT *stmt, const QryVertexParamT *insertParams, QryLabelCursorT *labelCursor, uint32_t schemaVersion)
{
    Status ret = GMERR_OK;

    bool checkFlag = DmServerGetCheckMode();
    if (SECUREC_UNLIKELY(checkFlag)) {
        ret = QryCheckVertexLabelLen(labelCursor, insertParams);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if (!QryIsNeedCheckVertex(labelCursor->labelDef.vertexLabel)) {
        return GMERR_OK;
    }
    QryObjectMapNodeT *vertexNode = labelCursor->vertexNode;
    char *labelName = labelCursor->labelDef.vertexLabel->metaCommon.metaName;
    if (schemaVersion != labelCursor->labelDef.vertexLabel->metaCommon.version) {
        ret = DmResetVertex(vertexNode->vertex);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "reset vertex, label name = %s.", labelName);
            return ret;
        }
    }
    for (uint32_t i = 0; i < insertParams->count; i++) {
        QryVertexDataT *vertexItem = &insertParams->vertexData[i];
        ret = DmDeSerialize2ExistsVertex(
            (uint8_t *)vertexItem->vertexBuf.str, vertexItem->vertexBuf.len, vertexNode->vertex, checkFlag);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "deserialize vertex, label name = %s.", labelName);
            return ret;
        }
        ret = DmVertexConstraintCheck(vertexNode->vertex);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "check vertex, label name = %s.", labelName);
            return ret;
        }
    }
    return GMERR_OK;
}

static Status QryIncUpdateVertex(
    QryStmtT *stmt, QryLabelCursorT *cursor, const QryUpdateVertexDataT *data, QryTupleInfoT *tupleInfo)
{
    Status ret = QryExecuteAutoUpdateEdge(stmt, cursor, tupleInfo, data->autoOperateFlag);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 更新二级索引
    for (uint32_t i = 0; i < cursor->secIdxNum; i++) {
        // 聚簇容器二级索引若为hashcluster由SE更新
        if (cursor->isChLabel && cursor->secIdxCtx[i]->idxMetaCfg.idxType == HASHCLUSTER_INDEX) {
            continue;
        }
        ret = QryUpdateSecondIndex(stmt, cursor, tupleInfo->vertex, cursor->secIdxCtx[i]);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

static Status QryFullUpdateVertex(
    QryStmtT *stmt, QryLabelCursorT *cursor, const QryUpdateVertexDataT *data, QryTupleInfoT *tupleInfo)
{
    Status ret = QryExecuteAutoUpdateEdge(stmt, cursor, tupleInfo, data->autoOperateFlag);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryUpdateSecondIndexesByTupleBuf(stmt, cursor, &tupleInfo->heapTupleBuf, &cursor->heapTuple.heapTupleBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

Status QryCheckNonChangeableProperty(
    DmVertexLabelT *label, const HeapTupleBufT *oldVertexBuf, const HeapTupleBufT *newVertexBuf)
{
    if (SECUREC_LIKELY(!label->commonInfo->accCheckAddr->isPartition)) {
        return GMERR_OK;
    }
    Status ret = DmCheckIsPartitionNotChange(
        label, oldVertexBuf->buf, oldVertexBuf->bufSize, newVertexBuf->buf, newVertexBuf->bufSize);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_DBG_ERROR(ret, "label %s partition not support change.", label->metaCommon.metaName);
        return ret;
    }
    return GMERR_OK;
}

Status QryGetNewVertexBuf(
    const DmVertexT *deltaVertex, HeapTupleBufT *oldVertexBuf, bool isFixed, HeapTupleBufT *newVertexBuf)
{
    Status ret;
    if (isFixed) {
        DbFastMemcpy(newVertexBuf->buf, newVertexBuf->bufSize, oldVertexBuf->buf, oldVertexBuf->bufSize);
        ret = DmVertexMergeIntoOldBuf(deltaVertex, newVertexBuf->buf);
    } else {
        ret = DmVertexMergeIntoNewBuf(deltaVertex, oldVertexBuf->buf, newVertexBuf->bufSize, newVertexBuf->buf);
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "merge vertex, isFixed=%" PRIu8 ".", (uint8_t)isFixed);
    }
    return ret;
}

// 当stmt过多，若size过大，会导致cachedTupBuf过大，可申请内存急剧下降，
// 且cachedTupBuf主要优化性能场景(fib表70-80 bytes)
// 现设定merge执行结束，size大于QRY_STM_MAX_CACHED_TUP_BUF_LEN时，主动释放cachedTupBuf
inline static void QryFreeCachedTupBufIfLarge(QryStmtT *stmt)
{
    if (stmt->cachedTupBufSize > QRY_STM_MAX_CACHED_TUP_BUF_LEN) {
        DbDynMemCtxFree(stmt->memCtx, stmt->cachedTupBuf);
        stmt->cachedTupBufSize = 0;
        stmt->cachedTupBuf = NULL;
    }
}

static Status QryUseCachedTupBuf(QryStmtT *stmt, uint32_t size, uint8_t **buf)
{
    if (size > stmt->cachedTupBufSize) {
        uint8_t *cachedTupBufLocal;
        Status ret = QryAllocMem(stmt->memCtx, size, (char **)&cachedTupBufLocal);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (stmt->cachedTupBuf != NULL) {
            DbDynMemCtxFree(stmt->memCtx, stmt->cachedTupBuf);
            stmt->cachedTupBufSize = 0;
            stmt->cachedTupBuf = NULL;
        }
        stmt->cachedTupBuf = cachedTupBufLocal;
        stmt->cachedTupBufSize = size;
    }
    *buf = stmt->cachedTupBuf;
    return GMERR_OK;
}

static Status QryMergeNormalVertex(QryStmtT *stmt, const QryUpdateVertexDataT *data, QryLabelCursorT *cursor,
    HeapTupleBufT *oldTupleBuf, QryTupleInfoT *tupleInfo)
{
    Status ret;
    bool isFixed = false;
    HeapTupleBufT *newTupleBuf = &tupleInfo->heapTupleBuf;
    char *labelName = cursor->labelDef.vertexLabel->metaCommon.metaName;
    if (data->label->metaVertexLabel->labelLevel == VERTEX_LEVEL_SIMPLE) {
        isFixed = true;
    } else {
        if ((ret = DmVertexUpdatePropeIsAllFixed(
                 data->deltaVertex->vertexDesc, data->label, data->deltaVertex, &isFixed)) != GMERR_OK) {
            DB_LOG_ERROR(ret, "Some properties is not set, label = %s.", labelName);
            return ret;
        }
    }

    if (isFixed && (!data->label->commonInfo->hasUpd)) {
        newTupleBuf->bufSize = oldTupleBuf->bufSize;
    } else {
        if ((ret = DmVertexGetMergeBufLength(data->deltaVertex->vertexDesc, data->deltaVertex, oldTupleBuf->buf,
                 &newTupleBuf->bufSize)) != GMERR_OK) {
            DB_LOG_ERROR(ret, "get merge buffer's len, label = %s.", labelName);
            return ret;
        }
        isFixed = false;
    }

    if ((ret = QryUseCachedTupBuf(stmt, newTupleBuf->bufSize, &newTupleBuf->buf)) != GMERR_OK) {
        return ret;
    }

    if ((ret = QryGetNewVertexBuf(data->deltaVertex, oldTupleBuf, isFixed, newTupleBuf)) != GMERR_OK) {
        return ret;
    }

    if ((ret = QryCheckNonChangeableProperty(data->label, oldTupleBuf, newTupleBuf)) != GMERR_OK) {
        return ret;
    }

    if ((ret = DmDeSerialize2ExistsVertex(
             newTupleBuf->buf, newTupleBuf->bufSize, tupleInfo->vertex, DmServerGetCheckMode())) != GMERR_OK) {
        DB_LOG_ERROR(ret, "deserialize, vertexLabel=%s.", labelName);
        return ret;
    }

    return DmVertexConstraintCheck(tupleInfo->vertex);
}

Status QryMergeVertex(QryStmtT *stmt, const QryUpdateVertexDataT *data, QryLabelCursorT *cursor,
    HeapTupleBufT *tupleBuf, QryTupleInfoT *tupleInfo)
{
    return QryMergeNormalVertex(stmt, data, cursor, tupleBuf, tupleInfo);
}

Status QryMergeUpdateVertex(QryStmtT *stmt, QryLabelCursorT *cursor, const QryUpdateVertexDataT *data)
{
    DB_POINTER3(stmt, cursor, data);
    HeapTupleBufT *tupleBuf = &cursor->heapTuple.heapTupleBuf;
    QryTupleInfoT tupleInfo = {0};
    DmVertexSetAutoIncProp2Null(data->deltaVertex, data->label);
    QryObjectMapNodeT *vertexNode = NULL;
    Status ret = QryAllocEmptyObject(stmt->session->objectMap, data->label->metaCommon.metaId, &vertexNode);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    tupleInfo.vertex = vertexNode->vertex;
    ret = QryMergeVertex(stmt, data, cursor, tupleBuf, &tupleInfo);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto ERROR;
    }
    // check need maintain syncDataVersion
    if (SECUREC_UNLIKELY(QryCheckIsSyncVertexLabel(cursor->labelDef.vertexLabel))) {
        ret = QryMaintainSyncDataVersion4Update(cursor, data, &tupleInfo.heapTupleBuf, tupleBuf, stmt);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            goto ERROR;
        }
    }
    ret = QryIncUpdateVertex(stmt, cursor, data, &tupleInfo);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto ERROR;
    }
    // 订阅推送更新
    ret = QrySubSendMergeUpdate4Sync(stmt, cursor->heapTuple, data, &tupleInfo, cursor->action);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto ERROR;
    }
    QryReleaseEmptyVertex(vertexNode);
    QryFreeCachedTupBufIfLarge(stmt);
    return ret;

ERROR:
    QryReleaseEmptyVertex(vertexNode);
    return ret;
}

Status QryExecuteUpdateSingleVertex(QryStmtT *stmt, QryCursorT *qryCursor, const QryVertexDataT *vertexData)
{
    QryUpdateVertexDescT *updateCtx = (QryUpdateVertexDescT *)stmt->context->entry;
    QryLabelCursorT *labelCursor = &qryCursor->labelCursors[0];
    QryUpdateVertexDataT data = {.label = updateCtx->query->qryLabel->def.vertexLabel,
        .deltaVertex = labelCursor->vertexNode->vertex,
        .deltaVertexBuf = vertexData->vertexBuf,
        .isNonDeSer = false,
        .autoOperateFlag = vertexData->autoOperateFlag,
        .isReplay = vertexData->isReplay,
        .replayOldVersion = vertexData->replayOldVersion};
    Status ret = QryMergeUpdateVertex(stmt, labelCursor, &data);
    if (ret == GMERR_OK) {
        qryCursor->affectRows++;
    }
    return ret;
}

static inline bool QryIsDeleteVertexError(QryStmtT *stmt, QryCursorT *qryCursor)
{
    return stmt->context->type == QRY_TYPE_DELETE_GRAPH && qryCursor->affectRows == 0;
}

Status QryExecuteUpdateOrDeleteVertexInner(QryStmtT *stmt, QryCursorT *qryCursor, PlanNodeT *plan,
    const QryVertexDataT *vertexData, const QryExecuteSingleVertexFunc func)
{
    DB_POINTER3(stmt, qryCursor, plan);
    Status ret;
    do {
        ret = QryFetch(stmt, qryCursor, plan, &qryCursor->isEof, 0);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        if (qryCursor->isEof) {
            if (QryIsDeleteVertexError(stmt, qryCursor)) {
                stmt->errorCode = DATA_EXCEPTION_YANG_INDEX_NOT_EXIST;
                QryLabelCursorT *labelCursor = &qryCursor->labelCursors[0];
                DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
                    "vertex does not exist when update or delete vertex for label: %s",
                    labelCursor->labelDef.vertexLabel->metaCommon.metaName);
                return GMERR_DATA_EXCEPTION;
            } else {
                break;
            }
        }
        if (SECUREC_UNLIKELY(qryCursor->isOverTime)) {
            if (!stmt->splitInfo.disabled) {
                break;
            }
            continue;
        }
        ret = func(stmt, qryCursor, vertexData);
        if (SECUREC_UNLIKELY(ret != GMERR_OK && ret != SYNC_DATA_CHECK_DROP_ERROR)) {
            return ret;
        }
        if (SECUREC_UNLIKELY(!stmt->splitInfo.disabled &&
                             QryExceedSplitTime(stmt->session->lastStmtReqTimestamp, stmt->session->splitTime))) {
            break;
        }
    } while (true);

    stmt->eof = qryCursor->isEof;
    return GMERR_OK;
}

Status QryCheckUpdateVertex(DmVertexT *deltaVertex, const DmVertexLabelT *vertexLabel)
{
    if (DmVertexIsSetResColPrope(deltaVertex)) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Resource property can not modify.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    if (vertexLabel->metaVertexLabel->pkIndex != NULL) {
        if (DmVertexPkAnyPropeIsSet(deltaVertex)) {
            DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED,
                "Update PK index is not supported. The vertexLabel name is %s.", vertexLabel->metaCommon.metaName);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
    }

    return GMERR_OK;
}

static Status QryExecuteUpdateVertexFirstSplit(QryStmtT *stmt, QryCursorT *qryCursor, PlanNodeT *plan, uint32_t uuid)
{
    DB_POINTER3(stmt, qryCursor, plan);
    Status ret = GMERR_OK;
    if (plan->type == PLAN_NODE_SCAN_VERTEX) {
        ret = QryScanPlanLockLabelIfNeed(stmt, &plan->scanPlan);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    ret = QryExecPlan(stmt, qryCursor, plan, 0);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    QryLabelCursorT *labelCursor = &qryCursor->labelCursors[0];
    QryVertexParamT *updateParams = (QryVertexParamT *)stmt->execParams;
    uint32_t updateNum = updateParams->count;
    if (!QrySchemaVersionExistCheck(labelCursor->labelDef.vertexLabel, uuid)) {
        return GMERR_UNDEFINED_TABLE;
    }

    // 只有范围更新才设置此标记位
    if (plan->type == PLAN_NODE_SCAN_VERTEX && plan->scanPlan.scanMode == SCAN_INDEX_PRIMARY) {
        SeTransSetDmlHint4BatchNum(stmt->session->seInstance, updateNum);
    } else {
        SeTransSetDmlHint4RangeUpdate(stmt->session->seInstance);
    }

    if (DmIsLabelSupportStatusMerge(labelCursor->labelDef.vertexLabel)) {
        ret = QryAppendStMgPubsubDataSet(&stmt->session->stmgSubDataSet, labelCursor->labelDef.vertexLabel);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    return GMERR_OK;
}

void QryResetBatchCursor(QryCursorT *qryCursor, QryLabelCursorT *labelCursor, QryIndexKeyT *indexKey)
{
    qryCursor->isEof = false;
    labelCursor->isEof = false;
    labelCursor->fetchCnt = 0;
    labelCursor->indexKey = indexKey;
}

static Status QryExecuteUpdateLoopBody(QryStmtT *stmt, QryCursorT *qryCursor)
{
    QryUpdateVertexDescT *updateCtx = (QryUpdateVertexDescT *)stmt->context->entry;
    PlanNodeT *plan = updateCtx->plan->updatePlan.next->qryVertexPlan.next;
    QryLabelCursorT *labelCursor = &qryCursor->labelCursors[0];
    uint32_t updatePos = labelCursor->updateSplitPos;
    QryVertexParamT *updateParams = (QryVertexParamT *)stmt->execParams;
    QryVertexDataT *vertexItem = updateParams->vertexData;
    uint32_t updateNum = updateParams->count;
    DbSetCliOpTableName(labelCursor->labelDef.vertexLabel->metaCommon.metaName, 0);
    DmClearVertex(labelCursor->vertexNode->vertex);
    Status ret = DmDeSerialize2ExistsVertex((uint8_t *)vertexItem[updatePos].vertexBuf.str,
        vertexItem[updatePos].vertexBuf.len, labelCursor->vertexNode->vertex, DmServerGetCheckMode());
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = QryCheckUpdateVertex(labelCursor->vertexNode->vertex, labelCursor->labelDef.vertexLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = QryExecuteUpdateOrDeleteVertexInner(
        stmt, qryCursor, plan, &vertexItem[updatePos], QryExecuteUpdateSingleVertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "execute update or delete vertex to label: %s.",
            labelCursor->labelDef.vertexLabel->metaCommon.metaName);
        return ret;
    }

    if (stmt->eof) {
        labelCursor->updateSplitPos++;
        if (labelCursor->updateSplitPos >= updateNum) {
            return GMERR_OK;
        }
        // 分片进来之后不能清理上一次的信息
        QryResetBatchCursor(qryCursor, labelCursor, &vertexItem[labelCursor->updateSplitPos].indexKey);
        ret = QryResetScanParam(labelCursor);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        stmt->eof = false;
        return GMERR_OK;
    }

    return GMERR_OK;
}

static Status QryExecuteUpdateVertexNormal(QryStmtT *stmt)
{
    QryUpdateVertexDescT *updateCtx = (QryUpdateVertexDescT *)stmt->context->entry;
    PlanNodeT *plan = updateCtx->plan->updatePlan.next->qryVertexPlan.next;
    QryCursorT *qryCursor = (QryCursorT *)stmt->qryCursor;
    QryLabelCursorT *labelCursor = &qryCursor->labelCursors[0];
    labelCursor->action = HEAP_OPTYPE_UPDATE;
    QryVertexParamT *updateParams = (QryVertexParamT *)stmt->execParams;
    uint32_t updateNum = updateParams->count;
    Status ret = GMERR_OK;
    if (stmt->splitInfo.isFirst) {
        ret = QryExecuteUpdateVertexFirstSplit(stmt, qryCursor, plan, updateCtx->query->uuid);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            goto ERR;
        }
        labelCursor->updateSplitPos = 0;
        if (updateNum == 0) {
            goto ERR;
        }
    }
    for (;;) {
        ret = QryExecuteUpdateLoopBody(stmt, qryCursor);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            goto ERR;
        }

        if (stmt->eof) {
            break;
        }

        if (SECUREC_UNLIKELY(qryCursor->isOverTime)) {
            break;
        }

        if (SECUREC_UNLIKELY(!stmt->splitInfo.disabled &&
                             QryExceedSplitTime(stmt->session->lastStmtReqTimestamp, stmt->session->splitTime))) {
            break;
        }
    }

    ret = QryMakeRespForDmlOperation(stmt, qryCursor->affectRows);
    if (SECUREC_UNLIKELY(ret != GMERR_OK || stmt->eof)) {
        goto ERR;
    }
    return ret;

ERR:
    QrySetErrorInfoWhenReplay(stmt, ret);
    return ret;
}

Status QryExecuteUpdateVertex(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryUpdateVertexDescT *updateCtx = (QryUpdateVertexDescT *)stmt->context->entry;
    PlanNodeT *plan = updateCtx->plan->updatePlan.next->qryVertexPlan.next;
    if (SECUREC_LIKELY(updateCtx->simpleChLabel)) {
        bool hasUpd = false;
        Status ret = QryExecuteUpdateVertexForChLabel(stmt, plan->scanPlan.qryLabel, &hasUpd);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        // 如果在open cursor后判断有升降级要切换Normal流程处理
        if (SECUREC_LIKELY(!hasUpd)) {
            return GMERR_OK;
        }
        // simpleChLabel为true没有申请cursor，切换Normal流程处理需要申请cursor
        ret = QryAllocCursorWithCond(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return QryExecuteUpdateVertexNormal(stmt);
}

Status QryExecuteRecursionDelVertex(QryStmtT *stmt, DmVertexT *vertex, bool isForceDel, QryLabelCursorT *labelCursor)
{
    DB_POINTER3(stmt, vertex, labelCursor);
    uint32_t edgeLabelId;
    QryCursorT *qryCursor = (QryCursorT *)stmt->qryCursor;
    DmVertexLabelT *vertexLabel = labelCursor->labelDef.vertexLabel;
    Status ret;

    for (uint32_t i = 0; i < vertexLabel->commonInfo->edgeLabelNum; i++) {
        edgeLabelId = vertexLabel->commonInfo->relatedEdgeLabels[i].edgeLabelId;
        ret = QryExecuteDelVertexForSingleEdge(stmt, edgeLabelId, vertex, isForceDel);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    ret = QryExecuteDeleteVertexInfo(stmt, labelCursor);
    if (ret != GMERR_OK) {
        return ret;
    }
    qryCursor->affectRows++;
    return GMERR_OK;
}

Status QryExecuteDelRelationshipVertex(QryStmtT *stmt, QryCursorT *qryCursor, uint32_t autoOperateFlag)
{
    DB_POINTER2(stmt, qryCursor);
    QryLabelCursorT *labelCursor = &qryCursor->labelCursors[0];
    bool isForceDelete = ((autoOperateFlag & QRY_AUTO_DELETE_WEAK_EDGE) != 0);
    QryObjectMapNodeT *vertexNode = NULL;

    Status ret = QryAllocAndDeSerializeVertex(stmt->session->objectMap, labelCursor->heapTuple.heapTupleBuf.buf,
        labelCursor->heapTuple.heapTupleBuf.bufSize, labelCursor->labelDef.vertexLabel->metaCommon.metaId, &vertexNode);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = QryExecuteRecursionDelVertex(stmt, vertexNode->vertex, isForceDelete, labelCursor);
    QryReleaseEmptyVertex(vertexNode);
    return ret;
}

Status QryExecuteDeleteSingleVertex(QryStmtT *stmt, QryCursorT *qryCursor, const QryVertexDataT *vertexData)
{
    DB_POINTER3(stmt, qryCursor, vertexData);
    Status ret;
    QryLabelCursorT *cursor = &qryCursor->labelCursors[0];
    bool isDelAllRelationVertex = ((vertexData->autoOperateFlag & QRY_AUTO_DEL_RELATION_VERTEX) != 0);

    if (cursor->labelDef.vertexLabel->commonInfo->edgeLabelNum == 0) {
        ret = QryExecuteDeleteVertexInfo(stmt, cursor);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        qryCursor->affectRows++;
        return ret;
    }

    if (!isDelAllRelationVertex) {
        ret = QryExecuteDeleteVertexInfo(stmt, cursor);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        ret = QryExecuteAutoDeleteEdge(stmt, cursor, vertexData->autoOperateFlag);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        qryCursor->affectRows++;
        return ret;
    }
    return QryExecuteDelRelationshipVertex(stmt, qryCursor, vertexData->autoOperateFlag);
}

static Status QryReleaseRes4MarkDelete(QryStmtT *stmt, QryLabelCursorT *labelCursor)
{
    DB_POINTER2(stmt, labelCursor);
    if (labelCursor->labelDef.vertexLabel->commonInfo->resColInfo == NULL) {
        return GMERR_OK;
    }

    Status ret = QryInitResData(stmt, labelCursor->labelDef.vertexLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = QryFreeRes(stmt, labelCursor);
    QryUnInitResData(stmt, labelCursor->labelDef.vertexLabel);
    return ret;
}

Status QryExecuteMarkDeleteSingleVertexWithSub(QryStmtT *stmt, QryLabelCursorT *labelCursor)
{
    DB_POINTER2(stmt, labelCursor);

    TupleT tuple = TupleBufGet(labelCursor->tupleBuf);
    DB_ASSERT(tuple.buf == labelCursor->heapTuple.heapTupleBuf.buf);          // 删除
    DB_ASSERT(tuple.bufSize == labelCursor->heapTuple.heapTupleBuf.bufSize);  // 删除

    QryTupleStateE state = QRY_TUPLE_NULL;
    // 直接使用vertexLabel->vertexDesc不合理
    Status ret = QryGetTupleState(tuple, labelCursor->labelDef.vertexLabel, &state);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 用户错误删除两次，返回nodata
    if (state == QRY_TUPLE_STMGGC) {
        DB_LOG_ERROR(GMERR_NO_DATA, "The record has been deleted, vertexLabel=%s.",
            labelCursor->labelDef.vertexLabel->metaCommon.metaName);
        return GMERR_NO_DATA;
    }

    // 生成订阅信息
    QrySubsRowDataSetT rowDataSet = {0};
    rowDataSet.rowsDataNum = 1;
    rowDataSet.rowsData[0].oldTupleBuf = labelCursor->heapTuple.heapTupleBuf;
    DmSubsEventE eventType;
    if ((labelCursor->heapTuple.dataStatus & QRY_OLD_DATA_STATUS_AGE) != 0) {
        eventType = DM_SUBS_EVENT_AGED;
    } else {
        eventType = DM_SUBS_EVENT_DELETE;
    }
    ret = QryGenerateVertexSubsMessageByRow(stmt, labelCursor->labelDef.vertexLabel, &rowDataSet, eventType);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryReleaseRes4MarkDelete(stmt, labelCursor);
    if (ret != GMERR_OK) {
        return ret;
    }

    return QryMarkDeleteHeapTuple(stmt, labelCursor, &labelCursor->heapTuple, labelCursor->addr, state);
}

static Status QrySetEdgeAddrWhenDeleteChildren(
    QryStmtT *stmt, DmVertexT *rootVertex, QryUpdateVertexDataT *data, TextT *graphText)
{
    // 客户端不感知边信息，如果传入buf包含实际建边信息，计算会有问题
    uint32_t replaceBufSize = DmVertexRefreshSeriBufLengthWithEdge(rootVertex, data->deltaVertexBuf.len);
    // 内存释放点:reset stmt->memCtx兜底统一释放
    char *replaceVertexBuf = DbDynMemCtxAlloc(stmt->memCtx, replaceBufSize);
    if (SECUREC_UNLIKELY(replaceVertexBuf == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc replaceVertex Buf.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(replaceVertexBuf, replaceBufSize, 0x00, replaceBufSize);
    Status ret = DmRefreshEdgeWithVertexBuf(rootVertex, (uint8_t *)data->deltaVertexBuf.str, data->deltaVertexBuf.len,
        (uint8_t *)replaceVertexBuf, replaceBufSize);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "serialize edgeAddrs");
        return ret;
    }
    // 替换原有buffer
    graphText->len = replaceBufSize;
    graphText->str = replaceVertexBuf;
    return GMERR_OK;
}

static Status QryDeleteChildrenAndSetEdgeAddr(
    QryStmtT *stmt, DmVertexT *rootVertex, bool isForceDel, QryUpdateVertexDataT *data, TextT *graphText)
{
    // 入边需要保留
    // data->deltaVertexBuf 客户端传入的新的vertex buf
    for (uint32_t i = 0; i < rootVertex->vertexDesc->edgeLabelNum; i++) {
        uint32_t edgeLabelId = rootVertex->vertexDesc->commonInfo->relatedEdgeLabels[i].edgeLabelId;
        DmEdgeLabelT *edgeLabel = NULL;
        Status ret = CataGetEdgeLabelById(edgeLabelId, (DmEdgeLabelT **)&edgeLabel, NULL);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get edgeLabel, id=%" PRIu32 ".", edgeLabelId);
            return ret;
        }
        EdgeScanDirectionE direction =
            (edgeLabel->sourceVertexLabelId == rootVertex->vertexDesc->labelId) ? EDGE_OUT : EDGE_IN;
        (void)CataReleaseEdgeLabel(edgeLabel);

        if (direction == EDGE_OUT) {
            // 对于出边，找到所有孩子节点并删除
            ret = QryExecuteDelVertexForSingleEdge(stmt, edgeLabelId, rootVertex, isForceDel);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return QrySetEdgeAddrWhenDeleteChildren(stmt, rootVertex, data, graphText);
}

static Status QryDeleteChildGraph(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, QryUpdateVertexDataT *data, TextT *graphText)
{
    bool isForceDelete = (data->autoOperateFlag & QRY_AUTO_DELETE_WEAK_EDGE);
    QryObjectMapNodeT *vertexNode = NULL;

    Status ret = QryAllocAndDeSerializeVertex(stmt->session->objectMap, labelCursor->heapTuple.heapTupleBuf.buf,
        labelCursor->heapTuple.heapTupleBuf.bufSize, data->label->metaCommon.metaId, &vertexNode);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = QryDeleteChildrenAndSetEdgeAddr(stmt, vertexNode->vertex, isForceDelete, data, graphText);
    QryReleaseEmptyVertex(vertexNode);
    return ret;
}

static Status QryCheckBuf4Replace(QryStmtT *stmt, QryLabelCursorT *labelCursor, QryTupleInfoT *tupleInfo)
{
    DB_POINTER3(stmt, labelCursor, tupleInfo);
    Status ret = QryCheckRes4Replace(stmt, labelCursor);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = QryCheckNonChangeableProperty(
        labelCursor->labelDef.vertexLabel, &labelCursor->heapTuple.heapTupleBuf, &tupleInfo->heapTupleBuf);
    return ret;
}

static Status QryReplaceVertexCore(QryStmtT *stmt, QryLabelCursorT *labelCursor, QryUpdateVertexDataT *data)
{
    QryTupleInfoT tupleInfo;
    tupleInfo.vertex = labelCursor->vertexNode->vertex;
    tupleInfo.heapTupleBuf.buf = (uint8_t *)data->deltaVertexBuf.str;
    tupleInfo.heapTupleBuf.bufSize = data->deltaVertexBuf.len;

    // 校验资源字段和NonChangeable字段（partitionId）
    Status ret = QryCheckBuf4Replace(stmt, labelCursor, &tupleInfo);
    if (ret != GMERR_OK) {
        return ret;
    }

    bool isReplaceGraph = data->autoOperateFlag & QRY_AUTO_DEL_RELATION_VERTEX;
    // 如果是replace graph，需要先删除所有孩子节点再replace
    TextT graphText = {0};
    if (isReplaceGraph &&
        (DmIsYangVertexDesc(tupleInfo.vertex->vertexDesc) || tupleInfo.vertex->vertexDesc->edgeLabelNum > 0)) {
        ret = QryDeleteChildGraph(stmt, labelCursor, data, &graphText);
        if (ret != GMERR_OK) {
            return ret;
        }
        tupleInfo.heapTupleBuf.buf = (uint8_t *)graphText.str;
        tupleInfo.heapTupleBuf.bufSize = graphText.len;
    }

    // check need maintain syncDataVersion
    DmVertexLabelT *vertexLabel = labelCursor->labelDef.vertexLabel;
    if (SECUREC_UNLIKELY(QryCheckIsSyncVertexLabel(vertexLabel))) {
        ret = data->isReplay != 0 ?
                  QryCheckSyncDataVersion(labelCursor->action, vertexLabel, &labelCursor->heapTuple.heapTupleBuf,
                      &tupleInfo.heapTupleBuf, data->replayOldVersion) :
                  QrySustainDataSyncVersion(stmt, vertexLabel, &tupleInfo.heapTupleBuf);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if ((ret = QryFullUpdateVertex(stmt, labelCursor, data, &tupleInfo)) != GMERR_OK) {
        return ret;
    }
    bool isAged = ((labelCursor->heapTuple.dataStatus & QRY_OLD_DATA_STATUS_AGE) != 0);
    ret = QrySubSend4Replace(stmt, labelCursor, data->deltaVertex, tupleInfo.heapTupleBuf, isAged);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

Status ExecMergeOrRepForSingleVertex(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, QryVertexDataT *vertexData, bool isFound, bool isMerge)
{
    Status ret;
    if (!isFound) {
        labelCursor->action = HEAP_OPTYPE_REPLACE_INSERT;
        ret = ContainerResetOpType(labelCursor->containerHdl, HEAP_OPTYPE_REPLACE_INSERT, false);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "reset op type, vertexLabel=%s.", labelCursor->labelDef.vertexLabel->metaCommon.metaName);
            return ret;
        }
        return QryInsertSingleVertexCore(stmt, labelCursor, vertexData, isMerge);
    }

    QryUpdateVertexDataT data = {.label = labelCursor->labelDef.vertexLabel,
        .deltaVertex = labelCursor->vertexNode->vertex,
        .deltaVertexBuf = vertexData->vertexBuf,
        .isNonDeSer = false,
        .autoOperateFlag = vertexData->autoOperateFlag,
        .isReplay = vertexData->isReplay,
        .replayOldVersion = vertexData->replayOldVersion};

    ContainerTupleBufMove(labelCursor->containerHdl, labelCursor->tupleBuf, labelCursor->idxCtx);
    labelCursor->heapTuple.heapTupleBuf = TupleBufGet(labelCursor->tupleBuf);

    ret = QryCheckRecordPush(stmt, labelCursor, &labelCursor->heapTuple.dataStatus);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (isMerge) {
        ret = QryMergeUpdateVertex(stmt, labelCursor, &data);
    } else {
        ret = QryReplaceVertexCore(stmt, labelCursor, &data);
    }
    return ret;
}

Status QryMergeReplaceFixPkConflict(QryLabelCursorT *labelCursor, bool *isFound)
{
    DB_POINTER2(labelCursor, isFound);
    Status ret = ContainerResetOpType(labelCursor->containerHdl, HEAP_OPTYPE_DELETE, false);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "reset operation type.");
        return ret;
    }

    ret = ContainerDeleteTuple(labelCursor->containerHdl, labelCursor->addr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, " delete from container.");
        return ret;
    }
    // 批量利用chRunHdl->heapTuple，需要清除
    ContainerClearTupleBuf(labelCursor->containerHdl, labelCursor->idxCtx);
    ret = ContainerIdxLookup(
        labelCursor->containerHdl, labelCursor->idxCtx, labelCursor->pkValue, &labelCursor->addr, isFound);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "lookup.");
        return ret;
    }

    return GMERR_OK;
}

Status QryMergeReplaceCore(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, QryVertexDataT *vertexData, bool *isFound, bool isMerge)
{
    DB_POINTER4(stmt, labelCursor, vertexData, isFound);
    labelCursor->posInfo = &vertexData->posInfo;
    Status ret = ExecMergeOrRepForSingleVertex(stmt, labelCursor, vertexData, *isFound, isMerge);

    uint32_t retry = 0;
    const uint32_t maxRetry = 100;  // 加上重试上限，避免索引问题导致死循.
    while (ret == GMERR_PRIMARY_KEY_VIOLATION &&
           labelCursor->labelDef.vertexLabel->commonInfo->heapInfo.isolationLevel == READ_COMMITTED &&
           retry++ < maxRetry) {
        // If the primary conflicts, try again. Insert operation will not lock row,so the update operation is
        // required. delete the heap tuple of the insert operation before the update operation is executed.
        ret = QryMergeReplaceFixPkConflict(labelCursor, isFound);
        if (ret != GMERR_OK) {
            return ret;
        }

        ret = ExecMergeOrRepForSingleVertex(stmt, labelCursor, vertexData, *isFound, isMerge);
    }

    if (ret != GMERR_OK) {
        DmIndexTypeE indexType = INDEX_TYPE_MAX;
        if (!labelCursor->isChLabel) {
            indexType = labelCursor->labelDef.vertexLabel->metaVertexLabel->pkIndex->idxLabelBase.indexType;
        }

        ConcurrencyControlE ccType = labelCursor->labelDef.vertexLabel->commonInfo->heapInfo.ccType;
        DB_LOG_ERROR(ret,
            " merge or replace to label: %s, retry times=%" PRIu32 ", indexType=%" PRIi32 ", ccType=%" PRIi32 ".",
            labelCursor->labelDef.vertexLabel->metaCommon.metaName, retry, (int32_t)indexType, (int32_t)ccType);
        return ret;
    }

    return GMERR_OK;
}

static Status QryMergePrepareWhenNotFound(QryStmtT *stmt, DmVertexT *vertex, DmVertexT *newVertex,
    QryLabelCursorT *labelCursor, QryVertexDataT *modifedVertexData)
{
    Status ret = GMERR_OK;
    ret = QryFillUpAutoGeneratedProperties(stmt, vertex, labelCursor, modifedVertexData);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmVertexConstraintCheck(vertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

static Status QryPrepareAndMergeVertex(QryStmtT *stmt, QryLabelCursorT *labelCursor, QryVertexDataT *vertexData,
    DmVertexT *vertex, DbResAckHeaderT *resAckHeader)
{
    bool isFound = false;
    Status ret = QryLookupIdxWithPrefetch(labelCursor, &isFound);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 此处modifedVertexData用于保护用户输入vertexData不被直接修改,不要删除
    QryVertexDataT modifedVertexData = *vertexData;
    DmVertexT *newVertex = NULL;
    if (!isFound) {
        ret = DmCreateEmptyVertexWithMemCtx(stmt->memCtx, labelCursor->labelDef.vertexLabel, &newVertex);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(
                ret, "create vertex, name=%s", labelCursor->labelDef.vertexLabel->metaVertexLabel->topRecordName);
            return ret;
        }
        ret = QryMergePrepareWhenNotFound(stmt, vertex, newVertex, labelCursor, &modifedVertexData);
        if (ret != GMERR_OK) {
            DmDestroyVertex(newVertex);
            return ret;
        }
    }
    ret = QryMergeReplaceCore(stmt, labelCursor, &modifedVertexData, &isFound, true);
    DmDestroyVertex(newVertex);  // 里面有判空
    if (ret != GMERR_OK) {
        return ret;
    }
    resAckHeader->affectRows += isFound ? QRY_AFFECT_ROW_UPDATE : QRY_AFFECT_ROW_INSERT;
    return GMERR_OK;
}

Status QryMergeSingleVertex(QryStmtT *stmt, QryVertexDataT *vertexData, DbResAckHeaderT *resAckHeader)
{
    QryLabelCursorT *labelCursor = &((QryCursorT *)stmt->qryCursor)->labelCursors[0];
    DmVertexT *vertex = labelCursor->vertexNode->vertex;
    if (DmAutoIncIsOnPk(vertex)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_FEATURE_NOT_SUPPORTED, "The merge op is not supported if the primary key is auto increment.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    DmClearVertex(vertex);
    Status ret = DmDeSerialize2ExistsVertex(
        (uint8_t *)vertexData->vertexBuf.str, vertexData->vertexBuf.len, vertex, DmServerGetCheckMode());
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "deserialize vertex, vertexLabel=%s.", labelCursor->labelDef.vertexLabel->metaCommon.metaName);
        return ret;
    }
    if (!DmVertexPkPropeIsAllSet(vertex)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "The primary key value is not all set, vertexLabel=%s.",
            labelCursor->labelDef.vertexLabel->metaCommon.metaName);
        return GMERR_DATA_EXCEPTION;
    }

    ret =
        DmGetKeyBufFromVertex(vertex, labelCursor->labelDef.vertexLabel->metaVertexLabel->pkIndex->idxLabelBase.indexId,
            (uint8_t **)&labelCursor->pkValue.keyData, &labelCursor->pkValue.keyLen);
    if (ret != GMERR_OK) {
        return ret;
    }

    return QryPrepareAndMergeVertex(stmt, labelCursor, vertexData, vertex, resAckHeader);
}
static Status DmVertexSetAutoIncPropWrap(const DmVertexT *vertex, const DmVertexLabelT *vertexLabel)
{
    Status ret = DmVertexSetAutoIncProp(vertex, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set AutoIncProp, vertexLabel=%s.", vertexLabel->metaCommon.metaName);
        return ret;
    }
    return GMERR_OK;
}

Status QrySetAutoGeneratedProp4ReplaceVertex(QryStmtT *stmt, QryLabelCursorT *labelCursor)
{
    DmVertexLabelT *vertexLabel = labelCursor->labelDef.vertexLabel;
    DmVertexT *vertex = labelCursor->vertexNode->vertex;

    Status ret = DmCheckBitMapConstrict(vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "The constraint is not satisfied, vertexLabel=%s.", vertexLabel->metaCommon.metaName);
        return ret;
    }

#ifdef FEATURE_REPLICATION
    if (stmt->session->isReplay) {
        ret = DmVertexSetAutoIncPropByOverwrite(vertex, vertexLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
        return DmVertexSetResColPrope2Zero(vertex);
    }
#endif
    ret = DmVertexSetAutoIncPropWrap(vertex, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    return DmVertexSetResColPrope2Zero(vertex);
}

static inline bool QryCheckSpecialVertexLabel(DmVertexLabelT *vertexLabel)
{
    // 非特殊复杂表直接返回true
    if (SECUREC_LIKELY(vertexLabel->metaVertexLabel->labelLevel != VERTEX_LEVEL_SPECIAL)) {
        return true;
    }

    if (DmServerGetCheckMode()) {
        return false;
    }

    // 如果为特殊复杂表，并且有memberKey字段进行Size校验，则需要反序列化操作
    if (vertexLabel->vertexDesc->hasMemberKey) {
        return false;
    }

    return true;
}

static inline bool QryCheckGeneralVertexLabel(DmVertexLabelT *vertexLabel)
{
    // 非一般复杂表直接返回true
    if (SECUREC_LIKELY(vertexLabel->metaVertexLabel->labelLevel != VERTEX_LEVEL_GENERAL)) {
        return true;
    }

    if (DmServerGetCheckMode()) {
        return false;
    }

    // 如果为一般复杂表，由客户端进行校验，为true则需反序列化进行非空字段校验
    if (vertexLabel->metaVertexLabel->checkValidity) {
        return false;
    }

    return true;
}

static bool QryCheckNonSuitableScene(DmVertexLabelT *vertexLabel)
{
    // 判断主键索引字段全定长，聚簇容器主键为定长字段，heap容器主键需判断
    if (SECUREC_UNLIKELY(
            vertexLabel->metaVertexLabel->pkIndex != NULL &&
            vertexLabel->metaVertexLabel->pkIndex->propeNum != vertexLabel->metaVertexLabel->pkIndex->fixedPropeNum)) {
        return false;
    }

    // 资源列
    if (SECUREC_UNLIKELY(vertexLabel->commonInfo->resColInfo != NULL)) {
        return false;
    }

    // 分区表
    if (SECUREC_UNLIKELY(vertexLabel->commonInfo->accCheckAddr->isPartition)) {
        return false;
    }

    // bitMap
    if (SECUREC_UNLIKELY(vertexLabel->vertexDesc->hasBitMapProp)) {
        return false;
    }

    // 自增列
    if (SECUREC_UNLIKELY(vertexLabel->commonInfo->autoIncrPropNum > 0)) {
        return false;
    }

    // 建边
    if (SECUREC_UNLIKELY(vertexLabel->commonInfo->edgeLabelNum != 0)) {
        return false;
    }

    // 升降级
    if (SECUREC_UNLIKELY(vertexLabel->commonInfo->hasUpd)) {
        return false;
    }

    return true;
}

Status QryCheckReplaceVertexAndGetKeyBuf(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, QryVertexDataT *vertexData, bool *vertexChanged)
{
    DmVertexLabelT *vertexLabel = labelCursor->labelDef.vertexLabel;
    DmVertexT *vertex = labelCursor->vertexNode->vertex;
    uint32_t pkIdxId = vertexLabel->metaVertexLabel->pkIndex->idxLabelBase.indexId;
    *vertexChanged = false;
    /*
     * 在V3兼容模式下：
     * 1. 简单表不需反序列化；
     * 2. 特殊复杂表不反序列化需满足下列条件：1）输入buf不进行合法性校验；2）无member key；
     * 3. 一般复杂表不反序列化需满足下列条件：1）输入buf不进行合法性校验；2）checkValidity为false客户端已校验；
     * 4.
     * 其他需要满足：1）主键key值所有字段为定长字段；2）无资源列；3）无分区；3）无bitmap；4）无自增列；5）无建边；6）无升降级；
     * 5. YANG 表都需要进行反序列化
     */
    if (SECUREC_LIKELY(QryIsCompatibleV3() && QryCheckSpecialVertexLabel(vertexLabel) &&
                       QryCheckGeneralVertexLabel(vertexLabel) && QryCheckNonSuitableScene(vertexLabel) &&
                       !DmIsYangVertexLabel(vertexLabel))) {
        if (SECUREC_UNLIKELY(!DmVertexBufCheckPkIsAllSetForV3Mode(vertexLabel, (uint8_t *)vertexData->vertexBuf.str))) {
            DB_LOG_ERROR(
                GMERR_DATA_EXCEPTION, "Primary key is not all set, label name: %s.", vertexLabel->metaCommon.metaName);
            return GMERR_DATA_EXCEPTION;
        }
        return QryGetPrimaryKeyBufFromVertexBufV3Mode(
            vertex, (uint8_t *)vertexData->vertexBuf.str, &labelCursor->pkValue);
    }
    Status ret = DmDeSerialize2ExistsVertex(
        (uint8_t *)vertexData->vertexBuf.str, vertexData->vertexBuf.len, vertex, DmServerGetCheckMode());
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(!DmVertexPkPropeIsAllSet(vertex))) {
        DB_LOG_AND_SET_LASERR(
            GMERR_DATA_EXCEPTION, "Primary key is not all set, label name: %s.", vertexLabel->metaCommon.metaName);
        return GMERR_DATA_EXCEPTION;
    }
    ret = DmVertexConstraintCheck(vertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = QrySetAutoGeneratedProp4ReplaceVertex(stmt, labelCursor);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    *vertexChanged = vertex->vertexDesc->hasBitMapProp || vertexLabel->commonInfo->autoIncrPropNum > 0 ||
                     vertexLabel->commonInfo->resColInfo != NULL;
    return DmGetKeyBufFromVertex(
        vertex, pkIdxId, (uint8_t **)&labelCursor->pkValue.keyData, &labelCursor->pkValue.keyLen);
}

static Status QryCheckInsertMarkDeleteData(QryLabelCursorT *labelCursor, bool isFound)
{
    DB_POINTER(labelCursor);
    TupleT tupleBuf;
    if (!labelCursor->isChLabel) {
        tupleBuf = TupleBufGet(&labelCursor->idxCtx->tupleBuf);
    } else {
        // 聚簇容器key compare的时候已经更新了labelCursor->tupleBuf
        tupleBuf = TupleBufGet(labelCursor->tupleBuf);
    }
    // insert操作转的replace操作，找到非标记删除的数据要报错
    if (isFound && labelCursor->isIns2Rep) {
        if (SECUREC_UNLIKELY(!IsHeapBufMarkDelete(labelCursor->labelDef.vertexLabel, &tupleBuf))) {
            DB_LOG_AND_SET_LASERR(GMERR_PRIMARY_KEY_VIOLATION,
                "Inserted data violates primary unique constraint restriction, label name: %s.",
                labelCursor->labelDef.vertexLabel->metaCommon.metaName);
            return GMERR_PRIMARY_KEY_VIOLATION;
        }
    }
    return GMERR_OK;
}

static Status QryLookupIdxCheckViolation(QryLabelCursorT *labelCursor, bool *isFound)
{
    DB_POINTER2(labelCursor, isFound);
    Status ret = QryLookupIdxWithPrefetch(labelCursor, isFound);
    if (ret != GMERR_OK) {
        return ret;
    }
    return QryCheckInsertMarkDeleteData(labelCursor, *isFound);
}

Status QryReplaceSingleVertex(QryStmtT *stmt, QryVertexDataT *vertexData, DbResAckHeaderT *resAckHeader)
{
    QryLabelCursorT *labelCursor = &((QryCursorT *)stmt->qryCursor)->labelCursors[0];
    bool vertexChanged = false;
    Status ret = QryCheckReplaceVertexAndGetKeyBuf(stmt, labelCursor, vertexData, &vertexChanged);
    if (ret != GMERR_OK) {
        return ret;
    }
    bool isFound = false;
    if ((ret = QryLookupIdxCheckViolation(labelCursor, &isFound)) != GMERR_OK) {
        return ret;
    }

    TupleT tuple = TupleBufGet(&labelCursor->idxCtx->tupleBuf);
    DmVertexLabelT *label = labelCursor->labelDef.vertexLabel;
    bool isLogicNotFound = !isFound || IsStatusMergeMarkDelete(label, &tuple, label->vertexDesc);
    if (isLogicNotFound) {
        ret = QryAllocRes(stmt, labelCursor, labelCursor->vertexNode->vertex);
        if (ret != GMERR_OK) {
            return ret;
        }
        vertexChanged = vertexChanged ? true : label->commonInfo->resColInfo != NULL;
    }
    QryVertexDataT modifedVertexData = *vertexData;
    if (vertexChanged) {
        ret = DmSerializeVertex(labelCursor->vertexNode->vertex, (uint8_t **)&modifedVertexData.vertexBuf.str,
            &modifedVertexData.vertexBuf.len);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    ret = QryMergeReplaceCore(stmt, labelCursor, &modifedVertexData, &isFound, false);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (isLogicNotFound) {  // 主键不存在或者标记删除，新插入的数据，资源表返回资源id
        ret = QryMakeRespForDmlOperationByRes(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    // labelCursor->isIns2Rep为true时代表用户进行的是insert操作
    bool isOperateInsert = labelCursor->isIns2Rep || !isFound;
    resAckHeader->affectRows += isOperateInsert ? QRY_AFFECT_ROW_INSERT : QRY_AFFECT_ROW_UPDATE;
    return GMERR_OK;
}

static inline bool QryCheckNeedBatch4Merge(QryLabelCursorT *labelCursor)
{
    const VertexLabelCommonInfoT *commonInfo = labelCursor->labelDef.vertexLabel->commonInfo;
    return commonInfo->heapInfo.ccType != CONCURRENCY_CONTROL_NORMAL && commonInfo->resColInfo == NULL &&
           commonInfo->autoIncrPropNum == 0 && commonInfo->edgeLabelNum == 0 &&
           !labelCursor->vertexNode->vertex->vertexDesc->hasBitMapProp;
}

static Status QryExecMergeVertex(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, const QryVertexParamT *replaceParams, DbResAckHeaderT *resAckHeader)
{
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < replaceParams->count; i++) {
        QryVertexDataT *vertexData = &replaceParams->vertexData[i];
        ret = QryMergeSingleVertex(stmt, vertexData, resAckHeader);
        if (SECUREC_UNLIKELY(ret != GMERR_OK && ret != SYNC_DATA_CHECK_DROP_ERROR)) {
            DB_LOG_ERROR(
                ret, "execute merge vertex for label: %s.", labelCursor->labelDef.vertexLabel->metaCommon.metaName);
            return ret;
        }
    }

    return GMERR_OK;
}

static Status QryExecRepChLabel(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, const QryVertexParamT *replaceParams, DbResAckHeaderT *resAckHeader)
{
    uint32_t insertNum = replaceParams->count;
    Status ret = GMERR_OK;
    if (insertNum > 1 && QryCheckNeedBatch4Merge(labelCursor)) {
        return QryBatchReplaceForChLabel(stmt, labelCursor, replaceParams, resAckHeader);
    }

    for (uint32_t i = 0; i < insertNum; i++) {
        QryVertexDataT *vertexData = &replaceParams->vertexData[i];
        ret = QryReplaceSingleVertexForChLabel(stmt, vertexData, resAckHeader);
        if (i != insertNum - 1) {
            // vertex置0操作，不释放内存
            DmClearVertex(labelCursor->vertexNode->vertex);
        }
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "execute replace vertex.");
            return ret;
        }
    }
    return GMERR_OK;
}

static Status QryRePrepareTrans(QryStmtT *stmt, const QryVertexParamT *replaceParams)
{
    DB_POINTER2(stmt, replaceParams);
    QryLabelCursorT *labelCursor = &((QryCursorT *)stmt->qryCursor)->labelCursors[0];
    const bool isIns2Rep = labelCursor->isIns2Rep;
    const HpOpTypeE action = labelCursor->action;
    // 当批量replace主键冲突时，回滚批量操作，再走单条replace
    Status ret = QryExecuteRollbackTrans(stmt);  // 往下labelCursor不可用
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "rollback trans when bath replace pk violated vertex.");
        return ret;
    }

    ret = QryAllocCursor(stmt);
    if (ret != GMERR_OK) {
        return GMERR_OK;
    }

    ret = QryExecuteBeginTrans(stmt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "begin trans after rollback bath replace.");
        return ret;
    }
    QryLabelCursorT *newLabelCursor = &((QryCursorT *)stmt->qryCursor)->labelCursors[0];
    QryMergRepVertexDescT *repCtx = (QryMergRepVertexDescT *)stmt->context->entry;
    newLabelCursor->action = action;
    newLabelCursor->isIns2Rep = isIns2Rep;
    ret = QryOpenVertexLabelCursor(stmt, repCtx->qryLabel, newLabelCursor, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "open vertex label cursor when reprepare trans, labelname: %s.",
            repCtx->qryLabel->def.vertexLabel->metaCommon.metaName);
        return ret;
    }
    if (SECUREC_UNLIKELY(!QrySchemaVersionExistCheck(repCtx->qryLabel->def.vertexLabel, repCtx->uuid))) {
        ret = GMERR_UNDEFINED_TABLE;
        DB_LOG_ERROR(ret, "check uuid when reprepare trans, labelname: %s, uuid=%" PRIu32 ".",
            repCtx->qryLabel->def.vertexLabel->metaCommon.metaName, repCtx->uuid);
        return ret;
    }
    SeTransSetDmlHint4BatchNum(stmt->session->seInstance, replaceParams->count);
    if (DmIsLabelSupportStatusMerge(newLabelCursor->labelDef.vertexLabel)) {
        ret = QryAppendStMgPubsubDataSet(&stmt->session->stmgSubDataSet, newLabelCursor->labelDef.vertexLabel);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status QryExecReplaceVertex(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, const QryVertexParamT *replaceParams, DbResAckHeaderT *resAckHeader)
{
    if (SECUREC_LIKELY(labelCursor->containerType == CONTAINER_CLUSTERED_HASH)) {
        return QryExecRepChLabel(stmt, labelCursor, replaceParams, resAckHeader);
    }
#ifdef ART_CONTAINER
    if (labelCursor->containerType == CONTAINER_ART) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Cs mode with art container");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
#endif
    Status ret = GMERR_OK;
    uint32_t insertNum = replaceParams->count;

    /*
     * 带资源字段、bitmap、自增列或者边的表，都需要创建insertNum个empty vertex，走批量性能会更糟糕。
     * merge request暂时不走batch
     * 非label latch暂时不走batch，因为如果发生插入失败的情况，需要物理删除之前批量插入的数据
     * 连续插入两条一样的数据，会导致replace insert失败，因此需要回滚之前批量插入的数据，再进行单条replace
     * 交互式事务中无法单独回滚批量操作，再进行单条replace，因此只有轻量化事务支持批量
     * insert操作转变成的replace操作只走单条replace
     * 表锁模式支持聚簇容器的情况下，可以将批量逻辑删除
     * 当前批量replace update老化数据的场景，如果一批中replace update两次该老化数据，会导致计数错误
     * 因此暂时如果一批中有update操作，直接走单条replace
     */
    bool canBatchInsert = QryIsSupportBatchInsert(labelCursor, replaceParams);
    if (QryCheckNeedBatch4Merge(labelCursor) && insertNum > 1 && canBatchInsert && !labelCursor->isIns2Rep &&
        stmt->autoCommit) {
        bool isRepUpdate = false;
        ret = QryBatchReplace(stmt, labelCursor, replaceParams, resAckHeader, &isRepUpdate);
        if (ret != GMERR_PRIMARY_KEY_VIOLATION && !isRepUpdate) {
            return ret;
        }

        ret = QryRePrepareTrans(stmt, replaceParams);  // 往下labelCursor不可用
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    QryLabelCursorT *newLabelCursor = &((QryCursorT *)stmt->qryCursor)->labelCursors[0];
    for (uint32_t i = 0; i < insertNum; i++) {
        QryVertexDataT *vertexData = &replaceParams->vertexData[i];
        ret = QryReplaceSingleVertex(stmt, vertexData, resAckHeader);
        if (i != insertNum - 1) {
            DmClearVertex(newLabelCursor->vertexNode->vertex);
        }
        if (SECUREC_UNLIKELY(ret != GMERR_OK && ret != SYNC_DATA_CHECK_DROP_ERROR)) {
            DB_LOG_ERROR(ret, "execute replace vertex.");
            return ret;
        }
    }

    return GMERR_OK;
}

Status ExecMergeOrRepVertex(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, const QryVertexParamT *replaceParams, DbResAckHeaderT *resAckHeader)
{
    SeTransSetDmlHint4BatchNum(stmt->session->seInstance, replaceParams->count);

    QryMergRepVertexDescT *mergRepCtx = (QryMergRepVertexDescT *)stmt->context->entry;
    if (SECUREC_UNLIKELY(mergRepCtx->isMerge)) {
        return QryExecMergeVertex(stmt, labelCursor, replaceParams, resAckHeader);
    }
    return QryExecReplaceVertex(stmt, labelCursor, replaceParams, resAckHeader);
}

Status QryExecuteMergRepVertex(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    Status ret = GMERR_OK;
    QryMergRepVertexDescT *mergRepCtx = (QryMergRepVertexDescT *)stmt->context->entry;

    DbResAckHeaderT resAckHeader = {0};
    uint32_t headOffSet = 0;
    ret = QryReserveRespHeader(stmt, &headOffSet, !mergRepCtx->isMerge);  // 目前merge不支持资源字段
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    QryLabelCursorT *labelCursor = &((QryCursorT *)stmt->qryCursor)->labelCursors[0];
    labelCursor->action = HEAP_OPTYPE_REPLACE_UPDATE;
    char *labelName = mergRepCtx->qryLabel->def.vertexLabel->metaCommon.metaName;
    DbSetCliOpTableName(labelName, 0);
    ret = QryOpenVertexLabelCursor(stmt, mergRepCtx->qryLabel, labelCursor, true);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "open label cursor when merge or replace vertex, label: %s.", labelName);
        return ret;
    }

    if (SECUREC_UNLIKELY(!QrySchemaVersionExistCheck(mergRepCtx->qryLabel->def.vertexLabel, mergRepCtx->uuid))) {
        return GMERR_UNDEFINED_TABLE;
    }

    if (SECUREC_UNLIKELY(labelCursor->labelDef.vertexLabel->commonInfo->resColInfo != NULL)) {
        ret = QryInitResData(stmt, mergRepCtx->qryLabel->def.vertexLabel);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "init res data when replace or merge vertex, label: %s.", labelName);
            return ret;
        }
    }

    QryVertexParamT *replaceParams = (QryVertexParamT *)stmt->execParams;
    bool checkFlag = DmServerGetCheckMode();
    if (SECUREC_UNLIKELY(checkFlag && !mergRepCtx->isMerge)) {
        ret = QryCheckVertexLabelLen(labelCursor, replaceParams);
        if (ret != GMERR_OK) {
            goto EXIT;
        }
    }

    // 设置状态合并链表
    if (SECUREC_UNLIKELY(DmIsLabelSupportStatusMerge(labelCursor->labelDef.vertexLabel))) {
        ret = QryAppendStMgPubsubDataSet(&stmt->session->stmgSubDataSet, labelCursor->labelDef.vertexLabel);
        if (ret != GMERR_OK) {
            goto EXIT;
        }
    }

    ret = ExecMergeOrRepVertex(stmt, labelCursor, replaceParams, &resAckHeader);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "execute replace or merge vertex, label name: %s.", labelName);
    }
    /*
     * the RSP:RPCHEADER|affectRows(uint32)
     */
    if (SECUREC_LIKELY(ret == GMERR_OK)) {
        ret = QryMakeResp4ResAckHeader(stmt, headOffSet, &resAckHeader, !mergRepCtx->isMerge);
    } else {
        QrySetErrorInfoWhenReplay(stmt, ret);
    }
EXIT:
    if (SECUREC_UNLIKELY(labelCursor->isOpen && labelCursor->labelDef.vertexLabel->commonInfo->resColInfo != NULL)) {
        QryUnInitResData(stmt, mergRepCtx->qryLabel->def.vertexLabel);
    }
    return ret;
}

static Status QryExecuteInsertVertexInner(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, const QryVertexParamT *insertParams, DbResAckHeaderT *resAckHeader)
{
    DB_POINTER4(stmt, labelCursor, insertParams, resAckHeader);
    uint32_t insertNum = insertParams->count;
    bool canBatchInsert = false;
    if (labelCursor->isChLabel) {
        canBatchInsert = true;  // 聚簇容器下不进行长度判断，内部实现是通过for循环插入
    } else {
        canBatchInsert = QryIsSupportBatchInsert(labelCursor, insertParams);
    }
    stmt->isBatchInsert4Yang = false;
    // 带资源字段、自增列或者边的表，都需要创建batchNum个empty vertex，走批量性能会更糟糕。
    if (labelCursor->labelDef.vertexLabel->commonInfo->resColInfo == NULL &&
        labelCursor->labelDef.vertexLabel->commonInfo->autoIncrPropNum == 0 &&
        labelCursor->labelDef.vertexLabel->commonInfo->edgeLabelNum == 0 && insertNum > 1 && canBatchInsert) {
        return QryBatchExecInsertVertexes(stmt, labelCursor, insertParams, resAckHeader);
    }
    // 单条插入时，前面经过check已经反序列化得到vertex，直接插入
    if (insertNum == 1 && QryIsNeedCheckVertex(labelCursor->labelDef.vertexLabel)) {
        QryVertexDataT *vertexItem = &insertParams->vertexData[0];
        labelCursor->posInfo = &vertexItem->posInfo;
        return QryInsertSingleVertexInner(stmt, labelCursor, vertexItem, labelCursor->vertexNode->vertex, resAckHeader);
    }
    for (uint32_t i = 0; i < insertNum; i++) {
        QryVertexDataT *vertexItem = &insertParams->vertexData[i];
        labelCursor->posInfo = &vertexItem->posInfo;
        Status ret = QryInsertSingleVertex(stmt, labelCursor, vertexItem, resAckHeader);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status QryExecuteInsert2RepVertex(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, const QryVertexParamT *insertParams, DbResAckHeaderT *resAckHeader)
{
    DB_POINTER4(stmt, labelCursor, insertParams, resAckHeader);
    uint32_t insertNum = insertParams->count;
    SeTransSetDmlHint4BatchNum(stmt->session->seInstance, insertNum);
    if (DmIsYangVertexLabel(labelCursor->labelDef.vertexLabel) ||
        (labelCursor->labelDef.vertexLabel->metaVertexLabel->pkIndex == NULL && !labelCursor->isChLabel)) {
        return QryExecuteInsertVertexInner(stmt, labelCursor, insertParams, resAckHeader);
    }
    // 因为要处理老化数据，对于已经老化或者标记删除的数据，插入应该成功，因此这里使用replace操作
    // 走replace流程时，需要将action修改为update，保证replace流程根据索引找数据时不释放锁
    labelCursor->isIns2Rep = true;
    labelCursor->action = HEAP_OPTYPE_REPLACE_UPDATE;
    Status ret = ContainerResetOpType(labelCursor->containerHdl, HEAP_OPTYPE_REPLACE_UPDATE, false);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return QryExecReplaceVertex(stmt, labelCursor, insertParams, resAckHeader);
}

Status QryExecuteInsertVertex(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryInsertVertexDescT *insertCtx = (QryInsertVertexDescT *)stmt->context->entry;
    DbResAckHeaderT resAckHeader = {0};
    uint32_t headOffSet = 0;
    Status ret = QryReserveRespHeader(stmt, &headOffSet, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    QryLabelCursorT *labelCursor = &((QryCursorT *)stmt->qryCursor)->labelCursors[0];
    labelCursor->action = HEAP_OPTYPE_INSERT;
    ret = QryOpenVertexLabelCursor(stmt, insertCtx->qryLabel, labelCursor, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "open vertex label cursor when execute insert vertex, labelname: %s.",
            insertCtx->qryLabel->def.vertexLabel->metaCommon.metaName);
        return ret;
    }
    DbSetCliOpTableName(insertCtx->qryLabel->def.vertexLabel->metaCommon.metaName, 0);
    if (SECUREC_UNLIKELY(!QrySchemaVersionExistCheck(insertCtx->qryLabel->def.vertexLabel, insertCtx->uuid))) {
        ret = GMERR_UNDEFINED_TABLE;
        DB_LOG_AND_SET_LASERR(ret, "UUID is not equal, labelName=%s, UUID=%" PRIu32 ".",
            insertCtx->qryLabel->def.vertexLabel->metaCommon.metaName, insertCtx->uuid);
        return ret;
    }
    QryVertexParamT *insertParams = (QryVertexParamT *)stmt->execParams;
    ret = QryCheckInsertVertex(stmt, insertParams, labelCursor, insertCtx->schemaVersion);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (labelCursor->labelDef.vertexLabel->commonInfo->resColInfo != NULL) {
        ret = QryInitResData(stmt, insertCtx->qryLabel->def.vertexLabel);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "init res data when execute insert vertex, labelname: %s.",
                insertCtx->qryLabel->def.vertexLabel->metaCommon.metaName);
            return ret;
        }
    }
    if (DmIsLabelSupportStatusMerge(labelCursor->labelDef.vertexLabel)) {
        ret = QryAppendStMgPubsubDataSet(&stmt->session->stmgSubDataSet, labelCursor->labelDef.vertexLabel);
        if (ret != GMERR_OK) {
            goto EXIT;
        }
    }
    ret = QryExecuteInsert2RepVertex(stmt, labelCursor, insertParams, &resAckHeader);
    /*
     * the RSP:RPCHEADER|affectRows(uint32)|resIdGroupCnt(uint32)|vertexLabelNameLen(uint32)
     *                  |vertexLabelName   |resIdCount(uint32)   |resId(uint64)|...
     */
    if (ret == GMERR_OK) {
        ret = QryMakeResp4ResAckHeader(stmt, headOffSet, &resAckHeader, true);
    }

EXIT:
    if (labelCursor->labelDef.vertexLabel->commonInfo->resColInfo != NULL) {
        QryUnInitResData(stmt, insertCtx->qryLabel->def.vertexLabel);
    }

    return ret;
}

#ifdef FEATURE_VLIVF
Status QryLoadOneVertexIndexKey(DmVertexT *vertex, DmVertexLabelT *vertexLabel, IndexCtxT **secIdxCtx)
{
    DB_POINTER3(vertex, vertexLabel, secIdxCtx);
    uint32_t loadIdxCnt = 0;
    int8_t *propeIsSetValue = vertex->record->propeIsSetValue;
    for (uint32_t i = 0; i < vertexLabel->metaVertexLabel->secIndexNum; ++i) {
        DmVlIndexLabelT *indexLabel = &vertexLabel->metaVertexLabel->secIndexes[i];
        if (indexLabel->idxLabelBase.indexType != VLIVF_INDEX) {
            continue;
        }
        if (propeIsSetValue[indexLabel->vlivfInfo.propId] == DM_PROPERTY_IS_NULL) {
            continue;
        }
        IndexKeyT indexKey;
        Status ret =
            DmGetKeyBufFromVertex(vertex, indexLabel->idxLabelBase.indexId, &indexKey.keyData, &indexKey.keyLen);
        if (ret != GMERR_OK) {
            return ret;
        }
        HpTupleAddr tupleAddr = {0};
        ret = IdxLoad(secIdxCtx[i], indexKey, tupleAddr);
        if (ret != GMERR_OK) {
            return ret;
        }
        ++loadIdxCnt;
    }
    if (SECUREC_UNLIKELY(!loadIdxCnt)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_INVALID_PROPERTY, "No vlivf index field to load, label name=%s.", vertexLabel->metaCommon.metaName);
        return GMERR_INVALID_PROPERTY;
    }
    return GMERR_OK;
}

Status QryLoadIndexKeyBuffers(QryStmtT *stmt, const QryVertexParamT *insertParams, QryLabelCursorT *labelCursor)
{
    DB_POINTER3(stmt, insertParams, labelCursor);
    Status ret = GMERR_OK;
    bool checkFlag = DmServerGetCheckMode();
    if (SECUREC_UNLIKELY(checkFlag)) {
        ret = QryCheckVertexLabelLen(labelCursor, insertParams);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    QryObjectMapNodeT *vertexNode = labelCursor->vertexNode;
    char *labelName = labelCursor->labelDef.vertexLabel->metaCommon.metaName;
    for (uint32_t i = 0; i < insertParams->count; i++) {
        QryVertexDataT *vertexItem = &insertParams->vertexData[i];
        ret = DmDeSerialize2ExistsVertex(
            (uint8_t *)vertexItem->vertexBuf.str, vertexItem->vertexBuf.len, vertexNode->vertex, checkFlag);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "deserialize vertex, label name=%s.", labelName);
            return ret;
        }
        ret = QryLoadOneVertexIndexKey(vertexNode->vertex, labelCursor->labelDef.vertexLabel, labelCursor->secIdxCtx);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "load one vertex index, label name=%s.", labelName);
            return ret;
        }
    }
    return GMERR_OK;
}

Status QryExecuteLoadIndex(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryInsertVertexDescT *insertCtx = (QryInsertVertexDescT *)stmt->context->entry;
    DbResAckHeaderT resAckHeader = {0};
    uint32_t headOffSet = 0;
    Status ret = QryReserveRespHeader(stmt, &headOffSet, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    QryLabelCursorT *labelCursor = &((QryCursorT *)stmt->qryCursor)->labelCursors[0];
    labelCursor->action = HEAP_OPTYPE_INSERT;
    ret = QryOpenVertexLabelCursor(stmt, insertCtx->qryLabel, labelCursor, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "open vertex label cursor when execute load index, labelname: %s.",
            insertCtx->qryLabel->def.vertexLabel->metaCommon.metaName);
        return ret;
    }
    if (SECUREC_UNLIKELY(!QrySchemaVersionExistCheck(insertCtx->qryLabel->def.vertexLabel, insertCtx->uuid))) {
        ret = GMERR_UNDEFINED_TABLE;
        DB_LOG_AND_SET_LASERR(ret, "UUID is not equal, labelName=%s, UUID=%" PRIu32 ".",
            insertCtx->qryLabel->def.vertexLabel->metaCommon.metaName, insertCtx->uuid);
        return ret;
    }
    QryVertexParamT *insertParams = (QryVertexParamT *)stmt->execParams;
    ret = QryLoadIndexKeyBuffers(stmt, insertParams, labelCursor);
    if (ret != GMERR_OK) {
        return ret;
    }
    return QryMakeResp4ResAckHeader(stmt, headOffSet, &resAckHeader, true);
}
#endif

#ifdef __cplusplus
}
#endif
