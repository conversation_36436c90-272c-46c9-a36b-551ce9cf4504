/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: source file of degrading
 * Author: GMDBv5 EE TEAM
 * Create: 2023-02-25
 */

#include "adpt_spinlock.h"
#include "adpt_sleep.h"
#include "db_hashmap.h"
#include "db_sysapp_context.h"
#include "drt_worker_manager.h"
#include "ee_bg_task_agent.h"
#include "ee_session_interface.h"
#include "ee_session.h"
#include "ee_context.h"
#include "ee_concurrency_control.h"
#include "ee_storage_container.h"
#include "ee_feature_import.h"
#include "ee_log.h"
#include "ee_rsm.h"
#include "ee_degrade.h"

#ifdef __cplusplus
extern "C" {
#endif

#define QRY_DEGRADE_MAX_ONCE_EXEC_NUM 100
#define QRY_DEGRADE_UPDATE_PROGRESS 100

typedef enum {
    QRY_DEGRADE_PHASE_INIT,
    QRY_DEGRADE_PHASE_UPDATE,
    QRY_DEGRADE_PHASE_COMPACT,
    QRY_DEGRADE_PHASE_PERSIST,  // 持久化阶段
    QRY_DEGRADE_PHASE_END,
} QryDegradePhase;

typedef struct {
    // ==后台专用无并发==
    QryDegradePhase phase;
    uint64_t beginTimeStamp;
    HpTupleCombineAddrT beginAddr;
    DmVertexLabelT *vertexLabel;
    bool hasBgTaskLock;  // 缩容后台任务需要和降级、老化串行处理

    // ==后台写前台读==
    uint32_t progress;         // 进度:0~99,后台写,前台读
    uint64_t totalTuples;      // 总的数据，最开始的时候读一次，后续发生变化也不读。
    uint64_t processedTuples;  // 已经处理的数据
    uint64_t trxTuples;        // 一次事务处理的数据

    // ==前台创建任务一次性写，后台读==
    uint32_t targetVersion;                    // 降级目标版本号
    uint32_t vertexLabelId;                    // 降级的表
    uint32_t namespaceId;                      // 降级表namespaceId,降级进度视图使用
    uint32_t degradedTimes;                    // 降级表已降级次数,降级进度视图使用
    uint32_t vertexLabelNameLen;               // 降级表名长度,降级视图使用
    char vertexLabelName[MAX_TABLE_NAME_LEN];  // 降级表名,降级进度视图使用

    // ==前台写，后台读，通过LabelLatch做并发保护==
    uint32_t originVersion;  // 首次降级的版本
    bool isInitScanPin;
} QryDegradeTaskT;

typedef struct {
    bool inited;
    DbSpinLockT lock;       // 使用读写锁保护整个结构体
    DbOamapT taskList;      // KEY:VertexLabelId，VALUE:QryDegradeTaskT
    DbOamapIteratorT iter;  // 获取任务使用的迭代器
    DbMemCtxT *memCtx;      // 后台线程使用的内存上下文
    SessionT *session;
} QryDegradeTaskListT;

// 并发方案:加读写锁保护
static QryDegradeTaskListT g_degradeTaskList = {0};

static void QryEndDegradeTask(QryDegradeTaskT *task)
{
    DB_POINTER(task);
    if (task->hasBgTaskLock && task->vertexLabel != NULL) {
        LabelRWLatchT *labelRWLatch =
            (LabelRWLatchT *)GetLabelRWLatchPtrById(task->vertexLabel->commonInfo->vertexLabelLatchId, NULL);
        if (labelRWLatch == NULL) {
            DB_LOG_AND_SET_LASERR(
                GMERR_UNEXPECTED_NULL_VALUE, "get labelLatch when end degrade, vertexLabel=%s.", task->vertexLabelName);
            return;  // 如果走到这里就是有BUG
        }

        LabelLatchUnlockBgTask(labelRWLatch, BGTASK_DEGRADE);
        task->hasBgTaskLock = false;
    }

    if (task->vertexLabel != NULL) {
        (void)CataReleaseVertexLabel(task->vertexLabel);
        task->vertexLabel = NULL;
    }
}

static void QryRmvDegradeTask(QryDegradeTaskT *task, bool removed)
{
    DB_POINTER(task);
    void *key = NULL;
    void *value = NULL;
    DbSpinLock(&g_degradeTaskList.lock);
    // 降级结束之后，LabeLatch已经释放又触发了降级不能删除任务
    // vertexLabel说明表不存在，isDeleted不为true说明没有新的降级
    if (removed || task->vertexLabel == NULL || !task->vertexLabel->metaCommon.isDeleted) {
        Status ret =
            DbOamapRemoveKv(&g_degradeTaskList.taskList, task->vertexLabelId, &task->vertexLabelId, &key, &value);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "remove task, vertexLabelId=%" PRIu32 ".", task->vertexLabelId);
            DB_ASSERT(false);
        }
        // 如果在分片间隙表被drop了，也不用维护rsmTableInfo
        QryRsmFinishDowngrade(task->vertexLabel, removed);
        QryEndDegradeTask(task);
        DbDynMemCtxFree(g_degradeTaskList.memCtx, task);
    }
    DbSpinUnlock(&g_degradeTaskList.lock);
}

static Status QryPrepareDegradeUpdate(QryDegradeTaskT *task)
{
    DB_POINTER(task);
    Status ret;
    QryEndDegradeTask(task);
    task->phase = QRY_DEGRADE_PHASE_INIT;  // 降级合并，这个函数出错，设置成Init重新调用
    ret = CataGetVertexLabelById(NULL, task->vertexLabelId, &task->vertexLabel);
    // 查询不到表则表明所有版本均已删除,删除当前降级任务
    if (ret == GMERR_UNDEFINED_TABLE) {
        return ret;
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get vertexLabel when prepare degrade, vertexLabel=%s.", task->vertexLabelName);
        return ret;
    }

    if (!task->hasBgTaskLock) {  // 不能反复加锁
        // 后台任务串行，如果冲突，下次再处理，
        LabelRWLatchT *labelRWLatch =
            (LabelRWLatchT *)GetLabelRWLatchPtrById(task->vertexLabel->commonInfo->vertexLabelLatchId, NULL);
        if (labelRWLatch == NULL) {
            DB_LOG_AND_SET_LASERR(ret, "get labelLatch when prepare degrade, vertexLabel=%s.", task->vertexLabelName);
            ret = GMERR_UNEXPECTED_NULL_VALUE;
            return ret;
        }

        if (!LabelLatchTryLockBgTask(labelRWLatch, BGTASK_DEGRADE)) {
            ret = GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
            DB_LOG_WARN(ret, "lock bgTask, vertexLabel=%s, bgTaskLock=%" PRIu32 ".", task->vertexLabelName,
                labelRWLatch->bgTaskLock);
            return ret;
        }
        task->hasBgTaskLock = true;
    }

    task->beginAddr = (HpTupleCombineAddrT){BEFORE_FIRST_TUPLE_ADDR, BEFORE_FIRST_TUPLE_BLOCK_ID};
    task->processedTuples = 0;
    task->trxTuples = 0;
    task->progress = 0;
    task->targetVersion = task->vertexLabel->metaCommon.version;
    task->degradedTimes = task->vertexLabel->commonInfo->degradedTimes;
    task->phase = QRY_DEGRADE_PHASE_UPDATE;
    return GMERR_OK;
}

void FreeDegradeTaskList(QryDegradeTaskListT *taskList)
{
    DB_POINTER(taskList);
    if (!taskList->inited) {
        return;
    }
    // HashMap中的value的内存,通过删除memCtx统一释放。
    DbOamapDestroy(&taskList->taskList);
    if (taskList->session != NULL) {
        QrySessionRelease(taskList->session);
        taskList->session = NULL;
    }
    taskList->inited = false;
    if (taskList->memCtx != NULL) {
        DbDeleteDynMemCtx(taskList->memCtx);
        taskList->memCtx = NULL;
    }
}

void QryUninitDegradeTaskList(void)
{
    FreeDegradeTaskList(&g_degradeTaskList);
}

Status QryGetDegradeTaskWithLock(QryDegradeTaskT **task)
{
    DB_POINTER(task);
    Status ret;
    void *key = NULL;
    void *value = NULL;
    ret = DbOamapFetch(&g_degradeTaskList.taskList, &g_degradeTaskList.iter, &key, &value);
    if (ret == GMERR_NO_DATA) {
        DbOamapResetIterator(&g_degradeTaskList.iter);
        ret = DbOamapFetch(&g_degradeTaskList.taskList, &g_degradeTaskList.iter, &key, &value);
    }

    if (ret != GMERR_OK) {
        return ret;  // 返回GMERR_NO_DATA是合法的，所以不打印日志
    }

    *task = (QryDegradeTaskT *)value;
    return GMERR_OK;
}

Status QryGetDegradeTask(QryDegradeTaskT **task)
{
    DbSpinLock(&g_degradeTaskList.lock);
    Status ret = QryGetDegradeTaskWithLock(task);
    DbSpinUnlock(&g_degradeTaskList.lock);
    return ret;
}

static bool QryDegradeTaskListIsEmpty(void)
{
    DbSpinLock(&g_degradeTaskList.lock);
    const bool isEmpty = DbOamapUsedSize(&g_degradeTaskList.taskList) == 0;
    DbSpinUnlock(&g_degradeTaskList.lock);
    return isEmpty;
}

static void QryUpdateDegradeProgress(QryDegradeTaskT *task)
{
    DB_POINTER(task);

    task->processedTuples += task->trxTuples;

    if (task->totalTuples == 0) {
        return;
    }
    // 理论上不会出现已执行数大于总执行数,避免并发意外添加保护,进度不大于100
    if (task->processedTuples > task->totalTuples) {
        task->processedTuples = task->totalTuples;
        task->progress = QRY_DEGRADE_UPDATE_PROGRESS;
        return;
    }
    task->progress = (uint32_t)(QRY_DEGRADE_UPDATE_PROGRESS * task->processedTuples / task->totalTuples);
}

static Status QryDegradeUpdate(QryDegradeTaskT *task, QrySeContainerT *container, DbMemCtxT *memCtx)
{
    DB_POINTER(task);
    Status ret;

    DmVertexT *vertex = NULL;
    ret = DmCreateEmptyVertexWithMemCtx(memCtx, task->vertexLabel, &vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create vertex, vertexLabel=%s.", task->vertexLabel->metaCommon.metaName);
        return ret;
    }

    if (task->totalTuples == 0) {
        ret = QrySeContainerGetTupleNum(container, &task->totalTuples);
        if (ret != GMERR_OK) {
            goto EXIT;
        }
    }

    QrySeCursorT cursor = {};
    ret = QryOpenSeCursor(container, &cursor, task->beginAddr);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    HpTupleAddr addr = 0;
    uint32_t execNum = 0;
    while ((ret = QrySeCursorFetch(&cursor, vertex, &addr)) == GMERR_OK) {
        ret = QrySeContainerUpdate(container, addr, vertex);
        if (ret != GMERR_OK) {
            goto EXIT;
        }
        task->trxTuples++;
        if (DbExceedTime(task->beginTimeStamp, QRY_RECEPTION_SPLIT_TIME)) {
            goto EXIT;
        }
        // 仅用于限制事务每次持有的锁的数量,若已超时则不用计数
        execNum++;
        // 当前限制执行次数暂定为100,后续可优化
        if (execNum >= QRY_DEGRADE_MAX_ONCE_EXEC_NUM) {
            goto EXIT;
        }
    }

EXIT:
    // 刷数据失败时会回滚,下一次任务执行需重新执行,因此不修改beginAddr
    if (ret == GMERR_OK || ret == GMERR_NO_DATA) {
        QryCloseSeCursor(&cursor, &task->beginAddr);
    } else {
        QryCloseSeCursor(&cursor, NULL);
    }
    DmDestroyVertex(vertex);
    return ret;
}

static Status QryDegradeCompact(QryDegradeTaskT *task, QrySeContainerT *container)
{
    DB_POINTER(task);
    Status ret;
    QrySeCursorT cursor = {};
    ret = QryOpenSeCursorDegradeCompact(container, &cursor, task->beginAddr);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ContainerTypeE type = *(ContainerTypePtrT)(cursor.container->runHdl);
    if (container->vertexLabel->metaCommon.isUseRsm && task->beginAddr.tupleAddr == BEFORE_FIRST_TUPLE_ADDR &&
        task->beginAddr.blockId == BEFORE_FIRST_TUPLE_BLOCK_ID && type == CONTAINER_HEAP) {
        // 保留内存首次执行二阶段，设置fixRowSize
        ret = HeapLabelRecoveryInfoUpdateFixRowSize(QryRsmGetContainerInfo(container->vertexLabel->commonInfo->rsmInfo),
            (PageSizeT)DmGetFixVertexLabelLen(container->vertexLabel),
            QryRsmGetRsmUndo(container->vertexLabel->commonInfo->rsmInfo), false);
        DB_ASSERT(ret == GMERR_OK);  // 只有服务端会走降级逻辑，shmPtrToAddr不会失败
    }

    while ((ret = QrySeCursorDegradeFetchPage(&cursor)) == GMERR_OK) {
        if (DbExceedTime(task->beginTimeStamp, QRY_RECEPTION_SPLIT_TIME)) {
            goto EXIT;
        }
        // 修改：当压缩完成时，进入持久化阶段
        if (ret == GMERR_NO_DATA) {
            // 压缩阶段完成，进入持久化阶段
            task->beginAddr = (HpTupleCombineAddrT){BEFORE_FIRST_TUPLE_ADDR, BEFORE_FIRST_TUPLE_BLOCK_ID};
            task->phase = QRY_DEGRADE_PHASE_PERSIST;  // 进入持久化阶段
            return GMERR_OK;
        }
    }

EXIT:
    // 缩容若失败仍需修改beginAddr,下次执行任务从失败处开始,不会有问题
    QryCloseSeCursor(&cursor, &task->beginAddr);
    return ret;
}

// 降级持久化阶段处理
Status QryDegradePersistPhase(QryDegradeTaskT *task, SessionT *session)
{
    DB_POINTER2(task, session);

    // 步骤1：获取表信息
    DmVertexLabelT *vertexLabel = task->vertexLabel;
    if (vertexLabel == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "VertexLabel is null in persist phase");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    // 步骤2：检查是否需要持久化
    if (vertexLabel->commonInfo->isPersisted) {
        DB_LOG_DEBUG("Table %s already persisted, skipping persist phase", vertexLabel->metaCommon.metaName);
        task->phase = QRY_DEGRADE_PHASE_END;
        return GMERR_OK;
    }

    // 步骤3：执行持久化
    Status ret = ExecuteDegradePersistence(session, vertexLabel, task);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Failed to persist degrade for table %s", vertexLabel->metaCommon.metaName);
        return ret;
    }

    // 步骤4：更新进度
    task->progress = QRY_DEGRADE_PERSIST_PROGRESS;  // 95%

    // 步骤5：进入结束阶段
    task->phase = QRY_DEGRADE_PHASE_END;

    DB_LOG_DEBUG("Completed persist phase for table %s", vertexLabel->metaCommon.metaName);
    return GMERR_OK;
}

// 执行降级持久化
static Status ExecuteDegradePersistence(SessionT *session, DmVertexLabelT *vertexLabel, QryDegradeTaskT *task)
{
    // 步骤1：开始事务（如果需要）
    bool needTransaction = !SeTransIsActive(session->seInstance);
    if (needTransaction) {
        TrxCfgT cfg = {
            .readOnly = false,
            .isLiteTrx = false,
            .isBackGround = true,
            .isInteractive = false,
            .isTrxForceCommit = false,
            .isRetryTrx = false,
            .connId = DB_INVALID_UINT16,
            .trxType = TRX_TYPE_READ_COMMITTED,
            .isolationLevel = ISOLATION_READ_COMMITTED,
        };

        Status ret = SeTransBegin(session->seInstance, &cfg);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    // 步骤2：更新系统表
    Status ret = SysTableUpdateOneVertexLabel(session, vertexLabel);
    if (ret != GMERR_OK) {
        if (needTransaction) {
            (void)SeTransRollback(session->seInstance, false);
        }
        return ret;
    }

    // 步骤3：提交事务
    if (needTransaction) {
        ret = SeTransCommit(session->seInstance);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    // 步骤4：更新持久化状态
    vertexLabel->commonInfo->isPersisted = true;
    vertexLabel->commonInfo->lastPersistTime = DbClockGetTsc();

    // 步骤5：记录降级持久化统计
    RecordDegradePersistStats(vertexLabel, task);

    return GMERR_OK;
}

static Status QryDegradeProcess(QryDegradeTaskT *task, QrySeContainerT *container, DbMemCtxT *memCtx)
{
    DB_POINTER(task);
    if (task->phase == QRY_DEGRADE_PHASE_UPDATE) {
        return QryDegradeUpdate(task, container, memCtx);
    }

    if (task->phase == QRY_DEGRADE_PHASE_COMPACT) {
        return QryDegradeCompact(task, container);
    }

    if (task->phase == QRY_DEGRADE_PHASE_PERSIST) {
        ret = QryDegradePersistPhase(task, container);
    }

    return GMERR_OK;
}

static Status QryDegradeTrxBegin(QryDegradeTaskT *task, QryStmtT *stmt)
{
    DB_POINTER2(task, stmt);
    Status ret;

    DmHeapInfoT *heapInfo = &task->vertexLabel->commonInfo->heapInfo;
    TrxCfgT cfg = {
        .readOnly = false,
        .isLiteTrx = QryVertexLabelIsLiteTrx(task->vertexLabel),
        .isBackGround = false,  // 表示使用轻量化事务（链表方式，非段页方式），轻量化事务undo日志不记录删除操作。
        .isInteractive = false,
        .isTrxForceCommit = false,
        .isRetryTrx = false,
        .connId = DB_INVALID_UINT16,
        .trxType = heapInfo->trxType,
        .isolationLevel = heapInfo->isolationLevel,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
    };

    ret = SeTransBegin(stmt->session->seInstance, &cfg);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "start transaction, vertexLabel=%s.", task->vertexLabel->metaCommon.metaName);
        return ret;
    }

    // 轻量化事务，设置允许undo日志扩展
    SeTransSetDmlHint4RangeUpdate(stmt->session->seInstance);

    ret = QryAcqLatchForVertexLabel(stmt, task->vertexLabel, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "acq latch, vertexLabel=%s.", task->vertexLabel->metaCommon.metaName);
        (void)SeTransRollback(stmt->session->seInstance, false);
        return ret;
    }
    ret = QrySetRsmUndoForVertexLabel(stmt, task->vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set rsm undo rec, vertexLabel=%s.", task->vertexLabel->metaCommon.metaName);
        return ret;
    }
    return GMERR_OK;
}

static Status QryDegradeTrxEnd(QryDegradeTaskT *task, QryStmtT *stmt, Status ret)
{
    DB_POINTER2(task, stmt);
    bool eof = ret == GMERR_NO_DATA;
    if (ret == GMERR_OK || ret == GMERR_NO_DATA) {
        ret = SeTransCommit(stmt->session->seInstance);
    }

    // 提交成功，才能更新进度和状态
    if (ret == GMERR_OK) {
        QryUpdateDegradeProgress(task);
        if (eof) {
            task->beginAddr = (HpTupleCombineAddrT){BEFORE_FIRST_TUPLE_ADDR, BEFORE_FIRST_TUPLE_BLOCK_ID};
            task->phase++;
        }
        task->trxTuples = 0;
    } else {
        task->trxTuples = 0;
        DB_LOG_ERROR(ret, "degrade commit, vertexLabel=%s, trxId=%" PRIu64 ".", task->vertexLabelName,
            ((TrxT *)stmt->session->seInstance->trx)->base.trxId);
        ret = SeTransRollback(stmt->session->seInstance, false);
    }

    QryReleaseAllLabelLatch(stmt->session);
    return ret;
}

static Status QryDegradeTrx(QryDegradeTaskT *task, QryStmtT *stmt)
{
    DB_POINTER2(task, stmt);
    Status ret;

    QrySeContainerT container = {};  // 必须放在goto EXIT前面

    // 首次执行
    if (task->phase == QRY_DEGRADE_PHASE_INIT) {
        ret = QryPrepareDegradeUpdate(task);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    // 开启事务+加表锁
    ret = QryDegradeTrxBegin(task, stmt);
    if (ret != GMERR_OK) {
        return ret;
    }  // 从这行之后才能goto

    // 降级合并
    if (task->vertexLabel->metaCommon.isDeleted) {
        ret = QryPrepareDegradeUpdate(task);
        if (ret != GMERR_OK) {
            goto EXIT;
        }
        DB_LOG_INFO("Degrade vertex label task merge, name=%s.", task->vertexLabelName);
    }

    ret =
        QryOpenSeContainer(stmt->session->seInstance, stmt->memCtx, task->vertexLabel, HEAP_OPTYPE_UPDATE, &container);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "OpenSeContainer, label name=%s, trxId=%" PRIu64 ".", task->vertexLabelName,
            ((TrxT *)stmt->session->seInstance->trx)->base.trxId);
        goto EXIT;
    }

    if (container.isChLabel && !task->isInitScanPin) {
        ret = ChLabelPin((ChLabelRunHdlT)container.runHdl);  // 必须在表锁里面
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "ChLabel scan pin, name=%s, trxId=%" PRIu64 ".", task->vertexLabelName,
                ((TrxT *)stmt->session->seInstance->trx)->base.trxId);
            goto EXIT;
        }
        task->isInitScanPin = true;
    }

    ret = QryDegradeProcess(task, &container, stmt->memCtx);
    // task phase的更新是在QryDegradeTrxEnd中, 这里必然不会等于QRY_DEGRADE_PHASE_END
    if (container.isChLabel && task->phase == QRY_DEGRADE_PHASE_COMPACT && task->isInitScanPin) {
        ChLabelUnPin((ChLabelRunHdlT)container.runHdl);  // 必须在表锁里面
        task->isInitScanPin = false;
    }
EXIT:
    ret = QryDegradeTrxEnd(task, stmt, ret);
    QryCloseSeContainer(&container);  // 没打开关闭不会有问题
    return ret;
}

static Status QryDegradeStmt(QryDegradeTaskT *task, SessionT *session)
{
    DB_POINTER2(task, session);
    Status ret;

    QryStmtT *stmt = NULL;
    ret = QryAllocStmt(session, &stmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryAllocCtxFromCtxMemPool(NULL, &stmt->context);
    if (ret != GMERR_OK) {
        QryReleaseStmt(stmt);
        return ret;
    }
    stmt->context->type = QRY_TYPE_UPDATE_VERTEX;
    ret = QryDegradeTrx(task, stmt);
    QryReleaseStmt(stmt);
    return ret;
}

bool QryDegradeTaskProc(void)
{
    if (g_degradeTaskList.session != NULL) {
        QryClearInvalidCache(g_degradeTaskList.session);
        QryClearInvalidSubsCache(g_degradeTaskList.session);
    }
    if (QryDegradeTaskListIsEmpty()) {
        return true;
    }

    Status ret;
    if (SECUREC_UNLIKELY(g_degradeTaskList.session == NULL)) {
        // 内存释放点:FreeDegradeTaskList
        ret = QryCreateBgTaskSession("DegradeTask", &g_degradeTaskList.session);
        if (ret != GMERR_OK) {
            return false;
        }
    }

    QryDegradeTaskT *task = NULL;
    ret = QryGetDegradeTask(&task);
    if (ret != GMERR_OK) {  // 这里不可能取不到任务
        DB_LOG_ERROR(ret, "get DegradeBGTask.");
        return false;  // 下一次重新尝试
    }
    task->beginTimeStamp = DbClockGetTsc();
    while (g_degradeTaskList.session != NULL &&
           !DbExceedTime(task->beginTimeStamp, g_degradeTaskList.session->splitTime)) {
        ret = QryDegradeStmt(task, g_degradeTaskList.session);
        if (ret != GMERR_OK || task->phase == QRY_DEGRADE_PHASE_END) {
            break;
        }
    }
    if (ret == GMERR_UNDEFINED_TABLE || task->phase == QRY_DEGRADE_PHASE_END) {
        QryRmvDegradeTask(task, ret == GMERR_UNDEFINED_TABLE);
        ret = GMERR_OK;
    }
    return ret != GMERR_OK;
}

// 只能在主线程初始化流程中调用
Status QryInitDegradeTaskList(void)
{
    DbMemCtxT *sysDynCtx = DbSrvGetSysDynCtx(DbGetProcGlobalId());
    if (sysDynCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "get sys dyn memctx.");
        return GMERR_INTERNAL_ERROR;
    }

    // memCtx用途：表降级后台任务使用
    // 生命周期：专项任务生命周期
    // 释放方式：就近释放
    // 兜底清空措施：无
    DbMemCtxArgsT args = {0};
    args.liteModOn = true;
    g_degradeTaskList.memCtx = DbCreateDynMemCtx(sysDynCtx, true, "top memory context of degrade task.", &args);
    if (g_degradeTaskList.memCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "create dynamic memctx.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    g_degradeTaskList.session = NULL;
    DbSpinInit(&g_degradeTaskList.lock);
    Status ret = DbOamapInit(&g_degradeTaskList.taskList, 0, DbOamapUint32Compare, g_degradeTaskList.memCtx, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "init degradeTaskList.");
        DbDeleteDynMemCtx(g_degradeTaskList.memCtx);
        g_degradeTaskList.memCtx = NULL;
        return ret;
    }
    g_degradeTaskList.inited = true;
    return GMERR_OK;
}

static Status QryAddToDegradeTaskListWithoutLock(
    uint32_t vertexLabelId, DmVertexLabelT *dropVertexLabel, uint32_t targetVersion)
{
    QryDegradeTaskT *task = DbOamapLookup(&g_degradeTaskList.taskList, vertexLabelId, &vertexLabelId, NULL);
    if (task != NULL) {
        return QryCreateBgTaskService();
    }
    // Map里不存在
    // 内存释放点:QryDestroyDegradeTask
    task = DbDynMemCtxAlloc(g_degradeTaskList.memCtx, sizeof(QryDegradeTaskT));
    if (task == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "allocate degradeTask.");
        return GMERR_OUT_OF_MEMORY;
    }

    *task = (QryDegradeTaskT){
        .vertexLabelId = vertexLabelId,
        .targetVersion = targetVersion,
        .originVersion = dropVertexLabel->metaCommon.version,
        .namespaceId = dropVertexLabel->metaCommon.namespaceId,
        .vertexLabelNameLen = DM_STR_LEN(dropVertexLabel->metaCommon.metaName),
        .degradedTimes = dropVertexLabel->commonInfo->degradedTimes,
    };
    errno_t err = strcpy_s(task->vertexLabelName, MAX_TABLE_NAME_LEN, dropVertexLabel->metaCommon.metaName);
    // 预期拷贝不会失败
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Copy vertexLabel name.");
        DbDynMemCtxFree(g_degradeTaskList.memCtx, task);
        return GMERR_DATA_EXCEPTION;
    }
    Status ret = DbOamapInsert(&g_degradeTaskList.taskList, vertexLabelId, &task->vertexLabelId, task, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "insert oamap, vertexLabelId=%" PRIu32 ".", vertexLabelId);
        DbDynMemCtxFree(g_degradeTaskList.memCtx, task);
        return ret;
    }
    ret = QryCreateBgTaskService();
    if (ret != GMERR_OK) {
        DbOamapRemove(&g_degradeTaskList.taskList, vertexLabelId, &task->vertexLabelId);
        DbDynMemCtxFree(g_degradeTaskList.memCtx, task);
        return ret;
    }
    QryBgTaskAgentSemPost();
    return GMERR_OK;
}

// 添加任务
Status QryAddToDegradeTaskListImpl(uint32_t vertexLabelId, DmVertexLabelT *dropVertexLabel, uint32_t targetVersion)
{
    DbSpinLock(&g_degradeTaskList.lock);
    Status ret = QryAddToDegradeTaskListWithoutLock(vertexLabelId, dropVertexLabel, targetVersion);
    DbSpinUnlock(&g_degradeTaskList.lock);
    return ret;
}

Status QryGetDegradeProgressWithoutLock(uint32_t vertexLabelId, uint32_t *progress)
{
    DB_POINTER(progress);
    QryDegradeTaskT *task = DbOamapLookup(&g_degradeTaskList.taskList, vertexLabelId, &vertexLabelId, NULL);
    if (task == NULL) {
        return GMERR_NO_DATA;  // 正常情况不应该记录日志
    }

    *progress = task->progress;
    return GMERR_OK;
}

// 查询进度
Status QryGetDegradeProgressImpl(uint32_t vertexLabelId, uint32_t *progress)
{
    DbSpinLock(&g_degradeTaskList.lock);
    Status ret = QryGetDegradeProgressWithoutLock(vertexLabelId, progress);
    DbSpinUnlock(&g_degradeTaskList.lock);
    return ret;
}

// 给视图提供迭代器接口, 这里只读数据，暂时不考虑和后台任务的并发和原子性，数据可能不准确。
Status QryFetchDegradeTaskInfoWithoutLock(QryDegradeTaskInfoIteratorT *iter, QryDegradeTaskInfoT *info)
{
    DB_POINTER2(iter, info);
    Status ret;
    void *key = NULL;
    void *value = NULL;
    ret = DbOamapFetch(&g_degradeTaskList.taskList, iter, &key, &value);
    if (ret != GMERR_OK) {
        if (ret != GMERR_NO_DATA) {
            DB_LOG_AND_SET_LASERR(ret, "fetch task.");
        }
        return ret;
    }

    QryDegradeTaskT *task = value;
    // 后台任务更新的时候没有加锁，不能保证原子性
    *info = (QryDegradeTaskInfoT){
        .targetVersion = task->targetVersion,
        .originVersion = task->originVersion,
        .totalTuples = task->totalTuples,
        .processedTuples = task->processedTuples,
        .progress = task->progress,
        .namespaceId = task->namespaceId,
        .degradedTimes = task->degradedTimes,
        .vertexLabelNameLen = task->vertexLabelNameLen,
        .vertexLabelName = task->vertexLabelName,
    };
    return GMERR_OK;
}

Status QryFetchDegradeTaskInfoImpl(QryDegradeTaskInfoIteratorT *iter, QryDegradeTaskInfoT *info)
{
    DbSpinLock(&g_degradeTaskList.lock);
    Status ret = QryFetchDegradeTaskInfoWithoutLock(iter, info);
    DbSpinUnlock(&g_degradeTaskList.lock);
    return ret;
}

void RegDegradeTaskFunctions(ImportFastPathFunctionsT *fastPathFunctions)
{
    fastPathFunctions->qryAddToDegradeTaskList = QryAddToDegradeTaskListImpl;
    fastPathFunctions->qryGetDegradeProgress = QryGetDegradeProgressImpl;
    fastPathFunctions->qryFetchDegradeTaskInfo = QryFetchDegradeTaskInfoImpl;
}

#ifdef __cplusplus
}
#endif
