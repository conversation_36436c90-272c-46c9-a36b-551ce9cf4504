/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: header file of fastpath background task scheduler.
 * Author: <PERSON><PERSON><PERSON>
 * Create: 2024-07-03
 */

#ifndef EE_BG_TASK_AGENT_H
#define EE_BG_TASK_AGENT_H

#include "ee_session.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef enum QryBgTaskNewType {
    BG_TASK_DEGRADE,
    BG_TASK_DROP_RES,
    BG_TASK_CHECK,
#ifdef FEATURE_YANG
    BG_TASK_YANG_RECYCLE,  // yang回收自增ID的后台任务
#endif
#ifdef FEATURE_PERSISTENCE
    BG_TASK_UP_OR_DEGRADE, // 升降级表延迟持久化的后台任务
#endif
    BG_TASK_BUTT,
} QryBgTaskAgentTypeE;

typedef bool (*QryBgWorkerProc)(void);

bool QryAgedTaskProc(void);

bool QryDegradeTaskProc(void);

bool QryDropResTaskProc(void);

bool QryYangRecycleTaskProc(void);

/**
 * @brief Background task post sem to wake task agent thread to deal with related task.
 */
void QryBgTaskAgentSemPost(void);

/**
 * @brief Create task agent service thread
 * @attention task allocated need to lazy load the service
 */
Status QryCreateBgTaskService(void);

/**
 * @brief Background task alloc session with this method
 * @param taskName for log info
 * @param session output new background session
 */
Status QryCreateBgTaskSession(const char *taskName, SessionT **session);

#ifdef __cplusplus
}
#endif

#endif /* EE_BG_TASK_AGENT_H */
