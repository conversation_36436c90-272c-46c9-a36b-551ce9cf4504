/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: ee path search implementation.
 * Author: GQL
 * Create: 2022-11-07
 */

#include "ee_gql_search.h"
#include "dm_meta_complex_path.h"
#include "ee_plan.h"
#include "se_hac_common.h"
#ifdef __cplusplus
extern "C" {
#endif

#define BUFFER_SIZE_DML 1000
#define BIT_IS_DELETE 0
#define BIT_IS_INSERT 1
#define BIT_IS_REPLACE 2
/* *********************************Path Search Execute Begin********************************* */

static inline bool IsValidRootRange(PositionT *position)
{
    DB_POINTER(position);
    return !(
        position->start == DB_INVALID_UINT32 || position->end == DB_INVALID_UINT32 || position->start > position->end);
}

static Status InitTasksOneWaySearchInfo(DbMemCtxT *memCtx, AAT *ir)
{
    DB_POINTER2(memCtx, ir);
    Status ret = OneWaySearchInfoInit(memCtx, &ir->topo.info.oneWaySearchInfo);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "oneWaySearchInfo alloc");
        return ret;
    }
    return ret;
}

static void DestroyTasksOneWaySearchInfo(DbMemCtxT *memCtx, AAT *ir)
{
    DB_POINTER2(memCtx, ir);
    OneWaySearchInfoDestroy(memCtx, &ir->topo.info.oneWaySearchInfo);
}

static Status FillTriggerData(DbMemCtxT *memCtx, SubSearchTaskT *psTask, AAT *ir)
{
    DB_POINTER3(memCtx, psTask, ir);
    Status ret;
    uint32_t slotNum = psTask->oplog.slotNum;
    AASlotT **slot = psTask->oplog.slot;
    for (uint32_t i = 0; i < slotNum; i++) {
        ret = AAPushPropSlot(ir, slot[i]);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    ir->topo.info.lastLevel = (PositionT){.start = 0, .end = psTask->oplog.slotNum - 1};
    return GMERR_OK;
}

static void PreparePreAllocLists(PathSearchTaskNewT *task, AAT *ir)
{
    DbListT *lists = task->preAllocLists;
    for (uint32_t i = 0; i < task->preAllocListCnt; i++) {
        DbClearList(&lists[i]);
    }
    ir->preAllocLists = task->preAllocLists;
}

static FusionModelQryStmtT InitFusionStmt(PathSearchTaskNewT *task, SubSearchTaskT *psTask)
{
    return (FusionModelQryStmtT){.dbId = task->readViewAfter->dbId,
        .namespaceId = task->readViewAfter->namespaceId,
        .session = task->readViewAfter,
        .prevSession = task->readViewBefore,
        .memCtx = task->eeMemCtx,
        .trxMemCtx = task->eeMemCtx,
        .planTree = psTask->plan->planTree,
        .utilityStmt = NULL,
        .utilityResultStmt = NULL,
        .planState = NULL,
        .seInstance = task->readViewAfter->seInstance,
        .totalParamNum = 0,
        .isPathDelRerun = psTask->isRerun};
}

static Status ExecuteSinglePathSearch(
    PathSearchTaskNewT *task, PathPvcTaskT *pvcTask, SubSearchTaskT *psTask, bool hasPreAllocLists)
{
    DB_POINTER4(task, task->eeMemCtx, pvcTask, psTask);
    // do NOT search the path pattern if it is an empty southbound task
    if (task->taskType == SOUTH_SMOOTH_OR_RECONCILIATION && psTask->pathInfo == NULL) {
        return GMERR_NO_DATA;
    }
    // NORMAL_TASK but doesn't have trigger data
    if (psTask->oplog.slotNum == 0) {
        psTask->ir.topo.info.rootVertexEle = (PositionT){.start = DB_INVALID_ID32, .end = 0};
        return GMERR_NO_DATA;
    }

    AAInfoT info = {.memCtx = task->eeMemCtx, .preAllocBuf = task->preAllocBuf, .needTopo = true};
    AAT *currIr = psTask->isRerun ? &psTask->supportIr : &psTask->ir;

    Status ret = NewAAWithPreAllocBuf(&info, currIr, AA_SLOT_NUM_SEARCH_TASK, hasPreAllocLists);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    currIr->prop.isReRun = psTask->isRerun ? true : false;

    // init member use for incremental search
    ret = InitTasksOneWaySearchInfo(task->eeMemCtx, currIr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // set trigger data for execute plan
    ret = FillTriggerData(task->eeMemCtx, psTask, currIr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (hasPreAllocLists) {
        PreparePreAllocLists(task, currIr);
    }

    FusionModelQryStmtT fusionStmt = InitFusionStmt(task, psTask);
    // execute the plan
    ret = ExecutorStart(&fusionStmt);
    if (ret != GMERR_OK) {
        ExecutorFinish(&fusionStmt);
        DestroyTasksOneWaySearchInfo(task->eeMemCtx, currIr);
        return ret;
    }
#ifdef ACCELERATOR_MODE_WITH_GFU
    DB_POINTER(task->execRunCtx);
    ExecProcNodeByGfu(task->execRunCtx, (void *)fusionStmt.planState, (void *)currIr);
#else
    ret = ExecProcNode(fusionStmt.planState, currIr);
    if (currIr->topo.info.oneWaySearchInfo.vertexLabelFilteredCnt > 0) {
        psTask->usedOneWayEdge = true;
    }
#endif
    ExecutorFinish(&fusionStmt);
    DestroyTasksOneWaySearchInfo(task->eeMemCtx, currIr);
    return ret;
}

static bool NeedRerunForDeletion(PathPvcTaskT *pvcTask, SubSearchTaskT *psTask)
{
    /*
     * replace needn't rerun
     * delete mode 1 needn't rerun
     * delete mode 2 needn't rerun
     * if first back trace meet oneway edge needn't rerun
     */
    if (pvcTask->subEvent != DM_SUBS_EVENT_DELETE || psTask->pathInfo->deletionMode < PATH_DEL_MODE_3 ||
        psTask->usedOneWayEdge) {
        return false;
    }
    /* path instance invalid => invalid needn't return rerun */
    PositionT *rootRange = &psTask->ir.topo.info.rootVertexEle;
    if (!IsValidRootRange(rootRange)) {
        return false;
    }
    VertexElementT **rootArray = psTask->ir.topo.rowArray;
    for (uint32_t rootIdx = rootRange->start; rootIdx <= rootRange->end; ++rootIdx) {
        if (rootArray[rootIdx]->status != ENTITY_FLAG_DELETED) {
            return true;
        }
    }
    return false;
}

Status BatchExecutePathSearch(PathSearchTaskNewT *task, bool hasPreAllocLists)
{
    DB_POINTER(task);
    Status ret;
    uint32_t pvcTaskCnt = DbListGetItemCnt(&task->pvcTaskList);
    for (uint32_t i = 0; i < pvcTaskCnt; i++) {
        // execute each pvc task of path search task
        PathPvcTaskT *pvcTask = (PathPvcTaskT *)DbListItem(&task->pvcTaskList, i);
        uint32_t pathPatternCnt = DbListGetItemCnt(pvcTask->psTasks);
        for (uint32_t j = 0; j < pathPatternCnt; j++) {
            /*
             * 1. execute all plans of one pvc task
             * 2. result of executing plans also store in SubSearchTaskT
             * 3. store root nodes of all valid path instance in SubSearchTaskT.ir
             */
            SubSearchTaskT *currTask = (SubSearchTaskT *)DbListItem(pvcTask->psTasks, j);
            ret = ExecuteSinglePathSearch(task, pvcTask, currTask, hasPreAllocLists);
            if (ret == GMERR_NO_DATA) {
                continue;
            }
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_ERROR(ret, "valid check of path: %s", currTask->pathInfo->metaCommon.metaName);
                return ret;
            }
            if (!NeedRerunForDeletion(pvcTask, currTask)) {
                continue;
            }
            currTask->isRerun = true;
            ret = ExecuteSinglePathSearch(task, pvcTask, currTask, false);
            if (ret == GMERR_NO_DATA) {
                continue;
            }
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "valid check on path: %s", currTask->pathInfo->metaCommon.metaName);
                return ret;
            }
        }
    }
    return GMERR_OK;
}

inline void DestroySubSearchTaskList(DbMemCtxT *eeMemCtx, DbListT *psTasks)
{
    DbDestroyList(psTasks);
    DbDynMemCtxFree(eeMemCtx, psTasks);
}

inline void DestroyPvcTask(PathPvcTaskT *onePvcTask)
{
    DestroySubSearchTaskList(onePvcTask->eeMemCtx, onePvcTask->psTasks);
}

void DestroyPvcTaskListContents(DbListT *pvcTaskList)
{
    for (uint32_t k = 0; k < DbListGetItemCnt(pvcTaskList); k++) {
        PathPvcTaskT *onePvcTask = (PathPvcTaskT *)DbListItem(pvcTaskList, k);
        DestroyPvcTask(onePvcTask);
    }
}

/* *********************************Path Search Execute End********************************* */

/**
 * @brief Evaluates the query stored in @p stmt and stores its result in the associative array pointed by @p pAA.
 *
 * @param[in] stmt The pointer to the query statement that is evaluated.
 * @param[in/out] pAA The pointer to the associative array that is preallocated and filled with required data.
 * @return Status GMERR_OK, otherwise an error code.
 */
static Status ExecuteMergeOrReplacePlan(GqlQryStmtT *stmt, AAT *pAA)
{
    DB_POINTER2(stmt, pAA);
    Status ret = GMERR_OK;
    FusionModelQryStmtT base;

    base = stmt->base;
    // init member use for incremental search
    ret = InitTasksOneWaySearchInfo(base.memCtx, pAA);
    if (ret != GMERR_OK) {
        return ret;
    }

    FusionModelQryStmtT fusionStmt = (FusionModelQryStmtT){.dbId = base.dbId,
        .namespaceId = base.namespaceId,
        .session = base.session,
        .prevSession = base.prevSession,
        .memCtx = base.memCtx,
        .trxMemCtx = base.trxMemCtx,
        .planTree = base.planTree,
        .totalParamNum = 0,
        .utilityStmt = NULL,
        .utilityResultStmt = NULL,
        .planState = NULL,
        .seInstance = base.seInstance,
        .isPathDelRerun = false};
    ret = ExecutorStart(&fusionStmt);
    if (ret != GMERR_OK) {
        ExecutorFinish(&fusionStmt);
        return ret;
    }
    ret = ExecProcNode(fusionStmt.planState, pAA);
    if (ret != GMERR_OK) {
        if (ret == GMERR_NO_DATA) {
            ret = GMERR_OK;
        } else {
            return ret;
        }
    }
    ExecutorFinish(&fusionStmt);

    return ret;
}

/**
 * @brief For a specific vertex element @p pVertexElement belonging to the associative array @p pAA ,
 * return the pointer to the corresponding associative array slot.
 *
 * @param[in] pAA The pointer to the associative array.
 * @param[out] pVertexElement The pointer to the vertex element.
 *
 * @return AASlotT* The pointer to the associative array slot.
 */
static AASlotT *GetAASlotByElement(AAT *pAA, VertexElementT *pVertexElement)
{
    if (pVertexElement == NULL) {
        return NULL;
    }
    return pAA->prop.slots[pVertexElement->propeIndex];
}

/**
 * @brief Fills out the header (GeneralHeader) of the buffer in the insert for the general specification:
 * GeneralHeader | InsertDmls.
 * GeneralHeader: RpcMsgHeader | BatchHeaderT | InsertDMLs
 * InsertDmls: BatchCmdHeaderT | buff | ... | BatchCmdHeaderT | buff
 *
 * @param[in/out] pFixBuffer The pointer to the fixed buffer that is filled.
 * @param[in] numJoinRows The number of rows that are to be inserted.
 * @return Status GMERR_OK if everything went well, an error otherwise.
 */
static Status FillHeaderOfFixBufferForDMLs(FixBufferT *pFixBuffer, uint32_t numJoinRows)
{
    Status ret = GMERR_OK;
    BatchHeaderT batchHeader;
    uint32_t offset;

    batchHeader.batchOrder = GMC_BATCH_ORDER_HYBRID;
    batchHeader.totalOpNum = numJoinRows;
    // Fill RpcMsgHeader.
    offset = MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE;
    FixBufInitPut(pFixBuffer, offset);  // move write pos
    FixBufSeek(pFixBuffer, offset);     // move read pos

    // Fill BatchHeaderT.
    ret = FixBufPutData(pFixBuffer, &batchHeader, sizeof(batchHeader));

    return ret;
}

/**
 * @brief Fills out the header for insert DMLs (BatchCmdHeaderT).
 * BatchCmdHeaderT: MSG_OP_RPC_REPLACE_VERTEX | vertexLabelId | vertexLabelSchemaVersion | uuid | vertexNum (1).
 *
 * @param[in/out] pFixBuffer The pointer to the fixed buffer that is filled.
 * @param[in] pVertexLabel The pointer to the vertex label to which inserted vertex belongs to.
 * @return Status GMERR_OK if everything went well, an error otherwise.
 */
static Status FillOneRowFixBufferForInsertDMLHeader(FixBufferT *pFixBuffer, DmVertexLabelT *pVertexLabel)
{
    Status ret = GMERR_OK;

    /**** BatchCmdHeaderT Start ***/
    ret = FixBufPutUint32(pFixBuffer, MSG_OP_RPC_REPLACE_VERTEX);
    if (ret != GMERR_OK) {
        return ret;
    }
    // Fills vertexLabelId.
    ret = FixBufPutUint32(pFixBuffer, pVertexLabel->metaCommon.metaId);
    if (ret != GMERR_OK) {
        return ret;
    }
    // Fills vertexLabelSchemaVersion.
    ret = FixBufPutUint32(pFixBuffer, pVertexLabel->metaCommon.version);
    if (ret != GMERR_OK) {
        return ret;
    }
    // Fills uuid.
    ret = FixBufPutUint32(pFixBuffer, pVertexLabel->metaVertexLabel->uuid);
    if (ret != GMERR_OK) {
        return ret;
    }
    // Fills vertexNum.
    ret = FixBufPutUint32(pFixBuffer, 1);
    if (ret != GMERR_OK) {
        return ret;
    }
    /**** BatchCmdHeaderT end ***/
    return ret;
}

/**
 * @brief Fills out the buffer where the vertex @p pVertex belonging to @p pVertexLabel with the specification:
 * BatchCmdHeaderT | buff
 * BatchCmdHeaderT: opCode | vertexLabelId | vertexSchemaVersion | uuid | vertexNum
 * buff: vertexLen | vertex | operateEdgeFlag
 *
 * @param[in/out] pFixBuffer The pointer to the fixed buffer that is filled.
 * @param[in] pHeapBuffer The tuple to the fixed buffer that is filled.
 * @return Status GMERR_OK if everything went well, an error otherwise.
 */
static Status FillOneRowFixBufferForInsertDML(FixBufferT *pFixBuffer, DmVertexLabelT *pVertexLabel, DmVertexT *pVertex)
{
    Status ret = GMERR_OK;
    HeapTupleBufT heapTupBuffer;

    ret = FillOneRowFixBufferForInsertDMLHeader(pFixBuffer, pVertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = DmSerializeVertex(pVertex, &heapTupBuffer.buf, &heapTupBuffer.bufSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    /**** buff Start ***/
    // Fills the vertex size.
    ret = FixBufPutUint32(pFixBuffer, heapTupBuffer.bufSize);
    if (ret != GMERR_OK) {
        return ret;
    }

    // Fills the vertex itself.
    ret = FixBufPutData(pFixBuffer, heapTupBuffer.buf, heapTupBuffer.bufSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    // Fills the operateEdge.
    ret = FixBufPutUint32(pFixBuffer, 0);
    /**** buff end ***/

    return ret;
}

/**
 * @brief Inserts primary key (index key) to the buffer @p pFixBuffer.
 *
 * @param[in/out] pFixBuffer The pointer to the fixed buffer that is filled.
 * @param[in] pIndexKey The pointer to the primary key that is inserted.
 * @return Status GMERR_OK if everything went well, an error otherwise.
 */
static Status HelpFillIndexKey(FixBufferT *pFixBuffer, const DmIndexKeyT *pIndexKey)
{
    Status ret = GMERR_OK;
    uint32_t len;
    uint32_t offset;
    uint8_t *vbuf;

    if (pIndexKey == NULL) {
        ret = FixBufPutUint32(pFixBuffer, 0);
        return ret;
    }
    len = DmIndexKeyGetSeriBufLength(pIndexKey);
    // fill pIndexKey len
    ret = FixBufPutUint32(pFixBuffer, len);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill pIndexKey
    ret = FixBufReserveDataOffset(pFixBuffer, len, &offset);
    if (ret != GMERR_OK) {
        return ret;
    }
    vbuf = (uint8_t *)FixBufOffsetToAddr(pFixBuffer, offset);
    ret = DmSerializeIndexKey2InvokerBuf(pIndexKey, len, vbuf);
    return ret;
}

/**
 * @brief Fills out the header for delete DMLs (BatchCmdHeaderT).
 * BatchCmdHeaderT: MSG_OP_RPC_DELETE_VERTEX | vertexLabelId | vertexSchemaVersion | uuid | indexId | condStr.
 *
 * @param[in/out] pFixBuffer The pointer to the fixed buffer that is filled.
 * @param[int] pVertexLabel The pointer to the vertex label to which inserted vertex belongs to.
 * @return Status GMERR_OK if everything went well, an error otherwise.
 */
static Status FillOneRowFixBufferForDeleteDMLHeader(FixBufferT *pFixBuffer, DmVertexLabelT *pVertexLabel)
{
    Status ret = GMERR_OK;

    /**** BatchCmdHeaderT Start ***/
    ret = FixBufPutUint32(pFixBuffer, MSG_OP_RPC_DELETE_VERTEX);
    if (ret != GMERR_OK) {
        return ret;
    }
    // Fills vertexLabelId.
    ret = FixBufPutUint32(pFixBuffer, pVertexLabel->metaCommon.metaId);
    if (ret != GMERR_OK) {
        return ret;
    }
    // Fills vertexLabelSchemaVersion.
    ret = FixBufPutUint32(pFixBuffer, pVertexLabel->metaCommon.version);
    if (ret != GMERR_OK) {
        return ret;
    }
    // Fills uuid.
    ret = FixBufPutUint32(pFixBuffer, pVertexLabel->metaVertexLabel->uuid);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill indexIndex
    ret = FixBufPutUint32(pFixBuffer, pVertexLabel->metaVertexLabel->pkIndex->indexId);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill cond str
    ret = FixBufPutUint32(pFixBuffer, 0);
    if (ret != GMERR_OK) {
        return ret;
    }
    /**** BatchCmdHeaderT end ***/
    return ret;
}

/**
 * @brief Allocates the required data for storing primary key info and stores it in @p ppIndexKey.
 *
 * @param[in] memCtx The pointer to the memory context used for allocations.
 * @param[in/out] pVertex The pointer to the vertex whose primary key is extracted.
 * @param[in] pVertexLabel The pointer to the vertex label to which a vertex belongs to.
 * @param[out] ppIndexKey The pointer to a pointer containing primary key information.
 * @return Status GMERR_OK if everything went well, an error otherwise.
 *
 * @note the memory allocated in @p pVertex->vertexKeyBuf will be released alongside the vertex itself.
 */
static Status DmVertexGetPrimaryKey(
    DbMemCtxT *memCtx, DmVertexT *pVertex, DmVertexLabelT *pVertexLabel, DmIndexKeyT **ppIndexKey)
{
    Status ret = GMERR_OK;
    DmIndexKeyT *pIndexKey;

    pIndexKey = *ppIndexKey;
    ret = DmCreateEmptyIndexKeyByIdxLabelWithMemCtx(memCtx, pVertexLabel->metaVertexLabel->pkIndex, &pIndexKey);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmGetKeyBufFromVertex(
        pVertex, pVertexLabel->metaVertexLabel->pkIndex->indexId, &pIndexKey->keyBuf, &pIndexKey->keyBufLen);
    if (ret != GMERR_OK) {
        DmDestroyIndexKey(pIndexKey);
        return ret;
    }
    *ppIndexKey = pIndexKey;
    return ret;
}

/**
 * @brief Fills out the buffer in the delete with the specification:
 * BatchCmdHeaderT | buff
 * BatchCmdHeaderT: opCode | vertexLabelId | vertexSchemaVersion | uuid | indexId | condStr
 * buff: deleteNum | rangeScanFlag | leftIndexKey | rightIndexKey | structFilter | operateEdgeFlag
 *
 * @param[in/out] pFixBuffer The pointer to the fixed buffer that is filled.
 * @param[in] pHeapBuffer The tuple to the fixed buffer that is filled.
 * @return Status GMERR_OK if everything went well, an error otherwise.
 */
static Status FillOneRowFixBufferForDeleteDML(
    DbMemCtxT *memCtx, FixBufferT *pFixBuffer, DmVertexLabelT *pVertexLabel, DmVertexT *pVertex)
{
    Status ret = GMERR_OK;
    DmIndexKeyT *pIndexKey;

    ret = FillOneRowFixBufferForDeleteDMLHeader(pFixBuffer, pVertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    // Fills delete number.
    ret = FixBufPutUint32(pFixBuffer, 1);
    if (ret != GMERR_OK) {
        return ret;
    }
    // Fills range scan flag.
    ret = FixBufPutUint32(pFixBuffer, 0);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmVertexGetPrimaryKey(memCtx, pVertex, pVertexLabel, &pIndexKey);
    if (ret != GMERR_OK) {
        return ret;
    }
    // Fills left index key.
    ret = HelpFillIndexKey(pFixBuffer, pIndexKey);
    if (ret != GMERR_OK) {
        goto CLEAN_INDEX_KEY;
    }
    // Fills right index key.
    ret = FixBufPutUint32(pFixBuffer, 0);
    if (ret != GMERR_OK) {
        goto CLEAN_INDEX_KEY;
    }
    // Fills struct filter.
    ret = FixBufPutUint32(pFixBuffer, 0);
    if (ret != GMERR_OK) {
        goto CLEAN_INDEX_KEY;
    }
    // Fills operate edge flag.
    ret = FixBufPutUint32(pFixBuffer, 0);
    if (ret != GMERR_OK) {
        goto CLEAN_INDEX_KEY;
    }
CLEAN_INDEX_KEY:
    DmDestroyIndexKey(pIndexKey);
    return ret;
}

/**
 * @struct AAPathIterator
 * @brief This structure stores the internal state of the volcano iterator that iterates over the associative array.
 * The associative array does not contain cycles.
 */
typedef struct AAPathIterator {
    AAT *pAA;                   // A pointer to the associative array that we are iterating over.
    DbListT lpVertexElems;      // A list of vertex elements belonging to the path. item<VertexElementT*>.
    DbListT lIndsOutEdgesInAA;  // A list of positions in outEdges for each of the currently iterated elements
                                //  in lpVertexElems. item<uint32_t>.
    uint32_t curRootPos;        // A position in the AA of the current root element in the topo.rowArray;
    uint32_t pathLayerMax;      // A maximal length of the path. start from 1
    uint32_t curPathLayer;      // The length of the currently iterated path.
} AAPathIteratorT;

static Status AAPathIteratorOpen4MergeReplace(
    DbMemCtxT *memCtx, AAT *pAA, uint32_t pathLayerMax, AAPathIteratorT *pIterator)
{
    Status ret = GMERR_OK;
    pIterator->pAA = pAA;
    pIterator->pathLayerMax = pathLayerMax;
    pIterator->curRootPos = pAA->topo.info.rootVertexEle.start;
    // release in AAPathIteratorClose
    DbCreateList(&pIterator->lpVertexElems, sizeof(VertexElementT *), memCtx);
    DbCreateList(&pIterator->lIndsOutEdgesInAA, sizeof(uint32_t), memCtx);
    // prepare placeholder for vertex element and edge element of one path instance
    for (uint32_t vertexId = 0; vertexId < pathLayerMax; vertexId++) {
        VertexElementT *ptr = NULL;
        uint32_t edgeIndOut = DB_INVALID_UINT32;
        ret = DbAppendListItem(&pIterator->lpVertexElems, &ptr);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DbAppendListItem(&pIterator->lIndsOutEdgesInAA, &edgeIndOut);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

static void AAPathIteratorClose(AAPathIteratorT *pIterator)
{
    DB_POINTER(pIterator);
    DbDestroyList(&pIterator->lIndsOutEdgesInAA);
    DbDestroyList(&pIterator->lpVertexElems);
}

static Status AAPathIteratorNext4ReplaceInner(
    AAPathIteratorT *pIterator, uint32_t pathLayer, VertexElementT *vertexElement, bool *pPathFound)
{
    Status ret = GMERR_OK;
    VertexElementT **curVertexElement = (VertexElementT **)DbListItem(&pIterator->lpVertexElems, pathLayer - 1);
    *curVertexElement = vertexElement;

    if ((*curVertexElement)->outEdges.start == DB_INVALID_UINT32) {
        // meets end
        if (pathLayer == pIterator->pathLayerMax) {
            *pPathFound = true;
        }
        return ret;
    }

    EdgeElementT *curOutEdgeElement = pIterator->pAA->topo.colArray[(*curVertexElement)->outEdges.start];
    uint32_t *curOutEdgeElementIdx = (uint32_t *)DbListItem(&pIterator->lIndsOutEdgesInAA, pathLayer - 1);
    *curOutEdgeElementIdx = (*curVertexElement)->outEdges.start;  // replace path is liner path
    VertexElementT *nextVertexElement = pIterator->pAA->topo.rowArray[curOutEdgeElement->dstIndex];
    return AAPathIteratorNext4ReplaceInner(pIterator, pathLayer + 1, nextVertexElement, pPathFound);
}

static Status AAPathIteratorNext4Replace(AAPathIteratorT *pIterator, uint32_t pathLayer, bool *pPathFound, bool *finish)
{
    Status ret = GMERR_OK;
    PositionT *pRootRngInd;
    pRootRngInd = &pIterator->pAA->topo.info.rootVertexEle;
    if ((pRootRngInd->start == DB_INVALID_UINT32) || (pIterator->curRootPos > pRootRngInd->end)) {
        // all root vertexElements have been DFS
        *finish = true;
        *pPathFound = false;
        return ret;
    }

    VertexElementT *curRootVertexElement = pIterator->pAA->topo.rowArray[pIterator->curRootPos];
    if (curRootVertexElement->status == ENTITY_FLAG_DELETED) {
        *pPathFound = false;
        pIterator->curRootPos++;
        return ret;
    }
    VertexElementT **curVertexElement = (VertexElementT **)DbListItem(&pIterator->lpVertexElems, pathLayer - 1);
    *curVertexElement = curRootVertexElement;

    // no empty edge in replace path && at least one edge in replace path
    EdgeElementT *curOutEdgeElement = pIterator->pAA->topo.colArray[curRootVertexElement->outEdges.start];
    uint32_t *curOutEdgeElementIdx = (uint32_t *)DbListItem(&pIterator->lIndsOutEdgesInAA, pathLayer - 1);
    *curOutEdgeElementIdx = curRootVertexElement->outEdges.start;  // replace path is liner path

    VertexElementT *nextVertexElement = pIterator->pAA->topo.rowArray[curOutEdgeElement->dstIndex];
    *pPathFound = false;
    ret = AAPathIteratorNext4ReplaceInner(pIterator, pathLayer + 1, nextVertexElement, pPathFound);
    if (ret != GMERR_OK) {
        return ret;
    }
    pIterator->curRootPos++;
    return GMERR_OK;
}

static Status AAPathIteratorNext4Merge(AAPathIteratorT *pIterator, uint32_t pathLayer, bool *pPathFound, bool *finish)
{
    Status ret = GMERR_OK;
    PositionT *pRootRngInd;
    pRootRngInd = &pIterator->pAA->topo.info.rootVertexEle;
    if ((pRootRngInd->start == DB_INVALID_UINT32) || (pIterator->curRootPos > pRootRngInd->end)) {
        // all root vertexElements have been DFS
        *finish = true;
        *pPathFound = false;
        return ret;
    }

    // only one layer && one normal edge && one empty edge in merge path && empty edge always been connected
    *pPathFound = true;
    VertexElementT *curRootVertexElement = pIterator->pAA->topo.rowArray[pIterator->curRootPos];
    VertexElementT **curVertexElement = (VertexElementT **)DbListItem(&pIterator->lpVertexElems, pathLayer - 1);
    *curVertexElement = curRootVertexElement;

    EdgeElementT *curOutEdgeElement = pIterator->pAA->topo.colArray[curRootVertexElement->outEdges.start];
    uint32_t *curOutEdgeElementIdx = (uint32_t *)DbListItem(&pIterator->lIndsOutEdgesInAA, pathLayer - 1);
    *curOutEdgeElementIdx = curRootVertexElement->outEdges.start;
    if (curOutEdgeElement->dstIndex != DB_INVALID_UINT32) {
        VertexElementT **nextVertexElement = (VertexElementT **)DbListItem(&pIterator->lpVertexElems, pathLayer);
        *nextVertexElement = pIterator->pAA->topo.rowArray[curOutEdgeElement->dstIndex];
    }
    pIterator->curRootPos++;
    return GMERR_OK;
}

/**
 * @brief Counts the number of paths encoded in pAA of the length @p pathLayerMax and stores in @p pNumDmls.
 *
 * @param[in] memCtx The memory context used for allocations.
 * @param[in] pAA The pointer to the associative array that encodes the information.
 * @param[in] pathLayerMax The length of each path in @p pAA.
 * @param[out] pNumPaths The pointer to the number of paths.
 * @return Status GMERR_OK if everything is fine, otherwise an error code.
 */
static Status CountNumberOfPaths(
    DbMemCtxT *memCtx, AAT *pAA, uint32_t pathLayerMax, uint32_t *pNumPaths, bool isReplacePath)
{
    Status ret = GMERR_OK;
    AAPathIteratorT pathIterator;
    // init path iterator
    ret = AAPathIteratorOpen4MergeReplace(memCtx, pAA, pathLayerMax, &pathIterator);
    if (ret != GMERR_OK) {
        *pNumPaths = 0;
        AAPathIteratorClose(&pathIterator);
        return ret;
    }

    bool finish = false;
    uint32_t pathCnt = 0;
    while (!finish) {
        // get next path instance
        bool isFound = false;
        uint32_t pathLayer = 1;
        if (isReplacePath) {
            ret = AAPathIteratorNext4Replace(&pathIterator, pathLayer, &isFound, &finish);
        } else {
            ret = AAPathIteratorNext4Merge(&pathIterator, pathLayer, &isFound, &finish);
        }
        if (ret != GMERR_OK) {
            break;
        }
        if (isFound) {
            pathCnt++;
        }
    }
    AAPathIteratorClose(&pathIterator);
    *pNumPaths = pathCnt;
    return ret;
}

static Status CopyVertexToVertex(DmVertexT *pSrcVertex, DmVertexLabelT *vertexLabel, DmVertexT *pDstVertex)
{
    Status ret = GMERR_OK;
    uint32_t propNum;
    propNum = vertexLabel->metaVertexLabel->schema->propeNum;

    for (uint32_t propIdx = 0; propIdx < propNum; propIdx++) {
        DmValueT value;
        DmPropertySchemaT *pProperty;
        pProperty = &vertexLabel->metaVertexLabel->schema->properties[propIdx];
        ret = DmVertexGetPropeByIdNoCopy(pSrcVertex, propIdx, &value);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DmVertexSetPropeByName(pProperty->name, value, pDstVertex);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

typedef struct MergePathGenOneDmlInfo {
    DmVertexLabelT *pVertexLabel;         // The pointer to the vertex label to which content we are inserting.
    DmVertexLabelT *pChangedVertexLabel;  // The pointer to the vertex label whose content is replaced.
    DbListT *pListOut;                    // The pointer to the list of vertices contributing to the path.
                                          // item<VertexElementT*>.
    DbListT *pListClauses;            // The pointer to the list of clauses which we are projecting. item<DmSetClauseT*>
    FixBufferT *pOutInsertBuffer;     // The out buffer that encoded all generated DMLs for insertions.
    FixBufferT *pOutDeleteBuffer;     // The out buffer that encoded all generated DMLs for deletions.
    uint32_t pathLayerMax;            // The length of the path.
    bool isInsert;                    // If the DML is insert.
    bool isReplacePath;               // If the DML is replace.
    bool needsAdditionalInsert;       // See @ref pNewVertInsertDelete.
    SessionT *pSession;               // Pointer to the current session.
    DmVertexT *pNewVert;              // The row that is going to be inserted in the merge path/replace path table.
    DmVertexT *pNewVertInsertDelete;  // In the case of the insert, used for inserting leftRow | NULL rows
                                      // in the case if the row from the right table was the last one contributing to
                                      // the leftRow | rightRow in the merge path in the case.
} MergePathGenOneDmlInfoT;

typedef struct GenPathInfoGen {
    DmComplexPathInfoT *pPlanComplexPath;  // Pointer to the path information.
    PlanT *pPlanPath;                      // Pointer to the physical plan of the path.
    SessionT *pPrevSession;                // Pointer to the previous session.
    SessionT *pSession;                    // Pointer to the current session.
    FixBufferT *pInsertBuffer;             // Pointer to the buffer in which the response will be saved.
    FixBufferT *pDeleteBuffer;             // Pointer to the buffer in which the response will be saved.
    DmVertexLabelT *pVertexLabel;          // The pointer to the vertex label that need to generate dml.
    DmVertexLabelT *pChangedVertLabel;     // The pointer to the vertex label that is changed.
    DbListT lpPathInfos;                   // The list of paths. item<DmComplexPathInfoT *>
    DbListT lpPathPlans;                   // The list of path plans. item<PlanT *>
    uint32_t idxPath;                      // The index of the path currently processed.
    DmComplexPathTypeE pathType;
} GenPathInfoGenT;

typedef struct DMLCounter {
    uint32_t inserts;
    uint32_t deletes;
} DMLCounterT;

static Status SetMergeTableDmValue4PropertyClause(
    DmSetClauseT *pSetClause, AASlotT *pAASlot1, AASlotT *pAASlot2, DmVertexT *pVert)
{
    DmValueT value;
    AASlotT *pLeftSlot;
    if (pAASlot1->dmVertex->vertexDesc->labelId == pSetClause->rightProp->vertexLabelId) {
        pLeftSlot = pAASlot1;
    } else {
        pLeftSlot = pAASlot2;
    }
    Status ret = DmVertexGetPropeByName(pLeftSlot->dmVertex, pSetClause->rightProp->fieldName.str, &value);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (value.type == DB_DATATYPE_NULL) {
        return GMERR_OK;
    }
    return DmVertexSetPropeByName(pSetClause->leftProp->fieldName.str, value, pVert);
}

static Status SetMergeTableDmValue4CoalesceClause(
    DmSetClauseT *pSetClause, AASlotT *pAASlot1, AASlotT *pAASlot2, DmVertexT *pVert)
{
    DmValueT value;
    AASlotT *pLeftSlot;
    char *fieldName = pSetClause->coalesceClause->coalesceLeftProp->fieldName.str;
    if (pAASlot1->dmVertex->vertexDesc->labelId == pSetClause->coalesceClause->coalesceLeftProp->vertexLabelId) {
        pLeftSlot = pAASlot1;
    } else {
        pLeftSlot = pAASlot2;
    }
    if (pAASlot2 == NULL) {
        if (pAASlot1->dmVertex->vertexDesc->labelId != pSetClause->coalesceClause->coalesceLeftProp->vertexLabelId) {
            fieldName = pSetClause->coalesceClause->coalesceRightProp->fieldName.str;
        }
        pLeftSlot = pAASlot1;
    }
    Status ret = DmVertexGetPropeByName(pLeftSlot->dmVertex, fieldName, &value);
    if (ret != GMERR_OK) {
        return ret;
    }
    return DmVertexSetPropeByName(pSetClause->leftProp->fieldName.str, value, pVert);
}

static Status SetMergeTableDmValue4CoalesceClause4Delete(
    DmSetClauseT *pSetClause, AASlotT *pAASlot1, AASlotT *pAASlot2, DmVertexT *pVert)
{
    DmValueT value;
    AASlotT *pLeftSlot = pAASlot1;
    char *fieldName = NULL;
    if (pAASlot1->dmVertex->vertexDesc->labelId == pSetClause->coalesceClause->coalesceLeftProp->vertexLabelId) {
        fieldName = pSetClause->coalesceClause->coalesceLeftProp->fieldName.str;
    } else {
        fieldName = pSetClause->coalesceClause->coalesceRightProp->fieldName.str;
    }
    Status ret = DmVertexGetPropeByName(pLeftSlot->dmVertex, fieldName, &value);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (value.type == DB_DATATYPE_NULL) {
        return GMERR_OK;
    }
    return DmVertexSetPropeByName(pSetClause->leftProp->fieldName.str, value, pVert);
}

static Status SetMergeTableProperty(
    bool isInsert, DmSetClauseT *pSetClause, AASlotT *pAASlot1, AASlotT *pAASlot2, DmVertexT *pVert)
{
    if (pSetClause->tag == DM_SET_CLAUSE_PROPERTY) {
        return SetMergeTableDmValue4PropertyClause(pSetClause, pAASlot1, pAASlot2, pVert);
    } else if (!isInsert) {
        return SetMergeTableDmValue4CoalesceClause4Delete(pSetClause, pAASlot1, pAASlot2, pVert);
    } else {
        return SetMergeTableDmValue4CoalesceClause(pSetClause, pAASlot1, pAASlot2, pVert);
    }
}

static Status SetMergeTableVertex(
    MergePathGenOneDmlInfoT *pOneDmlGenInfo, AASlotT *pAASlot1, AASlotT *pAASlot2, DmVertexT *pNewVert)
{
    Status ret = GMERR_OK;
    uint32_t clauseCnt = DbListGetItemCnt(pOneDmlGenInfo->pListClauses);
    for (uint32_t idxClause = 0; idxClause < clauseCnt; idxClause++) {
        DmSetClauseT *pSetClause = (DmSetClauseT *)DbListItem(pOneDmlGenInfo->pListClauses, idxClause);
        ret = SetMergeTableProperty(pOneDmlGenInfo->isInsert, pSetClause, pAASlot1, pAASlot2, pNewVert);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

static Status IsLeftRightValueInCoalClauseSame(
    DmSetClauseT *pSetClause, AASlotT *pAASlot1, AASlotT *pAASlot2, bool *equal)
{
    Status ret = GMERR_OK;
    DmValueT leftValue, rightValue;
    if (pSetClause->coalesceClause->coalesceLeftProp->vertexLabelId == pAASlot1->dmVertex->vertexDesc->labelId) {
        ret = DmVertexGetPropeByName(
            pAASlot1->dmVertex, pSetClause->coalesceClause->coalesceLeftProp->fieldName.str, &leftValue);
        ret = DmVertexGetPropeByName(
            pAASlot2->dmVertex, pSetClause->coalesceClause->coalesceRightProp->fieldName.str, &rightValue);
    } else {
        ret = DmVertexGetPropeByName(
            pAASlot1->dmVertex, pSetClause->coalesceClause->coalesceRightProp->fieldName.str, &leftValue);
        ret = DmVertexGetPropeByName(
            pAASlot2->dmVertex, pSetClause->coalesceClause->coalesceLeftProp->fieldName.str, &rightValue);
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    if (DmValueIsEqual(&leftValue, &rightValue)) {
        *equal = true;
    }
    return ret;
}

static Status IsVertexExistInHeadTable(MergePathGenOneDmlInfoT *pOneDmlGenInfo, AASlotT *pAASlot, bool *isFound)
{
    // lookup merge table by primary key
    Status ret = GMERR_OK;
    SessionT *session = pOneDmlGenInfo->pSession;
    // index begin scan
    LabelBeginCfgT beginCfg = {.trxMemCtx = session->memCtx,
        .eeMemCtx = session->memCtx,
        .vertexLabel = pOneDmlGenInfo->pVertexLabel,
        .seRunCtx = session->seInstance,
        .session = NULL};
    IndexScanDescT *desc = NULL;
    ret = IndexBeginScan(beginCfg, PK_INDEX_SEQ, &desc);
    if (ret != GMERR_OK) {
        return ret;
    }
    // index lookup
    IndexKeyT idxKey = {0};
    DmVertexLabelT *vertexLabel = NULL;
    ret = CataGetVertexLabelById(NULL, pAASlot->dmVertex->vertexDesc->labelId, &vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmGetKeyBufFromVertex(
        pAASlot->dmVertex, vertexLabel->metaVertexLabel->pkIndex->indexId, &idxKey.keyData, &idxKey.keyLen);
    (void)CataReleaseVertexLabel(vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    HpTupleAddr addr = {0};
    ret = IndexLookup(desc, idxKey, &addr, isFound);
    if (ret != GMERR_OK) {
        return ret;
    }
    // index end scan
    IndexEndScan(desc);
    return GMERR_OK;
}

static Status GenerateDMLOfMergePath4ReplaceConnectedByEmptyEdge(
    MergePathGenOneDmlInfoT *pOneDmlGenInfo, AASlotT *pAASlot1, AASlotT *pAASlot2)
{
    bool isFound = false;
    Status ret = IsVertexExistInHeadTable(pOneDmlGenInfo, pAASlot1, &isFound);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (isFound) {
        return GMERR_INVALID_BUFFER;
    }
    return SetMergeTableVertex(pOneDmlGenInfo, pAASlot1, pAASlot2, pOneDmlGenInfo->pNewVert);
}

/**
 * @brief Evaluates a list of clauses @p pOneDmlGenInfo->pListClauses by evaluating projection, by projection
 * from the vertices specified in @p pAASlot1 and @p pAASlot2.
 *
 * @param[in/out] pOneDmlGenInfo The pointer to the wrapper containing all used data.
 * @param[in] pAASlot1  The pointer to the first associative array slot.
 * @param[in] pAASlot2  The pointer to the second associative array slot.
 * @return Status GMERR_OK if everything is fine, otherwise an error code.
 */
static Status GenerateDMLBasedOnAATOfMergePath4Replace(
    MergePathGenOneDmlInfoT *pOneDmlGenInfo, AASlotT *pAASlot1, AASlotT *pAASlot2)
{
    Status ret = GMERR_OK;
    if (pAASlot2 == NULL) {
        // connected by empty edge
        return GenerateDMLOfMergePath4ReplaceConnectedByEmptyEdge(pOneDmlGenInfo, pAASlot1, pAASlot2);
    }
    bool needGenerated = false;
    uint32_t clauseCnt = DbListGetItemCnt(pOneDmlGenInfo->pListClauses);
    for (uint32_t idxClause = 0; idxClause < clauseCnt; idxClause++) {
        DmSetClauseT *pSetClause = (DmSetClauseT *)DbListItem(pOneDmlGenInfo->pListClauses, idxClause);
        if (pSetClause->tag == DM_SET_CLAUSE_PROPERTY) {
            continue;
        }
        bool equal = false;
        ret = IsLeftRightValueInCoalClauseSame(pSetClause, pAASlot1, pAASlot2, &equal);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (!equal) {
            needGenerated = true;
            break;
        }
    }
    if (!needGenerated) {
        return GMERR_INVALID_BUFFER;
    }
    return SetMergeTableVertex(pOneDmlGenInfo, pAASlot1, pAASlot2, pOneDmlGenInfo->pNewVert);
}

/**
 * @brief Fills buffer depending whether replace or delete is issued.
 *
 * @param[in] memCtx The pointer to the memory context used for allocations.
 * @param[in/out] pOneDmlGenInfo The pointer to the wrapper containing the vertex data for replacement/deletion.
 *
 * @return Status GMERR_OK if everything is fine, otherwise an error code.
 */
static Status GenerateDMLInBuffer(DbMemCtxT *memCtx, MergePathGenOneDmlInfoT *pOneDmlGenInfo, DMLCounterT *dmlCount)
{
    Status ret = GMERR_OK;
    DmVertexT *pNewVert;
    DmVertexT *pNewVertInsertDelete;

    pNewVert = pOneDmlGenInfo->pNewVert;
    pNewVertInsertDelete = pOneDmlGenInfo->pNewVertInsertDelete;

    if (pOneDmlGenInfo->isReplacePath && pOneDmlGenInfo->isInsert) {
        ret = FillOneRowFixBufferForInsertDML(pOneDmlGenInfo->pOutInsertBuffer, pOneDmlGenInfo->pVertexLabel, pNewVert);
        (dmlCount->inserts)++;
    } else if (pOneDmlGenInfo->pVertexLabel->pathAttributes->vertexLabelMergeViewType == DM_MERGE) {
        if (pOneDmlGenInfo->isInsert) {
            ret = FillOneRowFixBufferForDeleteDML(
                memCtx, pOneDmlGenInfo->pOutDeleteBuffer, pOneDmlGenInfo->pVertexLabel, pNewVert);
            (dmlCount->deletes)++;
            ret = FillOneRowFixBufferForInsertDML(
                pOneDmlGenInfo->pOutInsertBuffer, pOneDmlGenInfo->pVertexLabel, pNewVert);
            (dmlCount->inserts)++;
        } else {
            ret = FillOneRowFixBufferForDeleteDML(
                memCtx, pOneDmlGenInfo->pOutDeleteBuffer, pOneDmlGenInfo->pVertexLabel, pNewVert);
            (dmlCount->deletes)++;
            if (pOneDmlGenInfo->needsAdditionalInsert) {
                ret = FillOneRowFixBufferForInsertDML(
                    pOneDmlGenInfo->pOutInsertBuffer, pOneDmlGenInfo->pVertexLabel, pNewVertInsertDelete);
                (dmlCount->inserts)++;
            }
        }
    } else if (pOneDmlGenInfo->pVertexLabel->pathAttributes->vertexLabelMergeViewType == DM_MERGE_UPDATE) {
        if (pOneDmlGenInfo->isInsert) {
            ret = FillOneRowFixBufferForInsertDML(
                pOneDmlGenInfo->pOutInsertBuffer, pOneDmlGenInfo->pVertexLabel, pNewVert);
            (dmlCount->inserts)++;
        } else {
            if (pOneDmlGenInfo->needsAdditionalInsert) {
                ret = FillOneRowFixBufferForInsertDML(
                    pOneDmlGenInfo->pOutInsertBuffer, pOneDmlGenInfo->pVertexLabel, pNewVertInsertDelete);
                (dmlCount->inserts)++;
            } else {
                ret = FillOneRowFixBufferForDeleteDML(
                    memCtx, pOneDmlGenInfo->pOutDeleteBuffer, pOneDmlGenInfo->pVertexLabel, pNewVert);
                (dmlCount->deletes)++;
            }
        }
    } else {
        return GMERR_OK;
    }
    return ret;
}

static Status GetValueOnRightSide4Replace(DmSetClauseT *pSetClause, AASlotT *pAASlot, DmValueT *pValue)
{
    Status ret = DmVertexGetPropeByName(pAASlot->dmVertex, pSetClause->rightProp->fieldName.str, pValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

static Status SetValueOnLeftSide4Replace(DmSetClauseT *pSetClause, DmVertexT *pNewVert, DmValueT *pValue)
{
    return DmVertexSetPropeByName(pSetClause->leftProp->fieldName.str, *pValue, pNewVert);
}

static Status GenerateDMLBasedOnAATOfReplacePath(
    DbMemCtxT *memCtx, MergePathGenOneDmlInfoT *pOneDmlGenInfo, AASlotT *pAASlot1, AASlotT *pAASlot2)
{
    pOneDmlGenInfo->needsAdditionalInsert = false;
    Status ret = CopyVertexToVertex(pAASlot1->dmVertex, pOneDmlGenInfo->pVertexLabel, pOneDmlGenInfo->pNewVert);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (!pOneDmlGenInfo->isInsert) {
        return ret;
    }
    DmVertexT *pNewVert = pOneDmlGenInfo->pNewVert;
    uint32_t clauseCnt = DbListGetItemCnt(pOneDmlGenInfo->pListClauses);
    for (uint32_t idxClause = 0; idxClause < clauseCnt; idxClause++) {
        DmValueT value;
        DmSetClauseT *pSetClause = (DmSetClauseT *)DbListItem(pOneDmlGenInfo->pListClauses, idxClause);
        ret = GetValueOnRightSide4Replace(pSetClause, pAASlot2, &value);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = SetValueOnLeftSide4Replace(pSetClause, pNewVert, &value);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static bool TriggerByHeadTable(MergePathGenOneDmlInfoT *pOneDmlGenInfo, AASlotT *pAASlot)
{
    return pOneDmlGenInfo->pChangedVertexLabel->metaCommon.metaId == pAASlot->dmVertex->vertexDesc->labelId;
}

static Status GenerateDMLBasedOnAATOfMergePath4Delete(
    DbMemCtxT *memCtx, MergePathGenOneDmlInfoT *pOneDmlGenInfo, AASlotT *pAASlot1, AASlotT *pAASlot2)
{
    pOneDmlGenInfo->needsAdditionalInsert = false;
    Status ret = SetMergeTableVertex(pOneDmlGenInfo, pAASlot1, pAASlot2, pOneDmlGenInfo->pNewVert);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SetMergeTableVertex(pOneDmlGenInfo, pAASlot1, pAASlot2, pOneDmlGenInfo->pNewVertInsertDelete);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (pOneDmlGenInfo->isInsert) {
        return GMERR_OK;
    }
    if (TriggerByHeadTable(pOneDmlGenInfo, pAASlot1)) {
        // triggered by delete head table
        return GMERR_OK;
    }
    // triggered by delete tail table
    pOneDmlGenInfo->needsAdditionalInsert = true;
    bool needGenerate = false;
    uint32_t clauseCnt = DbListGetItemCnt(pOneDmlGenInfo->pListClauses);
    for (uint32_t idxClause = 0; idxClause < clauseCnt; idxClause++) {
        DmSetClauseT *pSetClause = (DmSetClauseT *)DbListItem(pOneDmlGenInfo->pListClauses, idxClause);
        if (pSetClause->tag == DM_SET_CLAUSE_PROPERTY) {
            continue;
        }
        bool equal = false;
        ret = IsLeftRightValueInCoalClauseSame(pSetClause, pAASlot1, pAASlot2, &equal);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (!equal) {
            needGenerate = true;
            break;
        }
    }
    if (!needGenerate) {
        return GMERR_INVALID_BUFFER;
    }
    return GMERR_OK;
}

/**
 * @brief Generate DML for one insert/deletes/replaces row into merge path/replace tables.
 *
 * @param[in] memCtx The pointer to the memory context used for allocations.
 * @param[in] pAA  The pointer to the associative array containing the paths.
 * @param[in/out] pOneDmlGenInfo The wrapper that is used for transferring information:
 * 1) affected path table: @p pOneDmlGenInfo->pVertexLabel,
 * 2) type of an DML (replace/insert): @p pOneDmlGneInfo->isReplacePath,
 * 3) current path: @p pOneDmlGenInfo->pListOut,
 * 4) current path length @p pOneDmlGenInfo->pathLayerMax ,
 * 5) vertex used for insertions/deletion: @p pOneDmlGenInfo->pNewVert ,
 * 6) vertex used for replaces: @p pOneDmlGenInfo->pNewVertInsertDelete
 *
 * @return Status GMERR_OK if everything is fine, otherwise an error code.
 */
static Status GenerateDMLBasedOnAATOfPath(
    DbMemCtxT *memCtx, AAT *pAA, MergePathGenOneDmlInfoT *pOneDmlGenInfo, DMLCounterT *dmlCount)
{
    Status ret = GMERR_OK;
    VertexElementT *pVert1;
    VertexElementT *pVert2;

    pVert1 = *(VertexElementT **)(DbListItem(pOneDmlGenInfo->pListOut, 0));
    if (pOneDmlGenInfo->isReplacePath) {
        pVert2 = *(VertexElementT **)(DbListItem(pOneDmlGenInfo->pListOut, pOneDmlGenInfo->pathLayerMax - 1));
    } else {
        pVert2 = *(VertexElementT **)(DbListItem(pOneDmlGenInfo->pListOut, 1));
    }

    AASlotT *pAASlot1 = GetAASlotByElement(pAA, pVert1);
    AASlotT *pAASlot2 = GetAASlotByElement(pAA, pVert2);

    ret = DmCreateEmptyVertexWithMemCtx(memCtx, pOneDmlGenInfo->pVertexLabel, &pOneDmlGenInfo->pNewVert);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmCreateEmptyVertexWithMemCtx(memCtx, pOneDmlGenInfo->pVertexLabel, &pOneDmlGenInfo->pNewVertInsertDelete);
    if (ret != GMERR_OK) {
        goto CLEAN_1;
    }

    /**
     * generate dml rule(REPLACE)
     * 1. merge path
     *   1). triggered by head vertex
     *           same vertex already exist: skip
     *           new vertex: build delete dml(only for merge) && replace dml on merge table
     *   2). triggered by tail vertex
     *           same vertex already exist: skip
     *           new vertex: build delete dml(only for merge) && replace dml on merge table
     * 2. replace path
     *   1). triggered by head vertex: build replace dml on replace table
     *   2). triggered by tail vertex: build replace dml on replace table
     * generate dml rule(DELETE)
     * 1. merge path
     *   1). triggered by head vertex: build delete dml on merge table
     *   2). triggered by tail vertex: build delete dml(only for merge) && replace dml on merge table
     * generate dml rule(TYPE of merge table): after generate dml, may only execute some of them
     * 1. type is merge: execute both delete && replace
     * 2. type is merge update: only execute replace
     **/

    if (pOneDmlGenInfo->isReplacePath) {
        // REPLACE 2 1) && 2 2)
        ret = GenerateDMLBasedOnAATOfReplacePath(memCtx, pOneDmlGenInfo, pAASlot1, pAASlot2);
    } else if (!pOneDmlGenInfo->isInsert) {
        // DELETE 1 1) 1 2)
        ret = GenerateDMLBasedOnAATOfMergePath4Delete(memCtx, pOneDmlGenInfo, pAASlot1, pAASlot2);
    } else {
        ret = GenerateDMLBasedOnAATOfMergePath4Replace(pOneDmlGenInfo, pAASlot1, pAASlot2);
    }
    if (ret != GMERR_OK) {
        if (ret == GMERR_INVALID_BUFFER) {
            ret = GMERR_OK;
        }
        goto CLEAN_2;
    }
    ret = GenerateDMLInBuffer(memCtx, pOneDmlGenInfo, dmlCount);
CLEAN_2:
    DmDestroyVertex(pOneDmlGenInfo->pNewVertInsertDelete);
CLEAN_1:
    DmDestroyVertex(pOneDmlGenInfo->pNewVert);
    return ret;
}

/**
 * @brief Generate DML for all insert/deletes/replaces rows into merge path/replace tables.
 *
 * @param[in] memCtx The pointer to the memory context used for allocations.
 * @param[in] pAA The pointer to the associative array containing the paths.
 * @param[in/out] pTypePathGenInfo A wrapper around the structure containing hte information about the path length,
 * the list of clauses, the affected path tables and original tables, and the buffer to be generated.
 * @param[in] isInsert True if the DMLs are either inserts or replaces, otherwise false.
 * @param[in] isReplacePath True if the DMLs are replaces, otherwise false.
 *
 * @return Status GMERR_OK if everything is fine, otherwise an error code.
 */
static Status GenerateDMLsBasedOnAATOfPaths(
    DbMemCtxT *memCtx, AAT *pAA, GenPathInfoGenT *pTypePathGenInfo, bool isInsert, DMLCounterT *dmlCount)
{
    Status ret = GMERR_OK;
    AAPathIteratorT pathIterator;
    MergePathGenOneDmlInfoT oneDmlGenPathInfo;
    uint32_t pathLayerMax;
    DbListT *pListClauses;
    pathLayerMax = pTypePathGenInfo->pPlanComplexPath->pattern->pathInfoVertexCount;
    pListClauses = &pTypePathGenInfo->pPlanComplexPath->generatePathInfo->setClauses;

    oneDmlGenPathInfo.pVertexLabel = pTypePathGenInfo->pVertexLabel;
    oneDmlGenPathInfo.pChangedVertexLabel = pTypePathGenInfo->pChangedVertLabel;
    oneDmlGenPathInfo.pListClauses = pListClauses;
    oneDmlGenPathInfo.pOutDeleteBuffer = pTypePathGenInfo->pDeleteBuffer;
    oneDmlGenPathInfo.pOutInsertBuffer = pTypePathGenInfo->pInsertBuffer;
    oneDmlGenPathInfo.pathLayerMax = pathLayerMax;
    oneDmlGenPathInfo.isInsert = isInsert;
    oneDmlGenPathInfo.isReplacePath = pTypePathGenInfo->pathType == DM_COMPLEX_PATH_REPLACE;
    oneDmlGenPathInfo.pSession = pTypePathGenInfo->pSession;

    ret = AAPathIteratorOpen4MergeReplace(memCtx, pAA, pathLayerMax, &pathIterator);
    if (ret != GMERR_OK) {
        AAPathIteratorClose(&pathIterator);
        return ret;
    }
    bool finish = false;
    while (!finish) {
        // get next path instance
        bool isFound = false;
        uint32_t pathLayer = 1;
        if (oneDmlGenPathInfo.isReplacePath) {
            ret = AAPathIteratorNext4Replace(&pathIterator, pathLayer, &isFound, &finish);
        } else {
            ret = AAPathIteratorNext4Merge(&pathIterator, pathLayer, &isFound, &finish);
        }
        if (ret != GMERR_OK) {
            break;
        }
        if (isFound) {
            oneDmlGenPathInfo.pListOut = &pathIterator.lpVertexElems;
            ret = GenerateDMLBasedOnAATOfPath(memCtx, pAA, &oneDmlGenPathInfo, dmlCount);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    AAPathIteratorClose(&pathIterator);
    return ret;
}

static Status AAPushPropSlotDeepCopy(AAT *aa, AASlotT *slot)
{
    Status ret = GMERR_OK;
    AASlotT *pSlotDeepCopy;
    ret = DeepCopyAASlot(aa->memCtx, slot, &pSlotDeepCopy);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = AAPushPropSlot(aa, pSlotDeepCopy);
    if (ret != GMERR_OK) {
        DestroyAASlot(aa->memCtx, &pSlotDeepCopy);
    }
    return ret;
}

/**
 * @brief Create the associative array @p ppAA and fill out with the AA slots stored in @p plpAAslots.
 *
 * @param[in] memCtx The pointer to the memory context used for allocations.
 * @param[in] plpAASlots The pointer to the list of AA slots representing DMLs affecting one table.
 * @param[out] ppAA The pointer to a pointer to the created associative array.
 *
 * @return Status GMERR_OK if everything went fine, otherwise an error code.
 */
static Status CreateAAndInsertDMLData(DbMemCtxT *memCtx, DbListT *plpAASlots, AAT **ppAA)
{
    Status ret = GMERR_OK;
    AAT *pAA = NULL;
    AAInfoT aaInfo;
    uint32_t cntDMLRows;

    pAA = (AAT *)DbDynMemCtxAlloc(memCtx, sizeof(AAT));
    if (pAA == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc AAT");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(pAA, sizeof(AAT), 0, sizeof(AAT));

    aaInfo.memCtx = memCtx;
    aaInfo.needTopo = true;
    cntDMLRows = DbListGetItemCnt(plpAASlots);
    ret = NewAAWithPreAllocBuf(&aaInfo, pAA, cntDMLRows, false);
    if (ret != GMERR_OK) {
        goto CLEAN;
    }

    for (uint32_t idx = 0; idx < cntDMLRows; idx++) {
        AASlotT *pAASlot;
        pAASlot = *((AASlotT **)DbListItem(plpAASlots, idx));
        ret = AAPushPropSlotDeepCopy(pAA, pAASlot);
        if (ret != GMERR_OK) {
            goto CLEAN_AA;
        }
    }

    pAA->topo.info.lastLevel = (PositionT){.start = 0, .end = cntDMLRows - 1};
    pAA->topo.info.rootVertexEle = (PositionT){.start = 0, .end = cntDMLRows - 1};
    goto EXIT;
CLEAN_AA:
    DestroyAA(pAA);
CLEAN:
    DbDynMemCtxFree(memCtx, pAA);
EXIT:
    *ppAA = pAA;
    return ret;
}

/**
 * @brief Initialized fusion statement @p pFusionStmt by using session @p pSession and @p pPrevSession
 * as current and previous and @p pPlan as the plan.
 *
 * @param[out] pFusionStmt The pointer to the initialized fusion statement.
 * @param[in] pSession         The pointer to the current session.
 * @param[in] pPrevSession     The pointer to the previous session.
 * @param[in] pPlan            The pointer to the plan.
 */
static void InitFusionStmtFromSessions(
    FusionModelQryStmtT *pFusionStmt, SessionT *pSession, SessionT *pPrevSession, PlanT *pPlan)
{
    *pFusionStmt = (FusionModelQryStmtT){.dbId = pSession->dbId,
        .namespaceId = pSession->namespaceId,
        .session = pSession,
        .prevSession = pPrevSession,
        .memCtx = pSession->memCtx,
        .trxMemCtx = pSession->memCtx,
        .planTree = pPlan,
        .totalParamNum = 0,
        .utilityStmt = NULL,
        .utilityResultStmt = NULL,
        .planState = NULL,
        .seInstance = pSession->seInstance,
        .isPathDelRerun = false};
    DB_UNUSED(pSession);
    DB_UNUSED(pPrevSession);
    DB_UNUSED(pPlan);
}

/**
 * @brief Generates DMLs affecting one merge/replace path specified in @p pTypePathGenInfo->pPlanPath.
 * The DMLs are stored in @p pTypePathGenInfo->pBuffer.
 *
 * @param[in] memCtx A pointer to the memory context used for allocations.
 * @param[in/out] pTypePathGenInfo A pointer to the wrapper containing sessions, buffer and affected vertex label .
 * @param[in] plpAASlots The pointer to the list of AA slots containing DMLs affecting one table.
 * @param[in] isInsert If the required DMLs are insertions.
 * @param[in] isReplacePath If the required DMLs are replace paths.
 *
 * @return Status GMERR_OK if everything went fine, otherwise an error code.
 */
static Status GenerateDMLsForOnePathTable(
    DbMemCtxT *memCtx, GenPathInfoGenT *pTypePathGenInfo, DbListT *plpAASlots, bool isInsert, DMLCounterT *dmlCount)
{
    Status ret = GMERR_OK;
    AAT *pAA;
    SessionT *pSession;
    SessionT *pPrevSession;
    FusionModelQryStmtT fusionStmt;
    GqlQryStmtT gqlPathInsert;

    ret = CreateAAndInsertDMLData(memCtx, plpAASlots, &pAA);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (isInsert) {
        pSession = pTypePathGenInfo->pSession;
        pPrevSession = pTypePathGenInfo->pPrevSession;
    } else {
        pSession = pTypePathGenInfo->pPrevSession;
        pPrevSession = pTypePathGenInfo->pPrevSession;
    }
    InitFusionStmtFromSessions(&fusionStmt, pSession, pPrevSession, pTypePathGenInfo->pPlanPath);
    gqlPathInsert = (GqlQryStmtT){.base = fusionStmt, .memCtx = memCtx};

    ret = ExecuteMergeOrReplacePlan(&gqlPathInsert, pAA);
    if (ret != GMERR_OK) {
        goto CLEAN;
    }
    ret = GenerateDMLsBasedOnAATOfPaths(memCtx, pAA, pTypePathGenInfo, isInsert, dmlCount);
CLEAN:
    DestroyAA(pAA);
    DbDynMemCtxFree(memCtx, pAA);

    return ret;
}

static bool IsPathAppended(DbListT *pathIdList, uint32_t pathId)
{
    uint32_t cnt = DbListGetItemCnt(pathIdList);
    for (uint32_t i = 0; i < cnt; i++) {
        uint32_t *id = (uint32_t *)DbListItem(pathIdList, i);
        if (*id == pathId) {
            return true;
        }
    }
    return false;
}

/**
 * @brief Filters all paths of type @p pathType to which @p pOneTableTasks->vertexLabel belongs to and adds to
 * @p plTypePaths and @p plTypePathPlans.
 *
 * @param[in] pOneTablePathTasks A path search task corresponding to one table with inserts.
 * @param[out] plTypePaths A list with pointers to the merge complex paths corresponding to
 * @p pOneTablePathSearch->vertexLabel.
 * @param[out] plTypePathPlans A list with pointers to the physical plans corresponding to
 * @param[in] pathType The type of a path for lists are built.
 *  @p pOneTablePathSearch->vertexLabel.
 *
 * @return Status GMERR_OK if everything went fine, otherwise an error code.
 *
 * @warning @p pOneTablePathTasks->psTasks MUST contain all paths to which the vertex label
 *  @p pOneTablePathTasks->vertexLabel belongs to.
 */
static Status PathSearchFilterListOfPaths(
    PathPvcTaskT *pOneTablePathTasks, DbListT *plTypePaths, DbListT *plTypePathPlans, DmComplexPathTypeE pathType)
{
    Status ret = GMERR_OK;
    DbListT pathIdList = {0};
    DbCreateList(&pathIdList, sizeof(uint32_t), pOneTablePathTasks->eeMemCtx);
    // each delete dml splited into different subsearch task, so need to apply deduplication in subsearch tasks
    uint32_t pathsCnt = DbListGetItemCnt(pOneTablePathTasks->psTasks);
    for (uint32_t pathIdx = 0; pathIdx < pathsCnt; pathIdx++) {
        SubSearchTaskT pathTask = *(SubSearchTaskT *)DbListItem(pOneTablePathTasks->psTasks, pathIdx);
        DmComplexPathInfoT *pPathInfo = pathTask.pathInfo;
        if (IsPathAppended(&pathIdList, pPathInfo->metaCommon.metaId)) {
            continue;
        }
        if (pPathInfo->pathType == pathType) {
            ret = DbAppendListItem(plTypePaths, &pPathInfo);
            if (ret != GMERR_OK) {
                return ret;
            }
            ret = DbAppendListItem(plTypePathPlans, &pathTask.plan->planTree);
            if (ret != GMERR_OK) {
                return ret;
            }
            ret = DbAppendListItem(&pathIdList, &pPathInfo->metaCommon.metaId);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    DbDestroyList(&pathIdList);
    return ret;
}

/**
 * @brief For a path in the list @p pTypePathGenInfo->lpPathPlans at the position @p idxPath, count the number of
 * DMLs that are required to be generated based on the DMLs affecting one table specified in @p plpAAslots.
 * The number of DMLs depends if a replace/insert/dml generation is requested is encoded in @p isInsRep.
 *
 * @param[in] memCtx The pointer to the memory context used for allocations.
 * @param[in] pTypePathGenInfo The pointer to a wrapper storing session and path information.
 * @param[in] plpAASlots The list of pointers to the AA slots updates to one table
 *  that are contributing to the path.
 * @param[out] pNumDMLs The number of DMLs to be generated.
 * @param[in] isInsRep Encodes which type of the DML generation is requested.
 *
 * @return Status GMERR_OK if everything went fine, otherwise an error code.
 */
static Status CountNumberOfDMLsOnePath(
    DbMemCtxT *memCtx, GenPathInfoGenT *pTypePathGenInfo, DbListT *plpAASlots, uint32_t *pNumDMLs, uint32_t isInsRep)
{
    Status ret = GMERR_OK;
    AAT *pAA;
    SessionT *pSession;
    SessionT *pPrevSession;
    DmComplexPathInfoT *pPathInfo;
    PlanT *pPlan;
    uint32_t pathLayerMax;
    bool isInsert, isReplacePath;
    FusionModelQryStmtT fusionStmt;

    isInsert = ((isInsRep & BIT_IS_INSERT) == BIT_IS_INSERT);
    isReplacePath = ((isInsRep & BIT_IS_REPLACE) == BIT_IS_REPLACE);
    pPlan = *(PlanT **)DbListItem(&pTypePathGenInfo->lpPathPlans, pTypePathGenInfo->idxPath);
    pPathInfo = *(DmComplexPathInfoT **)DbListItem(&pTypePathGenInfo->lpPathInfos, pTypePathGenInfo->idxPath);
    pathLayerMax = pPathInfo->pattern->pathInfoVertexCount;
    ret = CreateAAndInsertDMLData(memCtx, plpAASlots, &pAA);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (isInsert) {
        pSession = pTypePathGenInfo->pSession;
        pPrevSession = pTypePathGenInfo->pPrevSession;
    } else {
        // get valid path instance before executed delete dml
        pSession = pTypePathGenInfo->pPrevSession;
        pPrevSession = pTypePathGenInfo->pPrevSession;
    }

    InitFusionStmtFromSessions(&fusionStmt, pSession, pPrevSession, pPlan);
    GqlQryStmtT gqlPathInsert = {.base = fusionStmt, .memCtx = memCtx};
    ret = ExecuteMergeOrReplacePlan(&gqlPathInsert, pAA);
    if (ret != GMERR_OK) {
        goto CLEAN;
    }
    ret = CountNumberOfPaths(memCtx, pAA, pathLayerMax, pNumDMLs, isReplacePath);
CLEAN:
    DestroyAA(pAA);
    DbDynMemCtxFree(memCtx, pAA);
    return ret;
}

/**
 * @brief Counts the total number of DMLs required to be generated by applying the series of DMLs @p plpAAslots to
 * one table. The number of DMLs depends if a replace/insert/dml generation is requested is encoded in @p isInsRep.
 *
 * @param[in] memCtx The pointer to the memory context used for allocations.
 * @param[in] pTypePathGenInfo The pointer to a wrapper storing session and path information.
 * @param[in] plpAASlots The list of pointers to the AA slots updates to one table
 *  that are contributing to the paths.
 * @param[out] pNumDMLs The number of DMLs to be generated.
 * @param[in] isInsRep Encodes which type of the DML generation is requested.
 *
 * @return Status GMERR_OK if everything went fine, otherwise an error code.
 */
static Status CountNumberOfDMLs(
    DbMemCtxT *memCtx, GenPathInfoGenT *pTypePathGenInfo, DbListT *plpAASlots, uint32_t *pNumDMLs, uint32_t isInsRep)
{
    Status ret = GMERR_OK;
    uint32_t numPaths = 0;
    uint32_t totalNumPaths = 0;
    uint32_t pathPlansCnt;
    uint32_t numAffectedRows;

    pathPlansCnt = DbListGetItemCnt(&pTypePathGenInfo->lpPathInfos);
    numAffectedRows = DbListGetItemCnt(plpAASlots);
    if ((numAffectedRows == 0) || (pathPlansCnt == 0)) {
        *pNumDMLs = totalNumPaths;
        return ret;
    }

    for (uint32_t idxPath = 0; idxPath < pathPlansCnt; idxPath++) {
        pTypePathGenInfo->idxPath = idxPath;
        ret = CountNumberOfDMLsOnePath(memCtx, pTypePathGenInfo, plpAASlots, &numPaths, isInsRep);
        if (ret != GMERR_OK) {
            return ret;
        }
        totalNumPaths += numPaths;
    }
    *pNumDMLs = totalNumPaths;

    return ret;
}

/**
 * @brief Creates lists @p plpTypPaths and @p plpTypePathPlans and then fills out with all the
 * paths from @p pOneTableTask (all paths to which one table belongs to) that correspond to the @p pathType.
 *
 * @param[in] memCtx The pointer to the memory context.
 * @param[in] pOneTableTask The pointer to the structure storing all paths to which table @p pOneTableTask->vertexLabel
 *  belongs to.
 * @param[in] pathType The type of a path for lists are built.
 * @param[out] plpTypePaths The list of paths of type @p pathType to which table @p pOneTableTask->vertexLabel belongs
 * to
 * @param[out] plpTypePathPlans The list of paths of type @p pathType
 * to which table @p pOneTableTask->vertexLabel belongs to.
 *
 * @return Status GMERR_OK if everything went fine, otherwise an error code.
 */
static Status CreateAndInitializeViewPathLists(DbMemCtxT *memCtx, PathPvcTaskT *pOneTableTask,
    DmComplexPathTypeE pathType, DbListT *plpTypePaths, DbListT *plpTypePathPlans)
{
    Status ret = GMERR_OK;
    DbCreateList(plpTypePaths, sizeof(DmComplexPathInfoT *), memCtx);
    DbCreateList(plpTypePathPlans, sizeof(PlanT *), memCtx);

    ret = PathSearchFilterListOfPaths(pOneTableTask, plpTypePaths, plpTypePathPlans, pathType);
    return ret;
}

/**
 * @brief Fill buffer @p pOnePathTaskInfo->pBuffer with all generated DMLs for paths affected by an sequence of DMLs
 * @p plpAASlots to the table @p pOneTaskInfo->pChangedVertLabel.
 *
 * @param[in] memCtx The pointer to the memory context used for allocations.
 * @param[in/out] pOnePathTaskInfo
 * @param[in] plpAASlots The pointer to the list of generated AA slots representing DMLs affecting one table.
 * @param[in] isInsert true if the DMLs are inserts, otherwise false.
 * @param[in] pathType The path type for which DMLs are counted.
 *
 * @return Status GMERR_OK if everything went fine, otherwise an error code.
 */
static Status GenerateDMLsForPathTablesOneType(
    DbMemCtxT *memCtx, GenPathInfoGenT *pOnePathTaskInfo, DbListT *plpAASlots, bool isInsert, DMLCounterT *dmlCount)
{
    Status ret = GMERR_OK;
    uint32_t cntPaths;
    GenPathInfoGenT typePathGenInfo;
    if ((DbListGetItemCnt(plpAASlots) == 0) || (DbListGetItemCnt(&pOnePathTaskInfo->lpPathInfos) == 0) ||
        (pOnePathTaskInfo->pInsertBuffer == NULL)) {
        return ret;
    }

    typePathGenInfo.pDeleteBuffer = pOnePathTaskInfo->pDeleteBuffer;
    typePathGenInfo.pInsertBuffer = pOnePathTaskInfo->pInsertBuffer;
    typePathGenInfo.pSession = pOnePathTaskInfo->pSession;
    typePathGenInfo.pPrevSession = pOnePathTaskInfo->pPrevSession;
    typePathGenInfo.pChangedVertLabel = pOnePathTaskInfo->pChangedVertLabel;
    typePathGenInfo.pathType = pOnePathTaskInfo->pathType;
    cntPaths = DbListGetItemCnt(&pOnePathTaskInfo->lpPathInfos);
    for (uint32_t idxPath = 0; idxPath < cntPaths; idxPath++) {
        typePathGenInfo.pPlanComplexPath = *(DmComplexPathInfoT **)DbListItem(&pOnePathTaskInfo->lpPathInfos, idxPath);
        typePathGenInfo.pPlanPath = *(PlanT **)DbListItem(&pOnePathTaskInfo->lpPathPlans, idxPath);
        ret = CataGetVertexLabelById(
            NULL, typePathGenInfo.pPlanComplexPath->generatePathInfo->viewTableId, &typePathGenInfo.pVertexLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
        (void)CataReleaseVertexLabel(typePathGenInfo.pVertexLabel);
        ret = GenerateDMLsForOnePathTable(memCtx, &typePathGenInfo, plpAASlots, isInsert, dmlCount);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return ret;
}

/**
 * @brief Create and generate a list of AA slots @p pplpAAlots contributing to changes
 *  in one table @p pOneTableTask->vertexLabel based on @p pChangedEntries.
 *
 * @param[in] memCtx The pointer to the memory context used for allocations.
 * @param[in] pChangedEntries The DMLs affecting one table @p pOneTableTask->vertexLabel.
 * @param[out] pplpAASlots The pointer to a pointer to the list of generated AA slots representing DMLs affecting one
 * table.
 * @param[in] pOneTableTask The pointer to a wrapper storing information about DMLs to one table.
 * @param[in] isInsert true if the DMLs are inserts, otherwise false.
 *
 * @return Status GMERR_OK if everything went fine, otherwise an error code.
 */
static Status CreateAndGenerateListOfAASlotDMLs(DbMemCtxT *memCtx, OpBatchEntryDataT *pChangedEntries,
    DbListT *pplpAASlots, PathPvcTaskT *pOneTableTask, bool isInsert)
{
    Status ret = GMERR_OK;
    if (((pOneTableTask->subEvent == DM_SUBS_EVENT_REPLACE) && (isInsert)) ||
        ((pOneTableTask->subEvent == DM_SUBS_EVENT_DELETE) && (!isInsert))) {
        for (uint32_t idxDML = 0; idxDML < pChangedEntries->slotNum; idxDML++) {
            ret = DbAppendListItem(pplpAASlots, &pChangedEntries->slot[idxDML]);
            if (ret != GMERR_OK) {
                break;
            }
        }
    }
    return ret;
}

static Status InitPlpAASlots(DbMemCtxT *memCtx, DbListT **plpAASlotsInserts, DbListT **plpAASlotsDeletes)
{
    DbListT *plpAASlotsI = (DbListT *)DbDynMemCtxAlloc(memCtx, sizeof(DbListT));
    if (plpAASlotsI == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "list alloc");
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateList(plpAASlotsI, sizeof(AASlotT *), memCtx);

    DbListT *plpAASlotsD = (DbListT *)DbDynMemCtxAlloc(memCtx, sizeof(DbListT));
    if (plpAASlotsD == NULL) {
        DbDynMemCtxFree(memCtx, plpAASlotsD);
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "list alloc");
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateList(plpAASlotsD, sizeof(AASlotT *), memCtx);

    *plpAASlotsInserts = plpAASlotsI;
    *plpAASlotsDeletes = plpAASlotsD;
    return GMERR_OK;
}

static Status SetAASlotLists4Dml(
    DbMemCtxT *memCtx, PathPvcTaskT *pOneTableTask, DbListT *plpAASlotsInserts, DbListT *plpAASlotsDeletes)
{
    SubSearchTaskT *pSubSearchTask = (SubSearchTaskT *)DbListItem(pOneTableTask->psTasks, 0);
    Status ret =
        CreateAndGenerateListOfAASlotDMLs(memCtx, &pSubSearchTask->oplog, plpAASlotsInserts, pOneTableTask, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    // each delete dml splited into different subsearch task
    uint32_t subTaskCnt = DbListGetItemCnt(pOneTableTask->psTasks);
    for (uint32_t i = 0; i < subTaskCnt; i++) {
        pSubSearchTask = (SubSearchTaskT *)DbListItem(pOneTableTask->psTasks, i);
        ret =
            CreateAndGenerateListOfAASlotDMLs(memCtx, &pSubSearchTask->oplog, plpAASlotsDeletes, pOneTableTask, false);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

#define EXTEND_SIZE_FOR_DML_BUFFER 2

/**
 * @brief Counts the number of DMLs required to update paths of type @p pathType that are affected by
 * @p pOneTableTask->vertexLabel.
 *
 * @param[in] memCtx The pointer to the memory context used for allocations.
 * @param[in] pPathInfoGen The pointer to the wrapper for storing information about sessions.
 * @param[out] pNumDMLs The pointer to the number of required DMLs.
 * @param[in] pathType The path type for which DMLs are counted.
 * @param[in] pOneTableTask The pointer to @p PathPvcTaskT for which DMLs we check which paths should be updated.
 *
 * @return Status GMERR_OK if everything went fine, otherwise an error code.
 */
static Status CountNumberOfDMLsTotalOneTable(DbMemCtxT *memCtx, PathInfoGenDmlT *pPathInfoGen, uint32_t *pNumDMLs,
    DmComplexPathTypeE pathType, PathPvcTaskT *pOneTableTask)
{
    Status ret = GMERR_OK;
    uint32_t numDMLs = 0;
    DbListT *plpAASlotsInserts, *plpAASlotsDeletes;
    // init list which store trigger dml of sub search task
    ret = InitPlpAASlots(memCtx, &plpAASlotsInserts, &plpAASlotsDeletes);
    if (ret != GMERR_OK) {
        return ret;
    }
    GenPathInfoGenT typePathGenInfo;
    *pNumDMLs = 0;
    // set list which store trigger dml of sub search task
    ret = SetAASlotLists4Dml(memCtx, pOneTableTask, plpAASlotsInserts, plpAASlotsDeletes);
    if (ret != GMERR_OK) {
        return ret;
    }
    // set all DmComplexPathInfoT and path search Plan
    ret = CreateAndInitializeViewPathLists(
        memCtx, pOneTableTask, pathType, &typePathGenInfo.lpPathInfos, &typePathGenInfo.lpPathPlans);
    if (ret != GMERR_OK) {
        return ret;
    }

    typePathGenInfo.pPrevSession = pPathInfoGen->pPathSearchTask->readViewBefore;
    typePathGenInfo.pSession = pPathInfoGen->pPathSearchTask->readViewAfter;
    typePathGenInfo.pChangedVertLabel = pOneTableTask->vertexLabel;
    if (pathType == DM_COMPLEX_PATH_MERGE) {
        ret = CountNumberOfDMLs(memCtx, &typePathGenInfo, plpAASlotsInserts, &numDMLs, BIT_IS_INSERT);
        if (ret != GMERR_OK) {
            return ret;
        }
        *pNumDMLs += numDMLs * EXTEND_SIZE_FOR_DML_BUFFER;
        ret = CountNumberOfDMLs(memCtx, &typePathGenInfo, plpAASlotsDeletes, &numDMLs, BIT_IS_DELETE);
        if (ret != GMERR_OK) {
            return ret;
        }
        *pNumDMLs += numDMLs * EXTEND_SIZE_FOR_DML_BUFFER;
    } else if (pathType == DM_COMPLEX_PATH_REPLACE) {
        ret = CountNumberOfDMLs(memCtx, &typePathGenInfo, plpAASlotsInserts, &numDMLs, BIT_IS_INSERT | BIT_IS_REPLACE);
        if (ret != GMERR_OK) {
            return ret;
        }
        *pNumDMLs += numDMLs;
    }
    DbDestroyList(plpAASlotsInserts);
    DbDynMemCtxFree(memCtx, plpAASlotsInserts);
    DbDestroyList(plpAASlotsDeletes);
    DbDynMemCtxFree(memCtx, plpAASlotsDeletes);
    DbDestroyList(&typePathGenInfo.lpPathInfos);
    DbDestroyList(&typePathGenInfo.lpPathPlans);
    return ret;
}

/**
 * @brief Counts the number of DMLs required to update paths of type @p pathType affected by the batch of DMLs
 * specified in @p pPathInfoGen->pPathSearchTask->pvcTaskList .
 *
 * @param[in] memCtx The pointer to the memory context used for allocations.
 * @param[in] pPathInfoGen The pointer to the wrapper for storing information about sessions and DMLs.
 * @param[out] pNumDMLs The pointer to the number of required DMLs.
 * @param[in] pathType The path type for which DMLs are counted.
 *
 * @return Status GMERR_OK if everything went fine, otherwise an error code.
 */
static Status CountNumberOfDMLsTotal(
    DbMemCtxT *memCtx, PathInfoGenDmlT *pPathInfoGen, uint32_t *pNumDMLs, DmComplexPathTypeE pathType)
{
    Status ret = GMERR_OK;
    uint32_t tableSearchTaskCnt;
    *pNumDMLs = 0;
    tableSearchTaskCnt = DbListGetItemCnt(&pPathInfoGen->pPathSearchTask->pvcTaskList);
    for (uint32_t tabDMLTaskIdx = 0; tabDMLTaskIdx < tableSearchTaskCnt; tabDMLTaskIdx++) {
        uint32_t numDMLs;
        PathPvcTaskT oneTableTask;
        numDMLs = 0;
        oneTableTask = *(PathPvcTaskT *)DbListItem(&pPathInfoGen->pPathSearchTask->pvcTaskList, tabDMLTaskIdx);
        ret = CountNumberOfDMLsTotalOneTable(memCtx, pPathInfoGen, &numDMLs, pathType, &oneTableTask);
        if (ret != GMERR_OK) {
            return ret;
        }
        *pNumDMLs += numDMLs;
    }
    return GMERR_OK;
}

/**
 * @brief Create the buffer for DML requests and fills the header by the specified number of DMLs @p numDMLs .
 *
 * @param[in] memCtx The pointer to the memory context used for allocations.
 * @param[out] ppPathBuffer The pointer to the pointer to the created and initialized fixed buffer.
 * @param[in] numDMLs The number of DMLs that is supposed to be generated.
 *
 * @return Status GMERR_OK if everything went fine, otherwise an error code.
 */
static Status CreateAndFillHeaderOfTheDMLBuffer(DbMemCtxT *memCtx, FixBufferT **ppPathBuffer, uint32_t numDMLs)
{
    Status ret = GMERR_OK;
    FixBufferT *pPathBuffer;
    uint8_t *pBuffForFixBuffer;

    pPathBuffer = NULL;
    if (numDMLs == 0) {
        goto EXIT;
    }

    pPathBuffer = (FixBufferT *)DbDynMemCtxAlloc(memCtx, sizeof(FixBufferT));
    if (pPathBuffer == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "FixBufferT alloc");
        return GMERR_OUT_OF_MEMORY;
    }
    pBuffForFixBuffer = (uint8_t *)DbDynMemCtxAlloc(memCtx, BUFFER_SIZE_DML);
    if (pBuffForFixBuffer == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "FixBufferT alloc");
        ret = GMERR_OUT_OF_MEMORY;
        goto CLEAN_P_BUF;
    }

    FixBufInit(pPathBuffer, pBuffForFixBuffer, BUFFER_SIZE_DML, 0, FIX_BUF_FLAG_EXTEND_BUFFER, memCtx);
    ret = FillHeaderOfFixBufferForDMLs(pPathBuffer, numDMLs);
    if (ret == GMERR_OK) {
        goto EXIT;
    }
    DbDynMemCtxFree(memCtx, pBuffForFixBuffer);
CLEAN_P_BUF:
    DbDynMemCtxFree(memCtx, pPathBuffer);
EXIT:
    *ppPathBuffer = pPathBuffer;
    return ret;
}

/**
 * @brief Generates DMLs for all paths of type @p pathType to which the table @p pOneTableTask->vertexLabel
 *  belongs to.
 *
 * @param[in] memCtx The pointer to the memory context used for allocations.
 * @param[in/out] pPathInfoGen The wrapper that stores sessions and generated buffers.
 * @param[in] pathType The path type for which DMLs are create.
 * @param[in] pOneTableTask The pointer to the wrapper with all DMLs affecting one table and related paths.
 *
 * @return Status GMERR_OK if everything went fine, otherwise an error code.
 */
static Status GenerateDMLsForPathTablesOneTable(DbMemCtxT *memCtx, PathInfoGenDmlT *pPathInfoGen,
    DmComplexPathTypeE pathType, PathPvcTaskT *pOneTableTask, DMLCounterT *dmlCount)
{
    Status ret = GMERR_OK;
    DbListT *plpAASlotsInserts;
    DbListT *plpAASlotsDeletes;
    ret = InitPlpAASlots(memCtx, &plpAASlotsInserts, &plpAASlotsDeletes);
    if (ret != GMERR_OK) {
        return ret;
    }
    GenPathInfoGenT typePathGenInfo;
    ret = SetAASlotLists4Dml(memCtx, pOneTableTask, plpAASlotsInserts, plpAASlotsDeletes);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = CreateAndInitializeViewPathLists(
        memCtx, pOneTableTask, pathType, &typePathGenInfo.lpPathInfos, &typePathGenInfo.lpPathPlans);
    if (ret != GMERR_OK) {
        return ret;
    }

    typePathGenInfo.pPrevSession = pPathInfoGen->pPathSearchTask->readViewBefore;
    typePathGenInfo.pSession = pPathInfoGen->pPathSearchTask->readViewAfter;
    typePathGenInfo.pChangedVertLabel = pOneTableTask->vertexLabel;
    typePathGenInfo.pDeleteBuffer = pPathInfoGen->pDeletePathBuffer;
    typePathGenInfo.pInsertBuffer = pPathInfoGen->pInsertPathBuffer;
    typePathGenInfo.pathType = pathType;
    if (pathType == DM_COMPLEX_PATH_MERGE) {
        ret = GenerateDMLsForPathTablesOneType(memCtx, &typePathGenInfo, plpAASlotsInserts, true, dmlCount);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = GenerateDMLsForPathTablesOneType(memCtx, &typePathGenInfo, plpAASlotsDeletes, false, dmlCount);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else if (pathType == DM_COMPLEX_PATH_REPLACE) {
        ret = GenerateDMLsForPathTablesOneType(memCtx, &typePathGenInfo, plpAASlotsInserts, true, dmlCount);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    DbDestroyList(plpAASlotsInserts);
    DbDynMemCtxFree(memCtx, plpAASlotsInserts);
    DbDestroyList(plpAASlotsDeletes);
    DbDynMemCtxFree(memCtx, plpAASlotsDeletes);
    DbDestroyList(&typePathGenInfo.lpPathInfos);
    DbDestroyList(&typePathGenInfo.lpPathPlans);

    return ret;
}

Status RewriteBufferHeader(FixBufferT *buffer, uint32_t newSize)
{
    uint32_t seekPos = buffer->seekPos;
    uint32_t pos = buffer->pos;
    buffer->seekPos = 0;
    buffer->pos = 0;
    Status ret = FillHeaderOfFixBufferForDMLs(buffer, newSize);
    buffer->seekPos = seekPos;
    buffer->pos = pos;
    return ret;
}

Status GenerateDMLsForPathTables(DbMemCtxT *memCtx, PathInfoGenDmlT *pPathInfoGen, DmComplexPathTypeE pathType)
{
    DB_POINTER2(memCtx, pPathInfoGen);
    // since buffer(FixBufferT) can't extend, we need to get count of dml first
    uint32_t tableSearchTaskCnt = DbListGetItemCnt(&pPathInfoGen->pPathSearchTask->pvcTaskList);
    uint32_t totalNumberOfDMLs = 0;
    Status ret = CountNumberOfDMLsTotal(memCtx, pPathInfoGen, &totalNumberOfDMLs, pathType);
    if (ret != GMERR_OK) {
        return ret;
    }
    pPathInfoGen->pDeletePathBuffer = NULL;
    pPathInfoGen->pInsertPathBuffer = NULL;
    if (totalNumberOfDMLs == 0) {
        return ret;
    }

    // init buffer header
    if (pathType == DM_COMPLEX_PATH_MERGE) {
        ret = CreateAndFillHeaderOfTheDMLBuffer(memCtx, &pPathInfoGen->pDeletePathBuffer, totalNumberOfDMLs);
    }
    ret = CreateAndFillHeaderOfTheDMLBuffer(memCtx, &pPathInfoGen->pInsertPathBuffer, totalNumberOfDMLs);
    if (ret != GMERR_OK) {
        return ret;
    }
    DMLCounterT dmlCount = {0, 0};
    for (uint32_t tabDMLTaskIdx = 0; tabDMLTaskIdx < tableSearchTaskCnt; tabDMLTaskIdx++) {
        PathPvcTaskT oneTableTask =
            *(PathPvcTaskT *)DbListItem(&pPathInfoGen->pPathSearchTask->pvcTaskList, tabDMLTaskIdx);
        ret = GenerateDMLsForPathTablesOneTable(memCtx, pPathInfoGen, pathType, &oneTableTask, &dmlCount);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if (pathType == DM_COMPLEX_PATH_MERGE) {
        if (dmlCount.deletes == 0) {
            pPathInfoGen->pDeletePathBuffer = NULL;
        } else if (dmlCount.deletes != totalNumberOfDMLs) {
            ret = RewriteBufferHeader(pPathInfoGen->pDeletePathBuffer, dmlCount.deletes);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        if (dmlCount.inserts == 0) {
            pPathInfoGen->pInsertPathBuffer = NULL;
        } else if (dmlCount.inserts != totalNumberOfDMLs) {
            ret = RewriteBufferHeader(pPathInfoGen->pInsertPathBuffer, dmlCount.inserts);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }

    return ret;
}

#ifdef __cplusplus
}
#endif
