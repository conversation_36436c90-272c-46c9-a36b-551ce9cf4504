/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: The entry of execution concurrency control
 * Author: baiyang
 * Create: 2022-01-17
 */
#include "ee_concurrency_control.h"
#include "db_internal_error.h"
#include "se_heap.h"
#include "se_dfx.h"
#include "ee_context.h"
#include "adpt_sleep.h"
#include "ee_rsm.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct BaseLatchParamT {
    LabelRWLatchT *labelRWLatch;
    CCExecuteTypeE ccExeType;
    ConcurrencyControlE ccType;
} BaseLatchParam;  // release与acquire所共有的参数

typedef struct AcquireLatchParamT {
    BaseLatchParam baseParam;
    DmLabelTypeE labelType;
    uint32_t reserve;
    ShmemPtrT heapShmAddr;
    ShmemPtrT labelLatchShmAddr;  // 缓存点和边的latch共享内存addr
    uint32_t labelId;
    uint32_t labelLatchId;
    uint32_t labelLatchVersionId;
    uint32_t timeoutMs;
    uint64_t startTime;  // 请求开始时间session->lastStmtReqTimestamp (cpu cycle)
} AcqLatchParam;

static inline __attribute__((always_inline)) void QryReleaseHcLatchForRuMode(
    CCExecuteTypeE ccExeType, LabelRWLatchT *labelRWLatch)
{
    switch (ccExeType) {
        case CC_EXECUTE_DML:
            if (labelRWLatch->isAddHcLatch) {
                HcWLatchRelease(labelRWLatch);
            }
            break;
        case CC_EXECUTE_DQL:
            if (labelRWLatch->isAddHcLatch) {
                HcRLatchRelease(labelRWLatch);
            }
            break;
        case CC_EXECUTE_DDL:
            break;
        default:
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY, "inv ccExeType=%" PRIu32 "", (uint32_t)ccExeType);
    }
    return;
}

static inline __attribute__((always_inline)) void QryReleaseLabelLatchForRuMode(
    CCExecuteTypeE ccExeType, LabelRWLatchT *labelRWLatch)
{
    switch (ccExeType) {
        case CC_EXECUTE_DML:
            LabelRLatchRelease(labelRWLatch);
            LabelLatchAtomicClearRuMode(labelRWLatch);
            break;
        case CC_EXECUTE_DQL:
            LabelRLatchRelease(labelRWLatch);
            break;
        case CC_EXECUTE_DDL:
            LabelWLatchRelease(labelRWLatch);
            LabelLatchAtomicClearRuMode(labelRWLatch);
            break;
        default:
            DB_LOG_AND_SET_LASERR(
                GMERR_INVALID_PROPERTY, "inv ccExeType=%" PRIu32 " in Ru releaseLatch", (uint32_t)ccExeType);
    }
}

void QryReleaseLabelLatchForLabelLatchMode(CCExecuteTypeE ccExeType, LabelRWLatchT *labelRWLatch)
{
    switch (ccExeType) {
        case CC_EXECUTE_DML:
        case CC_EXECUTE_DDL:
            LabelWLatchRelease(labelRWLatch);
            break;
        case CC_EXECUTE_DQL:
            LabelRLatchRelease(labelRWLatch);
            break;
        default:
            DB_LOG_AND_SET_LASERR(
                GMERR_INVALID_PROPERTY, "inv ccExeType=%" PRIu32 " in labelLatch releaseLatch", (uint32_t)ccExeType);
    }
}

void QryReleaseLabelLatchForNormalMode(CCExecuteTypeE ccExeType, LabelRWLatchT *labelRWLatch)
{
    switch (ccExeType) {
        case CC_EXECUTE_DDL:
            LabelWLatchRelease(labelRWLatch);
            break;

        case CC_EXECUTE_DML:
        case CC_EXECUTE_DQL:
            LabelRLatchRelease(labelRWLatch);
            break;
        default:
            DB_LOG_AND_SET_LASERR(
                GMERR_INVALID_PROPERTY, "inv ccExeType=%" PRIu32 " in labelLatch releaseLatch", (uint32_t)ccExeType);
    }
}

/* vertexLabel & kvTable & edgeLabe 解锁 */
void QryReleaseLabelLatch(CCExecuteTypeE ccExeType, ConcurrencyControlE ccType, LabelRWLatchT *labelRWLatch)
{
    switch (ccType) {
        case CONCURRENCY_CONTROL_READ_UNCOMMIT:
            QryReleaseHcLatchForRuMode(ccExeType, labelRWLatch);
            QryReleaseLabelLatchForRuMode(ccExeType, labelRWLatch);
            break;
        case CONCURRENCY_CONTROL_LABEL_LATCH:
        case CONCURRENCY_CONTROL_READ_COMMIT_LABEL_LATCH:
            QryReleaseLabelLatchForLabelLatchMode(ccExeType, labelRWLatch);
            break;
        case CONCURRENCY_CONTROL_NORMAL:
            QryReleaseLabelLatchForNormalMode(ccExeType, labelRWLatch);
            break;
        default:
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY, "inv ccType=%" PRIu32 "in releaseLatch", (uint32_t)ccType);
    }
}

Status QryAcqHcLatchForRuMode(const AcqLatchParam *param)
{
    bool isGetLatch = false;
    LabelRWLatchT *labelRWLatch = param->baseParam.labelRWLatch;
    if (!labelRWLatch->isAddHcLatch) {
        return GMERR_OK;
    }
    switch (SWITCH_LIKELY(param->baseParam.ccExeType, CC_EXECUTE_DML)) {
        case CC_EXECUTE_DML:
            isGetLatch = HcWLatchTimedAcquire(labelRWLatch, param->timeoutMs * (uint32_t)USECONDS_IN_MSECOND);
            break;
        case CC_EXECUTE_DQL:
            isGetLatch = HcRLatchTimedAcquire(labelRWLatch, param->timeoutMs * (uint32_t)USECONDS_IN_MSECOND);
            break;
        case CC_EXECUTE_DDL:
            isGetLatch = true;
            break;
        default:
            QryReleaseLabelLatchForRuMode(param->baseParam.ccExeType, labelRWLatch);
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "inv ccExeType=%" PRIu32 "in hashCluster Index acqLatch.",
                (uint32_t)param->baseParam.ccExeType);
            return GMERR_DATA_EXCEPTION;
    }
    if (SECUREC_UNLIKELY(!isGetLatch)) {
        // hcLatch加不上, 主动释放已经上锁的labelRWLatch
        QryReleaseLabelLatchForRuMode(param->baseParam.ccExeType, labelRWLatch);
        DB_LOG_WARN(GMERR_LOCK_NOT_AVAILABLE, "get hcLatch unsucc, ccExeType = %" PRIu32 ".",
            (uint32_t)param->baseParam.ccExeType);
        return GMERR_LOCK_NOT_AVAILABLE;
    }
    return GMERR_OK;
}

static Status QryCheckLabelLatch(LabelRWLatchT *labelRWLatch, const AcqLatchParam *param, bool isGetLatch)
{
    Status ret = LabelLatchCheckVersion(labelRWLatch, param->labelLatchVersionId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        if (isGetLatch) {  // 已加上锁，但是versionId不匹配，得主动释放
            QryReleaseLabelLatchForRuMode(param->baseParam.ccExeType, labelRWLatch);
        }
        DB_LOG_AND_SET_LASERR(
            ret, "lb have been dropped in Ru acqLatch, ccExeType: %" PRId32, (int32_t)param->baseParam.ccExeType);
        return ret;
    }
    return GMERR_OK;
}

Status QryAcqLabelLatchForRuMode(const AcqLatchParam *param)
{
    bool isGetLatch = false;
    bool isTimeout = false;
    LabelRWLatchT *labelRWLatch = param->baseParam.labelRWLatch;
    do {
        switch (param->baseParam.ccExeType) {
            case CC_EXECUTE_DML:
                isGetLatch = LabelLatchAtomicSetRuMode(labelRWLatch, param->startTime, param->timeoutMs);  // 写写串行
                isTimeout = !isGetLatch;
                if (isTimeout) {
                    break;
                }
                isGetLatch = LabelRLatchTimedAcquire(labelRWLatch, param->timeoutMs * (uint32_t)USECONDS_IN_MSECOND);
                isTimeout = !isGetLatch;
                if (isTimeout) {
                    LabelLatchAtomicClearRuMode(labelRWLatch);
                }
                break;
            case CC_EXECUTE_DQL:
                isGetLatch = LabelRLatchTryAcquire(labelRWLatch);
                isTimeout = false;
                break;
            case CC_EXECUTE_DDL:
                // 聚簇容器内部会释放labelRWLatch.rwlatch读锁后上写锁进行扩缩容，因此使用labelRWLatch.ruModeNum控制DDL与DML的并发，非聚簇容器不存在该问题
                isGetLatch = LabelLatchAtomicSetRuMode(labelRWLatch, param->startTime, param->timeoutMs);  // 写写串行
                isTimeout = !isGetLatch;
                if (isTimeout) {
                    break;
                }
                isGetLatch = LabelWLatchTimedAcquire(labelRWLatch, param->timeoutMs * (uint32_t)USECONDS_IN_MSECOND);
                isTimeout = !isGetLatch;
                if (isTimeout) {
                    LabelLatchAtomicClearRuMode(labelRWLatch);
                }
                break;
            default:
                DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "inv ccExeType=%" PRIu32 " in Ru acqLatch.",
                    (uint32_t)param->baseParam.ccExeType);
                return GMERR_DATA_EXCEPTION;
        }
        Status ret = QryCheckLabelLatch(labelRWLatch, param, isGetLatch);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        if (SECUREC_UNLIKELY(!isGetLatch && !isTimeout)) {  // 未获得锁，进入休眠一段时间
            DbUsleep(TRY_LATCH_SLEEP_TIME_US);
        }
    } while (!isGetLatch && !isTimeout);
    return isTimeout ? GMERR_LOCK_NOT_AVAILABLE : QryAcqHcLatchForRuMode(param);
}

Status QryAcqLabelLatchForLabelLatchMode(const AcqLatchParam *param)
{
    Status ret;
    bool isGetLatch = false;
    bool isTimeout = false;
    LabelRWLatchT *labelRWLatch = param->baseParam.labelRWLatch;
    while (!isGetLatch && !isTimeout) {
        switch (param->baseParam.ccExeType) {
            case CC_EXECUTE_DML:
            case CC_EXECUTE_DDL:
                isGetLatch = LabelWLatchTimedAcquire(param->baseParam.labelRWLatch, param->timeoutMs);
                isTimeout = !isGetLatch;
                break;
            case CC_EXECUTE_DQL:
                isGetLatch = LabelRLatchTryAcquire(param->baseParam.labelRWLatch);
                isTimeout = false;
                break;
            default:
                DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "inv ccExeType=%" PRIu32 " in labelLatch acqLatch.",
                    (uint32_t)param->baseParam.ccExeType);
                return GMERR_DATA_EXCEPTION;
        }
        ret = LabelLatchCheckVersion(labelRWLatch, param->labelLatchVersionId);
        if (ret != GMERR_OK) {
            if (isGetLatch) {  // 已加上锁，但是versionId不匹配，得主动释放
                QryReleaseLabelLatchForLabelLatchMode(param->baseParam.ccExeType, param->baseParam.labelRWLatch);
            }
            DB_LOG_AND_SET_LASERR(
                ret, "acqLatch in drop label, ccExeType: %" PRIu32, (uint32_t)param->baseParam.ccExeType);
            return ret;
        }
        if (!isGetLatch && !isTimeout) {  // 未获得锁，进入休眠一段时间
            DbUsleep(TRY_LATCH_SLEEP_TIME_US);
        }
    }
    return isTimeout ? GMERR_LOCK_NOT_AVAILABLE : ret;
}

Status QryAcqTrxLockForNormalMode(QryStmtT *stmt, AcqLatchParam *param, LockStatT *trxLockStat)
{
    Status ret = GMERR_OK;
    switch (param->baseParam.ccExeType) {
        case CC_EXECUTE_DML:
            if (SeTransGetTrxType(stmt->session->seInstance) != OPTIMISTIC_TRX) {
                if (SeTransGetIsolationLevel(stmt->session->seInstance) == SERIALIZABLE) {
                    ret = HeapLabelXLockAcquire(stmt->session->seInstance, param->labelId, trxLockStat);
                } else {
                    ret = HeapLabelIXLockAcquire(stmt->session->seInstance, param->labelId, trxLockStat);
                }
            }
            break;
        case CC_EXECUTE_DQL:
            if (SeTransGetTrxType(stmt->session->seInstance) != OPTIMISTIC_TRX) {
                if (SeTransGetIsolationLevel(stmt->session->seInstance) == SERIALIZABLE) {
                    ret = HeapLabelSLockAcquire(stmt->session->seInstance, param->labelId, trxLockStat);
                } else {
                    ret = HeapLabelISLockAcquire(stmt->session->seInstance, param->labelId, trxLockStat);
                }
            }
            break;
        case CC_EXECUTE_DDL:
            if (SeTransGetTrxType(stmt->session->seInstance) != OPTIMISTIC_TRX) {
                ret = HeapLabelXLockAcquire(stmt->session->seInstance, param->labelId, trxLockStat);
            }
            break;
        default:
            ret = GMERR_DATA_EXCEPTION;
    }

    if (ret != GMERR_OK) {  // 事务锁获取失败，直接返回
        DB_LOG_WARN_AND_SET_LASTERR(ret, "get trx lock, ccExeType: %" PRId32, (int32_t)param->baseParam.ccExeType);
        return ret;
    }

    return GMERR_OK;
}

Status QryAcqLabelLatchForNormalMode(AcqLatchParam *param)
{
    Status ret = GMERR_OK;
    bool isTimeout = false;
    const uint32_t timeoutMs = param->timeoutMs;
    switch (param->baseParam.ccExeType) {
        case CC_EXECUTE_DML:
        case CC_EXECUTE_DQL:
            isTimeout =
                !LabelRLatchTimedAcquire(param->baseParam.labelRWLatch, timeoutMs * (uint32_t)USECONDS_IN_MSECOND);
            break;
        case CC_EXECUTE_DDL:
            // Normal模式下事务生命周期可能很长，LabelLatch也需要超时机制。
            // DQL/DML加的是读锁不会冲突，所以不用超时机制
            isTimeout =
                !LabelWLatchTimedAcquire(param->baseParam.labelRWLatch, timeoutMs * (uint32_t)USECONDS_IN_MSECOND);
            break;
        default:
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "inv ccExeType(%" PRIu32 ") in acqLatch for normal mode",
                (uint32_t)param->baseParam.ccExeType);
            return GMERR_DATA_EXCEPTION;
    }

    if (isTimeout) {
        ret = GMERR_LOCK_NOT_AVAILABLE;
        DB_LOG_WARN_AND_SET_LASTERR(
            ret, "busy in normal acqLatch, ccExeType: %" PRId32, (int32_t)param->baseParam.ccExeType);
        return ret;
    }

    ret = LabelLatchCheckVersion(param->baseParam.labelRWLatch, param->labelLatchVersionId);
    if (ret != GMERR_OK) {
        QryReleaseLabelLatchForNormalMode(param->baseParam.ccExeType, param->baseParam.labelRWLatch);
        DB_LOG_WARN_AND_SET_LASTERR(
            ret, "lb have been dropped in normal acqLatch, ccExeType: %" PRId32, (int32_t)param->baseParam.ccExeType);
        return ret;
    }

    return GMERR_OK;
}

Status QryAcqLatchForLabelInner(AcqLatchParam *param)
{
    Status ret = GMERR_OK;
    switch (SWITCH_LIKELY(param->baseParam.ccType, CONCURRENCY_CONTROL_READ_UNCOMMIT)) {
        case CONCURRENCY_CONTROL_READ_UNCOMMIT:
            ret = QryAcqLabelLatchForRuMode(param);
            break;
        case CONCURRENCY_CONTROL_LABEL_LATCH:
        case CONCURRENCY_CONTROL_READ_COMMIT_LABEL_LATCH:
            ret = QryAcqLabelLatchForLabelLatchMode(param);
            break;
        case CONCURRENCY_CONTROL_NORMAL:
            ret = QryAcqLabelLatchForNormalMode(param);
            break;
        default:
            DB_LOG_AND_SET_LASERR(
                GMERR_DATA_EXCEPTION, "inv ccType=%" PRIu32 " when acqLatch.", (uint32_t)param->baseParam.ccType);
            return GMERR_DATA_EXCEPTION;
    }
    return ret;
}

CCExecuteTypeE QryGetCCExeType(QryStmtT *stmt)
{
    // 普通事务下Yang DQL 不再加DQL锁，只加DML锁，否则会与ddl并发死锁
    if (SECUREC_LIKELY(QryTypeIsDML(stmt->context->type) || stmt->context->type == QRY_TYPE_COMMIT_TRANS ||
                       QryTypeIsYangDQL(stmt->context->type) || QryTypeIsYangImportData(stmt->context->type))) {
        return CC_EXECUTE_DML;
    } else if (QryTypeIsDQL(stmt->context->type)) {
        return CC_EXECUTE_DQL;
    } else if (QryTypeIsDDL(stmt->context->type) || QryTypeIsPrivDCL(stmt->context->type)) {
        return CC_EXECUTE_DDL;
    } else {
        return CC_EXECUTE_MAX;
    }
}

inline CCExecuteTypeE QryGetCCExeTypeWithCCType(QryStmtT *stmt, ConcurrencyControlE ccType)
{
    if (SECUREC_LIKELY(DmIsLabelLiteTrx(ccType))) {
        if (QryTypeIsCheck(stmt->context->type)) {  // 轻量化事务下开启/结束对账可以当做dml处理（单写者）
            return CC_EXECUTE_DML;
        }
    }
    return QryGetCCExeType(stmt);
}

void QryRemoveLabelLatchAfterDrop(DmLabelTypeE type, uint32_t labelLatchId, uint32_t labelLatchVersionId,
    ConcurrencyControlE ccType, DbInstanceHdT dbInstance)
{
    // drop操作，需要将表latch的versionId++后，释放表latch
    LabelRWLatchT *labelRWLatch = (LabelRWLatchT *)GetLabelRWLatchPtrById(labelLatchId, dbInstance);
    if (labelRWLatch == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PROPERTY, "inv labelRWLatch(labelLatchId:%" PRIu32 ")", labelLatchId);
        return;
    }
    if (type == RESOURCE_POOL || ccType != CONCURRENCY_CONTROL_READ_UNCOMMIT) {  // 资源池和非RU场景不用清RuMode
        RemoveLabelLatchWithOutRuMode(labelRWLatch, labelLatchId, labelLatchVersionId, dbInstance);
    } else {
        RemoveLabelLatchAndRuMode(labelRWLatch, labelLatchId, labelLatchVersionId, dbInstance);
    }
}

#ifdef FEATURE_RESPOOL_SRV
/* resource column pool 解锁 */
void QryReleaseLatchForResColPool(CCExecuteTypeE ccExeType, LabelRWLatchT *labelRWLatch)
{
    DB_POINTER(labelRWLatch);
    switch (ccExeType) {
        case CC_EXECUTE_DDL:
            LabelWLatchRelease(labelRWLatch);
            break;
        case CC_EXECUTE_DML:
        case CC_EXECUTE_DQL:
            LabelRLatchRelease(labelRWLatch);
            break;
        default:
            DB_LOG_ERROR(
                GMERR_INVALID_PROPERTY, "inv ccExeType=%" PRIu32 " in releaseLatchForResColPool", (uint32_t)ccExeType);
    }
}

Status QryAcqLatchForResColPoolInner(QryStmtT *stmt, AcqLatchParam *param)
{
    DB_POINTER2(stmt, param);
    LabelRWLatchT *labelRWLatch = param->baseParam.labelRWLatch;
    bool isGetLatch = false;
    Status ret = GMERR_OK;
    while (!isGetLatch) {
        ret = LabelLatchCheckVersion(labelRWLatch, param->labelLatchVersionId);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "lb versionId=%" PRIu32 " have been dropped when acqLatchForResColPool.",
                param->labelLatchVersionId);
            return ret;
        }
        switch (param->baseParam.ccExeType) {
            case CC_EXECUTE_DDL:
                LabelWLatchAcquire(labelRWLatch);
                isGetLatch = true;
                break;
            case CC_EXECUTE_DML:
            case CC_EXECUTE_DQL:
                isGetLatch = LabelRLatchTryAcquire(labelRWLatch);
                break;
            default:
                DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "inv ccExeType=%" PRIu32 " in acqLatchForResColPool",
                    (uint32_t)param->baseParam.ccExeType);
                return GMERR_DATA_EXCEPTION;
        }
        if (!isGetLatch) {
            DbUsleep(TRY_LATCH_SLEEP_TIME_US);
        }
    }
    ret = LabelLatchCheckVersion(labelRWLatch, param->labelLatchVersionId);
    if (ret != GMERR_OK) {
        QryReleaseLatchForResColPool(param->baseParam.ccExeType, labelRWLatch);
        DB_LOG_AND_SET_LASERR(
            ret, "acqLatchForResColPool in drop label, versionId:%" PRIu32 "", param->labelLatchVersionId);
    }
    return ret;
}
#endif

// 此处原有代码是latchId和ccExeType都相同才认为是同一个锁。
// 但是这样处理Normal模式会产生死锁，连接1两次读锁中间如果,如果被连接2中间加入一个写锁，目前GMDB读写锁实现写优先(意向锁)，就会死锁。
// 连接1(DML/DQL):显示开启事务------------>LabelLatch读锁------------->LabelLatch读锁-------->提交事务
// 连接2(DDL/DCL):---------------------------------------->LabelLatch写锁--------------------------->
// 轻量化事务模式：不支持显示事务，latchId和ccExeType都只会有一个。
// Normal模式：DDL/DCL不支持事务，DML/DQL都是加的读锁，所以可以不判断ccExeType是否相同。

QryLatchDefT *QryFindLatchInfoByLatchId(SessionT *session, uint32_t labelLatchId)
{
    QryTrxStatisticT *trxStatistics = &session->trxStatistics;
    if (trxStatistics->usedNum == 0) {
        return NULL;
    }
    QryLatchDefT *latchDef;
    for (uint32_t j = 0; j < trxStatistics->usedNum; j++) {
        latchDef = &trxStatistics->labelLatchArr[j];
        if (labelLatchId == latchDef->latchId) {
            return latchDef;
        }
    }

    GaListT *labelLatch = &trxStatistics->labelLatch;
    for (uint32_t i = 0; i < DbGaListGetCount(labelLatch); i++) {
        latchDef = (QryLatchDefT *)DbGaListGet(labelLatch, i);
        if (latchDef != NULL && labelLatchId == latchDef->latchId) {
            return latchDef;
        }
    }
    return NULL;
}

/* 若存在latchId都相同的latch，则返回该latch info（即该链接已经加过这个锁）
 * 否则返回第一个可用的空闲latch info供加锁使用
 */
QryLatchDefT *QryGetLatchInfo(SessionT *session, uint32_t latchId, bool *isLock)
{
    DB_POINTER2(session, isLock);
    *isLock = false;
    QryLatchDefT *latchDef = QryFindLatchInfoByLatchId(session, latchId);
    if (latchDef != NULL) {
        *isLock = true;
        return latchDef;
    }

    QryTrxStatisticT *trxStatistics = &session->trxStatistics;
    QryLatchDefT *newLatchDef = NULL;
    if (trxStatistics->usedNum < LABEL_LATCH_INFO_ARRAY_SIZE) {
        newLatchDef = &trxStatistics->labelLatchArr[trxStatistics->usedNum];
        trxStatistics->usedNum++;
    } else {
        GaListT *labelLatch = &session->trxStatistics.labelLatch;
        Status ret = DbGaListNew(labelLatch, sizeof(QryLatchDefT), (void **)&newLatchDef);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "create galist when set latch info.");
            return NULL;
        }
        newLatchDef->latchId = DB_INVALID_UINT32;
    }
    return newLatchDef;
}

void QryReleaseLatchInfo(SessionT *session, const QryLatchDefT *latchInfo)
{
    DB_POINTER(session);
    QryTrxStatisticT *trxStatistics = &session->trxStatistics;
    for (uint32_t j = 0; j < trxStatistics->usedNum; j++) {
        if (latchInfo == &trxStatistics->labelLatchArr[j]) {
            trxStatistics->usedNum--;
            return;
        }
    }
    GaListT *labelLatch = &trxStatistics->labelLatch;
    for (uint32_t i = 0; i < DbGaListGetCount(labelLatch); i++) {
        QryLatchDefT *latchDef = (QryLatchDefT *)DbGaListGet(labelLatch, i);
        if (latchDef == latchInfo) {
            DbGaListDelete(labelLatch, i);
            return;
        }
    }
    return;
}

inline static void QrySetLatchInfo(QryLatchDefT *latchInfo, const AcqLatchParam *param)
{
    latchInfo->latchId = param->labelLatchId;
    latchInfo->labelLatchVersionId = param->labelLatchVersionId;
    latchInfo->type = param->labelType;
    latchInfo->ccType = param->baseParam.ccType;
    latchInfo->ccExeType = param->baseParam.ccExeType;
    latchInfo->labelLatchType = LABEL_LATCH_INVALID;  // FastPath不再使用这个字段，只有datalog使用
    latchInfo->latch = (void *)param->baseParam.labelRWLatch;
}

inline static __attribute__((always_inline)) Status SetAcqParamOfLatchInfo(
    AcqLatchParam *param, const QryLatchDefT *latchInfo, DbInstanceHdT dbInstance)
{
    DB_POINTER2(param, latchInfo);
    if (SECUREC_LIKELY(param->labelLatchId == latchInfo->latchId &&
                       param->labelLatchVersionId == latchInfo->labelLatchVersionId)) {
        /* latchId和latch一定是严格绑定的，否则就可能很出问题 */
        DB_ASSERT(latchInfo->latch != NULL);
        param->baseParam.labelRWLatch = (LabelRWLatchT *)latchInfo->latch;
    } else if (param->labelType == RESOURCE_POOL) {
        param->baseParam.labelRWLatch = (LabelRWLatchT *)GetLabelRWLatchPtrById(param->labelLatchId, dbInstance);
        if (SECUREC_UNLIKELY(param->baseParam.labelRWLatch == NULL)) {
            DB_LOG_AND_SET_LASERR(
                GMERR_INVALID_PROPERTY, "inv respool LabelRWLatch, labelLatchId=%" PRIu32 ".", param->labelLatchId);
            return GMERR_INVALID_PROPERTY;
        }
    } else if (param->labelType == PRIVILEGE) {
        param->baseParam.labelRWLatch = (LabelRWLatchT *)GetLabelRWLatchPtrById(param->labelLatchId, dbInstance);
        if (SECUREC_UNLIKELY(param->baseParam.labelRWLatch == NULL)) {
            DB_LOG_AND_SET_LASERR(
                GMERR_INVALID_PROPERTY, "inv priv LabelRWLatch, labelLatchId=%" PRIu32 ".", param->labelLatchId);
            return GMERR_INVALID_PROPERTY;
        }
    } else {
        param->baseParam.labelRWLatch = (LabelRWLatchT *)DbShmPtrToAddr(param->labelLatchShmAddr);
        if (SECUREC_UNLIKELY(param->baseParam.labelRWLatch == NULL)) {
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY,
                "inv labelRWLatch addr, segId=%" PRIu32 ", offset=%" PRIu32 ".", param->labelLatchShmAddr.segId,
                param->labelLatchShmAddr.offset);
            return GMERR_INVALID_PROPERTY;
        }
    }
    return GMERR_OK;
}

static Status QryCheckNspStatus(QryStmtT *stmt)
{
    Status ret = GMERR_OK;
    uint32_t nspId = stmt->session->namespaceId;
    DmNamespaceT *nsp = stmt->session->nsp;
    // 如果nsp为NULL，默认为"public" namespace不进行校验，因为isClearing不为true
    if (SECUREC_UNLIKELY(nsp != NULL && nsp->state != NSP_NORMAL)) {
        if (stmt->context->opCode == MSG_OP_RPC_CLEAR_NAMESPACE ||
            stmt->context->opCode == MSG_OP_RPC_TRUNCATE_NAMESPACE || stmt->context->opCode == MSG_OP_RPC_IMPORT_DATA) {
            return GMERR_OK;  // 当前namespaceID在清理自身需要过滤处理
        } else {
            ret = GMERR_INTERNAL_ERROR;
            DB_LOG_AND_SET_LASERR(ret, "nsp is clearing, nspId is %" PRIu32 ", opCode is %" PRIi32 ".", nspId,
                (int32_t)stmt->context->opCode);
            return ret;
        }
    }
    return GMERR_OK;
}

Status QryAcqLatchForLabel(QryStmtT *stmt, AcqLatchParam *param, bool isDrop)
{
    DB_POINTER2(stmt, param);
    QryLatchDefT *latchInfo = NULL;
    QryTrxStatisticT *trxStatistics = &stmt->session->trxStatistics;
    if (SECUREC_LIKELY(trxStatistics->usedNum == 0)) {
        latchInfo = &trxStatistics->labelLatchArr[0];
        trxStatistics->usedNum++;
    } else {
        bool isLock;
        latchInfo = QryGetLatchInfo(stmt->session, param->labelLatchId, &isLock);
        if (SECUREC_UNLIKELY(latchInfo == NULL)) {
            DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "get latch info for lb");
            return GMERR_INTERNAL_ERROR;
        }

        if (isLock) {
            // 避免显示开启事务时，表latch加多次
            latchInfo->isDrop = latchInfo->isDrop || isDrop;
            return GMERR_OK;
        }
    }

    Status ret = SetAcqParamOfLatchInfo(param, latchInfo, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        QryReleaseLatchInfo(stmt->session, latchInfo);
        return ret;
    }

    ret = QryAcqLatchForLabelInner(param);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        QryReleaseLatchInfo(stmt->session, latchInfo);
        return ret;
    }
    QrySetLatchInfo(latchInfo, param);
    latchInfo->isDrop = isDrop;
    if (SECUREC_UNLIKELY(stmt->session->nsp != NULL)) {
        ret = QryCheckNspStatus(stmt);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;  // 提交/回滚之后放锁
        }
    }
    return GMERR_OK;
}

#ifdef FEATURE_RESPOOL_SRV
inline static void SetAcqParamForResColPool(
    QryStmtT *stmt, uint32_t latchId, uint32_t latchVersionId, AcqLatchParam *param)
{
    DB_POINTER2(stmt, param);
    *param = (AcqLatchParam){{
                                 .labelRWLatch = NULL,
                                 .ccExeType = QryGetCCExeType(stmt),
                                 .ccType = CONCURRENCY_CONTROL_INVALID,  // 资源列的表latch不关心ccType，此处是非空设置
                             },
        .labelType = RESOURCE_POOL, .heapShmAddr = DB_INVALID_SHMPTR, .labelLatchShmAddr = DB_INVALID_SHMPTR,
        .labelId = DB_INVALID_UINT32,  // 资源列的表latch不关心labelId，此处是非空设置
        .labelLatchId = latchId, .labelLatchVersionId = latchVersionId};
}

Status QryAcqLatchForResColPool(QryStmtT *stmt, uint32_t labelLatchId, uint32_t labelLatchVersionId, bool isDrop)
{
    DB_POINTER(stmt);

    AcqLatchParam param;
    SetAcqParamForResColPool(stmt, labelLatchId, labelLatchVersionId, &param);

    bool isLock;
    QryLatchDefT *latchInfo = QryGetLatchInfo(stmt->session, param.labelLatchId, &isLock);
    if (SECUREC_UNLIKELY(latchInfo == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "get latch info for rescolpool");
        return GMERR_INTERNAL_ERROR;
    }

    if (isLock) {
        // 避免显示开启事务时，表latch加多次。资源池可能会重复加锁。
        latchInfo->isDrop = latchInfo->isDrop || isDrop;
        return GMERR_OK;
    }

    Status ret = SetAcqParamOfLatchInfo(&param, latchInfo, NULL);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        QryReleaseLatchInfo(stmt->session, latchInfo);
        return ret;
    }

    ret = QryAcqLatchForResColPoolInner(stmt, &param);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        QryReleaseLatchInfo(stmt->session, latchInfo);
        return ret;
    }

    QrySetLatchInfo(latchInfo, &param);
    latchInfo->isDrop = isDrop;
    if (SECUREC_UNLIKELY(stmt->session->nsp != NULL)) {
        ret = QryCheckNspStatus(stmt);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;  // 提交/回滚之后放锁
        }
    }

    return GMERR_OK;
}

Status QryAcqLatchFor2ResColPool(QryStmtT *stmt, DmResColPoolT *pool1, DmResColPoolT *pool2)
{
    Status ret = GMERR_OK;
    // 顺序加锁
    if (pool1->labelLatchId < pool2->labelLatchId) {
        ret = QryAcqLatchForResColPool(stmt, pool1->labelLatchId, pool1->labelLatchVersionId, false);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = QryAcqLatchForResColPool(stmt, pool2->labelLatchId, pool2->labelLatchVersionId, false);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        ret = QryAcqLatchForResColPool(stmt, pool2->labelLatchId, pool2->labelLatchVersionId, false);
        if (ret != GMERR_OK) {
            return ret;
        }

        ret = QryAcqLatchForResColPool(stmt, pool1->labelLatchId, pool1->labelLatchVersionId, false);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}
#endif

uint32_t g_gmdbPrivLatchId = {0};

Status InitGlobalPrivLatch(DbInstanceHdT dbInstance)
{
    uint32_t labelLatchVersionId;
    ShmemPtrT latchLatchShmAddr;
    Status ret = InitLabelLatch(&g_gmdbPrivLatchId, &labelLatchVersionId, &latchLatchShmAddr, dbInstance);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "init priv global latch.");
        return ret;
    }
    return GMERR_OK;
}

inline static void SetAcqParamForPriv(QryStmtT *stmt, uint32_t latchId, AcqLatchParam *param)
{
    DB_POINTER2(stmt, param);
    *param = (AcqLatchParam){{
                                 .labelRWLatch = NULL,
                                 .ccExeType = QryGetCCExeType(stmt),
                                 .ccType = CONCURRENCY_CONTROL_INVALID,
                             },
        .labelType = PRIVILEGE, .heapShmAddr = DB_INVALID_SHMPTR, .labelLatchShmAddr = DB_INVALID_SHMPTR,
        .labelId = DB_INVALID_UINT32, .labelLatchId = latchId, .labelLatchVersionId = DB_INVALID_UINT32};
}

Status QryAcqLatchForPrivInner(QryStmtT *stmt, AcqLatchParam *param)
{
    DB_POINTER2(stmt, param);
    LabelRWLatchT *labelRWLatch = param->baseParam.labelRWLatch;
    bool isGetLatch = false;
    while (!isGetLatch) {
        switch (param->baseParam.ccExeType) {
            case CC_EXECUTE_DDL:
                LabelWLatchAcquire(labelRWLatch);
                isGetLatch = true;
                break;
            case CC_EXECUTE_DML:
            case CC_EXECUTE_DQL:
            default:
                DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "inv ccExeType=%" PRIu32 " in acqLatchForPriv",
                    (uint32_t)param->baseParam.ccExeType);
                return GMERR_DATA_EXCEPTION;
        }
        if (!isGetLatch) {
            DbUsleep(TRY_LATCH_SLEEP_TIME_US);
        }
    }
    return GMERR_OK;
}

Status QryAcqLatchForPriv(QryStmtT *stmt)
{
    DB_POINTER(stmt);

    AcqLatchParam param;
    SetAcqParamForPriv(stmt, g_gmdbPrivLatchId, &param);

    bool isLock;
    QryLatchDefT *latchInfo = QryGetLatchInfo(stmt->session, param.labelLatchId, &isLock);
    if (SECUREC_UNLIKELY(latchInfo == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "get latch info for priv.");
        return GMERR_INTERNAL_ERROR;
    }
    if (isLock) {
        return GMERR_OK;
    }

    Status ret = SetAcqParamOfLatchInfo(&param, latchInfo, NULL);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        QryReleaseLatchInfo(stmt->session, latchInfo);
        return ret;
    }

    ret = QryAcqLatchForPrivInner(stmt, &param);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        QryReleaseLatchInfo(stmt->session, latchInfo);
        return ret;
    }

    QrySetLatchInfo(latchInfo, &param);
    return GMERR_OK;
}

void QryReleaseLatchForPriv(CCExecuteTypeE ccExeType, LabelRWLatchT *labelRWLatch)
{
    DB_POINTER(labelRWLatch);
    switch (ccExeType) {
        case CC_EXECUTE_DDL:
            LabelWLatchRelease(labelRWLatch);
            break;
        case CC_EXECUTE_DML:
        case CC_EXECUTE_DQL:
        default:
            DB_LOG_ERROR(
                GMERR_INVALID_PROPERTY, "inv ccExeType=%" PRIu32 " in releaseLatchForPriv", (uint32_t)ccExeType);
    }
}

#define RELEASE_PAGE_BG_TIMEOUT_FACTOR 3
void SetAcqParamTimeout(AcqLatchParam *param, SessionT *session, bool useFactor)
{
    uint32_t timeoutMs = (uint32_t)DbCfgGetInt32Lite(DB_CFG_TRX_LOCK_TIME_OUT, NULL);
    if (param->baseParam.ccType == CONCURRENCY_CONTROL_NORMAL) {
        param->timeoutMs = timeoutMs;
        param->startTime = DB_MAX_UINT64;
    } else if (param->baseParam.ccType == CONCURRENCY_CONTROL_READ_UNCOMMIT) {
        param->timeoutMs = session->cltTimeoutMs;
        param->startTime = QrySessionGetServerStartTime(session);
    } else {
        param->timeoutMs = DB_MAX_UINT32;
        param->startTime = DB_MAX_UINT64;
    }
    if (useFactor) {
        param->timeoutMs = timeoutMs / RELEASE_PAGE_BG_TIMEOUT_FACTOR;
    }
}

inline static void SetAcqParamForVertexLabel(QryStmtT *stmt, const DmVertexLabelT *vertexLabel, AcqLatchParam *param)
{
    DB_POINTER3(stmt, vertexLabel, param);
    *param = (AcqLatchParam){
        {
            .labelRWLatch = NULL,
            .ccExeType = QryGetCCExeTypeWithCCType(stmt, vertexLabel->commonInfo->heapInfo.ccType),
            .ccType = vertexLabel->commonInfo->heapInfo.ccType,
        },
        .labelType = VERTEX_LABEL,
        .heapShmAddr = vertexLabel->commonInfo->heapInfo.heapShmAddr,
        .labelLatchShmAddr = vertexLabel->commonInfo->labelLatchShmAddr,
        .labelId = vertexLabel->metaCommon.metaId,
        .labelLatchId = vertexLabel->commonInfo->vertexLabelLatchId,
        .labelLatchVersionId = vertexLabel->commonInfo->vertexLabelLatchVersionId,
        .timeoutMs = DB_MAX_UINT32,
        .startTime = DB_MAX_UINT64,
    };
}

Status QryAcqLatchForVertexLabel(QryStmtT *stmt, const DmVertexLabelT *vertexLabel, bool isDrop)
{
    DB_POINTER2(stmt, vertexLabel);
    Status ret;

    AcqLatchParam param;
    SetAcqParamForVertexLabel(stmt, vertexLabel, &param);
    SetAcqParamTimeout(&param, stmt->session, stmt->context->type == QRY_TYPE_RELEASE_PAGE_BG);

    if (SECUREC_UNLIKELY(param.baseParam.ccType == CONCURRENCY_CONTROL_NORMAL)) {
        LockStatT trxLockStat = {0};
        ret = QryAcqTrxLockForNormalMode(stmt, &param, &trxLockStat);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }

        ret = QryAcqLatchForLabel(stmt, &param, isDrop);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }

        if (!DtlVertexLabelIsNoHeap(vertexLabel)) {
            // 事务锁DFX信息，刷新到heap上，此时已加上表latch，heap一定有效
            HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = param.heapShmAddr,
                .isPersistent = vertexLabel->metaCommon.isPersistent,
                .isUseRsm = vertexLabel->metaCommon.isUseRsm,
                .instanceId = DbGetInstanceId(DbGetInstanceByMemCtx(stmt->session->memCtx))};
            HeapUpdateLockStat(&heapCntrAcsInfo, &trxLockStat);
        }
        return GMERR_OK;
    }

    return QryAcqLatchForLabel(stmt, &param, isDrop);
}

#ifdef FEATURE_KV
inline static void SetAcqParamForKvLabel(QryStmtT *stmt, DmKvLabelT *kvTable, AcqLatchParam *param)
{
    DB_POINTER3(stmt, kvTable, param);
    *param = (AcqLatchParam){
        {
            .labelRWLatch = NULL,
            .ccExeType = QryGetCCExeTypeWithCCType(stmt, kvTable->ccType),
            .ccType = kvTable->ccType,
        },
        .labelType = KV_TABLE,
        .heapShmAddr = kvTable->heapShmAddr,
        .labelLatchShmAddr = kvTable->labelLatchShmAddr,
        .labelId = kvTable->metaCommon.metaId,
        .labelLatchId = kvTable->labelLatchId,
        .labelLatchVersionId = kvTable->labelLatchVersionId,
    };
}

Status QryAcqLatchForKvLabel(QryStmtT *stmt, DmKvLabelT *kvTable, bool isDrop)
{
    DB_POINTER2(stmt, kvTable);
    Status ret;

    AcqLatchParam param;
    SetAcqParamForKvLabel(stmt, kvTable, &param);
    SetAcqParamTimeout(&param, stmt->session, stmt->context->type == QRY_TYPE_RELEASE_PAGE_BG);
    if (param.baseParam.ccType == CONCURRENCY_CONTROL_NORMAL) {
        LockStatT trxLockStat = {0};
        ret = QryAcqTrxLockForNormalMode(stmt, &param, &trxLockStat);
        if (ret != GMERR_OK) {
            return ret;
        }

        // 事务锁DFX信息，刷新到heap上，此时已加上表latch，heap一定有效
        HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = param.heapShmAddr,
            .isPersistent = kvTable->metaCommon.isPersistent,
            .isUseRsm = kvTable->metaCommon.isUseRsm,
            .instanceId = DbGetProcGlobalId()};
        HeapUpdateLockStat(&heapCntrAcsInfo, &trxLockStat);
    }

    return QryAcqLatchForLabel(stmt, &param, isDrop);
}
#endif

inline static void SetAcqParamForEdgeLabel(QryStmtT *stmt, DmEdgeLabelT *edgeLabel, AcqLatchParam *param)
{
    DB_POINTER3(stmt, edgeLabel, param);
    *param = (AcqLatchParam){{
                                 .labelRWLatch = NULL,
                                 .ccExeType = QryGetCCExeTypeWithCCType(stmt, edgeLabel->ccType),
                                 .ccType = edgeLabel->ccType,
                             },
        .labelType = EDGE_LABEL, .heapShmAddr = DB_INVALID_SHMPTR, .labelLatchShmAddr = edgeLabel->labelLatchShmAddr,
        .labelId = edgeLabel->metaCommon.metaId, .labelLatchId = edgeLabel->edgeLabelLatchId,
        .labelLatchVersionId = edgeLabel->edgeLabelLatchVersionId};
}

inline Status QryAcqLatchForEdgeLabel(QryStmtT *stmt, DmEdgeLabelT *edgeLabel, bool isDrop)
{
    DB_POINTER2(stmt, edgeLabel);
    Status ret;

    AcqLatchParam param;
    SetAcqParamForEdgeLabel(stmt, edgeLabel, &param);
    SetAcqParamTimeout(&param, stmt->session, stmt->context->type == QRY_TYPE_RELEASE_PAGE_BG);

    if (param.baseParam.ccType == CONCURRENCY_CONTROL_NORMAL) {
        LockStatT trxLockStat = {0};
        ret = QryAcqTrxLockForNormalMode(stmt, &param, &trxLockStat);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return QryAcqLatchForLabel(stmt, &param, isDrop);
}

void QryReleaseLatch(QryLatchDefT *latchDef)
{
    if (SECUREC_UNLIKELY(latchDef->type == RESOURCE_POOL)) {
#ifdef FEATURE_RESPOOL_SRV
        QryReleaseLatchForResColPool(latchDef->ccExeType, (LabelRWLatchT *)latchDef->latch);
#endif
    } else if (SECUREC_UNLIKELY(latchDef->type == PRIVILEGE)) {
        QryReleaseLatchForPriv(latchDef->ccExeType, (LabelRWLatchT *)latchDef->latch);
    } else {
        QryReleaseLabelLatch(latchDef->ccExeType, latchDef->ccType, (LabelRWLatchT *)latchDef->latch);
    }
}

void QryReleaseAllLabelLatch(SessionT *session)
{
    DB_POINTER(session);

    GaListT *labelLatch = &session->trxStatistics.labelLatch;
    while (DbGaListGetCount(labelLatch) != 0) {
        QryLatchDefT *latchDef = (QryLatchDefT *)DbGaListGet(labelLatch, DbGaListGetCount(labelLatch) - 1);
        if (latchDef != NULL) {
            QryReleaseLatch(latchDef);
        }
        DbGaListDeleteTail(labelLatch);
    }
    QryTrxStatisticT *trxStatistics = &session->trxStatistics;
    int32_t usedNum = (int32_t)trxStatistics->usedNum;
    for (int32_t i = usedNum - 1; i >= 0; i--) {
        QryLatchDefT *latchDef = &trxStatistics->labelLatchArr[i];
        QryReleaseLatch(latchDef);
        trxStatistics->usedNum--;
    }
}

void QryReleaseLastLabelLatch(SessionT *session)
{
    DB_POINTER(session);
    GaListT *labelLatch = &session->trxStatistics.labelLatch;
    if (DbGaListGetCount(labelLatch) != 0) {
        QryLatchDefT *latchDef = (QryLatchDefT *)DbGaListGet(labelLatch, DbGaListGetCount(labelLatch) - 1);
        if (latchDef != NULL) {
            QryReleaseLatch(latchDef);
        }
        DbGaListDeleteTail(labelLatch);
    } else {
        QryTrxStatisticT *trxStatistics = &session->trxStatistics;
        int32_t usedNum = (int32_t)trxStatistics->usedNum;
        DB_ASSERT(usedNum > 0);
        QryLatchDefT *latchDef = &trxStatistics->labelLatchArr[usedNum - 1];
        QryReleaseLatch(latchDef);
        trxStatistics->usedNum--;
    }
}

void QryReleaseAndUpgradeAllLabelLatch(SessionT *session)
{
    DB_POINTER(session);

    GaListT *labelLatch = &session->trxStatistics.labelLatch;
    while (DbGaListGetCount(labelLatch) != 0) {
        // 表锁不能为NULL，表锁和表强绑定的
        QryLatchDefT *latchDef = (QryLatchDefT *)DbGaListGet(labelLatch, DbGaListGetCount(labelLatch) - 1);
        if (latchDef->isDrop) {
            QryRemoveLabelLatchAfterDrop(latchDef->type, latchDef->latchId, latchDef->labelLatchVersionId,
                latchDef->ccType, DbGetInstanceByMemCtx(session->memCtx));
        } else {
            QryReleaseLatch(latchDef);
        }
        DbGaListDeleteTail(labelLatch);
    }
    QryTrxStatisticT *trxStatistics = &session->trxStatistics;
    int32_t usedNum = (int32_t)trxStatistics->usedNum;
    for (int32_t i = usedNum - 1; i >= 0; i--) {
        QryLatchDefT *latchDef = &trxStatistics->labelLatchArr[i];
        if (latchDef->isDrop) {
            QryRemoveLabelLatchAfterDrop(latchDef->type, latchDef->latchId, latchDef->labelLatchVersionId,
                latchDef->ccType, DbGetInstanceByMemCtx(session->memCtx));
        } else {
            QryReleaseLatch(latchDef);
        }
        trxStatistics->usedNum--;
    }
}

void QryReleaseAndUpgradeLastLabelLatch(SessionT *session)
{
    DB_POINTER(session);
    GaListT *labelLatch = &session->trxStatistics.labelLatch;
    if (DbGaListGetCount(labelLatch) != 0) {
        // 表锁不能为NULL，表锁和表强绑定的
        QryLatchDefT *latchDef = (QryLatchDefT *)DbGaListGet(labelLatch, DbGaListGetCount(labelLatch) - 1);
        if (latchDef->isDrop) {
            QryRemoveLabelLatchAfterDrop(latchDef->type, latchDef->latchId, latchDef->labelLatchVersionId,
                latchDef->ccType, DbGetInstanceByMemCtx(session->memCtx));
        } else {
            QryReleaseLatch(latchDef);
        }
        DbGaListDeleteTail(labelLatch);
    } else {
        QryTrxStatisticT *trxStatistics = &session->trxStatistics;
        int32_t usedNum = (int32_t)trxStatistics->usedNum;
        DB_ASSERT(usedNum > 0);
        QryLatchDefT *latchDef = &trxStatistics->labelLatchArr[usedNum - 1];
        if (latchDef->isDrop) {
            QryRemoveLabelLatchAfterDrop(latchDef->type, latchDef->latchId, latchDef->labelLatchVersionId,
                latchDef->ccType, DbGetInstanceByMemCtx(session->memCtx));
        } else {
            QryReleaseLatch(latchDef);
        }
        trxStatistics->usedNum--;
    }
}
static Status CreateTrxLabelLock(DbMemCtxT *memCtx, TrxLabelLock **labelLock)
{
    // memory will be freed unitied , see Note1
    TrxLabelLock *labelLockTmp = (TrxLabelLock *)DbDynMemCtxAlloc(memCtx, sizeof(TrxLabelLock));
    if (labelLockTmp == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "labelLockTmp is null");
        return GMERR_OUT_OF_MEMORY;
    }
    *labelLockTmp = (TrxLabelLock){0};

    *labelLock = labelLockTmp;
    return GMERR_OK;
}

#ifdef FEATURE_KV
static void InitTrxKvLock(TrxLabelLock *labelLock, DmKvLabelT *baseLabel, SeLockModeE lockMode)
{
    labelLock->labelId = baseLabel->metaCommon.metaId;
    labelLock->lockMode = lockMode;
    labelLock->isFakeLabel = false;
    labelLock->isNoHeapLabel = false;
    labelLock->labelInfo = baseLabel;
    labelLock->heapShmAddr = baseLabel->heapShmAddr;
}
#endif

static void InitTrxLabelLock(TrxLabelLock *labelLock, DmVertexLabelT *vertexLabel, SeLockModeE lockMode)
{
    labelLock->labelId = vertexLabel->metaCommon.metaId;
    labelLock->lockMode = lockMode;
    labelLock->isFakeLabel = DtlVertexLabelIsFake(vertexLabel);
    labelLock->isNoHeapLabel = DtlVertexLabelIsNoHeap(vertexLabel);
    labelLock->labelInfo = vertexLabel;
    labelLock->heapShmAddr = vertexLabel->commonInfo->heapInfo.heapShmAddr;
}

static Status GetLabelLatch(const char *lableName, QryTrxStatisticT *trxStatistics, QryLatchDefT **newLatchDef)
{
    Status ret = GMERR_OK;
    if (trxStatistics->usedNum < LABEL_LATCH_INFO_ARRAY_SIZE) {
        *newLatchDef = &trxStatistics->labelLatchArr[trxStatistics->usedNum];
        trxStatistics->usedNum++;
    } else {
        GaListT *labelLatch = &trxStatistics->labelLatch;
        ret = DbGaListNew(labelLatch, sizeof(QryLatchDefT), (void **)newLatchDef);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "get lb latch when save lb latch. lb Name: %s", lableName);
            return ret;
        }
    }
    return ret;
}

static Status SaveLabelLatch(
    LabelRWLatchT *latch, DmVertexLabelT *vertexLabel, QryTrxStatisticT *trxStatistics, LabelLatchTypeE latchType)
{
    Status ret = GMERR_OK;
    QryLatchDefT *newLatchDef = NULL;
    ret = GetLabelLatch(vertexLabel->metaCommon.metaName, trxStatistics, &newLatchDef);
    if (ret != GMERR_OK) {
        return ret;
    }
    *newLatchDef = (QryLatchDefT){.latchId = vertexLabel->commonInfo->vertexLabelLatchId,
        .labelLatchVersionId = vertexLabel->commonInfo->vertexLabelLatchVersionId,
        .type = VERTEX_LABEL,
        .ccType = vertexLabel->commonInfo->heapInfo.ccType,
        .ccExeType = CC_EXECUTE_DML,
        .labelLatchType = latchType,
        .latch = (void *)latch};

    return GMERR_OK;
}

Status LatchLockVertexLabel(DmVertexLabelT *vertexLabel, QryTrxStatisticT *trxStatistics, LabelLatchTypeE latchType)
{
    DB_POINTER2(trxStatistics, vertexLabel);
    Status ret = GMERR_OK;

    LabelRWLatchT *latch = (LabelRWLatchT *)GetLabelRWLatchPtrById(
        vertexLabel->commonInfo->vertexLabelLatchId, DbGetInstanceByMemCtx(vertexLabel->memCtx));
    if (latch == NULL) {
        DB_LOG_AND_SET_LASERR(
            (ret = GMERR_DATA_EXCEPTION), "get lb Latch, lb name=%s", vertexLabel->metaCommon.metaName);
        return ret;
    }
    const uint32_t timeoutMs = (uint32_t)DbCfgGetInt32Lite(DB_CFG_TRX_LOCK_TIME_OUT, NULL);
    bool isTimeout = false;
    if (latchType == LABEL_LATCH_READ) {
        isTimeout = !LabelRLatchTimedAcquire(latch, timeoutMs * (uint32_t)USECONDS_IN_MSECOND);
    } else if (latchType == LABEL_LATCH_WRITE) {
        // WLatch need check if acq latch timeout
        isTimeout = !LabelWLatchTimedAcquire(latch, timeoutMs * (uint32_t)USECONDS_IN_MSECOND);
    } else {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "inv latchType(%" PRIu32 ") when acqLatch, lb name=%s", latchType,
            vertexLabel->metaCommon.metaName);
        return GMERR_DATA_EXCEPTION;
    }
    if (isTimeout) {
        ret = GMERR_LOCK_NOT_AVAILABLE;
        DB_LOG_AND_SET_LASERR(ret, "busy in acq label latch, label Name=%s", vertexLabel->metaCommon.metaName);
        return ret;
    }

    ret = LabelLatchCheckVersion(latch, vertexLabel->commonInfo->vertexLabelLatchVersionId);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "lb have been dropped, latchType=%" PRIu32 ", lb=%s.", (uint32_t)latchType,
            vertexLabel->metaCommon.metaName);
        goto ERROR;
    }

    if ((ret = SaveLabelLatch(latch, vertexLabel, trxStatistics, latchType)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "save labelLatch in session, latchType=%" PRIu32 ", lb=%s.", (uint32_t)latchType,
            vertexLabel->metaCommon.metaName);
        goto ERROR;
    }
    return GMERR_OK;

ERROR:
    LabelLatchRelease(latch, latchType);
    return ret;
}

Status TrxLockVertexLabel(DmVertexLabelT *vertexLabel, DbMemCtxT *memCtx, SeRunCtxHdT seRunCtx, SeLockModeE lockMode)
{
    TrxLabelLock *labelLock = NULL;
    Status ret = CreateTrxLabelLock(memCtx, &labelLock);
    if (ret != GMERR_OK) {
        return ret;
    }
    InitTrxLabelLock(labelLock, vertexLabel, lockMode);

    // trx lock will be freed when commit or rollback
    LockStatT trxLockStat = {0};  // DFX statistics of transaction locks
    ret = SeLabelLockAcquire(seRunCtx, DbGetInstanceId(DbGetInstanceByMemCtx(memCtx)), labelLock, &trxLockStat);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "lock org table, lb Id: %" PRIu32 "lb name=%s.", labelLock->labelId, vertexLabel->metaCommon.metaName);
        return ret;
    }

    ShmemPtrT heapAddr = vertexLabel->commonInfo->heapInfo.heapShmAddr;
    if (DbIsShmPtrValid(heapAddr)) {
        HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = heapAddr,
            .isPersistent = vertexLabel->metaCommon.isPersistent,
            .isUseRsm = vertexLabel->metaCommon.isUseRsm,
            .instanceId = DbGetInstanceId(DbGetInstanceByMemCtx(memCtx))};
        HeapUpdateLockStat(&heapCntrAcsInfo, &trxLockStat);
    }

    return ret;
}

#ifdef FEATURE_KV
static Status SaveKvLabelLatch(LabelRWLatchT *latch, DmKvLabelT *kvLabel, QryTrxStatisticT *trxStatistics)
{
    Status ret = GMERR_OK;
    QryLatchDefT *newLatchDef = NULL;
    ret = GetLabelLatch(kvLabel->metaCommon.metaName, trxStatistics, &newLatchDef);
    if (ret != GMERR_OK) {
        return ret;
    }
    *newLatchDef = (QryLatchDefT){.latchId = kvLabel->labelLatchId,
        .labelLatchVersionId = kvLabel->labelLatchVersionId,
        .type = KV_TABLE,
        .ccType = kvLabel->ccType,
        .ccExeType = CC_EXECUTE_DML,
        .labelLatchType = LABEL_LATCH_READ,
        .latch = (void *)latch};
    return GMERR_OK;
}

Status RLatchLockKvLabel(DmKvLabelT *label, SessionT *session)
{
    DB_POINTER2(session, label);
    Status ret = GMERR_OK;
    LabelRWLatchT *latch = (LabelRWLatchT *)GetLabelRWLatchPtrById(label->labelLatchId, NULL);
    if (latch == NULL) {
        DB_LOG_AND_SET_LASERR((ret = GMERR_DATA_EXCEPTION), "get kv labelLatch, Name: %s", label->metaCommon.metaName);
        return ret;
    }

    LabelRLatchAcquire(latch);
    ret = LabelLatchCheckVersion(latch, label->labelLatchVersionId);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "KV lable(%s) is dropped.", label->metaCommon.metaName);
        goto ERROR;
    }

    ret = SaveKvLabelLatch(latch, label, &session->trxStatistics);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "save kv label RLatch in session, lb=%s.", label->metaCommon.metaName);
        goto ERROR;
    }
    return GMERR_OK;

ERROR:
    LabelRLatchRelease(latch);
    return ret;
}

Status TrxLockKvLabel(DbMemCtxT *memCtx, SeRunCtxHdT seRunCtx, DmKvLabelT *baseLabel, SeLockModeE lockMode)
{
    TrxLabelLock *labelLock = NULL;
    Status ret = CreateTrxLabelLock(memCtx, &labelLock);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (baseLabel->ccType != CONCURRENCY_CONTROL_NORMAL) {
        ret = GMERR_FEATURE_NOT_SUPPORTED;
        DB_LOG_AND_SET_LASERR(
            ret, "datalog don't support Fast read uncommitted, kv lb: %s.", baseLabel->metaCommon.metaName);
        return ret;
    }

    InitTrxKvLock(labelLock, baseLabel, lockMode);

    // trx lock will be freed when commit or rollback
    LockStatT trxLockStat = {0};  // DFX statistics of transaction locks
    ret = SeLabelLockAcquire(seRunCtx, DbGetProcGlobalId(), labelLock, &trxLockStat);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "lock org table, lb Id: %" PRIu32 ", kv lb: %s.", labelLock->labelId, baseLabel->metaCommon.metaName);
        return ret;
    }

    ShmemPtrT heapAddr = baseLabel->heapShmAddr;
    if (DbIsShmPtrValid(heapAddr)) {
        HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = heapAddr,
            .isPersistent = baseLabel->metaCommon.isPersistent,
            .isUseRsm = baseLabel->metaCommon.isUseRsm,
            .instanceId = DbGetProcGlobalId()};
        HeapUpdateLockStat(&heapCntrAcsInfo, &trxLockStat);
    }
    return ret;
}
#endif

// 融合引擎加锁
Status QryAcqLatchForFusionLabel(SessionT *session, AcqLatchParam *param)
{
    QryLatchDefT *latchInfo = NULL;
    QryTrxStatisticT *trxStatistics = &session->trxStatistics;
    if (SECUREC_LIKELY(trxStatistics->usedNum == 0)) {
        latchInfo = &trxStatistics->labelLatchArr[0];
        trxStatistics->usedNum++;
    } else {
        bool isLock;
        latchInfo = QryGetLatchInfo(session, param->labelLatchId, &isLock);
        if (SECUREC_UNLIKELY(latchInfo == NULL)) {
            DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "get lb latch info");
            return GMERR_INTERNAL_ERROR;
        }

        if (isLock) {
            // 避免显式事务中表latch加多次
            return GMERR_OK;
        }
    }

    Status ret = SetAcqParamOfLatchInfo(param, latchInfo, DbGetInstanceByMemCtx(session->memCtx));
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        QryReleaseLatchInfo(session, latchInfo);
        return ret;
    }

    ret = QryAcqLatchForLabelInner(param);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        QryReleaseLatchInfo(session, latchInfo);
        return ret;
    }

    QrySetLatchInfo(latchInfo, param);
    return GMERR_OK;
}

static void SetAcqParamForFusionVertexLabel(
    const DmVertexLabelT *vertexLabel, AcqLatchParam *param, CCExecuteTypeE executeType)
{
    *param = (AcqLatchParam){
        {
            .labelRWLatch = NULL,
            .ccExeType = executeType,
            .ccType = vertexLabel->commonInfo->heapInfo.ccType,
        },
        .labelType = VERTEX_LABEL,
        .heapShmAddr = vertexLabel->commonInfo->heapInfo.heapShmAddr,
        .labelLatchShmAddr = vertexLabel->commonInfo->labelLatchShmAddr,
        .labelId = vertexLabel->metaCommon.metaId,
        .labelLatchId = vertexLabel->commonInfo->vertexLabelLatchId,
        .labelLatchVersionId = vertexLabel->commonInfo->vertexLabelLatchVersionId,
        .timeoutMs = DB_MAX_UINT32,
        .startTime = DB_MAX_UINT64,
    };
}

Status QryAcqLatchForFusionVertexLabel(SessionT *session, const DmVertexLabelT *vertexLabel, CCExecuteTypeE executeType)
{
    DB_POINTER2(session, vertexLabel);
    AcqLatchParam param;
    SetAcqParamForFusionVertexLabel(vertexLabel, &param, executeType);
    SetAcqParamTimeout(&param, session, false);
    return QryAcqLatchForFusionLabel(session, &param);
}

void ReleaseAllDtlLabelLatch(QryTrxStatisticT *trxStatistics)
{
    QryLatchDefT *latchDef;
    GaListT *labelLatch = &trxStatistics->labelLatch;
    while (DbGaListGetCount(labelLatch) != 0) {
        latchDef = (QryLatchDefT *)DbGaListGet(labelLatch, DbGaListGetCount(labelLatch) - 1);
        if (latchDef != NULL) {
            LabelLatchRelease((LabelRWLatchT *)latchDef->latch, latchDef->labelLatchType);
        }
        DbGaListDeleteTail(labelLatch);
    }

    for (int32_t i = (int32_t)trxStatistics->usedNum - 1; i >= 0; i--) {
        latchDef = &trxStatistics->labelLatchArr[i];
        LabelLatchRelease((LabelRWLatchT *)latchDef->latch, latchDef->labelLatchType);
    }
    trxStatistics->usedNum = 0;
}

// 不是所有的后台任务都需要复制，所以需要一个选项控制
Status QryBgTaskTxBeginForVertex(QryStmtT *stmt, DmVertexLabelT *vertexLabel, bool replication)
{
    Status ret = GMERR_OK;
    DmHeapInfoT *heapInfo = &vertexLabel->commonInfo->heapInfo;
    TrxCfgT cfg = {
        .readOnly = false,  // 不确定影响保险起见先用false
        .isLiteTrx = QryVertexLabelIsLiteTrx(vertexLabel),
        .isBackGround = false,  // 只有purge设置
        .isInteractive = false,
        .isTrxForceCommit = false,
        .isRetryTrx = false,
        .connId = DB_INVALID_UINT16,
        .trxType = heapInfo->trxType,
        .isolationLevel = heapInfo->isolationLevel,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
    };

    ret = SeTransBegin(stmt->session->seInstance, &cfg);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "begin transaction.");
        return ret;
    }

    // 轻量化事务，设置允许undo日志扩展
    SeTransSetDmlHint4RangeUpdate(stmt->session->seInstance);

    ret = QryAcqLatchForVertexLabel(stmt, vertexLabel, false);
    if (ret != GMERR_OK) {  // 是可能失败的不打印日志
        goto ERROR;
    }

    ret = QrySetRsmUndoForVertexLabel(stmt, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set trx lite undo rec, vl=%s.", vertexLabel->metaCommon.metaName);
        goto ERROR;
    }

#ifdef FEATURE_REPLICATION
    ret = QryReplicateTxStart(stmt->session->logBuf, SeTransGetTrxId(stmt->session->seInstance), replication);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "tx start in replication.");
        goto ERROR;
    }
#endif
    return GMERR_OK;

ERROR:  // 需要先回滚再释放锁
    (void)SeTransRollback(stmt->session->seInstance, false);
    QryReleaseAllLabelLatch(stmt->session);
    return ret;
}

#ifdef FEATURE_KV
// 不是所有的后台任务都需要复制，所以需要一个选项控制
Status QryBgTaskTxBeginForKV(QryStmtT *stmt, DmKvLabelT *kvLabel, bool replication)
{
    Status ret = GMERR_OK;
    TrxCfgT cfg = {
        .readOnly = false,  // 不确定影响保险起见先用false
        .isLiteTrx = QryKvTableIsLiteTrx(kvLabel),
        .isBackGround = false,  // 只有purge设置
        .isInteractive = false,
        .isTrxForceCommit = false,
        .isRetryTrx = false,
        .connId = DB_INVALID_UINT16,
        .trxType = kvLabel->trxType,
        .isolationLevel = kvLabel->isolationLevel,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
    };

    ret = SeTransBegin(stmt->session->seInstance, &cfg);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "begin trx.");
        return ret;
    }

    // 轻量化事务，设置允许undo日志扩展
    SeTransSetDmlHint4RangeUpdate(stmt->session->seInstance);

    ret = QryAcqLatchForKvLabel(stmt, kvLabel, false);
    if (ret != GMERR_OK) {  // 是可能失败的不打印日志
        goto ERROR;
    }

    ret = QrySetRsmUndoForKvLabel(stmt, kvLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set trx lite undo rec, kvLabel=%s.", kvLabel->metaCommon.metaName);
        goto ERROR;
    }

#ifdef FEATURE_REPLICATION
    ret = QryReplicateTxStart(stmt->session->logBuf, SeTransGetTrxId(stmt->session->seInstance), replication);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "tx start in replication.");
        goto ERROR;
    }
#endif

    return GMERR_OK;

ERROR:  // 需要先回滚再释放锁
    (void)SeTransRollback(stmt->session->seInstance, false);
    QryReleaseAllLabelLatch(stmt->session);
    return ret;
}
#endif

Status QryBgTaskTxEnd(QryStmtT *stmt, Status status)
{
    Status ret = status;
#ifdef FEATURE_REPLICATION
    if (ret == GMERR_OK || ret == GMERR_NO_DATA) {
        ret = QryReplicateTxCommit(stmt->session->logBuf);
    }
#endif
    if (ret == GMERR_OK || ret == GMERR_NO_DATA) {
        ret = SeTransCommit(stmt->session->seInstance);
    }
    if (ret != GMERR_OK) {
#ifdef FEATURE_REPLICATION
        (void)QryReplicateTxRollback(stmt->session->logBuf);
#endif
        Status ret1 = SeTransRollback(stmt->session->seInstance, false);
        DB_ASSERT(ret1 == GMERR_OK);  // 回滚失败处理不了
    }
    QryReleaseAllLabelLatch(stmt->session);
    return ret;
}
#ifdef __cplusplus
}
#endif
