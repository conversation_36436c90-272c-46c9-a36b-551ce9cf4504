/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: define the system table module basic function in here.
 * Author: linchunbo
 * Create: 2023-4-19
 */

#include "ee_systbl_basic_in.h"
#include "db_sysapp_context.h"
#include "ee_systbl_nsp_label_in.h"
#include "ee_systbl_index_in.h"
#include "ee_systbl_schema_in.h"
#include "ee_systbl_prop_label_in.h"
#include "ee_systbl_edge_label_in.h"
#include "ee_systbl_yang_in.h"
#include "ee_systbl_user_in.h"
#include "ee_systbl_role_in.h"
#ifdef FEATURE_SQL
#include "ee_systbl_util_sql_in.h"
#endif
#ifdef FEATURE_TS
#include "ee_systbl_ts_map_in.h"
#endif
#include "ee_systbl_list_util_in.h"
#include "ee_systbl_inner.h"
#include "ee_init.h"
#include "ee_persist.h"
#include "cpl_base_pub.h"
#include "ee_log.h"  // 放在最后一行

#ifdef __cplusplus
extern "C" {
#endif

#ifdef FEATURE_EDGELABEL
#define SYS_TABLE_JSON_EL_ITEM "," STB_EL_JSON
#else
#define SYS_TABLE_JSON_EL_ITEM ""
#endif

#ifdef FEATURE_YANG
#define SYS_TABLE_JSON_YANG_ITEM "," STB_YANG_JSON
#else
#define SYS_TABLE_JSON_YANG_ITEM ""
#endif

#ifdef FEATURE_TS
#define SYS_TABLE_JSON_TS_ITEM "," STB_TS_MAP_JSON
#else
#define SYS_TABLE_JSON_TS_ITEM ""
#endif

#ifdef FEATURE_SQL
#define SYS_TABLE_JSON_SQL_ITEM "," STB_SQL_MASTER_JSON
#else
#define SYS_TABLE_JSON_SQL_ITEM ""
#endif

#if defined(FEATURE_STREAM) || defined(FEATURE_TS)
#define SYS_TABLE_JSON_NODE_ITEM ""
#define SYS_TABLE_JSON_NSP_ITEM ""
#else
#define SYS_TABLE_JSON_NODE_ITEM "," STB_NODE_JSON
#define SYS_TABLE_JSON_NSP_ITEM "," STB_NSP_JSON
#endif
#define SYS_TABLE_JSON_IDX_ITEM "," STB_IDX_JSON

#define SYS_TABLE_JSON                                                                       \
    "[" STB_VL_JSON SYS_TABLE_JSON_EL_ITEM SYS_TABLE_JSON_YANG_ITEM SYS_TABLE_JSON_NODE_ITEM \
    "," STB_PROP_JSON SYS_TABLE_JSON_IDX_ITEM SYS_TABLE_JSON_NSP_ITEM "," STB_USER_JSON      \
    "," STB_ROLE_JSON SYS_TABLE_JSON_TS_ITEM SYS_TABLE_JSON_SQL_ITEM "]"

// 暂时假设最后几个uuid不会被使用到
const uint32_t g_systableIds[STB_CNT] = {DB_MAX_UINT32 - 10,
#ifdef FEATURE_EDGELABEL
    DB_MAX_UINT32 - 9,
#endif
#ifdef FEATURE_YANG
    DB_MAX_UINT32 - 8,
#endif
#if !defined(FEATURE_STREAM) && !defined(FEATURE_TS)
    DB_MAX_UINT32 - 7,
#endif
#ifdef FEATURE_SQL
    DB_MAX_UINT32 - 6,
#endif
#ifdef FEATURE_TS
    DB_MAX_UINT32 - 5,
#endif
    DB_MAX_UINT32 - 4,
#if !defined(FEATURE_STREAM) && !defined(FEATURE_TS)
    DB_MAX_UINT32 - 3,
#endif
    DB_MAX_UINT32 - 2, DB_MAX_UINT32 - 1, DB_MAX_UINT32};
// 系统表主键索引id
const uint32_t g_systablePkIndexId[STB_CNT] = {DB_MAX_UINT32 - 21,
#ifdef FEATURE_EDGELABEL
    DB_MAX_UINT32 - 20,
#endif
#ifdef FEATURE_YANG
    DB_MAX_UINT32 - 19,
#endif
#if !defined(FEATURE_STREAM) && !defined(FEATURE_TS)
    DB_MAX_UINT32 - 18,
#endif
    DB_MAX_UINT32 - 17, DB_MAX_UINT32 - 16,
#if !defined(FEATURE_STREAM) && !defined(FEATURE_TS)
#ifdef FEATURE_SQL
    DB_MAX_UINT32 - 15,
#endif
#ifdef FEATURE_TS
    DB_MAX_UINT32 - 14,
#endif
#endif
    DB_MAX_UINT32 - 13, DB_MAX_UINT32 - 12, DB_MAX_UINT32 - 11};
// 系统表二级索引id，当前每张系统表对应的DmVertexLabel，最多只有一个二级索引
const uint32_t g_systableSecIndexId[STB_CNT] = {DB_MAX_UINT32 - 32,
#ifdef FEATURE_EDGELABEL
    DB_MAX_UINT32 - 31,
#endif
#ifdef FEATURE_YANG
    DB_MAX_UINT32 - 30,
#endif
#if !defined(FEATURE_STREAM) && !defined(FEATURE_TS)
    DB_MAX_UINT32 - 29, DB_MAX_UINT32 - 28, DB_MAX_UINT32 - 27,
#endif
    DB_MAX_UINT32 - 26, DB_MAX_UINT32 - 25,
#if !defined(FEATURE_STREAM) && !defined(FEATURE_TS)
#ifdef FEATURE_SQL
    DB_MAX_UINT32 - 24,
#endif
#ifdef FEATURE_TS
    DB_MAX_UINT32 - 23,
#endif
#endif
    DB_MAX_UINT32 - 22};

SysTableGlobalT g_systable = {0};

#ifdef FEATURE_SQL
SysTableGlobalT *DmGetSysTable(DbInstanceHdT dbInstance)
{
    if (!DbIsMultiInstanceEnabled()) {
        return &g_systable;
    }
    if (dbInstance == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "dbInstance for get sysTable: null.");
        DB_ASSERT(false);
        return NULL;
    }
    DbInstanceT *dbIns = (DbInstanceT *)dbInstance;
    return (SysTableGlobalT *)dbIns->embSysTable;
}
#else
SysTableGlobalT *DmGetSysTable(DbInstanceHdT dbInstance)
{
    DB_UNUSED(dbInstance);
    return &g_systable;
}
#endif

Status SysTableStartImpl(DbInstanceHdT dbInstance)
{
    SessionT *session = NULL;
    QryStmtT *sysTableInitStmt = NULL;
    Status ret = QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, dbInstance, &session);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "import vertexLabels for DbStart.");
        return ret;
    }

    // 当前只有启动和swap阶段会调用SysTableStart
    SeMarkSwapCtx(session->seInstance);

#ifdef FEATURE_REPLICATION
    ret = FixBufCreate(session->rsp, session->memCtx, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "init fix buffer for DbStart.");
        goto EXIT;
    }
#endif

    ret = ExecutorInitStmt(session, &sysTableInitStmt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "init systable stmt for DbStart.");
        goto EXIT;
    }
    session->currentStmt = sysTableInitStmt;
    ret = SysTableInitCompilerHelper(sysTableInitStmt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "execute compiler systable helper.");
        goto EXIT;
    }

    ret = SysTableInitExecutorHelper(sysTableInitStmt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "execute executor systable helper.");
        goto EXIT;
    }

#ifdef FEATURE_TS
    if (DbCfgIsTsInstance()) {
        ret = SystableInitPhysicalLabels(sysTableInitStmt);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "init physical labels.");
            goto EXIT;
        }
    }
#endif
EXIT:
    QrySessionRelease(session);
    (void)DbAdptMallocTrim(0);
    return ret;
}

char *SysTableGetStbJsonImpl(void)
{
    return SYS_TABLE_JSON;
}

void SysTableInitStbVlImpl(StbTypeE sysTable, DmVertexLabelT *sysTableVl, DbInstanceHdT dbInstance)
{
    DB_POINTER(sysTableVl);
    SysTableGlobalT *sysTableGlobal = DmGetSysTable(dbInstance);
    sysTableGlobal->stbs[sysTable] = sysTableVl;
}

static void SysTableSQLAmInit(SysTableAmFuncT *sysTableAm)
{
#ifdef FEATURE_SQL
    sysTableAm->insertSqlRebuildLabel = SysTableInsertSqlRebuildLabelImpl;
    sysTableAm->deleteSqlRebuildLabel = SysTableDeleteSqlRebuildLabelImpl;
    sysTableAm->getSqlVertexLabel = SysTableGetSqlVertexLabelByNameImpl;
    sysTableAm->createRebuildSqlLabelCtx = SysTableCreateSqlRebuildLabelCtxImpl;
    sysTableAm->destroyRebuildSqlLabelCtx = SysTableDestroySqlRebuildLabelCtxImpl;
#endif
}

static void SysTableTSAmInit(SysTableAmFuncT *sysTableAm)
{
#ifdef FEATURE_TS
    sysTableAm->insertTsMap = SystableTsMapInsertImpl;
    sysTableAm->getTsMapGetIdListByKeyRange = SystableTsMapGetIdListByKeyRangeImpl;
    sysTableAm->getTsMapGetIdListByLogicalId = SystableTsMapGetIdListByLogicalIdImpl;
    sysTableAm->acquireTsMapWLatch = SystableTsMapAcquireWLatchImpl;
    sysTableAm->releaseTsMapWLatch = SystableTsMapReleaseWLatchImpl;
    sysTableAm->insertTsMapLogicalRecord = SystableTsMapInsertLogicalLabelImpl;
    sysTableAm->markTsMapDeletedById = SystableTsMapMarkLabelDeletedByIdImpl;
    sysTableAm->getTsMapLogicalIdList = SystableTsMapGetLogicalIdListImpl;
    sysTableAm->deleteTsMapByPhysicalId = SystableTsMapDeleteByPhysicalIdImpl;
    sysTableAm->getAllPhysicalLabelMeta = SystableTsMapGetAllPhysicalLabelMetaImpl;
    sysTableAm->getPhysicalLabelMetaById = SystableTsMapGetPhysicalLabelMetaByIdImpl;
    sysTableAm->getLabelListByKeyRange = SystableTsMapGetLabelListByKeyRangeImpl;
    sysTableAm->getPhysicalLabelById = SystableTsMapGetPhysicalLabelByIdImpl;
    sysTableAm->initTsMap = SystableTsMapInitImpl;
    sysTableAm->getPhysicalLabelTemplate = SystableGetPhysicalLabelTemplateImpl;
#endif
}

static void SysTablePrivAmInit(SysTableAmFuncT *sysTableAm)
{
    sysTableAm->createUserList = SysTableCreateUserListImpl;
    sysTableAm->destroyUserList = SysTableDestroyUserListImpl;
    sysTableAm->copyUserList = SysTableCopyUserListImpl;
    sysTableAm->insertOneUser = SysTableInsertOneUserImpl;
    sysTableAm->deleteOneUserById = SysTableDeleteOneUserByIdImpl;
    sysTableAm->getAllUser = SysTableGetAllUserImpl;

    sysTableAm->createRoleList = SysTableCreateRoleListImpl;
    sysTableAm->destroyRoleList = SysTableDestroyRoleListImpl;
    sysTableAm->copyRoleList = SysTableCopyRoleListImpl;
    sysTableAm->insertOneRole = SysTableInsertOneRoleImpl;
    sysTableAm->updateOneRole = SysTableUpdateOneRoleImpl;
    sysTableAm->deleteOneRoleById = SysTableDeleteOneRoleByIdImpl;
    sysTableAm->getAllRole = SysTableGetAllRoleImpl;
}

SO_EXPORT_FOR_TS void SysTableAmInit(void)
{
    SysTableAmFuncT sysTableAm = {
        .stbInitFunc = SysTableStartImpl,
        .getStbJson = SysTableGetStbJsonImpl,
        .initStbVl = SysTableInitStbVlImpl,

        .createNspList = SysTableCreateNspListImpl,
        .destroyNspList = SysTableDestroyNspListImpl,
        .copyNspList = SysTableCopyNspListImpl,
        .createVlList = SysTableCreateVlListImpl,
        .destroyVlList = SysTableDestroyVlListImpl,
        .copyVlList = SysTableCopyVlListImpl,
#ifdef FEATURE_EDGELABEL
        .createElList = SysTableCreateElListImpl,
        .destroyElList = SysTableDestroyElListImpl,
        .copyElList = SysTableCopyElListImpl,
#endif
        .assignVlInnerStbIds = SysTableAssignVlInnerStbIdsImpl,
        .insertOneVertexLabel = SysTableInsertOneVertexLabelImpl,
        .updateOneVertexLabel = SysTableUpdateOneVertexLabelImpl,
        .updateOneVertexLabelWithMemCtx = SysTableUpdateOneVertexLabelWithMemCtxImpl,
        .deleteOneVertexLabel = SysTableDeleteOneVertexLabelImpl,
        .getAllVertexLabel = SysTableGetAllVertexLabelImpl,

        // 新增：多版本持久化接口
        .assignVlInnerStbIds = SysTableAssignVlInnerStbIdsImpl,
        .insertOneVertexLabel = SysTableInsertOneVertexLabelImpl,
        .updateOneVertexLabel = SysTableUpdateOneVertexLabelImpl,
        .updateOneVertexLabelWithMemCtx = SysTableUpdateOneVertexLabelWithMemCtxImpl,
        .deleteOneVertexLabel = SysTableDeleteOneVertexLabelImpl,
        .getAllVertexLabel = SysTableGetAllVertexLabelImpl,
#ifdef FEATURE_EDGELABEL
        .insertOneEdgeLabel = SysTableInsertOneEdgeLabelImpl,
        .deleteOneEdgeLabel = SysTableDeleteOneEdgeLabelImpl,
        .getAllEdgeLabel = SysTableGetAllEdgeLabelImpl,
#endif
#if !defined(FEATURE_STREAM) && !defined(FEATURE_TS)
        .insertOneNamespace = SysTableInsertOneNamespaceImpl,
        .deleteOneNamespaceByName = SysTableDeleteOneNamespaceByNameImpl,
        .updateOneNamespace = SysTableUpdateOneNamespaceImpl,
        .getAllNamespace = SysTableNspGetAllNspImpl,
#endif
    };
    SysTableSQLAmInit(&sysTableAm);
    SysTableTSAmInit(&sysTableAm);
    SysTablePrivAmInit(&sysTableAm);
    SysTableSetAmFunc(&sysTableAm);
}

#ifdef __cplusplus
}
#endif
