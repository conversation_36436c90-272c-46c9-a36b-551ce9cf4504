/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Commands for creating and altering namespace structures and settings.
 * Note1: memCtx is used in short process period , we will free all memory when finishing one request
 * Author: GMDBv5 EE Team
 * Create: 2022-7-17
 */

#include "ee_cmd_namespace.h"
#include "ee_cmd_router_fusion.h"
#include "ee_cmd.h"
#include "ee_concurrency_control.h"
#include "ee_feature_import.h"
#include "dm_meta_namespace.h"
#include "ee_ddl_kv.h"
#include "ee_ddl_edge_label.h"
#include "ee_ddl_vertex_label.h"
#include "ee_ddl_resource_column.h"
#include "ee_dcl_ctrl.h"
#include "cpl_base_pub.h"
#include "ee_storage_ctx.h"
#include "ee_systbl.h"
#include "ee_rsm_tablespace.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

// NOTE:状态值必须连续，因为通过++修改了状态,执行过程按照状态定义顺序执行
typedef enum QryClearTableState {
    QRY_CTS_TRUNCATE_KV_TABLE,      // truncate kv表
    QRY_CTS_TRUNCATE_EDGE_LABEL,    // truncate边表
    QRY_CTS_TRUNCATE_VERTEX_LABEL,  // truncate点表
    QRY_CTS_TRUNCATE_RES_POOL,      // truncate资源表
    QRY_CTS_TRUNCATE_END,

    QRY_CTS_CLEANING_YANG_PLAN_CACHE,  // 删除YangPlanCache
    QRY_CTS_CLEANING_KV_TABLE,         // 删除kv表
    QRY_CTS_CLEANING_EDGE_LABEL,       // 删除边表
    QRY_CTS_UNBIND_RES_POOL,           // 解绑资源表
    QRY_CTS_CLEANING_VERTEX_LABEL,     // 删除点表
    QRY_CTS_CLEANING_RES_POOL,         // 删除资源表
    QRY_CTS_CLEANING_END,
} QryClearTableStateE;

typedef struct {
    union {
        uint32_t vertexLabelIdx;    // 删除到cata cache中第几个vertexLabel，从0开始
        uint32_t resPoolUnbindIdx;  // 解绑第几个点表的资源池，从0开始
        uint32_t edgeLabelIdx;      // 删除到cata cache中第几个edgeLabel，从0开始
        uint32_t kvTableIdx;        // 删除到cata cache中第几个kv表，不包括nsp中的全局kv表，从0开始
        uint32_t resPoolIdx;        // 删除到cata cache中第几个res pool，从0开始
    };

    bool isLastSplit;
    QryClearTableStateE state;
} QryClearTableInfoT;
Status QryHandleMiddleSplitWhenTruncateNsp(QryStmtT *stmt, uint32_t nspId);

static Status CheckReservedName(const char *nspName)
{
    char *reservedNames[NAMESPACE_DEFAULT_NUM] = {PUBLIC_NAMESPACE_NAME, "system", "sysview"};
    for (uint32_t i = 0; i < NAMESPACE_DEFAULT_NUM; ++i) {
        if (strcmp(nspName, reservedNames[i]) == 0) {
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_NAME, "Reserved nsp. Check nsp name:%s.", nspName);  // LCOV_EXCL_LINE
            return GMERR_INVALID_NAME;
        }
    }
    return GMERR_OK;
}

static Status CheckReservedNameForReboot(const char *nspName)
{
    char *reservedNames[NAMESPACE_DEFAULT_NUM - 1] = {"system", "sysview"};
    for (uint32_t i = 0; i < NAMESPACE_DEFAULT_NUM - 1; ++i) {
        if (strcmp(nspName, reservedNames[i]) == 0) {
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_NAME, "Reserved nsp. Check nsp name:%s.", nspName);  // LCOV_EXCL_LINE
            return GMERR_INVALID_NAME;
        }
    }
    return GMERR_OK;
}

Status QryCreateNamespace(SeRunCtxHdT seRunCtx, DmNamespaceT *nsp)
{
    DB_POINTER(nsp);

    Status ret = CheckReservedName(nsp->metaCommon.metaName);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx((DbMemCtxT *)seRunCtx->sessionMemCtx);
    if (nsp->metaCommon.metaId == 0) {
        ret = CataGenerateUuid(dbInstance, &nsp->metaCommon.metaId);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "generate uuid, name: %s.", nsp->metaCommon.metaName);  // LCOV_EXCL_LINE
            return ret;
        }
    }
    ret = CataCreateNamespace(nsp, dbInstance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create namespace, name: %s.", nsp->metaCommon.metaName);  // LCOV_EXCL_LINE
        return ret;
    }
    // DmNamespaceT->DobjPriv由CataCreateNamespace创建在共享内存中, 该成员变量赋值直接从cataCache中获取
    ret = CataCacheGetObjPrivByName(nsp, dbInstance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get objPriv for nsp, name: %s.", nsp->metaCommon.metaName);  // LCOV_EXCL_LINE
        return ret;
    }

#if defined(FEATURE_KV)
    // 为该namespace创建一个全局KV表
    ret = QryCreateGlobalKvTable(seRunCtx, nsp->metaCommon.metaId, nsp->defaultTspId);
    if (ret != GMERR_OK) {
        // DropNamespace中调用了DestroyNameSpace，这里不能再重复调用Destroy。
        (void)CataDropNamespaceByName(nsp->metaCommon.metaName, dbInstance);
    }
#endif
    return ret;
}

Status QryCreateNamespaceForReboot(SeRunCtxHdT seRunCtx, DmNamespaceT *nsp)
{
    DB_POINTER(nsp);

    Status ret = CheckReservedNameForReboot(nsp->metaCommon.metaName);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (strcmp(nsp->metaCommon.metaName, "public") == 0) {
        ret = CataNamespaceLoadPersistObjPriv(nsp);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "load persist public objPriv, name: %s.", nsp->metaCommon.metaName);  // LCOV_EXCL_LINE
            return ret;
        }
        return ret;
    } else {
        DbInstanceHdT dbInstance = DbGetInstanceByMemCtx((DbMemCtxT *)seRunCtx->sessionMemCtx);
        if (nsp->metaCommon.metaId == 0) {
            ret = CataGenerateUuid(dbInstance, &nsp->metaCommon.metaId);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "generate uuid, name: %s.", nsp->metaCommon.metaName);  // LCOV_EXCL_LINE
                return ret;
            }
        }
        ret = CataCreateNamespace(nsp, dbInstance);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "create nsp for reboot, name: %s.", nsp->metaCommon.metaName);  // LCOV_EXCL_LINE
            return ret;
        }
    }

#ifdef FEATURE_KV
    // 为该namespace创建一个全局KV表
    ret = QryCreateGlobalKvTable(seRunCtx, nsp->metaCommon.metaId, nsp->defaultTspId);
    if (ret != GMERR_OK) {
        // DropNamespace中调用了DestroyNameSpace，这里不能再重复调用Destroy。
        DbInstanceHdT dbInstance = DbGetInstanceByMemCtx((DbMemCtxT *)seRunCtx->sessionMemCtx);
        (void)CataDropNamespaceByName(nsp->metaCommon.metaName, dbInstance);
        return ret;
    }
#endif
    return ret;
}

Status QryDropNamespace(SeRunCtxHdT seRunCtx, const char *nspName)
{
    DB_POINTER2(nspName, nspName);
    Status ret = GMERR_OK;

    // 先获取namespace对应的id
    uint32_t nspId;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx((DbMemCtxT *)seRunCtx->sessionMemCtx);
    ret = CataGetNamespaceIdByName(dbInstance, nspName, &nspId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get nsp when drop nsp: %s.", nspName);  // LCOV_EXCL_LINE
        return ret;
    }

#if defined(FEATURE_KV)
    CataKeyT cataKey = {0};
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, nspId, GLOBAL_KV_TABLE_NAME);
    DmKvLabelT *nspKvTable = NULL;
    ret = CataGetLabelByName(&cataKey, &nspKvTable, dbInstance);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 校验namespace中的全局Kv表是否能成功删除
    ret = QryCheckDropKvTable(nspKvTable);
    if (ret != GMERR_OK) {
        (void)CataReleaseLabel(nspKvTable);
        DB_LOG_ERROR(ret, "check nsp's KvTable to drop, nsp name: %s.", nspName);  // LCOV_EXCL_LINE
        return ret;
    }
#endif

    // 注意：此处成功后，不允许再失败
    ret = CataDropNamespaceByName(nspName, dbInstance);
    if (ret != GMERR_OK) {
#if defined(FEATURE_KV)
        (void)CataReleaseLabel(nspKvTable);
#endif
        DB_LOG_ERROR(ret, "drop nsp: %s.", nspName);  // LCOV_EXCL_LINE
        return ret;
    }

#if defined(FEATURE_KV)
    // 已经提前校验，此处不会出错
    (void)QryDropKvTable(seRunCtx, nspKvTable);
    (void)CataReleaseLabel(nspKvTable);
#endif
    QryIncCheckChangedObjectCnt();
    QryIncCheckDeletedSubsCnt();

    // 删除namespace时清除对应的YANG的planCache
    if (IsYangPlanCacheExistByNspId(nspId)) {
        ClearYangPlanCacheByNspId(nspId);
    }
    return GMERR_OK;
}

#ifdef FEATURE_NAMESPACE_ENHANCE
Status QryBindNspToTsp(char *nspName, char *tspName, DbInstanceHdT dbInstance)
{
    DB_POINTER2(nspName, tspName);
    Status ret = CheckReservedName(nspName);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = CataCheckBindNspToTsp(nspName, tspName, dbInstance);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CataBindNsptoTsp(nspName, tspName, dbInstance);
}

Status QryDropNamespaceCheck(const char *nspName)
{
    DB_POINTER(nspName);
    return CheckReservedName(nspName);
}

static Status QryCheckAndGetNspIdByName(QryStmtT *stmt, char *nspName, uint32_t *nspId)
{
    Status ret = GMERR_OK;
    // 不能清理系统默认的命名空间名称system、sysview和public
    if ((ret = QryDropNamespaceCheck(nspName)) != GMERR_OK) {
        QryBuildErrorPath4Namespace(stmt, ret, nspName);
        return ret;
    }
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(stmt->session->memCtx);
    if ((ret = CataGetNamespaceIdByName(dbInstance, nspName, nspId)) != GMERR_OK) {
        QryBuildErrorPath4Namespace(stmt, ret, nspName);
    }
    return ret;
}
#endif

Status QryRefillCreateNspDesc(QryCreateNamespaceDescT *desc)
{
    DB_POINTER2(desc, desc->nsp);
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(desc->nsp->memCtx);
    if (desc->nsp->owner.str == NULL) {
        desc->nsp->owner = DbStr2Text("default");
    }
    if (desc->tspName.str == NULL) {
        desc->nsp->defaultTspId = PUBLIC_TABLESPACE_ID;
    } else {
        Status ret = CataGetTspIdByName(desc->tspName.str, &desc->nsp->defaultTspId, dbInstance);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get tableSpace Id from catalog, tspName: %s.", desc->tspName.str);  // LCOV_EXCL_LINE
            return ret;
        }
        bool isUseRsm = false;
        ret = QryRsmGetTspIsUseRsmByName(dbInstance, desc->tspName.str, &isUseRsm);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get isUseRsm flag from catalog, tspName: %s.", desc->tspName.str);  // LCOV_EXCL_LINE
            return ret;
        }
        if (isUseRsm) {
            // LCOV_EXCL_START
            DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "use rsm tableSpace, tspName: %s.", desc->tspName.str);
            // LCOV_EXCL_STOP
            return GMERR_INVALID_PARAMETER_VALUE;
        }
    }
    return GMERR_OK;
}

Status QryExecuteCreateNamespace(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryCreateNamespaceDescT *desc = (QryCreateNamespaceDescT *)stmt->context->entry;
    Status ret = QryRefillCreateNspDesc(desc);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (desc->nsp->metaCommon.metaId == 0) {
        ret = CataGenerateUuid(DbGetInstanceByMemCtx(stmt->session->memCtx), &desc->nsp->metaCommon.metaId);
        if (ret != GMERR_OK) {
            QryBuildErrorPath4Namespace(stmt, ret, desc->nsp->metaCommon.metaName);
            return ret;
        }
    }
    ret = QryCreateNamespace(stmt->session->seInstance, desc->nsp);
    if (ret != GMERR_OK) {
        QryBuildErrorPath4Namespace(stmt, ret, desc->nsp->metaCommon.metaName);
        return ret;
    }
    ret = SysTableInsertOneNamespace(stmt->session, desc->nsp);
    if (ret != GMERR_OK) {
        // 系统表插入失败, 清理namespace
        DB_LOG_ERROR(ret, "insert to nsp systb, namespace=%s.", desc->nsp->metaCommon.metaName);  // LCOV_EXCL_LINE
        (void)QryDropNamespace(stmt->session->seInstance, desc->nsp->metaCommon.metaName);
        QryBuildErrorPath4Namespace(stmt, ret, desc->nsp->metaCommon.metaName);
        return ret;
    }

#ifdef FEATURE_REPLICATION
    ret = QryReplicateCreateNameSpace(stmt->session->logBuf, desc->nsp);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create nsp in replication, name: %s.", desc->nsp->metaCommon.metaName);  // LCOV_EXCL_LINE
        return ret;
    }
#endif

    return ret;
}

Status QryExecuteCreateNamespaceForReboot(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryCreateNamespaceDescT *desc = (QryCreateNamespaceDescT *)stmt->context->entry;
    Status ret = QryRefillCreateNspDesc(desc);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (desc->nsp->metaCommon.metaId == 0) {
        ret = CataGenerateUuid(DbGetInstanceByMemCtx(stmt->session->memCtx), &desc->nsp->metaCommon.metaId);
        if (ret != GMERR_OK) {
            QryBuildErrorPath4Namespace(stmt, ret, desc->nsp->metaCommon.metaName);
            return ret;
        }
    }
    // 重启时创建系统表中读取出来的nsp时，不用插入系统表
    ret = QryCreateNamespaceForReboot(stmt->session->seInstance, desc->nsp);
    if (ret != GMERR_OK) {
        (void)QryDropNamespace(stmt->session->seInstance, desc->nsp->metaCommon.metaName);
        QryBuildErrorPath4Namespace(stmt, ret, desc->nsp->metaCommon.metaName);
        return ret;
    }
    if (strcmp(desc->nsp->metaCommon.metaName, PUBLIC_NAMESPACE_NAME) == 0) {
        DmNamespaceT *cataPublicNamespace = NULL;
        DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(desc->nsp->memCtx);
        ret = CataGetNamespaceByName(dbInstance, PUBLIC_NAMESPACE_NAME, &cataPublicNamespace);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get public nsp, nsp id=%s.", PUBLIC_NAMESPACE_NAME);  // LCOV_EXCL_LINE
            return ret;
        }
        ret = SysTableUpdateOneNamespace(stmt->session, cataPublicNamespace);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "update sys public nsp:%s.", PUBLIC_NAMESPACE_NAME);  // LCOV_EXCL_LINE
            return ret;
        }
    }
    return ret;
}

#ifdef FEATURE_NAMESPACE_ENHANCE
static Status QryGetAndUsePublicNamespace(DbInstanceHdT dbInstance, uint32_t *publicNameSpaceId)
{
    Status ret = CataGetNamespaceIdByName(dbInstance, PUBLIC_NAMESPACE_NAME, publicNameSpaceId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get nsp id, nsp=%s.", PUBLIC_NAMESPACE_NAME);  // LCOV_EXCL_LINE
        return ret;
    }
    ret = CataUseNamespace(*publicNameSpaceId, dbInstance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "use nsp, nsp=%s.", PUBLIC_NAMESPACE_NAME);  // LCOV_EXCL_LINE
        return ret;
    }
    return GMERR_OK;
}

Status QryExecuteDropCurrNamespace(QryStmtT *stmt, uint32_t currNameSpaceId)
{
    QryDropNamespaceDescT *desc = (QryDropNamespaceDescT *)stmt->context->entry;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(stmt->session->memCtx);
    Status ret = CataUnuseNamespace(currNameSpaceId, DbGetInstanceByMemCtx(stmt->memCtx));
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "drop nsp, name: %s.", desc->namespaceName.str);  // LCOV_EXCL_LINE
        return ret;
    }
    // 删除的namespace和当前stmt挂载的namespace一致，因此要清理
    if (stmt->session->nsp != NULL) {
        (void)CataReleaseNamespace(dbInstance, stmt->session->nsp);
        stmt->session->nsp = NULL;
    }

    ret = QryDropNamespace(stmt->session->seInstance, desc->namespaceName.str);
    if (ret != GMERR_OK) {
        Status result = CataUseNamespace(currNameSpaceId, dbInstance);
        if (result == GMERR_UNDEFINED_OBJECT) {  // 若Namespace被其他线程删除而无法use，此处也视为删除成功
            ret = GMERR_OK;
        }
        if (ret == GMERR_RESTRICT_VIOLATION) {
            stmt->errorCode = RESTRICT_VIOLATION_NAMESPACE_ERROR_PATH;
        }
        QryBuildErrorPath4Namespace(stmt, ret, desc->namespaceName.str);
    }
    return ret;
}

static Status QryDropCurrNamespace(QryStmtT *stmt)
{
    uint32_t publicNameSpaceId = 0;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(stmt->session->memCtx);
    Status ret = QryGetAndUsePublicNamespace(dbInstance, &publicNameSpaceId);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "get and use public nsp when drop cur nsp, id=%" PRIu32, stmt->session->namespaceId);
        // LCOV_EXCL_STOP
        return ret;
    }

    // 删除当前正在Use的Namespace
    uint32_t currNameSpaceId = stmt->session->namespaceId;
    ret = QryExecuteDropCurrNamespace(stmt, currNameSpaceId);
    if (ret != GMERR_OK) {
        (void)CataUnuseNamespace(publicNameSpaceId, DbGetInstanceByMemCtx(stmt->session->memCtx));
        return ret;
    }
    // 切换到public Namespace
    stmt->session->namespaceId = publicNameSpaceId;
    return GMERR_OK;
}

static Status QryDropGlobalKvTable(QryStmtT *stmt, uint32_t dropNameSpaceId)
{
    Status ret = GMERR_OK;
    CataKeyT cataKey = {0};
    DmKvLabelT *globalKvTable = NULL;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, dropNameSpaceId, GLOBAL_KV_TABLE_NAME);
    ret = CataGetLabelByName(&cataKey, &globalKvTable, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get label, name=%s.", GLOBAL_KV_TABLE_NAME);  // LCOV_EXCL_LINE
        return ret;
    }

    ret = QryAcqLatchForKvLabel(stmt, globalKvTable, true);
    (void)CataReleaseLabel(globalKvTable);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static Status QryDropNamespaceWithSysTable(QryStmtT *stmt, DmNamespaceT *nsp)
{
    Status ret = GMERR_OK;
#ifdef FEATURE_KV
    // 这里实际上只是加了表锁，并没有真的删除
    ret = QryDropGlobalKvTable(stmt, nsp->metaCommon.metaId);
    if (ret != GMERR_OK) {
        return ret;
    }
#endif

    // 预留空间避免失败，catalog无法回滚
    uint32_t *reservedAddr = FixBufReserveData(stmt->session->rsp, sizeof(uint32_t));
    if (reservedAddr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Get reserved address when drop nsp: %s.",
            nsp->metaCommon.metaName);  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }

    ret = SysTableDeleteOneNamespaceByName(stmt->session, nsp->metaCommon.metaName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "delete nsp from sys table, nsp name: %s.", nsp->metaCommon.metaName);  // LCOV_EXCL_LINE
        return ret;
    }

    uint32_t currNameSpaceId = stmt->session->namespaceId;
    if (currNameSpaceId != nsp->metaCommon.metaId) {
        ret = QryDropNamespace(stmt->session->seInstance, nsp->metaCommon.metaName);
    } else {
        ret = QryDropCurrNamespace(stmt);
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    *reservedAddr = stmt->session->namespaceId;

#ifdef FEATURE_REPLICATION
    ret = QryReplicateDropNameSpace(stmt->session->logBuf, nsp);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "drop nsp in replication, name: %s.", nsp->metaCommon.metaName);  // LCOV_EXCL_LINE
        return ret;
    }
#endif
    return GMERR_OK;
}

Status QryExecuteDropNamespace(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    Status ret = GMERR_OK;
    QryDropNamespaceDescT *desc = (QryDropNamespaceDescT *)stmt->context->entry;
    const char *name = desc->namespaceName.str;
    DmNamespaceT *nsp = NULL;
    // 不能清理系统默认的命名空间名称system、sysview和public
    ret = QryDropNamespaceCheck(name);
    if (ret != GMERR_OK) {
        QryBuildErrorPath4Namespace(stmt, ret, name);
        DB_LOG_ERROR(ret, "drop nsp, name=%s.", name);
        return ret;
    }

    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(stmt->session->memCtx);
    ret = CataGetNamespaceByName(dbInstance, name, &nsp);
    if (ret == GMERR_UNDEFINED_TABLE) {
        ret = GMERR_UNDEFINED_OBJECT;  // 用例有对错误码的校验
    }

    if (ret != GMERR_OK) {
        QryBuildErrorPath4Namespace(stmt, ret, name);
        DB_LOG_ERROR(ret, "get nsp in drop nsp:%s.", name);
        return ret;
    }
    // 其他分支YANG项目自己排查要不要补充ErrorPath
    ret = QryDropNamespaceWithSysTable(stmt, nsp);
    (void)CataReleaseNamespace(DbGetInstanceByMemCtx(stmt->session->memCtx), nsp);
    return ret;
}

Status QryExecuteBindNspToTsp(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryBindNspToTspDescT *desc = (QryBindNspToTspDescT *)stmt->context->entry;
    return QryBindNspToTsp(
        desc->namespaceName.str, desc->tablespaceName.str, DbGetInstanceByMemCtx(stmt->session->memCtx));
}

static void QryReleaseLastLabelIXLock(SessionT *session, ConcurrencyControlE ccType)
{
    if ((SeTransGetTrxType(session->seInstance) != OPTIMISTIC_TRX) && (ccType == CONCURRENCY_CONTROL_NORMAL)) {
        SeLockMgrLockReleaseLastNewLabelXLock(session->seInstance);
    }
}

#ifdef FEATURE_KV
Status QryDropKvTableWhenClearingNsp(QryStmtT *stmt, uint32_t dropNameSpaceId, QryClearTableInfoT *clearInfo)
{
    Status ret = GMERR_OK;
    // 从Catalog中查询对应名称的标签
    DmKvLabelT *label =
        CataFetchKvTableByNspId(dropNameSpaceId, &clearInfo->kvTableIdx, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (label == NULL) {
        // 表示已经删除了所有的Kv表
        clearInfo->state++;
        clearInfo->kvTableIdx = 0;
        return GMERR_OK;
    }
    bool isGlobalKvTable = strcmp(label->metaCommon.metaName, GLOBAL_KV_TABLE_NAME) == 0 &&
                           label->metaCommon.namespaceId == dropNameSpaceId &&
                           label->metaCommon.dbId == DEFAULT_DATABASE_ID;
    if (isGlobalKvTable) {
        goto EXIT;
    }
    // 加表锁，异常退出要解锁，删除操作完之后也要解锁
    ret = QryAcqLatchForKvLabel(stmt, label, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "acqLatch when drop KV table, name: %s.", label->metaCommon.metaName);  // LCOV_EXCL_LINE
        goto EXIT;
    }

    // 执行删除kv表
    ret = QryDropKvTable(stmt->session->seInstance, label);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "drop KV table, name: %s.", label->metaCommon.metaName);  // LCOV_EXCL_LINE
        goto EXIT;
    }

#ifdef FEATURE_REPLICATION
    ret = QryReplicateDropKvTable(stmt->session->logBuf, label);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "drop kv table in replication in nsp, label name=%s", label->metaCommon.metaName);
        // LCOV_EXCL_STOP
        goto EXIT;
    }
#endif

    QryIncCheckChangedObjectCnt();
    QryReleaseAndUpgradeAllLabelLatch(stmt->session);  // 成功才能调用
    QryReleaseLastLabelIXLock(stmt->session, label->ccType);
EXIT:
    (void)CataReleaseLabel(label);
    return ret;
}
#endif

Status QryDropEdgeLabelWhenClearingNsp(QryStmtT *stmt, uint32_t dropNameSpaceId, QryClearTableInfoT *clearInfo)
{
    Status ret = GMERR_OK;
    // 从Catalog中查询对应名称的标签
    DmEdgeLabelT *edgeLabel = CataFetchEdgeLabelByNspId(
        dropNameSpaceId, &clearInfo->edgeLabelIdx, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (edgeLabel == NULL) {
        // 表示已经删除了所有的边表
        clearInfo->state++;
        clearInfo->edgeLabelIdx = 0;
        return GMERR_OK;
    }

    ret = QryAcqLatchForEdgeLabel(stmt, edgeLabel, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "acqLatch when drop edgeLabel table: %s.", edgeLabel->metaCommon.metaName);  // LCOV_EXCL_LINE
        goto EXIT;
    }
    if (edgeLabel->metaCommon.isPersistent) {
        ret = SysTableDeleteOneEdgeLabel(stmt->session, edgeLabel);
        if (ret != GMERR_OK) {
            goto EXIT;
        }
    }
    ConcurrencyControlE ccType = edgeLabel->ccType;
    ret = QryDropEdgeLabelByName(edgeLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "drop edgeLabel table: %s.", edgeLabel->metaCommon.metaName);  // LCOV_EXCL_LINE
        goto EXIT;
    }
    // 最后一个分片结束后才提交事务，所以防止事务锁资源不足，提前在每个分片结束后将事务锁和表锁都释放
    QryReleaseAndUpgradeAllLabelLatch(stmt->session);
    QryReleaseLastLabelIXLock(stmt->session, ccType);
EXIT:
    (void)CataReleaseEdgeLabel(edgeLabel);
    return ret;
}

#ifdef FEATURE_RESPOOL_SRV
Status QryUnbindVertexLabelWhenClearingNsp(QryStmtT *stmt, uint32_t dropNameSpaceId, QryClearTableInfoT *clearInfo)
{
    Status ret = GMERR_OK;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(stmt->session->memCtx);
    DmVertexLabelT *vertexLabel =
        CataFetchVertexLabelByNspId(dropNameSpaceId, &clearInfo->resPoolUnbindIdx, dbInstance);
    if (vertexLabel == NULL) {
        // 所有的点表都已解绑
        clearInfo->state++;
        clearInfo->resPoolUnbindIdx = 0;
        return GMERR_OK;
    }
    // 给点表加表锁
    ret = QryAcqLatchForVertexLabel(stmt, vertexLabel, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "acqLatch when unbind VertexLabel: %s.", vertexLabel->metaCommon.metaName);  // LCOV_EXCL_LINE
        (void)CataReleaseVertexLabel(vertexLabel);
        return ret;
    }
    ConcurrencyControlE ccType = vertexLabel->commonInfo->heapInfo.ccType;
    DmResColInfoT *resColInfo = vertexLabel->commonInfo->resColInfo;
    if (resColInfo == NULL) {
        // 如果点表没有关联资源池，点表解锁返回
        goto RELEASE;
    }
    // 解绑点表上所有关联的资源池，包括资源表的解绑
    // 资源池加锁
    ret = QryUnbindAllResColPoolInVtxLabel(stmt, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unbind VertexLabel: %s.", vertexLabel->metaCommon.metaName);  // LCOV_EXCL_LINE
    }
RELEASE:
    (void)CataReleaseVertexLabel(vertexLabel);
    // 最后一个分片结束后才提交事务，所以防止事务锁资源不足，提前在每个分片结束后将事务锁和表锁都释放
    QryReleaseAllLabelLatch(stmt->session);
    QryReleaseLastLabelIXLock(stmt->session, ccType);
    return ret;
}
#endif

Status QryDropVertexLabelWhenClearingNsp(QryStmtT *stmt, uint32_t dropNameSpaceId, QryClearTableInfoT *clearInfo)
{
    Status ret = GMERR_OK;
    DmVertexLabelT *vertexLabel = CataFetchVertexLabelByNspId(
        dropNameSpaceId, &clearInfo->vertexLabelIdx, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (vertexLabel == NULL) {
        // 所有的点表都删除了
        clearInfo->state++;
        clearInfo->vertexLabelIdx = 0;
        return GMERR_OK;
    }
    ConcurrencyControlE ccType = vertexLabel->commonInfo->heapInfo.ccType;

    if ((SeTransGetTrxType(stmt->session->seInstance) != OPTIMISTIC_TRX) && (ccType == CONCURRENCY_CONTROL_NORMAL)) {
        DB_ASSERT(!SeLockMgrIsAnyLockAcquired(stmt->session->seInstance));
    }
    // 此时所有的表上关联的边表都已经被删除了，也解绑资源列，直接删除点表
    // 给点表加表锁
    ret = QryAcqLatchForVertexLabel(stmt, vertexLabel, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "acqLatch when drop VertexLabel: %s.", vertexLabel->metaCommon.metaName);  // LCOV_EXCL_LINE
        goto EXIT;
    }
    // 再次检查一下点表是否为datalog表，是否有订阅和是否处于对账期间
    ret = QryCheckDropSingleVertexLabel(vertexLabel);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    if (vertexLabel->metaCommon.isPersistent) {
        ret = SysTableDeleteOneVertexLabelEx(stmt->session, vertexLabel);
        if (ret != GMERR_OK) {
            // LCOV_EXCL_START
            DB_LOG_ERROR(ret, "delete stb when drop VertexLabel: %s.", vertexLabel->metaCommon.metaName);
            // LCOV_EXCL_STOP
            goto EXIT;
        }
    }
    ret = QryDropVertexLabelInTableLock(stmt, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "drop VertexLabel, name: %s.", vertexLabel->metaCommon.metaName);
        goto EXIT;
    }
    QryIncCheckChangedObjectCnt();
    // 最后一个分片结束后才提交事务，所以防止事务锁资源不足，提前在每个分片结束后将事务锁和表锁都释放
    QryReleaseAndUpgradeAllLabelLatch(stmt->session);  // 成功才能调用，会删表锁
    QryReleaseLastLabelIXLock(stmt->session, ccType);
EXIT:
    (void)CataReleaseVertexLabel(vertexLabel);
    return ret;
}

#ifdef FEATURE_RESPOOL_SRV
Status QryDropRespoolWhenClearingNsp(QryStmtT *stmt, uint32_t dropNameSpaceId, QryClearTableInfoT *clearInfo)
{
    Status ret = GMERR_OK;
    // 从Catalog中查询对应名称的标签
    DmResColPoolT *resPool =
        CataFetchResPoolByNspId(dropNameSpaceId, &clearInfo->resPoolIdx, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (resPool == NULL) {
        // 表示除了全局KV表，其他的表都被删除了
        clearInfo->state++;
        clearInfo->resPoolIdx = 0;
        return GMERR_OK;
    }
    ret = QryAcqLatchForResColPool(stmt, resPool->labelLatchId, resPool->labelLatchVersionId, true);
    if (ret != GMERR_OK) {
        // 可能需要release respool，和catalog对齐
        DB_LOG_ERROR(ret, "acqLatch when drop res pool: %s.", resPool->metaCommon.metaName);  // LCOV_EXCL_LINE
        goto EXIT;
    }
    // truncate respool
    ret = ResColPoolReleaseAllRes(resPool->resPool);
    if (ret != GMERR_OK) {
        // 可能需要release respool，和catalog对齐
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "release all respools related to the res pool: %s.", resPool->metaCommon.metaName);
        // LCOV_EXCL_STOP
        goto EXIT;
    }
    // 解绑该respool上的extendResPool
    bool isExisted = false;
    ret = ResColExtendedPoolIsExisted(resPool->resPool, &isExisted);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    if (isExisted) {
        ret = ResColUnbindExtendedPool(resPool->resPool);
        if (ret != GMERR_OK) {
            // 可能需要release respool，和catalog对齐
            // LCOV_EXCL_START
            DB_LOG_ERROR(ret, "unbind extended respools related to the res pool: %s.", resPool->metaCommon.metaName);
            // LCOV_EXCL_STOP
            goto EXIT;
        }
    }

    ret = QryDropResColPool(resPool);
    if (ret != GMERR_OK) {
        // 可能需要release respool，和catalog对齐
        DB_LOG_ERROR(ret, "drop respool %s.", resPool->metaCommon.metaName);  // LCOV_EXCL_LINE
        goto EXIT;
    }
    QryReleaseAndUpgradeAllLabelLatch(stmt->session);  // 成功才能调用
EXIT:
    (void)CataReleaseResColPool(resPool);
    return ret;
}
#endif

Status QryClearYangPlanCacheWhenClearingNsp(uint32_t nspId, QryClearTableInfoT *clearInfo)
{
    if (!IsYangPlanCacheExistByNspId(nspId)) {
        clearInfo->state++;
        return GMERR_OK;
    }
    // 清除namespace时清除对应的YANG的planCache
    ClearYangPlanCacheByNspId(nspId);
    return GMERR_OK;
}

/*
 * 在删表前，先检查dropNameSpace下所有的点表是不是datalog表或者有订阅或者对账期间，如果检查不通过就报错，
 * 但是并不能保证删表期间，突然新增订阅或者重新处于对账期间，或者新增了datalog表，所以在删除的时候需要重新检查。
 * 如果要解决上述的问题就需要给nsp加锁或者实现ddl串行，这些实现都有问题（超时或者性能差等），
 * 由于GMDBV5的ddl本身也并不保证原子性，所以采用了现在的方案。
 * 注意！！此接口只检查了点表，对应QryExecuteDropSingleVertexLabel的检查，如果后续删除点表的检查有修改，需要同步修改到此接口。
 * 注意！！如果边表、kv表、资源表删除操作新增了检查，也需要同步添加到此接口中。
 */
Status QryClearNamespaceCheck(QryStmtT *stmt, uint32_t dropNameSpaceId, QryClearTableInfoT *clearCheckInfo)
{
    Status ret = GMERR_OK;
    DmVertexLabelT *vertexLabel = NULL;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(stmt->session->memCtx);
    // 检查时不加表锁，因为删除的时候会再次进行相同的判断，
    // 并且如果这里加锁，遇上一个nsp下删除1w张表的场景时，锁的开销较大可能会导致超时
    while ((vertexLabel = CataFetchVertexLabelByNspId(dropNameSpaceId, &clearCheckInfo->vertexLabelIdx, dbInstance)) !=
           NULL) {
        // 删表检查期间不允许新增订阅
        CataSetVertexLabelCanSub(vertexLabel, false);
        // datalog表和有订阅的非datalog表不允许进行clearnsp操作
        ret = QryCheckVertexLabelDropCond(vertexLabel);
        CataSetVertexLabelCanSub(vertexLabel, true);
        (void)CataReleaseVertexLabel(vertexLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    // 所有的点表都检查过了，恢复tableInfo中的vertexLabel下标，使其在删除的时候能复用，从0开始
    clearCheckInfo->vertexLabelIdx = 0;
    return GMERR_OK;
}

Status QryHandleFirstSplitWhenClearingNsp(QryStmtT *stmt, uint32_t dropNameSpaceId, NameSpaceStateE state)
{
    Status ret = GMERR_OK;
    // 设置标记位，防止在清理过程中，有别的线程对相同的nsp进行任何操作
    ret = CataSetNamespaceState(DbGetInstanceByMemCtx(stmt->session->memCtx), dropNameSpaceId, state);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "nsp is clearing and set clear flag, nspId:%" PRIu32 ".", dropNameSpaceId);
        return ret;
    }

    // 需要给stmt->splitInfo.clearTableInfo申请内存，后续任何失败的时候要释放该资源
    stmt->splitInfo.extraInfo = (void *)DbDynMemCtxAlloc(stmt->session->memCtx, sizeof(QryClearTableInfoT));
    if (stmt->splitInfo.extraInfo == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        // LCOV_EXCL_START
        DB_LOG_AND_SET_LASERR(ret, "create QryClearTableInfoT, nspId is %" PRIu32 ".", dropNameSpaceId);
        // LCOV_EXCL_STOP
        goto ERROR;
    }

    (void)memset_s(stmt->splitInfo.extraInfo, sizeof(QryClearTableInfoT), 0, sizeof(QryClearTableInfoT));

    // 检查nsp下所有的表
    ret = QryClearNamespaceCheck(stmt, dropNameSpaceId, stmt->splitInfo.extraInfo);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "clear nsp(nspId: %" PRIu32 ").", dropNameSpaceId);
        goto ERROR;
    }

    stmt->splitInfo.isFirst = false;
    stmt->splitInfo.isLast = false;
    stmt->eof = false;

    return GMERR_OK;

ERROR:
    (void)CataSetNamespaceState(DbGetInstanceByMemCtx(stmt->session->memCtx), dropNameSpaceId, NSP_NORMAL);
    if (stmt->splitInfo.extraInfo != NULL) {
        DbDynMemCtxFree(stmt->session->memCtx, stmt->splitInfo.extraInfo);
        stmt->splitInfo.extraInfo = NULL;
    }
    return ret;
}

Status QryHandleLastSplitWhenClearingNsp(QryStmtT *stmt, uint32_t dropNameSpaceId, QryDropNamespaceDescT *desc)
{
    Status ret = GMERR_OK;
#ifdef FEATURE_KV
    DmKvLabelT *globalKvTable = NULL;
    CataKeyT cataKeyKvTable = {0};
    CataSetKeyForLabel(&cataKeyKvTable, DEFAULT_DATABASE_ID, dropNameSpaceId, GLOBAL_KV_TABLE_NAME);
    ret = CataGetLabelByName(&cataKeyKvTable, &globalKvTable, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryAcqLatchForKvLabel(stmt, globalKvTable, false);
    if (ret != GMERR_OK) {
        (void)CataReleaseLabel(globalKvTable);
        return ret;
    }

    // 清除namespace中全局Kv表的数据，和刚创建的状态一致；
    ret = QryTruncateKvTable(stmt, globalKvTable);
    (void)CataReleaseLabel(globalKvTable);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "drop global kv table of nspName when clearing: %s.", desc->namespaceName.str);
        // LCOV_EXCL_STOP
        return ret;
    }
#endif
    QryReleaseAndUpgradeAllLabelLatch(stmt->session);  // 成功才能调用
    stmt->eof = true;

    // 给客户端返回nspId
    ret = FixBufPutUint32(stmt->session->rsp, dropNameSpaceId);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_AND_SET_LASERR(ret, "put nspId=%" PRIu32 " to response when execute clear nsp.", dropNameSpaceId);
        // LCOV_EXCL_STOP
        return ret;
    }

    (void)CataSetNamespaceState(DbGetInstanceByMemCtx(stmt->session->memCtx), dropNameSpaceId, NSP_NORMAL);
    return GMERR_OK;
}

Status QryExecuteClearNamespace(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    Status ret = GMERR_OK;
    QryDropNamespaceDescT *desc = (QryDropNamespaceDescT *)stmt->context->entry;
    uint32_t dropNameSpaceId = 0;
    ret = QryCheckAndGetNspIdByName(stmt, desc->namespaceName.str, &dropNameSpaceId);  // 后续优化，没必要每次都调用
    if (ret != GMERR_OK) {
        return ret;
    }

    // 每个分片结束都立即释放表锁，因为加锁顺序与插入点边表的顺序相反，如果不释放，可能会造成死锁.
    // 如果是第一个分片需要加判断，防止清理正在被清理的表；设置正在被清理的标记位；给分片信息分配资源；
    // 判断nsp下所有的点表不是datalog表，没有订阅也不在对账期间，否则报错
    if (stmt->splitInfo.isFirst) {
        return QryHandleFirstSplitWhenClearingNsp(stmt, dropNameSpaceId, NSP_CLEARING);
    }

    // 如果是最后一个分片，清理掉全局kv表的数据，不删除全局kv表，和刚创建的状态一致；设置操作结束的标记
    if (((QryClearTableInfoT *)stmt->splitInfo.extraInfo)->state >= QRY_CTS_CLEANING_END) {
        ret = QryHandleLastSplitWhenClearingNsp(stmt, dropNameSpaceId, desc);
        DbDynMemCtxFree(stmt->session->memCtx, stmt->splitInfo.extraInfo);
        stmt->splitInfo.extraInfo = NULL;
        return ret;
    }

    // 任何一个分片（除了第一个和最后一个分片）进行的删表操作
    ret = QryHandleMiddleSplitWhenTruncateNsp(stmt, dropNameSpaceId);
    if (ret != GMERR_OK) {
        (void)CataSetNamespaceState(DbGetInstanceByMemCtx(stmt->session->memCtx), dropNameSpaceId, NSP_CLEAR_RETRY);
        DbDynMemCtxFree(stmt->session->memCtx, stmt->splitInfo.extraInfo);
        stmt->splitInfo.extraInfo = NULL;
        return ret;
    }

    return GMERR_OK;
}

#ifdef FEATURE_KV
Status QryTruncateKvTableInNsp(QryStmtT *stmt, uint32_t nspId, QryClearTableInfoT *clearInfo)
{
    Status ret = GMERR_OK;
    // 从Catalog中查询对应名称的标签
    DmKvLabelT *label =
        CataFetchKvTableByNspId(nspId, &clearInfo->kvTableIdx, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (label == NULL) {
        // 表示已经删除了所有的Kv表
        clearInfo->state++;
        clearInfo->kvTableIdx = 0;
        return GMERR_OK;
    }
    bool isGlobalKvTable = strcmp(label->metaCommon.metaName, GLOBAL_KV_TABLE_NAME) == 0 &&
                           label->metaCommon.namespaceId == nspId && label->metaCommon.dbId == DEFAULT_DATABASE_ID;
    if (isGlobalKvTable) {
        (void)CataReleaseLabel(label);
        return GMERR_OK;
    }
    // 加表锁，异常退出要解锁，删除操作完之后也要解锁
    ret = QryAcqLatchForKvLabel(stmt, label, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "acqLatch when truncate KV table, name: %s.", label->metaCommon.metaName);  // LCOV_EXCL_LINE
        (void)CataReleaseLabel(label);
        return ret;
    }

    // 执行删除kv表
    ret = QryTruncateKvTable(stmt, label);
    QryIncCheckChangedObjectCnt();
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "truncate KV table, name: %s.", label->metaCommon.metaName);  // LCOV_EXCL_LINE
    }
    QryReleaseAllLabelLatch(stmt->session);
    QryReleaseLastLabelIXLock(stmt->session, label->ccType);
    (void)CataReleaseLabel(label);
    return ret;
}
#endif

Status QryTruncateEdgeLabelInNsp(QryStmtT *stmt, uint32_t nspId, QryClearTableInfoT *clearInfo)
{
    Status ret = GMERR_OK;
    // 从Catalog中查询对应名称的标签
    DmEdgeLabelT *edgeLabel =
        CataFetchEdgeLabelByNspId(nspId, &clearInfo->edgeLabelIdx, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (edgeLabel == NULL) {
        // 表示已经删除了所有的边表
        clearInfo->state++;
        clearInfo->edgeLabelIdx = 0;
        return GMERR_OK;
    }
    ConcurrencyControlE ccType = edgeLabel->ccType;
    if ((SeTransGetTrxType(stmt->session->seInstance) != OPTIMISTIC_TRX) && (ccType == CONCURRENCY_CONTROL_NORMAL)) {
        DB_ASSERT(!SeLockMgrIsAnyLockAcquired(stmt->session->seInstance));
    }
    ret = QryAcqLatchForEdgeLabel(stmt, edgeLabel, false);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "acqLatch when truncate edgeLabel table, name: %s.", edgeLabel->metaCommon.metaName);
        // LCOV_EXCL_STOP
        (void)CataReleaseEdgeLabel(edgeLabel);
        return ret;
    }
    // truncate 边表
    ret = EdgeTopoLabelTruncate(edgeLabel->topoShmAddr, edgeLabel->metaCommon.isPersistent);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "drop edgeLabel table, name: %s.", edgeLabel->metaCommon.metaName);  // LCOV_EXCL_LINE
    }
    (void)CataReleaseEdgeLabel(edgeLabel);
    // 最后一个分片结束后才提交事务，所以防止事务锁资源不足，提前在每个分片结束后将事务锁和表锁都释放
    QryReleaseAllLabelLatch(stmt->session);
    QryReleaseLastLabelIXLock(stmt->session, ccType);
    return ret;
}

Status QryTruncateVertexLabelInNsp(QryStmtT *stmt, uint32_t nspId, QryClearTableInfoT *clearInfo)
{
    Status ret = GMERR_OK;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(stmt->session->memCtx);
    DmVertexLabelT *vertexLabel = CataFetchVertexLabelByNspId(nspId, &clearInfo->vertexLabelIdx, dbInstance);
    if (vertexLabel == NULL) {
        // 所有的点表都删除了
        clearInfo->state++;
        clearInfo->vertexLabelIdx = 0;
        return GMERR_OK;
    }
    // 给点表加表锁
    ret = QryAcqLatchForVertexLabel(stmt, vertexLabel, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "acqLatch when truncating VertexLabel, name: %s.", vertexLabel->metaCommon.metaName);
        (void)CataReleaseVertexLabel(vertexLabel);
        return ret;
    }
    ConcurrencyControlE ccType = vertexLabel->commonInfo->heapInfo.ccType;
    ret = QryTruncateVertexLabelAndIndex(stmt->session->seInstance, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "truncate VertexLabel, name: %s.", vertexLabel->metaCommon.metaName);  // LCOV_EXCL_LINE
        (void)CataReleaseVertexLabel(vertexLabel);
        return ret;
    }

    // 重置自增列的值(重新设置为起始值)
    QryResetAutoIncrPropValue(vertexLabel);
    QryUpdateCheckInfo4Truncate(vertexLabel);

#ifdef FEATURE_REPLICATION
    ret = QryReplicateTruncateVertexLabel(stmt->session->logBuf, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "replicate to truncate nsp vertex table: %s.", vertexLabel->metaCommon.metaName);
        (void)CataReleaseVertexLabel(vertexLabel);
        return ret;
    }
#endif

    (void)CataReleaseVertexLabel(vertexLabel);
    // 最后一个分片结束后才提交事务，所以防止事务锁资源不足，提前在每个分片结束后将事务锁和表锁都释放
    QryReleaseAllLabelLatch(stmt->session);
    QryReleaseLastLabelIXLock(stmt->session, ccType);
    return ret;
}

#ifdef FEATURE_RESPOOL_SRV
Status QryTruncateRespoolInNsp(QryStmtT *stmt, uint32_t nspId, QryClearTableInfoT *clearInfo)
{
    Status ret = GMERR_OK;
    // 从Catalog中查询对应名称的标签
    DmResColPoolT *resPool =
        CataFetchResPoolByNspId(nspId, &clearInfo->resPoolIdx, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (resPool == NULL) {
        // 表示除了全局KV表，其他的表都被truncate了
        clearInfo->state++;
        clearInfo->resPoolIdx = 0;
        return GMERR_OK;
    }
    ret = QryAcqLatchForResColPool(stmt, resPool->labelLatchId, resPool->labelLatchVersionId, false);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "acqLatch when truncate res pool, name: %s.", resPool->metaCommon.metaName);
        // LCOV_EXCL_STOP
        (void)CataReleaseResColPool(resPool);
        return ret;
    }
    // truncate respool
    ret = ResColPoolReleaseAllRes(resPool->resPool);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "release all respools related to the res pool, name: %s.", resPool->metaCommon.metaName);
        // LCOV_EXCL_STOP
    }
    (void)CataReleaseResColPool(resPool);
    QryReleaseAllLabelLatch(stmt->session);
    return ret;
}
#endif

Status QryHandleLastSplitWhenTruncateNsp(QryStmtT *stmt, uint32_t nspId, QryDropNamespaceDescT *desc)
{
    Status ret = GMERR_OK;
#ifdef FEATURE_KV
    CataKeyT cataKey = {0};
    DmKvLabelT *globalKvTable = NULL;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, nspId, GLOBAL_KV_TABLE_NAME);
    ret = CataGetLabelByName(&cataKey, &globalKvTable, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (ret != GMERR_OK) {
        return ret;
    }
    // 给全局kv表加表锁
    ret = QryAcqLatchForKvLabel(stmt, globalKvTable, false);
    if (ret != GMERR_OK) {
        (void)CataReleaseLabel(globalKvTable);
        return ret;
    }
    // 清除namespace中全局Kv表的数据，和刚创建的状态一致
    ret = QryTruncateKvTable(stmt, globalKvTable);
    (void)CataReleaseLabel(globalKvTable);
#endif
    QryReleaseAllLabelLatch(stmt->session);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "truncate global kv table of nspName when truncating: %s.", desc->namespaceName.str);
        // LCOV_EXCL_STOP
        return ret;
    }
    stmt->eof = true;

    // 给客户端返回nspId
    ret = FixBufPutUint32(stmt->session->rsp, nspId);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "put nspId=%" PRIu32 " to rsp when execute truncate nsp.", nspId);
        return ret;
    }

    // 设置状态，表示清理完毕
    (void)CataSetNamespaceState(DbGetInstanceByMemCtx(stmt->session->memCtx), nspId, NSP_NORMAL);
    return GMERR_OK;
}

Status QryHandleMiddleSplitWhenTruncateNsp(QryStmtT *stmt, uint32_t nspId)
{
    QryClearTableInfoT *clearInfo = (QryClearTableInfoT *)stmt->splitInfo.extraInfo;
    switch (clearInfo->state) {
#ifdef FEATURE_KV
        case QRY_CTS_TRUNCATE_KV_TABLE:
            return QryTruncateKvTableInNsp(stmt, nspId, clearInfo);
#endif
        case QRY_CTS_TRUNCATE_EDGE_LABEL:
            return QryTruncateEdgeLabelInNsp(stmt, nspId, clearInfo);
        case QRY_CTS_TRUNCATE_VERTEX_LABEL:
            return QryTruncateVertexLabelInNsp(stmt, nspId, clearInfo);
#ifdef FEATURE_RESPOOL_SRV
        case QRY_CTS_TRUNCATE_RES_POOL:
            return QryTruncateRespoolInNsp(stmt, nspId, clearInfo);
#endif
        case QRY_CTS_CLEANING_YANG_PLAN_CACHE:
            return QryClearYangPlanCacheWhenClearingNsp(nspId, clearInfo);
#ifdef FEATURE_KV
        case QRY_CTS_CLEANING_KV_TABLE:
            return QryDropKvTableWhenClearingNsp(stmt, nspId, clearInfo);
#endif
        case QRY_CTS_CLEANING_EDGE_LABEL:
            return QryDropEdgeLabelWhenClearingNsp(stmt, nspId, clearInfo);
#ifdef FEATURE_RESPOOL_SRV
        case QRY_CTS_UNBIND_RES_POOL:
            return QryUnbindVertexLabelWhenClearingNsp(stmt, nspId, clearInfo);
#endif
        case QRY_CTS_CLEANING_VERTEX_LABEL:
            return QryDropVertexLabelWhenClearingNsp(stmt, nspId, clearInfo);
#ifdef FEATURE_RESPOOL_SRV
        case QRY_CTS_CLEANING_RES_POOL:
            return QryDropRespoolWhenClearingNsp(stmt, nspId, clearInfo);
#endif
        default:
            clearInfo->state++;
            return GMERR_OK;
    }
}

Status QryExecuteTruncateNamespace(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    Status ret = GMERR_OK;
    QryDropNamespaceDescT *desc = (QryDropNamespaceDescT *)stmt->context->entry;
    uint32_t nspId = 0;
    ret = QryCheckAndGetNspIdByName(stmt, desc->namespaceName.str, &nspId);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 每个分片结束都立即释放表锁，因为加锁顺序与插入点边表的顺序相反，如果不释放，可能会造成死锁
    // 如果是第一个分片需要加判断，防止清理正在被清理的表；设置正在被清理的标记位；给分片信息分配资源
    if (stmt->splitInfo.isFirst) {
        return QryHandleFirstSplitWhenClearingNsp(stmt, nspId, NSP_TRUNCATING);
    }

    // 如果是最后一个分片，truncate全局kv表；设置操作结束的标记
    if (((QryClearTableInfoT *)stmt->splitInfo.extraInfo)->state >= QRY_CTS_TRUNCATE_END) {
        ret = QryHandleLastSplitWhenTruncateNsp(stmt, nspId, desc);
        DbDynMemCtxFree(stmt->session->memCtx, stmt->splitInfo.extraInfo);
        stmt->splitInfo.extraInfo = NULL;
        return ret;
    }

    // 任何一个分片（除了第一个和最后一个分片）进行的清表操作
    ret = QryHandleMiddleSplitWhenTruncateNsp(stmt, nspId);
    if (ret != GMERR_OK) {
        (void)CataSetNamespaceState(DbGetInstanceByMemCtx(stmt->session->memCtx), nspId, NSP_TRUNCATE_RETRY);
        DbDynMemCtxFree(stmt->session->memCtx, stmt->splitInfo.extraInfo);
        stmt->splitInfo.extraInfo = NULL;
        return ret;
    }
    return GMERR_OK;
}
#endif

Status QryCheckNspTrxTypeSame(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    if (SeTransGetState(stmt->session->seInstance) == TRX_STATE_NOT_STARTED) {
        return GMERR_OK;
    }
    QryUseNamespaceDescT *desc = (QryUseNamespaceDescT *)stmt->context->entry;
    uint32_t oldNamespaceId = stmt->session->namespaceId;
    DmTrxInfoT oldTrxInfo;
    Status ret = CataGetNspTrxInfoById(oldNamespaceId, &oldTrxInfo, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (ret != GMERR_OK) {
        return ret;
    }
    DmTrxInfoT newTrxInfo;
    if (desc->namespaceId == 0) {
        DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(stmt->session->memCtx);
        ret = CataGetNspTrxInfoByName(dbInstance, desc->namespaceName.str, &newTrxInfo);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get trx info from nsp:%s.", desc->namespaceName.str);  // LCOV_EXCL_LINE
            return ret;
        }
    } else {
        ret = CataGetNspTrxInfoById(desc->namespaceId, &newTrxInfo, DbGetInstanceByMemCtx(stmt->session->memCtx));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get trx info from nsp:%" PRIu32 ".", desc->namespaceId);  // LCOV_EXCL_LINE
            return ret;
        }
    }

    if (!(oldTrxInfo.trxType == newTrxInfo.trxType && oldTrxInfo.isolationLevel == newTrxInfo.isolationLevel)) {
        // LCOV_EXCL_START
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE,
            "change trx type when different trx between nsp and config, oldTrxInfo.trxType=%" PRIu32
            ", isolationLevel=%" PRIu32 "; newTrxInfo.trxType=%" PRIu32 ", isolationLevel=%" PRIu32 ". ",
            (uint32_t)oldTrxInfo.trxType, (uint32_t)oldTrxInfo.isolationLevel, (uint32_t)newTrxInfo.trxType,
            (uint32_t)newTrxInfo.isolationLevel);
        // LCOV_EXCL_STOP
        (void)QryExecuteRollbackTrans(stmt);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

Status QryExecuteUseNamespace(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    // 校验新老namespace的事务类型是否一致
    Status ret = QryCheckNspTrxTypeSame(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    QryUseNamespaceDescT *desc = (QryUseNamespaceDescT *)stmt->context->entry;
    uint32_t newNamespaceId = desc->namespaceId;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(stmt->session->memCtx);
    if (desc->namespaceId == 0) {
        ret = CataGetNamespaceIdByName(dbInstance, desc->namespaceName.str, &newNamespaceId);
        if (ret != GMERR_OK) {
            QryBuildErrorPath4Namespace(stmt, ret, desc->namespaceName.str);
            return ret;
        }
    }

    // 优先处理newNamespace，防止切换到错误的namespace，这样oldNamespace不处理
    ret = CataUseNamespace(newNamespaceId, dbInstance);
    if (ret != GMERR_OK) {
        return ret;
    }

    DmNamespaceT *tmpNsp = NULL;
    ret = CataGetNamespaceById(newNamespaceId, &tmpNsp, dbInstance);
    if (ret != GMERR_OK) {
        (void)CataUnuseNamespace(newNamespaceId, dbInstance);
        return ret;
    }

    uint32_t oldNamespaceId = stmt->session->namespaceId;
    ret = CataUnuseNamespace(oldNamespaceId, dbInstance);
    if (ret != GMERR_OK && ret != GMERR_UNDEFINED_OBJECT) {
        (void)CataReleaseNamespace(dbInstance, tmpNsp);
        (void)CataUnuseNamespace(newNamespaceId, dbInstance);
        QryBuildErrorPath4Namespace(stmt, ret, desc->namespaceName.str);
        return ret;
    }

    // 当前可能为"public" namespace, 而"public" namespace默认为NULL, 因此需要进行判空处理
    if (stmt->session->nsp != NULL) {
        (void)CataReleaseNamespace(dbInstance, stmt->session->nsp);
        stmt->session->nsp = NULL;
    }

    // 确保前面的流程都已经成功后再进行切换
    stmt->session->namespaceId = newNamespaceId;
    stmt->session->nsp = tmpNsp;

    ret = FixBufPutUint32(stmt->session->rsp, newNamespaceId);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_AND_SET_LASERR(ret, "put nspId=%" PRIu32 " to rsp when execute use nsp.", newNamespaceId);
        // LCOV_EXCL_STOP
    }
    return ret;
}

#ifdef __cplusplus
}
#endif
