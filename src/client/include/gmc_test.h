/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * File Name: gmc_test.h
 * Description: interface for testing
 *              Please keep new function in its corresponding position.
 * Author:
 * Create: 2021-7-8
 */

#ifndef GMC_TEST_H
#define GMC_TEST_H

#include "gmc_types.h"
#include "gmc_yang_types.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct GmcClientMemCtxStatInfo {
    uint64_t clientTotalMemSize;
    uint64_t clientcachedMemSize;
    uint64_t connMemSize;
    uint64_t connMsgSize;
    uint64_t stmtMemSize;
    uint64_t stmtOpSize;
} GmcClientMemCtxStatInfoT;

/*
 * @breif 测试专用，用来针对合并状态订阅进行打桩，在共享内存操作区内进行raise操作
 * @param signal
 */
GMC_EXPORT int32_t GmcRaiseForStatusMergeSub(uint32_t signal);

/*
 * @breif 测试专用，停止心跳。注意建立连接后调用此接口停止心跳，需要等待一段时间保证连接上的心跳消息收完，
 *        避免对warning功能测试的干扰。
 * @param conn
 */
GMC_EXPORT void GmcStopHeartbeat(GmcConnT *conn);

/*
 * @breif 用来调整raise和clearwarning的阈值
 * @param stmt
 * @param raiseRatio 新的触发warning阈值，整数，如触发warning阈值为80%，则raiseRatio为80
 * @param clearRatio 新的消除warning阈值，整数，如清除warning阈值为50%，则clearRatio为50
 * @return GMERR_OK - success; otherwise - fail
 */
GMC_EXPORT int32_t GmcChangeAlmThreshold(GmcStmtT *stmt, GmcAlarmIdE alarmId, uint16_t raiseRatio, uint16_t clearRatio);

/*
 * @brief detach all share memory segments, this interface is for testcases
 */
GMC_EXPORT void GmcDetachAllShmSeg(void);

/*
 * @breif
 * 仅供测试用途。
 * 使用场景：测试用例在Euler环境构造服务端多实例反复异常退出后重启，为确保服务端的共享内存资源不泄露，
 * 在客户端侧调用本接口手动清理。
 * 注意：在kill掉服务端前用例进程需要和服务端至少建立一次链接。在kill掉服务端后即可调用本函数清理共享内存。
 * @param instanceId 被kill掉的服务端进程的实例ID，需和启动服务端所用gmserver.ini的instanceId配置项保持一致。
 */
GMC_EXPORT void GmcCltClearShmemResourceByInstance(uint32_t instanceId);

/*
 * @breif
 * 仅供测试用途。
 * 使用场景：测试用例在Euler环境构造服务端多实例反复异常退出后重启，为确保服务端的共享内存资源不泄露，
 * 在客户端侧调用本接口手动清理，同时清理客户端侧对应指定服务端的其它资源。
 * 注意：在kill掉服务端前用例进程需要和服务端至少建立一次链接。在kill掉服务端后即可调用本函数清理共享内存。
 * @param instanceId 被kill掉的服务端进程的实例ID，需和启动服务端所用gmserver.ini的instanceId配置项保持一致。
 */
GMC_EXPORT int32_t GmcUnInitByInstance(uint32_t instanceId);

/*
 * @brief Check whether the specified field value meets the expectation
 * @param node: node handle
 * @param value: expected property name and value
 * @param equal: if value as expected return true, else return false (output parameter)
 * @return GMERR_OK - success; otherwise - fail
 */
GMC_EXPORT int32_t GmcNodeExptFieldValue(const GmcNodeT *node, const GmcPropValueT *value, bool *equal);

GMC_EXPORT int32_t GmcSubGetProperty(GmcStmtT *stmt, GmcSubFetchModeE mode, uint32_t id, void *value, bool *isNull);

GMC_EXPORT int32_t GmcGetClientMemCtxPhySize(GmcConnT *conn, GmcStmtT *stmt, GmcClientMemCtxStatInfoT *memCtxInfo);

GMC_EXPORT int32_t GmcGetClientMemCtxAllocSize(GmcConnT *conn, GmcStmtT *stmt, GmcClientMemCtxStatInfoT *memCtxInfo);

/**
 * @ingroup gmc_graph
 * @par 描述：
 * 将subtree的obj过滤模式下返回的结果树转化为json，便于校验结果
 * @param[in] replyTree subtree过滤返回的结果树
 * @param[out] replyJson 结果树转化为的json
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangTreeToJson(const GmcYangTreeT *replyTree, char **replyJson);
/**
 * @ingroup gmc_graph
 * @par 描述：
 * 将subtree的obj过滤模式下返回的结果树转化为json，并写入指定文件。
 * @param[in] pathname 文件路径
 * @param[in] mode 文件模式(r,w,a,r+,w+,a+)
 * @param[in] replyTree subtree过滤返回的结果树
 * @param[in] jsonFlag 转json参数
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcYangTreeToJsonFile(
    const char *pathname, const char *mode, const GmcYangTreeT *replyTree, uint32_t jsonFlag);

GMC_EXPORT int32_t GmcCheckPrefetchLabelsInCache(GmcStmtT *stmt, const char *labelName, GmcLabelTypeE labelType);

/**
 * @ingroup gmc
 * @par 描述：
 * 给测试查看客户端配置项的current value。
 * @param[in] cfgName 配置项名称
 * @param[in] type 数据类型
 * @param[out] value 客户端配置项的current value
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcGetCltCfg(const char *cfgName, GmcDataTypeE type, void *value);

/**
 * @ingroup gmc
 * @par 描述：
 * 给测试查看客户端batch句柄的option。
 * @param[in] batch batch句柄
 * @param[out] option option值
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcGetBatchOption(GmcBatchT *batch, GmcBatchOptionT *option);

/**
 * @ingroup get MsgSendTime generated at server
 * @param[in] stmt : stmt handler
 * @param[out] serverMsgSendTime : MsgSendTime getted by this pointer
 * @return GMERR_OK when SUCCEESS else not
 */
GMC_EXPORT int32_t GmcGetServerMsgSendTime(GmcStmtT *stmt, uint64_t *serverMsgSendTime);

/*
 * @ingroup get current time in rdtsc
 * @return current time in rdtsc
 */
GMC_EXPORT uint64_t GmcGetGlobalRdtsc(void);

/*
 * @ingroup convert cycles in rdtsc to microsecond
 */
GMC_EXPORT uint64_t GmcToUseconds(uint64_t cycles);

/**
 * @ingroup gmc
 * @par 描述：
 * 测试接口, 查询是否与服务端共进程
 */
GMC_EXPORT bool GmcIsSameProcess(void);

/**
 * @ingroup gmc
 * @par 描述：
 * 测试接口, 查询是否满足远端访问的条件（没attach服务端共享内存）
 * @param[in] instanceId 服务端ID，用于构造共享内存key
 */
GMC_EXPORT bool GmcIsRemoteClient(uint32_t instanceId);

GMC_EXPORT int32_t GmServerMain(int32_t argc, char *argv[], bool isLoopInside);

GMC_EXPORT void GmcEnableRegAdaptFuncs(void);

/**
 * @ingroup gmc
 * @par 描述：
 * 强行卸载Datalog的so，不校验表上的订阅是否都已经取消。
 * @param[in] stmt
 * @param[in] soName 要卸载的so名称
 * @param[in] namespaceName 要卸载的so所属的namespace名称，传入null等效于传入"public"。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcForceUnimportDatalogSo(GmcStmtT *stmt, const char *soName, const char *namespaceName);

/*
 * @breif 测试专用，客户收消息时候会校验 sessionId，这个接口更改客户端储存的sessionId
 * @param conn
   @param newSessionId 新的sessionId
 */
GMC_EXPORT void GmcModifyCliSessionId(GmcConnT *conn, unsigned char newSessionId[26]);

/**
 * @breif 测试专用，调用该接口将会触发服务端锁库
 * @param stmt
 */
GMC_EXPORT int32_t GmcSetDbEmergency(GmcStmtT *stmt);

#ifdef __cplusplus
}
#endif

#endif /* GMC_TEST_H */
