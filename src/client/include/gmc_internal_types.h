/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_internal_types.h
 * Description: File provide GMC internal types.
 *              Please keep new types in its corresponding position.
 * Author:
 * Create: 2021-8-11
 */

#ifndef GMC_INTERNAL_TYPES_H
#define GMC_INTERNAL_TYPES_H

#include "gmc_errno.h"
#include "stdint.h"
#include "db_text.h"

#ifdef __cplusplus
extern "C" {
#endif

/* direction for edge */
#define GMC_EDGE_DIRECTION_NONE 0
#define GMC_EDGE_DIRECTION_IN 1
#define GMC_EDGE_DIRECTION_OUT 2
#define GMC_EDGE_DIRECTION_BOTH 3

/* write op time cost */
#define CLT_EXECUTE_TIME_STAT_LENGTH 12   // 12个桶
#define CLT_EXECUTE_TIME_STAT_PERIOD 300  // 大小s，代表一个桶的统计周期大小

#ifdef FEATURE_GQL
#define USER_METADATA_FILED_NUM 11  // 要与dm_meta_basic.h中USER_META_TYPE_NUM_N一致
#else
#define USER_METADATA_FILED_NUM 9  // 要与dm_meta_basic.h中USER_META_TYPE_NUM_N一致
#endif

#define USER_MAX_CONN_NUMBER (MAX_CONN_NUM - EMRG_CONN_NUM)

#define CLT_BATCH_CMD_MAX_SIZE 1024u

typedef enum {
    GMC_CREATE_PRIV = 0x00000001,      // 针对所有类型
    GMC_DROP_PRIV = 0x00000002,        // 针对所有类型
    GMC_ALTER_PRIV = 0x00000004,       // 针对所有类型
    GMC_GET_PRIV = 0x00000008,         // 针对所有类型
    GMC_GRANT_PRIV = 0x00000010,       // 针对role
    GMC_REVOKE_PRIV = 0x00000020,      // 针对role
    GMC_BIND_PRIV = 0x00000040,        // 针对resource
    GMC_UNBIND_PRIV = 0x00000080,      // 针对resource
    GMC_INSERTANY_PRIV = 0x00000100,   // 对所有表有插入权限
    GMC_DELETEANY_PRIV = 0x00000200,   // 对所有表有删除权限
    GMC_UPDATEANY_PRIV = 0x00000400,   // 对所有表有更新权限
    GMC_SELECTANY_PRIV = 0x00000800,   // 对所有表有查询权限
    GMC_REPLACEANY_PRIV = 0x00001000,  // 对所有表有替换权限
    GMC_MERGEANY_PRIV = 0x00002000,    // 对所有表有合并权限
    GMC_TRUNCATE_PRIV = 0x00004000,    // 暂时仅支持vertex label 和 kvtable
    GMC_USE_PRIV = 0x00008000,         // 针对namespace
    GMC_OPEN_PRIV = 0x00010000,        // 针对binary file
    GMC_CLOSE_PRIV = 0x00020000,       // 针对binary file
    GMC_INVOKEANY_PRIV = 0x00040000,   // 针对datalog udf对象
} GmcSystemPrivsTypeE;

typedef enum {
    GMC_VERTEX_LABEL,
    GMC_KV_TABLE,
    GMC_DATALOG_UDF,
    GMC_NAMESPACE,
    GMC_ROLE,
    GMC_USER,
    GMC_TABLESPACE,
    GMC_EDGE_LABEL,
    GMC_RESOURCE,
    GMC_LABEL_SUBS,
    GMC_PATH_SUBS,
    GMC_BINARY_FILE,
    GMC_OBJ_TYPE_NUM,
} GmcObjectTypeE;

typedef enum {
    GMC_OBJ_SELECT_PRIV = 0x0001,
    GMC_OBJ_INSERT_PRIV = 0x0002,
    GMC_OBJ_UPDATE_PRIV = 0x0004,
    GMC_OBJ_DELETE_PRIV = 0x0008,
    GMC_OBJ_REPLACE_PRIV = 0x0010,
    GMC_OBJ_MERGE_PRIV = 0x0020,
    GMC_OBJ_TRUNCATE_IN_NSP_PRIV = 0x0040,  // 针对nsp中可包含的元数据对象
    GMC_OBJ_CREATE_IN_NSP_PRIV = 0x0080,    // 针对nsp中可包含的元数据对象
    GMC_OBJ_DROP_IN_NSP_PRIV = 0x0100,      // 针对nsp中可包含的元数据对象
    GMC_OBJ_GET_IN_NSP_PRIV = 0x0200,       // 针对nsp中可包含的元数据对象
    GMC_OBJ_ALTER_IN_NSP_PRIV = 0x0400,     // 针对nsp中可包含的元数据对象
    GMC_OBJ_USE_NSP_PRIV = 0x0800,          // 针对nsp中可包含的元数据对象
    GMC_OBJ_INVOKE_PRIV = 0x1000,           // 针对datalog udf 对象
    GMC_OBJ_VALIDATION = 0x2000,            // 只针对 nsp 对象
} GmcObjectPrivilegeE;

/**
 * @ingroup gmc_internal_types
 * ips工具使用，接受或拒绝一个进程
 * 需要和服务端的DrtIpsOperationTypeE保持一致
 */
typedef enum {
    GMC_IPS_REJECT_PROCESS = 0x0001, /**< ips拒绝一个进程 */
    GMC_IPS_ACCEPT_PROCESS = 0x0002, /**< ips接受一个进程 */
    GMC_IPS_OPERATION_BUTT = 0x0003, /**< ips无效的操作类型 */
} GmcIpsOperationTypeE;

typedef enum {
    GMC_PRIV_PROTOCOL_TYPE_DIRECT_READ = 0x0001,
    GMC_PRIV_PROTOCOL_TYPE_BUTT = 0x0002,
} GmcPrivProtocolTypeE;

typedef struct GmcObjectPrivs {
    const char *nameSpaceName;
    const char *objName;
    GmcObjectTypeE objType;
    GmcObjectPrivilegeE privs;
    bool isAtomic;
    bool toUser;
} GmcObjectPrivsT;

typedef struct GmcSystemPrivs {
    const char *userOrGrpName;
    const char *processName;
    GmcSystemPrivsTypeE opType;
    GmcObjectTypeE objectType;
    bool isAtomic;
    bool toUser;
} GmcSystemPrivsT;

/**
 * @ingroup gmc_internal_types
 * 通信权限相关的结构体。
 */
typedef struct {
    GmcPrivProtocolTypeE protocolType;
    const char *userName;
    const char *processName;
    bool isEnable;
} GmcPrivUserProtocolT;

typedef struct GmcMemSizeInfo {
    uint64_t objectNum;
    uint64_t objectSize;
    uint64_t indexTotalSize;
    uint64_t uniqueHashSize;
    uint64_t nonUniqueHashSize;
    uint64_t hashclusterSize;
    uint64_t lpmSize;
    uint64_t localkeySize;
    uint64_t totalSize;
} GmcMemSizeInfoT;

// userMetadataMaxNumArray二维数组每行按照以下顺序读取
// normal_vertexlabel_max
// yang_vertexlabel_max
// kv_label_max
// subscription_max
// resource_max
// namespace_max
// tablespace_max
// edge_max
// udf_max
typedef struct {
    uint16_t userNum;
    const char **userNames;
    const char **processNames;
    uint16_t *reservedConnNumArray;
    uint16_t (*userMetadataMaxNumArray)[USER_METADATA_FILED_NUM];
    bool isAtomic;
} GmcUsersCreateInfoT;

typedef struct {
    const char *groupName;
    const char *processName;
    uint16_t groupMetadataMaxNumArray[USER_METADATA_FILED_NUM];
    bool isAtomic;
} GmcGroupCreateInfoT;

typedef struct {
    uint16_t successNum;  // 输出创建成功的用户数
    uint16_t repeatNum;   // 输出重复创建的用户数
} GmcUsersCreateResultT;

typedef enum {
    GMC_USER_MAX_CONN_NUMBER, /**< ips限制单个用户的最大连接数量,范围[1, 1022] */
    GMC_USER_MAX_CONN_SPEED,  /**< ips限制单个用户的unit time的建连次数, 范围(0, uint32_max) */
} GmcIpsUserResTypeE;

typedef enum {
    GMC_CONN_MAX_MESSAGE_BUF, /**< ips限制单个连接服务端接收报文的大小, 范围[256, 2048] */
    GMC_CONN_MAX_MEMCTX_SIZE, /**< ips限制单个连接的通信memCtx的大小*/
} GmcIpsConnResTypeE;

typedef struct {
    GmcIpsUserResTypeE resType;
    const char *userName;
    const char *processName;
    uint32_t value;  // unit count
} GmcIpsUserResT;

typedef struct {
    GmcIpsConnResTypeE resType;
    uint16_t connId;
    uint32_t value;  // unit：kbyte
} GmcIpsConnResT;

typedef struct {
    TextT configName;
    TextT configCurrentValue;
    TextT configSafeValue;
    bool isSafe;
} GmcIdsConfigDetectResT;

typedef struct {
    const char *processName;
    uint32_t logCtrlValue;  // 日志控制项值，可表示日志级别或者日志开关
    uint32_t duration;      // unit: second
    uint64_t startTime;     // cpu cycle
} GmcLogCtrlInfoT;

typedef enum {
    GMC_DW_PUBSUB_SEND_DATA = 0,  // 直连写旧订阅发送数据
    GMC_DW_PUBSUB_STMG_NOTIFY,    // 直连写状态合并订阅通知订阅者
    GMC_DW_PUBSUB_STMG_GC,        // 直连写状态合并订阅通知回收
} GmcDwPubSubOperationE;

/**
 * @brief 使用信号接管保护内存操作的业务类型，自行添加后请在CltWaitOperShmEnd添加日志
 */
typedef enum DbSigBusinessType {
    SHMEM_OP_STMG_SUB,
    SHMEM_OP_DIRECT_WRITE,
    SHMEM_OP_WARM_REBOOT,
    SHMEM_OP_BUTT,
} ShmemOperateTypeE;

#ifdef __cplusplus
}
#endif

#endif /* GMC_INTERNAL_TYPES_H */
