/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_privilege.h
 * Description: Interface of gmdb priv.
 * Author: xuyingyu
 * Create: 2021-6-11
 */

#ifndef GMC_PRIVILEGE_H
#define GMC_PRIVILEGE_H

#include "gmc_types.h"
#include "gmc_internal_types.h"
#include "adpt_define.h"
#include "adpt_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/*
 * @brief Grant object priv to a db user or a db group
 * @param stmt: client handle object
 * @param userOrGrpName: the os user or group name, must end with '\0'
 * @param processName: the process name, must end with '\0'
 * @param objectType: the type of object
 * @return GMERR_OK - success; otherwise - fail
 */
GMDB_EXPORT Status GmcGrantObjectPrivs(
    GmcStmtT *stmt, const char *userOrGrpName, const char *processName, const GmcObjectPrivsT *objectPrivs);

/*
 * @brief Revoke object priv from a db user or a db group
 * @param stmt: client handle object
 * @param userOrGrpName: the os user or group name, must end with '\0'
 * @param processName: the process name, must end with '\0'
 * @param objectType: the type of object
 * @return GMERR_OK - success; otherwise - fail
 */
GMDB_EXPORT Status GmcRevokeObjectPrivs(
    GmcStmtT *stmt, const char *userOrGrpName, const char *processName, const GmcObjectPrivsT *objectPrivs);

/*
 * @brief Create multiple db users
 * @param stmt: client handle object
 * @param usersInfo: users' basic information including:
 *        userNum: the number of users to be created, must less than or equal to 1024
 *        userName: the array of os unames, and each name must end with '\0'
 *        processName: the array of process names, and each name must end with '\0'
 *        reservedConnNumArray: the array of server reserved connections for each user
 * @param result: users' creating result information including:
 *        succssNum: successfully created users number
 *        failNum: the number of users unable to be created
 *        repeatNum: the number of users which already exist
 * @return GMERR_OK - success; otherwise - fail
 */
GMDB_EXPORT Status GmcCreateUsers(GmcStmtT *stmt, GmcUsersCreateInfoT *usersInfo, GmcUsersCreateResultT *result);

/*
 * @brief Drop a db user
 * @param stmt: client handle object
 * @param fileType: parse the strValue as process name or pwd
 * @param userName: the os uname, must end with '\0'
 * @param processName: the process name, must end with '\0'
 * @return GMERR_OK - success; otherwise - fail
 */
GMDB_EXPORT Status GmcDropUser(GmcStmtT *stmt, const char *userName, const char *processName);

/*
 * @brief Create a db group
 * @param stmt: client handle object
 * @param groupInfo: groups' basic information including:
 *        groupName: group name, must end with '\0'
 *        processName: process name, must end with '\0'
 *        isAtomic: is rollback mode or not
 *        groupMetadataMaxNumArray: the array of server reserved connections for group
 * @return GMERR_OK - success; otherwise - fail
 */
GMDB_EXPORT Status GmcCreateGroup(GmcStmtT *stmt, GmcGroupCreateInfoT *groupInfo);

/*
 * @brief Drop a db group
 * @param stmt: client handle object
 * @param userName: the os group name, must end with '\0'
 * @param processName: the process name, must end with '\0'
 * @return GMERR_OK - success; otherwise - fail
 */
GMDB_EXPORT Status GmcDropGroup(GmcStmtT *stmt, const char *groupName, const char *processName);

/*
 * @brief Grant system priv to a db user or a db group
 * @param stmt: client handle object
 * @param userName: the os group name, must end with '\0'
 * @param processName: the process name, must end with '\0'
 * @param systemPrivsType: the type of system priv
 * @param objectType: the type of object
 * @return GMERR_OK - success; otherwise - fail
 */
GMDB_EXPORT Status GmcGrantSystemPrivs(GmcStmtT *stmt, GmcSystemPrivsT *sysPrivs);

/*
 * @brief Revoke system priv from a db user or a db group
 * @param stmt: client handle object
 * @param userName: the os group name, must end with '\0'
 * @param processName: the process name, must end with '\0'
 * @param systemPrivsType: the type of system priv
 * @param objectType: the type of object
 * @return GMERR_OK - success; otherwise - fail
 */
GMDB_EXPORT Status GmcRevokeSystemPrivs(GmcStmtT *stmt, GmcSystemPrivsT *sysPrivs);

/*
 * @brief ips reject or accept a process
 * @param stmt: client handle object
 * @param ipsOp: the ips operation for reject or accept
 * @param pid: process id
 * @return GMERR_OK - success; otherwise - fail
 */
GMDB_EXPORT Status GmcIpsRejectOrAcceptProcess(GmcStmtT *stmt, GmcIpsOperationTypeE ipsOp, uint32_t pid);

/*
 * @brief ips reject or accept a protocal, for now it is only supported for direct read
 * @param stmt: client handle object
 * @param PrivUserProtocolT: the related infos
 * @return GMERR_OK - success; otherwise - fail
 */
GMDB_EXPORT Status GmcIpsSetUserProtocol(GmcStmtT *stmt, GmcPrivUserProtocolT *userProtocol);

#ifdef __cplusplus
}
#endif

#endif /* GMC_PRIVILEGE_H */
