/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_kv_check.h
 * Description: header file for GMDB client gmc kv check
 * Create: 2021-9-3
 */

#ifndef GMC_KV_CHECK_H
#define GMC_KV_CHECK_H

#include "gmc_types.h"
#include "adpt_define.h"
#include "adpt_types.h"

#ifdef __cplusplus
extern "C" {
#endif

Status GmcKvDDLCheck(GmcStmtT *stmt, const char *kvTableName, GmcConnTypeE connType);
Status GmcKvCreateCheck(GmcStmtT *stmt, const char *kvTableName, const char *config<PERSON>son, GmcConnTypeE connType);
Status GmcKvPrepareStmtByLabelNameCheck(GmcStmtT *stmt, const char *kvTableName);

Status GmcKvSetCheck(GmcStmtT *stmt, const void *key, uint32_t keyLen, const void *value, uint32_t valueLen);
Status GmcKvSetAsyncCheck(GmcStmtT *stmt, const GmcKvTupleT *kvTuple);
Status GmcKvRemoveCheck(GmcStmtT *stmt, const void *key, uint32_t keyLen);
Status GmcKvRemoveAsyncCheck(GmcStmtT *stmt, const void *key, uint32_t keyLen);

Status GmcKvGetCheck(GmcStmtT *stmt, const void *key, uint32_t keyLen, const void *value, const uint32_t *valueLen);
Status GmcKvGetValueSizeCheck(GmcStmtT *stmt, const void *key, uint32_t keyLen, const uint32_t *valueSize);
Status GmcKvIsExistCheck(GmcStmtT *stmt, const void *key, uint32_t keyLen, const bool *isExist);
Status GmcKvTableRecordCountCheck(GmcStmtT *stmt, const uint32_t *count);
Status GmcKvScanCheck(const GmcStmtT *stmt);

Status GmcKvGetFromStmtCheck(
    GmcStmtT *stmt, void *const *key, const uint32_t *keyLen, void *const *value, const uint32_t *valueLen);
Status GmcKvInputToStmtCheck(GmcStmtT *stmt, const void *key, uint32_t keyLen, const void *value, uint32_t valueLen);

#ifdef __cplusplus
}
#endif

#endif
