/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_kv_check.c
 * Description: realization of check kv interface of gmdb
 * Create: 2021-09-03
 */

#include "gmc_kv_check.h"
#include "clt_check.h"
#include "clt_error.h"

inline static Status KvKeyCheck(const void *key, uint32_t keyLen)
{
    if (SECUREC_UNLIKELY(key == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "key");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (SECUREC_UNLIKELY(keyLen == 0 || keyLen > MAX_KEY_LEN)) {
        static_assert(MAX_KEY_LEN == 512, "Please edit last wrong message below");
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "The key len should be larger than 0 and small or equal to 512");
        return GMERR_INVALID_VALUE;
    }
    return GMERR_OK;
}

inline static Status KvValueCheck(const void *value, uint32_t valueLen)
{
    if (SECUREC_UNLIKELY(value == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "value");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (SECUREC_UNLIKELY(valueLen == 0 || valueLen > MAX_VALUE_LEN)) {
        static_assert(MAX_VALUE_LEN == 1024 * 1024, "Please edit last wrong message below");
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "The val len should be larger than 0 and smaller than 1048577");
        return GMERR_INVALID_VALUE;
    }
    return GMERR_OK;
}

Status GmcKvPrepareStmtByLabelNameCheck(GmcStmtT *stmt, const char *kvTableName)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    GmcConnTypeE connType = stmt->conn->connType;
    if (connType != GMC_CONN_TYPE_SYNC && connType != GMC_CONN_TYPE_ASYNC) {
        DB_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "operation for given conn type.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    ret = CltCheckStringValid(kvTableName, MAX_TABLE_NAME_LEN, "kvTableName");
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status GmcKvDDLCheck(GmcStmtT *stmt, const char *kvTableName, GmcConnTypeE connType)
{
    Status ret = CltStmtBasicCheck(stmt, connType);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(kvTableName, MAX_TABLE_NAME_LEN, "kvTableName");
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status GmcKvCreateCheck(GmcStmtT *stmt, const char *kvTableName, const char *configJson, GmcConnTypeE connType)
{
    Status ret = GmcKvDDLCheck(stmt, kvTableName, connType);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltCheckConfigJsonLength(configJson);
}

static Status GmcKvDMLCheck(const GmcStmtT *stmt, GmcConnTypeE connType)
{
    Status ret = CltStmtBasicCheck(stmt, connType);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = CltCheckStmtType(stmt, CLT_STMT_TYPE_KV);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

Status GmcKvSetCheck(GmcStmtT *stmt, const void *key, uint32_t keyLen, const void *value, uint32_t valueLen)
{
    Status ret = GmcKvDMLCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = KvKeyCheck(key, keyLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = KvValueCheck(value, valueLen);
    return ret;
}

Status GmcKvSetAsyncCheck(GmcStmtT *stmt, const GmcKvTupleT *kvTuple)
{
    Status ret = GmcKvDMLCheck(stmt, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (kvTuple == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "kv tuple");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    ret = KvKeyCheck(kvTuple->key, kvTuple->keyLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = KvValueCheck(kvTuple->value, kvTuple->valueLen);
    return ret;
}

Status GmcKvRemoveCheck(GmcStmtT *stmt, const void *key, uint32_t keyLen)
{
    Status ret = GmcKvDMLCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = KvKeyCheck(key, keyLen);
    return ret;
}

Status GmcKvRemoveAsyncCheck(GmcStmtT *stmt, const void *key, uint32_t keyLen)
{
    Status ret = GmcKvDMLCheck(stmt, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = KvKeyCheck(key, keyLen);
    return ret;
}

Status GmcKvGetCheck(GmcStmtT *stmt, const void *key, uint32_t keyLen, const void *value, const uint32_t *valueLen)
{
    Status ret = GmcKvDMLCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = KvKeyCheck(key, keyLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (value == NULL || valueLen == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "val or val size");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

Status GmcKvGetValueSizeCheck(GmcStmtT *stmt, const void *key, uint32_t keyLen, const uint32_t *valueSize)
{
    Status ret = GmcKvDMLCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = KvKeyCheck(key, keyLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (valueSize == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "val size");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

Status GmcKvIsExistCheck(GmcStmtT *stmt, const void *key, uint32_t keyLen, const bool *isExist)
{
    Status ret = GmcKvDMLCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = KvKeyCheck(key, keyLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (isExist == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "isExist");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

Status GmcKvTableRecordCountCheck(GmcStmtT *stmt, const uint32_t *count)
{
    Status ret = GmcKvDMLCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (count == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "count");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

Status GmcKvScanCheck(const GmcStmtT *stmt)
{
    return GmcKvDMLCheck(stmt, GMC_CONN_TYPE_SYNC);
}

Status GmcKvGetFromStmtCheck(
    GmcStmtT *stmt, void *const *key, const uint32_t *keyLen, void *const *value, const uint32_t *valueLen)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (stmt->stmtType != CLT_STMT_TYPE_KV && stmt->stmtType != CLT_STMT_TYPE_SUB_KV) {
        DB_SET_LASTERR(
            GMERR_WRONG_STMT_OBJECT, "Curr stmt type is %d, need kv or subscribe kv action.", (int32_t)stmt->stmtType);
        return GMERR_WRONG_STMT_OBJECT;
    }
    if (key == NULL || keyLen == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "key or key size");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (value == NULL || valueLen == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "value or value size");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

Status GmcKvInputToStmtCheck(GmcStmtT *stmt, const void *key, uint32_t keyLen, const void *value, uint32_t valueLen)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStmtType(stmt, CLT_STMT_TYPE_KV);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = KvKeyCheck(key, keyLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (value == NULL && valueLen == 0) {
        // 批量删除操作不用value
        return GMERR_OK;
    }
    ret = KvValueCheck(value, valueLen);
    return ret;
}
