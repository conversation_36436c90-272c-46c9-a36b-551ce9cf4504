/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: clt_graph_dml_vertex.c
 * Description: Implement of GMDB client graph vertex dml API
 * Author: zhaodu
 * Create: 2020-08-03
 */

#include "clt_catalog_sub.h"
#include "clt_cond_filter.h"
#include "clt_dml_kv.h"
#include "clt_proto.h"
#include "adpt_define.h"
#include "db_msg_buffer.h"
#include "db_rpc_msg_op.h"
#include "dm_data_kv.h"
#include "ds_concurrency_control.h"
#include "clt_da_read.h"
#include "se_trx.h"

static void ExecDsKvScanInitFetchArgs(
    GmcStmtT *stmt, ProtoHeadT *proto, DsFetchArgsT *dsFetchArgs, StructureCtxT *structureCtx)
{
    KvProtoT *kvProto = (KvProtoT *)proto;
    dsFetchArgs->kvScanArgs.kvLabel = kvProto->kvLabel;
    dsFetchArgs->kvScanArgs.kvIndexKey = &kvProto->indexKey;
    dsFetchArgs->preFetchRows = stmt->preFetchRows;
    dsFetchArgs->isDirectGetCount = false;

    if (proto->opCode == MSG_OP_RPC_SCAN_KV_BEGIN) {
        dsFetchArgs->scanMode = DS_SCAN_KV_SEQUENCE;
        dsFetchArgs->kvScanArgs.kvLabel = kvProto->kvLabel;
    } else {
        dsFetchArgs->scanMode = DS_SCAN_KV_INDEX;
        // userBuf是NULL填充userbufLen
        structureCtx->userBuf = (uint8_t *)(uintptr_t)kvProto->value;
        structureCtx->userbufLen = kvProto->valueLen;
        structureCtx->vertexOffset = 0;
        structureCtx->isUserBuffer = true;
        // ds直接拷贝读取结果
    }
}

Status ExecDsKvScan(GmcStmtT *stmt, ProtoHeadT *proto)
{
    DrRunCtxT *drRunCtx = &stmt->drRunCtx;
    SeRunCtxHdT seRunCtx = drRunCtx->base.seRunCtxHandle;
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    StructureCtxT *structureCtx = &drRunCtx->structureCtx;
    DsFetchArgsT *dsFetchArgs = &(drRunCtx->dsFetchArgs);
    ExecDsKvScanInitFetchArgs(stmt, proto, dsFetchArgs, structureCtx);
    drRunCtx->directReadBuff = &stmt->recvPack;
    FixBufResetMem(drRunCtx->directReadBuff);
    Status ret =
        DsAcqLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->labelLatchVersionId, drRunCtxBase->sessionCtx);
    if (ret != GMERR_OK) {
        return ret;
    }

    bool trxOpen = SeTransStartDirectReadCheck(seRunCtx);
    ret = CltChangeSeTransStateBeforeRead(trxOpen, seRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DsReleaseLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->sessionCtx);
        return ret;
    }

    ret = DirectReadCtxOpen(drRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT;
    }
    ret = MainStoreReadFetchNext(drRunCtx, &stmt->cacheRows, &stmt->eof);
    CltChangeSeTransStateAfterRead(trxOpen, seRunCtx);
    DsReleaseLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->sessionCtx);
    if (stmt->eof) {
        DirectReadCtxClose(drRunCtx, DIRECT_ACCESS_CLOSE_HEAP_CURSOR);
    }
    structureCtx->isUserBuffer = false;
    return ret;
EXIT:
    CltChangeSeTransStateAfterRead(trxOpen, seRunCtx);
    DsReleaseLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->sessionCtx);
    structureCtx->isUserBuffer = false;
    // heap懒加载导致，写入数据前直连读校验返回
    if (ret == GMERR_NO_DATA) {
        stmt->cacheRows = 0;
        stmt->eof = true;
        ret = GMERR_OK;
    }
    return ret;
}

Status CltFetchKvInner(GmcStmtT *stmt, CltOperKvT *operKv, bool *eof)
{
    DB_ASSERT(*eof == false);
    // 先清除kv，这样如果后续发现eof，外部拿到的kv为空
    (void)DmClearKv(operKv->kv);
    Status ret;
    while (stmt->cacheRows == 0) {
        if (stmt->eof) {
            *eof = true;
            return GMERR_OK;
        }
        ProtoHeadT proto = {};
        proto.opCode = MSG_OP_RPC_FETCH_CYPHER;
        if (operKv->fetchType == CLT_FETCH_VERTEX_DIRECT_READ) {
            ret = CltSendDirectReadMsg(stmt, &proto);
        } else {
            ret = CltStmtSendAndRecvScanMsg(stmt, &proto);
        }
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "kv fetch send msg");
            return ret;
        }
    }
    TextT data;
    ret = FixBufGetObject(&stmt->recvPack, &data);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "kvBuf in CltFetchKvFromStmt.");
        return ret;
    }
    ret = DmDeSerialize2ExistsKv((void *)data.str, data.len, operKv->kv);
    if (ret != GMERR_OK) {
        return ret;
    }
    stmt->cacheRows--;
    return ret;
}

__attribute__((noinline)) Status SubFetchScanKv(CltSubscriptionT *sub, bool *eof)
{
    if (sub->fetchCount == 1) {
        --sub->fetchCount;
        *eof = false;
        return GMERR_OK;
    }
    *eof = true;
    return GMERR_OK;
}

Status CltFetchKv(GmcStmtT *stmt, bool *eof)
{
    DB_ASSERT(*eof == false);
    CltOperKvT *operKv = CltGetOperationContext(stmt);
    if (SECUREC_UNLIKELY(operKv->sub != NULL)) {
        return SubFetchScanKv(operKv->sub, eof);
    }
    return CltFetchKvInner(stmt, operKv, eof);
}
