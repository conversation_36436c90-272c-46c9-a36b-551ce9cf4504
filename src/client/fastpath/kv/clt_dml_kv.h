/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: clt_graph_dml_vertex.h
 * Description: header file for GMDB client graph vertex dml API
 * Author: zhaodu
 * Create: 2020-08-04
 */

#ifndef CLT_DML_KV_H
#define CLT_DML_KV_H

#include "clt_stmt.h"

#ifdef __cplusplus
extern "C" {
#endif

Status CltFetchKv(GmcStmtT *stmt, bool *eof);
Status ExecDsKvScan(GmcStmtT *stmt, ProtoHeadT *proto);

inline static bool IsKvNotCsScan(const GmcStmtT *stmt, ProtoHeadT *protoObj)
{
    return stmt->conn->transMode != GMC_TRANS_USED_IN_CS && stmt->conn->openDirectRead;
}

#ifdef __cplusplus
}
#endif

#endif /* CLT_GRAPH_DML_KV_H */
