/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: clt_resource_pool.c
 * Description: interface implementation of resource pool of internal client
 * Author:
 * Create: 2023-06-06
 */

#include "clt_resource_pool.h"
#include "clt_da_write.h"

/******************************************* 内部check函数实现 *****************************/
Status GmcCreateResPoolCheck(GmcStmtT *stmt, const char *resPoolJson)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(resPoolJson, MAX_RESPOOL_FILE_SIZE, "resPoolJson");
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status GmcResPoolBasicCheck(GmcStmtT *stmt, const char *resPoolName)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltCheckStringValid(resPoolName, MAX_RES_POOL_NAME_LEN, "resPoolName");
}

Status GmcResPoolOfLabelCheck(GmcStmtT *stmt, const char *vertexLabel)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(vertexLabel, MAX_TABLE_NAME_LEN, "labelName");
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}
Status GmcGetResPoolCheck(GmcStmtT *stmt, const char *resPoolName, char *const *resPoolJson)
{
    Status ret = GmcResPoolBasicCheck(stmt, resPoolName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltPtrCheckWithErr(resPoolJson);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

// 仅给GmcGetResIdNum和GmcGetResIdInfo做检查使用
Status GmcGetResIdCheck(GmcStmtT *stmt)
{
    // check whether stmt type is vertex
    Status ret = CltCheckStmtType(stmt, CLT_STMT_TYPE_VERTEX);
    if (ret != GMERR_OK) {
        return ret;
    }

    // merge以上操作之后不能进行GmcGetResIdNum和GmcGetResIdInfo
    if (stmt->operationType > GMC_OPERATION_MERGE || stmt->operationType == GMC_OPERATION_DELETE) {
        DB_LOG_AND_SET_LASERR(
            GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "operationType:%" PRId32 ".", (int32_t)stmt->operationType);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }

    uint32_t opCode;
    if (stmt->lastOpStatus.lastOpDirectWrite) {  // 直连写适配
        opCode = (uint32_t)GetDwOpTypeToRpcOpCode(stmt->lastOpStatus.lastOpType);
    } else {
        // 将错误使用GmcGetResIdNum和GmcGetResIdInfo导致的fixbuffer问题由coredump改为返回错误码
        FixBufferT *fixBuffer = &stmt->recvPack;
        uint32_t offset = MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE + sizeof(uint32_t);
        if (FixBufGetPos(fixBuffer) < offset) {
            DB_LOG_AND_SET_LASERR(
                GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "fixBuffer whose pos is %" PRIu32 ".", FixBufGetPos(fixBuffer));
            return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
        }
        const FixBufferT *buffer = &stmt->recvPack;
        const OpHeaderT *op = ProtocolPeekFirstOpHeader(buffer);
        opCode = op->opCode;
    }

    switch (opCode) {
        case MSG_OP_RPC_INSERT_VERTEX:
        case MSG_OP_RPC_REPLACE_VERTEX:
        case MSG_OP_RPC_MERGE_VERTEX:
        case MSG_OP_RPC_UPDATE_VERTEX: {
            return GMERR_OK;
        }
        default:
            DB_LOG_AND_SET_LASERR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "Previous command not dml");
            return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
}

Status GmcGetResIdInfoCheck(GmcStmtT *stmt, const uint64_t *resIdBuf, const uint32_t *resIdNum)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltPtr2CheckWithErr(resIdBuf, resIdNum);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 这里和GmcGetResIdNum做同样的检查
    ret = GmcGetResIdCheck(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

/******************************************* 内部BuildRequest函数实现 *****************************/
Status CreateResPoolBuildRequest(FixBufferT *req, const void *in)
{
    FillSimpleStmtMsgHeader(req, MSG_OP_RPC_CREATE_RES_POOL);
    const char *content = in;
    return HelpFillString(req, true, content);
}

Status GetResPoolBuildRequest(FixBufferT *req, const void *in)
{
    FillSimpleStmtMsgHeader(req, MSG_OP_RPC_GET_RES_POOL);
    const char *content = in;
    return HelpFillString(req, true, content);
}

Status GetResPoolParseResponse(FixBufferT *rsp, void *out)
{
    return FixBufGetObject(rsp, out);
}

Status DestroyResPoolBuildRequest(FixBufferT *req, const void *in)
{
    FillSimpleStmtMsgHeader(req, MSG_OP_RPC_DROP_RES_POOL);
    const char *content = in;
    return HelpFillString(req, true, content);
}

Status CreateBindResPoolBuildRequest(FixBufferT *req, const void *in)
{
    FillSimpleStmtMsgHeader(req, MSG_OP_RPC_BIND_RES_POOL_TO_VERTEX_LABEL);
    const CltBindResPoolCfgT *config = in;
    const uint32_t strNum = 3;
    return HelpFillMultiString(req, false, strNum, config->resPoolName, config->bindName, config->extraName);
}

Status DestroyBindResPoolBuildRequest(FixBufferT *req, const void *in)
{
    FillSimpleStmtMsgHeader(req, MSG_OP_RPC_UNBIND_RES_POOL_TO_VERTEX_LABEL);
    const CltBindResPoolCfgT *config = in;
    const uint32_t strNum = 3;
    return HelpFillMultiString(req, false, strNum, config->resPoolName, config->bindName, config->extraName);
}

Status CreateBindExtResPoolBuildRequest(FixBufferT *req, const void *in)
{
    FillSimpleStmtMsgHeader(req, MSG_OP_RPC_BIND_EXTENDED_RES_POOL);
    const CltBindResPoolCfgT *config = in;
    const uint32_t strNum = 3;
    return HelpFillMultiString(req, false, strNum, config->resPoolName, config->bindName, config->extraName);
}

Status DestroyBindExtResPoolBuildRequest(FixBufferT *req, const void *in)
{
    FillSimpleStmtMsgHeader(req, MSG_OP_RPC_UNBIND_EXTENDED_RES_POOL);
    const CltBindResPoolCfgT *config = in;
    const uint32_t strNum = 3;
    return HelpFillMultiString(req, false, strNum, config->resPoolName, config->bindName, config->extraName);
}

/**************************** 内部对外接口实现, clt_resource_pool.h中函数具体实现 **************************/
Status ExecGetResIdNum(FixBufferT *fixBuffer, const char *labelName, uint32_t *bufLen)
{
    uint32_t resIdGroupCnt;
    uint32_t resIdCount = 0;
    TextT vertexName;
    Status ret = SecureFixBufGetUint32(fixBuffer, &resIdGroupCnt);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get resId Group count.");
        return ret;
    }
    for (uint32_t i = 0; i < resIdGroupCnt; i++) {
        ret = SecureFixBufGetText(fixBuffer, &vertexName);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "get vertexName when get resid num inner.");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        ret = SecureFixBufGetUint32(fixBuffer, &resIdCount);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "get resIdCount when get resid num inner.");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        // labelName为空则全部获取
        if (IsStringEmpty(labelName) || (strcmp(vertexName.str, labelName) == 0)) {
            *bufLen += resIdCount;
        }
        // 略过 resIdCount * 8 长度的buffer
        FixBufSeek(fixBuffer, FixBufGetSeekPos(fixBuffer) + (uint32_t)(resIdCount * sizeof(uint64_t)));
    }
    return GMERR_OK;
}

Status ExecGetResIdInfo(
    FixBufferT *fixBuffer, const char *labelName, uint32_t resIdNum, uint64_t *resourceId, uint32_t *bufIndex)
{
    uint32_t resIdCount;
    uint64_t resIdTemp;
    uint32_t resIdGroupCnt;
    TextT vertexNameInner;
    Status ret = SecureFixBufGetUint32(fixBuffer, &resIdGroupCnt);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get resId Group count.");
        return ret;
    }
    for (uint32_t i = 0; i < resIdGroupCnt; i++) {
        ret = SecureFixBufGetText(fixBuffer, &vertexNameInner);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "get vertexName.");
            return ret;
        }
        ret = SecureFixBufGetUint32(fixBuffer, &resIdCount);
        if (ret != GMERR_OK) {
            return ret;
        }
        // labelname不是空 且 两个labelname不相同的时候 略过 相应的buffer执行下一轮
        if (!IsStringEmpty(labelName) && (strcmp(vertexNameInner.str, labelName) != 0)) {
            FixBufSeek(fixBuffer, FixBufGetSeekPos(fixBuffer) + (uint32_t)(resIdCount * sizeof(uint64_t)));
            continue;
        }
        for (uint32_t j = 0; j < resIdCount; j++) {
            ret = SecureFixBufGetUint64(fixBuffer, &resIdTemp);
            if (ret != GMERR_OK) {
                return ret;
            }
            if (*bufIndex < resIdNum) {
                resourceId[(*bufIndex)++] = resIdTemp;
            } else {
                DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
                    "resIdNum for getResIdInfo, bufIndex is %" PRIu32 ", resIdNum is %" PRIu32 ".", *bufIndex,
                    resIdNum);
                return GMERR_DATA_EXCEPTION;
            }
        }
    }
    return GMERR_OK;
}
