/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * File Name: gmc_respool.c
 * Description: realization of interface of gmdb respool and clt_resource_pool.h
 * gmc_respool.c --> gmc_respool.h & clt_resource_pool.h
 * Create: 2021-10-23
 */

#include "gmc_respool.h"
#include "clt_resource_pool.h"
#include "dm_meta_res_col_pool.h"

/**************************** 对外接口实现，gmc_respool.h 中函数具体实现 **************************/
Status GmcCreateResPool(GmcStmtT *stmt, const char *resPoolJson)
{
    Status ret = GmcCreateResPoolCheck(stmt, resPoolJson);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltRequestSync(stmt->conn, CreateResPoolBuildRequest, resPoolJson, DummyParseRsp, NULL);
}

Status GmcDestroyResPool(GmcStmtT *stmt, const char *resPoolName)
{
    Status ret = GmcResPoolBasicCheck(stmt, resPoolName);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltRequestSync(stmt->conn, DestroyResPoolBuildRequest, resPoolName, DummyParseRsp, NULL);
}

Status GmcBindResPoolToLabel(GmcStmtT *stmt, const char *resPoolName, const char *vertexLabel)
{
    Status ret = GmcResPoolOfLabelCheck(stmt, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = CltCheckStringValid(resPoolName, MAX_RES_POOL_NAME_LEN, "resPoolName");
    if (ret != GMERR_OK) {
        return ret;
    }
    CltBindResPoolCfgT cfg = {};
    cfg.resPoolName = resPoolName;
    cfg.bindName = vertexLabel;
    cfg.extraName = NULL;
    return CltRequestSync(stmt->conn, CreateBindResPoolBuildRequest, &cfg, DummyParseRsp, NULL);
}

Status GmcUnbindResPoolFromLabel(GmcStmtT *stmt, const char *vertexLabel)
{
    Status ret = GmcResPoolOfLabelCheck(stmt, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    CltBindResPoolCfgT cfg = {};
    cfg.resPoolName = NULL;
    cfg.bindName = vertexLabel;
    cfg.extraName = NULL;
    return CltRequestSync(stmt->conn, DestroyBindResPoolBuildRequest, &cfg, DummyParseRsp, NULL);
}

Status GmcBindExtResPool(GmcStmtT *stmt, const char *resPoolName, const char *extendedPoolName)
{
    Status ret = GmcResPoolBasicCheck(stmt, resPoolName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(extendedPoolName, MAX_RES_POOL_NAME_LEN, "extendedPoolName");
    if (ret != GMERR_OK) {
        return ret;
    }
    CltBindResPoolCfgT cfg = {};
    cfg.resPoolName = resPoolName;
    cfg.bindName = extendedPoolName;
    cfg.extraName = NULL;
    return CltRequestSync(stmt->conn, CreateBindExtResPoolBuildRequest, &cfg, DummyParseRsp, NULL);
}

Status GmcUnbindExtResPool(GmcStmtT *stmt, const char *resPoolName)
{
    Status ret = GmcResPoolBasicCheck(stmt, resPoolName);
    if (ret != GMERR_OK) {
        return ret;
    }
    CltBindResPoolCfgT cfg = {};
    cfg.resPoolName = resPoolName;
    cfg.bindName = NULL;
    cfg.extraName = NULL;
    return CltRequestSync(stmt->conn, DestroyBindExtResPoolBuildRequest, &cfg, DummyParseRsp, NULL);
}

Status GmcGetResPool(GmcStmtT *stmt, const char *resPoolName, char **resPoolJson)
{
    Status ret = GmcGetResPoolCheck(stmt, resPoolName, resPoolJson);
    if (ret != GMERR_OK) {
        return ret;
    }
    TextT resPoolBuf;
    ret = CltRequestSync(stmt->conn, GetResPoolBuildRequest, resPoolName, GetResPoolParseResponse, &resPoolBuf);
    if (ret != GMERR_OK) {
        return ret;
    }

    DmResColPoolT *resPool = NULL;
    ret = DmDeSerializeResColPoolMeta(stmt->opCtx, (uint8_t *)resPoolBuf.str, resPoolBuf.len, &resPool);
    if (ret != GMERR_OK) {
        return ret;
    }
    // memory will be freed by GmcResetStmt or GmcFreeStmt
    *resPoolJson = resPool->labelJson;
    return GMERR_OK;
}

Status GmcSetCountResource(uint16_t count, uint64_t *resourceId)
{
    Status ret = CltPtrCheckWithErr(resourceId);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmResColSetCountOfResId(resourceId, count);
    return GMERR_OK;
}

Status GmcSetPoolIdResource(uint16_t poolId, uint64_t *resourceId)
{
    Status ret = CltPtrCheckWithErr(resourceId);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmResColSetPoolIdOfResId(resourceId, poolId);
    return GMERR_OK;
}

Status GmcSetStartIdxResource(uint32_t startIndex, uint64_t *resourceId)
{
    Status ret = CltPtrCheckWithErr(resourceId);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmResColSetStartIndexOfResId(resourceId, startIndex);
    return GMERR_OK;
}

Status GmcGetResIdNum(GmcStmtT *stmt, uint32_t *resIdNum)
{
    Status ret = CltPtr2CheckWithErr(stmt, resIdNum);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcGetResIdCheck(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 直连写适配
    // 在单写场景下，响应报文中的resIdGroupCnt恒为1，因此resIdNum即等于每行的资源字段个数
    if (stmt->lastOpStatus.lastOpDirectWrite) {
        *resIdNum = (stmt->resInfo == NULL) ? 0 : stmt->resInfo->resCountEachRow;
        return GMERR_OK;
    }

    *resIdNum = 0;
    FixBufferT *fixBuffer = &stmt->recvPack;
    FixBufSeek(fixBuffer, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE + sizeof(uint32_t));
    // datalog请求响应报文格式如下，获取resIdNum时需将seekPos移到errorCode和errorLabelName后
    // msgHeader|OpHeader|affectedRows(uint32_t)|errorCode(int64_t)|errorLabelName(TextT)|lastError(TextT)
    if (stmt->isDataService) {
        FixBufSeek(fixBuffer, FixBufGetSeekPos(fixBuffer) + sizeof(int64_t));
        TextT dtlErrorLabelName = {0};
        ret = SecureFixBufGetTextNullable(fixBuffer, &dtlErrorLabelName);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_AND_SET_LASERR(ret, "move seekPos to the end of dtlErrorLabelName");
            return ret;
        }
    }
    return ExecGetResIdNum(fixBuffer, NULL, resIdNum);
}

Status GmcGetResIdInfo(GmcStmtT *stmt, uint64_t *resIdBuf, uint32_t *resIdNum)
{
    // step 1: check input
    Status ret = GmcGetResIdInfoCheck(stmt, resIdBuf, resIdNum);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 直连写适配
    if (stmt->lastOpStatus.lastOpDirectWrite) {
        if (stmt->resInfo == NULL || *resIdNum == 0) {
            DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "get resid in stmt");
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        if (*resIdNum != stmt->resInfo->resCountEachRow) {
            DB_SET_LASTERR(
                GMERR_DATA_EXCEPTION, "given resIdNum unsound, correct is %" PRIu32, stmt->resInfo->resCountEachRow);
        }
        uint32_t size = (uint32_t)sizeof(ResId) * stmt->resInfo->resCountEachRow;
        errno_t err = memcpy_s(resIdBuf, size, stmt->resInfo->resIds, size);
        DB_ASSERT(err == EOK);
        return GMERR_OK;
    }

    FixBufferT *fixBuffer = &stmt->recvPack;
    // scape head plus affect rows
    uint32_t tempSeekPos = MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE + sizeof(uint32_t);
    FixBufSeek(fixBuffer, tempSeekPos);
    // 此处偏移同GmcGetResIdNum
    if (stmt->isDataService) {
        FixBufSeek(fixBuffer, FixBufGetSeekPos(fixBuffer) + sizeof(int64_t));
        TextT dtlErrorLabelName = {0};
        ret = SecureFixBufGetTextNullable(fixBuffer, &dtlErrorLabelName);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_AND_SET_LASERR(ret, "move seekPos to the end of dtlErrorLabelName");
            return ret;
        }
    }
    uint32_t bufIndex = 0;
    ret = ExecGetResIdInfo(fixBuffer, NULL, *resIdNum, resIdBuf, &bufIndex);
    if (bufIndex == 0) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "get resid in stmt");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (ret == GMERR_DATA_EXCEPTION && bufIndex == *resIdNum) {
        FixBufSeek(fixBuffer, tempSeekPos);
        (void)ExecGetResIdNum(fixBuffer, NULL, resIdNum);
        DB_SET_LASTERR(GMERR_DATA_EXCEPTION, "given resIdNum, correct is %" PRIu32, *resIdNum);
    }
    return ret;
}

Status GmcGetCountResource(uint64_t resourceId, uint16_t *count)
{
    Status ret = CltPtrCheckWithErr(count);
    if (ret != GMERR_OK) {
        return ret;
    }
    *count = DmResColGetCountFromResId(resourceId);
    return GMERR_OK;
}

Status GmcGetPoolIdResource(uint64_t resourceId, uint16_t *poolId)
{
    Status ret = CltPtrCheckWithErr(poolId);
    if (ret != GMERR_OK) {
        return ret;
    }
    *poolId = DmResColGetPoolIdFromResId(resourceId);
    return GMERR_OK;
}

Status GmcGetStartIdxResource(uint64_t resourceId, uint32_t *startIndex)
{
    Status ret = CltPtrCheckWithErr(startIndex);
    if (ret != GMERR_OK) {
        return ret;
    }
    *startIndex = DmResColGetStartIndexFromResId(resourceId);
    return GMERR_OK;
}
