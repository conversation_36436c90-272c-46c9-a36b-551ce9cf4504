/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * File Name: clt_resource_pool.h
 * Description: interface of resource pool of internal client
 * Author:
 * Create: 2021-08-10
 */

#ifndef CLT_RESOURCE_POOL_H
#define CLT_RESOURCE_POOL_H

#include "adpt_define.h"
#include "db_rpc_conn_msg.h"
#include "db_secure_msg_buffer.h"
#include "clt_check.h"
#include "clt_fill_msg.h"
#include "clt_msg.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @ingroup res_pool_cfg
 *
 */
typedef struct {
    const char *resPoolName;
    const char *bindName;
    const char *extraName;
} CltBindResPoolCfgT;

/* 内部函数声明 */
Status GmcCreateResPoolCheck(GmcStmtT *stmt, const char *resPool<PERSON>son);
Status GmcResPoolOfLabelCheck(GmcStmtT *stmt, const char *vertexLabel);
Status GmcResPoolOfFieldCheck(GmcStmtT *stmt, const char *labelName, const char *propertyName);
Status GmcResPoolBasicCheck(GmcStmtT *stmt, const char *resPoolName);
Status GmcGetResPoolCheck(GmcStmtT *stmt, const char *resPoolName, char *const *resPoolJson);
Status GmcGetResIdInfoCheck(GmcStmtT *stmt, const uint64_t *resIdBuf, const uint32_t *resIdNum);
Status GmcGetResIdCheck(GmcStmtT *stmt);

Status CreateResPoolBuildRequest(FixBufferT *req, const void *in);
Status GetResPoolBuildRequest(FixBufferT *req, const void *in);
Status GetResPoolParseResponse(FixBufferT *rsp, void *out);
Status DestroyResPoolBuildRequest(FixBufferT *req, const void *in);
Status CreateBindResPoolBuildRequest(FixBufferT *req, const void *in);
Status DestroyBindResPoolBuildRequest(FixBufferT *req, const void *in);
Status CreateBindResPoolToFieldBuildRequest(FixBufferT *req, const void *in);
Status DestroyBindResPoolToFieldBuildRequest(FixBufferT *req, const void *in);
Status CreateBindExtResPoolBuildRequest(FixBufferT *req, const void *in);
Status DestroyBindExtResPoolBuildRequest(FixBufferT *req, const void *in);

Status ExecGetResIdInfo(
    FixBufferT *fixBuffer, const char *labelName, uint32_t resIdNum, uint64_t *resourceId, uint32_t *bufIndex);
Status ExecGetResIdNum(FixBufferT *fixBuffer, const char *labelName, uint32_t *bufLen);

#ifdef __cplusplus
}
#endif

#endif /* CLT_RESOURCE_POOL_H */
