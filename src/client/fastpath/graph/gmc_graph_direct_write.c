/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: gmc_graph_direct_write.c
 * Description: realization of interface of gmdb graph for direct write
 * Author:
 * Create: 2024-07-05
 */

#include "gmc_internal.h"
#include "gmc_types.h"
#include "clt_check.h"
#include "clt_stmt.h"
#include "clt_da_write.h"
#include "clt_da_vertex_directread.h"

#ifdef DIRECT_WRITE
static Status DwAllocCostTimeInfo(GmcConnT *conn)
{
    if (SECUREC_UNLIKELY(conn->dwTimeCost == NULL)) {
        size_t size = (size_t)sizeof(CltDirectWriteTimeCostT);
        conn->dwTimeCost = DbDynMemCtxAlloc(conn->memCtx, size);
        if (conn->dwTimeCost == NULL) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc direct write time cost info.");
            return GMERR_OUT_OF_MEMORY;
        }
        errno_t err = memset_s(conn->dwTimeCost, size, 0, size);
        if (err != EOK) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "memset direct write time cost info.");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    }
    return GMERR_OK;
}

static Status DirectWriteCtxInitForStmt(GmcStmtT *stmt, DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(stmt, vertexLabel);
    DwRunCtxT *dwRunCtx = stmt->dwRunCtx;
    DirectAccessRunCtxT *dwBaseRunCtx = &dwRunCtx->base;

    // 初始化直连写上下文
    ReleaseDwRunCtx4StmtInner(dwRunCtx, stmt->memCtx);
    Status ret = DirectAccessInitBaseRunCtxInfo(dwBaseRunCtx, stmt->conn->memCtx);  // 这里传入的是conn级别的memCtx
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "init base runctx info.");
        return ret;
    }
    dwRunCtx->secIdxCtx = NULL;
    dwRunCtx->secIdxNum = 0;
    dwRunCtx->tupleAddr = 0;
    dwRunCtx->dwArgs = (DwArgsT){0};
    dwRunCtx->dwArgs.vertexArgs.partitionId = DM_MAX_PARTITION_ID;
    dwRunCtx->dwArgs.currOpType = GMC_OPERATION_BUTT;
    dwRunCtx->dwArgs.lastOpType = GMC_OPERATION_BUTT;
    dwRunCtx->labelStatistics = NULL;
    dwBaseRunCtx->isUseClusteredHashTable = vertexLabel->metaVertexLabel->containerType == CONTAINER_CLUSTERED_HASH;
    dwBaseRunCtx->containerType = vertexLabel->metaVertexLabel->containerType;
#ifdef ART_CONTAINER
    dwBaseRunCtx->isRealCluster = vertexLabel->commonInfo->isArtRealCluster;
    dwBaseRunCtx->artMemCtx = (DbMemCtxT *)DbGetShmemCtxById(DB_SE_ART_CONTAINER_SHM_CTX_ID, DbGetProcGlobalId());
#endif
    // 权限相关字段
    if (SECUREC_UNLIKELY(dwBaseRunCtx->objPrivNsp == NULL)) {
        VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
        dwBaseRunCtx->objPrivNsp = DbShmPtrToAddr(commonInfo->nspObjPrivShmPtr);
        if (dwBaseRunCtx->objPrivNsp == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "transfer shmptr to addr.");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    }
    dwBaseRunCtx->nspPrivVersion = dwBaseRunCtx->objPrivNsp->objPrivVersion;
    dwBaseRunCtx->sysPrivVersion = dwBaseRunCtx->role->sysPrivVersion;
    dwBaseRunCtx->hasInited = true;  // 表示stmt上的dwRunCtx初始化完成
    return GMERR_OK;
}

// 此函数仅在DirectWriteHandlePrepareForStmt中使用
static void DirectWriteHandleInitUnSucc(GmcStmtT *stmt)
{
    DbDynMemCtxFree(stmt->memCtx, stmt->dwRunCtx);
    stmt->dwRunCtx = NULL;
    stmt->dwRunCtxHasAllocated = false;
}

static Status DirectWriteAllocStmtRelColMem(GmcStmtT *stmt, DmVertexLabelT *vertexLabel)
{
    DB_POINTER(vertexLabel);
    DmResColInfoT *resColInfo = MEMBER_PTR(vertexLabel->commonInfo, resColInfo);
    if (resColInfo == NULL) {
        return GMERR_OK;
    }
    // 在prepare stmt阶段预先申请内存，方便后续获取resInfo。清理dwRunCtx的时候会释放内存。
    // 目前DirectWriteHandlePrepareForStmt在对外函数最后，不涉及异常分支清理资源。
    if (stmt->resInfo == NULL) {
        size_t allocSize = sizeof(DwResInfoT) + (sizeof(ResId) * resColInfo->resColCount);
        char *buf = (char *)DbDynMemCtxAlloc(stmt->memCtx, allocSize);
        if (buf == NULL) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc resInfo for when prepare stmt.");
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memset_s(buf, allocSize, 0x00, allocSize);
        stmt->resInfo = (DwResInfoT *)(void *)buf;
        stmt->resInfo->resIds = (ResId *)(void *)(buf + sizeof(DwResInfoT));
        stmt->resInfo->resCountEachRow = resColInfo->resColCount;
    } else {
        for (uint32_t i = 0; i < resColInfo->resColCount; i++) {
            stmt->resInfo->resIds[i] = 0;
        }
        stmt->resInfo->resCountEachRow = resColInfo->resColCount;
    }
    return GMERR_OK;
}

Status DirectWriteHandlePrepareForStmt(GmcStmtT *stmt)
{
    // 直连写适配
    // 操作类型属于直连写支持的类型时，将开关置为true
    Status ret = GMERR_OK;
    // 如果上一次openDirectWrite为true，则表示在prepare的时候被判断后面可以执行直连写的DML操作，但是并没有执行，或者执行失败
    // 如果是常规执行失败则不关注，如果是因为直连不支持而失败需要注意后续执行会不会因为上一次失败而受到影响
    CltOperVertexT *op = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = op->cltCataLabel->vertexLabel;

    // 这个开关使用直连写的强约束，所有可以通过静态信息判断的拦截都应在此处完成
    // 前面已校验了 stmt 和 operationType，此处校验 vertexLabel
    stmt->openDirectWrite = stmt->openDirectWrite && DirectWriteConstraintVerify(stmt, vertexLabel);
    if (SECUREC_LIKELY(stmt->openDirectWrite)) {
        // 如果是首次执行直连写，申请资源，并挂在conn之下以供复用
        if (SECUREC_UNLIKELY(stmt->conn->directWriteCtx == NULL)) {
            DwRunCtxT **dwRunCtx = (DwRunCtxT **)&stmt->conn->directWriteCtx;
            if ((ret = DwOpenDirectWriteRunCtx(stmt->conn, stmt->conn->sid, dwRunCtx)) != GMERR_OK) {
                DB_LOG_ERROR(ret, "Open DirectWriteRunCtx when prepare for direct "
                                  "write operation at first time.");
                return ret;
            }
        }
        if ((ret = DwAllocCostTimeInfo(stmt->conn)) != GMERR_OK) {
            return ret;
        }
        if (SECUREC_UNLIKELY(!stmt->dwRunCtxHasAllocated)) {  // stmt上的dwRunCtx已经分配并初始化完成后不执行此步骤
            DwRunCtxT *dwRunCtx = (DwRunCtxT *)stmt->conn->directWriteCtx;
            stmt->dwRunCtx = DbDynMemCtxAlloc(stmt->memCtx, sizeof(DwRunCtxT));
            if (SECUREC_UNLIKELY(stmt->dwRunCtx == NULL)) {
                DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc direct write run context.");
                return GMERR_OUT_OF_MEMORY;
            }
            stmt->dwRunCtxHasAllocated = true;
            errno_t err = memcpy_s(stmt->dwRunCtx, sizeof(DwRunCtxT), dwRunCtx, sizeof(DwRunCtxT));
            DB_ASSERT(err == EOK);
            if ((ret = DirectReadInitLabelLatch(&stmt->dwRunCtx->base, vertexLabel)) != GMERR_OK) {
                DirectWriteHandleInitUnSucc(stmt);
                DB_LOG_ERROR(ret, "Init label latch.");
                return ret;
            }
            if ((ret = DirectWriteCtxInitForStmt(stmt, vertexLabel)) != GMERR_OK) {
                DirectWriteHandleInitUnSucc(stmt);
                DB_LOG_ERROR(ret, "Init direct write run context.");
                return ret;
            }
        }
        if ((ret = DirectWriteAllocStmtRelColMem(stmt, vertexLabel)) != GMERR_OK) {
            return ret;
        }
    }  // else 如果上一次使用了直连写，这次不使用直连写，之前的资源要不要释放
    return GMERR_OK;
}
#endif  // DIRECT_WRITE
