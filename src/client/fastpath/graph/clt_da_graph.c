/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author:
 * Create: 2023-05-25
 */
#include "clt_da_graph.h"
#include "db_memcpy.h"
#include "ds_log.h"
#include "dm_data_kv.h"
#include "ee_check_utils.h"

void GetStMgDataDeleteMark(const HeapTupleBufT *heapTupleBuf, const DmVertexLabelT *vertexLabel,
    DmVertexDescT *vertexDesc, uint8_t *deleteMark)
{
    DB_POINTER3(heapTupleBuf, deleteMark, vertexLabel);
    DmValueT value;
    value.type = DB_DATATYPE_UINT8;
    DmVertexBufGetSysPrope(STATUS_MERGE_DELETE_MARK, heapTupleBuf->buf, &value, vertexDesc);
    *deleteMark = value.value.ubyteValue;
}

bool IsStmgMarkDelete(const DmVertexLabelT *vertexLabel, DmVertexDescT *vertexDesc, const HeapTupleBufT *heapTupleBuf)
{
    DB_POINTER2(vertexLabel, heapTupleBuf);
    if (!DmIsLabelSupportStatusMerge(vertexLabel)) {
        return false;
    }
    uint8_t deleteMark;
    GetStMgDataDeleteMark(heapTupleBuf, vertexLabel, vertexDesc, &deleteMark);
    return (bool)deleteMark;
}

static bool FilterOpMatch(int32_t opCode, int8_t compress)
{
    switch (opCode) {
        case GMC_OP_EQUAL:
            return compress == 0;
        case GMC_OP_LARGE_EQUAL:
            return compress >= 0;
        case GMC_OP_LARGE:
            return compress > 0;
        case GMC_OP_SMALL:
            return compress < 0;
        case GMC_OP_SMALL_EQUAL:
            return compress <= 0;
        default:
            DB_ASSERT(0);
            return false;
    }
}

inline static Status DsGetPropValueByDepth(
    uint32_t *nodePath, uint32_t fieldId, DmVertexT *vertex, uint32_t depth, DmValueT *value)
{
    if (depth != 0) {
        DmNodeT *node = NULL;
        Status ret = DmVertexGetNodeByIdPath(vertex, nodePath, depth, &node);
        if (ret != GMERR_OK) {
            return ret;
        }
        return DmNodeGetPropeByIdNoCopy(node, fieldId, value);
    } else {
        return DmVertexGetPropeByIdNoCopy(vertex, fieldId, value);
    }
}

bool DsMatchFilterStructure(const HeapTupleBufT *tupleBuf, StructFilterListT *structFilterList, DmVertexT *vertex)
{
    Status ret;
#ifdef FEATURE_GQL
    if (structFilterList == NULL || !structFilterList->isUsed) {
        return true;
    }
    DbListT *list = structFilterList->list;
    uint32_t count = DbListGetItemCnt(list);
    for (uint32_t i = 0; i < count; i++) {
        FilterInnerT *filterInner = (FilterInnerT *)DbListItem(list, i);
#else
    if (structFilterList == NULL || structFilterList->list == NULL) {
        return true;
    }
    FilterInnerT **list = structFilterList->list;
    uint32_t count = structFilterList->count;
    for (uint32_t i = 0; i < count; i++) {
        FilterInnerT *filterInner = list[i];
#endif  // FEATURE_GQL
        if (!filterInner->setValue) {
            break;
        }
        DmValueT value = {};
        ret = DmDeSerialize2ExistsVertex(tupleBuf->buf, tupleBuf->bufSize, vertex, DmGetCheckMode());
        if (ret != GMERR_OK) {
            return false;
        }
        ret = DsGetPropValueByDepth(filterInner->nodePath, filterInner->fieldId, vertex, filterInner->depth, &value);
        if (ret != GMERR_OK) {
            return false;
        }
        int8_t result;
        // tuple 中的Value 等于filter的value 返回 0
        // tuple 中的value 大于filter的value 返回 1
        // tuple 中的value 小于filter的value 返回-1
        ret = DmValueCompare(&value, &filterInner->value, &result);
        if (ret != GMERR_OK) {
            return false;
        }
        // 每个条件都必须match
        if (!FilterOpMatch(filterInner->compOp, result)) {
            return false;
        }
        filterInner++;
    }
    return true;
}

static Status IsVertexBufAgedForSimpleVertex(
    const HeapTupleBufT *heapTupleBuf, CltCataLabelT *cltCataLabel, const DmVertexT *vertex, bool *isAged)
{
    DmAccCheckT *accCheckAddr = (DmAccCheckT *)(DmAccCheckT *)(cltCataLabel->vertexAccCheck);
    DmCheckInfoT *ci = &accCheckAddr->checkInfo[0];
    if (SECUREC_LIKELY(!ci->version.hasBeenChecked)) {
        *isAged = false;
        return GMERR_OK;
    }

    uint8_t checkVersion = 0;
    DmVertexBufGetCheckVersion(heapTupleBuf->buf, &checkVersion, vertex->vertexDesc);

    // 根据checkInfo信息和vertexBuf中checkVersion判断vertexBuf是否老化数据
    *isAged = QryIsAged(&ci->version, checkVersion);
    return GMERR_OK;
}

Status IsVertexBufAged(
    const HeapTupleBufT *heapTupleBuf, CltCataLabelT *cltCataLabel, bool *isAged, DmVertexDescT *vertexDesc)
{
    DB_POINTER2(heapTupleBuf, cltCataLabel);
    DmVertexLabelT *vertexLabel = cltCataLabel->vertexLabel;
    if (DmVertexLabelIsDatalogLabel(vertexLabel)) {
        *isAged = false;
        return GMERR_OK;
    }

    Status ret;
    // 获取checkInfo信息
    uint8_t partitionId = DB_INVALID_UINT8;
    DmAccCheckT *accCheckAddr = (DmAccCheckT *)(cltCataLabel->vertexAccCheck);
    if (accCheckAddr->isPartition) {
        DmValueT partitionValue;
        ret = DmVertexBufGetPartitionValue(vertexLabel, heapTupleBuf->buf, heapTupleBuf->bufSize, &partitionValue);
        if (ret != GMERR_OK) {
            return ret;
        }
        DB_ASSERT(partitionValue.value.partitionValue < DM_MAX_PARTITION_ID);
        partitionId = partitionValue.value.partitionValue;
    }
    // 此处不用加锁，加锁逻辑在上层处理
    DmCheckInfoT *checkInfo = &accCheckAddr->checkInfo[(partitionId == DB_INVALID_UINT8) ? 0 : partitionId];

    if (SECUREC_LIKELY(!checkInfo->version.hasBeenChecked)) {
        *isAged = false;
        return GMERR_OK;
    }

    // 获取vertexBuf中的checkVersion
    DmValueT versionValue;
    versionValue.type = DB_DATATYPE_UINT8;
    DmVertexBufGetSysPrope(CHECK_VERSION, heapTupleBuf->buf, &versionValue, vertexDesc);
    uint8_t checkVersion = versionValue.value.ubyteValue;
    *isAged = QryIsAged(&checkInfo->version, checkVersion);
    return GMERR_OK;
}

Status MainStoreCheckAge(const HeapTupleBufT *heapTupleBuf, uint8_t *userData)
{
    DrHpCopyExtArgsT *args = (DrHpCopyExtArgsT *)userData;
    CltCataLabelT *cltCataLabel = args->drRunCtx->dsFetchArgs.vertexScanArgs.cltCataLabel;
    DmAccCheckT *accCheckAddr = (DmAccCheckT *)(cltCataLabel->vertexAccCheck);
    if (SECUREC_LIKELY(!accCheckAddr->hasBeenChecked)) {
        args->isAged = false;
        return GMERR_OK;
    }

    Status ret;
    DmVertexLabelT *vertexLabel = cltCataLabel->vertexLabel;
    if (!accCheckAddr->isPartition && vertexLabel->metaVertexLabel->labelLevel == VERTEX_LEVEL_SIMPLE) {
        ret = IsVertexBufAgedForSimpleVertex(heapTupleBuf, cltCataLabel, args->vertex, &args->isAged);
    } else {
        ret = IsVertexBufAged(heapTupleBuf, cltCataLabel, &args->isAged, vertexLabel->vertexDesc);
    }
    return ret;
}

Status MainStoreCheckAgeOpt(const HeapTupleBufT *heapTupleBuf, uint8_t *userData)
{
    DrHpCopyExtArgsOptT *args = (DrHpCopyExtArgsOptT *)userData;
    CltCataLabelT *cltCataLabel = args->drRunCtx->dsFetchArgs.vertexScanArgs.cltCataLabel;
    DmAccCheckT *accCheckAddr = (DmAccCheckT *)(cltCataLabel->vertexAccCheck);
    if (SECUREC_LIKELY(
            !accCheckAddr->isPartition && !(((DmCheckInfoT *)(&accCheckAddr->checkInfo[0]))->version.hasBeenChecked))) {
        args->isAged = false;
        return GMERR_OK;
    }

    Status ret;
    DmVertexLabelT *vertexLabel = cltCataLabel->vertexLabel;
    if (!accCheckAddr->isPartition && vertexLabel->metaVertexLabel->labelLevel == VERTEX_LEVEL_SIMPLE) {
        ret = IsVertexBufAgedForSimpleVertex(heapTupleBuf, cltCataLabel, args->vertex, &args->isAged);
    } else {
        DmVertexDescT *vertexDesc = cltCataLabel->vertexLabel->vertexDesc;
        ret = IsVertexBufAged(heapTupleBuf, cltCataLabel, &args->isAged, vertexDesc);
    }
    return ret;
}

Status StructureVertexCopy(DrRunCtxT *drRunCtx, const HeapTupleBufT *resBuf)
{
    StructureCtxT *structureCtx = &drRunCtx->structureCtx;
    if (structureCtx->isUserBuffer) {
        // 升降级场景
        if (SECUREC_UNLIKELY(structureCtx->vertexOffset == 0)) {
            uint8_t sysLen = *(uint8_t *)resBuf->buf;
            uint32_t bufLen = (uint32_t)sizeof(uint8_t) + sysLen + (uint32_t)sizeof(uint8_t);
            if (SECUREC_UNLIKELY(resBuf->bufSize < bufLen + sizeof(uint32_t))) {
                return GMERR_DATA_EXCEPTION;
            }
            uint32_t fixLen = 0;
            uint32_t varNum = 0;
            DmConvertVarintToUint32(*(uint32_t *)(resBuf->buf + bufLen), &fixLen, &varNum);
            structureCtx->vertexOffset = bufLen + varNum;
            uint32_t cpyLen = DB_MIN(structureCtx->userbufLen, fixLen - RESERVED_SIZE);
            uint8_t *buf = resBuf->buf + structureCtx->vertexOffset;
            // 此处因为性能原因使用GMDB项目自己实现的DbFastMemcpy而非memcpy_s，需要保证内存复制合法。
            // 如果用新schame读老buf，这里拷贝的是老buf中的定长字段，新增字段的默认值需要用户自己填充
            DbFastMemcpy(structureCtx->userBuf, cpyLen, buf, cpyLen);
            return GMERR_OK;
        }
        // structureCtx->userbufLen用户传入的buf长度，structureCtx->vertexOffset为头长度
        // 非升降级场景下保证内存不越界
        if (resBuf->bufSize < structureCtx->vertexOffset + structureCtx->userbufLen) {
            return GMERR_DATA_EXCEPTION;
        }
        uint8_t *buf = resBuf->buf + structureCtx->vertexOffset;
        // 此处因为性能原因使用GMDB项目自己实现的DbFastMemcpy而非memcpy_s，需要保证内存复制合法。
        DbFastMemcpy(structureCtx->userBuf, structureCtx->userbufLen, buf, structureCtx->userbufLen);
        return GMERR_OK;
    }
    return FixBufferCopy(drRunCtx->directReadBuff, resBuf->buf, resBuf->bufSize);
}

Status MainStoreUserBufferCopy(const HeapTupleBufT *heapTupleBuf, uint8_t *userData)
{
    DrHpCopyExtArgsT *args = (DrHpCopyExtArgsT *)userData;
    if (SECUREC_UNLIKELY(args->isAged)) {
        return GMERR_OK;
    }
    // 如果使用结构化读取这里进行buffercopy
    Status ret = StructureVertexCopy(args->drRunCtx, heapTupleBuf);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Structure copy buf.");
    }
    return ret;
}

// 客户端不要自己解析vertexbuf
inline static int32_t DsCmpKeyBufInV3Mode(DsHpFetchAndCmpInfoT *cmpInfo, uint8_t *buf)
{
    uint8_t nullInfoBytes = cmpInfo->nullInfoBytes;
    uint8_t *hashData = cmpInfo->idxKeyData;
    uint8_t *vertexBufCursor = (uint8_t *)buf;
    uint32_t varintByteNum;
    vertexBufCursor += GetSysBufLen(vertexBufCursor);

    DmVertexTypeE vertexType = (DmVertexTypeE)DM_GET_VERTEX_TYPE(*(uint8_t *)vertexBufCursor);
    vertexBufCursor += (uint32_t)sizeof(uint8_t);  // vertexType
    // 在DM_TREE_VERTEX vertex中，记录了vertex总长度
    DB_ASSERT(vertexType != DM_FLAT_VERTEX);
    if (vertexType == DM_TREE_VERTEX) {
        DmGetVarintLenFromBuf(vertexBufCursor, &varintByteNum);  // vertex总长度
        vertexBufCursor += varintByteNum;
        DmGetVarintLenFromBuf(vertexBufCursor, &varintByteNum);  // record len
        vertexBufCursor += varintByteNum;
    }
    DmGetVarintLenFromBuf(vertexBufCursor, &varintByteNum);  // fixLen
    vertexBufCursor += varintByteNum;
    hashData += nullInfoBytes;
    return memcmp(hashData, vertexBufCursor, cmpInfo->idxkeyLen - nullInfoBytes);
}

int32_t DsCmpKeyBufFromDM(DsHpFetchAndCmpInfoT *cmpInfo, uint8_t *buf)
{
    int32_t cmpRet = 0;
    uint32_t len = 0;
    // 后续需要改成注册函数
    if (SECUREC_LIKELY(cmpInfo->tpKeyBufInfo)) {
        DmGetKeyBufFromVertexBuf(
            (const DmIndexKeyBufInfoT *)cmpInfo->tpKeyBufInfo, (uint8_t *)buf, cmpInfo->tpKeyBuf, &len);
    } else {
        DB_ASSERT(cmpInfo->tpKeyBuf == NULL);
        DmGetKeyFromKvBuf((uint8_t *)buf, &cmpInfo->tpKeyBuf, &len);
    }

    if (len > cmpInfo->idxkeyLen) {
        cmpRet = 1;
    } else if (len < cmpInfo->idxkeyLen) {
        cmpRet = -1;
    } else {
        cmpRet = memcmp(cmpInfo->idxKeyData, cmpInfo->tpKeyBuf, len);
    }
    return cmpRet;
}

StatusInter DsHeapCmpAndCopyHpTupleBuf(HpReadRowInfoT *readRowInfo, void *userData)
{
    DB_POINTER2(readRowInfo, userData);
    DrHpCopyExtArgsT *args = (DrHpCopyExtArgsT *)userData;
    args->trxId = readRowInfo->trxId;
    DsHpFetchAndCmpInfoT *cmpInfo = &args->cmpInfo;

    if (SECUREC_LIKELY(cmpInfo->isV3Mode)) {
        cmpInfo->isMatch = DsCmpKeyBufInV3Mode(cmpInfo, readRowInfo->buf) == 0;
    } else {
        cmpInfo->isMatch = DsCmpKeyBufFromDM(cmpInfo, readRowInfo->buf) == 0;
    }

    if (SECUREC_UNLIKELY(!cmpInfo->isMatch)) {
        return STATUS_OK_INTER;
    }

    const HeapTupleBufT heapTupleBuf = {.bufSize = readRowInfo->bufSize, .buf = (uint8_t *)readRowInfo->buf};

    // 判断是否是stmg订阅的标记删除数据, 当前存在args->vertex为NULL的情况
    CltCataLabelT *cltCataLabel = args->drRunCtx->dsFetchArgs.vertexScanArgs.cltCataLabel;
    DmVertexDescT *vertexDesc = cltCataLabel->vertexLabel->vertexDesc;
    DmVertexLabelT *vertexLabel = cltCataLabel->vertexLabel;
    if (SECUREC_LIKELY(args->vertex != NULL)) {
        bool isMarkDelete = IsStmgMarkDelete(vertexLabel, vertexDesc, &heapTupleBuf);
        if (SECUREC_UNLIKELY(isMarkDelete)) {
            cmpInfo->isMatch = false;
            return STATUS_OK_INTER;
        }
    }
    // check aged data
    Status ret = GMERR_OK;
    if (SECUREC_LIKELY(args->checkAge)) {
        ret = MainStoreCheckAge(&heapTupleBuf, userData);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "check age");
            return ret;
        }
    }

    // copy data
    if (SECUREC_UNLIKELY(!args->needCopy)) {
        return STATUS_OK_INTER;
    }
    ret = MainStoreUserBufferCopy(&heapTupleBuf, userData);
    return (ret == GMERR_OK) ? STATUS_OK_INTER : MEMORY_OPERATE_FAILED_INTER;
}

Status StructureVertexCopyOpt(DrRunStructOptCtxT *drRunCtx, const HeapTupleBufT *resBuf)
{
    StructureCtxT *structureCtx = &drRunCtx->structureCtx;
    if (SECUREC_UNLIKELY(structureCtx->vertexOffset == 0)) {
        uint8_t sysLen = *(uint8_t *)resBuf->buf;
        uint32_t bufLen = (uint32_t)sizeof(uint8_t) + sysLen + (uint32_t)sizeof(uint8_t);
        if (SECUREC_UNLIKELY(resBuf->bufSize < bufLen + sizeof(uint32_t))) {
            return GMERR_DATA_EXCEPTION;
        }
        uint32_t fixLen = 0;
        uint32_t varNum = 0;
        DmConvertVarintToUint32(*(uint32_t *)(resBuf->buf + bufLen), &fixLen, &varNum);
        structureCtx->vertexOffset = bufLen + varNum;
        uint32_t cpyLen = DB_MIN(structureCtx->userbufLen, fixLen - RESERVED_SIZE);
        uint8_t *buf = resBuf->buf + structureCtx->vertexOffset;
        // 如果用新schame读老buf，这里拷贝的是老buf中的定长字段，新增字段的默认值需要用户自己填充
        (void)memcpy_s(structureCtx->userBuf, cpyLen, buf, cpyLen);
        return GMERR_OK;
    }
    // structureCtx->userbufLen用户传入的buf长度，structureCtx->vertexOffset为头长度
    // 非升降级场景下保证内存不越界
    if (resBuf->bufSize < structureCtx->vertexOffset + structureCtx->userbufLen) {
        return GMERR_DATA_EXCEPTION;
    }
    uint8_t *buf = resBuf->buf + structureCtx->vertexOffset;
    (void)memcpy_s(structureCtx->userBuf, structureCtx->userbufLen, buf, structureCtx->userbufLen);
    return GMERR_OK;
}

Status MainStoreUserBufferCopyOpt(const HeapTupleBufT *heapTupleBuf, uint8_t *userData)
{
    DrHpCopyExtArgsOptT *args = (DrHpCopyExtArgsOptT *)userData;
    if (SECUREC_UNLIKELY(args->isAged)) {
        return GMERR_OK;
    }
    // 如果使用结构化读取这里进行buffercopy
    Status ret = StructureVertexCopyOpt(args->drRunCtx, heapTupleBuf);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Structure copy buf.");
    }
    return ret;
}

StatusInter DsHeapCmpAndCopyHpTupleBufOpt(HpReadRowInfoT *readRowInfo, void *userData)
{
    DB_POINTER2(readRowInfo, userData);
    DrHpCopyExtArgsOptT *args = (DrHpCopyExtArgsOptT *)userData;
    args->trxId = readRowInfo->trxId;
    DsHpFetchAndCmpInfoT *cmpInfo = &args->cmpInfo;

    if (SECUREC_LIKELY(cmpInfo->isV3Mode)) {
        cmpInfo->isMatch = DsCmpKeyBufInV3Mode(cmpInfo, readRowInfo->buf) == 0;
    } else {
        cmpInfo->isMatch = DsCmpKeyBufFromDM(cmpInfo, readRowInfo->buf) == 0;
    }

    if (SECUREC_UNLIKELY(!cmpInfo->isMatch)) {
        return STATUS_OK_INTER;
    }

    const HeapTupleBufT heapTupleBuf = {.bufSize = readRowInfo->bufSize, .buf = (uint8_t *)readRowInfo->buf};

    // 判断是否是stmg订阅的标记删除数据, 当前存在args->vertex为NULL的情况
    CltCataLabelT *cltCataLabel = args->drRunCtx->dsFetchArgs.vertexScanArgs.cltCataLabel;
    DmVertexDescT *vertexDesc = cltCataLabel->vertexLabel->vertexDesc;
    DmVertexLabelT *vertexLabel = cltCataLabel->vertexLabel;
    if (SECUREC_LIKELY(args->vertex != NULL)) {
        bool isMarkDelete = IsStmgMarkDelete(vertexLabel, vertexDesc, &heapTupleBuf);
        if (SECUREC_UNLIKELY(isMarkDelete)) {
            cmpInfo->isMatch = false;
            return STATUS_OK_INTER;
        }
    }
    // check aged data
    Status ret = GMERR_OK;
    if (SECUREC_LIKELY(args->checkAge)) {
        ret = MainStoreCheckAgeOpt(&heapTupleBuf, userData);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "check age");
            return ret;
        }
    }

    // copy data
    if (SECUREC_UNLIKELY(!args->needCopy)) {
        return STATUS_OK_INTER;
    }
    ret = MainStoreUserBufferCopyOpt(&heapTupleBuf, userData);
    return (ret == GMERR_OK) ? STATUS_OK_INTER : MEMORY_OPERATE_FAILED_INTER;
}

Status DsHashClusterKeyCompare(
    Handle chRunCtx, IndexKeyT hashKey, const HeapTupleBufT *tupleBuf, int32_t *cmpRet, bool *isMatch)
{
    DB_POINTER(tupleBuf);
    *isMatch = false;

    ChLabelOpenCfgT *openCfg = (ChLabelOpenCfgT *)chRunCtx;
    DrHpCopyExtArgsT *args = (DrHpCopyExtArgsT *)openCfg->userData;
    DrRunCtxT *drRunCtx = args->drRunCtx;
    DmVertexLabelT *vertexLabel = drRunCtx->dsFetchArgs.vertexScanArgs.cltCataLabel->vertexLabel;
    DmVlIndexLabelT *pkIndex = MEMBER_PTR_NO_CHECK(vertexLabel->metaVertexLabel, pkIndex);
    args->cmpInfo = (DsHpFetchAndCmpInfoT){.tpKeyBufInfo = (const DmIndexKeyBufInfoT *)drRunCtx->indexKeyInfo,
        .nullInfoBytes = pkIndex->nullInfoBytes,
        .tpKeyBuf = drRunCtx->keyBuf,
        .idxKeyData = hashKey.keyData,
        .idxkeyLen = hashKey.keyLen,
        .isMatch = false,
        .isV3Mode = drRunCtx->isV3Mode};
    HpReadRowInfoT readRowInfo = {
        .trxId = DB_INVALID_ID64, .isGetSelfDeletedBuf = false, .bufSize = tupleBuf->bufSize, .buf = tupleBuf->buf};
    Status ret = DbGetExternalErrno(DsHeapCmpAndCopyHpTupleBuf(&readRowInfo, (void *)args));
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    *isMatch = args->cmpInfo.isMatch && !args->isAged;
    return args->isAged ? GMERR_NO_DATA : ret;
}

Status MainStoreVertexHashClusterOpen(DrRunCtxT *drRunCtx)
{
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (drRunCtxBase->chRunCtx != NULL) {
        return GMERR_OK;
    }
    if (SECUREC_UNLIKELY(drRunCtxBase->chCtxMem == NULL)) {
        Status ret;
        ContainerInitCfgT allocCfg = {
            .containerType = CONTAINER_CLUSTERED_HASH,
            .seRunCtx = drRunCtxBase->seRunCtxHandle,
        };

        if ((ret = ContainerAllocRunCtx(&drRunCtxBase->chCtxMem, &allocCfg)) != GMERR_OK) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc hash cluster when client open ctx.");
            return ret;
        }
    }
    drRunCtxBase->chRunCtx = (ChLabelRunHdlT)drRunCtxBase->chCtxMem;
    VertexScanArgsT vertexScanArgs = drRunCtx->dsFetchArgs.vertexScanArgs;
    ContainerInitCfgT initCfg = {.containerType = CONTAINER_CLUSTERED_HASH,
        .labelShmAddr = vertexScanArgs.cltCataLabel->vertexLabel->metaVertexLabel->containerShmAddr,
        .seRunCtx = drRunCtxBase->seRunCtxHandle,
        .dmInfo = vertexScanArgs.cltCataLabel->vertexLabel,
        .vertex = vertexScanArgs.vertex,
        .userData = NULL,
        .keyCmp = DsHashClusterKeyCompare,
        .labelLatchVersionId = drRunCtxBase->labelLatchVersionId,
        .labelRWLatch = drRunCtxBase->labelLatch,
        .usrMemCtx = drRunCtxBase->clientMemCtx};
    return ContainerInitRunCtx((ContainerHdlT)drRunCtxBase->chRunCtx, &initCfg);
}

#ifdef ART_CONTAINER

static Status DsArtScanCompare(ArtContainerRunHdlT runHdl, HpTupleAddr addr, uint8_t *buf)
{
    DB_POINTER(runHdl);
    DrHpCopyExtArgsT *args = ((DrHpCopyExtArgsT *)ArtContainerGetUserData(runHdl));
    uint8_t *cpyBufSrc = NULL;
    uint32_t cpyBufSize = 0;
    if (!ArtContainerGetIsRealCluster(runHdl)) {
        uint8_t *bufTmp = (uint8_t *)addr;
        cpyBufSrc = bufTmp + ART_MEMCTX_IDX_SIZE;
        cpyBufSize = ArtContainerGetRowSize(runHdl);
    } else {
        cpyBufSrc = buf;
        cpyBufSize = ArtContainerGetRowSize(runHdl);
    }

    FixBufferT *directReadBuff = args->drRunCtx->directReadBuff;
    Status ret = FixBufPutUint32(directReadBuff, cpyBufSize);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "art copy bufsize, bufSize:%" PRIu32, cpyBufSize);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    ret = FixBufPutData(directReadBuff, cpyBufSrc, cpyBufSize);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "artcopy buf, bufSize:%" PRIu32, cpyBufSize);
        return MEMORY_OPERATE_FAILED_INTER;
    }

    args->filterRet = true;
    return STATUS_OK_INTER;
}

Status MainStoreVertexArtClusterOpen(DrRunCtxT *drRunCtx)
{
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (drRunCtxBase->artRunCtx != NULL) {
        return GMERR_OK;
    }
    Status ret = GMERR_OK;
    if (SECUREC_UNLIKELY(drRunCtxBase->artCtxMem == NULL)) {
        ContainerInitCfgT allocCfg = {
            .containerType = CONTAINER_ART,
            .seRunCtx = drRunCtxBase->seRunCtxHandle,
        };

        if ((ret = ContainerAllocRunCtx(&drRunCtxBase->artCtxMem, &allocCfg)) != GMERR_OK) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc art container when client open ctx.");
            return ret;
        }
    }
    drRunCtxBase->artRunCtx = (ArtContainerRunHdlT)drRunCtxBase->artCtxMem;
    VertexScanArgsT vertexScanArgs = drRunCtx->dsFetchArgs.vertexScanArgs;
    ContainerInitCfgT initCfg = {.containerType = CONTAINER_ART,
        .labelShmAddr = vertexScanArgs.cltCataLabel->vertexLabel->metaVertexLabel->containerShmAddr,
        .seRunCtx = drRunCtxBase->seRunCtxHandle,
        .dmInfo = vertexScanArgs.cltCataLabel->vertexLabel,
        .pkIndex = MEMBER_PTR(vertexScanArgs.cltCataLabel->vertexLabel->metaVertexLabel, pkIndex),
        .vertex = vertexScanArgs.vertex,
        .userData = NULL,
        .keyCmp = NULL,
        .artKeyCmp = DsArtScanCompare,
        .labelLatchVersionId = drRunCtxBase->labelLatchVersionId,
        .labelRWLatch = drRunCtxBase->labelLatch,
        .usrMemCtx = drRunCtxBase->clientMemCtx};
    ret = ContainerInitRunCtx((ContainerHdlT)drRunCtxBase->artRunCtx, &initCfg);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Main store open art cluster");
        return ret;
    }
    ContainerOpenCfgT openCfg = {
        .vertex = vertexScanArgs.vertex,
        .usrMemCtx = drRunCtxBase->clientMemCtx,
    };
    ret = ContainerLabelOpen((ContainerHdlT)drRunCtxBase->artRunCtx, &openCfg);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        ContainerReleaseRunCtx(drRunCtxBase->artCtxMem);
        drRunCtxBase->artCtxMem = NULL;
        drRunCtxBase->artRunCtx = NULL;
        DB_LOG_ERROR(ret, "Open art clustered for directwrite.");
        return ret;
    }
    return ret;
}

#endif
Status MainStoreHandleInner(DrRunCtxT *drRunCtx)
{
    if (drRunCtx->base.isUseClusteredHashTable) {
        return MainStoreVertexHashClusterOpen(drRunCtx);
    } else {
        return MainStoreHeapLabelOpen(drRunCtx, VERTEX_LABEL);
    }
}
