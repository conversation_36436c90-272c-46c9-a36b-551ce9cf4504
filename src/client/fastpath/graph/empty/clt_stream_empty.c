/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: clt empty implement for stream
 * Author:
 * Create: 2024-11-27
 */

#include "clt_graph_dml_edge.h"
#include "clt_graph_ddl_edge.h"
#include "clt_meta_cache.h"
#include "clt_batch.h"
#include "clt_graph_ddl_vertex.h"
#include "clt_edge_topo_dml.h"
#include "gmc_internal.h"

#ifdef __cplusplus
extern "C" {
#endif

Status CltOperateEdge(GmcStmtT *stmt, CltCataLabelT *label, MsgOpcodeRpcE opCode)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status CltPreInsertOrDelEdge(GmcStmtT *stmt, const DmEdgeLabelT *edgeLabel)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status CltSetVertexPkName4Edge(GmcStmtT *stmt, const char *keyName, bool isSrc)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status CltGetEdgeLabel(GmcStmtT *stmt, const char *labelName, CltCataLabelT **edgeLabel)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

void CltCataRemoveEdgeLabelByName(CltCataKeyT *key)
{
    return;
}

void CltCataCloseEdgeLabel(CltCataLabelT *label)
{
    return;
}

Status CltAddBatchEdgeDMLCmd(GmcBatchT *batch, GmcStmtT *stmt, GmcOperationTypeE cmdType, DmEdgeLabelT *edgeLabel)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status GetRelatedLabels(GmcStmtT *stmt, const char *labelName, DbOamapT *vertices, DbOamapT *edges)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status CltDirectFetchNeighborBegin(GmcStmtT *stmt, const char *edgeLabelName)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

int32_t GmcCreateEdgeLabel(GmcStmtT *stmt, const char *labelJson, const char *configJson)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

#ifdef __cplusplus
}
#endif
