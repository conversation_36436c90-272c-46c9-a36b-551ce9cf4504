/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author:
 * Create: 2023-05-18
 */
#ifndef CLT_DA_VERTEX_DIRECTREAD_H
#define CLT_DA_VERTEX_DIRECTREAD_H
#include "gmc_errno.h"
#include "clt_da_handle.h"
#include "clt_da_read.h"
#ifdef __cplusplus
extern "C" {
#endif
Status MainStoreVertexIndexOpen(DrRunCtxT *drRunCtx);

Status VertexGetLabelInfoAndHeapShmAddr(
    const DrRunCtxT *drRunCtx, void **labelInfo, ShmemPtrT *heapShmAddr, bool *isUseRsm);

Status MainStoreVertexPkIdxFetchNext(DrRunCtxT *drRunCtx, uint32_t *fetchedRows);

Status PrepareVertexDirectReadCtx(DrRunCtxT *drRunCtx, CltCataLabelT *cltLabel, GmcOperationTypeE operationType);

Status PrepareVertexDirectReadCtxOpt(
    DrRunStructOptCtxT *drRunCtx, CltCataLabelT *cltLabel, GmcOperationTypeE operationType);

Status DirectReadFetchMainStore(DrRunCtxT *drRunCtx, const DmIndexKeyT *filter, DmVertexT *vertex,
    CltCataLabelT *cltCataLabel, uint32_t *fetchedRows);

Status DirectReadFetchMainStoreOpt(DrRunStructOptCtxT *drRunCtx, const DmIndexKeyT *filter, DmVertexT *vertex,
    CltCataLabelT *cltCataLabel, uint32_t *fetchedRows);

Status DirectReadInitLabelLatch(DirectAccessRunCtxT *runCtxBase, DmVertexLabelT *vertexLabel);

Status DirectReadInitLabelLatchOpt(DirectAccessStructOptRunCtxT *runCtxBase, DmVertexLabelT *vertexLabel);

bool IsStmgMarkDelete(const DmVertexLabelT *vertexLabel, DmVertexDescT *vertexDesc, const HeapTupleBufT *heapTupleBuf);

Status MainStoreFetch(DrRunCtxT *drRunCtx, char *keyBuf, uint32_t keySize, DmVertexT *vertex,
    CltCataLabelT *cltCataLabel, uint32_t *affectRows);

Status MainStoreVertexPkIndexPrepare(DrRunCtxT *drRunCtx);

Status DsHashClusterKeyCompare(
    Handle chRunCtx, IndexKeyT hashKey, const HeapTupleBufT *tupleBuf, int32_t *cmpRet, bool *isMatch);

Status MainStoreVertexScanOpen(DrRunCtxT *drRunCtx, DmLabelTypeE labelType);

Status MainStoreVertexPkIdxOpen(DrRunCtxT *drRunCtx, DmLabelTypeE labelType);

__attribute__((visibility("default"))) Status VertexPrivCheck(DrRunCtxT *drRunCtx, CltCataLabelT *cltLabel);

Status MainStoreVertexScanFetchNext(DrRunCtxT *drRunCtx, DmLabelTypeE labelType, uint32_t *fetchedRows, bool *isEOF);

#ifdef ART_CONTAINER
Status MainStoreContainerCondScanOpen(DrRunCtxT *drRunCtx, DmLabelTypeE labelType);

Status MainStoreContainerFetchNext(DrRunCtxT *drRunCtx, uint32_t *fetchedRows, bool *isEof);
#endif

#ifdef __cplusplus
}
#endif

#endif  // CLT_DA_VERTEX_DIRECTREAD_H
