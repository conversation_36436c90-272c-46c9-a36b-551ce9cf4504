/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: clt_edge_topo_dml.h
 * Description: header file for GMDB client edge topology dml API
 * Author: zhaomingming
 * Create: 2020-08-19
 */

#ifndef CLT_EDGE_TOPO_DML_H
#define CLT_EDGE_TOPO_DML_H

#include "clt_stmt.h"

#ifdef __cplusplus
extern "C" {
#endif

Status CltFetchVertexNeighbors(GmcStmtT *stmt, bool *eof);
GMDB_EXPORT Status CltDirectFetchNeighborBegin(GmcStmtT *stmt, const char *edgeLabelName);

#ifdef __cplusplus
}
#endif

#endif /* CLT_EDGE_TOPO_DML_H */
