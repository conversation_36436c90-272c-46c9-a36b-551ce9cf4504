/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: clt_edge_topo_dml.c
 * Description: Implement of GMDB client edge topology dml API
 * Author: zhaomingming
 * Create: 2020-08-19
 */

#include "clt_edge_topo_dml.h"

#include "clt_check.h"
#include "clt_graph_ddl_edge.h"
#include "clt_graph_filter.h"
#include "clt_stmt_extend.h"
#include "clt_da_read.h"
#include "dm_meta_topo_label.h"
#include "dm_meta_index_label.h"
#include "dm_data_prop.h"
#include "dm_meta_prop_label.h"
#include "ds_concurrency_control.h"
#include "clt_da_handle.h"
#include "se_access_pub.h"
#include "ee_topo_label.h"
#include "se_resource_session_pub.h"
#include "gmc_internal.h"
#include "se_trx.h"

static Status EdgeTopoHeapOpen(
    const GmcStmtT *stmt, CltCataLabelT *dstCataLabel, CltNeighborCtxT *cltNeighborCtx, const DrRunCtxT *drRunCtx)
{
    DmVertexLabelT *dstVertexLabel = dstCataLabel->vertexLabel;
    VertexLabelCommonInfoT *commonInfo = dstVertexLabel->commonInfo;
    cltNeighborCtx->dstVertexLabelLatch = (LabelRWLatchT *)DbShmPtrToAddr(commonInfo->labelLatchShmAddr);
    if (cltNeighborCtx->dstVertexLabelLatch == NULL) {
        DB_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "get vertex label latch, segId: %" PRIu32 ", offset: %" PRIu32,
            commonInfo->labelLatchShmAddr.segId, commonInfo->labelLatchShmAddr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    cltNeighborCtx->dstVertexLabelLatchVersionId = commonInfo->vertexLabelLatchVersionId;
    Status ret = DsAcqLatchForDirectRead(
        cltNeighborCtx->dstVertexLabelLatch, cltNeighborCtx->dstVertexLabelLatchVersionId, cltNeighborCtx->sessionCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {
        .heapShmAddr = commonInfo->heapInfo.heapShmAddr,
        .seRunCtx = drRunCtx->base.seRunCtxHandle,
        .dmInfo = dstVertexLabel,
        .isBackGround = false,
        .isUseRsm = dstVertexLabel->metaCommon.isUseRsm,
    };
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &cltNeighborCtx->heapRunHdl);
    if (ret != GMERR_OK) {
        DsReleaseLatchForDirectRead(cltNeighborCtx->dstVertexLabelLatch, cltNeighborCtx->sessionCtx);
        return ret;
    }
    HeapLabelOpenForDirectRead(cltNeighborCtx->heapRunHdl, stmt->opCtx);
    DsReleaseLatchForDirectRead(cltNeighborCtx->dstVertexLabelLatch, cltNeighborCtx->sessionCtx);
    cltNeighborCtx->vertex = NULL;
    return GMERR_OK;
}

static Status EdgeTopoEdgeOpen(
    GmcStmtT *stmt, DmEdgeLabelT *edgeLabel, uint64_t edgeAddr, CltNeighborCtxT *cltNeighborCtx, DrRunCtxT *drRunCtx)
{
    cltNeighborCtx->edgeLabelLatch = (LabelRWLatchT *)DbShmPtrToAddr(edgeLabel->labelLatchShmAddr);
    if (cltNeighborCtx->edgeLabelLatch == NULL) {
        DB_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "get edge label latch, segId: %" PRIu32 ", offset: %" PRIu32,
            edgeLabel->labelLatchShmAddr.segId, edgeLabel->labelLatchShmAddr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    cltNeighborCtx->edgeLabelLatchVersionId = edgeLabel->edgeLabelLatchVersionId;
    Status ret = DsAcqLatchForDirectRead(
        cltNeighborCtx->edgeLabelLatch, cltNeighborCtx->edgeLabelLatchVersionId, cltNeighborCtx->sessionCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    EdgeTopoOpenCfgT edgeTopoOpenCfg = {
        .edgeTopoIns = edgeLabel->topoShmAddr,
        .seRunCtx = drRunCtx->base.seRunCtxHandle,
        .usrMemCtx = stmt->opCtx,
        .edgeLabel = edgeLabel,
        .opType = FIXED_HEAP_OPTYPE_DIRECTREAD,
    };
    ret = EdgeTopoOpen(&edgeTopoOpenCfg, &cltNeighborCtx->edgeTopoCtx);
    if (ret != GMERR_OK) {
        DsReleaseLatchForDirectRead(cltNeighborCtx->edgeLabelLatch, cltNeighborCtx->sessionCtx);
        return ret;
    }

    StartEdgeT startEdge = {};
    startEdge.edgeLabel = edgeLabel;
    startEdge.startEdge = edgeAddr;
    ret = EdgeTopoOpenCursor(cltNeighborCtx->edgeTopoCtx, &startEdge, EDGE_OUT, &cltNeighborCtx->seCursor, stmt->opCtx);
    DsReleaseLatchForDirectRead(cltNeighborCtx->edgeLabelLatch, cltNeighborCtx->sessionCtx);
    return ret;
}

static Status CltOpenNeighborCtx(GmcStmtT *stmt, DmEdgeLabelT *edgeLabel, uint64_t edgeAddr)
{
    // 注意：因为后续HpRunHdlT需要使用，这里按照旧逻辑未释放dstVertexLabel，可能会导致泄漏
    // 在未来确认了生命周期后，需记得修改。
    CltCataLabelT *dstCataLabel = NULL;
    char *destVertexName = MEMBER_PTR(edgeLabel, destVertexName);
    Status ret = GetVertexLabelByNameWithCache(stmt, destVertexName, DM_SCHEMA_MIN_VERSION, &dstCataLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    // stmt类型即将切换，清除之前的内容
    CltResetStmt(stmt, true);

    // stmtType本应该在成功后设置，但因为异常时外部会reset stmt，
    // 提前设置stmtType可以调用CltClearStmtOperTopo清理资源，因此暂时不改
    stmt->stmtType = CLT_STMT_TYPE_EDGE_TOPO;
    CltNeighborCtxT *cltNeighborCtx = CltGetOperationContext(stmt);
    cltNeighborCtx->cltCataLabel = dstCataLabel;
    DrRunCtxT *drRunCtx = &stmt->drRunCtx;
    SeGetResSessionCtx(drRunCtx->base.seRunCtxHandle, &cltNeighborCtx->sessionCtx);

    ret = EdgeTopoEdgeOpen(stmt, edgeLabel, edgeAddr, cltNeighborCtx, drRunCtx);
    if (ret != GMERR_OK) {
        return ret;
    }

    return EdgeTopoHeapOpen(stmt, dstCataLabel, cltNeighborCtx, drRunCtx);
}

static Status GetStartEdge(GmcStmtT *stmt, DmEdgeLabelT *edgeLabel, uint64_t *edgeAddr)
{
    const CltOperVertexT *op = CltGetOperationContext(stmt);
    DmVertexT *vertex = op->vertex;
    DmVertexLabelT *vertexLabel = op->cltCataLabel->vertexLabel;
    char *vertexMetaName = MEMBER_PTR(&(vertexLabel->metaCommon), metaName);
    char *sourceVertexName = MEMBER_PTR(edgeLabel, sourceVertexName);
    if (strcmp(vertexMetaName, sourceVertexName) != 0) {
        DB_SET_LASTERR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
            "Curr prepared label should be source label of edge. expected: %s, actual: %s", sourceVertexName,
            vertexMetaName);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }

    // 使用 keyId = 0 (在主键存在时为主键，否则为第一个定义的索引)
    Status ret = CltSetIndexId(stmt, 0);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmIndexKeyT *indexKey;
    ret = CltGetSimpleIndexKeyNotNull(stmt, &indexKey);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t fetchedRows = 0;
    ret = DirectFetchVertex(stmt, indexKey, vertex, &fetchedRows);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(fetchedRows == 0)) {
        DB_SET_LASTERR(GMERR_NO_DATA, "no data when direct fetch vertex data.");
        return GMERR_NO_DATA;
    }

    return DmVertexGetFirstEdgeTopoAddrById(vertex, edgeLabel->metaCommon.metaId, edgeAddr);
}

Status CltDirectFetchNeighborBegin(GmcStmtT *stmt, const char *edgeLabelName)
{
#ifdef EXPERIMENTAL_NERGC
    // 直连读写目前不支持Nerg
    if (DbIsTcp()) {
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
#endif
    CltCataLabelT *cataLabel = NULL;
    Status ret = CltGetEdgeLabel(stmt, edgeLabelName, &cataLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint64_t edgeAddr;
    ret = GetStartEdge(stmt, cataLabel->edgeLabel, &edgeAddr);
    if (ret != GMERR_OK) {
        goto E;
    }

    ret = CltOpenNeighborCtx(stmt, cataLabel->edgeLabel, edgeAddr);
    if (ret != GMERR_OK) {
        CltResetStmt(stmt, true);
        // 此处没有return，因为操作成功时，也要继续向下走完成清理操作
    }
E:
    CltCataCloseEdgeLabel(cataLabel);
#ifdef EXPERIMENTAL_NERGC
    if (DbIsTcp()) {
        RemoveRemoteLabelFromCache(stmt);
    }
#endif
    return ret;
}

Status CltFetchVertexNeighbors(GmcStmtT *stmt, bool *eof)
{
    // get one dstVertex
    SeRunCtxHdT seRunCtx = (SeRunCtxHdT)stmt->drRunCtx.base.seRunCtxHandle;
    CltNeighborCtxT *cltNeighborCtx = CltGetOperationContext(stmt);
    HpTupleAddr vertexAddr;
    Status ret = CltConnServerCheck(stmt->conn);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = DsAcqLatchForDirectRead(
        cltNeighborCtx->edgeLabelLatch, cltNeighborCtx->edgeLabelLatchVersionId, cltNeighborCtx->sessionCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = EdgeTopoFetchNextEdge(cltNeighborCtx->edgeTopoCtx, cltNeighborCtx->seCursor, &vertexAddr, eof);
    DsReleaseLatchForDirectRead(cltNeighborCtx->edgeLabelLatch, cltNeighborCtx->sessionCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (*eof) {
        cltNeighborCtx->vertex = NULL;
        return GMERR_OK;
    }

    ret = DsAcqLatchForDirectRead(
        cltNeighborCtx->dstVertexLabelLatch, cltNeighborCtx->dstVertexLabelLatchVersionId, cltNeighborCtx->sessionCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    bool trxOpen = SeTransStartDirectReadCheck(seRunCtx);
    ret = CltChangeSeTransStateBeforeRead(trxOpen, seRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DsReleaseLatchForDirectRead(cltNeighborCtx->dstVertexLabelLatch, cltNeighborCtx->sessionCtx);
        return ret;
    }
    HeapTupleT heapTuple;
    ret = HeapFetchHpTuple(cltNeighborCtx->heapRunHdl, vertexAddr, &heapTuple);
    CltChangeSeTransStateAfterRead(trxOpen, seRunCtx);
    DsReleaseLatchForDirectRead(cltNeighborCtx->dstVertexLabelLatch, cltNeighborCtx->sessionCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    cltNeighborCtx->vertex = heapTuple.deSrlObjHdl.handle;
    return GMERR_OK;
}
