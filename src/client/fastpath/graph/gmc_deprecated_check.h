/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_deprecated_check.h
 * Description: header file for GMDB client gmc deprecated check
 * Create: 2021-9-18
 */

#ifndef GMC_DEPRECATED_CHECK_H
#define GMC_DEPRECATED_CHECK_H

#include "gmc_types.h"
#include "adpt_define.h"
#include "gmc_internal_types.h"
#include "adpt_types.h"

#ifdef __cplusplus
extern "C" {
#endif

Status GmcGetLabelEstiMemInfoCheck(
    GmcStmtT *stmt, const char *labelJson, const char *config<PERSON>son, const GmcMemSizeInfoT *memSizeInfoT);
Status GmcSetScheduleTaskCfgCheck(GmcStmtT *stmt, uint32_t workload, uint32_t duration);
Status GmcSetUserRequestWeightCheck(GmcStmtT *stmt, uint32_t weight, const char *userName, const char *processName);
Status GmcIpsSetUserResourcesLimitCheck(GmcStmtT *stmt, GmcIpsUserResT *userResource);
Status GmcIpsSetConnResourcesLimitCheck(GmcStmtT *stmt, GmcIpsConnResT *connResource);
Status GmcGetVertexLabelDegradeProgressCheck(GmcStmtT *stmt, const char *labelName);
Status GmcSetLogLevelCheck(GmcStmtT *stmt, const char *processName, uint32_t logLevel);
Status GmcSetLogSwitchCheck(GmcStmtT *stmt, const char *processName, uint32_t logSwitch);
Status GmcRsmMigrationCheck(GmcStmtT *stmt, const char *path);

#ifdef __cplusplus
}
#endif

#endif
