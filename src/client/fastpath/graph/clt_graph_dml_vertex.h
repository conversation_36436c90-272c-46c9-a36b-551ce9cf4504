/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: clt_graph_dml_vertex.h
 * Description: header file for GMDB client graph vertex dml API
 * Author: zhaodu
 * Create: 2020-08-04
 */

#ifndef CLT_GRAPH_DML_VERTEX_H
#define CLT_GRAPH_DML_VERTEX_H

#include "clt_stmt.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct CltScanBeginInfo {
    const char *condStr;
    const char *resultSetStr;
} CltScanBeginInfoT;

typedef struct CltChangeStatisticsInfo {
    uint64_t insertStat;
    uint64_t deleteStat;
    uint64_t updateStat;
    uint64_t replaceStat;
    uint64_t mergeStat;
    uint64_t setStat;
    uint64_t removeStat;
    uint64_t insertFailStat;
    uint64_t deleteFailStat;
    uint64_t updateFailStat;
    uint64_t replaceFailStat;
    uint64_t mergeFailStat;
    uint64_t setFailStat;
    uint64_t removeFailStat;
} CltChangeStatisticsInfoT;

bool CheckIfPkField(DmVertexT *vertex, DmVertexLabelT *vertexLabel, const char *name);

Status CltSetVertexProperty(GmcStmtT *stmt, const char *name, GmcDataTypeE type, const void *value, uint32_t size);
Status CltSetVertexPropertyById(GmcStmtT *stmt, uint32_t id, GmcDataTypeE type, const void *value, uint32_t size);
Status CltSetVertexPropertyByIdWithoutType(GmcStmtT *stmt, uint32_t id, const void *value, uint32_t size);

Status CltFetchNeighborVertexByCSMode(GmcStmtT *stmt, bool *eof);
Status CltFetchVertex(GmcStmtT *stmt, bool *eof, CltOperVertexT *operVertex);
Status ProcessGetVertexCountResp(GmcStmtT *stmt, uint64_t *count);
GMDB_EXPORT Status CltSendQueryNeighborsMessage(
    GmcStmtT *stmt, const char *labelName, const char *keyName, const char *condStr);
Status CltBatchGetChangeStatisticsCount(
    GmcStmtT *stmt, uint32_t type, uint32_t labelCount, const char *labels[], uint64_t statistics[]);

bool IsVertexDirectReadScan(const GmcStmtT *stmt);

GMDB_EXPORT Status ExecDeleteVertex(GmcStmtT *stmt);
Status ExecUpdateCheckVersion(GmcStmtT *stmt);

Status ExecInsertReplaceVertexAsync(GmcStmtT *stmt, const GmcAsyncRequestDoneContextT *requestCtx);
Status ExecMergeVertexAsync(GmcStmtT *stmt, const GmcAsyncRequestDoneContextT *requestCtx);
Status ExecDeleteVertexAsync(GmcStmtT *stmt, const GmcAsyncRequestDoneContextT *requestCtx);
Status ExecUpdateVertexAsync(GmcStmtT *stmt, const GmcAsyncRequestDoneContextT *requestCtx);

Status CltMergePutIndex(GmcStmtT *stmt);
Status ExecScanVertexByCS(GmcStmtT *stmt);
Status ExecScanVertexByDS(GmcStmtT *stmt);

Status CltGetVertex(GmcStmtT *stmt);

Status ExecuteWithNoStructDsMode(GmcStmtT *stmt);
Status ExecuteWithNoStructCsMode(GmcStmtT *stmt);
Status ExecuteWithNoStructDwMode(GmcStmtT *stmt);
Status ExecuteWithWriteCacheMode(GmcStmtT *stmt);
Status CltExecDsVertexScan(GmcStmtT *stmt, ProtoHeadT *proto);

Status GetVertexCountSync(GmcStmtT *stmt, uint64_t *count);
Status GetVertexCountDirect(GmcStmtT *stmt, uint64_t *count);

Status CltGetSubOpCode(GmcSubEventTypeE eventType, CltCataLabelT *cltCataLabel, uint32_t *opCode);
Status CltGetScanDataBuf(GmcStmtT *stmt, GmcDlrDataBufT *dataBufT, bool *getEof);
Status CltGetSubDataBuf(
    GmcStmtT *stmt, const GmcSubMsgInfoT *info, uint32_t opCode, GmcDlrDataBufT *dlrData, bool *getEof);
Status CltDlrGetStmgLabelData(
    GmcStmtT *stmt, const GmcSubMsgInfoT *info, uint32_t opCode, GmcDlrDataBufT *dlrData, bool *getEof);
Status GetLabelDegradeProcessResp(GmcStmtT *stmt, uint32_t *degradeProcess);

Status ExecInsertReplaceMergeVertexSync(GmcStmtT *stmt);
Status ExecIsVertexExistDsByNonUniqueKey(GmcStmtT *stmt, DmIndexKeyT *leftKey, bool *isExist);
#ifdef __cplusplus
}
#endif

#endif /* CLT_GRAPH_DML_VERTEX_H */
