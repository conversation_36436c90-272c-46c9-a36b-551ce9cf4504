/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author:
 * Create: 2023-05-18
 */
#include "clt_da_vertex_directread.h"
#include "db_crash_debug.h"
#include "ds_log.h"
#include "ds_concurrency_control.h"
#include "se_resource_session_pub.h"
#include "db_memcpy.h"
#include "clt_da_read_secidx_scan.h"
#include "clt_da_graph.h"
#include "db_secure_msg_buffer.h"
#include "se_chained_hash_index.h"
#include "db_rpc_business_msg.h"
#include "se_trx.h"

Status MainStoreVertexIndexOpen(DrRunCtxT *drRunCtx)
{
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    DmVertexLabelT *vertexLabel = drRunCtx->dsFetchArgs.vertexScanArgs.cltCataLabel->vertexLabel;

    DmVlIndexLabelT *pkIndex = MEMBER_PTR_NO_CHECK(vertexLabel->metaVertexLabel, pkIndex);
    IndexOpenCfgT indexOpenCfg = {
        .seRunCtx = (SeRunCtxHdT)drRunCtxBase->seRunCtxHandle,
        .vertex = drRunCtx->dsFetchArgs.vertexScanArgs.vertex,
        .heapHandle = drRunCtxBase->heapRunCtx,
        .chHandle = drRunCtxBase->chRunCtx,
        .callbackFunc =
            {
                .keyCmp = DsModifyHashCompare,
                .multiVersionKeyCmp = NULL,
                .hpBufCmp = NULL,
                .addrCheck = NULL,
                .addrCheckAndFetch = NULL,
            },
        .indexLabel = NULL,
        .userData = NULL,
        .needCheckIndexSatisfied = false,
        .indexType = pkIndex->idxLabelBase.indexType,
    };

    IndexCtxT *idxCtx = drRunCtxBase->idxCtxMem;
    Status ret = IdxOpen(pkIndex->idxLabelBase.shmAddr, &indexOpenCfg, idxCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "open index.");
        return ret;
    }
    TupleBufInit(&idxCtx->tupleBuf, drRunCtx->base.clientMemCtx);
    drRunCtxBase->pkIndexRunCtx = idxCtx;
    return GMERR_OK;
}

Status VertexGetLabelInfoAndHeapShmAddr(
    const DrRunCtxT *drRunCtx, void **labelInfo, ShmemPtrT *heapShmAddr, bool *isUseRsm)
{
    VertexScanArgsT vertexScanArgs = drRunCtx->dsFetchArgs.vertexScanArgs;
    VertexLabelCommonInfoT *commonInfo =
        (VertexLabelCommonInfoT *)(vertexScanArgs.cltCataLabel->vertexLabel->commonInfo);
    *labelInfo = (void *)(vertexScanArgs.cltCataLabel->vertexLabel);
    *heapShmAddr = commonInfo->heapInfo.heapShmAddr;
    *isUseRsm = vertexScanArgs.cltCataLabel->vertexLabel->metaCommon.isUseRsm;
    return GMERR_OK;
}

Status MainStoreVertexPkIdxFetchNext(DrRunCtxT *drRunCtx, uint32_t *fetchedRows)
{
    if (SECUREC_UNLIKELY(drRunCtx->dsFetchHandle.indexNotOpen)) {  //  索引未被创建，返回没有数据
        *fetchedRows = 0;
        return GMERR_OK;
    }
    HpTupleAddr tupleAddr;
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    bool isFound = false;
    DrHpCopyExtArgsT extArg = {
        .drRunCtx = drRunCtx,
        .vertex = drRunCtx->dsFetchArgs.vertexScanArgs.vertex,
        .cltCataLabel = drRunCtx->dsFetchArgs.vertexScanArgs.cltCataLabel,
        .isAged = false,
        .needCopy = true,
        .checkAge = true,
        .cmpInfo = {0},
    };
    Status ret;
    IndexKeyT hashKey;
    DmIndexKeyGetKeyBuf(drRunCtx->dsFetchArgs.vertexScanArgs.filter, &hashKey.keyData, &hashKey.keyLen);
    if (SECUREC_LIKELY(drRunCtxBase->isUseClusteredHashTable)) {
        ChLabelOpenCfgT *openCfg = (ChLabelOpenCfgT *)drRunCtxBase->chRunCtx;
        openCfg->userData = &extArg;
        ret = ChLabelLookUp(drRunCtxBase->chRunCtx, hashKey, &tupleAddr, &isFound);
    } else {
        IndexCtxT *idxCtx = drRunCtxBase->pkIndexRunCtx;
        idxCtx->idxOpenCfg.userData = &extArg;
        ret = IdxLookup(idxCtx, hashKey, &tupleAddr, &isFound);
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK || !isFound || extArg.isAged)) {
        *fetchedRows = 0;
    } else {  // 读取到数据
        *fetchedRows = 1;
    }
    return GMERR_OK;
}

Status VertexPrivCheck(DrRunCtxT *drRunCtx, CltCataLabelT *cltLabel)
{
    DmVertexLabelT *label = cltLabel->vertexLabel;
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (SECUREC_UNLIKELY(!drRunCtxBase->hasInited)) {
        DB_LOG_AND_SET_LASERR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "Stmt prepared.");
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    DmReusableMetaT *metaInfoAddr = (DmReusableMetaT *)(cltLabel->vertexMetaInfo);
    if (SECUREC_UNLIKELY(metaInfoAddr->vertexLabelId != label->metaCommon.metaId)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_TABLE, "label is dropped already.");
        return GMERR_UNDEFINED_TABLE;
    }
    if (SECUREC_UNLIKELY(drRunCtxBase->privPolicyMode == (uint32_t)PRIV_NON_AUTH)) {
        return GMERR_OK;
    }
    bool isObjPivChanged = IsVertexLabelPrivChanged(cltLabel, drRunCtxBase);
    if (SECUREC_UNLIKELY(isObjPivChanged || drRunCtxBase->sysPrivVersion != drRunCtxBase->role->sysPrivVersion)) {
        return DirectAccessPrivCheck(
            drRunCtxBase, &metaInfoAddr->objPrivilege, CATA_VERTEX_LABEL, SELECTANY_PRIV, DM_OBJ_SELECT_PRIV);
    }
    return GMERR_OK;
}

Status PrivCheck4PrepareVertexOfDirectRead(
    GmcOperationTypeE operationType, DrRunCtxT *drRunCtx, CltCataLabelT *cltLabel, VertexLabelCommonInfoT *commonInfo)
{
    Status ret = GMERR_OK;
    DmVertexLabelT *vertexLabel = cltLabel->vertexLabel;
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (operationType == GMC_OPERATION_SCAN) {
        if (drRunCtxBase->lastPrivPass) {  // 上一次检查通过了，本次检查只看权限有没有变化
            ret = VertexPrivCheck(drRunCtx, cltLabel);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                drRunCtxBase->lastPrivPass = false;
                drRunCtxBase->passPrivType = CLI_INVALID_PRIV;
                DB_LOG_ERROR(ret, "privilege check.");
                return ret;
            }
        } else {
            DmReusableMetaT *metaInfoAddr = (DmReusableMetaT *)DbShmPtrToAddr(commonInfo->metaInfoShm);
            if (metaInfoAddr == NULL) {
                DB_LOG_ERROR(GMERR_INTERNAL_ERROR,
                    "get metaInfoAddr worthless, because transfer shm worthless, segId: %" PRIu32 ", offset: %" PRIu32,
                    commonInfo->metaInfoShm.segId, commonInfo->metaInfoShm.offset);
                return GMERR_INTERNAL_ERROR;
            }
            if (metaInfoAddr->vertexLabelId != vertexLabel->metaCommon.metaId) {
                DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_TABLE, "label is dropped already.");
                return GMERR_UNDEFINED_TABLE;
            }

            ret = DirectAccessPrivCheck(
                &drRunCtx->base, &metaInfoAddr->objPrivilege, CATA_VERTEX_LABEL, SELECTANY_PRIV, DM_OBJ_SELECT_PRIV);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "privilege check.");
                return ret;
            }
            drRunCtxBase->lastPrivPass = true;
        }
    }
    drRunCtxBase->nspPrivVersion = drRunCtxBase->objPrivNsp->objPrivVersion;
    drRunCtxBase->sysPrivVersion = drRunCtxBase->role->sysPrivVersion;
    drRunCtxBase->hasInited = true;
    drRunCtxBase->isUseClusteredHashTable = vertexLabel->metaVertexLabel->containerType == CONTAINER_CLUSTERED_HASH;
    drRunCtxBase->containerType = vertexLabel->metaVertexLabel->containerType;
#ifdef ART_CONTAINER
    drRunCtxBase->isRealCluster = vertexLabel->commonInfo->isArtRealCluster;
#endif
    drRunCtx->isUseHcGlobalLatch = (commonInfo->heapInfo.ccType == CONCURRENCY_CONTROL_READ_UNCOMMIT) &&
                                   (vertexLabel->metaVertexLabel->hcIndexNum > 0);
    return ret;
}

Status PrepareVertexDirectReadCtx(DrRunCtxT *drRunCtx, CltCataLabelT *cltLabel, GmcOperationTypeE operationType)
{
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    DmVertexLabelT *vertexLabel = cltLabel->vertexLabel;
    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
    if (SECUREC_UNLIKELY(commonInfo == NULL)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "prepare vertex direct read ctx worthless, because commonInfo is null.");
        return GMERR_INTERNAL_ERROR;
    }
    if (SECUREC_UNLIKELY(drRunCtxBase->objPrivNsp == NULL)) {
        drRunCtxBase->objPrivNsp = DbShmPtrToAddr(commonInfo->nspObjPrivShmPtr);
        if (drRunCtxBase->objPrivNsp == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED,
                "transfer shmptr to addr, segId: %" PRIu32 ", offset: %" PRIu32, commonInfo->nspObjPrivShmPtr.segId,
                commonInfo->nspObjPrivShmPtr.offset);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    }
    // 直连读权限检查
    return PrivCheck4PrepareVertexOfDirectRead(operationType, drRunCtx, cltLabel, commonInfo);
}

Status PrepareVertexDirectReadCtxOpt(
    DrRunStructOptCtxT *drRunCtx, CltCataLabelT *cltLabel, GmcOperationTypeE operationType)
{
    DmVertexLabelT *vertexLabel = cltLabel->vertexLabel;
    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
    if (SECUREC_UNLIKELY(commonInfo == NULL)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "prepare vertex direct read ctx worthless, because commonInfo is null.");
        return GMERR_INTERNAL_ERROR;
    }
    DirectAccessStructOptRunCtxT *drRunCtxBase = &drRunCtx->base;
    drRunCtxBase->pkIndexRunCtx = NULL;
    drRunCtxBase->heapRunCtx = NULL;
    drRunCtxBase->hasInited = true;
    return GMERR_OK;
}

Status DirectReadFetchMainStore(DrRunCtxT *drRunCtx, const DmIndexKeyT *filter, DmVertexT *vertex,
    CltCataLabelT *cltCataLabel, uint32_t *fetchedRows)
{
#if defined(FEATURE_TS) && !defined(TS_MULTI_INST)
    return GMERR_OK;
#else
#ifdef TS_MULTI_INST
    if (DmVertexLabelIsTsLabel(cltCataLabel->vertexLabel)) {
        return GMERR_OK;
    }
#endif
    DB_POINTER4(drRunCtx, filter, vertex, fetchedRows);
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (SECUREC_UNLIKELY(!drRunCtxBase->isUseClusteredHashTable)) {
        IndexCtxT *idxCtx = (IndexCtxT *)drRunCtxBase->pkIndexRunCtx;
        if (drRunCtxBase->heapRunCtx != NULL && drRunCtxBase->pkIndexRunCtx != NULL &&
            idxCtx->idxMetaCfg.idxType == CHAINED_HASH_INDEX) {
            IndexKeyT key = {.keyData = (uint8_t *)filter->keyBuf, .keyLen = filter->keyBufLen};
            CHIndexPrefetch(idxCtx, key, true);
            idxCtx->idxOpenCfg.vertex = vertex;
        }
    }
    return MainStoreFetch(drRunCtx, (char *)filter->keyBuf, filter->keyBufLen, vertex, cltCataLabel, fetchedRows);
#endif
}

inline static Status DirectReadIdxLookupWithUserCfg(
    IndexCtxT *idxCtx, DrHpCopyExtArgsT *extArg, IndexKeyT hashKey, uint64_t *count)
{
    DB_ASSERT(idxCtx != NULL);
    idxCtx->idxOpenCfg.userData = extArg;
    extArg->needCopy = false;
    Status ret = IdxGetKeyCount(idxCtx, hashKey, count);
    idxCtx->idxOpenCfg.userData = NULL;
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "get key count with user self defined data");
    }
    return ret;
}

static Status DirectReadGetCountForSecondaryIdx(
    DrRunCtxT *drRunCtx, DrHpCopyExtArgsT *extArg, IndexKeyT hashKey, DsScanModeE scanMode, uint64_t *count)
{
    DB_ASSERT(scanMode == DS_SCAN_VERTEX_INDEX_SECONDARY);
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    extArg->checkAge = false;  // 该字段在二级索引中只有LPM索引会使用，并且LPM索引不用检查老化数据
    // clang-format off
    if (SECUREC_LIKELY(
        drRunCtx->isUseHcGlobalLatch && drRunCtx->currSecIndexRunCtx->idxMetaCfg.idxType == HASHCLUSTER_INDEX)) {
        DsAcqHcLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->sessionCtx);
    }
    // clang-format on
    Status ret = DirectReadIdxLookupWithUserCfg(drRunCtx->currSecIndexRunCtx, extArg, hashKey, count);
    // 防止中间出现大对象导致内存占用过多一直不释放
    TupleBufReleaseBigBuf(&drRunCtx->currSecIndexRunCtx->tupleBuf, MAX_CLT_HOLD_HEAP_TUPLEBUF_SIZE);
    // clang-format off
    if (SECUREC_LIKELY(
        drRunCtx->isUseHcGlobalLatch && drRunCtx->currSecIndexRunCtx->idxMetaCfg.idxType == HASHCLUSTER_INDEX)) {
        DsReleaseHcLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->sessionCtx);
    }
    // clang-format on
    return ret;
}

NO_INLINE Status DirectReadGetCount(DrRunCtxT *drRunCtx, uint64_t *count)
{
    if (SECUREC_UNLIKELY(drRunCtx->dsFetchHandle.indexNotOpen)) {
        *count = 0;  //  索引未被创建，返回没有数据
        return GMERR_OK;
    }

    // 通过索引获取记录数
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    IndexKeyT hashKey;
    VertexScanArgsT *vertexScanArgs = &drRunCtx->dsFetchArgs.vertexScanArgs;
    DmIndexKeyGetKeyBuf(vertexScanArgs->filter, &hashKey.keyData, &hashKey.keyLen);
    DrHpCopyExtArgsT extArg = {
        .drRunCtx = drRunCtx, .vertex = vertexScanArgs->vertex, .isAged = false, .needCopy = false, .checkAge = true};
    Status ret;
    DsScanModeE scanMode = drRunCtx->dsFetchArgs.scanMode;
    if (scanMode == DS_LOOKUP_VERTEX_INDEX_PRIMARY) {
        if (SECUREC_LIKELY(drRunCtxBase->isUseClusteredHashTable)) {
            ChLabelOpenCfgT *openCfg = (ChLabelOpenCfgT *)drRunCtxBase->chRunCtx;
            openCfg->userData = &extArg;
            ret = ChLabelGetKeyCount(drRunCtxBase->chRunCtx, hashKey, count);
            openCfg->userData = NULL;
        } else {
            ret = DirectReadIdxLookupWithUserCfg(drRunCtxBase->pkIndexRunCtx, &extArg, hashKey, count);
        }
    } else {
        ret = DirectReadGetCountForSecondaryIdx(drRunCtx, &extArg, hashKey, scanMode, count);
    }
    return ret;
}

Status DirectReadInitLabelLatch(DirectAccessRunCtxT *runCtxBase, DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(runCtxBase, vertexLabel);
    SeGetResSessionCtx(runCtxBase->seRunCtxHandle, &runCtxBase->sessionCtx);
    if (vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_SYSVIEW) {
        return GMERR_OK;
    }
    // 在正常事务/RU/大表锁模式下，直连读均通过表锁进行并发控制
    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
    runCtxBase->labelLatch = (LabelRWLatchT *)DbShmPtrToAddr(commonInfo->labelLatchShmAddr);
    if (SECUREC_UNLIKELY(runCtxBase->labelLatch == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "init label latch, segId: %" PRIu32 ", offset: %" PRIu32,
            commonInfo->labelLatchShmAddr.segId, commonInfo->labelLatchShmAddr.offset);
        return GMERR_INTERNAL_ERROR;
    }
    runCtxBase->labelLatchShmAddr = commonInfo->labelLatchShmAddr;
    runCtxBase->labelLatchVersionId = commonInfo->vertexLabelLatchVersionId;
    return GMERR_OK;
}

Status DirectReadInitLabelLatchOpt(DirectAccessStructOptRunCtxT *runCtxBase, DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(runCtxBase, vertexLabel);
    SeGetResSessionCtx(runCtxBase->seRunCtxHandle, &runCtxBase->sessionCtx);
    if (vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_SYSVIEW) {
        return GMERR_OK;
    }
    // 在正常事务/RU/大表锁模式下，直连读均通过表锁进行并发控制
    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
    runCtxBase->labelLatch = (LabelRWLatchT *)DbShmPtrToAddr(commonInfo->labelLatchShmAddr);
    if (SECUREC_UNLIKELY(runCtxBase->labelLatch == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "init label latch, segId: %" PRIu32 ", offset: %" PRIu32,
            commonInfo->labelLatchShmAddr.segId, commonInfo->labelLatchShmAddr.offset);
        return GMERR_INTERNAL_ERROR;
    }
    runCtxBase->labelLatchShmAddr = commonInfo->labelLatchShmAddr;
    runCtxBase->labelLatchVersionId = commonInfo->vertexLabelLatchVersionId;
    return GMERR_OK;
}

static inline Status MainStoreHashSecureOpen(DrRunCtxT *drRunCtx)
{
    Status ret;
    DirectReadCtxClose(drRunCtx, DIRECT_ACCESS_ONLY_RESET_HEAP);  // 防止重入失败，先close
    if ((ret = MainStoreVertexPkIdxOpen(drRunCtx, VERTEX_LABEL)) != GMERR_OK) {
        DirectReadCtxClose(drRunCtx, DIRECT_ACCESS_ONLY_RESET_HEAP);
        return ret;
    }
    return GMERR_OK;
}

static Status MainStoreVertexIndexOpenOpt(DrRunStructOptCtxT *drRunCtx)
{
    DirectAccessStructOptRunCtxT *drRunCtxBase = &drRunCtx->base;
    DmVertexLabelT *vertexLabel = drRunCtx->dsFetchArgs.vertexScanArgs.cltCataLabel->vertexLabel;
    IndexOpenCfgT indexOpenCfg = {
        .seRunCtx = (SeRunCtxHdT)drRunCtxBase->seRunCtxHandle,
        .vertex = drRunCtx->dsFetchArgs.vertexScanArgs.vertex,
        .heapHandle = drRunCtxBase->heapRunCtx,
        .chHandle = NULL,
        .callbackFunc =
            {
                .keyCmp = DsModifyHashCompareOpt,
                .multiVersionKeyCmp = NULL,
                .hpBufCmp = NULL,
                .addrCheck = NULL,
                .addrCheckAndFetch = NULL,
            },
        .indexLabel = NULL,
        .userData = NULL,
        .needCheckIndexSatisfied = false,
    };

    IndexCtxT *idxCtx = drRunCtxBase->idxCtxMem;
    DmVlIndexLabelT *pkIndex = MEMBER_PTR_NO_CHECK(vertexLabel->metaVertexLabel, pkIndex);
    Status ret = IdxOpen(pkIndex->idxLabelBase.shmAddr, &indexOpenCfg, idxCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "open index.");
        return ret;
    }
    TupleBufInit(&idxCtx->tupleBuf, drRunCtx->base.clientMemCtx);
    drRunCtxBase->pkIndexRunCtx = idxCtx;
    return GMERR_OK;
}

Status MainStoreIndexOpenOpt(DrRunStructOptCtxT *drRunCtx, DmLabelTypeE labelType)
{
    DirectAccessStructOptRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (drRunCtxBase->pkIndexRunCtx != NULL) {  // 使用上次缓存的主键索引
        return GMERR_OK;
    }
    Status ret;
    if (SECUREC_UNLIKELY(drRunCtxBase->idxCtxMem == NULL)) {
        // indexAlloc 函数要求直连读场景传入INDEX_TYPE_MAX，防止再次Open时，之前alloc的index长度不够用
        if ((ret = IdxAlloc((SeRunCtxHdT)drRunCtxBase->seRunCtxHandle, INDEX_TYPE_MAX, &(drRunCtxBase->idxCtxMem))) !=
            GMERR_OK) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc idxCtx when client open index.");
            return ret;
        }
    }
    return MainStoreVertexIndexOpenOpt(drRunCtx);
}

static Status MainStoreVertexPkIndexPrepareOpt(DrRunStructOptCtxT *drRunCtx)
{
    uint8_t *keyBuf = NULL;
    DmIndexKeyBufInfoT *indexKeyInfo = NULL;
    bool v3Mode = false;
    uint32_t indexId;
    DmVertexT *vertex;
    DirectAccessStructOptRunCtxT *drRunCtxBase = &drRunCtx->base;
    DB_POINTER(drRunCtxBase->heapRunCtx);
    indexId = ((IndexCtxT *)(drRunCtxBase->pkIndexRunCtx))->idxMetaCfg.indexId;
    vertex = ((IndexCtxT *)(drRunCtxBase->pkIndexRunCtx))->idxOpenCfg.vertex;
#if DM_MODE_V3
    v3Mode = (drRunCtx->dsFetchArgs.vertexScanArgs.cltCataLabel->vertexLabel->metaVertexLabel->isFixedLabel == true) &&
             drRunCtx->cfgCompatibleV3 &&
             (!DmVertexLabelIsDatalogLabel(drRunCtx->dsFetchArgs.vertexScanArgs.cltCataLabel->vertexLabel));
#endif
    Status ret;
    if (!v3Mode) {
        ret = DmVertexGetIndexKeyInfo(vertex, indexId, &indexKeyInfo);
        if (ret != GMERR_OK) {
            return ret;
        }

        DmVertexGetCompareKeyBuf(vertex, &keyBuf);
    }
    drRunCtx->isV3Mode = v3Mode;
    drRunCtx->keyBuf = keyBuf;
    drRunCtx->indexKeyInfo = indexKeyInfo;
    drRunCtxBase->pkPrepareFlag = true;
    return GMERR_OK;
}

static inline Status MainStoreHashSecureOpenOpt(DrRunStructOptCtxT *drRunCtx)
{
    DirectReadCtxCloseOpt(drRunCtx, DIRECT_ACCESS_ONLY_RESET_HEAP);  // 防止重入失败，先close
    Status ret = MainStoreHeapLabelOpenOpt(drRunCtx, VERTEX_LABEL);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DirectReadCtxCloseOpt(drRunCtx, DIRECT_ACCESS_ONLY_RESET_HEAP);
        return ret;
    }
    ret = MainStoreIndexOpenOpt(drRunCtx, VERTEX_LABEL);
    if (SECUREC_UNLIKELY(ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE)) {  //  索引未被创建过
        drRunCtx->dsFetchHandle.indexNotOpen = true;
        return GMERR_OK;
    } else if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(!drRunCtx->base.pkPrepareFlag)) {
        ret = MainStoreVertexPkIndexPrepareOpt(drRunCtx);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DirectReadCtxCloseOpt(drRunCtx, DIRECT_ACCESS_ONLY_RESET_HEAP);
            return ret;
        }
    }
    return GMERR_OK;
}

Status MainStoreHashOpen(DrRunCtxT *drRunCtx)
{
    Status ret = GMERR_OK;
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (SECUREC_LIKELY((drRunCtxBase->isUseClusteredHashTable))) {
        if (drRunCtxBase->chRunCtx == NULL || drRunCtxBase->pkPrepareFlag == false) {
            ret = MainStoreHashSecureOpen(drRunCtx);
        }
    } else {
        if (drRunCtxBase->heapRunCtx == NULL || drRunCtxBase->pkIndexRunCtx == NULL) {
            ret = MainStoreHashSecureOpen(drRunCtx);
        } else {
            // 如果heap和index的句柄已经创建，则只加锁和记录opencount
            HeapLabelOpenForDirectRead(drRunCtxBase->heapRunCtx, drRunCtxBase->clientMemCtx);
        }
    }
    return ret;
}

static Status MainStoreVertexLookUpHashFetchBuf(
    DrRunCtxT *drRunCtx, const IndexKeyT *hashKey, bool *isFound, DrHpCopyExtHdlT hpCopyExtHdl)
{
    Status ret;
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (SECUREC_LIKELY(drRunCtxBase->isUseClusteredHashTable)) {
        ChLabelOpenCfgT *openCfg = (ChLabelOpenCfgT *)drRunCtxBase->chRunCtx;
        openCfg->vertex = drRunCtx->dsFetchArgs.vertexScanArgs.vertex;
        openCfg->userData = (void *)hpCopyExtHdl;
        HpTupleAddr tupleAddr;
        ret = ChLabelLookUp(drRunCtxBase->chRunCtx, *hashKey, &tupleAddr, isFound);
        drRunCtxBase->trxId = ChLabelGetTrxId(drRunCtxBase->chRunCtx);
    } else {
        // 根据key, 调用索引的接口查找
        IndexCtxT *idxCtx = drRunCtxBase->pkIndexRunCtx;
        idxCtx->idxOpenCfg.userData = (void *)hpCopyExtHdl;
        HpTupleAddr tupleAddr;
        ret = IdxLookup(idxCtx, *hashKey, &tupleAddr, isFound);
        drRunCtxBase->trxId = hpCopyExtHdl->trxId;
    }
    return ret;
}

ALWAYS_INLINE static Status MainStoreVertexLookUpHash(
    DrRunCtxT *drRunCtx, const IndexKeyT *hashKey, bool *isFound, DrHpCopyExtHdlT hpCopyExtHdl)
{
    // 增加表锁
    hpCopyExtHdl->drRunCtx = drRunCtx;
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    SeRunCtxHdT seRunCtx = (SeRunCtxHdT)drRunCtx->base.seRunCtxHandle;
    Status ret =
        DsAcqLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->labelLatchVersionId, drRunCtxBase->sessionCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // 加session锁
    CRASHPOINT(DB_CRASH_EVENT_RLOCK, DB_CRASH_STATE_SESSION_LOCK);
    bool transOpen = SeTransStartDirectReadCheck(seRunCtx);
    ret = CltChangeSeTransStateBeforeRead(transOpen, seRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DsReleaseLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->sessionCtx);
        return ret;
    }
    // check 版本号是否存在
    if (SECUREC_UNLIKELY(drRunCtx->dsFetchArgs.vertexScanArgs.cltCataLabel->vertexLabel->metaCommon.isDeleted)) {
        ret = GMERR_INVALID_PARAMETER_VALUE;
        goto EXIT;
    }

    ret = MainStoreHashOpen(drRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT;
    }

    ret = MainStoreVertexLookUpHashFetchBuf(drRunCtx, hashKey, isFound, hpCopyExtHdl);
    CltChangeSeTransStateAfterRead(transOpen, seRunCtx);
    DsReleaseLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->sessionCtx);
    DirectReadCtxClose(drRunCtx, DIRECT_ACCESS_ONLY_RESET_HEAP);
    return ret;
EXIT:
    CltChangeSeTransStateAfterRead(transOpen, seRunCtx);
    DsReleaseLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->sessionCtx);
    // heap懒加载导致，写入数据前直连读校验返回
    if (ret == GMERR_NO_DATA) {
        *isFound = false;
        ret = GMERR_OK;
    }
    return ret;
}

inline Status MainStoreFetch(DrRunCtxT *drRunCtx, char *keyBuf, uint32_t keySize, DmVertexT *vertex,
    CltCataLabelT *cltCataLabel, uint32_t *affectRows)
{
    Status ret = GMERR_OK;
    IndexKeyT dshashKey = {(uint8_t *)keyBuf, keySize};
    DrHpCopyExtArgsT extArg = {
        .drRunCtx = drRunCtx, .vertex = vertex, .isAged = false, .needCopy = true, .checkAge = true};
    drRunCtx->dsFetchArgs.vertexScanArgs.vertex = vertex;
    drRunCtx->dsFetchArgs.vertexScanArgs.cltCataLabel = cltCataLabel;
    bool isFound = false;
    ret = MainStoreVertexLookUpHash((DrRunCtxT *)drRunCtx, &dshashKey, &isFound, &extArg);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Main Store fetch");
        return ret;
    }

    *affectRows = (!isFound || extArg.isAged) ? 0 : 1;
    return GMERR_OK;
}

Status MainStoreVertexPkIndexPrepare(DrRunCtxT *drRunCtx)
{
    uint8_t *keyBuf = NULL;
    DmIndexKeyBufInfoT *indexKeyInfo = NULL;
    bool v3Mode = false;
    uint32_t indexId;
    DmVertexT *vertex;
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (SECUREC_LIKELY(drRunCtxBase->isUseClusteredHashTable)) {
        DB_POINTER(drRunCtxBase->chRunCtx);
        indexId = ChLabelGetIndexId(drRunCtxBase->chRunCtx);
        vertex = ChLabelGetVertex(drRunCtxBase->chRunCtx);
    } else {
        DB_POINTER(drRunCtxBase->heapRunCtx);
        indexId = ((IndexCtxT *)(drRunCtxBase->pkIndexRunCtx))->idxMetaCfg.indexId;
        vertex = ((IndexCtxT *)(drRunCtxBase->pkIndexRunCtx))->idxOpenCfg.vertex;
    }
#if DM_MODE_V3
    v3Mode = (drRunCtx->dsFetchArgs.vertexScanArgs.cltCataLabel->vertexLabel->metaVertexLabel->isFixedLabel == true) &&
             drRunCtx->cfgCompatibleV3 &&
             (!DmVertexLabelIsDatalogLabel(drRunCtx->dsFetchArgs.vertexScanArgs.cltCataLabel->vertexLabel));
#endif
    Status ret;
    if (!v3Mode) {
        ret = DmVertexGetIndexKeyInfo(vertex, indexId, &indexKeyInfo);
        if (ret != GMERR_OK) {
            return ret;
        }

        DmVertexGetCompareKeyBuf(vertex, &keyBuf);
    }
    if (drRunCtxBase->isUseClusteredHashTable) {
        ChLabelOpenCfgT *openCfg = (ChLabelOpenCfgT *)drRunCtxBase->chRunCtx;
        openCfg->indexKeyInfo = indexKeyInfo;
    }
    drRunCtx->isV3Mode = v3Mode;
    drRunCtx->keyBuf = keyBuf;
    drRunCtx->indexKeyInfo = indexKeyInfo;
    drRunCtxBase->pkPrepareFlag = true;
    return GMERR_OK;
}

Status DsModifyHashCompare(IndexCtxT *idxCtx, IndexKeyT hashKey, const HpTupleAddr addr, int32_t *cmpRet, bool *isMatch)
{
    DB_UNUSED(cmpRet);
    DB_POINTER5(idxCtx, hashKey.keyData, idxCtx->idxHandle, cmpRet, isMatch);
    Handle hpHandle = idxCtx->idxOpenCfg.heapHandle;
    DB_ASSERT(idxCtx->idxOpenCfg.chHandle == NULL);  // 新容器handle应该为空
    *isMatch = false;
    DrHpCopyExtArgsT *args = (DrHpCopyExtArgsT *)idxCtx->idxOpenCfg.userData;
    DrRunCtxT *drRunCtx = args->drRunCtx;
    args->cmpInfo = (DsHpFetchAndCmpInfoT){.tpKeyBufInfo = drRunCtx->indexKeyInfo,
        .nullInfoBytes = idxCtx->idxMetaCfg.nullInfoBytes,
        .tpKeyBuf = drRunCtx->keyBuf,
        .idxKeyData = hashKey.keyData,
        .idxkeyLen = hashKey.keyLen,
        .isMatch = false,
        .isV3Mode = drRunCtx->isV3Mode};

    Status ret = HeapFetchAndProcInplace(hpHandle, addr, DsHeapCmpAndCopyHpTupleBuf, (void *)args);
    if (ret != GMERR_OK) {
        return ret;
    }

    *isMatch = args->cmpInfo.isMatch && !args->isAged;
    return args->isAged ? GMERR_NO_DATA : ret;
}

Status DsModifyHashCompareOpt(
    IndexCtxT *idxCtx, IndexKeyT hashKey, const HpTupleAddr addr, int32_t *cmpRet, bool *isMatch)
{
    DB_UNUSED(cmpRet);
    DB_POINTER5(idxCtx, hashKey.keyData, idxCtx->idxHandle, cmpRet, isMatch);
    Handle hpHandle = idxCtx->idxOpenCfg.heapHandle;
    DB_ASSERT(idxCtx->idxOpenCfg.chHandle == NULL);  // 新容器handle应该为空
    *isMatch = false;
    DrHpCopyExtArgsOptT *args = (DrHpCopyExtArgsOptT *)idxCtx->idxOpenCfg.userData;
    DrRunStructOptCtxT *drRunCtx = args->drRunCtx;
    args->cmpInfo = (DsHpFetchAndCmpInfoT){.tpKeyBufInfo = drRunCtx->indexKeyInfo,
        .nullInfoBytes = idxCtx->idxMetaCfg.nullInfoBytes,
        .tpKeyBuf = drRunCtx->keyBuf,
        .idxKeyData = hashKey.keyData,
        .idxkeyLen = hashKey.keyLen,
        .isMatch = false,
        .isV3Mode = drRunCtx->isV3Mode};

    Status ret = HeapFetchAndProcInplace(hpHandle, addr, DsHeapCmpAndCopyHpTupleBufOpt, (void *)args);
    if (ret != GMERR_OK) {
        return ret;
    }

    *isMatch = args->cmpInfo.isMatch && !args->isAged;
    return args->isAged ? GMERR_NO_DATA : ret;
}

static void MainStoreVertexScanFetchCheckLimit(
    MsHpScanUserDataT *inputData, uint32_t rowIndex, HpScanSubsActT *subsequentAction)
{
    // 直连读扫描支持设置limit count，由GmcSetScanLimit()设置
    if (inputData->limitCount > 0) {
        /* actualFetchedCnt表示截止到本轮扫描开始前，已成功获取到的记录数
         * 每调用一次Scan接口称为一轮扫描（eg.ChLabelScan()）
         * 一轮扫描过程中，不会逐行更新actualFetchedCnt的值；
         * 一轮扫描结束后，会把本轮扫描到的记录数累加到actualFetchedCnt，并带入到下一轮扫描中。
         *
         * remainingCount表示待扫描的条数（按扫描轮次更新，而非逐行更新），若小于等于maxPreFetchNums，说明本轮内就可以完成扫描
         * 因此在扫描到最后一条数据的时候要标记isScanBreak为true，外层的扫描逻辑会基于该标记位结束扫描
         */
        uint64_t remainingCount = inputData->limitCount - inputData->actualFetchedCnt;
        if (remainingCount <= inputData->maxFetchNum) {
            if (remainingCount - 1 == rowIndex) {  // rowIndex表示一轮扫描中所处理的第rowIndex条记录
                subsequentAction->isScanBreak = true;
            }
        }
    }
}

#ifdef FEATURE_HP_ANNSEQSCAN
StatusInter MainStoreVertexScanFetchProcVectorSimilaritySearch(
    MsHpScanUserDataT *inputData, const HeapTupleBufT *tupleBuf, HpScanSubsActT *subsequentAction)
{
    VectorSearchCtxT *vecSearchCtx = inputData->vecSearchCtx;
    float distance = 0;
    uint8_t *dataBuf = NULL;
    bool useQuantization = DmAutoQuantTypeIsUsed(vecSearchCtx->quantPair.type);
    if (vecSearchCtx->offset != DB_INVALID_UINT32) {
        dataBuf = tupleBuf->buf + vecSearchCtx->offset;
    } else {
        DmValueT value = {};
        FilterInnerT *filter = inputData->structFilterList->list[0];
        uint32_t propeId = useQuantization ? vecSearchCtx->quantPair.property : filter->property->propeId;
        Status ret =
            DmVertexBufGetFirstLevelFixedPropById(tupleBuf->buf, inputData->vertex->vertexDesc, propeId, &value);
        if (ret != GMERR_OK) {
            return STATUS_OK_INTER;
        }
        dataBuf = value.value.strAddr;
    }
    if (useQuantization) {
        DbLVQCodeT *lvqCtxData = DbLVQCodeAttach(dataBuf);
        DbLVQCodeT *lvqCtxQuery = DbLVQCodeAttach(vecSearchCtx->calcAddr);
        distance = DbLVQComputeDistance(lvqCtxData, lvqCtxQuery, vecSearchCtx->metric);
    } else {
        FilterInnerT *filter = inputData->structFilterList->list[0];
        uint32_t dim = DmAutoQuantGetDimFromFixedSize(filter->value.value.length);
        distance = DbVectorDistanceFloat(
            dim, (const float *)dataBuf, (const float *)vecSearchCtx->calcAddr, vecSearchCtx->metric);
    }
    uint32_t oldCnt = inputData->vecSearchCtx->prioQue->currentCount;
    Status ret =
        DbTryPushSimilPrioQue(inputData->vecSearchCtx->prioQue, (float)distance, tupleBuf->buf, tupleBuf->bufSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    subsequentAction->isMatched = (oldCnt != inputData->vecSearchCtx->prioQue->currentCount);
    return STATUS_OK_INTER;
}
#endif

PRI_IDX_READ_FUNC StatusInter MainStoreVertexScanFetchProc(const HeapTupleBufAndRowInfoT *heapBuffAndRowInfo,
    uint32_t rowIndex, void *userData, HpScanSubsActT *subsequentAction)
{
    // 默认全部置为false
    subsequentAction->isRollBackScan = false;
    subsequentAction->isMatched = false;
    subsequentAction->isScanBreak = false;
    const HeapTupleBufT *tupleBuf = &heapBuffAndRowInfo->tupleBuf;
    MsHpScanUserDataT *inputData = (MsHpScanUserDataT *)userData;
    CltCataLabelT *cltCataLabel = inputData->cltCataLabel;
    DmVertexLabelT *vertexLabel = cltCataLabel->vertexLabel;
    DmVertexDescT *vertexDesc = cltCataLabel->vertexLabel->vertexDesc;
    DB_ASSERT(inputData->labelType == VERTEX_LABEL);

    // 判断是否是老化数据
    bool isAged = false;
    Status ret = IsVertexBufAged(tupleBuf, cltCataLabel, &isAged, vertexDesc);
    if (ret != GMERR_OK) {  // IsVertexBufAged内部错误：退出扫描，返回错误
        subsequentAction->isScanBreak = true;
        DB_LOG_AND_SET_LASERR(ret, "check if vertex buffer is aged in MainStoreVertexScanFetchProc.");
        return DATA_EXCEPTION_INTER;
    }
    if (isAged) {  // 老化数据：继续扫描
        return STATUS_OK_INTER;
    }

    // 判断是否是stmg订阅的标记删除数据
    bool isDeleteMark = IsStmgMarkDelete(vertexLabel, vertexDesc, tupleBuf);
    if (isDeleteMark) {  // 标记删除数据，继续扫描
        return STATUS_OK_INTER;
    }

#ifdef FEATURE_HP_ANNSEQSCAN
    if (inputData->vecSearchCtx->isAnnSearch) {
        return MainStoreVertexScanFetchProcVectorSimilaritySearch(inputData, tupleBuf, subsequentAction);
    }

#endif

    uint32_t bufSize = tupleBuf->bufSize;
    uint32_t maxLength = FixBufGetMaxLenLimit(inputData->buff);
    if (sizeof(uint32_t) + SIZE_ALIGN4(bufSize) >
        maxLength - FixBufGetPos(inputData->buff)) {  // 写入超过2M：回滚游标，结束扫描
        subsequentAction->isRollBackScan = true;
        subsequentAction->isScanBreak = true;
        return STATUS_OK_INTER;
    }
    if (!DsMatchFilterStructure(tupleBuf, inputData->structFilterList, inputData->vertex)) {
        // 不满足过滤条件
        return STATUS_OK_INTER;
    }
    ret = SecureFixBufPutRawText(inputData->buff, bufSize, tupleBuf->buf);
    if (ret != GMERR_OK) {  // SecureFixBufPut内部错误：退出扫描，返回错误
        subsequentAction->isScanBreak = true;
        DB_LOG_AND_SET_LASERR(ret, "Fix buffer put data in MainStoreVertexScanFetchProc.");
        return DATA_EXCEPTION_INTER;
    }
    // 数据匹配：继续扫描
    subsequentAction->isMatched = true;
    MainStoreVertexScanFetchCheckLimit(inputData, rowIndex, subsequentAction);

    return STATUS_OK_INTER;
}

PRI_IDX_READ_FUNC Status MainStoreVertexScanFetchNext(
    DrRunCtxT *drRunCtx, DmLabelTypeE labelType, uint32_t *fetchedRows, bool *isEOF)
{
    Status ret;
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    HpFetchedAuxInfoT auxInfo = {0};
    MsHpScanUserDataT heapScanUserData = {
        .buff = drRunCtx->directReadBuff,
        .vertex = drRunCtx->dsFetchArgs.vertexScanArgs.vertex,
        .cltCataLabel = drRunCtx->dsFetchArgs.vertexScanArgs.cltCataLabel,
        .labelType = labelType,
        .limitCount = drRunCtx->dsFetchArgs.limitCount,
        .actualFetchedCnt = drRunCtx->dsFetchArgs.totalFetchedRowCnt,  // 说明详见 MainStoreVertexScanFetchProc()
        .maxFetchNum = drRunCtx->dsFetchArgs.preFetchRows,
        .structFilterList = drRunCtx->dsFetchArgs.vertexScanArgs.structFilterList,
    };
#ifdef FEATURE_HP_ANNSEQSCAN
    heapScanUserData.vecSearchCtx = drRunCtx->dsFetchArgs.vertexScanArgs.vecSearchCtx;
#endif
    if (SECUREC_LIKELY(drRunCtxBase->isUseClusteredHashTable)) {
        if (drRunCtxBase->chCursor == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_WRONG_STMT_OBJECT, "cursor has been closed");
            return GMERR_WRONG_STMT_OBJECT;
        }
        // vertex
        ChLabelScanSubsCondDataT scanSubsCondData = {
            .userScanRowProc = MainStoreVertexScanFetchProc,
            .userData = &heapScanUserData,
            .auxInfo = {{0}},
            .userAppendRowIdProc = NULL,
            .getRowId = false,
        };
        ret = ChLabelScan(drRunCtxBase->chRunCtx, drRunCtxBase->chCursor, &scanSubsCondData, &auxInfo);
    } else {
        if (drRunCtx->heapCursor == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_WRONG_STMT_OBJECT, "cursor has been closed");
            return GMERR_WRONG_STMT_OBJECT;
        }
        // vertex or kv
        HpScanSubsCondDataT scanSubsCondData = {
            .userScanRowProc = MainStoreVertexScanFetchProc,
            .userData = &heapScanUserData,
            .auxInfo = {{0}},
        };
        ret = HeapFetchNextWithCond(drRunCtxBase->heapRunCtx, drRunCtx->heapCursor, &scanSubsCondData, &auxInfo);
    }
    *fetchedRows = auxInfo.actualFetchRowsCnt;
    drRunCtx->dsFetchArgs.totalFetchedRowCnt += auxInfo.actualFetchRowsCnt;
    *isEOF = (drRunCtx->dsFetchArgs.totalFetchedRowCnt == drRunCtx->dsFetchArgs.limitCount) ? true : auxInfo.isEof;
    return ret;
}

Status MainStoreVertexScanOpen(DrRunCtxT *drRunCtx, DmLabelTypeE labelType)
{
    DB_ASSERT(labelType == VERTEX_LABEL);
    Status ret = GMERR_OK;
    if (SECUREC_LIKELY(drRunCtx->base.containerType == CONTAINER_CLUSTERED_HASH)) {
        ret = MainStoreVertexHashClusterOpen(drRunCtx);
    } else if (drRunCtx->base.containerType == CONTAINER_HEAP) {
        ret = MainStoreHeapLabelOpen(drRunCtx, labelType);
    }
#ifdef ART_CONTAINER
    else if (drRunCtx->base.containerType == CONTAINER_ART) {
        ret = MainStoreVertexArtClusterOpen(drRunCtx);
    }
#endif
    else {
        DB_ASSERT(false);
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    if (drRunCtx->dsFetchArgs.isDirectGetCount) {
        return GMERR_OK;
    }

    return MainStoreHeapCursorOpen(drRunCtx);
}

#ifdef ART_CONTAINER

// 条件扫描
Status MainStoreContainerCondScanOpen(DrRunCtxT *drRunCtx, DmLabelTypeE labelType)
{
    DB_ASSERT(labelType == VERTEX_LABEL);
    Status ret = GMERR_OK;
    if (SECUREC_LIKELY(drRunCtx->base.containerType == CONTAINER_CLUSTERED_HASH)) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "cond scan in Cluster hash");
        return GMERR_FEATURE_NOT_SUPPORTED;
    } else if (drRunCtx->base.containerType == CONTAINER_HEAP) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "cond scan in heap");
        return GMERR_FEATURE_NOT_SUPPORTED;
    } else if (drRunCtx->base.containerType == CONTAINER_ART) {
        ret = MainStoreVertexArtClusterOpen(drRunCtx);
    } else {
        DB_ASSERT(false);
    }
    if (ret != GMERR_OK) {
        return ret;
    }

    if (drRunCtx->dsFetchArgs.isDirectGetCount) {
        return GMERR_OK;
    }

    return ArtContainerBeginScan(drRunCtx->base.artRunCtx, &drRunCtx->artCursor);
}

static Status ArtGetContructScanCfg(
    DrRunCtxT *drRunCtx, IndexScanCfgT *scanCfg, IndexKeyT *leftHashKey, IndexKeyT *rightHashKey)
{
    DsFetchArgsT *args = &drRunCtx->dsFetchArgs;
    bool isLeftInclude = false;
    bool isRightInclude = false;
    bool isDescend = false;
    uint32_t scanFlags = args->vertexScanArgs.rangeScanFlags;
    DmIndexKeyT *leftFilter = drRunCtx->dsFetchArgs.vertexScanArgs.filter;
    DmIndexKeyT *rightFilter = drRunCtx->dsFetchArgs.vertexScanArgs.rightKey;
    if (leftFilter != NULL) {
        DmIndexKeyGetKeyBuf(leftFilter, (uint8_t **)&(leftHashKey->keyData), &(leftHashKey->keyLen));
    }
    if (rightFilter != NULL) {
        DmIndexKeyGetKeyBuf(rightFilter, (uint8_t **)&(rightHashKey->keyData), &(rightHashKey->keyLen));
    }
    MatchTypeE keyMatchType = INVALID_MATCH;
    if ((scanFlags & CS_RANGE_SCAN_ENABLED) != 0) {
        isLeftInclude = ((scanFlags & CS_RANGE_SCAN_LEFT_INCLUDE) != 0);
        isRightInclude = ((scanFlags & CS_RANGE_SCAN_RIGHT_INCLUDE) != 0);
        isDescend = ((scanFlags & CS_RANGE_SCAN_ASCEND) == 0);
        if ((scanFlags & CS_RANGE_SCAN_PREFIX) != 0) {
            keyMatchType = PREFIX_MATCH;
        } else if ((scanFlags & CS_RANGE_SCAN_RANGE) != 0) {
            keyMatchType = RANGE_MATCH;
        } else if ((scanFlags & CS_RANGE_SCAN_NEAR) != 0) {
            keyMatchType = NEAR_MATCH;
        }
    }
    // 单点查询可以不设置keyMatchType
    if (SECUREC_UNLIKELY(keyMatchType == INVALID_MATCH && rightHashKey->keyData != NULL)) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Art container scan match type");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    IndexScanCfgT scanCfgTmp = {.scanType = GetIndexRangeType(isLeftInclude, isRightInclude),
        .scanDirect = isDescend ? INDEX_SCAN_DESCEND : INDEX_SCAN_ASCEND,
        .leftKey = leftHashKey,
        .rightKey = rightHashKey,
        .scanMode = 0,
        .matchType = keyMatchType,
        .condIdx = args->vertexScanArgs.condIdx,
        .matchBytesNum = args->vertexScanArgs.matchBytesNum,
        .nearAIdx = args->vertexScanArgs.nearAIdx,
        .nearBIdx = args->vertexScanArgs.nearBIdx};
    *scanCfg = scanCfgTmp;
    return GMERR_OK;
}

static Status ArtFetchScan(DrRunCtxT *drRunCtx, bool *isEOF)
{
    if (SECUREC_UNLIKELY(*isEOF)) {
        return GMERR_OK;
    }
    IndexScanCfgT scanCfg = {0};
    IndexKeyT leftHashKey = {0};
    IndexKeyT rightHashKey = {0};
    bool isFound = false;
    if (ArtContainerGetIsFirstScan(drRunCtx->base.artRunCtx)) {
        Status ret = ArtGetContructScanCfg(drRunCtx, &scanCfg, &leftHashKey, &rightHashKey);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    do {
        DrHpCopyExtArgsT *extArg = (DrHpCopyExtArgsT *)(ArtContainerGetUserData(drRunCtx->base.artRunCtx));
        extArg->filterRet = false;

        Status ret = ArtContainerScan(drRunCtx->base.artRunCtx, drRunCtx->artCursor, &isFound, scanCfg);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        if (!isFound) {
            *isEOF = true;
            return GMERR_OK;
        }
        // 找到就退出
        if (SECUREC_LIKELY(extArg->filterRet)) {
            break;
        }
    } while (true);

    return GMERR_OK;
}

Status MainStoreContainerFetchNext(DrRunCtxT *drRunCtx, uint32_t *fetchedRows, bool *isEof)
{
    *fetchedRows = 0;
    // DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (SECUREC_UNLIKELY(drRunCtx->dsFetchHandle.indexNotOpen)) {
        *isEof = true;
        return GMERR_OK;
    }

    uint32_t preFetchRows = drRunCtx->dsFetchArgs.preFetchRows;
    DrHpCopyExtArgsT extArg = {.drRunCtx = drRunCtx,
        .vertex = drRunCtx->dsFetchArgs.vertexScanArgs.vertex,
        .checkAge = true,
        .isAged = false,
        .needCopy = true};
    ArtContainerSetUserData(drRunCtx->base.artRunCtx, &extArg);
    uint32_t maxLength = FixBufGetMaxLenLimit(drRunCtx->directReadBuff);
    uint32_t maxRowSize = 0;
    Status ret = GetMaxRowSize(drRunCtx, maxLength, &maxRowSize);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "in GetMaxRowSize");
        return ret;
    }
    while (*fetchedRows < preFetchRows && maxRowSize <= maxLength - FixBufGetPos(drRunCtx->directReadBuff)) {
        ret = ArtFetchScan(drRunCtx, isEof);
        if (SECUREC_UNLIKELY(ret != GMERR_OK || *isEof)) {
            break;
        }
        // fetchedRows表示"一轮扫描"中获取的行数，是从stmt->cacheRows透传过来的
        (*fetchedRows)++;
        // totalFetchedRowCnt表示扫描到的总行数
        drRunCtx->dsFetchArgs.totalFetchedRowCnt++;
        if (drRunCtx->dsFetchArgs.totalFetchedRowCnt == drRunCtx->dsFetchArgs.limitCount) {
            *isEof = true;
            break;
        }
    }
    return ret;
}
#endif

inline Status MainStoreVertexPkIdxOpen(DrRunCtxT *drRunCtx, DmLabelTypeE labelType)
{
    DB_ASSERT(labelType == VERTEX_LABEL);
    Status ret;
    if (SECUREC_LIKELY(drRunCtx->base.containerType == CONTAINER_CLUSTERED_HASH)) {  // 打开新容器，并且不用打开索引
        // 性能场景下提前返回，配合函数内联大量减少指令并避免栈保护指令
        if (drRunCtx->base.chRunCtx == NULL) {
            ret = MainStoreVertexHashClusterOpen(drRunCtx);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
        }
    } else if (drRunCtx->base.containerType == CONTAINER_HEAP) {
        ret = MainStoreHeapLabelOpen(drRunCtx, labelType);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        ret = MainStoreIndexOpen(drRunCtx, labelType);
        if (SECUREC_UNLIKELY(ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE)) {  //  索引未被创建过
            drRunCtx->dsFetchHandle.indexNotOpen = true;
            return GMERR_OK;
        } else if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
#ifdef ART_CONTAINER
    else if (drRunCtx->base.containerType == CONTAINER_ART) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "the way using single search in Art container");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
#endif
    else {
        DB_ASSERT(false);
    }
    if (SECUREC_UNLIKELY(!drRunCtx->base.pkPrepareFlag)) {
        return MainStoreVertexPkIndexPrepare(drRunCtx);
    }
    return GMERR_OK;
}

static Status MainStoreHashOpenOpt(DrRunStructOptCtxT *drRunCtx)
{
    Status ret = GMERR_OK;
    DirectAccessStructOptRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (drRunCtxBase->pkIndexRunCtx == NULL) {
        ret = MainStoreHashSecureOpenOpt(drRunCtx);
    } else {
        // 如果heap和index的句柄已经创建，则只加锁和记录opencount
        HeapLabelOpenForDirectRead(drRunCtxBase->heapRunCtx, drRunCtxBase->clientMemCtx);
    }

    return ret;
}

static Status MainStoreVertexLookUpHashFetchBufOpt(DirectAccessStructOptRunCtxT *drRunCtxBase, const IndexKeyT *hashKey,
    bool *isFound, DrHpCopyExtOptHdlT hpCopyExtHdl)
{
    // 根据key, 调用索引的接口查找
    IndexCtxT *idxCtx = drRunCtxBase->pkIndexRunCtx;
    idxCtx->idxOpenCfg.userData = (void *)hpCopyExtHdl;
    HpTupleAddr tupleAddr;
    Status ret = IdxLookup(idxCtx, *hashKey, &tupleAddr, isFound);
    drRunCtxBase->trxId = hpCopyExtHdl->trxId;
    return ret;
}

Status MainStoreVertexLookUpHashOpt(
    DrRunStructOptCtxT *drRunCtx, const IndexKeyT *hashKey, bool *isFound, DrHpCopyExtOptHdlT hpCopyExtHdl)
{
    // 增加表锁
    hpCopyExtHdl->drRunCtx = drRunCtx;
    DirectAccessStructOptRunCtxT *drRunCtxBase = &drRunCtx->base;
    Status ret =
        DsAcqLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->labelLatchVersionId, drRunCtxBase->sessionCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // check 版本号是否存在
    if (SECUREC_UNLIKELY(drRunCtx->dsFetchArgs.vertexScanArgs.cltCataLabel->vertexLabel->metaCommon.isDeleted)) {
        ret = GMERR_INVALID_PARAMETER_VALUE;
        goto EXIT;
    }

    ret = MainStoreHashOpenOpt(drRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT;
    }

    ret = MainStoreVertexLookUpHashFetchBufOpt(drRunCtxBase, hashKey, isFound, hpCopyExtHdl);
    DsReleaseLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->sessionCtx);
    HeapRunCtxT *heapRunCtx = (HeapRunCtxT *)drRunCtxBase->heapRunCtx;
    if (heapRunCtx != NULL) {
        HeapLabelResetCtx(heapRunCtx);
        heapRunCtx->staticPageInfo.isUseCache =
            !(drRunCtx->dsFetchArgs.vertexScanArgs.vertex->vertexDesc->commonInfo->hasUpd);
    }
    return ret;
EXIT:
    DsReleaseLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->sessionCtx);
    // heap懒加载导致，写入数据前直连读校验返回
    if (ret == GMERR_NO_DATA) {
        *isFound = false;
        ret = GMERR_OK;
    }
    return ret;
}

static inline Status MainStoreFetchOpt(DrRunStructOptCtxT *drRunCtx, char *keyBuf, uint32_t keySize, DmVertexT *vertex,
    CltCataLabelT *cltCataLabel, uint32_t *affectRows)
{
    Status ret = GMERR_OK;
    IndexKeyT dshashKey = {(uint8_t *)keyBuf, keySize};
    DrHpCopyExtArgsOptT extArg = {
        .drRunCtx = drRunCtx, .vertex = vertex, .isAged = false, .needCopy = true, .checkAge = true};
    drRunCtx->dsFetchArgs.vertexScanArgs.vertex = vertex;
    drRunCtx->dsFetchArgs.vertexScanArgs.cltCataLabel = cltCataLabel;
    bool isFound = false;
    ret = MainStoreVertexLookUpHashOpt(drRunCtx, &dshashKey, &isFound, &extArg);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Main Store fetch");
        return ret;
    }

    *affectRows = (!isFound || extArg.isAged) ? 0 : 1;
    return GMERR_OK;
}

Status DirectReadFetchMainStoreOpt(DrRunStructOptCtxT *drRunCtx, const DmIndexKeyT *filter, DmVertexT *vertex,
    CltCataLabelT *cltCataLabel, uint32_t *fetchedRows)
{
#if defined(FEATURE_TS) && !defined(TS_MULTI_INST)
    return GMERR_OK;
#else
#ifdef TS_MULTI_INST
    if (DmVertexLabelIsTsLabel(cltCataLabel->vertexLabel)) {
        return GMERR_OK;
    }
#endif
    DB_POINTER4(drRunCtx, filter, vertex, fetchedRows);
    DirectAccessStructOptRunCtxT *drRunCtxBase = &drRunCtx->base;
    IndexCtxT *idxCtx = (IndexCtxT *)drRunCtxBase->pkIndexRunCtx;
    if (drRunCtxBase->heapRunCtx != NULL && idxCtx != NULL && idxCtx->idxMetaCfg.idxType == CHAINED_HASH_INDEX) {
        IndexKeyT key = {.keyData = (uint8_t *)filter->keyBuf, .keyLen = filter->keyBufLen};
        CHIndexPrefetch(idxCtx, key, true);
        idxCtx->idxOpenCfg.vertex = vertex;
    }
    return MainStoreFetchOpt(drRunCtx, (char *)filter->keyBuf, filter->keyBufLen, vertex, cltCataLabel, fetchedRows);
#endif
}
