/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_graph_check.c
 * Description: realization of check graph interface of gmdb
 * Create: 2021-08-11
 */
#include "gmc_graph_check.h"

#include "gmc_internal.h"
#include "clt_error.h"
#include "clt_check.h"
#include "clt_meta_cache.h"
#include "clt_graph_vertex_struct.h"
#include "clt_utils.h"
#include "clt_graph_dml_vertex.h"
#include "dm_meta_prop_label.h"

Status GmcCreateLabelCheck(GmcStmtT *stmt, const char *labelJson, const char *config<PERSON>son, GmcConnTypeE expectedConnType)
{
    Status ret = CltStmtBasicCheck(stmt, expectedConnType);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(label<PERSON>son, CLT_MAX_LABEL_LENGTH, "labelJson");
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckConfigJsonLength(configJson);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status GmcAlterVertexLabelConfigCheck(
    GmcStmtT *stmt, const char *vertexLabel, const char *configJson, GmcConnTypeE expectedConnType)
{
    Status ret = CltCheckStringValid(vertexLabel, MAX_TABLE_NAME_LEN, "labelName");
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(configJson, MAX_CONFIG_FILE_SIZE, "new_configJson");
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltStmtBasicCheck(stmt, expectedConnType);
}

Status GmcDuplicateLabelCheck(GmcStmtT *stmt, const char *originLabel, const char *newLabel, const char *configJson,
    GmcConnTypeE expectedConnType)
{
    Status ret = CltStmtBasicCheck(stmt, expectedConnType);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(originLabel, MAX_TABLE_NAME_LEN, "original label name");
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(newLabel, MAX_TABLE_NAME_LEN, "target label name");
    if (ret != GMERR_OK) {
        return ret;
    }
    if (configJson != NULL) {
        ret = CltCheckConfigJsonLength(configJson);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    if (strcmp(originLabel, newLabel) == 0) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "origin label name is same as new.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

Status GmcTruncateVertexCheck(GmcStmtT *stmt, const char *labelName, GmcConnTypeE expectedConnType)
{
    Status ret = CltStmtBasicCheck(stmt, expectedConnType);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(labelName, MAX_TABLE_NAME_LEN, "labelName");
    if (ret != GMERR_OK) {
        return ret;
    }

    // 这里主要是为了检查hpr表，有耦合暂时去不掉，应该放到服务端去检查
    CltCataLabelT *cataVertexLabel = NULL;
    ret = GetVertexLabelByNameWithCache(stmt, labelName, DM_SCHEMA_MIN_VERSION, &cataVertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    CltCataCloseVertexLabel(cataVertexLabel);

    return GMERR_OK;
}

Status GmcDropLabelCheck(GmcStmtT *stmt, const char *labelName, GmcConnTypeE expectedConnType)
{
    Status ret = CltStmtBasicCheck(stmt, expectedConnType);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(labelName, MAX_TABLE_NAME_LEN, "labelName");
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

#define MAX_NEIGHBOR_COUNT 16

Status GmcAppendNeighborCheck(GmcStmtT *stmt, const char *neighborLabelName)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStmtType(stmt, CLT_STMT_TYPE_VERTEX);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltPtrCheckWithErr(neighborLabelName);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (stmt->neighborsInfo.count == MAX_NEIGHBOR_COUNT) {
        DB_SET_LASTERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "too many neighbors");
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    return GMERR_OK;
}

#ifdef FEATURE_STRUCT
Status GmcSetKeyRangeStructureCheck(
    GmcStmtT *stmt, const GmcRangeItemFlagT items[], uint32_t itemNumber, const GmcRangeKeySeriT *rangekeyInfo)
{
    Status res = CltBasicCheckWithStmt(stmt);
    if (res != GMERR_OK) {
        return res;
    }
    res = CltCheckStmtType(stmt, CLT_STMT_TYPE_VERTEX);
    if (res != GMERR_OK) {
        return res;
    }
    if (items == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "items when set key range.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (itemNumber == 0) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "itemNumber is 0.");
        return GMERR_INVALID_VALUE;
    }
    if (rangekeyInfo == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "rangekeyInfo");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if ((rangekeyInfo->leftKeySeri != NULL && rangekeyInfo->leftKeySeri->seriFunc == NULL) ||
        (rangekeyInfo->rightKeySeri != NULL && rangekeyInfo->rightKeySeri->seriFunc == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "seri and inner pointer in seri");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (items->lFlag >= GMC_COMPARE_RANGE_BUTT || items->rFlag >= GMC_COMPARE_RANGE_BUTT) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "items' flag.");
        return GMERR_INVALID_VALUE;
    }
    if (items->order >= GMC_ORDER_BUTT) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "items' order.");
        return GMERR_INVALID_VALUE;
    }
    return GMERR_OK;
}
#endif

Status GmcSetVertexPropertyByIdCheck(GmcStmtT *stmt)
{
    Status ret = CltCheckStmt4SetVertex(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    DB_POINTER(CltGetVertexInStmt(stmt));
    CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = cltVertex->cltCataLabel->vertexLabel;
    // 特殊复杂表限制在插入时使用单个属性设置值
    if (SECUREC_UNLIKELY(
            vertexLabel->metaVertexLabel->labelLevel == VERTEX_LEVEL_SPECIAL &&
            (stmt->operationType == GMC_OPERATION_INSERT || stmt->operationType == GMC_OPERATION_REPLACE))) {
        DB_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "single set by id in special complex table");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    if (SECUREC_UNLIKELY(DmIsYangVertexLabel(vertexLabel))) {
        DB_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "use this interface in yang vertex label");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

Status GmcSetVertexPropertyCheck(GmcStmtT *stmt, const char *name)
{
    Status ret = GmcSetVertexPropertyByIdCheck(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (IsStringEmpty(name)) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "propName");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

inline Status CltCheckStmt4SetVertex(const GmcStmtT *stmt)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (stmt->stmtType == CLT_STMT_TYPE_SUB_VERTEX && stmt->conn->connType == GMC_CONN_TYPE_SUB) {
        // 为支持Datalog全同步特性，需要在订阅回调中使用该接口，故在此条件判断下，直接返回OK
        return GMERR_OK;
    }
    ret = CltCheckStmtType(stmt, CLT_STMT_TYPE_VERTEX);
    return ret;
}

Status CltCheckStmt4GetVertex(const GmcStmtT *stmt)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
    if (stmt->stmtType == CLT_STMT_TYPE_TS) {
        return GMERR_OK;
    }
#endif
    // 性能快路径
    if (SECUREC_LIKELY(stmt->stmtType == CLT_STMT_TYPE_VERTEX || stmt->stmtType == CLT_STMT_TYPE_SUB_VERTEX)) {
        return GMERR_OK;
    }
    switch (stmt->stmtType) {
        case CLT_STMT_TYPE_SUB_PATH:
            // vertex is always not null
            return GMERR_OK;
        case CLT_STMT_TYPE_EDGE_TOPO: {
            const CltOperVertexT *operVertex = CltGetConstOperationContext(stmt);
            // operVertex is a union member, vertex is the first inner-member in this union member.
            if (operVertex->vertex == NULL) {
                DB_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "NULL vertex in stmt.");
                return GMERR_UNEXPECTED_NULL_VALUE;
            }
            return GMERR_OK;
        }
        case CLT_STMT_TYPE_KV:
        case CLT_STMT_TYPE_IDLE:
        case CLT_STMT_TYPE_CELL:
        case CLT_STMT_TYPE_SUB_KV:
        case CLT_STMT_TYPE_EDGE:
        case CLT_STMT_TYPE_STREAM:
#ifdef FEATURE_GQL
        case CLT_STMT_TYPE_SUB_COMPLEX_PATH:
#endif  // FEATURE_GQL
        default:
            DB_SET_LASTERR(GMERR_WRONG_STMT_OBJECT,
                "Curr stmt type %d, "
                "need vertex, neighbors, subscribe vertex or subscribe path action to get vertex.",
                (int32_t)stmt->stmtType);
            return GMERR_WRONG_STMT_OBJECT;
    }
}

Status CltGetVertexPropertyPreCheck(GmcStmtT *stmt)
{
    Status ret = CltCheckStmt4GetVertex(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return CltCheckAndDeseriData(stmt);
}

Status GmcGetVertexPropertySizeByIdCheck(GmcStmtT *stmt, const uint32_t *size)
{
    Status ret = CltGetVertexPropertyPreCheck(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = CltCheckIfUseNewSeri(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (SECUREC_UNLIKELY(size == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'size'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

Status GmcGetVertexPropertySizeByNameCheck(GmcStmtT *stmt, const char *propName, const uint32_t *propSize)
{
    Status ret = CltGetVertexPropertyPreCheck(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (IsStringEmpty(propName)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "propName");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (SECUREC_UNLIKELY(propSize == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'propSize'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    ret = CltCheckIfUseNewSeri(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status GmcGetVertexPropertyByIdCheck(GmcStmtT *stmt, const void *value, const bool *isNull)
{
    Status ret = CltGetVertexPropertyPreCheck(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (SECUREC_UNLIKELY(value == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'value'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (SECUREC_UNLIKELY(isNull == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'isNull'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    ret = CltCheckIfUseNewSeri(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (stmt->fetchEof && ((stmt->stmtType == CLT_STMT_TYPE_VERTEX && stmt->operationType == GMC_OPERATION_SCAN) ||
                              stmt->stmtType == CLT_STMT_TYPE_SUB_VERTEX)) {
        return GMERR_NO_DATA;
    }
    return GMERR_OK;
}

#ifdef FEATURE_VLIVF
ALWAYS_INLINE static bool CltIsValidMetricType(GmcVectorMetricE metric)
{
    return metric == GMC_VECTOR_METRIC_IP || metric == GMC_VECTOR_METRIC_L2 || metric == GMC_VECTOR_METRIC_COSINE;
}

Status GmcGetVecSimilarityCheck(
    GmcStmtT *stmt, const char *propName, float *queryVec, float *dis, GmcVectorMetricE metric)
{
    Status ret = CltGetVertexPropertyPreCheck(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (IsStringEmpty(propName)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'propName'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    ret = CltCheckIfUseNewSeri(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (SECUREC_UNLIKELY(queryVec == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'queryVec'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (SECUREC_UNLIKELY(dis == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'dis'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (SECUREC_UNLIKELY(!CltIsValidMetricType(metric))) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "this metric type");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}
#endif

Status GmcGetVertexPropertyByNameCheck(GmcStmtT *stmt, const char *propName, const void *propValue, const bool *isNull)
{
    Status ret = CltGetVertexPropertyPreCheck(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (IsStringEmpty(propName)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'propName'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    ret = CltCheckIfUseNewSeri(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (SECUREC_UNLIKELY(propValue == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'propValue'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (SECUREC_UNLIKELY(isNull == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'isNull'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    return GMERR_OK;
}

Status GmcVertexExptFieldValueCheck(const GmcStmtT *stmt, const GmcPropValueT *exptValue, const bool *asExpt)
{
    Status ret = CltGetVertexPropertyPreCheck((GmcStmtT *)(uintptr_t)stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (exptValue == NULL) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "Parameter value of 'exptValue'");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (asExpt == NULL) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "Parameter value of 'asExpt'");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (IsStringEmpty(exptValue->propertyName)) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "propName");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

Status GmcGetPrimaryKeyNameCheck(GmcStmtT *stmt, const char *primaryKeyName, uint32_t primaryKeyLength)
{
    Status ret = CltCheckStmt4GetVertex(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (primaryKeyName == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'primaryKeyName'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = operVertex->cltCataLabel->vertexLabel;
    DmVlIndexLabelT *pkIndex = MEMBER_PTR(vertexLabel->metaVertexLabel, pkIndex);
    if (pkIndex == NULL) {
        DB_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "primary key in The vertex label");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t indexNameLength = CltStrLen(MEMBER_PTR(pkIndex, indexName));
    if (primaryKeyLength < indexNameLength) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "primary key name len too small");
        return GMERR_INVALID_VALUE;
    }
    return GMERR_OK;
}

Status GmcGetVertexCountCheck(GmcStmtT *stmt, const char *labelName, const uint64_t *count)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(labelName, MAX_TABLE_NAME_LEN, "labelName");
    if (ret != GMERR_OK) {
        return ret;
    }
    if (SECUREC_UNLIKELY(count == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "count that gmc get vertex count");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

Status GmcGetNeighborsCheck(GmcStmtT *stmt, const char *currentLabelName)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (currentLabelName == NULL) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "currentLabelName");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    ret = CltCheckStmtType(stmt, CLT_STMT_TYPE_VERTEX);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status GmcGetRespVertexMetaCheck(GmcStmtT *stmt, const void *value, const uint32_t *valueLength)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltPtr2CheckWithErr(value, valueLength);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status GmcDirectFetchNeighborBeginCheck(GmcStmtT *stmt, const char *edgeLabelName)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (IsStringEmpty(edgeLabelName)) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "edgeLabelName");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    ret = CltCheckStmtType(stmt, CLT_STMT_TYPE_VERTEX);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 不能有事务开起
    if (stmt->conn->transMode != 0) {
        DB_SET_LASTERR(GMERR_TRANS_MODE_MISMATCH, "stmt conn's transMode.");
        return GMERR_TRANS_MODE_MISMATCH;
    }
    return GMERR_OK;
}

Status GmcSetSuperfieldByNameCheck(GmcStmtT *stmt, const char *superfieldName, const void *spValue)
{
    Status ret = CltCheckStmt4SetVertex(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(superfieldName, DM_MAX_NAME_LENGTH, "superfieldName");
    if (ret != GMERR_OK) {
        return ret;
    }
    if (spValue == NULL) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

Status GmcGetSuperfieldByNameCheck(GmcStmtT *stmt, const char *superfieldName, const void *value)
{
    Status ret = CltCheckStmt4GetVertex(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(superfieldName, DM_MAX_NAME_LENGTH, "superfieldName");
    if (ret != GMERR_OK) {
        return ret;
    }
    if (value == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "value is null.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return CltCheckAndDeseriData(stmt);
}

Status GmcSetSuperfieldByIdCheck(GmcStmtT *stmt, const void *value)
{
    Status ret = CltCheckStmt4SetVertex(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (value == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'value'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

Status GmcGetSuperfieldByIdCheck(GmcStmtT *stmt, const void *value)
{
    Status ret = CltCheckStmt4GetVertex(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (value == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'value'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return CltCheckAndDeseriData(stmt);
}

Status GmcPrepareDirectReadStmtByLabelNameCheck(GmcDirectReadStmtT *stmt, const char *vertexLabelName)
{
    Status ret = CltBasicCheckWithDirectReadStmt(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = CltCheckStringValid(vertexLabelName, MAX_TABLE_NAME_LEN, "vertexLabelName");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return GMERR_OK;
}

ALWAYS_INLINE static bool CltIsYangOnlyOperation(GmcOperationTypeE operationType)
{
    return operationType == GMC_OPERATION_REPLACE_GRAPH || operationType == GMC_OPERATION_DELETE_GRAPH ||
           operationType == GMC_OPERATION_REMOVE_GRAPH || operationType == GMC_OPERATION_NONE;
}

#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
inline static bool IsReady4Operation(TsStmtStatusE status)
{
    return status == TS_STMT_STATUS_PREPARE || status == TS_STMT_STATUS_BIND || status == TS_STMT_STATUS_BULKOP;
}

static bool CltIsTsStmtStatusValid(GmcStmtT *stmt)
{
    if (stmt->tsInfo == NULL || !IsReady4Operation(stmt->tsInfo->stmtStatus) || !stmt->tsInfo->isLabelPrepared) {
        return false;
    }
    CltOperVertexT *cltVertex = (CltOperVertexT *)CltGetOperationContext(stmt);
    if (cltVertex == NULL || cltVertex->cltCataLabel == NULL || cltVertex->cltCataLabel->vertexLabel == NULL) {
        return false;
    }
    DmVertexLabelT *targetTbl = CltGetVertexLabel(stmt);
    if (targetTbl == NULL || (!DmVertexLabelIsTsLabel(targetTbl) && !DmVertexLabelIsNormalLabel(targetTbl))) {
        return false;
    }
    return true;
}
#endif

#ifdef FEATURE_HP_ANNSEQSCAN
Status GmcExecuteAnnSearchCheck(GmcStmtT *stmt)
{
    if (stmt->operationType == GMC_OPERATION_SCAN && stmt->vecSearchCtx.isAnnSearch && stmt->limitCount == 0) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Limit count is 0 for vector similarity search!");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    if (stmt->operationType == GMC_OPERATION_SCAN && stmt->vecSearchCtx.isAnnSearch) {
        CltOperVertexT *operVertex = CltGetOperationContext(stmt);
        if (StructFilterListFilterNum(&operVertex->structFilterList) != 1) {
            DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Vector search use filters!");
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
        if (stmt->limitCount >= stmt->preFetchRows) {
            DB_LOG_AND_SET_LASERR(
                GMERR_FEATURE_NOT_SUPPORTED, "preFetchRows smaller than limit count for vector similarity search!");
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
    }
    return GMERR_OK;
}
#endif

Status GmcExecuteCheck(GmcStmtT *stmt)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
    if (stmt->stmtType != CLT_STMT_TYPE_TS) {
        ret = CltCheckStmtType(stmt, CLT_STMT_TYPE_VERTEX);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    } else {
        if (stmt->tsInfo->stmtStatus == TS_STMT_STATUS_BIND_PARA) {
            return GMERR_OK;
        }
        if (!CltIsTsStmtStatusValid(stmt)) {
            DB_LOG_AND_SET_LASERR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "TS table operation when check execute.");
            return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
        }
    }
#else
    ret = CltCheckStmtType(stmt, CLT_STMT_TYPE_VERTEX);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
#endif
    ret = StmtVertexLabelCheck(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(CltIsYangOnlyOperation(stmt->operationType))) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "type not a yang batch operation!");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

#ifdef FEATURE_HP_ANNSEQSCAN
    ret = GmcExecuteAnnSearchCheck(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
#endif

    return ret;
}

Status GmcExecuteAsyncCheck(GmcStmtT *stmt, const GmcAsyncRequestDoneContextT *requestCtx)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStmtType(stmt, CLT_STMT_TYPE_VERTEX);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (requestCtx == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "requestCtx");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return StmtVertexLabelCheck(stmt);
}

#ifdef FEATURE_STRUCT
Status GmcSetIndexKeyWithBufCheck(const GmcStmtT *stmt, const GmcSeriT *seri)
{
    if (SECUREC_UNLIKELY(stmt == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "stmt");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (SECUREC_UNLIKELY(stmt->stmtType != CLT_STMT_TYPE_VERTEX)) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "stmt type");
        return GMERR_INVALID_VALUE;
    }
    if (SECUREC_UNLIKELY(seri == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "seri");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (SECUREC_UNLIKELY(seri->seriFunc == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "seriFunc");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

Status GmcSetVertexWithBufCheck(const GmcStmtT *stmt, const GmcSeriT *seri)
{
    Status ret = GmcSetIndexKeyWithBufCheck(stmt, seri);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(stmt->operationType > GMC_OPERATION_REPLACE_WITH_RESOURCE &&
                         stmt->operationType != GMC_OPERATION_CHECK_REPLACE)) {
#if defined(FEATURE_STREAM) || defined(FEATURE_TS) || defined(TS_MULTI_INST)
        if (stmt->operationType == GMC_OPERATION_SQL_INSERT) {
            return GMERR_OK;
        }
#endif
        DB_SET_LASTERR(GMERR_INVALID_PROPERTY, "operationType");
        return GMERR_INVALID_PROPERTY;
    }
    return GMERR_OK;
}

Status GmcGetVertexDeseriCheck(const GmcStmtT *stmt, const GmcDeseriT *deseri)
{
    if (SECUREC_UNLIKELY(stmt == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "stmt is null");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (SECUREC_UNLIKELY(stmt->stmtType != CLT_STMT_TYPE_VERTEX && stmt->stmtType != CLT_STMT_TYPE_SUB_VERTEX)) {
        DB_SET_LASTERR(GMERR_WRONG_STMT_OBJECT,
            "Curr stmt type is %d, "
            "need vertex or subscribe vertex action to do this.",
            (int32_t)stmt->stmtType);
        return GMERR_WRONG_STMT_OBJECT;
    }
    if (SECUREC_UNLIKELY(deseri == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "deseri");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (SECUREC_UNLIKELY(deseri->deseriFunc == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "deseriFunc");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

Status GmcGetVertexBufCheck(const GmcStmtT *stmt, const GmcSeriT *keySeri, const GmcStructBufferT *inputBufInfo)
{
    if (SECUREC_UNLIKELY(stmt == NULL)) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "stmt");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (SECUREC_UNLIKELY(stmt->stmtType != CLT_STMT_TYPE_VERTEX && stmt->stmtType != CLT_STMT_TYPE_SUB_VERTEX)) {
        DB_SET_LASTERR(GMERR_WRONG_STMT_OBJECT,
            "Curr stmt type is %" PRIu32 ", need vertex or subscribe vertex action to do this.",
            (uint32_t)stmt->stmtType);
        return GMERR_WRONG_STMT_OBJECT;
    }
    if (SECUREC_UNLIKELY(stmt->stmtType == CLT_STMT_TYPE_VERTEX && stmt->operationType != GMC_OPERATION_SCAN)) {
        DB_SET_LASTERR(GMERR_WRONG_STMT_OBJECT,
            "Curr stmt operation type is %" PRIu32 ", need prepare vertex scan operation to do this.",
            (uint32_t)stmt->operationType);
        return GMERR_WRONG_STMT_OBJECT;
    }
    if (SECUREC_UNLIKELY(inputBufInfo == NULL)) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "inputBufInfo");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}
#endif

/*
 * [CltCheckNodeNameEmpty description]
 * @brief    Clt Check NodeName is empty or not
 * @param    stmt                     [description]
 * @param    nodeNama                 [description]
 * @return   Status                   [description]
 */
static Status CltCheckNodeNameEmpty(const GmcStmtT *stmt, const char *nodeName)
{
    if (IsStringEmpty(nodeName)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'nodeName'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

Status GmcGetPropTypeByNodeNameFieldNameCheck(const GmcStmtT *stmt, const char *nodeName, const char *fieldName,
    const uint32_t *fieldId, const uint32_t *propertyType)
{
    Status ret = CltCheckStmt4GetVertex(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckNodeNameEmpty(stmt, nodeName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(fieldName, MAX_TABLE_NAME_LEN, "fieldName");
    if (ret != GMERR_OK) {
        return ret;
    }
    if (fieldId == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'fieldId'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (propertyType == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'propertyType'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

/*
 * [CltCheckFieldNamePropeTypeNull description]
 * @brief    Clt Check FieldName PropeType is Null or not
 * @param    stmt                     [description]
 * @param    fieldName                [description]
 * @param    propertyType             [description]
 * @return   Status                   [description]
 */
static Status CltCheckFieldNamePropeTypeNull(const GmcStmtT *stmt, const char *fieldName, const uint32_t *propertyType)
{
    if (fieldName == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'fieldName'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (propertyType == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'propertyType'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

Status GmcGetPropTypeByNodeNameFieldIdCheck(const GmcStmtT *stmt, const GmcNodeNameAndPropIdT *nodeNameAndPropId,
    const char *fieldName, const uint32_t *propertyType)
{
    Status ret = CltCheckStmt4GetVertex(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (nodeNameAndPropId == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'nodeNameAndPropId'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    ret = CltCheckNodeNameEmpty(stmt, nodeNameAndPropId->nodeName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckFieldNamePropeTypeNull(stmt, fieldName, propertyType);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status GmcGetPropTypeByKeyNamePropOrderCheck(const GmcStmtT *stmt, const GmcKeyNameAndOrderT *keyNameAndOrder,
    const char *fieldName, const uint32_t *propertyType)
{
    Status ret = CltCheckStmt4GetVertex(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (keyNameAndOrder == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'keyNameAndOrder'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (IsStringEmpty(keyNameAndOrder->keyName)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'keyName'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    ret = CltCheckFieldNamePropeTypeNull(stmt, fieldName, propertyType);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}
Status GmcSetFilterStructureCheck(GmcStmtT *stmt, const GmcFilterStructT *filter)
{
    Status ret = CltCheckStmt4SetVertex(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(stmt->operationType != GMC_OPERATION_SCAN)
#ifdef FEATURE_GQL
        && (stmt->modelType != MODEL_GQL || stmt->operationType != GMC_OPERATION_DELETE)
#endif
    ) {
        DB_SET_LASTERR(GMERR_WRONG_STMT_OBJECT,
            "Curr stmt operation type is %d, "
            "need prepare vertex scan operation to do this.",
            (int32_t)stmt->operationType);
        return GMERR_WRONG_STMT_OBJECT;
    }
    if (SECUREC_UNLIKELY(filter == NULL || filter->value == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "filter.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (SECUREC_UNLIKELY(filter->valueLen == 0 || filter->compOp < (int32_t)GMC_OP_EQUAL ||
                         filter->compOp >= (int32_t)GMC_OP_INVALID)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_VALUE, "filter.");
        return GMERR_INVALID_VALUE;
    }
#ifdef FEATURE_VLIVF
    CltOperVertexT *op = CltGetOperationContext(stmt);
    CltIndexConditionT *idx = &op->idx;
    if (idx->indexLabel != NULL && idx->indexLabel->idxLabelBase.indexType == VLIVF_INDEX) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "set key range in Vlivf index.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
#endif
#ifdef FEATURE_HP_ANNSEQSCAN
    if (SECUREC_UNLIKELY(filter->compOp >= GMC_OP_IP_VECTOR_SIMILARITY &&
                         filter->compOp <= GMC_OP_COSINE_VECTOR_SIMILARITY && filter->nodeName != NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_VALUE, "vector similarity search filter and nodeName.");
        return GMERR_INVALID_VALUE;
    }
#endif
    return ret;
}

Status GmcAlterLabelCheck(const GmcStmtT *stmt, const char *labelJson, const bool isCompatible, const char *labelName,
    GmcConnTypeE expectedConnType)
{
    Status ret = CltStmtBasicCheck(stmt, expectedConnType);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(!isCompatible)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_VALUE, "incompatibe in alter vertexlabel.");
        return GMERR_INVALID_VALUE;
    }
    ret = CltCheckLabelNameLength(labelName);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltCheckStringValid(labelJson, CLT_MAX_LABEL_LENGTH, "labelJson");
}

bool IsDataSyncLabel(const DmVertexLabelT *vertexLabel)
{
    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
    if (SECUREC_UNLIKELY(commonInfo == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "check vertexlabel is data sync label for null commonInfo.");
        return false;
    }
    return commonInfo->dlrInfo.isDataSyncLabel;
}

bool IsStatusMergeLabelSyncFinish(const SubPushSingleLabel *sub)
{
    return DmIsLabelSupportStatusMerge(sub->cltCataLabel->vertexLabel) && sub->dlrSubCtx.isFinish;
}

bool IsDlrDataBufInit(GmcDlrDataBufT *dataBuf)
{
    if (dataBuf->bufSize <= DLR_BUF_HEADER_ALIGN_SIZE) {
        return false;
    }
    CltDlrDataBufHeaderT *bufHeader = (CltDlrDataBufHeaderT *)dataBuf->buf;
    if (bufHeader->magic == DLR_DATA_BUFF_MAGIC_NUM && bufHeader->isInitial) {
        return true;
    }
    return false;
}

Status GmcGetScanDlrCheck(const GmcStmtT *stmt, GmcDlrDataBufT *dataBufT, bool *getEof)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltPtr3CheckWithErr(stmt->conn, dataBufT, getEof);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (SECUREC_UNLIKELY(dataBufT->buf == NULL || dataBufT->bufSize == 0)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "buf");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (stmt->stmtType != CLT_STMT_TYPE_VERTEX) {
        DB_LOG_AND_SET_LASERR(
            GMERR_FEATURE_NOT_SUPPORTED, "stmt type %" PRIu32 " when get scan recv buf!", (uint32_t)stmt->stmtType);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    if (!IsDlrDataBufInit(dataBufT)) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "dlr databuf uninitialized");
        return GMERR_INVALID_VALUE;
    }
    const CltOperVertexT *operVertex = CltGetConstOperationContext(stmt);
    if (operVertex->vertexFetchType == CLT_FETCH_VERTEX_NEIGHBOR) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "vertexFetchType %" PRIu32 "when get scan recv buf!",
            (uint32_t)CLT_FETCH_VERTEX_NEIGHBOR);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    if (SECUREC_UNLIKELY(dataBufT->buf == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "buf address");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    if (!IsDataSyncLabel(operVertex->cltCataLabel->vertexLabel)) {
        DB_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "dlr not in data sync label.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    if (DmIsLabelSupportStatusMerge(operVertex->cltCataLabel->vertexLabel)) {
        DB_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "dlrScan not in status merge label.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

Status GmcGetSubDlrCheck(const GmcStmtT *stmt, const GmcSubMsgInfoT *info, GmcDlrDataBufT *dataBufT, bool *getEof)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltPtr3CheckWithErr(stmt->conn, info, dataBufT);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (SECUREC_UNLIKELY(dataBufT->buf == NULL || dataBufT->bufSize == 0)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "buf");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (SECUREC_UNLIKELY(getEof == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "getEof address");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    if (!IsDlrDataBufInit(dataBufT)) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "dlr databuf uninitialized");
        return GMERR_INVALID_VALUE;
    }

    const SubPushSingleLabel *subSingleLabel = CltGetConstOperationContext(stmt);
    if (subSingleLabel->cltCataLabel->vertexMetaInfo == NULL) {
        // 仅支持sub vertex
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "dlr not in vertex type.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    if (DmIsLabelSupportStatusMerge(subSingleLabel->cltCataLabel->vertexLabel) && !subSingleLabel->isStatusMerge) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "dlr stmg label not in stmg subscription.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    if (IsStatusMergeLabelSyncFinish(subSingleLabel)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_VALUE, "dlr sub has been finished");
        return GMERR_INVALID_VALUE;
    }
    // Stream multi inst should fix here
#if defined(FEATURE_STREAM) || defined(STREAM_MULTI_INST)
    return GMERR_OK;
#endif
    if (!IsDataSyncLabel(subSingleLabel->cltCataLabel->vertexLabel)) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "dlr not in data sync label.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

static Status DlrSubMsgTypeCheckForUpdateEvent(uint16_t msgType, GmcSubEventTypeE eventType, bool isDatalogSub)
{
    if (isDatalogSub) {
        if (!(((msgType & DM_SUBS_MSG_OLD_DATA) > 0) && ((msgType & DM_SUBS_MSG_NEW_DATA) > 0))) {
            DB_LOG_AND_SET_LASERR(
                GMERR_FEATURE_NOT_SUPPORTED, "Datalog sub event type %" PRIu32 ".", (uint32_t)eventType);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
    } else {
        if (!(((msgType & DM_SUBS_MSG_KEY_DATA) > 0) && ((msgType & DM_SUBS_MSG_DELTA_DATA) > 0))) {
            DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "sub event type %" PRIu32 ".", (uint32_t)eventType);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
    }

    return GMERR_OK;
}

Status DlrSubMsgTypeAndEventTypeCheck(
    uint16_t msgType, GmcSubEventTypeE eventType, bool isStatusMerge, bool isDatalogSub)
{
    Status ret = GMERR_OK;
    switch (eventType) {
        case GMC_SUB_EVENT_REPLACE:
        case GMC_SUB_EVENT_INSERT:
        case GMC_SUB_EVENT_REPLACE_INSERT:
        case GMC_SUB_EVENT_MERGE:
        case GMC_SUB_EVENT_MERGE_INSERT:
        case GMC_SUB_EVENT_REPLACE_UPDATE:
        case GMC_SUB_EVENT_MERGE_UPDATE:
            if (!((msgType & DM_SUBS_MSG_NEW_DATA) > 0) || isStatusMerge) {
                DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "sub event type %" PRIu32 ".", (uint32_t)eventType);
                return GMERR_FEATURE_NOT_SUPPORTED;
            }
            break;
        case GMC_SUB_EVENT_UPDATE:
            ret = DlrSubMsgTypeCheckForUpdateEvent(msgType, eventType, isDatalogSub);
            break;
        case GMC_SUB_EVENT_DELETE:
        case GMC_SUB_EVENT_AGED:
            if (!isStatusMerge && !((msgType & DM_SUBS_MSG_KEY_DATA) > 0)) {
                DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "sub event type %" PRIu32 ".", (uint32_t)eventType);
                return GMERR_FEATURE_NOT_SUPPORTED;
            }
            break;
        case GMC_SUB_EVENT_INITIAL_LOAD:
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
        case GMC_SUB_EVENT_MODIFY:
            if (!((msgType & DM_SUBS_MSG_NEW_DATA) > 0) || !isStatusMerge) {
                DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "sub event type %" PRIu32 ".", (uint32_t)eventType);
                return GMERR_FEATURE_NOT_SUPPORTED;
            }
            break;
        default:
            DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "sub event type %" PRIu32 ".", (uint32_t)eventType);
            return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return ret;
}

Status GmcPrefetchLabelsCheck(
    const GmcStmtT *stmt, const char **labelNames, const uint32_t length, GmcPrefetchLabelsDoneT userCb)
{
    if (DbIsHpeEnv()) {
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_ASYNC);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(labelNames == NULL || length == 0)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_VALUE, "labelNames can not be null or length can not be 0.");
        return GMERR_INVALID_VALUE;
    }
    if (SECUREC_UNLIKELY(userCb == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "user callback can not be null.");
        return GMERR_INVALID_VALUE;
    }
    return GMERR_OK;
}

inline Status CltCheckAndDeseriData(GmcStmtT *stmt)
{
    if (SECUREC_UNLIKELY(stmt->stmtType != CLT_STMT_TYPE_VERTEX)) {
        return GMERR_OK;
    }

    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    if (operVertex->doDeseri || stmt->columnCount != 0) {
        return GMERR_OK;
    }

    return CltGetVertex(stmt);
}

Status GmcVertexIsExistCheck(GmcStmtT *stmt, bool *isExist)
{
    Status ret = GmcExecuteCheck(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (isExist == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "isExist can not be null");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}
