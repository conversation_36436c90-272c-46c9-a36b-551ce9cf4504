/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_graph_check.h
 * Description: header file for GMDB client gmc graph check
 * Create: 2021-8-21
 */

#ifndef GMC_GRAPH_CHECK_H
#define GMC_GRAPH_CHECK_H
#include "gmc_yang_types.h"
#include "gmc_dlr.h"
#include "adpt_define.h"
#include "adpt_types.h"

#ifdef __cplusplus
extern "C" {
#endif

GMDB_EXPORT Status GmcCreateLabelCheck(
    GmcStmtT *stmt, const char *labelJson, const char *config<PERSON>son, GmcConnTypeE expectedConnType);

GMDB_EXPORT Status GmcAlterVertexLabelConfigCheck(
    GmcStmtT *stmt, const char *vertexLabel, const char *config<PERSON><PERSON>, GmcConnTypeE expectedConnType);

GMDB_EXPORT Status GmcDuplicateLabelCheck(GmcStmtT *stmt, const char *originLabel, const char *newLabel,
    const char *configJson, GmcConnTypeE expectedConnType);

Status GmcTruncateVertexCheck(GmcStmtT *stmt, const char *labelName, GmcConnTypeE expectedConnType);

GMDB_EXPORT Status GmcDropLabelCheck(GmcStmtT *stmt, const char *labelName, GmcConnTypeE expectedConnType);

GMDB_EXPORT Status GmcAppendNeighborCheck(GmcStmtT *stmt, const char *neighborLabelName);

Status GmcSetKeyRangeStructureCheck(
    GmcStmtT *stmt, const GmcRangeItemFlagT items[], uint32_t itemNumber, const GmcRangeKeySeriT *rangekeyInfo);

Status GmcSetVertexPropertyByIdCheck(GmcStmtT *stmt);

Status GmcSetVertexPropertyCheck(GmcStmtT *stmt, const char *name);

Status CltCheckStmt4SetVertex(const GmcStmtT *stmt);

Status CltCheckStmt4GetVertex(const GmcStmtT *stmt);

Status CltGetVertexPropertyPreCheck(GmcStmtT *stmt);

Status GmcGetVertexPropertySizeByIdCheck(GmcStmtT *stmt, const uint32_t *size);

Status GmcGetVertexPropertySizeByNameCheck(GmcStmtT *stmt, const char *propName, const uint32_t *propSize);

Status GmcGetVertexPropertyByIdCheck(GmcStmtT *stmt, const void *value, const bool *isNull);

Status GmcGetVertexPropertyByNameCheck(GmcStmtT *stmt, const char *propName, const void *propValue, const bool *isNull);

GMDB_EXPORT Status GmcVertexExptFieldValueCheck(
    const GmcStmtT *stmt, const GmcPropValueT *exptValue, const bool *asExpt);

Status GmcGetPrimaryKeyNameCheck(GmcStmtT *stmt, const char *primaryKeyName, uint32_t primaryKeyLength);

Status GmcGetVertexCountCheck(GmcStmtT *stmt, const char *labelName, const uint64_t *count);

GMDB_EXPORT Status GmcGetNeighborsCheck(GmcStmtT *stmt, const char *currentLabelName);

GMDB_EXPORT Status GmcGetRespVertexMetaCheck(GmcStmtT *stmt, const void *value, const uint32_t *valueLength);

GMDB_EXPORT Status GmcDirectFetchNeighborBeginCheck(GmcStmtT *stmt, const char *edgeLabelName);

Status GmcSetSuperfieldByNameCheck(GmcStmtT *stmt, const char *superfieldName, const void *spValue);

Status GmcGetSuperfieldByNameCheck(GmcStmtT *stmt, const char *superfieldName, const void *value);

Status GmcSetSuperfieldByIdCheck(GmcStmtT *stmt, const void *value);

Status GmcGetSuperfieldByIdCheck(GmcStmtT *stmt, const void *value);

Status GmcPrepareDirectReadStmtByLabelNameCheck(GmcDirectReadStmtT *stmt, const char *vertexLabelName);

Status GmcExecuteCheck(GmcStmtT *stmt);

Status GmcExecuteAsyncCheck(GmcStmtT *stmt, const GmcAsyncRequestDoneContextT *requestCtx);

Status GmcSetIndexKeyWithBufCheck(const GmcStmtT *stmt, const GmcSeriT *seri);

Status GmcSetVertexWithBufCheck(const GmcStmtT *stmt, const GmcSeriT *seri);

Status GmcGetVertexDeseriCheck(const GmcStmtT *stmt, const GmcDeseriT *deseri);

Status GmcGetVertexBufCheck(const GmcStmtT *stmt, const GmcSeriT *keySeri, const GmcStructBufferT *inputBufInfo);

Status GmcStmtAndNodeNameCheck(const GmcStmtT *stmt, const char *nodeName);

Status GmcGetPropTypeByNodeNameFieldNameCheck(const GmcStmtT *stmt, const char *nodeName, const char *fieldName,
    const uint32_t *fieldId, const uint32_t *propertyType);

Status GmcGetPropTypeByNodeNameFieldIdCheck(const GmcStmtT *stmt, const GmcNodeNameAndPropIdT *nodeNameAndPropId,
    const char *fieldName, const uint32_t *propertyType);

Status GmcGetPropTypeByKeyNamePropOrderCheck(const GmcStmtT *stmt, const GmcKeyNameAndOrderT *keyNameAndOrder,
    const char *fieldName, const uint32_t *propertyType);

Status GmcSetFilterStructureCheck(GmcStmtT *stmt, const GmcFilterStructT *filter);

Status GmcStmtCheckInput(GmcStmtT *stmt);

Status GmcAlterLabelCheck(const GmcStmtT *stmt, const char *labelJson, const bool isCompatible, const char *labelName,
    GmcConnTypeE expectedConnType);

Status GmcGetScanDlrCheck(const GmcStmtT *stmt, GmcDlrDataBufT *dataBufT, bool *getEof);
Status GmcGetSubDlrCheck(const GmcStmtT *stmt, const GmcSubMsgInfoT *info, GmcDlrDataBufT *dataBufT, bool *getEof);
Status DlrSubMsgTypeAndEventTypeCheck(
    uint16_t msgType, GmcSubEventTypeE eventType, bool isStatusMerge, bool isDatalogSub);
Status GmcPrefetchLabelsCheck(
    const GmcStmtT *stmt, const char **labelNames, const uint32_t length, GmcPrefetchLabelsDoneT userCb);
Status GmcVertexIsExistCheck(GmcStmtT *stmt, bool *isExist);

#ifdef FEATURE_VLIVF
Status GmcGetVecSimilarityCheck(
    GmcStmtT *stmt, const char *propName, float *queryVec, float *dis, GmcVectorMetricE metric);
#endif

#ifdef __cplusplus
}
#endif

#endif
