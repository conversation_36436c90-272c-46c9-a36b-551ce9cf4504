/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: clt_graph_dml_vertex.c
 * Description: execute funcs
 * Create: 2024-10-14
 */

#include "db_secure_msg_buffer.h"
#include "dm_yang_interface.h"
#include "clt_stmt.h"
#include "clt_stmt_extend.h"
#include "clt_msg.h"
#include "clt_fill_msg.h"
#include "clt_graph_dml_vertex.h"
#include "clt_da_vertex_directread.h"
#include "clt_da_write.h"
#include "clt_graph_filter.h"
#include "clt_write_cache.h"
#include "clt_resource_pool.h"

static Status CltSendStructureInsertMsg(GmcStmtT *stmt, MsgOpcodeRpcE opCode)
{
    stmt->isStructure = false;
    // 结构化接口报文已经填充完成，这里只需要发送即可
    GmcConnT *conn = stmt->conn;
    if (stmt->isDataService) {
        MsgHeaderT *header = RpcPeekMsgHeader(&conn->sendPack);
        CltSetDataServiceHeaderInfo(header, opCode);
    }
    Status ret = CltSendRequest(conn, &conn->sendPack);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    Status result = CltRecvStmtResponseWithCheckOpStatus(stmt);
    if (!stmt->isDataService && SECUREC_UNLIKELY(result != GMERR_OK)) {
        return result;
    }
    ret = SecureFixBufGetUint32(&stmt->recvPack, &stmt->affectRows);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "get affectRows when receive vertex dml message.");
        return result != GMERR_OK ? result : ret;
    }

    ret = CltStmtSetDtlErrorFromResponse(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return result != GMERR_OK ? result : ret;
    }
    return result;
}

Status ExecInsertReplaceMergeVertexSync(GmcStmtT *stmt)
{
    MsgOpcodeRpcE op = MSG_OP_RPC_INSERT_VERTEX;
    if (stmt->operationType == GMC_OPERATION_REPLACE || stmt->operationType == GMC_OPERATION_REPLACE_WITH_RESOURCE) {
        op = MSG_OP_RPC_REPLACE_VERTEX;
    } else if (stmt->operationType == GMC_OPERATION_MERGE) {
        op = MSG_OP_RPC_MERGE_VERTEX;
#ifdef FEATURE_VLIVF
    } else if (stmt->operationType == GMC_OPERATION_LOAD_INDEX) {
        op = MSG_OP_RPC_LOAD_INDEX;
#endif
    }

    GmcConnT *conn = stmt->conn;
    conn->cltOpSegTime.opType = (uint32_t)op;

    // GmcSetVertexWithBufCheck限制了这个标记为true时，一定是insert/replace操作
    if (stmt->isStructure) {
        return CltSendStructureInsertMsg(stmt, op);
    }

    CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
    DmVertexT *vetex = cltVertex->vertex;

    DmGetBitMapPropInfo(vetex);

    Status ret = (op != MSG_OP_RPC_MERGE_VERTEX) ? DmCheckBitMapConstrict(vetex) : CltMergePutIndex(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (op != MSG_OP_RPC_MERGE_VERTEX) {
        // insert和replace在非结构化时做memberKey校验
        ret = DmVertexConstraintMemberKeyCheck(vetex);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    // execute operation
    InsertVertexProtoT proto = {};
    proto.protoHead.opCode = op;
    proto.operateEdgeFlag = stmt->operateEdgeFlag;
    proto.vertex = vetex;
    CltOperVertexT *opVertex = CltGetOperationContext(stmt);
    proto.cltCataLabel = opVertex->cltCataLabel;
    DmVertexLabelT *vertexLabel = opVertex->cltCataLabel->vertexLabel;
    if (DmIsListVertexLabel(vertexLabel)) {
        ret = CltGetPosIndexKey(stmt, proto.vertex->position, &proto.posKey);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return CltStmtSendAndRecvAffectedRows(stmt, &proto.protoHead);
}

static void VertexDMLCallbackWrapper(GmcConnT *conn, const AsyncMsgContextT *ctx, Status ret, const void *out)
{
    DB_POINTER3(conn, ctx, out);
    uint64_t requestEndTime = DbGettimeMonotonicUsec();
    uint64_t requestStartTime = ctx->requestStartTime;
    CommonDMLCallback(conn, ctx, ret, out);
    CltSetTimeCost(conn, requestStartTime, requestEndTime);
}

#ifdef FEATURE_RESPOOL_CLI
static void VertexDMLResourceCallback(GmcConnT *conn, const AsyncMsgContextT *ctx, Status ret, const void *out)
{
    GmcResourceInfoT resInfo = {};
    typedef void (*AsyncDMLWithResourceDoneT)(void *userData,  //
        uint32_t affectedRows, GmcResourceInfoT *resInfo, Status status, const char *errMsg);

    if (ret != GMERR_OK) {
        const TextT *error = DbGetLastErrorInfo();
        InvokeUserCallback(AsyncDMLWithResourceDoneT, ctx->userCb, ctx->userData, 0, &resInfo, ret, error->str);
        return;
    }

    uint32_t affectRows = *(const uint32_t *)out;
    FixBufferT *rsp = &conn->recvPack;
    uint32_t seekPos = FixBufGetSeekPos(rsp);  // 记录获取number之前的pos以便于获取resid信息

    uint32_t resIdNum = 0;
    Status res = ExecGetResIdNum(rsp, NULL, &resIdNum);
    if (res != GMERR_OK) {
        InvokeUserCallback(AsyncDMLWithResourceDoneT, ctx->userCb, ctx->userData,  //
            affectRows, &resInfo, res, "unable to get resInfo");
        return;
    }

    if (resIdNum == 0) {
        InvokeUserCallback(AsyncDMLWithResourceDoneT, ctx->userCb, ctx->userData,  //
            affectRows, &resInfo, GMERR_OK, NULL);
        return;
    }
    // 本函数内申请释放
    uint64_t *resIdBuf = DbDynMemCtxAlloc(conn->memCtx, sizeof(uint64_t) * resIdNum);
    if (resIdBuf == NULL) {
        InvokeUserCallback(AsyncDMLWithResourceDoneT, ctx->userCb, ctx->userData,  //
            affectRows, &resInfo, GMERR_OUT_OF_MEMORY, "unable to get resInfo");
        return;
    }

    FixBufSeek(rsp, seekPos);  // 报文回滚回到获取number之前，从头获取residBuf信息
    uint32_t bufIndex = 0;
    res = ExecGetResIdInfo(rsp, NULL, resIdNum, resIdBuf, &bufIndex);
    if (res != GMERR_OK) {
        DbDynMemCtxFree(conn->memCtx, resIdBuf);  // 失败场景下内存指针没有传递到外面，并且会返回错误码，不用置空
        InvokeUserCallback(AsyncDMLWithResourceDoneT, ctx->userCb, ctx->userData,  //
            affectRows, &resInfo, res, "unable to get resInfo");
        return;
    }

    resInfo.resIdBuf = resIdBuf;
    resInfo.resIdNum = resIdNum;
    InvokeUserCallback(AsyncDMLWithResourceDoneT, ctx->userCb, ctx->userData, affectRows, &resInfo, GMERR_OK, NULL);
    DbDynMemCtxFree(conn->memCtx, resIdBuf);  // 只在函数内部使用，内存指针没有往外传递，不用置空
}

static void VertexDMLResourceCallbackWrapper(GmcConnT *conn, const AsyncMsgContextT *ctx, Status ret, const void *out)
{
    DB_POINTER3(conn, ctx, out);
    uint64_t requestEndTime = DbGettimeMonotonicUsec();
    uint64_t requestStartTime = ctx->requestStartTime;
    VertexDMLResourceCallback(conn, ctx, ret, out);
    CltSetTimeCost(conn, requestStartTime, requestEndTime);
}
#endif

static Status MakeAsyncCtxAndOpcode(
    GmcStmtT *stmt, const GmcAsyncRequestDoneContextT *requestCtx, AsyncMsgContextT *ctx, MsgOpcodeRpcE *opCode)
{
    switch (stmt->operationType) {
        case GMC_OPERATION_INSERT:
        case GMC_OPERATION_SQL_INSERT:
            *opCode = MSG_OP_RPC_INSERT_VERTEX;
            *ctx = MakeAsyncMsgContext(PARSE_AFFECTED_ROWS, VertexDMLCallbackWrapper, requestCtx->insertCb,
                requestCtx->userData, stmt->notDistribute);
            break;
        case GMC_OPERATION_REPLACE:
            *opCode = MSG_OP_RPC_REPLACE_VERTEX;
            *ctx = MakeAsyncMsgContext(PARSE_AFFECTED_ROWS, VertexDMLCallbackWrapper, requestCtx->replaceCb,
                requestCtx->userData, stmt->notDistribute);
            break;
#ifdef FEATURE_RESPOOL_CLI
        case GMC_OPERATION_INSERT_WITH_RESOURCE:
            *opCode = MSG_OP_RPC_INSERT_VERTEX;
            *ctx = MakeAsyncMsgContext(PARSE_AFFECTED_ROWS, VertexDMLResourceCallbackWrapper,
                requestCtx->insertWithResourceCb, requestCtx->userData, stmt->notDistribute);
            break;
        case GMC_OPERATION_REPLACE_WITH_RESOURCE:
            *opCode = MSG_OP_RPC_REPLACE_VERTEX;
            *ctx = MakeAsyncMsgContext(PARSE_AFFECTED_ROWS, VertexDMLResourceCallbackWrapper,
                requestCtx->replaceWithResourceCb, requestCtx->userData, stmt->notDistribute);
            break;
#endif
        default:
            DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "unsupport opType: %" PRId32 ".", stmt->operationType);
            return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

static Status ExecInsertReplaceWithStructure(GmcStmtT *stmt, MsgOpcodeRpcE opCode, AsyncMsgContextT *ctx)
{
    GmcConnT *conn = stmt->conn;
    FixBufferT *fixBuffer = &conn->sendPack;
    if (stmt->isDataService) {
        MsgHeaderT *header = RpcPeekMsgHeader(fixBuffer);
        CltSetDataServiceHeaderInfo(header, opCode);
    }
    Status ret = CltSendRequestAsync(conn, fixBuffer, ctx);
    if (ret != GMERR_OK) {
        FreeRemoteLabelIdArr(ctx);
        return ret;
    }
    stmt->isStructure = false;
    FreeRemoteLabelIdArr(ctx);
    return ret;
}

Status ExecInsertReplaceVertexAsync(GmcStmtT *stmt, const GmcAsyncRequestDoneContextT *requestCtx)
{
    AsyncMsgContextT ctx;
    MsgOpcodeRpcE opCode;
    Status ret = MakeAsyncCtxAndOpcode(stmt, requestCtx, &ctx, &opCode);
    if (ret != GMERR_OK) {
        return ret;
    }
#ifdef EXPERIMENTAL_NERGC
    if (DbIsTcp()) {
        ret = FillLabelIdInCtx(stmt, &ctx);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
#endif

    // GmcSetVertexWithBufCheck限制了这个标记为true时，一定是insert/replace操作
    if (stmt->isStructure) {
        return ExecInsertReplaceWithStructure(stmt, opCode, &ctx);
    }

    CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
    DmVertexT *vertex = cltVertex->vertex;
    DmGetBitMapPropInfo(vertex);

    ret = DmCheckBitMapConstrict(vertex);
    if (ret != GMERR_OK) {
        FreeRemoteLabelIdArr(&ctx);
        return ret;
    }

    // insert和replace在非结构化时做memberKey校验
    ret = DmVertexConstraintMemberKeyCheck(vertex);
    if (ret != GMERR_OK) {
        FreeRemoteLabelIdArr(&ctx);
        return ret;
    }

    // execute operation
    InsertVertexProtoT proto = {};
    proto.protoHead.opCode = opCode;
    proto.operateEdgeFlag = stmt->operateEdgeFlag;
    proto.vertex = vertex;
    proto.cltCataLabel = cltVertex->cltCataLabel;
    Status reqRet = CltRequestAsync(stmt->conn, ProtoBuildReq, &proto, &ctx);
    FreeRemoteLabelIdArr(&ctx);
    return reqRet;
}

Status ExecMergeVertexAsync(GmcStmtT *stmt, const GmcAsyncRequestDoneContextT *requestCtx)
{
    Status ret = CltMergePutIndex(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
    DmVertexT *vertex = cltVertex->vertex;
    DmGetBitMapPropInfo(vertex);

    // execute operation
    InsertVertexProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_MERGE_VERTEX;
    proto.operateEdgeFlag = stmt->operateEdgeFlag;
    proto.vertex = vertex;
    proto.cltCataLabel = cltVertex->cltCataLabel;
    AsyncMsgContextT ctx = MakeAsyncMsgContext(
        PARSE_AFFECTED_ROWS, CommonDMLCallback, requestCtx->mergeCb, requestCtx->userData, stmt->notDistribute);
#ifdef EXPERIMENTAL_NERGC
    if (DbIsTcp()) {
        ret = FillLabelIdInCtx(stmt, &ctx);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
#endif
    Status reqRet = CltRequestAsync(stmt->conn, ProtoBuildReq, &proto, &ctx);
    FreeRemoteLabelIdArr(&ctx);
    return reqRet;
}

static Status ExecDeleteVertexSync(GmcStmtT *stmt)
{
    CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = cltVertex->cltCataLabel->vertexLabel;

    // execute operation
    GmcConnT *conn = stmt->conn;
    conn->cltOpSegTime.opType = MSG_OP_RPC_DELETE_VERTEX;
    DeleteVertexProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_DELETE_VERTEX;
    proto.vertexLabel = vertexLabel;
    proto.condString = cltVertex->filter;
    proto.operateEdgeFlag = stmt->operateEdgeFlag;
    Status ret = CltGetRangedIndexKey(stmt, &proto.rangeScanFlag, &proto.leftIndexKey, &proto.rightIndexKey);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = CltStmtSendAndRecvAffectedRows(stmt, &proto.protoHead);
    return ret;
}

Status ExecDeleteVertex(GmcStmtT *stmt)
{
    return ExecDeleteVertexSync(stmt);
}

Status ExecDeleteVertexAsync(GmcStmtT *stmt, const GmcAsyncRequestDoneContextT *requestCtx)
{
    CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = cltVertex->cltCataLabel->vertexLabel;

    // prepare data
    StatusAndPtrT ret = CltGetSimpleIndexKey(stmt);
    if (ret.status != GMERR_OK) {
        return ret.status;
    }
    DmIndexKeyT *filter = (DmIndexKeyT *)ret.ptr;

    // execute operation
    DeleteVertexProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_DELETE_VERTEX;
    proto.vertexLabel = vertexLabel;
    proto.leftIndexKey = filter;
    proto.condString = cltVertex->filter;
    proto.operateEdgeFlag = stmt->operateEdgeFlag;
    AsyncMsgContextT ctx = MakeAsyncMsgContext(
        PARSE_AFFECTED_ROWS, CommonDMLCallback, requestCtx->deleteCb, requestCtx->userData, stmt->notDistribute);
#ifdef EXPERIMENTAL_NERGC
    if (DbIsTcp()) {
        ret.status = FillLabelIdInCtx(stmt, &ctx);
        if (ret.status != GMERR_OK) {
            return ret.status;
        }
    }
#endif
    Status reqRet = CltRequestAsync(stmt->conn, ProtoBuildReq, &proto, &ctx);
    FreeRemoteLabelIdArr(&ctx);
    return reqRet;
}

static Status ExecUpdateVertexSync(GmcStmtT *stmt)
{
    CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
    DmVertexT *vertex = cltVertex->vertex;

    // prepare data
    StatusAndPtrT ret = CltGetSimpleIndexKey(stmt);
    if (SECUREC_UNLIKELY(ret.status != GMERR_OK)) {
        return ret.status;
    }
    DmIndexKeyT *filter = (DmIndexKeyT *)ret.ptr;
    DmGetBitMapPropInfo(vertex);

    // execute operation
    GmcConnT *conn = stmt->conn;
    conn->cltOpSegTime.opType = MSG_OP_RPC_UPDATE_VERTEX;
    UpdateVertexProtoT proto = {
        .protoHead.opCode = MSG_OP_RPC_UPDATE_VERTEX,
        .vertex = vertex,
        .uniqueIndexKey = filter,
        .condStr = cltVertex->filter,
        .operateEdgeFlag = stmt->operateEdgeFlag,
        .cltCataLabel = cltVertex->cltCataLabel,
    };
    return CltStmtSendAndRecvAffectedRows(stmt, &proto.protoHead);
}

Status ExecUpdateVertexAsync(GmcStmtT *stmt, const GmcAsyncRequestDoneContextT *requestCtx)
{
    CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
    DmVertexT *vertex = cltVertex->vertex;

    // prepare data
    StatusAndPtrT ret = CltGetSimpleIndexKey(stmt);
    if (ret.status != GMERR_OK) {
        return ret.status;
    }
    DmIndexKeyT *filter = (DmIndexKeyT *)ret.ptr;
    DmGetBitMapPropInfo(vertex);

    // execute operation
    UpdateVertexProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_UPDATE_VERTEX;
    proto.vertex = vertex;
    proto.uniqueIndexKey = filter;
    proto.condStr = cltVertex->filter;
    proto.operateEdgeFlag = stmt->operateEdgeFlag;
    proto.cltCataLabel = cltVertex->cltCataLabel;
    AsyncMsgContextT ctx = MakeAsyncMsgContext(
        PARSE_AFFECTED_ROWS, VertexDMLCallbackWrapper, requestCtx->updateCb, requestCtx->userData, stmt->notDistribute);
#ifdef EXPERIMENTAL_NERGC
    if (DbIsTcp()) {
        ret.status = FillLabelIdInCtx(stmt, &ctx);
        if (ret.status != GMERR_OK) {
            return ret.status;
        }
    }
#endif
    Status reqRet = CltRequestAsync(stmt->conn, ProtoBuildReq, &proto, &ctx);
    FreeRemoteLabelIdArr(&ctx);
    return reqRet;
}

Status ExecUpdateCheckVersion(GmcStmtT *stmt)
{
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = operVertex->cltCataLabel->vertexLabel;
    Status ret;
    // prepare data
    DmIndexKeyT *filter;
    ret = CltGetSimpleIndexKeyNotNull(stmt, &filter);
    if (ret != GMERR_OK) {
        return ret;
    }

    // execute operation
    GmcConnT *conn = stmt->conn;
    conn->cltOpSegTime.opType = MSG_OP_RPC_UPDATE_CHECK_VERSION;
    UpdateCheckVersionProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_UPDATE_CHECK_VERSION;
    proto.vertexLabelId = vertexLabel->metaCommon.metaId;
    proto.indexKey = filter;
    ret = CltStmtSendAndRecvMsg(stmt, &proto.protoHead);
    return ret;
}

static Status CheckReplaceGetPkIndex(GmcStmtT *stmt, uint8_t **keyBuf, uint32_t *keySize)
{
    DB_POINTER3(stmt, keyBuf, keySize);
    CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
    DmVertexT *vertex = cltVertex->vertex;
    DmVlIndexLabelT *pkIndex = MEMBER_PTR(cltVertex->cltCataLabel->vertexLabel->metaVertexLabel, pkIndex);
    if (SECUREC_UNLIKELY(pkIndex == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "null primary key index.");
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t pkIndexId = pkIndex->idxLabelBase.indexId;

    Status ret;
    if (stmt->isStructure) {
        DmIndexKeyBufInfoT *indexKeyInfo;
        ret = DmVertexGetIndexKeyInfo(vertex, pkIndexId, &indexKeyInfo);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "get index key info when check replace");
            return ret;
        }

        // 使用stmt上的seriVertexBuf用来找到vertexBuf的位置
        uint8_t *vertexBuf = (uint8_t *)cltVertex->seriVertexBuf.str;
        bool ifPkSet =
            DmVertexBufIsAllSetKeyBufProp(indexKeyInfo, cltVertex->cltCataLabel->vertexLabel->vertexDesc, vertexBuf);
        if (!ifPkSet) {
            DB_LOG_AND_SET_LASERR(
                GMERR_DATA_EXCEPTION, "primary key is not all set when check replace with struct replace");
            return GMERR_DATA_EXCEPTION;
        }
        DmVertexGetCompareKeyBuf(vertex, keyBuf);
        DmGetKeyBufFromVertexBuf(indexKeyInfo, vertexBuf, *keyBuf, keySize);
    } else {
        bool ifPkSet = DmVertexPkPropeIsAllSet(vertex);
        if (!ifPkSet) {
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "primary key is not all set when check replace");
            return GMERR_DATA_EXCEPTION;
        }

        ret = DmGetKeyBufFromVertex(vertex, pkIndexId, keyBuf, keySize);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "get pk index buf.");
            return ret;
        }
    }
    return GMERR_OK;
}

static Status AllocStmtAndInitLabel(GmcStmtT *stmt, GmcStmtT **readStmt)
{
    CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = cltVertex->cltCataLabel->vertexLabel;
    GmcStmtT *tmpStmt;
    Status ret = AllocStmt(stmt->conn, &tmpStmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "alloc read stmt for check replace");
        return ret;
    }
    // 直连读拿的数据的表版本号需要和用户check_replace时prepare的一致
    uint32_t labelVersionId = vertexLabel->metaCommon.version;
    char *vertexLabelName = MEMBER_PTR(vertexLabel, metaCommon.metaName);
    ret = PrepareStmtForNewVertexLabel(tmpStmt, vertexLabelName, labelVersionId, GMC_OPERATION_SCAN);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        FreeStmt(tmpStmt);
        DB_LOG_ERROR(ret, "prepare read stmt for check replace");
        return ret;
    }
    // 需要填上
    DrRunCtxT *drRunCtx = &tmpStmt->drRunCtx;
    drRunCtx->directReadBuff = &tmpStmt->recvPack;
    drRunCtx->base.isUseClusteredHashTable = vertexLabel->metaVertexLabel->containerType == CONTAINER_CLUSTERED_HASH;
    drRunCtx->base.containerType = vertexLabel->metaVertexLabel->containerType;
#ifdef ART_CONTAINER
    drRunCtx->base.isRealCluster = vertexLabel->commonInfo->isArtRealCluster;
#endif
    // 直连读连接server是否正常检查
    ret = CltConnServerCheck(stmt->conn);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        FreeStmt(tmpStmt);
        return ret;
    }
    *readStmt = tmpStmt;
    return GMERR_OK;
}

static Status CheckFieldIsSame(GmcStmtT *stmt, GmcStmtT *readStmt, bool *isSame)
{
    // 拿到数据，进行比较，使用用户的比较函数,结构化非结构化只有这一个分支
    CltOperVertexT *currOpVertex = CltGetOperationContext(stmt);
    CltOperVertexT *readOpVertex = CltGetOperationContext(readStmt);
    Status ret = GMERR_OK;
    if (currOpVertex->compareFunc != NULL) {
        readOpVertex->doDeseri = false;
        *isSame = currOpVertex->compareFunc(readStmt, currOpVertex->cmpUserData);
        return ret;
    }
    // 使用默认比较函数，结构化和非结构化两个分支
    DmVertexT *currVertex = currOpVertex->vertex;
    DmVertexT *readVertex = readOpVertex->vertex;

    // 如果是结构化，需要从stmt上获取vertexbuf并反序列化成vertex
    if (stmt->isStructure) {
        TextT *seriVertexBuf = &currOpVertex->seriVertexBuf;
        DB_POINTER(seriVertexBuf->str);
        ret = DmDeSerialize2ExistsVertex(
            (uint8_t *)seriVertexBuf->str, seriVertexBuf->len, currVertex, CltGetSecurityMode());
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_AND_SET_LASERR(ret, "deserialize vertex when check if same");
            return ret;
        }
    }
    // 从readStmt上获取vertexbuf并反序列化成vertex
    ret = CltGetVertex(readStmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "get vertex from direct read buf");
        return ret;
    }
    ret = DmVertexIsSame(currVertex, readVertex, isSame);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "check current vertex with read vertex");
    }
    return ret;
}

static Status DirectReadFetchVertex(
    GmcStmtT *stmt, uint8_t *keyBuf, uint32_t keySize, uint32_t *affectRow, uint64_t *trxId)
{
    DB_POINTER2(stmt, keyBuf);
    DrRunCtxT *drRunCtx = &stmt->drRunCtx;
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    Status ret =
        MainStoreFetch(drRunCtx, (char *)keyBuf, keySize, operVertex->vertex, operVertex->cltCataLabel, affectRow);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "check replace get old data when direct read");
        return ret;
    }
    *trxId = drRunCtx->base.trxId;
    return GMERR_OK;
}

static Status ExecCheckReplaceSyncSend(GmcStmtT *stmt, uint64_t trxId, uint8_t *keyBuf, uint32_t keySize)
{
    DB_POINTER2(stmt, keyBuf);
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = operVertex->cltCataLabel->vertexLabel;
    // execute operation
    GmcConnT *conn = stmt->conn;
    conn->cltOpSegTime.opType = MSG_OP_RPC_CHECK_REPLACE;
    CheckReplaceProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_CHECK_REPLACE;
    proto.vertexLabelId = vertexLabel->metaCommon.metaId;
    proto.keySize = keySize;
    proto.keyBuf = (char *)keyBuf;
    proto.trxId = trxId;
    return CltStmtSendAndRecvMsg(stmt, &proto.protoHead);
}

static Status ExecCheckReplaceSync(GmcStmtT *stmt)
{
    DB_POINTER(stmt);
    uint8_t *keyBuf = NULL;
    uint32_t keySize = 0;
    // 1. 获取主键字段，主键没设置全，报错。
    Status ret = CheckReplaceGetPkIndex(stmt, &keyBuf, &keySize);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // 2.创建stmt并作直连读的表初始化
    GmcStmtT *readStmt;
    ret = AllocStmtAndInitLabel(stmt, &readStmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // 3.直连读拿一行数据和trxId,数据在stmt的vertex上
    uint32_t affectRows;
    uint64_t trxId;
    ret = DirectReadFetchVertex(readStmt, keyBuf, keySize, &affectRows, &trxId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        FreeStmt(readStmt);
        return ret;
    }

    // 4.比较数据是否一致
    bool isSame = false;
    if (affectRows == 0) {
        isSame = false;
    } else {
        ret = CheckFieldIsSame(stmt, readStmt, &isSame);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            FreeStmt(readStmt);
            return ret;
        }
    }

    // 5.释放stmt
    FreeStmt(readStmt);

    // 6.发送报文
    if (isSame) {
        // isSame == true:走check_replace报文流程，无结构化发送
        ret = ExecCheckReplaceSyncSend(stmt, trxId, keyBuf, keySize);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "exec update check update version");
        }
    } else {
        // isSame == false:走replace的流程，内部再区分结构化和非结构化
        stmt->operationType = GMC_OPERATION_REPLACE;
        ret = ExecInsertReplaceMergeVertexSync(stmt);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "exec replace process when check replace");
        }
    }
    return ret;
}

// 直连写相关处理函数
static Status ExecInsertVertexByDw(GmcStmtT *stmt)
{
    return DwProcessExecute(stmt);
}

static Status ExecReplaceVertexByDw(GmcStmtT *stmt)
{
    return DwProcessExecute(stmt);
}

static Status ExecDeleteVertexByDw(GmcStmtT *stmt)
{
    return DwProcessExecute(stmt);
}

static Status ExecMergeVertexByDw(GmcStmtT *stmt)
{
    DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "merge operation in Direct write.");
    return GMERR_FEATURE_NOT_SUPPORTED;
}

static Status ExecUpdateVersionByDw(GmcStmtT *stmt)
{
    DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "update version operation in Direct write.");
    return GMERR_FEATURE_NOT_SUPPORTED;
}

static Status ExecUpdateVertexByDw(GmcStmtT *stmt)
{
    return DwProcessExecute(stmt);
}

static Status ExecScanVertexByDw(GmcStmtT *stmt)
{
    DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "scan operation in Direct write.");
    return GMERR_FEATURE_NOT_SUPPORTED;
}

static Status ExecCheckReplaceByDw(GmcStmtT *stmt)
{
    return DwProcessExecute(stmt);
}

typedef Status (*ExecuteFunc)(GmcStmtT *stmt);

typedef struct ExecuteFuncs {
    ExecuteFunc dsModeFunc;          // Delta Store入口函数，包含直连读、缓存写接口
    ExecuteFunc csModeFunc;          // C/S入口函数，包含基于C/S通信模式的读写接口
    ExecuteFunc dwModeFunc;          // Direct Write入口函数，包含直连写接口
    ExecuteFunc writeCacheModeFunc;  // 写缓存入口函数
} ExecuteFuncsT;

static const ExecuteFuncsT g_executeFunc[] = {[GMC_OPERATION_INSERT] = {NULL, ExecInsertReplaceMergeVertexSync,
                                                  ExecInsertVertexByDw, ExecInsertVertexByWriteCache},
    [GMC_OPERATION_INSERT_WITH_RESOURCE] = {NULL, ExecInsertReplaceMergeVertexSync, ExecInsertVertexByDw, NULL},
    [GMC_OPERATION_REPLACE] = {NULL, ExecInsertReplaceMergeVertexSync, ExecReplaceVertexByDw,
        ExecReplaceVertexByWriteCache},
    [GMC_OPERATION_REPLACE_WITH_RESOURCE] = {NULL, ExecInsertReplaceMergeVertexSync, ExecReplaceVertexByDw, NULL},
    [GMC_OPERATION_DELETE] = {NULL, ExecDeleteVertexSync, ExecDeleteVertexByDw, ExecDeleteVertexByWriteCache},
    [GMC_OPERATION_MERGE] = {NULL, ExecInsertReplaceMergeVertexSync, ExecMergeVertexByDw, NULL},
    [GMC_OPERATION_UPDATE] = {NULL, ExecUpdateVertexSync, ExecUpdateVertexByDw, NULL},
    [GMC_OPERATION_UPDATE_VERSION] = {NULL, ExecUpdateCheckVersion, ExecUpdateVersionByDw, NULL},
    [GMC_OPERATION_SCAN] = {ExecScanVertexByDS, ExecScanVertexByCS, ExecScanVertexByDw, NULL},
    [GMC_OPERATION_CHECK_REPLACE] = {NULL, ExecCheckReplaceSync, ExecCheckReplaceByDw, NULL},
    [GMC_OPERATION_SQL_INSERT] = {NULL, ExecInsertReplaceMergeVertexSync, ExecInsertVertexByDw,
        ExecInsertVertexByWriteCache},
#ifdef FEATURE_VLIVF
    [GMC_OPERATION_LOAD_INDEX] = {NULL, ExecInsertReplaceMergeVertexSync, NULL, NULL}
#endif
};

Status ExecuteWithWriteCacheMode(GmcStmtT *stmt)
{
    DB_ASSERT((uint32_t)stmt->operationType < ELEMENT_COUNT(g_executeFunc));
    DB_ASSERT(g_executeFunc[stmt->operationType].writeCacheModeFunc != NULL);
    Status ret = CltConnServerCheckForDirectWrite(stmt->conn);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return g_executeFunc[stmt->operationType].writeCacheModeFunc(stmt);
}

Status ExecuteWithNoStructDsMode(GmcStmtT *stmt)
{
    DB_ASSERT((uint32_t)stmt->operationType < ELEMENT_COUNT(g_executeFunc));
    DB_ASSERT(g_executeFunc[stmt->operationType].dsModeFunc != NULL);
    Status ret = CltConnServerCheck(stmt->conn);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return g_executeFunc[stmt->operationType].dsModeFunc(stmt);
}

Status ExecuteWithNoStructCsMode(GmcStmtT *stmt)
{
    DB_ASSERT((uint32_t)stmt->operationType < ELEMENT_COUNT(g_executeFunc));
    DB_ASSERT(g_executeFunc[stmt->operationType].csModeFunc != NULL);
    return g_executeFunc[stmt->operationType].csModeFunc(stmt);
}

Status ExecuteWithNoStructDwMode(GmcStmtT *stmt)
{
    DB_ASSERT((uint32_t)stmt->operationType < ELEMENT_COUNT(g_executeFunc));
    DB_ASSERT(g_executeFunc[stmt->operationType].dwModeFunc != NULL);
    Status ret = CltConnServerCheckForDirectWrite(stmt->conn);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return g_executeFunc[stmt->operationType].dwModeFunc(stmt);
}
