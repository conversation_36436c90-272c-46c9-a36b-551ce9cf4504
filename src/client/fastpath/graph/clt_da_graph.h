/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author:
 * Create: 2023-05-25
 */
#ifndef CLT_DA_GRAPH_H
#define CLT_DA_GRAPH_H

#include "clt_da_read.h"
#ifdef __cplusplus
extern "C" {
#endif
bool IsStmgMarkDelete(const DmVertexLabelT *vertexLabel, DmVertexDescT *vertexDesc, const HeapTupleBufT *heapTupleBuf);

bool DsMatchFilterStructure(const HeapTupleBufT *tupleBuf, StructFilterListT *structFilterList, DmVertexT *vertex);

Status MainStoreCheckAge(const HeapTupleBufT *heapTupleBuf, uint8_t *userData);

Status MainStoreCheckAgeOpt(const HeapTupleBufT *heapTupleBuf, uint8_t *userData);

Status IsVertexBufAged(
    const HeapTupleBufT *heapTupleBuf, CltCataLabelT *cltCataLabel, bool *isAged, DmVertexDescT *vertexDesc);

Status MainStoreHandleInner(DrRunCtxT *drRunCtx);

Status MainStoreVertexHashClusterOpen(DrRunCtxT *drRunCtx);

Status MainStoreVertexArtClusterOpen(DrRunCtxT *drRunCtx);

StatusInter DsHeapCmpAndCopyHpTupleBuf(HpReadRowInfoT *readRowInfo, void *userData);

StatusInter DsHeapCmpAndCopyHpTupleBufOpt(HpReadRowInfoT *readRowInfo, void *userData);
#ifdef __cplusplus
}
#endif
#endif  // CLT_DA_GRAPH_H
