/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: clt_graph_dml_dlr.c
 * Description: Implement of GMDB client graph vertex dlr API
 * Author: GMDBV5 Team
 * Create: 2024-09-18
 */

#include "clt_graph_dml_vertex.h"

#include "clt_meta_cache.h"
#include "clt_utils.h"
#include "clt_conn.h"
#include "clt_graph_ddl_vertex.h"
#include "clt_graph_filter.h"
#include "clt_check.h"
#include "dm_meta_prop_label.h"
#include "dm_data_prop.h"
#include "ds_concurrency_control.h"
#include "clt_msg.h"
#include "clt_batch.h"
#include "clt_dlr.h"
#include "clt_sub_conn.h"
#include "clt_resource_pool.h"
#include "clt_da_read.h"
#include "clt_da_vertex_directread.h"
#include "gmc_internal.h"
#include "se_trx.h"

static Status CltDlrSetOldVersion(GmcDlrDataBufT *dataBufT, uint64_t oldVersion, bool isFillVersion)
{
    if (isFillVersion) {
        return CltSetValueToDlrDataBuf(dataBufT, &oldVersion, sizeof(oldVersion), true);
    }
    return GMERR_OK;
}

static Status CltDlrSetData(GmcDlrDataBufT *dataBufT, TextT *vertexBuf)
{
    // copy data len
    uint16_t bufLen = (uint16_t)vertexBuf->len;
    Status ret = CltSetValueToDlrDataBuf(dataBufT, &bufLen, sizeof(bufLen), false);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 根据len  copy data
    ret = CltSetValueToDlrDataBuf(dataBufT, vertexBuf->str, vertexBuf->len, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status CltFillDlrDataBuf(GmcDlrDataBufT *dataBufT, uint32_t opCode, TextT *vertexBuf)
{
    uint16_t convertOpCode = (uint16_t)opCode;
    Status ret = CltSetValueToDlrDataBuf(dataBufT, &convertOpCode, sizeof(convertOpCode), false);
    if (ret == GMERR_OK) {
        ret = CltDlrSetData(dataBufT, vertexBuf);
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltDlrSetOldVersion(dataBufT, DB_INVALID_UINT64, false);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status CltGetScanDataBuf(GmcStmtT *stmt, GmcDlrDataBufT *dataBufT, bool *getEof)
{
    FixBufferT *recvBuf = &(stmt->recvPack);
    uint32_t opCode = (uint32_t)MSG_OP_RPC_REPLACE_VERTEX;  // 全表扫描的opcode，重演的时候opcode设置为replace
    CltDlrDataBufHeaderT *bufHeader = (CltDlrDataBufHeaderT *)dataBufT->buf;
    // GmcFetch的时候，stmt->cacheRows--了，导致少一条数据，这个逻辑避免丢失一条数据
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    if (operVertex->readBefore) {
        stmt->cacheRows++;
        operVertex->readBefore = false;
    }
    TextT vertexBuf = {0};  // 保存每次取出来的VertexBuf
    Status ret = GMERR_OK;
    for (; stmt->cacheRows > 0;) {
        // copy opcode
        uint32_t startPos = bufHeader->pos;              // copy失败的时候回滚用的
        uint32_t originPos = FixBufGetSeekPos(recvBuf);  // 本次recvBuf的originpos，异常时候回滚用
        ret = FixBufGetObject(recvBuf, &vertexBuf);      // 拿出一条数据出来
        if (ret != GMERR_OK) {
            FixBufSeek(recvBuf, originPos);
            return ret;
        }
        ret = CltFillDlrDataBuf(dataBufT, opCode, &vertexBuf);
        if (ret != GMERR_OK) {
            bufHeader->pos = startPos;
            FixBufSeek(recvBuf, originPos);
            break;
        }
        stmt->cacheRows--;
        if (bufHeader->operationNums++ == bufHeader->maxFetchNum) {  // 成功copy一条数据
            break;
        }
    }
    // 整个buf copy出去，需要对stmt上的参数做一些修改
    if (stmt->cacheRows == 0) {
        *getEof = true;
        stmt->fetchEof = true;
    }
    if (*getEof == false && bufHeader->operationNums == 0) {
        stmt->cacheRows--;  // 消除上面stmt->cacheRows++的副作用
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "dlr data buf size too small for get a data.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    // GMERR_FIELD_OVERFLOW在这里表示dlr data buf的空间满了，不算异常
    if (ret == GMERR_FIELD_OVERFLOW) {
        return GMERR_OK;
    }
    return ret;
}

Status CltGetSubOpCode(GmcSubEventTypeE eventType, CltCataLabelT *cltCataLabel, uint32_t *opCode)
{
    if (cltCataLabel != NULL && DmVertexLabelIsDatalogLabel(cltCataLabel->vertexLabel)) {
        *opCode = (eventType == GMC_SUB_EVENT_DELETE) ? MSG_OP_RPC_DELETE_VERTEX : MSG_OP_RPC_INSERT_VERTEX;
        return GMERR_OK;
    }
    switch (eventType) {
        case GMC_SUB_EVENT_REPLACE:
        case GMC_SUB_EVENT_INSERT:
        case GMC_SUB_EVENT_REPLACE_INSERT:
        case GMC_SUB_EVENT_REPLACE_UPDATE:
        case GMC_SUB_EVENT_MERGE:
        case GMC_SUB_EVENT_MERGE_INSERT:
        case GMC_SUB_EVENT_MERGE_UPDATE:
        case GMC_SUB_EVENT_MODIFY:
        case GMC_SUB_EVENT_INITIAL_LOAD:
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
            *opCode = MSG_OP_RPC_REPLACE_VERTEX;
            break;
        case GMC_SUB_EVENT_DELETE:
        case GMC_SUB_EVENT_AGED:
            *opCode = MSG_OP_RPC_DELETE_VERTEX;
            break;
        case GMC_SUB_EVENT_UPDATE:
            *opCode = MSG_OP_RPC_UPDATE_VERTEX;
            break;
        case GMC_SUB_EVENT_KV_SET:
        case GMC_SUB_EVENT_TRIGGER_SCAN_BEGIN:
        case GMC_SUB_EVENT_TRIGGER_SCAN:
        case GMC_SUB_EVENT_TRIGGER_SCAN_END:
        case GMC_SUB_EVENT_BUTT:
        default:
            DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "for sub event type.");
            return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

static Status CltDlrSetIsReplay(GmcDlrDataBufT *dataBufT, uint64_t oldVersion)
{
    return CltSetValueToDlrDataBuf(dataBufT, &oldVersion, sizeof(oldVersion), true);
}

static inline Status CltDlrFillReplaceBuf(GmcDlrDataBufT *dlrData, TextT *vertexData)
{
    if (vertexData->len == 0 || vertexData->str == NULL) {
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return CltDlrSetData(dlrData, vertexData);
}

static Status CltDlrFillUpdateBuf(GmcDlrDataBufT *dlrData, TextT *vertexData, TextT *keyData, uint64_t oldVersion)
{
    if (keyData->len == 0 || keyData->str == NULL || vertexData->len == 0 || vertexData->str == NULL) {
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    Status ret = CltDlrSetData(dlrData, keyData);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltDlrSetData(dlrData, vertexData);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltDlrSetIsReplay(dlrData, oldVersion);
}

inline static Status CltDlrFillDeleteBuf(GmcDlrDataBufT *dlrData, TextT *keyData, uint64_t oldVersion)
{
    if (keyData->len == 0 || keyData->str == NULL) {
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    Status ret = CltDlrSetData(dlrData, keyData);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltDlrSetIsReplay(dlrData, oldVersion);
}

static Status CltSetSubDataToDlrDataBuf(
    GmcStmtT *stmt, FixBufferT *buffer, uint32_t opCode, GmcDlrDataBufT *dlrData, bool *match)
{
    TextT vertexData = {0};
    uint64_t oldVersion = DB_INVALID_UINT64;
    Status ret = CltGetDlrSubVertex(stmt, buffer, &vertexData, &oldVersion, match);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (!(*match)) {
        return GMERR_OK;
    }

    // 设置opCode
    uint16_t convertOpCode = (uint16_t)opCode;
    ret = CltSetValueToDlrDataBuf(dlrData, &convertOpCode, sizeof(convertOpCode), false);
    if (ret != GMERR_OK) {
        return ret;
    }

    // update: key + delta object || delete： key || else : new object
    SubPushSingleLabel *sub = CltGetOperationContext(stmt);
    /*
     * Datalog pubsub:
     *      insert: vertexData == sub->newData
     *      update: vertexData == sub->newData
     * Normal pubsub
     *      insert: vertexData == sub->newData
     *      update: vertexData == sub->deltaData
     */
    switch (opCode) {
        case MSG_OP_RPC_INSERT_VERTEX:
        case MSG_OP_RPC_REPLACE_VERTEX:
#if defined(FEATURE_STREAM) || defined(STREAM_MULTI_INST)
        case MSG_OP_RPC_STREAM_INSERT:
#endif
            ret = CltDlrFillReplaceBuf(dlrData, &vertexData);
            break;
        case MSG_OP_RPC_UPDATE_VERTEX:
            ret = CltDlrFillUpdateBuf(dlrData, &vertexData, &sub->keyData, oldVersion);
            break;
        case MSG_OP_RPC_DELETE_VERTEX:
            ret = CltDlrFillDeleteBuf(dlrData, &sub->keyData, oldVersion);
            break;
        default:
            if (vertexData.len == 0 || vertexData.str == NULL) {
                return GMERR_FEATURE_NOT_SUPPORTED;
            }
            ret = CltDlrSetData(dlrData, &vertexData);
            if (ret != GMERR_OK) {
                return ret;
            }
            return CltDlrSetOldVersion(dlrData, oldVersion, true);
    }
    return ret;
}

static Status CltDlrReverseBuf(DbMemCtxT *memCtx, SubPushSingleLabel *sub, TextT *vertexData)
{
    // 删除分支将buf中第一个的值直接置-1
    int32_t value = -1;
    uint32_t propId = 0;
    DmValueT propValue = {0};
    propValue.type = DB_DATATYPE_INT32;
    propValue.value.intValue = value;
    Status ret = DmVertexBufSetFirstLevelFixedPropById(
        sub->dlrSubCtx.heapTupleBuf->buf, sub->cltCataLabel->vertexLabel->vertexDesc, propId, &propValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DLR in dtl vertex buf can not set first level fixed prop.");
        return ret;
    }
    vertexData->str = (char *)sub->dlrSubCtx.heapTupleBuf->buf;
    vertexData->len = sub->dlrSubCtx.heapTupleBuf->len;
    return GMERR_OK;
}

static Status CltSetStmgDataToDlrDeleteDataBuf(SubPushSingleLabel *sub, TextT *vertexData)
{
    uint8_t *keyBuf = NULL;
    DmVlIndexLabelT *pkIndex = MEMBER_PTR(sub->cltCataLabel->vertexLabel->metaVertexLabel, pkIndex);
    if (SECUREC_UNLIKELY(pkIndex == NULL)) {
        DB_SET_LASTERR(GMERR_DATA_EXCEPTION, "primary key index.");
        return GMERR_DATA_EXCEPTION;
    }
    DmIndexKeyBufInfoT *indexKeyInfo;
    Status ret = DmVertexGetIndexKeyInfo(sub->vertex, pkIndex->idxLabelBase.indexId, &indexKeyInfo);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_SET_LASTERR(ret, "get PK key info.");
        return ret;
    }

    DmVertexGetVertexKeyBuf(sub->vertex, (uint8_t **)&keyBuf);
    DmGetKeyBufFromVertexBuf(indexKeyInfo, sub->dlrSubCtx.heapTupleBuf->buf, keyBuf, &vertexData->len);
    vertexData->str = (char *)keyBuf;
    return GMERR_OK;
}

Status CltSetStmgDataToDlrDataBuf(GmcStmtT *stmt, uint32_t opCode, GmcDlrDataBufT *dlrData)
{
    // 设置opCode
    SubPushSingleLabel *sub = CltGetOperationContext(stmt);
    Status ret = GMERR_OK;
    TextT vertexData;
    uint16_t tmpOpCode = (uint16_t)opCode;
    if (opCode == MSG_OP_RPC_DELETE_VERTEX) {
        // dlr datalog表只使用insert操作，delete操作对buf置-1
        if (CltGetIsDtlLabel(stmt)) {
            tmpOpCode = (uint16_t)MSG_OP_RPC_INSERT_VERTEX;
            ret = CltDlrReverseBuf(stmt->memCtx, sub, &vertexData);
        } else {
            ret = CltSetStmgDataToDlrDeleteDataBuf(sub, &vertexData);
        }
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        vertexData.str = (char *)sub->dlrSubCtx.heapTupleBuf->buf;
        vertexData.len = sub->dlrSubCtx.heapTupleBuf->len;
    }
    ret = CltSetValueToDlrDataBuf(dlrData, &tmpOpCode, sizeof(tmpOpCode), false);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltDlrSetData(dlrData, &vertexData);
    if (ret != GMERR_OK) {
        return ret;
    }
    switch ((uint32_t)tmpOpCode) {
        case MSG_OP_RPC_INSERT_VERTEX:
        case MSG_OP_RPC_REPLACE_VERTEX:
        case MSG_OP_RPC_REPLACE_GRAPH:
        case MSG_OP_RPC_MERGE_VERTEX:
#if defined(FEATURE_STREAM) || defined(STREAM_MULTI_INST)
        case MSG_OP_RPC_STREAM_INSERT:
#endif
#ifdef FEATURE_VLIVF
        case MSG_OP_RPC_LOAD_INDEX:
#endif
            // insert和replace操作无oldVersion
            return CltDlrSetOldVersion(dlrData, DB_INVALID_UINT64, false);
        default:
            return CltDlrSetOldVersion(dlrData, DB_INVALID_UINT64, true);
    }
}

static inline bool IsReachMaxFetchNum(CltDlrDataBufHeaderT *bufHeader)
{
    return bufHeader->operationNums == bufHeader->maxFetchNum;
}

static inline void CltDlrUpdateSubCtx(bool isFirstFetch, bool isFinish, uint32_t op, GmcStmtT *stmt)
{
    SubPushSingleLabel *sub = CltGetOperationContext(stmt);
    sub->dlrSubCtx.isFirstFetch = isFirstFetch;
    sub->dlrSubCtx.isFinish = isFinish;
    sub->dlrSubCtx.lastOperation = op;
    return;
}

static uint32_t CltDlrGetStmgCurrentOp(GmcStmtT *stmt, uint32_t opCode)
{
    SubPushSingleLabel *sub = CltGetOperationContext(stmt);
    return sub->dlrSubCtx.isFirstFetch ? opCode : sub->dlrSubCtx.lastOperation;
}

Status CltDlrGetStmgLabelData(
    GmcStmtT *stmt, const GmcSubMsgInfoT *info, uint32_t opCode, GmcDlrDataBufT *dlrData, bool *getEof)
{
    CltDlrDataBufHeaderT *bufHeader = (CltDlrDataBufHeaderT *)dlrData->buf;
    if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
        bufHeader->isInitialLoadEOF = true;
        *getEof = true;
        return GMERR_OK;
    }
    Status ret = GMERR_OK;
    uint32_t curOpCode = CltDlrGetStmgCurrentOp(stmt, opCode);

    do {
        uint32_t startPos = bufHeader->pos;  // copy失败的时候回滚用的
        ret = CltSetStmgDataToDlrDataBuf(stmt, curOpCode, dlrData);
        if (ret != GMERR_OK) {
            bufHeader->pos = startPos;
            break;
        }
        bufHeader->operationNums++;
        if (IsReachMaxFetchNum(bufHeader)) {
            CltDlrUpdateSubCtx(false, true, 0, stmt);
            *getEof = true;
            break;
        }
        ret = CltGetDlrNextTuple(stmt, &curOpCode);
        if (ret != GMERR_OK) {
            if (ret == GMERR_NO_DATA) {
                CltDlrUpdateSubCtx(false, true, 0, stmt);
                *getEof = true;
                return GMERR_OK;
            }
            bufHeader->pos = startPos;
            return ret;
        }
    } while (true);

    if (*getEof == false && bufHeader->operationNums == 0) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "dlr data buf size too small for get a stmg label data.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    //  用户的databuf空间不够，会返回这个错误码，但是这里不认为是出错，用过根据getEof来判断是否要重新获取数据
    if (ret == GMERR_FIELD_OVERFLOW) {
        CltDlrUpdateSubCtx(false, false, curOpCode, stmt);
        return GMERR_OK;
    }
    return ret;
}

Status CltGetSubDataBuf(
    GmcStmtT *stmt, const GmcSubMsgInfoT *info, uint32_t opCode, GmcDlrDataBufT *dlrData, bool *getEof)
{
    CltDlrDataBufHeaderT *bufHeader = (CltDlrDataBufHeaderT *)dlrData->buf;
    // buf的格式为opCode|vertex|opCode|vertex。。。

    // 当前订阅服务端只支持一条一条推送，
    // 支持一次把buf中数据全部读取出来
    SubPushSingleLabel *sub = CltGetOperationContext(stmt);
    FixBufferT *buffer = sub->msgData;
    Status ret = GMERR_OK;
    do {
        if (sub->rowsRemain == 0 || info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            *getEof = true;
            break;
        }
        uint32_t startPos = bufHeader->pos;             // copy失败的时候回滚用的
        uint32_t originPos = FixBufGetSeekPos(buffer);  // 本次recvBuf的originpos，异常时候回滚用

        bool match = false;
        ret = CltSetSubDataToDlrDataBuf(stmt, buffer, opCode, dlrData, &match);
        if (ret != GMERR_OK) {
            bufHeader->pos = startPos;
            FixBufSeek(buffer, originPos);
            break;
        }
        --sub->rowsRemain;
        if (match) {
            bufHeader->operationNums++;
            if (IsReachMaxFetchNum(bufHeader)) {
                *getEof = (sub->rowsRemain == 0) ? true : false;
                break;
            }
        }
    } while (true);

    if (*getEof == false && bufHeader->operationNums == 0 && ret == GMERR_FIELD_OVERFLOW) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "dlr data buf size too small for get a data.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    //  用户的databuf空间不够，会返回这个错误码，但是这里不认为是出错，用过根据getEof来判断是否要重新获取数据
    if (ret == GMERR_FIELD_OVERFLOW) {
        return GMERR_OK;
    }
    return ret;
}
