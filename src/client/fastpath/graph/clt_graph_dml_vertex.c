/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: clt_graph_dml_vertex.c
 * Description: Implement of GMDB client graph vertex dml API
 * Author: zhaodu
 * Create: 2020-08-03
 */

#include "clt_graph_dml_vertex.h"
#include "clt_meta_cache.h"
#include "clt_utils.h"
#include "clt_conn.h"
#include "clt_graph_ddl_vertex.h"
#include "clt_graph_filter.h"
#include "clt_heartbeat.h"
#include "clt_check.h"
#include "dm_meta_prop_label.h"
#include "dm_data_prop.h"
#include "dm_yang_interface.h"
#include "ds_concurrency_control.h"
#include "clt_msg.h"
#include "clt_batch.h"
#include "clt_dlr.h"
#include "clt_sub_conn.h"
#include "clt_resource_pool.h"
#include "clt_da_read.h"
#include "clt_da_vertex_directread.h"
#include "gmc_internal.h"
#include "clt_da_write.h"
#include "se_trx.h"
#include "clt_write_cache.h"
#include "gmc_graph_utils.h"
#include "clt_da_graph.h"

#ifdef FEATURE_YANG_ENABLE_DIRECTREAD
#include "gmc_graph_check.h"
#endif

#define FETCH_TRY_TIMES 600  // Fetch重试次数，此宏与服务端 QRY_RECEPTION_SPLIT_TIME 时间共同组成Fetch超时时间

#ifdef FEATURE_VLIVF
bool CheckIfVlIvfField(DmVertexT *vertex, DmVertexLabelT *vertexLabel, const char *name)
{
    DB_POINTER3(vertex, vertexLabel, name);
    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
    if (SECUREC_UNLIKELY(commonInfo == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "check if vlivf field for null commonInfo.");
        return false;
    }
    DmSchemaT *schema = MEMBER_PTR(vertexLabel->metaVertexLabel, schema);
    if (SECUREC_UNLIKELY(schema == NULL)) {
        return false;
    }
    DmPropertySchemaT *property;
    Status ret = DmSchemaGetPropeByName(schema, name, &property, false);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return false;
    }
    if (SECUREC_UNLIKELY(property->isInVlIvfKey)) {
        return true;
    }
    return false;
}
#endif

bool CheckIfPkField(DmVertexT *vertex, DmVertexLabelT *vertexLabel, const char *name)
{
    DB_POINTER(vertex);
    if (SECUREC_UNLIKELY(vertexLabel == NULL)) {
        return false;
    }

    // datalog 不判断,因为datalog的update语义和原先不同，datalog没有merge操作
    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
    if (SECUREC_UNLIKELY(commonInfo == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "check if pk filed for null commonInfo.");
        return true;
    }
    if (commonInfo->datalogLabelInfo != NULL) {
        return false;
    }
    DmSchemaT *schema = MEMBER_PTR(vertexLabel->metaVertexLabel, schema);
    if (SECUREC_UNLIKELY(schema == NULL)) {
        return false;
    }
    DmPropertySchemaT *property;
    Status ret = DmSchemaGetPropeByName(schema, name, &property, false);
    if (ret != GMERR_OK) {
        return false;
    }
    if (property->isInPrimKey) {
        return true;
    }
    return false;
}

Status CltSetVertexProperty(GmcStmtT *stmt, const char *name, GmcDataTypeE type, const void *value, uint32_t size)
{
    DmVertexT *vertex = CltGetVertexInStmt(stmt);
    DB_POINTER(vertex);

    // merge、update 操作，主键字段不可设置
    CltOperVertexT *cltOpVertex = CltGetOperationContext(stmt);
    if (stmt->operationType == GMC_OPERATION_MERGE || stmt->operationType == GMC_OPERATION_UPDATE) {
        if (CheckIfPkField(vertex, cltOpVertex->cltCataLabel->vertexLabel, name)) {
            DB_LOG_AND_SET_LASERR(
                GMERR_INVALID_OBJECT, "Primary key: set when merge or update oper, field name is %s", name);
            return GMERR_INVALID_OBJECT;
        }
    }

#ifdef FEATURE_VLIVF
    if (stmt->operationType == GMC_OPERATION_LOAD_INDEX) {
        if (!CheckIfVlIvfField(vertex, cltOpVertex->cltCataLabel->vertexLabel, name)) {
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_OBJECT,
                "Other field except vlivf key field: set when load index tentatively, field name is %s", name);
            return GMERR_INVALID_OBJECT;
        }
    }
#endif

    uint32_t propId;
    Status ret = DmVertexGetPropeIdByName(vertex, name, &propId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = CltSetVertexPropertyById(stmt, propId, type, value, size);
    if (ret != GMERR_OK) {
        DB_SET_LASTERR(ret, "Set property value by name %s.", name);
    }
    return ret;
}

#ifdef FEATURE_AUTOQUANT
Status CltSetVertexAutoQuantPropById(DmVertexT *vertex, uint16_t id, DmValueT *value, bool *needSetOrigin)
{
    *needSetOrigin = true;
    DmAutoQuantInfoT *autoQuantInfo = &vertex->vertexDesc->commonInfo->autoQuantInfo;
    if (autoQuantInfo->num == 0) {
        return GMERR_OK;
    }
    DmAutoQuantPairT *pairs = MEMBER_PTR(autoQuantInfo, pairs);
    for (uint8_t i = 0; i < autoQuantInfo->num; i++) {
        if (pairs[i].property == id) {
            *needSetOrigin = false;
            if (pairs[i].source != id) {
                DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "set auto quantization field %u.", id);
                return GMERR_FEATURE_NOT_SUPPORTED;
            }
        }
        if (pairs[i].source == id) {
            Status ret = CltSetAutoQuantCodeProp(vertex, value, &pairs[i]);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return GMERR_OK;
}
#endif

Status CltSetVertexPropertyById(GmcStmtT *stmt, uint32_t id, GmcDataTypeE type, const void *value, uint32_t size)
{
    DmValueT propertyValue;
    Status ret = SetDmValue(&propertyValue, (uint32_t)type, value, size);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_SET_LASTERR(ret, "Set prop val when clt set vertex prop by id %u.", id);
        return ret;
    }

    DmVertexT *vertex = CltGetVertexInStmt(stmt);
    DB_POINTER(vertex);
#ifdef FEATURE_AUTOQUANT
    bool needSetOrigin = true;
    ret = CltSetVertexAutoQuantPropById(vertex, (uint16_t)id, &propertyValue, &needSetOrigin);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (!needSetOrigin) {
        return GMERR_OK;
    }
#endif
    return DmVertexSetPropeById(id, propertyValue, vertex);
}

Status CltSetVertexPropertyByIdWithoutType(GmcStmtT *stmt, uint32_t id, const void *value, uint32_t size)
{
    DbDataTypeE type;
    DmVertexT *vertex = CltGetVertexInStmt(stmt);
    DB_ASSERT(vertex != NULL);
    Status ret = DmVertexGetPropeTypeById(id, vertex, &type);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltSetVertexPropertyById(stmt, id, (GmcDataTypeE)type, value, size);
}

Status CltGetScanRecvDataRpc(GmcStmtT *stmt)
{
    Status ret = CltRecvStmtResponseWithCheckOpStatus(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltParseExecAck(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    if (stmt->stmtType == CLT_STMT_TYPE_VERTEX && operVertex->vertexFetchType != CLT_FETCH_VERTEX_NEIGHBOR) {
        ret = SecureFixBufGetUint32(&stmt->recvPack, &stmt->columnCount);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "get columnCount when CltGetScanRecvDataRpc.");
            return ret;
        }
    }
    return ret;
}

Status CltGetVertex(GmcStmtT *stmt)
{
    // 读取fetch到的数据，完成后回归偏移位置以支持继续使用结构化读
    TextT vertexBuf;
    FixBufferT *fixBuf = &stmt->recvPack;

#ifdef FEATURE_HP_ANNSEQSCAN
    if (stmt->vecSearchCtx.isAnnSearch) {
        uint32_t slot = stmt->vecSearchCtx.prioQue->currentCount - stmt->cacheRows - 1;
        DbSimilPrioQueItemT *item = DbSimilPrioQueGetItem(stmt->vecSearchCtx.prioQue, slot);
        fixBuf = (FixBufferT *)item->item;
    }
#endif

    uint32_t originPos = FixBufGetSeekPos(fixBuf);
    // 极致性能场景下采用inline版本的fixbufGet以避免栈保护指令
    Status ret = FixBufGetObjectInline(fixBuf, &vertexBuf);
    FixBufSeek(fixBuf, originPos);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "get vertex when CltGetVertex.");
        return ret;
    }
    CltOperVertexT *op = CltGetOperationContext(stmt);
    if (stmt->useNewDeseri) {
        ret = DmParseVertex((uint8_t *)vertexBuf.str, vertexBuf.len, op->vertex, false);
        stmt->isFullyParsed = false;
        stmt->vertexBufferInFixBuffer.str = vertexBuf.str;
        stmt->vertexBufferInFixBuffer.len = vertexBuf.len;
    } else {
        ret = DmDeSerialize2ExistsVertex((uint8_t *)vertexBuf.str, vertexBuf.len, op->vertex, false);
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    op->doDeseri = true;
    return GMERR_OK;
}

#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
// warm reboot场景使用, 用来直接获取序列化数据
Status CltGetBinData(GmcStmtT *stmt, TextT *binDataBuf)
{
    // 读取fetch到二进制数据
    FixBufferT *fixBuf = &stmt->recvPack;
    uint32_t originPos = FixBufGetSeekPos(fixBuf);
    Status ret = FixBufGetObject(fixBuf, binDataBuf);
    FixBufSeek(fixBuf, originPos);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "get vertex when CltGetBinData.");
        return ret;
    }
    return GMERR_OK;
}
#endif

// 取数据
PRI_IDX_READ_FUNC static Status CltAcquireVertexData(GmcStmtT *stmt)
{
    Status ret;
    // cacheRows和eof来自于服务端的ack,设置一个尝试次数，避免服务端异常，陷入死循环。
    // 超时时间设置为60秒，与客户端默认超时时间拉齐
    uint32_t tryFetchTimes = FETCH_TRY_TIMES;
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    while (stmt->cacheRows == 0 && !stmt->eof && tryFetchTimes > 0) {
        bool *isUsing = &stmt->conn->isUsing;
        ret = CheckAndInitUsing(isUsing, "fetch new");
        if (SECUREC_LIKELY(ret != GMERR_OK)) {
            return ret;
        }
        ProtoHeadT proto = {};
        proto.opCode = MSG_OP_RPC_FETCH_CYPHER;
        if (SECUREC_LIKELY(operVertex->vertexFetchType == CLT_FETCH_VERTEX_DIRECT_READ)) {
            ret = CltSendDirectReadMsg(stmt, &proto);
        } else {
            ret = CltStmtSendAndRecvScanMsg(stmt, &proto);
        }
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            *isUsing = false;
            return ret;
        }
        tryFetchTimes--;
        *isUsing = false;
    }
    if (SECUREC_UNLIKELY(stmt->cacheRows == 0 && !stmt->eof)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "response when parse fetch rows and eof.");
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

#ifdef FEATURE_YANG_ENABLE_DIRECTREAD
Status CltYangDealWithDefault(GmcStmtT *stmt)
{
    Status ret = GMERR_OK;
    uint32_t skipCount = 0;
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    for (uint32_t i = 0; i < stmt->cacheRows; i++) {
        // i == 0 时说明第一次进来，buff中的数据就是当前下标为0的数据
        if (i != 0) {
            TextT vertexBuf;  // 将SecureFixBuffer的读指针移动下一条记录
            ret = FixBufGetObject(&stmt->recvPack, &vertexBuf);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_AND_SET_LASERR(ret, "skip vertexdata when do pre fetch.");
                return ret;
            }
        }
        operVertex->doDeseri = false;
        operVertex->readBefore = true;
        ret = CltGetVertexPropertyPreCheck(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
        DmVertexT *vertex = CltGetVertexInStmt(stmt);
        bool isSetByServer = false;
        bool isEqualDefault = false;
        ret = DmGetLeafListDefaultFlagsFromVertex(vertex, &isSetByServer, &isEqualDefault);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        if (isSetByServer) {
            skipCount++;
        } else {
            break;
        }
    }
    stmt->cacheRows -= skipCount;
    return GMERR_OK;
}
#endif

PRI_IDX_READ_FUNC Status CltPreVertexFetch(GmcStmtT *stmt, bool *eof, CltOperVertexT *operVertex)
{
    // 读取时不会将buffer偏移，当第二次fetch且还有cache row时进行偏移
    bool skipObj = stmt->cacheRows > 0 && operVertex->readBefore;

#ifdef FEATURE_HP_ANNSEQSCAN
    skipObj = skipObj && !stmt->vecSearchCtx.isAnnSearch;
#endif

    if (skipObj) {
        TextT vertexBuf;  // 将SecureFixBuffer的读指针移动下一条记录
        Status ret = FixBufGetObject(&stmt->recvPack, &vertexBuf);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_AND_SET_LASERR(ret, "skip vertexdata when do pre fetch.");
            return ret;
        }
    }
#ifdef FEATURE_YANG_ENABLE_DIRECTREAD
    // leaflist的默认值在直连读时应当过滤掉
    if (SECUREC_UNLIKELY(operVertex->cltCataLabel->vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_YANG) &&
        SECUREC_UNLIKELY(operVertex->cltCataLabel->vertexLabel->vertexDesc->yangInfoDesc->isLeaflist == true)) {
        Status ret = CltYangDealWithDefault(stmt);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_AND_SET_LASERR(ret, "deal with leaf-list default.");
            return ret;
        }
    }
#endif

    // If the server still has data, get it
    Status ret = CltAcquireVertexData(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    if (stmt->cacheRows == 0) {
        *eof = true;
    }
    return GMERR_OK;
}

Status CltReadRow(GmcStmtT *stmt)
{
    FixBufferT *fixBuf = &stmt->recvPack;
    DbClearList(&stmt->propSet);

    for (uint32_t i = 0, propCnt = stmt->columnCount; i < propCnt; i++) {
        CltPropertyT property = {};
        Status ret = SecureFixBufGetUint32(fixBuf, &property.datatype);
        if (SECUREC_UNLIKELY(ret != GMERR_OK || property.datatype >= GMC_DATATYPE_BUTT)) {
            DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "get propType when CltReadRow.");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        if (property.datatype != GMC_DATATYPE_NULL) {
            TextT propValue;  // 这里可空，因为属性值的长度可能为0
            ret = SecureFixBufGetObjectNullable(fixBuf, &propValue);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_AND_SET_LASERR(ret, "get propSize when CltReadRow.");
                return ret;
            }
            property.size = propValue.len;
            property.ptr = (uint8_t *)propValue.str;
        }
        DbListT *propSet = &stmt->propSet;
        ret = DbAppendListItem(propSet, &property);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_AND_SET_LASERR(ret, "append property to gaList.");
            return ret;
        }
    }

    stmt->cacheRows--;
    return GMERR_OK;
}

static Status CltGetColumnCount(GmcStmtT *stmt)
{
    FixBufferT *buffer = &stmt->recvPack;
    // cache real column count in this vertex label.
    Status ret = SecureFixBufGetUint32(buffer, &stmt->columnCount);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get columnCount when CltGetColumnCount.");
        return ret;
    }
    return GMERR_OK;
}

/*
 * In neighbor vertex query scene, vertex label name is exist in net message.
 * Because user may not know the destination vertex label name before GmcFetch
 * call, we should open the specific vertex label using the name here.
 *
 * When should we open the vertex label? First, vertexLabel in stmt's vertex is
 * null. Second, vertexLabel is not null, but current message name is different
 * from the vertexLabel.
 *
 * When should we close the vertex label? It's necessary that vertexLabel is not
 * null. In addition, the vertex should be opened in GmcFetch function instead of
 * opening by user.
 */
static Status CltCacheMetadataFromMessage(GmcStmtT *stmt)
{
    TextT text;
    FixBufferT *buffer = &stmt->recvPack;
    Status ret = SecureFixBufGetTextNullable(buffer, &text);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get text when cache metadata from message.");
        return ret;
    }
    if (text.len > 0) {
        // close vertex label opened in last call.
        DmVertexT *vertex = CltGetVertexInStmt(stmt);
        DB_POINTER(vertex);
        CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
        DmVertexLabelT *vertexLabel = cltVertex->cltCataLabel->vertexLabel;

        // open vertex label.
        if (strcmp(MEMBER_PTR5(&vertexLabel->metaCommon, metaName), text.str) != 0) {
            CltCataLabelT *cltCataLabel = NULL;
            ret = CltOpenVertexLabel(stmt, text.str, &cltCataLabel);
            if (ret != GMERR_OK) {
                return ret;
            }
            cltVertex->cltCataLabel = cltCataLabel;
        }

        ret = CltGetColumnCount(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

/*
 * To decrease code duplication, extract the common logic
 * used in CltFetchNeighborVertexByCSMode and CltFetchVertexByCSMode.
 * This function can be removed if the common logic develops
 * to be no more same at all.
 */
static Status CltFetchResultByCSMode(GmcStmtT *stmt)
{
    if (stmt->columnCount == 0) {
        Status ret = CltGetVertex(stmt);
        if (ret == GMERR_OK) {
            stmt->cacheRows--;
        }
        return ret;
    } else {
        return CltReadRow(stmt);
    }
}

/*
    MsgHeaderT  | fetchRows | columnCount  | eof  | reserved|
    [labelNameLen|labelName | PropertyCount| (vertexDataLen|vertexData) or (projectDataLen|projectData) |
    0|(vertexDataLen|vertexData) or (projectDataLen|projectData)|……]
    [labelNameLen|labelName | PropertyCount| (vertexDataLen|vertexData) or (projectDataLen|projectData) |
    0|(vertexDataLen|vertexData) or (projectDataLen|projectData)|……]
*/
Status CltFetchNeighborVertexByCSMode(GmcStmtT *stmt, bool *eof)
{
    Status ret;
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    do {
        ret = CltPreVertexFetch(stmt, eof, operVertex);
        if (ret != GMERR_OK || *eof) {
            break;
        }
        ret = CltCacheMetadataFromMessage(stmt);
        operVertex->vertexFetchType = CLT_FETCH_VERTEX_NEIGHBOR;
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = CltFetchResultByCSMode(stmt);
    } while (0);
    return ret;
}

/*
    MsgHeaderT  | fetchRows | columnCount  | eof  | reserved|
    PropertyCount| (vertexDataLen|vertexData) or (projectDataLen|projectData) |
                   (vertexDataLen|vertexData) or (projectDataLen|projectData)
*/
PRI_IDX_READ_FUNC Status CltFetchVertex(GmcStmtT *stmt, bool *eof, CltOperVertexT *operVertex)
{
    Status ret;
    do {
        ret = CltPreVertexFetch(stmt, eof, operVertex);
        if (SECUREC_UNLIKELY(ret != GMERR_OK || *eof)) {
            return ret;
        }
        // 部分查询不支持结构化接口，这里直接反序列化
        if (stmt->columnCount != 0) {
            ret = CltReadRow(stmt);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
        } else {
            operVertex->doDeseri = false;
            // fetch 之后将标记设置为true，下次fetch可以进行报文偏移
            operVertex->readBefore = true;
            stmt->cacheRows--;
        }
    } while (0);
    return ret;
}

/*
 * brief get vertex count from CltStmt's SecureFixBuffer
 */
Status ProcessGetVertexCountResp(GmcStmtT *stmt, uint64_t *count)
{
    FixBufferT *fixBuf = &stmt->recvPack;
    Status ret = SecureFixBufGetUint64(fixBuf, count);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get count when ProcessGetVertexCountResp.");
        return ret;
    }
    return GMERR_OK;
}

/*
   RpcMsgHeader | paramsetSize(uint16) | preFetchRows(uint16) | reserved(uint32) |
   diect(0:out , 1:in, 2:both) | srcVertexLabelNameLen | srcVertexLabelName
   srcLeftkeyLen | srcLeftkey | srcCondStrLen | srcCondStr | outputCount邻表label个数 |
   [ labelNameLen|labelName|outputFormatLen|outPutformat|... ]
*/
Status CltSendQueryNeighborsMessage(GmcStmtT *stmt, const char *labelName, const char *keyName, const char *condStr)
{
    CltOperVertexT *op = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = op->cltCataLabel->vertexLabel;
    // 要求labelName和已经打开的表一致
    if (strcmp(MEMBER_PTR5(&vertexLabel->metaCommon, metaName), labelName) != 0) {
        DB_SET_LASTERR(GMERR_UNDEFINED_TABLE, "Curr prepared label mismatch labelName.");
        return GMERR_UNDEFINED_TABLE;
    }

    Status ret;
    DmIndexKeyT *filter = stmt->keyValuesBuf;
    stmt->keyValuesBuf = NULL;  // follow the old logic, keyValuesBuf will be freed

    DbListT *keyValues = op->idx.leftValue;
    if (filter == NULL && keyName != NULL && keyValues->count > 0) {
        ret = DmCreateIndexKeyByNameWithMemCtx(
            stmt->opCtx, vertexLabel, keyName, keyValues->items, keyValues->count, &filter);
        if (ret != GMERR_OK) {
            return ret;
        }
        DbClearList(keyValues);  // follow the old logic, cltVertex->keyValues will be cleared
    }

    ScanPathProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_BEGIN_SCAN_PATH;
    proto.prepExecReq.exec.preFetchRows = 1;
    proto.srcLabelName = labelName;
    proto.indexKey = filter;
    proto.condStr = condStr;
    proto.neighborCount = stmt->neighborsInfo.count;
    proto.neighborsInfo = stmt->neighborsInfo.items;
    AllocStmtId(stmt);
    ret = CltStmtSendMsg(stmt, &proto.protoHead);

    DmDestroyIndexKey(filter);
    DbClearList(&stmt->neighborsInfo);  // follow the old logic, stmt->neighborsInfo will be cleared
    return ret;
}

static Status CltGetSingleLabelChangeStat(FixBufferT *buffer, CltChangeStatisticsInfoT *stat)
{
    typedef CltChangeStatisticsInfoT InfoT;
    static const uint8_t table[][2] = {
        [INSERT_TIME_STATIS] = {offsetof(InfoT, insertStat), offsetof(InfoT, insertFailStat)},
        [DELETE_TIME_STATIS] = {offsetof(InfoT, deleteStat), offsetof(InfoT, deleteFailStat)},
        [UPDATE_TIME_STATIS] = {offsetof(InfoT, updateStat), offsetof(InfoT, updateFailStat)},
        [REPLACE_TIME_STATIS] = {offsetof(InfoT, replaceStat), offsetof(InfoT, replaceFailStat)},
        [MERGE_TIME_STATIS] = {offsetof(InfoT, mergeStat), offsetof(InfoT, mergeFailStat)},
        [(int32_t)TIME_STATIS_END + KV_SET_TIME_STATIS] = {offsetof(InfoT, setStat), offsetof(InfoT, setFailStat)},
        [(int32_t)TIME_STATIS_END +
            KV_REMOVE_TIME_STATIS] = {offsetof(InfoT, removeStat), offsetof(InfoT, removeFailStat)},
    };

    uint32_t opRemain;
    Status ret = SecureFixBufGetUint32(buffer, &opRemain);

    for (uint16_t opType; ret == GMERR_OK && opRemain > 0;) {
        opRemain--;
        ret = SecureFixBufGetUint16(buffer, &opType);
        if (ret != GMERR_OK) {
            break;
        }
        if (opType >= ELEMENT_COUNT(table)) {
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "opType %" PRIu16 " is out of range.", opType);
            return GMERR_DATA_EXCEPTION;
        }
        uint8_t *baseAddr = (uint8_t *)stat;
        const uint8_t *offset = table[opType];
        ret = SecureFixBufGet2Uint64(buffer, (uint64_t *)(baseAddr + offset[0]), (uint64_t *)(baseAddr + offset[1]));
    }

    return ret;
}

static Status CltGetMultiLabelChangeStats(FixBufferT *buffer, CltChangeStatisticsInfoT stat[], uint32_t labelCount)
{
    uint32_t count;
    Status ret = SecureFixBufGetUint32(buffer, &count);
    if (ret != GMERR_OK || count != labelCount) {
        return GMERR_DATA_EXCEPTION;
    }
    for (count = 0; count < labelCount; ++count) {
        ret = CltGetSingleLabelChangeStat(buffer, &stat[count]);
        if (ret != GMERR_OK) {
            break;
        }
    }
    return GMERR_OK;
}

static void CltGetStatisticsCountByType(
    uint32_t type, uint32_t labelCount, const CltChangeStatisticsInfoT statInfo[], uint64_t count[])
{
    for (uint32_t i = 0; i < labelCount; i++) {
        if ((type & GMC_STATISTICS_TYPE_INSERT) != 0) {
            count[i] += statInfo[i].insertStat;
        }
        if ((type & GMC_STATISTICS_TYPE_DELETE) != 0) {
            count[i] += statInfo[i].deleteStat;
        }
        if ((type & GMC_STATISTICS_TYPE_UPDATE) != 0) {
            count[i] += statInfo[i].updateStat;
        }
        if ((type & GMC_STATISTICS_TYPE_REPLACE) != 0) {
            count[i] += statInfo[i].replaceStat;
        }
        if ((type & GMC_STATISTICS_TYPE_MERGE) != 0) {
            count[i] += statInfo[i].mergeStat;
        }
        if ((type & GMC_STATISTICS_TYPE_SET) != 0) {
            count[i] += statInfo[i].setStat;
        }
        if ((type & GMC_STATISTICS_TYPE_REMOVE) != 0) {
            count[i] += statInfo[i].removeStat;
        }
        if ((type & GMC_STATISTICS_TYPE_INSERT_FAILED) != 0) {
            count[i] += statInfo[i].insertFailStat;
        }
        if ((type & GMC_STATISTICS_TYPE_DELETE_FAILED) != 0) {
            count[i] += statInfo[i].deleteFailStat;
        }
        if ((type & GMC_STATISTICS_TYPE_UPDATE_FAILED) != 0) {
            count[i] += statInfo[i].updateFailStat;
        }
        if ((type & GMC_STATISTICS_TYPE_REPLACE_FAILED) != 0) {
            count[i] += statInfo[i].replaceFailStat;
        }
        if ((type & GMC_STATISTICS_TYPE_MERGE_FAILED) != 0) {
            count[i] += statInfo[i].mergeFailStat;
        }
        if ((type & GMC_STATISTICS_TYPE_SET_FAILED) != 0) {
            count[i] += statInfo[i].setFailStat;
        }
        if ((type & GMC_STATISTICS_TYPE_REMOVE_FAILED) != 0) {
            count[i] += statInfo[i].removeFailStat;
        }
    }
}

Status CltBatchGetChangeStatisticsCount(
    GmcStmtT *stmt, uint32_t type, uint32_t labelCount, const char *labels[], uint64_t statistics[])
{
    // labelCount is already checked outside, will not cause stack overflow
    // 在本函数内申请释放
    CltChangeStatisticsInfoT *statInfo = DbDynMemCtxAlloc(stmt->memCtx, sizeof(CltChangeStatisticsInfoT) * labelCount);
    if (statInfo == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc memctx when batch get change, size is %" PRIu32 ".",
            (uint32_t)(sizeof(CltChangeStatisticsInfoT) * labelCount));
        return GMERR_OUT_OF_MEMORY;
    }
    // initialize buffer for temporary result
    size_t size = sizeof(CltChangeStatisticsInfoT) * labelCount;
    (void)memset_s(statInfo, size, 0, size);

    // reset output
    size = sizeof(uint64_t) * labelCount;
    (void)memset_s(statistics, size, 0, size);

    GetStatCountProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_GET_STAT_COUNT;
    proto.labelCount = labelCount;
    proto.labelName = labels;

    Status ret = CltStmtSendAndRecvMsg(stmt, &proto.protoHead);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(stmt->memCtx, statInfo);
        return ret;
    }

    ret = CltGetMultiLabelChangeStats(&stmt->recvPack, statInfo, labelCount);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "change statistics response");
        DbDynMemCtxFree(stmt->memCtx, statInfo);
        return ret;
    }

    CltGetStatisticsCountByType(type, labelCount, statInfo, statistics);
    DbDynMemCtxFree(stmt->memCtx, statInfo);
    return GMERR_OK;
}

ALWAYS_INLINE static void PreProcessScanVertex(GmcStmtT *stmt)
{
    CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
    cltVertex->doDeseri = true;
    if (SECUREC_LIKELY(stmt->isCltStatisDisable)) {  // 性能场景下关闭该视图，服务端接收到的该视图相关数据不可信
        return;
    }
    DB_POINTER(cltVertex->vertex);
    DmVertexLabelT *vertexLabel = cltVertex->cltCataLabel->vertexLabel;
    DB_POINTER(vertexLabel);
    CltCataUpdateQueryStatistics(&vertexLabel->metaCommon, (uint16_t)stmt->conn->instanceId);
}

Status ExecScanVertexByCS(GmcStmtT *stmt)
{
    PreProcessScanVertex(stmt);
    Status ret;
    DmIndexKeyT *leftKey = NULL;
    const DmIndexKeyT *rightKey = NULL;
    CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
#ifdef FEATURE_GQL
    if (cltVertex->structFilterList.isUsed && stmt->modelType != MODEL_GQL) {
#else
    FilterInnerT **filterList = cltVertex->structFilterList.list;
    if (filterList != NULL && filterList[0]->setValue) {
#endif  // FEATURE_GQL
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY, "scan for filter structure in cs");
        return GMERR_INVALID_PROPERTY;
    }
    uint32_t rangeScanFlags;
    ret = CltGetRangedIndexKey(stmt, &rangeScanFlags, &leftKey, &rightKey);
    if (ret != GMERR_OK) {
        return ret;
    }
    GmcConnT *conn = stmt->conn;
    conn->cltOpSegTime.opType = MSG_OP_RPC_SCAN_VERTEX_BEGIN;
    CsScanVertexProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_SCAN_VERTEX_BEGIN;
    proto.prepExecReq.exec.preFetchRows = stmt->preFetchRows;
    proto.rangeScanFlag = rangeScanFlags;
    proto.labelId = cltVertex->cltCataLabel->vertexLabel->metaCommon.metaId;
    proto.schemaVersion = cltVertex->cltCataLabel->vertexLabel->metaCommon.version;
    proto.uuid = cltVertex->cltCataLabel->vertexLabel->metaVertexLabel->uuid;
    proto.leftIndexKey = leftKey;
    proto.rightIndexKey = rightKey;
    proto.isSysviewRecordCmd = stmt->isSysviewRecordCmd;
    proto.condStr = cltVertex->filter;
    proto.outputFmtStr = cltVertex->outputFormat;
    proto.orderByParamCount = DbListGetItemCnt(cltVertex->orderByParams);
    proto.orderByParams = DbListGetItems(cltVertex->orderByParams);
    proto.limitCount = stmt->limitCount;
    AllocStmtId(stmt);
    ret = CltStmtSendAndRecvScanMsg(stmt, &proto.protoHead);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 查询之后fetch标记设置为false，第一次fetch不进行报文偏移
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    operVertex->readBefore = false;
    return ret;
}

ALWAYS_INLINE static DsScanModeE ExecScanGetScanMode(
    const DmIndexKeyT *leftKey, const DmIndexKeyT *rightKey, DrRunCtxT *drRunCtx)
{
    if (leftKey == NULL) {
        return DS_SCAN_VERTEX_SEQUENCE;
    }
#ifdef ART_CONTAINER
    if (drRunCtx->base.containerType == CONTAINER_ART) {
        return DS_SCAN_VERTEX_INDEX_PRIMARY;
    }
#endif
    if (leftKey->indexConstraint == PRIMARY) {
        return DS_LOOKUP_VERTEX_INDEX_PRIMARY;
    } else {
        return DS_SCAN_VERTEX_INDEX_SECONDARY;
    }
}

#ifdef FEATURE_HP_ANNSEQSCAN
static DbVectorMetricE GetVecDisTypeByCompOp(int32_t compOp)
{
    if (compOp == GMC_OP_IP_VECTOR_SIMILARITY) {
        return DB_VECTOR_METRIC_IP;
    } else if (compOp == GMC_OP_L2_VECTOR_SIMILARITY) {
        return DB_VECTOR_METRIC_L2;
    } else if (compOp == GMC_OP_COSINE_VECTOR_SIMILARITY) {
        return DB_VECTOR_METRIC_COSINE;
    }
    return DB_VECTOR_METRIC_BUTT;
}

static bool IsCompOpMatchQuantizationMetric(int32_t compOp, DbVectorMetricE metric)
{
    if (compOp == GMC_OP_IP_VECTOR_SIMILARITY && metric == DB_VECTOR_METRIC_IP) {
        return true;
    }
    if (compOp == GMC_OP_L2_VECTOR_SIMILARITY && metric == DB_VECTOR_METRIC_L2) {
        return true;
    }
    if (compOp == GMC_OP_COSINE_VECTOR_SIMILARITY && metric == DB_VECTOR_METRIC_COSINE) {
        return true;
    }
    return false;
}

static bool FindAvalibleQuantProperty(
    int32_t compOp, uint16_t propeId, const DmVertexLabelT *vertexLabel, DmAutoQuantPairT *pair)
{
    DB_POINTER(pair);
    DmAutoQuantInfoT *autoQuantInfo = &vertexLabel->commonInfo->autoQuantInfo;
    DmAutoQuantPairT *pairs = MEMBER_PTR(autoQuantInfo, pairs);
    for (uint8_t i = 0; i < autoQuantInfo->num; i++) {
        if (pairs[i].source != propeId) {
            continue;
        }
        if (IsCompOpMatchQuantizationMetric(compOp, pairs[i].metric)) {
            *pair = pairs[i];
            return true;
        }
    }
    return false;
}

static Status PrepareVectorSimilarityScanSerializeVertex(GmcStmtT *stmt)
{
    DmVertexT *vertex = CltGetVertexInStmt(stmt);
    uint32_t serializeBufLen = 0;
    Status ret = DmVertexGetSeriBufLength(vertex, &serializeBufLen);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (stmt->vecSearchCtx.serializeBufLen < serializeBufLen) {
        if (stmt->vecSearchCtx.serializeBufLen != 0) {
            DbDynMemCtxFree(stmt->memCtx, stmt->vecSearchCtx.serializeBuf);
        }
        stmt->vecSearchCtx.serializeBuf = DbDynMemCtxAlloc(stmt->memCtx, serializeBufLen);
        if (stmt->vecSearchCtx.serializeBuf == NULL) {
            stmt->vecSearchCtx.serializeBufLen = 0;
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc mem for serializeBuf.");
            return GMERR_OUT_OF_MEMORY;
        }
        stmt->vecSearchCtx.serializeBufLen = serializeBufLen;
    }
    ret = DmSerializeVertex2InvokerBuf(vertex, serializeBufLen, stmt->vecSearchCtx.serializeBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static uint32_t GetFlatVertexPropertyOffsetInBuf(
    const DmVertexLabelT *vertexLabel, uint8_t *buf, uint32_t propeId, const DmVertexDescT *vertexDesc)
{
    DmSchemaT *schema = MEMBER_PTR(vertexLabel->metaVertexLabel, schema);
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    if (!schema->isFlat) {
        return DB_INVALID_UINT32;
    }
    if (!DmIsFixedType(properties[propeId].dataType)) {
        return DB_INVALID_UINT32;
    }
    uint32_t offset = (uint32_t)sizeof(uint8_t) + *(uint8_t *)buf + (uint32_t)sizeof(uint8_t);
    uint32_t fixedLength;
    uint32_t varintByteNum;
    DmConvertVarintToUint32(*((const uint32_t *)(buf + offset)), &fixedLength, &varintByteNum);
    offset += (varintByteNum + vertexDesc->recordDesc->propeInfos[propeId].offset);
    return offset;
}

static Status CheckFullAccuracyVectorSearchFilter(const FilterInnerT *filter)
{
    if (filter->property->dataType != DB_DATATYPE_FIXED) {
        return GMERR_FIELD_TYPE_NOT_MATCH;
    }
    if (filter->property->size != filter->value.value.length) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (filter->property->size == 0 || filter->property->size % sizeof(float) != 0) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

static Status PrepareExecVectorSimilarityScan(GmcStmtT *stmt)
{
    VectorSearchCtxT *vecCtx = &stmt->vecSearchCtx;
    vecCtx->prioQue = CltReCreatePrioQue(stmt->memCtx, stmt->limitCount, vecCtx->prioQue);
    if (vecCtx->prioQue == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
    FilterInnerT *filter = cltVertex->structFilterList.list[0];
    DmVertexLabelT *label = cltVertex->cltCataLabel->vertexLabel;
    vecCtx->offset = DB_INVALID_UINT32;
    vecCtx->metric = GetVecDisTypeByCompOp(filter->compOp);
    bool useQuant = FindAvalibleQuantProperty(filter->compOp, filter->property->propeId, label, &vecCtx->quantPair);

    DmVertexT *vertex = CltGetVertexInStmt(stmt);
    Status ret;

    if (useQuant) {
        ret = CltSetAutoQuantCodeProp(vertex, &filter->value, &vecCtx->quantPair);
    } else {
        ret = CheckFullAccuracyVectorSearchFilter(filter);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "check full accuracy vector search filter.");
            return ret;
        }
        ret = DmVertexSetPropeById(filter->fieldId, filter->value, vertex);
    }
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = PrepareVectorSimilarityScanSerializeVertex(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t calcPropId = useQuant ? vecCtx->quantPair.property : filter->fieldId;
    vecCtx->offset = GetFlatVertexPropertyOffsetInBuf(label, vecCtx->serializeBuf, calcPropId, vertex->vertexDesc);
    if (vecCtx->offset == DB_INVALID_UINT32) {
        DmValueT value = {};
        ret = DmVertexBufGetFirstLevelFixedPropById(vecCtx->serializeBuf, vertex->vertexDesc, calcPropId, &value);
        if (ret != GMERR_OK) {
            return ret;
        }
        vecCtx->calcAddr = value.value.strAddr;
    } else {
        vecCtx->calcAddr = vecCtx->serializeBuf + vecCtx->offset;
    }
    return ret;
}
#endif

Status ExecScanVertexByDS(GmcStmtT *stmt)
{
    PreProcessScanVertex(stmt);
    // No need to check DS transaction mode, it's checked by IsVertexDirectReadScan.
    DmIndexKeyT *leftKey = NULL;
    const DmIndexKeyT *rightKey = NULL;
    CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
    CondScanParaT scanPara;
    Status ret = CltGetRangedIndexKeyAndCondScanPara(stmt, &scanPara, &leftKey, &rightKey);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // clang-format off
    if (SECUREC_UNLIKELY(
        leftKey != NULL && leftKey->keyBufLen > DM_MAX_INDEX_KEY_SIZE_WITH_NULL(leftKey->nullInfoBytes))) {
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    // clang-format on

    DrRunCtxT *drRunCtx = &stmt->drRunCtx;
    // clang-format off
    drRunCtx->dsFetchArgs = (DsFetchArgsT) {
        .protoHead = {.opCode = MSG_OP_RPC_SCAN_VERTEX_BEGIN},
        .limitCount = stmt->limitCount,
        .preFetchRows = stmt->preFetchRows,
        .totalFetchedRowCnt = 0,
        .isSetPreFetch = stmt->isSetPreFetch,
        .isDirectGetCount = false,
        .vertexScanArgs = {.vertex = cltVertex->vertex,
            .cltCataLabel = cltVertex->cltCataLabel,
            .rangeScanFlags = scanPara.rangeScanFlags,
            .condIdx = scanPara.condIdx,
            .matchBytesNum = scanPara.matchBytesNum,
            .nearAIdx = scanPara.nearAIdx,
            .nearBIdx = scanPara.nearBIdx,
            .filter = (DmIndexKeyT *)leftKey,
            .rightKey = (DmIndexKeyT *)(uintptr_t)rightKey,
            .structFilterList = &cltVertex->structFilterList},
        .scanMode = ExecScanGetScanMode(leftKey, rightKey, drRunCtx)
    };
    // clang-format on

#ifdef FEATURE_HP_ANNSEQSCAN
    if (stmt->vecSearchCtx.isAnnSearch) {
        ret = PrepareExecVectorSimilarityScan(stmt);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    drRunCtx->dsFetchArgs.vertexScanArgs.vecSearchCtx = &stmt->vecSearchCtx;
#endif

    ret = CltSendDirectReadMsg(stmt, &drRunCtx->dsFetchArgs.protoHead);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // 查询之后fetch标记设置为false，第一次fetch不进行报文偏移
    cltVertex->readBefore = false;
    return ret;
}

bool IsVertexDirectReadScan(const GmcStmtT *stmt)
{
    if (!stmt->conn->openDirectRead) {
        return false;
    }
    if (stmt->operationType != GMC_OPERATION_SCAN) {
        return false;
    }
    if (CltTransNotNeedCSMode(stmt) != GMERR_OK) {
        return false;
    }
    const CltOperVertexT *cltVertex = CltGetConstOperationContext(stmt);
    if (cltVertex->cltCataLabel->vertexLabel->metaVertexLabel->vertexLabelType > VERTEX_TYPE_DATALOG) {
        return false;
    }
    if (cltVertex->filter != NULL) {  // condition string.
        return false;
    }
    if (cltVertex->doNotDsScan) {
        return false;
    }
    return true;
}

Status CltMergePutIndex(GmcStmtT *stmt)
{
    // 合并Vertex时，服务器只接收一个Vertex，因此需要把索引字段合并到Vertex上
    DmIndexKeyT *indexKey;
    Status ret = CltGetSimpleIndexKeyNotNull(stmt, &indexKey);
    if (ret != GMERR_OK) {
        return ret;
    }
    CltOperVertexT *op = CltGetOperationContext(stmt);
    DmVertexT *vertex = op->vertex;
    CltIndexConditionT *idx = &op->idx;
    DmVlIndexLabelT *indexLabel = idx->indexLabel;
    DB_ASSERT(indexLabel != NULL &&
              indexLabel->propeNum <= DM_MAX_KEY_PROPE_NUM);  // 上面能够成功获取indexKey，说明设置了索引

    DmValueT propertyValues[DM_MAX_KEY_PROPE_NUM];
    ret = DmGetPropeValuesFromKeyBuf(indexKey->keyBuf, indexLabel, propertyValues);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t propeNum = indexLabel->propeNum;
    uint32_t *propIds = MEMBER_PTR(indexLabel, propIds);
    for (uint32_t i = 0; i < propeNum; i++) {
        ret = DmVertexSetPropeById(propIds[i], propertyValues[i], vertex);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return GMERR_OK;
}

static inline Status FillDsFetchArgs(
    DsFetchArgsT *dsFetchArgs, DsScanVertexProtoT vertexProto, GmcStmtT *stmt, bool isGetCount)
{
    if (SECUREC_UNLIKELY(vertexProto.leftIndexKey->keyBufLen >
                         DM_MAX_INDEX_KEY_SIZE_WITH_NULL(vertexProto.leftIndexKey->nullInfoBytes))) {
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    if (vertexProto.leftIndexKey->indexConstraint == PRIMARY) {
        dsFetchArgs->scanMode = DS_LOOKUP_VERTEX_INDEX_PRIMARY;
    } else {
        dsFetchArgs->scanMode = DS_SCAN_VERTEX_INDEX_SECONDARY;
    }
    dsFetchArgs->preFetchRows = vertexProto.preFetchRows;
    dsFetchArgs->vertexScanArgs.vertex = vertexProto.vertex;
    // 该方法仅在GmcGetVertexRecordCount()流程下调用，非扫描流程，因此初始化为0
    dsFetchArgs->limitCount = 0;
    dsFetchArgs->totalFetchedRowCnt = 0;
    // 下层对const属性没有做约束，但是实际上不会修改indexKey的值，这里统一强转
    dsFetchArgs->vertexScanArgs.filter = (DmIndexKeyT *)(uintptr_t)vertexProto.leftIndexKey;
    dsFetchArgs->vertexScanArgs.rightKey = (DmIndexKeyT *)(uintptr_t)vertexProto.rightIndexKey;
    dsFetchArgs->vertexScanArgs.rangeScanFlags = vertexProto.rangeScanFlag;
    dsFetchArgs->isDirectGetCount = isGetCount;
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    dsFetchArgs->vertexScanArgs.structFilterList = &operVertex->structFilterList;

    return GMERR_OK;
}

PRI_IDX_READ_FUNC Status CltExecDsVertexScan(GmcStmtT *stmt, ProtoHeadT *proto)
{
    DB_UNUSED(proto);
    DrRunCtxT *drRunCtx = &stmt->drRunCtx;
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    drRunCtx->directReadBuff = &stmt->recvPack;
    SeRunCtxHdT seRunCtx = (SeRunCtxHdT)drRunCtx->base.seRunCtxHandle;
    Status ret =
        DsAcqLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->labelLatchVersionId, drRunCtxBase->sessionCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    bool trxOpen = SeTransStartDirectReadCheck(seRunCtx);
    ret = CltChangeSeTransStateBeforeRead(trxOpen, seRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DsReleaseLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->sessionCtx);
        return ret;
    }

    // check label是否被删除
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    if (SECUREC_UNLIKELY(operVertex->cltCataLabel->vertexLabel->metaCommon.isDeleted)) {
        ret = GMERR_INVALID_PARAMETER_VALUE;
        DB_LOG_AND_SET_LASERR(ret, "vertexlabel has been deleted.");
        goto EXIT;
    }

    ret = DirectReadCtxOpen(drRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT;
    }

    // 和cs机制保持一致，execute是取第一批数据
    FixBufResetMem(&stmt->recvPack);
    ret = MainStoreReadFetchNext(drRunCtx, &stmt->cacheRows, &stmt->eof);
    CltChangeSeTransStateAfterRead(trxOpen, seRunCtx);
    DsReleaseLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->sessionCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK || stmt->eof)) {
        DirectReadCtxClose(drRunCtx, DIRECT_ACCESS_CLOSE_HEAP_CURSOR);
    }
    return ret;
EXIT:
    CltChangeSeTransStateAfterRead(trxOpen, seRunCtx);
    DsReleaseLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->sessionCtx);
    // heap懒加载导致，写入数据前直连读校验返回
    if (ret == GMERR_NO_DATA) {
        stmt->cacheRows = 0;
        stmt->eof = true;
        ret = GMERR_OK;
    }
    return ret;
}

NO_INLINE Status GetVertexCountSync(GmcStmtT *stmt, uint64_t *count)
{
    CltOperVertexT *op = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = op->cltCataLabel->vertexLabel;

    GetCountProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_GET_COUNT;
    proto.labelId = vertexLabel->metaCommon.metaId;
    StatusAndPtrT getKeyRet = CltGetSimpleIndexKey(stmt);
    if (getKeyRet.status != GMERR_OK) {
        return getKeyRet.status;
    }
    proto.indexKey = (const DmIndexKeyT *)getKeyRet.ptr;

    Status ret = CltStmtSendAndRecvMsg(stmt, &proto.protoHead);
    if (ret != GMERR_OK) {
        return ret;
    }

    FixBufferT *fixBuf = &stmt->recvPack;
    ret = SecureFixBufGetUint64(fixBuf, count);
    return ret;
}

static inline void GetVertexCntInitFetchArgs(CltOperVertexT *operVertex, DrRunCtxT *drRunCtx)
{
    drRunCtx->dsFetchArgs.scanMode = DS_SCAN_VERTEX_SEQUENCE;
    drRunCtx->dsFetchArgs.vertexScanArgs.vertex = operVertex->vertex;
}

// 返回值表示是否支持逻辑计数
static bool GetUserDefItemNum(CltCataLabelT *cltCataLabel, uint64_t *count)
{
    if (SECUREC_LIKELY(DmVertexLabelIsNormalLabel(cltCataLabel->vertexLabel))) {
        DmAccCheckT *accCheckAddr = cltCataLabel->vertexAccCheck;
        if (SECUREC_LIKELY(!accCheckAddr->isPartition)) {
            *count = accCheckAddr->checkInfo[0].logicCnt;
            return true;
        }
        uint64_t logicCnt = 0;
        for (uint8_t i = 0; i < DM_MAX_PARTITION_ID; i++) {
            DmCheckInfoT *checkInfo = &accCheckAddr->checkInfo[i];
            logicCnt += checkInfo->logicCnt;
        }
        *count = logicCnt;
        return true;
    }
    return false;  // 不支持的时候不要修改userDefItemNum
}

static CLT_ALWAYS_INLINE Status GetVertexCnt4NoLeftKey(
    CltOperVertexT *operVertex, DirectAccessRunCtxT *drRunCtxBase, uint64_t *count)
{
    CltCataLabelT *cltCataLabel = operVertex->cltCataLabel;
    if (SECUREC_UNLIKELY(!GetUserDefItemNum(cltCataLabel, count))) {
        Status ret = GMERR_OK;
        if (SECUREC_LIKELY(drRunCtxBase->isUseClusteredHashTable)) {
            ret = ChLabelGetPhyItemNum(drRunCtxBase->chRunCtx, count);
        } else {
            ret = HeapLabelGetPhyItemNum(drRunCtxBase->heapRunCtx, count);
        }
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    operVertex->probNotPrep = true;
    return GMERR_OK;
}

NO_INLINE static Status OpenRunctx4ProtoWithLeftKey(DrRunCtxT *drRunCtx, GmcStmtT *stmt, DsScanVertexProtoT proto)
{
    DsFetchArgsT *dsFetchArgs = &(drRunCtx->dsFetchArgs);
    Status ret = FillDsFetchArgs(dsFetchArgs, proto, stmt, true);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = DirectReadCtxOpen(drRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return GMERR_OK;
}

static CLT_ALWAYS_INLINE Status DirectReadQuickOpen(DrRunCtxT *drRunCtx)
{
    DB_POINTER(drRunCtx);
    if (SECUREC_UNLIKELY(drRunCtx->base.chCursor != NULL || drRunCtx->base.heapRunCtx != NULL ||
                         drRunCtx->htCursor != NULL || drRunCtx->heapCursor != NULL)) {
        DirectReadCtxClose(drRunCtx, DIRECT_ACCESS_CLOSE_HEAP_CURSOR);
    }
    if (SECUREC_UNLIKELY(!drRunCtx->base.isUseClusteredHashTable || drRunCtx->base.chRunCtx == NULL)) {
        return MainStoreHandleInner(drRunCtx);
    }
    return GMERR_OK;
}

Status GetVertexCountDirectInner(GmcStmtT *stmt, uint64_t *count, DrRunCtxT *drRunCtx, CltOperVertexT *operVertex)
{
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    StatusAndPtrT getKeyRet = CltGetSimpleIndexKey(stmt);
    if (SECUREC_UNLIKELY(getKeyRet.status != GMERR_OK)) {
        return getKeyRet.status;
    }
    Status ret =
        DsAcqLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->labelLatchVersionId, drRunCtxBase->sessionCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // 全表记录数快路径
    if (SECUREC_LIKELY(getKeyRet.ptr == NULL)) {
        GetVertexCntInitFetchArgs(operVertex, drRunCtx);
        ret = DirectReadQuickOpen(drRunCtx);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            goto EXIT;
        }
        ret = GetVertexCnt4NoLeftKey(operVertex, drRunCtxBase, count);
    } else {
        DsScanVertexProtoT proto;
        proto.preFetchRows = CLT_DEFAULT_PRE_FETCH_ROW;
        proto.rangeScanFlag = 0;
        proto.vertex = operVertex->vertex;
        proto.cltCataLabel = operVertex->cltCataLabel;
        proto.leftIndexKey = (const DmIndexKeyT *)getKeyRet.ptr;
        proto.rightIndexKey = NULL;
        ret = OpenRunctx4ProtoWithLeftKey(drRunCtx, stmt, proto);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            goto EXIT;
        }
        drRunCtx->directReadBuff = &stmt->recvPack;
        ret = DirectReadGetCount(drRunCtx, count);
    }

    // 出于性能目的直接调用heap cursor关闭接口，如果修改DirectReadCtxClose，不要遗漏此处
    DirectReadCtxCloseHeapCursorNoLock(drRunCtx);
    DsReleaseLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->sessionCtx);
    return ret;
EXIT:
    DsReleaseLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->sessionCtx);
    // heap懒加载导致，写入数据前直连读校验返回
    if (ret == GMERR_NO_DATA) {
        *count = 0;
        ret = GMERR_OK;
    }
    return ret;
}

ALWAYS_INLINE Status GetVertexCountDirect(GmcStmtT *stmt, uint64_t *count)
{
    Status ret = CltConnServerCheck(stmt->conn);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    DrRunCtxT *drRunCtx = &stmt->drRunCtx;
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    CltCataLabelT *cltCataLabel = operVertex->cltCataLabel;
    if (SECUREC_UNLIKELY(!drRunCtxBase->hasInited)) {
        DB_LOG_AND_SET_LASERR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "uninited dr runctx.");
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    DmReusableMetaT *metaInfoAddr = cltCataLabel->vertexMetaInfo;
    if (SECUREC_UNLIKELY(metaInfoAddr->vertexLabelId != cltCataLabel->vertexLabel->metaCommon.metaId)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_TABLE, "label is dropped.");
        return GMERR_UNDEFINED_TABLE;
    }
    if (SECUREC_UNLIKELY(drRunCtxBase->privPolicyMode != (uint32_t)PRIV_NON_AUTH &&
                         (drRunCtxBase->passPrivType != CLI_SYS_PRIV ||
                             drRunCtxBase->role->sysPrivVersion != drRunCtxBase->sysPrivVersion))) {
        if (SECUREC_UNLIKELY((ret = VertexPrivCheck(drRunCtx, cltCataLabel)) != GMERR_OK)) {
            DB_LOG_WARN(ret, "in priv check when getting vertex count");
            return ret;
        }
    }
    drRunCtx->dsFetchArgs.vertexScanArgs.cltCataLabel = cltCataLabel;
    return GetVertexCountDirectInner(stmt, count, drRunCtx, operVertex);
}

/*
 * brief get vertexLabelDegradeProcess from CltStmt's SecureFixBuffer
 */
Status GetLabelDegradeProcessResp(GmcStmtT *stmt, uint32_t *degradeProcess)
{
    FixBufferT *fixBuf = &stmt->recvPack;
    return SecureFixBufGetUint32(fixBuf, degradeProcess);
}

/**
 * 在索引条件为非唯一键场景下判断是否有符合的记录
 * 实现方式为fetch一条，由于非唯一键走的是顺序扫，暂未做内存拷贝过程的优化，当前方式是只取一条，返回后获取是否扫到数据
 * @param stmt 客户端句柄
 * @param leftKey 索引条件
 * @param isExist 返回值，存在则为true
 * @return
 */
Status ExecIsVertexExistDsByNonUniqueKey(GmcStmtT *stmt, DmIndexKeyT *leftKey, bool *isExist)
{
    *isExist = false;
    CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
    DrRunCtxT *drRunCtx = &stmt->drRunCtx;
    // clang-format off
    drRunCtx->dsFetchArgs = (DsFetchArgsT) {
        .protoHead = {.opCode = MSG_OP_RPC_IS_VERTEX_EXIST},
        .limitCount = 1,
        .preFetchRows = 1,
        .totalFetchedRowCnt = 0,
        .isSetPreFetch = stmt->isSetPreFetch,
        .isDirectGetCount = false,
        .vertexScanArgs = {.vertex = cltVertex->vertex,
            .cltCataLabel = cltVertex->cltCataLabel,
            .filter = leftKey,
            .rightKey = NULL,
            .structFilterList = &cltVertex->structFilterList},
        .scanMode =  ExecScanGetScanMode(leftKey, NULL, drRunCtx)
    };
    // clang-format on
#ifdef FEATURE_HP_ANNSEQSCAN
    if (stmt->vecSearchCtx.isAnnSearch) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "use isExist by ann");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
#endif
    Status ret = CltSendDirectReadMsg(stmt, &drRunCtx->dsFetchArgs.protoHead);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // 查询之后fetch标记设置为false，第一次fetch不进行报文偏移
    *isExist = stmt->cacheRows > 0;
    DirectReadCtxClose(drRunCtx, DIRECT_ACCESS_CLOSE_HEAP_CURSOR);
    return ret;
}
