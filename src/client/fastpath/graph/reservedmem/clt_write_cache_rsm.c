/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: write cache rsm implement for client
 * Author:
 * Create: 2024-05-06
 */

#include "clt_write_cache.h"
#include "clt_graph_filter.h"

void CacheRsmUndoLogForCreatePage(WriteCacheDescT *desc, PageIdT nextPageId)
{
    DB_POINTER(desc);
    if (!IsNeedRsmUndo(desc)) {
        return;
    }
    RsmUndoRecordT *rsmUndoRecord = &desc->cltRsmUndoRecord;
    RsmUndoOpRecordT *opRecord = RsmUndoGetNextFreeRecord(rsmUndoRecord, rsmUndoRecord->usedUndoRecNum);
    opRecord->recordType = RSM_UNDO_WRITE_CACHE_CREATE_PAGE;
    opRecord->rsmWriteCacheCreatePageRec.deviceId = nextPageId.deviceId;
    opRecord->rsmWriteCacheCreatePageRec.blockId = nextPageId.blockId;
    RsmUndoMoveNextFreeRecord(rsmUndoRecord);
}

Status CltCacheCreateRsmNewPage(
    WriteCacheDescT *writeCacheDesc, WCachePageHeadT *prevPage, WCachePageHeadT **newPageHead)
{
    // 创建新页
    MdMgrT *mdMgr = (MdMgrT *)(void *)SeGetPageMgr((uint16_t)writeCacheDesc->dbInstanceId);
    if (mdMgr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "se get page manager.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    // 传入无效的labelId,页搬迁不搬迁
    PageIdT pageId;
    AllocPageParamT allocPageParam = {.spaceId = writeCacheDesc->tableSpaceIndex,
        .trmId = writeCacheDesc->writeCacheTrmId,
        .labelId = RSM_INVALID_LABEL_ID,
        .dbInstance = NULL,
        .labelRsmUndo = &writeCacheDesc->cltRsmUndoRecord};
    StatusInter innerRet = MdAllocPage(mdMgr, &allocPageParam, &pageId);
    if (innerRet != STATUS_OK_INTER) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc rsm page. innerRet = %" PRId32 "", (int32_t)innerRet);
        return GMERR_OUT_OF_MEMORY;
    }
    SHM_CRASHPOINT(SHM_CRASH_WC_SET_REQ_AFTER_ALLOC_NEW_PAGE);
    uint8_t *pagePtr = NULL;
    innerRet = MdGetPage(mdMgr, pageId, &pagePtr, ENTER_PAGE_NORMAL, true);
    if (innerRet != STATUS_OK_INTER || pagePtr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY,
            "get rsm page by pageid. innerRet = %" PRId32 ", pageId = %" PRIu32 ":%" PRIu32 "", (int32_t)innerRet,
            pageId.deviceId, pageId.blockId);
        return GMERR_OUT_OF_MEMORY;
    }
    // 偏移PageHeadT大小，因为这个PageHeadT是heap页默认的头，缓存页有自己的头
    WCachePageHeadT *pageHead = (WCachePageHeadT *)(void *)((char *)pagePtr + sizeof(PageHeadT));
    DbSpinInit(&pageHead->lock);
    pageHead->freeOffset = sizeof(WCachePageHeadT);
    pageHead->mergeBeginOffset = pageHead->freeOffset;
    pageHead->currPageId = pageId;
    pageHead->nextPageId = prevPage->nextPageId;
    SHM_CRASHPOINT(SHM_CRASH_WC_SET_REQ_AFTER_INIT_NEW_PAGE);
    CacheRsmUndoLogForCreatePage(writeCacheDesc, prevPage->nextPageId);
    SHM_CRASHPOINT(SHM_CRASH_WC_SET_REQ_AFTER_LOG_NEXT_PAGE_ID);
    prevPage->nextPageId = pageId;
    SHM_CRASHPOINT(SHM_CRASH_WC_SET_REQ_AFTER_INSERT_NEW_PAGE);
    CacheCltRsmUndoLogLeave(writeCacheDesc);
    SHM_CRASHPOINT(SHM_CRASH_WC_SET_REQ_AFTER_LEAVE_CREATE_PAGE_LOG);
    *newPageHead = pageHead;
    return GMERR_OK;
}

bool CltCheckRsmReqSize(uint32_t reqSize, const CltWriteCacheContextT *wcContext)
{
    return (reqSize + sizeof(PageHeadT) + sizeof(WCachePageHeadT)) <= wcContext->wcRunCtx->oriPageSize;
}
