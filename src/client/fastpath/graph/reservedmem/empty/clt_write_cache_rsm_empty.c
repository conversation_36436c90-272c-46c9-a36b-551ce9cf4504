/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: write cache rsm empty implement for client
 * Author:
 * Create: 2024-05-06
 */

#include "clt_write_cache_rsm.h"

Status CltCacheCreateRsmNewPage(
    WriteCacheDescT *writeCacheDesc, WCachePageHeadT *prevPage, WCachePageHeadT **newPageHead)
{
    return GMERR_OK;
}

bool CltCheckRsmReqSize(uint32_t reqSize, const CltWriteCacheContextT *wcContext)
{
    return false;
}
