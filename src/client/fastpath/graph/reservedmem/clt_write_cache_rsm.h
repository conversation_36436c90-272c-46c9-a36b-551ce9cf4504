/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: header file for GMDB client write cache rsm API
 * Create: 2024-05-06
 */

#ifndef CLT_WRITE_CACHE_RSM_H
#define CLT_WRITE_CACHE_RSM_H

#include "clt_write_cache.h"
#include "ee_merge_write_cache.h"

#ifdef __cplusplus
extern "C" {
#endif

Status CltCacheCreateRsmNewPage(
    WriteCacheDescT *writeCacheDesc, WCachePageHeadT *prevPage, WCachePageHeadT **newPageHead);

bool CltCheckRsmReqSize(uint32_t reqSize, const CltWriteCacheContextT *wcContext);

#ifdef __cplusplus
}
#endif

#endif /* CLT_WRITE_CACHE_RSM_H */
