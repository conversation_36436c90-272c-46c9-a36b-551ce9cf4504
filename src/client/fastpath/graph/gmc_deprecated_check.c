/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_deprecated_check.c
 * Description: realization of check deprecated interface of gmdb
 * Create: 2021-9-18
 */

#include "gmc_deprecated_check.h"
#include "clt_check.h"
#include "clt_utils.h"
#include "clt_error.h"

Status GmcGetLabelEstiMemInfoCheck(
    GmcStmtT *stmt, const char *labelJson, const char *configJson, const GmcMemSizeInfoT *memSizeInfoT)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(labelJson, CLT_MAX_LABEL_LENGTH, "labelJson");
    if (ret != GMERR_OK) {
        return ret;
    }
    if (memSizeInfoT == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter value of 'memSizeInfoT'");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

Status GmcSetScheduleTaskCfgCheck(GmcStmtT *stmt, uint32_t workload, uint32_t duration)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 工作量不能为0
    if (workload == 0) {
        DB_LOG_WARN(GMERR_INVALID_VALUE, "Workload is 0.");
        return GMERR_INVALID_VALUE;
    }
    // unit时间上限3600
    if (duration > CLT_MAX_DURATION) {
        DB_LOG_WARN(GMERR_INVALID_VALUE, "Duration exceed 3600, now duration is %" PRIu32 ".", duration);
        return GMERR_INVALID_VALUE;
    }
    return GMERR_OK;
}

Status GmcSetUserRequestWeightCheck(GmcStmtT *stmt, uint32_t weight, const char *userName, const char *processName)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 用户权重不能为0
    if (weight == 0) {
        DB_LOG_WARN(GMERR_INVALID_VALUE, "User weight is 0.");
        return GMERR_INVALID_VALUE;
    }
    ret = CltCheckStringValid(userName, MAX_USER_NAME_LEN, "uname");
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(processName, DB_MAX_PROC_NAME_LEN, "processName");
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static Status UserResourceCheck(
    GmcIpsUserResTypeE resType, const char *userName, const char *processName, uint32_t value)
{
    Status ret = CltCheckStringValid(userName, MAX_OS_USER_NAME_LENGTH, "uname");
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = CltCheckStringValid(processName, DB_MAX_PROC_NAME_LEN, "processName");
    if (ret != GMERR_OK) {
        return ret;
    }

    if (resType == GMC_USER_MAX_CONN_NUMBER) {
        ret = (value > USER_MAX_CONN_NUMBER) ? GMERR_INVALID_VALUE : GMERR_OK;
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "user max conn number.");
        return ret;
    }
    if (resType == GMC_USER_MAX_CONN_SPEED) {
        return GMERR_OK;
    }
    DB_SET_LASTERR(GMERR_INVALID_VALUE, "user resource type.");
    return GMERR_INVALID_VALUE;
}

Status GmcIpsSetUserResourcesLimitCheck(GmcStmtT *stmt, GmcIpsUserResT *userResource)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltPtrCheckWithErr(userResource);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = UserResourceCheck(
        userResource->resType, userResource->userName, userResource->processName, userResource->value);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static Status ConnResourceCheck(GmcIpsConnResTypeE resType, uint32_t value)
{
    if (resType == GMC_CONN_MAX_MESSAGE_BUF) {
        Status ret = (value < 256 || value > 2048) ? GMERR_INVALID_PARAMETER_VALUE : GMERR_OK;
        if (ret != GMERR_OK) {
            DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "conn max message buf.");
        }
        return ret;
    }
    if (resType == GMC_CONN_MAX_MEMCTX_SIZE) {
        return GMERR_OK;
    }
    DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "conn resource type.");
    return GMERR_INVALID_PARAMETER_VALUE;
}

Status GmcIpsSetConnResourcesLimitCheck(GmcStmtT *stmt, GmcIpsConnResT *connResource)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltPtrCheckWithErr(connResource);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = ConnResourceCheck(connResource->resType, connResource->value);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status GmcGetVertexLabelDegradeProgressCheck(GmcStmtT *stmt, const char *labelName)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (IsStringEmpty(labelName)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "labelName");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (CltStrLen(labelName) > MAX_TABLE_NAME_LEN) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "label %s exceeds the len limit", labelName);
        return GMERR_INVALID_VALUE;
    }

    return GMERR_OK;
}

Status GmcSetLogLevelCheck(GmcStmtT *stmt, const char *processName, uint32_t logLevel)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (logLevel >= (uint32_t)DB_LOG_LVL_BUTT) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "log level: %" PRIu32, logLevel);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return CltCheckStringValid(processName, DB_MAX_PROC_NAME_LEN, "processName");
}

Status GmcSetLogSwitchCheck(GmcStmtT *stmt, const char *processName, uint32_t logSwitch)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (logSwitch != (uint32_t) true && logSwitch != (uint32_t) false) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "log switch: %" PRIu32, logSwitch);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return CltCheckStringValid(processName, DB_MAX_PROC_NAME_LEN, "processName");
}

Status GmcRsmMigrationCheck(GmcStmtT *stmt, const char *path)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltCheckStringValid(path, DB_MAX_PATH, "rsmMigrationPath");
}
