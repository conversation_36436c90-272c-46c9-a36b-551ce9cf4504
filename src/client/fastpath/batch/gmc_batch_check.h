/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_batch_check.h
 * Description: File provide GMC APIs related to batch.
 *              Function order: DDL\DML\other func.
 *              Please keep new function in its corresponding position.
 * Author:
 * Create: 2021-10-23
 */

#ifndef GMC_BATCH_CHECK_H
#define GMC_BATCH_CHECK_H

#include "gmc_types.h"
#include "dm_meta_topo_label.h"
#include "gmc_dlr.h"

#ifdef __cplusplus
extern "C" {
#endif

Status GmcBatchAddDDLCheck(const GmcBatchT *batch, GmcOperationTypeE cmdType, const char *name, const char *json);
Status GmcBatchBufSizeCheck(const GmcBatchOptionT *batchOption, uint32_t bufSize);
Status GmcBatchErrCtrlCheck(const GmcBatchOptionT *batchOption, uint32_t batchErrCtrl);
Status GmcBatchExecOrderCheck(const GmcBatchOptionT *batchOption, uint32_t batchOrder);
Status GmcBatchResIdCheck(const GmcBatchRetT *batchRet, const char *vertexName, const void *resIdBuf);
Status GmcBatchResIdInfoCheck(
    const GmcBatchRetT *batchRet, const char *vertexName, const uint64_t *resIdBuf, const uint32_t *resIdNum);
Status GmcBatchAddKvDMLCheck(const GmcStmtT *stmt, const GmcBatchT *batch, GmcOperationTypeE cmdType);
Status GmcBatchExecuteAsyncCheck(const GmcBatchT *batch);
Status GmcBatchMergeOpsCheck(const GmcBatchT *batch);
Status GmcBatchAddVertexDMLCheck(const GmcBatchT *batch, const GmcStmtT *stmt);
GMDB_EXPORT Status GmcBatchAddEdgeDMLCheck(
    const GmcBatchT *batch, const GmcStmtT *stmt, const DmEdgeLabelT *edgeLabel, GmcOperationTypeE cmdType);
Status GmcBatchExecuteCheck(const GmcBatchT *batch, GmcBatchRetT *batchRet);

Status GmcBatchDeparseDlrErrorCountCheck(GmcStmtT *stmt, GmcBatchRetT *batchRet, uint32_t *errCount);
Status GmcBatchDeparseDlrErrorDetailInfoCheck(
    GmcStmtT *stmt, GmcBatchRetT *batchRet, GmcDlrErrDetailInfoT *errDetailInfos);
Status GmcSetDlrDataBufCheck(
    GmcStmtT *stmt, const GmcBatchT *batch, const GmcDlrDataBufT *dataBufT, const bool *setEof);
Status GmcSetDlrDataBufAttrCheck(
    GmcDlrDataBufT *dlrDataBuf, GmcDlrAttrTypeE attr, const void *value, uint32_t valueSize);

Status GmcGetDlrDataBufAttrCheck(
    const GmcDlrDataBufT *dlrDataBuf, GmcDlrAttrTypeE attr, void *value, uint32_t valueSize);
Status BatchErrorTransForV3(int32_t ret);

#ifdef __cplusplus
}
#endif

#endif /* GMC_BATCH_CHECK_H */
