/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: clt_batch_utils.h
 * Description: head file of clt_batch_utils.c
 * Create: 2024-10-14
 */
#ifndef CLT_BATCH_UTILS_H
#define CLT_BATCH_UTILS_H

#include "db_rpc_msg_op.h"
#include "adpt_define.h"
#include "clt_stmt.h"
#include "clt_batch.h"
#include "dm_meta_schema.h"
#include "gmc_types.h"

#ifdef __cplusplus
extern "C" {
#endif

Status CltBatchUpdateActualNum(GmcBatchT *batch);

Status CltBatchInitHeader(GmcBatchT *batch);

Status CltRebuildSendBuf(GmcBatchT *batch, bool checkRollback);

Status CltAddBatchCmd(GmcBatchT *batch, const ProtoHeadT *proto);

#ifdef __cplusplus
}
#endif

#endif
