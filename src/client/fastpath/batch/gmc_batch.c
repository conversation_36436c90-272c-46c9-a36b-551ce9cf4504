/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_batch.c
 * Description: realization of interface of gmdb batch
 * Create: 2021-10-23
 */

#include "gmc_batch.h"
#include "clt_batch.h"
#include "clt_check.h"
#include "clt_resource_pool.h"
#include "gmc_batch_check.h"

Status GmcBatchAddDDL(
    GmcBatchT *batch, GmcOperationTypeE cmdType, const char *labelName, const char *labelJson, const char *configJson)
{
    Status ret = GmcBatchAddDDLCheck(batch, cmdType, labelName, labelJson);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckConfigJsonLength(configJson);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltAddBatchDDLCmd(batch, cmdType, labelName, labelJson, configJson);
}

Status GmcBatchMergeOps(GmcBatchT *batch)
{
    Status ret = GmcBatchMergeOpsCheck(batch);
    if (ret != GMERR_OK) {
        return ret;
    }

    return ExecBatchMergeOps(batch);
}

Status GmcBatchAddDML(GmcBatchT *batch, GmcStmtT *stmt)
{
    Status ret = GmcBatchAddVertexDMLCheck(batch, stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return CltAddBatchVertexDMLCmd(batch, stmt);
}

Status GmcBatchAddKvDML(GmcBatchT *batch, const GmcStmtT *stmt, GmcOperationTypeE cmdType)
{
    Status ret = GmcBatchAddKvDMLCheck(stmt, batch, cmdType);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltAddBatchKvDMLCmd(batch, stmt, cmdType);
}

Status GmcBatchPrepare(GmcConnT *conn, const GmcBatchOptionT *batchOption, GmcBatchT **batch)
{
    Status ret = CltPtr2CheckWithErr(conn, batch);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (CltCheckConnStat(conn) != GMERR_OK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (batchOption != NULL && !batchOption->inited) {
        DB_LOG_AND_SET_LASERR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "batchOption uninited.");
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    if (batchOption != NULL && batchOption->batchType == GMC_BATCH_YANG &&
        batchOption->batchOrder != GMC_BATCH_ORDER_STRICT) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "yang batch order.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return CltBatchPrepare(conn, batchOption, batch);
}

Status GmcBatchExecute(GmcBatchT *batch, GmcBatchRetT *batchRet)
{
    Status ret = GmcBatchExecuteCheck(batch, batchRet);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ExecBatchExecute(batch, batchRet);
}

Status GmcBatchBindStmt(GmcBatchT *batch, GmcStmtT *stmt)
{
    // bind 这一步就相当于add vertex dml的开始
    Status ret = GmcBatchAddVertexDMLCheck(batch, stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    batch->bindStmt = stmt;
    stmt->batch = batch;
    return GMERR_OK;
}

void GmcBatchUnbindStmt(GmcBatchT *batch, GmcStmtT *stmt)
{
    if (batch == NULL || stmt == NULL) {
        return;
    }
    if (CltCheckBatchStat(batch) == GMERR_OK) {
        batch->bindStmt = NULL;
    }
    if (CltCheckStmtStat(stmt) == GMERR_OK) {
        stmt->batch = NULL;
    }
}

Status GmcBatchExecuteAsync(GmcBatchT *batch, GmcBatchDoneT userCb, void *userData)
{
    // 恢复batchRet避免结果不对
    Status ret = GmcBatchExecuteAsyncCheck(batch);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return ExecBatchExecuteAsync(batch, userCb, userData);
}

Status GmcBatchOptionInit(GmcBatchOptionT *batchOption)
{
    if (batchOption == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "batchOption");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    CltBatchOptionInit(batchOption);
    return GMERR_OK;
}

Status GmcBatchOptionSetExecOrder(GmcBatchOptionT *batchOption, uint32_t batchOrder)
{
    Status ret = GmcBatchExecOrderCheck(batchOption, batchOrder);
    if (ret != GMERR_OK) {
        return ret;
    }
    batchOption->batchOrder = batchOrder;
    return ret;
}

Status GmcBatchOptionSetBufRecycleSize(GmcBatchOptionT *batchOption, uint32_t bactchRecycleSize)
{
    Status ret = GmcBatchBufSizeCheck(batchOption, bactchRecycleSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    batchOption->batchRecycleSize = bactchRecycleSize;
    return ret;
}

/*
 * 补丁类型：光启补丁Demo
 * 补丁文件：patch/guangqi_patch/HP0203/gmc_batch.c
 * 函数类型：补丁函数
 * 变更说明：如有变更请评估影响性并同步修改补丁代码
 */
Status GmcBatchOptionSetBatchType(GmcBatchOptionT *batchOption, GmcBatchTypeE batchType)
{
    if (batchOption == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Batch option");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    if (batchType >= GMC_BATCH_BUTT) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "batch type");
        return GMERR_INVALID_VALUE;
    }

    if (batchType == GMC_BATCH_YANG) {
        if (DbIsHpeEnv()) {
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
    }

    batchOption->batchType = batchType;
    return GMERR_OK;
}

Status GmcBatchOptionSetMaxBatchOpNum(GmcBatchOptionT *batchOption, uint32_t maxBatchOpNum)
{
    if (batchOption == NULL) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "Batch option");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (maxBatchOpNum < DB_MAX_BATCH_OP_NUM_CONFIG_MIN || maxBatchOpNum > DB_MAX_BATCH_OP_NUM_CONFIG_MAX) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "Configurable maxBatchOpNum range is 1 to 4!");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    batchOption->maxBatchOpNum = maxBatchOpNum * DB_MAX_BATCH_OP_NUM_CONFIG_UNIT;
    return GMERR_OK;
}

Status GmcBatchOptionSetBufLimitSize(GmcBatchOptionT *batchOption, uint32_t bactchLimitSize)
{
    // 不用bactchLimitSize的设置检查，等GmcBatchPrepare时根据连接类型检查
    Status ret = CltPtrCheckWithErr(batchOption);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (bactchLimitSize == 0) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_VALUE, "bactchLimitSize Limit over range");
        return GMERR_INVALID_VALUE;
    }
    batchOption->batchLimitSize = bactchLimitSize;
    return ret;
}

// batch 句柄操作
Status GmcBatchDestroy(GmcBatchT *batch)
{
    if (SECUREC_UNLIKELY(batch == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "batch");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (SECUREC_UNLIKELY(CltCheckBatchStat(batch) != GMERR_OK)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (SECUREC_UNLIKELY(batch->conn->bigObjectStand != 0)) {
        UpdateConnObjMemCtxStatistics(batch->conn);
    }
    CltBatchDestroy(batch);
    return GMERR_OK;
}

Status GmcBatchReset(GmcBatchT *batch)
{
    if (SECUREC_UNLIKELY(batch == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "batch");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (SECUREC_UNLIKELY(CltCheckBatchStat(batch) != GMERR_OK)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (SECUREC_UNLIKELY(batch->conn->bigObjectStand != 0)) {
        UpdateConnObjMemCtxStatistics(batch->conn);
    }
    CltBatchResetSendBuf(batch);
    return GMERR_OK;
}

// batch result系列
Status GmcBatchDeparseRet(GmcBatchRetT *batchRet, uint32_t *totalNum, uint32_t *successNum)
{
    Status ret = CltPtr3CheckWithErr(batchRet, totalNum, successNum);
    if (ret != GMERR_OK) {
        return ret;
    }
    FixBufferT *buffer = batchRet->batchBuf;
    if (buffer == NULL || !(*batchRet->batchBufValid)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY, "batch buffer");
        *totalNum = 0;
        *successNum = 0;
        return GMERR_INVALID_PROPERTY;
    }

    FixBufSeek(buffer, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE);
    const BatchRespT *batchResp = SecureFixBufGetData(buffer, sizeof(BatchRespT));
    if (batchResp == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "batch response");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *totalNum = batchResp->totalNum;
    *successNum = batchResp->successNum;
    return GMERR_OK;
}

/*
 * opCode(uint32)|currOpDataLen(uint32)|affectRows(uint32)|resIdGroupCnt(uint32)|
 * vertexLabelNameLen(uint32)|vertexLabelName|resIdCount(uint32)|resId(uint64)
 */
#if !defined FEATURE_STREAM || defined TS_MULTI_INST
static Status GetResIdBeforeGroupCount(FixBufferT *fixBuffer)
{
    MsgOpcodeRpcE opCode;
    Status ret = SecureFixBufGetUint32(fixBuffer, &opCode);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get opCode when GetResIdGroupCount.");
        return ret;
    }
    uint32_t currOpDataLen = 0;
    ret = SecureFixBufGetUint32(fixBuffer, &currOpDataLen);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get currOpDataLen when GetResIdGroupCount.");
        return ret;
    }
    if (opCode != MSG_OP_RPC_INSERT_VERTEX && opCode != MSG_OP_RPC_REPLACE_VERTEX) {
        FixBufSeek(fixBuffer, FixBufGetSeekPos(fixBuffer) + currOpDataLen);
        return GMERR_INVALID_OPTION;
    }
    uint32_t affectRows = 0;
    ret = SecureFixBufGetUint32(fixBuffer, &affectRows);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get affectRows when GetResIdGroupCount.");
        return ret;
    }
    if (affectRows == 0) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return ret;
}

static Status ExecGetResIdNumBatch(FixBufferT *fixBuffer, const char *labelName, uint32_t *bufLen)
{
    Status ret = GMERR_OK;
    while (FixBufGetSeekPos(fixBuffer) < FixBufGetPos(fixBuffer)) {
        ret = GetResIdBeforeGroupCount(fixBuffer);
        // 略过非插入操作
        if (ret == GMERR_INVALID_OPTION) {
            continue;
        } else if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get item when GetResIdBeforeGroupCount.");
            break;
        }
        ret = ExecGetResIdNum(fixBuffer, labelName, bufLen);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get item when ExecGetResIdNum.");
            break;
        }
    }
    return ret;
}

Status GmcBatchGetResIdNum(GmcBatchRetT *batchRet, const char *vertexName, uint32_t *resIdNum)
{
    Status ret = GmcBatchResIdCheck(batchRet, vertexName, resIdNum);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 先设置成0之后再填入正确值
    *resIdNum = 0;
    // 检查batchRet中的buffer是否可用
    if (batchRet->batchBuf == NULL || !(*batchRet->batchBufValid)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY, "batch buffer");
        return GMERR_INVALID_PROPERTY;
    }
    FixBufferT *fixBuffer = batchRet->batchBuf;
    FixBufSeek(fixBuffer, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE + sizeof(BatchRespT));
    ret = ExecGetResIdNumBatch(fixBuffer, vertexName, resIdNum);

    if (*resIdNum == 0) {
        return GMERR_DATA_EXCEPTION;
    }
    return ret;
}

static Status ExecGetResIdInfoBatch(
    FixBufferT *fixBuffer, const char *labelName, uint32_t *resIdNum, uint64_t *resourceId)
{
    Status ret = GMERR_OK;
    uint32_t bufIndex = 0;
    uint32_t tempSeekPos = FixBufGetSeekPos(fixBuffer);
    while (FixBufGetSeekPos(fixBuffer) < FixBufGetPos(fixBuffer)) {
        ret = GetResIdBeforeGroupCount(fixBuffer);
        // 略过非插入操作
        if (ret == GMERR_INVALID_OPTION) {
            continue;
        } else if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get item when GetResIdBeforeGroupCount.");
            break;
        }
        ret = ExecGetResIdInfo(fixBuffer, labelName, *resIdNum, resourceId, &bufIndex);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get item when ExecGetResIdInfoInner.");
            break;
        }
    }
    // 此时resIdNum不正确
    if (ret == GMERR_DATA_EXCEPTION && bufIndex == *resIdNum) {
        FixBufSeek(fixBuffer, tempSeekPos);
        (void)ExecGetResIdNumBatch(fixBuffer, labelName, resIdNum);
    }
    return ret;
}

Status GmcBatchGetResIdInfo(GmcBatchRetT *batchRet, const char *vertexName, uint64_t *resIdBuf, uint32_t *resIdNum)
{
    Status ret = GmcBatchResIdInfoCheck(batchRet, vertexName, resIdBuf, resIdNum);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (batchRet->batchBuf == NULL || !(*batchRet->batchBufValid)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY, "batch buffer");
        return GMERR_INVALID_PROPERTY;
    }
    FixBufferT *fixBuffer = batchRet->batchBuf;
    FixBufSeek(fixBuffer, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE + sizeof(BatchRespT));
    return ExecGetResIdInfoBatch(fixBuffer, vertexName, resIdNum, resIdBuf);
}
#endif

Status GmcSetDlrDataBuf(GmcStmtT *stmt, GmcBatchT *batch, GmcDlrDataBufT *dlrDataBufT, bool *setEof)
{
    // check中保证类型为CLT_STMT_TYPE_VERTEX
    Status ret = GmcSetDlrDataBufCheck(stmt, batch, dlrDataBufT, setEof);
    if (ret != GMERR_OK) {
        return ret;
    }
    *setEof = false;
    return CltSetDlrDataBufToSend(stmt, batch, dlrDataBufT, setEof);
}

Status GmcBatchDeparseDlrErrorCount(GmcStmtT *stmt, GmcBatchRetT *batchRet, uint32_t *errCount)
{
    Status ret = GmcBatchDeparseDlrErrorCountCheck(stmt, batchRet, errCount);
    if (ret != GMERR_OK) {
        return ret;
    }
    FixBufferT *buffer = batchRet->batchBuf;

    if (buffer == NULL || !(*batchRet->batchBufValid)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_BUFFER, "batch buffer");
        *errCount = 0;
        return GMERR_INVALID_BUFFER;
    }
    FixBufSeek(buffer, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE);
    const BatchRespT *batchResp = SecureFixBufGetData(buffer, sizeof(BatchRespT));
    if (batchResp == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "response when parse dlr mistake count.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t offset = 0;
    ret = SecureFixBufGetUint32(buffer, &offset);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (offset >= buffer->pos) {
        DB_LOG_AND_SET_LASERR(
            GMERR_DATA_EXCEPTION, "offset is %" PRIu32 "exceed the buf pos is %" PRIu32 ".", offset, buffer->pos);
        return GMERR_DATA_EXCEPTION;
    }
    FixBufSeek(buffer, offset);
    return SecureFixBufGetUint32(buffer, errCount);
}

Status GmcBatchDeparseDlrErrorDetailInfo(
    GmcStmtT *stmt, GmcBatchRetT *batchRet, GmcDlrErrDetailInfoT *errDetailInfos, uint32_t errCount)
{
    Status ret = GmcBatchDeparseDlrErrorDetailInfoCheck(stmt, batchRet, errDetailInfos);
    if (ret != GMERR_OK) {
        return ret;
    }
    FixBufferT *buffer = batchRet->batchBuf;
    if (buffer == NULL || !(*batchRet->batchBufValid)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_BUFFER, "batch buffer");
        return GMERR_INVALID_BUFFER;
    }
    FixBufSeek(buffer, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE);
    const BatchRespT *batchResp = SecureFixBufGetData(buffer, sizeof(BatchRespT));
    if (batchResp == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "batch response");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t detailInfoOffset = 0;
    ret = SecureFixBufGetUint32(buffer, &detailInfoOffset);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (detailInfoOffset >= buffer->pos) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "detailInfoOffset is %" PRIu32 " exceed the buffer pos is %" PRIu32 ".", detailInfoOffset, buffer->pos);
        return GMERR_DATA_EXCEPTION;
    }
    FixBufSeek(buffer, detailInfoOffset);
    uint32_t errCountInner = 0;
    ret = SecureFixBufGetUint32(buffer, &errCountInner);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (errCount != errCountInner) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "errCount %" PRIu32 ", errCountInner is %" PRIu32 ".",
            errCount, errCountInner);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return CltDlrBatchDeparseErrInfo(buffer, errCount, errDetailInfos);
}

Status GmcSetDlrDataBufAttr(GmcDlrDataBufT *dlrDataBuf, GmcDlrAttrTypeE attr, const void *value, uint32_t valueSize)
{
    Status ret = GmcSetDlrDataBufAttrCheck(dlrDataBuf, attr, value, valueSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltSetDlrDataBufAttr(dlrDataBuf, attr, value);
}

Status GmcGetDlrDataBufAttr(const GmcDlrDataBufT *dlrDataBuf, GmcDlrAttrTypeE attr, void *value, uint32_t valueSize)
{
    Status ret = GmcGetDlrDataBufAttrCheck(dlrDataBuf, attr, value, valueSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltGetDlrDataBufAttr(dlrDataBuf, attr, value);
}

Status GmcDlrDataBufInit(GmcDlrDataBufT *dlrDataBuf, void *buf, uint32_t bufSize)
{
    Status ret = CltPtr2CheckWithErr(dlrDataBuf, buf);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltDlrDataBufInit(dlrDataBuf, (char *)buf, bufSize);
}
