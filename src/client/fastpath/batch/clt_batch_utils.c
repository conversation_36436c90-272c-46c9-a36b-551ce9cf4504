/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: clt_batch_utils.c
 * Description: util functions for clt_batch.c
 * Create: 2024-10-14
 */

#include "clt_fill_msg.h"
#include "gmc_batch_check.h"
#include "clt_batch_utils.h"

Status CltBatchUpdateActualNum(GmcBatchT *batch)
{
    const unsigned batchCmdMaxNum = (batch->conn->isLobConn || batch->option.batchType == GMC_BATCH_YANG) ?
                                        DB_LOB_MAX_BATCH_OP_NUM :
                                        batch->option.maxBatchOpNum;
    if (SECUREC_UNLIKELY(batch->actualNum >= batchCmdMaxNum)) {
        // 批量超过最大回退时，需要把加的vertexNum回退回去，batch->lastObjectNumPos记录了报文中vertexNum的位置
        DB_LOG_INFO_AND_SET_LASTERR(GMERR_BATCH_BUFFER_FULL, "The num of batch op exceeds the upper limit.");
        return GMERR_BATCH_BUFFER_FULL;
    }
    batch->actualNum++;
    return GMERR_OK;
}

Status CltBatchInitHeader(GmcBatchT *batch)
{
    FixBufferT *buffer = &batch->batchSendBuf;
    const GmcBatchOptionT *batchOption = &batch->option;
    MsgOpcodeRpcE opCode = MSG_OP_RPC_BATCH;

#if defined(FEATURE_STREAM) || defined(STREAM_MULTI_INST)
    if (batch->isStream) {
        opCode = MSG_OP_RPC_STREAM_BATCH_INSERT;
    }
#endif
    Status ret = CltFillMsgHeader(buffer, opCode, 0);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (batch->isDataService) {
        MsgHeaderT *msg = RpcPeekMsgHeader(buffer);
        msg->modelType = MODEL_DATALOG;
    }

    uint32_t offset;
    ret = SecureFixBufReserveDataOffset(buffer, sizeof(BatchHeaderT), &offset);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "header create worthless when init batch header");
        return ret;
    }

    BatchHeaderT *header = SecureFixBufOffsetToAddr(buffer, offset);
#ifdef SECUREFIXBUF
    if (SECUREC_UNLIKELY(header == NULL)) {
        return GMERR_CONNECTION_RESET_BY_PEER;
    }
#endif
    header->totalNum = 0;
    header->batchErrCtl = batchOption->batchErrCtl;
    header->batchOrder = batchOption->batchOrder;
    header->totalOpNum = 0;
    header->batchType = batchOption->batchType;
    header->diffType = batchOption->diffType;
    header->yangTmpIdListLen = 0;

    // 记录回滚点，后续没有添加成功需要回滚到这个点
    batch->rollBackPos = FixBufGetPos(buffer);
    // 当rollBackPos不为0，这两个字段才有意义
    batch->actualNum = 0;
    batch->lastOpInfo = (CltBatchLastOpInfoT){};
    return GMERR_OK;
}

Status CltRebuildSendBuf(GmcBatchT *batch, bool checkRollback)
{
    // 如果sendBuf异常，重建sendbuf
    FixBufferT *buffer = &batch->batchSendBuf;

    // 异常情况1：之前申请失败或主动释放导致buffer为空
    if (SECUREC_UNLIKELY(batch->rollBackPos == 0)) {
        if (SECUREC_UNLIKELY(batch->batchState != CLT_BATCH_STATE_PREPARED)) {
            // sendBuf重建只能在起始阶段
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_BUFFER, "sendBuf rebuild not in the prepare stage");
            return GMERR_INVALID_BUFFER;
        }
        // 业务每次在reset之后会rollBackPos置零，重新init
        return CltBatchInitHeader(batch);
    }

    // 异常情况2：之前填写报文的操作没有成功，回滚点不符合预期
    bool rollback = checkRollback && (FixBufGetPos(buffer) != batch->rollBackPos);
    if (SECUREC_UNLIKELY(rollback)) {
        // 执行回滚。注意rollBackPos在buffer不为空时才有意义
        FixBufInitPut(buffer, batch->rollBackPos);
    }

    return GMERR_OK;
}

/**
 * batch add 操作非优化统一接口
 * 优化接口在CltAddBatchVertexDMLCmd
 * 1. 报文结构为 : opCode | msg body
 * 2. 检查报文大小
 * 3. 更细batch头
 * 4. 更新批量回滚点
 * END : 回归报错统一出口
 */
Status CltAddBatchCmd(GmcBatchT *batch, const ProtoHeadT *proto)
{
    Status ret = CltRebuildSendBuf(batch, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 1. 填充报文 opcode | msg body
    FixBufferT *buffer = &batch->batchSendBuf;
    ret = SecureFixBufPutInt32(buffer, (int32_t)proto->opCode);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "clt add batch cmd when fill opcode.");
        goto END;
    }
    ret = CltFillMsgBody(buffer, proto);
    if (ret != GMERR_OK) {
        ret = BatchErrorTransForV3(ret);
        DB_LOG_AND_SET_LASERR(ret, "clt add batch cmd when fill message body.");
        goto END;
    }
    // 2.检查buffer大小
    if (FixBufGetPos(buffer) > batch->option.batchLimitSize * DB_KIBI) {
        ret = GMERR_BATCH_BUFFER_FULL;
        DB_LOG_AND_SET_LASERR(ret, "clt add batch cmd.");
        goto END;
    }
    // 3.修改和检查报文头
    ret = CltBatchUpdateActualNum(batch);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "clt add batch cmd when update the actual num.");
        goto END;
    }
    // 全部添加成功，更新header中的条数信息
    MsgBufUpdateBatchNum(buffer, false, 0, batch->actualNum);
    // 4.更新回滚需要的pos，添加成功后记录pos
    batch->rollBackPos = FixBufGetPos(buffer);
    return ret;
END:
    // 集中报错回滚
    FixBufInitPut(buffer, batch->rollBackPos);
    return ret;
}
