/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_batch_check.c
 * Description: realization of interface of gmdb batch check
 * Create: 2021-10-23
 */

#include "gmc_batch_check.h"
#include "gmc_internal_types.h"
#include "clt_check.h"
#include "clt_batch.h"
#include "dm_yang_interface.h"

static Status CheckBatchStateForDML(const GmcBatchT *batch, const GmcStmtT *stmt)
{
    if (SECUREC_UNLIKELY(batch == NULL || stmt == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "batch or stmt");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (SECUREC_UNLIKELY(CltCheckBatchStat(batch) != GMERR_OK)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (SECUREC_UNLIKELY(batch->batchState == CLT_BATCH_STATE_DDL)) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "DDL batch state");
        return GMERR_INVALID_VALUE;
    }
    return GMERR_OK;
}

Status GmcBatchAddDDLCheck(const GmcBatchT *batch, GmcOperationTypeE cmdType, const char *name, const char *json)
{
    if (SECUREC_UNLIKELY(batch == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "batch");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (SECUREC_UNLIKELY(batch->batchState == CLT_BATCH_STATE_DML)) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "DDL batch state");
        return GMERR_INVALID_VALUE;
    }
    switch (cmdType) {
        case GMC_OPERATION_CREATE_VERTEX_LABEL:
        case GMC_OPERATION_CREATE_EDGE_LABEL:
        case GMC_OPERATION_ALTER_VERTEX_LABEL:
            if (json == NULL) {
                DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter of 'labelJson'");
                return GMERR_NULL_VALUE_NOT_ALLOWED;
            }
            break;
        case GMC_OPERATION_DROP_VERTEX_LABEL:
        case GMC_OPERATION_DROP_EDGE_LABEL:
        case GMC_OPERATION_CREATE_KV_TABLE:
        case GMC_OPERATION_DROP_KV_TABLE:
            if (name == NULL) {
                DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Parameter of 'name'");
                return GMERR_NULL_VALUE_NOT_ALLOWED;
            }
            break;
        default:
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_VALUE, "cmd type");
            return GMERR_INVALID_VALUE;
    }
    return GMERR_OK;
}

Status GmcBatchAddVertexDMLCheck(const GmcBatchT *batch, const GmcStmtT *stmt)
{
    Status ret = CheckBatchStateForDML(batch, stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (CltCheckStmtStat(stmt) != GMERR_OK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    ret = CltCheckStmtType(stmt, CLT_STMT_TYPE_VERTEX);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    const CltOperVertexT *cltVertex = CltGetConstOperationContext(stmt);
    if (SECUREC_UNLIKELY((
            batch->option.batchType == GMC_BATCH_YANG && !DmIsYangVertexLabel(cltVertex->cltCataLabel->vertexLabel)))) {
        char *topRecordName = MEMBER_PTR(cltVertex->cltCataLabel->vertexLabel->metaVertexLabel, topRecordName);
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "%s not yang vertex label", topRecordName);
        return GMERR_INVALID_VALUE;
    }
    return ret;
}

Status GmcBatchAddEdgeDMLCheck(
    const GmcBatchT *batch, const GmcStmtT *stmt, const DmEdgeLabelT *edgeLabel, GmcOperationTypeE cmdType)
{
    Status ret = CheckBatchStateForDML(batch, stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStmtType(stmt, CLT_STMT_TYPE_EDGE);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(edgeLabel == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "edgeLabel.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (SECUREC_UNLIKELY(cmdType != GMC_OPERATION_INSERT && cmdType != GMC_OPERATION_DELETE)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "command type.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

Status GmcBatchAddKvDMLCheck(const GmcStmtT *stmt, const GmcBatchT *batch, GmcOperationTypeE cmdType)
{
    Status ret = CheckBatchStateForDML(batch, stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (CltCheckStmtStat(stmt) != GMERR_OK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    ret = CltCheckStmtType(stmt, CLT_STMT_TYPE_KV);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(cmdType != GMC_OPERATION_INSERT && cmdType != GMC_OPERATION_DELETE)) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "command type");
        return GMERR_INVALID_VALUE;
    }
    return ret;
}

Status GmcBatchBufSizeCheck(const GmcBatchOptionT *batchOption, uint32_t bufSize)
{
    const uint32_t bufMaxSize = 2048;
    Status ret = CltPtrCheckWithErr(batchOption);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(bufSize > bufMaxSize)) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "buffer size Limit over range");
        return GMERR_INVALID_VALUE;
    }
    return GMERR_OK;
}

Status GmcBatchErrCtrlCheck(const GmcBatchOptionT *batchOption, uint32_t batchErrCtrl)
{
    Status ret = CltPtrCheckWithErr(batchOption);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(batchErrCtrl >= GMC_BATCH_ERR_CTRL_BUTT)) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "batchErrCtrl");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

Status GmcBatchExecOrderCheck(const GmcBatchOptionT *batchOption, uint32_t batchOrder)
{
    Status ret = CltPtrCheckWithErr(batchOption);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(batchOrder >= GMC_BATCH_ORDER_BUTT)) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "batchOrder");
        return GMERR_INVALID_VALUE;
    }
    return GMERR_OK;
}

Status GmcBatchResIdInfoCheck(
    const GmcBatchRetT *batchRet, const char *vertexName, const uint64_t *resIdBuf, const uint32_t *resIdNum)
{
    Status ret = GmcBatchResIdCheck(batchRet, vertexName, resIdBuf);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(resIdNum == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "resIdNum");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

Status GmcBatchResIdCheck(const GmcBatchRetT *batchRet, const char *vertexName, const void *resIdBuf)
{
    Status ret = CltPtrCheckWithErr(batchRet);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(resIdBuf == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "resIdBuf");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    ret = CltCheckLabelNameLength(vertexName);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

ALWAYS_INLINE Status GmcBatchExecuteCheck(const GmcBatchT *batch, GmcBatchRetT *batchRet)
{
    Status ret = CltPtr2CheckWithErr(batch, batchRet);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(CltCheckBatchStat(batch) != GMERR_OK)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    batchRet->batchBuf = NULL;
    ret = CltCheckConnType(batch->conn, GMC_CONN_TYPE_SYNC);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(batch->batchState == CLT_BATCH_STATE_PREPARED)) {
        DB_SET_LASTERR(GMERR_INVALID_PROPERTY, "no batch command");
        return GMERR_INVALID_PROPERTY;
    }
    return ret;
}

Status GmcBatchExecuteAsyncCheck(const GmcBatchT *batch)
{
    if (SECUREC_UNLIKELY(batch == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "batch");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (SECUREC_UNLIKELY(CltCheckBatchStat(batch) != GMERR_OK)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    Status ret = CltCheckConnType(batch->conn, GMC_CONN_TYPE_ASYNC);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(batch->batchState == CLT_BATCH_STATE_PREPARED)) {
        DB_SET_LASTERR(GMERR_INVALID_PROPERTY, "no batch command");
        return GMERR_INVALID_PROPERTY;
    }
    return GMERR_OK;
}

Status GmcBatchMergeOpsCheck(const GmcBatchT *batch)
{
    if (SECUREC_UNLIKELY(batch == NULL)) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "batch");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    if (SECUREC_UNLIKELY(CltCheckBatchStat(batch) != GMERR_OK)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    if (SECUREC_UNLIKELY(!batch->canMerge)) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "batch can't be merged");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    if (SECUREC_UNLIKELY(batch->option.diffType != GMC_YANG_DIFF_OFF)) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "batch can't be merged when diff not off");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    if (SECUREC_UNLIKELY(batch->batchState != CLT_BATCH_STATE_DML)) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "batch command");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    return GMERR_OK;
}

Status GmcSetDlrDataBufCheck(GmcStmtT *stmt, const GmcBatchT *batch, const GmcDlrDataBufT *dataBufT, const bool *setEof)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    // datalog表放开GMC_OPERATION_INSERT操作
    bool isDtlLabel = CltGetIsDtlLabel(stmt);
    if (!isDtlLabel && SECUREC_UNLIKELY(stmt->operationType != GMC_OPERATION_REPLAY)) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "operation type");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (stmt->stmtType != CLT_STMT_TYPE_VERTEX) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "vertex type");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    ret = CltPtr2CheckWithErr(stmt->conn, batch);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (SECUREC_UNLIKELY(CltCheckBatchStat(batch) != GMERR_OK)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    if (SECUREC_UNLIKELY(dataBufT == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "batch address");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    if (SECUREC_UNLIKELY(dataBufT->buf == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "dlr data buf address");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    if (SECUREC_UNLIKELY(setEof == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "setEof address");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    CltDlrDataBufHeaderT *bufHeader = (CltDlrDataBufHeaderT *)dataBufT->buf;
    if (SECUREC_UNLIKELY(bufHeader->writeNums > bufHeader->operationNums)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_VALUE,
            "the dlr data buf write nums %" PRIu32 " is more than operation nums %" PRIu32 ".", bufHeader->writeNums,
            bufHeader->operationNums);
        return GMERR_INVALID_VALUE;
    }
    if (SECUREC_UNLIKELY(bufHeader->seekPos != DLR_BUF_HEADER_ALIGN_SIZE)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_BUFFER, "dlr buffer used seekpos %u", bufHeader->seekPos);
        return GMERR_INVALID_BUFFER;
    }
    return GMERR_OK;
}

Status GmcBatchDeparseDlrErrorCountCheck(GmcStmtT *stmt, GmcBatchRetT *batchRet, uint32_t *errCount)
{
    Status ret = CltPtr3CheckWithErr(stmt, batchRet, errCount);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (CltCheckStmtStat(stmt) != GMERR_OK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (SECUREC_UNLIKELY(stmt->operationType != GMC_OPERATION_REPLAY)) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "operation type");
        return GMERR_INVALID_VALUE;
    }
    return GMERR_OK;
}

Status GmcBatchDeparseDlrErrorDetailInfoCheck(
    GmcStmtT *stmt, GmcBatchRetT *batchRet, GmcDlrErrDetailInfoT *errDetailInfos)
{
    Status ret = CltPtr3CheckWithErr(stmt, batchRet, errDetailInfos);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (SECUREC_UNLIKELY(stmt->operationType != GMC_OPERATION_REPLAY)) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "operation type");
        return GMERR_INVALID_VALUE;
    }
    return GMERR_OK;
}

Status GmcSetDlrDataBufAttrCheck(
    GmcDlrDataBufT *dlrDataBuf, GmcDlrAttrTypeE attr, const void *value, uint32_t valueSize)
{
    if (dlrDataBuf == NULL || dlrDataBuf->buf == NULL || value == NULL) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "pointer when setting attr.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (dlrDataBuf->bufSize <= DLR_BUF_HEADER_ALIGN_SIZE) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "dlr data buf size for setting.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    Status ret = GMERR_OK;
    switch (attr) {
        case GMC_DLR_INITIAL_LOAD_IS_EOF:
        case GMC_DLR_REAL_FETCH_NUM:
            DB_SET_LASTERR((ret = GMERR_FEATURE_NOT_SUPPORTED), "set in dlr attribute types");
            break;
        case GMC_DLR_MAX_FETCH_NUM:
            if (valueSize < sizeof(uint32_t)) {
                DB_SET_LASTERR((ret = GMERR_INVALID_PARAMETER_VALUE), "dlr attr value size.");
                break;
            }
            uint32_t fetchNum = *(const uint32_t *)value;
            if (fetchNum == 0 || fetchNum > DLR_MAX_FETCH_NUM) {
                DB_SET_LASTERR((ret = GMERR_INVALID_PARAMETER_VALUE), "value.");
            }
            break;
        case GMC_DLR_ATTR_BUTT:
        default:
            DB_SET_LASTERR((ret = GMERR_INVALID_PARAMETER_VALUE), "dlr attribute type");
            break;
    }
    return ret;
}

Status GmcGetDlrDataBufAttrCheck(
    const GmcDlrDataBufT *dlrDataBuf, GmcDlrAttrTypeE attr, void *value, uint32_t valueSize)
{
    if (dlrDataBuf == NULL || dlrDataBuf->buf == NULL || value == NULL) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "pointer when getting attr.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (dlrDataBuf->bufSize <= DLR_BUF_HEADER_ALIGN_SIZE) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "dlr data buf size is too small for getting.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    Status ret = GMERR_OK;
    switch (attr) {
        case GMC_DLR_INITIAL_LOAD_IS_EOF:
            if (valueSize < sizeof(bool)) {
                DB_SET_LASTERR((ret = GMERR_INVALID_PARAMETER_VALUE), "dlr attr value size.");
            }
            break;
        case GMC_DLR_MAX_FETCH_NUM:
            DB_SET_LASTERR((ret = GMERR_FEATURE_NOT_SUPPORTED), "set in dlr attribute types");
            break;
        case GMC_DLR_REAL_FETCH_NUM:
            if (valueSize < sizeof(uint32_t)) {
                DB_SET_LASTERR((ret = GMERR_INVALID_PARAMETER_VALUE), "dlr attr value size.");
            }
            break;
        case GMC_DLR_ATTR_BUTT:
        default:
            DB_SET_LASTERR((ret = GMERR_INVALID_PARAMETER_VALUE), "dlr attribute type");
            break;
    }
    return ret;
}

Status BatchErrorTransForV3(int32_t ret)
{
    switch (ret) {
        case GMERR_PROGRAM_LIMIT_EXCEEDED:
            return GMERR_BATCH_BUFFER_FULL;
        default:
            break;
    }
    return ret;
}
