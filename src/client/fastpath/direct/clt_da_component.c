/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 * Description: Access Method for directwrite and directread
 * Author:
 * Create:
 */

#include "clt_da_write.h"
#include "db_direct_msg_pool.h"
#include "srv_data_fastpath_dw_pubsub.h"

#ifdef __cplusplus
extern "C" {
#endif

// 此文件为零散在各个模块被调用的直连写函数的打桩文件，无具体实现，主要为保证在--direct_write选项没被选中时代码能正常编译通过。
// 如果编译的时候加上--direct_write，则此文件会在编译时被忽略。

DirectSharedMsgPoolMgrT *g_gmdbCltDirectSharedMsgPoolMgr = NULL;
ClientDirectMsgRingCtxT g_gmdbCltMsgRingCtx = {0};

DW_EXPORT_METHOD_MACRO(MsgOpcodeRpcE, MSG_OP_RPC_SHARED_OBJ_END, GetDwOpTypeToRpcOpCode, (GmcOperationTypeE opType))
DW_EXPORT_METHOD_MACRO(Status, GMERR_OK, DwProcessExecute, (GmcStmtT * stmt))
DW_EXPORT_METHOD_MACRO(
    Status, GMERR_OK, DwOpenDirectWriteRunCtx, (GmcConnT * conn, uint32_t sessionId, DwRunCtxT **dwRunCtx))
DW_EXPORT_METHOD_MACRO(void, PLACEHOLDER, DwCloseDwRunCtx4Conn, (DbMemCtxT * memCtx, DwRunCtxT *dwRunCtx))
DW_EXPORT_METHOD_MACRO(void, PLACEHOLDER, ReleaseDwRunCtx4StmtInner, (DwRunCtxT * dwRunCtx, DbMemCtxT *memCtx))
DW_EXPORT_METHOD_MACRO(bool, false, DirectWriteConstraintVerify, (GmcStmtT * stmt, DmVertexLabelT *vertexLabel))
DW_EXPORT_METHOD_MACRO(bool, false, IsDirectWriteOps, (GmcOperationTypeE opType))
DW_EXPORT_METHOD_MACRO(bool, false, IsSupportDirectWrite, (GmcStmtT * stmt))
DW_EXPORT_METHOD_MACRO(bool, false, IsStmtSupportDirectWrite, (GmcStmtT * stmt))
DW_EXPORT_METHOD_MACRO(Status, GMERR_OK, TryAcqDwEntryToken, (GmcStmtT * stmt))
DW_EXPORT_METHOD_MACRO(void, PLACEHOLDER, ClearDwEntryToken, (GmcStmtT * stmt))

// Direct msssage pool
DW_EXPORT_METHOD_MACRO(Status, GMERR_OK, DirectShareMsgPoolCltOpen, (uint32_t instanceId, ShmemPtrT mgrPtr))
DW_EXPORT_METHOD_MACRO(Status, GMERR_OK, DirectSharedMsgPoolWriteMsg, (ShmemPtrT ptr))
DW_EXPORT_METHOD_MACRO(
    Status, GMERR_OK, DirectSharedMsgPoolRegisterEntry, (void *ctx, const MsgServiceEntry entry, void *userCtx))
DW_EXPORT_METHOD_MACRO(void, PLACEHOLDER, DirectShareMsgPoolCltInitNd, (void))
DW_EXPORT_METHOD_MACRO(void, PLACEHOLDER, DirectSharedMsgPoolCltUninitNd, (void))
DW_EXPORT_METHOD_MACRO(void, PLACEHOLDER, DirectSharedMsgUnInitClientRingCtx, (void))
DW_EXPORT_METHOD_MACRO(void, PLACEHOLDER, DirectSharedMsgPoolFree, (ShmemPtrT ptr))
DW_EXPORT_METHOD_MACRO(void, PLACEHOLDER, DirectSharedMsgPoolHandle, (void))
DW_EXPORT_METHOD_MACRO(ShmemPtrT, DB_INVALID_SHMPTR, DirectSharedPoolClientAlloc, (uint32_t size))
DW_EXPORT_METHOD_MACRO(DirectSharedMsgPoolMgrT *, NULL, DirectSharedMsgPoolMgrCreate, (DbMemCtxT * memCtx))

// Direct massage ring
DW_EXPORT_METHOD_MACRO(Status, GMERR_OK, DirectMsgRingCreate, (DbMemCtxT * memCtx, DirectMsgRingT **directMsgRing))
DW_EXPORT_METHOD_MACRO(Status, GMERR_OK, DirectMsgRingAdvanceMsg, (DirectMsgRingT * directMsgRing, ShmemPtrT data))
DW_EXPORT_METHOD_MACRO(Status, GMERR_OK, DirectMsgRingConsumeMsg, (DirectMsgRingT * directMsgRing, ShmemPtrT *data))
DW_EXPORT_METHOD_MACRO(Status, GMERR_OK, DirectMsgRingRead, (DirectMsgRingT * directMsgRing, ShmemPtrT *data))
DW_EXPORT_METHOD_MACRO(ShmemPtrT, DB_INVALID_SHMPTR, DwStmgMapCreate, (DbMemCtxT * memCtx))
DW_EXPORT_METHOD_MACRO(void, PLACEHOLDER, DwStmgMapDestroy, (DbShmOamapT * stmgMap))
DW_EXPORT_METHOD_MACRO(
    Status, GMERR_OK, DwStmgMapWriteMsg, (DbShmOamapT * stmgMap, uint8_t type, uint32_t vertexLabelId))
DW_EXPORT_METHOD_MACRO(Status, GMERR_OK, DwStmgMapConsumeMsg, (DbShmOamapT * stmgMap, uint8_t *data))

// Resource pool
DW_EXPORT_METHOD_MACRO(void, PLACEHOLDER, DwResetResInfo, (GmcStmtT * stmt))
DW_EXPORT_METHOD_MACRO(void, PLACEHOLDER, DwReleaseResInfo, (DwRunCtxT * dwRunCtx, DbMemCtxT *memCtx))

// log
DW_EXPORT_METHOD_MACRO(
    void, PLACEHOLDER, DwTimeCostLog, (GmcStmtT * stmt, uint64_t beginTime, uint64_t cpuBeginTime, DbThreadHandle tid))

#ifdef __cplusplus
}
#endif
