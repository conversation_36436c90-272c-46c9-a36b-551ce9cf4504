/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Access Method for direct write audit log
 * Author: maojinyong
 * Create: 2023-08-05
 */

#include "clt_da_write.h"
#include "clt_da_handle.h"
#include "clt_resource.h"
#include "clt_stmt.h"
#include "clt_graph_filter.h"
#include "clt_stmt_extend.h"

typedef Status (*BuildAuditLogFunc)(GmcStmtT *stmt, DbAuditEvtDescT *evtDesc);

typedef struct {
    const char *name;
    BuildAuditLogFunc resourceFunc;
    BuildAuditLogFunc logFunc;
} BuildAuditLogDescT;

static Status DwConvertVertexToStr(DbMemCtxT *memCtx, DmVertexT *vertex, char **buf)
{
    uint32_t length = 0;
    Status ret = DmVertexGetPrintLength(vertex, NULL, &length);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get vertex len when build audit log.");
        return ret;
    }
    *buf = (char *)DbDynMemCtxAlloc(memCtx, length);
    if (*buf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Build audit log, size=%" PRIu32 ".", length);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(*buf, length, 0x00, length);
    ret = DmVertexPrint(vertex, length, *buf, NULL);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(memCtx, *buf);
        *buf = NULL;
        DB_LOG_ERROR(ret, "Vertex to str when build audit log.");
        return ret;
    }
    return ret;
}

static Status DwConvertIndexKeyToStr(
    DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, const DmIndexKeyT *indexKey, char **buf)
{
    uint32_t length = 0;
    DmVlIndexLabelT *indexLabel = NULL;
    Status ret = DmGetIndexLabelByIndex(vertexLabel, indexKey->indexId, &indexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmIndexKeyBufGetPrintLength(indexKey->keyBuf, indexLabel, &length);
    if (ret != GMERR_OK) {
        return ret;
    }
    *buf = (char *)DbDynMemCtxAlloc(memCtx, length);
    if (*buf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Building audit log, size=%" PRIu32 ".", length);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(*buf, length, 0x00, length);
    ret = DmIndexKeyBufPrint(indexKey->keyBuf, indexLabel, length, *buf);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(memCtx, *buf);
        *buf = NULL;
        DB_LOG_ERROR(ret, "Index key to str when build audit log.");
        return ret;
    }
    return ret;
}

static Status DwBuildVertexAuditResource(GmcStmtT *stmt, DbAuditEvtDescT *resource)
{
    CltOperVertexT *op = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = op->cltCataLabel->vertexLabel;
    uint32_t namespaceId = vertexLabel->metaCommon.namespaceId;
    DmMetaCommonT *metaCommon = &vertexLabel->metaCommon;
    char *metaName = MEMBER_PTR(metaCommon, metaName);
    return DbAppendAuditEvtDesc(
        resource, "namespaceId: %" PRIu32 " type: VERTEX_LABEL name: \"%s\" ", namespaceId, metaName);
}

// 获取插入操作的vertex数据，将其转成str格式写入到日志中
static Status DwBuildInsertVertexParams4Audit(GmcStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    Status ret = GMERR_OK;
    CltOperVertexT *op = CltGetOperationContext(stmt);
    char *str = NULL;
    ret = DwConvertVertexToStr(stmt->memCtx, op->vertex, &str);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbAppendAuditEvtDesc(evtDesc, "vertex%" PRIu32 ": \"%s\" ", 0, str);
    DbDynMemCtxFree(stmt->memCtx, str);
    return ret;
}

// 获取更新和删除操作的索引数据，对于直连写而言，只需要关注主键
static Status DwBuildUpdateOrDeleteAuditLog(GmcStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    Status ret = GMERR_OK;
    CltOperVertexT *op = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = op->cltCataLabel->vertexLabel;
    DmIndexKeyT *idxKey = NULL;
    StatusAndPtrT getIndexRet = CltGetSimpleIndexKey(stmt);
    idxKey = (DmIndexKeyT *)getIndexRet.ptr;
    char *str = NULL;
    ret = DwConvertIndexKeyToStr(stmt->memCtx, vertexLabel, idxKey, &str);
    if (ret != GMERR_OK) {
        return ret;
    }
    // leftIndex对应为主键
    // rightIndex、autoOperateFlag对于直连写而言，不存在。此处设置为无效值，格式上与服务端保持一致
    ret = DbAppendAuditEvtDesc(
        evtDesc, "leftIndex: \"%s\" rightIndex: \"%s\" autoOperateFlag: \"%" PRIu32 "\" ", str, "(null)", 0);
    DbDynMemCtxFree(stmt->memCtx, str);
    return ret;
}

static const BuildAuditLogDescT g_dwAuditLogDesc[] = {
    [GMC_OPERATION_INSERT] = {"INSERT VERTEX", DwBuildVertexAuditResource, DwBuildInsertVertexParams4Audit},
    [GMC_OPERATION_UPDATE] = {"UPDATE VERTEX", DwBuildVertexAuditResource, DwBuildUpdateOrDeleteAuditLog},
    [GMC_OPERATION_DELETE] = {"DELETE VERTEX", DwBuildVertexAuditResource, DwBuildUpdateOrDeleteAuditLog},
    [GMC_OPERATION_REPLACE] = {"REPLACE VERTEX", DwBuildVertexAuditResource, DwBuildInsertVertexParams4Audit},
    [GMC_OPERATION_CHECK_REPLACE] = {
        "CHECK_REPLACE VERTEX", DwBuildVertexAuditResource, DwBuildInsertVertexParams4Audit}};

Status MakeDwAuditLogHeader(
    GmcStmtT *stmt, DbAuditEvtDescT *evtDesc, DbAuditEvtDescT *resource, const BuildAuditLogDescT *buildAuditLogDesc)
{
    const char *phase = "execute-by-directwrite";
    uint32_t sessionId = stmt->conn->sid;  // sessionId在建连时获取
    Status ret =
        DbAppendAuditEvtDesc(evtDesc, "sessionId: \"%" PRIu32 "\" stmtId: \"%" PRIu16 "\" type: \"%s\" action: \"%s\" ",
            sessionId, stmt->stmtId, buildAuditLogDesc->name, phase);
    if (ret != GMERR_OK) {
        return ret;
    }
    BuildAuditLogFunc resFunc = buildAuditLogDesc->resourceFunc;
    return resFunc(stmt, resource);
}

Status MakeDwAuditLogBody(
    GmcStmtT *stmt, DbAuditEvtTypeE auditType, DbAuditEvtDescT *evtDesc, const BuildAuditLogDescT *buildAuditLogDesc)
{
    BuildAuditLogFunc logFunc = buildAuditLogDesc->logFunc;
    return logFunc(stmt, evtDesc);
}

void DwAuditLog(GmcStmtT *stmt, Status opStatus)
{
    Status ret = GMERR_OK;
    if (!CltCfgIsOpenInt32(stmt->dwRunCtx->cfgShmConfig[CLT_CFG_AUDIT_LOG_DML_ENABLE].value.int32Val)) {
        return;
    }
    DbAuditEvtTypeE auditType = DB_AUDIT_DML;
    char *logMsg = DbDynMemCtxAlloc(stmt->memCtx, LOG_MAX_SIZE_OF_AUDIT_MSG);
    if (logMsg == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc logMsg, size=%" PRIu32 ".", LOG_MAX_SIZE_OF_AUDIT_MSG);
        return;
    }
    (void)memset_s(logMsg, LOG_MAX_SIZE_OF_AUDIT_MSG, 0, LOG_MAX_SIZE_OF_AUDIT_MSG);

    char *resourceMsg = DbDynMemCtxAlloc(stmt->memCtx, LOG_RESOURCE_SIZE_OF_AUDIT_MSG);
    if (resourceMsg == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc resourceMsg, size=%" PRIu32 ".", LOG_RESOURCE_SIZE_OF_AUDIT_MSG);
        DbDynMemCtxFree(stmt->memCtx, logMsg);
        return;
    }
    (void)memset_s(resourceMsg, LOG_RESOURCE_SIZE_OF_AUDIT_MSG, 0, LOG_RESOURCE_SIZE_OF_AUDIT_MSG);

    DbAuditEvtDescT evtDesc = {.msg = logMsg, .offset = 0, .msgLen = (uint16_t)LOG_MAX_SIZE_OF_AUDIT_MSG};
    DbAuditEvtDescT resource = {.msg = resourceMsg, .offset = 0, .msgLen = (uint16_t)LOG_RESOURCE_SIZE_OF_AUDIT_MSG};
    const BuildAuditLogDescT *buildAuditLogDesc = &g_dwAuditLogDesc[stmt->operationType];
    const char *action = buildAuditLogDesc->name;

    ret = MakeDwAuditLogHeader(stmt, &evtDesc, &resource, buildAuditLogDesc);
    if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
        DB_LOG_ERROR(ret, "Build audit log header, current action is %s.", action);
        goto FAIL;
    }
    ret = MakeDwAuditLogBody(stmt, auditType, &evtDesc, buildAuditLogDesc);
    if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
        DB_LOG_ERROR(ret, "Build audit log body, current action is %s.", action);
        goto FAIL;
    }
    // write audit log
    DB_LOG_AUDIT(stmt->conn->dwUserInfo->auditUserInfo, resource.msg, auditType, opStatus, "%s", evtDesc.msg);
FAIL:
    DbDynMemCtxFree(stmt->memCtx, logMsg);
    DbDynMemCtxFree(stmt->memCtx, resourceMsg);
}
