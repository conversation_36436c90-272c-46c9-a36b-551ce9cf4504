/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 * Description: The direct write entries and funcs
 * Author:
 * Create:
 */

#ifndef CLT_DA_WRITE_H
#define CLT_DA_WRITE_H

#include "clt_da_handle.h"
#include "clt_stmt.h"
#include "adpt_process_id.h"
#include "db_crash_debug.h"

#ifdef __cplusplus
extern "C" {
#endif

#define REPLACE_INSERT_AFFECT_ROW 1
#define REPLACE_UPDATE_AFFECT_ROW 2

#define DW_OLD_DATA_STATUS_AGE 0x0001
#define DW_OLD_DATA_STATUS_TRUNCATE 0x0002

#define DW_STM_MAX_CACHED_TUP_BUF_LEN 500u

#define DW_TOKEN_SLEEP_TIME_US 5
#define DW_TOKEN_TRY_TIMES_LIMIT 400000     // 2s，和信号处理框架超时时间保持一致。
#define DW_LONG_OPER_TIME_THRESH_US 500000  // 500ms

typedef struct DwPrivCheckInfo {
    DmPrivilegeE objectPriv;   // 对象权限
    CataSysPrivTypeE sysPriv;  // 系统权限
} DwPrivCheckInfoT;

// 为适配信号处理框架而封装的结构体，在获取SeRunCtx的时候使用
typedef struct {
    uint16_t instanceId;
    DbMemCtxT *memCtx;
    SeOpenCfgT *cltSeOpenCfg;
    SeRunCtxHdT *seRunCtxHandle;
} SeOpenShmProcessArgs;

typedef Status (*DwExecFunc)(GmcStmtT *stmt);
// 直连写handler
typedef struct DwExecHandler {
    DwExecFunc execFunc;        // 直连写的执行函数
    DwPrivCheckInfoT privInfo;  // 执行该操作所需要校验的权限信息
} DwExecHandlerT;

// 仿照EE的QryTupleInfo结构体，包装待更新记录作为入参。
typedef struct DwTupleInfo {
    DmVertexT *vertex;
    HeapTupleBufT heapTupleBuf;
} DwTupleInfoT;

// 直连写总入口函数
Status DwProcessExecute(GmcStmtT *stmt);

// 直连写各个操作入口函数
Status DwExecReplaceEntry(GmcStmtT *stmt);
Status DwExecInsertEntry(GmcStmtT *stmt);
Status DwExecUpdateEntry(GmcStmtT *stmt);
Status DwExecDeleteEntry(GmcStmtT *stmt);
Status DwExecCheckReplaceEntry(GmcStmtT *stmt);

// 记录审计日志
void DwAuditLog(GmcStmtT *stmt, Status opStatus);
// 耗时日志
void DwTimeCostLog(GmcStmtT *stmt, uint64_t beginTime, uint64_t cpuBeginTime, DbThreadHandle tid);

// 客户端建立连接时调用此接口，申请并初始化直连写上下文结构体
Status DwOpenDirectWriteRunCtx(GmcConnT *conn, uint32_t sessionId, DwRunCtxT **dwRunCtx);

// 客户端断连时调用此接口，释放直连写上下文结构体本身的内存（即对应 DwOpenDirectWriteRunCtx() 中申请的内存）
// 注意与 ReleaseDwRunCtx4StmtInner() 的区别，后者用于释放挂在直连写上下文中的各种资源的内存
void DwCloseDwRunCtx4Conn(DbMemCtxT *memCtx, DwRunCtxT *dwRunCtx);

// 释放挂在直连写上下文中的各种资源的内存，包括heapCtx/chCtx，索引，labelStatistics等
void ReleaseDwRunCtx4StmtInner(DwRunCtxT *dwRunCtx, DbMemCtxT *memCtx);

// 事务相关
Status DwBeginTrx(DwRunCtxT *dwRunCtx, uint16_t remoteId, ConcurrencyControlE ccType);
Status DwEndTrx(GmcStmtT *stmt, DwRunCtxT *dwRunCtx, Status opStatus, ConcurrencyControlE ccType);
Status DwTrxCommitCallBack(void *parameter);
#ifndef NDEBUG
void DwSetTrxCommitCallBack(SeRunCtxHdT seRunCtx, DwRunCtxT *dwRunCtx);
#endif
// 逻辑计数相关
Status DwRecordOldTupleState(DwRunCtxT *dwRunCtx, TupleT oldTuple);

// 统计计数相关
void DwStatisticsChangeForCheckAccount(
    DwRunCtxT *dwRunCtx, CltCataLabelT *cataLabel, const HeapTupleBufT *heapTupleBuf, uint8_t partitionId);
void DwStatisticsChangeForCheckAccountBySubType(
    DwRunCtxT *dwRunCtx, CltCataLabelT *cataLabel, const HeapTupleBufT *heapTupleBuf, uint8_t partitionId);
void DwResetLabelStatistics(DwLabelStatisticT *labelStatistics);

// 负责申请DwRunCtxT下各类资源的总入口，包括statistics, heap/ch, index
Status DwOpenHeapAndIdxes(GmcStmtT *stmt, DwRunCtxT *dwRunCtx, DbMemCtxT *memCtx);

// 该接口会重置索引和heap资源，目前在直连写事务操作结束后调用（DwEndTrx）
void DwResetDwRunCtx4Stmt(DwRunCtxT *dwRunCtx, DbMemCtxT *memCtx);

// 老化相关
Status DwGetPartitionId(
    DmVertexLabelT *vertexLabel, CltCataLabelT *cataLabel, const HeapTupleBufT *heapTupleBuf, uint8_t *partitionId);
Status DwGetCheckInfo(DmVertexLabelT *vertexLabel, CltCataLabelT *cataLabel, TupleT *tuple, DmCheckInfoT **checkInfo);
void DwPushAgeRecordBeforeUpdate(
    DwRunCtxT *dwRunCtx, DmVertexLabelT *vertexLabel, HeapTupleBufT *oldTupleBuf, DmSubsEventE eventType);
Status DwSetAgeDataStatus(
    DwRunCtxT *dwRunCtx, CltCataLabelT *cataLabel, HeapTupleBufT *heapTupleBuf, bool *isTruncated);
Status DwTupleCheckVersionUpdate(DwRunCtxT *dwRunCtx, HeapTupleBufT *heapTupleBuf, bool *isAged);

// cluster hash 相关
Status DwCheckBeforeUpdate(void *data, const HeapTupleBufT *oldTuple, const HeapTupleBufT *newTuple);
Status ChCheckNeedUpdateViewNum(const HeapTupleBufT *heapTupleBuf, void *data);

// 状态合并订阅相关
// StMg mark delete和老化标记本质都是标记删除，故统一逻辑处理
bool DwIsHeapTupleMarkDelete(const HeapTupleBufT *heapTupleBuf, CltCataLabelT *cataLabel);
Status DwCheckRecordPush(DwRunCtxT *dwRunCtx, HeapTupleBufT *heapTupleBuf, CltCataLabelT *cataLabel);

// 特殊字段，设置属性值
Status DwSetAutoGeneratedProperty(DwRunCtxT *dwRunCtx);

HpOpTypeE GetDwOpTypeToHeapOpType(GmcOperationTypeE opType);
MsgOpcodeRpcE GetDwOpTypeToRpcOpCode(GmcOperationTypeE opType);

/*
 * 直连写约束判断：
 * 使用直连写需经过四个方面的校验：1、stmt; 2、operationType; 3、vertexLabel; 4、动态信息判断
 * 1、stmt ==> IsStmtSupportDirectWrite()
 * （1）连接必须是同步连接
 * （2）必须是非显式事务
 *  (3) 配置文件中开启直连写功能
 * （4）只支持单实例
 *  (5) 不支持批写
 * 2、operationType ==> IsDirectWriteOps()
 *    满足直连写的操作，insert、replace、update、delete
 * 3、vertexLabel元数据校验 ==> DirectWriteConstraintVerify()
 *    对于从元数据中就可以获取的静态信息，这部分约束可以提前到prepare的时候就进行判断
 * 4、动态信息判断 ==> IsSupportDirectWrite()
 *    对于更新或删除操作时，当前必须设置主键（即，不支持基于二级索引、条件过滤的更新或删除）
 */
bool IsStmtSupportDirectWrite(GmcStmtT *stmt);
bool IsDirectWriteOps(GmcOperationTypeE opType);
bool DirectWriteConstraintVerify(GmcStmtT *stmt, DmVertexLabelT *vertexLabel);
bool IsSupportDirectWrite(GmcStmtT *stmt);

// 每个表仅允许一个链接走进直连写临界区，控制并发。
// 尝试获取vertexLabel中的dwEntryToken，如果获取到则允许进入直连写临界区，否则等待。
Status TryAcqDwEntryToken(GmcStmtT *stmt);
// 释放vertexLabel中的dwEntryToken。
void ClearDwEntryToken(GmcStmtT *stmt);

// 与 QryAgeTaskKeyT 定义相同
typedef struct DwAgeTaskKey {
    uint32_t labelId;         // label id
    uint8_t partitionId;      // partition id
    uint8_t oldCheckVersion;  // oldCheckVersion
    uint8_t reserved[2];
} DwAgeTaskKeyT;

inline static void SetDwAgeTaskKey(
    DwAgeTaskKeyT *ageTaskKey, uint32_t labelId, uint8_t partitionId, uint8_t oldCheckVersion)
{
    ageTaskKey->labelId = labelId;
    ageTaskKey->partitionId = partitionId;
    ageTaskKey->oldCheckVersion = oldCheckVersion;
}

// common
// 当stmt过多，若size过大，会导致cachedTupBuf过大，可申请内存急剧下降，
// 且cachedTupBuf主要优化性能场景(fib表70-80 bytes)
// 现设定merge执行结束，size大于DW_STM_MAX_CACHED_TUP_BUF_LEN时，主动释放cachedTupBuf
static inline void DwFreeCachedTupBufIfLarge(GmcStmtT *stmt)
{
    DwRunCtxT *dwRunCtx = stmt->dwRunCtx;
    if (dwRunCtx->cachedTupBufSize > DW_STM_MAX_CACHED_TUP_BUF_LEN) {
        if (dwRunCtx->cachedTupBuf != NULL) {
            DbDynMemCtxFree(stmt->memCtx, dwRunCtx->cachedTupBuf);
            dwRunCtx->cachedTupBufSize = 0;
            dwRunCtx->cachedTupBuf = NULL;
        }
    }
}

// 获取VertexLabel中的对账信息
static inline DmCheckInfoT *DwGetCheckInfoById(CltCataLabelT *cataLabel, uint8_t partitionId)
{
    DmAccCheckT *accCheckAddr = cataLabel->vertexAccCheck;
    if (partitionId != DB_INVALID_UINT8) {
        return &accCheckAddr->checkInfo[partitionId];
    }
    return &accCheckAddr->checkInfo[0];
}

static inline CltCataLabelT *DwGetCltCataLabel(DwRunCtxT *dwRunCtx)
{
    DB_POINTER(dwRunCtx);
    return dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
}

static inline DmVertexLabelT *DwGetVertexLabel(DwRunCtxT *dwRunCtx)
{
    DB_POINTER(dwRunCtx);
    return dwRunCtx->dwArgs.vertexArgs.cltCataLabel->vertexLabel;
}

#ifdef __cplusplus
}
#endif

#endif /* CLT_DA_WRITE_H */
