/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2021. All rights reserved.
 * Description: header file for direct access
 * Author: fanqiushi
 * Create: 2020-8-10
 */
#ifndef CLT_DA_HANDLE_H
#define CLT_DA_HANDLE_H

#include "db_mem_context.h"
#include "dm_data_index.h"
#include "dm_data_prop.h"
#include "se_index.h"
#include "se_heap.h"
#include "db_privileges.h"
#include "db_msg_buffer.h"
#include "db_rpc_msg_op.h"
#include "se_clustered_hash.h"
#include "clt_da_write_pubsub.h"
#include "clt_da_write_pubsub_status_merge.h"
#include "clt_catabase.h"
#include "db_shm_hashmap.h"
#include "se_resource_column.h"
#include "container_access.h"
#include "clt_utils.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    uint32_t nodePath[DB_SCHEMA_MAX_DEPTH];
    uint32_t depth;
    uint32_t fieldId;
    int32_t compOp;
    DmVertexT *vertex;
    DmPropertySchemaT *property;
    DmValueT value;
    bool setValue;
} FilterInnerT;

#ifdef FEATURE_GQL
typedef struct {
    DbListT *list;  // 结构化过滤条件(FilterInnerT)列表。考虑到内存小型化，这里仅存放指针，实际使用时再申请内存
    // 结构化过滤条件列表个数，非GQL场景下不超过 MAX_FILTER_STRUCT_NUM，GQL场景下不超过表字段上限
    bool isUsed;  // 标记结构化过滤是否被使用，当被非结构化过滤覆盖时置为false，当使用结构化时置为true
} StructFilterListT;

static inline void StructFilterListReset(StructFilterListT *list)
{
    DbClearList(list->list);
    list->isUsed = false;
}

static inline uint32_t StructFilterListFilterNum(StructFilterListT *list)
{
    uint32_t filterNum = 0;
    if (list != NULL && list->isUsed) {
        for (uint32_t i = 0; i < DbListGetItemCnt(list->list); i++) {
            FilterInnerT *filterInner = (FilterInnerT *)DbListItem(list->list, i);
            filterNum += filterInner->setValue;
        }
    }
    return filterNum;
}

#else
typedef struct {
    FilterInnerT **list;  // 结构化过滤条件列表。考虑到内存小型化，这里仅存放指针，实际使用时再申请内存
    uint32_t count;  // 结构化过滤条件列表个数，不超过 MAX_FILTER_STRUCT_NUM
} StructFilterListT;

static inline void StructFilterListReset(StructFilterListT *list)
{
    list->list = NULL;
}

static inline uint32_t StructFilterListFilterNum(StructFilterListT *list)
{
    uint32_t filterNum = 0;
    if (list != NULL) {
        for (uint32_t i = 0; i < list->count; i++) {
            filterNum += list->list[i]->setValue;
        }
    }
    return filterNum;
}

#endif  // FEATURE_GQL

typedef struct {
    uint8_t *userBuf;
    uint32_t userbufLen;
    uint32_t vertexOffset;
    bool isUserBuffer;
    bool isBufSizelimited;
} StructureCtxT;

typedef enum {
    DS_SCAN_VERTEX_SEQUENCE = 1,     // vertex table full scan
    DS_LOOKUP_VERTEX_INDEX_PRIMARY,  // vertex primary key scan
    DS_SCAN_VERTEX_INDEX_SECONDARY,  // vertex non unique index scan
#ifdef ART_CONTAINER
    DS_SCAN_VERTEX_INDEX_PRIMARY,
#endif
    DS_SCAN_KV_SEQUENCE,  // kv table full scan
    DS_SCAN_KV_INDEX,     // kv table primary key scan
} DsScanModeE;

typedef struct {
    DmVertexT *vertex;  // vertex结构体，这里是入参不用返回值使用
    CltCataLabelT *cltCataLabel;
    DmIndexKeyT *filter;      // 主扫描键，主键读取使用，rangescan的时候是左key
    DmIndexKeyT *rightKey;    // range扫描右key，只在rangescan的时候有值
    uint32_t rangeScanFlags;  // rangescan标志，以bit位存储需要按位解析
    uint8_t condIdx;          /**前缀匹配的索引项 */
    uint8_t matchBytesNum;    /**前缀匹配多少个字节 */
    uint8_t nearAIdx;         /**临近查询的A字段*/
    uint8_t nearBIdx;         /**临近查询的B字段，查询离A最近且B字段相同的数据*/
    StructFilterListT *structFilterList;
#ifdef FEATURE_HP_ANNSEQSCAN
    VectorSearchCtxT *vecSearchCtx;
#endif
} VertexScanArgsT;

typedef struct {
    DmKvLabelT *kvLabel;
    IndexKeyT *kvIndexKey;
} KvScanArgsT;

typedef struct {
    ProtoHeadT protoHead;
    DsScanModeE scanMode;
    uint32_t preFetchRows;        // 每轮扫描允许获取的最大记录数
    uint64_t totalFetchedRowCnt;  // 实际已扫描到的记录总数
    uint64_t limitCount;          // 直连扫描预期读取的条数（静态值，不随扫描过程而变化）
    bool isSetPreFetch;
    bool isDirectGetCount;
    union {
        VertexScanArgsT vertexScanArgs;
        KvScanArgsT kvScanArgs;
    };
} DsFetchArgsT;

typedef struct {
    bool indexNotOpen;  // 标志索引未是否被创建
} DsFetchHandleT;

typedef enum {
    CLI_INVALID_PRIV,
    CLI_SYS_PRIV,  // 系统权限
    CLI_OBJ_PRIV,  // 对象权限
    CLI_NSP_PRIV,  // nsp权限
} CliPrivTypeE;

typedef struct DirectAccessRunCtxBase {
    // read only
    Handle seRunCtxHandle;    // 直连读写main store需要的上下文
    DbMemCtxT *clientMemCtx;  // 客户端传入的申请动态内存的memtex
    CataRoleT *role;          // 直连客户端的角色信息
    uint32_t privPolicyMode;  // 服务侧的权限模式（authentication、不authentication、宽容模式）
    CliPrivTypeE passPrivType;

    // read write
    Handle pkIndexRunCtx;  // 主键索引的运行上下文，客户端直连模式下使用，由客户端的prepare stmt初始化
    IndexCtxT *idxCtxMem;  // idxCtx的内存，生命周期同DirectReadRunCtx Or DirectWriteRunCtx，避免每次都申请内存
    Handle heapRunCtx;  // heap的运行上下文，客户端直连模式下使用
    ContainerHdlT heapCtxMem;  // heapCtx的内存，生命周期同DirectReadRunCtx Or DirectWriteRunCtx，避免每次都申请内存
    uint64_t trxId;  // 事务Id,用于check replace
    ContainerTypeE containerType;
    bool isUseClusteredHashTable;
    bool pkPrepareFlag;

    // hash cluster table staff
    ChLabelRunHdlT chRunCtx;  // hash cluster table运行上下文
    ContainerHdlT chCtxMem;  // hashCluster table 的内存，生命周期同DirectReadRunCtx，避免每次都申请内存
#ifdef ART_CONTAINER
    ArtContainerRunHdlT artRunCtx;  // art cluster table运行上下文
    ContainerHdlT artCtxMem;  // art cluster table 的内存，生命周期同DirectReadRunCtx，避免每次都申请内存
    bool isRealCluster;
    DbMemCtxT *artMemCtx;
#endif
    Handle chCursor;  // cluster hash容器的cursor

    Handle labelLatch;
    ShmemPtrT labelLatchShmAddr;
    DbSessionCtxT *sessionCtx;  // 与labelLatch绑定，为保证客户端异常退出时，不为因labelLatch未释放而锁死
    uint32_t sysPrivVersion;
    uint32_t labelLatchVersionId;  // 与labelLatch绑定，为表锁的版本号，用来检查表是否变更(被Drop)
    bool lastPrivPass;             // vertexLabel表权限检查是否通过
    bool isKvHasPriv;              // kv表权限检查是否通过
    bool hasInited;

    // namespace check
    uint32_t nspPrivVersion;  // 缓存在客户端的namespace权限版本号
    DmObjPrivT *objPrivNsp;   // 通过共享内存可获取到的最新的namespace权限版本号
} DirectAccessRunCtxT;

typedef struct {
    DmVertexT *vertex;
    CltCataLabelT *cltCataLabel;
    uint8_t partitionId;  // 缓存tuple所属的分区号
    bool hasSubs;         // vertexLabel 上是否存在订阅
    DmIndexKeyT *pkIndex;
    HeapTupleBufT vertexBuf;  // 直连写 insert、replace 中的新 tuple，或 update 中 merge 后的新 tuple，delete
                              // 不用此字段
} VertexArgsT;

typedef struct {
    DmKvLabelT *kvLabel;
    IndexKeyT *kvIndexKey;
} KvArgsT;

typedef struct DirectWriteArgs {
    GmcOperationTypeE currOpType;
    GmcOperationTypeE lastOpType;
    union {
        VertexArgsT vertexArgs;
        KvArgsT kvArgs;
    };
} DwArgsT;

// 结构体构成必须与QryLabelStatisticValueT一致。
typedef struct DwLabelStatisticValue {
    uint64_t changeVertexCnt;   // 对账过程中，发生变更的记录数(包括更新的和删除的)
    uint64_t realAgedCnt;       // 老化过程中, 老化掉的记录数
    uint64_t realTruncatedCnt;  // 后台删除过程中, 删除掉的记录数
    uint64_t realRecoveryCnt;   // 老化过程中, 回滚的记录数
    int64_t logicCnt;           // 逻辑计数（排除待老化记录和新订阅标记删除记录）
    int64_t recordCnt;          // 记录数（排除新订阅标记删除记录）
} DwLabelStatisticValueT;

// 客户端逻辑计数。需要与QryTupleStateE保持一致。
typedef enum DwTupleState {
    DW_TUPLE_NULL,    // 记录不存在或者被物理删除
    DW_TUPLE_NORMAL,  // 正常的记录
    DW_TUPLE_AGING,   // 待老化的记录
    DW_TUPLE_STMGGC,  // 新订阅被标记删除的记录，待GC
} DwTupleStateE;

typedef struct DwLabelStatistic {
    CltCataLabelT *cltCataLabel;
    DwLabelStatisticValueT statisticsValue[DM_MAX_PARTITION_ID];  // 非分区情况下, 使用数组第一个元素作为统计变量
} DwLabelStatisticT;

typedef struct DwAgedMgrInfo {
    DbLatchT *latch;          // 老化任务的 latch
    ShmemPtrT latchShmAddr;   // 老化任务的 latch 的共享内存 addr
    DbShmOamapT *shmTaskMap;  // 老化任务的 shmTaskMap
} DwAgedMgrInfoT;

typedef struct DwResInfo {
    uint32_t resCountEachRow;
    ResId *resIds;  // 数组长度为resCountEachRow
} DwResInfoT;

typedef struct DirectWriteRunCtx {
    DirectAccessRunCtxT base;
    IndexCtxT **secIdxCtx;  // 表的二级索引
    uint32_t secIdxNum;
    uint32_t fetchCnt;        // 根据二级索引扫描到的条数
    IndexCtxT *curSecIdxCtx;  // 当前使用的二级索引上下文（update 和 delete）
    DmIndexKeyT *idxKey;      // 主键索引或者唯一二级索引（update 和 delete）
    IndexScanItrT htCursor;   // 二级索引的查询上下文
    HpTupleAddr tupleAddr;
    DwLabelDefT labelDef;
    DwArgsT dwArgs;         // 直连写参数，供运行时使用
    TupleBufT oldTupleBuf;  // 用于直连写 update、delete 以及 insert to update、replace to update 中存放
                            // oldTuple
    DwTupleStateE oldTupleState;
    DwSubsRowDataT subsRowData;  // 供老订阅使用
    DwSubsRowDataT ageRowData;   // 供老化数据使用
    DwStMgSubDataT stmgSubData;  // 供状态合并表使用（即新订阅）
    DwAgedMgrInfoT agedMgrInfo;
    DbCfgItemT *cfgShmConfig;
    DwLabelStatisticT *labelStatistics;     // 对账统计信息
    uint64_t lastStatTime;                  // 上一次性能统计的时间点
    uint8_t *cachedTupBuf;                  // 【直连写】merge操作使用的公共tuple buff
    uint8_t *structWriteBuf;                // 结构化写使用的 buf
    DmSubsEventE subsEvent;                 // 订阅事件类型
    DmSubsEventE ageEvent;                  // 老化事件类型，包括delete和age
    DmVertexT *maxVersionVertex;            // 最高版本的vertex，用于判断元数据版本
    CltCataLabelT *maxVersionCltCataLabel;  // 最高版本的vertexLabel，用于判断元数据版本
    ResColPoolRunHdl labelResPoolRunCtx;    // 表级绑定的资源池句柄
    bool isLabelResPoolLocked;              // 表级绑定的资源池是否正常加锁
    // 以下字段均用于性能优化
    uint32_t cachedTupBufSize;
    uint32_t structWriteBufLen;
    uint8_t dataStatus;
    bool isMaxVersionChange;
    bool isIns2Rep;          // insert 操作转变为 replace 操作的标记位，仅在 insert 流程中为 true
    bool ready4StructWrite;  // 是否已经校验过满足直接使用用户序列化的 buf 所需的条件
    bool useStructSeriBuf;   // 是否直接使用用户序列化函数产生的 buf
} DwRunCtxT;

typedef struct DirectReadRunCtx {
    DirectAccessRunCtxT base;
    StructureCtxT structureCtx;  // 结构化读需要的上下文
    IndexScanItrT htCursor;      // 二级索引的查询上下文
    Handle heapCursor;  // heap scan的运行句柄，客户端直连读模式下使用，由客户端的prepare stmt初始化
#ifdef ART_CONTAINER
    Handle artCursor;  // art scan迭代器
#endif
    uint64_t timeOfCreateCursor;
    uint64_t threadIdOfCreateCursor;
    IndexCtxT *currSecIndexRunCtx;  // 二级索引的运行上下文
    FixBufferT *directReadBuff;     // 客户端传入的用于直连读的buffer
    TupleBufT tupleBuf;             // 存储heapBuf，避免反复内存申请释放
    uint8_t *keyBuf;
    DmIndexKeyBufInfoT *indexKeyInfo;
    DsFetchArgsT dsFetchArgs;         // 直连读参数
    DsFetchHandleT dsFetchHandle;     // 直连读运行句柄
    uint8_t heapCursorReportTimeOut;  // 超长cursor的上报时间阈值
    bool isV3Mode;
    bool cfgCompatibleV3;     // CLT_CFG_COMPATIBLE_V3，配置项中设置
    bool isUseHcGlobalLatch;  // 性能优化，hash Cluster索引，直连读外层加锁
    bool isHeapCursorRefInc;  // 在二级索引扫描场景，标记heapcursor引用计数是否增加。
} DrRunCtxT;

typedef struct WriteCacheRunCtx {
    Handle wCacheMgrPtr;    // WriteCacheMgrT *
    Handle writeCacheDesc;  // WriteCacheDescT *
    Handle mdMgr;           // MdMgrT *
    uint32_t labelId;
    uint32_t realPageSize;         // 缓存页实际大小，只有在保留内存场景下与oriPageSize不一致
    uint32_t oriPageSize;          // 缓存页原始大小，即存储页大小
    PageIdT pageId;                // 只先对保留内存的场景进行优化
    GmcOperationTypeE lastOpType;  // 缓存上一次操作的类型
    void *writePageHead;  // pageId对应的页，多次请求大概率会复用，使用前先校验pageId是否发生变化
} WcRunCtxT;

Status DirectAccessInitBaseRunCtxInfo(DirectAccessRunCtxT *directRunCtx, DbMemCtxT *memCtx);
Status DirectAccessPrivCheck(DirectAccessRunCtxT *runCtxBase, DmObjPrivT *objPriv, CataObjTypeE cataObjType,
    CataSysPrivTypeE sysPrivType, DmPrivilegeE objPrivType);
bool IsVertexLabelPrivChanged(const CltCataLabelT *cltLabel, DirectAccessRunCtxT *daRunCtxBase);

typedef struct DirectAccessStructOptRunCtxBase {
    // read only
    Handle seRunCtxHandle;    // 直连读写main store需要的上下文
    DbMemCtxT *clientMemCtx;  // 客户端传入的申请动态内存的memtex

    // read write
    Handle pkIndexRunCtx;  // 主键索引的运行上下文，客户端直连模式下使用
    IndexCtxT *idxCtxMem;  // idxCtx的内存，生命周期同DirectReadRunCtx Or DirectWriteRunCtx，避免每次都申请内存
    Handle heapRunCtx;  // heap的运行上下文，客户端直连模式下使用
    ContainerHdlT heapCtxMem;  // heapCtx的内存，生命周期同DirectReadRunCtx Or DirectWriteRunCtx，避免每次都申请内存
    uint64_t trxId;  // 事务Id,用于check replace
    bool pkPrepareFlag;

    Handle labelLatch;
    ShmemPtrT labelLatchShmAddr;
    DbSessionCtxT *sessionCtx;  // 与labelLatch绑定，为保证客户端异常退出时，不为因labelLatch未释放而锁死
    uint32_t labelLatchVersionId;  // 与labelLatch绑定，为表锁的版本号，用来检查表是否变更(被Drop)
    bool hasInited;
} DirectAccessStructOptRunCtxT;

// new run context only for direact read, don't change member order, before modify it, please ask limianhong/wangsi
typedef struct DirectReadStructOptRunCtx {
    DirectAccessStructOptRunCtxT base;
    StructureCtxT structureCtx;  // 结构化读需要的上下文
    bool isV3Mode;
    bool cfgCompatibleV3;  // CLT_CFG_COMPATIBLE_V3，配置项中设置
    uint8_t *keyBuf;
    DmIndexKeyBufInfoT *indexKeyInfo;
    DsFetchArgsT dsFetchArgs;      // 直连读参数
    DsFetchHandleT dsFetchHandle;  // 直连读运行句柄
} DrRunStructOptCtxT;

#ifdef __cplusplus
}
#endif
#endif /* CLT_DA_HANDLE_H */
