/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 * Description: Index Access Method for directwrite
 * Author:
 * Create:
 */
#ifndef CLT_DA_WRITE_INDEX_H
#define CLT_DA_WRITE_INDEX_H

#include "dm_data_basic.h"
#include "db_mem_context.h"
#include "dm_meta_index_label.h"
#include "dm_meta_prop_strudefs.h"
#include "clt_da_handle.h"
#include "se_common.h"
#include "se_index.h"

#ifdef __cplusplus
extern "C" {
#endif

/*
 * 索引 insert、delete、replace、update、lookup、idxCtx open & release
 */
Status PkIdxOpenAndInitPkIdxCtx(DwRunCtxT *dwRunCtx, DmVlIndexLabelT *indexLabel);
Status SecondaryIdxesOpenAndInitCtxs(DwRunCtxT *dwRunCtx, DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel);
void ReleaseAllIdxCtxs(DwRunCtxT *dwRunCtx, DbMemCtxT *memCtx);

Status DwIdxLookup(DwRunCtxT *dwRunCtx, IndexKeyT *pkIdxKey, uint32_t indexId, bool *isFound);

Status DwSecondaryIdxesInsert(DwRunCtxT *dwRunCtx, HeapTupleBufT *heapTupleBuf) DIRECT_WRITE_SECTION_COMMON;
Status DwAllIdxesInsert(DwRunCtxT *dwRunCtx, HeapTupleBufT *heapTupleBuf);

Status SecondaryIdxesDelete(DwRunCtxT *dwRunCtx);
Status AllIdxesDelete(DwRunCtxT *dwRunCtx);
Status HcIdxesDelete(DwRunCtxT *dwRunCtx);

Status DwSecondaryIdxesUpdate(DwRunCtxT *dwRunCtx);

Status ChSetIdxesParms(DwRunCtxT *dwRunCtx);
IdxKeyCmpFunc SetIndexCmpFunc(DmVertexLabelT *vertexLabel, GmcOperationTypeE opType);

typedef Status (*ExecSingleVertexFunc)(GmcStmtT *stmt, DwRunCtxT *dwRunCtx);
Status ExecUpdateOrDeleteByPkIdx(GmcStmtT *stmt, ExecSingleVertexFunc func);
Status ExecUpdateOrDeleteBySecIdx(GmcStmtT *stmt, ExecSingleVertexFunc func);

#ifdef __cplusplus
}
#endif

#endif /* CLT_DA_WRITE_INDEX_H */
