/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 * Description: Access Method for direct access trx
 * Author:
 * Create:
 */

#include "clt_da_write.h"
#include "clt_da_write_pubsub_status_merge.h"
#include "clt_da_write_respool.h"
#include "clt_da_handle.h"
#include "se_define.h"
#include "se_heap_base.h"
#include "se_clustered_hash_label_base.h"
#include "clt_exception.h"

ALWAYS_INLINE static Status DwAcqLatch(
    LabelRWLatchT *labelRWLatch, uint32_t labelLatchVersionId, DbSessionCtxT *sessionCtx)
{
    DB_POINTER2(labelRWLatch, sessionCtx);
    LATCH_GET_START_WAITTIMES(RLATCH, labelRWLatch->rwlatch);
#ifndef ART_CONTAINER
    Status ret = CltLabelLatchAtomicSetRuMode(labelRWLatch, &g_gmdbCltProtectThreadCtx.isAllowManipuShm);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Acquire label latch in ru mode.");
        return ret;
    }
#else
    Status ret = GMERR_OK;
#endif
    DbRWSpinRLockWithSession(
        sessionCtx, &labelRWLatch->rwlatch, &labelRWLatch->rwlatchShmptr, LATCH_ADDR_LABELLATCH_RWLATCH_SHMEM);
    ret = LabelLatchCheckVersion(labelRWLatch, labelLatchVersionId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        LabelRLatchReleaseWithSession(labelRWLatch, sessionCtx);
#ifndef ART_CONTAINER
        LabelLatchAtomicClearRuMode(labelRWLatch);
#endif
        DB_LOG_AND_SET_LASERR(ret,
            "Label been dropped when acquire read latch in dw. LabelRWLatchVersionId: %" PRIu32
            ", labelLatchVersionId: %" PRIu32 ".",
            labelRWLatch->versionId, labelLatchVersionId);
        return ret;
    }
    LATCH_GET_END_WAITTIMES(RLATCH, labelRWLatch->rwlatch, sessionCtx->session);
    return GMERR_OK;
}

ALWAYS_INLINE static void DwAcqHcLatch(LabelRWLatchT *labelRWLatch, DbSessionCtxT *sessionCtx)
{
    DB_POINTER2(labelRWLatch, sessionCtx);
    LATCH_GET_START_WAITTIMES(RLATCH, labelRWLatch->hcLatch);
    DbRWSpinWLockWithSession(
        sessionCtx, &labelRWLatch->hcLatch, &labelRWLatch->hcLatchShmPtr, LATCH_ADDR_LABEL_HCLATCH_RWLATCH_SHMEM);
    LATCH_GET_END_WAITTIMES(RLATCH, labelRWLatch->hcLatch, sessionCtx->session);
}

ALWAYS_INLINE static void DwReleaseLatchForVertexLabel(
    LabelRWLatchT *labelRWLatch, DbSessionCtxT *sessionCtx, ConcurrencyControlE ccType)
{
    DB_POINTER2(labelRWLatch, sessionCtx);
    DB_ASSERT(ccType == CONCURRENCY_CONTROL_READ_UNCOMMIT || DmIsLabelLatchMode(ccType));
    if (ccType == CONCURRENCY_CONTROL_READ_UNCOMMIT) {
        if (labelRWLatch->isAddHcLatch) {
            LabelWHcLatchReleaseWithSession(labelRWLatch, sessionCtx);
        }
        LabelRLatchReleaseWithSession(labelRWLatch, sessionCtx);
#ifndef ART_CONTAINER
        LabelLatchAtomicClearRuMode(labelRWLatch);
#endif
        return;
    }
    if (DmIsLabelLatchMode(ccType)) {
        LabelWLatchReleaseWithSession(labelRWLatch, sessionCtx);
        return;
    }
    DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Concurrency type %" PRId32 " not supported by dw", (int32_t)ccType);
}

static Status DwAcqLatchForRuMode(LabelRWLatchT *labelRWLatch, uint32_t labelLatchVersionId, DbSessionCtxT *sessionCtx)
{
    Status ret = DwAcqLatch(labelRWLatch, labelLatchVersionId, sessionCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (labelRWLatch->isAddHcLatch) {
        DwAcqHcLatch(labelRWLatch, sessionCtx);
    }
    return GMERR_OK;
}

static Status DwAcqLatchForLabelLatchMode(
    LabelRWLatchT *labelRWLatch, uint32_t labelLatchVersionId, DbSessionCtxT *sessionCtx)
{
    LATCH_GET_START_WAITTIMES(WLATCH, labelRWLatch->rwlatch);
    DbRWSpinWLockWithSession(
        sessionCtx, &labelRWLatch->rwlatch, &labelRWLatch->rwlatchShmptr, LATCH_ADDR_LABELLATCH_RWLATCH_SHMEM);
    Status ret = LabelLatchCheckVersion(labelRWLatch, labelLatchVersionId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        LabelWLatchReleaseWithSession(labelRWLatch, sessionCtx);
        DB_LOG_AND_SET_LASERR(ret,
            "Label been dropped when acquire write latch in dw. LabelRWLatchVersionId: %" PRIu32
            ", labelLatchVersionId: %" PRIu32 ".",
            labelRWLatch->versionId, labelLatchVersionId);
        return ret;
    }
    LATCH_GET_END_WAITTIMES(WLATCH, labelRWLatch->rwlatch, sessionCtx->session);
    return GMERR_OK;
}

ALWAYS_INLINE static Status DwAcqLatchForVertexLabel(
    LabelRWLatchT *labelRWLatch, uint32_t labelLatchVersionId, DbSessionCtxT *sessionCtx, ConcurrencyControlE ccType)
{
    DB_POINTER2(labelRWLatch, sessionCtx);
    DB_ASSERT(ccType == CONCURRENCY_CONTROL_READ_UNCOMMIT || DmIsLabelLatchMode(ccType));
    if (ccType == CONCURRENCY_CONTROL_READ_UNCOMMIT) {
        return DwAcqLatchForRuMode(labelRWLatch, labelLatchVersionId, sessionCtx);
    }
    if (DmIsLabelLatchMode(ccType)) {
        return DwAcqLatchForLabelLatchMode(labelRWLatch, labelLatchVersionId, sessionCtx);
    }

    DB_LOG_AND_SET_LASERR(
        GMERR_FEATURE_NOT_SUPPORTED, "Concurrency type %" PRId32 " not supported by dw", (int32_t)ccType);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

/*********** transaction methods ************/
ALWAYS_INLINE static Status DwBeginTrxInner(DwRunCtxT *dwRunCtx, uint16_t remoteId)
{
    // 只实现autoCommit && isLiteTrx (RU)
    SeRunCtxHdT seRunCtx = (SeRunCtxHdT)dwRunCtx->base.seRunCtxHandle;
    TrxCfgT cfg = {0};
    cfg.isolationLevel = READ_UNCOMMITTED;
    cfg.readOnly = false;
    cfg.isLiteTrx = true;
    cfg.isBackGround = false;
    cfg.connId = remoteId;
    return SeTransBegin(seRunCtx, &cfg);
}

Status DwBeginTrx(DwRunCtxT *dwRunCtx, uint16_t remoteId, ConcurrencyControlE ccType)
{
    DB_POINTER(dwRunCtx);
    Status ret = GMERR_OK;
    SeRunCtxHdT seRunCtx = (SeRunCtxHdT)dwRunCtx->base.seRunCtxHandle;

    if (SECUREC_LIKELY(SeTransGetState(seRunCtx) == TRX_STATE_NOT_STARTED)) {
        ret = DwAcqLatchForVertexLabel(
            dwRunCtx->base.labelLatch, dwRunCtx->base.labelLatchVersionId, dwRunCtx->base.sessionCtx, ccType);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Acquire latch in dw when begin transaction.");
            return ret;
        }

        ret = DwBeginTrxInner(dwRunCtx, remoteId);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DwReleaseLatchForVertexLabel(dwRunCtx->base.labelLatch, dwRunCtx->base.sessionCtx, ccType);
            DB_LOG_ERROR(ret, "Dw begin transaction.");
        }
        return ret;
    }

    if (SeTransGetState(seRunCtx) == TRX_STATE_ABORT) {
        DB_LOG_AND_SET_LASERR(GMERR_TRANSACTION_ROLLBACK, "Need rollback when begin transaction.");
        return GMERR_TRANSACTION_ROLLBACK;
    }

    return GMERR_OK;
}

static void AllocPubSubBuf(GmcStmtT *stmt, DwRunCtxT *dwRunCtx)
{
    DmSubsEventE subsEvent = dwRunCtx->subsEvent;
    uint64_t trxId = dwRunCtx->base.trxId;
    Status ret = DwPubSubAllocBuf(stmt->conn, &dwRunCtx->labelDef, dwRunCtx->ageEvent, trxId, &dwRunCtx->ageRowData);
    // 不支持可靠订阅，失败不返回处理
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Dw pubsub age data, event type %" PRIu32 ".", (uint32_t)dwRunCtx->ageEvent);
    }
    ret = DwPubSubAllocBuf(stmt->conn, &dwRunCtx->labelDef, subsEvent, trxId, &dwRunCtx->subsRowData);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Dw pubsub row data, event type %" PRIu32 ".", (uint32_t)subsEvent);
    }
}

void DwSetTrxCommitCallBack(SeRunCtxHdT seRunCtx, DwRunCtxT *dwRunCtx)
{
    // 设置事务提交统计值刷新回调。直连写isLiteTrx必然是true。
    TrxCommitCallBackCfgT commitCallBackCfg = {
        .func = DwTrxCommitCallBack,
        .parameter = dwRunCtx->labelStatistics,
    };
    SeSetTrxCommitCallBackCfg(seRunCtx, &commitCallBackCfg);

    // 设置状态合并订阅回调函数
    if (!dwRunCtx->stmgSubData.isStMgLabel) {
        return;
    }
    DwStMgSubDataT *subData = &dwRunCtx->stmgSubData;
    UpdateStatusMergeListCfgT cfg = {
        .func = DwUpdateStMgListCb,
        .statusMergeSubDataSet = subData,
    };
    SeSetTrxCommitStatusMergeCfg(seRunCtx, &cfg);
}

static void ResetStmtStat4TrxCommitAbn(GmcStmtT *stmt)
{
    stmt->affectRows = 0;
}

// opStatus 是上层的执行操作返回的结果
// 若opStatus正常，则最终返回给客户端接口的errCode由事务提交成功与否来决定
// 若opStatus异常，则应该保留该错误码信息
Status DwEndTrx(GmcStmtT *stmt, DwRunCtxT *dwRunCtx, Status opStatus, ConcurrencyControlE ccType)
{
    DB_POINTER(dwRunCtx);
    // 只实现autoCommit && isLiteTrx (RU)
    Status ret = opStatus;
    SeRunCtxHdT seRunCtx = (SeRunCtxHdT)dwRunCtx->base.seRunCtxHandle;
    LabelRWLatchT *labelRWLatch = (LabelRWLatchT *)dwRunCtx->base.labelLatch;
    DbSessionCtxT *sessionCtx = dwRunCtx->base.sessionCtx;
    DwSetTrxCommitCallBack(seRunCtx, dwRunCtx);

    if (SECUREC_LIKELY(opStatus == GMERR_OK)) {
        if (SECUREC_UNLIKELY((ret = SeTransCommit(seRunCtx)) != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Dw commit transaction.");
            DwResetLabelStatistics(dwRunCtx->labelStatistics);
            DwReleaseStMgSubNode(&dwRunCtx->stmgSubData);
            DwResetDwRunCtx4Stmt(dwRunCtx, stmt->memCtx);
            DwResetResInfo(stmt);
            DwCloseRes(dwRunCtx);
            DwReleaseLatchForResPool(dwRunCtx, sessionCtx);
            DwReleaseLatchForVertexLabel(labelRWLatch, sessionCtx, ccType);
            ResetStmtStat4TrxCommitAbn(stmt);
            return ret;
        }
        if (dwRunCtx->dwArgs.vertexArgs.hasSubs) {
            AllocPubSubBuf(stmt, dwRunCtx);
        }

        // 对于状态合并表，无论是否有订阅关系，都需要维护推送消息，所以没必要判断subscriptionNum
        if (SECUREC_UNLIKELY(dwRunCtx->stmgSubData.isStMgLabel)) {
            DwPushStMgDataAfterCommit(&dwRunCtx->stmgSubData);
        }
    } else {
        DwResetLabelStatistics(dwRunCtx->labelStatistics);
        Status retTmp = SeTransRollback(seRunCtx, false);
        if (SECUREC_UNLIKELY(retTmp != GMERR_OK)) {
            DB_LOG_ERROR(retTmp, "Dw rollback transaction.");
        }
        DwReleaseStMgSubNode(&dwRunCtx->stmgSubData);
        DwResetResInfo(stmt);
        ResetStmtStat4TrxCommitAbn(stmt);
    }
    DwResetDwRunCtx4Stmt(dwRunCtx, stmt->memCtx);
    DwCloseRes(dwRunCtx);
    DwReleaseLatchForResPool(dwRunCtx, sessionCtx);
    DwReleaseLatchForVertexLabel(labelRWLatch, sessionCtx, ccType);
    if (dwRunCtx->dwArgs.vertexArgs.hasSubs) {
        DwPubSub(stmt->conn, dwRunCtx->subsRowData.bufferPtr);
        DwPubSub(stmt->conn, dwRunCtx->ageRowData.bufferPtr);
    }
    return ret;
}
