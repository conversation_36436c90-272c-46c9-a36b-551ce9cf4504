/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 * Description: Access Method for directwrite
 * Author:
 * Create:
 */
#include "db_tuple_buffer.h"
#include "clt_resource.h"
#include "clt_stmt.h"
#include "clt_graph_filter.h"
#include "clt_exception.h"
#include "clt_stmt_extend.h"
#include "clt_da_read.h"
#include "clt_da_write_index.h"
#include "clt_da_write_storage.h"
#include "clt_da_handle.h"
#include "se_trx.h"
#include "se_clustered_hash_label_base.h"
#include "ee_background_schedule.h"
#include "gmc_internal.h"
#include "adpt_process_name.h"
#include "clt_da_write_respool.h"
#include "db_dyn_load.h"
#include "clt_da_write.h"
#include "adpt_cpu_stats.h"
#include "ee_check_utils.h"

DbSpinLockT g_gmdbReserveMemCtxLock = {0};  // 控制客户端创建逃生通道的并发

// 对于 replace 操作，在存储层会进一步区分为 HEAP_OPTYPE_REPLACE_INSERT 和 HEAP_OPTYPE_REPLACE_UPDATE
// 直连写流程中，初始化为 HEAP_OPTYPE_REPLACE_INSERT，并在 replace 的具体执行流程中，
// 再区分到底是 REPLACE_INSERT 还是 REPLACE_UPDATE
HpOpTypeE GetDwOpTypeToHeapOpType(GmcOperationTypeE opType)
{
    switch (opType) {
        case GMC_OPERATION_INSERT:
            return HEAP_OPTYPE_INSERT;
        case GMC_OPERATION_REPLACE:
            return HEAP_OPTYPE_REPLACE_INSERT;
        case GMC_OPERATION_DELETE:
            return HEAP_OPTYPE_DELETE;
        case GMC_OPERATION_UPDATE:
            return HEAP_OPTYPE_UPDATE;
        case GMC_OPERATION_CHECK_REPLACE:
            return HEAP_OPTYPE_UPDATE;
        default:
            return HEAP_OPTYPE_MAX_TYPE;
    }
}

MsgOpcodeRpcE GetDwOpTypeToRpcOpCode(GmcOperationTypeE opType)
{
    switch (opType) {
        case GMC_OPERATION_INSERT:
            return MSG_OP_RPC_INSERT_VERTEX;
        case GMC_OPERATION_REPLACE:
            return MSG_OP_RPC_REPLACE_VERTEX;
        case GMC_OPERATION_DELETE:
            return MSG_OP_RPC_DELETE_VERTEX;
        case GMC_OPERATION_UPDATE:
            return MSG_OP_RPC_UPDATE_VERTEX;
        case GMC_OPERATION_CHECK_REPLACE:
            return MSG_OP_RPC_CHECK_REPLACE;
        default:
            return MSG_OP_RPC_SHARED_OBJ_END;
    }
}

// 操作类型与对应的执行函数，以及执行该操作所需要的权限
DwExecHandlerT g_gmdbDwExecutor[] = {
    [GMC_OPERATION_INSERT] = {DwExecInsertEntry, {DM_OBJ_INSERT_PRIV, INSERTANY_PRIV}},
    [GMC_OPERATION_REPLACE] = {DwExecReplaceEntry, {DM_OBJ_REPLACE_PRIV, REPLACEANY_PRIV}},
    [GMC_OPERATION_DELETE] = {DwExecDeleteEntry, {DM_OBJ_DELETE_PRIV, DELETEANY_PRIV}},
    [GMC_OPERATION_UPDATE] = {DwExecUpdateEntry, {DM_OBJ_UPDATE_PRIV, UPDATEANY_PRIV}},
    [GMC_OPERATION_CHECK_REPLACE] = {DwExecCheckReplaceEntry, {DM_OBJ_REPLACE_PRIV, REPLACEANY_PRIV}},
};

bool IsStmtSupportDirectWrite(GmcStmtT *stmt)
{
    if (!stmt->conn->enableDirectWrite || stmt->conn->connType != GMC_CONN_TYPE_SYNC ||
        stmt->conn->transMode != GMC_TRANS_NONE || stmt->conn->instanceId != 1 || stmt->batch != NULL) {
        return false;
    }
    return true;
}

bool IsDirectWriteOps(GmcOperationTypeE opType)
{
    return opType == GMC_OPERATION_INSERT || opType == GMC_OPERATION_REPLACE || opType == GMC_OPERATION_DELETE ||
           opType == GMC_OPERATION_UPDATE || opType == GMC_OPERATION_CHECK_REPLACE;
}

bool DirectWriteConstraintVerify(GmcStmtT *stmt, DmVertexLabelT *vertexLabel)
{
    /*
     * 1、直连写只支持基于主键索引的操作，若索引未设置或不是主键索引，则不支持走直连写
     * 2、直连写只支持RU模式
     * 3、直连写不支持显示事务
     * 4、直连写不支持yang、datalog
     * 5、直连写不支持自增列、bitmap、资源列
     * 6、直连写不支持边表
     * 7、直连写仅支持同步链接
     * 8、直连写仅支持replace、update、insert、delete、check_replace
     * 9、直连写不支持批量
     * 最后三点已在 IsStmtSupportDirectWrite 和 IsDirectWriteOps 方法中校验
     */
    // 表升级可能新增 bitMap 字段，需要重新校验
    if (SECUREC_LIKELY(stmt->vlUseDirectWrite && !vertexLabel->commonInfo->hasUpd)) {
        return true;
    }

    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
    if ((MEMBER_PTR(vertexLabel->metaVertexLabel, pkIndex) == NULL) || !DmIsLabelLiteTrx(commonInfo->heapInfo.ccType) ||
        DmIsYangVertexLabel(vertexLabel) || DmVertexLabelIsDatalogLabel(vertexLabel) ||
        (commonInfo->dlrInfo.isDataSyncLabel) || (commonInfo->edgeLabelNum > 0) || !commonInfo->enableDirectWrite) {
        stmt->vlUseDirectWrite = false;
        return false;
    }
    stmt->vlUseDirectWrite = true;
    return true;
}

bool IsSupportDirectWrite(GmcStmtT *stmt)
{
    // 信号接管未注册，不允许走直连写
    if (!stmt->openDirectWrite) {
        return false;
    }
    // 更新或删除操作时，仅支持主键索引、唯一二级索引。这里获取到的是通过GmcSetIndexKeyName设置的索引
    // 结构化写等场景如果先期按照直连写准备相关资源，后续如果再走CS模式会出问题。通过assert看护整个流程必须保持一致。
    if (stmt->operationType == GMC_OPERATION_UPDATE || stmt->operationType == GMC_OPERATION_DELETE) {
        StatusAndPtrT getKeyRet = CltGetSimpleIndexKey(stmt);
        if (getKeyRet.status != GMERR_OK) {
            return false;
        }
        DmIndexKeyT *idxKey = (DmIndexKeyT *)getKeyRet.ptr;
        if (idxKey == NULL || (idxKey->indexConstraint != PRIMARY && idxKey->indexConstraint != UNIQUE)) {
            if (stmt->isStructure) {
                DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED,
                    "DW struct write constraint violation during exec check. "
                    "IdxKey is null: %" PRIu32 ", indexConstraint: %" PRIu32 ".",
                    (uint32_t)(idxKey == NULL), idxKey != NULL ? idxKey->indexConstraint : DB_INVALID_UINT32);
                DB_ASSERT(false);
            }
            return false;
        }
    }
    return true;
}

Status TryAcqDwEntryToken(GmcStmtT *stmt)
{
    CltOperVertexT *op = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = op->cltCataLabel->vertexLabel;
    if (vertexLabel != NULL) {
        uint32_t pid = stmt->conn->pid;
        uint32_t tryTimes = 0;
        uint64_t startTime = DbRdtsc();
        while (!DbAtomicBoolCAS(&vertexLabel->commonInfo->dwEntryToken, DB_INVALID_UINT32, pid)) {
            if (SECUREC_UNLIKELY(tryTimes > DW_TOKEN_TRY_TIMES_LIMIT)) {
                uint64_t usedTime = DbToUseconds(DbRdtsc() - startTime);
                DB_LOG_AND_SET_LASERR(GMERR_LOCK_NOT_AVAILABLE,
                    "Dw token acquire timeout, labelname: %s, %" PRIu64 "ms total.",
                    MEMBER_PTR(&vertexLabel->metaCommon, metaName), usedTime);
                return GMERR_LOCK_NOT_AVAILABLE;
            }
            DbUsleep(DW_TOKEN_SLEEP_TIME_US);
            tryTimes++;
        }
    }
    return GMERR_OK;
}

void ClearDwEntryToken(GmcStmtT *stmt)
{
    CltOperVertexT *op = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = op->cltCataLabel->vertexLabel;
    if (vertexLabel != NULL) {
        uint32_t pid = stmt->conn->pid;
        while (!DbAtomicBoolCAS(&vertexLabel->commonInfo->dwEntryToken, pid, DB_INVALID_UINT32)) {
            DbUsleep(DW_TOKEN_SLEEP_TIME_US);
        }
    }
}

// 非性能路径函数
static Status CreateReserveMemCtx(uint16_t instanceId)
{
    if (g_reserveMemCtxForCltW != NULL) {
        return GMERR_OK;
    }
    DbSpinLock(&g_gmdbReserveMemCtxLock);
    if (g_reserveMemCtxForCltW != NULL) {
        DbSpinUnlock(&g_gmdbReserveMemCtxLock);
        return GMERR_OK;
    }
    SeInstanceT *seIns = SeGetInstance(instanceId);
    if (seIns == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "storage instance %" PRIu16 " unsound", instanceId);
        DbSpinUnlock(&g_gmdbReserveMemCtxLock);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DbMemCtxArgsT args = {0};
    args.liteModOn = true;
    args.maxTotalPhySize = SeGetEscapeMemCtxMaxSize(seIns->seConfig.pageSize * DB_KIBI);
    args.isEscapeCtx = true;
    /* Clt-SeEscapeMemCtx 说明
        用    途: 用于存储内部使用, 在事务提交或回滚阶段出现内存不足时代替sessionMemCtx用作逃生使用
        生命周期: 长进程
        释放策略: 就近释放
        兜底清空措施: 依赖g_gmdbCltInstance.memCtx，在客户端进程退出时销毁root节点来清理(CltUnInit)
    */
    DbMemCtxT *seEscapeMemCtx = DbCreateDynMemCtx(g_gmdbCltInstance.memCtx, true, "Clt-SeEscapeMemCtx", &args);
    if (seEscapeMemCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Create clt-escape dynamic mem ctx worthless!");
        DbSpinUnlock(&g_gmdbReserveMemCtxLock);
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    DbRegEscapeCtx(NULL, seEscapeMemCtx);
    g_reserveMemCtxForCltW = seEscapeMemCtx;

    DbSpinUnlock(&g_gmdbReserveMemCtxLock);
    return GMERR_OK;
}

static Status SeOpenShmProcess(void *args)
{
    // 由于在直连写场景下，会申请事务资源加非session锁，位于SeOpenTrx函数，所以需要做客户端异常退出的信号安全保护
    SeOpenShmProcessArgs *seOpenArgs = (SeOpenShmProcessArgs *)args;
    return SeOpen(seOpenArgs->instanceId, seOpenArgs->memCtx, seOpenArgs->cltSeOpenCfg, seOpenArgs->seRunCtxHandle);
}

static Status AllocDwRunCtxAndGetSeRunCtx(GmcConnT *conn, DwRunCtxT **dwRunCtx, uint16_t instanceId, uint32_t sessionId)
{
    Status ret;
    DbMemCtxT *memCtx = conn->memCtx;
    // 直连写上下文初始化流程。在建连时创建，断连时释放。
    DwRunCtxT *dwRunCtxTmp = (DwRunCtxT *)DbDynMemCtxAlloc(memCtx, sizeof(DwRunCtxT));
    if (dwRunCtxTmp == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Dw RunCtx alloc.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(dwRunCtxTmp, sizeof(DwRunCtxT), 0x00, sizeof(DwRunCtxT));

    // 为直连写申请trx提交/回滚时可能需要用到的逃生内存
    if ((ret = CreateReserveMemCtx(instanceId)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "Dw create reserved memCtx for client.");
        goto CLEAN1;
    }

    // 获取se的运行上下文，并且此操作是受信号处理框架保护。直连写SE句柄挂在conn上，生命周期和链接一致。
    SeOpenShmProcessArgs seOpenArgs = {.instanceId = instanceId,
        .memCtx = memCtx,
        .cltSeOpenCfg = &(SeOpenCfgT){.isClient = true,
            .isDirectWrite = true,
            .rsmCtxPtr = conn->rsmCtxPtr,
            .reserveMemCtx = g_reserveMemCtxForCltW,
            .pageMgrMemCtx = g_gmdbCltInstance.memCtx},
        .seRunCtxHandle = (SeRunCtxHdT *)&dwRunCtxTmp->base.seRunCtxHandle};
    if ((ret = CltProtectShmProcess(SeOpenShmProcess, (void *)&seOpenArgs, SHMEM_OP_DIRECT_WRITE)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "Dw open seRunCtx.");
        goto CLEAN1;
    }

    if ((ret = SeAttachResSessionById(dwRunCtxTmp->base.seRunCtxHandle, sessionId)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "Dw attach session to seRunCtx.");
        goto CLEAN2;
    }

    // SeOpen执行成功后，就占用了一个事务槽，将该事务槽保存在DbSession中，在异常断连时，服务端根据该事务槽进行回收
    SeRunCtxT *seRunCtx = (SeRunCtxT *)dwRunCtxTmp->base.seRunCtxHandle;
    seRunCtx->resSessionCtx.session->dwTrxSlot = ((TrxT *)seRunCtx->trx)->base.trxSlot;

    *dwRunCtx = dwRunCtxTmp;
    return GMERR_OK;
CLEAN2:
    (void)SeClose(dwRunCtxTmp->base.seRunCtxHandle);
CLEAN1:
    DbDynMemCtxFree(memCtx, dwRunCtxTmp);
    return ret;
}

Status DwOpenDirectWriteRunCtx(GmcConnT *conn, uint32_t sessionId, DwRunCtxT **dwRunCtx)
{
    Status ret = AllocDwRunCtxAndGetSeRunCtx(conn, dwRunCtx, (uint16_t)conn->instanceId, sessionId);
    if (ret != GMERR_OK) {
        return ret;
    }
    DwRunCtxT *dwRunCtxLocal = *dwRunCtx;
    // 初始化（建连时，vertexLabel还获取不到，只能初始化部分字段）
    ret = DirectAccessInitBaseRunCtxInfo(&(*dwRunCtx)->base, conn->memCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Dw init base runctx.");
        return ret;
    }
    dwRunCtxLocal->secIdxCtx = NULL;
    dwRunCtxLocal->secIdxNum = 0;
    dwRunCtxLocal->tupleAddr = 0;
    dwRunCtxLocal->cachedTupBuf = NULL;
    dwRunCtxLocal->cachedTupBufSize = 0;
    dwRunCtxLocal->dwArgs = (DwArgsT){0};
    dwRunCtxLocal->dwArgs.vertexArgs.partitionId = DM_MAX_PARTITION_ID;
    dwRunCtxLocal->dwArgs.currOpType = GMC_OPERATION_BUTT;
    dwRunCtxLocal->dwArgs.lastOpType = GMC_OPERATION_BUTT;
    dwRunCtxLocal->labelStatistics = NULL;
    dwRunCtxLocal->cfgShmConfig = conn->cfgShmConfig;
    dwRunCtxLocal->agedMgrInfo.latch = DbShmPtrToAddr(conn->agedMgrLatchShmAddr);
    if (dwRunCtxLocal->agedMgrInfo.latch == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED,
            "Get aged mgr latch dynamic addr by shmPtr: (segId: %" PRIu32 ", offset: %" PRIu32 ").",
            conn->agedMgrLatchShmAddr.segId, conn->agedMgrLatchShmAddr.offset);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    dwRunCtxLocal->agedMgrInfo.latchShmAddr = conn->agedMgrLatchShmAddr;
    dwRunCtxLocal->agedMgrInfo.shmTaskMap = conn->agedMgrShmTaskMap;
    TupleBufInit(&dwRunCtxLocal->oldTupleBuf, conn->memCtx);
    QryDfgmtTaskInitAccessMethod();
    return GMERR_OK;
}

// 释放conn级别的直连写资源
void DwCloseDwRunCtx4Conn(DbMemCtxT *memCtx, DwRunCtxT *dwRunCtx)
{
    DB_POINTER2(memCtx, dwRunCtx);
    TupleBufRelease(&dwRunCtx->oldTupleBuf);
    if (dwRunCtx->base.seRunCtxHandle != NULL) {
        (void)SeClose(dwRunCtx->base.seRunCtxHandle);
        dwRunCtx->base.seRunCtxHandle = NULL;
    }
    DbDynMemCtxFree(memCtx, dwRunCtx);
}

static Status AllocLabelStatistics(DwRunCtxT *dwRunCtx, DbMemCtxT *memCtx)
{
    if (dwRunCtx->labelStatistics != NULL) {
        return GMERR_OK;
    }
    // 首次执行时申请内存，执行结束后将这块内存重置清零（见 CommitCallBack()），但不释放。
    // 在GmcFreeStmt()时释放(见
    // ReleaseDwRunCtx4StmtInner())，其生命周期和挂在DwRunCtxT下的索引、heapCtx/chCtx等保持一致。
    DwLabelStatisticT *statisticsTmp = (DwLabelStatisticT *)DbDynMemCtxAlloc(memCtx, sizeof(DwLabelStatisticT));
    if (statisticsTmp == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Dw alloc label statistics.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(statisticsTmp, sizeof(DwLabelStatisticT), 0x00, sizeof(DwLabelStatisticT));
    // init
    statisticsTmp->cltCataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    dwRunCtx->labelStatistics = statisticsTmp;
    return GMERR_OK;
}

static void DwGetUserDefItemNumFromCheckInfo(DwLabelStatisticT *statistics, uint64_t *userDefItemNum)
{
    CltCataLabelT *cataLabel = statistics->cltCataLabel;
    DmAccCheckT *accCheckAddr = cataLabel->vertexAccCheck;
    uint32_t partitionNum = accCheckAddr->isPartition ? DM_MAX_PARTITION_ID : 1;
    uint64_t logicCnt = 0;
    for (uint8_t i = 0; i < partitionNum; i++) {
        DmCheckInfoT *checkInfo = &accCheckAddr->checkInfo[i];
        logicCnt += checkInfo->logicCnt;
    }
    *userDefItemNum = logicCnt;
}

static int64_t DwGetUserDefItemNumFromTrxStat(DwLabelStatisticT *statistics)
{
    int64_t logicCnt = 0;
    CltCataLabelT *cataLabel = statistics->cltCataLabel;
    DmAccCheckT *accCheckAddr = cataLabel->vertexAccCheck;
    uint32_t partitionNum = accCheckAddr->isPartition ? DM_MAX_PARTITION_ID : 1;
    for (uint8_t i = 0; i < partitionNum; i++) {
        DwLabelStatisticValueT *value = &statistics->statisticsValue[i];
        logicCnt += value->logicCnt;
    }
    return logicCnt;
}

static Status DwCheckMaxRecord(DwLabelStatisticT *statistics)
{
    DmVertexLabelT *vertexLabel = statistics->cltCataLabel->vertexLabel;
    uint64_t maxRecordCount = vertexLabel->commonInfo->heapInfo.maxVertexNum;
    uint64_t userDefItemNum = 0;
    if (!vertexLabel->commonInfo->heapInfo.maxVertexNumCheck) {
        maxRecordCount = DB_MAX_UINT64;
    }
    // 已提交的记录数
    DwGetUserDefItemNumFromCheckInfo(statistics, &userDefItemNum);
    // 未提交的记录数
    int64_t insertNum = DwGetUserDefItemNumFromTrxStat(statistics);
    // 轻量化事务，提交没有加锁，但是直连写一次操作只会更改同一张表，不涉及修改本事务没有修改的表。
    if (insertNum == 0) {
        return GMERR_OK;
    }
    if (SECUREC_UNLIKELY((userDefItemNum + insertNum) > maxRecordCount)) {
        uint64_t phyItemNum = 0;
        (void)ContainerGetPhyItemNumByVertexLabel(vertexLabel, &phyItemNum);
        DB_LOG_AND_SET_LASERR(GMERR_RECORD_COUNT_LIMIT_EXCEEDED,
            "curPhyItemNum:%" PRIu64 ", curUserDefItemNum:%" PRIu64 ", insertNum:%" PRIi64 ", maxItemNum:%" PRIu64 "",
            phyItemNum - insertNum, userDefItemNum, insertNum, maxRecordCount);
        return GMERR_RECORD_COUNT_LIMIT_EXCEEDED;
    }
    return GMERR_OK;
}

/*
 * 该方法在每次DML操作提交事务时执行。
 * 将一次DML操作流程中统计到的对账计数信息(对应为tuple级别)汇聚到vertexLabel中的checkInfo中(对应为表级别)。
 * 在对账老化场景中，会基于vertexLabel中的checkInfo执行相应的老化任务。
 */
Status DwTrxCommitCallBack(void *parameter)
{
    DwLabelStatisticT *statistics = (DwLabelStatisticT *)parameter;
    CltCataLabelT *cataLabel = statistics->cltCataLabel;
    DmVertexLabelT *vertexLabel = cataLabel->vertexLabel;
    // 只有Normal表支持对账老化和新订阅
    if (SECUREC_UNLIKELY(!DmVertexLabelIsNormalLabel(vertexLabel))) {
        return GMERR_OK;
    }
    // 校验记录数是否超限
    Status ret = DwCheckMaxRecord(statistics);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    DmAccCheckT *accCheckAddr = cataLabel->vertexAccCheck;
    uint32_t partitionNum = accCheckAddr->isPartition ? DM_MAX_PARTITION_ID : 1;
    // 刷新记录数到元数据
    for (uint32_t i = 0; i < partitionNum; i++) {
        DmCheckInfoT *checkInfo = &accCheckAddr->checkInfo[i];
        DwLabelStatisticValueT *value = &statistics->statisticsValue[i];
        checkInfo->recordCnt += value->recordCnt;
        checkInfo->logicCnt += value->logicCnt;
        value->recordCnt = 0;
        value->logicCnt = 0;
        // 未发生过对账则不统计后续的 Cnt
        if (!checkInfo->version.hasBeenChecked) {
            continue;
        }
        // 轻量化事务，提交没有加锁，但是直连写一次操作只会更改同一张表，不涉及修改本事务没有修改的表。
        checkInfo->changeVertexCnt += value->changeVertexCnt;
        checkInfo->realAgedCnt += value->realAgedCnt;
        checkInfo->realTruncatedCnt += value->realTruncatedCnt;
        checkInfo->realRecoveryCnt += value->realRecoveryCnt;
        // 使用后，计数重置清零
        *value = (DwLabelStatisticValueT){0};
    }
    return ret;
}

// 获取一条记录的对账版本号
static void GetTupleCheckVersion(CltCataLabelT *cataLabel, const HeapTupleBufT *heapTupleBuf, uint8_t *checkVersion)
{
    DmValueT value;
    value.type = DB_DATATYPE_UINT8;
    DmVertexBufGetSysPrope(CHECK_VERSION, heapTupleBuf->buf, &value, cataLabel->vertexLabel->vertexDesc);
    *checkVersion = value.value.ubyteValue;
}

Status DwGetPartitionId(
    DmVertexLabelT *vertexLabel, CltCataLabelT *cataLabel, const HeapTupleBufT *heapTupleBuf, uint8_t *partitionId)
{
    DB_POINTER3(vertexLabel, heapTupleBuf, partitionId);
    Status ret;
    DmValueT partitionVal;
    DmAccCheckT *accCheckAddr = cataLabel->vertexAccCheck;
    if (SECUREC_UNLIKELY(accCheckAddr->isPartition)) {
        ret = DmVertexBufGetPartitionValue(vertexLabel, heapTupleBuf->buf, heapTupleBuf->bufSize, &partitionVal);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (partitionVal.value.partitionValue >= DM_MAX_PARTITION_ID) {
            // last error信息和服务端保持一致
            char *metaName = MEMBER_PTR(&vertexLabel->metaCommon, metaName);
            DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED,
                "Partition ID %" PRIu8 " of vertexLabel %s exceeds the upper limit %" PRIu16,
                partitionVal.value.partitionValue, metaName, DM_MAX_PARTITION_ID);
            return GMERR_PROGRAM_LIMIT_EXCEEDED;
        }
        *partitionId = partitionVal.value.partitionValue;
    } else {
        *partitionId = DB_INVALID_UINT8;
    }
    return GMERR_OK;
}

Status DwGetCheckInfo(DmVertexLabelT *vertexLabel, CltCataLabelT *cataLabel, TupleT *tuple, DmCheckInfoT **checkInfo)
{
    uint8_t partitionId = 0;
    Status ret = DwGetPartitionId(vertexLabel, cataLabel, tuple, &partitionId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    *checkInfo = DwGetCheckInfoById(cataLabel, partitionId);
    return GMERR_OK;
}

void DwStatisticsChangeForCheckAccount(
    DwRunCtxT *dwRunCtx, CltCataLabelT *cataLabel, const HeapTupleBufT *heapTupleBuf, uint8_t partitionId)
{
    DmCheckInfoT *checkInfo = NULL;
    uint8_t checkVersion = 0;
    checkInfo = DwGetCheckInfoById(cataLabel, partitionId);  // 获取该记录所属的vertexLabel中的对账信息
    GetTupleCheckVersion(cataLabel, heapTupleBuf, &checkVersion);  // 获取该记录的对账版本号
    // tuple的对账版本号和vertexLabel的对账版本号一致，表示这条tuple是最新的
    if (CataIsCheckVersion(&checkInfo->version, checkVersion)) {
        return;
    }
    uint8_t id = partitionId == DB_INVALID_UINT8 ? 0 : partitionId;
    if (CataIsOldCheckVersion(&checkInfo->version, checkVersion)) {
        dwRunCtx->labelStatistics->statisticsValue[id].changeVertexCnt++;
        if (CataIsRecoveryVersion(&checkInfo->version, checkVersion)) {
            dwRunCtx->labelStatistics->statisticsValue[id].realRecoveryCnt++;
        }
        return;
    }
    if ((dwRunCtx->dataStatus & DW_OLD_DATA_STATUS_TRUNCATE) != 0) {
        dwRunCtx->labelStatistics->statisticsValue[id].realTruncatedCnt++;
    } else {
        dwRunCtx->labelStatistics->statisticsValue[id].realAgedCnt++;
    }
    return;
}

// 该函数只在update流程中调用，用来更新对账相关信息，和QryStaticChange4CheckAccountBySubType相对应
// 状态合并表在update流程中遇到标记删除的数据不更新checkinfo相关的dfx计数，因为这个计数在标记删除过程中已经更新过了，这里不能重复更新
// 本质原因是状态合并的标记删除状态比数据的老化状态更强
void DwStatisticsChangeForCheckAccountBySubType(
    DwRunCtxT *dwRunCtx, CltCataLabelT *cataLabel, const HeapTupleBufT *heapTupleBuf, uint8_t partitionId)
{
    DmVertexLabelT *vertexLabel = cataLabel->vertexLabel;
    uint8_t deleteMark = 0;
    if (DmIsLabelSupportStatusMerge(vertexLabel)) {
        DwGetStMgDataDeleteMark(heapTupleBuf, cataLabel, &deleteMark);
        if (deleteMark != 0) {
            return;
        }
    }
    DwStatisticsChangeForCheckAccount(dwRunCtx, cataLabel, heapTupleBuf, partitionId);
}

static Status IsVertexBufAged(const HeapTupleBufT *heapTupleBuf, CltCataLabelT *cataLabel, bool *isAged)
{
    DB_POINTER3(heapTupleBuf, cataLabel, isAged);
    uint8_t checkVersion;
    DmCheckInfoT *checkInfo = NULL;
    DmVertexLabelT *vertexLabel = cataLabel->vertexLabel;

    // 性能路径上，如果没有发生过对账则不进行老化数据的检查
    DmAccCheckT *accCheckAddr = (DmAccCheckT *)(cataLabel->vertexAccCheck);
    if (SECUREC_LIKELY(!accCheckAddr->hasBeenChecked)) {
        *isAged = false;
        return GMERR_OK;
    }
    uint8_t partitionId;
    Status ret = DwGetPartitionId(vertexLabel, cataLabel, heapTupleBuf, &partitionId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Get partition ID in dw when check vertexbuf aged.");
        return ret;
    }
    checkInfo = DwGetCheckInfoById(cataLabel, partitionId);
    GetTupleCheckVersion(cataLabel, heapTupleBuf, &checkVersion);
    *isAged = QryIsAged(&checkInfo->version, checkVersion);
    return GMERR_OK;
}

Status DwTupleCheckVersionUpdate(DwRunCtxT *dwRunCtx, HeapTupleBufT *heapTupleBuf, bool *isAged)
{
    DB_POINTER(dwRunCtx);
    Status ret = GMERR_OK;
    DmVertexLabelT *vertexLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel->vertexLabel;
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    TupleT oldTuple = TupleBufGet(&dwRunCtx->oldTupleBuf);

    // 获取分区id
    uint8_t partitionId = 0;
    ret = DwGetPartitionId(vertexLabel, cataLabel, &oldTuple, &partitionId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // 统计
    DwStatisticsChangeForCheckAccountBySubType(dwRunCtx, cataLabel, &oldTuple, partitionId);
    // 更新tuple的checkVersion，确保和vertexLabel中的checkVersion保持一致
    DmCheckInfoT *checkInfo = DwGetCheckInfoById(cataLabel, partitionId);
    ret = IsVertexBufAged(&oldTuple, cataLabel, isAged);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    DmVertexBufSetSysPrope(CHECK_VERSION,
        &(DmValueT){.type = DB_DATATYPE_UINT8, .value.ubyteValue = checkInfo->version.version},
        cataLabel->vertexLabel->vertexDesc, heapTupleBuf->buf);
    return GMERR_OK;
}

static void ReleaseLabelStatistics(DwRunCtxT *dwRunCtx, DbMemCtxT *memCtx)
{
    if (dwRunCtx->labelStatistics != NULL) {
        DbDynMemCtxFree(memCtx, dwRunCtx->labelStatistics);
        dwRunCtx->labelStatistics = NULL;
    }
}

void DwResetLabelStatistics(DwLabelStatisticT *labelStatistics)
{
    if (labelStatistics != NULL) {
        for (uint32_t i = 0; i < DM_MAX_PARTITION_ID; i++) {
            labelStatistics->statisticsValue[i] = (DwLabelStatisticValueT){0};
        }
    }
}

void ReleaseDwRunCtx4StmtInner(DwRunCtxT *dwRunCtx, DbMemCtxT *memCtx)
{
    DB_POINTER2(dwRunCtx, memCtx);
    ReleaseLabelStatistics(dwRunCtx, memCtx);
    ReleaseAllIdxCtxs(dwRunCtx, memCtx);
    ReleaseHeapCtx(dwRunCtx);
    DbDynMemCtxFree(memCtx, dwRunCtx->structWriteBuf);
    dwRunCtx->structWriteBufLen = 0;
    dwRunCtx->structWriteBuf = NULL;
    dwRunCtx->ready4StructWrite = false;
    dwRunCtx->useStructSeriBuf = false;
    dwRunCtx->isMaxVersionChange = false;
    if (dwRunCtx->cachedTupBuf != NULL) {
        DbDynMemCtxFree(memCtx, dwRunCtx->cachedTupBuf);
        dwRunCtx->cachedTupBuf = NULL;
        dwRunCtx->cachedTupBufSize = 0;
    }
}

static Status HeapOpenAndIdxesOpen(DwRunCtxT *dwRunCtx, DbMemCtxT *memCtx)
{
    DmVertexLabelT *vertexLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel->vertexLabel;
    Status ret = HeapOpenAndInitHeapRunCtx(dwRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Dw open heap.");
        return ret;
    }

    // 初始化主键索引的运行上下文
    DmVlIndexLabelT *pkIndex = MEMBER_PTR(vertexLabel->metaVertexLabel, pkIndex);
    ret = PkIdxOpenAndInitPkIdxCtx(dwRunCtx, pkIndex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Dw open pk index.");
        // 失败场景，此处应释放heap的内存
        ReleaseHeapCtx(dwRunCtx);
        return ret;
    }

    // 初始化所有二级索引的运行上下文
    ret = SecondaryIdxesOpenAndInitCtxs(dwRunCtx, memCtx, vertexLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Dw open sec index.");
        // 失败场景，此处应释放pkIdx、heap的内存
        ReleaseAllIdxCtxs(dwRunCtx, memCtx);
        ReleaseHeapCtx(dwRunCtx);
        return ret;
    }
    return GMERR_OK;
}

static Status ChOpenAndIdxesOpen(DwRunCtxT *dwRunCtx, DbMemCtxT *memCtx)
{
    DmVertexLabelT *vertexLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel->vertexLabel;
    // 聚簇容器只需要打开二级索引，且在打开聚簇容器之前
    Status ret = SecondaryIdxesOpenAndInitCtxs(dwRunCtx, memCtx, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Dw open sec index of clustered hash.");
        ReleaseAllIdxCtxs(dwRunCtx, memCtx);
        return ret;
    }

    ret = ChOpenAndInitChRunCtx(dwRunCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Dw open clustered hash.");
        // 失败场景，此处应释放二级索引的内存
        ReleaseAllIdxCtxs(dwRunCtx, memCtx);
        return ret;
    }

    ret = ChSetIdxesParms(dwRunCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Dw set index para of clustered hash.");
        // 失败场景，此处应释放二级索引和聚簇容器上下文(chRunCtx)的内存
        goto EXIT;
    }
    return GMERR_OK;
EXIT:
    ReleaseHeapCtx(dwRunCtx);
    ReleaseAllIdxCtxs(dwRunCtx, memCtx);
    return ret;
}

#ifdef ART_CONTAINER
static Status ArtCltOpen(DwRunCtxT *dwRunCtx, DbMemCtxT *memCtx)
{
    // 聚簇容器只需要打开二级索引，且在打开聚簇容器之前
    Status ret = ArtOpenAndInitArtRunCtx(dwRunCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Dw open art container.");
        // 失败场景，此处应释放二级索引的内存
        ReleaseAllIdxCtxs(dwRunCtx, memCtx);
        return ret;
    }

    ret = ChSetIdxesParms(dwRunCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Dw set index para of art container.");
        // 失败场景，此处应释放二级索引和聚簇容器上下文(chRunCtx)的内存
        ReleaseHeapCtx(dwRunCtx);
        ReleaseAllIdxCtxs(dwRunCtx, memCtx);
        return ret;
    }
    return GMERR_OK;
}
#endif

Status DwOpenHeapAndIdxes(GmcStmtT *stmt, DwRunCtxT *dwRunCtx, DbMemCtxT *memCtx)
{
    DB_POINTER2(dwRunCtx, memCtx);
    if (SECUREC_UNLIKELY(dwRunCtx->isMaxVersionChange)) {
        // 如果表最高版本发生变化，需要先释放基于旧的元数据申请出来的资源，后续再基于最新的元数据重新open
        // 最新的元数据在上一层 ProcessExecuteInner() 中已经更新
        ReleaseDwRunCtx4StmtInner(dwRunCtx, memCtx);
    }

    Status ret = AllocLabelStatistics(dwRunCtx, memCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Dw open label statistics.");
        return ret;
    }
    if (dwRunCtx->base.containerType == CONTAINER_CLUSTERED_HASH) {
        ret = ChOpenAndIdxesOpen(dwRunCtx, memCtx);
    } else if (dwRunCtx->base.containerType == CONTAINER_HEAP) {
        ret = HeapOpenAndIdxesOpen(dwRunCtx, memCtx);
    }
#ifdef ART_CONTAINER
    else if (dwRunCtx->base.containerType == CONTAINER_ART) {
        ret = ArtCltOpen(dwRunCtx, memCtx);
    }
#endif
    else {
        DB_ASSERT(false);
    }
    // 失败场景，此处应释放LabelStatistics的内存
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        ReleaseLabelStatistics(dwRunCtx, memCtx);
        return ret;
    }

    // open resource pool
    ret = DwOpenRes(dwRunCtx, memCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // 失败场景，此处应释放前面已成功申请的索引、存储容器和统计信息
        ReleaseAllIdxCtxs(dwRunCtx, memCtx);
        ReleaseHeapCtx(dwRunCtx);
        ReleaseLabelStatistics(dwRunCtx, memCtx);
        return ret;
    }
    stmt->dwRunCtxHasAllocated = true;
    return ret;
}

static uint32_t AgeTaskKeyToHash32(const DwAgeTaskKeyT *dwAgeTaskKey)
{
    uint32_t keyBufLen = (uint32_t)(sizeof(uint32_t) + sizeof(uint8_t) + sizeof(uint8_t));
    // keyBuf : labelId + partitionId + oldCheckVersion
    uint8_t keyBuf[sizeof(uint32_t) + sizeof(uint8_t) + sizeof(uint8_t)];

    uint8_t *keyBufPtr = keyBuf;
    *((uint32_t *)keyBufPtr) = dwAgeTaskKey->labelId;
    keyBufPtr += sizeof(uint32_t);
    *((uint8_t *)keyBufPtr) = dwAgeTaskKey->partitionId;
    keyBufPtr += sizeof(uint8_t);
    *((uint8_t *)keyBufPtr) = dwAgeTaskKey->oldCheckVersion;
    return DbHash32((uint8_t *)keyBuf, keyBufLen);
}

static uint32_t AgeTaskKeyCompare(const void *key1, const void *key2)
{
    const DwAgeTaskKeyT *ageTaskKey1 = (const DwAgeTaskKeyT *)key1;
    const DwAgeTaskKeyT *ageTaskKey2 = (const DwAgeTaskKeyT *)key2;
    return (ageTaskKey1->labelId == ageTaskKey2->labelId && ageTaskKey1->partitionId == ageTaskKey2->partitionId &&
            ageTaskKey1->oldCheckVersion == ageTaskKey2->oldCheckVersion);
}

static Status HeapTupleIsTruncated(
    DwRunCtxT *dwRunCtx, CltCataLabelT *cataLabel, HeapTupleBufT *heapTupleBuf, bool *isTruncate)
{
    uint8_t partitionId;
    uint8_t checkVersion;

    Status ret = DwGetPartitionId(cataLabel->vertexLabel, cataLabel, heapTupleBuf, &partitionId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Get partition id when get heap tuple truncated status.");
        return ret;
    }
    GetTupleCheckVersion(cataLabel, heapTupleBuf, &checkVersion);
    DwAgeTaskKeyT ageTaskKey;
    SetDwAgeTaskKey(&ageTaskKey, cataLabel->vertexLabel->metaCommon.metaId, partitionId, checkVersion);
    uint32_t hash = AgeTaskKeyToHash32(&ageTaskKey);
    bool *isTruncateTmp = NULL;
    DbRWSpinWLockWithSession(dwRunCtx->base.sessionCtx, dwRunCtx->agedMgrInfo.latch,
        &dwRunCtx->agedMgrInfo.latchShmAddr, LATCH_ADDR_AGED_MGR_LATCH_SHMEM);
    ret = DbShmOamapLookup(dwRunCtx->agedMgrInfo.shmTaskMap, (DbShmOamapFindParaT){hash, &ageTaskKey}, NULL,
        (void *)&isTruncateTmp, AgeTaskKeyCompare);
    DbRWSpinWUnlockWithSession(dwRunCtx->base.sessionCtx, dwRunCtx->agedMgrInfo.latch);
    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        char *metaName = MEMBER_PTR(&cataLabel->vertexLabel->metaCommon, metaName);
        DB_LOG_AND_SET_LASERR(ret,
            "Get vertexLabel (%s) truncate status from aged share taskMap, partitionId:%" PRIu8 ", checkVersion:%" PRIu8
            ".",
            metaName, partitionId, checkVersion);
        return ret;
    }
    *isTruncate = ret == GMERR_NO_DATA ? false : *isTruncateTmp;
    return GMERR_OK;
}

Status DwSetAgeDataStatus(DwRunCtxT *dwRunCtx, CltCataLabelT *cataLabel, HeapTupleBufT *heapTupleBuf, bool *isTruncated)
{
    DB_POINTER5(dwRunCtx, cataLabel, cataLabel->vertexLabel, heapTupleBuf, isTruncated);
    dwRunCtx->dataStatus |= DW_OLD_DATA_STATUS_AGE;
    Status ret = HeapTupleIsTruncated(dwRunCtx, cataLabel, heapTupleBuf, isTruncated);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Dw check whether heapTupleBuf is truncated.");
        return ret;
    }
    if (*isTruncated) {
        dwRunCtx->dataStatus |= DW_OLD_DATA_STATUS_TRUNCATE;
    }
    return GMERR_OK;
}

void DwPushAgeRecordBeforeUpdate(
    DwRunCtxT *dwRunCtx, DmVertexLabelT *vertexLabel, HeapTupleBufT *oldTupleBuf, DmSubsEventE eventType)
{
    DB_POINTER3(dwRunCtx, vertexLabel, oldTupleBuf);
    bool isGenerateSubs = NeedDwGenerateSubsMessage(&dwRunCtx->labelDef, eventType);  // 判断是否需要生成订阅消息
    if (!isGenerateSubs) {
        return;
    }
    dwRunCtx->ageRowData.oldTupleBuf = *oldTupleBuf;
    dwRunCtx->ageEvent = eventType;
}

Status DwCheckRecordPush(DwRunCtxT *dwRunCtx, HeapTupleBufT *heapTupleBuf, CltCataLabelT *cataLabel)
{
    DB_POINTER(dwRunCtx);
    bool isAged = false;
    DmVertexLabelT *vertexLabel = cataLabel->vertexLabel;
    Status ret = IsVertexBufAged(heapTupleBuf, cataLabel, &isAged);
    if (ret != GMERR_OK || !isAged) {
        return ret;
    }
    bool isTruncated = false;
    ret = DwSetAgeDataStatus(dwRunCtx, cataLabel, heapTupleBuf, &isTruncated);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // 标记删除的数据不推送
    if (DmIsLabelSupportStatusMerge(vertexLabel)) {
        uint8_t deleteMark;
        DwGetStMgDataDeleteMark(heapTupleBuf, cataLabel, &deleteMark);
        if (deleteMark != 0) {
            return GMERR_OK;
        }
    }
    DmSubsEventE eventType = isTruncated ? DM_SUBS_EVENT_DELETE : DM_SUBS_EVENT_AGED;
    DwPushAgeRecordBeforeUpdate(dwRunCtx, vertexLabel, heapTupleBuf, eventType);
    return GMERR_OK;
}

// StMg mark delete和老化标记本质都是标记删除，故统一逻辑处理
bool DwIsHeapTupleMarkDelete(const HeapTupleBufT *heapTupleBuf, CltCataLabelT *cataLabel)
{
    bool isAged = false;
    Status ret = IsVertexBufAged(heapTupleBuf, cataLabel, &isAged);
    if (ret != GMERR_OK) {
        return false;
    }
    bool isStMgMarkDelete = DwIsStMgMarkDelete(cataLabel, heapTupleBuf);
    return isAged || isStMgMarkDelete;
}

Status DwCheckBeforeUpdate(void *data, const HeapTupleBufT *oldTuple, const HeapTupleBufT *newTuple)
{
    DB_POINTER3(data, oldTuple, newTuple);
    ChLabelDeleteOrReplaceParaT *userData = (ChLabelDeleteOrReplaceParaT *)data;
    DmVertexLabelT *vertexLabel = ((DwLabelDefT *)userData->labelDef)->cataLabel->vertexLabel;
    Status ret =
        DmCheckIsPartitionNotChange(vertexLabel, oldTuple->buf, oldTuple->bufSize, newTuple->buf, newTuple->bufSize);
    if (ret != GMERR_OK) {
        char *metaName = MEMBER_PTR(&vertexLabel->metaCommon, metaName);
        DB_LOG_DBG_ERROR(ret, "label %s partition not support change.", metaName);
        return ret;
    }
    return GMERR_OK;
}

Status ChCheckNeedUpdateViewNum(const HeapTupleBufT *heapTupleBuf, void *data)
{
    DB_POINTER2(heapTupleBuf, data);
    ChLabelDeleteOrReplaceParaT *userData = (ChLabelDeleteOrReplaceParaT *)data;
    userData->isNeedUpdateViewNum = false;
    userData->isAged = false;
    userData->deleteMark = 0;
    DB_ASSERT(((DwLabelDefT *)userData->labelDef)->labelType == VERTEX_LABEL);
    CltCataLabelT *cataLabel = ((DwLabelDefT *)userData->labelDef)->cataLabel;
    Status ret = IsVertexBufAged(heapTupleBuf, cataLabel, &userData->isAged);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (DmIsLabelSupportStatusMerge(cataLabel->vertexLabel)) {
        DwGetStMgDataDeleteMark(heapTupleBuf, cataLabel, &userData->deleteMark);
    }
    userData->isNeedUpdateViewNum = (userData->deleteMark != 0) ? true : userData->isAged;
    return GMERR_OK;
}

static Status PrivCheck(DirectAccessRunCtxT *dwBaseRunCtx, CltCataLabelT *cataLabel, GmcOperationTypeE opType)
{
    DwPrivCheckInfoT privInfo = g_gmdbDwExecutor[opType].privInfo;
    DmReusableMetaT *metaInfoAddr = cataLabel->vertexMetaInfo;
    bool isObjPivChanged = IsVertexLabelPrivChanged(cataLabel, dwBaseRunCtx);
    // 对象权限发生变化，或系统权限发生变化
    if (SECUREC_UNLIKELY(isObjPivChanged || dwBaseRunCtx->sysPrivVersion != dwBaseRunCtx->role->sysPrivVersion)) {
        return DirectAccessPrivCheck(
            dwBaseRunCtx, &metaInfoAddr->objPrivilege, CATA_VERTEX_LABEL, privInfo.sysPriv, privInfo.objectPriv);
    }
    return GMERR_OK;
}

static Status PrivCheckForDirectWrite(DwRunCtxT *dwRunCtx, CltCataLabelT *cataLabel, UserInfoT *userInfo)
{
    Status ret = GMERR_OK;
    DmVertexLabelT *vertexLabel = cataLabel->vertexLabel;
    DmReusableMetaT *metaInfoAddr = cataLabel->vertexMetaInfo;
    // 如果上一次权限校验已经通过，且本次操作和上一次操作类型相同，则先看下权限信息是否发生变化，如果未变化，则不重复校验
    // 否则，需要一次完整的权限校验
    if (SECUREC_LIKELY(dwRunCtx->base.lastPrivPass && dwRunCtx->dwArgs.currOpType == dwRunCtx->dwArgs.lastOpType)) {
        ret = PrivCheck(&dwRunCtx->base, cataLabel, dwRunCtx->dwArgs.currOpType);
    } else {
        DwPrivCheckInfoT privInfo = g_gmdbDwExecutor[dwRunCtx->dwArgs.currOpType].privInfo;
        ret = DirectAccessPrivCheck(
            &dwRunCtx->base, &metaInfoAddr->objPrivilege, CATA_VERTEX_LABEL, privInfo.sysPriv, privInfo.objectPriv);
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        dwRunCtx->base.lastPrivPass = false;
        char *labelName = MEMBER_PTR(&vertexLabel->metaCommon, metaName);
        DB_LOG_AND_SET_LASERR(ret, "Insufficient privilege, labelName: %s, user: (%s : %s : %s).", labelName,
            userInfo->userName, userInfo->groupName, userInfo->processName);
        return ret;
    }
    dwRunCtx->base.lastPrivPass = true;
    return GMERR_OK;
}

static void FillDwRunCtx(GmcStmtT *stmt, DwRunCtxT *dwRunCtx)
{
    DB_POINTER2(stmt, dwRunCtx);
    CltOperVertexT *op = CltGetOperationContext(stmt);
    CltCataLabelT *cltCataLabel = op->cltCataLabel;
    dwRunCtx->labelDef.labelType = VERTEX_LABEL;
    dwRunCtx->labelDef.cataLabel = cltCataLabel;
    if (SECUREC_LIKELY(dwRunCtx->dwArgs.currOpType != GMC_OPERATION_BUTT)) {
        dwRunCtx->dwArgs.lastOpType = dwRunCtx->dwArgs.currOpType;
    }
    dwRunCtx->dwArgs.currOpType = stmt->operationType;
    dwRunCtx->dwArgs.vertexArgs.vertex = op->vertex;
    dwRunCtx->dwArgs.vertexArgs.cltCataLabel = cltCataLabel;
    // partitionId在首次获取分区号时设置有效值
    dwRunCtx->dwArgs.vertexArgs.partitionId = DM_MAX_PARTITION_ID;
    dwRunCtx->dwArgs.vertexArgs.hasSubs = cltCataLabel->vertexMetaInfo->subscriptionNum > 0;
    dwRunCtx->ageRowData = (DwSubsRowDataT){0};
    dwRunCtx->subsRowData = (DwSubsRowDataT){0};
    DmVertexLabelT *vertexLabel = cltCataLabel->vertexLabel;
    dwRunCtx->stmgSubData.isStMgLabel = DmIsLabelSupportStatusMerge(vertexLabel);
    if (SECUREC_UNLIKELY(dwRunCtx->stmgSubData.isStMgLabel)) {
        VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
        dwRunCtx->stmgSubData = (DwStMgSubDataT){
            .isStMgLabel = true,
            .vertexLabel = vertexLabel,
            .statusMergeListEntry = commonInfo->statusMergeList,
            .insertNode = DB_INVALID_SHMPTR,
            .updateNode = DB_INVALID_SHMPTR,
            .gcLabelId = DB_INVALID_UINT32,
        };
    }
}

static DmDmlPerfStatT *GetPerfStat(DwRunCtxT *dwRunCtx, GmcOperationTypeE type)
{
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    DmReusableMetaT *metaInfoAddr = cataLabel->vertexMetaInfo;
    switch (type) {
        case GMC_OPERATION_INSERT:
            return &metaInfoAddr->dmlPerfStat[INSERT_TIME_STATIS];
        case GMC_OPERATION_UPDATE:
            return &metaInfoAddr->dmlPerfStat[UPDATE_TIME_STATIS];
        case GMC_OPERATION_REPLACE:
            return &metaInfoAddr->dmlPerfStat[REPLACE_TIME_STATIS];
        case GMC_OPERATION_DELETE:
            return &metaInfoAddr->dmlPerfStat[DELETE_TIME_STATIS];
        case GMC_OPERATION_CHECK_REPLACE:
        default:
            return NULL;
    }
}

static DmDwDmlOperStatT *GetOperationStatForDw(DwRunCtxT *dwRunCtx, GmcOperationTypeE type)
{
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    DmReusableMetaT *metaInfoAddr = cataLabel->vertexMetaInfo;
    switch (type) {
        case GMC_OPERATION_INSERT:
            return &metaInfoAddr->dwDmlOperStat[INSERT_TIME_STATIS];
        case GMC_OPERATION_UPDATE:
            return &metaInfoAddr->dwDmlOperStat[UPDATE_TIME_STATIS];
        case GMC_OPERATION_REPLACE:
            return &metaInfoAddr->dwDmlOperStat[REPLACE_TIME_STATIS];
        case GMC_OPERATION_DELETE:
            return &metaInfoAddr->dwDmlOperStat[DELETE_TIME_STATIS];
        case GMC_OPERATION_CHECK_REPLACE:
        default:
            return NULL;
    }
}

static void UpdateExecuteCountForDwStat(DwRunCtxT *dwRunCtx, Status opStatus, GmcOperationTypeE type)
{
    DB_POINTER(dwRunCtx);
    DmDwDmlOperStatT *operStat = GetOperationStatForDw(dwRunCtx, type);
    if (SECUREC_UNLIKELY(operStat == NULL)) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "dwOperStat null, opType: %" PRIu32 ".", type);
        return;
    }
    if (SECUREC_LIKELY(opStatus == GMERR_OK)) {
        (void)DbAtomicFetchAndAdd64(&operStat->successExecuteCount, 1);
    } else {
        (void)DbAtomicFetchAndAdd64(&operStat->failExecuteCount, 1);
    }
}

static void UpdateDwExecuteTime(GmcStmtT *stmt, GmcOperationTypeE type)
{
    DB_POINTER(stmt);
    DwRunCtxT *dwRunCtx = stmt->dwRunCtx;
    uint64_t currentTime = DbRdtsc();
    uint64_t usedTime = DbToUseconds(currentTime - dwRunCtx->lastStatTime);
    int32_t perfStatVal = dwRunCtx->cfgShmConfig[CLT_CFG_DML_PERF_STAT_IS_ENABLED].value.int32Val;
    if (SECUREC_UNLIKELY(CltCfgIsOpenInt32(perfStatVal))) {
        DmDmlPerfStatT *perfStat = GetPerfStat(dwRunCtx, type);
        if (SECUREC_UNLIKELY(perfStat == NULL)) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "DwperfStat null, opType: %" PRIu32 ".", type);
            return;
        }
        (void)DbAtomicFetchAndAdd64(&perfStat->totalExecuteTime, usedTime);
        if (usedTime > perfStat->maxExecuteTime) {
            DbAtomicSet64(&perfStat->maxExecuteTime, usedTime);
        }
    }

    // 长操alarm
    CltOperVertexT *op = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = op->cltCataLabel->vertexLabel;
    if (SECUREC_UNLIKELY(usedTime > DW_LONG_OPER_TIME_THRESH_US)) {
        char *metaName = MEMBER_PTR(&vertexLabel->metaCommon, metaName);
        DB_LOG_WARN(GMERR_CONFIGURATION_LIMIT_EXCEEDED,
            "Dw long operation, total time: %" PRIu64 " us, opType %" PRIu32 ", labelName %s.", usedTime, type,
            metaName);
    }
}

static Status CopyLowVertex2HighVertexInner(
    GmcStmtT *stmt, CltCataLabelT *maxVersionCltCataLabel, DmVertexLabelT *vertexLabel, CltOperVertexT *op)
{
    DmVertexT *maxVersionVertex;
    DmVertexLabelT *maxVersionVertexLabel = maxVersionCltCataLabel->vertexLabel;
    Status ret = GMERR_OK;
    if (stmt->dwRunCtx->maxVersionVertex == NULL) {
        ret = DmCreateEmptyVertexByDescWithMemCtx(
            stmt->memCtx, maxVersionCltCataLabel->vertexLabel->vertexDesc, &maxVersionVertex);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        maxVersionVertex = stmt->dwRunCtx->maxVersionVertex;
    }
    if (op->vertex->isDeltaVertex) {
        DmClearVertex(maxVersionVertex);
    }
    DB_ASSERT(vertexLabel->metaCommon.version < maxVersionVertexLabel->metaCommon.version);
    uint8_t *vertexBuf = NULL;
    uint32_t vertexBufLength = 0;
    ret = DmSerializeVertex(op->vertex, &vertexBuf, &vertexBufLength);
    if (ret != GMERR_OK) {
        DmDestroyVertex(maxVersionVertex);
        stmt->dwRunCtx->maxVersionVertex = NULL;
        return ret;
    }
    ret = DmDeSerialize2ExistsVertex(vertexBuf, vertexBufLength, maxVersionVertex, false);
    if (ret != GMERR_OK) {
        DmDestroyVertex(maxVersionVertex);
        stmt->dwRunCtx->maxVersionVertex = NULL;
        return ret;
    }
    stmt->dwRunCtx->maxVersionVertex = maxVersionVertex;
    stmt->dwRunCtx->dwArgs.vertexArgs.vertex = maxVersionVertex;
    stmt->dwRunCtx->dwArgs.vertexArgs.cltCataLabel = maxVersionCltCataLabel;
    stmt->dwRunCtx->labelDef.cataLabel = maxVersionCltCataLabel;
    if (DmIsLabelSupportStatusMerge(maxVersionVertexLabel)) {
        stmt->dwRunCtx->stmgSubData = (DwStMgSubDataT){
            .isStMgLabel = true,
            .vertexLabel = maxVersionVertexLabel,
            .statusMergeListEntry = maxVersionCltCataLabel->vertexLabel->commonInfo->statusMergeList,
            .insertNode = DB_INVALID_SHMPTR,
            .updateNode = DB_INVALID_SHMPTR,
            .gcLabelId = DB_INVALID_UINT32,
        };
    }
    return GMERR_OK;
}

static bool NeedUpdDWMaxVersionVertexLabel(GmcStmtT *stmt, DmReusableMetaT *metaInfoAddr)
{
    CltCataLabelT *currMaxVerCltCataLabel = stmt->dwRunCtx->maxVersionCltCataLabel;
    if (SECUREC_UNLIKELY(currMaxVerCltCataLabel == NULL)) {
        return true;
    }
    if (SECUREC_UNLIKELY(currMaxVerCltCataLabel->vertexLabel->metaVertexLabel->isDeletedForClt ||
                         currMaxVerCltCataLabel->vertexLabel->metaCommon.version < metaInfoAddr->schemaMaxVersion)) {
        if (stmt->dwRunCtx->maxVersionVertex != NULL) {
            // maxVersionCltCataLabel发生变化，maxVersionVertex需要destroy再重新申请。
            DmDestroyVertex(stmt->dwRunCtx->maxVersionVertex);
            stmt->dwRunCtx->maxVersionVertex = NULL;
        }
        CltCataCloseVertexLabel(currMaxVerCltCataLabel);
        stmt->dwRunCtx->maxVersionCltCataLabel = NULL;
        return true;
    }
    return false;
}

static Status CopyLowVertex2HighVertex(GmcStmtT *stmt)
{
    Status ret = GMERR_OK;
    CltOperVertexT *op = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = op->cltCataLabel->vertexLabel;
    DmReusableMetaT *metaInfoAddr = op->cltCataLabel->vertexMetaInfo;
    char *metaName = MEMBER_PTR(&vertexLabel->metaCommon, metaName);
    // 如果该表发生过升降级，且当前用户操作的（op）非最高版本，需要将stmt中的vertex序列化、反序列化至最高版本
    if (SECUREC_UNLIKELY(vertexLabel->metaCommon.version < metaInfoAddr->schemaMaxVersion)) {
        // 如果现有的元数据版本落后或者已经失效，则需要花费一次CS通信获取最新版本。
        if (NeedUpdDWMaxVersionVertexLabel(stmt, metaInfoAddr)) {
            // maxVersionCltCataLabel发生变化，已经open的资源需要重新申请
            stmt->dwRunCtx->isMaxVersionChange = true;
            CltCataLabelT *newMaxVerCltCataLabel = NULL;
            ret = GetVertexLabelByNameWithCache(stmt, metaName, DM_SCHEMA_INVALID_VERSION, &newMaxVerCltCataLabel);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "Get vl %s from server for dw upd.", metaName);
                return ret;
            }
            stmt->dwRunCtx->maxVersionCltCataLabel = newMaxVerCltCataLabel;
        }
        ret = CopyLowVertex2HighVertexInner(stmt, stmt->dwRunCtx->maxVersionCltCataLabel, vertexLabel, op);
        if (ret != GMERR_OK) {
            CltCataCloseVertexLabel(stmt->dwRunCtx->maxVersionCltCataLabel);
            stmt->dwRunCtx->maxVersionCltCataLabel = NULL;
            return ret;
        }
    }
    return GMERR_OK;
}

// 校验客户端元数据的版本
static Status LabelVersionCheck(GmcStmtT *stmt)
{
    CltOperVertexT *op = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = op->cltCataLabel->vertexLabel;
    DmReusableMetaT *metaInfoAddr = op->cltCataLabel->vertexMetaInfo;
    char *metaName = MEMBER_PTR(&vertexLabel->metaCommon, metaName);
    // 降级场景，报错
    bool isValid = false;
    Status ret = DmIsSchemaVersionValid(vertexLabel, vertexLabel->metaVertexLabel->uuid, &isValid);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Schema version inv, labelName: %s.", metaName);
        return ret;
    }
    if (SECUREC_UNLIKELY(!isValid || vertexLabel->metaCommon.version > metaInfoAddr->schemaMaxVersion)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_UNDEFINED_TABLE, "Version inv, vertexlabel has been degraded, labelName: %s.", metaName);
        return GMERR_UNDEFINED_TABLE;
    }
    return GMERR_OK;
}

static bool CheckNonSuitableScene(CltCataLabelT *cltCataLabel)
{
    DmVertexLabelT *vertexLabel = cltCataLabel->vertexLabel;
    if (vertexLabel->metaVertexLabel->labelLevel != VERTEX_LEVEL_SIMPLE) {
        return false;
    }
    // 判断主键索引字段全定长，聚簇容器主键为定长字段，heap容器主键需判断
    DmVlIndexLabelT *pkIndex = MEMBER_PTR(vertexLabel->metaVertexLabel, pkIndex);
    if (pkIndex != NULL && pkIndex->propeNum != pkIndex->fixedPropeNum) {
        return false;
    }

    // 资源列
    DmResColInfoT *resColInfo = MEMBER_PTR(vertexLabel->commonInfo, resColInfo);
    if (resColInfo != NULL) {
        return false;
    }

    // 分区表
    if (cltCataLabel->vertexAccCheck->isPartition) {
        return false;
    }

    // bitMap
    if (vertexLabel->vertexDesc->hasBitMapProp) {
        return false;
    }

    // 自增列
    if (vertexLabel->commonInfo->autoIncrPropNum > 0) {
        return false;
    }

    // 建边
    if (vertexLabel->commonInfo->edgeLabelNum != 0) {
        return false;
    }

    // 升降级
    if (vertexLabel->commonInfo->hasUpd) {
        return false;
    }

    return true;
}

static Status PrepareForStructWrite(GmcStmtT *stmt, DwRunCtxT *dwRunCtx, CltCataLabelT *cltCataLabel)
{
    bool compatibleV3 = CltCfgIsOpenInt32(dwRunCtx->cfgShmConfig[CLT_CFG_COMPATIBLE_V3].value.int32Val);
    // 前面已校验 vertexLabel 不是一般复杂表，此处不再校验
    // 和服务端 QryCheckReplaceVertexAndGetKeyBuf() 保持一致！
    dwRunCtx->useStructSeriBuf = compatibleV3 && stmt->operationType == GMC_OPERATION_REPLACE &&
                                 (dwRunCtx->ready4StructWrite || CheckNonSuitableScene(cltCataLabel)) &&
                                 !cltCataLabel->vertexLabel->commonInfo->hasUpd;
    if (SECUREC_LIKELY(dwRunCtx->useStructSeriBuf)) {
        dwRunCtx->ready4StructWrite = true;
        (dwRunCtx->dwArgs).vertexArgs.vertexBuf.bufSize = dwRunCtx->structWriteBufLen;
        (dwRunCtx->dwArgs).vertexArgs.vertexBuf.buf = dwRunCtx->structWriteBuf;
    } else {
        dwRunCtx->ready4StructWrite = false;
        Status ret = DmDeSerialize2ExistsVertex(dwRunCtx->structWriteBuf, dwRunCtx->structWriteBufLen,
            (dwRunCtx->dwArgs).vertexArgs.vertex, CltGetSecurityMode());
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_AND_SET_LASERR(ret, "Dw deseri vertex.");
            return ret;
        }
    }
    return GMERR_OK;
}

Status DwSetAutoGeneratedProperty(DwRunCtxT *dwRunCtx)
{
    DmVertexLabelT *vertexLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel->vertexLabel;
    const char *labelName = MEMBER_PTR(vertexLabel, metaCommon.metaName);
    DmVertexT *vertex = dwRunCtx->dwArgs.vertexArgs.vertex;

    DmGetBitMapPropInfo(vertex);
    Status ret = DmCheckBitMapConstrict(vertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Constraint not satisfy, vl:%s.", labelName);
        return ret;
    }

    ret = DmVertexSetAutoIncProp(vertex, vertexLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Dw set AutoIncProp, vl:%s", labelName);
        return ret;
    }

    return DmVertexSetResColPrope2Zero(vertex);
}

void ResetHeapCtx(DwRunCtxT *dwRunCtx)
{
    DB_POINTER(dwRunCtx);
    if (dwRunCtx->base.containerType == CONTAINER_CLUSTERED_HASH) {
        if (dwRunCtx->base.chRunCtx != NULL) {
            ContainerResetCtx((ContainerHdlT)dwRunCtx->base.chRunCtx);
            ContainerClearTupleBuf((ContainerHdlT)dwRunCtx->base.chRunCtx, NULL);
        }
    } else if (dwRunCtx->base.containerType == CONTAINER_HEAP && dwRunCtx->base.heapRunCtx != NULL) {
        ContainerResetCtx((ContainerHdlT)dwRunCtx->base.heapRunCtx);
    }
#ifdef ART_CONTAINER
    else if (dwRunCtx->base.containerType == CONTAINER_ART && dwRunCtx->base.artRunCtx != NULL) {
        ContainerResetCtx((ContainerHdlT)dwRunCtx->base.artRunCtx);
        ContainerClearTupleBuf((ContainerHdlT)dwRunCtx->base.artRunCtx, NULL);
    }
#endif
}

void ResetHeapIdxCtx(DwRunCtxT *dwRunCtx)
{
    IndexCtxT *idxCtx = NULL;
    if (dwRunCtx->base.pkIndexRunCtx != NULL) {
        idxCtx = (IndexCtxT *)dwRunCtx->base.pkIndexRunCtx;
        TupleBufReset(&idxCtx->tupleBuf);
    }
    if (dwRunCtx->secIdxCtx != NULL) {
        for (uint32_t i = 0; i < dwRunCtx->secIdxNum; i++) {
            idxCtx = (IndexCtxT *)dwRunCtx->secIdxCtx[i];
            if (idxCtx == NULL) {
                continue;
            }
            TupleBufReset(&idxCtx->tupleBuf);
        }
    }
}

void ResetChIdxCtx(DwRunCtxT *dwRunCtx)
{
    IndexCtxT *idxCtx = NULL;
    if (dwRunCtx->base.pkIndexRunCtx != NULL) {
        idxCtx = (IndexCtxT *)dwRunCtx->base.pkIndexRunCtx;
        TupleBufReset(&idxCtx->tupleBuf);
    }
    if (dwRunCtx->secIdxCtx != NULL) {
        for (uint32_t i = 0; i < dwRunCtx->secIdxNum; i++) {
            idxCtx = (IndexCtxT *)dwRunCtx->secIdxCtx[i];
            if (idxCtx == NULL) {
                continue;
            }
            TupleBufReset(&idxCtx->tupleBuf);
        }
    }
}

void ResetAllIdxCtxs(DwRunCtxT *dwRunCtx)
{
    if (dwRunCtx->base.isUseClusteredHashTable) {
        ResetChIdxCtx(dwRunCtx);
    } else {
        ResetHeapIdxCtx(dwRunCtx);
    }
    TupleBufReleaseBigBuf(&dwRunCtx->oldTupleBuf, MAX_CLT_HOLD_HEAP_TUPLEBUF_SIZE);
}

void DwResetDwRunCtx4Stmt(DwRunCtxT *dwRunCtx, DbMemCtxT *memCtx)
{
    DB_POINTER2(dwRunCtx, memCtx);
    ResetAllIdxCtxs(dwRunCtx);
    ResetHeapCtx(dwRunCtx);
    TupleBufReset(&dwRunCtx->oldTupleBuf);
}

#ifdef FEATURE_RSMEM
Status IsSupportDmlWithRsm(GmcConnT *conn)
{
    uint8_t rsmFlag;
    Status ret = DbCltIsSerInRsmRecovery(conn, &rsmFlag);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (rsmFlag == true) {
        DB_LOG_ERROR(GMERR_UNDEFINED_TABLE, "Db is rebooting, can not operate vertex.");
        return GMERR_UNDEFINED_TABLE;
    }
    return GMERR_OK;
}
#endif

static Status ProcessExecuteInner(GmcStmtT *stmt)
{
    Status ret = GMERR_OK;
    DwRunCtxT *dwRunCtx = stmt->dwRunCtx;
    UserInfoT *userInfo = stmt->conn->dwUserInfo;
    CltOperVertexT *op = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = op->cltCataLabel->vertexLabel;
    ConcurrencyControlE ccType = vertexLabel->commonInfo->heapInfo.ccType;
    DwExecHandlerT *handler = &g_gmdbDwExecutor[stmt->operationType];
    FillDwRunCtx(stmt, dwRunCtx);  // fill args

    if (SECUREC_UNLIKELY((ret = PrivCheckForDirectWrite(dwRunCtx, op->cltCataLabel, userInfo)) != GMERR_OK)) {
        return ret;  // 日志信息已经在内层打印
    }
    if (dwRunCtx->dwArgs.vertexArgs.hasSubs) {  // 性能优化，只有订阅场景才涉及流控
        ret = FlowControlDirectWriteStartTrx(&stmt->conn->flowControl);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
#ifdef FEATURE_RSMEM
    ret = IsSupportDmlWithRsm(stmt->conn);
    if (ret != GMERR_OK) {
        return ret;
    }
#endif

    if (SECUREC_UNLIKELY((ret = DwBeginTrx(dwRunCtx, stmt->conn->remoteId, ccType)) != GMERR_OK)) {
        return ret;
    }
    CRASHPOINT(DB_CRASH_DIRECT_WRITE, DB_CRASH_STATUS_DW_TRX_BEGIN);

    if (SECUREC_UNLIKELY(vertexLabel->commonInfo->hasUpd)) {
        dwRunCtx->ready4StructWrite = false;
        if ((ret = LabelVersionCheck(stmt)) != GMERR_OK) {
            goto EXIT;  // 校验版本失败，回滚
        }
        // 升级场景，通过一次cs通信获取最高版本并进行资源释放
        if ((ret = CopyLowVertex2HighVertex(stmt)) != GMERR_OK) {
            goto EXIT;  // copy版本失败，回滚
        }
    }
    if (SECUREC_LIKELY(stmt->isStructure)) {
        if ((ret = PrepareForStructWrite(stmt, dwRunCtx, op->cltCataLabel)) != GMERR_OK) {
            goto EXIT;
        }
    }
    ret = handler->execFunc(stmt);
    CRASHPOINT(DB_CRASH_DIRECT_WRITE, DB_CRASH_STATUS_DW_TRX_END);

EXIT:
    ret = DwEndTrx(stmt, dwRunCtx, ret, ccType);
    return ret;
}

static Status DwProcessExecuteShmProcess(void *args)
{
    GmcStmtT *stmt = (GmcStmtT *)args;
    DwRunCtxT *dwRunCtx = stmt->dwRunCtx;
    dwRunCtx->lastStatTime = DbRdtsc();

    Status ret = ProcessExecuteInner(stmt);

    int32_t operStatVal = stmt->dwRunCtx->cfgShmConfig[CLT_CFG_DML_OPER_STAT_IS_ENABLED].value.int32Val;
    int32_t auditLogVal = stmt->dwRunCtx->cfgShmConfig[CLT_CFG_AUDIT_LOG_DML_ENABLE].value.int32Val;
    // 更新dml操作统计信息
    if (SECUREC_LIKELY(CltCfgIsOpenInt32(operStatVal))) {
        UpdateExecuteCountForDwStat(stmt->dwRunCtx, ret, stmt->operationType);
    }
    // 更新直连写执行时间信息
    if (SECUREC_LIKELY(ret == GMERR_OK)) {
        UpdateDwExecuteTime(stmt, stmt->operationType);
    }
    // 性能优化，只有订阅场景才更新流控信息
    if (dwRunCtx->dwArgs.vertexArgs.hasSubs) {
        GmcConnT *conn = stmt->conn;
        FlowControlWhenRecvResponse(&conn->flowControl, (GmcDbFlowCtrlLevelE)(conn->dwPubSubInfo->flowCtrlLevel));
    }

    // 更新审计日志
    if (SECUREC_UNLIKELY(CltCfgIsOpenInt32(auditLogVal))) {
        DwAuditLog(stmt, ret);
    }
    bool useStructSeriBuf = dwRunCtx->useStructSeriBuf;
    dwRunCtx->useStructSeriBuf = false;
    stmt->lastOpStatus.lastOpType = stmt->operationType;
    stmt->lastOpStatus.lastOpDirectWrite = true;
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // 直连写执行失败，在调用栈最外层打印表名和操作类型。这里不能使用last error，否则会覆盖last error信息
        CltOperVertexT *op = CltGetOperationContext(stmt);
        DmMetaCommonT *metaCommon = &op->cltCataLabel->vertexLabel->metaCommon;
        char *labelName = MEMBER_PTR(metaCommon, metaName);
        DB_LOG_ERROR(ret, "Dw execute unsuss, labelName: %s, opType: %" PRIi32 ".", labelName, stmt->operationType);
        return ret;
    }
    stmt->lastOpStatus.noNeedResetVertex = useStructSeriBuf;
    return GMERR_OK;
}

Status DwProcessExecute(GmcStmtT *stmt)
{
#ifdef ART_CONTAINER
    return CltProtectShmProcess(DwProcessExecuteShmProcess, (void *)stmt, SHMEM_OP_DIRECT_WRITE);
#else
    Status ret = TryAcqDwEntryToken(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Unable to acquire dw entry token.");
        return ret;
    }
    ret = CltProtectShmProcess(DwProcessExecuteShmProcess, (void *)stmt, SHMEM_OP_DIRECT_WRITE);
    ClearDwEntryToken(stmt);
    return ret;
#endif
}
