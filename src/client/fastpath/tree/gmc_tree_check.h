/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_tree_check.h
 * Description: header file for GMDB client gmc tree check
 * Create: 2021-9-02
 */

#ifndef GMC_TREE_CHECK_H
#define GMC_TREE_CHECK_H

#include "gmc_types.h"
#include "clt_check.h"
#include "gmc_graph_check.h"

#ifdef __cplusplus
extern "C" {
#endif

static Status GmcVertexJsonBasicCheck(GmcStmtT *stmt, const void *json)
{
    Status ret = CltCheckStmt4GetVertex(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltPtrCheckWithErr(json);
}

inline static Status GmcSetVertexByJsonCheck(GmcStmtT *stmt, const char *json)
{
    return GmcVertexJsonBasicCheck(stmt, json);
}

inline static Status GmcDumpVertexToJsonCheck(GmcStmtT *stmt, char *const *json)
{
    return GmcVertexJsonBasicCheck(stmt, json);
}

inline static Status GmcGetRootNodeCheck(GmcStmtT *stmt, GmcNodeT *const *node)
{
    Status ret = CltCheckStmt4GetVertex(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return CltPtrCheckWithErr(node);
}

inline static Status GmcGetChildNodeCheck(const GmcStmtT *stmt, const char *path, GmcNodeT *const *node)
{
    Status ret = CltCheckStmt4GetVertex(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return CltPtr2CheckWithErr(path, node);
}

inline static Status GmcNodeAllocKeyCheck(const GmcNodeT *node, const char *name, GmcIndexKeyT **key)
{
    Status ret = CltPtr3CheckWithErr(node, name, key);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(name, DM_MAX_NAME_LENGTH, "nodeName");
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

inline static Status GmcNodeGetChildCheck(const GmcNodeT *node, const char *name, GmcNodeT **child)
{
    Status ret = CltPtr3CheckWithErr(node, name, child);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(name, DM_MAX_NAME_LENGTH, "nodeName");
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif

#endif
