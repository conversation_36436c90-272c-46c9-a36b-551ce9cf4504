/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: clt_tree_dml.c
 * Description: Implement of GMDB client tree node dml API
 * Author: maxiaodi
 * Create: 2020-12-21
 */
#include "clt_tree_dml.h"

#include "gmc_internal.h"
#include "clt_graph_vertex_struct.h"
#include "clt_yang.h"
#include "clt_subtree_edit.h"

Status CltSwitchToElement(const GmcNodeT *node)
{
    DB_ASSERT(node->dmVertex == NULL);
    DmNodeT *dmNode = node->dmNode;
    if (dmNode->nodeDesc->nodeType == DM_NODE_RECORD && node->key == NULL && node->index == 0) {
        // 当前node只有一个element，且当前index为合法值，不切换
        return GMERR_OK;
    }

    if (DmNodeGetNodeType(dmNode) != DM_NODE_VECTOR ||  // 非VECTOR不涉及增量操作
        node->type != DELTA_UPDATE_NODE                 // 非增量操作
    ) {
        DB_ASSERT(node->key == NULL);  // for non delta node key should be converted to index
        return DmNodeSetElementIndex(dmNode, node->index);
    }

    if (node->key) {
        // 增量模式，且使用key选择元素
        return DmDeltaVectorUpdateByMemberKey(dmNode, node->key);
    }

    if ((node->index & IS_DELTA_APPEND_INDEX) == 0) {
        // 增量模式，且使用下标选择元素
        return DmDeltaVectorUpdate(dmNode, (uint16_t)node->index);
    }

    // 受DM的增量更新实现限制，使用GmcNodeGetNextElement获取增量append元素的下一个的元素时
    // 可能会获取到其他增量操作码对应的记录。用户需要保证使用方式正确，此处不用限制
    return DmNodeSetElementIndex(dmNode, node->index & ~IS_DELTA_APPEND_INDEX);
}

static int MatchNameWithPath(const char *name1, const char *name2)
{
    DB_ASSERT(name1 != name2);
    const char *name = name1;  // 以'\0'结尾
    const char *path = name2;  // 以NAME_PATH_SPLITTER结尾

    while (*name == *path) {
        // 调用者需要保证name不包括'.'
        DB_ASSERT(*name != NAME_PATH_SPLITTER);
        ++name, ++path;
    }

    // 表达式值为0表示匹配成功（与strcmp保持一致）
    return (*name != '\0') || (*path != NAME_PATH_SPLITTER);
}

Status CltGetNodeFromVertex(DmVertexT *vertex, const char *namepath, DmNodeT **node)
{
    const char *path = namepath;
    DmNodeT **children = vertex->nodes;
    DmNodeDescT **nodeDescs = vertex->vertexDesc->nodeDescs;
    uint32_t count = vertex->vertexDesc->nodeNum;

    for (int ch, (*cmp)(const char *, const char *) = MatchNameWithPath;;) {
        // skip current name
        const char *name = path;
        do {
            ch = *path, ++path;
            if (ch == '\0') {
                cmp = strcmp;
                break;
            }
        } while (ch != NAME_PATH_SPLITTER);

        DmNodeT *current = NULL;
        do {
            if (count == 0) {
                // node with given name not found
                return GMERR_INVALID_NAME;
            }
            --count;  // check next child
            current = children[count];
            if (current == NULL) {
                Status ret = DmCreateEmptyNode(vertex->memCtx, nodeDescs[count], &children[count], true);
                if (ret != GMERR_OK) {
                    return ret;
                }
                current = children[count];
            }
        } while (cmp(DmNodeGetName(current), name) != 0);

        // mark created (for V3 compatibility)
        current->isCreated = true;

        if (ch == '\0') {
            // reached end of path
            *node = current;
            return GMERR_OK;
        }

        // for delta vector, implicitly select (update) element 0 to proceed to next level
        if (SECUREC_UNLIKELY(DmNodeGetNodeType(current) != DM_NODE_RECORD)) {
            bool delta = (DmNodeGetNodeType(current) == DM_NODE_VECTOR && vertex->isDeltaVertex);
            Status ret = delta ? DmDeltaVectorUpdate(current, 0) : DmNodeSetElementIndex(current, 0);
            if (ret != GMERR_OK) {
                return ret;
            }
        }

        // prepare for next round
        children = current->currNodes;
        nodeDescs = current->nodeDesc->childNodeDescs;
        count = DmNodeGetPerElementNodeNum(current);
    }
}

Status CltGetRootNode(GmcStmtT *stmt, GmcNodeT *node)
{
    if (stmt->fetchEof && ((stmt->stmtType == CLT_STMT_TYPE_VERTEX && stmt->operationType == GMC_OPERATION_SCAN) ||
                              stmt->stmtType == CLT_STMT_TYPE_SUB_VERTEX)) {
        return GMERR_NO_DATA;
    }

    if (stmt->stmtType == CLT_STMT_TYPE_SUB_VERTEX) {
        SubPushSingleLabel *sub = CltGetOperationContext(stmt);
        node->dmVertex = sub->vertex;
        node->type = READ_ONLY_NODE;
    } else {
        Status ret = CltCheckAndDeseriData(stmt);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        node->dmVertex = CltGetVertexInStmt(stmt);
        if (node->dmVertex == NULL) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "dmVertex is null");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        if (stmt->operationType == GMC_OPERATION_SUBTREE_FILTER) {
#ifdef FEATURE_YANG
            ret = CltPrepareSubtree(stmt, node);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
#else
            DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Subtree filter without FEATURE_YANG.");
            return GMERR_FEATURE_NOT_SUPPORTED;
#endif
        }

        if (stmt->operationType == GMC_OPERATION_SCAN) {
            node->type = READ_ONLY_NODE;
        } else if (node->dmVertex->isDeltaVertex) {
            node->type = DELTA_UPDATE_NODE;
        } else {
            node->type = READ_WRITE_NODE;
        }
    }

    node->operationType = stmt->operationType;
    node->stmt = stmt;
    node->memCtx = stmt->opCtx;
    return GMERR_OK;
};

/***************************************************************************/
/*                        下面是ORM兼容接口的实现                            */
/***************************************************************************/

typedef union {
    DmNodeT *node;
    uint32_t subscript;
} PathNodeT;

static bool LookupNodeByNameNodeDesc(
    const DmNodeDescT *nodeDesc, const char *name, uint32_t level, PathNodeT path[DB_SCHEMA_MAX_DEPTH], uint32_t *depth)
{
    DmNodeDescT **const children = nodeDesc->childNodeDescs;
    const uint32_t count = nodeDesc->nodeNumPerElement;
    uint32_t nameLen = (uint32_t)strlen(name);
    for (uint32_t subscript = 0; subscript < count; ++subscript) {
        // 为了明显区别于id，这里使用subscript命名循环变量，表示子节点的相对顺序
        DB_ASSERT(level < DB_SCHEMA_MAX_DEPTH);

        // 如果找到了节点，记录下总的深度并返回
        DmNodeDescT *childNode = children[subscript];
        if (nameLen == childNode->nameLen - 1 && strcmp(childNode->name, name) == 0) {
            *depth = level;
            path[level].subscript = subscript;  // 记录下当前节点的下标
            return true;
        }

        // 如果在下一层找到了目标节点，直接返回
        if (LookupNodeByNameNodeDesc(childNode, name, level + 1, path, depth)) {
            path[level].subscript = subscript;  // 记录下当前节点的下标
            return true;
        }
    }

    return false;
}

static bool LookupNodeByNameNodeDescForVertex(
    DmVertexDescT *vertexDesc, const char *name, PathNodeT path[DB_SCHEMA_MAX_DEPTH], uint32_t *depth)
{
    const uint32_t level = 0;
    DmNodeDescT **nodeDescs = vertexDesc->nodeDescs;
    const uint32_t count = vertexDesc->nodeNum;
    uint32_t nameLen = (uint32_t)strlen(name);
    for (uint32_t subscript = 0; subscript < count; ++subscript) {
        // 为了明显区别于id，这里使用subscript命名循环变量，表示子节点的相对顺序
        // 如果找到了节点，记录下总的深度并返回
        const DmNodeDescT *child = nodeDescs[subscript];
        if (nameLen == child->nameLen - 1 && strcmp(child->name, name) == 0) {
            path[level].subscript = subscript;  // 记录下当前节点的下标
            *depth = level;
            return true;
        }

        // 如果在下一层找到了目标节点，直接返回
        if (LookupNodeByNameNodeDesc(child, name, level + 1, path, depth)) {
            path[level].subscript = subscript;  // 记录下当前节点的下标
            return true;
        }
    }

    return false;
}

static Status GetPathToNodeByName(const GmcNodeT *root, const char *name,  // input
    PathNodeT path[DB_SCHEMA_MAX_DEPTH], uint32_t *depth, DmNodeT ***children)
{
    DmNodeT **nodes;

    if (root->dmVertex) {
        DmVertexDescT *vertexDesc = root->dmVertex->vertexDesc;
        if (!LookupNodeByNameNodeDescForVertex(vertexDesc, name, path, depth)) {
            // 从名字找不到对应的子节点
            return GMERR_INVALID_NAME;
        }
        nodes = root->dmVertex->nodes;
    } else {
        DmNodeT *node = root->dmNode;
        DB_ASSERT(node->isCreated);
        Status ret = CltSwitchToElement(root);
        if (ret != GMERR_OK) {
            return ret;
        }
        DB_ASSERT(node->realElementNum > 0);
        nodes = node->currNodes;
        if (!LookupNodeByNameNodeDesc(node->nodeDesc, name, 0, path, depth)) {
            // 从名字找不到对应的子节点
            return GMERR_INVALID_NAME;
        }
    }

    // 记录第一层子节点用于遍历，
    // 这样能屏蔽第一层是vertex还是node的差异
    *children = nodes;
    return GMERR_OK;
}

static Status GetIndexLabelById(DmVertexLabelT *vertexLabel, char *name, uint32_t keyId, DmVlIndexLabelT **indexLabel)
{
    DmNodeSchemaT *nodeSchema = NULL;
    DmSchemaT *schema = MEMBER_PTR(vertexLabel->metaVertexLabel, schema);
    Status ret = DmSchemaGetNodeByNameDFS(schema, name, &nodeSchema);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmVlIndexLabelT *memberKeys = MEMBER_PTR(nodeSchema, memberKeys);
    *indexLabel = &memberKeys[keyId];
    return GMERR_OK;
}

static Status LookupElementWithKeyBuf(GmcNodeT *array, uint32_t keyId, GmcSeriT *keyBuf, bool rememberPosition)
{
    DB_ASSERT(keyBuf != NULL);

    DmNodeT *node = array->dmNode;
    if (keyId >= DmNodeGetMemberKeyNum(node)) {
        // 从keyId找不到对应的key
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    DmVertexLabelT *vertexLabel = CltGetCltVertexLabel(array->stmt)->vertexLabel;
    DmVlIndexLabelT *indexLabel = NULL;
    Status ret = GetIndexLabelById(vertexLabel, array->dmNode->nodeDesc->name, keyId, &indexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmIndexKeyT *indexKey = NULL;
    if (DmCreateEmptyIndexKeyByIdxLabelWithMemCtx(array->memCtx, indexLabel, &indexKey) != GMERR_OK) {
        return ret;
    }
    if (keyBuf->bufSize > indexKey->maxKeyLen) {
        ret = GMERR_FIELD_OVERFLOW;  // 传入的key太大
        goto EXIT;
    }

    indexKey->keyBufLen = keyBuf->bufSize;
    GmcStructureResvT reservedSize = {1, 1};
    ret = keyBuf->seriFunc(keyBuf, indexKey->keyBuf, &reservedSize);
    if (ret != GMERR_OK) {
        // 用户函数返回失败
        goto EXIT;
    }

    if (array->type != DELTA_UPDATE_NODE || DmNodeGetNodeType(node) != DM_NODE_VECTOR) {
        uint32_t index;
        if (array->stmt->useNewDeseri) {
            ret = DmNodeLookupFromRecordBuf(node, indexKey, &index);
        } else {
            ret = DmNodeLookup(node, indexKey, &index);
        }
        if (rememberPosition && ret == GMERR_OK) {
            // 如果成功，记录元素的下标
            array->index = index;
        }
        goto EXIT;
    }

    ret = DmDeltaVectorUpdateByMemberKey(node, indexKey);
    if (rememberPosition && ret == GMERR_OK) {
        // 如果成功，记录key并直接返回，不释放
        array->key = indexKey;
        return GMERR_OK;
    }

EXIT:
    DmDestroyIndexKey(indexKey);
    return ret;
}

static Status HandleMidwayNode(DmNodeT *current, uint32_t keyId, GmcSeriT *keyBuf, GmcNodeT *context)
{
    if (DmNodeGetNodeType(current) == DM_NODE_RECORD) {
        // 非数组节点，直接继续下一层
        return GMERR_OK;
    }

    if (context->dmNode != NULL || keyBuf == NULL) {
        // key已经使用过，或没有传key
        // 无法解决冲突，返回失败
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    // 使用key选择一个元素来解决名称冲突
    context->dmNode = current;
    return LookupElementWithKeyBuf(context, keyId, keyBuf, false);
}

static Status HandleTerminalNode(DmNodeT *current, uint32_t keyId, GmcSeriT *keyBuf, GmcNodeT *context)
{
    if (context->dmNode != NULL || keyBuf == NULL) {
        // key已经使用过，或没有传key
        // 如果当前节点是数组类节点，认为是返回节点本身
        context->dmNode = current;
        // 外部保证初始值正确，这里不再赋值
        DB_ASSERT(context->index == 0);
        DB_ASSERT(context->key == NULL);
        return GMERR_OK;
    }

    if (DmNodeGetNodeType(current) == DM_NODE_RECORD) {
        // 需要通过key选择元素，但是目标节点不是数组类节点
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    // 使用key选择目标节点中的一个元素
    context->dmNode = current;
    return LookupElementWithKeyBuf(context, keyId, keyBuf, true);
}

Status CltGetNodeWithKeyBuf(GmcNodeT *root, const char *name, uint32_t keyId, GmcSeriT *keyBuf, GmcNodeT **node)
{
    PathNodeT path[DB_SCHEMA_MAX_DEPTH];
    uint32_t depth = 0;
    DmNodeT *current = NULL;
    DmNodeT **children;
    Status ret = GetPathToNodeByName(root, name, path, &depth, &children);
    if (ret != GMERR_OK) {
        return ret;
    }

    GmcNodeT result = CltMakeEmptyCopyNode(root);
    PathNodeT *level = path;
    PathNodeT *const end = level + depth;
    const bool ro = (result.type == READ_ONLY_NODE);
    while (1) {
        current = children[level->subscript];
        if (ro && (!DmNodeIsCreated(current) || current->realElementNum == 0)) {
            // 如果是只读操作，路径上节点不存在或者为空时，认为目标节点不存在，返回失败
            return GMERR_NO_DATA;
        }
        if (level == end) {
            if ((ret = HandleTerminalNode(current, keyId, keyBuf, &result)) != GMERR_OK) {
                return ret;
            }
            break;  // 到达了目标节点，结束循环
        }
        if ((ret = HandleMidwayNode(current, keyId, keyBuf, &result)) != GMERR_OK) {
            return ret;
        }
        level->node = current;  // 记录路径上的节点供后续“创建”节点
        ++level;                // 继续下一层的查找
        uint32_t nodeNum = DmNodeGetPerElementNodeNum(current);
        DB_ASSERT(level->subscript < nodeNum);
        DB_ASSERT(current->realElementNum > 0);
        children = current->currNodes;
    }

    // 先复制结果到外部，再“创建”路径上的所有节点
    if (SECUREC_UNLIKELY((ret = CltReturnLocalNode(&result, node)) != GMERR_OK)) {
        return ret;
    }
    DbLinkedListAppend(&root->childListNode, &((*node)->childListNode));
    // 如果遇到一个已创建的节点，那么父节点肯定也已经创建，直接退出循环
    while (!current->isCreated) {
        current->isCreated = true;
        if (level-- == path) {
            // 已经处理完了路径上的所有节点
            break;
        }
        current = level->node;
    }

    return GMERR_OK;
}
