/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: clt_tree_dml.c
 * Description: Implement of GMDB client tree node dml API
 * Author: maxiaodi
 * Create: 2020-12-21
 */

#ifndef CLT_TREE_DML_H
#define CLT_TREE_DML_H

#include "clt_proto.h"
#include "clt_stmt.h"
#include "clt_utils.h"
#include "dm_meta_schema.h"
#include "gmc_graph_check.h"

#ifdef __cplusplus
extern "C" {
#endif

#define IS_DELTA_APPEND_INDEX 0x80000000

Status CltGetNodeWithKeyBuf(GmcNodeT *root, const char *name, uint32_t keyId, GmcSeriT *keyBuf, GmcNodeT **node);
Status CltGetNodeFromVertex(DmVertexT *vertex, const char *namepath, DmNodeT **node);
Status CltSwitchToElement(const GmcNodeT *node);
Status CltGetRootNode(GmcStmtT *stmt, GmcNodeT *node);

static inline bool CltIsRootNode(const GmcNodeT *handle)
{
    const DmVertexT *vertex = handle->dmVertex;

    // 不能同时为false或者同时为true，为避免warning此处不用异或操作
    DB_ASSERT((vertex != NULL && handle->dmNode == NULL) || (vertex == NULL && handle->dmNode != NULL));

    return (vertex != NULL);
}

static inline Status CltReturnLocalNodeReuse(const GmcNodeT *localNode, GmcNodeT **copyNode)
{
    // 直接复用copyNode的内存
    // 此处memCtx是stmt的opCtx，此处内存用以保存GmcNodeT句柄
    // 在用户调用GmcPrepareStmtByLabelName/GmcPrepareStmtByLabelNameWithVersion
    // GmcResetStmt、GmcFreeStmt等函数时，会重置或删除opCtx
    // 同时支持首次申请的Node句柄进行级联释放，使用GmcFreeNode接口

    // 如果tree不为null，说明是yangNode，不支持复用
    if (SECUREC_UNLIKELY((*copyNode)->tree != NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "for yang node.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    // 需要释放掉key的内存
    DmDestroyIndexKey((*copyNode)->key);
    // 使用新的localNode做值拷贝，同时保留其链表的结构
    TagLinkedListT childListNode = (*copyNode)->childListNode;
    **copyNode = *localNode;
    GmcNodeT *tmpCopyNode = *copyNode;
    tmpCopyNode->childListNode = childListNode;
    tmpCopyNode->magic = CLT_NODE_MAGIC_WORD;
    CltOperVertexT *operVertex = CltGetOperationContext(localNode->stmt);
    tmpCopyNode->vertexLabel = (void *)(operVertex->cltCataLabel->vertexLabel);
    return GMERR_OK;
}

static inline Status CltReturnLocalNode(const GmcNodeT *localNode, GmcNodeT **copyNode)
{
    // 此处memCtx是stmt的opCtx，此处内存用以保存GmcNodeT句柄
    // 在用户调用GmcPrepareStmtByLabelName/GmcPrepareStmtByLabelNameWithVersion
    // GmcResetStmt、GmcFreeStmt等函数时，会重置或删除opCtx
    // 同时支持首次申请的Node句柄进行级联释放，使用GmcFreeNode接口

    GmcNodeT *node = DbDynMemCtxAlloc(localNode->memCtx, sizeof(GmcNodeT));

    if (SECUREC_LIKELY(node != NULL)) {
        *node = *localNode;
        *copyNode = node;
        (*copyNode)->magic = CLT_NODE_MAGIC_WORD;
        CltOperVertexT *operVertex = CltGetOperationContext(node->stmt);
        (*copyNode)->vertexLabel = (void *)(operVertex->cltCataLabel->vertexLabel);
        return GMERR_OK;
    }

    DmDestroyIndexKey(localNode->key);
    DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc node blunder.");
    return GMERR_OUT_OF_MEMORY;
}

static inline GmcNodeT CltMakeEmptyCopyNode(const GmcNodeT *node)
{
    // only keep basic fields
    GmcNodeT localNode = {0};
    localNode.stmt = node->stmt;
    localNode.memCtx = node->memCtx;
    localNode.type = node->type;
    return localNode;
}

static inline Status CltNodeStatusCheck(const GmcNodeT *node)
{
    if (SECUREC_UNLIKELY(node->magic != CLT_NODE_MAGIC_WORD)) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "node double freed.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    Status ret = CltCheckStmt4GetVertex(node->stmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif

#endif /* CLT_TREE_DML_H */
