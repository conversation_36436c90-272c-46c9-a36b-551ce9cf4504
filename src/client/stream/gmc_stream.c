/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author:
 * Create: 2024-12-25
 */
#include "gmc_stream.h"

#include "db_stream_msg_pool.h"
#include "adpt_define.h"
#include "clt_error.h"
#include "db_mem_context_define.h"
#include "db_mem_context.h"
#include "db_memcpy.h"

static DB_THREAD_LOCAL StreamMsgWriterT g_writer;
static StreamMsgPoolT *g_streamMsgPool = NULL;

Status GmcStreamBeginOneWayWrite(uint16_t instanceId, const char *labelName, size_t size, void **buffer)
{
    if (labelName == NULL || buffer == NULL) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    size_t labelNameSize = strlen(labelName);
    if (labelNameSize == 0 || labelName[labelNameSize] != '\0') {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    labelNameSize++;
    if (SECUREC_UNLIKELY(g_streamMsgPool == NULL)) {
        g_streamMsgPool = DbGetShmemStructById(DB_SHM_STREAM_MSG_POOL_ID, instanceId);
        if (SECUREC_UNLIKELY(g_streamMsgPool == NULL)) {
            DB_LOG_AND_SET_LASERR(GMERR_DATA_CORRUPTED, "get stream msg pool");
            return GMERR_DATA_CORRUPTED;
        }
    }

    if (SECUREC_UNLIKELY(g_writer.owner == 0)) {
        g_writer.owner = (uint64_t)DbThreadGetTid();
    }

    Status ret = StreamMsgPoolBeginWrite(g_streamMsgPool, labelNameSize + size, &g_writer);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    uint8_t *vertexLabelName = StreamMsgPoolGetWriteBuf(&g_writer);
    DB_ASSERT(vertexLabelName != NULL);
    DbFastMemcpy(vertexLabelName, labelNameSize, (const uint8_t *)labelName, labelNameSize);
    *buffer = (void *)(vertexLabelName + labelNameSize);
    return GMERR_OK;
}

void GmcStreamEndOneWayWrite(bool abort)
{
    StreamMsgPoolEndWrite(&g_writer, abort);
}
