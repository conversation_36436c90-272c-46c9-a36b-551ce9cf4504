/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: clt_yang.h
 * Description: header file for GMDB client yang
 * Create: 2022-07-19
 */

#ifndef CLT_YANG_H
#define CLT_YANG_H

#include "gmc_types.h"
#include "adpt_define.h"
#include "dm_data_prop.h"
#include "dm_yang_interface.h"
#include "clt_yang_common.h"

#ifdef __cplusplus
extern "C" {
#endif

void CltRecordKeyOplog(CltOperVertexT *op, uint32_t index, GmcOperationTypeE opType);
Status CltYangSetRoot(GmcStmtT *stmt);
Status CltYangBindChild(GmcStmtT *parentStmt, GmcStmtT *childStmt);
Status CltYangSetVertexProperty(
    DmVertexT *vertex, DmVertexLabelT *vertexLabel, const GmcPropValueT *value, GmcYangPropOpTypeE opType);
Status CltYangSetListReferenceKey(GmcStmtT *stmt, GmcPropValueT **refKeyFields, uint32_t refKeyFieldsCount);
Status CltYangEditMultiChildNode(GmcNodeT *node, const char *path, GmcOperationTypeE opType, GmcNodeT **child);
Status CltYangEditChildNode(GmcNodeT *node, const char *name, GmcOperationTypeE opType, GmcNodeT **child);
Status CltYangSetNodeProperty(GmcNodeT *node, DmVertexLabelT *vertexLabel, DmSchemaT *nodeSchema,
    const GmcPropValueT *propValue, GmcYangPropOpTypeE opType);

Status CltYangSubtreePrepareFilter(GmcStmtT *stmt, GmcNodeT *root);
Status CltCreateAndInitChildTreeCtx(GmcStmtT *stmt, DmVertexLabelT *vertexLabel, DmNodeT *node, DmYangTreeT *tree);
Status CltSubtreeCreateVertexYangTree(GmcStmtT *stmt, DmVertexLabelT *vertexLabel, DmYangTreeT **treeNode);
Status CltSubtreeCreateNodeYangTree(GmcStmtT *stmt, const char *name, DmYangTreeT *parent, DmYangTreeT **treeNode);
Status CltLinkVertexTree(GmcStmtT *stmt, DmYangTreeT *parent, DmYangTreeT *child);
Status CltLinkNodeTree(GmcStmtT *stmt, DmYangTreeT *parent, DmYangTreeT *child);
Status CltYangSubtreeSetPropertyEmpty(GmcPropValueT *propValue, DmYangTreeT *tree);
Status CltYangSubtreeSetPropertyByName(GmcPropValueT *propValue, DmYangTreeT *tree);
Status CltCheckSourceNode(
    DbMemCtxT *memCtx, const DmVertexT *srcVertex, const char *sourceNodePath, GmcOperationTypeE childOpType);
Status CltGeneratePathByFieldOrNodeName(
    const GmcNodeT *node, const char *element, GmcYangChildElementTypeE elementType, GmcLostPathInfoT *pathInfo);
Status CltGeneratePathByVertexName(const GmcNodeT *node, const char *childName, GmcLostPathInfoT *pathInfo);
Status CltGetEnumOrIdentityValueByResultNode(
    GmcYangNodeT *info, GmcYangNodeValueT *nodeValue, GmcPropValueT *outPropValue);
Status CltTransEnumOrIdentityKeyToValue(DmSchemaT *schema, uint32_t propeId, DmYangValueParamT *outValue);
Status CltGetEnumOrIdentityInfoByType(DmPropertySchemaT *propertySchema, GmcConvertTypeE convertType,
    GmcPropValueT *inPropValue, GmcPropValueT *outPropValue);
Status CltYangSetAttributeFilterValue(
    DmPropertySchemaT *property, DbListT *filter, uint32_t index, GmcDataTypeE type, const void *value);
Status CltYangProcessSpecialTypes(DbMemCtxT *memCtx, DmSchemaT *schema, uint32_t propId, DmYangValueParamT *currValue);
Status CltFindChildNodeInNonChoice(const GmcNodeT *node, const char *element, bool *hasFound);
Status CltGetNodeSchemaByGmcNode(GmcNodeT *node, DmSchemaT **schema);
uint32_t CltYangGetNodePropeNumExcludeSysPrope(GmcNodeT *node);
Status CltYangVertexPropertyDefaultCheck(
    DmVertexT *vertex, DmVertexLabelT *vertexLabel, const GmcPropValueT *value, GmcYangPropDefaultStateE *defaultState);
Status CltYangNodePropertyDefaultCheck(
    GmcNodeT *node, DmSchemaT *nodeSchema, const GmcPropValueT *propValue, GmcYangPropDefaultStateE *defaultState);

#ifdef __cplusplus
}
#endif
#endif
