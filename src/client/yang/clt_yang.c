/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: clt_yang.c
 * Description: realization of client yang interface of gmdb
 * Create: 2022-07-19
 */
#include "clt_yang.h"
#include "clt_yang_helper.h"
#include "clt_check.h"
#include "clt_stmt.h"
#include "clt_tree_dml.h"
#include "clt_subtree_edit.h"
#include "clt_graph_filter.h"
#include "clt_graph_ddl_vertex.h"
#include "clt_graph_dml_vertex.h"
#include "clt_graph_vertex_struct.h"
#include "clt_graph_ddl_edge.h"
#include "dm_data_pattern.h"
#include "dm_yang_subtree.h"
#include "dm_yang_vertex.h"
#include "dm_yang_common.h"
#include "dm_yang_diff.h"
#include "dm_data_record.h"
#include "dm_meta_yang_vertex_label.h"
#include "gmc_internal.h"

static const char *GetNodeName(const GmcNodeT *node)
{
    if (node->operationType == GMC_OPERATION_SUBTREE_FILTER) {
        return DmGetSubtreeName(node->tree);
    }
    if (CltIsRootNode(node)) {
        return node->dmVertex->vertexDesc->labelName;
    }
    return node->dmNode->nodeDesc->name;
}

static const char *GetOptypeStr(GmcOperationTypeE op)
{
    if (op == GMC_OPERATION_INSERT) {
        return "create";
    } else if (op == GMC_OPERATION_REPLACE_GRAPH) {
        return "replace_graph";
    } else if (op == GMC_OPERATION_DELETE_GRAPH) {
        return "delete_graph";
    } else if (op == GMC_OPERATION_REMOVE_GRAPH) {
        return "remove_graph";
    } else if (op == GMC_OPERATION_MERGE) {
        return "merge";
    } else if (op == GMC_OPERATION_NONE) {
        return "none";
    } else {
        return "err";
    }
}

static const char *GetPropOptypeStr(GmcYangPropOpTypeE op)
{
    if (op == GMC_YANG_PROPERTY_OPERATION_CREATE) {
        return "create";
    } else if (op == GMC_YANG_PROPERTY_OPERATION_REPLACE) {
        return "replace";
    } else if (op == GMC_YANG_PROPERTY_OPERATION_DELETE) {
        return "delete";
    } else if (op == GMC_YANG_PROPERTY_OPERATION_REMOVE) {
        return "remove";
    } else if (op == GMC_YANG_PROPERTY_OPERATION_MERGE) {
        return "merge";
    } else if (op == GMC_YANG_PROPERTY_OPERATION_NONE) {
        return "none";
    } else {
        return "err";
    }
}

static Status CltYangSetIndexAndFilter(
    uint32_t value, DmVertexLabelT *vertexLabel, DmVlIndexLabelT *pkIndex, GmcStmtT *stmt)
{
    if (DmIsListVertexLabel(vertexLabel)) {
        const char *indexName = MEMBER_PTR(pkIndex, indexName);
        char *metaName = MEMBER_PTR(&vertexLabel->metaCommon, metaName);
        CltOperVertexT *operVertex = CltGetOperationContext(stmt);
        DbListT *filter = operVertex->idx.yangListKeyValue;
        DB_POINTER(filter);
        Status ret = CltSetIndexName(stmt, indexName);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Set index name, name=%s, child=%s.", indexName, metaName);
            return ret;
        }
        ret = CltSetFilterValue(filter, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Set refercen key, child=%s.", metaName);
            return ret;
        }
    }
    return GMERR_OK;
}

static Status CltYangSetDefaultProperty(GmcStmtT *stmt, DmVertexLabelT *vertexLabel, uint32_t value)
{
    DB_POINTER2(stmt, vertexLabel);
    GmcOperationTypeE opType = stmt->operationType;
    DmVlIndexLabelT *pkIndex = MEMBER_PTR(vertexLabel->metaVertexLabel, pkIndex);
    if (pkIndex == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Vertex label does not have a primary key index.");
        return GMERR_DATA_EXCEPTION;
    }

    // 如果是List节点，需要设置参考点的Pid
    Status ret = CltYangSetIndexAndFilter(value, vertexLabel, pkIndex, stmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (CltYangIsIndexKeyRequired(opType)) {
        const char *indexName = MEMBER_PTR(pkIndex, indexName);
        ret = CltSetIndexName(stmt, indexName);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(
                ret, "Set index name, name=%s, child=%s.", indexName, MEMBER_PTR(vertexLabel, metaCommon.metaName));
            return ret;
        }
        return CltSetSimpleIndexValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    }
    uint32_t propId = DmIsRootYangVertexLabel(vertexLabel) ? DM_YANG_ID_PROPE_SUBSCRIPT : DM_YANG_PID_PROPE_SUBSCRIPT;
    return CltSetVertexPropertyById(stmt, propId, GMC_DATATYPE_UINT32, &value, sizeof(value));
}

static void RecordEnumOrIdentityKeyOpLog(CltOperVertexT *op, uint32_t index, GmcOperationTypeE opType, DmValueT *value)
{
    uint32_t allocLen = value->value.length + 1;
    char *valueStr = DbDynMemCtxAlloc(op->vertex->memCtx, allocLen);
    if (valueStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY,
            "alloc valuestr, parent:%s index:%" PRIu32 ", printLen:%" PRIu32 ", type:%" PRIu32 ", length:%" PRIu32 ".",
            op->vertex->vertexDesc->labelName, index, allocLen, (uint32_t)value->type, value->value.length);
        return;
    }
    char *strCursor = valueStr;
    DmPrintEnumOrIdentityPropertyValue(value, &strCursor);
    YANG_TRACE_LOG(
        "|Key|(%s)--|index(%u):%s(%s)", op->vertex->vertexDesc->labelName, index, GetOptypeStr(opType), valueStr);
    DbDynMemCtxFree(op->vertex->memCtx, valueStr);
}

void CltRecordKeyOplog(CltOperVertexT *op, uint32_t index, GmcOperationTypeE opType)
{
    if (!DbYangTraceLogEnable(YANG_TRACE_DML)) {
        return;
    }
    CltIndexConditionT *idx = &op->idx;
    DmValueT *values = DbListGetItems(idx->leftValue);
    DmValueT *value = &values[index];
    if (DmIsAttributeType((uint32_t)value->type)) {
        if (idx->indexLabel == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Index label.");
            return;
        }
        DmPropertySchemaT *properties = MEMBER_PTR(idx->indexLabel, properties);
        uint32_t *propIds = MEMBER_PTR(idx->indexLabel, propIds);
        Status ret = DmYangConvertProperty(&properties[propIds[index]], value->value.uintValue, value);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "Convert field info for enum, index:%" PRIu32 ".", index);
            return;
        }
        RecordEnumOrIdentityKeyOpLog(op, index, opType, value);
        return;
    }
    uint32_t printLen = DmGetPropertyValuePrintLen(value) + 1;
    char *valueStr = DbDynMemCtxAlloc(op->vertex->memCtx, printLen);
    if (valueStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY,
            "alloc valuestr, parent:%s index:%" PRIu32 ", printLen:%" PRIu32 ", type:%" PRIu32 ", length:%" PRIu32 ".",
            op->vertex->vertexDesc->labelName, index, printLen, (uint32_t)value->type, value->value.length);
        return;
    }
    char *strCursor = valueStr;
    DmPrintPropertyValue(value, &strCursor, printLen);
    YANG_TRACE_LOG(
        "|Key|(%s)--|index(%u):%s(%s)", op->vertex->vertexDesc->labelName, index, GetOptypeStr(opType), valueStr);
    DbDynMemCtxFree(op->vertex->memCtx, valueStr);
}

void RecordContainerOpLog(const char *parent, const char *child, GmcOperationTypeE opType)
{
    if (!DbYangTraceLogEnable(YANG_TRACE_DML)) {
        return;
    }
    YANG_TRACE_LOG("|container|(%s)--|%s:%s", parent, child, GetOptypeStr(opType));
}

void RecordRootOpLog(GmcStmtT *stmt)
{
    if (!DbYangTraceLogEnable(YANG_TRACE_DML)) {
        return;
    }
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    YANG_TRACE_LOG("|root|(%s):%s.", operVertex->vertex->vertexDesc->labelName, GetOptypeStr(stmt->operationType));
    YANG_TRACE_LOG("|leaf|(%s)--|%s:%s(%" PRIu32 ").", operVertex->vertex->vertexDesc->labelName, "ID",
        GetOptypeStr(stmt->operationType), stmt->yangVertexTmpId);
}

void RecordListOpLog(GmcStmtT *parentStmt, GmcStmtT *childStmt)
{
    if (!DbYangTraceLogEnable(YANG_TRACE_DML)) {
        return;
    }
    CltOperVertexT *operVertex = CltGetOperationContext(childStmt);
    CltOperVertexT *parentOperVertex = CltGetOperationContext(parentStmt);
    char *topRecordName = operVertex->vertex->vertexDesc->labelName;
    char *parentTopRecordName = parentOperVertex->vertex->vertexDesc->labelName;
    YANG_TRACE_LOG("|list|(%s)--|%s:%s.", parentTopRecordName, topRecordName, GetOptypeStr(childStmt->operationType));
    YANG_TRACE_LOG("|leaf|(%s)--|%s:%s(%" PRIu32 ").", topRecordName, "ID", GetOptypeStr(childStmt->operationType),
        childStmt->yangVertexTmpId);
    YANG_TRACE_LOG("|leaf|(%s)--|%s:%s(%" PRIu32 ").", topRecordName, "PID", GetOptypeStr(childStmt->operationType),
        parentStmt->yangVertexTmpId);
}

static void RecordEnumOrIdentityNodeOpLog(
    const DmNodeT *node, const char *child, GmcYangPropOpTypeE opType, DmValueT *value)
{
    uint32_t allocLen = value->value.length + 1;
    char *valueStr = DbDynMemCtxAlloc(node->memCtx, allocLen);
    if (valueStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY,
            "alloc valuestr, parent:%s leaf:%s, allocLen:%" PRIu32 ", type:%" PRIu32 ", length:%" PRIu32 ".",
            node->nodeDesc->name, child, allocLen, (uint32_t)value->type, value->value.length);
        return;
    }
    char *strCursor = valueStr;
    DmPrintEnumOrIdentityPropertyValue(value, &strCursor);
    YANG_TRACE_LOG("|leaf|(%s)--|%s:%s(%s).", node->nodeDesc->name, child, GetPropOptypeStr(opType), valueStr);
    DbDynMemCtxFree(node->memCtx, valueStr);
}

void RecordNodeLeafOpLog(const DmNodeT *node, DmPropertySchemaT *propertySchema, const char *child, DmValueT *value,
    GmcYangPropOpTypeE opType)
{
    if (!DbYangTraceLogEnable(YANG_TRACE_DML)) {
        return;
    }
    if (DmIsAttributeType((uint32_t)value->type)) {
        Status ret = DmYangConvertProperty(propertySchema, value->value.uintValue, value);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "Convert field info, field=%s", child);
            return;
        }
        RecordEnumOrIdentityNodeOpLog(node, child, opType, value);
        return;
    }
    uint32_t printLen = DmGetPropertyValuePrintLen(value) + 1;
    char *valueStr = DbDynMemCtxAlloc(node->memCtx, printLen);
    if (valueStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY,
            "Alloc valueStr, parent:%s leaf:%s, printLen:%" PRIu32 ", type:%" PRIu32 ", length:%" PRIu32 ".",
            node->nodeDesc->name, child, printLen, (uint32_t)value->type, value->value.length);
        return;
    }
    char *strCursor = valueStr;
    DmPrintPropertyValue(value, &strCursor, printLen);
    YANG_TRACE_LOG("|leaf|(%s)--|%s:%s(%s).", node->nodeDesc->name, child, GetPropOptypeStr(opType), valueStr);
    DbDynMemCtxFree(node->memCtx, valueStr);
}

static void RecordEnumOrIdentityVertexOpLog(
    const DmVertexT *vertex, const char *child, GmcYangPropOpTypeE opType, DmValueT *value)
{
    uint32_t allocLen = value->value.length + 1;
    char *valueStr = DbDynMemCtxAlloc(vertex->memCtx, allocLen);
    if (valueStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY,
            "alloc valuestr, parent:%s leaf:%s, allocLen:%" PRIu32 ", type:%" PRIu32 ", length:%" PRIu32 ".",
            vertex->vertexDesc->labelName, child, allocLen, (uint32_t)value->type, value->value.length);
        return;
    }
    char *strCursor = valueStr;
    DmPrintEnumOrIdentityPropertyValue(value, &strCursor);
    YANG_TRACE_LOG("|leaf|(%s)--|%s:%s(%s).", vertex->vertexDesc->labelName, child, GetPropOptypeStr(opType), valueStr);
    DbDynMemCtxFree(vertex->memCtx, valueStr);
}

void RecordVertexLeafOpLog(const DmVertexT *vertex, DmPropertySchemaT *propertySchema, const char *child,
    DmValueT *value, GmcYangPropOpTypeE opType)
{
    if (!DbYangTraceLogEnable(YANG_TRACE_DML)) {
        return;
    }
    if (DmIsAttributeType((uint32_t)value->type)) {
        Status ret = DmYangConvertProperty(propertySchema, value->value.uintValue, value);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "Convert field info, field name:%s", child);
            return;
        }
        RecordEnumOrIdentityVertexOpLog(vertex, child, opType, value);
        return;
    }
    uint32_t printLen = DmGetPropertyValuePrintLen(value) + 1;
    char *valueStr = DbDynMemCtxAlloc(vertex->memCtx, printLen);
    if (valueStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY,
            "Alloc valueStr, parent:%s leaf:%s, printLen:%" PRIu32 ", type:%" PRIu32 ", length:%" PRIu32 ".",
            vertex->vertexDesc->labelName, child, printLen, (uint32_t)value->type, value->value.length);
        return;
    }
    char *strCursor = valueStr;
    DmPrintPropertyValue(value, &strCursor, printLen);
    YANG_TRACE_LOG("|leaf|(%s)--|%s:%s(%s).", vertex->vertexDesc->labelName, child, GetPropOptypeStr(opType), valueStr);
    DbDynMemCtxFree(vertex->memCtx, valueStr);
}

static Status CltVerifyVertexProperty(
    DmVertexT *vertex, DmVertexLabelT *vertexLabel, DmValueT *value, uint32_t propId, GmcYangPropOpTypeE opType)
{
    if (opType == GMC_YANG_PROPERTY_OPERATION_REMOVE || opType == GMC_YANG_PROPERTY_OPERATION_DELETE) {
        return GMERR_OK;
    }
    bool verifyResult = true;
    Status ret = DmVerifyPropConsById(vertex, vertexLabel, value, propId, &verifyResult);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Verify vertex property.");
        return ret;
    }
    if (!verifyResult) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY, "Verify vertex property.");
        return GMERR_INVALID_PROPERTY;
    }
    return GMERR_OK;
}

static Status CltVerifyNodeProperty(DmNodeT *node, DmVertexLabelT *vertexLabel, DmSchemaT *nodeSchema, DmValueT *value,
    uint32_t propId, GmcYangPropOpTypeE opType)
{
    if (opType == GMC_YANG_PROPERTY_OPERATION_REMOVE || opType == GMC_YANG_PROPERTY_OPERATION_DELETE) {
        return GMERR_OK;
    }
    bool verifyResult = true;
    Status ret = DmVerifyNodePropConsById(node, vertexLabel, nodeSchema, value, propId, &verifyResult);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Verify node property.");
        return ret;
    }
    if (!verifyResult) {
        DB_SET_LASTERR(GMERR_INVALID_PROPERTY, "Verify node property.");
        return GMERR_INVALID_PROPERTY;
    }
    return GMERR_OK;
}

static Status CheckIdAndPidProperty(DmVertexT *vertex, uint32_t propId)
{
    // Yang场景预留ID字段不可以设置
    if (propId == DM_YANG_ID_PROPE_SUBSCRIPT) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "Cannot set reserved ID!");
        return GMERR_INVALID_VALUE;
    }
    // Yang场景非根节点的预留PID字段不可以设置
    if (!DmIsRootYangVertexDesc(vertex->vertexDesc) && propId == DM_YANG_PID_PROPE_SUBSCRIPT) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "Cannot set reserved PID!");
        return GMERR_INVALID_VALUE;
    }
    return GMERR_OK;
}

Status CltYangSetRoot(GmcStmtT *stmt)
{
    DB_POINTER(stmt);
    // 根节点不用建边
    uint32_t edgeDirection = GMC_EDGE_DIRECTION_NONE;
    Status ret = CltSetStmtAttr(stmt, GMC_STMT_ATTR_AUTO_CREATE_EDGE, &edgeDirection);
    if (ret != GMERR_OK) {
        return ret;
    }

    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = CltGetCltVertexLabel(stmt)->vertexLabel;
    if (!DmIsRootYangVertexDesc(operVertex->vertex->vertexDesc)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "%s is not root.", operVertex->vertex->vertexDesc->labelName);
        return GMERR_DATATYPE_MISMATCH;
    }
    RecordRootOpLog(stmt);
    // ID值可任意设，但应与自增字段的取值范围保持一致，即 [1, 类型最大值]。不能为0。
    return CltYangSetDefaultProperty(stmt, vertexLabel, 1);
}

Status CltYangBindChild(GmcStmtT *parentStmt, GmcStmtT *childStmt)
{
    DB_POINTER2(parentStmt, childStmt);
    // YANG中的节点插边时只需要建入边，不用建出边
    uint32_t edgeDirection = GMC_EDGE_DIRECTION_IN;
    Status ret = CltSetStmtAttr(childStmt, GMC_STMT_ATTR_AUTO_CREATE_EDGE, &edgeDirection);
    if (ret != GMERR_OK) {
        return ret;
    }

    CltOperVertexT *operVertex = CltGetOperationContext(childStmt);
    DmVertexLabelT *vertexLabel = operVertex->cltCataLabel->vertexLabel;
    uint32_t pid = parentStmt->yangVertexTmpId;
    if (pid == DB_INVALID_UINT32) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "The parent of %s is not set.",
            MEMBER_PTR(vertexLabel->metaVertexLabel, topRecordName));
        return GMERR_DATA_EXCEPTION;
    }
    RecordListOpLog(parentStmt, childStmt);
    if (childStmt->operationType == GMC_OPERATION_INSERT) {
        uint32_t tmpId = childStmt->yangVertexTmpId;
        ret =
            CltSetVertexPropertyById(childStmt, DM_YANG_ID_PROPE_SUBSCRIPT, GMC_DATATYPE_UINT32, &tmpId, sizeof(tmpId));
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    // 把父节点的临时ID设置到子节点的PID字段（属性值或索引值）上
    return CltYangSetDefaultProperty(childStmt, vertexLabel, pid);
}

Status CltTransEnumOrIdentityKeyToValue(DmSchemaT *schema, uint32_t propeId, DmYangValueParamT *outValue)
{
    DmValueT tmpValue = {0};
    DmPropertySchemaT *propertySchema = &MEMBER_PTR(schema, properties)[propeId];
    Status ret = DmYangConvertPropertyNameToValue(propertySchema, (const char *)outValue->value, &tmpValue);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    outValue->value = tmpValue.value.strAddr;
    outValue->size = tmpValue.value.length;
    return GMERR_OK;
}

Status YangProcessEnumAndIdentity(DmSchemaT *schema, uint32_t propId, DmYangValueParamT *currValue)
{
    if (schema == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Schema is not valid.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    Status ret = GMERR_OK;
    if (DmIsAttributeType((uint32_t)currValue->type)) {
        DmPropertySchemaT *propertySchema = &MEMBER_PTR(schema, properties)[propId];
        GmcAttributePropertyT *attrProperty = (GmcAttributePropertyT *)currValue->value;
        currValue->value = attrProperty->value;
        currValue->size = attrProperty->size;
        if (attrProperty->type == GMC_ATTRIBUTE_NAME) {
            ret = CltTransEnumOrIdentityKeyToValue(schema, propId, currValue);
            if (ret != GMERR_OK) {
                return ret;
            }
        } else if (attrProperty->type == GMC_ATTRIBUTE_VALUE) {
            DmValueT tmpValue = {0};
            ret = DmYangConvertProperty(propertySchema, *(const int32_t *)attrProperty->value, &tmpValue);
            if (ret != GMERR_OK) {
                return ret;
            }
        } else {
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Attribute type is mismatch.");
            return GMERR_INVALID_PARAMETER_VALUE;
        }
    }
    return GMERR_OK;
}

Status CltYangProcessSpecialTypes(DbMemCtxT *memCtx, DmSchemaT *schema, uint32_t propId, DmYangValueParamT *currValue)
{
    DmPropertySchemaT *propertySchema = &MEMBER_PTR(schema, properties)[propId];
    if (DmIsAttributeType((uint32_t)currValue->type)) {
        return YangProcessEnumAndIdentity(schema, propId, currValue);
    } else if (DmIsYangUnionType(propertySchema->dataType)) {
        return DmYangProcessUnion(memCtx, propertySchema, currValue);
    } else if (DmIsYangEmptyType((uint32_t)currValue->type)) {
        return DmYangProcessEmpty(currValue);
    }
    return GMERR_OK;
}

Status CltYangSetVertexProperty(
    DmVertexT *vertex, DmVertexLabelT *vertexLabel, const GmcPropValueT *value, GmcYangPropOpTypeE opType)
{
    DB_POINTER2(vertex, value);
    uint32_t propId;
    Status ret = DmVertexGetPropeIdByName(vertex, value->propertyName, &propId);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CheckIdAndPidProperty(vertex, propId);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmYangValueParamT currValue = {.size = value->size, .type = (uint32_t)value->type, .value = value->value};
    DmSchemaT *schema = MEMBER_PTR(vertexLabel->metaVertexLabel, schema);
    if (opType == GMC_YANG_PROPERTY_OPERATION_REMOVE || opType == GMC_YANG_PROPERTY_OPERATION_DELETE) {
        currValue.type = (DbDataTypeE)GMC_DATATYPE_NULL;
        currValue.value = NULL;
        currValue.size = 0;
    } else {
        ret = CltYangProcessSpecialTypes(vertex->memCtx, schema, propId, &currValue);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Process yang special type.");
            return ret;
        }
    }
    DmValueT propertyValue;
    ret = SetDmValue(&propertyValue, (uint32_t)currValue.type, currValue.value, currValue.size);
    if (ret != GMERR_OK) {
        DB_SET_LASTERR(ret, "Set property value");
        return ret;
    }
    ret = CltVerifyVertexProperty(vertex, vertexLabel, &propertyValue, propId, opType);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 若后续操作失败，需调用reset接口重置propOp
    ret = DmVertexSetPropOpTypesById(vertex, propId, (DmPropOpTypeE)opType);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmVertexSetPropeById(propId, propertyValue, vertex);
    if (ret != GMERR_OK) {
        DmVertexResetPropOpType(vertex, propId);
        return ret;
    }
    DmPropertySchemaT *propertySchema = &MEMBER_PTR(schema, properties)[propId];
    RecordVertexLeafOpLog(vertex, propertySchema, value->propertyName, &propertyValue, opType);
    return GMERR_OK;
}

Status CltYangSetAttributeFilterValue(
    DmPropertySchemaT *property, DbListT *filter, uint32_t index, GmcDataTypeE type, const void *value)
{
    if (property == NULL || !DmIsAttributeType(property->dataType)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Property schema is not valid.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    const GmcAttributePropertyT *attrProperty = (const GmcAttributePropertyT *)value;
    Status ret = GMERR_OK;
    DmValueT tmpValue = {0};
    if (attrProperty->type == GMC_ATTRIBUTE_NAME) {
        // 做转换
        ret = DmYangConvertPropertyNameToValue(property, (const char *)attrProperty->value, &tmpValue);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        return CltSetFilterValue(filter, index, type, tmpValue.value.strAddr, tmpValue.value.length);
    } else if (attrProperty->type == GMC_ATTRIBUTE_VALUE) {
        // 设置枚举或identity类型的value时，转换一下，确保value值合法
        ret = DmYangConvertProperty(property, *(const int32_t *)attrProperty->value, &tmpValue);
        if (ret != GMERR_OK) {
            return ret;
        }
        return CltSetFilterValue(filter, index, type, attrProperty->value, attrProperty->size);
    }
    DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Attribute type is mismatch.");
    return GMERR_INVALID_PARAMETER_VALUE;
}

static Status CltYangSetListReferenceKeyInner(
    GmcStmtT *stmt, GmcPropValueT **refKeyFields, DmVertexLabelT *vertexLabel, uint32_t refKeyFieldsCount)
{
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    DbListT *filter = operVertex->idx.yangListKeyValue;
    Status ret = GMERR_OK;
    DmVlIndexLabelT *pkIndex = MEMBER_PTR(vertexLabel->metaVertexLabel, pkIndex);
    for (uint32_t i = 0; i < refKeyFieldsCount; i++) {
        if (refKeyFields[i] == NULL) {
            ret = GMERR_NULL_VALUE_NOT_ALLOWED;
            DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED,
                "The %" PRIu32 ".th field of reference key is null, total refKeyCount is %" PRIu32 ".", i,
                refKeyFieldsCount);
            break;
        }
        // 若propertyName有效，优先使用propertyName
        if (CheckStringInvalid(refKeyFields[i]->propertyName, GMC_PROPERTY_NAME_MAX_LEN) == GMERR_OK) {
            ret = DmIndexLabelGetPropeIdxByName(pkIndex, refKeyFields[i]->propertyName, &refKeyFields[i]->propertyId);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret,
                    "unsound property name of the %" PRIu32 "th reference key, total refKeyCount is %" PRIu32 ".", i,
                    refKeyFieldsCount);
                return ret;
            }
        }
        if (refKeyFields[i]->propertyId >= pkIndex->propeNum) {
            DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE,
                "index %" PRIu32 " out of primary key range, propeNum is %" PRIu32 ".", refKeyFields[i]->propertyId,
                pkIndex->propeNum);
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        DmYangValueParamT tmpValue = {
            .size = refKeyFields[i]->size, .type = (uint32_t)refKeyFields[i]->type, .value = refKeyFields[i]->value};
        DmSchemaT *schema = MEMBER_PTR(vertexLabel->metaVertexLabel, schema);
        uint32_t *propIds = MEMBER_PTR(pkIndex, propIds);
        ret = CltYangProcessSpecialTypes(stmt->memCtx, schema, propIds[refKeyFields[i]->propertyId], &tmpValue);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Process yang special types.");
            return ret;
        }
        ret = CltSetFilterValue(
            filter, refKeyFields[i]->propertyId, (GmcDataTypeE)tmpValue.type, tmpValue.value, tmpValue.size);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "Set the  %" PRIu32 ".th field of reference key.", i);
            break;
        }
    }
    return ret;
}

Status CltYangSetListReferenceKey(GmcStmtT *stmt, GmcPropValueT **refKeyFields, uint32_t refKeyFieldsCount)
{
    DB_POINTER2(stmt, refKeyFields);
    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(stmt);
    if (cltCataLabel == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Clt get cataLabel from stmt.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    DmVertexLabelT *vertexLabel = cltCataLabel->vertexLabel;
    return CltYangSetListReferenceKeyInner(stmt, refKeyFields, vertexLabel, refKeyFieldsCount);
}

static DmNodeOpTypeE ConvertToNodeOpType(GmcOperationTypeE op)
{
    DmNodeOpTypeE nodeOp = DM_NODE_OP_BOTTOM;
    switch (op) {
        case GMC_OPERATION_INSERT:
            nodeOp = DM_NODE_INSERT;
            break;
        case GMC_OPERATION_REPLACE_GRAPH:
            nodeOp = DM_NODE_REPLACE_GRAPH;
            break;
        case GMC_OPERATION_DELETE_GRAPH:
            nodeOp = DM_NODE_DELETE_GRAPH;
            break;
        case GMC_OPERATION_REMOVE_GRAPH:
            nodeOp = DM_NODE_REMOVE_GRAPH;
            break;
        case GMC_OPERATION_MERGE:
            nodeOp = DM_NODE_MERGE;
            break;
        case GMC_OPERATION_NONE:
            nodeOp = DM_NODE_NONE;
            break;
        default:
            break;
    }
    return nodeOp;
}

static Status CheckOpCodeIsLegal(DmNodeOpTypeE lastOpType, DmNodeOpTypeE opType)
{
    // 不允许对已经create的node再进行create操作
    if ((opType == DM_NODE_INSERT) &&
        (lastOpType == DM_NODE_MERGE || lastOpType == DM_NODE_INSERT || lastOpType == DM_NODE_REPLACE_GRAPH)) {
        DB_LOG_AND_SET_LASERR(GMERR_SYNTAX_ERROR, "It is not allowed to create the node that is created.");
        return GMERR_SYNTAX_ERROR;
    }

    // 不允许对已经delete的node再进行delete或者none操作
    if ((opType == DM_NODE_DELETE_GRAPH) &&
        (lastOpType == DM_NODE_DELETE_GRAPH || lastOpType == DM_NODE_REMOVE_GRAPH)) {
        DB_LOG_AND_SET_LASERR(GMERR_SYNTAX_ERROR, "It is not allowed to delete the node that is deleted.");
        return GMERR_SYNTAX_ERROR;
    }
    return GMERR_OK;
}

static void CltDeleteCaseNode(const DmNodeT *node, const char *caseName)
{
    for (uint32_t i = 0; i < node->nodeDesc->nodeNumPerElement; i++) {
        if (strcmp(caseName, node->nodeDesc->childNodeDescs[i]->name) == 0) {
            continue;
        }
        if (DmNodeIsCreated(node->currNodes[i])) {
            DmClearNode(node->currNodes[i]);
            break;
        }
    }
}

Status CltYangEditChildNode(GmcNodeT *node, const char *name, GmcOperationTypeE opType, GmcNodeT **child)
{
    DB_POINTER3(node, name, child);
    if (opType == GMC_OPERATION_SUBTREE_FILTER) {
        return CltEditChildSubtreeNode(node, name, child);
    }
    GmcNodeT localNode = CltMakeEmptyCopyNode(node);
    Status ret;
    if (CltIsRootNode(node)) {
        // 是根节点
        ret = DmVertexGetNodeByName(node->dmVertex, name, &localNode.dmNode);
    } else {
        // 是子节点，且切换元素成功
        ret = DmNodeGetChildNodeByName(node->dmNode, name, &localNode.dmNode);
    }
    if (ret != GMERR_OK) {
        return ret;
    }

    DmNodeOpTypeE nodeOp = ConvertToNodeOpType(opType);

    // 校验当前操作和上一个操作是否冲突，前面加入的操作必然合法，只需要校验上一个操作即可
    if (localNode.dmNode->opNum > 0) {
        DmNodeOpTypeE lastOp = localNode.dmNode->opArray[localNode.dmNode->opNum - 1];
        ret = CheckOpCodeIsLegal(lastOp, nodeOp);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(
                ret, "Node %s op is unsound lastOp %" PRIu32 ".", localNode.dmNode->nodeDesc->name, (uint32_t)lastOp);
            return ret;
        }
    }
    localNode.operationType = opType;

    if (!CltIsRootNode(node) && DmIsChoiceYangInfo(node->dmNode->nodeDesc->yangInfoDesc)) {
        CltDeleteCaseNode(node->dmNode, name);
    }

    // 顶点为create或者replace操作，其下子节点都是create操作，多操作情况下也只需要一个element，不用再添加element
    if ((node->stmt->operationType == GMC_OPERATION_INSERT ||
            node->stmt->operationType == GMC_OPERATION_REPLACE_GRAPH) &&
        localNode.dmNode->realElementNum == 1) {
        goto END;
    }

    localNode.dmNode->isCreated = true;
    localNode.index = localNode.dmNode->realElementNum;
    ret = DmDeltaVectorAddOperation(localNode.dmNode, nodeOp);
    if (ret != GMERR_OK) {
        return ret;
    }
END:
    ret = CltReturnLocalNode(&localNode, child);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    DbLinkedListAppend(&node->childListNode, &((*child)->childListNode));
    RecordContainerOpLog(GetNodeName(node), name, opType);
    return GMERR_OK;
}

Status CltYangVertexPropertyDefaultCheck(
    DmVertexT *vertex, DmVertexLabelT *vertexLabel, const GmcPropValueT *value, GmcYangPropDefaultStateE *defaultState)
{
    DB_POINTER4(vertex, vertexLabel, value, defaultState);
    uint32_t propId;
    Status ret = DmVertexGetPropeIdByName(vertex, value->propertyName, &propId);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmYangValueParamT currValue = {.size = value->size, .type = (uint32_t)value->type, .value = value->value};
    DmSchemaT *schema = MEMBER_PTR(vertexLabel->metaVertexLabel, schema);
    DmPropertySchemaT *propertySchema = &MEMBER_PTR(schema, properties)[propId];
    if (DmIsYangUnionType(propertySchema->dataType) &&
        !DmYangSchemaCheckUnionType(propertySchema->unionTypes, (uint32_t)currValue.type)) {
        *defaultState = GMC_YANG_PROPERTY_WITH_DEFAULT_NOT_EQUAL;
        return GMERR_OK;
    }

    ret = CltYangProcessSpecialTypes(vertex->memCtx, schema, propId, &currValue);
    if (ret != GMERR_OK) {
        return ret;
    }

    DmValueT propertyValue;
    ret = SetDmValue(&propertyValue, (uint32_t)currValue.type, currValue.value, currValue.size);
    if (ret != GMERR_OK) {
        DB_SET_LASTERR(ret, "Set property value");
        return ret;
    }
    bool isEqual = false;
    ret = DmVertexPropIsEqualDefault(vertex, &propertyValue, propId, &isEqual);
    if (ret != GMERR_OK) {
        *defaultState = GMC_YANG_PROPERTY_WITH_DEFAULT_NONE;
        return GMERR_OK;
    }
    *defaultState = isEqual ? GMC_YANG_PROPERTY_WITH_DEFAULT_EQUAL : GMC_YANG_PROPERTY_WITH_DEFAULT_NOT_EQUAL;
    return GMERR_OK;
}

Status CltYangNodePropertyDefaultCheck(
    GmcNodeT *node, DmSchemaT *nodeSchema, const GmcPropValueT *propValue, GmcYangPropDefaultStateE *defaultState)
{
    DB_POINTER4(node, nodeSchema, propValue, defaultState);
    uint32_t propId;
    Status ret = DmNodeGetPropeIdByName(node->dmNode, propValue->propertyName, &propId);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmYangValueParamT currValue = {
        .size = propValue->size, .type = (uint32_t)propValue->type, .value = propValue->value};
    ret = CltYangProcessSpecialTypes(node->memCtx, nodeSchema, propId, &currValue);
    if (ret != GMERR_OK) {
        return ret;
    }

    DmValueT dmValue;
    ret = SetDmValue(&dmValue, (uint32_t)currValue.type, currValue.value, currValue.size);
    if (ret != GMERR_OK) {
        DB_SET_LASTERR(ret, "Set property value");
        return ret;
    }
    bool isEqual = false;
    ret = DmNodePropIsEqualDefault(node->dmNode, &dmValue, propId, &isEqual);
    if (ret != GMERR_OK) {
        *defaultState = GMC_YANG_PROPERTY_WITH_DEFAULT_NONE;
        return GMERR_OK;
    }
    *defaultState = isEqual ? GMC_YANG_PROPERTY_WITH_DEFAULT_EQUAL : GMC_YANG_PROPERTY_WITH_DEFAULT_NOT_EQUAL;
    return GMERR_OK;
}

Status CltYangSetNodeProperty(GmcNodeT *node, DmVertexLabelT *vertexLabel, DmSchemaT *nodeSchema,
    const GmcPropValueT *propValue, GmcYangPropOpTypeE opType)
{
    DB_POINTER2(node, propValue);
    uint32_t propId;
    Status ret = DmNodeSetElementIndex(node->dmNode, node->index);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmNodeGetPropeIdByName(node->dmNode, propValue->propertyName, &propId);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmYangValueParamT currValue = {
        .size = propValue->size, .type = (uint32_t)propValue->type, .value = propValue->value};
    if (opType == GMC_YANG_PROPERTY_OPERATION_REMOVE || opType == GMC_YANG_PROPERTY_OPERATION_DELETE) {
        currValue.type = (DbDataTypeE)GMC_DATATYPE_NULL;
        currValue.value = NULL;
        currValue.size = 0;
    } else {
        ret = CltYangProcessSpecialTypes(node->memCtx, nodeSchema, propId, &currValue);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Process yang special type.");
            return ret;
        }
    }
    DmValueT dmValue;
    ret = SetDmValue(&dmValue, (uint32_t)currValue.type, currValue.value, currValue.size);
    if (ret != GMERR_OK) {
        DB_SET_LASTERR(ret, "Set property value");
        return ret;
    }
    ret = CltVerifyNodeProperty(node->dmNode, vertexLabel, nodeSchema, &dmValue, propId, opType);
    if (ret != GMERR_OK) {
        return ret;
    }
    bool isDelta = (node->type == DELTA_UPDATE_NODE);
    // 若后续操作失败，需调用reset接口重置propOp
    ret = DmNodeSetPropOpTypesById(node->dmNode, propId, (DmPropOpTypeE)opType, isDelta);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmNodeSetPropeById(propId, dmValue, node->dmNode);
    if (ret != GMERR_OK) {
        DmNodeResetPropOpType(node->dmNode, propId);
        return ret;
    }
    DmPropertySchemaT *propertySchema = &MEMBER_PTR(nodeSchema, properties)[propId];
    RecordNodeLeafOpLog(node->dmNode, propertySchema, propValue->propertyName, &dmValue, opType);
    return GMERR_OK;
}

static Status CheckParentAndChildOpType(DmNodeOpTypeE parentOp, GmcOperationTypeE childOp)
{
    if (parentOp == DM_NODE_DELETE_GRAPH || parentOp == DM_NODE_REMOVE_GRAPH) {
        DB_LOG_AND_SET_LASERR(GMERR_SYNTAX_ERROR,
            "Can not operate child when delete or remove parent! Parent op: %" PRIu32 "", (uint32_t)parentOp);
        return GMERR_SYNTAX_ERROR;
    }
    if (parentOp == DM_NODE_INSERT) {
        if (childOp == GMC_OPERATION_DELETE_GRAPH || childOp == GMC_OPERATION_REMOVE_GRAPH ||
            childOp == GMC_OPERATION_NONE) {
            DB_LOG_AND_SET_LASERR(GMERR_SYNTAX_ERROR,
                "Child operation type must be create, merge or replace when insert parent! Child op: %" PRIu32 "",
                (uint32_t)childOp);
            return GMERR_SYNTAX_ERROR;
        }
    }
    if (parentOp == DM_NODE_REPLACE_GRAPH) {
        if (childOp == GMC_OPERATION_DELETE_GRAPH || childOp == GMC_OPERATION_REMOVE_GRAPH ||
            childOp == GMC_OPERATION_NONE) {
            DB_LOG_AND_SET_LASERR(GMERR_SYNTAX_ERROR,
                "Child operation type must be create, merge or replace when replace parent! Child op: %" PRIu32 "",
                (uint32_t)childOp);
            return GMERR_SYNTAX_ERROR;
        }
    }
    return GMERR_OK;
}

Status CltCheckSourceNode(
    DbMemCtxT *memCtx, const DmVertexT *srcVertex, const char *sourceNodePath, GmcOperationTypeE childOpType)
{
    DB_POINTER3(memCtx, srcVertex, sourceNodePath);
    char *namePath[DB_SCHEMA_MAX_DEPTH];  // 属性名路径
    uint32_t depth = 0;
    // + 1 略过最前面的'/'
    Status ret = DbAllocNamePath(memCtx, sourceNodePath + 1, namePath, &depth);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmNodeT *parentNode = NULL;
    ret = DmVertexGetNodeByNamePath(srcVertex, (char **)namePath, depth, &parentNode);
    DbFreeNamePath(memCtx, namePath);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (!parentNode->isCreated || parentNode->opNum == 0) {
        DB_LOG_AND_SET_LASERR(GMERR_SYNTAX_ERROR, "Parent node %s has not been created!", parentNode->nodeDesc->name);
        return GMERR_SYNTAX_ERROR;
    }
    // 校验父子节点操作约束。父node存在多操作的情况下，只校验最后一次操作的合法性
    return CheckParentAndChildOpType(parentNode->opArray[parentNode->opNum - 1], childOpType);
}

// path: , element: abcd, => abcd
// path: abcd, element: efg, => efg/abcd
Status CltInsertElementToPath(GmcLostPathInfoT *pathInfo, const char *element)
{
    size_t elementLen = strlen(element);
    // 如果已有 path 为空，+1 是留出 '\0' 的空间；如果原 path 不为空，+1 是留出 '/' 的空间
    uint32_t totalElementLen = (uint32_t)(elementLen + 1);
    pathInfo->actualPathLen += totalElementLen;
    if (pathInfo->actualPathLen >= pathInfo->totalPathLen) {
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }

    char *path = pathInfo->path;
    size_t curPathLen = strlen(path);
    if (curPathLen > 0) {
        errno_t err = memmove_s(path + totalElementLen, pathInfo->totalPathLen - totalElementLen, path, curPathLen);
        if (err != EOK) {
            DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Memmove when create choice or case path.");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        path[elementLen] = NAME_PATH_SPLITTER;  // 在之前的路径前面填入 '/'
    }
    errno_t err = memcpy_s(path, pathInfo->totalPathLen, element, elementLen);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Memcpy when create choice or case path.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status CltFindElementInNode(const DmNodeT *node, const char *name, GmcYangChildElementTypeE elementType,
    GmcLostPathInfoT *pathInfo, bool *hasFound)
{
    Status ret;
    for (uint32_t i = 0; i < node->nodeDesc->nodeNumPerElement; i++) {
        DmNodeT *childNode = NULL;
        ret = DmNodeGetChildNodeByIndex(node, i, &childNode);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (strcmp(childNode->nodeDesc->name, name) == 0) {
            // 如果是choicecase类型节点则需要继续往下判断
            if (!DmIsChoiceCaseYangInfo(childNode->nodeDesc->yangInfoDesc) && elementType == GMC_YANG_TYPE_NODE) {
                *hasFound = true;
                return GMERR_OK;  // 找到要操作的节点时，该节点名本身不用放在 path 中
            }
        }
        if (!DmIsChoiceCaseYangInfo(childNode->nodeDesc->yangInfoDesc)) {
            continue;
        }

        ret = CltFindElementInNode(childNode, name, elementType, pathInfo, hasFound);
        // 当path的空间不足时，会继续计算当前path需要的实际长度，方便业务重新分配内存
        if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
            return ret;
        }

        if (*hasFound) {
            return CltInsertElementToPath(pathInfo, childNode->nodeDesc->name);
        }
        for (uint32_t propeId = 0; propeId < DmNodeGetPropeNum(childNode); propeId++) {
            if (!DmNodePropIsValidById(childNode, propeId)) {
                continue;
            }
            char *propName = NULL;
            ret = DmNodeGetPropNameById(childNode, propeId, &propName);
            if (ret != GMERR_OK) {
                return ret;
            }
            if (strcmp(propName, name) == 0 && elementType == GMC_YANG_TYPE_FIELD) {
                *hasFound = true;
                return CltInsertElementToPath(pathInfo, childNode->nodeDesc->name);
            }
        }
    }
    return GMERR_OK;
}

Status CltFindElementInVertex(const DmVertexT *vertex, const char *name, GmcYangChildElementTypeE elementType,
    GmcLostPathInfoT *pathInfo, bool *hasFound)
{
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < vertex->vertexDesc->nodeNum; i++) {
        DmNodeT *childNode = NULL;
        ret = DmVertexGetChildNodeByIndex(vertex, i, &childNode);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (!DmIsChoiceCaseYangInfo(childNode->nodeDesc->yangInfoDesc)) {
            continue;
        }
        ret = CltFindElementInNode(childNode, name, elementType, pathInfo, hasFound);
        if (ret != GMERR_PROGRAM_LIMIT_EXCEEDED && ret != GMERR_OK) {
            return ret;
        }

        if (*hasFound) {
            return CltInsertElementToPath(pathInfo, childNode->nodeDesc->name);
        }
    }
    return ret;
}

Status GetOutEdgeLabelByIndex(
    GmcStmtT *stmt, DmVertexLabelT *vertexLabel, uint32_t indexId, CltCataLabelT **edgeCataLabel)
{
    VertexLabelCommonInfoT *commonInfo = NULL;
    CltGetCommonInfoFromVertexLabel(stmt, vertexLabel, &commonInfo);
    if (commonInfo == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "commonInfoShmPtr is inv, segId: %" PRIu32 ", offset: %" PRIu32, vertexLabel->commonInfoShmPtr.segId,
            vertexLabel->commonInfoShmPtr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (indexId >= commonInfo->edgeLabelNum) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Inv edge for choice case path.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DmEdgeLabelInfoT *relatedEdgeLabels = NULL;
    CltGetRelatedEdgeLabelsFromVertexLabel(stmt, commonInfo, &relatedEdgeLabels);
    if (relatedEdgeLabels == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "relatedEdgeLabelsShm is inv, segId: %" PRIu32 ", offset: %" PRIu32, commonInfo->relatedEdgeLabelsShm.segId,
            commonInfo->relatedEdgeLabelsShm.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    Status ret = CltGetEdgeLabel(stmt, MEMBER_PTR(&relatedEdgeLabels[indexId], edgeName), edgeCataLabel);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "No edge for choice case path.");
        return ret;
    }
    return GMERR_OK;
}

Status CltFindChildVertexInNonChoice(const GmcNodeT *node, uint32_t uniqueNodeId, const char *name, bool *isFound)
{
    *isFound = false;
    DmVertexT *vertex = NULL;
    if (node->operationType == GMC_OPERATION_SUBTREE_FILTER) {
        vertex = DmSubtreeGetVertex(node->tree);
    } else {
        vertex = ((CltOperVertexT *)CltGetOperationContext(node->stmt))->vertex;
    }
    GmcStmtT *stmt = node->stmt;
    CltCataLabelT *cataLabel = NULL;
    Status ret = GetVertexLabelByNameWithCache(stmt, vertex->vertexDesc->labelName, DM_SCHEMA_MIN_VERSION, &cataLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    CltCataLabelT *edgeCataLabel = NULL;
    for (uint32_t i = DmIsRootYangVertexDesc(vertex->vertexDesc) ? 0 : 1; i < vertex->vertexDesc->edgeLabelNum; i++) {
        ret = GetOutEdgeLabelByIndex(stmt, cataLabel->vertexLabel, i, &edgeCataLabel);
        if (ret != GMERR_OK) {
            CltCataCloseVertexLabel(cataLabel);
#ifdef EXPERIMENTAL_NERGC
            if (DbIsTcp()) {
                RemoveRemoteLabelFromCache(stmt);
            }
#endif
            return ret;
        }
        char *dstLabelName = MEMBER_PTR(edgeCataLabel->edgeLabel, destVertexName);
        if (uniqueNodeId == edgeCataLabel->edgeLabel->sourceUniqueNodeId && strcmp(name, dstLabelName) == 0) {
            *isFound = true;
            CltCataCloseEdgeLabel(edgeCataLabel);
            CltCataCloseVertexLabel(cataLabel);
            return GMERR_OK;
        }
        CltCataCloseEdgeLabel(edgeCataLabel);
    }
    CltCataCloseVertexLabel(cataLabel);
    return GMERR_OK;
}

Status CltFindVertexChildInNonChoice(const DmVertexT *vertex, const char *name, bool *isFound)
{
    *isFound = false;
    for (uint32_t propeId = 0; propeId < vertex->record->recordDesc->propeNum; propeId++) {
        DmPropertyInfoT *propeInfo = &(vertex->record->recordDesc->propeInfos[propeId]);
        if (DmIsUselessProperty(propeInfo)) {
            continue;
        }
        if (strcmp(propeInfo->propeName, name) == 0) {
            *isFound = true;
            return GMERR_OK;
        }
    }
    for (uint32_t i = 0; i < vertex->vertexDesc->nodeNum; i++) {
        DmNodeT *childNode = NULL;
        Status ret = DmVertexGetChildNodeByIndex(vertex, i, &childNode);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (DmIsChoiceCaseYangInfo(childNode->nodeDesc->yangInfoDesc)) {
            continue;
        }
        if (strcmp(childNode->nodeDesc->name, name) == 0) {
            *isFound = true;
            return GMERR_OK;
        }
    }
    return GMERR_OK;
}

Status CltFindNodeChildInNonChoice(const DmNodeT *node, const char *element, bool *isFound)
{
    Status ret = GMERR_OK;
    *isFound = false;
    for (uint32_t propeId = 0; propeId < DmNodeGetPropeNum(node); propeId++) {
        if (!DmNodePropIsValidById(node, propeId)) {
            continue;
        }
        char *propName = NULL;
        ret = DmNodeGetPropNameById(node, propeId, &propName);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (strcmp(propName, element) == 0) {
            *isFound = true;
            return GMERR_OK;
        }
    }
    for (uint32_t i = 0; i < node->nodeDesc->nodeNumPerElement; i++) {
        DmNodeT *childNode = NULL;
        ret = DmNodeGetChildNodeByIndex(node, i, &childNode);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (DmIsChoiceCaseYangInfo(childNode->nodeDesc->yangInfoDesc)) {
            continue;
        }
        if (strcmp(childNode->nodeDesc->name, element) == 0) {
            *isFound = true;
            return GMERR_OK;
        }
    }
    return GMERR_OK;
}

// 判断element是否为node直接相连的字段，节点或者vertex（非choice-case的子节点）
Status CltFindChildNodeInNonChoice(const GmcNodeT *node, const char *element, bool *hasFound)
{
    Status ret = GMERR_OK;
    uint32_t uniqueNodeId = 0;
    if (CltIsRootNode(node)) {
        ret = CltFindVertexChildInNonChoice(node->dmVertex, element, hasFound);
        uniqueNodeId = 1;
    } else {
        ret = CltFindNodeChildInNonChoice(node->dmNode, element, hasFound);
        uniqueNodeId = node->dmNode->nodeDesc->yangInfoDesc->uniqueNodeId;
    }
    if (ret != GMERR_OK || *hasFound) {
        return ret;
    }

    return CltFindChildVertexInNonChoice(node, uniqueNodeId, element, hasFound);
}

// 获取vertex与vertex内部字段之间的choice/case路径信息
Status CltGeneratePathByFieldOrNodeName(
    const GmcNodeT *node, const char *element, GmcYangChildElementTypeE elementType, GmcLostPathInfoT *pathInfo)
{
    Status ret;
    bool hasFound = false;
    if (CltIsRootNode(node)) {
        ret = CltFindElementInVertex(node->dmVertex, element, elementType, pathInfo, &hasFound);
    } else {
        ret = CltFindElementInNode(node->dmNode, element, elementType, pathInfo, &hasFound);
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    if (!hasFound || pathInfo->actualPathLen == 0) {
        const char *parentName =
            CltIsRootNode(node) ? node->dmVertex->vertexDesc->labelName : node->dmNode->nodeDesc->name;
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "No choice case path from %s to %s.", parentName, element);
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    return GMERR_OK;
}

static Status CltGetChoiceCasePathToVertex(
    DmEdgeLabelT *edgeLabel, const GmcNodeT *srcNode, const char *childName, char **outPath)
{
    // 先校验确保源点所属 vertex 跟目的 vertex 之间有边关系
    DmVertexT *vertex = NULL;
    if (srcNode->operationType == GMC_OPERATION_SUBTREE_FILTER) {
        vertex = DmSubtreeGetVertex(srcNode->tree);
    } else {
        vertex = ((CltOperVertexT *)CltGetOperationContext(srcNode->stmt))->vertex;
    }
    char *path = MEMBER_PTR(edgeLabel, sourceNodePath);
    char *edgeMetaName = MEMBER_PTR(&(edgeLabel->metaCommon), metaName);
    if (vertex->vertexDesc->labelId != edgeLabel->sourceVertexLabelId) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "No edge for choice or case path, vertex=%s, child=%s",
            vertex->vertexDesc->labelName, childName);
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    // 正常情况下，要获取路径的vertex是case的孩子，case在部分打散映射为node类型，故边标签中的sourceNodePath不可能为空
    if (path == NULL || strcmp(path, "/") == 0) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Null sourceNodePath in edgeLabel %s, %s not child",
            edgeMetaName, childName);
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    // 源点为 vertex 的情况下，入边边标签中的 sourceNodePath 就是缺失的路径
    if (CltIsRootNode(srcNode)) {
        *outPath = path + 1;  // Skip the '/' character
        return GMERR_OK;
    }

    // 源点为 node 则返回入边边标签中的 sourceNodePath 在源点名称之后的部分
    const char *nodeName = srcNode->dmNode->nodeDesc->name;
    size_t nodeNameLen = strlen(nodeName);
    const char *node = strstr(path, nodeName);
    while (node != NULL) {
        // 有效的路径必定在源点 node 名称后还有 choice/case 的路径，所以 node 名字后一定带有一个 '/'
        if (*(node + nodeNameLen) == NAME_PATH_SPLITTER) {
            break;
        }
        node = strstr(node + 1, nodeName);
    }

    if (node == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "%s is not an ancestor node of %s", nodeName, childName);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    // Find the start of the next node
    *outPath = strchr(node, NAME_PATH_SPLITTER);
    if (*outPath == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_INVALID_PARAMETER_VALUE, "%s should not be the parent node of %s", nodeName, childName);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    *outPath += 1;  // Skip the '/' character
    return GMERR_OK;
}

// 获取 vertexLabel 的入边（固定为 relatedEdgeLabels 中的第一个边)
static Status GetInEdgeLabel(
    GmcStmtT *stmt, DmVertexLabelT *vertexLabel, const char *childName, CltCataLabelT **edgeCataLabel)
{
    VertexLabelCommonInfoT *commonInfo = NULL;
    CltGetCommonInfoFromVertexLabel(stmt, vertexLabel, &commonInfo);
    if (commonInfo == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "commonInfoShmPtr is inv, segId: %" PRIu32 ", offset: %" PRIu32, vertexLabel->commonInfoShmPtr.segId,
            vertexLabel->commonInfoShmPtr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (commonInfo->edgeLabelNum == 0) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "%s does not have edge label", childName);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DmEdgeLabelInfoT *relatedEdgeLabels = NULL;
    CltGetRelatedEdgeLabelsFromVertexLabel(stmt, commonInfo, &relatedEdgeLabels);
    if (relatedEdgeLabels == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "relatedEdgeLabelsShm is inv, segId: %" PRIu32 ", offset: %" PRIu32, commonInfo->relatedEdgeLabelsShm.segId,
            commonInfo->relatedEdgeLabelsShm.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    Status ret = CltGetEdgeLabel(stmt, MEMBER_PTR(&relatedEdgeLabels[0], edgeName), edgeCataLabel);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Get edge label of %s.", childName);
        return ret;
    }
    return GMERR_OK;
}

// 获取vertex与有边关联vertex之间的choice/case路径信息
Status CltGeneratePathByVertexName(const GmcNodeT *node, const char *childName, GmcLostPathInfoT *pathInfo)
{
    GmcStmtT *stmt = node->stmt;
    CltCataLabelT *cataLabel = NULL;
    Status ret = GetVertexLabelByNameWithCache(stmt, childName, DM_SCHEMA_MIN_VERSION, &cataLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 孩子不会是是根节点
    if (DmIsRootYangVertexLabel(cataLabel->vertexLabel)) {
        const char *parentName =
            CltIsRootNode(node) ? node->dmVertex->vertexDesc->labelName : node->dmNode->nodeDesc->name;
        DB_LOG_AND_SET_LASERR(
            GMERR_INVALID_PARAMETER_VALUE, "Inv choice|case path between %s and %s.", parentName, childName);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    CltCataLabelT *edgeCataLabel = NULL;
    ret = GetInEdgeLabel(stmt, cataLabel->vertexLabel, childName, &edgeCataLabel);
    if (ret != GMERR_OK) {
        CltCataCloseVertexLabel(cataLabel);
        return ret;
    }
    DB_ASSERT(edgeCataLabel != NULL);

    char *outPath = NULL;
    ret = CltGetChoiceCasePathToVertex(edgeCataLabel->edgeLabel, node, childName, &outPath);
    CltCataCloseEdgeLabel(edgeCataLabel);
    CltCataCloseVertexLabel(cataLabel);
    if (ret != GMERR_OK) {
#ifdef EXPERIMENTAL_NERGC
        if (DbIsTcp()) {
            RemoveRemoteLabelFromCache(stmt);
        }
#endif
        return ret;
    }
    // 正常情况下，父节点和子 vertex 之间至少隔了一个 choice 和一个 case，故路径中至少有一个 '/'
    if (strchr(outPath, NAME_PATH_SPLITTER) == NULL) {
        const char *parentName =
            CltIsRootNode(node) ? node->dmVertex->vertexDesc->labelName : node->dmNode->nodeDesc->name;
        DB_LOG_AND_SET_LASERR(
            GMERR_INVALID_PARAMETER_VALUE, "Inv choice or case path between %s and %s.", parentName, childName);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return CltInsertElementToPath(pathInfo, outPath);
}

static Status CheckAndEditChoiceCaseNode(DmNodeT *node, DmNodeOpTypeE opType)
{
    if (!DmIsChoiceCaseYangInfo(node->nodeDesc->yangInfoDesc)) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED,
            "Editing by name path only supports choice and case, %s is not choice or case.", node->nodeDesc->name);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    if (node->isCreated) {
        return GMERR_OK;
    }
    node->isCreated = true;
    return DmDeltaVectorAddOperation(node, opType);
}

static Status CltYangEditNodeByNamePath(
    GmcNodeT *node, char **namePath, uint32_t depth, GmcOperationTypeE opType, GmcNodeT **child)
{
    DmNodeOpTypeE nodeOp = ConvertToNodeOpType(opType);
    Status ret;
    uint32_t index = 0;
    DmNodeT *currentNode = NULL;
    if (CltIsRootNode(node)) {
        ret = DmVertexGetNodeByName(node->dmVertex, namePath[index], &currentNode);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = CheckAndEditChoiceCaseNode(currentNode, nodeOp);
        if (ret != GMERR_OK) {
            return ret;
        }
        index++;
    } else {
        currentNode = node->dmNode;
    }

    while (index < depth) {
        DmNodeT *childNode = NULL;
        ret = DmNodeGetChildNodeByName(currentNode, namePath[index], &childNode);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = CheckAndEditChoiceCaseNode(childNode, nodeOp);
        if (ret != GMERR_OK) {
            return ret;
        }
        // 编辑一个 case 的同时要删除对其它 case 的操作
        if (DmIsChoiceYangInfo(currentNode->nodeDesc->yangInfoDesc)) {
            CltDeleteCaseNode(currentNode, namePath[index]);
        }
        currentNode = childNode;
        index++;
    }

    GmcNodeT localNode = CltMakeEmptyCopyNode(node);
    localNode.operationType = opType;
    localNode.dmNode = currentNode;
    if (currentNode->realElementNum < 1) {
        DB_LOG_AND_SET_LASERR(
            GMERR_DATA_EXCEPTION, "unsound node %s, realElementNum less than 1.", currentNode->nodeDesc->name);
        return GMERR_DATA_EXCEPTION;
    }
    localNode.index = currentNode->realElementNum - 1;
    ret = CltReturnLocalNode(&localNode, child);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbLinkedListAppend(&node->childListNode, &((*child)->childListNode));
    return GMERR_OK;
}

Status CltYangEditMultiChildNode(GmcNodeT *node, const char *path, GmcOperationTypeE opType, GmcNodeT **child)
{
    char *namePath[DB_SCHEMA_MAX_DEPTH];  // 属性名路径
    uint32_t depth = 0;
    Status ret = DbAllocNamePath(node->memCtx, path, namePath, &depth);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Allocate name path for editing %s.", path);
        return ret;
    }
    // 路径至少由一个 choice 和 一个 case 构成，所以深度至少为 2
    if (depth <= 1) {
        DbFreeNamePath(node->memCtx, namePath);
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "%s, name path depth need more than 1", path);
        return ret;
    }

    if (opType == GMC_OPERATION_SUBTREE_FILTER) {
        ret = CltEditSubtreeNodeByNamePath(node, namePath, depth, child);
    } else {
        ret = CltYangEditNodeByNamePath(node, namePath, depth, opType, child);
    }
    DbFreeNamePath(node->memCtx, namePath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Edit node by name path %s.", path);
    } else {
        RecordContainerOpLog(GetNodeName(node), path, opType);
    }
    return ret;
}

static Status GetEnumOrIdentityValue(
    DmPropertySchemaT *propertySchema, GmcYangNodeValueT *nodeValue, uint32_t proId, GmcPropValueT *outPropValue)
{
    outPropValue->propertyId = proId;
    outPropValue->type = nodeValue->type;
    errno_t err =
        memcpy_s(outPropValue->propertyName, GMC_PROPERTY_NAME_MAX_LEN, nodeValue->name, strlen(nodeValue->name));
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "Copy property name, os ret %" PRId32, err);
        return GMERR_FIELD_OVERFLOW;
    }
    if (!DmIsAttributeType(propertySchema->dataType)) {
        return GMERR_OK;
    }
    DmValueT tmpValue = {0};
    Status ret = DmYangConvertProperty(propertySchema, *(const int32_t *)nodeValue->value, &tmpValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    err = memcpy_s(outPropValue->value, outPropValue->size, tmpValue.value.strAddr, tmpValue.value.length);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "Copy enum or identity key, os ret %" PRId32, err);
        return GMERR_FIELD_OVERFLOW;
    }
    outPropValue->size = tmpValue.value.length;
    return GMERR_OK;
}

static Status PollingPropertiesAndGetValue(DmSchemaT *schema, GmcYangNodeValueT *nodeValue, GmcPropValueT *outPropValue)
{
    for (uint32_t i = 0; i < schema->propeNum; i++) {
        DmPropertySchemaT *propertySchema = &MEMBER_PTR(schema, properties)[i];
        if (!DmIsAttributeType(propertySchema->dataType)) {
            continue;
        }
        char *proName = MEMBER_PTR(propertySchema, name);
        if (strcmp(proName, nodeValue->name) != 0) {
            continue;
        }
        return GetEnumOrIdentityValue(propertySchema, nodeValue, i, outPropValue);
    }
    DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "field name is not match. field name:%s", nodeValue->name);
    return GMERR_DATA_EXCEPTION;
}

Status CltGetEnumOrIdentityValueByResultNode(
    GmcYangNodeT *info, GmcYangNodeValueT *nodeValue, GmcPropValueT *outPropValue)
{
    if (info->root == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Inv result tree.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (!(info->root->treeType == DM_YANG_CLIENT_SUBTREE || info->root->treeType == DM_YANG_DIFF_CLIENT_TREE)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_INVALID_PARAMETER_VALUE, "Inv result tree type, type:%" PRIu32 ".", info->root->treeType);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (info->node.schema == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Inv result tree schema.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (info->type != GMC_YANG_FIELD) {
        bool isNode = false;
        Status ret = CltPartialNodeType(info, &isNode);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (isNode) {
            DB_LOG_AND_SET_LASERR(
                GMERR_INVALID_PARAMETER_VALUE, "Inv current node type, type:%" PRIu32 ".", info->type);
            return GMERR_INVALID_PARAMETER_VALUE;
        }
    }
    return PollingPropertiesAndGetValue(info->node.schema, nodeValue, outPropValue);
}

static Status CltSetOutPropValueByInPropValue(GmcPropValueT *inPropValue, GmcPropValueT *outPropValue)
{
    outPropValue->propertyId = inPropValue->propertyId;
    outPropValue->type = inPropValue->type;
    int32_t ret = strcpy_s(outPropValue->propertyName, GMC_PROPERTY_NAME_MAX_LEN, inPropValue->propertyName);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "Copy property name.");
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}

Status CltGetEnumOrIdentityInfoByType(DmPropertySchemaT *propertySchema, GmcConvertTypeE convertType,
    GmcPropValueT *inPropValue, GmcPropValueT *outPropValue)
{
    DmValueT tmpValue = {0};
    Status ret;
    errno_t err;
    switch (convertType) {
        case GMC_CONVERT_INT_TO_STR:
            ret = DmYangConvertProperty(propertySchema, *(const int32_t *)inPropValue->value, &tmpValue);
            if (ret != GMERR_OK) {
                return ret;
            }
            err = memcpy_s(outPropValue->value, outPropValue->size, tmpValue.value.strAddr, tmpValue.value.length);
            if (err != EOK) {
                DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Copy outPropertyValue.");
                return GMERR_MEMORY_OPERATE_FAILED;
            }
            outPropValue->size = tmpValue.value.length;
            break;
        case GMC_CONVERT_STR_TO_INT:
            ret = DmYangConvertPropertyNameToValue(propertySchema, (const char *)inPropValue->value, &tmpValue);
            if (ret != GMERR_OK) {
                return ret;
            }
            err = memcpy_s(outPropValue->value, outPropValue->size, tmpValue.value.strAddr, tmpValue.value.length);
            if (err != EOK) {
                DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Copy out property value.");
                return GMERR_MEMORY_OPERATE_FAILED;
            }
            outPropValue->size = tmpValue.value.length;
            break;
        case GMC_CONVERT_BUTT:
        default:
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "convertType is not valid!");
            return GMERR_INVALID_PARAMETER_VALUE;
    }
    ret = CltSetOutPropValueByInPropValue(inPropValue, outPropValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status CltGetNodeSchemaByGmcNode(GmcNodeT *node, DmSchemaT **schema)
{
    DB_POINTER2(node, schema);
    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(node->stmt);
    if (cltCataLabel == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "CltCataLabel is unexpected!");
        return GMERR_DATA_EXCEPTION;
    }
    if (CltIsRootNode(node)) {
        DmVertexLabelT *vertexLabel = cltCataLabel->vertexLabel;
        *schema = MEMBER_PTR(vertexLabel->metaVertexLabel, schema);
    } else {
        *schema = DmGetSchemaByUniqueId(MEMBER_PTR(cltCataLabel->vertexLabel->metaVertexLabel, schema),
            node->dmNode->nodeDesc->yangInfoDesc->uniqueNodeId);
    }
    if (*schema == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get node schema.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    return GMERR_OK;
}

uint32_t CltYangGetNodePropeNumExcludeSysPrope(GmcNodeT *node)
{
    DB_POINTER(node);
    uint32_t number = 0;
    if (CltIsRootNode(node)) {
        number = RecordGetPropeNumExcludeSysPrope(node->dmVertex->record->recordDesc);
    } else {
        number = RecordGetPropeNumExcludeSysPrope(node->dmNode->currRecord->recordDesc);
    }
    return number;
}
