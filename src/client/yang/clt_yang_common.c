/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: source file for yang diff tree
 * Author:
 * Create: 2022-7-30
 */
#include "clt_yang_common.h"
#include "clt_batch.h"
#include "clt_utils.h"
#include "gmc_yang_types.h"
#include "dm_data_pattern.h"
#include "dm_yang_interface.h"
#include "dm_yang_diff.h"
#include "dm_yang_subtree.h"
#include "dm_yang_union.h"
#include "dm_meta_basic_in.h"
#include "clt_meta_cache.h"
#include "clt_resource.h"
#include "clt_subtree_edit.h"
#include "dm_data_print_inner.h"
#include "clt_yang.h"
#include "clt_msg.h"
#include "adpt_locator.h"
#include "clt_graph_ddl_vertex.h"

static Status ParseCursor(FixBufferT *buf, GmcFetchRetT *fetchRet)
{
    uint32_t hasCursor = 0;
    Status ret = SecureFixBufGetUint32(buf, &hasCursor);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (hasCursor == DM_YANG_NO_CURSOR) {
        // 没有返回游标信息，说明查询已结束
        fetchRet->isEnd = true;
        uint32_t noUseIndex;
        return SecureFixBufGetUint32(buf, &noUseIndex);  // 略过过滤树索引的 uint32_t，不会再次查询所以不用到了
    }
    // 请求级别memctx，内存申请成功会在请求结束后由用户调用接口GmcYangFreeFetchRet释放
    // 中间过程失败会在外层直接调用FreeFetchRet释放memCtx，这里不再进行释放
    CltCursorT *cursor = (CltCursorT *)DbDynMemCtxAlloc(fetchRet->memCtx, sizeof(CltCursorT));
    if (cursor == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc cursor.");
        return GMERR_OUT_OF_MEMORY;
    }
    ret = SecureFixBufGetUint32(buf, &cursor->index);
    if (ret != GMERR_OK) {
        return ret;
    }
    fetchRet->cursor = cursor;
    return GMERR_OK;
}

#if defined(EXPERIMENTAL_NERGC)
static void CltInitYangIdxOffset(DmVlIndexLabelT *index)
{
    // indexNameOffset是否等于0承担了CltGetPosIndexKey内的判断的功能
    index->indexNameOffset = (uint16_t)MemOffset((uint8_t *)&(index->indexName), index);
    index->commentOffset = 0;
    index->idxLabelBase.srcLabelNameOffset = 0;
    index->propIdsOffset = 0;
    index->parentIdsOffset = 0;
    index->propertiesOffset = 0;
    for (uint32_t i = 0; i < index->propeNum; ++i) {
        index->properties[i].nameOffset = 0;
    }
}

static void CltInitYangLabelSchemaOffset(MetaVertexLabelT *metaVertexLabel)
{
    if (metaVertexLabel->schema != NULL) {
        metaVertexLabel->schema->propertiesOffset = 0;
        metaVertexLabel->schema->superFieldsOffset = 0;
        // yangInfoOffset是否等于0承担了节点类型判断的功能
        if (JudgeIsNotNull(&(metaVertexLabel->schema->yangInfoOffset), metaVertexLabel->schema->yangInfo)) {
            metaVertexLabel->schema->yangInfoOffset =
                MemOffset((uint8_t *)&(metaVertexLabel->schema->yangInfo), metaVertexLabel->schema);
        }
        metaVertexLabel->schema->nodesOffset = 0;
        for (uint32_t i = 0; i < metaVertexLabel->schema->propeNum; ++i) {
            metaVertexLabel->schema->properties[i].nameOffset = 0;
            metaVertexLabel->schema->properties[i].commentOffset = 0;
            metaVertexLabel->schema->properties[i].defaultValueOffset = 0;
            metaVertexLabel->schema->properties[i].constraintOffset = 0;
            metaVertexLabel->schema->properties[i].clauseInfoOffset = 0;
            metaVertexLabel->schema->properties[i].attributeInfoOffset = 0;
        }
    }
}

static void CltInitYangLabelOffset(DmVertexLabelT *label)
{
    label->metaCommon.metaNameOffset = 0;

    label->metaVertexLabel->topRecordNameOffset = 0;
    label->metaVertexLabel->commentOffset = 0;
    label->metaVertexLabel->schemaOffset = 0;
    label->metaVertexLabel->pkIndexOffset = 0;
    label->metaVertexLabel->secIndexesOffset = 0;
    label->metaVertexLabel->labelJsonOffset = 0;
    label->metaVertexLabel->configJsonOffset = 0;

    CltInitYangLabelSchemaOffset(label->metaVertexLabel);

    if (label->metaVertexLabel->pkIndex != NULL) {
        CltInitYangIdxOffset(label->metaVertexLabel->pkIndex);
    }

    if (label->metaVertexLabel->secIndexes != NULL && label->metaVertexLabel->secIndexNum != 0) {
        for (uint32_t i = 0; i < label->metaVertexLabel->secIndexNum; i++) {
            CltInitYangIdxOffset(&label->metaVertexLabel->secIndexes[i]);
        }
    }
    label->commonInfo->creatorOffset = 0;
    label->commonInfo->resColInfoOffset = 0;
    label->commonInfo->datalogLabelInfoOffset = 0;
    label->commonInfo->autoIncrPropInfoOffset = 0;
}

static Status CltRecvYangCsLabel(FixBufferT *buf, CltCataLabelT **cataLabel)
{
    *cataLabel = (CltCataLabelT *)DbDynMemCtxAlloc(g_gmdbCltInstance.cltCommCtx, sizeof(CltCataLabelT));
    if (*cataLabel == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc memctx worthless when client get vertexLabel, size: %" PRIu32 ".",
            (uint32_t)sizeof(CltCataLabelT));
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(*cataLabel, sizeof(CltCataLabelT), 0x00, sizeof(CltCataLabelT));

    TextT text = {0};
    Status ret = FixBufGetObject(buf, &text);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(g_gmdbCltInstance.cltCommCtx, *cataLabel);
        *cataLabel = NULL;
        return ret;
    }

    ret = DmDeSerializeVertexLabelWithMemCtx(
        g_gmdbCltInstance.cltCommCtx, (uint8_t *)text.str, text.len, &((*cataLabel)->vertexLabel));
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(g_gmdbCltInstance.cltCommCtx, *cataLabel);
        *cataLabel = NULL;
        return ret;
    }
    CltInitYangLabelOffset((*cataLabel)->vertexLabel);
    return GMERR_OK;
}
#endif

Status CltYangGetShmCataLabel(GmcConnT *conn, FixBufferT *buf, CltCataLabelT **shmCataLabel)
{
    CltCataLabelT *cataLabel = NULL;
    Status ret;
#if defined(EXPERIMENTAL_NERGC)
    if (DbIsTcp()) {  // deserialize vertexLabel into dyn
        ret = CltRecvYangCsLabel(buf, &cataLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        ret = RecvGetVertexLabel(buf, &cataLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
#else
    ret = RecvGetVertexLabel(buf, &cataLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
#endif
    ret = CltCataSaveVertexLabel(&cataLabel, (uint16_t)conn->instanceId, 1, DM_SCHEMA_MIN_VERSION);
    if (ret != GMERR_OK) {
        return ret;
    }
    *shmCataLabel = cataLabel;
    return GMERR_OK;
}

Status AllocCltLabel(DbMemCtxT *memCtx, CltCataLabelT *cataLabel, DmTreeDescT *desc, CltLabelT **label)
{
    // 请求级别memctx，内存申请成功会在请求结束后由用户调用接口GmcYangFreeFetchRet释放
    // 中间过程失败会在外层直接调用FreeFetchRet释放memCtx，这里不再进行释放
    CltLabelT *cltLabel = (CltLabelT *)DbDynMemCtxAlloc(memCtx, sizeof(CltLabelT));
    if (cltLabel == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc cltLabel.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(cltLabel, sizeof(CltLabelT), 0, sizeof(CltLabelT));
    cltLabel->desc = desc;
    Status ret = DmCreateEmptyVertexWithMemCtx(memCtx, cataLabel->vertexLabel, &cltLabel->vertex);
    cltLabel->cataLabel = cataLabel;
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(memCtx, cltLabel);
        return ret;
    }
    *label = cltLabel;
    return GMERR_OK;
}

Status ParseMetadata(GmcConnT *conn, FixBufferT *buf, GmcFetchRetT *fetchRet)
{
    DB_POINTER2(buf, fetchRet);
    uint32_t labelCount;
    Status ret = FixBufGetUint32(buf, &labelCount);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbOamapInit(&fetchRet->vertexLabelMap, labelCount, DbOamapUint32Compare, fetchRet->memCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmTreeDescT *desc = NULL;
    CltLabelT *cltLabel = NULL;
    CltCataLabelT *cataLabel = NULL;
    for (uint32_t i = 0; i < labelCount; i++) {
        ret = CltYangGetShmCataLabel(conn, buf, &cataLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
        TextT text = {0};
        ret = FixBufGetObject(buf, &text);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DmDeSerializeTreeDescWithMemCtx(
            fetchRet->memCtx, (uint8_t *)text.str, text.len, cataLabel->vertexLabel, &desc);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = AllocCltLabel(fetchRet->memCtx, cataLabel, desc, &cltLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DbOamapInsert(&fetchRet->vertexLabelMap, cataLabel->vertexLabel->metaCommon.metaId,
            &(cataLabel->vertexLabel->metaCommon.metaId), cltLabel, NULL);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status ParseSingleTreeMetadata(
    GmcFetchRetT *fetchRet, FixBufferT *buf, DmSubtreeT *parent, DmTreeDescT **treeDesc, DmVertexT **vertex)
{
    uint32_t type = 0;
    Status ret = FixBufGetUint32(buf, &type);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t id = 0;
    // 如果是vertex树id代表的labelMetaId，node树存代表的是节点在父节点的下标索引
    ret = FixBufGetUint32(buf, &id);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (type == DM_TREE_DESC_VERTEX) {
        CltLabelT *cltLabel = (CltLabelT *)DbOamapLookup(&fetchRet->vertexLabelMap, id, &id, NULL);
        if (cltLabel == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "unsound label id %" PRIu32 ".", id);
            return GMERR_DATA_EXCEPTION;
        }
        *treeDesc = cltLabel->desc;
        *vertex = cltLabel->vertex;
    } else {
        if (parent == NULL || id >= parent->desc->childNum) {
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "null parent or unsound id.");
            return GMERR_DATA_EXCEPTION;
        }
        *treeDesc = parent->desc->childDesc[id];
    }
    return GMERR_OK;
}

static Status ParseSingleTree(GmcFetchRetT *fetchRet, FixBufferT *buf, DmSubtreeT *parent, DmSubtreeT **tree)
{
    DmTreeDescT *treeDesc = NULL;
    DmVertexT *vertex = NULL;
    Status ret = ParseSingleTreeMetadata(fetchRet, buf, parent, &treeDesc, &vertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmSubtreeT **currTree = tree;  // 表示当前处理的树节点，在循环中会切换到兄弟树
    bool isExist = true;  // 表示节点是否存在，在循环中用于判断退出，此处使用while循环是为了减少递归调用深度，避免栈溢出
    while (isExist) {
        TextT subtreeBuf = {0};
        ret = FixBufGetObject(buf, &subtreeBuf);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DmDeSerializeSubtreeWithMemCtx(
            fetchRet->memCtx, (uint8_t *)subtreeBuf.str, subtreeBuf.len, treeDesc, currTree);
        if (ret != GMERR_OK) {
            return ret;
        }
        (*currTree)->vertex = vertex;
        ret = DmInitChildSubtree(parent, (*currTree), treeDesc);
        if (ret != GMERR_OK) {
            return ret;
        }
        // 获取子树和兄弟树的的flag信息,代表子树或兄弟树是否存在
        uint8_t *treeFlag = FixBufGetData(buf, ((*currTree)->desc->childNum + 1) * sizeof(uint8_t));
        if (treeFlag == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "get tree flag.");
            return GMERR_DATA_EXCEPTION;
        }
        for (uint32_t i = 0; i < (*currTree)->desc->childNum; ++i) {
            if (treeFlag[i] == 0) {
                continue;
            }
            ret = ParseSingleTree(fetchRet, buf, (*currTree), &(*currTree)->childTree[i]);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        isExist = treeFlag[(*currTree)->desc->childNum];
        currTree = &(*currTree)->nextTree;
    }
    return ret;
}

Status AllocYangTree(GmcFetchRetT *fetchRet, GmcYangTreeT **tree)
{
    *tree = NULL;
    DbMemCtxArgsT args = {0};
    args.liteModOn = true;
    // memCtx用途：用于Yang场景，subtree 或 diff 查询返回结果的内存等资源的申请
    // 生命周期：单消息级别
    // 释放方式：异常就近释放，提供兜底释放措施
    // 兜底清空措施：单独提供销毁该memCtx的对外接口：GmcYangFreeFetchRet
    DbMemCtxT *treeMemCtx = (DbMemCtxT *)DbCreateDynMemCtx(fetchRet->memCtx, false, "yangTreeMemCtxT", &args);
    if (treeMemCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Create memCtx.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    // 请求级别memctx，每颗结果树都有自己的memctx，内存申请成功会在请求结束后由用户调用接口GmcYangFreeTree释放
    // 中间过程失败会在外层直接调用FreeFetchRet释放父memCtx，这里不再进行释放
    GmcYangTreeT *tmpTree = (GmcYangTreeT *)DbDynMemCtxAlloc(treeMemCtx, sizeof(GmcYangTreeT));
    if (tmpTree == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc tmpTree.");
        DbDeleteDynMemCtx(treeMemCtx);
        return GMERR_OUT_OF_MEMORY;
    }
    tmpTree->memCtx = treeMemCtx;
    *tree = tmpTree;
    return GMERR_OK;
}

static Status ParseTreeReplyInner(
    FixBufferT *buf, uint32_t filterMode, uint32_t count, GmcFetchRetT *fetchRet, const GmcYangTreeT **treeReply)
{
    uint32_t treeIsNull;
    DmSubtreeT *result = NULL;
    GmcYangTreeT *tree = NULL;
    uint32_t defaultMode;
    uint32_t jsonFlag;
    for (uint32_t i = 0; i < count; i++) {
        Status ret = SecureFixBufGetUint32(buf, &treeIsNull);
        if (ret != GMERR_OK) {
            return ret;
        }
        if ((bool)treeIsNull) {
            treeReply[i] = NULL;
            continue;
        }
        ret = SecureFixBufGetUint32(buf, &defaultMode);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = SecureFixBufGetUint32(buf, &jsonFlag);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = ParseSingleTree(fetchRet, buf, NULL, &result);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Parse subtree.");
            return ret;
        }
        ret = AllocYangTree(fetchRet, &tree);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Alloc subtree.");
            return ret;
        }
        DB_POINTER(tree);
        tree->subtree = result;
        tree->treeType = DM_YANG_CLIENT_SUBTREE;
        tree->defaultMode = defaultMode;
        tree->jsonFlag = jsonFlag;
        tree->filterMode = filterMode;
        treeReply[i] = tree;
    }
    return GMERR_OK;
}

Status ParseTreeReply(GmcConnT *conn, FixBufferT *buf, uint32_t filterMode, GmcFetchRetT *fetchRet)
{
    Status ret = ParseMetadata(conn, buf, fetchRet);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Parse metadata.");
        return ret;
    }
    DbMemCtxT *memCtx = fetchRet->memCtx;
    uint32_t count = fetchRet->count;
    uint32_t allocSize = (uint32_t)sizeof(GmcYangTreeT *) * count;
    // 请求级别memctx，内存申请成功会在请求结束后由用户调用接口GmcYangFreeFetchRet释放
    // 中间过程失败会在外层直接调用FreeFetchRet释放memCtx，这里不再进行释放
    const GmcYangTreeT **treeReply = (const GmcYangTreeT **)DbDynMemCtxAlloc(memCtx, allocSize);
    if (treeReply == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc size(%" PRIu32 ")", allocSize);
        return GMERR_OUT_OF_MEMORY;
    }
    ret = ParseTreeReplyInner(buf, filterMode, count, fetchRet, treeReply);
    if (ret != GMERR_OK) {
        return ret;
    }
    fetchRet->treeReply = (const GmcYangTreeT **)treeReply;
    return GMERR_OK;
}

Status ParseJsonReplyFromObj(FixBufferT *buf, GmcFetchRetT *fetchRet)
{
    uint32_t count = fetchRet->count;
    // 请求级别memctx，内存申请成功会在请求结束后由用户调用接口GmcYangFreeFetchRet释放
    // 中间过程失败会在外层直接调用FreeFetchRet释放memCtx，这里不再进行释放
    const char **jsonReplys = (const char **)DbDynMemCtxAlloc(fetchRet->memCtx, sizeof(char *) * count);
    if (jsonReplys == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc json reply.");
        return GMERR_OUT_OF_MEMORY;
    }
    Status ret;
    for (uint32_t i = 0; i < count; i++) {
        char *tmpJson = NULL;
        if (fetchRet->treeReply[i] == NULL) {
            // 这里用于生成空的json串‘{}’
            ret = DmCreateSubtreeText(fetchRet->memCtx, NULL, 0, 0, &tmpJson);
        } else {
            ret = DmCreateSubtreeText(fetchRet->memCtx, fetchRet->treeReply[i]->subtree,
                fetchRet->treeReply[i]->jsonFlag, fetchRet->treeReply[i]->defaultMode, &tmpJson);
        }
        if (ret != GMERR_OK) {
            return ret;
        }
        jsonReplys[i] = tmpJson;
    }
    fetchRet->jsonReply = jsonReplys;
    return GMERR_OK;
}

Status ParseJsonReplyFromBuf(FixBufferT *buf, GmcFetchRetT *fetchRet)
{
    uint32_t count = fetchRet->count;
    // 请求级别memctx，内存申请成功会在请求结束后由用户调用接口GmcYangFreeFetchRet释放
    // 中间过程失败会在外层直接调用FreeFetchRet释放memCtx，这里不再进行释放
    const char **jsonReply = (const char **)DbDynMemCtxAlloc(fetchRet->memCtx, sizeof(char *) * count);
    if (jsonReply == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc reply json.");
        return GMERR_OUT_OF_MEMORY;
    }
    TextT result = {0};
    Status ret;
    for (uint32_t i = 0; i < count; i++) {
        ret = SecureFixBufGetText(buf, &result);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "get text from buf, index: %" PRIu32 ".", i);
            return ret;
        }
        // 请求级别memctx，内存申请成功会在请求结束后由用户调用接口GmcYangFreeFetchRet释放
        // 中间过程失败会在外层直接调用FreeFetchRet释放memCtx，这里不再进行释放
        char *tmpJson = (char *)DbDynMemCtxAlloc(fetchRet->memCtx, result.len);
        if (tmpJson == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "index: %" PRIu32 ".", i);
            return GMERR_OUT_OF_MEMORY;
        }
        errno_t errRet = memcpy_s(tmpJson, result.len, result.str, result.len);
        if (errRet != EOK) {
            DB_LOG_AND_SET_LASERR(
                GMERR_FIELD_OVERFLOW, "memory copy str, index:%" PRIu32 ", len:%" PRIu32 ".", i, result.len);
            return GMERR_FIELD_OVERFLOW;
        }
        jsonReply[i] = tmpJson;
    }
    fetchRet->jsonReply = jsonReply;
    return GMERR_OK;
}

Status CltParseFetchRet(GmcConnT *conn, FixBufferT *buf, GmcFetchRetT *fetchRet)
{
    DB_POINTER2(buf, fetchRet);
    Status ret = ParseCursor(buf, fetchRet);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t filterMode = 0;
    ret = SecureFixBufGetUint32(buf, &filterMode);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t count = 0;
    ret = SecureFixBufGetUint32(buf, &count);
    if (ret != GMERR_OK) {
        return ret;
    }
    fetchRet->count = count;
    // 结构化数据模式或model模式
    if (filterMode == (uint32_t)GMC_FETCH_JSON_RFC7951 || filterMode == (uint32_t)GMC_FETCH_FULL_JSON_RFC7951) {
        fetchRet->isJson = true;
        // buf转json，json由服务端转好了
        return ParseJsonReplyFromBuf(buf, fetchRet);
    }

    ret = ParseTreeReply(conn, buf, filterMode, fetchRet);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (filterMode == (uint32_t)GMC_FETCH_JSON || filterMode == (uint32_t)GMC_FETCH_FULL_JSON) {
        fetchRet->isJson = true;
        // obj转json
        return ParseJsonReplyFromObj(buf, fetchRet);
    }
    return GMERR_OK;
}

static void EncodeTreeFlag(const DmSubtreeT *tree, uint8_t *buf)
{
    for (uint32_t i = 0; i < tree->desc->childNum; i++) {
        buf[i] = tree->childTree[i] != NULL;
    }
    buf[tree->desc->childNum] = tree->nextTree != NULL;
}

Status CltPutSubtree2Buf(DmSubtreeT *tree, FixBufferT *buf)
{
    DB_POINTER(buf);
    if (tree == NULL) {
        return GMERR_OK;
    }
    // 对list节点只需要在填入第一个tree节点时填入labelId, 不必每个节点都填labelId
    Status ret = SecureFixBufPutUint32(buf, (uint32_t)tree->desc->type);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t id = DmIsVertexSubtree(tree) ? tree->desc->vertexLabel->metaCommon.metaId : tree->desc->index;
    ret = SecureFixBufPutUint32(buf, id);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmSubtreeT *currTree = tree;
    while (currTree != NULL) {
        ret = DmFillSubtreeBuf(currTree, buf);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Put subtree to buf, name=%s.", DmGetSubtreeName(currTree));
            return ret;
        }
        // treeFlag用来存子树和nextTree的状态，每个字节表示对应的树是否存在，后续可优化成使用一个bit来表示
        uint32_t treeFlagLen = (currTree->desc->childNum + 1) * (uint32_t)sizeof(uint8_t);
        uint32_t offset;
        ret = SecureFixBufReserveDataOffset(buf, treeFlagLen, &offset);
        if (ret != GMERR_OK) {
            return ret;
        }
        uint8_t *treeFlagBuf = (uint8_t *)SecureFixBufOffsetToAddr(buf, offset);
        // 由于FixBufReserveData函数中的buf可能是可以扩展的,后续的循环打包树的过程中可能会扩展buf，如果buf扩展，
        // treeFlag指针就不可用了，所以需要在这里就把状态数据给设置完成。
        EncodeTreeFlag(currTree, treeFlagBuf);

        // 打包子树
        for (uint32_t i = 0; i < currTree->desc->childNum; i++) {
            if (currTree->childTree[i] == NULL) {
                continue;
            }
            ret = CltPutSubtree2Buf(currTree->childTree[i], buf);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        currTree = currTree->nextTree;
    }
    return GMERR_OK;
}

uint32_t GetYangTreeValidCount(GmcFetchRetT *fetchRet, DmYangTreeT **treeList)
{
    uint32_t validCount = 0;
    for (uint32_t i = 0; i < fetchRet->count; i++) {
        if (treeList[i] == NULL || !DmYangTreeIsMatchOrPath(treeList[i])) {
            continue;
        }
        validCount++;
    }
    return validCount;
}

void InitYangTree(DmDecodeParamT *decodeParam, DmYangTreeTypeE treeType, DmYangTreeT *tree, GmcYangTreeT *yangTree)
{
    yangTree->tree = tree;
    yangTree->treeType = treeType;
    yangTree->vertexMap = decodeParam->vertexMap;
    yangTree->isExplicit = decodeParam->isExplicit;
    yangTree->cacheList = (DmYangVertexCacheListT *)(yangTree + 1);
}

Status CltConvertYangTree(DmDecodeParamT *decodeParam, GmcFetchRetT *fetchRet, DmYangTreeT **treeList,
    DmYangTreeTypeE treeType, const GmcYangTreeT ***yangTrees)
{
    DB_POINTER3(decodeParam, treeList, yangTrees);
    uint32_t count = fetchRet->count;
    uint32_t validCount = GetYangTreeValidCount(fetchRet, treeList);
    fetchRet->count = validCount;
    if (validCount == 0) {
        return GMERR_OK;
    }
    uint32_t allocSize = (uint32_t)sizeof(GmcYangTreeT *) * validCount;
    DbMemCtxT *memCtx = decodeParam->memCtx;
    // 请求级别memctx，内存申请成功会在请求结束后由用户调用接口GmcYangFreeFetchRet释放
    // 中间过程失败会在外层直接调用FreeFetchRet释放memCtx，这里不再进行释放
    *yangTrees = (const GmcYangTreeT **)DbDynMemCtxAlloc(memCtx, allocSize);
    if (*yangTrees == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "allocSize(%" PRIu32 ")", allocSize);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(*yangTrees, allocSize, 0, allocSize);
    uint32_t validIndex = 0;
    for (uint32_t i = 0; i < count; i++) {
        if (treeList[i] == NULL || !DmYangTreeIsMatchOrPath(treeList[i])) {
            continue;
        }
        // memCtx用途：用于Yang场景，subtree 或 diff 查询返回结果的内存等资源的申请
        // 生命周期：单消息级别
        // 释放方式：异常就近释放，提供兜底释放措施
        // 兜底清空措施：单独提供销毁该memCtx的对外接口：GmcYangFreeFetchRet
        DbMemCtxArgsT args = {0};
        args.liteModOn = true;
        DbMemCtxT *treeMemCtx = (DbMemCtxT *)DbCreateDynMemCtx(memCtx, false, "yangTreeMemCtxT", &args);
        if (treeMemCtx == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "create diff trees memCtx.");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        // 请求级别memctx，每棵结果树都有自己memctx，内存申请成功会在请求结束后由用户调用接口GmcYangFreeFetchRet释放
        // 中间过程失败会在外层直接调用FreeFetchRet释放父memCtx，这里不再进行释放
        GmcYangTreeT *tmpYangInfo =
            (GmcYangTreeT *)DbDynMemCtxAlloc(treeMemCtx, sizeof(GmcYangTreeT) + sizeof(DmYangVertexCacheListT));
        if (tmpYangInfo == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc yang info.");
            return GMERR_OUT_OF_MEMORY;
        }
        tmpYangInfo->memCtx = treeMemCtx;
        InitYangTree(decodeParam, treeType, treeList[i], tmpYangInfo);
        (*yangTrees)[validIndex++] = tmpYangInfo;
    }
    return GMERR_OK;
}

DmVlIndexLabelT *GetPkIndexFromYangNode(const GmcYangNodeT *currNode)
{
    switch (currNode->root->treeType) {
        case DM_YANG_CLIENT_SUBTREE:
            return MEMBER_PTR(currNode->node.subtree->desc->vertexLabel->metaVertexLabel, pkIndex);
        case DM_YANG_DIFF_CLIENT_TREE:
            return MEMBER_PTR(currNode->node.yangTree->vertexInfo->vertexLabel->metaVertexLabel, pkIndex);
        case DM_YANG_SERVER_DIFF_VERTEX_TREE:
        case DM_YANG_SERVER_DIFF_NODE_TREE:
        case DM_YANG_INVALID:
        default:
            return NULL;
    }
}

bool IsLeafListKeyFromDefaultValue(DmVertexT *vertex, uint32_t indexPropId)
{
    bool isSetByServer = false;
    bool isEqualDefault = false;
    Status ret = DmGetLeafListDefaultFlagsFromVertex(vertex, &isSetByServer, &isEqualDefault);
    if (ret != GMERR_OK) {
        DB_LOG_WARN_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Unable to get default flags.");
    }
    return (indexPropId != 0) && isSetByServer && isEqualDefault;
}

Status CltYangGetDmValueWithCopy(const DmValueT *dmValue, void *value, uint32_t capacity, uint32_t *type, bool *null)
{
    DmValueT unpackedValue = {0};
    Status ret = GMERR_OK;
    if (DmIsYangUnionType(dmValue->type)) {
        ret = DmYangUnpackUnionDmValue(dmValue, &unpackedValue);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "unpack value");
            return ret;
        }
    } else {
        unpackedValue = *dmValue;
    }
    // union类型的type为真实数据类型的type，empty的type为empty本身
    *type = unpackedValue.type;
    if ((ret = GetDmValueWithCopy(&unpackedValue, value, capacity, null)) != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Copy value buf.");
        return ret;
    }
    return GMERR_OK;
}

Status GetPropValue(const GmcYangNodeT *currNode, DmVertexT *vertex, uint32_t indexPropId, GmcYangNodeValueT *propValue)
{
    DmVlIndexLabelT *pkIndex = GetPkIndexFromYangNode(currNode);
    if (SECUREC_UNLIKELY(pkIndex == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "null PK index.");
        return GMERR_DATA_EXCEPTION;
    }
    DmPropertySchemaT *properties = MEMBER_PTR(pkIndex, properties);
    uint32_t *propIds = MEMBER_PTR(pkIndex, propIds);
    propValue->name = MEMBER_PTR(&properties[propIds[indexPropId]], name);
    propValue->isDefault =
        (currNode->type != GMC_YANG_LEAFLIST) ? false : IsLeafListKeyFromDefaultValue(vertex, indexPropId);
    // 获取字段值大小
    DmValueT dmValue;
    Status ret = DmVertexGetPropeByIdNoCopy(vertex, propIds[indexPropId], &dmValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (dmValue.type == DB_DATATYPE_NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Null key property (%s)", propValue->name);
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    ret = DmVertexGetRealPropeSizeById(vertex, propIds[indexPropId], &propValue->size);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 请求级别memctx，每棵结果树都有自己memctx，内存申请成功会在请求结束后由用户调用接口GmcYangFreeFetchRet释放
    uint8_t *buf = (uint8_t *)DbDynMemCtxAlloc(currNode->root->memCtx, propValue->size);
    if (buf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc buf.");
        return GMERR_OUT_OF_MEMORY;
    }
    bool isNull = false;
    if ((ret = CltYangGetDmValueWithCopy(&dmValue, buf, propValue->size, (uint32_t *)&propValue->type, &isNull)) !=
        GMERR_OK) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Copy value buf.");
        return ret;
    }
    propValue->value = buf;
    return GMERR_OK;
}

Status CltYangNodeGetKeyPropValue(const GmcYangNodeT *currNode, uint32_t indexPropId, GmcYangNodeValueT **value)
{
    DmVertexT *vertex;
    if (currNode->opType == GMC_DIFF_OP_REMOVE && currNode->oldData.vertex != NULL) {
        vertex = currNode->oldData.vertex;
    } else if (currNode->opType == GMC_DIFF_OP_CREATE && currNode->newData.vertex != NULL) {
        vertex = currNode->newData.vertex;
    } else {
        vertex = (currNode->newData.vertex != NULL) ? currNode->newData.vertex : currNode->oldData.vertex;
    }
    if (vertex == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "NULL Vertex");
        return GMERR_DATA_EXCEPTION;
    }
    // 请求级别memctx，每棵结果树都有自己memctx，内存申请成功会在请求结束后由用户调用接口GmcYangFreeFetchRet释放
    // 中间过程失败会在外层由用户调用接口GmcYangFreeFetchRet释放
    GmcYangNodeValueT *propValue =
        (GmcYangNodeValueT *)DbDynMemCtxAlloc(currNode->root->memCtx, sizeof(GmcYangNodeValueT));
    if (propValue == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc prop val.");
        return GMERR_OUT_OF_MEMORY;
    }
    Status ret = GetPropValue(currNode, vertex, indexPropId, propValue);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(currNode->root->memCtx, propValue);
        *value = NULL;
        return ret;
    }
    *value = propValue;
    return GMERR_OK;
}

GmcYangPrevNodeKeyValueT *GetPrevNodeKeyBufFromYangNode(const GmcYangNodeT *currNode, bool isNewData)
{
    return isNewData ? currNode->newData.prePosKey : currNode->oldData.prePosKey;
}

Status CltYangPrevNodeKeyPropValue(
    const GmcYangNodeT *currNode, bool isNewData, uint32_t indexPropId, GmcYangNodeValueT **value)
{
    // 获取字段值
    GmcYangPrevNodeKeyValueT *keyBuf = GetPrevNodeKeyBufFromYangNode(currNode, isNewData);
    if (keyBuf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "NULL KeyBuf, not exist prev node");
        return GMERR_DATA_EXCEPTION;
    }
    *value = &(keyBuf[indexPropId].value);
    return GMERR_OK;
}

static GmcDiffOpTypeE ConvertToClientOpType(DmDiffTreeTypeE op)
{
    uint32_t type[][2] = {
        {DM_DIFF_OPERATION_INVALID, GMC_DIFF_OP_INVALID},
        {DM_DIFF_OPERATION_UPDATE, GMC_DIFF_OP_UPDATE},
        {DM_DIFF_OPERATION_REMOVE, GMC_DIFF_OP_REMOVE},
        {DM_DIFF_OPERATION_CREATE, GMC_DIFF_OP_CREATE},
        {DM_DIFF_OPERATION_UPDATE_PATH, GMC_DIFF_OP_UPDATE},
        {DM_DIFF_OPERATION_REMOVE_PATH, GMC_DIFF_OP_REMOVE},
        {DM_DIFF_OPERATION_CREATE_PATH, GMC_DIFF_OP_CREATE},
        {DM_DIFF_OPERATION_CLONE, GMC_DIFF_OP_UPDATE},
    };

    for (uint32_t i = 0; i < ELEMENT_COUNT(type); i++) {
        if (type[i][0] == (uint32_t)op) {
            return type[i][1];
        }
    }
    return GMC_DIFF_OP_INVALID;
}

static Status CopyKeyValue(
    DbMemCtxT *memCtx, const DmValueT *keyValue, DmPropertySchemaT *property, GmcYangPrevNodeKeyValueT *value)
{
    if (keyValue->type == DB_DATATYPE_NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "NULL key property (%s)", MEMBER_PTR(property, name));
        return GMERR_DATA_EXCEPTION;
    }
    value->value.isDefault = false;
    value->value.name = MEMBER_PTR(property, name);
    value->value.type = (GmcDataTypeE)DmYangGetDmValueType(keyValue);
    uint32_t valueSize =
        DM_TYPE_NEED_MALLOC(keyValue->type) ? keyValue->value.length : DmGetBasicDataTypeLength(keyValue->type);
    value->value.size = DmYangGetValueRealSize(valueSize, (uint32_t)value->value.type);
    // 请求级别memctx，每棵结果树都有自己memctx，内存申请成功会在请求结束后由用户调用接口GmcYangFreeFetchRet释放
    // 中间过程失败会在外层由用户调用接口GmcYangFreeFetchRet释放
    value->buf = (uint8_t *)DbDynMemCtxAlloc(memCtx, value->value.size);
    if (value->buf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "property (%s)", MEMBER_PTR(property, name));
        return GMERR_OUT_OF_MEMORY;
    }
    bool isNull = false;
    Status ret =
        CltYangGetDmValueWithCopy(keyValue, value->buf, value->value.size, (uint32_t *)&value->value.type, &isNull);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Copy property (%s)", MEMBER_PTR(property, name));
        return ret;
    }
    value->value.value = value->buf;
    return GMERR_OK;
}

static Status GetPrevNodeKeyValueFromBuf(
    DbMemCtxT *memCtx, uint8_t *keyBuf, DmVlIndexLabelT *indexLabel, GmcYangPrevNodeKeyValueT **prevKeyValues)
{
    // 请求级别memctx，每棵结果树都有自己memctx，内存申请成功会在请求结束后由用户调用接口GmcYangFreeFetchRet释放
    // 中间过程失败会在外层由用户调用接口GmcYangFreeFetchRet释放
    GmcYangPrevNodeKeyValueT *value =
        (GmcYangPrevNodeKeyValueT *)DbDynMemCtxAlloc(memCtx, sizeof(GmcYangPrevNodeKeyValueT) * indexLabel->propeNum);
    if (value == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc node key val.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(value, sizeof(GmcYangPrevNodeKeyValueT) * indexLabel->propeNum, 0,
        sizeof(GmcYangPrevNodeKeyValueT) * indexLabel->propeNum);
    DmValueT keyValues[DM_MAX_KEY_PROPE_NUM] = {0};
    Status ret = DmGetPropeValuesFromKeyBuf(keyBuf, indexLabel, keyValues);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t *propIds = MEMBER_PTR(indexLabel, propIds);
    for (uint32_t i = 0; i < indexLabel->propeNum; i++) {
        ret = CopyKeyValue(memCtx, &keyValues[i], &MEMBER_PTR(indexLabel, properties)[propIds[i]], &value[i]);
        if (ret != GMERR_OK) {
            break;
        }
    }
    if (ret != GMERR_OK) {
        *prevKeyValues = NULL;
        return ret;
    }
    *prevKeyValues = value;
    return GMERR_OK;
}

Status GetPrevNodeKeyValue(GmcYangNodeT *curr)
{
    DmYangTreeT *currTree = curr->node.yangTree;
    GmcYangDataInfoT *newData = &curr->newData;
    GmcYangDataInfoT *oldData = &curr->oldData;
    newData->vertex = currTree->newVertex;
    if (currTree->vertexInfo->newPrevKeyBuffer != NULL) {
        Status ret = GetPrevNodeKeyValueFromBuf(curr->root->memCtx, currTree->vertexInfo->newPrevKeyBuffer->buf,
            MEMBER_PTR(currTree->vertexInfo->vertexLabel->metaVertexLabel, pkIndex), &newData->prePosKey);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        newData->prePosKey = NULL;
    }
    oldData->vertex = currTree->oldVertex;
    if (currTree->vertexInfo->oldPrevKeyBuffer != NULL) {
        Status ret = GetPrevNodeKeyValueFromBuf(curr->root->memCtx, currTree->vertexInfo->oldPrevKeyBuffer->buf,
            MEMBER_PTR(currTree->vertexInfo->vertexLabel->metaVertexLabel, pkIndex), &oldData->prePosKey);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        oldData->prePosKey = NULL;
    }
    return GMERR_OK;
}

Status CltPartialNodeType(const GmcYangNodeT *yangNode, bool *isNode)
{
    DB_POINTER2(yangNode, isNode);
    if (yangNode->root->treeType != DM_YANG_DIFF_CLIENT_TREE) {
        if (yangNode->node.subtree == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Null subtree.");
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        *isNode = !DmIsVertexSubtree(yangNode->node.subtree);
        return GMERR_OK;
    }
    if (yangNode->node.yangTree == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Null DiffTree.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    *isNode = !DmIsVertexTree(yangNode->node.yangTree);
    return GMERR_OK;
}

void CltSetYangNodeTypeByYangInfo(const DmYangInfoDescT *yangInfoDesc, GmcYangNodeT *curr)
{
    if (DmIsChoiceYangInfo(yangInfoDesc)) {
        curr->type = GMC_YANG_CHOICE;
    } else if (DmIsCaseYangInfo(yangInfoDesc)) {
        curr->type = GMC_YANG_CASE;
    } else if (DmIsContainerYangInfo(yangInfoDesc)) {
        curr->type = GMC_YANG_CONTAINER;
    } else if (DmIsLeafListYangInfo(yangInfoDesc)) {
        curr->type = GMC_YANG_LEAFLIST;
    } else {
        curr->type = GMC_YANG_LIST;
    }
}
// 封装节点级info信息
static Status GetYangNodeInfo(GmcYangNodeT *curr)
{
    // 设置yangNode propertyId设置为无效,超出范围
    DmYangTreeT *currTree = curr->node.yangTree;
    DmVertexT *treeVertex = DmGetYangTreeVertex(currTree);
    DmNodeT *treeNode = DmGetYangTreeNode(currTree);
    if (DmIsVertexTree(currTree) && treeVertex != NULL) {
        curr->name = DmGetYangAliasByShmPtr(currTree->vertexInfo->vertexLabel);
        CltSetYangNodeTypeByYangInfo(treeVertex->vertexDesc->yangInfoDesc, curr);
    } else if (!DmIsVertexTree(currTree) && treeNode != NULL) {
        curr->name = treeNode->nodeDesc->name;
        CltSetYangNodeTypeByYangInfo(treeNode->nodeDesc->yangInfoDesc, curr);
    } else {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Null vertex and node.");
        return GMERR_DATA_EXCEPTION;
    }
    GmcYangDataInfoT *newData = &curr->newData;
    GmcYangDataInfoT *oldData = &curr->oldData;
    curr->opType = ConvertToClientOpType(currTree->op);
    if (DmIsVertexTree(currTree)) {
        Status ret = GetPrevNodeKeyValue(curr);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get prev node key value.");
            return ret;
        }
    } else {
        newData->node = currTree->newNode;
        oldData->node = currTree->oldNode;
    }
    return GMERR_OK;
}

static Status AllocAndInitYangNodeDataValue(DbMemCtxT *memCtx, uint32_t propSize, GmcYangDataInfoT *dataInfo)
{
    // 请求级别memctx，每棵结果树都有自己memctx，内存申请成功会在请求结束后由用户调用接口GmcYangFreeFetchRet释放
    // 中间过程失败会在外层由用户调用接口GmcYangFreeFetchRet释放
    dataInfo->value = (GmcYangNodeValueT *)DbDynMemCtxAlloc(memCtx, sizeof(GmcYangNodeValueT));
    if (dataInfo->value == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc val.");
        return GMERR_OUT_OF_MEMORY;
    }
    dataInfo->value->size = propSize;
    dataInfo->value->isDefault = false;
    dataInfo->value->type = GMC_DATATYPE_NULL;
    dataInfo->buf = DbDynMemCtxAlloc(memCtx, propSize);
    if (dataInfo->buf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc buf.");
        return GMERR_OUT_OF_MEMORY;
    }
    dataInfo->value->value = dataInfo->buf;
    return GMERR_OK;
}

static Status InitYangNodeDataValueByVertex(DbMemCtxT *memCtx, const DmVertexT *oldVertex, const DmVertexT *tmpVertex,
    uint32_t propId, GmcYangDataInfoT *dataInfo)
{
    uint32_t propSize;
    Status ret = DmInitYangNodeDataValueByVertex(oldVertex, tmpVertex, propId, &propSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    return AllocAndInitYangNodeDataValue(memCtx, propSize, dataInfo);
}

static Status InitYangNodeDataValueByNode(
    DbMemCtxT *memCtx, const DmNodeT *oldNode, const DmNodeT *tmpNode, uint32_t propId, GmcYangDataInfoT *dataInfo)
{
    uint32_t propSize;
    Status ret = DmInitYangNodeDataValueByNode(oldNode, tmpNode, propId, &propSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    return AllocAndInitYangNodeDataValue(memCtx, propSize, dataInfo);
}

static Status InitYangVertexFieldValue(
    DbMemCtxT *memCtx, DmVertexT *oldVertex, DmVertexT *newVertex, uint32_t propId, GmcYangNodeT *info)
{
    DmVertexT *tmpVertex = oldVertex ? oldVertex : newVertex;
    if (tmpVertex == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Null vertex.");
        return GMERR_DATA_EXCEPTION;
    }
    Status ret = DmVertexGetPropNameById(tmpVertex, propId, &info->name);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = InitYangNodeDataValueByVertex(memCtx, oldVertex, tmpVertex, propId, &info->oldData);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = InitYangNodeDataValueByVertex(memCtx, newVertex, tmpVertex, propId, &info->newData);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

static Status InitYangNodeFieldValue(
    DbMemCtxT *memCtx, DmNodeT *oldNode, DmNodeT *newNode, uint32_t propId, GmcYangNodeT *info)
{
    DmNodeT *tmpNode = oldNode ? oldNode : newNode;
    if (tmpNode == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Null node");
        return GMERR_DATA_EXCEPTION;
    }
    Status ret = DmNodeGetPropNameById(tmpNode, propId, &info->name);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = InitYangNodeDataValueByNode(memCtx, oldNode, tmpNode, propId, &info->oldData);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = InitYangNodeDataValueByNode(memCtx, newNode, tmpNode, propId, &info->newData);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static Status InitYangFieldValue(const GmcYangNodeT *parent, uint32_t propId, GmcYangNodeT *info)
{
    DB_POINTER2(parent, info);
    const GmcYangTreeT *root = parent->root;
    DbMemCtxT *memCtx = root->memCtx;
    DmYangTreeT *parentTree = parent->node.yangTree;
    if (parentTree == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "init yang field value, Null parentTree.");
        return GMERR_DATA_EXCEPTION;
    }
    if (DmIsVertexTree(parentTree)) {
        DmVertexT *oldVertex = parentTree->oldVertex;
        DmVertexT *newVertex = parentTree->newVertex;
        return InitYangVertexFieldValue(memCtx, oldVertex, newVertex, propId, info);
    }
    DmNodeT *oldNode = parentTree->oldNode;
    DmNodeT *newNode = parentTree->newNode;
    return InitYangNodeFieldValue(memCtx, oldNode, newNode, propId, info);
}

Status InitDiffPropertyInfoWithDefaultByVertex(
    DmVertexDescT *vertexDesc, const DmVertexT *vertex, uint32_t propId, GmcYangDataInfoT *data)
{
    DmValueT dmValue = {0};
    Status ret =
        DmInitDiffPropertyInfoWithDefaultByVertex(vertexDesc, vertex, propId, &dmValue, &data->value->isDefault);
    if (ret != GMERR_OK) {
        return ret;
    }
    bool isNull = false;
    ret = CltYangGetDmValueWithCopy(&dmValue, data->buf, data->value->size, (uint32_t *)&data->value->type, &isNull);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "copy vertex property value.");
        return ret;
    }
    return GMERR_OK;
}

Status InitDiffPropertyInfoByVertex(
    const GmcYangNodeT *parent, uint32_t propId, DmDiffTreeTypeE propOpType, GmcYangNodeT *curr)
{
    DmYangTreeT *parentTree = parent->node.yangTree;
    GmcYangDataInfoT *oldData = &curr->oldData;
    GmcYangDataInfoT *newData = &curr->newData;
    Status ret;
    DmVertexT *vertex = DmGetYangTreeVertex(parentTree);
    // op类型在 DmVertexPropHasNoDiff，DmNodePropHasNoDiff 中判断，op类型确定后，此处 explicit diff不考虑去掉默认值。
    switch (propOpType) {
        case DM_DIFF_OPERATION_CREATE:
            ret = InitDiffPropertyInfoWithDefaultByVertex(vertex->vertexDesc, parentTree->newVertex, propId, newData);
            newData->value->name = curr->name;
            break;
        case DM_DIFF_OPERATION_REMOVE:
            ret = InitDiffPropertyInfoWithDefaultByVertex(vertex->vertexDesc, parentTree->oldVertex, propId, oldData);
            oldData->value->name = curr->name;
            break;
        case DM_DIFF_OPERATION_UPDATE:
            ret = InitDiffPropertyInfoWithDefaultByVertex(vertex->vertexDesc, parentTree->newVertex, propId, newData);
            if (ret != GMERR_OK) {
                break;
            }
            newData->value->name = curr->name;
            ret = InitDiffPropertyInfoWithDefaultByVertex(vertex->vertexDesc, parentTree->oldVertex, propId, oldData);
            oldData->value->name = curr->name;
            break;
        case DM_DIFF_OPERATION_INVALID:
        case DM_DIFF_OPERATION_CLONE:
        default:
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "unsound property operation type.");
            return GMERR_DATA_EXCEPTION;
    }
    return ret;
}

Status InitDiffPropertyInfoWithDefaultByNode(
    DmNodeDescT *nodeDesc, const DmNodeT *node, uint32_t propId, GmcYangDataInfoT *data)
{
    DmValueT dmValue = {0};
    Status ret = DmInitDiffPropertyInfoWithDefaultByNode(nodeDesc, node, propId, &dmValue, &data->value->isDefault);
    if (ret != GMERR_OK) {
        return ret;
    }
    bool isNull = false;
    ret = CltYangGetDmValueWithCopy(&dmValue, data->buf, data->value->size, (uint32_t *)&data->value->type, &isNull);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "copy vertex property value.");
        return ret;
    }
    return GMERR_OK;
}

Status InitDiffPropertyInfoByNode(
    const GmcYangNodeT *parent, uint32_t propId, DmDiffTreeTypeE propOpType, GmcYangNodeT *curr)
{
    DmYangTreeT *parentTree = parent->node.yangTree;
    GmcYangDataInfoT *oldData = &curr->oldData;
    GmcYangDataInfoT *newData = &curr->newData;
    Status ret;
    DmNodeT *node = DmGetYangTreeNode(parentTree);

    switch (propOpType) {
        case DM_DIFF_OPERATION_CREATE:
            ret = InitDiffPropertyInfoWithDefaultByNode(node->nodeDesc, parentTree->newNode, propId, newData);
            newData->value->name = curr->name;
            break;
        case DM_DIFF_OPERATION_REMOVE:
            ret = InitDiffPropertyInfoWithDefaultByNode(node->nodeDesc, parentTree->oldNode, propId, oldData);
            oldData->value->name = curr->name;
            break;
        case DM_DIFF_OPERATION_UPDATE:
            ret = InitDiffPropertyInfoWithDefaultByNode(node->nodeDesc, parentTree->newNode, propId, newData);
            if (ret != GMERR_OK) {
                break;
            }
            newData->value->name = curr->name;
            ret = InitDiffPropertyInfoWithDefaultByNode(node->nodeDesc, parentTree->oldNode, propId, oldData);
            oldData->value->name = curr->name;
            break;
        case DM_DIFF_OPERATION_INVALID:
        case DM_DIFF_OPERATION_CLONE:
        default:
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "incorrent property operation type.");
            return GMERR_DATA_EXCEPTION;
    }
    return ret;
}

// 封装字段级info信息
Status GetYangFieldNodeInfo(const GmcYangNodeT *parent, uint32_t propId, DmDiffTreeTypeE propOpType, GmcYangNodeT *curr)
{
    if (parent == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "get field node info, null parent.");
        return GMERR_DATA_EXCEPTION;
    }
    DmYangTreeT *parentTree = parent->node.yangTree;
    Status ret = InitYangFieldValue(parent, propId, curr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Init yang field value.");
        return ret;
    }
    if (DmIsVertexTree(parentTree)) {
        ret = InitDiffPropertyInfoByVertex(parent, propId, propOpType, curr);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Init property info by vertex.");
            return ret;
        }
    } else {
        ret = InitDiffPropertyInfoByNode(parent, propId, propOpType, curr);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Init property info by node.");
            return ret;
        }
    }
    curr->type = GMC_YANG_FIELD;
    curr->opType = ConvertToClientOpType(propOpType);
    return GMERR_OK;
}

static void SetSchemaByYangNextType(const GmcYangNodeT *parentNode, DmYangCursorT *cursor, GmcYangNodeT *curr)
{
    if (cursor->currType == YANG_NEXT_IS_FIELD) {
        curr->node.schema = parentNode->node.yangTree->diffResultCtx->schema;
    } else {
        if (cursor->curr->yangTree == NULL) {
            curr->node.schema = parentNode->root->tree->diffResultCtx->schema;
        } else {
            curr->node.schema = cursor->curr->yangTree->diffResultCtx->schema;
        }
    }
}

static Status CltYangNodeGetNextNodeInfo(const GmcYangNodeT *parentNode, DmYangCursorT *cursor, GmcYangNodeT **currNode)
{
    Status ret;
    const GmcYangTreeT *root = parentNode->root;
    // 请求级别memctx，每棵结果树都有自己memctx，内存申请成功会在请求结束后由用户调用接口GmcYangFreeFetchRet释放
    // 中间过程失败会在外层由用户调用接口GmcYangFreeFetchRet释放
    // 需要申请GmcYangNode, 并把内部数据转化到currNode中
    GmcYangNodeT *curr = (GmcYangNodeT *)DbDynMemCtxAlloc(root->memCtx, sizeof(GmcYangNodeT));
    if (curr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc yang node.");
        return GMERR_OUT_OF_MEMORY;
    }
    curr->root = root;
    curr->node.yangTree = cursor->curr->yangTree;
    curr->node.propId = cursor->curr->propId;
    curr->node.childIdx = cursor->curr->childIdx;
    SetSchemaByYangNextType(parentNode, cursor, curr);
    // 根据找到的是节点还是字段，填充info
    if (cursor->currType == YANG_NEXT_IS_FIELD) {
        ret = GetYangFieldNodeInfo(parentNode, cursor->curr->propId, cursor->propOpType, curr);
    } else {
        ret = GetYangNodeInfo(curr);
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get node info.");
        DbDynMemCtxFree(root->memCtx, curr);
        return ret;
    }
    *currNode = curr;
    return ret;
}

Status CltYangGetNextNode(const GmcYangNodeT *parentNode, const GmcYangNodeT *prevNode, GmcYangNodeT **currNode)
{
    DB_POINTER2(parentNode, currNode);
    DmYangTreeIterT curr = {0};
    const GmcYangTreeT *root = parentNode->root;
    DmYangCursorT cursor = {0};
    cursor.parent = &parentNode->node;
    if (prevNode == NULL) {
        cursor.prev = NULL;
    } else {
        cursor.prev = &prevNode->node;
    }

    cursor.curr = &curr;
    cursor.isExplicit = parentNode->root->isExplicit;
    cursor.currType = YANG_NEXT_IS_NULL;
    cursor.treeType = root->treeType;
    if (cursor.treeType == DM_YANG_DIFF_CLIENT_TREE && cursor.parent->yangTree != NULL) {
        cursor.parentOpType = cursor.parent->yangTree->op;
    }

    Status ret = DmGetNextYangNodeInfo(root->vertexMap, root->cacheList, &cursor);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 代表已经是最后的diff节点了，返回空，用户结束循环调用
    if (cursor.currType == YANG_NEXT_IS_NULL) {
        *currNode = NULL;
        return GMERR_OK;
    }
    return CltYangNodeGetNextNodeInfo(parentNode, &cursor, currNode);
}

Status CltYangGetRootNode(const GmcYangTreeT *yangTree, GmcYangNodeT **rootNode)
{
    DB_POINTER2(yangTree, rootNode);
    Status ret = GMERR_OK;
    DmYangTreeT *tree = yangTree->tree;
    if (tree == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Null dmYangTree.");
        return GMERR_DATA_EXCEPTION;
    }
    // 请求级别memctx，每棵结果树都有自己memctx，内存申请成功会在请求结束后由用户调用接口GmcYangFreeFetchRet释放
    // 中间过程失败会在外层由用户调用接口GmcYangFreeFetchRet释放
    GmcYangNodeT *root = (GmcYangNodeT *)DbDynMemCtxAlloc(yangTree->memCtx, sizeof(GmcYangNodeT));
    if (root == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc root.");
        return GMERR_OUT_OF_MEMORY;
    }
    root->root = yangTree;
    root->name = DmGetYangAliasByShmPtr(tree->vertexInfo->vertexLabel);
    root->node.yangTree = tree;
    root->node.propId = 0;
    root->node.childIdx = DB_MAX_UINT32;

    DmYangInitVertexCacheList(yangTree->memCtx, yangTree->cacheList);
    ret = DmYangVertexInit(yangTree->cacheList, tree);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "init vertex of root.");
        DbDynMemCtxFree(yangTree->memCtx, root);
        return ret;
    }
    // 获取根节点的yangTree节点信息
    ret = GetYangNodeInfo(root);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get node info of root.");
        DbDynMemCtxFree(yangTree->memCtx, root);
        return ret;
    }
    *rootNode = root;
    return GMERR_OK;
}

GmcDiffOpTypeE CltYangNodeGetOpType(const GmcYangNodeT *node)
{
    return node->opType;
}

GmcYangNodeValueT *CltYangNodeGetPropValue(const GmcYangNodeT *node, bool isNew)
{
    return isNew ? node->newData.value : node->oldData.value;
}

bool CltYangIsExistTreePrevNode(const GmcYangNodeT *node, bool isNew)
{
    return isNew ? (node->newData.prePosKey != NULL) : (node->oldData.prePosKey != NULL);
}

static CltCataLabelT *FindVertexLabelFromMap(DbOamapT *vertexLabelMap, uint32_t labelId)
{
    return (CltCataLabelT *)DbOamapLookup(vertexLabelMap, labelId, &labelId, NULL);
}

static Status GetVertexLabelFromMap(DbOamapT *vertexLabelMap, FixBufferT *buf, CltCataLabelT **label)
{
    *label = NULL;
    uint32_t labelId;
    Status ret = FixBufGetUint32(buf, &labelId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get labelId from buffer.");
        return ret;
    }
    *label = FindVertexLabelFromMap(vertexLabelMap, labelId);
    if (*label == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_TABLE, "find label from map.");
        return GMERR_UNDEFINED_TABLE;
    }
    return GMERR_OK;
}

Status DecodeSingleYangTree(
    DmDecodeParamT *decodeParam, FixBufferT *buf, DmYangTreeT *parent, uint32_t childIdx, DmYangTreeT **tree);
static Status DecodeChildTree(DmDecodeParamT *decodeParam, FixBufferT *buf, DmYangTreeT *parent, bool *hasBrother)
{
    Status ret = GMERR_OK;
    // 获取子树和兄弟树的的flag信息
    uint8_t *treeFlag = FixBufGetData(buf, (parent->childNum + 1) * sizeof(uint8_t));
    if (treeFlag == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "get tree flag when decode.");
        return GMERR_DATA_EXCEPTION;
    }
    // 解析子树
    for (uint32_t i = 0; i < parent->childNum; i++) {
        if ((bool)treeFlag[i]) {
            ret = DecodeSingleYangTree(decodeParam, buf, parent, i, &parent->childTreeCtx[i].tree);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }

    // 最后一个treeFlag表示兄弟树nextTree的存在与否
    *hasBrother = (bool)treeFlag[parent->childNum];
    return GMERR_OK;
}

static Status DecodeYangTreeBasicInfo(
    DbOamapT *vertexLabelMap, FixBufferT *buf, DmYangTreeT *parent, DmBasicYangTreeInfoT *basicInfo)
{
    Status ret;

    uint16_t uniqNodeId = 0;
    ret = FixBufGetUint16(buf, &uniqNodeId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get uniqueNodeId.");
        return ret;
    }
    // 从buf中取出LabelId，根据LabelId从缓存中获取vertexLabel
    if (uniqNodeId == DM_YANG_TREE_NODE_IS_VERTEX) {
        CltCataLabelT *label = NULL;
        ret = GetVertexLabelFromMap(vertexLabelMap, buf, &label);
        if (ret != GMERR_OK) {
            return ret;
        }
        basicInfo->vertexLabel = label->vertexLabel;
        basicInfo->vertexDesc = label->vertexLabel->vertexDesc;
    }

    uint32_t childNum = 0;
    ret = FixBufGetUint32(buf, &childNum);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get childNum.");
        return ret;
    }

    basicInfo->childNum = childNum;
    basicInfo->uniqNodeId = uniqNodeId;
    basicInfo->parent = parent;

    return GMERR_OK;
}

/**
 * vertexLabelId|nodeId|vertexAddr|childNum|op|vertexSize|oldVertexBuf|vertexSize|newVertexBuf|oldPrevKeyLen|
 * oldPrevKeyBuf|newPrevKeyLen|newPrevKeyBuf|
 */
Status DecodeSingleYangTree(
    DmDecodeParamT *decodeParam, FixBufferT *buf, DmYangTreeT *parent, uint32_t childIdx, DmYangTreeT **tree)
{
    DB_POINTER3(decodeParam, buf, tree);

    DmYangTreeT **currTree = tree;
    bool hasBrother = false;
    DmYangTreeT *prevTree = NULL;
    do {
        DmBasicYangTreeInfoT basicInfo;
        Status ret = DecodeYangTreeBasicInfo(decodeParam->vertexLabelMap, buf, parent, &basicInfo);
        if (ret != GMERR_OK) {
            return ret;
        }

        ret = DecodeYangDiffTreeFromBuf(decodeParam, buf, &basicInfo, currTree);
        if (ret != GMERR_OK) {
            return ret;
        }
        // vertex 类型结果树 vertexParentTree 指向自身， node 类型结果树指向其上层距离最近的 vertex 类型结果树
        if (DmIsVertexTree(*currTree)) {
            (*currTree)->diffResultCtx->vertexParentTree = (*currTree);
            (*currTree)->diffResultCtx->schema = MEMBER_PTR(basicInfo.vertexLabel->metaVertexLabel, schema);
        } else {
            DB_POINTER(parent);
            (*currTree)->diffResultCtx->vertexParentTree = parent->diffResultCtx->vertexParentTree;
            (*currTree)->diffResultCtx->schema =
                DmGetSchemaByUniqueId(parent->diffResultCtx->schema, basicInfo.uniqNodeId);
        }

        ret = DecodeChildTree(decodeParam, buf, *currTree, &hasBrother);
        if (ret != GMERR_OK) {
            return ret;
        }
        (*currTree)->diffResultCtx->parentTree = parent;
        (*currTree)->childIdx = childIdx;
        if (prevTree != NULL) {
            prevTree->nextTree = *currTree;
            (*currTree)->prevTree = prevTree;
        }
        prevTree = *currTree;
        currTree = &((*currTree)->nextTree);
    } while (hasBrother);
    return GMERR_OK;
}

static Status DecodeYangTreeList(
    DmDecodeParamT *decodeParam, FixBufferT *buf, DmYangTreeT ***yangTreeList, uint32_t count)
{
    Status ret = GMERR_OK;
    uint32_t allocSize = (uint32_t)sizeof(DmYangTreeT *) * (count);
    // 内存申请来自传入的ctx，内存的释放在ctx销毁时.当前ctx来自于顶层 "CLTYANG" memCtx
    *yangTreeList = (DmYangTreeT **)DbDynMemCtxAlloc(decodeParam->memCtx, allocSize);
    if (*yangTreeList == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc yang tree.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(*yangTreeList, allocSize, 0, allocSize);
    for (uint32_t i = 0; i < count; i++) {
        if ((ret = DecodeSingleYangTree(decodeParam, buf, NULL, 0, &((*yangTreeList)[i]))) != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

/**
 * vertexLabelCount|vertexLabel|vertexLabel|......
 * isExplicit|treeCount|
 * vertexLabelId|uniqNodeId|vertexAddr|childNum|
 * opCode|vertexSize|oldVertex|vertexSize|newVertex|oldPrevBuffer|newPrevBuffer
 */
Status DecodeYangTrees(DmDecodeParamT *decodeParam, FixBufferT *buf, DmYangTreeT ***yangTreeList, uint32_t *count)
{
    DB_POINTER4(decodeParam, buf, yangTreeList, count);
    Status ret = GMERR_OK;
    if ((ret = DbOamapInit(decodeParam->vertexMap, decodeParam->vertexLabelMap->size, DbOamapUint64Compare,
             decodeParam->memCtx, true)) != GMERR_OK) {
        return ret;
    }

    uint32_t isExplicit = 0;
    if ((ret = FixBufGetUint32(buf, &isExplicit)) != GMERR_OK) {
        return ret;
    }
    decodeParam->isExplicit = (bool)isExplicit;

    uint32_t yangTreeLen = 0;
    if ((ret = FixBufGetUint32(buf, &yangTreeLen)) != GMERR_OK) {
        return ret;
    }

    uint32_t seekLength = FixBufGetSeekLength(buf);
    if (seekLength < yangTreeLen) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "Seek length of buffer when decode. SeekLength: %" PRIu32 ", YangTreeLength: %" PRIu32, seekLength,
            yangTreeLen);
        return GMERR_DATA_EXCEPTION;
    }

    // 获取yangtree个数
    if ((ret = FixBufGetUint32(buf, count)) != GMERR_OK) {
        return ret;
    }

    if (*count == 0) {
        return GMERR_OK;
    }

    // 是否是分批返回
    uint32_t treeEncodeFinished = 0;
    if (decodeParam->treeType == DM_YANG_DIFF_CLIENT_TREE) {
        if ((ret = FixBufGetUint32(buf, &treeEncodeFinished)) != GMERR_OK) {
            return ret;
        }
        decodeParam->isEnd = treeEncodeFinished == 0 ? false : true;
    }

    return DecodeYangTreeList(decodeParam, buf, yangTreeList, *count);
}

Status DeSerializeShmVertexLabels(GmcConnT *conn, FixBufferT *buf, DbMemCtxT *memCtx, DbOamapT *vertexLabelMap)
{
    DB_POINTER3(buf, memCtx, vertexLabelMap);
    uint32_t labelCount;
    Status ret = FixBufGetUint32(buf, &labelCount);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbOamapInit(vertexLabelMap, labelCount, DbOamapUint32Compare, memCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    for (uint32_t i = 0; i < labelCount; i++) {
        CltCataLabelT *cataLabel = NULL;
        ret = CltYangGetShmCataLabel(conn, buf, &cataLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DbOamapInsert(vertexLabelMap, cataLabel->vertexLabel->metaCommon.metaId,
            &(cataLabel->vertexLabel->metaCommon.metaId), cataLabel, NULL);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status CltDeSerializeDiffTrees(GmcConnT *conn, DmDecodeParamT *decodeParam, GmcFetchRetT *fetchRet,
    DmYangTreeT ***diffTreeList, FixBufferT *fixBuf)
{
    DB_POINTER4(decodeParam, fetchRet, diffTreeList, fixBuf);
    decodeParam->vertexLabelMap = &fetchRet->vertexLabelMap;
    decodeParam->vertexMap = &fetchRet->vertexMap;
    decodeParam->isEnd = true;
    Status ret = DeSerializeShmVertexLabels(conn, fixBuf, decodeParam->memCtx, decodeParam->vertexLabelMap);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "decode vertexLabels when diff deseri.");
        return ret;
    }

    ret = DecodeYangTrees(decodeParam, fixBuf, diffTreeList, &fetchRet->count);
    if (ret != GMERR_OK) {
        return ret;
    }
    fetchRet->isEnd = decodeParam->isEnd;
    fetchRet->isExplicit = decodeParam->isExplicit;
    return GMERR_OK;
}

uint32_t GetVertexLabelInfoSerializeSize(const DbOamapT *vertexLabelMap)
{
    uint32_t size = sizeof(uint32_t);
    DbOamapIteratorT iter = 0;
    void *key = NULL;
    CltCataLabelT *cltCataLabel = NULL;
    while (DbOamapFetch(vertexLabelMap, &iter, &key, (void **)&cltCataLabel) == GMERR_OK) {
        const char *metaName = MEMBER_PTR(cltCataLabel->vertexLabel, metaCommon.metaName);
        size += (sizeof(uint32_t) + strlen(metaName) + 1 + sizeof(uint32_t));
    }
    return size;
}

uint32_t GetDiffTreeSerializeSize(const FixBufferT *buf)
{
    return sizeof(uint32_t) + sizeof(uint32_t) + sizeof(uint32_t) + FixBufGetSeekLength(buf);
}

uint32_t GetDiffBufLen(const DbOamapT *vertexLabelMap, const FixBufferT *buf)
{
    uint32_t bufLen = sizeof(uint32_t);
    bufLen += GetVertexLabelInfoSerializeSize(vertexLabelMap);
    bufLen += GetDiffTreeSerializeSize(buf);
    return bufLen;
}

void SerializeDiffBufLen(uint32_t bufLen, uint8_t **diffBuf)
{
    *(uint32_t *)(*diffBuf) = bufLen;
    (*diffBuf) += sizeof(uint32_t);
}

Status SerializeVertexLabelInfo(const DbOamapT *vertexLabelMap, uint8_t **diffBuf)
{
    *(uint32_t *)(*diffBuf) = DbOamapUsedSize(vertexLabelMap);
    (*diffBuf) += sizeof(uint32_t);
    DbOamapIteratorT iter = 0;
    void *key = NULL;
    CltCataLabelT *cltCataLabel = NULL;
    while (DbOamapFetch(vertexLabelMap, &iter, &key, (void **)&cltCataLabel) == GMERR_OK) {
        const char *metaName = MEMBER_PTR(cltCataLabel->vertexLabel, metaCommon.metaName);
        uint32_t nameLen = strlen(metaName) + 1;
        *(uint32_t *)(*diffBuf) = nameLen;
        (*diffBuf) += sizeof(uint32_t);
        int err = strcpy_s((char *)*diffBuf, nameLen, metaName);
        if (err != 0) {
            DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Unable to copy label:%s.", metaName);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        (*diffBuf) += nameLen;
        *(uint32_t *)(*diffBuf) = cltCataLabel->metaCommon->metaId;
        (*diffBuf) += sizeof(uint32_t);
    }
    return GMERR_OK;
}

// isEplicit|treeCount|copyLen|buf
Status SerializeDiffTreeBuf(const FixBufferT *buf, bool isExplicit, uint32_t treeCount, uint8_t **diffBuf)
{
    *(uint32_t *)(*diffBuf) = (uint32_t)isExplicit;
    (*diffBuf) += sizeof(uint32_t);

    *(uint32_t *)(*diffBuf) = treeCount;
    (*diffBuf) += sizeof(uint32_t);

    uint32_t copyLen = FixBufGetSeekLength(buf);
    *(uint32_t *)(*diffBuf) = copyLen;
    (*diffBuf) += sizeof(uint32_t);
    int err = memcpy_s(*diffBuf, copyLen, FixBufGetBuf(buf) + FixBufGetSeekPos(buf), copyLen);
    if (err != 0) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED,
            "Unable to copy diff tree, len%" PRIu32 ", seekPos:%" PRIu32 ".", copyLen, FixBufGetSeekPos(buf));
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status SerializeDiffBuf(
    const DmDecodeParamT *decodeParam, const FixBufferT *buf, uint32_t treeCount, uint32_t bufLen, uint8_t *diffTreeBuf)
{
    uint8_t **diffBuf = &diffTreeBuf;
    SerializeDiffBufLen(bufLen, diffBuf);
    Status ret = SerializeVertexLabelInfo(decodeParam->vertexLabelMap, diffBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    return SerializeDiffTreeBuf(buf, decodeParam->isExplicit, treeCount, diffBuf);
}

Status DecodeDiffBuf(DmDecodeParamT *decodeParam, FixBufferT *buf, GmcFetchRetT *fetchRet)
{
    DB_POINTER3(decodeParam, buf, fetchRet);
    Status ret = GMERR_OK;

    uint32_t isExplicit = 0;
    if ((ret = FixBufGetUint32(buf, &isExplicit)) != GMERR_OK) {
        return ret;
    }
    decodeParam->isExplicit = (bool)isExplicit;
    fetchRet->isExplicit = decodeParam->isExplicit;

    uint32_t yangTreeLen = 0;
    if ((ret = FixBufGetUint32(buf, &yangTreeLen)) != GMERR_OK) {
        return ret;
    }
    uint32_t seekLen = FixBufGetSeekLength(buf);
    if (seekLen < yangTreeLen) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "Seek length of buffer when decode. seekLen: %" PRIu32 ", YangTreeLength: %" PRIu32, seekLen, yangTreeLen);
        return GMERR_DATA_EXCEPTION;
    }

    // 获取yangtree个数
    if ((ret = FixBufGetUint32(buf, &fetchRet->count)) != GMERR_OK) {
        return ret;
    }

    if (fetchRet->count == 0) {
        return GMERR_OK;
    }

    // 是否是分批返回
    uint32_t treeEncodeFinished = 0;
    if ((ret = FixBufGetUint32(buf, &treeEncodeFinished)) != GMERR_OK) {
        return ret;
    }
    fetchRet->isEnd = treeEncodeFinished == 0 ? false : true;

    uint32_t bufSize = GetDiffBufLen(decodeParam->vertexLabelMap, buf);
    // 内存申请来自传入的ctx，内存的释放在ctx销毁时.当前ctx来自于顶层 "CLTYANG" memCtx
    uint8_t *diffBuf = (uint8_t *)DbDynMemCtxAlloc(decodeParam->memCtx, bufSize);
    if (diffBuf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc diffbuf.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(diffBuf, bufSize, 0, bufSize);
    ret = SerializeDiffBuf(decodeParam, buf, fetchRet->count, bufSize, diffBuf);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(fetchRet->memCtx, diffBuf);
        return ret;
    }
    fetchRet->diffTreeBuf = diffBuf;
    return GMERR_OK;
}

Status CltDeSerializeDiffBuf(GmcConnT *conn, GmcFetchRetT *fetchRet, FixBufferT *fixBuf)
{
    DB_POINTER3(conn, fetchRet, fixBuf);
    Status ret = DeSerializeShmVertexLabels(conn, fixBuf, fetchRet->memCtx, &fetchRet->vertexLabelMap);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Deseri shm vertex label.");
        return ret;
    }
    DmDecodeParamT decodeParam = {0};
    decodeParam.vertexLabelMap = &fetchRet->vertexLabelMap;
    decodeParam.isEnd = true;
    decodeParam.filterState = DM_YANG_TREE_STATE_MATCH;
    decodeParam.memCtx = fetchRet->memCtx;

    return DecodeDiffBuf(&decodeParam, fixBuf, fetchRet);
}

Status ParseDiffBufLen(uint32_t bufLen, const uint8_t **buf)
{
    uint32_t realLen = *(const uint32_t *)(*buf);
    if (realLen > bufLen) {
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH,
            "the length of buffer is not enough when fetch result, curent:%" PRIu32 ", need:%" PRIu32, bufLen, realLen);
        return GMERR_DATATYPE_MISMATCH;
    }
    (*buf) += sizeof(uint32_t);
    return GMERR_OK;
}

typedef struct LabelIdPair {
    uint32_t srcLabelId;
    uint32_t dstLabelId;
} LabelIdPairT;

Status ParsePrefetchVertexLabelsRespond(GmcConnT *conn, DbOamapT *vertexLabelMap, LabelIdPairT *idPair)
{
    FixBufferT *res = &conn->recvPack;
    uint32_t labelsCount;
    Status ret = FixBufGetUint32(res, &labelsCount);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "parse response index");
        return ret;
    }
    CltCataLabelT *cataLabel = NULL;
    for (uint32_t i = 0; i < labelsCount; i++) {
#if defined(EXPERIMENTAL_NERGC)
        if (DbIsTcp()) {  // deserialize vertexLabel into dyn
            ret = CltRecvVertexLabel(res, &cataLabel);
            if (ret != GMERR_OK) {
                return ret;
            }
        } else {
            ret = RecvGetVertexLabel(res, &cataLabel);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
#else
        ret = RecvGetVertexLabel(res, &cataLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
#endif
        ret = CltCataSaveVertexLabel(&cataLabel, (uint16_t)conn->instanceId, 1, 0);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "save label to cache");
            return ret;
        }
        ret = DbOamapInsert(vertexLabelMap, idPair[i].srcLabelId, &(idPair[i].srcLabelId), cataLabel, NULL);
        if (ret != GMERR_OK) {
            return ret;
        }
        idPair[i].dstLabelId = cataLabel->vertexLabel->metaCommon.metaId;
    }
    return GMERR_OK;
}

void RelaseLabelNames(DbMemCtxT *memCtx, uint32_t labelCount, char **labelName)
{
    for (uint32_t i = 0; i < labelCount; i++) {
        if (labelName[i] != NULL) {
            DbDynMemCtxFree(memCtx, labelName[i]);
        }
    }
    DbDynMemCtxFree(memCtx, labelName);
}

Status FetchVertexLabels(
    GmcStmtT *stmt, uint32_t labelCount, const uint8_t **buf, LabelIdPairT *labelIdPairs, GmcFetchRetT *fetchRet)
{
    Status ret = DbOamapInit(&fetchRet->vertexLabelMap, labelCount, DbOamapUint32Compare, fetchRet->memCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    char **labelName = (char **)DbDynMemCtxAlloc(fetchRet->memCtx, sizeof(char *) * labelCount);
    if (labelName == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc labelName.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(labelName, sizeof(char *) * labelCount, 0, sizeof(char *) * labelCount);
    for (uint32_t i = 0; i < labelCount; i++) {
        uint32_t labelNameLen = *(const uint32_t *)(*buf);
        (*buf) += sizeof(uint32_t);

        labelName[i] = (char *)DbDynMemCtxAlloc(fetchRet->memCtx, labelNameLen);
        if (labelName[i] == NULL) {
            ret = GMERR_OUT_OF_MEMORY;
            goto Release;
        }
        errno_t err = strcpy_s(labelName[i], labelNameLen, (const char *)(*buf));
        if (err) {
            ret = GMERR_MEMORY_OPERATE_FAILED;
            goto Release;
        }
        (*buf) += labelNameLen;

        uint32_t srcLabelId = *(const uint32_t *)(*buf);
        labelIdPairs[i].srcLabelId = srcLabelId;
        (*buf) += sizeof(uint32_t);
    }
    PrefetchLabelsArgsT args = {.opCode = MSG_OP_RPC_GET_VERTEX_LABELS,
        .labelNames = (const char **)(uintptr_t)labelName,
        .length = labelCount};
    ret = CltRequestSyncWithCheck(stmt, PrefetchLabelsBuildReq, (const void *)&args, DummyParseRsp, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "inv labelType request");
        goto Release;
        ;
    }
    ret = ParsePrefetchVertexLabelsRespond(stmt->conn, &fetchRet->vertexLabelMap, labelIdPairs);
Release:
    RelaseLabelNames(fetchRet->memCtx, labelCount, labelName);
    return ret;
}

Status InsertLabelIdTransMap(LabelIdPairT *labelIdPairs, uint32_t labelCount, DbOamapT *labelIdTranslMap)
{
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < labelCount; i++) {
        ret = DbOamapInsert(labelIdTranslMap, labelIdPairs[i].srcLabelId, &labelIdPairs[i].srcLabelId,
            &labelIdPairs[i].dstLabelId, NULL);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status ParseVertexLabelInfo(GmcStmtT *stmt, const uint8_t **buf, DbOamapT *labelIdTranslMap, GmcFetchRetT *fetchRet)
{
    uint32_t labelCount = *(const uint32_t *)(*buf);
    (*buf) += sizeof(uint32_t);
    Status ret = DbOamapInit(labelIdTranslMap, labelCount, DbOamapUint32Compare, fetchRet->memCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 内存在GmcYangFreeFetchRet统一释放
    LabelIdPairT *labelIdPairs = (LabelIdPairT *)DbDynMemCtxAlloc(fetchRet->memCtx, sizeof(LabelIdPairT) * labelCount);
    if (labelIdPairs == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc id pairs.");
        return GMERR_OUT_OF_MEMORY;
    }
    ret = FetchVertexLabels(stmt, labelCount, buf, labelIdPairs, fetchRet);
    if (ret != GMERR_OK) {
        goto Release;
    }
    ret = InsertLabelIdTransMap(labelIdPairs, labelCount, labelIdTranslMap);
    if (ret == GMERR_OK) {
        return ret;
    }
Release:
    DbDynMemCtxFree(fetchRet->memCtx, labelIdPairs);
    return ret;
}

Status ParseDiffTree(GmcStmtT *stmt, DmDecodeParamT *decodeParam, const uint8_t **buf, GmcFetchRetT *fetchRet)
{
    fetchRet->isExplicit = (bool)*(const uint32_t *)(*buf);
    decodeParam->isExplicit = fetchRet->isExplicit;
    (*buf) += sizeof(uint32_t);

    fetchRet->count = *(const uint32_t *)(*buf);
    (*buf) += sizeof(uint32_t);
    if (fetchRet->count == 0) {
        return GMERR_OK;
    }
    Status ret = GMERR_OK;
    if ((ret = DbOamapInit(decodeParam->vertexMap, decodeParam->vertexLabelMap->size, DbOamapUint64Compare,
             decodeParam->memCtx, true)) != GMERR_OK) {
        return ret;
    }
    uint32_t allocSize = (uint32_t)sizeof(DmYangTreeT *) * (fetchRet->count);
    // 内存申请来自传入的ctx，内存的释放在ctx销毁时.当前ctx来自于顶层 "CLTYANG" memCtx
    DmYangTreeT **yangTreeList = (DmYangTreeT **)DbDynMemCtxAlloc(fetchRet->memCtx, allocSize);
    if (yangTreeList == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc yang tree.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(yangTreeList, allocSize, 0, allocSize);
    uint32_t fixBufLen = *(const uint32_t *)(*buf);
    (*buf) += sizeof(uint32_t);
    FixBufferT fixBuf;
    FixBufInit(&fixBuf, NULL, 0, 0, FIX_BUF_FLAG_LOB_BUFFER, fetchRet->memCtx);
    ret = FixBufPutData(&fixBuf, *buf, fixBufLen);
    if (ret != GMERR_OK) {
        goto Release;
    }
    for (uint32_t i = 0; i < fetchRet->count; i++) {
        if ((ret = DecodeSingleYangTree(decodeParam, &fixBuf, NULL, 0, &(yangTreeList[i]))) != GMERR_OK) {
            goto Release;
        }
    }
    const GmcYangTreeT **treeReply = NULL;
    ret = CltConvertYangTree(decodeParam, fetchRet, yangTreeList, decodeParam->treeType, &treeReply);
    if (ret != GMERR_OK) {
        goto Release;
    }
    fetchRet->treeReply = (const GmcYangTreeT **)treeReply;
    return GMERR_OK;
Release:
    DbDynMemCtxFree(fetchRet->memCtx, yangTreeList);
    return ret;
}

Status DiffBufCheckIsLegal(const uint8_t **diffBuf, uint32_t size, const uint8_t *diffTailBuf)
{
    if (*diffBuf + size > diffTailBuf) {
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

Status DiffBufCheckBufLenIsLegal(const uint8_t *diffTailBuf, const uint8_t **diffBuf)
{
    Status ret = DiffBufCheckIsLegal(diffBuf, sizeof(uint32_t), diffTailBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    (*diffBuf) += sizeof(uint32_t);
    return GMERR_OK;
}

Status DiffBufCheckVertexInfoBufIsLegal(const uint8_t *diffTailBuf, const uint8_t **diffBuf)
{
    Status ret = DiffBufCheckIsLegal(diffBuf, sizeof(uint32_t), diffTailBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t labelCount = *(const uint32_t *)(*diffBuf);
    (*diffBuf) += sizeof(uint32_t);
    for (uint32_t i = 0; i < labelCount; i++) {
        ret = DiffBufCheckIsLegal(diffBuf, sizeof(uint32_t), diffTailBuf);
        if (ret != GMERR_OK) {
            return ret;
        }
        uint32_t nameLen = *(const uint32_t *)(*diffBuf);
        (*diffBuf) += sizeof(uint32_t);
        ret = DiffBufCheckIsLegal(diffBuf, nameLen, diffTailBuf);
        if (ret != GMERR_OK) {
            return ret;
        }
        (*diffBuf) += nameLen;
        ret = DiffBufCheckIsLegal(diffBuf, sizeof(uint32_t), diffTailBuf);
        if (ret != GMERR_OK) {
            return ret;
        }
        (*diffBuf) += sizeof(uint32_t);
    }

    return GMERR_OK;
}

Status DiffBufCheckTreeBufIsLegal(const uint8_t *diffTailBuf, const uint8_t **diffBuf)
{
    Status ret = DiffBufCheckIsLegal(diffBuf, sizeof(uint32_t), diffTailBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    (*diffBuf) += sizeof(uint32_t);

    ret = DiffBufCheckIsLegal(diffBuf, sizeof(uint32_t), diffTailBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    (*diffBuf) += sizeof(uint32_t);

    ret = DiffBufCheckIsLegal(diffBuf, sizeof(uint32_t), diffTailBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t treeBufLen = *(const uint32_t *)(*diffBuf);
    (*diffBuf) += sizeof(uint32_t);

    ret = DiffBufCheckIsLegal(diffBuf, treeBufLen, diffTailBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    (*diffBuf) += treeBufLen;
    return GMERR_OK;
}

Status CltDiffBufCheckIsLegal(const uint8_t *diffBuf, uint32_t bufLen)
{
    const uint8_t *diffTailBuf = diffBuf + bufLen;
    const uint8_t *diffHeaderBuf = diffBuf;
    Status ret = DiffBufCheckBufLenIsLegal(diffTailBuf, &diffHeaderBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DiffBufCheckVertexInfoBufIsLegal(diffTailBuf, &diffHeaderBuf);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = DiffBufCheckTreeBufIsLegal(diffTailBuf, &diffHeaderBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    DB_ASSERT(diffHeaderBuf <= diffTailBuf);
    return GMERR_OK;
}

Status CltFetchRetFromDiffBuf(GmcStmtT *stmt, uint32_t bufLen, const uint8_t *diffBuf, GmcFetchRetT *fetchRet)
{
    const uint8_t *buf = diffBuf;
    Status ret = CltDiffBufCheckIsLegal(diffBuf, bufLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = ParseDiffBufLen(bufLen, &buf);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbOamapT labelIdTransMap;
    ret = ParseVertexLabelInfo(stmt, &buf, &labelIdTransMap, fetchRet);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmDecodeParamT decodeParam = {0};
    decodeParam.vertexLabelMap = &fetchRet->vertexLabelMap;
    decodeParam.vertexMap = &fetchRet->vertexMap;
    decodeParam.isEnd = true;
    decodeParam.filterState = DM_YANG_TREE_STATE_MATCH;
    decodeParam.memCtx = fetchRet->memCtx;
    decodeParam.treeType = fetchRet->treeType;
    return ParseDiffTree(stmt, &decodeParam, &buf, fetchRet);
}

static void CltInitLeafrefPathOptions(GmcGetLeafrefPathOptionsT *leafrefOption, GmcGetLeafrefPathModeE mode)
{
    DB_POINTER(leafrefOption);
    leafrefOption->mode = mode;
}

Status CltYangGetLeafrefPathOptionsCreate(GmcGetLeafrefPathModeE mode, GmcGetLeafrefPathOptionsT **leafrefOption)
{
    // 用户调用 GmcYangGetLeafrefPathOptionsDestroy 接口释放该内存
    if (mode >= GMC_LEAFREF_GET_PATH_BUTT) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "leafref path mode %" PRIu32 ".", (uint32_t)mode);
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    *leafrefOption = DbDynMemCtxAlloc(g_gmdbCltInstance.memCtx, sizeof(GmcConnOptionsT));
    if (*leafrefOption == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc leafref Opt.");
        return GMERR_OUT_OF_MEMORY;
    }
    CltInitLeafrefPathOptions(*leafrefOption, mode);
    return GMERR_OK;
}

void CltYangGetLeafrefPathOptionsDestroy(GmcGetLeafrefPathOptionsT *leafrefOption)
{
    DbDynMemCtxFree(g_gmdbCltInstance.memCtx, leafrefOption);
}

static void CltInitFetchDiffOption(GmcFetchDiffOptionT *option)
{
    option->mode = GMC_FETCH_DIFF_REPORT_ALL;
}

Status CltYangDiffFetchOptionCreate(GmcFetchDiffOptionT **option)
{
    // 用户调用 GmcYangDiffFetchOptionDestroy 接口释放该内存
    *option = DbDynMemCtxAlloc(g_gmdbCltInstance.memCtx, sizeof(GmcFetchDiffOptionT));
    if (*option == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc option.");
        return GMERR_OUT_OF_MEMORY;
    }
    CltInitFetchDiffOption(*option);
    return GMERR_OK;
}

void CltYangDiffFetchOptionDestroy(GmcFetchDiffOptionT *option)
{
    DbDynMemCtxFree(g_gmdbCltInstance.memCtx, option);
}

Status CltFetchDiffTreeCheck(const GmcFetchRetT *fetchRet, const GmcFetchDiffOptionT *opt)
{
    // 检查 opt 设置时的合法性
    if (opt != NULL && opt->mode > GMC_FETCH_DIFF_EXPLICIT) {
        DB_LOG_AND_SET_LASERR(
            GMERR_INVALID_PARAMETER_VALUE, "fetch diff option mode:%" PRIu32 ".", (uint32_t)opt->mode);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    // 检查多批次时的设置类型应相同
    if (fetchRet == NULL) {
        return GMERR_OK;
    }
    bool isExplicit = opt != NULL && opt->mode == GMC_FETCH_DIFF_EXPLICIT;
    if (isExplicit != fetchRet->isExplicit) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE,
            "Different diff fetch modes, pre: %" PRIu32 ", now: %" PRIu32 ".", (uint32_t)fetchRet->isExplicit,
            (uint32_t)isExplicit);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

Status CltYangCreateSubtreeFilterOption(GmcSubtreeOptionT **option)
{
    // 用户调用 GmcYangDiffFetchOptionDestroy 接口释放该内存
    *option = DbDynMemCtxAlloc(g_gmdbCltInstance.memCtx, sizeof(GmcSubtreeOptionT));
    if (*option == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc option.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(*option, sizeof(GmcSubtreeOptionT), 0, sizeof(GmcSubtreeOptionT));
    return GMERR_OK;
}

void CltYangDestroySubtreeFilterOption(GmcSubtreeOptionT *option)
{
    DbDynMemCtxFree(g_gmdbCltInstance.memCtx, option);
}

void CltGetCommonInfoFromVertexLabel(GmcStmtT *stmt, DmVertexLabelT *vertexLabel, VertexLabelCommonInfoT **commonInfo)
{
#if defined(EXPERIMENTAL_NERGC)
    if (DbIsTcp()) {
        *commonInfo = vertexLabel->commonInfo;
    } else {
        *commonInfo = (VertexLabelCommonInfoT *)DbShmPtrToAddr(vertexLabel->commonInfoShmPtr);
    }
#else
    *commonInfo = (VertexLabelCommonInfoT *)DbShmPtrToAddr(vertexLabel->commonInfoShmPtr);
#endif
}

void CltGetRelatedEdgeLabelsFromVertexLabel(
    GmcStmtT *stmt, VertexLabelCommonInfoT *commonInfo, DmEdgeLabelInfoT **relatedEdgeLabels)
{
#if defined(EXPERIMENTAL_NERGC)
    if (DbIsTcp()) {
        *relatedEdgeLabels = commonInfo->relatedEdgeLabels;
    } else {
        *relatedEdgeLabels = (DmEdgeLabelInfoT *)DbShmPtrToAddr(commonInfo->relatedEdgeLabelsShm);
    }
#else
    *relatedEdgeLabels = (DmEdgeLabelInfoT *)DbShmPtrToAddr(commonInfo->relatedEdgeLabelsShm);
#endif
}
