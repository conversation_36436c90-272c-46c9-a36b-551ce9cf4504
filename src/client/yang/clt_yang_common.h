/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: clt_yang_difftree.h
 * Description: header file for GMDB client yang diff
 * Create: 2022-7-27
 */

#ifndef CLT_YANG_COMMON_H
#define CLT_YANG_COMMON_H

#include "clt_types.h"
#include "gmc_yang_types.h"
#include "clt_meta_cache.h"
#include "dm_yang_common.h"
#include "dm_yang_subtree.h"
#include "db_msg_buffer.h"
#include "db_hashmap.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct CltLabel {
    DmTreeDescT *desc;
    DmVertexT *vertex;
    CltCataLabelT *cataLabel;
} CltLabelT;

struct GmcFetchRetT {
    DbMemCtxT *memCtx;  // 管理本次获取结果的内存申请释放，CLTYANG，在AllocFetchRet中创建，其父为conn->memCtx
    CltCursorT *cursor;  // 用于分批查询，当isEnd为false时记录查询结果的位置，以便下一次查询从该位置开始
    union {
        const char **jsonReply;          // subtree查询的返回结果 json 数据。
        const GmcYangTreeT **treeReply;  // subtree、Diff查询的返回结果 API 数据。
        const uint8_t *diffTreeBuf;      // Diff查询的返回结果 序列化数据。
    };
    uint32_t count;  // 返回结果树的个数，subtree查询结果当前只支持1颗结果树，后续支持多棵结果树，diff可能有多颗树
    bool isEnd;      // 指定查询结果是否完整，若isEnd为true则查询结果完整
    bool isJson;     // 返回结果时json串还是结构化数据
    bool isFetchBuf;
    bool isExplicit;  // diff 过程中是否为explicit模式
    DbOamapT vertexLabelMap;
    DbOamapT vertexMap;
    DmYangTreeTypeE treeType;
};

struct GmcGetLeafrefPathOptionsT {
    GmcGetLeafrefPathModeE mode;
};

struct GmcSubtreeOptionT {
    GmcSubtreeFilterT *subtreeFilter;
    const char *defaultPrefix;  // GMC_FETCH_JSON_RFC7951或GMC_FETCH_FULL_JSON_RFC7951查询模式下默认值的前缀
};

struct GmcYangTreeT {
    // 根据treeType选择对应的树模型：subtree树使用DmSubtreeT(subtree)，diff树使用DmYangTreeT(tree)
    union {
        DmSubtreeT *subtree;
        DmYangTreeT *tree;
    };
    DbMemCtxT *memCtx;
    DbOamapT *vertexMap;
    DmYangTreeTypeE treeType;
    uint32_t defaultMode;  // 当前tree使用的默认值过滤模式
    uint32_t filterMode;   // 当前tree通过什么过滤模式过滤出来的
    uint32_t jsonFlag;     // json做为返回值时的格式
    DmYangVertexCacheListT *cacheList;
    bool isExplicit;  // diff 过程中是否为explicit模式
};

typedef struct GmcYangPrevNodeKeyValue {
    GmcYangNodeValueT value;
    uint8_t *buf; /**< 用于指向node value实际属性值的内存管理， value->value指向该内存 */
} GmcYangPrevNodeKeyValueT;

typedef union GmcYangDataInfo {
    struct {
        GmcYangNodeValueT *value;
        uint8_t *buf; /**< 用于指向node value实际属性值的内存管理， value->value指向该内存 */
    };
    struct {
        union {
            DmVertexT *vertex;
            DmNodeT *node;
        };
        GmcYangPrevNodeKeyValueT *prePosKey; /**< 当diff类型为GMC_DIFF_CONTAINER/GMC_DIFF_LIST时指向对应vertex的前驱信息
                                              */
    };
} GmcYangDataInfoT;

struct GmcYangNodeT {
    // Yang Node 的元数据
    const GmcYangTreeT *root;  // node所属Yang tree 根节点句柄
    // Yang Node 的内容数据
    DmYangTreeIterT node;     // 当前node的信息
    char *name;               /**< 如果type为GMC_YANG_FIELD这里为属性名称，其他情况则为label名称 */
    GmcYangNodeTypeE type;    /**< yangtree信息类型是属性变更还是节点位置变更 */
    GmcDiffOpTypeE opType;    /**< diff变更类型只有update，remove，create三种状态 */
    GmcYangDataInfoT oldData; /**< oldData指向变更前数据, subtree场景只有oldData*/
    GmcYangDataInfoT newData; /**< newData指向变更后数据 */
};

typedef struct GmcFetchDiffOptionsT {
    GmcFetchDiffModeE mode; /**< fetch diff 模式 */
} GmcFetchDiffOptionT;

Status CltPutSubtree2Buf(DmSubtreeT *tree, FixBufferT *buf);
Status CltDeSerializeDiffBuf(GmcConnT *conn, GmcFetchRetT *fetchRet, FixBufferT *fixBuf);
Status CltDeSerializeDiffTrees(GmcConnT *conn, DmDecodeParamT *decodeParam, GmcFetchRetT *fetchRet,
    DmYangTreeT ***diffTreeList, FixBufferT *fixBuf);
Status CltParseFetchRet(GmcConnT *conn, FixBufferT *buf, GmcFetchRetT *fetchRet);
Status CltConvertYangTree(DmDecodeParamT *decodeParam, GmcFetchRetT *fetchRet, DmYangTreeT **treeList,
    DmYangTreeTypeE treeType, const GmcYangTreeT ***yangTrees);
Status CltDeSerializeSubtrees(
    DbMemCtxT *memCtx, FixBufferT *buffer, GmcSubtreeWithDefaultModeE defaultMode, GmcYangTreeT **replyTree);
void CltFreeYangTree(GmcBatchT *batch);
Status CltYangNodeGetKeyPropValue(const GmcYangNodeT *currNode, uint32_t indexPropId, GmcYangNodeValueT **value);
Status CltYangPrevNodeKeyPropValue(
    const GmcYangNodeT *currNode, bool isNewData, uint32_t indexPropId, GmcYangNodeValueT **value);
Status CltYangGetNextNode(const GmcYangNodeT *parentNode, const GmcYangNodeT *prevNode, GmcYangNodeT **currNode);
Status CltYangGetRootNode(const GmcYangTreeT *yangTree, GmcYangNodeT **rootNode);
GmcDiffOpTypeE CltYangNodeGetOpType(const GmcYangNodeT *node);
GmcYangNodeValueT *CltYangNodeGetPropValue(const GmcYangNodeT *node, bool isNew);
bool CltYangIsExistTreePrevNode(const GmcYangNodeT *node, bool isNew);
Status CltPartialNodeType(const GmcYangNodeT *yangNode, bool *isNode);
void CltSetYangNodeTypeByYangInfo(const DmYangInfoDescT *yangInfoDesc, GmcYangNodeT *curr);
Status CltFetchRetFromDiffBuf(GmcStmtT *stmt, uint32_t bufLen, const uint8_t *diffBuf, GmcFetchRetT *fetchRet);
Status CltYangGetLeafrefPathOptionsCreate(GmcGetLeafrefPathModeE mode, GmcGetLeafrefPathOptionsT **leafrefOption);
void CltYangGetLeafrefPathOptionsDestroy(GmcGetLeafrefPathOptionsT *leafrefOption);
Status CltYangDiffFetchOptionCreate(GmcFetchDiffOptionT **option);
void CltYangDiffFetchOptionDestroy(GmcFetchDiffOptionT *option);
Status CltFetchDiffTreeCheck(const GmcFetchRetT *fetchRet, const GmcFetchDiffOptionT *opt);
Status CltYangGetDmValueWithCopy(const DmValueT *dmValue, void *value, uint32_t capacity, uint32_t *type, bool *null);
Status CltYangCreateSubtreeFilterOption(GmcSubtreeOptionT **option);
void CltYangDestroySubtreeFilterOption(GmcSubtreeOptionT *option);
void CltGetRelatedEdgeLabelsFromVertexLabel(
    GmcStmtT *stmt, VertexLabelCommonInfoT *commonInfo, DmEdgeLabelInfoT **relatedEdgeLabels);
void CltGetCommonInfoFromVertexLabel(GmcStmtT *stmt, DmVertexLabelT *vertexLabel, VertexLabelCommonInfoT **commonInfo);
#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* CLT_YANG_COMMON_H */
