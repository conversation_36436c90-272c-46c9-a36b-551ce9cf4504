/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: clt subtre tree scan node
 * Author: wuyongzheng
 * Create: 2023-02-08
 */
#include "clt_yang_common.h"
#include "clt_utils.h"
#include "gmc_yang_types.h"
#include "dm_data_pattern.h"
#include "dm_yang_interface.h"
#include "dm_yang_subtree.h"

// 封装节点级info信息
static Status GetSubtreeNodeInfo(GmcYangNodeT *curr)
{
    // 设置yangNode propertyId设置为无效,超出范围
    DmSubtreeT *currTree = curr->node.subtree;
    if (currTree == NULL) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Current yang node.");
        return GMERR_DATA_EXCEPTION;
    }
    curr->name = DmGetSubtreeName(currTree);
    bool isVertex = DmIsVertexSubtree(currTree);
    if (isVertex) {
        CltSetYangNodeTypeByYangInfo(currTree->vertex->vertexDesc->yangInfoDesc, curr);
    } else {
        CltSetYangNodeTypeByYangInfo(currTree->node->nodeDesc->yangInfoDesc, curr);
    }
    GmcYangDataInfoT *oldData = &curr->oldData;
    // subtree场景下只有oldData有数据
    if (isVertex) {
        oldData->vertex = currTree->vertex;
    } else {
        oldData->node = currTree->node;
    }
    return GMERR_OK;
}

static Status AllocAndInitDataValue(DbMemCtxT *memCtx, uint32_t propSize, GmcYangDataInfoT *dataInfo)
{
    // 释放原则：通过释放memctx达到其变量释放目的
    // memctx: yangTreeMemCtxT, 释放该ctx需要调用GmcYangFreeTree，调用时机：用户主动调用
    dataInfo->value = (GmcYangNodeValueT *)DbDynMemCtxAlloc(memCtx, sizeof(GmcYangNodeValueT));
    if (dataInfo->value == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Allocate value.");
        return GMERR_OUT_OF_MEMORY;
    }
    dataInfo->value->size = propSize;
    dataInfo->value->isDefault = false;
    dataInfo->value->type = GMC_DATATYPE_NULL;
    // 释放原则：通过释放memctx达到其变量释放目的
    // memctx: yangTreeMemCtxT, 释放该ctx需要调用GmcYangFreeTree，调用时机：用户主动调用
    dataInfo->buf = DbDynMemCtxAlloc(memCtx, propSize);
    if (dataInfo->buf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Allocate buf.");
        return GMERR_OUT_OF_MEMORY;
    }
    dataInfo->value->value = dataInfo->buf;
    return GMERR_OK;
}

static Status InitSubtreeVertexFieldValue(DbMemCtxT *memCtx, DmVertexT *oldVertex, uint32_t propId, GmcYangNodeT *info)
{
    Status ret = DmVertexGetPropNameById(oldVertex, propId, &info->name);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t propSize;
    ret = DmVertexGetRealPropeSizeById(oldVertex, propId, &propSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    return AllocAndInitDataValue(memCtx, propSize, &info->oldData);
}

static Status InitSubtreeNodeFieldValue(DbMemCtxT *memCtx, DmNodeT *oldNode, uint32_t propId, GmcYangNodeT *info)
{
    Status ret = DmNodeGetPropNameById(oldNode, propId, &info->name);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t propSize;
    ret = DmNodeGetRealPropeSizeById(oldNode, propId, &propSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    return AllocAndInitDataValue(memCtx, propSize, &info->oldData);
}

static Status InitSubtreeFieldValue(const GmcYangNodeT *parent, uint32_t propId, GmcYangNodeT *info)
{
    DB_POINTER2(parent, info);
    const GmcYangTreeT *root = parent->root;
    DbMemCtxT *memCtx = root->memCtx;
    DmSubtreeT *parentTree = parent->node.subtree;
    if (parentTree == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Parent subtree is unexpected.");
        return GMERR_DATA_EXCEPTION;
    }
    if (DmIsVertexSubtree(parentTree)) {
        return InitSubtreeVertexFieldValue(memCtx, parentTree->vertex, propId, info);
    }
    return InitSubtreeNodeFieldValue(memCtx, parentTree->node, propId, info);
}

static Status InitSubtreeNodeProperty(const GmcYangNodeT *parent, uint32_t propId, GmcYangDataInfoT *data)
{
    DmSubtreeT *tree = parent->node.subtree;
    DmValueT dmValue = {0};
    Status ret;
    if (DmIsVertexSubtree(tree)) {
        ret = DmVertexGetPropeByIdNoCopy(tree->vertex, propId, &dmValue);
    } else {
        ret = DmNodeGetPropeByIdNoCopy(tree->node, propId, &dmValue);
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    bool isNull = false;
    ret = CltYangGetDmValueWithCopy(&dmValue, data->buf, data->value->size, (uint32_t *)&data->value->type, &isNull);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Copy DM value when init subtree.");
        return ret;
    }
    return GMERR_OK;
}

// 封装字段级info信息
Status GetSubtreeFieldInfo(const GmcYangNodeT *parent, GmcYangNodeT *curr, bool isDefault)
{
    if (parent == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Parent yang node is unexpected.");
        return GMERR_DATA_EXCEPTION;
    }
    GmcYangDataInfoT *oldData = &curr->oldData;
    Status ret = InitSubtreeFieldValue(parent, curr->node.propId, curr);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = InitSubtreeNodeProperty(parent, curr->node.propId, oldData);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (parent->root->defaultMode == GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED) {
        oldData->value->isDefault = isDefault;
    } else {
        oldData->value->isDefault = false;
    }
    curr->type = GMC_YANG_FIELD;
    curr->oldData.value->name = curr->name;
    return GMERR_OK;
}

static Status CltGetNextNodeInfo(const GmcYangNodeT *parentNode, DmYangCursorT *cursor, GmcYangNodeT **currNode)
{
    const GmcYangTreeT *root = parentNode->root;
    // 需要申请GmcYangNode, 并把内部数据转化到currNode中
    size_t size = sizeof(GmcYangNodeT);
    // 释放原则：通过释放memctx达到其变量释放目的
    // memctx: yangTreeMemCtxT, 释放该ctx需要调用GmcYangFreeTree，调用时机：用户主动调用
    GmcYangNodeT *curr = DbDynMemCtxAlloc(root->memCtx, size);
    if (curr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc node info buf.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(curr, size, 0, size);
    curr->root = root;
    curr->node.subtree = cursor->curr->subtree;
    curr->node.propId = cursor->curr->propId;
    curr->node.childIdx = cursor->curr->childIdx;
    curr->node.schema = DmTreeDescGetSchema(parentNode->node.subtree->desc);
    // 根据找到的是节点还是字段，填充info
    Status ret;
    if (cursor->currType == YANG_NEXT_IS_FIELD) {
        ret = GetSubtreeFieldInfo(parentNode, curr, cursor->curr->isDefault);
    } else {
        ret = GetSubtreeNodeInfo(curr);
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Get node info worthless.");
        DbDynMemCtxFree(root->memCtx, curr);
        return ret;
    }
    *currNode = curr;
    return GMERR_OK;
}

Status CltSubtreeGetNextNode(const GmcYangNodeT *parentNode, const GmcYangNodeT *prevNode, GmcYangNodeT **currNode)
{
    DB_POINTER2(parentNode, currNode);
    DmYangTreeIterT curr = {0};
    const GmcYangTreeT *root = parentNode->root;
    DmYangCursorT cursor = {0};
    cursor.parent = &parentNode->node;
    cursor.prev = prevNode == NULL ? NULL : &prevNode->node;
    cursor.curr = &curr;
    cursor.currType = YANG_NEXT_IS_NULL;
    cursor.treeType = root->treeType;
    cursor.defaultMode = root->defaultMode;
    Status ret = DmSubtreeGetNextNodeInfo(&cursor);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 代表已经是最后的信息节点了，返回空，用户结束循环调用
    if (cursor.currType == YANG_NEXT_IS_NULL) {
        *currNode = NULL;
        return GMERR_OK;
    }
    return CltGetNextNodeInfo(parentNode, &cursor, currNode);
}

Status CltSubtreeGetRootNode(const GmcYangTreeT *yangTree, GmcYangNodeT **rootNode)
{
    DmSubtreeT *subtree = yangTree->subtree;
    if (subtree == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Subtree is NULL.");
        return GMERR_DATA_EXCEPTION;
    }
    Status ret = GMERR_OK;
    if (subtree->vertexSeriBuf != NULL) {
        ret = DmDeSerialize2ExistsVertex(
            subtree->vertexSeriBuf, subtree->vertexSeriBufLen, subtree->vertex, DmGetCheckMode());
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    size_t size = sizeof(GmcYangNodeT);
    // 释放原则：通过释放memctx达到其变量释放目的
    // memctx: yangTreeMemCtxT, 释放该ctx需要调用GmcYangFreeTree，调用时机：用户主动调用
    GmcYangNodeT *root = (GmcYangNodeT *)DbDynMemCtxAlloc(yangTree->memCtx, size);
    if (root == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc root node info.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(root, size, 0, size);
    root->root = yangTree;
    root->name = DmGetSubtreeName(subtree);
    root->node.subtree = subtree;
    root->node.propId = 0;
    root->node.childIdx = DB_MAX_UINT32;

    // 获取根节点的yangTree节点信息
    ret = GetSubtreeNodeInfo(root);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get node info worthless.");
        DbDynMemCtxFree(yangTree->memCtx, root);
        return ret;
    }
    *rootNode = root;
    return GMERR_OK;
}
