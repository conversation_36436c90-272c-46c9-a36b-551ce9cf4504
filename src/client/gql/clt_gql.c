
/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: client for complex path subscription
 * Author: gongsai
 * Create: 2023-07-29
 */

#include "clt_gql.h"
#include "clt_msg.h"
#include "clt_resource.h"
#include "db_rpc_business_msg.h"

Status CltFillGqlMsg(FixBufferT *req, const void *input)
{
    const GqlMsgT *gqlMsg = (const GqlMsgT *)input;
    MsgHeaderT *msg = RpcPeekMsgHeader(req);
    OpHeaderT *op = ProtocolPeekFirstOpHeader(req);
    msg->serviceId = DRT_SERVICE_STMT;
    msg->modelType = MODEL_GQL;
    op->opCode = (uint32_t)gqlMsg->opCode;
    if (gqlMsg->gql == NULL) {
        // 不携带语句
        return GMERR_OK;
    }
    return FixBufPutString(req, gqlMsg->gql);
}

Status CltParseGqlSubRsp(FixBufferT *rsp, void *out)
{
    TextT tmp = {0};
    Status ret = SecureFixBufGetText(rsp, &tmp);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to get sub response.");
        return ret;
    }
    *(TextT *)out = tmp;
    return GMERR_OK;
}

Status CltSubCreateForComplexPath(GmcStmtT *stmt, const CreateSubParams *params, CltSubscriptionT **subs)
{
    GmcConnT *conn = stmt->conn;
    size_t size = strlen(params->subName) + 1;  // subscription name size
    DbOamapT *cata = &g_gmdbCltInstance.subscriptionMap;
    // 此内存在用户调用GmcUnSubscribe函数时，删除订阅关系进行释放，释放函数CltSubDestroy
    CltSubscriptionT *sub = DbDynMemCtxAlloc(cata->memCtx, sizeof(CltSubscriptionT) + size);
    if (sub == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    sub->subMemCtx = cata->memCtx;
    sub->state = 0;
    sub->userCb = params->userCb;
    sub->userData = params->userData;
    sub->cltSubsType = params->subsType;
    sub->versionMap = NULL;
    sub->fullSync = params->fullSync;
    sub->type = CLT_STMT_TYPE_SUB_COMPLEX_PATH;
    sub->subUserCbTimeDetails.subUserCbExecTotalTime = 0;
    sub->subUserCbTimeDetails.subUserCbExecCnt = 0;
    sub->stMgSubsEventBitMap = 0;
    sub->inInitLoadRound = false;
    sub->constraint = (DmSubsConstraintT){0};
    errno_t err = strcpy_s(sub->name, size, params->subName);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DbDynMemCtxFree(cata->memCtx, sub);  // 失败场景下内存指针没有传递到外面，并且会返回错误码，不用置空
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "Unable to copy sub name string, return value is %d", err);
        return GMERR_FIELD_OVERFLOW;
    }
    CltSetCataKey(&sub->cataKey, (uint16_t)conn->instanceId, (uint16_t)conn->dbId, conn->namespaceId, sub->name);

    (*subs) = sub;
    return GMERR_OK;
}

Status CltCreateAndSaveComplexPathSub(GmcStmtT *stmt, const char *input, GmcSubCallbackT userCb, void *userData)
{
    // 1.向服务端发送创建订阅请求
    GqlMsgT gqlMsg = (GqlMsgT){.gql = input, .opCode = MSG_OP_RPC_GQL_SUBSCRIBE};
    TextT subName = (TextT){0};
    Status ret = CltRequestSync(stmt->conn, CltFillGqlMsg, (const void *)&gqlMsg, CltParseGqlSubRsp, (void *)&subName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to send subscribe path message.");
        return ret;
    }
    if (subName.len == 0 || subName.str == NULL) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "returned subName is NULL.");
        return GMERR_DATA_EXCEPTION;
    }
    // 2.解析获取服务端回复的订阅名,开始创建和保存客户端订阅的映射关系
    uint32_t size = (uint32_t)sizeof(CreateSubParams);
    CreateSubParams params;
    errno_t err = memset_s(&params, size, '\0', size);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "Unable to init CreateSubParams. the sub is %s.", subName.str);
        return GMERR_INTERNAL_ERROR;
    }
    params.subName = subName.str;
    params.userCb = userCb;
    params.userData = userData;
    params.fullSync = false;
    params.labelName = NULL;
    params.subsType = MESSAGE_QUEUE;

    // 创建客户端订阅的映射关系
    CltSubscriptionT *sub = NULL;
    ret = CltSubCreateForComplexPath(stmt, &params, &sub);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to create path subscription. the sub is %s.", subName.str);
        return ret;
    }

    // 保存客户端订阅的映射关系
    ret = CltCataSaveSub(sub);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to save path subscription. the sub is %s.", subName.str);
        return ret;
    }

    return GMERR_OK;
}

Status SubPushPathFetch(GmcStmtT *stmt, bool *eof)
{
    SubPushSingleLabel *sub = CltGetOperationContext(stmt);
    FixBufferT *buffer = sub->msgData;
    stmt->receivedPathData = (uint8_t *)FixBufGetBuf(buffer) + FixBufGetSeekPos(buffer);
    uint32_t pubCnt = 0;
    Status ret = FixBufGetUint32(buffer, &pubCnt);
    if (ret != GMERR_OK) {
        return ret;
    }
    *eof = true;

    return GMERR_OK;
}
