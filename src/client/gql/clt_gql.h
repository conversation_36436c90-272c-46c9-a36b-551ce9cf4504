/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: client for complex path subscription
 * Author: gongsai
 * Create: 2023-07-29
 */

#ifndef CLT_GQL_H
#define CLT_GQL_H

#include "clt_meta_cache.h"
#include "db_hashmap.h"
#include "db_rpc_msg_op.h"
#include "dm_data_prop.h"
#include "dm_data_kv.h"
#include "dm_meta_subscription.h"
#include "clt_catalog_sub.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct GqlMsg {
    const char *gql;
    MsgOpcodeRpcE opCode;
} GqlMsgT;

Status CltFillGqlMsg(FixBufferT *req, const void *input);
Status CltParseGqlSubRsp(FixBufferT *rsp, void *out);
Status CltSubCreateForComplexPath(GmcStmtT *stmt, const CreateSubParams *params, CltSubscriptionT **subs);
Status CltCreateAndSaveComplexPathSub(GmcStmtT *stmt, const char *input, GmcSubCallbackT userCb, void *userData);
Status SubPushPathFetch(GmcStmtT *stmt, bool *eof);

#ifdef __cplusplus
}
#endif

#endif /* CLT_GQL_H */
