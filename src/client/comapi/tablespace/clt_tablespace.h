/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: internal interface of gmdb tablespace
 * Description:
 * Author:
 * Create: 2023-06-06
 */

#ifndef CLT_TABLESPACE_H
#define CLT_TABLESPACE_H

#include "clt_check.h"
#include "clt_msg.h"

#ifdef __cplusplus
extern "C" {
#endif

Status GmcCreateTablespaceCheck(GmcStmtT *stmt, GmcTspCfgT *tspCfg, GmcConnTypeE type);
Status CreateTablespaceBuildRequest(FixBufferT *req, const void *in);
Status CreateRsmTablespaceBuildRequest(FixBufferT *req, const void *in);
Status DropTablespaceBuildRequest(FixBufferT *req, const void *in);

#ifdef __cplusplus
}
#endif

#endif /* CLT_TABLESPACE_H */
