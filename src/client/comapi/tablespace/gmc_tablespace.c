/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: gmc_tablespace.c
 * Description: realization of interface of gmdb tablespace
 * Create: 2022-09-01
 */

#include "gmc_tablespace.h"
#include "clt_tablespace.h"

Status GmcCreateTablespace(GmcStmtT *stmt, GmcTspCfgT *tspCfg)
{
    Status ret = GmcCreateTablespaceCheck(stmt, tspCfg, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltRequestSync(stmt->conn, CreateTablespaceBuildRequest, tspCfg, DummyParseRsp, NULL);
}

Status GmcCreateRsmTablespace(GmcStmtT *stmt, GmcTspCfgT *tspCfg)
{
    Status ret = GmcCreateTablespaceCheck(stmt, tspCfg, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltRequestSync(stmt->conn, CreateRsmTablespaceBuildRequest, tspCfg, DummyParseRsp, NULL);
}

Status GmcDropTablespace(GmcStmtT *stmt, const char *tablespaceName)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(tablespaceName, MAX_TABLE_SPACE_LENGTH, "tablespaceName");
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltRequestSync(stmt->conn, DropTablespaceBuildRequest, tablespaceName, DummyParseRsp, NULL);
}

Status GmcCreateTablespaceAsync(GmcStmtT *stmt, GmcTspCfgT *tspCfg, GmcTablespaceDoneT userCb, void *userData)
{
    Status ret = GmcCreateTablespaceCheck(stmt, tspCfg, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    AsyncMsgContextT ctx = MakeAsyncMsgContext(DummyParseRsp, CommonDDLCallback, userCb, userData, stmt->notDistribute);
    return CltRequestAsync(stmt->conn, CreateTablespaceBuildRequest, tspCfg, &ctx);
}

Status GmcDropTablespaceAsync(GmcStmtT *stmt, const char *tablespaceName, GmcTablespaceDoneT userCb, void *userData)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(tablespaceName, MAX_TABLE_SPACE_LENGTH, "tablespaceName");
    if (ret != GMERR_OK) {
        return ret;
    }
    AsyncMsgContextT ctx = MakeAsyncMsgContext(DummyParseRsp, CommonDDLCallback, userCb, userData, stmt->notDistribute);
    return CltRequestAsync(stmt->conn, DropTablespaceBuildRequest, tablespaceName, &ctx);
}
