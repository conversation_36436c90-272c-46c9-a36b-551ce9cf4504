/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: clt_tablespace.c
 * Description: internal realization of interface of gmdb tablespace
 * Author:
 * Create: 2023-06-06
 */

#include "clt_tablespace.h"

Status GmcCreateTablespaceCheck(GmcStmtT *stmt, GmcTspCfgT *tspCfg, GmcConnTypeE type)
{
    if (tspCfg == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Param tspCfg.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    Status ret = CltStmtBasicCheck(stmt, type);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(tspCfg->tablespaceName, MAX_TABLE_SPACE_LENGTH, "tablespaceName");
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status CreateTablespaceBuildReq(FixBufferT *req, const void *in, bool isUseRsm)
{
    FillSimpleStmtMsgHeader(req, MSG_OP_RPC_CREATE_TABLESPACE);
    const GmcTspCfgT *config = in;
    Status ret = HelpFillString(req, true, config->tablespaceName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SecureFixBufPutUint16(req, config->initSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SecureFixBufPutUint16(req, config->stepSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SecureFixBufPutUint16(req, config->maxSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint16_t useRsmFlag = isUseRsm ? 1 : 0;
    return SecureFixBufPutUint16(req, useRsmFlag);
}

Status CreateTablespaceBuildRequest(FixBufferT *req, const void *in)
{
    return CreateTablespaceBuildReq(req, in, false);
}

Status CreateRsmTablespaceBuildRequest(FixBufferT *req, const void *in)
{
    return CreateTablespaceBuildReq(req, in, true);
}

Status DropTablespaceBuildRequest(FixBufferT *req, const void *in)
{
    FillSimpleStmtMsgHeader(req, MSG_OP_RPC_DROP_TABLESPACE);
    const char *tablespaceName = in;
    return HelpFillString(req, true, tablespaceName);
}
