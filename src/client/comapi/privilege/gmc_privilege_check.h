/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_privilege_check.h
 * Description: header file for GMDB client gmc priv check
 * Create: 2021-9-02
 */

#ifndef GMC_PRIVILEGE_CHECK_H
#define GMC_PRIVILEGE_CHECK_H

#include "gmc_types.h"
#include "gmc_internal_types.h"
#include "adpt_define.h"
#include "adpt_types.h"

#ifdef __cplusplus
extern "C" {
#endif

Status GmcObjectPrivsCheck(
    GmcStmtT *stmt, const char *userName, const char *processName, const GmcObjectPrivsT *objectPrivs);

Status GmcUserOpCheck(GmcStmtT *stmt, const char *userName, const char *processName);

Status GmcBatchUserOpCheck(GmcStmtT *stmt, GmcUsersCreateInfoT *usersInfo, const GmcUsersCreateResultT *result);

Status GmcIpsRejectOrAccepProcessCheck(GmcStmtT *stmt, GmcIpsOperationTypeE ipsOp);

Status GmcIpsSetUserProtocolCheck(GmcStmtT *stmt, GmcPrivUserProtocolT *userProtocol);

#ifdef __cplusplus
}
#endif

#endif
