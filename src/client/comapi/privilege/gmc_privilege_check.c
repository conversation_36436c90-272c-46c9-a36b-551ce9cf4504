/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_privilege_check.c
 * Description: realization of check interface of gmdb
 * Create: 2021-09-02
 */

#include "gmc_privilege_check.h"
#include "clt_error.h"
#include "clt_check.h"

static Status UserAndProcessCheck(const char *userName, const char *processName)
{
    Status ret = CltCheckStringValid(userName, MAX_OS_USER_NAME_LENGTH, "uname");
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(processName, DB_MAX_PROC_NAME_LEN, "processName");
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static Status UserAndProcessBatchCheck(const char **userName, const char **processName, uint16_t userNum)
{
    if (userNum > DB_MAX_BATCH_OP_NUM_DEFAULT) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "batch create user num exceed the maximum limit");
        return GMERR_INVALID_VALUE;
    }
    if (userNum == 0) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "batch create user num: 0");
        return GMERR_INVALID_VALUE;
    }
    if (userName == NULL || processName == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "batch create user input names ptr");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    for (uint32_t i = 0; i < userNum; i++) {
        Status ret = UserAndProcessCheck(userName[i], processName[i]);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status GmcObjectPrivsCheck(
    GmcStmtT *stmt, const char *userName, const char *processName, const GmcObjectPrivsT *objectPrivs)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltPtr3CheckWithErr(userName, processName, objectPrivs);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = UserAndProcessCheck(userName, processName);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (IsStringEmpty(objectPrivs->objName)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "objName.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

Status GmcUserOpCheck(GmcStmtT *stmt, const char *userName, const char *processName)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = UserAndProcessCheck(userName, processName);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status GmcBatchUserOpCheck(GmcStmtT *stmt, GmcUsersCreateInfoT *usersInfo, const GmcUsersCreateResultT *result)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (stmt->conn->connType != GMC_CONN_TYPE_SYNC) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "only support syn connection in batch create user.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (usersInfo == NULL || result == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "users input struct ptr.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    ret = UserAndProcessBatchCheck(usersInfo->userNames, usersInfo->processNames, usersInfo->userNum);
    if (ret != GMERR_OK) {
        return ret;
    }
    for (uint32_t i = 0; i < usersInfo->userNum; i++) {
        uint16_t reservedConnNum = (usersInfo->reservedConnNumArray)[i];
        if (reservedConnNum > (uint16_t)MAX_RESERVED_CONN_NUM_PER_USER) {
            DB_SET_LASTERR(GMERR_INVALID_VALUE,
                "the reserved conn num execeed maximum.the usr Num index is:%" PRIu16
                ", the reservedConnNum is %" PRIu16 "",
                usersInfo->userNum, reservedConnNum);
            return GMERR_INVALID_VALUE;
        }
    }
    return GMERR_OK;
}

Status GmcIpsRejectOrAccepProcessCheck(GmcStmtT *stmt, GmcIpsOperationTypeE ipsOp)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (ipsOp >= GMC_IPS_OPERATION_BUTT) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "ipsOp.");
        return GMERR_INVALID_VALUE;
    }
    return GMERR_OK;
}

Status GmcIpsSetUserProtocolCheck(GmcStmtT *stmt, GmcPrivUserProtocolT *userProtocol)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (userProtocol == NULL) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "userProtocol ptr");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (userProtocol->protocolType >= GMC_PRIV_PROTOCOL_TYPE_BUTT) {
        DB_SET_LASTERR(GMERR_INVALID_VALUE, "protocolType.");
        return GMERR_INVALID_VALUE;
    }
    return UserAndProcessCheck(userProtocol->userName, userProtocol->processName);
}
