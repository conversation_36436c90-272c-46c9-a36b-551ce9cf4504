/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc.c
 * Description: realization of interface of GmcInit and GmcUninit
 * Author: zhangyong
 * Create: 2021-06-11
 */

#include "gmc.h"
#include "gmc_persist.h"
#include "gmc_ts_persist.h"
#include "clt_async_timeout.h"
#include "clt_graph_filter.h"
#include "clt_resource.h"
#include "clt_proto.h"
#include "gmc_check.h"
#include "clt_check.h"
#include "clt_heartbeat.h"
#include "db_config.h"
#include "db_secure_msg_buffer.h"
#include "clt_msg.h"
#include "dm_data_basic.h"
#include "db_msg_buffer.h"
#include "clt_catalog_sub.h"
#include "adpt_init.h"
#include "db_dyn_load.h"
#include "clt_safety_gate.h"

#define PID_NUM 32

typedef struct {
    GmcAlarmIdE alarmId;
} AlarmDataCfgT;

typedef struct {
    const char *configName;
    const DmValueT *configValue;
} SetCfgT;

typedef struct TagFlushCfg {
    const char *path;
    bool isBackup;
    bool binConsistency;
    bool allowReplace;
    bool reserve;
    GmcDatabaseBackupModeE mode;
} FlushCfgT;

typedef struct {
    const char *path;
} VerifyCfgT;

typedef struct {
    const char *ctrlFilePath;
    const char *tempFilePath;
} SwapDataDirCfgT;

typedef struct {
    const char *configName;
} GetCfgT;

Status GetAlarmDataBuildRequest(FixBufferT *req, const void *in)
{
    const AlarmDataCfgT *config = in;
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_GET_ALARM_DATA);
    // fill alarmId
    return FixBufPutUint32(req, (uint32_t)config->alarmId);
}

Status SetCfgBuildRequest(FixBufferT *req, const void *in)
{
    const SetCfgT *config = in;
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_SET_CFG);
    Status res = HelpFillString(req, true, config->configName);
    if (res != GMERR_OK) {
        return res;
    }
    const DmValueT *value = config->configValue;
    uint32_t size = DmValueGetSeriLen(value);
    res = FixBufPutUint32(req, size);
    if (res != GMERR_OK) {
        return res;
    }
    uint32_t offset;
    res = SecureFixBufReserveDataOffset(req, size, &offset);
    if (res != GMERR_OK) {
        return res;
    }
    uint8_t *addr = SecureFixBufOffsetToAddr(req, offset);
#ifdef SECUREFIXBUF
    if (SECUREC_UNLIKELY(addr == NULL)) {
        return GMERR_CONNECTION_RESET_BY_PEER;
    }
#endif
    return DmValueSeri2InvokerBuf(value, addr);
}

Status GetCfgBuildRequest(FixBufferT *req, const void *in)
{
    const GetCfgT *config = in;
    FillSimpleStmtMsgHeader(req, MSG_OP_RPC_GET_CFG);
    return HelpFillString(req, true, config->configName);
}

static Status CltMemAdaptFuncs(GmcMemAdptFuncsT *gmcMemAdaptedFuncs)
{
    if (gmcMemAdaptedFuncs->memInitFunc == NULL || gmcMemAdaptedFuncs->memAllocFunc == NULL ||
        gmcMemAdaptedFuncs->memFreeFunc == NULL || gmcMemAdaptedFuncs->memFinalizeFunc == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_CONFIG_ERROR, "Empty adapted func.");
        return GMERR_CONFIG_ERROR;
    }
    DbMemAdptFuncsT memAdaptedFuncs = {0};
    memAdaptedFuncs.memInitFunc = (DbMemInitFuncT)gmcMemAdaptedFuncs->memInitFunc;
    memAdaptedFuncs.memAllocFunc = (DbMemAllocFuncT)gmcMemAdaptedFuncs->memAllocFunc;
    memAdaptedFuncs.memFreeFunc = (DbMemFreeFuncT)gmcMemAdaptedFuncs->memFreeFunc;
    memAdaptedFuncs.memFinalizeFunc = (DbMemFinalizeFuncT)gmcMemAdaptedFuncs->memFinalizeFunc;
    memAdaptedFuncs.maxSingleStepAllocSize = gmcMemAdaptedFuncs->maxSingleStepAllocSize;
    return DbAdptRegMemFuncs(&memAdaptedFuncs);
}

static Status LogAdaptFuncs(const GmcLogAdptFuncsT *logAdaptedFuncs)
{
    if (logAdaptedFuncs->userWriteFunc == NULL || logAdaptedFuncs->handle == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_CONFIG_ERROR, "Empty log adapted func.");
        return GMERR_CONFIG_ERROR;
    }

    DbLogRegAdptFuncs(logAdaptedFuncs);
    return GMERR_OK;
}

inline static void FixBufSwap(FixBufferT *a, FixBufferT *b)
{
    DB_POINTER2(a, b);

    // a 与 b 应该相互兼容
    DB_ASSERT(a->flags == b->flags);
    DB_ASSERT(a->memCtx == b->memCtx);

    FixBufferT t = *a;
    *a = *b;
    *b = t;
}

Status GmcRegAdaptFuncs(GmcAdptTypeE adaptType, GmcAdptFuncsHandle adaptFuncs)
{
    if (adaptFuncs == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "The provided adaptation funcs.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (!DbCommonIsSingleCltProcess()) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "reg in current deploy mode.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    Status ret;
    if (adaptType == GMC_ADPT_MEMORY) {
        if (CltCheckInited() == GMERR_OK) {
            DB_LOG_AND_SET_LASERR(GMERR_CONFIG_ERROR, "Can not register adapt functions because of system has "
                                                      "been initialized.");
            return GMERR_CONFIG_ERROR;
        }
        ret = CltMemAdaptFuncs(adaptFuncs);
        return ret;
    }
    // 用户可以多次注册日志函数，日志函数会被更新
    if (adaptType == GMC_ADPT_LOG) {
        ret = LogAdaptFuncs(adaptFuncs);
        return ret;
    }
    return GMERR_CONFIG_ERROR;
}

Status GmcRegTimeoutEpollFunc(GmcEpollRegT epollReg)
{
    Status ret = CltPtrCheckWithErr(epollReg);
    if (ret != GMERR_OK) {
        return GMERR_EPOLL_REG_FUNC_NULL;
    }
    EpollRegT epollRegTmp = {};
    epollRegTmp.epollRegType = EPOLLREG;
    epollRegTmp.epollRegFunc = epollReg;
    epollRegTmp.userData = NULL;
    ret = InitAsyncTimeout(&epollRegTmp);
    return ret;
}

Status GmcRegTimeoutEpollFuncWithUserData(GmcEpollRegWithUserDataT epollReg, void *userData)
{
    Status ret = CltPtrCheckWithErr(epollReg);
    if (ret != GMERR_OK) {
        return GMERR_EPOLL_REG_FUNC_NULL;
    }
    EpollRegT epollRegTmp = {};
    epollRegTmp.epollRegType = EPOLLREG_WITH_USD;
    epollRegTmp.epollRegWithUsDFunc = epollReg;
    epollRegTmp.userData = userData;
    ret = InitAsyncTimeout(&epollRegTmp);
    return ret;
}

Status GmcRegHeartBeatEpollFunc(GmcEpollRegT epollReg)
{
    Status ret = CltPtrCheckWithErr(epollReg);
    if (ret != GMERR_OK) {
        return GMERR_EPOLL_REG_FUNC_NULL;
    }
    EpollRegT epollRegTmp = {};
    epollRegTmp.epollRegType = EPOLLREG;
    epollRegTmp.epollRegFunc = epollReg;
    epollRegTmp.userData = NULL;
    ret = InitHeartBeat(&epollRegTmp);
    return ret;
}

Status GmcRegHeartBeatEpollFuncWithUserData(GmcEpollRegWithUserDataT epollReg, void *userData)
{
    Status ret = CltPtrCheckWithErr(epollReg);
    if (ret != GMERR_OK) {
        return GMERR_EPOLL_REG_FUNC_NULL;
    }
    EpollRegT epollRegTmp = {};
    epollRegTmp.epollRegType = EPOLLREG_WITH_USD;
    epollRegTmp.epollRegWithUsDFunc = epollReg;
    epollRegTmp.userData = userData;
    ret = InitHeartBeat(&epollRegTmp);
    return ret;
}

Status GmcInit(void)
{
    DbSetLogInitFlag(true);
#ifdef EXPERIMENTAL_NERGC
    (void)CltInitLogCtrl();
#endif
    // 校验是否在异步回调中
    Status ret = CltCheckIfInCallBack();
    if (ret != GMERR_OK) {
        return ret;
    }
    CltInstanceT *clt = &g_gmdbCltInstance;
    DbSpinLock(&clt->initLock);
    clt->pid = DbAdptGetpid();
#if defined(FEATURE_CLT_SERVER_SAME_PROCESS) || !defined(NDEBUG)
    if (clt->initCount++ > 0) {
        DbSpinUnlock(&clt->initLock);
        return GMERR_OK;
    }
#endif
    Status result = DbPatchInit();
    if (result != GMERR_OK) {
        DbSpinUnlock(&clt->initLock);
        return result;
    }
    result = CltInit(clt);
    g_initTime++;
    if (g_initTime == 1) {
        uint64_t pid = (uint64_t)DbAdptGetpid();
        uint64_t timeStamp = DbRdtsc();
        clt->cltUnique = (pid << PID_NUM) | timeStamp;
    }
    DbSpinUnlock(&clt->initLock);
#ifdef FEATURE_SERVER_FUNC_REG
    if (result == GMERR_OK) {
        DbDisableRegAdaptFuncs();
    }
#endif
    return result;
}

Status GmcUnInit(void)
{
    // 校验是否在异步回调中
    Status ret = CltCheckIfInCallBack();
    if (ret != GMERR_OK) {
        return ret;
    }
    CltInstanceT *clt = &g_gmdbCltInstance;
    DbSpinLock(&clt->initLock);
#if defined(FEATURE_CLT_SERVER_SAME_PROCESS) || !defined(NDEBUG)
    // 没有init就做uninit或者uinit的次数和init的次数不对应就直接返回
    if (clt->initCount == 0 || --clt->initCount > 0) {
        DbSpinUnlock(&clt->initLock);
        return GMERR_OK;
    }
#endif
    // 因为客户端在建立连接时才会获得存储句柄，所以PageMgr是在clt进行SeOpen时才打开，最终在这里进行释放
    SeReleaseCltPageMgr(clt->memCtx);
    CltUnInit(clt);

    // 清理存储引擎device缓存
    SeClearDevCache();
    DbDynLoadCltComponentizeUnFinished();
    DbPatchUnInit();
    DbSpinUnlock(&clt->initLock);
    return GMERR_OK;
}

Status GmcGetPropertyById(const GmcStmtT *stmt, uint32_t id, void *propValue, uint32_t *propSize, bool *isNull)
{
    Status ret = GmcGetPropertyByIdCheck(stmt, id, propValue, propSize, isNull);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    if (SECUREC_UNLIKELY(
            stmt->fetchEof && ((stmt->stmtType == CLT_STMT_TYPE_VERTEX && stmt->operationType == GMC_OPERATION_SCAN) ||
                                  stmt->stmtType == CLT_STMT_TYPE_SUB_VERTEX || stmt->stmtType == CLT_STMT_TYPE_TS))) {
        return GMERR_NO_DATA;
    }
    return CltGetPropertyById(stmt, id, propValue, propSize, isNull);
}

const char *GmcGetLastError(void)
{
    TextT *text = DbGetLastErrorInfo();
    if (text == NULL) {
        return "";
    }
    return text->str;
}

Status GmcGetAlarmData(GmcStmtT *stmt, GmcAlarmIdE alarmId, GmcAlarmDataT *alarmData)
{
    // step 1: check input
    Status ret = GmcGetAlarmDataCheck(stmt, alarmId, alarmData);
    if (ret != GMERR_OK) {
        return ret;
    }
    // step 2: construct cfg
    AlarmDataCfgT cfg = {};
    cfg.alarmId = alarmId;
    // step 3: send msg and recv msg
    alarmData->alarmId = alarmId;
    ret = CltRequestSync(stmt->conn, GetAlarmDataBuildRequest, &cfg, ProcessAlarmDataResp, alarmData);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get alarmData send and recv");
        return ret;
    }
    return ret;
}

static Status CltGetValueFromBuf(FixBufferT *buf, DmValueT *value)
{
    TextT text;
    Status ret = FixBufGetObject(buf, &text);
    if (ret == GMERR_OK) {
        ret = DmValueDeSeriNoCopy((uint8_t *)text.str, text.len, value);
    }
    return ret;
}

static Status CltRecvCfgFromBuf(FixBufferT *buf, CltCfgValueT *cfg)
{
    Status ret = SecureFixBufGetText(buf, &cfg->cfgName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SecureFixBufGetText(buf, &cfg->cfgDesc);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SecureFixBufGetUint32(buf, &cfg->dataType);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltGetValueFromBuf(buf, &cfg->curValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltGetValueFromBuf(buf, &cfg->minValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltGetValueFromBuf(buf, &cfg->maxValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltGetValueFromBuf(buf, &cfg->defaultValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SecureFixBufGetUint32(buf, &cfg->changeMode);
    return ret;
}

static void CltGetInfoFromText(TextT *val, GmcDataTypeE *type, void **value, uint32_t *size)
{
    *type = GMC_DATATYPE_STRING;
    *value = val->str;
    *size = val->len;
}

static void CltGetInfoFromUint32(uint32_t *val, GmcDataTypeE *type, void **value, uint32_t *size)
{
    *type = GMC_DATATYPE_UINT32;
    *value = val;
    *size = sizeof(uint32_t);
}

static void CltGetInfoFromValue(DmValueT *val, GmcDataTypeE *type, void **value, uint32_t *size)
{
    uint32_t data = (uint32_t)val->type;
    *type = data;

    if (!DM_TYPE_NEED_MALLOC(data)) {
        *value = &val->value;
        *size = DmGetBasicDataTypeLength(data);
        return;
    }

    void *addr = (void *)val->value.strAddr;
    uint32_t len = val->value.length;
    *value = addr, *size = len;
}

Status GmcSetCfg(GmcStmtT *stmt, const char *confName, GmcDataTypeE type, const void *value, uint32_t valueSize)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (IsStringEmpty(confName)) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "confName when set config");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (value == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "value when set config");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (type != GMC_DATATYPE_INT32 && type != GMC_DATATYPE_STRING) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_VALUE, "type when set config");
        return GMERR_INVALID_VALUE;
    }

    DmValueT propertyValue;  // valueSize will be checked by SetDmValue
    ret = SetDmValue(&propertyValue, (uint32_t)type, value, valueSize);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set property value");
        return ret;
    }
    SetCfgT cfg = {};
    cfg.configName = confName;
    cfg.configValue = &propertyValue;
    ret = CltRequestSync(stmt->conn, SetCfgBuildRequest, &cfg, DummyParseRsp, NULL);
    return ret;
}

Status GmcGetCfg(GmcStmtT *stmt, const char *confName)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (IsStringEmpty(confName)) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "confName when get config");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    GetCfgT cfg = {};
    cfg.configName = confName;
    ret = CltRequestSync(stmt->conn, GetCfgBuildRequest, &cfg, DummyParseRsp, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }

    FixBufferT *buffer = &stmt->recvPack;
    FixBufSwap(&stmt->conn->recvPack, buffer);
    OpHeaderT *op = ProtocolPeekFirstOpHeader(buffer);
    ret = CltRecvCfgFromBuf(buffer, &stmt->cfgValue);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "get config message from buffer.");
        op->opCode = MSG_OP_RPC_NONE;  // invalidate result
        return ret;
    }
    if (stmt->cfgValue.dataType >= DB_DATATYPE_NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "the config dataType: %" PRIu32 ".", stmt->cfgValue.dataType);
        return GMERR_DATA_EXCEPTION;
    }
    return ret;
}

Status GmcGetCfgInfoByType(
    GmcStmtT *stmt, GmcCfgInfoTypeE infoType, GmcDataTypeE *type, void **value, uint32_t *valueSize)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltPtr3CheckWithErr(type, value, valueSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    const FixBufferT *buffer = &stmt->recvPack;
    if (FixBufGetPos(buffer) < MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE ||
        ProtocolPeekFirstOpHeader(buffer)->opCode != MSG_OP_RPC_GET_CFG) {
        DB_LOG_AND_SET_LASERR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "config value need retry to get properly");
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }

    CltCfgValueT *cfg = &stmt->cfgValue;
    switch (infoType) {
        case GMC_CFG_INFO_TYPE_NAME:
            CltGetInfoFromText(&cfg->cfgName, type, value, valueSize);
            return GMERR_OK;
        case GMC_CFG_INFO_TYPE_DESC:
            CltGetInfoFromText(&cfg->cfgDesc, type, value, valueSize);
            return GMERR_OK;
        case GMC_CFG_INFO_TYPE_CUR_VALUE:
            CltGetInfoFromValue(&cfg->curValue, type, value, valueSize);
            return GMERR_OK;
        case GMC_CFG_INFO_TYPE_MIN_VALUE:
            CltGetInfoFromValue(&cfg->minValue, type, value, valueSize);
            return GMERR_OK;
        case GMC_CFG_INFO_TYPE_MAX_VAULE:
            CltGetInfoFromValue(&cfg->maxValue, type, value, valueSize);
            return GMERR_OK;
        case GMC_CFG_INFO_TYPE_DEFAULT_VALUE:
            CltGetInfoFromValue(&cfg->defaultValue, type, value, valueSize);
            return GMERR_OK;
        case GMC_CFG_INFO_TYPE_CHANGE_MODE:
            CltGetInfoFromUint32(&cfg->changeMode, type, value, valueSize);
            return GMERR_OK;
        case GMC_CFG_INFO_TYPE_DATA_TYPE:
            CltGetInfoFromUint32(&cfg->dataType, type, value, valueSize);
            return GMERR_OK;
        default:
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "infoType");
            return GMERR_INVALID_PARAMETER_VALUE;
    }
}

// labelType的结构参考QryGetDetailedLabelTypeByName接口的注释
Status GmcGetLabelTypeByName(GmcStmtT *stmt, const char *labelName, uint32_t *labelType)
{
    Status ret = GmcGetLabelTypeByNameCheck(stmt, labelName, labelType);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t detailedType;
    ret = CltGetLabelTypeByName(stmt, labelName, &detailedType);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t type = CltExtractLabelType(detailedType);
    if (type != VERTEX_LABEL && type != KV_TABLE) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "the value of labelType: %" PRIu32 ", detailed type is %" PRIu32 ".", type, detailedType);
        return GMERR_DATA_EXCEPTION;
    }

    *labelType = (type == VERTEX_LABEL) ? GMC_LABEL_TYPE_VERTEX : GMC_LABEL_TYPE_KV;
    return GMERR_OK;
}

int32_t GmcGetVertexLabelTypeByName(GmcStmtT *stmt, const char *labelName, GmcVertexLabelTypeE *vertexLabelType)
{
    Status ret = GmcGetLabelTypeByNameCheck(stmt, labelName, vertexLabelType);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t detailedType;
    ret = CltGetLabelTypeByName(stmt, labelName, &detailedType);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t type = CltExtractVertexLabelType(detailedType);
    if (type >= GMC_VERTEX_TYPE_BUTT) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "not a vertexlabel type: %" PRIu32 ", detailed type is %" PRIu32 ".", type, detailedType);
        return GMERR_DATA_EXCEPTION;
    }

    *vertexLabelType = type;
    return GMERR_OK;
}

int32_t GmcGetDatalogLabelTypeByName(GmcStmtT *stmt, const char *labelName, GmcDtlLabelTypeE *datalogLabelType)
{
    Status ret = GmcGetLabelTypeByNameCheck(stmt, labelName, datalogLabelType);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t detailedType;
    ret = CltGetLabelTypeByName(stmt, labelName, &detailedType);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t type = CltExtractDatalogLabelType(detailedType);
    if (type >= GMC_DTL_TYPE_BUTT) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "not a datalog label, type is %" PRIu32 ", detailed type is %" PRIu32 ".", type, detailedType);
        return GMERR_DATA_EXCEPTION;
    }

    *datalogLabelType = type;
    return GMERR_OK;
}

Status GmcLabelIsExist(GmcStmtT *stmt, const char *labelName, bool *isExist)
{
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    Status ret = GmcLabelIsExistCheck(stmt, labelName, isExist);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = CltGetLabelByName(stmt, labelName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get label %s exist when send msg.", labelName);
        return ret;
    }

    uint32_t exist = 0u;
    FixBufferT *buffer = &stmt->conn->recvPack;
    ret = SecureFixBufGetUint32(buffer, &exist);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get label %s exist when RecvGetLabelExist.", labelName);
        return ret;
    }
    *isExist = (exist != 0u);
    return GMERR_OK;
#endif
    DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "GmcLabelIsExist not in warm reboot.");
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status GmcSetCltCfg(const char *cfgName, GmcDataTypeE type, const void *value, uint32_t valueSize)
{
    Status ret = GmcSetCltCfgCheck(cfgName, type, value);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetCltCfgBeforeInitCheck(cfgName);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmValueT propertyValue;
    ret = SetDmValue(&propertyValue, (uint32_t)type, value, valueSize);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set value when set config");
        return ret;
    }

    DbCfgValueT cfgValue = {};
    cfgValue.type = propertyValue.type;
    cfgValue.int32Val = propertyValue.value.intValue;

    return DbCltCfgSetByName(cfgName, &cfgValue);
}

static Status FlushDataBuildReq(FixBufferT *req, const void *in)
{
    const FlushCfgT *config = in;
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_FLUSH_DATA);
    Status ret = HelpFillString(req, true, config->path);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(req, (uint32_t)config->isBackup);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(req, (uint32_t)config->binConsistency);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(req, (uint32_t)config->allowReplace);
    if (ret != GMERR_OK) {
        return ret;
    }
    return FixBufPutUint32(req, (uint32_t)config->mode);
}

Status GmcFlushData(GmcStmtT *stmt, const char *path, bool binConsistency)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (path != NULL && strlen(path) >= DB_MAX_PATH) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE,
            "flush path length exceeds the limit, path:%s, DB_MAX_PATH: %" PRIu32 ".", path, (uint32_t)strlen(path));
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    FlushCfgT cfg = {
        .path = path,
        .isBackup = false,
        .binConsistency = binConsistency,
        .allowReplace = false,
        .mode = GMC_DATABASE_BACKUP_FULL,
    };
    return CltRequestSync(stmt->conn, FlushDataBuildReq, &cfg, DummyParseRsp, NULL);
}

Status GmcFlushDataBackup(GmcStmtT *stmt, const char *path, bool allowReplace, GmcDatabaseBackupModeE mode)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (path == NULL || *path == '\0') {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Backup to a empty path,");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    if (strlen(path) >= DB_MAX_PATH) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE,
            "backup path length exceeds the limit, path:%s, DB_MAX_PATH: %" PRIu32 ".", path, (uint32_t)strlen(path));
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    if (mode > GMC_DATABASE_BACKUP_SCHEMA) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Unrecoginized backup mode %" PRIu32 ".", mode);
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    FlushCfgT cfg = {
        .path = path,
        .isBackup = true,
        .binConsistency = false,
        .allowReplace = allowReplace,
        .mode = mode,
    };
    return CltRequestSync(stmt->conn, FlushDataBuildReq, &cfg, DummyParseRsp, NULL);
}

static Status FlushEnableBuildReq(FixBufferT *req, const void *enable)
{
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_FLUSH_ENABLE);
    return FixBufPutUint32(req, (uint32_t)(*((const bool *)enable)));
}

Status GmcFlushEnable(GmcConnT *conn, bool enable)
{
    Status ret = CltPtrCheckWithErr(conn);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckConnType(conn, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltRequestSync(conn, FlushEnableBuildReq, &enable, DummyParseRsp, NULL);
}

static Status VerifyPersistDataBuildReq(FixBufferT *req, const void *in)
{
    const VerifyCfgT *config = in;
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_VERIFY_PERSIST_DATA);
    return HelpFillString(req, true, config->path);
}

Status GmcVerifyPersistenceData(GmcStmtT *stmt, const char *path)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (path != NULL && strlen(path) >= DB_MAX_PATH) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE,
            "flush path length exceeds the limit, path:%s, DB_MAX_PATH: %" PRIu32 ".", path, (uint32_t)strlen(path));
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    VerifyCfgT cfg = {.path = path};
    return CltRequestSync(stmt->conn, VerifyPersistDataBuildReq, &cfg, DummyParseRsp, NULL);
}

static Status SwapDataDirBuildReq(FixBufferT *req, const void *in)
{
    const SwapDataDirCfgT *config = in;
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_SWAP_DATA_DIR);
    Status ret = HelpFillString(req, true, config->ctrlFilePath);
    if (ret != GMERR_OK) {
        return ret;
    }
    return HelpFillString(req, true, config->tempFilePath);
}

Status GmcSwapDataDir(GmcStmtT *stmt, const char *ctrlFilePath, const char *tempFilePath)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = GmcSwapPathCheck(ctrlFilePath, tempFilePath);
    if (ret != GMERR_OK) {
        return ret;
    }

    SwapDataDirCfgT cfg = {.ctrlFilePath = ctrlFilePath, .tempFilePath = tempFilePath};
    return CltRequestSync(stmt->conn, SwapDataDirBuildReq, &cfg, DummyParseRsp, NULL);
}

void GmcOpenGate(void)
{
#ifdef SAFETY_GATE
    SafetyGateOpen();
#else
    return;
#endif
}

void GmcCloseGate(void)
{
#ifdef SAFETY_GATE
    SafetyGateClose();
#else
    return;
#endif
}
