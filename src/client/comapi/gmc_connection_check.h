/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_connection_check.h
 * Description: header file for GMDB client gmc connection check
 * Create: 2021-8-21
 */

#ifndef GMC_CONNECTION_CHECK_H
#define GMC_CONNECTION_CHECK_H

#include "gmc_types.h"
#include "adpt_define.h"
#include "adpt_types.h"
#include "clt_error.h"

#ifdef __cplusplus
extern "C" {
#endif

#define CLT_CALLBACK_TIMEOUT_MIN 1     // 回调函数超时打印日志，最小超时时间阈值1ms
#define CLT_CALLBACK_TIMEOUT_MAX 1000  // 回调函数超时打印日志，最大超时时间阈值1000ms

#define CLT_ASYNC_TIMEOUT_MIN 800      // 异步超时处理最小值ms
#define CLT_ASYNC_TIMEOUT_MAX 1000000  // 异步超时处理最大值ms

#define SUB_MSG_RING_SIZE_MIN 0x40     // 服务端订阅缓存队列最小值
#define SUB_MSG_RING_SIZE_MAX 0x10000  // 服务端订阅缓存队列最大值

Status GmcSetStmtAttrCheck(GmcStmtT *stmt, GmcStmtAttrTypeE attr, const void *value, uint32_t valueSize);
Status GmcGetStmtAttrCheck(GmcStmtT *stmt, GmcStmtAttrTypeE attr, const void *value, uint32_t valueSize);

static inline Status GmcStmtAttrUseNewDeseriCheck(const void *value, uint32_t valueSize)
{
    Status ret = GMERR_OK;
    if (SECUREC_UNLIKELY(valueSize < sizeof(bool))) {
        DB_SET_LASTERR((ret = GMERR_INVALID_PARAMETER_VALUE), "attr value size.");
        return ret;
    }
    // 只允许设置为true,不允许为false.
    if (SECUREC_UNLIKELY(!(*(bool *)(uint8_t *)(uintptr_t)(value)))) {
        DB_SET_LASTERR((ret = GMERR_INVALID_PARAMETER_VALUE), "false value.");
    }
    return ret;
}

#ifdef __cplusplus
}
#endif

#endif
