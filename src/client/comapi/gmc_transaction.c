/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_transaction.c
 * Description: gmdb client transaction interface
 * Author: zhangyong
 * Create: 2021-06-11
 */
#include "gmc.h"
#include "gmc_internal.h"

#include "clt_check.h"
#include "clt_msg.h"

static Status GmcTransCheck(GmcConnT *conn, GmcConnTypeE expectedConnType)
{
    Status ret = CltPtrCheckWithErr(conn);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckConnType(conn, expectedConnType);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static Status GmcTransSavepointCheck(GmcConnT *conn, const char *savepointName, GmcConnTypeE expectedConnType)
{
    Status ret = GmcTransCheck(conn, expectedConnType);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (savepointName != NULL) {
        ret = CheckStringInvalid(savepointName, MAX_SAVEPOINT_LENGTH);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_VALUE, "Savepoint name exceeded limit!");
            return GMERR_INVALID_VALUE;
        }
    }
    return GMERR_OK;
}

static Status TransStartBuildReq(FixBufferT *req, const void *in)
{
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_TX_START);
    const GmcTxConfigT *config = in;
    return SecureFixBufPut4Uint32(req, (uint32_t)config->type, (uint32_t)config->readOnly, (uint32_t)config->trxType,
        (uint32_t)config->forceCommitMode);
}

static Status TransEndBuildReq(FixBufferT *req, const void *in)
{
    const MsgOpcodeRpcE *opCode = in;
    FillPublicStmtMsgHeader(req, *opCode);
    return GMERR_OK;
}

static Status TransStartSync(GmcConnT *conn, const GmcTxConfigT *config)
{
    Status ret = CltRequestSync(conn, TransStartBuildReq, config, DummyParseRsp, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }
    conn->transMode = GMC_TRANS_USED_IN_CS;
    return GMERR_OK;
}

static Status TransEndSync(GmcConnT *conn, const MsgOpcodeRpcE *opCode)
{
    Status ret = CltRequestSync(conn, TransEndBuildReq, opCode, DummyParseRsp, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }
    conn->transMode = GMC_TRANS_NONE;
    return GMERR_OK;
}

static void TransAsyncCallback(GmcConnT *conn, const AsyncMsgContextT *ctx, Status ret, const void *out)
{
    DB_UNUSED(out);

    if (ret != GMERR_OK) {
        const TextT *error = DbGetLastErrorInfo();
        InvokeUserCallback(GmcTransDoneT, ctx->userCb, ctx->userData, ret, error->str);
        return;
    }

    conn->transMode = (uint8_t)ctx->cltDataU32;  // 由Async接口设置，保证有效
    InvokeUserCallback(GmcTransDoneT, ctx->userCb, ctx->userData, GMERR_OK, NULL);
}

static Status TransStartAsync(GmcConnT *conn, const GmcTxConfigT *config, GmcTransDoneT userCb, void *userData)
{
    AsyncMsgContextT ctx = MakeAsyncMsgContext(DummyParseRsp, TransAsyncCallback, userCb, userData, false);
    ctx.cltDataU32 = GMC_TRANS_USED_IN_CS;
    return CltRequestAsync(conn, TransStartBuildReq, config, &ctx);
}

static Status TransEndAsync(GmcConnT *conn, const MsgOpcodeRpcE *opCode, GmcTransDoneT userCb, void *userData)
{
    AsyncMsgContextT ctx = MakeAsyncMsgContext(DummyParseRsp, TransAsyncCallback, userCb, userData, false);
    ctx.cltDataU32 = GMC_TRANS_NONE;
    return CltRequestAsync(conn, TransEndBuildReq, opCode, &ctx);
}

static GmcTxConfigT CltTranslateTxConfig(const GmcTxConfigT *config)
{
    if (config != NULL) {
        return *config;
    }
    GmcTxConfigT defaultCfg = {
        .readOnly = false,
        .transMode = GMC_TRANS_USED_IN_CS,
        .type = GMC_TX_ISOLATION_DEFAULT,
        .trxType = GMC_DEFAULT_TRX,
    };
    return defaultCfg;
}

Status GmcTransStart(GmcConnT *conn, const GmcTxConfigT *config)
{
    Status ret = GmcTransCheck(conn, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FlowControlWhenStartTx(&conn->flowControl);
    if (ret != GMERR_OK) {
        return ret;
    }

    const GmcTxConfigT cfg = CltTranslateTxConfig(config);
    if (cfg.transMode == GMC_TRANS_USED_IN_CS) {
        return TransStartSync(conn, &cfg);
    }

    DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "config.");
    return GMERR_INVALID_PARAMETER_VALUE;
}

Status GmcTransStartAsync(GmcConnT *conn, const GmcTxConfigT *config, GmcTransDoneT userCb, void *userData)
{
    Status ret = GmcTransCheck(conn, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FlowControlWhenStartTx(&conn->flowControl);
    if (ret != GMERR_OK) {
        return ret;
    }

    const GmcTxConfigT cfg = CltTranslateTxConfig(config);
    if (cfg.transMode == GMC_TRANS_USED_IN_CS) {
        return TransStartAsync(conn, &cfg, userCb, userData);
    }
    DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "config.");
    return GMERR_INVALID_PARAMETER_VALUE;
}

Status GmcTransCommit(GmcConnT *conn)
{
    Status ret = GmcTransCheck(conn, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    const MsgOpcodeRpcE opCode = MSG_OP_RPC_TX_COMMIT;
    return TransEndSync(conn, &opCode);
}

Status GmcTransCommitAsync(GmcConnT *conn, GmcTransDoneT userCb, void *userData)
{
    Status ret = GmcTransCheck(conn, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    const MsgOpcodeRpcE opCode = MSG_OP_RPC_TX_COMMIT;
    return TransEndAsync(conn, &opCode, userCb, userData);
}

Status GmcTransRollBack(GmcConnT *conn)
{
    Status ret = GmcTransCheck(conn, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    const MsgOpcodeRpcE opCode = MSG_OP_RPC_TX_ROLLBACK;
    return TransEndSync(conn, &opCode);
}

Status GmcTransRollBackAsync(GmcConnT *conn, GmcTransDoneT userCb, void *userData)
{
    Status ret = GmcTransCheck(conn, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    const MsgOpcodeRpcE opCode = MSG_OP_RPC_TX_ROLLBACK;
    return TransEndAsync(conn, &opCode, userCb, userData);
}

static Status TransAbortBuildReq(FixBufferT *req, const void *in)
{
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_TX_ABORT);
    const uint64_t *transId = in;
    return SecureFixBufPutUint64(req, *transId);
}

Status GmcTransAbort(GmcConnT *conn, uint64_t transId)
{
    Status ret = GmcTransCheck(conn, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    return CltRequestSync(conn, TransAbortBuildReq, &transId, DummyParseRsp, NULL);
}

void GmcCloseVertexLabel(void *label)
{
    CltCataCloseVertexLabel((CltCataLabelT *)label);
}

static Status CreateSavePointBuildReq(FixBufferT *req, const void *in)
{
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_TX_CREATE_SAVEPOINT);
    return SecureFixBufPutStringNullable(req, in);
}

static Status ReleaseSavePointBuildReq(FixBufferT *req, const void *in)
{
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_TX_RELEASE_SAVEPOINT);
    return SecureFixBufPutStringNullable(req, in);
}

static Status RollbackSavePointBuildReq(FixBufferT *req, const void *in)
{
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_TX_ROLLBACK_SAVEPOINT);
    return SecureFixBufPutStringNullable(req, in);
}

Status GmcTransCreateSavepointAsync(GmcConnT *conn, const char *savepointName, GmcTransDoneT userCb, void *userData)
{
    Status ret = GmcTransSavepointCheck(conn, savepointName, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    AsyncMsgContextT ctx = MakeAsyncMsgContext(DummyParseRsp, CommonDDLCallback, userCb, userData, false);
    return CltRequestAsync(conn, CreateSavePointBuildReq, savepointName, &ctx);
}

Status GmcTransReleaseSavepointAsync(GmcConnT *conn, const char *savepointName, GmcTransDoneT userCb, void *userData)
{
    Status ret = GmcTransSavepointCheck(conn, savepointName, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    AsyncMsgContextT ctx = MakeAsyncMsgContext(DummyParseRsp, CommonDDLCallback, userCb, userData, false);
    return CltRequestAsync(conn, ReleaseSavePointBuildReq, savepointName, &ctx);
}

Status GmcTransRollBackSavepointAsync(GmcConnT *conn, const char *savepointName, GmcTransDoneT userCb, void *userData)
{
    Status ret = GmcTransSavepointCheck(conn, savepointName, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    AsyncMsgContextT ctx = MakeAsyncMsgContext(DummyParseRsp, CommonDDLCallback, userCb, userData, false);
    return CltRequestAsync(conn, RollbackSavePointBuildReq, savepointName, &ctx);
}

Status GmcTransCheckOptimisticTrxConflictAsync(GmcConnT *conn, GmcTransDoneT userCb, void *userData)
{
    Status ret = GmcTransCheck(conn, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    AsyncMsgContextT ctx = MakeAsyncMsgContext(DummyParseRsp, TransAsyncCallback, userCb, userData, false);
    ctx.cltDataU32 = GMC_TRANS_NONE;
    const MsgOpcodeRpcE opCode = MSG_OP_RPC_TX_CHECK_OPTIMISTIC_CONFLICT;
    return CltRequestAsync(conn, TransEndBuildReq, &opCode, &ctx);
}
