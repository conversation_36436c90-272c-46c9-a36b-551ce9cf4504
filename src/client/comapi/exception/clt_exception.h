/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: clt_exception.h
 * Description: Implement of GMDB client exception handle
 * Author:
 * Create: 2023-03-13
 */

#ifndef CLT_EXCEPTION_H
#define CLT_EXCEPTION_H

#include "adpt_define.h"
#include "adpt_spinlock.h"
#include "db_linkedlist.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_PROTECT_THREAD_NUM 30  // 支持子线程注册至保护线程的个数

/**
 * @brief 信号处理策略枚举
 * 策略0，客户端不在共享内存操作区
 * 策略1，客户端在操作共享内存区，且信号为外部信号，比如SIGABRT、SIGTERM
 * 策略2，客户端在操作共享内存区，且信号为内部信号，比如SIGSEGV、SIGBUS
 */
typedef enum DbSignalStrategy {
    POLICY_ZERO,
    POLICY_ONE,
    POLICY_TWO,
    POLICY_ONE_STAGE_TWO,
} DbSignalStrategyE;

typedef struct ProtectThreadContext {
    volatile uint32_t shmOperCounts[(uint32_t)SHMEM_OP_BUTT];  // 共享内存区操作个数，按业务类型计数
    uint32_t isAllowManipuShm;
    uint32_t isSignalRegistered;
} ProtectThreadContextT;

extern ProtectThreadContextT g_gmdbCltProtectThreadCtx;

Status CltOperExternalSignal(void);

/**
 * @brief 客户端是否注册信号接管条件判断函数，只有注册了接管信号钩子，才允许走直连写以及创建状态合并订阅
 * @return true 如果信号已经注册
 */
bool CltIsSignalRegistered(void);

/**
 * @brief 重置信号量相关状态，主要用于测试，信号接管后如果不杀死客户端进程，需要重置下信号状态标记
 */
void CltResetSignalStatus(void);

/**
 * @brief 信号注册函数，客户端进程启动时，直接调用此方法即可，主要用于DB测试，业务使用Orm提供的注册接口
 * @attention 在设备上，平台有一套信号接管方案，优先调用平台信号接管接口
 * 在HPE环境上，客户端进程不退出，默认信号注册成功
 * linux提供的sigaction接口和平台有冲突，可能不生效
 *
 * 注册的三种处理策略
 * @li 策略0，重新抛出signo，走系统默认处理
 * @li 策略1，不处理，待下一次重新抛出对应信号
 * @li 策略2，设备上单板重启，linux平台停止客户端，并记录日志
 */
Status CltTryRegisterSignal(void);

#ifdef __cplusplus
}
#endif

#endif /* CLT_EXCEPTION_H */
