/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: gmc_exception_empty.c
 * Description: empty realization of interface of gmdb client exception handle
 * Author:
 * Create: 2024-08-16
 */

#include "gmc.h"
#include "gmc_internal.h"
#include "clt_exception.h"
#include "adpt_atomic.h"

ProtectThreadContextT g_gmdbCltProtectThreadCtx = {
    .isAllowManipuShm = 1,
};

void CltResetSignalStatus(void)
{
    return;
}

Status CltOperExternalSignal(void)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status CltProtectShmProcess(OperShmFuncT operShmFunc, void *args, ShmemOperateTypeE type)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

void GmcSignalRegisterNotify(void)
{
    return;
}

inline bool CltIsSignalRegistered(void)
{
    return false;
}

int32_t GmcCrashHandlerHook(GmcSignalInfoT *signalInfo, GmcSignalOutputDataT *outputData)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status CltTryRegisterSignal(void)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}
