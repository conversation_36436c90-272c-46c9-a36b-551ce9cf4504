/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: clt_sub_conn.h
 * Description: Implement of GMDB client sub push event handler.
 * Author: cailinfeng
 * Create: 2021-08-30
 */

#ifndef CLT_SUB_CONN_H
#define CLT_SUB_CONN_H

#include "adpt_define.h"
#include "adpt_eventfd.h"
#include "db_msg_buffer.h"
#include "db_notify_channel.h"

#ifdef __cplusplus
extern "C" {
#endif

#define SUB_FAILED_MSG_TYPE 1
#define SUB_FETCH_COUNT_MAX 64
#define SUB_FETCH_TIME_MAX (32 * 1000)

typedef struct {
    GmcStmtT *stmt;
    GmcConnT *conn;
} SubEvtCallBackArgs;

void SubPushCb(int32_t fd, void *arg);
void SubPushStatusMergeCb(int32_t fd, void *arg);

// 对于不同类型订阅消息的fetch操作，目前只有单表订阅
Status SubPushVertexFetch(GmcStmtT *stmt, bool *eof);
Status SubPushKvFetch(GmcStmtT *stmt, bool *eof);
Status CltGetDlrSubVertex(GmcStmtT *stmt, FixBufferT *buffer, TextT *vertexData, uint64_t *oldVersion, bool *match);
Status CltGetDlrNextTuple(GmcStmtT *stmt, uint32_t *op);
void CltClearSubsOnSubConn(GmcConnT *subConn);
// 使用instanceId作为高4位和metaId作为低28位作为key去存储订阅关系，instance 4位足够，metaId28位也足够
static inline uint32_t StmgSubSetMetaHash(uint32_t instanceId, uint32_t metaId)
{
    uint32_t metaHash = (instanceId << 28) | metaId;
    return metaHash;
}

Status SelfAwakeNotifyFd(GmcConnT *subConn);

typedef struct NotifyChanWriteArgs {
    DbNotifyMsgQueueT *msgQueue;
    const DbNotifyDataT *data;
    DbNotifyPriorityE priority;
} NotifyChanWriteArgsT;

// 只往共享内存写数据，不使用notifyChann fd唤醒，客户端使用，流程中的rwLatch记录信息部分有误
static inline Status NotifyChanWriteShmProcess(void *args)
{
    NotifyChanWriteArgsT *sendArgs = (NotifyChanWriteArgsT *)args;
    return DbNotifyMsgQueueWrite(sendArgs->msgQueue, sendArgs->data, sendArgs->priority);
}

typedef struct NotifyChanReadArgs {
    DbMemCtxT *memCtx;
    DbNotifyMsgQueueT *msgQueue;
    DbNotifyMsgBufferT **notifyMsgBuf;
} NotifyChanReadArgsT;

static inline Status NotifyChanReadShmProcess(void *args)
{
    NotifyChanReadArgsT *readArgs = (NotifyChanReadArgsT *)args;
    return DbNotifyChannelRecvOpen(readArgs->memCtx, readArgs->msgQueue, readArgs->notifyMsgBuf);
}

#ifdef __cplusplus
}
#endif

#endif /* CLT_SUB_CONN_H */
