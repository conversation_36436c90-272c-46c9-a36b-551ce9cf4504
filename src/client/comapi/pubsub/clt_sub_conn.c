/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: clt_sub_conn.c
 * Description: Implement of GMDB client sub push event handler.
 * Author: zhangyong
 * Create: 2021-06-16
 */

#include "clt_sub_conn.h"

#include <dlfcn.h>
#include "clt_catalog_sub.h"
#include "clt_msg.h"
#include "clt_stmt.h"
#include "clt_exception.h"
#include "db_rpc_msg.h"
#include "db_share_msg_node.h"
#include "clt_check.h"
#include "gmc_subscription.h"
#include "gmc_internal.h"
#include "clt_cond_filter.h"
#include "se_subs_status_merge.h"
#include "clt_da_read.h"
#include "clt_da_vertex_directread.h"
#include "clt_da_graph.h"
#include "adpt_io.h"
#include "adpt_process_name.h"
#include "clt_namespace.h"

/*
 * 解析紧跟在DbSubsEventAckT后的内容。
 * DbSubsEventAckT |
 *   subsName | labelName | msgType | [msgData] |
 *   subsName | labelName | msgType | [msgData] |
 *   ...
 */
static Status CltSubParseHeader(FixBufferT *msg, GmcSubMsgInfoT *info)
{
    TextT subName;
    Status ret = SecureFixBufGetText(msg, &subName);
    if (ret != GMERR_OK) {
        goto FAIL;
    }

    TextT labelName;
    ret = SecureFixBufGetText(msg, &labelName);
    if (ret != GMERR_OK) {
        goto FAIL;
    }
#ifdef FEATURE_GQL
    info->labelName = labelName.str;
#endif  // FEATURE_GQL

    ret = SecureFixBufGetUint16(msg, &info->msgType);
    if (ret != GMERR_OK) {
        goto FAIL;
    }

    info->subscriptionName = subName.str;
    return GMERR_OK;

FAIL:
    DB_LOG_ERROR(ret, "parse sub specific data.");
    return ret;
}

static void CheckAvgSubCallbackExecTimeout(
    uint64_t costTime, GmcConnT *conn, const GmcSubMsgInfoT *info, CltSubscriptionT *sub)
{
    DB_POINTER3(conn, info, sub);
    // 进入下一个周期，如果上一个周期内平均回调时间超过阈值，打印日志后并刷新记录。
    if (sub->subUserCbTimeDetails.subUserCbExecCnt >= CLT_SUB_USERCB_TIME_STAT_TIMES) {
        uint64_t avgTime =
            sub->subUserCbTimeDetails.subUserCbExecTotalTime / sub->subUserCbTimeDetails.subUserCbExecCnt;
        if (SECUREC_UNLIKELY(avgTime > (uint64_t)conn->avgCallbackTimeoutMs)) {
            DB_LOG_WARN(GMERR_PROGRAM_LIMIT_EXCEEDED,
                "user callback func execute too long in last period, average cost time: %" PRIu64
                "(ms), more than: %" PRIu64 "(ms), sub name: %s",
                avgTime, (uint64_t)conn->avgCallbackTimeoutMs, info->subscriptionName);
        }
        sub->subUserCbTimeDetails.subUserCbExecCnt = 0;
        sub->subUserCbTimeDetails.subUserCbExecTotalTime = 0;
    }

    sub->subUserCbTimeDetails.subUserCbExecCnt++;
    sub->subUserCbTimeDetails.subUserCbExecTotalTime += costTime;
}

static void CheckSingleSubCallbackExecTimeout(
    uint64_t costTime, GmcConnT *conn, const GmcSubMsgInfoT *info, CltSubscriptionT *sub, uint16_t rows)
{
    // 判断单个订阅回调是否超过阈值，如果超过，打印日志并记录。
    if (SECUREC_UNLIKELY(costTime > (uint64_t)conn->singleCallbackTimeoutMs)) {
        const char *funcName = "";
        Dl_info dlInfo;
        int32_t rc = dladdr((void *)sub->userCb, &dlInfo);
        if (rc != 0 && dlInfo.dli_sname != NULL) {
            funcName = dlInfo.dli_sname;
        }
        DB_LOG_WARN(GMERR_PROGRAM_LIMIT_EXCEEDED,
            "user callback func execute too long, remoteId %" PRIu16 ", connName %s, cost time: %" PRIu64
            "(ms), more than: %" PRIu32 "(ms), fetch rows number: %" PRIu16
            ", user callback func name: %s, sub name: %s,  sub event type: %" PRIu32 ".",
            conn->remoteId, conn->connName == NULL ? "NULL" : conn->connName, costTime, conn->singleCallbackTimeoutMs,
            rows, funcName, info->subscriptionName, (uint32_t)info->eventType);
        conn->timeCost.userCbTimeCostDetails.userCbTimeoutCnt++;
    }
    CallbackExecTimeStatistic(costTime, conn);
}

static void InitsubSingleLabel(SubPushSingleLabel *subSingleLabel, CltSubscriptionT *sub, FixBufferT *msgData,
    const DbSubsEventAckT *ack, const GmcSubMsgInfoT *info)
{
    subSingleLabel->base = sub->base;
    subSingleLabel->cltCataLabel = sub->cltCataLabel;
    subSingleLabel->msgData = msgData;
    subSingleLabel->msgMaxType = ack->msgMaxType;
    subSingleLabel->eventType = (uint32_t)info->eventType;
    subSingleLabel->requiredType = info->msgType;
    subSingleLabel->rowsRemain = ack->rows;
    subSingleLabel->msgVersion = ack->version;
    subSingleLabel->versionMap = sub->versionMap;
    subSingleLabel->isStatusMerge = false;
    subSingleLabel->isDatalogSub = sub->isDatalogSub;
    subSingleLabel->constraint = &sub->constraint;
    subSingleLabel->subYangInfo = &sub->subYangInfo->subYangInfo;
}

static void UserCbDfx(
    CltSubscriptionT *sub, uint64_t costTime, GmcStmtT *stmt, const GmcSubMsgInfoT *info, const DbSubsEventAckT *ack)
{
    if (info->eventType != GMC_SUB_EVENT_INITIAL_LOAD_EOF && info->eventType != GMC_SUB_EVENT_TRIGGER_SCAN_END) {
        SubPushSingleLabel *subSingleLabel = CltGetOperationContext(stmt);
        sub->recvMsgCnt += subSingleLabel->recvMsgCnt;
        sub->matchMsgCnt += subSingleLabel->matchMsgCnt;
        sub->isSendHeartBeat = true;
    }

    // 计算单个回调耗时用于视图呈现
    CheckSingleSubCallbackExecTimeout(costTime, stmt->conn, info, sub, ack->rows);
    // 计算订阅关系级别的平均回调耗时用于打日志
    CheckAvgSubCallbackExecTimeout(costTime, stmt->conn, info, sub);
}

static void CltSubParseBody(GmcStmtT *stmt, const DbSubsEventAckT *ack, FixBufferT *msgData, const GmcSubMsgInfoT *info)
{
    CltSubscriptionT *sub = NULL;

    CltCataKeyT cataKey;
    CltSetCataKey(
        &cataKey, (uint16_t)stmt->conn->instanceId, (uint16_t)ack->dbId, ack->namespaceId, info->subscriptionName);
    Status ret = CltCataPrepareSub(&cataKey, false, &sub);
    if (sub == NULL) {
        // 订阅关系不存在，或者已经被删除
        // 这里打一行warn日志
        DB_LOG_WARN(GMERR_DATA_EXCEPTION, "sub not found, sub name: %s.", info->subscriptionName);
        stmt->conn->connDfxInfo.abnormalCbCnt++;
        return;
    } else if (ret != GMERR_OK) {
        // 内部返回非OK的场景已经打过日志了，不重复打印日志
        stmt->conn->connDfxInfo.abnormalCbCnt++;
        goto CLOSESUB;
    }
    bool isLockScan = false;
    if (SECUREC_UNLIKELY(sub->inInitLoadRound)) {
        DbRWSpinWLock(&sub->scanRwLock);
        isLockScan = true;
    }

    if (info->eventType == GMC_SUB_EVENT_TRIGGER_SCAN_BEGIN) {
        // do nothing, invoke callback only
        goto CALLBACK;
    }

    if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF || info->eventType == GMC_SUB_EVENT_TRIGGER_SCAN_END) {
        // clear version map and then invoke callback
        CltSubVersionMapDestroy(sub);
        goto CALLBACK;
    }

    // 初始化stmt的内容，以便用户在回调中通过GmcFetch读取数据
    SubPushSingleLabel *subSingleLabel = CltGetOperationContext(stmt);
    (void)memset_s(subSingleLabel, sizeof(SubPushSingleLabel), 0, sizeof(SubPushSingleLabel));
    InitsubSingleLabel(subSingleLabel, sub, msgData, ack, info);
    stmt->stmtType = sub->type;

CALLBACK:
    // 部分事件没有实际数据，不用初始化stmt，
    // 只需要直接执行回调通知用户有这个事件出现即可
    if (SECUREC_UNLIKELY(sub->userData == NULL)) {
        stmt->conn->connDfxInfo.emptyUserDataCnt++;
    }

    stmt->dtlErrorCode = 0;
    uint64_t startTime = DbRdtsc();
    sub->userCb(stmt, info, sub->userData);
    uint64_t costTime = DbToMseconds(DbRdtsc() - startTime);
    UserCbDfx(sub, costTime, stmt, info, ack);

    // 这里后续可以改为CltResetStmt，但目前Reset的流程太重
    // 因为只需要清除树模型句柄等临时内容，先用这两行代替
    DbMemCtxReset(stmt->opCtx);
    stmt->stmtType = CLT_STMT_TYPE_IDLE;
    if (SECUREC_UNLIKELY(isLockScan)) {
        DbRWSpinWUnlock(&sub->scanRwLock);
    }
CLOSESUB:
    // 关闭已打开的订阅关系
    CltCataCloseSub(sub);
}

static void CltSubHandleNormalMsg(GmcStmtT *stmt, const DbSubsEventAckT *ack, FixBufferT *msg, GmcSubMsgInfoT *info)
{
    // 非共享内存消息体模式，认为header->subsNum固定为1，只解析一次订阅关系
    Status ret = CltSubParseHeader(msg, info);
    if (ret != GMERR_OK) {
        return;
    }

    // 消息体紧跟在刚才解析的内容之后
    FixBufferT *msgData = msg;
    CltSubParseBody(stmt, ack, msgData, info);
}

static void CltSubHandleSharedMsg(GmcStmtT *stmt, const DbSubsEventAckT *ack, FixBufferT *msg, GmcSubMsgInfoT *info)
{
    if (SECUREC_UNLIKELY(ack->subsDataSize < SHARE_MSG_HEADER_ALIGN_SIZE)) {
        DB_LOG_ERROR(
            GMERR_DATA_EXCEPTION, "shared msg size is unsound, ack subsDataSize: %" PRIu32 ".", ack->subsDataSize);
        return;
    }

    // 共享内存消息体模式，实际推送的内容存储在header->subsData，需要attach
    FixBufferT msgData = {0};
#ifdef SECUREFIXBUF
    DbDataT ctx;
    ctx.ptr = &stmt->conn->entryCtx;
    SecureFixBufSetVerifyProc(&msgData, CltFixBufVerify, ctx);
#endif
    Status ret = SecureFixBufAttachShm(&msgData, ack->subsData, ack->subsDataSize);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "attach shared msg content");
        return;
    }

    for (uint32_t countRemain = ack->subsNum; countRemain > 0; --countRemain) {
        // 根据subsNum执行多次解析操作，如果出错需要break，防止后续读操作错位
        ret = CltSubParseHeader(msg, info);
        if (ret != GMERR_OK) {
            break;
        }
        // 使用共享内存消息体继续执行解析
        // subsDataSize已经检查过，这里可以直接FixBufSeek
        FixBufSeek(&msgData, SHARE_MSG_HEADER_ALIGN_SIZE);
        CltSubParseBody(stmt, ack, &msgData, info);
    }

    // subsData引用计数不为0时，直接返回
    uint32_t ref = SecureFixBufDecRefBuf(&msgData);
    if (ref > 0) {
        return;
    }

    // 否则调用DRT接口将subsData添加到release list，后续由服务端负责释放
    const GmcConnT *conn = stmt->conn;
    const void *buf = SecureFixBufGetBuf(&msgData);
#ifdef SECUREFIXBUF
    if (SECUREC_UNLIKELY(buf == NULL)) {
        return;
    }
#endif
    const ShareMsgHeaderT *sharedMsgHeader = buf;
    (void)DbShareMsgReleaseListAddNode(conn->subMsgMgr, sharedMsgHeader->shareMsgNodeId);
}

static void CltSubHandleMsg(GmcConnT *conn, GmcStmtT *stmt, FixBufferT *msg)
{
    const DbSubsEventAckT *ack = SecureFixBufGetData(msg, sizeof(DbSubsEventAckT));
    if (SECUREC_UNLIKELY(ack == NULL)) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Got unsound ack");
        return;
    }

    // 如果满足条件，通知用户服务器推送了消息丢失通知
    if (SECUREC_UNLIKELY(ack->subsMsgType == SUB_FAILED_MSG_TYPE)) {
        if (conn->subFailedCb != NULL) {
            conn->subFailedCb(stmt, conn->subFailedData);
        }
        return;
    }

    // 更新订阅消息接收统计data
    CltConnDfxInfoT *stat = &conn->connDfxInfo;
    uint32_t eventType = ack->eventType;
    if (SECUREC_UNLIKELY(eventType >= ELEMENT_COUNT(stat->subEventCount))) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Got unsound event type: %" PRIu32 ".", eventType);
        return;
    }
    stat->subEventCount[eventType]++;
    stat->cliRecvCnt++;

    // 在这里提前准备好回调所需的部分数据，避免每次赋值
    // msgType, subscriptionName为特定订阅相关的内容，后续再填写
    GmcSubMsgInfoT info = {};
    info.labelCount = 1;
    info.eventType = (GmcSubEventTypeE)eventType;
    info.serialNumber = ack->serialNumber;
    info.connectionName = conn->connName;

    if (SubsDataGetTxWayShare(ack->subsDataFlag)) {
        CltSubHandleSharedMsg(stmt, ack, msg, &info);
    } else {
        CltSubHandleNormalMsg(stmt, ack, msg, &info);
    }
}

static Status CltSubHandleMultiMsg(
    GmcConnT *conn, GmcStmtT *stmt, const OpHeaderT *op, const MsgHeaderT *msg, FixBufferT *buffer)
{
    const OpHeaderT *tmpOp = op;
    for (uint32_t count = msg->opNum;;) {
        uint32_t size = tmpOp->len - MSG_OP_HEADER_ALIGN_SIZE;
        void *data = SecureFixBufGetData(buffer, size);
        if (data == NULL) {
            return GMERR_DATA_EXCEPTION;
        }
        FixBufferT splited = {};
        FixBufInit(&splited, data, size, size, 0, NULL);
        DbDataT ctx;
        ctx.ptr = &conn->entryCtx;
        SecureFixBufSetVerifyProc(&splited, CltFixBufVerify, ctx);
        CltSubHandleMsg(conn, stmt, &splited);
        if (--count == 0) {
            return GMERR_OK;
        }
        tmpOp = SecureFixBufGetData(buffer, MSG_OP_HEADER_ALIGN_SIZE);
        if (tmpOp == NULL) {
            return GMERR_DATA_EXCEPTION;
        }
    }
    return GMERR_OK;
}

void CltSubRecvAndHandleMultiMsg(GmcConnT *conn, SubEvtCallBackArgs *cbArgs, PubsubCommDfxInfoT *dfxInfo)
{
    Status ret = CltAttachAndRefreshConn(conn, NULL, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "refresh conn.");
        return;
    }
    FixBufferT *buffer = &conn->recvPack;
    dfxInfo->pipeRecvStartTime = DbGlobalRdtsc();
    // 使用socket通信（欧拉环境，或者独立部署rtosv2环境），订阅连接需要阻塞式接收，因为服务端会先塞入订阅共享内存发送队列DbSendBufRingT
    // 如果服务端出现通信发送阻塞（即底层socket通信队列满），会先塞入到共享内存发送队列，发送失败后移出
    // 此时非阻塞接收的话，就会导致没完整收到报文，然后走到共享内存发送队列，就会导致非预期访问。
    // 设备环境由于采用channel通信，可以保证底层原子通信，报文会完整发送，故不存在上述问题，发送部分报文就触发客户端的可读事件
    ret = CltPipeRecv(conn, buffer, conn->lctrType == LCTR_TYPE_HPE_CHANNEL);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "receive pushed msg, conn: %s", conn->connName);
        goto FINISH;
    }

    // 检验报文的有效性
    if (SECUREC_UNLIKELY((ret = CltMsgIsValidWithLog(buffer, conn)) != GMERR_OK)) {
        goto FINISH;
    }
    const MsgHeaderT *msg = RpcPeekMsgHeader(buffer);
    CltMonitorCSCommSingleTimeForPubsub(msg, conn, dfxInfo);
    const OpHeaderT *op = ProtocolPeekFirstOpHeader(buffer);
    if (op->opCode == MSG_OP_RPC_HEARTBEAT) {
        conn->heartbeatInfo.recvCnt++;
        goto FINISH;  // skip heartbeat response from server
    }
    if (cbArgs->stmt == NULL) {
        ret = AllocStmt(conn, &(cbArgs->stmt));
        if (ret != GMERR_OK) {
            cbArgs->stmt = NULL;
            goto FINISH;
        }
    }
    GmcStmtT *stmt = cbArgs->stmt;
    // 先消费，后释放内存，避免重复释放
    if (conn->lctrType != LCTR_TYPE_TCP) {
        DbShmEntrySndBufRingFrontDequeue(&conn->entryCtx);
    }
    ret = CltSubHandleMultiMsg(conn, stmt, op, msg, buffer);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "split pushed msg");
    }
FINISH:
    CltDetachAndReleaseConn(conn, NULL, true);
    return;
}

void SubPushCb(int32_t fd, void *arg)
{
    SubEvtCallBackArgs *cbArgs = (SubEvtCallBackArgs *)arg;
    GmcConnT *conn = cbArgs->conn;
    ++conn->connDfxInfo.epollWakeCnt;
    PubsubCommDfxInfoT dfxInfo = {.subPushCbStartTime = DbGlobalRdtsc()};

    if (DbIsEulerEnv()) {
        dfxInfo.handledMsgNum++;
        CltSubRecvAndHandleMultiMsg(conn, cbArgs, &dfxInfo);
        return;
    }
#if !DB_ENV_EULER
    uint64_t count;
    // 临时处理方案, 主方案需要推动业务修改成EPOLLET(边缘触发模式)
    DbAdptReadForCb(fd, &count, sizeof(uint64_t), NULL);

    uint32_t msgNum = 0;
    (void)DbPipeUnRecvMsgSize(&conn->pipe, &msgNum);

    while (msgNum > 0) {
        --msgNum;
        dfxInfo.handledMsgNum++;
        CltSubRecvAndHandleMultiMsg(conn, cbArgs, &dfxInfo);
    }
#endif
}

typedef struct {
    StatusMergeListT *statusMergeList;
    ShmemPtrT cursor;
    TupleAddr *tuple;
} GetSubTupleArgs;

Status GetSubTupleShmProcess(void *args)
{
    GetSubTupleArgs *getSubTupleArgs = (GetSubTupleArgs *)args;
    return DbCltReadOne(getSubTupleArgs->statusMergeList, getSubTupleArgs->cursor, getSubTupleArgs->tuple,
        &g_gmdbCltProtectThreadCtx.isAllowManipuShm);
}

#ifdef FEATURE_STMG_SUBS
static void CltGetStMgDataDeleteMark(
    const TupleBufT *tupleBuf, const DmVertexLabelT *vertexLabel, DmVertexDescT *vertexDesc, uint8_t *deleteMark)
{
    DB_POINTER3(tupleBuf, deleteMark, vertexLabel);
    DmValueT value;
    value.type = DB_DATATYPE_UINT8;
    DmVertexBufGetSysPrope(STATUS_MERGE_DELETE_MARK, tupleBuf->buf, &value, vertexDesc);
    *deleteMark = value.value.ubyteValue;
}

inline static void CltIsVertexBufAged(DmVertexLabelT *vertexLabel, const TupleBufT *tupleBuf, bool *isAged)
{
    uint8_t deleteMark;
    CltGetStMgDataDeleteMark(tupleBuf, vertexLabel, vertexLabel->vertexDesc, &deleteMark);
    if (deleteMark == (uint8_t)STMG_DATA_AGED) {
        *isAged = true;
    };
}

static inline void CltSetEventType4MarkDeleteData(GmcSubMsgInfoT *info, TupleBufT *tuple, CltCataLabelT *cataLabel)
{
    bool isAged = false;
    CltIsVertexBufAged(cataLabel->vertexLabel, tuple, &isAged);
    info->eventType = isAged ? GMC_SUB_EVENT_AGED : GMC_SUB_EVENT_DELETE;
}

void CltSetSubMsgInfo(GmcSubMsgInfoT *info, CltSubscriptionT *sub, TupleBufT *tuple)
{
    DmValueT deleteMark;
    deleteMark.type = DB_DATATYPE_UINT8;
    CltCataLabelT *cltCataLabel = sub->cltCataLabel;

    DmVertexBufGetSysPrope(STATUS_MERGE_DELETE_MARK, tuple->buf, &deleteMark, cltCataLabel->vertexLabel->vertexDesc);
    if (SECUREC_LIKELY(sub->inInitLoadRound == false)) {
        // 只有modify和delete两种eventType:
        // 新增了一个系统字段，判断是否delete就完事，系统字段名：STATE_MERGE_DELETE_MARK
        if (deleteMark.value.ucharValue == 0) {
            info->eventType = GMC_SUB_EVENT_MODIFY;
            info->msgType = GMC_SUB_MSG_NEW_DATA;
        } else {
            CltSetEventType4MarkDeleteData(info, tuple, cltCataLabel);
            info->msgType = GMC_SUB_MSG_OLD_DATA;
        }
    } else {
        if (deleteMark.value.ucharValue != 0) {
            CltSetEventType4MarkDeleteData(info, tuple, cltCataLabel);
            info->msgType = GMC_SUB_MSG_OLD_DATA;
        } else {
            info->eventType = GMC_SUB_EVENT_INITIAL_LOAD;
            info->msgType = GMC_SUB_MSG_NEW_DATA;
        }
    }
}

inline static void StmgCallBack(GmcConnT *conn, GmcStmtT *stmt, CltSubscriptionT *sub, GmcSubMsgInfoT *info)
{
    // 部分事件没有实际数据，不用初始化stmt，
    // 只需要直接执行回调通知用户有这个事件出现即可
    if (SECUREC_UNLIKELY(sub->userData == NULL)) {
        stmt->conn->connDfxInfo.emptyUserDataCnt++;
    }
    uint64_t startTime = DbRdtsc();
    sub->userCb(stmt, info, sub->userData);
    uint64_t endTime = DbRdtsc();
    uint64_t costTime = DbToMseconds(endTime - startTime);
    // 计算单个回调耗时用于视图呈现
    CheckSingleSubCallbackExecTimeout(costTime, stmt->conn, info, sub, 1);
    // 计算订阅关系级别的平均回调耗时用于打日志
    CheckAvgSubCallbackExecTimeout(costTime, stmt->conn, info, sub);

    DbMemCtxReset(stmt->opCtx);
}

static void CltDlrSubCtxInit(TupleBufT *tuple, ShmemPtrT cursor, DlrSubCtxT *dlrSubCtx)
{
    dlrSubCtx->isFirstFetch = true;
    dlrSubCtx->isFinish = false;
    dlrSubCtx->heapTupleBuf = tuple;
    dlrSubCtx->cursor = cursor;
    dlrSubCtx->lastOperation = 0;
}

static void SubPushCallbackWithEventType(GmcConnT *conn, GmcStmtT *stmt, CltSubscriptionT *sub, GmcSubMsgInfoT *info)
{
    if (info->eventType == GMC_SUB_EVENT_DELETE) {
        if ((sub->stMgSubsEventBitMap & EVENT_DELETE) != 0) {
            StmgCallBack(conn, stmt, sub, info);
            sub->stmgDeleteCnt++;
        }
    } else if (info->eventType == GMC_SUB_EVENT_MODIFY) {
        if ((sub->stMgSubsEventBitMap & EVENT_MODIFY) != 0) {
            StmgCallBack(conn, stmt, sub, info);
            sub->stmgModifyCnt++;
        }
    } else if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD) {
        if ((sub->stMgSubsEventBitMap & EVENT_INITIAL_LOAD) != 0) {
            StmgCallBack(conn, stmt, sub, info);
            sub->stmgInitLoadCnt++;
        }
    } else if (info->eventType == GMC_SUB_EVENT_AGED) {
        if ((sub->stMgSubsEventBitMap & EVENT_DELETE) != 0 && (sub->stMgSubsEventBitMap & EVENT_AGED) == 0) {
            info->eventType = GMC_SUB_EVENT_DELETE;
            StmgCallBack(conn, stmt, sub, info);
            sub->stmgDeleteCnt++;
        } else if ((sub->stMgSubsEventBitMap & EVENT_AGED) != 0) {
            StmgCallBack(conn, stmt, sub, info);
            sub->stmgAgeCnt++;
        }
    }
}

// status merge 订阅获取订阅推送数据并触发用户回调
void CltGetStmgSubData(GmcConnT *conn, GmcStmtT *stmt, TupleBufT *tuple, CltSubscriptionT *sub)
{
    /* 区别于老订阅：sub->vertexLabel在GmcSubscribe时已从服务端获取，sub->vertex的空间在GmcSubscribe时创建 */
    // 1 初始化info,
    GmcSubMsgInfoT info;
    info.labelCount = 1;
    info.serialNumber = 0;
    info.connectionName = conn->connName;
    info.subscriptionName = sub->name;
    CltSetSubMsgInfo(&info, sub, tuple);

    // 2 订阅条件过滤，带条件则反序列化并判断是否满足contraint
    bool isMatch = true;
    bool tempDeseri = false;
    if (sub->constraint.conditionNum != 0) {
        Status ret = DmDeSerialize2ExistsVertex(tuple->buf, tuple->len, sub->vertex, false);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "deserialize to vertex.");
            return;
        }
        tempDeseri = true;

        isMatch = false;
        ret = CltCompareStmgConstraint(&sub->constraint, sub->vertex, info.eventType, &isMatch);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "compare constraint.");
            return;
        }
    }
    ++sub->recvMsgCnt;
    sub->isSendHeartBeat = true;

    if (isMatch) {
        SubPushSingleLabel *subSingleLabel = CltGetOperationContext(stmt);
        (void)memset_s(subSingleLabel, sizeof(SubPushSingleLabel), 0, sizeof(SubPushSingleLabel));
        subSingleLabel->base = sub->base;
        subSingleLabel->requiredType = info.msgType;
        subSingleLabel->isStatusMerge = true;
        subSingleLabel->rowsRemain = 1;  // status merge一次只能推送一行数据
        subSingleLabel->cltCataLabel = sub->cltCataLabel;
        subSingleLabel->constraint = &sub->constraint;
        subSingleLabel->isDeSerialized = tempDeseri;
        CltDlrSubCtxInit(tuple, sub->cursor, &subSingleLabel->dlrSubCtx);
        stmt->stmtType = sub->type;
        subSingleLabel->newData = (TextT){.len = tuple->len, .str = (char *)tuple->buf};
        SubPushCallbackWithEventType(conn, stmt, sub, &info);
        stmt->stmtType = CLT_STMT_TYPE_IDLE;
        ++sub->matchMsgCnt;
    }
    return;
}

void CltInitialLoadEofSub(CltSubscriptionT *sub, GmcStmtT *stmt, GmcConnT *conn)
{
    GmcSubMsgInfoT info = {0};
    info.labelCount = 1;
    info.serialNumber = 0;
    info.connectionName = conn->connName;
    info.subscriptionName = sub->name;
    info.eventType = GMC_SUB_EVENT_INITIAL_LOAD_EOF;
    info.msgType = GMC_SUB_MSG_NEW_DATA;
    SubPushSingleLabel *subSingleLabel = CltGetOperationContext(stmt);
    (void)memset_s(subSingleLabel, sizeof(SubPushSingleLabel), 0, sizeof(SubPushSingleLabel));
    subSingleLabel->base = sub->base;
    subSingleLabel->requiredType = info.msgType;
    subSingleLabel->isStatusMerge = true;
    subSingleLabel->cltCataLabel = sub->cltCataLabel;
    sub->userCb(stmt, &info, sub->userData);
    return;
}
#endif

Status SubFetchTupleBufferAddr(DrRunCtxT *drRunCtx, TupleAddr tuple, TupleBufT *tupleBuf)
{
    Status ret;
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (drRunCtxBase->isUseClusteredHashTable) {
        ret = ChLabelFetchTupleBuffer(drRunCtxBase->chRunCtx, tuple, tupleBuf);
        if (ret != GMERR_OK) {
            // internal error happened when we can not fetch tuple, should exit current table and continue next table
            DB_LOG_ERROR(ret, "cluster hash label fetch tuple buffer");
        }
    } else {
        ret = HeapFetchHpTupleBuffer(drRunCtxBase->heapRunCtx, tuple, tupleBuf);
        if (ret != GMERR_OK) {
            // internal error happened when we can not fetch tuple, should exit current table and continue next table
            DB_LOG_ERROR(ret, "heap label fetch tuple buffer");
        }
    }
    return ret;
}

Status SelfAwakeNotifyFd(GmcConnT *subConn)
{
#if defined(RTOSV2X) || defined(RTOSV2) || (defined(HPE) && !defined(HPE_SIMULATION))
    return DbAdptEventWakeSelf(subConn->selfEventWakeUp, 1);
#else
    return DbNotifyFdWrite(subConn->notifyChan.nfd);
#endif
}

#ifdef FEATURE_STMG_SUBS
Status SubVertexMainStoreCtxOpen(DrRunCtxT *drRunCtx, DmVertexLabelT *vertexLabel, CltSubscriptionT *sub)
{
    drRunCtx->dsFetchArgs.vertexScanArgs.vertex = sub->vertex;
    drRunCtx->dsFetchArgs.vertexScanArgs.cltCataLabel = sub->cltCataLabel;
    drRunCtx->base.isUseClusteredHashTable = vertexLabel->metaVertexLabel->containerType == CONTAINER_CLUSTERED_HASH;
    drRunCtx->base.containerType = vertexLabel->metaVertexLabel->containerType;
#ifdef ART_CONTAINER
    drRunCtx->base.isRealCluster = vertexLabel->commonInfo->isArtRealCluster;
#endif
    Status ret;
    // 暂时没加表锁，前提是1.订阅存在时，表一定没被删除；2.list内部能维护同一行数据的读写并发
    if (vertexLabel->metaVertexLabel->containerType == CONTAINER_HEAP) {
        ret = MainStoreHeapLabelOpen(drRunCtx, VERTEX_LABEL);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        ret = MainStoreVertexHashClusterOpen(drRunCtx);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

void SubSelfAwake(GmcConnT *conn, CltSubscriptionT *sub, bool needWakeUp)
{
    DbNotifyDataT data = (DbNotifyDataT){.data = sub->subsMetaId};
    NotifyChanWriteArgsT writeArgs = {
        .msgQueue = conn->notifyChan.msgQue,
        .data = &data,
        .priority = sub->priority,
    };
    Status ret = CltProtectShmProcess(NotifyChanWriteShmProcess, (void *)&writeArgs, SHMEM_OP_STMG_SUB);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Channel write notify data");
    }
    if (needWakeUp) {
        ret = SelfAwakeNotifyFd(conn);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Channel awake self %d", conn->selfEventWakeUp);
        }
    }
    return;
}

inline static bool IsBatchFetchTimeout(uint64_t startTime)
{
    uint64_t endTime = DbRdtsc();
    uint64_t costTime = DbToMseconds(endTime - startTime);
    return costTime >= (uint64_t)SUB_FETCH_TIME_MAX;
}

static void HandleGetSubTupleNoData(
    GmcConnT *conn, GmcStmtT *stmt, CltSubscriptionT *sub, DrRunCtxT *drRunCtx, Status ret)
{
    if (ret == GMERR_NO_DATA) {
        if (sub->inInitLoadRound == true) {
            sub->inInitLoadRound = false;
            if ((sub->stMgSubsEventBitMap & EVENT_INITIAL_LOAD_EOF) != 0) {
                CltInitialLoadEofSub(sub, stmt, conn);
            }
        }
        DirectReadCtxClose(drRunCtx, DIRECT_ACCESS_CLOSE_HEAP_CURSOR_CTX);
        return;
    }
    DirectReadCtxClose(drRunCtx, DIRECT_ACCESS_CLOSE_HEAP_CURSOR_CTX);
    DB_LOG_ERROR(ret, "clt get tuple addr.");
}

static StatusMergeListT *GetStatusMergeList(DmVertexLabelT *label)
{
    VertexLabelCommonInfoT *commonInfo = label->commonInfo;
    if (SECUREC_UNLIKELY(commonInfo == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "get sub tuple addr because commonInfo is null.");
        return NULL;
    }
    StatusMergeListT *stmgList = DbShmPtrToAddr(commonInfo->statusMergeList);
    if (SECUREC_UNLIKELY(stmgList == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "convert Address To Arm 32 Address Addr SegId %" PRIu32 " offset %" PRIu32 ".",
            commonInfo->statusMergeList.segId, commonInfo->statusMergeList.offset);
        return NULL;
    }
    return stmgList;
}

ALWAYS_INLINE Status CltGetSubTupleCb(
    DrRunCtxT *drRunCtx, TupleAddr tuple, GmcConnT *conn, GmcStmtT *stmt, CltSubscriptionT *sub)
{
    DirectAccessRunCtxT *drBase = &drRunCtx->base;
    Status ret = DsAcqLatchForDirectRead(drBase->labelLatch, drBase->labelLatchVersionId, drBase->sessionCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // 3 从tupleAddr获取heapTupleBuf
    // tuple->buf的内存已经拷贝,这里拿到的heapTupleBuf是一行数据
    // drRunCtx>base.heapRunCtx这个建连时初始化
    ret = SubFetchTupleBufferAddr(drRunCtx, tuple, &drRunCtx->tupleBuf);
    DsReleaseLatchForDirectRead(drBase->labelLatch, drBase->sessionCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // 4 处理数据并调用户回调
    CltGetStmgSubData(conn, stmt, &drRunCtx->tupleBuf, sub);
    return GMERR_OK;
}

void DirectReadPubSubData(
    GmcConnT *conn, GmcStmtT *stmt, CltSubscriptionT *sub, DrRunCtxT *drRunCtx, DbNotifyDataT *data)
{
    DmVertexLabelT *label = sub->cltCataLabel->vertexLabel;
    StatusMergeListT *stmgList = GetStatusMergeList(label);
    StatusMergeNodeBaseT *readNode = GetSubsNodeAddr(sub->cursor);
    if (SECUREC_UNLIKELY(stmgList == NULL || readNode == NULL)) {
        return;
    }
    Status ret = DirectReadInitLabelLatch(&drRunCtx->base, label);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // 内部已经记了错误码
        return;
    }

    TupleAddr tuple = 0;
    uint32_t count = 0;
    uint64_t startTime = DbRdtsc();
    bool isTimeout = false;
    while (count++ < SUB_FETCH_COUNT_MAX && !conn->notifyChan.ctrlInfo->isSuspend && !isTimeout) {
        ret = SubVertexMainStoreCtxOpen(drRunCtx, label, sub);
        if (SECUREC_UNLIKELY(ret != GMERR_OK && ret != GMERR_NO_DATA)) {
            return;
        }
        if (ret != GMERR_NO_DATA) {
            GetSubTupleArgs getSubTupleArgs = {.statusMergeList = stmgList, .cursor = sub->cursor, .tuple = &tuple};
            ret = CltProtectShmProcess(GetSubTupleShmProcess, (void *)&getSubTupleArgs, SHMEM_OP_STMG_SUB);
        }
        // 取数据,如果count已经到了但是还没有fetch完需要调用DbNotifyChannelSend把data重新写回去
        // initial_load时要发送最后一条消息, mainstore open以及getSubTupleAddr都会返回no_data
        if (ret != GMERR_OK) {
            HandleGetSubTupleNoData(conn, stmt, sub, drRunCtx, ret);
            break;
        }
        ret = CltGetSubTupleCb(drRunCtx, tuple, conn, stmt, sub);
        SeCursorNodeSetConsuming(readNode, false);  // 不加锁，不要求严格并发
        DirectReadCtxClose(drRunCtx, DIRECT_ACCESS_CLOSE_HEAP_CURSOR_CTX);
        TupleBufReleaseBigBuf(&drRunCtx->tupleBuf, MAX_CLT_HOLD_HEAP_TUPLEBUF_SIZE);
        if (SECUREC_LIKELY(ret != GMERR_OK)) {
            return;
        }
        // 5 判断是否超时
        isTimeout = IsBatchFetchTimeout(startTime);
    }

    if (ret != GMERR_NO_DATA && (count >= SUB_FETCH_COUNT_MAX || isTimeout || conn->notifyChan.ctrlInfo->isSuspend)) {
        // 剩余的数据在下一个自激励周期中取出
        SubSelfAwake(conn, sub, !conn->notifyChan.ctrlInfo->isSuspend);
    }
}
#endif

static inline bool WaitUntilSubNotUsed(CltSubscriptionT *sub)
{
    uint32_t tryTimes = SUB_TRY_TIMES;
    while (tryTimes != 0) {
        if (DbAtomicGet(&sub->state) == 1) {
            DbUsleep(USECONDS_IN_MSECOND);  // 1ms
            tryTimes--;
        } else {
            break;
        }
    }
    return tryTimes > 0;
}

void CltClearSubsOnSubConn(GmcConnT *subConn)
{
    DB_POINTER(subConn);
    if (subConn->connType != GMC_CONN_TYPE_SUB) {
        return;
    }
    DbOamapT *subscriptionMap = &g_gmdbCltInstance.subscriptionMap;
    DbOamapIteratorT iter = 0;
    void *key;
    CltSubscriptionT *sub;
    DbRWSpinWLock(&subscriptionMap->rwLock);
    while (DbOamapFetch(subscriptionMap, &iter, &key, (void **)&sub) == GMERR_OK) {
        if (!sub) {
            continue;
        }
        if (sub->cataKey.instanceId == subConn->instanceId && sub->subChannelId == subConn->remoteId) {
            uint32_t hash = CltCataKeyToHash32(&sub->cataKey);
#ifdef FEATURE_STMG_SUBS
            if (sub->cltSubsType == STATUS_MERGE) {
                if (!WaitUntilSubNotUsed(sub) || (CltSubMarkUnusedCursor(sub) != GMERR_OK)) {
                    continue;
                }
            }
#endif
            (void)DbOamapRemove(subscriptionMap, hash, &sub->cataKey);
#ifdef FEATURE_STMG_SUBS
            if (sub->cltSubsType == STATUS_MERGE) {
                CltSubNameMapRemove(sub);
            }
#endif
            uint32_t oldCount = DbAtomicFetchOr(&sub->state, LABEL_NOT_IN_CACHE);
            CltSubDestroy(oldCount == 0, sub);
        }
    }
    DbRWSpinWUnlock(&subscriptionMap->rwLock);
}

void CltGetDlrTupleSubEvent(
    DrRunCtxT *drRunCtx, DmVertexLabelT *vertexLabel, TupleBufT *heapTupleBuf, GmcSubEventTypeE *subsEvent)
{
    DmValueT deleteMark;
    deleteMark.type = DB_DATATYPE_UINT8;
    DmVertexBufGetSysPrope(STATUS_MERGE_DELETE_MARK, heapTupleBuf->buf, &deleteMark, vertexLabel->vertexDesc);
    *subsEvent = (deleteMark.value.ucharValue != 0) ? GMC_SUB_EVENT_DELETE : GMC_SUB_EVENT_MODIFY;
}

Status CltDlrTupleIsMatchConstraint(
    SubPushSingleLabel *subSingleLabel, TupleBufT *heapTupleBuf, GmcSubEventTypeE subsEvent, bool *isMatch)
{
    Status ret = DmDeSerialize2ExistsVertex(heapTupleBuf->buf, heapTupleBuf->len, subSingleLabel->vertex, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "dlr deserialize to vertex.");
        return ret;
    }

    ret = CltCompareStmgConstraint(subSingleLabel->constraint, subSingleLabel->vertex, subsEvent, isMatch);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "dlr compare constraint.");
        return ret;
    }
    return GMERR_OK;
}

Status CltGetDlrNextTuple(GmcStmtT *stmt, uint32_t *op)
{
    DB_POINTER2(stmt, op);
    TupleAddr tuple = 0;
    SubPushSingleLabel *subSingleLabel = CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = subSingleLabel->cltCataLabel->vertexLabel;
    DrRunCtxT *drRunCtx = &stmt->drRunCtx;
    bool isMatch = false;
    Status ret = GMERR_OK;
    StatusMergeListT *stmgList = DbShmPtrToAddr(vertexLabel->commonInfo->statusMergeList);
    StatusMergeNodeBaseT *readNode = GetSubsNodeAddr(subSingleLabel->dlrSubCtx.cursor);
    if (stmgList == NULL || readNode == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    while (!isMatch) {
        GetSubTupleArgs getSubTupleArgs = {
            .statusMergeList = stmgList, .cursor = subSingleLabel->dlrSubCtx.cursor, .tuple = &tuple};
        ret = CltProtectShmProcess(GetSubTupleShmProcess, (void *)&getSubTupleArgs, SHMEM_OP_STMG_SUB);
        if (ret != GMERR_OK) {
            break;
        }

        ret = SubFetchTupleBufferAddr(drRunCtx, tuple, subSingleLabel->dlrSubCtx.heapTupleBuf);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "dlr get tuple buf.");
            break;
        }
        GmcSubEventTypeE subsEvent;
        CltGetDlrTupleSubEvent(drRunCtx, vertexLabel, subSingleLabel->dlrSubCtx.heapTupleBuf, &subsEvent);
        if (DmVertexLabelIsDatalogLabel(vertexLabel)) {
            *op = (subsEvent == GMC_SUB_EVENT_DELETE) ? MSG_OP_RPC_DELETE_VERTEX : MSG_OP_RPC_INSERT_VERTEX;
        } else {
            *op = (subsEvent == GMC_SUB_EVENT_DELETE) ? MSG_OP_RPC_DELETE_VERTEX : MSG_OP_RPC_REPLACE_VERTEX;
        }

        if (subSingleLabel->constraint->conditionNum == 0) {
            break;
        }
        ret = CltDlrTupleIsMatchConstraint(subSingleLabel, subSingleLabel->dlrSubCtx.heapTupleBuf, subsEvent, &isMatch);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "dlr compare constraint.");
            return ret;
        }
    }
    SeCursorNodeSetConsuming(readNode, false);  // 不加锁，不要求严格并发

    return ret;
}

#ifdef FEATURE_STMG_SUBS
static Status SubPushStatusMergeReadNotifyMsg(
    int32_t fd, GmcConnT *conn, DbNotifyMsgBufferT **notifyMsgBuffer, bool *isResume)
{
    Status ret = GMERR_OK;
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    if (fd != ioCtx->getRelFd(conn->selfEventWakeUp)) {
#endif
        (void)DbNotifyFdRead(fd);
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    } else {
        eventfd_t value;
        (void)DbAdptEventFdRead(conn->selfEventWakeUp, &value);
    }
#endif
    if (conn->notifyChan.ctrlInfo->isSuspend) {
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    if (conn->notifyChan.cacheMsgBuf != NULL) {
        *notifyMsgBuffer = conn->notifyChan.cacheMsgBuf;
        *isResume = true;
        conn->notifyChan.cacheMsgBuf = NULL;  // 所有权转移到notifyMsgBuffer
        return GMERR_OK;
    }
    // 这里通过NotifyChanReadShmProcess接口从notifyChannel中将metaID按照优先级顺序放到notifyMsgBuffer.data的数组里。
    NotifyChanReadArgsT readArgs = (NotifyChanReadArgsT){
        .memCtx = conn->memCtx,
        .msgQueue = conn->notifyChan.msgQue,
        .notifyMsgBuf = notifyMsgBuffer,
    };
    if ((ret = CltProtectShmProcess(NotifyChanReadShmProcess, &readArgs, SHMEM_OP_STMG_SUB)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "notify chan recv open");
        return ret;
    }

    return GMERR_OK;
}

void SubPushStatusMergeCb(int32_t fd, void *arg)
{
    SubEvtCallBackArgs *cbArgs = (SubEvtCallBackArgs *)arg;
    GmcConnT *conn = cbArgs->conn;
    ++conn->connDfxInfo.epollWakeCnt;
    Status ret;

    // 1. 创建stmt
    if (cbArgs->stmt == NULL && AllocStmt(conn, &(cbArgs->stmt)) != GMERR_OK) {
        return;
    }
    GmcStmtT *stmt = cbArgs->stmt;

    // 2. 读notify消息
    DbNotifyMsgBufferT *notifyMsgBuffer = NULL;
    bool isResume = false;
    if (SubPushStatusMergeReadNotifyMsg(fd, conn, &notifyMsgBuffer, &isResume) != GMERR_OK) {
        return;
    }

    /*
    1.第一层for循环：首先会根据优先级取到订阅关系，把订阅关系取完
    2.第二层for循环：会根据每一个订阅关系取数据，取完或者达到count上限退出
    */
    // 取订阅关系
    DbNotifyDataT data;
    while (DbNotifyChannelFetch(notifyMsgBuffer, &data) == GMERR_OK) {
        // 1 读出metaId并找到对应CltSubscriptionT,data.data就是metaId
        uint32_t metaIdHash = StmgSubSetMetaHash(conn->instanceId, data.data);

        DbOamapT *cataKeyMap = &g_gmdbCltInstance.subCataKeyMap;
        CltCataKeyT *cataKey = DbOamapLookupWithLock(cataKeyMap, metaIdHash, &metaIdHash, NULL);
        if (SECUREC_UNLIKELY(cataKey == NULL)) {
            stmt->conn->connDfxInfo.abnormalCbCnt++;
            continue;
        }

        CltSubscriptionT *sub = NULL;
        ret = CltCataPrepareSub(cataKey, false, &sub);
        if (sub == NULL) {
            // 订阅关系不存在，或者已经被删除,这里不作为异常处理，不打日志
            stmt->conn->connDfxInfo.abnormalCbCnt++;
            continue;
        } else if (ret != GMERR_OK) {
            stmt->conn->connDfxInfo.abnormalCbCnt++;
            CltCataCloseSub(sub);
            continue;
        }
        DirectReadPubSubData(conn, stmt, sub, &stmt->drRunCtx, &data);
        CltCataCloseSub(sub);
        if (SECUREC_UNLIKELY(conn->notifyChan.ctrlInfo->isSuspend)) {
            conn->notifyChan.cacheMsgBuf = notifyMsgBuffer;
            notifyMsgBuffer = NULL;
            return;
        }
    }
    DbNotifyChannelRecvClose(notifyMsgBuffer);
    if (isResume && (ret = SelfAwakeNotifyFd(conn)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "self wakeup");
    }
}
#else
void SubPushStatusMergeCb(int32_t fd, void *arg)
{
    return;
}
#endif
