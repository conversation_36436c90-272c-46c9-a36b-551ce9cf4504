/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_namespace.c
 * Description: realization of interface of gmdb namespace
 * Create: 2021-10-23
 */

#include "gmc_namespace.h"
#include "clt_namespace.h"

#ifdef FEATURE_NAMESPACE_ENHANCE
Status GmcDropNamespace(GmcStmtT *stmt, const char *namespaceName)
{
    Status ret = GmcDropNamespaceCheck(stmt, namespaceName, false);
    if (ret != GMERR_OK) {
        return ret;
    }

    // namespace下有一个默认的kv表，在namespace被删除时，应该顺便将kv表的相关信息从客户端元数据缓存中移除
    GmcConnT *conn = stmt->conn;
#ifdef FEATURE_KV
    CltCataKeyT key;
    CltSetCataKey(&key, (uint16_t)conn->instanceId, (uint16_t)conn->dbId, conn->namespaceId, GLOBAL_KV_TABLE_NAME);
    CltCataRemoveKvTableByName(&key);
#endif

    uint32_t *nspId = &conn->namespaceId;  // 请求成功时，ParseResponse 会直接将结果解析并保存到 conn->namespaceId 上
    ret = CltRequestSync(conn, DropNamespaceBuildRequest, namespaceName, U32_RESULT_PARSE_RSP, nspId);
    if (ret != GMERR_OK) {
        return ret;
    }
    CltCataRemoveAllLabelsInNsp(*nspId);
    return ret;
}

Status GmcDropNamespaceAsync(GmcStmtT *stmt, const char *namespaceName, GmcDropNamespaceDoneT userCb, void *userData)
{
    Status ret = GmcDropNamespaceCheck(stmt, namespaceName, true);
    if (ret != GMERR_OK) {
        return ret;
    }

    // namespace下有一个默认的kv表，在namespace被删除时，应该顺便将kv表的相关信息从客户端元数据缓存中移除
    GmcConnT *conn = stmt->conn;
#ifdef FEATURE_KV
    CltCataKeyT key;
    CltSetCataKey(&key, (uint16_t)conn->instanceId, (uint16_t)conn->dbId, conn->namespaceId, GLOBAL_KV_TABLE_NAME);
    CltCataRemoveKvTableByName(&key);
#endif

    AsyncMsgContextT ctx =
        MakeAsyncMsgContext(U32_RESULT_PARSE_RSP, DropNamespaceCallback, userCb, userData, stmt->notDistribute);
    return CltRequestAsync(conn, DropNamespaceBuildRequest, namespaceName, &ctx);
}

Status GmcTruncateNamespaceAsync(
    GmcStmtT *stmt, const char *namespaceName, GmcTruncateNamespaceDoneT userCb, void *userData)
{
    Status ret = GmcDropNamespaceCheck(stmt, namespaceName, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    GmcConnT *conn = stmt->conn;
    AsyncMsgContextT ctx = MakeAsyncMsgContext(DummyParseRsp, CommonDDLCallback, userCb, userData, stmt->notDistribute);
    return CltRequestAsync(conn, TruncateNamespaceBuildRequest, namespaceName, &ctx);
}

Status GmcClearNamespace(GmcStmtT *stmt, const char *namespaceName)
{
    Status ret = GmcDropNamespaceCheck(stmt, namespaceName, false);
    if (ret != GMERR_OK) {
        return ret;
    }
    GmcConnT *conn = stmt->conn;
    uint32_t *nspId = &conn->namespaceId;  // 请求成功时，ParseResponse 会直接将结果解析并保存到 conn->namespaceId 上
    ret = CltRequestSync(conn, ClearNamespaceBuildRequest, namespaceName, U32_RESULT_PARSE_RSP, nspId);
    if (ret == GMERR_OK) {
        CltCataRemoveAllDeletedLabels();
    }
    return ret;
}

Status GmcClearNamespaceAsync(GmcStmtT *stmt, const char *namespaceName, GmcClearNamespaceDoneT userCb, void *userData)
{
    Status ret = GmcDropNamespaceCheck(stmt, namespaceName, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    GmcConnT *conn = stmt->conn;
    AsyncMsgContextT ctx =
        MakeAsyncMsgContext(U32_RESULT_PARSE_RSP, ClearNamespaceCallback, userCb, userData, stmt->notDistribute);
    return CltRequestAsync(conn, ClearNamespaceBuildRequest, namespaceName, &ctx);
}
#endif

Status GmcUseNamespace(GmcStmtT *stmt, const char *namespaceName)
{
    Status ret = GmcDropNamespaceCheck(stmt, namespaceName, false);
    if (ret != GMERR_OK) {
        return ret;
    }

    GmcConnT *conn = stmt->conn;
    uint32_t *nspId = &conn->namespaceId;  // 请求成功时，ParseResponse 会直接将结果解析并保存到 conn->namespaceId 上
    return CltRequestSync(conn, UseNamespaceByNameBuildRequest, namespaceName, U32_RESULT_PARSE_RSP, nspId);
}

#ifdef FEATURE_NAMESPACE_ENHANCE
Status GmcUseNamespaceAsync(GmcStmtT *stmt, const char *namespaceName, GmcUseNamespaceDoneT userCb, void *userData)
{
    Status ret = GmcDropNamespaceCheck(stmt, namespaceName, true);
    if (ret != GMERR_OK) {
        return ret;
    }

    GmcConnT *conn = stmt->conn;
    AsyncMsgContextT ctx =
        MakeAsyncMsgContext(U32_RESULT_PARSE_RSP, UseNamespaceCallback, userCb, userData, stmt->notDistribute);
    return CltRequestAsync(conn, UseNamespaceByNameBuildRequest, namespaceName, &ctx);
}

Status GmcCreateNamespaceWithCfg(GmcStmtT *stmt, GmcNspCfgT *nspCfg)
{
    Status ret = GmcCreateNamespaceWithCfgCheck(stmt, nspCfg, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltRequestSync(stmt->conn, CreateNamespaceBuildRequest, nspCfg, DummyParseRsp, NULL);
}

Status GmcCreateNamespace(GmcStmtT *stmt, const char *namespaceName, const char *userName)
{
    Status ret = GmcCreateNamespaceCheck(stmt, namespaceName, userName, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    GmcNspCfgT cfg = {};
    cfg.namespaceName = namespaceName;
    cfg.userName = userName;
    cfg.trxCfg = CltDefaultTrxCfg();
    return CltRequestSync(stmt->conn, CreateNamespaceBuildRequest, &cfg, DummyParseRsp, NULL);
}

Status GmcCreateNamespaceWithCfgAsync(
    GmcStmtT *stmt, GmcNspCfgT *nspCfg, GmcCreateNamespaceDoneT userCb, void *userData)
{
    Status ret = GmcCreateNamespaceWithCfgCheck(stmt, nspCfg, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    AsyncMsgContextT ctx = MakeAsyncMsgContext(DummyParseRsp, CommonDDLCallback, userCb, userData, stmt->notDistribute);
    return CltRequestAsync(stmt->conn, CreateNamespaceBuildRequest, nspCfg, &ctx);
}

Status GmcCreateNamespaceAsync(
    GmcStmtT *stmt, const char *namespaceName, const char *userName, GmcCreateNamespaceDoneT userCb, void *userData)
{
    Status ret = GmcCreateNamespaceCheck(stmt, namespaceName, userName, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    GmcNspCfgT cfg = {};
    cfg.namespaceName = namespaceName;
    cfg.userName = userName;
    cfg.trxCfg = CltDefaultTrxCfg();
    AsyncMsgContextT ctx = MakeAsyncMsgContext(DummyParseRsp, CommonDDLCallback, userCb, userData, stmt->notDistribute);
    return CltRequestAsync(stmt->conn, CreateNamespaceBuildRequest, &cfg, &ctx);
}

Status GmcBindNamespaceToTableSpaceAsync(
    GmcStmtT *stmt, const char *namespaceName, const char *tablespaceName, GmcTablespaceDoneT userCb, void *userData)
{
    Status ret = GmcBindNamespaceToTableSpaceCheck(stmt, namespaceName, tablespaceName, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    CltBindNspToTspCfgT cfg = {};
    cfg.namespaceName = namespaceName;
    cfg.tablespaceName = tablespaceName;
    AsyncMsgContextT ctx = MakeAsyncMsgContext(DummyParseRsp, CommonDDLCallback, userCb, userData, stmt->notDistribute);
    return CltRequestAsync(stmt->conn, BindNspToTspBuildRequest, &cfg, &ctx);
}
#endif
