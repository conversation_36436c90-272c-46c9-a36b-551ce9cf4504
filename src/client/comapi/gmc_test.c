/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * File Name: gmc_test.h
 * Description: interface for testing
 *              Please keep new function in its corresponding position.
 * Author:
 * Create: 2021-7-8
 */

#include "gmc_test.h"
#include "clt_heartbeat.h"
#include "clt_check.h"
#include "clt_stmt.h"
#include "clt_exception.h"
#include "clt_msg.h"
#include "db_secure_msg_buffer.h"
#include "se_instance.h"
#include "db_signal.h"
#include "clt_proto.h"
#include "gmc_yang.h"
#include "gmc_check.h"
#include "gmc_internal.h"
#include "clt_yang_common.h"
#include "dm_yang_subtree.h"
#include "clt_batch.h"
#include "adpt_rdtsc.h"
#include "db_common_init.h"
#include "db_alarm.h"

#define DTL_DEFAULT_NAMESPACE "public"

typedef struct {
    GmcAlarmIdE alarmId;
    uint16_t raiseRatio;
    uint16_t clearRatio;
} AlarmThresholdCfgT;

Status ChangeAlmThresholdBuildRequest(FixBufferT *req, const void *in)
{
    const AlarmThresholdCfgT *config = in;
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_MODIFY_ALARM_THRESHOLD);
    // fill alarmId
    Status ret = FixBufPutUint32(req, (uint32_t)config->alarmId);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill raise and clear threshold
    ret = SecureFixBufPutUint16(req, config->raiseRatio);
    if (ret != GMERR_OK) {
        return ret;
    }
    return SecureFixBufPutUint16(req, config->clearRatio);
}

void GmcStopHeartbeat(GmcConnT *conn)
{
    UnInitHeartBeat();
}

Status GmcChangeAlmThreshold(GmcStmtT *stmt, GmcAlarmIdE alarmId, uint16_t raiseRatio, uint16_t clearRatio)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (alarmId >= GMC_ALARM_ID_UNDEFINED) {
        DB_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "wrong alarm id.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    if (raiseRatio > DB_PERCENTAGE_BASE || clearRatio > DB_PERCENTAGE_BASE) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "raiseRatio or clearRatio.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    AlarmThresholdCfgT cfg = {
        .alarmId = alarmId,
        .raiseRatio = raiseRatio,
        .clearRatio = clearRatio,
    };
    return CltRequestSync(stmt->conn, ChangeAlmThresholdBuildRequest, &cfg, DummyParseRsp, NULL);
}

void GmcDetachAllShmSeg(void)
{
    if (!DbCommonIsSingleCltProcess()) {
        return;
    }
    CltClearAllShmResource();

    // 清理存储引擎device缓存
    SeReleaseCltPageMgr(g_gmdbCltInstance.memCtx);
    SeClearDevCache();
}

extern DbSpinLockT g_gmdbCltShmInitedSpinLock;
extern bool g_gmdbCltShmInited[MAX_INSTANCE_NUM];

/*
 * 仅用于Euler环境测试用例。
 * 使用场景：多实例场景用例构造某个服务端进程退出，用例进程负责清理该服务端进程申请的共享内存。
 */
static void CltDestroyShmByInstanceId(uint32_t instanceId)
{
    // 共进程模式客户端不允许清理共享内存资源。
    if (!DbCommonIsSingleCltProcess()) {
        return;
    }
    DbSpinLock(&g_gmdbCltShmInitedSpinLock);
    uint32_t idx = DbGetIdxByInstanceId(instanceId);
    if (g_gmdbCltShmInited[idx]) {
        // 销毁所有共享内存上下文上挂接的共享内存段。
        DbAdptCliClearAllPoolMgr(instanceId);
        // 销毁所有单独创建的共享内存段。
        for (uint32_t i = DB_TOP_SHMEM_ID; i < DB_INVALID_SHMEM_ID; ++i) {
            uint32_t key = DbConstructShmemKeyById(instanceId, i);
            DB_SHMEM_DESTORY(key);
        }
        g_gmdbCltShmInited[idx] = false;
    }
    DbSpinUnlock(&g_gmdbCltShmInitedSpinLock);
}

/*
 * 仅供测试用途。
 * 使用场景：测试用例在Euler环境构造服务端多实例反复异常退出后重启，为确保服务端的共享内存资源不泄露，
 * 在客户端侧调用本接口手动清理，入参instanceId为被kill掉的服务端进程的实例ID。
 * 注意：在kill掉服务端前用例进程需要和服务端至少建立一次链接。在kill掉服务端后即可调用本函数清理共享内存。
 */
void GmcCltClearShmemResourceByInstance(uint32_t instanceId)
{
    if (!DbCommonIsSingleCltProcess()) {
        return;
    }
    CltDestroyShmByInstanceId(instanceId);
}

extern PageMgrT *g_cltPageMgr[(uint32_t)SE_STORAGE_TYPE_BORDER][MAX_INSTANCE_NUM];
extern DbSpinLockT g_mgrLock;
static void SeReleaseCltPageMgrByInstance(DbMemCtxT *memCtx, uint32_t instanceId)
{
    uint32_t idx = DbGetIdxByInstanceId(instanceId);
    DbSpinLock(&g_mgrLock);
    for (uint32_t type = 0; type < (uint32_t)SE_BUFFER_POOL; type++) {
        if (g_cltPageMgr[type][idx] != NULL) {
            DbDynMemCtxFree(memCtx, g_cltPageMgr[type][idx]);
            g_cltPageMgr[type][idx] = NULL;
        }
    }
    g_cltPageMgr[SE_BUFFER_POOL][idx] = NULL;
    DbSpinUnlock(&g_mgrLock);
}

extern HeartBeatMgrT g_heartBeatMgr;
static void HeartBeatDiscByInstance(uint32_t instanceId)
{
    if (!g_cltSameProcessOpen) {
        return;
    }
#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
    uint32_t idx = DbGetIdxByInstanceId(instanceId);
    HeartBeatMgrT *mgr = &g_heartBeatMgr;
    DbSpinLock(&mgr->lock);
    DbRWSpinWLock(&g_gmdbCltInstance.connLock);
    if (g_gmdbCltInstance.gConnCount != mgr->heartBeatConnNum) {
        DbRWSpinWUnlock(&g_gmdbCltInstance.connLock);
        DbSpinUnlock(&mgr->lock);
        return;
    }
    DbRWSpinWUnlock(&g_gmdbCltInstance.connLock);
    mgr->heartBeatPreparedFlag[idx] = false;
    if (mgr->heartBeatConn[idx] != NULL) {
        CltDisconnect(mgr->heartBeatConn[idx]);
        mgr->heartBeatConn[idx] = NULL;
        mgr->heartBeatConnNum--;
    }
    if (mgr->serverLocatorForHeartBeat[idx] != NULL) {
        DbDynMemCtxFree(g_gmdbCltInstance.memCtx, mgr->serverLocatorForHeartBeat[idx]);
        mgr->serverLocatorForHeartBeat[idx] = NULL;
    }
    DbSpinUnlock(&mgr->lock);
    return;
#endif
}

extern CltNdT g_gmdbCltNd;
static void ShareMsgPoolUninitCltNdByInstance(uint32_t instanceId)
{
    uint32_t idx = DbGetIdxByInstanceId(instanceId);
    if (g_gmdbCltNd.ndArr[idx] != DB_INVALID_ND) {
        (void)DbVnotifyDestroy(g_gmdbCltNd.ndArr[idx]);
    }
}

extern DbAlarmStatT *g_gmdbAlarmStat[MAX_INSTANCE_NUM];
static void DbDetachAllIdpShmemByInstance(uint32_t instanceId)
{
    if (!DbCommonIsSingleCltProcess()) {
        return;
    }
    uint32_t idx = DbGetIdxByInstanceId(instanceId);
    DbResetCachedTopShmCtxPtr(instanceId);
    if (g_gmdbAlarmStat[idx] != NULL) {
        (void)DB_SHMEM_DETACH(g_gmdbAlarmStat[idx]);
        g_gmdbAlarmStat[idx] = NULL;
    }
    DbLogDestroyCurCtrlItem();
    DbLogDetachShmem();
}

static void CltUnInitByInstance(CltInstanceT *clt, uint32_t instanceId)
{
#ifndef NDEBUG
    DB_LOG_ERROR(GMERR_OK, "INFO: gmc unit exec");
#endif
    CltCataClearLabelCache((uint16_t)instanceId, VERTEX_LABEL);
    CltCataClearLabelCache((uint16_t)instanceId, EDGE_LABEL);
    CltCataClearLabelCache((uint16_t)instanceId, KV_TABLE);
    HeartBeatDiscByInstance(instanceId);
    // 暂不支持元数据缓存兜底释放，需要用例手动清理stmt。
    ShareMsgPoolUninitCltNdByInstance(instanceId);
    DbDetachAllIdpShmemByInstance(instanceId);
    CltDestroyShmByInstanceId(instanceId);
}

// 仅限Euler环境使用；仅限用例内部调用，不能作为用例结束后的去初始化兜底手段。
Status GmcUnInitByInstance(uint32_t instanceId)
{
    // 校验是否在异步回调中
    Status ret = CltCheckIfInCallBack();
    if (ret != GMERR_OK) {
        return ret;
    }
    CltInstanceT *clt = &g_gmdbCltInstance;
    DbSpinLock(&clt->initLock);
    // 因为客户端在建立连接时才会获得存储句柄，所以PageMgr是在clt进行SeOpen时才打开，最终在这里进行释放
    SeReleaseCltPageMgrByInstance(clt->memCtx, instanceId);
    CltUnInitByInstance(clt, instanceId);
    DbSpinUnlock(&clt->initLock);
    return GMERR_OK;
}

static Status RaiseSignalOpenShmFunc(int32_t *signalPtr)
{
    return DbRaise(*signalPtr);
}

Status GmcRaiseForStatusMergeSub(uint32_t signal)
{
    if (signal >= DB_SIGNAL_COUNT) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return CltProtectShmProcess((OperShmFuncT)RaiseSignalOpenShmFunc, &signal, SHMEM_OP_STMG_SUB);
}

Status GmcSubGetProperty(GmcStmtT *stmt, GmcSubFetchModeE mode, uint32_t id, void *value, bool *isNull)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltPtr2CheckWithErr(value, isNull);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (stmt->stmtType != CLT_STMT_TYPE_SUB_VERTEX) {
        DB_LOG_AND_SET_LASERR(GMERR_WRONG_STMT_OBJECT,
            "Curr stmt type %" PRId32 "can't do this operation, need subscribe vertex action.",
            (int32_t)stmt->stmtType);
        return GMERR_WRONG_STMT_OBJECT;
    }
    if (mode != GMC_SUB_FETCH_NEW && mode != GMC_SUB_FETCH_OLD && mode != GMC_SUB_FETCH_DELTA) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "mode.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    const TextT *buf;
    const SubPushSingleLabel *sub = CltGetConstOperationContext(stmt);
    if (mode == GMC_SUB_FETCH_NEW) {
        buf = &sub->newData;
    } else if (mode == GMC_SUB_FETCH_OLD) {
        buf = &sub->oldData;
    } else {
        buf = &sub->deltaData;
    }
    if (buf->len == 0) {
        return GMERR_NO_DATA;
    }

    DmVertexT *vertex = sub->vertex;
    DmVertexLabelT *label = sub->cltCataLabel->vertexLabel;
    DmPropertySchemaT *prop;
    DmSchemaT *schema = MEMBER_PTR(label->metaVertexLabel, schema);
    ret = DmSchemaGetPropeById(schema, id, &prop);
    if (ret != GMERR_OK) {
        return ret;
    }

    DmValueT dmv;
    ret = DmVertexBufGetFirstLevelFixedPropById((void *)buf->str, vertex->vertexDesc, id, &dmv);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = GetDmValueWithCopy(&dmv, value, prop->size, isNull);
    return ret;
}

#ifdef FEATURE_YANG
static Status YangNodeInfoToJson(GmcYangNodeT *curInfo, const DmJsonPrintFormatTypeT *formatType, DbJsonT *json);

static Status ChildInfoToJson(GmcYangNodeT *curInfo, const DmJsonPrintFormatTypeT *formatType, DbJsonT *curObj)
{
    GmcYangNodeT *childInfo = NULL;
    GmcYangNodeT *prevChild = NULL;
    Status ret = GmcYangNodeGetNext(curInfo, prevChild, &childInfo);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Subtree %s get next node.", curInfo->name);
        return ret;
    }

    while (childInfo != NULL) {
        ret = YangNodeInfoToJson(childInfo, formatType, curObj);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Subtree node info %s get next node.", childInfo->name);
            return ret;
        }
        prevChild = childInfo;
        ret = GmcYangNodeGetNext(curInfo, prevChild, &childInfo);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Subtree node %s get next node.", childInfo->name);
            return ret;
        }
    }
    return GMERR_OK;
}

Status ProcessDefaultValueToJson(
    const DmJsonPrintFormatTypeT *formatType, const char *propName, DmValueT *dmv, DbJsonT *json)
{
    // 若属性值来源于默认值，在前面加 @。只有 GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED 才可能设置 isDefault
    // 为true，故此处不用判断默认值模式
    char newPropName[DM_MAX_NAME_LENGTH + 1];
    newPropName[0] = '@';
    errno_t err = strcpy_s((char *)newPropName + 1, DM_MAX_NAME_LENGTH, propName);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "strcpy property %s when convert yang tree to json.", propName);
        return GMERR_FIELD_OVERFLOW;
    }
    return DmRecordJsonSetNew(json, newPropName, dmv, *formatType);
}

static Status PropNodeToJson(
    GmcYangNodeT *curInfo, const DmJsonPrintFormatTypeT *formatType, const char *propName, DbJsonT *json)
{
    GmcYangNodeValueT *value = NULL;
    Status ret = GmcYangNodeGetOldValue(curInfo, &value);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (value == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_UNEXPECTED_NULL_VALUE, "Property value: %s when convert yang tree to json.", propName);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (strcmp(propName, value->name) != 0) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Prop name(%s) not equal curr node name(%s).", value->name, propName);
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t valueSize = value->type == GMC_DATATYPE_STRING ? value->size - 1 : value->size;
    DmValueT dmv;
    char enumStr[DM_MAX_NAME_LENGTH] = {0};
    GmcPropValueT outPropValue = {0};
    if (value->type == GMC_DATATYPE_BITMAP) {
        GmcBitMapT tmpBmp;
        tmpBmp.bits = (uint8_t *)(uintptr_t)(value->value);
        tmpBmp.beginPos = 0;
        tmpBmp.endPos = (uint16_t)(valueSize - 1u);
        dmv.value.length =
            (uint16_t)(((uint16_t)(tmpBmp.endPos - tmpBmp.beginPos) + (uint16_t)(1u)) / (uint16_t)BYTE_LENGTH);
        ret = SetDmValue(&dmv, (uint32_t)value->type, &tmpBmp, sizeof(GmcBitMapT));
    } else if (DmIsAttributeType((uint32_t)value->type)) {
        outPropValue.value = enumStr;
        outPropValue.size = DM_MAX_NAME_LENGTH;
        value->name = curInfo->name;
        ret = GmcYangConvertResultProperty(curInfo, value, &outPropValue);
        dmv.type = DB_DATATYPE_STRING;  // 这里赋值为string，让其调用string to json的方法
        dmv.value.constStrAddr = outPropValue.value;
        dmv.value.length = outPropValue.size + 1;
    } else {
        ret = SetDmValue(&dmv, (uint32_t)value->type, value->value, valueSize);
    }
    if (ret != GMERR_OK) {
        return ret;
    }

    if (!value->isDefault) {
        return DmRecordJsonSetNew(json, propName, &dmv, *formatType);
    }
    return ProcessDefaultValueToJson(formatType, propName, &dmv, json);
}

static Status ListNodeToJson(
    GmcYangNodeT *curInfo, const DmJsonPrintFormatTypeT *formatType, const char *listName, DbJsonT *json)
{
    Status ret;
    DbJsonT *curObj = DbJsonCreateObject();
    if (curObj == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, "Create json object for %s.", listName);
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }
    ret = ChildInfoToJson(curInfo, formatType, curObj);
    if (ret != GMERR_OK) {
        DbJsonDelete(curObj);
        return ret;
    }
    // 如果是第一个 list 元素需要创建新array并把array挂到json上
    DbJsonT *curArray = DbJsonObjectGet(json, listName);
    if (curArray == NULL) {
        curArray = DbJsonCreateArray();
        if (curArray == NULL) {
            DbJsonDelete(curObj);
            DB_LOG_AND_SET_LASERR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, "Create json array for %s.", listName);
            return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
        }
        ret = DbJsonArrayAppendNew(curArray, curObj);
        if (ret != GMERR_OK) {
            DbJsonDelete(curArray);
            DB_LOG_AND_SET_LASERR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
                "Append new object to json array when processing %s.", listName);
            return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
        }

        ret = DbJsonObjectSetNew(json, listName, curArray);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, "Add json array %s.", listName);
            return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
        }
        return GMERR_OK;
    }
    if (!DbJsonIsArray(curArray)) {
        DbJsonDelete(curObj);
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "DbJsonT type of %s should be array.", listName);
        return GMERR_DATA_EXCEPTION;
    }
    // 如果不是第一个 list 元素只需在已创建的array后面追加当前object
    ret = DbJsonArrayAppendNew(curArray, curObj);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(
            GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, "Append new object to json array when processing %s.", listName);
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }

    return GMERR_OK;
}

static Status YangNodeInfoToJson(GmcYangNodeT *curInfo, const DmJsonPrintFormatTypeT *formatType, DbJsonT *json)
{
    GmcYangNodeTypeE infoType;
    Status ret = GmcYangNodeGetType(curInfo, &infoType);
    if (ret != GMERR_OK) {
        return ret;
    }
    const char *infoName = NULL;
    ret = GmcYangNodeGetName(curInfo, &infoName);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (infoName == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Yang node name when convert yang tree to json.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    // 属性类型，其value就是属性值，直接获取并填入json。
    if (infoType == GMC_YANG_FIELD) {
        return PropNodeToJson(curInfo, formatType, infoName, json);
    }

    if (infoType == GMC_YANG_CONTAINER || infoType == GMC_YANG_CHOICE || infoType == GMC_YANG_CASE) {
        // 节点类型需要新建object
        DbJsonT *curObj = DbJsonCreateObject();
        if (curObj == NULL) {
            DB_LOG_AND_SET_LASERR(
                GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, "Unable to create json object for %s.", infoName);
            return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
        }
        // 非list类型节点，将object填充好后挂到json上
        ret = ChildInfoToJson(curInfo, formatType, curObj);
        if (ret != GMERR_OK) {
            DbJsonDelete(curObj);
            return ret;
        }
        return DbJsonObjectSetNew(json, infoName, curObj);
    }
    // list类型，其value是一个array
    return ListNodeToJson(curInfo, formatType, infoName, json);
}

static Status YangTreeSchemaInfoToJson(const GmcYangTreeT *replyTree, char **replyJson)
{
    DbMemCtxT *memCtx = replyTree->memCtx;
    DmSubtreeT *schemaSubtree = replyTree->subtree;
    DbJsonT *jsonObj = DbJsonCreateObject();
    if (jsonObj == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, "Create json object.");
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }
    bool isRetNameList = replyTree->filterMode == (uint32_t)GMC_FETCH_MODEL_LIST ? true : false;
    Status ret = DmCreateModelTreeJson(memCtx, isRetNameList, schemaSubtree, jsonObj);
    if (ret != GMERR_OK) {
        DbJsonDelete(jsonObj);
        return ret;
    }
    uint32_t janssonFlag = DB_JSON_INDENT(4) & 0xFFFFFF;
    ret = DmJsonToStr(memCtx, jsonObj, janssonFlag ^ DM_JSON_EXPORT_NULL_INFO, replyJson);
    DbJsonDelete(jsonObj);
    return ret;
}

Status GmcYangTreeToJson(const GmcYangTreeT *replyTree, char **replyJson)
{
    if (replyTree == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Reply tree.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    if (replyJson == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Reply json pointer.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    if (replyTree->filterMode == (uint32_t)GMC_FETCH_MODEL_LIST || replyTree->filterMode == (uint32_t)GMC_FETCH_MODEL) {
        return YangTreeSchemaInfoToJson(replyTree, replyJson);
    }

    DbMemCtxT *memCtx = replyTree->memCtx;
    DbMemCtxT *oldMemCtx = DbMemCtxSwitchTo(memCtx);
    GmcYangNodeT *rootNode = NULL;
    Status ret = GmcYangGetRootNode(replyTree, &rootNode);
    if (ret != GMERR_OK) {
        return ret;
    }

    DmJsonPrintFormatTypeT formatType;
    formatType.fmtInfo = NULL;
    formatType.nullInfoFmt = DM_EXPORT_NULL_INFO;
    formatType.jsonOutputFmt = DB_JSON_EXPORT;
    uint32_t janssonFlag = DB_JSON_INDENT(4) & 0xFFFFFF;

    DbJsonT *jsonRoot = DbJsonCreateObject();
    if (jsonRoot == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, "Create json object for root.");
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }
    ret = ChildInfoToJson(rootNode, &formatType, jsonRoot);
    (void)DbMemCtxSwitchBack(oldMemCtx, memCtx);
    if (ret != GMERR_OK) {
        DbJsonDelete(jsonRoot);
        return ret;
    }
    ret = DmJsonToStr(memCtx, jsonRoot, janssonFlag ^ DM_JSON_EXPORT_NULL_INFO, replyJson);
    DbJsonDelete(jsonRoot);
    return ret;
}

Status GmcYangTreeToJsonFile(const char *pathname, const char *mode, const GmcYangTreeT *replyTree, uint32_t jsonFlag)
{
    if (pathname == NULL || mode == NULL || replyTree == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "pathname or mode or reply tree");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    char *replyJson = NULL;
    uint32_t janssonFlag = jsonFlag & 0xFFFFFF;
    Status ret = DmCreateSubtreeText(replyTree->memCtx, replyTree->subtree, janssonFlag ^ DM_JSON_EXPORT_NULL_INFO,
        replyTree->defaultMode, &replyJson);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Create subtree DbJsonT Text.");
        return ret;
    }

    FILE *fp = fopen(pathname, mode);
    if (fp == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_FILE_OPERATE_FAILED, "Open file %s, os ret no %" PRId32 ".", pathname, DbAptGetErrno());
        DbDynMemCtxFree(replyTree->memCtx, replyJson);  // 只在函数内部使用，内存指针没有往外传递，不用置空
        return GMERR_FILE_OPERATE_FAILED;
    }

    int retf = fputs(replyJson, fp);
    DbDynMemCtxFree(replyTree->memCtx, replyJson);  // 只在函数内部使用，内存指针没有往外传递，不用置空
    if (retf == EOF) {
        DB_LOG_AND_SET_LASERR(
            GMERR_FILE_OPERATE_FAILED, "write file %s, os ret no %" PRId32, pathname, DbAptGetErrno());
        (void)fclose(fp);
        return GMERR_FILE_OPERATE_FAILED;
    }

    (void)fclose(fp);
    return GMERR_OK;
}
#endif
Status GmcCheckPrefetchLabelsInCache(GmcStmtT *stmt, const char *labelName, GmcLabelTypeE labelType)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK || labelName == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "input parameter.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    CltCataKeyT cataKey;
    GmcConnT *conn = stmt->conn;
    void *label = NULL;
    if (labelType == GMC_VERTEX_LABEL_TYPE) {
        CltSetCataKey(&cataKey, (uint16_t)conn->instanceId, (uint16_t)conn->dbId, conn->namespaceId, labelName);
        ret = CltCataGetVertexLabelByName(stmt, &cataKey, DM_SCHEMA_MIN_VERSION, (CltCataLabelT **)&label);
#ifdef FEATURE_EDGELABEL
    } else if (labelType == GMC_EDGE_LABEL_TYPE) {
        CltSetCataKey(&cataKey, (uint16_t)conn->instanceId, (uint16_t)conn->dbId, conn->namespaceId, labelName);
        ret = CltCataGetEdgeLabelByName(&cataKey, (CltCataLabelT **)&label);
#endif
    } else {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "labelType.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    if (ret != GMERR_OK || label == NULL) {
        return GMERR_NO_DATA;
    }

    if (labelType == GMC_VERTEX_LABEL_TYPE) {
        CltCataCloseVertexLabel((CltCataLabelT *)label);
    } else {
#ifdef FEATURE_EDGELABEL
        CltCataCloseEdgeLabel((CltCataLabelT *)label);
#endif
    }

    return GMERR_OK;
}

Status GmcGetClientMemCtxPhySize(GmcConnT *conn, GmcStmtT *stmt, GmcClientMemCtxStatInfoT *memCtxInfo)
{
    Status ret = CltCheckInited();
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "clt not initialized");
        return ret;
    }
    if (memCtxInfo == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "memCtxInfo");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (conn && CltCheckConnStat(conn) != GMERR_OK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (stmt && CltCheckStmtStat(stmt) != GMERR_OK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    *memCtxInfo = (GmcClientMemCtxStatInfoT){0};
    memCtxInfo->clientTotalMemSize = DbDynMemCtxGetTreePhySize(g_gmdbCltInstance.memCtx, true);
    memCtxInfo->clientcachedMemSize = DbDynMemCtxGetTreePhySize(g_gmdbCltInstance.cltCommCtx, true);
    if (conn) {
        memCtxInfo->connMemSize = DbDynMemCtxGetTreePhySize(conn->memCtx, true);  // local dynamic memctx
        memCtxInfo->connMsgSize = DbDynMemCtxGetTreePhySize(conn->msgCtx, true);  // message memctx
    }
    if (stmt) {
        memCtxInfo->stmtMemSize = DbDynMemCtxGetTreePhySize(stmt->memCtx, true);
        memCtxInfo->stmtOpSize = DbDynMemCtxGetTreePhySize(stmt->opCtx, true);
    }
    return GMERR_OK;
}

Status GmcGetClientMemCtxAllocSize(GmcConnT *conn, GmcStmtT *stmt, GmcClientMemCtxStatInfoT *memCtxInfo)
{
    Status ret = CltCheckInited();
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "clt not initialized");
        return ret;
    }
    if (memCtxInfo == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "memCtxInfo");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (conn && CltCheckConnStat(conn) != GMERR_OK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (stmt && CltCheckStmtStat(stmt) != GMERR_OK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    *memCtxInfo = (GmcClientMemCtxStatInfoT){0};
    memCtxInfo->clientTotalMemSize = DbDynMemCtxGetTreeAllocSize(g_gmdbCltInstance.memCtx, true);
    memCtxInfo->clientcachedMemSize = DbDynMemCtxGetTreeAllocSize(g_gmdbCltInstance.cltCommCtx, true);
    if (conn) {
        memCtxInfo->connMemSize = DbDynMemCtxGetTreeAllocSize(conn->memCtx, true);  // local dynamic memctx
        memCtxInfo->connMsgSize = DbDynMemCtxGetTreeAllocSize(conn->msgCtx, true);  // message memctx
    }
    if (stmt) {
        memCtxInfo->stmtMemSize = DbDynMemCtxGetTreeAllocSize(stmt->memCtx, true);
        memCtxInfo->stmtOpSize = DbDynMemCtxGetTreeAllocSize(stmt->opCtx, true);
    }
    return GMERR_OK;
}

Status GmcGetCltCfg(const char *cfgName, GmcDataTypeE type, void *value)
{
    Status ret = GmcSetCltCfgCheck(cfgName, type, value);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbCltCfgEmItemIdE cfgId = DbCltCfgGetIdByName(cfgName);
    DbCfgValueT cfgValue = {};
    ret = DbCltCfgGetById(cfgId, &cfgValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    *(int32_t *)value = cfgValue.int32Val;

    return GMERR_OK;
}

Status GmcGetBatchOption(GmcBatchT *batch, GmcBatchOptionT *option)
{
    if (batch == NULL || option == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "batch or option");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    option->batchType = batch->option.batchType;
    option->diffType = batch->option.diffType;
    option->batchOrder = batch->option.batchOrder;
    option->batchErrCtl = batch->option.batchErrCtl;
    option->batchRecycleSize = batch->option.batchRecycleSize;
    option->batchLimitSize = batch->option.batchLimitSize;
    option->maxBatchOpNum = batch->option.maxBatchOpNum;
    option->inited = batch->option.inited;
    return GMERR_OK;
}

int32_t GmcGetServerMsgSendTime(GmcStmtT *stmt, uint64_t *serverMsgSendTime)
{
    if (stmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "stmt when get server msg send time");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    if (serverMsgSendTime == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "serverMsgSendTime when get server msg send time");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    GmcConnT *conn = stmt->conn;
    if (conn == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "conn in stmt when get server msg send time");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    FixBufferT *buffer = &conn->recvPack;
    const MsgHeaderT *msgHeader = RpcPeekMsgHeader(buffer);
    *serverMsgSendTime = msgHeader->rspSendStartTime;
    return GMERR_OK;
}

uint64_t GmcGetGlobalRdtsc(void)
{
    return DbGlobalRdtsc();
}

uint64_t GmcToUseconds(uint64_t cycles)
{
    return DbToUseconds(cycles);
}

bool GmcIsSameProcess(void)
{
    return (DbGetServerSameProcessStartFlag() != 0);
}

bool GmcIsRemoteClient(uint32_t instanceId)
{
    for (uint32_t i = 0; i < DB_SHM_CTX_BUTT; i++) {
        if (DbCheckShmemCtxAttachById(i, instanceId)) {
            return false;
        }
    }
    return true;
}

int32_t GmcForceUnimportDatalogSo(GmcStmtT *stmt, const char *soName, const char *namespaceName)
{
    if (IsStringEmpty(namespaceName)) {
        // 如果用户未设置namespaceName，则使用默认的public命名空间
        namespaceName = DTL_DEFAULT_NAMESPACE;
    }

    Status ret = GmcUnImportDatalogCheck(stmt, soName, namespaceName);
    if (ret != GMERR_OK) {
        return ret;
    }

    UninstallDatalogProtoT proto = {0};
    proto.protoHead.opCode = MSG_OP_RPC_UNINSTALL_DATALOG;
    proto.fileName = soName;
    proto.namespaceName = namespaceName;
    proto.ignorePubsub = true;
    ret = CltStmtSendAndRecvMsg(stmt, &proto.protoHead);
    if (ret == GMERR_OK) {
        CltCataRemoveAllDeletedLabels();
    }
    return ret;
}

void GmcEnableRegAdaptFuncs(void)
{
#ifdef FEATURE_SERVER_FUNC_REG
    DbEnableRegAdaptFuncs();
#endif
}

#ifdef EXPERIMENTAL_NERGC
#define SESSION_ID_LEN 26
#define SESSION_BINARY_PRINT_LEN 2
void GmcModifyCliSessionId(GmcConnT *conn, unsigned char newSessionId[SESSION_ID_LEN])
{
    memcpy_s(&conn->sessionId, sizeof(conn->sessionId), &newSessionId, SESSION_ID_LEN);
    char connHex[SESSION_ID_LEN * SESSION_BINARY_PRINT_LEN + 1] = {0};

    for (int i = 0; i < SESSION_ID_LEN; i++) {
        (void)sprintf_s(connHex + (i * SESSION_BINARY_PRINT_LEN), sizeof(connHex) - (i * SESSION_BINARY_PRINT_LEN),
            "%02x", conn->sessionId.id[i]);
    }
    DB_LOG_INFO("GmcModifyCliSessionId conn sessionId: %s", connHex);
}
#endif

static Status SetDbEmergencyBuildReq(FixBufferT *req, const void *in)
{
    DB_UNUSED(in);
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_SET_EMERGENCY);
    return GMERR_OK;
}

Status GmcSetDbEmergency(GmcStmtT *stmt)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltRequestSync(stmt->conn, SetDbEmergencyBuildReq, NULL, DummyParseRsp, NULL);
}
