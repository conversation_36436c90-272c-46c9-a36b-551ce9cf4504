/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: clt_resource.c
 * Description: Implement of GMDB client resource
 * Author: wangwei
 * Create: 2020-08-03
 */

#include "clt_resource.h"
#include "clt_catalog.h"
#include "clt_heartbeat.h"
#include "db_share_msg_pool.h"
#include "clt_async_conn.h"
#include "clt_async_timeout.h"
#include "clt_yang_errorpath.h"
#include "adpt_function_loader.h"
#include "db_dyn_load.h"
#include "adpt_init.h"

// 为直连写申请, trx提交/回滚时可能需要用到的逃生memCtx
void *g_reserveMemCtxForCltW = NULL;

ServerConfigT g_gmdbServerConfig = {0};

uint32_t g_initTime = 0;

SO_EXPORT CltInstanceT g_gmdbCltInstance = {
    // some non-zero members must be initialized here
    .initStatus = GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
    .initLock = DB_SPINLOCK_INIT_VAL,  // 客户端进程级数据结构g_gmdbCltInstance进行初始化以及去初始化时需要加锁保护
    // other members will be zero initialized
};

// 客户端需要加载的特性列表
const char *g_gmdbCltCmpntList[] = {
    COMPONENT_DURABLE_MEMDATA, COMPONENT_MEMDATA, COMPONENT_BUFFER_POOL, "trm", COMPONENT_PERSISTENCE, COMPONENT_HAC};

uint32_t CltGetCmpntListNum(void)
{
    return (uint32_t)ELEMENT_COUNT(g_gmdbCltCmpntList);
}

const char **CltGetCmpntList(void)
{
    return g_gmdbCltCmpntList;
}

Status CltInitMap(CltInstanceT *clt)
{
    // memCtx用途：客户端存储元数据的内存
    // 生命周期：长进程级别
    // 释放方式：就近释放
    // 兜底清空措施：客户端进程去初始化时，销毁该memCtx
    DbMemCtxArgsT args = {0};
    args.dynCtxNeedRecycle = true;
    args.liteModOn = true;
    void *catalogCtx = DbCreateDynMemCtx(clt->memCtx, true, "CLTCATA", &args);
    if (catalogCtx == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Alloc catalogCtx.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    clt->cltCommCtx = catalogCtx;

    struct {
        DbOamapT *map;
        DbOamapCompareT cmp;
    } table[] = {
        {&clt->labelMap, CltCataKeyCompare},
        {&clt->subscriptionMap, CltCataKeyCompare},
        {&clt->subCataKeyMap, DbOamapUint32Compare},
        {&clt->queryStatisticsMap, CltCataKeyCompare},
    };

    for (uint32_t i = 0; i < ELEMENT_COUNT(table); ++i) {
        // 与进程同生命周期。但是为支撑yang客户端小型化，在客户端进程满足已下条件时会进行Destroy
        // 不存在stmt时，并且开启catalist特性，同时关闭客户端统计视图，并且该进程不存在订阅关系
        Status ret = DbOamapInit(table[i].map, 0, table[i].cmp, catalogCtx, true);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Oamap init at client init");
            return GMERR_INTERNAL_ERROR;
        }
    }
    clt->cataListMgr.cataListHead = NULL;
    clt->cataListMgr.memCtx = catalogCtx;
    clt->cataListMgr.isSet = false;
    DbSpinInit(&clt->cataListMgr.lock);
    return GMERR_OK;
}

static void CltInitComponent(void)
{
    if (DbCommonIsSingleCltProcess()) {
        FixedHeapInit();
    }
}

Status CltInit(CltInstanceT *clt)
{
    // already inited, skip
    if (clt->initStatus == GMERR_OK) {
        return GMERR_OK;
    }

    Status result = DbCommonInitClt();
    if (result != GMERR_OK) {
        DB_LOG_ERROR(result, "initialize the environment.");
        return result;
    }

    CltInitComponent();

    CltInitCltControlChannelInfo();

    // memCtx用途：客户端实例内存
    // 生命周期：长进程级别
    // 释放方式：就近释放
    // 兜底清空措施：客户端进程去初始化时，销毁该memCtx
    DbMemCtxArgsT args = {0};
    args.liteModOn = true;
    DbMemCtxT *rootCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "CLTROOT", &args);
    if (rootCtx == NULL) {
        DbCommonUninitClt();
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Alloc the clt rootCtx.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    clt->memCtx = rootCtx;
    result = CltInitMap(clt);
    if (result != GMERR_OK) {
        goto E;
    }
    result = InitEventContext();
    if (result != GMERR_OK) {
        goto E;
    }
    ShareMsgPoolInitCltNd();
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    DirectShareMsgPoolCltInitNd();
#endif
    clt->initStatus = GMERR_OK;
    return result;
E:
    DbDeleteDynMemCtx(rootCtx);
    DbCommonUninitClt();
    clt->memCtx = NULL;
    return result;
}

void CltDeleteTopDynMemCtx(DbMemCtxT *memCtx)
{
    // 处理极端场景下心跳与gmc uninit之间的并发
    if (IsUseListCata()) {
        CltCataListMgrT *cataListMgr = &g_gmdbCltInstance.cataListMgr;
        DbSpinLock(&cataListMgr->lock);
        DbDeleteDynMemCtx(memCtx);
        DbSpinUnlock(&cataListMgr->lock);
    } else {
        DbOamapT *map = &g_gmdbCltInstance.labelMap;
        DbRWSpinWLock(&map->rwLock);
        DbDeleteDynMemCtx(memCtx);
        DbRWSpinWUnlock(&map->rwLock);
    }
}

void CltUnInit(CltInstanceT *clt)
{
    if (clt->initStatus == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
        return;
    }
#ifndef NDEBUG
    DB_LOG_ERROR(GMERR_OK, "INFO: gmc unit exec");
#endif
    clt->initStatus = GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    UnInitAsyncTimeout();
    UnInitHeartBeat();
    HeartBeatDisc();
    UnInitEventContext();

    CltUnInitCltControlChannelInfo();

    if (clt->memCtx != NULL) {
        clt->cltCommCtx = NULL;
        DbOamapT invalidMap = {};
        clt->subscriptionMap = invalidMap;
        clt->queryStatisticsMap = invalidMap;
        // g_reserveMemCtxForCltW 通过销毁根节点来兜底清理（即DbDeleteDynMemCtx(clt->memCtx)）
        // 释放后应将其置为空
        g_reserveMemCtxForCltW = NULL;
        // all members will be freed by deleting root memctx
        CltDeleteTopDynMemCtx(clt->memCtx);
        clt->cataListMgr = (CltCataListMgrT){0};
        clt->labelMap = invalidMap;
        // set members to zero
        clt->memCtx = NULL;
    }
    g_heartBeatMgr = (HeartBeatMgrT){0};
    DbCltCfgSetDefaultByName("clientCataLimitEnable");
    DbCltCfgSetDefaultByName("clientLiteDynMemModeEnable");
    ShareMsgPoolUninitCltNd();
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    DirectSharedMsgPoolCltUninitNd();
#endif
    DirectSharedMsgUnInitClientRingCtx();
    DbCommonUninitClt();
    DbAdptResetMemFuncs();
#ifdef FEATURE_YANG
    DbFreeErrorPath();
#endif
}

inline bool CltGetSecurityMode(void)
{
    DbCfgValueT cfgValue;
    Status ret = DbCltCfgGetById(DB_CLT_CFG_SECURITY_CHECK_MODE, &cfgValue);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return true;
    }
    return cfgValue.int32Val == 1;
}

bool CltCheckStatisticSysviewIsDisable(void)
{
    DbCfgValueT cfgValue = {};
    Status ret = DbCltCfgGetById(DB_CLT_CFG_QRY_STATISTIC_ENABLE, &cfgValue);
    if (ret != GMERR_OK) {
        return true;
    }
    return cfgValue.int32Val == 0;
}

int32_t CltGetCataLimitNum(void)
{
    DbCfgValueT cfgValue = {};
    Status ret = DbCltCfgGetById(DB_CLT_CFG_CLT_LIMIT_ENABLE, &cfgValue);
    if (ret != GMERR_OK) {
        return false;
    }
    return cfgValue.int32Val;
}

/*
 * description: 客户端加载组件化so，单so场景下编译链接so，直接返回，多so场景下，dlopen g_gmdbCltCmpntList 中指定的so
 * param {const char} *cmpntLibDir so所在目录
 * param {const bool} *cmpntNeedLoad so是否需要加载
 * return {*} 加载成功返回 GMERR_OK
 */
Status CltLoadCmpntSo(const char *cmpntLibDir, const bool *cmpntNeedLoad)
{
    DB_POINTER2(cmpntLibDir, cmpntNeedLoad);
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < ELEMENT_COUNT(g_gmdbCltCmpntList); i++) {
        const char *cmpntName = g_gmdbCltCmpntList[i];
        if (!cmpntNeedLoad[i] || DbDynLoadSoIsLoadedClt(cmpntName)) {
            continue;
        }
        char cmpntPath[DB_MAX_PATH] = {0};
        ret = DbDynGetFeatureLibPath(cmpntPath, cmpntLibDir, cmpntName);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get so full path, %s, %s.", cmpntLibDir, cmpntName);
            break;
        }
#ifdef SINGLE_SO
        ret = DbDynLoadSetSingleSoClt(cmpntName);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "set single so %s handle.", cmpntName);
            break;
        }
#else
        void *handle = NULL;
        ret = DbAdptLoadLibrary(cmpntPath, &handle, RTLD_LAZY);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "load so, %s.", cmpntPath);
            break;
        }
        ret = DbDynLoadSetSoHandleClt(cmpntName, handle);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "set %s so handle.", cmpntName);
            break;
        }
#endif
    }
    DbPatchRestore();
    if (ret != GMERR_OK) {
        DbDynUnloadFeatureSoHandle();
    }
    return ret;
}

// 调用各个组件的初始化接口，组件初始化顺序与服务端保持一致
Status CltInitEachComponent(uint16_t instanceId)
{
    DB_UNUSED(instanceId);
    return GMERR_OK;
}

Status CltInitCmpntResource(uint16_t instanceId, const char *cmpntLibDir, const bool *cmpntNeedLoad)
{
    if (DbDynLoadCltComponentizeHasFinished()) {
        return GMERR_OK;
    }
    Status ret = CltLoadCmpntSo(cmpntLibDir, cmpntNeedLoad);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltInitEachComponent(instanceId);
    if (ret != GMERR_OK) {
        DbDynUnloadFeatureSoHandle();
        return ret;
    }
    DbDynLoadCltComponentizeFinished();
    return GMERR_OK;
}
