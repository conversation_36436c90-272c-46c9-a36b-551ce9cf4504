/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: clt_resource.h
 * Description: header file for GMDB client resource
 * Author: wangwei
 * Create: 2020-7-31
 */

#ifndef CLT_RESOURCE_H
#define CLT_RESOURCE_H

#include "db_hashmap.h"
#include "clt_meta_cache.h"

#ifdef __cplusplus
extern "C" {
#endif
typedef struct {
    Status initStatus;
    DbSpinLockT initLock;
    DbMemCtxT *memCtx;  // 其父为topMemCtx，uninit的时候会一把删除，所属内存最长生命周期为一个客户端实例
    DbMemCtxT *cltCommCtx;  // CLTCATA, 其父为上一行的memCtx，用于客户端缓存元数据
    DbOamapT labelMap;
    DbOamapT subscriptionMap;
    DbOamapT subCataKeyMap;
    DbOamapT queryStatisticsMap;
    CltCataListMgrT cataListMgr;
    uint64_t cltUnique;
    uint64_t gStmtCount;
    uint64_t initCount;
    uint64_t gConnCount;
    uint64_t pid;
    DbRWSpinLockT connLock;
} CltInstanceT;
static_assert((offsetof(CltInstanceT, connLock) % sizeof(uint32_t)) == 0,
    "field connLock in CltInstanceT must be aligned by 4 bytes");
DB_CHECK_ALIGN_ATOMIC64_MEMBER(CltInstanceT, gStmtCount);

// 客户端进程级别的配置项建议放置，而不是GmcConnT中
typedef struct ServerConfig {
    uint32_t firstWorkerHungThreshold;  // unit: us
} ServerConfigT;

extern uint32_t g_initTime;
extern CltInstanceT g_gmdbCltInstance;
extern void *g_reserveMemCtxForCltW;
extern ServerConfigT g_gmdbServerConfig;

Status CltInit(CltInstanceT *clt);
void CltUnInit(CltInstanceT *clt);
bool CltGetSecurityMode(void);
bool CltCheckStatisticSysviewIsDisable(void);
int32_t CltGetCataLimitNum(void);
Status CltInitCmpntResource(uint16_t instanceId, const char *cmpntLibDir, const bool *cmpntNeedLoad);
uint32_t CltGetCmpntListNum(void);
const char **CltGetCmpntList(void);
static inline bool CltCfgIsOpenInt32(int32_t value)
{
    return value != 0;
}

#ifdef __cplusplus
}
#endif

#endif /* __CLT_RESOURCE_H__ */
