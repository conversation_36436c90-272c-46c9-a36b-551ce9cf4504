/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: clt_conn.c
 * Description: Implement of GMDB client for connection
 * Author:
 * Create: 2020-08-05
 */

#include "clt_conn.h"
#include "clt_conn_helper.h"
#include "clt_async_conn.h"
#include "clt_async_timeout.h"
#include "clt_meta_cache.h"
#include "clt_check.h"
#include "clt_msg.h"
#include "clt_namespace.h"
#include "clt_heartbeat.h"
#include "clt_resource.h"
#include "db_common_init.h"
#include "adpt_sleep.h"
#include "adpt_process_name.h"
#include "db_share_msg_node.h"
#include "db_secure_msg_buffer.h"
#include "db_direct_msg_pool.h"
#include "clt_da_write.h"
#include "clt_da_read.h"
#include "db_config.h"
#include "adpt_io.h"
#include "clt_catalog_sub.h"
#include "clt_sub_conn.h"
#include "db_rsmem.h"
#include "db_dyn_load.h"
#include "clt_error.h"
#include "adpt_define.h"
#ifdef FEATURE_HAC
#include "se_hac_common.h"
#endif

Status CltDirectOpen(GmcConnT *conn, CliConnectResponseT *rsp, const CliConnectRequestT *requestParams);
void CltDirectClose(GmcConnT *conn);

#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
bool g_cltSameProcessOpen = true;
#else
bool g_cltSameProcessOpen = false;
#endif

// 拷贝客户端要组件化的so名字到建连请求
void CltCopyCmpntInfoToReq(CliConnectRequestT *request)
{
    uint32_t cmpntNum = CltGetCmpntListNum();
    DB_ASSERT(cmpntNum <= COMPONENT_SO_MAX_NUM);
    const char **srcCmpntArr = CltGetCmpntList();
    for (uint32_t i = 0; i < cmpntNum; i++) {
        size_t cmpntNameLen = strlen(srcCmpntArr[i]);
        DB_ASSERT(cmpntNameLen < DB_MAX_NAME_LEN);
        (void)memcpy_s(request->cmpntLibNameArr[i], DB_MAX_NAME_LEN, srcCmpntArr[i], cmpntNameLen);
    }
    request->cmpntLibNameArrNum = cmpntNum;
}

inline static void SetCltCallBackTimeOutInfo(GmcConnOptionsT *options)
{
#ifdef RTOSV2X
    options->singleCallbackTimeoutMs = CLT_CALLBACK_SINGLE_TIMEOUT_THRESHOLD_RTOSV2X;
    options->avgcallbackTimeoutMs = CLT_CALLBACK_AVG_TIMEOUT_THRESHOLD_RTOSV2X;
#else
    options->singleCallbackTimeoutMs = CLT_CALLBACK_SINGLE_TIMEOUT_THRESHOLD;
    options->avgcallbackTimeoutMs = CLT_CALLBACK_AVG_TIMEOUT_THRESHOLD;
#endif
}

void CltInitConnOptions(GmcConnOptionsT *connOptions)
{
    DB_POINTER(connOptions);

    const uint32_t defaultTimeout = DEFAULT_TIMEOUT * DB_RDTSC_SECOND_TO_MSECOND_TO_USECOND;  // 60S
    const uint32_t defaultMsgQSize = 64;
    const uint16_t defaultMaxStmtCount = 10240;

    GmcConnOptionsT options = {};
    options.requestParams.requestWeight = CS_DEFAULT_REQUEST_WEIGHT;
    options.requestParams.isLobConn = false;
    options.requestParams.timeoutMs =
        MAX_WAIT_TIME_PER_REQUEST;  // 先用异步请求最大超时时间初始化，建连握手的时候再根据连接类型修改
    options.requestParams.logThreshold = 0;
    options.requestParams.rollBackThreshold = 0;
    options.requestTimeoutUs = CS_NEVER_TIMEOUT;
    options.requestParams.subMsgRingSize = DEFAULT_SUB_MSG_RING_SIZE;
    CltCopyCmpntInfoToReq(&options.requestParams);
    options.msgReadTimeoutMs = defaultTimeout;   // 60 * 1000 ms
    options.msgWriteTimeoutMs = defaultTimeout;  // 60 * 1000 ms
    options.msgQueueSize = defaultMsgQSize;
    options.openDirectRead = true;
    options.openDirectWrite = true;  // conn级别的开关，默认打开，后续逐步考虑移除
    options.timeInterval = 1;        // CLT_HEARTBEAT_INTERVAL * 1 s
    options.timeThreshold = CLT_DISABLE_SLOW_OP_LOG;
    options.packShrinkThreshold = DB_MAX_UINT32;
    options.maxStmtCount = defaultMaxStmtCount;
    options.bigObjectStand = 0;  // 性能原因需要将默认值改为0，用户需要使用时先配置门限
    options.connMemCtxLimit = MAX_CLIENT_CONN_MEMCTX_ALLOC_SIZE;
    options.asyncTimeoutMs = MAX_WAIT_TIME_PER_REQUEST;  // 异步超时时间阈值
    options.isLocalAsyncTimeout = false;
    options.isCsMode = false;
#if (defined(FEATURE_TS) && !defined(TS_MULTI_INST) && !defined(FEATURE_FASTPATH) && !defined(FEATURE_STREAM))
    options.isCsMode = true;
    options.requestParams.isLobConn = true;
#endif
    SetCltCallBackTimeOutInfo(&options);
    *connOptions = options;
}

Status CltConnOptionsCreate(GmcConnOptionsT **connOptions)
{
    // 用户调用GmcConnOptionsDestroy接口释放该内存
    *connOptions = DbDynMemCtxAlloc(g_gmdbCltInstance.memCtx, sizeof(GmcConnOptionsT));
    if (*connOptions == NULL) {
        // Alloc memory worthless when client alloc connection options
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Client alloc connection options.");
        return GMERR_OUT_OF_MEMORY;
    }
    CltInitConnOptions(*connOptions);
    return GMERR_OK;
}

void CltConnOptionsDestroy(GmcConnOptionsT *connOptions)
{
    (void)memset_s(connOptions, sizeof(GmcConnOptionsT), 0, sizeof(GmcConnOptionsT));
    DbDynMemCtxFree(g_gmdbCltInstance.memCtx, connOptions);  // 正常释放内存，后续不会再用到
}

#if (defined SECUREFIXBUF)
// 由于时序问题，第一个包需要打桩处理，消息池合入后优化该接口
inline static Status ShmBufSkipVerify(DbDataT ctx)
{
    DB_UNUSED(ctx);
    return GMERR_OK;
}
#endif

static void CltFixbuffCreate(GmcConnT *conn, DbMemCtxT *msgCtx)
{
    conn->msgCtx = msgCtx;
    uint32_t flags = conn->isLobConn ? FIX_BUF_FLAG_LOB_BUFFER : FIX_BUF_FLAG_EXTEND_BUFFER;
    DbMemCtxT *memCtx =
        (CltConnIsAsyncLob(conn) && conn->lctrType == LCTR_TYPE_HPE_CHANNEL) ? conn->memCtx : conn->msgCtx;
    // channel通信下:
    // sendPack内报文的内存申请在填报文的逻辑中，由服务端释放
    // recvPack内报文的内存接收与服务端，在MsgBufferFreeListPush中加入freelist或者释放
    // 注意：channel通信大报文场景下conn->sendPack和conn->recvPack都为动态内存。
    // socket通信下，默认不释放，除非开启缩容
    FixBufInit(&conn->sendPack, NULL, 0, 0, flags, memCtx);
    FixBufInit(&conn->recvPack, NULL, 0, 0, flags, memCtx);

#if (defined SECUREFIXBUF)
    // 由于时序问题，第一个包需要打桩处理，消息池合入后优化该接口
    DbDataT ctx;
    ctx.ptr = NULL;
    SecureFixBufSetVerifyProc(&conn->recvPack, ShmBufSkipVerify, ctx);
    SecureFixBufSetVerifyProc(&conn->sendPack, ShmBufSkipVerify, ctx);
#endif
    return;
}

#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
static Status GetServerNewConnMsgMemctx(GmcConnT *conn, PipeBufferT pipeBuf, uint32_t *memCtxId, uint32_t instanceId)
{
    // 接收消息池addr
    Status ret = DbAdptPipeRecv(&conn->pipe, &pipeBuf);
    if (ret != GMERR_OK) {
        // channel 模式中，超出连接数量，从这里报错
        DB_LOG_AND_SET_LASERR(
            GMERR_CONNECTION_FAILURE, "|Connect stage1| |handshake| Clt recv 5th response: %" PRId32 ".", ret);
        return GMERR_CONNECTION_FAILURE;
    }
    conn->connStageTimePoint.recvHandshakeForthRsp = DbRdtsc();
    ShmemPtrT shmPtr = pipeBuf.shmPtr;
    ret = SharedMsgPoolOpen(&conn->msgPoolCtx, shmPtr, instanceId, &conn->msgPoolCtx.vnofd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Connect stage1| open shared msg pool:(%" PRIu32 ",%" PRIu32 "), vnofd: %" PRId32 ".",
            shmPtr.segId, shmPtr.offset, conn->msgPoolCtx.vnofd);
        return ret;
    }
    SharedMsgPoolT *msgPool = (SharedMsgPoolT *)(void *)conn->msgPoolCtx.smpHdl;
    if (msgPool == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "|Connect stage1| msgPool: (%" PRIu32 ",%" PRIu32 ") from Ctx.",
            shmPtr.segId, shmPtr.offset);
        return GMERR_INTERNAL_ERROR;
    }
    *memCtxId = msgPool->memCtxId;
    return GMERR_OK;
}

static Status CreateMsgBufferForConnChannel(GmcConnT *conn, uint32_t shmCtxId)
{
    void *msgCtx = DbOpenShmemCtx(shmCtxId, conn->instanceId);
    if (msgCtx == NULL) {
        DB_LOG_ERROR(
            GMERR_MEMORY_OPERATE_FAILED, "open msgCtx with id:(%" PRIu32 ", %" PRIu32 ").", shmCtxId, conn->instanceId);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    conn->msgCtx = msgCtx;
    conn->msgCtxId = shmCtxId;
    CltFixbuffCreate(conn, msgCtx);
    if (!conn->isLobConn && !conn->isCsMode) {
        FixBufSetAllocCb(&conn->sendPack, SharedMsgPoolGet, SharedMsgPoolReturn, &conn->msgPoolCtx);
        FixBufSetAllocCb(&conn->recvPack, SharedMsgPoolGet, SharedMsgPoolReturn, &conn->msgPoolCtx);
    }
    return GMERR_OK;
}

static Status InitMsgBufferForConnChannel(GmcConnT *conn, PipeBufferT *pipeBuf)
{
    uint32_t newMemCtxId = DB_INVALID_UINT32;
    Status ret = GetServerNewConnMsgMemctx(conn, *pipeBuf, &newMemCtxId, conn->instanceId);
    if (ret != GMERR_OK) {
        return ret;
    }

    return CreateMsgBufferForConnChannel(conn, newMemCtxId);
}

static void FreeMsgBufferForConnChannel(GmcConnT *conn)
{
    // 如果共享内存也可以像动态内存一样通过关闭memCtx释放内存，那么这一段可以删除
    if (!CltConnIsAsyncLob(conn)) {
        conn->recvPack.memCtx = NULL;
    }

    SecureFixBufRelease(&conn->sendPack);
    SecureFixBufRelease(&conn->recvPack);

#ifndef NDEBUG
    if (conn->msgPool.magic != CLT_CONN_MAGIC_WORD || (conn->msgPool.size == 0 && conn->msgPool.next != NULL)) {
        // Client msg pool list size is 0 but list not NULL!
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Cur List node addr:%p.magic code:%u", conn->msgPool.next,
            conn->msgPool.magic);
        DB_ASSERT(false);
    }
#endif

    for (MsgBufferList *list = conn->msgPool.next; list != NULL;) {
        MsgBufferList *item = list;
        list = item->next;
        FixBufferT *buffer = &conn->sendPack;
        FixBufInit(buffer, item, item->size, 0, buffer->flags, buffer->memCtx);
        SecureFixBufRelease(buffer);
    }
    // uninit msg pool
    conn->msgPool = (MsgBufferList){};
#ifndef NDEBUG
    conn->msgPool.magic = CLT_CONN_MAGIC_WORD;
#endif
    // free conn->msgCtx
    DbMemCtxT *msgCtx = conn->msgCtx;
    if (msgCtx != NULL) {
        (void)DbCloseShmemCtx(msgCtx->ctxId, conn->instanceId);
    }
}

static void UninitMsgBufferForConnChannel(GmcConnT *conn)
{
    FreeMsgBufferForConnChannel(conn);
    // close msg pool
    SharedMsgPoolClose(&conn->msgPoolCtx, &conn->msgPoolCtx.vnofd);
}
#else
static Status InitMsgBufferForConnSocket(GmcConnT *conn)
{
    // memCtx用途：客户端连接通信内存
    // 生命周期：连接级别
    // 释放方式：兜底清空
    // 兜底清空措施：断连时，销毁该memctx
    DbMemCtxT *parent = conn->memCtx;
    const char *name = "CONN_PACK";
    DbMemCtxArgsT args = {};
    args.dynCtxNeedRecycle = true;
    args.liteModOn = true;
    DbMemCtxT *msgCtx = DbCreateDynMemCtx(parent, true, name, &args);
    if (msgCtx == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Open msgCtx.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    CltFixbuffCreate(conn, msgCtx);
    return GMERR_OK;
}

static void UninitMsgBufferForConnSocket(GmcConnT *conn)
{
    // free conn->msgCtx
    DbMemCtxT *msgCtx = conn->msgCtx;
    DbDeleteDynMemCtx(msgCtx);
}
#endif

static Status InitMsgBuffer(GmcConnT *conn, PipeBufferT *pipeBuf)
{
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    return InitMsgBufferForConnChannel(conn, pipeBuf);
#else
    return InitMsgBufferForConnSocket(conn);
#endif
}

DbSpinLockT g_cltAttachlock = {0};
bool g_cltAttachlockInException = false;
#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
static Status InitShmemAndMsgBufferInCsMode(GmcConnT *conn, ComInfoT *comInfo, PipeBufferT *pipeBuf)
{
    Status ret = DbShmMgrAttach(RW_PERMISSION);
    if (ret != GMERR_OK) {
        return ret;
    }
    CltLockForAttach();
    ret = DbCommonAttachCltShmemInCsMode(conn->instanceId, conn->msgCtxId, NULL, conn->isCsMode);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Connect stage1| Try attach share memory.");
        CltUnLockForAttach();
        return GMERR_CONNECTION_RESET_BY_PEER;
    }
    ret = InitMsgBuffer(conn, pipeBuf);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Connect stage1| Try init msg buffer.");
        DbCommonDetachCltShmemInCsMode(conn->instanceId, conn->msgCtxId, conn->isCsMode);
        CltUnLockForAttach();
        return ret;
    }
    ret = DbCommonGetCltShmemVersion(conn->instanceId, &conn->shmVersion);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Connect stage1| Try get clt shmem version.");
        DbCommonDetachCltShmemInCsMode(conn->instanceId, conn->msgCtxId, conn->isCsMode);
        CltUnLockForAttach();
        return ret;
    }
    conn->attachRef++;
    DbLogInitLocalClt(conn->instanceId);
    CltUnLockForAttach();
    return GMERR_OK;
}
#endif

typedef union TagStackBuf {
    ComInfoT comInfo;
    ShmemPtrT shmPtr;
    uint8_t bytes[8];
} StackBufT;

static Status ServerSyn(GmcConnT *conn)
{
    StackBufT stackBuf;
    PipeBufferT pipeBuf = {};
    pipeBuf.buf = stackBuf.bytes;
    pipeBuf.totLen = sizeof(ComInfoT);
    Status ret = DbAdptPipeRecv(&conn->pipe, &pipeBuf);
    if (ret != GMERR_OK) {
        // channel 模式中，超出连接数量，从这里报错
        DB_LOG_AND_SET_LASERR(
            GMERR_CONNECTION_FAILURE, "|Connect stage1| |handshake| Clt recv 2nd response: %" PRId32 ".", ret);
        return GMERR_CONNECTION_FAILURE;
    }
    conn->connStageTimePoint.recvSecConnRsp = DbRdtsc();
    if (!DbIsEulerEnv()) {
        // channel 模式中，comInfo用shmPtr表示
        stackBuf.shmPtr = pipeBuf.shmPtr;
    }

    ComInfoT comInfo = stackBuf.comInfo;
    if (comInfo.result == COM_RESULT_CONNECTION_USE_UP) {
        // usocket 模式中，超出连接数量，从这里报错
        DB_LOG_ERROR(GMERR_TOO_MANY_CONNECTIONS, "|Connect stage1|.");
        return GMERR_TOO_MANY_CONNECTIONS;
    } else if (comInfo.result != COM_RESULT_SUCCEED) {
        DB_LOG_ERROR(GMERR_CONNECTION_FAILURE, "|Connect stage1| result: %" PRIu8 ".", comInfo.result);
        return GMERR_CONNECTION_FAILURE;
    }

    // 因为通信两端大都直接写入数据，不处理字节序，目前暂时忽略endian
    DB_UNUSED(comInfo.endian);
    conn->instanceId = comInfo.instanceId;
    conn->msgPoolCtx.vnofd = comInfo.nd;
#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
    if (conn->isCsMode && conn->lctrType != LCTR_TYPE_TCP) {
        return InitShmemAndMsgBufferInCsMode(conn, &comInfo, &pipeBuf);
    }
#endif

    if (conn->lctrType != LCTR_TYPE_TCP) {
        if ((ret = DbCommonTryInitCltShmem(comInfo.instanceId)) != GMERR_OK) {
            DB_LOG_ERROR(ret, "|Connect stage1| Try init shmem.");
            return GMERR_CONNECTION_FAILURE;
        }
    }
    return InitMsgBuffer(conn, &pipeBuf);
}

static void CltSubConsumeSingleLeftoverMsgBeforeDisconnect(GmcConnT *conn, FixBufferT *msg)
{
    const DbSubsEventAckT *ack = SecureFixBufGetData(msg, sizeof(DbSubsEventAckT));
    FixBufferT msgData = {0};
#ifdef SECUREFIXBUF
    DbDataT ctx;
    ctx.ptr = &conn->entryCtx;
    SecureFixBufSetVerifyProc(&msgData, CltFixBufVerify, ctx);
#endif
    if ((ack == NULL) || (!SubsDataGetTxWayShare(ack->subsDataFlag)) ||
        (ack->subsDataSize < SHARE_MSG_HEADER_ALIGN_SIZE) ||
        (SecureFixBufAttachShm(&msgData, ack->subsData, ack->subsDataSize) != GMERR_OK)) {
        return;
    }

    uint32_t ref = SecureFixBufDecRefBuf(&msgData);
    if (ref > 0) {
        return;
    }

    const void *buf = SecureFixBufGetBuf(&msgData);
#ifdef SECUREFIXBUF
    if (SECUREC_UNLIKELY(buf == NULL)) {
        return;
    }
#endif
    const ShareMsgHeaderT *sharedMsgHeader = buf;
    (void)DbShareMsgReleaseListAddNode(conn->subMsgMgr, sharedMsgHeader->shareMsgNodeId);
}

static void CltSubConsumeLeftoverMsgBeforeDisconnect(GmcConnT *conn, FixBufferT *buffer)
{
    const MsgHeaderT *msgTmp = RpcPeekMsgHeader(buffer);
    const OpHeaderT *opTmp = ProtocolPeekFirstOpHeader(buffer);
    if (opTmp->opCode == MSG_OP_RPC_HEARTBEAT) {
        conn->heartbeatInfo.recvCnt++;
        return;
    }
    if (conn->lctrType != LCTR_TYPE_TCP) {
        DbShmEntrySndBufRingFrontDequeue(&conn->entryCtx);
    }
    for (uint32_t count = msgTmp->opNum;;) {
        uint32_t size = opTmp->len - MSG_OP_HEADER_ALIGN_SIZE;
        void *data = SecureFixBufGetData(buffer, size);
        if (data == NULL) {
            break;
        }
        FixBufferT opBufTmp = {};
        FixBufInit(&opBufTmp, data, size, size, 0, NULL);
        DbDataT ctx;
        ctx.ptr = &conn->entryCtx;
        SecureFixBufSetVerifyProc(&opBufTmp, CltFixBufVerify, ctx);
        CltSubConsumeSingleLeftoverMsgBeforeDisconnect(conn, &opBufTmp);
        if (--count == 0) {
            return;
        }
        opTmp = SecureFixBufGetData(buffer, MSG_OP_HEADER_ALIGN_SIZE);
        if (opTmp == NULL) {
            break;
        }
    }
}

static Status CltRecvResponseForDisconnect(GmcConnT *conn, FixBufferT *buffer)
{
    bool needConsume = (conn->connType == GMC_CONN_TYPE_SUB);
    uint32_t expected = conn->serialNumberSend;
    uint32_t distance;

    do {
        Status ret = CltPipeRecv(conn, buffer, false);
        if (ret != GMERR_OK) {
            return ret;
        }
        // 为了正确处理serialNumber发生翻转的情况，这里不比较大小，而是比较距离
        const MsgHeaderT *msg = RpcPeekMsgHeader(buffer);
        CltMonitorCSCommunicationSingleTime(msg, conn);
        uint32_t actual = msg->serialNumber;
        distance = expected - actual;
        if (distance == 0) {
            return GMERR_OK;
        }
        if (needConsume) {
            CltSubConsumeLeftoverMsgBeforeDisconnect(conn, buffer);
        }
    } while (distance <= INT32_MAX);

    return GMERR_OK;
}

static Status ClientFin(GmcConnT *conn)
{
    Status ret = CltAttachAndRefreshConn(conn, NULL, true);
    if (ret != GMERR_OK) {
        // attach failed means server down
        return ret;
    }
    FixBufferT req = {0};
    FixBufferT rsp = {0};
    FixBufInit(&req, NULL, 0, 0, FIX_BUF_FLAG_EXTEND_BUFFER, conn->msgCtx);
    FixBufInit(&rsp, NULL, 0, 0, FIX_BUF_FLAG_EXTEND_BUFFER, conn->msgCtx);
#ifdef SECUREFIXBUF
    DbDataT ctx;
    ctx.ptr = &conn->entryCtx;
    SecureFixBufSetVerifyProc(&req, CltFixBufVerify, ctx);
    SecureFixBufSetVerifyProc(&rsp, CltFixBufVerify, ctx);
#endif
    FixBufSetAllocCb(&req, SharedMsgPoolGet, SharedMsgPoolReturn, &conn->msgPoolCtx);
    FixBufSetAllocCb(&rsp, SharedMsgPoolGet, SharedMsgPoolReturn, &conn->msgPoolCtx);

    ret = RpcReserveMsgOpHeader(&req);
    if (ret != GMERR_OK) {
        goto FINISH;
    }
    MsgHeaderT *msg = RpcPeekMsgHeader(&req);
    OpHeaderT *op = ProtocolPeekFirstOpHeader(&req);
    msg->serviceId = DRT_SERVICE_CONN;
    msg->modelType = MODEL_DUMMY;
    op->opCode = MSG_OP_RPC_DISCONNECT;

    ret = CltSendRequest(conn, &req);
    if (ret != GMERR_OK) {
        goto FINISH;
    }

    if (conn->connType == GMC_CONN_TYPE_ASYNC && conn->recvStatus != RECIVED_ALL) {
        // 在断连时走入该分支，证明已出现异步与断连的并发问题，接收到的也大概率是畸形报文，故不再接收，直接走断连流程即可
        // 场景1：欧拉环境，异步报文未接收完成，此时便进行断连操作
        // 场景2：HPE环境大报文场景，异步大报文未接收完成，此时便进行断连操作
        // 由于该场景属于小概率发生的正常场景，故仅打印WARNING日志
        DB_LOG_WARN(GMERR_DATA_EXCEPTION, "Discard async msg when disconn, recvStatus is %" PRIu32 ".",
            (uint32_t)conn->recvStatus);
        ret = GMERR_DATA_EXCEPTION;
        goto FINISH;
    }

    // 通过接收应答来等待服务器处理完成
    ret = CltRecvResponseForDisconnect(conn, &rsp);

FINISH:
    CltDetachAndReleaseConn(conn, NULL, true);
    return ret;
}

static void CltDisconnectImpl(GmcConnT *conn, uint32_t stepDone)
{
    // undo step 8
    if (stepDone >= 0x8 && conn->childStmtCount > 0) {
        RemoveStmtFromConn(conn, true);
    }

    // undo step 7
    if (stepDone >= 0x7 && conn->connType == GMC_CONN_TYPE_ASYNC) {
        RemoveConnFromAsyncTimeout(conn);
    }

    // undo step 6
    if (stepDone >= 0x6) {
        RemoveConnFromHeartBeat(conn);
    }

    // undo step 5
    if (stepDone >= 0x5) {
        TearDownEvents(conn);
        // 手册已经说明不支持使用订阅连接创建stmt, 但测试用例存在这样的用法，此处用于兜底释放手段
        if (conn->connType == GMC_CONN_TYPE_SUB) {
            RemoveStmtFromConn(conn, false);
        }
    }

    // undo step 4
    if (stepDone >= 0x4) {
        CltDirectClose(conn);
        (void)ClientFin(conn);
    }

    // undo step 2
    if (stepDone >= 0x2) {
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
        if (!conn->isCsMode) {
            UninitMsgBufferForConnChannel(conn);
        }
#else
        UninitMsgBufferForConnSocket(conn);
#endif
    }

    // undo step 1
    if (stepDone >= 0x1) {
        if (conn->connType == GMC_CONN_TYPE_SUB && conn->notifyChan.nfd != DB_INVALID_FD) {
            (void)DbNotifyFdClose(conn->notifyChan.nfd);
            DbCltNotifyChannelClear(&conn->notifyChan);
        }
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
        const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
        if (conn->connType == GMC_CONN_TYPE_SUB && conn->selfEventWakeUp != DB_INVALID_FD) {
            (void)ioCtx->close(conn->selfEventWakeUp);
        }
#endif
        DbPipeClose(&conn->pipe);
    }
}

static void CltRecordConnAbnormalStatistics(const GmcConnT *conn)
{
    if (conn->connDfxInfo.rcvFailCnt != 0 || conn->connDfxInfo.sndFailCnt != 0) {
        DB_LOG_ERROR(GMERR_CONNECTION_EXCEPTION,
            "|CONN| ConnId %" PRIu16 ", rcv unsuc count:%" PRIu32 ", send unsuc count:%" PRIu32 ", need analyze.",
            conn->remoteId, conn->connDfxInfo.rcvFailCnt, conn->connDfxInfo.sndFailCnt);
    }
}

void CltDisconnect(GmcConnT *conn)
{
    conn->magic = CLT_CONN_MAGIC_WORD_AFTER_FREE;
    CltRecordConnAbnormalStatistics(conn);
    CltClearSubsOnSubConn(conn);
    CltDisconnectImpl(conn, UINT32_MAX);
    DbDeleteDynMemCtx(conn->memCtx);
    DbRWSpinWLock(&g_gmdbCltInstance.connLock);
    g_gmdbCltInstance.gConnCount--;
    if (g_gmdbCltInstance.gConnCount == 0) {
        // 清理客户端缓存的cataLabels
        CltCataClearLabelCacheAll();
    }
    DbRWSpinWUnlock(&g_gmdbCltInstance.connLock);
}

#if defined(RTOSV2) || defined(RTOSV2X) || defined(HPE)
static Status InitConnSpecificResource(GmcConnT *conn, const CliConnectResponseT *cfg)
{
    SharedMsgPoolT *msgPool = (SharedMsgPoolT *)(void *)conn->msgPoolCtx.smpHdl;
    if (msgPool == NULL) {
        DB_LOG_ERROR(
            GMERR_INTERNAL_ERROR, "|Connect stage3|msgPool from ctx. Srv remoteId %" PRIu16 ".", conn->remoteId);
        return GMERR_INTERNAL_ERROR;
    }
    // 在设备上，且是订阅连接需要重新切换msg mem ctx
    DbMemCtxT *srcCtx = conn->msgCtx;
    DB_ASSERT(srcCtx != NULL);
    uint32_t srcCtxId = srcCtx->ctxId;
    uint32_t dstCtxId = msgPool->memCtxId;
    if (SECUREC_UNLIKELY(srcCtxId == dstCtxId)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR,
            "|Connect stage3| srcCtxId: %" PRIu32 " == dstCtxId: %" PRIu32 ", srv remoteId: %" PRIu16 ".", srcCtxId,
            dstCtxId, conn->remoteId);
        return GMERR_INTERNAL_ERROR;
    }
    // 释放srcCtx的通信buffer，并关闭srcCtx
    FreeMsgBufferForConnChannel(conn);
    // 使用dstCtx重新初始化通信buffer
    Status ret = CreateMsgBufferForConnChannel(conn, dstCtxId);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (DbIsHpeSimulationEnv()) {
        DbNotifyFdRecvFd(&conn->pipe, &conn->notifyChan.nfd);
        return ret;
    }
    uint32_t id = DbNotifyChanIdGen(cfg->instanceId, cfg->connId);
    if ((ret = DbNotifyFdCreate(id, &conn->notifyChan.nfd)) != GMERR_OK) {
        DB_LOG_ERROR(ret,
            "|Connect stage3| Create notify fd with id %" PRIu32 ", srv instanceId: %" PRIu32 ", srv remoteId: %" PRIu16
            ".",
            id, cfg->instanceId, conn->remoteId);
        return ret;
    }
    if ((ret = DbEventFdCreate(0, &conn->selfEventWakeUp)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Connect stage3| Alloc event fd, srv remoteId: %" PRIu16 ".", conn->remoteId);
    }
    return ret;
}
#endif

static Status CltInitConnFromCfg(GmcConnT *conn, const CliConnectResponseT *cfg)
{
    DmSetAddrMode((SeHpTupleAddrMode)cfg->tupleAddrMode);
    DbSetTcpConnectConfig(cfg->clientConnectByTcp);
    conn->remoteId = cfg->connId;
#ifdef EXPERIMENTAL_NERGC
    conn->sessionId = cfg->sessionId;
    conn->isAuth = true;
#endif
    bool newFlowCtrl = false;
    for (int32_t i = 0; i < (int32_t)GMC_DB_FLOW_CTRL_LEVEL_BUTT - 1; i++) {
        conn->flowControl.flowCtrlSleepTime[i] = cfg->flowCtrlSleepTime[i];
        newFlowCtrl = newFlowCtrl || (conn->flowControl.flowCtrlSleepTime[i] != 0);
    }
    conn->flowControl.newFlowCtrl = newFlowCtrl;
    // 不要求严格并发安全
    if (g_gmdbServerConfig.firstWorkerHungThreshold == 0u) {
        g_gmdbServerConfig.firstWorkerHungThreshold = cfg->firstWorkerHungThreshold;
    }
    if (conn->connType != GMC_CONN_TYPE_SUB || conn->lctrType == LCTR_TYPE_TCP) {
        return GMERR_OK;
    }
    // 初始化订阅连接特有的成员变量
    ShareMsgMgrT *shareMsgMgrAddr = DbShmPtrToAddr(cfg->shareMsgMgrPtr);
    if (shareMsgMgrAddr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED,
            "|Connect stage3| Open shared msg manager (%" PRIu32 ", %" PRIu32 "), srv conn id %" PRIu16 ".",
            cfg->shareMsgMgrPtr.segId, cfg->shareMsgMgrPtr.offset, conn->remoteId);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    conn->subMsgMgr = shareMsgMgrAddr;
    conn->notifyChan.msgQue = DbShmPtrToAddr(cfg->shmNotifyMsgQue);
    conn->notifyChan.ctrlInfo = DbShmPtrToAddr(cfg->shmNotifyChanCtrl);
    if (conn->notifyChan.msgQue == NULL || conn->notifyChan.ctrlInfo == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED,
            "|Connect stage3| open notify msg queue or ctrlInfo, srv conn id %" PRIu16 ", msg queue: (%" PRIu32
            ", %" PRIu32 "), ctrlInfo: (%" PRIu32 ", %" PRIu32 ")",
            conn->remoteId, cfg->shmNotifyMsgQue.segId, cfg->shmNotifyMsgQue.offset, cfg->shmNotifyChanCtrl.segId,
            cfg->shmNotifyChanCtrl.offset);
        return GMERR_MEMORY_OPERATE_FAILED;
    }

#if defined(RTOSV2) || defined(RTOSV2X) || defined(HPE)
    return InitConnSpecificResource(conn, cfg);
#else
    // 光启场景下暂不支持nfd接收(用于状态合并订阅)
#ifndef EXPERIMENTAL_GUANGQI
    return DbNotifyFdRecvFd(&conn->pipe, &conn->notifyChan.nfd);
#else
    return GMERR_OK;
#endif
#endif
}

inline static Status CltGetAgedMgrInfo(GmcConnT *conn, CliConnectResponseT *rsp)
{
    conn->agedMgrLatchShmAddr = rsp->agedMgrLatchShmAddr;
    conn->agedMgrShmTaskMap = DbShmPtrToAddr(rsp->agedMgrShmTaskMapShmAddr);
    if (conn->agedMgrShmTaskMap == NULL) {
        (void)DbCloseShmemCtx(DB_EE_AGED_MGR_CTX_ID, conn->instanceId);
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Get aged mgr taskMap dynamic addr: (%" PRIu32 ", %" PRIu32 ").",
            rsp->agedMgrShmTaskMapShmAddr.segId, rsp->agedMgrShmTaskMapShmAddr.offset);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}
#ifdef EXPERIMENTAL_NERGC
#define CLT_HEARTBEAT_DEFAULT_INTERVAL_S 5
ALWAYS_INLINE static Status CltGetCfgMgrInfo4Tcp(GmcConnT *conn)
{
    size_t size = sizeof(DbCfgItemT) * CLT_CFG_EM_ITEM_NUM;
    DbCfgItemT *cfgShmConfig = (DbCfgItemT *)DbDynMemCtxAlloc(conn->memCtx, size);
    if (cfgShmConfig == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Create config handle for conn %" PRIu16 ".", conn->remoteId);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(cfgShmConfig, size, 0, size);
    cfgShmConfig[CLT_CFG_HEARTBEAT_INTERVAL].value.int32Val = CLT_HEARTBEAT_DEFAULT_INTERVAL_S;
    conn->cfgShmConfig = cfgShmConfig;
    return GMERR_OK;
}
#endif
inline static Status CltGetCfgMgrInfo4Shm(GmcConnT *conn, CliConnectResponseT *rsp)
{
    conn->cfgShmConfig = DbShmPtrToAddr(rsp->cfgShmPtr);
    if (conn->cfgShmConfig == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Get cfg config dynamic addr: (%" PRIu32 ", %" PRIu32 ").",
            rsp->cfgShmPtr.segId, rsp->cfgShmPtr.offset);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

inline static Status CltGetCfgMgrInfo(GmcConnT *conn, CliConnectResponseT *rsp)
{
#ifdef EXPERIMENTAL_NERGC
    if (conn->lctrType == LCTR_TYPE_TCP) {
        return CltGetCfgMgrInfo4Tcp(conn);
    } else {
        return CltGetCfgMgrInfo4Shm(conn, rsp);
    }
#else
    return CltGetCfgMgrInfo4Shm(conn, rsp);
#endif
}

static Status CltGetPubSubInfo(GmcConnT *conn, CliConnectResponseT *rsp)
{
    if (conn->connType != GMC_CONN_TYPE_SYNC) {
        conn->dwPubSubInfo = NULL;
        return GMERR_OK;
    }
    conn->dwPubSubInfo = DbShmPtrToAddr(rsp->dwPubSubInfoPtr);
    if (conn->dwPubSubInfo == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED,
            "|DirectWrite| Get pubSub info addr, (%" PRIu32 ", %" PRIu32 ").", rsp->dwPubSubInfoPtr.segId,
            rsp->dwPubSubInfoPtr.offset);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    // 记录链接的version，便于服务端消费直连写订阅消息时确保消息和链接的一致性，避免计数出错。
    conn->dwPubSubInfo->connVersion = rsp->connVersion;
    return GMERR_OK;
}

static Status CltInitMgrForDirectWrite(GmcConnT *conn, CliConnectResponseT *rsp)
{
    Status ret = CltGetAgedMgrInfo(conn, rsp);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 客户端初始化dfgmt管理器，用于直连场景下的缩容
    ret = DbOpenDfgmtMgr(rsp->dfgmtMgrShmPtr);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 流控等级和订阅ringbuf addr容器
    ret = CltGetPubSubInfo(conn, rsp);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 客户端初始化直连写消息池Mgr
    ret = DirectShareMsgPoolCltOpen(conn->instanceId, rsp->directMsgPoolMgrShmPtr);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

static Status CltInitUserInfoForDirectWrite(GmcConnT *conn, const CliConnectRequestT *requestParams)
{
    conn->dwUserInfo = (UserInfoT *)DbDynMemCtxAlloc(conn->memCtx, sizeof(UserInfoT));
    if (conn->dwUserInfo == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "userInfo for directwrite.");
        return GMERR_OUT_OF_MEMORY;
    }
    uint32_t uid = DbAdptGetuid();
    uint32_t gid = DbAdptGetgid();
    int32_t err = snprintf_s(conn->dwUserInfo->auditUserInfo, DB_AUDIT_USER_INFO, DB_AUDIT_USER_INFO - 1,
        "%" PRIu32 "-%s", uid, requestParams->processName);
    if (SECUREC_UNLIKELY(err < 0)) {
        DbDynMemCtxFree(conn->memCtx, conn->dwUserInfo);
        conn->dwUserInfo = NULL;
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Make auditUserInfo for directwrite.");
        return GMERR_INTERNAL_ERROR;
    }
    Status ret = DbGetUserNameByUid(uid, conn->dwUserInfo->userName, DM_MAX_NAME_LENGTH);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(conn->memCtx, conn->dwUserInfo);
        conn->dwUserInfo = NULL;
        DB_LOG_ERROR(ret, "Get user name for directwrite.");
        return ret;
    }
    ret = DbGetGroupNameByGid(gid, conn->dwUserInfo->groupName, DM_MAX_NAME_LENGTH);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(conn->memCtx, conn->dwUserInfo);
        conn->dwUserInfo = NULL;
        DB_LOG_ERROR(ret, "Get group name for directwrite.");
        return ret;
    }
    ret = DbGetProcessNameByPid(requestParams->pid, conn->dwUserInfo->processName, DM_MAX_NAME_LENGTH);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(conn->memCtx, conn->dwUserInfo);
        conn->dwUserInfo = NULL;
        DB_LOG_ERROR(ret, "Get process name for directwrite.");
        return ret;
    }
    return GMERR_OK;
}

ALWAYS_INLINE Status CltDirectOpen(GmcConnT *conn, CliConnectResponseT *rsp, const CliConnectRequestT *requestParams)
{
    if (conn->isCsMode) {
        conn->openDirectRead = false;
        conn->transMode = GMC_TRANS_USED_IN_CS;
        conn->enableDirectWrite = false;
        return GMERR_OK;
    }
    // bufferpool动态内存场景不支持直连读，强制改为CS模式，按需持久化下bufpool部署共享内存可以使用
    if (DbDynLoadHasFeature(COMPONENT_BUFFER_POOL) && SeBufPoolDeployInDynMem((uint16_t)conn->instanceId)) {
        conn->openDirectRead = false;
        conn->transMode = GMC_TRANS_USED_IN_CS;
    }
    // 默认会初始化直连读句柄
    DrRunCtxT **drRunCtx = (DrRunCtxT **)&conn->directReadCtx;
    Status ret = DirectReadOpen(conn, rsp->sid, rsp->trxSlot, conn->memCtx, drRunCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret,
            "Open direct read. srv instanceId %" PRIu32 ", srv remoteId:%" PRIu16 ", memCtxId %" PRIu32 ".",
            conn->instanceId, conn->remoteId, conn->memCtx->ctxId);
        return ret;
    }

    conn->enableDirectWrite = CltCfgIsOpenInt32(conn->cfgShmConfig[CLT_CFG_DIRECT_WRITE].value.int32Val);
    if (conn->enableDirectWrite) {
        conn->sid = rsp->sid;  // 将response中的sessionID缓存在conn中，供后续申请直连访问资源时使用
        // 获取用户信息，和服务端格式保持一致。供审计日志、异常分支关键信息等场景使用
        // 建连时申请；断连时释放，详见 CltDirectClose()
        ret = CltInitUserInfoForDirectWrite(conn, requestParams);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = CltInitMgrForDirectWrite(conn, rsp);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(conn->memCtx, conn->dwUserInfo);
            conn->dwUserInfo = NULL;
            return ret;
        }
    }
#ifdef FEATURE_HAC
    if (conn->openDirectRead || conn->enableDirectWrite) {
        ret = CltHacMgrCtxInit(conn->instanceId);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Client get HacMgr.");
            return ret;
        }
    }
#endif
    return GMERR_OK;
}

void CltDirectClose(GmcConnT *conn)
{
#if defined(EXPERIMENTAL_NERGC)
    if (DbIsTcp()) {
        return;
    }
#endif
    if (conn->isCsMode) {
        // cs模式下不打开直连读上下文
        return;
    }
    (void)DirectReadClose(conn->memCtx, conn->directReadCtx);
    if (conn->directWriteCtx != NULL) {
        DwCloseDwRunCtx4Conn(conn->memCtx, conn->directWriteCtx);
        conn->directWriteCtx = NULL;
    }
    if (conn->dwUserInfo != NULL) {
        DbDynMemCtxFree(conn->memCtx, conn->dwUserInfo);
        conn->dwUserInfo = NULL;
    }
    if (conn->dwTimeCost != NULL) {
        DbDynMemCtxFree(conn->memCtx, conn->dwTimeCost);
        conn->dwTimeCost = NULL;
    }
}

static inline void InitConnRequesBuffer(FixBufferT *req, FixBufferT *rsp, GmcConnT *conn)
{
    FixBufInit(req, NULL, 0, 0, FIX_BUF_FLAG_EXTEND_BUFFER, conn->msgCtx);
    FixBufInit(rsp, NULL, 0, 0, FIX_BUF_FLAG_EXTEND_BUFFER, conn->msgCtx);
    FixBufSetAllocCb(req, SharedMsgPoolGet, SharedMsgPoolReturn, &conn->msgPoolCtx);
    FixBufSetAllocCb(rsp, SharedMsgPoolGet, SharedMsgPoolReturn, &conn->msgPoolCtx);
}

// 客户端组件化
static Status CltComponentize(uint16_t instanceId, const char *libDir, const bool *needLoad)
{
    return CltInitCmpntResource(instanceId, libDir, needLoad);
}

// 用于检测服务端主动断连
static void SecureFixBufInit(GmcConnT *conn)
{
    DB_POINTER(conn);
    DbDataT ctx;
    ctx.ptr = &conn->entryCtx;
    SecureFixBufSetVerifyProc(&conn->recvPack, CltFixBufVerify, ctx);
    SecureFixBufSetVerifyProc(&conn->sendPack, CltFixBufVerify, ctx);
}

static Status ClientRsmInit(GmcConnT *conn)
{
    if (!CltCfgIsOpenInt32(conn->cfgShmConfig[CLI_CFG_IS_USE_RSM].value.int32Val)) {
        return GMERR_OK;
    }
    // 保留内存涉及在客户端访问存储的接口，需要提前设置instanceId。目前g_gmdbInstanceId没有并发看护，不支持多实例场景。
    DbSetCltInstanceId((uint16_t)conn->instanceId);
    uint32_t blockSize = (uint32_t)conn->cfgShmConfig[CLI_CFG_RSM_BLOCK_MAX_SIZE].value.int32Val;
    DbCfgValueT cfgValue = conn->cfgShmConfig[CLI_CFG_RSM_KEY_RANGE].value;

    uint32_t range[RSM_KEY_RANGE_NUM];
    Status ret = CfgParamThresholdGetValue(
        cfgValue.str, (uint32_t)strlen(cfgValue.str), range, RSM_KEY_RANGE_NUM, RSM_KEY_RANGE_INFO);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get rsm key range value, cfgValue %s.", cfgValue.str);
        return ret;
    }

    ret = DbRsmCommonInitCltRsm(blockSize, range[0], range[1]);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Init client rsmem.");
    }
    return ret;
}

static Status ClientConnStage3(GmcConnT *conn, CliConnectResponseT *rsp, const CliConnectRequestT *requestParams)
{
    Status ret = CltComponentize(rsp->instanceId, rsp->cmpntLibPath, rsp->cmpntLibLoadFlag);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Connect stage3| Client componentize. path: %s, id:%" PRIu32 ", srv remoteId %" PRIu16 ".",
            rsp->cmpntLibPath, rsp->instanceId, conn->remoteId);
        return ret;
    }
    SecureFixBufInit(conn);
    ret = CltGetCfgMgrInfo(conn, rsp);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (conn->lctrType != LCTR_TYPE_TCP) {
        ret = ClientRsmInit(conn);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|Connect stage3| Client rsmem init, srv remoteId %" PRIu16 ".", conn->remoteId);
            return ret;
        }

        ret = CltDirectOpen(conn, rsp, requestParams);
        if (ret != GMERR_OK) {
            CltDirectClose(conn);
            DB_LOG_ERROR(ret, "|Connect stage3| Client direct open, srv remoteId %" PRIu16 ".", conn->remoteId);
            return ret;
        }
    }
    return ret;
}

static Status ClientConnInitByLctrType(GmcConnT *conn, CliConnectResponseT *rsp)
{
    DB_POINTER2(conn, rsp);
    Status ret = GMERR_OK;
    if (conn->lctrType != LCTR_TYPE_TCP) {
        conn->serverModeShm = DbShmPtrToAddr(rsp->serverModeShmPtr);
        if (conn->serverModeShm == NULL) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
                "|Connect stage3| Transform server mode addr. srv | %" PRIu16 "|%" PRIu32 ", %" PRIu32 "|.",
                conn->remoteId, rsp->serverModeShmPtr.segId, rsp->serverModeShmPtr.offset);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        conn->connVersion = ((PrivServerModeT *)conn->serverModeShm)->version;
        // open操作当前不涉及资源的申请，因此后续异常分支不用close
        ret = DbShmEntryOpen(rsp->shmEntry, rsp->shmEntryVersion, &conn->entryCtx);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret,
                "|Connect stage3| ShmEntryOpen with param:(%" PRIu32 ",%" PRIu32 ",%" PRIu32 ",%" PRIu16 ").",
                rsp->shmEntry.segId, rsp->shmEntry.offset, rsp->shmEntryVersion, conn->remoteId);
            return ret;
        }
        // 当前订阅连接服务端必定申请了ringHdl，用于资源回收
        if (conn->connType == GMC_CONN_TYPE_SUB && conn->entryCtx.ringHdl == NULL) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "|Connect stage3| Sub conn verify, srv remoteId %" PRIu16 ".",
                conn->remoteId);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
    } else {
        conn->serverModeShm = NULL;
        conn->connVersion = 0;
        conn->entryCtx.ringHdl = NULL;
        conn->entryCtx.hdl = NULL;
#ifdef EXPERIMENTAL_NERGC
        if (DbIsTcp()) {
            CltUpdateLogCtrl(&rsp->logCtrl);
        }
#endif
    }
    return ret;
}

static Status ClientSyn(GmcConnT *conn, CliConnectRequestT *requestParams)
{
    conn->serialNumberSend = UINT32_MAX;  // 保证建连接请求的序列号为0
    CliConnectResponseT rsp = {};

    // 初始化clientSyn的报文fixBuffer壳，在填报中申请内存
    FixBufferT reqBuf = {0};
    FixBufferT rspBuf = {0};
    InitConnRequesBuffer(&reqBuf, &rspBuf, conn);
    Status ret;
#ifdef EXPERIMENTAL_NERGC
    ret = DbGetCredit(&conn->pipe, requestParams->userName, strlen(requestParams->userName) + 1, requestParams->auth,
        DB_RPC_CONN_AUTH_LEN);
    if (ret != GMERR_OK) {
        FixBufRelease(&reqBuf);
        return ret;
    }
#endif
    // 发送clientSyn报文并接收服务端应答
    ret = CltSendConnRequest(conn, requestParams, &rsp, &reqBuf, &rspBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    FixBufRelease(&rspBuf);

    // 如果是订阅连接需要切换报文的memCtx
    ret = CltInitConnFromCfg(conn, &rsp);
    if (ret != GMERR_OK) {
        return ret;
    }
    conn->rsmCtxPtr = rsp.rsmCtxPtr;

    // socket需要释放发送报文
    if (reqBuf.memCtx->memType == DB_DYNAMIC_MEMORY) {
        FixBufRelease(&reqBuf);
    }

    ret = ClientConnInitByLctrType(conn, &rsp);
    if (ret != GMERR_OK) {
        return ret;
    }

    return ClientConnStage3(conn, &rsp, requestParams);
}

static Status CltConnectInnerPipeConn(DbPipeT *pipe, const DbLctrT *srvLctr, const DbLctrT *cliLctr, uint32_t bufSize,
    DbPipeMemParasT *memParas, bool nonBlock)
{
    Status ret = DbPipeConnect(pipe, srvLctr, cliLctr, bufSize, memParas, nonBlock);
    if (ret != GMERR_OK) {
        // 失败可能为创建管道失败，或者发送握手请求失败，详见接口内实现
        DB_LOG_ERROR(GMERR_CONNECTION_FAILURE,
            "conn exception. possibly 1.create pipe exception; 2.send handshake exception; 3.server unexist. ret: %d.",
            ret);
        return GMERR_CONNECTION_FAILURE;
    }
    return ret;
}

ALWAYS_INLINE
static Status CltConnectInner(GmcConnT *conn, const DbLctrT *locator, GmcConnOptionsT *options, uint32_t *stepDone)
{
    // step 1: pipe connect
    DbPipeMemParasT memParas = {DbDynMemCtxAlloc, DbDynMemCtxFree, conn->memCtx};
    conn->connStageTimePoint.sendHandshakeReq = DbRdtsc();
    Status ret = CltConnectInnerPipeConn(&conn->pipe, locator, NULL, options->msgQueueSize, &memParas, false);
    if (ret != GMERR_OK) {
        // 失败可能为创建管道失败，或者发送握手请求失败，详见接口内实现
        DB_LOG_ERROR(GMERR_CONNECTION_FAILURE, "conn exception. ret: %d.", ret);
        return GMERR_CONNECTION_FAILURE;
    }
    conn->connStageTimePoint.recvHandshakeFirstRsp = DbRdtsc();
    *stepDone = 0x1;  // if set timeout worthless, connection will be automatically closed
    ret = DbPipeSetSendTimeout(&conn->pipe, options->msgWriteTimeoutMs);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_CONNECTION_FAILURE, "Set timeout return %d.", ret);
        return GMERR_CONNECTION_FAILURE;
    }
    ret = DbPipeSetRecvTimeout(&conn->pipe, options->msgReadTimeoutMs);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_CONNECTION_FAILURE, "Set timeout return %d.", ret);
        return GMERR_CONNECTION_FAILURE;
    }
    // step 2: recv first package from server, and init conn->msgCtx
    ret = ServerSyn(conn);
    if (ret != GMERR_OK) {
        return ret;
    }
    *stepDone = 0x2;

    // step 4: send and receive first rpc message, and init direct read handle conn->directCtx
    ret = ClientSyn(conn, &options->requestParams);
    if (ret != GMERR_OK) {
        return ret;
    }
    *stepDone = 0x4;

    // 客户端运行在HPE内部环境，因HPE未支持epoll系列函数，无法完成心跳上报功能
    // step 5: setup epoll and heartbeat. message buffer for heartbeat will be inited here
    ret = SetupEvents(conn);
    if (ret != GMERR_OK) {
        return ret;
    }
    *stepDone = 0x5;

    // 无论任意连接类型都可以单独注册心跳的Epoll
    ret = AddConnToHeartBeat(conn);
    if (ret != GMERR_OK) {
        return ret;
    }
    *stepDone = 0x6;

    // step 6: setup timeout
    if (conn->connType == GMC_CONN_TYPE_ASYNC) {
        ret = AddConnToAsyncTimeout(conn);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;  // connect success, no need to set stepDone here
}

static GmcConnT *CltAllocConnectionBasic(DbMemCtxT *memCtx, const GmcConnOptionsT *options, const DbLctrT *lctr)
{
    // 注意：这个函数任何类型的连接都可能会用到。
    // 请务必不要在这个函数中操作各种连接类型的特有变量，即union中的内容
    // 在用户调用GmcDisconnect接口释放该内存
    GmcConnT *conn = DbDynMemCtxAlloc(memCtx, sizeof(GmcConnT));
    if (conn == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "GmcConnT for conn.");
        return NULL;
    }
    errno_t err = memset_sp(conn, sizeof(GmcConnT), 0, sizeof(GmcConnT));
    if (err != EOK) {
        DbDynMemCtxFree(memCtx, conn);  // 失败场景下内存指针没有传递到外面，并且会返回错误码，不用置空
        return NULL;
    }

    // 基本状态信息
    const CliConnectRequestT *params = &options->requestParams;
    conn->connType = params->clientType;
    // Yang异步请求 或 时序csMode下 为大报文请求
    conn->isLobConn = params->clientType == GMC_CONN_TYPE_ASYNC ? params->isLobConn : options->isCsMode;
    conn->openDirectRead = DbLctrIsTcp(lctr) ? false : options->openDirectRead;
    conn->enableDirectWrite = false;
    conn->directWriteCtx = NULL;
    conn->dwUserInfo = NULL;
    conn->namespaceId = CLT_CATA_AUTO_GENE_INIT_ID + CLT_CATA_RESERVED_RES_COL_POOL_ID + 1;
    conn->asyncLocalTimerFd = DB_INVALID_FD;
    conn->pid = DbAdptGetpid();
    conn->isCsMode = options->isCsMode;
    conn->msgCtxId = DB_INVALID_ID32;
#ifdef EXPERIMENTAL_NERGC
    memset_s(&conn->sessionId, sizeof(conn->sessionId), 0, sizeof(conn->sessionId));
    conn->isAuth = false;
    (void)memcpy_s(conn->userName, MAX_USER_NAME_LEN, params->userName, MAX_USER_NAME_LEN);
#endif
    // stmt列表
    conn->childStmtLimit = options->maxStmtCount;
    DbLinkedListInit(&conn->childStmtList);
    DbLinkedListInit(&conn->stmtIdList);

    // 通信相关
    conn->epollReg = options->epollReg;
    conn->flowControl.flowCtrlNotice = options->flowCtrlNoticeCb;
    conn->flowControl.args = options->flowCtrlNoticeData;
    conn->requestTimeoutUs = options->requestTimeoutUs;
    conn->packShrinkThreshold = options->packShrinkThreshold;
    conn->bigObjectStand = options->bigObjectStand;
    conn->memCtx = memCtx;
    conn->lctrType = lctr->type;  // 通信类型

    // DFX相关
    conn->heartbeatInterval = options->timeInterval;
    conn->dwTimeCost = NULL;
#if (defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)) && !defined(NDEBUG)
    conn->msgPool.magic = CLT_CONN_MAGIC_WORD;
#endif
    return conn;
}

static GmcConnT *CltInitConnectionServerLocator(GmcConnT *conn, const GmcConnOptionsT *options)
{
    // 异步连接需要存储serverLocator用于获取元数据时创建临时同步连接
    size_t size = strlen(options->serverLocator) + 1;
    // 在用户调用GmcDisconnect接口释放该内存
    void *serverLocator = DbDynMemCtxAlloc(conn->memCtx, size);
    if (serverLocator == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc serverLocator.");
        return NULL;
    }
    errno_t err = memcpy_s(serverLocator, size, options->serverLocator, size);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Copy serverLocator return %" PRId32 ".", err);
        DbDynMemCtxFree(conn->memCtx, serverLocator);  // 失败场景下内存指针没有传递到外面，并且会返回错误码，不用置空
        return NULL;
    }
    conn->serverLocator = serverLocator;
    conn->sendMsgQueue.memCtx = conn->memCtx;
    return conn;
}

static GmcConnT *CltInitConnectionAsyncMember(GmcConnT *conn, const GmcConnOptionsT *options)
{
    conn->sendMsgQueue.memCtx = conn->memCtx;
    // 异步连接需要存储serverLocator用于获取元数据时创建临时同步连接
    return CltInitConnectionServerLocator(conn, options);
}

static GmcConnT *CltInitConnectionSubMember(GmcConnT *conn, const GmcConnOptionsT *options)
{
    // 订阅连接需要存储连接名供用户回调使用
    const CliConnectRequestT *params = &options->requestParams;
    size_t size = strlen(params->connName) + 1;
    // 在用户调用GmcDisconnect接口释放该内存
    void *connName = DbDynMemCtxAlloc(conn->memCtx, size);
    if (connName == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc connName.");
        return NULL;
    }
    errno_t err = memcpy_s(connName, size, params->connName, size);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Copy connName return %" PRId32 ".", err);
        DbDynMemCtxFree(conn->memCtx, connName);  // 失败场景下内存指针没有传递到外面，并且会返回错误码，不用置空
        return NULL;
    }
    conn->connName = connName;
    conn->subFailedCb = options->subFailedCb;
    conn->subFailedData = options->subFailedData;
    conn->selfEventWakeUp = DB_INVALID_FD;
    DbCltNotifyChannelClear(&conn->notifyChan);
    return CltInitConnectionServerLocator(conn, options);
}
#if defined(RTOSV2) || defined(RTOSV2X) || defined(HPE)
void CltHeartBeatConnect(GmcConnOptionsT *options, uint32_t instanceId)
{
    DB_UNUSED(options);
    DB_UNUSED(instanceId);
    return;
}
#else

static Status CltCacheHeartBeatServerLocator(GmcConnOptionsT *options, uint32_t instId)
{
    char *serverLocator;
    HeartBeatMgrT *mgr = &g_heartBeatMgr;
    if (mgr->serverLocatorForHeartBeat[instId] == NULL && strlen(options->serverLocator) != 0) {
        size_t size = strlen(options->serverLocator) + 1;
        DbMemCtxT *ctx = g_gmdbCltInstance.memCtx;
        // 正常场景下Uninit阶段会释放
        serverLocator = DbDynMemCtxAlloc(ctx, size);
        if (serverLocator == NULL) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Malloc for locator.");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        errno_t err = memcpy_s(serverLocator, size, options->serverLocator, size);
        if (err != EOK) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Copy for locator.");
            DbDynMemCtxFree(ctx, serverLocator);  // 失败场景下内存指针没有传递到外面，并且会返回错误码，不用置空
            return GMERR_MEMORY_OPERATE_FAILED;
        }

        if (!DbAtomicBoolCASForPtr(
                (uintptr_t *)&(mgr->serverLocatorForHeartBeat[instId]), (uintptr_t)NULL, (uintptr_t)serverLocator)) {
            DbDynMemCtxFree(ctx, serverLocator);
        }
    }
    return GMERR_OK;
}

static void CltHeartBeatConnectInner(GmcConnOptionsT *options, uint32_t instId)
{
    HeartBeatMgrT *mgr = &g_heartBeatMgr;
    DbSpinLock(&mgr->lock);
    if (mgr->heartBeatConn[instId] == NULL && mgr->serverLocatorForHeartBeat[instId] != NULL) {
        GmcConnT *connHeart = NULL;
        // 1.创建connOptions并设置默认值
        GmcConnOptionsT connOptions;
        CltInitConnOptions(&connOptions);
        connOptions.isCsMode = options->isCsMode;
        (void)memcpy_s(
            connOptions.requestParams.userName, MAX_USER_NAME_LEN, options->requestParams.userName, MAX_USER_NAME_LEN);
        // 2.建立同步连接
        Status ret = CltConnect(GMC_CONN_TYPE_SYNC, mgr->serverLocatorForHeartBeat[instId], &connOptions, &connHeart);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Create conn for heart beat.");
            DbSpinUnlock(&mgr->lock);
            return;
        }
        if (!DbAtomicBoolCASForPtr((uintptr_t *)&mgr->heartBeatConn[instId], (uintptr_t)NULL, (uintptr_t)connHeart)) {
            CltDisconnect(connHeart);
        } else {
            mgr->heartBeatConnNum++;
        }
    }
    DbSpinUnlock(&mgr->lock);
}

void CltHeartBeatConnect(GmcConnOptionsT *options, uint32_t instanceId)
{
    if (!g_cltSameProcessOpen) {
        return;
    }

    uint32_t instId = DbGetIdxByInstanceId(instanceId);
    // 没有注册心跳的情况下不建立心跳链接，防止工具占用连接数
    if (CltIsHeartBeatReg() == false) {
        return;
    }
    if (CltCacheHeartBeatServerLocator(options, instId) != GMERR_OK) {
        return;
    }
    CltHeartBeatConnectInner(options, instId);
}
#endif

static GmcConnT *CltAllocConnection(DbMemCtxT *memCtx, GmcConnOptionsT *options, const DbLctrT *lctr)
{
    const CliConnectRequestT *params = &options->requestParams;
    GmcConnT *conn = CltAllocConnectionBasic(memCtx, options, lctr);
    if (conn == NULL) {
        return NULL;
    }
    options->requestParams.srvMemCtxLimit = options->srvMemCtxLimit;
    // 部分订阅连接特有的属性需要在下面初始化
    conn->singleCallbackTimeoutMs = options->singleCallbackTimeoutMs;
    conn->avgCallbackTimeoutMs = options->avgcallbackTimeoutMs;
    if (params->clientType == GMC_CONN_TYPE_SYNC) {
        conn->cltOpSegTime.timeThreshold = options->timeThreshold;
        options->requestParams.timeoutMs = options->msgReadTimeoutMs;  // 如果是同步链接，超时时间选择同步的超时时间阈值
        return CltInitConnectionServerLocator(conn, options);
    }
    if (params->clientType == GMC_CONN_TYPE_ASYNC) {
        conn->asyncTimeoutMs = options->asyncTimeoutMs;
        options->requestParams.timeoutMs = options->asyncTimeoutMs;  // 如果是异步链接，超时时间选择异步的超时时间阈值
        conn->isLocalAsyncTimeout = options->isLocalAsyncTimeout;
        return CltInitConnectionAsyncMember(conn, options);
    }
    DB_ASSERT(params->clientType == GMC_CONN_TYPE_SUB);
    return CltInitConnectionSubMember(conn, options);
}

inline static uint64_t CalConnStageTimeUsed(uint64_t endCycle, uint64_t beginCycle)
{
    if (endCycle == 0 || beginCycle == 0 || endCycle < beginCycle) {
        return 0;
    }
    return DbToMseconds(endCycle - beginCycle);
}

// 建连各阶段耗时如果超过 timeThresholdMs 则记录日志
void CheckConnStageTimeUsed(GmcConnT *conn, uint64_t timeThresholdMs)
{
    CltConnStageTimePointT timePoint = conn->connStageTimePoint;
    // [发送握手请求, 收到第一个握手应答]
    uint64_t timeUsed1 = CalConnStageTimeUsed(timePoint.recvHandshakeFirstRsp, timePoint.sendHandshakeReq);
    // [收到第一个握手应答, 收到第二个握手应答]
    uint64_t timeUsed2 = CalConnStageTimeUsed(timePoint.recvHandshakeSecRsp, timePoint.recvHandshakeFirstRsp);
    // [收到第二个握手应答, 收到第三个握手应答]
    uint64_t timeUsed3 = CalConnStageTimeUsed(timePoint.recvHandshakeThirdRsp, timePoint.recvHandshakeSecRsp);
    // [收到第三个握手应答, 收到第四个握手应答]
    uint64_t timeUsed4 = CalConnStageTimeUsed(timePoint.recvHandshakeForthRsp, timePoint.recvHandshakeThirdRsp);
    // [收到第四个握手应答, 发送第二个建连请求]
    uint64_t timeUsed5 = CalConnStageTimeUsed(timePoint.sendSecConnReq, timePoint.recvHandshakeForthRsp);
    // [发送第二个建连请求, 收到第二个建连请求的应答]
    uint64_t timeUsed6 = CalConnStageTimeUsed(timePoint.recvSecConnRsp, timePoint.sendSecConnReq);
    if (timeUsed1 > timeThresholdMs || timeUsed2 > timeThresholdMs || timeUsed3 > timeThresholdMs ||
        timeUsed4 > timeThresholdMs || timeUsed5 > timeThresholdMs || timeUsed6 > timeThresholdMs) {
        uint32_t pid = DbAdptGetpid();
        char pName[DB_MAX_PROC_NAME_LEN] = {0};
        (void)DbGetProcessNameByPid(pid, pName, DB_MAX_PROC_NAME_LEN);
        DB_LOG_WARN(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
            "Attention: dev status for establish conn's perf may deteriorate. "
            "client:(%" PRIu32 "-%s), srv conn id "
            "%" PRIu16 ", time "
            "used(ms):(%" PRIu64 ",%" PRIu64 ",%" PRIu64 ",%" PRIu64 ",%" PRIu64 ",%" PRIu64 ").",
            pid, pName, conn->remoteId, timeUsed1, timeUsed2, timeUsed3, timeUsed4, timeUsed5, timeUsed6);
    }
    conn->connStageTimePoint = (CltConnStageTimePointT){0};
}

static Status CltConnectImpl(DbMemCtxT *memCtx, const DbLctrT *lctr, GmcConnOptionsT *options, GmcConnT **rconn)
{
    static_assert(sizeof(GmcConnT) <= DB_MAX_CONN_BYTES, "Conn handle too big.");
    GmcConnT *conn = CltAllocConnection(memCtx, options, lctr);
    if (conn == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc connection.");
        return GMERR_OUT_OF_MEMORY;
    }

    for (uint32_t retry = 3;;) {
        uint32_t stepDone = 0;
        Status ret = CltConnectInner(conn, lctr, options, &stepDone);
        CheckConnStageTimeUsed(conn, MSECONDS_IN_SECOND);
        if (ret == GMERR_OK) {
            *rconn = conn;
            return ret;
        }
        CltDisconnectImpl(conn, stepDone);
        if (stepDone >= 0x4) {
            CltDetachAndReleaseConn(conn, NULL, true);
            // 和服务器建立连接成功，但后续操作失败，此时没必要再重试
            return ret;
        }
        if (stepDone >= 0x2) {
            CltDetachAndReleaseConn(conn, NULL, true);
        }
        if (--retry == 0 || ret == GMERR_INSUFFICIENT_PRIVILEGE) {
            // 没有足够的重试机会或者权限验证失败，不再重试
            return ret;
        }
        DB_LOG_INFO(
            "Try connect. remaining try count: %" PRIu32 ", previous worthless step: %" PRIu32 ".", retry, stepDone);
        const uint32_t sleep = 50 * DB_RDTSC_SECOND_TO_MSECOND_TO_USECOND;
        DbUsleep(sleep);
    }
}

Status CltConnect(GmcConnTypeE connType, const char *serverLocator, GmcConnOptionsT *connOptions, GmcConnT **conn)
{
    DbLctrT locator;
    Status ret = DbLctrParse(&locator, serverLocator);
    if (ret != GMERR_OK) {
        DB_SET_LASTERR(ret, "Parse str to DbLctrT .");
        return ret;
    }
    DbSetIsTcp(locator.type == LCTR_TYPE_TCP);
    CliConnectRequestT *params = &connOptions->requestParams;
    params->clientType = connType;
    params->pid = DbAdptGetpid();
    params->cltUnique = g_gmdbCltInstance.cltUnique;
    ret = DbGetProcessNameByPid(params->pid, params->processName, sizeof(params->processName));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Get process name by pid, db ret no: %" PRId32 ", os ret no: %" PRId32 ".",
            ret, (int32_t)DbAptGetErrno());
        return GMERR_INTERNAL_ERROR;
    }

    params->tid = DbThreadGetSelfId();
    ret = DbThreadGetName(pthread_self(), params->threadName, DB_THREAD_NAME_MAX_LEN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get thread name when build request.");
        return ret;
    }

    // memCtx用途：客户端连接内存
    // 生命周期：连接级别
    // 释放方式：就近释放
    // 兜底清空措施：断连时，销毁该memctx
    DbMemCtxArgsT args = {};
    args.maxTotalPhySize = connOptions->connMemCtxLimit;
    args.liteModOn = true;
    DbMemCtxT *memCtx = DbCreateDynMemCtx(g_gmdbCltInstance.memCtx, true, "GmcConnT", &args);
    if (memCtx == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Connection alloc.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    ret = CltConnectImpl(memCtx, &locator, connOptions, conn);
    if (ret != GMERR_OK) {
        DbDeleteDynMemCtx(memCtx);
        return ret;
    }
    (*conn)->magic = CLT_CONN_MAGIC_WORD;
    DB_LOG_DEBUG("Connect-connType:%d, connId:%u, ctxName:%s, ctxId:%u.", (int32_t)connType, (*conn)->remoteId,
        (*conn)->msgCtx->ctxName, (*conn)->msgCtx->ctxId);
    CltDetachAndReleaseConn(*conn, NULL, true);
    DbRWSpinWLock(&g_gmdbCltInstance.connLock);
    g_gmdbCltInstance.gConnCount++;
    DbRWSpinWUnlock(&g_gmdbCltInstance.connLock);
    return GMERR_OK;
}

void RefreshTimeCostData(CltTimeCostT *timeCons, uint64_t stopCycles)
{
    // 更新pos和updateTime
    uint64_t stopSec = DbNowSec();
    uint64_t periodTime = stopSec / CLT_EXECUTE_TIME_STAT_PERIOD;  // 5分钟

    periodTime = periodTime * CLT_EXECUTE_TIME_STAT_PERIOD;
    timeCons->updateTime = stopCycles;
    timeCons->arrayPos = timeCons->arrayPos + 1;
    timeCons->arrayPos = timeCons->arrayPos % CLT_EXECUTE_TIME_STAT_LENGTH;
    timeCons->timeConsDetails.periodTime = periodTime;
    timeCons->timeConsDetails.totalTime = 0;
    timeCons->timeConsDetails.insertTimes = 0;
    timeCons->timeConsDetails.maxTime = 0;
    timeCons->timeConsDetails.maxStartTime = 0;
}

void CltSetTimeCost(GmcConnT *conn, uint64_t startCycles, uint64_t stopCycles)
{
    CltTimeCostT *timeCons = &(conn->timeCost);
    uint64_t deltaTime = DbToUseconds(stopCycles - startCycles);

    if (DbToSeconds(stopCycles - timeCons->updateTime) > CLT_EXECUTE_TIME_STAT_PERIOD) {
        RefreshTimeCostData(timeCons, stopCycles);
    }
    // 更新pos对应的数据
    timeCons->timeConsDetails.totalTime = timeCons->timeConsDetails.totalTime + deltaTime;
    timeCons->timeConsDetails.insertTimes = timeCons->timeConsDetails.insertTimes + 1;
    // 这里刷新峰值，待完善
    if (deltaTime > timeCons->timeConsDetails.maxTime) {
        timeCons->timeConsDetails.maxTime = deltaTime;
        timeCons->timeConsDetails.maxStartTime = startCycles;
    }
}

void CltResetOpTimeCostLogVal(GmcConnT *conn)
{
    conn->cltOpSegTime.opStartTime = DbGlobalRdtsc();
    conn->cltOpSegTime.sendMsgStartTime = 0;  // 用来判断是不是直连读
}

static void CltRefreshBuffer(GmcConnT *conn, GmcStmtT *stmt, DbMemCtxT *msgCtx, bool refreshBuf)
{
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    if (msgCtx == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "|Refresh Buffer| memCtx in conn buf.");
        return;
    }
    conn->msgCtx = msgCtx;
#endif
}

static Status CltRefreshMsgPool(GmcConnT *conn)
{
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    conn->msgPoolCtx.smpHdl = DbShmPtrToAddr(conn->msgPoolCtx.shmemPtr);
    if (conn->msgPoolCtx.smpHdl == NULL) {
        return GMERR_INTERNAL_ERROR;
    }
#endif
    return GMERR_OK;
}

static void CltAttachAndRefreshConnDetachUnlock(GmcConnT *conn)
{
    DbCommonDetachCltShmemInCsMode(conn->instanceId, conn->msgCtxId, conn->isCsMode);
    CltUnLockForAttach();
}

Status CltAttachAndRefreshConn(GmcConnT *conn, GmcStmtT *stmt, bool refreshBuf)
{
#if defined(EXPERIMENTAL_NERGC)
    if (DbIsTcp()) {
        return GMERR_OK;
    }
#endif
    if (SECUREC_LIKELY(!conn->isCsMode)) {
        return GMERR_OK;
    }

    CltLockForAttach();

    if (DbCommonCheckCltShmemVersion(conn->instanceId, conn->shmVersion) == false) {
        CltUnLockForAttach();
        return GMERR_CONNECTION_RESET_BY_PEER;
    }
    DbMemCtxT *msgCtx = NULL;
    Status ret = DbCommonAttachCltShmemInCsMode(conn->instanceId, conn->msgCtxId, (void **)&msgCtx, conn->isCsMode);
    if (ret != GMERR_OK) {
        DB_LOG_WARN(ret, "|Attach Conn| Attach share memory.");
        CltUnLockForAttach();
        return GMERR_CONNECTION_RESET_BY_PEER;
    }
    if (conn->attachRef == 0) {
        ret = CltRefreshShmEntry(conn);
        if (ret != GMERR_OK) {
            DB_LOG_WARN(ret, "|Refresh Conn| Refresh shmEntry.");
            CltAttachAndRefreshConnDetachUnlock(conn);
            return GMERR_CONNECTION_RESET_BY_PEER;
        }
        ret = CltRefreshMsgPool(conn);
        if (ret != GMERR_OK) {
            DB_LOG_WARN(ret, "|Refresh Conn| Refresh MsgPool.");
            CltAttachAndRefreshConnDetachUnlock(conn);
            return GMERR_CONNECTION_RESET_BY_PEER;
        }
        CltRefreshBuffer(conn, stmt, msgCtx, refreshBuf);
    }
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    if (refreshBuf) {
        FixBufInit(&conn->sendPack, NULL, 0, 0, FIX_BUF_FLAG_LOB_BUFFER, conn->msgCtx);
        FixBufInit(&conn->recvPack, NULL, 0, 0, FIX_BUF_FLAG_LOB_BUFFER, conn->msgCtx);
        if (stmt != NULL) {
            FixBufInit(&stmt->recvPack, NULL, 0, 0, FIX_BUF_FLAG_LOB_BUFFER, conn->msgCtx);
        }
    }
#endif
    conn->attachRef++;
    CltUnLockForAttach();

    return GMERR_OK;
}

static void CltReleaseBuffer(GmcConnT *conn, GmcStmtT *stmt, bool refreshBuf)
{
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    conn->msgCtx = NULL;
#endif
}

void CltDetachAndReleaseConn(GmcConnT *conn, GmcStmtT *stmt, bool refreshBuf)
{
#if defined(EXPERIMENTAL_NERGC)
    if (DbIsTcp()) {
        return;
    }
#endif
    if (SECUREC_LIKELY(!conn->isCsMode)) {
        return;
    }
    CltLockForAttach();
    conn->attachRef--;
    if (conn->attachRef == 0) {
        conn->entryCtx.hdl = NULL;
        conn->msgPoolCtx.smpHdl = NULL;
        CltReleaseBuffer(conn, stmt, refreshBuf);
    }
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    if (refreshBuf) {
        // sendPack内存应该由服务端控制，客户端负责处理recvPack内存
        FixBufRelease(&conn->recvPack);
        if (stmt != NULL) {
            FixBufRelease(&stmt->recvPack);
        }
    }
#endif
    DbCommonDetachCltShmemInCsMode(conn->instanceId, conn->msgCtxId, conn->isCsMode);
    CltUnLockForAttach();
}

#ifdef FEATURE_STREAM
Status CltGetConnAttr(const GmcConnT *conn, GmcConnAttrTypeE attr, void *value, uint32_t valueSize)
{
    switch (attr) {
        case GMC_CONN_ATTR_INSTANCE_ID:
            *(uint32_t *)value = conn->instanceId;
            break;
        default:
            DB_ASSERT(false);
            break;
    }
    return GMERR_OK;
}
#endif
