/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: clt_async_conn.c
 * Description: Implement of GMDB client for async event handler.
 * Author: zhangyong
 * Create: 2021-05-13
 */
#include "clt_async_conn.h"
#include <sys/epoll.h>
#include <sys/timerfd.h>
#include <unistd.h>
#include "clt_async_timeout.h"
#include "clt_heartbeat.h"
#include "clt_batch.h"
#include "clt_msg.h"
#include "clt_resource.h"
#include "clt_resource_pool.h"
#include "clt_sub_conn.h"
#include "clt_yang_common.h"
#include "clt_yang_errorpath.h"
#include "adpt_process_id.h"
#include "adpt_io.h"

DB_THREAD_LOCAL bool g_isInCallback = false;
typedef struct TimeoutEventCallbackArg {
    GmcConnT *conn;
    TimeoutAuditInfoT auditInfo;
} TimeoutEventCallbackArgT;

typedef void *EventCallbackArg;
typedef void (*EventCallback)(int32_t fd, EventCallbackArg arg);

typedef enum EventType { ASYNC_EVENT, SUB_EVENT, SUB_NOTI_EVENT, SUB_SELF_AWAKE, ASYNC_LOCAL_TIMEOUT_EVENT } EventTypeE;
typedef struct {
    EventCallback readableCb;  // fd可读时回调函数
    EventCallback writableCb;  // fd可写时回调函数，仅连接fd有效
    EventCallbackArg arg;      // 参数
    DbSpinLockT lock;          // 操作EventContextT需要加锁保护
    EventTypeE eventType;
    int32_t fd;
} EventContextT;

// 业务消息处理epoll对象，当epoll对象由客户端创建时，这个变量才有意义；由用户创建时，epoll注册函数放在GmcConnOptionsT中。
// 用途：存储客户端进程全局EventId，用以客户端自己启动Epoll线程（仅测试使用）
// 是否并发初始化：是
// 是否并发读写：否
// 并发方案：为防止测试用例进行重入，暂不进行防重入设计（仅测试使用）
GMDB_EXPORT int32_t g_gmdbCltEventEpollFd = DB_INVALID_FD;

// fd事件上下文和锁
// 用途：存储客户端Fd事件的Map
// 是否并发初始化：否
// 是否并发读写：是
// 并发方案：通过Map的锁进行并发控制
static DbOamapT g_gmdbCltEventCtxs;
static DbRWSpinLockT g_gmdbCltEventCtxsRwLock;

static Status CreateTimeoutEventCallbackArg(TimeoutEventCallbackArgT **eventCallback, DbMemCtxT *memCtx, GmcConnT *conn)
{
    TimeoutEventCallbackArgT *arg =
        (TimeoutEventCallbackArgT *)DbDynMemCtxAlloc(memCtx, sizeof(TimeoutEventCallbackArgT));
    if (arg == NULL) {
        // GMERR_OUT_OF_MEMORY, "Alloc TimeoutEventCallbackArgT memory worthless."
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "TimeoutEventCallbackArgT");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(arg, sizeof(TimeoutEventCallbackArgT), 0, sizeof(TimeoutEventCallbackArgT));
    arg->conn = conn;
    InitTimeoutAuditInfo(&arg->auditInfo);

    *eventCallback = arg;
    return GMERR_OK;
}

Status InitEventContext(void)
{
    g_gmdbCltEventEpollFd = DB_INVALID_FD;
    DbRWSpinInit(&g_gmdbCltEventCtxsRwLock);
    DbOamapT *map = &g_gmdbCltEventCtxs;
    DbMemCtxT *memCtx = g_gmdbCltInstance.memCtx;
    // 生命周期与DB客户端生命周期相同，跟随客户端根memCtx一同销毁
    return DbOamapInit(map, 0, DbOamapUint32Compare, memCtx, true);
}

void FreeSubStmt(EventContextT *ctx)
{
    SubEvtCallBackArgs *cbArg = ctx->arg;
    if (cbArg != NULL) {
        GmcStmtT *stmt = cbArg->stmt;
        if (stmt != NULL) {
            FreeStmt(stmt);
            cbArg->stmt = NULL;
        }
    }
}

void DelEpollConnFd(EpollRegT *epollReg, const GmcConnT *conn, int32_t fd)
{
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    if (epollReg->epollRegType == EPOLLREG) {
        GmcEpollRegT epollRegTmp = epollReg->epollRegFunc;
        if (epollRegTmp(ioCtx->getRelFd(fd), GMC_EPOLL_DEL) != 0) {
            // GMERR_EPOLL_OPERATE_FAILED, "Suffer a defeat to unregister fd %d from epoll when remove event.", fd
            DB_LOG_ERROR(GMERR_EPOLL_OPERATE_FAILED, "Unregister fd %d from epoll", fd);
        }
    } else if (epollReg->epollRegType == EPOLLREG_WITH_USD) {
        GmcEpollRegWithUserDataT epollRegTmp = epollReg->epollRegWithUsDFunc;
        if (epollRegTmp(ioCtx->getRelFd(fd), GMC_EPOLL_DEL, 0, epollReg->userData) != 0) {
            // GMERR_EPOLL_OPERATE_FAILED, "Suffer a defeat to unregister fd %d from epoll when remove event.", fd
            DB_LOG_ERROR(GMERR_EPOLL_OPERATE_FAILED, "Unregister fd %d from epoll", fd);
        }
    } else if (epollReg->epollRegType == EPOLLREG_WITH_CONN) {
        GmcEpollRegWithConnT epollRegTmp = epollReg->epollRegWithConnFunc;
        if (epollRegTmp(ioCtx->getRelFd(fd), GMC_EPOLL_DEL, 0, conn, epollReg->userData) != 0) {
            // GMERR_EPOLL_OPERATE_FAILED, "Suffer a defeat to unregister fd %d from epoll when remove event.", fd
            DB_LOG_ERROR(GMERR_EPOLL_OPERATE_FAILED, "Unregister fd %d from epoll", fd);
        }
    } else {
        // GMERR_DATA_EXCEPTION, "Epollreg type mistake."
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Epollreg type.");
    }
}

void RemoveEventContextForSubConn(EventContextT *eventCtx)
{
    SubEvtCallBackArgs *cbArg = eventCtx->arg;
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    GmcConnT *conn = (GmcConnT *)cbArg->conn;
    if (conn == NULL) {
        FreeSubStmt(eventCtx);
        return;
    }
    EpollRegT *epollReg = (EpollRegT *)&conn->epollReg;
    if (epollReg == NULL) {
        goto E0;
    }

    DelEpollConnFd(epollReg, conn, eventCtx->fd);
    DbOamapT *map = &g_gmdbCltEventCtxs;
    // 处理SUB_NOTI_EVENT连接资源
    if (conn->notifyChan.nfd != DB_INVALID_FD) {
        DelEpollConnFd(epollReg, conn, conn->notifyChan.nfd);
        int32_t fd = ioCtx->getRelFd(conn->notifyChan.nfd);
        EventContextT *ctx = DbOamapRemove(map, (uint32_t)fd, &fd);

        DbDynMemCtxFree(map->memCtx, ctx);
    }

#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    if (conn->selfEventWakeUp != DB_INVALID_FD) {
        DelEpollConnFd(epollReg, conn, conn->selfEventWakeUp);
        int32_t fd = ioCtx->getRelFd(conn->selfEventWakeUp);
        EventContextT *ctx = DbOamapRemove(map, (uint32_t)fd, &fd);

        DbDynMemCtxFree(map->memCtx, ctx);
    }
#endif

E0:
    FreeSubStmt(eventCtx);
    DbDynMemCtxFree(conn->memCtx, eventCtx->arg);
}

void RemoveEventContextForAsyncConn(EventContextT *eventCtx)
{
    GmcConnT *conn = (GmcConnT *)eventCtx->arg;

    if (conn == NULL) {
        return;
    }
    EpollRegT *epollReg = (EpollRegT *)&conn->epollReg;
    if (epollReg != NULL) {
        DelEpollConnFd(epollReg, conn, eventCtx->fd);
    }
}

void RemoveEventContextForAsyncTimeoutConn(EventContextT *eventCtx)
{
    TimeoutEventCallbackArgT *arg = (TimeoutEventCallbackArgT *)eventCtx->arg;
    if (arg == NULL) {
        return;
    }

    GmcConnT *conn = arg->conn;
    EpollRegT *epollReg = (EpollRegT *)&conn->epollReg;
    if (epollReg != NULL) {
        DelEpollConnFd(epollReg, conn, eventCtx->fd);
    }

    DbDynMemCtxFree(conn->memCtx, arg);
}

void UnInitEventContext(void)
{
    DbOamapT *map = &g_gmdbCltEventCtxs;
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    uint32_t iter = 0;
    void *key, *value;
    DbRWSpinWLock(&g_gmdbCltEventCtxsRwLock);
    while (DbOamapFetch(map, &iter, &key, &value) == GMERR_OK) {
        EventContextT *eventCtx = value;
        if (eventCtx == NULL) {
            continue;
        }
        if (eventCtx->eventType == ASYNC_EVENT) {
            RemoveEventContextForAsyncConn(eventCtx);
        } else if (eventCtx->eventType == SUB_EVENT) {
            RemoveEventContextForSubConn(eventCtx);
        } else if (eventCtx->eventType == SUB_NOTI_EVENT) {
            // 订阅连接资源释放在SUB_EVENT中统一处理
            continue;
        } else if (eventCtx->eventType == SUB_SELF_AWAKE) {
            continue;
        } else if (eventCtx->eventType == ASYNC_LOCAL_TIMEOUT_EVENT) {
            RemoveEventContextForAsyncTimeoutConn(eventCtx);
        }

        // 从map中remove掉eventCtx，CltHandleEvent线程便无法获取该eventCtx.
        (void)DbOamapRemove(map, (uint32_t)ioCtx->getRelFd(eventCtx->fd), &eventCtx->fd);
        DbDynMemCtxFree(map->memCtx, eventCtx);  // 已经从map移除，后续不会再使用到
    }
    DbRWSpinWUnlock(&g_gmdbCltEventCtxsRwLock);
}

#ifdef FEATURE_YANG
Status CltFillErrInfo(FixBufferT *response, char **(*getErrMemAddr)(void))
{
    TextT testError;
    Status ret = SecureFixBufGetText(response, &testError);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "get testError.");
        return GMERR_DATA_EXCEPTION;
    }
    char **errorInfoAddr = getErrMemAddr();
    // 如果addr不为空，且不等于异常提示的全局信息，则进行内存释放
    if (*errorInfoAddr != NULL && *errorInfoAddr != DbGetErrorScenceErrorPath()) {
        DbFree(*errorInfoAddr);
        *errorInfoAddr = NULL;
    }
    // 当用户调用GmcYangFreeErrorPathInfo接口，释放该内存。后续可以考虑是否基于连接memCtx进行申请内存
    *errorInfoAddr = DbMalloc(testError.len);
    if (*errorInfoAddr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "blunder info");
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t errRet = strcpy_s(*errorInfoAddr, testError.len, testError.str);
    if (errRet != EOK) {
        DbFree(*errorInfoAddr);
        *errorInfoAddr = NULL;
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}
#endif

void CltSetErrorPathFromRsp(FixBufferT response)
{
    // 增加是否有ErrorPath的检查 hasErrorPath | ErrorPath Status | Error Msg | Error Path
    int32_t hasErrorPath = 0;
    Status ret = SecureFixBufGetInt32(&response, &hasErrorPath);
    if (ret != GMERR_OK) {
        // GMERR_DATA_EXCEPTION, "Unable to get hasErrorPath from response."
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "get hasErrorPath.");
        return;
    }
#ifdef FEATURE_YANG
    // 该出错场景无errorpath
    if (hasErrorPath != 1) {
        return;
    }
    // 函数走到该处，则说明本次一定有带errorpath返回，则将上次的errorpath清空，防止产生误导
    DbFreeErrorPath();

    uint32_t errorCode = 0;
    ret = SecureFixBufGetUint32(&response, &errorCode);
    if (ret != GMERR_OK) {
        // "Unable to get blunderCode from response."
        DB_LOG_AND_SET_LASERR(ret, "get blunderCode.");
        return;
    }
    uint32_t *code = DbGetErrorCode();
    *code = errorCode;

    // 解析并设置errorClauseIndex，当一个节点上存在多条语句时，指定出错的是（同类型的）第几条，从0开始计数
    uint32_t errorClauseIndex = 0;
    ret = SecureFixBufGetUint32(&response, &errorClauseIndex);
    if (ret != GMERR_OK) {
        // "Unable to get clauseIndex from response."
        DB_LOG_AND_SET_LASERR(ret, "get clauseIndex.");
        return;
    }
    uint32_t *clauseIndex = DbGetErrorClauseIndex();
    *clauseIndex = errorClauseIndex;

    ret = CltFillErrInfo(&response, DbGetErrorPathMsgInfo);
    if (ret != GMERR_OK) {
        goto FAIL0;
    }
    ret = CltFillErrInfo(&response, DbGetErrorPathInfo);
    if (ret != GMERR_OK) {
        goto FAIL1;
    }
    return;
FAIL1:
    // 如果pathStr的CltFillErrInfo执行失败，则说明失败原因为
    // buff中取信息|申请内存|copy信息，清空pathStr内容，提示用户本次有errorpath产生，但是生成时失败
    DbSetErrorSenceToErrorpathStr();
FAIL0:
    // 如果pathMsg的CltFillErrInfo执行失败，则说明失败原因为
    // buff中取信息|申请内存|copy信息，清空pathMsg内容，提示用户本次有errorpath产生，但是生成时失败,上面的path信息是本次成功生成的，正常返回给用户
    DbSetErrorSenceToErrorpathMsg();
    return;
#endif
}

static void CltAllocErrorPath(GmcConnT *conn, FixBufferT response)
{
    const MsgHeaderT *msgHdr = RpcPeekMsgHeader(&response);
    uint32_t offset = msgHdr->lastErrorPos;
    bool offsetInvalid = (offset != SIZE_ALIGN4(offset) || offset > FixBufGetPos(&response));
    if (offsetInvalid) {
        // GMERR_DATA_EXCEPTION, "Unable to get last mistake from response."
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Get last mistake.");
        return;
    }

    TextT text;
    FixBufSeek(&response, offset);
    Status ret = SecureFixBufGetText(&response, &text);
    if (ret != GMERR_OK) {
        // "Unable to get last mistake from response."
        DB_LOG_AND_SET_LASERR(ret, "Get last mistake.");
        return;
    }
    CltSetErrorPathFromRsp(response);
    return;
}

void CallbackExecTimeStatistic(uint64_t costTime, GmcConnT *conn)
{
    uint64_t cbCostTotalTime;
    if (conn->timeCost.userCbTimeCostDetails.userCbExecCnt == 0) {
        cbCostTotalTime = costTime;
    } else {
        cbCostTotalTime = conn->timeCost.userCbTimeCostDetails.userCbExecCnt *
                              conn->timeCost.userCbTimeCostDetails.userCbExecAvgTime +
                          costTime;
    }
    conn->timeCost.userCbTimeCostDetails.userCbExecCnt++;
    conn->timeCost.userCbTimeCostDetails.userCbExecAvgTime =
        cbCostTotalTime / conn->timeCost.userCbTimeCostDetails.userCbExecCnt;

    // 构建一个包含新元素的临时数组，用来排序
    uint64_t tempArray[USERCB_MAX_EXEC_TIME_COUNT + 1];
    tempArray[0] = costTime;
    for (int i = 1; i < (USERCB_MAX_EXEC_TIME_COUNT + 1); i++) {
        tempArray[i] = conn->timeCost.userCbTimeCostDetails.userCbExecTime[i - 1];
    }
    // 哈希排序，默认原来的数组是有序的，且从大到小。
    for (int j = 0; j < USERCB_MAX_EXEC_TIME_COUNT; j++) {
        if (tempArray[j] < tempArray[j + 1]) {
            uint64_t temp = tempArray[j];
            tempArray[j] = tempArray[j + 1];
            tempArray[j + 1] = temp;
            continue;
        }
        break;
    }
    // 给userCbExecTime数组重新赋值
    for (int i = 0; i < USERCB_MAX_EXEC_TIME_COUNT; i++) {
        conn->timeCost.userCbTimeCostDetails.userCbExecTime[i] = tempArray[i];
    }
}

static void CheckCallbackExecTimeout(uint64_t startTime, GmcConnT *conn)
{
    uint64_t endTime = DbRdtsc();
    uint64_t costTime = DbToMseconds(endTime - startTime);
    if (SECUREC_UNLIKELY(costTime > (uint64_t)conn->singleCallbackTimeoutMs)) {
        // |Async|user callback function execute too long
        DB_LOG_WARN(GMERR_PROGRAM_LIMIT_EXCEEDED,
            "|Async|user callback function, cost: %" PRIu64 "(ms), more than %" PRIu32 "(ms).", costTime,
            conn->singleCallbackTimeoutMs);
        conn->timeCost.userCbTimeCostDetails.userCbTimeoutCnt++;
    }
    CallbackExecTimeStatistic(costTime, conn);
}

inline static void CheckCallbackUserParam(GmcConnT *conn, const AsyncMsgContextT *asyncMsgCtx)
{
    if (SECUREC_UNLIKELY(asyncMsgCtx->userCb == NULL)) {
        conn->connDfxInfo.emptyCbCnt++;
    }
    if (SECUREC_UNLIKELY(asyncMsgCtx->userData == NULL)) {
        conn->connDfxInfo.emptyUserDataCnt++;
    }
}

static void CheckMessageSendToRecvCostTime(const GmcConnT *conn, const AsyncMsgContextT *asyncMsgCtx)
{
    uint64_t startTime = asyncMsgCtx->requestStartTime;
    uint64_t currentTime = DbGettimeMonotonicUsec();
    uint64_t timeThreshold = conn->asyncTimeoutMs * USECONDS_IN_MSECOND;
    uint64_t costTime = currentTime - startTime;
    if (costTime > timeThreshold) {
        // Async message cost too much time:
        DB_LOG_WARN(GMERR_PROGRAM_LIMIT_EXCEEDED, "Async message, cost: %" PRIu64 "(us), more than %" PRIu64 "(us).",
            costTime, timeThreshold);
    }
}

#ifdef EXPERIMENTAL_NERGC
void RemoveRemoteLabelFromCacheById(GmcConnT *conn, AsyncMsgContextT *ctx)
{
    if (ctx->remoteLabelIdNum == 0) {
        return;
    }
    CltCataRemoveLabelById(conn->instanceId, ctx->remoteLabelIdArr[0]);
}
#endif

static void CallBackUserFunc(GmcConnT *conn, const void *data, const AsyncMsgContextT *asyncMsgCtx, Status ret)
{
    CheckCallbackUserParam(conn, asyncMsgCtx);
    uint64_t startTime = DbRdtsc();
    g_isInCallback = true;
    asyncMsgCtx->cltCallback(conn, asyncMsgCtx, ret, data);
    g_isInCallback = false;
    CheckCallbackExecTimeout(startTime, conn);
}

static void ParseSingleAsyncResponse(GmcConnT *conn)
{
    FixBufferT *buffer = &conn->recvPack;
    Status ret = CltRecvResponseAsync(conn, buffer);
    if (ret != GMERR_OK) {
        // |CAF| worthless to receive async response
        DB_LOG_ERROR(ret, "Receive async response, server remoteId: %" PRIu16 ".", conn->remoteId);
        return;
    }
    // 如果未接收完整，直接返回，第一现场已打info日志。
    if (conn->recvStatus != RECIVED_ALL) {
        return;
    }

    MsgHeaderT *header = RpcPeekMsgHeader(buffer);
    OpHeaderT *op = ProtocolPeekFirstOpHeader(buffer);
    if (op->opCode == MSG_OP_RPC_HEARTBEAT) {
        conn->heartbeatInfo.recvCnt++;
        return;
    }

    // 业务线程、异步应答处理线程和异步超时处理线程都会写map，此处为异步应答处理线程
    // 忙等方式获取锁，降低线程被调度出去的概率
    DbOamapT *asyncCtxMap = &conn->requestCtxMap;
    while (!DbRWSpinTryWLock(&asyncCtxMap->rwLock)) {
        continue;
    }

    // 从map中移除，防止其他线程获取到此ctx
    AsyncMsgContextT *asyncMsgCtx = DbOamapRemove(asyncCtxMap, header->serialNumber, &header->serialNumber);
    DbRWSpinWUnlock(&asyncCtxMap->rwLock);
    if (asyncMsgCtx == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR,
            "serialNumber %" PRIu32 " dismissed, lastSuccessSerialNum:%" PRIu32 ", server remoteId:%" PRIu16
            ", opcode:%" PRIu32 ".",
            header->serialNumber, conn->asyncReqLastTime.lastSuccessSerialNum, conn->remoteId, GetOpCode(buffer));
        return;
    }

    // 异步消息收到了，但是从发送到接收超过了设置的超时阈值，打印一个warning级别日志，需要靠超时定时器触发回调告诉用户
    CheckMessageSendToRecvCostTime(conn, asyncMsgCtx);

    // server might abandon some response if client cann't consume in time.
    if (header->opStatus != GMERR_OK) {
        // 为yang场景的mistakepath申请内存
        CltAllocErrorPath(conn, *buffer);
    }

    uint64_t data[64];
    ret = header->opStatus;
    if (ret == GMERR_OK) {
        // parseRsp 内部需要保证不会将 data 写越界
        ret = asyncMsgCtx->parseRsp(buffer, data);
#ifdef EXPERIMENTAL_NERGC
    } else if (ret == GMERR_UNDEFINED_TABLE) {
        if (DbIsTcp()) {
            RemoveRemoteLabelFromCacheById(conn, asyncMsgCtx);
        }
#endif
    }

    CallBackUserFunc(conn, data, asyncMsgCtx, ret);

    DB_LOG_DBG_DEBUG("|CAF| handle async event, server remoteId %" PRIu16 ", opCode %" PRIu32 ", serialNumber %" PRIu32
                     ".",
        conn->remoteId, op->opCode, header->serialNumber);
    DestroyAsyncCtx(asyncCtxMap->memCtx, asyncMsgCtx);  // 已经从map中移除，后续不会再使用到
}

static int32_t CltConnGetFd(const GmcConnT *conn)
{
    int32_t connFd = DbPipeGetEventFd(&conn->pipe);
    return connFd;
}

static void AsyncReadCb(int32_t fd, EventCallbackArg arg)
{
    GmcConnT *conn = (GmcConnT *)arg;
    conn->asyncEpollEnterTime.lastEpollEnterRTime = DbGettimeMonotonicUsec();
    if (SECUREC_UNLIKELY(conn->connStatus == CONN_IS_BROKEN)) {
        return;
    }

    uint64_t expCount;
    // 临时处理方案, 主方案需要推动业务修改成EPOLLET(边缘触发模式)
    DbAdptReadForCb(fd, &expCount, sizeof(uint64_t), NULL);

    uint32_t msgNum = 0;
    (void)DbPipeUnRecvMsgSize(&conn->pipe, &msgNum);

    while (msgNum > 0) {
        if (SECUREC_UNLIKELY(conn->connStatus == CONN_IS_BROKEN)) {
            break;
        }
        ParseSingleAsyncResponse(conn);
        (void)DbPipeUnRecvMsgSize(&conn->pipe, &msgNum);
    }
}

static void AsyncWriteCb(int32_t fd, EventCallbackArg arg)
{
    DB_UNUSED(fd);
    // 只有异步连接会走到该分支，这两个连接注册的回调arg为conn,可以强转
    GmcConnT *conn = (GmcConnT *)arg;
    conn->asyncEpollEnterTime.lastEpollEnterWTime = DbGettimeMonotonicUsec();
#if defined(RTOSV2) || defined(RTOSV2X) || defined(HPE)
    // 注册写回调时需要保证是大报文连接，此处不用判断
    ConsumeMessageBufferChannelLob(conn);
#else
    ConsumeMessageBuffer(conn);
#endif
}

static void AsyncLocalTimeoutCb(int32_t fd, EventCallbackArg arg)
{
    TimeoutEventCallbackArgT *timeoutArg = (TimeoutEventCallbackArgT *)arg;
    GmcConnT *conn = timeoutArg->conn;
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    if (fd != ioCtx->getRelFd(conn->asyncLocalTimerFd)) {
        // Async local timeout check unsucc
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR,
            "Async local timeout check : timer fd: %" PRId32 ". asyncLocalTimerFd: %" PRId32 ".", fd,
            conn->asyncLocalTimerFd);
        return;
    }
    uint64_t count;
    (void)DbAdptReadForTimeEvent(fd, &count, sizeof(uint64_t), NULL);

    RecordLatestEpollEnterTimeoutTime(&timeoutArg->auditInfo, DbGettimeMonotonicUsec());
    HandleSingleAsyncConn(conn, &timeoutArg->auditInfo);
}

static int32_t EpollRWCtl(int32_t fd, GmcEpollCtlTypeE type, uint32_t events, void *userData)
{
    int32_t *epollFd = (int32_t *)userData;
    struct epoll_event event = {};
    event.data.fd = fd;
    event.events = events;
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();

    if (type == GMC_EPOLL_ADD) {
        return ioCtx->epollCtl(*epollFd, EPOLL_CTL_ADD, fd, &event);
    } else if (type == GMC_EPOLL_MOD) {
        return ioCtx->epollCtl(*epollFd, EPOLL_CTL_MOD, fd, &event);
    } else if (type == GMC_EPOLL_DEL) {
        return ioCtx->epollCtl(*epollFd, EPOLL_CTL_DEL, fd, NULL);
    }
    DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Epoll rw.");
    return GMERR_INVALID_PARAMETER_VALUE;
}

static void SetCbForEvent(EventContextT *ctx, EventCallbackArg arg, EventTypeE eventType, uint32_t *events)
{
    DB_POINTER(ctx);
    if (eventType == SUB_EVENT) {
        ctx->writableCb = NULL;
        ctx->readableCb = SubPushCb;
        *events = (EPOLLIN | EPOLLRDHUP);
    } else if (eventType == ASYNC_EVENT) {
        GmcConnT *conn = (GmcConnT *)arg;
        if (conn->lctrType == LCTR_TYPE_USOCKET || conn->lctrType == LCTR_TYPE_TCP) {
            // socket通信注册读写事件回调
            ctx->writableCb = AsyncWriteCb;
            ctx->readableCb = AsyncReadCb;
            // 注册可读、可写、边沿触发
            *events = (uint32_t)(EPOLLIN | EPOLLOUT | EPOLLET);
        } else {
            ctx->readableCb = AsyncReadCb;
            // channel通信注册读事件回调
            if (conn->isLobConn) {
                ctx->writableCb = AsyncWriteCb;
                *events = (uint32_t)(EPOLLIN | EPOLLOUT);
            } else {
                ctx->writableCb = NULL;
                *events = EPOLLIN;
            }
        }
    } else if (eventType == SUB_NOTI_EVENT || eventType == SUB_SELF_AWAKE) {
        ctx->writableCb = NULL;
        ctx->readableCb = SubPushStatusMergeCb;
        *events = EPOLLIN;
    } else if (eventType == ASYNC_LOCAL_TIMEOUT_EVENT) {
        ctx->writableCb = NULL;
        ctx->readableCb = AsyncLocalTimeoutCb;
        *events = EPOLLIN;
    }
}

static inline int32_t CltGetFd(const GmcConnT *conn, EventTypeE eventType)
{
    int32_t fd;
    if (eventType == SUB_NOTI_EVENT) {
        fd = conn->notifyChan.nfd;
    } else if (eventType == SUB_SELF_AWAKE) {
        fd = conn->selfEventWakeUp;
    } else if (eventType == ASYNC_LOCAL_TIMEOUT_EVENT) {
        fd = conn->asyncLocalTimerFd;
    } else {
        fd = CltConnGetFd(conn);
    }
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    return ioCtx->getRelFd(fd);
}

static inline void CltInitEvent(
    EventContextT *ctx, EventCallbackArg arg, EventTypeE eventType, uint32_t *events, int32_t fd)
{
    SetCbForEvent(ctx, arg, eventType, events);
    ctx->arg = arg;
    ctx->fd = fd;
    ctx->eventType = eventType;
    DbSpinInit(&ctx->lock);
    return;
}

static Status EpollFuncExecute(GmcConnT *conn, EpollRegT *epollReg, uint32_t events, int32_t fd)
{
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    if (epollReg->epollRegType == EPOLLREG) {
        if (epollReg->epollRegFunc(ioCtx->getRelFd(fd), GMC_EPOLL_ADD) != 0) {
            return GMERR_DATA_EXCEPTION;
        }
    } else if (epollReg->epollRegType == EPOLLREG_WITH_USD) {
        // userData为用户通过接口传进来的，这里只负责传给epollFunc,不用检查
        if (epollReg->epollRegWithUsDFunc(ioCtx->getRelFd(fd), GMC_EPOLL_ADD, events, epollReg->userData) != 0) {
            return GMERR_DATA_EXCEPTION;
        }
    } else if (epollReg->epollRegType == EPOLLREG_WITH_CONN) {
        if (epollReg->epollRegWithConnFunc(ioCtx->getRelFd(fd), GMC_EPOLL_ADD, events, conn, epollReg->userData) != 0) {
            return GMERR_DATA_EXCEPTION;
        }
    } else {
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

static void LogEpollRegisterFdUnsucc(EpollRegT *epollReg, Status ret, int32_t fd, uint32_t events)
{
    const char *funcName = "";
    Dl_info epollFuncInfo;
    int32_t rc = dladdr((void *)epollReg->epollRegFunc, &epollFuncInfo);
    if (rc != 0 && epollFuncInfo.dli_sname != NULL) {
        funcName = epollFuncInfo.dli_sname;
    }
    DB_LOG_AND_SET_LASERR(ret,
        "register fd %" PRId32 " to epoll, reg type: %" PRIu32 ", reg func: %s, events: %" PRIu32 "", fd,
        (uint32_t)epollReg->epollRegType, funcName, events);
}

static Status AddEvent(EventTypeE eventType, EventCallbackArg arg, GmcConnT *conn, EpollRegT *epollReg)
{
    DB_POINTER(arg);
    int32_t fd = CltGetFd(conn, eventType);
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    DbOamapT *map = &g_gmdbCltEventCtxs;
    // 该内存在事件被从Map中移除时进行释放，通过函数RemoveEvent触发
    EventContextT *ctx = DbDynMemCtxAlloc(map->memCtx, sizeof(*ctx));
    if (ctx == NULL) {
        // Suffer a defeat to alloc event ctx for fd when add event.;
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Alloc event ctx for fd %" PRId32 ".", fd);
        return GMERR_OUT_OF_MEMORY;
    }

    // 给不同的连接类型设置不同的可读可写回调
    uint32_t events = DB_INVALID_UINT32;
    CltInitEvent(ctx, arg, eventType, &events, fd);

    DbRWSpinRLock(&g_gmdbCltEventCtxsRwLock);
    Status ret = DbOamapInsertWithLock(map, (uint32_t)ioCtx->getRelFd(fd), &ctx->fd, ctx, NULL);
    if (ret != GMERR_OK) {
        // Suffer a defeat to insert event ctx of fd when add event.
        DB_LOG_AND_SET_LASERR(ret, "Insert event ctx of fd %" PRId32 ".", fd);
        goto E1;
    }
    // 大报文连接必须使用EPOLLREG_WITH_USD或者EPOLLREG_WITH_CONN
    if (conn->isLobConn && epollReg->epollRegType == EPOLLREG) {
        // Use epoll register func worthless when use big message connection.
        DB_LOG_AND_SET_LASERR(ret, "using epoll register func.");
        goto E2;
    }

    ret = EpollFuncExecute(conn, epollReg, events, fd);
    if (ret != GMERR_OK) {
        goto E2;
    }
    DbRWSpinRUnlock(&g_gmdbCltEventCtxsRwLock);

    DB_LOG_DBG_DEBUG("|CAF| event added for fd %" PRId32 ".", ioCtx->getRelFd(fd));
    return GMERR_OK;

E2:
    ret = GMERR_DATA_EXCEPTION;
    LogEpollRegisterFdUnsucc(epollReg, ret, fd, events);
    void *value = DbOamapRemoveWithLock(map, (uint32_t)fd, &ctx->fd);
    DB_ASSERT(value == ctx);
E1:
    DbRWSpinRUnlock(&g_gmdbCltEventCtxsRwLock);
    DbDynMemCtxFree(map->memCtx, ctx);  // 已经从map中移除后续不会再使用到
    return ret;
}

static void *RemoveEvent(EventTypeE eventType, const GmcConnT *conn, EpollRegT *epollReg)
{
    int32_t fd = CltGetFd(conn, eventType);
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    DelEpollConnFd(epollReg, conn, fd);
    // 请先RemoveEvent，再关闭fd，否则这里可能清掉已被重新分配的fd对应的eventCtx
    DbOamapT *map = &g_gmdbCltEventCtxs;
    DbRWSpinRLock(&g_gmdbCltEventCtxsRwLock);
    // 从map中remove掉ctx，CltHandleEvent线程便无法获取该ctx.
    EventContextT *ctx = DbOamapRemoveWithLock(map, (uint32_t)ioCtx->getRelFd(fd), &fd);

    // 对于心跳，之前可能已经停过，直接返回NULL
    if (ctx == NULL) {
        DbRWSpinRUnlock(&g_gmdbCltEventCtxsRwLock);
        return NULL;
    }

    DbSpinLock(&ctx->lock);
    DbSpinUnlock(&ctx->lock);

    void *arg = ctx->arg;
    DbDynMemCtxFree(map->memCtx, ctx);  // 已经从map移除，后续不再使用到
    DbRWSpinRUnlock(&g_gmdbCltEventCtxsRwLock);

    DB_LOG_DBG_DEBUG("|CAF| event removed for fd %" PRId32 ".", ioCtx->getRelFd(fd));
    return arg;
}

static void HandleEvent(int32_t fd, uint32_t events)
{
    DbOamapT *map = &g_gmdbCltEventCtxs;
    DbRWSpinRLock(&g_gmdbCltEventCtxsRwLock);
    DbRWSpinRLock(&map->rwLock);

    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    EventContextT *ctx = DbOamapLookup(map, (uint32_t)ioCtx->getRelFd(fd), &fd, NULL);
    if (ctx != NULL) {
        // 标记回调正在运行。必须在map->rwLock看护下完成，否则RemoveEvent可能先释放ctx
        // 因为一个内部fd只会注册到一个epoll线程，assert不应该失败
        bool ok = DbSpinTryLock(&ctx->lock);
        if (!ok) {
            DbRWSpinRUnlock(&map->rwLock);
            DbRWSpinRUnlock(&g_gmdbCltEventCtxsRwLock);
            // Try lock worthless.
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Try lock.");
            DB_ASSERT(ok);
            return;
        }
    }
    DbRWSpinRUnlock(&map->rwLock);

    if (ctx != NULL) {
        if ((events & EPOLLIN) != 0) {
            DB_POINTER(ctx->readableCb);
            ctx->readableCb(ioCtx->getRelFd(fd), ctx->arg);
        }
        if ((events & EPOLLOUT) != 0) {
            DB_POINTER(ctx->writableCb);
            ctx->writableCb(ioCtx->getRelFd(fd), ctx->arg);
        }
        DbSpinUnlock(&ctx->lock);
    }
    DbRWSpinRUnlock(&g_gmdbCltEventCtxsRwLock);
}

ALWAYS_INLINE void CltHandleEvent(int32_t fd, uint32_t events)
{
    if (fd < 0) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Fd %" PRId32 ".", fd);
        return;
    }

    if (HandleHeartbeat(fd)) {
        return;
    }

    if (HandleAsyncTimeout(fd)) {
        return;
    }
    HandleEvent(fd, events);
}

static Status SetupEventsForAsyncConn(GmcConnT *conn)
{
    EpollRegT *epollReg = &conn->epollReg;
    TimeoutEventCallbackArgT *arg = NULL;

    // 记录异步响应事件的Map，生命周期同该异步连接，在断连时随连接memCtx一起销毁
    Status ret = DbOamapInit(&conn->requestCtxMap, 0, DbOamapUint32Compare, conn->memCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = AddEvent(ASYNC_EVENT, conn, conn, epollReg);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (conn->isLocalAsyncTimeout) {
        // 创建定时器
        ret = CreateAsyncTimeoutTimer(&conn->asyncLocalTimerFd);
        if (ret != GMERR_OK) {
            goto ERR;
        }

        // 创建TimeoutCallbackArg
        ret = CreateTimeoutEventCallbackArg(&arg, conn->memCtx, conn);
        if (ret != GMERR_OK) {
            goto ERR;
        }

        ret = AddEvent(ASYNC_LOCAL_TIMEOUT_EVENT, (EventCallbackArg)arg, conn, epollReg);
        if (ret != GMERR_OK) {
            goto ERR;
        }
    }
    return GMERR_OK;

ERR:
    if (arg != NULL) {
        DbDynMemCtxFree(conn->memCtx, arg);
        arg = NULL;
    }

    if (conn->asyncLocalTimerFd != DB_INVALID_FD) {
        DbAdptTimerFdClose(conn->asyncLocalTimerFd);
        conn->asyncLocalTimerFd = DB_INVALID_FD;
    }

    RemoveEvent(ASYNC_EVENT, conn, epollReg);
    return ret;
}

static void TearDownEventsForAsyncConn(GmcConnT *conn)
{
    EpollRegT *epollReg = (EpollRegT *)&conn->epollReg;
    void *arg = NULL;
    if (conn->isLocalAsyncTimeout) {
        // 移除的时候，要保证先移除超时的fd。
        // 如果先移除异步连接的fd，可能出现问题，异步连接的fd已被移除，无法收到服务端应答，被超时机制检测到
        arg = RemoveEvent(ASYNC_LOCAL_TIMEOUT_EVENT, conn, epollReg);
        DB_ASSERT(arg != NULL);
        TimeoutEventCallbackArgT *timeoutArg = (TimeoutEventCallbackArgT *)arg;
        DB_ASSERT(timeoutArg->conn == conn);
        DbDynMemCtxFree(conn->memCtx, timeoutArg);
        // 需要先将fd从map中移除，然后再close fd，防止移除到其他的fd
        Status ret = DbAdptTimerFdClose(conn->asyncLocalTimerFd);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Close a timer fd for local timeout.");
        }
    }
    arg = RemoveEvent(ASYNC_EVENT, conn, epollReg);
    DB_ASSERT(arg == conn);
}

static void RemoveSubFinalEvent(GmcConnT *conn, EventTypeE type)
{
    // add/remove event使用的conn都是合法的（不考虑用户传野指针），那么不应该有意外
    SubEvtCallBackArgs *cbArgs = (SubEvtCallBackArgs *)RemoveEvent(type, conn, (EpollRegT *)&conn->epollReg);
    if (cbArgs != NULL) {
        GmcStmtT *stmt = cbArgs->stmt;
        if (stmt != NULL) {
            DB_ASSERT(stmt->conn == conn);
            FreeStmt(stmt);
            cbArgs->stmt = NULL;
        }
        DbDynMemCtxFree(conn->memCtx, cbArgs);  // 正常释放内存，后续不会再使用到
    }
}

static void TearDownEventsForSubConn(GmcConnT *conn)
{
    EpollRegT *epollReg = (EpollRegT *)&conn->epollReg;

    if (conn->notifyChan.nfd != DB_INVALID_FD) {
        (void)RemoveEvent(SUB_NOTI_EVENT, conn, epollReg);  // 跟SUB_EVENT的cbArgs参数相同，下面SUB_EVENT处理即可
    }

#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    if (conn->selfEventWakeUp != DB_INVALID_FD) {
        (void)RemoveEvent(SUB_SELF_AWAKE, conn, epollReg);
    }
#endif

    RemoveSubFinalEvent(conn, SUB_EVENT);
}

static Status SetupEventsForSubConn(GmcConnT *conn)
{
    EpollRegT *epollReg = &conn->epollReg;
    // 此内存在用户通过接口GmcDisconnect删除订阅连接时，随着conn->memCtx一起被释放
    SubEvtCallBackArgs *cbArgs = DbDynMemCtxAlloc(conn->memCtx, sizeof(SubEvtCallBackArgs));
    if (cbArgs == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "SubEvtCallBackArgs.");
        return GMERR_OUT_OF_MEMORY;
    }
    cbArgs->stmt = NULL;
    cbArgs->conn = conn;
    Status ret = AddEvent(SUB_EVENT, cbArgs, conn, epollReg);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(conn->memCtx, cbArgs);  // 失败场景下只在函数内部使用，map中也找不到对应的ctx
        return ret;
    }
    if (conn->notifyChan.nfd == DB_INVALID_FD) {
        return GMERR_OK;
    }
    ret = AddEvent(SUB_NOTI_EVENT, cbArgs, conn, epollReg);
    if (ret != GMERR_OK) {
        RemoveSubFinalEvent(conn, SUB_EVENT);
        return ret;
    }
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    if (conn->selfEventWakeUp == DB_INVALID_FD) {
        return GMERR_OK;
    }
    ret = AddEvent(SUB_SELF_AWAKE, cbArgs, conn, epollReg);
    if (ret != GMERR_OK) {
        (void)RemoveEvent(SUB_NOTI_EVENT, conn, epollReg);
        RemoveSubFinalEvent(conn, SUB_EVENT);
        return ret;
    }
#endif
    return ret;
}

Status SetupEvents(GmcConnT *conn)
{
    // 要么使用用户侧epoll对象，要么使用客户端epoll对象，两种不要混用
    if (g_gmdbCltEventEpollFd != DB_INVALID_FD && conn->epollReg.epollRegType != EPOLLREG_INVALID) {
        // Two different epoll object exist when add event.
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Different object when add event.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    // 修正为实际使用的epollReg，用于后续断开连接时使用
    if (g_gmdbCltEventEpollFd != DB_INVALID_FD) {
        // 如果用户使用GmcStartEpoll开启epoll,则使用该GmcEpollRegWithUserDataT函数原型的epollReg
        conn->epollReg.epollRegType = EPOLLREG_WITH_USD;
        conn->epollReg.epollRegWithUsDFunc = EpollRWCtl;
        conn->epollReg.userData = &g_gmdbCltEventEpollFd;
    }

    if (conn->connType == GMC_CONN_TYPE_SYNC) {
        return GMERR_OK;
    }

    Status ret = DbPipeSetNoBlock(&conn->pipe, true);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "worthless to set pipe noblock: %" PRIu32 ".", conn->remoteId);
        return ret;
    }
    // 对于其他连接，必须基于epoll
    if (conn->epollReg.epollRegType == EPOLLREG_INVALID) {
        // No epoll object available when add event.
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "epoll object unavailable.");
        return GMERR_DATA_EXCEPTION;
    }
    if (conn->connType == GMC_CONN_TYPE_ASYNC) {
        return SetupEventsForAsyncConn(conn);
    } else {
        DB_ASSERT(conn->connType == GMC_CONN_TYPE_SUB);
        return SetupEventsForSubConn(conn);
    }
}

void TearDownEvents(GmcConnT *conn)
{
    if (conn->connType == GMC_CONN_TYPE_SYNC) {
        return;
    } else if (conn->connType == GMC_CONN_TYPE_ASYNC) {
        TearDownEventsForAsyncConn(conn);
    } else {
        DB_ASSERT(conn->connType == GMC_CONN_TYPE_SUB);
        TearDownEventsForSubConn(conn);
    }
}

#ifdef EXPERIMENTAL_NERGC
static Status CopyAsyncRemoteLabelIdArr(const AsyncMsgContextT *srcCtx, AsyncMsgContextT *dstCtx)
{
    if (srcCtx->remoteLabelIdNum == 0 || srcCtx->remoteLabelIdArr == NULL) {
        dstCtx->remoteLabelIdArr = NULL;
        dstCtx->remoteLabelIdNum = 0;
        return GMERR_OK;
    }
    dstCtx->remoteLabelIdNum = srcCtx->remoteLabelIdNum;
    size_t idArrSize = sizeof(uint32_t) * srcCtx->remoteLabelIdNum;
    dstCtx->remoteLabelIdArr = DbDynMemCtxAlloc(g_gmdbCltInstance.memCtx, idArrSize);
    if (dstCtx->remoteLabelIdArr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Malloc idArr when Copy.");
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = memcpy_s(dstCtx->remoteLabelIdArr, idArrSize, srcCtx->remoteLabelIdArr, idArrSize);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Copy idArr, ret: %" PRId32 "", err);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}
#endif

Status AddAsyncMsg(GmcConnT *conn, const AsyncMsgContextT *asyncCtx)
{
    DbOamapT *map = &conn->requestCtxMap;
    DbMemCtxT *memCtx = map->memCtx;
    // 此内存在消费完该异步操作返回的消息后（服务端返回 或
    // 触发异步超时），从map中移除ctx时释放内存，通过RemoveAsyncMsg函数
    AsyncMsgContextT *ctx = DbDynMemCtxAlloc(memCtx, sizeof(AsyncMsgContextT));
    if (SECUREC_UNLIKELY(ctx == NULL)) {
        // Alloc memctx worthless when connection add async msg.
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "when add async msg.");
        return GMERR_OUT_OF_MEMORY;
    }
    *ctx = *asyncCtx;

    Status ret;
#ifdef EXPERIMENTAL_NERGC
    ret = CopyAsyncRemoteLabelIdArr(asyncCtx, ctx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DestroyAsyncCtx(memCtx, ctx);  // 插入map失败时，内存只应该在函数内部使用，不会传到外面
        return ret;
    }
#endif
    ret = DbOamapInsertNoDupKeyWithLock(map, ctx->serialNumber, &ctx->serialNumber, ctx, NULL);
    if (SECUREC_LIKELY(ret == GMERR_OK)) {
        return GMERR_OK;
    }
    DestroyAsyncCtx(memCtx, ctx);  // 插入map失败时，内存只应该在函数内部使用，不会传到外面
    // Save msg ctx worthless when connection add async msg.
    DB_LOG_AND_SET_LASERR(ret, "Save msg ctx.");
    return ret;
}

void RemoveAsyncMsg(GmcConnT *conn, const AsyncMsgContextT *asyncCtx)
{
    DbOamapT *asyncCtxMap = &conn->requestCtxMap;  // 从map中移除，防止其他线程获取到此ctx
    AsyncMsgContextT *asyncMsgCtx = DbOamapRemoveWithLock(asyncCtxMap, asyncCtx->serialNumber, &asyncCtx->serialNumber);
    if (asyncMsgCtx != NULL) {
        DestroyAsyncCtx(asyncCtxMap->memCtx, asyncMsgCtx);
    }
}

void DestroyAsyncCtx(DbMemCtxT *memCtx, AsyncMsgContextT *asyncCtx)
{
#ifdef EXPERIMENTAL_NERGC
    if (asyncCtx->remoteLabelIdArr != NULL) {
        DbDynMemCtxFree(g_gmdbCltInstance.memCtx, asyncCtx->remoteLabelIdArr);
        asyncCtx->remoteLabelIdArr = NULL;
        asyncCtx->remoteLabelIdNum = 0;
    }
#endif
    DbDynMemCtxFree(memCtx, asyncCtx);  // 正常释放内存，后续不会再用到
}
