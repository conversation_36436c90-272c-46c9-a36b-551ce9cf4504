/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: clt_async_conn.c
 * Description: Interface of GMDB client for async event handler.
 * Author: zhangyong
 * Create: 2021-05-13
 */

#ifndef CLT_ASYNC_CONN_H
#define CLT_ASYNC_CONN_H

#include "clt_conn.h"

#ifdef __cplusplus
extern "C" {
#endif

#define CLT_TIMEOUT_CTX_EXTEND_SIZE 32

typedef struct AsyncHandleCtx {
    AsyncMsgContextT *asyncMsgCtx;
    uint64_t processTime;
} AsyncHandleCtxT;

// user callback prototype
typedef void (*AsyncDDLDoneT)(void *userData, Status status, const char *errMsg);
typedef void (*AsyncDMLDoneT)(void *userData, uint32_t affectedRows, Status status, const char *errMsg);

extern int32_t g_gmdbCltEventEpollFd;

Status InitEventContext(void);
void UnInitEventContext(void);
void DelEpollConnFd(EpollRegT *epollReg, const GmcConnT *conn, int32_t fd);

Status SetupEvents(GmcConnT *conn);
void TearDownEvents(GmcConnT *conn);

// asyncCtx为外部临时构造的栈变量，AddAsyncMsg内部会进行拷贝
Status AddAsyncMsg(GmcConnT *conn, const AsyncMsgContextT *asyncCtx);
// 用于在发送失败时，撤销AddAsyncMsg的操作
void RemoveAsyncMsg(GmcConnT *conn, const AsyncMsgContextT *asyncCtx);
void DestroyAsyncCtx(DbMemCtxT *memCtx, AsyncMsgContextT *asyncCtx);

void CltHandleEvent(int32_t fd, uint32_t events);

void CltSetErrorPathFromRsp(FixBufferT response);
void CallbackExecTimeStatistic(uint64_t costTime, GmcConnT *conn);
#ifdef __cplusplus
}
#endif

#endif /* CLT_ASYNC_CONN_H */
