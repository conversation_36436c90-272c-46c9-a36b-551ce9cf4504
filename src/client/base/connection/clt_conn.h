/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: clt_conn.h
 * Description: Implement of GMDB client for connection
 * Author:
 * Create: 2020-08-05
 */

#ifndef CLT_CONN_H
#define CLT_CONN_H

#include "adpt_eventfd.h"
#include "clt_flow_control.h"
#include "db_pipe.h"
#include "db_hashmap.h"
#include "db_linkedlist.h"
#include "db_rpc_conn_msg.h"
#include "db_share_msg_node.h"
#include "db_shm_entry.h"
#include "db_notify_channel.h"
#include "db_direct_msg_pool.h"
#include "db_shm_hashmap.h"
#include "dm_data_define.h"
#include "db_stream_msg_pool.h"

#ifdef __cplusplus
extern "C" {
#endif

#define CLT_DISABLE_SLOW_OP_LOG UINT64_MAX  // 关闭客户端慢操作日志

#define USERCB_MAX_EXEC_TIME_COUNT 5     // 回调函数执行时间，统计最长的几次。
#define MAX_WAIT_TIME_PER_REQUEST 30000  // 异步请求等待默认最大时间，Ms;
#define CLT_CALLBACK_SINGLE_TIMEOUT_THRESHOLD_RTOSV2X 1000  // rtosv2x下单条回调函数超时打印日志，默认超时时间阈值1000ms
#define CLT_CALLBACK_AVG_TIMEOUT_THRESHOLD_RTOSV2X 10  // rtosv2x下平均回调函数超时打印日志，默认超时时间阈值10ms
#define CLT_CALLBACK_SINGLE_TIMEOUT_THRESHOLD 200  // 单条回调函数超时打印日志，默认超时时间阈值200ms
#define CLT_CALLBACK_AVG_TIMEOUT_THRESHOLD 1       // 平均回调函数超时打印日志，默认超时时间阈值1ms
// 目前WSDK使用Datalog的异步操作，在打流启动场景下，可能导致Datalog
// DML事务锁持有时间过长，故该值建议与配置的事务锁超时时间保持一致
#define DEFAULT_SUB_MSG_RING_SIZE (16 * DB_KIBI)  // 订阅连接消息队列大小
#define TIMEOUT_TIMER_INTERVAL 50                 // 超时定时器间隔时间，s

#define DIRECT_WRITE_STATIS_NUM 3                // 记录直连写路径下top3耗时信息
#define DIRECT_WRITE_STATIS_COUNT_THRESHOLD 100  // 为了避免频繁打印日志，暂定从第100次操作时才记录日志

extern bool g_cltSameProcessOpen;

/* general user callback prototype of async request. */
typedef void (*AsyncRequestDoneT)(void);

typedef enum EpollRegTypeE {
    EPOLLREG_INVALID = 0,
    EPOLLREG = 1,
    EPOLLREG_WITH_USD = 2,
    EPOLLREG_WITH_CONN = 3,
} EpollRegTypeE;

typedef struct {
    EpollRegTypeE epollRegType;  // epollreg的类型
    union {
        GmcEpollRegT epollRegFunc;
        GmcEpollRegWithUserDataT epollRegWithUsDFunc;
        GmcEpollRegWithConnT epollRegWithConnFunc;
    };
    void *userData;  // 传入epollReg函数的用户data，会挂在连接上
} EpollRegT;  // 将连接fd注册到epoll对象时使用的操作函数及类型，当前有2类epollReg函数原型

struct GmcConnOptionsT {
    char serverLocator[DB_LCTR_MAX_LEN];
    CliConnectRequestT requestParams;
    uint32_t requestTimeoutUs;                     // request msg timeout  (usec)
    uint32_t msgReadTimeoutMs;                     // msec
    uint32_t msgWriteTimeoutMs;                    // msec
    uint32_t msgQueueSize;                         // max msg num of HPE channel ring
    uint32_t packShrinkThreshold;                  // scale-in conn->sendPack, conn->recvPack, stmt->recvPack
    EpollRegT epollReg;                            // epollreg函数及类型的结构体
    GmcConnFlowCtrlLevelNoticeT flowCtrlNoticeCb;  // flow control callback
    void *flowCtrlNoticeData;                      // user data of flow control callback
    GmcSubFailedCallbackT subFailedCb;             // subscribe notice callback
    void *subFailedData;                           // user data of subscribe notice callback
    uint16_t maxStmtCount;
    uint16_t srvMemCtxLimit;   // 连接的server memCtx的内存上限，unit：Mbyte
    bool isLocalAsyncTimeout;  // 异步连接是否启用连接级timer fd的超时机制
    bool openDirectRead;       // Open direct read switch.
    bool openDirectWrite;      // Open direct write switch.
    bool isCsMode;             // in csMode, only channel can use shared memory
    uint32_t timeInterval;     // interval which control client sysview resoure send.
    uint64_t timeThreshold;  // 是否打印日志的时间阈值，-1-关闭；0-全部打印；剩下的正整数-时间阈值
    uint64_t bigObjectStand;           // DFX大对象检测的阈值
    uint32_t connMemCtxLimit;          // 连接的memCtx的内存上限，unit：Mbyte, 默认为1024M
    uint32_t singleCallbackTimeoutMs;  // 单条回调函数超时阈值,订阅、异步共用
    uint32_t avgcallbackTimeoutMs;     // 平均回调函数超时阈值，仅订阅回调使用
    uint32_t asyncTimeoutMs;           // 异步消息超时时间阈值
};

void CltInitConnOptions(GmcConnOptionsT *connOptions);
Status CltConnOptionsCreate(GmcConnOptionsT **connOptions);
void CltConnOptionsDestroy(GmcConnOptionsT *connOptions);

typedef struct MsgBufferList {
#ifndef NDEBUG
    uint32_t magic;
#endif
    struct MsgBufferList *next;
    uint32_t size;
    DbSpinLockT lock;  // 异步连接发送和接收可能不在一个线程，需要加锁...
} MsgBufferList;

typedef struct {
    uint64_t periodTime;
    uint64_t maxTime;
    uint64_t maxStartTime;
    uint32_t insertTimes;
    uint64_t totalTime;
} TimeCostDetailsT;

typedef struct {
    uint64_t userCbTimeoutCnt;
    uint64_t userCbExecAvgTime;
    uint64_t userCbExecTime[USERCB_MAX_EXEC_TIME_COUNT];
    uint64_t userCbExecCnt;
} UserCbTimeCostDetailT;

typedef struct {
    uint64_t updateTime;
    UserCbTimeCostDetailT userCbTimeCostDetails;
    uint32_t arrayPos;
    TimeCostDetailsT timeConsDetails;
} CltTimeCostT;

typedef struct {
    uint64_t execTime;
    uint64_t cpuTime;
    uint32_t opType;
} CltDwTimeCostItemT;

typedef struct {
    CltDwTimeCostItemT top3Items[DIRECT_WRITE_STATIS_NUM];  // 记录直连写耗时top 3的相关信息
    uint64_t totalExecCnt;                                  // 总执行次数
    uint32_t minPos;                                        // 记录top3Items数组中最小值的位置
} CltDirectWriteTimeCostT;

typedef struct {
    uint64_t timeThreshold;     // 日志打印所需的时间阈值，操作耗时大于此时间才会打印
    uint64_t opStartTime;       // check完，开始操作的时间点
    uint64_t sendMsgStartTime;  // 开始向服务端发送报文的时间点
    uint64_t rspSendStartTime;  // 服务端开始向客户端发送消息的时间点，从服务端返回的消息里面拿到
    uint64_t rspGetTime;  // 客户端接收到服务端报文的时间点
    uint64_t opEndTime;   // 完成整个操作的时间
    uint32_t opType;
} CltOpSegTimeT;

typedef struct {
    uint64_t normalObjAvgDynMem;    // 统计连接memCtx动态内存普通对象操作的平均值
    uint64_t normalObjMaxDynMem;    // 统计连接memCtx动态内存普通对象操作的最大值
    uint64_t bigObjAvgDynMem;       // 统计连接memCtx动态内存超大对象操作的平均值
    uint64_t bigObjMaxDynMem;       // 统计连接memCtx动态内存超大对象操作的最大值
    uint32_t normalObjDynMemCount;  // 统计连接memCtx动态内存普通对象操作的次数
    uint32_t bigObjDynMemCount;     // 统计连接memCtx动态内存超大对象操作的次数
} CltDynMemT;

typedef struct {
    uint32_t normalObjSendAvgSize;  // 统计发送普通报文的平均值
    uint32_t normalObjSendMaxSize;  // 统计发送普通报文的最大值
    uint32_t bigObjSendAvgSize;     // 统计发送超大报文的平均值
    uint32_t bigObjSendMaxSize;     // 统计发送超大报文的最大值
    uint32_t normalObjSendCount;    // 统计发送普通报文的次数
    uint32_t bigObjSendCount;       // 统计发送超大报文的次数
} CltSendObjT;

typedef struct {
    uint32_t normalObjRecvAvgSize;  // 统计接收普通报文的平均值
    uint32_t normalObjRecvMaxSize;  // 统计接收普通报文的最大值
    uint32_t bigObjRecvAvgSize;     // 统计接收超大报文的平均值
    uint32_t bigObjRecvMaxSize;     // 统计接收超大报文的最大值
    uint32_t normalObjRecvCount;    // 统计发送普通报文的次数
    uint32_t bigObjRecvCount;       // 统计发送超大报文的次数
} CltRecvObjT;

// 异步发送最近一次的报文内容及时间信息，用于超时打印
typedef struct {
    uint32_t lastSuccessSerialNum;  // 上一次成功发送的异步消息的序列号
    uint64_t lastRequestTime;       // 上一次成功发送的异步消息的时间点
} CltAsyncReqLastTimeT;

// 该连接epoll最近一次触发进入epoll响应的时间信息，用于超时打印
typedef struct {
    uint64_t lastEpollEnterRTime;  // 上一次成功进入读回调时间AsyncReadCb
    uint64_t lastEpollEnterWTime;  // 上一次成功进入写回调时间AsyncWriteCb
} CltAsyncEpollEnterTimeT;

typedef struct AsyncMsgContextT {
    uint32_t serialNumber;
    uint8_t notDistribute;  // MODEL_DATALOG下使用
    // 应答报文解析函数，异步框架接收应答后会先调用此函数解析报文，然后将报文解析的结果等信息传入客户端回调
    Status (*parseRsp)(FixBufferT *, void *);

    // 客户端回调及其自定义参数，用于在 parseRsp 后做进一步的必要处理以及唤起用户回调
    void (*cltCallback)(GmcConnT *conn, const struct AsyncMsgContextT *ctx, Status ret, const void *out);
    union {
        void *cltData;
        uint32_t cltDataU32;
    };

    // 用户回调及其参数
    union {
        void *userCbAddr;      // type erased function pointer
        void (*userCb)(void);  // make codecheck happy, not using void* here
    };
    void *userData;

    // 用于异步超时的处理
    uint64_t requestStartTime;  // us，异步发送的时间，如果走客户端消息队列发送为进入消息队列的时间。
#ifdef EXPERIMENTAL_NERGC
    uint32_t remoteLabelIdNum;
    uint32_t *remoteLabelIdArr;
#endif
} AsyncMsgContextT;

typedef struct AsyncMsgBufferNode {
    struct AsyncMsgBufferNode *next;
    FixBufferT nodeBuffer;
} AsyncMsgBufferNodeT;

typedef struct {
    AsyncMsgBufferNodeT *head;
    AsyncMsgBufferNodeT *tail;
    size_t size;        // 消息总的内存大小，(AsyncMsgBufferNodeT +sizeof(buf->pos))*count
    uint32_t count;     // 消息总个数，用于debug
    DbSpinLockT lock;   // 不同线程访问
    DbMemCtxT *memCtx;  // 其父为conn->memCtx，未进行单独清理操作，生命周期为连接级
} AsyncMsgBufferQueueT;

typedef enum CliConnStatusE {
    CONN_IS_NORMAL = 0,
    CONN_IS_BROKEN = 1,
} CliConnStatusE;

typedef enum CliRecivedStatus {
    RECIVED_ALL = 0,
    UNRECIVED_HEADER = 1,
    UNRECIVED_BODY = 2,
    RECIVED_PART = 3,
} CliRecivedStatusE;

typedef struct {
    uint64_t sendCnt;
    uint64_t recvCnt;
} CltHeartBeatInfoT;

typedef struct UserInfo {
    char userName[DM_MAX_NAME_LENGTH];
    char groupName[DM_MAX_NAME_LENGTH];
    char processName[DM_MAX_NAME_LENGTH];
    char auditUserInfo[DB_AUDIT_USER_INFO];  // 打印audit日志
} UserInfoT;

// 建连各阶段计时点
typedef struct {
    uint64_t sendHandshakeReq;       // 发送建连一阶段握手请求
    uint64_t recvHandshakeFirstRsp;  // 收到建连一阶段握手应答1
    uint64_t recvHandshakeSecRsp;    // 收到建连一阶段握手应答2
    uint64_t recvHandshakeThirdRsp;  // 收到建连一阶段握手应答3
    uint64_t recvHandshakeForthRsp;  // 收到建连一阶段握手应答4
    uint64_t sendSecConnReq;         // 发送建连二阶段业务请求
    uint64_t recvSecConnRsp;         // 收到建连二阶段业务应答
} CltConnStageTimePointT;

/*
 * 来看护连接句柄的字节数上限，防止conn句柄膨胀过大,当前 1464，新增请备注
 * 直连写新增dwPubSubInfo， directMsgRing共16字节
 * 直连写新增日志统计信息结构体CltDirectWriteTimeCostT类型的指针，占8字节
 * 订阅新增dfx，记录从服务端发送第一条报文到客户端进入SubPushCb的时间，占8字节
 * 时序新增isCsMode和serverLocator和，记录是否为纯CS模式以及记录当前连接的serverLocator，占16字节
 * 时序新增entryCtx中的shmemPtr和localVersion，用于refreshConn;
 * 订阅事件类型增加了diff事件类型，connDfxInfo结构体自动扩充了8个字节
 * stmt并发检测新增isUsing标志位判断conn当前是否正在被使用 共1字节
 * MsgBufferList增加magic code保护 共8字节
 * 新增ConnDfxInfo字段，用于记录epoll一些信息，占4字节，ConnDfxInfo部分uint64_t可以优化
 * flowControl字段新增labelFlowCtrlSleepTime，用于记录表级别流控信息，细粒度流控所需，占8字节
 */
#define DB_MAX_CONN_BYTES 1464

struct GmcConnT {
    // 连接占用内存的大小有相关指标，
    // 添加变量时请注意对内存底噪及空间局部性的影响

    /***** 操作句柄及状态信息 *****/
    uint32_t magic;
    GmcConnTypeE connType : 8;  // 连接类型
    uint8_t transMode : 8;      // 事务开启状态
    uint8_t dbId : 8;           // 数据库ID
    bool openDirectRead : 1;    // 是否使用直连查询
    bool isLobConn : 1;  // 大报文连接开关，用于标记该连接可发送大于2M的报文，默认为false
    bool isLocalAsyncTimeout : 1;    // 异步连接是否启用连接级timer fd的超时机制
    bool isCsMode : 1;               // 是否开启CS模式；CS模式：除通信外，禁用共享内存
    bool enableDirectWrite : 1;      // 是否使用直连写
    bool isUsing;                    // 判断当前conn是否正在被使用
    uint8_t padding[2];              // 字节填充
    uint32_t attachRef;              // csMode引用计数
    int32_t asyncLocalTimerFd;       // 记录异步连接的连接级timer fd
    ShmemPtrT rsmCtxPtr;             // 保留内存ptr
    void *directReadCtx;             // 直连操作句柄
    void *directWriteCtx;            // 直连写句柄，在首次执行直连prepare时初始化
    UserInfoT *dwUserInfo;           // 用户信息
    ShmemPtrT agedMgrLatchShmAddr;   // 老化任务的 latch 的共享内存 addr
    DbShmOamapT *agedMgrShmTaskMap;  // 老化任务的 shmTaskMap
    DbCfgItemT *cfgShmConfig;        // 共享内存中维护的配置项，客户端可读
    DwPubSubShmInfoT *dwPubSubInfo;  // 直连写订阅共享信息
    uint32_t lastLogicWindow;        // 直连写订阅上次逻辑窗口
    uint32_t windowOperCount;        // 本次逻辑窗口操作次数
    uint32_t instanceId;             // 目标实例ID
    uint64_t shmVersion;
    uint32_t namespaceId;  // 命名空间ID
    void *serverModeShm;  // 用来判断服务端是处于普通模式，还是单用户模式（只有DBA用户有权限与服务端交互）
    uint32_t connVersion;  // 用于校验服务端模式版本
    uint32_t pid;
    uint32_t msgCtxId;
#ifdef EXPERIMENTAL_NERGC
    SessionIdT sessionId;
    bool isAuth;
    char userName[MAX_USER_NAME_LEN];
#endif
    DbShmEntryCtxT entryCtx;

    /***** stmt相关信息 *****/

    DbSpinLockT lock;  // 控制心跳线程和主线程对childStmtList的并发访问（如果后续移除心跳可以删除）
    uint16_t childStmtCount;       // 当前连接的子stmt个数
    uint16_t childStmtLimit;       // 当前连接最多允许的子stmt个数
    TagLinkedListT childStmtList;  // 当前连接的子stmt列表
    TagLinkedListT stmtIdList;     // 当前连接申请了stmtId的子stmt列表

    /***** 通信相关成员变量 *****/

    EpollRegT epollReg;  // 将连接fd注册到epoll对象时使用的操作函数，当前有两种EpollReg函数原型
    CltFlowControlT flowControl;   // 流控相关信息
    DbPipeT pipe;                  // 通信管道
    uint32_t requestTimeoutUs;     // 业务请求超时时间（仅用于stmt消息）
    uint32_t serialNumberSend;     // 已消耗的序列号
    uint32_t packShrinkThreshold;  // 缓冲区缩容阈值
    DbLctrTypeE lctrType;          // 通信类型
    FixBufferT recvPack;           // 接收缓冲
    FixBufferT sendPack;           // 发送缓冲
    DbMemCtxT *memCtx;             // 通用动态内存上下文
    SharedMsgPoolCtxT msgPoolCtx;  // 消息池上下文
    DbMemCtxT *msgCtx;             // 通信内存上下文

#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    MsgBufferList msgPool;  // 消息池，用于优化channel的性能
#endif

    /***** 各种连接类型的特有变量 *****/

    union {
        /***** 异步连接 *****/
        struct {
            DbOamapT requestCtxMap;             // request context map in async conn
            uint32_t timeoutIndex;              // index of timeout contexts in async conn
            AsyncMsgBufferQueueT sendMsgQueue;  // message buffer queue in async conn
            CliRecivedStatusE recvStatus;  // 异步消息接收状态，用于标记报文是否已经接收完整。
            CliConnStatusE connStatus;  // 连接状态，异步时用来判断该返回什么错误码，在CltInitConnection那里初始化
            CltAsyncReqLastTimeT asyncReqLastTime;        // 异步最近一次发送时间统计
            CltAsyncEpollEnterTimeT asyncEpollEnterTime;  // 异步最近一次epoll响应事件的时间
        };
        /***** 订阅连接 *****/
        struct {
            const char *connName;               // 订阅通道名称
            ShareMsgMgrT *subMsgMgr;            // 订阅消息管理结构
            GmcSubFailedCallbackT subFailedCb;  // 失败通知回调
            void *subFailedData;                // 失败通知回调的自定义参数
            DbCltNotifyChannelT notifyChan;     // 新订阅通知通道
        };
    };
    int selfEventWakeUp;
    uint16_t recvSeq;     // 大报文接收时的序号标记
    char *serverLocator;  // 用于GetKvTable GetVertexLabel ReSub时创建临时同步连接

    /***** DFX功能相关 *****/
    int reserved;
    uint16_t remoteId;                    // 服务侧的连接ID
    uint32_t heartbeatCounter;            // 详细心跳包的发送计时器
    uint32_t heartbeatInterval;           // 详细心跳包的发送周期
    CltHeartBeatInfoT heartbeatInfo;      // 心跳发送和应答的统计
    CltConnDfxInfoT connDfxInfo;          // 通信统计信息
    CltTimeCostT timeCost;                // 写请求视图消耗时间
    CltOpSegTimeT cltOpSegTime;           // 写请求耗时日志数据保存的结构体
    CltDynMemT cltDynMem;                 // 连接menCtx动态内存统计信息
    CltSendObjT cltSendObj;               // 发送报文大小统计信息
    CltRecvObjT cltRecvObj;               // 接收报文大小统计信息
    uint64_t bigObjectStand;              // 超大报文阈值
    uint32_t sid;                         // sessionId，仅供直连写在初始化句柄时使用
    uint32_t singleCallbackTimeoutMs;     // 单条回调函数超时阈值，订阅、异步有效
    uint32_t avgCallbackTimeoutMs;        // 平均回调函数超时阈值，仅订阅有效
    uint32_t asyncTimeoutMs;              // 异步消息超时时间阈值
    uint32_t stmtAllocSuccNums;           // 申请stmt成功的次数
    uint32_t stmtAllocFailNums;           // 申请stmt失败的次数
    uint64_t csCommTotalTime;             // 记录通信dfx的总时间，用以求平均值
    uint64_t csCommCount;                 // 记录通信dfx的统计次数，用以求平均值
    uint64_t serverSend2CliSubCbCost;     // 记录从服务端发送第一条报文到客户端进入SubPushCb的时间
    CltDirectWriteTimeCostT *dwTimeCost;  // 直连写耗时日志数据保存的结构体
    CltConnStageTimePointT connStageTimePoint;
};
DB_CHECK_ALIGN_ATOMIC64_MEMBER(GmcConnT, connDfxInfo);

Status CltConnect(GmcConnTypeE connType, const char *serverLocator, GmcConnOptionsT *connOptions, GmcConnT **conn);
Status CltConnectResource(DbMemCtxT *memCtx, const DbLctrT *lctr, GmcConnOptionsT *options, GmcConnT *conn);
void CltDisconnect(GmcConnT *conn);
void CltHeartBeatConnect(GmcConnOptionsT *options, uint32_t instanceId);

// 处理timeconsumption
void CltSetTimeCost(GmcConnT *conn, uint64_t startCycles, uint64_t stopCycles);
void RefreshTimeCostData(CltTimeCostT *timeCons, uint64_t stopCycles);
// 慢操作耗时日志相关函数
void CltResetOpTimeCostLogVal(GmcConnT *conn);

static inline uint32_t GetOpCode(FixBufferT *buffer)
{
    OpHeaderT *op = ProtocolPeekFirstOpHeader(buffer);
    return op->opCode;
}

static inline uint32_t GetSeriNumber(FixBufferT *buffer)
{
    MsgHeaderT *msg = RpcPeekMsgHeader(buffer);
    return msg->serialNumber;
}

inline static void CltInitCltControlChannelInfo(void)
{
    DbInitCltControlChannelInfo();
}

inline static void CltUnInitCltControlChannelInfo(void)
{
    DbUninitCltControlChannelInfo();
}

Status CltAttachAndRefreshConn(GmcConnT *conn, GmcStmtT *stmt, bool refreshBuf);
void CltDetachAndReleaseConn(GmcConnT *conn, GmcStmtT *stmt, bool refreshBuf);
Status CltGetConnAttr(const GmcConnT *conn, GmcConnAttrTypeE attr, void *value, uint32_t valueSize);

static inline uint32_t CltConnIsAsyncLob(const GmcConnT *conn)
{
    return conn->isLobConn && conn->connType == GMC_CONN_TYPE_ASYNC;
}

#ifdef __cplusplus
}
#endif

#endif /* CLT_CONN_H */
