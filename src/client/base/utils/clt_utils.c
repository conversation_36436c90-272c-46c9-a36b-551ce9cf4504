/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: clt_common.c
 * Description: Implement of GMDB client common
 * Author: xxietao
 * Create: 2020-8-14
 */

#include "clt_utils.h"
#include "clt_error.h"
#include "dm_yang_union.h"
#include "db_secure_msg_buffer.h"

static Status SetNeedMallocDmValue(DmValueT *dmv, uint32_t type, const void *value, uint32_t size)
{
    // for DB_DATATYPE_STRING, length = strlen + 1
    if (type == DB_DATATYPE_STRING) {
        if (SECUREC_UNLIKELY(size != strlen(value))) {
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        dmv->value.constStrAddr = value;
        dmv->value.length = size + 1;
        return GMERR_OK;
    }

    // for DB_DATATYPE_BITMAP, beginPos and endPos should be set
    if (type == DB_DATATYPE_BITMAP) {
        if (SECUREC_UNLIKELY(size != sizeof(GmcBitMapT))) {
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        const GmcBitMapT *bmp = value;
        if (SECUREC_UNLIKELY(bmp->endPos < bmp->beginPos)) {
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        dmv->value.strAddr = bmp->bits;
        dmv->value.beginPos = bmp->beginPos;
        dmv->value.endPos = bmp->endPos;
        dmv->value.length = (uint32_t)((bmp->endPos - bmp->beginPos) + 1) / (uint32_t)BYTE_LENGTH;
        return GMERR_OK;
    }

    // DB_DATATYPE_BYTES, DB_DATATYPE_UNION and DB_DATATYPE_FIXED
    dmv->value.constStrAddr = value;
    dmv->value.length = size;
    return GMERR_OK;
}

// 目前只支持了type是string或fixed类型
inline Status SetDmValueWithCopy(DbMemCtxT *memCtx, DmValueT *dmv, uint32_t type, const void *value, uint32_t size)
{
    // convert NULL to DB_DATATYPE_NULL
    if (SECUREC_UNLIKELY(value == NULL || (type == DB_DATATYPE_BYTES && size == 0))) {
        dmv->type = DB_DATATYPE_NULL;
        return GMERR_OK;
    }
    dmv->type = type;

    uint8_t *buf = (uint8_t *)DbDynMemCtxAlloc(memCtx, size + 1);
    if (buf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc buf size %" PRIu32 ".", size);
        return GMERR_OUT_OF_MEMORY;
    }
    Status ret = GMERR_OK;
    errno_t err = memcpy_s(buf, size, value, size);
    if (SECUREC_UNLIKELY(err != EOK)) {
        ret = GMERR_FIELD_OVERFLOW;
        DB_LOG_AND_SET_LASERR(ret, "copy DM value, ret val %d", err);
        goto FAIL;
    }
    buf[size] = '\0';
    ret = SetNeedMallocDmValue(dmv, type, buf, size);
    if (ret != GMERR_OK) {
        goto FAIL;
    }
    return GMERR_OK;

FAIL:
    DbDynMemCtxFree(memCtx, buf);
    return ret;
}

// function is inline to optimize performance of SetDmValueForFilter
inline Status SetDmValue(DmValueT *dmv, uint32_t type, const void *value, uint32_t size)
{
    // !!! this is UNSIGNED comparison !!!
    if (SECUREC_UNLIKELY(type >= DB_DATATYPE_UNDEFINED)) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    // convert NULL to DB_DATATYPE_NULL
    if (SECUREC_UNLIKELY(type == DB_DATATYPE_NULL || value == NULL || (type == DB_DATATYPE_BYTES && size == 0))) {
        dmv->type = DB_DATATYPE_NULL;
        return GMERR_OK;
    }

    dmv->type = type;

    if (SECUREC_UNLIKELY(DM_TYPE_NEED_MALLOC(type))) {
        return SetNeedMallocDmValue(dmv, type, value, size);
    }

    // for non DM_TYPE_NEED_MALLOC types, caller should check size and set value
    uint32_t expected = DmGetBasicDataTypeLength(type);
    if (SECUREC_UNLIKELY(size != expected)) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    switch (type) {
        case DB_DATATYPE_INT8:
        case DB_DATATYPE_UINT8:
        case DB_DATATYPE_EMPTY:
            *((uint8_t *)(&dmv->value)) = *(const uint8_t *)value;
            return GMERR_OK;
        case DB_DATATYPE_INT16:
        case DB_DATATYPE_UINT16:
            *((uint16_t *)(&dmv->value)) = *(const uint16_t *)value;
            return GMERR_OK;
        case DB_DATATYPE_ENUM:
        case DB_DATATYPE_IDENTITY:
        case DB_DATATYPE_INT32:
        case DB_DATATYPE_UINT32:
            *((uint32_t *)(&dmv->value)) = *(const uint32_t *)value;
            return GMERR_OK;
        case DB_DATATYPE_INT64:
        case DB_DATATYPE_UINT64:
            *((uint64_t *)(&dmv->value)) = *(const uint64_t *)value;
            return GMERR_OK;
        default: {
            errno_t err = memcpy_s(&dmv->value, expected, value, size);
            if (SECUREC_UNLIKELY(err != EOK)) {
                DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "copy DM val, ret val %d", err);
                return GMERR_FIELD_OVERFLOW;
            }
            break;
        }
    }

    // valid partition range is [0, DM_MAX_PARTITION_ID)
    bool ok = (type != DB_DATATYPE_PARTITION || dmv->value.partitionValue < DM_MAX_PARTITION_ID);
    return ok ? GMERR_OK : GMERR_INVALID_PARAMETER_VALUE;
}

Status SetDmValueForFilter(DmValueT *dmv, uint32_t type, const void *value, uint32_t size)
{
    // 约束参考 QryIndexCheckIsPropertySupport
    DbDataTypeE typeE = (DbDataTypeE)type;

    // DmIndexKeyT序列化未支持bitmap类型
    // DmIndexKeyT序列化支持doule、float、bitfield8~64、partition类型，但索引字段中包含这些类型的功能未对外开放
    if (typeE == DB_DATATYPE_BITMAP || typeE == DB_DATATYPE_DOUBLE || typeE == DB_DATATYPE_FLOAT ||
        (typeE >= DB_DATATYPE_BITFIELD8 && typeE <= DB_DATATYPE_PARTITION)) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
#ifdef FEATURE_YANG
    if (typeE == DB_DATATYPE_EMPTY) {
        return SetDmValue(dmv, type, value, DmYangGetEmptyStoreSize());
    }
#endif

    // call may be inlined and useless branches may be removed
    return SetDmValue(dmv, type, value, size);
}

Status GetDmValue(const DmValueT *dmv, void *value, uint32_t capacity, bool *null)
{
    // !!! this is UNSIGNED comparison !!!
    // to improve robustness, if type is invalid, implicitly treat it as null
    uint32_t type = (uint32_t)dmv->type;
    *null = (type == DB_DATATYPE_NULL || type >= DB_DATATYPE_UNDEFINED);
    if (*null) {
        return GMERR_OK;
    }

    // for DM_TYPE_NEED_MALLOC types, value should be directly copied by DM interface
    if (DM_TYPE_NEED_MALLOC(dmv->type)) {
        DB_ASSERT(dmv->value.strAddr == value);
        DB_ASSERT(dmv->value.length <= capacity);
        return GMERR_OK;
    }

    uint32_t size = DmGetBasicDataTypeLength(dmv->type);
    if (capacity < size) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "size for get value");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    errno_t err = memcpy_s(value, capacity, &dmv->value, size);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "Memcpy when get dm value, ret val %d", err);
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}

Status GetDmValueWithCopy(const DmValueT *dmv, void *value, uint32_t capacity, bool *null)
{
    // !!! this is UNSIGNED comparison !!!
    // to improve robustness, if type is invalid, implicitly treat it as null
    uint32_t type = (uint32_t)dmv->type;
    *null = (type >= DB_DATATYPE_UNDEFINED);
    if (*null) {
        return GMERR_OK;
    }

    // copy value for DM_TYPE_NEED_MALLOC types
    if (DM_TYPE_NEED_MALLOC(dmv->type)) {
        if (dmv->value.length == 0) {
            // 受到DM实现限制，dmv->value.strAddr此时可能为NULL，因此不能直接调用memcpy
            return GMERR_OK;
        }
        if (dmv->value.length <= capacity) {
            errno_t err = memcpy_s(value, capacity, dmv->value.strAddr, dmv->value.length);
            if (SECUREC_UNLIKELY(err != EOK)) {
                DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "copy DMV val, ret val %d", err);
                return GMERR_FIELD_OVERFLOW;
            }
            return GMERR_OK;
        }
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, " Len(%u) of variant prop val is shorter than that(%u) in buf.",
            capacity, dmv->value.length);
        return GMERR_DATA_EXCEPTION;
    }

    uint32_t size = DmGetBasicDataTypeLength(dmv->type);
    if (capacity < size) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    errno_t err = memcpy_s(value, capacity, &dmv->value, size);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "copy DM value, ret val %d", err);
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}

Status CltSetValueToDlrDataBuf(GmcDlrDataBufT *dataBufT, void *srcValue, uint32_t size, bool isAlign)
{
    CltDlrDataBufHeaderT *bufHeader = (CltDlrDataBufHeaderT *)dataBufT->buf;
    if (bufHeader->bufSize < size) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "dlr data bufSize %" PRIu32 "is smaller than the size %" PRIu32 "to set.", bufHeader->bufSize, size);
        return GMERR_DATA_EXCEPTION;
    }
    if (bufHeader->pos > bufHeader->bufSize - size) {
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW,
            "dlr data buf size, three value is %" PRIu32 ", %" PRIu32 ", %" PRIu32 ".", bufHeader->pos,
            bufHeader->bufSize, size);
        return GMERR_FIELD_OVERFLOW;
    }
    errno_t err = memcpy_s(dataBufT->buf + (bufHeader->pos), size, srcValue, size);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "copy val to dlr user databuf.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    bufHeader->pos = bufHeader->pos + size;
    if (isAlign) {
        bufHeader->pos = SIZE_ALIGN4(bufHeader->pos);
    }
    dataBufT->writtenLength = bufHeader->pos;
    return GMERR_OK;
}

Status CltGetValueFromDlrDataBuf(GmcDlrDataBufT *dataBufT, void *dstValue, uint32_t size, bool isAlign)
{
    CltDlrDataBufHeaderT *bufHeader = (CltDlrDataBufHeaderT *)dataBufT->buf;
    if (bufHeader->bufSize < size) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "dlr data buf size %" PRIu32 "is smaller than the size %" PRIu32 "to get.", bufHeader->bufSize, size);
        return GMERR_DATA_EXCEPTION;
    }
    if (bufHeader->seekPos > bufHeader->bufSize - size) {
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW,
            "dlr data buf, three value is %" PRIu32 ", %" PRIu32 ", %" PRIu32 ".", bufHeader->seekPos,
            bufHeader->bufSize, size);
        return GMERR_FIELD_OVERFLOW;
    }
    errno_t err = memcpy_s(dstValue, size, dataBufT->buf + (bufHeader->seekPos), size);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "get data from dlr databuf.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    bufHeader->seekPos = bufHeader->seekPos + size;
    if (isAlign) {
        bufHeader->seekPos = SIZE_ALIGN4(bufHeader->seekPos);
    }
    return GMERR_OK;
}

Status CltDlrDataBufInit(GmcDlrDataBufT *dataBuf, char *buf, uint32_t bufSize)
{
    dataBuf->buf = buf;
    dataBuf->bufSize = bufSize;
    if (dataBuf->bufSize <= DLR_BUF_HEADER_ALIGN_SIZE) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "init dlr data buf size.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    CltDlrDataBufHeaderT *bufHeader = (CltDlrDataBufHeaderT *)dataBuf->buf;
    bufHeader->operationNums = 0;
    bufHeader->writeNums = 0;
    bufHeader->pos = DLR_BUF_HEADER_ALIGN_SIZE;
    bufHeader->seekPos = DLR_BUF_HEADER_ALIGN_SIZE;
    bufHeader->bufSize = dataBuf->bufSize;
    bufHeader->maxFetchNum = DLR_DEFAULT_FETCH_NUM;
    bufHeader->magic = DLR_DATA_BUFF_MAGIC_NUM;
    bufHeader->isInitialLoadEOF = false;
    bufHeader->isInitial = true;
    dataBuf->writtenLength = bufHeader->pos;
    return GMERR_OK;
}

void CltDlrDataBufHeaderReset(GmcDlrDataBufT *dataBuf)
{
    CltDlrDataBufHeaderT *bufHeader = (CltDlrDataBufHeaderT *)dataBuf->buf;
    bufHeader->operationNums = 0;
    bufHeader->pos = DLR_BUF_HEADER_ALIGN_SIZE;
    bufHeader->seekPos = DLR_BUF_HEADER_ALIGN_SIZE;
    bufHeader->bufSize = dataBuf->bufSize;
    bufHeader->isInitialLoadEOF = false;
    bufHeader->isInitial = true;
    dataBuf->writtenLength = bufHeader->pos;
}

Status CltGetDlrDataBufWithoutCopy(GmcDlrDataBufT *dataBufT, TextT *tmpVertex)
{
    CltDlrDataBufHeaderT *bufHeader = (CltDlrDataBufHeaderT *)dataBufT->buf;
    if (bufHeader->bufSize < tmpVertex->len) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "dlr data buf size is smaller than the size to get, two value is %" PRIu32 ", %" PRIu32 ".",
            bufHeader->bufSize, tmpVertex->len);
        return GMERR_DATA_EXCEPTION;
    }
    if (bufHeader->seekPos > bufHeader->bufSize - tmpVertex->len) {
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW,
            "dlr data buf is fetch over, three value is %" PRIu32 ", %" PRIu32 ", %" PRIu32 ".", bufHeader->seekPos,
            bufHeader->bufSize, tmpVertex->len);
        return GMERR_FIELD_OVERFLOW;
    }
    tmpVertex->str = dataBufT->buf + (bufHeader->seekPos);
    bufHeader->seekPos = bufHeader->seekPos + tmpVertex->len;
    bufHeader->seekPos = SIZE_ALIGN4(bufHeader->seekPos);
    return GMERR_OK;
}

#ifdef FEATURE_HP_ANNSEQSCAN
#include "dm_data_record.h"

static void CltInitPrioQueItem(uint8_t *item, DbMemCtxT *memctx)
{
    FixBufInit((FixBufferT *)item, NULL, 0, 0, FIX_BUF_FLAG_EXTEND_BUFFER, memctx);
}

static void CltResetPrioQueItem(uint8_t *item)
{
    FixBufResetMem((FixBufferT *)item);
}

static void CltReleasePrioQueItem(uint8_t *item)
{
    FixBufRelease((FixBufferT *)item);
}

static Status CltPutPrioQueItem(uint8_t *item, uint8_t *buf, uint32_t bufSize)
{
    return SecureFixBufPutRawText((FixBufferT *)item, bufSize, buf);
}

DbSimilPrioQueT *CltReCreatePrioQue(DbMemCtxT *memctx, uint32_t limitCount, DbSimilPrioQueT *oldQue)
{
    DB_POINTER(memctx);

    DbSimilPrioQueFuncsT funcs = {.initItem = CltInitPrioQueItem,
        .resetItem = CltResetPrioQueItem,
        .releaseItem = CltReleasePrioQueItem,
        .putItem = CltPutPrioQueItem};

    return DbReCreateSimilPrioQue(oldQue, limitCount, memctx, sizeof(FixBufferT), funcs);
}

Status CltSetAutoQuantCodeProp(DmVertexT *vertex, DmValueT *value, DmAutoQuantPairT *pair)
{
    DB_POINTER3(vertex, value, pair);
    DmPropertyInfoT *propeInfos = vertex->vertexDesc->recordDesc->propeInfos;
    if (value->type != propeInfos[pair->source].dataType) {
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "Prop %u val type!", pair->source);
        return GMERR_DATATYPE_MISMATCH;
    }
    if (value->value.length % sizeof(float) != 0) {
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "Input vector size %u.", value->value.length);
        return GMERR_DATATYPE_MISMATCH;
    }
    uint32_t dim = DmAutoQuantGetDimFromFixedSize(value->value.length);
    uint32_t codeSize = DbLVQCodeSize(dim, pair->nCodeBits);
    if (pair->property == pair->source) {
        if (codeSize != propeInfos[pair->property].propeMaxLen) {
            DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "Input vector size %u.", value->value.length);
            return GMERR_DATATYPE_MISMATCH;
        }
    } else {
        if (value->value.length != propeInfos[pair->source].propeMaxLen) {
            DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "Prop %u val len!", pair->source);
            return GMERR_DATATYPE_MISMATCH;
        }
    }
    DmValueT quantValue;
    quantValue.type = propeInfos[pair->property].dataType;
    quantValue.value.length = codeSize;
    quantValue.value.strAddr = DbMalloc(codeSize);
    if (quantValue.value.strAddr == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    DbLVQCodeT *lvqCode = DbLVQCodeAttach(quantValue.value.strAddr);
    DbLVQQuantize(lvqCode, dim, pair->nCodeBits, (const float *)value->value.strAddr);
    Status ret = DmVertexSetPropeById(pair->property, quantValue, vertex);
    DbFree(quantValue.value.strAddr);
    return ret;
}
#endif
