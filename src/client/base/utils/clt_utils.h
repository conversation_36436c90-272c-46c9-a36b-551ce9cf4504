/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2021. All rights reserved.
 * File Name: clt_utils.h
 * Description: header file for GMDB client common
 * Author: xxietao
 * Create: 2020-8-14
 */

#ifndef CLT_UTILS_H
#define CLT_UTILS_H

#include <securec.h>
#include "dm_data_basic.h"
#include "gmc_dlr.h"
#include "clt_dlr.h"

#ifdef FEATURE_HP_ANNSEQSCAN
#include "db_msg_buffer.h"
#include "db_vector_distance.h"
#include "db_vector_quantization.h"
#include "dm_auto_quant.h"
#include "dm_data_prop_seri.h"
#include "db_similarity_priority_queue.h"
#endif

#ifdef __cplusplus
extern "C" {
#endif

inline static bool IsStringEmpty(const char *str)
{
    if (SECUREC_UNLIKELY(str == NULL || *str == '\0')) {
        // 这个分支一般走不到，添加预测，可影响内联后代码生成
        return true;
    }
    return false;
}

inline static uint32_t CltStrLen(const char *str)
{
    if (str == NULL) {
        return 0;
    }
    size_t len = strlen(str);
    return (uint32_t)(len > 0 ? len + 1 : len);
}

// 对于DM_NEED_MALLOC类型，一个由DM拷贝，一个由客户端拷贝。
// 这里后续可以统一成从DM读取数据时不拷贝，客户端负责拷贝
Status GetDmValue(const DmValueT *dmv, void *value, uint32_t capacity, bool *null);
Status GetDmValueWithCopy(const DmValueT *dmv, void *value, uint32_t capacity, bool *null);

// 将用户输入转换成DM的数据结构。SetDmValueForFilter比SetDmValue有更多限制。
GMDB_EXPORT Status SetDmValue(DmValueT *dmv, uint32_t type, const void *value, uint32_t size);
GMDB_EXPORT Status SetDmValueWithCopy(
    DbMemCtxT *memCtx, DmValueT *dmv, uint32_t type, const void *value, uint32_t size);
Status SetDmValueForFilter(DmValueT *dmv, uint32_t type, const void *value, uint32_t size);

Status CltSetValueToDlrDataBuf(GmcDlrDataBufT *dataBufT, void *srcValue, uint32_t size, bool isAlign);
Status CltGetValueFromDlrDataBuf(GmcDlrDataBufT *dataBufT, void *dstValue, uint32_t size, bool isAlign);
Status CltDlrDataBufInit(GmcDlrDataBufT *dataBuf, char *buf, uint32_t bufSize);
Status CltGetDlrDataBufWithoutCopy(GmcDlrDataBufT *dataBufT, TextT *tmpVertex);
void CltDlrDataBufHeaderReset(GmcDlrDataBufT *dataBuf);

#ifdef FEATURE_HP_ANNSEQSCAN

DbSimilPrioQueT *CltReCreatePrioQue(DbMemCtxT *memctx, uint32_t limitCount, DbSimilPrioQueT *oldQue);

typedef struct VectorSearchCtx {
    bool isAnnSearch;
    DbVectorMetricE metric;
    DmAutoQuantPairT quantPair;
    uint8_t *serializeBuf;
    uint8_t *calcAddr;
    uint32_t offset;
    uint32_t serializeBufLen;
    DbSimilPrioQueT *prioQue;
} VectorSearchCtxT;

static inline void VectorSearchCtxInit(VectorSearchCtxT *ctx)
{
    ctx->isAnnSearch = false;
    ctx->metric = DB_VECTOR_METRIC_BUTT;
    DmInitAutoQuantPair(&ctx->quantPair);
    ctx->prioQue = NULL;
    ctx->serializeBuf = NULL;
    ctx->serializeBufLen = 0;
    ctx->calcAddr = NULL;
    ctx->offset = DB_INVALID_UINT32;
}

static inline void VectorSearchCtxReset(VectorSearchCtxT *ctx)
{
    ctx->isAnnSearch = false;
    ctx->metric = DB_VECTOR_METRIC_BUTT;
    DmInitAutoQuantPair(&ctx->quantPair);
}

Status CltSetAutoQuantCodeProp(DmVertexT *vertex, DmValueT *value, DmAutoQuantPairT *pair);
#endif

#ifdef __cplusplus
}
#endif

#endif /* CLT_UTILS_H */
