/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: header file for dlr
 * Author: Chengzahngpei
 * Create: 2021-04-09
 */

#ifndef CLT_DLR_H
#define CLT_DLR_H

#include "adpt_define.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    uint32_t operationNums;  // 这一批buf中有几个vertex或者key(delete操作),也就是重演的时候batch需要插入几次，
    uint32_t writeNums;  // 重演的时候，用来计数已经往batch中插入了几次了
    uint32_t pos;        // buf写指针
    uint32_t seekPos;    // buf读指针
    uint32_t bufSize;
    uint32_t maxFetchNum;  // 单次数据buf获取的最大记录数
    uint32_t magic;
    bool isInitialLoadEOF;  // 全量订阅获取全量数据是否已经结束
    bool isInitial;
} CltDlrDataBufHeaderT;

#define DLR_BUF_HEADER_ALIGN_SIZE (SIZE_ALIGN4(sizeof(CltDlrDataBufHeaderT)))
#define DLR_MAX_FETCH_NUM 1024
#define DLR_DEFAULT_FETCH_NUM 100
#define DLR_DATA_BUFF_MAGIC_NUM 0xabcdabcd

#ifdef __cplusplus
}
#endif

#endif /* CLT_DLR_H */
