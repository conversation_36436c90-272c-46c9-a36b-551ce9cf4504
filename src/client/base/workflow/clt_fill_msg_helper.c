/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: client fill msg helper
 * Author: GMDBV5 Team
 * Create: 2024-09-18
 */

#include "clt_fill_msg.h"
#include "db_secure_msg_buffer.h"
#include "clt_stmt.h"
#include "clt_msg.h"

/**
 * copy a simple object to buffer.
 * simple object means means object that only contains POD types (except pointers)
 */
Status HelpFillSimpleObject(FixBufferT *buf, const void *obj, uint32_t objSize)
{
    return FixBufPutData(buf, obj, objSize);
}

// if handleNull, will put a 0 into buf when we meet a NULL string, else directly return
inline Status HelpFillString(FixBufferT *buf, bool handleNull, const char *str)
{
    Status ret = GMERR_OK;
    if (str != NULL) {
        uint32_t size = CltStrLen(str);
        ret = SecureFixBufPutRawText(buf, size, (const uint8_t *)str);
    } else if (handleNull) {
        ret = FixBufPutUint32(buf, 0);
    }
    return ret;
}

/**
 * if handleNull is true, a 0 will be put into buf for len when we meet a NULL string
 * else, the NULL string will be directly skipped
 */
Status HelpFillMultiString(FixBufferT *buf, bool handleNull, uint32_t strNum, ...)
{
    Status ret = GMERR_OK;
    const char *str = NULL;
    va_list strList;
    va_start(strList, strNum);
    for (uint32_t count = strNum; count > 0; --count) {
        str = va_arg(strList, const char *);
        ret = HelpFillString(buf, handleNull, str);
        if (ret != GMERR_OK) {
            break;
        }
    }
    va_end(strList);
    return ret;
}

// if vertex is NULL, will cause error
Status HelpFillVertex(FixBufferT *buf, DmVertexT *vertex)
{
    uint32_t len;
    Status ret = DmVertexGetSeriBufLength(vertex, &len);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // fill vertex len
    ret = FixBufPutUint32(buf, len);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // fill vertex
    uint32_t offset;
    ret = SecureFixBufReserveDataOffset(buf, len, &offset);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    uint8_t *vbuf = SecureFixBufOffsetToAddr(buf, offset);
#ifdef SECUREFIXBUF
    if (SECUREC_UNLIKELY(vbuf == NULL)) {
        return GMERR_CONNECTION_RESET_BY_PEER;
    }
#endif
    return DmSerializeVertex2InvokerBuf(vertex, len, vbuf);
}

// if edge is NULL, will cause error
Status HelpFillEdge(FixBufferT *buf, const DmEdgeT *edge)
{
    uint32_t len = DmEdgeGetSeriBufLength(edge);
    // fill edge len
    Status ret = FixBufPutUint32(buf, len);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill edge
    uint32_t offset;
    ret = SecureFixBufReserveDataOffset(buf, len, &offset);
    if (ret != GMERR_OK) {
        return ret;
    }
    void *vbuf = SecureFixBufOffsetToAddr(buf, offset);
#ifdef SECUREFIXBUF
    if (SECUREC_UNLIKELY(vbuf == NULL)) {
        return GMERR_CONNECTION_RESET_BY_PEER;
    }
#endif
    return DmSerializeEdge2InvokerBuf(edge, len, vbuf);
}

// if indexKey is NULL, will fill len 0
Status HelpFillIndexKey(FixBufferT *buf, const DmIndexKeyT *indexKey)
{
    if (SECUREC_UNLIKELY(indexKey == NULL)) {
        return FixBufPutUint32(buf, 0);
    }
    uint32_t len = DmIndexKeyGetSeriBufLength(indexKey);
    // fill indexKey len
    Status ret = FixBufPutUint32(buf, len);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // fill indexKey
    uint32_t offset;
    ret = SecureFixBufReserveDataOffset(buf, len, &offset);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    uint8_t *vbuf = SecureFixBufOffsetToAddr(buf, offset);
#ifdef SECUREFIXBUF
    if (SECUREC_UNLIKELY(vbuf == NULL)) {
        return GMERR_CONNECTION_RESET_BY_PEER;
    }
#endif
    return DmSerializeIndexKey2InvokerBuf(indexKey, len, vbuf);
}

Status HelpPutDmValue(FixBufferT *buf, const DmValueT *value)
{
    uint32_t size = DmValueGetSeriLen(value);
    Status ret = FixBufPutUint32(buf, size);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t offset;
    ret = SecureFixBufReserveDataOffset(buf, size, &offset);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint8_t *addr = SecureFixBufOffsetToAddr(buf, offset);
#ifdef SECUREFIXBUF
    if (SECUREC_UNLIKELY(addr == NULL)) {
        return GMERR_CONNECTION_RESET_BY_PEER;
    }
#endif
    return DmValueSeri2InvokerBuf(value, addr);
}

#ifdef FEATURE_GQL
Status HelpFillStructFilterList(FixBufferT *buf, const StructFilterListT *structFilterList)
{
    if (structFilterList == NULL || !structFilterList->isUsed || structFilterList->list == NULL) {
        return FixBufPutUint32(buf, 0);
    }
    DbListT *list = structFilterList->list;
    uint32_t filterCnt = DbListGetItemCnt(list);
    uint32_t offset;
    Status ret = FixBufReserveDataOffset(buf, sizeof(uint32_t), &offset);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t validFilterCnt = 0;
    for (uint32_t i = 0; i < filterCnt; i++) {
        FilterInnerT *filter = (FilterInnerT *)DbListItem(list, i);
        if (!filter->setValue || filter->compOp != 0) {
            // 为赋值的filter或者比较符不是等值的 直接skip
            continue;
        }
        ret = FixBufPutUint32(buf, filter->fieldId);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = FixBufPutInt32(buf, 0);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = HelpPutDmValue(buf, &filter->value);
        if (ret != GMERR_OK) {
            return ret;
        }
        validFilterCnt++;
    }
    return FixBufPutReservedData(buf, offset, &validFilterCnt, sizeof(uint32_t));
}

Status HelpFillStructFilterListByVertex(FixBufferT *buf, const CltOperVertexT *op)
{
    uint32_t offset;
    Status ret = FixBufReserveDataOffset(buf, sizeof(uint32_t), &offset);
    if (ret != GMERR_OK) {
        return ret;
    }
    // set FilterStructure
    uint32_t validFilterCnt = 0;
    DmVertexLabelT *vertexLabel = op->cltCataLabel->vertexLabel;
    RecordDescT *record = op->vertex->record->recordDesc;
    for (uint32_t i = 0; i < record->propeNum; ++i) {
        DmPropertyInfoT *prop = record->propeInfos;
        if (!prop->isFixed) {
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "variable field for get tbl name %s's schema len.",
                vertexLabel->metaCommon.metaName);
            return ret;
        }
        DmValueT propeValue;
        ret = DmVertexGetPropeByIdNoCopy(op->vertex, prop->propeId, &propeValue);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "get prop val. the table name is %s, the prope name is %s.",
                vertexLabel->metaCommon.metaName, prop->propeName);
            return ret;
        }
        ret = FixBufPutUint32(buf, prop->propeId);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = FixBufPutInt32(buf, 0);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = HelpPutDmValue(buf, &propeValue);
        if (ret != GMERR_OK) {
            return ret;
        }
        validFilterCnt++;
    }
    return FixBufPutReservedData(buf, offset, &validFilterCnt, sizeof(uint32_t));
}
#endif  // FEATURE_GQL

Status HelpFillPosInfo(const DmIndexKeyT *uniqFilter, DmListPosE pos, FixBufferT *fixBuffer)
{
    Status ret = FixBufPutUint32(fixBuffer, (uint32_t)pos);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Fill pos");
        return ret;
    }
    if (uniqFilter == NULL) {
        ret = FixBufPutUint32(fixBuffer, 0);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Fill zero len flag in msg.");
            return ret;
        }
        return GMERR_OK;
    }

    uint32_t len = DmIndexKeyGetSeriBufLength(uniqFilter);
    ret = FixBufPutUint32(fixBuffer, len);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Fill pos key len, len:%" PRIu32, len);
        return ret;
    }
    uint8_t *buf = SecureFixBufReserveData(fixBuffer, len);
    if (buf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Buf for set pos key.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    ret = DmSerializeIndexKey2InvokerBuf(uniqFilter, len, buf);
    return ret;
}
