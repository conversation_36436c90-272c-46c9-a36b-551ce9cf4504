/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: rpc message implement
 * Author:
 * Create: 2021-04-17
 */

#include "clt_msg.h"
#include "clt_msg_helper.h"

#include <sys/epoll.h>
#include "clt_async_conn.h"
#include "clt_flow_control.h"
#include "clt_resource.h"
#include "clt_check.h"
#include "clt_yang_errorpath.h"
#include "adpt_io.h"
#include "clt_error.h"
#ifdef PERF_SAMPLE_STAT
#include "db_perf_stat.h"
extern PerfDataT g_clientSend[MAX_CONN_NUM];
extern PerfDataT g_clientAsyncSend[MAX_CONN_NUM];
extern PerfDataT g_clientRecv[MAX_CONN_NUM];
#endif

#define HEX_STR_LEN 2

#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
#define FREE_LIST_CACH_LIMIT 4
// 客户端接收的报文内存会通过该接口在下一次接收时push到freelist，供发送报文取用，订阅连接不复用
static void MsgBufferFreeListPush(MsgBufferList *list, FixBufferT *buffer)
{
    DB_ASSERT(list != NULL);
    DB_ASSERT(buffer != NULL);
    DB_ASSERT(buffer->memCtx->memType == DB_SHARED_MEMORY);

#ifndef NDEBUG
    if (list->magic != CLT_CONN_MAGIC_WORD) {
        DB_LOG_AND_SET_LASERR(
            GMERR_MEMORY_OPERATE_FAILED, "msg pool list trampled when push.magic code:%u", list->magic);
        DB_ASSERT(false);
    }
#endif

    const uint32_t cacheLimit = FREE_LIST_CACH_LIMIT;
    const uint32_t threshold = MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE;
    DB_ASSERT(threshold >= sizeof(MsgBufferList));  // msg本身应该有足够空间容纳元数据

    if (FixBufGetTotalLength(buffer) >= threshold &&  // 消息够大
        list->size < cacheLimit &&                    // 缓存个数没有超过限制（不要求准确）
        DbSpinTryLock(&list->lock)                    // 能够直接获得锁
    ) {
        void *buf = SecureFixBufGetBuf(buffer);
#ifdef SECUREFIXBUF
        if (SECUREC_UNLIKELY(buf == NULL)) {
            DbSpinUnlock(&list->lock);
            FixBufInit(buffer, NULL, 0, 0, buffer->flags, buffer->memCtx);
            return;
        }
#endif
        MsgBufferList *curr = buf;
        MsgBufferList *next = list->next;
        curr->next = next;
        curr->size = FixBufGetTotalLength(buffer);
        list->next = curr;
        list->size++;
        DbSpinUnlock(&list->lock);
        FixBufInit(buffer, NULL, 0, 0, buffer->flags, buffer->memCtx);
        return;  // added to free list
    }
    // 如果不放进freelist，则直接释放接收到的报文的内存
    SecureFixBufRelease(buffer);
}

// 客户端发送的报文内存会在发送后通过该接口从freelist中pop一个
static void MsgBufferFreeListPop(MsgBufferList *list, FixBufferT *buffer)
{
    DB_ASSERT(list != NULL);
    DB_ASSERT(buffer != NULL);
    DB_ASSERT(buffer->memCtx->memType == DB_SHARED_MEMORY);

#ifndef NDEBUG
    if (list->magic != CLT_CONN_MAGIC_WORD) {
        DB_LOG_AND_SET_LASERR(
            GMERR_MEMORY_OPERATE_FAILED, "msg pool list trampled when pop.magic code:%u", list->magic);
        DB_ASSERT(false);
    }
#endif

    MsgBufferList *curr = NULL;

    if (DbSpinTryLock(&list->lock)) {
        if (list->size > 0) {
            curr = list->next;
            DB_ASSERT(curr != NULL);
            MsgBufferList *next = curr->next;
            list->next = next;
            list->size--;
        }
        DbSpinUnlock(&list->lock);
    }
    if (curr != NULL) {
        FixBufInit(buffer, curr, curr->size, 0, buffer->flags, buffer->memCtx);
    } else {
        FixBufInit(buffer, NULL, 0, 0, buffer->flags, buffer->memCtx);
    }
}

void MsgBufferFreeListFreeAll(GmcConnT *conn)
{
    DB_ASSERT(conn != NULL);
#ifndef NDEBUG
    if (conn->msgPool.magic != CLT_CONN_MAGIC_WORD) {
        DB_LOG_AND_SET_LASERR(
            GMERR_MEMORY_OPERATE_FAILED, "msg pool list trampled when free all.magic code:%u", conn->msgPool.magic);
        DB_ASSERT(false);
    }
#endif
    SecureFixBufRelease(&conn->recvPack);
    SecureFixBufRelease(&conn->sendPack);
    for (MsgBufferList *list = conn->msgPool.next; list != NULL;) {
        MsgBufferList *item = list;
        list = item->next;
        FixBufferT *buffer = &conn->sendPack;
        FixBufInit(buffer, item, item->size, 0, buffer->flags, buffer->memCtx);
        SecureFixBufRelease(buffer);
    }
    conn->msgPool = (MsgBufferList){};
#ifndef NDEBUG
    conn->msgPool.magic = CLT_CONN_MAGIC_WORD;
#endif
}

#endif

#if defined(FUZZCOLLECT)
void CollectLibFuzzerPacket(FixBufferT *buffer)
{
    // 报文体是空的
    if (buffer->pos <= MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE) {
        return;
    }
    const char *dirPath = "./libFuzz_data/temp_corpus/";
    Status ret = GMERR_OK;
    if (!DbDirExist(dirPath)) {
        ret = DbMakeDirectory(dirPath, PERM_USRRWX);
        if (ret != GMERR_OK) {
            return;
        }
    }
    static uint64_t countWrite = 0;
    char fileName[DB_MAX_PATH] = {0};
    uint64_t timeStamp = DbRdtsc();
    ret = snprintf_s(fileName, DB_MAX_PATH, DB_MAX_PATH - 1, "%s%lu", dirPath, (uint64_t)(timeStamp + countWrite));
    if (ret < 0) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW, "Generate file path.");
        return;
    }
    int32_t fd = DB_INVALID_FD;
    ret = DbOpenFile(fileName, O_CREAT | O_WRONLY, S_IRWXU, &fd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Open file: %s.", fileName);
        return;
    }
    ret = DbWriteFile(fd, buffer->buf + MSG_HEADER_ALIGN_SIZE, buffer->pos - MSG_HEADER_ALIGN_SIZE);
    DbCloseFile(fd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Write buffer to file: %s.", fileName);
        return;
    }
    countWrite++;
}
#endif

static Status FlowControlAccordingToOpCode(GmcConnT *conn, MsgOpcodeRpcE opCode)
{
    // DML性能快速路径
    if (SECUREC_LIKELY((opCode >= MSG_OP_RPC_DML_BEGIN && opCode < MSG_OP_RPC_DML_END) || opCode == MSG_OP_RPC_BATCH)) {
        return FlowControlWhenSendRequest(&conn->flowControl);
    }

    // 部分opCode不受流控影响
    bool ignore = opCode <= MSG_OP_RPC_CLT_SHM_EXPAND || opCode == MSG_OP_RPC_GET_VERTEX_LABEL ||
                  opCode == MSG_OP_RPC_TX_CHECK_OPTIMISTIC_CONFLICT ||
                  (opCode >= MSG_OP_RPC_DCL_BEGIN && opCode <= MSG_OP_RPC_TX_ROLLBACK_SAVEPOINT);
    if (SECUREC_UNLIKELY(ignore)) {
        conn->flowControl.currentTime = DbGlobalRdtsc();
        return GMERR_OK;
    }

    return FlowControlWhenSendRequest(&conn->flowControl);
}

void UpdateConnSendSecureFixBufStatistics(GmcConnT *conn, const FixBufferT *buffer)
{
    CltSendObjT *sndObj = &conn->cltSendObj;
    // 当大对象发送数或普通对象发送数达到uint32最大值时，通过打印LOG记录当前的发送对象相关统计信息，然后重置
    if (sndObj->bigObjSendCount == DB_MAX_UINT32 || sndObj->normalObjSendCount == DB_MAX_UINT32) {
        DB_LOG_WARN(GMERR_PROGRAM_LIMIT_EXCEEDED,
            "Connection id is %" PRIu16 ", send object statistics: big object max size is %" PRIu32
            " bytes,  big object average size is %" PRIu32 " bytes, big object send count is %" PRIu32
            ". normal object max size is %" PRIu32 " bytes, normal object average size is %" PRIu32
            ", normal object send count is %" PRIu32 ".",
            conn->remoteId, sndObj->bigObjSendMaxSize, sndObj->bigObjSendAvgSize, sndObj->bigObjSendCount,
            sndObj->normalObjSendMaxSize, sndObj->normalObjSendAvgSize, sndObj->normalObjSendCount);
        sndObj->bigObjSendCount = 0;
        sndObj->bigObjSendMaxSize = 0;
        sndObj->bigObjSendAvgSize = 0;
        sndObj->normalObjSendCount = 0;
        sndObj->normalObjSendMaxSize = 0;
        sndObj->normalObjSendAvgSize = 0;
    }
    uint32_t size = FixBufGetPos(buffer);
    if (size > conn->bigObjectStand) {
        sndObj->bigObjSendCount++;
        sndObj->bigObjSendMaxSize = (size > sndObj->bigObjSendMaxSize) ? size : sndObj->bigObjSendMaxSize;
        uint64_t bigObjSendSize =
            ((uint64_t)(sndObj->bigObjSendCount - 1) * (uint64_t)sndObj->bigObjSendAvgSize + (uint64_t)size);
        sndObj->bigObjSendAvgSize = (uint32_t)(bigObjSendSize / sndObj->bigObjSendCount);
    } else {
        sndObj->normalObjSendCount++;
        sndObj->normalObjSendMaxSize = (size > sndObj->normalObjSendMaxSize) ? size : sndObj->normalObjSendMaxSize;
        uint64_t normalObjSendSize =
            ((uint64_t)(sndObj->normalObjSendCount - 1) * (uint64_t)sndObj->normalObjSendAvgSize + (uint64_t)size);
        sndObj->normalObjSendAvgSize = (uint32_t)(normalObjSendSize / sndObj->normalObjSendCount);
    }
}

static int32_t CltConnGetFd(const GmcConnT *conn)
{
    return DbPipeGetEventFd(&conn->pipe);
}

static inline Status ModEpollEvent(GmcConnT *conn)
{
    // 插入写消息队列成功后需要mod事件，事件类型需要保持和add一致
    int32_t connFd = CltConnGetFd(conn);
    uint32_t events = EPOLLIN | EPOLLOUT | EPOLLET;
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    if (conn->epollReg.epollRegType == EPOLLREG_WITH_USD) {
        GmcEpollRegWithUserDataT epollRegFunc = conn->epollReg.epollRegWithUsDFunc;
        if (epollRegFunc(ioCtx->getRelFd(connFd), GMC_EPOLL_MOD, events, conn->epollReg.userData) != 0) {
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "register fd %" PRIu32 " to epoll when epoll mod.", connFd);
            return GMERR_DATA_EXCEPTION;
        }
    } else if (conn->epollReg.epollRegType == EPOLLREG_WITH_CONN) {
        GmcEpollRegWithConnT epollRegFunc = conn->epollReg.epollRegWithConnFunc;
        if (epollRegFunc(ioCtx->getRelFd(connFd), GMC_EPOLL_MOD, events, conn, conn->epollReg.userData) != 0) {
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "register fd %" PRIu32 " to epoll when epoll mod.", connFd);
            return GMERR_DATA_EXCEPTION;
        }
    }
    return GMERR_OK;
}

static inline void FixBufShallowCopy(FixBufferT *src, FixBufferT *dst)
{
    DB_POINTER2(src, dst);
    *dst = *src;
    FixBufferT zero = {};
    zero.memCtx = src->memCtx;
    zero.flags = src->flags;
    zero.verify = src->verify;
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    zero.allocFunc = src->allocFunc;
    zero.freeFunc = src->freeFunc;
    zero.ctx = src->ctx;
#endif
    *src = zero;
}

static Status AsyncMessageBufferQueuePush(AsyncMsgBufferQueueT *queue, FixBufferT *buffer, GmcConnT *conn)
{
    DB_POINTER2(queue, buffer);
    DB_ASSERT(buffer->memCtx->memType == DB_DYNAMIC_MEMORY);

    DbSpinLock(&queue->lock);
    uint32_t maxBufferQueueSize = (buffer->flags & FIX_BUF_FLAG_LOB_BUFFER) != 0 ?
                                      MAX_ASYNC_MESSAGE_BUFFER_QUEUE_SIZE_LOB :
                                      MAX_ASYNC_MESSAGE_BUFFER_QUEUE_SIZE;
    uint32_t maxBufferQueueNum = (buffer->flags & FIX_BUF_FLAG_LOB_BUFFER) != 0 ?
                                     MAX_ASYNC_MESSAGE_BUFFER_QUEUE_NUMBER_LOB :
                                     MAX_ASYNC_MESSAGE_BUFFER_QUEUE_NUMBER;

    uint32_t nodeSize = (uint32_t)sizeof(AsyncMsgBufferNodeT) + FixBufGetPos(buffer);
    if ((queue->size + nodeSize) > maxBufferQueueSize || queue->count > maxBufferQueueNum) {
        DbSpinUnlock(&queue->lock);
        DB_LOG_ERROR(GMERR_CONNECTION_SEND_BUFFER_FULL, "Message buffer queue is full, need try again later.");
        return GMERR_CONNECTION_SEND_BUFFER_FULL;
    }
    // 该内存在该异步队列消息被发送时进行释放，通过调用AsyncMessageBufferQueuePop函数
    AsyncMsgBufferNodeT *node = DbDynMemCtxAlloc(queue->memCtx, sizeof(AsyncMsgBufferNodeT));
    if (SECUREC_UNLIKELY(node == NULL)) {
        DB_LOG_ERROR(
            GMERR_OUT_OF_MEMORY, "Alloc size %" PRIu32 "async msg buffer node.", (uint32_t)sizeof(AsyncMsgBufferNodeT));
        DbSpinUnlock(&queue->lock);
        return GMERR_OUT_OF_MEMORY;
    }
    node->next = NULL;
    FixBufShallowCopy(buffer, &node->nodeBuffer);
    if (queue->tail) {
        // 1.已经有至少一个节点
        queue->tail->next = node;
    } else {
        // 2.未创建节点
        queue->head = node;
    }
    queue->tail = node;
    queue->size += nodeSize;
    ++queue->count;

    conn->connDfxInfo.sendQueueSize = queue->size;
    DbSpinUnlock(&queue->lock);
    return GMERR_OK;
}

static void AsyncMessageBufferQueuePop(AsyncMsgBufferQueueT *queue)
{
    // 调用者保证对队列加锁
    AsyncMsgBufferNodeT *node = queue->head;
    DB_POINTER(node);

    size_t nodeSize = sizeof(AsyncMsgBufferNodeT) + FixBufGetPos(&node->nodeBuffer);
    queue->head = node->next;
    if (queue->head == NULL) {
        queue->tail = NULL;
    }
    queue->size -= nodeSize;
    --queue->count;

    FixBufRelease(&node->nodeBuffer);
    DbDynMemCtxFree(queue->memCtx, node);  // 正常释放内存，后续不会再用到
}

void ConsumeMessageBuffer(GmcConnT *conn)
{
    DB_POINTER(conn);
    AsyncMsgBufferQueueT *queue = &conn->sendMsgQueue;

    DbSpinLock(&queue->lock);
    const DbPipeT *pipe = &conn->pipe;
    Status ret;
    while (queue->head) {
        // 1. 取头结点
        AsyncMsgBufferNodeT *node = queue->head;
        // 2. 非阻塞发送

#if defined(MSG_DUMP)
        MsgDebugDump(&node->nodeBuffer, conn->remoteId, true);
#endif
        ret = DbPipeSendNoWait(pipe, &node->nodeBuffer);
        // 3. 后处理
        if (ret == GMERR_OK) {
            UpdateConnSendSize(conn, &node->nodeBuffer);
            uint32_t serinum = GetSeriNumber(&node->nodeBuffer);
            AsyncMessageBufferQueuePop(queue);
            // 只有pop成功了才sndSuccessCnt++
            conn->asyncReqLastTime.lastRequestTime = DbGettimeMonotonicUsec();
            conn->asyncReqLastTime.lastSuccessSerialNum = serinum;
            (void)DbAtomicInc(&conn->connDfxInfo.sndSuccessCnt);
            conn->connDfxInfo.sendQueueSize = queue->size;
        } else if (ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
            (void)DbAtomicAdd64(&(conn->connDfxInfo.connSendSize), (uint64_t)(node->nodeBuffer.seekPos));
            break;
        } else {
            conn->connStatus = CONN_IS_BROKEN;
            uint32_t opCode = GetOpCode(&node->nodeBuffer);
            DB_LOG_ERROR(ret,
                "Send buffer by epoll. conn id: %" PRIu16 ", serialNumber: %" PRIu32 ", opCode: %" PRIu32
                ", buffer send size is %" PRIu32 ".",
                conn->remoteId, conn->serialNumberSend, opCode, node->nodeBuffer.seekPos);
            break;
        }
    }
    DbSpinUnlock(&queue->lock);
}

#if !defined(RTOSV2) && !defined(RTOSV2X) && !defined(HPE)
Status CltSendRequestSocketBlock(GmcConnT *conn, FixBufferT *buffer, const AsyncMsgContextT *asyncCtx)
{
    DB_POINTER2(buffer, asyncCtx);
    Status ret = AddAsyncMsg(conn, asyncCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbPipeT *pipe = &conn->pipe;
    /* 发包务必保证seekpos为0，除非有重发机制，则保证首发为0 */
    DB_ASSERT(FixBufGetSeekPos(buffer) == 0);
#if defined(MSG_DUMP)
    MsgDebugDump(buffer, conn->remoteId, true);
#endif
    ret = DbPipeSend(pipe, buffer);
    if (ret != GMERR_OK) {
        RemoveAsyncMsg(conn, asyncCtx);
        return ret;
    }
    UpdateConnSendSize(conn, buffer);
    // 缩容：欧拉环境下，当用户设置packShrinkThreshold参数，sendBuffer会缩容
    ShrinkMsgBuffer(conn, buffer);
    return ret;
}

Status CltSendRequestSocketMQ(GmcConnT *conn, FixBufferT *buffer, const AsyncMsgContextT *asyncCtx)
{
    DB_POINTER2(buffer, asyncCtx);
    // 1.判断连接状态，如果异常，直接返回
    if (conn->connStatus == CONN_IS_BROKEN) {
        return GMERR_CONNECTION_RESET_BY_PEER;
    }

    // asyncCtx插入map
    Status ret = AddAsyncMsg(conn, asyncCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 2.判断队列是否不为空，是的话，消息插入队列即返回
    AsyncMsgBufferQueueT *queue = &conn->sendMsgQueue;
    if (queue->count > 0) {
        ret = AsyncMessageBufferQueuePush(queue, buffer, conn);
        if (ret != GMERR_OK) {
            goto FAILURE;
        }
        ret = ModEpollEvent(conn);
        if (ret != GMERR_OK) {
            goto FAILURE;
        }

        // 缩容：欧拉环境下，当用户设置packShrinkThreshold参数，sendBuffer会缩容
        ShrinkMsgBuffer(conn, buffer);
        return ret;
    }

    // 3.快路径，直接发送，否则插入队列
    const DbPipeT *pipe = &conn->pipe;
    uint32_t seekPosBeforeSend = FixBufGetSeekPos(buffer);
#if defined(MSG_DUMP)
    MsgDebugDump(buffer, conn->remoteId, true);
#endif
    ret = DbPipeSendNoWait(pipe, buffer);
    if (ret == GMERR_OK) {
        UpdateConnSendSize(conn, buffer);
    } else if (ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
        uint32_t seekPosAfterSend = FixBufGetSeekPos(buffer);
        (void)DbAtomicAdd64(&(conn->connDfxInfo.connSendSize), (uint64_t)(seekPosAfterSend - seekPosBeforeSend));
        // 发送部分字节，会将发送的size记录到buffer->seekPos
        ret = AsyncMessageBufferQueuePush(queue, buffer, conn);
        if (ret != GMERR_OK) {
            goto FAILURE;
        }
        ret = ModEpollEvent(conn);
        if (ret != GMERR_OK) {
            goto FAILURE;
        }
    } else if (ret != GMERR_OK) {
        // GMERR_CONNECTION_RESET_BY_PEER,从map清除AsyncCtx,直接返回
        goto FAILURE;
    }

    // 缩容：欧拉环境下，当用户设置packShrinkThreshold参数，sendBuffer会缩容
    ShrinkMsgBuffer(conn, buffer);
    return ret;

FAILURE:
    RemoveAsyncMsg(conn, asyncCtx);
    return ret;
}

Status CltSendRequestAsyncSocket(GmcConnT *conn, FixBufferT *buffer, const AsyncMsgContextT *asyncCtx)
{
    // 使用GmcEpollRegT类型的epollReg函数，走阻塞式发送异步消息，否则走非阻塞发送
    DB_POINTER2(buffer, asyncCtx);
    Status ret;
    if (conn->epollReg.epollRegType == EPOLLREG) {
        ret = CltSendRequestSocketBlock(conn, buffer, asyncCtx);
    } else {
        ret = CltSendRequestSocketMQ(conn, buffer, asyncCtx);
    }
    return ret;
}
#else
void ConsumeMessageBufferChannelLob(GmcConnT *conn)
{
    DB_POINTER(conn);
    AsyncMsgBufferQueueT *queue = &conn->sendMsgQueue;

    DbSpinLock(&queue->lock);
    Status ret;
    while (queue->head) {
        // 1. 取头结点
        AsyncMsgBufferNodeT *node = queue->head;
        // 2. 循环分片发送,发送完整个大包则弹出节点。
#if defined(MSG_DUMP)
        MsgDebugDump(&node->nodeBuffer, conn->remoteId, true);
#endif
        ret = MsgSendLobPack(&conn->pipe, conn->msgCtx, &node->nodeBuffer, true, &conn->msgPoolCtx);
        // 3. 后处理
        if (ret == GMERR_OK) {
            uint32_t serinum = GetSeriNumber(&node->nodeBuffer);
            AsyncMessageBufferQueuePop(queue);
            (void)DbAtomicInc(&conn->connDfxInfo.sndSuccessCnt);
            conn->connDfxInfo.sendQueueSize = queue->size;
            conn->asyncReqLastTime.lastRequestTime = DbGettimeMonotonicUsec();
            conn->asyncReqLastTime.lastSuccessSerialNum = serinum;
        } else if (ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
            break;
        } else {
            conn->connStatus = CONN_IS_BROKEN;
            uint32_t opCode = GetOpCode(&node->nodeBuffer);
            DB_LOG_ERROR(ret,
                "Send buffer by epoll. conn id: %" PRIu16 ", serialNumber: %" PRIu32 ", opCode: %" PRIu32
                ", buffer send size: %" PRIu32 ".",
                conn->remoteId, conn->serialNumberSend, opCode, node->nodeBuffer.seekPos);
            break;
        }
    }
    DbSpinUnlock(&queue->lock);
}

Status CltSendRequestAsyncChannelLobMQ(GmcConnT *conn, FixBufferT *buffer, const AsyncMsgContextT *asyncCtx)
{
    DB_POINTER2(buffer, asyncCtx);

    // 1.判断连接状态，如果异常，直接返回
    if (conn->connStatus == CONN_IS_BROKEN) {
        return GMERR_CONNECTION_RESET_BY_PEER;
    }

    // asyncCtx插入map
    Status ret = AddAsyncMsg(conn, asyncCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // 2.判断队列是否不为空，是的话，消息插入队列即返回
    AsyncMsgBufferQueueT *queue = &conn->sendMsgQueue;
    if (queue->count > 0) {
        ret = AsyncMessageBufferQueuePush(queue, buffer, conn);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            goto FAILURE;
        }
        return ret;
    }
#if defined(MSG_DUMP)
    MsgDebugDump(buffer, conn->remoteId, true);
#endif
    // 3.队列为空，尝试循环分片发送,否则插入队列
    ret = MsgSendLobPack(&conn->pipe, conn->msgCtx, buffer, true, &conn->msgPoolCtx);
    if (ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
        ret = AsyncMessageBufferQueuePush(queue, buffer, conn);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            goto FAILURE;
        }
    } else if (ret != GMERR_OK) {
        goto FAILURE;
    }
    return ret;
FAILURE:
    FixBufRelease(buffer);
    RemoveAsyncMsg(conn, asyncCtx);
    return ret;
}

Status CltSendRequestAsyncChannelNormal(GmcConnT *conn, FixBufferT *buffer, const AsyncMsgContextT *asyncCtx)
{
    DB_POINTER2(buffer, asyncCtx);
    // asyncCtx插入map
    Status ret = AddAsyncMsg(conn, asyncCtx);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 发送
    const DbPipeT *pipe = &conn->pipe;
    /* 发包务必保证seekpos为0，除非有重发机制，则保证首发为0 */
    DB_ASSERT(FixBufGetSeekPos(buffer) == 0);
#if defined(MSG_DUMP)
    MsgDebugDump(buffer, conn->remoteId, true);
#endif
#ifdef PERF_SAMPLE_STAT
    uint32_t opCode = GetOpCode(buffer);
    if (opCode == MSG_OP_RPC_REPLACE_VERTEX) {
        uint64_t tsc = DbGlobalRdtsc();
        DbPerfAppendData(g_clientAsyncSend, tsc, conn->remoteId);
        if (g_clientAsyncSend[conn->remoteId].curPos == g_clientAsyncSend[conn->remoteId].totalSize) {
            DbPerfWriteDataToFileByIdx(CLIENT_SEND_ASYNC_LOG, g_clientAsyncSend, conn->remoteId);
        }
    }
#endif
    ret = DbPipeSend(pipe, buffer);
    if (ret != GMERR_OK) {
        RemoveAsyncMsg(conn, asyncCtx);
        return ret;
    }
    UpdateConnSendSize(conn, buffer);
    // HPE独有分支
    MsgBufferFreeListPop(&conn->msgPool, buffer);  // replace old buffer with new buffer
    FixBufSetAllocCb(buffer, SharedMsgPoolGet, SharedMsgPoolReturn, &conn->msgPoolCtx);
    return ret;
}

Status CltSendRequestAsyncChannel(GmcConnT *conn, FixBufferT *buffer, const AsyncMsgContextT *asyncCtx)
{
    DB_POINTER3(conn, buffer, asyncCtx);
    Status ret;
    if (CltConnIsAsyncLob(conn)) {
        ret = CltSendRequestAsyncChannelLobMQ(conn, buffer, asyncCtx);
    } else {
        ret = CltSendRequestAsyncChannelNormal(conn, buffer, asyncCtx);
    }
    return ret;
}
#endif

Status CltFailureProcess(Status tret, FixBufferT *buffer, GmcConnT *conn)
{
    Status ret = tret;
    if (SECUREC_UNLIKELY(ret == GMERR_REQUEST_TIME_OUT)) {
        conn->connDfxInfo.connTimeoutCnt++;
    }
    uint32_t opCode = GetOpCode(buffer);
    if (tret == GMERR_CONNECTION_SEND_BUFFER_FULL && buffer->seekPos > 0) {
        DB_LOG_ERROR(ret,
            "Some bytes of the msg packet have been sent. conn id: %" PRIu16 ", serialNumber: %" PRIu32
            ", opCode: %" PRIu32 ", buffer send size is %" PRIu32 ".",
            conn->remoteId, conn->serialNumberSend, opCode, buffer->seekPos);
        ret = GMERR_CONNECTION_EXCEPTION;
    }

    DB_LOG_AND_SET_LASERR(ret,
        "request send, conn id: %" PRIu16 ", serialNumber: %" PRIu32 ",  opCode: %" PRIu32
        ", buffer send size: %" PRIu32 ", current pid: %" PRId64 ".",
        conn->remoteId, conn->serialNumberSend, opCode, buffer->pos, (int64_t)getpid());
    conn->connDfxInfo.sndFailCnt++;
    return ret;
}

Status CltSendResponse(GmcConnT *conn, FixBufferT *buffer)
{
    // 1. complete msgheader
    (void)CltCompleteDtlMsgHeader(conn, buffer);

    // 2. send
    /* 发包务必保证seekpos为0，除非有重发机制，则保证首发为0; datalog场景可能重发 */
    FixBufSeek(buffer, 0);
#if defined(MSG_DUMP)
    MsgDebugDump(buffer, conn->remoteId, true);
#endif
    Status ret = DbPipeSend(&conn->pipe, buffer);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return CltFailureProcess(ret, buffer, conn);
    }
    // 3. post process
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    MsgBufferFreeListPop(&conn->msgPool, buffer);
    FixBufSetAllocCb(buffer, SharedMsgPoolGet, SharedMsgPoolReturn, &conn->msgPoolCtx);
#endif
    return ret;
}

Status CltSendRequest(GmcConnT *conn, FixBufferT *buffer)
{
#if defined(FUZZCOLLECT)
    CollectLibFuzzerPacket(buffer);
#endif
    uint32_t opCode = GetOpCode(buffer);
    // 1.流控
    Status ret = FlowControlAccordingToOpCode(conn, opCode);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // 2. complete msgheader
    if (SECUREC_UNLIKELY(conn->bigObjectStand)) {
        UpdateConnSendSecureFixBufStatistics(conn, buffer);
    }
    if (CltCompleteMsgHeader(conn, buffer) == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    // 3. send
#ifdef PERF_SAMPLE_STAT
    if (opCode == MSG_OP_RPC_REPLACE_VERTEX) {
        uint64_t tsc = DbGlobalRdtsc();
        DbPerfAppendData(g_clientSend, tsc, conn->remoteId);
        if (g_clientSend[conn->remoteId].curPos == g_clientSend[conn->remoteId].totalSize) {
            DbPerfWriteDataToFileByIdx(CLIENT_SEND_LOG, g_clientSend, conn->remoteId);
        }
    }
#endif
    /* 发包务必保证seekpos为0，除非有重发机制，则保证首发为0 */
    DB_ASSERT(FixBufGetSeekPos(buffer) == 0);
#if defined(MSG_DUMP)
    MsgDebugDump(buffer, conn->remoteId, true);
#endif
    conn->connStageTimePoint.sendSecConnReq = (opCode == MSG_OP_RPC_CONNECT ? DbRdtsc() : 0);
    ret = DbPipeSend(&conn->pipe, buffer);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return CltFailureProcess(ret, buffer, conn);
    }
    UpdateConnSendSize(conn, buffer);
    // 4. post process
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    // if send success by channel, make sent buffer invisible
    // receiver should be responsible to release it
    MsgBufferFreeListPop(&conn->msgPool, buffer);
    FixBufSetAllocCb(buffer, SharedMsgPoolGet, SharedMsgPoolReturn, &conn->msgPoolCtx);
#else
    // 欧拉环境下，当用户设置packShrinkThreshold参数，sendBuffer会缩容
    ShrinkMsgBuffer(conn, buffer);
#endif
    conn->connDfxInfo.sndSuccessCnt++;
    return ret;
}

Status CltSendRequestAsync(GmcConnT *conn, FixBufferT *buffer, AsyncMsgContextT *asyncCtx)
{
#if defined(FUZZCOLLECT)
    CollectLibFuzzerPacket(buffer);
#endif

    uint32_t opCode = GetOpCode(buffer);
    // 1.流控
    Status ret = FlowControlAccordingToOpCode(conn, opCode);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // 2. complete msgheader
    if (conn->bigObjectStand) {
        UpdateConnSendSecureFixBufStatistics(conn, buffer);
    }

    if (asyncCtx->notDistribute) {
        const MsgExtendT extend = {MSG_EXTENTD_TAG_NOT_DISTRIBUTE, sizeof(uint8_t)};
        ret = MsgAddExtend(buffer, &extend, &asyncCtx->notDistribute);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            (void)DbAtomicInc(&conn->connDfxInfo.sndFailCnt);
            return ret;
        }
    }
    if (CltCompleteMsgHeader(conn, buffer) == NULL) {
        (void)DbAtomicInc(&conn->connDfxInfo.sndFailCnt);
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    // 3. 补全异步上下文基本信息
    MsgHeaderT *msg = RpcPeekMsgHeader(buffer);
    asyncCtx->serialNumber = msg->serialNumber;
    asyncCtx->requestStartTime = DbGettimeMonotonicUsec();
    conn->asyncReqLastTime.lastRequestTime = asyncCtx->requestStartTime;
    conn->asyncReqLastTime.lastSuccessSerialNum = conn->serialNumberSend;
#if !defined(RTOSV2) && !defined(RTOSV2X) && !defined(HPE)
    ret = CltSendRequestAsyncSocket(conn, buffer, asyncCtx);
#else
    ret = CltSendRequestAsyncChannel(conn, buffer, asyncCtx);
#endif
    if (SECUREC_LIKELY(ret == GMERR_OK)) {
        (void)DbAtomicInc(&conn->connDfxInfo.sndSuccessCnt);
        return ret;
    }

    // 失败打日志
    DB_LOG_AND_SET_LASERR(ret,
        "request send, conn id: %" PRIu16 ", opCode: %" PRIu32 ", serialNumber: %" PRIu32 ", buffer size: %" PRIu32 ".",
        conn->remoteId, opCode, conn->serialNumberSend, buffer->pos);
    conn->connDfxInfo.sndFailCnt++;
    return ret;
}

// 用于判断是否接收完完整消息
static inline bool CheckUnRecvedAll(GmcConnT *conn, FixBufferT *buffer)
{
    // 必须是异步连接才做判断
    if (conn->connType != GMC_CONN_TYPE_ASYNC) {
        return false;
    }
    return FixBufGetPos(buffer) < FixBufGetTotalLength(buffer) ? true : false;
}

#if !defined(RTOSV2) && !defined(RTOSV2X) && !defined(HPE)
static void TransMsgHeaderToHexArray(uint8_t *buffer, char *headerBuf)
{
    uint32_t headerLen = MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE;
    for (uint32_t i = 0; i < headerLen; i++) {
        errno_t err = sprintf_s((headerBuf + i * HEX_STR_LEN), headerLen, "%02X", buffer[i]);
        if (err < 0) {
            DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "header buf, i: %" PRIu32 ".", i);
            return;
        }
    }
}

static bool IsMsgHeaderValid(GmcConnT *conn, FixBufferT *buffer)
{
    if (conn->connType == GMC_CONN_TYPE_SUB) {
        return true;
    }
    const MsgHeaderT *msg = RpcPeekMsgHeader(buffer);
    uint16_t msgMagicNum = msg->msgMagicNum;
    if (SECUREC_UNLIKELY(msgMagicNum != MSG_VERIFY_NUMBER)) {
        conn->connStatus = CONN_IS_BROKEN;
        char headerBuf[MSG_OP_HEADER_HEX_LEN] = {0};
        TransMsgHeaderToHexArray(buffer->buf, headerBuf);
        DB_LOG_AND_SET_LASERR(GMERR_CONNECTION_EXCEPTION,
            "verify the msg header msgMagicNum, msgMagicNum is %" PRIu16 ", buffer totalLen:%" PRIu32 ", pos: %" PRIu32
            ", seekpos: %" PRIu32 ", header buf:%s.",
            msgMagicNum, buffer->totalLength, buffer->pos, buffer->seekPos, headerBuf);
        return false;
    }
    return true;
}

static Status ReceiveHeader(GmcConnT *conn, FixBufferT *buffer, uint32_t headerLen, bool noBlock)
{
    Status ret;
    // there must be enough space to receive a header
    uint32_t capacity = FixBufGetTotalLength(buffer);
    if (capacity < headerLen) {
        ret = SecureFixBufExtend(buffer, CS_PACK_SIZE);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    // receive message header
    FixBufSetTotalLen(buffer, headerLen);
    DbPipeT *pipe = &conn->pipe;
    ret = DbPipeRecv(pipe, buffer, noBlock);
    return ret;
}

static Status ReceiveBody(GmcConnT *conn, FixBufferT *buffer, uint32_t headerLen, bool noBlock)
{
    // if no message body to receive, return
    const MsgHeaderT *msg = RpcPeekMsgHeader(buffer);
    uint32_t size = msg->size;
    if (size == headerLen) {
        return GMERR_OK;
    }
    if (SECUREC_UNLIKELY(size < headerLen)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_DATA_EXCEPTION, "msg size %" PRIu32 " is less than headerLen %" PRIu32 ".", size, headerLen);
        return GMERR_DATA_EXCEPTION;
    }

    // extend to required size. buffer->buf knows its actual size, no need to change buffer->totalLength here
    Status ret = SecureFixBufExtend(buffer, size);
    if (ret != GMERR_OK) {
        return ret;
    }

    // receive remaining message body
    DbPipeT *pipe = &conn->pipe;
    ret = DbPipeRecv(pipe, buffer, noBlock);
    return ret;
}

// 用于接收未接收完的报文
static Status CltSocketRecvLast(GmcConnT *conn, FixBufferT *buffer)
{
    Status ret;
    DB_ASSERT(conn->connType == GMC_CONN_TYPE_ASYNC);
    DB_ASSERT(conn->recvStatus != RECIVED_ALL);

    DbPipeT *pipe = &conn->pipe;
    ret = DbPipeRecv(pipe, buffer, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "receive the remaining part.");
        return ret;
    }
    if (CheckUnRecvedAll(conn, buffer)) {
        return ret;
    }

    if (conn->recvStatus == UNRECIVED_HEADER) {
        // 检查消息头是否正确
        if (!IsMsgHeaderValid(conn, buffer)) {
            DB_LOG_AND_SET_LASERR(
                GMERR_CONNECTION_EXCEPTION, "verify the message header, connection id %" PRIu16 ".", conn->remoteId);
            return GMERR_CONNECTION_EXCEPTION;
        }

        // 消息头接收完整标志位复位
        conn->recvStatus = UNRECIVED_BODY;

        // seekpos略过消息头
        const uint32_t headerLen = MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE;
        FixBufSeek(buffer, headerLen);

        // 接收body
        ret = ReceiveBody(conn, buffer, headerLen, true);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Receive body.");
            return ret;
        }
        if (CheckUnRecvedAll(conn, buffer)) {
            return ret;
        }
    }
    conn->recvStatus = RECIVED_ALL;

    // 重设totallength
    uint32_t recoLength = SecureFixBufGetActualSize(buffer);
    FixBufSetTotalLen(buffer, recoLength);
    return ret;
}

static Status CltSocketRecv(GmcConnT *conn, FixBufferT *buffer, bool noBlock)
{
    Status ret;
    // 第一步：异步连接先判断是否上个报文有剩余部分需要接收
    if (conn->connType == GMC_CONN_TYPE_ASYNC && conn->recvStatus != RECIVED_ALL) {
        ret = CltSocketRecvLast(conn, buffer);
        return ret;
    }

    // 第二步：reset fixbuffer, 然后接收一个完整报文,如果消息头或消息体未接收完整立即返回并置标志位
    // 缩容
    ShrinkMsgBuffer(conn, buffer);

    // reset read and write position
    FixBufResetMem(buffer);

    // receive header
    const uint32_t headerLen = MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE;
    ret = ReceiveHeader(conn, buffer, headerLen, noBlock);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Receive header.");
        return ret;
    }
    // 检查消息头是否接收完整
    if (CheckUnRecvedAll(conn, buffer)) {
        conn->recvStatus = UNRECIVED_HEADER;
        DB_LOG_INFO("The msg header received non-completely, waiting for the next transmission. server "
                    "connection id is %" PRIu16 ".",
            conn->remoteId);
        return ret;
    }
    // 检查消息头是否正确
    if (!IsMsgHeaderValid(conn, buffer)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_CONNECTION_EXCEPTION, "verify the msg header, conn id is %" PRIu16 ".", conn->remoteId);
        return GMERR_CONNECTION_EXCEPTION;
    }

    // seekpos skip header
    FixBufSeek(buffer, headerLen);

    // receive body
    ret = ReceiveBody(conn, buffer, headerLen, noBlock);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Receive body.");
        return ret;
    }

    // 检查body是否接收完整
    if (CheckUnRecvedAll(conn, buffer)) {
        conn->recvStatus = UNRECIVED_BODY;
        DB_LOG_INFO("The msg body received non-completely, waiting for the next transmission. server "
                    "connection id is %" PRIu16,
            conn->remoteId);
        return ret;
    }

    // 重设total length
    uint32_t recoLength = SecureFixBufGetActualSize(buffer);
    FixBufSetTotalLen(buffer, recoLength);
    return ret;
}
#else
static inline Status SkipHeader(FixBufferT *buffer)
{
    // read position must be zero
    uint32_t offset = FixBufGetSeekPos(buffer);
    DB_ASSERT(offset == 0);

    // check and skip header
    const uint32_t headerLen = MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE;
    const MsgHeaderT *msg = FixBufGetData(buffer, headerLen);
    return (msg != NULL) ? GMERR_OK : GMERR_DATA_EXCEPTION;
}

static Status CltChannelRecv(GmcConnT *conn, FixBufferT *buffer, bool noBlock)
{
#ifdef SECUREFIXBUF
    if (SECUREC_UNLIKELY(!SecureFixBufValid(buffer))) {
        return GMERR_CONNECTION_RESET_BY_PEER;
    }
#endif
    // free previous received shared memory
    MsgBufferList *freeList = &conn->msgPool;
    // 订阅连接与CS模式不对接收的buffer内存做缓存
    if (conn->connType != GMC_CONN_TYPE_SUB && !conn->isCsMode) {
        MsgBufferFreeListPush(freeList, buffer);
    } else {
        SecureFixBufRelease(buffer);
    }

    // receive message from shared memory
    DbPipeT *pipe = &conn->pipe;
    GetMsgBufFunc allocFunc = buffer->allocFunc;
    FreeMsgBufFunc freeFunc = buffer->freeFunc;
    void *ctx = buffer->ctx;
    Status ret = DbPipeRecv(pipe, buffer, noBlock);
    if (ret != GMERR_OK) {
        return ret;
    }
    FixBufSetAllocCb(buffer, allocFunc, freeFunc, ctx);
    ret = SkipHeader(buffer);
#ifdef PERF_SAMPLE_STAT
    uint32_t opCode = GetOpCode(buffer);
    if (opCode == MSG_OP_RPC_REPLACE_VERTEX) {
        uint64_t tsc = DbGlobalRdtsc();
        DbPerfAppendData(g_clientRecv, tsc, conn->remoteId);
        if (g_clientRecv[conn->remoteId].curPos == g_clientRecv[conn->remoteId].totalSize) {
            DbPerfWriteDataToFileByIdx(CLIENT_RECV_LOG, g_clientRecv, conn->remoteId);
        }
    }
#endif
    return ret;
}

static Status CltChannelRecvLobLast(GmcConnT *conn, FixBufferT *buffer, FixBufferT *tmpBuffer)
{
    MsgHeaderT *msgHeader = RpcPeekMsgHeader(tmpBuffer);
    uint32_t size = msgHeader->size - MSG_HEADER_ALIGN_SIZE;

    // tmpBuffer需要剥消息头
    uint8_t *srcBuf = FixBufGetBuf(tmpBuffer) + MSG_HEADER_ALIGN_SIZE;
    Status ret = FixBufPutData(buffer, srcBuf, size);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "copy data to lob msg pack.");
        return ret;
    }
    // 如果tmpBuffer中的isFinish等于true，需要修改buffer中的isFinish标志位
    conn->recvSeq++;
    if (msgHeader->isFinish) {
        MsgHeaderT *msg = RpcPeekMsgHeader(buffer);
        msg->isFinish = true;
        conn->recvSeq = 0;
        conn->recvStatus = RECIVED_ALL;
        return SkipHeader(buffer);
    }
    return GMERR_OK;
}

static Status CheckSequenceRight(GmcConnT *conn, MsgHeaderT *msgHeader, FixBufferT *buffer)
{
    if (conn->recvSeq != msgHeader->seq) {
        // 校验失败，有可能是心跳应答，直接丢掉，释放内存返回。
        OpHeaderT *op = ProtocolPeekFirstOpHeader(buffer);
        if (op->opCode == MSG_OP_RPC_HEARTBEAT) {
            return GMERR_OK;
        }
        DB_LOG_AND_SET_LASERR(GMERR_CONNECTION_RESET_BY_PEER,
            "Check receive sequence for lob message, recv sequeue is %" PRIu16 ", seq in msg heaer is %" PRIu16 " .",
            conn->recvSeq, msgHeader->seq);
        return GMERR_CONNECTION_RESET_BY_PEER;
    }
    return GMERR_OK;
}

static Status CltChannelRecvLobInner(GmcConnT *conn, FixBufferT *buffer, FixBufferT *tmpBuffer, bool noBlock)
{
    // 1. 使用tmpBuffer收包
    Status ret = DbPipeRecv(&conn->pipe, tmpBuffer, noBlock);
    if (ret != GMERR_OK) {
        return ret;
    }
#if defined(MSG_DUMP)
    MsgDebugDump(tmpBuffer, conn->remoteId, false);
#endif
    // 2. 拿到消息头,检验消息头序列
    MsgHeaderT *msgHeader = RpcPeekMsgHeader(tmpBuffer);
    ret = CheckSequenceRight(conn, msgHeader, tmpBuffer);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 非首包，走接收剩余包的流程
    if (conn->recvSeq != 0) {
        ret = CltChannelRecvLobLast(conn, buffer, tmpBuffer);
        return ret;
    }
    // 除了建连，入参buffer都为动态内存，先释放之前的，再重新创建一个
    if (buffer->buf != NULL && buffer->memCtx->memType == DB_DYNAMIC_MEMORY) {
        // 创建动态内存buffer用于组包，并在下一次进入时释放。
        FixBufRelease(buffer);
        ret = FixBufCreate(buffer, conn->memCtx, msgHeader->size, FIX_BUF_FLAG_LOB_BUFFER);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "create buffer for log msg.");
            return ret;
        }
    } else {
        FixBufResetMem(buffer);
    }
    uint32_t size = FixBufGetPos(tmpBuffer);
    ret = FixBufPutData(buffer, msgHeader, size);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "copy data of first recv pack to lob buffer.");
        return ret;
    }

    // 序列号增1并判断是否结束
    conn->recvSeq++;
    if (msgHeader->isFinish) {
        conn->recvSeq = 0;
        conn->recvStatus = RECIVED_ALL;
        return SkipHeader(buffer);
    }
    conn->recvStatus = RECIVED_PART;
    return GMERR_OK;
}

static Status CltChannelRecvLob(GmcConnT *conn, FixBufferT *buffer, bool noBlock)
{
    FixBufferT tmpBuffer = {};
    FixBufInit(&tmpBuffer, NULL, 0, 0, FIX_BUF_FLAG_EXTEND_BUFFER, conn->msgCtx);
#ifdef SECUREFIXBUF
    DbDataT ctx;
    ctx.ptr = &conn->entryCtx;
    SecureFixBufSetVerifyProc(&tmpBuffer, CltFixBufVerify, ctx);
#endif
    FixBufSetAllocCb(&tmpBuffer, SharedMsgPoolGet, SharedMsgPoolReturn, &conn->msgPoolCtx);
    Status ret = CltChannelRecvLobInner(conn, buffer, &tmpBuffer, noBlock);
    if (FixBufGetBuf(&tmpBuffer) != NULL) {
        FixBufRelease(&tmpBuffer);
    }
    return ret;
}
#endif

#define CONN_TIME_AVG_THRESHOLD 1000
#define CONN_TIME_AVG_PERIOD 128
void CltMonitorCSCommunicationAvgTime(uint64_t cost, GmcConnT *conn)
{
    conn->csCommTotalTime += cost;
    conn->csCommCount++;
    if (SECUREC_UNLIKELY(conn->csCommTotalTime >= CONN_TIME_AVG_PERIOD)) {  // 周期性判断平均通信时间是否超时
        uint64_t avgTime = conn->csCommTotalTime / conn->csCommCount;
        if (SECUREC_UNLIKELY(avgTime > CONN_TIME_AVG_THRESHOLD)) {
            DB_LOG_WARN(GMERR_PROGRAM_LIMIT_EXCEEDED,
                "srv to clt communication too long, avg cost time: %" PRIu64 "(ms), more than: %" PRIu64
                "(ms), conn type is : %" PRIu16 ", remote id is %" PRIu16 ".",
                avgTime, (uint64_t)CONN_TIME_AVG_THRESHOLD, (uint16_t)conn->connType, conn->remoteId);
        }
        conn->csCommTotalTime = 0;
        conn->csCommCount = 0;
    }
}

void CltMonitorCSCommunicationSingleTime(const MsgHeaderT *header, GmcConnT *conn)
{
    uint64_t cost = DbToMseconds(DbGlobalRdtsc() - header->rspSendStartTime);
    uint64_t threshold = conn->isLobConn ? CONN_TIME_LOB_THRESHOLD : CONN_TIME_THRESHOLD;
    if (SECUREC_UNLIKELY(cost > threshold)) {
        DB_LOG_WARN(GMERR_PROGRAM_LIMIT_EXCEEDED,
            "srv to clt communication too long, single cost time: %" PRIu64 "(ms), more than: %" PRIu64
            "(ms), conn type is : %" PRIu16 ", remote id is %" PRIu16 ".",
            cost, threshold, (uint16_t)conn->connType, conn->remoteId);
    }
    CltMonitorCSCommunicationAvgTime(cost, conn);
}

void CltMonitorCSCommSingleTimeForPubsub(const MsgHeaderT *header, GmcConnT *conn, PubsubCommDfxInfoT *dfxInfo)
{
    uint64_t currTime = DbGlobalRdtsc();  // 客户端最近一次调用CltPipeRecv的结束时间
    // 服务端发送报文到客户端收到该报文的总耗时，存在多条消息时，包含前序消息的处理时间
    uint64_t cost = DbToMseconds(currTime - header->rspSendStartTime);
    uint64_t threshold = conn->isLobConn ? CONN_TIME_LOB_THRESHOLD : CONN_TIME_THRESHOLD;

    if (SECUREC_UNLIKELY(cost > threshold)) {
        // 服务端发送首个报文到客户端进入SubPushCb的时间
        if (dfxInfo->handledMsgNum == 1) {
            conn->serverSend2CliSubCbCost = DbToMseconds(dfxInfo->subPushCbStartTime - header->rspSendStartTime);
        }
        // 客户端进入SubPushCb到最近一次调用CltPipeRecv的时间间隔
        uint64_t subCb2PipeRecvCost = DbToMseconds(dfxInfo->pipeRecvStartTime - dfxInfo->subPushCbStartTime);
        // 服务端发送当前消息到客户端最近一次调用CltPipeRecv的时间间隔
        uint64_t serverSend2PipeRecvCost = DbToMseconds(dfxInfo->pipeRecvStartTime - header->rspSendStartTime);
        // 客户端最近一次CltPipeRecv消耗的时间
        uint64_t pipeRecvCost = DbToMseconds(currTime - dfxInfo->pipeRecvStartTime);

        DB_LOG_WARN(GMERR_PROGRAM_LIMIT_EXCEEDED,
            "srv to clt communication too long, single cost time: %" PRIu64 "(ms), more than: %" PRIu64
            "(ms), conn type is : %" PRIu16 ", remote id is %" PRIu16 ", handled msg num: %" PRIu32
            ", server send first msg to client sub cb time: %" PRIu64
            "(ms), client sub cb to pipe recv start time: %" PRIu64
            "(ms), server send to client pipe recv time: %" PRIu64 "(ms)"
            ", pipe recv time: %" PRIu64 "(ms).",
            cost, threshold, (uint16_t)conn->connType, conn->remoteId, dfxInfo->handledMsgNum,
            conn->serverSend2CliSubCbCost, subCb2PipeRecvCost, serverSend2PipeRecvCost, pipeRecvCost);
    }
    CltMonitorCSCommunicationAvgTime(cost, conn);
}

Status CltPipeRecv(GmcConnT *conn, FixBufferT *buffer, bool noBlock)
{
    Status ret = GMERR_OK;
#if !defined(RTOSV2) && !defined(RTOSV2X) && !defined(HPE)
    ret = CltSocketRecv(conn, buffer, noBlock);
#else
    if (CltConnIsAsyncLob(conn)) {
        ret = CltChannelRecvLob(conn, buffer, noBlock);
    } else {
        ret = CltChannelRecv(conn, buffer, noBlock);
    }
#endif

#if defined(MSG_DUMP)
    MsgDebugDump(buffer, conn->remoteId, false);
#endif

    if (SECUREC_UNLIKELY(ret == GMERR_REQUEST_TIME_OUT)) {
        conn->connDfxInfo.connTimeoutCnt++;
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "receive response, conn id: %" PRIu16 ".", conn->remoteId);
        return ret;
    }
    UpdateConnRecvSize(conn, buffer);
    // 同步、订阅下该判断皆不成立,故添加assert
    if (conn->recvStatus != RECIVED_ALL) {
        DB_ASSERT(conn->connType == GMC_CONN_TYPE_ASYNC);
        return ret;
    }

    const OpHeaderT *op = ProtocolPeekFirstOpHeader(buffer);
    if (op->opCode != MSG_OP_RPC_HEARTBEAT) {
        conn->connDfxInfo.rcvSuccessCnt++;
    }
    return GMERR_OK;
}

Status CltMsgIsValid(FixBufferT *buffer)
{
    DB_POINTER(buffer);
    if (SECUREC_LIKELY(!CltGetSecurityMode())) {
        return GMERR_OK;
    }
    uint32_t pos = FixBufGetPos(buffer);
    const MsgHeaderT *msgHdr = RpcPeekMsgHeader(buffer);

    // 为防止connect返回报文和用例中的空报文报错，将来可能修改
    // datalog和订阅都存在serialNumber为0,并且无法通过校验，该处保留
    if (msgHdr->serialNumber == 0 || msgHdr->opNum == 0) {
        return GMERR_OK;
    }

    // 校验消息头魔术字
    if (SECUREC_UNLIKELY(msgHdr->msgMagicNum != MSG_VERIFY_NUMBER)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "msg magicnum: %" PRIu16 ".", msgHdr->msgMagicNum);
        return GMERR_DATA_EXCEPTION;
    }

    // 报文实际长度要等于msgHeader中的size
    if (SECUREC_UNLIKELY(pos != msgHdr->size)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "The msg len in the packet header is %" PRIu32 ", but the len of the received msg is %" PRIu32 ".",
            msgHdr->size, pos);
        return GMERR_DATA_EXCEPTION;
    }

    OpHeaderT *opHdr = ProtocolPeekFirstOpHeader(buffer);

    // 校验opCode合法性
    if (SECUREC_UNLIKELY(opHdr->opCode >= MSG_OP_RPC_CEIL)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "The opCode is %" PRIu32 ".", (uint32_t)opHdr->opCode);
        return GMERR_DATA_EXCEPTION;
    }
    // 校验opHeader中len的正确性
    if (SECUREC_UNLIKELY(opHdr->len != pos - MSG_HEADER_ALIGN_SIZE - msgHdr->extendSize)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "The expected op length is %" PRIu32 ", but the length in the received opHeader is %" PRIu32 ".",
            pos - MSG_HEADER_ALIGN_SIZE - msgHdr->extendSize, opHdr->len);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

static Status CltCheckResponseMsg(GmcConnT *conn, FixBufferT *buffer)
{
#ifdef EXPERIMENTAL_NERGC
    if (!conn->isAuth) {
        return GMERR_OK;
    }
    const MsgHeaderT *msgHdr = RpcPeekMsgHeader(buffer);
    if (memcmp(&msgHdr->sessionId, &conn->sessionId, sizeof(SessionIdT)) != 0) {
        char headerHex[SESSION_ID_LEN * SESSION_BINARY_PRINT_LEN + 1] = {0};
        char connHex[SESSION_ID_LEN * SESSION_BINARY_PRINT_LEN + 1] = {0};

        for (int i = 0; i < SESSION_ID_LEN; i++) {
            sprintf_s(headerHex + (i * SESSION_BINARY_PRINT_LEN), sizeof(headerHex) - (i * SESSION_BINARY_PRINT_LEN),
                "%02x", msgHdr->sessionId.id[i]);
            sprintf_s(connHex + (i * SESSION_BINARY_PRINT_LEN), sizeof(connHex) - (i * SESSION_BINARY_PRINT_LEN),
                "%02x", conn->sessionId.id[i]);
        }
        DB_LOG_ERROR(GMERR_CONNECTION_EXCEPTION, "inv sessionId: %s, conn sessionId: %s", headerHex, connHex);
        return GMERR_CONNECTION_EXCEPTION;
    }
    DbLogCtrlItemT logCtrlItem;
    CltGetLogCtrl(&logCtrlItem);
    static MsgExtendT extendLog = {.tag = MSG_EXTENTD_TAG_LOG_CTL, .size = sizeof(DbLogCtrlItemT)};
    MsgGetExtend(buffer, &extendLog, &logCtrlItem);
    CltUpdateLogCtrl(&logCtrlItem);
#endif
    return GMERR_OK;
}

Status CltMsgIsValidWithLog(FixBufferT *buffer, GmcConnT *conn)
{
    Status ret = CltMsgIsValid(buffer);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        uint32_t opCode = GetOpCode(buffer);
        const MsgHeaderT *msgHdr = RpcPeekMsgHeader(buffer);
        DB_LOG_AND_SET_LASERR(ret,
            "Malformed message verification, conn id: %" PRIu16 ", send serialNumber: %" PRIu32
            ", recv serialNumber: %" PRIu32 ", opCode: %" PRIu32 ", buffer send size: %" PRIu32 ".",
            conn->remoteId, conn->serialNumberSend, msgHdr->serialNumber, opCode, buffer->pos);
        return ret;
    }
    return CltCheckResponseMsg(conn, buffer);
}

Status CltMsgGetDtlError(FixBufferT *buffer, int64_t *errorCode, char *errorLableName, uint32_t errorLableNameSize)
{
    Status ret = SecureFixBufGetInt64(buffer, errorCode);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "get dtlErrorCode from response.");
        return ret;
    }

    TextT dtlErrorLabelName = {0};
    ret = SecureFixBufGetTextNullable(buffer, &dtlErrorLabelName);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "get dtlErrorLabelName from response.");
        return ret;
    }
    if (dtlErrorLabelName.len == 0) {
        return GMERR_OK;
    }
    if (strncpy_s(errorLableName, errorLableNameSize, dtlErrorLabelName.str, errorLableNameSize - 1) != EOK) {
        DB_LOG_AND_SET_LASERR(
            GMERR_MEMORY_OPERATE_FAILED, "copy dtlErrorLabelName. Expected data len:%" PRIu32 ".", errorLableNameSize);
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    return GMERR_OK;
}

static void CltSetLastErrorFromResponse(FixBufferT response, uint32_t offset)
{
    bool offsetInvalid = (offset != SIZE_ALIGN4(offset) || offset > FixBufGetPos(&response));
    if (SECUREC_UNLIKELY(offsetInvalid)) {
        DB_LOG_WARN_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "get last blunder from response.");
        return;
    }

    TextT text;
    FixBufSeek(&response, offset);
    Status ret = SecureFixBufGetTextNullable(&response, &text);  // 可能是空的
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_WARN_AND_SET_LASTERR(ret, "get last blunder text from response.");
        return;
    }

    const TextT *lastError = DbGetLastErrorInfo();
    if (SECUREC_UNLIKELY(lastError == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_NO_DATA, "DbGetLastErrorInfo.");
        return;
    }

    const size_t capacity = LOG_MAX_SIZE_OF_LOG_MSG;
    errno_t err = strncpy_s(lastError->str, capacity, text.str, capacity - 1);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_NO_DATA, "string copy when set last wrong message, ret val is %d", err);
    }

    return;
}

static void CltRecvPostProcess(GmcConnT *conn, FixBufferT *buffer)
{
    const MsgHeaderT *msgHdr = RpcPeekMsgHeader(buffer);
    CltMonitorCSCommunicationSingleTime(msgHdr, conn);
    // update flow control config from header
    FlowControlWhenRecvResponse(&conn->flowControl, msgHdr->flowCtrlLevel);

    if (SECUREC_LIKELY(msgHdr->opStatus == GMERR_OK)) {
        return;
    }

    // set last error from response if opStatus is not OK
    CltSetLastErrorFromResponse(*buffer, msgHdr->lastErrorPos);
}

void UpdateConnRecvSecureFixBufStatistics(GmcConnT *conn, const FixBufferT *buffer)
{
    if (conn->bigObjectStand == 0) {
        return;
    }
    CltRecvObjT *recvObj = &conn->cltRecvObj;
    // 当大对象接收数或普通对象接收数达到uint32最大值时，通过打印LOG记录当前的发送对象相关统计信息，然后重置
    if (recvObj->bigObjRecvCount == DB_MAX_UINT32 || recvObj->normalObjRecvCount == DB_MAX_UINT32) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED,
            "Connection id is %" PRIu16 ", receive object statistics: big object max size is %" PRIu32
            " bytes,  big object average size is %" PRIu32 " bytes, big object receive count is %" PRIu32
            ". normal object max size is %" PRIu32 " bytes, normal object average size is %" PRIu32
            ", normal object receive count is %" PRIu32 ".",
            conn->remoteId, recvObj->bigObjRecvMaxSize, recvObj->bigObjRecvAvgSize, recvObj->bigObjRecvCount,
            recvObj->normalObjRecvMaxSize, recvObj->normalObjRecvAvgSize, recvObj->normalObjRecvCount);
        recvObj->bigObjRecvCount = 0;
        recvObj->bigObjRecvMaxSize = 0;
        recvObj->bigObjRecvAvgSize = 0;
        recvObj->normalObjRecvCount = 0;
        recvObj->normalObjRecvMaxSize = 0;
        recvObj->normalObjRecvAvgSize = 0;
    }
    uint32_t size = FixBufGetPos(buffer);
    if (size > conn->bigObjectStand) {
        recvObj->bigObjRecvCount++;
        recvObj->bigObjRecvMaxSize = (size > recvObj->bigObjRecvMaxSize) ? size : recvObj->bigObjRecvMaxSize;
        uint64_t bigObjRecvSize =
            ((uint64_t)(recvObj->bigObjRecvCount - 1) * (uint64_t)recvObj->bigObjRecvAvgSize + (uint64_t)size);
        recvObj->bigObjRecvAvgSize = (uint32_t)(bigObjRecvSize / recvObj->bigObjRecvCount);
    } else {
        recvObj->normalObjRecvCount++;
        recvObj->normalObjRecvMaxSize = (size > recvObj->normalObjRecvMaxSize) ? size : recvObj->normalObjRecvMaxSize;
        uint64_t normalObjRecvSize =
            ((uint64_t)(recvObj->normalObjRecvCount - 1) * (uint64_t)recvObj->normalObjRecvAvgSize + (uint64_t)size);
        recvObj->normalObjRecvAvgSize = (uint32_t)(normalObjRecvSize / recvObj->normalObjRecvCount);
    }
}
Status CltRecvResponseAsync(GmcConnT *conn, FixBufferT *buffer)
{
    Status ret = CltPipeRecv(conn, buffer, true);
    UpdateConnRecvSecureFixBufStatistics(conn, buffer);
    if (ret == GMERR_OK && conn->recvStatus == RECIVED_ALL) {
        ret = CltMsgIsValidWithLog(buffer, conn);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        CltRecvPostProcess(conn, buffer);
    }
    // 异步接收通常是由epoll唤醒，通常不会失败，除非服务器发送应答后将通道关闭，因此修改rcvFailCnt意义不大
    return ret;
}

Status CltRecvResponse(GmcConnT *conn, FixBufferT *buffer)
{
    const uint32_t expected = conn->serialNumberSend;
    uint32_t distance;

    do {
        Status ret = CltPipeRecv(conn, buffer, false);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            // 同步接收不会有心跳包
            ++(conn->connDfxInfo.rcvFailCnt);
            return ret;
        }
        MsgHeaderT *msgHdr = RpcPeekMsgHeader(buffer);
        UpdateConnRecvSecureFixBufStatistics(conn, buffer);
        distance = expected - msgHdr->serialNumber;  // value may wrap around, use distance instead of comparision
        if (SECUREC_LIKELY(distance == 0)) {
            // 报文校验
            ret = CltMsgIsValidWithLog(buffer, conn);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
            // received the expected package
            CltRecvPostProcess(conn, buffer);
            return GMERR_OK;
        }
        // serinum与发送的serinum不匹配，循环接收的逻辑一直都有，不改变，但是此处打一行错误日志
        uint32_t opCode = GetOpCode(buffer);
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION,
            "Expected serinumber is %" PRIu32 ", but serinumber in msg header is %" PRIu32 ", conn id is %" PRIu16
            ", opCode is %" PRIu32 ".",
            expected, msgHdr->serialNumber, conn->remoteId, opCode);
    } while (distance <= INT32_MAX);

    // distance is unusual, maybe msgHdr->serialNumber is newer than expected
    DB_SET_LASTERR(GMERR_DATA_EXCEPTION, "serial number while receiving response.");
    return GMERR_DATA_EXCEPTION;
}

Status RpcReserveMsgOpHeader(FixBufferT *req)
{
    struct Init {
        MsgHeaderT msg;
        OpHeaderT op;
    };

    static_assert(sizeof(struct Init) == MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE, "Please check alignment.");
    static_assert(sizeof(MsgHeaderT) == MSG_HEADER_ALIGN_SIZE, "Please check alignment.");

    FixBufResetMem(req);
    struct Init *init = FixBufReserveData(req, sizeof(struct Init));
    if (SECUREC_UNLIKELY(init == NULL)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    *init = (struct Init){.msg = (MsgHeaderT){
                              .protocolVersion = MSG_PROTOCOL_VERSION_PRIVATE,
                              .serialNumber = 0,
                              .size = 0,
                              .opNum = 1,
                              .stmtId = 0,
                              .flags = CS_FLAG_NONE,
                              .successNum = 0,
                              .priority = MSG_PRIO_NORMAL,
                              .extendSize = 0,
                              .msgMagicNum = MSG_VERIFY_NUMBER,
                          }};
    return GMERR_OK;
}

Status ProtoBuildReq(FixBufferT *req, const void *in)
{
    const ProtoHeadT *proto = in;
    FillSimpleStmtMsgHeader(req, proto->opCode);
    return CltFillMsgBody(req, proto);
}

Status DummyParseRsp(FixBufferT *rsp, void *out)
{
    // 用于请求只需要通过opStatus即可判断结果的场景，没有额外的数据需要解析
    DB_UNUSED(rsp);
    DB_UNUSED(out);
    return GMERR_OK;
}

Status PrefetchLabelsBuildReq(FixBufferT *req, const void *in)
{
    const PrefetchLabelsArgsT *args = (const PrefetchLabelsArgsT *)in;
    uint32_t pos = FixBufGetPos(req);
    FillSimpleStmtMsgHeader(req, args->opCode);
    uint32_t offset;
    Status ret = SecureFixBufReserveDataOffset(req, sizeof(uint32_t), &offset);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto ERROR_EXIT;
    }
    uint32_t reqNameCount = DB_MIN(CLT_MAX_PRE_FETCH_LABELS_COUNT, args->length);
    for (uint32_t i = 0; i < reqNameCount; i++) {
        const char *labelName = args->labelNames[i];
        ret = CltCheckStringValid(labelName, MAX_TABLE_NAME_LEN, "labelName");
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            goto ERROR_EXIT;
        }
        uint32_t size = CltStrLen(labelName);
        ret = SecureFixBufPutRawText(req, size, labelName);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            goto ERROR_EXIT;
        }
    }
    uint32_t *addr = SecureFixBufOffsetToAddr(req, offset);
    *addr = reqNameCount;
    return GMERR_OK;

ERROR_EXIT:
    // rollback to pos
    DB_LOG_ERROR(ret, "build request for prefetch labels.");
    FixBufInitPut(req, pos);
    return ret;
}

void CommonDDLCallback(GmcConnT *conn, const AsyncMsgContextT *ctx, Status ret, const void *out)
{
    DB_UNUSED(out);
    DB_UNUSED(conn);

    if (ret != GMERR_OK) {
        const TextT *error = DbGetLastErrorInfo();
        InvokeUserCallback(AsyncDDLDoneT, ctx->userCb, ctx->userData, ret, error->str);
        return;
    }

    InvokeUserCallback(AsyncDDLDoneT, ctx->userCb, ctx->userData, GMERR_OK, NULL);
}

void CommonDMLCallback(GmcConnT *conn, const AsyncMsgContextT *ctx, Status ret, const void *out)
{
    DB_UNUSED(conn);

    if (ret != GMERR_OK) {
        const TextT *error = DbGetLastErrorInfo();
        InvokeUserCallback(AsyncDMLDoneT, ctx->userCb, ctx->userData, 0, ret, error->str);
        return;
    }

    const uint32_t *affectRows = out;
    InvokeUserCallback(AsyncDMLDoneT, ctx->userCb, ctx->userData, *affectRows, GMERR_OK, NULL);
}

void ClearNamespaceCallback(GmcConnT *conn, const AsyncMsgContextT *ctx, Status ret, const void *out)
{
    DB_UNUSED(out);
    DB_UNUSED(conn);

    if (ret != GMERR_OK) {
        const TextT *error = DbGetLastErrorInfo();
        InvokeUserCallback(AsyncDDLDoneT, ctx->userCb, ctx->userData, ret, error->str);
        return;
    }
    const uint32_t *nspId = out;
    CltCataRemoveAllLabelsInNsp(*nspId);
    InvokeUserCallback(AsyncDDLDoneT, ctx->userCb, ctx->userData, GMERR_OK, NULL);
}

inline Status CltRequestSendOnly(GmcConnT *conn, RequestBuilderT buildReq, const void *in)
{
    // 通过 in 中的数据构造请求
    DB_POINTER2(conn, buildReq);
    FixBufferT *req = &conn->sendPack;
    Status ret = RpcReserveMsgOpHeader(req);
    ret = (ret == GMERR_OK) ? buildReq(req, in) : ret;
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "build request message.");
        return ret;
    }
    // 内部有last error，这里如果error，不处理
    return CltSendRequest(conn, req);
}

Status CltRequestSync(GmcConnT *conn, Status (*buildReq)(FixBufferT *req, const void *in), const void *in,
    Status (*parseRsp)(FixBufferT *rsp, void *out), void *out)
{
    DB_POINTER3(conn, buildReq, parseRsp);
    Status ret = CltAttachAndRefreshConn(conn, NULL, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Request Sync| refresh conn.");
        return ret;
    }
    FixBufferT *rsp = &conn->recvPack;
    ret = CltRequestSendOnly(conn, buildReq, in);
    if (ret != GMERR_OK) {
        // 内部有last error，这里不处理
        goto FINISH;
    }

    ret = CltRecvResponse(conn, rsp);
    if (ret != GMERR_OK) {
        // 内部有last error，这里不处理
        goto FINISH;
    }
    const MsgHeaderT *msg = RpcPeekMsgHeader(rsp);
    ret = msg->opStatus;
    if (ret != GMERR_OK) {
        // 内部有last error，这里不处理
        goto FINISH;
    }
    // 解析应答数据并保存到 out 中
    ret = parseRsp(rsp, out);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "parse response message.");
        goto FINISH;
    }

FINISH:
    CltDetachAndReleaseConn(conn, NULL, true);
    return ret;
}

Status CltRequestAsync(
    GmcConnT *conn, Status (*buildReq)(FixBufferT *req, const void *in), const void *in, AsyncMsgContextT *asyncCtx)
{
    DB_POINTER3(conn, buildReq, asyncCtx);
    DB_POINTER2(asyncCtx->parseRsp, asyncCtx->cltCallback);
    FixBufferT *req = &conn->sendPack;

    // 通过 in 中的数据构造请求
    Status ret = RpcReserveMsgOpHeader(req);
    ret = (ret == GMERR_OK) ? buildReq(req, in) : ret;
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "build request message.");
        return ret;
    }
    return CltSendRequestAsync(conn, req, asyncCtx);
}

static Status ConnectBuildReq(FixBufferT *req, const CliConnectRequestT *requestParams)
{
    DB_POINTER2(req, requestParams);
    MsgHeaderT *msg = RpcPeekMsgHeader(req);
    OpHeaderT *op = ProtocolPeekFirstOpHeader(req);
    msg->serviceId = DRT_SERVICE_CONN;
    msg->modelType = MODEL_DUMMY;
    op->opCode = MSG_OP_RPC_CONNECT;
    return FixBufPutData(req, requestParams, sizeof(CliConnectRequestT));
}

static Status ConnParseRsp(FixBufferT *rsp, CliConnectResponseT *response)
{
    DB_POINTER2(rsp, response);
    CliConnectResponseT *resp = FixBufGetData(rsp, sizeof(CliConnectResponseT));
    if (resp == NULL) {
        DB_LOG_EMRG_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
            "|connect stage2| connect response. (expectSize:pos:seekPos):(%" PRIu32 ", %" PRIu32 ", %" PRIu32 ").",
            (uint32_t)sizeof(CliConnectResponseT), FixBufGetPos(rsp), FixBufGetSeekPos(rsp));
        return GMERR_DATA_EXCEPTION;
    }
    *response = *resp;
    return GMERR_OK;
}

#if (defined SECUREFIXBUF)
inline static Status ShmBufSkipVerify(DbDataT ctx)
{
    DB_UNUSED(ctx);
    return GMERR_OK;
}
#endif

// 用于clientsyn 建连时发的消息，不区分同步异步订阅连接
ALWAYS_INLINE
Status CltSendConnRequest(GmcConnT *conn, const CliConnectRequestT *requestParams, CliConnectResponseT *rsponse,
    FixBufferT *req, FixBufferT *rsp)
{
#ifdef SECUREFIXBUF
    DbDataT ctx;
    ctx.ptr = &conn->entryCtx;
    SecureFixBufSetVerifyProc(req, ShmBufSkipVerify, ctx);
    SecureFixBufSetVerifyProc(rsp, ShmBufSkipVerify, ctx);
#endif
    Status ret = RpcReserveMsgOpHeader(req);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Connect stage2: reserve msg op header.");
        return ret;
    }
    ret = ConnectBuildReq(req, requestParams);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Connect stage2: build connect request message.");
        return ret;
    }
    ret = CltSendRequest(conn, req);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Connect stage2: send connection request.");
        return ret;
    }
    ret = CltRecvResponse(conn, rsp);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Connect stage2: recv connection request.");
        return ret;
    }
    conn->connStageTimePoint.recvSecConnRsp = DbRdtsc();
    const MsgHeaderT *msg = RpcPeekMsgHeader(rsp);
    ret = msg->opStatus;
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Connect stage2: connection.");
        return ret;
    }
    ret = ConnParseRsp(rsp, rsponse);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Connect stage2: parse connect response message.");
    }
    return ret;
}
