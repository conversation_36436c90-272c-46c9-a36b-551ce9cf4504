/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: clt_msg.h
 * Description: Implement of GMDB client message
 * Author:
 * Create: 2020-08-10
 */

#ifndef CLT_MSG_H
#define CLT_MSG_H

#include "clt_conn.h"
#include "clt_fill_msg.h"
#include "db_secure_msg_buffer.h"

#ifdef __cplusplus
extern "C" {
#endif

// 客户端消息队列规格
#define MAX_ASYNC_MESSAGE_BUFFER_QUEUE_SIZE (3u * DB_MEBI)
#define MAX_ASYNC_MESSAGE_BUFFER_QUEUE_NUMBER 10000

#define MAX_ASYNC_MESSAGE_BUFFER_QUEUE_SIZE_LOB (32u * DB_MEBI + 1 * DB_MEBI)
#define MAX_ASYNC_MESSAGE_BUFFER_QUEUE_NUMBER_LOB 10

typedef struct {
    MsgOpcodeRpcE opCode;
    const char **labelNames;
    const uint32_t length;
} PrefetchLabelsArgsT;

#define CLT_MAX_PRE_FETCH_LABELS_COUNT 200

Status CltPipeRecv(GmcConnT *conn, FixBufferT *buffer, bool noBlock);

Status CltSendRequest(GmcConnT *conn, FixBufferT *buffer);
Status CltRecvResponse(GmcConnT *conn, FixBufferT *buffer);
Status CltSendResponse(GmcConnT *conn, FixBufferT *buffer);

Status CltSendRequestAsync(GmcConnT *conn, FixBufferT *buffer, AsyncMsgContextT *asyncCtx);
Status CltRecvResponseAsync(GmcConnT *conn, FixBufferT *buffer);
Status CltMsgIsValid(FixBufferT *buffer);
Status CltMsgIsValidWithLog(FixBufferT *buffer, GmcConnT *conn);
Status CltMsgGetDtlError(FixBufferT *buffer, int64_t *errorCode, char *errorLableName, uint32_t errorLableNameSize);

inline static void UpdateConnRecvSize(GmcConnT *conn, const FixBufferT *buffer)
{
    uint32_t size = FixBufGetPos(buffer);
    conn->connDfxInfo.connRecvSize += size;
}

inline static void UpdateConnSendSize(GmcConnT *conn, const FixBufferT *buffer)
{
    uint32_t size = FixBufGetPos(buffer);
    (void)DbAtomicAdd64(&(conn->connDfxInfo.connSendSize), (uint64_t)size);
}

void ConsumeMessageBuffer(GmcConnT *conn);
#if defined(RTOSV2) || defined(RTOSV2X) || defined(HPE)
void ConsumeMessageBufferChannelLob(GmcConnT *conn);
void MsgBufferFreeListFreeAll(GmcConnT *conn);
#endif

inline static void ShrinkMsgBuffer(const GmcConnT *conn, FixBufferT *msgBuf)
{
    if (FixBufGetTotalLength(msgBuf) > conn->packShrinkThreshold) {
        SecureFixBufRelease(msgBuf);
    }
}

/***** 新的消息处理机制 *****/

typedef Status (*RequestBuilderT)(FixBufferT *req, const void *in);
typedef Status (*ResponseParserT)(FixBufferT *rsp, void *out);

typedef struct PubsubCommDfxInfo {
    uint32_t handledMsgNum;       // 已处理的订阅推送消息条数
    uint64_t subPushCbStartTime;  // 记录客户端处理epoll事件，进入SubPushCb的时刻
    uint64_t pipeRecvStartTime;   // 记录pubsub最近一次调用CltPipeRecv收消息的起始时刻
} PubsubCommDfxInfoT;

void CltMonitorCSCommunicationSingleTime(const MsgHeaderT *header, GmcConnT *conn);
void CltMonitorCSCommSingleTimeForPubsub(const MsgHeaderT *header, GmcConnT *conn, PubsubCommDfxInfoT *dfxInfo);
Status CltRequestSendOnly(GmcConnT *conn, RequestBuilderT buildReq, const void *in);
Status CltRequestSync(GmcConnT *conn, RequestBuilderT buildReq, const void *in, ResponseParserT parseRsp, void *out);
Status CltRequestAsync(GmcConnT *conn, RequestBuilderT buildReq, const void *in, AsyncMsgContextT *asyncCtx);
// 建连时发clientsyn的接口
Status CltSendConnRequest(GmcConnT *conn, const CliConnectRequestT *requestParams, CliConnectResponseT *rsponse,
    FixBufferT *req, FixBufferT *rsp);
static inline Status CltRequestSyncWithCheck(
    GmcStmtT *stmt, RequestBuilderT buildReq, const void *in, ResponseParserT parseRsp, void *out)
{
    Status ret = CltRequestSync(stmt->conn, buildReq, in, parseRsp, out);
#ifdef EXPERIMENTAL_NERGC
    if (DbIsTcp() && ret == GMERR_UNDEFINED_TABLE) {
        RemoveRemoteLabelFromCache(stmt);
    }
#endif
    return ret;
}
/***** 新通信框架使用的一些工具函数，临时放在这里 *****/

Status RpcReserveMsgOpHeader(FixBufferT *req);
Status ProtoBuildReq(FixBufferT *req, const void *in);
Status DummyParseRsp(FixBufferT *rsp, void *out);
Status PrefetchLabelsBuildReq(FixBufferT *req, const void *in);

void CommonDDLCallback(GmcConnT *conn, const AsyncMsgContextT *ctx, Status ret, const void *out);
void CommonDMLCallback(GmcConnT *conn, const AsyncMsgContextT *ctx, Status ret, const void *out);
void ClearNamespaceCallback(GmcConnT *conn, const AsyncMsgContextT *ctx, Status ret, const void *out);

#define InvokeUserCallback(type, userCb, ...) \
    if ((userCb) != NULL) {                   \
        ((type)(userCb))(__VA_ARGS__);        \
    }

inline static void FillPublicStmtMsgHeader(FixBufferT *req, MsgOpcodeRpcE opCode)
{
    MsgHeaderT *msg = RpcPeekMsgHeader(req);
    OpHeaderT *op = ProtocolPeekFirstOpHeader(req);
    msg->serviceId = DRT_SERVICE_STMT;
    msg->modelType = MODEL_PUBLIC;
    DB_ASSERT(msg->stmtId == 0);  // RpcReserveMsgOpHeader 给初值
    op->opCode = (uint32_t)opCode;
}

inline static void FillSimpleStmtMsgHeader(FixBufferT *req, MsgOpcodeRpcE opCode)
{
    MsgHeaderT *msg = RpcPeekMsgHeader(req);
    OpHeaderT *op = ProtocolPeekFirstOpHeader(req);
    msg->serviceId = DRT_SERVICE_STMT;
    msg->modelType = MODEL_FASTPATH;
    DB_ASSERT(msg->stmtId == 0);  // RpcReserveMsgOpHeader 给初值
    op->opCode = (uint32_t)opCode;
}

inline static void FillStmtMsgHeader(FixBufferT *req, MsgOpcodeRpcE opCode, uint16_t stmtId)
{
    MsgHeaderT *msg = RpcPeekMsgHeader(req);
    OpHeaderT *op = ProtocolPeekFirstOpHeader(req);
    msg->serviceId = DRT_SERVICE_STMT;
    msg->modelType = MODEL_FASTPATH;
    msg->stmtId = stmtId;
    op->opCode = (uint32_t)opCode;
}

inline static void FillYangDataServiceMsgHeader(FixBufferT *req, MsgOpcodeRpcE opCode)
{
    MsgHeaderT *msg = RpcPeekMsgHeader(req);
    OpHeaderT *op = ProtocolPeekFirstOpHeader(req);
    msg->serviceId = DRT_SERVICE_STMT;
    msg->modelType = MODEL_YANG;
    op->opCode = (uint32_t)opCode;
}

inline static AsyncMsgContextT MakeAsyncMsgContext(ResponseParserT parseRsp,
    void (*cltCallback)(GmcConnT *conn, const AsyncMsgContextT *ctx, Status ret, const void *out),  //
    void *userCb, void *userData, uint8_t notDistribute)
{
    AsyncMsgContextT ctx = {};
    ctx.parseRsp = parseRsp;
    ctx.cltCallback = cltCallback;
    ctx.userCbAddr = userCb;
    ctx.userData = userData;
    ctx.notDistribute = notDistribute;
#ifdef EXPERIMENTAL_NERGC
    ctx.remoteLabelIdArr = NULL;
#endif
    return ctx;
}

// 函数指针兼容时直接使用相应的Common接口，提升性能
#define U32_RESULT_PARSE_RSP ((ResponseParserT)SecureFixBufGetUint32)
#define OBJECT_RESULT_PARSE_RSP ((ResponseParserT)SecureFixBufGetObject)
#define OBJECT_NULLABLE_RESULT_PARSE_RSP ((ResponseParserT)SecureFixBufGetObjectNullable)
#define TEXT_RESULT_PARSE_RSP ((ResponseParserT)SecureFixBufGetText)
#define TEXT_NULLABLE_RESULT_PARSE_RSP ((ResponseParserT)SecureFixBufGetTextNullable)

// 常用于DML操作的别名
#define PARSE_AFFECTED_ROWS U32_RESULT_PARSE_RSP

#ifdef __cplusplus
}
#endif

#endif  // CLT_MSG_H
