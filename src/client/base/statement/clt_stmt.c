/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: clt_stmt.c
 * Description: Implement of GMDB client stmt
 * Author:
 * Create: 2020-08-05
 */

#include "clt_stmt.h"
#include "gmc_internal.h"

#include "clt_async_conn.h"
#include "clt_meta_cache.h"
#include "clt_msg.h"
#include "clt_fill_msg.h"
#include "clt_dml_kv.h"
#include "clt_graph_dml_vertex.h"
#include "dm_data_kv.h"
#include "dm_yang_interface.h"
#include "dm_yang_subtree.h"
#include "db_alarm.h"
#include "ds_concurrency_control.h"
#include "clt_resource.h"
#include "clt_check.h"
#include "clt_da_read.h"
#include "clt_da_vertex_directread.h"
#include "clt_da_write.h"
#include "clt_da_write_respool.h"
#include "clt_stmt_helper.h"
#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
#include "clt_ts.h"
#endif
#include "se_trx.h"

Status CltExecDsCypherFetch(GmcStmtT *stmt)
{
    DrRunCtxT *drRunCtx = (DrRunCtxT *)&stmt->drRunCtx;
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    Status ret =
        DsAcqLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->labelLatchVersionId, drRunCtxBase->sessionCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    SeRunCtxHdT seRunCtx = (SeRunCtxHdT)drRunCtx->base.seRunCtxHandle;
    bool transOpen = SeTransStartDirectReadCheck(seRunCtx);
    ret = CltChangeSeTransStateBeforeRead(transOpen, seRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT;
    }
    ret = MainStoreReadFetchNext(drRunCtx, &stmt->cacheRows, &stmt->eof);
    CltChangeSeTransStateAfterRead(transOpen, seRunCtx);
EXIT:
    DsReleaseLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->sessionCtx);
    if (SECUREC_LIKELY(ret != GMERR_OK || stmt->eof)) {
        DirectReadCtxClose(drRunCtx, DIRECT_ACCESS_CLOSE_HEAP_CURSOR);
    }
    return ret;
}

Status CltStmtRespMsg(GmcStmtT *stmt, const ProtoHeadT *proto)
{
    GmcConnT *conn = stmt->conn;
    FixBufferT *buf = &conn->sendPack;
    Status ret = GMERR_OK;
    // Resp Batch操作在过程中已填充报文体
    if (proto->opCode != MSG_OP_RPC_DATALOG_SUB_BATCH_RESPONSE) {
        ret = CltFillMsg(buf, stmt->stmtId, proto);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    FixBufferT *recvBuf = &stmt->conn->recvPack;
    MsgHeaderT *recvMsg = RpcPeekMsgHeader(recvBuf);
    MsgHeaderT *sendMsg = RpcPeekMsgHeader(buf);
    sendMsg->serialNumber = recvMsg->serialNumber;
    return CltSendResponse(conn, buf);
}

Status CltStmtSendMsg(GmcStmtT *stmt, const ProtoHeadT *proto)
{
    GmcConnT *conn = stmt->conn;
    FixBufferT *buf = &conn->sendPack;

    Status ret = CltFillMsg(buf, stmt->stmtId, proto);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (stmt->isDataService && stmt->notDistribute) {
        MsgHeaderT *header = RpcPeekMsgHeader(buf);
        CltSetDataServiceHeaderInfo(header, proto->opCode);
        const MsgExtendT extend = {MSG_EXTENTD_TAG_NOT_DISTRIBUTE, sizeof(uint8_t)};
        uint8_t notDistribute = stmt->notDistribute;
        ret = MsgAddExtend(buf, &extend, &notDistribute);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return CltSendRequest(conn, buf);
}

Status CltStmtSendAndRecvMsg(GmcStmtT *stmt, const ProtoHeadT *proto)
{
#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
    if (stmt->conn->isCsMode && stmt->conn->msgCtx == NULL) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "send msg without attach shmCtx");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
#endif
    Status result = CltStmtSendMsg(stmt, proto);
    if (result == GMERR_OK) {
        result = CltRecvStmtResponseWithCheckOpStatus(stmt);
    }
    return result;
}

Status CltRecvDrStmtResponseWithCheckOpStatus(GmcDirectReadStmtT *stmt)
{
    GmcConnT *conn = stmt->conn;
    FixBufferT *buffer = stmt->recvPack;
    Status result = CltRecvResponse(conn, buffer);
    if (SECUREC_LIKELY(result == GMERR_OK)) {
        const MsgHeaderT *header = RpcPeekMsgHeader(buffer);
        result = header->opStatus;
        conn->cltOpSegTime.sendMsgStartTime = header->reqSendStartTime;
        conn->cltOpSegTime.rspSendStartTime = header->rspSendStartTime;
        conn->cltOpSegTime.rspGetTime = DbRdtsc();
    }
    return result;
}

Status CltDrStmtSendMsg(GmcDirectReadStmtT *stmt, const ProtoHeadT *proto)
{
    GmcConnT *conn = stmt->conn;
    FixBufferT *buf = &conn->sendPack;

    Status ret = CltFillMsg(buf, *(stmt->stmtId), proto);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltSendRequest(conn, buf);
}

Status CltDrStmtSendAndRecvMsg(GmcDirectReadStmtT *stmt, const ProtoHeadT *proto)
{
    Status result = CltDrStmtSendMsg(stmt, proto);
    if (result == GMERR_OK) {
        result = CltRecvDrStmtResponseWithCheckOpStatus(stmt);
    }
    return result;
}

Status CltStmtSendAndRecvAffectedRows(GmcStmtT *stmt, const ProtoHeadT *proto)
{
    Status result = CltStmtSendAndRecvMsg(stmt, proto);
    if (SECUREC_UNLIKELY(!stmt->isDataService && result != GMERR_OK)) {
        return result;
    }

    Status ret = SecureFixBufGetUint32(&stmt->recvPack, &stmt->affectRows);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "get affectRows in received msg.");
        return result != GMERR_OK ? result : ret;
    }

    if (stmt->isDataService) {
        ret = CltStmtSetDtlErrorFromResponse(stmt);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return result != GMERR_OK ? result : ret;
        }
    }

    return result;
}

Status CltRecvSingleDetectConfig(FixBufferT *rsp, DbListT *configsDetectRes)
{
    TextT configName = {};
    Status result = SecureFixBufGetUint32(rsp, &(configName.len));
    if (result != GMERR_OK) {
        return result;
    }
    configName.str = SecureFixBufGetData(rsp, configName.len + 1);
    if (configName.str == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    TextT configCurrentValue = {};
    result = SecureFixBufGetUint32(rsp, &(configCurrentValue.len));
    if (result != GMERR_OK) {
        return result;
    }
    configCurrentValue.str = SecureFixBufGetData(rsp, configCurrentValue.len + 1);
    if (configCurrentValue.str == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    TextT configSafeValue = {};
    result = SecureFixBufGetUint32(rsp, &(configSafeValue.len));
    if (result != GMERR_OK) {
        return result;
    }
    configSafeValue.str = SecureFixBufGetData(rsp, configSafeValue.len + 1);
    if (configSafeValue.str == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    bool *isSafe = SecureFixBufGetData(rsp, sizeof(bool));
    if (isSafe == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    GmcIdsConfigDetectResT singleCfgDetectRes = {.isSafe = *isSafe,
        .configName = configName,
        .configCurrentValue = configCurrentValue,
        .configSafeValue = configSafeValue};
    return DbAppendListItem(configsDetectRes, &singleCfgDetectRes);
}

Status CltStmtSendAndRecvDetectConfigs(
    GmcStmtT *stmt, const ProtoHeadT *proto, uint32_t *detectCfgNum, DbListT **configsDetectRes)
{
    GmcConnT *conn = stmt->conn;
    FixBufferT *buf = &conn->sendPack;
    Status result = CltFillMsgHeader(buf, proto->opCode, stmt->stmtId);
    result = (result == GMERR_OK) ? CltSendRequest(conn, buf) : result;
    result = (result == GMERR_OK) ? CltRecvStmtResponseWithCheckOpStatus(stmt) : result;
    if (result != GMERR_OK) {
        return result;
    }
    result = SecureFixBufGetUint32(&stmt->recvPack, detectCfgNum);
    if (result != GMERR_OK) {
        return result;
    }
    // 该内存在gmids工具使用完后进行释放，位于GmIdsConfigDetection函数
    *configsDetectRes = DbDynMemCtxAlloc(stmt->memCtx, sizeof(DbListT));
    if (*configsDetectRes == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc DbList for configsDetectRes");
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateList(*configsDetectRes, sizeof(GmcIdsConfigDetectResT), stmt->memCtx);
    uint32_t i = 0;
    while (i < *detectCfgNum) {
        result = CltRecvSingleDetectConfig(&stmt->recvPack, *configsDetectRes);
        if (result != GMERR_OK) {
            DbDynMemCtxFree(
                stmt->memCtx, *configsDetectRes);  // 失败场景下内存指针没有传递到外面，并且会返回错误码，不用置空
            return result;
        }
        i++;
    }
    return result;
}

Status CltRecvStmtResponseWithCheckOpStatus(GmcStmtT *stmt)
{
    GmcConnT *conn = stmt->conn;
    FixBufferT *buffer = &stmt->recvPack;
    Status result = CltRecvResponse(conn, buffer);
    if (SECUREC_LIKELY(result == GMERR_OK)) {
        const MsgHeaderT *header = RpcPeekMsgHeader(buffer);
        result = header->opStatus;
        conn->cltOpSegTime.sendMsgStartTime = header->reqSendStartTime;
        conn->cltOpSegTime.rspSendStartTime = header->rspSendStartTime;
        conn->cltOpSegTime.rspGetTime = DbRdtsc();
#ifdef EXPERIMENTAL_NERGC
        if (DbIsTcp()) {
            if (result == GMERR_UNDEFINED_TABLE) {
                RemoveRemoteLabelFromCache(stmt);
            }
        }
#endif
    }
    return result;
}

#ifdef FEATURE_KV
static bool IsKvPrivChanged(const CltCataLabelT *cltLabel, DirectAccessRunCtxT *baseRunCtx)
{
    DmKvLabelT *kvLabel = cltLabel->kvLabel;
    if (cltLabel->objPrivVersion != kvLabel->objPrivVersion) {
        return true;
    }
    if (SECUREC_UNLIKELY(baseRunCtx->objPrivNsp == NULL)) {
        baseRunCtx->objPrivNsp = DbShmPtrToAddr(kvLabel->nspObjPrivShmPtr);
        if (baseRunCtx->objPrivNsp == NULL) {  // 后面check的时候会返回失败值
            DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED,
                "trans shmptr to addr, segId: %" PRIu32 ", offset: %" PRIu32, kvLabel->nspObjPrivShmPtr.segId,
                kvLabel->nspObjPrivShmPtr.offset);
            return true;
        }
    }
    if (baseRunCtx->objPrivNsp->objPrivVersion != baseRunCtx->nspPrivVersion) {
        return true;
    }
    return false;
}

Status KvPrivCheckInner(
    uint32_t sysPrivVersion, GmcStmtT *stmt, DmReusableMetaT *metaInfoAddr, CltOperKvT *operKv, CataRoleT *role)
{
    DirectAccessRunCtxT *drRunCtxBase = &stmt->drRunCtx.base;
    if (!drRunCtxBase->isKvHasPriv) {
        Status ret = DirectAccessPrivCheck(
            &stmt->drRunCtx.base, &metaInfoAddr->objPrivilege, CATA_KV_TABLE, SELECTANY_PRIV, DM_OBJ_SELECT_PRIV);
        if (ret == GMERR_OK) {
            drRunCtxBase->isKvHasPriv = true;
        }
        return ret;
    }
    bool isObjPivChanged = IsKvPrivChanged(operKv->cltCataLabel, &stmt->drRunCtx.base);
    if (isObjPivChanged) {
        return DirectAccessPrivCheck(
            &stmt->drRunCtx.base, &metaInfoAddr->objPrivilege, CATA_KV_TABLE, SELECTANY_PRIV, DM_OBJ_SELECT_PRIV);
    }
    if (sysPrivVersion != role->sysPrivVersion) {
        return DirectAccessPrivCheck(
            &stmt->drRunCtx.base, &metaInfoAddr->objPrivilege, CATA_KV_TABLE, SELECTANY_PRIV, DM_OBJ_SELECT_PRIV);
    }
    return GMERR_OK;
}

Status KvPrivCheck(GmcStmtT *stmt)
{
    DirectAccessRunCtxT *drRunCtxBase = &stmt->drRunCtx.base;
    if (!drRunCtxBase->hasInited) {
        DB_SET_LASTERR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "Stmt preparing.");
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    CataRoleT *role = drRunCtxBase->role;
    uint32_t sysPrivVersion = drRunCtxBase->sysPrivVersion;
    CltOperKvT *operKv = CltGetOperationContext(stmt);
    DmKvLabelT *label = operKv->cltCataLabel->kvLabel;
    DmReusableMetaT *metaInfoAddr = (DmReusableMetaT *)DbShmPtrToAddr(label->metaInfoShm);
    if (metaInfoAddr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "metaInfoAddr null, segId: %" PRIu32 ", offset: %" PRIu32,
            label->metaInfoShm.segId, label->metaInfoShm.offset);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (metaInfoAddr->vertexLabelId != label->metaCommon.metaId) {
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_TABLE, "dropped.");
        return GMERR_UNDEFINED_TABLE;
    }
    return KvPrivCheckInner(sysPrivVersion, stmt, metaInfoAddr, operKv, role);
}
#endif

Status PrivCheckAgain(GmcStmtT *stmt)
{
    DrRunCtxT *drRunCtx = &stmt->drRunCtx;
    DsScanModeE scanMode = drRunCtx->dsFetchArgs.scanMode;
    switch (scanMode) {
        case DS_SCAN_VERTEX_SEQUENCE:         // vertex table full scan
        case DS_LOOKUP_VERTEX_INDEX_PRIMARY:  // vertex primary key lookup
#ifdef ART_CONTAINER
        case DS_SCAN_VERTEX_INDEX_PRIMARY:
#endif
        case DS_SCAN_VERTEX_INDEX_SECONDARY: {  // vertex non unique index scan
            CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
            return VertexPrivCheck(drRunCtx, cltVertex->cltCataLabel);
        }
#ifdef FEATURE_KV
        case DS_SCAN_KV_SEQUENCE:  // kv table full scan
        case DS_SCAN_KV_INDEX:     // kv table primary key scan
            return KvPrivCheck(stmt);
#endif
        default:
            return GMERR_INTERNAL_ERROR;
    }
}

PRI_IDX_READ_FUNC Status CltSendDirectReadMsg(GmcStmtT *stmt, ProtoHeadT *proto)
{
    Status ret;
    ret = CltConnServerCheck(stmt->conn);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ShrinkMsgBuffer(stmt->conn, &stmt->recvPack);

    switch (SWITCH_LIKELY(proto->opCode, MSG_OP_RPC_SCAN_VERTEX_BEGIN)) {
        case MSG_OP_RPC_IS_VERTEX_EXIST:
        case MSG_OP_RPC_SCAN_VERTEX_BEGIN: {
            CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
            ret = VertexPrivCheck(&stmt->drRunCtx, cltVertex->cltCataLabel);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
            ret = CltExecDsVertexScan(stmt, proto);
            if (SECUREC_LIKELY(ret == GMERR_OK)) {
                cltVertex->vertexFetchType = CLT_FETCH_VERTEX_DIRECT_READ;
            }
            return ret;
        }
#ifdef FEATURE_KV
        case MSG_OP_RPC_GET_KV:
        case MSG_OP_RPC_SCAN_KV_BEGIN:
        case MSG_OP_RPC_IS_KV_EXIST:
            ret = KvPrivCheck(stmt);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
            ret = ExecDsKvScan(stmt, proto);
            if (SECUREC_LIKELY(ret == GMERR_OK)) {
                CltOperKvT *operKv = CltGetOperationContext(stmt);
                operKv->fetchType = CLT_FETCH_VERTEX_DIRECT_READ;
            }
            return ret;
#endif
        case MSG_OP_RPC_FETCH_CYPHER:
            FixBufResetMem(&stmt->recvPack);
            ret = PrivCheckAgain(stmt);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
            return CltExecDsCypherFetch(stmt);
        default:
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY, "op code when send direct read msg");
            return GMERR_INVALID_PROPERTY;
    }
}

Status CltStmtSetDtlErrorFromResponse(GmcStmtT *stmt)
{
    if (!stmt->isDataService) {
        return GMERR_OK;
    }
    if (stmt->dtlErrorLabelName == NULL) {
        // dtlErrorLabelName will be allocated at first time and freed when free stmt
        stmt->dtlErrorLabelName = DbDynMemCtxAlloc(stmt->memCtx, MAX_TABLE_NAME_LEN);
        if (stmt->dtlErrorLabelName == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "alloc dtlErrorLabelName");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    }
    stmt->dtlErrorLabelName[0] = '\0';
    return CltMsgGetDtlError(&stmt->recvPack, &stmt->dtlErrorCode, stmt->dtlErrorLabelName, MAX_TABLE_NAME_LEN);
}

Status CltStmtSendAndRecvScanMsg(GmcStmtT *stmt, const ProtoHeadT *proto)
{
    Status ret = CltStmtSendMsg(stmt, proto);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (proto->opCode != MSG_OP_RPC_FETCH_CYPHER) {
        if (stmt->stmtType == CLT_STMT_TYPE_VERTEX) {
            CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
            cltVertex->vertexFetchType = CLT_FETCH_VERTEX_CS;
#ifdef FEATURE_KV
        } else if (stmt->stmtType == CLT_STMT_TYPE_KV) {
            CltOperKvT *operKv = CltGetOperationContext(stmt);
            operKv->fetchType = CLT_FETCH_VERTEX_CS;
#endif
        } else {
            return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
        }
    }
    // 特殊情况扫描没有ack
    if (proto->opCode == MSG_OP_RPC_GET_KV || proto->opCode == MSG_OP_RPC_IS_KV_EXIST) {
        return CltRecvStmtResponseWithCheckOpStatus(stmt);
    }
    return CltGetScanRecvDataRpc(stmt);
}

static void CltClearStmtOperVertex(GmcStmtT *stmt, bool isClearVertexLabel);
#ifdef FEATURE_EDGELABEL
static void CltClearStmtOperTopo(GmcStmtT *stmt);
#endif
#ifdef FEATURE_KV
static void CltClearStmtOperKv(GmcStmtT *stmt);
#endif
#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
static void CltClearStmtOperTs(GmcStmtT *stmt, bool isClearVertexLabel);
#endif
#if defined(EXPERIMENTAL_NERGC)
static void CltClearStmtOperRemote(GmcStmtT *stmt, bool isClearVertexLabel);
#endif

void ClearOpData(GmcStmtT *stmt, bool isClearVertexLabel)
{
    // 暂时删不掉
#if defined(EXPERIMENTAL_NERGC)
    if (DbIsTcp()) {
        CltClearStmtOperRemote(stmt, isClearVertexLabel);
        return;
    }

#endif
    if (stmt->stmtType == CLT_STMT_TYPE_VERTEX) {
        CltClearStmtOperVertex(stmt, isClearVertexLabel);
    } else if (stmt->stmtType == CLT_STMT_TYPE_EDGE_TOPO) {
#ifdef FEATURE_EDGELABEL
        CltClearStmtOperTopo(stmt);
#endif
    } else if (stmt->stmtType == CLT_STMT_TYPE_KV) {
#ifdef FEATURE_KV
        CltClearStmtOperKv(stmt);
#endif
    } else if (stmt->stmtType == CLT_STMT_TYPE_TS) {
#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
        CltClearStmtOperTs(stmt, isClearVertexLabel);
#endif
    }
}

inline static void OpClear(CltOperVertexT *op)
{
    op->idx.isIdxSelect = false;
    op->idx.isKeyBufSet = false;
    op->idx.rangeScanFlags = 0;
    op->seriVertexBuf.str = NULL;
    op->seriVertexBuf.len = 0;
    DbClearList(op->idx.leftValue);
    DbClearList(op->idx.rightValue);
    DbClearList(op->orderByParams);
    DbClearList(op->idx.yangListKeyValue);
    StructFilterListReset(&op->structFilterList);
}

void CltInitStmtOperVertex(GmcStmtT *stmt, DmVertexT *vertex, bool isInit)  // 抽出来以便reset流程复用代码
{
    CltOperVertexT *op = CltGetOperationContext(stmt);
    CltCataLabelT *cataLabel = op->cltCataLabel;
    if (isInit) {
        *op = (CltOperVertexT){.vertex = vertex, .cltCataLabel = cataLabel, .doDeseri = true, .idx = {0}};
        // op上面挂的list生命周期和stmt是一样的，最后free stmt的时候一起释放
        op->idx.leftValue = &stmt->leftValue;
        op->idx.rightValue = &stmt->rightValue;
        op->idx.yangListKeyValue = &stmt->yangListKeyValue;
        op->orderByParams = &stmt->orderByParams;
        // 记录数需要清0
        DbClearList(op->idx.leftValue);
        DbClearList(op->idx.rightValue);
        DbClearList(op->idx.yangListKeyValue);
        DbClearList(op->orderByParams);
    } else {
        *op = (CltOperVertexT){.vertex = vertex,
            .cltCataLabel = op->cltCataLabel,
            .doDeseri = true,
            .orderByParams = op->orderByParams,
            .idx = op->idx};  // idx需要做缓存，不能每次都释放
        OpClear(op);
    }
    stmt->stmtType = CLT_STMT_TYPE_VERTEX;
}

void CltInitDrStmtOperVertex(GmcDirectReadStmtT *stmt, DmVertexT *vertex, bool isInit)  // 抽出来以便reset流程复用代码
{
    CltDrOperVertexT *op = CltGetDrStmtOperationContext(stmt);
    CltCataLabelT *cataLabel = op->cltCataLabel;
    if (isInit) {
        *op = (CltDrOperVertexT){.vertex = vertex, .cltCataLabel = cataLabel, .doDeseri = true, .idx = {0}};
    } else {
        *op = (CltDrOperVertexT){.vertex = vertex,
            .cltCataLabel = op->cltCataLabel,
            .doDeseri = true,
            .idx = op->idx};  // idx需要做缓存，不能每次都释放
    }
    stmt->stmtType = CLT_STMT_TYPE_VERTEX;
}

void ReleaseDwRunCtx4Stmt(GmcStmtT *stmt)
{
    if (stmt->dwRunCtx == NULL) {
        return;
    }
    ReleaseDwRunCtx4StmtInner(stmt->dwRunCtx, stmt->memCtx);
    DbDynMemCtxFree(stmt->memCtx, stmt->dwRunCtx);
    stmt->dwRunCtx = NULL;
    stmt->dwRunCtxHasAllocated = false;
    stmt->vlUseDirectWrite = false;
    if (stmt->resInfo != NULL) {  // resInfo在直连写场景下使用
        DbDynMemCtxFree(stmt->memCtx, stmt->resInfo);
        stmt->resInfo = NULL;
    }
}

void ReleaseWcRunCtx4Stmt(GmcStmtT *stmt)
{
    if (stmt->wcRunCtx == NULL) {
        return;
    }
    DbDynMemCtxFree(stmt->memCtx, stmt->wcRunCtx);
    stmt->wcRunCtx = NULL;
    stmt->wcRunCtxHasAllocated = false;
}

static void CltClearStmtOperVertex(GmcStmtT *stmt, bool isClearVertexLabel)
{
    DB_ASSERT(stmt->stmtType == CLT_STMT_TYPE_VERTEX);
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    // FreeStmt和切表时销毁缓存的indexKey的内存
    DmDestroyIndexKey(operVertex->idx.leftKey);
    DmDestroyIndexKey(operVertex->idx.rightKey);
    operVertex->idx.rightKey = NULL;
    operVertex->idx.leftKey = NULL;
    operVertex->structFilterList.list = NULL;
#ifdef FEATURE_GQL
    operVertex->structFilterList.isUsed = false;
#endif  // FEATURE_GQL

    DmVertexT *vertex = operVertex->vertex;
    DirectReadCtxClose(&stmt->drRunCtx, DIRECT_ACCESS_CLOSE_HEAP_CURSOR_CTX);
    DmDestroyVertex(vertex);
    operVertex->vertex = NULL;
    if (stmt->dwRunCtx != NULL) {
        DmDestroyVertex(stmt->dwRunCtx->maxVersionVertex);
        stmt->dwRunCtx->maxVersionVertex = NULL;
    }
    if (isClearVertexLabel && operVertex->cltCataLabel != NULL) {
        CltCataCloseVertexLabel(operVertex->cltCataLabel);
        operVertex->cltCataLabel = NULL;
        if (stmt->dwRunCtx != NULL && stmt->dwRunCtx->maxVersionCltCataLabel != NULL) {
            CltCataCloseVertexLabel(stmt->dwRunCtx->maxVersionCltCataLabel);
            stmt->dwRunCtx->maxVersionCltCataLabel = NULL;
        }
    }
    operVertex->sub = NULL;
    ReleaseDwRunCtx4Stmt(stmt);
    ReleaseWcRunCtx4Stmt(stmt);
    return;
}

void CltInitStmtOperEdgeT(GmcStmtT *stmt)
{
    ClearOpData(stmt, true);

    CltOperEdgeT *operEdge = CltGetOperationContext(stmt);
    DbCreateListWithExtendSize(&operEdge->srcVrtxPkFilter, sizeof(DmValueT), DM_MAX_KEY_PROPE_NUM, stmt->opCtx);
    DbCreateListWithExtendSize(&operEdge->dstVrtxPkFilter, sizeof(DmValueT), DM_MAX_KEY_PROPE_NUM, stmt->opCtx);
    stmt->stmtType = CLT_STMT_TYPE_EDGE;
    stmt->isDataService = false;
}

#ifdef FEATURE_KV
static void CltClearStmtOperKv(GmcStmtT *stmt)
{
    DB_ASSERT(stmt->stmtType == CLT_STMT_TYPE_KV);
    CltOperKvT *operKv = (CltOperKvT *)CltGetOperationContext(stmt);
    DirectReadCtxClose(&stmt->drRunCtx, DIRECT_ACCESS_CLOSE_HEAP_CURSOR_CTX);
    DmDestroyKv(operKv->kv);
    operKv->kv = NULL;
    CltCataCloseKvTable(operKv->cltCataLabel);
    operKv->cltCataLabel = NULL;
    operKv->sub = NULL;
}
#endif

#ifdef FEATURE_EDGELABEL
static void CltClearStmtOperTopo(GmcStmtT *stmt)
{
    DB_ASSERT(stmt->stmtType == CLT_STMT_TYPE_EDGE_TOPO);
    CltNeighborCtxT *cltNeighborCtx = CltGetOperationContext(stmt);
    if (cltNeighborCtx->heapRunHdl != NULL) {
        HeapLabelResetCtx(cltNeighborCtx->heapRunHdl);
        HeapLabelReleaseRunctx(cltNeighborCtx->heapRunHdl);
        cltNeighborCtx->heapRunHdl = NULL;
    }
    if (cltNeighborCtx->edgeTopoCtx != NULL && cltNeighborCtx->seCursor != NULL) {
        EdgeTopoCloseCursor(cltNeighborCtx->edgeTopoCtx, cltNeighborCtx->seCursor);
        cltNeighborCtx->seCursor = NULL;
    }
    if (cltNeighborCtx->edgeTopoCtx != NULL) {
        EdgeTopoClose(cltNeighborCtx->edgeTopoCtx);
        cltNeighborCtx->edgeTopoCtx = NULL;
    }
    if (cltNeighborCtx->vertex != NULL) {
        DmDestroyVertex(cltNeighborCtx->vertex);
        cltNeighborCtx->vertex = NULL;
    }
    if (cltNeighborCtx->cltCataLabel != NULL) {
        CltCataCloseEdgeLabel(cltNeighborCtx->cltCataLabel);
        cltNeighborCtx->cltCataLabel = NULL;
    }
}
#endif

void CltDynCloseVertexLabel(CltCataLabelT *cltCataLabel)
{
    if (cltCataLabel == NULL) {
        return;
    }
    if (cltCataLabel->vertexLabel != NULL) {
        DmDestroyVertexLabel(cltCataLabel->vertexLabel);
        cltCataLabel->vertexLabel = NULL;
    }
    DbDynMemCtxFree(g_gmdbCltInstance.cltCommCtx, cltCataLabel);
}

#if defined(EXPERIMENTAL_NERGC)
static void CltClearStmtOperRemote(GmcStmtT *stmt, bool isClearVertexLabel)
{
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    if (isClearVertexLabel && operVertex->cltCataLabel != NULL && stmt->conn->connType != GMC_CONN_TYPE_SUB) {
        DmDestroyVertex(operVertex->vertex);
        (void)DbAtomicDec(&operVertex->cltCataLabel->cltRefCount);
        operVertex->cltCataLabel = NULL;
        operVertex->vertex = NULL;
    }
    return;
}
#endif

#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
static void CltClearStmtOperTs(GmcStmtT *stmt, bool isClearVertexLabel)
{
    DB_ASSERT(stmt->stmtType == CLT_STMT_TYPE_TS);
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    if (isClearVertexLabel && stmt->tsInfo->isLabelPrepared && operVertex->cltCataLabel != NULL) {
        if (stmt->conn->isCsMode) {
            CltDynCloseVertexLabel(operVertex->cltCataLabel);
        } else {
            CltCataCloseVertexLabel(operVertex->cltCataLabel);
        }
        operVertex->cltCataLabel = NULL;
    }
    return;
}
#endif

void AllocStmtId(GmcStmtT *stmt)
{
    // 已经分配stmtId则直接返回
    GmcConnT *conn = stmt->conn;
    if (stmt->stmtId != 0) {
        return;
    }

    // 查找未分配的stmtId及链表的插入位置
    uint32_t id = 0;
    TagLinkedListT *dummy = &conn->stmtIdList;
    TagLinkedListT *node = dummy;
    const GmcStmtT *currStmt;
    do {
        ++id;  // stmtId == 0 预留给默认stmt，从1开始申请
        node = node->next;
        if (node == dummy) {
            break;
        }
        currStmt = LIST_ENTRY(node, GmcStmtT, stmtIdListNode);
    } while (id >= currStmt->stmtId);

    // 将stmt加入链表。这里的截断是安全的，因为id最大等于count + 1，不超过stmts->limit
    DB_ASSERT(id <= conn->childStmtLimit);
    stmt->stmtId = (uint16_t)id;
    DbLinkedListInsert(node, &stmt->stmtIdListNode);
}

void ReleaseStmtId(GmcStmtT *stmt)
{
    // 没有分配stmtId则直接返回
    GmcConnT *conn = stmt->conn;
    if (stmt->stmtId == 0) {
        return;
    }

    if (conn->connType != GMC_CONN_TYPE_SYNC) {
        // 因为释放stmt消息服务器不会返回应答，与大多数异步处理逻辑不一致
        // 同时异步操作不会执行查询，不用释放远端stmt
        return;
    }

    if (CltAttachAndRefreshConn(conn, stmt, true) != GMERR_OK) {
        DbLinkedListRemove(&stmt->stmtIdListNode);
        return;
    }

    FixBufferT *req = &conn->sendPack;
    Status ret = RpcReserveMsgOpHeader(req);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Reserve msg opHeader when release stmt Id:%" PRIu16 ".", stmt->stmtId);
        DbLinkedListRemove(&stmt->stmtIdListNode);
        CltDetachAndReleaseConn(conn, NULL, true);
        return;
    }

    FillStmtMsgHeader(req, MSG_OP_RPC_RELEASE_STMT, stmt->stmtId);

    ret = CltSendRequest(conn, req);
    DB_UNUSED(ret);

    stmt->stmtId = 0;  // 保证函数可以多次调用（虽然目前没啥用）
    DbLinkedListRemove(&stmt->stmtIdListNode);
    CltDetachAndReleaseConn(conn, NULL, true);
}

static Status CltAllocStmtWithMemCtx(DbMemCtxT *memCtx, GmcConnT *conn, GmcStmtT **stmtx)
{
    if (conn->childStmtCount >= conn->childStmtLimit) {
        DB_SET_LASTERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "Num of child stmt.");
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    // 该内存在用户调用GmcFreeStmt接口时进行释放
    GmcStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(GmcStmtT));
    if (stmt == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "client alloc stmt.");
        return GMERR_OUT_OF_MEMORY;
    }
    // memCtx用途：客户端操作数据内存
    // 生命周期：请求级别
    // 释放方式：兜底清空
    // 兜底清空措施：执行语句结束时，即调用客户端Prepare、Reset、Free等操作STMT的资源API，重置该memCtx
    DbMemCtxArgsT args = {0};
    void *opCtx = DbCreateDynMemCtx(memCtx, false, "CLTSTMT_OP", &args);
    if (opCtx == NULL) {
        DbDynMemCtxFree(memCtx, stmt);  // 失败场景下内存指针没有传递到外面，并且会返回错误码，不用置空
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Alloc opctx when clt alloc stmt.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    // 如果有变量初始值是0，后续可不用再设置值
    (void)memset_s(stmt, sizeof(GmcStmtT), 0, sizeof(GmcStmtT));

    // 初始化基本属性
    stmt->conn = conn;
    stmt->memCtx = memCtx;
    stmt->opCtx = opCtx;

    InitStmtRecvAndSndBuffer(stmt, conn);

    // 初始化业务数据，建议非必要的字段使用时再初始化
    DbCreateList(&stmt->propSet, sizeof(CltPropertyT), memCtx);
    stmt->operateEdgeFlag = CLT_FLAG_AUTO_CREATE_IN_EDGE | CLT_FLAG_AUTO_CREATE_OUT_EDGE;
    stmt->isSetPreFetch = false;
    stmt->preFetchRows = CLT_DEFAULT_PRE_FETCH_ROW;
    stmt->isDataService = false;
    stmt->notDistribute = false;
#ifdef FEATURE_GQL
    stmt->modelType = MODEL_FASTPATH;
#endif  // FEATURE_GQL

    DbCreateList(&stmt->neighborsInfo, sizeof(CltNeighborInfoT), memCtx);

#ifdef FEATURE_HP_ANNSEQSCAN
    VectorSearchCtxInit(&stmt->vecSearchCtx);
#endif

    // 客户端部分系统视图是否设置关闭
    stmt->isCltStatisDisable = CltCheckStatisticSysviewIsDisable();

    // 初始化DS以及直连读&直连写句柄
    Status ret = CltAllocDirectAccessContent(stmt, conn);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(memCtx, stmt);  // 失败场景下内存指针没有传递到外面，并且会返回错误码，不用置空
        return ret;
    }

    // 添加到conn的stmt列表
    conn->childStmtCount++;
    DbSpinLock(&conn->lock);
    DbLinkedListAppend(&conn->childStmtList, &stmt->childStmtListNode);
    DbSpinUnlock(&conn->lock);

    *stmtx = stmt;
    return GMERR_OK;
}

Status AllocStmt(GmcConnT *conn, GmcStmtT **stmt)
{
    // memCtx用途：客户端STMT句柄管理结构体内存
    // 生命周期：连接级别
    // 释放方式：兜底清空
    // 兜底清空措施：销毁客户端句柄STMT时，销毁该memCtx
    DbMemCtxArgsT args = {0};
    args.liteModOn = true;
    DbMemCtxT *memCtx = DbCreateDynMemCtx(conn->memCtx, false, "CLTSTMT", &args);
    if (memCtx == NULL) {
        (void)DbAtomicInc(&conn->stmtAllocFailNums);
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Alloc memctx for the stmt");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    Status ret = CltAllocStmtWithMemCtx(memCtx, conn, stmt);
    if (ret != GMERR_OK) {
        (void)DbAtomicInc(&conn->stmtAllocFailNums);
        DbDeleteDynMemCtx(memCtx);
        return ret;
    }

    (void)DbAtomicInc(&conn->stmtAllocSuccNums);
    (void)DbAtomicInc64(&g_gmdbCltInstance.gStmtCount);
    DbCreateListWithExtendSize(&(*stmt)->leftValue, sizeof(DmValueT), DM_MAX_KEY_PROPE_NUM, (*stmt)->memCtx);
    DbCreateListWithExtendSize(&(*stmt)->rightValue, sizeof(DmValueT), DM_MAX_KEY_PROPE_NUM, (*stmt)->memCtx);
    DbCreateListWithExtendSize(
        &(*stmt)->orderByParams, sizeof(CltOrderByParamT), DM_MAX_KEY_PROPE_NUM, (*stmt)->memCtx);
    DbCreateListWithExtendSize(&(*stmt)->yangListKeyValue, sizeof(DmValueT), DM_MAX_KEY_PROPE_NUM, (*stmt)->memCtx);
    (*stmt)->magic = CLT_MAGIC_WORD;
    return ret;
}

void ReleaseStmtRes(GmcStmtT *stmt)
{
    // 释放直连写相关资源
    // 若上游函数由于异常情况调用FreeStmt()，这里stmtType也可能是CLT_STMT_TYPE_IDEL或其他类型
    CltResetStmt(stmt, true);  // 清掉已经打开的资源，如表元数据和句柄等
    DirectAccessRunCtxT *drRunCtxBase = &stmt->drRunCtx.base;
    if (drRunCtxBase->idxCtxMem != NULL) {
        IdxRelease(drRunCtxBase->idxCtxMem);
        drRunCtxBase->idxCtxMem = NULL;
    }
    if (drRunCtxBase->heapCtxMem != NULL) {
        ContainerReleaseRunCtx(drRunCtxBase->heapCtxMem);
        drRunCtxBase->heapCtxMem = NULL;
        drRunCtxBase->heapRunCtx = NULL;
    }
    if (drRunCtxBase->chCtxMem != NULL) {
        ContainerReleaseRunCtx(drRunCtxBase->chCtxMem);
        drRunCtxBase->chCtxMem = NULL;
        drRunCtxBase->chRunCtx = NULL;
    }

#ifdef FEATURE_HP_ANNSEQSCAN
    if (stmt->vecSearchCtx.prioQue != NULL) {
        DbDestroySimilPrioQue(stmt->vecSearchCtx.prioQue);
        stmt->vecSearchCtx.prioQue = NULL;
    }
#endif

    if (FixBufGetBuf(&stmt->recvPack) != NULL) {
        SecureFixBufRelease(&stmt->recvPack);  // 报文可能来自共享内存，需要释放
    }
    DbDeleteDynMemCtx(stmt->memCtx);  // 释放根memCtx清掉所有的内存占用
}

void FreeStmt(GmcStmtT *stmt)
{
    stmt->magic = CLT_MAGIC_WORD_AFTER_FREE;
    GmcConnT *conn = stmt->conn;
    ReleaseStmtId(stmt);

    // 如果assert失败，可能是double free等用户使用问题
    DB_ASSERT(conn->childStmtCount > 0);
    // 从conn的stmt列表中移除
    conn->childStmtCount--;
    (void)DbAtomicDec64(&g_gmdbCltInstance.gStmtCount);
    DbSpinLock(&conn->lock);
    DbLinkedListRemove(&stmt->childStmtListNode);

#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
    if (stmt->stmtType == CLT_STMT_TYPE_TS && stmt->tsInfo != NULL &&
        stmt->tsInfo->stmtStatus == TS_STMT_STATUS_FETCH) {
        // 处理query结果没fetch完全时，客户端断联的情况
        CltOperQlT *op = CltGetOperationContext(stmt);
        if (op->tempFile) {
            SeBufFileClose(op->tempFile);
            op->tempFile = NULL;
        }
    }
#endif
    // 通过notify机制，通知server释放通信共享内存
#if defined(RTOSV2) || defined(RTOSV2X) || defined(HPE)
    if (IsUseListCata() && conn->childStmtCount == 0 && conn->requestCtxMap.size == 0) {
        MsgBufferFreeListFreeAll(conn);
        SharedMsgPoolResetRequest(&conn->msgPoolCtx);
    }
#endif
    DbSpinUnlock(&conn->lock);
    ReleaseStmtRes(stmt);
}

static void CltGetAttrAutoCreateEdge(const GmcStmtT *stmt, uint32_t *value)
{
    bool in = ((stmt->operateEdgeFlag & CLT_FLAG_AUTO_CREATE_IN_EDGE) != 0);
    bool out = ((stmt->operateEdgeFlag & CLT_FLAG_AUTO_CREATE_OUT_EDGE) != 0);
    if (in && out) {
        *value = GMC_EDGE_DIRECTION_BOTH;
    } else if (in) {
        *value = GMC_EDGE_DIRECTION_IN;
    } else if (out) {
        *value = GMC_EDGE_DIRECTION_OUT;
    } else {
        *value = GMC_EDGE_DIRECTION_NONE;
    }
}

#define CLT_MAX_PRE_FETCH_ROW 2048

static Status CltSetAttrAutoCreateEdge(GmcStmtT *stmt, const uint32_t *value)
{
    switch (*value) {
        case GMC_EDGE_DIRECTION_NONE:
            stmt->operateEdgeFlag &= (uint8_t)~CLT_FLAG_AUTO_CREATE_IN_EDGE;
            stmt->operateEdgeFlag &= (uint8_t)~CLT_FLAG_AUTO_CREATE_OUT_EDGE;
            break;
        case GMC_EDGE_DIRECTION_IN:
            stmt->operateEdgeFlag |= CLT_FLAG_AUTO_CREATE_IN_EDGE;
            stmt->operateEdgeFlag &= (uint8_t)~CLT_FLAG_AUTO_CREATE_OUT_EDGE;
            break;
        case GMC_EDGE_DIRECTION_OUT:
            stmt->operateEdgeFlag |= CLT_FLAG_AUTO_CREATE_OUT_EDGE;
            stmt->operateEdgeFlag &= (uint8_t)~CLT_FLAG_AUTO_CREATE_IN_EDGE;
            break;
        case GMC_EDGE_DIRECTION_BOTH:
            stmt->operateEdgeFlag |= CLT_FLAG_AUTO_CREATE_IN_EDGE;
            stmt->operateEdgeFlag |= CLT_FLAG_AUTO_CREATE_OUT_EDGE;
            break;
        default:
            DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "stmt attr");
            return GMERR_INVALID_PARAMETER_VALUE;
    }

    return GMERR_OK;
}

static void CltSetAttrAutoDeleteEdge(GmcStmtT *stmt, const bool *value)
{
    if (*value) {
        stmt->operateEdgeFlag |= CLT_FLAG_AUTO_DEL_EDGE;
    } else {
        stmt->operateEdgeFlag &= (uint8_t)~CLT_FLAG_AUTO_DEL_EDGE;
    }
}

static Status CltSetAttrPreFetchRows(GmcStmtT *stmt, const uint32_t *value)
{
    uint32_t preFetchRows = *value;
    if ((preFetchRows - 1) < CLT_MAX_PRE_FETCH_ROW) {
        stmt->isSetPreFetch = true;
        stmt->preFetchRows = (uint16_t)preFetchRows;
        return GMERR_OK;
    }

    DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "Prefetch row.");
    return GMERR_INVALID_PARAMETER_VALUE;
}

static bool IsModelTypeValid(const ModelTypeE type)
{
    switch (type) {
        case MODEL_DUMMY:
        case MODEL_FASTPATH:
        case MODEL_DATALOG:
        case MODEL_PUBLIC:
        case MODEL_SQL:
        case MODEL_TS:
#ifdef FEATURE_GQL
        case MODEL_GQL:
#endif
        case MODEL_STREAM:
        case MODEL_BUTT: {
            // 已对外提供的模型类型
            return true;
        }
        default:
            return false;
    }
}

static Status CltSetAttrModelType(GmcStmtT *stmt, const uint16_t *value)
{
    if (!IsModelTypeValid(*value)) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    stmt->modelType = *(const uint16_t *)value;
#ifdef FEATURE_GQL
    if (stmt->modelType == MODEL_GQL) {
        stmt->isDataService = true;
    }
#endif
    return GMERR_OK;
}

Status CltSetStmtAttr(GmcStmtT *stmt, GmcStmtAttrTypeE attr, const void *value)
{
    Status ret = GMERR_OK;
    switch (SWITCH_LIKELY(attr, GMC_STMT_ATTR_USE_NEW_DESERI)) {
        case GMC_STMT_ATTR_USE_NEW_DESERI:
            stmt->useNewDeseri = *(const bool *)value;
            break;
        case GMC_STMT_ATTR_AUTO_DELETE_EDGE:
            CltSetAttrAutoDeleteEdge(stmt, (const bool *)value);
            break;
        case GMC_STMT_ATTR_AUTO_CREATE_EDGE:
            ret = CltSetAttrAutoCreateEdge(stmt, value);
            break;
        case GMC_STMT_ATTR_PRE_FETCH_ROWS:
            ret = CltSetAttrPreFetchRows(stmt, value);
            break;
        case GMC_STMT_ATTR_DTL_ERROR_CODE:
            stmt->dtlErrorCode = *(const int64_t *)value;
            break;
        case GMC_STMT_ATTR_NOT_DISTRIBUTE:
            stmt->notDistribute = *(const bool *)value;
            break;
#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
        case GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE:
            ret = CltSetAttrTsRowArraySize(stmt, value);
            break;
        case GMC_STMT_ATTR_TS_BIND_BY_COL:
            ret = CltSetAttrTsBindByCol(stmt, value);
            break;
        case GMC_STMT_ATTR_TS_BIND_BY_ROW:
            ret = CltSetAttrTsBindByRow(stmt, value);
            break;
#endif
        case GMC_STMT_ATTR_MODEL_TYPE:
            ret = CltSetAttrModelType(stmt, value);
            break;
        default:
            DB_ASSERT(false);
            break;
    }
    return ret;
}

Status CltGetAttrDtlErrorLabelName(const GmcStmtT *stmt, void *value, uint32_t valueSize)
{
    if (stmt->dtlErrorLabelName == NULL) {
        *(char *)value = '\0';
        return GMERR_OK;
    }
    if (strncpy_s((char *)value, valueSize, stmt->dtlErrorLabelName, valueSize - 1) != EOK) {
        DB_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "copy dtl blunder label name");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status CltGetStmtAttr(const GmcStmtT *stmt, GmcStmtAttrTypeE attr, void *value, uint32_t valueSize)
{
    // 性能路径，GMC_STMT_ATTR_AFFECTED_ROWS为常用类型直接用if判断处理
    if (attr == GMC_STMT_ATTR_AFFECTED_ROWS) {
        *(uint32_t *)value = stmt->affectRows;
        return GMERR_OK;
    }
    switch (attr) {
        case GMC_STMT_ATTR_AFFECTED_ROWS:
            *(uint32_t *)value = stmt->affectRows;
            break;
        case GMC_STMT_ATTR_AUTO_DELETE_EDGE:
            *(bool *)value = ((stmt->operateEdgeFlag & CLT_FLAG_AUTO_DEL_EDGE) != 0);
            break;
        case GMC_STMT_ATTR_AUTO_CREATE_EDGE:
            CltGetAttrAutoCreateEdge(stmt, value);
            break;
        case GMC_STMT_ATTR_CACHE_ROWS:
            *(uint32_t *)value = stmt->cacheRows;
            break;
        case GMC_STMT_ATTR_PRE_FETCH_ROWS:
            *(uint32_t *)value = stmt->preFetchRows;
            break;
        case GMC_STMT_ATTR_DTL_ERROR_CODE:
            *(int64_t *)value = stmt->dtlErrorCode;
            break;
        case GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME:
            return CltGetAttrDtlErrorLabelName(stmt, value, valueSize);
        case GMC_STMT_ATTR_NOT_DISTRIBUTE:
            *(bool *)value = stmt->notDistribute;
            break;
        case GMC_STMT_ATTR_RESULT_COLS:
            *(uint32_t *)value = stmt->columnCount;
            break;
        case GMC_STMT_ATTR_RESULT_ROWS:
            *(uint32_t *)value = stmt->queryResRows;
            break;
        // not support
        case GMC_STMT_ATTR_BUTT:
        default:
            DB_ASSERT(false);
            break;
    }
    return GMERR_OK;
}

void ReleaseStmtSubtreeLabelList(GmcStmtT *stmt)
{
    DB_POINTER(stmt);
    DbListT *labelDescs = &stmt->labelDescs;
    uint32_t count = DbListGetItemCnt(labelDescs);
    CltTreeDescT **items = DbListGetItems(labelDescs);
    for (uint32_t i = 0; i < count; i++) {
        CltCataCloseVertexLabel(items[i]->cltCataLabel);
    }
    DbDestroyList(labelDescs);
}

#if defined(FEATURE_TS) || defined(TS_MULTI_INST)

inline static void ClearBulkInsertMetaData(GmcStmtT *stmt)
{
    if (stmt->tsInfo->bulkInsertInfo.memCtx != NULL) {
        DbDeleteDynMemCtx(stmt->tsInfo->bulkInsertInfo.memCtx);
        stmt->tsInfo->bulkInsertInfo.memCtx = NULL;
    }
}

inline static void ClearBulkInsertTempData(GmcStmtT *stmt)
{
    if (stmt->tsInfo->bulkInsertInfo.tempMemCtx != NULL) {
        DbDeleteDynMemCtx(stmt->tsInfo->bulkInsertInfo.tempMemCtx);
        stmt->tsInfo->bulkInsertInfo.tempMemCtx = NULL;
    }
}

static void ResetStmtTsInfoAndOpCtx(GmcStmtT *stmt)
{
    CltOperQlT *op = CltGetOperationContext(stmt);
    if (stmt->tsInfo->stmtStatus != TS_STMT_STATUS_BEGIN && !stmt->tsInfo->isLabelPrepared && op != NULL &&
        op->tempFile) {
        SeBufFileClose(op->tempFile);
        op->tempFile = NULL;
    }
    if (stmt->tsInfo->stmtStatus == TS_STMT_STATUS_FETCH || stmt->tsInfo->stmtStatus == TS_STMT_STATUS_GET_PROP ||
        stmt->tsInfo->stmtStatus == TS_STMT_STATUS_BULKOP || stmt->tsInfo->stmtStatus == TS_STMT_STATUS_PREPARE_SQL) {
        DbClearList(&stmt->tsInfo->bindColStmts);
        stmt->tsInfo->stmtStatus = TS_STMT_STATUS_INIT;
        stmt->tsInfo->isDataClipped = false;
        ClearBulkInsertTempData(stmt);
        ClearBulkInsertMetaData(stmt);
        ClearParamQueryData(stmt);
        (void)memset_s(&stmt->tsInfo->bulkInsertInfo, sizeof(TsBulkInsertStmtT), 0, sizeof(TsBulkInsertStmtT));
    }
    if (stmt->tsInfo->stmtStatus == TS_STMT_STATUS_EXEC_DIR) {
        ClearParamQueryData(stmt);
    }
}
#endif

static void ResetStmtWithoutOperHandle(GmcStmtT *stmt)
{
    // 使用新的反序列化解析模式会在此修改为默认模式
    stmt->useNewDeseri = false;
    stmt->isFullyParsed = false;
    stmt->vertexBufferInFixBuffer.str = NULL;
    stmt->vertexBufferInFixBuffer.len = 0;

    stmt->affectRows = 0;
    stmt->cacheRows = 0;
    stmt->queryResRows = 0;
    stmt->columnCount = 0;
    stmt->limitCount = 0;
    stmt->dtlErrorCode = 0;
    if (stmt->dtlErrorLabelName != NULL) {
        stmt->dtlErrorLabelName[0] = '\0';
    }
    stmt->isExecuted = false;
    stmt->yangVertexTmpId = DB_INVALID_UINT32;

    // do not set the stmtId to invalid, for reusing the stmt of server: stmt->stmtId = DB_INVALID_ID32

    stmt->fetchEof = false;
    stmt->preFetchRows = CLT_DEFAULT_PRE_FETCH_ROW;

    stmt->lastOpStatus.lastOpType = GMC_OPERATION_BUTT;
    stmt->lastOpStatus.lastOpDirectWrite = false;
    stmt->lastOpStatus.lastOpWriteCache = false;
    stmt->lastOpStatus.noNeedResetVertex = false;
    DwResetResInfo(stmt);
#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
    if (stmt->stmtType == CLT_STMT_TYPE_TS && stmt->tsInfo != NULL) {
        ResetStmtTsInfoAndOpCtx(stmt);
    }
#endif
    DbClearList(&stmt->propSet);
    if (SECUREC_UNLIKELY(stmt->keyValuesBuf != NULL)) {
        DmDestroyIndexKey(stmt->keyValuesBuf);
        stmt->keyValuesBuf = NULL;
    }
    DbClearList(&stmt->neighborsInfo);
    ReleaseStmtSubtreeLabelList(stmt);
    stmt->isStructure = false;
    // Release DS handle
    if (SECUREC_UNLIKELY(stmt->operationType == GMC_OPERATION_SCAN && !stmt->eof)) {
        DirectReadCtxClose(&stmt->drRunCtx, DIRECT_ACCESS_CLOSE_HEAP_CURSOR);
    }
    stmt->eof = false;
    // reset op memory contex
    if (!stmt->opCtx->isReset) {  // 性能优化，减少加锁耗时
        DbMemCtxReset(stmt->opCtx);
    }
    FixBufResetMem(&stmt->recvPack);
#ifdef FEATURE_HP_ANNSEQSCAN
    VectorSearchCtxReset(&stmt->vecSearchCtx);
#endif
}

void CltResetStmt(GmcStmtT *stmt, bool isClearVertexLabel)
{
    ClearOpData(stmt, isClearVertexLabel);

    ResetStmtWithoutOperHandle(stmt);
    stmt->stmtType = CLT_STMT_TYPE_IDLE;
}

// 只清理vertex，不清理vertexlabel
static void CltClearStmtOperVertexNoLabel(GmcStmtT *stmt)
{
    DB_ASSERT(stmt->stmtType == CLT_STMT_TYPE_VERTEX);

    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    DmVertexT *vertex = operVertex->vertex;
    if (stmt->operationType == GMC_OPERATION_UPDATE || stmt->operationType == GMC_OPERATION_MERGE ||
        stmt->operationType == GMC_OPERATION_NONE) {
        DmClearVertex(vertex);
        vertex->isDeltaVertex = true;
    } else {
        if (stmt->operationType != GMC_OPERATION_SCAN) {
            (void)DmResetVertex(vertex);
        }
    }

    // 大多数下属内存会在ResetStmtWithoutOperHandle通过Reset其opCtx统一清理，故不会产生内存泄漏
    CltInitStmtOperVertex(stmt, vertex, false);
}

void CltResetStmtNew(GmcStmtT *stmt)
{
    GmcConnT *conn = stmt->conn;
    FixBufferT *buffer = &stmt->recvPack;
    ShrinkMsgBuffer(conn, buffer);
    if (SECUREC_LIKELY(stmt->stmtType == CLT_STMT_TYPE_VERTEX)) {
        CltClearStmtOperVertexNoLabel(stmt);
        ResetStmtWithoutOperHandle(stmt);
    } else {
        // 对于其他操作走原先的逻辑，将stmtType还原为IDLE
        CltResetStmt(stmt, true);
    }
}

static void CltClearOpQuickForScan(GmcStmtT *stmt)
{
    CltOperVertexT *op = CltGetOperationContext(stmt);
    *op = (CltOperVertexT){.vertex = op->vertex,
        .cltCataLabel = op->cltCataLabel,
        .doDeseri = true,
        .orderByParams = op->orderByParams,
        .idx = op->idx};  // idx需要做缓存，不能每次都释放
    OpClear(op);
}

static void CltClearStmtQuickForScan(GmcStmtT *stmt)
{
    // 使用新的反序列化解析模式会在此修改为默认模式
    stmt->useNewDeseri = false;
    stmt->isFullyParsed = false;
    stmt->vertexBufferInFixBuffer.str = NULL;
    stmt->vertexBufferInFixBuffer.len = 0;

    if (SECUREC_UNLIKELY(!stmt->eof)) {  // 说明业务没有完整的取到所有的数据，没有机会close，所以在这里close一下
        // Release DS handle
        DirectReadCtxClose(&stmt->drRunCtx, DIRECT_ACCESS_CLOSE_HEAP_CURSOR);
    }

    stmt->affectRows = 0;
    stmt->cacheRows = 0;
    stmt->columnCount = 0;
    stmt->limitCount = 0;
    stmt->dtlErrorCode = 0;
    if (stmt->dtlErrorLabelName != NULL) {
        stmt->dtlErrorLabelName[0] = '\0';
    }
    stmt->isExecuted = false;
    // do not set the stmtId to invalid, for reusing the stmt of server: stmt->stmtId = DB_INVALID_ID32
    stmt->eof = false;
    stmt->fetchEof = false;
    stmt->isStructure = false;
    stmt->yangVertexTmpId = DB_INVALID_UINT32;

    stmt->lastOpStatus.lastOpType = GMC_OPERATION_BUTT;
    stmt->lastOpStatus.lastOpDirectWrite = false;
    stmt->lastOpStatus.lastOpWriteCache = false;
    stmt->lastOpStatus.noNeedResetVertex = false;
    DwResetResInfo(stmt);

    DbClearList(&stmt->propSet);
    DbClearList(&stmt->neighborsInfo);
    GmcConnT *conn = stmt->conn;
    FixBufferT *buffer = &stmt->recvPack;
    if (SECUREC_UNLIKELY(FixBufGetTotalLength(buffer) > conn->packShrinkThreshold)) {
        SecureFixBufRelease(buffer);
    } else {
        FixBufResetMem(buffer);
    }
    if (SECUREC_UNLIKELY(stmt->keyValuesBuf != NULL)) {
        DmDestroyIndexKey(stmt->keyValuesBuf);
        stmt->keyValuesBuf = NULL;
    }
    // reset op memory contex
    if (!stmt->opCtx->isReset) {  // 性能优化，减少加锁耗时
        DbMemCtxReset(stmt->opCtx);
    }
#ifdef FEATURE_HP_ANNSEQSCAN
    VectorSearchCtxReset(&stmt->vecSearchCtx);
#endif
}

Status CltPrivCheckForScan(CltOperVertexT *cltVertex, DrRunCtxT *drRunCtx, DmReusableMetaT *metaInfoAddr)
{
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    DmVertexLabelT *vertexLabel = cltVertex->cltCataLabel->vertexLabel;

    Status ret = GMERR_OK;
    if (SECUREC_LIKELY(drRunCtxBase->lastPrivPass)) {  // 上一次检查通过了，本次检查只看权限有没有变化
        ret = VertexPrivCheck(drRunCtx, cltVertex->cltCataLabel);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            drRunCtxBase->lastPrivPass = false;
            drRunCtxBase->passPrivType = CLI_INVALID_PRIV;
            DB_LOG_ERROR(ret, "check privilege.");
            return ret;
        }
    } else {
        if (metaInfoAddr->vertexLabelId != vertexLabel->metaCommon.metaId) {
            DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_TABLE, "dropped.");
            return GMERR_UNDEFINED_TABLE;
        }
        ret = DirectAccessPrivCheck(
            &drRunCtx->base, &metaInfoAddr->objPrivilege, CATA_VERTEX_LABEL, SELECTANY_PRIV, DM_OBJ_SELECT_PRIV);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "check privilege");
            return ret;
        }
        drRunCtxBase->lastPrivPass = true;
    }
    drRunCtxBase->nspPrivVersion = drRunCtxBase->objPrivNsp->objPrivVersion;
    drRunCtxBase->sysPrivVersion = drRunCtxBase->role->sysPrivVersion;
    drRunCtxBase->hasInited = true;
    drRunCtxBase->isUseClusteredHashTable = vertexLabel->metaVertexLabel->containerType == CONTAINER_CLUSTERED_HASH;
    drRunCtxBase->containerType = vertexLabel->metaVertexLabel->containerType;
#ifdef ART_CONTAINER
    drRunCtxBase->isRealCluster = vertexLabel->commonInfo->isArtRealCluster;
#endif
    return GMERR_OK;
}

CLT_ALWAYS_INLINE static Status CltRsesetDrtStmtForQuickForScan(GmcStmtT *stmt)
{
    DrRunCtxT *drRunCtx = &stmt->drRunCtx;
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
    VertexLabelCommonInfoT *commonInfo = (VertexLabelCommonInfoT *)(cltVertex->cltCataLabel->vertexLabel->commonInfo);
    DmReusableMetaT *metaInfoAddr = (DmReusableMetaT *)(cltVertex->cltCataLabel->vertexMetaInfo);
    if (SECUREC_UNLIKELY(metaInfoAddr == NULL)) {
        // because transfer shm worthless.
        DB_SET_LASTERR(GMERR_INTERNAL_ERROR, "get metaInfoAddr");
        return GMERR_INTERNAL_ERROR;
    }
    if (SECUREC_UNLIKELY(drRunCtxBase->objPrivNsp == NULL)) {
        drRunCtxBase->objPrivNsp = DbShmPtrToAddr(commonInfo->nspObjPrivShmPtr);
        if (drRunCtxBase->objPrivNsp == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR,
                "get object namespace privilege, segId: %" PRIu32 ", offset: %" PRIu32,
                commonInfo->nspObjPrivShmPtr.segId, commonInfo->nspObjPrivShmPtr.offset);
            return GMERR_INTERNAL_ERROR;
        }
    }
    // 直连读权限检查
    Status ret = CltPrivCheckForScan(cltVertex, drRunCtx, metaInfoAddr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return GMERR_OK;
}

Status CltResetStmtQuickForScan(GmcStmtT *stmt)
{
    DB_ASSERT(stmt->stmtType == CLT_STMT_TYPE_VERTEX);
    CltClearOpQuickForScan(stmt);
    CltClearStmtQuickForScan(stmt);
    Status ret;
#if defined(EXPERIMENTAL_NERGC)
    if (!DbIsTcp()) {
        ret = CltRsesetDrtStmtForQuickForScan(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
#else
    ret = CltRsesetDrtStmtForQuickForScan(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
#endif
    return GMERR_OK;
}

static void CltClearDrStmtQuickForScan(GmcDirectReadStmtT *stmt)
{
    FixBufResetMem(stmt->recvPack);
    stmt->cacheRows = 0;
}

static void CltClearDrStmtOpQuickForScan(GmcDirectReadStmtT *stmt)
{
    CltDrOperVertexT *op = CltGetDrStmtOperationContext(stmt);
    *op = (CltDrOperVertexT){.vertex = op->vertex,
        .cltCataLabel = op->cltCataLabel,
        .doDeseri = true,
        .idx = op->idx};  // idx需要做缓存，不能每次都释放
}

Status CltResetDrStmtQuickForScan(GmcDirectReadStmtT *stmt)
{
    DB_ASSERT(stmt->stmtType == CLT_STMT_TYPE_VERTEX);
    CltClearDrStmtOpQuickForScan(stmt);
    CltClearDrStmtQuickForScan(stmt);
    CltDrOperVertexT *cltVertex = CltGetDrStmtOperationContext(stmt);
    DmReusableMetaT *metaInfoAddr = (DmReusableMetaT *)(cltVertex->cltCataLabel->vertexMetaInfo);
    if (SECUREC_UNLIKELY(metaInfoAddr == NULL)) {
        // because transfer shm worthless
        DB_SET_LASTERR(GMERR_INTERNAL_ERROR, "get metaInfoAddr.");
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

static Status ProcessAlarmDetailByType(DbAlarmDataT *rstAlmData, GmcAlarmDataT *alarmData)
{
    uint32_t alarmId = (uint32_t)alarmData->alarmId;
    const char *alarmTypeStr[] = {"block memory", "async_msg_pool", "sub_msg_pool", "connection", "object",
        "table space", "sub_msg", "heap memory", "hung worker", "table space used info", "rsm table space used info",
        "storage_space", "storage_file"};
    const char *srcType = (rstAlmData->srcType == DB_ALARM_SERVER) ? "server" : "client";

    uint64_t succTimes = rstAlmData->succTimes;
    uint64_t failTimes = rstAlmData->failTimes;
    int count;
    if (rstAlmData->status == DB_ALARM_EMPTY && rstAlmData->alarmStatus == DB_ALARM_STATUS) {
        float alarmVal = (float)rstAlmData->activeValue;  // 0~1
        count = snprintf_truncated_s(alarmData->detail, sizeof(alarmData->detail),
            "Alarm of %s is active, from %s, value=%.2f, "
            "slient corruption statis succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".",
            alarmTypeStr[alarmId], srcType, alarmVal, succTimes, failTimes);
    } else if (rstAlmData->status == DB_ALARM_EMPTY) {
        count = snprintf_truncated_s(alarmData->detail, sizeof(alarmData->detail),
            "Slient corruption statis of %s succTimes=%" PRIu64 ", failTimes=%" PRIu64 ".", alarmTypeStr[alarmId],
            succTimes, failTimes);
    } else if (rstAlmData->status == DB_ALARM_CLEARED) {
        count = snprintf_truncated_s(alarmData->detail, sizeof(alarmData->detail),
            "Alarm of %s is cleared, from %s, value=%.2f, slient corruption statis succTimes=%" PRIu64
            ", failTimes=%" PRIu64 ".",
            alarmTypeStr[alarmId], srcType, rstAlmData->clearedValue, succTimes, failTimes);
    } else if (rstAlmData->status == DB_ALARM_ACTIVE) {
        count = snprintf_truncated_s(alarmData->detail, sizeof(alarmData->detail),
            "Alarm of %s is active, from %s, value=%.2f, slient corruption statis succTimes=%" PRIu64
            ", failTimes=%" PRIu64 ".",
            alarmTypeStr[alarmId], srcType, rstAlmData->activeValue, succTimes, failTimes);
    } else if (rstAlmData->status == DB_ALARM_ACTIVE_CLEARED) {
        // 转换为float可以保证整数部分的值在一定范围内（39位十进制），从而保证即便值异常，输出也不会被截断
        float alarmVal = (float)rstAlmData->alarmDetail.alarmVal;  // 0~1
        alarmData->activeValue = alarmVal;
        count = snprintf_truncated_s(alarmData->detail, sizeof(alarmData->detail),
            "Alarm of %s, from %s is active_cleared with alarm_value=%.2f, succTimes=%" PRIu64 ", failTimes=%" PRIu64
            ".",
            alarmTypeStr[alarmId], srcType, alarmVal, rstAlmData->alarmDetail.succTimes,
            rstAlmData->alarmDetail.failTimes);
    } else {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "set alarm detail because scene is wrong.");
        return GMERR_DATA_EXCEPTION;
    }
    if (count <= 0) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "set alarm detail");
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

Status ProcessAlarmDataResp(FixBufferT *fixBuffer, void *out)
{
    GmcAlarmDataT *alarmData = (GmcAlarmDataT *)out;
    TextT tmpText;
    Status ret = FixBufGetObject(fixBuffer, &tmpText);
    if (ret != GMERR_OK || tmpText.len != sizeof(DbAlarmDataT)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,  //
            "get tmpText when ProcessAlarmDataResp.");
        return GMERR_DATA_EXCEPTION;
    }

    DbAlarmDataT rstAlmData;
    errno_t err = memcpy_s(&rstAlmData, sizeof(rstAlmData), tmpText.str, tmpText.len);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "copy data to rst alarm data");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    alarmData->srcType = (GmcAlarmSrcTypeE)rstAlmData.srcType;
    alarmData->status = (GmcAlarmStatusE)rstAlmData.status;
    alarmData->alarmStatus = (GmcSimpleStatusE)rstAlmData.alarmStatus;
    alarmData->activeValue = rstAlmData.activeValue;
    alarmData->clearedValue = rstAlmData.clearedValue;
    alarmData->succTimes = rstAlmData.succTimes;
    alarmData->failTimes = rstAlmData.failTimes;
    alarmData->activeThreshold = rstAlmData.activeThreshold;
    alarmData->clearedThreshold = rstAlmData.clearedThreshold;
    // 给gmsysview alarm使用的非全局alarm的location信息
    alarmData->userDefRscId = (uint64_t)rstAlmData.userDefRscId;
    // 数据校验
    if (alarmData->srcType >= GMC_ALARM_SRC_TYPE_BUTT) {
        DB_LOG_AND_SET_LASERR(
            GMERR_DATA_EXCEPTION, "the val of srcType, type is %" PRIu32 ".", (uint32_t)alarmData->srcType);
        return GMERR_DATA_EXCEPTION;
    }
    if (alarmData->status >= GMC_ALARM_STATUS_BUTT) {
        DB_LOG_AND_SET_LASERR(
            GMERR_DATA_EXCEPTION, "the val of status, status is %" PRIu32 ".", (uint32_t)alarmData->status);
        return GMERR_DATA_EXCEPTION;
    }
    if (alarmData->alarmStatus >= GMC_ALARM_SIMPLE_STATUS_BUTT) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "the val of alarmStatus, alarmStatus is %" PRIu32 ".",
            (uint32_t)alarmData->alarmStatus);
        return GMERR_DATA_EXCEPTION;
    }

    return ProcessAlarmDetailByType(&rstAlmData, alarmData);
}

void UpdateStmtObjMemCtxStatistics(GmcStmtT *stmt)
{
    if (stmt->stmtDynMem.bigObjDynMemCount == DB_MAX_UINT32 || stmt->stmtDynMem.normalObjDynMemCount == DB_MAX_UINT32) {
        DB_LOG_WARN(GMERR_PROGRAM_LIMIT_EXCEEDED,
            "conn id is %" PRIu16 ", stmt id is %" PRIu16 ", stmt obj statistics: big obj max size is %" PRIu64
            " bytes,  big obj avg size is %" PRIu64 " bytes, stmt big obj cnt is %" PRIu32
            ". normal obj max size is %" PRIu64 " bytes, normal obj avg size is %" PRIu64
            ", stmt normal obj cnt is %" PRIu32 ".",
            stmt->conn->remoteId, stmt->stmtId, stmt->stmtDynMem.bigObjMaxDynMem, stmt->stmtDynMem.bigObjAvgDynMem,
            stmt->stmtDynMem.bigObjDynMemCount, stmt->stmtDynMem.normalObjMaxDynMem,
            stmt->stmtDynMem.normalObjAvgDynMem, stmt->stmtDynMem.normalObjDynMemCount);
        stmt->stmtDynMem.bigObjMaxDynMem = 0;
        stmt->stmtDynMem.bigObjAvgDynMem = 0;
        stmt->stmtDynMem.bigObjDynMemCount = 0;
        stmt->stmtDynMem.normalObjMaxDynMem = 0;
        stmt->stmtDynMem.normalObjAvgDynMem = 0;
        stmt->stmtDynMem.normalObjDynMemCount = 0;
    }
    uint64_t memSize = DbMemCtxGetPhySizePeakOnTree(stmt->memCtx);
    if (memSize > stmt->conn->bigObjectStand) {
        stmt->stmtDynMem.bigObjDynMemCount++;
        stmt->stmtDynMem.bigObjMaxDynMem =
            (memSize > stmt->stmtDynMem.bigObjMaxDynMem) ? memSize : stmt->stmtDynMem.bigObjMaxDynMem;
        stmt->stmtDynMem.bigObjAvgDynMem =
            ((stmt->stmtDynMem.bigObjDynMemCount - 1) * stmt->stmtDynMem.bigObjAvgDynMem + memSize) /
            stmt->stmtDynMem.bigObjDynMemCount;
    } else {
        stmt->stmtDynMem.normalObjDynMemCount++;
        stmt->stmtDynMem.normalObjMaxDynMem =
            (memSize > stmt->stmtDynMem.normalObjMaxDynMem) ? memSize : stmt->stmtDynMem.normalObjMaxDynMem;
        stmt->stmtDynMem.normalObjAvgDynMem =
            ((stmt->stmtDynMem.normalObjDynMemCount - 1) * stmt->stmtDynMem.normalObjAvgDynMem + memSize) /
            stmt->stmtDynMem.normalObjDynMemCount;
    }
    DbMemCtxResetPhySizePeakOnTree(stmt->memCtx);
}

void UpdateConnObjMemCtxStatistics(GmcConnT *conn)
{
    uint64_t memSize = 0;
    if (conn->cltDynMem.bigObjDynMemCount == DB_MAX_UINT32 || conn->cltDynMem.normalObjDynMemCount == DB_MAX_UINT32) {
        DB_LOG_WARN(GMERR_PROGRAM_LIMIT_EXCEEDED,
            "conn id is %" PRIu16 ", conn obj statistics: big obj max size is %" PRIu64
            " bytes,  big obj avg size is %" PRIu64 " bytes, conn big obj cnt is %" PRIu32
            ". normal obj max size is %" PRIu64 " bytes, normal obj avg size is %" PRIu64
            ", conn normal obj cnt is %" PRIu32 ".",
            conn->remoteId, conn->cltDynMem.bigObjMaxDynMem, conn->cltDynMem.bigObjAvgDynMem,
            conn->cltDynMem.bigObjDynMemCount, conn->cltDynMem.normalObjMaxDynMem, conn->cltDynMem.normalObjAvgDynMem,
            conn->cltDynMem.normalObjDynMemCount);
        conn->cltDynMem.bigObjMaxDynMem = 0;
        conn->cltDynMem.bigObjAvgDynMem = 0;
        conn->cltDynMem.bigObjDynMemCount = 0;
        conn->cltDynMem.normalObjMaxDynMem = 0;
        conn->cltDynMem.normalObjAvgDynMem = 0;
        conn->cltDynMem.normalObjDynMemCount = 0;
    }
    // 此为DFX函数，若获取memCtx的INFO信息出错，不应影响正常的业务执行，记录0即可。内部出错一般为加锁失败。
    (void)DbMemCtxGetPhySizePeak(conn->memCtx, &memSize);
    if (memSize > conn->bigObjectStand) {
        conn->cltDynMem.bigObjDynMemCount++;
        conn->cltDynMem.bigObjMaxDynMem =
            (memSize > conn->cltDynMem.bigObjMaxDynMem) ? memSize : conn->cltDynMem.bigObjMaxDynMem;
        conn->cltDynMem.bigObjAvgDynMem =
            ((conn->cltDynMem.bigObjDynMemCount - 1) * conn->cltDynMem.bigObjAvgDynMem + memSize) /
            conn->cltDynMem.bigObjDynMemCount;
    } else {
        conn->cltDynMem.normalObjDynMemCount++;
        conn->cltDynMem.normalObjMaxDynMem =
            (memSize > conn->cltDynMem.normalObjMaxDynMem) ? memSize : conn->cltDynMem.normalObjMaxDynMem;
        conn->cltDynMem.normalObjAvgDynMem =
            ((conn->cltDynMem.normalObjDynMemCount - 1) * conn->cltDynMem.normalObjAvgDynMem + memSize) /
            conn->cltDynMem.normalObjDynMemCount;
    }
    (void)DbMemCtxResetPhySizePeak(conn->memCtx);
}

/*
 * 订阅连接需要在TearDownEvents后再将挂载的stmt释放
 */
void RemoveStmtFromConn(GmcConnT *conn, bool ignoreSubConn)
{
    DbSpinLock(&conn->lock);
    TagLinkedListT *dummy = &conn->childStmtList;
    TagLinkedListT *nextNode = dummy->next;
    for (TagLinkedListT *node = dummy; (node = nextNode) != dummy;) {
        nextNode = node->next;
        GmcStmtT *stmt = LIST_ENTRY(node, GmcStmtT, childStmtListNode);
        DB_ASSERT(stmt != NULL);
        GmcConnTypeE connType = stmt->conn->connType;
        if (ignoreSubConn && connType != GMC_CONN_TYPE_SYNC && connType != GMC_CONN_TYPE_ASYNC) {
            continue;
        }
        stmt->magic = CLT_MAGIC_WORD_AFTER_FREE;
        ReleaseStmtId(stmt);

        // 如果assert失败，可能是double free等用户使用问题
        DB_ASSERT(conn->childStmtCount > 0);
        // 从conn的stmt列表中移除
        conn->childStmtCount--;
        (void)DbAtomicDec64(&g_gmdbCltInstance.gStmtCount);
        DbLinkedListRemove(&stmt->childStmtListNode);
        ReleaseStmtRes(stmt);
    }
    DbSpinUnlock(&conn->lock);
}

inline DmVertexT *CltGetVertexInStmt(const GmcStmtT *stmt)
{
    bool ok = (stmt->stmtType == CLT_STMT_TYPE_VERTEX || stmt->stmtType == CLT_STMT_TYPE_EDGE_TOPO ||
               stmt->stmtType == CLT_STMT_TYPE_SUB_VERTEX || stmt->stmtType == CLT_STMT_TYPE_SUB_PATH);
    if (SECUREC_UNLIKELY(!ok)) {
        return NULL;
    }
    const CltOperVertexT *operVertex = CltGetConstOperationContext(stmt);
    return operVertex->vertex;
}

inline DmVertexT *CltGetVertexInStmt4DirectRead(const GmcDirectReadStmtT *stmt)
{
    if (SECUREC_UNLIKELY(stmt->stmtType != CLT_STMT_TYPE_VERTEX)) {
        return NULL;
    }
    const CltOperVertexT *operVertex = CltGetDrStmtConstOperationContext(stmt);
    return operVertex->vertex;
}

CltCataLabelT *CltGetCltCataLabelInStmt(GmcStmtT *stmt)
{
    bool ok = (stmt->stmtType == CLT_STMT_TYPE_VERTEX || stmt->stmtType == CLT_STMT_TYPE_EDGE_TOPO ||
               stmt->stmtType == CLT_STMT_TYPE_SUB_VERTEX || stmt->stmtType == CLT_STMT_TYPE_SUB_PATH ||
               stmt->stmtType == CLT_STMT_TYPE_KV);
    if (SECUREC_UNLIKELY(!ok)) {
        return NULL;
    }
    return ((CltOperVertexT *)(stmt->operationContext))->cltCataLabel;
}

static void CltClearDrStmtOperVertex(GmcDirectReadStmtT *stmt, bool isClearVertexLabel)
{
    DB_ASSERT(stmt->stmtType == CLT_STMT_TYPE_VERTEX);
    CltDrOperVertexT *operVertex = CltGetDrStmtOperationContext(stmt);
    // FreeStmt和切表时销毁缓存的indexKey的内存
    DmDestroyIndexKey(operVertex->idx.leftKey);
    operVertex->idx.leftKey = NULL;

    DmVertexT *vertex = operVertex->vertex;
    DirectReadCtxCloseOpt(&stmt->drRunCtx, DIRECT_ACCESS_CLOSE_HEAP_CURSOR_CTX);
    DmDestroyVertex(vertex);
    operVertex->vertex = NULL;
    if (isClearVertexLabel && operVertex->cltCataLabel != NULL) {
        CltCataCloseVertexLabel(operVertex->cltCataLabel);
        operVertex->cltCataLabel = NULL;
    }
    return;
}

static void ResetDrStmtWithoutOperHandle(GmcDirectReadStmtT *stmt)
{
    // 使用新的反序列化解析模式会在此修改为默认模式
    stmt->cacheRows = 0;
    FixBufResetMem(stmt->recvPack);
}

void CltResetDrStmt(GmcDirectReadStmtT *stmt, bool isClearVertexLabel)
{
    if (stmt->stmtType == CLT_STMT_TYPE_VERTEX) {
        CltClearDrStmtOperVertex(stmt, isClearVertexLabel);
    }

    stmt->stmtType = CLT_STMT_TYPE_IDLE;
    ResetDrStmtWithoutOperHandle(stmt);
}
#ifdef EXPERIMENTAL_NERGC
typedef struct GetInvalidLabelsInfo {
    uint32_t labelType;  // DmLabelTypeE
    uint32_t labelId;
    uint32_t version;
} GetInvalidLabelsInfoT;

typedef struct GetInvalidLabelsPara {
    uint32_t instanceId;
    DbListT list;  // GetInvalidLabelsInfoT
} GetInvalidLabelsParaT;

Status CltFillOneLabelForGetInvalidLabels(void *labeList, CltCataLabelT *labelTmp, DmLabelTypeE labelType)
{
    DbListT *list = labeList;
    GetInvalidLabelsInfoT info = {
        .labelType = labelType, .labelId = labelTmp->metaCommon->metaId, .version = labelTmp->metaCommon->version};
    return DbAppendListItem(list, &info);
}

Status CltFillGetInvalidLabelsReq(FixBufferT *req, const void *in)
{
    const GetInvalidLabelsParaT *para = in;
    FillSimpleStmtMsgHeader(req, MSG_OP_RPC_GET_INVALID_LABEL_BY_LABEL_LIST);
    uint32_t reservedOffset = 0;
    Status ret = FixBufReserveDataOffset(req, sizeof(uint32_t), &reservedOffset);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    uint32_t labelNum = DbListGetItemCnt(&para->list);
    uint32_t index = 0;
    for (; index < labelNum; index++) {
        GetInvalidLabelsInfoT *info = DbListItem(&para->list, index);
        ret = FixBufPutUint32(req, info->labelType);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = FixBufPutUint32(req, info->labelId);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = FixBufPutUint32(req, info->labelType);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    uint32_t *addr = FixBufOffsetToAddr(req, reservedOffset);
    *addr = index;
    return ret;
}

Status CltParseGetInvalidLabelsRsp(FixBufferT *rsp, void *out)
{
    GetInvalidLabelsParaT *para = out;
    DbListT *labelList = &para->list;
    uint32_t labelCnt;
    Status ret = FixBufGetUint32(rsp, &labelCnt);
    if (ret != GMERR_OK) {
        return ret;
    }
    for (uint32_t i = 0; i < labelCnt; i++) {
        uint32_t index = 0;
        ret = FixBufGetUint32(rsp, &index);
        if (ret != GMERR_OK) {
            return ret;
        }
        GetInvalidLabelsInfoT *info = DbListItem(labelList, i);
        CltCataRemoveLabelById(para->instanceId, info->labelId);
    }
    return GMERR_OK;
}
void CltClearUndefinedLabelByRequest(GmcStmtT *stmt)
{
    GetInvalidLabelsParaT para = {0};
    para.instanceId = stmt->conn->instanceId;
    DbCreateList(&para.list, sizeof(GetInvalidLabelsInfoT), stmt->memCtx);
    uint32_t labelNum = CltCataMapGetAllCacheLabel(CltFillOneLabelForGetInvalidLabels, &para.list);
    if (labelNum == 0) {
        return;
    }
    Status ret = CltRequestSync(stmt->conn, CltFillGetInvalidLabelsReq, &para, CltParseGetInvalidLabelsRsp, &para);
    if (ret != GMERR_OK) {
        CltCataRemoveAllLabels();
    }
    DbDestroyList(&para.list);
}

void RemoveRemoteLabelFromCache(GmcStmtT *stmt)
{
    CltClearUndefinedLabelByRequest(stmt);
}

Status FillLabelIdInCtx(GmcStmtT *stmt, AsyncMsgContextT *ctx)
{
    CltOperVertexT *cltVertex = CltGetOperationContext(stmt);
    if (cltVertex->cltCataLabel == NULL || cltVertex->cltCataLabel->vertexLabel == NULL) {
        return GMERR_OK;
    }
    ctx->remoteLabelIdNum = 1;
    ctx->remoteLabelIdArr = DbDynMemCtxAlloc(g_gmdbCltInstance.memCtx, sizeof(uint32_t));
    if (ctx->remoteLabelIdArr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc memory for remote label id with problem.");
        return GMERR_OUT_OF_MEMORY;
    }
    *(ctx->remoteLabelIdArr) = cltVertex->cltCataLabel->vertexLabel->metaCommon.metaId;
    return GMERR_OK;
}

#endif

void FreeRemoteLabelIdArr(AsyncMsgContextT *ctx)
{
#ifdef EXPERIMENTAL_NERGC
    if (ctx->remoteLabelIdArr != NULL) {
        DbDynMemCtxFree(g_gmdbCltInstance.memCtx, ctx->remoteLabelIdArr);
        ctx->remoteLabelIdArr = NULL;
        ctx->remoteLabelIdNum = 0;
    }
#endif
}
