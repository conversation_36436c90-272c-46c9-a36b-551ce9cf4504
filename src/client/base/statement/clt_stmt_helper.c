/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: clt_stmt_helper.c
 * Description:
 * Author:
 * Create: 2024-10-14
 */

#include "clt_stmt_helper.h"
#include "clt_check.h"
#include "db_secure_msg_buffer.h"

#ifdef __cplusplus
extern "C" {
#endif

Status CltAllocDirectAccessContent(GmcStmtT *stmt, GmcConnT *conn)
{
    stmt->openDirectRead = conn->openDirectRead;
#if defined(EXPERIMENTAL_NERGC)
    if (DbIsTcp()) {
        return GMERR_OK;
    }
#endif
    if (!conn->isCsMode) {
        DrRunCtxT *drRunCtx = (DrRunCtxT *)conn->directReadCtx;
        stmt->drRunCtx = *drRunCtx;
    }
    return GMERR_OK;
}

void InitStmtRecvAndSndBuffer(GmcStmtT *stmt, GmcConnT *conn)
{
    // 初始化通信报文
    uint32_t flags = conn->isLobConn ? FIX_BUF_FLAG_LOB_BUFFER : FIX_BUF_FLAG_EXTEND_BUFFER;
    FixBufInit(&stmt->recvPack, NULL, 0, 0, flags, conn->msgCtx);
    FixBufInit(&stmt->labelRecvPack, NULL, 0, 0, flags, conn->msgCtx);
    if (!conn->isLobConn) {
        FixBufSetAllocCb(&conn->sendPack, SharedMsgPoolGet, SharedMsgPoolReturn, &conn->msgPoolCtx);
        FixBufSetAllocCb(&conn->recvPack, SharedMsgPoolGet, SharedMsgPoolReturn, &conn->msgPoolCtx);
    }
    DbDataT ctx;
    ctx.ptr = &conn->entryCtx;
    SecureFixBufSetVerifyProc(&stmt->recvPack, CltFixBufVerify, ctx);
    FixBufSetAllocCb(&stmt->recvPack, SharedMsgPoolGet, SharedMsgPoolReturn, &conn->msgPoolCtx);
    SecureFixBufSetVerifyProc(&stmt->labelRecvPack, CltFixBufVerify, ctx);
    FixBufSetAllocCb(&stmt->labelRecvPack, SharedMsgPoolGet, SharedMsgPoolReturn, &conn->msgPoolCtx);
}

#ifdef __cplusplus
}
#endif
