/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: clt_stmt_helper.h
 * Description:
 * Author:
 * Create: 2024-10-14
 */

#ifndef CLT_STMT_HELPER_H
#define CLT_STMT_HELPER_H

#include "gmc_errno.h"
#include "clt_stmt.h"

#ifdef __cplusplus
extern "C" {
#endif

Status CltAllocDirectAccessContent(GmcStmtT *stmt, GmcConnT *conn);

void InitStmtRecvAndSndBuffer(GmcStmtT *stmt, GmcConnT *conn);
#ifdef __cplusplus
}
#endif

#endif  // CLT_STMT_HELPER_H
