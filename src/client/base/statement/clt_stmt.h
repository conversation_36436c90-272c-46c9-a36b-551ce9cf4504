/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2021. All rights reserved.
 * File Name: clt_stmt.h
 * Description: Implement of GMDB client stmt
 * Author:
 * Create: 2020-08-05
 */

#ifndef CLT_STMT_H
#define CLT_STMT_H

#include <assert.h>
#include <stddef.h>
#include "clt_async_conn.h"
#include "clt_conn.h"
#include "db_list.h"

#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
#include "db_query_language_define.h"
#include "clt_ts_bulk_insert.h"
#include "se_temp_file.h"
#endif

#include "dm_data_kv.h"
#include "dm_data_prop.h"
#include "gmc_dlr.h"
#include "dm_yang_common.h"
#include "dm_yang_subtree.h"
#include "ee_topo_label.h"
#include "clt_da_handle.h"
#include "clt_catabase.h"

#include "clt_error.h"

#ifdef __cplusplus
extern "C" {
#endif

#define CLT_DEFAULT_PRE_FETCH_ROW 100
#define CLT_ALWAYS_INLINE inline __attribute__((always_inline))
#define CLT_FLOW_CTRL_QUEUE_PRIORITY_DEFAULT 1

#define CLT_MAGIC_WORD 0xDEADBEAF
#define CLT_MAGIC_WORD_AFTER_FREE 0xDEADDEAD
#define CLT_CONN_MAGIC_WORD 0xDB22F00D
#define CLT_CONN_MAGIC_WORD_AFTER_FREE 0xDB22BAAD
#define CLT_BATCH_MAGIC_WORD 0xDB22D00D
#define CLT_BATCH_MAGIC_WORD_AFTER_FREE 0xDB22CAFE
#define CLT_NODE_MAGIC_WORD 0xDB22FEE1
#define CLT_NODE_MAGIC_WORD_AFTER_FREE 0xDB22DEAD

typedef enum {
    CLT_STMT_TYPE_IDLE = 0,
    CLT_STMT_TYPE_VERTEX,
    CLT_STMT_TYPE_EDGE,
    CLT_STMT_TYPE_EDGE_TOPO,
    CLT_STMT_TYPE_SUB_VERTEX,
    CLT_STMT_TYPE_SUB_PATH,
    CLT_STMT_TYPE_SUB_KV,
    CLT_STMT_TYPE_KV,
    CLT_STMT_TYPE_TS,
    CLT_STMT_TYPE_STREAM,
#ifdef FEATURE_GQL
    CLT_STMT_TYPE_SUB_COMPLEX_PATH,
#endif  // FEATURE_GQL
    CLT_STMT_TYPE_CELL,
} CltStmtTypeE;

// neighbor vertex 查询应该从CltStmtTypeE类型中区分开，但是修改较大，后续再弄。
typedef enum {
    CLT_FETCH_VERTEX_DIRECT_READ,  // 用dstore查询vertex，vertex可能在dstore或main store
    CLT_FETCH_VERTEX_CS,           // 用连接查询vertex，vertex在main store
    CLT_FETCH_VERTEX_NEIGHBOR,     // 用连接查询邻接点
} CltVertexFetchE;

typedef struct {
    DmObjectT *kv;
    CltCataLabelT *cltCataLabel;
    CltVertexFetchE fetchType;
    void *sub;  // CltSubscriptionT
} CltOperKvT;

typedef struct WarmRebootLabelInfo {
    char *oriLabelName;   // 原始表表名
    char *labelName;      // 表名
    char *namespaceName;  // 表名对应的命名空间
    uint32_t labelType;   // 表类型
    uint16_t propNum;
    uint16_t version;  // 预留版本号，目前默认0
    uint8_t isUpOrDe;  // 表是否进行过升降级
} WarmRebootLabelInfoT;

typedef struct {
    bool isKeyBufSet;
    bool isIdxSelect;
    uint32_t rangeScanFlags;
    uint8_t condIdx;       /**前缀匹配的索引项 */
    uint8_t matchBytesNum; /**前缀匹配多少个字节 */
    uint8_t nearAIdx;      /**临近查询的A字段*/
    uint8_t nearBIdx;      /**临近查询的B字段，查询离A最近且B字段相同的数据*/
    DmVlIndexLabelT *indexLabel;
    DmIndexKeyT *leftKey;
    DmIndexKeyT *rightKey;
    DbListT *leftValue;
    DbListT *rightValue;
    DbListT *yangListKeyValue;
} CltIndexConditionT;

#ifdef FEATURE_GQL
// used for delete
typedef enum DeleteType {
    DELETE_BY_NONE = 0,  // default delete type
    DELETE_BY_INDEX,     // delete by index
    DELETE_BY_FIELD,     // delete by field
    DELETE_BY_RECORD     // delete by record
} DeleteTypeE;
#endif  // FEATURE_GQL

typedef struct {
    DmVertexT *vertex;  // input(insert, replace, merge, update) or output(query) vertex
    CltCataLabelT *cltCataLabel;
    CltIndexConditionT idx;
    const char *filter;
    const char *outputFormat;
    DbListT *orderByParams;
    void *sub;                        // 存放订阅信息CltSubscriptionT*
    CltVertexFetchE vertexFetchType;  // fetch type when fetch vertex

    bool doDeseri;  // 是否已经执行反序列化标记，执行完成反序列化或者重新查询后置为true，fetch完成置为false
    bool readBefore;  // fetch第一次标记，多条数据读时在第一条读完之后设置成true，用于fetch多条控制报文偏移
    bool probNotPrep;  // 是否有可能不用做prepare
    bool doNotDsScan;  // 用来判断是否能执行直连读

    StructFilterListT structFilterList;   // 只支持DS Scan和GQL模型下的BatchDelete
    GmcCheckReplaceCmpFuncT compareFunc;  // check replace时使用的自定义比较函数
    void *cmpUserData;                    // check replace 用户用于比较的数据
    TextT seriVertexBuf;                  // 结构化写入时在sendpack中标记vertexBuf的位置及长度

#ifdef FEATURE_GQL
    DeleteTypeE deleteType;  // for delete operation in GQL
#endif                       // FEATURE_GQL
} CltOperVertexT;

typedef struct CltneighborCtx {
    DmVertexT *vertex;  // output vertex
    CltCataLabelT *cltCataLabel;
    // direct query related
    EdgeTopoCursorHdlT seCursor;
    FixedHeapRunHdlT edgeTopoCtx;
    HpRunHdlT heapRunHdl;
    Handle edgeLabelLatch;       // 用来进行edgeLabel的并发控制
    Handle dstVertexLabelLatch;  // 用来进行dstVertexLabel的并发控制
    DbSessionCtxT *sessionCtx;  // 与labelLatch绑定，为保证客户端异常退出时，不为因labelLatch未释放而锁死
    uint32_t edgeLabelLatchVersionId;
    uint32_t dstVertexLabelLatchVersionId;
} CltNeighborCtxT;

typedef struct {
    DmVertexT *vertex;            // output vertex
    CltCataLabelT *cltCataLabel;  // 忘记当时为什么在这个结构体里面加了
    char srcVrtxPkName[DM_MAX_NAME_LENGTH];
    char dstVrtxPkName[DM_MAX_NAME_LENGTH];
    DbListT srcVrtxPkFilter;
    DbListT dstVrtxPkFilter;
} CltOperEdgeT;

typedef struct {
    uint8_t *ptr;   // point to property value in packet
    uint32_t size;  // size of value fetched
    uint32_t datatype;
} CltPropertyT;

#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
typedef struct {
    QueryDataDescT dataDesc;            // Data描述
    uint32_t affectedNum;               // DDL:受影响的表数量；DML：受影响行数
    QueryResultSetDescT resultSetDesc;  // 结果集描述
    DbListT columnDefines;              // 数组，列属性定义
    // 过程中用于记录状态的属性
    uint32_t lastRowLen;      // 上一行的长度
    uint32_t currRowLen;      // 当前行的长度
    uint32_t currTupleIndex;  // 当前元组下标
    DbListT currTuple;        // 数组，当前读取的元组的属性
    BufferFileT *tempFile;
    FixBufferT dynBuffer;  // 动态内存中的缓存数据
} CltOperQlT;
#endif

// CltStmtFlagT
#define CLT_FLAG_AUTO_DEL_EDGE 0x00000001u
#define CLT_FLAG_AUTO_CREATE_OUT_EDGE 0x00000002u
#define CLT_FLAG_AUTO_CREATE_IN_EDGE 0x00000004u
#define CLT_FLAG_AUTO_DEL_RELATION_VERTEX 0x00000008u

typedef struct DlrSubCtx {
    bool isFirstFetch;
    bool isFinish;
    TupleBufT *heapTupleBuf;
    ShmemPtrT cursor;
    uint32_t lastOperation;
} DlrSubCtxT;

typedef struct {
    union {
        DmVertexT *vertex;
        DmKvT *kv;
        DmObjectT *base;
    };
    CltCataLabelT *cltCataLabel;
    FixBufferT *msgData;
    uint32_t msgMaxType;  // 消息类型的最大集（key | oldData | newData | deltaData）
    uint32_t rowsRemain;
    uint64_t msgVersion;
    DbOamapT *versionMap;
    uint32_t eventType;
    uint32_t requiredType;  // msgType subscribed, it may contain more than what should be provied to the user
                            // use GmcSubGetMsgType to get the type accessible to user
    TextT keyData;
    TextT oldData;
    TextT newData;
    TextT deltaData;
    bool isStatusMerge;
    bool isDeSerialized;
    bool isDatalogSub;
    DlrSubCtxT dlrSubCtx;
    DmSubsConstraintT *constraint;
    DmSubYangInfoT *subYangInfo;
    void *oldObj;
    void *newObj;
    void *deltaObj;
    uint32_t recvMsgCnt;
    uint32_t matchMsgCnt;
} SubPushSingleLabel;

typedef struct {
    DmVertexT *vertex;
    uint32_t pathLen;
    DmVertexT **vertices;
} SubPushPath;

/* account check detail */
struct GmcCheckInfoT {
    GmcCheckStatusE checkStatus;
    uint16_t oldCheckVersion;
    uint16_t checkVersion;
    uint64_t shouldAgedCnt;
    uint64_t realAgedCnt;
    uint64_t shouldTruncateCnt;
    uint64_t realTruncatedCnt;
    uint64_t shouldRecoveryCnt;
    uint64_t realRecoveryCnt;
};

typedef struct {
    TextT cfgName;
    TextT cfgDesc;
    uint32_t changeMode;
    uint32_t dataType;
    DmValueT curValue;
    DmValueT minValue;
    DmValueT maxValue;
    DmValueT defaultValue;
} CltCfgValueT;

typedef struct {
    uint64_t normalObjAvgDynMem;    // 统计stmt句柄memCtx动态内存普通对象操作的平均值
    uint64_t normalObjMaxDynMem;    // 统计stmt句柄memCtx动态内存普通对象操作的最大值
    uint64_t bigObjAvgDynMem;       // 统计stmt句柄memCtx动态内存超大对象操作的平均值
    uint64_t bigObjMaxDynMem;       // 统计stmt句柄memCtx动态内存超大对象操作的最大值
    uint32_t normalObjDynMemCount;  // 统计stmt句柄memCtx动态内存普通对象操作的次数
    uint32_t bigObjDynMemCount;     // 统计stmt句柄memCtx动态内存超大对象操作的次数
} StmtDynMemT;

typedef struct LastOpStatus {
    GmcOperationTypeE lastOpType;  // 上一次操作的类型
    bool lastOpDirectWrite;        // 上一次操作使用直连写
    bool lastOpWriteCache;         // 上一次操作使用写缓存
    bool noNeedResetVertex;        // 上一次的操作后，本次操作 prepare 时不重置 vertex
} LastOpStatusT;

#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
struct GmcStmtViewT {
    TsStmtT tsInfo;               // 时序结构化写所需信息
    DmVertexLabelT *vertexLabel;  // 缓存的表元数据
    CltStmtTypeE stmtType;        // stmt类型
    GmcOperationTypeE operationType;
    uint32_t columnCount;
};
#endif

struct GmcStmtT {
    uint32_t magic;
    CltStmtTypeE stmtType;
    GmcConnT *conn;
    FixBufferT recvPack;  // cache response (including sync message response) in for stmt use
    DbMemCtxT *memCtx;  // 其父为conn->memCtx，释放stmt的时候会一把删除，所属内存最长生命周期为stmt级
    bool useNewDeseri;              // 标记是否使用新的反序列化函数，对应DmParseVertex
    bool isFullyParsed;             // 表示复杂表vertex是否已经进行过全量解析
    TextT vertexBufferInFixBuffer;  // 缓存fixbuffer上用于子节点进行vertex全量解析buffer对象

    DbMemCtxT *opCtx;  // everything allocated by opCtx will be freed on resetting stmt
    GmcOperationTypeE operationType;

    FixBufferT labelRecvPack;  // only for get vertex label when catalog number is not unlimited
    uint16_t stmtId;           // remote stmt id, use uint16_t, consistent with MsgHeaderT
    bool isDataService : 1;    // 用于标记是否是data service
    bool notDistribute : 1;    // 用于标记datalog请求是否不下发

    uint32_t affectRows;
    uint32_t cacheRows;
    uint32_t queryResRows;  // total num of query result rows
    uint32_t columnCount;
    uint64_t limitCount;  // move to CltOperVertexT?
    int64_t dtlErrorCode;
    char *dtlErrorLabelName;  // dtlErrorLabelName will be allocated MAX_TABLE_NAME_LEN at first time and freed when
                              // free stmt
    bool isExecuted;          // 用于Fetch时判断是否execute过，后续可以改造成动作的步骤
    bool eof;                 // mark whether the server scan has reached the EOF
    bool fetchEof;            // 用于fetch完的eof
    bool isStructure;
    uint32_t yangVertexTmpId;  // yang批操作中，当前操作节点的临时ID
    DbListT neighborsInfo;
    DrRunCtxT drRunCtx;       // 缓存直连读场景下的heap以及pkindex的运行上下文
    DbListT labelDescs;       // 缓存子树过滤中用到的vertex Label 描述信息链表
    DbListT propSet;          /* query properties set */
    uint8_t operateEdgeFlag;  // each bit represent a status , definition is in CltStmtFlagT

    DbListT orderByParams;
    DbListT leftValue;
    DbListT rightValue;
    DbListT yangListKeyValue;
    uint64_t operationContext[45];  // 当前的操作上下文，uint64保证 head addr 8字节对齐

    GmcBatchT *batch;     // stmt绑定batch之后指向batch指针
    DwRunCtxT *dwRunCtx;  // 【直连写】直连写上下文，在首次执行直连访问时申请
    WcRunCtxT *wcRunCtx;  // 【写缓存】上下文，在首次执行写缓存访问时申请

    bool isCltStatisDisable;  // 用于判断是否需要客户端统计视图，性能场景下需要关闭一些视图
    bool isSetPreFetch;       // 用于聚簇容器全表扫描特殊处理
    uint16_t preFetchRows;    /* rows returned from server in execute or fetch ack */

    DmIndexKeyT *keyValuesBuf;
    GmcCheckInfoT checkInfo;

    TagLinkedListT childStmtListNode;  // 对应GmcConnT::childStmtList
    TagLinkedListT stmtIdListNode;     // 对应GmcConnT::stmtIdList
    StmtDynMemT stmtDynMem;            // stmt句柄menCtx动态内存统计信息
    CltCfgValueT cfgValue;             /* config 的值仅在同步连接的时候生效 */
    bool isTools;  // 用于标记是否是gmimport的数据导入功能在使用stmt,进行CheckDatalogOpeationType检查
    bool isSysviewRecordCmd;  // 用于标记是否是视图record命令查询在使用stmt，在服务端进行过滤条件格式检查
    bool openDirectRead;         // 标记是否开启直连读
    bool dwRunCtxHasAllocated;   // 【直连写】表示DwRunCtxT结构体自身的内存是否已经分配
    bool openDirectWrite;        // 【直连写】标记是否开启直连写
    bool vlUseDirectWrite;       // 【直连写】vertexLabel 是否支持直连写
    bool openWriteCache;         // 【写缓存】标记是否开启写缓存
    bool wcRunCtxHasAllocated;   // 【写缓存】vertexLabel 是否支持写缓存
    LastOpStatusT lastOpStatus;  // 【直连写/写缓存】判断最近一次操作是否走直连写/写缓存路径
    DwResInfoT *resInfo;  // 【直连写】记录每次执行时的资源列信息，作为GmcGetResIdNum、GmcGetResIdInfo接口的数据源
#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
    TsStmtT *tsInfo;         // 时序结构化写所需信息
    FixBufferT dynRecvPack;  // 缓存的响应数据
#endif
    ModelTypeE modelType : 8;  // 基于语言api中指向当前需要执行的语句类型，如果和传递的语句不匹配由服务端报错
#ifdef FEATURE_GQL
    uint8_t *receivedPathData;   // 本次读取的Path数据
    char *receivedPathDataDesc;  // 本次读取的Path报文类型描述
#endif                           // FEATURE_GQL
#ifdef FEATURE_HP_ANNSEQSCAN
    VectorSearchCtxT vecSearchCtx;
#endif
};

// todo 函数、结构体解耦时迁移到和结构体放一起
static_assert(
    sizeof(((GmcStmtT *)0)->operationContext) >= sizeof(CltOperVertexT), "insufficient context size to CltOperVertexT");
static_assert(sizeof(((GmcStmtT *)0)->operationContext) >= sizeof(CltNeighborCtxT),
    "insufficient context size to CltNeighborCtxT");
static_assert(
    sizeof(((GmcStmtT *)0)->operationContext) >= sizeof(CltOperKvT), "insufficient context size to CltOperKvT");
static_assert(
    sizeof(((GmcStmtT *)0)->operationContext) >= sizeof(CltOperEdgeT), "insufficient context size to CltOperEdgeT");
static_assert(sizeof(((GmcStmtT *)0)->operationContext) >= sizeof(SubPushSingleLabel),
    "insufficient context size to SubPushSingleLabel");
static_assert(
    sizeof(((GmcStmtT *)0)->operationContext) >= sizeof(SubPushPath), "insufficient context size to SubPushPath");
#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
static_assert(
    sizeof(((GmcStmtT *)0)->operationContext) >= sizeof(CltOperQlT), "insufficient context size to CltOperQlT");
#endif

typedef struct RespSendFailedIndex {
    uint16_t failedDataNum;                     // 错误index的数量
    uint16_t failedIndexes[GMC_SUB_BATCH_MAX];  // 记录错误index数组
    bool isFillFailedIndex;                     // 标记是否填充过FailedIndex
} RespSendFailedIndexT;

typedef enum { RESP_BATCH_STATE_PREPARED, RESP_BATCH_STATE_INSERT, RESP_BATCH_STATE_BUTT } RespBatchInsertStateE;

typedef struct RespSendBatchInsert {
    RespBatchInsertStateE state;
    uint32_t rollBackPos;
    FixBufferT *sendBuf;
    uint32_t objectNumPos;
    uint16_t actualNum;
} RespSendBatchInsertT;

struct GmcRespT {
    GmcRespModeTypeE mode;
    GmcStmtT *bindStmt;
    union {
        RespSendFailedIndexT failIndexResp;
        RespSendBatchInsertT batchInsertResp;
    };
};

static_assert(offsetof(CltOperVertexT, vertex) == offsetof(CltNeighborCtxT, vertex), "offsetof not matching");
static_assert(offsetof(CltOperVertexT, vertex) == offsetof(SubPushSingleLabel, vertex), "offsetof not matching");
static_assert(offsetof(CltOperVertexT, vertex) == offsetof(SubPushPath, vertex), "offsetof not matching");

// for normal cases, fill msg into sendPack in stmt, then send it
Status CltStmtSendMsg(GmcStmtT *stmt, const ProtoHeadT *proto);
Status CltRecvStmtResponseWithCheckOpStatus(GmcStmtT *stmt);
GMDB_EXPORT Status CltStmtSendAndRecvMsg(GmcStmtT *stmt, const ProtoHeadT *proto);
Status CltStmtSendAndRecvAffectedRows(GmcStmtT *stmt, const ProtoHeadT *proto);
Status CltStmtRespMsg(GmcStmtT *stmt, const ProtoHeadT *proto);

Status CltStmtSendAndRecvScanMsg(GmcStmtT *stmt, const ProtoHeadT *proto);
Status CltSendDirectReadMsg(GmcStmtT *stmt, ProtoHeadT *proto);
Status CltStmtSetDtlErrorFromResponse(GmcStmtT *stmt);

Status CltStmtSendAndRecvDetectConfigs(
    GmcStmtT *stmt, const ProtoHeadT *proto, uint32_t *detectCfgNum, DbListT **configsDetectRes);

// ! 新代码请勿使用这些函数，不要依赖将要移除的CltStmtHandlerT !
void CltInitStmtOperVertex(GmcStmtT *stmt, DmVertexT *vertex, bool isInit);
GMDB_EXPORT void CltInitStmtOperEdgeT(GmcStmtT *stmt);
void ClearOpData(GmcStmtT *stmt, bool isClearVertexLabel);
void UpdateStmtObjMemCtxStatistics(GmcStmtT *stmt);
void UpdateConnObjMemCtxStatistics(GmcConnT *conn);

inline static void *CltGetOperationContext(GmcStmtT *stmt)
{
    DB_POINTER(stmt);
    return (void *)((stmt)->operationContext);
}

inline static DmVertexLabelT *CltGetVertexLabel(GmcStmtT *stmt)
{
    DB_POINTER(stmt);
    CltOperVertexT *cltVertex = (CltOperVertexT *)CltGetOperationContext(stmt);
    return cltVertex->cltCataLabel->vertexLabel;
}

inline static CltCataLabelT *CltGetCltVertexLabel(GmcStmtT *stmt)
{
    DB_POINTER(stmt);
    CltOperVertexT *cltVertex = (CltOperVertexT *)CltGetOperationContext(stmt);
    return cltVertex->cltCataLabel;
}

inline static const void *CltGetConstOperationContext(const GmcStmtT *stmt)
{
    DB_POINTER(stmt);
    return (const void *)((stmt)->operationContext);
}

void AllocStmtId(GmcStmtT *stmt);
void ReleaseStmtId(GmcStmtT *stmt);

GMDB_EXPORT void CltResetStmt(GmcStmtT *stmt, bool isClearVertexLabel);
void CltResetStmtNew(GmcStmtT *stmt);
Status CltResetStmtQuickForScan(GmcStmtT *stmt);
GMDB_EXPORT Status CltGetScanRecvDataRpc(GmcStmtT *stmt);
Status AllocStmt(GmcConnT *conn, GmcStmtT **stmt);
void FreeStmt(GmcStmtT *stmt);
Status CltSetStmtAttr(GmcStmtT *stmt, GmcStmtAttrTypeE attr, const void *value);
Status CltGetStmtAttr(const GmcStmtT *stmt, GmcStmtAttrTypeE attr, void *value, uint32_t valueSize);
Status ProcessAlarmDataResp(FixBufferT *fixBuffer, void *out);
void RemoveStmtFromConn(GmcConnT *conn, bool ignoreSubConn);

void CltDynCloseVertexLabel(CltCataLabelT *cltCataLabel);

// 为了解决销毁treeDesc的时候，不能close cltCataLabel问题，新封装的结构体
typedef struct CltTreeDesc {
    DmTreeDescT *treeDesc;
    CltCataLabelT *cltCataLabel;
} CltTreeDescT;

typedef struct CltDirectReadIndexConditionT {
    DmVlIndexLabelT *indexLabel;
    DmIndexKeyT *leftKey;
} CltDrIndexConditionT;

typedef struct CltDirectReadOperVertexT {
    DmVertexT *vertex;  // input(insert, replace, merge, update) or output(query) vertex
    CltCataLabelT *cltCataLabel;
    CltDrIndexConditionT idx;
    bool doDeseri;  // 是否已经执行反序列化标记，执行完成反序列化或者重新查询后置为true，fetch完成置为false
} CltDrOperVertexT;

// new stmt only for direact read, will be merged in base stmt later, before modify it, please ask limianhong/wangsi
// create by GmcStmt, can't support work parallely with father GmcStmt
struct GmcDirectReadStmtT {
    DbMemCtxT *memCtx;  // 是其父亲stmt的memCtx，释放stmt的时候会一把删除，所属内存最长生命周期为stmt级
    uint32_t *magic;
    CltStmtTypeE stmtType;
    GmcConnT *conn;
    FixBufferT *recvPack;
    FixBufferT *labelRecvPack;
    uint16_t *stmtId;
    uint32_t cacheRows;
    DrRunStructOptCtxT drRunCtx;
    union {
        CltDrOperVertexT drOperVertexContext;
    };
};

inline static void *CltGetDrStmtOperationContext(GmcDirectReadStmtT *stmt)
{
    DB_POINTER(stmt);
    return (void *)(&stmt->drOperVertexContext);
}

inline static const void *CltGetDrStmtConstOperationContext(const GmcDirectReadStmtT *stmt)
{
    DB_POINTER(stmt);
    return (const void *)(&stmt->drOperVertexContext);
}

// ! 新代码请勿使用这些函数，不要依赖将要移除的CltStmtHandlerT !
void CltInitDrStmtOperVertex(GmcDirectReadStmtT *stmt, DmVertexT *vertex, bool isInit);

// for normal cases, fill msg into sendPack in stmt, then send it
Status CltDrStmtSendMsg(GmcDirectReadStmtT *stmt, const ProtoHeadT *proto);
Status CltRecvDrStmtResponseWithCheckOpStatus(GmcDirectReadStmtT *stmt);
GMDB_EXPORT Status CltDrStmtSendAndRecvMsg(GmcDirectReadStmtT *stmt, const ProtoHeadT *proto);

GMDB_EXPORT void CltResetDrStmt(GmcDirectReadStmtT *stmt, bool isClearVertexLabel);
Status CltResetDrStmtQuickForScan(GmcDirectReadStmtT *stmt);
#ifdef EXPERIMENTAL_NERGC
void CltClearUndefinedLabelByRequest(GmcStmtT *stmt);
void RemoveRemoteLabelFromCache(GmcStmtT *stmt);
Status FillLabelIdInCtx(GmcStmtT *stmt, AsyncMsgContextT *ctx);
#endif
void FreeRemoteLabelIdArr(AsyncMsgContextT *ctx);
inline static bool CltGetIsDtlLabel(GmcStmtT *stmt)
{
    DB_POINTER(stmt);
    bool isDtlLabel = false;
    CltCataLabelT *cltCataLabel = CltGetCltVertexLabel(stmt);
    if (cltCataLabel != NULL) {
        isDtlLabel = DmVertexLabelIsDatalogLabel(cltCataLabel->vertexLabel);
    }
    return isDtlLabel;
}

inline static bool CltGetIsDataSyncLabel(const GmcStmtT *stmt)
{
    const CltOperVertexT *cltVertex = (const CltOperVertexT *)CltGetConstOperationContext(stmt);
    VertexLabelCommonInfoT *commonInfo = cltVertex->cltCataLabel->vertexLabel->commonInfo;
    return commonInfo->dlrInfo.isDataSyncLabel;
}

#ifdef __cplusplus
}
#endif

#endif
