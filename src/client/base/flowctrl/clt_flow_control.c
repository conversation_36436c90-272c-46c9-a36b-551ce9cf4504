/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: clt_flow_control.c
 * Description: Implement of GMDB client flowcontrol
 * Create: 2021-04-17
 */

#include "clt_flow_control.h"
#include "adpt_rdtsc.h"
#include "adpt_sleep.h"
#include "clt_error.h"

#define FLOWCTRL_WAITING_PERIOD (USECONDS_IN_SECOND * 3)

static void FlowControlStatInfoUpdate(CltFlowControlT *flowControl, GmcDbFlowCtrlLevelE flowControlLevel)
{
    if ((flowControl->realTimeFlowControlLevel != GMC_DB_FLOW_CTRL_LEVEL_0) && (flowControl->msgSentLimit != 1) &&
        (flowControl->msgSent > flowControl->msgSentLimit)) {
        flowControl->realFlowControlTimes = flowControl->realFlowControlTimes + 1;
        flowControl->realLastFlowControlTime = DbGetMsec();
    }
    if ((flowControlLevel > flowControl->realTimeFlowControlLevel) && (flowControlLevel != GMC_DB_FLOW_CTRL_LEVEL_0)) {
        flowControl->flowControlTimes = flowControl->flowControlTimes + 1;
        flowControl->lastFlowControlTime = DbGetMsec();
    }
    if ((flowControl->flowControlLevel != flowControlLevel) && flowControlLevel == GMC_DB_FLOW_CTRL_LEVEL_0) {
        flowControl->flowControlFreeTimes = flowControl->flowControlFreeTimes + 1;
        flowControl->lastFlowControlFreeTime = DbGetMsec();
    }
    if (flowControl->newFlowCtrl && flowControlLevel != GMC_DB_FLOW_CTRL_LEVEL_0) {
        flowControl->lastRecvFlowControlTime = DbToUseconds(DbRdtsc());
    }
}

void FlowControlWhenRecvResponse(CltFlowControlT *flowControl, GmcDbFlowCtrlLevelE flowControlLevel)
{
    if ((uint32_t)flowControlLevel > (uint32_t)GMC_DB_FLOW_CTRL_LEVEL_3) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "FlowControlLevel %" PRIu32 " from srv.", (uint32_t)flowControlLevel);
        return;
    }
    FlowControlStatInfoUpdate(flowControl, flowControlLevel);
    if (flowControl->realTimeFlowControlLevel != flowControlLevel) {
        flowControl->realTimeFlowControlLevel = flowControlLevel;
        if (flowControl->flowCtrlNotice != NULL) {
            flowControl->flowCtrlNotice(flowControl->args, flowControl->realTimeFlowControlLevel);
        }
    }
    uint64_t time = DbToUseconds(DbRdtsc());
    if (time < flowControl->nextPassiveUpdate) {
        return;
    }
    flowControl->nextPassiveUpdate = time + USECONDS_IN_SECOND;
    flowControl->flowControlLevel = flowControl->realTimeFlowControlLevel;

    if (flowControlLevel == GMC_DB_FLOW_CTRL_LEVEL_0) {
        return;
    }

    // change flowControl limit
    uint32_t index = (uint32_t)flowControlLevel - 1;
    int32_t flowControlStep[3] = {1, -1, -3};
    const uint32_t msgSentLimitMin = 1;    // msgSentLimit允许的最小值
    const uint32_t txStartedLimitMin = 1;  // txStartedLimit允许的最小值

    // before的值较小时，加上一个小于0的delta可能会得到一个负值。
    // 按照补码的表示方式，大于INT32_MAX即认为结果为负，重设为允许的最小值
    // 至于delta等于1时，因为实际的before几乎不可能大于等于INT32_MAX，所以不用特殊处理
    uint32_t delta = (uint32_t)flowControlStep[index];

    flowControl->msgSentLimit = flowControl->msgSentBefore + delta;
    if (flowControl->msgSentLimit > INT32_MAX ||  // < 0
        flowControl->msgSentLimit < msgSentLimitMin) {
        flowControl->msgSentLimit = msgSentLimitMin;
    }

    flowControl->txStartedLimit = flowControl->txStartedBefore + delta;
    if (flowControl->txStartedLimit > INT32_MAX ||  // < 0
        flowControl->txStartedLimit < txStartedLimitMin) {
        flowControl->txStartedLimit = txStartedLimitMin;
    }
}

inline static Status FlowControlUpdate(CltFlowControlT *flowControl, uint32_t *current, uint32_t limit)
{
    GmcDbFlowCtrlLevelE realTimeFlowCtrlLevel = flowControl->realTimeFlowControlLevel;
    flowControl->currentTime = DbGlobalRdtsc();
    uint64_t time = DbToUseconds(flowControl->currentTime);
    if (SECUREC_UNLIKELY(flowControl->newFlowCtrl && realTimeFlowCtrlLevel != GMC_DB_FLOW_CTRL_LEVEL_0 &&
                         time <= flowControl->lastRecvFlowControlTime + FLOWCTRL_WAITING_PERIOD)) {
        bool hasTableFlowCtrl = flowControl->labelFlowCtrlSleepTime != 0;
        uint32_t sleepTime = hasTableFlowCtrl ? DbGetSubFlowControlSleepTime(
                                                    flowControl->labelFlowCtrlSleepTime, realTimeFlowCtrlLevel - 1) :
                                                flowControl->flowCtrlSleepTime[(int32_t)realTimeFlowCtrlLevel - 1];
        DbUsleep(sleepTime);
        DB_LOG_WARN(GMERR_COMMON_STREAM_OVERLOAD, "FlowControlLevel=%" PRIu32 ", SleepTime=%" PRIu32,
            (uint32_t)realTimeFlowCtrlLevel, sleepTime);
        time += sleepTime;  // 考虑到流控对时间精确度要求不高，这里将time加上sleep时间作为最新时间
    }
    if (time >= flowControl->nextUpdate) {
        flowControl->nextUpdate = time + USECONDS_IN_SECOND;
        flowControl->msgSentBefore = flowControl->msgSent;
        flowControl->msgSent = 0;
        flowControl->txStartedBefore = flowControl->txStarted;
        flowControl->txStarted = 0;
    }
    if (flowControl->flowControlLevel == GMC_DB_FLOW_CTRL_LEVEL_0 || *current < limit) {
        ++(*current);
        return GMERR_OK;
    }
    return flowControl->newFlowCtrl ? GMERR_OK : GMERR_COMMON_STREAM_OVERLOAD;
}

Status FlowControlWhenSendRequest(CltFlowControlT *flowControl)
{
    Status ret = FlowControlUpdate(flowControl, &flowControl->msgSent, flowControl->msgSentLimit);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "Sending request reached limit");
        return ret;
    }
    return GMERR_OK;
}

Status FlowControlWhenStartTx(CltFlowControlT *flowControl)
{
    Status ret = FlowControlUpdate(flowControl, &flowControl->txStarted, flowControl->txStartedLimit);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_SET_LASTERR(ret, "Starting transaction reached limit");
        return ret;
    }
    return GMERR_OK;
}

Status FlowControlDirectWriteStartTrx(CltFlowControlT *flowControl)
{
    Status ret = FlowControlUpdate(flowControl, &flowControl->txStarted, flowControl->txStartedLimit);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "DirectWrite: starting trx reached limit");
        return ret;
    }
    return GMERR_OK;
}
