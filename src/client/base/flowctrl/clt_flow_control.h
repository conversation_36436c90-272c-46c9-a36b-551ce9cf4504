/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: clt_conn.h
 * Description: Implement of GMDB client flowcontrol
 * Create: 2021-04-17
 */

#ifndef CLT_FLOW_CONTROL_H
#define CLT_FLOW_CONTROL_H

#include "gmc_types.h"
#include "adpt_define.h"
#include "adpt_types.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    uint64_t currentTime;  // 记录流控开始时的时间
    uint64_t nextPassiveUpdate;  // 下次被动刷新时间（按照服务器返回的消息头中的流控等级进行刷新）
    uint64_t nextUpdate;                   // 下次主动刷新时间
    GmcDbFlowCtrlLevelE flowControlLevel;  // 流控等级,周期内是一个稳定的流控等级，是一个流控周期发生变化
    GmcDbFlowCtrlLevelE realTimeFlowControlLevel;  // 流控等级，每一个response 的流控等级不同时，就会更新,是实时更新
    uint32_t msgSentBefore;                        // 上个周期内发送的消息数
    uint32_t msgSent;                              // 本周期内发送的消息数
    uint32_t msgSentLimit;                         // 一个周期内允许发送的消息数
    uint32_t txStartedBefore;                    // 上个周期内发起的事务数
    uint32_t txStarted;                          // 本周期内发起的事务数
    uint32_t txStartedLimit;                     // 一个周期内允许发起的事务数
    GmcConnFlowCtrlLevelNoticeT flowCtrlNotice;  // APP 注册的流控通知函数
    void *args;                                  // APP注册的流控通知函数的参数

    uint64_t lastFlowControlTime;      // 最后一次流控发生的时间
    uint64_t lastFlowControlFreeTime;  // 最后一次解反压的时间
    uint64_t realLastFlowControlTime;  // 事实上客户端最后一次流控发生的时间
    uint64_t lastRecvFlowControlTime;  // 最后一次收到不为零流控等级时间
    uint64_t labelFlowCtrlSleepTime;   // 使用64bit标识表流控sleepTime（us）,
                                      // 低0~20位标识一级流控时间,20~40位标识二级流控时间,40~60位标识三级流控时间
    uint32_t flowCtrlSleepTime[(int32_t)GMC_DB_FLOW_CTRL_LEVEL_BUTT - 1];  // 流控等级对应需要sleep的时间（us）
    uint32_t flowControlTimes;                                             // 得到流控错误码的次数
    uint32_t flowControlFreeTimes;                                         // 解反压的次数
    uint32_t realFlowControlTimes : 31;                                    // 事实上客户端流控发生的次数
    bool newFlowCtrl : 1;                                                  // 是否采用新的流控方案
} CltFlowControlT;

void FlowControlWhenRecvResponse(CltFlowControlT *flowControl, GmcDbFlowCtrlLevelE flowControlLevel);
Status FlowControlWhenSendRequest(CltFlowControlT *flowControl);
Status FlowControlWhenStartTx(CltFlowControlT *flowControl);

Status FlowControlDirectWriteStartTrx(CltFlowControlT *flowControl);

#ifdef __cplusplus
}
#endif

#endif
