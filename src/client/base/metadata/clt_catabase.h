/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: clt_catabase.h
 * Description: header file for GMDB client catalog
 * Author:
 * Create: 2022-11-01
 */
#ifndef CLT_CATABASE_H
#define CLT_CATABASE_H

#include "dm_meta_basic.h"
#include "dm_meta_prop_strudefs.h"
#include "dm_meta_topo_label.h"
#include "dm_meta_kv_label.h"

#ifdef __cplusplus
extern "C" {
#endif
typedef struct CltCataLabelT {
    union {
        DmVertexLabelT *vertexLabel;  // catalog共享内存中的vertexlabel
        DmEdgeLabelT *edgeLabel;
        DmKvLabelT *kvLabel;
        DmMetaCommonT *metaCommon;  // 不确定类型的时候用这个
    };
    DmReusableMetaT *vertexMetaInfo;  // 性能要求，先转换好，减少转换测次数，kv和edge没有这个，会浪费几个字节
    DmAccCheckT *vertexAccCheck;  // 性能要求，先转换好，减少转换测次数，kv和edge没有这个，会浪费几个字节
    uint32_t cltRefCount;  // 客户端的vertexLabel引用计数
    uint32_t iniMetaId;
    uint32_t *srvRefCount;        // 服务端引用计数的链表
    ShmemPtrT labelShmPtr;        // catalog共享内存中的vertexlabel
    ShmemPtrT srvRefCountShmPtr;  // 服务端引用计数的shmPtr
    struct CltCataLabelT *prev;
    struct CltCataLabelT *next;
    uint32_t objPrivVersion;  // 校验对象权限
} CltCataLabelT;

#ifdef __cplusplus
}
#endif

#endif /* CLT_CATABASE_H */
