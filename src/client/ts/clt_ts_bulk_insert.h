/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Author: lihainuo
 * Description: bulk operation info of TS
 * Create: 2024-02-26
 */
#ifndef CLT_TS_BULK_INSERT_H
#define CLT_TS_BULK_INSERT_H

#include "dm_data_ts.h"

#ifdef __cplusplus
extern "C" {
#endif

#define SYSTBL_SQL_TYPE 3
#define TS_NOTATION_DECIMAL 10  // 以10进制形式将字符串转化为Integer

typedef struct TsBulkInsertStmt {
    DbMemCtxT *memCtx;       // 元数据内存上下文，非每次注入释放
    DbMemCtxT *tempMemCtx;   // 用来进行构造 CU 的临时内存上下文，每次注入释放
    TsLabelTypeE labelType;  // 时序表类型
    bool isDeduplicate;      // 是否去重
    uint32_t rowOffset;      // 默认为列绑定形式（为零）。对于行绑定形式，表示行偏移量。
    uint32_t rowArraySize;   // 注入数据总行数
    uint32_t logicTblId;     // 逻辑表 ID
    TsIntervalT interval;    // 分区间隔
    TsIntervalT tolerance;   // Tolerance间隔
    TsCompressionModeE compressionMode;  // 表级压缩参数
} TsBulkInsertStmtT;

typedef enum {
    TS_STMT_STATUS_BEGIN,
    TS_STMT_STATUS_INIT = TS_STMT_STATUS_BEGIN,
    TS_STMT_STATUS_PREPARE,      // GmcPrepareStmtByLabelName
    TS_STMT_STATUS_BIND,         // GmcBindCol
    TS_STMT_STATUS_BULKOP,       // GmcExecute
    TS_STMT_STATUS_EXEC_DIR,     // GmcExecDirect
    TS_STMT_STATUS_FETCH,        // GmcFetch
    TS_STMT_STATUS_GET_PROP,     // GmcGetPropertyById
    TS_STMT_STATUS_PREPARE_SQL,  // GmcPrepareSql
    TS_STMT_STATUS_BIND_PARA,    // GmcBindPara
    TS_STMT_STATUS_END = TS_STMT_STATUS_BIND_PARA,
} TsStmtStatusE;

typedef struct TsStmt {
    DbListT bindColStmts;              // bind col info list (TsBindColStmtT)
    TsBulkInsertStmtT bulkInsertInfo;  // save bulk insert info
    bool isDataClipped;                // indicate if the data is clipped
    bool isLabelPrepared;              // to mark if the table is in stmt's operationCtx
    TsStmtStatusE stmtStatus;
    uint8_t sqlType;  // 0: normal, 1:view, 2:explain, 3:sysTable
    // prepare sql (parameter query)
    DbMemCtxT *prepareMemCtx;  // prepare sql memctx, all is freed after processed.
    char *prepareSql;          // parameter query sql command
    uint16_t maxIndex;         // current max assigned para index
    DmValueT *paraArr;         // para value array with index
} TsStmtT;

/**
 * @brief time series client bulk insert interface
 * @param[in] tsStmt ts bulk insert statement
 * @param[in] bindColLists bounded column list, item <TsBindColStmtT>
 * @param[in/out] buf buf to store bulky data
 * @return GMERR_OK or other error code
 */
Status CltHelpFillTsBulkInsertMsg(TsBulkInsertStmtT *tsStmt, DbListT *bindColLists, FixBufferT *buf);

#ifdef __cplusplus
}
#endif
#endif
