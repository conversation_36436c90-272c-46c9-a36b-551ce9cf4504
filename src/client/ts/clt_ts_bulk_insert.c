/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Author: lihainuo
 * Description: realization of bulk insert of TS
 * Create: 2024-08-16
 */
#include "clt_msg.h"
#include "clt_fill_msg.h"
#include "clt_check.h"
#include "clt_ts_bulk_insert.h"

#define INVALID_UPPERBOUND (-1)

inline static bool IsToleranceOverFlow(time_t lowerBound, time_t upperBound, int64_t tolerance)
{
    return (lowerBound > 0 && lowerBound < tolerance) || upperBound + tolerance < upperBound;
}

static Status CltTsGetTimeAsInteger(void *data, DbDataTypeE bindType, int64_t *timeStamp)
{
    errno = EOK;  // 初始化 errno, errno 是线程安全的
    if (SECUREC_UNLIKELY(bindType == DB_DATATYPE_FIXED)) {
        char *endPtr = NULL;
        *timeStamp = strtoll(data, &endPtr, TS_NOTATION_DECIMAL);
        if (SECUREC_UNLIKELY(errno == ERANGE)) {  // fixed str 转换之后溢出
            DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "convert time value to integer:result is out of range");
            return GMERR_FIELD_OVERFLOW;
        }
        if (*endPtr != '\0') {  // 如果 fixed str 无法被转换为 integer 的话，endPtr 将指向第一个有问题的字符
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_VALUE,
                "convert time value to integer:\"%s\" represented as non-effective integer.", (char *)data);
            return GMERR_INVALID_VALUE;
        }
    } else {
        switch (bindType) {
            case DB_DATATYPE_INT8:
                *timeStamp = (int64_t)(*(int8_t *)data);
                break;
            case DB_DATATYPE_UINT8:
                *timeStamp = (int64_t)(*(uint8_t *)data);
                break;
            case DB_DATATYPE_INT16:
                *timeStamp = (int64_t)(*(int16_t *)data);
                break;
            case DB_DATATYPE_UINT16:
                *timeStamp = (int64_t)(*(uint16_t *)data);
                break;
            case DB_DATATYPE_INT32:
                *timeStamp = (int64_t)(*(int32_t *)data);
                break;
            case DB_DATATYPE_UINT32:
                *timeStamp = (int64_t)(*(uint32_t *)data);
                break;
            case DB_DATATYPE_INT64:
                *timeStamp = *(int64_t *)data;
                break;
            default:
                DB_LOG_AND_SET_LASERR(GMERR_INVALID_VALUE, "convert time column value");
                return GMERR_INVALID_VALUE;
        }
    }
    if (*timeStamp < 0) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_VALUE, "Value of the time col.");
        return GMERR_INVALID_VALUE;
    }
    return GMERR_OK;
}

static time_t CltTsSplitDataByTimeInterval(TsBulkInsertStmtT *tsStmt, TsBindColStmtT *timeColStmt, uint32_t *index,
    bool *setLowerTolerance, bool *setUpperTolerance)
{
    time_t lowerBound, upperBound;
    void *colArray = timeColStmt->colWiseVal.data;
    int64_t timeStamp = 0;
    Status ret = CltTsGetTimeAsInteger(
        colArray + (size_t)(*index) * timeColStmt->colWiseVal.dataSize, timeColStmt->colWiseVal.dataType, &timeStamp);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return INVALID_UPPERBOUND;
    }
    TsGetTimeBoundary((time_t)timeStamp, tsStmt->interval, &upperBound, &lowerBound);
    if (timeStamp > upperBound || timeStamp < lowerBound) {
        DB_LOG_ERROR(GMERR_PROGRAM_LIMIT_EXCEEDED, "time_col:%" PRId64 " exceeds the system time range", timeStamp);
    }
    // 当前约束时序表分区间隔为 1 min, 因此 interval.day 和 interval.month 值应当均为 0
    DB_ASSERT(tsStmt->tolerance.day == 0 && tsStmt->tolerance.month == 0 &&
              tsStmt->tolerance.microsecond == TS_MICROSECONDS_PER_MIN);
    int64_t tolerance = (tsStmt->tolerance.microsecond / TS_MICROSECONDS_PER_SECOND);
    if (tolerance > 0 && SECUREC_UNLIKELY(IsToleranceOverFlow(lowerBound, upperBound, tolerance))) {
        tolerance = 0;
    }
    int64_t value;
    uint32_t rowCount = 1;
    while ((*index) + rowCount < tsStmt->rowArraySize) {  // 热路径，后续考虑支持SIMD
        if (rowCount >= MAX_CU_SIZE) {
            break;
        }
        ret = CltTsGetTimeAsInteger(colArray + (size_t)(*index + rowCount) * timeColStmt->colWiseVal.dataSize,
            timeColStmt->colWiseVal.dataType, &value);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return INVALID_UPPERBOUND;
        }
        if (lowerBound <= value && value < upperBound) {
            rowCount++;
            continue;
        }
        if (tolerance > 0 && lowerBound - tolerance <= value && value < lowerBound) {
            *setLowerTolerance = true;
            rowCount++;
            continue;
        }
        if (tolerance > 0 && upperBound <= value && value < upperBound + tolerance) {
            *setUpperTolerance = true;
            rowCount++;
            continue;
        }
        // 时间不满足当前分区，切新 batch
        break;
    }
    (*index) += rowCount;
    return upperBound;
}

// 内存表不分区，也不受tolerance影响
static Status CltTsMemTableVerifyTimeCol(TsBulkInsertStmtT *tsStmt, TsBindColStmtT *timeColStmt, const uint32_t *index)
{
    void *colArray = timeColStmt->colWiseVal.data;
    int64_t timeStamp = 0;
    uint32_t rowCount = 0;

    while ((*index) + rowCount < tsStmt->rowArraySize) {  // 热路径，后续考虑支持SIMD
        Status ret = CltTsGetTimeAsInteger(colArray + (size_t)(*index + rowCount) * timeColStmt->colWiseVal.dataSize,
            timeColStmt->colWiseVal.dataType, &timeStamp);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        rowCount++;
    }

    return GMERR_OK;
}

static Status CltGenerateTimeCuDescAndCu(
    TsBulkInsertStmtT *tsStmt, TsBindColStmtT *timeColStmt, uint32_t *index, time_t *upperBound, CuStmtT *cuStmt)
{
    Status ret = GMERR_OK;
    uint32_t batchBegin = *index;
    bool setLowerTolerance = false, setUpperTolerance = false;
    if (timeColStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_VALUE, "time col data.");
        return GMERR_INVALID_VALUE;
    }
    if (tsStmt->labelType == LOGIC_LABEL) {
        // 根据时间节点确定时间界限接口不存在错误场景
        *upperBound = CltTsSplitDataByTimeInterval(tsStmt, timeColStmt, index, &setLowerTolerance, &setUpperTolerance);
        if (*upperBound == INVALID_UPPERBOUND) {  // 出错的场景接口内部已打印
            return GMERR_DATA_EXCEPTION;
        }
    } else {
        ret = CltTsMemTableVerifyTimeCol(tsStmt, timeColStmt, index);
        if (ret != GMERR_OK) {  // 出错的场景接口内部已打印
            // 与逻辑表对外表现保持一致
            return GMERR_DATA_EXCEPTION;
        }
        *index = tsStmt->rowArraySize;
    }
    DbMemCtxReset(cuStmt->tempMemCtx);
    ret = DmInitCuAndCuDesc(timeColStmt, batchBegin, *index - batchBegin, true, cuStmt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "generate time col cu and cu desc.");
        return ret;
    }
    SetToleranceMask(cuStmt->cuDesc, setLowerTolerance, setUpperTolerance);
    return GMERR_OK;
}

static Status FixBufferPutCommonInfo(TsBulkInsertStmtT *tsStmt, uint32_t columnNum, FixBufferT *buf)
{
    Status ret = FixBufPutUint32(buf, tsStmt->logicTblId);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "logical table id to fixBuffer.");
        return ret;
    }
    ret = FixBufPutUint32(buf, tsStmt->rowArraySize);  // total rows
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "total row number to fixBuffer.");
        return ret;
    }
    ret = FixBufPutUint32(buf, columnNum);  // total columns, equal CUs per bound
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "col num to fixBuffer.");
        return ret;
    }
    ret = FixBufPutUint16(buf, tsStmt->isDeduplicate);  // is de-duplicate
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "col num to fixBuffer.");
        return ret;
    }
    return ret;
}

static Status FixBufPutCuDescAndCu(DbMemCtxT *memCtx, FixBufferT *buf, DmCuDescT *desc, DmCuT *cu)
{
    DmBuffer cuBuf = {0};
    cuBuf.memCtx = memCtx;
    Status ret = DmSerializeCu(cu, &cuBuf, &desc->magic);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "serialize cu.");
        if (ret == GMERR_DATA_EXCEPTION) {  // 序列化越界，此时 cuDm中的 buffer 已成功申请，需要被释放
            goto FREE_CU_BUFFER;
        }
        return ret;
    }
    DmBuffer cuDescBuf = {0};
    cuDescBuf.memCtx = memCtx;
    ret = DmSerializeCuDesc(desc, &cuDescBuf);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "serialize cu descriptor.");
        return ret;
    }
    ret = FixBufPutData(buf, (char *)cuDescBuf.buf, cuDescBuf.len);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "serialized cu descriptor data to fixBuffer.");
        goto FREE_CU_DESC_BUFFER;
    }
    ret = FixBufPutData(buf, (char *)cuBuf.buf, cuBuf.len);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "serialized cu data to fixBuffer.");
    }
FREE_CU_DESC_BUFFER:
    DbDynMemCtxFree(cuDescBuf.memCtx, cuDescBuf.buf);
FREE_CU_BUFFER:
    DbDynMemCtxFree(cuBuf.memCtx, cuBuf.buf);
    // free other cu part whose memory was allocated via memCtx
    if (!IsCuSrcDataBindClientMem(cu)) {
        DbDynMemCtxFree(cu->memCtx, cu->srcData);
        cu->srcData = NULL;
    }
    if (cu->compressAlgoNum != 0) {
        DbDynMemCtxFree(cu->memCtx, cu->compressAlgos);
        cu->compressAlgos = NULL;
    }
    return ret;
}

/**
 * time-series bulk insert msg
 *      RpcMsgHeader | logicTblId | totalRows | totalColumns | upperBound | CuDesc | CU | CuDesc | CU |
 *      upperBound | CuDesc | CU | CuDesc | CU | ...
 */
Status CltHelpFillTsBulkInsertMsg(TsBulkInsertStmtT *tsStmt, DbListT *bindColLists, FixBufferT *buf)
{
    DB_POINTER5(tsStmt, tsStmt->memCtx, bindColLists, buf, buf->memCtx);
    Status ret = FixBufferPutCommonInfo(tsStmt, DbListGetItemCnt(bindColLists), buf);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "common info to fixBuffer.");
        return ret;
    }
    TsBindColStmtT *timeColStmt = (TsBindColStmtT *)DbListItem(bindColLists, 0);  // time column
    uint32_t batchEndIdx = 0;
    while (batchEndIdx < tsStmt->rowArraySize) {
        uint32_t batchBeginIdx = batchEndIdx;
        // cuDesc 和 cu 均为栈变量，临时使用，会被序列化到 fixBuffer 中
        DmCuDescT cuDesc = (DmCuDescT){0};
        DmCuT cu = (DmCuT){0};
        // 时间列
        CuStmtT cuStmt = (CuStmtT){
            .tempMemCtx = tsStmt->tempMemCtx, .compressionMode = tsStmt->compressionMode, .cu = &cu, .cuDesc = &cuDesc};
        time_t upperBound = 0;
        ret = CltGenerateTimeCuDescAndCu(tsStmt, timeColStmt, &batchEndIdx, &upperBound, &cuStmt);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "create cu desc and cu of time col.");
            return ret;
        }
        if ((ret = FixBufPutInt64(buf, upperBound)) != GMERR_OK) {
            DB_LOG_ERROR(ret, "time upper bound to fixBuffer.");
            return ret;
        }
        if ((ret = FixBufPutCuDescAndCu(tsStmt->tempMemCtx, buf, &cuDesc, &cu)) != GMERR_OK) {
            DB_LOG_ERROR(ret, "time col cu and cu desc to fixBuffer.");
            return ret;
        }
        for (uint32_t i = 1; i < DbListGetItemCnt(bindColLists); i++) {
            cuDesc = (DmCuDescT){0};
            cu = (DmCuT){0};
            // 非时间列
            TsBindColStmtT *colStmt = (TsBindColStmtT *)DbListItem(bindColLists, i);
            cuStmt.cu = &cu;
            cuStmt.cuDesc = &cuDesc;
            DbMemCtxReset(cuStmt.tempMemCtx);
            ret = DmInitCuAndCuDesc(colStmt, batchBeginIdx, batchEndIdx - batchBeginIdx, true, &cuStmt);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "create cu desc and cu.");
                return ret;
            }
            if ((ret = FixBufPutCuDescAndCu(tsStmt->tempMemCtx, buf, &cuDesc, &cu)) != GMERR_OK) {
                DB_LOG_ERROR(ret, "other col cu and cu desc to fixBuffer.");
                return ret;
            }
        }
    }
    return GMERR_OK;  // 显式返回成功标识
}
