/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: clt_ts_helper.c
 * Description:
 * Author:
 * Create: 2025-02-13
 */

#include "clt_ts_helper.h"

#ifdef __cplusplus
extern "C" {
#endif

Status CopyTsRspIntoDyn(DbMemCtxT *opCtx, CltOperQlT *op, FixBufferT *buf)
{
    Status ret = FixBufCreate(&op->dynBuffer, opCtx, FixBufGetTotalLength(buf), 0);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "create dql dyn fix buf");
        return ret;
    }
    uint32_t seekPos = FixBufGetSeekPos(buf);
    return FixBufPutData(&op->dynBuffer, FixBufGetBuf(buf) + seekPos, FixBufGetTotalLength(buf) - seekPos);
}

#ifdef __cplusplus
}
#endif
