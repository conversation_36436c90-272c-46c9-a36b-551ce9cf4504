#其他依赖模块的头文件目录按需添加
include_directories(${GMDB_CJSON_INC_PATH})
include_directories(${GMDB_JANSSON_INC_PATH})
include_directories(${GMDB_XXHASH_INC_PATH})

include_sub_directories_recursively(${CMAKE_CURRENT_SOURCE_DIR})

aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/base SRC_CLIENT_BASE_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/base/instance SRC_CLIENT_INSTANCE_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/base/connection SRC_CLIENT_CONNECTION_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/base/statement SRC_CLIENT_STATEMENT_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/base/workflow SRC_CLIENT_WORKFLOW_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/base/utils SRC_CLIENT_UTILS_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/base/flowctrl SRC_CLIENT_CAPI_FLOWCTRL_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/base/metadata SRC_CLIENT_METADATA_LIST)

aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/comapi SRC_CLIENT_COMMON_CAPI_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/comapi/namespace SRC_CLIENT_CAPI_NAMESPACE_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/comapi/privilege SRC_CLIENT_CAPI_PRIVILEGE_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/comapi/exception SRC_CLIENT_CAPI_EXCEPTION_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/comapi/pubsub SRC_CLIENT_CAPI_PUBSUB_LIST)

if (FEATURE_TABLESPACE)
	aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/comapi/tablespace SRC_CLIENT_CAPI_TABLESPACE_LIST)
endif()

aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/datalog SRC_CLIENT_DATALOG_CAPI_LIST)

aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/fastpath SRC_CLIENT_FASTPATH_CAPI_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/fastpath/graph SRC_CLIENT_CAPI_GRAPH_LIST)
if (NOT FEATURE_EDGELABEL)
	list(REMOVE_ITEM SRC_CLIENT_CAPI_GRAPH_LIST ${CMAKE_CURRENT_SOURCE_DIR}/fastpath/graph/clt_graph_ddl_edge.c)
	list(REMOVE_ITEM SRC_CLIENT_CAPI_GRAPH_LIST ${CMAKE_CURRENT_SOURCE_DIR}/fastpath/graph/clt_edge_topo_dml.c)
	list(REMOVE_ITEM SRC_CLIENT_CAPI_GRAPH_LIST ${CMAKE_CURRENT_SOURCE_DIR}/fastpath/graph/clt_graph_dml_edge.c)
endif()
if (FEATURE_RSMEM)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/fastpath/graph/reservedmem SRC_CLIENT_CAPI_GRAPH_LIST)
else()
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/fastpath/graph/reservedmem/empty SRC_CLIENT_CAPI_GRAPH_LIST)
endif()
if(DIRECT_WRITE)
	aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/fastpath/direct SRC_CLIENT_DA_LIST)
	list(REMOVE_ITEM SRC_CLIENT_DA_LIST ${CMAKE_CURRENT_SOURCE_DIR}/fastpath/direct/clt_da_component.c)
else()
	set(SRC_CLIENT_DA_LIST ${CMAKE_CURRENT_SOURCE_DIR}/fastpath/direct/clt_da_read.c)
	list(APPEND SRC_CLIENT_DA_LIST ${CMAKE_CURRENT_SOURCE_DIR}/fastpath/direct/clt_da_component.c)
endif()
if(FEATURE_TS OR TS_MULTI_INST)
	aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/ts SRC_CLIENT_TS_LIST)
	if (NOT TS_MULTI_INST)
		list(REMOVE_ITEM SRC_CLIENT_CAPI_GRAPH_LIST ${CMAKE_CURRENT_SOURCE_DIR}/fastpath/graph/clt_write_cache.c)
		list(APPEND SRC_CLIENT_CAPI_GRAPH_LIST ${CMAKE_CURRENT_SOURCE_DIR}/fastpath/graph/empty/clt_write_cache_empty.c)
	endif()
endif()
if(FEATURE_STREAM OR STREAM_MULTI_INST)
	aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/stream SRC_CLIENT_STREAM_LIST)
endif()
if(FEATURE_FASTPATH OR TS_MULTI_INST OR FEATURE_STREAM)
	list(REMOVE_ITEM SRC_CLIENT_TS_LIST ${CMAKE_CURRENT_SOURCE_DIR}/ts/gmc_ts_empty.c)
endif()
if(FEATURE_STREAM AND NOT TS_MULTI_INST)
	list(APPEND SRC_CLIENT_CAPI_GRAPH_LIST ${CMAKE_CURRENT_SOURCE_DIR}/fastpath/graph/empty/clt_stream_empty.c)
endif()

if (FEATURE_GQL)
	aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/gql SRC_CLIENT_GQL_LIST)
endif ()

if (FEATURE_YANG)
	aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/yang SRC_CLIENT_CAPI_YANG_LIST)
endif()

if (FEATURE_STRUCT)
	aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/fastpath/batch SRC_CLIENT_CAPI_BATCH_LIST)
	aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/fastpath/graph/struct SRC_CLIENT_CAPI_GRAPH_STRUCT_LIST)
endif()

if (FEATURE_KV)
	aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/fastpath/kv SRC_CLIENT_CAPI_KV_LIST)
endif()

if (FEATURE_RESPOOL_CLI)
	aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/fastpath/respool SRC_CLIENT_CAPI_RESPOOL_LIST)
endif()

if (FEATURE_TREE)
	aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/fastpath/tree SRC_CLIENT_CAPI_TREE_LIST)
endif()

set(DATALOG_SRCS ${DATALOG_SRCS} ${SRC_CLIENT_DATALOG_CAPI_LIST} PARENT_SCOPE)
list(APPEND SRC_CLIENT_BASE_LIST ${CMAKE_CURRENT_SOURCE_DIR}/datalog/gmc_datalog_inner.c)

set(SRC_GMCLIENT_LIST ${SRC_CLIENT_BASE_LIST} ${SRC_CLIENT_COMON_CAPI_LIST} ${SRC_CLIENT_FASTPATH_CAPI_LIST} ${SRC_CLIENT_CAPI_BATCH_LIST} ${SRC_CLIENT_CAPI_FLOWCTRL_LIST} ${SRC_CLIENT_CAPI_GRAPH_LIST}
    ${SRC_CLIENT_CAPI_GRAPH_STRUCT_LIST} ${SRC_CLIENT_CAPI_KV_LIST} ${SRC_CLIENT_CAPI_NAMESPACE_LIST} ${SRC_CLIENT_CAPI_PRIVILEGE_LIST} ${SRC_CLIENT_CAPI_PUBSUB_LIST}
    ${SRC_CLIENT_CAPI_RESPOOL_LIST} ${SRC_CLIENT_CAPI_TABLESPACE_LIST} ${SRC_CLIENT_CAPI_TREE_LIST} ${SRC_CLIENT_CAPI_YANG_LIST} ${SRC_CLIENT_CAPI_EXCEPTION_LIST}
    ${SRC_CLIENT_CONNECTION_LIST} ${SRC_CLIENT_STATEMENT_LIST} ${SRC_CLIENT_COMMON_CAPI_LIST} ${SRC_CLIENT_INSTANCE_LIST} ${SRC_CLIENT_METADATA_LIST} ${SRC_CLIENT_UTILS_LIST}
		${SRC_CLIENT_WORKFLOW_LIST} ${SRC_CLIENT_DA_LIST} ${SRC_CLIENT_TS_LIST} ${SRC_CLIENT_STREAM_LIST} ${SRC_CLIENT_GQL_LIST})

if(TS_MULTI_INST AND NOT FEATURE_YANG)
	list(REMOVE_ITEM SRC_GMCLIENT_LIST ${CMAKE_CURRENT_SOURCE_DIR}/base/metadata/clt_catalog_sub_yang.c)
endif()

if(MODULARBUILD)
	set(MODULE_NAME gmclient)
	add_library(${MODULE_NAME} SHARED ${SRC_GMCLIENT_LIST})
	target_link_libraries(${MODULE_NAME} gmdatamodel gmexecutor)
	if(STRIP)
        separate_debug_info(${MODULE_NAME})
    endif()
	set_target_properties(${MODULE_NAME} PROPERTIES VERSION ${GMDB_PROJECT_VERSION} SOVERSION ${GMDB_MAJOR_VERSION})
	install(TARGETS ${MODULE_NAME} LIBRARY DESTINATION lib)
else()
	list(APPEND LIBRARIES_LIST ${SRC_GMCLIENT_LIST})
	set(LIBRARIES_LIST ${LIBRARIES_LIST} PARENT_SCOPE)
endif()

if(NOT FEATURE_SIMPLEREL AND DEBUG)
  	install(FILES include/gmc_test.h include/gmc_internal.h include/gmc_internal_types.h include/gmc_privilege.h
  	  DESTINATION include
  	)
endif()
