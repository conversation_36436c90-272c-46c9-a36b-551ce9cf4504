/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: db crash debug point def head file
 * Author: l<PERSON><PERSON><PERSON><PERSON>
 * Create: 2024-07-10
 */
#ifndef DB_CRASH_DEBUG_DEF_H
#define DB_CRASH_DEBUG_DEF_H

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#ifdef ENABLE_CRASHPOINT

typedef enum DbShmCrashPointType {
    SHM_CRASH_BEGIN = 0,
    // FIXED_HEAP_INSERT
    SHM_CRASH_FH_INSERT_DFX = SHM_CRASH_BEGIN,  // 测试更新DFX后发生异常的场景，看护perfStat的锁、物理/逻辑计数等DFX
    SHM_CRASH_FH_INIT_PAGE_BEFORE,  // 测试初始化页之前发生异常，恢复重启后首次初始化是否完备
    SHM_CRASH_FH_INIT_PAGE_AFTER,   // 测试初始化页之后发生异常，恢复重启后二次初始化是否ok
    SHM_CRASH_FH_INS_UNDO_LOG_BEFORE,  // 测试记录undo前发生异常，恢复重启是否是干净的页
    SHM_CRASH_FH_INS_UNDO_LOG_AFTER,   // 测试记录undo后发生异常，测试回滚未执行操作，是否ok
    SHM_CRASH_FH_ALLOC_ROW_BEFORE,     // 测试修改slot前发生异常，未修改状态下，rsmUndo回滚是否ok
    SHM_CRASH_FH_INIT_ROW_BEFORE,  // 测试修改buf前发生异常，页面修改状态不一致下，rsmUndo回滚是否ok
    SHM_CRASH_FH_INIT_ROW_AFTER,  // 测试修改buf后发生异常，完全修改状态下，rsmUndo回滚是否ok
    // FIXED_HEAP_BATCH_INSERT
    SHM_CRASH_FH_BATCH_INS_UNDO_LOG_BEFORE,  // 测试记录undo前发生异常，恢复重启是否是干净的页
    SHM_CRASH_FH_BATCH_INS_UNDO_LOG_AFTER,   // 测试记录undo后发生异常，undo能否回滚
    SHM_CRASH_FH_BATCH_INS_BEFORE,  // 测试记录rsmUndo后，真正修改前发生异常，rsmUndo能否恢复现场然后undo回滚
    SHM_CRASH_FH_BATCH_INS_AFTER,  // 测试真正修改后发生异常，rsmUndo能否恢复现场然后undo回滚
    SHM_CRASH_FH_BATCH_INS_RSM_UNDO_FINISH,  // 测试rsmUndo回退后发生异常，undo能否回滚
    // FIXED_HEAP_UPDATE
    SHM_CRASH_FH_UPD_UNDO_LOG_AFTER,     // 测试记录undo后发生异常，测试回滚未执行操作，是否ok
    SHM_CRASH_FH_UPD_NORMAL_ROW_BEFORE,  // 测试记录rsmUndo后、更新normal行前发生异常，rsmUndo回滚是否ok
    SHM_CRASH_FH_UPD_NORMAL_ROW_AFTER,  // 测试更新normal行后发生异常，rsmUndo回滚是否ok
    SHM_CRASH_FH_ALLOC_DST_ROW_BEFORE,  // 测试更新normal行到src行/src行更新到src行，申请dst行时发生异常
    SHM_CRASH_FH_INIT_DST_ROW_BEFORE,  //  测试更新normal行到src行/src行更新到src行，写入dst行前发生异常
    SHM_CRASH_FH_INIT_DST_ROW_AFTER,  //  测试更新normal行到src行/src行更新到src行，写入dst行后发生异常
    SHM_CRASH_FH_UPD_SRC_ROW_BEFORE,  // 测试更新normal行到src行/src行更新到src行，刷新srcRow前发生异常
    SHM_CRASH_FH_UPD_SRC_ROW_AFTER,  // 测试更新normal行到src行/src行更新到src行，刷新srcRow后发生异常
    SHM_CRASH_FH_UPD_DEL_DST_ROW_BEFORE,  // 测试提交时删除旧的dst行/回滚时删除新的dst行前发生异常
    SHM_CRASH_FH_UPD_DEL_DST_ROW_AFTER,  // 测试提交时删除旧的dst行/回滚时删除新的dst行后发生异常
    SHM_CRASH_FH_UPD_DEL_DST_ROW_FINISH,  // 测试提交时删除旧的dst行/回滚时删除新的dst行已完成，测试重启恢复后重新提交/回滚
    SHM_CRASH_FH_UPD_DEL_DST_ROW_AND_RETURN_TO_MD,  // 测试事务提交时，删除最后的dst行，然后归还页到memdata后复位
    SHM_CRASH_FH_UPD_ROLLBACK_TO_SRC_ROW_BEFORE,  // 测试更新src行到src行的回滚流程，回滚到旧src行前发生异常
    SHM_CRASH_FH_UPD_ROLLBACK_TO_SRC_ROW_AFTER,  // 测试更新src行到src行的回滚流程，回滚到旧src行后发生异常
    SHM_CRASH_HP_PARTIAL_UPD_BEFORE,             // 测试部分更新前发生异常，rsmUndo回滚是否ok
    SHM_CRASH_HP_PARTIAL_UPD_AFTER,              // 测试部分更新后发生异常，rsmUndo回滚是否ok
    // FIXED_HEAP_DELETE
    SHM_CRASH_FH_DEL_NORMAL_ROW_BEFORE,  // 测试记录rsmUndo后、删除normal行前发生异常，rsmUndo重演是否ok
    SHM_CRASH_FH_DEL_NORMAL_ROW_AFTER,  // 测试删除normal行后发生异常，rsmUndo重演是否ok
    SHM_CRASH_FH_DEL_NORMAL_ROW_FINISH,  // 测试删除normal行、回退rsmUndo后发生异常，页锁是否正确恢复
    SHM_CRASH_FH_DEL_SRC_ROW_BEFORE,
    SHM_CRASH_FH_DEL_SRC_ROW_AFTER,
    SHM_CRASH_FH_DEL_SRC_ROW_FINISH,
    SHM_CRASH_FH_DEL_DST_ROW_BEFORE,
    SHM_CRASH_FH_DEL_DST_ROW_AFTER,
    SHM_CRASH_FH_DEL_DST_ROW_FINISH,
    // FIXED_HEAP_MARK_DEL
    SHM_CRASH_HP_MARK_DEL_ROW_BEFORE_RSM_UNDO,  // 测试记了undo但未实际打上标记删除位（未记录rsmUndo）时发生异常
    SHM_CRASH_HP_MARK_DEL_ROW_BEFORE,  // 测试记录完rsmUndo发生异常
    SHM_CRASH_HP_MARK_DEL_ROW_AFTER,   // 测试打完标记删除位后发生异常
    // 内核态状态相关测试
    SHM_CRASH_RECOVERY_FINISH_BEFORE,  // 测试在恢复阶段发生异常退出，重启通过内核态校验能否正常拦截
    // VAR_HEAP
    SHM_CRASH_VH_INIT_PAGE_BEFORE,
    SHM_CRASH_VH_INIT_PAGE_AFTER,
    SHM_CRASH_VH_COMPRESS_ROW_BEFORE,  // 测试变长heap页面整理前，记录rsmUndo后发生异常
    SHM_CRASH_VH_COMPRESS_ROW_AFTER,   // 测试变长heap页面整理后发生异常
    SHM_CRASH_VH_MEMMOVE_ROW_BEFORE,
    SHM_CRASH_VH_MEMMOVE_ROW_AFTER,
    SHM_CRASH_VH_ALLOC_SLOT_BEFORE,
    SHM_CRASH_VH_ALLOC_SLOT_AFTER,
    SHM_CRASH_VH_INS_UNDO_LOG_BEFORE,  // 测试记录undo前发生异常，恢复重启是否是干净的页
    SHM_CRASH_VH_INS_UNDO_LOG_AFTER,  // 测试记录undo后、rsmUndo前发生异常，测试回滚未执行操作，是否ok
    SHM_CRASH_VH_INS_RSM_UNDO_LOG_AFTER,   // 测试记录rsmUndo后发生异常，恢复重启是否是干净的页
    SHM_CRASH_VH_INS_SLOT_AFTER,           // 测试完成slot插入后发生异常，恢复重启是否是干净的页
    SHM_CRASH_VH_INS_RSM_UNDO_LOG_FINISH,  // 测试rsmUndo回退后发生异常，事务undo回滚是否ok
    // VAR_HEAP_DELETE
    SHM_CRASH_VH_DEL_NORMAL_ROW_BEFORE,  // 测试记录rsmUndo后、删除normal行前发生异常，rsmUndo重演是否ok
    SHM_CRASH_VH_DEL_NORMAL_ROW_AFTER,  // 测试删除normal行后发生异常，rsmUndo重演是否ok
    SHM_CRASH_VH_DEL_NORMAL_ROW_FINISH,  // 测试删除normal行、回退rsmUndo后发生异常，页锁是否正确恢复
    SHM_CRASH_VH_DEL_SRC_ROW_BEFORE,
    SHM_CRASH_VH_DEL_SRC_ROW_AFTER,
    SHM_CRASH_VH_DEL_SRC_ROW_FINISH,
    SHM_CRASH_VH_DEL_DST_ROW_CHANGE_STATUS_BEFORE,
    SHM_CRASH_VH_DEL_DST_ROW_BEFORE,
    SHM_CRASH_VH_DEL_DST_ROW_AFTER,
    SHM_CRASH_VH_DEL_DST_ROW_FINISH,
    // VAR_HEAP_UPDATE
    SHM_CRASH_VH_UPD_NORMAL_ROW_BEFORE,  // 测试记录rsmUndo后、更新normal行前发生异常，rsmUndo回滚是否ok
    SHM_CRASH_VH_UPD_NORMAL_ROW_AFTER,   // 测试更新normal行后发生异常，rsmUndo回滚是否ok
    SHM_CRASH_VH_UPD_NORMAL_ROW_FINISH,  // 测试回退rsmUndo后发生异常，事务回滚是否ok
    SHM_CRASH_VH_UPD_NORMAL_ROW_ROLLBACK_BEFORE,
    SHM_CRASH_VH_UPD_NORMAL_ROW_ROLLBACK_AFTER,
    SHM_CRASH_VH_UPD_NORMAL_ROW_ROLLBACK_FINISH,
    SHM_CRASH_VH_ALLOC_DST_ROW_BEFORE,  // 测试更新normal行到src行/src行更新到src行，申请dst行时发生异常
    SHM_CRASH_VH_INIT_DST_ROW_BEFORE,  //  测试更新normal行到src行/src行更新到src行，写入dst行前发生异常
    SHM_CRASH_VH_INIT_DST_ROW_AFTER,  //  测试更新normal行到src行/src行更新到src行，写入dst行后发生异常
    SHM_CRASH_VH_UPD_SRC_ROW_BEFORE,  // 测试更新normal行到src行/src行更新到src行，刷新srcRow前发生异常
    SHM_CRASH_VH_UPD_SRC_ROW_AFTER,  // 测试更新normal行到src行/src行更新到src行，刷新srcRow后发生异常
    SHM_CRASH_VH_UPD_SRC_ROW_FINISH,
    SHM_CRASH_VH_UPD_DEL_DST_ROW_BEFORE,  // 测试提交时删除旧的dst行/回滚时删除新的dst行前发生异常
    SHM_CRASH_VH_UPD_DEL_DST_ROW_AFTER,  // 测试提交时删除旧的dst行/回滚时删除新的dst行后发生异常
    SHM_CRASH_VH_UPD_DEL_DST_ROW_FINISH,  // 测试提交时删除旧的dst行/回滚时删除新的dst行已完成，测试重启恢复后重新提交/回滚
    SHM_CRASH_VH_UPD_ROLLBACK_TO_SRC_ROW_BEFORE,  // 测试更新src行到src行的回滚流程，回滚到旧src行前发生异常
    SHM_CRASH_VH_UPD_ROLLBACK_TO_SRC_ROW_AFTER,  // 测试更新src行到src行的回滚流程，回滚到旧src行后发生异常
    SHM_CRASH_VH_UPD_ROLLBACK_TO_SRC_ROW_FINISH,
    // VAR_HEAP_BATCH_INSERT
    SHM_CRASH_VH_BATCH_INS_UNDO_LOG_BEFORE,
    SHM_CRASH_VH_BATCH_INS_UNDO_LOG_AFTER,
    SHM_CRASH_VH_BATCH_INS_BEFORE,
    SHM_CRASH_VH_BATCH_INS_AFTER,
    SHM_CRASH_VH_BATCH_INS_RSM_UNDO_FINISH,
    SHM_CRASH_VH_BATCH_INS_MID_ROW_INS_AFTER,  // 测试插入完批量中的一半数据后重启
    SHM_CRASH_VH_BATCH_INS_ALL_ROW_INS_AFTER,  // 测试插入完批量中的所有数据后重启
    // HEAP_BATCH_DELETE
    SHM_CRASH_HP_BATCH_DEL_SEC_ROW_DEL_AFTER,  // 测试删除完批量中的第2条记录后重启
    SHM_CRASH_HP_BATCH_DEL_MID_ROW_DEL_AFTER,  // 测试删除完批量中的一半数据后重启
    SHM_CRASH_HP_BATCH_DEL_ALL_ROW_DEL_AFTER,  // 测试删除完批量中的所有数据后重启
    // trx流程相关测试
    SHM_CRASH_TRX_COMMIT_BEFORE,  // 测试事务提交前发生异常，预期恢复重启后事务自动回滚
    SHM_CRASH_TRX_COMMIT_AFTER,   // 测试事务提交后发生异常，预期恢复重启后事务自动提交
    SHM_CRASH_BG_TRX_COMMIT_BEFORE,  // 测试后台（缩容）事务提交前发生异常，预期恢复重启后事务自动回滚
    SHM_CRASH_BG_TRX_COMMIT_AFTER,  // 测试后台（缩容）事务提交后发生异常，预期恢复重启后事务自动提交
    SHM_CRASH_TRX_COMMIT_HANDLE_REC,  // 测试事务提交处理undo流程中发生异常，测试重复提交是否会有问题
    SHM_CRASH_TRX_ROLLBACK_HANDLE_REC,  // 测试事务回滚处理undo流程中发生异常，测试重复回滚是否会有问题
    // rsmBlock相关测试
    SHM_CRASH_BLOCK_ALLOC_BLOCK_BEFORE,
    SHM_CRASH_BLOCK_ALLOC_BLOCK_AFTER_KEY_CREATE,
    SHM_CRASH_BLOCK_ALLOC_BLOCK_AFTER_ALLOC_DEV,
    SHM_CRASH_BLOCK_ALLOC_BLOCK_AFTER_SET_SUB_BLOCK_DEV_PTR,
    SHM_CRASH_BLOCK_ALLOC_BLOCK_AFTER_SET_SUB_BLOCK_USED,
    SHM_CRASH_BLOCK_ALLOC_BLOCK_AFTER_SET_ALL_SUB_BLOCK,
    SHM_CRASH_BLOCK_ALLOC_BLOCK_AFTER_SET_DEV_FIRST_ID,
    SHM_CRASH_BLOCK_ALLOC_BLOCK_AFTER_SET_LAST_SUB_BLOCK_NEXT_ID,
    SHM_CRASH_BLOCK_ALLOC_BLOCK_AFTER_SET_BLOCK_FIRST_FREE_ID,
    SHM_CRASH_BLOCK_ALLOC_BLOCK_AFTER_SET_DEV_USED,
    SHM_CRASH_BLOCK_ALLOC_BLOCK_AFTER_ADD_BLOCK_CUR_CNT,
    SHM_CRASH_BLOCK_ALLOC_BLOCK_AFTER_SET_BLOCK_UESD,
    SHM_CRASH_BLOCK_ALLOC_BLOCK_AFTER_SUB_MGR_FREE_CNT,
    SHM_CRASH_BLOCK_ALLOC_BLOCK_AFTER,
    SHM_CRASH_BLOCK_ALLOC_PAGE_BEFORE,
    SHM_CRASH_BLOCK_ALLOC_PAGE_AFTER_SET_USED,
    SHM_CRASH_BLOCK_ALLOC_PAGE_AFTER_SET_NEW,
    SHM_CRASH_BLOCK_ALLOC_PAGE_AFTER_SUB_FREE_CNT,
    SHM_CRASH_BLOCK_ALLOC_PAGE_AFTER,
    SHM_CRASH_BLOCK_FREE_PAGE_BEFORE,
    SHM_CRASH_BLOCK_FREE_PAGE_AFTER_SET_FREE,
    SHM_CRASH_BLOCK_FREE_PAGE_AFTER_ADD_BLK_CNT,
    SHM_CRASH_BLOCK_FREE_PAGE_AFTER,
    SHM_CRASH_BLOCK_REUSE_DEV_BEFORE,
    SHM_CRASH_BLOCK_REUSE_DEV_AFTER_SUB_BIND_DEV_CNT,
    SHM_CRASH_BLOCK_REUSE_DEV_AFTER_SET_ALL_SUB_BLOCK_FREE,
    SHM_CRASH_BLOCK_REUSE_DEV_AFTER_SET_DEV_FREE_CNT,
    SHM_CRASH_BLOCK_REUSE_DEV_AFTER_SET_DEV_USED,
    SHM_CRASH_BLOCK_REUSE_DEV_AFTER,
    SHM_CRASH_BLOCK_ALLOC_DEV_BEFORE,
    SHM_CRASH_BLOCK_ALLOC_DEV_AFTER_ALLOC_DEV,
    SHM_CRASH_BLOCK_ALLOC_DEV_AFTER_SET_SUB_BLOCK_DEV_PTR,
    SHM_CRASH_BLOCK_ALLOC_DEV_AFTER_SET_SUB_BLOCK_USED,
    SHM_CRASH_BLOCK_ALLOC_DEV_AFTER_SET_ALL_SUB_BLOCK,
    SHM_CRASH_BLOCK_ALLOC_DEV_AFTER_SET_DEV_FIRST_ID,
    SHM_CRASH_BLOCK_ALLOC_DEV_AFTER_SET_LAST_SUB_BLOCK_NEXT_ID,
    SHM_CRASH_BLOCK_ALLOC_DEV_AFTER_SET_BLOCK_FIRST_FREE_ID,
    SHM_CRASH_BLOCK_ALLOC_DEV_AFTER_SET_DEV_USED,
    SHM_CRASH_BLOCK_ALLOC_DEV_AFTER_ADD_BLOCK_CUR_CNT,
    SHM_CRASH_BLOCK_ALLOC_DEV_AFTER,
    SHM_CRASH_BLOCK_MEMCPY_SUB_BLOCK_BEFORE,
    SHM_CRASH_BLOCK_MEMCPY_SUB_BLOCK_AFTER,
    SHM_CRASH_BLOCK_HANDLE_ENTRY_INFO_BEFORE,
    SHM_CRASH_BLOCK_INFO_GET_SUB_BLOCK_BEFORE,
    SHM_CRASH_BLOCK_INFO_GET_DEST_SUB_BLOCK,
    SHM_CRASH_BLOCK_INFO_GET_SRC_SUB_BLOCK,
    SHM_CRASH_BLOCK_INFO_CHANGE_DEST_LIST_FIRST_SUB_BLOCK,
    SHM_CRASH_BLOCK_INFO_CHANGE_SRC_LIST_FIRST_SUB_BLOCK,
    SHM_CRASH_BLOCK_INFO_CHANGE_SRC_SUB_BLOCK_DEV_ID,
    SHM_CRASH_BLOCK_INFO_CHANGE_DEST_SUB_BLOCK_DEV_ID,
    SHM_CRASH_BLOCK_INFO_CHANGE_DEST_SUB_BLOCK_BEGIN_POS,
    SHM_CRASH_BLOCK_INFO_CHANGE_DEST_SUB_BLOCK_OFFSET,
    SHM_CRASH_BLOCK_INFO_CHANGE_SRC_SUB_BLOCK_BEGIN_POS,
    SHM_CRASH_BLOCK_INFO_CHANGE_SRC_SUB_BLOCK_OFFSET,
    SHM_CRASH_BLOCK_INFO_CHANGE_DEST_SUB_BLOCK_FLAG,
    SHM_CRASH_BLOCK_INFO_CHANGE_SRC_SUB_BLOCK_FLAG,
    SHM_CRASH_BLOCK_INFO_CHANGE_DEST_DEV_FREE_CNT,
    SHM_CRASH_BLOCK_INFO_CHANGE_SRC_DEV_FREE_CNT,
    SHM_CRASH_BLOCK_HANDLE_ENTRY_INFO_AFTER,
    SHM_CRASH_BLOCK_FREE_DEV_BEFORE,
    SHM_CRASH_BLOCK_FREE_DEV_TO_OS_CHANGE_SUB_BLK_INFO,
    SHM_CRASH_BLOCK_FREE_DEV_TO_OS_CHANGE_SUB_BLK_STATUS,
    SHM_CRASH_BLOCK_FREE_DEV_CHANGE_SUB_BLK_STATUS,
    SHM_CRASH_BLOCK_FREE_DEV_TO_OS_CHANGE_BLOCK_FREE_ID,
    SHM_CRASH_BLOCK_FREE_DEV_TO_OS_CHANGE_DEV_FIRST_ID,
    SHM_CRASH_BLOCK_FREE_DEV_TO_OS_CHANGE_BLOCK_DEV_CNT,
    SHM_CRASH_BLOCK_FREE_DEV_TO_OS_CHANGE_DEV_STATUS,
    SHM_CRASH_BLOCK_FREE_DEV_CHANGE_BLOCK_DEV_CNT,
    SHM_CRASH_BLOCK_FREE_DEV_AFTER,
    SHM_CRASH_MIGRATION_BEFORE,
    SHM_CRASH_MIGRATION_EXTEND_BLOCK_INIT,
    SHM_CRASH_MIGRATION_TO_EXTEND_BLOCK,
    SHM_CRASH_MIGRATION_SET_BLOCK_DESC,
    SHM_CRASH_MIGRATION_FREE_BLOCK_AND_SET_FINISH,
    SHM_CRASH_MIGRATION_AFTER,
    // rsmSpace相关测试
    SHM_CRASH_SPACE_ALLOC_DEV_BEFORE,
    SHM_CRASH_SPACE_ALLOC_DEV_AFTER_ALLOC_DEV,
    SHM_CRASH_SPACE_ALLOC_DEV_AFTER_ADD_CHUNK_CNT,
    SHM_CRASH_SPACE_ALLOC_DEV_AFTER_ADD_DEV_SIZE,
    SHM_CRASH_SPACE_ALLOC_DEV_AFTER,
    SHM_CRASH_SPACE_ALLOC_PAGE_BEFORE,
    SHM_CRASH_SPACE_ALLOC_PAGE_AFTER_ALLOC_ENTRY,
    SHM_CRASH_SPACE_ALLOC_PAGE_AFTER_ADD_SUB_BLK_CNT,
    SHM_CRASH_SPACE_ALLOC_PAGE_AFTER,
    SHM_CRASH_SPACE_FREE_PAGE_BEFORE,
    SHM_CRASH_SPACE_FREE_PAGE_AFTER_FREE_ENTRY,
    SHM_CRASH_SPACE_FREE_PAGE_AFTER_SUB_SUB_BLK_CNT,
    SHM_CRASH_SPACE_FREE_PAGE_AFTER,
    SHM_CRASH_SPACE_INIT_PAGE_BEFORE,
    SHM_CRASH_SPACE_INIT_PAGE_AFTER_LOCK,
    SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_CHECK_SUM,
    SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_BEGIN_POS,
    SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_END_POS,
    SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_ADDR,
    SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_ENTRY_USED_NUM,
    SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_PAGE_STATE,
    SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_TRM_ID,
    SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_LSN,
    SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_NEXT_PAGE_ID,
    SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_COMPRESS_STATE,
    SHM_CRASH_SPACE_INIT_PAGE_AFTER_MEMSET_RESERVE,
    SHM_CRASH_SPACE_INIT_PAGE_AFTER_UNLOCK,
    SHM_CRASH_SPACE_INIT_PAGE_AFTER,
    SHM_CRASH_SPACE_RESET_PAGE_BEFORE,
    SHM_CRASH_SPACE_RESET_PAGE_AFTER_SET_INVALID_TRM_ID,
    SHM_CRASH_SPACE_RESET_PAGE_AFTER_SET_UNINIT,
    SHM_CRASH_SPACE_RESET_PAGE_AFTER_SET_DEVICE_ID,
    SHM_CRASH_SPACE_RESET_PAGE_AFTER_SET_BLOCK_ID,
    SHM_CRASH_SPACE_RESET_PAGE_AFTER,
    SHM_CRASH_SPACE_FREE_DEV_BEFORE,
    SHM_CRASH_SPACE_FREE_DEV_FINISHED,
    SHM_CRASH_SPACE_FREE_DEV_UPDATE_DEV_SIZE,
    SHM_CRASH_SPACE_FREE_DEV_UPDATE_SUB_BLOCK_CNT,
    SHM_CRASH_SPACE_FREE_DEV_UPDATE_SUB_BLOCK_MOVE_CNT,
    SHM_CRASH_SPACE_FREE_DEV_AFTER,
    // 写缓存相关测试
    SHM_CRASH_WC_SET_REQ_BEFORE,
    SHM_CRASH_WC_SET_REQ_AFTER_REQ_LOCK,
    SHM_CRASH_WC_SET_REQ_AFTER_ALLOC_NEW_PAGE,
    SHM_CRASH_WC_SET_REQ_AFTER_INIT_NEW_PAGE,
    SHM_CRASH_WC_SET_REQ_AFTER_LOG_NEXT_PAGE_ID,
    SHM_CRASH_WC_SET_REQ_AFTER_INSERT_NEW_PAGE,
    SHM_CRASH_WC_SET_REQ_AFTER_LEAVE_CREATE_PAGE_LOG,
    SHM_CRASH_WC_SET_REQ_AFTER_INC_PAGE_NUM,
    SHM_CRASH_WC_SET_REQ_AFTER_LOG_WRITE_PAGE_ID,
    SHM_CRASH_WC_SET_REQ_AFTER_UPDATE_WRITE_PAGE,
    SHM_CRASH_WC_SET_REQ_AFTER_LEAVE_WRITE_PAGE_LOG,
    SHM_CRASH_WC_SET_REQ_AFTER_WRITE_PAGE_LOCK,
    SHM_CRASH_WC_SET_REQ_AFTER_COPY_REQ,
    SHM_CRASH_WC_SET_REQ_AFTER_LOG_FREE_OFFSET,
    SHM_CRASH_WC_SET_REQ_AFTER_UPDATE_FREE_OFFSET,
    SHM_CRASH_WC_SET_REQ_AFTER_LEAVE_SET_REQ_LOG,
    SHM_CRASH_WC_SET_REQ_AFTER,
    SHM_CRASH_WC_MERGE_BEFORE,
    SHM_CRASH_WC_MERGE_AFTER_ADD_REQ,
    SHM_CRASH_WC_MERGE_AFTER_MERGE_OPCODE,
    SHM_CRASH_WC_MERGE_AFTER_LOG_MERGED_REQ_NUM,
    SHM_CRASH_WC_MERGE_AFTER_INC_MERGED_REQ_NUM,
    SHM_CRASH_WC_MERGE_AFTER_LEAVE_MERGED_REQ_NUM_LOG,
    SHM_CRASH_WC_MERGE_AFTER_PAGE_LOCK,
    SHM_CRASH_WC_MERGE_AFTER_LOG_RESET_PAGE,
    SHM_CRASH_WC_MERGE_AFTER_INIT_FREE_OFFSET,
    SHM_CRASH_WC_MERGE_AFTER_INIT_MERGE_BEGIN_OFFSET,
    SHM_CRASH_WC_MERGE_AFTER_RESET_MERGED_REQ_NUM_WHEN_FREE,
    SHM_CRASH_WC_MERGE_AFTER_LEAVE_RESET_PAGE_LOG,
    SHM_CRASH_WC_MERGE_AFTER_LOG_NEW_MERGE_PAGE_ID,
    SHM_CRASH_WC_MERGE_AFTER_UPDATE_MERGE_PAGE,
    SHM_CRASH_WC_MERGE_AFTER_LEAVE_UPDATE_MERGE_PAGE_LOG,
    SHM_CRASH_WC_MERGE_AFTER_LOG_MERGE_BEGIN_OFFSET,
    SHM_CRASH_WC_MERGE_AFTER_UPDATE_MERGE_BEGIN_OFFSET,
    SHM_CRASH_WC_MERGE_AFTER_RESET_MERGED_REQ_NUM_WHEN_ON_WRITE,
    SHM_CRASH_WC_MERGE_AFTER_LEAVE_MERGE_BEGIN_OFFSET_LOG,
    SHM_CRASH_WC_MERGE_AFTER,
    SHM_CRASH_WC_SCALE_IN_BEFORE,
    SHM_CRASH_WC_SCALE_IN_AFTER_REQ_LOCK,
    SHM_CRASH_WC_SCALE_IN_AFTER_LOG_DELETE_PAGE,
    SHM_CRASH_WC_SCALE_IN_AFTER_UPDATE_NEXT_PAGE_ID,
    SHM_CRASH_WC_SCALE_IN_AFTER_LEAVE_DELETE_PAGE_LOG,
    SHM_CRASH_WC_SCALE_IN_AFTER_DEC_PAGE_NUM,
    SHM_CRASH_WC_SCALE_IN_AFTER_FREE_PAGE,
    SHM_CRASH_WC_SCALE_IN_AFTER,
    // rsm-tsp相关测试
    SHM_CRASH_RSM_TSP_INFO_LOCK,       // 测试新建tsp时，刚加上tableInfo的锁就挂掉的场景
    SHM_CRASH_RSM_TSP_INFO_ALLOC_MEM,  // 测试新建tsp时，申请item内存完后挂掉，测试由对账完成回收
    SHM_CRASH_RSM_TSP_INFO_INSERT_BEFORE,  // 测试新建tsp时，记了rsmUndo后挂掉
    SHM_CRASH_RSM_TSP_INFO_INSERT_AFTER,   // 测试新建tsp时，完成修改后挂掉
    SHM_CRASH_RSM_TSP_INFO_INSERT_FINISH,  // 测试新建tsp时，完成tableInfo修改后、回退rsmUndo后挂掉
    SHM_CRASH_RSM_TSP_INFO_FREE_MEM,  // 测试删除tsp时，释放item内存完后挂掉，测试不会重复回收
    SHM_CRASH_RSM_TSP_INFO_REMOVE_BEFORE,  // 测试删除tsp时，记了rsmUndo后挂掉
    SHM_CRASH_RSM_TSP_INFO_REMOVE_AFTER,   // 测试删除tsp时，完成修改后挂掉
    SHM_CRASH_RSM_TSP_INFO_REMOVE_FINISH,  // 测试删除tsp时，完成tableInfo修改后、回退rsmUndo后挂掉
    // 升级相关测试
    SHM_CRASH_FH_UPGRADE_BEFORE,
    SHM_CRASH_FH_UPGRADE_AFTER,
    SHM_CRASH_TB_UPGRADE_BEFORE,
    SHM_CRASH_TB_UPGRADE_AFTER,
    SHM_CRASH_TB_UPGRADE_FINISH,
    SHM_CRASH_FH_UPGRADE_MEMMOVE_BEFORE,
    SHM_CRASH_FH_UPGRADE_MEMMOVE_AFTER,
    SHM_CRASH_FH_SET_NOT_UPG,
    SHM_CRASH_FH_SET_UPG,
    // 降级相关测试
    SHM_CRASH_BG_DOWNGRADE_CFG_CHANGE_BEFORE,  // 测试后台降级2阶段，修改heapCfg前挂掉的场景
    SHM_CRASH_BG_DOWNGRADE_CFG_CHANGE_AFTER,   // 测试后台降级2阶段，修改heapCfg后挂掉的场景
    SHM_CRASH_BG_DOWNGRADE_MEMMOVE_BEFORE,     // 测试后台降级2阶段，进行页面整理前挂掉的场景
    SHM_CRASH_BG_DOWNGRADE_MEMMOVE_AFTER,      // 测试后台降级2阶段，进行页面整理后挂掉的场景
    SHM_CRASH_FG_DOWNGRADE_TBINFO_BEFORE,  // 测试降级前台任务，修改tableInfo中版本数量前挂掉的场景
    SHM_CRASH_FG_DOWNGRADE_TBINFO_AFTER,  // 测试降级前台任务，修改tableInfo中版本数量前挂掉的场景
    SHM_CRASH_FG_DOWNGRADE_TASK_BEFORE,   // 测试降级前台任务，修改后台任务信息前挂掉的场景
    SHM_CRASH_FG_DOWNGRADE_TASK_AFTER,    // 测试降级前台任务，修改后台任务信息后挂掉的场景
    SHM_CRASH_FG_DOWNGRADE_TASK_FINISH,  // 测试降级前台任务，tableInfo版本数量、后台任务信息一致性问题
    // 对账老化相关测试
    SHM_CRASH_REFRESH_CHECK_VERSION_BEFORE,
    SHM_CRASH_REFRESH_CHECK_VERSION_BEFORE_SAVE,
    SHM_CRASH_REFRESH_CHECK_VERSION_AFTER_SAVE,
    SHM_CRASH_REFRESH_CHECK_VERSION_AFTER,
    SHM_CRASH_UPDATE_MIN_RECOVERY_BEFORE,
    SHM_CRASH_UPDATE_MIN_RECOVERY_AFTER,
    SHM_CRASH_UPDATE_CHECK_STATUS_BEFORE,
    SHM_CRASH_UPDATE_CHECK_STATUS_AFTER,
    SHM_CRASH_RESET_OLD_VERSION_AFTER,
    SHM_CRASH_CH_INSERT_BEFORE,
    SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_BITMAP,
    SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_CNT,
    SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_PREV_BITMAP,
    SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_LOCAL_ENTRY_CNT,
    SHM_CRASH_CH_INSERT_ALLOC_ENTRY,
    SHM_CRASH_CH_STASH_BUCKET_ALLOC_UPDATE_BITMAP,
    SHM_CRASH_CH_STASH_BUCKET_ALLOC_UPDATE_CNT,
    SHM_CRASH_CH_INSERT_ALLOC_STASH_ENTRY,
    SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_FREE_SLOT_ID,
    SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_FREE_SLOT_CNT,
    SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_USED,
    SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_BITMAP,
    SHM_CRASH_CH_INSERT_ALLOC_TUPLE_BEFORE_WRITE,
    SHM_CRASH_CH_ENTRY_INIT_HASHCODE,
    SHM_CRASH_CH_ENTRY_INIT_LOGIC_SLOT,
    SHM_CRASH_CH_ENTRY_INIT_PHY_SLOT,
    SHM_CRASH_CH_ENTRY_INIT_VERSION,
    SHM_CRASH_CH_INSERT_WRITE_ENTRY,
    SHM_CRASH_CH_INSERT_WRITE_TUPLE,
    SHM_CRASH_CH_STASHED_ALLOC_UPDATE_BITMAP,
    SHM_CRASH_CH_STASHED_ALLOC_UPDATE_CNT,
    SHM_CRASH_CH_INSERT_FINISHED,
    SHM_CRASH_CH_INSERT_DISPLACE_BEFORE,
    SHM_CRASH_CH_INSERT_DISPLACE_COPY,
    SHM_CRASH_CH_BUCKET_FREE_UPDATE_BITMAP,
    SHM_CRASH_CH_BUCKET_FREE_UPDATE_CNT,
    SHM_CRASH_CH_BUCKET_FREE_UPDATE_PREV_BITMAP,
    SHM_CRASH_CH_BUCKET_FREE_UPDATE_LOCAL_ENTRY_CNT,
    SHM_CRASH_CH_ENTRY_CLEAR_HASHCODE,
    SHM_CRASH_CH_ENTRY_CLEAR_LOGIC_SLOT,
    SHM_CRASH_CH_ENTRY_CLEAR_PHY_SLOT,
    SHM_CRASH_CH_ENTRY_CLEAR_VERSION,
    SHM_CRASH_CH_INSERT_DISPLACE_FINISHED,
    SHM_CRASH_CH_BATCH_REPLACE_WITHOUT_UPDATE_CNT,
    SHM_CRASH_CH_DELETE_BEFORE,
    SHM_CRASH_CH_DELETE_CLEAR_BUCKET,
    SHM_CRASH_CH_DELETE_CLEAR_ENTRY,
    SHM_CRASH_CH_STASHED_FREE_UPDATE_BITMAP,
    SHM_CRASH_CH_STASHED_FREE_UPDATE_CNT,
    SHM_CRASH_CH_STASH_BUCKET_FREE_UPDATE_BITMAP,
    SHM_CRASH_CH_STASH_BUCKET_FREE_UPDATE_CNT,
    SHM_CRASH_CH_TUPLE_FREE_UPDATE_BITMAP,
    SHM_CRASH_CH_TUPLE_FREE_UPDATE_NEXT_SLOT_ID,
    SHM_CRASH_CH_TUPLE_FREE_UPDATE_FREE_SLOT_ID,
    SHM_CRASH_CH_TUPLE_FREE_UPDATE_FREE_SLOT_COUNT,
    SHM_CRASH_CH_TUPLE_FREE_UPDATE_USED,
    SHM_CRASH_CH_DELETE_FINISHED,
    SHM_CRASH_CH_UPDATE_BEFORE,
    SHM_CRASH_CH_UPDATE_CHANGE_TRX_ID,
    SHM_CRASH_CH_UPDATE_CHANGE_BUFFER_SIZE,
    SHM_CRASH_CH_UPDATE_CHANGE_ROW,
    SHM_CRASH_CH_UPDATE_FINISHED,
    SHM_CRASH_CH_UPDATE_MULVERSION_BEFORE,
    SHM_CRASH_CH_UPDATE_MULVERSION_NO_CHANGE,
    SHM_CRASH_CH_UPDATE_MULVERSION_BEFORE_DELETE,
    SHM_CRASH_CH_UPDATE_MULVERSION_DELETE,
    SHM_CRASH_CH_UPDATE_MULVERSION_BEFORE_INSERT,
    SHM_CRASH_CH_UPDATE_MULVERSION_INSERT,
    SHM_CRASH_CH_UPDATE_MULVERSION_FINISHED,
    SHM_CRASH_CH_UPDATE_ROLLBACK_BEFORE,
    SHM_CRASH_CH_UPDATE_ROLLBACK_FINISHED,
    SHM_CRASH_CH_UPDATE_ROLLBACK_MULVERSION_BEFORE,
    SHM_CRASH_CH_UPDATE_ROLLBACK_MULVERSION_DELETE,
    SHM_CRASH_CH_UPDATE_ROLLBACK_MULVERSION_UPDATE_STAT,
    SHM_CRASH_CH_UPDATE_ROLLBACK_MULVERSION_INSERT,
    SHM_CRASH_CH_UPDATE_ROLLBACK_MULVERSION_FINISHED,
    SHM_CRASH_CH_RECYCLE_PAGE_BEFORE,
    SHM_CRASH_CH_RECYCLE_PAGE_FREE_PAGE,
    SHM_CRASH_CH_RECYCLE_PAGE_UPDATE_TYPE,
    SHM_CRASH_CH_RECYCLE_PAGE_UPDATE_PAGE_ADDR,
    SHM_CRASH_CH_RECYCLE_PAGE_UPDATE_SEG_DEPTH,
    SHM_CRASH_CH_RECYCLE_PAGE_UPDATE_OTHER_SEG,
    SHM_CRASH_CH_RECYCLE_PAGE_UPDATE_SEG_CNT,
    SHM_CRASH_CH_RECYCLE_PAGE_FINISHED,
    SHM_CRASH_CH_MUL_VERSION_RECYCLE_FIRST_PAGE_BEFORE,
    SHM_CRASH_CH_MUL_VERSION_RECYCLE_FIRST_PAGE_CHANGE_ALL_SEG,
    SHM_CRASH_CH_MUL_VERSION_RECYCLE_FIRST_PAGE_CHANGE_CUR_SEG,
    SHM_CRASH_CH_MUL_VERSION_RECYCLE_FIRST_PAGE_FREE_PAGE_BEFORE,
    SHM_CRASH_CH_MUL_VERSION_RECYCLE_FIRST_PAGE_FREE_PAGE,
    SHM_CRASH_CH_MUL_VERSION_RECYCLE_FIRST_PAGE_FINISHED,
    SHM_CRASH_CH_MUL_VERSION_RECYCLE_MIDDLE_PAGE_BEFORE,
    SHM_CRASH_CH_MUL_VERSION_RECYCLE_MIDDLE_PAGE_GET_PRE_ADDR,
    SHM_CRASH_CH_MUL_VERSION_RECYCLE_MIDDLE_PAGE_CHANGE_SEG,
    SHM_CRASH_CH_MUL_VERSION_RECYCLE_MIDDLE_PAGE_FREE_PAGE,
    SHM_CRASH_CH_MUL_VERSION_RECYCLE_MIDDLE_PAGE_FINISHED,
    SHM_CRASH_CH_MUL_VERSION_RECYCLE_LAST_PAGE_BEFORE,
    SHM_CRASH_CH_MUL_VERSION_RECYCLE_LAST_PAGE_GET_PRE_ADDR,
    SHM_CRASH_CH_MUL_VERSION_RECYCLE_LAST_PAGE_CHANGE_SEG,
    SHM_CRASH_CH_MUL_VERSION_RECYCLE_LAST_PAGE_FREE_PAGE,
    SHM_CRASH_CH_MUL_VERSION_RECYCLE_LAST_PAGE_FINISHED,
    SHM_CRASH_CH_SCALEIN_DIR_BEFORE,
    SHM_CRASH_CH_SCALEIN_DIR_FREE_DIR_PAGE,
    SHM_CRASH_CH_SCALEIN_DIR_FREE_DIR_PAGE_FINISHED,
    SHM_CRASH_CH_SCALEIN_DIR_UPDATE_DIR_PAGE_CNT,
    SHM_CRASH_CH_SCALEIN_DIR_UPDATE_DIR_CAP,
    SHM_CRASH_CH_SCALEIN_DIR_FINISHED,
    SHM_CRASH_CH_EXPAND_BEFORE,
    SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_BEFORE,
    SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE,
    SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE,
    SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE_COUNT,
    SHM_CRASH_CH_EXPAND_ALLOC_NEXT_SEGPAGE_BEFORE,
    SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_AFTER,
    SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_BEFORE,
    SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_SEG_DEPTH,
    SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_AFTER,
    SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_BEFORE,
    SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_AFTER,
    SHM_CRASH_CH_EXPAND_MEMCPY,
    SHM_CRASH_CH_PROC_BUCKET_UPDATE_BITMAP,
    SHM_CRASH_CH_PROC_BUCKET_CLEAR_ENTRY,
    SHM_CRASH_CH_PROC_BUCKET_UPDATE_ALLOC_BITMAP,
    SHM_CRASH_CH_PROC_BUCKET_UPDATE_LOCAL_ENTRY_CNT,
    SHM_CRASH_CH_PROC_BUCKET_UPDATE_PREV_BITMAP,
    SHM_CRASH_CH_PROC_BUCKET_UPDATE_CNT,
    SHM_CRASH_CH_PROC_STASHED_UPDATE_BITMAP,
    SHM_CRASH_CH_PROC_STASHED_CLEAR_ENTRY,
    SHM_CRASH_CH_PROC_STASHED_UPDATE_ALLOC_BIMAP,
    SHM_CRASH_CH_PROC_STASHED_UPDATE_CNT,
    SHM_CRASH_CH_PROC_STASHED_UPDATE_STASH_BITMAP,
    SHM_CRASH_CH_PROC_STASHED_UPDATE_STASH_CNT,
    SHM_CRASH_CH_EXPAND_RESET_FREE_SLOT_CNT,
    SHM_CRASH_CH_EXPAND_RESET_FREE_SLOT_ID,
    SHM_CRASH_CH_EXPAND_UPDATE_FREE_SLOT_CNT,
    SHM_CRASH_CH_EXPAND_UPDATE_NEXT_SLOT_ID,
    SHM_CRASH_CH_EXPAND_UPDATE_FREE_SLOT_ID,
    SHM_CRASH_CH_EXPAND_PROC_OLD_PAGE,
    SHM_CRASH_CH_EXPAND_PROC_NEW_PAGE,
    SHM_CRASH_CH_EXPAND_PROC_NEXT_PAGE,
    SHM_CRASH_CH_EXPAND_SEG_SPLIT_END,
    SHM_CRASH_CH_EXPAND_FRESH_DIR_BEFORE,
    SHM_CRASH_CH_EXPAND_FRESH_DIR_HALF,
    SHM_CRASH_CH_EXPAND_FRESH_DIR_MAP_RECURSION_BEFORE,
    SHM_CRASH_CH_EXPAND_FRESH_DIR_MAP_RECURSION_HALF,
    SHM_CRASH_CH_EXPAND_FRESH_DIR_MAP_RECURSION_AFTER,
    SHM_CRASH_CH_EXPAND_FRESH_DIR_AFTER,
    SHM_CRASH_CH_EXPAND_DOUBLE_DIR_BEFORE,
    SHM_CRASH_CH_EXPAND_DOUBLE_DIR_ALLOC_PAGE,
    SHM_CRASH_CH_EXPAND_DOUBLE_DIR_RECORD_PAGE_COUNT,
    SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_OLD_DIR,
    SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_NEW_DIR,
    SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_DIR_DEPTH,
    SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_DIR_CAP,
    SHM_CRASH_CH_EXPAND_DOUBLE_DIR_FAIL,
    SHM_CRASH_CH_EXPAND_ROLLBACK_FREE_SEG,
    SHM_CRASH_CH_EXPAND_ROLLBACK_UPDATE_SEG_CNT,
    SHM_CRASH_CH_EXPAND_ROLLBACK_UPDATE_DIR_DEPTH,
    SHM_CRASH_CH_EXPAND_ROLLBACK_UPDATE_DIR_PATTERN,
    SHM_CRASH_CH_EXPAND_DOUBLE_DIR_AFTER,
    SHM_CRASH_CH_EXPAND_FINISHED,
    SHM_CRASH_CH_MUL_VERSION_EXPAND_BEFORE,
    SHM_CRASH_CH_MUL_VERSION_EXPAND_SET_CHAIN,
    SHM_CRASH_CH_MUL_VERSION_EXPAND_ADD_LOCK,
    SHM_CRASH_CH_MUL_VERSION_EXPAND_UPDATE_SEG_DEVICE_ID,
    SHM_CRASH_CH_MUL_VERSION_EXPAND_UPDATE_SEG_BLOCK_ID,
    SHM_CRASH_CH_MUL_VERSION_EXPAND_UPDATE_DIR,
    SHM_CRASH_CH_MUL_VERSION_EXPAND_SET_OUTPUT,
    SHM_CRASH_CH_MUL_VERSION_EXPAND_FINISHED,
    SHM_CRASH_CH_SCALE_IN_FREE_EMPTY_PAGE_BEFORE,
    SHM_CRASH_CH_SCALE_IN_FREE_EMPTY_PAGE,
    SHM_CRASH_CH_SCALE_IN_UPDATE_DIR_BEFORE,
    SHM_CRASH_CH_SCALE_IN_UPDATE_PATTERN,
    SHM_CRASH_CH_SCALE_IN_UPDATE_SEG_DEPTH,
    SHM_CRASH_CH_SCALE_IN_FREE_EMPTY_PAGE_END,
    SHM_CRASH_CH_VERTICAL_SCALE_IN_BEFORE,
    SHM_CRASH_CH_VERTICAL_SCALE_IN_MERGE_HALF,
    SHM_CRASH_CH_VERTICAL_SCALE_IN_MERGE_BUCKET,
    SHM_CRASH_CH_VERTICAL_SCALE_IN_MERGE_STASH_BUCKET,
    SHM_CRASH_CH_VERTICAL_SCALE_IN_END,
    SHM_CRASH_CH_HORIZONTAL_SCALE_IN_BEFORE,
    SHM_CRASH_CH_HORIZONTAL_SCALE_IN_MERGE_HALF,
    SHM_CRASH_CH_HORIZONTAL_SCALE_IN_EXIT,
    SHM_CRASH_CH_HORIZONTAL_SCALE_IN_MERGE_BUCKET,
    SHM_CRASH_CH_HORIZONTAL_SCALE_IN_MERGE_STASH_BUCKET,
    SHM_CRASH_CH_HORIZONTAL_SCALE_IN_END,
    SHM_CRASH_CH_MUL_VERSION_SCALE_IN_BEFORE,
    SHM_CRASH_CH_MUL_VERSION_SCALE_IN_PART_SUCC,
    SHM_CRASH_CH_MUL_VERSION_SCALE_IN_RECORD_COUNT,
    SHM_CRASH_CH_MUL_VERSION_SCALE_IN_HALF,
    SHM_CRASH_CH_MUL_VERSION_SCALE_IN_UPDATE_PAGE_CNT,
    SHM_CRASH_CH_MUL_VERSION_SCALE_IN_UPDATE_PAGE_CHAIN,
    SHM_CRASH_CH_MUL_VERSION_SCALE_IN_FREE_PAGE_CHAIN_BEFORE,
    SHM_CRASH_CH_MUL_VERSION_SCALE_IN_FREE_PAGE_CHAIN_HALF,
    SHM_CRASH_CH_MUL_VERSION_SCALE_IN_FREE_PAGE_CHAIN_UPDATE_COUNT,
    SHM_CRASH_CH_MUL_VERSION_SCALE_IN_UPDATE_DIR_BEFORE,
    SHM_CRASH_CH_MUL_VERSION_SCALE_IN_ROLLBACK_BEFORE,
    SHM_CRASH_CH_MUL_VERSION_SCALE_IN_ROLLBACK_SRC_PAGE,
    SHM_CRASH_CH_MUL_VERSION_SCALE_IN_ROLLBACK_DST_PAGE,
    SHM_CRASH_CH_MUL_VERSION_SCALE_IN_ROLLBACK_UPDATE_COUNT,
    SHM_CRASH_CH_MUL_VERSION_SCALE_IN_FINISHED,
    SHM_CRASH_CH_MUL_VERSION_SCALE_IN_END,
    SHM_CRASH_MEMCTX_ALLOC_SET_CHUNK_USED_BEFORE,
    SHM_CRASH_MEMCTX_ALLOC_SET_CHUNK_USED_AFTER,
    SHM_CRASH_MEMCTX_ALLOC_SET_SEGMENT_OFFSET,
    SHM_CRASH_MEMCTX_ALLOC_INIT_SEGMENT_BEFORE,
    SHM_CRASH_MEMCTX_ALLOC_INIT_SEGMENT_AFTER,
    SHM_CRASH_MEMCTX_ALLOC_SET_ALGO_BEFORE,
    SHM_CRASH_MEMCTX_ALLOC_SET_TOTAL_ALLOC_SIZE_BEFORE,
    SHM_CRASH_MEMCTX_ALLOC_SET_ALGO_AFTER,
    SHM_CRASH_MEMCTX_FREE_BEFORE,
    SHM_CRASH_MEMCTX_FREE_AFTER,
    SHM_CRASH_MEMCTX_CREATE_BEFORE,
    SHM_CRASH_MEMCTX_CREATE_AFTER,
    SHM_CRASH_DDL_CREATE_VERTEX_BEGIN,
    SHM_CRASH_DDL_CREATE_VERTEX_INIT_LATCH,
    SHM_CRASH_DDL_CREATE_VERTEX_CREATE_RSM_INFO,
    SHM_CRASH_DDL_CREATE_VERTEX_SET_CONTAINER_INFO,
    SHM_CRASH_DDL_CREATE_VERTEX_CREATE_CONTAINER,
    SHM_CRASH_DDL_CREATE_VERTEX_CREATE_INDEX,
    SHM_CRASH_DDL_CREATE_VERTEX_CREATE_WRITE_CACHE,
    SHM_CRASH_DDL_CREATE_VERTEX_END,
    SHM_CRASH_DDL_CREATE_KV_BEGIN,
    SHM_CRASH_DDL_CREATE_KV_INIT_LATCH,
    SHM_CRASH_DDL_CREATE_KV_CREATE_RSM_INFO,
    SHM_CRASH_DDL_CREATE_KV_CREATE_HEAP,
    SHM_CRASH_DDL_CREATE_KV_INIT_INDEX,
    SHM_CRASH_DDL_CREATE_KV_END,
    SHM_CRASH_DDL_DROP_VERTEX_BEGIN,
    SHM_CRASH_DDL_DROP_VERTEX_DROP_CONTAINER,
    SHM_CRASH_DDL_DROP_VERTEX_DROP_INDEX,
    SHM_CRASH_DDL_DROP_VERTEX_DROP_WRITE_CACHE,
    SHM_CRASH_DDL_DROP_VERTEX_FREE_RSM_INFO,
    SHM_CRASH_DDL_DROP_VERTEX_END,
    SHM_CRASH_DDL_DROP_KV_BEGIN,
    SHM_CRASH_DDL_DROP_KV_DROP_HEAP,
    SHM_CRASH_DDL_DROP_KV_FREE_RSM_INFO,
    SHM_CRASH_DDL_DROP_KV_DROP_INDEX,
    SHM_CRASH_DDL_DROP_KV_END,
    SHM_CRASH_DDL_TRUNCATE_VERTEX_BEGIN,
    SHM_CRASH_DDL_TRUNCATE_VERTEX_TRUNC_CH_CACHE_ENABLE,
    SHM_CRASH_DDL_TRUNCATE_VERTEX_TRUNC_CH_FREE_SEG_PAGE,
    SHM_CRASH_DDL_TRUNCATE_VERTEX_TRUNC_CH_FREE_DIR_PAGE,
    SHM_CRASH_DDL_TRUNCATE_VERTEX_TRUNC_CH_RESET_DATA,
    SHM_CRASH_DDL_TRUNCATE_VERTEX_TRUNC_CH_CONSTRUCT_DIR,
    SHM_CRASH_DDL_TRUNCATE_HEAP_RESET_INFO,
    SHM_CRASH_DDL_TRUNCATE_HEAP_FREE_BLOCK,
    SHM_CRASH_DDL_TRUNCATE_HEAP_RESET_FSM,
    SHM_CRASH_DDL_TRUNCATE_HEAP_INIT_STAT,
    SHM_CRASH_DDL_TRUNCATE_HEAP_RECORD_TRX_ID,
    SHM_CRASH_DDL_TRUNCATE_VERTEX_TRUNC_CONTAINER,
    SHM_CRASH_DDL_TRUNCATE_VERTEX_TRUNC_PK_INDEX,
    SHM_CRASH_DDL_TRUNCATE_VERTEX_TRUNC_SEC_INDEX,
    SHM_CRASH_DDL_TRUNCATE_VERTEX_RESET_AUTO_INC,
    SHM_CRASH_DDL_TRUNCATE_VERTEX_UPDATE_CHECK_INFO,
    SHM_CRASH_DDL_TRUNCATE_VERTEX_END,
    SHM_CRASH_DDL_TRUNCATE_KV_BEGIN,
    SHM_CRASH_DDL_TRUNCATE_KV_TRUNC_CONTAINER,
    SHM_CRASH_DDL_TRUNCATE_KV_TRUNC_INDEX,
    SHM_CRASH_DDL_TRUNCATE_KV_END,
    SHM_CRASH_DDL_ALTER_CONFIG_BEGIN,
    SHM_CRASH_DDL_ALTER_CONFIG_CHANGE_HEAP_BEFORE,
    SHM_CRASH_DDL_ALTER_CONFIG_CHANGE_HEAP_AFTER,
    SHM_CRASH_DDL_ALTER_CONFIG_CHANGE_CLUSTERED_HASH_BEFORE,
    SHM_CRASH_DDL_ALTER_CONFIG_CHANGE_CLUSTERED_HASH_AFTER,
    SHM_CRASH_DDL_ALTER_CONFIG_SET_ITEM_NUM_FINISH,
    SHM_CRASH_DDL_ALTER_CONFIG_END,
    SHM_CRASH_DDL_DUPLICATE_LABEL_BEFORE_FINISH,
    SHM_CRASH_DDL_CHANGE_HEAP_FIX_ROW_SIZE_BEFORE,
    SHM_CRASH_DDL_CHANGE_HEAP_FIX_ROW_SIZE_AFTER,
    SHM_CRASH_END,
} DbShmCrashPointTypeT;

#endif

#ifdef __cplusplus
}
#endif /* __cplusplus */
#endif
