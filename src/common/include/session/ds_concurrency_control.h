/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: The entry of direct read concurrency control
 * Author: baiyang
 * Create: 2022-03-08
 */
#ifndef DIRECT_READ_CONCURRENCY_CONTROL_H
#define DIRECT_READ_CONCURRENCY_CONTROL_H
#include "db_label_latch_mgr.h"

#ifdef __cplusplus
extern "C" {
#endif

// 对外接口
GMDB_EXPORT void DsReleaseLatchForDirectRead(LabelRWLatchT *labelRWLatch, DbSessionCtxT *ctx);

GMDB_EXPORT Status DsAcqLatchForDirectRead(
    LabelRWLatchT *labelRWLatch, uint32_t labelLatchVersionId, DbSessionCtxT *ctx);

GMDB_EXPORT void DsAcqHcLatchForDirectRead(LabelRWLatchT *labelRWLatch, DbSessionCtxT *ctx);
GMDB_EXPORT void DsReleaseHcLatchForDirectRead(LabelRWLatchT *labelRWLatch, DbSessionCtxT *ctx);

#ifdef __cplusplus
}
#endif
#endif /* DIRECT_READ_CONCURRENCY_CONTROL_H */
