/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: db_bit.h
 * Description: header file for bits
 * Author:
 * Create: 2024-1-15
 */

#ifndef DB_BIT_H
#define DB_BIT_H

#include <stdint.h>
#include <stdbool.h>
#include "adpt_define.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifndef NDEBUG
inline static void BitMapCheck(uint32_t bitmap, uint32_t n)
{
    uint32_t val = bitmap;
    uint32_t loop = 0;
    while (val != 0) {
        loop++;
        val &= (val - 1);
    }
    DB_ASSERT(loop == n);
}
#endif

inline static uint32_t BitMapSet(uint32_t bitmap, uint32_t n)
{
    return (bitmap |= ((uint32_t)1 << n));
}

inline static uint32_t BitMapUnSet(uint32_t bitmap, uint32_t n)
{
    return (bitmap &= ~((uint32_t)1 << n));
}

inline static bool BitMapIsSet(uint32_t bitmap, uint32_t n)
{
    return (bitmap & ((uint32_t)1 << n)) != 0;
}

#ifdef __cplusplus
}
#endif

#endif  // DB_BIT_H
