/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: sysview.h
 * Description: header file for system view
 * Author:
 * Create: 2020-09-01
 */

#ifndef SYSVIEW_H
#define SYSVIEW_H
#include "db_hashmap.h"
#include "adpt_spinlock.h"
#include "adpt_define.h"
#include "adpt_types.h"
#include "db_mem_context.h"
#include "db_msg_buffer.h"
#include "db_text.h"
#include "dm_data_prop_seri.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define DB_SYSVIEW_VERSION 0
#define DB_VIEW_SCHEMA_MAX_LEN 65535
#define MAX_ROW_ID 64
#define DB_VIEW_NUM_INIT 1
#define DB_VIEW_MAX_NUM 300
#define TO_STRING(x) #x
#define SYSVIEW_NUM_TO_STRING(x) TO_STRING(x)
#define MAX_TABLE_NAME_LEN_STR SYSVIEW_NUM_TO_STRING(MAX_TABLE_NAME_LEN)
#define DM_MAX_NAME_LENGTH_STR SYSVIEW_NUM_TO_STRING(DM_MAX_NAME_LENGTH)
#define MAX_TABLE_NAME_LEN_STR_FOR_SO_INFO SYSVIEW_NUM_TO_STRING(1025)  // 两倍TABLE_NAME长度
#define DM_MAX_PATTERN_LENGTH_STR SYSVIEW_NUM_TO_STRING(DM_MAX_PATTERN_LENGTH)
#define DM_MAX_COMMENT_LENGTH_STR SYSVIEW_NUM_TO_STRING(DM_MAX_COMMENT_LENGTH)
#define DB_MAX_PATH_STR SYSVIEW_NUM_TO_STRING(DB_MAX_PATH)
#define MAX_NAMESPACE_LENGTH_STR SYSVIEW_NUM_TO_STRING(MAX_NAMESPACE_LENGTH)
#define MAX_RES_POOL_NAME_LEN_STR SYSVIEW_NUM_TO_STRING(MAX_RES_POOL_NAME_LEN)
#define DTL_UDF_NAME_MAX_LEN_STR SYSVIEW_NUM_TO_STRING(DTL_UDF_NAME_MAX_LEN)
#define DB_MAX_INDEX_NAME_LENGTH_STR SYSVIEW_NUM_TO_STRING(DB_MAX_INDEX_NAME_LENGTH)
#define TIME_STR_MAX_SIZE_STR SYSVIEW_NUM_TO_STRING(TIME_STR_MAX_SIZE)
#ifdef FEATURE_VLIVF
#define DB_VLIVF_MAX_CENTROIDS_NUM SYSVIEW_NUM_TO_STRING(DM_MAX_CENTROIDS_NUM)
#define DB_VLIVF_MAX_CLUSTER_PATH_LEN_STR SYSVIEW_NUM_TO_STRING(DM_MAX_CLUSTER_PATH_LENGTH)
#define DB_VLIVF_MAX_DISTANCE_RANGE_NUM SYSVIEW_NUM_TO_STRING(DM_MAX_DISTANCE_RANGE_NUM)
#define DB_VLIVF_MAX_DISTANCE_RANGE_LEN_STR SYSVIEW_NUM_TO_STRING(DM_MAX_DISTANCE_RANGE_STR_LEN)
#endif
typedef struct SvCursor {
    char rowId[MAX_ROW_ID];
    TextT *viewCond;
    void *stmt;
    bool isCachedInLabelCursor;
    void *data;
} SvCursorT;

// vertexlabel  Parse Create Release function
typedef Status (*SvVertexLabelPaser)(void *stmt, const TextT *labelJson, const TextT *configJson);
typedef Status (*SvVertexLabelCreate)(void *stmt);
typedef Status (*SvVertexLabelRelease)(DbInstanceHdT dbInstance, const char *vertexLabelName);

/* *
 * @brief 各视图的查询接口
 * @param vertex: 视图对应的vertex
 * @param cursor: 游标参数，用于记录查询到那一条记录了，是16字节的内存，可以转换为各视图需要的数据类型
 * @return 成功或者其他失败原因
 */
typedef Status (*SvQueryProc)(void *vertex, SvCursorT *cursor);

typedef struct SvDef {
    char *svName;           // view name
    char *json;             // view json
    SvQueryProc queryProc;  // view query process
    bool isSupportCond;     // is support innner condition process in this view query
    bool isCreatedTable;    // indicates whether to create a catalog table
} SvDefT;

typedef struct SvInstance {
    DbSpinLockT lock;
    uint32_t count;  // view count
    SvVertexLabelPaser vertexLabelPaser;
    SvVertexLabelCreate vertexLabelCreate;
    SvVertexLabelRelease vertexLabelRelease;
    DbMemCtxT *memCtx;  // dynamic memCtx
    DbOamapT viewMap;
} SvInstanceT;

typedef struct ViewReq {
    TextT labelName;
} ViewReqT;

// sysview init
GMDB_EXPORT Status SvInit(SvInstanceT *inst, uint16_t instanceId);
// destroy sysview
GMDB_EXPORT void SvDestroy(SvInstanceT *inst);
GMDB_EXPORT void SvReset(SvInstanceT *inst);
// regist view and query process
GMDB_EXPORT Status SvRegistViews(SvInstanceT *inst, uint32_t viewsCnt, SvDefT *viewsInfo);
// regist view and query process for multi-instance
GMDB_EXPORT Status SvRegistViewsMultiIns(
    SvInstanceT *viewInst, SvDefT **dbInstanceViewsInfo, SvDefT *viewsInfo, uint32_t size);
// user view name to get the vertexlabel
GMDB_EXPORT Status SvGetVertexLabel(SvInstanceT *svInst, void *stmt, const char *viewName);
// query view and put view result into vertex
GMDB_EXPORT Status SvQueryView(SvInstanceT *inst, const ViewReqT *viewReq, void *vertex, SvCursorT *cursor);
// using the convergence engine query view and put view result into vertex
GMDB_EXPORT Status SvQueryGmsysView(SvInstanceT *inst, char *metaName, DmVertexT *dmVertex, SvCursorT *cursor);
GMDB_EXPORT Status SvGetViewByName(SvInstanceT *viewInst, const char *viewName, SvDefT **view);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* _SYSVIEW_H */
