
/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name:
 * Description:
 * Author:
 * Create: 2024-11-20
 */

#ifndef DB_STREAM_MSG_POOL_H
#define DB_STREAM_MSG_POOL_H

#include "adpt_types.h"
#include "db_text.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct StreamMsgPool StreamMsgPoolT;
typedef struct StreamMsgReader {
    StreamMsgPoolT *msgPool;
    uint64_t owner;
} StreamMsgReaderT;

typedef struct StreamMsgWriter {
    StreamMsgPoolT *msgPool;
    uint8_t *buf;
    uint64_t owner;
} StreamMsgWriterT;

// 服务端接口
SO_EXPORT_FOR_TS Status StreamMsgPoolInit(void);
ShmemPtrT GetStreamMsgPool(void);

SO_EXPORT_FOR_TS Status StreamMsgPoolBeginRead(StreamMsgPoolT *msgPool, uint64_t owner, StreamMsgReaderT *reader);
SO_EXPORT_FOR_TS void StreamMsgPoolEndRead(StreamMsgReaderT *reader);
SO_EXPORT_FOR_TS TextT StreamMsgPoolReadNext(StreamMsgReaderT *reader);
SO_EXPORT_FOR_TS void StreamMsgPoolReaderWait(StreamMsgPoolT *msgPool, uint64_t owner, uint32_t waitTime);

// 客户端接口
Status StreamMsgPoolBeginWrite(StreamMsgPoolT *msgPool, uint32_t size, StreamMsgWriterT *writer);
void StreamMsgPoolEndWrite(StreamMsgWriterT *writer, bool abort);
uint8_t *StreamMsgPoolGetWriteBuf(StreamMsgWriterT *writer);
void StreamMsgPoolNotifyReader(StreamMsgPoolT *msgPool);

#ifdef __cplusplus
}
#endif

#endif  // DB_STREAM_MSG_POOL_H
