/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: db_share_msg.h
 * Description: share message for subscribe
 * Author:
 * Create: 2022-04-08
 */

#ifndef DB_SHARE_MSG_H
#define DB_SHARE_MSG_H

#include "db_mem_context.h"
#include "db_msg_buffer.h"
#include "db_timer.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct ShareMsgNode {
    uint32_t nextFreeNodeId;     // 在FreeList中的后向结点
    uint32_t nextQueueNodeId;    // 用于上层事务记录后向节点
    uint32_t nextReleaseNodeId;  // 在releaseList中的后向节点
    uint32_t preUsingNodeId;     // 在usingList中的前向结点
    uint32_t nextUsingNodeId;    // 在usingList中的后向结点
    FixBufferT shareMsgBuff;
    uint64_t timestamp;
} ShareMsgNodeT;

#define SHARE_MSG_ARRAY_NUM (uint32_t)3  // 为了性能和底噪 数组只扩充3次
#define SHARE_MSG_FREE_THRESHOLD (uint32_t)8
#define SHARE_MSG_FIRST_ARRAY_ITEM_NUM (uint32_t)2048
#define SHARE_MSG_SECOND_ARRAY_ITEM_NUM (uint32_t)16384
#define SHARE_MSG_THIRD_ARRAY_ITEM_NUM (uint32_t)112640

typedef struct ShareMsgNodeArray {
    DbMemCtxT *memCtx;  // 共享内存ctx
    DbSpinLockT lock;
    uint32_t allocNum;
    uint32_t usedNum;
    uint32_t curPos;
    uint32_t freeIdx;   // 空闲idx
    uint32_t itemSize;  // 一个元素大小
    uint32_t extUsed;
    uint32_t arrayThr[SHARE_MSG_ARRAY_NUM];
    ShareMsgNodeT *arrayAddr[SHARE_MSG_ARRAY_NUM];
    ShmemPtrT extsArray[SHARE_MSG_ARRAY_NUM];
    TimerHandleT timer;
} ShareMsgNodeArrayT;

typedef struct ShareMsgMgr {
    DbMemCtxT *shMemCtx;  // 共享内存ctx
    ShmemPtrT shareMsgMgrPtr;
    ShareMsgNodeArrayT shareNodeArray;
    DbSpinLockT usingListLock;  // usingList的操作暂时加锁，后续优化为无锁
    uint32_t usingListHead;     // 已经使用的msg list
    uint32_t usingListTail;
    uint32_t releaseListHead;  // 待释放的list
} ShareMsgMgrT;

GMDB_EXPORT Status DbShareMsgInitNodeArray(ShareMsgMgrT *shareMsgMgr, DbMemCtxT *memCtx);
GMDB_EXPORT void DbShareMsgDestroyNodeArray(ShareMsgMgrT *shareMsgMgr);

GMDB_EXPORT Status DbShareMsgAllocNode(ShareMsgMgrT *shareMsgMgr, uint32_t size, uint32_t *nodeId,
    ShareMsgNodeT **shareMsgNode, FixBufferT **shareMsgBuff);

GMDB_EXPORT ShareMsgNodeT *DbShareMsgGetNodeByNodeId(ShareMsgMgrT *shareMsgMgr, uint32_t nodeId);

// share msg add node into release api for client, (ATTENTION: just client call)
GMDB_EXPORT Status DbShareMsgReleaseListAddNode(ShareMsgMgrT *shareMsgMgr, uint32_t nodeId);

GMDB_EXPORT void DbShareMsgNodeAddRefCount(ShareMsgNodeT *node);
GMDB_EXPORT void DbShareMsgNodeDecRefCount(ShareMsgMgrT *shareMsgMgr, ShareMsgNodeT *node, uint32_t nodeId);
GMDB_EXPORT Status DbShareMsgFreeReleaseList(ShareMsgMgrT *shareMsgMgr);
GMDB_EXPORT uint8_t DbShareMsgNodeGetUsage(const ShareMsgMgrT *shareMsgMgr);

inline static bool DbShareMsgNeedFreeReleaseList(const ShareMsgMgrT *shareMsgMgr)
{
    DB_POINTER(shareMsgMgr);
    uint32_t usedNum = shareMsgMgr->shareNodeArray.usedNum;
    uint32_t allocNum = shareMsgMgr->shareNodeArray.allocNum;
    DB_ASSERT(allocNum > 0);
    DB_ASSERT(usedNum <= allocNum);
    uint32_t freeNum = (allocNum - usedNum);
    uint32_t usedRatio = (uint32_t)(((float)usedNum / (float)allocNum) * DB_PERCENTAGE_BASE);
    const uint32_t usedThreshold = 30;
    return (usedRatio >= usedThreshold || freeNum > SHARE_MSG_FREE_THRESHOLD);
}

#ifdef __cplusplus
}

#endif

#endif /* DB_SHARE_MSG_H */
