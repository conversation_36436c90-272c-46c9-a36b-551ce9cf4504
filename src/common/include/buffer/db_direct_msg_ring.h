/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: db_direct_msg_ring.h
 * Description: direct server share msg ring
 * Author: <PERSON><PERSON><PERSON>
 * Create: 2023-12-21
 */

#ifndef DRT_DIRECT_MSG_RING_H
#define DRT_DIRECT_MSG_RING_H

#include "adpt_types.h"
#include "db_mem_context.h"
#include "db_shm_linkedlist.h"
#include "db_shm_hashmap.h"

#ifdef __cplusplus
extern "C" {
#endif

// 直连写共享通道结构体与接口
#define DIRECT_MSG_RING_BASE_COUNT 1024
#define DIRECT_MSG_RING_BASE_MAX_COUNT (1024 * 10)
#define DIRECT_MSG_RING_EXPAND_FACTOR 2
#define DIRECT_MSG_RING_SHRINK_FACTOR 4
#define DIRECT_STMG_MAP_COUNT 16
#define DIRECT_MSG_RING_MAX_ARRAY_NUM 8

// 消息环状态
typedef enum {
    RING_STATUS_FREE,        // 无消息，可以获取使用
    RING_STATUS_READY,       // 消息就绪，等待处理
    RING_STATUS_BLOCK,       // 消息阻塞
    RING_STATUS_REMOVED = 5  // 被移除状态
} RingStatusE;

// 老订阅消息环
typedef struct DirectMsgRing {
    DbSpinLockT ringLock;
    uint32_t totalNum;  // 消息环当前的总量
    uint32_t usedNum;   // 已经使用的数量
    uint32_t pos;       // 当前位置
    uint32_t ctxId;
    ShmemPtrT selfPtr;
    ShmemPtrT msgArrays[DIRECT_MSG_RING_MAX_ARRAY_NUM];
} DirectMsgRingT;

typedef struct DirectMsgRingBuff {
    uint32_t currNum;
    ShmemPtrT msg[DIRECT_MSG_RING_BASE_COUNT];
} DirectMsgRingBuffT;

typedef struct DirectStmgData {
    uint8_t type;
    uint32_t vertexLabelId;
} DirectStmgDataT;

// 新订阅消息环
typedef struct DirectStmgRing {
    DbSpinLockT ringLock;
    uint32_t totalNum;
    uint32_t usedNum;
    uint32_t pos;
    DbMemCtxT *memCtx;
    ShmemPtrT selfPtr;
    DirectStmgDataT msgPools[];
} DirectStmgRingT;

typedef struct DwPubSubShmInfo {
    volatile uint32_t msgCnt;  // 用于控制直连写/CS写订阅推送保序
    uint16_t connVersion;      // 用于填充直连写订阅消息体的connVersion
    uint8_t flowCtrlLevel;
} DwPubSubShmInfoT;

/*
 * 创建直连写老订阅共享ringBuffer
 * @param memCtx 共享内存上下文
 * @param dst共享ringBuffer指针
 */
Status DirectMsgRingCreate(DbMemCtxT *memCtx, DirectMsgRingT **directMsgRing);

/*
 * 销毁直连写共享ringBuffer
 * @param memCtx msgRing的内存上下文
 * @param directMsgRing 共享内存ringBuffer
 */
void DirectMsgRingDestroy(DbMemCtxT *memCtx, DirectMsgRingT *directMsgRing);

// 客户端将消息写入共享内存通道
Status DirectMsgRingAdvanceMsg(DirectMsgRingT *directMsgRing, ShmemPtrT data);

/*
 * 服务端将处理失败的消息写会ringBuffer，如果推送队列满，则可以反压到客户端
 * @param directMsgRing 客户端服务端交互的消息队列
 * @param data 写回的消息对应的共享内存addr
 */
void DirectMsgRingWriteBackMsg(DirectMsgRingT *directMsgRing, ShmemPtrT data, volatile uint32_t *totalMsgNum);

// 服务端消费buffer消息
Status DirectMsgRingConsumeMsg(DirectMsgRingT *directMsgRing, ShmemPtrT *data);

// 服务端读取buffer消息，不消费
Status DirectMsgRingRead(DirectMsgRingT *directMsgRing, ShmemPtrT *data);

/*
 * 创建直连写新订阅共享内存map
 * @param memCtx 共享内存上下文
 */
ShmemPtrT DwStmgMapCreate(DbMemCtxT *memCtx);

/*
 * 销毁直连写新订阅共享内存map
 * @param stmgRing 新订阅共享内存map
 */
void DwStmgMapDestroy(DbShmOamapT *stmgMap);

/*
 * 客户端写入状态合并表消息
 * @param stmgRing 新订阅共享内存map
 * @param type 消息类型
 * @param vertexLabelId 触发订阅消息的labelId
 */
Status DwStmgMapWriteMsg(DbShmOamapT *stmgMap, uint8_t type, uint32_t vertexLabelId);

/*
 * 服务端读取状态合并表消息
 * @param stmgRing 新订阅共享内存map
 * @out data 从map里读取到的表信息
 */
Status DwStmgMapConsumeMsg(DbShmOamapT *stmgMap, uint8_t *data);

#ifdef __cplusplus
}
#endif
#endif  // DRT_DIRECT_MSG_RING_H
