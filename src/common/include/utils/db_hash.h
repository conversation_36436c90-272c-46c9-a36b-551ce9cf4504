/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2021. All rights reserved.
 * File Name: db_hash.h
 * Description: header file for hash functions
 * Author:
 * Create: 2020-7-27
 */

#ifndef DB_HASH_H
#define DB_HASH_H

#include <inttypes.h>
#include "adpt_define.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define DB_XXHASH_SEED 10u

GMDB_EXPORT uint32_t DbHash32(const uint8_t *key, uint32_t len);

GMDB_EXPORT uint32_t DbHash32Combine(uint32_t lhs, uint32_t rhs);

GMDB_EXPORT uint32_t DbHash32WithSeed(const uint8_t *key, uint32_t len, uint32_t seed);

GMDB_EXPORT uint32_t DbStrToHash32(const char *str);  // equivalent of DbHash32(str, strlen(str))

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* DB_HASH_H */
