/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2024. All rights reserved.
 * File Name: db_condition.h
 * Description: Condition variable interfaces
 * Author: GQL
 * Create: 2022-10-24
 */
#ifndef DB_CONDITION_H
#define DB_CONDITION_H
#if defined(FEATURE_GQL)

#include "adpt_types.h"
#include "adpt_semaphore.h"
#include "adpt_spinlock.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Thread condition variable */
typedef struct {
    DbSemT sem;           // using semaphore to implement condition variable
    DbSpinLockT cntLock;  // used to protect waitCnt
    uint32_t waitCnt;     // number of waiting threads
} DbConditionT;

/*
 * @brief Initialize condition variable
 * @param cond[in/out]: condition variable to be initialized
 * @return: GMERR_OK if init success, else init fail
 */
Status DbCondInit(DbConditionT *cond);

/*
 * @brief Wait for the condition variable to be signaled or broadcast.
 * @param cond[in]: condition variable
 * @param lock[in]: lock is assumed to be locked before
 * @return: GMERR_OK if success, else fail
 */
Status DbCondWait(DbConditionT *cond, DbSpinLockT *lock);

/*
 * @brief Wait for the condition variable to be signaled or broadcast.
 * @param cond[in]: condition variable
 * @param lock[in]: lock is assumed to be locked before
 * @param timeoutUs: timeout in mu seconds
 * @return: GMERR_OK if success, else fail
 */
Status DbCondTimedWait(DbConditionT *cond, DbSpinLockT *lock, uint32_t timeoutUs);

/*
 * @brief Wake up one thread waiting for the condition variable.
 * @param cond[in]: condition variable
 * @return : GMERR_OK if success, else fail
 */
Status DbCondSignal(DbConditionT *cond);

/*
 * @brief Wake up all threads waiting for condition variable.
 * @param cond[in]: condition variable
 * @return: GMERR_OK if success, else fail
 */
Status DbCondBroadcast(DbConditionT *cond);

/*
 * @brief Destroy the condition variable.
 * @param cond[in]: condition variable to be destroyed
 * @return: GMERR_OK if success, else fail
 */
Status DbCondDestroy(DbConditionT *cond);

#ifdef __cplusplus
}
#endif
#endif  // FEATURE_GQL
#endif  // DB_CONDITION_H
