/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: db_queue.h
 * Description: Queue implemented with extensible array for high efficiency.
 * It's not implemented with traditional linkedlist, which has high cost of allocating memory.
 * The interface defined here can refer to STL Queue. Some basic operations can be found here like pop(),
 * push(), front() and so on.
 * @attention
 * 1.Pop operation don't free element actually, it just shifts the front pointer to the right. So there is
 *   a risk of out of memory when there are massive push/pop operations.
 * 2.The right shift of front pointer can be rectified by DbQueueClear operation.
 * 3.The memory allocated for the queue have to be released manually.
 * Author: GQL
 * Create: 2022-12-14
 */
#ifndef DB_QUEUE_H
#define DB_QUEUE_H

#include "db_mem_context.h"
#include "gmc_errno.h"

#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef struct DbQueue {
    DbMemCtxT *memCtx;  // memory context
    void *items;
    uint32_t itemSize;
    uint32_t capacity;  // capacity of items array
    uint32_t front;     // front index of the queue
    uint32_t rear;      // rear index of the queue
} DbQueueT;

/*
 * @brief Create db queue, the default size is 64u. DbQueueDestroy must be called when created queue is not used.
 * @param[in] memCtx: memory context
 * @param[in] queue: input queue, the memory is already allocated.
 * @param[in] itemSize: size of item in the queue
 * @return ok if queue is created successfully.
 */
GMDB_EXPORT Status DbQueueCreate(DbMemCtxT *memCtx, DbQueueT *queue, uint32_t itemSize);

/*
 * @brief Create db queue with given queue size. More details can refer to DbQueueCreate.
 */
GMDB_EXPORT Status DbQueueCreateWithSize(DbMemCtxT *memCtx, DbQueueT *queue, uint32_t itemSize, uint32_t queueSize);

/*
 * @brief To judge whether the queue is empty.
 */
GMDB_EXPORT bool DbQueueIsEmpty(DbQueueT *queue);

/*
 * @brief Push one item to db queue.
 * @attention the input must be the pointer of item. if the item is type ElementT, then input item must be ElementT *.
 * The item pointer must not be used after the call to the function: you need to free the memory.
 * @param[in] queue: target queue.
 * @param[in] item: pointer to item
 * @return ok if item is copied into the queue.
 */
GMDB_EXPORT Status DbQueuePush(DbQueueT *queue, void *item);

/*
 * @brief Pop the front item from the queue and set the front to right by one step. User have to be careful that
 * the queue may be empty.
 * Take an example:
 * If the item in the queue is ElementT *,
 * Then we have to pop the front item from the queue with following codes:
 *     ElementT *item = (ElementT *)DbMemCtxAlloc(memCtx, sizeof(ElementT));
 *     Status ret = DbQueuePop(queue, (void *)item);
 * @param[in] queue: target queue.
 * @param[out] item: pointer to allocated memory for the item; the item is copied there
 * @return ok if the item is copied out of the queue.
 */
GMDB_EXPORT Status DbQueuePop(DbQueueT *queue, void *item);

/*
 * @brief Read the front item from the queue and do not change the front pointer. More details can refer to
 * DbQueueRemove.
 */
GMDB_EXPORT Status DbQueueFront(DbQueueT *queue, void *item);

/*
 * @brief Read the rear item from the queue and do not change the rear pointer.
 */
GMDB_EXPORT Status DbQueueBack(DbQueueT *queue, void *item);

/**
 * @brief return the element count in the queue.
 */
GMDB_EXPORT uint32_t DbQueueSize(DbQueueT *queue);

/*
 * @brief set the queue front and rear to zero.
 * @attention the memory of queue item is not re-initialized to zero.
 */
GMDB_EXPORT void DbQueueClear(DbQueueT *queue);

/*
 * @brief Free the memory allocated for the queue items.
 */
GMDB_EXPORT void DbQueueDestroy(DbQueueT *queue);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* DB_QUEUE_H */
