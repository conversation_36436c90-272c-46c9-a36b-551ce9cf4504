/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: db_status.h
 * Description: 这个文件实现了Status相关的一些辅助宏。
 * 使用方法：
 * 假设我们有函数：
 * ```c
 * // in func.h
 * Status Func(int a, int *b, int *c);
 * // in func.c
 * Status Func(int a, int *b, int *c) {
 *     if (a > INT_MAX / 2) {
 *         return GMERR_INVALID_PARAMETER_VALUE;
 *     }
 *     *b = a;
 *     *c = a * 2;
 *     return GMERR_OK;
 * }
 * ```
 * 此时b和c是输出参数，利用本文件提供的辅助宏，可以比较方便的将其改为使用返回值，
 * 从而提高可读性，并提升性能（返回值可能可以通过寄存器返回，并且使用返回值更容易被编译器优化）。
 * 修改后：
 * ```c
 * // in func.h
 * typedef struct { int b, c; } FuncResultT;
 *
 * // 如果我们希望某个类型支持STATUS_OR系列函数，就在对应类型的定义后添加这个宏。
 * STATUS_OR_DEFINE(FuncResultT)
 *
 * STATUS_OR(FuncResultT) Func(int a);
 * // in func.c
 * STATUS_OR(FuncResultT) Func(int a) {
 *     if (a > INT_MAX / 2) {
 *         return STATUS_OR_MAKE_STATUS(FuncResultT, GMERR_INVALID_PARAMETER_VALUE);  // 返回错误码
 *     }
 *     return STATUS_OR_MAKE_RESULT(FuncResultT, {.b = a, .c = a * 2});  // 返回结果
 * }
 * ```
 * 使用的地方可以由
 * ```c
 * Status ret;
 * int b, c;
 * ret = Func(a, &b, &c);
 * if (ret == GMERR_OK) printf("%d %d\n", b, c);
 * ```
 * 变为
 * ```c
 * STATUS_OR(FuncResultT) ret = Func(a);
 * if (ret.status == GMERR_OK) printf("%d %d\n", ret.result.b, ret.result.c);
 * ```
 *
 * 特别的，如果我们只有一个输出参数，比如
 * ```c
 * Status Func(Input input, Output *output);
 * ```
 * 我们只需要改为
 * ```c
 * STATUS_OR(Output) Func(Input input);
 * ```
 * 如果我们并没有STATUS_OR_DEFINE(Output)，那么还需要在Output定义的地方添加这个宏。
 *
 * 如果我们的返回值本身也是指针，比如
 * ```c
 * Status Func(Input input, Output **output);  // 即返回值为Output*类型
 * ```
 * 我们只需要添加STATUS_OR_PTR_DEFINE(Output)，并将代码改为
 * ```c
 * STATUS_OR_PTR(Output) Func(Input input);
 * // 注意：STATUS_OR_PTR而非STATUS_OR
 * // 此时函数体内的STATUS_OR_MAKE_STATUS/STATUS_OR_MAKE_RESULT
 * // 也应替换为STATUS_OR_PTR_MAKE_STATUS/STATUS_OR_PTR_MAKE_RESULT
 * ```
 *
 * 本头文件已经对基本类型添加了STATUS_OR_DEFINE/STATUS_OR_PTR_DEFINE，如果有其它类型需要被支持，
 * 但是我们又不能改变其源码时，可以在这里添加相应的STATUS_OR_DEFINE/STATUS_OR_PTR_DEFINE。
 * Author: sujiao
 * Create: 2022-2-15
 */
#ifndef DB_STATUS_H
#define DB_STATUS_H

#include <assert.h>
#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

#include "gmc_errno.h"
#include "adpt_types.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define STATUS_OR(type) struct StatusOr##type
#define STATUS_OR_MAKE_STATUS(type, ...) ((STATUS_OR(type)){.status = __VA_ARGS__, {.dummy = {}}})
#define STATUS_OR_MAKE_RESULT(type, ...) ((STATUS_OR(type)){.status = GMERR_OK, {.result = __VA_ARGS__}})

#define STATUS_OR_PTR(type) struct StatusOrPtr##type
#define STATUS_OR_PTR_MAKE_STATUS(type, ...) ((STATUS_OR_PTR(type)){.status = __VA_ARGS__, {.dummy = {}}})
#define STATUS_OR_PTR_MAKE_RESULT(type, ...) ((STATUS_OR_PTR(type)){.status = GMERR_OK, {.result = __VA_ARGS__}})

#define STATUS_OR_DEFINE(type) \
    STATUS_OR(type)            \
    {                          \
        Status status;         \
        union {                \
            struct {           \
            } dummy;           \
            type result;       \
        };                     \
    };

#define STATUS_OR_PTR_DEFINE(type) \
    STATUS_OR_PTR(type)            \
    {                              \
        Status status;             \
        union {                    \
            struct {               \
            } dummy;               \
            type *result;          \
        };                         \
    };

#define STATUS_OR_DEFINE_ALL(type) \
    STATUS_OR_DEFINE(type)         \
    STATUS_OR_PTR_DEFINE(type)

// 为基本类型添加支持
STATUS_OR_DEFINE_ALL(char)
STATUS_OR_DEFINE_ALL(short)
STATUS_OR_DEFINE_ALL(int)
STATUS_OR_DEFINE_ALL(long)
STATUS_OR_DEFINE_ALL(float)
STATUS_OR_DEFINE_ALL(double)

STATUS_OR_DEFINE_ALL(size_t)
STATUS_OR_DEFINE_ALL(bool)
STATUS_OR_DEFINE_ALL(int8_t)
STATUS_OR_DEFINE_ALL(int16_t)
STATUS_OR_DEFINE_ALL(int32_t)
STATUS_OR_DEFINE_ALL(int64_t)
STATUS_OR_DEFINE_ALL(uint8_t)
STATUS_OR_DEFINE_ALL(uint16_t)
STATUS_OR_DEFINE_ALL(uint32_t)
STATUS_OR_DEFINE_ALL(uint64_t)

STATUS_OR_PTR_DEFINE(void)

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
