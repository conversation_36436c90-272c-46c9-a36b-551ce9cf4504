/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: 组件动态加载框架接口
 * Author: chenao
 * Create: 2022-08-30
 * Notes:
 */
#ifndef DB_DYN_LOAD_H
#define DB_DYN_LOAD_H

#include "adpt_types.h"
#include "db_hashmap.h"

#ifdef __cplusplus
extern "C" {
#endif

#define COMPONENT_MEMDATA "memdata"
#define COMPONENT_DURABLE_MEMDATA "durablememdata"
#define COMPONENT_TRM "trm"
#ifdef TS_MULTI_INST
#define COMPONENT_BUFFER_POOL "db_ts"
#define COMPONENT_PERSISTENCE "db_ts"
#define COMPONENT_BTREE "db_ts"
#define COMPONENT_CSTORE "db_ts"
#define COMPONENT_TS "db_ts"
#define COMPONENT_STREAM "db_ts"
#else
#define COMPONENT_BUFFER_POOL "bufferpool"
#define COMPONENT_PERSISTENCE "persistence"
#define COMPONENT_BTREE COMPONENT_TRM
#define COMPONENT_CSTORE COMPONENT_TRM
#define COMPONENT_TS "ts"
#define COMPONENT_STREAM "stream"
#endif
#define COMPONENT_HAC "hac"
#define COMPONENT_SQL "sql"
#define COMPONENT_MINIKV "minikv"
#define COMPONENT_YANG "yang"
#define COMPONENT_DISTRIBUTION "distribution"
#define COMPONENT_GQL "gql"
#define COMPONENT_SIMPLE_RELATION "simplerel"
#define COMPONENT_INSPECT_TOOL "inspect"

GMDB_EXPORT Status DbDynLoadFeatureSo(DbInstanceHdT dbInstance);
GMDB_EXPORT void *DbDynLoadGetFunc(const char *feature, const char *subsystem);
GMDB_EXPORT bool DbDynLoadHasFeature(const char *feature);
// 获取组件化so目录的路径
GMDB_EXPORT const char *DbDynGetFeatureLibDirPath(void);
// 获取组件化so的完整路径
GMDB_EXPORT Status DbDynGetFeatureLibPath(char *featurePath, const char *featureDir, const char *featureName);

GMDB_EXPORT Status DbDynLoadFeatureFuncClt(void);

GMDB_EXPORT Status DbDynLoadSetSoHandleClt(const char *feature, void *soHandle);
GMDB_EXPORT Status DbDynLoadSetSingleSoClt(const char *feature);
GMDB_EXPORT bool DbDynLoadSoIsLoadedClt(const char *feature);

// 客户端已经完成组件化
GMDB_EXPORT bool DbDynLoadCltComponentizeHasFinished(void);
// 客户端组件化完成
GMDB_EXPORT void DbDynLoadCltComponentizeFinished(void);
// 客户端组件化未完成（uninit调用，避免init时候不能重新加载）
GMDB_EXPORT void DbDynLoadCltComponentizeUnFinished(void);

GMDB_EXPORT void DbDynUnloadFeatureSoHandle(void);

#ifdef __cplusplus
}
#endif

#endif
