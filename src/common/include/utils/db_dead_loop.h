/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: db_dead_loop.h
 * Description: header file for db common dead loop detect
 * Author:
 * Create: 2022-8-30
 */

#ifndef DB_DEAD_LOOP_H
#define DB_DEAD_LOOP_H

#include "adpt_define.h"
#include "db_mem_context.h"

#ifdef __cplusplus
extern "C" {
#endif

// userData是注册token填
// runData是当次填
// 返回值非STATUS_OK，将根据配置方式重试
typedef Status (*DbDeadLoopProc)(void *ctx, DbDataT userData, DbDataT runData);

typedef enum DbDeadLoopHungLevelE {
    DEAD_LOOP_HUNG_NORMAL = 0,
    DEAD_LOOP_HUNG_LEVEL_1,
    DEAD_LOOP_HUNG_LEVEL_2,
    DEAD_LOOP_HUNG_LEVEL_3,
    DEAD_LOOP_HUNG_LEVEL_BUTT
} DbDeadLoopHungLevelE;

#define DEAD_LOOP_FAILED_RETRY_DEFAULT_PERIOD 5000

#define DB_DEAD_LOOP_EXCEPTION_HUNG_LEVEL_NUM ((uint32_t)DEAD_LOOP_HUNG_LEVEL_BUTT - 1)

typedef struct {
    DbDeadLoopHungLevelE level;
    uint32_t threshold;  // 挂死时间 unit：s，传入DB_INVALID_UINT32，则表示不包含该挂死等级，下同
    DbDeadLoopProc proc;
    uint32_t retryPeriod;
} DbDeadLoopInitArgsT;

typedef struct {
    uint32_t levelThreshold[DB_DEAD_LOOP_EXCEPTION_HUNG_LEVEL_NUM];
} DbDeadLoopTokenArgsT;

typedef struct DbDeadLoopRegPara {
    DbMemCtxT *memctx;
    uint32_t maxNum;
    DbDeadLoopInitArgsT args[DB_DEAD_LOOP_EXCEPTION_HUNG_LEVEL_NUM];
    uint32_t period;  // monitor线程循环周期 unit：ms
    bool needTimer;
} DbDeadLoopRegParaT;

GMDB_EXPORT void DbDeadLoopSetRegPara(DbDeadLoopRegParaT *para, DbDeadLoopHungLevelE level, uint32_t threshold,
    DbDeadLoopProc proc, uint32_t retryPeriod);

GMDB_EXPORT Status DbDeadLoopRegist(DbDeadLoopTypeE type, const DbDeadLoopRegParaT *para);

GMDB_EXPORT void DbDeadLoopUnRegist(DbDeadLoopTypeE type);

GMDB_EXPORT uint32_t DbDeadLoopAllocToken(
    DbDeadLoopTypeE type, void *userCtx, DbDataT userData, const DbDeadLoopTokenArgsT *args);

GMDB_EXPORT void DbDeadLoopFreeToken(DbDeadLoopTypeE type, uint32_t token);

GMDB_EXPORT void DbDeadLoopDetectBegin(DbDeadLoopTypeE type, uint32_t token, DbDataT runData);

GMDB_EXPORT void DbDeadLoopDetectEnd(DbDeadLoopTypeE type, uint32_t token);

GMDB_EXPORT void DbDeadLoopReTrigger(DbDeadLoopTypeE type, uint32_t token);

GMDB_EXPORT void DbDeadLoopCheckProc(DbDeadLoopTypeE type);

#ifdef __cplusplus
}
#endif
#endif /* DB_DEAD_LOOP_H */
