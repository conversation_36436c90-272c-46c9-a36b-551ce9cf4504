/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 * File Name: db_json.h
 * Description: header file for db_json
 * Author:
 * Create: 2022/6/8
 */

#ifndef DB_JSON_H
#define DB_JSON_H

#include "db_json_common.h"
#include "adpt_define.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

// 修改开源软件流程太麻烦，为了避免栈溢出，在调用json_loads前包一层简单的校验。
// 如果后续有机会，应该直接修改开源软件的JSON_PARSER_MAX_DEPTH配置项。
// 下面的函数因为需要区分客户端和服务端配置项，因此有两个版本。
GMDB_EXPORT DbJsonT *DbLoadJsonClt(const char *string, size_t flags);  // json_loads
GMDB_EXPORT DbJsonT *DbLoadJsonSrv(const char *string, size_t flags);  // json_loads
GMDB_EXPORT DbJsonT *DbLoadJsonFileCltWithHeader(
    const char *path, size_t flags);  // json_load_file,没有header的话，headerLen传入0

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
