/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2024. All rights reserved.
 * File Name: db_concurrent_ringbuf.h
 * Description: header file for ring buffer interface
 * Author: GQL
 * Create: 2022-10-24
 */
#if defined(FEATURE_GQL)
#ifndef DB_CONCURRENT_RINGBUF_H
#define DB_CONCURRENT_RINGBUF_H

#include "db_mem_context.h"
#include "adpt_spinlock.h"
#include "db_condition.h"
#include "adpt_thread.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef bool (*DbRingBufCondFuncT)(void *item);

typedef struct DbConcurrentRingBuf {
    DbSpinLockT lock;
    DbConditionT writeNotify;  // 在读取完毕后通知生产者可写
    DbConditionT readNotify;   // 在写入完毕后通知消费者可读
    void **items;
    uint32_t head;
    uint32_t tail;
    uint32_t capacity;
    uint32_t count;
} DbConcurrentRingBufT;

GMDB_EXPORT Status DbCreateRingBuf(DbMemCtxT *memCtx, uint32_t capacity, DbConcurrentRingBufT **ringbuf);
GMDB_EXPORT Status DbDestroyRingBuf(DbConcurrentRingBufT *ringbuf, DbMemCtxT *memCtx);

GMDB_EXPORT Status DbRingBufBlockWrite(DbConcurrentRingBufT *ringbuf, void *item);

GMDB_EXPORT Status DbRingBufBlockReadWithCond(DbConcurrentRingBufT *ringbuf, DbRingBufCondFuncT func, void **item);
GMDB_EXPORT Status DbRingBufReadNotify(DbConcurrentRingBufT *ringbuf);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  /* DB_CONCURRENT_RINGBUF_H */
#endif  // FEATURE_GQL
