/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: header file for inter process manager
 * Create: 2024-1-15
 */

#ifndef DB_INTER_PROCESS_MGR_H
#define DB_INTER_PROCESS_MGR_H

#include "adpt_spinlock.h"
#include "db_file_lock.h"
#include "db_internal_error.h"
#include "db_mem_utils.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_PROCESS_COUNT 8

typedef void (*FileMapMemBlockInitFn)(
    uint32_t blockIdx, ShmemPtrT blockPtr, int8_t *memBlockBuf, uint32_t blockBufSize, void *args);

#define FILEMAP_BUF_ALIGN 16

typedef enum {
    // auto map
    FILEMAP_SYSTABLE = 0,
    FILEMAP_TXS_PAGE_COUNT,
    FILEMAP_PROCESS_INFO,
    FILEMAP_TABLE_LOCKS,
    FILEMAP_BUFPOOL,
    // keep 4K aligned
    FILEMAP_RESERVED,
    // manual map
    FILEMAP_PAGE_BUF,
    FILEMAP_ROOT_MEMORY_CTX,
    FILEMAP_TYPE_MAX
} FileMapBufTypeE;

typedef struct {
    bool autoMap;
    void *data;
    uint64_t offset;
    uint64_t length;
} FileMapBufT;

typedef struct {
    int8_t *buf;
    uint32_t bufSize;
    int64_t offset;
    DbSpinLockT lock;
} MemBlockDataT;

typedef struct {
    uint32_t perBlockSize;
    uint32_t totalBlockCnt;
    uint64_t bufOffset;
    MemBlockDataT *blockData;
} MapMemBlockT;

typedef struct FileMapMgr {
    int32_t fd;
    bool isFirstBoot;
    int8_t *mappedBase;
    int8_t *autoMappedEnd;
    void *memCtx;
    FileMapBufT buffers[FILEMAP_TYPE_MAX];
} FileMapMgrT;

typedef struct {
    void *memCtx;
    const char *filePath;
    bool isReadOnly;
    bool isFirstBoot;
    int32_t fd;
    uint64_t bufSize[FILEMAP_TYPE_MAX];
} FileMapInitArgsT;

typedef struct TagProcesCtrl {
    volatile uint32_t reloadState;  // Indicate whether share memory been reload or not
    volatile uint32_t txnsCnt;
} ProcesCtrlT;

typedef struct TagProcessInfo {
    volatile uint32_t recoverStatus;
    volatile ProcesCtrlT processCtrls[MAX_PROCESS_COUNT];
} ProcessInfoT;

typedef struct ProcessInfoMgr {
    int fileMapFd;
    int processId;
    ProcessInfoT *info;
} ProcessInfoMgrT;

typedef struct ShareModeInfo {
    FileMapMgrT *fileMapMgr;
    int fileMapFd;
    int32_t processIndex;
    bool isFirstBoot;
    MtFileLockT recoveryLock;
    ProcessInfoMgrT processMgr;
    bool sharedReadOnly;
} SharedModeInfoT;

#ifdef __cplusplus
}
#endif
#endif  // DB_INTER_PROCESS_MGR_H
