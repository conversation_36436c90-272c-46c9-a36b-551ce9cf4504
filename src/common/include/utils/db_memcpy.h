/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: db_memcpy.h
 * Description: 这个文件实现了一个高效的memcpy函数DbFastMemcpy，主要优化了
 * 1. 64字节以内的复制
 * 2. dst和src是8字节和16字节对齐（即dst-src % 8 == 0和dst-src % 16 == 0）的大于64字节的复制。
 * 需要注意：
 * 1. 这里的实现为内联函数，大量使用会增加二进制文件大小，所以请仅在性能关键瓶颈处使用。
 * 2. 使用之前请务必先做性能测试，确认使用本版本更优之后再使用！
 * 3. 此代码专为aarch64指令集优化，在别的场景使用请务必先进行性能测试。
 * Author: sujiao
 * Create: 2022-1-19
 */

#ifndef DB_MEMCPY_H
#define DB_MEMCPY_H

#include "adpt_define.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/*
 * 使用memcpy的说明：
 * 本代码中使用了memcpy函数，但是因为memcpy的size只取了1，2，4，8，16，都是编译期常量，
 * 并且目标和源addr至少有一个为局部变量addr。所以编译器会将其优化为直接将内存数据加载
 * 到寄存器或直接将寄存器中数据存入内存，而不会有memcpy的函数调用。
 * 此处为了性能，不适用安全函数即memcpy_s。
 */

// 从src快速复制size个字节到data，并增加src到未读位置，如果src没有按照size对齐，性能可能会有一定下降。
#define DB_FAST_READ(src, data, size)                                           \
    do {                                                                        \
        static_assert(sizeof(data) == (size), "Data size does not match size"); \
        (void)memcpy((uint8_t *)&(data), src, sizeof(data));                    \
        src += sizeof(data);                                                    \
    } while (0)

// 从data快速复制size个字节到dst，并增加dst到未写位置，如果dst没有按照size对齐，性能可能会有一定下降。
#define DB_FAST_WRITE(dst, data, size)                                          \
    do {                                                                        \
        static_assert(sizeof(data) == (size), "Data size does not match size"); \
        (void)memcpy(dst, (uint8_t *)&(data), sizeof(data));                    \
        dst += sizeof(data);                                                    \
    } while (0)

// 内部函数请勿直接使用。
// 需要保证dst和src按照16字节对齐，本函数会从src复制16 * n字节至dst。
inline static void DbMemcpy16Align16(uint8_t *dst, const uint8_t *src, size_t n)
{
    DB_ASSERT(((uintptr_t)dst % 16 == 0) && ((uintptr_t)src % 16 == 0));
    struct {
        uint64_t d0, d1;
    } d0, d1, d2, d3;

    // 4 * 16字节批量读写。
    while (n >= 4) {
        DB_FAST_READ(src, d0, 16);
        DB_FAST_READ(src, d1, 16);
        DB_FAST_READ(src, d2, 16);
        DB_FAST_READ(src, d3, 16);
        DB_FAST_WRITE(dst, d0, 16);
        DB_FAST_WRITE(dst, d1, 16);
        DB_FAST_WRITE(dst, d2, 16);
        DB_FAST_WRITE(dst, d3, 16);
        n -= 4;
    }

    // 特殊处理剩余的n < 4个16字节。
    if ((n & 2) != 0) {
        DB_FAST_READ(src, d0, 16);
        DB_FAST_READ(src, d1, 16);
        DB_FAST_WRITE(dst, d0, 16);
        DB_FAST_WRITE(dst, d1, 16);
    }
    if ((n & 1) != 0) {
        DB_FAST_READ(src, d0, 16);
        DB_FAST_WRITE(dst, d0, 16);
    }
}
// 内部函数请勿直接使用。
// 需要保证dst按照16字节对齐，src按照8字节对齐，本函数会从src复制16 * n字节至dst，需要保证n > 2。
inline static void DbMemcpy16Align8(uint8_t *dst, const uint8_t *src, size_t n)
{
    DB_ASSERT(n > 2 && ((uintptr_t)dst % 16 == 0) && ((uintptr_t)src % 8 == 0));
    struct {
        uint64_t d0, d1;
    } d0, d1, d2, d3, d4;
    // 如果dst是8字节对齐的，那么我们会先读取16字节并写入8字节，
    // 之后的4 * 16字节批量读写就都可以是16字节对齐的了。

    // 先读24个字节并写入16个字节，剩下的8个字节会在后边写入。
    n -= 2;
    DB_FAST_READ(src, d2.d1, 8);
    DB_FAST_READ(src, d3, 16);
    d4.d0 = d2.d1;
    d4.d1 = d3.d0;
    DB_FAST_WRITE(dst, d4, 16);

    while (n >= 4) {
        DB_FAST_READ(src, d0, 16);
        DB_FAST_READ(src, d1, 16);
        DB_FAST_READ(src, d2, 16);

        d4.d0 = d3.d1;
        d4.d1 = d0.d0;
        DB_FAST_WRITE(dst, d4, 16);

        d4.d0 = d0.d1;
        d4.d1 = d1.d0;
        DB_FAST_WRITE(dst, d4, 16);

        d4.d0 = d1.d1;
        d4.d1 = d2.d0;
        DB_FAST_WRITE(dst, d4, 16);

        DB_FAST_READ(src, d3, 16);

        d4.d0 = d2.d1;
        d4.d1 = d3.d0;
        DB_FAST_WRITE(dst, d4, 16);

        n -= 4;
    }
    DB_FAST_READ(src, d0.d0, 8);
    d4.d0 = d3.d1;
    d4.d1 = d0.d0;
    DB_FAST_WRITE(dst, d4, 16);

    // 特殊处理剩余的n < 4个16字节。
    if ((n & 2) != 0) {
        DB_FAST_READ(src, d0, 16);
        DB_FAST_READ(src, d1, 16);
        d4.d0 = d0.d1;
        d4.d1 = d1.d0;
        DB_FAST_WRITE(dst, d0.d0, 8);
        DB_FAST_WRITE(dst, d4, 16);
        DB_FAST_WRITE(dst, d1.d1, 8);
    }
    if ((n & 1) != 0) {
        DB_FAST_READ(src, d0, 16);
        DB_FAST_WRITE(dst, d0.d0, 8);
        DB_FAST_WRITE(dst, d0.d1, 8);
    }
}

// 内部函数请勿直接使用。
// 需要保证dst + n按照16字节对齐，本函数会从src复制n字节至dst。
inline static void DbMemcpy16Prefix(uint8_t *dst, const uint8_t *src, size_t n)
{
    DB_ASSERT((((uintptr_t)dst + n) % 16 == 0) && n > 0 && n < 16);

    // 这里的顺序可以保证每个read都是对齐的，请不要随意变动。
    if ((n & 1) != 0) {
        uint8_t d;
        DB_FAST_READ(src, d, 1);
        DB_FAST_WRITE(dst, d, 1);
    }
    if ((n & 2) != 0) {
        uint16_t d;
        DB_FAST_READ(src, d, 2);
        DB_FAST_WRITE(dst, d, 2);
    }
    if ((n & 4) != 0) {
        uint32_t d;
        DB_FAST_READ(src, d, 4);
        DB_FAST_WRITE(dst, d, 4);
    }
    if ((n & 8) != 0) {
        uint64_t d;
        DB_FAST_READ(src, d, 8);
        DB_FAST_WRITE(dst, d, 8);
    }
}

// 内部函数请勿直接使用。
// 需要保证dst按照16字节对齐，本函数会从src复制n字节至dst。
inline static void DbMemcpy16Suffix(uint8_t *dst, const uint8_t *src, size_t n)
{
    DB_ASSERT(((uintptr_t)dst % 16 == 0) && n > 0 && n < 16);

    // 这里的顺序可以保证每个read都是对齐的，请不要随意变动。
    if ((n & 8) != 0) {
        uint64_t d;
        DB_FAST_READ(src, d, 8);
        DB_FAST_WRITE(dst, d, 8);
    }
    if ((n & 4) != 0) {
        uint32_t d;
        DB_FAST_READ(src, d, 4);
        DB_FAST_WRITE(dst, d, 4);
    }
    if ((n & 2) != 0) {
        uint16_t d;
        DB_FAST_READ(src, d, 2);
        DB_FAST_WRITE(dst, d, 2);
    }
    if ((n & 1) != 0) {
        uint8_t d;
        DB_FAST_READ(src, d, 1);
        DB_FAST_WRITE(dst, d, 1);
    }
}

// 内部函数请勿直接使用。
// 需要保证dst按照16字节对齐，本函数会从src复制16 * n字节至dst，需要保证n > 2。
inline static void DbMemcpy16(uint8_t *dst, const uint8_t *src, size_t n)
{
    DB_ASSERT(n > 2 && ((uintptr_t)dst % 16 == 0));

    if (((uintptr_t)src & 15) == 0) {
        DbMemcpy16Align16(dst, src, n);
    } else if (((uintptr_t)src & 15) == 8) {
        DbMemcpy16Align8(dst, src, n);
    } else {
        // 此段代码为极度优化后的结果，如果要修改请务必做好性能测试，并详细比对产生的汇编代码。

        uint8_t __attribute__((vector_size(32))) d0, d1;  // d0和d1为32字节的数据
        struct {
            uint64_t d0, d1;
        } d2;

        // 4 * 16字节批量读写。
        while (n >= 4) {
            DB_FAST_READ(src, d0, 32);
            DB_FAST_READ(src, d1, 32);
            DB_FAST_WRITE(dst, d0, 32);
            DB_FAST_WRITE(dst, d1, 32);
            n -= 4;
        }
        // 特殊处理剩余的n < 4个16字节。
        if ((n & 2) != 0) {
            DB_FAST_READ(src, d0, 32);
            DB_FAST_WRITE(dst, d0, 32);
        }
        if ((n & 1) != 0) {
            DB_FAST_READ(src, d2, 16);
            DB_FAST_WRITE(dst, d2, 16);
        }
    }
}

// 内部函数请勿直接使用。
// 本函数会从src复制n字节至dst，需要保证n > 64。
inline static void DbMemcpy64Plus(uint8_t *dst, const uint8_t *src, size_t n)
{
    DB_ASSERT(n > 64);
    // 找到一个prefix，使得dst + prefix按照16字节对齐，并且0 <= prefix < 16。
    // 然后调用DbMemcpy16Prefix复制prefix个字节。
    size_t prefix = (-(uintptr_t)dst) & 15;
    if (prefix != 0) {
        DbMemcpy16Prefix(dst, src, prefix);
    }

    // dst + prefix按照16字节对齐后，可以拷贝的16字节数量为infix。
    size_t infix = (n - prefix) / 16;
    DB_ASSERT(infix > 2);
    DbMemcpy16(dst + prefix, src + prefix, infix);

    // dst + n - suffix实际为dst + prefix + infix * 16，即剩下还未复制内容的起始addr，
    // 不难发现，dst + n - suffix是16字节对齐的。
    // 因此可以调用DbMemcpy16Suffix复制剩下的suffix个字节。
    size_t suffix = (n - prefix) & 15;
    if (suffix != 0) {
        DbMemcpy16Suffix(dst + n - suffix, src + n - suffix, suffix);
    }
}

// 本函数会从src复制n字节至dst，本函数的语义与C语言标准库中memcpy基本相同。
// 区别是入口处会断言目标大小不小于源大小、目标和源不为空指针，且使用uint8_t*而非void*。
// 使用此代码时，请务必标注理由（通常为性能相关），并标注需要调用者保证参数合法。
inline static void DbFastMemcpy(uint8_t *dst, size_t m, const uint8_t *src, size_t n)
{
    DB_ASSERT(m >= n);
    DB_POINTER2(dst, src);
    if (n < 4) {
        // 特殊处理n < 4的情况
        if ((n & 1) != 0) {
            uint8_t d;
            DB_FAST_READ(src, d, 1);
            DB_FAST_WRITE(dst, d, 1);
        }
        if ((n & 2) != 0) {
            uint16_t d;
            DB_FAST_READ(src, d, 2);
            DB_FAST_WRITE(dst, d, 2);
        }
    } else if (n <= 8) {
        // n在[4, 8]，分别从src到dst和src + n - 4到dst + n - 4复制4个字节,
        // 其实际效果等价于复制从src到dst复制n个字节。
        // 后续n <= 16至n <= 64的分支采取的类似的优化。
        uint32_t d0, d1;

        const uint8_t *srcTail = (const uint8_t *)((uintptr_t)src + (n - 4));
        DB_FAST_READ(src, d0, 4);
        DB_FAST_READ(srcTail, d1, 4);

        uint8_t *dstTail = (uint8_t *)((uintptr_t)dst + (n - 4));
        DB_FAST_WRITE(dst, d0, 4);
        DB_FAST_WRITE(dstTail, d1, 4);
    } else if (n <= 16) {
        uint64_t d0, d1;

        const uint8_t *srcTail = (const uint8_t *)((uintptr_t)src + (n - 8));
        DB_FAST_READ(src, d0, 8);
        DB_FAST_READ(srcTail, d1, 8);

        uint8_t *dstTail = (uint8_t *)((uintptr_t)dst + (n - 8));
        DB_FAST_WRITE(dst, d0, 8);
        DB_FAST_WRITE(dstTail, d1, 8);
    } else if (n <= 32) {
        struct {
            uint64_t d0, d1;
        } d0, d1;

        const uint8_t *srcTail = (const uint8_t *)((uintptr_t)src + (n - 16));
        DB_FAST_READ(src, d0, 16);
        DB_FAST_READ(srcTail, d1, 16);

        uint8_t *dstTail = (uint8_t *)((uintptr_t)dst + (n - 16));
        DB_FAST_WRITE(dst, d0, 16);
        DB_FAST_WRITE(dstTail, d1, 16);
    } else if (n <= 64) {
        uint8_t __attribute__((vector_size(32))) d0, d1;  // d0和d1为32字节的数据

        const uint8_t *srcTail = (const uint8_t *)((uintptr_t)src + (n - 32));
        DB_FAST_READ(src, d0, 32);
        DB_FAST_READ(srcTail, d1, 32);

        uint8_t *dstTail = (uint8_t *)((uintptr_t)dst + (n - 32));
        DB_FAST_WRITE(dst, d0, 32);
        DB_FAST_WRITE(dstTail, d1, 32);
    } else {
        DbMemcpy64Plus(dst, src, n);
    }
}

#undef DB_FAST_READ
#undef DB_FAST_WRITE

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* DB_MEMCPY_H */
