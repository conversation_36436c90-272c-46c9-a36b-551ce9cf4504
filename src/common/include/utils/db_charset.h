/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: db_charset.h
 * Description: header file for character set
 * Author:
 * Create: 2020-1-20
 */

#ifndef DB_CHARSET_H
#define DB_CHARSET_H

#include "db_string_utf8.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

typedef enum CharSetType {
    DB_CHARSET_UTF8 = 0,
    DB_CHARSET_GBK = 1,
    DB_CHARSET_MAX = 2,
} DbCharSetTypeE;

GMDB_EXPORT bool DbTextLike(const TextT *text1, const TextT *text2, DbCharSetTypeE type);
GMDB_EXPORT Status DbTextLikeEscape(const char *strInput, const char *strEnd, char *wildStr, const char *wildEnd,
    char escape, int32_t *cmpRet, DbCharSetTypeE type);

/* Get bytes of single str */
typedef Status (*DbCharSetStrBytesFunc)(const char *str, uint32_t len, uint32_t *bytes);
/* Get bytes of single str reversed */
typedef Status (*DbCharSetReverseStrBytesFunc)(const char *str, uint32_t len, uint32_t *bytes);
/* like */
typedef bool (*DbCharSetTextLikeFunc)(const TextT *text1, const TextT *text2);
/* escape like */
typedef Status (*DbCharSetTextLikeEscapeFunc)(
    const char *str, const char *strEnd, char *wildStr, const char *wildEnd, char escape, int32_t *cmpRet);

typedef struct {
    DbCharSetStrBytesFunc strBytes;
    DbCharSetReverseStrBytesFunc reverseStrBytes;
    DbCharSetTextLikeFunc like;
    DbCharSetTextLikeEscapeFunc escapeLike;
} DbCharSetFuncT;

GMDB_EXPORT const DbCharSetFuncT *DbGetCharSetFunc(DbCharSetTypeE type);

#define DB_GET_CHARSET_ID DB_CHARSET_UTF8

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* DB_CHARSET_H */
