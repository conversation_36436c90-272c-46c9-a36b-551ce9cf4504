/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: db_perf_stat.h
 * Description: embedded performance statistics tool for cpu cycle calculation
 * Author: chendechen
 * Create: 2022-05-26
 */

#ifndef DB_PERF_STAT_H
#define DB_PERF_STAT_H

#include "adpt_rdtsc.h"
#include "adpt_cpu_info.h"
#include "db_log.h"
#include "gmc_errno.h"
#ifdef PERF_SAMPLE_STAT
#include "adpt_process_name.h"
#include "db_common_init.h"
#include "db_file.h"
#include "db_utils.h"
#include <stdio.h>
#endif  // PERF_SAMPLE_STAT

#ifdef __cplusplus
extern "C" {
#endif

#ifdef PERF_SAMPLE_STAT
/** AC设备打点工具 **/

#define CLIENT_SEND_LOG "/opt/vrpv8/home/<USER>"
#define CLIENT_SEND_ASYNC_LOG "/opt/vrpv8/home/<USER>"
#define CLIENT_RECV_LOG "/opt/vrpv8/home/<USER>"
#define SERVER_SEND_LOG "/opt/vrpv8/home/<USER>"
#define SERVER_RECV_LOG "/opt/vrpv8/home/<USER>"

#define MAX_PERF_DATA_SIZE 100000

typedef struct {
    bool init;
    uint32_t totalSize;
    uint32_t curPos;
    uint64_t *data;
} PerfDataT;

inline static void DbPerfAllocPerfData(PerfDataT *perfData)
{
    perfData->data = (uint64_t *)malloc(sizeof(uint64_t) * MAX_PERF_DATA_SIZE);
    (void)memset_s(perfData->data, sizeof(uint64_t) * MAX_PERF_DATA_SIZE, 0, sizeof(uint64_t) * MAX_PERF_DATA_SIZE);
    perfData->totalSize = MAX_PERF_DATA_SIZE;
    perfData->curPos = 0;
    perfData->init = true;
}

inline static void DbPerfFreePerfData(PerfDataT *perfDatas, uint32_t connIdx)
{
    if (perfDatas[connIdx].init) {
        free(perfDatas[connIdx].data);
        perfDatas[connIdx].data = NULL;
        perfDatas[connIdx].curPos = 0;
        perfDatas[connIdx].totalSize = 0;
        perfDatas[connIdx].init = false;
    }
}

inline static void DbPerfResetPerfData(PerfDataT *perfDatas, uint32_t connIdx)
{
    if (perfDatas[connIdx].init) {
        (void)memset_s(perfDatas[connIdx].data, sizeof(uint64_t) * MAX_PERF_DATA_SIZE, 0x00,
            sizeof(uint64_t) * MAX_PERF_DATA_SIZE);
        perfDatas[connIdx].totalSize = MAX_PERF_DATA_SIZE;
        perfDatas[connIdx].curPos = 0;
    }
}

inline static void DbPerfAppendData(PerfDataT *perfDatas, uint64_t cycles, uint32_t connIdx)
{
    if (!perfDatas[connIdx].init) {
        DbPerfAllocPerfData(&perfDatas[connIdx]);
    }
    if (perfDatas[connIdx].curPos >= perfDatas[connIdx].totalSize) {
        return;
    }
    perfDatas[connIdx].data[perfDatas[connIdx].curPos++] = cycles;
}

static void DbPerfWriteDataToFileByIdx(const char *filePath, PerfDataT *perfDatas, uint32_t connIdx)
{
    PerfDataT perfData = perfDatas[connIdx];
    if (!perfData.init) {
        return;
    }

    if (perfData.curPos == 0) {
        return;
    }

    FILE *fp1 = NULL;
    fp1 = fopen(filePath, "a+");
    if (fp1 == NULL) {
        printf("fopen failed, fp is NULL!\n");
        return;
    }

    (void)fprintf(fp1, "conn idx %u\n", connIdx);
    for (uint32_t i = 0; i < perfData.curPos; ++i) {
        (void)fprintf(fp1, "%lu", perfData.data[i]);
        (void)fprintf(fp1, "\n");
    }
    (void)fclose(fp1);
    DbPerfResetPerfData(perfDatas, connIdx);
}

inline static void DbPerfWriteDataToFile(const char *filePath, PerfDataT *perfDatas)
{
    FILE *fp1 = NULL;
    fp1 = fopen(filePath, "a+");
    if (fp1 == NULL) {
        printf("fopen failed, fp is NULL!\n");
        return;
    }

    for (uint32_t i = 0; i < MAX_CONN_NUM; ++i) {
        PerfDataT perfData = perfDatas[i];
        if (!perfData.init) {
            continue;
        }

        if (perfData.curPos == 0) {
            continue;
        }

        fprintf(fp1, "conn idx %u\n", i);
        for (uint32_t j = 0; j < perfData.curPos; ++j) {
            fprintf(fp1, "%lu", perfData.data[j]);
            fprintf(fp1, "\n");
        }
        DbPerfResetPerfData(perfDatas, i);
    }

    fclose(fp1);
}
#endif  // PERF_SAMPLE_STAT

#ifdef PERF_SAMPLE_STAT
#define DB_DEF_PERF_STAT_POINT(name) \
    DB_THREAD_LOCAL uint64_t g_tlsExecTimes##name = 0, g_tlsToalCycle##name = 0, g_tlsStartTime##name = 0
#define DB_EXTERN_PERF_STAT_POINT(name) \
    extern DB_THREAD_LOCAL uint64_t g_tlsExecTimes##name, g_tlsToalCycle##name, g_tlsStartTime##name
#define DB_START_TEST_CPU_CYCLES(name)
#define DB_STOP_TEST_CPU_CYCLES(name)
#else
#define DB_DEF_PERF_STAT_POINT(name)
#define DB_EXTERN_PERF_STAT_POINT(name)
#define DB_START_TEST_CPU_CYCLES(name)
#define DB_STOP_TEST_CPU_CYCLES(name)
#endif

#ifdef __cplusplus
}
#endif

#endif
