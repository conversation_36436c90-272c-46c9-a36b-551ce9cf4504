/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 * File Name: db_endian_trans.h
 * Description: head of trans data endian
 * Author:
 * Create: 2024-12-31
 */

#ifndef DB_ENDIAN_TRANS_H
#define DB_ENDIAN_TRANS_H

#include <stdint.h>
#include <stdbool.h>
#include "adpt_define.h"

#ifdef __cplusplus
extern "C" {
#endif

// 与DB_FILE_FORMAT_TYPE保持一致
#define DB_BIG_ENDIAN_TYPE 0
#define DB_LITTLE_ENDIAN_TYPE 1
#define DB_DEFAULT_ENDIAN_TYPE 2

/**
 * @brief 将二字节数据转换大小端。传入小端,返回大端;传入大端,返回小端。
 * @param val [in]: 待转换的数据。
 * @return 转换大小端后的二字节数据
 */
GMDB_EXPORT uint16_t DbEndianTransForBit16(uint16_t val);

/**
 * @brief 将四字节数据转换大小端。传入小端,返回大端;传入大端,返回小端。
 * @param val [in]: 待转换的数据。
 * @return 转换大小端后的四字节数据
 */
GMDB_EXPORT uint32_t DbEndianTransForBit32(uint32_t val);

/**
 * @brief 将八字节数据转换大小端。传入小端,返回大端;传入大端,返回小端。
 * @param val [in]: 待转换的数据。
 * @return 转换大小端后的八字节数据
 */
GMDB_EXPORT uint64_t DbEndianTransForBit64(uint64_t val);

/**
 * @brief 获取当前机器的大小端情况。
 * @return 0 大端 1 小端
 */
uint8_t DbGetCurrEndian(void);

/**
 * @brief 根据传入的类型判断是否需要转换。
 * @param endianType [in]: 数据的大小端类型。
 * @return true 要转 false 不转
 */
GMDB_EXPORT bool DbNeedSwapEndian(uint8_t endianType);

/**
 * @brief 根据传入的类型判断是否需要转换。
 * @param val [in]: 待转换的数据。
 * @param isNeedSwap [in]: 是否需要转换。
 * @return 转换结果
 */
uint16_t DbTryTransEndian16(uint16_t val, bool isNeedSwap);

/**
 * @brief 根据传入的类型判断是否需要转换。
 * @param val [in]: 待转换的数据。
 * @param isNeedSwap [in]: 是否需要转换。
 * @return 转换结果
 */
uint32_t DbTryTransEndian32(uint32_t val, bool isNeedSwap);

/**
 * @brief 根据传入的类型判断是否需要转换。
 * @param val [in]: 待转换的数据。
 * @param isNeedSwap [in]: 是否需要转换。
 * @return 转换结果
 */
uint64_t DbTryTransEndian64(uint64_t val, bool isNeedSwap);

/**
 * @brief 将指针数据转换大小端。传入小端,返回大端;传入大端,返回小端。
 * @param val [in]: 待转换的数据。
 * @return 转换大小端后的指针数据
 */
inline static uintptr_t DbEndianTransForAddr(uintptr_t val)
{
    if (sizeof(uintptr_t) == sizeof(uint32_t)) {
        return DbEndianTransForBit32((uint32_t)val);
    }
    return (uintptr_t)DbEndianTransForBit64(val);
}

#ifdef __cplusplus
}
#endif

#endif /* DB_ENDIAN_TRANS_H */
