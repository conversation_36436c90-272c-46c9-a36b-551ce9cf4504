/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: db_lvq.h
 * Description: header file for quantization
 * Author:
 * Create: 2024-8-26
 */

#ifndef DB_QUANTIZATION_H
#define DB_QUANTIZATION_H

#include <inttypes.h>
#include "db_lvq.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

typedef enum {
    DB_QUANT_TYPE_LVQ = 0,
    DB_QUANT_TYPE_PQ,
    DB_QUANT_TYPE_BUTT,
} DbVectorQuantTypeE;

GMDB_EXPORT const char *DbGetVectorQuantStrByType(DbVectorQuantTypeE type);
GMDB_EXPORT DbVectorQuantTypeE DbGetVectorQuantTypeByStr(const char *str);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* DB_QUANTIZATION_H */
