/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: db_vector_distance.h
 * Description: header file for ANN vector distance
 * Author:
 * Create: 2024-8-26
 */

#ifndef DB_VECTOR_DISTANCE_H
#define DB_VECTOR_DISTANCE_H

#include <inttypes.h>
#include "adpt_define.h"
#include "db_dot_product.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

typedef enum {
    DB_VECTOR_METRIC_IP = 0,
    DB_VECTOR_METRIC_L2,
    DB_VECTOR_METRIC_COSINE,
    DB_VECTOR_METRIC_BUTT,
} DbVectorMetricE;

#define DB_GET_IP_FROM_DOT_PRODUCT(dp) ((float)1.0 - (dp))
#define DB_GET_COSINE_DIS_FROM_COSINE_SIMILARITY(cos) ((float)1.0 - (cos))

GMDB_EXPORT float DbVectorSquareSumFloat(uint32_t dim, const float *vec);
GMDB_EXPORT float DbInnerProductFloat(uint32_t dim, const float *vec1, const float *vec2);
GMDB_EXPORT float DbL2WithSquareFloat(uint32_t dim, const float *vec1, float sqV1, const float *vec2, float sqV2);
GMDB_EXPORT float DbL2Float(uint32_t dim, const float *vec1, const float *vec2);
GMDB_EXPORT float DbCosineWithSquareFloat(uint32_t dim, const float *vec1, float sqV1, const float *vec2, float sqV2);
GMDB_EXPORT float DbCosineFloat(uint32_t dim, const float *vec1, const float *vec2);
GMDB_EXPORT float DbVectorDistanceFloat(uint32_t dim, const float *vec1, const float *vec2, DbVectorMetricE metric);

GMDB_EXPORT const char *DbGetVectorMetricStrByType(DbVectorMetricE type);
GMDB_EXPORT DbVectorMetricE DbGetVectorMetricTypeByStr(const char *str);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* DB_VECTOR_DISTANCE_H */
