/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: db_inter_process_rwlatch.h
 * Description:
 * Author: linhuangcheng
 * Create: 2024/12/16
 */
#ifndef DB_INTER_PROCESS_RWLATCH_H
#define DB_INTER_PROCESS_RWLATCH_H
#include "adpt_define.h"
#include "adpt_types.h"
#include "db_internal_error.h"
#include "db_rwlatch.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef StatusInter (*RWLatchLatchWFn)(DbLatchT *latch, uint32_t intervalUs, DbCheckFn checkFn, void *checkArgs);
typedef StatusInter (*RWLatchLatchRFn)(DbLatchT *latch, uint32_t intervalUs, DbCheckFn checkFn, void *checkArgs);

typedef struct {
    RWLatchLatchWFn rLatchFn;
    RWLatchLatchWFn wLatchFn;
    DbCheckFn checkFn;
    uint32_t intervalUs;
    void *checkArgs;
} InterProcRWLatchUtilT;

void DbInterProcSetCurrUtils(const InterProcRWLatchUtilT *utils);

const InterProcRWLatchUtilT *DbInterProcGetCurrUtils(void);

static inline bool DbIsRWLatchWithCheck(void)
{
    const InterProcRWLatchUtilT *utils = DbInterProcGetCurrUtils();
    return (utils->checkFn != NULL);
}

// LCOV_EXCL_BR_START
static inline StatusInter InterProcRWLatchR(DbLatchT *latch, uint32_t intervalUs, DbCheckFn checkFn, void *checkArgs)
{
    DB_UNUSED(intervalUs);
    DB_UNUSED(checkFn);
    DB_UNUSED(checkArgs);
    DbRWLatchR(latch);
    return STATUS_OK_INTER;
}

static inline StatusInter InterProcRWLatchW(DbLatchT *latch, uint32_t intervalUs, DbCheckFn checkFn, void *checkArgs)
{
    DB_UNUSED(intervalUs);
    DB_UNUSED(checkFn);
    DB_UNUSED(checkArgs);
    DbRWLatchW(latch);
    return STATUS_OK_INTER;
}
// LCOV_EXCL_BR_STOP

static inline StatusInter InterProcRWLatchRWithCheck(
    DbLatchT *latch, uint32_t intervalUs, DbCheckFn checkFn, void *checkArgs)
{
    while (!DbRWTimedLatchR(latch, intervalUs)) {
        Status ret = (*checkFn)(checkArgs);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return DbGetStatusInterErrno(ret);
        }
    }
    return STATUS_OK_INTER;
}

static inline StatusInter InterProcRWLatchWWithCheck(
    DbLatchT *latch, uint32_t intervalUs, DbCheckFn checkFn, void *checkArgs)
{
    while (!DbRWTimedLatchW(latch, intervalUs)) {
        Status ret = (*checkFn)(checkArgs);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return DbGetStatusInterErrno(ret);
        }
    }
    return STATUS_OK_INTER;
}

__attribute__((unused)) static void DbInterProcRWLatchFnInit(InterProcRWLatchUtilT *lockFns)
{
    DB_POINTER(lockFns);
    lockFns->intervalUs = 0u;
    lockFns->checkFn = NULL;
    lockFns->checkArgs = NULL;
    lockFns->rLatchFn = InterProcRWLatchR;
    lockFns->wLatchFn = InterProcRWLatchW;
}

__attribute__((unused)) static void DbInterProcRWLatchWithCheckFnInit(
    InterProcRWLatchUtilT *lockFns, uint32_t intervalUs, DbCheckFn checkFn, void *checkArgs)
{
    DB_ASSERT(intervalUs > 0u);
    DB_POINTER2(lockFns, checkFn);
    lockFns->intervalUs = intervalUs;
    lockFns->checkFn = checkFn;
    lockFns->checkArgs = checkArgs;
    lockFns->rLatchFn = InterProcRWLatchRWithCheck;
    lockFns->wLatchFn = InterProcRWLatchWWithCheck;
}

static inline __attribute__((always_inline)) void DbInterProcRWLatchInit(DbLatchT *latch)
{
    DB_POINTER(latch);
    DbRWLatchInit(latch);
}

static inline __attribute__((always_inline)) void DbInterProcRWUnlatchR(DbLatchT *latch)
{
    DB_POINTER(latch);
    DbRWUnlatchR(latch);
}

static inline __attribute__((always_inline)) void DbInterProcRWUnlatchW(DbLatchT *latch)
{
    DB_POINTER(latch);
    DbRWUnlatchW(latch);
}

static inline __attribute__((always_inline)) StatusInter DbInterProcRWLatchR(DbLatchT *latch)
{
    DB_POINTER(latch);
    const InterProcRWLatchUtilT *utils = DbInterProcGetCurrUtils();
    return (*utils->rLatchFn)(latch, utils->intervalUs, utils->checkFn, utils->checkArgs);
}

static inline __attribute__((always_inline)) StatusInter DbInterProcRWLatchW(DbLatchT *latch)
{
    DB_POINTER(latch);
    const InterProcRWLatchUtilT *utils = DbInterProcGetCurrUtils();
    return (*utils->wLatchFn)(latch, utils->intervalUs, utils->checkFn, utils->checkArgs);
}

// 需要资源释放时请勿使用此函数
#define RW_LATCH_WLATCH_RETURN_IF_FAILED(latch)              \
    do {                                                     \
        StatusInter latchRet = DbInterProcRWLatchW(latch);   \
        if (SECUREC_UNLIKELY(latchRet != STATUS_OK_INTER)) { \
            return latchRet;                                 \
        }                                                    \
    } while (0)

#define RW_LATCH_RLATCH_RETURN_IF_FAILED(latch)              \
    do {                                                     \
        StatusInter latchRet = DbInterProcRWLatchR(latch);   \
        if (SECUREC_UNLIKELY(latchRet != STATUS_OK_INTER)) { \
            return latchRet;                                 \
        }                                                    \
    } while (0)

#ifdef __cplusplus
}
#endif

#endif
