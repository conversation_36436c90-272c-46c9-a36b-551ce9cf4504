/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: db_mem_utils.h
 * Description: common memory operation utils
 * Author: zengchuanrui
 * Create: 2024-03-18
 */
#ifndef DB_MEM_UTILS_H
#define DB_MEM_UTILS_H

#include "db_mem_context.h"

#ifdef __cplusplus
extern "C" {
#endif
typedef ShmemPtrT (*DbMemCtxAllocFn)(DbMemCtxT *memCtx, uint32_t size);
typedef void (*DbMemCtxFreeFn)(DbMemCtxT *memCtx, ShmemPtrT shmPtr);
// conver ShmemPtrT to memory addr
typedef void *(*DbMemCtxShmptr2AddrFn)(const DbMemCtxT *ctx, ShmemPtrT shmPtr);
// conver ShmemPtrT to memory addr
typedef void *(*DbShmptr2AddrFn)(ShmemPtrT shmPtr);

// conver ShmemPtrT to memory addr
typedef void *(*DynPtr2MemAddrFn)(const DbMemCtxT *ctx, void *ptr);
// conver memory addr to ShmemPtrT
typedef ShmemPtrT (*MemAddr2PtrFn)(DbMemCtxT *ctx, void *addr);
// ptr 在非一写多读场景为本进程虚拟Addr, 在一写多读场景为ShmemPtrT通过调用MemPtrToMemAddr转换而来
typedef void *(*DynPtrShiftFn)(void *ptr, uint32_t offset);

// 数存大内存场景下，offset应支持到64位
typedef void *(*DynPtrShift64Fn)(void *ptr, uintptr_t offset);

typedef struct {
    DbMemCtxAllocFn memAlloc;
    DbMemCtxFreeFn memFree;
    DbShmptr2AddrFn shmPtr2addr;
    DbMemCtxShmptr2AddrFn memCtxShmPtr2addr;
    // old file map adapt function
    DynPtr2MemAddrFn dynPtr2addr;
    DynPtrShiftFn dynPtrShift;
    DynPtrShift64Fn dynPtrShift64;
    // dynamic memory: seServerMemCtx
    // fileMap memory: FileMapMemCtx
    DbMemCtxT *memCtx;
    bool sharedMemory;
} MemUtilsT;

#ifdef SYS32BITS
// 32位平台只会用到前4字节
// 将动态内存的 shmPtr addr 转为本地内存 addr
static inline __attribute__((always_inline)) void *DbDynShmemPtrToAddr(ShmemPtrT ptr)
{
    if (ptr.offset != DB_INVALID_UINT32) {
        // 转换offset
        return (void *)((uintptr_t)(*(uint32_t *)&ptr.offset));
    }
    return NULL;
}

static inline __attribute__((always_inline)) ShmemPtrT DbAddrToDynShmemPtr(const void *addr)
{
    if (addr != NULL) {
        // 转换offset
        return (ShmemPtrT){.offset = (*(uint32_t *)(uintptr_t)&addr), .segId = 0};
    }
    return DB_INVALID_SHMPTR;
}
#else
// 将动态内存的 shmPtr addr 转为本地内存 addr
static inline __attribute__((always_inline)) void *DbDynShmemPtrToAddr(ShmemPtrT ptr)
{
    return DbIsShmPtrValid(ptr) ? (void *)((uintptr_t)(*(uint64_t *)&ptr)) : NULL;
}

static inline __attribute__((always_inline)) ShmemPtrT DbAddrToDynShmemPtr(const void *addr)
{
    return (addr != NULL) ? (*(ShmemPtrT *)(uintptr_t)&addr) : DB_INVALID_SHMPTR;
}
#endif

// 终端场景下用动态内存来模拟共享内存的内存申请接口，不修改返回值类型以保持接口定义不变，故此函数申请动态内存并返回动态内存的shmPtr
static inline ShmemPtrT DbDynShmemMemCtxAlloc(DbMemCtxT *memCtx, uint32_t size)
{
    DB_ASSERT(memCtx->memType == DB_DYNAMIC_MEMORY);
    void *addr = DbDynMemCtxAlloc(memCtx, size);
    return (addr != NULL) ? DbAddrToDynShmemPtr(addr) : DB_INVALID_SHMPTR;
}

static inline void DbDynShmemMemCtxFree(DbMemCtxT *memCtx, ShmemPtrT shmPtr)

{
    DbDynMemCtxFree(memCtx, DbDynShmemPtrToAddr(shmPtr));
}

static inline __attribute__((always_inline)) void *DbMemCtxDynShmemPtrToAddr(const DbMemCtxT *memCtx, ShmemPtrT ptr)
{
    DB_UNUSED(memCtx);
    return DbDynShmemPtrToAddr(ptr);
}

static inline void *DynamicPtr2MemAddr(const DbMemCtxT *ctx, void *ptr)
{
    DB_UNUSED(ctx);
    return ptr;
}

static inline void *DynamicMemAddrShift(void *ptr, uint32_t offset)
{
    return (void *)((uintptr_t)ptr + offset);
}

static inline void *DynamicMemAddrShift64(void *ptr, uintptr_t offset)
{
    return (void *)((uintptr_t)ptr + offset);
}

static inline void *DbWrappedMemCtxAllocAddr(DbMemCtxT *memCtx, uint32_t size, ShmemPtrT *shmPtr)
{
    ShmemPtrT ptr = DbDynShmemMemCtxAlloc(memCtx, size);
    void *memAddr = DbDynShmemPtrToAddr(ptr);
    if (SECUREC_UNLIKELY(memAddr == NULL)) {
        return NULL;
    }
    *shmPtr = ptr;
    return memAddr;
}

#ifdef __cplusplus
}
#endif

#endif /* DB_MEM_UTILS_H */
