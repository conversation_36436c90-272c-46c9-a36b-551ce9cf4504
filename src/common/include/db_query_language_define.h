/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: definitions for query language struct
 * Author: zhu<PERSON>min
 * Create: 2023-07-10
 */

#ifndef DB_QUERY_LANGUAGE_DEFINE_H
#define DB_QUERY_LANGUAGE_DEFINE_H

#include "adpt_types.h"
#include "db_list.h"
#include "db_text.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef DbValueT QueryTabRowT;

typedef struct {
    uint32_t maxLen;
    uint32_t len;
    void *data;
} QueryTupleFieldT;

typedef enum {
    QUERY_TYPE_UNDEFINED,
    QUERY_TYPE_DDL,
    QUERY_TYPE_DML,
    QUERY_TYPE_DQL,
    QUERY_TYPE_DCL,
    QUERY_TYPE_BUTT,
} QueryTypeE;

/**
 * 结果数据描述
 */
typedef struct {
    QueryTypeE queryType : 4;  // 需求未明确前先都设为UNKNOWN（多条语句提交时如何填入）查询类型：DDL\DML\DQL\DCL...
    bool hasResultSet;  // 是否包含结果集
    bool largeResult;   // 是否为大结果
} QueryDataDescT;

/**
 * 数据中的结果集描述
 */
typedef struct {
    uint32_t rowNum;     // DQL：当前结果集行数
    uint32_t columnNum;  // 结果集列数
    bool eof;            // 是否到达结尾
} QueryResultSetDescT;

/**
 * 列属性描述
 */
typedef struct QueryColumnProperty {
    DbDataTypeE dataType;  // 数据类型
    uint32_t dataLen;      // dataType为FIXED时的长度
    bool isPrimary;        // 是主键
    bool canNull;          // 可为空
    bool isSensitive;      // 是否敏感字段
    bool isValid;          // false：record, true: normal
} QueryColumnPropertyT;

/**
 * 列定义
 */
typedef struct QueryColumnDefine {
    QueryColumnPropertyT columnProperty;  // 列属性
    TextT columnName;                     // 列名
    TextT comments;                       // 注释
} QueryColumnDefineT;

typedef struct QueryResultSet {
    QueryResultSetDescT qryResultDesc;
    DbListT colDefines;  // all rows share the same column defines, i.e. QueryColumnDefineT *
    DbListT rows;        // each row represents a DmValueT array, i.e. QueryTabRowT
    bool tryShallowCopy;
} QueryResultSetT;

typedef struct QueryResult {
    QueryDataDescT dataDesc;  // Data描述
    uint32_t affectedNum;     // DDL:受影响的表数量；DML：受影响行数
    QueryResultSetT resultSet;
} QueryResultT;

typedef struct QueryResultList {
    DbListT results;  // store query result of multiple stmts, i.e. QueryResultT *
    DbMemCtxT *memCtx;
    bool isReturning;
} QueryResultListT;

#ifdef __cplusplus
}
#endif

#endif /* DB_QUERY_LANGUAGE_DEFINE_H */
