/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: log for haotian
 * Author: jinfanglin
 * Create: 2025-03-10
 */

#ifndef DB_LOG_HAOTIAN_H
#define DB_LOG_HAOTIAN_H

#include "db_log_base.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#ifdef IDS_HAOTIAN
#define LOG_AUDIT(userInfo, resource, evtType, evtResult, evtDesc, ...) \
    DbAuditWrite(userInfo, resource, evtType, (int32_t)evtResult, evtDesc, ##__VA_ARGS__)

#define LOG_RUN_INFO_CUSTOM(format, ...) \
    LOG_WRITE_CUSTOM(true, DB_LOG_TYPE_RUN, DB_LOG_LVL_INFO, LOG_NO_ERRORCODE, format, ##__VA_ARGS__)
#define LOG_RUN_DBG_CUSTOM(format, ...) \
    LOG_WRITE_CUSTOM(true, DB_LOG_TYPE_RUN, DB_LOG_LVL_DBG, LOG_NO_ERRORCODE, format, ##__VA_ARGS__)
#define LOG_RUN_WARN_CUSTOM(errCode, format, ...) \
    LOG_WRITE_CUSTOM(true, DB_LOG_TYPE_RUN, DB_LOG_LVL_WARN, errCode, format, ##__VA_ARGS__)
#define LOG_RUN_ERROR_CUSTOM(errCode, format, ...) \
    LOG_WRITE_CUSTOM(true, DB_LOG_TYPE_RUN, DB_LOG_LVL_ERR, errCode, format, ##__VA_ARGS__)
#define LOG_RUN_EMRG_CUSTOM(errCode, format, ...) \
    LOG_WRITE_CUSTOM(true, DB_LOG_TYPE_RUN, DB_LOG_LVL_EMRG, errCode, format, ##__VA_ARGS__)

#define LOG_OP_INFO_CUSTOM(format, ...) \
    LOG_WRITE_CUSTOM(true, DB_LOG_TYPE_OP, DB_LOG_LVL_INFO, LOG_NO_ERRORCODE, format, ##__VA_ARGS__)
#define LOG_OP_WARN_CUSTOM(errCode, format, ...) \
    LOG_WRITE_CUSTOM(true, DB_LOG_TYPE_OP, DB_LOG_LVL_WARN, errCode, format, ##__VA_ARGS__)
#define LOG_OP_ERROR_CUSTOM(errCode, format, ...) \
    LOG_WRITE_CUSTOM(true, DB_LOG_TYPE_OP, DB_LOG_LVL_ERR, errCode, format, ##__VA_ARGS__)
#define LOG_OP_DBG_CUSTOM(format, ...) \
    LOG_WRITE_CUSTOM(true, DB_LOG_TYPE_OP, DB_LOG_LVL_DBG, LOG_NO_ERRORCODE, format, ##__VA_ARGS__)
#define LOG_OP_EMRG_CUSTOM(errCode, format, ...) \
    LOG_WRITE_CUSTOM(true, DB_LOG_TYPE_OP, DB_LOG_LVL_EMRG, errCode, format, ##__VA_ARGS__)

// 不进行日志折叠的日志宏
#define LOG_RUN_ERROR_CUSTOM_UNFOLD(errCode, format, ...) \
    LOG_WRITE_CUSTOM(false, DB_LOG_TYPE_RUN, DB_LOG_LVL_ERR, errCode, format, ##__VA_ARGS__)
#define LOG_RUN_INFO_CUSTOM_UNFOLD(format, ...) \
    LOG_WRITE_CUSTOM(false, DB_LOG_TYPE_RUN, DB_LOG_LVL_INFO, LOG_NO_ERRORCODE, format, ##__VA_ARGS__)
#define LOG_RUN_WARN_CUSTOM_UNFOLD(errCode, format, ...) \
    LOG_WRITE_CUSTOM(false, DB_LOG_TYPE_RUN, DB_LOG_LVL_WARN, errCode, format, ##__VA_ARGS__)
#define LOG_RUN_DBG_CUSTOM_UNFOLD(format, ...) \
    LOG_WRITE_CUSTOM(false, DB_LOG_TYPE_RUN, DB_LOG_LVL_DBG, LOG_NO_ERRORCODE, format, ##__VA_ARGS__)
#define LOG_RUN_EMRG_CUSTOM_UNFOLD(errCode, format, ...) \
    LOG_WRITE_CUSTOM(false, DB_LOG_TYPE_RUN, DB_LOG_LVL_EMRG, errCode, format, ##__VA_ARGS__)
#define LOG_OP_DBG_CUSTOM_UNFOLD(format, ...) \
    LOG_WRITE_CUSTOM(false, DB_LOG_TYPE_OP, DB_LOG_LVL_DBG, LOG_NO_ERRORCODE, format, ##__VA_ARGS__)
#define LOG_OP_INFO_CUSTOM_UNFOLD(format, ...) \
    LOG_WRITE_CUSTOM(false, DB_LOG_TYPE_OP, DB_LOG_LVL_INFO, LOG_NO_ERRORCODE, format, ##__VA_ARGS__)
#define LOG_OP_WARN_CUSTOM_UNFOLD(errCode, format, ...) \
    LOG_WRITE_CUSTOM(false, DB_LOG_TYPE_OP, DB_LOG_LVL_WARN, errCode, format, ##__VA_ARGS__)
#define LOG_OP_ERROR_CUSTOM_UNFOLD(errCode, format, ...) \
    LOG_WRITE_CUSTOM(false, DB_LOG_TYPE_OP, DB_LOG_LVL_ERR, errCode, format, ##__VA_ARGS__)

// 仅在debug模式下生效的日志宏
#ifndef NDEBUG
#define LOG_DEBUG_INFO_CUSTOM(format, ...) \
    LOG_WRITE_CUSTOM(true, DB_LOG_TYPE_DEBUG, DB_LOG_LVL_INFO, LOG_NO_ERRORCODE, format, ##__VA_ARGS__)
#define LOG_DEBUG_DBG_CUSTOM(format, ...) \
    LOG_WRITE_CUSTOM(true, DB_LOG_TYPE_DEBUG, DB_LOG_LVL_DBG, LOG_NO_ERRORCODE, format, ##__VA_ARGS__)
#define LOG_DEBUG_WARN_CUSTOM(errCode, format, ...) \
    LOG_WRITE_CUSTOM(true, DB_LOG_TYPE_DEBUG, DB_LOG_LVL_WARN, errCode, format, ##__VA_ARGS__)
#define LOG_DEBUG_ERROR_CUSTOM(errCode, format, ...) \
    LOG_WRITE_CUSTOM(true, DB_LOG_TYPE_DEBUG, DB_LOG_LVL_ERR, errCode, format, ##__VA_ARGS__)
// 关闭日志折叠
#define LOG_DEBUG_DBG_CUSTOM_UNFOLD(format, ...) \
    LOG_WRITE_CUSTOM(false, DB_LOG_TYPE_DEBUG, DB_LOG_LVL_DBG, LOG_NO_ERRORCODE, format, ##__VA_ARGS__)
#define LOG_DEBUG_INFO_CUSTOM_UNFOLD(format, ...) \
    LOG_WRITE_CUSTOM(false, DB_LOG_TYPE_DEBUG, DB_LOG_LVL_INFO, LOG_NO_ERRORCODE, format, ##__VA_ARGS__)
#define LOG_DEBUG_WARN_CUSTOM_UNFOLD(errCode, format, ...) \
    LOG_WRITE_CUSTOM(false, DB_LOG_TYPE_DEBUG, DB_LOG_LVL_WARN, errCode, format, ##__VA_ARGS__)
#define LOG_DEBUG_ERROR_CUSTOM_UNFOLD(errCode, format, ...) \
    LOG_WRITE_CUSTOM(false, DB_LOG_TYPE_DEBUG, DB_LOG_LVL_ERR, errCode, format, ##__VA_ARGS__)
#define LOG_DEBUG_EMRG_CUSTOM_UNFOLD(errCode, format, ...) \
    LOG_WRITE_CUSTOM(false, DB_LOG_TYPE_DEBUG, DB_LOG_LVL_EMRG, errCode, format, ##__VA_ARGS__)
#define LOG_DEBUG_EMRG_CUSTOM(errCode, format, ...) \
    LOG_WRITE_CUSTOM(true, DB_LOG_TYPE_DEBUG, DB_LOG_LVL_EMRG, errCode, format, ##__VA_ARGS__)
#else
#define LOG_DEBUG_INFO_CUSTOM(format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#define LOG_DEBUG_DBG_CUSTOM(format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#define LOG_DEBUG_WARN_CUSTOM(errCode, format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#define LOG_DEBUG_ERROR_CUSTOM(errCode, format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#define LOG_DEBUG_DBG_CUSTOM_UNFOLD(format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#define LOG_DEBUG_INFO_CUSTOM_UNFOLD(format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#define LOG_DEBUG_WARN_CUSTOM_UNFOLD(errCode, format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#define LOG_DEBUG_ERROR_CUSTOM_UNFOLD(errCode, format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#define LOG_DEBUG_EMRG_CUSTOM_UNFOLD(errCode, format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#define LOG_DEBUG_EMRG_CUSTOM(errCode, format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#endif

#define LOG_RUN_ERROR_CUSTOM_REENTRY_UNFOLD(errCode, format, ...) \
    LOG_WRITE_CUSTOM(false, DB_LOG_TYPE_SIGNAL, DB_LOG_LVL_ERR, errCode, format, ##__VA_ARGS__)  // 仅用于信号处理函数

#endif

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* DB_LOG_HAOTIAN_H */
