/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: file content
 * Author: gaohaiyang
 * Create: 2020-09-11
 */

#ifndef DB_LOG_H
#define DB_LOG_H
// 日志模块内部加锁代码统一使用不打印超时日志的锁，后缀为NoTimeoutLog，否则在特殊场景下可能导致栈溢出问题
#include "db_log_base.h"
#include "db_log_haotian.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define LOG_DEFAULT_FIELD_LENGTH 75       // 每条日志消息默认字段的长度
#define LOG_HEADER_SIZE_OF_AUDIT_MSG 120  // 审计日志去除evtDesc部分的大小预估
#define LOG_RESOURCE_SIZE_OF_AUDIT_MSG 256
#define LOG_MAX_SIZE_OF_AUDIT_MSG \
    ((1024 - (uint16_t)(LOG_HEADER_SIZE_OF_AUDIT_MSG)) - (uint16_t)(LOG_RESOURCE_SIZE_OF_AUDIT_MSG))

// 审计日志的evtDsec
typedef struct DbAuditEvtDesc {
    char *msg;
    uint16_t offset;
    uint16_t msgLen;
} DbAuditEvtDescT;

/*
 * description: 构建审计日志的事件描述
 * return {type} 成功返回STATUS_OK，否则返回错误码
 */
GMDB_EXPORT Status DbAppendAuditEvtDesc(DbAuditEvtDescT *evtDesc, const char *format, ...)
    __attribute__((format(printf, 2, 3)));

GMDB_EXPORT Status DbLogInitLocalSrv(void);

GMDB_EXPORT void DbLogInitLocalClt(uint32_t instanceId);

GMDB_EXPORT void DbLogUnInitLocal(void);

/*
 * description: 判断当前level级别的日志是否可写
 * param {level} level：日志级别
 */
GMDB_EXPORT bool DbLogCanWrite(uint32_t level);

#define YANG_TRACE_DML 1          // tarceLog for dml
#define YANG_TRACE_SUBTREE 2      // tarceLog for subtree
#define YANG_TRACE_DIFF 4         // tarceLog for diff
#define YANG_TRACE_VALIDATE 8     // tarceLog for validate
#define YANG_TRACE_PLANECACHE 16  // tarceLog for planecache
#define YANG_TRACE_SUBS 32        // tarceLog for subs
#define YANG_TRACE_OTHER 64       // tarceLog for other yang run log
/*
 * description: 判断当前mask值的trace日志是否可写
 * param {level} level：日志级别
 */
GMDB_EXPORT extern uint32_t g_gmdbTraceLogMask;

extern GMDB_EXPORT bool g_gmdbServerImportSourceFlag;

GMDB_EXPORT bool DbYangTraceLogEnable(uint32_t mask);

GMDB_EXPORT void DbSetYangTraceLogMask(uint32_t mask);

/*
 * description: 设置日志初始化标志
 */
GMDB_EXPORT void DbSetLogInitFlag(bool val);

/*
 * description: Tbm日志钩子函数
 */
GMDB_EXPORT void DbTbmLogger(uint32_t level, const char *formatStr, va_list args);

/*
 * description: 由上层传递可变参数列表args的写日志的统一接口
 * param {type} type：日志类型；其余参数：日志写入项
 */
GMDB_EXPORT void DbLogWriteWithArgs(bool needFold, DbLogTypeE type, const char *module, uint32_t level, int32_t errCode,
    const char *fileName, uint32_t lineNum, const char *funcName, const char *formatStr, va_list *args, bool isFormat);

/*
 * description: 设置日志头中的线程信息：connid-pid-processname
 * param {threadMsg} threadMsg：线程信息
 */
GMDB_EXPORT void DbLogSetThreadMsg(const char *threadMsg);

GMDB_EXPORT void DbLogSetThreadMsgFromConnMgr(char *threadMsgs);

GMDB_EXPORT void DbLogClearThreadMsgFromConnMgr(void);

GMDB_EXPORT void DbLogWriteAndSetLastError(bool needFold, DbLogTypeE type, const char *module, uint32_t level,
    int32_t errCode, const char *fileName, uint32_t lineNum, const char *funcName, const char *formatStr, ...);

/*
 * description: 启动日志初始化，使能启动日志。当前只有服务端在启动阶段需要调用。
 */
static inline Status DbLogInitBootLog(void)
{
    Status ret = DbBootLogInit();
    if (ret != GMERR_OK) {
        return ret;
    }
    DbAdptLogApiT logApis = {DbLogWrite, DbLogWriteNoFmt, DbAuditWrite};
    DbAdptLogRegLogApi(&logApis);
    DbAdptRegitstGetErrCodeDescApi(DbErrGetBrief);
    return GMERR_OK;
}

// Minimize size by removing funciton names from logs
#define __DB_FUNC_NAME__ " "

// common模块
#ifdef LOG_MODULE
#undef LOG_MODULE
#endif
#define LOG_MODULE "COMMON"
#define DTL_RUN_LINK_LOG_EXECUTE 1        // linkLog for executor
#define DTL_RUN_LINK_LOG_SERVICE 2        // linkLog for data service
#define DTL_RUN_LINK_LOG_DML_RESOURCE 4   // linkLog for time and memory in one DML
#define DTL_RUN_LINK_LOG_PLAN_RESOURCE 8  // linkLog for time and memory in each Plan
#define DTL_RUN_LINK_LOG_PUBSUB 16        // linkLog for sync pubsub
#define DTL_RUN_LINK_LOG_REQUEST 32       // linkLog for DML Request
#define DTL_RUN_LINK_LOG_DATA 64          // linkLog for delta data in each plan
#define DTL_RUN_LINK_LOG_DIF_PART_TIME_COST \
    128  // linkLog for different part time cost include:pre process, post process, total pubsub time cost

// DTL_RUN_LINK_LOG_IGNORE_BYTE and DTL_RUN_LINK_LOG_IGNORE_STR must used with DTL_RUN_LINK_LOG_DATA.
#define DTL_RUN_LINK_LOG_IGNORE_BYTE 256  // ignore byte field in delta and replace this field with '*'.
#define DTL_RUN_LINK_LOG_IGNORE_STR 512   // ignore str field in delta and replace this field with '*'.

#define DTL_RUN_LINK_LOG_SESSION_ID_OFFSET 32
#define DTL_RUN_LINK_LOG_WORKER_ID_OFFSET 16
GMDB_EXPORT extern DB_THREAD_LOCAL uint64_t g_dtlRequestId;  // 一条datalog请求的标识，构造方式为：sessionId | (自增id)
GMDB_EXPORT extern DB_THREAD_LOCAL bool g_gmdbIsPrintLog;

#define WRITE_LOG_FILE_WITH_MS(fmt, ...)                                                               \
    do {                                                                                               \
        struct timeval curTime;                                                                        \
        gettimeofday(&curTime, NULL);                                                                  \
        int milli = curTime.tv_usec / 1000;                                                            \
        char time_secs_str[20] = {0};                                                                  \
        struct tm nowTime;                                                                             \
        localtime_r(&curTime.tv_sec, &nowTime);                                                        \
        strftime(time_secs_str, sizeof(time_secs_str), "%Y-%m-%d %H:%M:%S", &nowTime);                 \
        char time_str[24] = {0};                                                                       \
        snprintf_s(time_str, sizeof(time_str), sizeof(time_str) - 1, "%s:%03d", time_secs_str, milli); \
        char space[] = " ";                                                                            \
        FILE *fp = NULL;                                                                               \
        fp = fopen("/OSM/component/ukre/data/db.log", "a+");                                           \
        if (fp == NULL) {                                                                              \
            break;                                                                                     \
        }                                                                                              \
        fputs(time_str, fp);                                                                           \
        fputs(space, fp);                                                                              \
        fprintf(fp, fmt, ##__VA_ARGS__);                                                               \
        fprintf(fp, "\n");                                                                             \
        fflush(fp);                                                                                    \
        fclose(fp);                                                                                    \
    } while (0)

// 使用该宏写日志到文件，仅设备问题定位场景使用
#define WRITE_LOG_FILE(fmt, ...)                                                  \
    do {                                                                          \
        time_t now = time(NULL);                                                  \
        char timeStr[20];                                                         \
        char space[] = " ";                                                       \
        strftime(timeStr, sizeof(timeStr), "%Y-%m-%d %H:%M:%S", localtime(&now)); \
        FILE *fp = NULL;                                                          \
        fp = fopen("/opt/vrpv8/home/<USER>", "a+");                               \
        if (fp == NULL) {                                                         \
            printf("fopen failed, fp is NULL!\n");                                \
            break;                                                                \
        }                                                                         \
        fputs(timeStr, fp);                                                       \
        fputs(space, fp);                                                         \
        fprintf(fp, "file:%s:%d |%s|", __FILE__, __LINE__, __DB_FUNC_NAME__);     \
        fprintf(fp, fmt, ##__VA_ARGS__);                                          \
        fprintf(fp, "\n");                                                        \
        fclose(fp);                                                               \
    } while (0)

// 光启场景将日志输出的db.log中
#define YANG_TRACE_LOG(format, ...)                          \
    do {                                                     \
        DB_LOG_WARN_UNFOLD(GMERR_OK, format, ##__VA_ARGS__); \
        WRITE_LOG_FILE(format, ##__VA_ARGS__);               \
    } while (0)

// 使用配置项来开启yang追踪日志的打印
#define YANG_TRACE_MASK_LOG(mask, format, ...) \
    do {                                       \
        if (!DbYangTraceLogEnable(mask)) {     \
            break;                             \
        }                                      \
        YANG_TRACE_LOG(format, ##__VA_ARGS__); \
    } while (0)

void DbLogSetWriteLog(bool flag);
bool DbLogGetWriteLog(void);

// 审计日志相关日志宏
#define DB_LOG_AUDIT(userInfo, resource, evtType, evtResult, evtDesc, ...) \
    DbAuditWrite(userInfo, resource, evtType, (int32_t)evtResult, evtDesc, ##__VA_ARGS__)

// 日志折叠相关日志宏
#ifndef NDEBUG
// 仅在debug模式下生效的日志宏
#define DB_LOG_DBG_INFO(format, ...)                                                                       \
    DbLogWrite(true, DB_LOG_TYPE_DEBUG, LOG_MODULE, DB_LOG_LVL_INFO, LOG_NO_ERRORCODE, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#define DB_LOG_DBG_DEBUG(format, ...)                                                                     \
    DbLogWrite(true, DB_LOG_TYPE_DEBUG, LOG_MODULE, DB_LOG_LVL_DBG, LOG_NO_ERRORCODE, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#define DB_LOG_DBG_WARN(errCode, format, ...)                                                                       \
    DbLogWrite(true, DB_LOG_TYPE_DEBUG, LOG_MODULE, DB_LOG_LVL_WARN, errCode, __FILE__, __LINE__, __DB_FUNC_NAME__, \
        format, ##__VA_ARGS__)
#define DB_LOG_DBG_ERROR(errCode, format, ...)                                                                     \
    DbLogWrite(true, DB_LOG_TYPE_DEBUG, LOG_MODULE, DB_LOG_LVL_ERR, errCode, __FILE__, __LINE__, __DB_FUNC_NAME__, \
        format, ##__VA_ARGS__)
#define DB_LOG_DBG_DEBUG_UNFOLD(format, ...)                                                               \
    DbLogWrite(false, DB_LOG_TYPE_DEBUG, LOG_MODULE, DB_LOG_LVL_DBG, LOG_NO_ERRORCODE, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#else
#define DB_LOG_DBG_INFO(format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#define DB_LOG_DBG_DEBUG(format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#define DB_LOG_DBG_WARN(errCode, format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#define DB_LOG_DBG_ERROR(errCode, format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#define DB_LOG_DBG_DEBUG_UNFOLD(format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#endif

#define DB_LOG_DEBUG(format, ...)                                                                       \
    DbLogWrite(true, DB_LOG_TYPE_RUN, LOG_MODULE, DB_LOG_LVL_DBG, LOG_NO_ERRORCODE, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#define DB_LOG_INFO(format, ...)                                                                         \
    DbLogWrite(true, DB_LOG_TYPE_RUN, LOG_MODULE, DB_LOG_LVL_INFO, LOG_NO_ERRORCODE, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#define DB_LOG_WARN(errCode, format, ...)                                                                \
    DbLogWrite(true, DB_LOG_TYPE_RUN, LOG_MODULE, DB_LOG_LVL_WARN, (int32_t)errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#define DB_LOG_ERROR(errCode, format, ...)                                                              \
    DbLogWrite(true, DB_LOG_TYPE_RUN, LOG_MODULE, DB_LOG_LVL_ERR, (int32_t)errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#define DB_LOG_EMRG(errCode, format, ...)                                                                \
    DbLogWrite(true, DB_LOG_TYPE_RUN, LOG_MODULE, DB_LOG_LVL_EMRG, (int32_t)errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)

#define DB_OP_LOG_EMRG(errCode, format, ...)                                                            \
    DbLogWrite(true, DB_LOG_TYPE_OP, LOG_MODULE, DB_LOG_LVL_EMRG, (int32_t)errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)

// 关闭日志折叠相关日志宏
#define DB_LOG_INFO_UNFOLD(format, ...)                                                                   \
    DbLogWrite(false, DB_LOG_TYPE_RUN, LOG_MODULE, DB_LOG_LVL_INFO, LOG_NO_ERRORCODE, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#define DB_LOG_WARN_UNFOLD(errCode, format, ...)                                                          \
    DbLogWrite(false, DB_LOG_TYPE_RUN, LOG_MODULE, DB_LOG_LVL_WARN, (int32_t)errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#define DB_LOG_ERROR_UNFOLD(errCode, format, ...)                                                        \
    DbLogWrite(false, DB_LOG_TYPE_RUN, LOG_MODULE, DB_LOG_LVL_ERR, (int32_t)errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#define DB_LOG_ERROR_SIGNAL_UNFOLD(errCode, format, ...)                                                    \
    DbLogWrite(false, DB_LOG_TYPE_SIGNAL, LOG_MODULE, DB_LOG_LVL_ERR, (int32_t)errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)  // 仅用于信号处理函数

#define DTL_RUN_LINK_LOG(mask, format, ...)                                                         \
    do {                                                                                            \
        int32_t runLinkLevel = DbCfgGetInt32Lite(DB_CFG_DATALOG_RUN_LINK_LOG, NULL);                \
        if (((uint32_t)runLinkLevel & (uint32_t)mask) != 0) {                                       \
            DB_LOG_WARN(GMERR_OK, "|requestId=%" PRIu64 "|" format, g_dtlRequestId, ##__VA_ARGS__); \
        }                                                                                           \
    } while (0)

//  根据g_gmdbIsPrintLog判断是否打印日志（按需建表场景，第一次请求处理的时候，表不存在是正常的，no need to write log）
#define DB_LOG_ERROR_ON_DEMAND(errCode, format, ...)      \
    do {                                                  \
        if (g_gmdbIsPrintLog) {                           \
            DB_LOG_ERROR(errCode, format, ##__VA_ARGS__); \
        }                                                 \
    } while (0)

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* DB_LOG_H */
