/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: DB的内部错误码定义
 * Author: chendechen
 * Create: 2021-06-10
 */
#ifndef DB_INTERNAL_ERROR_H
#define DB_INTERNAL_ERROR_H

#include "adpt_types.h"
#include "gmc_errno.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define ERROR_CODE_BIT_OFFSET 10

#define MAX_INTERNAL_ERR_CODE (1 << ERROR_CODE_BIT_OFFSET)  // 内部错误码用低10位，外部错误码用高22位

typedef enum StatusInternal {
    STATUS_OK_INTER = 0,
    // GMERR_NO_DATA
    NO_DATA_INTER = GMERR_NO_DATA << ERROR_CODE_BIT_OFFSET,

    NO_DATA_HEAP_ITEM_NOT_EXIST,
    NO_DATA_HEAP_ITEM_OFFSET_INVALID,
    NO_DATA_HEAP_PAGE_NOT_EXIST,
    INT_ERR_HEAP_LOB_DATA_INCOMPLETE,
    NO_DATA_HEAP_NOT_CREATED,

    NO_DATA_FIXED_HEAP_ITEM_NOT_EXIST,
    NO_DATA_FIXED_HEAP_PAGE_NOT_EXIST,

    NO_DATA_SE_HASH_LOOK_UP_FAILD,
    NO_DATA_SE_SECOND_HASH_OPEN_FETCH_FAILD,
    NO_DATA_SE_SECOND_HASH_FETCH_FAILD,
    NO_DATA_SE_SECOND_HASH_FETCH_FIRSTNODE_FAILD,
    NO_DATA_SE_SECOND_HASH_REMOVE_FAILD,
    NO_DATA_NULL_POINTER,
    NO_DATA_TIMER_RTOS_TIMER_NOT_FOUND,
    NO_DATA_CONFIG_FILE_NOT_EXIST,
    NO_DATA_FILE_NOT_EXIST,
    NO_DATA_FILE_SEEK,
    NO_DATA_ART_MEM_PAGE_RELEASED,
    NO_DATA_FNODE_NODESIZE_NOT_FOUND,
    NO_DATA_DATALOG_NO_SUBSCRIPTION,
    NO_DATA_UNDO_REC_HANDLE,
    NO_DATA_SE_DEVICE_NOT_EXIST,
    NO_DATA_BUFFER_POOL_IS_FULL,

    NO_DATA_END,

    UNRECOGNIZED_DB_VERSION_INTER = GMERR_UNRECOGNIZED_DB_VERSION << ERROR_CODE_BIT_OFFSET,

    FEATURE_NOT_SUPPORTED_INNER = GMERR_FEATURE_NOT_SUPPORTED << ERROR_CODE_BIT_OFFSET,

    // GMERR_DATA_EXCEPTION
    DATA_EXCEPTION_INTER = GMERR_DATA_EXCEPTION << ERROR_CODE_BIT_OFFSET,

    DATA_EXCEPTION_LPM_IDX_SHM_ADDR_INVALID,
    DATA_EXCEPTION_LPM_INVALID_VALUESLOT_TYPE,
    DATA_EXCEPTION_LPM_IDX_INVALID_KEY,
    DATA_EXCEPTION_ART_INVALID_LEAFNODE,
    DATA_EXCEPTION_ART_INVALID_INNERNODE,
    DATA_EXCEPTION_ART_INVALID_NODE,
    DATA_EXCEPTION_ART_EXCEED_MAX_KEYLEN,
    DATA_EXCEPTION_UNDO_DROP_OPENED_INSTANCE,
    DATA_EXCEPTION_UNDO_SPACE_NOT_OPENED,
    DATA_EXCEPTION_UNDO_WRITE_UNDO_RECORD_FAILED,
    DATA_EXCEPTION_UNDO_SHM_ADDR_INVALID,
    DATA_EXCEPTION_UNDO_INIT_PAGE_FAILED,
    DATA_EXCEPTION_FIXED_HEAP_INVALID_PARAMETER,
    DATA_EXCEPTION_FIXED_HEAP_INVALID_ROW,
    DATA_EXCEPTION_FIXED_HEAP_UPDATE_INVALID_LENGTH,
    DATA_EXCEPTION_EDGE_TOPO_INVALID_FIRST_EDGE_ADDR,
    DATA_EXCEPTION_TABLE_PRINT_INVALID_ARUGMENT,
    DATA_EXCEPTION_TABLE_PRINT_INVALID_PAGE_CAPACITY,
    DATA_EXCEPTION_TABLE_PRINT_INVALID_COLUNM_WIDTH,
    DATA_EXCEPTION_TABLE_PRINT_COLUMN_WIDTH_NOT_SET,
    DATA_EXCEPTION_TABLE_PRINT_VALUE_NOT_SET,
    DATA_EXCEPTION_TABLE_PRINT_VALUE_COUNT_ERROR,
    DATA_EXCEPTION_TABLE_PRINT_VALUE_TYPE_ERROR,
    DATA_EXCEPTION_TABLE_PRINT_VALUE_PARSE_ERROR,
    DATA_EXCEPTION_COMMON_GET_CPU_INFO_FAILED,
    DATA_EXCEPTION_COMMON_CHARSET_INVALID_UTF8_STRING,
    DATA_EXCEPTION_CONFIG_FILE_FORMAT_ERROR,
    DATA_EXCEPTION_VALUE_OUT_OF_RANGE,
    DATA_EXCEPTION_CONFIG_INVALID_CONFIG_VALUE,
    DATA_EXCEPTION_COMMON_DIGIT_STRING_OUT_OF_RANGE,
    DATA_EXCEPTION_YANG_DELETE_NO_EXIST_FIELD,
    DATA_EXCEPTION_YANG_CREATE_EXIST_FIELD,
    DATA_EXCEPTION_YANG_MANDATORY_NO_CHOICE,
    DATA_EXCEPTION_YANG_MANDATORY_NO_CASE,
    DATA_EXCEPTION_YANG_MANDATORY_NO_FIELD,
    DATA_EXCEPTION_YANG_INDEX_CONFLICT,
    DATA_EXCEPTION_YANG_INDEX_NOT_EXIST,
    DATA_EXCEPTION_YANG_TARGET_EXIST,
    DATA_EXCEPTION_YANG_DIFFTREE_NOT_EXIST,

    DATA_EXCEPTION_END,

    // 溢出错误
    FIELD_OVERFLOW_INTER = GMERR_FIELD_OVERFLOW << ERROR_CODE_BIT_OFFSET,
    FIELD_OVERFLOW_END,

    // GMERR_INVALID_PARAMETER_VALUE
    INVALID_PARAMETER_VALUE_INTER = GMERR_INVALID_PARAMETER_VALUE << ERROR_CODE_BIT_OFFSET,
    INVALID_PARAMETER_VALUE_YANG_LIST,
    INVALID_PARAMETER_VALUE_END,

    // NULL值指针错误
    NULL_VALUE_NOT_ALLOWED_INTER = GMERR_NULL_VALUE_NOT_ALLOWED << ERROR_CODE_BIT_OFFSET,
    NULL_VALUE_NOT_ALLOWED_END,

    // GMERR_UNIQUE_VIOLATION
    UNIQUE_VIOLATION_INTER = GMERR_UNIQUE_VIOLATION << ERROR_CODE_BIT_OFFSET,

    UNIQUE_VIOLATION_ART_NOT_SUPPORT_WRITE_DUPLICATE,

    UNIQUE_VIOLATION_END,

    // GMERR_RESTRICT_VIOLATION
    RESTRICT_VIOLATION_INTER = GMERR_RESTRICT_VIOLATION << ERROR_CODE_BIT_OFFSET,

    RESTRICT_VIOLATION_UNDO_READ_ONLY_TRX_NOT_SUPPORT,
    RESTRICT_VIOLATION_UNDO_TOO_MANY_CONCURRENT_TRXS,
    RESTRICT_VIOLATION_OPTIMISTIC_TRX,
    // drop namesapce 时 namespace中的labelcount不为0
    RESTRICT_VIOLATION_NAMESPACE_ERROR_PATH,

    RESTRICT_VIOLATION_END,

    // GMERR_TRANS_MODE_MISMATCH
    TRANS_MODE_MISMATCH_INTER = GMERR_TRANS_MODE_MISMATCH << ERROR_CODE_BIT_OFFSET,
    TRANS_TYPE_MISMATCH,
    TRANS_ISOLATION_MISMATCH,

    TRANS_MODE_MISMATCH_END,

    // GMERR_TRANSACTION_ROLLBACK
    TRANSACTION_ROLLBACK_INTER = GMERR_TRANSACTION_ROLLBACK << ERROR_CODE_BIT_OFFSET,
    TRANSACTION_ROLLBACK_END,

    // GMERR_SYNTAX_ERROR
    SYNTAX_ERROR_INTER = GMERR_SYNTAX_ERROR << ERROR_CODE_BIT_OFFSET,

    SYNTAX_ERROR_LPM_IDX_DROP_DUPLICATE,
    SYNTAX_ERROR_NON_UNIQUE_DROP_DUPLICATE,
    SYNTAX_ERROR_SORTED_IDX_DROP_DUPLICATE,
    SYNTAX_ERROR_LPM_IDX_INVALID_KEY,
    SYNTAX_ERROR_LPM_IDX_DEFAULT_VRID_OR_VRFID,
    SYNTAX_ERROR_LPM_IDX_INVALID_VERSION,
    SYNTAX_ERROR_CONFIG_INVALID_CONFIG_NAME,

    SYNTAX_ERROR_END,

    // 非法命名错误
    INVALID_NAME_INTER = GMERR_INVALID_NAME << ERROR_CODE_BIT_OFFSET,
    INVALID_NAME_END,

    GMERR_UNDEFINED_TABLE_INTER = GMERR_UNDEFINED_TABLE << ERROR_CODE_BIT_OFFSET,

    // 重复数据错误
    DUPLICATE_OBJECT_INTER = GMERR_DUPLICATE_OBJECT << ERROR_CODE_BIT_OFFSET,
    DUPLICATE_OBJECT_END,

    // 资源字段错误
    RESOURCE_POOL_ERROR_INTER = GMERR_RESOURCE_POOL_ERROR << ERROR_CODE_BIT_OFFSET,

    RES_COL_ERR_NOT_EXIST,
    RES_COL_ERR_INVALID_COUNT,
    RES_COL_ERR_POOL_INVALID_PARA,
    RES_COL_ERR_POOL_NOT_EXIST,
    RES_COL_ERR_POOL_NOT_BOUND,
    RES_COL_ERR_POOL_CREATE_FAILED,
    RES_COL_ERR_POOL_DROP_FAILED,
    RES_COL_ERR_POOL_UNBIND_FAILED,
    RES_COL_ERR_POOL_OPEN_FAILED,
    RES_COL_ERR_POOL_CLOSE_FAILED,
    RES_COL_ERR_POOL_REFERENCE_HAS_CYCLE,
    RES_COL_ERR_ID_INVALID_PARA,
    RES_COL_ERR_SPEC_RES_NOT_EXIST,
    RES_COL_ERR_SPEC_RES_ALREADY_USED,
    RES_COL_ERR_POOL_TOO_MANY_RES_COUNT,

    // 资源字段错误结束
    RESOURCE_POOL_ERROR_END,

    // 资源池已存在
    RES_POOL_ALREADY_EXIST_INTER = GMERR_RESOURCE_POOL_ALREADY_EXIST << ERROR_CODE_BIT_OFFSET,
    RES_POOL_ALREADY_EXIST_END,

    // 资源池已绑定
    RES_POOL_ALREADY_BOUND_INTER = GMERR_RESOURCE_POOL_ALREADY_BOUND << ERROR_CODE_BIT_OFFSET,
    RES_POOL_ALREADY_BOUND_END,

    RES_COL_ERR_POOL_NOT_ENOUGH_RES = GMERR_RESOURCE_POOL_NOT_ENOUGH << ERROR_CODE_BIT_OFFSET,
    RES_COL_ERR_POOL_NOT_ENOUGH_RES_END,

    // GMERR_INSUFFICIENT_RESOURCES
    INSUFF_RES_INTER = GMERR_INSUFFICIENT_RESOURCES << ERROR_CODE_BIT_OFFSET,

    INSUFF_RES_UNDO_CREATE_RSEG_FAILED,
    INSUFF_RES_UNDO_GET_UNDO_PAGE_FAILED,
    INSUFF_RES_UNDO_CREATE_UNDOSEG_FAILED,
    INSUFF_RES_UNDO_ADD_NEW_PAGE_FAILED,
    INSUFF_RES_UNDO_PAGE_NOT_ENOUGH_SPACE,
    INSUFF_RES_UNDO_GET_RESOURCE_HANDLE_FAIL,
    INSUFF_RES_FIXED_HEAP_OUT_OF_FILE_BLOCK,
    INSUFF_RES_HASH_ALLOC_SHM_FAILED,
    INSUFF_RES_HASH_ITERATOR_ALLOC_FAILED,
    INSUFF_RES_HASH_NODE_ALLOC_FAILED,
    INSUFF_RES_ART_ALLOC_MEM_FAILED,
    INSUFF_RES_ART_MEM_CTX_ALLOC_MEM_FAILED,
    INSUFF_RES_ART_MEM_SHM_ADDR_INVALID,
    INSUFF_RES_NON_UNIQUE_ALLOC_SHM_FAILED,
    INSUFF_RES_NON_UNIQUE_OPEN_FAILED,
    INSUFF_RES_LPM_IDX_ALLOC_SHM_FAILED,
    INSUFF_RES_LPM_IDX_OPEN_FAILED,
    INSUFF_RES_SORTED_IDX_ALLOC_SHM_FAILED,
    INSUFF_RES_SORTED_IDX_ITERATOR_ALLOC_FAILED,
    INSUFF_RES_SORTED_IDX_OPEN_FAILED,
    INSUFF_RES_CREATE_FIXED_HEAP_MEM_CTX_FAILED,
    INSUFF_RES_PAGE_ID,
    INS_RES_TRX,  // 没有可用的trx
    INSUFF_RES_SCAN_ALLOC_CURSOR,
    INSUFF_RES_DATAFILE_TOO_MANY,
    INSUFF_RES_DATAFILE,
    INSUFF_RES_DATAFILE_PER_SPACE,
    INSUFF_RES_SPACE_TOO_MANY,
    INSUFF_RES_SPACE_SIZE,
    INSUFF_RES_END,

    // GMERR_OUT_OF_MEMORY
    OUT_OF_MEMORY_INTER = GMERR_OUT_OF_MEMORY << ERROR_CODE_BIT_OFFSET,

    OUT_OF_MEMORY_MEM_FAILED,
    OUT_OF_MEMORY_ALLOC_LSN_NODE_FAILED,

    OUT_OF_MEMORY_END,

    // GMERR_CONFIGURATION_LIMIT_EXCEEDED
    CONFIGURATION_LIMIT_EXCEEDED_INTER = GMERR_RECORD_COUNT_LIMIT_EXCEEDED << ERROR_CODE_BIT_OFFSET,
    CONFIGURATION_LIMIT_EXCEEDED_HEAP_ITEM,

    CONFIGURATION_LIMIT_EXCEEDED_END,

    // GMERR_PROGRAM_LIMIT_EXCEEDED
    PROGRAM_LIMIT_EXCEEDED_INTER = GMERR_PROGRAM_LIMIT_EXCEEDED << ERROR_CODE_BIT_OFFSET,

    PROGRAM_LIMIT_EXCEEDED_HASH_REACH_MAX_BUCKET_PAGE_NUM,
    PROGRAM_LIMIT_EXCEEDED_FIXED_HEAP_EXCEED_CURSOR_LIMIT,
    PROGRAM_LIMIT_EXCEEDED_UNDO_TOO_LARGE_RECORD,
    PROGRAM_LIMIT_EXCEEDED_HEAP_MAX_ROW_SIZE,
    PROGRAM_LIMIT_EXCEEDED_HEAP_TUPLE_ADDR,
    PROGRAM_LIMIT_EXCEEDED_END,

    // OBJECT_NOT_IN_PREREQUISITE
    OBJECT_NOT_IN_PREREQUISITE_STATE_INTER = GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE << ERROR_CODE_BIT_OFFSET,

    OBJ_NOT_PREREQUISITE_LPM_IDX_NOT_OPEN,
    OBJ_NOT_PREREQUISITE_LPM_IDX_HAS_OPEN,
    OBJ_NOT_PREREQUISITE_LPM_IDX_HAS_NOT_BEEN_INSERTED,
    OBJ_NOT_PREREQUISITE_HASH_HAS_OPEN,
    OBJ_NOT_PREREQUISITE_HASH_DROP_DUPLICATE,
    OBJ_NOT_PREREQUISITE_SECOND_HASH_TYPE_NOT_MATCH,
    OBJ_NOT_PREREQUISITE_NON_UNIQUE_NOT_OPEN,
    OBJ_NOT_PREREQUISITE_NON_UNIQUE_HAS_OPEN,
    OBJ_NOT_PREREQUISITE_SORTED_IDX_NOT_OPEN,
    OBJ_NOT_PREREQUISITE_SORTED_IDX_HAS_OPEN,
    OBJ_NOT_PREREQUISITE_FIXED_HEAP_DROP_OPENED_INSTANCE,
    OBJ_NOT_PREREQUISITE_FIXED_HEAP_NOT_OPEN,
    OBJ_NOT_PREREQUISITE_FIXED_HEAP_INVALID_CURSOR,
    OBJ_NOT_PREREQUISITE_UNDO_INVALID_PTR,
    OBJ_NOT_PREREQUISITE_UNDO_PURGER_INIT_FAIL,
    OBJ_NOT_PREREQUISITE_SE_INVALID_POINTER,
    OBJ_NOT_PREREQUISITE_FILE_OPEN,
    OBJ_NOT_PREREQUISITE_FILE_IO_ERR,
    OBJ_NOT_PREREQUISITE_FILE_REMOVE,
    OBJ_NOT_PREREQUISITE_FILE_STAT,

    OBJECT_NOT_IN_PREREQUISITE_STATE_END,

    // 锁获取失败
    LOCK_NOT_AVAILABLE_INTER = GMERR_LOCK_NOT_AVAILABLE << ERROR_CODE_BIT_OFFSET,

    // trx lock
    LK_NO_AVA_LOCK_POOL_NO_RESOURCE,
    LK_NO_AVA_DEAD_LOCK,
    LK_NO_AVA_DEAD_LOCK_SELF,       // Detects a deadlock and the start point is involved
    LK_NO_AVA_DEAD_LOCK_OTHER,      // Detects a deadlock but the start point is not involved
    LK_NO_AVA_DEAD_LOCK_MAX_DEPTH,  // Searching reaches the max depth
    LK_NO_AVA_WAIT_TOO_LONG,
    LK_NO_AVA_ACQ_POOL_NO_RESOURCE,
    LK_NO_AVA_LOCK_CONFLICT,
    LK_NO_AVA_LOCK_UPDATE_CONFLICT,
    LK_NO_AVA_LOCK_ALLOC_SCAN_CURSOR,

    // 锁获取失败
    LOCK_NOT_AVAILABLE_END,

    // unable to operate file
    FILE_OPERATE_FAILED_INTER = GMERR_FILE_OPERATE_FAILED << ERROR_CODE_BIT_OFFSET,
    FILE_OPERATE_FAILED_END,

    // CRC check fail
    INT_ERR_CRC_CHECK_ERROR = GMERR_CRC_CHECK_FAILED << ERROR_CODE_BIT_OFFSET,

    // GMERR_PROCESS_ABORT
    INT_PROCESS_ABORT_ERROR = GMERR_PROCESS_ABORT << ERROR_CODE_BIT_OFFSET,

    // GMERR_CONFIG_ERROR
    CONFIG_ERROR_INTER = GMERR_CONFIG_ERROR << ERROR_CODE_BIT_OFFSET,
    CONFIG_ERROR_END,

    // 内部错误
    INTERNAL_ERROR_INTER = GMERR_INTERNAL_ERROR << ERROR_CODE_BIT_OFFSET,

    // common
    INT_ERR_TIMER_START_RTOS_TIMER_FAIL,
    INT_ERR_TIMER_CREATE_RTOS_TIMER_FAIL,
    INT_ERR_TIMER_SET_RTOS_WAITING_TIME_FAIL,
    INT_ERR_SPINLOCK_SPIN_TOO_LONG,
    INT_ERR_DIR_CREAT,
    INT_ERR_COMMON_SHARED_SEM_INIT_FAIL,
    INT_ERR_COMMON_RWMUTEX_ENQUEUE_FAIL,
    INT_ERR_COMMON_RWMUTEX_ACQUIRE_FAIL,
    INT_ERR_SECUREC_MEMORY_COPY_FAIL,
    INT_ERR_STOP_TIMER_FAIL,
    INT_ERR_DELETE_TIMER_FAIL,

    // SE
    INT_ERR_SE_HASH_FETCH_NEXT_FAILED,
    INT_ERR_SE_HASH_INVALID_SE_INSTANCE,
    INT_ERR_SE_HASH_SHM_ADDR_INVALID,
    INT_ERR_SE_HASH_NODE_TO_ADDR_FAILED,
    INT_ERR_SE_ART_EXCEED_NODE_LENGTH,
    INT_ERR_SE_FIXED_HEAP_INIT_FSM_FAILED,
    INT_ERR_SE_FIXED_HEAP_GET_MEMPAGE_FAILED,
    INT_ERR_SE_NON_UNIQUE_INVALID_SE_INSTANCE,
    INT_ERR_ART_SECUREC_MEMORY_COPY_FAILED,
    INT_ERR_ART_STACK_ERROR,
    INT_ERR_ART_MEM_GET_SHMARRAY_ITEM_ERROR,
    INT_ERR_ART_CLEAR_DELFLAG_INVALID_STATE,

    // device
    INT_ERR_INVALID_DEV_ID,

    // memdata
    INT_ERR_SE_INVALID_PAGE_ID,
    INT_ERR_SE_DOUBLE_FREE_PAGE,
    INT_ERR_SE_INVALID_PAGE_CHECKSUM,

    // LFS
    INT_ERR_LFS_INVALID_BLOCK_ID,
    INT_ERR_LFS_USED_BLOCK,
    INT_ERR_LFS_NO_FREE_BLOCK,
    INT_ERR_LFS_BLOCK_HAS_RELEASED,
    INT_ERR_LFS_INVALID_REQ_SIZE,
    INT_ERR_LFS_NO_FRAG_BLOCK,          // 没有可整理的碎片block
    INT_ERR_LFS_PAGEFREESIZE_NOTMATCH,  // 因表升降级导致多级链表的值改变
    INT_ERR_LFS_TOO_MANY_FSM_PAGE,
    INT_ERR_LFS_INVALID_FSM_PAGE_ID,

    // Heap
    INT_ERR_HEAP_OPEN_FAILED,
    INT_ERR_HEAP_NOT_OPEN,
    INT_ERR_HEAP_UNEXPECT_ERROR,
    INT_ERR_NAMED_LATCH_INIT_FAILED,
    INT_ERR_HEAP_SCAN_CURSOR_NOT_CLOSE,
    INT_ERR_HEAP_BLOCK_NOT_EXIST,
    INT_ERR_HEAP_INVALID_PARAMETER,
    INT_ERR_HEAP_UPD_ALLOC_SAME_PAG,
    INT_ERR_HEAP_READ_LOB_FAILED_TOO_MUCH,
    INT_ERR_HEAP_NOT_SUPPORT,
    INT_ERR_FIXED_HEAP_FREESIZE_NOTMATCH,  // 因表升降级导致定长表不能满足需求

    // trx
    INT_ERR_TRX_NEED_OPEN_READVIEW,
    INT_ERR_PAGE_SORTER_OUT_OF_ARRAY,
    INT_ERR_PAGE_SORTER_PAGE_SAME_LATCH_EXISTED,
    INT_ERR_PAGE_SORTER_PAGE_LATCH_CONFLICT,

    // redo
    INT_ERR_REDO_ALLOC_LSN_NODE_FAILED,
    INT_ERR_REDO_MTR_OPEN_BUF_FAILED,
    INT_ERR_REDO_BUFFER_NOT_ENOUGH,
    INT_ERR_REDO_TOO_MANY_DIRTY_PAGES,
    INT_ERR_REDO_PART_INVALID,
    INT_ERR_REDO_POINT_INVALID,
    INT_ERR_REDO_BATCH_INVALID,
    INT_ERR_REDO_SCAN_END,
    INT_ERR_REDO_INVALID_TYPE,
    INT_ERR_REDO_REPLAY_FUNC_NULL,
    INT_ERR_REDO_REPLAY_REGISTERED,
    INT_ERR_REDO_REPLAY_ERROR,

    // space
    INT_ERR_SPACE_ALREADY_EXIST,
    INT_ERR_SEGMENT_TOO_MANY,
    INT_ERR_DATAFILE_ALREADY_EXIST,
    INT_ERR_DATAFILE_ALREADY_CLOSE,
    INT_WARN_DATAFILE_EXTEND,
    INT_ERR_SPACE_OFFLINE,
    INT_ERR_BUF_POOL_OUT_OF_MEM,
    SPACE_SET_SIZE_ERROR,
    SPACE_TYPE_ERROR,
    INT_ERR_FLUSH_LOCK_NOT_AVAILABLE,
    INT_ERR_BACK_UP_IN_PROGRESS,
    INT_ERR_BACK_UP_LOCK_NOT_AVAILABLE,
    INT_ERR_SPACE_CFG,
    INT_ERR_SPACE_INVALID_SPACE_ID,
    SPACE_ERROR_END,

    // check point
    INT_ERR_WAIT_CKPT_TIMEOUT,

    // undo
    INT_ERR_UNDO_REC_NOT_FOUND,

    // res session
    INT_ERR_RES_SESSION_INVALID_RECORD_ID,
    INT_ERR_RES_SESSION_ALLOC_FAILED,

    // oplog
    INT_OPLOG_OPERATE_FAILED,
    INT_OPLOG_FILE_NOT_EXIST_ERROR,
    INT_OPLOG_FILE_CREATE_EXIST_ERROR,
    INT_OPLOG_FILE_INVALID,
    INT_OPLOG_EVENT_OVER_SIZE_ERROR,
    INT_OPLOG_POINT_INVALID,
    INT_OPLOG_BUF_PARSE_EVENT_END,
    INT_OPLOG_APPLY_FUNC_NULL,
    INT_OPLOG_APPLY_ERROR,
    INT_OPLOG_INVALID_TYPE,
    INT_OPLOG_APPLY_FUNC_REGISTERED,

    // 内部错误结束
    INTERNAL_ERROR_END,

    // GMERR_UNEXPECTED_NULL_VALUE
    UNEXPECTED_NULL_VALUE_INTER = GMERR_UNEXPECTED_NULL_VALUE << ERROR_CODE_BIT_OFFSET,
    UNEXPECTED_NON_ZERO_VALUE_INTER,

    UNEXPECTED_NULL_VALUE_END,

    // GMERR_INVALID_BUFFER
    INVALID_BUFFER_INTER = GMERR_INVALID_BUFFER << ERROR_CODE_BIT_OFFSET,
    INVALID_BUFFER_END,

    // GMERR_MEMORY_OPERATE_FAILED
    MEMORY_OPERATE_FAILED_INTER = GMERR_MEMORY_OPERATE_FAILED << ERROR_CODE_BIT_OFFSET,

    MEMORY_OPERATE_FAILED_END,

    MEM_CTX_MEMORY_EXCEEDED_THRESHOLD_INTER = GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD << ERROR_CODE_BIT_OFFSET,
    MEM_CTX_MEMORY_EXCEEDED_THRESHOLD_END,

    // Retry error
    CONNECTION_TIMED_OUT_INTER = GMERR_CONNECTION_TIMED_OUT << ERROR_CODE_BIT_OFFSET,
    CONNECTION_TIMED_OUT_END,

    CONNECTION_RESET_BY_PEER_INTER = GMERR_CONNECTION_RESET_BY_PEER << ERROR_CODE_BIT_OFFSET,
    CONNECTION_RESET_BY_PEER_END,

    CONNECTION_SEND_BUFFER_FULL_INTER = GMERR_CONNECTION_SEND_BUFFER_FULL << ERROR_CODE_BIT_OFFSET,
    CONNECTION_SEND_BUFFER_FULL_END,

    BATCH_CMD_SEND_BUFF_OVERFLOW_INTER = GMERR_BATCH_CMD_SEND_BUFF_OVERFLOW << ERROR_CODE_BIT_OFFSET,
    BATCH_CMD_SEND_BUFF_OVERFLOW_END,

    REQUEST_TIME_OUT_INTER = GMERR_REQUEST_TIME_OUT << ERROR_CODE_BIT_OFFSET,
    REQUEST_TIME_OUT_END,

    // Load third party library failed
    LOAD_THIRD_PARTY_LIBRARY_FAILED_INTER = GMERR_LOAD_THIRD_PARTY_LIBRARY_FAILED << ERROR_CODE_BIT_OFFSET,
    LOAD_THIRD_PARTY_LIBRARY_FAILED_END,

    GET_THIRD_PARTY_FUNCTION_FAILED_INTER = GMERR_GET_THIRD_PARTY_FUNCTION_FAILED << ERROR_CODE_BIT_OFFSET,
    GET_THIRD_PARTY_FUNCTION_FAILED_END,

    THIRD_PARTY_FUNCTION_EXECUTE_FAILED_INTER = GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED << ERROR_CODE_BIT_OFFSET,
    THIRD_PARTY_FUNCTION_EXECUTE_FAILED_END,

    // priv error
    INVALID_GRANTOR_INTER = GMERR_INVALID_GRANTOR << ERROR_CODE_BIT_OFFSET,
    INVALID_GRANTOR_END,

    INVALID_GRANT_OPERATION_INTER = GMERR_INVALID_GRANT_OPERATION << ERROR_CODE_BIT_OFFSET,
    INVALID_GRANT_OPERATION_END,

    PRIVILEGE_NOT_GRANTED_INTER = GMERR_PRIVILEGE_NOT_GRANTED << ERROR_CODE_BIT_OFFSET,
    PRIVILEGE_NOT_GRANTED_END,

    PRIVILEGE_NOT_REVOKED_INTER = GMERR_PRIVILEGE_NOT_REVOKED << ERROR_CODE_BIT_OFFSET,
    PRIVILEGE_NOT_REVOKED_END,

    INSUFFICIENT_PRIVILEGE_INTER = GMERR_INSUFFICIENT_PRIVILEGE << ERROR_CODE_BIT_OFFSET,
    INSUFFICIENT_PRIVILEGE_END,

    // 数通场景，持久化外部错误码会暴露
    DISK_NO_SPACE_ERROR_INTER = GMERR_DISK_NO_SPACE_ERROR << ERROR_CODE_BIT_OFFSET,
    DISK_NO_SPACE_ERROR_END,

    DIR_OPERATE_FAILED_INTER = GMERR_DIR_OPERATE_FAILED << ERROR_CODE_BIT_OFFSET,
    OPERATE_FAILED_END,

    FLUSH_STATE_ERROR_INTER = GMERR_FLUSH_STATE_ERROR << ERROR_CODE_BIT_OFFSET,
    FLUSH_STATE_ERROR_END,

    DATA_CORRUPTION_INTER = GMERR_DATA_CORRUPTION << ERROR_CODE_BIT_OFFSET,
    DATA_CORRUPTION_END,

    DATABASE_NOT_AVAILABLE_INTER = GMERR_DATABASE_NOT_AVAILABLE << ERROR_CODE_BIT_OFFSET,
    DATABASE_NOT_AVAILABLE_END,

    PERMISSION_DENIED_INTER = GMERR_PERMISSION_DENIED << ERROR_CODE_BIT_OFFSET,
    PERMISSION_DENIED_END,

    SCAN_AT_END_INTER = GMERR_SCAN_AT_END << ERROR_CODE_BIT_OFFSET,
    SCAN_AT_END_END,

    FILE_NO_SPACE_ERROR_INTER = GMERR_FILE_NO_SPACE_ERROR << ERROR_CODE_BIT_OFFSET,
    FILE_NO_SPACE_ERROR_END,

    FATAL_ERROR_INTER = GMERR_FATAL << ERROR_CODE_BIT_OFFSET,
    FATAL_ERROR_END,

    // sync
    EQUIP_ID_NOT_REGISTERED_INTER = GMERR_EQUIP_ID_NOT_REGISTERED << ERROR_CODE_BIT_OFFSET,
    INT_SYNC_ICLOUD_NOT_REGISTRY = GMERR_SYNC_PREREQUISITES_ABNORMAL << ERROR_CODE_BIT_OFFSET,
    INT_SYNC_INVALID_ICLOUD,
    INT_SYNC_THREAD_POOL_NOT_REGISTRY,
    INT_SYNC_INVALID_THREAD_POOL,
    INT_SYNC_INVALID_PARAM = GMERR_SYNC_INVALID_ARGS << ERROR_CODE_BIT_OFFSET,
    INT_SYNC_INNER_ERROR,
    INT_SYNC_TASK_OVER_LIMIT = GMERR_SYNC_EXCEED_TASK_QUEUE_LIMIT << ERROR_CODE_BIT_OFFSET,
    INT_SYNC_QUERY_END,
    INT_SYNC_END,

    // Bottom
    STATUS_MAX_VALUE_INTER
} StatusInter;

// 用于将内部错误码转化为外部错误码
#ifdef FEATURE_SIMPLEREL
#define DB_V1ERR_MAX_VALUE (0x202a0255)  // VOS_ERRNO_DB_STROPER_FAILURE
inline static Status DbGetExternalErrno(StatusInter errCode)
{
    Status ret = (Status)errCode;
    return (Status)((ret <= DB_V1ERR_MAX_VALUE) ? ret : (Status)((uint32_t)ret >> ERROR_CODE_BIT_OFFSET));
}
#else
inline static Status DbGetExternalErrno(StatusInter errCode)
{
    Status ret = (Status)errCode;
    return (Status)((ret < GMERR_MAX_VALUE) ? ret : (Status)((uint32_t)ret >> ERROR_CODE_BIT_OFFSET));
}
#endif

// 用于计算相对于首个同类内部错误码的偏移
inline static uint32_t DbGetInternalErrno(StatusInter errCode)
{
    Status ret = (Status)errCode;
    return (uint32_t)((ret < GMERR_MAX_VALUE) ? 0 : ((uint32_t)ret & (MAX_INTERNAL_ERR_CODE - 1)));
}

// 用于将外部错误码转换为内部错误码
inline static StatusInter DbGetStatusInterErrno(Status errCode)
{
    return (StatusInter)((uint32_t)errCode << ERROR_CODE_BIT_OFFSET);
}

inline static bool DbIsInsufficientMemoryErr(StatusInter errCode)
{
    Status ret = DbGetExternalErrno(errCode);
    return ret == GMERR_MEMORY_OPERATE_FAILED || ret == GMERR_OUT_OF_MEMORY || ret == GMERR_INSUFFICIENT_RESOURCES;
}

inline static bool DbIsNoDataErr(StatusInter errCode)
{
    Status ret = DbGetExternalErrno(errCode);
    return ret == GMERR_NO_DATA;
}

inline static uint32_t DbGetErrnoByOffset(Status errCode, uint32_t offset)
{
    return ((uint32_t)errCode << ERROR_CODE_BIT_OFFSET) + offset;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* DB_INTERNAL_ERROR_H */
