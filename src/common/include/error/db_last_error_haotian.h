/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: last error for haotian
 * Author: jinfanglin
 * Create: 2025-03-10
 */

#ifndef DB_LAST_ERROR_HAOTIAN_H
#define DB_LAST_ERROR_HAOTIAN_H

#include "db_last_error_base.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#ifdef IDS_HAOTIAN
#define LOG_AND_SET_LAST_ERROR_PRUNE(type, lvl, eCode, ...) \
    do {                                                    \
        LOG_WRITE(true, type, lvl, eCode, ##__VA_ARGS__);   \
        DB_SET_LAST_ERROR(eCode, ##__VA_ARGS__);            \
    } while (0)

#define LOG_AND_SET_LAST_ERROR_CUSTOM_PRUNE(type, lvl, eCode, format, ...) \
    DbLogWriteAndSetLastError(true, type, LOG_MODULE, lvl, eCode, __FILE__, __LINE__, __func__, format, ##__VA_ARGS__)

// 3种不同级别的日志写入同时写入LastError，参数缩写eCode（errCode）
#define LOG_WARN_AND_SET_LAST_ERROR(type, eCode, ...) \
    LOG_AND_SET_LAST_ERROR_PRUNE(type, DB_LOG_LVL_WARN, eCode, ##__VA_ARGS__)
#define LOG_ERROR_AND_SET_LAST_ERROR(type, eCode, ...) \
    LOG_AND_SET_LAST_ERROR_PRUNE(type, DB_LOG_LVL_ERR, eCode, ##__VA_ARGS__)
#define LOG_EMRG_AND_SET_LAST_ERROR(type, eCode, ...) \
    LOG_AND_SET_LAST_ERROR_PRUNE(type, DB_LOG_LVL_EMRG, eCode, ##__VA_ARGS__)
#define LOG_EMRG_AND_SET_LAST_ERROR_CUSTOM(type, eCode, fmt, ...) \
    LOG_AND_SET_LAST_ERROR_CUSTOM_PRUNE(type, DB_LOG_LVL_EMRG, eCode, fmt, ##__VA_ARGS__)
#define LOG_ERROR_AND_SET_LAST_ERROR_CUSTOM(type, eCode, fmt, ...) \
    LOG_AND_SET_LAST_ERROR_CUSTOM_PRUNE(type, DB_LOG_LVL_ERR, eCode, fmt, ##__VA_ARGS__)

// 写错误日志的同时写入Last error.
#define OP_EMRG_AND_SET_LAST_ERROR(errCode, ...) LOG_EMRG_AND_SET_LAST_ERROR(DB_LOG_TYPE_OP, errCode, ##__VA_ARGS__)
#define OP_ERROR_AND_SET_LAST_ERROR(errCode, ...) LOG_ERROR_AND_SET_LAST_ERROR(DB_LOG_TYPE_OP, errCode, ##__VA_ARGS__)
#define OP_WARN_AND_SET_LAST_ERROR(errCode, ...) LOG_WARN_AND_SET_LAST_ERROR(DB_LOG_TYPE_OP, errCode, ##__VA_ARGS__)

#define DEBUG_EMRG_AND_SET_LAST_ERROR(errCode, ...) \
    LOG_EMRG_AND_SET_LAST_ERROR(DB_LOG_TYPE_DEBUG, errCode, ##__VA_ARGS__)
#define DEBUG_ERROR_AND_SET_LAST_ERROR(errCode, ...) \
    LOG_ERROR_AND_SET_LAST_ERROR(DB_LOG_TYPE_DEBUG, errCode, ##__VA_ARGS__)
#define DEBUG_WARN_AND_SET_LAST_ERROR(errCode, ...) \
    LOG_WARN_AND_SET_LAST_ERROR(DB_LOG_TYPE_DEBUG, errCode, ##__VA_ARGS__)
#define DEBUG_EMRG_CUSTOM_AND_SET_LAST_ERROR(errCode, format, ...) \
    LOG_EMRG_AND_SET_LAST_ERROR_CUSTOM(DB_LOG_TYPE_DEBUG, errCode, format, ##__VA_ARGS__)

#define OP_ERROR_CUSTOM_AND_SET_LAST_ERROR(errCode, ...) \
    LOG_ERROR_AND_SET_LAST_ERROR_CUSTOM(DB_LOG_TYPE_OP, errCode, format, ##__VA_ARGS__)
#define OP_WARN_CUSTOM_AND_SET_LAST_ERROR(errCode, ...) \
    LOG_WARN_AND_SET_LAST_ERROR_CUSTOM(DB_LOG_TYPE_OP, errCode, format, ##__VA_ARGS__)
#define OP_EMRG_CUSTOM_AND_SET_LAST_ERROR(errCode, format, ...) \
    LOG_EMRG_AND_SET_LAST_ERROR_CUSTOM(DB_LOG_TYPE_OP, errCode, format, ##__VA_ARGS__)

#define RUN_WARN_AND_SET_LAST_ERROR(errCode, ...) LOG_WARN_AND_SET_LAST_ERROR(DB_LOG_TYPE_RUN, errCode, ##__VA_ARGS__)
#define RUN_WARN_CUSTOM_AND_SET_LAST_ERROR(errCode, format, ...) \
    LOG_WARN_AND_SET_LAST_ERROR_CUSTOM(DB_LOG_TYPE_RUN, errCode, format, ##__VA_ARGS__)
#define RUN_ERROR_CUSTOM_AND_SET_LAST_ERROR(errCode, format, ...) \
    LOG_ERROR_AND_SET_LAST_ERROR_CUSTOM(DB_LOG_TYPE_RUN, errCode, format, ##__VA_ARGS__)
#define RUN_ERROR_AND_SET_LAST_ERROR(errCode, ...) LOG_ERROR_AND_SET_LAST_ERROR(DB_LOG_TYPE_RUN, errCode, ##__VA_ARGS__)
#define RUN_EMRG_CUSTOM_AND_SET_LAST_ERROR(errCode, format, ...) \
    LOG_EMRG_AND_SET_LAST_ERROR_CUSTOM(DB_LOG_TYPE_RUN, errCode, format, ##__VA_ARGS__)
#define RUN_EMRG_AND_SET_LAST_ERROR(errCode, ...) LOG_EMRG_AND_SET_LAST_ERROR(DB_LOG_TYPE_RUN, errCode, ##__VA_ARGS__)
#define DB_SET_LAST_ERROR_CUSTOM(format, ...) DbWriteLastErrorCustom(LOG_NO_ERRORCODE, format, ##__VA_ARGS__)
// 写错误日志的同时写入Last error.
#define DEBUG_ERROR_CUSTOM_AND_SET_LAST_ERROR(errCode, format, ...) \
    LOG_AND_SET_LAST_ERROR_CUSTOM_PRUNE(DB_LOG_TYPE_DEBUG, DB_LOG_LVL_ERR, errCode, format, ##__VA_ARGS__)
#define DEBUG_WARN_CUSTOM_AND_SET_LAST_ERROR(errCode, format, ...) \
    LOG_AND_SET_LAST_ERROR_CUSTOM_PRUNE(DB_LOG_TYPE_DEBUG, DB_LOG_LVL_WARN, errCode, format, ##__VA_ARGS__)

#endif
#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* DB_LAST_ERROR_HAOTIAN_H */
