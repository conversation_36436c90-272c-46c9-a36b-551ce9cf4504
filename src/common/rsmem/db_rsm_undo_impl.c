/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: source file for reserved memory undo
 * Author: baiyang
 * Create: 2024-03-20
 */

#include "db_log.h"
#include "db_rsm_undo.h"

#ifdef __cplusplus
extern "C" {
#endif

void RsmUndoRecordInitImpl(void *rsmUndoRecord, uint32_t totalUndoRecNum)
{
    DB_POINTER(rsmUndoRecord);
    RsmUndoRecordObjT *rsmUndoRecordObj = (RsmUndoRecordObjT *)rsmUndoRecord;
    rsmUndoRecordObj->totalUndoRecNum = totalUndoRecNum;
    rsmUndoRecordObj->usedUndoRecNum = 0;
    RsmUndoLogTraceInitDebug(rsmUndoRecordObj);
}

RsmUndoOpRecordT *RsmUndoGetNextFreeRecordImpl(void *rsmUndoRecord, uint32_t arrPos)
{
    DB_POINTER(rsmUndoRecord);
    RsmUndoRecordObjT *rsmUndoRecordObj = (RsmUndoRecordObjT *)rsmUndoRecord;
    // rsmUndo是修改完成后即可清理复用，一般来说不会积压超过预期深度
    DB_ASSERT(arrPos < rsmUndoRecordObj->totalUndoRecNum);
    return &rsmUndoRecordObj->recordArr[arrPos];
}

static DbRsmUndoOpRecordHandleFuncT g_gmdbRsmUndoOpRecordHandleFuncArr[RSM_UNDO_END] = {0};

void DbRsmUndoSetOpRecordHandleImpl(const RsmUndoOpRecordHandleConfT conf[], uint32_t confNum)
{
    for (uint32_t i = 0; i < confNum; i++) {
        DB_ASSERT(conf[i].type < RSM_UNDO_END);
        DB_ASSERT(g_gmdbRsmUndoOpRecordHandleFuncArr[conf[i].type] == NULL);
        g_gmdbRsmUndoOpRecordHandleFuncArr[conf[i].type] = conf[i].func;
    }
}

static void RsmUndoHandleOpRecord(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    DB_ASSERT(opRecord->recordType < RSM_UNDO_END);
    DbRsmUndoOpRecordHandleFuncT func = g_gmdbRsmUndoOpRecordHandleFuncArr[opRecord->recordType];
    func(ctx, opRecord);
}

void DbRsmUndoRollbackImpl(RsmUndoCtxT *ctx, void *rsmUndoRecord, RsmUndoRollbackTypeE rollbackType)
{
    DB_POINTER2(ctx, rsmUndoRecord);
    RsmUndoRecordObjT *rsmUndoRecordObj = (RsmUndoRecordObjT *)rsmUndoRecord;
    if (rsmUndoRecordObj->usedUndoRecNum == 0) {
        return;
    }
    DB_LOG_INFO("RsmUndo-Rollback opRecord Info: usedUndoRecNum:%" PRIu32 ", rollbackType:%" PRIu32,
        rsmUndoRecordObj->usedUndoRecNum, (uint32_t)rollbackType);
    uint32_t partOfRecord = 0;
    bool isRollback = false;
    for (int32_t i = (int32_t)rsmUndoRecordObj->usedUndoRecNum - 1; i >= 0; --i) {
        RsmUndoLogTraceRecoverDebug(rsmUndoRecordObj, i);
        const RsmUndoOpRecordT *opRecord = &rsmUndoRecordObj->recordArr[i];
        if (opRecord->recordType >= RSM_UNDO_REBUILD_BEGIN && opRecord->recordType < RSM_UNDO_REBUILD_END) {
            // 处于if判断首位，处理表恢复前部分逻辑（initPage、resetPage、alterConfig）
            if (rollbackType == RSM_UNDO_ROLLBACK_REBUILD) {
                RsmUndoHandleOpRecord(ctx, opRecord);
                partOfRecord++;
            }
        } else if (opRecord->recordType >= RSM_UNDO_HEAP_BEGIN && opRecord->recordType < RSM_UNDO_CLUSTERED_END) {
            if (rollbackType == RSM_UNDO_ROLLBACK_DML || rollbackType == RSM_UNDO_ROLLBACK_ALL) {
                DB_LOG_INFO("RsmUndo-Rollback opRecord Info: recordType:%" PRIu32 ", labelId:%" PRIu32
                            ", rsmUndoRecNum:%" PRIu32,
                    opRecord->recordType, opRecord->labelId, rsmUndoRecordObj->usedUndoRecNum);
                RsmUndoHandleOpRecord(ctx, opRecord);
                partOfRecord++;
                isRollback = true;
            }
        } else if (opRecord->recordType >= RSM_UNDO_TABLE_INFO_BEGIN &&
                   opRecord->recordType < RSM_UNDO_TABLE_INFO_END) {
            if (rollbackType == RSM_UNDO_ROLLBACK_DDL || rollbackType == RSM_UNDO_ROLLBACK_ALL) {
                RsmUndoHandleOpRecord(ctx, opRecord);
                partOfRecord++;
                isRollback = true;
            }
        } else {
            if (rollbackType == RSM_UNDO_ROLLBACK_ALL) {
                RsmUndoHandleOpRecord(ctx, opRecord);
                partOfRecord++;
            }
        }
    }
    if (isRollback || rollbackType == RSM_UNDO_ROLLBACK_ALL) {
        DB_ASSERT(partOfRecord == rsmUndoRecordObj->usedUndoRecNum);
        rsmUndoRecordObj->usedUndoRecNum = 0;
    } else if (rollbackType == RSM_UNDO_ROLLBACK_REBUILD) {
        rsmUndoRecordObj->usedUndoRecNum -= partOfRecord;
    }
}

const DbRsmUndoAmT g_gmdbRsmUndoAm = {
    .rsmUndoRecordInitFunc = RsmUndoRecordInitImpl,
    .rsmUndoGetNextFreeRecFunc = RsmUndoGetNextFreeRecordImpl,
    .rsmUndoSetOpRecordHandleFunc = DbRsmUndoSetOpRecordHandleImpl,
    .rsmUndoRollbackFunc = DbRsmUndoRollbackImpl,
};

#ifdef __cplusplus
}
#endif
