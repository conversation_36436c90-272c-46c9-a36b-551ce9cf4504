/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: reserve memory implementation.
 * Author: <PERSON><PERSON><PERSON><PERSON>e
 * Create: 2024-02-17
 */

#include <sys/mman.h>
#include "db_rsmem_impl.h"
#include "db_rsmem_algo.h"
#include "db_rsmem_init.h"
#include "db_rsm_check.h"
#include "db_file.h"

#ifdef __cplusplus
extern "C" {
#endif

struct DbRsmMgrOptionT {
    uint32_t keyMin;
    uint32_t maxNumBlocks;  // 配置的保留内存block范围
    uint32_t maxBlockSize;  // bytes
    uint32_t pageSize;
    DbRsmCallbacksT callbacks;
    DbRsmIpcPermT perm;
    bool isUseExtendBlock;    // 是否使用高端内存
    uint32_t extendBlockNum;  // 高端内存block数量
    uint32_t totalBlockNum;   // 总block数，包括保留内存和高端内存
    uint32_t extendBlockSize;  // bytes 高端内存总大小，0: 不使用高端内存；非0且不小于deviceSize：使用高端内存
    uint32_t extendBlockStartIdx;  // 高端内存起始blockId
    // 管理结构后紧接着blockSize数组，unit:M
};

DbRsmMgrOptionT *DbRsmMgrOptionCreate(uint32_t totalBlockNum)
{
    size_t allocSize = sizeof(DbRsmMgrOptionT) + sizeof(uint32_t) * totalBlockNum;
    DbRsmMgrOptionT *p = (DbRsmMgrOptionT *)malloc(allocSize);
    if (p == NULL) {
        return NULL;
    }
    (void)memset_s(p, allocSize, 0, allocSize);
    return p;
}

void DbRsmMgrOptionDestroy(DbRsmMgrOptionT *option)
{
    DB_POINTER(option);
    free(option);
}

void DbRsmMgrOptionSetKeyMin(DbRsmMgrOptionT *option, uint32_t key)
{
    DB_POINTER(option);
    option->keyMin = key;
}

void DbRsmMgrOptionSetMaxNumBlocks(DbRsmMgrOptionT *option, uint32_t numBlocks)
{
    DB_POINTER(option);
    option->maxNumBlocks = numBlocks;
}

void DbRsmMgrOptionSetMaxBlockSizeAndPageSize(DbRsmMgrOptionT *option, uint32_t maxBlockSize, uint32_t pageSize)
{
    DB_POINTER(option);
    option->maxBlockSize = SIZE_M(maxBlockSize);
    option->pageSize = pageSize;
}

uint32_t DbRsmMgrOptionGetPageSizeInKB(DbRsmMgrOptionT *option)
{
    DB_POINTER(option);
    return (option->pageSize * DB_KIBI);
}

void DbRsmMgrOptionSetCallbacks(DbRsmMgrOptionT *option, DbRsmCallbacksT *callbacks)
{
    DB_POINTER2(option, callbacks);
    option->callbacks = *callbacks;
}

void DbRsmMgrOptionSetIpcPerm(DbRsmMgrOptionT *option, DbRsmIpcPermT *perm)
{
    DB_POINTER2(option, perm);
    option->perm = *perm;
}

void DbRsmMgrOptionSetExtendBlockInfo(DbRsmMgrOptionT *option, uint32_t extendBlockSize, uint32_t extendBlockStartIdx)
{
    DB_POINTER(option);
    if (extendBlockSize == 0) {
        option->isUseExtendBlock = false;
        option->extendBlockSize = 0;
        option->extendBlockStartIdx = DB_INVALID_UINT32;
        return;
    }
    option->isUseExtendBlock = true;
    option->extendBlockSize = SIZE_M(extendBlockSize);
    option->extendBlockStartIdx = extendBlockStartIdx;
}

uint32_t *DbRsmMgrOptionGetBlockSizeArray(DbRsmMgrOptionT *option)
{
    // 管理结构后紧接着blockSize数组
    return (uint32_t *)(option + 1);
}

void DbRsmMgrOptionSetBlockSizeArray(DbRsmMgrOptionT *option, uint32_t totalBlockNum, uint32_t extendBlockSize,
    uint32_t blockMaxSize, uint32_t deviceSize)
{
    DB_POINTER(option);
    uint32_t *blockSizeArr = DbRsmMgrOptionGetBlockSizeArray(option);
    for (uint32_t i = 0; i < totalBlockNum; i++) {
        blockSizeArr[i] = blockMaxSize;
    }
    uint32_t fullBlockNum = extendBlockSize / blockMaxSize;
    option->totalBlockNum = totalBlockNum;
    if (extendBlockSize % blockMaxSize < deviceSize) {
        // 所有block都是完整的，直接返回
        option->extendBlockNum = fullBlockNum;
        return;
    }
    option->extendBlockNum = fullBlockNum + 1;
    uint32_t remainBlockIdx = fullBlockNum + 1;  // 保留内存剩余block起始位置
    uint32_t remainBlockSize = extendBlockSize % blockMaxSize;
    blockSizeArr[remainBlockIdx] = remainBlockSize;
    // 完整block比剩余block大，保证不翻转
    DB_ASSERT(blockMaxSize > remainBlockSize);
    blockSizeArr[remainBlockIdx + 1] = blockMaxSize - remainBlockSize;

    uint32_t remainExtendBlockIdx = totalBlockNum - 1;  // 高端内存剩余block起始位置
    blockSizeArr[remainExtendBlockIdx] = remainBlockSize;
}

struct DbRsmBlockT {
    bool isUsed;
    bool isExtendBlock;
    uint32_t id;
    uint32_t key;
    uint32_t curSize;  // bytes
    uint32_t maxSize;  // bytes
};

typedef struct {
    uint32_t id;
    uint32_t key;
    uint32_t curSize;  // bytes
    uint32_t maxSize;  // bytes
} DbRsmExtendBlockT;

typedef struct {
    DbSpinLockT lock;  // 内存申请加Pid锁
    bool isInit;       // 高端内存是否已完成初始化
    uint8_t reserve[3];
    uint32_t minKey;             // extend block起始key
    uint32_t maxKey;             // extend block最大key
    uint32_t blockNum;           // extend block数量
    uint32_t extendBlockSize;    // bytes
    uint32_t maxBlockSize;       // bytes
    char fileName[DB_MAX_PATH];  // 高端内存空间文件绝对路径
    DbRsmExtendBlockT blockArr[];
} DbRsmExtendBlockMgrT;

struct DbRsmMgrT {  // 存储在保留内存，服务端和客户端共享
    uint32_t magic;
    uint32_t selfKey;
    uint32_t usedMaxKey;
    uint32_t nextKey;  // 下一个创建的保留内存key
    size_t selfSize;
    DbRsmMgrOptionT option;
    ShmemPtrT metaInfoPtr[(uint32_t)DB_RSM_META_TYPE_MAX_CNT];
    DbRsmBlockT blocks[];
};

typedef struct DbRsmLocalMgr {
    DbRsmMgrT *sharedMgr;
    DbRsmExtendBlockMgrT *extendBlockMgr;
    DbRsmCallbacksT callbacks;
    DbRsmCallbacksT extendBlockCallbacks;
    uint8_t *extendBlockStartAddr;
} DbRsmLocalMgrT;

DbRsmLocalMgrT g_gmdbRsmLocalMgr;

#define DB_MAX_RSM_KEY_NUM 256
#define DB_RSM_EXTEND_BLOCK_FILE_NAME "GMDBV5_RSM"

static DbSpinLockT g_gmdbRsmAddrsLock = {0};
static void *g_gmdbRsmAddrs[DB_MAX_RSM_KEY_NUM] = {0};

uint32_t DbRsmBlockCreateIdImpl(void)
{
    DbRsmMgrT *mgr = g_gmdbRsmLocalMgr.sharedMgr;
    DbRsmExtendBlockMgrT *extendBlockMgr = g_gmdbRsmLocalMgr.extendBlockMgr;
    if (mgr->option.isUseExtendBlock && extendBlockMgr != NULL && extendBlockMgr->isInit) {
        // 如果高端内存已完成初始化，优先使用高端内存
        for (uint32_t i = extendBlockMgr->minKey; i <= extendBlockMgr->maxKey; i++) {
            if (!mgr->blocks[i].isUsed) {
                mgr->blocks[i].isUsed = true;
                return i;
            }
        }
    }

    // 按顺序获取keyId
    for (uint32_t i = mgr->nextKey; i < mgr->option.totalBlockNum; i++) {
        if (!mgr->blocks[i].isUsed && !mgr->blocks[i].isExtendBlock) {
            mgr->blocks[i].isUsed = true;
            mgr->nextKey = i + 1;
            return i;
        }
    }
    for (uint32_t i = 1; i < mgr->nextKey; i++) {
        if (!mgr->blocks[i].isUsed && !mgr->blocks[i].isExtendBlock) {
            mgr->blocks[i].isUsed = true;
            mgr->nextKey = i + 1;
            return i;
        }
    }

    return mgr->option.totalBlockNum;
}

DbRsmBlockT *DbRsmBlockGetHandleImpl(uint32_t id)
{
    DbRsmMgrT *mgr = g_gmdbRsmLocalMgr.sharedMgr;
    if (id < mgr->option.totalBlockNum) {
        return &mgr->blocks[id];
    }
    return NULL;
}

uint32_t DbRsmBlockGetMaxSize(DbRsmBlockT *block)
{
    DB_POINTER(block);
    return g_gmdbRsmLocalMgr.sharedMgr->option.maxBlockSize;
}

uint32_t DbRsmBlockGetMgrSelfSize(void)
{
    return g_gmdbRsmLocalMgr.sharedMgr->selfSize;
}

uint32_t DbRsmBlockGetCurSize(DbRsmBlockT *block)
{
    DB_POINTER(block);
    return DbAtomicGet(&block->curSize);
}

static Status DbRsmGetExtendBlockFileName(const char *path, int32_t len, char *name)
{
    int32_t ret = sprintf_s(name, len, "%s/%s", path, DB_RSM_EXTEND_BLOCK_FILE_NAME);
    if (ret < 0) {
        DB_LOG_AND_SET_LASERR(
            GMERR_MEMORY_OPERATE_FAILED, "Get rsm file name, path: %s, os ret no %" PRId32 ".", path, DbAptGetErrno());
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

inline static bool DbIsRsmExtendBlockInit(void)
{
    DbRsmExtendBlockMgrT *extendBlockMgr = g_gmdbRsmLocalMgr.extendBlockMgr;
    if (extendBlockMgr != NULL && extendBlockMgr->isInit) {
        return true;
    }
    return false;
}

Status DbRsmExtendBlockCreateMem(bool isAttach)
{
    DbRsmExtendBlockMgrT *extendBlockMgr = g_gmdbRsmLocalMgr.extendBlockMgr;
    int32_t fd;
    int32_t flags = isAttach ? READ_WRITE : (CREATE_FILE | TRUNCATE_FILE | READ_WRITE);
    Status ret = DbOpenFile(extendBlockMgr->fileName, flags, PERM_USRRW, &fd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Open rsm file %s, attach: %" PRIu32 ", os ret: %" PRId32 ".", extendBlockMgr->fileName,
            (uint32_t)isAttach, DbAptGetErrno());
        return ret;
    }
    if (!isAttach) {
        // 确保有足够空间
        ret = ftruncate(fd, extendBlockMgr->extendBlockSize);
        if (ret != GMERR_OK) {
            DbCloseFile(fd);
            DB_LOG_AND_SET_LASERR(GMERR_FILE_OPERATE_FAILED,
                "Truncate rsm file, path: %s, size: %" PRIu32 ", os ret: %" PRId32 ".", extendBlockMgr->fileName,
                extendBlockMgr->extendBlockSize, DbAptGetErrno());
            return GMERR_FILE_OPERATE_FAILED;
        }
    }
    void *p = mmap(NULL, extendBlockMgr->extendBlockSize, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);
    DbCloseFile(fd);
    if (p == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_FILE_OPERATE_FAILED,
            "Mmap rsm file when init extend block, path: %s, attach: %" PRIu32 ", os ret: %" PRId32 ".",
            extendBlockMgr->fileName, (uint32_t)isAttach, DbAptGetErrno());
        return GMERR_FILE_OPERATE_FAILED;
    }
    g_gmdbRsmLocalMgr.extendBlockStartAddr = p;
    return GMERR_OK;
}

Status DbRsmExtendBlockInitImp(const char *path)
{
    DbRsmExtendBlockMgrT *extendBlockMgr = g_gmdbRsmLocalMgr.extendBlockMgr;
    if (extendBlockMgr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "Rsm extend block mgr is null.");
        // 调用者确保已初始化mgr
        DB_ASSERT(false);
        return GMERR_INTERNAL_ERROR;
    }
    if (extendBlockMgr->isInit) {
        return GMERR_OK;
    }
    if (strlen(path) == 0 || !DbDirExist(path)) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "Init extend block, path is empty or not exist %s.", path);
        return GMERR_INTERNAL_ERROR;
    }
    char extendBlockFileName[DB_MAX_PATH] = {0};
    Status ret = DbRsmGetExtendBlockFileName(path, DB_MAX_PATH, extendBlockFileName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbPidSpinLock(&extendBlockMgr->lock, PID_SHM_LOCK_TIMEOUT_MS);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "Get lock when init rsm, path: %s.", path);
        return ret;
    }
    errno_t cpyRet = strcpy_s(extendBlockMgr->fileName, DB_MAX_PATH, extendBlockFileName);
    if (cpyRet != 0) {
        DbPidSpinUnlock(&extendBlockMgr->lock);
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Cpy rsm extend block file name: %s.", extendBlockFileName);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    ret = DbRsmExtendBlockCreateMem(false);
    if (ret != GMERR_OK) {
        DbPidSpinUnlock(&extendBlockMgr->lock);
        return ret;
    }
    DbPidSpinUnlock(&extendBlockMgr->lock);
    return GMERR_OK;
}

bool DbIsRsmExtendBlockKeyRange(uint32_t key)
{
    DbRsmExtendBlockMgrT *extendBlockMgr = g_gmdbRsmLocalMgr.extendBlockMgr;
    if (key >= extendBlockMgr->minKey && key <= extendBlockMgr->maxKey) {
        return true;
    }
    return false;
}

void *DbRsmExtendBlockMalloc(uint32_t key, size_t size)
{
    if (!DbIsRsmExtendBlockKeyRange(key)) {
        // 保证传入的key有效
        DB_ASSERT(false);
        return NULL;
    }
    if (!DbIsRsmExtendBlockInit()) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Rsm extend block mgr not init when alloc, key: %" PRIu32 ".", key);
        return NULL;
    }
    DbRsmExtendBlockMgrT *extendBlockMgr = g_gmdbRsmLocalMgr.extendBlockMgr;
    Status ret = DbPidSpinLock(&extendBlockMgr->lock, PID_SHM_LOCK_TIMEOUT_MS);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(
            ret, "Get lock when alloc rsm, key: %" PRIu32 ", min_key: %" PRIu32 ".", key, extendBlockMgr->minKey);
        return NULL;
    }
    if (g_gmdbRsmLocalMgr.extendBlockStartAddr == NULL) {
        ret = DbRsmExtendBlockCreateMem(true);
        if (ret != GMERR_OK) {
            DbPidSpinUnlock(&extendBlockMgr->lock);
            return NULL;
        }
    }
    uint32_t blockIdx = key - extendBlockMgr->minKey;
    DbRsmExtendBlockT *block = &extendBlockMgr->blockArr[blockIdx];
    uint8_t *startAddr = g_gmdbRsmLocalMgr.extendBlockStartAddr + (blockIdx * extendBlockMgr->maxBlockSize);

    uint32_t allocOffset = block->curSize;
    block->curSize += (uint32_t)size;
    if (block->curSize > block->maxSize) {
        block->curSize -= (uint32_t)size;
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
            "Alloc rsm, key %" PRIu32 ", curSize: %" PRIu32 ", allocSize %zu, maxSize %" PRIu32 ".", key,
            block->curSize, size, block->maxSize);
        DbPidSpinUnlock(&extendBlockMgr->lock);
        return NULL;
    }
    DbPidSpinUnlock(&extendBlockMgr->lock);
    return startAddr + allocOffset;
}

void DbRsmExtendBlockFree(uint32_t key, size_t size)
{
    if (!DbIsRsmExtendBlockKeyRange(key)) {
        // 保证传入的key有效
        DB_ASSERT(false);
        return;
    }
    DbRsmExtendBlockMgrT *extendBlockMgr = g_gmdbRsmLocalMgr.extendBlockMgr;
    Status ret = DbPidSpinLock(&extendBlockMgr->lock, PID_SHM_LOCK_TIMEOUT_MS);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(
            ret, "Get lock when free rsm, key: %" PRIu32 ", min_key: %" PRIu32 ".", key, extendBlockMgr->minKey);
        return;
    }
    uint32_t blockIdx = key - extendBlockMgr->minKey;
    DbRsmExtendBlockT *block = &extendBlockMgr->blockArr[blockIdx];
    // 确保不发生反转错误
    DB_ASSERT(block->curSize >= size);
    block->curSize -= size;
    DbPidSpinUnlock(&extendBlockMgr->lock);
}

void *DbRsmExtendBlockAt(uint32_t key)
{
    if (!DbIsRsmExtendBlockKeyRange(key)) {
        // 保证传入的key有效
        DB_ASSERT(false);
        return NULL;
    }
    DbRsmExtendBlockMgrT *extendBlockMgr = g_gmdbRsmLocalMgr.extendBlockMgr;
    uint32_t blockIdx = key - extendBlockMgr->minKey;
    if (g_gmdbRsmLocalMgr.extendBlockStartAddr != NULL) {
        return g_gmdbRsmLocalMgr.extendBlockStartAddr + (blockIdx * extendBlockMgr->maxBlockSize);
    }
    if (!extendBlockMgr->isInit) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "Rsm extend block not init when attach.");
        return NULL;
    }
    Status ret = DbRsmExtendBlockCreateMem(true);
    if (ret != GMERR_OK) {
        return NULL;
    }
    return (uint8_t *)g_gmdbRsmLocalMgr.extendBlockStartAddr + (blockIdx * extendBlockMgr->maxBlockSize);
}

Status DbRsmExtendBlockDt(uint32_t key)
{
    if (!DbIsRsmExtendBlockKeyRange(key)) {
        // 保证传入的key有效
        DB_ASSERT(false);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (g_gmdbRsmLocalMgr.extendBlockStartAddr == NULL) {
        return GMERR_OK;
    }
    // 仅客户端退出时会detach，munmap后不再使用
    int32_t ret = munmap(g_gmdbRsmLocalMgr.extendBlockStartAddr, g_gmdbRsmLocalMgr.extendBlockMgr->extendBlockSize);
    g_gmdbRsmLocalMgr.extendBlockStartAddr = NULL;
    if (ret != 0) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR,
            "Unmap rsm extend block when detach, key: %" PRIu32 ", os ret: %" PRId32 ".", key, DbAptGetErrno());
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

Status DbRsmExtendBlockCtl(uint32_t key, uint32_t cmd, DbRsmIpcPermT *perm)
{
    DB_UNUSED3(key, cmd, perm);
    return GMERR_OK;
}

void DbRsmSetExtendBlockCallbacks(DbRsmCallbacksT *extendBlockCallbacks)
{
    DB_POINTER(extendBlockCallbacks);
    extendBlockCallbacks->rsmMalloc = DbRsmExtendBlockMalloc;
    extendBlockCallbacks->rsmFree = DbRsmExtendBlockFree;
    extendBlockCallbacks->rsmAt = DbRsmExtendBlockAt;
    extendBlockCallbacks->rsmDt = DbRsmExtendBlockDt;
    extendBlockCallbacks->rsmCtl = DbRsmExtendBlockCtl;
}

void DbRsmExtendBlockDetachForCltUninit(void)
{
    DbRsmExtendBlockMgrT *extendBlockMgr = g_gmdbRsmLocalMgr.extendBlockMgr;
    if (extendBlockMgr == NULL) {
        return;
    }
    (void)DbRsmExtendBlockDt(extendBlockMgr->minKey);
}

static void *DbRsmAttachBlock(DbRsmBlockT *block)
{
    DB_POINTER(block);
    DbSpinLock(&g_gmdbRsmAddrsLock);
    void *basePtr = !block->isExtendBlock ? g_gmdbRsmLocalMgr.callbacks.rsmAt(block->key) :
                                            g_gmdbRsmLocalMgr.extendBlockCallbacks.rsmAt(block->key);
    if (basePtr == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE,
            "Unable get rsm addr. Block key: %" PRIu32 ", isUsed %" PRIu32 ", curSize %" PRIu32 ".", block->key,
            (uint32_t)block->isUsed, block->curSize);
        DbSpinUnlock(&g_gmdbRsmAddrsLock);
        return NULL;
    }
    if ((void *)DbAtomicGetPtr((uintptr_t *)&g_gmdbRsmAddrs[block->id]) == NULL) {
        DbAtomicSetPtr((uintptr_t *)&g_gmdbRsmAddrs[block->id], (uintptr_t)basePtr);
    }
    DbSpinUnlock(&g_gmdbRsmAddrsLock);
    return basePtr;
}

ShmemPtrT DbRsmBlockAllocImpl(DbRsmBlockT *block, uint32_t size)
{
    if (block == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "rsm block is null.");
        return DB_INVALID_SHMPTR;
    }

    void *p = !block->isExtendBlock ? g_gmdbRsmLocalMgr.callbacks.rsmMalloc(block->key, size) :  // DbAdptRsmMallocImpl
                  g_gmdbRsmLocalMgr.extendBlockCallbacks.rsmMalloc(block->key, size);  // DbRsmExtendBlockMalloc
    if (p == NULL) {
        return DB_INVALID_SHMPTR;
    }
    uint32_t maxKey = g_gmdbRsmLocalMgr.sharedMgr->usedMaxKey;

    if (DbAtomicGet(&block->curSize) == 0) {
        DbAtomicSetPtr((uintptr_t *)&g_gmdbRsmAddrs[block->id], (uintptr_t)p);
        if (!block->isExtendBlock) {
            // 只有保留内存用usedMaxKey
            g_gmdbRsmLocalMgr.sharedMgr->usedMaxKey = maxKey > block->key ? maxKey : block->key;
        }
    }

    void *basePtr = (void *)DbAtomicGetPtr((uintptr_t *)&g_gmdbRsmAddrs[block->id]);
    if (SECUREC_UNLIKELY(basePtr == NULL)) {
        // 服务端申请新block并申请内存，block中的curSize更新，客户端的g_gmdbRsmAddrs未更新，需重新attach
        basePtr = DbRsmAttachBlock(block);
        if (SECUREC_UNLIKELY(basePtr == NULL)) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE,
                "Unable get rsm addr when alloc. Block key: %" PRIu32 ", isUsed %" PRIu32 ", curSize %" PRIu32 ".",
                block->key, (uint32_t)block->isUsed, block->curSize);
            return DB_INVALID_SHMPTR;
        }
    }

    (void)DbAtomicAdd(&block->curSize, size);

    uint32_t offset = (uint32_t)(p - basePtr);
    return (ShmemPtrT){.offset = offset, .segId = DB_RSM_MAKE_DUMMY_SEGID(block->key)};
}

ShmemPtrT DbRsmBlockAllocAlignImpl(DbRsmBlockT *block, uint32_t size, uint32_t alignSize)
{
    // 该接口基于两个前提来实现的：
    // 1. 向该block申请内存不会有并发（上层保证）
    // 2. 保留内存的空间是连续分配的：
    // 因此本函数中的tmp变量只需再多申请一个alignSize大小、return rsmPtr，使用DbShmPtrToAddrAlign就能对齐到正确的位置
    ShmemPtrT rsmPtr = DbRsmBlockAlloc(block, size);
    if (!DbIsShmPtrValid(rsmPtr)) {
        return rsmPtr;
    }
    DB_UINTPTR ptr = (DB_UINTPTR)DbRsmPtrToAddr(rsmPtr);
    if (ptr == 0u) {
        DbRsmBlockFree(block, size);
        return DB_INVALID_SHMPTR;
    }
    if (ptr % alignSize == 0) {
        return rsmPtr;
    }
    ShmemPtrT tmp = DbRsmBlockAlloc(block, alignSize);
    if (!DbIsShmPtrValid(tmp)) {
        DbRsmBlockFree(block, size);
        return DB_INVALID_SHMPTR;
    }
    DB_ASSERT(tmp.offset == rsmPtr.offset + size);
    return rsmPtr;
}

void DbRsmBlockFreeImpl(DbRsmBlockT *block, uint32_t size)  // zhangyingjie alignSize有内存泄漏，需要解决
{
    if (block == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "rsm block is null.");
        return;
    }
    if (DbAtomicGet(&block->curSize) < size) {
        return;
    }
    // DbAdptRsmFreeImpl
    !block->isExtendBlock ? g_gmdbRsmLocalMgr.callbacks.rsmFree(block->key, size) :
                            g_gmdbRsmLocalMgr.extendBlockCallbacks.rsmFree(block->key, size);
    (void)DbAtomicSub(&block->curSize, size);
}

void *DbRsmPtrToAddrImpl(ShmemPtrT shmPtr)
{
    if (SECUREC_UNLIKELY(!DbIsShmPtrValid(shmPtr) || CheckShmPtrZero(shmPtr))) {
        return NULL;
    }
    uint32_t blockIdx = DB_RSM_GET_REAL_SEGID(shmPtr.segId) - g_gmdbRsmLocalMgr.sharedMgr->option.keyMin;
    void *basePtr = DbRsmAttachBlock(&g_gmdbRsmLocalMgr.sharedMgr->blocks[blockIdx]);
    if (basePtr == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE,
            "Unable attach rsm. SegId: %" PRIu32 ", realSegId:%" PRIu32 ", offset: %" PRIu32 ".", shmPtr.segId,
            DB_RSM_GET_REAL_SEGID(shmPtr.segId), shmPtr.offset);
        return NULL;
    }
    return basePtr + shmPtr.offset;
}

void *DbRsmPtrToAddrWithCheckImpl(ShmemPtrT shmPtr)
{
    uint32_t blockIdx = DB_RSM_GET_REAL_SEGID(shmPtr.segId) - g_gmdbRsmLocalMgr.sharedMgr->option.keyMin;
    void *baseAddr = DbRsmAttachBlock(&g_gmdbRsmLocalMgr.sharedMgr->blocks[blockIdx]);
    if (baseAddr == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE,
            "Unable attach rsm. SegId: %" PRIu32 ", realSegId:%" PRIu32 ", offset: %" PRIu32 ".", shmPtr.segId,
            DB_RSM_GET_REAL_SEGID(shmPtr.segId), shmPtr.offset);
        return NULL;
    }
    if (DbCommonGetWarmReboot()) {
        DbReserveMemCtxT *topRsmemCtx = DbRsmGetTopRsmemCtxImpl();
        if (topRsmemCtx != NULL && !IsShmemPtrEqual(shmPtr, topRsmemCtx->header.shmPtrSelf)) {
            DbReserveMemAlgoT *rsmemAlgo = &topRsmemCtx->rsmemAlgo;
            DbRsmChunkMetaT *chunkMeta =
                (DbRsmChunkMetaT *)(void *)(baseAddr + shmPtr.offset -
                                            MALLOCALIGN(rsmemAlgo->alignSize, sizeof(DbRsmChunkMetaT)));
            DB_ASSERT((chunkMeta->isUsed && chunkMeta->magicNum == RSM_CHUNK_META_MAGIC_NUMBER) ||
                      ((!chunkMeta->isUsed && chunkMeta->magicNum == RSM_CHUNK_META_MAGIC_FREE_NUMBER)));
            // 删除子RsmMemCtx时，会取nextMemCtx，这时候isUsed不一定为true
            if (chunkMeta->isUsed) {
                chunkMeta->version = rsmemAlgo->version;
            }
        }
    }

    return baseAddr + shmPtr.offset;
}

DbRsmExtendBlockMgrT *DbRsmGetExtendBlockMgr(uint8_t *startAddr, uint32_t totalBlockNum)
{
    size_t offset = sizeof(DbRsmMgrT) + totalBlockNum * sizeof(DbRsmBlockT);
    return (DbRsmExtendBlockMgrT *)(startAddr + offset);
}

void DbRsmExtendBlockMgrInitParam(DbRsmMgrOptionT *option, DbRsmExtendBlockMgrT *extendBlockMgr)
{
    DbSpinInit(&extendBlockMgr->lock);
    extendBlockMgr->isInit = false;
    extendBlockMgr->minKey = option->extendBlockStartIdx;
    extendBlockMgr->maxKey = option->totalBlockNum - 1;
    extendBlockMgr->blockNum = option->extendBlockNum;
    extendBlockMgr->extendBlockSize = option->extendBlockSize;
    extendBlockMgr->maxBlockSize = option->maxBlockSize;
    uint32_t *blockSizeArr = DbRsmMgrOptionGetBlockSizeArray(option);
    for (uint32_t i = 0; i < extendBlockMgr->blockNum; i++) {
        extendBlockMgr->blockArr[i].id = i;
        extendBlockMgr->blockArr[i].key = extendBlockMgr->minKey + i;
        extendBlockMgr->blockArr[i].curSize = 0;
        extendBlockMgr->blockArr[i].maxSize = SIZE_M(blockSizeArr[extendBlockMgr->minKey + i]);
    }
}

DbRsmMgrT *DbRsmMgrCreate(DbRsmMgrOptionT *option)
{
    DB_POINTER(option);

    size_t size;
    if (!option->isUseExtendBlock) {
        size = sizeof(DbRsmMgrT) + option->totalBlockNum * sizeof(DbRsmBlockT);
    } else {
        // 管理结构顺序: DbRsmMgrT | totalNum * DbRsmBlockT | DbRsmExtendBlockMgrT | extendBlockNum *DbRsmExtendBlockT
        size = sizeof(DbRsmMgrT) + option->totalBlockNum * sizeof(DbRsmBlockT) + sizeof(DbRsmExtendBlockMgrT) +
               option->extendBlockNum * sizeof(DbRsmExtendBlockT);
    }
    DbRsmMgrT *mgr = (DbRsmMgrT *)option->callbacks.rsmMalloc(option->keyMin, size);
    if (mgr == NULL) {
        return NULL;
    }
    (void)memset_s(mgr, size, 0, size);
    mgr->magic = DB_RSMEM_MAGIC_NUM;
    mgr->option = *option;
    mgr->selfKey = option->keyMin;
    mgr->usedMaxKey = option->keyMin;
    mgr->selfSize = size;
    mgr->nextKey = 1;

    for (uint32_t i = 0; i < (uint32_t)DB_RSM_META_TYPE_MAX_CNT; i++) {
        mgr->metaInfoPtr[i] = DB_INVALID_SHMPTR;
    }
    uint32_t *blockSizeArr = DbRsmMgrOptionGetBlockSizeArray(option);
    for (uint32_t i = 0; i < option->totalBlockNum; i++) {
        DbRsmBlockT *rsmBlock = &mgr->blocks[i];
        rsmBlock->isUsed = false;
        rsmBlock->isExtendBlock = (i < option->extendBlockStartIdx) ? false : true;
        rsmBlock->id = i;
        rsmBlock->key = option->keyMin + i;
        rsmBlock->curSize = 0;
        rsmBlock->maxSize = SIZE_M(blockSizeArr[i]);
    }
    // fill first block content
    mgr->blocks[0].isUsed = true;
    mgr->blocks[0].curSize = (uint32_t)size;
    g_gmdbRsmAddrs[0] = (void *)mgr;
    g_gmdbRsmLocalMgr.sharedMgr = mgr;
    g_gmdbRsmLocalMgr.callbacks = option->callbacks;
    if (option->isUseExtendBlock) {
        DbRsmExtendBlockMgrT *extendBlockMgr = DbRsmGetExtendBlockMgr((uint8_t *)mgr, option->totalBlockNum);
        DbRsmExtendBlockMgrInitParam(option, extendBlockMgr);
        g_gmdbRsmLocalMgr.extendBlockMgr = extendBlockMgr;
        DbRsmSetExtendBlockCallbacks(&g_gmdbRsmLocalMgr.extendBlockCallbacks);
    }
    return mgr;
}

DbRsmMgrT *DbRsmMgrAttach(DbRsmMgrOptionT *option)
{
    DB_POINTER(option);
    DbSpinLock(&g_gmdbRsmAddrsLock);
    void *basePtr = (void *)DbAtomicGetPtr((uintptr_t *)&g_gmdbRsmAddrs[0]);
    if (basePtr != NULL) {
        DbSpinUnlock(&g_gmdbRsmAddrsLock);
        return (DbRsmMgrT *)basePtr;
    }

    DbRsmMgrT *mgr = (DbRsmMgrT *)option->callbacks.rsmAt(option->keyMin);
    if (mgr == NULL) {
        DbSpinUnlock(&g_gmdbRsmAddrsLock);
        return NULL;
    }

    g_gmdbRsmAddrs[0] = (void *)mgr;
    for (uint32_t i = 1; i <= mgr->usedMaxKey; i++) {
        if (!mgr->blocks[i].isUsed) {
            continue;
        }
        g_gmdbRsmAddrs[i] = option->callbacks.rsmAt(option->keyMin + i);
        if (g_gmdbRsmAddrs[i] == NULL) {
            DbSpinUnlock(&g_gmdbRsmAddrsLock);
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "rsm block is null. usedMaxKey:%" PRIu32 ", i:%" PRIu32,
                mgr->usedMaxKey, i);
            return NULL;
        }
    }
    g_gmdbRsmLocalMgr.sharedMgr = mgr;
    mgr->option.callbacks = option->callbacks;
    g_gmdbRsmLocalMgr.callbacks = option->callbacks;
    if (mgr->option.isUseExtendBlock) {
        g_gmdbRsmLocalMgr.extendBlockMgr = DbRsmGetExtendBlockMgr((uint8_t *)mgr, mgr->option.totalBlockNum);
        DbRsmSetExtendBlockCallbacks(&g_gmdbRsmLocalMgr.extendBlockCallbacks);
        g_gmdbRsmLocalMgr.extendBlockStartAddr = NULL;
    }
    DbSpinUnlock(&g_gmdbRsmAddrsLock);
    return mgr;
}

void DbRsmMgrDetach(void)
{
    DbSpinLock(&g_gmdbRsmAddrsLock);
    g_gmdbRsmLocalMgr = (DbRsmLocalMgrT){0};
    (void)memset_s(g_gmdbRsmAddrs, sizeof(void *) * DB_MAX_RSM_KEY_NUM, 0, sizeof(void *) * DB_MAX_RSM_KEY_NUM);
    DbSpinUnlock(&g_gmdbRsmAddrsLock);
}

void DbRsmMgrDestroy(DbRsmMgrT *mgr)
{
    DB_POINTER(mgr);

    g_gmdbRsmLocalMgr.callbacks.rsmFree(mgr->selfKey, mgr->selfSize);
    g_gmdbRsmLocalMgr = (DbRsmLocalMgrT){0};
}

void DbRsmMgrSetMetaInfoImpl(DbRsmMetaInfoTypeE type, ShmemPtrT ptr)
{
    g_gmdbRsmLocalMgr.sharedMgr->metaInfoPtr[type] = ptr;
}

ShmemPtrT DbRsmMgrGetMetaInfoImpl(DbRsmMetaInfoTypeE type)
{
    if (DbCommonIsServer()) {
        // 处理服务端按保留内存编译，但在启动时未带保留内存参数的情况
        bool isUseRsm = DbCfgGetInt32Lite(DB_CFG_IS_USE_RSM, NULL) == 0 ? false : true;
        if (!isUseRsm) {
            return DB_INVALID_SHMPTR;
        }
    }
    return g_gmdbRsmLocalMgr.sharedMgr->metaInfoPtr[type];
}

uint32_t DbRsmMgrGetMinKey(void)
{
    DB_POINTER(g_gmdbRsmLocalMgr.sharedMgr);
    return g_gmdbRsmLocalMgr.sharedMgr->selfKey;
}
uint32_t DbRsmMgrGetMaxKey(void)
{
    DB_POINTER(g_gmdbRsmLocalMgr.sharedMgr);
    return g_gmdbRsmLocalMgr.sharedMgr->usedMaxKey;
}

void DbRsmBlockCheckAndFreeMemoryImpl(uint32_t keyId, uint32_t usedSize)
{
    DbRsmBlockT *block = DbRsmBlockGetHandleImpl(keyId);
    if (block == NULL) {
        DB_LOG_ERROR(
            GMERR_UNEXPECTED_NULL_VALUE, "rsm block is null. keyId %" PRIu32 ", usedSize %" PRIu32, keyId, usedSize);
        return;
    }
    // 恢复阶段调用，no need to lock block
    DB_ASSERT(block->curSize >= usedSize);
    if (block->curSize == usedSize) {
        return;
    }
    DB_LOG_INFO("rsm block check and free, keyId %" PRIu32 ", usedSize %" PRIu32 ", currSize %" PRIu32, keyId, usedSize,
        block->curSize);
    uint32_t freeSize = block->curSize - usedSize;
    DbRsmBlockFreeImpl(block, freeSize);
}

Status DbRsmCreateMemCtxImpl(const char *ctxName, const DbRsmCreateMemCtxOptionT *option, void **outputMemCtx)
{
    // 子rsmCtx不记录rsmUndo，如果重启，则在表恢复完成后一把删除所有子memCtx
    DbMemCtxT *topRsmemCtx = (DbMemCtxT *)DbRsmGetTopRsmemCtx();
    if (topRsmemCtx == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get top ctx when create rsmem ctx, ctx name %s", ctxName);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DbMemCtxArgsT ctxArgs = {0};
    ctxArgs.instanceId = DbGetProcGlobalId();
    DbReserveMemParamT rsmParam;
    AlgoParamT algoParam;
    // 如果传入option为空，则使用默认配置:RSM_MEMCTX_BASE_SIZE,RSM_MEMCTX_STEP_SIZE
    if (option != NULL) {
        rsmParam.alignSize = option->alignSize;
        rsmParam.baseSize = option->baseSize;
        rsmParam.stepSize = option->stepSize;
        rsmParam.maxSize = option->maxSize;
        algoParam.rsmParam = &rsmParam;
        ctxArgs.algoParam = &algoParam;
        if (option->ctxId != DB_MAX_UINT32) {
            ctxArgs.ctxId = option->ctxId;
        }
    }
    DbMemCtxT *rsmCtx = DbCreateReserveMemCtx(topRsmemCtx, ctxName, &ctxArgs);
    if (rsmCtx == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to create rsmem ctx, ctx name: %s.", ctxName);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *outputMemCtx = (void *)rsmCtx;
    return GMERR_OK;
}

void DbRsmDeleteMemCtxImpl(void *ctx)
{
    DbDeleteShmemCtx((DbMemCtxT *)ctx);
}

static void RsmCtxLock(DbReserveMemCtxT *ctx)
{
    DbSpinLock(&ctx->rsmemAlgo.lock);
}

static void RsmCtxUnlock(DbReserveMemCtxT *ctx)
{
    DbSpinUnlock(&ctx->rsmemAlgo.lock);
}

Status DbRsmHandleRecoveryCtx(void)
{
    // 如果恢复sessionCtx被使用过了，交由使用者进行处理，重新创建一个新的挂载上去
    // 如果没被使用，维持不变即可
    uint32_t recoveryCtxId;
    bool IsRecoveryCtxUsed = DbCommonIsRecoverySessionCtxUsed(&recoveryCtxId);
    if (IsRecoveryCtxUsed) {
        DbMemCtxT *recoverySessionCtx;
        Status ret = DbRsmCreateMemCtx("session rsm ctx recovery", NULL, (void **)&recoverySessionCtx);
        if (ret != GMERR_OK) {
            return ret;
        }
        // 首次创建子memCtx时，不会按照baseSize去申请内存，此处先申请再释放，使得按baseSize进行扩展，保证可用，后续调整
        ShmemPtrT shmPtr = DbShmemCtxAlloc(recoverySessionCtx, sizeof(uint32_t));
        DB_ASSERT(DbIsShmPtrValid(shmPtr));
        DbShmemCtxFree(recoverySessionCtx, shmPtr);
        DbRsmMgrSetMetaInfo(DB_RSM_META_TYPE_RECOVERY_RSMEM_CTX_PTR, recoverySessionCtx->shmPtrSelf);
        DbCommonSetRecoverySessionCtxNotUse();
    }
    return GMERR_OK;
}

Status DbRsmDeleteAllChildMemCtxImpl(void)
{
    DbMemCtxT *topCtx = (DbMemCtxT *)DbRsmGetTopRsmemCtx();
    if (topCtx == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get top ctx when delete rsmem ctx");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (topCtx->childNum == 0) {
        return GMERR_OK;
    }
    DbReserveMemCtxT *topRsmCtx = (DbReserveMemCtxT *)topCtx;
    RsmCtxLock(topRsmCtx);
    DbMemCtxT **childCtx = (DbMemCtxT **)DB_MALLOC((uint32_t)(sizeof(DbMemCtxT *) * topCtx->childNum));
    if (SECUREC_UNLIKELY(childCtx == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to delete rsm child ctx, child num %" PRIu32, topCtx->childNum);
        RsmCtxUnlock(topRsmCtx);
        return GMERR_OUT_OF_MEMORY;
    }
    uint32_t index = 0;
    uint32_t childNum = topCtx->childNum;
    DbMemCtxT *nextChild = (DbMemCtxT *)DbRsmPtrToAddrImpl(topCtx->shmCtxPtrs.shmPtrHead);
    while (nextChild != NULL) {
        DB_ASSERT(index < childNum);
        childCtx[index++] = nextChild;
        nextChild = (DbMemCtxT *)DbRsmPtrToAddrImpl(nextChild->shmCtxPtrs.shmPtrNext);
    }
    uint32_t delNum = 0;
    for (uint32_t i = 0; i < index; ++i) {
        DbMemCtxT *child = childCtx[i];
        DB_ASSERT(child != NULL);
        if (((DbReserveMemCtxT *)child)->rsmemAlgo.isWarmReboot) {
            uint32_t ctxId = ((DbReserveMemCtxT *)child)->header.ctxId;
            if (ctxId != (uint32_t)DB_ESCAPE_RSMEMCTX_ID) {
                DbRsmDeleteMemCtxImpl(child);
                delNum++;
            } else {
                DbRsmCtxReset(child);
            }
        }
    }
    RsmCtxUnlock(topRsmCtx);
    DB_FREE(childCtx);
    DB_LOG_INFO("Delete rsmem child ctx, delete num %" PRIu32 ", before child num %" PRIu32
                ", current child num %" PRIu32,
        delNum, childNum, topCtx->childNum);
    return DbRsmHandleRecoveryCtx();
}

void *DbRsmMemCtxAllocImpl(void *ctx, uint32_t size, ShmemPtrT *shmPtr)
{
    DB_POINTER(shmPtr);
    DbMemCtxT *rsmCtx = (DbMemCtxT *)ctx;
    ShmemPtrT ptr = DbShmemCtxAlloc(rsmCtx, size);
    if (!DbIsShmPtrValid(ptr)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "rsm ctx alloc unsucc, size:%" PRIu32, size);
        return NULL;
    }
    void *p = DbRsmPtrToAddrImpl(ptr);
    if (p == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "rsm ptr to addr unsucc, segId:%" PRIu32 ", offset:%" PRIu32,
            ptr.segId, ptr.offset);
        DbShmemCtxFree(rsmCtx, ptr);
        return NULL;
    }
    *shmPtr = ptr;
    return p;
}

void *DbRsmAllocImpl(uint32_t size, ShmemPtrT *shmPtr)
{
    DB_POINTER(shmPtr);
    DbMemCtxT *topRsmemCtx = DbRsmGetTopRsmemCtx();
    return DbRsmMemCtxAllocImpl(topRsmemCtx, size, shmPtr);
}

void DbRsmFreeImpl(ShmemPtrT shmPtr)
{
    DbShmemCtxFree(DbRsmGetTopRsmemCtx(), shmPtr);
}

void DbRsmFreeNoLockImpl(ShmemPtrT shmPtr)
{
    DbShmemCtxFreeNoLock(DbRsmGetTopRsmemCtx(), shmPtr);
}

void *DbRsmGetMetaInfoPtrImpl(DbRsmMetaInfoTypeE metaInfoType, ShmemPtrT *shmPtr)
{
    ShmemPtrT ptr = DbRsmMgrGetMetaInfo(metaInfoType);
    if (!DbIsShmPtrValid(ptr)) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Get metaInfo unsucc, type:%" PRId32, (int32_t)metaInfoType);
        return NULL;
    }
    void *p = DbRsmPtrToAddrWithCheckImpl(ptr);
    if (p == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE,
            "DbRsmPtrToAddr for metaInfo unsucc, type:%" PRId32 ", segId:%" PRIu32 ", offset:%" PRIu32,
            (int32_t)metaInfoType, ptr.segId, ptr.offset);
        return p;
    }
    if (shmPtr != NULL) {
        *shmPtr = ptr;
    }
    return p;
}

static void DbRsmCheckAndFreeSegChunks(DbReserveMemCtxT *topRsmemCtx, DbRsmSegHeaderT *segHeader, ShmemPtrT segRsmPtr)
{
    uint32_t alignedMetaSize = MALLOCALIGN(topRsmemCtx->rsmemAlgo.alignSize, sizeof(DbRsmChunkMetaT));
    uint32_t offset = MALLOCALIGN(topRsmemCtx->rsmemAlgo.alignSize, sizeof(DbRsmSegHeaderT));
    DbRsmChunkMetaT *chunkMeta = NULL;
    while (offset < segHeader->allocSize) {
        chunkMeta = (DbRsmChunkMetaT *)(void *)((uint8_t *)segHeader + offset);
        DB_ASSERT((chunkMeta->isUsed && chunkMeta->magicNum == RSM_CHUNK_META_MAGIC_NUMBER) ||
                  ((!chunkMeta->isUsed && chunkMeta->magicNum == RSM_CHUNK_META_MAGIC_FREE_NUMBER)));
        if (!chunkMeta->isUsed) {
            offset += chunkMeta->chunkAreaSize;
            continue;
        }
        if (chunkMeta->version == topRsmemCtx->rsmemAlgo.version) {
            offset += chunkMeta->chunkAreaSize;
            continue;
        }
        ShmemPtrT chunkMetaPtr = {.offset = segRsmPtr.offset + offset + alignedMetaSize, .segId = segRsmPtr.segId};
        DbRsmFreeNoLockImpl(chunkMetaPtr);
        DB_LOG_INFO_UNFOLD(
            "topRsmCtx check, free segId:%" PRIu32 ", offset:%" PRIu32, chunkMetaPtr.segId, chunkMetaPtr.offset);
        offset += chunkMeta->chunkAreaSize;
    }
}

uint32_t DbRsmGetBlockTotalSize(DbReserveMemCtxT *topRsmemCtx, DbRsmSegMgrT *segMgr)
{
    uint32_t rsmMgrSize = DbRsmBlockGetMgrSelfSize();
    uint32_t ctxHeaderSize = topRsmemCtx->rsmemAlgo.rsmHeaderUsedSize;
    uint32_t totalSegmentSize = segMgr->segTotalSize;
    return (rsmMgrSize + ctxHeaderSize + totalSegmentSize);
}

void DbRsmCheckTopMemoryCtx(RsmCheckCtxT *ctx)
{
    DbReserveMemCtxT *topRsmemCtx = (DbReserveMemCtxT *)DbRsmGetTopRsmemCtx();
    if (topRsmemCtx == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get top ctx when check top rsmem ctx");
        return;
    }
    DbRsmSegMgrT *segMgr = (DbRsmSegMgrT *)DbRsmPtrToAddrImpl(topRsmemCtx->rsmemAlgo.segMgrPtr);
    if (segMgr == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get seg mgr when check top rsmem ctx");
        return;
    }
    if (DbMemCtxLock((DbMemCtxT *)topRsmemCtx) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Unable to lock when check rsm memory.");
        return;
    }
    DbRsmBlockCheckAndFreeMemory(segMgr->blockId, DbRsmGetBlockTotalSize(topRsmemCtx, segMgr));
    DbRsmSegHeaderT *segHeader = NULL;
    DbSpinLock(&segMgr->lock);
    for (uint32_t i = 0; i < segMgr->segUsedCnt; i++) {
        DB_ASSERT(segMgr->segOffset[i] != RSM_INVALID_SEG_OFFSET);
        ShmemPtrT segRsmPtr = {.segId = segMgr->segId, .offset = segMgr->segOffset[i]};
        if (!DbIsShmPtrValid(segRsmPtr)) {
            continue;
        }
        segHeader = (DbRsmSegHeaderT *)DbRsmPtrToAddr(segRsmPtr);
        if (segHeader == NULL) {
            continue;
        }
        if (segHeader->isUsed && segHeader->owner == topRsmemCtx->header.shmPtrSelf.offset) {
            DbRsmCheckAndFreeSegChunks(topRsmemCtx, segHeader, segRsmPtr);
        }
    }
    DbSpinUnlock(&segMgr->lock);
    DbMemCtxUnLock((DbMemCtxT *)topRsmemCtx);
}

void DbSetRsmCheckTopRsmmemCtxHandleImpl(void)
{
    static bool isSet = false;
    if (isSet) {
        return;
    }
    isSet = true;

    const RsmCheckHandleFuncConfT conf = {
        .type = RSM_CHECK_TOP_RSMEM_CTX,
        .func = DbRsmCheckTopMemoryCtx,
    };

    DbRsmCheckSetHandleFunc(conf);
}

Status DbRsmFlagCreateImpl(void)
{
    ShmemPtrT rsmFlagShm;
    uint8_t *rsmFlag = DbRsmAlloc(sizeof(uint8_t), &rsmFlagShm);
    if (rsmFlag == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "RsmAlloc for rsm kernel unsucc");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s((void *)rsmFlag, sizeof(uint8_t), 0x0, sizeof(uint8_t));
    DbRsmMgrSetMetaInfo(DB_RSM_META_TYPE_RECOVERY_FLAG, rsmFlagShm);
    return GMERR_OK;
}

Status DbRsmGetRsmFlagImpl(uint8_t *rsmFlag)
{
    uint8_t *rsmFlagTmp = DbRsmGetMetaInfoPtr(DB_RSM_META_TYPE_RECOVERY_FLAG, NULL);
    if (rsmFlagTmp == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Get metaInfoPtr for rsm kernel unsucc");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *rsmFlag = *rsmFlagTmp;
    return GMERR_OK;
}

void DbRsmResetRecoverySessionCtxImpl(void *ctx)
{
    DbRsmCtxReset(ctx);
}

Status DbRsmBlockCopyToExtendBlock(DbRsmBlockT *dstBlock, DbRsmBlockT *srcBlock)
{
    if (!srcBlock->isUsed || srcBlock->curSize == 0) {
        // 当前保留内存block未被使用，不copy
        return GMERR_OK;
    }
    void *srcPtr = (void *)DbAtomicGetPtr((uintptr_t *)&g_gmdbRsmAddrs[srcBlock->id]);
    if (SECUREC_UNLIKELY(srcPtr == NULL)) {
        srcPtr = DbRsmAttachBlock(srcBlock);
        if (SECUREC_UNLIKELY(srcPtr == NULL)) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE,
                "Get rsm addr when copy block. src key: %" PRIu32 ", dst key: %" PRIu32 ", curSize %" PRIu32 ".",
                srcBlock->key, dstBlock->key, srcBlock->curSize);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
    }
    DbRsmExtendBlockMgrT *extendBlockMgr = g_gmdbRsmLocalMgr.extendBlockMgr;
    uint32_t extendBlockId = dstBlock->key - extendBlockMgr->minKey;
    void *dstPtr = g_gmdbRsmLocalMgr.extendBlockStartAddr + (extendBlockId * extendBlockMgr->maxBlockSize);
    // 初始化时已确保两个block大小一致
    DB_ASSERT(srcBlock->maxSize == dstBlock->maxSize);
    errno_t cpyRet = memcpy_s(dstPtr, srcBlock->curSize, srcPtr, srcBlock->curSize);
    if (SECUREC_UNLIKELY(cpyRet != EOK)) {
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW,
            "Copy rsm block, srcKey %" PRIu32 ", dstKey %" PRIu32 ", cpy ret %" PRId32 ".", srcBlock->key,
            dstBlock->key, cpyRet);
        return GMERR_FIELD_OVERFLOW;
    }
    extendBlockMgr->blockArr[extendBlockId].curSize = srcBlock->curSize;
    dstBlock->curSize = srcBlock->curSize;
    dstBlock->isUsed = true;
    return GMERR_OK;
}

Status DbRsmMigrationToExtendBlockImp(void)
{
    DbRsmExtendBlockMgrT *extendBlockMgr = g_gmdbRsmLocalMgr.extendBlockMgr;
    DbRsmMgrT *mgr = g_gmdbRsmLocalMgr.sharedMgr;
    for (uint32_t i = 0; i < extendBlockMgr->blockNum; i++) {
        uint32_t srcBlockId = i + 1;  // 保留内存数据block从第一个开始
        uint32_t dstBlockId = extendBlockMgr->minKey + i;
        DbRsmBlockT *srcBlock = &mgr->blocks[srcBlockId];
        DbRsmBlockT *dstBlock = &mgr->blocks[dstBlockId];
        Status ret = DbRsmBlockCopyToExtendBlock(dstBlock, srcBlock);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status DbRsmMigrationFreeBlockAndSetFinishImp(void)
{
    DbRsmExtendBlockMgrT *extendBlockMgr = g_gmdbRsmLocalMgr.extendBlockMgr;
    DbRsmMgrT *mgr = g_gmdbRsmLocalMgr.sharedMgr;
    uint32_t maxSrcBlockNum = mgr->option.totalBlockNum - extendBlockMgr->blockNum - 1;
    uint32_t migrateBlockNum = DB_MIN(extendBlockMgr->blockNum, maxSrcBlockNum);
    for (uint32_t i = 0; i < migrateBlockNum; i++) {
        uint32_t srcBlockId = i + 1;  // 保留内存第0个block为控制block，数据block从第一个开始
        DbRsmBlockT *srcBlock = &mgr->blocks[srcBlockId];
        if (srcBlock->isUsed) {
            // 释放已迁移的保留内存
            if (srcBlock->curSize != 0) {
                DbRsmBlockFreeImpl(srcBlock, srcBlock->curSize);
            }
            srcBlock->isUsed = false;
        }
    }
    extendBlockMgr->isInit = true;
    return GMERR_OK;
}

const DbRsmemAmT g_gmdbRsmemAm = {.dbRsmBlockCreateIdFunc = DbRsmBlockCreateIdImpl,
    .dbRsmBlockGetHandleFunc = DbRsmBlockGetHandleImpl,
    .dbRsmBlockAllocFunc = DbRsmBlockAllocImpl,
    .dbRsmBlockAllocAlignFunc = DbRsmBlockAllocAlignImpl,
    .dbRsmBlockFreeFunc = DbRsmBlockFreeImpl,
    .dbRsmPtrToAddrFunc = DbRsmPtrToAddrImpl,
    .dbRsmPtrToAddrWithCheckFunc = DbRsmPtrToAddrWithCheckImpl,
    .dbRsmGetTopRsmemCtxFunc = DbRsmGetTopRsmemCtxImpl,
    .dbRsmGetRsmemAlgoMethodsFunc = DbRsmGetRsmemAlgoMethodsImpl,
    .dbRsmMgrSetMetaInfoFunc = DbRsmMgrSetMetaInfoImpl,
    .dbRsmMgrGetMetaInfoFunc = DbRsmMgrGetMetaInfoImpl,
    .dbRsmInitBasicRsmemElemsFunc = DbRsmInitBasicRsmemElemsImpl,
    .dbRsmCommonInitCltRsmFunc = DbRsmCommonInitCltRsmImpl,
    .dbRsmCommonUnInitCltRsmFunc = DbRsmCommonUnInitCltRsmImpl,
    .dbRsmGlobalConfigFlashIdFunc = RsmemGlobalConfigFlashIdImpl,
    .dbRsmBlockCheckAndFreeMemoryFunc = DbRsmBlockCheckAndFreeMemoryImpl,
    .dbRsmCreateMemCtxFunc = DbRsmCreateMemCtxImpl,
    .dbRsmDeleteMemCtxFunc = DbRsmDeleteMemCtxImpl,
    .dbRsmDeleteAllChildMemCtxFunc = DbRsmDeleteAllChildMemCtxImpl,
    .dbRsmMemCtxAllocFunc = DbRsmMemCtxAllocImpl,
    .dbRsmAllocFunc = DbRsmAllocImpl,
    .dbRsmFreeFunc = DbRsmFreeImpl,
    .dbRsmGetMetaInfoPtrFunc = DbRsmGetMetaInfoPtrImpl,
    .dbSetRsmCheckTopRsmmemCtxHandleFunc = DbSetRsmCheckTopRsmmemCtxHandleImpl,
    .dbRsmGetMemCtxStatViewFunc = DbRsmGetMemCtxStatViewImpl,
    .dbRsmGetEscapeCtxFunc = DbRsmGetEscapeCtxImpl,
    .dbRsmCheckEscapeCtxMarkFunc = DbRsmCheckEscapeCtxMarkImpl,
    .dbRsmGetUserAllocSizeFunc = DbRsmGetUserAllocSizeImpl,
    .dbRsmCreateRsmFlagFunc = DbRsmFlagCreateImpl,
    .dbRsmGetRsmFlagFunc = DbRsmGetRsmFlagImpl,
    .dbRsmRecoveryCtxResetFunc = DbRsmResetRecoverySessionCtxImpl,
    .dbRsmExtendBlockInitFunc = DbRsmExtendBlockInitImp,
    .dbRsmMigrationToExtendBlockFunc = DbRsmMigrationToExtendBlockImp,
    .dbRsmMigrationFreeBlockAndSetFinishFunc = DbRsmMigrationFreeBlockAndSetFinishImp};

#ifdef __cplusplus
}
#endif
