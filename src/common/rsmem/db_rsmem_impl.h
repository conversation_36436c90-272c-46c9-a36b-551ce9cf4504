/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: reserve memory implementation.
 * Author: zhangyingjie
 * Create: 2024-02-17
 */

#ifndef DB_RSMEM_IMPL_H
#define DB_RSMEM_IMPL_H

#include <assert.h>
#include "adpt_rsmem.h"
#include "db_rsmem.h"

#ifdef __cplusplus
extern "C" {
#endif

// rsm mgr option
typedef struct DbRsmMgrOptionT DbRsmMgrOptionT;

DbRsmMgrOptionT *DbRsmMgrOptionCreate(uint32_t totalBlockNum);
void DbRsmMgrOptionDestroy(DbRsmMgrOptionT *option);
void DbRsmMgrOptionSetKeyMin(DbRsmMgrOptionT *option, uint32_t key);
void DbRsmMgrOptionSetMaxNumBlocks(DbRsmMgrOptionT *option, uint32_t numBlocks);
void DbRsmMgrOptionSetMaxBlockSizeAndPageSize(DbRsmMgrOptionT *option, uint32_t maxBlockSize, uint32_t pageSize);
uint32_t DbRsmMgrOptionGetPageSizeInKB(DbRsmMgrOptionT *option);
void DbRsmMgrOptionSetCallbacks(DbRsmMgrOptionT *option, DbRsmCallbacksT *callbacks);
void DbRsmMgrOptionSetIpcPerm(DbRsmMgrOptionT *option, DbRsmIpcPermT *perm);
void DbRsmMgrOptionSetExtendBlockInfo(DbRsmMgrOptionT *option, uint32_t extendBlockSize, uint32_t extendBlockStartIdx);
void DbRsmMgrOptionSetBlockSizeArray(DbRsmMgrOptionT *option, uint32_t totalBlockNum, uint32_t extendBlockSize,
    uint32_t blockMaxSize, uint32_t deviceSize);
uint32_t *DbRsmMgrOptionGetBlockSizeArray(DbRsmMgrOptionT *option);

// rsm mgr
typedef struct DbRsmMgrT DbRsmMgrT;

DbRsmMgrT *DbRsmMgrCreate(DbRsmMgrOptionT *option);
DbRsmMgrT *DbRsmMgrAttach(DbRsmMgrOptionT *option);
void DbRsmMgrDetach(void);
void DbRsmMgrDestroy(DbRsmMgrT *mgr);
void DbRsmExtendBlockDetachForCltUninit(void);

uint32_t DbRsmMgrGetMinKey(void);
uint32_t DbRsmMgrGetMaxKey(void);

#ifdef __cplusplus
}
#endif

#endif /* DB_RSMEM_IMPL_H */
