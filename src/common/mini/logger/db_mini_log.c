/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: db_mini_log.c
 * Description: implement of db mini log
 * Author:
 * Create: 2023-12-14
 */

#include "db_mini_log.h"
#include "db_log.h"
#include "db_error.h"
#include "db_log_utils.h"
#include "adpt_rdtsc.h"
#include "adpt_time.h"
#include "adpt_mini_define.h"
#include "db_last_error.h"

// 兼容非mini编译
DB_THREAD_LOCAL bool g_gmdbIsPrintLog = true;  // 按需建表场景，表不存在的时候，标识是否打印相关日志。
bool g_gmdbServerImportSourceFlag = false;  // 标识server启动过程中，权限是否导入完毕
uint32_t g_gmdbTraceLogMask = 0;

bool DbYangTraceLogEnable(uint32_t mask)
{
    return false;
}

/*
 * description: Log Resource Initialization
 * return {type} Success, STATUS_OK. Fail, error code.
 */
Status DbLogModuleInit(void)
{
    Status ret = DbAdptLogInit();
    if (ret != GMERR_OK) {
        return ret;
    }
    DbAdptLogApiT logApis = {DbLogWrite, DbLogWriteNoFmt};
    (void)DbAdptLogRegLogApi(&logApis);
    return GMERR_OK;
}

void DbLogModuleUnInit(void)
{
    DbAdptLogUnInit();
}

static void DbLogInitLogContent(DbLogInfoT *info, const DbLogHeaderT *header, const char *logDesc)
{
    DB_POINTER3(info, header, logDesc);
    info->header = *header;
    info->logDesc = logDesc;
}

/*
 * description: Log writing interface of the common and higher-level modules
 */
void DbLogWrite(bool needFold, DbLogTypeE type, const char *module, uint32_t level, int32_t errCode,
    const char *fileName, uint32_t lineNum, const char *funcName, const char *formatStr, ...)
{
    // Obtains the variable parameter list.
    va_list args;
    va_start(args, formatStr);
    DbLogWriteWithArgs(needFold, type, module, level, errCode, fileName, lineNum, funcName, formatStr, &args, true);
    va_end(args);
}

// Construct a common log：run、operate、alarm、debug
Status DbLogMakeLog(LogTextStrT *logText, const DbLogInfoT *info, va_list *args, int32_t errCode, bool isFormat)
{
    DB_POINTER4(logText, logText->buf, info, args);
    Status ret;

#ifndef HARMONY_OS
    // timestamp，fixed length
    if (DbIsEulerEnv()) {
        ret = DbLogAddTimestamp(logText, DbRdtsc());
        if (ret != GMERR_OK) {
            return ret;
        }
    }
#endif
    // timestamp + header，header length is fixed.
    ret = DbLogAddHeader(logText, &info->header, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (isFormat) {
        const DbErrDetailT *errCodeDetail = DbErrGetDetail(errCode);
        if (errCodeDetail != NULL) {
            ret = DbLogTextPrintf(logText, "%s ", errCodeDetail->brief);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    // timestamp + header + logDesc，logDesc length is't fixed，"\n" needs to be added after the logBuf is constructed.
    ret = DbLogTextVprintf(logText, info->logDesc, args);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbLogTextFinishPrint(logText);
    return GMERR_OK;
}

/*
 * description: Unified log writing interface for the variable parameter list args transferred by the upper layer
 */
void DbLogWriteWithArgs(bool needFold, DbLogTypeE type, const char *module, uint32_t level, int32_t errCode,
    const char *fileName, uint32_t lineNum, const char *funcName, const char *formatStr, va_list *args, bool isFormat)
{
    DB_POINTER2(module, formatStr);
    // Log Content Initialization
    DbLogHeaderT header = {.type = type,
        .module = module,
        .level = level,
        .pid = DbAdptGetpid(),
        .tid = DbThreadGetTid(),
        .errCode = errCode};
    DbLogInfoT logInfo;
    DbLogInitLogContent(&logInfo, &header, formatStr);
    // Construct logs
    char logBuf[(uint32_t)DB_DEFAULT_LOG_SIZE + 1] = {0};
    LogTextStrT logText = {logBuf, DB_DEFAULT_LOG_SIZE + 1, 0};
    Status ret = DbLogMakeLog(&logText, &logInfo, args, errCode, isFormat);
    if (ret != GMERR_OK) {
        return;
    }

    DbLogWriteArgT writeArg = {.errCode = errCode, .type = (int32_t)type, .level = level};
    DbLogAdptWrite(&writeArg, &logText);
}

void DbLogWriteNoFmt(bool needFold, DbLogTypeE type, const char *module, uint32_t level, int32_t errCode,
    const char *fileName, uint32_t lineNum, const char *funcName, ...)
{
    const char *formatStr = DbErrGetFmtStr((Status)errCode);
    if (formatStr == NULL) {
        return;
    }
    // Obtain the variable parameter list.
    va_list args;
    va_start(args, funcName);
    DbWriteBootLogWithArgs(errCode, level, fileName, lineNum, formatStr, &args, false);
    va_end(args);
    return;
}

void DbAuditWrite(
    const char *userName, const char *resource, DbAuditEvtTypeE evtType, int32_t evtResult, const char *evtDesc, ...)
{
    return;
}

void DbLogWriteAndSetLastError(bool needFold, DbLogTypeE type, const char *module, uint32_t level, int32_t errCode,
    const char *fileName, uint32_t lineNum, const char *funcName, const char *formatStr, ...)
{
    va_list ap;
    va_start(ap, formatStr);
    char logInfo[LOG_MAX_SIZE_OF_LOG_MSG] = {0};
    int32_t err = vsnprintf_s(logInfo, LOG_MAX_SIZE_OF_LOG_MSG, LOG_MAX_SIZE_OF_LOG_MSG - 1, formatStr, ap);
    if (err < 0) {
        logInfo[LOG_MAX_SIZE_OF_LOG_MSG - 1] = '\0';
    }
    va_end(ap);
    DbLogWrite(needFold, type, module, level, errCode, fileName, lineNum, funcName, "%s", logInfo);
    DbWriteLastErrorCustom(errCode, "%s", logInfo);
}
