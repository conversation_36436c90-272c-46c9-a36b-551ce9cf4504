/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: db_instance.c
 * Description: implementation file of db instance management
 * Author: wenming
 * Create: 2023-12-21
 */

#include "adpt_string.h"
#include "db_instance.h"
#include "db_mini_common_init.h"
#include "db_json.h"
#include "adpt_string.h"
#include "table_lock.h"

#define SYSTEM_TABLE_VERSION_SIZE sizeof(uint32_t)
#define TRX_PAGE_COUNT_SIZE sizeof(uint32_t)
#define DB_INVALID_PROCESS_INDEX (-1)
#define DB_RECOVER_STATUS_SIZE sizeof(uint32_t)

#define RECOVER_LOCK_OFFSET (MAX_PROCESS_COUNT + 1)

static DbInstanceMgrT g_dbInstanceMgr = {0};
static DbSpinLockT g_instanceInitLock = DB_SPINLOCK_INIT_VAL;

typedef bool (*InstanceElementCompFunc)(DbInstanceT *node, void *key);

static inline bool InstanceComparePath(DbInstanceT *node, void *path)
{
    const char *dataFileDirPath = DbGetDataFileDirRealPath(node->cfgMgr);
    if (dataFileDirPath == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Obtain data file path from existed instance %u.", node->instanceId);
        DB_ASSERT(false);
        return false;
    }
    return DbStrCmp(dataFileDirPath, path, true) == 0;
}

static inline bool InstanceCompareId(DbInstanceT *node, void *instanceId)
{
    return node->instanceId == *(uint32_t *)instanceId;
}

static DbInstanceT *FindInstanceByCompare(void *key, InstanceElementCompFunc func)
{
    if (DbLinkedListEmpty(&g_dbInstanceMgr.instanceList)) {
        return NULL;
    }
    DbInstanceT *node = NULL;
    DbInstanceT *tmpNode = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(node, tmpNode, &g_dbInstanceMgr.instanceList, linkedNode)
    {
        bool isEqual = func(node, key);
        if (isEqual) {
            return node;
        }
    }
    return NULL;
}

static DbInstanceT *GetInstanceByConfigJson(const char *cfgParameter)
{
    DbJsonT *config = DbLoadJsonSrv(cfgParameter, 0);
    if (config == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_JSON_CONTENT, "Inv db config json.");
        return NULL;
    }
    void *jsonValue = DbJsonObjectGet(config, "dataFilePath");
    if (jsonValue == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_JSON_CONTENT, "Get Datapath from db config.");
        return NULL;
    }
    if (DbJsonGetType(jsonValue) != DB_JSON_STRING) {
        DB_LOG_ERROR(GMERR_DATATYPE_MISMATCH, "cfgParm type not str");
        return NULL;
    }
    const char *tmpPath = DbJsonStringValue(jsonValue);
    if (tmpPath == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Fetch path from cfgJson.");
        return NULL;
    }
    char realPath[PATH_MAX];
    // get the data file real path, the dir path must already exist.
    Status ret = DbGetRealPath(tmpPath, realPath, PATH_MAX);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "dataFilePath in cfgJson inv.");
        return NULL;
    }
    DbInstanceT *dbIns = FindInstanceByCompare(realPath, InstanceComparePath);
    DbJsonDelete(config);
    return dbIns;
}

static inline bool InstanceCompareMultiInsTag(DbInstanceT *node, void *multiInsTag)
{
    return node->multiInsTag == *(uint32_t *)multiInsTag;
}

static inline DbInstanceT *GetInstanceByMultiInsTag(uint32_t multiInsTag)
{
    return FindInstanceByCompare((void *)&multiInsTag, InstanceCompareMultiInsTag);
}

static inline DbInstanceT *GetInstanceById(uint32_t instanceId)
{
    return FindInstanceByCompare((void *)&instanceId, InstanceCompareId);
}

Status DbInstanceMgrInit(DbCommonInitCfgT *initCfg)
{
    DB_UNUSED(initCfg);
    DbSetServerThreadFlag();
    static bool isInited = false;
    if (isInited) {
        return GMERR_OK;
    }
    DbSpinLock(&g_instanceInitLock);
    if (isInited) {
        DbSpinUnlock(&g_instanceInitLock);
        return GMERR_OK;
    }
    Status ret = DbCommonGlobalInit(NULL);
    if (ret != GMERR_OK) {
        DbSpinUnlock(&g_instanceInitLock);
        return ret;
    }
    g_dbInstanceMgr.instanceId = 1;
    DbRWLatchInit(&g_dbInstanceMgr.latch);
    // Initiate linked list
    DbLinkedListInit(&g_dbInstanceMgr.instanceList);
    isInited = true;
    DbSpinUnlock(&g_instanceInitLock);
    return GMERR_OK;
}

bool IsOpenInSharedMode(const DbCfgMgrT *cfgMgr)
{
    DB_POINTER(cfgMgr);
    const DbCfgItemT *cfgItem = &cfgMgr->cfgItems[DB_CFG_SHARED_MODE_ENABLE];
    return (cfgItem->int32Val == 1);
}

static void DbInitShareModeInfo(uint32_t dbInstanceId, int flags, DbInstanceT *dbIns)
{
    DB_POINTER(dbIns);
    dbIns->instanceId = dbInstanceId;
    dbIns->sharedModeInfo.fileMapMgr = NULL;
    dbIns->sharedModeInfo.fileMapFd = DB_INVALID_FD;
    dbIns->sharedModeInfo.processIndex = DB_INVALID_PROCESS_INDEX;
    dbIns->sharedModeInfo.isFirstBoot = true;
    dbIns->sharedModeInfo.sharedReadOnly = IsReadOnlyInSharedMode(flags);
}

Status DbAllocInstance(const char *cfgParameter, int flags, DbInstanceT **instance)
{
    DbRWLatchW(&g_dbInstanceMgr.latch);
    DbInstanceT *dbIns = GetInstanceByConfigJson(cfgParameter);
    if (dbIns != NULL) {
        dbIns->refCnt++;
        *instance = dbIns;
        DbRWUnlatchW(&g_dbInstanceMgr.latch);
        return GMERR_OK;
    }
    dbIns = (DbInstanceT *)DB_MALLOC(sizeof(DbInstanceT));
    (void)memset_s(dbIns, sizeof(DbInstanceT), 0, sizeof(DbInstanceT));
    DbRWLatchInit(&dbIns->latch);
    dbIns->status = DB_INSTANCE_OFFLINE;
    dbIns->refCnt = 1;
    DbInitShareModeInfo(g_dbInstanceMgr.instanceId++, flags, dbIns);

    Status ret = DbCfgCreateCfgMgr(cfgParameter, (DbCfgMgrT **)&dbIns->cfgMgr);
    if (ret != GMERR_OK) {
        DB_BOOT_LOG_ERROR(ret, "Configure creates unsucc. file is %s.", cfgParameter);
        DB_FREE(dbIns);
        DbRWUnlatchW(&g_dbInstanceMgr.latch);
        return ret;
    }
    ret = DbCfgSetInstanceId(dbIns->cfgMgr, dbIns->instanceId);
    if (ret != GMERR_OK) {
        DB_BOOT_LOG_ERROR(ret, "instanceId sets unsucc. file is %s.", cfgParameter);
        DbCfgFreeCfgMgr((DbCfgMgrT **)&dbIns->cfgMgr);
        DB_FREE(dbIns);
        DbRWUnlatchW(&g_dbInstanceMgr.latch);
        return ret;
    }

    DbLinkedListAppend(&g_dbInstanceMgr.instanceList, &dbIns->linkedNode);
    *instance = dbIns;
    DbRWUnlatchW(&g_dbInstanceMgr.latch);
    return GMERR_OK;
}

void DbReleaseInstance(DbInstanceT *instance)
{
    DbRWLatchW(&g_dbInstanceMgr.latch);
    DB_ASSERT(instance->refCnt > 0);
    instance->refCnt = instance->refCnt - 1;
    if (instance->refCnt == 0 && instance->status == DB_INSTANCE_OFFLINE) {
        DbLinkedListRemove(&instance->linkedNode);
        DbCfgFreeCfgMgr((DbCfgMgrT **)&instance->cfgMgr);
        DB_FREE(instance);
        if (DbLinkedListEmpty(&g_dbInstanceMgr.instanceList)) {
            g_dbInstanceMgr.instanceId = 1;
        }
    }
    DbRWUnlatchW(&g_dbInstanceMgr.latch);
}

Status DbGetInstanceById(uint32_t instanceId, DbInstanceT **instance)
{
    DbRWLatchW(&g_dbInstanceMgr.latch);
    DbInstanceT *dbIns = GetInstanceById(instanceId);
    if (dbIns == NULL) {
        DB_LOG_ERROR(GMERR_NO_DATA, "Get Instance by id (%u).", instanceId);
        DbRWUnlatchW(&g_dbInstanceMgr.latch);
        return GMERR_NO_DATA;
    }
    dbIns->refCnt = dbIns->refCnt + 1;
    DbRWUnlatchW(&g_dbInstanceMgr.latch);
    *instance = dbIns;
    return GMERR_OK;
}

Status DbGetInstanceByMultiInsTag(uint32_t multiInsTag, DbInstanceT **instance)
{
    DbRWLatchR(&g_dbInstanceMgr.latch);
    DbInstanceT *dbIns = GetInstanceByMultiInsTag(multiInsTag);
    DbRWUnlatchR(&g_dbInstanceMgr.latch);

    if (SECUREC_UNLIKELY(dbIns == NULL)) {
        DB_LOG_ERROR(GMERR_NO_DATA, "Get Instance by multi-instance tag share: %u.", multiInsTag);
        return GMERR_NO_DATA;
    }
    *instance = dbIns;
    return GMERR_OK;
}

Status DbGetInstanceByIdShare(uint32_t instanceId, DbInstanceT **instance)
{
    DbRWLatchR(&g_dbInstanceMgr.latch);
    DbInstanceT *dbIns = GetInstanceById(instanceId);
    DbRWUnlatchR(&g_dbInstanceMgr.latch);

    if (SECUREC_UNLIKELY(dbIns == NULL)) {
        DB_LOG_ERROR(GMERR_NO_DATA, "Get Instance by id share (%u).", instanceId);
        return GMERR_NO_DATA;
    }
    *instance = dbIns;
    return GMERR_OK;
}

Status DbGetInstanceByConfigJson(const char *cfgParameter, DbInstanceT **instance)
{
    DbRWLatchW(&g_dbInstanceMgr.latch);
    DbInstanceT *dbIns = GetInstanceByConfigJson(cfgParameter);
    if (dbIns == NULL) {
        DB_LOG_WARN(GMERR_NO_DATA, "Get Instance by config Json.");
        DbRWUnlatchW(&g_dbInstanceMgr.latch);
        return GMERR_NO_DATA;
    }
    dbIns->refCnt = dbIns->refCnt + 1;
    DbRWUnlatchW(&g_dbInstanceMgr.latch);
    *instance = dbIns;
    return GMERR_OK;
}

DbMultiInstanceSwitchE DbGetMultiInstanceSwitch(void)
{
    return DB_MULTI_INSTANCE_SWITCH_OFF;
}

bool DbIsMultiInstanceEnabled(void)
{
    return false;
}

void DbSetMultiInstanceSwitch(DbMultiInstanceSwitchE status)
{
    DB_UNUSED(status);
}

DbInstanceT *DbGetGlobalInstance(void)
{
    return NULL;
}
