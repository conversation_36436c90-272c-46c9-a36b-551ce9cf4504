/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: db_mini_process_mgr.c
 * Description: process management
 * Author:
 * Create: 2024-1-5
 */

#include "db_mini_process_mgr.h"
#include "db_inter_process_mgr.h"
#include "db_log.h"
#include "gmc_errno.h"
#include "db_file.h"
#include "db_instance.h"

#define RECOVER_LOCK_OFFSET 9

static inline bool IsValidProcessIndex(int processIdx)
{
    return ((processIdx) >= 0 && (processIdx) < MAX_PROCESS_COUNT);
}

Status DbInitAndLockOpeningByte(
    const char *dbFilePath, bool isOpenOnly, ProcessInfoMgrT *processInfoMgr, void *topDynMemCtx)
{
    DB_POINTER(processInfoMgr);
    Status ret = FileMapFileOpen(dbFilePath, isOpenOnly, topDynMemCtx, &processInfoMgr->fileMapFd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "open map file(lock opening byte).");
        return ret;
    }

    ret = DbOpeningByteLock(processInfoMgr);
    if (ret != GMERR_OK) {
        DbCloseFile(processInfoMgr->fileMapFd);
        processInfoMgr->fileMapFd = 0;
        DB_LOG_ERROR(ret, "get write lock(lock opening byte).");
    }
    return ret;
}

Status DbOpeningByteLock(ProcessInfoMgrT *processInfoMgr)
{
    DB_POINTER(processInfoMgr);
    FileLockT fileLock = {0};
    FileLockInit(&fileLock, processInfoMgr->fileMapFd, OPENING_LOCK_OFFSET, LOCK_BYTE_LEN);
    Status ret = FileLockAddLock(&fileLock, DB_WRLOCK);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get file write lock.");
    }
    return ret;
}

void DbOpeningByteUnlock(ProcessInfoMgrT *processInfoMgr)
{
    DB_POINTER(processInfoMgr);
    if (processInfoMgr->fileMapFd <= 0) {
        return;
    }
    FileLockT fileLock = {0};
    FileLockInit(&fileLock, processInfoMgr->fileMapFd, OPENING_LOCK_OFFSET, LOCK_BYTE_LEN);
    (void)FileLockUnlock(&fileLock);
}

Status DbProcessByteTryLockByIndex(int fileMapFd, int index)
{
    FileLockT fileLock = {0};
    FileLockInit(&fileLock, fileMapFd, PROCESS_LOCK_OFFSET + index, LOCK_BYTE_LEN);
    return FileLockTryLock(&fileLock, DB_WRLOCK);
}

Status DbProcessByteTryLock(ProcessInfoMgrT *processInfoMgr)
{
    DB_POINTER(processInfoMgr);
    // try to get a lock from process[0] ~ process[7] that means this process takes place
    for (int i = 0; i < MAX_PROCESS_COUNT; i++) {
        Status ret = DbProcessByteTryLockByIndex(processInfoMgr->fileMapFd, i);
        if (ret == GMERR_OK) {
            processInfoMgr->processId = i;
            return GMERR_OK;
        } else if (ret != GMERR_FILE_LOCK_BUSY) {
            DB_LOG_ERROR(ret, "get wlock(read only process).");
            return ret;
        }
    }
    DB_LOG_ERROR(GMERR_PROCESS_LIMIT_EXCEEDED, "read only process limit exceeded.");
    return GMERR_PROCESS_LIMIT_EXCEEDED;
}

Status DbProcessByteUnlock(ProcessInfoMgrT *processInfoMgr)
{
    DB_ASSERT(processInfoMgr->processId < MAX_PROCESS_COUNT);
    FileLockT fileLock = {0};
    FileLockInit(&fileLock, processInfoMgr->fileMapFd, PROCESS_LOCK_OFFSET + processInfoMgr->processId, LOCK_BYTE_LEN);
    Status ret = FileLockUnlock(&fileLock);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unlock current process.");
    }
    return ret;
}

Status GetProcessExitedState(ProcessInfoMgrT *processInfoMgr, int processId, bool *isExited)
{
    FileLockT fileLock = {0};
    FileLockInit(&fileLock, processInfoMgr->fileMapFd, PROCESS_LOCK_OFFSET + processId, LOCK_BYTE_LEN);
    FileLockTypeE lockType;
    Status ret = FileLockGetLockType(&fileLock, &lockType);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get file lock type(get exit state).");
        return ret;
    }
    *isExited = (lockType == DB_UNLOCK);
    return GMERR_OK;
}

Status GetAllProcessExistState(ProcessInfoMgrT *processInfoMgr, uint32_t processCnt, bool *isExist)
{
    for (uint32_t i = 0; i < processCnt; i++) {
        if (i == (uint32_t)processInfoMgr->processId) {
            isExist[i] = true;
            continue;
        }
        bool exited = false;
        ProcessInfoMgrT processMgr = {.fileMapFd = processInfoMgr->fileMapFd, .processId = i};
        Status ret = GetProcessExitedState(&processMgr, i, &exited);
        if (ret != GMERR_OK) {
            return ret;
        }
        isExist[i] = !exited;
    }
    return GMERR_OK;
}

Status DbGetIsFirstBootProcess(ProcessInfoMgrT *processInfoMgr, bool *isFirstBoot)
{
    for (int32_t i = 0; i < MAX_PROCESS_COUNT; i++) {
        if (i == processInfoMgr->processId) {
            continue;  // no need to check self-process
        }
        bool isExited = false;
        ProcessInfoMgrT mgr = {.fileMapFd = processInfoMgr->fileMapFd, .processId = i};
        Status ret = GetProcessExitedState(&mgr, i, &isExited);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get process exit state(get isFirstBoot).");
            return ret;
        }
        if (!isExited) {
            *isFirstBoot = false;
            return GMERR_OK;
        }
    }
    *isFirstBoot = true;
    return GMERR_OK;
}

Status IsExistsProcessExitAbnormally(void *processInfoMgr)
{
    ProcessInfoMgrT *mgr = (ProcessInfoMgrT *)processInfoMgr;
    // other thread/process detect the abort
    if (ProcessGetRecoverStatus(mgr) == DB_NEED_RECOVER) {
        return GMERR_PROCESS_ABORT;
    }
    for (int i = 0; i < MAX_PROCESS_COUNT; i++) {
        // ProcessGetTxnsCount can not detect local process
        if (i == mgr->processId) {
            continue;
        }
        if (ProcessGetTxnsCount(mgr, i) > 0) {
            FileLockT fileLock = {0};
            FileLockInit(&fileLock, mgr->fileMapFd, PROCESS_LOCK_OFFSET + i, LOCK_BYTE_LEN);
            FileLockTypeE lockType;
            Status ret = FileLockGetLockType(&fileLock, &lockType);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "get file lock type(detecting).");
                return GMERR_PROCESS_ABORT;
            }
            if (lockType == DB_UNLOCK) {
                ProcessSetRecoverStatus(mgr, DB_NEED_RECOVER);
                return GMERR_PROCESS_ABORT;
            }
        }
    }
    return GMERR_OK;
}

bool IsTheOnlyOneProcess(const ProcessInfoMgrT *processInfoMgr)
{
    int processId = processInfoMgr->processId;
    for (int i = 0; i < MAX_PROCESS_COUNT; i++) {
        // ProcessGetTxnsCount can not detect local process
        if (i == processId) {
            continue;
        }
        bool exited = false;
        ProcessInfoMgrT processMgr = {.fileMapFd = processInfoMgr->fileMapFd, .processId = i};
        Status ret = GetProcessExitedState(&processMgr, i, &exited);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get process exit.");
            return false;
        }
        if (!exited) {
            return false;
        }
    }
    return true;
}

bool IsTheOnlyOneProcess4Instance(DbInstanceHdT dbInstance)
{
    return IsTheOnlyOneProcess(&((DbInstanceT *)dbInstance)->sharedModeInfo.processMgr);
}

void *DbGetProcessMgr(DbInstanceHdT dbInstance)
{
    return (void *)(&((DbInstanceT *)dbInstance)->sharedModeInfo.processMgr);
}

Status DbGetProcessIndex(void *dbIns, uint8_t *processIdx)
{
    DbInstanceT *dbInstance = (DbInstanceT *)dbIns;
    if (dbInstance == NULL || dbInstance->sharedModeInfo.fileMapMgr == NULL) {
        *processIdx = 0;
        return GMERR_OK;
    }
    if (!IsValidProcessIndex(dbInstance->sharedModeInfo.processIndex)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Process index %" PRId32 ".", dbInstance->sharedModeInfo.processIndex);
        return GMERR_INTERNAL_ERROR;
    }
    *processIdx = (uint8_t)dbInstance->sharedModeInfo.processIndex;
    return GMERR_OK;
}
