/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: db_mini_tuple_buffer.c
 * Description: tuple buffer
 * Author:
 * Create: 2023-11-13
 */

#include "db_memcpy.h"
#include "db_tuple_buffer.h"

#ifdef __cplusplus
extern "C" {
#endif

void TupleBufInit(TupleBufT *tupleBuf, DbMemCtxT *memCtx)
{
    DB_POINTER2(tupleBuf, memCtx);
    *tupleBuf = (TupleBufT){.len = 0, .cap = 0, .buf = NULL, .memCtx = memCtx};
}

void TupleBufRelease(TupleBufT *tupleBuf)
{
    DB_POINTER(tupleBuf);
    if (tupleBuf->buf != NULL) {
        DbDynMemCtxFree(tupleBuf->memCtx, tupleBuf->buf);
    }

    *tupleBuf = (TupleBufT){};
}

// 释放内存但是保留memCtx指针，配合MemCtxReset使用
void TupleBufReset(TupleBufT *tupleBuf)
{
    DB_POINTER(tupleBuf);
    if (tupleBuf->buf != NULL) {
        DbDynMemCtxFree(tupleBuf->memCtx, tupleBuf->buf);
    }

    *tupleBuf = (TupleBufT){.memCtx = tupleBuf->memCtx};
}

Status TupleBufReserve(TupleBufT *tupleBuf, uint32_t len)
{
    DB_POINTER2(tupleBuf, tupleBuf->memCtx);
    if (len > tupleBuf->cap) {
        DbDynMemCtxFree(tupleBuf->memCtx, tupleBuf->buf);  // 里面会判断空指针
        tupleBuf->cap = 0;
        tupleBuf->len = 0;
        const uint32_t alignSize = 32;
        const uint32_t cap = ((len + alignSize - 1) / alignSize) * alignSize;
        tupleBuf->buf = DbDynMemCtxAlloc(tupleBuf->memCtx, cap);
        if (tupleBuf->buf == NULL) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Expand tuple buffer, size=%" PRIu32 ".", len);
            return GMERR_OUT_OF_MEMORY;
        }
        tupleBuf->cap = cap;
    }

    tupleBuf->len = len;
    return GMERR_OK;
}

Status TupleBufPut(TupleBufT *tupleBuf, uint32_t len, const uint8_t *buf)
{
    DB_POINTER2(tupleBuf, buf);
    Status ret = TupleBufReserve(tupleBuf, len);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 性能敏感路径
    DbFastMemcpy(tupleBuf->buf, tupleBuf->cap, buf, len);
    tupleBuf->len = len;
    return GMERR_OK;
}

Status TupleBufCopy(TupleBufT *dst, TupleBufT *src)
{
    DB_POINTER2(dst, src);
    return TupleBufPut(dst, src->len, src->buf);
}

void TupleBufMove(TupleBufT *dst, TupleBufT *src)
{
    DB_POINTER2(dst, src);
    DB_ASSERT(dst->memCtx == src->memCtx);
    uint8_t *buf = dst->buf;
    uint32_t cap = dst->cap;

    dst->buf = src->buf;
    dst->cap = src->cap;
    dst->len = src->len;

    src->buf = buf;
    src->cap = cap;
    src->len = 0;
}

#ifdef __cplusplus
}
#endif
