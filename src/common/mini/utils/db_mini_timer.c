/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: db_mini_timer.c
 * Description: implement of mini timer
 * Author:
 * Create: 2023-12-15
 */

#include "db_timer.h"
#include "adpt_rdtsc.h"

Status DbTimerRegister(DbTimerT *timer, TimerHandleT *timerHandle)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status DbTimerUnregister(const TimerHandleT *timerHandle, TimerModeE mode)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

bool DbCheckTimerHandle(const TimerHandleT timer)
{
    return true;
}

bool DbExceedTime(uint64_t startTime, double timeThreshold)
{
    uint64_t endTime = DbClockGetTsc();
    if (endTime <= startTime) {
        return false;
    }
    double td = (double)DbToMseconds(endTime - startTime);
    return td >= timeThreshold;
}

uint64_t DbClockGetTsc(void)
{
    return DbRdtsc();
}
