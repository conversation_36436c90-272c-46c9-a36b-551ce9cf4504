/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: db_mini_config.c
 * Description: implement of db mini config
 * Author:
 * Create: 2023-12-14
 */

#include "db_config.h"
#include <securec.h>
#include "adpt_string.h"
#include "db_json.h"
#include "db_utils.h"
#include "db_instance.h"

#define PAGE_SIZE_4K 4
#define PAGE_SIZE_8K 8
#define PAGE_SIZE_16K 16
#define PAGE_SIZE_32K 32
#define PAGE_SIZE_64K 64
#define MAX_NAME_LEN 128
#define BUF_POOL_POLICY_NUM 3

typedef struct TagBufPolicyMap {
    const char *policyName;
    BufRecyTypeE policyNum;
} BufPolicyMapT;

// fusion
#define CONFIG_MAX_HOURS_NUM (24)
#define CONFIG_MAX_MINUTERS_NUM (60)
#define CONFIG_MAX_TRIGGER_TIME_LEN (5)

DbCfgMgrHandleT DbGetCfgHandle(DbInstanceHdT dbInstance)
{
    if (dbInstance == NULL) {  // LCOV_EXCL_BR_LINE
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "dbInstance is null");
        return NULL;
    }
    DbInstanceT *dbIns = (DbInstanceT *)dbInstance;
    return dbIns->cfgMgr;
}

Status DbCfgRegisterNofityFunc(DbCfgEmItemIdE id, DbCfgChangeNotifyFuncT func, DbInstanceHdT dbInstance)
{
    return GMERR_OK;
}
// end fusion

/* ****************************************************************************
Description  : Validates a value for a CI whose data type is int with a value
                range.
Input        : cfgItemDesc: the description of CI.
Input        : value: the value to validates.
Output       : None
Return Value : return GMERR_OK when success.
**************************************************************************** */
Status CfgParamValidateIrange(const DbCfgItemDescT *cfgItemDesc, const void *value)
{
    DB_POINTER2(cfgItemDesc, value);
    if (*(const int64_t *)value < cfgItemDesc->min || *(const int64_t *)value > cfgItemDesc->max) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "(%s) int32 value not in proper range(%" PRId64 ", %" PRId64 ")",
            cfgItemDesc->name, cfgItemDesc->min, cfgItemDesc->max);
        return GMERR_CONFIG_ERROR;
    }
    return GMERR_OK;
}

Status CfgParamValidateLogPath(const DbCfgItemDescT *cfgItemDesc, const void *value)
{
    return GMERR_OK;
}

Status CfgParamValidateShmemPermission(const DbCfgItemDescT *cfgItemDesc, const void *value)
{
    return GMERR_OK;
}

Status CfgParamValidateWorkerHungThreshold(const DbCfgItemDescT *cfgItemDesc, const void *value)
{
    return GMERR_OK;
}

void DbLogUpdateFoldCfg(const char *configName, const DbCfgValueT *oldValue, DbCfgValueT *newValue)
{
    return;
}

Status CfgParamValidateTrxMonitorThreshold(const DbCfgItemDescT *cfgItemDesc, const void *value)
{
    return GMERR_OK;
}

Status CfgParamFlowCtrlSleepTime(const DbCfgItemDescT *cfgItemDesc, const void *value)
{
    return GMERR_OK;
}

Status CfgParamValidateDefaultValue(const DbCfgItemDescT *cfgItemDesc, const void *value)
{
    DB_POINTER2(cfgItemDesc, value);
    if (cfgItemDesc->min != cfgItemDesc->max || *(const int64_t *)value != cfgItemDesc->max) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "(%s) int32 value not in proper range(%" PRId64 ", %" PRId64 ")",
            cfgItemDesc->name, cfgItemDesc->min, cfgItemDesc->max);
        return GMERR_CONFIG_ERROR;
    }
    return GMERR_OK;
}

Status CfgParamValidateIrangePowerOfTwo(const DbCfgItemDescT *cfgItemDesc, const void *value)
{
    DB_POINTER2(cfgItemDesc, value);
    Status ret = CfgParamValidateIrange(cfgItemDesc, value);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t val = *(const uint32_t *)value;
    if ((val & (val - 1)) != 0x00) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "(%s) int32 value not the power of 2", cfgItemDesc->name);
        return GMERR_CONFIG_ERROR;
    }
    return GMERR_OK;
}

Status CfgParamValidatePageSize(const DbCfgItemDescT *cfgItemDesc, const void *value)
{
    DB_POINTER2(cfgItemDesc, value);
    switch (*(const int32_t *)value) {
        case PAGE_SIZE_4K:
        case PAGE_SIZE_8K:
        case PAGE_SIZE_16K:
        case PAGE_SIZE_32K:
        case PAGE_SIZE_64K:
            return GMERR_OK;
        default:
            break;
    }
    DB_LOG_ERROR(GMERR_CONFIG_ERROR, "(%s) value not in proper range", cfgItemDesc->name);
    return GMERR_CONFIG_ERROR;
}

Status CfgParamValidatePath(const DbCfgItemDescT *cfgItemDesc, const void *value)
{
    DB_POINTER2(cfgItemDesc, value);
    const char *filePath = (const void *)value;
    if (strlen(filePath) >= PATH_MAX) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "dataFilePath len exceed limit");
        return GMERR_CONFIG_ERROR;
    }
    // filePath should not end with '/'
    if (strlen(filePath) > 0 && filePath[strlen(filePath) - 1] == '/') {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "dataFilePath can't end with forward slash ");
        return GMERR_CONFIG_ERROR;
    }

    return GMERR_OK;
}

Status CfgParamValidateLockTableBucketNum(const DbCfgItemDescT *cfgItemDesc, const void *value)
{
    DB_POINTER2(cfgItemDesc, value);
    int32_t numValue = *(const int32_t *)value;
    if (numValue != cfgItemDesc->min && numValue != cfgItemDesc->max) {
        DB_LOG_AND_SET_LASERR(GMERR_CONFIG_ERROR,
            "Config lock table bucket num int32 value is not right, value=%" PRIi32 ", value should be %" PRIi64
            " or %" PRIi64 ".",
            numValue, cfgItemDesc->min, cfgItemDesc->max);
        return GMERR_CONFIG_ERROR;
    }
    return GMERR_OK;
}

Status CfgParamValidateIrangeIncluding0(const DbCfgItemDescT *cfgItemDesc, const void *value)
{
    DB_POINTER2(cfgItemDesc, value);
    if (*(const int32_t *)value == 0) {
        return GMERR_OK;
    }
    return CfgParamValidateIrange(cfgItemDesc, value);
}

Status CfgParamValidateTriggerTime(const DbCfgItemDescT *cfgItemDesc, const void *value)
{
    const char *strValue = (const char *)value;
    // 按照规定格式和长度校验（hh:mm），允许12:5这种配置（和12:05等价）
    uint32_t stringLength = (uint32_t)strlen(strValue);
    if (stringLength > CONFIG_MAX_TRIGGER_TIME_LEN) {
        DB_LOG_WARN(GMERR_CONFIG_ERROR, "str len should less than (%d)", CONFIG_MAX_TRIGGER_TIME_LEN);
        return GMERR_CONFIG_ERROR;
    }
    for (uint32_t i = 0; i < stringLength; ++i) {
        bool isVaild = ((strValue[i] >= '0' && strValue[i] <= '9') || strValue[i] == ':');
        if (!isVaild) {
            DB_LOG_AND_SET_LASERR(GMERR_CONFIG_ERROR, "Inv char in trigger time config");
            return GMERR_CONFIG_ERROR;
        }
    }

    int32_t hours = 0;
    int32_t minuters = 0;
    const int32_t expectedArgCnt = 2;
    int32_t validCnt = sscanf_s(strValue, "%" PRId32 ":%" PRId32 "", &hours, &minuters);
    if (validCnt != expectedArgCnt) {
        DB_LOG_WARN(GMERR_CONFIG_ERROR, "parse config str blunder cnt:%d, %d(expected)", validCnt, expectedArgCnt);
        return GMERR_CONFIG_ERROR;
    }

    // 检验时分范围是否越界
    if (hours >= CONFIG_MAX_HOURS_NUM || minuters >= CONFIG_MAX_MINUTERS_NUM) {
        DB_LOG_WARN(GMERR_CONFIG_ERROR,
            "Inv config params, hour:%d(should less than %d). minuter:%d(shold less than %d)", hours,
            CONFIG_MAX_HOURS_NUM, minuters, CONFIG_MAX_MINUTERS_NUM);
        return GMERR_CONFIG_ERROR;
    }

    return GMERR_OK;
}

#define DB_CFG_SE_DEV_SIZE_DESC "size of device, unit: K"

#define DB_CFG_SE_EXTEND_SIZE_DESC "size of extend, unit: K"

#define DB_CFG_SE_PAGE_SIZE_DESC "size of page, unit: K"

#define DB_CFG_SE_CTRL_PAGE_SIZE_DESC "size of ctrl page, unit: K"

#define DB_CFG_SE_MAX_MEM_DESC "total size of memory, unit: M"

#define DB_CFG_SE_BUFFERPOOL_SIZE_DESC "size of bufferpool, unit: K"
#define DB_CFG_SE_BUFFERPOOL_LOB_EXPAND_BUF_SIZE_DESC "max size of expand buf for large object in buffer pool, unit: K"

#define DB_CFG_CONN_MAX_DESC "Maximum number of connections supported by the server, default 1024, range:[16, 1024]."
#define DB_CFG_IS_CLT_STATS_ENABLE_DESC \
    "If true, server enable to receive statistics from client, otherwise, server did not receive."

// redo config desc
#define DB_CFG_REDO_PUB_BUF_SIZE_DESC "redo public buffer size, unit is KB."
#define DB_CFG_REDO_FLUSH_BY_TRX_DESC "redo log will be flushing while transaction committed."
#define DB_CFG_REDO_FILE_SIZE_DESC "redo log file size, unit is MB."
#define DB_CFG_LOB_REDO_FILE_SIZE_DESC "lob redo log file size, unit is MB. [96, 1024]"
// buffer pool desc
#define DB_CFG_SE_BUFFERPOOL_POLICY_DESC "policy of buff pool which define the priority of different pages to in or out"
#define DB_CFG_SE_BUFFERPOOL_NUM_DESC "number of bufferpool instances"
// enable pre fetch desc
#define DB_CFG_PRE_FETCH_PAGES_ENABLE_DESC "if true, pre fetch pages will be enabled"
#define DB_CFG_MAX_PRE_FETCH_THRE_NUM_DESC "maximum number of bg threads for pre fetch pages"
// buffer pool high priority list ratio desc
#define DB_CFG_SE_BUFFERPOOL_HIGH_PRIORITY_RATIO_DESC "ratio of bufferpool high priority list."
// buffer pool priority list ratio desc
#define DB_CFG_SE_BUFFERPOOL_PRIORITY_RATIO_DESC "sum ratio of bufferpool high priority list and priority list."
// space config desc
#define DB_CFG_DATA_FILE_DIR_PATH_DESC "db data file path"
#define DB_CFG_CRC_CHECK_ENABLE_DESC "if true, page crc check will be enabled"
#define DB_CFG_DOUBLE_WRITE_ENABLE_DESC "if true, double write will be enabled"
#define DB_CFG_DOUBLE_WRITE_BLOCK_NUM_DESC "double write block(page) num each"
#define DB_CFG_SHARED_MODE_ENABLE_DESC "if true, shared mode will be enabled, multi process will be supported"

// fusion
#define DB_CFG_MESSAGE_SECURITY_CHECK_DESC \
    "If true, server will check whether the messages and buffers are legal, default 1(abled)."

#define DB_CFG_LONG_PROC_TIME_THRESHOLD_DESC "Max process time for long process, range from -1 to 500, unit: ms."

#define DB_CFG_LOG_LENGTH_MAX_DESC "The maximum length of one log, unit: Byte."

#define DB_CFG_LOCK_TABLE_BUCKET_NUM_DESC "num of lock table bucket (must be 509 or 1021)."

#define DB_CFG_SE_GET_INSTANCE_ID_DESC "current storage engine of id"

#define DB_CFG_MEM_COMPACT_ENABLE_DESC "back ground memory compact enable flag."

#define DB_CFG_MAX_UNDO_SPACE_SIZE_DESC "maximum memory size of undo space, unit: MB"
#define DB_CFG_LOG_FOLD_RULE_DESC "log fold rule"

#define DB_CFG_TRX_LOCK_WAKEUP_PERIOD_DESC "period of trx lock wakeup, unit: ms"
#define DB_CFG_TRX_LOCK_DEADLOCK_CHECK_PERIOD_DESC "period of trx lock deadlock checkout, unit: ms"
#define DB_CFG_LATCH_DEADLOCK_DEBUG_TIMEOUT_DESC "timeout of latch deadlock(used when GMDB_LATCH_DEBUG is on, unit: us"
#define DB_CFG_TRX_LOCK_JUMP_QUEUE_PERIOD_DESC "period of trx lock jump-queue, unit: ms"
#define DB_CFG_TRX_LOCK_TIME_OUT_DESC "timeout of a trx lock waiting for Acquisition, unit: ms"
#define DB_CFG_TRX_MONITOR_ENABLE_DESC "enable transaction monitor or not"
#define DB_CFG_COMPATIBLE_V3_DESC "compatible V3"

#define DB_CFG_FEATURE_LIB_PATH_DESC "The so lib path for dynamic load features."

#define DB_CFG_PERSISTENT_MODE_DESC "persistent mode, on demand or increment."
// redo config desc
#define DB_CFG_DEV_BLOCK_SIZE_DESC "block size of disk device, must be pow of 2."
#define DB_CFG_IS_FLUSH_ON_DEMAND_DESC "Is running in Flush On Demand Mode."
#define DB_CFG_REDO_PUB_BUF_SIZE_DESC "redo public buffer size, unit is KB."
#define DB_CFG_REDO_BUF_PARTS_DESC "redo public buffer part count."
#define DB_CFG_REDO_FLUSH_BY_SIZE_DESC \
    "redo log will be flushing while buffer size greater than threadhold, unit is KB.\"0\" meats disable."
#define DB_CFG_REDO_FLUSH_BY_TIME_DESC \
    "redo log will be flushing while  time interval greater than threadhold, unit is ms.\"0\" meats disable."
#define DB_CFG_REDO_FLUSH_CHECK_PERIOD_DESC "redo flush check period"
#define DB_CFG_REDO_DIR_DESC "redo log file directory."
#define DB_CFG_REDO_FILE_SIZE_DESC "redo log file size, unit is MB."
#define DB_CFG_REDO_FILE_COUNT_DESC "redo log file count."
#define DB_CFG_REDO_FILE_DROP_ON_CLOSE_DESC "whether drop redo log file when normal shut down or not"
// space config desc
#define DB_CFG_DB_FILES_MAX_COUNT_DESC "space data file max count"
#define DB_CFG_CTRL_FILE_DIR_PATH_DESC "db ctrl file dir path"
#define DB_CFG_SPACE_MAX_NUM_DESC "space max num"
#define DB_CFG_FILE_SYNC_WRITE_ENABLE_DESC "file will be open with O_SYNC flag when it is true"
#define DB_CFG_DB_FILE_SIZE_DESC "db space file max size, unit:KB"
#define DB_CFG_SPACE_BLOCK_NUMS_PER_EXTENT_DESC "the number of blocks per extent, must be pow of 2."
// oplog desc
#define DB_CFG_OPLOG_FILE_SIZE_DESC "the max size of oplog file, uint is kb"
// chech point
#define DB_CFG_CKPT_PERIOD_DESC "The period of checkpoint, unit: second."
#define DB_CFG_CKPT_THLD_DESC "If queue greater than threshold checkpoint will be triggered."
#define DB_CFG_CKPT_TRIGGER_TIME_DESC "DB will flush data (checkpoint trigger) by this time every day. Format: hh:mm."
// 多区持久化
#define DB_CFG_MULTIZONE_NUM_DESC "enable/disable persistent file backup."
#define DB_CFG_RECOVERY_ZONE_ID_DESC "recovery by zone id"
#define DB_CFG_PERSISTENT_COMPRESS_MODE_DESC "persistent compress mode, no compression, on page or device."

#define DB_CFG_DIRECT_WRITER_DESC "Configuration options of the direct write switch."

#define DB_CFG_CONDENSED_CTRL_PAGES_DESC "If the condensed ctrl pages is enabled or not"
#define DB_CFG_ENABLE_RELEASE_DEVICE_DESC "Enable device returned to OS."
#define DB_CFG_IS_USE_RSM_DESC "Configuration options of the reserved memory."
#define DB_CFG_RSM_BLOCK_MAX_SIZE_DESC "max size of each reserved memory block."
#define DB_CFG_RSM_KEY_RANGE_DESC "List of the reserved memory."
// enf fusion

#define DB_CFG_LPM4_VRID_MAX_DESC \
    "user can config vrid max value of ipv4 lpm(Longest Prefix Match), default 16,upper limit is 4096."
#define DB_CFG_LPM4_VRFID_MAX_DESC \
    "user can config vrfid max value of ipv4 lpm(Longest Prefix Match), default 1024,upper limit is 16384."
#define DB_CFG_LPM6_VRID_MAX_DESC \
    "user can config vrid max value of ipv6 lpm(Longest Prefix Match), default 16,upper limit is 4096."
#define DB_CFG_LPM6_VRFID_MAX_DESC \
    "user can config vrfid max value of ipv6 lpm(Longest Prefix Match), default 1024,upper limit is 16384."

#define DB_CFG_DEFAULT_HASH_TYPE_DESC                                                                            \
    "default hash type(cceh | chained) for tables' primary key and unique localhash key, which bucket num less " \
    "than 1024."

#define DB_CFG_AUDIT_LOG_DDL_ENABLE_DESC "Audit log DDL enable flag."

#define DB_CFG_DBA_DESC "DBA Information."

#define DB_CFG_LONG_PROC_TIME_THRESHOLD_DESC "Max process time for long process, range from -1 to 500, unit: ms."

#define DB_CFG_LOCAL_LOCATOR_DESC "The local locator listened in the unix socket mode."

#define DB_CFG_DEFAULT_TABLE_SPACE_MAX_SIZE_DESC "default table space maxsize, unit: MB"

#define DB_CFG_SE_GET_INSTANCE_ID_DESC "current storage engine of id"

#define DB_CFG_PLAN_CACHE_SIZE_DESC "total size of plan cache, unit: MB"

#define DB_CFG_FLOW_CONTROL_DESC "is flow contorl in RPC mode (client ---> main store)"

#define DB_CFG_OVER_LOAD_THRESHOLD_DESC                                                                             \
    "the overload threshold and recover threshold for cpu usage, memory usage, pipeline buff usage,subscrib queue " \
    "usage"

#define DB_CFG_MEM_COMPACT_ENABLE_DESC "back ground memory compact enable flag."

#define DB_CFG_MAX_UNDO_SPACE_SIZE_DESC "maximum memory size of undo space, unit: MB"

#define DB_CFG_TRX_LOCK_WAKEUP_PERIOD_DESC "period of trx lock wakeup, unit: ms"
#define DB_CFG_TRX_LOCK_DEADLOCK_CHECK_PERIOD_DESC "period of trx lock deadlock checkout, unit: ms"
#define DB_CFG_LATCH_DEADLOCK_DEBUG_TIMEOUT_DESC "timeout of latch deadlock(used when GMDB_LATCH_DEBUG is on, unit: us"
#define DB_CFG_TRX_LOCK_JUMP_QUEUE_PERIOD_DESC "period of trx lock jump-queue, unit: ms"
#define DB_CFG_TRX_LOCK_TIME_OUT_DESC "timeout of a trx lock waiting for Acquisition, unit: ms"
#define DB_CFG_TRX_MONITOR_ENABLE_DESC "enable transaction monitor or not"
#define DB_CFG_ENABLE_CONN_SUBS_STATIS_DESC "enable conn subs statis"
#define DB_CFG_SCHEDULE_PERF_STAT_IS_ENABLED_DESC "enable statistics related to the actual CPU time usage or not"
#define DB_CFG_MAX_SORT_BUFFER_SIZE_DESC "max size of sort buffer memory alloced by server, unit: MB"
#define DB_CFG_MONITOR_WORKER_SCHEDULE_PERIOD_DESC \
    "monitor worker schedule period from 0ms to 1000ms and 0 means monitor worker is not exist. unit:ms"
#define DB_CFG_WORKER_HUNG_THRESHOLD_DESC "worker dead threshold, unit:s"

#define DB_CFG_DML_OPER_STAT_IS_ENABLED_DESC "enable dml operation statistics or not"
#define DB_CFG_DML_PERF_STAT_IS_ENABLED_DESC "enable dml performance statistics or not"

#define DB_CFG_USER_POLICY_MODE_DESC "set the authentication mode"

#define DB_CFG_MAX_STMT_CNT_DESC "max count of stmt per session in server"

#define DB_CFG_RT_SCHEDULE_MODE_DESC \
    "Server schedule mode: 0 = thread per connection, 1 = recv agent, 2 = thread pool, 3 = single thread"

#define DB_CFG_PERMANENT_WORKER_NUM_DESC "Core worker number for worker pool."

#define DB_CFG_MAX_CONN_MSG_SHM_MEM_DESC \
    "Maximum shared memory that can be used by each connection excluding subs connections, unit: MB."

#define DB_CFG_SUBS_CHANNEL_SHARE_MEM_MAX_DESC "the global max share mem size that subs channel can use, unit: MB."

#define DB_CFG_SUBS_CHANNEL_DYNAMIC_MEM_MAX_DESC \
    "the global max dynamic mem size that rtos subs channel can use, unit: MB."

#define DB_CFG_COMPATIBLE_V3_DESC "compatible V3"

#define DB_CFG_TABLE_LOCK_IS_ENABLED_DESC "enable table lock or not"

#define DB_CFG_IS_FAST_READ_UNCOMMITTED_DESC "fast read uncommitted or not"

#define DB_CFG_CLUSTER_HASH_IS_ENABLED_DESC "enable cluster hash or not"

#define DB_CFG_IS_CLT_STATIS_ENABLE_DESC \
    "If true, server enable to receive statistics from client, otherwise, server did not receive."

#define DB_CFG_MESSAGE_SECURITY_CHECK_DESC \
    "If true, server will check whether the messages and buffers are legal, default 1(abled)."

#define DB_CFG_DEFAULT_TRANSACTION_TYPE_DESC \
    "Type of the transaction mode. The options are optimistic and pessimistic. 0-PESSIMISTIC_TRX 1-OPTIMISTIC_TRX"

#define DB_CFG_DEFAULT_ISOLATION_LEVEL_DESC \
    "Isolation Level Type : 0-READ_UNCOMMITTED 1-READ_COMMITTED 2-REPEATABLE_READ 3-SERIALIZABLE"

#define DB_CFG_HASH_CLUSTER_BUCKET_CNT_DESC "HashCluster key's hash buckets count."

#define DB_CFG_YANG_BIG_OBJECT_SIZE_DESC "Check whether the response from the server is a large object, unit: KB."

#define DB_CFG_SHM_SUB_MSG_TIMEOUT_DESC \
    "The client consumes subscription data within the timeout period, and the server reclaims the memory. unit: min."

#define DB_CFG_DATALOG_TIMEOUT_SCHEDULE_INTERVAL_DESC \
    "Datalog Timeout schedule interval is used to set the cycle schedule interval of background task. unit: ms."

#define DB_CFG_DATALOG_CALLBACK_TIMEOUT_THRESHOLD_DESC "Datalog timeout duration of the pubsub callback. unit: s."

#define DB_CFG_DTL_UDF_TIME_OUT_DESC "Timeout of single UDF function, range from 0 to 1000. unit: ms."

#define DB_CFG_CONNECT_TIMEOUT_DESC                                                                                \
    "Timeout duration of the connection, unit min. If there is no service request within the specified period of " \
    "time (Direct read is a service request, but heartbeat is not a service request.) The server closes the "      \
    "connection. The value 0 indicates that the function is disabled."

#define DB_CFG_UDF_MAX_MEM_DESC "Maximum dynamic memory that can be allocated in udf in one session. unit: KB."

#define DB_CFG_UDF_MEM_ALLOC_ALARM_DESC \
    "Alarm size of alloc memory in udf. Record alarm information in log when the limit is exceeded. unit: KB"

#define DB_CFG_UDF_ENABLE_DESC "If true, server enables loading UDF, otherwise server cannot load."

#define DB_CFG_DATALOG_LOG_RUN_LINK_DESC "Datalog Run Link Log Level."

#define DB_CFG_ENABLE_DATALOG_DML_WHEN_UPG_DESC "If enable datalog dml when upgrading datalog program."

#define DB_CFG_DATALOG_UPG_FETCH_SIZE_DESC "Datalog Upgrade fetch size is used to set MaxFetchSize from heap."

#define DB_CFG_MAX_NORMAL_TABLE_NUM_DESC "max count of normal vertexlabel in server, default 2000, range:[1000, 10000]."

#define DB_CFG_DATALOG_UPGRADE_MEM_ACTUAL_ESTIMATE_PERCENTAGE_DESC \
    "datalog upgrade actually used memSize / upgrade estimated memSize * 100%, default 300."

#define DB_CFG_MAX_YANG_TABLE_NUM_DESC "max count of yang vertexlabel in server, default 2000, range:[1000, 10000]."
#define DB_CFG_MAX_SUBSCRIPTION_NUM_DESC "max count of subscription in server, default 1024, range:[1024, 2048]."

#define DB_CFG_FEATURE_LIB_PATH_DESC "The so lib path for dynamic load features."
#define DB_CFG_FEATURE_NAMES_DESC "The feature names of dynamic load features."
#define DB_CFG_DATALOG_SO_PATH_DESC \
    "File paths where server import datalog so resources from (Please open schemaLoader first)."
#define DB_CFG_LOCK_TABLE_BUCKET_NUM_DESC "num of lock table bucket (must be 509 or 1021)."
#define DB_CFG_VERTICAL_ISOLATION_ENABLE_DESC "enable vertical isolation or not"

#define DB_CFG_PERSISTENT_MODE_DESC "persistent mode, on demand or increment."
// redo config desc
#define DB_CFG_REDO_PUB_BUF_SIZE_DESC "redo public buffer size, unit is KB."
#define DB_CFG_REDO_BUF_PARTS_DESC "redo public buffer part count."
#define DB_CFG_REDO_FLUSH_BY_SIZE_DESC \
    "redo log will be flushing while buffer size greater than threadhold, unit is KB.\"0\" meats disable."
#define DB_CFG_REDO_FLUSH_BY_TIME_DESC \
    "redo log will be flushing while  time interval greater than threadhold, unit is ms.\"0\" meats disable."
#define DB_CFG_REDO_FLUSH_CHECK_PERIOD_DESC "redo flush check period"
#define DB_CFG_REDO_FILE_SIZE_DESC "redo log file size, unit is MB."
#define DB_CFG_REDO_FILE_COUNT_DESC "redo log file count."
// space config desc
#define DB_CFG_DATA_FILE_MAX_COUNT_DESC "space data file max count"
#define DB_CFG_USER_SPACE_FILE_MAX_COUNT_DESC "db user space max size, unit: M"
#define DB_CFG_USER_SPACE_SIZE_LIMITED_DESC "db user space max size is limited or not, true or false"
#define DB_CFG_DATA_FILE_SIZE_DESC "db space file max size, unit:M"
// check point
#define DB_CFG_CKPT_PERIOD_DESC "The period of checkpoint, unit: second."
#define DB_CFG_CKPT_THLD_DESC "If queue greater than threshold checkpoint will be triggered."

#define DB_CFG_CRC_CHECK_ENABLE_DESC "if true, page crc check will be enabled"
#define DB_CFG_DOUBLE_WRITE_ENABLE_DESC "if true, double write will be enabled"
// 多区持久化
#define DB_CFG_MULTIZONE_NUM_DESC "enable/disable persistent file backup."
#define DB_CFG_RECOVERY_ZONE_ID_DESC "recovery by zone id"
#define DB_CFG_PERSISTENT_COMPRESS_MODE_DESC "persistent compress mode, no compression, on page or device."
// bufferpool
#define DB_CFG_SE_BUFFERPOOL_POLICY_DESC "policy of buff pool which define the priority of different pages to in or out"
#define DB_CFG_SPACE_COMPRESS_AREA_ENABLE_DESC "space compress is on when using bufferpool with on-demand persistent."
#define DB_CFG_SPACE_COMPRESS_AREA_SIZE_DESC "space compress area size, unit: KB."
#define DB_CFG_SE_BUFFER_POOL_CHUNK_SIZE_DESC \
    "bufferpool chunk size, unit: KB. pageBuf will not be split into chunk if 0"
#define DB_CFG_BUFFERPOOL_MEM_TYPE_DESC "bufferpool memory type. 0 means dynamic memory, 1 means shared memory."

#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
#define DB_CFG_FILE_LOCK_PATH_DESC "usr defined file lock path, default is /run/verona/"
#endif
#define DB_CFG_DIRECT_WRITER_DESC "Configuration options of the direct write switch."
#define DB_CFG_SHM_MAP_MODE_DESC "Configuration options of the share memory mapping mode."
#define DB_CFG_SHM_SPACE_SIZE_DESC "Configuration options of the share memory space size for central management."
#define DB_CFG_ENABLE_SHARE_MSG_POOL_DESC "Configuration options of the share msg pool."
#if defined(FEATURE_TS) || defined(FEATURE_SQL)
#define DB_CFG_TEMP_FILE_DIR_DESC "Temp file directory."
#define DB_CFG_OP_MEMORY_DESC "Memory size for storing intermediate results of the operator."
#define DB_CFG_IS_FILE_TAPE_CHECK_SUM_DESC "enable the filetape checksum function, the default value is 0."
#endif
#define DB_CFG_ENABLE_SIGNAL_REGISTER_DESC "whether register DB signal."
#ifdef WARM_REBOOT
#define DB_PERIODIC_PERSISTENCE_DESC \
    "Configuration options of the periodc persistence. 0 means not support periodc persistence."
#define DB_PERIODIC_PERSISTENCE_DIRPATH_DESC "Configuration options of the periodc persistence dirPath."
#endif
#define DB_CFG_CLIENT_HEARTBEAT_INTERVAL_DESC "Report interval of heartbeat in client, unit seconds"

// record db's config need considerate compatibility
static DbCfgItemDescT g_compatibleDbCfgItemDescs[(int32_t)COMPATIBLE_DB_CFG_EM_ITEM_BUTT] = {
    // { Name,         Description info,          Min value,        Max value,      Default,       Validating function,
    // ID,        ChangeMode,                  Data type,                    Required,                 Notify function}
    {"redoFileSize", DB_CFG_REDO_FILE_SIZE_DESC, 16, 1024, "64", CfgParamValidateIrange,
        COMPATIBLE_DB_CFG_REDO_FILE_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
}; /* for coding safe */

static DbCfgItemDescT g_dbCfgItemDescs[(int32_t)DB_CFG_EM_ITEM_BUTT] = {
    // { Name,        Description info,         Min value,       Max value,     Default,    Validating function,     ID,
    // ChangeMode,                  Data type,                    Required,                            Notify function}
    {"deviceSize", DB_CFG_SE_DEV_SIZE_DESC, 4, 262144, "4096", CfgParamValidateIrangePowerOfTwo, DB_CFG_SE_DEV_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"extendSize", DB_CFG_SE_EXTEND_SIZE_DESC, 4, 262144, "4096", CfgParamValidateIrange, DB_CFG_SE_EXTEND_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"pageSize", DB_CFG_SE_PAGE_SIZE_DESC, 4, 64, "4", CfgParamValidatePageSize, DB_CFG_SE_PAGE_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"ctrlPageSize", DB_CFG_SE_CTRL_PAGE_SIZE_DESC, 4, 64, "32", CfgParamValidatePageSize, DB_CFG_SE_CTRL_PAGE_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"crcCheckEnable", DB_CFG_CRC_CHECK_ENABLE_DESC, 0, 1, "1", CfgParamValidateIrange, DB_CFG_CRC_CHECK_ENABLE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"dwrEnable", DB_CFG_DOUBLE_WRITE_ENABLE_DESC, 0, 1, "1", CfgParamValidateIrange, DB_CFG_DWR_ENABLE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"dwrBlockNum", DB_CFG_DOUBLE_WRITE_BLOCK_NUM_DESC, 1, 128, "128", CfgParamValidateIrange, DB_CFG_DWR_BLOCK_NUM,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    // bufferPoolSize max size : 4G - pageSize * 1
    {"bufferPoolSize", DB_CFG_SE_BUFFERPOOL_SIZE_DESC, 256, 4194304, "1024", CfgParamValidateIrange,
        DB_CFG_SE_BUFFERPOOL_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    // buffer pool policy , default to be BUF_RECYCLE_TABLE which value is 1
    {"bufferPoolPolicy", DB_CFG_SE_BUFFERPOOL_POLICY_DESC, BUF_RECYCLE_NORMAL, BUF_RECYCLE_VECTOR_INDEX, "1",
        CfgParamValidateIrange, DB_CFG_SE_BUFFERPOOL_POLICY, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"compressSpaceEnable", DB_CFG_SPACE_COMPRESS_AREA_ENABLE_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_SPACE_COMPRESS_AREA_ENABLE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"compressSpaceSize", DB_CFG_SPACE_COMPRESS_AREA_SIZE_DESC, 4096, DB_MAX_INT32, "1048576", CfgParamValidateIrange,
        DB_CFG_SPACE_COMPRESS_AREA_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"bufferpoolMemType", DB_CFG_BUFFERPOOL_MEM_TYPE_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_BUFFERPOOL_MEM_TYPE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    // mini 多了一个require参数
    {"bufferPoolChunkSize", DB_CFG_SE_BUFFER_POOL_CHUNK_SIZE_DESC, 0, 4194304, "0", CfgParamValidateIrange,
        DB_CFG_SE_BUFFER_POOL_CHUNK_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
#if defined(IDS_HAOTIAN)
    {"bufferPoolNum", DB_CFG_SE_BUFFERPOOL_NUM_DESC, 1, 32, "1", CfgParamValidateIrange, DB_CFG_SE_BUFFERPOOL_NUM,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"preFetchPagesEnable", DB_CFG_PRE_FETCH_PAGES_ENABLE_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_PRE_FETCH_PAGES_ENABLE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"maxPreFetchThreNum", DB_CFG_MAX_PRE_FETCH_THRE_NUM_DESC, 1, 256, "8", CfgParamValidateIrange,
        DB_CFG_MAX_PRE_FETCH_THRE_NUM, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    {"loadTablePriorityRatio", DB_CFG_SE_BUFFERPOOL_HIGH_PRIORITY_RATIO_DESC, 0, 70, "30", CfgParamValidateIrange,
        DB_CFG_SE_BUFFERPOOL_HIGH_PRIORITY_RATIO, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"bufferPoolPriorityRatio", DB_CFG_SE_BUFFERPOOL_PRIORITY_RATIO_DESC, 0, 80, "40", CfgParamValidateIrange,
        DB_CFG_SE_BUFFERPOOL_PRIORITY_RATIO, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#endif
    {"maxConnNum", DB_CFG_CONN_MAX_DESC, 16, 1024, "100", CfgParamValidateIrange, DB_CFG_CONN_MAX,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    // redo begin, redo's public buf
    {"redoPubBufSize", DB_CFG_REDO_PUB_BUF_SIZE_DESC, 256, 16384, "4096", CfgParamValidateIrange,
        DB_CFG_REDO_PUB_BUF_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"redoFlushByTrx", DB_CFG_REDO_FLUSH_BY_TRX_DESC, 0, 2, "0", CfgParamValidateIrange, DB_CFG_REDO_FLUSH_BY_TRX,
        DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, false, NULL},
    // 32M * 2 + max(redoPubBufSize) * 2 = 96 (redoFileSize >= 2 * redoPubBufSize)
    {"redoFileSize", DB_CFG_REDO_FILE_SIZE_DESC, 96, 1024, "96", CfgParamValidateIrange, DB_CFG_REDO_FILE_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    // space
    {"dataFilePath", DB_CFG_DATA_FILE_DIR_PATH_DESC, 0, 0, "", CfgParamValidatePath, DB_CFG_DATA_FILE_DIR_PATH,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, true, NULL},
    {"sharedModeEnable", DB_CFG_SHARED_MODE_ENABLE_DESC, 0, 1, "0", CfgParamValidateIrange, DB_CFG_SHARED_MODE_ENABLE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    // fusion
    {"messageSecurityCheck", DB_CFG_MESSAGE_SECURITY_CHECK_DESC, 1, 1, "1", CfgParamValidateDefaultValue,
        DB_CFG_MESSAGE_SECURITY_CHECK, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, false, NULL},
    {"longProcTimeThreshold", DB_CFG_LONG_PROC_TIME_THRESHOLD_DESC, 100, 100, "100", CfgParamValidateDefaultValue,
        DB_CFG_LONG_PROC_TIME_THRESHOLD, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, false, NULL},
    {"logLengthMax", DB_CFG_LOG_LENGTH_MAX_DESC, 512, 512, "512", CfgParamValidateDefaultValue, DB_CFG_LOG_LENGTH_MAX,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"lockTableBucketNum", DB_CFG_LOCK_TABLE_BUCKET_NUM_DESC, 1021, 1021, "1021", CfgParamValidateDefaultValue,
        DB_CFG_LOCK_TABLE_BUCKET_NUM, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, false, NULL},
    {"maxSeMem", DB_CFG_SE_MAX_MEM_DESC, 1048552, 1048552, "1048552", CfgParamValidateDefaultValue, DB_CFG_SE_MAX_MEM,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"instanceId", DB_CFG_SE_GET_INSTANCE_ID_DESC, FIRST_INSTANCE_ID, 1, "1", CfgParamValidateDefaultValue,
        DB_CFG_SE_GET_INSTANCE_ID, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"isUseHugePage", "Use huge page for storage or not.", 0, 0, "0", CfgParamValidateDefaultValue, DB_CFG_HUGE_PAGE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"memCompactEnable", DB_CFG_MEM_COMPACT_ENABLE_DESC, 0, 0, "0", CfgParamValidateDefaultValue,
        DB_CFG_MEM_COMPACT_ENABLE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"minFragmentationMemThreshold", "min fragmentation Memory threshold, unit: MB", 64, 64, "64",
        CfgParamValidateDefaultValue, DB_CFG_DEFRAGMENTATION_MEM_THRESHOLD, DB_CFG_CHANGE_NOT_ALLOWED,
        DB_DATATYPE_INT32, false, NULL},
    {"maxUndoSpaceSize", DB_CFG_MAX_UNDO_SPACE_SIZE_DESC, 300, 300, "300", CfgParamValidateDefaultValue,
        DB_CFG_MAX_UNDO_SPACE_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"trxLockWakeupPeriod", DB_CFG_TRX_LOCK_WAKEUP_PERIOD_DESC, 100, 100, "100", CfgParamValidateDefaultValue,
        DB_CFG_TRX_LOCK_WAKEUP_PERIOD, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"trxDeadlockCheckPeriod", DB_CFG_TRX_LOCK_DEADLOCK_CHECK_PERIOD_DESC, 300, 300, "300",
        CfgParamValidateDefaultValue, DB_CFG_TRX_LOCK_DEADLOCK_CHECK_PERIOD, DB_CFG_CHANGE_NOT_ALLOWED,
        DB_DATATYPE_INT32, false, NULL},
    {"latchDeadlockDebugTimeout", DB_CFG_LATCH_DEADLOCK_DEBUG_TIMEOUT_DESC, 10000000, 10000000, "10000000",
        CfgParamValidateDefaultValue, DB_CFG_LATCH_DEADLOCK_DEBUG_TIMEOUT, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32,
        false, NULL},
    {"trxLockJumpQueuePeriod", DB_CFG_TRX_LOCK_JUMP_QUEUE_PERIOD_DESC, 500, 500, "500", CfgParamValidateDefaultValue,
        DB_CFG_TRX_LOCK_JUMP_QUEUE_PERIOD, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"trxLockTimeOut", DB_CFG_TRX_LOCK_TIME_OUT_DESC, 1000, 1000, "1000", CfgParamValidateDefaultValue,
        DB_CFG_TRX_LOCK_TIME_OUT, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"trxMonitorEnable", DB_CFG_TRX_MONITOR_ENABLE_DESC, 0, 0, "0", CfgParamValidateDefaultValue,
        DB_CFG_TRX_MONITOR_ENABLE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},  // mini不用，默认关闭
    {"compatibleV3", DB_CFG_COMPATIBLE_V3_DESC, 0, 0, "0", CfgParamValidateDefaultValue, DB_CFG_COMPATIBLE_V3,
        DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, false, NULL},
    {"persistentMode", DB_CFG_PERSISTENT_MODE_DESC, 1, 1, "1", CfgParamValidateDefaultValue, DB_CFG_PERSISTENT_MODE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    // redo
    {"redoBufParts", DB_CFG_REDO_BUF_PARTS_DESC, 1, 1, "1", CfgParamValidateDefaultValue, DB_CFG_REDO_BUF_PARTS,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"redoFlushBySize", DB_CFG_REDO_FLUSH_BY_SIZE_DESC, 1000, 1000, "1000", CfgParamValidateDefaultValue,
        DB_CFG_REDO_FLUSH_BY_SIZE, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, false, NULL},
    {"redoFlushByTime", DB_CFG_REDO_FLUSH_BY_TIME_DESC, 1000, 1000, "1000", CfgParamValidateDefaultValue,
        DB_CFG_REDO_FLUSH_BY_TIME, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, false, NULL},
    {"redoFlushCheckPeriod", DB_CFG_REDO_FLUSH_CHECK_PERIOD_DESC, 500, 500, "500", CfgParamValidateDefaultValue,
        DB_CFG_REDO_FLUSH_CHECK_PERIOD, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"redoFileCount", DB_CFG_REDO_FILE_COUNT_DESC, 1, 1, "1", CfgParamValidateDefaultValue, DB_CFG_REDO_FILE_COUNT,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"redoFileDropOnClose", DB_CFG_REDO_FILE_DROP_ON_CLOSE_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_REDO_FILE_DROP_ON_CLOSE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    // space
    {"dbFilesMaxCnt", DB_CFG_DB_FILES_MAX_COUNT_DESC, 2, 2, "2", CfgParamValidateDefaultValue,
        DB_CFG_DB_FILES_MAX_COUNT, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"dbFileSize", DB_CFG_DB_FILE_SIZE_DESC, 1073741824, 1073741824, "1073741824", CfgParamValidateDefaultValue,
        DB_CFG_DB_FILE_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"spaceMaxNum", DB_CFG_SPACE_MAX_NUM_DESC, 2, 2, "2", CfgParamValidateDefaultValue, DB_CFG_SPACE_MAX_NUM,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"fileSyncWriteEnable", DB_CFG_FILE_SYNC_WRITE_ENABLE_DESC, 0, 1, "1", CfgParamValidateIrange,
        DB_CFG_FILE_SYNC_WRITE_ENABLE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    // checkpoint
    {"ckptPeriod", DB_CFG_CKPT_PERIOD_DESC, 65535, 65535, "65535", CfgParamValidateDefaultValue, DB_CFG_CKPT_PERIOD,
        DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, false, NULL},  // mini不使用，设置为最大值
    {"ckptThreshold", DB_CFG_CKPT_THLD_DESC, DB_MAX_INT32, DB_MAX_INT32, "2147483647", CfgParamValidateDefaultValue,
        DB_CFG_CKPT_THLD, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, false, NULL},  // mini不使用，设置为最大值
    // 多区持久化
    {"multizonePersistNum", DB_CFG_MULTIZONE_NUM_DESC, 1, 1, "1", CfgParamValidateDefaultValue, DB_CFG_MULTIZONE_NUM,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"recoveryZoneId", DB_CFG_RECOVERY_ZONE_ID_DESC, 1, 1, "1", CfgParamValidateDefaultValue, DB_CFG_RECOVERY_ZONE_ID,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"persistentCompressMode", DB_CFG_PERSISTENT_COMPRESS_MODE_DESC, 0, 0, "0", CfgParamValidateDefaultValue,
        DB_CFG_PERSISTENT_COMPRESS_MODE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},  // mini不用，默认关闭
    {"featureLibPath", DB_CFG_FEATURE_LIB_PATH_DESC, 0, 0, "", NULL, DB_CFG_FEATURE_LIB_PATH, DB_CFG_CHANGE_NOT_ALLOWED,
        DB_DATATYPE_STRING, false, NULL},
    {"minFragmentationRateThreshold", "min fragmentation rate threshold, unit: percentage", 50, 50, "50",
        CfgParamValidateDefaultValue, DB_CFG_DEFRAGMENTATION_RATE_THRESHOLD, DB_CFG_CHANGE_NOT_ALLOWED,
        DB_DATATYPE_INT32, NULL},
    {"directWrite", DB_CFG_DIRECT_WRITER_DESC, 0, 0, "0", CfgParamValidateDefaultValue, DB_CFG_DIRECT_WRITE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},  // mini不用，默认关闭
    // mini目前使用紧密排布的ctrl page
    {"condensedCtrlPages", DB_CFG_CONDENSED_CTRL_PAGES_DESC, 1, 1, "1", CfgParamValidateDefaultValue,
        DB_CFG_CONDENSED_CTRL_PAGES, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
    {"enableReleaseDevice", DB_CFG_ENABLE_RELEASE_DEVICE_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_ENABLE_RELEASE_DEVICE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"isUseRsm", DB_CFG_IS_USE_RSM_DESC, 0, 1, "0", CfgParamValidateIrange, DB_CFG_IS_USE_RSM,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"RsmBlockSize", DB_CFG_RSM_BLOCK_MAX_SIZE_DESC, 1, 128, "128", CfgParamValidateIrange, DB_CFG_RSM_BLOCK_MAX_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"RsmKeyRange", DB_CFG_RSM_KEY_RANGE_DESC, 0, 0, "0,9", NULL, DB_CFG_RSM_KEY_RANGE, DB_CFG_CHANGE_NOT_ALLOWED,
        DB_DATATYPE_STRING, NULL},
    // end fusion
    // oplog
    {"oplogFileSize", DB_CFG_OPLOG_FILE_SIZE_DESC, 512, 5120, "2048", CfgParamValidateIrange, DB_CFG_OPLOG_FILE_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, false, NULL},
#if defined(FEATURE_TS) || defined(FEATURE_SQL)
    {"tempFileDir", DB_CFG_TEMP_FILE_DIR_DESC, 0, 0, "/data/gmdb/temp", CfgParamValidateLogPath, DB_CFG_TEMP_FILE_DIR,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
    {"operatorMemory", DB_CFG_OP_MEMORY_DESC, 10, 1024, "100", CfgParamValidateIrange, DB_CFG_OP_MEMORY,
        DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    {"isFileTapeCheckSum", DB_CFG_IS_FILE_TAPE_CHECK_SUM_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_IS_FILE_TAPE_CHECK_SUM, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
#endif
    {"isFastReadUncommitted", DB_CFG_IS_FAST_READ_UNCOMMITTED_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_IS_FAST_READ_UNCOMMITTED, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"enableTableLock", DB_CFG_TABLE_LOCK_IS_ENABLED_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_TABLE_LOCK_IS_ENABLED, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"datalogUpgradeFetchSize", DB_CFG_DATALOG_UPG_FETCH_SIZE_DESC, 1, 2147483647, "1", CfgParamValidateIrange,
        DB_CFG_DATALOG_UPG_FETCH_SIZE, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    {"maxNormalTableNum", DB_CFG_MAX_NORMAL_TABLE_NUM_DESC, DB_MAX_NORMAL_TABLE_NUM_MIN, DB_MAX_NORMAL_TABLE_NUM_MAX,
        "2000", CfgParamValidateIrange, DB_CFG_MAX_NORMAL_TABLE_NUM, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32,
        NULL},
    {"defaultTransactionType", DB_CFG_DEFAULT_TRANSACTION_TYPE_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_DEFAULT_TRANSACTION_TYPE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"defaultIsolationLevel", DB_CFG_DEFAULT_ISOLATION_LEVEL_DESC, 0, 3, "1", CfgParamValidateIrange,
        DB_CFG_DEFAULT_ISOLATION_LEVEL, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"maxYangTableNum", DB_CFG_MAX_YANG_TABLE_NUM_DESC, DB_MAX_YANG_TABLE_NUM_MIN, DB_MAX_YANG_TABLE_NUM_MAX, "2000",
        CfgParamValidateIrange, DB_CFG_MAX_YANG_TABLE_NUM, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"maxSubscriptionNum", DB_CFG_MAX_SUBSCRIPTION_NUM_DESC, DB_MAX_SUBSCRIPTION_NUM_MIN, DB_MAX_SUBSCRIPTION_NUM_MAX,
        "1024", CfgParamValidateIrange, DB_CFG_MAX_SUBSCRIPTION_NUM, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32,
        NULL},
    {"userPolicyMode", DB_CFG_USER_POLICY_MODE_DESC, 0, 2, "0", CfgParamValidateIrange, DB_CFG_USER_POLICY_MODE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"DBA", DB_CFG_DBA_DESC, 0, 0, DB_DBA_NAME ":" DB_DBA_PROCESS_NAME, NULL, DB_CFG_DBA_INFO,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
    {"maxSortBufferSize", DB_CFG_MAX_SORT_BUFFER_SIZE_DESC, 1, 256, "64", CfgParamValidateIrange,
        DB_CFG_MAX_SORT_BUFFER_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"enableClusterHash", DB_CFG_CLUSTER_HASH_IS_ENABLED_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_CLUSTER_HASH_IS_ENABLED, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"enableDmlOperStat", DB_CFG_DML_OPER_STAT_IS_ENABLED_DESC, 0, 1, "1", CfgParamValidateIrange,
        DB_CFG_DML_OPER_STAT_IS_ENABLED, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"enableDmlPerfStat", DB_CFG_DML_PERF_STAT_IS_ENABLED_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_DML_PERF_STAT_IS_ENABLED, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    {"maxTotalDynSize", "Total size of Dynamic memory alloced by server, unit: MB.", 24, 16384, "2048",
        CfgParamValidateIrange, DB_CFG_SERVER_TOTAL_DYN_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"udfMemAllocAlarmSize", DB_CFG_UDF_MEM_ALLOC_ALARM_DESC, 0, 2048, "2048", CfgParamValidateIrange,
        DB_CFG_UDF_MEM_ALLOC_ALARM, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"udfMemSizeMax", DB_CFG_UDF_MAX_MEM_DESC, 1, 2048, "2048", CfgParamValidateIrange, DB_CFG_UDF_MAX_MEM,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"udfTimeOut", DB_CFG_DTL_UDF_TIME_OUT_DESC, 0, 1000, "1000", CfgParamValidateIrange, DB_CFG_DTL_UDF_TIME_OUT,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"auditLogEnableDDL", DB_CFG_AUDIT_LOG_DDL_ENABLE_DESC, 0, 1, "1", CfgParamValidateIrange,
        DB_CFG_AUDIT_LOG_DDL_ENABLE, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    {"maxStmtCnt", DB_CFG_MAX_STMT_CNT_DESC, 1, 65535, "10240", CfgParamValidateIrange, DB_CFG_MAX_STMT_CNT,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"yangBigObjectSize", DB_CFG_YANG_BIG_OBJECT_SIZE_DESC, 1, 65536, "512", CfgParamValidateIrange,
        DB_CFG_YANG_BIG_OBJECT_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"workerHungThreshold", DB_CFG_WORKER_HUNG_THRESHOLD_DESC, 3, 1000, "20,299,300",
        CfgParamValidateWorkerHungThreshold, DB_CFG_WORKER_HUNG_THRESHOLD, DB_CFG_CHANGE_NOT_ALLOWED,
        DB_DATATYPE_STRING, NULL},
    {"maxSysDynSize", "system memory size of Dynamic memory alloced by server, unit: MB", 12, 16351, "512",
        CfgParamValidateIrange, DB_CFG_MAX_SYS_DYN_MEM, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},  // if 64
    {"defaultTablespaceMaxSize", DB_CFG_DEFAULT_TABLE_SPACE_MAX_SIZE_DESC, 1, 16352, "32", CfgParamValidateIrange,
        DB_CFG_DEFAULT_TABLE_SPACE_MAX_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"defaultHashType", DB_CFG_DEFAULT_HASH_TYPE_DESC, 0, 0, DB_DEFAULT_HASH_TYPE_CFG_HASH_INDEX, NULL,
        DB_CFG_DEFAULT_HASH_TYPE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
};

static Status DbCfgItemSetInt32Value(const DbCfgItemDescT *desc, DbCfgItemT *item, int64_t value)
{
    DB_POINTER(item);
    item->type = DB_DATATYPE_INT32;
    Status ret = GMERR_OK;
    if (desc->validate != NULL && (ret = desc->validate(desc, (void *)&value)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "Config int value inv range, %u: %s", MAX_NAME_LEN, desc->name);
        return ret;
    }
    item->int32Val = (int32_t)value;
    return GMERR_OK;
}

static Status DbCfgItemSetStrValue(const DbCfgItemDescT *desc, DbCfgItemT *item, const char *value)
{
    DB_POINTER(item);
    item->type = DB_DATATYPE_STRING;
    if (desc->validate != NULL && desc->validate(desc, (const void *)value) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, " Config String value inv: %u, %s", MAX_NAME_LEN, desc->name);
        return GMERR_CONFIG_ERROR;
    }
    size_t valueLen = strlen(value) + 1;
    // released with cfgMgr on error
    char *newValue = DB_MALLOC(valueLen);
    if (newValue == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Alloc mem for config str value");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    errno_t errNo = strcpy_s(newValue, valueLen, value);
    if (errNo != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Copy to config str value, ret: %d", errNo);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    DB_FREE(item->str);
    item->str = TrimInvisibleChar(newValue);
    return GMERR_OK;
}

static Status DbCfgItemSetDefaultValue(DbCfgItemT *item, const DbCfgItemDescT *desc)
{
    DB_POINTER2(item, desc);
    Status ret;
    if (desc->type == DB_DATATYPE_INT32) {
        int32_t defaultValue = 0;
        ret = DbStrToInt32(desc->defaultValue, &defaultValue);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Set config default value, convert input data.");
            return ret;
        }
        return DbCfgItemSetInt32Value(desc, item, defaultValue);
    }
    size_t strLen = strlen(desc->defaultValue) + 1;
    char *defaultValue = DB_MALLOC(strLen);
    if (defaultValue == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Set config default value.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    errno_t errNo = strcpy_s(defaultValue, strLen, desc->defaultValue);
    if (errNo != EOK) {
        DB_FREE(defaultValue);
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Set config default value, ret: %d", errNo);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    ret = DbCfgItemSetStrValue(desc, item, defaultValue);
    DB_FREE(defaultValue);
    return ret;
}

DbCfgEmItemIdE DbCfgGetIdByName(const char *configName)
{
    DB_POINTER(configName);
    uint32_t id;
    for (id = 0; id < (uint32_t)DB_CFG_EM_ITEM_BUTT; id++) {
        if (DbStrCmp(g_dbCfgItemDescs[id].name, configName, true) == 0) {
            return (DbCfgEmItemIdE)id;
        }
    }
    return DB_CFG_EM_ITEM_BUTT;
}

static Status DbSetConfigValue(DbCfgMgrT *cfgMgr, const char *key, void *value, const DbCfgItemDescT *desc)
{
    DB_POINTER4(cfgMgr, key, value, desc);
    DbCfgItemT *cfgItem = &cfgMgr->cfgItems[desc->id];
    Status ret;
    if (desc->type == DB_DATATYPE_STRING) {
        const char *configValue = DbJsonStringValue(value);
        if (configValue == NULL) {
            DB_LOG_ERROR(GMERR_CONFIG_ERROR, " Config value can not be null or datatype mismatch: %u, %s", MAX_NAME_LEN,
                desc->name);
            return GMERR_CONFIG_ERROR;
        }
        ret = DbCfgItemSetStrValue(desc, cfgItem, configValue);
    } else if (DbJsonIsNumber(value)) {
        double configValue = DbJsonNumberValue(value);
        ret = DbCfgItemSetInt32Value(desc, cfgItem, configValue);
    } else {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, " Config value datatype mismatch: %u, %s", MAX_NAME_LEN, desc->name);
        return GMERR_CONFIG_ERROR;
    }
    cfgItem->prepared = ret == GMERR_OK;
    return ret;
}

static void DbCfgFreeTempCfgMgr(DbCfgMgrT **cfgMgr)
{
    DbCfgMgrT *cfgMgrPtr = *cfgMgr;
    for (uint32_t n = 0; n < (uint32_t)DB_CFG_EM_ITEM_BUTT; n++) {
        DbCfgItemT *cfgItem = &cfgMgrPtr->cfgItems[n];
        if (cfgItem->str != NULL) {
            DB_FREE(cfgItem->str);
            cfgItem->str = NULL;
        }
    }
    DB_FREE(cfgMgrPtr);
    *cfgMgr = NULL;
}

static Status DbCfgGetDefaultCfgMgr(DbCfgMgrT **cfgMgr)
{
    DB_POINTER(cfgMgr);
    DbCfgMgrT *tmpCfgMgr = (DbCfgMgrT *)DB_MALLOC(sizeof(DbCfgMgrT));
    if (tmpCfgMgr == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Alloc mem for default config mgr.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(tmpCfgMgr, sizeof(DbCfgMgrT), 0x0, sizeof(DbCfgMgrT));
    Status ret = GMERR_OK;
    DbRWSpinInit(&tmpCfgMgr->cfgModifyLock);
    for (uint32_t n = 0; n < sizeof(g_dbCfgItemDescs) / sizeof(g_dbCfgItemDescs[0]); n++) {
        DbCfgItemT *cfgItem = &tmpCfgMgr->cfgItems[n];
        cfgItem->id = g_dbCfgItemDescs[n].id;
        ret = DbCfgItemSetDefaultValue(cfgItem, &g_dbCfgItemDescs[n]);
        if (ret != GMERR_OK) {
            DbCfgFreeTempCfgMgr(&tmpCfgMgr);
            return ret;
        }
    }
    for (uint32_t n = 0; n < sizeof(g_compatibleDbCfgItemDescs) / sizeof(g_compatibleDbCfgItemDescs[0]); n++) {
        DbCfgItemT *compatibleCfgItem = &tmpCfgMgr->compatibleCfgItems[n];
        compatibleCfgItem->id = g_compatibleDbCfgItemDescs[n].id;
        ret = DbCfgItemSetDefaultValue(compatibleCfgItem, &g_compatibleDbCfgItemDescs[n]);
        if (ret != GMERR_OK) {
            DbCfgFreeTempCfgMgr(&tmpCfgMgr);
            return ret;
        }
    }
    tmpCfgMgr->cfgItemNum = DB_CFG_EM_ITEM_BUTT;
    tmpCfgMgr->compatibleCfgItemNum = COMPATIBLE_DB_CFG_EM_ITEM_BUTT;
    *cfgMgr = tmpCfgMgr;
    return ret;
}

static Status DbCfgNormalizePathCfg(DbCfgMgrT *cfgMgr)
{
    DbCfgItemT *cfgItem = &cfgMgr->cfgItems[DB_CFG_DATA_FILE_DIR_PATH];
    char *oldValue = (char *)cfgItem->str;
    if (strlen(oldValue) == 0) {  // dataFilePath must be set by the user.
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "dataFilePath must be set first.");
        return GMERR_CONFIG_ERROR;
    }
    char *tmp = DB_MALLOC(PATH_MAX);
    if (tmp == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    // get the data file real path, the dir path must already exist.
    Status ret = DbGetRealPath(oldValue, tmp, PATH_MAX);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "dataFilePath inv.");
        DB_FREE(tmp);
        return GMERR_CONFIG_ERROR;
    }

    if (DbDirExist(tmp)) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "datafilepath can't be an exist dir.");
        DB_FREE(tmp);
        return GMERR_CONFIG_ERROR;
    }

    size_t pathLen = strlen(tmp) + 1;
    char *newValue = DB_MALLOC(pathLen);
    if (newValue == NULL) {
        DB_FREE(tmp);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (strcpy_s(newValue, pathLen, tmp) != EOK) {
        DB_FREE(newValue);
        DB_FREE(tmp);
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Set value for path.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    DB_FREE(tmp);
    DB_FREE(oldValue);
    DbAdptMallocTrim(0);
    cfgItem->str = newValue;
    return GMERR_OK;
}

static Status DbCfgVerifyRequiredItemAllSet(DbCfgMgrT *cfgMgr)
{
    DB_POINTER(cfgMgr);
    for (uint32_t i = 0; i < DB_CFG_EM_ITEM_BUTT; i++) {
        if (g_dbCfgItemDescs[i].required && !cfgMgr->cfgItems[i].prepared) {
            DB_LOG_ERROR(GMERR_CONFIG_ERROR, "%s required, but not set.", g_dbCfgItemDescs[i].name);
            return GMERR_CONFIG_ERROR;
        }
        cfgMgr->cfgItems[i].prepared = true;  // all prepared as default value set
    }
    return GMERR_OK;
}

static Status DbVerifyAndSetConfigJson(DbCfgMgrT *cfgMgr, DbJsonT *configJson)
{
    DB_POINTER2(cfgMgr, configJson);
    Status ret = GMERR_OK;
    int configJsonSize = DbJsonGetArraySize(configJson);
    for (int i = 0; i < configJsonSize; i++) {
        void *configObject = DbJsonArrayGet(configJson, i);
        const char *key = DbJsonObjectIterKey(configObject);
        if (key == NULL) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "config name NULL.");
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        void *value = DbJsonObjectGet(configJson, key);
        DbCfgEmItemIdE id = DbCfgGetIdByName(key);
        if (id == DB_CFG_EM_ITEM_BUTT) {
            DB_LOG_ERROR(GMERR_CONFIG_ERROR, "config name (%s) inv.", (const char *)key);
            return GMERR_CONFIG_ERROR;
        }
        if (value == NULL || DbJsonGetType(value) == DB_JSON_NULL) {
            DB_LOG_ERROR(GMERR_CONFIG_ERROR, "config value (%s) NULL.", (const char *)key);
            return GMERR_CONFIG_ERROR;
        }
        if (cfgMgr->cfgItems[id].prepared) {
            DB_LOG_ERROR(GMERR_CONFIG_ERROR, "case-insensitive duplicate config name (%s) found.", (const char *)key);
            return GMERR_CONFIG_ERROR;
        }
        ret = DbSetConfigValue(cfgMgr, key, value, &g_dbCfgItemDescs[id]);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    ret = DbCfgVerifyRequiredItemAllSet(cfgMgr);
    if (ret != GMERR_OK) {
        return ret;
    }
    // For special requirements, the path needs to be normalized to an absolute path.
    // Currently, only DB_CFG_DATA_FILE_DIR_PATH will be normalized.
    return DbCfgNormalizePathCfg(cfgMgr);
}

static bool DbCfgItemEquals(DbCfgItemT *item1, DbCfgItemT *item2)
{
    DB_POINTER2(item1, item2);
    if (!(item1->prepared == item2->prepared && item1->id == item2->id && item1->type == item2->type)) {
        return false;
    }

    if (item1->type == DB_DATATYPE_STRING) {
        return DbStrCmp((const char *)item1->str, (const char *)item2->str, false) == 0;
    }

    // 临时分配的cfgMgr对应的instanceId是不分配的，这里不检查
    if (item1->id == DB_CFG_SE_GET_INSTANCE_ID) {
        return true;
    }

    return (item1->int32Val == item2->int32Val);
}

Status DbCfgCreateCfgMgr(const char *configJson, DbCfgMgrT **cfgMgr)
{
    DB_POINTER2(configJson, cfgMgr);
    DbJsonT *configJsonObj = NULL;
    int translationResult = DbJsonStrTransToJsonObject(configJson, false, true, &configJsonObj);
    *cfgMgr = NULL;
    if (configJsonObj == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_JSON_CONTENT, "Inv json content.");
        return GMERR_INVALID_JSON_CONTENT;
    }
    Status ret;
    do {
        if (translationResult != DB_JSON_OK || DB_JSON_OBJECT != DbJsonGetType(configJsonObj)) {
            DB_LOG_ERROR(GMERR_INVALID_JSON_CONTENT, "Inv json content.");
            ret = GMERR_INVALID_JSON_CONTENT;
            break;
        }
        ret = DbCfgGetDefaultCfgMgr(cfgMgr);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Inv get default manager.");
            break;
        }
        ret = DbVerifyAndSetConfigJson(*cfgMgr, configJsonObj);
        if (ret != GMERR_OK && *cfgMgr != NULL) {
            DbCfgFreeCfgMgr(cfgMgr);
            *cfgMgr = NULL;
        }
    } while (0);
    DbJsonDelete(configJsonObj);
    return ret;
}

void DbCfgFreeCfgMgr(DbCfgMgrT **cfgMgr)
{
    DB_POINTER(cfgMgr);
    DbCfgMgrT *mgr = *cfgMgr;
    if (mgr == NULL) {
        return;
    }
    for (uint32_t i = 0; i < mgr->cfgItemNum; i++) {
        DB_FREE(mgr->cfgItems[i].str);
        mgr->cfgItems[i].str = NULL;
    }
    for (uint32_t i = 0; i < mgr->compatibleCfgItemNum; i++) {
        DB_FREE(mgr->compatibleCfgItems[i].str);
        mgr->compatibleCfgItems[i].str = NULL;
    }
    DB_FREE(mgr);
    *cfgMgr = NULL;
}

bool DbCfgEquals(DbCfgMgrT *oldMgr, DbCfgMgrT *newMgr)
{
    if (oldMgr == NULL || newMgr == NULL) {
        DB_LOG_WARN(GMERR_INVALID_PARAMETER_VALUE, "mgr1 or mgr2 NULL");
        return oldMgr == newMgr;
    }
    if (oldMgr->cfgItemNum != newMgr->cfgItemNum) {
        return false;
    }
    for (uint32_t i = 0; i < oldMgr->cfgItemNum; i++) {
        if (!DbCfgItemEquals(&oldMgr->cfgItems[i], &newMgr->cfgItems[i])) {
            return false;
        }
    }
    return true;
}

Status DbCfgGetInt32(DbCfgMgrT *mgr, uint32_t cfgItemId, bool fetchLatestConfig, int32_t *value)
{
    DB_POINTER(value);
    // Configuration items cannot be obtained before they are successfully loaded.
    if (mgr == NULL) {
        DB_LOG_WARN(GMERR_CONFIG_ERROR, "Get config int value.");
        return GMERR_CONFIG_ERROR;
    }
    DbCfgItemT *cfgItem = fetchLatestConfig ? &mgr->cfgItems[cfgItemId] : &mgr->compatibleCfgItems[cfgItemId];
    *value = cfgItem->int32Val;
    return GMERR_OK;
}

Status DbCfgGetInt32WithLock(DbCfgMgrT *mgr, uint32_t cfgItemId, bool fetchLatestConfig, int32_t *value)
{
    DB_POINTER(value);
    // Configuration items cannot be obtained before they are successfully loaded.
    if (mgr == NULL) {
        DB_LOG_WARN(GMERR_CONFIG_ERROR, "Get config int value with spin lock.");
        return GMERR_CONFIG_ERROR;
    }
    DbRWSpinRLock(&mgr->cfgModifyLock);
    Status ret = DbCfgGetInt32(mgr, cfgItemId, fetchLatestConfig, value);
    DbRWSpinRUnlock(&mgr->cfgModifyLock);
    return ret;
}

Status DbCfgGetStr(DbCfgMgrT *mgr, uint32_t cfgItemId, char *value, size_t maxDestLen)
{
    DB_POINTER(value);
    // Configuration items cannot be obtained before they are successfully loaded.
    if (mgr == NULL) {
        DB_LOG_WARN(GMERR_CONFIG_ERROR, "Get config string value.");
        return GMERR_CONFIG_ERROR;
    }
    DbCfgItemT *cfgItem = &mgr->cfgItems[cfgItemId];
    errno_t errNo = strcpy_s(value, maxDestLen, cfgItem->str);
    return errNo == EOK ? GMERR_OK : GMERR_MEMORY_OPERATE_FAILED;
}

Status DbCfgGetStrWithLock(DbCfgMgrT *mgr, uint32_t cfgItemId, char *value, size_t maxDestLen)
{
    DB_POINTER(value);
    // Configuration items cannot be obtained before they are successfully loaded.
    if (mgr == NULL) {
        DB_LOG_WARN(GMERR_CONFIG_ERROR, "Get config string value with spin lock.");
        return GMERR_CONFIG_ERROR;
    }
    DbRWSpinRLock(&mgr->cfgModifyLock);
    Status ret = DbCfgGetStr(mgr, cfgItemId, value, maxDestLen);
    DbRWSpinRUnlock(&mgr->cfgModifyLock);
    return ret;
}

// fusion
static Status DbCfgItemExtractValue(DbCfgItemT *item, DbCfgValueT *cfgValue)
{
    DbCfgValueT value = {0};
    value.type = item->type;
    if (value.type == DB_DATATYPE_INT32) {
        value.int32Val = item->int32Val;
    } else {
        errno_t err = strcpy_s(value.str, sizeof(value.str), item->str);
        if (err != EOK) {
            DB_LOG_WARN(GMERR_MEMORY_OPERATE_FAILED, "Extract cfgItem value, ret: %d", err);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    }
    *cfgValue = value;
    return GMERR_OK;
}

Status DbCfgValueCpy(const DbCfgValueT *src, DbCfgValueT *tgt)
{
    DB_POINTER2(src, tgt);
    tgt->type = src->type;

    if (src->type == DB_DATATYPE_INT32) {
        tgt->int32Val = src->int32Val;
        return GMERR_OK;
    }

    errno_t ret = strcpy_s(tgt->str, sizeof(tgt->str), src->str);
    if (ret != EOK) {
        DB_LOG_WARN(GMERR_MEMORY_OPERATE_FAILED, "Copy config value, ret: %d", ret);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status DbCfgGet(DbCfgMgrHandleT handle, DbCfgEmItemIdE id, DbCfgValueT *value)
{
    DB_ASSERT(id != DB_CFG_DATA_FILE_DIR_PATH);  // 获取路径请使用DbCfgGetDirRealPath
    if (handle == NULL || value == NULL) {
        DB_LOG_WARN(GMERR_NO_DATA, "Get config value, inv handle or value.");
        return GMERR_NO_DATA;
    }
    DbCfgMgrT *mgr = (DbCfgMgrT *)handle;
    if ((uint32_t)id >= mgr->cfgItemNum) {
        DB_LOG_WARN(GMERR_CONFIG_ERROR, "Config item id out of bound");
        return GMERR_CONFIG_ERROR;
    }
    DbCfgValueT cfgValue = {0};
    Status ret = DbCfgItemExtractValue(&mgr->cfgItems[id], &cfgValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbRWSpinRLock(&mgr->cfgModifyLock);
    ret = DbCfgValueCpy(&cfgValue, value);
    DbRWSpinRUnlock(&mgr->cfgModifyLock);
    return ret;
}

const char *DbGetDataFileDirRealPath(DbCfgMgrT *cfgMgr)
{
    const DbCfgItemT *cfgItem = &cfgMgr->cfgItems[DB_CFG_DATA_FILE_DIR_PATH];
    return cfgItem->str;
}

// ensure that the dirPath ends with char '/'
Status DbFormatDirPath(char *dirPath, uint32_t buffSize)
{
    DB_POINTER(dirPath);

    size_t length = strlen(dirPath);
    if (dirPath[length - 1] == '/') {
        return GMERR_OK;
    }

    if (length + 1 >= buffSize) {
        return GMERR_CONFIG_ERROR;
    }
    dirPath[length] = '/';
    dirPath[length + 1] = '\0';
    return GMERR_OK;
}

Status DbCfgGetDirPath(DbCfgMgrHandleT handle, uint32_t buffSize, char *value)
{
    if (handle == NULL || value == NULL) {
        DB_LOG_WARN(GMERR_NO_DATA, "Get config value, inv handle or value.");
        return GMERR_NO_DATA;
    }
    DbCfgMgrT *mgr = (DbCfgMgrT *)handle;
    const char *path = DbGetDataFileDirRealPath(mgr);
    errno_t ret = strcpy_s(value, buffSize, path);
    if (ret != EOK) {
        DB_LOG_WARN(GMERR_MEMORY_OPERATE_FAILED, "Copy config value, ret: %d", ret);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status DbCfgSetInstanceId(DbCfgMgrHandleT handle, uint16_t instanceId)
{
    if (handle == NULL) {
        return GMERR_NO_DATA;
    }
    DbCfgMgrT *cfgMgr = (DbCfgMgrT *)handle;
    DbCfgItemT *cfgItem = &cfgMgr->cfgItems[DB_CFG_SE_GET_INSTANCE_ID];
    cfgItem->int32Val = (int32_t)instanceId;
    return GMERR_OK;
}

Status DbSrvGetUint8CfgsQuick(DbCfgMgrHandleT handle, CfgPairUintT *cfgPairs, uint32_t num)
{
    DB_POINTER(cfgPairs);
    DbCfgValueT cfgValue;
    for (uint32_t i = 0; i < num; i++) {
        DB_POINTER(cfgPairs[i].value);
        Status ret = DbCfgGet(handle, cfgPairs[i].id, &cfgValue);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Get uint8 configer (ID: %" PRIu32 "; NAME: %s) . %" PRIu32 " | %" PRIu32 ".",
                (uint32_t)cfgPairs[i].id, cfgPairs[i].cfgName, i, num);
            return ret;
        }
        *(uint8_t *)cfgPairs[i].value = (uint8_t)cfgValue.int32Val;
    }
    return GMERR_OK;
}

Status DbSrvGetUint16CfgsQuick(DbCfgMgrHandleT handle, CfgPairUintT *cfgPairs, uint32_t num)
{
    DB_POINTER(cfgPairs);
    DbCfgValueT cfgValue;
    for (uint32_t i = 0; i < num; i++) {
        DB_POINTER(cfgPairs[i].value);
        Status ret = DbCfgGet(handle, cfgPairs[i].id, &cfgValue);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Get uint16 configer (ID: %" PRIu32 "; NAME: %s) . %" PRIu32 " | %" PRIu32 ".",
                (uint32_t)cfgPairs[i].id, cfgPairs[i].cfgName, i, num);
            return ret;
        }
        *(uint16_t *)cfgPairs[i].value = (uint16_t)cfgValue.int32Val;
    }
    return GMERR_OK;
}

Status DbSrvGetUint32CfgsQuick(DbCfgMgrHandleT handle, CfgPairUintT *cfgPairs, uint32_t num)
{
    DB_POINTER(cfgPairs);
    DbCfgValueT cfgValue;
    for (uint32_t i = 0; i < num; i++) {
        DB_POINTER(cfgPairs[i].value);
        Status ret = DbCfgGet(handle, cfgPairs[i].id, &cfgValue);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Get uint32 configer (ID: %" PRIu32 "; NAME: %s) . %" PRIu32 " | %" PRIu32 ".",
                (uint32_t)cfgPairs[i].id, cfgPairs[i].cfgName, i, num);
            return ret;
        }
        *(uint32_t *)cfgPairs[i].value = (uint32_t)cfgValue.int32Val;
    }
    return GMERR_OK;
}

Status DbInitConfigFile(const char *configFileName, DbCfgMgrHandleT *cfgHandle)
{
    return GMERR_OK;
}

void DbSetCfgHandle(const DbCfgMgrHandleT cfgHandle)
{}

bool DbCfgValueIsEqual(const DbCfgValueT *oldValue, const DbCfgValueT *newValue)
{
    return false;
}

const char *DbGetFeatureLibPath(DbCfgMgrT *cfgMgr)
{
    DbCfgItemT *cfgItem = &cfgMgr->cfgItems[DB_CFG_FEATURE_LIB_PATH];
    return cfgItem->str;
}

Status DbGetSharedModeVal(DbCfgMgrHandleT cfgHandle, uint16_t *value)
{
    DbCfgValueT cfgValue;
    Status ret = DbCfgGet(cfgHandle, DB_CFG_SHARED_MODE_ENABLE, &cfgValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get shared mode value.");
        return ret;
    }
    *value = (cfgValue.int32Val == DB_ENABLE_SHARE_MODE);
    return GMERR_OK;
}

Status DbGetRsmBlockCnt(uint32_t *rsmBlockCnt)
{
    return GMERR_OK;
}

Status DbCfgGetFirstDBAInfo(char *dbaName, uint32_t dbaNameLen, char *dbaProcess, uint32_t dbaProcessLen)
{
    return GMERR_OK;
}

Status DbCfgGetBool(uint32_t cfgItemsId, bool *value, bool isNeedLock, DbInstanceHdT dbInstance)
{
    return GMERR_OK;
}

Status DbCfgVerifyUserPolicyStrongMode(void)
{
    return GMERR_OK;
}

Status CfgParamThresholdGetValue(
    char *valueString, uint32_t strLen, uint32_t *value, uint32_t valueNum, const char *info)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

#if defined(FEATURE_TS) || defined(FEATURE_SQL)
Status DbCfgGetTempFileMaxSize(uint64_t *maxSize, DbInstanceHdT dbInstance)
{
    return GMERR_OK;
}
#endif

bool g_gmdbIsTsInstance = false;
bool DbCfgIsTsInstance(void)
{
    return g_gmdbIsTsInstance;
}
// end fusion
