/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: db_mini_crc.c
 * Description: implement of db mini crc
 * Author:
 * Create: 2023-12-27
 */

#include "db_crc.h"
#include "zlib.h"

void DbAddCheckSum(char *data, uint32_t dataSize, uint32_t *checkSum)
{
    *checkSum = 0;                                                    // checkSum point to data->checkSum,
    *checkSum = (uint32_t)crc32(0, (const uint8_t *)data, dataSize);  // using CRC function in zlib
}

bool DbCheckCrc(char *data, uint32_t dataSize, uint32_t *checkSum)
{
    uint32_t cacheCheckSum = *checkSum;
    *checkSum = 0;
    uint32_t calcCheckSum = (uint32_t)crc32(0, (const uint8_t *)data, dataSize);  // using CRC function in zlib
    *checkSum = cacheCheckSum;
    return cacheCheckSum == calcCheckSum;
}
