/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: implement of mini hash.
 * Author:
 * Create: 2023-12-12
 */

#ifdef FEATURE_MINIKV
#ifndef HASH_USE_OPENSSL
#define HASH_USE_OPENSSL
#endif
#endif
#include <string.h>

#include "db_hash.h"
#include "adpt_define.h"
#include "adpt_log.h"
#ifdef HASH_USE_OPENSSL
#include "openssl/lhash.h"
#else
#include "xxh3.h"
#endif

#define MAGIC_BIAS_NUM 0x9e3779b9
#define MAGIC_LEFT_BITWISE 6
#define MAGIC_RIGHT_BITWISE 2
#define FNV32_OFFSET_BASIS 0x811c9dc5U
#define FNV32_PRIME 0x1000193U

uint32_t DbHash32(const uint8_t *key, uint32_t len)
{
    DB_POINTER(key);
    const char *data = (const char *)key;
    uint32_t hash = FNV32_OFFSET_BASIS;
    for (uint32_t i = 0; i < len; ++i) {
        hash *= FNV32_PRIME;
        hash = hash ^ data[i];
    }

    return hash;
}

uint32_t DbHash32Combine(uint32_t lhs, uint32_t rhs)
{
    lhs ^= rhs + MAGIC_BIAS_NUM + (lhs << MAGIC_LEFT_BITWISE) + (lhs >> MAGIC_RIGHT_BITWISE);
    return lhs;
}

uint32_t DbHash32WithSeed(const uint8_t *key, uint32_t len, uint32_t seed)
{
    DB_POINTER(key);
#ifdef HASH_USE_OPENSSL
    return (uint32_t)OPENSSL_LH_strhash((const char *)key);
#else
    return XXH32(key, len, seed);
#endif
}

ALWAYS_INLINE uint32_t DbStrToHash32(const char *str)
{
    DB_POINTER(str);
#ifdef HASH_USE_OPENSSL
    return (uint32_t)OPENSSL_LH_strhash(str);
#else
    size_t size = strlen(str);
    return XXH32(str, size, DB_XXHASH_SEED);
#endif
}
