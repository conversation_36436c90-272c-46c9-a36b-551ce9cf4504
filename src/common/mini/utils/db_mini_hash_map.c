/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: db_mini_hash_map.c
 * Description: implement of db mini hash map
 * Author:
 * Create: 2023-12-14
 */

#include <string.h>
#include "gmc_errno.h"
#include "adpt_memory.h"
#include "db_hashmap.h"

inline static uint32_t DbOamapBucketHashMask(void)
{
    // returns max value that DbOamapBucketT::hash can hold
    // if DbOamapBucketT::hash is a bitfield, this will be useful
    DbOamapBucketT bucket = {};
    bucket.hash = -1;  // Deliberately used -1, will be converted to the corresponding maximum integer.
    return bucket.hash;
}

#define FREE 0u
#define USED 1u
#define DELETED 2u

// from db_hashmap_common.c
uint32_t DbOamapGetNearPrime(uint32_t capacity)
{
    static const uint32_t prime[] = {7, 13, 31, 61, 127, 251, 509, 1021, 1297, 2039, 4093, 8191, 16381, 32749, 65521,
        131071, 262139, 524287, 1048573, 2097143, 4194301, 8388593, 16777213, 33554393, 67108859, 134217689, 268435399,
        536870909, 1073741789, 2147483647, 0xfffffffb};

    const uint32_t count = (uint32_t)ELEMENT_COUNT(prime);
    uint32_t left = 0;           // prime[i] (i < left) cannot be result
    uint32_t right = count - 1;  // prime[i] (i >= right) can be result

    do {
        uint32_t mid = left + ((right - left) >> 1);
        if (capacity > prime[mid]) {
            left = mid + 1;
        } else {
            right = mid;
        }
    } while (left != right);

    // if capacity is greater than all values in prime[], will return prime[count - 1]
    return prime[left];
}

static void DbOamapBucketZeroOut(DbOamapBucketT *bucket)
{
    bucket->state = FREE;
    bucket->hash = 0;
    bucket->key = NULL;
    bucket->value = NULL;
}

static void DbOamapZeroOut(DbOamapT *map)
{
    DbOamapT zero = {0};
    *map = zero;
}

static DbOamapBucketT *DbOamapAllocBucket(DbMemCtxT *memCtx, uint32_t count)
{
    // calc size
    const uint32_t scale = (uint32_t)sizeof(DbOamapBucketT);
    const uint32_t maxCapacity = UINT32_MAX / scale;
    if (count > maxCapacity) {
        return NULL;
    }
    uint32_t size = count * scale;

    // alloc memory
    DbOamapBucketT *buckets = NULL;
    if (memCtx != NULL) {
        buckets = DbDynMemCtxAlloc(memCtx, size);
    } else {
        buckets = DbAdptDynamicMemAlloc(size);
    }
    if (buckets == NULL) {
        return NULL;
    }

    // initialize buckets
    for (uint32_t index = 0; index < count; ++index) {
        DbOamapBucketT *bucket = &buckets[index];
        DbOamapBucketZeroOut(bucket);
    }
    return buckets;
}

// The bucket argument should be set to NULL by caller after calling this function
static void DbOamapFreeBucket(DbMemCtxT *memCtx, DbOamapBucketT *buckets)
{
    if (memCtx != NULL) {
        DbDynMemCtxFree(memCtx, buckets);
    } else {
        DbAdptDynamicMemFree(buckets);
    }
}

static void DbOamapInsertBucket(DbOamapT *map, DbOamapBucketT *bucket, uint32_t hash, void *key, void *value)
{
    bucket->state = USED;
    bucket->hash = hash;
    bucket->key = key;
    bucket->value = value;
    map->size++;
}

static void *DbOamapRemoveBucket(DbOamapT *map, DbOamapBucketT *bucket)
{
    void *value = bucket->value;
    bucket->state = DELETED;
    bucket->hash = 0;
    bucket->key = NULL;
    bucket->value = NULL;
    map->size--;
    return value;
}

Status DbOamapInit(DbOamapT *map, uint32_t capacity, DbOamapCompareT comparator, DbMemCtxT *memCtx, bool extendable)
{
    DB_POINTER2(map, comparator);

    DbRWSpinInit(&map->rwLock);
    map->extendable = extendable;
    map->memCtx = memCtx;
    map->size = 0;
    map->capacity = DbOamapGetNearPrime(capacity);
    map->compareFunc = comparator;
    map->buckets = DbOamapAllocBucket(map->memCtx, map->capacity);
    if (map->buckets == NULL) {
        DbOamapZeroOut(map);
        // fail to alloc memory while initializing hash map
        return GMERR_OUT_OF_MEMORY;
    }

    return GMERR_OK;
}

void DbOamapDestroy(DbOamapT *map)
{
    // Destroy must be guaranteed to be reentrant.
    if (map == NULL) {
        return;
    }
    if (map->buckets != NULL) {
        // the whole map will be zeroed out later, no UAF risk
        DbOamapFreeBucket(map->memCtx, map->buckets);
    }
    DbOamapZeroOut(map);
}

static bool DbOamapCheckBucket(const DbOamapT *map, uint32_t index, uint32_t hash, const void *key, DbOamapBucketT **bk)
{
    DbOamapBucketT *bucket = &map->buckets[index];

    // remember first candidate, but we need to search further to ensure that no duplicate is found
    if (bucket->state == DELETED) {
        if (*bk == NULL) {
            *bk = bucket;
        }
        return false;
    }

    // no duplicate is found, time to return
    if (bucket->state == FREE) {
        if (*bk == NULL) {
            *bk = bucket;
        }
        return true;
    }

    // duplicate is found, time to return
    if ((hash == bucket->hash) && map->compareFunc(key, bucket->key) != 0) {
        *bk = bucket;
        return true;
    }

    return false;
}

static DbOamapBucketT *DbOamapFind(const DbOamapT *map, uint32_t hash, const void *key)
{
    if (map->capacity == 0) {
        return NULL;
    }
    uint32_t capacity = map->capacity;
    uint32_t remain = hash % capacity;
    DbOamapBucketT *bucket = NULL;

    for (uint32_t index = remain; index < capacity;) {
        if (DbOamapCheckBucket(map, index, hash, key, &bucket)) {
            return bucket;
        }
        ++index;
    }

    for (uint32_t index = remain; index > 0;) {
        --index;
        if (DbOamapCheckBucket(map, index, hash, key, &bucket)) {
            return bucket;
        }
    }

    return bucket;
}

static Status DbOamapRehash(DbOamapT *map, uint32_t newCapacity)
{
    uint32_t size = map->size;
    uint32_t oldCapacity = map->capacity;
    DbOamapBucketT *oldBuckets = map->buckets;

    if (size > oldCapacity || size > newCapacity) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    DbOamapBucketT *newBuckets = DbOamapAllocBucket(map->memCtx, newCapacity);
    if (newBuckets == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    map->capacity = newCapacity;
    map->buckets = newBuckets;

    for (uint32_t index = 0; index < oldCapacity; ++index) {
        DbOamapBucketT *oldBucket = &oldBuckets[index];
        if (oldBucket->state == USED) {
            DbOamapBucketT *newBucket = DbOamapFind(map, oldBucket->hash, oldBucket->key);
            // because size <= newCapacity, newBucket should never be NULL
            if (newBucket == NULL) {
                DB_ASSERT(false);
                continue;
            }
            *newBucket = *oldBucket;
        }
    }
    // oldBuckets will never be used again, no UAF risk
    DbOamapFreeBucket(map->memCtx, oldBuckets);
    return GMERR_OK;
}

Status DbOamapInsert(DbOamapT *map, uint32_t hash, void *key, void *value, uint32_t *mapIdx)
{
    // The value may be empty. The check is not required.
    DB_POINTER2(map, key);
    if (map->capacity == 0) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    uint32_t hashVar = hash & DbOamapBucketHashMask();

    if (map->extendable && (map->size + (map->size >> 1) >= map->capacity)) {
        // capacity can't be greater than DbOamapAllocBucket::max, so map->capacity + 1 will never overflow
        uint32_t capacity = DbOamapGetNearPrime(map->capacity + 1);
        Status result = DbOamapRehash(map, capacity);
        if (result != GMERR_OK) {
            DB_LOG_WARN(result, "rehash inv.");
        }
    }

    DbOamapBucketT *bucket = DbOamapFind(map, hashVar, key);
    if (bucket == NULL) {
        // hash map is full and cannot extend capacity
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (bucket->state == USED) {
        // duplicate key mistake while inserting into hash map
        return GMERR_DUPLICATE_OBJECT;
    }

    if (mapIdx != NULL) {
        // warning: follow the old logic, mapIdx is assigned <=> return code is GMERR_OK
        *mapIdx = (uint32_t)(bucket - map->buckets);
    }
    DbOamapInsertBucket(map, bucket, hashVar, key, value);
    return GMERR_OK;
}

void *DbOamapLookupKey(const DbOamapT *map, uint32_t hash, const void *key)
{
    DB_POINTER2(map, key);
    if (map->capacity <= 0) {
        return NULL;
    }
    uint32_t hashVar = hash & DbOamapBucketHashMask();

    const DbOamapBucketT *bucket = DbOamapFind(map, hashVar, key);
    if (bucket != NULL && bucket->state == USED) {
        return bucket->key;
    }

    return NULL;
}

void *DbOamapLookup(const DbOamapT *map, uint32_t hash, const void *key, uint32_t *mapIdx)
{
    DB_POINTER2(map, key);
    if (map->capacity <= 0) {
        return NULL;
    }
    uint32_t hashVar = hash & DbOamapBucketHashMask();

    const DbOamapBucketT *bucket = DbOamapFind(map, hashVar, key);

    if (bucket != NULL && bucket->state == USED) {
        if (mapIdx != NULL) {
            // warning: follow the old logic, mapIdx is assigned <=> value is found
            *mapIdx = (uint32_t)(bucket - map->buckets);
        }
        return bucket->value;
    }

    return NULL;
}

void *DbOamapRemove(DbOamapT *map, uint32_t hash, const void *key)
{
    DB_POINTER2(map, key);
    if (map->capacity <= 0) {
        return NULL;
    }
    uint32_t hashVar = hash & DbOamapBucketHashMask();

    DbOamapBucketT *bucket = DbOamapFind(map, hashVar, key);
    if (bucket != NULL && bucket->state == USED) {
        return DbOamapRemoveBucket(map, bucket);
    }

    return NULL;
}

Status DbOamapFetch(const DbOamapT *map, DbOamapIteratorT *iter, void **key, void **value)
{
    DB_POINTER4(map, iter, key, value);

    for (uint32_t index = *iter; index < map->capacity;) {
        const DbOamapBucketT *bucket = &map->buckets[index];
        ++index;
        if (bucket->state == USED) {
            *iter = index;
            *key = bucket->key;
            *value = bucket->value;
            return GMERR_OK;
        }
    }

    *iter = map->capacity;
    *key = NULL;
    *value = NULL;
    return GMERR_NO_DATA;  // end of container reached
}

uint32_t DbOamapPtrCompare(const void *key1, const void *key2)
{
    return (key1 == key2);
}

uint32_t DbOamapUint64Compare(const void *key1, const void *key2)
{
    DB_POINTER2(key1, key2);
    return (*(const uint64_t *)key1 == *(const uint64_t *)key2);
}

uint32_t DbOamapUint32Compare(const void *key1, const void *key2)
{
    DB_POINTER2(key1, key2);
    return (*(const uint32_t *)key1 == *(const uint32_t *)key2);
}
