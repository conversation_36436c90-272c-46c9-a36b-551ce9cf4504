/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: implement of db recycle stack.
 * Author: zhaoliang
 * Create: 2024-02-18
 */

#include "db_recycle_stack.h"

static void **DbRecycleStackAllocItems(DbMemCtxT *memCtx, uint32_t capacity)
{
    void **stackItems = NULL;
    size_t allocSize = sizeof(void *) * capacity;
    if (memCtx != NULL) {
        stackItems = (void **)DbDynMemCtxAlloc(memCtx, allocSize);
    } else {
        stackItems = (void **)DbAdptDynamicMemAlloc(allocSize);
    }
    if (stackItems != NULL) {
        (void)memset_s(stackItems, allocSize, 0, allocSize);
    }
    return stackItems;
}

inline static void DbRecycleStackClear(DbRecycleStackT *stack)
{
    stack->rwLock.lock = 0u;
    stack->capacity = 0u;
    stack->memCtx = NULL;
    stack->isRecyclable = false;
    stack->useSize = 0u;
    stack->top = 0u;
    stack->stackItems = NULL;
}

Status DbRecycleStackInit(DbRecycleStackT *stack, uint32_t capacity, DbMemCtxT *memCtx, bool isRecyclable)
{
    DB_POINTER(stack);
    if (capacity == 0u) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DbRWSpinInit(&stack->rwLock);
    stack->capacity = capacity;
    stack->memCtx = memCtx;
    stack->isRecyclable = isRecyclable;
    stack->useSize = 0u;
    stack->top = capacity - 1;
    stack->stackItems = DbRecycleStackAllocItems(memCtx, capacity);
    if (stack->stackItems == NULL) {
        // fail to alloc memory while initializing stack
        DbRecycleStackClear(stack);
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

Status DbRecycleStackPush(DbRecycleStackT *stack, void *item, void **oldItem)
{
    DB_POINTER2(stack, item);
    DbRWSpinWLock(&stack->rwLock);
    if (stack->useSize >= stack->capacity && !stack->isRecyclable) {
        // stack is full
        DbRWSpinWUnlock(&stack->rwLock);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    uint32_t nextTop = (stack->top + 1) % stack->capacity;
    void *tempItem = NULL;
    if (stack->useSize == stack->capacity) {
        // the last item
        tempItem = stack->stackItems[nextTop];
    } else {
        stack->useSize++;
    }
    stack->stackItems[nextTop] = item;
    stack->top = nextTop;
    if (oldItem != NULL) {
        *oldItem = tempItem;
    }
    DbRWSpinWUnlock(&stack->rwLock);
    return GMERR_OK;
}

static void *GetTopItem(DbRecycleStackT *stack, bool isRemove)
{
    DbRWSpinWLock(&stack->rwLock);
    if (stack->useSize == 0u) {
        DbRWSpinWUnlock(&stack->rwLock);
        return NULL;
    }
    void *topItem = stack->stackItems[stack->top];
    if (isRemove) {
        stack->stackItems[stack->top] = NULL;
        stack->top = (stack->top + stack->capacity - 1) % stack->capacity;
        stack->useSize--;
        DB_ASSERT(stack->useSize < stack->capacity);
    }
    DbRWSpinWUnlock(&stack->rwLock);
    return topItem;
}

void *DbRecycleStackPop(DbRecycleStackT *stack)
{
    DB_POINTER(stack);
    return GetTopItem(stack, true);
}

void *DbRecycleStackPeek(DbRecycleStackT *stack)
{
    DB_POINTER(stack);
    return GetTopItem(stack, false);
}

bool DbRecycleStackIsEmpty(DbRecycleStackT *stack)
{
    DB_POINTER(stack);
    return stack->useSize == 0u;
}

bool DbRecycleStackIsFull(DbRecycleStackT *stack)
{
    DB_POINTER(stack);
    return stack->useSize == stack->capacity;
}

void DbRecycleStackDestroy(DbRecycleStackT *stack)
{
    if (stack == NULL) {
        return;
    }
    if (stack->stackItems != NULL) {
        if (stack->memCtx != NULL) {
            DbDynMemCtxFree(stack->memCtx, stack->stackItems);
        } else {
            DbAdptDynamicMemFree(stack->stackItems);
        }
    }
    DbRecycleStackClear(stack);
}
