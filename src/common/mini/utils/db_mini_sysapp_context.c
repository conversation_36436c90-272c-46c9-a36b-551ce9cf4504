/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: DB 系统应用分区公共依赖部分
 * Author: zhaoyongluo
 * Create: 2022-08-26
 */

#include "db_sysapp_context.h"
#include "db_instance.h"
#include "db_config.h"
#include "db_log.h"

#define BLOCK_POOL_BASE_SIZE (64 * DB_KIBI)
#define BLOCK_POOL_STEP_SIZE (1 * DB_MEBI)
#define BLOCK_POOL_MAX_SIZE (2 * DB_MEBI)
#define MIN_APP_SHM_SIZE 12
#define MIN_APP_DYN_SIZE MIN_APP_SHM_SIZE

DbMemCtxT *DbSrvGetSysDynCtx(uint16_t instanceId)
{
    DbInstanceT *dbInstance = NULL;
    Status ret = DbGetInstanceById(instanceId, &dbInstance);
    if (ret != GMERR_OK) {
        return NULL;
    }
    DbMemCtxT *ctx = (DbMemCtxT *)dbInstance->sysDynMemCtx;
    DbReleaseInstance(dbInstance);
    return ctx;
}

DbMemCtxT *DbSrvGetAppDynCtx(uint16_t instanceId)
{
    DbInstanceT *dbInstance = NULL;
    Status ret = DbGetInstanceById(DbGetProcGlobalId(), &dbInstance);
    if (ret != GMERR_OK) {
        return NULL;
    }
    DbMemCtxT *ctx = (DbMemCtxT *)dbInstance->appDynMemCtx;
    DbReleaseInstance(dbInstance);
    return ctx;
}

Status DbSrvInitSysAppDynCtx(DbInstanceHdT dbInstance)
{
    DbInstanceT *dbIns = (DbInstanceT *)dbInstance;
    DbMemCtxT *topDynMemctx = (DbMemCtxT *)dbIns->topDynMemCtx;
    if (topDynMemctx == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    dbIns->sysDynMemCtx = topDynMemctx;
    dbIns->appDynMemCtx = topDynMemctx;
    return GMERR_OK;
}

DbMemCtxT *DbSrvGetSysShmCtx(uint16_t instanceId)
{
    DbInstanceT *dbInstance = NULL;
    Status ret = DbGetInstanceById(instanceId, &dbInstance);
    if (ret != GMERR_OK) {
        return NULL;
    }
    DbMemCtxT *ctx = (DbMemCtxT *)dbInstance->sysShmMemCtx;
    DbReleaseInstance(dbInstance);
    return ctx;
}

DbMemCtxT *DbSrvGetAppShmCtx(uint16_t instanceId)
{
    DbInstanceT *dbInstance = NULL;
    Status ret = DbGetInstanceById(instanceId, &dbInstance);
    if (ret != GMERR_OK) {
        return NULL;
    }
    DbMemCtxT *ctx = (DbMemCtxT *)dbInstance->appShmMemCtx;
    DbReleaseInstance(dbInstance);
    return ctx;
}

Status CreateSysAppShmCtx(
    DbInstanceT *dbIns, DbMemCtxT *shmMemctxParent, uint64_t maxSysShmSize, uint64_t maxAppShmSize)
{
    DbMemCtxArgsT argsBlock = {0};
    argsBlock.instanceId = shmMemctxParent->instanceId;
    argsBlock.maxTotalAllocSize = maxSysShmSize;
    argsBlock.ctxId = DB_SYS_SHMEMCTX_ID;

    DbBlockMemParamT blockParam = {0};
    blockParam.baseSize = BLOCK_POOL_BASE_SIZE;
    blockParam.stepSize = BLOCK_POOL_STEP_SIZE;
    blockParam.maxSize = BLOCK_POOL_MAX_SIZE;
    blockParam.blkPoolType = BLK_NORMAL;

    AlgoParamT algoParam = {.blockParam = &blockParam};
    argsBlock.algoParam = &algoParam;

    // Create SYS shmem context.
    // Create SYS shmem group for memory threshold constraint.
    uint64_t sysGroupId = DB_INVALID_UINT64;
    if (DbInitShmemGroup(SYS_SHM_GROUP_ID, maxSysShmSize, "GMDB SysShm Group", &sysGroupId) != GMERR_OK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    argsBlock.groupId = sysGroupId;
    DbMemCtxT *sysShmCtx = (DbMemCtxT *)DbCreateBlockPoolShmemCtx(shmMemctxParent, "SysShmContext", &argsBlock);
    if (sysShmCtx == NULL) {
        DB_LOG_INFO("Inv create SysShmContext.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    // Create APP shmem context.
    argsBlock.maxTotalAllocSize = maxAppShmSize;
    argsBlock.ctxId = DB_APP_SHMEMCTX_ID;
    // Euler shmem group pre-distributes shmem threshold, so App is assigned to root shmem group.
    argsBlock.groupId = shmMemctxParent->groupId;
    DbMemCtxT *appShmCtx = (DbMemCtxT *)DbCreateBlockPoolShmemCtx(shmMemctxParent, "AppShmContext", &argsBlock);
    if (appShmCtx == NULL) {
        DB_LOG_INFO("Inv create AppShmContext.");
        DbDeleteShmemCtx(sysShmCtx);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    dbIns->sysShmMemCtx = sysShmCtx;
    dbIns->appShmMemCtx = appShmCtx;
    return GMERR_OK;
}

Status DbSrvInitSysAppShmCtx(DbInstanceHdT dbInstance, DbMemCtxT *shmMemctxParent)
{
    DbInstanceT *dbIns = (DbInstanceT *)dbInstance;
    return CreateSysAppShmCtx(dbIns, shmMemctxParent, UINT64_MAX, UINT64_MAX);
}

uint64_t DbSrvGetMaxSysDynSize(uint16_t instanceId)
{
    DB_ASSERT(false);
    return 0;
}

uint64_t DbSrvGetMaxEscapeMemCtxSize(void)
{
    DB_ASSERT(false);
    return 0;
}

void DbSrvSetMaxEscapeMemCtxSize(uint64_t size)
{}

uint64_t DbSrvGetMaxAppDynSize(uint16_t instanceId)
{
    DB_ASSERT(false);
    return 0;
}

uint64_t DbSrvGetMaxSysShmSize(void)
{
    DB_ASSERT(false);
    return 0;
}

uint64_t DbSrvGetMaxAppShmSize(DbInstanceHdT dbInstance)
{
    DB_ASSERT(false);
    return 0;
}
