/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: db_mini_utils.c
 * Description: Implement of db common utils functions
 * Author: minikv
 * Create: 2024-01-20
 */

#include "adpt_types.h"
#include "adpt_process_id.h"
#include "db_utils.h"
#if (defined(__aarch64__) && !defined(HPE)) || defined(HPE_SIMULATION)
#include <arm_acle.h>
#endif

Status DbMakeProcessBackground(void)
{
    int32_t pid = DbAdptFork();
    if (pid == 0) {
        return DbAdptSetpgid(0, 0);
    } else if (pid < 0) {
        return GMERR_INTERNAL_ERROR;
    } else {
        exit(0);
    }
}

void DbRightTrim(char *text)
{
    if (text == NULL) {
        return;
    }
    uint32_t len;
    uint8_t *tmpBuf = (uint8_t *)text;

    len = (uint32_t)strlen(text);
    if (len == 0) {
        return;
    }
    tmpBuf = tmpBuf + (len - 1);
    while ((tmpBuf >= (uint8_t *)text) && (*tmpBuf <= ' ')) {
        tmpBuf--;
    }
    tmpBuf[1] = '\0';
}

char *DbLeftTrim(char *text)
{
    if (text == NULL) {
        return NULL;
    }
    uint8_t *tmpBuf = (uint8_t *)text;

    while ((*tmpBuf != '\0') && (*tmpBuf <= ' ')) {
        tmpBuf++;
    }
    return (char *)tmpBuf;
}

static uint32_t CRC32Inner(char *data, uint32_t length, uint32_t crcInput)
{
    uint32_t len = length;
    uint32_t crc = crcInput;
#if defined(__aarch64__) && !defined(HPE)
    while (len >= sizeof(uint8_t)) {
        crc = __crc32b(crc, *((uint8_t *)data));
        data += sizeof(uint8_t);
        len -= sizeof(uint8_t);
    }
#else
    int k;

    while (len--) {
        // Ensure char is converted to uint8_t to process non-ascii values correctly
        crc ^= (uint8_t)*data++;
        for (k = 0; k < CHAR_BIT; k++) {
            crc = (crc >> 1) ^ (0xEDB88320 & (0 - (crc & 1)));
        }
    }
#endif
    return crc;
}

uint32_t DbCRC32(char *data, uint32_t length)
{
    uint32_t crc = CRC32Inner(data, length, (uint32_t)DB_MAX_UINT32);
    return ~crc;
}

static uint32_t CRC32InnerLegacy(char *data, uint32_t length, uint32_t crcInput)
{
    uint32_t crc = crcInput;
    uint32_t strLen = length;
    char *str = data;
    while (strLen-- != 0) {
        // A conversion error happens here in the legacy version
        // crc will be overwritten completely instead of the 8 rightmost bits
        int32_t temp = (int32_t)(*str);
        crc ^= (uint32_t)temp;  // crc ^= *str; str++;
        str += 1;
        for (uint32_t i = 0; i < (uint32_t)BYTE_LENGTH; ++i) {
            if ((crc & 1) != 0) {
                crc = (crc >> 1) ^ (uint32_t)0xEDB88320;  // 0xEDB88320= reverse 0x04C11DB7
            } else {
                crc = (crc >> 1);
            }
        }
    }
    return crc;
}

uint32_t DbCRC32Legacy(char *data, uint32_t length)
{
    uint32_t crc = CRC32InnerLegacy(data, length, (uint32_t)DB_MAX_UINT32);
    return ~crc;
}
