/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: db_mini_string.c
 * Description: implement of db mini string
 * Author:
 * Create: 2023-12-14
 */

#include "adpt_string.h"
#include <stdlib.h>
#include <string.h>
#include "adpt_types.h"

// UTF8 encoding fast match table
// 0   - 127 0xxxxxxx UTF_8 1 byte
// 128 - 191 10xxxxxx UTF_8 inner record code, use "0u" to indicate it
// 192 - 223 110xxxxx UTF_8 2 byte
// 224 - 239 1110xxxx UTF_8 3 byte
// 240 - 247 11110xxx UTF_8 4 byte
// 248 - 251 111110xx UTF_8 5 byte (invalid)
// 252 - 255 1111110x UTF_8 6 byte (invalid)
static const uint8_t UTF8_TRAILING_BYTE[256] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
    2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 6, 6, 6, 6};

#define UTF8_CONTINUATION_BYTE_CHARACTER 0u
#define UTF8_ONE_BYTE_CHARACTER 1u
#define UTF8_TWO_BYTE_CHARACTER 2u
#define UTF8_THREE_BYTE_CHARACTER 3u
#define UTF8_FOUR_BYTE_CHARACTER 4u

Status DbStrToInt64(const char *str, int64_t *val)
{
    DB_POINTER2(str, val);

    char *strEnd = NULL;
    errno = 0;  // clear errno before convert
    const uint32_t base = 10;
    int64_t value = strtoll(str, &strEnd, (int)base);

    if ((errno != 0) ||      // conversion mistake
        (strEnd == str) ||   // no valid character interpreted
        ((*strEnd) != '\0')  // not null terminated after last valid character
    ) {
        return GMERR_DATA_EXCEPTION;
    }

    *val = value;
    return GMERR_OK;
}

Status DbStrToInt32(const char *str, int32_t *val)
{
    DB_POINTER2(str, val);

    int64_t int64Value = 0;
    Status ret = DbStrToInt64(str, &int64Value);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (int64Value > DB_MAX_INT32 || int64Value < DB_MIN_INT32) {
        return GMERR_DATATYPE_MISMATCH;
    }

    *val = (int32_t)int64Value;
    return GMERR_OK;
}

Status DbStrToUint32(const char *str, uint32_t *val)
{
    DB_POINTER2(str, val);

    int64_t int64Value = 0;
    Status ret = DbStrToInt64(str, &int64Value);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (int64Value > DB_MAX_UINT32 || int64Value < 0) {
        return GMERR_DATATYPE_MISMATCH;
    }

    *val = (uint32_t)int64Value;
    return GMERR_OK;
}

int32_t DbStrCmp(const char *s1, const char *s2, bool caseIns)
{
    DB_POINTER2(s1, s2);
    int ret = caseIns ? strcasecmp(s1, s2) : strcmp(s1, s2);
    if (ret > 0) {
        return 1;
    } else if (ret < 0) {
        return -1;
    }
    return 0;
}

int32_t DbStrFindLastChar(const char *str, uint16_t strLen, const char chr)
{
    DB_POINTER(str);

    for (int32_t i = strLen - 1; i >= 0; i--) {
        if (str[i] == chr) {
            return i;
        }
    }

    return -1;
}

static inline bool CheckInnerByteValid(const char *content, uint32_t index, uint8_t currentByteLength)
{
    for (uint32_t innerCur = index + 1; innerCur < index + currentByteLength; ++innerCur) {
        if (UTF8_TRAILING_BYTE[(uint8_t)content[innerCur]] != UTF8_CONTINUATION_BYTE_CHARACTER) {
            return false;
        }
    }
    return true;
}

bool Utf8CheckValid(const char *content)
{
    if (content == NULL) {
        return false;
    }
    size_t byteIndex = 0u;
    size_t byteLength = strlen(content);
    while (byteIndex < byteLength) {
        size_t currentByteLength = UTF8_TRAILING_BYTE[(uint8_t)(content[byteIndex])];
        if (byteIndex + currentByteLength > byteLength) {
            return false;
        }

        if (currentByteLength == UTF8_ONE_BYTE_CHARACTER) {
            byteIndex += currentByteLength;
            continue;
        }

        if (currentByteLength >= UTF8_TWO_BYTE_CHARACTER && currentByteLength <= UTF8_FOUR_BYTE_CHARACTER) {
            if (!CheckInnerByteValid(content, byteIndex, currentByteLength)) {
                return false;
            }
            byteIndex += currentByteLength;
            continue;
        }
        return false;
    }
    return true;
}

// To ensure the function performance, content must comply with the UTF-8 encoding specifications
int32_t Utf8NextByteIndexGet(const char *content, size_t index)
{
    if (SECUREC_UNLIKELY(content == NULL)) {
        return UTF8_ARGS_INVALID;
    }
    size_t byteLength = strlen(content);
    if (SECUREC_UNLIKELY(byteLength > DB_MAX_INT32)) {
        return UTF8_ARGS_INVALID;
    }
    size_t count = index;
    size_t byteIndex = 0;
    while (count > 0) {
        if (byteIndex >= byteLength) {
            return UTF8_ARGS_INVALID;
        }
        byteIndex += UTF8_TRAILING_BYTE[(uint8_t)(content[byteIndex])];
        count--;
    }
    return (int32_t)byteIndex;
}

// content must comply with the UTF-8 encoding specifications
int32_t Utf8CharacterLengthGet(const char *content)
{
    if (SECUREC_UNLIKELY(content == NULL)) {
        return UTF8_ARGS_INVALID;
    }
    size_t byteLength = strlen(content);
    if (SECUREC_UNLIKELY(byteLength > DB_MAX_INT32)) {
        return UTF8_ARGS_INVALID;
    }
    size_t characterLength = 0;
    size_t byteIndex = 0;
    while (byteIndex < byteLength) {
        byteIndex += UTF8_TRAILING_BYTE[(uint8_t)(content[byteIndex])];
        characterLength++;
    }
    return (int32_t)characterLength;
}
