/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: db_mini_file_lock.c
 * Description: file lock
 * Author:
 * Create: 2024-1-3
 */

#include "db_file_lock.h"

#include <fcntl.h>

#include "db_log.h"
#include "gmc_errno.h"

#define DB_FILE_LOCK_FIXED_REF 1
void FileLockInit(FileLockT *lock, int fd, int start, int len)
{
    DB_POINTER(lock);
    lock->fd = fd;
    lock->start = start;
    lock->len = len;
}

Status FileLockAddLock(FileLockT *lock, FileLockTypeE type)
{
    DB_POINTER(lock);
    struct flock fl;
    fl.l_len = lock->len;
    fl.l_start = lock->start;
    fl.l_type = (short int)type;
    fl.l_whence = SEEK_SET;
    int ret = fcntl(lock->fd, F_SETLKW, &fl);
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_FILE_LOCK_ERROR, "file lock add lock, err_no: %d", errno);
        return GMERR_FILE_LOCK_ERROR;
    }

    return GMERR_OK;
}

Status FileLockTryLock(FileLockT *lock, FileLockTypeE type)
{
    DB_POINTER(lock);
    struct flock fl;
    fl.l_len = lock->len;
    fl.l_start = lock->start;
    fl.l_type = (short int)type;
    fl.l_whence = SEEK_SET;
    int ret = fcntl(lock->fd, F_SETLK, &fl);
    if (ret != 0) {
        if (errno == EACCES || errno == EAGAIN) {
            DB_LOG_WARN(GMERR_FILE_LOCK_BUSY, "file lock busy, err_no: %d", errno);
            return GMERR_FILE_LOCK_BUSY;
        }

        DB_LOG_ERROR(GMERR_FILE_LOCK_ERROR, "file lock try lock, err_no: %d", errno);
        return GMERR_FILE_LOCK_ERROR;
    }

    return GMERR_OK;
}

Status FileLockUnlock(FileLockT *lock)
{
    DB_POINTER(lock);
    struct flock fl;
    fl.l_len = lock->len;
    fl.l_start = lock->start;
    fl.l_type = F_UNLCK;
    fl.l_whence = SEEK_SET;
    int ret = fcntl(lock->fd, F_SETLK, &fl);
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_FILE_LOCK_ERROR, "file lock unlock, err_no: %d", errno);
        return GMERR_FILE_LOCK_ERROR;
    }

    return GMERR_OK;
}

Status FileLockGetLockType(FileLockT *lock, FileLockTypeE *type)
{
    DB_POINTER(lock);
    struct flock fl;
    fl.l_type = F_WRLCK;
    fl.l_len = lock->len;
    fl.l_start = lock->start;
    fl.l_whence = SEEK_SET;
    int ret = fcntl(lock->fd, F_GETLK, &fl);
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_FILE_LOCK_ERROR, "file lock get lock, err_no: %d.", errno);
        return GMERR_FILE_LOCK_ERROR;
    }

    // if fcntl can add lock, it will set l_type to F_UNLCK, else will set current lock info to l_type
    *type = (FileLockTypeE)fl.l_type;
    return GMERR_OK;
}

void MtFileLockInit(MtFileLockT *lock, int fd, int start, int len)
{
    DB_POINTER(lock);
    DB_ASSERT(fd >= 0);
    DB_ASSERT(start >= 0);
    DB_ASSERT(len >= 0);
    lock->fd = fd;
    DbRWSpinInit(&lock->lock);
    DbSpinInit(&lock->mutex);
    lock->start = start;
    lock->len = len;
    lock->shareCount = 0;
}

static Status MtLockFile(int fd, int start, int len, FileLockTypeE type)
{
    struct flock lock;
    lock.l_len = len;
    lock.l_start = start;
    lock.l_type = (short int)type;
    lock.l_whence = SEEK_SET;
    // Two processes are deadlocked if they wait for resources held by each other and are not freed, It will set errno
    // EDEADLK. There is not deadlock when use regular lock because locks on different threads, but lock error too. It
    // should try to lock until fcntl success or errno is not EDEADLK.
    for (;;) {
        if (fcntl(fd, F_SETLKW, &lock) == 0) {
            return GMERR_OK;
        } else if (errno == EDEADLK) {
            continue;
        } else {
            DB_LOG_ERROR(GMERR_FILE_LOCK_ERROR, "multi threads file lock, err_no: %d.", errno);
            return GMERR_FILE_LOCK_ERROR;
        }
    }

    return GMERR_OK;
}

Status MtFileLockRLock(MtFileLockT *lock)
{
    DB_POINTER(lock);
    DbRWSpinRLock(&lock->lock);
    DbSpinLock(&lock->mutex);
    DB_ASSERT(lock->shareCount >= 0);
    if (lock->shareCount == 0) {
        Status ret = MtLockFile(lock->fd, lock->start, lock->len, F_RDLCK);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "multi threads file lock read lock.");
            DbSpinUnlock(&lock->mutex);
            DbRWSpinRUnlock(&lock->lock);
            return ret;
        }
    }

    ++lock->shareCount;
    DbSpinUnlock(&lock->mutex);
    return GMERR_OK;
}

Status MtFileLockWLock(MtFileLockT *lock)
{
    DB_POINTER(lock);
    DbRWSpinWLock(&lock->lock);
    DbSpinLock(&lock->mutex);
    DB_ASSERT(lock->shareCount == 0);
    Status ret = MtLockFile(lock->fd, lock->start, lock->len, F_WRLCK);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "multi threads file lock write lock.");
        DbSpinUnlock(&lock->mutex);
        DbRWSpinWUnlock(&lock->lock);
        return ret;
    }

    DbSpinUnlock(&lock->mutex);
    return GMERR_OK;
}

void MtFileLockRUnlock(MtFileLockT *lock)
{
    DB_POINTER(lock);
    DbSpinLock(&lock->mutex);
    DB_ASSERT(lock->shareCount > 0);
    if (lock->shareCount == DB_FILE_LOCK_FIXED_REF) {
        Status ret = MtLockFile(lock->fd, lock->start, lock->len, F_UNLCK);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "multi threads file lock read unlock.");
        }
    }

    --lock->shareCount;
    DbSpinUnlock(&lock->mutex);
    DbRWSpinRUnlock(&lock->lock);
}

void MtFileLockWUnlock(MtFileLockT *lock)
{
    DB_POINTER(lock);
    DbSpinLock(&lock->mutex);
    DB_ASSERT(lock->shareCount == 0);
    Status ret = MtLockFile(lock->fd, lock->start, lock->len, F_UNLCK);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "multi threads file lock write unlock.");
    }

    DbSpinUnlock(&lock->mutex);
    DbRWSpinWUnlock(&lock->lock);
}
