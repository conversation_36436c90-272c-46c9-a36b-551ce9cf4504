/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: db_mini_alarm.c
 * Description: source file for common mini alarm
 * Author:
 * Create: 2023-12-14
 */

#include "db_alarm.h"

DbAlarmHeadT *DbGetAlarmHead(DbAlarmIdE alarmId)
{
    return NULL;
}

DbAlarmInfoNodeT *DbGetAlarmNode(DbAlarmIdE alarmId, uint32_t userDefRscId)
{
    return NULL;
}

inline void DbAlarmUpdateFailCnt(DbAlarmIdE alarmId, uint32_t addCnt)
{}

inline void DbAlarmUpdateSuccCnt(DbAlarmIdE alarmId, uint32_t addCnt)
{}

inline void DbAlarmUpdateSuccCntByInstanceId(uint32_t instanceId, DbAlarmIdE alarmId, uint32_t addCnt)
{}

inline void DbAlarmUpdateFailCntByInstanceId(uint32_t instanceId, DbAlarmIdE alarmId, uint32_t addCnt)
{}

Status DbCheckGlobalAlarmRes(DbAlarmIdE alarmId, double usedRatio)
{
    return GMERR_OK;
}

void DbModifyAlarmThreshold(DbAlarmIdE alarmId, uint16_t raiseRatio, uint16_t clearRatio)
{}

DbAlarmStatT DbGetAlarmStat(DbAlarmIdE alarmId)
{
    // 小结构, 直接返回只读的副本, 效率也很高
    return (DbAlarmStatT){0, 0};
}
