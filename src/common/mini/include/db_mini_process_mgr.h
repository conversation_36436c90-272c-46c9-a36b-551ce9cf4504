/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: header of db_mini_process_mgr.c.
 * Author:
 * Create: 2024-1-5
 */

#ifndef DB_MINI_PROCESS_MGR_H
#define DB_MINI_PROCESS_MGR_H

#include "adpt_atomic.h"
#include "db_inter_process_mgr.h"
#include "db_file_lock.h"
#include "gmc_errno.h"

#ifdef __cplusplus
extern "C" {
#endif

#define LOCK_BYTE_LEN 1
#define OPENING_LOCK_OFFSET 0
#define PROCESS_LOCK_OFFSET 1
#define PROCESS_RELOADED 1
#define DB_NEED_RECOVER 1
#define WRITE_PROCESS_INDEX 0

typedef struct TagDbInstanceT DbInstanceT;

Status DbInitAndLockOpeningByte(
    const char *dbFilePath, bool isOpenOnly, ProcessInfoMgrT *processInfoMgr, void *topDynMemCtx);

Status DbOpeningByteLock(ProcessInfoMgrT *processInfoMgr);

void DbOpeningByteUnlock(ProcessInfoMgrT *processInfoMgr);

Status DbProcessByteTryLock(ProcessInfoMgrT *processInfoMgr);

Status DbProcessByteUnlock(ProcessInfoMgrT *processInfoMgr);

Status GetProcessExitedState(ProcessInfoMgrT *processInfoMgr, int processId, bool *isExited);

Status GetAllProcessExistState(ProcessInfoMgrT *processInfoMgr, uint32_t processCnt, bool *isExist);

Status DbGetIsFirstBootProcess(ProcessInfoMgrT *processInfoMgr, bool *isFirstBoot);

Status DbGetProcessIndex(void *dbIns, uint8_t *processIdx);

static inline __attribute__((always_inline)) void InitProcessInfo(ProcessInfoT *processInfo)
{
    processInfo->recoverStatus = 0;
    for (int32_t i = 0; i < MAX_PROCESS_COUNT; ++i) {
        processInfo->processCtrls[i].reloadState = 0;
        processInfo->processCtrls[i].txnsCnt = 0;
    }
}

static inline __attribute__((always_inline)) uint32_t ProcessGetReloadStatus(ProcessInfoMgrT *mgr, int processIndex)
{
    DB_ASSERT(processIndex >= 0 && processIndex < MAX_PROCESS_COUNT);
    return DbAtomicGet(&mgr->info->processCtrls[processIndex].reloadState);
}

static inline __attribute__((always_inline)) void ProcessClearReloadStatus(ProcessInfoMgrT *mgr, int processIndex)
{
    DB_ASSERT(processIndex >= 0 && processIndex < MAX_PROCESS_COUNT);
    (void)DbAtomicTAS(&mgr->info->processCtrls[processIndex].reloadState, 0);
}

static inline __attribute__((always_inline)) uint32_t ProcessGetTxnsCount(ProcessInfoMgrT *mgr, int processIndex)
{
    DB_ASSERT(processIndex >= 0 && processIndex < MAX_PROCESS_COUNT);
    return DbAtomicGet(&mgr->info->processCtrls[processIndex].txnsCnt);
}

static inline __attribute__((always_inline)) void ProcessTxnsCountInc(ProcessInfoMgrT *mgr)
{
    if (mgr->info == NULL) {
        return;
    }
    int processIndex = mgr->processId;
    DB_ASSERT(processIndex >= 0 && processIndex < MAX_PROCESS_COUNT);
    DB_ASSERT(mgr->info->processCtrls[processIndex].txnsCnt < UINT32_MAX);
    (void)DbAtomicAdd(&mgr->info->processCtrls[processIndex].txnsCnt, 1);
}

static inline __attribute__((always_inline)) void ProcessTxnsCountDec(ProcessInfoMgrT *mgr)
{
    if (mgr->info == NULL) {
        return;
    }
    int processIndex = mgr->processId;
    DB_ASSERT(processIndex >= 0 && processIndex < MAX_PROCESS_COUNT);
    DB_ASSERT(mgr->info->processCtrls[processIndex].txnsCnt > 0);  // Should never equal to 0 here
    (void)DbAtomicDec(&mgr->info->processCtrls[processIndex].txnsCnt);
}

static inline __attribute__((always_inline)) uint32_t ProcessGetRecoverStatus(ProcessInfoMgrT *mgr)
{
    return mgr->info == NULL ? 0 : DbAtomicGet(&mgr->info->recoverStatus);
}

static inline __attribute__((always_inline)) void ProcessSetRecoverStatus(ProcessInfoMgrT *mgr, uint32_t status)
{
    if (mgr->info == NULL) {
        return;
    }
    (void)DbAtomicTAS(&mgr->info->recoverStatus, status);
}

static inline __attribute__((always_inline)) bool IsReadOnlyProcess(int processIndex)
{
    DB_ASSERT(processIndex < MAX_PROCESS_COUNT);
    return processIndex > OPENING_LOCK_OFFSET;
}

static inline __attribute__((always_inline)) void InitProcessInfoMgr(
    ProcessInfoMgrT *mgr, ProcessInfoT *processInfo, int fileMapFd, int processId)
{
    DB_POINTER(mgr);
    mgr->info = processInfo;
    mgr->fileMapFd = fileMapFd;
    mgr->processId = processId;
}

#ifdef __cplusplus
}
#endif
#endif  // DB_PROCESS_MGR_H
