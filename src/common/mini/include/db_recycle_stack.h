/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: header of db recycle stack.
 * Author: zhaoliang
 * Create: 2024-1-5
 */

#ifndef DB_RECYCLE_STACK_H
#define DB_RECYCLE_STACK_H

#include "adpt_types.h"
#include "db_mem_context.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

typedef struct DbRecycleStackT DbRecycleStackT;

struct DbRecycleStackT {
    DbRWSpinLockT rwLock;
    bool isRecyclable;
    DbMemCtxT *memCtx;
    void **stackItems;
    uint32_t capacity;  // cannot be 0.
    uint32_t useSize;
    uint32_t top;  // The range of top is [0, capacity - 1].
};

Status DbRecycleStackInit(DbRecycleStackT *stack, uint32_t capacity, DbMemCtxT *memCtx, bool isRecyclable);

/**
 * @brief Push item into stack.
 * @param[in] stack Stack struct pointer.
 * @param[in] item Which will be pushed into stack.
 * @param[out] oldItem If recycle stack is full, the replaced item is returned. Otherwise, NULL is returned.
 * @return GMERR_OK if success.
 */
Status DbRecycleStackPush(DbRecycleStackT *stack, void *item, void **oldItem);

/**
 * @brief Pop item from stack.
 * @param[in] stack Stack struct pointer.
 * @return return NULL when stack is empty.
 */
void *DbRecycleStackPop(DbRecycleStackT *stack);

/**
 * @brief Peek item from stack.
 * @param[in] stack Stack struct pointer.
 * @return return NULL when stack is empty.
 */
void *DbRecycleStackPeek(DbRecycleStackT *stack);

bool DbRecycleStackIsEmpty(DbRecycleStackT *stack);

bool DbRecycleStackIsFull(DbRecycleStackT *stack);

void DbRecycleStackDestroy(DbRecycleStackT *stack);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* DB_RECYCLE_STACK_H */
