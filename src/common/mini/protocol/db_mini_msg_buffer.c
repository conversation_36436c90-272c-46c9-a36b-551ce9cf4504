/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: db_mini_msg_buffer.c
 * Description: implement of mini msg buffer
 * Author:
 * Create: 2023-12-22
 */

#include "db_msg_buffer.h"
#include "adpt_memory.h"
#include "db_status.h"
#include "db_text.h"

// Using 33M, include max value size:32M, max key size:1K
#define MSG_MINI_KV_BUFFER_MAX_LENGTH (33 * DB_MEBI)

// MsgBufferHeadT is used internally, not visible externally, only FixBufferT is sensed externally
typedef struct MsgBufferHeadT {
    DbMemTypeE type;    // buffer type: heap memory or shared memory
    uint32_t length;    // Available memory size, not including MsgBufferHeadT
    uint32_t shmctxId;  // shmemCtxId
    uint32_t ref;       // Number of times the buffer has been referenced
    ShmemPtrT shmPtr;   // Shared Memory Offset
} MsgBufferHeadT;

inline static MsgBufferHeadT *MsgBufGetHeader(uint8_t *ptr)
{
    DB_ASSERT(ptr != NULL);
    void *head = ptr - sizeof(MsgBufferHeadT);
    DB_ASSERT(head != NULL);
    return head;
}

static STATUS_OR_PTR(void) MsgBufAlloc(DbMemCtxT *memCtx, uint32_t size)
{
    if (memCtx == NULL) {
        return STATUS_OR_PTR_MAKE_STATUS(void, GMERR_MEMORY_OPERATE_FAILED);
    }

    // External guarantee that size is within a reasonable range.
    // For size 0, consider using FixBufInit
    if (size == 0 || size > MSG_MINI_KV_BUFFER_MAX_LENGTH) {
        return STATUS_OR_PTR_MAKE_STATUS(void, GMERR_MEMORY_OPERATE_FAILED);
    }

    uint32_t allocSize = (uint32_t)sizeof(MsgBufferHeadT) + size;
    uint32_t shmctxId;
    ShmemPtrT shmPtr;
    MsgBufferHeadT *head = NULL;

    shmctxId = DB_INVALID_ID32;
    shmPtr = DB_INVALID_SHMPTR;
    head = DbDynMemCtxAlloc(memCtx, allocSize);
    if (head != NULL) {
        head->type = memCtx->memType;
        head->length = size;
        head->ref = 1;
        head->shmctxId = shmctxId;
        head->shmPtr = shmPtr;
        uint8_t *buf = (uint8_t *)head + sizeof(MsgBufferHeadT);
#ifndef NDEBUG
        /*
         * This was originally set to 0, but it doesn't make sense and affects performance, so it should be removed.
         * Users should not rely on the initial value,
         * the debug version is set to a non-zero value so that problems can be detected in time.
         */
        (void)memset_s(buf, size, 0xff, size);
#endif
        return STATUS_OR_PTR_MAKE_RESULT(void, buf);
    }

    DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "malloc for msg buf.");
    return STATUS_OR_PTR_MAKE_STATUS(void, GMERR_MEMORY_OPERATE_FAILED);
}

static void MsgBufFree(DbMemCtxT *memCtx, uint8_t *buf)
{
    if (SECUREC_UNLIKELY(buf == NULL)) {
        return;
    }

    // Require external assurance that buf is no longer referenced.
    MsgBufferHeadT *head = MsgBufGetHeader(buf);
    DB_ASSERT(head->ref == 0);

    DB_ASSERT(memCtx != NULL);
    DbDynMemCtxFree(memCtx, head);
}

static uint32_t MsgBufDecRef(uint8_t *buf)
{
    if (SECUREC_UNLIKELY(buf == NULL)) {
        /* When buf is NULL, a non-zero value is returned,
        indicating that no free-related operations need to be performed */
        return 1;
    }

    MsgBufferHeadT *head = MsgBufGetHeader(buf);
    uint32_t ref = DbAtomicDec(&head->ref);
    return ref;
}

static uint32_t MsgBufGetSize(uint8_t *buf)
{
    if (SECUREC_UNLIKELY(buf == NULL)) {
        return 0;
    }

    MsgBufferHeadT *head = MsgBufGetHeader(buf);
    return head->length;
}

static void MsgBufFreeEx(DbMemCtxT *memCtx, uint8_t *buf)
{
    uint32_t ref = MsgBufDecRef(buf);
    if (ref == 0) {
        // The ref is 0 before free.
        MsgBufFree(memCtx, buf);
    }
}

Status FixBufCreate(FixBufferT *buffer, DbMemCtxT *memCtx, uint32_t initSize, uint32_t flags)
{
    STATUS_OR_PTR(void) ret = MsgBufAlloc(memCtx, initSize);
    if (ret.status != GMERR_OK) {
        return ret.status;
    }
    FixBufInit(buffer, ret.result, initSize, 0, flags, memCtx);
    return GMERR_OK;
}

void FixBufRelease(FixBufferT *buffer)
{
    DB_POINTER(buffer);
    // The SUB_BUF release is not allowed.
    DB_ASSERT((buffer->flags & FIX_BUF_FLAG_SUB_BUF) == 0);

    MsgBufFreeEx(buffer->memCtx, buffer->buf);
    FixBufZeroOut(buffer);
}

static Status FixBufExtendInner(FixBufferT *buffer, uint32_t newSize)
{
    DB_POINTER(buffer);
    if ((buffer->flags & FIX_BUF_FLAG_SUB_BUF) != 0) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Refuse expand SUB_BUF");
        return GMERR_DATA_EXCEPTION;
    }

    if ((buffer->flags & FIX_BUF_FLAG_EXTEND_BUFFER) == 0) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "fix buf extend flag unset.");
        return GMERR_DATA_EXCEPTION;
    }

    uint8_t *oldBuf = buffer->buf;
    if (oldBuf != NULL) {
        uint32_t oldSize = MsgBufGetSize(oldBuf);
        if (oldSize >= newSize) {
            // It is space enough to reinitialize.
            buffer->totalLength = newSize;
            return GMERR_OK;
        }
    }

    DbMemCtxT *memCtx = buffer->memCtx;
    STATUS_OR_PTR(void) ret = MsgBufAlloc(memCtx, newSize);
    if (ret.status != GMERR_OK) {
        DB_LOG_ERROR(ret.status, "msg buf alloc.");
        return ret.status;
    }

    uint8_t *newBuf = ret.result;
    if (newSize < buffer->pos) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    Status status = GMERR_OK;
    if (buffer->pos > 0) {
        // Copy the used part of the oldBuf, the caller has ensured that the newBuf can hold the oldBuf.
        errno_t errNo = memcpy_s(newBuf, newSize, oldBuf, buffer->pos);
        if (SECUREC_UNLIKELY(errNo != EOK)) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "set dm value: Memcpy.");
            status = GMERR_MEMORY_OPERATE_FAILED;
        }
    }

    buffer->buf = newBuf;
    MsgBufFreeEx(memCtx, oldBuf);

    buffer->totalLength = newSize;
    return status;
}

static Status FixBufGrow(FixBufferT *buffer, uint32_t available, uint64_t required64)
{
    DB_POINTER(buffer);

    // REQUIRED excess the maximum length limit and may not even be in the uint32 range.
    if (SECUREC_UNLIKELY(required64 > MSG_MINI_KV_BUFFER_MAX_LENGTH)) {
        DB_LOG_ERROR(GMERR_PROGRAM_LIMIT_EXCEEDED, "required size exceed msg buf limits.");
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }

    // REQUIRED in the uint32 range, but needs to be expanded, calculate the expanded size and expand it.
    uint32_t required = (uint32_t)required64;
    uint32_t current = available;
    if (current == 0) {
        current = CS_PACK_SIZE;
    }

    // calc new size. if current is not zero,
    // we can find x, which (current << x) >= required
    while (current < required) {
        current = current << 1;
    }

    // required <= current <= MSG_MINI_KV_BUFFER_MAX_LENGTH
    current = DB_MIN(current, MSG_MINI_KV_BUFFER_MAX_LENGTH);

    // extend to new size
    return FixBufExtendInner(buffer, current);
}

inline static uint64_t FixBufSizeAlign(uint64_t size)
{
    // size needs to be memory aligned, to handle the bounds correctly use uint64 to save the result.
    const uint64_t align = 4;
    return ((size + (align - 1)) / align) * align;
}

static STATUS_OR(uint32_t) FixBufReserveDataOffsetInner(FixBufferT *buffer, uint64_t size)
{
    DB_POINTER(buffer);
    /* Generally there is no such usage, here assert in order to find possible problems,
    and if turn it off, it does not affect the correctness. */

    // This function is the unified entry point for write operations, where legality and alignment are checked.
    if (size == 0 || buffer->seekPos > buffer->pos || buffer->pos > buffer->totalLength ||
        buffer->pos != SIZE_ALIGN4(buffer->pos)) {
        return STATUS_OR_MAKE_STATUS(uint32_t, GMERR_MEMORY_OPERATE_FAILED);
    }

    uint32_t offset = buffer->pos;
    uint64_t required = offset + size;
    uint32_t available = buffer->totalLength;

    if (SECUREC_UNLIKELY(required > available)) {
        Status ret = FixBufGrow(buffer, available, required);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return STATUS_OR_MAKE_STATUS(uint32_t, ret);
        }
    }

    // Successful expand indicate that REQUIRED within uint32.
    buffer->pos = (uint32_t)required;
    return STATUS_OR_MAKE_RESULT(uint32_t, offset);
}

inline static STATUS_OR_PTR(void) FixBufReserveDataInner(FixBufferT *buffer, uint64_t size)
{
    STATUS_OR_PTR(void) res;
    STATUS_OR(uint32_t) ret = FixBufReserveDataOffsetInner(buffer, size);
    res.status = ret.status;
    if (SECUREC_LIKELY(ret.status == GMERR_OK)) {
        uint32_t offset = ret.result;
        res.result = FixBufOffsetToAddr(buffer, offset);
    }
    return res;
}

Status FixBufPutData(FixBufferT *buffer, const void *data, uint32_t size)
{
    DB_POINTER2(buffer, data);

    STATUS_OR_PTR(void) ret = FixBufReserveDataInner(buffer, FixBufSizeAlign(size));
    if (ret.status != GMERR_OK) {
        DB_LOG_ERROR(ret.status, "fix buf reserve data.");
        return ret.status;
    }

    errno_t errNo = memcpy_s(ret.result, size, data, size);
    if (errNo != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Inv memcpy_s.");
    }
    return errNo == EOK ? GMERR_OK : GMERR_MEMORY_OPERATE_FAILED;
}

inline static STATUS_OR_PTR(void) FixBufPeekOrGetData(FixBufferT *buffer, uint64_t size, bool peek)
{
    DB_POINTER(buffer);
    // This function is the unified entry point for read operations, where legality and alignment are checked.
    if (buffer->seekPos > buffer->pos || buffer->pos > buffer->totalLength ||
        buffer->seekPos != SIZE_ALIGN4(buffer->seekPos)) {
        return STATUS_OR_PTR_MAKE_STATUS(void, GMERR_MEMORY_OPERATE_FAILED);
    }

    uint32_t offset = buffer->seekPos;
    uint64_t required = offset + size;
    uint32_t available = buffer->pos;

    // Not enough data to read, REQUIRED may not even be in the uint32 range.
    if (SECUREC_UNLIKELY(required > available)) {
        return STATUS_OR_PTR_MAKE_STATUS(void, GMERR_NO_DATA);
    }

    if (!peek) {
        // Update read pointer, REQUIRED is in the uint32 range.
        buffer->seekPos = (uint32_t)required;
    }

    void *addr = FixBufOffsetToAddr(buffer, offset);
    DB_UNUSED(addr);  // Solving abnormal warning problems.
    return STATUS_OR_PTR_MAKE_RESULT(void, addr);
}

inline static STATUS_OR_PTR(void) FixBufPeekData(FixBufferT *buffer, uint64_t size)
{
    return FixBufPeekOrGetData(buffer, size, true);
}

inline static STATUS_OR_PTR(void) FixBufGetDataInner(FixBufferT *buffer, uint64_t size)
{
    return FixBufPeekOrGetData(buffer, size, false);
}

void *FixBufGetData(FixBufferT *buffer, uint32_t size)
{
    STATUS_OR_PTR(void) ret = FixBufGetDataInner(buffer, FixBufSizeAlign(size));
    return (ret.status == GMERR_OK) ? ret.result : NULL;
}

inline Status FixBufPutUint32(FixBufferT *buffer, uint32_t value)
{
    STATUS_OR_PTR(void) ret = FixBufReserveDataInner(buffer, sizeof(uint32_t));
    if (ret.status != GMERR_OK) {
        DB_LOG_ERROR(ret.status, "fix buf reserve data.");
        return ret.status;
    }

    uint32_t *addr = ret.result;
    *addr = value;
    return GMERR_OK;
}

static Status FixBufPutLengthDelimited(FixBufferT *buffer, const char *dataStr, uint32_t dataLen)
{
    DB_POINTER(dataStr);
    uint32_t size = dataLen;
    const void *data = dataStr;

    uint64_t total = (uint64_t)sizeof(uint32_t) + (uint64_t)size;

    STATUS_OR_PTR(void) ret = FixBufReserveDataInner(buffer, FixBufSizeAlign(total));
    if (SECUREC_UNLIKELY(ret.status != GMERR_OK)) {
        return ret.status;
    }

    uint32_t *addr = ret.result;
    *addr = size;

    if (SECUREC_LIKELY(size > 0)) {
        void *dest = addr + 1;  // Skip a uint32_t
        errno_t errNo = memcpy_s(dest, size, data, size);
        if (errNo != EOK) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Inv memcpy_s.");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    }

    return GMERR_OK;
}

static Status FixBufGetLengthDelimited(
    FixBufferT *buffer, TextT *content, bool (*verify)(uint32_t size, const uint8_t *data))
{
    DB_POINTER2(content, verify);
    STATUS_OR_PTR(void) ret = FixBufPeekData(buffer, sizeof(uint32_t));
    if (SECUREC_UNLIKELY(ret.status != GMERR_OK)) {
        return ret.status;
    }

    uint32_t *addr = ret.result;
    uint32_t size = *addr;
    void *data = addr + 1;  // Skip a uint32_t
    uint64_t total = (uint64_t)sizeof(uint32_t) + (uint64_t)size;

    ret = FixBufGetDataInner(buffer, FixBufSizeAlign(total));
    if (SECUREC_UNLIKELY(ret.status != GMERR_OK)) {
        return ret.status;
    }

    // Expected to get the same addr before and after.
    if (SECUREC_UNLIKELY(addr != ret.result)) {
        return GMERR_DATA_EXCEPTION;
    }

    if (SECUREC_UNLIKELY(!verify(size, data))) {
        return GMERR_DATA_EXCEPTION;
    }

    content->len = size;
    content->str = data;
    return GMERR_OK;
}

Status FixBufPutRawText(FixBufferT *buffer, uint32_t len, const void *data)
{
    return FixBufPutLengthDelimited(buffer, data, len);
}

bool VerifyObject(uint32_t size, const uint8_t *data)
{
    // should not be empty
    if (SECUREC_UNLIKELY(size == 0)) {
        return false;
    }

    DB_UNUSED(data);
    return true;
}

bool VerifyObjectNullable(uint32_t size, const uint8_t *data)
{
    // can be empty
    if (size == 0) {
        return true;
    }

    return VerifyObject(size, data);
}

Status FixBufGetObject(FixBufferT *buffer, TextT *text)
{
    return FixBufGetLengthDelimited(buffer, text, VerifyObject);
}

Status FixBufGetObjectNullable(FixBufferT *buffer, TextT *text)
{
    return FixBufGetLengthDelimited(buffer, text, VerifyObjectNullable);
}

bool VerifyTextEx(uint32_t size, const uint8_t *data)
{
    // should not be empty
    if (SECUREC_UNLIKELY(size == 0)) {
        return false;
    }

    // Check exactly to make sure it matches size = strlen + 1
    const void *str = data;
    return size == strnlen(str, size) + 1;
}

bool VerifyTextExNullable(uint32_t size, const uint8_t *data)
{
    // can be empty
    if (size == 0) {
        return true;
    }

    return VerifyTextEx(size, data);
}

Status FixBufGetTextExNullable(FixBufferT *buffer, TextT *text)
{
    return FixBufGetLengthDelimited(buffer, text, VerifyTextExNullable);
}

Status FixBufGetText(FixBufferT *buffer, TextT *text)
{
    return FixBufGetLengthDelimited(buffer, text, VerifyTextEx);
}
