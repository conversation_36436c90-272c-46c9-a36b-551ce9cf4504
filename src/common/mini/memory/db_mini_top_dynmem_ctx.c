/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: db_mini_top_dynmem_ctx.c
 * Description: for common mini dynamic memory top context
 * Author:
 * Create: 2023-12-20
 */
#include <securec.h>
#include "db_mini_dynmem_algo.h"
#include "db_mini_mem_context_internal.h"
#include "db_config.h"
#include "db_instance.h"

// 这两个全局变量malloc free的时候使用，是关键路径，不要封装
uint64_t g_gmdbMaxDynSize = DB_MAX_MEMCTX_TOTAL_PHY_SIZE;  // 系统配置动态内存上限，unit为字节。
uint64_t g_gmdbCurrUsedDynSize = 0;                        // 系统当前通过malloc分配的内存，unit为字节。

uint64_t DbGetCurrDynSize(void)
{
    return DbAtomicGet64(&g_gmdbCurrUsedDynSize);
}

DbTopDynMemCtxT *DbGetTopDynMemCtx(DbInstanceHdT dbInstance)
{
    DB_ASSERT(false);
    return NULL;
}

static Status TopMemCtxMemberInit(DbMemCtxT *ctx, DbInstanceT *dbIns)
{
    Status ret = MemCtxMemberInit(ctx, true, dbIns, DB_DYNAMIC_MEMORY, "Top Dynamic Memory Context");
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)SetMemCtxId(ctx, 0);
    ctx->level = 0;
    ctx->isTopMemCtx = true;
    ctx->maxTotalPhySize = DB_MAX_MEMCTX_TOTAL_PHY_SIZE;
    ctx->errorCode = GMERR_OK;
    ctx->noLimited = true;
    return GMERR_OK;
}

Status DbInitTopDynMemCtx(DbInstanceHdT dbInstance)
{
    Status ret = InitDynMemCtxStatInfo();
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to init DynMemCtx statInfo.");
        return ret;
    }
    DbTopDynMemCtxT *topMemCtx = (DbTopDynMemCtxT *)DB_MALLOC(sizeof(DbTopDynMemCtxT));
    (void)memset_s(topMemCtx, sizeof(DbTopDynMemCtxT), 0, sizeof(DbTopDynMemCtxT));
    ret = TopMemCtxMemberInit((DbMemCtxT *)topMemCtx, dbInstance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Top dynmemCtx init mistake");
        return ret;
    }
    (void)DynamicAlgoInit((DbMemCtxT *)topMemCtx, NULL);
    topMemCtx->dynamicAlgo.needRecycle = true;
    topMemCtx->status = DB_MEMCTX_INITED;
    ((DbInstanceT *)dbInstance)->topDynMemCtx = topMemCtx;
#ifndef NDEBUG
    ((DbMemCtxT *)topMemCtx)->ctxId = InsertCtxToDynCtxArr((DbMemCtxT *)topMemCtx);
#endif
    return ret;
}

void DbDestroyTopDynMemCtx(DbInstanceHdT dbInstance)
{
    DbInstanceT *dbIns = (DbInstanceT *)dbInstance;
    // 依赖topDynMemCtx兜底释放场景，强制释放所有内存
    DeleteDynMemCtx((DbMemCtxT *)dbIns->topDynMemCtx, true);
    DB_FREE(dbIns->topDynMemCtx);
    dbIns->topDynMemCtx = NULL;
}

// 外部调用者对g_gmdbMemCtxLock加锁，防止查询统计信息的时候，ctx被删除。
static void DynMemCtxGetTreePhySize(DbMemCtxT *ctx, uint64_t *size)
{
    if (ctx == NULL || ctx->magicCode != DB_MAGIC_CODE || ctx->memType != DB_DYNAMIC_MEMORY) {
        return;
    }
    // dynCtx在删除操作跟查询上下文树并发的时候有加锁，其他操作未加锁，需要原子操作。
    *size += DbAtomicGet64(&((DbDynamicMemCtxT *)ctx)->dynamicAlgo.totalPhySize);
    DbMemCtxT *nextChild = ctx->dynCtxPtrs.head;
    while (nextChild != NULL) {
        DynMemCtxGetTreePhySize(nextChild, size);
        nextChild = nextChild->dynCtxPtrs.next;
    }
}

// 接口内已对g_gmdbMemCtxLock加锁。
// 在memCtx树形结构中，获取自此memCtx往下的所有memCtx节点的totalPhySize之和（包括本memCtx）
uint64_t DbDynMemCtxGetTreePhySize(DbMemCtxT *ctx, bool globalLock)
{
    Status ret = CheckDynMemCtxValid(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Unable to get dynCtx tree phySize. Ctx inv.");
        return 0;
    }
    if (globalLock) {
        DbMemCtxGlobalLock();
    }
    uint64_t size = 0;
    DynMemCtxGetTreePhySize(ctx, &size);
    if (globalLock) {
        DbMemCtxGlobalUnlock();
    }
    return size;
}

// 外部调用者对g_gmdbMemCtxLock加锁，防止查询统计信息的时候，ctx被删除。
// 在memCtx树形结构中，获取自此memCtx往下的所有memCtx节点的totalAllocSize之和（包括本memCtx）
static void DynMemCtxGetTreeAllocSize(DbMemCtxT *ctx, uint64_t *size)
{
    if (ctx == NULL || ctx->magicCode != DB_MAGIC_CODE || ctx->memType != DB_DYNAMIC_MEMORY) {
        return;
    }
    // 非线程共享的dynCtx，在删除操作跟查询上下文树并发的时候有加锁，其他操作未加锁，需要原子操作。
    *size += DbAtomicGet64(&(ctx->totalAllocSize));
    DbMemCtxT *nextChild = ctx->dynCtxPtrs.head;
    while (nextChild != NULL) {
        DynMemCtxGetTreeAllocSize(nextChild, size);
        nextChild = nextChild->dynCtxPtrs.next;
    }
}

// 获取以当前节点为父节点的动态内存上下文申请内存总大小
uint64_t DbDynMemCtxGetTreeAllocSize(DbMemCtxT *ctx, bool globalLock)
{
    if (SECUREC_UNLIKELY(CheckDynMemCtxValid(ctx) != GMERR_OK)) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Unable to get dynMemCtx tree allocSize.");
        return 0;
    }
    if (ctx->collectAllocSizeOnThisTree) {
        return DbAtomicGet64(&ctx->totalAllocSizeOnThisTree);
    }
    uint64_t size = 0;
    if (globalLock) {
        DbMemCtxGlobalLock();
    }
    DynMemCtxGetTreeAllocSize(ctx, &size);
    if (globalLock) {
        DbMemCtxGlobalUnlock();
    }
    return size;
}
