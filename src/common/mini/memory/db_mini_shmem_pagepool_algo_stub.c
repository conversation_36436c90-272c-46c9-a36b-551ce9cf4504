/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: db_mini_shmem_pagepool_algo_stub.c
 * Description: stub source file for common shared memory page pool algorithm
 * Author: minikv
 * Create: 2024-01-23
 */

#include "db_mem_context.h"
#include "db_mini_shmem_pagepool_algo.h"

void *DbCreatePagePoolShmemCtx(DbMemCtxT *parent, const char *ctxName, DbMemCtxArgsT *args)
{
    DB_UNUSED(parent);
    DB_UNUSED(ctxName);
    DB_UNUSED(args);
    return NULL;
}
