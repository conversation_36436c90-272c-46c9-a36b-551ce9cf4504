/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: db_instance.c
 * Description: Used to manage instances
 * Create: 2024-12-9
 */

#include "adpt_string.h"
#include "db_config.h"
#include "db_config_file.h"
#include "adpt_init.h"
#include "db_instance.h"
#include "sysview.h"
#include "db_top_shmem_ctx.h"
#include "adpt_process_lock.h"

#define MAX_DB_INSTANCE_IDX ((1 << 8) - 1)
#define MAX_DB_INSTANCE_NUM (128)
#define DB_INSTANCE_IDX_BIT (8)

typedef bool (*InstanceElementCompFunc)(DbInstanceT *node, char *key);

static DbInstanceMgrT g_dbInstanceMgr = {0};
static DbSpinLockT g_instanceGlobalLock = DB_SPINLOCK_INIT_VAL;
static DbMultiInstanceSwitchE g_gmdbMultiInstanceSwitch = DB_MULTI_INSTANCE_SWITCH_DEFAULT;

inline DbMultiInstanceSwitchE DbGetMultiInstanceSwitch(void)
{
    return g_gmdbMultiInstanceSwitch;
}

void DbSetMultiInstanceSwitch(DbMultiInstanceSwitchE status)
{
    g_gmdbMultiInstanceSwitch = status;
}

bool DbIsMultiInstanceEnabled(void)
{
    return DbGetMultiInstanceSwitch() == DB_MULTI_INSTANCE_SWITCH_ON;
}

bool DbNeedGlobalFinalize(DbInstanceT *instance)
{
    if (DbIsMultiInstanceEnabled()) {
#ifdef IDS_HAOTIAN
        return g_dbInstanceMgr.globalInstance->instanceId == instance->instanceId;
#else
        return false;
#endif
    }
    return true;
}

const char *DbGetDataFileDirRealPath(DbCfgMgrT *cfgMgr)
{
    const DbCfgItemT *cfgItem = &cfgMgr->cfgItems[DB_CFG_DATA_FILE_DIR_PATH];
    return cfgItem->value.str;
}

static uint32_t GetInstanceIdx(uint32_t instanceId)
{
    return instanceId & MAX_DB_INSTANCE_IDX;
}

static bool InstanceComparePath(DbInstanceT *node, char *path)
{
    const char *dataFileDirPath = DbGetDataFileDirRealPath(node->cfgMgr);
    if (dataFileDirPath == NULL) {
        DB_LOG_ERROR(
            GMERR_UNEXPECTED_NULL_VALUE, "Obtain data file path from existed instance %u unsucc", node->instanceId);
        DB_ASSERT(false);
        return false;
    }
    return DbStrCmp(dataFileDirPath, path, true) == 0;
}

static inline uint16_t InstanceArraySize(uint16_t extraArraySz)
{
    return (extraArraySz + DB_DEFAULT_INSTANCE_CNT);
}

static inline bool IsInstanceIdValid(DbInstanceMgrT *mgr, uint16_t instanceId)
{
    return (instanceId > 0 && instanceId <= InstanceArraySize(mgr->extraInsSize));
}

static DbInstanceT *GetInstanceById(uint32_t instanceId)
{
    if (instanceId <= MAX_DB_INSTANCE_NUM) {
        return g_dbInstanceMgr.globalInstance;
    }
    uint32_t instanceIdx = GetInstanceIdx(instanceId);

    if (g_dbInstanceMgr.instanceCnt == 0) {
        return NULL;
    } else if (instanceIdx == 0 || instanceIdx > g_dbInstanceMgr.maxInstanceId) {
        DB_LOG_ERROR(
            GMERR_INTERNAL_ERROR, "Get instance id %u go wrong, maxId %u.", instanceIdx, g_dbInstanceMgr.maxInstanceId);
        DB_ASSERT(false);
        return NULL;
    }
    if (instanceIdx <= DB_DEFAULT_INSTANCE_CNT) {
        return g_dbInstanceMgr.instances[instanceIdx - 1];
    }
    return g_dbInstanceMgr.extraInstances[instanceIdx - DB_DEFAULT_INSTANCE_CNT - 1];
}

static DbInstanceT *FindInstanceByCompare(void *key, InstanceElementCompFunc func)
{
    if (g_dbInstanceMgr.instanceCnt == 0) {
        return NULL;
    }
    for (uint16_t i = 0; i < DB_DEFAULT_INSTANCE_CNT; i++) {
        if (g_dbInstanceMgr.instances[i] == NULL) {
            continue;
        }
        bool isEqual = func(g_dbInstanceMgr.instances[i], key);
        if (isEqual) {
            return g_dbInstanceMgr.instances[i];
        }
    }
    if (g_dbInstanceMgr.extraInsSize == 0 || g_dbInstanceMgr.extraInstances == NULL) {
        return NULL;
    }
    for (uint16_t i = 0; i < g_dbInstanceMgr.extraInsSize; i++) {
        if (g_dbInstanceMgr.extraInstances[i] == NULL) {
            continue;
        }
        bool isEqual = func(g_dbInstanceMgr.extraInstances[i], key);
        if (isEqual) {
            return g_dbInstanceMgr.extraInstances[i];
        }
    }
    return NULL;
}

static Status EnlargeInstanceMgr(DbInstanceMgrT *mgr, uint16_t maxInstanceId)
{
    if (maxInstanceId == 0 || maxInstanceId > MAX_DB_INSTANCE_NUM) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "InstanceId %u is not allowed.", maxInstanceId);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (maxInstanceId <= InstanceArraySize(mgr->extraInsSize)) {
        return GMERR_OK;
    }
    // 16 is instances buffer extend step
    uint16_t arrayCnt = (uint16_t)DB_CALC_ALIGN(maxInstanceId - 1, DB_DEFAULT_INSTANCE_CNT);
    uint32_t arraySize = arrayCnt * sizeof(DbInstanceT *);
    DbInstanceT **extraInstances = (DbInstanceT **)DB_MALLOC(arraySize);
    if (extraInstances == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc instance array go wrong, size %u.", arraySize);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(extraInstances, arraySize, 0, arraySize);
    if (mgr->extraInstances != NULL) {
        errno_t err =
            memcpy_s(extraInstances, arraySize, mgr->extraInstances, mgr->extraInsSize * sizeof(DbInstanceT *));
        if (err != EOK) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
                "Copy from old instances go wrong new insSize %u, old insSize %u.", arraySize, mgr->extraInsSize);
            DB_FREE(extraInstances);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        DB_FREE(mgr->extraInstances);
    }
    // 此数组在最后一个实例关闭时释放
    mgr->extraInstances = extraInstances;
    mgr->extraInsSize = arrayCnt;
    return GMERR_OK;
}

static Status AllocInstanceIdx(DbInstanceMgrT *mgr, uint16_t *insId)
{
    if (mgr->instanceCnt > MAX_DB_INSTANCE_NUM) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "InstanceCnt reach limit.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    Status ret = EnlargeInstanceMgr(mgr, mgr->instanceCnt + 1);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Alloc instance id %u go wrong.", mgr->instanceCnt + 1);
        return ret;
    }
    for (uint16_t i = 0; i < DB_DEFAULT_INSTANCE_CNT; i++) {
        if (mgr->instances[i] == NULL) {
            *insId = i + 1;
            return GMERR_OK;
        }
    }
    for (uint16_t i = 0; i < mgr->extraInsSize && mgr->extraInstances != NULL; i++) {
        if (mgr->extraInstances[i] == NULL) {
            *insId = DB_DEFAULT_INSTANCE_CNT + i + 1;
            return GMERR_OK;
        }
    }
    DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Found valid instance go wrong extra size %u.", mgr->extraInsSize);
    return GMERR_INTERNAL_ERROR;
}

static Status AllocInstanceId(DbCfgMgrT *cfg, uint16_t *dbInsId)
{
    uint16_t insIdPre = 0;
    uint16_t insIdSuf = 0;

    Status ret = AllocInstanceIdx(&g_dbInstanceMgr, &insIdSuf);
    if (ret != GMERR_OK) {
        DB_BOOT_LOG_ERROR(ret, "Alloc instance unsucc.");
        return ret;
    }

    insIdPre = DbGetProcGlobalId();

    *dbInsId = (uint16_t)((insIdPre << DB_INSTANCE_IDX_BIT) | insIdSuf);
    return GMERR_OK;
}

static Status SetInstanceUsed(DbInstanceMgrT *mgr, uint16_t instanceId, DbInstanceT *instance)
{
    uint32_t instanceIdx = GetInstanceIdx(instanceId);
    if (!IsInstanceIdValid(mgr, (uint16_t)instanceIdx) || instance == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Add instance instanceId %u extraInsSize %u is not allowed.",
            instanceId, mgr->extraInsSize);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DbInstanceT **instanceBuf = mgr->instances;

    if (instanceIdx > DB_DEFAULT_INSTANCE_CNT) {
        instanceBuf = mgr->extraInstances;
        instanceIdx -= DB_DEFAULT_INSTANCE_CNT;
    }
    if (instanceBuf[instanceIdx - 1] != NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Add %s instance index %u already set.",
            instanceIdx > DB_DEFAULT_INSTANCE_CNT ? "extra" : "", instanceBuf[instanceIdx - 1]->instanceId);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    mgr->instanceCnt++;
    instanceBuf[instanceIdx - 1] = instance;
    mgr->maxInstanceId = (uint16_t)DB_MAX(mgr->maxInstanceId, instanceIdx);
    return GMERR_OK;
}

static void SetInstanceUnused(DbInstanceMgrT *mgr, uint16_t instanceId)
{
    if (mgr->globalInstance->instanceId == instanceId) {
        return;
    }
    uint32_t instanceIdx = GetInstanceIdx((uint32_t)instanceId);
    if (!IsInstanceIdValid(mgr, (uint16_t)instanceIdx) || mgr->instanceCnt == 0) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE,
            "Remove instanceId %u instanceCnt %u extraInsSize %u is not allowed.", mgr->instanceCnt, instanceIdx,
            mgr->extraInsSize);
        return;
    }
    DbInstanceT **instanceBuf = mgr->instances;
    if (instanceIdx > DB_DEFAULT_INSTANCE_CNT) {
        instanceBuf = mgr->extraInstances;
        instanceIdx -= DB_DEFAULT_INSTANCE_CNT;
    }
    if (instanceBuf[instanceIdx - 1] == NULL) {
        DB_LOG_WARN(GMERR_INTERNAL_ERROR, "%s instanceId %u has been removed.",
            instanceIdx > DB_DEFAULT_INSTANCE_CNT ? "extra" : "", instanceIdx);
        return;
    }
    instanceBuf[instanceIdx - 1] = NULL;
    mgr->instanceCnt--;
    if (mgr->instanceCnt == 0) {
        DB_FREE(mgr->extraInstances);
        mgr->extraInstances = NULL;
        mgr->extraInsSize = 0;
        DB_LOG_INFO("release last db instance in process succ.");
    }
}

Status GetInstanceByConfig(DbCfgMgrT *cfg, DbInstanceT **dbIns)
{
    Status ret = GMERR_OK;
    char path[DB_MULTIZONE_MAX_PATH] = {0};

    ret = DbCfgGetDirPath(cfg, DB_MULTIZONE_MAX_PATH, path);
    if (ret != GMERR_OK) {
        DB_BOOT_LOG_ERROR(ret, "Cannot get config file path");
        return ret;
    }

    *dbIns = FindInstanceByCompare(path, InstanceComparePath);
    return ret;
}

void DbInstanceGlobalLock(void)
{
    DbSpinLock(&g_instanceGlobalLock);
    return;
}

void DbInstanceGlobalUnLock(void)
{
    DbSpinUnlock(&g_instanceGlobalLock);
    return;
}

Status DbInstanceMgrInit(DbCommonInitCfgT *initCfg)
{
    DbSetMultiInstanceSwitch(DB_MULTI_INSTANCE_SWITCH_ON);
    DbSetServerThreadFlag();

    if (g_dbInstanceMgr.isGlobalInit) {
        return GMERR_OK;
    }

    Status ret = DbCommonGlobalInit(initCfg);
    if (ret != GMERR_OK) {
        return ret;
    }

    DbRWLatchInit(&g_dbInstanceMgr.latch);
    g_dbInstanceMgr.maxInstanceId = MAX_DB_INSTANCE_NUM;

    g_dbInstanceMgr.isGlobalInit = true;
    return GMERR_OK;
}

static void FreeDbInstance(DbInstanceT **instance)
{
    DbInstanceT *dbIns = *instance;

    if (dbIns->cfgMgr != NULL) {
        DB_FREE(dbIns->cfgMgr);
    }

    if (dbIns->svIns != NULL) {
        DB_FREE(dbIns->svIns);
    }

    if (dbIns->topShmMemEntry != NULL) {
        DB_FREE(dbIns->topShmMemEntry);
    }

    DB_FREE(dbIns);
    *instance = NULL;
}

static Status AllocDbInstance(DbInstanceT **instance)
{
    DbInstanceT *dbIns = NULL;

    dbIns = (DbInstanceT *)DB_MALLOC(sizeof(DbInstanceT));
    if (dbIns == NULL) {  // LCOV_EXCL_BR_LINE
        DB_BOOT_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "worthless to malloc db instance");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(dbIns, sizeof(DbInstanceT), 0, sizeof(DbInstanceT));
    *instance = dbIns;

    dbIns->cfgMgr = (DbCfgMgrT *)DB_MALLOC(sizeof(DbCfgMgrT));
    if (dbIns->cfgMgr == NULL) {  // LCOV_EXCL_BR_LINE
        DB_BOOT_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Cannot allocate memory for config manager");
        FreeDbInstance(instance);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(dbIns->cfgMgr, sizeof(DbCfgMgrT), 0, sizeof(DbCfgMgrT));

    dbIns->svIns = (SvInstanceT *)DB_MALLOC(sizeof(SvInstanceT));
    if (dbIns->svIns == NULL) {  // LCOV_EXCL_BR_LINE
        DB_BOOT_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Cannot allocate memory for sv instance");
        FreeDbInstance(instance);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(dbIns->svIns, sizeof(SvInstanceT), 0, sizeof(SvInstanceT));

    dbIns->topShmMemEntry = (DbTopShmemCtxEntryT *)DB_MALLOC(sizeof(DbTopShmemCtxEntryT));
    if (dbIns->topShmMemEntry == NULL) {  // LCOV_EXCL_BR_LINE
        DB_BOOT_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Cannot allocate memory for topShmMemEntry");
        FreeDbInstance(instance);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(dbIns->topShmMemEntry, sizeof(DbTopShmemCtxEntryT), 0, sizeof(DbTopShmemCtxEntryT));
    return GMERR_OK;
}

static void FreeDbInstanceAndUnLatch(DbInstanceT **instance)
{
    FreeDbInstance(instance);
    DbRWUnlatchW(&g_dbInstanceMgr.latch);
}

static Status LatchAndAllocDbInstance(DbInstanceT **instance)
{
    DbRWLatchW(&g_dbInstanceMgr.latch);

    Status ret = AllocDbInstance(instance);
    if (ret != GMERR_OK) {
        DbRWUnlatchW(&g_dbInstanceMgr.latch);
    }

    return ret;
}

#ifdef IDS_HAOTIAN
// 目前最长为durablememdata，带'\0'为14个字节
#define FEATURE_NAME_MAX_LEN 16
#define FEATURE_NAMES_CFG_MAX_LEN 512
#define FEATURE_NAME_ARRAY_MAX_COUNT (FEATURE_NAMES_CFG_MAX_LEN / FEATURE_NAME_MAX_LEN)

static Status DbGetFeatureNameArray(
    DbCfgMgrT *cfgMgr, char featuresArray[][FEATURE_NAME_MAX_LEN], uint32_t arrayMaxLen, uint32_t *featureNum)
{
    DbCfgValueT featureCfg = {0};
    Status ret = DbCfgGet(cfgMgr, DB_CFG_FEATURE_NAMES, &featureCfg);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get cfg: featureNames");
        return ret;
    }
    DbStrToLower(featureCfg.str);

    uint32_t cnt = 0;
    uint32_t len = (uint32_t)strlen(featureCfg.str) + 1;
    char *buf = (char *)DB_MALLOC(len);
    if (buf == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc buffer(size %u)", len);
        return GMERR_OUT_OF_MEMORY;
    }
    // 此处不检查返回值，因为buf是按照features的长度申请的，这里strcpy_s不会失败
    (void)strcpy_s(buf, len, featureCfg.str);
    char *pCurrent = NULL;
    char *nextToken = NULL;
    pCurrent = strtok_s(buf, ",", &nextToken);
    while (pCurrent) {
        errno_t retTmp = strncpy_s(featuresArray[cnt], FEATURE_NAME_MAX_LEN, pCurrent, FEATURE_NAME_MAX_LEN - 1);
        if (retTmp != EOK) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "split featureNames");
            DB_FREE(buf);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        cnt++;
        if (cnt > arrayMaxLen) {
            DB_FREE(buf);
            return GMERR_FIELD_OVERFLOW;
        }
        pCurrent = strtok_s(NULL, ",", &nextToken);
    }
    DB_FREE(buf);
    *featureNum = cnt;
    return GMERR_OK;
}

static bool DbIsFeatureNameArrayEqual(char newFeatures[][FEATURE_NAME_MAX_LEN], uint32_t newFeaturesLen,
    char globalFeatures[][FEATURE_NAME_MAX_LEN], uint32_t globalFeaturesLen)
{
    if (newFeaturesLen != globalFeaturesLen) {
        return false;
    }
    uint32_t matchCnt = 0;
    for (uint32_t i = 0; i < newFeaturesLen; i++) {
        for (uint32_t j = 0; j < globalFeaturesLen; j++) {
            if (strcmp(newFeatures[i], globalFeatures[j]) == 0) {
                matchCnt++;
                break;
            }
        }
    }
    if (matchCnt != newFeaturesLen) {
        return false;
    }
    return true;
}

static Status DbCheckFeatureConsistWithGlobal(DbCfgMgrT *globalCfgMgr, DbCfgMgrT *newCfgMgr)
{
    DB_POINTER2(globalCfgMgr, newCfgMgr);
    uint32_t newFeatureNum = 0;
    char newFeatures[FEATURE_NAME_ARRAY_MAX_COUNT][FEATURE_NAME_MAX_LEN] = {0};
    Status ret = DbGetFeatureNameArray(newCfgMgr, newFeatures, FEATURE_NAME_ARRAY_MAX_COUNT, &newFeatureNum);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t globalFeatureNum = 0;
    char globalFeatures[FEATURE_NAME_ARRAY_MAX_COUNT][FEATURE_NAME_MAX_LEN] = {0};
    ret = DbGetFeatureNameArray(globalCfgMgr, globalFeatures, FEATURE_NAME_ARRAY_MAX_COUNT, &globalFeatureNum);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (!DbIsFeatureNameArrayEqual(newFeatures, newFeatureNum, globalFeatures, globalFeatureNum)) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "featureNames not consist with global");
        return GMERR_CONFIG_ERROR;
    }
    return GMERR_OK;
}

Status DbNeedEqualCfgCheck(DbCfgMgrT *newCfgMgr)
{
    DB_POINTER(newCfgMgr);
    DbCfgEmItemIdE needEqCfgTable[] = {DB_CFG_PERSISTENT_MODE, DB_CFG_FEATURE_NAMES, DB_CFG_SE_GET_INSTANCE_ID,
        DB_CFG_LOG_LENGTH_MAX, DB_CFG_LOG_FILE_NUM_MAX, DB_CFG_LOG_FILE_SIZE_MAX, DB_CFG_ENABLE_LOG_FOLD,
        DB_CFG_LOG_FOLD_RULE, DB_CFG_SERVER_TOTAL_SHM_SIZE, DB_CFG_SERVER_TOTAL_DYN_SIZE,
        DB_CFG_MEM_PEAK_DFX_THREASHOLD, DB_CFG_LITE_DYN_MEM_MOD, DB_CFG_FILE_LOCK_PATH,
        DB_CFG_LATCH_DEADLOCK_DEBUG_TIMEOUT, DB_CFG_MAX_SORT_BUFFER_SIZE, DB_CFG_SHMEM_PERMISSION,
        DB_CFG_DEV_SHMEM_PERMISSION, DB_CFG_FEATURE_LIB_PATH, DB_CFG_VERTICAL_ISOLATION_ENABLE};
    uint32_t needEqCfgNum = sizeof(needEqCfgTable) / sizeof(DbCfgEmItemIdE);
    DbInstanceT *globalInst = g_dbInstanceMgr.globalInstance;
    DbCfgMgrT *globalCfgMgr = DbGetCfgHandle(globalInst);
    Status ret = GMERR_OK;

    for (uint32_t i = 0; i < needEqCfgNum; i++) {
        DbCfgEmItemIdE cfg = needEqCfgTable[i];
        if (cfg == DB_CFG_FEATURE_NAMES) {
            // 对于FeatureNames，需要单独判断两个配置项中的每个Feature是否一一对应
            ret = DbCheckFeatureConsistWithGlobal(globalCfgMgr, newCfgMgr);
            if (ret != GMERR_OK) {
                return ret;
            }
            continue;
        }
        if (!DbCfgItemEquals(&globalCfgMgr->cfgItems[cfg], &newCfgMgr->cfgItems[cfg])) {
            DB_LOG_ERROR(
                GMERR_CONFIG_ERROR, "cfg (cfg Id:%" PRId32 ") not consist with global", globalCfgMgr->cfgItems[cfg].id);
            return GMERR_CONFIG_ERROR;
        }
    }
    return GMERR_OK;
}
#endif

Status InitializeConfigAndLock(DbInstanceT *dbIns, const char *cfgParameter)
{
    Status ret = DbInitConfigFile(cfgParameter, (DbCfgMgrT **)&dbIns->cfgMgr);
    if (ret != GMERR_OK) {
        DB_BOOT_LOG_ERROR(ret, "Cannot init config file");
        return ret;
    }

    DbCfgValueT locatorCfg;
    if (DbCfgGet(dbIns->cfgMgr, DB_CFG_FILE_LOCK_PATH, &locatorCfg) == GMERR_OK) {
        ret = DbSetFilePathLock(locatorCfg.str);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

#ifdef IDS_HAOTIAN
    ret = DbNeedEqualCfgCheck((DbCfgMgrT *)dbIns->cfgMgr);
    if (ret != GMERR_OK) {
        return ret;
    }
#endif

    return GMERR_OK;
}

Status AllocateAndInitializeInstance(DbInstanceT *dbIns)
{
    uint16_t instanceId = 0;
    Status ret = AllocInstanceId(dbIns->cfgMgr, &instanceId);
    if (ret != GMERR_OK) {
        DB_BOOT_LOG_ERROR(ret, "Alloc instance unsucc.");
        return ret;
    }

    DbRWLatchInit(&dbIns->latch);
    dbIns->instanceId = (uint32_t)instanceId;
    dbIns->status = DB_INSTANCE_OFFLINE;
    dbIns->refCnt = 1;
    dbIns->recoveryState = RECOVERY_BUTT;

    ret = SetInstanceUsed(&g_dbInstanceMgr, (uint16_t)dbIns->instanceId, dbIns);
    if (ret != GMERR_OK) {
        DB_BOOT_LOG_ERROR(ret, "Set instance used unsucc.");
        return ret;
    }

    return GMERR_OK;
}

Status DbGetOrAllocInstance(const char *cfgParameter, DbInstanceT **instance)
{
    DbInstanceT *dbIns = NULL;
    *instance = NULL;

    Status ret = LatchAndAllocDbInstance(&dbIns);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = InitializeConfigAndLock(dbIns, cfgParameter);
    if (ret != GMERR_OK) {
        FreeDbInstanceAndUnLatch(&dbIns);
        return ret;
    }

    ret = GetInstanceByConfig(dbIns->cfgMgr, instance);
    if (ret != GMERR_OK) {
        FreeDbInstanceAndUnLatch(&dbIns);
        return ret;
    }
    if (*instance != NULL) {
        (*instance)->refCnt++;
        FreeDbInstanceAndUnLatch(&dbIns);
        return GMERR_OK;
    }

    ret = AllocateAndInitializeInstance(dbIns);
    if (ret != GMERR_OK) {
        FreeDbInstanceAndUnLatch(&dbIns);
        return ret;
    }
    *instance = dbIns;

    DbRWUnlatchW(&g_dbInstanceMgr.latch);
    return GMERR_OK;
}

Status DbGetOrAllocGlobalInstance(const char *cfgParameter, DbInstanceT **instance)
{
    Status ret = GMERR_OK;
    DbInstanceT *dbIns = NULL;
    *instance = NULL;
    DbCfgValueT cfgValue = {0};

    if (g_dbInstanceMgr.globalInstance != NULL && g_dbInstanceMgr.globalInstance->status != DB_INSTANCE_OFFLINE) {
        *instance = g_dbInstanceMgr.globalInstance;
        return GMERR_OK;
    }

    ret = LatchAndAllocDbInstance(&dbIns);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = DbInitConfigFile(cfgParameter, (DbCfgMgrT **)&dbIns->cfgMgr);
    if (ret != GMERR_OK) {
        DB_BOOT_LOG_ERROR(ret, "Cannot init config file");
        FreeDbInstanceAndUnLatch(&dbIns);
        return ret;
    }

    DbCfgValueT locatorCfg;
    if (DbCfgGet(dbIns->cfgMgr, DB_CFG_FILE_LOCK_PATH, &locatorCfg) == GMERR_OK) {
        ret = DbSetFilePathLock(locatorCfg.str);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    DbRWLatchInit(&dbIns->latch);
    dbIns->status = DB_INSTANCE_OFFLINE;
    dbIns->refCnt = 1;
    dbIns->recoveryState = RECOVERY_BUTT;
    DbSetCfgHandle(dbIns->cfgMgr);

    ret = DbCfgGet(dbIns->cfgMgr, DB_CFG_SE_GET_INSTANCE_ID, &cfgValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to get cfg value.");
        FreeDbInstanceAndUnLatch(&dbIns);
        return ret;
    }
    dbIns->instanceId = cfgValue.int32Val;

    *instance = dbIns;
    g_dbInstanceMgr.globalInstance = dbIns;

    DbRWUnlatchW(&g_dbInstanceMgr.latch);
    return GMERR_OK;
}

Status DbGetInstanceById(uint32_t instanceId, DbInstanceT **instance)
{
    if (!DbIsMultiInstanceEnabled()) {
        return GMERR_OK;
    }

    DbRWLatchW(&g_dbInstanceMgr.latch);
    DbInstanceT *dbIns = GetInstanceById(instanceId);
    if (dbIns == NULL) {
        DB_LOG_ERROR(GMERR_NO_DATA, "Cannot get Instance by id %u", instanceId);
        DbRWUnlatchW(&g_dbInstanceMgr.latch);
        return GMERR_NO_DATA;
    }
    dbIns->refCnt = dbIns->refCnt + 1;
    DbRWUnlatchW(&g_dbInstanceMgr.latch);
    *instance = dbIns;
    return GMERR_OK;
}

Status DbGetInstanceByIdNolock(uint32_t instanceId, DbInstanceT **instance)
{
    if (!DbIsMultiInstanceEnabled()) {
        return GMERR_OK;
    }

    DbInstanceT *dbIns = GetInstanceById(instanceId);
    if (dbIns == NULL) {
        DB_LOG_ERROR(GMERR_NO_DATA, "Cannot get Instance by id %u", instanceId);
        return GMERR_NO_DATA;
    }
    *instance = dbIns;
    return GMERR_OK;
}

DbInstanceT *DbGetGlobalInstance(void)
{
    if (!DbIsMultiInstanceEnabled()) {
        return NULL;
    }

    DbRWLatchW(&g_dbInstanceMgr.latch);
    DbInstanceT *dbIns = g_dbInstanceMgr.globalInstance;
    if (dbIns == NULL) {
        DB_LOG_ERROR(GMERR_NO_DATA, "Cannot get globalInstance.");
    }

    DbRWUnlatchW(&g_dbInstanceMgr.latch);
    return dbIns;
}

DbInstanceMgrT *DbGetInstanceMgr(void)
{
    return &g_dbInstanceMgr;
}

void DbReleaseInstance(DbInstanceT *instance)
{
    if (!DbIsMultiInstanceEnabled()) {
        return;
    }

    DbRWLatchW(&g_dbInstanceMgr.latch);
    if (instance->refCnt == 0) {
        DbRWUnlatchW(&g_dbInstanceMgr.latch);
        return;
    }
    instance->refCnt = instance->refCnt - 1;
    if (instance->refCnt == 0 && instance->status == DB_INSTANCE_OFFLINE) {
        SetInstanceUnused(&g_dbInstanceMgr, (uint16_t)instance->instanceId);
        FreeDbInstance(&instance);
        if (g_dbInstanceMgr.globalInstance == instance) {
            g_dbInstanceMgr.globalInstance = NULL;
        }
    }
    DbRWUnlatchW(&g_dbInstanceMgr.latch);
}
