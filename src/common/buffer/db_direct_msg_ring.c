/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 * File Name: db_direct_msg_ring.c
 * Description: direct server share massage ring impl
 * Author: <PERSON><PERSON><PERSON>
 * Create: 2023-12-25
 */

#include "adpt_sleep.h"
#include "adpt_pipe.h"
#include "db_internal_error.h"
#include "db_direct_msg_ring.h"

#ifdef __cplusplus
extern "C" {
#endif

#define DIRECT_MSG_POOL_WAIT_TIME_US 100000
#define DIRECT_MSG_RING_MAX_COUNT (DIRECT_MSG_RING_MAX_ARRAY_NUM * DIRECT_MSG_RING_BASE_COUNT)

static uint32_t GetArrayIdxByPos(uint32_t pos)
{
    return pos / DIRECT_MSG_RING_BASE_COUNT;
}

static uint32_t GetMsgIdxInArrayByPos(uint32_t pos)
{
    return pos % DIRECT_MSG_RING_BASE_COUNT;
}

Status DirectMsgRingCreate(DbMemCtxT *memCtx, DirectMsgRingT **directMsgRing)
{
    DB_POINTER2(memCtx, directMsgRing);
    ShmemPtrT ringMgrPtr = DbShmemCtxAlloc(memCtx, sizeof(DirectMsgRingT));
    DirectMsgRingT *newRing = DbShmPtrToAddr(ringMgrPtr);
    if (newRing == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "DW msgRing mgr, segId: %" PRIu32 ", offset: %" PRIu32 ".",
            ringMgrPtr.segId, ringMgrPtr.offset);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(newRing, sizeof(DirectMsgRingT), 0, sizeof(DirectMsgRingT));

    ShmemPtrT ringBufPtr = DbShmemCtxAlloc(memCtx, sizeof(DirectMsgRingBuffT));
    DirectMsgRingBuffT *buff = DbShmPtrToAddr(ringBufPtr);
    if (buff == NULL) {
        DbShmemCtxFree(memCtx, ringMgrPtr);
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "DW msgRing buf, segId: %" PRIu32 ", offset: %" PRIu32 ".",
            ringMgrPtr.segId, ringMgrPtr.offset);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(buff, sizeof(DirectMsgRingBuffT), 0xFF, sizeof(DirectMsgRingBuffT));
    buff->currNum = 0;

    for (uint32_t i = 0; i < DIRECT_MSG_RING_MAX_ARRAY_NUM; i++) {
        newRing->msgArrays[i] = DB_INVALID_SHMPTR;
    }
    newRing->selfPtr = ringMgrPtr;
    newRing->pos = 0;
    newRing->usedNum = 0;
    newRing->totalNum = DIRECT_MSG_RING_BASE_COUNT;
    newRing->msgArrays[0] = ringBufPtr;
    newRing->ctxId = memCtx->ctxId;
    DbSpinInit(&newRing->ringLock);
    *directMsgRing = newRing;
    return GMERR_OK;
}

inline void DirectMsgRingDestroy(DbMemCtxT *memCtx, DirectMsgRingT *directMsgRing)
{
    DB_POINTER2(memCtx, directMsgRing);
    for (uint32_t i = 0; i < DIRECT_MSG_RING_MAX_ARRAY_NUM; i++) {
        if (DbIsShmPtrValid(directMsgRing->msgArrays[i])) {
            DbShmemCtxFree(memCtx, directMsgRing->msgArrays[i]);
        }
    }
    DbShmemCtxFree(memCtx, directMsgRing->selfPtr);
}

static Status DirectMsgRingExpand(DirectMsgRingT *directMsgRing, uint32_t arrIdx)
{
    DB_POINTER(directMsgRing);
    Status ret = GMERR_OK;
    // 直连写客户端已经提前刷过g_gmdbInstanceId，且当前只支持实例ID等于1。
    DbMemCtxT *memCtx = DbGetShmemCtxById(directMsgRing->ctxId, DbGetProcGlobalId());
    if (SECUREC_UNLIKELY(memCtx == NULL)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "DW memCtx expand msgRing, ctxId: %" PRIu32 ", instanceId: %" PRIu32 ".", directMsgRing->ctxId,
            DbGetProcGlobalId());
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    ShmemPtrT newRingBufPtr = DbShmemCtxAlloc(memCtx, sizeof(DirectMsgRingBuffT));
    DirectMsgRingBuffT *newRingBuf = DbShmPtrToAddr(newRingBufPtr);
    if (SECUREC_UNLIKELY(newRingBuf == NULL)) {
        // 如已有扩展，认为成功，还能继续写入。
        DB_LOG_WARN(GMERR_OUT_OF_MEMORY, "DW expand msgRing.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(newRingBuf, sizeof(DirectMsgRingBuffT), 0xFF, sizeof(DirectMsgRingBuffT));
    newRingBuf->currNum = 0;
    directMsgRing->msgArrays[arrIdx] = newRingBufPtr;
    directMsgRing->totalNum += DIRECT_MSG_RING_BASE_COUNT;
    return ret;
}

static Status DirectMsgRingShrink(DirectMsgRingT *directMsgRing, DirectMsgRingBuffT *msgArr, uint32_t arrIdx)
{
    DB_POINTER2(directMsgRing, msgArr);
    Status ret = GMERR_OK;
    if (msgArr->currNum == 0) {
        // 同一个单元还能继续存消息。
        if (GetArrayIdxByPos(directMsgRing->pos) == arrIdx) {
            return ret;
        }
        DbMemCtxT *memCtx = DbGetShmemCtxById(directMsgRing->ctxId, DbGetProcGlobalId());
        if (SECUREC_UNLIKELY(memCtx == NULL)) {
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        DbShmemCtxFree(memCtx, directMsgRing->msgArrays[arrIdx]);
        directMsgRing->msgArrays[arrIdx] = DB_INVALID_SHMPTR;
        directMsgRing->totalNum -= DIRECT_MSG_RING_BASE_COUNT;
    }
    return ret;
}

Status DirectMsgRingAdvanceMsg(DirectMsgRingT *directMsgRing, ShmemPtrT data)
{
    Status ret = GMERR_OK;
    DbSpinLock(&directMsgRing->ringLock);
    if (directMsgRing->usedNum >= DIRECT_MSG_RING_MAX_COUNT) {
        DbSpinUnlock(&directMsgRing->ringLock);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    uint32_t arrIdx = GetArrayIdxByPos(directMsgRing->pos);
    if (!DbIsShmPtrValid(directMsgRing->msgArrays[arrIdx])) {
        if (SECUREC_UNLIKELY((ret = DirectMsgRingExpand(directMsgRing, arrIdx)) != GMERR_OK)) {
            DbSpinUnlock(&directMsgRing->ringLock);
            return ret;
        }
    }
    DirectMsgRingBuffT *msgArr = DbShmPtrToAddr(directMsgRing->msgArrays[arrIdx]);
    if (SECUREC_UNLIKELY(msgArr == NULL)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "DW push msg, segId: %" PRIu32 ", offset: %" PRIu32 ".",
            directMsgRing->msgArrays[arrIdx].segId, directMsgRing->msgArrays[arrIdx].offset);
        DbSpinUnlock(&directMsgRing->ringLock);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    uint32_t msgIdx = GetMsgIdxInArrayByPos(directMsgRing->pos);
    msgArr->msg[msgIdx] = data;
    msgArr->currNum++;
    directMsgRing->usedNum++;
    directMsgRing->pos++;
    directMsgRing->pos =
        (directMsgRing->pos >= (DIRECT_MSG_RING_MAX_ARRAY_NUM * DIRECT_MSG_RING_BASE_COUNT)) ? 0 : directMsgRing->pos;

    DbSpinUnlock(&directMsgRing->ringLock);
    return ret;
}

Status DirectMsgRingConsumeMsg(DirectMsgRingT *directMsgRing, ShmemPtrT *data)
{
    DbSpinLock(&directMsgRing->ringLock);
    if (directMsgRing->usedNum == 0) {
        DbSpinUnlock(&directMsgRing->ringLock);
        return GMERR_NO_DATA;
    }
    DB_ASSERT(directMsgRing->pos < DIRECT_MSG_RING_MAX_COUNT);
    uint32_t tailPos =
        (DIRECT_MSG_RING_MAX_COUNT + directMsgRing->pos - directMsgRing->usedNum) % DIRECT_MSG_RING_MAX_COUNT;
    uint32_t arrIdx = GetArrayIdxByPos(tailPos);
    uint32_t msgIdx = GetMsgIdxInArrayByPos(tailPos);

    DirectMsgRingBuffT *msgArr = DbShmPtrToAddr(directMsgRing->msgArrays[arrIdx]);
    if (SECUREC_UNLIKELY(msgArr == NULL)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "DW consume msg, segId: %" PRIu32 ", offset: %" PRIu32 ".",
            directMsgRing->msgArrays[arrIdx].segId, directMsgRing->msgArrays[arrIdx].offset);
        DbSpinUnlock(&directMsgRing->ringLock);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    *data = msgArr->msg[msgIdx];
    if (DbIsShmPtrValid(msgArr->msg[msgIdx])) {
        msgArr->msg[msgIdx] = DB_INVALID_SHMPTR;
        msgArr->currNum--;
    }
    directMsgRing->usedNum--;
    DirectMsgRingShrink(directMsgRing, msgArr, arrIdx);
    DbSpinUnlock(&directMsgRing->ringLock);
    return GMERR_OK;
}

Status DirectMsgRingRead(DirectMsgRingT *directMsgRing, ShmemPtrT *data)
{
    if (!DbSpinTimedLock(&directMsgRing->ringLock, DIRECT_MSG_POOL_WAIT_TIME_US)) {
        // Unable to read ring msg, lock is unavailable.
        DB_LOG_ERROR(GMERR_LOCK_NOT_AVAILABLE, "read directSubMsg.");
        return GMERR_LOCK_NOT_AVAILABLE;
    }
    if (directMsgRing->usedNum == 0) {
        DbSpinUnlock(&directMsgRing->ringLock);
        return GMERR_NO_DATA;
    }
    uint32_t tailPos =
        ((DIRECT_MSG_RING_MAX_ARRAY_NUM * DIRECT_MSG_RING_BASE_COUNT) + directMsgRing->pos - directMsgRing->usedNum) %
        (DIRECT_MSG_RING_MAX_ARRAY_NUM * DIRECT_MSG_RING_BASE_COUNT);
    uint32_t arrIdx = GetArrayIdxByPos(tailPos);
    uint32_t msgIdx = GetMsgIdxInArrayByPos(tailPos);
    DirectMsgRingBuffT *msgArr = DbShmPtrToAddr(directMsgRing->msgArrays[arrIdx]);
    if (SECUREC_UNLIKELY(msgArr == NULL)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "DW read msg, segId: %" PRIu32 ", offset: %" PRIu32 ".",
            directMsgRing->msgArrays[arrIdx].segId, directMsgRing->msgArrays[arrIdx].offset);
        DbSpinUnlock(&directMsgRing->ringLock);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    *data = msgArr->msg[msgIdx];
    DbSpinUnlock(&directMsgRing->ringLock);
    return GMERR_OK;
}

/************************************** 状态合并订阅相关接口 **************************************/
static void DwStmgMapArgsInit(DbShmOamapInitArgT *args, DbMemCtxT *memCtx)
{
    args->capacity = DIRECT_STMG_MAP_COUNT;
    args->memCtx = memCtx;
    args->extendable = true;
    args->fixedSizeDeepExtend = true;
    args->keySize = (uint32_t)sizeof(DirectStmgDataT);
    args->valueSize = 0u;
}

ShmemPtrT DwStmgMapCreate(DbMemCtxT *memCtx)
{
    ShmemPtrT stmgMapPtr = DbShmemCtxAlloc(memCtx, sizeof(DbShmOamapT));
    DbShmOamapT *stmgMap = DbShmPtrToAddr(stmgMapPtr);
    if (SECUREC_UNLIKELY(stmgMap == NULL)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Trans dw stmgMap.");
        DbShmemCtxFree(memCtx, stmgMapPtr);
        return DB_INVALID_SHMPTR;
    }
    DbShmOamapInitArgT args = {0};
    DwStmgMapArgsInit(&args, memCtx);
    Status ret = DbShmOamapInit(stmgMap, &args);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Init dw stmgMap.");
        DbShmemCtxFree(memCtx, stmgMapPtr);
        return DB_INVALID_SHMPTR;
    }
    return stmgMapPtr;
}

void DwStmgMapDestroy(DbShmOamapT *stmgMap)
{
    DbShmOamapUninit(stmgMap);
    DbMemCtxT *memCtx = DbShmPtrToAddr(stmgMap->memCtxPtr);
    if (SECUREC_UNLIKELY(memCtx == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Destroy stmgMap get memCtx.");
        return;
    }
    DbShmemCtxFree(memCtx, stmgMap->selfPtr);
}

inline static uint32_t DwStmgDataToHash(const DirectStmgDataT *data)
{
    uint32_t keyBufLen = (uint32_t)sizeof(DirectStmgDataT);
    return DbHash32((const uint8_t *)data, keyBufLen);
}

static void DwStmgDataKeyCopy(const void *srcKey, void *destKey)
{
    const DirectStmgDataT *stmgSrcKey = (const DirectStmgDataT *)srcKey;
    DirectStmgDataT *stmgDestKey = (DirectStmgDataT *)destKey;
    stmgDestKey->type = stmgSrcKey->type;
    stmgDestKey->vertexLabelId = stmgSrcKey->vertexLabelId;
}

static uint32_t DwStmgDataKeyCompare(const void *key1, const void *key2)
{
    const DirectStmgDataT *stmgKey1 = (const DirectStmgDataT *)key1;
    const DirectStmgDataT *stmgKey2 = (const DirectStmgDataT *)key2;
    return stmgKey1->type == stmgKey2->type && stmgKey1->vertexLabelId == stmgKey2->vertexLabelId;
}

void DwStmgDataValueCopy(const void *srcValue, void *destValue)
{
    DB_UNUSED2(srcValue, destValue);
    return;
}

Status DwStmgMapConsumeMsg(DbShmOamapT *stmgMap, uint8_t *data)
{
    DbRWSpinWLock(&stmgMap->rwLock);
    ShmemPtrT msgPtr, valPtr;
    uint32_t iter = 0;
    Status ret = DbShmOamapFetch(stmgMap, &iter, &msgPtr, &valPtr);
    if (ret != GMERR_OK) {
        DbRWSpinWUnlock(&stmgMap->rwLock);
        return ret;
    }
    DirectStmgDataT *msg = DbShmPtrToAddr(msgPtr);
    if (SECUREC_UNLIKELY(msg == NULL)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Consume dw stmgMap inv.");
        DbRWSpinWUnlock(&stmgMap->rwLock);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    // data缓冲区的填充需要和服务端解析订阅消息的逻辑保持一致。
    uint8_t *bufCorsor = data;
    *(uint8_t *)bufCorsor = msg->type;
    bufCorsor += sizeof(uint8_t);
    (void)memcpy_s(bufCorsor, sizeof(uint32_t), &msg->vertexLabelId, sizeof(uint32_t));
    DbShmOamapRemoveByIdx(stmgMap, iter - 1);
    DbRWSpinWUnlock(&stmgMap->rwLock);
    return GMERR_OK;
}

Status DwStmgMapWriteMsg(DbShmOamapT *stmgMap, uint8_t type, uint32_t vertexLabelId)
{
    DirectStmgDataT data = {
        .type = type,
        .vertexLabelId = vertexLabelId,
    };
    uint32_t hash = DwStmgDataToHash(&data);
    void *value = NULL;
    // 查找map中是否存在消息，如果存在，不再重复插入。
    DbRWSpinWLock(&stmgMap->rwLock);
    Status ret = DbShmOamapLookup(stmgMap, (DbShmOamapFindParaT){hash, &data}, NULL, &value, DwStmgDataKeyCompare);
    if (ret == GMERR_NO_DATA) {
        ret = DbShmOamapInsert(stmgMap, hash, (DbShmOamapKVT){&data, NULL, sizeof(DirectStmgDataT), 0u}, NULL,
            (DbShmOamapFuncsT){DwStmgDataKeyCopy, DwStmgDataValueCopy, DwStmgDataKeyCompare});
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Write dw stmgMap.");
        }
    } else if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Loockup dw stmgMap.");
    }
    DbRWSpinWUnlock(&stmgMap->rwLock);
    return ret;
}

#ifdef __cplusplus
}
#endif
