/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name:
 * Description:
 * Author:
 * Create: 2024-11-20
 */

#include "adpt_sleep.h"
#include "adpt_pipe.h"
#include "db_mem_context.h"
#include "db_stream_msg_pool.h"

#include "clt_error.h"

#ifdef __cplusplus
extern "C" {
#endif

#define STREAM_MEM_BASE_SIZE (256 * DB_KIBI)
#define STREAM_MEM_STEP_SIZE (256 * DB_KIBI)
#define STREAM_MEM_MAX_SIZE (1 * DB_MEBI)
#define STREAM_CHANNEL_SIZE (64 * DB_KIBI - 32)
#define STREAM_CHANNEL_NUM 2
#define STREAM_POOLING_WRITE_TIMEOUT 2
#define STREAM_MAGIC_CODE 0x4D534700

// 此结构体在共享内存里，不能包含指针
typedef struct StreamMsg {
    uint32_t magicCode;
    uint32_t size;
    uint8_t buf[];
} StreamMsgT;

// 此结构体在共享内存里，不能包含指针
typedef struct StreamMsgChannel {
    uint32_t writePos;
    uint32_t readPos;
    uint8_t buf[STREAM_CHANNEL_SIZE];
} StreamMsgChannelT;

// 此结构体在共享内存里，不能包含指针
typedef struct StreamMsgPool {
    ShmemPtrT shmPtr;
    uint64_t owner;         // CAS
    uint32_t writeChanId;   // 当前写入那个缓冲区
    uint64_t lastLockTime;  // 记录上次客户端操作锁的时间
    bool activeRead;        // 读线程是否活跃
    DbSemT sem;
    StreamMsgChannelT channels[STREAM_CHANNEL_NUM];
} StreamMsgPoolT;

static Status StreamMsgPoolInitShmMemCtx(DbMemCtxT **shmMemCtx)
{
    DbBlockMemParamT blockParam = {
        .isHugePage = false,
        .baseSize = STREAM_MEM_BASE_SIZE,
        .stepSize = STREAM_MEM_STEP_SIZE,
        .maxSize = STREAM_MEM_MAX_SIZE,
        .isReused = true,
        .allowBigChunk = true,
        .blkPoolType = BLK_NORMAL,
    };

    AlgoParamT algoParam = {
        .blockParam = &blockParam,
    };

    DbMemCtxArgsT argsBlock = {
        .instanceId = DbGetProcGlobalId(),
        .ctxId = DB_STREAM_SHMCTX_ID,
        .algoParam = &algoParam,
        .collectAllocSizeOnThisTree = true,
    };

    DbMemCtxT *topShmMemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, DbGetProcGlobalId());
    if (topShmMemCtx == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "get top shmemCtx.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    *shmMemCtx = (DbMemCtxT *)DbCreateBlockPoolShmemCtx(topShmMemCtx, "StreamMsgPool", &argsBlock);
    if (*shmMemCtx == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "create StreamMsgPool shmemCtx.");
        return GMERR_OUT_OF_MEMORY;
    }

    return GMERR_OK;
}

Status StreamMsgPoolInit(void)
{
    Status ret = GMERR_OK;
    DbMemCtxT *shmMemCtx = NULL;
    ret = StreamMsgPoolInitShmMemCtx(&shmMemCtx);
    if (ret != GMERR_OK) {
        return ret;
    }

    ShmemPtrT msgPoolPtr = DbShmemStructAllocById(shmMemCtx, sizeof(StreamMsgPoolT), DB_SHM_STREAM_MSG_POOL_ID);
    StreamMsgPoolT *msgPool = DbShmPtrToAddr(msgPoolPtr);
    if (msgPool == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "allocate StreamMsgPool.");
        goto ERROR;
    }

    (void)memset_s(msgPool, sizeof(StreamMsgPoolT), 0, sizeof(StreamMsgPoolT));
    msgPool->shmPtr = msgPoolPtr;
    msgPool->owner = DB_INVALID_UINT64;
    msgPool->activeRead = true;
    ret = DbSemInit(&msgPool->sem, PROCESS_SEMAPHORE, 0);
    if (ret != GMERR_OK) {
        goto ERROR;
    }
    return GMERR_OK;
ERROR:
    DbDeleteShmemCtx(shmMemCtx);
    return ret;
}

static StreamMsgT *StreamMsgChannelRead(StreamMsgChannelT *channel)
{
    if (channel->readPos >= channel->writePos) {
        if (channel->readPos > 0) {
            channel->readPos = 0;
            channel->writePos = 0;
        }

        return NULL;
    }

    StreamMsgT *msg = (StreamMsgT *)(channel->buf + channel->readPos);
    DB_ASSERT(msg->magicCode == STREAM_MAGIC_CODE);
    channel->readPos += (uint32_t)sizeof(StreamMsgT) + msg->size;
    return msg;
}

static uint32_t StreamMsgReadChanId(StreamMsgPoolT *msgPool)
{
    return (msgPool->writeChanId + 1) % STREAM_CHANNEL_NUM;
}

static bool StreamMsgPoolNeedSwap(StreamMsgPoolT *msgPool)
{
    uint32_t readChanId = StreamMsgReadChanId(msgPool);
    StreamMsgChannelT *channel = &msgPool->channels[readChanId];
    return channel->readPos >= channel->writePos;
}

Status StreamMsgPoolBeginRead(StreamMsgPoolT *msgPool, uint64_t owner, StreamMsgReaderT *reader)
{
    *reader = (StreamMsgReaderT){
        .msgPool = msgPool,
        .owner = owner,
    };

    if (SECUREC_UNLIKELY(!StreamMsgPoolNeedSwap(msgPool))) {
        return GMERR_OK;
    }
    // 交换缓冲区
    // 加锁
    uint64_t oldValue = DbAtomicValCAS64(&msgPool->owner, DB_INVALID_UINT64, reader->owner);
    if (oldValue != DB_INVALID_UINT64) {
        uint32_t times = 20;
        uint32_t splitSecond = 0;
        // 不加个while循环，可能和客户端加锁+更新lastLockTime的逻辑有时序问题
        while (times > 0) {
            // 判断是否需要强制解锁
            uint64_t lastLockTime = DbAtomicGet64(&msgPool->lastLockTime);
            uint64_t currTime = DbGlobalRdtsc();
            splitSecond = (uint32_t)DbToSeconds(currTime - lastLockTime);
            if (splitSecond < STREAM_POOLING_WRITE_TIMEOUT) {
                return GMERR_LOCK_NOT_AVAILABLE;
            }
            usleep(1);
            times--;
        }
        if (!DbAtomicBoolCAS64(&msgPool->owner, oldValue, reader->owner)) {
            // 这里失败说明从DbAtomicValCAS64到DbAtomicBoolCAS64之间，有新的客户端进行了lock，这里就不要再强行lock了
            return GMERR_LOCK_NOT_AVAILABLE;
        }
    }
    uint32_t readChanId = StreamMsgReadChanId(msgPool);
    msgPool->writeChanId = readChanId;
    // 解锁
    bool cas = DbAtomicBoolCAS64(&msgPool->owner, reader->owner, DB_INVALID_UINT64);
    DB_ASSERT(cas);
    // 说明没有数据可读
    if (StreamMsgPoolNeedSwap(msgPool)) {
        return GMERR_NO_DATA;
    }
    return GMERR_OK;
}

void StreamMsgPoolEndRead(StreamMsgReaderT *reader)
{
    *reader = (StreamMsgReaderT){.owner = DB_INVALID_UINT64};
}

TextT StreamMsgPoolReadNext(StreamMsgReaderT *reader)
{
    StreamMsgPoolT *msgPool = reader->msgPool;
    uint32_t readChanId = (msgPool->writeChanId + 1) % STREAM_CHANNEL_NUM;
    StreamMsgT *msg = StreamMsgChannelRead(&msgPool->channels[readChanId]);
    if (SECUREC_LIKELY(msg != NULL)) {
        DB_ASSERT(msg->magicCode == STREAM_MAGIC_CODE);
        return (TextT){.str = (char *)msg->buf, .len = msg->size};
    }

    return (TextT){};
}

static uint8_t *StreamMsgChannelReserve(StreamMsgChannelT *channel, uint32_t size)
{
    if (channel->writePos + sizeof(StreamMsgT) + size > STREAM_CHANNEL_SIZE) {
        return NULL;
    }
    StreamMsgT *msg = (StreamMsgT *)(channel->buf + channel->writePos);
    msg->magicCode = STREAM_MAGIC_CODE;
    msg->size = size;
    return msg->buf;
}

ALWAYS_INLINE
static uint8_t *StreamMsgPoolReserve(StreamMsgPoolT *msgPool, uint64_t owner, uint32_t size)
{
    // 加锁
    if (SECUREC_UNLIKELY(!DbAtomicBoolCAS64(&msgPool->owner, DB_INVALID_UINT64, owner))) {
        return NULL;
    }
    DbAtomicSet64(&msgPool->lastLockTime, DbGlobalRdtsc());
    uint8_t *buf = StreamMsgChannelReserve(&msgPool->channels[msgPool->writeChanId], size);
    if (SECUREC_UNLIKELY(buf == NULL)) {
        // 解锁
        bool cas = DbAtomicBoolCAS64(&msgPool->owner, owner, DB_INVALID_UINT64);
        DB_ASSERT(cas);
    }
    return buf;
}

ALWAYS_INLINE
Status StreamMsgPoolBeginWrite(StreamMsgPoolT *msgPool, uint32_t size, StreamMsgWriterT *writer)
{
    uint8_t *tmpBuf = StreamMsgPoolReserve(msgPool, writer->owner, size);
    if (SECUREC_UNLIKELY(tmpBuf == NULL)) {
        // 错误码和通信使用的错误码兼容
        return GMERR_CONNECTION_SEND_BUFFER_FULL;
    }
    writer->msgPool = msgPool;
    writer->buf = tmpBuf;
    return GMERR_OK;
}

ALWAYS_INLINE
void StreamMsgPoolEndWrite(StreamMsgWriterT *writer, bool abort)
{
    StreamMsgPoolT *msgPool = writer->msgPool;
    if (SECUREC_LIKELY(msgPool != NULL)) {
        uint64_t owner = DbAtomicGet64(&msgPool->owner);
        if (SECUREC_UNLIKELY(owner != writer->owner)) {
            return;
        }
        if (SECUREC_LIKELY(!abort)) {
            StreamMsgChannelT *chan = &msgPool->channels[msgPool->writeChanId];
            StreamMsgT *msg = (StreamMsgT *)(chan->buf + chan->writePos);
            chan->writePos += (uint32_t)sizeof(StreamMsgT) + msg->size;
        }
        StreamMsgPoolNotifyReader(msgPool);
        bool cas = DbAtomicBoolCAS64(&msgPool->owner, writer->owner, DB_INVALID_UINT64);
        if (SECUREC_UNLIKELY(!cas)) {
            // 用户不感知是否成功
            DB_SET_LASTERR(GMERR_LOCK_NOT_AVAILABLE, "Write operation timed out");
        }
    }
}

ALWAYS_INLINE uint8_t *StreamMsgPoolGetWriteBuf(StreamMsgWriterT *writer)
{
    return writer->buf;
}

void StreamMsgPoolNotifyReader(StreamMsgPoolT *msgPool)
{
    if (!msgPool->activeRead) {
        (void)DbSemPost(&msgPool->sem);
        msgPool->activeRead = true;
    }
}

void StreamMsgPoolReaderWait(StreamMsgPoolT *msgPool, uint64_t owner, uint32_t waitTime)
{
    bool cas = DbAtomicBoolCAS64(&msgPool->owner, DB_INVALID_UINT64, owner);
    if (cas) {
        msgPool->activeRead = false;
        cas = DbAtomicBoolCAS64(&msgPool->owner, owner, DB_INVALID_UINT64);
        DB_ASSERT(cas);
        (void)DbSemTimedWait(&msgPool->sem, waitTime);
    }
}

#ifdef __cplusplus
}
#endif
