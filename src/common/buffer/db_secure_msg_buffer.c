/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: source file for secure message buffer
 * Author: chenjunyu
 * Create: 2022-10-29
 */

#include "db_secure_msg_buffer.h"

ALWAYS_INLINE static Status SecureFixBufferVerify(FixBufferT *buffer)
{
#ifdef SECUREFIXBUF
    DB_POINTER2(buffer, buffer->verify.proc);
    return buffer->verify.proc(buffer->verify.ctx);
#endif
    return GMERR_OK;
}

#define SECURE_FIX_BUF_NUM2 2
#define SECURE_FIX_BUF_NUM3 3
#define SECURE_FIX_BUF_NUM4 4
#define SECURE_FIX_BUF_NUM5 5
#define SECURE_FIX_BUF_NUM6 6

Status SecureFixBufPut2Uint32(FixBufferT *buffer, uint32_t val1, uint32_t val2)
{
    Status ret = SecureFixBufferVerify(buffer);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    uint32_t *u32 = (uint32_t *)FixBufReserveData(buffer, SECURE_FIX_BUF_NUM2 * (uint32_t)sizeof(uint32_t));
    if (SECUREC_UNLIKELY(u32 == NULL)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    *u32 = val1;
    *(u32 + 1) = val2;

    return GMERR_OK;
}

Status SecureFixBufPut3Uint32(FixBufferT *buffer, uint32_t val1, uint32_t val2, uint32_t val3)
{
    Status ret = SecureFixBufferVerify(buffer);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    uint32_t *u32 = (uint32_t *)FixBufReserveData(buffer, SECURE_FIX_BUF_NUM3 * (uint32_t)sizeof(uint32_t));
    if (SECUREC_UNLIKELY(u32 == NULL)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    *u32 = val1;
    *(u32 + 1) = val2;
    *(u32 + 2) = val3;

    return GMERR_OK;
}

Status SecureFixBufPut4Uint32(FixBufferT *buffer, uint32_t val1, uint32_t val2, uint32_t val3, uint32_t val4)
{
    Status ret = SecureFixBufferVerify(buffer);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    uint32_t *u32 = (uint32_t *)FixBufReserveData(buffer, SECURE_FIX_BUF_NUM4 * (uint32_t)sizeof(uint32_t));
    if (SECUREC_UNLIKELY(u32 == NULL)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    *u32 = val1;
    *(u32 + 1) = val2;
    *(u32 + 2) = val3;
    *(u32 + 3) = val4;

    return GMERR_OK;
}

Status SecureFixBufPut5Uint32(
    FixBufferT *buffer, uint32_t val1, uint32_t val2, uint32_t val3, uint32_t val4, uint32_t val5)
{
    Status ret = SecureFixBufferVerify(buffer);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    uint32_t *u32 = (uint32_t *)FixBufReserveData(buffer, SECURE_FIX_BUF_NUM5 * (uint32_t)sizeof(uint32_t));
    if (SECUREC_UNLIKELY(u32 == NULL)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    *u32 = val1;
    *(u32 + 1) = val2;
    *(u32 + 2) = val3;
    *(u32 + 3) = val4;
    *(u32 + 4) = val5;

    return GMERR_OK;
}

Status SecureFixBufPut2Uint64(FixBufferT *buffer, uint64_t val1, uint64_t val2)
{
    Status ret = SecureFixBufferVerify(buffer);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    uint64_t *u64 = (uint64_t *)FixBufReserveData(buffer, SECURE_FIX_BUF_NUM2 * (uint32_t)sizeof(uint64_t));
    if (SECUREC_UNLIKELY(u64 == NULL)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    *u64 = val1;
    *(u64 + 1) = val2;

    return GMERR_OK;
}

Status SecureFixBufPut3Uint64(FixBufferT *buffer, uint64_t val1, uint64_t val2, uint64_t val3)
{
    Status ret = SecureFixBufferVerify(buffer);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    uint64_t *u64 = (uint64_t *)FixBufReserveData(buffer, SECURE_FIX_BUF_NUM3 * (uint32_t)sizeof(uint64_t));
    if (SECUREC_UNLIKELY(u64 == NULL)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    *u64 = val1;
    *(u64 + 1) = val2;
    *(u64 + 2) = val3;

    return GMERR_OK;
}

Status SecureFixBufPut4Uint64(FixBufferT *buffer, uint64_t val1, uint64_t val2, uint64_t val3, uint64_t val4)
{
    Status ret = SecureFixBufferVerify(buffer);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    uint64_t *u64 = (uint64_t *)FixBufReserveData(buffer, SECURE_FIX_BUF_NUM4 * (uint32_t)sizeof(uint64_t));
    if (SECUREC_UNLIKELY(u64 == NULL)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    *u64 = val1;
    *(u64 + 1) = val2;
    *(u64 + 2) = val3;
    *(u64 + 3) = val4;

    return GMERR_OK;
}

Status SecureFixBufGet2Uint64(FixBufferT *buffer, uint64_t *val1, uint64_t *val2)
{
    Status ret = SecureFixBufferVerify(buffer);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    const uint64_t *u64 = (uint64_t *)FixBufGetData(buffer, SECURE_FIX_BUF_NUM2 * (uint32_t)sizeof(uint64_t));

    *val1 = u64[0];
    *val2 = u64[1];

    return GMERR_OK;
}

Status SecureFixBufGet6Uint64(
    FixBufferT *buffer, uint64_t *val1, uint64_t *val2, uint64_t *val3, uint64_t *val4, uint64_t *val5, uint64_t *val6)
{
    Status ret = SecureFixBufferVerify(buffer);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    const uint64_t *u64 = (uint64_t *)FixBufGetData(buffer, SECURE_FIX_BUF_NUM6 * (uint32_t)sizeof(uint64_t));

    *val1 = u64[0];
    *val2 = u64[1];
    *val3 = u64[2];
    *val4 = u64[3];
    *val5 = u64[4];
    *val6 = u64[5];

    return GMERR_OK;
}
