/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: db_timer.c
 * Description: source file for db timer rtos
 * Author: wangsheng
 * Create: 2020-10-28
 */

#include "db_timer.h"
#include "db_log.h"
#include "db_internal_error.h"

#ifdef __cplusplus
extern "C" {
#endif

Status DbTimerRegister(DbTimerT *timer, TimerHandleT *timerHandle)
{
    DB_POINTER2(timer, timerHandle);
    *timerHandle = DbTimerCreate(timer->timerName, &timer->callBackT, timer->period, timer->mode);
    if (!DbCheckTimerHandle(*timerHandle)) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Create timer.");
        return GMERR_DATA_EXCEPTION;
    }
    Status ret = DbTimerStart(*timerHandle, (uint32_t)timer->mode);
    if (ret != GMERR_OK) {
        (void)DbTimerDelete(*timerHandle);
        DB_LOG_ERROR(ret, "Start timer.");
        return ret;
    }
    return GMERR_OK;
}

Status DbTimerUnregister(const TimerHandleT *timerHandle, TimerModeE mode)
{
    DB_POINTER(timerHandle);
    Status ret;
    if (mode == TIMER_MODE_LOOP) {
        ret = DbTimerStop(*timerHandle);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Stop timer(timerHandle:%" PRIu64 ").", timerHandle->timerHandle);
            return ret;
        }
    }
    ret = DbTimerDelete(*timerHandle);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Delete timer(timerHandle:%" PRIu64 ").", timerHandle->timerHandle);
        return ret;
    }
    return GMERR_OK;
}

uint64_t DbClockGetTsc(void)
{
    return DbRdtsc();
}

bool DbExceedTime(uint64_t startTime, double timeThreshold)
{
    uint64_t endTime = DbClockGetTsc();
    if (endTime <= startTime) {
        return false;
    }
    double td = (double)DbToMseconds(endTime - startTime);
    return td >= timeThreshold;
}

#ifdef __cplusplus
}
#endif
