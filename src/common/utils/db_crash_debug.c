/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: db crash debug source file
 * Author: <PERSON><PERSON><PERSON><PERSON>jian
 * Create: 2021-09-22
 */
#include "db_crash_debug.h"
#include "db_mem_context.h"

#ifdef ENABLE_CRASHPOINT

bool g_gmdbIsInitCrashPoint = false;
DbCrashPointHook g_gmdbCrashPointHook = NULL;
Handle g_gmdbCrashPointCtxHead = NULL;

void DbInitCrashPoint(DbCrashPointHook crashPointHook, void *ctxHead)
{
    DB_POINTER2(crashPointHook, ctxHead);
    g_gmdbCrashPointHook = crashPointHook;
    g_gmdbCrashPointCtxHead = ctxHead;
    g_gmdbIsInitCrashPoint = true;
}

#endif

#ifdef ENABLE_CRASHPOINT

typedef struct DbShmCrashState {
    uint32_t srvAttachCnt;
    uint32_t cltAttachCnt;
    DbShmCrashPointTypeT type;
} DbShmCrashStateT;

DbShmCrashStateT *g_gmdbShmCrashState = NULL;
bool g_gmdbIsCltSetShmCrashPoint = false;

// 故障注入是否生效
bool ShmCrashPointIsValid(void)
{
    // 客户端:set故障点才表示有效
    return DbCommonIsServer() || g_gmdbIsCltSetShmCrashPoint;
}

DbShmCrashStateT *GetShmCrashStatePtr(void)
{
    return (DbShmCrashStateT *)DbGetShmemStructById(DB_SHM_CRASH_POINT_STRUCT_ID, GET_INSTANCE_ID);
}

// 依赖topShmCtx的兜底回收
static void ShmCrashPointInit(void)
{
    if (g_gmdbShmCrashState != NULL || GetShmCrashStatePtr() != NULL) {  // 已经初始化过了
        return;
    }
    // 共享内存申请。本接口不支持多实例，instanceId使用默认值。
    DbMemCtxT *topShmCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, GET_INSTANCE_ID);
    ShmemPtrT shmPtr = DbShmemStructAllocById(topShmCtx, sizeof(DbShmCrashStateT), DB_SHM_CRASH_POINT_STRUCT_ID);
    g_gmdbShmCrashState = (DbShmCrashStateT *)DbShmPtrToAddr(shmPtr);
    if (g_gmdbShmCrashState == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Alloc DB_SHM_CRASH_POINT_STRUCT_ID");
    } else {
        *g_gmdbShmCrashState = (DbShmCrashStateT){.srvAttachCnt = 0, .cltAttachCnt = 0, .type = SHM_CRASH_END};
    }
}

void DbShmCrashPointAttach(void)
{
    if (!ShmCrashPointIsValid()) {
        return;
    }
    // 共享内存attach。本接口不支持多实例，instanceId使用默认值。
    g_gmdbShmCrashState = GetShmCrashStatePtr();
    if (g_gmdbShmCrashState != NULL) {
        g_gmdbShmCrashState->srvAttachCnt += (uint32_t)DbCommonIsServer();
        g_gmdbShmCrashState->cltAttachCnt += (uint32_t)(!DbCommonIsServer());
    }
}

// 服务端通过attachCnt判断crash有效，客户端通过全局指针判断是否有效
bool ShmCrashStateIsAttach(void)
{
    return g_gmdbShmCrashState != NULL && (!DbCommonIsServer() || g_gmdbShmCrashState->srvAttachCnt != 0);
}

// 只期望客户端无并发使用
void DbShmCrashPointSet(DbShmCrashPointTypeT type)
{
    g_gmdbIsCltSetShmCrashPoint = true;
    ShmCrashPointInit();
    DbShmCrashPointAttach();
    if (ShmCrashStateIsAttach()) {
        g_gmdbShmCrashState->type = type;
    }
}

// 访问共享内存，需要保证共享内存有效
void DbShmCrashPointDetach(void)
{
    if (!ShmCrashPointIsValid()) {
        return;
    }
    if (ShmCrashStateIsAttach()) {
        if (DbCommonIsServer()) {
            g_gmdbShmCrashState->srvAttachCnt--;
        } else {
            g_gmdbShmCrashState->cltAttachCnt--;
            if (g_gmdbShmCrashState->cltAttachCnt == 0) {
                g_gmdbShmCrashState = NULL;
                g_gmdbIsCltSetShmCrashPoint = false;
            }
        }
    }
}

// 测试客户端，不重启时，一把clear
void DbShmCrashPointClear(void)
{
    g_gmdbShmCrashState = NULL;
}

// 不考虑服务端并发
bool DbShmCrashIsHit(DbShmCrashPointTypeT type)
{
    return ShmCrashPointIsValid() && ShmCrashStateIsAttach() && g_gmdbShmCrashState->type == type;
}

void DbShmCrashExit(void)
{
    if (!ShmCrashPointIsValid()) {
        return;
    }
    if (DbCommonIsServer()) {
        g_gmdbShmCrashState->type = SHM_CRASH_END;
        exit(0);
    } else {
        pthread_exit(&g_gmdbShmCrashState->type);
    }
}

#endif
