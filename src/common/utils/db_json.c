/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: db_json.c
 * Description: implementation for db_json
 * Author:
 * Create: 2022/6/8
 */

#include "db_json.h"

#include <stdbool.h>
#include <stdio.h>

#include "adpt_define.h"
#include "db_config.h"
#include "db_file.h"

static bool DbCheckJsonDepth(const char *string)
{
    const uint32_t jsonMaxDepth = 128;
    uint32_t d = 0;

    bool s = false;  // is string
    bool e = false;  // is escape

    const char *p = string;
    char c;

    while (c = *p, ++p, c != 0) {
        if (e) {
            e = false;
            continue;
        }
        if (s) {
            if (c == '\\') {
                e = true;
            } else if (c == '"') {
                s = false;
            }
            continue;
        }
        if (c == '{' || c == '[') {
            ++d;
            if (d > jsonMaxDepth) {
                return false;
            }
        } else if (c == '}' || c == ']') {
            if (d > 0) {
                --d;
            }
        } else if (c == '"') {
            s = true;
        }
    }

    return true;
}
#ifndef FEATURE_MINIKV
DbJsonT *DbLoadJsonClt(const char *string, size_t flags)
{
    DB_POINTER(string);
    DbCfgValueT cfgValue;
    Status ret = DbCltCfgGetById(DB_CLT_CFG_SECURITY_CHECK_MODE, &cfgValue);
    if (ret != GMERR_OK) {
        return NULL;
    }

    if (cfgValue.int32Val != 0) {
        if (!DbCheckJsonDepth(string)) {
            return NULL;
        }
    }

    DbJsonT *json = DbJsonLoads(string, flags);
    return json;
}

DbJsonT *DbLoadJsonSrv(const char *string, size_t flags)
{
    DB_POINTER(string);
    if (DbCfgGetBoolLite(DB_CFG_MESSAGE_SECURITY_CHECK, NULL)) {
        if (!DbCheckJsonDepth(string)) {
            return NULL;
        }
    }

    return DbJsonLoads(string, flags);
}

// 带Header的json导入，Header+DbJsonT，需要略过header,才是json数据部分，没有header的话,headerLen传入0
DbJsonT *DbLoadJsonFileCltWithHeader(const char *path, size_t flags)
{
    DB_POINTER(path);
    DbJsonT *json = NULL;

    size_t size;
    Status ret = DbFileSize(path, &size);
    if (ret != GMERR_OK) {
        goto EXIT0;
    }
    char *buf = (char *)DB_MALLOC((uint32_t)(size + 1));
    if (buf == NULL) {
        goto EXIT0;
    }

    int32_t fd;
    ret = DbOpenFile(path, READ_ONLY, PERM_USER, &fd);
    if (ret != GMERR_OK) {
        goto EXIT1;
    }
    ret = DbReadFileExpSize(fd, buf, size);
    DbCloseFile(fd);
    if (ret != GMERR_OK) {
        goto EXIT1;
    }

    buf[size] = '\0';
    json = DbLoadJsonClt(buf, flags);

EXIT1:
    DB_FREE(buf);
EXIT0:
    return json;
}
#else
DbJsonT *DbLoadJsonClt(const char *string, size_t flags)
{
    return NULL;
}

DbJsonT *DbLoadJsonSrv(const char *string, size_t flags)
{
    DB_POINTER(string);
    if (!DbCheckJsonDepth(string)) {
        return NULL;
    }
    DbJsonT *object = NULL;
    Status ret = DbJsonStrTransToJsonObject(string, false, true, &object);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Load json string");
        return NULL;
    }
    return object;
}

DbJsonT *DbLoadJsonFileCltWithHeader(const char *path, size_t flags)
{
    return NULL;
}
#endif
