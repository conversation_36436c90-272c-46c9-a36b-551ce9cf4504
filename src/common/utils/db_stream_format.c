/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Direct and safe replacement for printf, used for parsing and formatting
 *              strings in the streaming engine.
 *              Formatting follows C11/GNU11 standard, as conversion specifiers are
 *              defined as: %[flags][width][.precision][length_mod]conversion_type
 *              Additional checks prevent unsafe conversions or buffer overflows.
 *              NOTE: keep this file GMDB-AGNOSTIC and PORTABLE, please!
 * Author: <PERSON>
 * Create: 2024-10-18
 */

#include "db_stream_format.h"
#include "adpt_define.h"

#ifdef __cplusplus
extern "C" {
#endif

int StreamFormatParseOptionalFlags(const char **inputBuffer, ConvSpec *const cs)
{
    if ((inputBuffer == NULL) || (cs == NULL)) {
        return FORMAT_STATUS_NULL_PTR_ERROR;
    }

    (*inputBuffer)++;
    int32_t flagC = 1;
    do {
        switch (**inputBuffer) {
            case '0':
                (*cs).flags = (int32_t)((uint32_t)((*cs).flags) | FORMAT_FLAGS_ZPAD);
                (*inputBuffer)++;
                (*cs).chrNum++;
                flagC = 1;
                break;
            case '-':
                (*cs).flags = (int32_t)((uint32_t)((*cs).flags) | FORMAT_FLAGS_LEFT);
                (*inputBuffer)++;
                (*cs).chrNum++;
                flagC = 1;
                break;
            case '+':
                (*cs).flags = (int32_t)((uint32_t)((*cs).flags) | FORMAT_FLAGS_PLUS);
                (*inputBuffer)++;
                (*cs).chrNum++;
                flagC = 1;
                break;
            case ' ':
                (*cs).flags = (int32_t)((uint32_t)((*cs).flags) | FORMAT_FLAGS_SPCE);
                (*inputBuffer)++;
                (*cs).chrNum++;
                flagC = 1;
                break;
            case '#':
                (*cs).flags = (int32_t)((uint32_t)((*cs).flags) | FORMAT_FLAGS_HASH);
                (*inputBuffer)++;
                (*cs).chrNum++;
                flagC = 1;
                break;
            default:
                flagC = 0;
                break;
        }
    } while ((flagC != 0) && **inputBuffer != '\0');
    return FORMAT_STATUS_OK;
}

int StreamFormatParseWidthMod(const char **inputBuffer, ConvSpec *const cs)
{
    if ((inputBuffer == NULL) || (cs == NULL)) {
        return FORMAT_STATUS_NULL_PTR_ERROR;
    }

    char *end = NULL;
    if ((**inputBuffer) == '\0') {
        return FORMAT_STATUS_OK;
    } else if (StreamFormatIsNumChar(**inputBuffer)) {
        (*cs).width = (int32_t)strtol(*inputBuffer, &end, STREAM_FORMAT_BASE_10);
        (*cs).chrNum += (int32_t)(end - *inputBuffer);
        *inputBuffer = end;
    } else if (**inputBuffer == '*') {
        // Feature not supported
        (*inputBuffer)++;
        (*cs).chrNum++;
        return FORMAT_STATUS_UNSUPPORTED_WIDTH_MOD;
    }
    return FORMAT_STATUS_OK;
}

int StreamFormatParsePrecisionMod(const char **inputBuffer, ConvSpec *const cs)
{
    if ((inputBuffer == NULL) || (cs == NULL)) {
        return FORMAT_STATUS_NULL_PTR_ERROR;
    }

    if (!(**inputBuffer != '\0')) {
        return FORMAT_STATUS_OK;
    } else if (**inputBuffer == '.') {
        char *end = NULL;
        (*inputBuffer)++;
        (*cs).chrNum++;
        if (!(**inputBuffer != '\0')) {
            return FORMAT_STATUS_OK;
        } else if (StreamFormatIsNumChar(**inputBuffer)) {
            (*cs).precision = (int32_t)strtol(*inputBuffer, &end, STREAM_FORMAT_BASE_10);
            (*cs).chrNum += (int32_t)(end - *inputBuffer);
            *inputBuffer = end;
        } else if (**inputBuffer == '*') {
            // Feature not supported
            (*inputBuffer)++;
            (*cs).chrNum++;
            return FORMAT_STATUS_UNSUPPORTED_PRECISION_MOD;
        }
    }
    return FORMAT_STATUS_OK;
}

int StreamFormatParseLengthMod(const char **inputBuffer, ConvSpec *const cs)
{
    if ((inputBuffer == NULL) || (cs == NULL)) {
        return FORMAT_STATUS_NULL_PTR_ERROR;
    }

    if (!(**inputBuffer != '\0')) {
        return FORMAT_STATUS_OK;
    }
    switch (**inputBuffer) {
        case 'l':
            (*cs).lengthMod = FORMAT_LMOD_LONG;
            (*inputBuffer)++;
            (*cs).chrNum++;
            if (!(**inputBuffer != '\0')) {
                return FORMAT_STATUS_OK;
            } else if (**inputBuffer == 'l') {
                (*cs).lengthMod = FORMAT_LMOD_LONG_LONG;
                (*inputBuffer)++;
                (*cs).chrNum++;
            }
            break;
        case 'h':
            (*cs).lengthMod = FORMAT_LMOD_SHORT;
            (*inputBuffer)++;
            (*cs).chrNum++;
            if (!(**inputBuffer != '\0')) {
                return FORMAT_STATUS_OK;
            } else if (**inputBuffer == 'h') {
                (*cs).lengthMod = FORMAT_LMOD_CHAR;
                (*inputBuffer)++;
                (*cs).chrNum++;
            }
            break;
        case 'j':
        case 't':
        case 'z':
        case 'L':
            // Modifiers not supported
            (*inputBuffer)++;
            (*cs).chrNum++;
            return FORMAT_STATUS_UNSUPPORTED_LENGTH_MOD;
        default:
            break;
    }
    return FORMAT_STATUS_OK;
}

int StreamFormatParseType(const char **inputBuffer, ConvSpec *const cs)
{
    int status = FORMAT_STATUS_OK;
    if (!(**inputBuffer != '\0')) {
        return FORMAT_STATUS_OK;
    }
    (*cs).actualChr = **inputBuffer;
    switch ((*cs).actualChr) {
        case 'd':
        case 'i':
        case 'u':
        case 'x':
        case 'X':
        case 'o':
        case 'b':
            (*cs).type = FORMAT_TYPE_INT;
            break;
        case 'f':
        case 'F':
        case 'e':
        case 'E':
        case 'g':
        case 'G':
            (*cs).type = FORMAT_TYPE_FLT;
            break;
        case 'c':
            (*cs).type = FORMAT_TYPE_CHR;
            break;
        case 's':
            (*cs).type = FORMAT_TYPE_STR;
            break;
        case '%':
            (*cs).type = FORMAT_TYPE_PER;
            break;
        case 'p':
            (*cs).type = FORMAT_TYPE_UNK;
            status = FORMAT_STATUS_PTR_CONVERTER_UNSUPPORTED;
            break;
        case 'n':
            (*cs).type = FORMAT_TYPE_UNK;
            status = FORMAT_STATUS_CHR_COUNTER_UNSUPPORTED;
            break;
        default:  // This also includes 'a' and 'A' specifiers
            (*cs).type = FORMAT_TYPE_UNK;
            break;
    }

    (*cs).chrNum++;
    (*inputBuffer)++;

    return status;
}

int StreamFormatSetAdditionalFlags(const char **inputBuffer, ConvSpec *const cs)
{
    if ((inputBuffer == NULL) || (cs == NULL)) {
        return FORMAT_STATUS_NULL_PTR_ERROR;
    }

    int status = FORMAT_STATUS_OK;
    // Set uppercase flag
    const char actChr = (*cs).actualChr;
    switch (actChr) {
        case 'E':
        case 'F':
        case 'G':
        case 'X':
            (*cs).flags = (int32_t)((uint32_t)((*cs).flags) | FORMAT_FLAGS_UPCS);
            break;
        default:
            break;
    }

    // Zero pad makes sense only in integer mode AND if width is defined
    if (((uint32_t)((*cs).flags) & FORMAT_FLAGS_ZPAD) != 0 &&
        (((uint32_t)((*cs).type) != FORMAT_TYPE_INT) || ((*cs).width < 0))) {
        (*cs).flags = (int32_t)((uint32_t)((*cs).flags) & ~FORMAT_FLAGS_ZPAD);
    }

    // Reset zero pad flag if precision or minus flags are set
    if ((((uint32_t)((*cs).flags) & FORMAT_FLAGS_LEFT) | ((uint32_t)((*cs).precision >= 0))) != 0) {
        (*cs).flags = (int32_t)((uint32_t)((*cs).flags) & ~FORMAT_FLAGS_ZPAD);
    }

    // In int mode, plus or space flags are only meaningful for 'i' and 'd'
    // On the other hand, 'i' and 'd' are not influenced by the hash flag
    if ((*cs).type == FORMAT_TYPE_INT) {
        if (((*cs).actualChr == 'd') || ((*cs).actualChr == 'i')) {
            (*cs).flags = (int32_t)((uint32_t)((*cs).flags) & ~FORMAT_FLAGS_HASH);
        } else {
            (*cs).flags = (int32_t)((uint32_t)((*cs).flags) & (~(FORMAT_FLAGS_PLUS | FORMAT_FLAGS_SPCE)));
        }
    }

    return status;
}

int StreamFormatGetNextCS(const char **inputBuffer, ConvSpec *const cs)
{
    if ((inputBuffer == NULL) || (cs == NULL)) {
        return FORMAT_STATUS_NULL_PTR_ERROR;
    }

    int status = FORMAT_STATUS_OK;
    // Reset the conversion specifier
    (*cs).flags = 0;
    (*cs).width = -1;
    (*cs).precision = -1;
    (*cs).lengthMod = 0;
    (*cs).type = FORMAT_TYPE_UNK;
    (*cs).chrNum = 1;
    (*cs).actualChr = '\0';

    // Start with the optional flags
    status = StreamFormatParseOptionalFlags(inputBuffer, cs);
    if (status != FORMAT_STATUS_OK) {
        goto ERR_INVALID_FMT;
    }

    // Check if we have an optional width modifier
    status = StreamFormatParseWidthMod(inputBuffer, cs);
    if (status != FORMAT_STATUS_OK) {
        goto ERR_INVALID_FMT;
    }

    // Check if we have an optional precision modifier
    status = StreamFormatParsePrecisionMod(inputBuffer, cs);
    if (status != FORMAT_STATUS_OK) {
        goto ERR_INVALID_FMT;
    }

    // Check if we have optional length modifiers
    status = StreamFormatParseLengthMod(inputBuffer, cs);
    if (status != FORMAT_STATUS_OK) {
        goto ERR_INVALID_FMT;
    }

    // Check the format type
    status = StreamFormatParseType(inputBuffer, cs);
    if (status != FORMAT_STATUS_OK) {
        goto ERR_INVALID_FMT;
    }

    // Additional flags depending on the conversion type
    status = StreamFormatSetAdditionalFlags(inputBuffer, cs);

ERR_INVALID_FMT:
    return status;
}

// We assume that validSpecsFound has been correctly allocated.
// validConverters is the number of valid converters found in the inputBuffer, and it also
// represents the length of validSpecsFound array.
// Note that FORMAT_TYPE_UNK and FORMAT_TYPE_PER ('%%') do NOT count as valid converters!
SO_EXPORT int StreamFormatVerifier(
    const char *inputBuffer, const int32_t maxArgs, int32_t *const validConverters, ConvSpec *const validSpecsFound)
{
    int status = FORMAT_STATUS_OK;

    // Initial check on the input buffer
    if ((inputBuffer == NULL) || (validSpecsFound == NULL) || (validConverters == NULL)) {
        return FORMAT_STATUS_NULL_PTR_ERROR;
    }
    if (maxArgs < 0) {
        return FORMAT_STATUS_NEGATIVE_MAX_CS;
    }

    *validConverters = 0;

    // Main loop
    while (*inputBuffer != 0) {
        // Look for a '%' in the input string
        if (*inputBuffer != '%') {
            inputBuffer++;
            continue;
        }

        // We found a '%', let's parse the format specifier: it should be like
        // %[flags][width][.precision][length_mod]type
        ConvSpec tempCS;
        status = StreamFormatGetNextCS(&inputBuffer, &tempCS);
        if (status != FORMAT_STATUS_OK) {
            // Something unrecoverable happened, so exit prematurely and return the error code
            return status;
        }

        if (((uint32_t)(tempCS.type) & FORMAT_TYPE_UNK) == 0 && ((uint32_t)(tempCS.type) & FORMAT_TYPE_PER) == 0) {
            // Bound check on the output array
            if ((*validConverters) == maxArgs) {
                return FORMAT_STATUS_TOO_MANY_CONV_SPECS;
            }
            validSpecsFound[(*validConverters)++] = tempCS;
        }
    }
    return FORMAT_STATUS_OK;
}

// Following: all the functions to output a specific type on a buffer
// They all take the base pointer of the output string and the offset where to start writing.
// The offset is modified - incremented by the effective number of chars written

// 'width' defines the size of the box where to copy the char
// '-' flag sets left justification inside the box
int StreamFormatWriteChar(const char val, const ConvSpec *const conversionSpec, const int32_t outBufReminder,
    char *const outputBuffer, int32_t *const outbufPtr)
{
    // Size check
    if (outBufReminder < 1) {
        return FORMAT_STATUS_BUFFER_OVERFLOW;
    }

    outputBuffer[(*outbufPtr)++] = val;

    return FORMAT_STATUS_OK;
}

// 'precision' defines the max num of chars to be printed, eventually truncating the input string
// 'width' defines the size of the box where to copy the string; it is never smaller than the string itself
// '-' flag sets left justification inside the box
// Writes to the outputBuffer are constantly checked to prevent buffer overflow
int StreamFormatWriteString(const char *const val, const ConvSpec *const conversionSpec, const int32_t outBufReminder,
    char *const outputBuffer, int32_t *const outbufPtr)
{
    // Size check
    if (outBufReminder < 1) {
        return FORMAT_STATUS_BUFFER_OVERFLOW;
    }
    if (val == NULL) {
        return FORMAT_STATUS_NULL_PTR_ERROR;
    }

    int32_t inStrLen = (int32_t)strnlen(val, 1024);

    // Evaluate how many chars of the string must be written (precision mod)
    // Truncate the string if precision is < than string length
    // Otherwise print all the string
    int32_t nToBeCopied;
    int32_t prec = (*conversionSpec).precision;
    if (prec >= 0) {
        nToBeCopied = inStrLen < prec ? inStrLen : prec;
    } else {
        nToBeCopied = inStrLen;
    }

    // Evaluate the box width: it should be at least as big as nToBeCopied
    // If width is bigger, we will insert some padding spaces
    int32_t width = (*conversionSpec).width;  // -1 by default
    int32_t actualWidth = nToBeCopied < width ? width : nToBeCopied;
    // Evaluate the number of padding space
    int32_t paddingSpaces = (actualWidth - nToBeCopied) > 0 ? (actualWidth - nToBeCopied) : 0;

    // Now check how many chars from the box can fit inside the buffer
    // (it's the minimum between the buffersize and the actual box width)
    // This prevents buffer overflow.
    int32_t availableSpace = outBufReminder - 1 < actualWidth ? outBufReminder - 1 : actualWidth;

    // Start writing characters on the output buffer
    // Take care of the additional spaces in case of right justification
    if (((uint32_t)((*conversionSpec).flags) & FORMAT_FLAGS_LEFT) == 0) {
        while (paddingSpaces-- != 0) {
            if (availableSpace-- < 0) {
                break;
            }
            outputBuffer[(*outbufPtr)++] = ' ';
        }
    }
    // Copy the string
    int32_t t = 0;
    while (nToBeCopied-- != 0) {
        if (availableSpace-- < 0) {
            break;
        }
        outputBuffer[(*outbufPtr)++] = val[t++];
    }
    // Take care of the additional spaces in case of left justification
    if (((uint32_t)((*conversionSpec).flags) & FORMAT_FLAGS_LEFT) != 0) {
        while (paddingSpaces-- != 0) {
            if (availableSpace-- < 0) {
                break;
            }
            outputBuffer[(*outbufPtr)++] = ' ';
        }
    }

    return FORMAT_STATUS_OK;
}

// Helper functions for StreamFormatWriteInt and StreamFormatWriteUInt
int StreamFormatGetBase(const ConvSpec *const conversionSpec)
{
    switch ((*conversionSpec).actualChr) {
        case 'b':
            return STREAM_FORMAT_BASE_2;
        case 'o':
            return STREAM_FORMAT_BASE_8;
        case 'x':
        case 'X':
            return STREAM_FORMAT_BASE_16;
        case 'd':
        case 'i':
        case 'u':
        default:
            return STREAM_FORMAT_BASE_10;
    }
}

int32_t StreamFormatGetBasicLengthU(uint64_t val, const int32_t base)
{
    int32_t basicLen = 0;
    uint64_t v = val;
    do {
        v /= (uint64_t)base;
        basicLen++;
    } while (v != 0);
    return basicLen;
}

int32_t StreamFormatGetBasicLength(int64_t val, const int32_t base)
{
    int32_t basicLen = 0;
    int64_t v = val;
    do {
        v /= (int64_t)base;
        basicLen++;
    } while (v != 0);
    return basicLen;
}

const char *StreamFormatGetAlphabet(int32_t flags)
{
    const char *lowerCaseDigits = "0123456789abcdef";
    const char *upperCaseDigits = "0123456789ABCDEF";
    if (((uint32_t)(flags)&FORMAT_FLAGS_UPCS) != 0) {
        return upperCaseDigits;
    } else {
        return lowerCaseDigits;
    }
}

void StreamFormatWritePaddingSpaces(
    char *const outputBuffer, int32_t *const outbufPtr, FormatSpaceFields *const formatSpace)
{
    while ((*formatSpace).paddingSpaces-- != 0) {
        if ((*formatSpace).availableSpace-- <= 0) {
            break;
        }
        outputBuffer[(*outbufPtr)++] = ' ';
    }
}

FormatSpaceFields StreamFormatEvaluateFieldsInt(
    const ConvSpec *const conversionSpec, int32_t basicLen, const int64_t val)
{
    FormatSpaceFields temp;
    int32_t prec = (*conversionSpec).precision;
    int32_t width = (*conversionSpec).width;
    int32_t nToBeCopied = basicLen;
    temp.paddingZeros = (prec - nToBeCopied) > 0 ? (prec - nToBeCopied) : 0;
    // Figuring out if the optional signs should steal chars from the padding spaces or zeroes was a mess...
    nToBeCopied +=
        temp.paddingZeros + (int32_t)((uint32_t)(val < 0) | ((uint32_t)((*conversionSpec).flags) & FORMAT_FLAGS_PLUS) |
                                      ((uint32_t)((*conversionSpec).flags) & FORMAT_FLAGS_SPCE));
    temp.paddingSpaces = (width - nToBeCopied) > 0 ? (width - nToBeCopied) : 0;
    return temp;
}

FormatSpaceFields StreamFormatEvaluateFieldsUInt(
    const ConvSpec *const conversionSpec, int32_t basicLen, const int64_t val)
{
    FormatSpaceFields temp;
    int32_t prec = (*conversionSpec).precision;
    int32_t width = (*conversionSpec).width;
    int32_t nToBeCopied = basicLen;
    temp.paddingZeros = (prec - nToBeCopied) > 0 ? (prec - nToBeCopied) : 0;
    nToBeCopied += temp.paddingZeros;
    temp.paddingSpaces = (width - nToBeCopied) > 0 ? (width - nToBeCopied) : 0;

    return temp;
}

int StreamFormatManageHashFlag(uint64_t val, const ConvSpec *const conversionSpec, char *const outputBuffer,
    int32_t *const outbufPtr, FormatSpaceFields *const formatSpaces)
{
    if (((uint32_t)((*conversionSpec).flags) & FORMAT_FLAGS_HASH) != 0 && (val != 0)) {
        if ((*conversionSpec).actualChr != 'u') {
            outputBuffer[(*outbufPtr)++] = '0';
            (*formatSpaces).availableSpace--;
            if ((*formatSpaces).availableSpace <= 0) {
                return FORMAT_STATUS_BUFFER_OVERFLOW;
            }
        }
        if ((*conversionSpec).actualChr == 'x') {
            outputBuffer[(*outbufPtr)++] = 'x';
            (*formatSpaces).availableSpace--;
        } else if ((*conversionSpec).actualChr == 'X') {
            outputBuffer[(*outbufPtr)++] = 'X';
            (*formatSpaces).availableSpace--;
        } else if ((*conversionSpec).actualChr == 'b') {
            outputBuffer[(*outbufPtr)++] = 'b';
            (*formatSpaces).availableSpace--;
        }
    }
    return FORMAT_STATUS_OK;
}

int StreamFormatManageOptionalIntFlags(const int64_t val, const ConvSpec *const conversionSpec,
    char *const outputBuffer, int32_t *const outbufPtr, FormatSpaceFields *const formatSpaces)
{
    if (val < 0) {
        outputBuffer[(*outbufPtr)++] = '-';
        (*formatSpaces).availableSpace--;
    } else if (((uint32_t)((*conversionSpec).flags) & FORMAT_FLAGS_PLUS) != 0) {
        outputBuffer[(*outbufPtr)++] = '+';
        (*formatSpaces).availableSpace--;
    } else if (((uint32_t)((*conversionSpec).flags) & FORMAT_FLAGS_SPCE) != 0) {
        outputBuffer[(*outbufPtr)++] = ' ';
        (*formatSpaces).availableSpace--;
    }

    if ((*formatSpaces).availableSpace <= 0) {
        return FORMAT_STATUS_BUFFER_OVERFLOW;
    }

    return FORMAT_STATUS_OK;
}

// This is basically a safer itoa(), with boundary checks (output always gets truncated if there's not enough space)
// Also, all the itoa implementations that I found allocate a temporary buffer for the conversion, execute the divisions
// storing the intermediate results in the temp buffer (divisions start from the LSB), and finally reverse the string in
// the temporary buffer. This version does not use any of that, with the additional cost of one extra round of divisions
// for evaluating the actual number of chars needed to store the complete conversion, and use this info to eventually
// truncate the output. Following a set of features and flags and their current status.
// "-" justify left instead of right inside the field (OK)
// "+" plus sign is always prepended when non negative (OK)
// ' ' space sign is always prepended when non negative (OK)
// '0' 0s instead of spaces are prepended to all the field length. (NOT SUPPORTED)
//     Ignored if '-' is set. Ignored if precision is set. (OK)
// Width mod: specifies the minimum field width (the value is never truncated) (OK)
// Precision: specifies the minimum number of digits to appear (OK)
// 'd' and 'i': decimal conversion (OK)
// 'o': octal conversion (OK)
//      '#' alternate flag always writes a leading zero (OK)
// 'x': hexadecimal conversion - letters abcdef are used (OK)
//      '#' alternate flag always writes a leading '0x' (OK)
// 'X': hexadecimal conversion - letters ABCDEF are used (OK)
//      '#' alternate flag always writes a leading '0X' (OK)
// 'b': binary conversion (not in C11 standard) (OK)
//      '#' alternate flag always writes a leading '0b' (OK)
int StreamFormatWriteUInt(uint64_t val, const ConvSpec *const conversionSpec, const int32_t outBufReminder,
    char *const outputBuffer, int32_t *const outbufPtr)
{
    // Size check
    if (outBufReminder < 1) {
        return FORMAT_STATUS_BUFFER_OVERFLOW;
    }

    // Set the appropriate base, according to the character used in the conversion specifier
    int32_t base = StreamFormatGetBase(conversionSpec);

    // Get the number of required chars by repeatedly dividing the input val by the base value
    int32_t basicLen = StreamFormatGetBasicLengthU(val, base);

    // Evaluate how many characters, zeros and spaces should be written in the string:
    FormatSpaceFields formatSpaces = StreamFormatEvaluateFieldsUInt(conversionSpec, basicLen, (int64_t)val);
    formatSpaces.availableSpace = outBufReminder;

    // Evaluate the greatest divider and start from this one
    uint64_t divider = 1;
    for (int32_t i = 0; i < basicLen - 1; i++) {
        divider *= (uint64_t)base;
    }

    const char *digits = StreamFormatGetAlphabet((*conversionSpec).flags);

    // Start writing characters on the output buffer
    // Take care of the additional spaces in case of right justification
    if (!(((uint32_t)((*conversionSpec).flags) & FORMAT_FLAGS_LEFT) != 0)) {
        StreamFormatWritePaddingSpaces(outputBuffer, outbufPtr, &formatSpaces);
    }
    if (formatSpaces.availableSpace <= 0) {
        return FORMAT_STATUS_BUFFER_OVERFLOW;
    }

    // Manage the hash flag
    if (StreamFormatManageHashFlag(val, conversionSpec, outputBuffer, outbufPtr, &formatSpaces) ==
        FORMAT_STATUS_BUFFER_OVERFLOW) {
        return FORMAT_STATUS_BUFFER_OVERFLOW;
    }

    while (formatSpaces.paddingZeros-- != 0) {
        if (formatSpaces.availableSpace-- <= 0) {
            break;
        }
        outputBuffer[(*outbufPtr)++] = '0';
    }

    // Ready to output chars now
    uint64_t v = val;
    while ((divider != 0) && (formatSpaces.availableSpace-- > 0)) {
        const uint8_t digit = (uint8_t)(v / divider);
        outputBuffer[(*outbufPtr)++] = digits[digit];
        v -= (digit * divider);
        divider /= (uint64_t)base;
    }

    // Take care of the additional spaces in case of left justification
    if (((uint32_t)((*conversionSpec).flags) & FORMAT_FLAGS_LEFT) != 0) {
        StreamFormatWritePaddingSpaces(outputBuffer, outbufPtr, &formatSpaces);
    }

    return FORMAT_STATUS_OK;
}

int StreamFormatWriteInt(int64_t val, const ConvSpec *const conversionSpec, const int32_t outBufReminder,
    char *const outputBuffer, int32_t *const outbufPtr)
{
    // Size check
    if (outBufReminder < 1) {
        return FORMAT_STATUS_BUFFER_OVERFLOW;
    }

    // Set the appropriate base, according to the character used in the conversion specifier
    int32_t base = StreamFormatGetBase(conversionSpec);

    // Get the number of required chars by repeatedly dividing the input val by the base value
    int32_t basicLen = StreamFormatGetBasicLength(val, base);

    // Evaluate how many characters, zeros and spaces should be written in the string:
    FormatSpaceFields formatSpaces = StreamFormatEvaluateFieldsInt(conversionSpec, basicLen, val);
    formatSpaces.availableSpace = outBufReminder;

    // Evaluate the greatest divider and start from this one
    int64_t divider = 1;
    for (int32_t i = 0; i < basicLen - 1; i++) {
        divider *= base;
    }

    // Set the alphabet (upper or lower case)
    const char *digits = StreamFormatGetAlphabet((*conversionSpec).flags);

    // Start writing characters on the output buffer
    // Take care of the additional spaces in case of right justification
    if (((uint32_t)((*conversionSpec).flags) & FORMAT_FLAGS_LEFT) == 0) {
        StreamFormatWritePaddingSpaces(outputBuffer, outbufPtr, &formatSpaces);
    }
    if (formatSpaces.availableSpace <= 0) {
        return FORMAT_STATUS_BUFFER_OVERFLOW;
    }

    // Write the optional sign or space
    if (StreamFormatManageOptionalIntFlags(val, conversionSpec, outputBuffer, outbufPtr, &formatSpaces) ==
        FORMAT_STATUS_BUFFER_OVERFLOW) {
        return FORMAT_STATUS_BUFFER_OVERFLOW;
    };

    while (formatSpaces.paddingZeros-- != 0) {
        if (formatSpaces.availableSpace-- <= 0) {
            break;
        }
        outputBuffer[(*outbufPtr)++] = '0';
    }

    // Ready to output chars now
    int64_t v = (val > 0) ? val : -val;
    while (divider != 0 && (formatSpaces.availableSpace-- > 0)) {
        const uint8_t digit = (uint8_t)(v / divider);
        outputBuffer[(*outbufPtr)++] = digits[digit];
        v -= (digit * divider);
        divider /= base;
    }

    // Take care of the additional spaces in case of left justification
    if (((uint32_t)((*conversionSpec).flags) & FORMAT_FLAGS_LEFT) != 0) {
        StreamFormatWritePaddingSpaces(outputBuffer, outbufPtr, &formatSpaces);
    }

    return FORMAT_STATUS_OK;
}

#ifdef __cplusplus
}
#endif
