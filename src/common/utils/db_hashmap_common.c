/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: db_hashmap_common.h
 * Description: Implement of hashmap and shmhashmap common related functions
 * Author:
 * Create: 2023-7-21
 */

#include "gmc_errno.h"
#include "adpt_memory.h"
#include "db_hashmap_common.h"

uint32_t DbOamapGetNearPrime(uint32_t capacity)
{
    static const uint32_t PRIME[] = {7, 13, 31, 61, 127, 251, 509, 1021, 1297, 2039, 4093, 8191, 16381, 32749, 65521,
        131071, 262139, 524287, 1048573, 2097143, 4194301, 8388593, 16777213, 33554393, 67108859, 134217689, 268435399,
        536870909, 1073741789, 2147483647, 0xfffffffb};

    const uint32_t count = (uint32_t)ELEMENT_COUNT(PRIME);
    uint32_t left = 0;           // prime[i] (i < left) cannot be result
    uint32_t right = count - 1;  // prime[i] (i >= right) can be result

    do {
        uint32_t mid = left + ((right - left) >> 1);
        if (capacity > PRIME[mid]) {
            left = mid + 1;
        } else {
            right = mid;
        }
    } while (left != right);

    // if capacity is greater than all values in prime[], will return prime[count - 1]
    return PRIME[left];
}
