/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2019-2020. All rights reserved.
 * File Name: db_hashmap.c
 * Description: Implement of hashmap-related functions
 * Author:
 * Create: 2020-8-21
 */
#include <string.h>
#include "gmc_errno.h"
#include "adpt_memory.h"
#include "common_log.h"
#include "db_hashmap.h"

typedef struct {
    uint32_t index;
    uint32_t hash;
    const void *key;
    bool findFirstEmptyBucket;
} OamapCheckArgsT;

typedef struct {
    uint32_t hash;
    bool findFirstEmptyBucket;
    void *key;
    void *value;
} OamapInsertArgsT;

inline static uint32_t DbOamapBucketHashMask(void)
{
    // returns max value that DbOamapBucketT::hash can hold
    // if DbOamapBucketT::hash is a bitfield, this will be useful
    DbOamapBucketT bucket = {};
    bucket.hash = DB_INVALID_UINT32;  // 最大整数
    return bucket.hash;
}

static void DbOamapBucketZeroOut(DbOamapBucketT *bucket)
{
    bucket->state = FREE;
    bucket->hash = 0;
    bucket->key = NULL;
    bucket->value = NULL;
}

static void DbOamapZeroOut(DbOamapT *map)
{
    DbOamapT zero = {};
    *map = zero;
}

static DbOamapBucketT *DbOamapAllocBucket(DbMemCtxT *memCtx, uint32_t count)
{
    // calc size
    const uint32_t scale = (uint32_t)sizeof(DbOamapBucketT);
    const uint32_t max = UINT32_MAX / scale;
    if (count > max) {
        return NULL;
    }
    uint32_t size = count * scale;

    // alloc memory
    DbOamapBucketT *buckets = NULL;
    if (memCtx != NULL) {
        // 内存释放点:DbOamapFreeBucket
        buckets = DbDynMemCtxAlloc(memCtx, size);
    } else {
        buckets = DB_MALLOC(size);
    }
    if (buckets == NULL) {
        return NULL;
    }

    // initialize buckets
    for (uint32_t index = 0; index < count; ++index) {
        DbOamapBucketT *bucket = &buckets[index];
        DbOamapBucketZeroOut(bucket);
    }
    return buckets;
}

static void DbOamapFreeBucket(DbMemCtxT *memCtx, DbOamapBucketT *buckets)
{
    if (memCtx != NULL) {
        DbDynMemCtxFree(memCtx, buckets);
    } else {
        DB_FREE(buckets);
    }
}

static void DbOamapInsertBucket(DbOamapT *map, DbOamapBucketT *bucket, uint32_t hash, void *key, void *value)
{
    bucket->state = USED;
    bucket->hash = hash;
    bucket->key = key;
    bucket->value = value;
    map->size++;
}

static void *DbOamapRemoveBucket(DbOamapT *map, DbOamapBucketT *bucket)
{
    void *value = bucket->value;
    bucket->state = DELETED;
    bucket->hash = 0;
    bucket->key = NULL;
    bucket->value = NULL;
    map->size--;
    return value;
}

Status DbOamapInit(DbOamapT *map, uint32_t capacity, DbOamapCompareT comparator, DbMemCtxT *memCtx, bool extendable)
{
    DB_POINTER2(map, comparator);

    DbRWSpinInit(&map->rwLock);
    map->extendable = extendable;
    map->memCtx = memCtx;
    map->size = 0;
    map->capacity = DbOamapGetNearPrime(capacity);
    map->buckets = DbOamapAllocBucket(map->memCtx, map->capacity);
    map->compareFunc = comparator;

    if (map->buckets == NULL) {
        DbOamapZeroOut(map);
        // Failed to alloc memory while initializing hash map
        return GMERR_OUT_OF_MEMORY;
    }

    return GMERR_OK;
}

void DbOamapDestroy(DbOamapT *map)
{
    // Destroy必须保证是可重入
    if (map == NULL) {
        return;
    }
    if (map->buckets != NULL) {
        DbOamapFreeBucket(map->memCtx, map->buckets);
    }
    DbOamapZeroOut(map);
}

void DbOamapClear(DbOamapT *map)
{
    DB_POINTER(map);

    for (uint32_t index = 0; index < map->capacity; ++index) {
        DbOamapBucketT *bucket = &map->buckets[index];
        DbOamapBucketZeroOut(bucket);
    }

    map->size = 0;
}

// 性能路径强制内联
ALWAYS_INLINE static bool DbOamapCheckBucket(const DbOamapT *map, OamapCheckArgsT *checkArgs, DbOamapBucketT **bk)
{
    DbOamapBucketT *bucket = &map->buckets[checkArgs->index];

    // remember first candidate, but we need to search further to ensure that no duplicate is found
    if (bucket->state == DELETED) {
        if (*bk == NULL) {
            *bk = bucket;
        }
        return checkArgs->findFirstEmptyBucket;
    }

    // no duplicate is found, time to return
    if (bucket->state == FREE) {
        if (*bk == NULL) {
            *bk = bucket;
        }
        return true;
    }

    // duplicate is found, time to return
    if ((checkArgs->hash == bucket->hash) && map->compareFunc(checkArgs->key, bucket->key) != 0) {
        *bk = bucket;
        return true;
    }

    return false;
}

static DbOamapBucketT *DbOamapFind(const DbOamapT *map, uint32_t hash, const void *key, bool findFirstEmptyBucket)
{
    // capacity在Map初始化的时候保证不为0
    uint32_t capacity = map->capacity;
    uint32_t remain = hash % capacity;
    DbOamapBucketT *bucket = NULL;

    OamapCheckArgsT checkArgs = {.index = 0, .hash = hash, .key = key, .findFirstEmptyBucket = findFirstEmptyBucket};
    for (checkArgs.index = remain; checkArgs.index < capacity;) {
        if (DbOamapCheckBucket(map, &checkArgs, &bucket)) {
            return bucket;
        }
        ++checkArgs.index;
    }

    for (checkArgs.index = remain; checkArgs.index > 0;) {
        --checkArgs.index;
        if (DbOamapCheckBucket(map, &checkArgs, &bucket)) {
            return bucket;
        }
    }

    return bucket;
}

static Status DbOamapRehash(DbOamapT *map, uint32_t newCapacity)
{
    uint32_t oldCapacity = map->capacity;
    DbOamapBucketT *oldBuckets = map->buckets;

    DbOamapBucketT *newBuckets = DbOamapAllocBucket(map->memCtx, newCapacity);
    if (newBuckets == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    map->capacity = newCapacity;
    map->buckets = newBuckets;

    for (uint32_t index = 0; index < oldCapacity; ++index) {
        DbOamapBucketT *oldBucket = &oldBuckets[index];
        if (oldBucket->state == USED) {
            DbOamapBucketT *newBucket = DbOamapFind(map, oldBucket->hash, oldBucket->key, false);
            // because size <= newCapacity, newBucket should never be NULL
            if (newBucket == NULL) {
                continue;
            }
            *newBucket = *oldBucket;
        }
    }

    DbOamapFreeBucket(map->memCtx, oldBuckets);
    return GMERR_OK;
}

Status DbOamapExtend(DbOamapT *map)
{
    DB_POINTER(map);
    if (map->extendable && (map->size + (map->size >> 1) >= map->capacity)) {
        // capacity can't be greater than DbOamapAllocBucket::max, so map->capacity + 1 will never overflow
        uint32_t capacity = DbOamapGetNearPrime(map->capacity + 1);
        Status ret = DbOamapRehash(map, capacity);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status DbOamapInsertInner(DbOamapT *map, OamapInsertArgsT *insertArgs, uint32_t *mapIdx)
{
    uint32_t hashVar = insertArgs->hash & DbOamapBucketHashMask();

    Status result = DbOamapExtend(map);
    DB_UNUSED(result);

    DbOamapBucketT *bucket = DbOamapFind(map, hashVar, insertArgs->key, insertArgs->findFirstEmptyBucket);
    if (bucket == NULL) {
        // hash map is full and cannot extend capacity
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (bucket->state == USED) {
        // duplicate key error while inserting into hash map
        return GMERR_DUPLICATE_OBJECT;
    }

    if (mapIdx != NULL) {
        // warning: follow the old logic, mapIdx is assigned <=> return code is GMERR_OK
        *mapIdx = (uint32_t)(bucket - map->buckets);
    }
    DbOamapInsertBucket(map, bucket, hashVar, insertArgs->key, insertArgs->value);
    return GMERR_OK;
}

Status DbOamapInsert(DbOamapT *map, uint32_t hash, void *key, void *value, uint32_t *mapIdx)
{
    // value值可能为空，不检查
    DB_POINTER2(map, key);
    OamapInsertArgsT insertArgs = {.hash = hash, .findFirstEmptyBucket = false, .key = key, .value = value};
    return DbOamapInsertInner(map, &insertArgs, mapIdx);
}

Status DbOamapInsertNoDupKeyWithLock(DbOamapT *map, uint32_t hash, void *key, void *value, uint32_t *mapIdx)
{
    // value值可能为空，不检查
    DB_POINTER2(map, key);
    OamapInsertArgsT insertArgs = {.hash = hash, .findFirstEmptyBucket = true, .key = key, .value = value};
    DbRWSpinWLockWithoutLog(&map->rwLock);  // 涉及日志模块，使用不打印超时日志锁
    Status result = DbOamapInsertInner(map, &insertArgs, mapIdx);
    DbRWSpinWUnlock(&map->rwLock);
    return result;
}

Status DbOamapInsertWithLock(DbOamapT *map, uint32_t hash, void *key, void *value, uint32_t *mapIdx)
{
    DB_POINTER2(map, key);
    DbRWSpinWLockWithoutLog(&map->rwLock);  // 涉及日志模块，使用不打印超时日志锁
    Status result = DbOamapInsert(map, hash, key, value, mapIdx);
    DbRWSpinWUnlock(&map->rwLock);
    return result;
}

void *DbOamapLookupKey(const DbOamapT *map, uint32_t hash, const void *key)
{
    DB_POINTER2(map, key);
    uint32_t hashVar = hash & DbOamapBucketHashMask();

    const DbOamapBucketT *bucket = DbOamapFind(map, hashVar, key, false);
    if (bucket != NULL && bucket->state == USED) {
        return bucket->key;
    }

    return NULL;
}

void *DbOamapLookup(const DbOamapT *map, uint32_t hash, const void *key, uint32_t *mapIdx)
{
    DB_POINTER2(map, key);
    uint32_t hashVar = hash & DbOamapBucketHashMask();

    const DbOamapBucketT *bucket = DbOamapFind(map, hashVar, key, false);

    if (bucket != NULL && bucket->state == USED) {
        if (mapIdx != NULL) {
            // warning: follow the old logic, mapIdx is assigned <=> value is found
            *mapIdx = (uint32_t)(bucket - map->buckets);
        }
        return bucket->value;
    }

    return NULL;
}

#ifdef FEATURE_GQL
void DbOamapBatchLookup(const DbOamapT *map, void *handle, GetNextHashFuncT getNextHash, uint32_t batchNum, void **slot)
{
    DB_POINTER4(map, handle, getNextHash, slot);
    uint32_t hash = 0;
    for (uint32_t i = 0; i < batchNum; i++) {
        hash = getNextHash(handle, i);
        slot[i] = DbOamapLookup(map, hash, (const void *)&hash, NULL);
    }
}
#endif

void *DbOamapLookupWithLock(DbOamapT *map, uint32_t hash, const void *key, uint32_t *mapIdx)
{
    DB_POINTER2(map, key);
    DbRWSpinRLockWithoutLog(&map->rwLock);  // 涉及日志模块，使用不打印超时日志锁
    void *result = DbOamapLookup(map, hash, key, mapIdx);
    DbRWSpinRUnlock(&map->rwLock);
    return result;
}

void *DbOamapRemove(DbOamapT *map, uint32_t hash, const void *key)
{
    DB_POINTER2(map, key);
    uint32_t hashVar = hash & DbOamapBucketHashMask();

    DbOamapBucketT *bucket = DbOamapFind(map, hashVar, key, false);
    if (bucket != NULL && bucket->state == USED) {
        return DbOamapRemoveBucket(map, bucket);
    }

    return NULL;
}

void *DbOamapRemoveWithLock(DbOamapT *map, uint32_t hash, const void *key)
{
    DB_POINTER2(map, key);
    DbRWSpinWLock(&map->rwLock);
    void *result = DbOamapRemove(map, hash, key);
    DbRWSpinWUnlock(&map->rwLock);
    return result;
}

Status DbOamapRemoveKv(DbOamapT *map, uint32_t hash, const void *key, void **mapKey, void **value)
{
    DB_POINTER4(map, key, mapKey, value);
    uint32_t hashVar = hash & DbOamapBucketHashMask();

    DbOamapBucketT *bucket = DbOamapFind(map, hashVar, key, false);
    if (bucket != NULL && bucket->state == USED) {
        *mapKey = bucket->key;
        *value = DbOamapRemoveBucket(map, bucket);
        return GMERR_OK;
    }

    return GMERR_NO_DATA;
}

Status DbOamapRemoveKvWithLock(DbOamapT *map, uint32_t hash, const void *key, void **mapKey, void **value)
{
    DB_POINTER4(map, key, mapKey, value);
    DbRWSpinWLock(&map->rwLock);
    Status result = DbOamapRemoveKv(map, hash, key, mapKey, value);
    DbRWSpinWUnlock(&map->rwLock);
    return result;
}

void DbOamapResetIterator(DbOamapIteratorT *iter)
{
    DB_POINTER(iter);
    *iter = 0;
}

void *DbOamapLookupByIdx(const DbOamapT *map, uint32_t mapIdx)
{
    DB_POINTER(map);

    if (mapIdx >= map->capacity) {
        return NULL;
    }

    const DbOamapBucketT *bucket = &map->buckets[mapIdx];
    if (bucket->state == USED) {
        return bucket->value;
    }

    return NULL;
}

void *DbOamapRemoveByIdx(DbOamapT *map, uint32_t mapIdx)
{
    DB_POINTER(map);
    if (mapIdx >= map->capacity) {
        return NULL;
    }

    DbOamapBucketT *bucket = &map->buckets[mapIdx];
    if (bucket->state == USED) {
        return DbOamapRemoveBucket(map, bucket);
    }

    return NULL;
}

Status DbOamapFetch(const DbOamapT *map, DbOamapIteratorT *iter, void **key, void **value)
{
    DB_POINTER4(map, iter, key, value);

    for (uint32_t index = *iter; index < map->capacity;) {
        const DbOamapBucketT *bucket = &map->buckets[index];
        ++index;
        if (bucket->state == USED) {
            *iter = index;
            *key = bucket->key;
            *value = bucket->value;
            return GMERR_OK;
        }
    }

    *iter = map->capacity;
    *key = NULL;
    *value = NULL;
    return GMERR_NO_DATA;  // end of container reached
}

Status DbOamapFetchWithLock(DbOamapT *map, DbOamapIteratorT *iter, void **key, void **value)
{
    DB_POINTER4(map, iter, key, value);
    DbRWSpinRLockWithoutLog(&map->rwLock);  // 涉及日志模块，使用不打印超时日志锁
    Status result = DbOamapFetch(map, iter, key, value);
    DbRWSpinRUnlock(&map->rwLock);
    return result;
}

uint32_t DbOamapPtrCompare(const void *key1, const void *key2)
{
    return (key1 == key2);
}

uint32_t DbOamapUint64Compare(const void *key1, const void *key2)
{
    DB_POINTER2(key1, key2);
    return (*(const uint64_t *)key1 == *(const uint64_t *)key2);
}

uint32_t DbOamapUint32Compare(const void *key1, const void *key2)
{
    DB_POINTER2(key1, key2);
    return (*(const uint32_t *)key1 == *(const uint32_t *)key2);
}

uint32_t DbOamapUint16Compare(const void *key1, const void *key2)
{
    DB_POINTER2(key1, key2);
    return (*(const uint16_t *)key1 == *(const uint16_t *)key2);
}

uint32_t DbOamapStringCompare(const void *key1, const void *key2)
{
    DB_POINTER2(key1, key2);
    return (strcmp(key1, key2) == 0);
}
