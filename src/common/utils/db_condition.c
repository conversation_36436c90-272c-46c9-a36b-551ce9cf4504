/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 * File Name: db_condition.c
 * Description: Implementation of condition variable
 * Author: GQL
 * Create: 2022-10-24
 *
 * Implementation reference:
 *      Paper:"Implementing Condition Variables with Semaphores"
 */

#if defined(FEATURE_GQL)
#include "db_condition.h"
#include "adpt_log.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

Status DbCondInit(DbConditionT *cond)
{
    DB_POINTER(cond);

    Status ret = DbSemInit(&(cond->sem), THREAD_SEMAPHORE, 0);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Init Condition Variables.");
        return ret;
    }

    DbSpinInit(&(cond->cntLock));
    cond->waitCnt = 0;
    return GMERR_OK;
}

Status DbCondWait(DbConditionT *cond, DbSpinLockT *lock)
{
    DB_POINTER2(cond, lock);

    DbSpinLock(&(cond->cntLock));
    cond->waitCnt++;
    DbSpinUnlock(&(cond->cntLock));

    DbSpinUnlock(lock);
    Status ret = DbSemWait(&(cond->sem));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Wait Condition Variables.");
    }
    DbSpinLock(lock);
    return ret;
}

Status DbCondTimedWait(DbConditionT *cond, DbSpinLockT *lock, uint32_t timeoutUs)
{
    DB_POINTER2(cond, lock);

    DbSpinLock(&(cond->cntLock));
    cond->waitCnt++;
    DbSpinUnlock(&(cond->cntLock));

    DbSpinUnlock(lock);
    Status ret = DbSemTimedWait(&(cond->sem), timeoutUs);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Wait Condition Variables.");
    }
    DbSpinLock(lock);
    return ret;
}

Status DbCondSignal(DbConditionT *cond)
{
    DB_POINTER(cond);
    Status ret = GMERR_OK;

    DbSpinLock(&(cond->cntLock));
    if (cond->waitCnt > 0) {
        cond->waitCnt--;
        ret = DbSemPost(&(cond->sem));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Post Condition Variables signal.");
        }
    }

    DbSpinUnlock(&(cond->cntLock));
    return ret;
}

Status DbCondBroadcast(DbConditionT *cond)
{
    DB_POINTER(cond);

    DbSpinLock(&(cond->cntLock));
    // Notify all waiting threads
    for (; cond->waitCnt > 0; cond->waitCnt--) {
        Status ret = DbSemPost(&(cond->sem));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Broadcast Condition Variables.");
            DbSpinUnlock(&(cond->cntLock));
            return ret;
        }
    }

    DbSpinUnlock(&(cond->cntLock));
    return GMERR_OK;
}

Status DbCondDestroy(DbConditionT *cond)
{
    DB_POINTER(cond);

    return DbSemDestroy(&(cond->sem));
}

#ifdef __cplusplus
}
#endif  /* __cplusplus */
#endif  // FEATURE_GQL
