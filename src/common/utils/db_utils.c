/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: db_utils.h
 * Description: Implement of db common utils functions
 * Author:
 * Create: 2020-7-27
 */

#include "adpt_types.h"
#include "adpt_thread.h"
#include "db_file.h"
#include "adpt_process_id.h"
#if defined(__aarch64__) && !defined(HPE)
#include <arm_acle.h>
#endif
#if defined(__x86_64__) && !defined(HPE)
#include <x86intrin.h>
#endif
#include "db_hash.h"
#include "adpt_string.h"
#include "db_utils.h"

#ifdef __cplusplus
extern "C" {
#endif
#define DB_CRC_SHIFT_BYTE 8
#define HMAC_KEY_LEN_MAX 112
#define HMAC_KEY_LEN_MIN 8
Status DbCheckHmacKeyComplexity(const char *key, uint32_t keyLen)
{
    DB_POINTER(key);
    if (keyLen < HMAC_KEY_LEN_MIN || keyLen > HMAC_KEY_LEN_MAX) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    uint32_t flag = 0;
    for (uint32_t i = 0; i < keyLen; i++) {
        char c = key[i];
        if (IsLower(c)) {  // 包含a-z
            flag |= 0b0001;
        } else if (IsUpper(c)) {  // 包含A-Z
            flag |= 0b0010;
        } else if (IsNumChar(c)) {  // 包含0-9
            flag |= 0b0100;
        } else if (IsPunct(c) || (c == ' ')) {  // 包含特殊字符
            flag |= 0b1000;
        } else {
            return GMERR_INVALID_PARAMETER_VALUE;
        }
    }
    uint16_t n = 0;
    while (flag != 0) {
        flag &= (flag - 1);
        n++;
    }
    // 至少包含2种字符
    return n >= 2 ? GMERR_OK : GMERR_INVALID_PARAMETER_VALUE;
}

Status DbMakeProcessBackground(void)
{
    int32_t pid = DbAdptFork();
    if (pid == 0) {
        return DbAdptSetpgid(0, 0);
    } else if (pid < 0) {
        return GMERR_INTERNAL_ERROR;
    } else {
        exit(0);
    }
}

void DbRightTrim(char *text)
{
    if (text == NULL) {
        return;
    }
    uint32_t len;
    uint8_t *tmpBuf = (uint8_t *)text;

    len = (uint32_t)strlen(text);
    if (len == 0) {
        return;
    }
    tmpBuf = tmpBuf + (len - 1);
    while ((tmpBuf >= (uint8_t *)text) && (*tmpBuf <= ' ')) {
        tmpBuf--;
    }
    tmpBuf[1] = '\0';
}

char *DbLeftTrim(char *text)
{
    if (text == NULL) {
        return NULL;
    }
    uint8_t *tmpBuf = (uint8_t *)text;

    while ((*tmpBuf != '\0') && (*tmpBuf <= ' ')) {
        tmpBuf++;
    }
    return (char *)tmpBuf;
}

#ifndef HPE
bool DbCheckProcessIsExist(uint32_t pid)
{
    // rtos下面暂不用
    return true;
}
#else
bool DbCheckProcessIsExist(uint32_t pid)
{
    // hpe下暂不支持
    return true;
}
#endif

char *DbGetFileNameInfo(char *fileName, uint32_t filePathLen, FileOptTypeE type)
{
    DB_POINTER(fileName);
    char *lastPos = NULL;
    char *leftPtr = NULL;
    char *token = strtok_r(fileName, "/", &leftPtr);
    while (token != NULL) {
        lastPos = token;
        token = strtok_r(NULL, "/", &leftPtr);
    }
    if (lastPos == NULL) {
        return NULL;
    }
    size_t leftLen = strlen(lastPos);
    while (leftLen > 0) {
        leftLen--;
        if (lastPos[leftLen] == '.') {
            lastPos[leftLen] = '\0';
            break;
        }
    }
    if (leftLen == 0) {
        return NULL;
    }
    FileOptTypeE optType = type;
    if (optType == DB_FILENAME_PREFIX) {
        return lastPos;
    } else if (optType == DB_FILENAME_SUFFIX) {
        return &lastPos[leftLen + 1];
    }
    lastPos[leftLen] = '.';
    return lastPos;
}

static uint32_t CRC32Inner(char *data, uint32_t length, uint32_t crcInput)
{
    uint32_t len = length;
    uint32_t crc = crcInput;
#if defined(__aarch64__) && !defined(HPE)
    while (len >= sizeof(uint8_t)) {
        crc = __crc32b(crc, *((uint8_t *)data));
        data += sizeof(uint8_t);
        len -= sizeof(uint8_t);
    }
#else
    int k;

    while (len-- != 0) {
        // Ensure char is converted to uint8_t to process non-ascii values correctly
        crc ^= (uint8_t)*data++;
        for (k = 0; k < CHAR_BIT; k++) {
            crc = (crc >> 1) ^ (0xEDB88320 & (0 - (crc & 1)));
        }
    }
#endif
    return crc;
}

uint32_t DbCRC32(char *data, uint32_t length)
{
    uint32_t crc = CRC32Inner(data, length, (uint32_t)DB_MAX_UINT32);
    return ~crc;
}

static uint32_t CRC32InnerLegacy(char *data, uint32_t length, uint32_t crcInput)
{
    uint32_t crc = crcInput;
    uint32_t strLen = length;
    char *str = data;
    while (strLen-- != 0) {
        // A conversion error happens here in the legacy version
        // crc will be overwritten completely instead of the 8 rightmost bits
        int32_t temp = (int32_t)(*str);
        crc ^= (uint32_t)temp;  // crc ^= *str; str++;
        str += 1;
        for (uint32_t i = 0; i < (uint32_t)BYTE_LENGTH; ++i) {
            if ((crc & 1) != 0) {
                crc = (crc >> 1) ^ (uint32_t)0xEDB88320;  // 0xEDB88320= reverse 0x04C11DB7
            } else {
                crc = (crc >> 1);
            }
        }
    }
    return crc;
}

uint32_t DbCRC32Legacy(char *data, uint32_t length)
{
    uint32_t crc = CRC32InnerLegacy(data, length, (uint32_t)DB_MAX_UINT32);
    return ~crc;
}

static uint32_t CrcOrHashInner(char *keyData, uint32_t keyLen)
{
    uint8_t *data = (uint8_t *)keyData;
    uint32_t len = keyLen;
    uint32_t hashCode = 0;
#if defined(__aarch64__) && !defined(HPE)
    uint32_t crc = 0;
    while (len >= sizeof(uint64_t)) {
        hashCode = __crc32d(crc, *((uint64_t *)data));
        data += sizeof(uint64_t);
        len -= sizeof(uint64_t);
    }
    if ((len & sizeof(uint32_t)) != 0) {
        hashCode = __crc32w(hashCode, *((uint32_t *)data));
        data += sizeof(uint32_t);
    }
    if ((len & sizeof(uint16_t)) != 0) {
        hashCode = __crc32h(hashCode, *((uint16_t *)data));
        data += sizeof(uint16_t);
    }
    if ((len & sizeof(uint8_t)) != 0) {
        hashCode = __crc32b(hashCode, *((uint8_t *)data));
    }
#elif defined(__x86_64__) && !defined(HPE)
    uint64_t crcCode = 0;
    while (len >= sizeof(uint64_t)) {
        hashCode = (uint32_t)__crc32q(crcCode, *((uint64_t *)(void *)data));
        data += sizeof(uint64_t);
        len -= sizeof(uint64_t);
    }
    if ((len & sizeof(uint32_t)) != 0) {
        hashCode = __crc32d(hashCode, *((uint32_t *)(void *)data));
        data += sizeof(uint32_t);
    }
    if ((len & sizeof(uint16_t)) != 0) {
        hashCode = __crc32w(hashCode, *((uint16_t *)(void *)data));
        data += sizeof(uint16_t);
    }
    if ((len & sizeof(uint8_t)) != 0) {
        hashCode = __crc32b(hashCode, *((uint8_t *)data));
    }
#else
    hashCode = DbHash32(data, len);
#endif
    return hashCode;
}

uint32_t DbCrcOrHash(char *data, uint32_t length)
{
    uint32_t checkCode = CrcOrHashInner(data, length);
    return ~checkCode;
}

static const uint32_t CRC32_TABLE[256] = {0x0, 0x77073096, 0xee0e612c, 0x990951ba, 0x76dc419, 0x706af48f, 0xe963a535,
    0x9e6495a3, 0xedb8832, 0x79dcb8a4, 0xe0d5e91e, 0x97d2d988, 0x9b64c2b, 0x7eb17cbd, 0xe7b82d07, 0x90bf1d91,
    0x1db71064, 0x6ab020f2, 0xf3b97148, 0x84be41de, 0x1adad47d, 0x6ddde4eb, 0xf4d4b551, 0x83d385c7, 0x136c9856,
    0x646ba8c0, 0xfd62f97a, 0x8a65c9ec, 0x14015c4f, 0x63066cd9, 0xfa0f3d63, 0x8d080df5, 0x3b6e20c8, 0x4c69105e,
    0xd56041e4, 0xa2677172, 0x3c03e4d1, 0x4b04d447, 0xd20d85fd, 0xa50ab56b, 0x35b5a8fa, 0x42b2986c, 0xdbbbc9d6,
    0xacbcf940, 0x32d86ce3, 0x45df5c75, 0xdcd60dcf, 0xabd13d59, 0x26d930ac, 0x51de003a, 0xc8d75180, 0xbfd06116,
    0x21b4f4b5, 0x56b3c423, 0xcfba9599, 0xb8bda50f, 0x2802b89e, 0x5f058808, 0xc60cd9b2, 0xb10be924, 0x2f6f7c87,
    0x58684c11, 0xc1611dab, 0xb6662d3d, 0x76dc4190, 0x1db7106, 0x98d220bc, 0xefd5102a, 0x71b18589, 0x6b6b51f,
    0x9fbfe4a5, 0xe8b8d433, 0x7807c9a2, 0xf00f934, 0x9609a88e, 0xe10e9818, 0x7f6a0dbb, 0x86d3d2d, 0x91646c97,
    0xe6635c01, 0x6b6b51f4, 0x1c6c6162, 0x856530d8, 0xf262004e, 0x6c0695ed, 0x1b01a57b, 0x8208f4c1, 0xf50fc457,
    0x65b0d9c6, 0x12b7e950, 0x8bbeb8ea, 0xfcb9887c, 0x62dd1ddf, 0x15da2d49, 0x8cd37cf3, 0xfbd44c65, 0x4db26158,
    0x3ab551ce, 0xa3bc0074, 0xd4bb30e2, 0x4adfa541, 0x3dd895d7, 0xa4d1c46d, 0xd3d6f4fb, 0x4369e96a, 0x346ed9fc,
    0xad678846, 0xda60b8d0, 0x44042d73, 0x33031de5, 0xaa0a4c5f, 0xdd0d7cc9, 0x5005713c, 0x270241aa, 0xbe0b1010,
    0xc90c2086, 0x5768b525, 0x206f85b3, 0xb966d409, 0xce61e49f, 0x5edef90e, 0x29d9c998, 0xb0d09822, 0xc7d7a8b4,
    0x59b33d17, 0x2eb40d81, 0xb7bd5c3b, 0xc0ba6cad, 0xedb88320, 0x9abfb3b6, 0x3b6e20c, 0x74b1d29a, 0xead54739,
    0x9dd277af, 0x4db2615, 0x73dc1683, 0xe3630b12, 0x94643b84, 0xd6d6a3e, 0x7a6a5aa8, 0xe40ecf0b, 0x9309ff9d, 0xa00ae27,
    0x7d079eb1, 0xf00f9344, 0x8708a3d2, 0x1e01f268, 0x6906c2fe, 0xf762575d, 0x806567cb, 0x196c3671, 0x6e6b06e7,
    0xfed41b76, 0x89d32be0, 0x10da7a5a, 0x67dd4acc, 0xf9b9df6f, 0x8ebeeff9, 0x17b7be43, 0x60b08ed5, 0xd6d6a3e8,
    0xa1d1937e, 0x38d8c2c4, 0x4fdff252, 0xd1bb67f1, 0xa6bc5767, 0x3fb506dd, 0x48b2364b, 0xd80d2bda, 0xaf0a1b4c,
    0x36034af6, 0x41047a60, 0xdf60efc3, 0xa867df55, 0x316e8eef, 0x4669be79, 0xcb61b38c, 0xbc66831a, 0x256fd2a0,
    0x5268e236, 0xcc0c7795, 0xbb0b4703, 0x220216b9, 0x5505262f, 0xc5ba3bbe, 0xb2bd0b28, 0x2bb45a92, 0x5cb36a04,
    0xc2d7ffa7, 0xb5d0cf31, 0x2cd99e8b, 0x5bdeae1d, 0x9b64c2b0, 0xec63f226, 0x756aa39c, 0x26d930a, 0x9c0906a9,
    0xeb0e363f, 0x72076785, 0x5005713, 0x95bf4a82, 0xe2b87a14, 0x7bb12bae, 0xcb61b38, 0x92d28e9b, 0xe5d5be0d,
    0x7cdcefb7, 0xbdbdf21, 0x86d3d2d4, 0xf1d4e242, 0x68ddb3f8, 0x1fda836e, 0x81be16cd, 0xf6b9265b, 0x6fb077e1,
    0x18b74777, 0x88085ae6, 0xff0f6a70, 0x66063bca, 0x11010b5c, 0x8f659eff, 0xf862ae69, 0x616bffd3, 0x166ccf45,
    0xa00ae278, 0xd70dd2ee, 0x4e048354, 0x3903b3c2, 0xa7672661, 0xd06016f7, 0x4969474d, 0x3e6e77db, 0xaed16a4a,
    0xd9d65adc, 0x40df0b66, 0x37d83bf0, 0xa9bcae53, 0xdebb9ec5, 0x47b2cf7f, 0x30b5ffe9, 0xbdbdf21c, 0xcabac28a,
    0x53b39330, 0x24b4a3a6, 0xbad03605, 0xcdd70693, 0x54de5729, 0x23d967bf, 0xb3667a2e, 0xc4614ab8, 0x5d681b02,
    0x2a6f2b94, 0xb40bbe37, 0xc30c8ea1, 0x5a05df1b, 0x2d02ef8d};

uint32_t DBTableCRC32(char *data, uint32_t length)
{
    uint32_t crc = 0xffffffff;
    uint8_t *byteArray = (uint8_t *)data;
    for (uint32_t i = 0; i < length; i++) {
        crc = CRC32_TABLE[(crc ^ byteArray[i]) & 0xFF] ^ (crc >> DB_CRC_SHIFT_BYTE);
    }
    return ~crc;
}

#ifdef EXPERIMENTAL_NERGC
#define PBKDF2_RANDOM_DEV_PATH "/dev/urandom"
Status DbSecureRandBytes(uint8_t *buff, uint32_t buffLen)
{
    DB_POINTER(buff);
    int32_t fd = -1;
    Status ret = DbOpenFile(PBKDF2_RANDOM_DEV_PATH, O_RDONLY, O_RDONLY, &fd);
    if (ret != GMERR_OK) {
        return GMERR_FILE_OPERATE_FAILED;
    }
    size_t nread = 0;
    do {
        size_t tmpCnt = 0;
        ret = DbReadFile(fd, buff + nread, buffLen - nread, &tmpCnt);
        nread += tmpCnt;
        if (ret != GMERR_OK) {
            DbCloseFile(fd);
            return GMERR_FILE_OPERATE_FAILED;
        }
    } while (nread != buffLen);
    DbCloseFile(fd);
    return GMERR_OK;
}
#endif

#ifdef __cplusplus
}
#endif
