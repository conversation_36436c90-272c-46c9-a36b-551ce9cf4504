/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2021. All rights reserved.
 * File Name: Dbcharset.c
 * Description: Implement of character set
 * Author:
 * Create: 2020-1-20
 */

#include "db_charset.h"
#include "common_log.h"

#ifdef __cplusplus
extern "C" {
#endif

void DbStrRemoveFirstN(const char **str, uint32_t cnt)
{
    uint32_t count = cnt;
    while (count > 0) {
        (*str)++;
        count--;
    }
}

void DbStrPopFirst(char **str, char *out)
{
    (*out) = (*str)[0];
    (*str)++;
};

const DbCharSetFuncT *DbGetCharSetFunc(DbCharSetTypeE type)
{
    /* order should by same as 'charset_type_t' */
    static const DbCharSetFuncT charSetFunc[(int32_t)DB_CHARSET_MAX] = {
        {
            DbUtf8StrBytes,
            DbUtf8ReverseStrBytes,
            DbUtf8TextLike,
            DbUtf8TextLikeEscape,
        },
        {NULL, NULL, NULL, NULL},
    };

    return &charSetFunc[type];
}

static Status DbTextFindWildcard(const char **str, const char *strEnd, char **wildStr, const char *wildEnd, char escape,
    int32_t *cmpRet, bool *needReturn, DbCharSetTypeE type)
{
    char sChar, wChar;
    uint32_t sBytes;

    while (*wildStr != wildEnd) {
        bool escaped = false;
        wChar = (*wildStr)[0];

        if (wChar == '%') {
            break;
        }

        (*wildStr)++;

        if (wChar == escape) {
            if (*wildStr == wildEnd) {
                DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "the wildStr is null when find wildcard");
                return GMERR_DATA_EXCEPTION;
            }
            DbStrPopFirst(wildStr, &wChar);
            escaped = true;
        }

        if (*str == strEnd) {
            *cmpRet = -1;
            *needReturn = true;
            return GMERR_OK;
        }

        sChar = (*str)[0];
        if ((escaped || wChar != '_') && sChar != wChar) {
            *cmpRet = 1;
            *needReturn = true;
            return GMERR_OK;
        }
        /* no escaped wild char,str need to skip a char(may include multi bytes) */
        sBytes = 1;
        if (!escaped && wChar == '_') {
            Status ret =
                (*DbGetCharSetFunc(type)).strBytes(*str, (uint32_t)((uintptr_t)*str - (uintptr_t)strEnd), &sBytes);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        DbStrRemoveFirstN(str, sBytes);
    }
    if (*wildStr == wildEnd) {
        *cmpRet = (*str != strEnd);
        *needReturn = true;
    }
    return GMERR_OK;
}

static bool DbTextRemoveWildcards(
    const char **str, const char *strEnd, char **wildStr, const char *wildEnd, int32_t *cmpRet, DbCharSetTypeE type)
{
    char wChar;
    uint32_t sBytes;

    for (; *wildStr != wildEnd;) {
        wChar = (*wildStr)[0];
        if (wChar == '%') {
            (*wildStr)++;
            continue;
        }

        if (wChar == '_') {
            (*wildStr)++;
            if ((*DbGetCharSetFunc(type)).strBytes(*str, (uint32_t)((uintptr_t)strEnd - (uintptr_t)*str), &sBytes) !=
                GMERR_OK) {
                *cmpRet = -1;
                return true;
            }
            DbStrRemoveFirstN(str, sBytes);
            continue;
        }
        break;
    }
    return false;
}

/* find str2 in str1 , match_len1 returns how many bytes are matched in str1. */
static int32_t DbInLike(
    const char *str1, uint32_t len1, const char *str2, uint32_t len2, uint32_t *matchLen1, DbCharSetTypeE type)
{
    uint32_t charLen1;
    uint32_t i1;
    uint32_t i2 = 0;
    uint32_t pos;

    for (i1 = 0; i1 < len1;) {
        // try compare str1 at i1 same as str2 ?
        // '_' means any one character
        if (str2[0] == '_' || str1[i1] == str2[0]) {
            pos = i1;
            for (i2 = 0; i2 < len2;) {
                if (i1 >= len1) {
                    break;
                }

                if (str2[i2] != '_' && str1[i1] != str2[i2]) {
                    break;
                }
                // move i1 to next character head position.
                if (str2[i2] == '_') {
                    if ((*DbGetCharSetFunc(type)).strBytes(str1 + i1, len1 - i1, &charLen1) != GMERR_OK) {
                        return -1;
                    }
                    i1 += charLen1;
                } else {
                    i1 += 1;
                }
                i2 += 1;
            }
            if (i2 == len2) {
                *matchLen1 = i1 - pos;
                return (int32_t)pos;
            }
            i1 = pos;  // resume i1
        }

        if (str2[0] == '_') {
            if ((*DbGetCharSetFunc(type)).strBytes(str1 + i1, len1 - i1, &charLen1) != GMERR_OK) {
                return -1;
            }
            i1 += charLen1;
        } else {
            i1 += 1;
        }
    }

    return -1;
}

bool DbTextCompareFirstPiece(
    const TextT *text1, const TextT *text2, DbCharSetTypeE type, uint32_t *pos2, uint32_t *pos1)
{
    /*
     * compare the first piece of text2(piece is split by %) to text1
     * eg. text1 is 'abcdefg' text2 is 'a%c%e%g'
     * below is compare text1 to text2-part 'a'
     */

    uint32_t i1;
    uint32_t i2;
    for (i2 = 0, i1 = 0; i2 < text2->len;) {
        if (text2->str[i2] == '%') {
            *pos2 = i2 + 1;
            break;
        }

        /* compare text one by one char */
        if (i2 >= text1->len) {
            return false;
        }

        if (text1->str[i1] == text2->str[i2]) {
            i1++;
            i2++;
            continue;
        }

        if (text2->str[i2] == '_') {
            uint32_t len1;
            if ((*DbGetCharSetFunc(type)).strBytes(text1->str + i1, text1->len - i1, &len1) != GMERR_OK) {
                return false;
            }
            i1 += len1;
            i2++;
            continue;
        }

        return false;
    }

    *pos1 = i1;
    if (*pos2 == 0) {
        return (text1->len == i1 && text2->len == i2);
    }

    return true;
}

bool DbTextCompareMiddlePiece(
    const TextT *text1, const TextT *text2, DbCharSetTypeE type, uint32_t *pos1, uint32_t *pos2)
{
    /*
     * compare the middle piece of text2(piece is split by %) to text1
     * eg. text1 is 'abcdefg' text2 is 'a%c%e%g'
     * below is compare text1 to text2-part 'c' & 'e'
     */

    int32_t pos;
    for (uint32_t i2 = *pos2; i2 < text2->len; i2++) {
        if (text2->str[i2] == '%') {
            if (i2 > *pos2) {
                uint32_t partLen1;
                uint32_t partLen2 = i2 - *pos2;

                pos = DbInLike(text1->str + *pos1, (text1->len - *pos1), text2->str + *pos2, partLen2, &partLen1, type);
                if (pos < 0) {
                    return false;
                }
                *pos1 += (uint32_t)pos + partLen1;
            }

            *pos2 = i2 + 1;
        }
    }

    return true;
}

bool DbTextCompareLastPiece(const TextT *text1, const TextT *text2, DbCharSetTypeE type, uint32_t partLen2)
{
    /*
     * compare the last piece of text2(piece is split by %) to text1
     * eg. text1 is 'abcdefg' text2 is 'a%c%e%g'
     * below is compare text1 to text2-part 'g'
     */

    uint32_t len1;
    uint32_t i1 = text1->len - 1;
    uint32_t i2 = text2->len - 1;
    for (; i2 >= text2->len - partLen2;) {
        if (text1->str[i1] == text2->str[i2]) {
            i1--;
            i2--;
            continue;
        }

        if (text2->str[i2] == '_') {
            if ((*DbGetCharSetFunc(type)).reverseStrBytes(text1->str + i1, i1 + 1, &len1) != GMERR_OK) {
                return false;
            }
            i1 -= len1;
            i2--;
            continue;
        }

        return false;
    }

    return true;
}

bool DbTextLike(const TextT *text1, const TextT *text2, DbCharSetTypeE type)
{
    DB_POINTER2(text1, text2);
    uint32_t pos1;
    uint32_t pos2 = 0;

    bool isLike = DbTextCompareFirstPiece(text1, text2, type, &pos2, &pos1);
    if (pos2 == 0) {
        return isLike;
    }
    if (isLike == false) {
        return false;
    }

    if (DbTextCompareMiddlePiece(text1, text2, type, &pos1, &pos2) == false) {
        return false;
    }

    uint32_t partLen2 = text2->len - pos2;
    if (text1->len < partLen2 + pos1) {
        return false;
    }

    return DbTextCompareLastPiece(text1, text2, type, partLen2);
}

Status DbTextLikeEscape(const char *strInput, const char *strEnd, char *wildStr, const char *wildEnd, char escape,
    int32_t *cmpRet, DbCharSetTypeE type)
{
    DB_POINTER4(strEnd, wildStr, wildEnd, cmpRet);
    char sChar, wChar;
    bool needReturn = false;
    const char *str = strInput;

    while (wildStr != wildEnd) {
        Status ret = DbTextFindWildcard(&str, strEnd, &wildStr, wildEnd, escape, cmpRet, &needReturn, type);
        if (ret != GMERR_OK || needReturn == true) {
            return ret;
        }

        if (DbTextRemoveWildcards(&str, strEnd, &wildStr, wildEnd, cmpRet, type)) {
            return GMERR_OK;
        }

        if (wildStr == wildEnd || str == strEnd) {
            *cmpRet = ((wildStr == wildEnd) ? 0 : -1);
            return GMERR_OK;
        }

        DbStrPopFirst(&wildStr, &wChar);
        if (wChar == escape) {
            if (wildStr == wildEnd) {
                DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Null wildstr for like escape");
                return GMERR_DATA_EXCEPTION;
            }
            DbStrPopFirst(&wildStr, &wChar);
        }

        for (;;) {
            while (str != strEnd) {
                sChar = str[0];
                if (sChar == wChar) {
                    break;
                }
                str++;
            }
            if (str == strEnd) {
                *cmpRet = -1;
                return GMERR_OK;
            }
            str++;
            ret = DbTextLikeEscape(str, strEnd, wildStr, wildEnd, escape, cmpRet, type);
            if (ret != GMERR_OK) {
                return ret;
            }
            if (*cmpRet <= 0) {
                return GMERR_OK;
            }
        }
    }
    *cmpRet = ((str != strEnd) ? 1 : 0);
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
