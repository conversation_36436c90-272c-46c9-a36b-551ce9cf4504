/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: db_text.c
 * Description: Implement of string-related functions
 * Author: wangwei
 * Create: 2020-10-26
 */

#include "db_text.h"
#include "db_mem_context.h"
#include "securec.h"
#include "gmc_errno.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

static uint8_t X2N(uint8_t c)
{
    // 并发方案：不涉及并发，资源只读
    const uint8_t hexToNumMap[DB_HEX_TO_NUM_MAP_LEN] = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x0b, 0x0c,
        0x0d, 0x0e, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
    return hexToNumMap[c];
}

int32_t DbCmpText(const TextT *text1, const TextT *text2)
{
    DB_POINTER2(text1, text2);
    uint32_t i, cmpLen;
    unsigned char c1, c2;

    cmpLen = (text1->len < text2->len) ? text1->len : text2->len;
    for (i = 0; i < cmpLen; i++) {
        c1 = (unsigned char)text1->str[i];
        c2 = (unsigned char)text2->str[i];

        if (c1 > c2) {
            return 1;
        } else if (c1 < c2) {
            return -1;
        }
    }
    return (text1->len > text2->len) ? 1 : ((text1->len == text2->len) ? 0 : -1);
}

int32_t DbCmpTextIns(const TextT *text1, const TextT *text2)
{
    DB_POINTER2(text1, text2);
    uint32_t i, cmpLen;
    unsigned char c1, c2;

    cmpLen = (text1->len < text2->len) ? text1->len : text2->len;
    for (i = 0; i < cmpLen; i++) {
        c1 = (unsigned char)UPPER(text1->str[i]);
        c2 = (unsigned char)UPPER(text2->str[i]);
        if (c1 > c2) {
            return 1;
        } else if (c1 < c2) {
            return -1;
        }
    }
    return (text1->len > text2->len) ? 1 : ((text1->len == text2->len) ? 0 : -1);
}

int32_t DbCmpTextStr(const TextT *text, const char *str)
{
    DB_POINTER2(text, str);
    uint32_t i;
    unsigned char c1, c2;
    for (i = 0; i < text->len; i++) {
        if (str[i] == '\0') {
            return 1;
        }
        c1 = (unsigned char)text->str[i];
        c2 = (unsigned char)str[i];

        if (c1 > c2) {
            return 1;
        } else if (c1 < c2) {
            return -1;
        }
    }
    return (str[i] == '\0') ? 0 : -1;
}

// will add '\0' to text.str, and text.len
Status DbCopyStrToText(void *memCtx, TextT *dstText, const char *str, const uint32_t strLen)
{
    DB_POINTER2(memCtx, dstText);
    if (str == NULL || strLen == 0) {
        dstText->str = NULL;
        dstText->len = 0;
        return GMERR_OK;
    }

    // 内存释放点:无，只能利用memCtx统一释放
    dstText->str = DbDynMemCtxAlloc(memCtx, strLen + 1);
    if (SECUREC_UNLIKELY(dstText->str == NULL)) {
        return GMERR_OUT_OF_MEMORY;
    }
    dstText->len = strLen + 1;
    errno_t secChk = strcpy_s(dstText->str, dstText->len, str);
    if (SECUREC_UNLIKELY(secChk != EOK)) {
        DbDynMemCtxFree(memCtx, dstText->str);
        dstText->str = NULL;
        return GMERR_OUT_OF_MEMORY;
    }
    dstText->str[strLen] = 0x00;
    return GMERR_OK;
}

int32_t DbCmpDigitext(DigitextT *dtext1, DigitextT *dtext2)
{
    DB_POINTER2(dtext1, dtext2);
    TextT text1 = {dtext1->len, dtext1->str};
    TextT text2 = {dtext2->len, dtext2->str};
    return DbCmpText(&text1, &text2);
}

int32_t DbCalcSignificandExpn(int32_t dotOffset, int32_t precOffset, int32_t precision)
{
    int32_t offset = dotOffset;
    if (offset >= 0) {
        offset -= precOffset;
        return ((offset > 0) ? (offset - 1) : offset);
    } else {
        return precision - 1;
    }
}

void DbRecordDigit(NumPartT *np, int32_t *precision, int32_t *precOffset, int32_t pos, char c)
{
    DB_POINTER3(np, precision, precOffset);
    if (*precision >= 0) {
        ++(*precision);
        if (*precision == (DB_MAX_NUMERIC_BUFF + 1)) {
            np->doRound = (c >= '5');
            return;
        }
    } else {
        *precision = 1;
    }

    if (*precision == 1) {
        *precOffset = pos;
    }
    DB_TEXT_APPEND(&np->digitText, c);
}

static void DbKmpMatchGetNext(const TextT *subText, int32_t *next)
{
    int32_t i = 1;
    int32_t j = 0;
    next[0] = 0;
    while ((uint32_t)i < subText->len) {
        if (j == 0 || subText->str[i - 1] == subText->str[j - 1]) {
            i++;
            j++;
            next[i] = j;
        } else {
            j = next[j];
        }
    }
}

static bool DbKmpMatch(const TextT *text, const TextT *subText, const int32_t *next)
{
    int32_t i = 1;
    int32_t j = 1;
    while ((uint32_t)i <= text->len && (uint32_t)j <= subText->len) {
        if (j == 0 || text->str[i - 1] == subText->str[j - 1]) {
            i++;
            j++;
        } else {
            j = next[j];
        }
    }
    return (uint32_t)j > subText->len;
}

int32_t DbTextContains(void *memCtx, const TextT *text, const TextT *subText, bool *isContains)
{
    DB_POINTER4(memCtx, text, subText, isContains);
    if (text->len < subText->len) {
        *isContains = false;
        return GMERR_OK;
    }

    uint32_t nextLen = (uint32_t)((subText->len + 1) * sizeof(int32_t));
    int32_t *next = DbDynMemCtxAlloc(memCtx, nextLen);
    if (next == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(next, nextLen, 0x00, nextLen);
    DbKmpMatchGetNext(subText, next);
    *isContains = DbKmpMatch(text, subText, next);
    DbDynMemCtxFree(memCtx, next);
    return GMERR_OK;
}

void DbRtrimText(TextT *text)
{
    int32_t index;

    if (text->str == NULL || text->len == 0) {
        text->len = 0;
        return;
    }

    index = (int32_t)text->len - 1;
    while (index >= 0) {
        if ((char)text->str[index] > (char)' ') {
            text->len = (uint32_t)(index + 1);
            return;
        }
        --index;
    }
}

void DbLtrimText(TextT *text)
{
    if (text->str == NULL || text->len == 0) {
        text->len = 0;
        return;
    }

    while (text->len > 0) {
        if ((char)*text->str > ' ') {
            break;
        }
        text->str++;
        text->len--;
    }
}

void DbTrimText(TextT *text)
{
    DB_POINTER(text);
    DbLtrimText(text);
    DbRtrimText(text);
}

void DbHexTextToBytes(const TextT *hexText, bool hasPrefix, uint8_t *bytes, uint32_t maxSize, uint32_t *size)
{
    DB_POINTER3(hexText, bytes, size);
    uint32_t hexPos = 0;
    uint32_t binPos = 0;
    char *str = hexText->str;
    if (hasPrefix) {
        hexPos += DB_COMMON_HEX_PREFIX_LEN;
    }

    if ((hexText->len % 2) != 0) { /* 2: odd or even */
        binPos = 1;
        bytes[0] = X2N((uint8_t)(str[hexPos]));
        hexPos++;
    }

    while (hexPos < hexText->len - 1 && binPos < maxSize) {
        bytes[binPos] = (uint8_t)(X2N((uint8_t)str[hexPos]) << DB_HEX_TO_NUM_HIGH_BIT_OFFSET);
        bytes[binPos] |= X2N((uint8_t)(str[hexPos + 1]));
        hexPos += DB_COMMON_HEX_PREFIX_LEN;
        binPos++;
    }
    *size = binPos;
}

bool DbBufferIsAllZero(uint8_t *buf, size_t len)
{
    for (size_t i = 0; i < len; i++) {
        if (buf[i] != 0) {
            return false;
        }
    }
    return true;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
