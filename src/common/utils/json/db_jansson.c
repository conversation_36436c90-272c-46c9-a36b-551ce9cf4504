/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: jansson common
 * Author:
 * Create: 2024-07-11
 */

#include "db_json_common.h"
#include "db_log.h"
#include "jansson.h"

DbJsonTypeE DbJsonGetType(const DbJsonT *item)
{
    if (SECUREC_UNLIKELY(item == NULL)) {
        return DB_JSON_INVALID;
    }
    switch (json_typeof((const json_t *)item)) {
        case JSON_FALSE:
            return DB_JSON_FALSE;
        case JSON_TRUE:
            return DB_JSON_TRUE;
        case JSON_NULL:
            return DB_JSON_NULL;
        case JSON_INTEGER:
            return DB_JSON_INTEGER;
        case JSON_REAL:
            return DB_JSON_REAL;
        case JSON_STRING:
            return DB_JSON_STRING;
        case JSON_ARRAY:
            return DB_JSON_ARRAY;
        case JSON_OBJECT:
            return DB_JSON_OBJECT;
        default:
            return DB_JSON_INVALID;
    }
    return DB_JSON_INVALID;
}

bool DbJsonIsObject(const DbJsonT *const item)
{
    return (bool)json_is_object((const json_t *)item);
}

bool DbJsonIsArray(const DbJsonT *const item)
{
    return (bool)json_is_array((const json_t *)item);
}

bool DbJsonIsString(const DbJsonT *const item)
{
    return (bool)json_is_string((const json_t *)item);
}

bool DbJsonIsInteger(const DbJsonT *const item)
{
    return (bool)json_is_integer((const json_t *)item);
}

bool DbJsonIsReal(const DbJsonT *const item)
{
    return (bool)json_is_real((const json_t *)item);
}

bool DbJsonIsNumber(const DbJsonT *const item)
{
    return (bool)json_is_number((const json_t *)item);
}

bool DbJsonIsTrue(const DbJsonT *const item)
{
    return (bool)json_is_true((const json_t *)item);
}

bool DbJsonIsBool(const DbJsonT *const item)
{
    return (bool)json_is_boolean((const json_t *)item);
}

DbJsonT *DbJsonCreateObject(void)
{
    return (DbJsonT *)json_object();
}

DbJsonT *DbJsonCreateArray(void)
{
    return (DbJsonT *)json_array();
}

DbJsonT *DbJsonCreateString(const char *str)
{
    return (DbJsonT *)json_string(str);
}

DbJsonT *DbJsonCreateInteger(int64_t value)
{
    return (DbJsonT *)json_integer(value);
}

DbJsonT *DbJsonCreateReal(double value)
{
    return (DbJsonT *)json_real(value);
}

DbJsonT *DbJsonCreateBool(bool value)
{
    return (DbJsonT *)json_boolean(value);
}

DbJsonT *DbJsonCreateNull(void)
{
    return (DbJsonT *)json_null();
}

void DbJsonDelete(DbJsonT *item)
{
    json_decref((json_t *)item);
}

DbJsonT *DbJsonIncref(DbJsonT *item)
{
    return (DbJsonT *)json_incref((json_t *)item);
}

DbJsonT *DbJsonObjectGet(const DbJsonT *item, const char *str)
{
    return (DbJsonT *)json_object_get((const json_t *)item, str);
}

Status DbJsonObjectSetNew(DbJsonT *object, const char *key, DbJsonT *value)
{
    int ret = json_object_set_new((json_t *)object, key, (json_t *)value);
    if (SECUREC_UNLIKELY(ret != DB_JSON_OK)) {
        DB_LOG_ERROR(
            GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, "Unable to set new element to json object, Name is %s.", key);
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }
    return GMERR_OK;
}

void *DbJsonObjectIter(DbJsonT *item)
{
    return (void *)json_object_iter((json_t *)item);
}

void DbJsonDeleteObjectItem(DbJsonT *item, const char *key)
{
    return (void)json_object_del((json_t *)item, key);
}

const char *DbJsonObjectIterKey(DbJsonT *item)
{
    return (const char *)json_object_iter_key((void *)item);
}

void *DbJsonObjectIterNext(DbJsonT *item, DbJsonT *iter)
{
    return (void *)json_object_iter_next((json_t *)item, (void *)iter);
}

DbJsonT *DbJsonObjectIterValue(DbJsonT *iter)
{
    return (DbJsonT *)json_object_iter_value((void *)iter);
}

size_t DbJsonGetArraySize(const DbJsonT *array)
{
    return (size_t)json_array_size((const json_t *)array);
}

DbJsonT *DbJsonArrayGet(const DbJsonT *array, size_t index)
{
    return (DbJsonT *)json_array_get((const json_t *)array, index);
}

Status DbJsonArrayAppendNew(DbJsonT *array, DbJsonT *item)
{
    int ret = json_array_append_new((json_t *)array, (json_t *)item);
    if (SECUREC_UNLIKELY(ret != DB_JSON_OK)) {
        DB_LOG_ERROR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, "Unable to append new element to jansson array.");
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }
    return GMERR_OK;
}

int32_t DbJsonReplaceItemInArray(DbJsonT *array, int which, DbJsonT *newitem)
{
    int ret = json_array_set((json_t *)array, (size_t)which, (json_t *)newitem);
    if (SECUREC_UNLIKELY(ret != DB_JSON_OK)) {
        DB_LOG_ERROR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, "Unable to replace jansson array.");
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }
    return GMERR_OK;
}

const char *DbJsonStringValue(const DbJsonT *item)
{
    return (const char *)json_string_value((const json_t *)item);
}

int64_t DbJsonIntegerValue(const DbJsonT *item)
{
    return (int64_t)json_integer_value((const json_t *)item);
}

double DbJsonRealValue(const DbJsonT *item)
{
    return json_real_value((const json_t *)item);
}

double DbJsonNumberValue(const DbJsonT *item)
{
    return json_number_value((const json_t *)item);
}

bool DbJsonBooleanValue(const DbJsonT *item)
{
    return json_boolean_value((const json_t *)item);
}

DbJsonT *DbJsonDuplicate(DbJsonT *item, int recurse)
{
    if (recurse == 0) {
        return (DbJsonT *)json_copy((json_t *)item);
    } else {
        return (DbJsonT *)json_deep_copy((json_t *)item);
    }
}

char *DbJsonDumps(const DbJsonT *item, size_t flags)
{
    return json_dumps((const json_t *)item, flags);
}

DbJsonT *DbJsonLoads(const char *str, size_t flags)
{
    json_error_t mistake;
    DbJsonT *result = (DbJsonT *)json_loads(str, flags, &mistake);
    if (SECUREC_UNLIKELY(result == NULL)) {
        DB_LOG_ERROR(GMERR_INVALID_JSON_CONTENT,
            "DbJsonT loads not correct : line %" PRId32 " column %" PRId32 " position %" PRId32
            " source \"%s\" text \"%s\"",
            mistake.line, mistake.column, mistake.position, mistake.source, mistake.text);
    }
    return result;
}

DbJsonT *DbJsonLoadsFile(const char *path, size_t flags)
{
    json_error_t mistake;
    DbJsonT *result = (DbJsonT *)json_load_file(path, flags, &mistake);
    if (SECUREC_UNLIKELY(result == NULL)) {
        DB_LOG_ERROR(GMERR_INVALID_JSON_CONTENT,
            "json loads file not correct : line %" PRId32 " column %" PRId32 " position %" PRId32
            " source \"%s\" text \"%s\"",
            mistake.line, mistake.column, mistake.position, mistake.source, mistake.text);
    }
    return result;
}

void DbJsonFree(void *object)
{
    if (object == NULL) {
        return;
    }
    json_free_t freeFunc;
    json_get_alloc_funcs(NULL, &freeFunc);
    freeFunc((void *)object);
}
