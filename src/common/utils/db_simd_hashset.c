/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: db_simd_hashset.c
 * Description: Implement of simd hashset functions
 * Author:
 * Create: 2022-10-25
 */
#if defined(FEATURE_TS) || defined(FEATURE_HAC)
#if defined(__ARM_NEON__) || defined(__ARM_NEON)
#include <arm_neon.h>
#endif
#include "db_simd_hashset.h"

#define HASH_CODE_BATCH (8)

DbSimdHashSetT *DbSimdHashSetCreate(DbMemCtxT *memCtx, uint32_t capacity)
{
    DB_POINTER(memCtx);
    DB_ASSERT(capacity > 0);
    DbSimdHashSetT *hashSet = (DbSimdHashSetT *)DbDynMemCtxAlloc(memCtx, sizeof(DbSimdHashSetT));
    if (hashSet == NULL) {
        return NULL;
    }
    (void)memset_s(hashSet, sizeof(DbSimdHashSetT), 0, sizeof(DbSimdHashSetT));
    hashSet->count = 0;
    hashSet->cap = (capacity / HASH_CODE_BATCH + 1) * HASH_CODE_BATCH;
    hashSet->memCtx = memCtx;
    hashSet->data = (uint16_t *)DbDynMemCtxAlloc(memCtx, sizeof(uint16_t) * hashSet->cap);
    if (hashSet->data == NULL) {
        DbDynMemCtxFree(memCtx, hashSet);
        return NULL;
    }
    return hashSet;
}

void DbSimdHashSetDestroy(DbSimdHashSetT *hashSet)
{
    if (hashSet != NULL) {
        DbMemCtxT *memCtx = hashSet->memCtx;
        DbDynMemCtxFree(memCtx, hashSet->data);
        DbDynMemCtxFree(memCtx, hashSet);
    }
}

bool DbSimdHashSetContains(DbSimdHashSetT *hashSet, uint16_t hashCode)
{
    DB_POINTER(hashSet);
#if defined(__ARM_NEON__) || defined(__ARM_NEON)
    uint32_t batchCount = (hashSet->count == 0) ? 0 : ((hashSet->count - 1) / HASH_CODE_BATCH + 1);
    if (batchCount > 0) {
        uint64_t cmp64x2[2];
        uint16x8_t new16x8 = vdupq_n_u16(hashCode);
        for (uint32_t i = 0; i < batchCount; i++) {
            uint16x8_t old16x8 = vld1q_u16(&hashSet->data[i * HASH_CODE_BATCH]);
            uint16x8_t cmp16x8 = vceqq_u16(new16x8, old16x8);
            vst1q_u16((uint16_t *)cmp64x2, cmp16x8);
            if (cmp64x2[0] != 0 || cmp64x2[1] != 0) {
                return true;
            }
        }
    }
#else
    for (uint32_t i = 0; i < hashSet->count; i++) {
        if (hashSet->data[i] == hashCode) {
            return true;
        }
    }
#endif
    return false;
}

Status DbSimdHashSetInsert(DbSimdHashSetT *hashSet, uint16_t hashCode)
{
    DB_POINTER(hashSet);
    if (SECUREC_UNLIKELY(hashSet->count >= hashSet->cap)) {
        return GMERR_INTERNAL_ERROR;
    }
#if defined(__ARM_NEON__) || defined(__ARM_NEON)
    if (SECUREC_UNLIKELY(hashSet->count > hashSet->cap - HASH_CODE_BATCH)) {
        hashSet->data[hashSet->count] = hashCode;
    } else {
        uint16x8_t new16x8 = vdupq_n_u16(hashCode);
        vst1q_u16(&hashSet->data[hashSet->count], new16x8);
    }
#else
    hashSet->data[hashSet->count] = hashCode;
#endif
    hashSet->count++;
    return GMERR_OK;
}

void DbSimdHashSetClear(DbSimdHashSetT *hashSet)
{
    DB_POINTER(hashSet);
    hashSet->count = 0;
    return;
}

Status DbSimdHashSetUniqueInsert(DbSimdHashSetT *hashSet, uint16_t hashCode)
{
    if (!DbSimdHashSetContains(hashSet, hashCode)) {
        return DbSimdHashSetInsert(hashSet, hashCode);
    }
    return GMERR_OK;
}
#endif
