/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: 组件动态加载框架
 * Author: chenao
 * Create: 2022-08-30
 * Notes:
 */

#include "db_dyn_load.h"

#include "adpt_init.h"
#include "adpt_string.h"
#include "adpt_function_loader.h"
#include "db_config.h"
#include "db_hashmap.h"
#include "db_internal_error.h"

#define FUNC_MAP_INIT_CAPACITY 32
#define DYN_LOAD_BUILD_ALL_TYPE (char *)"build_all_type"

typedef struct DbDynLoadCtxT {
    DbOamapT funcMap;
    bool finishLoad;
    char featureLibPath[DB_MAX_PATH];
} DbDynLoadCtxT;

typedef struct DynLoadFuncMapKeyT {
    const char *feature;
    const char *subsystem;
} DynLoadFuncMapKeyT;

typedef struct DynLoadFuncMapValueT {
    void *soHandle;
    void *function;
    bool singleSoLoadFeature;
} DynLoadFuncMapValueT;

typedef struct DynLoadFuncCfgT {
    DynLoadFuncMapKeyT key;
    DynLoadFuncMapValueT value;
} DynLoadFuncCfgT;

typedef struct DynLoadFeatureDepT {
    char *feature;
    char *depFeatures;
} DynLoadFeatureDepT;

#if defined(COMMON_STATIC) || !defined(SINGLE_SO)
#define SET_DYNLOAD_FUNC(func) #func  // convert func to string
#else
// 不考虑静态工具(gmconvert, gmprecompiler等)在单一so场景下，使用编译时依赖 function pointer
#define SET_DYNLOAD_FUNC(func) func
#endif

#ifdef TS_MULTI_INST
#define SET_DYNLOAD_TS_FUNC(func) #func  // convert func to string
#else
// 不考虑静态工具(gmconvert, gmprecompiler等)在单一so场景下，使用编译时依赖 function pointer
#define SET_DYNLOAD_TS_FUNC(func) SET_DYNLOAD_FUNC(func)
#endif

// 依赖so加载递归调用最大深度为特性个数，此数组中特性个数最大不导致调用栈溢出
const DynLoadFeatureDepT g_featureDep[] = {
    {COMPONENT_DURABLE_MEMDATA, COMPONENT_PERSISTENCE}, {COMPONENT_BUFFER_POOL, COMPONENT_PERSISTENCE},
    {"datalog", "fusion"},
    /* support multiple features depency config
     support nested denency
     {"feature1", "feature2,feature3"}
     {"feature2", "feature2xx,feature2yy"}
     {"feature3", "feature3xx,feature3yy"} */
};

// 这部分属于组件化，在单so下，提前声明，便于map大数组能够直接存储函数指针
#if defined(SINGLE_SO) && !defined(COMMON_STATIC)
typedef struct TagSeInstanceT SeInstanceT;
StatusInter MdInit(SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx);
typedef struct TagPageMgrT PageMgrT;
StatusInter MdCreatePageMgr(SeInstanceT *seIns, DbMemCtxT *memCtx, PageMgrT **pageMgr);
void MdDestroyPageMgr(SeInstanceT *seIns, PageMgrT *pageMgr);
StatusInter DumemInit(SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx);
StatusInter DumemCreatePageMgr(SeInstanceT *seIns, DbMemCtxT *memCtx, PageMgrT **pageMgr);
void DumemDestroyPageMgr(SeInstanceT *seIns, PageMgrT *pageMgr);
typedef struct TagDumemStatT DumemStatT;
StatusInter DumemGetStat(SeInstanceT *seIns, DumemStatT *dumemStat);
#ifdef FEATURE_BUFFERPOOL
typedef struct BufpoolStat BufpoolStatT;
StatusInter BufpoolInit(SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx);
StatusInter BufpoolCreatePageMgr(SeInstanceT *seIns, DbMemCtxT *memCtx, PageMgrT **pageMgr, void *sessionCtx);
void BufpoolDestroyPageMgr(SeInstanceT *seIns, PageMgrT *pageMgr);
void BufpoolGetStat(SeInstanceT *seIns, BufpoolStatT *bufpoolStat);
#endif
void ResColPoolAmInit(void);
void DatalogCompilerFuncRegister(void);
Status DataLogMultiServiceInit(void);
void DatalogMultiServiceUnInit(void);
void DeltaLabelScanFuncInit(void);
void DeltaLabelModifyFuncInit(void);
typedef struct Session SessionT;
Status ImportDatalogSoFromFile(SessionT *session, char *filePath, DbMemCtxT *memCtx);
Status ImportDatalogPatchFromFile(SessionT *session, char *filePath, DbMemCtxT *memCtx);
void DtlTempTimeoutMethodInit(void);
void DtlDropTimeoutMethodInit(void);
void DatalogPlanMethodFuncRegister(void);
void ExecInitDeltaSeqScanRegister(void);
void DatalogPlanStmtFuncRegister(void);
void DatalogUdfRegister(void);
void CmdLoadPluginRegister(void);
#ifdef FEATURE_DATALOG
void CmdUnloadPluginRegister(void);
#endif
typedef struct SvInstance SvInstanceT;
Status SvServiceDatalogViewRegist(SvInstanceT *viewInst);
void FastPathCompilerFuncRegister(void);
void FastPathExecutorFuncRegister(void);
Status FastPathServiceInit(void);
void FastPathServiceUnInit(DbInstanceHdT dbInstance);
void DatalogExecutorFuncRegister(void);
#ifdef FEATURE_SQL
Status SqlServiceInit(DbInstanceHdT dbInstance);
void SqlServiceUnInit(DbInstanceHdT dbInstance);
void SqlExecutorFuncRegister(void);
#endif
#ifdef FEATURE_HAC
void HacHashIdxAMInit(void);
void MultiHashIdxAMInit(void);
StatusInter HacMgrCtxInit(SeInstanceT *seInsPtr, DbMemCtxT *memCtx);
#endif
void HashIndexAMInit(void);
void HashLocalIndexAMInit(void);
void HashLinklistIdxAmInit(void);
void SortedIndexAMInit(void);
void ArtHashClusterAMInit(void);
void Lpm4IndexAMInit(void);
void Lpm6IndexAMInit(void);
void HcIndexAmInit(void);
void CHIndexAMinit(void);
void FixedHeapAmInit(void);
#ifdef FEATURE_CSTORE
void CStoreAmInit(void);
#endif
void BTreeIndexAMInit(void);
#ifdef FEATURE_SIMPLEREL
void TTreeIndexAMInit(void);
#endif
#ifdef FEATURE_DISKANN
void DiskAnnIndexAmInit(void);
void DiskAnnIndexAnnFuncInit(void);
#endif

#ifdef FEATURE_VLIVF
void VlIvfIndexAmInit(void);
#endif

#ifdef FEATURE_LPASMEM
void LpasMemIndexAmInit(void);
void LpasMemIndexAnnFuncInit(void);
#endif
#ifdef FEATURE_PERSISTENCE
Status StorageStringConfigGet(SeInstanceT *seIns);
typedef struct TagSeRunCtxT *SeRunCtxHdT;
typedef Status (*VertexLabelIndexRebuildFunc)(SeRunCtxHdT seRunCtx);
typedef struct TagSeInstanceT *SeInstanceHdT;
StatusInter DbRecoveryImpl(VertexLabelIndexRebuildFunc idxRebuildFunc, SeInstanceHdT seIns);
typedef struct LabelInfo LabelInfoT;
Status PurgerGetLabelInfo(SeRunCtxHdT seRunCtx, LabelInfoT *labelInfo);
Status PurgerReleaseLabelInfo(LabelInfoT *labelInfo);
void RedoAmInit(SeInstanceT *seIns);
void SpaceAmInit(SeInstanceT *seIns);
void CkptAmInit(void);
void HeapRedoAmInit(SeInstanceT *seIns);
void PersistenceTranslateAmInit(SeInstanceT *seIns);
void HeapTranslateAmInit(SeInstanceT *seIns);
typedef struct CompressStat CompressAreaStatT;
void CompressAreaGetStat(SeInstanceT *seIns, CompressAreaStatT *compressStat);
#ifdef FEATURE_FIXEDHEAP
void FixedHeapTranslateAmInit(SeInstanceT *seIns);
#endif
void LfsRedoAmInit(SeInstanceT *seIns);
void BTreeIndexRedoAmInit(SeInstanceT *seIns);
#ifdef FEATURE_DISKANN
void DiskAnnIndexRedoAmInit(SeInstanceT *seIns);
#endif
#ifdef FEATURE_LPASMEM
void LpasMemIndexRedoAmInit(SeInstanceT *seIns);
#endif
void SePersistAmInit(void);
StatusInter UndoRedoHandleInit(SeInstanceT *seIns);
#ifndef FEATURE_MINIKV
void SysTableAmInit(void);
void PersistenceCompilerFuncRegister(void);
void PersistenceExecutorFuncRegister(DbInstanceHdT dbInstance);
#else
Status MiniExecAmInit(DbInstanceHdT dbInstance);
Status DmMiniKvAmInit(DbInstanceHdT dbInstance);
#endif
#ifdef FEATURE_INSPECT_TOOL
void InspectAmInit(void);
#endif
#endif
#ifdef FEATURE_YANG
void YangCompilerFuncRegister(void);
void YangExecutorFuncRegister(void);
Status YangServiceInit(void);
void YangServiceUnInit(void);
Status SvServiceYangViewRegist(SvInstanceT *viewInst);
#endif
#ifdef FEATURE_TS
Status TsServiceInit(void);
void TsServiceUnInit(void);
#endif
#ifdef FEATURE_GQL
Status GqlServiceInit(void);
void GqlServiceUnInit(void);
#endif
#ifdef FEATURE_STREAM
Status StreamServiceInit(DbInstanceHdT dbInstance);
void StreamServiceUnInit(DbInstanceHdT dbInstance);
#endif
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
StatusInter DafMgrInit(DbMemCtxT *memCtx);
void DafMgrUnInit(void);
#endif
#endif
void SeHeapAmInit(void);

DynLoadFuncCfgT g_funcCfg[] = {
#ifdef FEATURE_MEMDATA
    {{COMPONENT_MEMDATA, "init"}, {NULL, SET_DYNLOAD_FUNC(MdInit), false}},
    {{COMPONENT_MEMDATA, "createPageMgr"}, {NULL, SET_DYNLOAD_FUNC(MdCreatePageMgr), false}},
    {{COMPONENT_MEMDATA, "destroyPageMgr"}, {NULL, SET_DYNLOAD_FUNC(MdDestroyPageMgr), false}},
#endif
#ifdef FEATURE_DURABLE_MEMDATA
    {{COMPONENT_DURABLE_MEMDATA, "init"}, {NULL, SET_DYNLOAD_FUNC(DumemInit), false}},
    {{COMPONENT_DURABLE_MEMDATA, "createPageMgr"}, {NULL, SET_DYNLOAD_FUNC(DumemCreatePageMgr), false}},
    {{COMPONENT_DURABLE_MEMDATA, "destroyPageMgr"}, {NULL, SET_DYNLOAD_FUNC(DumemDestroyPageMgr), false}},
    {{COMPONENT_DURABLE_MEMDATA, "getDumemStat"}, {NULL, SET_DYNLOAD_FUNC(DumemGetStat), false}},

#endif
#ifdef FEATURE_BUFFERPOOL
    {{COMPONENT_BUFFER_POOL, "init"}, {NULL, SET_DYNLOAD_TS_FUNC(BufpoolInit), false}},
    {{COMPONENT_BUFFER_POOL, "createPageMgr"}, {NULL, SET_DYNLOAD_TS_FUNC(BufpoolCreatePageMgr), false}},
    {{COMPONENT_BUFFER_POOL, "destroyPageMgr"}, {NULL, SET_DYNLOAD_TS_FUNC(BufpoolDestroyPageMgr), false}},
    {{COMPONENT_BUFFER_POOL, "getBufPoolView"}, {NULL, SET_DYNLOAD_TS_FUNC(BufpoolGetStat), false}},
#endif
#ifdef FEATURE_RESPOOL
    {{"trm", "respool"}, {NULL, SET_DYNLOAD_FUNC(ResColPoolAmInit), false}},
#endif
#ifdef FEATURE_DATALOG
    {{"datalog", "compiler-func-register"}, {NULL, SET_DYNLOAD_FUNC(DatalogCompilerFuncRegister), false}},
// 门禁部分平台不编译service目录
#if !defined(RTOSV2X) && !defined(RTOSV2) && defined(FEATURE_DATALOG)
    {{"datalog", "services-init"}, {NULL, SET_DYNLOAD_FUNC(DataLogMultiServiceInit), false}},
    {{"datalog", "services-destroy"}, {NULL, SET_DYNLOAD_FUNC(DatalogMultiServiceUnInit), false}},
    {{"datalog", "services-view-init"}, {NULL, SET_DYNLOAD_FUNC(SvServiceDatalogViewRegist), false}},
    {{"datalog", "executor-delta-label-scan-init"}, {NULL, SET_DYNLOAD_FUNC(DeltaLabelScanFuncInit), false}},
    {{"datalog", "executor-delta-label-modify-init"}, {NULL, SET_DYNLOAD_FUNC(DeltaLabelModifyFuncInit), false}},
    {{"datalog", "import-data"}, {NULL, SET_DYNLOAD_FUNC(ImportDatalogSoFromFile), false}},
    {{"datalog", "import-dataPatch"}, {NULL, SET_DYNLOAD_FUNC(ImportDatalogPatchFromFile), false}},
    {{"datalog", "temp-timeout-method"}, {NULL, SET_DYNLOAD_FUNC(DtlTempTimeoutMethodInit), false}},
    {{"datalog", "drop-timeout-method"}, {NULL, SET_DYNLOAD_FUNC(DtlDropTimeoutMethodInit), false}},
    {{"datalog", "executor-planmethod-func-init"}, {NULL, SET_DYNLOAD_FUNC(DatalogPlanMethodFuncRegister), false}},
    {{"datalog", "executor-planstmt-func-init"}, {NULL, SET_DYNLOAD_FUNC(DatalogPlanStmtFuncRegister), false}},
    {{"datalog", "executor-init-delta-seq-init"}, {NULL, SET_DYNLOAD_FUNC(ExecInitDeltaSeqScanRegister), false}},
    {{"datalog", "executor-udf-func-init"}, {NULL, SET_DYNLOAD_FUNC(DatalogUdfRegister), false}},
    {{"datalog", "plugin-load"}, {NULL, SET_DYNLOAD_FUNC(CmdLoadPluginRegister), false}},
#endif  // !defined(RTOSV2X) && !defined(RTOSV2)
#ifdef FEATURE_DATALOG
    {{"datalog", "plugin-unload"}, {NULL, SET_DYNLOAD_FUNC(CmdUnloadPluginRegister), false}},
    {{"datalog", "executor-func-register"}, {NULL, SET_DYNLOAD_FUNC(DatalogExecutorFuncRegister), false}},
#endif
#endif
#ifdef FEATURE_FASTPATH
    {{"fastpath", "compiler-func-register"}, {NULL, SET_DYNLOAD_FUNC(FastPathCompilerFuncRegister), false}},
    {{"fastpath", "executor-func-register"}, {NULL, SET_DYNLOAD_FUNC(FastPathExecutorFuncRegister), false}},
// 门禁部分平台不编译service目录
#if !defined(RTOSV2X) && !defined(RTOSV2)
    {{"fastpath", "services-init"}, {NULL, SET_DYNLOAD_FUNC(FastPathServiceInit), false}},
    {{"fastpath", "services-destroy"}, {NULL, SET_DYNLOAD_FUNC(FastPathServiceUnInit), false}},
#endif  // !defined(RTOSV2X) && !defined(RTOSV2)
#endif
#ifdef FEATURE_SQL
    {{COMPONENT_SQL, "executor-func-register"}, {NULL, SET_DYNLOAD_FUNC(SqlExecutorFuncRegister), false}},
// 门禁部分平台不编译service目录
#if !defined(RTOSV2X) && !defined(RTOSV2)
    {{COMPONENT_SQL, "service-init"}, {NULL, SET_DYNLOAD_FUNC(SqlServiceInit), false}},
    {{COMPONENT_SQL, "service-destroy"}, {NULL, SET_DYNLOAD_FUNC(SqlServiceUnInit), false}},
#endif  // !defined(RTOSV2X) && !defined(RTOSV2)
#endif
#ifdef FEATURE_HAC
    {{COMPONENT_HAC, "hac_hash"}, {NULL, SET_DYNLOAD_FUNC(HacHashIdxAMInit), false}},
    {{COMPONENT_HAC, "multi_hash"}, {NULL, SET_DYNLOAD_FUNC(MultiHashIdxAMInit), false}},
    {{COMPONENT_HAC, "hac_mgr_init"}, {NULL, SET_DYNLOAD_FUNC(HacMgrCtxInit), false}},
#endif
#ifdef FEATURE_HASH
    {{"trm", "hash_index"}, {NULL, SET_DYNLOAD_FUNC(HashIndexAMInit), false}},
#endif
#ifdef FEATURE_LIST_LOCALHASH
    {{"trm", "list_local_hash"}, {NULL, SET_DYNLOAD_FUNC(HashLocalIndexAMInit), false}},
#endif
#ifdef FEATURE_HASH_LINKLIST
    {{"trm", "hash_linklist"}, {NULL, SET_DYNLOAD_FUNC(HashLinklistIdxAmInit), false}},
#endif
#ifdef FEATURE_SORTED
    {{"trm", "art_sorted"}, {NULL, SET_DYNLOAD_FUNC(SortedIndexAMInit), false}},
    {{"trm", "art_hash_cluster"}, {NULL, SET_DYNLOAD_FUNC(ArtHashClusterAMInit), false}},
#endif
#ifdef FEATURE_LPM
    {{"trm", "lpm4"}, {NULL, SET_DYNLOAD_FUNC(Lpm4IndexAMInit), false}},
    {{"trm", "lpm6"}, {NULL, SET_DYNLOAD_FUNC(Lpm6IndexAMInit), false}},
#endif
#ifdef FEATURE_HASH_CLUSTER
    {{"trm", "hash_cluster"}, {NULL, SET_DYNLOAD_FUNC(HcIndexAmInit), false}},
#endif
#ifdef FEATURE_CHAINED_HASH
    {{"trm", "chained_hash"}, {NULL, SET_DYNLOAD_FUNC(CHIndexAMinit), false}},
#endif
#ifdef FEATURE_FIXEDHEAP
    {{"trm", "fixedheap"}, {NULL, SET_DYNLOAD_FUNC(FixedHeapAmInit), false}},
#endif
#ifdef FEATURE_CSTORE
    {{COMPONENT_CSTORE, "cstore"}, {NULL, SET_DYNLOAD_TS_FUNC(CStoreAmInit), false}},
#endif
#ifdef FEATURE_BTREE
    {{COMPONENT_BTREE, "btree"}, {NULL, SET_DYNLOAD_TS_FUNC(BTreeIndexAMInit), false}},
#endif
#ifdef FEATURE_TTREE
    {{"trm", "ttree"}, {NULL, SET_DYNLOAD_FUNC(TTreeIndexAMInit), false}},
#endif
#ifdef FEATURE_DISKANN
    {{"trm", "diskann"}, {NULL, SET_DYNLOAD_FUNC(DiskAnnIndexAmInit), false}},
    {{"trm", "ann_diskann"}, {NULL, SET_DYNLOAD_FUNC(DiskAnnIndexAnnFuncInit), false}},
#endif
#ifdef FEATURE_VLIVF
    {{"trm", "vlivf"}, {NULL, SET_DYNLOAD_FUNC(VlIvfIndexAmInit), false}},
#endif
#ifdef FEATURE_LPASMEM
    {{"trm", "lpasmem"}, {NULL, SET_DYNLOAD_FUNC(LpasMemIndexAmInit), false}},
    {{"trm", "ann_lpasmem"}, {NULL, SET_DYNLOAD_FUNC(LpasMemIndexAnnFuncInit), false}},
#endif
#ifdef FEATURE_PERSISTENCE
    {{COMPONENT_PERSISTENCE, "base"}, {NULL, SET_DYNLOAD_TS_FUNC(SePersistAmInit), false}},
    {{COMPONENT_PERSISTENCE, "string_config"}, {NULL, SET_DYNLOAD_TS_FUNC(StorageStringConfigGet), false}},
    {{COMPONENT_PERSISTENCE, "recovery"}, {NULL, SET_DYNLOAD_TS_FUNC(DbRecoveryImpl), false}},
    {{COMPONENT_PERSISTENCE, "redo"}, {NULL, SET_DYNLOAD_TS_FUNC(RedoAmInit), false}},
    {{COMPONENT_PERSISTENCE, "space"}, {NULL, SET_DYNLOAD_TS_FUNC(SpaceAmInit), false}},
    {{COMPONENT_PERSISTENCE, "checkpoint"}, {NULL, SET_DYNLOAD_TS_FUNC(CkptAmInit), false}},
    {{COMPONENT_PERSISTENCE, "heap_redo"}, {NULL, SET_DYNLOAD_TS_FUNC(HeapRedoAmInit), false}},
    {{COMPONENT_PERSISTENCE, "lfs_redo"}, {NULL, SET_DYNLOAD_TS_FUNC(LfsRedoAmInit), false}},
    {{COMPONENT_PERSISTENCE, "undo_redo"}, {NULL, SET_DYNLOAD_TS_FUNC(UndoRedoHandleInit), false}},
    {{COMPONENT_PERSISTENCE, "persistence_translate"}, {NULL, SET_DYNLOAD_TS_FUNC(PersistenceTranslateAmInit), false}},
    {{COMPONENT_PERSISTENCE, "heap_translate"}, {NULL, SET_DYNLOAD_TS_FUNC(HeapTranslateAmInit), false}},
    {{COMPONENT_PERSISTENCE, "space_compress_area_stat"}, {NULL, SET_DYNLOAD_TS_FUNC(CompressAreaGetStat), false}},
#ifdef FEATURE_FIXEDHEAP
    {{COMPONENT_PERSISTENCE, "fixedheap_translate"}, {NULL, SET_DYNLOAD_TS_FUNC(FixedHeapTranslateAmInit), false}},
#endif
#ifdef FEATURE_BTREE
    {{COMPONENT_PERSISTENCE, "btree_index_redo"}, {NULL, SET_DYNLOAD_TS_FUNC(BTreeIndexRedoAmInit), false}},
#endif
#ifdef FEATURE_DISKANN
    {{COMPONENT_PERSISTENCE, "diskann_index_redo"}, {NULL, SET_DYNLOAD_TS_FUNC(DiskAnnIndexRedoAmInit), false}},
#endif
#ifdef FEATURE_LPASMEM
    {{COMPONENT_PERSISTENCE, "lpasmem_index_redo"}, {NULL, SET_DYNLOAD_FUNC(LpasMemIndexRedoAmInit), false}},
#endif
#ifndef FEATURE_MINIKV
    {{COMPONENT_PERSISTENCE, "systable"}, {NULL, SET_DYNLOAD_TS_FUNC(SysTableAmInit), false}},
    {{COMPONENT_PERSISTENCE, "compiler-func-register"},
        {NULL, SET_DYNLOAD_TS_FUNC(PersistenceCompilerFuncRegister), false}},
    {{COMPONENT_PERSISTENCE, "executor-func-register"},
        {NULL, SET_DYNLOAD_TS_FUNC(PersistenceExecutorFuncRegister), false}},
#else
    {{COMPONENT_MINIKV, "datamodel-kv-func-register"}, {NULL, SET_DYNLOAD_FUNC(DmMiniKvAmInit), false}},
    {{COMPONENT_MINIKV, "executor-func-register"}, {NULL, SET_DYNLOAD_FUNC(MiniExecAmInit), false}},
#endif
#ifdef FEATURE_INSPECT_TOOL
    {{COMPONENT_PERSISTENCE, COMPONENT_INSPECT_TOOL}, {NULL, SET_DYNLOAD_TS_FUNC(InspectAmInit), false}},
#endif
#endif  // endof ifdefine FEATURE_PERSISTENCE
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
    {{"trm", "daf_init"}, {NULL, SET_DYNLOAD_FUNC(DafMgrInit), false}},
    {{"trm", "daf_uninit"}, {NULL, SET_DYNLOAD_FUNC(DafMgrUnInit), false}},
#endif

#ifdef FEATURE_YANG
    {{COMPONENT_YANG, "compiler-func-register"}, {NULL, SET_DYNLOAD_FUNC(YangCompilerFuncRegister), false}},
    {{COMPONENT_YANG, "executor-func-register"}, {NULL, SET_DYNLOAD_FUNC(YangExecutorFuncRegister), false}},
// 门禁部分平台不编译service目录
#if !defined(RTOSV2X) && !defined(RTOSV2)
    {{COMPONENT_YANG, "services-init"}, {NULL, SET_DYNLOAD_FUNC(YangServiceInit), false}},
    {{COMPONENT_YANG, "services-destroy"}, {NULL, SET_DYNLOAD_FUNC(YangServiceUnInit), false}},
    {{COMPONENT_YANG, "services-view-init"}, {NULL, SET_DYNLOAD_FUNC(SvServiceYangViewRegist), false}},
#endif  // !defined(RTOSV2X) && !defined(RTOSV2)
#endif

#ifdef FEATURE_TS
#if !defined(RTOSV2X) && !defined(RTOSV2)
    {{COMPONENT_TS, "services-init"}, {NULL, SET_DYNLOAD_TS_FUNC(TsServiceInit), false}},
    {{COMPONENT_TS, "services-uninit"}, {NULL, SET_DYNLOAD_TS_FUNC(TsServiceUnInit), false}},
#endif
#ifdef TS_MULTI_INST
    {{COMPONENT_TS, "se_heap_am_init"}, {NULL, SET_DYNLOAD_TS_FUNC(SeHeapAmInit), false}},
#endif
#endif
#ifdef FEATURE_GQL
#if !defined(RTOSV2X) && !defined(RTOSV2)
    {{COMPONENT_GQL, "services-init"}, {NULL, SET_DYNLOAD_FUNC(GqlServiceInit), false}},
    {{COMPONENT_GQL, "services-uninit"}, {NULL, SET_DYNLOAD_FUNC(GqlServiceUnInit), false}},
#endif
#endif
#ifdef FEATURE_STREAM
#if !defined(RTOSV2X) && !defined(RTOSV2)
    {{COMPONENT_STREAM, "stream-services-init"}, {NULL, SET_DYNLOAD_TS_FUNC(StreamServiceInit), false}},
    {{COMPONENT_STREAM, "stream-services-uninit"}, {NULL, SET_DYNLOAD_TS_FUNC(StreamServiceUnInit), false}},
#endif
#endif
};

DbDynLoadCtxT g_dynLoadCtx = {0};
static Status DbDynLoadOpenOneSo(const char *feature, bool needLoadDepFeatures);

Status DbDynGetFeatureLibPath(char *featurePath, const char *featureDir, const char *featureName)
{
    DB_POINTER3(featurePath, featureDir, featureName);
    int32_t len = -1;
    if (strlen(featureDir) != 0) {
        len = snprintf_s(featurePath, DB_MAX_PATH, DB_MAX_PATH - 1, "%s/libgm%s.so", featureDir, featureName);
    } else {
        len = snprintf_s(featurePath, DB_MAX_PATH, DB_MAX_PATH - 1, "libgm%s.so", featureName);
    }
    if (len < 0) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Get full path, lib path: %s, featureName: %s", featureDir, featureName);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

static uint32_t DynLoadFuncMapCmp(const void *key1, const void *key2)
{
    DB_POINTER2(key1, key2);
    const DynLoadFuncMapKeyT *keyMap1 = (const DynLoadFuncMapKeyT *)key1;
    const DynLoadFuncMapKeyT *keyMap2 = (const DynLoadFuncMapKeyT *)key2;
    return (strcmp(keyMap1->feature, keyMap2->feature) == 0 && strcmp(keyMap1->subsystem, keyMap2->subsystem) == 0);
}

static Status DbDynLoadSetSoHandle(const char *feature, void *soHandle)
{
    DB_POINTER2(feature, soHandle);
    if (sizeof(g_funcCfg) == 0) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "g_funcCfg is empty. featureName: %s", feature);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    bool hit = false;
    size_t count = sizeof(g_funcCfg) / sizeof(DynLoadFuncCfgT);
    uint32_t i = 0;
    while (i < count) {
        if (strcmp(g_funcCfg[i].key.feature, feature) == 0) {
            hit = true;
            g_funcCfg[i].value.soHandle = soHandle;
        }
        i++;
    }
    if (!hit) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Set feature so handle, featureName: %s", feature);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

static Status DbDynLoadSetSingleSo(const char *feature)
{
    DB_POINTER(feature);
    if (sizeof(g_funcCfg) == 0) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "g_funcCfg is empty. featureName: %s", feature);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    bool hit = false;
    size_t count = sizeof(g_funcCfg) / sizeof(DynLoadFuncCfgT);
    uint32_t i = 0;
    while (i < count) {
        if (strcmp(g_funcCfg[i].key.feature, feature) == 0) {
            hit = true;
            g_funcCfg[i].value.singleSoLoadFeature = true;
        }
        i++;
    }
    if (!hit) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Set single so, featureName: %s", feature);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

static bool DbDynLoadSoIsLoaded(const char *feature)
{
    DB_POINTER(feature);
    if (sizeof(g_funcCfg) == 0) {
        return false;
    }
    size_t count = sizeof(g_funcCfg) / sizeof(DynLoadFuncCfgT);
    uint32_t i = 0;
    while (i < count) {
        if (strcmp(g_funcCfg[i].key.feature, feature) == 0) {
            if (g_funcCfg[i].value.soHandle != NULL || g_funcCfg[i].value.singleSoLoadFeature) {
                return true;
            }
        }
        i++;
    }
    return false;
}

char *DbDynLoadGetDepFeatures(const char *feature)
{
    DB_POINTER(feature);
    size_t count = sizeof(g_featureDep) / sizeof(DynLoadFeatureDepT);
    uint32_t i = 0;
    while (i < count) {
        if (strcmp(g_featureDep[i].feature, feature) == 0) {
            return g_featureDep[i].depFeatures;
        }
        i++;
    }
    return NULL;
}

static Status DbDynLoadAllSo(void)
{
#ifdef SINGLE_SO
    size_t count = sizeof(g_funcCfg) / sizeof(DynLoadFuncCfgT);
    for (uint32_t i = 0; i < count; i++) {
        Status ret = DbDynLoadOpenOneSo(g_funcCfg[i].key.feature, false);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
#endif
    return GMERR_OK;
}

static Status DbDynLoadFeatureList(const char *features)
{
    DB_POINTER(features);
    uint32_t len = (uint32_t)strlen(features) + 1;
    char *buf = (char *)DB_MALLOC(len);
    if (buf == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc buffer(size %u)", len);
        return GMERR_OUT_OF_MEMORY;
    }
    // 此处不检查返回值，因为buf是按照features的长度申请的，这里strcpy_s不会失败
    (void)strcpy_s(buf, len, features);
    char *pCurrent = NULL;
    char *nextToken = NULL;
    pCurrent = strtok_s(buf, ",", &nextToken);
    DB_LOG_INFO("dynamic load feature list: %s", features);
    while (pCurrent) {
        Status ret = DbDynLoadOpenOneSo(pCurrent, true);
        if (ret != GMERR_OK) {
            DB_FREE(buf);
            return ret;
        }
        pCurrent = strtok_s(NULL, ",", &nextToken);
    }
    DB_FREE(buf);
    DbPatchRestore();
    return GMERR_OK;
}

static Status DbDynLoadDepFeatures(const char *feature)
{
    DB_POINTER(feature);
    char *depFeatures = DbDynLoadGetDepFeatures(feature);
    if (depFeatures == NULL) {
        return GMERR_OK;
    }
    return DbDynLoadFeatureList(depFeatures);
}

#if defined(TS_MULTI_INST) || !defined(SINGLE_SO)
static Status DbDynLoadActualSo(const char *libNameBuf, const char *feature)
{
    void *soHandle = NULL;
    Status ret = DbAdptLoadLibrary(libNameBuf, &soHandle, RTLD_NOW);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Dlopen so: %s", libNameBuf);
        return ret;
    }
    ret = DbDynLoadSetSoHandle(feature, soHandle);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Set so handle : %s", libNameBuf);
    }
    return ret;
}
#endif

static Status DbDynLoadOpenOneSo(const char *feature, bool needLoadDepFeatures)
{
    DB_POINTER(feature);
    if (DbDynLoadSoIsLoaded(feature)) {
        return GMERR_OK;
    }
    char libNameBuf[DB_MAX_PATH] = "";
    Status ret = DbDynGetFeatureLibPath(libNameBuf, g_dynLoadCtx.featureLibPath, feature);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get lib name for feature %s", feature);
        return ret;
    }
#ifdef SINGLE_SO
#ifdef TS_MULTI_INST
    if (!DbCfgIsTsInstance() || DbStrCmp(feature, COMPONENT_TS, false) != 0) {
        ret = DbDynLoadSetSingleSo(feature);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Set single so handle : %s", libNameBuf);
            return ret;
        }
    } else {
        ret = DbDynLoadActualSo(libNameBuf, feature);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
#else
    ret = DbDynLoadSetSingleSo(feature);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Set single so handle : %s", libNameBuf);
        return ret;
    }
#endif
#else
    ret = DbDynLoadActualSo(libNameBuf, feature);
    if (ret != GMERR_OK) {
        return ret;
    }
#endif
    if (needLoadDepFeatures) {
        ret = DbDynLoadDepFeatures(feature);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Load feature(s) denency for : %s", libNameBuf);
            return ret;
        }
        DB_LOG_INFO("Load %s denency succ, feature:%s", libNameBuf, feature);
    }

    return GMERR_OK;
}

static Status DbDynLoadOpenSo(DbInstanceHdT dbInstance)
{
    DbCfgValueT featureCfg = {0};
    Status ret = DbCfgGet(DbGetCfgHandle(dbInstance), DB_CFG_FEATURE_NAMES, &featureCfg);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get config option for DB_CFG_FEATURE_NAMES");
        return ret;
    }
    if (strlen(featureCfg.str) == 0) {
        return DbDynLoadAllSo();
    }
    DbStrToLower(featureCfg.str);
    if (strcmp(featureCfg.str, DYN_LOAD_BUILD_ALL_TYPE) == 0) {
        return DbDynLoadAllSo();
    }
#ifdef TS_MULTI_INST
    if (DbCfgIsTsInstance()) {
        return DbDynLoadAllSo();
    }
#endif
    return DbDynLoadFeatureList(featureCfg.str);
}

static Status DbDynLoadFuncCfg(void)
{
    Status ret = DbOamapInit(&g_dynLoadCtx.funcMap, FUNC_MAP_INIT_CAPACITY, DynLoadFuncMapCmp, NULL, true);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Init map for dynload func");
        return ret;
    }
    if (sizeof(g_funcCfg) == 0) {
        return GMERR_OK;
    }
    size_t count = sizeof(g_funcCfg) / sizeof(DynLoadFuncCfgT);
    uint32_t i = 0;
    while (i < count) {
        uint32_t hash = DbStrToHash32(g_funcCfg[i].key.subsystem);
        ret = DbOamapInsert(&g_dynLoadCtx.funcMap, hash, &g_funcCfg[i].key, &g_funcCfg[i].value, NULL);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "Insert func(%s) into map", g_funcCfg[i].key.subsystem);
            return ret;
        }
        i++;
    }
    return GMERR_OK;
}

Status DbDynLoadFeatureSo(DbInstanceHdT dbInstance)
{
    Status ret = DbDynLoadFuncCfg();
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Load func");
        return ret;
    }
    DbCfgValueT libPath = {0};
    ret = DbCfgGet(DbGetCfgHandle(dbInstance), DB_CFG_FEATURE_LIB_PATH, &libPath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get config libpath info");
        return ret;
    }
    if (strlen(libPath.str) >= DB_MAX_PATH) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "Long feature lib path: %s", libPath.str);
        return GMERR_CONFIG_ERROR;
    }
    (void)memcpy_s(g_dynLoadCtx.featureLibPath, DB_MAX_PATH, libPath.str, strlen(libPath.str));

    ret = DbDynLoadOpenSo(dbInstance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Load feature so");
        return ret;
    }
    g_dynLoadCtx.finishLoad = true;
    return GMERR_OK;
}

Status DbDynLoadFeatureFuncClt(void)
{
    return DbDynLoadFuncCfg();
}

void *DbDynLoadGetFunc(const char *feature, const char *subsystem)
{
    // 此函数内为INFO日志打印 失败和成功的后续由调用的地方做处理
    DB_POINTER2(feature, subsystem);
#ifdef TS_MULTI_INST
    // 双实例场景，GMDBV5实例场景下，所有在libgmdb_ts.so中的函数应该返回null,除了SeHeapAmInit
    if (!DbCfgIsTsInstance() && DbStrCmp(feature, COMPONENT_TS, false) == 0) {
        DB_LOG_INFO("Get func for (%s,%s) for none ts", feature, subsystem);
        return NULL;
    }
#endif
    uint32_t hash = DbStrToHash32(subsystem);
    DynLoadFuncMapKeyT key = {feature, subsystem};
    DynLoadFuncMapValueT *value = (DynLoadFuncMapValueT *)DbOamapLookup(&g_dynLoadCtx.funcMap, hash, &key, NULL);
    if (value == NULL) {
        DB_LOG_INFO("Get func for (%s,%s)", feature, subsystem);
        return NULL;
    }

#if defined(SINGLE_SO) && !defined(COMMON_STATIC)
#ifdef TS_MULTI_INST
    // 双实例场景，GMDBV5实例场景下，所有在libgmdb_ts.so中的函数应该返回null,除了SeHeapAmInit
    if (!DbCfgIsTsInstance() || DbStrCmp(feature, COMPONENT_TS, false) != 0) {
        if (DbCommonIsServer() && !value->singleSoLoadFeature) {
            DB_LOG_INFO("Dlsym a function %s soHandle", (char *)value->function);
            return NULL;
        }
        return value->function;
    }
#else
    if (DbCommonIsServer() && !value->singleSoLoadFeature) {
        DB_LOG_INFO("Dlsym a function %s soHandle", (char *)value->function);
        return NULL;
    }
    return value->function;
#endif
#endif
    void *func = NULL;
    FuncTableItemT table = {.destination = &func, .symbol = (char *)value->function};
    DB_LOG_INFO("ready to dlsym feature:%s,subsystem:%s, symbol:%s", feature, subsystem, (char *)value->function);
    Status ret = DbAdptLoadFunc(value->soHandle, &table, 1);
    if (ret != GMERR_OK) {
        DB_LOG_INFO(
            "Dlsym a function(%s, handle:%s null)", (char *)value->function, value->soHandle == NULL ? "is" : "not");
        return NULL;
    }
    return func;
}

bool DbDynLoadHasFeature(const char *feature)
{
#ifdef TS_MULTI_INST
    // 双实例场景，GMDBV5实例场景下，时序相关的feature都返回不存在
    if (!DbCfgIsTsInstance() && DbStrCmp(feature, COMPONENT_TS, false) == 0) {
        return false;
    }
#endif
    if (g_dynLoadCtx.finishLoad != true) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Server initial feature load not finished");
        return false;
    }
    return DbDynLoadSoIsLoaded(feature);
}

void DbDynUnloadFeatureSoHandle(void)
{
    if (sizeof(g_funcCfg) == 0) {
        return;
    }
    // 共进程模式下客户端不允许修改so
    if (DbCommonIsCltThreadInServProcess()) {
        return;
    }
    size_t count = sizeof(g_funcCfg) / sizeof(DynLoadFuncCfgT);
    for (uint32_t i = 0; i < count; ++i) {
        if (g_funcCfg[i].value.soHandle != NULL && !g_funcCfg[i].value.singleSoLoadFeature) {
            DbAdptUnLoadLibrary(g_funcCfg[i].value.soHandle);
            g_funcCfg[i].value.soHandle = NULL;
        }
    }
    DbOamapDestroy(&g_dynLoadCtx.funcMap);
}

Status DbDynLoadSetSoHandleClt(const char *feature, void *soHandle)
{
    return DbDynLoadSetSoHandle(feature, soHandle);
}

Status DbDynLoadSetSingleSoClt(const char *feature)
{
    return DbDynLoadSetSingleSo(feature);
}

bool DbDynLoadSoIsLoadedClt(const char *feature)
{
    return DbDynLoadSoIsLoaded(feature);
}

const char *DbDynGetFeatureLibDirPath(void)
{
    return g_dynLoadCtx.featureLibPath;
}

bool DbDynLoadCltComponentizeHasFinished(void)
{
    return g_dynLoadCtx.finishLoad;
}

void DbDynLoadCltComponentizeFinished(void)
{
    g_dynLoadCtx.finishLoad = true;
}

void DbDynLoadCltComponentizeUnFinished(void)
{
    g_dynLoadCtx.finishLoad = false;
}
