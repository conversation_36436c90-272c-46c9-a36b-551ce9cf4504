/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: db_dynmem_algo.h
 * Description: header file for common memory dynamic algorithm
 * Author: wanglei
 * Create: 2020-8-7
 */

#ifndef DB_MEM_DYNAMIC_ALGO_H
#define DB_MEM_DYNAMIC_ALGO_H

#include <time.h>
#include "db_mem_context.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MEM_ALLOC_CALL_STACK_MAX_SIZE 15

/* 支持补丁在线修改值 */
extern uint32_t g_gmdbOnlineMemtraceFlag;
extern uint32_t g_gmdbOnlineMemtracePid;

/*
 * Block、BigChunk分别在头尾部加上magicNum用于检测
 * Chunk只在头部加上了magicNum，因为考虑到chunk在Block内且每个chunk连续，踩前一个尾部等于后一个头部
 * 同时考虑内存底噪要每个Chunk再多加8字节，所以没有添加chunk尾部的magicNum
 */
typedef struct DbDynamicAlgo DbDynamicAlgoT;
typedef struct DbAllocBlock DbAllocBlockT;
typedef struct DbAllocChunk DbAllocChunkT;
typedef struct DbAllocBigChunk DbAllocBigChunkT;
typedef struct DbAllocBigChunkTail DbAllocBigChunkTailT;
typedef struct DbDynUniChunk DbDynUniChunkT;

typedef struct DynMemChunkTraceInfo {
    uint64_t size;
    uint8_t stackSize;
    time_t time;
    void *callStack[MEM_ALLOC_CALL_STACK_MAX_SIZE];
    uint8_t traceReserve;  // Reserve for strcmp during callstack collection.
    // Maintain a two-way linked list of all allocated memory chunks
    struct DynMemChunkTraceInfo *prev;
    struct DynMemChunkTraceInfo *next;
} DynMemChunkTraceInfoT;

/*
 * DbAllocBlock
 * An DbAllocBlockT is the unit of memory that is obtained by db_dynmem_algo.c
 * from DB_MALLOC. It contains one or more AllocChunks, which are the units
 * requested by DynamicAlgoAlloc and freed by DynamicAlgoFree. AllocChunks
 * cannot be returned to DB_MALLOC individually, instead they are put
 * on freelists by DynamicAlgoFree and re-used by the next DynamicAlgoAlloc that has
 * a matching request size.
 *
 * DbAllocBlock is the header data for a block --- the usable space
 * within the block begins at the next alignment boundary.
 */
struct DbAllocBlock {
    uint32_t magicNum;
    uint32_t reserve;
    DbDynamicAlgoT *allocSet; /* aset that owns this block */
    DbAllocBlockT *prev;      /* prev block in DynamicAlgo's blocks list, if any */
    DbAllocBlockT *next;      /* next block in DynamicAlgo's blocks list */
    char *freeptr;            /* start of free space in this block */
    char *endptr;             /* end of space in this block */
    uint32_t blockSize;       /* size of the whole block, including block header */
    uint16_t blockAddrOffset; /* record start address of actual memory block for releasing to OS */
    uint16_t allocCount;      /* number of chunks that are not freed */
#ifndef NDEBUG
    uint16_t chunkNum; /* number of chunks partitioned from this block */
#endif
};

// 在debug版本暴露内存出错函数，用于ut打桩
#ifndef NDEBUG
#define MEM_ERR_FUNC_INLINE
#else
#define MEM_ERR_FUNC_INLINE inline static
#endif

/*
 * DbAllocChunk/DbAllocBigChunk
 * The prefix of each piece of allocated memory
 */
struct DbAllocBigChunk {
    uint32_t magicNum;
    uint32_t chunkAddrOffset;
    DbAllocBigChunkT *next; /* next chunk in DynamicAlgo's bigChunks list */
    DbAllocBigChunkT *prev; /* prev chunk in DynamicAlgo's bigChunks list */
    uint64_t bigChunkSize;
};

struct DbAllocBigChunkTail {
    uint32_t reserve;  // 底层保持8字节对齐，reserve放在首部，确保magicNum在尾部
    uint32_t magicNum;
};

/*
 * YunShanLite服务端出于小型化需求，会有开关二次管理混用的场景，此时必须保证DbAllocChunk和DbDynUniChunk结构体大小一直，否则
 * 检验逃生通道是会出错。当前默认8字节对齐，如果开放用户配置对齐大小，则无法有效访问管理结构。
 */
struct DbAllocChunk {
    /* 'ownerOrNextChunk' is the owning DynamicAlgo if allocated, or the freelist link if free */
    void *ownerOrNextChunk;
    /* size is always the size of the usable space in the chunk, maxSize: 16M */
#ifdef LARGE_MEM_ALLOC
    uint64_t size : 63;
#else
    uint32_t size : 31;
#endif
    bool memtraceChunk : 1;
    /* the offset of this chunk in the block */
    uint32_t chunkOffset : 23;  // 申请chunk的时候，block不能超过8M
    uint32_t magicNum : 8;
    /* marked if chunk is from escape ctx */
    // 当前默认8字节对齐，如果开放用户配置对齐大小，则无法有效访问管理结构
    bool escapeMark : 1;
};

/* The only chunk type in lite mod. Memory is directly allocated from OS. */
struct DbDynUniChunk {
    void *pNextChunk;
    uint32_t magicNum;
#ifdef LARGE_MEM_ALLOC
    uint64_t size : 62;
#else
    uint32_t size : 30;
#endif
    /* marked if chunk has enabled memtrace */
    bool memtraceChunk : 1;
    /* marked if chunk is from escape ctx */
    // 当前默认8字节对齐，如果开放用户配置对齐大小，则无法有效访问管理结构
    bool escapeMark : 1;
#if !defined(RTOSV2X) && !defined(HPE)
    void *pPrevChunk;
#endif
#ifndef NDEBUG
    void *chunkOwner;
#endif
};

// clang-format off
#define TYPEALIGN(ALIGNVAL, LEN) (((size_t)(LEN) + ((size_t)(ALIGNVAL) - 1)) & ~((size_t)((ALIGNVAL) - 1)))
// clang-format on
#define MAXALIGN(alignSize, LEN) TYPEALIGN(alignSize, LEN)  // for align

// DynMemCtx default baseSize: 8KB
#define DYNMEM_DEFAULT_BASESIZE (8 * 1024)
// DynMemCtx default stepSize: 32KB
#define DYNMEM_DEFAULT_STEPSIZE (32 * 1024)

/* In the debug mode, when a memory context allocs more than 1GB of memory
   or 1 million times per second, a warning log will be given */
#define MEMCTX_ABNORMAL_ALLOC_SIZE_RATE (1024 * 1024 * 1024)
#define MEMCTX_ABNORMAL_ALLOC_TIMES_RATE 1000000

/*
 * AllocBlock Pointer is AllocBlockHeader + AllocBlockDataArea,
 * AllocBloc DataArea is (AllocChunkHeader + AllocChunkDataArea)..(AllocChunkHeader + AllocChunkDataArea)
 */
#define ALLOCBLOCK_DATA(pointer, ALLOC_BLOCKHDRSIZE) ((uintptr_t)(pointer) + (ALLOC_BLOCKHDRSIZE))
#define ALLOCCHUNK_DATA(pointer, ALLOC_CHUNKHDRSIZE) ((uintptr_t)(pointer) + (ALLOC_CHUNKHDRSIZE))
#define ALLOCBIGCHUNK_DATA(pointer, ALLOC_CHUNKHDRSIZE, ALLOC_BIGCHUNKHDRSIZE) \
    (((uintptr_t)(pointer) + (ALLOC_CHUNKHDRSIZE)) + (ALLOC_BIGCHUNKHDRSIZE))

/* find the chunk header through the AllocChunkDataArea pointer */
#define ALLOCCHUNK_HEADER(pointer, ALLOC_CHUNKHDRSIZE) ((DbAllocChunkT *)((uintptr_t)(pointer) - (ALLOC_CHUNKHDRSIZE)))

/* find the block header through the AllocBlockataArea pointer */
#define ALLOCBLOCK_HEADER(pointer, ALLOC_BLOCKHDRSIZE) ((DbAllocBlockT *)((uintptr_t)(pointer) - (ALLOC_BLOCKHDRSIZE)))

/* find the big chunk header through the AllocBlockataArea pointer */
#define ALLOCBIGCHUNK_HEADER(pointer, ALLOC_CHUNKHDRSIZE, ALLOC_BIGCHUNKHDRSIZE) \
    ((DbAllocBigChunkT *)(((uintptr_t)(pointer) - (ALLOC_CHUNKHDRSIZE)) - (ALLOC_BIGCHUNKHDRSIZE)))
#define GET_ALLOCCHUNK_HEADER(pointer, ALLOC_BIGCHUNKHDRSIZE) \
    ((DbAllocChunkT *)(((uintptr_t)(pointer)) + (ALLOC_BIGCHUNKHDRSIZE)))

/* find the owner block of a chunk */
#define OWNER_BLOCK_OF_CHUNK(chunk) ((DbAllocBlockT *)((uintptr_t)(chunk) - (chunk)->chunkOffset))

#define BYTE_SIZE_EXPONENT 3u
#define FREE_LIST_BITMAP_SIZE (1u << BYTE_SIZE_EXPONENT)
#define FREELIST_BITMAP_BYTENUM (((ALLOCSET_NUM_FREELISTS - 1u) >> BYTE_SIZE_EXPONENT) + 1u)

// 结构体成员尽可能将热数据放到一块，避免cache miss
struct DbDynamicAlgo {
    union {
        DbAllocChunkT *freelist[ALLOCSET_NUM_FREELISTS]; /* free chunk lists */
        DbDynUniChunkT *uniChunkList[UNICHUNK_LISTS];    /* v2x、hpe多级链表维护chunk */
    };
    uint8_t freelistBitmap[FREELIST_BITMAP_BYTENUM]; /* for freelist performance */
    uint16_t allocChunkLimit;                        /* effective chunk size limit */
    uint8_t alignSize;                               /* size of dynamic memory alignment */
    bool needRecycle;  /* whether the memory block needs to be recycled and released to the OS */
    bool isDefaultMod; /* User does not define baseSize and stepSize under DefaultMod */
    bool liteModOn; /* 配置项（DB_CFG_LITE_DYN_MEM_MOD = true）时，通过将此入参置为true关闭二次管理。 */
    uint32_t initBlockSize; /* initial block size */
    uint32_t nextBlockSize; /* next block size to allocate */
    uint32_t maxBlockSize;  /* maximum block size */
    uint32_t baseSize;
    uint32_t stepSize;
    uint64_t maxSize;            // ctx可向OS申请的最大物理内存
    DbAllocBlockT *keeper;       /* if not NULL, keep this block over resets */
    DbAllocBlockT *blocks;       /* head of list of blocks in this set */
    DbAllocBigChunkT *bigChunks; /* alloc chunk size bigger than ALLOC_CHUNK_LIMIT */
    uint32_t bigChunkNum;        /* record big chunk num */
    uint32_t uniChunkNum;
    uint32_t blockNum;            /* record block num */
    uint64_t totalBlockSize;      /* total block size */
    uint64_t totalAllocSizeByCtx; /* total alloc memory size by this memory context. */
    uint64_t totalPhySize;        /* total physical memory for this memory context */
    uint64_t currPhySizePeak;   // Record the peak physical memory usage of memCtx：max(currPhySizePeak, totalPhySize)
                                // This value is refreshed when applying for memory, and the reset interface needs to be
                                // called to reset it.
    uint64_t totalBigChunkSize; /* total big chunk size */
    uint32_t allocTimes;        // dynCtx申请内存总次数
    uint32_t freeTimes;         // dynCtx释放内存总次数
#ifndef NDEBUG
    uint64_t whenAlloc;      /* record alloc memory time for debug */
    uint64_t allocSizeRate;  /* record total alloc in a second for debug */
    uint32_t allocTimesRate; /* record alloc memory times in a second for debug */
#endif
#if !defined(RTOSV2X) && !defined(HPE)
    DbDynUniChunkT *uniChunkDoubleList; /* v2x、hpe外的环境使用多级链表维护chunk */
#endif
    DynMemChunkTraceInfoT *traceInfoList; /* record all alloc small chunks for memory trace */
};
DB_CHECK_ALIGN_ATOMIC64_MEMBER(struct DbDynamicAlgo, totalPhySize);
DB_CHECK_ALIGN_ATOMIC64_MEMBER(struct DbDynamicAlgo, currPhySizePeak);

typedef struct DbDynamicMemCtx {
    DbMemCtxT header;
    DbDynamicAlgoT dynamicAlgo;
} DbDynamicMemCtxT;

typedef enum DbTopDynMemCtxStatus { DB_MEMCTX_DESTROYED, DB_MEMCTX_INITED } DbTopDynMemCtxStatusE;

struct DbTopDynMemCtx {
    DbMemCtxT header;
    DbDynamicAlgoT dynamicAlgo;
    DbTopDynMemCtxStatusE status;
};

/* Dynamic memory operation function */
typedef struct DbMemCtxMethods {
    void *(*dbCtxAlloc)(DbMemCtxT *curCtx, size_t size);
    void (*dbCtxFree)(DbMemCtxT *curCtx, void *ptr);
    void (*dbCtxReset)(DbMemCtxT *curCtx);
    void (*dbCtxClearCurCtx)(DbMemCtxT *curCtx);
    Status (*dbCtxIsEmpty)(DbMemCtxT *curCtx, bool *isEmpty);
    void (*dbCtxStats)(DbMemCtxT *curCtx, void *ctxStats);
    uint64_t (*dbCtxGetPhySizePeak)(DbMemCtxT *curCtx);
    void (*dbCtxResetPhySizePeak)(DbMemCtxT *curCtx);
    uint64_t (*dbCtxGetAndResetPeak)(DbMemCtxT *curCtx);
} DbMemCtxMethodsT;

Status DynamicAlgoInit(DbMemCtxT *ctx, const AlgoParamT *algoParam);
void *DynamicAlgoAlloc(DbMemCtxT *ctx, size_t size) __attribute__((section(".text.hot.memory")));
void DynamicAlgoFree(DbMemCtxT *ctx, void *ptr) __attribute__((section(".text.hot.memory")));
void DynamicAlgoReset(DbMemCtxT *ctx) __attribute__((section(".text.hot.memory")));
void DynamicAlgoClear(DbMemCtxT *ctx) __attribute__((section(".text.hot.memory")));
void DynamicAlgoStats(DbMemCtxT *ctx, void *pdyStats);
uint64_t DynamicAlgoGetPhySizePeak(DbMemCtxT *ctx);
void DynamicAlgoResetPhySizePeak(DbMemCtxT *ctx);
uint64_t DynamicAlgoGetAndResetPeak(DbMemCtxT *ctx);

/**************** Methods for lite ctx ****************/
void *DynamicAlgoAllocLite(DbMemCtxT *ctx, size_t size) __attribute__((section(".text.hot.memory")));
void DynamicAlgoFreeLite(DbMemCtxT *ctx, void *ptr) __attribute__((section(".text.hot.memory")));
void DynamicAlgoResetLite(DbMemCtxT *ctx) __attribute__((section(".text.hot.memory")));
void DynamicAlgoClearLite(DbMemCtxT *ctx) __attribute__((section(".text.hot.memory")));

// 外部调用者保证对bitmap加锁，将idx对应槽位 1->0.
inline static void EraseBitMapByIdx(uint8_t *bitmap, uint32_t idx)
{
    bitmap[idx >> BYTE_SIZE_EXPONENT] &= (uint8_t) ~(((uint8_t)(1u << (idx & (FREE_LIST_BITMAP_SIZE - 1u)))));
}

// 外部调用者保证对bitmap加锁，将idx对应槽位0->1
inline static void MarkBitMapByIdx(uint8_t *bitmap, uint32_t idx)
{
    bitmap[idx >> BYTE_SIZE_EXPONENT] |= (uint8_t)(1u << (idx & (FREE_LIST_BITMAP_SIZE - 1u)));
}

#ifdef __cplusplus
}
#endif

#endif /* DB_MEM_DYNAMIC_ALGO_H */
