/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: db_shmem_pagepool_algo.h
 * Description: internal header file for common shared memory page pool algorithm
 * Author: wanglei
 * Create: 2020-8-24
 */

#ifndef DB_SHMEM_PAGEPOOL_ALGO_H
#define DB_SHMEM_PAGEPOOL_ALGO_H

#include "db_mem_context.h"
#include "db_mem_context_internal.h"

#ifdef __cplusplus
extern "C" {
#endif

#define PAGEPOOL_DEFAULT_BASENUM 1            // initial 1 pages
#define PAGEPOOL_DEFAULT_MAXNUM 10240         // max pages num 10240
#define PAGEPOOL_DEFAULT_STEPNUM 16           // 16 pages to add at each extension
#define PAGEPOOL_DEFAULT_PERMISSION 0         // default 0
#define PAGEPOOL_DEFAULT_PAGESIZE (4 * 1024)  // page size:1024 * 4 = 4K

// 如非必要尽量不要修改此结构体顺序，这里很容易劣化性能
typedef struct DbShmemPagePoolAlgo {
    uint32_t key;              // made by server id + instance id + ctx id
    uint32_t allocCount;       // all times allocated by this context
    uint32_t freeCount;        // all times freed by this context
    PagePoolAttributeT attr;   // attribute for page pool
    ShmemPtrT pagePoolShmPtr;  // shmem pointer for page pool
} DbShmemPagePoolAlgoT;

typedef struct DbPagePoolShmemCtx {
    DbMemCtxT header;
    DbShmemPagePoolAlgoT shmemPagePoolAlgo;
} DbPagePoolShmemCtxT;

const DbShmemCtxMethodsT *GetShmemPagePoolAlgoMethods(void);
Status ShmemPagePoolAlgoInit(DbMemCtxT *curCtx, const AlgoParamT *algoParam);
ShmemPtrT ShmemPagePoolAlgoAlloc(DbMemCtxT *curCtx, size_t size);
Status ShmemPagePoolAlgoFree(DbMemCtxT *curCtx, ShmemPtrT ptr);
void ShmemPagePoolAlgoReset(DbMemCtxT *curCtx);
Status ShmemPagePoolAlgoClear(DbMemCtxT *curCtx);
Status ShmemPagePoolAlgoDestroy(DbMemCtxT *curCtx);
void ShmemPagePoolAlgoDestroyForce(DbMemCtxT *curCtx);
Status ShmemPagePoolIsEmpty(DbMemCtxT *curCtx, bool *isEmpty);
uint64_t ShmemPagePoolGetPhySizePeak(DbMemCtxT *ctx);
void ShmemPagePoolResetPhySizePeak(DbMemCtxT *ctx);
uint64_t ShmemPagePoolGetAndResetPeak(DbMemCtxT *ctx);

#ifdef __cplusplus
}
#endif

#endif /* DB_SHMEM_PAGEPOOL_ALGO_H */
