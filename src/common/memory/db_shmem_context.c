/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: db_mem_shmem_algo.c
 * Description: defines shared memory methods.
 * Author: jinfanglin
 * Create: 2022-10-29
 */

#include "db_mem_context_internal.h"
#include "db_top_shmem_ctx.h"
#include "db_shmem_blockpool_algo.h"
#include "db_shmem_pagepool_algo.h"
#include "db_mem_log.h"
#include "db_alarm.h"
#include "db_config.h"
#include "db_instance.h"

// 共享内存的权限，server起来前从配置文件中读取
static uint32_t g_gmdbShmemPermission = DEFAULT_HPE_SHMEM_PERMISSION;
static uint32_t g_gmdbDevShmemPermission = DEFAULT_HPE_SHMEM_PERMISSION;
// Container of shmem contexts being marked deleted.
ShmemCtxDetachMgrT *g_gmdbShmemCtxDetachMgr = NULL;
// Time record for treeAllocSize calibration.
uint64_t g_gmdbShmemCtxTimer = 0;
// To lock shmemCtx tree structure.
static DbSpinLockT g_gmdbShmemCtxLock = {0};

/* Internal function declarations */
static Status ShmemCtxDetachPool(DbMemCtxT *context);
static Status ShmemCtxDestroy(DbMemCtxT *ctx);

void DbShmemCtxGlobalLock(void)
{
    DbSpinLock(&g_gmdbShmemCtxLock);
}

void DbShmemCtxGlobalUnlock(void)
{
    DbSpinUnlock(&g_gmdbShmemCtxLock);
}

Status CheckShmemCtxValid(const DbMemCtxT *ctx)
{
    Status ret = CheckMemCtxValid(ctx);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (SECUREC_UNLIKELY(ctx->memType != DB_SHARED_MEMORY && ctx->memType != DB_RESERVE_MEMORY)) {
        DB_ASSERT(ctx->memType == DB_SHARED_MEMORY || ctx->memType == DB_RESERVE_MEMORY);
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "ctx is dynCtx, ctxId: %" PRIu32 ", ctx name: %s.", ctx->ctxId,
            ctx->ctxName);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

inline static uint32_t ConstrctKeyById(uint32_t instanceId, uint32_t id)
{
    /*
     * Constructing shared memory key, There are 32 bits in total.
     * The next 8 bits are instance ID and the lower 24 bits are id.
     * 30~31 - server id
     * 22~29 - instance id
     * 0~21 - id
     */
    uint32_t serverId = GET_SERVER_ID;
    uint32_t key = 0;
    key |= serverId << DB_SERVERID_SHIFT;
    key |= instanceId << DB_INSTANCEID_SHIFT;
    key |= id;
    return key;
}

// 共享内存上下文初始化成功之前，通过该接口指定id来生成shmemkey用于创建共享内存。
uint32_t DbConstructShmemKeyById(uint32_t instanceId, DbShmemIdE id)
{
    uint32_t shmemId = (uint32_t)id;
    return ConstrctKeyById(instanceId, shmemId);
}

// 根据ctxid构造pagePool、blockPool算法管理结构的key。
uint32_t ConstructPoolKeyByCtxId(uint32_t instanceId, uint32_t ctxId)
{
    return ConstrctKeyById(instanceId, ctxId);
}

// 只在设备环境有实际参考意义，Euler环境返回的是poolMgr的segId。
uint32_t ShmemCtxGetPtNo(DbMemCtxT *ctx)
{
    DB_POINTER(ctx);
    if (ctx->memType == DB_RESERVE_MEMORY) {
        return DB_INVALID_UINT32;
    }
    DB_ASSERT(ctx->memType == DB_SHARED_MEMORY || ctx->memType == DB_RESERVE_MEMORY);
    if (ctx->methodType == ALGO_BLOCK) {
        DbShmemBlockAlgoT *blockAlgo = &((DbBlockShmemCtxT *)ctx)->shmemBlockAlgo;
        return blockAlgo->blkPoolShmPtr.segId;
    } else {
        DbShmemPagePoolAlgoT *pageAlgo = &((DbPagePoolShmemCtxT *)ctx)->shmemPagePoolAlgo;
        return pageAlgo->pagePoolShmPtr.segId;
    }
}

inline static void DbOpenShmemCtxRunLog(DbMemCtxT *pCtx, uint32_t poolKey)
{
    if (pCtx->memType == DB_RESERVE_MEMORY) {
        return;
    }
    MEM_RUN_LINK_LOG(MEM_RUN_LINK_LOG_SHMEMCTX,
        "Open shmem ctxId %" PRIu32 ", ctxName: %s, ptNo %" PRIu32 ", key %" PRIu32 ". DB attachCnt %" PRIu32
        ", qingluan refCnt %" PRIu32 ".",
        pCtx->ctxId, pCtx->ctxName, ShmemCtxGetPtNo(pCtx), poolKey, pCtx->attachCount,
        DbGetShmemCtxAttachCntNoLock(pCtx));
}

DbMemCtxT *DbOpenShmemCtx(uint32_t ctxId, uint32_t instanceId)
{
    /* 1.当此内存上下文已经被标记删除后，其他进程不在被允许open此内存上下文
     * 2.如果没有被删除，那么就执行attach
     */
    DbMemCtxT *pCtx = (DbMemCtxT *)DbGetShmemCtxById(ctxId, instanceId);
    Status ret = CheckShmemCtxValid(pCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "open shmctx, id: %" PRIu32 ".", ctxId);
        return NULL;
    }
    ret = DbMemCtxLock(pCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_LOCK_NOT_AVAILABLE, "pen shmCtx %" PRIu32 " name %s.", pCtx->ctxId, pCtx->ctxName);
        return NULL;
    }
    if (CheckMemCtxMagicCode(pCtx) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "open shmem ctx.");
        goto EXIT;
    }
    if (pCtx->markDeleted) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "open shmCtx %" PRIu32 " name %s, ctx mark deleted.", pCtx->ctxId,
            pCtx->ctxName);
        goto EXIT;
    }

    bool staticCtx = ctxId < DB_START_SPECIAL_CTX_ID;
    uint32_t poolKey = ConstructPoolKeyByCtxId(instanceId, ctxId);
    if (pCtx->methodType == ALGO_BLOCK) {
        // adpt block pool attach
        if (DbAdptBlockPoolAttach(poolKey, staticCtx, ((DbBlockShmemCtxT *)pCtx)->shmemBlockAlgo.blkPoolShmPtr) !=
            GMERR_OK) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "open block shmctx, id: %" PRIu32 ".", ctxId);
            goto EXIT;
        }
    } else if (pCtx->methodType == ALGO_PAGE_POOL) {
        // adpt page pool attach
        if (DbAdptPagePoolAttach(poolKey, staticCtx, ((DbPagePoolShmemCtxT *)pCtx)->shmemPagePoolAlgo.pagePoolShmPtr) !=
            GMERR_OK) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Unable to open page shmctx, id: %" PRIu32 ".", ctxId);
            goto EXIT;
        }
    }
    (void)DbAtomicFetchAndAdd(&(pCtx->attachCount), 1);
    DbOpenShmemCtxRunLog(pCtx, poolKey);
#if !defined(NDEBUG) && defined(HPE_SIMULATION)
    pCtx->cliAttPid = (uint32_t)getpid();
#endif
    DbMemCtxUnLock(pCtx);
    return pCtx;
EXIT:
    DbMemCtxUnLock(pCtx);
    return NULL;
}

Status DbCloseShmemCtx(uint32_t ctxId, uint32_t instanceId)
{
    /* detach all shared memory in the current memory context,
     * if current ctx is a parent node, all child memory ctx will not be accessible
     */
    DbMemCtxT *pCtx = (DbMemCtxT *)DbGetShmemCtxById(ctxId, instanceId);
    Status ret = CheckShmemCtxValid(pCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "close memCtx, id: %" PRIu32 ".", ctxId);
        return ret;
    }

    // Detach all segments possessed by current ctx. Also decrease refCnt of ctx pool.refCnt.
    // Deleting operation performed by server.
    ret = ShmemCtxDetachPool(pCtx);
    if (ret == GMERR_OK) {
        DB_LOG_DEBUG("close shmCtx, id: %" PRIu32 ", name: %s.", pCtx->ctxId, pCtx->ctxName);
    }

    return ret;
}

// Called by client on the first connection,
// as Dopra does not allow memory access to shmemCtx that has not been attached.
void DbCltOpenAllStaticShmemCtx(uint32_t instanceId)
{
    if (!DbCommonIsSingleCltProcess()) {
        return;
    }
    // Opening topShmemCtx will register topShmemCtx entry. TopShmemCtx must be opened before any other ctx's opening.
    // Any open failure does not cause break from loop, in case some ctxId declared but not actually created.
    // All the persistent shmemCtx will not be deleted, once they are created, attachment should success.
    for (uint32_t ctxId = DB_TOP_SHMEMCTX_ID; ctxId < DB_START_SPECIAL_CTX_ID; ctxId++) {
#ifdef EXPERIMENTAL_GUANGQI
        // device mgr在光启环境下需要单独配置权限，防止权限不够打错误日志，考虑到光启环境不涉及直联读写，skip该memCtx。
        if (ctxId == DB_SE_DEV_SHMEMCTX_ID) {
            continue;
        }
#endif
        // check whether ctx do exist, other wise may cause error log.
        if (DbGetShmemCtxById(ctxId, instanceId) != NULL) {
            DbMemCtxT *ctx = DbOpenShmemCtx(ctxId, instanceId);
            if (ctx == NULL && ctxId == DB_TOP_SHMEMCTX_ID) {
                DB_LOG_ERROR_UNFOLD(GMERR_MEMORY_OPERATE_FAILED, "Clt open topShmemCtx, other ctx opening skipped.");
                break;
            }
        }
    }
}

// Called by client during uinit process.
void DbCltCloseAllStaticShmemCtx(uint32_t instanceId)
{
    if (!DbCommonIsSingleCltProcess() || !DbIsTopShmemCtxValid(instanceId)) {
        return;
    }
    // TopShmemCtx should be detached last, as all ctx handles are allocated from it.
    for (uint32_t i = 0; i < DB_START_SPECIAL_CTX_ID; i++) {
#ifdef EXPERIMENTAL_GUANGQI
        // device mgr在光启环境下需要单独配置权限，防止权限不够打错误日志，考虑到光启环境不涉及直联读写，skip该memCtx。
        if (i == DB_SE_DEV_SHMEMCTX_ID) {
            continue;
        }
#endif
        uint32_t ctxId = ((uint32_t)DB_START_SPECIAL_CTX_ID - 1) - i;
        if (DbGetShmemCtxById(ctxId, instanceId) != NULL) {
            (void)DbCloseShmemCtx(ctxId, instanceId);
        }
    }
}

// detach掉系统中所有共享内存段，包括内存管理模块的所有共享内存段以及日志模块的共享内存段（日志模块的detach暂未实现）
void DbDetachAllShm(uint32_t instanceId)
{
    // detach掉内存管理模块涉及的所有共享内存段
    ShmCtxDetachAllShm(instanceId);
}

void ShmemResetMaxTotalPhySizeBottom(const DbMemCtxT *header, uint64_t maxPhySize)
{
    if (header->methodType == ALGO_BLOCK) {
        DbAdptBlockPoolResetMaxPhySize(((const DbBlockShmemCtxT *)header)->shmemBlockAlgo.blkPoolShmPtr, maxPhySize);
    }
    if (header->methodType == ALGO_PAGE_POOL) {
        DbAdptPagePoolResetMaxPhySize(
            ((const DbPagePoolShmemCtxT *)header)->shmemPagePoolAlgo.pagePoolShmPtr, maxPhySize);
    }
}

Status DbShmemCtxResetMaxSize(DbMemCtxT *ctx, uint64_t maxSize)
{
    Status ret = CheckShmemCtxValid(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "reset ctx MaxSize.");
        return ret;
    }
    ret = DbMemCtxLock(ctx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return GMERR_LOCK_NOT_AVAILABLE;
    }
    if (CheckMemCtxMagicCode(ctx) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "reset shmCtx maxSize.");
        DbMemCtxUnLock(ctx);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    uint64_t totalPhySize = DbShmCtxGetTotalPhySize(ctx, false);
    if (totalPhySize > maxSize) {
        DbMemCtxUnLock(ctx);
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE,
            "reset ctx maxTotalPhySize, total physize: %" PRIu64 ", max size: %" PRIu64 ".", totalPhySize, maxSize);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (ctx->methodType == ALGO_BLOCK) {
        ret = DbAdptBlockPoolResetMaxSize(((DbBlockShmemCtxT *)ctx)->shmemBlockAlgo.blkPoolShmPtr, maxSize);
    } else if (ctx->methodType == ALGO_PAGE_POOL) {
        ret = DbAdptPagePoolResetMaxSize(((DbPagePoolShmemCtxT *)ctx)->shmemPagePoolAlgo.pagePoolShmPtr, maxSize);
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "reset ctx maxSize.");
    }
    DbMemCtxUnLock(ctx);
    return ret;
}

uint32_t DbGetShmPermission(void)
{
    return g_gmdbShmemPermission;
}

uint32_t DbGetDevShmPermission(void)
{
    return g_gmdbDevShmemPermission;
}

static Status SetShmemPermission(char *permissionStr, uint32_t *permissionOutput)
{
    DB_POINTER2(permissionStr, permissionOutput);

    uint32_t len = (uint32_t)strlen(permissionStr);
    DB_ASSERT(len == SHM_PERMISSION_LEN);  // 在CfgParamValidateShmemPermission函数中已校验

    uint32_t permissionBit = 0;
    // permissionStr第一位是八进制标识位，不进行计算
    for (uint32_t i = 0; i < len - 1; i++) {
        if ((permissionStr[i + 1] > '7') || (permissionStr[i + 1] < '0')) {
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "set shmem permission.");
            return GMERR_DATA_EXCEPTION;
        }
        uint32_t bitOperateNum = ((((SHM_PERMISSION_LEN - 1) - 1) - i) * (SHM_PERMISSION_LEN - 1));
        permissionBit |= (uint32_t)(((uint8_t)((uint8_t)permissionStr[i + 1] - '0')) << bitOperateNum);
    }

    *permissionOutput = permissionBit;
    return GMERR_OK;
}

// 仅在服务端调用。服务端读取配置文件中的permission并更新g_gmdbShmemPermission。
Status UpdateShmemPermission(DbInstanceHdT dbInstance)
{
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(dbInstance);
    DbCfgValueT cfgValue;
    Status ret = DbCfgGet(cfgHandle, DB_CFG_SHMEM_PERMISSION, &cfgValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get shmem permission from config file.");
        return ret;
    }
    ret = SetShmemPermission(cfgValue.str, &g_gmdbShmemPermission);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set shmem permission.");
        return ret;
    }

    ret = DbCfgGet(cfgHandle, DB_CFG_DEV_SHMEM_PERMISSION, &cfgValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get device shmem permission from config file.");
        return ret;
    }
    if (strlen(cfgValue.str) == 0) {
        // 如果用户未配置DB_CFG_DEV_SHMEM_PERMISSION，则默认使用DB_CFG_SHMEM_PERMISSION的值。
        g_gmdbDevShmemPermission = g_gmdbShmemPermission;
    } else {
        ret = SetShmemPermission(cfgValue.str, &g_gmdbDevShmemPermission);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "set device shmem permission.");
        }
    }
    return ret;
}

#ifdef HPE
// 仅在服务端调用。服务端读取配置文件中的hpeAddShmemAcl并更新g_gmdbGroupAclEntry。
Status UpdateAdptShmemGroupAcl(void)
{
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);
    DbCfgValueT cfgValue = {0};
    Status ret = DbCfgGet(cfgHandle, DB_CFG_HPE_ADD_SHMEM_ACL, &cfgValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get hpeAddShmemAcl from config file.");
        return ret;
    }
    if (strlen(cfgValue.str) != 0) {
        ret = DbAdptUpdateShmemGroupAcl(cfgValue.str);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "update shmem group acl, config value: %s.", cfgValue.str);
            return ret;
        }
    }
    return GMERR_OK;
}
#endif

static DbTopShmemCtxT *AttachTopShmemCtx(uint32_t instanceId)
{
    uint32_t topShmemCtxKey = DbConstructShmemKeyById(instanceId, DB_TOP_SHMEM_ID);
    DbTopShmemCtxT *pTopShmemCtx = (DbTopShmemCtxT *)DB_SHMEM_ATTACH(topShmemCtxKey, RW_PERMISSION);
    return pTopShmemCtx;
}

// If isShmemCtx boolean is false, means caller wants those shmem struct addr registered in topShmemCtx.
void *GetAddrById(uint32_t id, uint32_t instanceId, bool isShmemCtx)
{
    if ((isShmemCtx && id >= DB_MAX_FIXED_ID) || (!isShmemCtx && id >= DB_MAX_SHM_STRUCT_ID)) {
        DB_LOG_ERROR(
            GMERR_MEMORY_OPERATE_FAILED, "get %s addr by id %" PRIu32 ".", isShmemCtx ? "shmemCtx" : "struct", id);
        return NULL;
    }
    DbInstanceT *dbInstance = NULL;
    Status ret = DbGetInstanceByIdNolock(instanceId, &dbInstance);
    if (ret != GMERR_OK) {
        return NULL;
    }
    // Get addr of topShmemCtx.
    TopShmemCtxEntryLock(instanceId, dbInstance);
    DbTopShmemCtxT *cachedAddr = GetTopShmemCtxAddr(instanceId, dbInstance);
    DbTopShmemCtxT *pTopShmemCtx = NULL;
    if (cachedAddr == NULL) {
        pTopShmemCtx = AttachTopShmemCtx(instanceId);
        if (pTopShmemCtx == NULL) {
            uint32_t key = DbConstructShmemKeyById(instanceId, DB_TOP_SHMEM_ID);
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
                "get topShmemCtx. Key: %" PRIu32 ", perm: %" PRIu32 ", OS ret: %" PRId32 ".", key,
                DbAdptGetShmPermByKey(key), (int32_t)errno);
            TopShmemCtxEntryUnlock(instanceId, dbInstance);
            return NULL;
        }
        SetTopShmemCtxAddr(pTopShmemCtx, dbInstance);
    } else {
        pTopShmemCtx = cachedAddr;
    }
    TopShmemCtxEntryUnlock(instanceId, dbInstance);

    if (isShmemCtx && id == DB_TOP_SHMEMCTX_ID) {
        return pTopShmemCtx;
    }
    // 为实现原子读，先用整数型变量承载shmptr。
    uint64_t shmemCtxPtrTmp = DbAtomicGet64((uint64_t *)&(pTopShmemCtx->shmemCtxPtr[id]));
    ShmemPtrT shmemCtxPtr = *(ShmemPtrT *)&shmemCtxPtrTmp;
    ShmemPtrT shmPtr = isShmemCtx ? shmemCtxPtr : pTopShmemCtx->shmemStructPtr[id];

    return DbShmPtrToAddr(shmPtr);
}

bool CheckAddrAttachById(uint32_t id, uint32_t instanceId)
{
    DbInstanceT *dbInstance = NULL;
    Status ret = DbGetInstanceById(instanceId, &dbInstance);
    if (ret != GMERR_OK) {
        return false;
    }
    // Get addr of topShmemCtx.
    TopShmemCtxEntryLock(instanceId, dbInstance);
    DbTopShmemCtxT *cachedAddr = GetTopShmemCtxAddr(instanceId, dbInstance);
    if (cachedAddr == NULL) {
        TopShmemCtxEntryUnlock(instanceId, dbInstance);
        DbReleaseInstance(dbInstance);
        return false;
    }
    if (id == DB_TOP_SHMEMCTX_ID) {
        TopShmemCtxEntryUnlock(instanceId, dbInstance);
        DbReleaseInstance(dbInstance);
        return true;
    }
    DbTopShmemCtxT *pTopShmemCtx = cachedAddr;
    TopShmemCtxEntryUnlock(instanceId, dbInstance);
    DbReleaseInstance(dbInstance);

    // 为实现原子读，先用整数型变量承载shmptr。
    uint64_t shmemCtxPtrTmp = DbAtomicGet64((uint64_t *)&(pTopShmemCtx->shmemCtxPtr[id]));
    ShmemPtrT shmemCtxPtr = *(ShmemPtrT *)&shmemCtxPtrTmp;
    return DbIsShmPtrValid(shmemCtxPtr);  // review1: 这里要看是否真正attach了
}

DbMemCtxT *DbGetShmemCtxById(uint32_t ctxId, uint32_t instanceId)
{
    // DB_INVALID_UINT32 now used as specific non-valid ctxId, should return NULL and avoid printing error log.
    if (SECUREC_UNLIKELY(ctxId == DB_INVALID_UINT32)) {
        return NULL;
    }
    return (DbMemCtxT *)GetAddrById(ctxId, instanceId, true);
}

bool DbCheckShmemCtxAttachById(uint32_t ctxId, uint32_t instanceId)
{
    // DB_INVALID_UINT32 now used as specific non-valid ctxId, should return NULL and avoid printing error log.
    if (SECUREC_UNLIKELY(ctxId == DB_INVALID_UINT32)) {
        return false;
    }
    return CheckAddrAttachById(ctxId, instanceId);
}

void *DbGetShmemStructById(uint32_t structId, uint32_t instanceId)
{
    return GetAddrById(structId, instanceId, false);
}

uint32_t DbMemCtxGetBlockPoolMaxSegNum(void)
{
    return DbAdptGetBlockPoolMaxSegNum();
}

bool DbShmemCtxIsDeleteReady(DbMemCtxT *ctx)
{
    Status ret = CheckShmemCtxValid(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "If shmCtx is ready to delete.");
        return false;
    }
    ret = DbMemCtxLock(ctx);
    if (ret != GMERR_OK) {
        return false;
    }
    DB_ASSERT(ctx->memType == DB_SHARED_MEMORY);
    if (ctx->attachCount == 0) {
        DbMemCtxUnLock(ctx);
        return true;
    }
    DbMemCtxUnLock(ctx);
    return false;
}

// 调用者保证入参合法。
ShmemPtrT DbShmemStructAllocById(DbMemCtxT *parent, uint32_t size, DbShmemStructIdE id)
{
    if (parent == NULL || parent->magicCode != DB_MAGIC_CODE || parent->memType != DB_SHARED_MEMORY) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "%s", "alloc shm struct by id.");
        return DB_INVALID_SHMPTR;
    }
    if (id >= DB_MAX_SHM_STRUCT_ID) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "%s id: %" PRId32 ".", "alloc shm struct by id.", (int32_t)id);
        return DB_INVALID_SHMPTR;
    }
    ShmemPtrT newShmPtr = DbShmemCtxAlloc(parent, size);
    if (!DbIsShmPtrValid(newShmPtr)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "%s segId: %" PRIu32 ", offset: %" PRIu32 ".", "alloc shm struct by id.",
            newShmPtr.segId, newShmPtr.offset);
        return DB_INVALID_SHMPTR;
    }
    if (RegStructIdToTopShmCtx(id, newShmPtr, parent->instanceId) != GMERR_OK) {
        DbShmemCtxFree(parent, newShmPtr);
        return DB_INVALID_SHMPTR;
    }
    return newShmPtr;
}

// 调用者保证入参合法。
void DbShmemStructFreeById(DbMemCtxT *parent, ShmemPtrT ptr, DbShmemStructIdE id)
{
    Status ret = CheckShmemCtxValid(parent);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "free shmPtr by id.");
        return;
    }
    DbShmemCtxFree(parent, ptr);
    if (RmStructCacheFromTopShmCtx(id, parent->instanceId, ptr) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "remove shmem struct id: %" PRId32 ".", (int32_t)id);
        return;
    }
}

// Caller should make sure lock is acquired outside.
static uint32_t ShmemCtxGetUserAllocSize(const DbMemCtxT *ctx, ShmemPtrT ptr)
{
    DB_ASSERT(
        ctx->methodType == ALGO_BLOCK || ctx->methodType == ALGO_PAGE_POOL || ctx->methodType == ALGO_RSMEM_BLOCK);
    uint32_t allocSize = 0;
    if (ctx->methodType == ALGO_BLOCK) {
        allocSize = DbAdptBlockPoolGetUserAllocSize(ptr);
    } else if (ctx->methodType == ALGO_PAGE_POOL) {
        allocSize = DbAdptPagePoolGetUserAllocSize(ptr);
    } else if (ctx->methodType == ALGO_RSMEM_BLOCK) {
        allocSize = DbRsmGetUserAllocSize(ptr);
    }
    return allocSize;
}

static Status ShmemCtxCheckAllocSizeOnTree(DbMemCtxT *ctx, uint32_t size)
{
    DB_POINTER(ctx);
    // 如果开启统计memCtx树的allocSize，则需要判断用户使用的内存是否达到约束上限。
    if (ctx->collectAllocSizeOnThisTree) {
        MemCtxUpdateAllocSizeOnTree(ctx, size, true);
        DbMemCtxT *thresholdParent =
            (DbMemCtxT *)DbGetShmemCtxById(ctx->shmCtxPtrs.thresholdParentCtxId, ctx->instanceId);
        if (thresholdParent != NULL) {
            uint64_t parentAllocSize = DbAtomicGet64(&(thresholdParent->totalAllocSizeOnThisTree));
            uint64_t parentMaxAllocSize = DbAtomicGet64(&(thresholdParent->maxTotalAllocSize));
            if (parentAllocSize > parentMaxAllocSize) {
                DB_LOG_ERROR(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD,
                    "ShmCtx alloc exceeds threshold, ctxId: %" PRIu32 ", ctxName: %s, parentCtxId: %" PRIu32
                    ", parentCtxName: %s. ParentMaxAllocSize: %" PRIu64 ", new allocSize: %" PRIu32
                    ", parent curr allocSize: %" PRIu64 ".",
                    ctx->ctxId, ctx->ctxName, thresholdParent->ctxId, thresholdParent->ctxName, parentMaxAllocSize,
                    size, (parentAllocSize - size));
                MemCtxUpdateAllocSizeOnTree(ctx, size, false);
                return GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD;
            }
        }
    }
    return GMERR_OK;
}

static void ShmemCtxRecordPeakInfo(DbMemCtxT *ctx)
{
    if (SECUREC_LIKELY(g_gmdbPeakSizeDfxThresh == DB_INVALID_UINT64)) {
        return;
    }
    uint64_t newPeakAllocSize = DB_MAX(ctx->peakAllocSize, ctx->totalAllocSize);
    // Only print log at the moment when memCtx peak allocSize exceeds the threshold.
    if (newPeakAllocSize >= g_gmdbPeakSizeDfxThresh && ctx->peakAllocSize < g_gmdbPeakSizeDfxThresh) {
        DB_LOG_WARN(GMERR_OUT_OF_MEMORY,
            "ShmCtx reaches mem peak. name: %s, id: %" PRIu32 ", totalAllocSize: %" PRIu64 ", totalPhySize: %" PRIu64
            ". Current log threshold: %" PRIu64 ".",
            ctx->ctxName, ctx->ctxId, ctx->totalAllocSize, DbShmCtxGetTotalPhySize(ctx, false),
            g_gmdbPeakSizeDfxThresh);
    }
#ifdef DB_MEM_TRACE
    if (!ctx->enableMemTrace || ctx->memtraceThreshold == DB_INVALID_UINT64) {
        return;
    }
    // Only pring all callstack log at the moment when memCtx peak allocSize exceeds the threshold.
    if (newPeakAllocSize >= ctx->memtraceThreshold && ctx->peakAllocSize < ctx->memtraceThreshold) {
        DbMemCtxPrintAllCallStack(ctx, false);
    }
#endif
}

ShmemPtrT RsmEscapeCtxAlloc(const DbMemCtxT *currCtx, size_t size)
{
    // 内存不足场景下, 存储层设置了g_gmdbUseEscapeFlag才允许使用逃生通道申请。
    if (!g_gmdbUseEscapeFlag) {
        return DB_INVALID_SHMPTR;
    }
    DbMemCtxT *rsmEscapeCtx = DbRsmGetEscapeCtx();
    if (rsmEscapeCtx == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "rsmEscapeCtx is null.");
        return DB_INVALID_SHMPTR;
    }
    // 校验逃生通道ctx以防循环调用, 非错误场景不打印日志
    if (currCtx == rsmEscapeCtx) {
        return DB_INVALID_SHMPTR;
    }

    ShmemPtrT ptr = DbShmemCtxAlloc(rsmEscapeCtx, size);
    if (SECUREC_UNLIKELY(!DbIsShmPtrValid(ptr))) {
        DB_LOG_ERROR_UNFOLD(
            GMERR_OUT_OF_MEMORY, "rsm-escapeCtx alloc. totalAllocSize: %" PRIu64, rsmEscapeCtx->totalAllocSize);
        // 极端场景下, addr空间不足会失败，只打印日志
    }
    return ptr;
}

#if (defined HPE || defined RTOSV2 || defined RTOSV2X)
// for hpe
ShmemPtrT DbShmemCtxAlloc(DbMemCtxT *ctx, uint32_t size)
{
    Status ret = CheckShmemCtxValid(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Alloc shm.");
        return DB_INVALID_SHMPTR;
    }
    if (DbMemCtxLock(ctx) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Alloc shm.");
        return DB_INVALID_SHMPTR;
    }
    if (CheckMemCtxMagicCode(ctx) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Alloc shm.");
        DbMemCtxUnLock(ctx);
        return DB_INVALID_SHMPTR;
    }
    const DbShmemCtxMethodsT *shmemMethods = GetShmemMethods((uint32_t)ctx->methodType);
    DB_POINTER(shmemMethods);
    ShmemPtrT ptr = shmemMethods->dbShmCtxAlloc(ctx, size);
    if (!DbIsShmPtrValid(ptr)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
            "Alloc ShmCtx. Id: %" PRIu32 ", name: %s, size: %" PRIu32 ", attachCount: %" PRIu32
            ", totalAllocSize: %" PRIu64 ", maxTotalAllocSize: %" PRIu64 ", totalAllocSizeOnThisTree: %" PRIu64
            ", childNum: %" PRIu32 ".",
            ctx->ctxId, ctx->ctxName, size, ctx->attachCount, ctx->totalAllocSize, ctx->maxTotalAllocSize,
            ctx->totalAllocSizeOnThisTree, ctx->childNum);
        DbAlarmUpdateFailCntByInstanceId(ctx->instanceId, DB_ALARM_SHM_USED_INFO, 1);
        DbMemCtxUnLock(ctx);
        if (ctx->methodType != ALGO_RSMEM_BLOCK) {
            return DB_INVALID_SHMPTR;
        }
        // 保留内存的类型，尝试切换逃生memCtx申请内存
        return RsmEscapeCtxAlloc(ctx, size);
    }
    uint32_t realAllocSize = ShmemCtxGetUserAllocSize(ctx, ptr);  // hpe环境下，ptr中只记录pool实际分配的大小。
    // 如果开启统计memCtx树的allocSize，则需要判断用户使用的内存是否达到约束上限。
    if (ShmemCtxCheckAllocSizeOnTree(ctx, realAllocSize) != GMERR_OK) {
        shmemMethods->dbShmCtxFree(ctx, ptr);
        DbAlarmUpdateFailCntByInstanceId(ctx->instanceId, DB_ALARM_SHM_USED_INFO, 1);
        DbMemCtxUnLock(ctx);
        return DB_INVALID_SHMPTR;
    }
    DbAlarmUpdateSuccCntByInstanceId(ctx->instanceId, DB_ALARM_SHM_USED_INFO, 1);
    // 在对应的memctx上更新totalAllocSize信息
    ctx->totalAllocSize += realAllocSize;
    ShmemCtxRecordPeakInfo(ctx);
    ctx->peakAllocSize = DB_MAX(ctx->peakAllocSize, ctx->totalAllocSize);
    DbMemCtxUnLock(ctx);
    return ptr;
}

ShmemPtrT DbShmemCtxAllocNoLock(DbMemCtxT *ctx, uint32_t size)
{
    Status ret = CheckShmemCtxValid(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Alloc shm nolock.");
        return DB_INVALID_SHMPTR;
    }
    if (CheckMemCtxMagicCode(ctx) != GMERR_OK || ctx->memType != DB_RESERVE_MEMORY) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Alloc shm, type:%" PRIu32, ctx->memType);
        return DB_INVALID_SHMPTR;
    }
    const DbShmemCtxMethodsT *shmemMethods = GetShmemMethods((uint32_t)ctx->methodType);
    DB_POINTER(shmemMethods);
    ShmemPtrT ptr = shmemMethods->dbShmCtxAlloc(ctx, size);
    if (!DbIsShmPtrValid(ptr)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
            "Alloc ShmCtx. id: %" PRIu32 ", name: %s, size: %" PRIu32 ", attachCount: %" PRIu32
            ", totalAllocSize: %" PRIu64 ", maxTotalAllocSize: %" PRIu64 ", totalAllocSizeOnThisTree: %" PRIu64
            ", childNum: %" PRIu32 ".",
            ctx->ctxId, ctx->ctxName, size, ctx->attachCount, ctx->totalAllocSize, ctx->maxTotalAllocSize,
            ctx->totalAllocSizeOnThisTree, ctx->childNum);
        DbAlarmUpdateFailCntByInstanceId(ctx->instanceId, DB_ALARM_SHM_USED_INFO, 1);
        if (ctx->methodType != ALGO_RSMEM_BLOCK) {
            return DB_INVALID_SHMPTR;
        }
        // 保留内存的类型，尝试切换逃生memCtx申请内存
        return RsmEscapeCtxAlloc(ctx, size);
    }
    uint32_t realAllocSize = ShmemCtxGetUserAllocSize(ctx, ptr);  // hpe环境下，ptr中只记录pool实际分配的大小。
    // 如果开启统计memCtx树的allocSize，则需要判断用户使用的内存是否达到约束上限。
    if (ShmemCtxCheckAllocSizeOnTree(ctx, realAllocSize) != GMERR_OK) {
        shmemMethods->dbShmCtxFree(ctx, ptr);
        DbAlarmUpdateFailCntByInstanceId(ctx->instanceId, DB_ALARM_SHM_USED_INFO, 1);
        return DB_INVALID_SHMPTR;
    }
    DbAlarmUpdateSuccCntByInstanceId(ctx->instanceId, DB_ALARM_SHM_USED_INFO, 1);
    // 在对应的memctx上更新totalAllocSize信息
    ctx->totalAllocSize += realAllocSize;
    ShmemCtxRecordPeakInfo(ctx);
    ctx->peakAllocSize = DB_MAX(ctx->peakAllocSize, ctx->totalAllocSize);
    return ptr;
}

#else
// 共享内存申请和释放是db内部接口，其他模块调用者保证入参ctx的合法性。
ShmemPtrT DbShmemCtxAlloc(DbMemCtxT *ctx, uint32_t size)
{
    Status ret = CheckShmemCtxValid(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Alloc shm.");
        return DB_INVALID_SHMPTR;
    }
    if (DbMemCtxLock(ctx) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Alloc shm.");
        return DB_INVALID_SHMPTR;
    }
    if (CheckMemCtxMagicCode(ctx) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Alloc shm.");
        DbMemCtxUnLock(ctx);
        return DB_INVALID_SHMPTR;
    }
    uint32_t realSize =
        (ctx->methodType == ALGO_PAGE_POOL) ? ((DbPagePoolShmemCtxT *)ctx)->shmemPagePoolAlgo.attr.pageSize : size;
    // 如果开启统计memCtx树的allocSize，则需要判断用户使用的内存是否达到约束上限。
    if (ShmemCtxCheckAllocSizeOnTree(ctx, realSize) != GMERR_OK) {
        DbAlarmUpdateFailCntByInstanceId(ctx->instanceId, DB_ALARM_SHM_USED_INFO, 1);
        DbMemCtxUnLock(ctx);
        return DB_INVALID_SHMPTR;
    }

    const DbShmemCtxMethodsT *shmemMethods = GetShmemMethods((uint32_t)ctx->methodType);
    DB_POINTER(shmemMethods);
    ShmemPtrT ptr = shmemMethods->dbShmCtxAlloc(ctx, realSize);
    if (!DbIsShmPtrValid(ptr)) {
        DbAlarmUpdateFailCntByInstanceId(ctx->instanceId, DB_ALARM_SHM_USED_INFO, 1);
        // 申请失败，对统计信息做回退操作, 函数内部会判断collectAllocSizeOnThisTree是否打开。
        MemCtxUpdateAllocSizeOnTree(ctx, realSize, false);
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
            "Alloc shmem. id: %" PRIu32 ", name: %s, size: %" PRIu32 ", attachCount: %" PRIu32
            ", totalAllocSize: %" PRIu64 ", maxTotalAllocSize: %" PRIu64 ", totalAllocSizeOnThisTree: %" PRIu64
            ", childNum: %" PRIu32 ".",
            ctx->ctxId, ctx->ctxName, size, ctx->attachCount, ctx->totalAllocSize, ctx->maxTotalAllocSize,
            ctx->totalAllocSizeOnThisTree, ctx->childNum);
        DbMemCtxUnLock(ctx);
        if (ctx->methodType != ALGO_RSMEM_BLOCK) {
            return DB_INVALID_SHMPTR;
        }
        // 保留内存的类型，尝试切换逃生memCtx申请内存
        return RsmEscapeCtxAlloc(ctx, size);
    }
    DbAlarmUpdateSuccCntByInstanceId(ctx->instanceId, DB_ALARM_SHM_USED_INFO, 1);
    // 在对应的memctx上更新totalAllocSize信息
    ctx->totalAllocSize += realSize;
    ShmemCtxRecordPeakInfo(ctx);
    ctx->peakAllocSize = DB_MAX(ctx->peakAllocSize, ctx->totalAllocSize);
    DbMemCtxUnLock(ctx);
    return ptr;
}

ShmemPtrT DbShmemCtxAllocNoLock(DbMemCtxT *ctx, uint32_t size)
{
    Status ret = CheckShmemCtxValid(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Alloc shm.");
        return DB_INVALID_SHMPTR;
    }
    if (CheckMemCtxMagicCode(ctx) != GMERR_OK || ctx->memType != DB_RESERVE_MEMORY) {
        // 由上层负责加锁，目前只有保留内存场景可用，在DbCreateReserveMemCtx中已经加过锁
        // 需要保证rsmUndo不会被并发使用，因此将锁提到外层
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Alloc shm, type:%" PRIu32, ctx->memType);
        return DB_INVALID_SHMPTR;
    }
    uint32_t realSize =
        (ctx->methodType == ALGO_PAGE_POOL) ? ((DbPagePoolShmemCtxT *)ctx)->shmemPagePoolAlgo.attr.pageSize : size;
    // 如果开启统计memCtx树的allocSize，则需要判断用户使用的内存是否达到约束上限。
    if (ShmemCtxCheckAllocSizeOnTree(ctx, realSize) != GMERR_OK) {
        DbAlarmUpdateFailCntByInstanceId(ctx->instanceId, DB_ALARM_SHM_USED_INFO, 1);
        return DB_INVALID_SHMPTR;
    }

    const DbShmemCtxMethodsT *shmemMethods = GetShmemMethods((uint32_t)ctx->methodType);
    DB_POINTER(shmemMethods);
    ShmemPtrT ptr = shmemMethods->dbShmCtxAlloc(ctx, realSize);
    if (!DbIsShmPtrValid(ptr)) {
        DbAlarmUpdateFailCntByInstanceId(ctx->instanceId, DB_ALARM_SHM_USED_INFO, 1);
        // 申请失败，对统计信息做回退操作, 函数内部会判断collectAllocSizeOnThisTree是否打开。
        MemCtxUpdateAllocSizeOnTree(ctx, realSize, false);
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
            "Alloc shmem. id: %" PRIu32 ", name: %s, size: %" PRIu32 ", attachCount: %" PRIu32
            ", totalAllocSize: %" PRIu64 ", maxTotalAllocSize: %" PRIu64 ", totalAllocSizeOnThisTree: %" PRIu64
            ", childNum: %" PRIu32 ".",
            ctx->ctxId, ctx->ctxName, size, ctx->attachCount, ctx->totalAllocSize, ctx->maxTotalAllocSize,
            ctx->totalAllocSizeOnThisTree, ctx->childNum);
        if (ctx->methodType != ALGO_RSMEM_BLOCK) {
            return DB_INVALID_SHMPTR;
        }
        // 保留内存的类型，尝试切换逃生memCtx申请内存
        return RsmEscapeCtxAlloc(ctx, size);
    }
    DbAlarmUpdateSuccCntByInstanceId(ctx->instanceId, DB_ALARM_SHM_USED_INFO, 1);
    // 在对应的memctx上更新totalAllocSize信息
    ctx->totalAllocSize += realSize;
    ShmemCtxRecordPeakInfo(ctx);
    ctx->peakAllocSize = DB_MAX(ctx->peakAllocSize, ctx->totalAllocSize);
    return ptr;
}
#endif

ShmemPtrT DbShmemCtxAllocAlign(DbMemCtxT *ctx, uint32_t size, uint32_t alignSize)
{
    return DbShmemCtxAlloc(ctx, size + alignSize);
}

void DbShmemCtxFreeInnerNoLock(DbMemCtxT *ctx, ShmemPtrT ptr)
{
    if (CheckMemCtxMagicCode(ctx) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Free shm nolock.");
        return;
    }
    // db启动过程中初始化全局变量，db运行期间，不会修改、释放该全局变量。
    const DbShmemCtxMethodsT *shmemMethods = GetShmemMethods((uint32_t)ctx->methodType);
    DB_POINTER(shmemMethods);
    if (SECUREC_UNLIKELY(shmemMethods->dbShmCtxFree == NULL)) {
        DB_LOG_ERROR(
            GMERR_MEMORY_OPERATE_FAILED, "Inv dhmCtxFree func, name : %s, id : %" PRIu32 ".", ctx->ctxName, ctx->ctxId);
        return;
    }
    if (SECUREC_UNLIKELY(ctx->isReset)) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Free shmPtr. Ctx been reset.");
        return;
    }
    uint32_t size = ShmemCtxGetUserAllocSize(ctx, ptr);
    Status ret = shmemMethods->dbShmCtxFree(ctx, ptr);
    // 在对应的memctx上更新totalAllocSize信息
    if (ret == GMERR_OK) {
        ctx->totalAllocSize -= size;
        // 统计memCtx树的allocSize，如果开关开启
        if (ctx->collectAllocSizeOnThisTree) {
            MemCtxUpdateAllocSizeOnTree(ctx, size, false);
        }
    }
}

void DbShmemCtxFreeInner(DbMemCtxT *ctx, ShmemPtrT ptr)
{
    Status ret = CheckShmemCtxValid(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Free shm.");
        return;
    }
    ret = DbMemCtxLock(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_LOCK_NOT_AVAILABLE, "Free shm.");
        return;
    }
    DbShmemCtxFreeInnerNoLock(ctx, ptr);
    DbMemCtxUnLock(ctx);
}

void RsmEscapeCtxFree(DbMemCtxT *rsmEscapeCtx, ShmemPtrT ptr)
{
    if (g_gmdbUseEscapeFlag) {
        DbShmemCtxFreeInner(rsmEscapeCtx, ptr);
    }
}

void RsmEscapeCtxFreeNoLock(DbMemCtxT *rsmEscapeCtx, ShmemPtrT ptr)
{
    if (g_gmdbUseEscapeFlag) {
        DbShmemCtxFreeInnerNoLock(rsmEscapeCtx, ptr);
    }
}

// 调用者保证入参合法。
void DbShmemCtxFree(DbMemCtxT *ctx, ShmemPtrT ptr)
{
    if (SECUREC_UNLIKELY(!DbIsShmPtrValid(ptr) || CheckShmPtrZero(ptr))) {
        if (ctx != NULL) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
                "Inv shmPtr, name: %s, id: %" PRIu32 ". segId: %" PRIu32 ", offset: %" PRIu32 ".", ctx->ctxName,
                ctx->ctxId, ptr.segId, ptr.offset);
        } else {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Inv shmPtr, segId: %" PRIu32 ", offset: %" PRIu32 ".", ptr.segId,
                ptr.offset);
        }
        return;
    }

    if (ctx != NULL && ctx->methodType == ALGO_RSMEM_BLOCK && DbRsmCheckEscapeCtxMark(ptr)) {
        DbMemCtxT *rsmEscapeCtx = DbRsmGetEscapeCtx();
        if (ctx != rsmEscapeCtx) {
            if (rsmEscapeCtx != NULL) {
                RsmEscapeCtxFree(rsmEscapeCtx, ptr);
            } else {
                DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "rsmEscapeCtx is null.");
            }
            return;
        }
    }
    DbShmemCtxFreeInner(ctx, ptr);
}

void DbShmemCtxFreeNoLock(DbMemCtxT *ctx, ShmemPtrT ptr)
{
    if (SECUREC_UNLIKELY(!DbIsShmPtrValid(ptr) || CheckShmPtrZero(ptr))) {
        if (ctx != NULL) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
                "Inv shmPtr, name: %s, id: %" PRIu32 ". segId: %" PRIu32 ", offset: %" PRIu32 ".", ctx->ctxName,
                ctx->ctxId, ptr.segId, ptr.offset);
        } else {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Inv shmPtr, Ptr segId: %" PRIu32 ", offset: %" PRIu32 ".",
                ptr.segId, ptr.offset);
        }
        return;
    }

    if (ctx != NULL && ctx->methodType == ALGO_RSMEM_BLOCK && DbRsmCheckEscapeCtxMark(ptr)) {
        DbMemCtxT *rsmEscapeCtx = DbRsmGetEscapeCtx();
        if (ctx != rsmEscapeCtx) {
            if (rsmEscapeCtx != NULL) {
                RsmEscapeCtxFreeNoLock(rsmEscapeCtx, ptr);
            } else {
                DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Inv rsmescapeCtx.");
            }
            return;
        }
    }
    DbShmemCtxFreeInnerNoLock(ctx, ptr);
}

static void ShmemCtxDeleteDirect(DbMemCtxT *ctx)
{
    DbShmemCtxGlobalLock();
    (void)DeleteMemCtxNode(ctx, false);
    DbShmemCtxGlobalUnlock();
}

void DbDeleteShmemCtx(DbMemCtxT *ctx)
{
    // 对于同一个memctx最好不要调用两次这个接口，并且调用方需保证调用了此接口后将memctx指针置空。
    Status ret = CheckShmemCtxValid(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Delete shmCtx.");
        return;
    }
    ret = DbMemCtxLock(ctx);
    DbInstanceHdT dbInstance = ctx->dbIns;
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_LOCK_NOT_AVAILABLE, "Delete shmCtx %" PRIu32 " name %s.", ctx->ctxId, ctx->ctxName);
        return;
    }
    if (CheckMemCtxMagicCode(ctx) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Delete shm ctx.");
        DbMemCtxUnLock(ctx);
        return;
    }
    if (ctx->markDeleted) {
        DB_LOG_ERROR(
            GMERR_MEMORY_OPERATE_FAILED, "Delete shmCtx %" PRIu32 " name %s, mark deleted.", ctx->ctxId, ctx->ctxName);
        DbMemCtxUnLock(ctx);
        return;
    }
    DbMemCtxUnLock(ctx);
    // Destroy the subtree rooted by ctx, and put all the nodes into detachMgr.
    ShmemCtxDeleteDirect(ctx);
    // Check the ctx deleteMgr and delete what is ready to delete.
    DbShmemCtxDetachMgrScanSrv(dbInstance);
}

#if (defined HPE || defined RTOSV2 || defined RTOSV2X)
Status ShmemCtxBatchAllocInner(DbMemCtxT *ctx, uint32_t size, ShmemPtrT *ptrArr, uint32_t arrLen)
{
    const DbShmemCtxMethodsT *shmemMethods = GetShmemMethods((uint32_t)ctx->methodType);
    DB_ASSERT(shmemMethods != NULL);
    uint32_t allocSizeSum = 0;
    uint32_t allocCnt = 0;
    for (uint32_t i = 0; i < arrLen; i++) {
        ShmemPtrT ptr = shmemMethods->dbShmCtxAlloc(ctx, size);
        if (DbIsShmPtrValid(ptr)) {
            ptrArr[i] = ptr;
            uint32_t realAllocSize = ShmemCtxGetUserAllocSize(ctx, ptr);
            allocSizeSum += realAllocSize;
            allocCnt++;
        } else {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
                "Alloc shm. id: %" PRIu32 ", name: %s, size: %" PRIu32 ", attachCount: %" PRIu32
                ", totalAllocSize: %" PRIu64 ", maxTotalAllocSize: %" PRIu64 ", totalAllocSizeOnThisTree: %" PRIu64
                ", childNum: %" PRIu32 ".",
                ctx->ctxId, ctx->ctxName, size, ctx->attachCount, ctx->totalAllocSize, ctx->maxTotalAllocSize,
                ctx->totalAllocSizeOnThisTree, ctx->childNum);
            break;
        }
    }
    // 有一个申请失败，整个批申请也视为失败。需要释放已申请的内存。
    if (SECUREC_UNLIKELY(allocCnt != arrLen || ShmemCtxCheckAllocSizeOnTree(ctx, allocSizeSum) != GMERR_OK)) {
        for (uint32_t i = 0; i < allocCnt; i++) {
            shmemMethods->dbShmCtxFree(ctx, ptrArr[i]);
            ptrArr[i] = DB_INVALID_SHMPTR;
        }
        DbAlarmUpdateFailCntByInstanceId(ctx->instanceId, DB_ALARM_SHM_USED_INFO, 1);
        return GMERR_OUT_OF_MEMORY;
    }
    ctx->totalAllocSize += allocSizeSum;
    ShmemCtxRecordPeakInfo(ctx);
    ctx->peakAllocSize = DB_MAX(ctx->peakAllocSize, ctx->totalAllocSize);
    DbAlarmUpdateSuccCntByInstanceId(ctx->instanceId, DB_ALARM_SHM_USED_INFO, 1);
    return GMERR_OK;
}

Status DbShmemCtxBatchAlloc(DbMemCtxT *ctx, uint32_t size, ShmemPtrT *ptrArr, uint32_t arrLen)
{
    if (CheckShmemCtxValid(ctx) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Batch alloc shm.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (DbMemCtxLock(ctx) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Batch alloc shm.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (CheckMemCtxMagicCode(ctx) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Batch alloc shm.");
        DbMemCtxUnLock(ctx);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    Status ret = ShmemCtxBatchAllocInner(ctx, size, ptrArr, arrLen);
    DbMemCtxUnLock(ctx);
    return ret;
}
#else
Status ShmemCtxBatchAllocInner(DbMemCtxT *ctx, uint32_t size, ShmemPtrT *ptrArr, uint32_t arrLen)
{
    Status ret = GMERR_OK;
    uint32_t realSize =
        (ctx->methodType == ALGO_PAGE_POOL) ? ((DbPagePoolShmemCtxT *)ctx)->shmemPagePoolAlgo.attr.pageSize : size;
    uint32_t allocSizeSum = realSize * arrLen;
    if (SECUREC_UNLIKELY((ret = ShmemCtxCheckAllocSizeOnTree(ctx, allocSizeSum)) != GMERR_OK)) {
        DbAlarmUpdateFailCntByInstanceId(ctx->instanceId, DB_ALARM_SHM_USED_INFO, 1);
        return ret;
    }

    uint32_t allocCnt = 0;
    const DbShmemCtxMethodsT *shmemMethods = GetShmemMethods((uint32_t)ctx->methodType);
    DB_ASSERT(shmemMethods != NULL);
    for (uint32_t i = 0; i < arrLen; i++) {
        ShmemPtrT ptr = shmemMethods->dbShmCtxAlloc(ctx, realSize);
        if (DbIsShmPtrValid(ptr)) {
            ptrArr[i] = ptr;
            allocCnt++;
        } else {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
                "Alloc shm. id: %" PRIu32 ", name: %s, size: %" PRIu32 ", attachCount: %" PRIu32
                ", totalAllocSize: %" PRIu64 ", maxTotalAllocSize: %" PRIu64 ", totalAllocSizeOnThisTree: %" PRIu64
                ", childNum: %" PRIu32 ".",
                ctx->ctxId, ctx->ctxName, size, ctx->attachCount, ctx->totalAllocSize, ctx->maxTotalAllocSize,
                ctx->totalAllocSizeOnThisTree, ctx->childNum);
            break;
        }
    }
    // 有一个申请失败，整个批申请也视为失败。
    if (SECUREC_UNLIKELY(allocCnt < arrLen)) {
        for (uint32_t i = 0; i < allocCnt; i++) {
            shmemMethods->dbShmCtxFree(ctx, ptrArr[i]);
            ptrArr[i] = DB_INVALID_SHMPTR;
        }
        MemCtxUpdateAllocSizeOnTree(ctx, allocSizeSum, false);
        DbAlarmUpdateFailCntByInstanceId(ctx->instanceId, DB_ALARM_SHM_USED_INFO, 1);
        return GMERR_OUT_OF_MEMORY;
    }
    DbAlarmUpdateSuccCntByInstanceId(ctx->instanceId, DB_ALARM_SHM_USED_INFO, 1);
    ctx->totalAllocSize += allocSizeSum;
    ShmemCtxRecordPeakInfo(ctx);
    ctx->peakAllocSize = DB_MAX(ctx->peakAllocSize, ctx->totalAllocSize);
    return ret;
}

Status DbShmemCtxBatchAlloc(DbMemCtxT *ctx, uint32_t size, ShmemPtrT *ptrArr, uint32_t arrLen)
{
    Status ret = CheckShmemCtxValid(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Batch alloc shm.");
        return ret;
    }
    if (DbMemCtxLock(ctx) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Batch alloc shm.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (CheckMemCtxMagicCode(ctx) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Batch alloc shm.");
        DbMemCtxUnLock(ctx);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    ret = ShmemCtxBatchAllocInner(ctx, size, ptrArr, arrLen);
    DbMemCtxUnLock(ctx);
    return ret;
}
#endif

/*
 * - Directly trigger shmemCtx arena deletion, rely on dopra to clear all resources. Gmserver will not retry deleting
 * this memCtx. Caller guarantees processes attach to this ctx has been killed.
 * - Does not guarantee immediate deletion of the arena, still we recycle the ctxId through it may be used for new arena
 * for some time.
 * - Use only under HPE scenario.
 */
#if (defined HPE) && (!defined HPE_SIMULATION)
void DbDeleteShmemCtxWhenAbn(DbMemCtxT *ctx)
{
    Status ret = CheckShmemCtxValid(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Delete memCtx.");
        return;
    }
    DbAtomicSet(&(ctx->attachCount), 0u);
    // If client process exits abnormally, DAP will automatically decrease refCnt on client side.
    DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
        "Shmctx deleted in illegal case. CtxId: %" PRIu32 ", ctxName: %s, ptno: %" PRIu32 ".", ctx->ctxId, ctx->ctxName,
        ShmemCtxGetPtNo(ctx));
    DbDeleteShmemCtx(ctx);
    // 定时校准阈值统计。
    DbShmemCtxGlobalLock();
    ShmemCtxRecheckTreeAllocSize(true, DbGetInstanceByMemCtx(ctx));
    DbShmemCtxGlobalUnlock();
}
#else
void DbDeleteShmemCtxWhenAbn(DbMemCtxT *ctx)
{
    Status ret = CheckShmemCtxValid(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Delete memCtx.");
        return;
    }
    DbShmemCtxGlobalLock();
    ShmemCtxRecheckTreeAllocSize(true, DbGetInstanceByMemCtx(ctx));  // Calibrate ctx tree statistics first.
    DbAtomicSet(&(ctx->attachCount), 0u);
    // If client process exits abnormally, DAP will automatically decrease refCnt on client side.
    DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
        "Shmctx deleted in illegal case. id: %" PRIu32 ", name: %s, ptno: %" PRIu32 ".", ctx->ctxId, ctx->ctxName,
        ShmemCtxGetPtNo(ctx));
    // Delink operation guanrantees to succeed, as client does not acquire parent's lock,
    // and delink does not lock brother nodes.
    DbMemCtxT *parentCtx = DbGetParentMemCtxNoLock(ctx);
    if (parentCtx != NULL) {
        DelinkCtxFromParent(parentCtx, ctx, false);
    }
    const DbShmemCtxMethodsT *shmemMethods = GetShmemMethods((uint32_t)ctx->methodType);
    // Server init logic guaranttees non-null function pointer.
    DB_POINTER(shmemMethods);
    DB_POINTER(shmemMethods->dbShmCtxDestroyCtxForce);
    uint64_t totalAllocSize = ctx->totalAllocSize;
    shmemMethods->dbShmCtxDestroyCtxForce(ctx);
    // Update memCtx statistics.
    MemCtxUpdateAllocSizeOnTree(ctx, totalAllocSize, false);
    // Recycle ctxId
    if (RmCtxCacheFromTopShmCtx(ctx->ctxId, ctx->instanceId, ctx->shmPtrSelf) == GMERR_LOCK_NOT_AVAILABLE) {
        (void)RmCtxCacheFromTopShmCtx(ctx->ctxId, ctx->instanceId, ctx->shmPtrSelf);
    }
    // Free memory of the deleted ctx.
    FreeMemCtx(ctx, ctx->shmPtrSelf, ctx->memType, ctx->instanceId);
    DbShmemCtxGlobalUnlock();
}
#endif

Status DbShmemCtxClearIfEmpty(DbMemCtxT *ctx, bool *empty)
{
    Status ret = CheckShmemCtxValid(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Clear empty shmCtx. Id: %" PRIu32 ", Name: %s.", ctx->ctxId, ctx->ctxName);
        return ret;
    }

    ret = DbMemCtxLock(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Clear empty shmCtx. Id: %" PRIu32 ", Name: %s.", ctx->ctxId, ctx->ctxName);
        return GMERR_LOCK_NOT_AVAILABLE;
    }
    if (CheckMemCtxMagicCode(ctx) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Clear shm ctx.");
        DbMemCtxUnLock(ctx);
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    uint64_t size = ctx->totalAllocSize;
    bool isEmpty = false;
    const DbShmemCtxMethodsT *shmemMethods = GetShmemMethods((uint32_t)ctx->methodType);
    DB_POINTER(shmemMethods);
    ret = shmemMethods->dbShmCtxIsEmpty(ctx, &isEmpty);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Check empty shmCtx.");
        DbMemCtxUnLock(ctx);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (empty != NULL) {
        *empty = isEmpty;
    }
    if (isEmpty) {
        ret = shmemMethods->dbShmCtxClearCurCtx(ctx);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Clear empty shmCtx.");
            DbMemCtxUnLock(ctx);
            return ret;
        }
        if (ctx->collectAllocSizeOnThisTree) {
            MemCtxUpdateAllocSizeOnTree(ctx, size, false);
        }
        ctx->isReset = true;
    }

    DbMemCtxUnLock(ctx);
    return GMERR_OK;
}

inline static void DestroyShmemCtxRunLog(DbMemCtxT *ctx)
{
    if (ctx->memType == DB_RESERVE_MEMORY) {
        return;
    }
    uint32_t poolKey = ConstructPoolKeyByCtxId(ctx->instanceId, ctx->ctxId);
    MEM_RUN_LINK_LOG(MEM_RUN_LINK_LOG_SHMEMCTX,
        "Delete shmCtx. Id %" PRIu32 ", name: %s, ptNo %" PRIu32 ", key %" PRIu32 ". DB attachCnt %" PRIu32 ".",
        ctx->ctxId, ctx->ctxName, ShmemCtxGetPtNo(ctx), poolKey, ctx->attachCount);
}

static Status ShmemCtxDestroy(DbMemCtxT *ctx)
{
    Status ret = CheckShmemCtxValid(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Destroy shmCtx.");
        return ret;
    }
    ret = DbMemCtxLock(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_LOCK_NOT_AVAILABLE, "Destroy shmCtx.");
        return GMERR_LOCK_NOT_AVAILABLE;
    }
    if ((ret = CheckMemCtxMagicCode(ctx)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "Destroy shmCtx.");
        goto EXIT;
    }
    if ((ret = CheckShmemPtInfoInMap(ctx)) != GMERR_OK) {
        goto EXIT;
    }
    DbMemCtxT *childCtx = DbShmPtrToAddr(ctx->shmCtxPtrs.shmPtrHead);
    if (childCtx != NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Destroy shmCtx, ctx has child.");
        DbMemCtxUnLock(ctx);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    const DbShmemCtxMethodsT *shmemMethods = GetShmemMethods((uint32_t)ctx->methodType);
    DB_POINTER(shmemMethods);
    ret = shmemMethods->dbShmCtxDestroyCurCtx(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "Destroy shmCtx, maybe refcount not detach, id: %" PRIu32 ", name: %s", ctx->ctxId, ctx->ctxName);
        goto EXIT;
    }
    DestroyShmemCtxRunLog(ctx);
EXIT:
    DbMemCtxUnLock(ctx);
    return ret;
}

inline static void DbCloseShmemCtxRunLog(DbMemCtxT *ctx)
{
    if (ctx->memType == DB_RESERVE_MEMORY) {
        return;
    }
    uint32_t poolKey = ConstructPoolKeyByCtxId(ctx->instanceId, ctx->ctxId);
    MEM_RUN_LINK_LOG(MEM_RUN_LINK_LOG_SHMEMCTX,
        "Close shmCtx. Id %" PRIu32 ", name: %s, ptNo %" PRIu32 ", key %" PRIu32 ". attachCnt %" PRIu32 ".", ctx->ctxId,
        ctx->ctxName, ShmemCtxGetPtNo(ctx), poolKey, ctx->attachCount);
}

// 将内存上下文中管理的内存addr detach出此进程，保证操作系统底层可以真正的清除掉这部分内存
static Status ShmemCtxDetachPool(DbMemCtxT *context)
{
    Status ret = CheckShmemCtxValid(context);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Detach shmCtxPool.");
        return ret;
    }
    DB_ASSERT(context->methodType == ALGO_BLOCK || context->methodType == ALGO_PAGE_POOL);
    ret = DbMemCtxLock(context);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_LOCK_NOT_AVAILABLE, "Detach shmCtxPool.");
        return ret;
    }
    if (CheckMemCtxMagicCode(context) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Detach shmCtxPool.");
        DbMemCtxUnLock(context);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (context->methodType == ALGO_BLOCK) {
        // adpt block pool detach
        DbShmemBlockAlgoT *pShmemBlockAlgo = &((DbBlockShmemCtxT *)context)->shmemBlockAlgo;
        ret = DbAdptBlockPoolDetach(pShmemBlockAlgo->blkPoolShmPtr);
    } else if (context->methodType == ALGO_PAGE_POOL) {
        // adpt block pool detach
        DbShmemPagePoolAlgoT *pShmemPagePoolAlgo = &((DbPagePoolShmemCtxT *)context)->shmemPagePoolAlgo;
        ret = DbAdptPagePoolDetach(pShmemPagePoolAlgo->pagePoolShmPtr);
    }
    if (ret == GMERR_OK) {
        (void)DbAtomicFetchAndSub(&(context->attachCount), 1);
        DbCloseShmemCtxRunLog(context);
    } else {
        DB_LOG_ERROR(ret, "Detach ctx. Id %" PRIu32 ", name %s.", context->ctxId, context->ctxName);
    }
    DbMemCtxUnLock(context);
    return ret;
}

uint32_t DbGetShmemCtxAttachCntNoLock(DbMemCtxT *ctx)
{
    uint32_t refCnt = 0;
    Status ret = CheckShmemCtxValid(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get shmCtx refCnt.");
        return refCnt;
    }
    if (ctx->methodType == ALGO_BLOCK) {
        DbShmemBlockAlgoT *pShmemBlockAlgo = &((DbBlockShmemCtxT *)ctx)->shmemBlockAlgo;
        ret = DbAdptBlockPoolGetRefCnt(pShmemBlockAlgo->blkPoolShmPtr, &refCnt);
    }
    if (ctx->methodType == ALGO_PAGE_POOL) {
        DbShmemPagePoolAlgoT *pShmemPagePoolAlgo = &((DbPagePoolShmemCtxT *)ctx)->shmemPagePoolAlgo;
        ret = DbAdptPagePoolGetRefCnt(pShmemPagePoolAlgo->pagePoolShmPtr, &refCnt);
    }
    if (ret != GMERR_OK) {
        // Zero is considered abnormal for now. RefCnt starts from one as ctx creation is also considered as reference.
        refCnt = 0;
    }
    return refCnt;
}

Status DbShmemCtxDetachMgrInit(DbInstanceHdT dbInstance)
{
    ShmemCtxDetachMgrT *shmemCtxDetachMgr = NULL;

    shmemCtxDetachMgr = DB_MALLOC(sizeof(ShmemCtxDetachMgrT));
    if (shmemCtxDetachMgr == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Init ShmemCtxDetachMgr.");
        return GMERR_INTERNAL_ERROR;
    }
    // allocate memory for the ShmemCtxDetachMgr structure, and then perform the initialization.
    shmemCtxDetachMgr->magicNum = DB_MAGIC_CODE;
    shmemCtxDetachMgr->elemCnt = 0;
    shmemCtxDetachMgr->inited = false;  // 为避免内存底噪增大，这里先不初始化数组。
    if (DbIsMultiInstanceEnabled() && dbInstance) {
        ((DbInstanceT *)dbInstance)->shmemCtxDetachMgr = (void *)shmemCtxDetachMgr;
    } else {
        g_gmdbShmemCtxDetachMgr = shmemCtxDetachMgr;
    }
    DbSpinInit(&shmemCtxDetachMgr->lock);
    return GMERR_OK;
}

void DbShmemCtxDetachMgrDestroy(DbInstanceHdT dbInstance)
{
    ShmemCtxDetachMgrT **shmemCtxDetachMgr = &g_gmdbShmemCtxDetachMgr;
    if (DbIsMultiInstanceEnabled() && dbInstance) {
        shmemCtxDetachMgr = (ShmemCtxDetachMgrT **)(&(((DbInstanceT *)dbInstance)->shmemCtxDetachMgr));
    }
    if (*shmemCtxDetachMgr != NULL) {
        DB_FREE(*shmemCtxDetachMgr);
        *shmemCtxDetachMgr = NULL;
    }
}

void DbShmemCtxDetachMgrInsert(ShmemPtrT ctxPtr, DbInstanceHdT dbInstance)
{
    Status ret = GMERR_OK;
    ShmemCtxDetachMgrT *shmemCtxDetachMgr = g_gmdbShmemCtxDetachMgr;
    if (DbIsMultiInstanceEnabled() && dbInstance) {
        shmemCtxDetachMgr = (ShmemCtxDetachMgrT *)((DbInstanceT *)dbInstance)->shmemCtxDetachMgr;
    }
    if (shmemCtxDetachMgr == NULL || shmemCtxDetachMgr->magicNum != DB_MAGIC_CODE) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Inv ShmemCtxDetachMgr(add element).");
        return;
    }
    ret = DbPidSpinLock(&shmemCtxDetachMgr->lock, PID_SHM_LOCK_TIMEOUT_MS);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Add element to ShmemCtxDetachMgr.");
        return;
    }
    // Init the array if it has not been.
    if (!shmemCtxDetachMgr->inited) {
        for (uint32_t i = 0; i < DB_MAX_FIXED_ID * DB_MAX_SINGLE_PROCESS_INSTANCE_NUM; i++) {
            shmemCtxDetachMgr->ctxArray[i] = DB_INVALID_SHMPTR;
        }
        shmemCtxDetachMgr->inited = true;
    }
    // Find a slot to put the ctxPtr
    uint32_t insertIdx = DB_MAX_FIXED_ID * DB_MAX_SINGLE_PROCESS_INSTANCE_NUM;
    for (uint32_t i = 0; i < DB_MAX_FIXED_ID * DB_MAX_SINGLE_PROCESS_INSTANCE_NUM; i++) {
        if (!DbIsShmPtrValid(shmemCtxDetachMgr->ctxArray[i])) {
            insertIdx = i;
            break;
        }
    }
    if (insertIdx < DB_MAX_FIXED_ID * DB_MAX_SINGLE_PROCESS_INSTANCE_NUM) {
        shmemCtxDetachMgr->ctxArray[insertIdx] = ctxPtr;
        shmemCtxDetachMgr->elemCnt++;
    } else {
        // Free slot not found
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Add element ShmemCtxDetachMgr(no slot in array).");
    }
    DbPidSpinUnlock(&shmemCtxDetachMgr->lock);
}

Status DbReserveMemGetRefCnt(DbMemCtxT *ctx, uint32_t *refCnt)
{
    DB_POINTER2(ctx, refCnt);
    *refCnt = 0;
    return GMERR_OK;
}

static Status ShmemCtxGetRefCnt(DbMemCtxT *ctx, uint32_t *refCnt)
{
    Status ret = CheckShmemCtxValid(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get shmCtx refCnt.");
        return ret;
    }
    DB_ASSERT(
        ctx->methodType == ALGO_BLOCK || ctx->methodType == ALGO_PAGE_POOL || ctx->methodType == ALGO_RSMEM_BLOCK);
    ret = DbMemCtxLock(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_LOCK_NOT_AVAILABLE, "Get shmCtx refCnt.");
        return ret;
    }
    if (CheckMemCtxMagicCode(ctx) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Get shmCtx refCnt.");
        DbMemCtxUnLock(ctx);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (ctx->methodType == ALGO_BLOCK) {
        ret = DbAdptBlockPoolGetRefCnt(((DbBlockShmemCtxT *)ctx)->shmemBlockAlgo.blkPoolShmPtr, refCnt);
    } else if (ctx->methodType == ALGO_PAGE_POOL) {
        ret = DbAdptPagePoolGetRefCnt(((DbPagePoolShmemCtxT *)ctx)->shmemPagePoolAlgo.pagePoolShmPtr, refCnt);
    } else if (ctx->methodType == ALGO_RSMEM_BLOCK) {
        ret = DbReserveMemGetRefCnt(ctx, refCnt);
    }
    DbMemCtxUnLock(ctx);
    return ret;
}

static void ShmemCtxDetachMgrScanSrvNoLock(ShmemCtxDetachMgrT *shmemCtxDetachMgr, DbInstanceHdT dbInstance)
{
    if (shmemCtxDetachMgr->elemCnt == 0) {
        return;
    }
    Status ret = GMERR_OK;
    uint32_t scanCnt = 0;
    uint32_t elemCnt = shmemCtxDetachMgr->elemCnt;
    for (uint32_t i = 0; i < DB_MAX_FIXED_ID * DB_MAX_SINGLE_PROCESS_INSTANCE_NUM; i++) {
        if (scanCnt >= elemCnt) {
            break;
        }

        if (!DbIsShmPtrValid(shmemCtxDetachMgr->ctxArray[i])) {
            continue;
        }
        // A valid record. Check whether all its attachments has been released.
        uint32_t refCnt = DB_INVALID_UINT16;
        scanCnt++;
        DbMemCtxT *ctx = (DbMemCtxT *)DbShmPtrToAddr(shmemCtxDetachMgr->ctxArray[i]);
        ret = CheckShmemCtxValid(ctx);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Server scan ShmemCtxDetachMgr.");
            continue;
        }

        // Check owner of ctx. Server process should not delete ctx of others.
        if (DbGetInstanceId(dbInstance) != ctx->instanceId) {
            continue;
        }

        ret = ShmemCtxGetRefCnt(ctx, &refCnt);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Server scan ShmemCtxDetachMgr. Get refCnt: %" PRIu32 " %s.",
                ctx->ctxId, ctx->ctxName);
            continue;
        }
        // RefCnt 1 indicates that only the server is attached to ctx, the ctx is ready to delete.
        if (refCnt <= 1) {
            // Ctx in layer 1 and 2 are persistent. Their lifespan is same as server.
            // Cannot guarantee child is deleted before parent for now.
            if (ShmemCtxDestroy(ctx) != GMERR_OK) {
                continue;
            }
            // Remove addr record in topShmemCtx and recycle ctx ID.
            if (RmCtxCacheFromTopShmCtx(ctx->ctxId, ctx->instanceId, ctx->shmPtrSelf) == GMERR_LOCK_NOT_AVAILABLE) {
                (void)RmCtxCacheFromTopShmCtx(ctx->ctxId, ctx->instanceId, ctx->shmPtrSelf);
            }
            FreeMemCtx(ctx, ctx->shmPtrSelf, ctx->memType, ctx->instanceId);
            shmemCtxDetachMgr->ctxArray[i] = DB_INVALID_SHMPTR;
            shmemCtxDetachMgr->elemCnt--;
        } else if (ctx->attachCount == 0) {
            // attachCount is decreased upon every shmemCtxClose, if attachCount is 0 but adapter refCnt is
            // greater than 1, then record abnormal log.
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
                "Adapt:decrease ctx refCnt, possible resource leak. Id: %" PRIu32 ", name: %s, ptNo: %" PRIu32
                ", refCnt: %" PRIu32 ".",
                ctx->ctxId, ctx->ctxName, ShmemCtxGetPtNo(ctx), refCnt);
        }
    }
}

// Server interface. Scan DetachMgr and remove all that is ready to delete.
void DbShmemCtxDetachMgrScanSrv(DbInstanceHdT dbInstance)
{
    DbShmemCtxGlobalLock();
    ShmemCtxDetachMgrT *shmemCtxDetachMgr = g_gmdbShmemCtxDetachMgr;
    if (DbIsMultiInstanceEnabled() && dbInstance) {
        shmemCtxDetachMgr = (ShmemCtxDetachMgrT *)((DbInstanceT *)dbInstance)->shmemCtxDetachMgr;
    }
    Status ret = GMERR_OK;
    if (shmemCtxDetachMgr == NULL || shmemCtxDetachMgr->magicNum != DB_MAGIC_CODE) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "ServerScan ShmemCtxDetachMgr(inv mgr).");
        DbShmemCtxGlobalUnlock();
        return;
    }
    if (!shmemCtxDetachMgr->inited) {
        DbShmemCtxGlobalUnlock();
        return;
    }
    ret = DbPidSpinLock(&shmemCtxDetachMgr->lock, PID_SHM_LOCK_TIMEOUT_MS);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Server scan ShmemCtxDetachMgr.");
        DbShmemCtxGlobalUnlock();
        return;
    }

    ShmemCtxDetachMgrScanSrvNoLock(shmemCtxDetachMgr, dbInstance);

    DbPidSpinUnlock(&shmemCtxDetachMgr->lock);
    DbShmemCtxGlobalUnlock();
}

Status DbInitShmemGroup(uint64_t groupIdIn, uint64_t maxTotalPhySize, const char *groupName, uint64_t *groupIdOut)
{
    if (DbIsMultiInstanceEnabled()) {
        return GMERR_OK;
    }
    if (groupIdIn >= (uint64_t)MAX_SHM_GROUP_NUM || groupIdIn == (uint64_t)INVALID_SHM_GROUP_ID) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Init shmGroup. GroupId: %" PRIu64 ", max: %" PRIu32 ".", groupIdIn,
            (uint32_t)MAX_SHM_GROUP_NUM);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (strlen(groupName) >= DB_MAX_SHMEM_GROUP_NAME) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Init shmGroup. Id: %" PRIu64 ", name %s, len: %" PRIu32 ".",
            groupIdIn, groupName, (uint32_t)strlen(groupName));
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return DbAdptInitShmemGroup(groupIdIn, maxTotalPhySize, groupName, groupIdOut);
}

// 当前使用有加全局锁，先不用原子操作。
void ShmemCtxSetTimer(uint64_t newTime)
{
    g_gmdbShmemCtxTimer = newTime;
}

uint64_t ShmemCtxGetTimer(void)
{
    return g_gmdbShmemCtxTimer;
}

static bool ShmemCtxNeedCal(const DbMemCtxT *ctx, uint64_t currTreeSize, uint64_t newTreeSize)
{
    if (ctx->maxTotalAllocSize == DB_INVALID_UINT64) {
        return false;
    }
    uint64_t deltaSize = currTreeSize >= newTreeSize ? (currTreeSize - newTreeSize) : (newTreeSize - currTreeSize);
    // 遍历计算得到的ctx树allocSize总和如果和totalAllocSizeOnThisTree相差过大，超过maxTotalAllocSize的一定比例，触发校准。
    if (((double)deltaSize / (double)ctx->maxTotalAllocSize) > SHMEMCTX_CAL_TREESIZE_THRESH) {
        return true;
    }
    return false;
}

static uint64_t ShmemCtxRecheckTreeAllocSizeInner(DbMemCtxT *ctx)
{
    DB_POINTER(ctx);
    // No need to lock the ctx for nextChildPtr, as ctx tree will not be modified in MemCtxGlobalLock.
    uint64_t newTreeAllocSize = 0;
    ShmemPtrT nextChildPtr = ctx->shmCtxPtrs.shmPtrHead;
    while (DbIsShmPtrValid(nextChildPtr)) {
        DbMemCtxT *childCtx = DbShmPtrToAddr(nextChildPtr);
        if (childCtx == NULL) {
            break;
        }
        uint64_t childTreeAllocSize = ShmemCtxRecheckTreeAllocSizeInner(childCtx);
        newTreeAllocSize += childTreeAllocSize;
        nextChildPtr = childCtx->shmCtxPtrs.shmPtrNext;
    }
    /*
     * If parent node's collectAllocSizeOnThisTree is set, so should the child node. If the child is not set,
     * neither does the parent node.
     */
    if (ctx->collectAllocSizeOnThisTree) {
        Status ret = DbMemCtxLock(ctx);
        if (ret == GMERR_OK) {
            newTreeAllocSize += ctx->totalAllocSize;
            uint64_t currTreeSize = DbAtomicGet64(&ctx->totalAllocSizeOnThisTree);
            if (ShmemCtxNeedCal(ctx, currTreeSize, newTreeAllocSize)) {
                DB_LOG_WARN(GMERR_MEMORY_OPERATE_FAILED,
                    "Cal shm tree alloc size. Id %" PRIu32 ", name %s, curSize: %" PRIu64 ", "
                    "new size %" PRIu64 ". Threshold %" PRIu64 ".",
                    ctx->ctxId, ctx->ctxName, currTreeSize, newTreeAllocSize, ctx->maxTotalAllocSize);
                DbAtomicSet64(&ctx->totalAllocSizeOnThisTree, newTreeAllocSize);
            }
            DbMemCtxUnLock(ctx);
        } else {
            DB_LOG_ERROR(ret, "Recheck ctx %s treeAllocSize.", ctx->ctxName);
        }
    }

    return newTreeAllocSize;
}

/*
 * Only called in server.
 * Must be called within DbShmemCtxGlobalLock.
 */
void ShmemCtxRecheckTreeAllocSize(bool checkTime, DbInstanceHdT dbInstance)
{
    if (checkTime) {
        uint64_t currTime = DbRdtsc();
        uint32_t totalSec = (uint32_t)DbToSeconds(currTime - ShmemCtxGetTimer());
        if (totalSec > SHMEM_CTX_RECHECK_TIME) {
            ShmemCtxSetTimer(currTime);
        } else {
            return;
        }
    }
    DbMemCtxT *topShmemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, DbGetInstanceId(dbInstance));
    if (topShmemCtx == NULL) {
        return;
    }
    (void)ShmemCtxRecheckTreeAllocSizeInner(topShmemCtx);
}

/*
 * 仅限DB重新启动时调用。必须在任何共享内存资源初始化前调用。当前只支持共进程场景，不能有其它客户端进程和DB通信。
 * 多实例场景、数通环境禁止使用。
 */
void DbShmemClearAllShmem(DbInstanceHdT dbInstance)
{
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(dbInstance);
    DbCfgValueT cfgValue;
    Status ret = DbCfgGet(cfgHandle, DB_CFG_START_UP_SHM_CLEAR, &cfgValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get startup clear shm config.");
        return;
    }
    if (cfgValue.int32Val != 1) {
        DB_LOG_INFO("Skip shm clear process on start up.");
        return;
    }
    DbAdptDestroyAllSeg();
    for (uint32_t i = DB_TOP_SHMEM_ID; i < DB_INVALID_SHMEM_ID; i++) {
        uint32_t key = DbConstructShmemKeyById(DbGetInstanceId(dbInstance), i);
        DB_SHMEM_DESTORY(key);
    }
}

#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
#define SHM_CTX_MAX_TRY_CNT 5000  // total 2.5s
#define SHM_CTX_SLEEP_TIME 500    // 500us

static Status DbShmemTryAttach(uint32_t shmCtxId, ShmemPtrT *shmPtr)
{
    uint32_t key = ConstructPoolKeyByCtxId(DbGetProcGlobalId(), shmCtxId);
    bool staticCtx = shmCtxId < DB_START_SPECIAL_CTX_ID;
    // 当前仅DB_SE_DEV_SHMEMCTX_ID为pagepool，暂时写死
    if (shmCtxId == DB_SE_DEV_SHMEMCTX_ID) {
        return DbAdptPagePoolTryAttach(key, staticCtx, shmPtr);
    }
    return DbAdptBlockPoolTryAttach(key, staticCtx, shmPtr);
}

static Status DbShmemGetRefCnt(uint32_t shmCtxId, ShmemPtrT shmPtr, uint32_t *refCnt)
{
    // 当前仅DB_SE_DEV_SHMEMCTX_ID为pagepool，暂时写死
    if (shmCtxId == DB_SE_DEV_SHMEMCTX_ID) {
        return DbAdptPagePoolGetRefCnt(shmPtr, refCnt);
    }
    return DbAdptBlockPoolGetRefCnt(shmPtr, refCnt);
}

static Status DbShmemTryDestroy(uint32_t shmCtxId, ShmemPtrT shmPtr)
{
    // 当前仅DB_SE_DEV_SHMEMCTX_ID为pagepool，暂时写死
    if (shmCtxId == DB_SE_DEV_SHMEMCTX_ID) {
        return DbAdptPagePoolDestroy(shmPtr);
    }
    return DbAdptBlockPoolDestroy(shmPtr);
}

static Status DbShmemClearOneShmemCtxById(uint32_t shmCtxId)
{
    ShmemPtrT shmPtr = DB_INVALID_SHMPTR;
    // 1. try to attach shmCtx, GMERR_NO_DATA means that this ctx has been destroyed, return ok
    Status ret = DbShmemTryAttach(shmCtxId, &shmPtr);
    if (ret == GMERR_NO_DATA) {
        return GMERR_OK;
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Try attach shm. Id: %" PRIu32 ", os no: %" PRId32 ".", shmCtxId, errno);
        return ret;
    }

    uint32_t refCnt = 0;
    uint32_t tryCnt = 0;
    // 2. try to delete shmCtx
    while ((ret = DbShmemGetRefCnt(shmCtxId, shmPtr, &refCnt)) == GMERR_OK) {
        // only server attach this shm
        if (refCnt <= 1) {
            if (DbShmemTryDestroy(shmCtxId, shmPtr) == GMERR_OK) {
                return GMERR_OK;
            }
        }
        DbUsleep(SHM_CTX_SLEEP_TIME);
        if (tryCnt++ >= SHM_CTX_MAX_TRY_CNT) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
                "Timeout:destroy shm. key: %" PRIu32 ", refCnt: %" PRIu32 ", os no: %" PRId32 ".", shmCtxId, refCnt,
                errno);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    }
    DB_LOG_ERROR(ret, "Get refCnt of shm. key: %" PRIu32 ", os no: %" PRId32 ".", shmCtxId, errno);
    return ret;
}

Status DbShmemClearAllShmemSafe(void)
{
    // 1. clear all shmem alloc from shmCtx
    for (uint32_t i = DB_TOP_SHMEMCTX_ID; i < DB_MAX_FIXED_ID; ++i) {
        Status ret = DbShmemClearOneShmemCtxById(i);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    // 2. clear all shmem alloc from OS
    for (uint32_t i = DB_TOP_SHMEM_ID; i < DB_INVALID_SHMEM_ID; ++i) {
        // LOG共享内存会在DbLogInitLocalSrv内销毁
        // TOP共享内存可复用
        // dataFileDirPath内存必须复用
        if (i == DB_LOG_SHMEM_ID || i == DB_TOP_SHMEM_ID || i == DB_INNER_PERSIST_PATH_SHMEM_ID ||
            i == DB_INPUT_PERSIST_PATH_SHMEM_ID) {
            continue;
        }
        uint32_t key = DbConstructShmemKeyById(DbGetProcGlobalId(), i);
        DB_SHMEM_DESTORY(key);
    }
    return GMERR_OK;
}
#endif

#ifdef MEM_POISON
void DbShmemCtxClearAsanList(void *param)
{
    DB_UNUSED(param);
    if (!DbCommonIsServer()) {
        return;
    }
    for (uint32_t i = 0; i < DB_START_SPECIAL_CTX_ID; i++) {
        DbMemCtxT *ctx = DbGetShmemCtxById(i, DbGetProcGlobalId());
        if (SECUREC_UNLIKELY(ctx == NULL)) {
            continue;
        }
        if (ctx->methodType == ALGO_BLOCK) {
            DbAdptBlockPoolClearAsanList(((DbBlockShmemCtxT *)ctx)->shmemBlockAlgo.blkPoolShmPtr);
        }
        if (ctx->methodType == ALGO_PAGE_POOL) {
            DbAdptPagePoolClearAsanList(((DbPagePoolShmemCtxT *)ctx)->shmemPagePoolAlgo.pagePoolShmPtr);
        }
    }
}

Status DbShmemAsanTimerInit(DbAsanTimerHandlerT *asanHandler, uint32_t tmInterval)
{
    CallBackStructT callBack = {.callBack = NULL, .parameter = NULL};
    callBack.callBack = DbShmemCtxClearAsanList;
    callBack.parameter = NULL;
    asanHandler->timerHandle = DbTimerCreate(asanHandler->timerName, &callBack, tmInterval, TIMER_MODE_LOOP);
    if (!DbCheckTimerHandle(asanHandler->timerHandle)) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Create asan timer.");
        return GMERR_DATA_EXCEPTION;
    }
    Status ret = DbTimerStart(asanHandler->timerHandle, TIMER_MODE_LOOP);
    if (ret != GMERR_OK) {
        (void)DbTimerDelete(asanHandler->timerHandle);
        DB_LOG_ERROR(ret, "Start asan timer");
        return ret;
    }
    return GMERR_OK;
}

Status DbShmemAsanTimerUninit(DbAsanTimerHandlerT *asanHandler)
{
    if (!DbCheckTimerHandle(asanHandler->timerHandle)) {
        return GMERR_OK;
    }
    Status ret = DbTimerStop(asanHandler->timerHandle);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Stop asan timer.");
        return ret;
    }
    ret = DbTimerDelete(asanHandler->timerHandle);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Delete asan timer.");
        return ret;
    }
    asanHandler->timerHandle.timerHandle = 0;
    return GMERR_OK;
}

#endif
