/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: db_dynmem_algo.c
 * Description: source file for common memory dynamic algorithm
 * Author: jinfanglin
 * Create: 2020-8-7
 */

#include "db_dynmem_algo.h"
#include <securec.h>
#include <execinfo.h>
#include "db_mem_log.h"
#include "db_mem_context_internal.h"
#include "db_mem_context_pool.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_FREE_MEMCTX_NUM 100

static bool g_gmdbIsLiteDynMemMod = false;
/* 支持补丁在线修改值 */
uint32_t g_gmdbOnlineMemtraceFlag = 0;
uint32_t g_gmdbOnlineMemtracePid = DB_INVALID_UINT32;
uint32_t g_gmdbOnlineMemtraceSampleRate = 10;
// 暂不支持通配符
const char *g_gmdbMemtracePrefix = "";

static void LinkDynChunkTraceInfo(
    DbMemCtxT *ctx, DbDynamicAlgoT *pDynMemAlgo, DynMemChunkTraceInfoT *traceInfo, uint64_t size);

typedef struct BigChunkAllocParams {
    bool enableMemTrace;
    uint64_t userDataSize;
    uint32_t chunkHdrSize;
    uint32_t bigChunkHdrSize;
    uint32_t bigChunkTailSize;
    uint64_t blockSize;
} BigChunkAllocParamsT;

typedef struct ChunkAllocParams {
    bool enableMemTrace;
    uint32_t chunkHdrSize;
    uint32_t alignAllocSize;
    uint32_t actualAllocSize;
    uint32_t freeIdx;
} ChunkAllocParamsT;

typedef struct UniChunkAllocParams {
    bool enableMemTrace;
    uint32_t chunkHdrSize;
    uint32_t allocSize;
    uint32_t actualAllocSize;
} UniChunkAllocParamsT;

void DbSetMemTraceSwitch(uint32_t switchVal, uint32_t pid, uint32_t sampleRate)
{
    g_gmdbOnlineMemtraceFlag = switchVal;
    g_gmdbOnlineMemtracePid = pid;
    g_gmdbOnlineMemtraceSampleRate = sampleRate;
}

void SetLiteDynMemMod(bool liteDynMod)
{
    g_gmdbIsLiteDynMemMod = liteDynMod;
}

bool DbIsLiteDynMemMod(void)
{
    return g_gmdbIsLiteDynMemMod;
}

uint64_t DbDynMemCtxGetTotalPhySize(DbMemCtxT *ctx, bool ctxLock)
{
    if (SECUREC_UNLIKELY(CheckDynMemCtxValid(ctx) != GMERR_OK)) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Get phySize.");
        return 0;
    }
    if (ctxLock) {
        Status ret = DbMemCtxLock(ctx);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return 0;
        }
    }
    uint64_t totalPhySize = ((DbDynamicMemCtxT *)ctx)->dynamicAlgo.totalPhySize;
    if (ctxLock) {
        DbMemCtxUnLock(ctx);
    }
    return totalPhySize;
}

static Status SetDynMemUserDefinedSize(DbDynamicAlgoT *pDynamicAlgo, const AlgoParamT *algoParam)
{
    pDynamicAlgo->baseSize = (algoParam == NULL || algoParam->dynParam == NULL || algoParam->dynParam->baseSize == 0) ?
                                 DYNMEM_DEFAULT_BASESIZE :
                                 algoParam->dynParam->baseSize;
    pDynamicAlgo->stepSize = (algoParam == NULL || algoParam->dynParam == NULL || algoParam->dynParam->stepSize == 0) ?
                                 DYNMEM_DEFAULT_STEPSIZE :
                                 algoParam->dynParam->stepSize;
    // 默认采用旧的二次管理模式，个别ctx为满足小型化需求要指定扩展步长，这种情况必须两个入参都指定。
    if (pDynamicAlgo->baseSize != DYNMEM_DEFAULT_BASESIZE || pDynamicAlgo->stepSize != DYNMEM_DEFAULT_STEPSIZE) {
        pDynamicAlgo->isDefaultMod = false;
    }
    pDynamicAlgo->maxSize = (algoParam == NULL || algoParam->dynParam == NULL || algoParam->dynParam->maxSize == 0) ?
                                DB_MAX_UINT64 :
                                algoParam->dynParam->maxSize;
    pDynamicAlgo->alignSize =
        (algoParam == NULL || algoParam->dynParam == NULL || algoParam->dynParam->alignSize == 0) ?
            DB_DEFAULT_ALIGN_SIZE :
            algoParam->dynParam->alignSize;
    if (pDynamicAlgo->alignSize != DB_DEFAULT_ALIGN_SIZE) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Unsupport modify alignSize: %" PRIu8 ".", pDynamicAlgo->alignSize);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

Status DynamicAlgoInit(DbMemCtxT *ctx, const AlgoParamT *algoParam)
{
    DB_POINTER(ctx);
    DbDynamicAlgoT *pDynamicAlgo = &((DbDynamicMemCtxT *)ctx)->dynamicAlgo;
    pDynamicAlgo->isDefaultMod = true;
    pDynamicAlgo->blocks = NULL;
    pDynamicAlgo->bigChunks = NULL;
    pDynamicAlgo->keeper = NULL;
    pDynamicAlgo->initBlockSize = INIT_BLOCK_SIZE;
    pDynamicAlgo->nextBlockSize = NEXT_BLOCK_SIZE;
    pDynamicAlgo->maxBlockSize = MAX_BLOCK_SIZE;
    // DbAllocChunk中chunkOffset约束Block的上限
    // 小于8388607(23位最大值)即可
    DB_ASSERT(MAX_BLOCK_SIZE <= 8 * DB_MEBI - 1);
    pDynamicAlgo->allocChunkLimit = ALLOC_CHUNK_LIMIT;
#ifndef NDEBUG
    pDynamicAlgo->allocSizeRate = 0;
    pDynamicAlgo->allocTimesRate = 0;
    pDynamicAlgo->whenAlloc = 0;
#endif
    pDynamicAlgo->totalPhySize = 0;
    pDynamicAlgo->currPhySizePeak = 0;
    pDynamicAlgo->totalAllocSizeByCtx = 0;
    pDynamicAlgo->blockNum = 0;
    pDynamicAlgo->bigChunkNum = 0;
    pDynamicAlgo->uniChunkNum = 0;
    pDynamicAlgo->totalBlockSize = 0;
    pDynamicAlgo->totalBigChunkSize = 0;
    Status ret = SetDynMemUserDefinedSize(pDynamicAlgo, algoParam);
    if (ret != GMERR_OK) {
        return ret;
    }
    pDynamicAlgo->traceInfoList = NULL;

#if !defined(RTOSV2X) && !defined(HPE)
    pDynamicAlgo->uniChunkDoubleList = NULL;
#endif
    // uniChunkList是union成员，同时初始化。
    for (uint32_t i = 0; i < ALLOCSET_NUM_FREELISTS; ++i) {
        pDynamicAlgo->freelist[i] = NULL;
    }

    for (uint32_t i = 0; i < FREELIST_BITMAP_BYTENUM; ++i) {
        pDynamicAlgo->freelistBitmap[i] = 0u;
    }

    ctx->methodType = ALGO_DYNAMIC;
    return GMERR_OK;
}

// DynamicAlgoInit初始化成功后，后续流程失败，需要调用该函数释放资源。
// 目前没有需要释放的资源，暂时是桩函数。
void DynamicAlgoUnInit(DbMemCtxT *ctx)
{
    (void)ctx;
}

inline static void SetDynMemCtxLiteMode(DbDynamicMemCtxT *dynCtx, DbMemCtxArgsT *args)
{
    if (DbIsLiteDynMemMod()) {
        // 小型化场景，配置项（DB_CFG_LITE_DYN_MEM_MOD = true）允许关闭二次管理。
        // 配置项开启useLite，服务端按需配置ctx是否开启lite，客户端ctx统一开启lite。
#if (defined RTOSV2 || defined RTOSV2X)
        dynCtx->dynamicAlgo.liteModOn = true;
#else
        dynCtx->dynamicAlgo.liteModOn = args->liteModOn;
#endif

#ifdef ASAN
        // asan环境下(不区分server、client)统一关闭二次管理，方便内存检测。
        dynCtx->dynamicAlgo.liteModOn = true;
#endif
    } else {
        // 非小型化场景，使用二次管理。
        dynCtx->dynamicAlgo.liteModOn = false;
    }
}

/* Dynamic memory context can be created without a parent node */
DbMemCtxT *DbCreateDynMemCtx(DbMemCtxT *parent, bool isShared, const char *ctxName, DbMemCtxArgsT *args)
{
    DB_POINTER(ctxName);
    if (SECUREC_UNLIKELY(args == NULL)) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Inv args(create dynctx).");
        return NULL;
    }

    if (!args->isoCtx) {
        DbMemCtxGlobalLock();
    }
    Status ret = CheckDynMemCtxValid(parent);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Inv parent(create dynctx).");
        if (!args->isoCtx) {
            DbMemCtxGlobalUnlock();
        }
        return NULL;
    }

    args->init = DynamicAlgoInit;
    args->uninit = DynamicAlgoUnInit;
    args->memType = DB_DYNAMIC_MEMORY;
    args->ctxSize = (uint32_t)sizeof(DbDynamicMemCtxT);
    DbDynamicMemCtxT *dynCtx = (DbDynamicMemCtxT *)CreateMemCtx(parent, ctxName, isShared, (void *)args);
    if (dynCtx == NULL) {
        goto EXIT;
    }
    dynCtx->dynamicAlgo.needRecycle = args->dynCtxNeedRecycle;
    SetDynMemCtxLiteMode(dynCtx, args);
EXIT:
    if (!args->isoCtx) {
        DbMemCtxGlobalUnlock();
    }
    return (DbMemCtxT *)dynCtx;
}

// 这个函数如果在多线程调用的时候，需要外部调用来保证加锁, 参数合法性有调用方保证
static void AddDynCtxToPoolFreeList(DbMemCtxPoolT *pool, DbMemCtxT *newNode)
{
    DbMemCtxT *tmp = pool->freeList;
    pool->freeList = newNode;
    newNode->nextForPool = tmp;
}

static void AddDynCtxToPoolCtxList(DbMemCtxPoolT *pool, DbMemCtxT *newNode)
{
    DbMemCtxT *tmp = pool->ctxList;
    pool->ctxList = newNode;
    newNode->nextForDelete = tmp;
}

static void RmDynCtxFromPoolFreeList(DbMemCtxPoolT *pool)
{
    DbMemCtxT *tmp = pool->freeList;
    DbMemCtxT *newRoot = tmp->nextForPool;
    pool->freeList = newRoot;
    tmp->nextForPool = NULL;
}

DbMemCtxPoolT *DbCreateDynMemCtxPool(bool isShared, DbMemCtxT *parentCtx)
{
    DbMemCtxPoolT *ctxPool = (DbMemCtxPoolT *)DbDynMemCtxAlloc(parentCtx, sizeof(DbMemCtxPoolT));
    if (ctxPool == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Create ctxpool.");
        return NULL;
    }
    ctxPool->isShared = isShared;
    ctxPool->parentCtx = parentCtx;
    ctxPool->freeList = NULL;
    ctxPool->ctxList = NULL;
    ctxPool->ctxNum = 0;
    ctxPool->freeCtxNum = 0;
    ctxPool->isWithMaxSizeForEachCtx = false;
    ctxPool->maxSizeForEachCtx = DB_MAX_UINT64;
    ctxPool->magicCode = DB_MAGIC_CODE;
    DbSpinInit(&ctxPool->lock);
    return ctxPool;
}

DbMemCtxT *DbGetDynMemCtxFromPool(DbMemCtxPoolT *ctxPool)
{
#ifndef NDEBUG
    DB_POINTER2(ctxPool, ctxPool->parentCtx);
    if (ctxPool->magicCode != DB_MAGIC_CODE) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Check magic code(acquire dynCtx).");
        return NULL;
    }
#endif
    DbSpinLock(&ctxPool->lock);
    if (ctxPool->freeList != NULL) {
        DbDynamicMemCtxT *ctx = (DbDynamicMemCtxT *)ctxPool->freeList;
        if (ctx != NULL) {
            RmDynCtxFromPoolFreeList(ctxPool);
            ctxPool->freeCtxNum--;
        }
        DbSpinUnlock(&ctxPool->lock);
        return (DbMemCtxT *)ctx;
    } else {
        // 如果freelist为空，则创建一个新的memctx
        char ctxName[DB_MAX_MEMCTX_NAME];
        const char *format = "%s auto gen ctx";
        char *parentName = ctxPool->parentCtx->ctxName;
        if (snprintf_s(ctxName, DB_MAX_MEMCTX_NAME, DB_MAX_MEMCTX_NAME - 1, format, parentName) < 0) {
            DbSpinUnlock(&ctxPool->lock);
            return NULL;
        }
        DbMemCtxArgsT args = {0};
        args.liteModOn = true;
        args.maxTotalPhySize = ctxPool->isWithMaxSizeForEachCtx ? ctxPool->maxSizeForEachCtx : 0;
        // 如果池子中没有空闲的ctx则申请一个,在配对的释放函数DbReturnDynMemCtxToPool中，会调用DbMemCtxReset
        DbDynamicMemCtxT *ctx =
            (DbDynamicMemCtxT *)DbCreateDynMemCtx(ctxPool->parentCtx, ctxPool->isShared, ctxName, &args);
        if (ctx != NULL) {
            ctx->header.isPoolMember = true;
            ctxPool->ctxNum++;
            AddDynCtxToPoolCtxList(ctxPool, (DbMemCtxT *)ctx);
        }
        DbSpinUnlock(&ctxPool->lock);
        return (DbMemCtxT *)ctx;
    }
}

static void RmDynCtxFromPoolCtxList(DbMemCtxPoolT *ctxPool, DbMemCtxT *memCtx)
{
    DbMemCtxT *prev = NULL;
    DbMemCtxT *curr = ctxPool->ctxList;
    while (curr != NULL) {
        if (curr == memCtx) {
            if (ctxPool->ctxList == memCtx) {
                ctxPool->ctxList = curr->nextForDelete;
            } else {
                // currCtx不是表头，prev必然不为NULL
                DB_ASSERT(prev != NULL);
                prev->nextForDelete = curr->nextForDelete;
            }
            curr->nextForDelete = NULL;
            break;
        }
        prev = curr;
        curr = curr->nextForDelete;
    }
}

Status DbReturnDynMemCtxToPool(DbMemCtxPoolT *ctxPool, DbMemCtxT *memCtx)
{
#ifndef NDEBUG
    DB_POINTER3(ctxPool, ctxPool->parentCtx, memCtx);
    if (ctxPool->magicCode != DB_MAGIC_CODE) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Check magic code(free dynctx).");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
#endif
    // 将此memctx放入freelist的头部
    DbSpinLock(&ctxPool->lock);
    if ((memCtx->dynCtxPtrs.parent != ctxPool->parentCtx) && (memCtx->isPoolMember)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "ctx not belong to ctxpool.");
        DbSpinUnlock(&ctxPool->lock);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (ctxPool->freeCtxNum > MAX_FREE_MEMCTX_NUM) {
        RmDynCtxFromPoolCtxList(ctxPool, memCtx);
        ctxPool->ctxNum--;
        DbDeleteDynMemCtx(memCtx);
    } else {
        AddDynCtxToPoolFreeList(ctxPool, memCtx);
        ctxPool->freeCtxNum++;
        DbMemCtxReset(memCtx);
    }
    DbSpinUnlock(&ctxPool->lock);
    return GMERR_OK;
}

MEM_ERR_FUNC_INLINE void DynamicAlgoFreeChkHdrErr(const DbMemCtxT *ctx, const DbAllocChunkT *pChunk)
{
    if (pChunk->magicNum == MEM_MAGIC_FREE_NUMBER_8) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "Mem double free, magic: %" PRIu32 ", ctxId: %" PRIu32 ", ctxName: %s", pChunk->magicNum, ctx->ctxId,
            ctx->ctxName);
    } else {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "Mem access out of bounds, magic: %" PRIu32 ", ctxId: %" PRIu32 ", ctxName: %s", pChunk->magicNum,
            ctx->ctxId, ctx->ctxName);
    }
    DB_ASSERT(false);
}

inline static bool DbDynamicAlgoChkValid(
    const DbMemCtxT *ctx, const DbDynamicAlgoT *pDynMemAlgo, const DbAllocChunkT *pChunk)
{
    if (SECUREC_UNLIKELY(pChunk->magicNum != MEM_MAGIC_NUMBER_8)) {
        DynamicAlgoFreeChkHdrErr(ctx, pChunk);
        return false;
    }
    /* Check the chunk header and we must alloc and free in the same memctx */
    if (SECUREC_UNLIKELY(pChunk->ownerOrNextChunk != pDynMemAlgo)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Alloc and free should in same ctx. Id: %" PRIu32 ", name: %s.",
            ctx->ctxId, ctx->ctxName);
        DB_ASSERT(false);
        return false;
    }
    return true;
}

static void *AlignDynMemBlockAddr(void *addr, uint32_t alignSize)
{
    DB_POINTER(addr);
    // DB_MALLOC出的addr一定是8的倍数，所以不用再做对齐
    if (alignSize == DB_DEFAULT_ALIGN_SIZE) {
        return addr;
    }
    DB_UINTPTR address = (DB_UINTPTR)addr;
    if (alignSize < DB_MIN_ALIGN_SIZE) {  // 对齐的字节数只能是>=4字节的且是2的整数幂
        return NULL;
    } else if (((alignSize & (alignSize - 1)) == 0) && alignSize <= DB_MAX_ALIGN_SIZE) {
        uint32_t offset = (uint32_t)(address % alignSize);
        // return aligned addr
        return (offset == 0) ? addr : (void *)((uintptr_t)addr + alignSize - offset);
    }
    return NULL;
}

inline static __attribute__((always_inline)) void LinkBigChunkToBigChunkList(
    DbDynamicAlgoT *pDynamicAlgo, DbAllocBigChunkT *alignedBigChunk)
{
    alignedBigChunk->prev = NULL;
    alignedBigChunk->next = pDynamicAlgo->bigChunks;
    if (pDynamicAlgo->bigChunks != NULL) {
        pDynamicAlgo->bigChunks->prev = alignedBigChunk;
    }
    pDynamicAlgo->bigChunks = alignedBigChunk;
}

static void SetChunkHdr(DbAllocChunkT *pChunk, DbDynamicMemCtxT *ctx, ChunkAllocParamsT *params)
{
    DbDynamicAlgoT *pDynamicAlgo = &ctx->dynamicAlgo;
    pChunk->ownerOrNextChunk = pDynamicAlgo;
    pChunk->size = params->alignAllocSize;
    pChunk->chunkOffset = (uint32_t)((uintptr_t)pChunk - (uintptr_t)pDynamicAlgo->blocks);
    pChunk->magicNum = MEM_MAGIC_NUMBER_8;
    if (SECUREC_UNLIKELY(IsEscapeCtx((DbMemCtxT *)ctx))) {
        pChunk->escapeMark = true;
    } else {
        pChunk->escapeMark = false;
    }
    pChunk->memtraceChunk = params->enableMemTrace;
}

static void SetChunkHdr4BigChunkAlloc(DbAllocChunkT *pBigChunk, DbDynamicMemCtxT *ctx, BigChunkAllocParamsT *params)
{
    DbDynamicAlgoT *pDynamicAlgo = &ctx->dynamicAlgo;
    pBigChunk->ownerOrNextChunk = pDynamicAlgo;
    pBigChunk->size = params->userDataSize;
    // Member chunkOffset is not used in big chunk scenario.
    pBigChunk->chunkOffset = 0u;
    pBigChunk->magicNum = MEM_MAGIC_NUMBER_8;
    if (SECUREC_UNLIKELY(IsEscapeCtx((DbMemCtxT *)ctx))) {
        pBigChunk->escapeMark = true;
    } else {
        pBigChunk->escapeMark = false;
    }
    pBigChunk->memtraceChunk = params->enableMemTrace;
}

inline static void SetBigChunkHdr(DbAllocBigChunkT *pBigChunk, void *pBlock, BigChunkAllocParamsT *params)
{
    pBigChunk->magicNum = MEM_MAGIC_NUMBER;
    pBigChunk->chunkAddrOffset = (uint32_t)((uintptr_t)(pBigChunk) - (uintptr_t)(pBlock));
    pBigChunk->bigChunkSize = params->blockSize;
}

inline static void SetBigChunkTail(DbAllocBigChunkT *pBigChunk, BigChunkAllocParamsT *params)
{
    DbAllocBigChunkTailT *pBigChkTail =
        (DbAllocBigChunkTailT *)((void *)pBigChunk + params->blockSize - params->bigChunkTailSize);
    pBigChkTail->magicNum = MEM_MAGIC_NUMBER;
}

static void DynCtxRecordPeakInfoAlarm(DbMemCtxT *ctx)
{
    if (SECUREC_LIKELY(g_gmdbPeakSizeDfxThresh == DB_INVALID_UINT64)) {
        return;
    }
    uint64_t newPeakAllocSize = DB_MAX(ctx->peakAllocSize, ctx->totalAllocSize);
    // Only print log at the moment when memCtx peak allocSize exceeds the threshold.
    if (newPeakAllocSize >= g_gmdbPeakSizeDfxThresh && ctx->peakAllocSize < g_gmdbPeakSizeDfxThresh) {
        DB_LOG_WARN(GMERR_OUT_OF_MEMORY,
            "DynCtx reaches mem peak. Name: %s, id: %" PRIu32 ", totalAllocSize: %" PRIu64 ", totalPhySize: %" PRIu64
            ". Current threshold: %" PRIu64 ".",
            ctx->ctxName, ctx->ctxId, ctx->totalAllocSize, DbDynMemCtxGetTotalPhySize(ctx, false),
            g_gmdbPeakSizeDfxThresh);
    }
    // 根据阈值打印memtrace私有日志暂时用宏隔离，release不启用。
#ifdef DB_MEM_TRACE
    if (!ctx->enableMemTrace || ctx->memtraceThreshold == DB_INVALID_UINT64) {
        return;
    }
    // Only pring all callstack log at the moment when memCtx peak allocSize exceeds the threshold.
    if (newPeakAllocSize >= ctx->memtraceThreshold && ctx->peakAllocSize < ctx->memtraceThreshold) {
        DbMemCtxPrintAllCallStack(ctx, false);
    }
#endif
}

static void InitBigChunkAllocParam(DbMemCtxT *ctx, uint64_t size, bool enableMemTrace, BigChunkAllocParamsT *params)
{
    params->enableMemTrace = enableMemTrace;
    params->userDataSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, size);
    params->chunkHdrSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbAllocChunkT));
    params->bigChunkHdrSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbAllocBigChunkT));
    params->bigChunkTailSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbAllocBigChunkTailT));
    // 如果后续允许用户指定alignsize，那么这里需要加上alignsize来做对齐。
    params->blockSize =
        params->bigChunkHdrSize + params->chunkHdrSize + params->userDataSize + params->bigChunkTailSize;
    if (SECUREC_UNLIKELY(params->enableMemTrace)) {
        params->blockSize += MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DynMemChunkTraceInfoT));
    }
}

static void BigChunkAllocUpdCtxStats(DbMemCtxT *ctx, DbAllocChunkT *pChunk, BigChunkAllocParamsT *params)
{
    DbDynamicAlgoT *pDynAlgo = &((DbDynamicMemCtxT *)ctx)->dynamicAlgo;
    ctx->isReset = false;
    ctx->totalAllocSize += pChunk->size;
    pDynAlgo->totalPhySize += params->blockSize;
    DynCtxRecordPeakInfoAlarm(ctx);
    ctx->peakAllocSize = DB_MAX(ctx->peakAllocSize, ctx->totalAllocSize);
    pDynAlgo->currPhySizePeak = DB_MAX(pDynAlgo->currPhySizePeak, pDynAlgo->totalPhySize);
    pDynAlgo->totalAllocSizeByCtx += params->blockSize;
    pDynAlgo->bigChunkNum++;
    pDynAlgo->totalBigChunkSize += params->blockSize;
}

static void *AllocBigChunk(DbMemCtxT *ctx, uint64_t size, bool enableMemTrace)
{
    DbDynamicAlgoT *pDynAlgo = &((DbDynamicMemCtxT *)ctx)->dynamicAlgo;
    BigChunkAllocParamsT params = {0};
    InitBigChunkAllocParam(ctx, size, enableMemTrace, &params);
    // 1、判断用户传入参数是否合法。若对齐后的大小比size还小，说明size过大导致对齐后反转了
    // 2、ctx向OS申请的内存不能超过maxSize
    if (SECUREC_UNLIKELY(params.blockSize < size || pDynAlgo->totalPhySize + params.blockSize > pDynAlgo->maxSize)) {
        DB_LOG_ERROR(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD,
            "Alloc bigChunk, size: %" PRIu64 ", blockSize: %" PRIu64 ", maxSize: %" PRIu64 ".", size, params.blockSize,
            pDynAlgo->maxSize);
        return NULL;
    }
    void *bigChunk = DB_MALLOC(params.blockSize);
    if (SECUREC_UNLIKELY(bigChunk == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
            "Alloc bigChunk. Global dynSize: %" PRIu64 ", totalPhySize: %" PRIu64 ", block size: %" PRIu64
            ", alignSize: %" PRIu32 ".",
            DbGetCurrDynSize(), pDynAlgo->totalPhySize, params.blockSize, pDynAlgo->alignSize);
        return NULL;
    }
    DbAllocBigChunkT *alignedBigChunk = (DbAllocBigChunkT *)AlignDynMemBlockAddr(bigChunk, pDynAlgo->alignSize);
    // ctx向OS申请的内存不能超过阈值树约束
    if (alignedBigChunk == NULL || UpdateDynCtxThresholdParent(ctx, params.blockSize, true) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Alloc bigChunk. %s",
            (alignedBigChunk == NULL) ? "Align bigChunk." : "Update parent ctx threshold.");
        DB_FREE(bigChunk);
        return NULL;
    }
    DbAllocChunkT *chunkHdr = GET_ALLOCCHUNK_HEADER(alignedBigChunk, params.bigChunkHdrSize);
    SetChunkHdr4BigChunkAlloc(chunkHdr, (DbDynamicMemCtxT *)ctx, &params);
    SetBigChunkTail(alignedBigChunk, &params);
    SetBigChunkHdr(alignedBigChunk, bigChunk, &params);
    BigChunkAllocUpdCtxStats(ctx, chunkHdr, &params);
    /* record big chunk memtrace info */
    if (params.enableMemTrace) {
        DynMemChunkTraceInfoT *traceInfo =
            (DynMemChunkTraceInfoT *)((void *)chunkHdr + params.chunkHdrSize + params.userDataSize);
        LinkDynChunkTraceInfo(ctx, pDynAlgo, traceInfo, params.userDataSize);
    }
    LinkBigChunkToBigChunkList(pDynAlgo, alignedBigChunk);

    return (void *)ALLOCBIGCHUNK_DATA(alignedBigChunk, params.chunkHdrSize, params.bigChunkHdrSize);
}

static void AddRestOfBlockToFreelist(DbMemCtxT *ctx, DbAllocBlockT *pBlock, uint32_t freeSpace)
{
    /* The existing active (top) block does not have enough room for
     * the requested allocation, but it might still have a useful
     * amount of space in it.  Once we push it down in the block list,
     * we'll never try to allocate more space from it. So, before we
     * do that, carve up its free space into chunks that we can put on
     * the set's freelists.
     *
     * Because we can only get here when there's less than
     * ALLOC_CHUNK_LIMIT left in the block, this loop cannot iterate
     * more than ALLOCSET_NUM_FREELISTS-1 times.
     */
    DbDynamicAlgoT *pDynamicAlgo = &((DbDynamicMemCtxT *)ctx)->dynamicAlgo;
    uint32_t blkFreeSpace = freeSpace;
    uint32_t alignedChunkHdrSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbAllocChunkT));
    uint32_t minChunkSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, 1 << ALLOC_MINBITS) + alignedChunkHdrSize;
    while (blkFreeSpace >= minChunkSize) {
        uint32_t availChunkDataAreaSize = blkFreeSpace - alignedChunkHdrSize;
        uint32_t localFreeIdx = GetFreeIdx(availChunkDataAreaSize);
        /* In most cases, we'll get back the index of the next larger
         * freelist than the one we need to put this chunk on. The
         * exception is when availchunk is exactly a power of 2.
         */
        if (availChunkDataAreaSize != GetFreeSize(localFreeIdx)) {
            localFreeIdx--;
            if (localFreeIdx < ALLOCSET_NUM_FREELISTS) {
                availChunkDataAreaSize = GetFreeSize(localFreeIdx);
            } else {
                break;
            }
        }
        DbAllocChunkT *pChunk = (DbAllocChunkT *)(pBlock->freeptr);
        pBlock->freeptr += (availChunkDataAreaSize + alignedChunkHdrSize);
        blkFreeSpace -= (availChunkDataAreaSize + alignedChunkHdrSize);
        pChunk->size = availChunkDataAreaSize;
        pChunk->chunkOffset = (uint32_t)((uintptr_t)pChunk - (uintptr_t)pBlock);
        pChunk->magicNum = MEM_MAGIC_FREE_NUMBER_8;
        pChunk->memtraceChunk = false;
        if (SECUREC_UNLIKELY(IsEscapeCtx(ctx))) {
            pChunk->escapeMark = true;
        } else {
            pChunk->escapeMark = false;
        }
#ifndef NDEBUG
        pBlock->chunkNum++;
#endif
        pChunk->ownerOrNextChunk = (void *)pDynamicAlgo->freelist[localFreeIdx];
        pDynamicAlgo->freelist[localFreeIdx] = pChunk;
        MarkBitMapByIdx(pDynamicAlgo->freelistBitmap, localFreeIdx);
    }
}

static void SetBlock(DbDynamicAlgoT *pDynamicAlgo, DbAllocBlockT *pBlock, DbAllocBlockT *ppBlock, uint32_t blockSize)
{
    uint32_t allocBlockHdrSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbAllocBlockT));
    pBlock->magicNum = MEM_MAGIC_NUMBER;
    pBlock->allocSet = pDynamicAlgo;
    pBlock->freeptr = ((char *)pBlock) + allocBlockHdrSize;
    pBlock->endptr = ((char *)pBlock) + blockSize;
    pBlock->next = pDynamicAlgo->blocks;
    pBlock->prev = NULL;
    pBlock->allocCount = 0;
    pBlock->blockSize = blockSize;
#ifndef NDEBUG
    pBlock->chunkNum = 0;
#endif
    pBlock->blockAddrOffset = (uint16_t)((uintptr_t)(pBlock) - (uintptr_t)(ppBlock));
}

static void UpdateDynAlgoInfoForNewBlockDefault(
    DbDynamicAlgoT *pDynamicAlgo, DbAllocBlockT *pBlock, uint32_t blockSize, uint32_t allocBlockHdrSize)
{
    if (pDynamicAlgo->blocks != NULL) {
        pDynamicAlgo->blocks->prev = pBlock;  // 将新申请的block插入到blocks链表
    }
    if (pDynamicAlgo->keeper == NULL && blockSize == pDynamicAlgo->initBlockSize) {
        pDynamicAlgo->keeper = pBlock;  // 记录第一块大小为initBlockSize的内存
    }
    // 更新动态内存算法管理结构体的统计信息
    pDynamicAlgo->blocks = pBlock;
    pDynamicAlgo->totalPhySize += pBlock->blockSize;
    pDynamicAlgo->currPhySizePeak = DB_MAX(pDynamicAlgo->currPhySizePeak, pDynamicAlgo->totalPhySize);
    pDynamicAlgo->totalAllocSizeByCtx += allocBlockHdrSize;  // 申请新的block的时候需要把block header大小算上。
    pDynamicAlgo->blockNum++;
    pDynamicAlgo->totalBlockSize += pBlock->blockSize;
    pDynamicAlgo->nextBlockSize <<= 1;
}

static void *AllocNewBlockDefault(DbMemCtxT *header, DbDynamicAlgoT *pDynamicAlgo, uint32_t chunkDataAreaSize)
{
    // for align
    uint32_t allocBlockHdrSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbAllocBlockT));
    uint32_t allocChunkHdrSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbAllocChunkT));
    /* The first such block has size initBlockSize, and we double the
     * space in each succeeding block, but not more than maxBlockSize.
     */
    if (pDynamicAlgo->nextBlockSize > pDynamicAlgo->maxBlockSize) {
        pDynamicAlgo->nextBlockSize = pDynamicAlgo->maxBlockSize;
    }
    uint32_t blockSize = pDynamicAlgo->nextBlockSize;
    /* If initBlockSize is less than ALLOC_CHUNK_LIMIT, we could need more
     * space... but try to keep it a power of 2.
     */
    // 如果后续允许用户指定alignsize，那么这里需要加上alignsize来做对齐。
    uint32_t requiredSize = allocBlockHdrSize + allocChunkHdrSize + chunkDataAreaSize;
    while (blockSize < requiredSize) {
        blockSize <<= 1;
    }
    // ctx向OS申请的内存不能超过maxSize
    if (pDynamicAlgo->totalPhySize + blockSize > pDynamicAlgo->maxSize) {
        DB_LOG_ERROR(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD,
            "Alloc block, ctx: %s, totalPhySize: %" PRIu64 ", blockSize: %" PRIu32 ", maxSize: %" PRIu64 ".",
            header->ctxName, pDynamicAlgo->totalPhySize, blockSize, pDynamicAlgo->maxSize);
        return NULL;
    }
    DbAllocBlockT *ppBlock = (DbAllocBlockT *)DB_MALLOC(blockSize);
    /*
     * We could be asking for pretty big blocks here, so cope if malloc
     * fails.  But give up if there's less than a meg or so available...
     */
    while (ppBlock == NULL && blockSize > MAX_BLOCK_SIZE) {  // 64 * 1024 kBytes
        blockSize >>= 1;
        if (blockSize < requiredSize) {
            break;
        }
        ppBlock = (DbAllocBlockT *)DB_MALLOC(blockSize);
    }
    if (ppBlock == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
            "Alloc new block. Curr dynSize: %" PRIu64 ", totalPhySize: %" PRIu64 ", blockSize: %" PRIu32 ".",
            DbGetCurrDynSize(), pDynamicAlgo->totalPhySize, blockSize);
        return NULL;
    }
    // pBlock是按照alignSize对齐后的addr
    DbAllocBlockT *pBlock = (DbAllocBlockT *)AlignDynMemBlockAddr((void *)ppBlock, DB_DEFAULT_ALIGN_SIZE);
    if (pBlock == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Align new block.");
        DB_FREE(ppBlock);
        return NULL;
    }
    Status ret = UpdateDynCtxThresholdParent(header, blockSize, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Alloc new block and update parent ctx %s.", header->ctxName);
        DB_FREE(ppBlock);
        return NULL;
    }
    SetBlock(pDynamicAlgo, pBlock, ppBlock, blockSize);
    UpdateDynAlgoInfoForNewBlockDefault(pDynamicAlgo, pBlock, blockSize, allocBlockHdrSize);
    header->isReset = false;
    return pBlock;
}

static uint32_t GetUserDefNewBlockSize(DbDynamicAlgoT *pDynamicAlgo)
{
    if (pDynamicAlgo->blocks == NULL) {
        return pDynamicAlgo->baseSize;
    } else {
        return pDynamicAlgo->stepSize;
    }
}

static void UpdateDynAlgoInfoForUserDefNewBlock(
    DbDynamicAlgoT *pDynamicAlgo, DbAllocBlockT *pBlock, uint32_t blockSize, uint32_t allocBlockHdrSize)
{
    if (pDynamicAlgo->blocks != NULL) {
        pDynamicAlgo->blocks->prev = pBlock;  // 将新申请的block插入到blocks链表
    }
    if (pDynamicAlgo->keeper == NULL && blockSize == pDynamicAlgo->baseSize) {
        pDynamicAlgo->keeper = pBlock;  // 记录第一块大小为baseSize的内存
    }
    // 更新动态内存算法管理结构体的统计信息
    pDynamicAlgo->blocks = pBlock;
    pDynamicAlgo->totalPhySize += pBlock->blockSize;
    pDynamicAlgo->currPhySizePeak = DB_MAX(pDynamicAlgo->currPhySizePeak, pDynamicAlgo->totalPhySize);
    pDynamicAlgo->totalAllocSizeByCtx += allocBlockHdrSize;  // 申请新的block的时候需要把block header大小算上。
    pDynamicAlgo->blockNum++;
    pDynamicAlgo->totalBlockSize += pBlock->blockSize;
}

static void *AllocUserDefNewBlock(DbMemCtxT *header, DbDynamicAlgoT *pDynamicAlgo, uint32_t actualAllocSize)
{
    uint32_t blockSize = GetUserDefNewBlockSize(pDynamicAlgo);
    uint32_t allocBlockHdrSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbAllocBlockT));
    uint32_t allocChunkHdrSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbAllocChunkT));
    uint32_t requiredSize = allocBlockHdrSize + allocChunkHdrSize + actualAllocSize;
    if (blockSize <= requiredSize) {
        return NULL;
    }

    if (pDynamicAlgo->totalPhySize + blockSize > pDynamicAlgo->maxSize) {
        DB_LOG_ERROR(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD,
            "Exceeded ctx %s maxSize. Curr totalPhySize: %" PRIu64 ", new blockSize: %" PRIu32 ", maxSize: %" PRIu64
            ".",
            header->ctxName, pDynamicAlgo->totalPhySize, blockSize, pDynamicAlgo->maxSize);
        return NULL;
    }
    void *ppBlock = (void *)DB_MALLOC(blockSize);
    if (ppBlock == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
            "Alloc new block. Curr dynSize: %" PRIu64 ", totalPhySize: %" PRIu64 ", blockSize: %" PRIu32 ".",
            DbGetCurrDynSize(), pDynamicAlgo->totalPhySize, blockSize);
        return NULL;
    }
    Status ret = UpdateDynCtxThresholdParent(header, blockSize, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "Alloc block and update parent for ctx %s, blockSize: %" PRIu32 ".", header->ctxName, blockSize);
        DB_FREE(ppBlock);
        return NULL;
    }
    // block头算在baseSize/stepSize里
    DbAllocBlockT *pBlock = (DbAllocBlockT *)AlignDynMemBlockAddr((void *)ppBlock, DB_DEFAULT_ALIGN_SIZE);
    if (pBlock == NULL) {
        DB_FREE(ppBlock);
        return NULL;
    }
    SetBlock(pDynamicAlgo, pBlock, ppBlock, blockSize);
    UpdateDynAlgoInfoForUserDefNewBlock(pDynamicAlgo, pBlock, blockSize, allocBlockHdrSize);
    header->isReset = false;
    return pBlock;
}

static void *AllocNewBlock(DbMemCtxT *header, DbDynamicAlgoT *pDynamicAlgo, uint32_t actualAllocSize)
{
    if (pDynamicAlgo->isDefaultMod) {
        // DynMem block size mulitiplies by 2 at each expansion, 64KB maximum.
        return AllocNewBlockDefault(header, pDynamicAlgo, actualAllocSize);
    } else {
        // If user defines baseSize or stepSize, dynMem block expands according to user's definition.
        return AllocUserDefNewBlock(header, pDynamicAlgo, actualAllocSize);
    }
}

static DbAllocChunkT *AllocAndInitChunk(DbDynamicMemCtxT *ctx, ChunkAllocParamsT *params)
{
    DbMemCtxT *header = &ctx->header;
    DbDynamicAlgoT *pDynamicAlgo = &ctx->dynamicAlgo;
    DbAllocChunkT *pChunk = (DbAllocChunkT *)(pDynamicAlgo->blocks->freeptr);
    SetChunkHdr(pChunk, ctx, params);
    pDynamicAlgo->blocks->freeptr += (params->chunkHdrSize + params->actualAllocSize);
    pDynamicAlgo->blocks->allocCount++;
    pDynamicAlgo->totalAllocSizeByCtx +=
        params->chunkHdrSize + params->actualAllocSize;  // 扩展新block的时候，blockheader已经加过了。
#ifndef NDEBUG
    pDynamicAlgo->blocks->chunkNum++;
#endif
    header->totalAllocSize += params->alignAllocSize;
    DynCtxRecordPeakInfoAlarm(header);
    header->peakAllocSize = DB_MAX(header->peakAllocSize, header->totalAllocSize);
    header->isReset = false;
    return pChunk;
}

#ifndef NDEBUG
static void DynMemAllocAbnormalAlarm(const DbMemCtxT *pHeader, DbDynamicAlgoT *pDynMemAlgo, uint32_t size)
{
    uint64_t currTime = DbGettimeMonotonicUsec();
    if (currTime - pDynMemAlgo->whenAlloc > USECONDS_IN_SECOND) {
        pDynMemAlgo->whenAlloc = currTime;
        pDynMemAlgo->allocSizeRate = size;
        pDynMemAlgo->allocTimesRate = 1;
    } else {
        /* if we alloc too much memory or too much times in a second, we will print warning to the log */
        pDynMemAlgo->allocSizeRate += size;
        pDynMemAlgo->allocTimesRate += 1;
        if (pDynMemAlgo->allocSizeRate > MEMCTX_ABNORMAL_ALLOC_SIZE_RATE) {
            DB_LOG_WARN(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD, "Ctx name: %s, alloc size rate: %" PRIu64 ".",
                pHeader->ctxName, pDynMemAlgo->allocSizeRate);
        }
        if (pDynMemAlgo->allocTimesRate > MEMCTX_ABNORMAL_ALLOC_TIMES_RATE) {
            DB_LOG_WARN(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD, "Ctx name: %s, alloc times rate: %" PRIu32 ".",
                pHeader->ctxName, pDynMemAlgo->allocTimesRate);
        }
    }
}
#endif

static DbAllocChunkT *GetChunkFromFreeList(
    DbDynamicMemCtxT *pDynMemCtx, DbDynamicAlgoT *pDynMemAlgo, ChunkAllocParamsT *params)
{
    DbAllocChunkT *pChunk = pDynMemAlgo->freelist[params->freeIdx];
    if (pChunk != NULL) {
        pDynMemAlgo->freelist[params->freeIdx] = (DbAllocChunkT *)pChunk->ownerOrNextChunk;  // next available chunk
        if (pDynMemAlgo->freelist[params->freeIdx] == NULL) {
            EraseBitMapByIdx(pDynMemAlgo->freelistBitmap, params->freeIdx);
        }
        pDynMemAlgo->totalAllocSizeByCtx += (params->chunkHdrSize + params->actualAllocSize);
        pChunk->ownerOrNextChunk = (void *)pDynMemAlgo;
        DbAllocBlockT *ownerBlock = OWNER_BLOCK_OF_CHUNK(pChunk);
        ownerBlock->allocCount++;
        pDynMemCtx->header.totalAllocSize += params->alignAllocSize;
        DynCtxRecordPeakInfoAlarm(&pDynMemCtx->header);
        pDynMemCtx->header.peakAllocSize = DB_MAX(pDynMemCtx->header.peakAllocSize, pDynMemCtx->header.totalAllocSize);
        if (SECUREC_UNLIKELY(IsEscapeCtx((DbMemCtxT *)pDynMemCtx))) {
            pChunk->escapeMark = true;
        } else {
            pChunk->escapeMark = false;
        }
        pChunk->magicNum = MEM_MAGIC_NUMBER_8;

        if (SECUREC_UNLIKELY(params->enableMemTrace)) {
            // 复用的chunk要重设size，上面计算actualAllocSize时已经加上了memtrace信息的大小。
            pChunk->memtraceChunk = true;
            pChunk->size -= MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DynMemChunkTraceInfoT));
            DynMemChunkTraceInfoT *traceInfo =
                (DynMemChunkTraceInfoT *)((uintptr_t)pChunk + params->chunkHdrSize + params->alignAllocSize);
            LinkDynChunkTraceInfo(&pDynMemCtx->header, pDynMemAlgo, traceInfo, params->alignAllocSize);
        }
    }
    return pChunk;
}

static void LinkDynChunkTraceInfo(
    DbMemCtxT *ctx, DbDynamicAlgoT *pDynMemAlgo, DynMemChunkTraceInfoT *traceInfo, uint64_t size)
{
    traceInfo->stackSize = (uint8_t)backtrace(traceInfo->callStack, MEM_ALLOC_CALL_STACK_MAX_SIZE);
    traceInfo->time = time(NULL);
    traceInfo->traceReserve = 0;
    traceInfo->size = size;
    if (pDynMemAlgo->traceInfoList == NULL) {
        pDynMemAlgo->traceInfoList = traceInfo;
        traceInfo->prev = NULL;
        traceInfo->next = NULL;
    } else {
        traceInfo->next = pDynMemAlgo->traceInfoList;
        traceInfo->next->prev = traceInfo;
        pDynMemAlgo->traceInfoList = traceInfo;
        traceInfo->prev = NULL;
    }
}

static void InitChunkAllocParams(DbMemCtxT *ctx, uint64_t allocSize, bool enableMemTrace, ChunkAllocParamsT *params)
{
    params->enableMemTrace = enableMemTrace;
    params->chunkHdrSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbAllocChunkT));
    uint64_t tmpSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, allocSize);
    if (SECUREC_UNLIKELY(params->enableMemTrace)) {
        tmpSize += MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DynMemChunkTraceInfoT));
    }
    /* Get the freelist index and the actual size to be allocated according to the required size */
    GetFreeIdxAndSize(tmpSize, &params->actualAllocSize, &params->freeIdx);
    if (SECUREC_LIKELY(!params->enableMemTrace)) {
        params->alignAllocSize = params->actualAllocSize;
    } else {
        // 开启memtrace时，chunk调测头里的size值统一记录内存申请的真实大小减去memtrace信息结构体对齐后的大小。方便free时能找到memtrace信息。
        params->alignAllocSize =
            params->actualAllocSize - MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DynMemChunkTraceInfoT));
    }
}

// 概率生成memtrace信息，避免过度占用cpu。
static bool NeedGenMemtrace(void)
{
    return (uint32_t)rand() % g_gmdbOnlineMemtraceSampleRate == 0;
}

// 内存上下文名称字符串匹配。
static bool CheckCtxNamePrefixMatch(const char *string, const char *prefix)
{
    uint32_t strLength = strlen(string);
    uint32_t prefixLen = strlen(prefix);
    uint32_t idx = 0;
    while (idx < strLength && idx < prefixLen) {
        if (string[idx] != prefix[idx]) {
            return false;
        }
        idx++;
    }
    return true;
}

inline static bool CheckEnableMemtrace(DbMemCtxT *ctx)
{
    if (SECUREC_UNLIKELY(ctx->enableMemTrace)) {
        return NeedGenMemtrace();
    }
    // 如果当前ctx未开启memtrace，则根据全局变量约束判断是否要开启。外面有加ctx锁。
    if (SECUREC_UNLIKELY(g_gmdbOnlineMemtraceFlag == 1)) {
        // 检查进程号匹配和前缀过滤匹配。
        if ((g_gmdbOnlineMemtracePid == DB_INVALID_UINT32 || DbAdptGetpid() == g_gmdbOnlineMemtracePid) &&
            CheckCtxNamePrefixMatch(ctx->ctxName, g_gmdbMemtracePrefix)) {
            ctx->isShared = true;
            return NeedGenMemtrace();
        }
    }
    return false;
}

static bool CheckIsBigChunkWhenAlloc(DbDynamicAlgoT *pDynMemAlgo, uint64_t allocSize, bool enableMemTrace)
{
    if (SECUREC_LIKELY(!enableMemTrace)) {
        return allocSize > pDynMemAlgo->allocChunkLimit;
    } else {
        uint64_t tmpSize =
            MAXALIGN(DB_DEFAULT_ALIGN_SIZE, allocSize) + MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DynMemChunkTraceInfoT));
        return tmpSize > pDynMemAlgo->allocChunkLimit;
    }
}

void *DynamicAlgoAlloc(DbMemCtxT *ctx, size_t size)
{
    DB_POINTER(ctx);
    DbDynamicMemCtxT *pDynMemCtx = (DbDynamicMemCtxT *)ctx;
    DbDynamicAlgoT *pDynMemAlgo = &pDynMemCtx->dynamicAlgo;
    uint64_t allocSize = (uint64_t)size;
#ifndef NDEBUG
    if (allocSize == 0) {
        DB_LOG_DBG_DEBUG("Dynctx input alloc size: 0.");
    }
    DynMemAllocAbnormalAlarm(ctx, pDynMemAlgo, allocSize);
#endif

    bool enableMemTrace = CheckEnableMemtrace(ctx);
    if (SECUREC_UNLIKELY(CheckIsBigChunkWhenAlloc(pDynMemAlgo, allocSize, enableMemTrace))) {
        /* Big chunk allocation */
        /* If requested size exceeds maximum for small chunks, allocate an entire block for this request. */
        return AllocBigChunk(ctx, allocSize, enableMemTrace);
    }
    /* Small chunk allocation */
    /* Request is small enough to be treated as a chunk. Look in the
     * corresponding free list to see if there is a free chunk we could reuse.
     * If one is found, remove it from the free list, make it again a member
     * of the alloc chunk and return its data address.
     */
    ChunkAllocParamsT params = {0};
    InitChunkAllocParams(ctx, allocSize, enableMemTrace, &params);
    /* Case1: get the chunk from the free list */
    DbAllocChunkT *pChunk = GetChunkFromFreeList(pDynMemCtx, pDynMemAlgo, &params);
    if (SECUREC_LIKELY(pChunk != NULL)) {
        return (void *)ALLOCCHUNK_DATA(pChunk, params.chunkHdrSize);
    }

    /* Case2: get the chunk from the available block(space enough) */
    DbAllocBlockT *pBlock = pDynMemAlgo->blocks;  // the first (active) block in the blocks list
    if (pBlock != NULL) {
        uint32_t freeSpace = (uint32_t)((uintptr_t)pBlock->endptr - (uintptr_t)pBlock->freeptr);
        if (freeSpace < (params.actualAllocSize + params.chunkHdrSize)) {
            /* not enough space to allocate via current active block, we need a new block */
            /* Add the remaining space in the current active block to the free List */
            AddRestOfBlockToFreelist(ctx, pBlock, freeSpace);
            pBlock = NULL;  // we need a new block
        }
    }

    /* if there are not enough space blocks, then alloc a new block */
    if (pBlock == NULL) {
        pBlock = AllocNewBlock(ctx, pDynMemAlgo, params.actualAllocSize);
        if (pBlock == NULL) {
            return NULL;
        }
    }
    /* then get the chunk from the available block(space enough) */
    pChunk = AllocAndInitChunk(pDynMemCtx, &params);
    if (SECUREC_UNLIKELY(params.enableMemTrace)) {
        DynMemChunkTraceInfoT *traceInfo =
            (DynMemChunkTraceInfoT *)((uintptr_t)pChunk + params.chunkHdrSize + params.alignAllocSize);
        LinkDynChunkTraceInfo(ctx, pDynMemAlgo, traceInfo, params.alignAllocSize);
    }

    return (void *)ALLOCCHUNK_DATA(pChunk, params.chunkHdrSize);
}

uint64_t DbDynamicAlgoGetChunkSize(DbMemCtxT *ctx, void *ptr)
{
    DbDynamicMemCtxT *pDynMemCtx = (DbDynamicMemCtxT *)ctx;
    DbDynamicAlgoT *pDynMemAlgo = &pDynMemCtx->dynamicAlgo;
    if (pDynMemAlgo->liteModOn) {
        uint32_t chunkHdrSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbDynUniChunkT));
        DbDynUniChunkT *pChunk = (DbDynUniChunkT *)ALLOCCHUNK_HEADER(ptr, chunkHdrSize);
        return pChunk->size;
    } else {
        uint32_t chunkHdrSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbAllocChunkT));
        DbAllocChunkT *pChunk = (DbAllocChunkT *)ALLOCCHUNK_HEADER(ptr, chunkHdrSize);
        return pChunk->size;
    }
}

// 释放超过allocChunkLimit(8k)的大块chunk。
void DynamicAlgoDelinkBigChunk(DbDynamicMemCtxT *ctx, DbAllocBigChunkT *bigChunk)
{
    DbAllocBigChunkT *prevChunk = bigChunk->prev;
    if (prevChunk == NULL) {
        ctx->dynamicAlgo.bigChunks = bigChunk->next;
    } else {
        bigChunk->prev->next = bigChunk->next;
    }
    if (bigChunk->next != NULL) {
        bigChunk->next->prev = bigChunk->prev;
    }
    bigChunk->prev = NULL;  // clear link info
    bigChunk->next = NULL;  // clear link info
}

MEM_ERR_FUNC_INLINE void DynamicAlgoFreeBlkHdrErr(const DbMemCtxT *ctx, const DbAllocBlockT *block)
{
    if (block->magicNum == MEM_MAGIC_FREE_NUMBER) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "Mem double free, inv head magic: %" PRIu32 ", ctxId: %" PRIu32 ", ctxName: %s", block->magicNum,
            ctx->ctxId, ctx->ctxName);
    } else {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "Mem access out of bounds. inv head magic: %" PRIu32 ", ctxId: %" PRIu32 ", ctxName: %s", block->magicNum,
            ctx->ctxId, ctx->ctxName);
    }
    DB_ASSERT(false);
}

inline static bool DbDynamicAlgoBlockValid(const DbMemCtxT *ctx, const DbAllocBlockT *block)
{
    if (block->magicNum != MEM_MAGIC_NUMBER) {
        DynamicAlgoFreeBlkHdrErr(ctx, block);
        return false;
    }
    return true;
}

ALWAYS_INLINE static void RemoveChunksInFreeList(
    DbAllocChunkT **freelist, const DbAllocBlockT *block, DbDynamicAlgoT *pDynMemAlgo, uint32_t *chunkNum)
{
    for (uint32_t i = 0; i < ALLOCSET_NUM_FREELISTS; i++) {
        // Remove chunks on the front of list.
        DbAllocChunkT *headChunk = freelist[i];
        while (headChunk != NULL && OWNER_BLOCK_OF_CHUNK(headChunk) == block) {
            freelist[i] = (DbAllocChunkT *)headChunk->ownerOrNextChunk;
            headChunk = freelist[i];
            (*chunkNum)++;
        }
        if (headChunk == NULL) {
            EraseBitMapByIdx(pDynMemAlgo->freelistBitmap, i);
            continue;
        }
        // Remove chunks in the middle of list.
        DbAllocChunkT *prevChunk = headChunk;
        DbAllocChunkT *nextChunk = (DbAllocChunkT *)headChunk->ownerOrNextChunk;
        while (nextChunk != NULL) {
            if (OWNER_BLOCK_OF_CHUNK(nextChunk) == block) {
                prevChunk->ownerOrNextChunk = nextChunk->ownerOrNextChunk;
                (*chunkNum)++;
            } else {
                prevChunk = nextChunk;
            }
            nextChunk = (DbAllocChunkT *)nextChunk->ownerOrNextChunk;
        }
    }
}

/*
 * Input para block is a recyclable memory block on which all memory chunks allocated by the user have been free.
 * The current recycle strategy is to remove all the information of this block from the free list, it may cause
 *   memory free performance jitter. Therefore, it is not applicable to performance scenarios and process-oriented
 *   memory management scenarios.
 */
void RecycleBlockFromFreeList(DbMemCtxT *pHeader, DbDynamicAlgoT *pDynMemAlgo, DbAllocBlockT *block)
{
    // This function is triggered only when block allocCount is 0.
    DB_ASSERT(block->allocCount == 0);
    if (!DbDynamicAlgoBlockValid(pHeader, block)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Free block.");
        return;
    }
    // 1.The first memory block reserved for reset is not recycled;
    // 2.The memory block that is currently for allocation is not recycled.
    if (block == pDynMemAlgo->keeper || block == pDynMemAlgo->blocks) {
        return;
    }
    DbAllocChunkT **freelist = pDynMemAlgo->freelist;
    uint32_t chunkNum = 0;  // Number of chunks removed from the free list
    RemoveChunksInFreeList(freelist, block, pDynMemAlgo, &chunkNum);
#ifndef NDEBUG
    // check the number of recycled chunks is as expected
    DB_ASSERT(chunkNum == block->chunkNum);
#endif
    block->prev->next = block->next;
    if (block->next != NULL) {
        block->next->prev = block->prev;
    }

    // Maintain the statistics of the algorithm layer
    uint32_t blockSize = block->blockSize;
    pDynMemAlgo->totalPhySize -= blockSize;
    uint32_t blockHdrSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbAllocBlockT));
    pDynMemAlgo->totalAllocSizeByCtx -= blockHdrSize;
    pDynMemAlgo->blockNum--;
    pDynMemAlgo->totalBlockSize -= block->blockSize;

    DB_FREE((void *)((uintptr_t)block - block->blockAddrOffset));
    // 这个地方更新阈值是减法操作不会超过父节点阈值，逻辑上不会失败。
    Status ret = UpdateDynCtxThresholdParent(pHeader, blockSize, false);
    DB_ASSERT(ret == GMERR_OK);
}

/* Remove an allocated small chunk (to free now) from the recorded allchunks list (for memtrace) */
void DelinkChunkTraceInfo(DbMemCtxT *ctx, DbDynamicAlgoT *pDynMemAlgo, DynMemChunkTraceInfoT *traceInfo)
{
    if (pDynMemAlgo->traceInfoList == traceInfo) {
        pDynMemAlgo->traceInfoList = traceInfo->next;
        if (traceInfo->next != NULL) {
            traceInfo->next->prev = NULL;
        }
    } else {
        if (traceInfo->next != NULL) {
            traceInfo->next->prev = traceInfo->prev;
        }
        traceInfo->prev->next = traceInfo->next;
    }
}

MEM_ERR_FUNC_INLINE void DynamicAlgoFreeBigChkHdrErr(const DbMemCtxT *ctx, const DbAllocBigChunkT *pBigChunk)
{
    if (pBigChunk->magicNum == MEM_MAGIC_FREE_NUMBER) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "Mem double free, inv big head magic: %" PRIu32 ", ctxId: %" PRIu32 ", ctxName: %s", pBigChunk->magicNum,
            ctx->ctxId, ctx->ctxName);
    } else {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "Mem access out of bounds, inv big head magic: %" PRIu32 ", ctxId: %" PRIu32 ", ctxName: %s",
            pBigChunk->magicNum, ctx->ctxId, ctx->ctxName);
    }
    DB_ASSERT(false);
}

MEM_ERR_FUNC_INLINE void DynamicAlgoFreeBigChkTailErr(const DbMemCtxT *ctx, const DbAllocBigChunkTailT *pBigChunkTail)
{
    if (pBigChunkTail->magicNum == MEM_MAGIC_FREE_NUMBER) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "Mem double free, inv tail magic: %" PRIu32 ", ctxId: %" PRIu32 ", ctxName: %s", pBigChunkTail->magicNum,
            ctx->ctxId, ctx->ctxName);
    } else {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "Mem access out of bounds, inv tail magic: %" PRIu32 ", ctxId: %" PRIu32 ", ctxName: %s",
            pBigChunkTail->magicNum, ctx->ctxId, ctx->ctxName);
    }
    DB_ASSERT(false);
}

inline static bool DynamicAlgoBigChkValid(
    const DbMemCtxT *ctx, const DbAllocBigChunkT *bigChunk, const DbAllocBigChunkTailT *bigChunkTail)
{
    bool isValid = true;
    if (SECUREC_UNLIKELY(bigChunk->magicNum != MEM_MAGIC_NUMBER)) {
        DynamicAlgoFreeBigChkHdrErr(ctx, bigChunk);
        isValid = false;
    }
    if (SECUREC_UNLIKELY(bigChunkTail->magicNum != MEM_MAGIC_NUMBER)) {
        DynamicAlgoFreeBigChkTailErr(ctx, bigChunkTail);
        isValid = false;
    }
    return isValid;
}

static void FreeNormalChunk(DbMemCtxT *ctx, void *ptr, DbAllocChunkT *pChunk, uint32_t chkHdrSize)
{
    DbDynamicAlgoT *pDynMemAlgo = &((DbDynamicMemCtxT *)ctx)->dynamicAlgo;
    // 重设魔术字
    pChunk->magicNum = MEM_MAGIC_FREE_NUMBER_8;
    /* 去初始化memtrace信息。 */
    if (SECUREC_UNLIKELY(pChunk->memtraceChunk)) {
        DynMemChunkTraceInfoT *traceInfo = (DynMemChunkTraceInfoT *)((uintptr_t)ptr + pChunk->size);
        DelinkChunkTraceInfo(ctx, pDynMemAlgo, traceInfo);
        pChunk->memtraceChunk = false;
        // memtrace场景需要先将memtrace信息大小加回去再复用。实际chunk大小=用户申请大小+memtrace信息。
        pChunk->size += MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DynMemChunkTraceInfoT));
    }
    /* put the chunk into freelist */
    uint32_t freeIdx = GetFreeIdx(pChunk->size);
    pChunk->ownerOrNextChunk = pDynMemAlgo->freelist[freeIdx];
    pDynMemAlgo->freelist[freeIdx] = pChunk;
    MarkBitMapByIdx(pDynMemAlgo->freelistBitmap, freeIdx);
    /* 更新内存上下文统计值。 */
    pDynMemAlgo->totalAllocSizeByCtx -= (chkHdrSize + pChunk->size);

    DbAllocBlockT *ownerBlock = OWNER_BLOCK_OF_CHUNK(pChunk);
    ownerBlock->allocCount--;
    if (ownerBlock->allocCount == 0 && pDynMemAlgo->needRecycle) {
        /* trigger the memory recycle */
        RecycleBlockFromFreeList(ctx, pDynMemAlgo, ownerBlock);
    }
}

static void FreeBigChunk(DbMemCtxT *ctx, void *ptr, DbAllocChunkT *pChunk, uint32_t chkHdrSize)
{
    DbDynamicAlgoT *pDynMemAlgo = &((DbDynamicMemCtxT *)ctx)->dynamicAlgo;
    /* Big chunks are certain to have been allocated as single-chunk */
    uint32_t bigChkHdrSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbAllocBigChunkT));
    uint32_t bigChkTailSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbAllocBigChunkTailT));
    DbAllocBigChunkT *bigChunk = (DbAllocBigChunkT *)ALLOCBIGCHUNK_HEADER(ptr, chkHdrSize, bigChkHdrSize);
    DbAllocBigChunkTailT *bigChunkTail =
        (DbAllocBigChunkTailT *)((uint8_t *)bigChunk + bigChunk->bigChunkSize - bigChkTailSize);
    if (!DynamicAlgoBigChkValid(ctx, bigChunk, bigChunkTail)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Free bigChunk.");
        return;
    }
    pChunk->magicNum = MEM_MAGIC_FREE_NUMBER_8;
    if (SECUREC_UNLIKELY(pChunk->memtraceChunk)) {
        DynMemChunkTraceInfoT *traceInfo = (DynMemChunkTraceInfoT *)((uintptr_t)ptr + pChunk->size);
        DelinkChunkTraceInfo(ctx, pDynMemAlgo, traceInfo);
        pChunk->memtraceChunk = false;
    }
    bigChunk->magicNum = MEM_MAGIC_FREE_NUMBER;
    bigChunkTail->magicNum = MEM_MAGIC_FREE_NUMBER;
    DynamicAlgoDelinkBigChunk((DbDynamicMemCtxT *)ctx, bigChunk);
    uint64_t totalSize = bigChunk->bigChunkSize;  // Include data area and management structure sizes
    /* The update threshold here is the subtraction operation and will not exceed the threshold */
    Status ret = UpdateDynCtxThresholdParent(ctx, totalSize, false);
    DB_ASSERT(ret == GMERR_OK);
    pChunk->ownerOrNextChunk = NULL;
    DB_FREE((void *)((uintptr_t)bigChunk - bigChunk->chunkAddrOffset));
    pDynMemAlgo->totalPhySize -= totalSize;
    pDynMemAlgo->totalAllocSizeByCtx -= totalSize;
    pDynMemAlgo->bigChunkNum--;
    pDynMemAlgo->totalBigChunkSize -= totalSize;
}

inline static bool CheckIsBigChunkWhenFree(DbDynamicAlgoT *pDynMemAlgo, DbAllocChunkT *pChunk)
{
    uint32_t tmpSize = pChunk->size;
    if (pChunk->memtraceChunk) {
        tmpSize += MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DynMemChunkTraceInfoT));
    }
    return tmpSize > pDynMemAlgo->allocChunkLimit;
}

void DynamicAlgoFree(DbMemCtxT *ctx, void *ptr)
{
    DB_POINTER2(ctx, ptr);
    DbDynamicMemCtxT *pDynMemCtx = (DbDynamicMemCtxT *)ctx;
    DbDynamicAlgoT *pDynMemAlgo = &pDynMemCtx->dynamicAlgo;
    /* Here we need to judge whether it is the header of the chunk, because sometimes the memory
     * is out of bounds, so that the chunk we get is not what we want.
     */
    uint32_t chkHdrSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbAllocChunkT));
    DbAllocChunkT *pChunk = (DbAllocChunkT *)ALLOCCHUNK_HEADER(ptr, chkHdrSize);
    if (!DbDynamicAlgoChkValid(ctx, pDynMemAlgo, pChunk)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Free ptr.");
        return;
    }
    ctx->totalAllocSize -= pChunk->size;
    if (CheckIsBigChunkWhenFree(pDynMemAlgo, pChunk)) {
        /* free a big chunk */
        FreeBigChunk(ctx, ptr, pChunk, chkHdrSize);
    } else {
        /* free a small chunk */
        FreeNormalChunk(ctx, ptr, pChunk, chkHdrSize);
    }
}

// 调用者保证ctx非空
static void DynamicAlgoClearBigChunk(DbDynamicMemCtxT *ctx)
{
    DbDynamicAlgoT *pDynamicAlgo = &ctx->dynamicAlgo;
    DbMemCtxT *memCtx = &ctx->header;
    uint32_t bigChunkHdrSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbAllocBigChunkT));
    uint32_t bigChkTailSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbAllocBigChunkTailT));
    DbAllocBigChunkT *pBigChunk = pDynamicAlgo->bigChunks;

    uint64_t totalSizeSum = 0;
    while (pBigChunk != NULL) {
        DbAllocChunkT *pChunk = GET_ALLOCCHUNK_HEADER(pBigChunk, bigChunkHdrSize);
        DbAllocBigChunkTailT *bigChunkTail =
            (DbAllocBigChunkTailT *)((void *)pBigChunk + pBigChunk->bigChunkSize - bigChkTailSize);
        if (SECUREC_UNLIKELY(!DynamicAlgoBigChkValid(memCtx, pBigChunk, bigChunkTail))) {
            // 遍历bigChunk链表的过程中检测到踩内存，认为指向后面的bigChunk的指针不再可信，停止释放bigChunk。
            pDynamicAlgo->bigChunks = NULL;
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Clear bigChunk.");
            break;
        }
        DbAllocBigChunkT *next = pBigChunk->next;
        uint64_t totalSize = pBigChunk->bigChunkSize;  // Include data area and management structure sizes
        totalSizeSum += totalSize;
        pDynamicAlgo->totalPhySize -= totalSize;
        pChunk->ownerOrNextChunk = NULL;
        DB_FREE((void *)((uintptr_t)pBigChunk - pBigChunk->chunkAddrOffset));
        pBigChunk = next;
    }
    // 这个地方更新阈值是减法操作不会超过父节点阈值，逻辑上不会失败。
    Status ret = UpdateDynCtxThresholdParent(memCtx, totalSizeSum, false);
    DB_ASSERT(ret == GMERR_OK);
    pDynamicAlgo->bigChunks = NULL;
}

static ALWAYS_INLINE void ResetDynMemFreeList(DbDynamicAlgoT *pDynamicAlgo)
{
    for (uint32_t i = 0; i < FREELIST_BITMAP_BYTENUM; i++) {
        if (pDynamicAlgo->freelistBitmap[i] == 0) {
            continue;
        }
        uint32_t freeListIdx = 0;
        uint8_t byteInFreeList = pDynamicAlgo->freelistBitmap[i];
        while (byteInFreeList != 0) {
            if ((byteInFreeList & 1u) != 0) {
                pDynamicAlgo->freelist[i * BYTE_LENGTH + freeListIdx] = NULL;
            }
            byteInFreeList = byteInFreeList >> 1;
            ++freeListIdx;
        }
        pDynamicAlgo->freelistBitmap[i] = 0u;
    }
}

static void ResetDynMemAlgoInfo(DbDynamicAlgoT *pDynamicAlgo)
{
    pDynamicAlgo->totalAllocSizeByCtx = 0;
    pDynamicAlgo->blockNum = (pDynamicAlgo->blocks == NULL) ? 0 : 1;
    pDynamicAlgo->bigChunkNum = 0;
    pDynamicAlgo->totalBigChunkSize = 0;
    pDynamicAlgo->totalBlockSize = (pDynamicAlgo->blocks == NULL) ? 0 : pDynamicAlgo->initBlockSize;
    pDynamicAlgo->allocTimes = 0;
    pDynamicAlgo->freeTimes = 0;
}

// ctx非空已经由外部调用者保证,外部调用者亦保证了其没有孩子节点
void DynamicAlgoReset(DbMemCtxT *ctx)
{
    DbDynamicMemCtxT *pContext = (DbDynamicMemCtxT *)ctx;
    DbDynamicAlgoT *pDynamicAlgo = &pContext->dynamicAlgo;
    DbAllocBlockT *pBlock = pDynamicAlgo->blocks;
    pDynamicAlgo->blocks = pDynamicAlgo->keeper;
    pDynamicAlgo->nextBlockSize = NEXT_BLOCK_SIZE;
    if (pDynamicAlgo->bigChunks != NULL) {
        DynamicAlgoClearBigChunk(pContext);  // 清除此memctx的bigChunk
    }

    // 清空freelist和freeChunkNum中信息
    ResetDynMemFreeList(pDynamicAlgo);

    pDynamicAlgo->traceInfoList = NULL;

    uint64_t totalSizeSum = 0;
    while (pBlock != NULL) {
        if (!DbDynamicAlgoBlockValid(ctx, pBlock)) {
            // Next block ptr is not safe under memory access out of bound scenario, return directly.
            // 如果在遍历blocks链表的时候检测到block头踩存，认为链表当前block及之后的block都无法正确访问，全部废弃。
            pDynamicAlgo->keeper = NULL;
            pDynamicAlgo->blocks = NULL;
            pDynamicAlgo->totalPhySize = 0;
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Reset ctx.");
            break;
        }
        DbAllocBlockT *next = pBlock->next;
        if (pBlock == pDynamicAlgo->keeper) {
            uint8_t *dataStart = ((uint8_t *)pBlock) + MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbAllocBlockT));
            pBlock->freeptr = (char *)dataStart;
            pBlock->prev = NULL;
            pBlock->next = NULL;
            pBlock->allocCount = 0;
#ifndef NDEBUG
            pBlock->chunkNum = 0;
#endif
        } else {
            uint32_t totalSize = pBlock->blockSize;
            totalSizeSum += totalSize;
            pDynamicAlgo->totalPhySize -= (uint64_t)totalSize;
            DB_FREE((void *)((uintptr_t)pBlock - pBlock->blockAddrOffset));
        }
        pBlock = next;
    }
    if (totalSizeSum != 0) {
        // 这个地方更新阈值是减法操作不会超过父节点阈值，逻辑上不会失败。
        Status ret = UpdateDynCtxThresholdParent(ctx, totalSizeSum, false);
        DB_ASSERT(ret == GMERR_OK);
    }
    ctx->totalAllocSize = 0;
    ctx->peakAllocSize = 0;
    ResetDynMemAlgoInfo(pDynamicAlgo);
}

// ctx非空已经由外部调用者保证
void DynamicAlgoClear(DbMemCtxT *ctx)
{
    DbDynamicMemCtxT *pContext = (DbDynamicMemCtxT *)ctx;
    DbDynamicAlgoT *pDynamicAlgo = &pContext->dynamicAlgo;
    DbAllocBlockT *pBlock = (DbAllocBlockT *)pDynamicAlgo->blocks;
    for (uint32_t i = 0; i < ALLOCSET_NUM_FREELISTS; ++i) {
        pDynamicAlgo->freelist[i] = NULL;
    }
    for (uint32_t i = 0; i < FREELIST_BITMAP_BYTENUM; ++i) {
        pDynamicAlgo->freelistBitmap[i] = 0u;
    }
    pDynamicAlgo->blocks = NULL;
    pDynamicAlgo->nextBlockSize = NEXT_BLOCK_SIZE;
    DynamicAlgoClearBigChunk(pContext);
    pDynamicAlgo->bigChunks = NULL;
    pDynamicAlgo->keeper = NULL;
    pDynamicAlgo->traceInfoList = NULL;

    uint64_t totalSizeSum = 0;
    while (pBlock != NULL) {
        if (!DbDynamicAlgoBlockValid(ctx, pBlock)) {
            // Next block ptr is not safe under memory access out of bound scenario, return directly.
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Clear ctx.");
            break;
        }
        DbAllocBlockT *pNextBlock = pBlock->next;
        uint32_t totalSize = pBlock->blockSize;
        totalSizeSum += totalSize;
        DB_FREE((void *)((uintptr_t)pBlock - pBlock->blockAddrOffset));
        pBlock = pNextBlock;
    }
    if (totalSizeSum != 0) {
        // 这个地方更新阈值是减法操作不会超过父节点阈值，逻辑上不会失败。
        Status ret = UpdateDynCtxThresholdParent(ctx, totalSizeSum, false);
        DB_ASSERT(ret == GMERR_OK);
    }
    pDynamicAlgo->totalPhySize = 0;
    ctx->totalAllocSize = 0;
    ctx->peakAllocSize = 0;
    ResetDynMemAlgoInfo(pDynamicAlgo);
}

static uint64_t DbDyMemCtxGetTotalPhySpaceOnTree(DbMemCtxT *header)
{
    DbMemCtxT *thresholdParent = header->dynCtxPtrs.phyThreshParent;
    if (thresholdParent == NULL) {
        return DB_MAX_MEMCTX_TOTAL_PHY_SIZE;
    }
    return thresholdParent->totalPhySizeOnThisTree;
}

// ctx非空已经由外部调用者保证 加锁也由外界保证
void DynamicAlgoStats(DbMemCtxT *ctx, void *pdyStats)
{
    DB_POINTER(pdyStats);
    DbDyMemCtxStatsT *pDbDyMemCtxStats = (DbDyMemCtxStatsT *)pdyStats;
    DbDynamicMemCtxT *pContext = (DbDynamicMemCtxT *)ctx;
    DbDynamicAlgoT *pDynamicAlgo = &pContext->dynamicAlgo;
    (void)memset_s(pDbDyMemCtxStats, sizeof(DbDyMemCtxStatsT), 0, sizeof(DbDyMemCtxStatsT));
    pDbDyMemCtxStats->nBlock = pDynamicAlgo->blockNum;
    pDbDyMemCtxStats->totalBlockSize = pDynamicAlgo->totalBlockSize;
    pDbDyMemCtxStats->bigChunkNum = pDynamicAlgo->bigChunkNum;
    pDbDyMemCtxStats->uniChunkNum = pDynamicAlgo->uniChunkNum;
    pDbDyMemCtxStats->totalBigChunkSize = pDynamicAlgo->totalBigChunkSize;
    pDbDyMemCtxStats->totalUniChunkSize = pDynamicAlgo->liteModOn ? pDynamicAlgo->totalPhySize : 0u;
    pDbDyMemCtxStats->totalAllocSize = ctx->totalAllocSize;
    pDbDyMemCtxStats->totalPhySize = pDynamicAlgo->totalPhySize;
    pDbDyMemCtxStats->maxTotalPhySize = ctx->maxTotalPhySize;
    pDbDyMemCtxStats->childNum = ctx->childNum;
    pDbDyMemCtxStats->totalPhySizeOnThisTree = DbDyMemCtxGetTotalPhySpaceOnTree(ctx);
    pDbDyMemCtxStats->allocTimes = pDynamicAlgo->allocTimes;
    pDbDyMemCtxStats->freeTimes = pDynamicAlgo->freeTimes;
    pDbDyMemCtxStats->memUtilizationRate =
        (pDbDyMemCtxStats->totalPhySize == 0) ?
            0 :
            (float)pDbDyMemCtxStats->totalAllocSize / (float)pDbDyMemCtxStats->totalPhySize;
#ifndef NDEBUG
    pDbDyMemCtxStats->childOperateRate = ctx->childOperateRate;
    pDbDyMemCtxStats->allocTimesRate = pDynamicAlgo->allocTimesRate;
    pDbDyMemCtxStats->allocSizeRate = pDynamicAlgo->allocSizeRate;
#endif
}

// 获取memCtx物理内存使用峰值，调用者保证ctx不为空。
// 原子操作不加锁。
uint64_t DynamicAlgoGetPhySizePeak(DbMemCtxT *ctx)
{
    DbDynamicMemCtxT *pContext = (DbDynamicMemCtxT *)ctx;
    DbDynamicAlgoT *pDynamicAlgo = &pContext->dynamicAlgo;
    return DbAtomicGet64(&(pDynamicAlgo->currPhySizePeak));
}

// reset memCtx物理内存使用峰值，调用者保证ctx不为空。
// 上层调用者加锁。
void DynamicAlgoResetPhySizePeak(DbMemCtxT *ctx)
{
    DbDynamicMemCtxT *pContext = (DbDynamicMemCtxT *)ctx;
    DbDynamicAlgoT *pDynamicAlgo = &pContext->dynamicAlgo;
    DbAtomicSet64(&pDynamicAlgo->currPhySizePeak, pDynamicAlgo->totalPhySize);
}

// 为了提升快路径性能，提供get and clear physize peak接口。
// 原子操作不加锁。
uint64_t DynamicAlgoGetAndResetPeak(DbMemCtxT *ctx)
{
    DbDynamicMemCtxT *pContext = (DbDynamicMemCtxT *)ctx;
    DbDynamicAlgoT *pDynamicAlgo = &pContext->dynamicAlgo;
    // 这里先取值再reset是可以的，这里对峰值精确要求没有特别高（原子操作不对ctx加锁，性能更高）。
    uint64_t size = DbAtomicGet64(&(pDynamicAlgo->currPhySizePeak));
    DbAtomicSet64(&pDynamicAlgo->currPhySizePeak, pDynamicAlgo->totalPhySize);
    return size;
}

Status DbDynCtxResetMaxSize(DbMemCtxT *ctx, uint64_t size)
{
    if (SECUREC_UNLIKELY(ctx == NULL || ctx->magicCode != DB_MAGIC_CODE)) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Reset ctx maxsize.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    Status ret = DbMemCtxLock(ctx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return GMERR_LOCK_NOT_AVAILABLE;
    }
    uint64_t totalPhySize = ((DbDynamicMemCtxT *)ctx)->dynamicAlgo.totalPhySize;
    if (totalPhySize > size) {
        DbMemCtxUnLock(ctx);
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE,
            "Reset ctx maxsize. totalPhysize %" PRIu64 ", maxSize: %" PRIu64 ".", totalPhySize, size);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    ((DbDynamicMemCtxT *)ctx)->dynamicAlgo.maxSize = size;
    DbMemCtxUnLock(ctx);
    return GMERR_OK;
}

/*
 * If chunk is allocated from escape ctx, the chunk header will be marked.
 * Beware alignSize is 8 bytes by default so that we are able to backtrace the header.
 */
bool CheckEscapeCtxMark(DbMemCtxT *ctx, void *ptr)
{
    DB_POINTER(ptr);
    DbDynamicMemCtxT *escapeCtx = (DbDynamicMemCtxT *)GetEscapeCtx(ctx);
    DB_POINTER(escapeCtx);  // Checked by caller.

    if (!escapeCtx->dynamicAlgo.liteModOn) {
        uint32_t chunkHdrSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbAllocChunkT));
        DbAllocChunkT *pChunk = (DbAllocChunkT *)ALLOCCHUNK_HEADER(ptr, chunkHdrSize);
        if (!pChunk->escapeMark) {
            return false;
        }
        if (pChunk->ownerOrNextChunk != &escapeCtx->dynamicAlgo) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Chunk not belong to escapeCtx.");
            return false;
        }
    } else {
        uint32_t chunkHdrSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbDynUniChunkT));
        DbDynUniChunkT *pUniChunk = (DbDynUniChunkT *)ALLOCCHUNK_HEADER(ptr, chunkHdrSize);
        if (!pUniChunk->escapeMark) {
            return false;
        }
#ifndef NDEBUG
        if (pUniChunk->chunkOwner != &escapeCtx->dynamicAlgo) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "UniChunk not belong to escapeCtx.");
            return false;
        }
#endif
    }

    return true;
}

void DbDynCtxGetChunkTraceStats(DbMemCtxT *ctx, DynChunkTraceStatsT *chunkTraceStats, bool isBigChunk)
{
    DB_POINTER2(ctx, chunkTraceStats);
    // MiniKv未支持memtrace在线开启，接口保持不变。V5不区分是否bigChunk，isBigChunk入参实际不生效，且一次调用就能收集全。
    if (isBigChunk) {
        chunkTraceStats->currChunk = NULL;
        return;
    }
    const DbDynamicMemCtxT *pContext = (const DbDynamicMemCtxT *)ctx;
    const DbDynamicAlgoT *pDynamicAlgo = &pContext->dynamicAlgo;

    DynMemChunkTraceInfoT *chunkTraceInfo = NULL;
    if (chunkTraceStats->isListHead) {
        chunkTraceStats->isListHead = false;
        chunkTraceInfo = pDynamicAlgo->traceInfoList;
    } else {
        chunkTraceInfo = chunkTraceStats->nextChunk;
    }
    if (chunkTraceInfo != NULL) {
        chunkTraceStats->currChunk = chunkTraceInfo;
        chunkTraceStats->nextChunk = chunkTraceInfo->next;
        chunkTraceStats->callStack = chunkTraceInfo->callStack;
        chunkTraceStats->stackSize = chunkTraceInfo->stackSize;
        chunkTraceStats->time = chunkTraceInfo->time;
        chunkTraceStats->chunkSize = chunkTraceInfo->size;
    } else {
        chunkTraceStats->currChunk = NULL;
    }
}

/**************** Methods for lite ctx ****************/
MEM_ERR_FUNC_INLINE void LogUniChkHdrErr(const DbMemCtxT *ctx, const DbDynUniChunkT *pChunk)
{
    if (pChunk->magicNum == MEM_MAGIC_FREE_NUMBER) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "Mem double free, inv head magic: %" PRIu32 ", ctxId: %" PRIu32 ", ctxName: %s", pChunk->magicNum,
            ctx->ctxId, ctx->ctxName);
    } else {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "Mem access out of bounds, inv head magic: %" PRIu32 ", ctxId: %" PRIu32 ", ctxName: %s", pChunk->magicNum,
            ctx->ctxId, ctx->ctxName);
    }
}

inline static bool CheckUniChkValid(
    const DbMemCtxT *ctx, const DbDynamicAlgoT *pDynMemAlgo, const DbDynUniChunkT *pChunk)
{
    if (pChunk->magicNum != MEM_MAGIC_NUMBER) {
        LogUniChkHdrErr(ctx, pChunk);
        DB_ASSERT(false);
        return false;
    }
#ifndef NDEBUG
    if (pChunk->chunkOwner != pDynMemAlgo) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "Alloc and free not in the same ctx. CtxId: %" PRIu32 ", ctxName: %s.", ctx->ctxId, ctx->ctxName);
        DB_ASSERT(false);
        return false;
    }
#endif
    return true;
}

static void SetDynUniChunk(
    DbMemCtxT *ctx, DbDynamicAlgoT *pDynMemAlgo, DbDynUniChunkT *pChunk, UniChunkAllocParamsT *params)
{
    DB_POINTER3(ctx, pDynMemAlgo, pChunk);
    pChunk->escapeMark = IsEscapeCtx(ctx);
    pChunk->size = params->allocSize;
    pChunk->magicNum = MEM_MAGIC_NUMBER;
#ifndef NDEBUG
    pChunk->chunkOwner = pDynMemAlgo;
#endif
    pChunk->memtraceChunk = params->enableMemTrace;
}

typedef struct MemMallocHdrLite {
    uint32_t addrOffset;
    uint32_t reserve;
} MemMallocHdrLiteT;

// 关闭二次管理在非yunshanlite环境手动对齐addr。全局内存统计值在外面做。
inline static __attribute__((always_inline)) void *DbMallocLite(uint32_t size)
{
    // 多申请8字节，如果malloc的首addr非8字节对齐，还要补上对齐操作。
    uint32_t alignSize = (uint32_t)MALLOCALIGN(DB_DEFAULT_ALIGN_SIZE, size + DB_DEFAULT_ALIGN_SIZE) +
                         (uint32_t)MALLOCALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(MemMallocHdrLiteT));
    // 看护反转
    if (SECUREC_UNLIKELY(alignSize < size)) {
        return NULL;
    }
    void *ptr = DbAdptDynamicMemAlloc(alignSize);
    if (ptr == NULL) {
        return NULL;
    }
    /*
     *  malloc分配的内存块结构:
     *  | 首addr对齐的偏移 | 4B(记录为首addr对齐做的偏移大小) | 4B reserve | 返回给用户的指针addr
     *  ^                ^                                                           ^
     *  |                |                                                           |
     * ptr              alignedPtr                                                 returned ptr
     */
    MemMallocHdrLiteT *alignedPtr = (MemMallocHdrLiteT *)MALLOCALIGN(DB_DEFAULT_ALIGN_SIZE, ptr);
    // 记录对齐首addr的字节数。
    alignedPtr->addrOffset = (uint32_t)((uintptr_t)alignedPtr - (uintptr_t)ptr);
    return (uint8_t *)alignedPtr + MALLOCALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(MemMallocHdrLiteT));
}

// 关闭二次管理在非yunshanlite环境手动对齐addr。全局内存统计值在外面做。
inline static __attribute__((always_inline)) void DbFreeLite(void *ptr)
{
    if (ptr == NULL) {
        return;
    }
    void *actualPtr = (void *)((uintptr_t)ptr - MALLOCALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(MemMallocHdrLiteT)));
    // 获取释放的内存大小。
    MemMallocHdrLiteT *memMallocHdrPtr = (MemMallocHdrLiteT *)actualPtr;
    // 减去对齐首addr的字节大小，得到最初malloc的指针。
    actualPtr = (void *)((uintptr_t)actualPtr - memMallocHdrPtr->addrOffset);
    DbAdptDynamicMemFree(actualPtr);
}

inline static void *UniChunkMemAlloc(uint32_t allocSize)
{
#if (defined HPE || defined RTOSV2X)
    return DbAdptDynamicMemAlloc(allocSize);
#else
    return DbMallocLite(allocSize);
#endif
}

inline static void UniChunkMemFree(void *pChunk)
{
#if (defined HPE || defined RTOSV2X)
    DbAdptDynamicMemFree(pChunk);
#else
    DbFreeLite(pChunk);
#endif
}

// euler环境、v2环境, 关闭二次管理算法的时候，采用双向链表管理chunk。
// hpe、v2x，在关闭二次管理算法的时候，采用多级链表管理chunk。
#if (defined HPE || defined RTOSV2X)
// 多级链表操作逻辑
static inline void AddDynUniChunkToList(DbDynamicAlgoT *pDynMemAlgo, DbDynUniChunkT *pChunk)
{
    DB_POINTER2(pDynMemAlgo, pChunk);
    // 根据ptr计算出ptr所属的链表，并插入。
    pChunk->pNextChunk = pDynMemAlgo->uniChunkList[(uintptr_t)pChunk % UNICHUNK_LISTS];
    pDynMemAlgo->uniChunkList[(uintptr_t)pChunk % UNICHUNK_LISTS] = pChunk;
    pDynMemAlgo->uniChunkNum++;
}

static void RmDynUniChunkFromList(DbDynamicAlgoT *pDynMemAlgo, DbDynUniChunkT *pChunk)
{
    DB_POINTER2(pDynMemAlgo, pChunk);
    DbDynUniChunkT *nextChunk = pDynMemAlgo->uniChunkList[(uintptr_t)pChunk % UNICHUNK_LISTS];
    DB_ASSERT(nextChunk != NULL);  // remove前已经add过，所以不为空。
    DbDynUniChunkT *prevChunk = NULL;
    while (nextChunk != NULL) {
        if (nextChunk == pChunk) {
            break;
        }
        prevChunk = nextChunk;
        nextChunk = nextChunk->pNextChunk;
    }
    if (prevChunk == NULL) {
        pDynMemAlgo->uniChunkList[(uintptr_t)pChunk % UNICHUNK_LISTS] = pChunk->pNextChunk;
    } else {
        prevChunk->pNextChunk = pChunk->pNextChunk;
    }
    pDynMemAlgo->uniChunkNum--;
    pChunk->pNextChunk = NULL;
}

static inline void FreeAllUniChunks(DbDynamicAlgoT *pDynamicAlgo)
{
    DbDynUniChunkT *pNextChunk = NULL;
    for (uint32_t i = 0; i < UNICHUNK_LISTS; i++) {
        DbDynUniChunkT *pCurrChunk = pDynamicAlgo->uniChunkList[i];
        while (pCurrChunk != NULL) {
            pNextChunk = pCurrChunk->pNextChunk;
            UniChunkMemFree((void *)pCurrChunk);
            pCurrChunk = pNextChunk;
        }
        pDynamicAlgo->uniChunkList[i] = NULL;
    }
    (void)DbAtomicFetchAndSub64(&g_gmdbCurrUsedDynSize, pDynamicAlgo->totalPhySize);
}

#else
// 双向链表操作逻辑

static void AddDynUniChunkToList(DbDynamicAlgoT *pDynMemAlgo, DbDynUniChunkT *pChunk)
{
    DB_POINTER2(pDynMemAlgo, pChunk);
    if (pDynMemAlgo->uniChunkDoubleList != NULL) {
        DbDynUniChunkT *nextChunk = pDynMemAlgo->uniChunkDoubleList;
        nextChunk->pPrevChunk = (void *)pChunk;
    }
    pChunk->pNextChunk = pDynMemAlgo->uniChunkDoubleList;
    pChunk->pPrevChunk = (void *)pDynMemAlgo;
    pDynMemAlgo->uniChunkDoubleList = (void *)pChunk;
    pDynMemAlgo->uniChunkNum++;
}

static void RmDynUniChunkFromList(DbDynamicAlgoT *pDynMemAlgo, DbDynUniChunkT *pChunk)
{
    DB_POINTER3(pDynMemAlgo, pChunk, pChunk->pPrevChunk);
    if (pChunk->pPrevChunk == (void *)pDynMemAlgo) {
        pDynMemAlgo->uniChunkDoubleList = pChunk->pNextChunk;
    } else {
        DbDynUniChunkT *prevChunk = (DbDynUniChunkT *)(pChunk->pPrevChunk);
        prevChunk->pNextChunk = pChunk->pNextChunk;
    }
    if (pChunk->pNextChunk != NULL) {
        DbDynUniChunkT *nextChunk = (DbDynUniChunkT *)(pChunk->pNextChunk);
        nextChunk->pPrevChunk = pChunk->pPrevChunk;
    }
    pDynMemAlgo->uniChunkNum--;
    pChunk->pNextChunk = NULL;
    pChunk->pPrevChunk = NULL;
}

static inline void FreeAllUniChunks(DbDynamicAlgoT *pDynamicAlgo)
{
    DbDynUniChunkT *pCurrChunk = pDynamicAlgo->uniChunkDoubleList;
    DbDynUniChunkT *pNextChunk = NULL;
    while (pCurrChunk != NULL) {
        pNextChunk = pCurrChunk->pNextChunk;
        UniChunkMemFree((void *)pCurrChunk);
        pCurrChunk = pNextChunk;
    }
    pDynamicAlgo->uniChunkDoubleList = NULL;
    (void)DbAtomicFetchAndSub64(&g_gmdbCurrUsedDynSize, pDynamicAlgo->totalPhySize);
}

#endif

static Status DynAlgoAllocLiteChkThreshold(const DbMemCtxT *ctx, const DbDynamicAlgoT *pDynMemAlgo, uint32_t allocSize)
{
    if ((pDynMemAlgo->totalPhySize + allocSize) > pDynMemAlgo->maxSize) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
            "Ctx %s, id %" PRIu32 " reaches maxSize. "
            "TotalPhySize: %" PRIu64 ", maxSize: %" PRIu64 ", request size: %" PRIu32 "",
            ctx->ctxName, ctx->ctxId, pDynMemAlgo->totalPhySize, pDynMemAlgo->maxSize, (uint32_t)allocSize);
        return GMERR_OUT_OF_MEMORY;
    }
    uint64_t localUsedSize = DbAtomicAdd64(&g_gmdbCurrUsedDynSize, allocSize);
    if (localUsedSize > g_gmdbMaxDynSize) {
        (void)DbAtomicSubAndFetch64(&g_gmdbCurrUsedDynSize, allocSize);
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "LiteCtx dynSize: %" PRIu64 ", max dynSize: %" PRIu64 ".", localUsedSize,
            g_gmdbMaxDynSize);
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

static void InitUniChunkAllocParams(DbMemCtxT *ctx, uint32_t allocSize, UniChunkAllocParamsT *params)
{
    params->chunkHdrSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbDynUniChunkT));
    params->enableMemTrace = CheckEnableMemtrace(ctx);
    if (SECUREC_LIKELY(!params->enableMemTrace)) {
        params->allocSize = allocSize;
        params->actualAllocSize = allocSize + params->chunkHdrSize;  // 关闭二次管理算法，直接累加即可。
    } else {
        // memtrace场景需要保证能从chunk调测头根据偏移算出traceInfo的位置。
        params->allocSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, allocSize);
        params->actualAllocSize =
            params->chunkHdrSize + MAXALIGN(DB_DEFAULT_ALIGN_SIZE, allocSize) + sizeof(DynMemChunkTraceInfoT);
    }
}

void *DynamicAlgoAllocLite(DbMemCtxT *ctx, size_t size)
{
    DB_POINTER(ctx);
    DbDynamicAlgoT *pDynMemAlgo = &((DbDynamicMemCtxT *)ctx)->dynamicAlgo;
    uint32_t allocSize = (uint32_t)size;
#ifndef NDEBUG
    if (allocSize == 0) {
        DB_LOG_DBG_DEBUG("Lite dynamic ctx input alloc size is 0.");
    }
    DynMemAllocAbnormalAlarm(ctx, pDynMemAlgo, allocSize);
#endif
    UniChunkAllocParamsT params = {0};
    InitUniChunkAllocParams(ctx, allocSize, &params);
    // AllocSize inverted.
    if (params.actualAllocSize < allocSize) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Size inverted. size: %" PRIu32 ".", allocSize);
        return NULL;
    }
    // Check ctx threshold and global dyn mem threshold.
    if (DynAlgoAllocLiteChkThreshold(ctx, pDynMemAlgo, params.actualAllocSize) != GMERR_OK) {
        return NULL;
    }
    // Lite dyn ctx mod only record chunkSize in DbDynUniChunkT.
    DbDynUniChunkT *pChunk = (DbDynUniChunkT *)UniChunkMemAlloc(params.actualAllocSize);
    if (pChunk == NULL) {
        (void)DbAtomicSubAndFetch64(&g_gmdbCurrUsedDynSize, params.actualAllocSize);
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "Alloc unichunk. Id %" PRIu32 ", name %s, totalPhySize %" PRIu64 ", allocSize %" PRIu32 ".", ctx->ctxId,
            ctx->ctxName, pDynMemAlgo->totalPhySize, allocSize);
        return NULL;
    }
    if (UpdateDynCtxThresholdParent(ctx, params.actualAllocSize, true) != GMERR_OK) {
        (void)DbAtomicSubAndFetch64(&g_gmdbCurrUsedDynSize, params.actualAllocSize);
        UniChunkMemFree((void *)pChunk);
        return NULL;
    }
    // set chunk meta
    SetDynUniChunk(ctx, pDynMemAlgo, pChunk, &params);
    AddDynUniChunkToList(pDynMemAlgo, pChunk);
    if (SECUREC_UNLIKELY(params.enableMemTrace)) {
        DynMemChunkTraceInfoT *traceInfo = (DynMemChunkTraceInfoT *)((uintptr_t)pChunk + params.chunkHdrSize +
                                                                     MAXALIGN(DB_DEFAULT_ALIGN_SIZE, allocSize));
        LinkDynChunkTraceInfo(ctx, pDynMemAlgo, traceInfo, params.allocSize);
    }

    // update ctx dfx info
    pDynMemAlgo->totalAllocSizeByCtx += params.actualAllocSize;
    pDynMemAlgo->totalPhySize += params.actualAllocSize;
    pDynMemAlgo->currPhySizePeak = DB_MAX(pDynMemAlgo->currPhySizePeak, pDynMemAlgo->totalPhySize);
    ctx->totalAllocSize += params.allocSize;
    ctx->isReset = false;
    DynCtxRecordPeakInfoAlarm(ctx);
    ctx->peakAllocSize = DB_MAX(ctx->peakAllocSize, ctx->totalAllocSize);
    // return chunk ptr
    return (void *)ALLOCCHUNK_DATA(pChunk, params.chunkHdrSize);
}

void DynamicAlgoFreeLite(DbMemCtxT *ctx, void *ptr)
{
    DB_POINTER2(ctx, ptr);
    DbDynamicMemCtxT *pDynMemCtx = (DbDynamicMemCtxT *)ctx;
    DbDynamicAlgoT *pDynMemAlgo = &pDynMemCtx->dynamicAlgo;
    uint32_t chunkHdrSize = MAXALIGN(DB_DEFAULT_ALIGN_SIZE, sizeof(DbDynUniChunkT));
    DbDynUniChunkT *pChunk = (DbDynUniChunkT *)ALLOCCHUNK_HEADER(ptr, chunkHdrSize);
    if (!CheckUniChkValid(ctx, pDynMemAlgo, pChunk)) {
        return;
    }

    uint32_t actualAllocSize = pChunk->size + chunkHdrSize;
    if (SECUREC_UNLIKELY(pChunk->memtraceChunk)) {
        actualAllocSize += sizeof(DynMemChunkTraceInfoT);
    }
    // Update stats
    ctx->totalAllocSize -= pChunk->size;
    pDynMemAlgo->totalAllocSizeByCtx -= actualAllocSize;
    pDynMemAlgo->totalPhySize -= actualAllocSize;
    /* The update threshold here is the subtraction operation and will not exceed the threshold */
    Status ret = UpdateDynCtxThresholdParent(ctx, actualAllocSize, false);
    DB_ASSERT(ret == GMERR_OK);
    RmDynUniChunkFromList(pDynMemAlgo, pChunk);
    if (SECUREC_UNLIKELY(pChunk->memtraceChunk)) {
        DynMemChunkTraceInfoT *traceInfo = (DynMemChunkTraceInfoT *)((uintptr_t)ptr + pChunk->size);
        DelinkChunkTraceInfo(ctx, pDynMemAlgo, traceInfo);
    }

    // free chunk memory
    pChunk->magicNum = MEM_MAGIC_FREE_NUMBER;
    UniChunkMemFree((void *)pChunk);
    (void)DbAtomicFetchAndSub64(&g_gmdbCurrUsedDynSize, actualAllocSize);
}

void DynamicAlgoResetLite(DbMemCtxT *ctx)
{
    DbDynamicMemCtxT *pContext = (DbDynamicMemCtxT *)ctx;
    DbDynamicAlgoT *pDynamicAlgo = &pContext->dynamicAlgo;
    // 释放链表中的所有chunk
    FreeAllUniChunks(pDynamicAlgo);
    // 这个地方更新阈值是减法操作不会超过父节点阈值，逻辑上不会失败。
    Status ret = UpdateDynCtxThresholdParent(ctx, pDynamicAlgo->totalPhySize, false);
    DB_ASSERT(ret == GMERR_OK);
    ctx->totalAllocSize = 0;
    pDynamicAlgo->uniChunkNum = 0;
    pDynamicAlgo->totalAllocSizeByCtx = 0;
    pDynamicAlgo->totalPhySize = 0;
    pDynamicAlgo->currPhySizePeak = 0;
    pDynamicAlgo->allocTimes = 0;
    pDynamicAlgo->freeTimes = 0;
    pDynamicAlgo->traceInfoList = NULL;
#ifndef NDEBUG
    pDynamicAlgo->whenAlloc = 0;
    pDynamicAlgo->allocSizeRate = 0;
    pDynamicAlgo->allocTimesRate = 0;
#endif
}

void DynamicAlgoClearLite(DbMemCtxT *ctx)
{
    DynamicAlgoResetLite(ctx);
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
