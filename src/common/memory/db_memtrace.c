/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name:
 * Description: source file for common memory context
 * Author: zhouhaonan
 * Create: 2024-9-23
 */

#include <execinfo.h>
#include "db_mem_context.h"
#include "db_mem_context_internal.h"
#include "db_dynmem_algo.h"
#include "db_shmem_blockpool_algo.h"
#include "db_mem_log.h"
#include "db_hashmap.h"

#define CALL_STACK_STRING_MAX_LEN 2048
#define CLT_MEMTRACE_STACK_PRINT_NUM 3

typedef struct MemTraceInfoTuple {
    uint64_t notFreedCnt;
    uint64_t totalAllocSize;
    uint32_t hash;
    uint32_t stackSize;
} MemTraceInfoTupleT;

typedef struct MemTraceInfo {
    void *callstack;
    MemTraceInfoTupleT *traceInfo;
} MemTraceInfoT;

static void InitTraceInfoTuple(
    MemTraceInfoTupleT *newTraceInfoTuple, uint64_t chunkSize, uint32_t hash, uint32_t stackSize)
{
    newTraceInfoTuple->hash = hash;
    newTraceInfoTuple->notFreedCnt = 1;
    newTraceInfoTuple->totalAllocSize = chunkSize;
    newTraceInfoTuple->stackSize = stackSize;
}

static void UpdateTraceInfoTuple(MemTraceInfoTupleT *traceInfoTuple, uint64_t chunkSize)
{
    (traceInfoTuple->notFreedCnt)++;
    traceInfoTuple->totalAllocSize += chunkSize;
}

static Status PutMemTraceInfoIntoMap(void *pCallStack, uint32_t stackSize, uint64_t chunkSize, DbOamapT *map)
{
    uint32_t stackMemSize = stackSize * sizeof(uintptr_t);
    uint32_t hash = DbHash32((uint8_t *)pCallStack, stackMemSize);
    MemTraceInfoTupleT *traceInfoTuple = DbOamapLookup(map, hash, pCallStack, NULL);
    if (traceInfoTuple == NULL) {
        // 初次插入需要申请键值对内存
        MemTraceInfoTupleT *newTraceInfoTuple = DB_MALLOC(sizeof(MemTraceInfoTupleT));
        if (SECUREC_UNLIKELY(newTraceInfoTuple == NULL)) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc new tace info.");
            return GMERR_OUT_OF_MEMORY;
        }
        uint8_t *stackKey = (uint8_t *)DB_MALLOC(stackMemSize);
        if (SECUREC_UNLIKELY(stackKey == NULL)) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc new tace info.");
            DB_FREE(newTraceInfoTuple);
            return GMERR_OUT_OF_MEMORY;
        }
        // 后续释放memCtx锁，为保证并发使用深拷贝。
        (void)memcpy_s(stackKey, stackMemSize, pCallStack, stackMemSize);
        InitTraceInfoTuple(newTraceInfoTuple, chunkSize, hash, stackSize);
        Status ret = DbOamapInsert(map, hash, stackKey, newTraceInfoTuple, NULL);
        if (ret != GMERR_OK) {
            DB_FREE(stackKey);
            DB_FREE(newTraceInfoTuple);
            DB_LOG_ERROR(ret, "Unable to put chunk trace info into map.");
            return ret;
        }
    } else {
        DB_ASSERT(stackSize == traceInfoTuple->stackSize);
        UpdateTraceInfoTuple(traceInfoTuple, chunkSize);
    }
    return GMERR_OK;
}

static void ReleaseTraceInfoMap(DbOamapT *map)
{
    DbOamapIteratorT iter = 0;
    void *key = NULL;
    void *value = NULL;
    while (DbOamapFetch(map, &iter, &key, &value) == GMERR_OK) {
        DB_FREE(key);
        DB_FREE(value);
    }
    DbOamapDestroy(map);
    DB_FREE(map);
}

static void CollectDynMemTraceInfo(DbMemCtxT *ctx, DbOamapT *map)
{
    DbDynamicAlgoT *pDynMemAlgo = &((DbDynamicMemCtxT *)ctx)->dynamicAlgo;
    DynMemChunkTraceInfoT *currTrace = pDynMemAlgo->traceInfoList;
    while (currTrace != NULL) {
        if (PutMemTraceInfoIntoMap(currTrace->callStack, currTrace->stackSize, currTrace->size, map) != GMERR_OK) {
            break;
        }
        currTrace = currTrace->next;
    }
}

// 共享内存memtrace目前只在编译宏下生效。
#ifdef DB_MEM_TRACE
static void CollectShmemTraceInfo(DbMemCtxT *ctx, DbOamapT *map)
{
    DB_ASSERT(ctx->methodType == ALGO_BLOCK);
    Status ret = GMERR_OK;
    uint32_t pid = DbAdptGetpid();
    DbShmemBlockAlgoT *pShmemBlockAlgo = &((DbBlockShmemCtxT *)(void *)ctx)->shmemBlockAlgo;
    ShmemPtrT shmPtrChunk = pShmemBlockAlgo->allocChunks;
    while (DbIsShmPtrValid(shmPtrChunk)) {
        ShmChunkTraceInfoT *pChunkTraceInfo = (ShmChunkTraceInfoT *)(DbShmPtrToAddr(shmPtrChunk));
        if (SECUREC_UNLIKELY(pChunkTraceInfo == NULL)) {
            return;
        }
        // Only examine those shmem chunks allocated by current process
        if (pChunkTraceInfo->pid == pid) {
            uint64_t chunkSize = pChunkTraceInfo->size;
            ret = PutMemTraceInfoIntoMap(pChunkTraceInfo->callStack, pChunkTraceInfo->stackSize, chunkSize, map);
            if (ret != GMERR_OK) {
                return;
            }
        }
        shmPtrChunk = pChunkTraceInfo->nextChunk;
    }
}
#else
static void CollectShmemTraceInfo(DbMemCtxT *ctx, DbOamapT *map)
{
    DB_UNUSED(ctx);
    DB_UNUSED(map);
    return;
}
#endif

static void CollectMemTraceInfo(DbMemCtxT *ctx, DbOamapT *map)
{
    if (ctx->methodType == ALGO_DYNAMIC) {
        CollectDynMemTraceInfo(ctx, map);
    } else if (ctx->methodType == ALGO_BLOCK) {
        CollectShmemTraceInfo(ctx, map);
    }
}

DbMemCtxT *MemCtxFetchDynCtx(uint32_t *iter)
{
    DbMemCtxGlobalLock();
    DynMemCtxStatInfoT *ctxStatInfo = DbGetDynCtxStatInfo();
    DbMemCtxT *ctx = NULL;
    while (*iter < ctxStatInfo->currCtxArrayLen) {
        ctx = ctxStatInfo->pCtxArray[(*iter)++];
        if (ctx != NULL && ctx->magicCode == DB_MAGIC_CODE) {
            break;
        }
    }
    DbMemCtxGlobalUnlock();
    return ctx;
}

static void UpdTopTraceInfoArr(MemTraceInfoT *info, MemTraceInfoT *arr)
{
    uint32_t idx = DB_INVALID_UINT32;
    for (uint32_t i = 0; i < CLT_MEMTRACE_STACK_PRINT_NUM; i++) {
        if (arr[i].traceInfo != NULL) {
            if (info->traceInfo->notFreedCnt >= arr[i].traceInfo->notFreedCnt) {
                idx = i;
            } else {
                break;
            }
        } else {
            arr[i] = *info;
            idx = DB_INVALID_UINT32;
            break;
        }
    }
    if (idx != DB_INVALID_UINT32) {
        for (uint32_t i = idx; i > 0; i--) {
            arr[i - 1] = arr[i];
        }
        arr[idx] = *info;
    }
}

static void PrintCallStack(void *callStack, uint32_t stackSize)
{
    char **stackTrace = backtrace_symbols(callStack, stackSize);
    if (stackTrace == NULL) {
        return;
    }
    for (uint32_t i = 0; i < stackSize; i++) {
        if (stackTrace[i] != NULL) {
            DB_LOG_WARN_UNFOLD(GMERR_OK, "|STACK-%" PRId32 "|:%s", i, stackTrace[i]);
#if (defined HPE || defined RTOSV2 || defined RTOSV2X)
            WRITE_LOG_FILE("|STACK-%" PRId32 "|:%s\n", i, stackTrace[i]);
#endif
        }
    }
    DbAdptDynamicMemFree(stackTrace);
}

static void PrintTopDynCallStack(DbOamapT *map)
{
    MemTraceInfoT infoArr[CLT_MEMTRACE_STACK_PRINT_NUM] = {0};
    uint32_t iter = 0;
    void *key, *value;
    while (DbOamapFetch(map, &iter, &key, &value) == GMERR_OK) {
        MemTraceInfoT info = {.callstack = key, .traceInfo = (MemTraceInfoTupleT *)value};
        UpdTopTraceInfoArr(&info, infoArr);
    }
    for (uint32_t i = 0; i < CLT_MEMTRACE_STACK_PRINT_NUM; i++) {
        if (infoArr[i].traceInfo == NULL) {
            continue;
        }
        // str is malloced inside.
        PrintCallStack(infoArr[i].callstack, infoArr[i].traceInfo->stackSize);

        DB_LOG_WARN_UNFOLD(GMERR_OK, "NOT_FREED_COUNT: %" PRIu64 ".", infoArr[i].traceInfo->notFreedCnt);
        DB_LOG_WARN_UNFOLD(GMERR_OK, "TOTAL_ALLOC_SIZE: %" PRIu64 ".", infoArr[i].traceInfo->totalAllocSize);
#if (defined HPE || defined RTOSV2 || defined RTOSV2X)
        WRITE_LOG_FILE("NOT_FREED_COUNT: %" PRIu64 ".\n", infoArr[i].traceInfo->notFreedCnt);
        WRITE_LOG_FILE("TOTAL_ALLOC_SIZE: %" PRIu64 ".\n", infoArr[i].traceInfo->totalAllocSize);
#endif
    }
}

static bool CheckMemtraceEnable(void)
{
    if (g_gmdbOnlineMemtraceFlag == 0) {
        return false;
    }
    if (g_gmdbOnlineMemtracePid == DB_INVALID_UINT32) {
        return true;
    } else {
        return DbAdptGetpid() == g_gmdbOnlineMemtracePid;
    }
}

void DbDynMemCtxPrintTopCallStack(void)
{
    if (!CheckMemtraceEnable()) {
        return;
    }
    DbOamapT *map = (DbOamapT *)DB_MALLOC(sizeof(DbOamapT));
    if (SECUREC_UNLIKELY(map == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc memtrace info map.");
        return;
    }
    Status ret = DbOamapInit(map, DB_MEMTRACE_MAP_SIZE, DbOamapStringCompare, NULL, true);  // 128: the init size of map
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Init memtrace info map.");
        DB_FREE(map);
        return;
    }
    uint32_t iter = 0;
    DbMemCtxT *ctx = NULL;
    while ((ctx = MemCtxFetchDynCtx(&iter)) != NULL) {
        if ((ret = DbMemCtxLock(ctx)) != GMERR_OK) {
            continue;
        }
        CollectMemTraceInfo(ctx, map);
        DbMemCtxUnLock(ctx);
    }
    PrintTopDynCallStack(map);
    ReleaseTraceInfoMap(map);
}

#ifdef DB_MEM_TRACE

static char *ConvertStackPtrArr2String(void *callStack, uint8_t stackSize)
{
    char **stackTrace = backtrace_symbols(callStack, stackSize);
    if (stackTrace == NULL) {
        return NULL;
    }
    char *str = DB_MALLOC(CALL_STACK_STRING_MAX_LEN);
    if (str == NULL) {
        /* DB_FREE can not be used in here due to extra 'size head' */
        DbAdptDynamicMemFree(stackTrace);
        return NULL;
    }
    int32_t len = sprintf_s(str, CALL_STACK_STRING_MAX_LEN, "\n");
    if (len < 0) {
        DbAdptDynamicMemFree(stackTrace);
        return str;
    }
    for (uint32_t i = 1; i < stackSize; i++) {
        len = sprintf_s(str, CALL_STACK_STRING_MAX_LEN, "%s\t%s\n", str, stackTrace[i]);
        if (len < 0) {
            break;
        }
    }
    DbAdptDynamicMemFree(stackTrace);
    return str;
}

static void PrintAllStack(DbOamapT *map)
{
    DbOamapIteratorT iter = 0;
    void *key = NULL;
    void *value = NULL;
    while (DbOamapFetch(map, &iter, &key, &value) == GMERR_OK) {
        MemTraceInfoTupleT *valueTuple = (MemTraceInfoTupleT *)value;
        // str is malloced inside.
        char *str = ConvertStackPtrArr2String(key, valueTuple->stackSize);
        if (SECUREC_UNLIKELY(str == NULL)) {
            continue;
        }
        WRITE_LOG_FILE("----------\n");
        WRITE_LOG_FILE("CALL_STACK");
        WRITE_LOG_FILE("%s\n", (char *)str);
        DB_FREE(str);

        WRITE_LOG_FILE("NOT_FREED_COUNT: %" PRIu64 ".\n", valueTuple->notFreedCnt);
        WRITE_LOG_FILE("TOTAL_ALLOC_SIZE: %" PRIu64 ".\n", valueTuple->totalAllocSize);
        WRITE_LOG_FILE("STACK_HASH: %" PRIu32 ".\n", valueTuple->hash);
        WRITE_LOG_FILE("----------\n");
    }
}

void DbMemCtxPrintAllCallStack(DbMemCtxT *ctx, bool ctxLock)
{
    if (ctx == NULL || ctx->magicCode != DB_MAGIC_CODE || ctx->memType != DB_DYNAMIC_MEMORY) {
        WRITE_LOG_FILE("Unable to print memCtx callStack, input memCtx inv.");
        return;
    }
    Status ret;
    if (ctxLock && ((ret = DbMemCtxLock(ctx)) != GMERR_OK)) {
        return;
    }
    DbOamapT *map = (DbOamapT *)DB_MALLOC(sizeof(DbOamapT));
    if (map == NULL) {
        WRITE_LOG_FILE("Unable to alloc Memory trace info map.");
        goto EXIT;
    }
    ret = DbOamapInit(map, 128, DbOamapStringCompare, NULL, true);  // 128: the init size of map
    if (ret != GMERR_OK) {
        WRITE_LOG_FILE("Unable to init Memory trace info map.");
        DB_FREE(map);
        goto EXIT;
    }

    CollectMemTraceInfo(ctx, map);

    WRITE_LOG_FILE("============================");
    WRITE_LOG_FILE("CTX_NAME: %s", ctx->ctxName);
    WRITE_LOG_FILE("CTX_ID: %u", ctx->ctxId);
    if (ctxLock) {
        DbMemCtxUnLock(ctx);
    }
    PrintAllStack(map);
    ReleaseTraceInfoMap(map);
EXIT:
    if (ctxLock) {
        DbMemCtxUnLock(ctx);
    }
}

#endif
