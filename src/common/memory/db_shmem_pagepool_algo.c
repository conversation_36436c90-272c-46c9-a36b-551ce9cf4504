/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: db_shmem_pagepool_algo.c
 * Description: source file for common shared memory page pool algorithm
 * Author: wanglei
 * Create: 2020-8-24
 */

#include "db_shmem_pagepool_algo.h"
#include <securec.h>
#include "db_top_shmem_ctx.h"
#include "db_mem_log.h"

static const DbShmemCtxMethodsT g_gmdbShmemPagePoolAlgoMethods = {ShmemPagePoolAlgoAlloc, ShmemPagePoolAlgoFree,
    ShmemPagePoolAlgoReset, ShmemPagePoolAlgoClear, ShmemPagePoolAlgoDestroy, ShmemPagePoolAlgoDestroyForce,
    ShmemPagePoolIsEmpty, ShmemPagePoolGetPhySizePeak, ShmemPagePoolResetPhySizePeak, ShmemPagePoolGetAndResetPeak};

const DbShmemCtxMethodsT *GetShmemPagePoolAlgoMethods(void)
{
    return &g_gmdbShmemPagePoolAlgoMethods;
}

static Status CheckPagePoolAlgoParm(const DbPageMemParamT *algoParam)
{
    if (algoParam->baseNum == 0 || algoParam->maxNum == 0 || algoParam->pageSize == 0 || algoParam->stepNum == 0 ||
        algoParam->maxNum < algoParam->baseNum) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "PagePoolAlgo para, basenum: %" PRIu32 ", maxnum:%" PRIu32 ", pageSize:%" PRIu32 ", stepNum:%" PRIu32 ".",
            algoParam->baseNum, algoParam->maxNum, algoParam->pageSize, algoParam->stepNum);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static void InitPagePoolAttr(DbShmemPagePoolAlgoT *pShmemPagePoolAlgo, DbMemCtxT *curCtx, const AlgoParamT *algoParam)
{
    pShmemPagePoolAlgo->attr.pageSize = algoParam->pageParam->pageSize;
    pShmemPagePoolAlgo->attr.baseNum = algoParam->pageParam->baseNum;
    pShmemPagePoolAlgo->attr.maxNum = algoParam->pageParam->maxNum;
    pShmemPagePoolAlgo->attr.stepNum = algoParam->pageParam->stepNum;
    pShmemPagePoolAlgo->attr.groupId = curCtx->groupId;
    pShmemPagePoolAlgo->attr.maxTotalPhySize = curCtx->maxTotalPhySize;
    pShmemPagePoolAlgo->attr.isHugePage = algoParam->pageParam->isHugePage;
    pShmemPagePoolAlgo->attr.permission = algoParam->pageParam->permission;  // Passing the permission in HPE scenario
    pShmemPagePoolAlgo->attr.instanceId = DbGetProcGlobalId();
    pShmemPagePoolAlgo->attr.staticCtx = curCtx->ctxId < DB_START_SPECIAL_CTX_ID;
}

Status ShmemPagePoolAlgoInit(DbMemCtxT *curCtx, const AlgoParamT *algoParam)
{
    /* system built-in algorithms are not required udAlgoParam */
    DB_POINTER3(curCtx, algoParam, algoParam->pageParam);

    // 检查pagepool的算法参数是否合法。这是最外层的初始化函数，在此处判断完，下层的算法层就不用重复判断这些参数的合法性
    Status ret = CheckPagePoolAlgoParm(algoParam->pageParam);
    if (ret != GMERR_OK) {
        return ret;
    }
    curCtx->methodType = ALGO_PAGE_POOL;
    DbShmemPagePoolAlgoT *pShmemPagePoolAlgo = &((DbPagePoolShmemCtxT *)curCtx)->shmemPagePoolAlgo;
    pShmemPagePoolAlgo->key = ConstructPoolKeyByCtxId(curCtx->instanceId, curCtx->ctxId);
    /* init page pool parameters */
    InitPagePoolAttr(pShmemPagePoolAlgo, curCtx, algoParam);
    pShmemPagePoolAlgo->allocCount = 0;
    pShmemPagePoolAlgo->freeCount = 0;
    pShmemPagePoolAlgo->pagePoolShmPtr = DbAdptPagePoolCreate(pShmemPagePoolAlgo->key, &pShmemPagePoolAlgo->attr);
    if (SECUREC_UNLIKELY(!DbIsShmPtrValid(pShmemPagePoolAlgo->pagePoolShmPtr))) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Create pagePoolMgr.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if ((ret = InsertShmemPtMap(curCtx)) != GMERR_OK) {
        goto EXIT;
    }
    /* Add ctx to shmem group */
    if ((ret = DbAdptAddMember2ShmemGroup(pShmemPagePoolAlgo->pagePoolShmPtr, curCtx->groupId)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "Add ctx %s to shm group %" PRIu64 ".", curCtx->ctxName, curCtx->groupId);
        goto EXIT;
    }
    MEM_RUN_LINK_LOG(MEM_RUN_LINK_LOG_SHMEMCTX,
        "Create shm. Id %" PRIu32 ", name: %s, ptNo %" PRIu32 ", key %" PRIu32 ". DB attachCnt %" PRIu32 ".",
        curCtx->ctxId, curCtx->ctxName, ShmemCtxGetPtNo(curCtx),
        ConstructPoolKeyByCtxId(curCtx->instanceId, curCtx->ctxId), curCtx->attachCount);
    return ret;
EXIT:
    // 此时没有客户端attach，保证删除成功。
    (void)DbAdptPagePoolDestroy(pShmemPagePoolAlgo->pagePoolShmPtr);
    return ret;
}

// ShmemPagePoolAlgoInit初始化成功，若后续流程失败，需要释放资源。
void ShmemPagePoolAlgoUnInit(DbMemCtxT *curCtx)
{
    DbShmemPagePoolAlgoT *pShmemPagePoolAlgo = &((DbPagePoolShmemCtxT *)curCtx)->shmemPagePoolAlgo;
    // 此时没有客户端attach，保证删除成功。
    (void)DbAdptPagePoolDestroy(pShmemPagePoolAlgo->pagePoolShmPtr);
}

static Status InitDefaultPgePoolAlgoParam(DbMemCtxArgsT *args)
{
    if (args->algoParam != NULL) {
        return GMERR_OK;
    }
    AlgoParamT *algoParam = (AlgoParamT *)DB_MALLOC(sizeof(AlgoParamT));
    if (algoParam == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Init shm algoParam, size: %zu.", sizeof(AlgoParamT));
        return GMERR_OUT_OF_MEMORY;
    }
    DbPageMemParamT *pagePoolParam = (DbPageMemParamT *)DB_MALLOC(sizeof(DbPageMemParamT));
    if (pagePoolParam == NULL) {
        DB_FREE(algoParam);
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Init shm pageAlgoPara, size: %zu.", sizeof(DbPageMemParamT));
        return GMERR_OUT_OF_MEMORY;
    }
    args->algoParam = algoParam;
    args->algoParam->pageParam = pagePoolParam;
    pagePoolParam->isHugePage = false;
    pagePoolParam->baseNum = PAGEPOOL_DEFAULT_BASENUM;
    pagePoolParam->maxNum = PAGEPOOL_DEFAULT_MAXNUM;
    pagePoolParam->stepNum = PAGEPOOL_DEFAULT_STEPNUM;
    pagePoolParam->permission = PAGEPOOL_DEFAULT_PERMISSION;
    pagePoolParam->pageSize = PAGEPOOL_DEFAULT_PAGESIZE;
    return GMERR_OK;
}

static void DestoryPageAlgoParam(DbMemCtxArgsT *ctxArgs, bool isUserConfig)
{
    if (!isUserConfig) {
        DB_FREE(ctxArgs->algoParam->pageParam);
        DB_FREE(ctxArgs->algoParam);
        ctxArgs->algoParam = NULL;
    }
}

void *DbCreatePagePoolShmemCtx(DbMemCtxT *parent, const char *ctxName, DbMemCtxArgsT *args)
{
    // Check the ctx deleteMgr first and delete what is ready to delete.
    // This would release ctx pool and recycle ctxId.
    // DbShmemCtxGlobalLock also performed inside.
    DbInstanceHdT dbInstance = parent->dbIns;
    DbShmemCtxDetachMgrScanSrv(dbInstance);

    if (SECUREC_UNLIKELY(args == NULL)) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Inv args when to create pagePool memctx.");
        return NULL;
    }
    DbShmemCtxGlobalLock();
    Status ret = CheckShmemCtxValid(parent);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Inv parent ctx(create pagePoolCtx).");
        goto EXIT;
    }

    if (!IsShmemCtxIdValid(args->ctxId, parent->instanceId)) {
        goto EXIT;
    }
    args->init = ShmemPagePoolAlgoInit;
    args->uninit = ShmemPagePoolAlgoUnInit;
    args->ctxSize = (uint32_t)sizeof(DbPagePoolShmemCtxT);
    args->algoType = ALGO_PAGE_POOL;
    args->memType = DB_SHARED_MEMORY;  // must be shared memory type
    args->instanceId = parent->instanceId;

    // Init default pagePool params if user does not define them
    bool isUserConfig = (args->algoParam != NULL);
    if (InitDefaultPgePoolAlgoParam(args) != GMERR_OK) {
        goto EXIT;
    }

    DbMemCtxT *poolShmemCtx = (DbMemCtxT *)CreateMemCtx(parent, ctxName, true, args);
    DestoryPageAlgoParam(args, isUserConfig);
    DbShmemCtxGlobalUnlock();
    if (poolShmemCtx == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Create pagePoolCtx struct.");
        return poolShmemCtx;
    }
    // register ctxid and shmempointer to top memory context
    if (RegCtxIdToTopShmCtx(poolShmemCtx->ctxId, poolShmemCtx->shmPtrSelf, args->instanceId) != GMERR_OK) {
        DbDeleteShmemCtx(poolShmemCtx);
        return NULL;
    }
    return (void *)poolShmemCtx;
EXIT:
    DbShmemCtxGlobalUnlock();
    return NULL;
}

ShmemPtrT ShmemPagePoolAlgoAlloc(DbMemCtxT *curCtx, size_t size)
{
    DB_POINTER(curCtx);
    (void)size;
    DbShmemPagePoolAlgoT *pShmemPagePoolAlgo = &((DbPagePoolShmemCtxT *)curCtx)->shmemPagePoolAlgo;
    uint32_t pageSize = pShmemPagePoolAlgo->attr.pageSize;
    ShmemPtrT newShmemPtr = DbAdptPagePoolAllocPage(pShmemPagePoolAlgo->pagePoolShmPtr, pageSize);
    if (DbIsShmPtrValid(newShmemPtr)) {
        (void)DbAtomicInc(&(pShmemPagePoolAlgo->allocCount));
        curCtx->isReset = false;
    } else {
        uint64_t totalPhySize = DbAdptPagePoolGetTotalPhySize(pShmemPagePoolAlgo->pagePoolShmPtr);
        PagePoolAttributeT attr = pShmemPagePoolAlgo->attr;
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "Alloc from pagePool %s. CtxId: %" PRIu32 ", totalPhySize: %" PRIu64 ", "
            "allocSize: %" PRIu32 "; baseNum: %" PRIu32 ", stepNum: %" PRIu32 ", maxNum: %" PRIu32 ".",
            curCtx->ctxName, curCtx->ctxId, totalPhySize, attr.pageSize, attr.baseNum, attr.stepNum, attr.maxNum);
    }
    return newShmemPtr;
}

Status ShmemPagePoolAlgoFree(DbMemCtxT *curCtx, ShmemPtrT ptr)
{
    DB_POINTER(curCtx);
    DbShmemPagePoolAlgoT *pShmemPagePoolAlgo = &((DbPagePoolShmemCtxT *)curCtx)->shmemPagePoolAlgo;
    Status ret = DbAdptPagePoolFreePage(pShmemPagePoolAlgo->pagePoolShmPtr, ptr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Free page, ctxId: %" PRIu32 ", ctxName: %s, segId: %" PRIu32 " offset: %" PRIu32 ".",
            curCtx->ctxId, curCtx->ctxName, ptr.segId, ptr.offset);
        return ret;
    }
    (void)DbAtomicInc(&(pShmemPagePoolAlgo->freeCount));
    return GMERR_OK;
}

// 调用钩子函数前已对ctx加锁。
void ShmemPagePoolAlgoReset(DbMemCtxT *curCtx)
{
    DB_POINTER(curCtx);
    DbShmemPagePoolAlgoT *pShmemPagePoolAlgo = &((DbPagePoolShmemCtxT *)curCtx)->shmemPagePoolAlgo;
    DbAdptPagePoolReset(pShmemPagePoolAlgo->pagePoolShmPtr);

    pShmemPagePoolAlgo->freeCount = 0;
    pShmemPagePoolAlgo->allocCount = 0;
    ((DbMemCtxT *)curCtx)->totalAllocSize = 0;
}

// 调用钩子函数前已对ctx加锁。
Status ShmemPagePoolAlgoClear(DbMemCtxT *curCtx)
{
    DB_POINTER(curCtx);
    DbShmemPagePoolAlgoT *pShmemPagePoolAlgo = &((DbPagePoolShmemCtxT *)curCtx)->shmemPagePoolAlgo;
    Status ret = DbAdptPagePoolClear(pShmemPagePoolAlgo->pagePoolShmPtr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Clear shmCtx, ctxId %" PRIu32 ", ctxName %s.", curCtx->ctxId, curCtx->ctxName);
        return ret;
    }

    pShmemPagePoolAlgo->freeCount = 0;
    pShmemPagePoolAlgo->allocCount = 0;
    ((DbMemCtxT *)curCtx)->totalAllocSize = 0;
    return ret;
}

Status ShmemPagePoolAlgoDestroy(DbMemCtxT *curCtx)
{
    DB_POINTER(curCtx);
    DbShmemPagePoolAlgoT *pShmemPagePoolAlgo = &((DbPagePoolShmemCtxT *)curCtx)->shmemPagePoolAlgo;
    return DbAdptPagePoolDestroy(pShmemPagePoolAlgo->pagePoolShmPtr);
}

void ShmemPagePoolAlgoDestroyForce(DbMemCtxT *curCtx)
{
    DB_POINTER(curCtx);
    DbShmemPagePoolAlgoT *pShmemPagePoolAlgo = &((DbPagePoolShmemCtxT *)curCtx)->shmemPagePoolAlgo;
    DbAdptPagePoolDestroyForce(pShmemPagePoolAlgo->pagePoolShmPtr);
}

Status ShmemPagePoolIsEmpty(DbMemCtxT *curCtx, bool *isEmpty)
{
    DB_POINTER(curCtx);
    DbShmemPagePoolAlgoT *pShmemPagePoolAlgo = &((DbPagePoolShmemCtxT *)curCtx)->shmemPagePoolAlgo;
    *isEmpty = (pShmemPagePoolAlgo->allocCount == pShmemPagePoolAlgo->freeCount) ? true : false;
    return GMERR_OK;
}

DbPageMemParamT DbShmCtxGetPagePoolParams(const DbMemCtxT *ctx)
{
    DB_POINTER(ctx);
    DB_ASSERT(ctx->methodType == ALGO_PAGE_POOL);
    const DbShmemPagePoolAlgoT *pShmemPagePoolAlgo =
        &((const DbPagePoolShmemCtxT *)(const void *)ctx)->shmemPagePoolAlgo;
    DbPageMemParamT params = {0};
    params.pageSize = pShmemPagePoolAlgo->attr.pageSize;
    params.isHugePage = pShmemPagePoolAlgo->attr.isHugePage;
    params.baseNum = pShmemPagePoolAlgo->attr.baseNum;
    params.stepNum = pShmemPagePoolAlgo->attr.stepNum;
    params.maxNum = pShmemPagePoolAlgo->attr.maxNum;
    return params;
}

// 上层调用者加锁
uint64_t ShmemPagePoolGetPhySizePeak(DbMemCtxT *ctx)
{
    DbPagePoolShmemCtxT *pCtx = (DbPagePoolShmemCtxT *)ctx;
    DbShmemPagePoolAlgoT *pShmemBlockAlgo = &pCtx->shmemPagePoolAlgo;
    return DbAdptGetPagePoolPhySizePeak(pShmemBlockAlgo->pagePoolShmPtr);
}

// 上层调用者加锁
void ShmemPagePoolResetPhySizePeak(DbMemCtxT *ctx)
{
    DbPagePoolShmemCtxT *pCtx = (DbPagePoolShmemCtxT *)ctx;
    DbShmemPagePoolAlgoT *pShmemBlockAlgo = &pCtx->shmemPagePoolAlgo;
    DbAdptPagePoolResetPhySizePeak(pShmemBlockAlgo->pagePoolShmPtr);
}

uint64_t ShmemPagePoolGetAndResetPeak(DbMemCtxT *ctx)
{
    DbPagePoolShmemCtxT *pCtx = (DbPagePoolShmemCtxT *)ctx;
    DbShmemPagePoolAlgoT *pShmemBlockAlgo = &pCtx->shmemPagePoolAlgo;
    return DbAdptPagePoolGetAndResetPeak(pShmemBlockAlgo->pagePoolShmPtr);
}
