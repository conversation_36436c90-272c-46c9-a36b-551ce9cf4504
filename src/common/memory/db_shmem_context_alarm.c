/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: db_shmem_context_alarm.c
 * Description: defines shared memory alarm methods.
 * Author: mem team
 * Create: 2025-3-5
 */

#include "db_mem_context_internal.h"
#include "db_top_shmem_ctx.h"
#include "db_alarm.h"
#include "db_config.h"

typedef struct TopUseShmemCtxItem {
    uint32_t ctxId;
    uint64_t totalPhySize;
} TopUseShmemCtxItemT;

Status DbGetNonSeShmUsedInfoAlarmGetInfo(
    double *useRatio, uint64_t *totalPhySize, uint64_t *sePhySize, uint64_t *serverCfgMaxSize, uint64_t *seCfgMaxSize)
{
    // 多实例场景不开启
    if (DbIsMultiInstanceEnabled()) {
        return GMERR_OK;
    }
    *totalPhySize = DbShmCtxGetTreePhySize(DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, DbGetProcGlobalId()), true);
    DbMemCtxT *memCtx = DbGetShmemCtxById(DB_SE_DEV_SHMEMCTX_ID, DbGetProcGlobalId());
    Status ret = CheckShmemCtxValid(memCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get non se alarm info.");
        return ret;
    }
    *sePhySize = DbShmCtxGetTotalPhySize(memCtx, true);
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);
    DbCfgValueT cfgValue;
    // serverCfgMaxSize为server配置总的共享内存大小， 获取到的内存大小为M，需要换算为字节。
    ret = DbCfgGet(cfgHandle, DB_CFG_SERVER_TOTAL_SHM_SIZE, &cfgValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    *serverCfgMaxSize = (uint64_t)(uint32_t)cfgValue.int32Val * DB_MEBI;
    // seCfgMaxSize为存储引擎配置的可用共享内存大小，获取到的内存大小为M，需要换算为字节。
    ret = DbCfgGet(cfgHandle, DB_CFG_SE_MAX_MEM, &cfgValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    *seCfgMaxSize = (uint64_t)(uint32_t)cfgValue.int32Val * DB_MEBI;

    double nonSeUsedSize = (double)(*totalPhySize - *sePhySize);
    double nonSeMaxSize = (double)(*serverCfgMaxSize - *seCfgMaxSize);
    // serverCfgMaxSize == seCfgMaxSize, 说明共享内存全部分配给se用，非se无共享内存可用，会触发warning。
    if (*serverCfgMaxSize == *seCfgMaxSize) {
        *useRatio = 1.0;
        return GMERR_OK;
    }
    *useRatio = nonSeUsedSize / nonSeMaxSize;
    return GMERR_OK;
}

Status DbGetNonSeShmUsedInfoAlarmWithoutLog(double *useRatio)
{
    DB_POINTER(useRatio);
    uint64_t totalPhySize = 0;
    uint64_t sePhySize = 0;
    uint64_t serverCfgMaxSize = 0;
    uint64_t seCfgMaxSize = 0;
    return DbGetNonSeShmUsedInfoAlarmGetInfo(useRatio, &totalPhySize, &sePhySize, &serverCfgMaxSize, &seCfgMaxSize);
}

// 计算warning指标：DB_ALARM_SHM_USED_INFO, 非se实际占用共享内存与非se配置的可用的共享内存比率。
// 存在问题：目前配置共享内存总大小的宏（DB_CFG_SERVER_TOTAL_SHM_SIZE）实际并没有使用，无法限制系统总共享内存上限。
Status DbGetNonSeShmUsedInfoAlarm(double *useRatio)
{
    DB_POINTER(useRatio);
    // 多实例场景不开启
    if (DbIsMultiInstanceEnabled()) {
        return GMERR_OK;
    }
    uint64_t totalPhySize = 0;
    uint64_t sePhySize = 0;
    uint64_t serverCfgMaxSize = 0;
    uint64_t seCfgMaxSize = 0;
    Status ret =
        DbGetNonSeShmUsedInfoAlarmGetInfo(useRatio, &totalPhySize, &sePhySize, &serverCfgMaxSize, &seCfgMaxSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 如果使用率大于alarm消除阈值，则可能属于共享内存使用即将触发0号alarm/处于0号alarm未消除的状态，
    // 打印内存占有量最多的10个ctx信息。每300秒扫描一次，不会导致刷屏和影响性能。
    DbAlarmHeadT *alarmHead = DbGetAlarmHead(DB_ALARM_SHM_USED_INFO);
    DB_POINTER(alarmHead);
    if (*useRatio > alarmHead->alarmCfg->cfg.threshold.clearPercent) {
        DB_LOG_WARN(GMERR_INSUFFICIENT_RESOURCES,
            "MEM: Short on shmem resource. CtxNum %" PRIu32 ", globalPhySize: %" PRIu64 ", sePhySize: %" PRIu64 ", "
            "serverCfgMaxSize: %" PRIu64 ", seCfgMaxSize: %" PRIu64 ". Use ratio %lf, clear per %lf.",
            DbGetMemCtxNum(DB_SHARED_MEMORY), totalPhySize, sePhySize, serverCfgMaxSize, seCfgMaxSize, *useRatio,
            alarmHead->alarmCfg->cfg.threshold.clearPercent);
        // 该函数会对所有memCtx的内存使用作排序，为耗时操作且在全局锁内。性能较差场景不启动该DFX功能。
#if (!defined(ARM32) || defined(NDEBUG))
        DbPrintShmemAlarmLog();
#endif
    }
    return GMERR_OK;
}

static int32_t ShmemCtxTotalPhySizeCmp(const void *item1, const void *item2)
{
    TopUseShmemCtxItemT value1 = *(const TopUseShmemCtxItemT *)item1;
    TopUseShmemCtxItemT value2 = *(const TopUseShmemCtxItemT *)item2;
    // 防止int类型相减反转。
    if (value2.totalPhySize > value1.totalPhySize) {
        return 1;
    } else if (value2.totalPhySize == value1.totalPhySize) {
        return 0;
    } else {
        return -1;
    }
}

static void AlarmLogShmemCtx(const TopUseShmemCtxItemT *shmemCtxArray, uint32_t ctxNum)
{
    char *shmemCtxLog = DB_MALLOC(MEM_ALARM_LOG_LENGTH * sizeof(char));
    if (SECUREC_UNLIKELY(shmemCtxLog == NULL)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Alloc mem for shmemCtxLog.");
        return;
    }
    (void)memset_s(shmemCtxLog, MEM_ALARM_LOG_LENGTH * sizeof(char), 0x00, MEM_ALARM_LOG_LENGTH * sizeof(char));
    int32_t err = snprintf_s(shmemCtxLog, MEM_ALARM_LOG_LENGTH, MEM_ALARM_LOG_LENGTH - 1,
        "MEM: Top %" PRIu32 " shmemCtx: ", (uint32_t)SHMEMCTX_PRINT_NUM);
    if (err < 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Generate shmCtx alarm log, ret %" PRId32 ".", err);
        DB_FREE(shmemCtxLog);
        return;
    }
    for (uint32_t i = 0; i < DB_MIN(SHMEMCTX_PRINT_NUM, ctxNum); i++) {
        uint32_t ctxId = shmemCtxArray[i].ctxId;
        DbMemCtxT *ctx = (DbMemCtxT *)DbGetShmemCtxById(ctxId, DbGetProcGlobalId());
        if (ctx == NULL) {
            continue;
        }
        // 拼接字符串防止海量日志。
        err = snprintf_s(shmemCtxLog + strlen(shmemCtxLog), MEM_ALARM_LOG_LENGTH - strlen(shmemCtxLog),
            (MEM_ALARM_LOG_LENGTH - strlen(shmemCtxLog)) - 1, "%" PRIu32 ", %s, %" PRIu64 "; ", ctx->ctxId,
            ctx->ctxName, shmemCtxArray[i].totalPhySize);
        if (err < 0) {
            DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Generate shmctx alarm log, ret %" PRId32 ".", err);
            DB_FREE(shmemCtxLog);
            return;
        }
    }
    DB_LOG_WARN(GMERR_INSUFFICIENT_RESOURCES, "%s", shmemCtxLog);
    DB_FREE(shmemCtxLog);
}

// DFX功能：日志打印使用内存使用最多的几个共享内存ctx信息。内部会加全局锁。
// 在共享内存0号alarm（非存储共享内存使用量）达到80%以上的时候，会调用改打印。
void DbPrintShmemAlarmLog(void)
{
    TopUseShmemCtxItemT *shmemCtxArray = DB_MALLOC(SHMEMCTX_PRINT_ARRAY_LEN * sizeof(TopUseShmemCtxItemT));
    if (SECUREC_UNLIKELY(shmemCtxArray == NULL)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Generate shmCtx alarm log.");
        return;
    }
    uint32_t topShmemCtxId = DB_TOP_SHMEMCTX_ID;
    DbTopShmemCtxT *pTopShmemCtx = (DbTopShmemCtxT *)DbGetShmemCtxById(topShmemCtxId, DbGetProcGlobalId());
    // 服务拉起后保证topShmemCtx不为空。
    DB_ASSERT(pTopShmemCtx != NULL);

    DbShmemCtxGlobalLock();
    uint32_t ctxNum = 0;
    for (uint32_t ctxId = 0; ctxId < DB_MAX_FIXED_ID; ctxId++) {
        if (ctxNum >= SHMEMCTX_PRINT_ARRAY_LEN) {
            break;
        }
        if (DbIsShmPtrValid(pTopShmemCtx->shmemCtxPtr[ctxId])) {
            // 该日志打印功能针对非存储的ctx。
            if (ctxId == DB_SE_DEV_SHMEMCTX_ID) {
                continue;
            }
            shmemCtxArray[ctxNum].ctxId = ctxId;
            DbMemCtxT *ctx = (DbMemCtxT *)DbGetShmemCtxById(ctxId, DbGetProcGlobalId());
            if (ctx != NULL) {
                shmemCtxArray[ctxNum].totalPhySize = DbShmCtxGetTotalPhySize(ctx, true);
                ctxNum++;
            }
        }
    }

    qsort(shmemCtxArray, ctxNum, sizeof(TopUseShmemCtxItemT), ShmemCtxTotalPhySizeCmp);
    AlarmLogShmemCtx(shmemCtxArray, ctxNum);
    DbShmemCtxGlobalUnlock();
    DB_FREE(shmemCtxArray);
}
