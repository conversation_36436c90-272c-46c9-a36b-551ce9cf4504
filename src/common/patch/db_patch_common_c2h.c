/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: db_patch_common_c2h.c
 * Description: db patch libgmdb.so cold to hot hook for hpe and dap
 * Author:
 * Create: 2022-11-10
 */
#include "db_utils.h"
#include "common_log.h"
#include "adpt_patch_c2h_type.h"
#include "db_patch_common_c2h.h"

/* 预埋冷转热激活 */
__attribute__((visibility("default"))) void LibGmdbPatchC2hActiveFunc(void)
{
    VOS_NOP();
    DB_LOG_INFO("libgmdb.so.5 reserved patch c2h active\n");
}
/* 预埋冷转热去激活 */
__attribute__((visibility("default"))) void LibGmdbPatchC2hDeactiveFunc(void)
{
    VOS_NOP();
    DB_LOG_INFO("libgmdb.so.5 reserved patch c2h deactive\n");
}
// 冷转热钩子函数,给HPE补丁机制调用
__attribute__((visibility("default"))) int32_t LibGmdbPatchC2hHook(
    bool isActive, const HpePatchInfo *patchInfo, bool *pIsProcess)
{
    if (patchInfo == NULL || pIsProcess == NULL) {
        return 1;
    }
    // 这里的libgmdb.so.5需要和设备上maps同名
    if (strcmp(patchInfo->acFileName, "libgmdb.so.5") != 0 && strcmp(patchInfo->acFileName, "libgmdb.so.5.1") != 0) {
        *pIsProcess = false;
        return 0;
    }
    *pIsProcess = true;

    if (isActive) {
        LibGmdbPatchC2hActiveFunc();
    } else {
        LibGmdbPatchC2hDeactiveFunc();
    }

    return 0;
}

// 约定好名字的钩子函数，给dopra补丁机制调用
__attribute__((visibility("default"))) int32_t libgmdb_PAT_C2HSyncHookFunc(SISP_PAT_C2H_OPER_CODE_E operCode,
    const SISP_PAT_C2H_OPER_CB_S *rspMsg, const SISP_PAT_CFG_INFO_S *cfgInfo, uint32_t *isProcess)
{
    if (cfgInfo == NULL || isProcess == NULL) {
        return 1;
    }
    int32_t ret = 0;
    *isProcess = 0;

    // 这里的libgmdb.so.5需要和设备上maps同名
    if (strcmp(cfgInfo->acFileName, "libgmdb.so.5.1") != 0) {
        return ret;
    }

    switch (operCode) {
        case SISP_PAT_C2H_OPER_ACTIVE: {
            LibGmdbPatchC2hActiveFunc();
            break;
        }
        case SISP_PAT_C2H_OPER_DEACTIVE: {
            LibGmdbPatchC2hDeactiveFunc();
            break;
        }
        default:
            (void)DbPrintfDefault("libgmdb.so c2h active or deactive oper worthless.");
            ret = 1;
    }

    *isProcess = 1;
    return ret;
}

__attribute__((visibility("default"))) int32_t libgmdb_ts_PAT_C2HSyncHookFunc(SISP_PAT_C2H_OPER_CODE_E operCode,
    const SISP_PAT_C2H_OPER_CB_S *rspMsg, const SISP_PAT_CFG_INFO_S *cfgInfo, uint32_t *isProcess)
{
    if (cfgInfo == NULL || isProcess == NULL) {
        return 1;
    }
    int32_t ret = 0;
    *isProcess = 0;

    // 这里的libgmdb_ts.so.5需要和设备上maps同名
    if (strcmp(cfgInfo->acFileName, "libgmdb_ts.so.5.1") != 0) {
        return ret;
    }

    switch (operCode) {
        case SISP_PAT_C2H_OPER_ACTIVE: {
            LibGmdbPatchC2hActiveFunc();
            break;
        }
        case SISP_PAT_C2H_OPER_DEACTIVE: {
            LibGmdbPatchC2hDeactiveFunc();
            break;
        }
        default:
            (void)DbPrintfDefault("libgmdb_ts.so c2h active or deactive oper worthless.");
            ret = 1;
    }

    *isProcess = 1;
    return ret;
}
