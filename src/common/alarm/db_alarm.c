/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: alarm.c
 * Description:
 * Author: jiangqu
 * Create: 2021-06-01
 */
#include "db_alarm.h"
#include "adpt_atomic.h"
#include "common_log.h"
#include "db_config.h"
#include "db_sysapp_context.h"
#include "db_instance.h"

// warning激活的相对阈值设置为0.900000001的原因：
// 1、double精度的问题，0.90编译器词法分析成0.89999，当操作0.90 * 100，实际会显示为89
// 2、浮点数比较的精度采用的是1e-6，0.900000001 * 100 = 90.0000001
#define CLEAR_PERCENT 0.80
#define TIME_INTERVAL 300000
#define TIME_TEN_SEC 10000
#define INVALID_TIME_INTERVAL 0

DbAlarmStatT *g_gmdbAlarmStat[MAX_INSTANCE_NUM] = {0};
DbAlarmHeadT g_gmdbAlarmInfo[(size_t)DB_ALARM_ID_UNDEFINED] = {0};
DbAlarmStatT *g_gmdbCurInstanceAlarmStat = NULL;

// 用途：存储各个alarm信息的初始配置值
// 是否并发初始化：否
// 是否并发读写：否
// 并发方案：无
DbAlarmCfgT g_alarmCfg[] = {
    {.alarmId = DB_ALARM_SHM_USED_INFO,
        .checkMethod = DB_ALARM_CHECK_IN_SCHEDULE_TASK,
        .isGlobal = true,               // 共享内存使用warning
        .timeInterval = TIME_INTERVAL,  // unit：ms
        .cfg = {.threshold = {.clearPercent = CLEAR_PERCENT, .raisePercent = RELATIVE_RAISE_PERCENT}}},
    {.alarmId = DB_ALARM_ASYNC_CONN_RING,
        .checkMethod = DB_ALARM_CHECK_IN_FUNC_CALL,
        .isGlobal = false,  // 异步连接队列使用warning
        .timeInterval = INVALID_TIME_INTERVAL,
        .cfg = {.threshold = {.raisePercent = RELATIVE_RAISE_PERCENT, .clearPercent = CLEAR_PERCENT}}},
    {.alarmId = DB_ALARM_SUB_CONN_RING,
        .checkMethod = DB_ALARM_CHECK_IN_FUNC_CALL,
        .isGlobal = false,  // 订阅连接队列使用warning
        .timeInterval = INVALID_TIME_INTERVAL,
        .cfg = {.threshold = {.raisePercent = RELATIVE_RAISE_PERCENT, .clearPercent = CLEAR_PERCENT}}},
    {.alarmId = DB_ALARM_CONNECTION_NUMBER,
        .checkMethod = DB_ALARM_CHECK_IN_FUNC_CALL,
        .isGlobal = true,  // 连接数目warning
        .timeInterval = INVALID_TIME_INTERVAL,
        .cfg = {.threshold = {.raisePercent = RELATIVE_RAISE_PERCENT, .clearPercent = CLEAR_PERCENT}}},
    {.alarmId = DB_ALARM_CLIENT_STMT,
        .checkMethod = DB_ALARM_CHECK_IN_SCHEDULE_TASK,
        .isGlobal = false,  // 客户端stmt使用量warning
        .timeInterval = TIME_TEN_SEC,
        .cfg = {.threshold = {.raisePercent = RELATIVE_RAISE_PERCENT, .clearPercent = CLEAR_PERCENT}}},
    {.alarmId = DB_ALARM_VERTEX_EDGE_DATA,
        .checkMethod = DB_ALARM_CHECK_IN_SCHEDULE_TASK,
        .isGlobal = true,  // 点边数据warning
        .timeInterval = TIME_INTERVAL,
        .cfg = {.threshold = {.raisePercent = RELATIVE_RAISE_PERCENT, .clearPercent = CLEAR_PERCENT}}},
    {.alarmId = DB_ALARM_SUB_MSG_POOL,
        .checkMethod = DB_ALARM_CHECK_IN_FUNC_CALL,
        .isGlobal = true,  // 订阅消息内存池warning
        .timeInterval = TIME_INTERVAL,
        .cfg = {.threshold = {.raisePercent = RELATIVE_RAISE_PERCENT, .clearPercent = CLEAR_PERCENT}}},
    {.alarmId = DB_ALARM_DYM_USED_INFO,
        .checkMethod = DB_ALARM_CHECK_IN_SCHEDULE_TASK,
        .isGlobal = true,  // 动态内存使用warning
        .timeInterval = TIME_INTERVAL,
        .cfg = {.threshold = {.clearPercent = CLEAR_PERCENT, .raisePercent = RELATIVE_RAISE_PERCENT}}},
    {.alarmId = DB_ALARM_HUNG_WORKER,
        .checkMethod = DB_ALARM_CHECK_IN_FUNC_CALL,  // 是callback，但是monitor检测也是周期的
        .isGlobal = true,                            // 线程挂死warning，线程处理长时间未返回喂狗
        .timeInterval = INVALID_TIME_INTERVAL,
        .cfg = {.threshold = {.raisePercent = RELATIVE_RAISE_PERCENT, .clearPercent = CLEAR_PERCENT}}},
    {.alarmId = DB_ALARM_TABLE_SPACE_USED_INFO,
        .checkMethod = DB_ALARM_CHECK_IN_SCHEDULE_TASK,
        .isGlobal = true,  // tableSpace warning
        .timeInterval = TIME_INTERVAL,
        .cfg = {.threshold = {.raisePercent = RELATIVE_RAISE_PERCENT, .clearPercent = CLEAR_PERCENT}}},
    {.alarmId = DB_ALARM_RSM_TABLE_SPACE_USED_INFO,
        .checkMethod = DB_ALARM_CHECK_IN_SCHEDULE_TASK,
        .isGlobal = true,  // rsm-tableSpace warning
        .timeInterval = TIME_INTERVAL,
        .cfg = {.threshold = {.raisePercent = RELATIVE_RAISE_PERCENT, .clearPercent = CLEAR_PERCENT}}},
    {.alarmId = DB_ALARM_STORAGE_SPACE_USED_INFO,
        .checkMethod = DB_ALARM_CHECK_IN_SCHEDULE_TASK,
        .isGlobal = true,  // storage space warning
        .timeInterval = TIME_INTERVAL,
        .cfg = {.threshold = {.raisePercent = RELATIVE_RAISE_PERCENT, .clearPercent = CLEAR_PERCENT}}},
    {.alarmId = DB_ALARM_STORAGE_FILE,
        .checkMethod = DB_ALARM_CHECK_IN_SCHEDULE_TASK,
        .isGlobal = true,  // storage file warning
        .timeInterval = TIME_INTERVAL,
        .cfg = {.threshold = {.raisePercent = RELATIVE_RAISE_PERCENT, .clearPercent = CLEAR_PERCENT}}},
};

// 用途：存储各个alarm信息的初始配置值
// 是否并发初始化：否
// 是否并发读写：否
// 并发方案：无
DbTimerHandlerT g_alarmTimerHandleDef[] = {
    {.timerName = "totalShm",
        .timerHandle = {.timerHandle = 0},
        .passParm = {.alarmId = DB_ALARM_SHM_USED_INFO, .arg = DbGetNonSeShmUsedInfoAlarm}},
    {.timerName = "totalDynMem",
        .timerHandle = {.timerHandle = 0},
        .passParm = {.alarmId = DB_ALARM_DYM_USED_INFO, .arg = DbGetDynMemUsedInfoAlarm}}};

/* warning清除，需要考虑当ACTIVE_CLEARED状态出现的时候，收集detail数据 */
// 非全局alarm项改成链表，刷新数据时，直接传入链表节点刷新，刷新逻辑和改成链表形式前一致
static void DbClearAlarmStatus(DbAlarmInfoNodeT *alarmInfoNode, double usedRatio)
{
    if (alarmInfoNode->alarmInfo.alarmData.alarmStatus == DB_ALARM_ACTIVE) {
        alarmInfoNode->alarmInfo.alarmData.alarmStatus = DB_ALARM_ACTIVE_CLEARED;
        // 更新detail中active的起始时间，必须也在alarmData中startTime更新之前
        alarmInfoNode->alarmInfo.detailData.endTime = alarmInfoNode->alarmInfo.alarmData.startTime;
        alarmInfoNode->alarmInfo.alarmData.startTime = DbRdtsc();
        DB_ASSERT(alarmInfoNode->alarmInfo.lastStatus == DB_ALARM_STATUS);
        alarmInfoNode->alarmInfo.lastStatus = DB_NORMAL_STATUS;
        // 更新detail的其他信息
        alarmInfoNode->alarmInfo.detailData.startTime = alarmInfoNode->alarmInfo.alarmData.startTime;
        alarmInfoNode->alarmInfo.detailData.succTimes = g_gmdbCurInstanceAlarmStat[alarmInfoNode->alarmId].succTimes;
        alarmInfoNode->alarmInfo.detailData.failTimes = g_gmdbCurInstanceAlarmStat[alarmInfoNode->alarmId].failTimes;
    } else if (alarmInfoNode->alarmInfo.alarmData.alarmStatus == DB_ALARM_EMPTY) {
        if (alarmInfoNode->alarmInfo.lastStatus == DB_ALARM_STATUS) {
            alarmInfoNode->alarmInfo.alarmData.alarmStatus = DB_ALARM_CLEARED;
            alarmInfoNode->alarmInfo.alarmData.startTime = DbRdtsc();
            alarmInfoNode->alarmInfo.lastStatus = DB_NORMAL_STATUS;
        } else {
            DB_ASSERT(alarmInfoNode->alarmInfo.lastStatus == DB_NORMAL_STATUS);
        }
    }
    alarmInfoNode->alarmInfo.alarmData.curVal = usedRatio;
}

/* warning激活，不用特殊考虑ACTIVE_CLEARED状态，
 * 因为ACTIVE_CLEAR -> ACTIVE的时候，按照ACTIVE处理即可 */
// 非全局alarm项改成链表，刷新数据时，直接传入链表节点刷新，刷新逻辑和改成链表形式前一致
static void DbRaiseAlarmStatus(DbAlarmInfoNodeT *alarmInfoNode, double usedRatio)
{
    if (alarmInfoNode->alarmInfo.alarmData.alarmStatus == DB_ALARM_CLEARED ||
        alarmInfoNode->alarmInfo.alarmData.alarmStatus == DB_ALARM_ACTIVE_CLEARED) {
        alarmInfoNode->alarmInfo.alarmData.alarmStatus = DB_ALARM_ACTIVE;
        alarmInfoNode->alarmInfo.alarmData.startTime = DbRdtsc();
        DB_ASSERT(alarmInfoNode->alarmInfo.lastStatus == DB_NORMAL_STATUS);
        alarmInfoNode->alarmInfo.lastStatus = DB_ALARM_STATUS;
    } else if (alarmInfoNode->alarmInfo.alarmData.alarmStatus == DB_ALARM_EMPTY) {
        if (alarmInfoNode->alarmInfo.lastStatus == DB_NORMAL_STATUS) {
            alarmInfoNode->alarmInfo.alarmData.alarmStatus = DB_ALARM_ACTIVE;
            alarmInfoNode->alarmInfo.alarmData.startTime = DbRdtsc();
            alarmInfoNode->alarmInfo.lastStatus = DB_ALARM_STATUS;
        } else {
            DB_ASSERT(alarmInfoNode->alarmInfo.lastStatus == DB_ALARM_STATUS);
        }
    }
    alarmInfoNode->alarmInfo.alarmData.curVal = usedRatio;
    alarmInfoNode->alarmInfo.detailData.alarmVal = usedRatio;
}

/* 更新global资源的warning状态 */
void DbUpdateGlobalAlmStatus(DbAlarmInfoNodeT *alarmInfoNode, double usedRatio)
{
    if (alarmInfoNode == NULL) {
        return;
    }
    double raiseRatio = alarmInfoNode->alarmInfo.alarmCfg->cfg.threshold.raisePercent;
    double clearRatio = alarmInfoNode->alarmInfo.alarmCfg->cfg.threshold.clearPercent;

    // 更新数据
    DbRWSpinLockT *wLock = &alarmInfoNode->alarmInfo.rwLock;
    DbRWSpinWLock(wLock);

    if (usedRatio < clearRatio || DbAbs(usedRatio - clearRatio) < DB_DOUBLE_COMPARE_PRECISION) {
        DbClearAlarmStatus(alarmInfoNode, usedRatio);
    } else if (usedRatio > raiseRatio || DbAbs(usedRatio - raiseRatio) < DB_DOUBLE_COMPARE_PRECISION) {
        DbRaiseAlarmStatus(alarmInfoNode, usedRatio);
    } else {
        // only update current value
        alarmInfoNode->alarmInfo.alarmData.curVal = usedRatio;
    }
    DbRWSpinWUnlock(wLock);
}

static void DbAlmUpdateAlmStatus(DbAlarmIdE alarmId, uint32_t userDefRscId, double usedRatio)
{
    DbAlarmInfoNodeT *alarmNode = DbGetAlarmNode(alarmId, userDefRscId);
    if (alarmNode != NULL) {
        DbUpdateGlobalAlmStatus(alarmNode, usedRatio);
    }
}

/*******************************************************************************
  函 数 名		:  DbCheckNonGlobalAlarmRes
  功能描述		:  当warning monitor资源使用有变化的时候被调用，通常嵌入在其他模块中，用来
                  检查非global资源是达到warning状态，需要获取具体的对象id的资源使用率
  输入参数		:  目标资源的alarm id 及 具体某一对象的id
  输出参数		:  None
  返 回 值		:  函数执行状态
*******************************************************************************/
Status DbCheckNonGlobalAlarmRes(DbAlarmIdE alarmId, const void *arg)
{
    switch (alarmId) {
        case DB_ALARM_ASYNC_CONN_RING:
        case DB_ALARM_SUB_CONN_RING:
        case DB_ALARM_CLIENT_STMT: {
            DB_POINTER(arg);
            // 防止删除节点和刷新节点并发，主要是DB_ALARM_CLIENT_STMT加锁，连接的alarm刷新不会存在和删除节点的并发
            DbRWSpinLockT *wLock = &g_gmdbAlarmInfo[alarmId].rwLock;
            DbRWSpinWLock(wLock);
            const DbAlmT *drt2Alarm = (const DbAlmT *)arg;
            DbAlmUpdateAlmStatus(alarmId, drt2Alarm->id, drt2Alarm->usedRatio);
            DbRWSpinWUnlock(wLock);
            break;
        }
        case DB_ALARM_SUB_MSG_POOL:
        case DB_ALARM_HUNG_WORKER:
        case DB_ALARM_CONNECTION_NUMBER:
        case DB_ALARM_VERTEX_EDGE_DATA:
        case DB_ALARM_SHM_USED_INFO:
        case DB_ALARM_DYM_USED_INFO:
        case DB_ALARM_TABLE_SPACE_USED_INFO:
        case DB_ALARM_RSM_TABLE_SPACE_USED_INFO:
        case DB_ALARM_STORAGE_SPACE_USED_INFO:
        case DB_ALARM_STORAGE_FILE:
        case DB_ALARM_ID_UNDEFINED:
        default:
            return GMERR_FEATURE_NOT_SUPPORTED;
    }

    return GMERR_OK;
}

/*******************************************************************************
  函 数 名		:  DbCheckGlobalAlarmRes
  功能描述		:  当warning monitor资源使用有变化的时候被调用，通常嵌入在其他模块中，用来
                  检查global资源是达到warning状态，需要获取资源使用率
  输入参数		:  目标资源的alarm id，当前的使用情况
  输出参数		:  None
  返 回 值		:  函数执行状态
*******************************************************************************/
Status DbCheckGlobalAlarmRes(DbAlarmIdE alarmId, double usedRatio)
{
    switch (alarmId) {
        case DB_ALARM_SHM_USED_INFO:
        case DB_ALARM_CONNECTION_NUMBER:
        case DB_ALARM_VERTEX_EDGE_DATA:
        case DB_ALARM_SUB_MSG_POOL:
        case DB_ALARM_DYM_USED_INFO:
        case DB_ALARM_HUNG_WORKER:
        case DB_ALARM_TABLE_SPACE_USED_INFO:
        case DB_ALARM_RSM_TABLE_SPACE_USED_INFO:
        case DB_ALARM_STORAGE_SPACE_USED_INFO:
        case DB_ALARM_STORAGE_FILE:
            break;
        case DB_ALARM_ASYNC_CONN_RING:
        case DB_ALARM_SUB_CONN_RING:
        case DB_ALARM_CLIENT_STMT:
        case DB_ALARM_ID_UNDEFINED:
        default:
            return GMERR_FEATURE_NOT_SUPPORTED;
    }
    DbAlarmInfoNodeT *alarmNode = DbGetAlarmNode(alarmId, 0);
    DbUpdateGlobalAlmStatus(alarmNode, usedRatio);

    return GMERR_OK;
}

// 全局alarm项刷新SuccCnt，只用于服务端，一个服务端只有一个节点
inline void DbAlarmUpdateSuccCnt(DbAlarmIdE alarmId, uint32_t addCnt)
{
    if (g_gmdbCurInstanceAlarmStat != NULL) {
        (void)DbAtomicFetchAndAdd64(&g_gmdbCurInstanceAlarmStat[alarmId].succTimes, addCnt);
    }
}

// 全局alarm项刷新FailCnt，只用于服务端，一个服务端只有一个节点
inline void DbAlarmUpdateFailCnt(DbAlarmIdE alarmId, uint32_t addCnt)
{
    if (g_gmdbCurInstanceAlarmStat != NULL) {
        (void)DbAtomicFetchAndAdd64(&g_gmdbCurInstanceAlarmStat[alarmId].failTimes, addCnt);
    }
}

// 全局alarm项刷新SuccCnt，根据实例Id刷新
void DbAlarmUpdateSuccCntByInstanceId(uint32_t instanceId, DbAlarmIdE alarmId, uint32_t addCnt)
{
    uint32_t instId = instanceId;
    if (DbIsMultiInstanceEnabled()) {
        instId = DbGetProcGlobalId();
    }

    uint32_t idx = DbGetIdxByInstanceId(instId);
    if (idx >= MAX_INSTANCE_NUM) {
        return;
    }
    if (g_gmdbAlarmStat[idx] == NULL) {
        return;
    }
    (void)DbAtomicFetchAndAdd64(&g_gmdbAlarmStat[idx][alarmId].succTimes, addCnt);
}

// 全局alarm项刷新FailCnt，根据实例Id刷新
void DbAlarmUpdateFailCntByInstanceId(uint32_t instanceId, DbAlarmIdE alarmId, uint32_t addCnt)
{
    uint32_t instId = instanceId;
    if (DbIsMultiInstanceEnabled()) {
        instId = DbGetProcGlobalId();
    }

    uint32_t idx = DbGetIdxByInstanceId(instId);
    if (idx >= MAX_INSTANCE_NUM) {
        return;
    }
    if (g_gmdbAlarmStat[idx] == NULL) {
        return;
    }
    (void)DbAtomicFetchAndAdd64(&g_gmdbAlarmStat[idx][alarmId].failTimes, addCnt);
}

Status DbAlarmStatInit(void)
{
    uint32_t instanceId = DbGetProcGlobalId();
    uint32_t alarmStatShmemKey = DbConstructShmemKeyById(instanceId, DB_ALARM_STAT_ID);
    uint32_t idx = DbGetIdxByInstanceId(instanceId);
    if (idx >= MAX_INSTANCE_NUM) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Incorrect alarm stat idx %" PRIu32 "", idx);
        return GMERR_DATA_EXCEPTION;
    }
    g_gmdbAlarmStat[idx] = (DbAlarmStatT *)DB_SHMEM_CREATE(
        alarmStatShmemKey, sizeof(DbAlarmStatT) * DB_ALARM_ID_UNDEFINED, DbGetShmPermission());
    if (g_gmdbAlarmStat[idx] == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Shmem Key: %" PRIu32 ", perm: %" PRIu32 ". OS ret: %" PRId32 "",
            alarmStatShmemKey, DbGetShmPermission(), (int32_t)errno);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(g_gmdbAlarmStat[idx], sizeof(DbAlarmStatT) * DB_ALARM_ID_UNDEFINED, 0,
        sizeof(DbAlarmStatT) * DB_ALARM_ID_UNDEFINED);
    g_gmdbCurInstanceAlarmStat = g_gmdbAlarmStat[idx];
    return GMERR_OK;
}

void DbAlarmStatUninit(void)
{
    uint32_t instanceId = DbGetProcGlobalId();
    uint32_t idx = DbGetIdxByInstanceId(instanceId);
    g_gmdbCurInstanceAlarmStat = NULL;
    if (idx >= MAX_INSTANCE_NUM) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Incorrect alarm stat idx %" PRIu32 "", idx);
        return;
    }
    (void)DB_SHMEM_DETACH(g_gmdbAlarmStat[idx]);
    g_gmdbAlarmStat[idx] = NULL;
    uint32_t alarmStatShmemKey = DbConstructShmemKeyById(instanceId, DB_ALARM_STAT_ID);
    DB_SHMEM_DESTORY(alarmStatShmemKey);
}

/*******************************************************************************
  函 数 名		:  DbAlarmInit
  功能描述		:  初始化alarm资源的配置和状态
  输入参数		:  None
  输出参数		:  None
  返 回 值		:  函数执行状态
*******************************************************************************/
Status DbAlarmInit(void)
{
    Status ret = DbAlarmStatInit();
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)memset_s(&g_gmdbAlarmInfo[0], sizeof(g_gmdbAlarmInfo), 0, sizeof(g_gmdbAlarmInfo));
    DbMemCtxT *topDynMemctx = DbSrvGetSysDynCtx(DbGetProcGlobalId());
    DbMemCtxArgsT args = {0};
    args.liteModOn = true;
    DbMemCtxT *alarmDynCtx = DbCreateDynMemCtx(topDynMemctx, true, "AlarmDynContext", &args);
    if (alarmDynCtx == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Create memCtx.");
        ret = GMERR_OUT_OF_MEMORY;
        goto EXIT1;
    }

    for (uint32_t i = 0; i < DB_ALARM_ID_UNDEFINED; i++) {
        g_gmdbAlarmInfo[i].alarmCfg = &g_alarmCfg[i];
        g_gmdbAlarmInfo[i].memCtx = alarmDynCtx;
        if ((bool)(g_alarmCfg[i].isGlobal)) {
            g_gmdbAlarmInfo[i].next = (DbAlarmInfoNodeT *)DbDynMemCtxAlloc(alarmDynCtx, sizeof(DbAlarmInfoNodeT));
            if (g_gmdbAlarmInfo[i].next == NULL) {
                DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc next node.");
                ret = GMERR_OUT_OF_MEMORY;
                goto EXIT2;
            }
            (void)memset_s(g_gmdbAlarmInfo[i].next, sizeof(DbAlarmInfoNodeT), 0, sizeof(DbAlarmInfoNodeT));
            g_gmdbAlarmInfo[i].next->alarmInfo.alarmCfg = &g_alarmCfg[i];
            g_gmdbAlarmInfo[i].next->alarmId = (DbAlarmIdE)i;
        } else {
            g_gmdbAlarmInfo[i].next = NULL;
        }
    }

    // 注册所有common模块的定时任务
    ret = DbAlarmTimerRegister(g_alarmTimerHandleDef, ELEMENT_COUNT(g_alarmTimerHandleDef));
    if (ret != GMERR_OK) {
        goto EXIT2;
    }
    return GMERR_OK;
EXIT2:
    DbDeleteDynMemCtx(alarmDynCtx);
EXIT1:
    DbAlarmStatUninit();
    return ret;
}

Status DbAlarmUnInit(void)
{
    // 先取消定时器，避免节点删了之后，定时器还在刷新数据
    Status ret = DbAlarmTimerUnregister(g_alarmTimerHandleDef, ELEMENT_COUNT(g_alarmTimerHandleDef));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unregister db alarm timer.");
        return ret;
    }
    // UnInit的时候释放全部的alarmInfoNode节点
    DbMemCtxT *alarmDynCtx = NULL;
    for (uint32_t i = 0; i < DB_ALARM_ID_UNDEFINED; i++) {
        alarmDynCtx = g_gmdbAlarmInfo[i].memCtx;
        for (DbAlarmInfoNodeT *infoNode = g_gmdbAlarmInfo[i].next; infoNode != NULL;) {
            DbAlarmInfoNodeT *nextTmp = infoNode->next;
            DbDynMemCtxFree(alarmDynCtx, infoNode);
            infoNode = nextTmp;
        }
        g_gmdbAlarmInfo[i].next = NULL;
        g_gmdbAlarmInfo[i].memCtx = NULL;
    }
    // 删除alarmDynCtx,这里alarmDynCtx就是头结点数组的最后一个head上的memCtx
    DbDeleteDynMemCtx(alarmDynCtx);
    DbAlarmStatUninit();
    return GMERR_OK;
}

/* *
 * @brief 回调函数，各模块提供的用于获取资源使用率的函数格式
 * @param usedRatio: 资源资源当前的使用率
 */
typedef Status (*AlmUsageProc)(double *usedRatio);

/*******************************************************************************
  功能描述		:  定时任务的统一回调函数
  输入参数		:  params 包含具体模块回调函数信息
  输出参数		:  None
  返 回 值		:  None
*******************************************************************************/
void DbAlarmStatusCollection(void *params)
{
    DbCallBackParmsT *param = (DbCallBackParmsT *)params;
    Status ret = GMERR_OK;
    double usedRatio;
    if ((bool)(g_gmdbAlarmInfo[param->alarmId].alarmCfg->isGlobal)) {
        ret = ((AlmUsageProc)(param->arg))(&usedRatio);
        if (ret != GMERR_OK) {
            DB_LOG_INFO("alarm:%" PRId32 " status global collect", (uint32_t)param->alarmId);
        } else {
            ret = DbCheckGlobalAlarmRes(param->alarmId, usedRatio);
        }
    } else if (param->alarmId == DB_ALARM_CLIENT_STMT) {
        ret = ((AlmUsageProc)(param->arg))(&usedRatio);
    }
    if (ret != GMERR_OK) {
        DB_LOG_INFO("alarm:%" PRId32 " status collect", (uint32_t)param->alarmId);
    }
}

/* 定时任务注册函数，注册所有收集warning数据的定时任务 */
Status DbAlarmTimerRegister(DbTimerHandlerT *alarmHandler, const uint32_t taskNum)
{
    for (uint32_t i = 0; i < taskNum; i++) {
        CallBackStructT callBack;
        DbAlarmIdE alarmId = alarmHandler[i].passParm.alarmId;
        uint32_t tmInterval = g_gmdbAlarmInfo[alarmId].alarmCfg->timeInterval;
        callBack.callBack = DbAlarmStatusCollection;
        callBack.parameter = &(alarmHandler[i].passParm);
        alarmHandler[i].timerHandle = DbTimerCreate(alarmHandler[i].timerName, &callBack, tmInterval, TIMER_MODE_LOOP);
        if (!DbCheckTimerHandle(alarmHandler[i].timerHandle)) {
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Create alarm timer for register");
            return GMERR_DATA_EXCEPTION;
        }
        Status ret = DbTimerStart(alarmHandler[i].timerHandle, TIMER_MODE_LOOP);
        if (ret != GMERR_OK) {
            (void)DbTimerDelete(alarmHandler[i].timerHandle);
            DB_LOG_INFO("alarm:%" PRId32 " timer regist", (uint32_t)alarmId);
            return ret;
        }
    }
    return GMERR_OK;
}

Status DbAlarmTimerUnregister(DbTimerHandlerT *alarmHandler, const uint32_t taskNum)
{
    for (uint32_t i = 0; i < taskNum; i++) {
        if (!DbCheckTimerHandle(alarmHandler[i].timerHandle)) {
            return GMERR_OK;
        }
        Status ret = DbTimerStop(alarmHandler[i].timerHandle);
        if (ret != GMERR_OK) {
            DB_LOG_INFO("alarm:%" PRId32 " timer unregist stop", (uint32_t)alarmHandler[i].passParm.alarmId);
            return ret;
        }
        ret = DbTimerDelete(alarmHandler[i].timerHandle);
        if (ret != GMERR_OK) {
            DB_LOG_INFO("alarm:%" PRId32 " timer unregist delete", (uint32_t)alarmHandler[i].passParm.alarmId);
            return ret;
        }
        alarmHandler[i].timerHandle.timerHandle = 0;
    }
    return GMERR_OK;
}

void DbGetAlarmDataInner(DbAlarmInfoNodeT *infoNode, DbAlarmDataT *alarmData)
{
    if (infoNode == NULL) {
        return;
    }
    alarmData->succTimes = g_gmdbCurInstanceAlarmStat[infoNode->alarmId].succTimes;
    alarmData->failTimes = g_gmdbCurInstanceAlarmStat[infoNode->alarmId].failTimes;
    alarmData->userDefRscId = infoNode->alarmInfo.userDefRscId;
    DbRWSpinLockT *rLock = NULL;
    rLock = &infoNode->alarmInfo.rwLock;
    DbRWSpinRLock(rLock);
    alarmData->alarmStatus = infoNode->alarmInfo.lastStatus;
    alarmData->status = infoNode->alarmInfo.alarmData.alarmStatus;
    if (alarmData->alarmStatus == DB_ALARM_STATUS) {
        alarmData->activeValue = infoNode->alarmInfo.alarmData.curVal;
        alarmData->clearedValue = 0;
    } else {
        alarmData->clearedValue = infoNode->alarmInfo.alarmData.curVal;
        alarmData->activeValue = 0;
    }
    if (SECUREC_UNLIKELY(alarmData->status == DB_ALARM_ACTIVE_CLEARED)) {
        alarmData->alarmDetail.alarmVal = infoNode->alarmInfo.detailData.alarmVal;
        alarmData->alarmDetail.succTimes = infoNode->alarmInfo.detailData.succTimes;
        alarmData->alarmDetail.failTimes = infoNode->alarmInfo.detailData.failTimes;
        alarmData->alarmDetail.endTime = infoNode->alarmInfo.detailData.endTime;
    }
    // clear alarm status to empty after a round of reporting
    infoNode->alarmInfo.alarmData.alarmStatus = DB_ALARM_EMPTY;
    DbRWSpinRUnlock(rLock);
}

void DbGetNonGlobalAlarmData(DbAlarmIdE alarmId, DbAlarmDataT *alarmData)
{
    // 如果没有非全局节点,直接退出了，外面初始化的是0
    // 非全局节点没有alarm状态的情况，返回第一个节点
    if (g_gmdbAlarmInfo[alarmId].next != NULL) {
        DbRWSpinLockT *wLock = &g_gmdbAlarmInfo[alarmId].rwLock;
        DbRWSpinWLock(wLock);
        // 优先获取处于alarm状态切没有被获取过的，即not empty
        for (DbAlarmInfoNodeT *infoNode = g_gmdbAlarmInfo[alarmId].next; infoNode != NULL;) {
            DbAlarmInfoNodeT *nextTmp = infoNode->next;
            if (infoNode->alarmInfo.lastStatus == DB_ALARM_STATUS &&
                infoNode->alarmInfo.alarmData.alarmStatus != DB_ALARM_EMPTY) {
                DbGetAlarmDataInner(infoNode, alarmData);
                DbRWSpinWUnlock(wLock);
                return;
            }
            infoNode = nextTmp;
        }
        // 如果都被获取过，则返回第一个处于alarm状态切是empty
        for (DbAlarmInfoNodeT *infoNode = g_gmdbAlarmInfo[alarmId].next; infoNode != NULL;) {
            DbAlarmInfoNodeT *nextTmp = infoNode->next;
            if (infoNode->alarmInfo.lastStatus == DB_ALARM_STATUS) {
                DbGetAlarmDataInner(infoNode, alarmData);
                DbRWSpinWUnlock(wLock);
                return;
            }
            infoNode = nextTmp;
        }
        // 没有从上面的for循环中退出，表示整个链表没有处于alarm状态的项，此时拿第一个值出去
        DbGetAlarmDataInner(g_gmdbAlarmInfo[alarmId].next, alarmData);
        DbRWSpinWUnlock(wLock);
    }
    // 如果没有走上面的if分支，代表当前非全局alarm项，一个数据都没有，需要根据之前的lastStatus，修改一下返回值。
    alarmData->succTimes = g_gmdbCurInstanceAlarmStat[alarmId].succTimes;
    alarmData->failTimes = g_gmdbCurInstanceAlarmStat[alarmId].failTimes;
    if (g_gmdbAlarmInfo[alarmId].lastStatus == DB_ALARM_STATUS) {
        alarmData->status = DB_ALARM_CLEARED;
        g_gmdbAlarmInfo[alarmId].lastStatus = DB_NORMAL_STATUS;
    }
    return;
}

void DbGetAlarmData(DbAlarmIdE alarmId, DbAlarmDataT *alarmData)
{
    if (alarmId == DB_ALARM_CLIENT_STMT) {
        alarmData->srcType = DB_ALARM_CLIENT;
    } else {  // 可省略
        alarmData->srcType = DB_ALARM_SERVER;
    }
    alarmData->activeThreshold = g_gmdbAlarmInfo[alarmId].alarmCfg->cfg.threshold.raisePercent;
    alarmData->clearedThreshold = g_gmdbAlarmInfo[alarmId].alarmCfg->cfg.threshold.clearPercent;

    /*
     * 此处加了读锁，但实际对alarm_info有一个状态的更新，实际使用不应有多读操作,
     * 若有可能会alarmStatus出现不一致的empty状态，不过概率低，影响很小
     */

    switch (alarmId) {
        case DB_ALARM_ASYNC_CONN_RING:
        case DB_ALARM_SUB_CONN_RING:
        case DB_ALARM_CLIENT_STMT: {
            DbGetNonGlobalAlarmData(alarmId, alarmData);
            break;
        }
        case DB_ALARM_HUNG_WORKER:
        case DB_ALARM_SUB_MSG_POOL:
        case DB_ALARM_CONNECTION_NUMBER:
        case DB_ALARM_VERTEX_EDGE_DATA:
        case DB_ALARM_SHM_USED_INFO:
        case DB_ALARM_DYM_USED_INFO:
        case DB_ALARM_TABLE_SPACE_USED_INFO:
        case DB_ALARM_RSM_TABLE_SPACE_USED_INFO:
        case DB_ALARM_STORAGE_SPACE_USED_INFO:
        case DB_ALARM_STORAGE_FILE:
            DbGetAlarmDataInner(g_gmdbAlarmInfo[alarmId].next, alarmData);
            break;
        case DB_ALARM_ID_UNDEFINED:
        default:
            break;
    }
    g_gmdbAlarmInfo[alarmId].lastStatus = alarmData->alarmStatus;
    return;
}

void DbModifyAlarmThreshold(DbAlarmIdE alarmId, uint16_t raiseRatio, uint16_t clearRatio)
{
    g_alarmCfg[alarmId].cfg.threshold.raisePercent = (double)raiseRatio / DB_PERCENTAGE_BASE;
    g_alarmCfg[alarmId].cfg.threshold.clearPercent = (double)clearRatio / DB_PERCENTAGE_BASE;
}

/******************************************************************************
  函 数 名		:  DbSerializeAlarmData
  功能描述		:  序列化结构体DbAlarmDataT
  输入参数		:  要序列化的结构数据内容tgtData
  输出参数		:  buffer存放序列化后的数据
  返 回 值		:  执行成功，或内存分配失败
*******************************************************************************/
Status DbSerializeAlarmData(const DbAlarmDataT *tgtData, DbBufferT *buffer)
{
    uint32_t len = sizeof(DbAlarmDataT);
    buffer->len = len;
    // 在QryExecuteGetAlarmData函数内成对申请释放，通过DbReleaseAlarmData接口释放
    if (buffer->memCtx == NULL) {
        buffer->buf = (uint8_t *)DbDynMemAlloc(len);
    } else {
        buffer->buf = (uint8_t *)DbDynMemCtxAlloc(buffer->memCtx, len);
    }
    if (buffer->buf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "size is %" PRIu32 ".", len);
        return GMERR_OUT_OF_MEMORY;
    }
    *((DbAlarmDataT *)(buffer->buf)) = *tgtData;
    return GMERR_OK;
}

/******************************************************************************
  函 数 名		:  DbReleaseAlarmData
  功能描述		:  与DbSerializeAlarmData配套使用，释放DbSerializeAlarmData申请的内存
  输入参数		:  DbBuffer
  输出参数		:  无
  返 回 值		:  无
*******************************************************************************/
void DbReleaseAlarmData(DbBufferT *buffer)
{
    DB_POINTER(buffer);
    if (buffer->buf != NULL) {
        if (buffer->memCtx == NULL) {
            DbDynMemFree(buffer->buf);
        } else {
            DbDynMemCtxFree(buffer->memCtx, buffer->buf);  // 正常释放内存，后续不会再用到
        }
    }
}

// 根据userDefRscId从链表中获取节点，找不到则返回NULL,外部使用的时候需要注意加锁
DbAlarmInfoNodeT *DbGetAlarmNode(DbAlarmIdE alarmId, uint32_t userDefRscId)
{
    switch (alarmId) {
        case DB_ALARM_ASYNC_CONN_RING:
        case DB_ALARM_SUB_CONN_RING:
        case DB_ALARM_CLIENT_STMT: {
            for (DbAlarmInfoNodeT *infoNode = g_gmdbAlarmInfo[alarmId].next; infoNode != NULL;) {
                DbAlarmInfoNodeT *nextTmp = infoNode->next;
                if (infoNode->alarmInfo.userDefRscId == userDefRscId) {
                    return infoNode;
                }
                infoNode = nextTmp;
            }
            break;
        }
        case DB_ALARM_HUNG_WORKER:
        case DB_ALARM_SUB_MSG_POOL:
        case DB_ALARM_CONNECTION_NUMBER:
        case DB_ALARM_VERTEX_EDGE_DATA:
        case DB_ALARM_SHM_USED_INFO:
        case DB_ALARM_DYM_USED_INFO:
        case DB_ALARM_TABLE_SPACE_USED_INFO:
        case DB_ALARM_RSM_TABLE_SPACE_USED_INFO:
        case DB_ALARM_STORAGE_SPACE_USED_INFO:
        case DB_ALARM_STORAGE_FILE:
        case DB_ALARM_ID_UNDEFINED:
            // 全局alarm项只有头结点后面的一个链表节点，可以直接返回，不用userDefRscId参数
            DB_UNUSED(userDefRscId);
            return g_gmdbAlarmInfo[alarmId].next;
        default:
            return NULL;
    }
    return NULL;
}

// 删除链表中节点，只提供给非全局alarm项使用，全局alarm项节点的删除在DbAlarmUnInit中操作
Status DbDeleteNonGlobalAlarmNode(DbAlarmIdE alarmId, uint32_t userDefRscId)
{
    if (alarmId == DB_ALARM_ASYNC_CONN_RING || alarmId == DB_ALARM_SUB_CONN_RING || alarmId == DB_ALARM_CLIENT_STMT) {
        // 获取内存上下文
        DbMemCtxT *alarmDynCtx = (DbMemCtxT *)g_gmdbAlarmInfo[alarmId].memCtx;
        // 找到对应节点
        DbRWSpinLockT *wLock = &g_gmdbAlarmInfo[alarmId].rwLock;
        DbRWSpinWLock(wLock);
        DbAlarmInfoNodeT *alarmNode = DbGetAlarmNode(alarmId, userDefRscId);
        if (alarmNode == NULL) {
            DbRWSpinWUnlock(wLock);
            return GMERR_OK;  // 找不到对应节点就直接返回，不报错
        }
        // 拿到被删除节点的前一个节点和后一个节点
        DbAlarmInfoNodeT *nextNode = alarmNode->next;
        DbAlarmInfoNodeT *prevNode = alarmNode->prev;
        if (prevNode == NULL) {  // 约定如果preNode==NULL,说明是头结点后面的第一个节点
            g_gmdbAlarmInfo[alarmId].next = nextNode;  // 此时头节点的next，需要指向被删除节点的后一个链表节点
            if (nextNode != NULL) {
                nextNode->prev = NULL;  // nextNode->prev置NULL,同如上说明的约定，头结点后面第一个节点的Prev=NULL
            }
        } else {
            prevNode->next = nextNode;
            if (nextNode != NULL) {
                // 后一个节点不为NULL的话，需要nextNode->prev指向prevNode，把链表串起来
                nextNode->prev = prevNode;
            }
        }
        DbDynMemCtxFree(alarmDynCtx, alarmNode);
        DbRWSpinWUnlock(wLock);
        return GMERR_OK;
    } else {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Alarm types is not NonGlobal.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
}

// 创建链表中节点，只提供给非全局alarm项使用，全局alarm项节点的创建在DbAlarmInit中操作
Status DbCreatNonGlobalAlarmNode(DbAlarmIdE alarmId, uint32_t userDefRscId)
{
    if (alarmId == DB_ALARM_ASYNC_CONN_RING || alarmId == DB_ALARM_SUB_CONN_RING || alarmId == DB_ALARM_CLIENT_STMT) {
        // 获取内存上下文
        DbMemCtxT *alarmDynCtx = (g_gmdbAlarmInfo[alarmId].memCtx);
        // alloc一个节点
        DbRWSpinLockT *wLock = &g_gmdbAlarmInfo[alarmId].rwLock;
        DbRWSpinWLock(wLock);
        DbAlarmInfoNodeT *alarmNode = (DbAlarmInfoNodeT *)DbDynMemCtxAlloc(alarmDynCtx, sizeof(DbAlarmInfoNodeT));
        if (alarmNode == NULL) {
            DbRWSpinWUnlock(wLock);
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc alarm node.");
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memset_s(alarmNode, sizeof(DbAlarmInfoNodeT), 0, sizeof(DbAlarmInfoNodeT));
        // 把新创建的节点挂到链表的头结点后面
        DbAlarmInfoNodeT *tempNode = g_gmdbAlarmInfo[alarmId].next;
        if (tempNode != NULL) {
            alarmNode->next = tempNode;
            tempNode->prev = alarmNode;
        }
        g_gmdbAlarmInfo[alarmId].next = alarmNode;
        alarmNode->prev = NULL;  // 约定头结点后面的第一个链表节点的prev==NULL
        alarmNode->alarmId = alarmId;
        alarmNode->alarmInfo.userDefRscId = userDefRscId;
        alarmNode->alarmInfo.alarmCfg = g_gmdbAlarmInfo[alarmId].alarmCfg;
        DbRWSpinWUnlock(wLock);
        return GMERR_OK;
    } else {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Alarm types is not NonGlobal.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
}

DbAlarmHeadT *DbGetAlarmHead(DbAlarmIdE alarmId)
{
    return &g_gmdbAlarmInfo[alarmId];
}

DbAlarmStatT DbGetAlarmStat(DbAlarmIdE alarmId)
{
    // 小结构, 直接返回只读的副本, 效率也很高
    return g_gmdbCurInstanceAlarmStat[alarmId];
}

Status DbTryAttachAlarmStat(uint32_t instanceId)
{
    uint32_t idx = DbGetIdxByInstanceId(instanceId);
    if (g_gmdbAlarmStat[idx] != NULL) {
        return GMERR_OK;
    }
    uint32_t alarmStatShmemKey = DbConstructShmemKeyById(instanceId, DB_ALARM_STAT_ID);
    g_gmdbAlarmStat[idx] = (DbAlarmStatT *)DB_SHMEM_ATTACH(alarmStatShmemKey, RW_PERMISSION);
    if (SECUREC_UNLIKELY(g_gmdbAlarmStat[idx] == NULL)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "Attach alarm stat. Key: %" PRIu32 ", perm: %" PRIu32 ", OS ret: %" PRId32 ".", alarmStatShmemKey,
            DbAdptGetShmPermByKey(alarmStatShmemKey), (int32_t)errno);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

void DbDetachAllAlarmStat(void)
{
    for (uint32_t i = 0; i < MAX_INSTANCE_NUM; i++) {
        if (g_gmdbAlarmStat[i] != NULL) {
            (void)DB_SHMEM_DETACH(g_gmdbAlarmStat[i]);
            g_gmdbAlarmStat[i] = NULL;
        }
    }
}

void DbDetachAlarmStat(uint32_t instanceId)
{
    if (g_gmdbAlarmStat[DbGetIdxByInstanceId(instanceId)] != NULL) {
        (void)DB_SHMEM_DETACH(g_gmdbAlarmStat[DbGetIdxByInstanceId(instanceId)]);
        g_gmdbAlarmStat[DbGetIdxByInstanceId(instanceId)] = NULL;
    }
}
