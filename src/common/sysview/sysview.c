/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: system.h
 * Description: sysview file for system view
 * Author:
 * Create: 2020-09-01
 */
#include "sysview.h"
#include <string.h>
#include "db_mem_context.h"
#include "db_sysapp_context.h"
#include "db_hash.h"
#include "securec.h"
#include "common_log.h"

#ifdef __cplusplus
extern "C" {
#endif

uint32_t SvHashViewName(const char *viewName)
{
    return DbStrToHash32(viewName);
}

static Status SvRegistSingleView(SvInstanceT *inst, SvDefT *viewInfo)
{
    DB_POINTER2(inst, viewInfo);

    uint32_t viewNameHash = SvHashViewName(viewInfo->svName);
    DbSpinLock(&inst->lock);
    if (inst->count >= DB_VIEW_MAX_NUM) {
        DbSpinUnlock(&inst->lock);
        DB_LOG_ERROR(GMERR_INSUFFICIENT_RESOURCES, "%s", viewInfo->svName);
        return GMERR_INSUFFICIENT_RESOURCES;
    }
    if (DbOamapLookup(&inst->viewMap, viewNameHash, viewInfo->svName, NULL)) {
        DbSpinUnlock(&inst->lock);
        DB_LOG_ERROR(GMERR_DUPLICATE_OBJECT, "%s", viewInfo->svName);
        return GMERR_DUPLICATE_OBJECT;
    }
    if (DbOamapInsert(&inst->viewMap, viewNameHash, viewInfo->svName, viewInfo, NULL) != GMERR_OK) {
        DbSpinUnlock(&inst->lock);
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "%s", viewInfo->svName);
        return GMERR_DATA_EXCEPTION;
    }
    viewInfo->isCreatedTable = false;
    inst->count++;
    DbSpinUnlock(&inst->lock);
    return GMERR_OK;
}

Status SvRegistViews(SvInstanceT *inst, uint32_t viewsCnt, SvDefT *viewsInfo)
{
    DB_POINTER2(inst, viewsInfo);
    // register view to sysview instance
    for (uint32_t i = 0; i < viewsCnt; i++) {
        Status ret = SvRegistSingleView(inst, &viewsInfo[i]);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status SvRegistViewsMultiIns(SvInstanceT *viewInst, SvDefT **dbInstanceViewsInfo, SvDefT *viewsInfo, uint32_t size)
{
    SvDefT *views = DbDynMemCtxAlloc(viewInst->memCtx, sizeof(SvDefT) * size);
    if (views == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "malloc for instance views.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(views, sizeof(SvDefT) * size, 0, sizeof(SvDefT) * size);
    for (uint32_t i = 0; i < size; ++i) {
        views[i] = viewsInfo[i];
    }
    *dbInstanceViewsInfo = views;
    Status ret = SvRegistViews(viewInst, size, views);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(viewInst->memCtx, views);
        return ret;
    }
    return GMERR_OK;
}

Status SvGetViewByName(SvInstanceT *viewInst, const char *viewName, SvDefT **view)
{
    DB_POINTER3(viewInst, viewName, view);

    DbSpinLock(&viewInst->lock);
    *view = DbOamapLookup(&viewInst->viewMap, SvHashViewName(viewName), viewName, NULL);
    DbSpinUnlock(&viewInst->lock);
    if (*view == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_NAME, "%s", viewName);
        return GMERR_INVALID_NAME;
    }
    return GMERR_OK;
}

Status SvGetVertexLabel(SvInstanceT *svInst, void *stmt, const char *viewName)
{
    DB_POINTER3(svInst, stmt, viewName);
    SvDefT *view = NULL;
    Status ret = SvGetViewByName(svInst, viewName, &view);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 检查一下 catalog 中是否存在，如果存在则返回成功
    if (view->isCreatedTable) {
        return GMERR_OK;
    }

    if (svInst->vertexLabelPaser == NULL || svInst->vertexLabelCreate == NULL) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "get label in sysview.");
        return GMERR_DATA_EXCEPTION;
    }

    DbSpinLock(&svInst->lock);
    if (view->isCreatedTable) {
        DbSpinUnlock(&svInst->lock);
        return GMERR_OK;
    }
    TextT configJson, labelJson;
    configJson.len = 0;
    configJson.str = NULL;
    labelJson.len = (uint32_t)(strlen(view->json) + 1);
    labelJson.str = view->json;
    ret = svInst->vertexLabelPaser(stmt, &labelJson, &configJson);
    if (ret != GMERR_OK) {
        DbSpinUnlock(&svInst->lock);
        DB_LOG_ERROR(ret, "Parse %s vertexlabel json.", viewName);
        return ret;
    }

    // 创建视图表
    ret = svInst->vertexLabelCreate(stmt);
    if (ret != GMERR_OK) {
        DbSpinUnlock(&svInst->lock);
        DB_LOG_ERROR(ret, "Create %s label.", viewName);
        return ret;
    }

    // 表示该表已经创建
    view->isCreatedTable = true;
    DbSpinUnlock(&svInst->lock);
    return GMERR_OK;
}

/*
 * 补丁类型：光启补丁Demo
 * 补丁文件：patch/guangqi_patch/HP0204/sysview.c
 * 函数类型：补丁函数
 * 变更说明：如有变更请评估影响性并同步修改补丁代码
 */
Status SvQueryView(SvInstanceT *inst, const ViewReqT *viewReq, void *vertex, SvCursorT *cursor)
{
    DB_POINTER3(viewReq, vertex, cursor);
    SvDefT *view = NULL;
    Status ret = SvGetViewByName(inst, viewReq->labelName.str, &view);
    if (ret != GMERR_OK) {
        return ret;
    }
    DB_ASSERT(view->queryProc != NULL);
    return view->queryProc(vertex, cursor);
}

Status SvQueryGmsysView(SvInstanceT *inst, char *metaName, DmVertexT *dmVertex, SvCursorT *cursor)
{
    DB_POINTER4(inst, metaName, dmVertex, cursor);
    SvDefT *view = NULL;
    Status ret = SvGetViewByName(inst, metaName, &view);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Has no view name %s.", metaName);
        return ret;
    }

    if (view->queryProc == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "View :%s proc func.", metaName);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (cursor->stmt == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "View :%s stmt of cursor.", metaName);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    return view->queryProc((void *)dmVertex, cursor);
}

// init the sysview
Status SvInit(SvInstanceT *inst, uint16_t instanceId)
{
    DB_POINTER(inst);

    DbSpinInit(&inst->lock);
    inst->count = 0;
    DbMemCtxT *sysDynCtx = DbSrvGetSysDynCtx(instanceId);
    if (sysDynCtx == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get sys dyn context.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DbMemCtxArgsT args = {0};
    args.dynCtxNeedRecycle = true;
    args.liteModOn = true;
    inst->memCtx = DbCreateDynMemCtx(sysDynCtx, true, "sysview_root_memory_context", &args);
    if (inst->memCtx == NULL) {
        DB_LOG_ERROR(GMERR_NULL_VALUE_NOT_ALLOWED, "Create Dynamic MemCtx!");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    Status ret = DbOamapInit(&inst->viewMap, DB_VIEW_NUM_INIT, DbOamapUint32Compare, inst->memCtx, true);
    if (ret != GMERR_OK) {
        DbDeleteDynMemCtx(inst->memCtx);
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "DbOamap Init!");
        return GMERR_DATA_EXCEPTION;
    }

    inst->vertexLabelPaser = NULL;
    inst->vertexLabelCreate = NULL;
    inst->vertexLabelRelease = NULL;
    return GMERR_OK;
}

// destroy the sysview instance
void SvDestroy(SvInstanceT *inst)
{
    DB_POINTER(inst);

    DbOamapIteratorT iter = 0;
    char *svName = NULL;
    SvDefT *viewEntry = NULL;

    DbSpinLock(&inst->lock);

    DbOamapResetIterator(&iter);
    while (DbOamapFetch(&inst->viewMap, &iter, (void **)&svName, (void **)&viewEntry) == GMERR_OK) {
        viewEntry = DbOamapRemove(&inst->viewMap, SvHashViewName(svName), svName);
        if (viewEntry == NULL) {
            DB_LOG_ERROR(GMERR_NULL_VALUE_NOT_ALLOWED, "%s", svName);
            continue;
        }
        if (inst->vertexLabelRelease != NULL) {
            DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(inst->memCtx);
            inst->vertexLabelRelease(dbInstance, viewEntry->svName);
        }
        inst->count--;
    }

    DbOamapDestroy(&inst->viewMap);
    DbDeleteDynMemCtx(inst->memCtx);
    DbSpinUnlock(&inst->lock);
}

void SvReset(SvInstanceT *inst)
{
    DB_POINTER(inst);

    DbOamapIteratorT iter = 0;
    char *svName = NULL;
    SvDefT *viewEntry = NULL;
    DbSpinLock(&inst->lock);
    DbOamapResetIterator(&iter);
    while (DbOamapFetch(&inst->viewMap, &iter, (void **)&svName, (void **)&viewEntry) == GMERR_OK) {
        viewEntry->isCreatedTable = false;
    }
    DbSpinUnlock(&inst->lock);
}

#ifdef __cplusplus
}
#endif
