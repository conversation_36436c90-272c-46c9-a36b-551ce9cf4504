/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: db_resource_session.c
 * Description: source file for resource session
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * Create: 2021-4-15
 */
#include "db_resource_session.h"
#include "db_resource_session_pub.h"
#include "common_log.h"
#include "adpt_atomic.h"
#include "db_config.h"
#include "db_crash_debug.h"
#include "db_label_latch_mgr.h"
#include "adpt_memory.h"
#if defined(FEATURE_HAC) && defined(ACCELERATOR_MODE)
#include "db_accelerator_pub.h"
#endif
#include "db_instance.h"

#define DB_RESOURCE_SESSION_SHM_CTX_BASE_SIZE (1024 * 256)
#define DB_RESOURCE_SESSION_SHM_CTX_STEP_SIZE (1024 * 1024)
#define RW_LATCH_TRY_TIMES 500

Status DbSessionMgrCreate(DbMemCtxT *topShmMemCtx)
{
    DbMemCtxArgsT shmMemCtxArgs = {0};
    DbInstanceT *dbInstance = (DbInstanceT *)DbGetInstanceByMemCtx(topShmMemCtx);
    shmMemCtxArgs.instanceId = DbGetInstanceId(dbInstance);
    shmMemCtxArgs.ctxId = DB_SE_RES_SESSION_SHM_CTX_ID;

    // Use private memory context setting
    char param[sizeof(DbBlockMemParamT)] = {0};
    DbBlockMemParamT *blockParam = (DbBlockMemParamT *)param;
    blockParam->isHugePage = false;
    blockParam->baseSize = DB_RESOURCE_SESSION_SHM_CTX_BASE_SIZE;
    blockParam->stepSize = DB_RESOURCE_SESSION_SHM_CTX_STEP_SIZE;
    blockParam->isReused = true;
    blockParam->allowBigChunk = false;
    uint64_t maxSize = DbGetTopShmemMaxSize(DbGetInstanceId(dbInstance));
    // limitSize为memCtx通过配置参数计算的内存上限，创建的时候maxSize不能超过该值。
    uint64_t limitSize = blockParam->baseSize + (uint64_t)blockParam->stepSize * (DbMemCtxGetBlockPoolMaxSegNum() - 1);
    limitSize = DB_MIN(UINT32_MAX, limitSize);  // 参数数值不超过uint32
    blockParam->maxSize = (uint64_t)DB_MIN(maxSize, limitSize);
    blockParam->blkPoolType = BLK_NORMAL;
    AlgoParamT algoParam;
    algoParam.blockParam = blockParam;
    shmMemCtxArgs.algoParam = &algoParam;

    // memCtx用途：用于创建session的管理结构体
    // 生命周期：长进程级别
    // 释放方式：兜底清空
    // 兜底清空措施：依赖上层seTopShmMemCtx, server退出时销毁
    DbMemCtxT *sessionShmMemCtx = DbCreateBlockPoolShmemCtx(topShmMemCtx, "sessionShmMemCtxArgs", &shmMemCtxArgs);
    if (sessionShmMemCtx == NULL) {
        // "COM: resSessionMgr alloc ShmemCtx worthless, Args(%" PRIu32 ", %" PRIu32 ")"
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Alloc ShmemCtx, Args(%" PRIu32 ", %" PRIu32 ")",
            shmMemCtxArgs.instanceId, shmMemCtxArgs.ctxId);
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    ShmemPtrT resSessionMgrShm =
        DbShmemStructAllocById(sessionShmMemCtx, sizeof(DbSessionMgr), DB_SE_RES_SESSION_SHM_STRUCT_ID);
    DbSessionMgr *resSessionMgr = (DbSessionMgr *)DbShmPtrToAddr(resSessionMgrShm);
    if (resSessionMgr == NULL) {
        DbShmemStructFreeById(sessionShmMemCtx, resSessionMgrShm, DB_SE_RES_SESSION_SHM_STRUCT_ID);
        DbDeleteShmemCtx(sessionShmMemCtx);
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Get resSessionMgr. (%" PRIu32 ", %" PRIu32 ")",
            resSessionMgrShm.segId, resSessionMgrShm.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    Status ret = DbSessionMgrInit(resSessionMgr, DB_SE_RES_SESSION_SHM_CTX_ID, resSessionMgrShm, dbInstance);
    if (ret != GMERR_OK) {
        DbShmemStructFreeById(sessionShmMemCtx, resSessionMgrShm, DB_SE_RES_SESSION_SHM_STRUCT_ID);
        DbDeleteShmemCtx(sessionShmMemCtx);
        DB_LOG_ERROR(
            ret, "Create resSessionMgr, Args(%" PRIu32 ", %" PRIu32 ")", shmMemCtxArgs.instanceId, shmMemCtxArgs.ctxId);
    }
    return ret;
}

DbSessionT *GetSessionFromSessionPool(ShmemPtrT *sessionMemPool, uint32_t sessionId)
{
    DB_POINTER(sessionMemPool);
    ShmemPtrT sessionMemPtr = sessionMemPool[sessionId];
    if (DbIsShmPtrValid(sessionMemPtr)) {
        return (DbSessionT *)DbShmPtrToAddr(sessionMemPtr);
    }
    return NULL;
}

static DbSessionT *AllocResourceSession(ShmemPtrT *sessionMemPool, DbMemCtxT *shmMemCtx, uint32_t sessionId)
{
    DB_POINTER(sessionMemPool);
    // 长生命周期内存，随着服务端进程销毁，释放该内存。目前是存放预置的session ShmemPtrT（MAX_BG_WORKER_NUM + 1）
    ShmemPtrT newSessionMem = DbShmemCtxAlloc((DbMemCtxT *)shmMemCtx, (uint32_t)sizeof(DbSessionT));
    sessionMemPool[sessionId] = newSessionMem;
    DbSessionT *newSession = (DbSessionT *)DbShmPtrToAddr(newSessionMem);
    if (newSession == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Get newSession, segId: %" PRIu32 ", offset: %" PRIu32,
            newSessionMem.segId, newSessionMem.offset);
        return NULL;
    }

    newSession->id = sessionId;
    newSession->currAccessDsId = DB_INVALID_UINT32;
    newSession->isFree = true;
    newSession->shmLatchStack = DB_INVALID_SHMPTR;
    newSession->shmRefArray = DB_INVALID_SHMPTR;
    newSession->nextSessionId = sessionId == 0u ? (uint32_t)DB_INVALID_SESSION_ID : (sessionId + 1);
    newSession->shmRole = DB_INVALID_SHMPTR;
    newSession->role = NULL;
    newSession->privPolicyMode = (uint32_t)DB_INVALID_UINT32;
    newSession->dwTrxSlot = DB_INVALID_UINT16;
    return newSession;
}

Status DbSessionMgrInit(DbSessionMgr *mgr, uint32_t ctxId, ShmemPtrT shmPtr, DbInstanceHdT dbInstance)
{
    DB_POINTER(mgr);
    (void)memset_s(mgr, sizeof(DbSessionMgr), 0, sizeof(DbSessionMgr));
    void *shmMemCtx = DbGetShmemCtxById(ctxId, DbGetInstanceId(dbInstance));
    if (shmMemCtx == NULL) {
        // "COM: Get ShmMemCtx from DbSessionMgr worthless shmCtxId(%" PRIu32 ")."
        DB_LOG_ERROR(GMERR_NO_DATA, "Get ShmMemCtx, shmCtxId(%" PRIu32 ").", mgr->shmCtxId);
        return GMERR_NO_DATA;
    }
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(dbInstance);
    DbCfgValueT connMax = {0};
    Status ret = DbCfgGet(cfgHandle, DB_CFG_CONN_MAX, &connMax);
    if (ret != GMERR_OK) {
        // "worthless to get config value when init sessionMgr."
        DB_LOG_ERROR(ret, "Get config value.");
        return ret;
    }

    // 客户端连接线程个数+后台线程线程个数
    mgr->maxNums = (uint16_t)connMax.int32Val + MAX_BG_WORKER_NUM;
#ifdef FEATURE_REPLICATION
    // 主备复制，重演最多使用和主机连接数+后线程一样的session
    if (DbCfgGetBoolLite(DB_CFG_ENABLE_REPLICATION, dbInstance)) {
        mgr->maxNums *= 2;
    }
#endif
    mgr->maxNums += 1;  // 一个id为0预留线程. 因为sessionId为0不能作为锁的owner

    // 需要保证连接的规格上限不会计算溢出造成反转,添加编译期检查
    static_assert((MAX_CONN_NUM + MAX_BG_WORKER_NUM) * 2 + 1 < DB_MAX_UINT16, "mgr->maxNums out of range");
    // 长生命周期内存，随着服务端进程销毁，释放该内存
    // 存放的是ShmemPtrT指针
    mgr->sessionPoolShmPtr = DbShmemCtxAlloc((DbMemCtxT *)shmMemCtx, (uint32_t)sizeof(ShmemPtrT) * mgr->maxNums);
    ShmemPtrT *sessionMemPool = (ShmemPtrT *)DbShmPtrToAddr(mgr->sessionPoolShmPtr);
    if (sessionMemPool == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "Alloc session pool.",
            "|SESSION_DEBUG:%s| Alloc session pool when init sessionMgr.",
            "Session_DbSessionMgrInit_1, segId: %" PRIu32 ", offset: %" PRIu32, mgr->sessionPoolShmPtr.segId,
            mgr->sessionPoolShmPtr.offset);
        return GMERR_INTERNAL_ERROR;
    }

    for (uint32_t i = 0; i < mgr->maxNums; ++i) {
        sessionMemPool[i] = DB_INVALID_SHMPTR;
    }
    // session id 为0由于id值与锁的idle状态相同，因此预留
    mgr->firstFreeId = DB_FIRST_SESSION_ID;
    mgr->usedCnt = 0;
    mgr->shmCtxId = ctxId;

    DbRWSpinInit(&mgr->lock);
    return GMERR_OK;
}

void DbShmRefArrayInit(DbShmRefArrayT *array, uint32_t count, uint32_t shmCtxId)
{
    DB_POINTER(array);
    array->shmCtxId = shmCtxId;
    array->firstFreeDescId = INVALID_SHMREF_DESC_ID;
    array->capacity = DEFAULT_SHMREF_DESC_COUNT;
    array->extShmPtr = DB_INVALID_SHMPTR;
    array->extendCount = count;
    array->curPos = 0;
    array->usedCnt = 0;
    array->extendTimes = 0;
    for (uint32_t i = 0; i < DEFAULT_SHMREF_DESC_COUNT; i++) {
        array->cacheDescs[i].nextDescId = INVALID_SHMREF_DESC_ID;
        array->cacheDescs[i].item.shmType = REF_SHM_INVALID;
        array->cacheDescs[i].item.shmAddr = DB_INVALID_SHMPTR;
        array->cacheDescs[i].item.refCount = 0;
    }
}

void DbShmRefArrayDestroy(DbShmRefArrayT *array, DbInstanceHdT dbInstance)
{
    DB_POINTER(array);
    DbMemCtxT *shmMemCtx = (DbMemCtxT *)DbGetShmemCtxById(array->shmCtxId, DbGetInstanceId(dbInstance));
    if (shmMemCtx == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Get shmMemCtx by Id: %" PRIu32 ".", array->shmCtxId);
        DB_ASSERT(false);
        return;
    }
    if (DbIsShmPtrValid(array->extShmPtr)) {
        DbShmemCtxFree(shmMemCtx, array->extShmPtr);
        array->extShmPtr = DB_INVALID_SHMPTR;
    }
}

Status DbShmRefArrayGetItemById(DbShmRefArrayT *array, ShmRefDescT **desc, uint32_t itemId)
{
    DB_POINTER2(array, desc);
    if (itemId > array->curPos) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Unsound refArray ItemId: %" PRIu32 ", curPos is %" PRIu32 ".", itemId,
            array->curPos);
        return GMERR_DATA_EXCEPTION;
    }
    if (itemId < DEFAULT_SHMREF_DESC_COUNT) {
        *desc = &array->cacheDescs[itemId];
    } else {
        ShmRefDescT *dynShmArray = DbShmPtrToAddr(array->extShmPtr);
        if (dynShmArray == NULL) {
            DB_LOG_ERROR(GMERR_INTERNAL_ERROR,
                "get dynShmArray by shmPtr: (segId: %" PRIu32 ", offset: %" PRIu32 ") worthless",
                array->extShmPtr.segId, array->extShmPtr.offset);
            return GMERR_INTERNAL_ERROR;
        }
        *desc = &dynShmArray[itemId - DEFAULT_SHMREF_DESC_COUNT];
    }
    return GMERR_OK;
}

Status DbShmRefArrayScaleOut(DbShmRefArrayT *array)
{
    DB_POINTER(array);
    if (!DbCommonIsServer()) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "client alloc share memory.");
        return GMERR_INTERNAL_ERROR;
    }
    DbMemCtxT *shmMemCtx = (DbMemCtxT *)DbGetShmemCtxById(array->shmCtxId, DbGetProcGlobalId());
    if (shmMemCtx == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Get shmMemCtx by Id: %" PRIu32, array->shmCtxId);
        DB_ASSERT(false);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    uint32_t oldDynArrayCount = (array->capacity - DEFAULT_SHMREF_DESC_COUNT);
    uint32_t newDynArrayCount = 0;
    if (oldDynArrayCount == 0) {
        // 第一次扩容
        newDynArrayCount = array->extendCount << 1;
    } else {
        newDynArrayCount = oldDynArrayCount << 1;
    }
    uint32_t oldArraySize = (uint32_t)(oldDynArrayCount * sizeof(ShmRefDescT));
    uint32_t newArraySize = (uint32_t)(newDynArrayCount * sizeof(ShmRefDescT));

    ShmemPtrT oldExtShmPtr = array->extShmPtr;
    // 长生命周期，shmRefArray当前不具备缩容功能，会在服务端进程销毁时，释放该内存
    ShmemPtrT newExtShmPtr = DbShmemCtxAlloc(shmMemCtx, newArraySize);

    ShmRefDescT *oldDynArray = DbShmPtrToAddr(array->extShmPtr);
    ShmRefDescT *newDynArray = (ShmRefDescT *)DbShmPtrToAddr(newExtShmPtr);

    if (newDynArray == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED,
            "scale out newDynArray, segId: %" PRIu32 ", offset: %" PRIu32, newExtShmPtr.segId, newExtShmPtr.offset);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(newDynArray, newArraySize, 0, newArraySize);
    if (oldDynArray != NULL) {
        errno_t err = memcpy_s(newDynArray, newArraySize, oldDynArray, oldArraySize);
        if (err != EOK) {
            DbShmemCtxFree(shmMemCtx, newExtShmPtr);
            // "memcpy_s worthless when share array scale out."
            DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "memcpy_s when share array scale out.");
            return GMERR_INTERNAL_ERROR;
        }
    }
    // 原子操作完成新数组和旧数组的切换
    DbAtomicSet64(&array->extShmAddr, *(uint64_t *)&newExtShmPtr);
    if (DbIsShmPtrValid(oldExtShmPtr)) {
        // 释放旧扩充数组
        DbShmemCtxFree(shmMemCtx, oldExtShmPtr);
    }
    DbAtomicSet(&array->capacity, (DEFAULT_SHMREF_DESC_COUNT + newDynArrayCount));
    array->extendTimes++;
    return GMERR_OK;
}

Status DbShmRefArrayGetNewItem(DbShmRefArrayT *array, uint32_t *itemId, ShmRefDescT **desc)
{
    DB_POINTER3(array, itemId, desc);
    Status ret;
    if (array->firstFreeDescId != INVALID_SHMREF_DESC_ID) {
        // 从空闲链表中获取一个描述符
        ret = DbShmRefArrayGetItemById(array, desc, array->firstFreeDescId);
        if (ret != GMERR_OK) {
            return ret;
        }
        *itemId = array->firstFreeDescId;
        array->firstFreeDescId = (*desc)->nextDescId;
    } else {
        if (array->curPos >= MAX_SHMREF_DESC_ID) {
            DB_LOG_ERROR(GMERR_INSUFFICIENT_RESOURCES, "out of max array curPos: %" PRIu32 ".", array->curPos);
            return GMERR_INSUFFICIENT_RESOURCES;
        }
        if (array->curPos >= array->capacity) {
            // 此时，动态数组已经被使用的元素个数应与其容量相等. 需要扩充动态数组
            DB_ASSERT(array->usedCnt == array->capacity);
            ret = DbShmRefArrayScaleOut(array);
            if (ret != GMERR_OK) {
                // "scale out dynamic share array worthless when shm ref array get new item."
                DB_LOG_ERROR(ret, "scale out ShmRefArray.");
                return ret;
            }
        }
        DB_ASSERT(array->curPos < array->capacity);
        ret = DbShmRefArrayGetItemById(array, desc, array->curPos);
        if (ret != GMERR_OK) {
            return ret;
        }
        *itemId = array->curPos;
        array->curPos++;
    }
    array->usedCnt++;
    (*desc)->nextDescId = INVALID_SHMREF_DESC_ID;
    return GMERR_OK;
}

// 不用判断当前表是否已经在array中有位置了，只要array位置已经满了就需要扩充array
bool DbShmRefArrayExpCheck(const DbSessionCtxT *sessionCtx, uint32_t *usedCnt)
{
    DB_POINTER(sessionCtx);
    DbShmRefArrayT *array = sessionCtx->refArray;
    DB_POINTER(array);

    if (array->firstFreeDescId != INVALID_SHMREF_DESC_ID) {
        return false;
    } else {
        DB_ASSERT(array->curPos < MAX_SHMREF_DESC_ID);
        if (array->curPos >= array->capacity) {
            // 此时，动态数组已经被使用的元素个数应与其容量相等. 需要扩充动态数组
            DB_ASSERT(array->usedCnt == array->capacity);
            *usedCnt = array->usedCnt;
            return true;
        }
        return false;
    }
}

Status DbShmRefArrayExpand(DbSessionCtxT *sessionCtx, uint32_t usedCnt)
{
    DB_POINTER(sessionCtx);
    DbShmRefArrayT *array = sessionCtx->refArray;
    DB_POINTER(array);
    uint32_t usedCntServe = 0;
    if (DbShmRefArrayExpCheck(sessionCtx, &usedCntServe) && (usedCntServe == usedCnt)) {
        Status ret = DbShmRefArrayScaleOut(array);
        if (ret != GMERR_OK) {
            // "scale out dynamic share array worthless when shem ref array expand."
            DB_LOG_ERROR(ret, "scale out ShmRefArray.");
            return ret;
        }
    } else {
        // "param from client does not match that of server when expand shm."
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "param unmatch.");
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

Status DbShmRefArrayRemoveItem(DbShmRefArrayT *array, uint32_t itemId)
{
    DB_POINTER(array);
    ShmRefDescT *desc = NULL;
    Status ret = DbShmRefArrayGetItemById(array, &desc, itemId);
    if (ret != GMERR_OK) {
        return ret;
    }
    DB_POINTER(desc);
    DB_ASSERT(desc->nextDescId == INVALID_SHMREF_DESC_ID);
    if (array->firstFreeDescId == INVALID_SHMREF_DESC_ID) {
        desc->nextDescId = INVALID_SHMREF_DESC_ID;
    } else {
        desc->nextDescId = array->firstFreeDescId;
    }
    array->firstFreeDescId = itemId;
    array->usedCnt--;
    return GMERR_OK;
}

inline static bool DbShmRefArrayCompareItem(const ShmRefItem *item1, const ShmRefItem *item2)
{
    DB_POINTER2(item1, item2);

    return (item1->shmType == item2->shmType) && (item1->shmAddr.segId == item2->shmAddr.segId) &&
           (item1->shmAddr.offset == item2->shmAddr.offset);
}

bool DbShmRefArrayLookUp(DbShmRefArrayT *array, const ShmRefItem *item, uint32_t *descId, ShmRefDescT **desc)
{
    DB_POINTER4(array, item, descId, desc);
    DB_ASSERT(item->shmType != REF_SHM_INVALID);
    ShmRefDescT *descTemp = NULL;
    uint32_t id = 0;
    for (; id < array->curPos && id < DEFAULT_SHMREF_DESC_COUNT; ++id) {
        descTemp = &array->cacheDescs[id];
        if (DbShmRefArrayCompareItem(&descTemp->item, item)) {
            *descId = id;
            *desc = descTemp;
            return true;
        }
    }
    if (array->extendTimes == 0) {
        return false;
    }
    // 从扩充数组中去查找
    ShmRefDescT *dynShmArray = DbShmPtrToAddr(array->extShmPtr);
    if (dynShmArray == NULL) {
        // "get extent shared array worthless, (segId: %" PRIu32 ", offset: %" PRIu32 ")."
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Get extShmArr, (segId: %" PRIu32 ", offset: %" PRIu32 ").",
            array->extShmPtr.segId, array->extShmPtr.offset);
        return false;
    }
    if (id < DEFAULT_SHMREF_DESC_COUNT) {
        return false;
    }
    for (; id < array->curPos; ++id) {
        descTemp = &dynShmArray[id - DEFAULT_SHMREF_DESC_COUNT];
        if (DbShmRefArrayCompareItem(&descTemp->item, item)) {
            *descId = id;
            *desc = descTemp;
            return true;
        }
    }
    return false;
}

Status DbSessionAllocRole(const DbSessionMgr *mgr, uint32_t size, DbSessionT *dbSession)
{
    DB_POINTER2(mgr, dbSession);
    void *shmMemCtx = DbGetShmemCtxById(mgr->shmCtxId, DbGetProcGlobalId());
    if (shmMemCtx == NULL) {
        // "worthless to get the share memory context by instanceId(%" PRIu16 ")"
        DB_LOG_ERROR(GMERR_NO_DATA, "Get shmMemCtx by instanceId(%" PRIu16 ")", DbGetProcGlobalId());
        return GMERR_NO_DATA;
    }
    DB_ASSERT(!dbSession->isFree);
    // 在session销毁时，释放该内存，调用DbSessionFree函数
    dbSession->shmRole = DbShmemCtxAlloc((DbMemCtxT *)shmMemCtx, size);
    dbSession->role = DbShmPtrToAddr(dbSession->shmRole);
    if (dbSession->role == NULL) {
        // "worthless to alloc dbsession role, sessionID(%" PRIu32 "), segId: %" PRIu32 ", offset: %" PRIu32,
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
            "Alloc dbsession role, sessionID(%" PRIu32 "), segId: %" PRIu32 ", offset: %" PRIu32, dbSession->id,
            dbSession->shmRole.segId, dbSession->shmRole.offset);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(dbSession->role, size, 0, size);
    return GMERR_OK;
}

void DbSessionNext(DbSessionMgr *mgr, DbSessionT *session)
{
    mgr->firstFreeId = session->nextSessionId;
    mgr->usedCnt++;
    session->nextSessionId = DB_INVALID_SESSION_ID;
    session->isFree = false;
}

static DbSessionT *TryAlloclSessionFromSessionPool(DbSessionMgr *mgr, DbMemCtxT *shmMemCtx, uint32_t sessionId)
{
    DB_POINTER(mgr);
    if (sessionId >= mgr->maxNums) {
        DB_LOG_AND_SET_LASERR(GMERR_NO_DATA, "Exceed max(%" PRIu32 ").", mgr->maxNums);
        return NULL;
    }
    if (sessionId == DB_INVALID_SESSION_ID) {
        // "COM: No more resSessions can be allocated useCnt(%" PRIu32 ")."
        DB_LOG_AND_SET_LASERR(GMERR_NO_DATA, "Alloc resSessions, useCnt(%" PRIu32 ").", mgr->usedCnt);
        return NULL;
    }

    ShmemPtrT *sessionMemPool = (ShmemPtrT *)DbShmPtrToAddr(mgr->sessionPoolShmPtr);
    if (sessionMemPool == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR,
            "session pool shmPtr cannot map, segId: %" PRIu32 ", offset: %" PRIu32, mgr->sessionPoolShmPtr.segId,
            mgr->sessionPoolShmPtr.offset);
        return NULL;
    }
    ShmemPtrT sessionMemPtr = sessionMemPool[sessionId];
    if (DbIsShmPtrValid(sessionMemPtr)) {
        return (DbSessionT *)DbShmPtrToAddr(sessionMemPtr);
    }
    return AllocResourceSession(sessionMemPool, shmMemCtx, sessionId);
}

DbSessionT *DbSessionAlloc(DbSessionMgr *mgr, DbInstanceHdT dbInstance)
{
    DB_POINTER(mgr);
    DbRWSpinWLock(&mgr->lock);
    DbMemCtxT *shmMemCtx = (DbMemCtxT *)DbGetShmemCtxById(mgr->shmCtxId, DbGetInstanceId(dbInstance));
    if (shmMemCtx == NULL) {
        DbRWSpinWUnlock(&mgr->lock);
        // "COM: Get ShmMemCtx from DbSessionMgr worthless shmCtxId(%" PRIu32 ")"
        DB_LOG_AND_SET_LASERR(GMERR_NO_DATA, "Get ShmMemCtx, shmCtxId(%" PRIu32 ")", mgr->shmCtxId);
        return NULL;
    }

    DbSessionT *session = TryAlloclSessionFromSessionPool(mgr, shmMemCtx, mgr->firstFreeId);
    if (session == NULL) {
        DbRWSpinWUnlock(&mgr->lock);
        DB_LOG_AND_SET_LASERR(
            GMERR_INTERNAL_ERROR, "session pool get sessionID(%" PRIu32 ") unexpected", mgr->firstFreeId);
        return NULL;
    }
    // 开辟一个用于记录main store中latch信息的锁栈并初始化
    // 在session销毁时，释放该内存，调用DbSessionFree函数
    session->shmLatchStack = DbShmemCtxAlloc(shmMemCtx, sizeof(LatchStackT));
    LatchStackT *latchStack = (LatchStackT *)DbShmPtrToAddr(session->shmLatchStack);
    if (latchStack == NULL) {
        DbRWSpinWUnlock(&mgr->lock);
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "Alloc latch stack, sessionID(%" PRIu32 "), segId: %" PRIu32 ", offset: %" PRIu32, session->id,
            session->shmLatchStack.segId, session->shmLatchStack.offset);
        return NULL;
    }
    (void)memset_s(latchStack, sizeof(LatchStackT), 0, sizeof(LatchStackT));
    // 开辟一个用于记录存储引擎容器引用计数的动态数组
    // 在session销毁时，释放该内存，调用DbSessionFree函数
    session->shmRefArray = DbShmemCtxAlloc(shmMemCtx, sizeof(DbShmRefArrayT));
    DbShmRefArrayT *refArray = (DbShmRefArrayT *)DbShmPtrToAddr(session->shmRefArray);
    if (refArray == NULL) {
        DbShmemCtxFree(shmMemCtx, session->shmLatchStack);
        DbRWSpinWUnlock(&mgr->lock);
        // "Alloc share memory reference array worthless, sessionID(%" PRIu32 "), segId: %" PRIu32
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "Alloc shmRefArray, sessionID(%" PRIu32 "), segId: %" PRIu32 ", offset: %" PRIu32, session->id,
            session->shmRefArray.segId, session->shmRefArray.offset);
        return NULL;
    }
    DB_ASSERT(session->dwTrxSlot == DB_INVALID_UINT16);
    session->dwTrxSlot = DB_INVALID_UINT16;
    DbShmRefArrayInit(refArray, DEFAULT_SHMREF_DESC_COUNT, mgr->shmCtxId);
    DbSessionNext(mgr, session);
    DbRWSpinWUnlock(&mgr->lock);
    return session;
}

static inline void DbSessionFreeLatchStack(DbSessionT *session, DbMemCtxT *shmMemCtx)
{
#ifndef NDEBUG
    LatchStackT *latchStack = (LatchStackT *)DbShmPtrToAddr(session->shmLatchStack);
    if (latchStack != NULL) {
        latchStack->pid = 0;
    }
#endif
    DbShmemCtxFree(shmMemCtx, session->shmLatchStack);  // 销毁清空锁栈信息
    session->shmLatchStack = DB_INVALID_SHMPTR;
}

void DbSessionFree(DbSessionMgr *mgr, uint32_t sessionId, DbInstanceHdT dbInstance)
{
    DB_POINTER(mgr);
    DB_ASSERT(sessionId < mgr->maxNums);
    DbRWSpinWLock(&mgr->lock);
    ShmemPtrT *sessionMemPool = DbShmPtrToAddr(mgr->sessionPoolShmPtr);
    if (sessionMemPool == NULL) {
        // "get sessionPool worthless when session free, segId: %" PRIu32 ", offset: %" PRIu32,
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "Get sessionPool, segId: %" PRIu32 ", offset: %" PRIu32,
            mgr->sessionPoolShmPtr.segId, mgr->sessionPoolShmPtr.offset);
        goto END;
    }
    DbSessionT *session = GetSessionFromSessionPool(sessionMemPool, sessionId);
    if (session == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "session pool get sessionID(%" PRIu32 ") unexpected", sessionId);
        goto END;
    }
    session->nextSessionId = mgr->firstFreeId;
    mgr->firstFreeId = session->id;
    DB_ASSERT(session->dwTrxSlot == DB_INVALID_UINT16);
    DB_ASSERT(mgr->usedCnt > 0);
    mgr->usedCnt--;

    DbMemCtxT *shmMemCtx = (DbMemCtxT *)DbGetShmemCtxById(mgr->shmCtxId, DbGetInstanceId(dbInstance));
    if (shmMemCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "Get memCtx. ctxId: %" PRIu32, mgr->shmCtxId);
        DB_ASSERT(false);
        goto END;
    }
    // 销毁锁栈共享内存addr
    DbSessionFreeLatchStack(session, shmMemCtx);

    // 销毁共享内存引用计数数组
    DbShmRefArrayT *refArray = (DbShmRefArrayT *)DbShmPtrToAddr(session->shmRefArray);
    if (refArray == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "ref array shmPtr is unsound, segId: %" PRIu32 ", offset: %" PRIu32,
            session->shmRefArray.segId, session->shmRefArray.offset);
        goto END;
    }
    DbShmRefArrayDestroy(refArray, dbInstance);
    DbShmemCtxFree(shmMemCtx, session->shmRefArray);  // 销毁清除引用计数数组
    session->shmRefArray = DB_INVALID_SHMPTR;

    if (DbIsShmPtrValid(session->shmRole)) {
        DbShmemCtxFree(shmMemCtx, session->shmRole);
        session->shmRole = DB_INVALID_SHMPTR;
        session->role = NULL;
    }
    session->isFree = true;
END:
    DbRWSpinWUnlock(&mgr->lock);
}

void DbSessionCtxInit(DbSessionCtxT *ctx, const DbSessionCtxCfgT *cfg)
{
    DB_POINTER(ctx);
    // sessionMgr外部有判空。
    ctx->sessionMgr = DbGetShmemStructById(DB_SE_RES_SESSION_SHM_STRUCT_ID, cfg->instanceId);
    ctx->isOpenSession = false;
    ctx->isDirectRead = cfg->isDirectRead;
    ctx->isDirectWrite = cfg->isDirectWrite;
    ctx->sessionPool = NULL;
    ctx->session = NULL;
    ctx->latchStack = NULL;
    ctx->refArray = NULL;
    ctx->lastDescId = INVALID_SHMREF_DESC_ID;
    ctx->lastRefDesc = NULL;
    ctx->pageMgr = cfg->pageMgr;
    ctx->getPageLatchFunc = cfg->getPageLatchFunc;
    ctx->leavePageLatchFunc = cfg->leavePageLatchFunc;
}

bool DbSessionFindShmRefDesc(
    const DbSessionCtxT *sessionCtx, const ShmRefItem *item, uint32_t *descId, ShmRefDescT **desc)
{
    DB_POINTER4(sessionCtx, item, descId, desc);
    DB_ASSERT(item->shmType != REF_SHM_INVALID);
    if (sessionCtx->lastRefDesc != NULL) {
        if (DbShmRefArrayCompareItem(&sessionCtx->lastRefDesc->item, item)) {
            *descId = sessionCtx->lastDescId;
            *desc = sessionCtx->lastRefDesc;
            return true;
        }
    }
    return DbShmRefArrayLookUp(sessionCtx->refArray, item, descId, desc);
}

void DbSessionSetShmRefItem(ShmRefItem *dstItem, const ShmRefItem *srcItem, bool isIncrease)
{
    DB_POINTER2(dstItem, srcItem);
    if (isIncrease) {
        dstItem->refCount += srcItem->refCount;
        dstItem->latchOffset = srcItem->latchOffset;
        dstItem->openCntOffset = srcItem->openCntOffset;
        dstItem->shmAddr = srcItem->shmAddr;
        dstItem->labelLatchVersionId = srcItem->labelLatchVersionId;
        dstItem->labelLatchShmAddr = srcItem->labelLatchShmAddr;
        COMPILER_BARRIER;
        dstItem->shmType = srcItem->shmType;
    } else {
        if (dstItem->refCount >= srcItem->refCount) {
            dstItem->refCount -= srcItem->refCount;
        } else {
            DB_ASSERT(false);
            dstItem->refCount = 0;
        }
    }
}

Status DbSessionUpdateShmRefCount(DbSessionCtxT *sessionCtx, const ShmRefItem *item, bool isIncrease)
{
    DB_POINTER2(sessionCtx, item);
    Status ret;
    uint32_t descId;
    ShmRefDescT *desc = NULL;
    bool isFound = DbSessionFindShmRefDesc(sessionCtx, item, &descId, &desc);
    if (!isFound && isIncrease) {
        ret = DbShmRefArrayGetNewItem(sessionCtx->refArray, &descId, &desc);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Suffer a defeat to get new a refArray desc.");
            return ret;
        }
        desc->item.shmType = REF_SHM_INVALID;
        desc->item.refCount = 0;
    }
    if (desc == NULL) {
        // "session update opencount worthless. Suffer a defeat to find a valid desc."
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "No valid desc.");
        return GMERR_DATA_EXCEPTION;
    }
    DbSessionSetShmRefItem(&desc->item, item, isIncrease);
    if (desc->item.refCount == 0) {
        desc->item.shmType = REF_SHM_INVALID;
        ret = DbShmRefArrayRemoveItem(sessionCtx->refArray, descId);
        // 预期一定成功
        DB_ASSERT(ret == GMERR_OK);
        sessionCtx->lastRefDesc = NULL;
        sessionCtx->lastDescId = INVALID_SHMREF_DESC_ID;
    } else {
        sessionCtx->lastRefDesc = desc;
        sessionCtx->lastDescId = descId;
    }
    return GMERR_OK;
}

inline static DbSpinLockT *DbGetSpinlockAddr(ShmemPtrT addrBase, uint32_t offset)
{
    ShmemPtrT latchAddr = addrBase;
    GET_MEMBER_SHMPTR(latchAddr, offset);
    if (!DbIsShmPtrValid(latchAddr)) {
        return NULL;
    }
    return (DbSpinLockT *)DbShmPtrToAddr(latchAddr);
}

inline static uint32_t *DbGetOpenCntByShmPtrAndOffset(ShmemPtrT addrBase, uint32_t offset)
{
    ShmemPtrT refAddr = addrBase;
    GET_MEMBER_SHMPTR(refAddr, offset);
    if (!DbIsShmPtrValid(refAddr)) {
        return NULL;
    }
    return (uint32_t *)DbShmPtrToAddr(refAddr);
}

inline static uint32_t DbGetSpinlockOwner(const DbSpinLockT *lock)
{
    return lock->lock;
}

// 只有服务端会调用这个接口,用于客户端异常退出后服务端清理会话锁资源使用
uint8_t *DbSessionGetLatchAddr(const DbSessionCtxT *ctx, LatchStackItemT *item)
{
#if defined(FEATURE_HAC) && defined(ACCELERATOR_MODE)
    if (item->latchType == LATCH_ADDR_ACCELERATOR_LOCK) {  // 硬件锁没有显示addr
        return NULL;
    }
#endif
    if (!DbIsShmPtrValid(item->shmAddr)) {
        return NULL;
    }
    if (item->latchType == LATCH_ADDR_PAGEID) {
        uint8_t *latch = NULL;
        StatusInter ret = ctx->getPageLatchFunc(ctx->pageMgr, item->shmAddr, &latch, 0, false);
        if (ret != STATUS_OK_INTER) {
            DB_LOG_ERROR(ret, "session get pageLatch unsucc.");
            return NULL;
        }
        return latch;
    } else {
        return (uint8_t *)DbShmPtrToAddr(item->shmAddr);
    }
}

#if defined(FEATURE_HAC) && defined(ACCELERATOR_MODE)
static void DbLatchRecoverForAccLatch(LatchStackItemT *item)
{
    if (item->latchAcqMode == LATCH_ACQ_READ) {  // 当前只有读
        DbAccUnlatchR(item->latchPoolId, item->latchId);
    }
    item->latchType = LATCH_ADDR_INVALID;
}
#endif

void DbSessionLeaveLatchAddr(const DbSessionCtxT *ctx, LatchStackItemT *item)
{
    if (item->latchType == LATCH_ADDR_PAGEID) {
        ctx->leavePageLatchFunc(ctx->pageMgr, item->shmAddr, false);
    }
}

inline static bool DbLatchStackCompareItem(const LatchStackItemT *item1, const LatchStackItemT *item2)
{
    DB_POINTER2(item1, item2);

    return (item1->latchType == item2->latchType) && (item1->shmAddr.segId == item2->shmAddr.segId) &&
           (item1->shmAddr.offset == item2->shmAddr.offset);
}

void DbSessionCountLatch(DbSessionMgr *mgr, const LatchStackItemT *item, uint32_t sessionId, uint32_t *count)
{
    uint32_t cnt = 0;
    ShmemPtrT *sessionMemPool = DbShmPtrToAddr(mgr->sessionPoolShmPtr);
    if (sessionMemPool == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get sessionPool worthless. (%" PRIu32 ", %" PRIu32 ")",
            mgr->sessionPoolShmPtr.segId, mgr->sessionPoolShmPtr.offset);
        *count = 0;
        DB_ASSERT(false);
        return;
    }
    DbRWSpinRLock(&mgr->lock);
    for (uint32_t sid = DB_FIRST_SESSION_ID; sid < (uint32_t)mgr->maxNums; sid++) {
        DbSessionT *session = GetSessionFromSessionPool(sessionMemPool, sid);
        if (session == NULL || session->isFree || session->id == sessionId) {
            continue;
        }
        LatchStackT *latchStack = (LatchStackT *)DbShmPtrToAddr(session->shmLatchStack);
        if (latchStack == NULL) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get latch stack worthless. (%" PRIu32 ", %" PRIu32 ")",
                session->shmLatchStack.segId, session->shmLatchStack.offset);
            DB_ASSERT(false);
            continue;
        }
        for (uint32_t i = 0; i < latchStack->stackTop; ++i) {
            if (DbLatchStackCompareItem(item, &latchStack->stack[i])) {
                cnt++;
                DB_LOG_WARN_UNFOLD(GMERR_LOCK_NOT_AVAILABLE,
                    "recover lock from sess is (%" PRIu32 ", %" PRIu32 "), count: %" PRIu32 ", curSid: %" PRIu32 "",
                    item->shmAddr.segId, item->shmAddr.offset, cnt, session->id);
                break;
            }
        }
    }
    *count = cnt;
    DbRWSpinRUnlock(&mgr->lock);
}

void DbSessionRemoveDuplicateLatch(LatchStackT *latchStack)
{
    DB_POINTER(latchStack);
    if (SECUREC_LIKELY(latchStack->stackStatus == STATUS_NORMAL)) {
        return;
    }
    int32_t stackTop = (int32_t)latchStack->stackTop;
    for (int32_t i = stackTop; i > LATCH_STACK_BOTTOM; --i) {
        LatchStackItemT *itemRight = &latchStack->stack[i];
        for (int32_t j = i - 1; j >= LATCH_STACK_BOTTOM; --j) {
            LatchStackItemT *itemLeft = &latchStack->stack[j];
            if (DbLatchStackCompareItem(itemLeft, itemRight)) {
                itemRight->latchType = LATCH_ADDR_INVALID;
                // 最多有两把相同的锁，检查到后立即退出
                return;
            }
        }
    }
}

void DbSessionCleanLatch(const DbSessionCtxT *ctx)
{
    DbSessionT *session = (DbSessionT *)ctx->session;
    LatchStackT *latchStack = ctx->latchStack;
    LatchStackItemT *item = NULL;
    uint8_t *latchAddr = NULL;
    int32_t stackTop = (int32_t)latchStack->stackTop;
    // 如果锁栈中记录了重复的锁信息，需要移除重复的锁。
    DbSessionRemoveDuplicateLatch(latchStack);
    /* note:session在记录加锁操作时，有可能只是将锁的信息记录栈中并已经完成加锁操作,
       但是还未更新锁栈的stackTop就crash了.
            所以在清理锁资源的时候要假设存在上述场景，从当前栈顶元素下标的高一位stackTop开始清理锁资源
    */
    for (int32_t i = stackTop; i >= LATCH_STACK_BOTTOM; --i) {
        item = &latchStack->stack[i];
        if (i == stackTop && item->latchType == LATCH_ADDR_INVALID) {
            continue;
        } else {
            // 在锁栈为STATUS_ABNORMAL状态下，可能锁栈中记录了一把无效的锁，其他状态下一定记录了完整的锁信息
            if (latchStack->stackStatus == STATUS_ABNORMAL && item->latchType == LATCH_ADDR_INVALID) {
                continue;
            }
            DB_ASSERT(item->latchType != LATCH_ADDR_INVALID);
        }
#if defined(FEATURE_HAC) && defined(ACCELERATOR_MODE)
        if (item->latchType == LATCH_ADDR_ACCELERATOR_LOCK) {
            DbLatchRecoverForAccLatch(item);
            continue;
        }
#endif
        latchAddr = DbSessionGetLatchAddr(ctx, item);
        if (latchAddr == NULL) {
            DB_POINTER(latchAddr);
            continue;
        }
        DbSpinLockT *spinlock = (DbSpinLockT *)latchAddr;
        if (DbGetSpinlockOwner(spinlock) != session->id) {
            DbSpinLockOwner(session->id, spinlock);
        }
        if (item->latchAcqMode != LATCH_ACQ_SPIN) {  // 非spinlock需要复原latch的模式和计数
            // while get latch count other sessions can not get this spinlock
            uint32_t count = 0;
            DbSessionCountLatch(ctx->sessionMgr, item, session->id, &count);  // 统计客户端占用的锁
            DbLatchT *latch = (DbLatchT *)latchAddr;
            // Correct latch mode and latch count
            DbLatchRecover(latch, count, (uint32_t)item->latchType, item->shmAddr);
        }
        item->latchType = LATCH_ADDR_INVALID;
        item->shmAddr = DB_INVALID_SHMPTR;
        item->virtAddr = NULL;
        DbSpinUnlock(spinlock);
        DbSessionLeaveLatchAddr(ctx, item);
    }
    latchStack->stackTop = LATCH_STACK_BOTTOM;
}

static void DbSessionGetRefCount(DbSessionMgr *mgr, const ShmRefItem *curShmRef, uint32_t sessionId, uint32_t *openCnt)
{
    ShmemPtrT *sessionMemPool = DbShmPtrToAddr(mgr->sessionPoolShmPtr);
    if (sessionMemPool == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get sessionPool worthless. (%" PRIu32 ", %" PRIu32 ")",
            mgr->sessionPoolShmPtr.segId, mgr->sessionPoolShmPtr.offset);
        DB_ASSERT(false);
        return;
    }

    DbRWSpinRLock(&mgr->lock);
    uint32_t cnt = 0;
    for (uint32_t sid = DB_FIRST_SESSION_ID; sid < (uint32_t)mgr->maxNums; sid++) {
        DbSessionT *session = GetSessionFromSessionPool(sessionMemPool, sid);
        if (session == NULL || session->isFree || session->id == sessionId) {
            continue;
        }
        DbShmRefArrayT *shmArray = DbShmPtrToAddr(session->shmRefArray);
        if (shmArray == NULL) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get shm refArray. (%" PRIu32 ", %" PRIu32 ")",
                session->shmRefArray.segId, session->shmRefArray.offset);
            DB_ASSERT(false);
            continue;
        }
        uint32_t descId;
        ShmRefDescT *desc = NULL;
        bool isFound = DbShmRefArrayLookUp(shmArray, curShmRef, &descId, &desc);
        if (!isFound) {
            continue;
        }
        DB_ASSERT(descId < MAX_SHMREF_DESC_ID);
        cnt += desc->item.refCount;
    }
    *openCnt = cnt;
    DbRWSpinRUnlock(&mgr->lock);
}

void DbShmOpenCountUpdate(const ShmRefItem *shmRef, uint32_t openCnt)
{
    uint32_t *shmOpenCnt = DbGetOpenCntByShmPtrAndOffset(shmRef->shmAddr, shmRef->openCntOffset);
    if (shmOpenCnt == NULL) {
        DB_LOG_DBG_ERROR(GMERR_INTERNAL_ERROR,
            "COM: Open count value was recorded but was found unsound (segId:%" PRIu32 ", offset:%" PRIu32
            ") (openCnt offset: %" PRIu32 ")",
            shmRef->shmAddr.segId, shmRef->shmAddr.offset, shmRef->openCntOffset);
        return;
    }
    *shmOpenCnt = openCnt;
}

void DbSessionCleanShmRefCount(const DbSessionCtxT *ctx, DbSessionT *session, ShmRefDescT *desc)
{
    uint32_t count = 0;
    // while count shmRef, other sessions can not get this spinlock
    DbSessionGetRefCount(ctx->sessionMgr, &desc->item, session->id, &count);
    DbShmOpenCountUpdate(&desc->item, count);
    desc->item.shmType = REF_SHM_INVALID;
    desc->item.refCount = 0;
    desc->item.shmAddr = DB_INVALID_SHMPTR;
    desc->item.latchOffset = DB_INVALID_ID32;
    desc->item.openCntOffset = DB_INVALID_ID32;
}

static void DbSessionCleanShmContainerRef(const DbSessionCtxT *ctx)
{
    DbSessionT *session = (DbSessionT *)ctx->session;
    ShmRefDescT *desc = NULL;
    DbShmRefArrayT *shmArray = ctx->refArray;
    Status ret;
    for (int32_t descId = (int32_t)shmArray->curPos - 1; descId >= 0; --descId) {
        ret = DbShmRefArrayGetItemById(shmArray, &desc, descId);
        if (ret != GMERR_OK) {
            continue;
        }
        if (desc == NULL || desc->item.shmType == REF_SHM_INVALID) {
            continue;
        }
        // 为啥这里不用按DbGetRefLockAddr处理？---DmvertexLabelT非持久化，是重启后重建的，因此和内存态一样使用即可
        LabelRWLatchT *labelRWLatch = (LabelRWLatchT *)DbShmPtrToAddr(desc->item.labelLatchShmAddr);
        if (labelRWLatch == NULL) {
            DB_POINTER(labelRWLatch);
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "labelRwLatch is NULL, segId:%" PRIu32 ", offset:%" PRIu32,
                desc->item.labelLatchShmAddr.segId, desc->item.labelLatchShmAddr.offset);
            continue;
        }
        LabelRLatchAcquire(labelRWLatch);
        ret = LabelLatchCheckVersion(labelRWLatch, desc->item.labelLatchVersionId);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            LabelRLatchRelease(labelRWLatch);
            desc->item.shmType = REF_SHM_INVALID;
            continue;  // 表已被drop，继续往后处理
        }

        // 先校验表latch的版本号，再进行锁addr的转换，这个desc->item.shmAddr有可能已经被归还给底层了
        DbSpinLockT *spinlock = DbGetSpinlockAddr(desc->item.shmAddr, desc->item.latchOffset);
        if (spinlock == NULL) {
            DB_POINTER(spinlock);
            continue;
        }

        if (DbGetSpinlockOwner(spinlock) != session->id) {
            DbSpinLockOwner(session->id, spinlock);
        }
        DbSessionCleanShmRefCount(ctx, session, desc);
        DbSpinUnlock(spinlock);
        LabelRLatchRelease(labelRWLatch);
    }
}

void DbSessionReleaseClientResource(DbSessionCtxT *ctx)
{
    if (!ctx->isOpenSession) {  // 客户端直连读流程，不用释放session
        ctx->session = NULL;
        return;
    }
    DbSessionT *session = (DbSessionT *)ctx->session;
    DB_ASSERT(session != NULL);
    DB_ASSERT(ctx->sessionMgr != NULL);

    // 为了防止cursor对应内存已经失效，需要先加上对应的表latch，然后再释放cursor计数
    DbSessionCleanShmContainerRef(ctx);
}

// 如果出现逆序加锁情况，调整锁栈需要严格保证修改顺序
inline static void LatchStackStrictPush(LatchStackT *latchStack, uint8_t *virAddr, const ShmemPtrT *shmAddr,
    LatchAcqModeE latchMode, LatchAddrTypeE latchType)
{
    DB_ASSERT(latchStack->stackTop < LATCH_STACK_TOP);
    LatchStackItemT *item = &latchStack->stack[latchStack->stackTop];
    item->virtAddr = virAddr;
    item->shmAddr = *shmAddr;
    item->latchAcqMode = latchMode;
    COMPILER_BARRIER;  // 防止编译优化导致latchType在addr之前赋值
    item->latchType = latchType;
}

#define DB_SESSION_LOG_INFO_SIZE 2048
static void DbSessionLatchStackLog(LatchStackT *latchStack, const uint8_t *latchAddr)
{
    char *logInfo = (char *)DB_MALLOC(DB_SESSION_LOG_INFO_SIZE);
    if (logInfo == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc session log info");
        return;
    }
    (void)memset_s(logInfo, DB_SESSION_LOG_INFO_SIZE, 0x0, DB_SESSION_LOG_INFO_SIZE);
    for (uint32_t i = 0; i < latchStack->stackTop; ++i) {
        LatchStackItemT *item = &latchStack->stack[i];
        (void)sprintf_s(logInfo, DB_SESSION_LOG_INFO_SIZE,
            "%s idx:%" PRIu32 ", latchAcqMode:%" PRId32 ", latchType:%" PRId32 ", pid:%" PRIu32 ", virtAddr:%" PRIx64
            ", shmAddr segId:%" PRIu32 ", offset:%" PRIu32 ".",
            logInfo, i, item->latchAcqMode, item->latchType, item->pid, (uint64_t)(uintptr_t)item->virtAddr,
            item->shmAddr.segId, item->shmAddr.offset);
    }
    DB_LOG_WARN_UNFOLD(GMERR_LOCK_NOT_AVAILABLE,
        "Need update latch stack. latchAddr:%" PRIx64 ", stackTop:%" PRIu32 ", stackStatus:%" PRIu32 ", stackInfo:%s",
        (uint64_t)(uintptr_t)latchAddr, latchStack->stackTop, latchStack->stackStatus, logInfo);
    DB_FREE(logInfo);
}

bool DbSessionCheckAndUpdateStack(DbSessionCtxT *sessionCtx, const uint8_t *latchAddr)
{
    // 栈顶记录的锁addr和当前要解锁的锁addr不一致，说明出现了逆序解锁场景, 需要整理锁栈
    // 发生逆序解锁的场景应该是非常少的
    LatchStackT *latchStack = sessionCtx->latchStack;
    bool isFound = false;
    int32_t idx = (int32_t)latchStack->stackTop - 1;
    for (; idx >= LATCH_STACK_BOTTOM; --idx) {
        if (latchStack->stack[idx].virtAddr == latchAddr) {
            isFound = true;
            break;
        }
    }
    // 一定可以找到
    if (!isFound) {
        DbSessionLatchStackLog(latchStack, latchAddr);
        // debug下打印日志后断言。release下避免越界，只解锁，不出栈
        DB_ASSERT(false);
        return false;
    }
    // 更新锁栈状态，在该状态中，锁栈可能记录了两把相同的锁或者中间记录了一把latchType为LATCH_ADDR_INVALID的锁
    // 在最后清理锁栈的时候需要根据该状态标识进行相应处理
    latchStack->stackStatus = STATUS_ABNORMAL;
    // 将当前需要解的锁重新压入栈顶, 再将原来锁的信息覆盖删除

    LatchStackStrictPush(latchStack, latchStack->stack[idx].virtAddr, &latchStack->stack[idx].shmAddr,
        latchStack->stack[idx].latchAcqMode, latchStack->stack[idx].latchType);
    for (uint32_t i = (uint32_t)idx; i < latchStack->stackTop; ++i) {
        LatchStackItemT *latchItemDst = &latchStack->stack[i];
        LatchStackItemT *latchItemSrc = &latchStack->stack[i + 1];
        latchItemDst->latchType = LATCH_ADDR_INVALID;
        COMPILER_BARRIER;
        latchItemDst->latchAcqMode = latchItemSrc->latchAcqMode;
        latchItemDst->shmAddr = latchItemSrc->shmAddr;
        latchItemDst->virtAddr = latchItemSrc->virtAddr;
        COMPILER_BARRIER;
        latchItemDst->latchType = latchItemSrc->latchType;
    }
    latchStack->stack[latchStack->stackTop].latchType = LATCH_ADDR_INVALID;
    COMPILER_BARRIER;
    latchStack->stackStatus = STATUS_NORMAL;
    return true;
}

inline static void DbSessionLatchPop(DbSessionCtxT *sessionCtx)
{
    DB_POINTER(sessionCtx);
    LatchStackT *latchStack = sessionCtx->latchStack;
    DB_ASSERT(latchStack->stackTop > LATCH_STACK_BOTTOM);
    latchStack->stackTop--;
}

inline static void DbSessionResetLatchType(DbSessionCtxT *sessionCtx)
{
    DB_POINTER(sessionCtx);
    LatchStackT *latchStack = sessionCtx->latchStack;
    latchStack->stack[latchStack->stackTop].latchType = LATCH_ADDR_INVALID;
}

inline static void DbSessionUpdateStack(DbSessionCtxT *sessionCtx)
{
    DB_POINTER(sessionCtx);
    sessionCtx->latchStack->stackTop++;
}

void DbRWSpinRLockWithSession(
    DbSessionCtxT *sessionCtx, DbLatchT *latch, const ShmemPtrT *latchShmAddr, LatchAddrTypeE latchType)
{
    DB_POINTER4(sessionCtx, sessionCtx->session, latch, latchShmAddr);
    DbSessionLatchPush(sessionCtx, (uint8_t *)latch, latchShmAddr, LATCH_ACQ_READ, latchType);
    CRASHPOINT(DB_CRASH_EVENT_RLOCK, DB_CRASH_STATE_SESSION_PUSH_LATCH);
    DbRWLatchREnterForClient(latch, sessionCtx->session->id);
    CRASHPOINT(DB_CRASH_EVENT_RLOCK, DB_CRASH_STATE_SESSION_LOCK);
    DbSessionUpdateStack(sessionCtx);
    CRASHPOINT(DB_CRASH_EVENT_RLOCK, DB_CRASH_STATE_SESSION_UPDATE_TOP);
    DbRWLatchLeave(latch);
}

void DbRWSpinWLockWithSession(
    DbSessionCtxT *sessionCtx, DbLatchT *latch, const ShmemPtrT *latchShmAddr, LatchAddrTypeE latchType)
{
    DB_POINTER4(sessionCtx, sessionCtx->session, latch, latchShmAddr);
    DbSessionLatchPush(sessionCtx, (uint8_t *)latch, latchShmAddr, LATCH_ACQ_WRITE, latchType);
    CRASHPOINT(DB_CRASH_EVENT_WLOCK, DB_CRASH_STATE_SESSION_PUSH_LATCH);
    DbRWLatchWEnterForClient(latch, sessionCtx->session->id);
    CRASHPOINT(DB_CRASH_EVENT_WLOCK, DB_CRASH_STATE_SESSION_LOCK);
    DbSessionUpdateStack(sessionCtx);
    CRASHPOINT(DB_CRASH_EVENT_WLOCK, DB_CRASH_STATE_SESSION_UPDATE_TOP);
    DbRWLatchLeave(latch);
}

bool DbRWSpinTryRLockWithSession(
    DbSessionCtxT *sessionCtx, DbLatchT *latch, const ShmemPtrT *latchShmAddr, LatchAddrTypeE latchType)
{
    DB_POINTER4(sessionCtx, sessionCtx->session, latch, latchShmAddr);
    DbSessionLatchPush(sessionCtx, (uint8_t *)latch, latchShmAddr, LATCH_ACQ_READ, latchType);
    CRASHPOINT(DB_CRASH_EVENT_TRY_RLOCK, DB_CRASH_STATE_SESSION_PUSH_LATCH);
    for (uint32_t i = 1; i <= RW_LATCH_TRY_TIMES; i++) {
        if (DbRWLatchTryREnterForClient(latch, sessionCtx->session->id)) {
            break;
        }
        CRASHPOINT(DB_CRASH_EVENT_TRY_RLOCK, DB_CRASH_STATE_SESSION_TRY_LOCK_FAIL);
        if (i == RW_LATCH_TRY_TIMES) {
            DbSessionResetLatchType(sessionCtx);
            return false;
        }
    }
    CRASHPOINT(DB_CRASH_EVENT_TRY_RLOCK, DB_CRASH_STATE_SESSION_LOCK);
    DbSessionUpdateStack(sessionCtx);
    CRASHPOINT(DB_CRASH_EVENT_TRY_RLOCK, DB_CRASH_STATE_SESSION_UPDATE_TOP);
    DbRWLatchLeave(latch);
    return true;
}

ALWAYS_INLINE void DbRWSpinRUnlockWithSession(DbSessionCtxT *sessionCtx, DbLatchT *latch)
{
    DB_POINTER3(sessionCtx, sessionCtx->session, latch);
    LatchStackT *latchStack = sessionCtx->latchStack;
    bool isLatchItemFoundInStack = true;
    if (SECUREC_UNLIKELY(latchStack->stack[latchStack->stackTop - 1].virtAddr != (uint8_t *)latch)) {
        DB_LOG_WARN(GMERR_LOCK_NOT_AVAILABLE,
            "lock need update stack, owner: %" PRIu32 ", "
            "pid: %" PRIu32 ", latchMode: %" PRIu32 ", cltCnt: %" PRIu32 ", serverCnt: %" PRIu32 "",
            latch->spinlock.lock, latch->pid, latch->latchMode, latch->clientCnt, latch->serverCnt);
        isLatchItemFoundInStack = DbSessionCheckAndUpdateStack(sessionCtx, (uint8_t *)latch);
    }
    DbRWUnlatchREnterForClient(latch, sessionCtx->session->id);
    CRASHPOINT(DB_CRASH_EVENT_UN_RLOCK, DB_CRASH_STATE_SESSION_UNLOCK);
    if (SECUREC_LIKELY(isLatchItemFoundInStack)) {
        DbSessionLatchPop(sessionCtx);
    }
    CRASHPOINT(DB_CRASH_EVENT_UN_RLOCK, DB_CRASH_STATE_SESSION_POP_LATCH);
    DbRWLatchLeave(latch);
    CRASHPOINT(DB_CRASH_EVENT_UN_RLOCK, DB_CRASH_STATE_SESSION_BEFORE_RESET);
    DbSessionResetLatchType(sessionCtx);
}

void DbRWSpinWUnlockWithSession(DbSessionCtxT *sessionCtx, DbLatchT *latch)
{
    DB_POINTER3(sessionCtx, sessionCtx->session, latch);
    LatchStackT *latchStack = sessionCtx->latchStack;
    bool isLatchItemFoundInStack = true;
    if (SECUREC_UNLIKELY(latchStack->stack[latchStack->stackTop - 1].virtAddr != (uint8_t *)latch)) {
        isLatchItemFoundInStack = DbSessionCheckAndUpdateStack(sessionCtx, (uint8_t *)latch);
    }
    DbRWUnlatchWEnterForClient(latch, sessionCtx->session->id);
    CRASHPOINT(DB_CRASH_EVENT_UN_WLOCK, DB_CRASH_STATE_SESSION_UNLOCK);
    if (SECUREC_LIKELY(isLatchItemFoundInStack)) {
        DbSessionLatchPop(sessionCtx);
    }
    CRASHPOINT(DB_CRASH_EVENT_UN_WLOCK, DB_CRASH_STATE_SESSION_POP_LATCH);
    DbRWLatchLeave(latch);
    CRASHPOINT(DB_CRASH_EVENT_UN_WLOCK, DB_CRASH_STATE_SESSION_BEFORE_RESET);
    DbSessionResetLatchType(sessionCtx);
}

bool DbRWSpinTryLockOwnerWithSession(
    DbSessionCtxT *sessionCtx, DbSpinLockT *lock, const ShmemPtrT *lockShmAddr, LatchAddrTypeE latchType)
{
    DB_POINTER4(sessionCtx, sessionCtx->session, lock, lockShmAddr);
    DbSessionLatchPush(sessionCtx, (uint8_t *)lock, lockShmAddr, LATCH_ACQ_SPIN, latchType);
    CRASHPOINT(DB_CRASH_EVENT_TRY_SPINLOCK, DB_CRASH_STATE_SESSION_PUSH_LATCH);
    if (!DbSpinTryLockOwner(sessionCtx->session->id, lock)) {
        CRASHPOINT(DB_CRASH_EVENT_TRY_SPINLOCK, DB_CRASH_STATE_SESSION_TRY_LOCK_FAIL);
        DbSessionResetLatchType(sessionCtx);
        return false;
    }
    CRASHPOINT(DB_CRASH_EVENT_TRY_SPINLOCK, DB_CRASH_STATE_SESSION_LOCK);
    DbSessionUpdateStack(sessionCtx);
    return true;
}

inline void DbSpinlockOwnerWithSession(
    DbSessionCtxT *sessionCtx, DbSpinLockT *lock, const ShmemPtrT *lockShmAddr, LatchAddrTypeE latchType)
{
    DB_POINTER4(sessionCtx, sessionCtx->session, lock, lockShmAddr);
    DbSessionLatchPush(sessionCtx, (uint8_t *)lock, lockShmAddr, LATCH_ACQ_SPIN, latchType);
    CRASHPOINT(DB_CRASH_EVENT_SPINLOCK, DB_CRASH_STATE_SESSION_PUSH_LATCH);
    DbSpinLockOwner(sessionCtx->session->id, lock);
    CRASHPOINT(DB_CRASH_EVENT_SPINLOCK, DB_CRASH_STATE_SESSION_LOCK);
    DbSessionUpdateStack(sessionCtx);
}

inline void DbSpinUnlockOwnerWithSession(DbSessionCtxT *sessionCtx, DbSpinLockT *latch)
{
    DB_POINTER3(sessionCtx, sessionCtx->session, latch);
    LatchStackT *latchStack = sessionCtx->latchStack;
    bool isLatchItemFoundInStack = true;
    if (SECUREC_UNLIKELY(latchStack->stack[latchStack->stackTop - 1].virtAddr != (uint8_t *)latch)) {
        isLatchItemFoundInStack = DbSessionCheckAndUpdateStack(sessionCtx, (uint8_t *)latch);
    }
    if (SECUREC_LIKELY(isLatchItemFoundInStack)) {
        DbSessionLatchPop(sessionCtx);
    }
    CRASHPOINT(DB_CRASH_EVENT_UNSPINLOCK, DB_CRASH_STATE_SESSION_POP_LATCH);
    DbSpinUnlock(latch);
    CRASHPOINT(DB_CRASH_EVENT_UNSPINLOCK, DB_CRASH_STATE_SESSION_UNLOCK);
    DbSessionResetLatchType(sessionCtx);
}

#ifdef FEATURE_HAC
void DbAccRLockWithSession(DbSessionCtxT *sessionCtx, const LatchStackItemT *latchItem)
{
    DB_POINTER2(sessionCtx, latchItem);
#ifndef ACCELERATOR_MODE
    DbRWSpinRLockWithSession(
        sessionCtx, (void *)latchItem->virtAddr, (const ShmemPtrT *)&latchItem->shmAddr, latchItem->latchType);
#else
    uint32_t latchPoolId = latchItem->latchPoolId;
    uint32_t latchId = latchItem->latchId;
    DbSessionAccLatchPush(sessionCtx, latchPoolId, latchId, LATCH_ACQ_READ);
    DbSessionUpdateStack(sessionCtx);
    DbAccLatchR(latchPoolId, latchId);
    CRASHPOINT(DB_CRASH_EVENT_RLOCK, DB_CRASH_STATE_SESSION_LOCK);
#endif
}

void DbAccRUnlockWithSession(DbSessionCtxT *sessionCtx, DbLatchT *latch)
{
#ifndef ACCELERATOR_MODE
    DB_POINTER2(sessionCtx, latch);
    DbRWSpinRUnlockWithSession(sessionCtx, latch);
#else
    DB_POINTER(sessionCtx);
    LatchStackT *latchStack = sessionCtx->latchStack;
    LatchStackItemT *latchItem = &latchStack->stack[latchStack->stackTop - 1];
    DbAccUnlatchR(latchItem->latchPoolId, latchItem->latchId);
    DbSessionLatchPop(sessionCtx);
    CRASHPOINT(DB_CRASH_EVENT_UNSPINLOCK, DB_CRASH_STATE_SESSION_UNLOCK);
    DbSessionResetLatchType(sessionCtx);
#endif
}
#endif

void DbUpdateLatchConflictStatWithSession(uint32_t waitTimes, DbSessionT *session, bool isRead)
{
#ifdef LATCH_CONFLICT_DEBUG
    DB_POINTER(session);
    if (isRead) {
        if (waitTimes != 0) {
            session->readerLatchInfo.readerConflictCount++;
        } else {
            session->readerLatchInfo.readerConflictCount = 0;
        }
        session->readerLatchInfo.readerMaxWaitCount = DB_MAX(session->readerLatchInfo.readerConflictCount, waitTimes);
        session->readerLatchInfo.readerTotalWaitCount += waitTimes;
    }
    if (!isRead) {
        if (waitTimes != 0) {
            session->writerLatchInfo.writerConflictCount++;
        } else {
            session->writerLatchInfo.writerConflictCount = 0;
        }
        session->writerLatchInfo.writerMaxWaitCount = DB_MAX(session->writerLatchInfo.writerConflictCount, waitTimes);
        session->writerLatchInfo.writerTotalWaitCount += waitTimes;
    }
#endif
}
