/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: db_resource_session.h
 * Description: internal header file for resource session
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * Create: 2022-6-30
 */
#ifndef DB_RESOURCE_SESSION_H
#define DB_RESOURCE_SESSION_H
#include "adpt_define.h"
#include "adpt_types.h"
#include "db_resource_session_pub.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

inline static void DbSessionLatchPush(DbSessionCtxT *sessionCtx, uint8_t *virAddr, const ShmemPtrT *shmAddr,
    LatchAcqModeE latchMode, LatchAddrTypeE latchType)
{
    LatchStackT *latchStack = sessionCtx->latchStack;
    DB_ASSERT(latchStack->stackTop < LATCH_STACK_TOP);
    LatchStackItemT *item = &latchStack->stack[latchStack->stackTop];
    // 默认采用可靠性锁后， stack_top的增加在spinLock内，此处不增加内存屏蔽
#ifndef NDEBUG
    uint32_t localPid = DbRWlatchGetPid();
    DB_ASSERT(latchStack->pid == localPid);
    DB_ASSERT(latchType != LATCH_ADDR_INVALID);
    if (latchType != LATCH_ADDR_PAGEID
#if defined(FEATURE_HAC) && defined(ACCELERATOR_MODE)
        && latchType != LATCH_ADDR_ACCELERATOR_LOCK
#endif
    ) {
        uint8_t *targetVirAddr = (uint8_t *)DbShmPtrToAddr(*shmAddr);
        DB_ASSERT(targetVirAddr == virAddr);
    }
#endif
    item->virtAddr = virAddr;
    item->shmAddr = *shmAddr;
    item->latchAcqMode = latchMode;
    item->latchType = latchType;
}

#ifdef FEATURE_HAC
inline static void DbSessionAccLatchPush(
    DbSessionCtxT *sessionCtx, uint32_t latchPoolId, uint32_t latchId, LatchAcqModeE latchMode)
{
    LatchStackT *latchStack = sessionCtx->latchStack;
    LatchStackItemT *item = &latchStack->stack[latchStack->stackTop];
    item->latchPoolId = latchPoolId;
    item->latchId = latchId;
    item->latchAcqMode = latchMode;
    item->latchType = LATCH_ADDR_ACCELERATOR_LOCK;
}
#endif

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
