/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: db_lvq.h
 * Description: Implement of quantization
 * Author:
 * Create: 2024-8-26
 */

#include "db_vector_quantization.h"

#define DB_QUANT_TYPE_NONE_STR "none"
#define DB_QUANT_TYPE_LVQ_STR "lvq"
#define DB_QUANT_TYPE_PQ_STR "pq"

const char *DbGetVectorQuantStrByType(DbVectorQuantTypeE type)
{
    const char *str = DB_QUANT_TYPE_NONE_STR;
    switch (type) {
        case DB_QUANT_TYPE_LVQ:
            str = DB_QUANT_TYPE_LVQ_STR;
            break;
        case DB_QUANT_TYPE_PQ:
            str = DB_QUANT_TYPE_PQ_STR;
            break;
        default:
            break;
    }
    return str;
}

DbVectorQuantTypeE DbGetVectorQuantTypeByStr(const char *str)
{
    DbVectorQuantTypeE type = DB_QUANT_TYPE_BUTT;
    if (strcmp(str, DB_QUANT_TYPE_LVQ_STR) == 0) {
        type = DB_QUANT_TYPE_LVQ;
    } else if (strcmp(str, DB_QUANT_TYPE_PQ_STR) == 0) {
        type = DB_QUANT_TYPE_PQ;
    }
    return type;
}
