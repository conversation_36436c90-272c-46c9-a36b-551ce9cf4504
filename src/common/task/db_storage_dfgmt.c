/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * File Name: db_storage_dfgmt.c
 * Description: source file for storage defragment
 * Create: 2021-4-19
 */
#include "db_storage_dfgmt.h"
#include "securec.h"
#include "gmc_errno.h"
#include "common_log.h"
#include "adpt_rdtsc.h"
#include "adpt_thread.h"
#include "adpt_semaphore.h"

#ifdef __cplusplus
extern "C" {
#endif

static DfgmtMgrT *g_gmdbDfgmtMgr = NULL;
bool g_gmdbIsServerDfgmt = true;

DfgmtMgrT *DbGetDfgmtMgr(void)
{
    return g_gmdbDfgmtMgr;
}

ShmemPtrT DbGetDfgmtMgrShmPtr(void)
{
    if (g_gmdbDfgmtMgr != NULL) {
        return g_gmdbDfgmtMgr->mgrShmPtr;
    }
    return DB_INVALID_SHMPTR;
}

DbMemCtxT *DbGetDfgmtMemCtx(void)
{
    if (g_gmdbDfgmtMgr == NULL) {
        return NULL;
    }
    if (g_gmdbIsServerDfgmt) {
        return g_gmdbDfgmtMgr->memCtx;
    } else {
        return DbGetShmemCtxById(g_gmdbDfgmtMgr->shmctxId, g_gmdbDfgmtMgr->instanceId);
    }
}

Status DbOpenDfgmtMgr(ShmemPtrT mgrShmPtr)
{
    // 直连写和共进程互斥
    if (DbCommonIsSingleCltProcess() && DbIsShmPtrValid(mgrShmPtr)) {
        g_gmdbDfgmtMgr = DbShmPtrToAddr(mgrShmPtr);
        if (g_gmdbDfgmtMgr == NULL) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "open serverDfgmtMgr with params:(%" PRIu32 ", %" PRIu32 ")",
                mgrShmPtr.segId, mgrShmPtr.offset);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        g_gmdbIsServerDfgmt = false;
    }
    return GMERR_OK;
}

void DbCloseDfgmtMgr(void)
{
    // 直连写和共进程互斥
    if (DbCommonIsSingleCltProcess()) {
        g_gmdbDfgmtMgr = NULL;
    }
}

Status DbCreateDfgmtMgr(DbMemCtxT *memCtx)
{
    DB_POINTER(memCtx);
    // dfgmtMgr在DbSrvDfgmtTasksFreeMgr中释放
    ShmemPtrT mgrShmPtr = DbShmemCtxAlloc(memCtx, sizeof(DfgmtMgrT));
    g_gmdbDfgmtMgr = DbShmPtrToAddr(mgrShmPtr);
    if (g_gmdbDfgmtMgr == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "serverDfgmtMgr is null");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    g_gmdbDfgmtMgr->mgrShmPtr = mgrShmPtr;
    g_gmdbDfgmtMgr->memCtx = memCtx;
    g_gmdbDfgmtMgr->shmctxId = memCtx->ctxId;
    g_gmdbDfgmtMgr->instanceId = DbGetProcGlobalId();
    return GMERR_OK;
}

void DbRelaseDfgmtMgr(void)
{
    if (g_gmdbDfgmtMgr == NULL) {
        return;
    }
    DbShmemCtxFree(g_gmdbDfgmtMgr->memCtx, g_gmdbDfgmtMgr->mgrShmPtr);
    g_gmdbDfgmtMgr = NULL;
}

bool DfgmtTaskIsOneTask(MemTaskTypeE taskType)
{
    return (taskType == MEM_COMPACT_RSM_PAGE);
}

bool DfgmtTaskIsLimited(DfgmtMgrT *dfgmtMgr, MemTaskTypeE taskType)
{
    return (DfgmtTaskIsOneTask(taskType) && dfgmtMgr->taskStats[taskType].curTaskCount != 0);
}

Status DfgmtTaskCreate(DfgmtMgrT *dfgmtMgr, MemTaskTypeE taskType, const DfgmtTaskParaT *para)
{
    if (DfgmtTaskIsLimited(dfgmtMgr, taskType)) {  // 达到该类型task创建上限，不创建
        return GMERR_DATA_EXCEPTION;
    }

    ShmemPtrT taskCtxPtr = DbDfgmtTaskCtxAlloc(taskType, para);
    if (!DbIsShmPtrValid(taskCtxPtr)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "inv taskCtx, taskType:%" PRId32 ".", (int32_t)taskType);
        return GMERR_OUT_OF_MEMORY;
    }

    ShmemPtrT newTaskPtr = DbShmemCtxAlloc(DbGetDfgmtMemCtx(), sizeof(DfgmtTaskT));
    DfgmtTaskT *newTask = (DfgmtTaskT *)DbShmPtrToAddr(newTaskPtr);
    if (newTask == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "newTask is null");
        DbDfgmtTaskCtxFree(taskType, taskCtxPtr);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(newTask, sizeof(DfgmtTaskT), 0, sizeof(DfgmtTaskT));
    newTask->self = newTaskPtr;
    newTask->taskCtxPtr = taskCtxPtr;
    newTask->taskType = taskType;
    newTask->node.self = DbAdptShmOffsetShift(newTaskPtr, offsetof(DfgmtTaskT, node));

    Status ret = GMERR_OK;
    if ((ret = DbShmLinkedListAppend(&dfgmtMgr->taskList, &newTask->node)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "append node to list.");
        DbShmemCtxFree(DbGetDfgmtMemCtx(), newTaskPtr);
        DbDfgmtTaskCtxFree(taskType, taskCtxPtr);
        return ret;
    }
    DfgmtTaskStatT *taskStat = &dfgmtMgr->taskStats[taskType];
    if (taskStat->curTaskCount == 0) {
        taskStat->curTaskStatus = TASK_INIT;
    }
    taskStat->curTaskCount++;
    taskStat->createCount++;
    return GMERR_OK;
}

Status DbCreateDfgmtTask(MemTaskTypeE taskType, const DfgmtTaskParaT *para)
{
    if (taskType >= MEM_COMPACT_MAX) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "unsound task type, type=%" PRId32 ".", (int32_t)taskType);
        return GMERR_DATA_EXCEPTION;
    }

    DfgmtMgrT *dfgmtMgr = DbGetDfgmtMgr();
    if (dfgmtMgr == NULL) {
        return GMERR_OK;
    }

    DbSpinLock(&dfgmtMgr->lock);
    Status ret = DfgmtTaskCreate(dfgmtMgr, taskType, para);
    DbSpinUnlock(&dfgmtMgr->lock);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)DbSemPost(&dfgmtMgr->sem);
    return ret;
}

AmForDfgmtT g_gmdbDfgmtAm[MEM_COMPACT_MAX] = {0};
void DbSetDfgmtAm(MemTaskTypeE taskType, AmForDfgmtT dfgmtAm)
{
    if (taskType >= MEM_COMPACT_MAX) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "unsound task type, type=%" PRId32 ".", (int32_t)taskType);
        return;
    }

    g_gmdbDfgmtAm[taskType] = dfgmtAm;
}

ShmemPtrT DbDfgmtTaskCtxAlloc(MemTaskTypeE taskType, const DfgmtTaskParaT *para)
{
    if (g_gmdbDfgmtAm[taskType].taskCtxAllocFunc == NULL) {
        return DB_INVALID_SHMPTR;
    }
    return g_gmdbDfgmtAm[taskType].taskCtxAllocFunc(para);
}

void DbDfgmtTaskCtxReset(MemTaskTypeE taskType, DfgmtTaskCtxT *taskCtx)
{
    DB_POINTER(taskCtx);
    if (g_gmdbDfgmtAm[taskType].taskCtxResetFunc == NULL) {
        return;
    }
    g_gmdbDfgmtAm[taskType].taskCtxResetFunc(taskCtx);
}

bool DbDfgmtTaskEntry(MemTaskTypeE taskType, DfgmtTaskCtxT *taskCtx, DfgmtSessionT *session)
{
    if (g_gmdbDfgmtAm[taskType].taskEntryFunc == NULL) {
        return true;
    }
    return g_gmdbDfgmtAm[taskType].taskEntryFunc(taskCtx, session);
}

void DbDfgmtTaskCtxFree(MemTaskTypeE taskType, ShmemPtrT taskCtxPtr)
{
    if (g_gmdbDfgmtAm[taskType].taskCtxFreeFunc == NULL) {
        return;
    }
    g_gmdbDfgmtAm[taskType].taskCtxFreeFunc(taskCtxPtr);
}

#ifdef __cplusplus
}
#endif
