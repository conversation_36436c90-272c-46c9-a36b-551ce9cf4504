/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * Description: source file for log
 * Author: gaohaiyang
 * Create: 2020-09-11
 */

#include "db_log.h"
#include "db_error.h"
#include "db_file.h"
#include "adpt_string.h"
#include "adpt_atomic.h"
#include "db_timer.h"
#include "db_config.h"
#include "db_common_init.h"
#include "adpt_memory.h"
#include "db_log_utils.h"
#include "db_log_fold.h"
#include "db_log_ctrl.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

uint16_t g_gmdbMaxLogSize = DB_DEFAULT_LOG_SIZE;  // DB能够写日志的最大size，默认值1024，支持配置项可配

bool g_gmdbServerImportSourceFlag = false;  // 标识server启动过程中，权限是否导入完毕

static void DbSetLogSize(uint16_t logSize)
{
    g_gmdbMaxLogSize = logSize;
}

DB_THREAD_LOCAL uint64_t g_dtlRequestId =
    0;  // 一条datalog请求的标识，构造方式为：sessionid(32位~63位) | workerId(16位~31位) | (自增id)(0位~15位)
DB_THREAD_LOCAL bool g_gmdbIsPrintLog = true;  // 按需建表场景，表不存在的时候，标识是否打印相关日志。

DB_THREAD_LOCAL char g_gmdbLogThreadMsg[DB_LOG_THREAD_MSG_LEN] = {0};
DB_THREAD_LOCAL char *g_gmdbLogThreadMsgFromConnMgr = NULL;

uint32_t g_gmdbTraceLogMask = 0;
/*
 * description: 初始化审计日志控制项
 */
static void DbAuditInitCtrlItem(DbAuditCtrlItemT *auditCtrlItem)
{
    DB_POINTER(auditCtrlItem);
    auditCtrlItem->auditTypeSwitch[DB_AUDIT_DDL] = DbCfgGetBoolLite(DB_CFG_AUDIT_LOG_DDL_ENABLE, NULL);
    auditCtrlItem->auditTypeSwitch[DB_AUDIT_DCL] = DbCfgGetBoolLite(DB_CFG_AUDIT_LOG_DCL_ENABLE, NULL);
    auditCtrlItem->auditTypeSwitch[DB_AUDIT_DML] = DbCfgGetBoolLite(DB_CFG_AUDIT_LOG_DML_ENABLE, NULL);
    auditCtrlItem->auditTypeSwitch[DB_AUDIT_DQL] = DbCfgGetBoolLite(DB_CFG_AUDIT_LOG_DQL_ENABLE, NULL);
}

/*
 * description: 初始化进程共用的日志配置信息
 * param {type} 待赋值的日志配置项结构体指针
 */
Status DbLogInitFileCfg(DbLogFileCfgT *fileCfg)
{
    DB_POINTER(fileCfg);
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);
    DbCfgValueT cfgValue;
    Status ret = DbCfgGet(cfgHandle, DB_CFG_LOG_FILE_NUM_MAX, &cfgValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    fileCfg->maxLogFileNum = (uint16_t)cfgValue.int32Val;

    ret = DbCfgGet(cfgHandle, DB_CFG_LOG_FILE_SIZE_MAX, &cfgValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    fileCfg->maxLogFileSize = (uint32_t)cfgValue.int32Val;

    ret = DbCfgGet(cfgHandle, DB_CFG_LOG_LENGTH_MAX, &cfgValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbSetLogSize((uint16_t)cfgValue.int32Val);  // 读取配置项中的logSize
#if !defined(NDEBUG)
    ret = DbCfgGet(cfgHandle, DB_CFG_YANG_TRACE_LOG, &cfgValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbSetYangTraceLogMask((uint32_t)cfgValue.int32Val);
#endif
    return GMERR_OK;
}

static Status DbLogInitCtxInShmSrv(void)
{
    DbLogFileCfgT *fileCfg = DbLogGetFileCfg();
    if (fileCfg == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    Status ret = DbLogInitFileCfg(fileCfg);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbAuditCtrlItemT *auditCtrl = DbLogGetAuditCtrlItem();
    if (auditCtrl == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    DbAuditInitCtrlItem(auditCtrl);

    ret = DbLogInitCtrlHeader();
    if (ret != GMERR_OK) {
        return ret;
    }
    // 为了适配UT测试, 正常服务端启动, DbLogInitBootLog 已经注册过一次.
    DbAdptLogApiT logApis = {DbLogWrite, DbLogWriteNoFmt, DbAuditWrite};
    DbAdptLogRegLogApi(&logApis);
    DbAdptRegitstGetErrCodeDescApi(DbErrGetBrief);
    DbAdptLogSetFileCfg(fileCfg);
    DbLogCreateCtrlItem();
    return GMERR_OK;
}

/*
 * description: 日志资源初始化
 * return {type} 成功返回STATUS_OK，失败返回错误码
 */
Status DbLogInitLocalSrv(void)
{
    // 进程间共享内存，由server创建
    Status ret = DbLogCreateShareMemSrv();
    if (ret != GMERR_OK) {
        return ret;
    }
    // 初始化进程间上下文，只由server进行
    ret = DbLogInitCtxInShmSrv();
    if (ret != GMERR_OK) {
        DbLogDestroyShmem();
        return ret;
    }
    return GMERR_OK;
}

void DbLogInitLocalClt(uint32_t instanceId)
{
    if (DbLogCheckShmemNotNull() && DbLogGetInstanceId() == instanceId) {
        // 共实例时，共享内存全局变量由服务端线程进行初始化，因此客户端可以直接设置g_logInitFlag=false
        DbSetLogInitFlag(false);
        return;
    }
    // attach共享内存
    (void)DbLogAttachShmem(instanceId);
    DbAdptLogApiT logApis = {DbLogWrite, DbLogWriteNoFmt, DbAuditWrite};
    DbAdptLogRegLogApi(&logApis);
    DbAdptRegitstGetErrCodeDescApi(DbErrGetBrief);
    DbAdptLogSetFileCfg(DbLogGetFileCfg());
    DbLogCreateCtrlItem();
    if (DbLogGetCurCtrlItem() != NULL) {
        DbSetLogInitFlag(false);
    }
}

// 共享内存不清理
void DbLogUnInitLocal(void)
{
    // 共进程模式下使用server的日志函数
    if (DbCommonIsCltThreadInServProcess()) {
        return;
    }
    DbLogDestroyCurCtrlItem();
    DbLogDetachShmem();
    DbLogDestroyShmem();
}

static void DbLogInitLogContent(
    DbLogInfoT *info, const DbLogHeaderT *header, const DbLogLocationT *place, const char *logDesc)
{
    DB_POINTER4(info, header, place, logDesc);
    info->header = *header;
    info->place = *place;
    info->logDesc = logDesc;
}

static inline bool DbLogCanWriteCheckCtrlItem(uint32_t level, const DbLogCtrlItemT *ctrlItem)
{
    return ctrlItem->deftLogLevel >= level && ctrlItem->enableLog;
}

bool DbLogCanWrite(uint32_t level)
{
    DbLogCtrlItemT *ctrlItem = DbLogGetCurCtrlItem();
    return ctrlItem != NULL && DbLogCanWriteCheckCtrlItem(level, ctrlItem);
}

#if defined(YANG_DEBUG) || !defined(NDEBUG)
bool DbYangTraceLogEnable(uint32_t mask)
{
    return g_gmdbTraceLogMask & mask;
}
#else
bool DbYangTraceLogEnable(uint32_t mask)
{
    return false;
}
#endif

void DbSetYangTraceLogMask(uint32_t mask)
{
    g_gmdbTraceLogMask = mask;
}

bool g_logInitFlag = false;

bool DbGetLogInitFlag(void)
{
    return g_logInitFlag;
}

void DbSetLogInitFlag(bool val)
{
    g_logInitFlag = val;
}

static DB_THREAD_LOCAL bool g_gmdbIsWriteLog = false;  // 线程变量，用于记录当前线程是否处于记日志过程中

void DbLogSetWriteLog(bool flag)
{
    g_gmdbIsWriteLog = flag;
}

bool DbLogGetWriteLog(void)
{
    return g_gmdbIsWriteLog;
}

#ifndef NDEBUG
void DbLogCheckFormat(const char *formatStr, va_list *args)
{
    uint32_t bufSize = DB_DEFAULT_LOG_SIZE + 1;
    char *logBuf = (char *)DB_MALLOC(bufSize);
    if (logBuf == NULL) {
        return;
    }
    (void)memset_s(logBuf, bufSize, 0, bufSize);
    int32_t writeCnt = vsnprintf_truncated_s(logBuf, bufSize, formatStr, *args);
    if (SECUREC_UNLIKELY(writeCnt < 0)) {
        DB_ASSERT(false);
    }
    DB_FREE(logBuf);
}
#endif

/*
 * description: common及以上模块的日志写接口
 */
void DbLogWrite(bool needFold, DbLogTypeE type, const char *module, uint32_t level, int32_t errCode,
    const char *fileName, uint32_t lineNum, const char *funcName, const char *formatStr, ...)
{
    DB_POINTER2(fileName, funcName);
    if (g_gmdbIsWriteLog) {
        return;
    }
    g_gmdbIsWriteLog = true;
#ifndef NDEBUG
    va_list params;
    va_start(params, formatStr);
    DbLogCheckFormat(formatStr, &params);
    va_end(params);
#endif
    // 客户端启动阶段不看日志控制信息直接打印日志
    if (SECUREC_UNLIKELY(DbGetLogInitFlag())) {
        // 获取可变参数列表
        va_list args;
        va_start(args, formatStr);
        DbLogWriteWithArgs(needFold, type, module, level, errCode, fileName, lineNum, funcName, formatStr, &args, true);
        va_end(args);
        g_gmdbIsWriteLog = false;
        return;
    }
    DbLogCtrlItemT *ctrlItem = DbLogGetCurCtrlItem();
    if (SECUREC_LIKELY(ctrlItem != NULL)) {
        if (!DbLogCanWriteCheckCtrlItem(level, ctrlItem)) {  // 日志开关&级别过滤检查
            g_gmdbIsWriteLog = false;
            return;
        }
        DbLogResetCtrlItem();  // 在日志本地写开关和日志级别超过时间上限时，自动关闭并重置控制项字段
        // 获取可变参数列表
        va_list args;
        va_start(args, formatStr);
        DbLogWriteWithArgs(needFold, type, module, level, errCode, fileName, lineNum, funcName, formatStr, &args, true);
        va_end(args);
        g_gmdbIsWriteLog = false;
        return;
    }

    // 如果日志控制区未初始化, 并且在启动阶段, 我们需要打印 boot日志, 否则不打印
    if (!DbLogIsBootState()) {
        g_gmdbIsWriteLog = false;
        return;
    }
    va_list args;
    va_start(args, formatStr);
    DbWriteBootLogWithArgs(errCode, level, fileName, lineNum, formatStr, &args, true);
    va_end(args);
    g_gmdbIsWriteLog = false;
    return;
}

/*
 * description: Tbm日志钩子函数
 */
void DbLogWriteForTbm(bool needFold, DbLogTypeE type, const char *module, uint32_t level, int32_t errCode,
    const char *fileName, uint32_t lineNum, const char *funcName, const char *formatStr, va_list *args)
{
    DB_POINTER2(fileName, funcName);
    // 客户端启动阶段不看日志控制信息直接打印日志
    if (DbGetLogInitFlag()) {
        // 获取可变参数列表
        DbLogWriteWithArgs(needFold, type, module, level, errCode, fileName, lineNum, funcName, formatStr, args, true);
        return;
    }
    DbLogCtrlItemT *ctrlItem = DbLogGetCurCtrlItem();
    if (SECUREC_LIKELY(ctrlItem != NULL)) {
        if (!DbLogCanWriteCheckCtrlItem(level, ctrlItem)) {  // 日志开关&级别过滤检查
            return;
        }
        DbLogResetCtrlItem();  // 在日志本地写开关和日志级别超过时间上限时，自动关闭并重置控制项字段
        // 获取可变参数列表
        DbLogWriteWithArgs(needFold, type, module, level, errCode, fileName, lineNum, funcName, formatStr, args, true);
        return;
    }

    // 如果日志控制区未初始化, 并且在启动阶段, 我们需要打印 boot日志, 否则不打印
    if (!DbLogIsBootState()) {
        return;
    }
    DbWriteBootLogWithArgs(errCode, level, fileName, lineNum, formatStr, args, true);
    return;
}

/*
 * description: Tbm日志钩子函数
 */
void DbTbmLogger(uint32_t level, const char *formatStr, va_list args)
{
    if (!DbLogCanWrite(level)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "The first parameter 'level' is unsound.");
        return;
    }
    if (formatStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "The second parameter 'formatStr' is unsound.");
        return;
    }
    va_list argsCopy;
    /* Note: This seemingly unnecessary copy is required in case va_list
     * is an array type. */
    va_copy(argsCopy, args);

    DbLogFoldModeE fold = DbCfgGetInt32Lite(DB_CFG_ENABLE_LOG_FOLD, NULL);

    DbLogWriteForTbm(fold == ENABLE_ALL_LOG_FOLD, DB_LOG_TYPE_RUN, LOG_MODULE, level, LOG_NO_ERRORCODE, __FILE__,
        __LINE__, __func__, formatStr, &argsCopy);
    va_end(argsCopy);
}

static Status DbLogAddThreadMsg(LogTextStrT *logText)
{
    if (g_gmdbLogThreadMsgFromConnMgr != NULL) {
        return DbLogTextPrintf(logText, "%s, ", g_gmdbLogThreadMsgFromConnMgr);
    }
    if (strlen(g_gmdbLogThreadMsg) != 0) {
        return DbLogTextPrintf(logText, "%s, ", g_gmdbLogThreadMsg);
    }
    return GMERR_OK;
}

// 构造一条普通日志：run、operate、alarm、debug
Status DbLogMakeLog(
    LogTextStrT *logText, const DbLogInfoT *info, uint64_t nowTimeStamp, va_list *args, int32_t errCode, bool isFormat)
{
    DB_POINTER4(logText, logText->buf, info, args);
    // 此处构造日志通用格式最小集，其余日志信息下沉至写日志函数前
    // threadMsg长度固定, timestamp仅用于判断折叠，取秒以后即可
    Status ret = DbLogTextPrintf(logText, "%08" PRIu64 ", ", (nowTimeStamp % USECOND_OF_ONE_MIN));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbLogAddThreadMsg(logText);
    if (ret != GMERR_OK) {
        return ret;
    }
    // timestamp + threadMsg + header，header长度固定
    ret = DbLogAddHeader(logText, &info->header, &info->place);
    if (ret != GMERR_OK) {
        return ret;
    }
    // timestamp + threadMsg + header + 错误码对应公共字符串， 错误码对应公共字符串长度不固定
    if (isFormat) {
        const DbErrDetailT *errCodeDetail = DbErrGetDetail(errCode);
        if (errCodeDetail != NULL) {
            ret = DbLogTextPrintf(logText, "%s ", errCodeDetail->brief);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    // timestamp + threadMsg + header + logDesc，logDesc长度不固定，存在截断情况，logBuf构造完后还需要加'\n'
    ret = DbLogTextVprintf(logText, info->logDesc, args);
    if (ret != GMERR_OK) {
        return ret;
    }
    // timestamp + header + logDesc + logLocation，logLocation长度不固定，存在截断情况
    // 日志包含位置信息前提：debug类型的日志
    if (info->header.type == DB_LOG_TYPE_DEBUG) {
        ret = DbLogTextPrintf(logText, LOG_FILE_LOC_FORMAT, DbLogGetFileName(info->place.fileName), info->place.lineNum,
            info->place.funcName);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

/*
 * description: 由上层传递可变参数列表args的写日志的统一接口
 */
void DbLogWriteWithArgs(bool needFold, DbLogTypeE type, const char *module, uint32_t level, int32_t errCode,
    const char *fileName, uint32_t lineNum, const char *funcName, const char *formatStr, va_list *args, bool isFormat)
{
    DB_POINTER4(module, fileName, funcName, formatStr);
    // 日志内容初始化
    const char *pureFileName = strrchr(fileName, '/');
    if (pureFileName == NULL) {
        pureFileName = fileName;
    } else {
        pureFileName = pureFileName + 1;
    }
    DbLogLocationT place = {.fileName = pureFileName, .lineNum = lineNum, .funcName = funcName, .reserve = 0};
    static uint64_t logId = 0;
    static uint64_t printLogId = 0;
    DbLogHeaderT header = {.type = type,
        .module = module,
        .level = level,
        .tid = DbThreadGetTid(),
        .errCode = errCode,
        .logId = &logId,
        .printLogId = &printLogId};
    (void)DbAtomicInc64(&logId);
    DbLogInfoT logInfo;
    DbLogInitLogContent(&logInfo, &header, &place, formatStr);
    // 日志折叠
    uint64_t nowTimeStamp = DbGetCurrentTimestamp();
    if (needFold) {
        DbLogLazyPrintFoldInfo();
        if (DbLogNeedFold(&logInfo, (int32_t)type, &nowTimeStamp)) {
            return;
        }
    }
    (void)DbAtomicInc64(&printLogId);
    // 构造日志
    char *logBuf = (char *)DB_MALLOC(DB_DEFAULT_LOG_SIZE + 1 - DB_COMMON_LOG_HEADER_SIZE);
    if (logBuf == NULL) {
        return;
    }
    (void)memset_s(logBuf, (DB_DEFAULT_LOG_SIZE + 1 - DB_COMMON_LOG_HEADER_SIZE), 0,
        (DB_DEFAULT_LOG_SIZE + 1 - DB_COMMON_LOG_HEADER_SIZE));
    LogTextStrT logText = {logBuf, (uint32_t)(g_gmdbMaxLogSize + 1 - DB_COMMON_LOG_HEADER_SIZE), 0};
    Status ret = DbLogMakeLog(&logText, &logInfo, nowTimeStamp, args, errCode, isFormat);
    if (ret != GMERR_OK) {
        DB_FREE(logBuf);
        return;
    }
    // 写日志
    DbLogWriteArgT writeArg = {.errCode = errCode, .type = (int32_t)type, .level = level};
#if !defined(NDEBUG) && defined(TEMP_DEBUG)
    // 数存设备联调需要
    WRITE_LOG_FILE_WITH_MS("DEBUG GMERR-%d %s", errCode, logText.buf);
#endif
    DbLogAdptWrite(&writeArg, &logText);
    DB_FREE(logBuf);
}

void DbLogWriteNoFmt(bool needFold, DbLogTypeE type, const char *module, uint32_t level, int32_t errCode,
    const char *fileName, uint32_t lineNum, const char *funcName, ...)
{
    DB_POINTER3(module, fileName, funcName);
    if (g_gmdbIsWriteLog) {
        return;
    }
    g_gmdbIsWriteLog = true;
    const char *formatStr = NULL;
    DbLogCtrlItemT *ctrlItem = DbLogGetCurCtrlItem();
    if (SECUREC_LIKELY(ctrlItem != NULL)) {
        if (!DbLogCanWriteCheckCtrlItem(level, ctrlItem)) {  // 日志开关&级别过滤检查
            g_gmdbIsWriteLog = false;
            return;
        }
        DbLogResetCtrlItem();  // 在日志本地写开关和日志级别超过时间上限时，自动关闭并重置控制项字段
        formatStr = DbErrGetFmtStr((Status)errCode);
        if (formatStr == NULL) {
            g_gmdbIsWriteLog = false;
            return;
        }
        // 获取可变参数列表
        va_list args;
        va_start(args, funcName);
        DbLogWriteWithArgs(
            needFold, type, module, level, errCode, fileName, lineNum, funcName, formatStr, &args, false);
        va_end(args);
        g_gmdbIsWriteLog = false;
        return;
    }
    // 如果日志控制区未初始化, 并且在启动阶段, 我们需要打印 boot日志, 否则不打印
    if (!DbLogIsBootState()) {
        g_gmdbIsWriteLog = false;
        return;
    }
    formatStr = DbErrGetFmtStr((Status)errCode);
    if (formatStr == NULL) {
        g_gmdbIsWriteLog = false;
        return;
    }
    va_list args;
    va_start(args, funcName);
    DbWriteBootLogWithArgs(errCode, level, fileName, lineNum, formatStr, &args, false);
    va_end(args);
    g_gmdbIsWriteLog = false;
    return;
}

bool DbAuditLogCanWrite(const DbAuditLogInfoT *info)
{
    DB_POINTER(info);
    if (info->userName == NULL) {
        return false;
    }
    DbLogCtrlItemT *ctrlItem = DbLogGetCurCtrlItem();
    if (ctrlItem == NULL) {
        return false;
    }
    return DbAuditEnable(info->evtType);
}

// 审计日志：userName, visitedResource, eventType, eventResult, eventDesc
#define DB_AUDIT_SUCCEED_FORMAT "%s, %s, %s, success, "
#define DB_AUDIT_FAILED_FORMAT "%s, %s, %s, fail-%" PRId32 ", "

// 构造一条审计日志：secure
Status DbLogMakeAuditLog(LogTextStrT *logText, const DbAuditLogInfoT *info, va_list *args, int32_t evtResult)
{
    DB_POINTER3(logText, info, args);
    if (info->resource == NULL || info->userName == NULL || info->evtDesc == NULL || info->evtType >= DB_AUDIT_BUTT) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    // 审计事件类型
    char *typeDesc[] = {"DDL", "DCL", "DML", "DQL"};
    Status ret =
        (info->evtResult == (int32_t)GMERR_OK) ?
            DbLogTextPrintf(logText, DB_AUDIT_SUCCEED_FORMAT, info->userName, info->resource, typeDesc[info->evtType]) :
            DbLogTextPrintf(logText, DB_AUDIT_FAILED_FORMAT, info->userName, info->resource, typeDesc[info->evtType],
                info->evtResult);
    if (ret != GMERR_OK) {
        return ret;
    }
    // evtResult对应的公共字符串信息
    const DbErrDetailT *errCodeDetail = DbErrGetDetail(evtResult);
    if (errCodeDetail != NULL) {
        ret = DbLogTextPrintf(logText, "%s ", errCodeDetail->brief);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    ret = DbLogTextVprintf(logText, info->evtDesc, args);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

void DbAuditWrite(
    const char *userName, const char *resource, DbAuditEvtTypeE evtType, int32_t evtResult, const char *evtDesc, ...)
{
    // 审计日志信息
    if (g_gmdbIsWriteLog) {
        return;
    }
    g_gmdbIsWriteLog = true;
    DbAuditLogInfoT auditInfo = {
        .evtResult = evtResult, .resource = resource, .evtType = evtType, .userName = userName, .evtDesc = evtDesc};
    if (!DbAuditLogCanWrite(&auditInfo)) {
        g_gmdbIsWriteLog = false;
        return;
    }
    // 一条审计日志
    DbLogWriteArgT writeArg = {
        .errCode = evtResult, .type = (int32_t)DB_LOG_TYPE_SECURE, .level = (uint32_t)DB_LOG_LVL_INFO};
    char *auditLog = (char *)DB_MALLOC(DB_DEFAULT_LOG_SIZE + 1);
    if (auditLog == NULL) {
        g_gmdbIsWriteLog = false;
        return;
    }
    (void)memset_s(auditLog, (DB_DEFAULT_LOG_SIZE + 1), 0, (DB_DEFAULT_LOG_SIZE + 1));
    LogTextStrT logText = {auditLog, (uint32_t)(g_gmdbMaxLogSize + 1), 0};
    // 获取可变参数列表
    va_list args;
    va_start(args, evtDesc);
    Status ret = DbLogMakeAuditLog(&logText, &auditInfo, &args, evtResult);
    va_end(args);
    if (ret != GMERR_OK) {
        g_gmdbIsWriteLog = false;
        DB_FREE(auditLog);
        return;
    }
    DbLogAdptWrite(&writeArg, &logText);
    g_gmdbIsWriteLog = false;
    DB_FREE(auditLog);
}

Status DbAppendAuditEvtDesc(DbAuditEvtDescT *evtDesc, const char *format, ...)
{
    DB_POINTER2(evtDesc, format);
    va_list args;
    va_start(args, format);
    int32_t iret = vsnprintf_truncated_s(
        evtDesc->msg + evtDesc->offset, (size_t)(uint16_t)(evtDesc->msgLen - evtDesc->offset), format, args);
    va_end(args);
    if (iret < 0) {
        return GMERR_FIELD_OVERFLOW;
    }
    evtDesc->offset = (uint16_t)(evtDesc->offset + iret);
    if (evtDesc->offset >= evtDesc->msgLen - 1) {
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    return GMERR_OK;
}

void DbLogSetThreadMsg(const char *threadMsg)
{
    (void)strncpy_s(g_gmdbLogThreadMsg, DB_LOG_THREAD_MSG_LEN, threadMsg, strlen(threadMsg));
    g_gmdbLogThreadMsg[DB_LOG_THREAD_MSG_LEN - 1] = 0;
}

void DbLogSetThreadMsgFromConnMgr(char *threadMsgs)
{
    g_gmdbLogThreadMsgFromConnMgr = threadMsgs;
}

void DbLogClearThreadMsgFromConnMgr(void)
{
    g_gmdbLogThreadMsgFromConnMgr = NULL;
}

void DbLogWriteAndSetLastError(bool needFold, DbLogTypeE type, const char *module, uint32_t level, int32_t errCode,
    const char *fileName, uint32_t lineNum, const char *funcName, const char *formatStr, ...)
{
    va_list ap;
    va_start(ap, formatStr);
    char logInfo[LOG_MAX_SIZE_OF_LOG_MSG] = {0};
    int32_t err = vsnprintf_s(logInfo, LOG_MAX_SIZE_OF_LOG_MSG, LOG_MAX_SIZE_OF_LOG_MSG - 1, formatStr, ap);
    if (err < 0) {
        logInfo[LOG_MAX_SIZE_OF_LOG_MSG - 1] = '\0';
    }
    va_end(ap);
    DbLogWrite(needFold, type, module, level, errCode, fileName, lineNum, funcName, "%s", logInfo);
    DbWriteLastErrorCustom(errCode, "%s", logInfo);
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
