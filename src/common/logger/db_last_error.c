/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * Description: source file for last error
 * Author: gaohaiyang
 * Create: 2021-09-16
 */

#include "db_last_error.h"
#include "db_error.h"
#include "adpt_memory.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

static TextT *DbInitLastError(TextT *lastError)
{
    DB_POINTER(lastError);
    if (lastError->str != NULL) {
        return lastError;
    }

    lastError->str = g_gmdbLastErrorStr;

    lastError->str[0] = 0;
    lastError->len = 1;

    return lastError;
}

TextT *DbGetLastErrorInfo(void)
{
    TextT *curLastError = (TextT *)&g_gmdbLastError;
    return DbInitLastError(curLastError);
}

void DbAppendLastErrorInfo(const char *formatStr, ...)
{
    DB_POINTER(formatStr);
    TextT *error = DbGetLastErrorInfo();
    if (error == NULL) {
        return;
    }

    uint32_t size = error->len;
    uint32_t len = size - 1;  // 去除结尾0的长度
    char *buffer = error->str + len;
    uint32_t capacity = LOG_MAX_SIZE_OF_LOG_MSG - len;

    va_list args;
    va_start(args, formatStr);
    int32_t count = vsnprintf_truncated_s(buffer, capacity, formatStr, args);
    va_end(args);

    if (count > 0) {
        // 返回的count不包含结尾0，可直接累加到含0的size上
        // 失败或者无输出时count <= 0，size不变
        error->len = size + (uint32_t)count;
    }

    // vsnprintf_truncated_s负责添加结束符
    DB_ASSERT(error->len <= LOG_MAX_SIZE_OF_LOG_MSG);
    DB_ASSERT(error->str[error->len - 1] == 0);
}

inline static Status LogTextVprintf(TextT *logText, int32_t *len, const char *fmt, va_list *va)
{
    DB_POINTER3(logText, fmt, va);
    DB_ASSERT(logText->len <= LOG_MAX_SIZE_OF_LOG_MSG);
    *len = vsnprintf_truncated_s(logText->str, LOG_MAX_SIZE_OF_LOG_MSG, fmt, *va);
    if (SECUREC_UNLIKELY(*len < 0)) {
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}

inline static Status LogTextPrintf(TextT *logText, int32_t *len, const char *fmt, ...)
{
    DB_POINTER2(logText, fmt);
    va_list args;
    va_start(args, fmt);
    Status ret = LogTextVprintf(logText, len, fmt, &args);
    va_end(args);
    return ret;
}

static void DbWriteLastErrorImpl(TextT *error, const char *formatStr, va_list args, Status errCode, bool isFormat)
{
    DB_POINTER3(error, error->str, formatStr);
    // 拼接错误码对应字符串描述信息
    int32_t errDescWriteLen = 0;
    if (errCode != GMERR_OK && isFormat) {  // 旧宏没有传入errCode，默认为GMERR_OK，不打印错误码描述信息。
        const DbErrDetailT *errCodeDetail = DbErrGetDetail(errCode);
        if (errCodeDetail != NULL) {
            Status ret = LogTextPrintf(error, &errDescWriteLen, "%s ", errCodeDetail->brief);
            if (ret != GMERR_OK) {
                return;
            }
        }
    }
    // 返回的count不包含结尾0，需要额外加1
    int32_t count =
        vsnprintf_truncated_s(error->str + errDescWriteLen, LOG_MAX_SIZE_OF_LOG_MSG - errDescWriteLen, formatStr, args);
    count = DB_MAX(count, 0);
    errDescWriteLen = DB_MAX(errDescWriteLen, 0);
    error->len = (uint32_t)errDescWriteLen + (uint32_t)count + 1;

    // vsnprintf_truncated_s负责添加结束符
    DB_ASSERT(error->len <= LOG_MAX_SIZE_OF_LOG_MSG);
    DB_ASSERT(error->str[error->len - 1] == 0);
}

void DbWriteLastError(Status errCode, ...)
{
    TextT *error = DbGetLastErrorInfo();
    if (error == NULL) {
        return;
    }

    const char *logDescStr = DbErrGetFmtStr(errCode);
    if (logDescStr == NULL) {
        return;
    }

    va_list args;
    va_start(args, errCode);
    DbWriteLastErrorImpl(error, logDescStr, args, errCode, false);
    va_end(args);
}

void DbWriteLastErrorCustom(Status errCode, const char *formatStr, ...)
{
    TextT *error = DbGetLastErrorInfo();
    if (error == NULL) {
        return;
    }

    va_list args;
    va_start(args, formatStr);
    DbWriteLastErrorImpl(error, formatStr, args, errCode, true);
    va_end(args);
}

void DbResetLastErrorInfo(void)
{
    TextT *error = DbGetLastErrorInfo();
    if (error == NULL) {
        return;
    }

    error->str[0] = '\0';
    error->len = 1;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
