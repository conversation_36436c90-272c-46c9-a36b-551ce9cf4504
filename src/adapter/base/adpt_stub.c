/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: adpt_stub.c
 * Description: Implement of stub functions
 * Author:
 * Create: 2025-02-05
 */
#if defined(INJECT)
#include <stdint.h>
#include <stdbool.h>
#include "adpt_types.h"

#if defined(__aarch64__) || defined(__arm__)
#define JMP_CODE_SIZE 16
#elif defined(__x86_64__) || defined(__i386__)
#define JMP_CODE_SIZE 14
#endif

#define MAX_STUB_NUM 32
uint8_t trampolines[MAX_STUB_NUM][JMP_CODE_SIZE] = {0x0};
bool isUsed[MAX_STUB_NUM] = {false};

void *allocTrampoline(uint32_t *index)
{
    for (uint32_t j = 0; j < MAX_STUB_NUM; ++j) {
        if (!isUsed[j]) {
            *index = j;
            isUsed[j] = true;
            return (void *)trampolines[j];
        }
    }
    return 0x0;
}

void freeTrampoline(uint32_t index)
{
    if (index == DB_MAX_UINT32) {
        return;
    }
    isUsed[index] = false;
}
#endif
