/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: adpt_file.c
 * Description: Adapt file operation
 * Author:
 * Create: 2022-03-14
 */
#include <syslog.h>
#include <dirent.h>
#include "db_file.h"
#include "adpt_sleep.h"
#include "adpt_log.h"
#include "adpt_string.h"

#define DB_TRY_WRITE_TIMES 10
#define DB_TIME_5MS 5000

static Status DbAdptProcessRealPath(const char *path, char *realPath, bool isPrintLog)
{
    Status ret = GMERR_OK;
    if (isPrintLog) {
        ret = DbAdptRealPath(path, realPath);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Get path: %s, os: %" PRId32 ".", path, (int32_t)errno);
        }
    } else {
        ret = DbAdptRealPathNoLog(path, realPath);
    }
    return ret;
}

static Status DbProcessRealPath(const char *path, char *realPath, size_t maxLen, bool isPrintLog)
{
    char directory[PATH_MAX] = {0};
    char *fileName = strrchr(path, '/');
    Status ret;

    if (fileName == NULL) {
        ret = DbAdptProcessRealPath("./", realPath, isPrintLog);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (strcat_s(realPath, maxLen, "/") != EOK) {
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Concat str, os: %" PRId32 ".", (int32_t)errno);
            return GMERR_DATA_EXCEPTION;
        }
        if (strcat_s(realPath, maxLen, path) != EOK) {
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Concat str, os: %" PRId32 ".", (int32_t)errno);
            return GMERR_DATA_EXCEPTION;
        }
    } else {
        // 避免当path为 '/' 或者 '/fileName' 时，计算出路径长度为0
        uint32_t dirLen = strlen(path) - strlen(fileName);
        if (strncpy_s(directory, PATH_MAX, path, dirLen ? dirLen : 1) != EOK) {
            DB_LOG_ERROR(
                GMERR_DATA_EXCEPTION, "Str copy, name %s, path: %s, os: %" PRId32 ".", fileName, path, (int32_t)errno);
            return GMERR_DATA_EXCEPTION;
        }
        ret = DbAdptProcessRealPath(directory, realPath, isPrintLog);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (strcat_s(realPath, maxLen, fileName) != EOK) {
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Concat str, name %s, os: %" PRId32 ".", fileName, (int32_t)errno);
            return GMERR_DATA_EXCEPTION;
        }
    }
    return GMERR_OK;
}

Status DbGetRealPath(const char *path, char *realPath, size_t maxLen)
{
    DB_POINTER2(path, realPath);
    return DbProcessRealPath(path, realPath, maxLen, true);
}

Status DbGetRealPathNoLog(const char *path, char *realPath, size_t maxLen)
{
    DB_POINTER2(path, realPath);
    return DbProcessRealPath(path, realPath, maxLen, false);
}

Status DbRemoveFile(const char *fileName)
{
    Status ret = DbAdptRemove(fileName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Remove file: %s, os: %" PRId32 ".", fileName, (int32_t)errno);
    }
    return ret;
}

Status DbRemoveFileNoLog(const char *fileName)
{
    return DbAdptRemoveNoLog(fileName);
}

static bool DbAdptFileExist(const char *file, struct stat *buf)
{
    DB_POINTER2(file, buf);
    Status ret = stat(file, buf);
    if (ret != GMERR_OK) {
        return false;
    }
    return true;
}

bool DbFileExist(const char *filePath)
{
    DB_POINTER(filePath);
    struct stat statBuf;

    if (!DbAdptFileExist(filePath, &statBuf)) {
        return false;
    }

    /* S_ISREG: judge whether it's a regular file or not by the flag */
    if (S_ISREG(statBuf.st_mode)) {
        return true;
    }

    return false;
}

Status DbOpenFile(const char *fullPath, int32_t flag, uint32_t permission, int32_t *fd)
{
    DB_POINTER2(fullPath, fd);
    char realPath[PATH_MAX] = {0};
    Status ret = DbGetRealPathNoLog(fullPath, realPath, PATH_MAX);
    if (ret != GMERR_OK) {
        return ret;
    }

    int32_t openFd;
    ret = DbAdptOpen(realPath, flag, permission, &openFd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Open file: %s, os: %" PRId32 ".", realPath, (int32_t)errno);
        return ret;
    }

    *fd = openFd;

    return GMERR_OK;
}

#ifdef FEATURE_REPLICATION
Status DbOpenPipe(int pipefd[2])
{
    DB_POINTER(pipefd);
    Status ret = DbAdptPipe(pipefd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Open pipe, os: %" PRId32 ".", (int32_t)errno);
        return ret;
    }

    return GMERR_OK;
}
#endif

Status DbReadFile(int32_t fd, void *buf, size_t count, size_t *readCount)
{
    DB_POINTER2(buf, readCount);
    Status ret = DbAdptRead(fd, buf, count, readCount);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Read file, count: %" PRId32 ", os: %" PRId32 ".", (int32_t)count, (int32_t)errno);
        return ret;
    }

    return GMERR_OK;
}

Status DbWriteFile(int32_t fd, const void *buf, size_t count)
{
    DB_POINTER(buf);
    size_t writtenCount;
    uint32_t tryTimes = 0;
    Status ret = GMERR_OK;
    for (; tryTimes < DB_TRY_WRITE_TIMES; tryTimes++) {
        ret = DbAdptWrite(fd, buf, count, &writtenCount);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Write file, count: %" PRId32 ", os:  %" PRId32 ".", (int32_t)count, (int32_t)errno);
            return ret;
        }
        if (writtenCount == count) {
            break;
        } else if (writtenCount == 0) {
            DbUsleep(DB_TIME_5MS);
            continue;
        } else {
            DB_LOG_ERROR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "Write file, count: %" PRId32 ", os: %" PRId32 ".",
                (int32_t)count, (int32_t)errno);
            return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
        }
    }
    return (tryTimes == DB_TRY_WRITE_TIMES) ? GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE : GMERR_OK;
}

void DbCloseFile(int32_t fd)
{
    (void)DbAdptClose(fd);
}

Status DbFileSize(const char *filePath, size_t *size)
{
    DB_POINTER2(filePath, size);
    struct stat statBuf;
    Status ret = DbAdptStat(filePath, &statBuf);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get file stat: %s, os: %" PRId32 ".", filePath, (int32_t)errno);
        return ret;
    }

    *size = (size_t)statBuf.st_size; /* size is long long */
    return GMERR_OK;
}

Status DbFileSizeByFd(int fd, size_t *size)
{
    DB_POINTER(size);
    struct stat statBuf;
    Status ret = DbAdptFStat(fd, &statBuf);
    if (ret != GMERR_OK) {
        return ret;
    }

    *size = (size_t)statBuf.st_size; /* size is long long */
    return GMERR_OK;
}

Status DbReadFileExpSize(int32_t fd, void *buf, size_t count)
{
    size_t expectCount = 0;

    Status ret = DbReadFile(fd, buf, count, &expectCount);
    if ((ret != GMERR_OK) || (expectCount != count)) {
        DB_LOG_ERROR(ret, "Read file expSize, expectCount: %" PRId64 ", count: %" PRId64 ", os: %" PRId32 ".",
            (int64_t)expectCount, (int64_t)count, (int32_t)errno);
        return ret;
    }

    return GMERR_OK;
}

#if (!defined HPE) || (defined FEATURE_TS) || defined(TS_MULTI_INST) || (defined HPE_SIMULATION)
static inline Status DbMakeDirectoryGetErrCode(void)
{
    if (errno == EEXIST) {
        DB_LOG_WARN(GMERR_DIRECTORY_OPERATE_FAILED, "Directory already exist, os: %" PRId32 ".", (int32_t)errno);
        return GMERR_OK;
    } else {
        Status errCode = (errno == ENOSPC) ? GMERR_DISK_NO_SPACE_ERROR : GMERR_DIRECTORY_OPERATE_FAILED;
        DB_LOG_ERROR(errCode, "Make dir, os: %" PRId32 ".", (int32_t)errno);
        return errCode;
    }
}

Status DbMakeDirectory(const char *fullPath, uint32_t mode)
{
    if (fullPath == NULL) {
        DB_LOG_ERROR(GMERR_NO_DATA, "Path is null.");
        return GMERR_NO_DATA;
    }
    if (strlen(fullPath) > DB_MAX_PATH) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Path too long, len: %" PRIu64 ", os: %" PRId32 ".",
            (uint64_t)strlen(fullPath), (int32_t)errno);
        return GMERR_DATA_EXCEPTION;
    }

    /* Check for existence */
    if (access(fullPath, 0) != -1) {
        return GMERR_OK;
    }

    char tmpPath[DB_MAX_PATH + 1];
    const char *pcur = fullPath;

    (void)memset_s(tmpPath, sizeof(tmpPath), 0, sizeof(tmpPath));
    int pos = 0;
    while (*pcur++ != '\0') {
        tmpPath[pos++] = *(char *)((uintptr_t)pcur - 1);
        if ((*pcur == OS_PATH_SEP_CHAR || *pcur == '\0') && access(tmpPath, 0) != 0 && strlen(tmpPath) > 0) {
            Status ret = mkdir(tmpPath, mode);
            if (ret != GMERR_OK) {
                ret = DbMakeDirectoryGetErrCode();
                if (ret != GMERR_OK) {
                    return ret;
                }
            }
        }
    }
    return GMERR_OK;
}
#endif

Status DbRemoveDir(const char *filePath)
{
    DB_POINTER(filePath);
    struct stat statBuf;
    Status ret = DbAdptStat(filePath, &statBuf);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (S_ISREG(statBuf.st_mode)) {
        return DbAdptRemove(filePath);
    }
    if (!S_ISDIR(statBuf.st_mode)) {
        DB_LOG_ERROR(GMERR_DIRECTORY_OPERATE_FAILED, "Inv type: %s", filePath);
        return GMERR_DIRECTORY_OPERATE_FAILED;
    }
    DIR *dir = NULL;
    ret = DbAdptOpenDir(filePath, (void **)&dir);
    if (ret != GMERR_OK) {
        return ret;
    }
    char fileName[PATH_MAX] = {0};
    while (DbAdptReadDir(dir, fileName, PATH_MAX) == GMERR_OK) {
        if (DbStrCmp(".", fileName, false) == 0 || DbStrCmp("..", fileName, false) == 0) {
            continue;
        }
        char subPath[PATH_MAX] = {0};
        int32_t pathLen = sprintf_s(subPath, PATH_MAX, "%s/%s", filePath, fileName);
        if (pathLen < 0) {
            DbAdptCloseDir(dir);
            DB_LOG_ERROR(GMERR_DIRECTORY_OPERATE_FAILED, "Sub file path: %s", subPath);
            return GMERR_DIRECTORY_OPERATE_FAILED;
        }
        ret = DbRemoveDir(subPath);
        if (ret != GMERR_OK) {
            DbAdptCloseDir(dir);
            DB_LOG_ERROR(ret, "Remove: %s", subPath);
            return ret;
        }
    }
    DbAdptCloseDir(dir);

    return DbAdptRemove(filePath);
}

bool DbDirExist(const char *dirPath)
{
    DB_POINTER(dirPath);
    struct stat statBuf;
    Status ret = DbAdptStat(dirPath, &statBuf);
    if (ret != GMERR_OK) {
        return false;
    }
    /* S_ISDIR: judge whether it's a regular directory or not by the flag */
    if (S_ISDIR(statBuf.st_mode)) {
        return true;
    }

    return false;
}

Status DbReadLine(int32_t fd, char *buffer, size_t bufferSize, size_t *totalRead)
{
    DB_POINTER2(buffer, totalRead);
    char *buf = buffer;
    *totalRead = 0;
    uint32_t readNums = 0;
    size_t readCnt;
    char ch;
    Status ret = GMERR_OK;
    do {
        ret = DbAdptRead(fd, &ch, 1, &readCnt);
        if (ret != GMERR_OK) {
            // read error occur
            DB_LOG_ERROR(ret, "Read file, os: %" PRId32 ".", (int32_t)errno);
            return ret;
        }

        if (readCnt == 0) {
            // no data
            break;
        }

        if (readNums < (bufferSize - 1)) {
            readNums++;
            *buf++ = ch;
        }
    } while (ch != '\n');

    *buf = '\0';
    *totalRead = readNums;
    return GMERR_OK;
}

Status DbOpenDir(const char *dirPath, DbDIRT **dir)
{
    return DbAdptOpenDir(dirPath, (void **)dir);
}

Status DbReadDir(DbDIRT *dir, char subDir[], uint32_t subDirSize)
{
    return DbAdptReadDir(dir, subDir, subDirSize);
}

void DbCloseDir(DbDIRT *dir)
{
    DbAdptCloseDir(dir);
}

Status DbPreadFile(int32_t fd, char *buf, uint32_t count, int64_t offset, uint32_t *readCount)
{
    DB_POINTER2(buf, readCount);
    Status ret = DbAdptPread(fd, buf, count, offset, readCount);
    if (ret != GMERR_OK || *readCount != count) {
        size_t size = 0;
        DbFileSizeByFd(fd, &size);
        DB_LOG_ERROR(ret,
            "Unable to pread file, os no %" PRId32 ", expected count = %" PRIu32 ", read count = %" PRIu32
            ", offset = %" PRIu32 ", file size = %" PRIu32,
            (int32_t)errno, count, *readCount, offset, size);
    }
    return ret;
}

Status DbPwriteFile(int32_t fd, const char *buf, uint32_t count, int64_t offset)
{
    DB_POINTER(buf);
    uint32_t writtenCount;
    uint32_t tryTimes = 0;
    Status ret = GMERR_OK;
    for (; tryTimes < DB_TRY_WRITE_TIMES; tryTimes++) {
        ret = DbAdptPwrite(fd, buf, count, offset, &writtenCount);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Pwrite file, os: %" PRId32 ".", (int32_t)errno);
            return ret;
        }
        if (writtenCount == count) {
            break;
        } else if (writtenCount == 0) {
            DbUsleep(DB_TIME_5MS);
            continue;
        } else {
            return GMERR_DISK_NO_SPACE_ERROR;
        }
    }
    return (tryTimes == DB_TRY_WRITE_TIMES) ? GMERR_DISK_NO_SPACE_ERROR : GMERR_OK;
}

Status DbFsyncFile(int32_t fd)
{
    return DbAdptFsync(fd);
}

Status DbSeekFileHead(int32_t fd, int64_t offset)
{
    return DbAdptSeekFileHead(fd, offset);
}

Status DbSeekFileEnd(int32_t fd, int64_t offset)
{
    return DbAdptSeekFileEnd(fd, offset);
}

void DbRemoveUmask(void)
{
    (void)DbAdptRemoveUmask();
}
