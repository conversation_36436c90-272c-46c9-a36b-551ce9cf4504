/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: db_locator.c
 * Description: source file for gmdb locator
 * Author:
 * Create: 2020-9-25
 */

#include "adpt_locator.h"
#include <securec.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include "adpt_chan_define.h"
#include "adpt_log.h"
#include "adpt_tcp_euler.h"

#ifdef __cplusplus
extern "C" {
#endif

#define DB_IP_LOC1 0
#define DB_IP_LOC2 1
#define DB_IP_LOC3 2
#define DB_IP_LOC4 3

typedef Status (*DbLctrParseProc)(DbLctrT *locator, const char *url);
typedef int32_t (*DbLctrFmtProc)(const DbLctrT *locator, char *buff, uint32_t len);
typedef bool (*DbLctrCmpProc)(const DbLctrT *lctr1, const DbLctrT *lctr2);

typedef struct DbLctrDesc {
    DbLctrTypeE type;
    const char *pname;
    DbLctrParseProc parseFunc;
    DbLctrFmtProc fmtFunc;
    DbLctrCmpProc cmpFunc;
} DbLctrDescT;

static bool g_gmdbIsTcp = false;

bool DbIsTcp(void)
{
    return g_gmdbIsTcp;
}

void DbSetIsTcp(bool isTcp)
{
    g_gmdbIsTcp = isTcp;
}

Status DbLctrParseChannel(DbLctrT *locator, const char *url)
{
    DB_POINTER2(locator, url);
    const void *str = strlen(url) == 0 ? CTL_CHANNEL_NAME : url;
    errno_t err = strcpy_s(locator->hpeChannel.channelName, sizeof(locator->hpeChannel.channelName), str);
    return err == EOK ? GMERR_OK : GMERR_FIELD_OVERFLOW;
}

int32_t DbLctrFmtChannel(const DbLctrT *locator, char *buff, uint32_t len)
{
    DB_POINTER2(locator, buff);
    int32_t ret = snprintf_s(buff, len, len - 1, "%s:%s", DB_LCTR_PNAME_HPE_CHAN, locator->hpeChannel.channelName);
    return (ret < 0) ? GMERR_FIELD_OVERFLOW : GMERR_OK;
}

bool DbLctrCmpChannel(const DbLctrT *lctr1, const DbLctrT *lctr2)
{
    return !strcmp(lctr1->hpeChannel.channelName, lctr2->hpeChannel.channelName);
}

Status DbLctrParseDomain(DbLctrT *locator, const char *url)
{
    DB_POINTER2(locator, url);
    if (strlen(url) == 0) {
        // url is empty
        return GMERR_DATA_EXCEPTION;
    }
    errno_t err = strcpy_s(locator->uSocket.domain, sizeof(locator->uSocket.domain), url);
    return err == EOK ? GMERR_OK : GMERR_FIELD_OVERFLOW;
}

int32_t DbLctrFmtDomain(const DbLctrT *locator, char *buff, uint32_t len)
{
    DB_POINTER2(locator, buff);
    int32_t ret = snprintf_s(buff, len, len - 1, "%s:%s", DB_LCTR_PNAME_USOCKET, locator->uSocket.domain);
    return (ret < 0) ? GMERR_FIELD_OVERFLOW : GMERR_OK;
}

bool DbLctrCmpDomain(const DbLctrT *lctr1, const DbLctrT *lctr2)
{
    return !strcmp(lctr1->uSocket.domain, lctr2->uSocket.domain);
}

#ifdef HPE
int inet_aton(const char *__cp, struct in_addr *__inp)
{
    return 0;
}
#endif

Status DbLctrParseSetIpPort(DbLctrT *locator, const char *ipStr, const char *portStr)
{
    DB_POINTER3(locator, ipStr, portStr);
    struct in_addr ip;
    int32_t ret = inet_aton(ipStr, &ip);
    if (ret == 0) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Parse ip port.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    uint32_t len = (uint32_t)strlen(portStr);
    for (uint32_t i = 0; i < len; i++) {
        if (portStr[i] > '9' || portStr[i] < '0') {
            return GMERR_INVALID_PARAMETER_VALUE;
        }
    }
    char *strEnd = NULL;
    int base = 10;
    locator->tcp.port = (uint16_t)strtol(portStr, &strEnd, base);
    locator->tcp.ip = ip.s_addr;
    return GMERR_OK;
}

static int32_t DbLctrCaseInsStrCmp(const char *str1, const char *str2)
{
    const char *s1 = str1;
    const char *s2 = str2;
    const char diff = 'a' - 'A';
    while (true) {
        int32_t v1 = (*s1 >= 'a' && *s1 <= 'z') ? ((int32_t)*s1 - (int32_t)diff) : *s1;
        ++s1;
        int32_t v2 = (*s2 >= 'a' && *s2 <= 'z') ? ((int32_t)*s2 - (int32_t)diff) : *s2;
        ++s2;
        if (v1 < v2) {
            return -1;
        } else if (v1 > v2) {
            return 1;
        } else if (v1 == '\0') {
            return 0;
        }
    }
}

Status DbLctrParseIp(DbLctrT *locator, const char *url)
{
    DB_POINTER2(locator, url);
    char urlFormated[DB_LCTR_MAX_LEN];
    errno_t error = strcpy_s(urlFormated, DB_LCTR_MAX_LEN, url);
    if (error != EOK) {
        return GMERR_FIELD_OVERFLOW;
    }
    char *locComma = strchr(urlFormated, ',');
    if (locComma == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Ip has no comma.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    *locComma = 0;
    char *ipStr = urlFormated;
    char *portStr = locComma + 1;

    char *locEqual = strchr(ipStr, '=');
    if (locEqual == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Ip has no \'=\'.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    *locEqual = 0;
    if (DbLctrCaseInsStrCmp(ipStr, DB_LCTR_KEY_STR_IP) != 0) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Url has no key word %s.", DB_LCTR_KEY_STR_IP);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    ipStr = locEqual + 1;

    locEqual = strchr(portStr, '=');
    if (locEqual == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Port has no \'=\'.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    *locEqual = 0;
    if (DbLctrCaseInsStrCmp(portStr, DB_LCTR_KEY_STR_PORT) != 0) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Url has no key word %s.", DB_LCTR_KEY_STR_PORT);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    portStr = locEqual + 1;

    return DbLctrParseSetIpPort(locator, ipStr, portStr);
}

int32_t DbLctrFmtTcp(const DbLctrT *locator, char *buff, uint32_t len)
{
    DB_POINTER2(locator, buff);
    int32_t ret = snprintf_s(buff, len, len - 1, "%s:%s=%u.%u.%u.%u,%s=%u", DB_LCTR_PNAME_TCP, DB_LCTR_KEY_STR_IP,
        locator->tcp.ipSecs[DB_IP_LOC1], locator->tcp.ipSecs[DB_IP_LOC2], locator->tcp.ipSecs[DB_IP_LOC3],
        locator->tcp.ipSecs[DB_IP_LOC4], DB_LCTR_KEY_STR_PORT, locator->tcp.port);
    return (ret < 0) ? GMERR_FIELD_OVERFLOW : GMERR_OK;
}

bool DbLctrCmpTcp(const DbLctrT *lctr1, const DbLctrT *lctr2)
{
    return lctr1->tcp.ip == lctr2->tcp.ip && lctr1->tcp.port == lctr2->tcp.port;
}

// 用途：获取通信类型对应的listenLocator的解析函数以及构造函数
// 是否并发初始化：否
// 是否并发读写：否
// 并发方案：无
const DbLctrDescT g_gmdbLctrDescs[] = {
    {LCTR_TYPE_HPE_CHANNEL, DB_LCTR_PNAME_HPE_CHAN, DbLctrParseChannel, DbLctrFmtChannel, DbLctrCmpChannel},
    {LCTR_TYPE_USOCKET, DB_LCTR_PNAME_USOCKET, DbLctrParseDomain, DbLctrFmtDomain, DbLctrCmpDomain},
    {LCTR_TYPE_TCP, DB_LCTR_PNAME_TCP, DbLctrParseIp, DbLctrFmtTcp, DbLctrCmpTcp},
    {LCTR_TYPE_USOCKET, DB_LCTR_PNAME_DOPHI, DbLctrParseDomain, DbLctrFmtDomain, DbLctrCmpDomain},
};

const DbLctrDescT *DbLctrGetDesc(DbLctrTypeE type)
{
    uint32_t lctrTypeNum = sizeof(g_gmdbLctrDescs) / sizeof(g_gmdbLctrDescs[0]);

    for (uint32_t i = 0; i < lctrTypeNum; i++) {
        if (g_gmdbLctrDescs[i].type == type) {
            return &g_gmdbLctrDescs[i];
        }
    }
    DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Inv lctr type: %u.", (uint32_t)type);
    return NULL;
}

Status DbLctrGetType(DbLctrTypeE *type, const char *strType)
{
    DB_POINTER2(type, strType);

    uint32_t lctrTypeNum = sizeof(g_gmdbLctrDescs) / sizeof(g_gmdbLctrDescs[0]);
    for (uint32_t i = 0; i < lctrTypeNum; i++) {
        if (DbLctrCaseInsStrCmp(g_gmdbLctrDescs[i].pname, strType) == 0) {
            *type = g_gmdbLctrDescs[i].type;
            return GMERR_OK;
        }
    }
    DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Inv str type:%s.", strType);
    return GMERR_INVALID_PARAMETER_VALUE;
}

Status DbLctrParse(DbLctrT *locator, const char *url)
{
    if (locator == NULL || url == NULL) {
        DB_LOG_ERROR(GMERR_NO_DATA, "Parse locator NULL.");
        return GMERR_NO_DATA;
    }

    const char *locSemicolon = (const char *)strchr(url, ';');
    uint32_t urlLen = (locSemicolon == NULL) ? (uint32_t)strlen(url) : (uint32_t)(locSemicolon - url);
    if (urlLen >= DB_LCTR_MAX_LEN) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Url too long:%u.", urlLen);
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    char urlFormated[DB_LCTR_MAX_LEN] = {0};
    errno_t ret = strncpy_s(urlFormated, DB_LCTR_MAX_LEN, url, urlLen);
    if (ret != EOK) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW, "String urlFormated. ret: %" PRId32, ret);
        return GMERR_FIELD_OVERFLOW;
    }

    char *locColon = strchr(urlFormated, ':');
    if (locColon == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "locColon strchr mistake.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    int32_t typeLen = (int32_t)((uintptr_t)locColon - (uintptr_t)urlFormated);
    if (typeLen >= DB_LCTR_MAX_TYPE_STR_LEN) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Locator parse type mistake.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    char *strType = urlFormated;
    *locColon = 0;

    Status staret = DbLctrGetType(&locator->type, strType);
    if (staret != GMERR_OK) {
        return staret;
    }
#if !defined(FEATURE_NATIVE_TCP) && defined(FEATURE_SERVER_FUNC_REG)
    if (locator->type == LCTR_TYPE_TCP && !DbAdptTcpIsRegister()) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "tcp type need adpt funcs.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
#endif
    const DbLctrDescT *desc = DbLctrGetDesc(locator->type);
    if (desc == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Locator type query get null.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    return desc->parseFunc(locator, locColon + 1);
}

Status DbLctrParseGroup(DbLctrT *locator, uint32_t lctrCnt, const char *url, uint32_t *parseCnt)
{
    if (locator == NULL || url == NULL || parseCnt == NULL) {
        DB_LOG_ERROR(GMERR_NO_DATA, "Input to parse locator group NULL.");
        return GMERR_NO_DATA;
    }
    if (lctrCnt == 0) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "LctrCnt: 0.");
        return GMERR_DATA_EXCEPTION;
    }
    *parseCnt = 0;
    const char *nextLctr = url;
    do {
        Status ret = DbLctrParse(locator + (*parseCnt), nextLctr);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR((int32_t)ret, "Locator %u parse mistake.", *parseCnt);
            return ret;
        }
        (*parseCnt)++;
        const char *loc = strchr(nextLctr, ';');
        nextLctr = (loc == NULL) ? NULL : (loc + 1);
    } while ((nextLctr != NULL) && ((*parseCnt) < lctrCnt));

    return GMERR_OK;
}

Status DbLctrFmt(const DbLctrT *locator, char *buff, int32_t *len)
{
    if (locator == NULL || buff == NULL || len == NULL) {
        DB_LOG_ERROR(GMERR_NO_DATA, "Lctr fmt inv.");
        return GMERR_NO_DATA;
    }
    if (*len <= 0) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Inv len %d.", *len);
        return GMERR_DATA_EXCEPTION;
    }
    const DbLctrDescT *desc = DbLctrGetDesc(locator->type);
    if (desc == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Lctr fmt mistake, type:%" PRId32 "", (int32_t)locator->type);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    Status ret = desc->fmtFunc(locator, buff, *(uint32_t *)len);
    if (ret == GMERR_OK) {
        *len = (int32_t)strlen(buff);
    }
    return ret;
}

bool DbLctrCmp(const DbLctrT *lctr1, const DbLctrT *lctr2)
{
    if (lctr1->type != lctr2->type) {
        return false;
    }
    const DbLctrDescT *desc = DbLctrGetDesc(lctr1->type);
    if (desc == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "LctrCmp mistake: %" PRId32 "", (int32_t)lctr1->type);
        return false;
    }
    return desc->cmpFunc(lctr1, lctr2);
}

#ifdef __cplusplus
}
#endif
