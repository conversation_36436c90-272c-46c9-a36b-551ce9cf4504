/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: adpt_io.c
 * Description: source file for gmdb io interface
 * Author: client
 * Create: 2023-10-10
 */

#include "adpt_io.h"
#include <stdio.h>

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define SAFE_IO_LIB_PATH "libsafeio.so"
#define SAFE_IO_SOCKET_SYM "socket_s"
#define SAFE_IO_ACCEPT_SYM "accept_s"
#define SAFE_IO_OPEN_SYM "open_s"
#define SAFE_IO_PIPE_SYM "pipe_s"
#define SAFE_IO_EPOLL_CREATE_SYM "epoll_create_s"
#define SAFE_IO_TIMERFD_CREATE_SYM "timerfd_create_s"
#define SAFE_IO_EVENTFD_SYM "eventfd_s"
#define SAFE_IO_CLOSE_SYM "close_s"
#define SAFE_IO_READ_SYM "read_s"
#define SAFE_IO_WRITE_SYM "write_s"
#define SAFE_IO_BIND_SYM "bind_s"
#define SAFE_IO_CONNECT_SYM "connect_s"
#define SAFE_IO_LISTEN_SYM "listen_s"
#define SAFE_IO_SEND_SYM "send_s"
#define SAFE_IO_SENDMSG_SYM "sendmsg_s"
#define SAFE_IO_EVENTFDREAD_SYM "eventfd_read_s"
#define SAFE_IO_EVENTFDWRITE_SYM "eventfd_write_s"
#define SAFE_IO_RECV_SYM "recv_s"
#define SAFE_IO_RECVMSG_SYM "recvmsg_s"
#define SAFE_IO_GETSOCKOPT_SYM "getsockopt_s"
#define SAFE_IO_SETSOCKOPT_SYM "setsockopt_s"
#define SAFE_IO_TIMERFD_SETTIME_SYM "timerfd_settime_s"
#define SAFE_IO_POLL_SYM "poll_s"
#define SAFE_IO_MMAP_SYM "mmap_s"
#define SAFE_IO_FSTAT_SYM "fstat_s"
#define SAFE_IO_GET_REL_FD_SYM "safeio_func_ret_fd_get"

DbIoFuncT g_dbIoCtx = {};

HandleT DbIoCtxOpenDlOfSafe(void)
{
    return dlopen(SAFE_IO_LIB_PATH, RTLD_LAZY);
}
#if !DB_ENV_EULER
Status DbEpollCtl(int32_t epFd, int32_t op, int32_t fd, struct epoll_event *event)
{
    EpollCtlFuncT epollCtlFunc = epoll_ctl;

    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t epRelFd = ioCtx->getRelFd(epFd);
    int32_t relFd = ioCtx->getRelFd(fd);
    return epollCtlFunc(epRelFd, op, relFd, event);
}

Status DbEpollWait(int32_t epFd, struct epoll_event *events, int32_t maxEvents, int32_t timeout)
{
    EpollWaitFuncT epollWaitFunc = epoll_wait;

    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t relFd = ioCtx->getRelFd(epFd);
    return epollWaitFunc(relFd, events, maxEvents, timeout);
}

Status DbEpollCreate(int size)
{
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    return ioCtx->epollCreate(size);
}

Status DbEpollAddEvent(int epFd, int fd, struct epoll_event event)
{
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    return ioCtx->epollCtl(epFd, EPOLL_CTL_ADD, fd, &event);
}

Status DbEpollDelEvent(int epFd, int fd)
{
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    return ioCtx->epollCtl(epFd, EPOLL_CTL_DEL, fd, NULL);
}

Status DbLoadSafeIoInterfaceOfOperate1(DbIoFuncT *ioCtx)
{
    Status ret = GMERR_INTERNAL_ERROR;
    ioCtx->read = dlsym(ioCtx->ioHandle, SAFE_IO_READ_SYM);
    if (ioCtx->read == NULL) {
        return ret;
    }

    ioCtx->write = dlsym(ioCtx->ioHandle, SAFE_IO_WRITE_SYM);
    if (ioCtx->write == NULL) {
        return ret;
    }

    ioCtx->bind = dlsym(ioCtx->ioHandle, SAFE_IO_BIND_SYM);
    if (ioCtx->bind == NULL) {
        return ret;
    }

    ioCtx->connect = dlsym(ioCtx->ioHandle, SAFE_IO_CONNECT_SYM);
    if (ioCtx->connect == NULL) {
        return ret;
    }

    ioCtx->listen = dlsym(ioCtx->ioHandle, SAFE_IO_LISTEN_SYM);
    if (ioCtx->listen == NULL) {
        return ret;
    }

    ioCtx->send = dlsym(ioCtx->ioHandle, SAFE_IO_SEND_SYM);
    if (ioCtx->send == NULL) {
        return ret;
    }

    ioCtx->sendMsg = dlsym(ioCtx->ioHandle, SAFE_IO_SENDMSG_SYM);
    if (ioCtx->sendMsg == NULL) {
        return ret;
    }

    ioCtx->fstat = dlsym(ioCtx->ioHandle, SAFE_IO_FSTAT_SYM);
    if (ioCtx->fstat == NULL) {
        return ret;
    }
    return GMERR_OK;
}

Status DbLoadSafeIoInterfaceOfOperate2(DbIoFuncT *ioCtx)
{
    Status ret = GMERR_INTERNAL_ERROR;
    ioCtx->eventFdRead = dlsym(ioCtx->ioHandle, SAFE_IO_EVENTFDREAD_SYM);
    if (ioCtx->eventFdRead == NULL) {
        return ret;
    }

    ioCtx->eventFdWrite = dlsym(ioCtx->ioHandle, SAFE_IO_EVENTFDWRITE_SYM);
    if (ioCtx->eventFdWrite == NULL) {
        return ret;
    }

    ioCtx->recv = dlsym(ioCtx->ioHandle, SAFE_IO_RECV_SYM);
    if (ioCtx->recv == NULL) {
        return ret;
    }

    ioCtx->recvMsg = dlsym(ioCtx->ioHandle, SAFE_IO_RECVMSG_SYM);
    if (ioCtx->recvMsg == NULL) {
        return ret;
    }

    ioCtx->getSockOpt = dlsym(ioCtx->ioHandle, SAFE_IO_GETSOCKOPT_SYM);
    if (ioCtx->getSockOpt == NULL) {
        return ret;
    }

    ioCtx->setSockOpt = dlsym(ioCtx->ioHandle, SAFE_IO_SETSOCKOPT_SYM);
    if (ioCtx->setSockOpt == NULL) {
        return ret;
    }

    ioCtx->timerFdSetTime = dlsym(ioCtx->ioHandle, SAFE_IO_TIMERFD_SETTIME_SYM);
    if (ioCtx->timerFdSetTime == NULL) {
        return ret;
    }

    ioCtx->poll = dlsym(ioCtx->ioHandle, SAFE_IO_POLL_SYM);
    if (ioCtx->poll == NULL) {
        return ret;
    }

    ioCtx->mmap = dlsym(ioCtx->ioHandle, SAFE_IO_MMAP_SYM);
    if (ioCtx->mmap == NULL) {
        return ret;
    }

    ioCtx->getRelFd = dlsym(ioCtx->ioHandle, SAFE_IO_GET_REL_FD_SYM);
    if (ioCtx->getRelFd == NULL) {
        return ret;
    }
    return GMERR_OK;
}

Status DbLoadSafeIoInterfaceOfMangement(DbIoFuncT *ioCtx)
{
    Status ret = GMERR_INTERNAL_ERROR;
    ioCtx->socket = dlsym(ioCtx->ioHandle, SAFE_IO_SOCKET_SYM);
    if (ioCtx->socket == NULL) {
        return ret;
    }

    ioCtx->accept = dlsym(ioCtx->ioHandle, SAFE_IO_ACCEPT_SYM);
    if (ioCtx->accept == NULL) {
        return ret;
    }

    ioCtx->open = dlsym(ioCtx->ioHandle, SAFE_IO_OPEN_SYM);
    if (ioCtx->open == NULL) {
        return ret;
    }

    ioCtx->pipe = dlsym(ioCtx->ioHandle, SAFE_IO_PIPE_SYM);
    if (ioCtx->open == NULL) {
        return ret;
    }

    ioCtx->epollCreate = dlsym(ioCtx->ioHandle, SAFE_IO_EPOLL_CREATE_SYM);
    if (ioCtx->epollCreate == NULL) {
        return ret;
    }

    ioCtx->timerFdCreate = dlsym(ioCtx->ioHandle, SAFE_IO_TIMERFD_CREATE_SYM);
    if (ioCtx->timerFdCreate == NULL) {
        return ret;
    }

    ioCtx->eventFd = dlsym(ioCtx->ioHandle, SAFE_IO_EVENTFD_SYM);
    if (ioCtx->eventFd == NULL) {
        return ret;
    }

    ioCtx->close = dlsym(ioCtx->ioHandle, SAFE_IO_CLOSE_SYM);
    if (ioCtx->close == NULL) {
        return ret;
    }
    return GMERR_OK;
}

void DbLoadNativeIoInterface(DbIoFuncT *ioCtx)
{
    // epoll_ctl_s, epoll_wait_s实现有问题，不支持自定义epoll_event中data，故使用原生接口，传入rel_fd
    ioCtx->epollCtl = DbEpollCtl;
    ioCtx->epollWait = DbEpollWait;
    // 可变参数传入原生接口
    ioCtx->ioctl = (IoCtlFuncT)ioctl;
    ioCtx->fcntl = fcntl;
}

Status DbLoadSafeIoInterface(DbIoFuncT *ioCtx)
{
    DbLoadNativeIoInterface(ioCtx);
    Status ret = DbLoadSafeIoInterfaceOfMangement(ioCtx);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = DbLoadSafeIoInterfaceOfOperate1(ioCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    return DbLoadSafeIoInterfaceOfOperate2(ioCtx);
}
#endif

void DbIoCtxCloseDlOfSafe(DbIoFuncT *ioCtx)
{
    if (ioCtx->ioHandle == NULL) {
        return;
    }

    (void)dlclose(ioCtx->ioHandle);
    ioCtx->ioHandle = NULL;
}

Status DbInitIoCtxOfSafe(DbIoFuncT *ioCtx)
{
    ioCtx->ioHandle = DbIoCtxOpenDlOfSafe();
    if (ioCtx->ioHandle == NULL) {
        return GMERR_INTERNAL_ERROR;
    }
#if !DB_ENV_EULER
    Status ret = DbLoadSafeIoInterface(ioCtx);
    if (ret != GMERR_OK) {
        DbIoCtxCloseDlOfSafe(ioCtx);
        return ret;
    }
#endif
    return GMERR_OK;
}

int32_t DbGetRelFdOfNative(int32_t fd)
{
    return fd;
}

void DbInitIoCtxOfNative(DbIoFuncT *ioCtx)
{
    *ioCtx = (DbIoFuncT){
        .ioHandle = NULL,
        .open = open,
        .pipe = pipe,
        .ioctl = (IoCtlFuncT)ioctl,
        .socket = socket,
        .accept = accept,
        .epollCreate = epoll_create,
        .timerFdCreate = timerfd_create,
        .eventFd = eventfd,
        .close = close,
        .read = read,
        .eventFdRead = eventfd_read,
        .write = write,
        .eventFdWrite = eventfd_write,
        .fcntl = fcntl,
        .bind = bind,
        .connect = connect,
        .listen = listen,
        .send = send,
        .sendMsg = sendmsg,  // 使用sendmsg_s传递eventfd时，处理辅助数据需要赋值为真实fd
        .recv = recv,
        .recvMsg = recvmsg,
        .getSockOpt = getsockopt,
        .setSockOpt = setsockopt,
        .timerFdSetTime = timerfd_settime,
        .poll = poll,
        .epollCtl = epoll_ctl,    // 未提供支持的安全IO接口，当前未替换
        .epollWait = epoll_wait,  // 未提供支持的安全IO接口，当前未替换
        .mmap = mmap,
        .fstat = fstat,
        .getRelFd = DbGetRelFdOfNative,
    };
}

void DbInitIoCtx(DbIoFuncT *ioCtx)
{
    Status ret = DbInitIoCtxOfSafe(ioCtx);
    if (ret != GMERR_OK) {
        DbInitIoCtxOfNative(ioCtx);
    }
}

DbIoFuncT *DbGetIoCtxPtr(void)
{
    return &g_dbIoCtx;
}

// constructor属性，main启动时自动调用该函数
__attribute__((constructor(101))) void DbAutoInitIoCtx(void)
{
    if (g_dbIoCtx.ioHandle != NULL) {
        return;
    }
    DbInitIoCtx(&g_dbIoCtx);
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
