/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: adpt_semaphore.c
 * Description: Semaphore interfaces
 * Author:
 * Create: 2021-11-3
 */

#include "adpt_semaphore.h"

#ifdef __cplusplus
extern "C" {
#endif

Status DbSemInit(DbSemT *sem, int32_t pshared, uint32_t value)
{
    DB_POINTER(sem);
    sem->share = (pshared == PROCESS_SEMAPHORE);
    return sem->share ? DbShareSemInit(&sem->sem.shareSem, value) : DbThreadSemInit(&sem->sem.threadSem, value);
}

Status DbSemWait(DbSemT *sem)
{
    DB_POINTER(sem);
    return sem->share ? DbShareSemWait(&sem->sem.shareSem) : DbThreadSemWait(&sem->sem.threadSem);
}

Status DbSemTryWait(DbSemT *sem)
{
    DB_POINTER(sem);
    return sem->share ? DbShareSemTryWait(&sem->sem.shareSem) : DbThreadSemTryWait(&sem->sem.threadSem);
}

Status DbSemTimedWait(DbSemT *sem, uint32_t timeoutUs)
{
    DB_POINTER(sem);
    return sem->share ? DbShareSemTimedWait(&sem->sem.shareSem, timeoutUs) :
                        DbThreadSemTimedWait(&sem->sem.threadSem, timeoutUs);
}

Status DbSemPost(DbSemT *sem)
{
    DB_POINTER(sem);
    return sem->share ? DbShareSemPost(&sem->sem.shareSem) : DbThreadSemPost(&sem->sem.threadSem);
}

Status DbSemDestroy(DbSemT *sem)
{
    DB_POINTER(sem);
    return sem->share ? DbShareSemDestroy(&sem->sem.shareSem) : DbThreadSemDestroy(&sem->sem.threadSem);
}

#ifdef __cplusplus
}
#endif
