/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 * File Name: adpt_openssl.c
 * Description: dlopen openssl
 * Author: liuche
 * Create: 2024-09-09
 */

#include "adpt_openssl.h"
#include <dlfcn.h>
#include "adpt_log.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#ifndef FEATURE_MINIKV

typedef unsigned char *(*OSSLHMAC)(const EVP_MD *evpMd, const void *key, int keyLen, const unsigned char *data,
    size_t dataLen, unsigned char *md, unsigned int *mdLen);
typedef int (*RANDPrivBytes)(unsigned char *buf, int num);
typedef void (*RANDSeed)(const void *buf, int num);
typedef EVP_MAC *(*EVPMacFetch)(OSSL_LIB_CTX *libCtx, const char *algoRithm, const char *properties);
typedef EVP_MAC_CTX *(*EVPMacCtxNew)(EVP_MAC *mac);
typedef void (*EVPMacFree)(EVP_MAC *mac);
typedef int (*EVPMacInit)(EVP_MAC_CTX *ctx, const unsigned char *key, size_t keyLen, const OSSL_PARAM params[]);
typedef int (*EVPMacUpdate)(EVP_MAC_CTX *ctx, const unsigned char *data, size_t dataLen);
typedef int (*EVPMacFinal)(EVP_MAC_CTX *ctx, unsigned char *out, size_t *outl, size_t outSize);
typedef void (*EVPMacCtxFree)(EVP_MAC_CTX *ctx);
typedef EVP_MD_CTX *(*EVPMDCtxNew)(void);
typedef void (*EVPMDCtxFree)(EVP_MD_CTX *ctx);
typedef const EVP_MD *(*EVPSha256)(void);
typedef int (*EVPDigestInitEx)(EVP_MD_CTX *ctx, const EVP_MD *type, ENGINE *impl);
typedef int (*EVPDigestUpdate)(EVP_MD_CTX *ctx, const void *data, size_t count);
typedef int (*EVPDigestFinalEx)(EVP_MD_CTX *ctx, unsigned char *md, unsigned int *iSize);
typedef int (*PKCS5PBKDF2HMac)(const char *pass, int passLen, const unsigned char *salt, int saltLen, int iter,
    const EVP_MD *digest, int keyLen, unsigned char *out);
typedef unsigned char *(*DBSHA256)(const unsigned char *d, size_t n, unsigned char *md);
typedef OSSL_PARAM (*ConstructUtf8String)(const char *key, char *buf, size_t bSize);
typedef OSSL_PARAM (*ConstructUtf8End)(void);

typedef struct DbDependentOpensslFuncs {
    void *handle;
    OSSLHMAC hMac;
    RANDPrivBytes randPrivBytes;
    RANDSeed randSeed;
    EVPMacFetch evpMacFetch;
    EVPMacCtxNew evpMacCtxNew;
    EVPMacFree evpMacFree;
    EVPMacInit evpMacInit;
    EVPMacUpdate evpMacUpdate;
    EVPMacFinal evpMacFinal;
    EVPMacCtxFree evpMacCtxFree;
    EVPMDCtxNew evpMdCtxNew;
    EVPMDCtxFree evpMdCtxFree;
    EVPSha256 evpSha256;
    EVPDigestInitEx evpDigestInitEx;
    EVPDigestUpdate evpDigestUpdate;
    EVPDigestFinalEx evpDigestFinalEx;
    PKCS5PBKDF2HMac pkcS5PBKDF2Hmac;
    DBSHA256 sha256;
    ConstructUtf8String constructUtf8String;
    ConstructUtf8End constructUtf8End;
} DbDependentOpensslFuncsT;

DbDependentOpensslFuncsT g_gmdbDependentOpensslFuncs = {0};

typedef void *(*DbDependentOpensslApi)(void);

static inline Status DbInitOpenSSLSymbol(void)
{
    if (g_gmdbDependentOpensslFuncs.handle == NULL) {
        g_gmdbDependentOpensslFuncs.handle = dlopen("libcrypto.so", RTLD_NOW | RTLD_GLOBAL);
        if (g_gmdbDependentOpensslFuncs.handle == NULL) {
            char *errorStr = dlerror();
            DB_LOG_ERROR(GMERR_LOAD_THIRD_PARTY_LIBRARY_FAILED, "dlopen libcrypto.so: %s",
                (errorStr != NULL) ? errorStr : "NULL");
            return GMERR_LOAD_THIRD_PARTY_LIBRARY_FAILED;
        }
    }

    return GMERR_OK;
}

/*
 * dlopen的形式去加载openssl动态库
 * 终端编译时需要编译openssl
 * CT编译形态用归档的交付件openssl的so libcrypto.so, 路径在test目录的third_party/output下按不同形态存放
 */
static Status DbDLSYMOpenSSLSymbol(const char *symName, DbDependentOpensslApi *api)
{
    *api = dlsym(g_gmdbDependentOpensslFuncs.handle, symName);
    if (*api == NULL) {
        char *errorStr = dlerror();
        DB_LOG_ERROR(
            GMERR_GET_THIRD_PARTY_FUNCTION_FAILED, "dlsym %s: %s", symName, (errorStr != NULL) ? errorStr : "NULL");
        return GMERR_GET_THIRD_PARTY_FUNCTION_FAILED;
    }

    return GMERR_OK;
}

#endif

unsigned char *DbHMAC(const EVP_MD *evpMd, const void *key, int keyLen, const unsigned char *data, size_t dataLen,
    unsigned char *md, unsigned int *mdLen)
{
#ifdef FEATURE_MINIKV
    return HMAC(evpMd, key, keyLen, data, dataLen, md, mdLen);
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return NULL;
    }

    if (g_gmdbDependentOpensslFuncs.hMac == NULL) {
        ret = DbDLSYMOpenSSLSymbol("HMAC", (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.hMac));
        if (ret != GMERR_OK) {
            return NULL;
        }
    }

    return g_gmdbDependentOpensslFuncs.hMac(evpMd, key, keyLen, data, dataLen, md, mdLen);
}

int DbRANDPrivBytes(unsigned char *buf, int num)
{
#ifdef FEATURE_MINIKV
    return RAND_priv_bytes(buf, num);
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return -1;
    }

    if (g_gmdbDependentOpensslFuncs.randPrivBytes == NULL) {
        ret = DbDLSYMOpenSSLSymbol(
            "RAND_priv_bytes", (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.randPrivBytes));
        if (ret != GMERR_OK) {
            return -1;
        }
    }

    return g_gmdbDependentOpensslFuncs.randPrivBytes(buf, num);
}

void DbRANDSeed(const void *buf, int num)
{
#ifdef FEATURE_MINIKV
    return RAND_seed(buf, num);
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return;
    }

    if (g_gmdbDependentOpensslFuncs.randSeed == NULL) {
        ret = DbDLSYMOpenSSLSymbol("RAND_seed", (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.randSeed));
        if (ret != GMERR_OK) {
            return;
        }
    }

    return g_gmdbDependentOpensslFuncs.randSeed(buf, num);
}

EVP_MAC *DbEVPMacFetch(OSSL_LIB_CTX *libCtx, const char *algoRithm, const char *properties)
{
#ifdef FEATURE_MINIKV
    return EVP_MAC_fetch(libCtx, algoRithm, properties);
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return NULL;
    }

    if (g_gmdbDependentOpensslFuncs.evpMacFetch == NULL) {
        ret =
            DbDLSYMOpenSSLSymbol("EVP_MAC_fetch", (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.evpMacFetch));
        if (ret != GMERR_OK) {
            return NULL;
        }
    }

    return g_gmdbDependentOpensslFuncs.evpMacFetch(libCtx, algoRithm, properties);
}

EVP_MAC_CTX *DbEVPMacCtxNew(EVP_MAC *mac)
{
#ifdef FEATURE_MINIKV
    return EVP_MAC_CTX_new(mac);
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return NULL;
    }

    if (g_gmdbDependentOpensslFuncs.evpMacCtxNew == NULL) {
        ret = DbDLSYMOpenSSLSymbol(
            "EVP_MAC_CTX_new", (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.evpMacCtxNew));
        if (ret != GMERR_OK) {
            return NULL;
        }
    }

    return g_gmdbDependentOpensslFuncs.evpMacCtxNew(mac);
}

void DbEVPMacFree(EVP_MAC *mac)
{
#ifdef FEATURE_MINIKV
    return EVP_MAC_free(mac);
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return;
    }

    if (g_gmdbDependentOpensslFuncs.evpMacFree == NULL) {
        ret = DbDLSYMOpenSSLSymbol("EVP_MAC_free", (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.evpMacFree));
        if (ret != GMERR_OK) {
            return;
        }
    }

    return g_gmdbDependentOpensslFuncs.evpMacFree(mac);
}

int DbEVPMacInit(EVP_MAC_CTX *ctx, const unsigned char *key, size_t keyLen, const OSSL_PARAM params[])
{
#ifdef FEATURE_MINIKV
    return EVP_MAC_init(ctx, key, keyLen, params);
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return -1;
    }

    if (g_gmdbDependentOpensslFuncs.evpMacInit == NULL) {
        ret = DbDLSYMOpenSSLSymbol("EVP_MAC_init", (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.evpMacInit));
        if (ret != GMERR_OK) {
            return -1;
        }
    }

    return g_gmdbDependentOpensslFuncs.evpMacInit(ctx, key, keyLen, params);
}

int DbEVPMacUpdate(EVP_MAC_CTX *ctx, const unsigned char *data, size_t dataLen)
{
#ifdef FEATURE_MINIKV
    return EVP_MAC_update(ctx, data, dataLen);
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return 0;
    }

    if (g_gmdbDependentOpensslFuncs.evpMacUpdate == NULL) {
        ret = DbDLSYMOpenSSLSymbol(
            "EVP_MAC_update", (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.evpMacUpdate));
        if (ret != GMERR_OK) {
            return 0;
        }
    }

    return g_gmdbDependentOpensslFuncs.evpMacUpdate(ctx, data, dataLen);
}

int DbEVPMacFinal(EVP_MAC_CTX *ctx, unsigned char *out, size_t *outl, size_t outSize)
{
#ifdef FEATURE_MINIKV
    return EVP_MAC_final(ctx, out, outl, outSize);
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return 0;
    }

    if (g_gmdbDependentOpensslFuncs.evpMacFinal == NULL) {
        ret =
            DbDLSYMOpenSSLSymbol("EVP_MAC_final", (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.evpMacFinal));
        if (ret != GMERR_OK) {
            return 0;
        }
    }

    return g_gmdbDependentOpensslFuncs.evpMacFinal(ctx, out, outl, outSize);
}

void DbEVPMacCtxFree(EVP_MAC_CTX *ctx)
{
#ifdef FEATURE_MINIKV
    return EVP_MAC_CTX_free(ctx);
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return;
    }

    if (g_gmdbDependentOpensslFuncs.evpMacCtxFree == NULL) {
        ret = DbDLSYMOpenSSLSymbol(
            "EVP_MAC_CTX_free", (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.evpMacCtxFree));
        if (ret != GMERR_OK) {
            return;
        }
    }

    return g_gmdbDependentOpensslFuncs.evpMacCtxFree(ctx);
}

EVP_MD_CTX *DbEVPMDCtxNew(void)
{
#ifdef FEATURE_MINIKV
    return EVP_MD_CTX_new();
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return NULL;
    }

    if (g_gmdbDependentOpensslFuncs.evpMdCtxNew == NULL) {
        ret =
            DbDLSYMOpenSSLSymbol("EVP_MD_CTX_new", (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.evpMdCtxNew));
        if (ret != GMERR_OK) {
            return NULL;
        }
    }

    return g_gmdbDependentOpensslFuncs.evpMdCtxNew();
}

void DbEVPMDCtxFree(EVP_MD_CTX *ctx)
{
#ifdef FEATURE_MINIKV
    return EVP_MD_CTX_free(ctx);
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return;
    }

    if (g_gmdbDependentOpensslFuncs.evpMdCtxFree == NULL) {
        ret = DbDLSYMOpenSSLSymbol(
            "EVP_MD_CTX_free", (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.evpMdCtxFree));
        if (ret != GMERR_OK) {
            return;
        }
    }

    return g_gmdbDependentOpensslFuncs.evpMdCtxFree(ctx);
}

const EVP_MD *DbEVPSha256(void)
{
#ifdef FEATURE_MINIKV
    return EVP_sha256();
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return NULL;
    }

    if (g_gmdbDependentOpensslFuncs.evpSha256 == NULL) {
        ret = DbDLSYMOpenSSLSymbol("EVP_sha256", (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.evpSha256));
        if (ret != GMERR_OK) {
            return NULL;
        }
    }

    return g_gmdbDependentOpensslFuncs.evpSha256();
}

int DbEVPDigestInitEx(EVP_MD_CTX *ctx, const EVP_MD *type, ENGINE *impl)
{
#ifdef FEATURE_MINIKV
    return EVP_DigestInit_ex(ctx, type, impl);
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return 0;
    }

    if (g_gmdbDependentOpensslFuncs.evpDigestInitEx == NULL) {
        ret = DbDLSYMOpenSSLSymbol(
            "EVP_DigestInit_ex", (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.evpDigestInitEx));
        if (ret != GMERR_OK) {
            return 0;
        }
    }

    return g_gmdbDependentOpensslFuncs.evpDigestInitEx(ctx, type, impl);
}

int DbEVPDigestUpdate(EVP_MD_CTX *ctx, const void *data, size_t count)
{
#ifdef FEATURE_MINIKV
    return EVP_DigestUpdate(ctx, data, count);
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return 0;
    }

    if (g_gmdbDependentOpensslFuncs.evpDigestUpdate == NULL) {
        ret = DbDLSYMOpenSSLSymbol(
            "EVP_DigestUpdate", (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.evpDigestUpdate));
        if (ret != GMERR_OK) {
            return 0;
        }
    }

    return g_gmdbDependentOpensslFuncs.evpDigestUpdate(ctx, data, count);
}

int DbEVPDigestFinalEx(EVP_MD_CTX *ctx, unsigned char *md, unsigned int *iSize)
{
#ifdef FEATURE_MINIKV
    return EVP_DigestFinal_ex(ctx, md, iSize);
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return 0;
    }

    if (g_gmdbDependentOpensslFuncs.evpDigestFinalEx == NULL) {
        ret = DbDLSYMOpenSSLSymbol(
            "EVP_DigestFinal_ex", (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.evpDigestFinalEx));
        if (ret != GMERR_OK) {
            return 0;
        }
    }

    return g_gmdbDependentOpensslFuncs.evpDigestFinalEx(ctx, md, iSize);
}

unsigned char *DbSHA256(const unsigned char *d, size_t n, unsigned char *md)
{
#ifdef FEATURE_MINIKV
    return SHA256(d, n, md);
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return NULL;
    }

    if (g_gmdbDependentOpensslFuncs.sha256 == NULL) {
        ret = DbDLSYMOpenSSLSymbol("SHA256", (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.sha256));
        if (ret != GMERR_OK) {
            return NULL;
        }
    }

    return g_gmdbDependentOpensslFuncs.sha256(d, n, md);
}

int DbPKCS5PBKDF2HMac(const char *pass, int passLen, const unsigned char *salt, int saltLen, int iter,
    const EVP_MD *digest, int keyLen, unsigned char *out)
{
#ifdef FEATURE_MINIKV
    return PKCS5_PBKDF2_HMAC(pass, passLen, salt, saltLen, iter, digest, keyLen, out);
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return 0;
    }

    if (g_gmdbDependentOpensslFuncs.pkcS5PBKDF2Hmac == NULL) {
        ret = DbDLSYMOpenSSLSymbol(
            "PKCS5_PBKDF2_HMAC", (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.pkcS5PBKDF2Hmac));
        if (ret != GMERR_OK) {
            return 0;
        }
    }

    return g_gmdbDependentOpensslFuncs.pkcS5PBKDF2Hmac(pass, passLen, salt, saltLen, iter, digest, keyLen, out);
}

OSSL_PARAM DbConstructUtf8String(const char *key, char *buf, size_t bSize)
{
#ifdef FEATURE_MINIKV
    return OSSL_PARAM_construct_utf8_string(key, buf, bSize);
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return (OSSL_PARAM){0};
    }

    if (g_gmdbDependentOpensslFuncs.constructUtf8String == NULL) {
        ret = DbDLSYMOpenSSLSymbol("OSSL_PARAM_construct_utf8_string",
            (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.constructUtf8String));
        if (ret != GMERR_OK) {
            return (OSSL_PARAM){0};
        }
    }

    return g_gmdbDependentOpensslFuncs.constructUtf8String(key, buf, bSize);
}

OSSL_PARAM DbConstructUtf8End(void)
{
#ifdef FEATURE_MINIKV
    return OSSL_PARAM_construct_end();
#endif
    Status ret = DbInitOpenSSLSymbol();
    if (ret != GMERR_OK) {
        return (OSSL_PARAM){0};
    }

    if (g_gmdbDependentOpensslFuncs.constructUtf8End == NULL) {
        ret = DbDLSYMOpenSSLSymbol(
            "OSSL_PARAM_construct_end", (DbDependentOpensslApi *)&(g_gmdbDependentOpensslFuncs.constructUtf8End));
        if (ret != GMERR_OK) {
            return (OSSL_PARAM){0};
        }
    }

    return g_gmdbDependentOpensslFuncs.constructUtf8End();
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
