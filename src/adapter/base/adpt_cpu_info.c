/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: adpt_cpu_info.c
 * Description: source file for cpu info
 * Author: chendechen
 * Create: 2022-5-27
 */

#include "adpt_cpu_info.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#if defined(PERF_SAMPLE_STAT) || !defined(NDEBUG)
// 用途：保存CPU频率
// 是否并发初始化：否
// 是否并发读写：否
// 并发方案：无
static uint64_t g_cpuFrequency = 0;

#define CPU_INFO_COMMAND "cat /proc/cpuinfo|grep \"cpu MHz\"|sed -e 's/.*:[^0-9]//'"
#define CPU_INFO_STR_LEN 80
#define HPE_5151_CPU_FREQUENCY 1000000000

/*
 * 获取并初始化当前机器主频
 * 在设备侧，当前cpu主频还没有很好的获取方式，暂时以hard-coding的方式
 * 后续考虑提需求，底层环境支持获取当前cpu主频
 */
Status DbInitCPUFrequency(void)
{
#if defined(HPE) || defined(RTOSV2X)
    g_cpuFrequency = HPE_5151_CPU_FREQUENCY;
#else
    FILE *fp;
    char cpuFrequency[CPU_INFO_STR_LEN] = {0};
    fp = popen(CPU_INFO_COMMAND, "r");
    if (fp == NULL) {
        return GMERR_FILE_OPERATE_FAILED;
    }
    char *ret = fgets(cpuFrequency, CPU_INFO_STR_LEN, fp);
    if (ret == NULL) {
        (void)pclose(fp);
        return GMERR_FILE_OPERATE_FAILED;
    }
    int closeRet = pclose(fp);
    if (closeRet != 0) {
        return GMERR_FILE_OPERATE_FAILED;
    }
    char *strEnd = NULL;
    int base = 10;
    g_cpuFrequency = (strtoull(cpuFrequency, &strEnd, base)) * DB_MEGA;
#endif
    DB_ASSERT(g_cpuFrequency != 0);
    return GMERR_OK;
}

#endif

#ifdef __cplusplus
}
#endif /* __cplusplus */
