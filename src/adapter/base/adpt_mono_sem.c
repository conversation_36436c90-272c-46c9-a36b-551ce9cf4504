/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: adpt_mono_sem.c
 * Description: monopoly semaphore interfaces
 * Author:
 * Create: 2021-11-3
 */

#include <sys/time.h>
#include <pthread.h>
#include "adpt_time.h"
#include "adpt_sleep.h"
#include "adpt_spinlock.h"
#include "adpt_mono_sem.h"

#ifdef __cplusplus
extern "C" {
#endif

#define RETRY_DELAY_US ((uint32_t)1)
#define L1_DELAY_US ((uint32_t)10)
#define MAX_DELAY_US ((uint32_t)1 * USECONDS_IN_MSECOND)
#define RETRY_DELAY_MUL ((uint32_t)2)
#define L1_TIME ((uint64_t)10 * USECONDS_IN_MSECOND)

Status DbMonoSemInit(DbMonoSemT *sem, int32_t shared, uint32_t value)
{
    DB_POINTER(sem);
    int32_t ret = sem_init(&sem->sem, shared, value);
#ifndef SEM_CLOCK_WAIT
    sem->retryDelayUs = RETRY_DELAY_US;
#endif
    return (ret != 0) ? GMERR_INTERNAL_ERROR : GMERR_OK;
}

Status DbMonoSemWait(DbMonoSemT *sem)
{
    DB_POINTER(sem);
    int32_t ret = sem_wait(&sem->sem);
    return (ret != 0) ? GMERR_INTERNAL_ERROR : GMERR_OK;
}

Status DbMonoSemTryWait(DbMonoSemT *sem)
{
    DB_POINTER(sem);
    int32_t ret = sem_trywait(&sem->sem);
    return (ret != 0) ? GMERR_INTERNAL_ERROR : GMERR_OK;
}

Status DbMonoSemTimedWait(DbMonoSemT *sem, uint32_t timeoutUs)
{
    DB_POINTER(sem);
    struct timespec tm;
#ifdef SEM_CLOCK_WAIT
    (void)clock_gettime(CLOCK_MONOTONIC, &tm);
#else
    (void)clock_gettime(CLOCK_REALTIME, &tm);
#endif
    uint64_t nsecs = (uint64_t)tm.tv_nsec + (uint64_t)timeoutUs * NSECONDS_IN_USECOND;
    tm.tv_sec = tm.tv_sec + (int64_t)(nsecs / NSECONDS_IN_SECOND);
    tm.tv_nsec = (int64_t)(nsecs % NSECONDS_IN_SECOND);
#ifdef SEM_CLOCK_WAIT
    int32_t ret = sem_clockwait(&sem->sem, CLOCK_MONOTONIC, &tm);
#else
    int32_t ret = sem_timedwait(&sem->sem, &tm);
#endif
    return (ret != 0) ? GMERR_INTERNAL_ERROR : GMERR_OK;
}

// 仅不支持CLOCK_WAIT的共享信号量使用此timedwait接口
#ifndef SEM_CLOCK_WAIT
static uint64_t GetMonotonicTimeUs(void)
{
    struct timespec tm;
    clock_gettime(DB_CLOCK_MONOTONIC, &tm);
    return (uint64_t)(tm.tv_sec * USECONDS_IN_SECOND + tm.tv_nsec / NSECONDS_IN_USECOND);
}

Status DbMockShareSemTimedWait(DbMonoSemT *sem, uint32_t timeoutUs)
{
    DB_POINTER(sem);
    uint64_t startUs = GetMonotonicTimeUs();
    uint32_t elpsedUs = 0;
    // 关于while(true)的特殊说明：此信号量有明确的退出机制，但需要在退出判断后做sleep和退避，减少对性能的影响
    while (true) {
        if (sem_trywait(&sem->sem) == 0) {
            sem->retryDelayUs = RETRY_DELAY_US;
            return GMERR_OK;
        }
        if (errno != EAGAIN) {
            return GMERR_INTERNAL_ERROR;
        }
        elpsedUs = (uint32_t)(GetMonotonicTimeUs() - startUs);
        // 经历的时间低于10ms时，retryDelayUs最大为10us
        uint32_t maxDelayUs = elpsedUs < L1_TIME ? L1_DELAY_US : MAX_DELAY_US;
        if (elpsedUs >= timeoutUs) {
            return GMERR_INTERNAL_ERROR;
        }
        uint32_t remainingUs = timeoutUs - elpsedUs;
        uint32_t sleepUs = (sem->retryDelayUs < remainingUs) ? sem->retryDelayUs : remainingUs;
        DbUsleep(sleepUs);
        sem->retryDelayUs =
            (sem->retryDelayUs * RETRY_DELAY_MUL < maxDelayUs) ? sem->retryDelayUs * RETRY_DELAY_MUL : maxDelayUs;
    }
}
#endif

Status DbMonoSemPost(DbMonoSemT *sem)
{
    DB_POINTER(sem);
    int32_t ret = sem_post(&sem->sem);
    return (ret != 0) ? GMERR_INTERNAL_ERROR : GMERR_OK;
}

Status DbMonoSemDestroy(DbMonoSemT *sem)
{
    DB_POINTER(sem);
    int32_t ret = sem_destroy(&sem->sem);
    return (ret != 0) ? GMERR_INTERNAL_ERROR : GMERR_OK;
}

#ifdef __cplusplus
}
#endif
