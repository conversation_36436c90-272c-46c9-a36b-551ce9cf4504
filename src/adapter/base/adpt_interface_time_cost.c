/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: adpt_interface_time_cost.c
 * Description:用于性能优化打点
 * Author: gaohaiyang
 * Create: 2024-08-29
 */

#include "adpt_interface_time_cost.h"
#include "adpt_rdtsc.h"
#include "gmc_errno.h"
#include "adpt_log.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#if defined(PERF_SAMPLE_STAT)

// 耗时日志长度最值 512 Byte，按需修改
#define TIME_COST_LOG_LEN_MAX 512

typedef struct {
    uint64_t timePoint;
} TimePointArrayT;

typedef struct {
    int32_t opCode;
    uint32_t tpUsedCnt;
    bool writeLog;
    TimePointArrayT tpArray[64];  // 一次 [DbPerfBegin, DbPerfEnd] 之间支持的打点个数最大值为64，按需修改
} DbPerfTimePointT;

// 需要打点的opcode，按需修改
// 以 MSG_OP_RPC_YANG_VALIDATION = 4017, MSG_OP_RPC_SUBTREE_FILTER = 4018 为例子
DbPerfTimePointT g_gmdbPerfTimePoint[] = {{4017, 0, false, {{0}}}, {4018, 0, false, {{0}}}};

inline static DbPerfTimePointT *DbPerfGetTpArrayByOpCode(int32_t opCode)
{
    uint32_t index;
    bool found = false;
    for (index = 0; index < ELEMENT_COUNT(g_gmdbPerfTimePoint); index++) {
        if (g_gmdbPerfTimePoint[index].opCode == opCode) {
            found = true;
            break;
        }
    }
    return found ? &g_gmdbPerfTimePoint[index] : NULL;
}

void DbPerfAppendTpAutomatically(int32_t opCode, bool useGlobalRdtsc)
{
    DbPerfTimePointT *tp = DbPerfGetTpArrayByOpCode(opCode);
    if (tp == NULL || !tp->writeLog) {
        return;
    }
    tp->tpArray[tp->tpUsedCnt].timePoint = useGlobalRdtsc ? DbGlobalRdtsc() : DbRdtsc();
    tp->tpUsedCnt++;
}

void DbPerfAppendTpManually(int32_t opCode, uint64_t timePoint)
{
    DbPerfTimePointT *tp = DbPerfGetTpArrayByOpCode(opCode);
    if (tp == NULL || !tp->writeLog) {
        return;
    }
    tp->tpArray[tp->tpUsedCnt].timePoint = timePoint;
    tp->tpUsedCnt++;
}

void DbPerfAppendTpBreakPoint(int32_t opCode)
{
    DbPerfAppendTpManually(opCode, DB_INVALID_UINT64);
}

inline static bool DbPerfMeetBreakPoint(uint64_t tp)
{
    return tp == DB_INVALID_UINT64;
}

void DbPerfBegin(int32_t opCode)
{
    DbPerfTimePointT *tp = DbPerfGetTpArrayByOpCode(opCode);
    if (tp == NULL) {
        return;
    }
    DB_ASSERT(tp->tpUsedCnt == 0);
    tp->writeLog = true;
    (void)memset_s(tp->tpArray, sizeof(TimePointArrayT) * ELEMENT_COUNT(tp->tpArray), 0u,
        sizeof(TimePointArrayT) * ELEMENT_COUNT(tp->tpArray));
}

void DbPerfEnd(int32_t opCode)
{
    DbPerfTimePointT *tp = DbPerfGetTpArrayByOpCode(opCode);
    if (tp == NULL) {
        return;
    }

    char e2eTimeCostStr[TIME_COST_LOG_LEN_MAX] = {0};
    (void)snprintf_s(
        e2eTimeCostStr, TIME_COST_LOG_LEN_MAX, TIME_COST_LOG_LEN_MAX - 1, "opCode:%" PRId32 ";us;", opCode);
    for (uint32_t i = 0; i < tp->tpUsedCnt - 1; i++) {
        // 自己是断点或者下一个点是断点
        if (DbPerfMeetBreakPoint(tp->tpArray[i].timePoint) || DbPerfMeetBreakPoint(tp->tpArray[i + 1].timePoint)) {
            continue;
        }
        DB_ASSERT(tp->tpArray[i + 1].timePoint > tp->tpArray[i].timePoint);
        uint64_t timeCostUs = DbToUseconds(tp->tpArray[i + 1].timePoint - tp->tpArray[i].timePoint);
        int32_t writeCnt =
            snprintf_s(e2eTimeCostStr + strlen(e2eTimeCostStr), TIME_COST_LOG_LEN_MAX - strlen(e2eTimeCostStr),
                TIME_COST_LOG_LEN_MAX - strlen(e2eTimeCostStr) - 1, "timeCost:%" PRIu64 ";;", timeCostUs);
        if (writeCnt < 0) {
            e2eTimeCostStr[TIME_COST_LOG_LEN_MAX - 1] = '\0';
            break;
        }
    }
    // 至少2个时间点才有意义，该校验逻辑上可以放在函数开头，放在此处是为了方便定位打点少问题
    if (tp->tpUsedCnt <= 1) {
        DB_ASSERT(false);
    }
    DB_LOG_ERROR(GMERR_MAX_VALUE, "%s", e2eTimeCostStr);
    // 打印一轮后，重置计数
    tp->writeLog = false;
    tp->tpUsedCnt = 0;
    (void)memset_s(tp->tpArray, sizeof(TimePointArrayT) * ELEMENT_COUNT(tp->tpArray), 0u,
        sizeof(TimePointArrayT) * ELEMENT_COUNT(tp->tpArray));
}

#endif  // PERF_SAMPLE_STAT

#ifdef __cplusplus
}
#endif /* __cplusplus */
