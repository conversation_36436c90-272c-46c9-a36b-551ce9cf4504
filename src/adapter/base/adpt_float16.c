/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: adpt_float16.c
 * Description: Implement of float16 functions
 * Author:
 * Create: 2025-2-18
 */

#include "adpt_float16.h"
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
#include <arm_fp16.h>
#elif defined(__F16C__)
#include <immintrin.h>
#include <f16cintrin.h>
#endif

// 符号位相关
#define FP32_SIGN_MASK 0x1
#define FP16_SIGN_MASK 0x1

// 指数位相关
#define FP32_EXP_AND_FRAC_BITS 31
#define FP16_EXP_AND_FRAC_BITS 15
#define FP32_EXP_MASK 0xFF
#define FP16_EXP_MASK 0x1F
#define FP32_EXP_SHIFT 127
#define FP16_EXP_SHIFT 15

#define FP16_EXP_ZERO_THRESHOLD (-10)
#define FP16_EXP_MAX 31

// 尾数位相关
#define FP32_FRAC_BITS 23
#define FP16_FRAC_BITS 10
#define FP_FRAC_DIFF_BITS (FP32_FRAC_BITS - FP16_FRAC_BITS)

#define FP32_FRAC_MASK 0x7FFFFF
#define FP16_FRAC_MASK 0x3FF
#define FP32_FRAC_PRE_ONE_MASK 0x800000
#define FP16_FRAC_PRE_ONE_MASK 0x400

// 数值范围
#define FP16_INFINITY_MASK 0x7C00
#define FP16_NAN_MASK 0x7E00
#define FLOAT16_EPSILON 1e-3

float16 DbFloatToFloat16(float num)
{
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
    return (float16)num;
#elif defined(__F16C__)
    return (float16)(_cvtss_sh(num, _MM_FROUND_TO_NEAREST_INT));
#else
    uint32_t bits = *(uint32_t *)&num;
    uint32_t sign = (bits >> FP32_EXP_AND_FRAC_BITS) & FP32_SIGN_MASK;
    uint32_t exp = (bits >> FP32_FRAC_BITS) & FP32_EXP_MASK;
    uint32_t frac = bits & FP32_FRAC_MASK;
    // 处理特殊情况（零、非规格化数、无穷大、NaN）
    if (exp == FP32_EXP_MASK) {
        if (frac == 0) {
            return (float16)((sign << FP16_EXP_AND_FRAC_BITS) | FP16_INFINITY_MASK);  // 无穷大
        } else {
            return (float16)((sign << FP16_EXP_AND_FRAC_BITS) | FP16_NAN_MASK);  // NaN
        }
    }
    // 调整指数和尾数
    int32_t newExp = exp - FP32_EXP_SHIFT + FP16_EXP_SHIFT;
    if (newExp >= FP16_EXP_MAX) {
        return (float16)((sign << FP16_EXP_AND_FRAC_BITS) | FP16_INFINITY_MASK);  // 溢出，返回无穷大
    } else if (newExp <= 0) {
        if (newExp <= FP16_EXP_ZERO_THRESHOLD) {
            return (float16)(sign << FP16_EXP_AND_FRAC_BITS);  // 零
        } else {
            // 非规格化数
            frac |= FP32_FRAC_PRE_ONE_MASK;  // 添加隐含的 1
            frac >>= (1 - newExp);
            return (float16)((sign << FP16_EXP_AND_FRAC_BITS) | (frac >> FP_FRAC_DIFF_BITS));
        }
    } else {
        // 规格化数
        return (float16)((sign << FP16_EXP_AND_FRAC_BITS) | (newExp << FP16_FRAC_BITS) | (frac >> FP_FRAC_DIFF_BITS));
    }
#endif
}

float DbFloat16ToFloat(float16 num)
{
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
    return (float)num;
#elif defined(__F16C__)
    return _cvtsh_ss(num);
#else
    // 提取 FP16 的符号位、指数位和尾数位
    uint16_t sign = (num >> FP16_EXP_AND_FRAC_BITS) & FP16_SIGN_MASK;
    uint16_t exp = (num >> FP16_FRAC_BITS) & FP16_EXP_MASK;
    uint16_t frac = num & FP16_FRAC_MASK;
    // 处理特殊情况（零、非规格化数、无穷大、NaN）
    if (exp == 0) {
        if (frac == 0) {
            return sign ? -0.0f : 0.0f;  // 零
        } else {
            // 非规格化数转换为规格化数
            exp = 1 - FP16_EXP_SHIFT;
            while ((frac & FP16_FRAC_PRE_ONE_MASK) == 0) {
                frac <<= 1;
                exp--;
            }
            frac &= FP16_FRAC_MASK;
        }
    } else if (exp == FP16_EXP_MAX) {
        if (frac == 0) {
            return sign ? -INFINITY : INFINITY;  // 无穷大
        } else {
            return NAN;  // NaN
        }
    } else {
        exp -= FP16_EXP_SHIFT;  // 调整指数
    }
    // 构造 FP32
    uint32_t fp32 =
        (sign << FP32_EXP_AND_FRAC_BITS) | ((exp + FP32_EXP_SHIFT) << FP32_FRAC_BITS) | (frac << FP_FRAC_DIFF_BITS);
    return *(float *)&fp32;
#endif
}

float16 DbFloat16Add(float16 num1, float16 num2)
{
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
    return (float16)(num1 + num2);
#else
    float f1 = DbFloat16ToFloat(num1);
    float f2 = DbFloat16ToFloat(num2);
    return DbFloatToFloat16(f1 + f2);
#endif
}

float DbFloat16AddToFloat(float16 num1, float16 num2)
{
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
    return (float)(num1 + num2);
#else
    return DbFloat16ToFloat(num1) + DbFloat16ToFloat(num2);
#endif
}

float16 DbFloatAddToFloat16(float num1, float num2)
{
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
    return (float16)(num1 + num2);
#else
    return DbFloatToFloat16(num1 + num2);
#endif
}

float16 DbFloat16AddFloat(float16 num1, float num2)
{
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
    return (float16)(num1 + num2);
#else
    return DbFloatToFloat16(DbFloat16ToFloat(num1) + num2);
#endif
}

float16 DbFloat16Sub(float16 num1, float16 num2)
{
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
    return (float16)(num1 - num2);
#else
    float f1 = DbFloat16ToFloat(num1);
    float f2 = DbFloat16ToFloat(num2);
    return DbFloatToFloat16(f1 - f2);
#endif
}

float DbFloat16SubToFloat(float16 num1, float16 num2)
{
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
    return (float)(num1 - num2);
#else
    return DbFloat16ToFloat(num1) - DbFloat16ToFloat(num2);
#endif
}

float16 DbFloatSubToFloat16(float num1, float num2)
{
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
    return (float16)(num1 - num2);
#else
    return DbFloatToFloat16(num1 - num2);
#endif
}

float16 DbFloat16SubFloat(float16 num1, float num2)
{
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
    return (float16)(num1 - num2);
#else
    return DbFloatToFloat16(DbFloat16ToFloat(num1) - num2);
#endif
}

float DbFloat16MulToFloat(float16 num1, float16 num2)
{
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
    return (float)(num1 * num2);
#else
    float f1 = DbFloat16ToFloat(num1);
    float f2 = DbFloat16ToFloat(num2);
    return f1 * f2;
#endif
}

float16 DbFloat16Mul(float16 num1, float16 num2)
{
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
    return (float16)(num1 * num2);
#else
    float f1 = DbFloat16ToFloat(num1);
    float f2 = DbFloat16ToFloat(num2);
    return DbFloatToFloat16(f1 * f2);
#endif
}

float16 DbFloat16MulFloat(float16 num1, float num2)
{
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
    return (float16)(num1 * num2);
#else
    float f1 = DbFloat16ToFloat(num1);
    return DbFloatToFloat16(f1 * num2);
#endif
}

float DbFloat16Div(float16 num1, float num2)
{
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
    return (float)(num1 / num2);
#else
    float f1 = DbFloat16ToFloat(num1);
    return f1 / num2;
#endif
}

float16 DbFloat16DivToFloat16(float16 num1, float num2)
{
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
    return (float16)(num1 / num2);
#else
    float f1 = DbFloat16ToFloat(num1);
    return DbFloatToFloat16(f1 / num2);
#endif
}

bool DbIsFloat16Equal(float16 num1, float16 num2)
{
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
    return fabs((float)num1 - (float)num2) < FLOAT16_EPSILON;
#else
    float f1 = DbFloat16ToFloat(num1);
    float f2 = DbFloat16ToFloat(num2);
    return fabs(f1 - f2) < FLOAT16_EPSILON;
#endif
}

bool DbIsFloat16EqualFloat(float16 num1, float num2)
{
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
    return fabs((float)num1 - num2) < FLOAT16_EPSILON;
#else
    float f1 = DbFloat16ToFloat(num1);
    return fabs(f1 - num2) < FLOAT16_EPSILON;
#endif
}

bool DbIsFloat16Greater(float16 num1, float16 num2)
{
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
    return num1 > num2;
#else
    float f1 = DbFloat16ToFloat(num1);
    float f2 = DbFloat16ToFloat(num2);
    return (fabs(f1 - f2) > FLOAT16_EPSILON) && (f1 > f2);
#endif
}

bool DbIsFloat16GreaterFloat(float16 num1, float num2)
{
#ifdef __ARM_FEATURE_FP16_SCALAR_ARITHMETIC
    return num1 > num2;
#else
    float f1 = DbFloat16ToFloat(num1);
    return (fabs(f1 - num2) > FLOAT16_EPSILON) && (f1 > num2);
#endif
}
