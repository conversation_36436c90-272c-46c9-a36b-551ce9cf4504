/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: adpt_timer_hpe.c
 * Description: adapter hpe timer
 * Author: l<PERSON><PERSON><PERSON>dan
 * Create: 2020-10-21
 */

#include <sys/timerfd.h>
#include "adpt_timer.h"
#include "adpt_register_hpe.h"
#include "adpt_function_loader.h"
#include "adpt_log.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef enum NotificationType {
    NOTIFICATION_VNOTIFY = 0,
    NOTIFICATION_INNER,
    NOTIFICATION_RXTX,
    NOTIFICATION_TIMER,
    NOTIFICATION_TYPE_MAX
} NotificationTypeE;

static struct {
    uintptr_t (*create)(const char *name, void (*callBackFunc)(void *), void *parameter, uint32_t time, uint32_t flag);
    int32_t (*start)(uintptr_t timer);
    int32_t (*stop)(uintptr_t timer);
    int32_t (*delete)(uintptr_t timer);
    int32_t (*ndCreate)(NotificationTypeE type, char *name, int32_t data);
    int32_t (*ndDestroy)(int32_t nd);
    int32_t (*ndSettingTime)(int32_t nd, uint32_t intervalMs);
} g_gmdbHpeTimerAdapter = {NULL};

int32_t HpeNdCreateStub(NotificationTypeE type, char *name, int32_t data)
{
    DB_UNUSED(type);
    DB_UNUSED(name);
    DB_UNUSED(data);
    return 0;
}

int32_t HpeNdDestroyStub(int32_t nd)
{
    DB_UNUSED(nd);
    return 0;
}

int32_t HpeNdSetTimerStub(int32_t nd, uint32_t intervalMs)
{
    DB_UNUSED(nd);
    DB_UNUSED(intervalMs);
    return 0;
}

Status DbAdptRegHpeTimerFuncs(void *adapter)
{
    const FuncTableItemT table[] = {
        {&g_gmdbHpeTimerAdapter.create, "HpeSwTimerCreate"},
        {&g_gmdbHpeTimerAdapter.start, "HpeSwTimerStart"},
        {&g_gmdbHpeTimerAdapter.stop, "HpeSwTimerStop"},
        {&g_gmdbHpeTimerAdapter.delete, "HpeSwTimerDelete"},
    };
    Status ret = DbAdptLoadFunc(adapter, table, ELEMENT_COUNT(table));
    if (ret != GMERR_OK) {
        return ret;
    }
    DbAdptLoadFuncWithStub(adapter, (void **)&g_gmdbHpeTimerAdapter.ndCreate, "HpeNdCreate", HpeNdCreateStub);
    DbAdptLoadFuncWithStub(adapter, (void **)&g_gmdbHpeTimerAdapter.ndDestroy, "HpeNdDestroy", HpeNdDestroyStub);
    DbAdptLoadFuncWithStub(adapter, (void **)&g_gmdbHpeTimerAdapter.ndSettingTime, "HpeNdSetTimer", HpeNdSetTimerStub);
    return GMERR_OK;
}

#define HPE_SW_TIMER_FLAG_ONE_SHOT (0 << 1) /* < one shot timer */
#define HPE_SW_TIMER_FLAG_PERIODIC (1 << 1) /* < periodic timer */

TimerHandleT DbTimerCreate(const char *name, CallBackStructT *inputPara, uint32_t time, TimerModeE type)
{
    TimerHandleT timer = {0};
    if (g_gmdbHpeTimerAdapter.create == NULL || inputPara == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "TimeCreate.");
        return timer;
    }
    void *parameter = inputPara->parameter;
    uint32_t flag = (type == TIMER_MODE_NOLOOP) ? HPE_SW_TIMER_FLAG_ONE_SHOT : HPE_SW_TIMER_FLAG_PERIODIC;
    timer.timerHandle = g_gmdbHpeTimerAdapter.create(name, inputPara->callBack, parameter, time, flag);
    return timer;
}

Status DbTimerStart(TimerHandleT timer, uint32_t type)
{
    if (g_gmdbHpeTimerAdapter.start == NULL) {
        return GMERR_GET_THIRD_PARTY_FUNCTION_FAILED;
    }
    int32_t ret = g_gmdbHpeTimerAdapter.start(timer.timerHandle);
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "timer start. ret: %" PRId32 ", type: %" PRIu32 ".", ret, type);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

Status DbTimerStop(TimerHandleT timer)
{
    if (g_gmdbHpeTimerAdapter.stop == NULL) {
        return GMERR_GET_THIRD_PARTY_FUNCTION_FAILED;
    }
    int32_t ret = g_gmdbHpeTimerAdapter.stop(timer.timerHandle);
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "timer stop: %" PRId32 ".", ret);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

Status DbTimerDelete(TimerHandleT timer)
{
    if (g_gmdbHpeTimerAdapter.delete == NULL) {
        return GMERR_GET_THIRD_PARTY_FUNCTION_FAILED;
    }
    int32_t ret = g_gmdbHpeTimerAdapter.delete(timer.timerHandle);
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "timer delete: %" PRId32 ".", ret);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

bool DbCheckTimerHandle(const TimerHandleT timer)
{
    return timer.timerHandle != 0;
}
// HPE下的timerfd必须用下面的用法，青鸾会在xpoll中自动清理事件，不在上层显式调用read接口
Status DbAdptTimerFdCreate(int clockid, int flags, int32_t *fd)
{
    // hpe环境下创建定时器nd的参数是固定的
    *fd = g_gmdbHpeTimerAdapter.ndCreate(NOTIFICATION_TIMER, NULL, 0);
    if (*fd <= 0) {
        DB_LOG_ERROR(
            GMERR_INTERNAL_ERROR, "timer fd create. fd: %" PRId32 ". os ret: %" PRId32 ".", *fd, (int32_t)errno);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

Status DbAdptTimerFdSetTime(int fd, int flags, struct itimerspec *newTs, struct itimerspec *oldTs)
{
    int32_t ret =
        g_gmdbHpeTimerAdapter.ndSettingTime(fd, newTs->it_interval.tv_sec * 1000);  // hpe的timer接口时间unit：毫秒
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "timer fd set time. ret: %" PRId32 ", fd: %" PRId32 ".", ret, fd);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

Status DbAdptTimerFdClose(int32_t fd)
{
    int32_t ret = g_gmdbHpeTimerAdapter.ndDestroy(fd);
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "timer fd close. ret: %" PRId32 ", fd: %" PRId32 ".", ret, fd);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
