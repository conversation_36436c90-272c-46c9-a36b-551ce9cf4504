/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: adpt_process_id_hpe.c
 * Description: adapter hpe os api
 * Author:
 * Create: 2020-7-27
 */
#include <unistd.h>
#include "adpt_function_loader.h"
#include "adpt_process_id.h"
#include "adpt_process_name.h"

#ifdef __cplusplus
extern "C" {
#endif

static struct {
    uid_t (*getUid)(void);  // VM/BM模式下获取vm.elf/bm.elf的uid, UM模式下获取当前进程的uid
    pid_t (*getPid)(void);  // VM/BM模式下获取vm.elf/bm.elf的pid, UM模式下获取当前进程的pid
    gid_t (*getGid)(void);  // VM/BM模式下获取vm.elf/bm.elf的gid, UM模式下获取当前进程的gid

    void (*signal)(int sig, void (*func)(int));
    int32_t (*sigaction)(int sig, const struct sigaction *act, struct sigaction *oldAct);
} g_gmdbAdptOsHpe = {NULL};

Status DbAdptRegHpeOsFuncs(void *adapter)
{
    const FuncTableItemT table[] = {
        {&g_gmdbAdptOsHpe.getUid, "HPE_GetEnvUid"},
        {&g_gmdbAdptOsHpe.getPid, "HPE_GetEnvPid"},
        {&g_gmdbAdptOsHpe.getGid, "HPE_GetEnvGid"},
        {&g_gmdbAdptOsHpe.signal, "HPE_Signal"},
        {&g_gmdbAdptOsHpe.sigaction, "HPE_Sigaction"},
    };
    return DbAdptLoadFunc(adapter, table, ELEMENT_COUNT(table));
}

int32_t DbAdptSigaction(int32_t sig, const struct sigaction *act, struct sigaction *oldAct)
{
    return g_gmdbAdptOsHpe.sigaction((int)sig, act, oldAct);
}

uint32_t DbAdptGetuid(void)
{
    return g_gmdbAdptOsHpe.getUid();
}

uint32_t DbAdptGetpid(void)
{
    return (uint32_t)getpid();
}

uint32_t DbAdptGetgid(void)
{
    return g_gmdbAdptOsHpe.getGid();
}

int32_t DbAdptFork(void)
{
    return -1;
}

Status DbAdptSetpgid(int32_t pid, int32_t pgid)
{
    return GMERR_INTERNAL_ERROR;
}

bool DbAdptProcessIsExist(uint32_t pid)
{
    char processName[DB_MAX_PROC_NAME_LEN] = {0};
    Status ret = DbGetProcessNameByPid(pid, processName, DB_MAX_PROC_NAME_LEN);
    if (ret != GMERR_OK) {
        return false;
    }
    return true;
}

#ifdef __cplusplus
}
#endif
