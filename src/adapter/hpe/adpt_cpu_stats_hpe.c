/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: adpt_cpu_stats_hpe.c
 * Description: db_cpu_stat file for stastic cpu information.
 * Author: Fang
 * Create: 2021-03-15
 */
#include <unistd.h>
#include "adpt_function_loader.h"
#include "adpt_thread.h"
#include "adpt_cpu_stats.h"

#ifdef __cplusplus
extern "C" {
#endif

#define HPE_CPU_USAGE_TRANS_RATIO 100

typedef enum {
    HPE_CPU_USAGE_LAST_TEN_SECODS = 0,  // get cpu usage in the last ten seconds
    HPE_CPU_USAGE_LAST_ONE_SECODS = 1,  // get cpu usage in the last one seconds
    HPE_CPU_USAGE_ALL_TIME = 0xffff,    // get cpu usage from system startup to now
} HpeCpuUsageModeE;

static struct {
    uint64_t (*hpePthreadGetRunningTime)(pthread_t lwp);
    int32_t (*hpePthreadGetCpuUsage)(pthread_t lwp, int mode);
    int32_t (*hpeProcessGetCpuUsage)(int pid, int mode);
} g_gmdbAdptCpuTime = {NULL};

Status DbAdptRegHpeCpuStatFuncs(void *adapter)
{
    const FuncTableItemT table[] = {
        {&g_gmdbAdptCpuTime.hpePthreadGetRunningTime, "HpePthreadGetRunningTime"},
        {&g_gmdbAdptCpuTime.hpePthreadGetCpuUsage, "HpePthreadGetCpuUsage"},
        {&g_gmdbAdptCpuTime.hpeProcessGetCpuUsage, "HpeProcessGetCpuUsage"},
    };
    return DbAdptLoadFunc(adapter, table, ELEMENT_COUNT(table));
}

// hpe返回的比值是10000，这个接口是10S的时间的cpu占用百分比
uint32_t DbGetAllCpuUsage(void)
{
    int32_t pid = (int)getpid();
    uint32_t cpuHpeUsage = (uint32_t)(g_gmdbAdptCpuTime.hpeProcessGetCpuUsage(pid, HPE_CPU_USAGE_LAST_TEN_SECODS));
    uint32_t cpuUsage = (uint32_t)(cpuHpeUsage / HPE_CPU_USAGE_TRANS_RATIO);
    return cpuUsage;
}

// hpe未提供该接口
uint32_t DbCpuNum(void)
{
    return 0;
}

// hpe返回的比值是10000，为了和欧拉的cpu usage 一致，转换为百分制，一秒内的cpu占用
uint8_t DbGetAvgCpuUsage(void)
{
    int32_t pid = (int)getpid();
    int32_t cpuHpeUsage = g_gmdbAdptCpuTime.hpeProcessGetCpuUsage(pid, HPE_CPU_USAGE_LAST_ONE_SECODS);
    uint8_t cpuUsage = (uint8_t)(cpuHpeUsage / HPE_CPU_USAGE_TRANS_RATIO);
    return cpuUsage;
}

uint64_t DbGetTimeThreadCpuUsec(DbThreadHandle handle)
{
    return g_gmdbAdptCpuTime.hpePthreadGetRunningTime(handle);
}
#ifdef __cplusplus
}
#endif
