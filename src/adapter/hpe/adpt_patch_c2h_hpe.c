/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: adpt_patch_c2h_hpe.c
 * Description: db patch libgmadapter.so cold to hot hook for hpe
 * Author:
 * Create: 2022-11-10
 */
#include "adpt_patch_c2h_hpe.h"
#include "adpt_log.h"

/* 预埋冷转热激活 */
__attribute__((visibility("default"))) void LibGmAdapterHpePatchC2hActiveFunc(void)
{
    VOS_NOP();
    DB_LOG_INFO("LibGmAdapter.so.5 reserved patch c2h active\n");
}

/* 预埋冷转热去激活 */
__attribute__((visibility("default"))) void LibGmAdapterHpePatchC2hDeactiveFunc(void)
{
    VOS_NOP();
    DB_LOG_INFO("LibGmAdapter.so.5 reserved patch c2h deactive\n");
}

// 冷转热钩子函数,给HPE补丁机制调用
int32_t LibGmAdapterPatchC2hHook(bool isActive, const HpePatchInfo *patchInfo, bool *pIsProcess)
{
    if (patchInfo == NULL || pIsProcess == NULL) {
        return 1;
    }
    // 这里的libgmadapter.so.5需要和设备上maps同名
    if (strcmp(patchInfo->acFileName, "libgmadapter.so.5") != 0 &&
        strcmp(patchInfo->acFileName, "libgmadapter.so.5.1") != 0) {
        *pIsProcess = false;
        return 0;
    }
    *pIsProcess = true;

    if (isActive) {
        LibGmAdapterHpePatchC2hActiveFunc();
    } else {
        LibGmAdapterHpePatchC2hDeactiveFunc();
    }

    return 0;
}

// 约定好名字的钩子函数，给dopra补丁机制调用
__attribute__((visibility("default"))) int32_t libgmadapter_PAT_C2HSyncHookFunc(SISP_PAT_C2H_OPER_CODE_E operCode,
    const SISP_PAT_C2H_OPER_CB_S *rspMsg, const SISP_PAT_CFG_INFO_S *cfgInfo, uint32_t *isProcess)
{
    if (cfgInfo == NULL || isProcess == NULL) {
        return 1;
    }
    int32_t ret = 0;
    *isProcess = 0;

    // 这里的libgmdb.so.5需要和设备上maps同名
    if (strcmp(cfgInfo->acFileName, "libgmadapter.so.5") != 0 &&
        strcmp(cfgInfo->acFileName, "libgmadapter.so.5.1") != 0) {
        return ret;
    }

    switch (operCode) {
        case SISP_PAT_C2H_OPER_ACTIVE: {
            LibGmAdapterHpePatchC2hActiveFunc();
            break;
        }
        case SISP_PAT_C2H_OPER_DEACTIVE: {
            LibGmAdapterHpePatchC2hDeactiveFunc();
            break;
        }
        default:
            DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "libgmdb.so c2h active or deactive oper worthless.");
            ret = 1;
    }

    *isProcess = 1;
    return ret;
}
