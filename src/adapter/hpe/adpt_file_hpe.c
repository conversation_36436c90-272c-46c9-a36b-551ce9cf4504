/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: adpt_file_hpe.c
 * Description: Adapt file operation
 * Author:
 * Create: 2022-03-01
 */

#include "dirent.h"
#include "adpt_file.h"
#include "adpt_io.h"
#include "adpt_log.h"

Status DbAdptAccess(const char *pathname, int mode)
{
    DB_POINTER(pathname);
    Status ret = access(pathname, mode);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_FILE_OPERATE_FAILED, "Hpe: Access file: %s, ret: %" PRId32 ", os no: %" PRId32 ".", pathname,
            ret, (int32_t)errno);
        return GMERR_FILE_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status DbAdptClose(int32_t fd)
{
    // hpe的close不支持close epollfd
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    Status ret = ioCtx->close(fd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            GMERR_FILE_OPERATE_FAILED, "Hpe: Close file, ret: %" PRId32 ", os no %" PRId32 ".", ret, (int32_t)errno);
        return GMERR_FILE_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status DbAdptOpen(const char *fullPath, int32_t flag, uint32_t permission, int32_t *fd)
{
    DB_POINTER2(fullPath, fd);
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    *fd = ioCtx->open(fullPath, flag, permission);
    if (*fd == DB_INVALID_FD) {
        DB_LOG_ERROR(GMERR_FILE_OPERATE_FAILED, "Hpe: Open file: %s, os no %" PRId32 ".", fullPath, (int32_t)errno);
        return GMERR_FILE_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status DbAdptRead(int32_t fd, void *buf, size_t count, size_t *readCount)
{
    DB_POINTER(buf);
    // HPE环境下read接口一次读取内容不超过124页
    // 如果传入的不是页对齐的，可能不到500k
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    ssize_t ret = ioCtx->read(fd, buf, count);
    if (ret == -1) {
        // 异步IO读不到内容是正常的,返回OK避免日志打印
        if (errno == EAGAIN) {
            if (readCount != NULL) {
                *readCount = 0;
            }
            return GMERR_OK;
        }

        DB_LOG_ERROR(GMERR_FILE_OPERATE_FAILED, "Hpe: Read file, os no: %" PRId32 ".", (int32_t)errno);
        return GMERR_FILE_OPERATE_FAILED;
    }

    if (readCount != NULL) {
        *readCount = (size_t)ret;
    }
    return GMERR_OK;
}

void DbAdptRemoveUmask(void)
{
    (void)umask(0);
}

Status DbAdptReadNoLog(int32_t fd, void *buf, size_t count, size_t *readCount)
{
    DB_POINTER(buf);
    // HPE环境下read接口一次读取内容不超过124页
    // 如果传入的不是页对齐的，可能不到500k
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    ssize_t ret = ioCtx->read(fd, buf, count);
    if (ret == -1) {
        return GMERR_INTERNAL_ERROR;
    }

    if (readCount != NULL) {
        *readCount = (size_t)ret;
    }
    return GMERR_OK;
}

Status DbAdptReadForTimeEvent(int32_t fd, void *buf, size_t count, size_t *readCount)
{
    return GMERR_OK;
}

void DbAdptReadForCb(int32_t fd, void *buf, size_t count, size_t *readCount)
{
    DB_UNUSED(fd);
    DB_UNUSED(buf);
    DB_UNUSED(count);
    DB_UNUSED(readCount);
}

Status DbAdptRealPath(const char *path, char *realPath)
{
    DB_POINTER2(path, realPath);
    if (realpath(path, realPath) == NULL) {
        int32_t errNum = (int32_t)errno;
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Hpe: Get real path: %s, os no: %" PRId32 ".", path, errNum);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

Status DbAdptRealPathNoLog(const char *path, char *realPath)
{
    DB_POINTER2(path, realPath);
    if (realpath(path, realPath) == NULL) {
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
Status DbAdptRemove(const char *fileName)
{
    DB_POINTER(fileName);
    Status ret = remove(fileName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_FILE_OPERATE_FAILED, "Hpe: Remove file: %s, os no: %" PRId32 ", ret: %" PRId32 ".", fileName,
            (int32_t)errno, ret);
        return GMERR_FILE_OPERATE_FAILED;
    }
    return ret;
}

Status DbAdptRemoveNoLog(const char *fileName)
{
    DB_POINTER(fileName);
    Status ret = remove(fileName);
    if (ret != GMERR_OK) {
        return GMERR_FILE_OPERATE_FAILED;
    }
    return ret;
}

Status DbAdptRename(const char *oldname, const char *newname)
{
    DB_POINTER2(oldname, newname);
    Status ret = rename(oldname, newname);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_FILE_OPERATE_FAILED,
            "Hpe: Rename file, new: %s, old: %s, os no: %" PRId32 ", ret: %" PRId32 ".", newname, oldname,
            (int32_t)errno, ret);
        return GMERR_FILE_OPERATE_FAILED;
    }
    return ret;
}
#else
Status DbAdptRemove(const char *fileName)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status DbAdptRemoveNoLog(const char *fileName)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status DbAdptRename(const char *oldname, const char *newname)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}
#endif

Status DbAdptStat(const char *file, struct stat *buf)
{
    DB_POINTER2(file, buf);
    Status ret = stat(file, buf);
    if (ret != GMERR_OK) {
        return GMERR_FILE_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status DbAdptFStat(int fd, struct stat *buf)
{
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    if (ioCtx->fstat(fd, buf) != 0) {
        DB_LOG_ERROR(GMERR_FILE_OPERATE_FAILED, "Hpe: Get file stat, os no: %" PRId32 ".", (int32_t)errno);
        return GMERR_FILE_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status DbAdptWrite(int32_t fd, const void *buf, size_t count, size_t *writeCount)
{
    DB_POINTER(buf);
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    ssize_t ret = ioCtx->write(fd, buf, count);
    if (ret == -1) {
        Status errCode = (errno == ENOSPC) ? GMERR_DISK_NO_SPACE_ERROR : GMERR_FILE_OPERATE_FAILED;
        DB_LOG_ERROR(errCode, "Hpe: Write file, os no: %" PRId32 ".", (int32_t)errno);
        return errCode;
    }

    if (writeCount != NULL) {
        *writeCount = (size_t)ret;
    }
    return GMERR_OK;
}

Status DbAdptOpenDir(const char *dirPath, void **dir)
{
    DB_POINTER2(dirPath, dir);
    DIR *openedDir = opendir(dirPath);
    if (openedDir == NULL) {
        DB_LOG_ERROR(GMERR_DIRECTORY_OPERATE_FAILED, "Hpe: Open dir: %s, os no %" PRId32 ".", dirPath, (int32_t)errno);
        return GMERR_DIRECTORY_OPERATE_FAILED;
    }

    *dir = openedDir;
    return GMERR_OK;
}

Status DbAdptReadDir(void *dir, char subDir[], uint32_t subDirSize)
{
    DB_POINTER(dir);
    struct dirent *entry = readdir(dir);
    if (entry == NULL) {
        return GMERR_NO_DATA;
    }

    if (strlen(entry->d_name) >= subDirSize) {
        DB_LOG_ERROR(GMERR_DIRECTORY_OPERATE_FAILED, "Hpe: Read dir: %s, len: %" PRIu64 ", os no: %" PRId32 ".",
            entry->d_name, (uint64_t)strlen(entry->d_name), (int32_t)errno);
        return GMERR_DIRECTORY_OPERATE_FAILED;
    }

    if (strcpy_s(subDir, subDirSize, entry->d_name) != EOK) {
        DB_LOG_ERROR(GMERR_DIRECTORY_OPERATE_FAILED, "Hpe: Read dir: %s, len: %" PRIu64 ", os no: %" PRId32 ".",
            entry->d_name, (uint64_t)strlen(entry->d_name), (int32_t)errno);
        return GMERR_DIRECTORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

void DbAdptCloseDir(void *dir)
{
    DB_POINTER(dir);
    (void)closedir(dir);
}

#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
Status DbAdptPread(int32_t fd, char *buf, uint32_t count, int64_t offset, uint32_t *readCount)
{
    DB_POINTER(buf);
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t relFd = ioCtx->getRelFd(fd);
    ssize_t retCount = pread64(relFd, buf, count, offset);
    if (retCount == -1) {
        DB_LOG_ERROR(
            GMERR_FILE_OPERATE_FAILED, "Hpe: Pread, count: %" PRId32 ", os no: %" PRId32 ".", count, (int32_t)errno);
        return GMERR_FILE_OPERATE_FAILED;
    }

    if (readCount != NULL) {
        *readCount = (uint32_t)retCount;
    }

    return GMERR_OK;
}

Status DbAdptPwrite(int32_t fd, const char *buf, uint32_t count, int64_t offset, uint32_t *writeCount)
{
    DB_POINTER(buf);
    // 安全函数没有适配pwrite64函数，调用时需要转换fd
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t relFd = ioCtx->getRelFd(fd);
    ssize_t ret = pwrite64(relFd, buf, count, offset);
    if (ret == -1) {
        Status errCode = (errno == ENOSPC) ? GMERR_DISK_NO_SPACE_ERROR : GMERR_FILE_OPERATE_FAILED;
        DB_LOG_ERROR(errCode, "Hpe: Pwrite, count: %" PRId32 ", os no: %" PRId32 ".", count, (int32_t)errno);
        return errCode;
    }

    if (writeCount != NULL) {
        *writeCount = (uint32_t)ret;
    }
    return GMERR_OK;
}

Status DbAdptFsync(int32_t fd)
{
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t relFd = ioCtx->getRelFd(fd);
    int ret = fsync(relFd);
    if (ret == -1) {
        DB_LOG_ERROR(
            GMERR_FILE_OPERATE_FAILED, "Hpe: Fsync, ret: %" PRId32 ", os no: %" PRId32 ".", ret, (int32_t)errno);
        return GMERR_FILE_OPERATE_FAILED;
    }
    return GMERR_OK;
}
#else
Status DbAdptPread(int32_t fd, char *buf, uint32_t count, int64_t offset, uint32_t *readCount)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status DbAdptPwrite(int32_t fd, const char *buf, uint32_t count, int64_t offset, uint32_t *writeCount)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status DbAdptFsync(int32_t fd)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}
#endif

Status DbAdptSeekFileHead(int32_t fd, int64_t offset)
{
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t relFd = ioCtx->getRelFd(fd);
    int64_t seekOffset = (int64_t)lseek64(relFd, (off64_t)offset, SEEK_SET);
    if (seekOffset != offset) {
        return GMERR_FILE_OPERATE_FAILED;
    }
    return GMERR_OK;
}

#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
Status DbAdptSeekFileEnd(int32_t fd, int64_t offset)
{
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t relFd = ioCtx->getRelFd(fd);
    int64_t seekOffset = (int64_t)lseek64(relFd, (off64_t)offset, SEEK_END);
    if (seekOffset == -1) {
        DB_LOG_ERROR(GMERR_FILE_OPERATE_FAILED, "Hpe: Seek file end, offset: %" PRId64 ", os no: %" PRId32 ".",
            seekOffset, (int32_t)errno);
    }
    return GMERR_OK;
}
#else
Status DbAdptSeekFileEnd(int32_t fd, int64_t offset)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}
#endif

#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
Status DbAdptFtruncate(int32_t fd, int64_t len)
{
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t relFd = ioCtx->getRelFd(fd);
    int ret = ftruncate(relFd, len);
    if (ret != 0) {
        DB_LOG_ERROR(
            GMERR_FILE_OPERATE_FAILED, "Hpe: Truncate file, len: %" PRId64 ", os no: %" PRId32 ".", len, errno);
        return GMERR_FILE_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status DbAdptFallocate(int32_t fd, int32_t mode, uint32_t count, int64_t offset)
{
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t relFd = ioCtx->getRelFd(fd);
    ssize_t ret = fallocate(relFd, mode, offset, count);
    if (ret == -1) {                // ENOSPC stands NO available disk space.
        if (errno == EOPNOTSUPP) {  // hmdfs not support now
            return GMERR_OK;
        } else if (errno == ENOSPC) {
            DB_LOG_ERROR(GMERR_DISK_NO_SPACE_ERROR, "Hpe: Fallocate.");
            return GMERR_DISK_NO_SPACE_ERROR;
        } else {
            DB_LOG_ERROR(GMERR_FILE_OPERATE_FAILED, "Hpe: Fallocate: %" PRId32 ".", (int32_t)errno);
            return GMERR_FILE_OPERATE_FAILED;
        };
    }
    return GMERR_OK;
}
#endif
