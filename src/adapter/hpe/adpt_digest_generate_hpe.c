/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: adpt_digest_generate_hpe.c
 * Description: implement of hpe digest generate
 * Create: 2025-04-30
 */
#include "adpt_digest_generate.h"
#include "adpt_function_loader.h"
#include "adpt_log.h"

#define SE_DIGEST_GENERATE_SUCC 0

DbDigest32GeneFuncT g_gmdbDigest32GenFunc = NULL;

Status DbAdptLoadDigestFunc(char *tamperProofSoPath)
{
    void *soHandle = NULL;
    Status ret = DbAdptLoadLibrary(tamperProofSoPath, &soHandle, RTLD_NOW);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "load persist digest so");
        return ret;
    }
    const FuncTableItemT table[] = {{.symbol = "GmdbDigest32Generate", .destination = &g_gmdbDigest32GenFunc}};
    ret = DbAdptLoadFunc(soHandle, table, ELEMENT_COUNT(table));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "load page digest func");
        return ret;
    }
    return GMERR_OK;
}

Status DbDigest32Generate(uint8_t *data, uint32_t dataLen, DbDigest32T *digest, uint32_t digestLen)
{
    if (g_gmdbDigest32GenFunc != NULL) {
        int32_t ret = g_gmdbDigest32GenFunc(data, dataLen, digest, digestLen);
        if (ret != (int32_t)SE_DIGEST_GENERATE_SUCC) {
            DB_LOG_ERROR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, "generate digest");
            return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
        }
        return GMERR_OK;
    }
    return GMERR_OK;
}
