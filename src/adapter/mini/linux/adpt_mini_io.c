/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: adpt_mini_io.c
 * Description: source file for gmdb io interface
 * Author: minikv
 * Create: 2024-01-22
 */

#include "adpt_io.h"
#include <unistd.h>
#include <fcntl.h>
#include <stdio.h>

#define SAFE_IO_FSYNC_SYM "fsync_s"
#define SAFE_IO_FALLOCATE_SYM "fallocate_s"

Status DbInitIoCtxOfSafeLinux(DbIoFuncT *ioCtx)
{
    ioCtx->fsync = dlsym(ioCtx->io<PERSON><PERSON><PERSON>, SAFE_IO_FSYNC_SYM);
    if (ioCtx->fsync == NULL) {
        return GMERR_INTERNAL_ERROR;
    }

    ioCtx->fallocate = dlsym(ioCtx->io<PERSON><PERSON><PERSON>, SAFE_IO_FALLOCATE_SYM);
    if (ioCtx->fallocate == NULL) {
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

void DbInitIoCtxOfNativeLinux(DbIoFuncT *ioCtx)
{
    ioCtx->fsync = fsync;
    ioCtx->fallocate = fallocate;
}

void DbInitIoCtxLinux(DbIoFuncT *ioCtx)
{
    Status ret = DbInitIoCtxOfSafeLinux(ioCtx);
    if (ret != GMERR_OK) {
        DbIoCtxCloseDlOfSafe(ioCtx);
        DbInitIoCtxOfNative(ioCtx);
        DbInitIoCtxOfNativeLinux(ioCtx);
    }
}

// constructor属性，main启动时自动调用该函数
__attribute__((constructor(102))) void DbAutoInitIoCtxLinux(void)
{
    if (g_dbIoCtx.ioHandle == NULL) {
        DbInitIoCtxOfNativeLinux(&g_dbIoCtx);
    }
    DbInitIoCtxLinux(&g_dbIoCtx);
}
