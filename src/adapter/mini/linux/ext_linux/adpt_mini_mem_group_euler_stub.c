/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Memory group management functions stub for GMDB Euler.
 * Author: minikv
 * Create: 2024-01-22
 */

#include "adpt_memory.h"
#include "adpt_mem_group_euler.h"

ShmemGroupMgrT *g_gmdbShmemGroupMgr[MAX_INSTANCE_NUM] = {NULL};

/*
 * Create a new shmem ctx group. Only called by server.
 */
Status DbAdptInitShmemGroup(uint64_t groupIdIn, uint64_t maxTotalPhySize, const char *groupName, uint64_t *groupIdOut)
{
    return GMERR_OK;
}

// Euler does not require explicit insertion into group.
Status DbAdptAddMember2ShmemGroup(ShmemPtrT poolPtr, uint64_t groupId)
{
    DB_UNUSED(poolPtr);
    DB_UNUSED(groupId);
    return GMERR_OK;
}
