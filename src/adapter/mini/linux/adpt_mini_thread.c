/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: adpt_mini_thread.c
 * Description: Adapter thread operations in Linux
 * Author: minikv
 * Create: 2024-01-19
 */

#include <sys/prctl.h>
#include <sys/syscall.h>
#include "adpt_log.h"
#include "adpt_rdtsc.h"
#include "adpt_sleep.h"
#include "adpt_thread.h"

#define SLEEP_1000MS (1000 * 1000)
#define SLEEP_TIMEOUT_MS (3000)
#define SLEEP_TIMEOUT_GATE (3)

typedef struct {
    char name[DB_THREAD_NAME_MAX_LEN];
    uint32_t priority;
    uint32_t stackSize;
    DbThreadTypeE type;
    DbThreadEntryProc entryFunc;
    void *entryArgs;
} ThreadAttrsForRtosT;

static Status GetEulerThreadAttrs(const ThreadAttrsT *userInputs, ThreadAttrsForRtosT *threadAttrs)
{
    int32_t ret =
        snprintf_s(threadAttrs->name, DB_THREAD_NAME_MAX_LEN, DB_THREAD_NAME_MAX_LEN - 1, "%s", userInputs->name);
    if (ret < 0) {
        return GMERR_FIELD_OVERFLOW;
    }

    threadAttrs->entryFunc = userInputs->entryFunc;
    threadAttrs->entryArgs = userInputs->entryArgs;
    threadAttrs->priority = (uint32_t)userInputs->priority;
    threadAttrs->type = userInputs->type;
    threadAttrs->stackSize = userInputs->stackSize;
    return GMERR_OK;
}

static Status ThreadFuncAttrsCheck(const ThreadAttrsForRtosT *threadAttrs, const DbThreadHandle *handle)
{
    if (threadAttrs == NULL || handle == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    DbThreadTypeE type = threadAttrs->type;
    if ((type != DB_THREAD_DETACHABLE) && (type != DB_THREAD_JOINABLE)) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    return GMERR_OK;
}

static Status SetThreadAttrs(const ThreadAttrsForRtosT *threadAttrs, pthread_attr_t *pthreadAttrs)
{
    if (threadAttrs == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    int32_t ret = (int32_t)pthread_attr_init(pthreadAttrs);
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "thread attrs init, ret: %" PRId32 "os no %" PRId32, ret, errno);
        return GMERR_INTERNAL_ERROR;
    }

    /* set the thread detach state */
    ret = (int32_t)pthread_attr_setdetachstate(pthreadAttrs,
        ((threadAttrs->type == DB_THREAD_DETACHABLE) ? PTHREAD_CREATE_DETACHED : PTHREAD_CREATE_JOINABLE));
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "attrs set detach state, ret: %" PRId32 "os no %" PRId32, ret, errno);
        (void)pthread_attr_destroy(pthreadAttrs);
        return GMERR_INTERNAL_ERROR;
    }

    uint32_t curStackSize = DB_DEFAULT_THREAD_STACK_SIZE;
    if (threadAttrs->stackSize != 0) {
        curStackSize = threadAttrs->stackSize;
    }

    ret = pthread_attr_setstacksize(pthreadAttrs, curStackSize);
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "attr set stach size, ret: %" PRId32 "os no %" PRId32, ret, errno);
        (void)pthread_attr_destroy(pthreadAttrs);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

/* 创建任务时将任务名传入 */
static void *EntryFuncDefaultWrapper(void *threadAttrs)
{
    if (threadAttrs == NULL) {
        return NULL;
    }

    ThreadAttrsForRtosT *tempInfo = (ThreadAttrsForRtosT *)threadAttrs;
    if (prctl(PR_SET_NAME, tempInfo->name) != DB_SUCCESS) {
        free(threadAttrs);
        return NULL;
    }
    void *ret = tempInfo->entryFunc(tempInfo->entryArgs);
    free(threadAttrs);
    return ret;
}

Status DbThreadCreate(const ThreadAttrsT *threadAttrs, DbThreadHandle *handle)
{
    DB_POINTER(threadAttrs);
    ThreadAttrsForRtosT *info = (ThreadAttrsForRtosT *)malloc(sizeof(ThreadAttrsForRtosT));
    if (info == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    Status ret = GetEulerThreadAttrs(threadAttrs, info);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get linux thread attrs.");
        free(info);
        return ret;
    }

    ret = ThreadFuncAttrsCheck(info, handle);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Thread func attrs check");
        free(info);
        return ret;
    }

    pthread_attr_t attrs;
    ret = SetThreadAttrs(info, &attrs);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Set thread attrs, os no %" PRId32 ".", (int32_t)errno);
        free(info);
        return ret;
    }

    int32_t res = pthread_create(handle, &attrs, EntryFuncDefaultWrapper, info);
    if (res != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Create pthread, thread name: (%s), res: %" PRId32 ", os no: %" PRId32 ".",
            info->name, res, (int32_t)errno);
        (void)pthread_attr_destroy(&attrs);
        free(info);
        return GMERR_INTERNAL_ERROR;
    }
    (void)pthread_attr_destroy(&attrs);

    return GMERR_OK;
}

Status DbThreadBindCpuSet(DbThreadHandle handle, cpu_set_t *cpuSet)
{
    return GMERR_OK;
}

void DbThreadStoreCpuSet(cpu_set_t *cpuSet)
{
    return;
}

void DbThreadResetCpuSet(void)
{
    return;
}

Status DbThreadSetName(DbThreadHandle handle, const char *threadName)
{
    int32_t ret = pthread_setname_np(handle, threadName);
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
            "Set thread name %s. ret: %" PRId32 ", os no: %" PRId32 ".", threadName, ret, (int32_t)errno);
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }
    return GMERR_OK;
}

Status DbThreadGetName(DbThreadHandle handle, char *threadName, size_t len)
{
    int32_t ret = pthread_getname_np(handle, threadName, len);
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
            "Get thread name %s. ret: %" PRId32 ", os no: %" PRId32 ".", threadName, ret, (int32_t)errno);
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }
    return GMERR_OK;
}

Status DbThreadJoin(DbThreadHandle handle, void **result)
{
    if (handle == 0) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    int ret = pthread_join(handle, result);
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "pthread_join, ret %" PRId32 ", os no: %" PRId32, ret, errno);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

uint64_t DbThreadGetSelfId(void)
{
    return (uint64_t)pthread_self();
}

DbThreadHandle DbThreadGetTid(void)
{
    return (DbThreadHandle)pthread_self();
}

uint32_t DbThreadGetSysTid(void)
{
    return syscall(SYS_gettid);
}

void DbUsleep(uint32_t timeUs)
{
    uint64_t startCycle = DbRdtsc();
    (void)usleep(timeUs);
    uint64_t endCycle = DbRdtsc();
    uint64_t sleepMseconds = DbToMseconds(endCycle - startCycle);
    // sleep时间小于1秒时，超时打印门禁为3秒；sleep大于1秒时门禁为3倍sleep时间
    if (((timeUs < SLEEP_1000MS) && (sleepMseconds > SLEEP_TIMEOUT_MS)) ||
        ((timeUs >= SLEEP_1000MS) && (sleepMseconds > (SLEEP_TIMEOUT_GATE * timeUs / USECONDS_IN_MSECOND)))) {
        DB_LOG_WARN(GMERR_INTERNAL_ERROR, "EULER sleep timeout, takeMs:%" PRIu64 ".", sleepMseconds);
    }
}
