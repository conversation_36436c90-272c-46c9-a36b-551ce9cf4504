/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: adpt_mini_process_id.c
 * Description: source file for GMDB base adapter
 * Author: minikv
 * Create: 2024-01-19
 */

#include <pthread.h>
#include "db_file.h"
#include "adpt_log.h"
#include "adpt_process_id.h"

uint32_t DbAdptGetpid(void)
{
    return (uint32_t)getpid();
}

int32_t DbAdptFork(void)
{
    return (int32_t)fork();
}

Status DbAdptSetpgid(int32_t pid, int32_t pgid)
{
    int ret = setpgid(pid, pgid);
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "set pgid. ret: %" PRId32 ", os no: %" PRId32 ".", ret, (int32_t)errno);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

bool DbAdptProcessIsExist(uint32_t pid)
{
    char path[DB_MAX_PATH] = {0};
    int32_t err = snprintf_s(path, DB_MAX_PATH, DB_MAX_PATH - 1, "/proc/%u", pid);
    if (err <= 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Process not exist. os ret:%" PRId32, err);
        return false;
    }
    return DbDirExist(path);
}
