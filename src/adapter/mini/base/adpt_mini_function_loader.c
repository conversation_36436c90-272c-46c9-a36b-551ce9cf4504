/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: adpt_mini_function_loader.c
 * Description: source file for function loader
 * Author: minikv
 * Create: 2024-01-19
 */

#include "adpt_function_loader.h"

Status DbAdptLoadFunc(void *handle, const FuncTableItemT table[], uint32_t count)
{
    for (const FuncTableItemT *item = table, *end = table + count; item != end; ++item) {
        void **destination = item->destination;
        *destination = dlsym(handle, item->symbol);
        if (*destination == NULL) {
            DB_LOG_AND_SET_LASERR(
                GMERR_GET_THIRD_PARTY_FUNCTION_FAILED, "worthless to Load Func(%s), %s", item->symbol, dlerror());
            return GMERR_GET_THIRD_PARTY_FUNCTION_FAILED;
        }
    }
    return GMERR_OK;
}

void DbAdptUnLoadLibrary(void *handle)
{
    DB_POINTER(handle);
    (void)dlclose(handle);
}
