/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: adpt_mini_startup_time.c
 * Description: Support print startup time(Only used to analyze startup time)
 * Author: minikv
 * Create: 2024-01-19
 */

#include "adpt_process_name.h"
#include "db_file.h"
#include "adpt_startup_time.h"

// 兼容非mini场景编译
DB_THREAD_LOCAL char g_gmdbCliOpTableName[CLT_TABLE_NAME_LEN] = {0};
bool g_gmdbOpenPrintRuntime = false;

void DbPrintServiceEntryThreadRunTime(const char *stage, const char *func, CliOpInfoT *cliOpInfo, char *tname)
{
    return;
}

void DbOpenPrintRuntime(void)
{
    return;
}

void DbRecordRuntimeLog(char *formatStr, ...)
{
    return;
}
