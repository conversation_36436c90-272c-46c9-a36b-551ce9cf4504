/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: adpt_mini_persist.c
 * Description: implement of mini persist
 * Author:
 * Create: 2023-12-25
 */

#include "adpt_define.h"
#include "adpt_compress.h"
#include "adpt_file.h"

bool DbEnableCompress(void)
{
    return false;
}

inline Status DbDataCompress(uint8_t *dest, uint32_t *destLen, const uint8_t *source, uint32_t sourceLen)
{
    return GMERR_DATA_EXCEPTION;
}

Status DbDataDecompress(uint8_t *dest, uint32_t *destLen, const uint8_t *source, uint32_t sourceLen)
{
    return GMERR_DATA_EXCEPTION;
}

inline Status DbSpaceDataCompress(uint8_t *dest, uint32_t *destLen, const uint8_t *source, uint32_t sourceLen)
{
    return GMERR_DATA_EXCEPTION;
}

Status DbSpaceDataDecompress(uint8_t *dest, uint32_t *destLen, const uint8_t *source, uint32_t sourceLen)
{
    return GMERR_DATA_EXCEPTION;
}

bool DbIsEnablePersistFileNameConvert(void)
{
    return false;
}
Status DbPersistFileNameConvert(const char *src, char *dest, uint32_t destSize)
{
    return GMERR_DATA_EXCEPTION;
}
