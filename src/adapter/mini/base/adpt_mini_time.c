/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: adpt_mini_time.c
 * Description: source file for gmdb time
 * Author: minikv
 * Create: 2024-01-19
 */

#include "adpt_log.h"
#include "adpt_time.h"

uint64_t DbGetNsec(void)
{
    struct timespec requestStart;
    (void)clock_gettime(DB_CLOCK_MONOTONIC, &requestStart);
    return (uint64_t)requestStart.tv_nsec + NSECONDS_IN_SECOND * (uint64_t)requestStart.tv_sec;
}

uint64_t DbGetMsec(void)
{
    return DbGetNsec() / NSECONDS_IN_MSECOND;
}

uint64_t DbGetSec(void)
{
    struct timespec requestStart;
    (void)clock_gettime(DB_CLOCK_MONOTONIC, &requestStart);
    return (uint64_t)requestStart.tv_sec;
}

// 用途：记录平年闰年的月份天数对应关系
// 是否并发初始化：否
// 是否并发读写：否
// 并发方案：无
uint32_t g_gmdbMonDays[DEVIL_TIMELINUX_COUNT_ROW][DEVIL_TIMELINUX_COUNT_COL] = {
    {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31}, {31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31}};

inline static bool IsLeapYear(uint32_t year)
{
    unsigned leapYearCycle = 4;
    unsigned largeCycle = 100;
    return (year % leapYearCycle == 0 && year % largeCycle != 0) || (year % (leapYearCycle * largeCycle) == 0);
}

#define DB_MAX_YEAR 9999
Status DbStrToTime(const char *time, const char *format, struct tm *timeptr)
{
    DB_POINTER3(time, format, timeptr);

    if (time[0] == '\0') {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Inv time format.");
        return GMERR_DATA_EXCEPTION;
    }
    (void)memset_s(timeptr, sizeof(struct tm), 0, sizeof(struct tm));
    CmTimeDescT desc;
    char *retVal = strptime(time, format, timeptr);
    if (!retVal || *retVal != '\0') {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Inv time format.");
        return GMERR_DATA_EXCEPTION;
    }
    desc.year = (uint16_t)((uint16_t)timeptr->tm_year + (uint16_t)DEVIL_TIMELINUX_BASE_YEAR);
    if (desc.year > DB_MAX_YEAR) {
        (void)memset_s(timeptr, sizeof(struct tm), 0, sizeof(struct tm));
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Inv time format.");
        return GMERR_DATA_EXCEPTION;
    }
    desc.mon = (uint8_t)((uint8_t)timeptr->tm_mon + (uint8_t)1);
    desc.day = (uint8_t)timeptr->tm_mday;
    desc.hour = (uint8_t)timeptr->tm_hour;
    desc.min = (uint8_t)timeptr->tm_min;
    desc.sec = (uint8_t)timeptr->tm_sec;
    desc.msec = 0;
    if (desc.day > g_gmdbMonDays[IsLeapYear(desc.year) != 0][desc.mon - 1]) {
        (void)memset_s(timeptr, sizeof(struct tm), 0, sizeof(struct tm));
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Inv time format.");
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

Status DbStrToTimestamp(const char *timeStr, const char *format, int64_t *timestamp)
{
    DB_POINTER3(timeStr, format, timestamp);
    struct tm timePtr;
    Status ret = DbStrToTime(timeStr, format, &timePtr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Inv time str or format.");
        return ret;
    }

    /* set DST to invalid,so that if we want to change value back to str, there will be no mistakes */
    timePtr.tm_isdst = -1;
    *timestamp = (int64_t)mktime(&timePtr);
    return GMERR_OK;
}

Status DbTimeToStr(const CmTimeDescT *desc, char *timeStr, uint16_t maxsize, TimeStrFormatIdE timeFormat)
{
    DB_POINTER2(desc, timeStr);
    DB_UNUSED(timeFormat);  // 保留拓展
    if (maxsize > TIME_STR_MAX_SIZE || maxsize == 0) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Inv maxsize.");
        return GMERR_DATA_EXCEPTION;
    }
    (void)memset_s(timeStr, maxsize, 0, maxsize);
    struct tm timeptr;
    (void)memset_s(&timeptr, sizeof(struct tm), 0, sizeof(struct tm));
    timeptr.tm_year = desc->year - DEVIL_TIMELINUX_BASE_YEAR;
    timeptr.tm_mon = desc->mon - 1;
    timeptr.tm_mday = desc->day;
    timeptr.tm_hour = desc->hour;
    timeptr.tm_min = desc->min;
    timeptr.tm_sec = desc->sec;

    if (strftime(timeStr, maxsize, DB_DEFAULT_TIME_FORMAT, &timeptr) == 0) {
        (void)memset_s(timeStr, maxsize, 0, maxsize);
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Inv strftime.");
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

#define DAYS_1 365
#define DAYS_4 (DAYS_1 * 4 + 1)
#define DAYS_100 (DAYS_4 * 25 - 1)
#define DAYS_400 (DAYS_100 * 4 + 1)
#define NUM_FOUR_HUNDRED 400
#define NUM_FOUR (4u)
#define NUM_HUNDRED (100u)
#define NUM_TWELVE 12
#define NUM_THIRTY_ONE 31
#define MS_SINCE 0.5
void DbDecodeTimeDesc(double dayTime, CmTimeDescT *desc)
{
    DB_POINTER(desc);
    uint32_t days = (uint32_t)dayTime;
    double totalMs = (dayTime - days) * DEVIL_TIMELINUX_DAY_TIME;
    days += DATE_DELTA;
    desc->year = 1;
    while (days >= DAYS_400) {
        desc->year = (uint16_t)(desc->year + (uint16_t)NUM_FOUR_HUNDRED);
        days -= DAYS_400;
    }

    for (uint32_t hundredCount = 1; days >= DAYS_100 && hundredCount < NUM_FOUR; hundredCount++) {
        desc->year = (uint16_t)(desc->year + (uint16_t)NUM_HUNDRED);
        days -= DAYS_100;
    }

    while (days >= DAYS_4) {
        desc->year = (uint16_t)(desc->year + (uint16_t)NUM_FOUR);
        days -= DAYS_4;
    }

    while (days > DAYS_1) {
        if (IsLeapYear(desc->year)) {
            days--;
        }

        desc->year++;
        days -= DAYS_1;
    }

    if (days == 0) {
        desc->year--;
        desc->mon = NUM_TWELVE;
        desc->day = NUM_THIRTY_ONE;
    } else {
        desc->mon = 1;
        uint32_t *dayTab = g_gmdbMonDays[(uint8_t)(IsLeapYear(desc->year))];

        uint32_t i = 0;
        while (days > dayTab[i]) {
            days -= dayTab[i];
            i++;
        }

        desc->mon = (uint8_t)(desc->mon + i);
        desc->day = (uint8_t)(days);
    }
    totalMs += MS_SINCE;

    desc->hour = (uint8_t)(totalMs / DEVIL_TIMELINUX_TOTAL_SEC_TIME);
    totalMs -= desc->hour * DEVIL_TIMELINUX_TOTAL_SEC_TIME;

    desc->min = (uint8_t)(totalMs / DEVIL_TIMELINUX_MSEC_HOUR);
    totalMs -= desc->min * DEVIL_TIMELINUX_MSEC_HOUR;

    desc->sec = (uint8_t)(totalMs / DEVIL_TIMELINUX_SEC_TRANS_MSEC);
    totalMs -= desc->sec * DEVIL_TIMELINUX_SEC_TRANS_MSEC;

    desc->msec = (uint32_t)totalMs;
}

double DbEncodeTimeDesc(CmTimeDescT *desc)
{
    DB_POINTER(desc);

    uint16_t year = (uint16_t)desc->year;
    uint8_t month = (uint8_t)desc->mon;
    uint32_t yDays = (uint8_t)desc->day;
    uint32_t *dayTab = g_gmdbMonDays[(uint8_t)IsLeapYear(year)];

    uint16_t i;
    for (i = 0; i < month - 1; i++) {
        yDays += dayTab[i];
    }
    i = (uint16_t)(year - (uint16_t)1);
    uint32_t date = (uint32_t)(double)i * DAYS_1;
    date += i / NUM_FOUR;
    date -= i / NUM_HUNDRED;
    date += i / NUM_FOUR_HUNDRED;

    date += yDays;
    date -= DATE_DELTA;

    double time = desc->hour * DEVIL_TIMELINUX_TOTAL_SEC_TIME;
    time += desc->min * DEVIL_TIMELINUX_MSEC_HOUR;
    time += desc->sec * DEVIL_TIMELINUX_SEC_TRANS_MSEC;
    time += (double)desc->msec;

    const double numEightSixFour = 86400000.0;
    return (double)date + (double)time / numEightSixFour;
}

void DbConvertTimestampToDesc(int64_t time, CmTimeDescT *desc)
{
    DB_POINTER(desc);
    time_t tt = (time_t)time;
    struct tm pstm;
    tzset();
    (void)DB_LOCAL_TIME_R(&tt, &pstm);

    desc->year = (uint16_t)((uint16_t)pstm.tm_year + (uint16_t)DEVIL_TIMELINUX_BASE_YEAR);
    desc->mon = (uint8_t)(pstm.tm_mon + 1);
    desc->day = (uint8_t)pstm.tm_mday;
    desc->hour = (uint8_t)pstm.tm_hour;
    desc->min = (uint8_t)pstm.tm_min;
    desc->sec = (uint8_t)pstm.tm_sec;
    desc->msec = 0;
    (void)memset_s(desc->reserve, DEVIL_TIMEH_RESERVE_COUNT, 0, DEVIL_TIMEH_RESERVE_COUNT);
    return;
}

double DbEncodeTimestamp(int64_t time)
{
    CmTimeDescT cTime;
    DbConvertTimestampToDesc(time, &cTime);
    return DbEncodeTimeDesc(&cTime);
}

uint64_t DbGettimeMonotonicUsec(void)
{
    struct timespec ts;
    (void)clock_gettime(DB_CLOCK_MONOTONIC, &ts);
    return ((uint64_t)ts.tv_sec * DEVIL_TIMELINUX_MSEC_TIME + (uint64_t)ts.tv_nsec / DEVIL_TIMELINUX_SEC_TRANS_MSEC);
}

uint64_t DbGettimeMonotonicMsec(void)
{
    return DbGettimeMonotonicUsec() / DEVIL_TIMELINUX_SEC_TRANS_MSEC;
}

uint64_t DbGetCurrentTimestamp(void)
{
    struct timeval timestamp;
    (void)DB_GET_TIME_OF_DAY(&timestamp, NULL);
    return (uint64_t)timestamp.tv_sec * DEVIL_TIMELINUX_MSEC_TIME + (uint64_t)timestamp.tv_usec;
}

struct timespec g_gmdbClockRealTime = {0};  // 通过spinlock在赋值以及读取时需要进行加锁保护
DbSpinLockT g_gmdbTimeLock;                 // 操作g_gmdbClockRealTime数据结构时需要加锁保护

inline static bool IsLaterTime(struct timespec timeA, struct timespec timeB)
{
    return timeA.tv_sec > timeB.tv_sec || (timeA.tv_sec == timeB.tv_sec && timeA.tv_nsec > timeB.tv_nsec);
}

void DbSetLargestGlobalTime(struct timespec inputTime)
{
    if (IsLaterTime(inputTime, g_gmdbClockRealTime)) {
        DbSpinLock(&g_gmdbTimeLock);
        if (IsLaterTime(inputTime, g_gmdbClockRealTime)) {
            g_gmdbClockRealTime.tv_sec = inputTime.tv_sec;
            g_gmdbClockRealTime.tv_nsec = inputTime.tv_nsec;
        }
        DbSpinUnlock(&g_gmdbTimeLock);
    }
}
