/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: adpt_eventfd.h
 * Description: header file for GMDB eventfd
 * Author:
 * Create: 2023-6-29
 */

#ifndef DB_ADPT_EVENTFD_H
#define DB_ADPT_EVENTFD_H

#include <sys/eventfd.h>
#include "adpt_define.h"
#include "adpt_types.h"

#ifdef __cplusplus
extern "C" {
#endif

ADPTR_EXPORT Status DbEventFdCreate(uint32_t id, int32_t *efd);
ADPTR_EXPORT Status DbEventFdRead(int32_t fd);
ADPTR_EXPORT Status DbEventFdWrite(int32_t fd);
ADPTR_EXPORT Status DbAdptEventFdRead(int32_t fd, eventfd_t *value);
ADPTR_EXPORT Status DbAdptEventFdWrite(int32_t fd, eventfd_t value);

#ifdef __cplusplus
}
#endif
#endif /* DB_ADPT_EVENTFD_H */
