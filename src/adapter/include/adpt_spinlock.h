/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: adpt_spinlock.h
 * Description: Spinlock interfaces
 * Author: liaoqidan
 * Create: 2020-10-21
 */
#ifndef ADPT_SPINLOCK_H
#define ADPT_SPINLOCK_H

#include "adpt_types.h"
#include "adpt_atomic.h"

#ifdef __cplusplus
extern "C" {
#endif

/* spinlock config parameters */
#define SPIN_ROUNDS 10
#if !(defined HPE || defined EXPERIMENTAL_GUANGQI)
#define SPIN_FACTOR 100
#define SPIN_SHIFT_BIT 1u
#else
#define SPIN_FACTOR 1
#define SPIN_SHIFT_BIT 0u
#endif

#define SPIN_SLEEP_DELAY 500u

#define SPIN_ADJUSTMENT_FACTOR 0.5

#ifndef HPE
#define SPIN_MIN_DELAY_USEC 100u
#else
#define SPIN_MIN_DELAY_USEC 100u
#endif

#ifndef HPE
#define SPIN_MAX_DELAY_USEC 500u
#else
#define SPIN_MAX_DELAY_USEC 1000000u
#endif

#define SPIN_TIMEOUT_FOREVER 0

/* check pid per 6s */
#define PID_RWLOCK_CHECK_INTERVAL 6000000

#define LOCK_FLAG_LOG_NONE 0x00
#define LOCK_FLAG_LOG_TIMEOUT 0x01

/* 通用结构体 */
typedef struct DbSpinLock {
    volatile uint32_t lock;
#ifdef GMDB_LATCH_DEBUG
    char *name;      // name of worker
    uint64_t owner;  // id of worker
    // debug info of lock holder
    char *file;
    int32_t line;
    char *func;
#endif
} DbSpinLockT;

#define DB_SPINLOCK_INIT_VAL \
    {                        \
        0                    \
    }  // spinLock初始化宏定义
ADPTR_EXPORT extern uint32_t g_gmdbLatchDeadlockTimeout;
ADPTR_EXPORT extern uint32_t g_gmdbLatchDeadlockStackTimeout;
ADPTR_EXPORT extern uint32_t g_gmdbPidLockSleepTimes;
ADPTR_EXPORT extern uint32_t g_gmdbPidLockStackSleepTimes;

/**
 * @brief 创建并初始化自旋锁
 * @param spinLock : 入参出参，由user创建一个DbSpinLockT对象作为入参，初始化后作为出参传出spinlock句柄
 * @return 返回void类型
 */
ADPTR_EXPORT void DbSpinInit(DbSpinLockT *latch);

#ifdef GMDB_LATCH_DEBUG
ADPTR_EXPORT void DbSpinLockEx(DbSpinLockT *spinLock, uint8_t flag, const char *file, int32_t line, const char *func)
    __attribute__((section(".text.hot.rwlock")));

ADPTR_EXPORT bool DbSpinTryLockEx(DbSpinLockT *spinLock, const char *file, int32_t line, const char *func)
    __attribute__((section(".text.hot.rwlock")));

ADPTR_EXPORT bool DbSpinTimedLockEx(DbSpinLockT *spinLock, uint32_t timeoutUs, const char *file, int32_t line,
    const char *func) __attribute__((section(".text.hot.rwlock")));

#define DbSpinLock(spinLock) DbSpinLockEx((spinLock), LOCK_FLAG_LOG_TIMEOUT, __FILE__, __LINE__, __func__)
#define DbSpinTryLock(spinLock) DbSpinTryLockEx((spinLock), __FILE__, __LINE__, __func__)
#define DbSpinTimedLock(spinLock, timeoutUs) DbSpinTimedLockEx((spinLock), (timeoutUs), __FILE__, __LINE__, __func__)
#else
ADPTR_EXPORT void DbSpinLockEx(DbSpinLockT *spinLock, uint8_t flag) __attribute__((section(".text.hot.rwlock")));
ADPTR_EXPORT bool DbSpinTryLockEx(DbSpinLockT *spinLock) __attribute__((section(".text.hot.rwlock")));
ADPTR_EXPORT bool DbSpinTimedLockEx(DbSpinLockT *spinLock, uint32_t timeoutUs)
    __attribute__((section(".text.hot.rwlock")));

#define DbSpinLock(spinLock) DbSpinLockEx(spinLock, LOCK_FLAG_LOG_TIMEOUT)
#define DbSpinTryLock(spinLock) DbSpinTryLockEx(spinLock)
#define DbSpinTimedLock(spinLock, timeoutUs) DbSpinTimedLockEx((spinLock), (timeoutUs))
#endif

// 不打印锁超时日志，原有入口默认打印锁超时日志
#define DbSpinLockWithoutLog(spinLock) DbSpinLockEx(spinLock, LOCK_FLAG_LOG_NONE)

/**
 * @brief 释放自旋锁
 * @param spinLock : 入参，spinlock句柄
 * @return 返回void类型
 */
inline static void DbSpinUnlock(DbSpinLockT *latch)
{
    DB_POINTER(latch);
#if defined(__arm__) || defined(__aarch64__)
    __sync_lock_release(&latch->lock);
#else
    // barrier is needed to prevent lock = 0 being reordered into critical section
    COMPILER_BARRIER;
    latch->lock = 0;
#endif
}

/**
 * @brief 获取自旋锁
 * @param spinLock : 入参，owner锁的持有者, spinlock句柄
 * @return 返回void类型
 */
ADPTR_EXPORT void DbSpinLockOwner(uint32_t owner, DbSpinLockT *latch) __attribute__((section(".text.hot.rwlock")));

/**
 * @brief 获取自旋锁
 * @param spinLock : 入参，owner锁的持有者, spinlock句柄
 * @param timeoutUs : 入参，表示设置的超时等待上限，出参，表示成功操作后，等待剩余的时长
 * @return 返回bool类型,表示是否lock成功，
 */
ADPTR_EXPORT bool DbSpinTimedLockOwner(uint32_t owner, DbSpinLockT *spinLock, uint32_t *timeoutUs)
    __attribute__((section(".text.hot.rwlock")));

/**
 * @brief 尝试获取自旋锁
 * @param spinLock : 入参，owner锁的持有者, spinlock句柄
 * @return 返回void类型
 */
inline static bool DbSpinTryLockOwner(uint32_t owner, DbSpinLockT *latch)
{
    DB_POINTER(latch);
    return DbAtomicExchange32WithAcquireBarrier(&latch->lock, 0u, owner);
}

typedef struct DbRWSpinLock {
    volatile int32_t lock;
#ifdef GMDB_LATCH_DEBUG
    char *name;
    uint64_t owner;
    // debug info of lock holder
    char *file;
    int32_t line;
    char *func;
#endif
#ifdef LATCH_CONFLICT_DEBUG
    uint32_t readerWaitTimes;
    uint32_t writerWaitTimes;
#endif
} DbRWSpinLockT;

typedef struct DbRWSpinStat {
    uint32_t readerCount;    // # of readers in the critical section
    uint32_t maxReadSpins;   // max spin rounds of acquiring rlock
    uint32_t maxWriteSpins;  // max spin rounds of acquiring wlock
} DbRWSpinStatT;

/**
 * @brief 创建并初始化读写自旋锁
 * @param rwSpinLock : 入参出参，由user创建一个DbRWSpinLockT对象作为入参，初始化后作为出参传出rwspinlock句柄。
 * @return 返回void类型
 */
ADPTR_EXPORT void DbRWSpinInit(DbRWSpinLockT *rwlock);

#ifdef GMDB_LATCH_DEBUG
ADPTR_EXPORT void DbRWSpinRLockEx(DbRWSpinLockT *rwlock, DbRWSpinStatT *latchStat, uint8_t flag, const char *file,
    int32_t line, const char *func) __attribute__((section(".text.hot.rwlock")));

ADPTR_EXPORT bool DbRWSpinTryRLockEx(DbRWSpinLockT *rwlock, const char *file, int32_t line, const char *func)
    __attribute__((section(".text.hot.rwlock")));

ADPTR_EXPORT bool DbRWSpinTimedRLockEx(DbRWSpinLockT *rwlock, DbRWSpinStatT *latchStat, uint32_t timeoutUs,
    uint8_t flag, const char *file, int32_t line, const char *func) __attribute__((section(".text.hot.rwlock")));

#define DbRWSpinRLock(rwSpinLock) \
    DbRWSpinRLockEx((rwSpinLock), NULL, LOCK_FLAG_LOG_TIMEOUT, __FILE__, __LINE__, __func__)
#define DbRWSpinTryRLock(rwSpinLock) DbRWSpinTryRLockEx((rwSpinLock), __FILE__, __LINE__, __func__)
#define DbRWSpinTimedRLock(rwSpinLock, timeoutUs) \
    DbRWSpinTimedRLockEx((rwSpinLock), NULL, (timeoutUs), LOCK_FLAG_LOG_TIMEOUT, __FILE__, __LINE__, __func__)

ADPTR_EXPORT void DbRWSpinWLockEx(DbRWSpinLockT *rwlock, DbRWSpinStatT *latchStat, uint8_t flag, const char *file,
    int32_t line, const char *func) __attribute__((section(".text.hot.rwlock")));

ADPTR_EXPORT void DbReetrantRWSpinWLockEx(DbRWSpinLockT *rwSpinLock, DbRWSpinStatT *rwSpinLockStat, const char *file,
    int32_t line, const char *func) __attribute__((section(".text.hot.rwlock")));

ADPTR_EXPORT bool DbRWSpinTryWLockEx(DbRWSpinLockT *rwlock, const char *file, int32_t line, const char *func)
    __attribute__((section(".text.hot.rwlock")));

ADPTR_EXPORT bool DbRWSpinTimedWLockEx(DbRWSpinLockT *rwlock, DbRWSpinStatT *latchStat, uint32_t timeoutUs,
    uint8_t flag, const char *file, int32_t line, const char *func) __attribute__((section(".text.hot.rwlock")));

#define DbRWSpinWLock(rwSpinLock) \
    DbRWSpinWLockEx((rwSpinLock), NULL, LOCK_FLAG_LOG_TIMEOUT, __FILE__, __LINE__, __func__)
#define DbReetrantRWSpinWLock(rwSpinLock) DbReetrantRWSpinWLockEx((rwSpinLock), NULL, __FILE__, __LINE__, __func__)
#define DbRWSpinTryWLock(rwSpinLock) DbRWSpinTryWLockEx((rwSpinLock), __FILE__, __LINE__, __func__)
#define DbRWSpinTimedWLock(rwSpinLock, timeoutUs) \
    DbRWSpinTimedWLockEx((rwSpinLock), NULL, (timeoutUs), LOCK_FLAG_LOG_TIMEOUT, __FILE__, __LINE__, __func__)
#else
ADPTR_EXPORT void DbRWSpinRLockEx(DbRWSpinLockT *rwlock, DbRWSpinStatT *latchStat, uint8_t flag)
    __attribute__((section(".text.hot.rwlock")));
ADPTR_EXPORT bool DbRWSpinTryRLockEx(DbRWSpinLockT *rwlock) __attribute__((section(".text.hot.rwlock")));
ADPTR_EXPORT bool DbRWSpinTimedRLockEx(DbRWSpinLockT *rwlock, DbRWSpinStatT *latchStat, uint32_t timeoutUs,
    uint8_t flag) __attribute__((section(".text.hot.rwlock")));

#define DbRWSpinRLock(rwSpinLock) DbRWSpinRLockEx(rwSpinLock, NULL, LOCK_FLAG_LOG_TIMEOUT)
#define DbRWSpinTryRLock(rwSpinLock) DbRWSpinTryRLockEx(rwSpinLock)
#define DbRWSpinTimedRLock(rwSpinLock, timeoutUs) \
    DbRWSpinTimedRLockEx((rwSpinLock), NULL, (timeoutUs), LOCK_FLAG_LOG_TIMEOUT)

ADPTR_EXPORT void DbRWSpinWLockEx(DbRWSpinLockT *rwlock, DbRWSpinStatT *latchStat, uint8_t flag)
    __attribute__((section(".text.hot.rwlock")));

ADPTR_EXPORT void DbReetrantRWSpinWLockEx(DbRWSpinLockT *rwSpinLock, DbRWSpinStatT *rwSpinLockStat)
    __attribute__((section(".text.hot.rwlock")));

ADPTR_EXPORT bool DbRWSpinTryWLockEx(DbRWSpinLockT *rwlock) __attribute__((section(".text.hot.rwlock")));

ADPTR_EXPORT bool DbRWSpinTimedWLockEx(DbRWSpinLockT *rwlock, DbRWSpinStatT *latchStat, uint32_t timeoutUs,
    uint8_t flag) __attribute__((section(".text.hot.rwlock")));

#define DbRWSpinWLock(rwSpinLock) DbRWSpinWLockEx((rwSpinLock), NULL, LOCK_FLAG_LOG_TIMEOUT)
#define DbReetrantRWSpinWLock(rwSpinLock) DbReetrantRWSpinWLockEx((rwSpinLock), NULL)
#define DbRWSpinTryWLock(rwSpinLock) DbRWSpinTryWLockEx(rwSpinLock)
#define DbRWSpinTimedWLock(rwSpinLock, timeoutUs) \
    DbRWSpinTimedWLockEx((rwSpinLock), NULL, (timeoutUs), LOCK_FLAG_LOG_TIMEOUT)
#endif

// 不打印锁超时日志，原有入口默认打印锁超时日志
#define DbRWSpinRLockWithoutLog(rwSpinLock) DbRWSpinRLockEx((rwSpinLock), NULL, LOCK_FLAG_LOG_NONE)
#define DbRWSpinWLockWithoutLog(rwSpinLock) DbRWSpinWLockEx((rwSpinLock), NULL, LOCK_FLAG_LOG_NONE)

ADPTR_EXPORT void DbRWSpinRUnlockEx(DbRWSpinLockT *rwlock, DbRWSpinStatT *latchStat)
    __attribute__((section(".text.hot.rwlock")));
#define DbRWSpinRUnlock(rwSpinLock) DbRWSpinRUnlockEx((rwSpinLock), NULL)

ADPTR_EXPORT void DbRWSpinWUnlockEx(DbRWSpinLockT *rwlock, DbRWSpinStatT *latchStat)
    __attribute__((section(".text.hot.rwlock")));
#define DbRWSpinWUnlock(rwSpinLock) DbRWSpinWUnlockEx((rwSpinLock), NULL)

// AC设备上CPU时间片为10ms，当前DB支持最大连接个数为1536个，故超时时间定为15s
#define PID_SHM_LOCK_TIMEOUT_MS 15000
#define WAIT_FOREVER (-1)

ADPTR_EXPORT Status DbPidSpinLock(DbSpinLockT *lock, int32_t timeoutMs);
ADPTR_EXPORT void DbPidSpinUnlock(DbSpinLockT *lock);

#ifdef __cplusplus
}
#endif
#endif  // ADPT_SPINLOCK_H
