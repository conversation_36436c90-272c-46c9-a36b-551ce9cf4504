/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: adpt_init.h
 * Description: header file of adapter
 * Author:
 * Create: 2020-10-21
 */
#ifndef ADPT_INIT_H
#define ADPT_INIT_H

#include "gmc_errno.h"
#include "adpt_types.h"
#include "adpt_patch_c2h_type.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct DbPatchCallBack {
    int32_t (*hpePatchInit)(const char *name);
    void (*patchRestore)(void);
} DbPatchCallBackT;

typedef struct DbToolPatchCtx {
    HandleT patchDopraHandle;
    HandleT patchModuleHandle;
    uint32_t (*patchDopraPatchInit)(void);
    uint32_t (*patchModuleInit)(SISP_PAT_MODULE_INIT_PARA_S *modulePara);
} DbToolPatchCtxT;

typedef void (*PatchInitLog)(int32_t priority, const char *format, ...);

ADPTR_EXPORT extern DbPatchCallBackT g_gmdbPatchInit;

ADPTR_EXPORT Status DbAdapterInit(void);
ADPTR_EXPORT void DbAdapterClose(void);
ADPTR_EXPORT Status DbAdptExecSetCfgFuncs(void *adapter);
ADPTR_EXPORT Status DbAdptExeHpeBaseInitFuncs(void *adapter);
ADPTR_EXPORT void DbAdapterLogClose(void);
ADPTR_EXPORT void DbPatchRestore(void);
ADPTR_EXPORT Status DbPatchInit(void);
ADPTR_EXPORT void DbPatchUnInit(void);
ADPTR_EXPORT Status DbToolPatchInit(const char *moduleName, PatchInitLog patchInitLog, int32_t priority);
ADPTR_EXPORT void DbToolPatchUnInit(const char *moduleName, PatchInitLog patchInitLog, int32_t priority);

#ifdef __cplusplus
}
#endif
#endif  // ADPT_INIT_H
