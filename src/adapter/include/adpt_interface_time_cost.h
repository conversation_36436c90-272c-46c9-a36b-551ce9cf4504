/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: adpt_interface_time_cost.h
 * Description:用于性能优化打点
 * Author:gaohaiyang
 * Create: 2024-08-29
 */
#ifndef ADPT_INTERFACE_TIME_COST_H
#define ADPT_INTERFACE_TIME_COST_H

#include "adpt_define.h"

#ifdef __cplusplus
extern "C" {
#endif

#if defined(PERF_SAMPLE_STAT)

/*
适用场景：某个opcode执行流程中，对中间流程打点，可查看各个流程的耗时；不建议在多层函数嵌套调用或者循环中使用。
使用方法：
1、确定想分析的opcode，在 g_gmdbPerfTimePoint 中添加对应opcode的信息
2、确定打点起止点，起点调用 DbPerfBegin 、终点调用 DbPerfEnd，这两个函数仅标记代码起止点，不参与耗时计算
3、在关心的流程前后埋点，根据需要可以使用 DbPerfAppendTpAutomatically 、 DbPerfAppendTpManually 、
DbPerfAppendTpBreakPoint
4、支持的最大打点个数是64个，如需修改，修改 tpArray 数组大小
*/

/* 例子
DbPerfBegin
DbPerfAppendTpAutomatically
    funcA
DbPerfAppendTpAutomatically
    funcB
DbPerfAppendTpAutomatically
    funcC
DbPerfAppendTpAutomatically
DbPerfEnd
将统计三个函数的执行耗时， funcA 、 funcB 、 funcC
*/

// 耗时统计开始
ADPTR_EXPORT void DbPerfBegin(int32_t opCode);
// 自动添加计时TimePoint，需要指定是否使用globalRdtsc。在统计跨进程通信时候，通常 useGlobalRdtsc 需要为true
ADPTR_EXPORT void DbPerfAppendTpAutomatically(int32_t opCode, bool useGlobalRdtsc);
// 手动添加计时TimePoint，需要指定cpu cycle值
ADPTR_EXPORT void DbPerfAppendTpManually(int32_t opCode, uint64_t timePoint);
// 添加断点BreakPoint，表示断点不参与耗时统计，如[tp1,bp,tp2]，tp1和bp，bp和tp2的耗时不会统计，tp1和tp2之间也不统计，因为中间有bp了
ADPTR_EXPORT void DbPerfAppendTpBreakPoint(int32_t opCode);
// 耗时统计结束，会打印 [DbPerfBegin, DbPerfEnd] 之间，相邻tp之间的耗时。如果中间有bp，如 DbPerfAppendTpBreakPoint 说明
ADPTR_EXPORT void DbPerfEnd(int32_t opCode);

#endif  // PERF_SAMPLE_STAT

#ifdef __cplusplus
}
#endif
#endif  // ADPT_INTERFACE_TIME_COST_H
