/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: adpt_file.h
 * Description: Adapt file operation
 * Author:
 * Create: 2022-03-14
 */

#ifndef ADPT_FILE_H
#define ADPT_FILE_H
#include <fcntl.h>
#include <unistd.h>
#include <sys/stat.h>
#include <gmc_types.h>
#include "adpt_define.h"
#include "adpt_types.h"

#ifdef __cplusplus
extern "C" {
#endif

ADPTR_EXPORT Status DbAdptAccess(const char *pathname, int mode);
ADPTR_EXPORT Status DbAdptClose(int32_t fd);
ADPTR_EXPORT Status DbAdptOpen(const char *fullPath, int32_t flag, uint32_t permission, int32_t *fd);
Status DbAdptPipe(int pipefd[2]);
ADPTR_EXPORT Status DbAdptRead(int32_t fd, void *buf, size_t count, size_t *readCount);
ADPTR_EXPORT Status DbAdptReadNoLog(int32_t fd, void *buf, size_t count, size_t *readCount);
ADPTR_EXPORT Status DbAdptRealPath(const char *path, char *realPath);
ADPTR_EXPORT Status DbAdptRealPathNoLog(const char *path, char *realPath);
ADPTR_EXPORT Status DbAdptRemove(const char *fileName);
ADPTR_EXPORT Status DbAdptRemoveNoLog(const char *fileName);
ADPTR_EXPORT Status DbAdptRename(const char *oldname, const char *newname);
ADPTR_EXPORT Status DbAdptStat(const char *file, struct stat *buf);
ADPTR_EXPORT Status DbAdptFStat(int fd, struct stat *buf);
ADPTR_EXPORT Status DbAdptWrite(int32_t fd, const void *buf, size_t count, size_t *writeCount);
ADPTR_EXPORT Status DbAdptOpenDir(const char *dirPath, void **dir);
ADPTR_EXPORT Status DbAdptReadDir(void *dir, char subDir[], uint32_t subDirSize);
ADPTR_EXPORT void DbAdptCloseDir(void *dir);
ADPTR_EXPORT void DbAdptRemoveUmask(void);

ADPTR_EXPORT Status DbAdptPread(int32_t fd, char *buf, uint32_t count, int64_t offset, uint32_t *readCount);
ADPTR_EXPORT Status DbAdptPwrite(int32_t fd, const char *buf, uint32_t count, int64_t offset, uint32_t *writeCount);
ADPTR_EXPORT Status DbAdptFsync(int32_t fd);
ADPTR_EXPORT Status DbAdptSeekFileHead(int32_t fd, int64_t offset);
ADPTR_EXPORT Status DbAdptSeekFileEnd(int32_t fd, int64_t offset);
ADPTR_EXPORT Status DbAdptFtruncate(int32_t fd, int64_t len);
ADPTR_EXPORT Status DbAdptFallocate(int32_t fd, int32_t mode, uint32_t count, int64_t offset);

typedef int32_t (*PersistFileNameConvertFuncT)(const char *src, char *dest, uint32_t destSize);
ADPTR_EXPORT Status DbAdptRegFileNameConvertFuncs(PersistFileNameConvertFuncT fileNameConvertFunc);
ADPTR_EXPORT Status DbPersistFileNameConvert(const char *src, char *dest, uint32_t destSize);

ADPTR_EXPORT bool DbIsEnablePersistFileNameConvert(void);
/*
    该日志接口仅用于adapter file下相关函数记录错误日志使用，非相关函数请勿调用。
    文件相关接口与日志打印函数有循环依赖，euler下记录syslog，非euler与其他日志一致。
*/

#define LOG_ERR 3
#define LOG_WARNING 4

ADPTR_EXPORT Status DbAdptReadForTimeEvent(int32_t fd, void *buf, size_t count, size_t *readCount);
ADPTR_EXPORT void DbAdptReadForCb(int32_t fd, void *buf, size_t count, size_t *readCount);

#ifdef __cplusplus
}
#endif
#endif  // ADPT_FILE_H
