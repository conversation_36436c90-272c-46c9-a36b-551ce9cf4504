/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: adpt_thread_sem.h
 * Description: share semaphore interfaces
 * Author:
 * Create: 2025-4-7
 */
#ifndef ADPT_THREAD_SEM_H
#define ADPT_THREAD_SEM_H

#include <semaphore.h>
#include "adpt_mono_sem.h"
#include "adpt_mock_sem.h"
#include "adpt_types.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef union DbThreadSem {
#ifdef SEM_CLOCK_WAIT
    DbMonoSemT monoSem;
#else
    DbSemMockT mockSem;
#endif
} DbThreadSemT;

static inline Status DbThreadSemInit(DbThreadSemT *sem, uint32_t value)
{
    DB_POINTER(sem);
#ifdef SEM_CLOCK_WAIT
    return DbMonoSemInit(&sem->monoSem, 0, value);
#else
    return DbMockSemInit(&sem->mockSem, value);
#endif
}

static inline Status DbThreadSemWait(DbThreadSemT *sem)
{
    DB_POINTER(sem);
#ifdef SEM_CLOCK_WAIT
    return DbMonoSemWait(&sem->monoSem);
#else
    return DbMockSemWait(&sem->mockSem);
#endif
}

static inline Status DbThreadSemTryWait(DbThreadSemT *sem)
{
    DB_POINTER(sem);
#ifdef SEM_CLOCK_WAIT
    return DbMonoSemTryWait(&sem->monoSem);
#else
    return DbMockSemTryWait(&sem->mockSem);
#endif
}

static inline Status DbThreadSemTimedWait(DbThreadSemT *sem, uint32_t timeoutUs)
{
    DB_POINTER(sem);
#ifdef SEM_CLOCK_WAIT
    return DbMonoSemTimedWait(&sem->monoSem, timeoutUs);
#else
    return DbMockSemTimedwait(&sem->mockSem, timeoutUs);
#endif
}

static inline Status DbThreadSemPost(DbThreadSemT *sem)
{
    DB_POINTER(sem);
#ifdef SEM_CLOCK_WAIT
    return DbMonoSemPost(&sem->monoSem);
#else
    return DbMockSemPost(&sem->mockSem);
#endif
}

static inline Status DbThreadSemDestroy(DbThreadSemT *sem)
{
    DB_POINTER(sem);
#ifdef SEM_CLOCK_WAIT
    return DbMonoSemDestroy(&sem->monoSem);
#else
    return DbMockSemDestroy(&sem->mockSem);
#endif
}

#ifdef __cplusplus
}
#endif
#endif  // ADPT_THREAD_SEM_H
