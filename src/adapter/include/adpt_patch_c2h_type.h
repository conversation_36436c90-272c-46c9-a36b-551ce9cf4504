/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: adpt_patch_c2h_type.h
 * Description: db patch cold to hot header
 * Author:
 * Create: 2022-11-10
 */
#ifndef ADPT_PATCH_C2H_TYPE_H
#define ADPT_PATCH_C2H_TYPE_H

#include "stdint.h"
#include "stdio.h"
#include "string.h"
#include "adpt_define.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/**************** 这一部分给hpe冷转热钩子函数使用 ******************/
// "hpe_patch.h"
#define HPE_PAT_PATCHNAME_LEN 64
#define HPE_PAT_PATCHALIASNAME_LEN 64
#define HPE_PAT_FILEPATH_LEN 128
#define HPE_PAT_FILENAME_LEN 128

#define VOS_NOP()                         \
    {                                     \
        __asm__ __volatile__("nop \r\n"   \
                             "nop \r\n"   \
                             "nop \r\n"   \
                             "nop \r\n"); \
    }

typedef struct tagHpePatchInfo {
    uint32_t patchType;
    char acPatchName[HPE_PAT_PATCHNAME_LEN];           /* 补丁名称:用于加载 */
    char acPatchFilePath[HPE_PAT_FILEPATH_LEN];        /* 补丁单元文件路径 */
    char acPatchAliasName[HPE_PAT_PATCHALIASNAME_LEN]; /* 补丁名称别名: 用于dopra识别patchNo */
    char acFileName[HPE_PAT_FILENAME_LEN];             /* 文件名 */
} HpePatchInfo;
typedef int32_t (*HpePatchC2hHook)(bool isActive, const HpePatchInfo *patchInfo, bool *pIsProcess);

/**************** 这一部分给rtosv2x冷转热钩子函数使用 ******************/
// "sisp_patch_sdk.h"
#define SISP_PAT_PROC_NAME_LEN 32
#define SISP_PAT_FUNC_NAME_LEN 64
#define SISP_PAT_VPATCHINST_PATCHNAME_LEN 64
#define SISP_PAT_VPATCHINST_CPUTYPE_LEN 64
#define SISP_PAT_VPATCHINST_OWNERNAME_LEN 64
#define SISP_PAT_VPATCHINST_CPTNAME_LEN 128
#define SISP_PAT_VPATCHINST_FILENAME_LEN 128
#define SISP_PAT_VPATCHINST_FILEPATH_LEN 128
#define SISP_PAT_VPATCHINST_PATPKGTYPE_LEN 16
#define SISP_PAT_VPATCHINST_PATRUNTIME_LEN 32
#define SISP_PAT_VPATCHINST_EFFECTPOLICY_LEN 32
#define SISP_PAT_VPATCHINST_EFFECTSCOPE_LEN 64
#define SISP_PAT_VPATCHINST_PATCHALIASNAME_LEN 64
#define SISP_PAT_VPATCHINST_APPBASEVERSION_LEN 32
#define SISP_PAT_MAX_PATPKGNAME_LEN 128
#define SISP_PAT_PKG_VERSION_LEN 64
#define SISP_PAT_AGT_INST_NAME_LEN 64
#define SISP_PAT_SUBPKGVER_LEN 64
#define SISP_PAT_SUBPKGNAME_LEN 64
#define SISP_MPATCH_OPERATED_DOING 0x1111
#define SISP_PAT_UNIT_SHAVALUE_MAX_LEN (64 + 1)

#define PATCH_DOPRA_LIB_NAME "libpatch_dopra.so"
#define PATCH_DOPRA_PATCH_INIT_FUNC "SISP_PAT_DopraPatchInit"
#define PATCH_APPINTF_LIB_NAME "libpatch_app_interaction.so"
#define PATCH_MODULE_INIT_FUNC "SISP_PAT_ModuleInit"
#define PATCH_RESTORE_FOR_DLL_FUNC "SISP_PAT_RestoreForDLL"

#define PATCH_HPE_LIB_NAME "libehlibc.so"
#define PATCH_HPE_INIT_FUNC "HpePatchInit"
#define PATCH_HPE_RESTORE_FUNC "HpePatchRestore"

typedef enum enSispPatC2HOperCode {
    SISP_PAT_C2H_OPER_ACTIVE,   /* 冷转热激活操作 */
    SISP_PAT_C2H_OPER_DEACTIVE, /* 冷转热去激活操作 */
    SISP_PAT_C2H_OPER_MAX
} SISP_PAT_C2H_OPER_CODE_E;

typedef struct tagSispPatC2HOperCb {
    uint32_t uiOperSessionId; /* 本次操作session id */
    uint64_t ullJobSessionId; /* 本次任务session id */
} SISP_PAT_C2H_OPER_CB_S;

typedef struct SispPatSysTime {
    uint16_t usYear;    /* < 年，取值范围为 >=1970, <2038 */
    uint8_t ucMonth;    /* < 月，取值范围为[1,12] */
    uint8_t ucDate;     /* < 日，取值范围为[1, MAXDATE], 最大日期取决于年/月 */
    uint8_t ucHour;     /* < 小时, 取值范围为[0, 23] */
    uint8_t ucMinute;   /* < 分，取值范围为[0, 59] */
    uint8_t ucSecond;   /* < 秒，取值范围为[0,59] */
    uint8_t ucWeek;     /* < 星期，取值范围为[0, 6], 0代表星期天 */
    uint32_t uiMillSec; /* < 毫秒，取值范围为[0,999] */
} SISP_PAT_SYS_SYSTIME;

typedef enum tagSispPatEffectType {
    SISP_PAT_EFFECT_TYPE_HOT = 1,
    SISP_PAT_EFFECT_TYPE_COLD = 2,
    SISP_PAT_EFFECT_TYPE_INVALID
} SISP_PAT_EFFECT_TYPE;

typedef struct tagSISP_PAT_CFG_INFO_S {
    char acPatchName[SISP_PAT_VPATCHINST_PATCHNAME_LEN];   /* 补丁名称 */
    uint32_t uiState;                                      /* 补丁的状态 */
    char acCpuType[SISP_PAT_VPATCHINST_CPUTYPE_LEN];       /* cpu类型 */
    char cPatPkgType;                                      /* 0：正式补丁 1：HHW补丁 */
    uint32_t uiPatchType;                                  /* 补丁类型 */
    char acOwnerName[SISP_PAT_VPATCHINST_OWNERNAME_LEN];   /* 构件名称 */
    char acCptName[SISP_PAT_VPATCHINST_CPTNAME_LEN];       /* 补丁对象名称 */
    char acSha256Value[SISP_PAT_UNIT_SHAVALUE_MAX_LEN];    /* 补丁单元对象哈希值 */
    char acFileName[SISP_PAT_VPATCHINST_FILENAME_LEN];     /* 文件名 */
    char acFilePath[SISP_PAT_VPATCHINST_FILEPATH_LEN];     /* 文件路径 */
    char acPatPkgType[SISP_PAT_VPATCHINST_PATPKGTYPE_LEN]; /* 0：正式补丁 1：HHW补丁 0Xxxx标识兼容包 */
    SISP_PAT_SYS_SYSTIME stPatRunTime;                     /* 补丁生效时间 */
    SISP_PAT_EFFECT_TYPE enEffectType;                     /* hot,cold */
    uint32_t uiPriority;
    char acEffectPolicy[SISP_PAT_VPATCHINST_EFFECTPOLICY_LEN];
    char acEffectScope[SISP_PAT_VPATCHINST_EFFECTSCOPE_LEN];
    char acPatchAliasName[SISP_PAT_VPATCHINST_PATCHALIASNAME_LEN]; /* 补丁名称别名 */
    char acEffectVersion[SISP_PAT_VPATCHINST_APPBASEVERSION_LEN];  /* 补丁生效的基础软件包版本 */
    char acPatchFilePath[SISP_PAT_VPATCHINST_FILEPATH_LEN];        /* 补丁单元文件路径 */
    char acPatPkgName[SISP_PAT_MAX_PATPKGNAME_LEN];
    char acPatchOriginalName[SISP_PAT_VPATCHINST_FILENAME_LEN]; /* R类补丁的源文件名称 xxx.ko */
} SISP_PAT_CFG_INFO_S;

typedef struct tagSispPatchModuleInitPara {
    char acProcName[SISP_PAT_PROC_NAME_LEN];
} SISP_PAT_MODULE_INIT_PARA_S;

#ifdef __cplusplus
}
#endif  /* __cplusplus */
#endif  // ADPT_PATCH_C2H_TYPE_H
