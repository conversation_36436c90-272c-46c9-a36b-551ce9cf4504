/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: adpt_mock_sem.h
 * Description: monopoly semaphore interfaces
 * Author:
 * Create: 2025-4-7
 */
#ifndef ADPT_MOCK_SEM_H
#define ADPT_MOCK_SEM_H

#include <pthread.h>
#include <time.h>
#include <stdint.h>
#include "adpt_types.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifndef SEM_CLOCK_WAIT
typedef struct {
    pthread_mutex_t mutex;
    pthread_cond_t cond;
    int32_t count;    // 当前信号量值
    int32_t waiters;  // 当前等待的线程数
} DbSemMockT;

ADPTR_EXPORT Status DbMockSemInit(DbSemMockT *sem, uint32_t value);

ADPTR_EXPORT Status DbMockSemDestroy(DbSemMockT *sem);

ADPTR_EXPORT Status DbMockSemWait(DbSemMockT *sem);

ADPTR_EXPORT Status DbMockSemTryWait(DbSemMockT *sem);

ADPTR_EXPORT Status DbMockSemTimedwait(DbSemMockT *sem, uint32_t timeoutUs);

ADPTR_EXPORT Status DbMockSemPost(DbSemMockT *sem);
#endif

#ifdef __cplusplus
}
#endif

#endif  // ADPT_MOCK_SEM_H
