/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: header file of thread api
 * Author: wangllixiang
 * Create: 2020/8/12
 */

#ifndef ADPT_THREAD_H
#define ADPT_THREAD_H

#include <pthread.h>
#include <signal.h>
#include <sys/types.h>
#include <unistd.h>
#include "adpt_types.h"

#ifdef __cplusplus
extern "C" {
#endif

#define DB_DEFAULT_THREAD_STACK_SIZE (128 * DB_KIBI)

#define DB_THREAD_NAME_MAX_LEN 32
typedef enum { DB_THREAD_DETACHABLE, DB_THREAD_JOINABLE } DbThreadTypeE;

typedef enum DbThreadPriority {
    THREAD_PRIORITY_DEFAULT = 0,
    THREAD_PRIORITY_LOW,
    THREAD_PRIORITY_MIDDLE,
    THREAD_PRIORITY_HIGH
} DbThreadPriorityE;

typedef pthread_t DbThreadHandle;
typedef pid_t DbGlobalThreadHandle;
typedef void *(*DbThreadEntryProc)(void *param);
typedef void *(*DbThreadExitProc)(void *param);

typedef struct {
    char name[DB_THREAD_NAME_MAX_LEN];
    DbThreadPriorityE priority; /* 任务优先级 */
    DbThreadTypeE type;         /* HPE环境不用 */
    DbThreadEntryProc entryFunc;
    DbThreadExitProc exitFunc;
    void *entryArgs; /* 任务执行函数的参数 */
    void *exitArgs;  /* 任务退出函数的参数 */
    uint32_t stackSize;
    uint32_t cpu;         /* 逻辑CPU */
    uint32_t bindCpuFlag; /* 绑核属性标志:1标识绑核，0标识不指定核 */
    void *userAttr;       /* 用户可扩展属性 */
} ThreadAttrsT;

typedef struct DbMonitorThr {
    uint32_t reminderMs;
    uint32_t generalMs;
    uint32_t seriousMs;
    uint32_t fatalMs;
} DbMonitorThresholdT;

/* base functions */
typedef int32_t (*DbThreadCreateFuncT)(const ThreadAttrsT *threadAttrs, DbThreadHandle *handle);
typedef int32_t (*DbThreadJoinFuncT)(DbThreadHandle handle, void **result);
typedef int32_t (*DbThreadKillFuncT)(DbThreadHandle handle, int32_t signal);
typedef int32_t (*DbThreadSetNameFuncT)(DbThreadHandle handle, const char *threadName);
typedef int32_t (*DbThreadGetNameFuncT)(DbThreadHandle handle, char *threadName, size_t len);
typedef DbThreadHandle (*DbThreadGetTidFuncT)(void);
typedef uint64_t (*DbsThreadGetSelfIdFuncT)(void);
typedef void (*DbThreadSleepFuncT)(uint32_t timeUs);
typedef void (*DbThreadScheYieldFuncT)(void);

/* condition functions */
typedef int32_t (*DbThreadCondInitFuncT)(pthread_cond_t *cond, const pthread_condattr_t *attr);
typedef int32_t (*DbThreadCondDestroyFuncT)(pthread_cond_t *cond);
typedef int32_t (*DbThreadCondSignalFuncT)(pthread_cond_t *cond);
typedef int32_t (*DbThreadCondBroadcastFuncT)(pthread_cond_t *cond);
typedef int32_t (*DbThreadCondWaitFuncT)(pthread_cond_t *cond, pthread_mutex_t *externalMutex);
typedef int32_t (*DbThreadCondTimedwaitFuncT)(
    pthread_cond_t *cond, pthread_mutex_t *externalMutex, const struct timespec *timeSpec);
typedef int32_t (*DbThreadCondAttrInitFuncT)(pthread_condattr_t *attr);
typedef int32_t (*DbThreadCondAttrDestroyFuncT)(pthread_condattr_t *attr);
typedef int32_t (*DbThreadCondAttrSetClockFuncT)(pthread_condattr_t *attr, clockid_t clockId);

/* lock functions */
typedef int32_t (*DbThreadMutexInitFuncT)(pthread_mutex_t *mutex, const pthread_mutexattr_t *attr);
typedef int32_t (*DbThreadMutexLockFuncT)(pthread_mutex_t *mutex);
typedef int32_t (*DbThreadMutexUnlockFuncT)(pthread_mutex_t *mutex);
typedef int32_t (*DbThreadMutexDestroyFuncT)(pthread_mutex_t *mutex);
typedef int32_t (*DbThreadRwlockInitFuncT)(pthread_rwlock_t *rwlock, const pthread_rwlockattr_t *attr);
typedef int32_t (*DbThreadRwlockWrlockFuncT)(pthread_rwlock_t *lock);
typedef int32_t (*DbThreadRwlockRdlockFuncT)(pthread_rwlock_t *lock);
typedef int32_t (*DbThreadRwlockUnlockFuncT)(pthread_rwlock_t *lock);

typedef struct DbThreadFuncs {
    // base functions
    DbThreadCreateFuncT threadCreate;
    DbThreadJoinFuncT threadJoin;
    DbThreadKillFuncT threadKill;
    DbThreadSetNameFuncT threadSetName;
    DbThreadGetNameFuncT threadGetName;
    DbThreadGetTidFuncT threadGetTid;
    DbsThreadGetSelfIdFuncT threadGetSelfId;
    DbThreadSleepFuncT threadSleep;
    DbThreadScheYieldFuncT threadScheYield;

    // condition functions
    DbThreadCondInitFuncT threadCondInit;
    DbThreadCondDestroyFuncT threadCondDestroy;
    DbThreadCondSignalFuncT threadCondSignal;
    DbThreadCondBroadcastFuncT threadCondBroadcast;
    DbThreadCondWaitFuncT threadCondWait;
    DbThreadCondTimedwaitFuncT threadCondTimedwait;
    DbThreadCondAttrInitFuncT threadCondAttrInit;
    DbThreadCondAttrDestroyFuncT threadCondAttrDestroy;
    DbThreadCondAttrSetClockFuncT threadCondAttrSetClock;

    // lock functions
    DbThreadMutexInitFuncT threadMutexInit;
    DbThreadMutexLockFuncT threadMutexLock;
    DbThreadMutexUnlockFuncT threadMutexUnlock;
    DbThreadMutexDestroyFuncT threadMutexDestroy;
    DbThreadRwlockInitFuncT threadRwlockInit;
    DbThreadRwlockWrlockFuncT threadRwlockWrlock;
    DbThreadRwlockRdlockFuncT threadRwlockRdlock;
    DbThreadRwlockUnlockFuncT threadRwlockUnlock;
} DbThreadFuncsT;

ADPTR_EXPORT Status DbAdptRegThreadFuncs(const DbThreadFuncsT *threadFuncs);

/* 初始化条件变量 */
ADPTR_EXPORT Status DbThreadCondInit(pthread_cond_t *cond, const pthread_condattr_t *attr);
/* 销毁条件变量 */
ADPTR_EXPORT Status DbThreadCondDestroy(pthread_cond_t *cond);
/* 发送信号通知等待此条件变量的线程 */
ADPTR_EXPORT Status DbThreadCondSignal(pthread_cond_t *cond);
/* 广播通知所有等待此条件变量的线程 */
ADPTR_EXPORT Status DbThreadCondBroadcast(pthread_cond_t *cond);
/* 等待条件变量被激活，阻塞当前线程 */
ADPTR_EXPORT Status DbThreadCondWait(pthread_cond_t *cond, pthread_mutex_t *externalMutex);
/* 等待条件变量被激活，阻塞当前线程，但会在指定时间内超时返回 */
ADPTR_EXPORT Status DbThreadCondTimedwait(
    pthread_cond_t *cond, pthread_mutex_t *externalMutex, const struct timespec *timeSpec);
/* 用于初始化条件变量属性对象 */
ADPTR_EXPORT Status DbThreadCondAttrInit(pthread_condattr_t *attr);
/* 用于销毁条件变量属性对象 */
ADPTR_EXPORT Status DbThreadCondAttrDestroy(pthread_condattr_t *attr);
/* 用于设置条件变量属性对象的时钟 */
ADPTR_EXPORT Status DbThreadCondAttrSetClock(pthread_condattr_t *attr, clockid_t clockId);

/* 用于初始化互斥锁，m为互斥锁对象指针，a为互斥锁属性对象指针，如果a为NULL，则使用默认属性。 */
ADPTR_EXPORT Status DbThreadMutexInit(pthread_mutex_t *mutex, const pthread_mutexattr_t *attr);
/* 用于加锁互斥锁，m为互斥锁对象指针。如果互斥锁已经被其他线程锁定，则当前线程会被阻塞，直到互斥锁被解锁。 */
ADPTR_EXPORT Status DbThreadMutexLock(pthread_mutex_t *mutex);
/* 用于解锁互斥锁，m为互斥锁对象指针。如果当前线程未锁定该互斥锁，则解锁操作将导致未定义的行为。 */
ADPTR_EXPORT Status DbThreadMutexUnlock(pthread_mutex_t *mutex);
/* 用于销毁互斥锁，m为互斥锁对象指针。如果互斥锁仍被其他线程锁定，则销毁操作将导致未定义的行为。 */
ADPTR_EXPORT Status DbThreadMutexDestroy(pthread_mutex_t *mutex);
/* 初始化读写锁，可以指定属性attr */
ADPTR_EXPORT Status DbThreadRwlockInit(pthread_rwlock_t *rwlock, const pthread_rwlockattr_t *attr);
/* 获取写入锁，如果锁已经被其他线程占用，则当前线程会被阻塞 */
ADPTR_EXPORT Status DbThreadRwlockWrlock(pthread_rwlock_t *lock);
/* 获取读取锁，如果锁已经被写入锁占用，则当前线程会被阻塞 */
ADPTR_EXPORT Status DbThreadRwlockRdlock(pthread_rwlock_t *lock);
/* 释放读写锁，使得其他线程可以获取锁 */
ADPTR_EXPORT Status DbThreadRwlockUnlock(pthread_rwlock_t *lock);

typedef void (*DbThreadHungCallback)(DbGlobalThreadHandle threadId, uint32_t ms);

ADPTR_EXPORT Status DbThreadCreate(const ThreadAttrsT *threadAttrs, DbThreadHandle *handle);
ADPTR_EXPORT Status DbThreadSetName(DbThreadHandle handle, const char *threadName);
ADPTR_EXPORT Status DbThreadGetName(DbThreadHandle handle, char *threadName, size_t len);
ADPTR_EXPORT Status DbThreadJoin(DbThreadHandle handle, void **result);
ADPTR_EXPORT Status DbThreadKill(DbThreadHandle handle, int32_t signal);
ADPTR_EXPORT DbThreadHandle DbThreadGetTid(void);
ADPTR_EXPORT uint64_t DbThreadGetSelfId(void);
ADPTR_EXPORT uint32_t DbThreadGetSysTid(void);
ADPTR_EXPORT void DbThreadSleep(uint32_t timeUs);
ADPTR_EXPORT void DbThreadScheYield(void);
// 以下几个接口需要在hpe下有新的实现
ADPTR_EXPORT Status DbThreadBindCpuSet(DbThreadHandle handle, cpu_set_t *cpuSet);
ADPTR_EXPORT void DbThreadStoreCpuSet(cpu_set_t *cpuSet);
ADPTR_EXPORT void DbThreadResetCpuSet(void);

typedef Status (*DbThreadMonitorCreateFunc)(DbMonitorThresholdT *monitorThr, DbThreadHungCallback theaedHungCallback);
typedef Status (*DbThreadMonitorBeginFunc)(void);
typedef Status (*DbThreadMonitorEndFunc)(void);
typedef void (*DbThreadMonitorDestroyFunc)(void);

typedef struct {
    DbThreadHungCallback threadHungCallback;
    DbThreadMonitorCreateFunc monitorCreate;
    DbThreadMonitorBeginFunc monitorBegin;
    DbThreadMonitorEndFunc monitorEnd;
    DbThreadMonitorDestroyFunc monitorDestroy;
} DbThreadMonitorFuncs;

// thread monitor API
ADPTR_EXPORT Status DbAdptRegThreadMonitorFuncs(const DbThreadMonitorFuncs *monitorFuncs);
ADPTR_EXPORT void DbThreadHungAdptCallback(DbGlobalThreadHandle threadId, uint32_t ms);
ADPTR_EXPORT Status DbThreadMonitorCreate(
    DbMonitorThresholdT *monitorThr, const DbThreadHungCallback threadHungCallback);
ADPTR_EXPORT Status DbThreadMonitorEnter(void);
ADPTR_EXPORT Status DbThreadMonitorBegin(void);
ADPTR_EXPORT Status DbThreadMonitorEnd(void);
ADPTR_EXPORT Status DbThreadMonitorExit(void);
ADPTR_EXPORT void DbSetHungThreadId(const DbThreadHandle handle);
ADPTR_EXPORT DbThreadHandle DbGetHungThreadId(void);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // ADPT_THREAD_H
