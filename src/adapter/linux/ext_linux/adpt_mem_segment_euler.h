/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * Description: segment management for shared memory
 * Author: jin<PERSON><PERSON>gin
 * Create: 2020-08-18
 */

#ifndef ADPT_MEM_SEGMENT_EULER_H
#define ADPT_MEM_SEGMENT_EULER_H

#include "adpt_types.h"
#include "adpt_atomic.h"
#include "adpt_mem_common.h"

#ifdef __cplusplus
extern "C" {
#endif

#define SHMMGR_VALID_MAGIC_NUM 0xDBDBDBDB  // 标识Euler下segMgr，rootPool和g_gmdbSegRecycleMgr共享内存管理结构已被初始化
#define BASE_SEGMENT_KEY 0xFF0000u
#define INSTANCE_ID 0x1000000u
#define INSTANCE_OFFSET 24u

#define SEGMENT_SIZE_LEVEL_NUM 11u  // Segment管理器的free list有11级

typedef struct {
    ShmemPtrT freeListShmPtr;  // SegId数组，记录空闲Segment的segId
    uint32_t freeListLen;
    uint32_t freeSegNum;
} FreeSegListT;

typedef struct {
    FreeSegListT freeListLevels[SEGMENT_SIZE_LEVEL_NUM];
    FreeSegListT hugeSegFreeList;
} FreeSegMgrT;

typedef struct {
    bool isProtect : 1;  // 是否加保护。true为加保护区，保护区的大小为两个osPagesize大小。False为不加保护区，大小为0
    uint32_t userAllocSize : 31;  // 用户申请大小，便于统计每个实例申请共享内存的大小。
    uint32_t dataAreaSize;        // 除保护区外真正可用的大小
} SegSizeT;

typedef struct {
    SegInfoT segInfo;
    DbSpinLockT lock;        // 短周期，内存模块用来控制segMgr并发申请释放内存。
    uint32_t shmPermission;  // 创建共享内存使用此权限。
    uint32_t segNumActive[MAX_INSTANCE_NUM];
    uint32_t segNumFreeList;
    uint32_t segNumMgr;
    SegSizeT segSize[MAX_SEGMENT_NUM];
    FreeSegMgrT freeSegMgr;
    // bitmap记录segId的使用情况，1标记为已经使用，0标记为尚未使用，用来对系统的segId进行复用
    uint8_t segIdBitMap[MAX_SEGMENT_NUM / BYTE_LENGTH];
    // 目前最多支持双实例，实例ID约束为1和2，0禁用
    uint64_t maxShmSize[MAX_INSTANCE_NUM];   // 系统配置共享内存上限，大小为字节
    uint64_t usedShmSize[MAX_INSTANCE_NUM];  // 当前系统使用的共享内存值，大小为字节
    uint64_t segSizeActive[MAX_INSTANCE_NUM];
    uint64_t segSizeIdle;
    uint64_t segSizeMgr;
} SegMgrT;

typedef struct {
    SegInfoT segInfo;
    DbSpinLockT lock;
    uint8_t segIdBitMap[MAX_SEGMENT_NUM / BYTE_LENGTH];  // 记录待回收的segId，1标记为待回收，0标记为不用回收
    uint64_t freeSegSize;  // 回收管理结构中待回收的共享内存大小，unit:字节
    uint32_t freeSegNum;   // 回收管理结构中待回收的segment数量
    bool isShmReturnToOs;  // 是否开启归还共享内存给OS，true：开启，false：关闭
} RecycleSegMgrT;

extern void *g_gmdbSegAddrs[MAX_SEGMENT_NUM];
/* Clear segment head addr cache in g_gmdbSegAddrs array. */
void DbRtosShmClearSegCache(void);

/**
 * @brief alloc segment by size
 * @param size should be 2^n
 * @return
 */
uint32_t SegMgrAlloc(uint32_t size, bool isHugePage, uint32_t permission, uint32_t instanceId);

#ifndef NDEBUG
uint32_t SegMgrGetFreeSeg(uint32_t size, bool isHugePage, uint32_t permission, uint32_t instanceId);
#endif

// isHugePage标识释放的是否是大页
Status SegMgrFree(uint32_t segId, bool isHugePage, uint32_t instanceId);

void *SegMgrGetSegAddr(uint32_t segId);

// 由调用者保证segId是合法的。
uint32_t SegMgrGetSizeById(uint32_t segId);

uint32_t RtosGetSegSizeById(uint32_t segId);

void *SegMgrGetSegHeaderAddr(uint32_t segId);

uint32_t SegMgrGetSegProtectSize(uint32_t segId);

void *SegMgrAttachSegment(uint32_t segId);

Status SegMgrDetachSegment(uint32_t segId);

void MoveSegIdToFront(uint32_t *segIds);

// RootPool related functions
ShmemPtrT RootPoolAlloc(uint32_t size);

ShmemPtrT RootPoolCreatePoolMgr(uint32_t key, uint32_t size);

ShmemPtrT RootPoolGetPoolMgr(uint32_t key);

void RootPoolFree(ShmemPtrT shmPtr);

void RootPoolReleasePoolMgr(uint32_t key);

inline static uint32_t ConstructSegKey(uint32_t segId)
{
    return INSTANCE_ID | BASE_SEGMENT_KEY | segId;
}

inline static uint32_t ConstructSegKeyByIns(uint32_t instanceId, uint32_t segId)
{
    return (instanceId << INSTANCE_OFFSET) | BASE_SEGMENT_KEY | segId;
}

inline static __attribute__((always_inline)) void *SegMgrOffsetToAddr(ShmemPtrT shmemPtr)
{
    uint8_t *addr = (uint8_t *)DbAtomicGetPtr((uintptr_t *)&g_gmdbSegAddrs[shmemPtr.segId]);
    if (SECUREC_UNLIKELY(addr == NULL)) {
        addr = (uint8_t *)SegMgrAttachSegment(shmemPtr.segId);
        if (addr == NULL) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Get addr. SegId: %" PRIu32 ", offset: %" PRIu32 ".",
                shmemPtr.segId, shmemPtr.offset);
            return (void *)addr;
        }
    }
    return (void *)(addr + shmemPtr.offset);
}

// 外部调用者保证对bitmap加锁，从bitMap中获取一个segId，并将对应bit位从0设置为1
// bitmap中bit位如果是0标识没有使用，如果是1标识已经被占用
inline static uint32_t AllocSegIdFromBitMap(uint8_t *bitmap)
{
    for (uint32_t i = 0; i < MAX_SEGMENT_NUM / BYTE_LENGTH; i++) {
        uint8_t *byte = &bitmap[i];
        if ((*byte) == (uint8_t)0xFF) {  // 所有的bit位都是1
            continue;
        }
        for (uint32_t j = 0; j < BYTE_LENGTH; j++) {
            if (((*byte) & (uint8_t)(1u << j)) == 0) {
                *byte |= (uint8_t)(1u << j);
                return (i * BYTE_LENGTH + j);
            }
        }
    }
    return INVALID_SEG_ID;
}

// 外部调用者保证对bitmap加锁，将segId归还给bitmap 1->0.
inline static void EraseBitMapBySegId(uint8_t *bitmap, uint32_t segId)
{
    bitmap[segId / BYTE_LENGTH] &= (uint8_t) ~(((uint8_t)(1 << (segId % BYTE_LENGTH))));
}

// 外部调用者保证对bitmap加锁，将segId设置为已经使用0->1
inline static void MarkBitMapBySegId(uint8_t *bitmap, uint32_t segId)
{
    bitmap[segId / BYTE_LENGTH] |= (uint8_t)(1 << (segId % BYTE_LENGTH));
}

inline static bool IsSegIdMarked(const uint8_t *bitmap, uint32_t segId)
{
    return (bitmap[segId / BYTE_LENGTH] & (uint8_t)(1 << (segId % BYTE_LENGTH))) != 0;
}

// 提供接口校验shmemPtr是否合法。
// 判断条件：1、segId在[0-4095]区间内; 2、shmPtr.offset + size <= segSize。
bool RtosCheckShmPtrValid(ShmemPtrT shmPtr, uint32_t size);

void RtosClearPoolAttachList(void);

Status RtosAddPoolToAttachList(ShmemPtrT poolPtr, PoolTypeE poolType, uint32_t instanceId);

bool RtosRemovePoolFromAttachList(ShmemPtrT poolPtr, bool forceRemove);

bool RtosPoolIsAttached(ShmemPtrT poolPtr);

Status MarkSegMgrBitMapBySegId(uint32_t segId);
Status EraseSegMgrBitMapBySegId(uint32_t segId);

#ifdef __cplusplus
}
#endif
#endif  // ADPT_MEM_SEGMENT_EULER_H
