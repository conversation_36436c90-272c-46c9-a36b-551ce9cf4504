/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: file content
 * Author: gaohaiyang
 * Create: 2021-09-17
 */

#ifndef ADPT_LOG_EULER_H
#define ADPT_LOG_EULER_H

#include "adpt_types.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

typedef void *(*DbLogHandleInitFuncT)(void);
typedef void (*DbLogHandleUnInitFuncT)(void *logHandle);
typedef void (*DbLogWriteFuncT)(void *logHandle, uint32_t level, int32_t errorCode, const char *str);
typedef void (*DbLogLevelSetFuncT)(void *logHandle, uint32_t level, uint32_t durationSec);

typedef struct {
    DbLogHandleInitFuncT logInit;
    DbLogHandleUnInitFuncT logDestroy;
    DbLogWriteFuncT logWrite;
    DbLogLevelSetFuncT logLevelSet;
} DbLogFuncsT;

#ifdef FEATURE_SERVER_FUNC_REG
Status DbAdptRegLogFuncs(const DbLogFuncsT *userDefLogFuncs);
#endif

Status DbAdptOrmLogInit(void);
void DbAdptOrgLogUnInit(void);

Status DbAdptEulerLogInit(void);
void DbAdptEulerLogUnInit(void);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* ADPT_LOG_EULER_H */
