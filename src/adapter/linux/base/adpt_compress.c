/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: adpt_compress.c
 * Description:
 * Author:
 * Create: 2023-09-07
 */
#include "adpt_compress.h"
#include "adpt_log.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#ifdef FEATURE_SERVER_FUNC_REG

bool g_gmdbCompressInit = false;
DbCompressFuncsT g_gmdbCompressFuns = {0};

bool g_gmdbSpaceCompressInit = false;
DbCompressFuncsT g_gmdbSpaceCompressFuns = {0};

Status DbAdptRegCompressFuncs(const DbCompressFuncsT *compressFuncs)
{
    DB_POINTER(compressFuncs);
    if (g_gmdbCompressInit) {
        return GMERR_DUPLICATE_OBJECT;
    }

    g_gmdbCompressFuns = *compressFuncs;

    // 确保压缩和解压函数都被注册后才开启持久化数据压缩解压能力
    if (g_gmdbCompressFuns.compressFunc != NULL && g_gmdbCompressFuns.decompressFunc != NULL) {
        g_gmdbCompressInit = true;
    }
    return GMERR_OK;
}

Status DbAdptRegSpaceCompressFuncs(const DbCompressFuncsT *compressFuncs)
{
    DB_POINTER(compressFuncs);
    if (g_gmdbSpaceCompressInit) {
        return GMERR_DUPLICATE_OBJECT;
    }

    g_gmdbSpaceCompressFuns = *compressFuncs;

    // 确保压缩和解压函数都被注册后才开启持久化数据压缩解压能力
    if (g_gmdbSpaceCompressFuns.compressFunc != NULL && g_gmdbSpaceCompressFuns.decompressFunc != NULL) {
        g_gmdbSpaceCompressInit = true;
    }
    return GMERR_OK;
}
#endif

Status DbDataCompress(uint8_t *dest, uint32_t *destLen, const uint8_t *source, uint32_t sourceLen)
{
#ifdef FEATURE_SERVER_FUNC_REG
    if (g_gmdbCompressFuns.compressFunc != NULL) {
        int32_t ret = g_gmdbCompressFuns.compressFunc(dest, destLen, source, sourceLen);
        if (ret != 0) {
            // no need to write log here，有些场景压缩失败后，仍然继续后续操作，由调用处打印日志
            return GMERR_DATA_EXCEPTION;
        }
        return GMERR_OK;
    }
#endif
    DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Unregist compress func.");
    return GMERR_DATA_EXCEPTION;
}

Status DbDataDecompress(uint8_t *dest, uint32_t *destLen, const uint8_t *source, uint32_t sourceLen)
{
#ifdef FEATURE_SERVER_FUNC_REG
    if (g_gmdbCompressFuns.decompressFunc != NULL) {
        int32_t ret = g_gmdbCompressFuns.decompressFunc(dest, destLen, source, sourceLen);
        if (ret != 0) {
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Execute decompress func:%" PRId32, ret);
            return GMERR_DATA_EXCEPTION;
        }
        return GMERR_OK;
    }
#endif
    DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Unregist decompress func.");
    return GMERR_DATA_EXCEPTION;
}

Status DbSpaceDataCompress(uint8_t *dest, uint32_t *destLen, const uint8_t *source, uint32_t sourceLen)
{
#ifdef FEATURE_SERVER_FUNC_REG
    if (g_gmdbSpaceCompressFuns.compressFunc != NULL) {
        int32_t ret = g_gmdbSpaceCompressFuns.compressFunc(dest, destLen, source, sourceLen);
        if (ret != 0) {
            // no need to write log here，有些场景压缩失败后，仍然继续后续操作，由调用处打印日志
            return GMERR_DATA_EXCEPTION;
        }
        return GMERR_OK;
    }
#endif
    DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Unregist space compress func.");
    return GMERR_DATA_EXCEPTION;
}

Status DbSpaceDataDecompress(uint8_t *dest, uint32_t *destLen, const uint8_t *source, uint32_t sourceLen)
{
#ifdef FEATURE_SERVER_FUNC_REG
    if (g_gmdbSpaceCompressFuns.decompressFunc != NULL) {
        int32_t ret = g_gmdbSpaceCompressFuns.decompressFunc(dest, destLen, source, sourceLen);
        if (ret != 0) {
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Execute space decompress func:%" PRId32, ret);
            return GMERR_DATA_EXCEPTION;
        }
        return GMERR_OK;
    }
#endif
    DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Unregist space decompress func.");
    return GMERR_DATA_EXCEPTION;
}

bool DbEnableCompress(void)
{
#ifdef FEATURE_SERVER_FUNC_REG
    return g_gmdbCompressInit;
#endif
    return false;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
