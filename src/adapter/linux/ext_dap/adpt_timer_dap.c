/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: adpt_timer_dap.c
 * Description: Semaphore interfaces
 * Author: liaoqidan
 * Create: 2020-10-21
 */

#include <stdlib.h>
#include <pthread.h>
#include <signal.h>
#include <time.h>
#include <dlfcn.h>
#include "adpt_define.h"
#include "adpt_timer.h"
#include "adpt_time.h"
#include "adpt_log.h"

#ifdef __cplusplus
extern "C" {
#endif

struct Timer {
    timer_t timerId;
    CallBackStructT callback;
    uint32_t period;
    TimerModeE type;
    struct Timer *next;
};

// 用途：存放定时器的当前定时器（定时器可能有多个）
// 是否并发初始化：是
// 是否并发读写：是
// 并发方案：通过rwlock控制g_gmdbTimers及timers链表的并发
static struct Timer *g_gmdbTimers;
static pthread_rwlock_t g_gmdbTimersRWLock = PTHREAD_RWLOCK_INITIALIZER;

static void DbTimersAdd(struct Timer *t)
{
    int err = DbThreadRwlockWrlock(&g_gmdbTimersRWLock);
    if (err != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "rwrlock for timer, os %s", dlerror());
        return;
    }

    t->next = g_gmdbTimers;
    g_gmdbTimers = t;

    err = DbThreadRwlockUnlock(&g_gmdbTimersRWLock);
    if (err != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "unlock for timer, os no %" PRId32, errno);
        return;
    }
}

static struct Timer *DbTimersRemove(const struct Timer *t)
{
    struct Timer *ret = NULL;

    int err = DbThreadRwlockWrlock(&g_gmdbTimersRWLock);
    if (err != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "rwrlock, os no %" PRId32, errno);
        return NULL;
    }

    struct Timer *prev = NULL;
    struct Timer *cur = g_gmdbTimers;
    while (cur != NULL) {
        if (cur == t) {
            if (prev == NULL) {
                g_gmdbTimers = cur->next;
            } else {
                prev->next = cur->next;
            }
            cur->next = NULL;
            ret = cur;
            break;
        }
        prev = cur;
        cur = cur->next;
    }

    err = DbThreadRwlockUnlock(&g_gmdbTimersRWLock);
    if (err != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "unlock, os no %" PRId32, errno);
        return NULL;
    }

    return ret;
}
static CallBackStructT DbTimersGetCallback(const struct Timer *t)
{
    CallBackStructT callback = {.callBack = NULL, .parameter = NULL};

    int err = DbThreadRwlockRdlock(&g_gmdbTimersRWLock);
    if (err != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "rwrlock, os no %" PRId32, errno);
        return callback;
    }

    struct Timer *cur = g_gmdbTimers;
    while (cur != NULL) {
        if (cur == t) {
            callback = cur->callback;
            break;
        }
        cur = cur->next;
    }

    err = DbThreadRwlockUnlock(&g_gmdbTimersRWLock);
    if (err != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "unlock, os no %" PRId32, errno);
    }

    return callback;
}

static void DbTimerCallbackWraper(union sigval param)
{
    // 用于区分共进程模式下的客户端和服务端线程
    DbSetServerThreadFlag();
    struct Timer *t = (struct Timer *)param.sival_ptr;
    CallBackStructT callback = DbTimersGetCallback(t);
    if (callback.callBack != NULL) {
        callback.callBack(callback.parameter);
    }
    DbClearServerThreadFlag();
}

TimerHandleT DbTimerCreate(const char *name, CallBackStructT *inputPara, uint32_t time, TimerModeE type)
{
    DB_POINTER2(name, inputPara);

    struct Timer *t = malloc(sizeof(*t));
    if (t == NULL) {
        return (TimerHandleT){.timerHandle = 0};
    }
    // clang-format off
    *t = (struct Timer) {
        .timerId = 0,
        .callback = *inputPara,
        .period = time,
        .type = type,
        .next = NULL,
    };
    // clang-format on
    struct sigevent sig = {
        .sigev_notify = SIGEV_THREAD,
        .sigev_value =
            {
                .sival_ptr = t,
            },
        .sigev_notify_function = DbTimerCallbackWraper,
    };
    int ret = timer_create(CLOCK_MONOTONIC, &sig, &t->timerId);
    if (ret != 0) {
        free(t);
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "timer create. ret: %" PRId32 ", os no: %" PRId32 ".", ret, (int32_t)errno);
        return (TimerHandleT){.timerHandle = 0};
    }

    DbTimersAdd(t);

    return (TimerHandleT){.timerHandle = (uintptr_t)t};
}

Status DbTimerStart(TimerHandleT timer, uint32_t type)
{
    struct Timer *t = (struct Timer *)(uintptr_t)timer.timerHandle;
    if (t == NULL) {
        return GMERR_INTERNAL_ERROR;
    }

    DB_ASSERT((uint32_t)t->type == type);

    struct itimerspec new = {0};
    new.it_value.tv_sec = t->period / (uint32_t)MSECONDS_IN_SECOND;
    new.it_value.tv_nsec = (int64_t)((t->period % MSECONDS_IN_SECOND) * NSECONDS_IN_MSECOND);
    if ((uint32_t)t->type != 0) {
        new.it_interval.tv_sec = new.it_value.tv_sec;
        new.it_interval.tv_nsec = new.it_value.tv_nsec;
    }
    int ret = timer_settime(t->timerId, 0, &new, NULL);
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Timer set time, os no: %" PRId32 ".", errno);
        return GMERR_INTERNAL_ERROR;
    }

    return GMERR_OK;
}

Status DbTimerStop(TimerHandleT timer)
{
    struct Timer *t = (struct Timer *)(uintptr_t)timer.timerHandle;
    if (t == NULL) {
        return GMERR_INTERNAL_ERROR;
    }

    struct itimerspec new = {0};
    int ret = timer_settime(t->timerId, 0, &new, NULL);
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Timer stop, os no: %" PRId32 ".", errno);
        return GMERR_INTERNAL_ERROR;
    }

    return GMERR_OK;
}

Status DbTimerDelete(TimerHandleT timer)
{
    struct Timer *t = (struct Timer *)(uintptr_t)timer.timerHandle;
    if (t == NULL) {
        return GMERR_INTERNAL_ERROR;
    }

    int ret = timer_delete(t->timerId);
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Timer delete, os no %" PRId32 ".", errno);
        return GMERR_INTERNAL_ERROR;
    }

    if (DbTimersRemove(t) == NULL) {
        return GMERR_INTERNAL_ERROR;
    }

    free(t);

    return GMERR_OK;
}

bool DbCheckTimerHandle(const TimerHandleT timer)
{
    return timer.timerHandle != 0;
}

#ifdef __cplusplus
}
#endif
