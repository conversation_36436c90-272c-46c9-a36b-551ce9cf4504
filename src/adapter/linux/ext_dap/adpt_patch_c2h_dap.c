/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: adpt_patch_c2h_dap.c
 * Description: db patch libgmadapter.so cold to hot hook for dap
 * Author:
 * Create: 2022-11-10
 */
#include "adpt_patch_c2h_type.h"
#include "adpt_log.h"

/* 预埋冷转热激活 */
__attribute__((visibility("default"))) void LibGmAdapterDapPatchC2hActiveFunc(void)
{
    VOS_NOP();
    DB_LOG_INFO("libgmadapter.so.5.1 reserved patch c2h active\n");
}

/* 预埋冷转热去激活 */
__attribute__((visibility("default"))) void LibGmAdapterDapPatchC2hDeactiveFunc(void)
{
    VOS_NOP();
    DB_LOG_INFO("libgmadapter.so.5.1 reserved patch c2h deactive\n");
}

__attribute__((visibility("default"))) int32_t libgmadapter_PAT_C2HSyncHookFunc(SISP_PAT_C2H_OPER_CODE_E operCode,
    const SISP_PAT_C2H_OPER_CB_S *rspMsg, const SISP_PAT_CFG_INFO_S *cfgInfo, uint32_t *isProcess)
{
    if (cfgInfo == NULL || isProcess == NULL) {
        return 1;
    }
    int32_t ret = 0;
    *isProcess = 0;

    // 这里的libgmadapter.so.5.1需要和设备上maps同名
    if (strcmp(cfgInfo->acFileName, "libgmadapter.so.5.1") != 0) {
        return ret;
    }

    switch (operCode) {
        case SISP_PAT_C2H_OPER_ACTIVE: {
            LibGmAdapterDapPatchC2hActiveFunc();
            break;
        }
        case SISP_PAT_C2H_OPER_DEACTIVE: {
            LibGmAdapterDapPatchC2hDeactiveFunc();
            break;
        }
        default:
            printf("libgmadapter.so.5.1 c2h active or deactive oper worthless.");
            ret = 1;
    }

    *isProcess = 1;
    return ret;
}
