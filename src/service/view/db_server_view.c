/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * File Name: db_server_view.c
 * Description: server view file for server view.
 * Author:
 * Create: 2021.05.24
 */

#include "db_server_view.h"
#include "dm_data_prop.h"
#include "adpt_cpu_stats.h"
#include "db_config.h"
#include "drt_instance.h"
#include "drt_flow_control.h"
#include "adpt_rdtsc.h"
#include "db_instance.h"
#include "db_server_dfgmt_task.h"
#include "se_daemon.h"
#include "ee_session.h"
#include "se_dfx.h"
#include "ee_stmt.h"
#include "ee_stmt_interface.h"
#include "db_server.h"
#include "db_version.h"  // cmake自动生成的头文件
#include "drt_common.h"
#include "srv_data_service.h"
#include "srv_data_fastpath_dw_pubsub.h"
#include "ee_background_schedule.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifdef NDE<PERSON>UG
#define GMDB_BUILT_MODE "release"
#else
#define GMDB_BUILT_MODE "debug"
#endif

typedef struct KeyResource {
    uint32_t cpuUsage;
    uint64_t usedShmMemSize;
    uint64_t usedDynMemSize;
    uint64_t connUsedNum;
    uint64_t workerUsedNum;
    uint64_t lockUsedNum;
    double usedShmMemRate;
    double usedDynMemRate;
    double connUsedRate;
    double workerUsedRate;
    double lockUsedRate;
} KeyResourceT;

static Status SvDbServerSetRebootInfo(const DmVertexT *vertex)
{
#ifdef FEATURE_RSMEM
    DB_POINTER(vertex);
    DmValueT value;
    value.type = DB_DATATYPE_BOOL;
    value.value.boolValue = DbCommonGetIsWarmReboot();
    Status ret = DmVertexSetPropeByName("WARM_REBOOT", value, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DB_SERVER view write warm reboot .");
        return ret;
    }

    value.value.boolValue = DbCfgGetBoolLite(DB_CFG_IS_USE_RSM, NULL);
    ret = DmVertexSetPropeByName("USE_RSM", value, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DB_SERVER view write use rsmem.");
        return ret;
    }

    value.type = DB_DATATYPE_UINT64;
    value.value.ulongValue = DbCommonGetWarmRebootTime();
    ret = DmVertexSetPropeByName("RECOVERY_TIME", value, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DB_SERVER view write recovery time.");
        return ret;
    }
#endif
    return GMERR_OK;
}

#define USAGE_STRING_LEN 16
#define KEY_RESOURCE_EXCEED_THRESHOLD 0.900000001  // 和 RELATIVE_RAISE_PERCENT 保持一致
#define KEY_RESOURCE_SRT_LEN 128

static Status SetServerCpuInfo(const DmVertexT *vertex)
{
    Status ret = DmVertexSetUint16PropeByName(vertex, "CPU_USAGE", (uint16_t)DbGetAllCpuUsage());
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Server stat view write cpu usage.");
        return ret;
    }
    ret = DmVertexSetUint64PropeByName(vertex, "CPU_CYCLE_PER_USEC", DbGetCyclesPerUsec());
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Server stat view write cpu cycle per microseconds.");
        return ret;
    }
    return GMERR_OK;
}

static Status SvQueryProcDbServerSetTime(void *vertex)
{
    struct timeval nowTimestamp;
    (void)DB_GET_TIME_OF_DAY(&nowTimestamp, NULL);
    uint64_t nowTimeVal = (uint64_t)nowTimestamp.tv_sec * DEVIL_TIMELINUX_MSEC_TIME + (uint64_t)nowTimestamp.tv_usec;
    char timeStr[DB_MAX_NAME_LEN];
    uint64_t dbStartTimeVal =
        DbGetServerStartTimeVal(DbGetInstanceId(DbGetInstanceByMemCtx(((DmVertexT *)vertex)->memCtx)));
    Status ret = DbGetDurationStrByTimeVal(dbStartTimeVal, nowTimeVal, timeStr, DB_MAX_NAME_LEN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Server view get running time string.");
        return ret;
    }

    ret = DmVertexSetStrPropeByName((const DmVertexT *)vertex, "RUNNING_TIME", timeStr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Server view set running time string.");
        return ret;
    }
    ret = DbGetTimestampStrByTimeVal(dbStartTimeVal, timeStr, DB_MAX_NAME_LEN, DEFAULT_TIME_FORMAT);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Server view get timestamp: %" PRIu64, nowTimeVal);
        return ret;
    }
    return DmVertexSetStrPropeByName((const DmVertexT *)vertex, "START_TIME", timeStr);
}

Status SvQueryProcDbServer(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);

    DbInstanceHdT dbInstance = DbGetInstanceBySvCursor(cursor);

    uint16_t *serverId = (uint16_t *)cursor->rowId;
    if (*serverId >= DB_MAX_SINGLE_PROCESS_INSTANCE_NUM) {
        DB_LOG_DEBUG("Get server view record finish.");
        return GMERR_NO_DATA;
    }

    Status ret = SetServerCpuInfo(vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Server stat view write cpu info.");
        return ret;
    }

    ret = DmVertexSetUint64PropeByName(vertex, "INITIATE_DELAY", DbGetServerInitiateDelay(DbGetInstanceId(dbInstance)));

    ret = (ret != GMERR_OK) ? ret : SvQueryProcDbServerSetTime(vertex);

    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetStrPropeByName((const DmVertexT *)vertex, "SERVER_VERSION", GMDB_V5_CODE_VERSION);

    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName((const DmVertexT *)vertex, "BUILD_MODE", GMDB_BUILT_MODE);

    ret = (ret != GMERR_OK) ? ret : SvDbServerSetRebootInfo(vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Server stat view write build mode.");
    } else {
        (*serverId)++;
    }
    return ret;
}

static Status SvConfigViewSetConfigValue(const DmVertexT *vertex, DbCfgValueT *cfgValue)
{
    DB_POINTER2(vertex, cfgValue);

    char *cfgValueStr = NULL;
    char cfgValueIntStr[DB_CFG_PARAM_MAX_STRING] = {0};
    if (cfgValue->type == DB_DATATYPE_STRING) {
        cfgValueStr = cfgValue->str;
    } else {
        int32_t snLen = snprintf_s(
            cfgValueIntStr, DB_CFG_PARAM_MAX_STRING, DB_CFG_PARAM_MAX_STRING - 1, "%" PRId32, cfgValue->int32Val);
        if (snLen < 0) {
            DB_LOG_ERROR(GMERR_FIELD_OVERFLOW, "Config view unsucc to copy config value");
            return GMERR_FIELD_OVERFLOW;
        }
        cfgValueStr = cfgValueIntStr;
    }
    Status ret = DmVertexSetStrPropeByName(vertex, "VALUE", cfgValueStr);
    if (ret != GMERR_OK) {
        return ret;
    }
    char *cfgTypeStr = (cfgValue->type == DB_DATATYPE_STRING) ? "STRING" : "INT32";
    return DmVertexSetStrPropeByName(vertex, "TYPE", cfgTypeStr);
}

Status SvQueryProcConfigPara(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    DbInstanceHdT dbInstance = DbGetInstanceBySvCursor(cursor);

    uint16_t *configId = (uint16_t *)cursor->rowId;

    if (*configId >= (int)DB_CFG_EM_ITEM_BUTT) {
        DB_LOG_DEBUG("Get server view record finish.");
        return GMERR_NO_DATA;
    }
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(dbInstance);
    DbCfgValueT cfgValue;
    Status ret = DbCfgGet(cfgHandle, *configId, &cfgValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unsucc to get config value.");
        return ret;
    }
    ret = SvConfigViewSetConfigValue(vertex, &cfgValue);

    const DbCfgItemDescT *cfgDesc = DbCfgDescById(*configId);
    if (cfgDesc == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get cfgDesc.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    char *cfgChangeModeStr[] = {
        "DB_CFG_CHANGE_NOT_ALLOWED", "DB_CFG_CHANGE_MORE", "DB_CFG_CHANGE_LESS", "DB_CFG_CHANGE_NO_LIMIT"};
    char *cfgModeStr = cfgChangeModeStr[cfgDesc->changeMode];

    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "NAME", cfgDesc->name);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "CHANGE_MODE", cfgModeStr);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "DESCRIPTION", cfgDesc->desc);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "DEFAULT_VALUE", cfgDesc->defaultValue);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetInt32PropeByName(vertex, "MIN", (int32_t)cfgDesc->min);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetInt32PropeByName(vertex, "MAX", (int32_t)cfgDesc->max);
    (*configId)++;
    return ret;
}

static Status SvMemCompactTasksInner(const DfgmtTaskStatT *taskStat, MemTaskTypeE taskType, const DmVertexT *vertex)
{
    DB_POINTER2(taskStat, vertex);
    Status ret = DmVertexSetUint32PropeByName(vertex, "TASK_TYPE", (uint32_t)taskType);
    if (ret != GMERR_OK) {
        return ret;
    }
    char *taskStatus[(int)TASK_STATUS_MAX] = {"init", "running", "finished"};
    char *taskStr = taskStatus[taskStat->curTaskStatus];
    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "TASK_STATUS", taskStr);

    char *subTaskStatusForLabel[(int32_t)LABEL_DFGMT_STATUS_MAX] = {
        "init", "heap", "clustered_hash", "primary_key", "second_index", "finished"};

    char *subTaskStatusForRsmPage[(int32_t)RSM_PAGE_DFGMT_STATUS_MAX] = {
        "init", "empty", "compressing", "compress_abort", "compress_finish"};

    if (taskType == MEM_COMPACT_LABEL && taskStat->curTaskState < LABEL_DFGMT_STATUS_MAX) {
        taskStr = subTaskStatusForLabel[taskStat->curTaskState];
    } else if (taskType == MEM_COMPACT_RSM_PAGE && taskStat->curTaskState < RSM_PAGE_DFGMT_STATUS_MAX) {
        taskStr = subTaskStatusForRsmPage[taskStat->curTaskState];
    } else {
        ret = GMERR_DATA_EXCEPTION;
    }
    taskStr = (ret != GMERR_OK) ? "" : taskStr;

    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "SUB_TASK_STATUS", taskStr);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "CREATE_COUNT", taskStat->createCount);
    uint32_t destroyCount = taskStat->createCount - taskStat->curTaskCount;
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "DESTROY_COUNT", destroyCount);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "SCHEDULE_COUNT", taskStat->schedExecCount);

    uint64_t curTaskExecBeginTime = taskStat->curTaskExecBeginTime;
    uint64_t timeConsume = curTaskExecBeginTime != 0 ? DbToUseconds(DbRdtsc() - curTaskExecBeginTime) : 0;
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "DURATION", timeConsume);

    uint64_t execTimeAvg = (taskStat->schedExecCount != 0) ? (taskStat->execTimeTotal / taskStat->schedExecCount) : 0;
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "EXEC_TIME_AVG", execTimeAvg);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "EXEC_TIME_MIN", taskStat->execTimeMin);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "EXEC_TIME_MAX", taskStat->execTimeMax);
    return ret;
}

Status SvQueryProcMemCompactTasksStat(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(cursor, vertex);
    DbInstanceHdT dbInstance = DbGetInstanceBySvCursor(cursor);

    if (DbGetDfgmtMgr() == NULL) {
        DB_LOG_DBG_INFO("Get mem compact task view record finish because not enable.");
        return GMERR_NO_DATA;
    }

    DrtInstanceT *drtInstance = DrtGetInstance(dbInstance);
    if (drtInstance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get instance when query memory compact view.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    int32_t *taskType = (int32_t *)cursor->rowId;
    DfgmtTaskStatT taskStat;
    while ((*taskType) < (int32_t)MEM_COMPACT_MAX) {
        (void)DbSrvGetDfgmtTaskStat((MemTaskTypeE)*taskType, &taskStat);
        if (taskStat.createCount != 0) {
            break;
        }
        (*taskType)++;
    }
    if ((*taskType) >= (int32_t)MEM_COMPACT_MAX) {
        DB_LOG_DBG_INFO("Get mem compact task view record finish.");
        return GMERR_NO_DATA;
    }
    Status ret = SvMemCompactTasksInner(&taskStat, (MemTaskTypeE)*taskType, (const DmVertexT *)vertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    (*taskType)++;
    return GMERR_OK;
}

static Status SvQueryProcSetGlobalResource(void *vertex, SvCursorT *cursor)
{
    DbInstanceHdT dbInstance = DbGetInstanceBySvCursor(cursor);
    Status ret = DmVertexSetUint64PropeByName(vertex, "TOTAL_SHM_MEM_SIZE", DbGetMaxShmemSize());
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint64PropeByName(vertex, "TOTAL_USED_SHM_MEM_SIZE",
                  DbShmCtxGetTreePhySize(DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, DbGetInstanceId(dbInstance)), true));
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "TOTAL_DYN_MEM_SIZE", DbGetMaxDynSize());
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "TOTAL_USED_DYN_MEM_SIZE", DbGetCurrDynSize());
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint16PropeByName(vertex, "CPU_USAGE", (uint16_t)DbGetAllCpuUsage());
    return ret;
}

static Status SvQueryReverseConnFillCycle(DrtReservedConnStatT *stat, const DmNodeT *cycleNode, const uint32_t numCycle)
{
    Status ret = DmNodeSetUint32PropeByName(cycleNode, "USER_ID", numCycle + 1);
    ret = (ret != GMERR_OK) ? ret :
                              DmNodeSetStrPropeByName(
                                  cycleNode, "USER_NAME", stat->tokens[numCycle].userName, MAX_OS_USER_NAME_LENGTH + 1);
    ret = (ret != GMERR_OK) ? ret :
                              DmNodeSetStrPropeByName(cycleNode, "PROCESS_NAME", stat->tokens[numCycle].processName,
                                  DB_MAX_PROC_NAME_LEN + 1);
    ret = (ret != GMERR_OK) ? ret :
                              DmNodeSetUint32PropeByName(
                                  cycleNode, "RESERVED_CONN_NUM", (uint32_t)stat->tokens[numCycle].reservedConnNum);
    ret = (ret != GMERR_OK) ? ret :
                              DmNodeSetUint32PropeByName(
                                  cycleNode, "USED_RESERVED_CONN_NUM", (uint32_t)stat->tokens[numCycle].useConnNum);
    return ret;
}

static Status SvQuerySetReverseConnResource(DrtReservedConnStatT *stat, const DmVertexT *vertex, const char *nodeName)
{
    DB_POINTER(stat);
    DmNodeT *cycleNode = NULL;
    Status ret = DmVertexGetNodeByName(vertex, nodeName, &cycleNode);
    if (ret != GMERR_OK) {
        return ret;
    }
    DB_POINTER(cycleNode);
    for (uint32_t numCycle = 0; numCycle < MAX_USE_RESERVED_CONN_USER_NUM; numCycle++) {
        ret = DmNodeSetElementIndex(cycleNode, numCycle);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = SvQueryReverseConnFillCycle(stat, cycleNode, numCycle);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status SvQueryProcSetRtResource(void *vertex, SvCursorT *cursor, DbInstanceHdT dbInstance)
{
    DrtInstanceT *drtIns = DrtGetInstance(dbInstance);
    if (drtIns == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Get drtInstance unsucc when set RtResouce.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    Status ret = DmVertexSetUint32PropeByName(vertex, "SUB_NODE_PILED_SIZE", DrtGetUsedSubNodeNum(dbInstance));

    uint64_t subMsgShmCtxSize = 0;
    ret = (ret != GMERR_OK) ? ret : DrtGetSubMsgShmCtxSize(&subMsgShmCtxSize, dbInstance);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "SUB_MSG_SHM_MEM_SIZE", subMsgShmCtxSize);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint32PropeByName(vertex, "TOTAL_CONN_NUM", DbDynArrayGetMaxNum(&drtIns->connMgr.connArr));
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint16PropeByName(vertex, "TOTAL_USED_NORMAL_CONN_NUM", drtIns->connMgr.curNum);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint16PropeByName(vertex, "TOTAL_RESERVED_CONN_NUM", drtIns->connMgr.reservedNum);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint16PropeByName(vertex, "TOTAL_USED_RESERVED_CONN_NUM", drtIns->connMgr.useReservedNum);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint16PropeByName(vertex, "TOTAL_EMER_CONN_NUM", EMRG_CONN_MAX_NUM);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint16PropeByName(vertex, "TOTAL_USED_EMER_CONN_NUM", drtIns->connMgr.useEmrgNum);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint16PropeByName(vertex, "DRT_WORKER_MAX_NUM", drtIns->workerMgr.workerList.maxNum);
    ret = (ret != GMERR_OK) ? ret :
                              DmVertexSetUint16PropeByName(
                                  vertex, "DRT_WORKER_USED_NUM", WorkerListGetTotalNum(&drtIns->workerMgr.workerList));
    ret = (ret != GMERR_OK) ?
              ret :
              SvQuerySetReverseConnResource(&drtIns->connMgr.reservedConnStat, vertex, "RESERVED_CONN_INFO");
    return ret;
}

static Status SvQueryProcSetSeResource(void *vertex, SvCursorT *cursor, DbInstanceHdT dbInstance)
{
    uint64_t seTopShmCtxSize = 0;
    Status ret = SeGetTopShmctxUsedSize(DbGetInstanceId(dbInstance), &seTopShmCtxSize);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "SE_TOP_SHM_MEM_SIZE", seTopShmCtxSize);
    uint64_t seDevShmCtxSize = 0;
    ret = (ret != GMERR_OK) ? ret : SeGetDevShmctxUsedSize(DbGetInstanceId(dbInstance), &seDevShmCtxSize);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "SE_DEV_SHM_MEM_SIZE", seDevShmCtxSize);

    SeLockOverviewStatT lockStat = {0};
    SeGetLockOverView(QryGetSeInstance(cursor->stmt), &lockStat);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "TRX_LOCK_NUM", lockStat.lockCnt);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "TRX_LOCK_USED_NUM", lockStat.usedLockCnt);
    return ret;
}

static Status SvQueryProcSetOtherResource(void *vertex, SvCursorT *cursor, DbInstanceHdT dbInstance)
{
    Status ret = DmVertexSetUint32PropeByName(vertex, "TOTAL_USED_STMT_NUM", QrySessionGetUsedStmtNum(dbInstance));
    ret = (ret != GMERR_OK) ? ret :
                              DmVertexSetUint64PropeByName(vertex, "CATALOG_MEMCTX", CataGetUsedMemCtxSize(dbInstance));
    return ret;
}

Status SvQueryProcKeyResourceStat(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    DbInstanceHdT dbInstance = DbGetInstanceBySvCursor(cursor);

    uint32_t *instance = (uint32_t *)cursor->rowId;
    if (*instance > 0) {
        return GMERR_NO_DATA;
    }
    Status ret = SvQueryProcSetGlobalResource(vertex, cursor);

    ret = (ret != GMERR_OK) ? ret : SvQueryProcSetRtResource(vertex, cursor, dbInstance);

    ret = (ret != GMERR_OK) ? ret : SvQueryProcSetSeResource(vertex, cursor, dbInstance);

    ret = (ret != GMERR_OK) ? ret : SvQueryProcSetOtherResource(vertex, cursor, dbInstance);

    ++(*instance);
    return ret;
}

static Status SvQueryProcConnTreatFillCycle(
    const DrtPipeThreatStatT *stat, const DmNodeT *cycleNode, const uint16_t cycleIndex)
{
    Status ret = DmNodeSetUint32PropeByName(cycleNode, "REQUEST_COUNT", stat->statPerCycle[cycleIndex].requestCount);
    ret = (ret != GMERR_OK) ?
              ret :
              DmNodeSetUint32PropeByName(cycleNode, "BIG_PACK_COUNT", stat->statPerCycle[cycleIndex].bigPackCount);
    ret = (ret != GMERR_OK) ?
              ret :
              DmNodeSetUint32PropeByName(cycleNode, "TOTAL_REQUEST_SIZE", stat->statPerCycle[cycleIndex].requestSize);
    return ret;
}

static Status SvQueryProcConnThreatFillCycleIntoVertex(
    const DrtPipeThreatStatT *stat, const DmVertexT *vertex, uint64_t currentIdx, const char *nodeName)
{
    DB_POINTER(stat);
    DmNodeT *cycleNode = NULL;
    Status ret = DmVertexGetNodeByName(vertex, nodeName, &cycleNode);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint16_t startId = (uint16_t)(currentIdx + DRT_PIPE_THREAT_STAT_CYCLE_NUMS - 1);
    for (uint8_t numCycle = 0; numCycle < (DRT_PIPE_THREAT_STAT_CYCLE_NUMS - 1); numCycle++) {
        ret = DmNodeSetElementIndex(cycleNode, numCycle);
        if (ret != GMERR_OK) {
            return ret;
        }
        uint16_t cycleIndex = (uint16_t)((startId - numCycle) % DRT_PIPE_THREAT_STAT_CYCLE_NUMS);
        ret = DmNodeSetUint8PropeByName(cycleNode, "CYCLE_ID", (uint8_t)(numCycle + 1u));
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = SvQueryProcConnTreatFillCycle(stat, cycleNode, cycleIndex);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

inline static uint32_t SvDivide(uint64_t divisor, uint32_t dividends)
{
    return (dividends == 0) ? 0 : (uint32_t)(divisor / dividends);
}

static Status SvQueryProcConnThreat(const DrtPipeT *pipe, const DmVertexT *vertex)
{
    DB_POINTER(pipe);
    Status ret = DmVertexSetUint16PropeByName(vertex, "CONN_ID", pipe->connId);
    DrtPipeThreatStatT stat = pipe->threatStat;
    uint16_t curIdx = stat.curIdx;
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "CYCLE_COUNT", stat.cycleCnt);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint32PropeByName(vertex, "CURRENT_REQUEST_COUNT", stat.statPerCycle[curIdx].requestCount);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint32PropeByName(vertex, "CURRENT_BIG_PACK_COUNT", stat.statPerCycle[curIdx].bigPackCount);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint32PropeByName(vertex, "CURRENT_TOTAL_REQUEST_SIZE", stat.statPerCycle[curIdx].requestSize);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "MAX_REQUEST_COUNT", stat.maxRequestCount);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "MAX_BIG_PACK_COUNT", stat.maxBigPackCount);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "MAX_TOTAL_REQUEST_SIZE", stat.maxRequestSize);
    uint32_t requestAvg = SvDivide(stat.allRequestCount, stat.cycleCnt);
    uint32_t bigPackAvg = SvDivide(stat.allBigPackCount, stat.cycleCnt);
    uint32_t requestSizeAvg = SvDivide(stat.allRequestSize, stat.cycleCnt);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "AVERAGE_REQUEST_COUNT", requestAvg);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "AVERAGE_BIG_PACK_COUNT", bigPackAvg);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "AVERAGE_TOTAL_REQUEST_SIZE", requestSizeAvg);
    ret = (ret != GMERR_OK) ? ret : SvQueryProcConnThreatFillCycleIntoVertex(&stat, vertex, curIdx, "CONN_CYCLE_STAT");
    return ret;
}

Status SvQueryProcConnThreatStat(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    DbInstanceHdT dbInstance = DbGetInstanceBySvCursor(cursor);

    DrtInstanceT *drtInstance = DrtGetInstance(dbInstance);
    if (drtInstance == NULL) {
        DB_LOG_ERROR(
            GMERR_UNEXPECTED_NULL_VALUE, "Unable to get instance when query connection threat statistics view.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DrtPipeListT *pipeList = &drtInstance->connMgr.pipeList;
    DB_POINTER(pipeList);
    uint16_t *pipeId = (uint16_t *)cursor->rowId;
    DrtBucketT *drtBucket = NULL;
    DrtPipeT *drtPipe = NULL;
    DbRWSpinRLock(&pipeList->lock);
    while (DbDynArrayValidId(&pipeList->pipeArr, *pipeId)) {
        drtBucket = (DrtBucketT *)DbDynArrayGetItemById(&pipeList->pipeArr, *pipeId);
        drtPipe = drtBucket == NULL ? NULL : (DrtPipeT *)drtBucket->item;
        if (drtPipe != NULL && drtPipe->dbPipe.protoPipe != NULL && drtPipe->connId != DB_INVALID_UINT16) {
            break;
        }
        (*pipeId)++;
    }
    if (!DbDynArrayValidId(&pipeList->pipeArr, *pipeId) || drtPipe == NULL) {
        DbRWSpinRUnlock(&pipeList->lock);
        return GMERR_NO_DATA;
    }
    Status ret = SvQueryProcConnThreat(drtPipe, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to operate connection threat statistics.");
    }
    (*pipeId)++;
    DbRWSpinRUnlock(&pipeList->lock);
    return ret;
}

static uint64_t SvQueryTotalInvalidMsgNum(DbInstanceHdT dbInstance)
{
    DrtInstanceT *drtIns = DrtGetInstance(dbInstance);
    if (drtIns == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get instance when query novalid msg num.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DrtProbeDataT drtProbe = drtIns->connMgr.connProbeData;
    return DrtGetProbeData(&drtProbe, drtIns)->invalidMsgNum;
}

static Status SvQuerySetServiceProbe(const DmVertexT *vertex, SvCursorT *cursor, DbInstanceHdT dbInstance)
{
    ServiceMgrT *serviceMgr = SrvGetServiceMgr();
    ServiceProbeT serviceProbe = serviceMgr->serviceProbe;
    Status ret = GMERR_OK;
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "DML_TOTAL_NUM", serviceProbe.totalDmlTimes);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "DML_FAILED_NUM", serviceProbe.failedDmlTimes);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint64PropeByName(vertex, "PRIVILEGE_OPERATION_TOTAL_TIMES", serviceProbe.totalPrivTimes);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint64PropeByName(vertex, "EXCEED_PRIVILEGE_TIMES", serviceProbe.exceedPrivTimes);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint64PropeByName(vertex, "SPECIFIC_DQL_TOTAL_TIMES", serviceProbe.specificDqlTimes);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint64PropeByName(vertex, "INVALID_RPC_MSG_NUM", SvQueryTotalInvalidMsgNum(dbInstance));
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "REQUEST_NUM", serviceProbe.requestNum);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "REQUEST_SIZE", serviceProbe.requestSize);
    ret =
        (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "BATCH_OPERATION_NUM", serviceProbe.batchOpNum);
    return ret;
}

static Status SvQuerySetRuntimeProbe(void *vertex, SvCursorT *cursor, DbInstanceHdT dbInstance)
{
    DrtInstanceT *drtIns = DrtGetInstance(dbInstance);
    if (drtIns == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get instance when query set runtime probe.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    Status ret = GMERR_OK;
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint64PropeByName(vertex, "HUNG_WORKER_TIMES", drtIns->workerMgr.workerList.hungWorkerTimes);
    ret = (ret != GMERR_OK) ? ret :
                              DmVertexSetUint64PropeByName(vertex, "FLOW_CTRL_LEVEL",
                                  (uint64_t)DrtGetGlobalFlowCtrlLevel(&drtIns->flowCtrlInfo));
    DrtProbeDataT drtProbe = {0};
    ret = (ret != GMERR_OK) ? ret :
                              DmVertexSetUint64PropeByName(vertex, "ABNORMAL_DISCONNECT_TIMES",
                                  DrtGetProbeData(&drtProbe, drtIns)->abnDisconnTimes);
    return ret;
}

inline static Status ConvertUsageToString(char *dstStr, uint32_t strLen, uint64_t usage)
{
    int32_t ret = snprintf_truncated_s(dstStr, strLen, "%.2f%%", (double)usage);
    if (ret < 0) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "convert usage to string unsucc.");
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

static Status SvQuerySetOtherProbe(void *vertex, SvCursorT *cursor, DbInstanceHdT dbInstance)
{
    char cpuUsage[USAGE_STRING_LEN] = {0};
    Status ret = ConvertUsageToString(cpuUsage, USAGE_STRING_LEN, (uint64_t)DbGetAvgCpuUsage());
    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "CPU_USAGE", cpuUsage);
    ret = (ret != GMERR_OK) ? ret :
                              DmVertexSetUint64PropeByName(vertex, "DYNAMIC_MEMORY_USAGE",
                                  DbDynMemCtxGetTreePhySize((DbMemCtxT *)DbGetTopDynMemCtx(dbInstance), true));
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint64PropeByName(vertex, "SHARED_MEMORY_USAGE",
                  DbShmCtxGetTreePhySize(DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, DbGetInstanceId(dbInstance)), false));
    return ret;
}

Status SvQueryProcProbeData(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    DbInstanceHdT dbInstance = DbGetInstanceBySvCursor(cursor);

    uint32_t *instance = (uint32_t *)cursor->rowId;
    if (*instance > 0) {
        return GMERR_NO_DATA;
    }
    Status ret = SvQuerySetServiceProbe(vertex, cursor, dbInstance);

    ret = (ret != GMERR_OK) ? ret : SvQuerySetRuntimeProbe(vertex, cursor, dbInstance);

    ret = (ret != GMERR_OK) ? ret : SvQuerySetOtherProbe(vertex, cursor, dbInstance);

    ++(*instance);
    return ret;
}

static inline bool IsKeyResourceExceed(double keyRecource)
{
    // 比较关键资源使用率是否超过阈值
    return keyRecource >= KEY_RESOURCE_EXCEED_THRESHOLD;
}

static inline double SvDoubleDivide(uint64_t divisor, uint64_t dividends)
{
    return (dividends == 0) ? 0 : ((double)divisor / (double)dividends);
}

Status ConcatWarnWithKeyResource(char *keyRecourceStr, uint64_t keyRecource, bool isExceed)
{
    // 根据是否alarm，拼接资源字段与alarm字符串
    int32_t ret;
    if (isExceed) {
        ret = snprintf_s(
            keyRecourceStr, KEY_RESOURCE_SRT_LEN, KEY_RESOURCE_SRT_LEN - 1, "%" PRIu64 "\t%s", keyRecource, "WARNING");
    } else {
        ret = snprintf_s(keyRecourceStr, KEY_RESOURCE_SRT_LEN, KEY_RESOURCE_SRT_LEN - 1, "%" PRIu64 "", keyRecource);
    }
    if (ret < 0) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW, "Unable to concatenate key resource str.");
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}

void KeyResourceData(KeyResourceT *keyResource, SvCursorT *cursor, DbInstanceHdT dbInstance)
{
    keyResource->usedShmMemSize =
        DbShmCtxGetTreePhySize(DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, DbGetInstanceId(dbInstance)), false);
    keyResource->usedShmMemRate = SvDoubleDivide(keyResource->usedShmMemSize, DbGetMaxShmemSize());

    keyResource->usedDynMemSize = DbGetCurrDynSize();
    keyResource->usedDynMemRate = SvDoubleDivide(keyResource->usedDynMemSize, DbGetMaxDynSize());

    keyResource->cpuUsage = DbGetAvgCpuUsage();

    DrtInstanceT *drtIns = DrtGetInstance(dbInstance);
    if (drtIns == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get instance when query resource data.");
        return;
    }
    keyResource->connUsedNum =
        (uint64_t)drtIns->connMgr.curNum + (uint64_t)drtIns->connMgr.reservedNum + (uint64_t)drtIns->connMgr.useEmrgNum;
    keyResource->connUsedRate = SvDoubleDivide(keyResource->connUsedNum, DbDynArrayGetMaxNum(&drtIns->connMgr.connArr));

    keyResource->workerUsedNum = WorkerListGetTotalNum(&drtIns->workerMgr.workerList);
    keyResource->workerUsedRate = SvDoubleDivide(keyResource->workerUsedNum, drtIns->workerMgr.workerList.maxNum);

    SeLockOverviewStatT lockStat = {0};
    SeGetLockOverView(QryGetSeInstance(cursor->stmt), &lockStat);
    keyResource->lockUsedNum = lockStat.usedLockCnt;
    keyResource->lockUsedRate = SvDoubleDivide(keyResource->lockUsedNum, lockStat.lockCnt);
}

Status SvQueryProcGetTimeStamp(void *vertex, SvCursorT *cursor, DbInstanceHdT dbInstance)
{
    uint64_t nowTime = DbClockGetTsc();
    char time[DB_MAX_NAME_LEN] = {0};
    uint64_t dbStartTime = DbGetServerStartTime(DbGetInstanceId(dbInstance));
    Status ret = DbGetDurationStr(dbStartTime, nowTime, time, DB_MAX_NAME_LEN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Server view get running time string unsucc when get time stamp.");
        return ret;
    }
    return DmVertexSetStrPropeByName(vertex, "EXECUTE_TIME", time);
}

Status SvQueryGetServiceLevel(void *vertex, SvCursorT *cursor)
{
    ServiceMgrT *serviceMgr = SrvGetServiceMgr();
    ServiceImpairmentLevelE serviceLevel = SrvGetServiceImpairmentLevel(&serviceMgr->serviceDetectData);
    char *serviceLevelTable[] = {"NORMAL", "MILD", "GENERAL", "SEVERE", "CRITICAL", "BUTT"};
    char *level = serviceLevelTable[serviceLevel];
    return DmVertexSetStrPropeByName(vertex, "SERVICE_IMPAIRMENT_LEVEL", level);
}

Status SvQueryGetThroughPut(void *vertex, SvCursorT *cursor)
{
    ServiceMgrT *serviceMgr = SrvGetServiceMgr();
    uint64_t throughPut = serviceMgr->serviceDetectData.totalExecNum;
    Status ret = DmVertexSetUint64PropeByName(vertex, "THROUGHPUT", throughPut);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint64PropeByName(vertex, "FAILEDNUM", serviceMgr->serviceDetectData.totalFailNum);
    return ret;
}

Status SvQueryGetKeyRecource(void *vertex, SvCursorT *cursor, DbInstanceHdT dbInstance)
{
    KeyResourceT keyResource = {0};
    KeyResourceData(&keyResource, cursor, dbInstance);
    char usedShmSize[KEY_RESOURCE_SRT_LEN] = {0};
    Status ret = ConcatWarnWithKeyResource(
        usedShmSize, keyResource.usedShmMemSize, IsKeyResourceExceed(keyResource.usedShmMemRate));
    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "SHM_MEM_USAGE_INFO", usedShmSize);

    char usedDynSize[KEY_RESOURCE_SRT_LEN] = {0};
    ret = (ret != GMERR_OK) ? ret :
                              ConcatWarnWithKeyResource(usedDynSize, keyResource.usedDynMemSize,
                                  IsKeyResourceExceed(keyResource.usedDynMemRate));
    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "DYN_MEM_UASGE_INFO", usedDynSize);

    char cpuUsage[KEY_RESOURCE_SRT_LEN] = {0};
    ret = (ret != GMERR_OK) ? ret :
                              ConcatWarnWithKeyResource(cpuUsage, (uint64_t)keyResource.cpuUsage,
                                  IsKeyResourceExceed((double)keyResource.cpuUsage / DB_PERCENTAGE_BASE));
    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "CPU_USAGE_INFO", cpuUsage);

    char usedConn[KEY_RESOURCE_SRT_LEN] = {0};
    ret = (ret != GMERR_OK) ? ret :
                              ConcatWarnWithKeyResource(
                                  usedConn, keyResource.connUsedNum, IsKeyResourceExceed(keyResource.connUsedRate));
    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "CONN_USAGE_INFO", usedConn);

    char usedWorker[KEY_RESOURCE_SRT_LEN] = {0};
    ret = (ret != GMERR_OK) ? ret :
                              ConcatWarnWithKeyResource(usedWorker, keyResource.workerUsedNum,
                                  IsKeyResourceExceed(keyResource.workerUsedRate));
    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "DRT_WORKER_USAGE_INFO", usedWorker);

    char lockUsedNum[KEY_RESOURCE_SRT_LEN] = {0};
    ret = (ret != GMERR_OK) ? ret :
                              ConcatWarnWithKeyResource(
                                  lockUsedNum, keyResource.lockUsedNum, IsKeyResourceExceed(keyResource.lockUsedRate));
    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "TRX_LOCK_USAGE_INFO", lockUsedNum);
    return ret;
}

Status SvQueryProcServiceDetection(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    DbInstanceHdT dbInstance = DbGetInstanceBySvCursor(cursor);
    uint32_t *instance = (uint32_t *)cursor->rowId;
    if (*instance > 0) {
        return GMERR_NO_DATA;
    }

    Status ret = SvQueryGetKeyRecource(vertex, cursor, dbInstance);
    ret = (ret != GMERR_OK) ? ret : SvQueryGetServiceLevel(vertex, cursor);
    ret = (ret != GMERR_OK) ? ret : SvQueryProcGetTimeStamp(vertex, cursor, dbInstance);
    ret = (ret != GMERR_OK) ? ret : SvQueryGetThroughPut(vertex, cursor);

    ++(*instance);

    return ret;
}

static Status SetDirectMsgPoolProperty(const DmVertexT *dmVertex, DirectMsgRingT *directMsgRing)
{
    Status ret = DmVertexSetUint32PropeByName(dmVertex, "MAX_RING_SIZE", directMsgRing->totalNum);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to set ring size.");
        return ret;
    }
    ret = DmVertexSetUint32PropeByName(dmVertex, "RING_USED_SIZE", directMsgRing->usedNum);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to set ring used size.");
        return ret;
    }
    ret = DmVertexSetUint32PropeByName(dmVertex, "CURRENT_POS", directMsgRing->pos);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to set current position.");
    }
    return ret;
}

#define DB_MAX_CTX_STRING_LENGTH 128
Status SvQueryProcRuntimeDirectMsgPoolStat(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    DbInstanceHdT dbInstance = DbGetInstanceBySvCursor(cursor);
    int32_t isOpenDirectWrite = DbCfgGetDirectWriteLite();
    if (isOpenDirectWrite == 0) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_NAME, "direct write config is closed.");
        return GMERR_INVALID_NAME;
    }
    uint32_t *rowId = (uint32_t *)cursor->rowId;
    if (*rowId > 0) {
        return GMERR_NO_DATA;
    }
    (*rowId)++;
    DrtInstanceT *drtInstance = DrtGetInstance(dbInstance);
    if (drtInstance == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to obtain inst when view msg pool.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DirectSharedMsgPoolMgrT *msgPoolMgr = drtInstance->directMsgPoolMgr;
    if (msgPoolMgr == NULL || msgPoolMgr->memCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to obtain direct msg pool ctx.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DirectMsgRingT *msgRing = (DirectMsgRingT *)DbShmPtrToAddr(msgPoolMgr->msgRing);
    if (msgRing == NULL) {
        return GMERR_NO_DATA;
    }
    const DmVertexT *dmVertex = (const DmVertexT *)vertex;
    char *ctxName = msgPoolMgr->memCtx->ctxName;
    Status ret = DmVertexSetStrPropeByName(dmVertex, "CTX_NAME", ctxName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to set ctx name.");
        return ret;
    }
    uint64_t allocSize = DbShmCtxGetTreeAllocSize(msgPoolMgr->memCtx, true);
    char memoryInfo[DB_MAX_CTX_STRING_LENGTH] = {0};
    // 视图场景，发生截断，直接打印截断前部分的信息
    (void)snprintf_truncated_s(memoryInfo, DB_MAX_CTX_STRING_LENGTH,
        "[%" PRIu64 "] MB [%" PRIu64 "] KB [%" PRIu64 "] Byte", CALCULATE_MBYTE(allocSize), CALCULATE_KBYTE(allocSize),
        CALCULATE_BYTE(allocSize));
    ret = DmVertexSetStrPropeByName(dmVertex, "SHMEM_ALLOCSIZE", memoryInfo);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to set memory size.");
        return ret;
    }
    return SetDirectMsgPoolProperty(dmVertex, msgRing);
}

static SvDefT g_gmdbDbViewDef[] = {{"V$DB_SERVER", DB_SERVER, SvQueryProcDbServer, false, false},
    {"V$CONFIG_PARAMETERS", CONFIG_PARAMETERS, SvQueryProcConfigPara, false, false},
    {"V$MEM_COMPACT_TASKS_STAT", MEM_COMPACT_TASKS_STAT, SvQueryProcMemCompactTasksStat, false, false},
    {"V$DB_SERVER_KEY_RESOURCE", DB_SERVER_KEY_RESOURCE, SvQueryProcKeyResourceStat, false, false},
    {"V$DRT_CONN_THREAT_STAT", DRT_CONN_THREAT_STAT, SvQueryProcConnThreatStat, false, false},
    {"V$DB_PROBE_DATA", DB_PROBE_DATA, SvQueryProcProbeData, false, false},
    {"V$DB_SERVICE_DETECTION", DB_SERVICE_DETECTION, SvQueryProcServiceDetection, false, false},
    {"V$DRT_DIRECT_MSG_POOL_STAT", DRT_DIRECT_MSG_POOL_STAT, SvQueryProcRuntimeDirectMsgPoolStat, false, false}};

#ifdef FEATURE_SQL
Status SvDbViewRegist(SvInstanceT *viewInst)
{
    if (!DbIsMultiInstanceEnabled()) {
        return SvRegistViews(viewInst, ELEMENT_COUNT(g_gmdbDbViewDef), g_gmdbDbViewDef);
    }
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(viewInst->memCtx);
    if (dbInstance == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "dbInstance for init g_gmdbDbViewDef is null.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DbInstanceT *dbIns = (DbInstanceT *)dbInstance;
    return SvRegistViewsMultiIns(
        viewInst, (SvDefT **)&(dbIns->embDbViewDef), g_gmdbDbViewDef, ELEMENT_COUNT(g_gmdbDbViewDef));
}
#else
Status SvDbViewRegist(SvInstanceT *viewInst)
{
    return SvRegistViews(viewInst, ELEMENT_COUNT(g_gmdbDbViewDef), g_gmdbDbViewDef);
}
#endif

#ifdef __cplusplus
}
#endif /* __cplusplus */
