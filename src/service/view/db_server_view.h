/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * File Name: db_server_view.h
 * Description: header file for server view.
 * Author:
 * Create: 2021.07.30
 */

#ifndef DB_SERVER_VIEW_H
#define DB_SERVER_VIEW_H

#include "sysview.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#ifdef FEATURE_RSMEM
#define DB_SERVER_RSMEM \
    "\
        ,\
        {\"name\":\"WARM_REBOOT\",\"type\":\"boolean\"},\
        {\"name\":\"USE_RSM\",\"type\":\"boolean\"},\
        {\"name\":\"RECOVERY_TIME\",\"type\":\"uint64\"}\
        "
#else
#define DB_SERVER_RSMEM
#endif

#define DB_SERVER \
    "[{\
        \"type\":\"record\",\
        \"name\":\"V$DB_SERVER\",\
        \"fields\":[\
            {\"name\":\"SERVER_VERSION\",\"type\":\"string\",\"size\":32},\
            {\"name\":\"BUILD_MODE\",\"type\":\"string\",\"size\":8},\
            {\"name\":\"INITIATE_DELAY\",\"type\":\"uint64\"},\
            {\"name\":\"START_TIME\",\"type\":\"string\",\"size\":32},\
            {\"name\":\"RUNNING_TIME\",\"type\":\"string\",\"size\":32},\
            {\"name\":\"CPU_CYCLE_PER_USEC\",\"type\":\"uint64\"},\
            {\"name\":\"CPU_USAGE\",\"type\":\"uint16\"}\
            " DB_SERVER_RSMEM "\
        ]\
    }]"

#define CONFIG_PARAMETERS \
    "[{\
        \"type\":\"record\",\
        \"name\":\"V$CONFIG_PARAMETERS\",\
        \"fields\":[\
            {\"name\":\"NAME\",\"type\":\"string\",\"size\":256},\
            {\"name\":\"VALUE\",\"type\":\"string\",\"size\":256},\
            {\"name\":\"TYPE\",\"type\":\"string\",\"size\":8},\
            {\"name\":\"MIN\",\"type\":\"int32\"},\
            {\"name\":\"MAX\",\"type\":\"int32\"},\
            {\"name\":\"DEFAULT_VALUE\",\"type\":\"string\",\"size\":256},\
            {\"name\":\"CHANGE_MODE\",\"type\":\"string\",\"size\":32},\
            {\"name\":\"DESCRIPTION\",\"type\":\"string\",\"size\":512}\
        ]\
    }]"

#define MEM_COMPACT_TASKS_STAT \
    "[{\
        \"type\":\"record\",\
        \"name\":\"V$MEM_COMPACT_TASKS_STAT\",\
        \"fields\":[\
            {\"name\":\"TASK_TYPE\",\"type\":\"uint32\"},\
            {\"name\":\"TASK_STATUS\",\"type\":\"string\",\"size\":16},\
            {\"name\":\"SUB_TASK_STATUS\",\"type\":\"string\",\"size\":16},\
            {\"name\":\"CREATE_COUNT\",\"type\":\"uint32\"},\
            {\"name\":\"DESTROY_COUNT\",\"type\":\"uint32\"},\
            {\"name\":\"SCHEDULE_COUNT\",\"type\":\"uint32\"},\
            {\"name\":\"DURATION\",\"type\":\"uint64\"},\
            {\"name\":\"EXEC_TIME_AVG\",\"type\":\"uint64\"},\
            {\"name\":\"EXEC_TIME_MIN\",\"type\":\"uint64\"},\
            {\"name\":\"EXEC_TIME_MAX\",\"type\":\"uint64\"}\
        ]\
    }]"

#define DB_SERVER_KEY_RESOURCE \
    "[{\
        \"type\": \"record\",\
        \"name\": \"V$DB_SERVER_KEY_RESOURCE\",\
        \"fields\": [\
            {\"name\": \"TOTAL_SHM_MEM_SIZE\",\"type\": \"uint64\"},\
            {\"name\": \"TOTAL_USED_SHM_MEM_SIZE\",\"type\": \"uint64\"},\
            {\"name\": \"TOTAL_DYN_MEM_SIZE\",\"type\": \"uint64\"},\
            {\"name\": \"TOTAL_USED_DYN_MEM_SIZE\",\"type\": \"uint64\"},\
            {\"name\": \"CPU_USAGE\",\"type\":\"uint16\"},\
            {\"name\": \"SUB_NODE_PILED_SIZE\",\"type\": \"uint32\"},\
            {\"name\": \"SUB_MSG_SHM_MEM_SIZE\",\"type\": \"uint64\"},\
            {\"name\": \"TOTAL_CONN_NUM\",\"type\": \"uint32\"},\
            {\"name\": \"TOTAL_USED_NORMAL_CONN_NUM\",\"type\": \"uint16\"},\
            {\"name\": \"TOTAL_RESERVED_CONN_NUM\",\"type\": \"uint16\"},\
            {\"name\": \"TOTAL_USED_RESERVED_CONN_NUM\",\"type\": \"uint16\"},\
            {\"name\": \"TOTAL_EMER_CONN_NUM\",\"type\": \"uint16\"},\
            {\"name\": \"TOTAL_USED_EMER_CONN_NUM\",\"type\": \"uint16\"},\
            {\"name\": \"DRT_WORKER_MAX_NUM\",\"type\": \"uint16\"},\
            {\"name\": \"DRT_WORKER_USED_NUM\",\"type\": \"uint16\"},\
            {\"name\": \"TOTAL_USED_STMT_NUM\",\"type\": \"uint32\"},\
            {\"name\": \"SE_TOP_SHM_MEM_SIZE\",\"type\": \"uint64\"},\
            {\"name\": \"SE_DEV_SHM_MEM_SIZE\",\"type\": \"uint64\"},\
            {\"name\": \"TRX_LOCK_NUM\",\"type\": \"uint32\"},\
            {\"name\": \"TRX_LOCK_USED_NUM\",\"type\": \"uint32\"},\
            {\"name\": \"CATALOG_MEMCTX\",\"type\": \"uint64\"},\
            {\"name\": \"RESERVED_CONN_INFO\",\"type\":\"record\",\"fixed_array\":true,\"size\":5,\
                \"fields\":[\
                    {\"name\":\"USER_ID\",\"type\":\"uint32\"},\
                    {\"name\":\"USER_NAME\",\"type\":\"string\",\"size\":128},\
                    {\"name\":\"PROCESS_NAME\",\"type\":\"string\",\"size\":16},\
                    {\"name\":\"RESERVED_CONN_NUM\",\"type\":\"uint32\"},\
                    {\"name\":\"USED_RESERVED_CONN_NUM\",\"type\":\"uint32\"}\
                ]\
            }\
        ]\
    }]"

#define DRT_CONN_THREAT_STAT \
    "[{\
        \"type\": \"record\",\
        \"name\": \"V$DRT_CONN_THREAT_STAT\",\
        \"fields\": [\
            {\"name\": \"CONN_ID\",\"type\": \"uint16\"},\
            {\"name\": \"CYCLE_COUNT\",\"type\": \"uint32\"},\
            {\"name\": \"CURRENT_REQUEST_COUNT\",\"type\": \"uint32\"},\
            {\"name\": \"CURRENT_BIG_PACK_COUNT\",\"type\": \"uint32\"},\
            {\"name\": \"CURRENT_TOTAL_REQUEST_SIZE\",\"type\": \"uint32\"},\
            {\"name\": \"MAX_REQUEST_COUNT\",\"type\": \"uint32\"},\
            {\"name\": \"MAX_BIG_PACK_COUNT\",\"type\": \"uint32\"},\
            {\"name\": \"MAX_TOTAL_REQUEST_SIZE\",\"type\": \"uint32\"},\
            {\"name\": \"AVERAGE_REQUEST_COUNT\",\"type\": \"uint32\"},\
            {\"name\": \"AVERAGE_BIG_PACK_COUNT\",\"type\": \"uint32\"},\
            {\"name\": \"AVERAGE_TOTAL_REQUEST_SIZE\",\"type\": \"uint32\"},\
            {\"name\": \"CONN_CYCLE_STAT\",\"type\":\"record\",\"fixed_array\":true,\"size\":12,\
                \"fields\":[\
                    {\"name\":\"CYCLE_ID\",\"type\":\"uint8\"},\
                    {\"name\":\"REQUEST_COUNT\",\"type\":\"uint32\"},\
                    {\"name\":\"BIG_PACK_COUNT\",\"type\":\"uint32\"},\
                    {\"name\":\"TOTAL_REQUEST_SIZE\",\"type\":\"uint32\"}\
                ]\
            }\
        ]\
    }]"

#define DB_PROBE_DATA \
    "[{\
        \"type\":\"record\",\
        \"name\":\"V$DB_PROBE_DATA\",\
        \"fields\":[\
            {\"name\":\"ABNORMAL_DISCONNECT_TIMES\",\"type\":\"uint64\"},\
            {\"name\":\"REQUEST_NUM\",\"type\":\"uint64\"},\
            {\"name\":\"REQUEST_SIZE\",\"type\":\"uint64\"},\
            {\"name\":\"FLOW_CTRL_LEVEL\",\"type\":\"uint64\"},\
            {\"name\":\"HUNG_WORKER_TIMES\",\"type\":\"uint64\"},\
            {\"name\":\"DML_TOTAL_NUM\",\"type\":\"uint64\"},\
            {\"name\":\"DML_FAILED_NUM\",\"type\":\"uint64\"},\
            {\"name\":\"BATCH_OPERATION_NUM\",\"type\":\"uint64\"},\
            {\"name\":\"PRIVILEGE_OPERATION_TOTAL_TIMES\",\"type\":\"uint64\"},\
            {\"name\":\"EXCEED_PRIVILEGE_TIMES\",\"type\":\"uint64\"},\
            {\"name\":\"SPECIFIC_DQL_TOTAL_TIMES\",\"type\":\"uint64\"},\
            {\"name\":\"INVALID_RPC_MSG_NUM\",\"type\":\"uint64\"},\
            {\"name\":\"CPU_USAGE\",\"type\":\"string\",\"size\":16},\
            {\"name\":\"SHARED_MEMORY_USAGE\",\"type\":\"uint64\"},\
            {\"name\":\"DYNAMIC_MEMORY_USAGE\",\"type\":\"uint64\"}\
        ]\
    }]"

#define DB_SERVICE_DETECTION \
    "[{\
        \"type\":\"record\",\
        \"name\":\"V$DB_SERVICE_DETECTION\",\
        \"fields\":[\
            {\"name\":\"EXECUTE_TIME\",\"type\":\"string\",\"size\":16},\
            {\"name\":\"SERVICE_IMPAIRMENT_LEVEL\",\"type\":\"string\",\"size\":128},\
            {\"name\":\"THROUGHPUT\",\"type\":\"uint64\"},\
            {\"name\":\"FAILEDNUM\",\"type\":\"uint64\"},\
            {\"name\":\"SHM_MEM_USAGE_INFO\",\"type\":\"string\",\"size\":128},\
            {\"name\":\"DYN_MEM_UASGE_INFO\",\"type\":\"string\",\"size\":128},\
            {\"name\":\"CPU_USAGE_INFO\",\"type\":\"string\",\"size\":128},\
            {\"name\":\"CONN_USAGE_INFO\",\"type\":\"string\",\"size\":128},\
            {\"name\":\"DRT_WORKER_USAGE_INFO\",\"type\":\"string\",\"size\":128},\
            {\"name\":\"TRX_LOCK_USAGE_INFO\",\"type\":\"string\",\"size\":128}\
        ]\
    }]"

#define DRT_DIRECT_MSG_POOL_STAT \
    "[{\
        \"type\":\"record\",\
        \"name\":\"V$DRT_DIRECT_MSG_POOL_STAT\",\
        \"fields\":[\
            {\"name\":\"CTX_NAME\",\"type\":\"string\",\"size\":128},\
            {\"name\":\"SHMEM_ALLOCSIZE\",\"type\":\"string\", \"size\":128},\
            {\"name\":\"MAX_RING_SIZE\",\"type\":\"uint32\"},\
            {\"name\":\"RING_USED_SIZE\",\"type\":\"uint32\"},\
            {\"name\":\"CURRENT_POS\",\"type\":\"uint32\"}\
        ]\
    }]"

Status SvQueryProcDbServer(void *vertex, SvCursorT *cursor);
Status SvQueryProcConfigPara(void *vertex, SvCursorT *cursor);
Status SvQueryProcMemCompactTasksStat(void *vertex, SvCursorT *cursor);
Status SvQueryProcKeyResourceStat(void *vertex, SvCursorT *cursor);
Status SvQueryProcConnThreatStat(void *vertex, SvCursorT *cursor);
Status SvQueryProcProbeData(void *vertex, SvCursorT *cursor);
Status SvQueryProcServiceDetection(void *vertex, SvCursorT *cursor);
Status SvDbViewRegist(SvInstanceT *viewInst);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* DB_SERVER_VIEW_H */
