/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author:
 * Create: 2023-06-13
 */
#include "srv_data_public.h"
#include "gmc_errno.h"
#include "adpt_types.h"
#include "srv_data_service.h"
#include "ee_session_interface.h"
#include "ee_session.h"
#include "ee_stmt.h"
#include "ptl_service_utils.h"
#include "srv_data_public_prepare.h"
#include "srv_data_log.h"
#include "srv_data_alarm.h"
#include "srv_data_statistics.h"
#include "srv_data_base_proc.h"
#include "db_sysapp_context.h"
#include "db_dead_loop.h"
#include "db_mem_context_pool.h"
#include "ee_schedule.h"
#include "ee_command.h"
#include "ee_dcl_desc.h"
#include "srv_data_public_log.h"
#include "cpl_public_parser.h"
#include "cpl_public_parser_ddl.h"
#include "sysview_instance.h"
#include "qry_sysview.h"
#ifdef SHUTDOWN
#include "db_server.h"
#endif
#include "db_instance.h"

#define DEAD_LOOP_CHECK_PERIOD 1000

#ifndef FEATURE_SIMPLEREL
static inline bool RequestTimeout(const SessionT *session)
{
    if (session->reqTimeOut == CS_NEVER_TIMEOUT) {
        return false;
    }

    return session->reqTimeOut < (int64_t)DbToUseconds(DbRdtsc() - session->reqStartTime);
}

static bool NeedAuditLog(uint32_t opCode)
{
    if (DbCfgGetBoolLite(DB_CFG_AUDIT_LOG_DCL_ENABLE, NULL) && opCode != MSG_OP_RPC_GET_VERTEX_LABEL &&
        opCode != MSG_OP_RPC_GET_LABEL && opCode != MSG_OP_RPC_GET_ALARM_DATA &&
        opCode != MSG_OP_RPC_MODIFY_ALARM_THRESHOLD) {
        return true;
    }
    return false;
}

Status PublicPrepareProcess(QryStmtT *stmt, uint32_t opCode)
{
    DB_POINTER(stmt);
    stmt->phase = QRY_PHASE_PREPARE;
    Status ret = PublicPrepare(stmt, opCode);
    if (SECUREC_UNLIKELY(stmt->context == NULL)) {
        return ret;
    }
    if (NeedAuditLog(opCode)) {
        PublicAuditLog(stmt, ret);
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        SrvDataRecordLongStat(stmt, true);
    }
    return ret;
}

Status PublicExecution(QryStmtT *stmt, uint32_t opCode, LongOperationInfoT *longInfo)
{
    DB_POINTER(stmt);
    stmt->phase = QRY_PHASE_EXECUTE;
    if (stmt->session->isLongTrxRollBack && opCode != MSG_OP_RPC_TX_ROLLBACK) {
        // 显式事务中被动回滚的事务需要客户端调用回滚，其他非自调度任务都报错
        // 隐式事务isLongTrxRollBack永远为false
        DB_LOG_AND_SET_LASERR(GMERR_TRANSACTION_ROLLBACK, "It needs rollback before %s.", DbGetOpCodeMsg(opCode));
        return GMERR_TRANSACTION_ROLLBACK;
    }
    Status ret = SrvDataCheckStmtStatus(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (longInfo != NULL || !stmt->splitInfo.isFirst) {
        stmt->beginExecuteTime = DbRdtsc();
    }
    if (stmt->splitInfo.isFirst) {
        QryReleaseExecData(stmt);
    }
    if (SECUREC_LIKELY(ret == GMERR_OK)) {
        ret = ExecCommand(stmt);
    }
    if (SECUREC_LIKELY(ret == GMERR_OK)) {
        // 在(1)longInfo不为NULL，(2)是CS连接，(3)是DML操作的情况下，记录慢操作日志
        if (longInfo != NULL && stmt->session->sesType != SESSION_TYPE_NO_CONN && QryTypeIsDML(stmt->context->type)) {
            SrvDataRecordLongLog(stmt, opCode, longInfo);
        }
    }
#ifdef SHUTDOWN
    if ((ret == GMERR_OK) && (opCode == MSG_OP_RPC_SHUTDOWN_SERVER)) {
        DbProcessStop();
    }
#endif
    if (stmt->splitInfo.isLast) {
        FastpathStat4Execute(stmt, ret);
        if (NeedAuditLog(opCode)) {
            PublicAuditLog(stmt, ret);
        }
        if (stmt->splitInfo.batch.batchOrder == 0) {  // ORDER_STRICT
            PublicUpdateDataSpaceAlarmData(stmt->context->type, ret, 1);
        } else {  // ORDER_SEMI
            PublicUpdateDataSpaceAlarmData(stmt->context->type, ret, stmt->splitInfo.batch.totalOpNum);
        }
        if (SECUREC_UNLIKELY(
                BigObject(QryGetSessionPool(DbGetInstanceByMemCtx(stmt->session->memCtx)), stmt->session->rsp->pos))) {
            QrySessionPoolStaticMemPeak(stmt->session, SESSION_MEM_TYPE_BIG_OBJECT);
        }
    }
    bool needCount = (stmt->splitInfo.isLast || ret != GMERR_OK);
    SrvDataRecordLongStat(stmt, needCount);
    return ret;
}
static Status PublicProcessRequestInner(QryStmtT *stmt, uint32_t opCode, LongOperationInfoT *longInfo)
{
    Status ret = PublicPrepareProcess(stmt, opCode);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        if (stmt->context != NULL) {
            // prepare阶段也会申请页(比如索引的目录页的申请)，需要统计失败情况下的次数
            PublicUpdateDataSpaceAlarmData(stmt->context->type, ret, 1);
        }
        return ret;
    }
    return PublicExecution(stmt, opCode, longInfo);
}

static bool IsPublicCommand(uint32_t opCode)
{
    if (opCode < MSG_OP_RPC_DCL_END && opCode >= MSG_OP_RPC_DCL_BEGIN) {
        return true;
    }
    switch (opCode) {
        case MSG_OP_RPC_GET_VERTEX_LABEL:
        case MSG_OP_RPC_MODIFY_ALARM_THRESHOLD:
        case MSG_OP_RPC_GET_ALARM_DATA:
        case MSG_OP_RPC_GET_LABEL:
        case MSG_OP_RPC_CREATE_SUBSCRIPTION:
        case MSG_OP_RPC_DROP_SUBSCRIPTION:
        case MSG_OP_RPC_CREATE_USER:
        case MSG_OP_RPC_CREATE_GROUP:
        case MSG_OP_RPC_DROP_USER:
        case MSG_OP_RPC_DROP_GROUP:
            return true;
        default:
            return false;
    }
    return false;
}

Status PublicProcessCommand(QryStmtT *stmt, uint32_t opCode, LongOperationInfoT *longInfo)
{
    if (SECUREC_UNLIKELY(!IsPublicCommand(opCode))) {
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "process command: opCode = %" PRIu32 ".", opCode);
        return GMERR_DATATYPE_MISMATCH;
    }
    if (stmt->session->isLongTrxRollBack && opCode != MSG_OP_RPC_TX_ROLLBACK) {
        // 显式事务中被动回滚的事务需要客户端调用回滚，其他非自调度任务都报错
        // 隐式事务isLongTrxRollBack永远为false
        DB_ASSERT(!stmt->autoCommit);
        DB_LOG_AND_SET_LASERR(GMERR_TRANSACTION_ROLLBACK, "It needs rollback before %s.", DbGetOpCodeMsg(opCode));
        return GMERR_TRANSACTION_ROLLBACK;
    }
    stmt->lastStatTime = stmt->beginPrepareTime = DbRdtsc();
    stmt->totalTime = 0;

    return PublicProcessRequestInner(stmt, opCode, longInfo);
}
// in this request, no MsgHeaderT exists
Status PublicProcessRequest(SessionT *session, uint32_t opCode, MsgHeaderT *msgHeader, LongOperationInfoT *longInfo)
{
    DB_POINTER2(session, msgHeader);
    if (SECUREC_UNLIKELY(longInfo != NULL)) {
        longInfo->longestQryInfo.namespaceId = session->namespaceId;
    }
    // judge whether req has been timeout
    if (SECUREC_UNLIKELY(RequestTimeout(session))) {
        DB_LOG_AND_SET_LASERR(GMERR_REQUEST_TIME_OUT, "check time.");
        return GMERR_REQUEST_TIME_OUT;
    }
    Status opStatus = QryGetStmtById(session, msgHeader->stmtId, &session->currentStmt);
    if (SECUREC_UNLIKELY(opStatus != GMERR_OK)) {
        return opStatus;
    }

    opStatus = PtlPrivVerifyServerMode(session->conn);
    if (SECUREC_UNLIKELY(opStatus != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(opStatus, "Check server mode.");
        return opStatus;
    }

    QryStmtT *stmt = session->currentStmt;
    uint32_t flags = msgHeader->flags;
    QrySetStmtExecInfo(stmt, flags);
    QryClearInvalidSubsCache(session);

    return PublicProcessCommand(stmt, opCode, longInfo);
}

Status PublicServiceEntry(DrtServiceCtxT *serviceCtx, DrtProcCtxT *procCtx)
{
    DB_POINTER5(serviceCtx, procCtx, procCtx->msgHeader, procCtx->conn, procCtx->conn->session);
    FixBufferT *request = &procCtx->msg;
    MsgHeaderT *msgHeader = procCtx->msgHeader;
    // 该处暂时默认一条消息，需要改动service.entry结构
    uint32_t opCode = ProtocolPeekFirstOpHeader(request)->opCode;
    CliOpInfoT cliOpInfo = {.pid = procCtx->conn->pid, .tid = procCtx->conn->peerTid, .opCode = opCode};
    DbPrintServiceEntryThreadRunTime("begin", "PublicServiceEntry", &cliOpInfo, procCtx->conn->peerThreadName);
    SessionT *session = (SessionT *)procCtx->conn->session;
    session->procCtx = procCtx;

    // for conn from DRT, SHOULD do this routine by the session-self
    session->req = request;

    // skip the head for most process
    RpcSeekFirstOpMsg(request);

    // set request startTime and timeout
    session->reqStartTime = msgHeader->reqStartTime;
    session->reqTimeOut = msgHeader->reqTimeOut;
    session->cltTimeoutMs = QrySessionGetServerTimeout(session);

    // 每一次session开始处理新请求，对应的过载信息归0
    QrySessionInitSubsOverLoad(session);
    session->lastStmtReqTimestamp = serviceCtx->procStartTime;

    SrvDataInitResponse(session, session->rsp, msgHeader->flags);
    session->currentStmt = NULL;
    DbSetIsPrintLog(false, opCode);
    uint32_t oldSeekPos = FixBufGetSeekPos(session->req);
    Status opStatus = PublicProcessRequest(session, opCode, msgHeader, &procCtx->longOperationRecord);
    DbSetIsPrintLog(true, opCode);
    if (DbIsCreateTableOnDemandWithOpStatus(opStatus, opCode)) {
        // 按需建表流程（错误码统一为opStatus）
        Status ret = DbCreateTableOnDemand(
            session->currentStmt, session->currentStmt->context->labelName, session->namespaceId, false, opStatus);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Create tables on demand, opCode: %" PRId32 "", opCode);
            goto EXIT;
        }
        // 重试请求
        FixBufSeek(session->req, oldSeekPos);
        FixBufRelease(session->rsp);
        SrvDataInitResponse(session, session->rsp, msgHeader->flags);
        opStatus = PublicProcessRequest(session, opCode, msgHeader, &procCtx->longOperationRecord);
    }
EXIT:
    DbPrintServiceEntryThreadRunTime("end", "PublicServiceEntry", &cliOpInfo, procCtx->conn->peerThreadName);
    Status ret = SrvDataSetLastErr(opStatus, session);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 此处对重调度情况没有明确传参，要求内部判断rsp是否为空后才能对rsp进行操作
    ret = SrvDataSendResponse(session, (int32_t)opStatus, opCode);
    QryClearInvalidSubsCache(session);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "send response, opcode: %" PRIu32 ", opstatus: %" PRId32 "", opCode, (int32_t)opStatus);
    }
    return opStatus;
}
#endif /* FEATURE_SIMPLEREL */

static Status QryInitUserConnCacheAndThreatStat(DbInstanceHdT instance)
{
#ifndef FEATURE_SIMPLEREL
    Status ret = QryInitUserConnCache(instance);
    if (ret != GMERR_OK) {
        return ret;
    }
    return QryInitThreatStat(instance);
#else
    return GMERR_OK;
#endif /* FEATURE_SIMPLEREL */
}

static inline Status InitSessionPoolAndUserStat(DbInstanceHdT instance)
{
    Status ret = QryInitSessionPool(instance);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 单进程多实例开启时，UserConnCache和ThreatStat在全局实例初始化处进行初始化
    if (!DbIsMultiInstanceEnabled()) {
        return QryInitUserConnCacheAndThreatStat(instance);
    }
    return ret;
}

static Status LongTrxLog(void *ctx, DbDataT userData, DbDataT runData)
{
    DrtInstanceT *drtIns = DrtGetInstance(DbGetInstanceByMemCtx((DbMemCtxT *)ctx));
    if (drtIns == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_NULL_VALUE_NOT_ALLOWED, "Get DRT instance when LongTrxLog, connId=%" PRIu32 ".", userData.u32);
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    DrtConnectionT *conn = DrtAttachConnById(&drtIns->connMgr, (uint16_t)userData.u32);
    if (conn == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_NULL_VALUE_NOT_ALLOWED, "Get connection by connId=%" PRIu32 " when LongTrxLog.", userData.u32);
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    (void)DbAtomicInc(&g_gmdbLongTrxLogCount);

    // 定时器回调函数里调用，不能访问session
    DB_LOG_WARN(GMERR_ACTIVE_TRANSACTION,
        "Long Transaction %" PRIu64 " runs timeout but still active, connId=%" PRIu32 ".", runData.u64, userData.u32);
    DrtDetachConnection(conn);
    return GMERR_OK;
}

static Status LongTrxRollBack(void *ctx, DbDataT userData, DbDataT runData)
{
    return QryAddLongTrxRollBackTask(userData.u32, runData.u64);
}

static Status InitTrxMonitor(DbInstanceHdT instance)
{
    if (!DbCfgGetBoolLite(DB_CFG_TRX_MONITOR_ENABLE, instance)) {
        DB_LOG_INFO("Transaction Monitor not enable.\n");
        return GMERR_OK;
    }

    DbCfgValueT cfgValue;
    uint32_t value[TRX_MONITOR_THRESHOLD_NUM];
    // 长事务monitor时间阈值，unit：秒
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(instance);
    Status ret = DbCfgGet(cfgHandle, DB_CFG_TRX_MONITOR_THRESHOLD, &cfgValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get transaction monitor threshold config.");
        return ret;
    }
    ret = CfgParamThresholdGetValue(
        cfgValue.str, (uint32_t)strlen(cfgValue.str), value, TRX_MONITOR_THRESHOLD_NUM, TRX_MONITOR_THRESHOLD_INFO);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get transaction monitor threshold value.");
        return ret;
    }
    // 长事务monitor时间阈值的unit是s, 其值在config有限制，不会溢出
    uint32_t trxMonLogThreshold = value[0];
    uint32_t trxMonRollBackThreshold = value[1];

    // 长事务注册monitor
    DbMemCtxArgsT args = {0};
    DbMemCtxT *sysDynCtx = DbSrvGetSysDynCtx(DbGetInstanceId(instance));
    if (sysDynCtx == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "get sys dyn memctx.");
        return GMERR_INTERNAL_ERROR;
    }
    DbMemCtxT *trxMonMemCtx = DbCreateDynMemCtx(sysDynCtx, true, "trx monitor dynamic memory", &args);
    if (trxMonMemCtx == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "create trx monitor dynamic memory.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    DbDeadLoopRegParaT para = {0};
    para.memctx = trxMonMemCtx;
    para.maxNum = QryGetSessionPool(instance)->maxSessions;
    para.period = DEAD_LOOP_CHECK_PERIOD;
    para.needTimer = true;

    DbDeadLoopSetRegPara(
        &para, DEAD_LOOP_HUNG_LEVEL_1, trxMonLogThreshold, LongTrxLog, DEAD_LOOP_FAILED_RETRY_DEFAULT_PERIOD);
    DbDeadLoopSetRegPara(
        &para, DEAD_LOOP_HUNG_LEVEL_2, trxMonRollBackThreshold, LongTrxRollBack, DEAD_LOOP_FAILED_RETRY_DEFAULT_PERIOD);
    ret = DbDeadLoopRegist(DEAD_LOOP_TRX, &para);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "regist transaction monitor.");
    }
    return ret;
}

static Status InitTrxMgrCheckFunc(DbInstanceHdT dbInstance)
{
    DrtInstanceT *drtInstance = DrtGetInstance(dbInstance);
    if (drtInstance == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Get DRT instance when UnInit.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    OptiTrxSetLabelLastTrxIdAndTrxCommitTime setFunc[(uint32_t)TRX_CHECK_READVIEW_NUM] = {
        CataSetEdgeLabelTrxIdAndTrxCommitTimeById, CataSetKvLabelTrxIdAndTrxCommitTimeById,
        CataSetVertexLabelTrxIdAndTrxCommitTimeById};
    OptiTrxGetLabelLastTrxIdAndTrxCommitTime getFunc[(uint32_t)TRX_CHECK_READVIEW_NUM] = {
        CataGetEdgeLabelTrxIdAndTrxCommitTimeById, CataGetKvLabelTrxIdAndTrxCommitTimeById,
        CataGetVertexLabelTrxIdAndTrxCommitTimeById};
    OptiTrxGetLabelName getLabelName[(uint32_t)TRX_CHECK_READVIEW_NUM] = {
        CataGetLabelNameByEdgeLabelId, CataGetLabelNameByKvLabelId, CataGetLabelNameByVertexLabelId};
    return SeInitTrxMgrCheckFunc((uint16_t)drtInstance->instanceId, setFunc, getFunc, getLabelName);
}

#ifndef FEATURE_SIMPLEREL
void SvServiceInit(DbInstanceHdT dbInstance)
{
    SvInstanceT *svInst = DbSvGetSvInstance(dbInstance);
    svInst->vertexLabelCreate = SysViewCreate;
    svInst->vertexLabelPaser = QryParseCreateSysview;
    svInst->vertexLabelRelease = SysViewRelease;
}
#endif /* FEATURE_SIMPLEREL */

Status PublicServiceInitGlobalPart(DbInstanceHdT instance)
{
    Status ret = SrvInitServiceMgr(instance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Server manager init in global start!");
        return ret;
    }
#ifndef FEATURE_SIMPLEREL
    ret = PublicRegisterAlarmTask();
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "register alarm timer.");
        return ret;
    }
#endif /* FEATURE_SIMPLEREL */
    ret = QryInitUserConnCache(instance);
    if (ret != GMERR_OK) {
        return ret;
    }

#if defined(FEATURE_TS) && !defined(FEATURE_STREAM)
    if (DbCfgIsTsInstance()) {
        return GMERR_OK;
    }
#endif

#ifndef FEATURE_SIMPLEREL
    ret = QryInitThreatStat(instance);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = QryCreateSubsEventConfigMap();
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "ConfigVar init");
        return ret;
    }
#endif /* FEATURE_SIMPLEREL */
    return ret;
}

Status PublicServiceInit(DbInstanceHdT instance)
{
    DbMemCtxT *appDynCtx = DbSrvGetAppDynCtx(DbGetInstanceId(instance));
    if (appDynCtx == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "get app dyn memctx.");
        return GMERR_INTERNAL_ERROR;
    }
    DbMemCtxPoolT *memCtxPool = DbCreateDynMemCtxPool(false, appDynCtx);
    if (memCtxPool == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "create dynamic memctx pool when qry memctx pool.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    QrySetMemCtxPool(instance, memCtxPool);
    Status ret;
#ifndef FEATURE_SIMPLEREL
    if (!DbIsMultiInstanceEnabled()) {
        if ((ret = PublicRegisterAlarmTask()) != GMERR_OK) {
            DB_LOG_ERROR(ret, "register alarm timer.");
            return ret;
        }
        if ((ret = QryCreateSubsEventConfigMap()) != GMERR_OK) {
            DB_LOG_ERROR(ret, "ConfigVar init");
            return ret;
        }
    }
#endif /* FEATURE_SIMPLEREL */
    if ((ret = InitSessionPoolAndUserStat(instance)) != GMERR_OK) {
        return ret;
    }
    if ((ret = InitTrxMonitor(instance)) != GMERR_OK) {
        return ret;
    }
    if ((ret = InitTrxMgrCheckFunc(instance)) != GMERR_OK) {
        return ret;
    }
#ifndef FEATURE_SIMPLEREL
    ret = ModelServiceRegister(DataServiceMgrGet(), PublicServiceEntry, MODEL_PUBLIC);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "register service entry function PublicServiceEntry.");
        return ret;
    }
    SvServiceInit(instance);
    ret = SysViewRegist(DbSvGetSvInstance(instance));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "register fastpath system view.");
    }
#endif /* FEATURE_SIMPLEREL */
    return ret;
}

void PublicServiceUnInit(DbInstanceHdT dbInstance)
{
#ifndef FEATURE_SIMPLEREL
    if (!DbIsMultiInstanceEnabled()) {
        QryUnInitUserConnCache();
        QryUninitUserThreatStat();
        (void)PublicUnregisterAlarmTask();
    }
#endif /* FEATURE_SIMPLEREL */
    DrtInstanceT *drtInstance = DrtGetInstance(dbInstance);
    if (drtInstance == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Get DRT instance when UnInit.");
    } else {
        (void)SeLockResourceDestroy((uint16_t)drtInstance->instanceId);
    }
    if (DbCfgGetBoolLite(DB_CFG_TRX_MONITOR_ENABLE, dbInstance)) {
        DbDeadLoopUnRegist(DEAD_LOOP_TRX);
    }
}
