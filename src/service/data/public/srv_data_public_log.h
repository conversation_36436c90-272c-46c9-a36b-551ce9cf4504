/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author:
 * Create: 2023-06-15
 */
#ifndef GMDBV5_SRV_DATA_PUBLIC_LOG_H
#define GMDBV5_SRV_DATA_PUBLIC_LOG_H
#include "srv_data_log.h"
#include "ee_context.h"

#ifdef __cplusplus
extern "C" {
#endif

void GetPublicDescByType(QryTypeE type, BuildAuditLogDescT *buildAuditLogDesc);
void PublicAuditLog(QryStmtT *stmt, Status status);

#ifdef __cplusplus
}
#endif

#endif  // GMDBV5_SRV_DATA_PUBLIC_LOG_H
