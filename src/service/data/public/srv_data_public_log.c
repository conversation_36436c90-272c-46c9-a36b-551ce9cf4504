/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author:
 * Create: 2023-06-15
 */
#include "srv_data_public_log.h"
#include "ee_dcl_desc.h"
#include "ee_ddl_desc.h"

static Status QryBuildCreateSubscriptionAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryCreateSubscriptionDescT *desc = (QryCreateSubscriptionDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: SUBSCRIPTION name: \"%s\" ",
        desc->subscription->metaCommon.namespaceId, desc->subscription->metaCommon.metaName);
}

static Status QryBuildCreateSubscriptionAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryCreateSubscriptionDescT *desc = (QryCreateSubscriptionDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "schema: \"%s\" ", desc->subscription->subsJson.str);
}

static Status QryBuildDropSubscriptionAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryDropEdgeLabelDescT *desc = (QryDropEdgeLabelDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(
        resource, "namespaceId: %" PRIu32 " type: SUBSCRIPTION name: \"%s\" ", desc->namespaceId, desc->labelName.str);
}

static Status QryBuildCreateOrDropGroupAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryCreateOrDropGroupDescT *desc = (QryCreateOrDropGroupDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "type: GROUP name: \"%s\" ", desc->groupName.str);
}

Status QryBuildCreateOrDropUserAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryCreateOrDropUserDescT *desc = (QryCreateOrDropUserDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "type: USER name: \"%s\"", desc->userName.str);
}

Status QryBuildCreateOrDropUserAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryCreateOrDropUserDescT *desc = (QryCreateOrDropUserDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "processName: \"%s\" ", desc->processName.str);
}

Status QryBuildCreateUserAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryCreateOrDropUserBatchDescT *desc = (QryCreateOrDropUserBatchDescT *)stmt->context->entry;
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < desc->userNum; ++i) {
        BatchCreateUserInnerDescT *userInfo = &(desc->userInfo[i]);
        ret = DbAppendAuditEvtDesc(resource, "type: USER name: \"%s\"", userInfo->userName.str);
        if (ret != GMERR_OK) {
            break;
        }
    }
    return ret;
}

Status QryBuildCreateUserAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryCreateOrDropUserBatchDescT *desc = (QryCreateOrDropUserBatchDescT *)stmt->context->entry;
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < desc->userNum; ++i) {
        BatchCreateUserInnerDescT *userInfo = &(desc->userInfo[i]);
        ret = DbAppendAuditEvtDesc(
            evtDesc, "processName: \"%s\" isAtomic: \"%" PRId8 "\"", userInfo->processName.str, desc->isAtomic);
        if (ret != GMERR_OK) {
            break;
        }
    }
    return ret;
}

static Status QryBuildCreateOrDropGroupAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryCreateOrDropGroupDescT *desc = (QryCreateOrDropGroupDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(
        evtDesc, "processName: \"%s\" isAtomic: \"%" PRId8 "\"", desc->processName.str, desc->isAtomic);
}

static Status QryBuildTransAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    return DbAppendAuditEvtDesc(resource, "type: Trans");
}

static Status QryBuildAbortTransAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryAbortTransDescT *desc = (QryAbortTransDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "abort transId: %" PRIu64 ".", desc->transId);
}

static Status QryBuildSavepointAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QrySavepointDescT *desc = (QrySavepointDescT *)stmt->context->entry;
    char *str = desc->savepointName.len == 0 ? (char *)"" : desc->savepointName.str;
    return DbAppendAuditEvtDesc(resource, "Savepoint name : %s.", str);
}

Status QryBuildGrantOrRevokeSysPrivsAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryGrantOrRevokeSysPrivsDescT *desc = (QryGrantOrRevokeSysPrivsDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(
        resource, "type: PRIVILEGE opType: %" PRIu32 " objType: %" PRIu32 "", desc->opType, desc->objType);
}

static Status QryBuildSetCfgAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QrySetCfgDescT *desc = (QrySetCfgDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "type: CONFIG name: \"%s\"", desc->cfgName.str);
}

static Status QryBuildSetScheduleTaskCfgAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    return DbAppendAuditEvtDesc(resource, "type: SCHEDULE TASK CONFIG");
}

static Status QryBuildSetUserRequestWeightAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryUserRequestWeightDescT *desc = (QryUserRequestWeightDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "type: WEIGHT : %" PRIu32 " ", desc->weight);
}

static Status QryBuildUseNamespaceAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryUseNamespaceDescT *desc = (QryUseNamespaceDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "type: NAMESPACE name: \"%s\" ", desc->namespaceName.str);
}

static Status QryBuildSetUserResourceLimitAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QrySetUserResLimitDescT *desc = (QrySetUserResLimitDescT *)stmt->context->entry;
    uint32_t resourceType = (uint32_t)desc->resType;
    return DbAppendAuditEvtDesc(resource, "type: RESOURCE type: %" PRIu32 " ", resourceType);
}

static Status QryBuildSetConnResourceLimitAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QrySetConnResLimitDescT *desc = (QrySetConnResLimitDescT *)stmt->context->entry;
    uint32_t resourceType = (uint32_t)desc->resType;
    return DbAppendAuditEvtDesc(resource, "type: RESOURCE type: %" PRIu32 " ", resourceType);
}

static Status QryBuildSetLogCtrlItem(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QrySetLogCtrlDescT *desc = (QrySetLogCtrlDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "LOG CTRL ITEM: %s.", desc->processName.str);
}

Status QryBuildSetLogLevelAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QrySetLogCtrlDescT *desc = (QrySetLogCtrlDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "Set log level to %" PRIu32 ", start %" PRIu64 ", last %" PRIu32 " second",
        desc->logCtrlVal, desc->startCycle, desc->duration);
}

Status QryBuildSetLogSwitchAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QrySetLogCtrlDescT *desc = (QrySetLogCtrlDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "Set log switch to %" PRIu32, desc->logCtrlVal);
}

static Status QryBuildTransCheckConflictAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    return DbAppendAuditEvtDesc(resource, "CHCECK TRX CONFLICT");
}

Status QryBuildGrantOrRevokeSysPrivsAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryGrantOrRevokeSysPrivsDescT *desc = (QryGrantOrRevokeSysPrivsDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "user/group Name: \"%s\" processName: \"%s\" isAtomic: \"%" PRId8 "\"",
        desc->userOrGrpName.str, desc->processName.str, desc->isAtomic);
}

static Status QryBuildSetCfgAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QrySetCfgDescT *desc = (QrySetCfgDescT *)stmt->context->entry;
    switch (desc->cfgValue.type) {
        case DB_DATATYPE_INT32:
            return DbAppendAuditEvtDesc(evtDesc, "value: \"%" PRId32 "\"", desc->cfgValue.value.intValue);
        case DB_DATATYPE_STRING:
            return DbAppendAuditEvtDesc(evtDesc, "value: \"%s\"", (const char *)desc->cfgValue.value.strAddr);
        default:
            return GMERR_OK;
    }
}

Status QryBuildSetScheduleTaskCfgAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryScheduleTaskCfgDescT *desc = (QryScheduleTaskCfgDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "workload: %" PRIu32 " unitTime: %" PRIu32 "", desc->workload, desc->unitTime);
}

Status QryBuildSetUserRequestWeightAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryUserRequestWeightDescT *desc = (QryUserRequestWeightDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(
        evtDesc, "userName: \"%s\" processName: \"%s\" ", desc->userName.str, desc->processName.str);
}

static Status QryBuildGrantOrRevokeObjectPrivsAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryGrantOrRevokeObjPrivsDescT *desc = (QryGrantOrRevokeObjPrivsDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource,
        "type: PRIVILEGE objtype: %" PRIu32 " objName: \"%s\" namespaceName: \"%s\" priv: %" PRIu16 " ",
        (uint32_t)desc->objPrivs->objType, desc->objPrivs->objName, desc->objPrivs->namespaceName,
        desc->objPrivs->privileges);
}

static Status QryBuildIpsCloseConnectionAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    DB_UNUSED(stmt);
    return DbAppendAuditEvtDesc(resource, "connection");
}

static Status QryBuildIpsSetUserModeAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryIpsSetUserModeDesc *desc = (QryIpsSetUserModeDesc *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "set user mode: %" PRIu32 " .", desc->userMode);
}

static Status QryBuildIpsSetUserProtocolAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryIpsSetUserProtocolDescT *desc = (QryIpsSetUserProtocolDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource,
        "set user protocol: %" PRIu32 " userName \"%s\" procName \"%s\" isEnable %" PRIu32 ".", desc->protocolType,
        desc->userName.str, desc->processName.str, (uint32_t)desc->isEnable);
}

static Status QryBuildIpsRejectOrAcceptProcessResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryRejectOrAcceptProcessDescT *desc = (QryRejectOrAcceptProcessDescT *)stmt->context->entry;
    char msgSource[MAX_MSG_SOURCE_LEN] = {0};
    Status ret = DrtGetMsgSource(msgSource, MAX_MSG_SOURCE_LEN, desc->pid);
    if (ret != GMERR_OK) {
        return ret;
    }
    return DbAppendAuditEvtDesc(resource, "pid_processName: %s", msgSource);
}

static Status QryBuildIdsConfigDetectProcessResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    return DbAppendAuditEvtDesc(resource, "system threatening config detection");
}

static Status QryBuildGrantOrRevokeObjectPrivsAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryGrantOrRevokeObjPrivsDescT *desc = (QryGrantOrRevokeObjPrivsDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "user/group Name: \"%s\" processName: \"%s\" isAtomic: \"%" PRId8 "\"",
        desc->userOrGroupName.str, desc->processName.str, desc->isAtomic);
}

static Status QryBuildCloseConnectionAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryCloseConnectionDescT *desc = (QryCloseConnectionDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "connId: %" PRIu16 " ", desc->connId);
}

static Status QryBuildIpsRejectOrAcceptProcessAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryRejectOrAcceptProcessDescT *desc = (QryRejectOrAcceptProcessDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "operate: \"%s process\" pid: %" PRIu32 " ",
        desc->opType == DRT_IPS_REJECT_PROCESS ? "reject" : "accept", desc->pid);
}

static Status QryBuildSetUserResourceLimitAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QrySetUserResLimitDescT *desc = (QrySetUserResLimitDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "userName: \"%s\" processName: \"%s\" value: %" PRIu32 "", desc->userName.str,
        desc->processName.str, desc->limitValue);
}

static Status QryBuildSetConnResourceLimitAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QrySetConnResLimitDescT *desc = (QrySetConnResLimitDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "connId: %" PRIu32 " value: %" PRIu32 "", desc->connId, desc->limitValue);
}

static Status QryBuildFlushDataAuditResource(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    return DbAppendAuditEvtDesc(evtDesc, "type: Flush Data");
}

static Status QryBuildFlushDataAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryFlushDataDescT *desc = (QryFlushDataDescT *)stmt->context->entry;
    if (!desc->isBackup) {
        return DbAppendAuditEvtDesc(
            evtDesc, "path: \"%s\" binConsistency: %" PRId8 "", desc->path.str, desc->binConsistency);
    }
    return DbAppendAuditEvtDesc(evtDesc, "path: \"%s\" allowReplace: %" PRId8 " mode: %" PRIu32 "", desc->path.str,
        desc->allowReplace, desc->mode);
}

static Status QryBuildFlushEnableAuditResource(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    return DbAppendAuditEvtDesc(evtDesc, "type: Flush Enable");
}

static Status QryBuildFlushEnableAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryFlushEnableDescT *desc = (QryFlushEnableDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "enable: %" PRId8, (uint8_t)desc->enable);
}

static Status QryBuildVerifyDataAuditResource(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    return DbAppendAuditEvtDesc(evtDesc, "type: Verify Data");
}

static Status QryBuildVerifyDataAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryVerifyPersistDataDescT *desc = (QryVerifyPersistDataDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "path: \"%s\" ", desc->path.str);
}

static Status QryBuildSwapDataDirAuditResource(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    return DbAppendAuditEvtDesc(evtDesc, "type: Swap Data Dir");
}

static Status QryBuildSwapDataDirAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QrySwapDataDirDescT *desc = (QrySwapDataDirDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(
        evtDesc, "ctrl file path: \"%s\", temp file path: \"%s\"", desc->ctrlFilePath.str, desc->tempFilePath.str);
}

static Status QryBuildRsmMigrationAuditResource(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    return DbAppendAuditEvtDesc(evtDesc, "type: Rsm Migration");
}

static Status QryBuildRsmMigrationAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryRsmMigrationDescT *desc = (QryRsmMigrationDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "path: \"%s\"", desc->path.str);
}

static Status QryBuildImportDataAuditResource(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    return DbAppendAuditEvtDesc(evtDesc, "type: Import Data");
}

static Status QryBuildImportDataAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryImportDataDescT *desc = (QryImportDataDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "dir: %s", desc->importDir.str);
}

static Status QryBuildStorageEmergencyAuditResource(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    return DbAppendAuditEvtDesc(evtDesc, "type: Set Emergency");
}

#ifdef SHUTDOWN
static Status QryBuildShutDownAuditResource(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    return DbAppendAuditEvtDesc(evtDesc, "type: ShutDown DbServer");
}
#endif

static void GetAuditLogDesc(BuildAuditLogDescT *auditLogDesc, QryTypeE type, BuildAuditLogDescT *buildAuditLogDesc)
{
    buildAuditLogDesc->name = auditLogDesc[type].name;
    buildAuditLogDesc->logFunc = auditLogDesc[type].logFunc;
    buildAuditLogDesc->resourceFunc = auditLogDesc[type].resourceFunc;
}
void GetPublicDescByType(QryTypeE type, BuildAuditLogDescT *buildAuditLogDesc)
{
    BuildAuditLogDescT auditLogDesc[] = {
        // DDL
        [QRY_TYPE_CREATE_SUBSCRIPTION] = {"CREATE SUBSCRIPTION", QryBuildCreateSubscriptionAuditResource,
            QryBuildCreateSubscriptionAuditLog},
        [QRY_TYPE_DROP_SUBSCRIPTION] = {"DROP SUBSCRIPTION", QryBuildDropSubscriptionAuditResource, NULL},
        [QRY_TYPE_CREATE_GROUP] = {"CREATE GROUP", QryBuildCreateOrDropGroupAuditResource,
            QryBuildCreateOrDropGroupAuditLog},
        [QRY_TYPE_DROP_GROUP] = {"DROP ROLE", QryBuildCreateOrDropGroupAuditResource,
            QryBuildCreateOrDropGroupAuditLog},
        [QRY_TYPE_CREATE_USER] = {"CREATE USER", QryBuildCreateUserAuditResource, QryBuildCreateUserAuditLog},
        [QRY_TYPE_DROP_USER] = {"DROP USER", QryBuildCreateOrDropUserAuditResource, QryBuildCreateOrDropUserAuditLog},
        // DCL
        [QRY_TYPE_BEGIN_TRANS] = {"BEGIN TRANS", QryBuildTransAuditResource, NULL},
        [QRY_TYPE_COMMIT_TRANS] = {"COMMIT TRANS", QryBuildTransAuditResource, NULL},
        [QRY_TYPE_ROLLBACK_TRANS] = {"ROLLBACK TRANS", QryBuildTransAuditResource, NULL},
        [QRY_TYPE_ABORT_TRANS] = {"ABORT TRANS", QryBuildAbortTransAuditResource, NULL},
        [QRY_TYPE_CREATE_SAVEPOINT] = {"CREATE SAVEPOINT", QryBuildSavepointAuditResource, NULL},
        [QRY_TYPE_RELEASE_SAVEPOINT] = {"RELEASE SAVEPOINT", QryBuildSavepointAuditResource, NULL},
        [QRY_TYPE_ROLLBACK_SAVEPOINT] = {"ROLLBACK SAVEPOINT", QryBuildSavepointAuditResource, NULL},
        [QRY_TYPE_USE_NAMESPACE] = {"USE NAMESPACE", QryBuildUseNamespaceAuditResource, NULL},
        [QRY_TYPE_GRANT_SYS_PRIVS] = {"GRANT SYSPRIVS", QryBuildGrantOrRevokeSysPrivsAuditResource,
            QryBuildGrantOrRevokeSysPrivsAuditLog},
        [QRY_TYPE_REVOKE_SYS_PRIVS] = {"REVOKE SYSPRIVS", QryBuildGrantOrRevokeSysPrivsAuditResource,
            QryBuildGrantOrRevokeSysPrivsAuditLog},
        [QRY_TYPE_GRANT_OBJ_PRIVS] = {"GRANT OBJECT PRIVS", QryBuildGrantOrRevokeObjectPrivsAuditResource,
            QryBuildGrantOrRevokeObjectPrivsAuditLog},
        [QRY_TYPE_REVOKE_OBJ_PRIVS] = {"REVOKE OBJECT PRIVS", QryBuildGrantOrRevokeObjectPrivsAuditResource,
            QryBuildGrantOrRevokeObjectPrivsAuditLog},
        [QRY_TYPE_IPS_CLOSE_CONNECTION] = {"IPS CLOSE CONNECTION", QryBuildIpsCloseConnectionAuditResource,
            QryBuildCloseConnectionAuditLog},
        [QRY_TYPE_IPS_SET_USER_MODE] = {"IPS SET USERMODE", QryBuildIpsSetUserModeAuditResource, NULL},
        [QRY_TYPE_IPS_SET_USER_PROTOCOL] = {"IPS SET USER PROTOCOL", QryBuildIpsSetUserProtocolAuditResource, NULL},
        [QRY_TYPE_IPS_REJECT_OR_ACCEPT_PROCESS] = {"IPS REJECT OR ACCEPT PROCESS",
            QryBuildIpsRejectOrAcceptProcessResource, QryBuildIpsRejectOrAcceptProcessAuditLog},
        [QRY_TYPE_IPS_SET_USER_RESOURCE_LIMIT] = {"SET USER RESOURCE LIMIT", QryBuildSetUserResourceLimitAuditResource,
            QryBuildSetUserResourceLimitAuditLog},
        [QRY_TYPE_IPS_SET_CONN_RESOURCE_LIMIT] = {"SET CONNECTION RESOURCE LIMIT",
            QryBuildSetConnResourceLimitAuditResource, QryBuildSetConnResourceLimitAuditLog},
        [QRY_TYPE_IDS_CONFIG_DETECTION] = {"IDS CONFIG DETECT PROCESS", QryBuildIdsConfigDetectProcessResource, NULL},
        [QRY_TYPE_SET_CFG] = {"SET CFG", QryBuildSetCfgAuditResource, QryBuildSetCfgAuditLog},
        [QRY_TYPE_SET_SCHEDULE_TASK_CFG] = {"SET SCHEDULE TASK CFG", QryBuildSetScheduleTaskCfgAuditResource,
            QryBuildSetScheduleTaskCfgAuditLog},
        [QRY_TYPE_SET_USER_REQUEST_WEIGHT] = {"SET USER REQUEST WEIGHT", QryBuildSetUserRequestWeightAuditResource,
            QryBuildSetUserRequestWeightAuditLog},
        [QRY_TYPE_SET_LOG_LEVEL] = {"SET LOG LEVEL", QryBuildSetLogCtrlItem, QryBuildSetLogLevelAuditLog},
        [QRY_TYPE_SET_LOG_SWITCH] = {"SET LOG SWITCH", QryBuildSetLogCtrlItem, QryBuildSetLogSwitchAuditLog},
        [QRY_TYPE_CHECK_TX_OPTIMISTIC_CONFLICT] = {"CHCECK TRX CONFLICT", QryBuildTransCheckConflictAuditResource,
            NULL},
        [QRY_TYPE_FLUSH_DATA] = {"FLUSH DATA", QryBuildFlushDataAuditResource, QryBuildFlushDataAuditLog},
        [QRY_TYPE_FLUSH_ENABLE] = {"FLUSH ENABLE", QryBuildFlushEnableAuditResource, QryBuildFlushEnableAuditLog},
        [QRY_TYPE_IMPORT_DATA] = {"IMPORT DATA", QryBuildImportDataAuditResource, QryBuildImportDataAuditLog},
        [QRY_TYPE_VERIFY_PERSIST_DATA] = {"VERIFY PERSIST DATA", QryBuildVerifyDataAuditResource,
            QryBuildVerifyDataAuditLog},
        [QRY_TYPE_SWAP_DATA_DIR] = {"SWAP DATA DIR", QryBuildSwapDataDirAuditResource, QryBuildSwapDataDirAuditLog},
        [QRY_TYPE_RSM_MIGRATION] = {"RSM MIGRATION", QryBuildRsmMigrationAuditResource, QryBuildRsmMigrationAuditLog},
        [QRY_TYPE_SET_EMERGENCY] = {"SET EMERGENCY", QryBuildStorageEmergencyAuditResource, NULL},
#ifdef SHUTDOWN
        [QRY_TYPE_SHUTDOWN_DBSERVER] = {"SHUTDOWN DBSERVER", QryBuildShutDownAuditResource, NULL},
#endif
    };
    GetAuditLogDesc(auditLogDesc, type, buildAuditLogDesc);
}

void PublicAuditLog(QryStmtT *stmt, Status status)
{
    DB_POINTER(stmt);
    if (stmt->status < STMT_STATUS_PREPARED) {
        return;
    }
    BuildAuditLogDescT buildAuditLogDesc = {0};
    GetPublicDescByType(stmt->context->type, &buildAuditLogDesc);
    if (QryTypeIsDDL(stmt->context->type)) {
        WriteAuditLog(stmt, status, DB_AUDIT_DDL, &buildAuditLogDesc);
    } else {
        WriteAuditLog(stmt, status, DB_AUDIT_DCL, &buildAuditLogDesc);
    }
}
