/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: data structure and interface of PathSearchTaskQueue
 * Author:
 * Create: 2023-8-26
 */

#ifndef SRV_PATH_SEARCH_TASK_H
#define SRV_PATH_SEARCH_TASK_H

#include "srv_path_history_task_queue.h"
#include "srv_data_gql_path_summary.h"

#ifdef __cplusplus
extern "C" {
#endif

Status ProcessNorthSummary(PathSummaryT *summary, PathHistoryQueueT *queue);

#ifdef __cplusplus
}
#endif

#endif  // SRV_PATH_SEARCH_TASK_H
