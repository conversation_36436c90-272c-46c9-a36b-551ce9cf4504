/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Implementations for interface of PathSearchTaskQueue
 * Author:
 * Create: 2023-8-26
 */
#include "srv_path_search_task.h"
#include "srv_path_trigger_interface.h"
#include "srv_data_gql_transaction.h"
#include "srv_data_gql_path.h"

// if all dml in one mini summary belongs to replace of one table, fast path for it
static Status FulfillSearchTask4ReplaceOneTable(
    DbMemCtxT *pathMemCtx, PathMiniSummaryT *miniSummary, PathSearchTaskNewT *pathSearchTask)
{
    // set trigger data
    PathTrigDataT trigData = {0};
    uint32_t elementCnt = DbListGetItemCnt(&miniSummary->element);
    trigData.dmlType = DM_PATH_TRIG_EVENT_REPLACE;
    trigData.slotNum = elementCnt;
    // life cycle: release in abnormal branch; after path search
    trigData.slot4SimplePath = (AASlotT *)DbDynMemCtxAlloc(pathMemCtx, sizeof(AASlotT) * elementCnt);
    if (SECUREC_UNLIKELY(trigData.slot4SimplePath == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc newTrigDataList for one table.");
        return GMERR_OUT_OF_MEMORY;
    }
    for (uint32_t i = 0; i < elementCnt; i++) {
        PathMiniSummaryElementT *element = (PathMiniSummaryElementT *)DbListItem(&miniSummary->element, i);
        if (i == 0) {
            trigData.vertexLabelId = element->vertexLabelId;
        }
        trigData.slot4SimplePath[i] = element->slot;
    }
    // set path search task
    Status ret = PathTrigEvaluateRulesForOneReplaceEvent(pathMemCtx, &trigData, pathSearchTask);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        if (ret != GMERR_UNDEFINED_TABLE) {
            DbDynMemCtxFree(pathMemCtx, trigData.slot4SimplePath);
            return ret;
        }
    }
    return GMERR_OK;
}

static Status FulfillSearchTask(DbMemCtxT *pathMemCtx, PVCDiffT *pvcDiff, DbListT *events,
    PathMiniSummaryT *miniSummary, PathSearchTaskNewT *pathSearchTask)
{
    /*
     * create map to store the info of each dml operation
     * 1. the DmVertexT of dml
     * 2. the bitmap storing difference between new/old DmVertexT
     */
    // set pvc diff map of replace
    if (miniSummary->isReplaceOnSameTable && miniSummary->isTrigsAllTrue) {
        return FulfillSearchTask4ReplaceOneTable(pathMemCtx, miniSummary, pathSearchTask);
    }
    Status ret = CreatePVCDiff(miniSummary, pathMemCtx, pvcDiff);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT1;
    }
    DbCreateList(events, sizeof(PathTrigDataT), pathMemCtx);
    /*
     * 1. get all event from pvc diff map
     * 2. only store event for replace now
     * 3. each dml map to an event now
     */
    ret = BuildEventFromPVCDiff(pvcDiff, events, pathMemCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT2;
    }
    /*
     * 1. get all execute plan for path search from events
     * 2. each event may map to multiple execute plans
     */
    ret = PathTrigEvaluateRulesForEvent(events, pathMemCtx, pathSearchTask);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT2;
    }
    return ret;

EXIT2:
    DbDestroyList(events);
EXIT1:
    return ret;
}

static void FulfillPathSearchTask(
    PathSummaryT *summary, PathMiniSummaryT *miniSummary, WorkerMgrT *workerMgr, PathSearchTaskNewT *pathSearchTask)
{
    pathSearchTask->readViewAfter = miniSummary->readViewSessionAfter;
    pathSearchTask->readViewBefore = miniSummary->readViewSessionBefore;
    pathSearchTask->eeMemCtx = miniSummary->readViewSessionAfter->memCtx;
    pathSearchTask->sub = NULL;  // null for northbound tasks
    pathSearchTask->taskType = NORMAL_DML;
    pathSearchTask->workerMgr = workerMgr;
    pathSearchTask->workerTaskId = miniSummary->workerTaskId;
    pathSearchTask->pathSearchTaskCnt = &summary->pathSearchNum;
    pathSearchTask->summaryMemCtx = summary->memCtx;
    pathSearchTask->needSubPathNode = false;
    pathSearchTask->isMergeReplace = summary->isMergeReplace;
    pathSearchTask->batchOriginId = summary->batchOriginId;
    pathSearchTask->preAllocMem = miniSummary->preAllocMem;
    DbCreateList(&pathSearchTask->pvcTaskList, sizeof(PathPvcTaskT), pathSearchTask->eeMemCtx);
}

Status PathBuildPathSearchTask(
    PathSummaryT *summary, PathMiniSummaryT *miniSummary, WorkerMgrT *workerMgr, PathSearchTaskNewT *pathSearchTask)
{
    DB_POINTER2(miniSummary, pathSearchTask);
    FulfillPathSearchTask(summary, miniSummary, workerMgr, pathSearchTask);
    PVCDiffT pvcDiff = {0};
    DbListT events = {0};
    // set info of path search task
    Status ret = FulfillSearchTask(pathSearchTask->eeMemCtx, &pvcDiff, &events, miniSummary, pathSearchTask);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT;
    }
    // check whether the task will trigger path search or not
    uint32_t pvcTaskCnt = DbListGetItemCnt(&pathSearchTask->pvcTaskList);
    if (pvcTaskCnt == 0) {
        ret = GMERR_NO_DATA;
        goto EXIT;
    }
    // occupy sub node for path search task
    if (pathSearchTask->needSubPathNode) {
        ret = QrySubPathGetNode(&pathSearchTask->subNode);
    } else {
        pathSearchTask->subNode = NULL;
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT;
    }
    pathSearchTask->workerMgr = workerMgr;
    pathSearchTask->workerTaskId = miniSummary->workerTaskId;
    pathSearchTask->preAllocMemId = DB_INVALID_UINT16;
    return GMERR_OK;
EXIT:
    DestroyPvcTaskListContents(&pathSearchTask->pvcTaskList);
    DbDestroyList(&pathSearchTask->pvcTaskList);
    (void)GqlCommitTransaction(miniSummary->readViewSessionAfter);
    QrySessionRelease(miniSummary->readViewSessionAfter);
    miniSummary->readViewSessionAfter = NULL;
    if (miniSummary->readViewSessionBefore != NULL) {
        (void)GqlCommitTransaction(miniSummary->readViewSessionBefore);
        QrySessionRelease(miniSummary->readViewSessionBefore);
        miniSummary->readViewSessionBefore = NULL;
    }
    DrtWorkerDestroyTask(workerMgr, miniSummary->workerTaskId);
    miniSummary->workerTaskId = DB_INVALID_UINT16;
    return ret;
}

void PrepareAndSchedulePathSearch(PathHistoryQueueT *queue, PathSearchPreAllocMemT *preAllocMem,
    PathSearchTaskNewT *pathSearchTasks, uint32_t searchTaskNum)
{
    // schedule path search task [1, n - 1]
    for (uint32_t i = 0; i < searchTaskNum - 1; i++) {
        // 如果有预分配内存，这里给一个
        if (preAllocMem != NULL) {
            pathSearchTasks[i].preAllocMemId = preAllocMem->id;
            pathSearchTasks[i].preAllocBuf = preAllocMem->preAllocForAASlot + i;
            pathSearchTasks[i].preAllocBuf->bufferCursor = pathSearchTasks[i].preAllocBuf->buffer;
            pathSearchTasks[i].preAllocLists =
                preAllocMem->preAllocForList + (i * queue->preAllocMemMgr.preAllocListCnt);
            pathSearchTasks[i].preAllocListCnt = queue->preAllocMemMgr.preAllocListCnt;
        }
        // put task into worker tasklist
        DrtWorkerResetTask(pathSearchTasks[i].workerMgr, pathSearchTasks[i].workerTaskId, queue, &pathSearchTasks[i],
            PathSearchProcessEntry4North);
        DrtWorkerScheduleTask(
            pathSearchTasks[i].workerMgr, pathSearchTasks[i].workerTaskId, TASK_PRIORITY_NORMAL, false);
    }
    // schedule last path search task (n)
    if (preAllocMem != NULL) {
        pathSearchTasks[searchTaskNum - 1].preAllocBuf = preAllocMem->preAllocForAASlot + (searchTaskNum - 1);
        pathSearchTasks[searchTaskNum - 1].preAllocBuf->bufferCursor =
            pathSearchTasks[searchTaskNum - 1].preAllocBuf->buffer;
        pathSearchTasks[searchTaskNum - 1].preAllocLists =
            preAllocMem->preAllocForList + ((searchTaskNum - 1) * queue->preAllocMemMgr.preAllocListCnt);
        pathSearchTasks[searchTaskNum - 1].preAllocListCnt = queue->preAllocMemMgr.preAllocListCnt;
        pathSearchTasks[searchTaskNum - 1].preAllocMemId = preAllocMem->id;
    }
    RescheduleHistoryTask(queue);
    PathSearchProcessEntry4North(queue, &pathSearchTasks[searchTaskNum - 1]);
}

void ReleaseSummaryFailed(PathHistoryQueueT *queue, PathSummaryT *summary, uint32_t miniSummaryCnt)
{
    for (uint32_t i = 0; i < miniSummaryCnt; i++) {
        PathMiniSummaryT *miniSummary = summary->miniSummaryList[i];
        if (SECUREC_UNLIKELY(miniSummary == NULL)) {
            continue;
        }
        if (miniSummary->workerTaskId != DB_INVALID_UINT16) {
            DrtWorkerDestroyTask(queue->workerMgr, summary->miniSummaryList[i]->workerTaskId);
        }
        if (miniSummary->readViewSessionAfter != NULL) {
            (void)GqlCommitTransaction(miniSummary->readViewSessionAfter);
            QrySessionRelease(miniSummary->readViewSessionAfter);
            miniSummary->readViewSessionAfter = NULL;
        }
        if (miniSummary->readViewSessionBefore != NULL) {
            (void)GqlCommitTransaction(miniSummary->readViewSessionBefore);
            QrySessionRelease(miniSummary->readViewSessionBefore);
            miniSummary->readViewSessionBefore = NULL;
        }
        ReleaseBatchDmlPreAllocMem(miniSummary->preAllocMem);
    }
}

Status ProcessNorthSummary(PathSummaryT *summary, PathHistoryQueueT *queue)
{
    DB_POINTER(summary);
    uint32_t miniSummaryCnt = summary->miniSummaryNum;
    PathSearchPreAllocMemT *preAllocMem = GetPreAllocMem(queue);
    // 如果使用的是非预分配内存:最后一个搜索任务执行完成，直接delete顶层的summary->memCtx时释放
    PathSearchTaskNewT *pathSearchTasks;
    if (preAllocMem != NULL) {
        pathSearchTasks = (PathSearchTaskNewT *)(preAllocMem->preAllocForPathSearchTask);
    } else {
        pathSearchTasks = DbDynMemCtxAlloc(summary->memCtx, sizeof(PathSearchTaskNewT) * miniSummaryCnt);
        if (pathSearchTasks == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "cannot alloc mem for path search tasks");
            goto FAILED;
        }
    }
    Status ret;
    // build path search task for each mini-summary
    for (uint32_t i = 0; i < miniSummaryCnt; i++) {
        PathMiniSummaryT *miniSummary = summary->miniSummaryList[i];
        if (SECUREC_UNLIKELY(miniSummary == NULL)) {
            continue;
        }
        // RescheduleHistoryTask必须在这里之后，不然actionList会有并发问题
        ret = PathBuildPathSearchTask(summary, miniSummary, queue->workerMgr, &pathSearchTasks[summary->pathSearchNum]);
        if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
            goto FAILED;
        }
        // nodata说明这个miniSummary不搜索，不处理它。这里不+1
        summary->pathSearchNum += (ret == GMERR_NO_DATA) ? 0 : 1;
    }
    if (summary->pathSearchNum == 0) {
        ret = GMERR_OK;
        goto FAILED;
    }
    // 内部会RescheduleHistoryTask
    PrepareAndSchedulePathSearch(queue, preAllocMem, pathSearchTasks, summary->pathSearchNum);
    return ret;

FAILED:
    if (summary->isMergeReplace) {
        PathServiceT *service = GetPathService();
        ret = PathMergeReplaceQueueLockTryRelease(service->pathMergeReplaceQueue, summary->batchOriginId);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Unable to PathMergeReplaceQueueLockTryRelease.");
        }
    }
    if (preAllocMem != NULL) {
        ReleasePreAllocMem(queue, preAllocMem->id);
    }
    RescheduleHistoryTask(queue);
    ReleaseSummaryFailed(queue, summary, miniSummaryCnt);
    DestroyPathSummary(summary->memCtx);
    return ret;
}
