/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author: GQL Team
 * Create: 2024-04-07
 */
#include "srv_path_smooth_recon_base.h"

#include "dm_meta_key_oper.h"
#include "cpl_gql_capi_compiler.h"
#include "cpl_gql_ast.h"
#include "srv_sr_map.h"
#include "srv_data_gql_transaction.h"
#include "srv_data_gql_path.h"

Status GenerateOneSouthPvcTaskForOnlyBeginEnd(PathSearchTaskNewT *pathSearchTask, bool isFirst, bool isLast)
{
    Status ret;
    PathPvcTaskT pvcTask = {0};
    pvcTask.eeMemCtx = pathSearchTask->eeMemCtx;

    DbListT *taskList = DbDynMemCtxAlloc(pvcTask.eeMemCtx, sizeof(DbListT));
    if (taskList == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc taskList, the size is %zu.", sizeof(DbListT));
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateList(taskList, sizeof(SubSearchTaskT), pvcTask.eeMemCtx);
    SubSearchTaskT task = {0};
    task.pathInfo = NULL;
    task.plan = NULL;
    if (isFirst) {
        task.beginEndFlag |= (uint8_t)BEGIN_PATH_TASK;  // BEGIN_PATH_TASK(0x01)
    }
    if (isLast) {
        task.beginEndFlag |= (uint8_t)END_PATH_TASK;  // END_PATH_TASK(0x10)
    }
    ret = DbAppendListItem(taskList, &task);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT;
    }
    pvcTask.psTasks = taskList;
    ret = DbAppendListItem(&pathSearchTask->pvcTaskList, &pvcTask);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT;
    }
    return ret;

EXIT:
    DestroySubSearchTaskList(pvcTask.eeMemCtx, taskList);
    return ret;
}

Status GenerateSubSearchTask(
    DbMemCtxT *memCtx, DmComplexPathInfoT *pInfo, PlannedStmtT *plan, AASlotT *slot, SubSearchTaskT *task)
{
    task->oplog.slotNum = 1;
    task->oplog.slot = (AASlotT **)DbDynMemCtxAlloc(memCtx, sizeof(AASlotT *));
    if (task->oplog.slot == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc slots of subSearchTask in GenerateOneSouthPvcTask.");
        return GMERR_OUT_OF_MEMORY;
    }
    task->oplog.slot[0] = slot;

    task->pathInfo = pInfo;
    PlanCacheKeyT planKey = {
        .pathId = pInfo->metaCommon.metaId, .dmlType = DM_PATH_TRIG_EVENT_ONESHOT_SUB};  // default dmltype
    task->planKey = PlanCacheKey2Hash32(&planKey);
    task->plan = plan;

    task->beginEndFlag = NORMAL_PATH_TASK;  // Normal(0x00) by default
    return GMERR_OK;
}

Status AppendSubSearchTask(DbMemCtxT *memCtx, DbListT *taskList, AASlotT *slot, PlannedStmtT *plan, uint32_t pathId)
{
    DmComplexPathInfoT *pInfo;
    // Release at the end of the lifecycle of  pInfo
    Status ret = CataGetComplexPathInfoById(pathId, &pInfo);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    SubSearchTaskT task = {0};
    ret = GenerateSubSearchTask(memCtx, pInfo, plan, slot, &task);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        (void)CataReleaseComplexPathInfo(pInfo);
        return ret;
    }
    ret = DbAppendListItem(taskList, &task);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        (void)CataReleaseComplexPathInfo(pInfo);
        return ret;
    }
    (void)CataReleaseComplexPathInfo(pInfo);
    return ret;
}

Status GenerateOneSouthPvcTask(PathSearchTaskNewT *pathSearchTask, HeapTupleBufT heapTupleBufs,
    DmVertexLabelT *vertexLabel, DbListT *pathIdList, DbListT *pathPlanList)
{
    PathPvcTaskT pvcTask = {0};
    pvcTask.eeMemCtx = pathSearchTask->eeMemCtx;
    pvcTask.subEvent = DM_SUBS_EVENT_REPLACE;
    pvcTask.vertexLabel = vertexLabel;
    AASlotT *slot = DbDynMemCtxAlloc(pvcTask.eeMemCtx, sizeof(AASlotT));
    if (slot == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc AASlotT in GenerateOneSouthPvcTask.");
        return GMERR_OUT_OF_MEMORY;
    }
    *slot = (AASlotT){0};
    Status ret = DmDeSerializeVertexWithMemCtx(
        pvcTask.eeMemCtx, heapTupleBufs.buf, heapTupleBufs.bufSize, vertexLabel, &slot->dmVertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Unable to build AASlotT in GenerateOneSouthPvcTask.");
        goto EXIT2;
    }
    DbListT *taskList = DbDynMemCtxAlloc(pvcTask.eeMemCtx, sizeof(DbListT));
    if (taskList == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc taskList, the size is %zu.", sizeof(DbListT));
        goto EXIT3;
    }
    DbCreateList(taskList, sizeof(SubSearchTaskT), pvcTask.eeMemCtx);
    uint32_t pathCnt = DbListGetItemCnt(pathIdList);
    for (uint32_t j = 0; j < pathCnt; j++) {
        PlannedStmtT *plan = (PlannedStmtT *)DbListItem(pathPlanList, j);
        uint32_t pathId = *(uint32_t *)DbListItem(pathIdList, j);
        ret = AppendSubSearchTask(pvcTask.eeMemCtx, taskList, slot, plan, pathId);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            goto EXIT4;
        }
    }
    pvcTask.psTasks = taskList;
    ret = DbAppendListItem(&pathSearchTask->pvcTaskList, &pvcTask);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT4;
    }
    return ret;
EXIT4:
    DestroySubSearchTaskList(pvcTask.eeMemCtx, taskList);
EXIT3:
    DmDestroyVertex(slot->dmVertex);
EXIT2:
    DbDynMemCtxFree(pvcTask.eeMemCtx, slot);
    return ret;
}

void InitPathSearchTaskForSouth(
    SRMapFetchedDataT *fetchedData, PathSummaryT *summary, PathSearchTaskNewT *pathSearchTask)
{
    // Set info of path search task
    pathSearchTask->eeMemCtx = summary->readViewSession->memCtx;
    pathSearchTask->readViewAfter = summary->readViewSession;
    pathSearchTask->readViewBefore = NULL;
    pathSearchTask->taskType = SOUTH_SMOOTH_OR_RECONCILIATION;
    pathSearchTask->needSubPathNode = true;
    DbCreateList(&pathSearchTask->pvcTaskList, sizeof(PathPvcTaskT), pathSearchTask->eeMemCtx);
    pathSearchTask->sub = fetchedData->auxInfo.pSub;
}

Status GenerateSouthPvcTasks(PathSearchTaskNewT *pathSearchTask, SRMapFetchedDataT *fetchedData)
{
    for (uint32_t i = 0; i < fetchedData->auxInfo.numTuplesFetched; i++) {
        DmVertexLabelT *vertexLabel = NULL;
        // Release at the end of the lifecycle of vertexLabel
        Status ret = CataGetVertexLabelById(
            DbGetInstanceByMemCtx(pathSearchTask->eeMemCtx), fetchedData->tableIds[i], &vertexLabel);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Unable to get vertex label by id(%" PRIu32 ").", fetchedData->tableIds[i]);
            return ret;
        }
        ret = GenerateOneSouthPvcTask(pathSearchTask, fetchedData->heapTupleBufs[i], vertexLabel,
            fetchedData->listPathIds[i], fetchedData->listPhysPlans[i]);
        (void)CataReleaseVertexLabel(vertexLabel);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Unable to create PVC task.");
            DestroyPvcTaskListContents(&pathSearchTask->pvcTaskList);
            return ret;
        }
    }

    // Add BEGIN/END/BEGIN_END flag for the first/last/first_and_last Sub Search Task
    if (fetchedData->auxInfo.isFirst) {
        PathPvcTaskT *firstPvcTask = (PathPvcTaskT *)DbListItem(&pathSearchTask->pvcTaskList, 0);
        SubSearchTaskT *firstSubTask = (SubSearchTaskT *)DbListItem(firstPvcTask->psTasks, 0);
        firstSubTask->beginEndFlag |= (uint8_t)BEGIN_PATH_TASK;  // BEGIN_PATH_TASK(0x01)
    }
    if (fetchedData->auxInfo.isEnd) {
        uint32_t pvcTaskCnt = DbListGetItemCnt(&pathSearchTask->pvcTaskList);
        PathPvcTaskT *lastPvcTask = (PathPvcTaskT *)DbListItem(&pathSearchTask->pvcTaskList, pvcTaskCnt - 1);
        uint32_t lastSubTaskCnt = DbListGetItemCnt(lastPvcTask->psTasks);
        SubSearchTaskT *lastSubTask = (SubSearchTaskT *)DbListItem(lastPvcTask->psTasks, lastSubTaskCnt - 1);
        lastSubTask->beginEndFlag |= (uint8_t)END_PATH_TASK;  // END_PATH_TASK(0x10)
    }
    return GMERR_OK;
}

Status GenerateOneSouthPathSearchTask(PathSummaryT *summary, PathHistoryQueueT *queue, SRMapFetchedDataT *fetchedData)
{
    PathSearchTaskNewT pathSearchTask = {0};
    InitPathSearchTaskForSouth(fetchedData, summary, &pathSearchTask);
    Status ret;
    if (fetchedData->auxInfo.numTuplesFetched == 0) {
        if (fetchedData->auxInfo.isEnd) {
            // No records fetched but it is the NOBEGIN_END/BEGIN_END round, generate one Sub Search Task
            ret = GenerateOneSouthPvcTaskForOnlyBeginEnd(
                &pathSearchTask, fetchedData->auxInfo.isFirst, fetchedData->auxInfo.isEnd);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_ERROR(ret, "Unable to create BEGIN|END-only PVC task.");
                goto EXIT1;
            }
        } else {
            // Impossible cases: No records fetched and it is NOBEGIN_NOEND/BEGIN_NOEND round
            DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Unexpected results for one fetch.");
            ret = GMERR_INTERNAL_ERROR;
            goto EXIT1;
        }
    } else {
        ret = GenerateSouthPvcTasks(&pathSearchTask, fetchedData);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            goto EXIT1;
        }
    }

    // Occupy sub node for path search task
    ret = QrySubPathGetNode(&pathSearchTask.subNode);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Unable to occupy sub node for path search tasks");
        if (fetchedData->auxInfo.numTuplesFetched != 0) {
            DestroyPvcTaskListContents(&pathSearchTask.pvcTaskList);
        }
        goto EXIT1;
    }

    // here, within the function, we mark History Queue Worker unscheduled and schedule a new history task if possible
    PathSearchProcessEntry4South(queue, &pathSearchTask);
    return GMERR_OK;

EXIT1:
    DbDestroyList(&pathSearchTask.pvcTaskList);
    (void)GqlCommitTransaction(summary->readViewSession);
    QrySessionRelease(summary->readViewSession);
    summary->readViewSession = NULL;
    RescheduleHistoryTask(queue);
    return ret;
}

Status CreateFetchedData(DbMemCtxT *memCtx, SRMapFetchedDataT *fetchedData, uint32_t maxNumTuplesToFetch)
{
    fetchedData->heapTupleBufs = (HeapTupleBufT *)DbDynMemCtxAlloc(memCtx, sizeof(HeapTupleBufT) * maxNumTuplesToFetch);
    if (fetchedData->heapTupleBufs == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc mem to heapTupleBufs");
        return GMERR_OUT_OF_MEMORY;
    }

    fetchedData->tableIds = (uint32_t *)DbDynMemCtxAlloc(memCtx, sizeof(uint32_t) * maxNumTuplesToFetch);
    if (fetchedData->tableIds == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc mem to tableIds");
        DbDynMemCtxFree(memCtx, fetchedData->heapTupleBufs);
        return GMERR_OUT_OF_MEMORY;
    }

    fetchedData->listPhysPlans = (DbListT **)DbDynMemCtxAlloc(memCtx, sizeof(DbListT *) * maxNumTuplesToFetch);
    if (fetchedData->listPhysPlans == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc mem to listPhysPlans");
        DbDynMemCtxFree(memCtx, fetchedData->tableIds);
        DbDynMemCtxFree(memCtx, fetchedData->heapTupleBufs);
        return GMERR_OUT_OF_MEMORY;
    }

    fetchedData->listPathIds = (DbListT **)DbDynMemCtxAlloc(memCtx, sizeof(DbListT *) * maxNumTuplesToFetch);
    if (fetchedData->listPathIds == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc mem to listPathIds");
        DbDynMemCtxFree(memCtx, fetchedData->listPhysPlans);
        DbDynMemCtxFree(memCtx, fetchedData->tableIds);
        DbDynMemCtxFree(memCtx, fetchedData->heapTupleBufs);
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

void DestroyFetchedData(DbMemCtxT *memCtx, SRMapFetchedDataT *fetchedData)
{
    DbDynMemCtxFree(memCtx, fetchedData->listPathIds);
    DbDynMemCtxFree(memCtx, fetchedData->listPhysPlans);
    DbDynMemCtxFree(memCtx, fetchedData->tableIds);
    DbDynMemCtxFree(memCtx, fetchedData->heapTupleBufs);
}

inline static void ClearFetchedData(DbMemCtxT *memCtx, SRMapFetchedDataT *fetchedData)
{
    for (uint32_t k = 0; k < fetchedData->auxInfo.numTuplesFetched; k++) {
        DbDestroyList(fetchedData->listPathIds[k]);
        DbDynMemCtxFree(memCtx, fetchedData->listPathIds[k]);
    }
    for (uint32_t k = 0; k < fetchedData->auxInfo.numTuplesFetched; k++) {
        DbDynMemCtxFree(SRGetMemCtx(), fetchedData->heapTupleBufs[k].buf);
        DbDestroyList(fetchedData->listPhysPlans[k]);
        DbDynMemCtxFree(memCtx, fetchedData->listPhysPlans[k]);
    }
}

Status GeneratePathTaskForSouthSummary(
    PathSummaryT *summary, PathHistoryQueueT *queue, bool *isLast, bool *isDropped, bool isRecon)
{
    SRMapFetchParamsInT paramsIn = {.memCtx = summary->memCtx, .pSession = summary->readViewSession};
    SRMapFetchedDataT fetchedData = {.auxInfo = {0}};
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(summary->memCtx);
    if (isRecon) {
        paramsIn.pEntry = NULL;
        paramsIn.maxNumTuplesToFetch = CataGetSlowRecordPerFetchNum(dbInstance);
    } else {
        paramsIn.pEntry = summary->srMapEntry;
        paramsIn.maxNumTuplesToFetch = CataGetFastRecordPerFetchNum(dbInstance);
    }
    Status ret = CreateFetchedData(summary->memCtx, &fetchedData, paramsIn.maxNumTuplesToFetch);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // 失败时内部释放，下方如果失败，需要调用DestroyFetchedData释放
        return ret;
    }

    if (isRecon) {
        ret = FetchFromSRMapTuplesRecon(&paramsIn, &fetchedData);
    } else {
        ret = FetchFromSRMapTuplesSmooth(&paramsIn, &fetchedData);
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Unable to fetch data tuples from the SRMap");
        // If meets error in SRMap fetch functions,
        // 1. SR map already destroy/memFree all the lists in the DblistT
        // 2. it does not increase refCount on the sub info in metadata
        goto EXIT3;
    }

    *isLast = fetchedData.auxInfo.isEnd;
    *isDropped = fetchedData.auxInfo.isDropped;

    if (*isDropped) {
        ret = GqlCommitTransaction(summary->readViewSession);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Unable to commit transaction in GenerateOnePaskSearchTask.");
            QrySessionRelease(summary->readViewSession);
            summary->readViewSession = NULL;
        }
        // It does not increase refCount on the sub info in metadata since smooth/recon is already dropped
        goto RELEASE_MEMORY_ALL;
    }

    ret = GenerateOneSouthPathSearchTask(summary, queue, &fetchedData);
    if (fetchedData.auxInfo.numTuplesFetched == 0 && *isLast) {
        // No need to release refCount on the sub info since it contains an END message
        goto RELEASE_MEMORY_ALL;
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Unable to generate Pask Search task");
        // The SRMap fetch functions have increased the refCount on the sub info in metadata
        (void)CataReleaseSubscription(fetchedData.auxInfo.pSub);
    }
RELEASE_MEMORY_ALL:
    ClearFetchedData(summary->memCtx, &fetchedData);
EXIT3:
    DestroyFetchedData(summary->memCtx, &fetchedData);
    return ret;
}

Status ConvertConditions(DbMemCtxT *memCtx, SRTableSubAnalyzedT *tableSub, DbListT **listFilterConds)
{
    Status ret;
    *listFilterConds = (DbListT *)DbDynMemCtxAlloc(memCtx, sizeof(DbListT));
    if (*listFilterConds == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to analyze path, malloc meets mistake");
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateList(*listFilterConds, sizeof(SRMapFilterCondT), memCtx);
    uint32_t conditionsNum = DbListGetItemCnt(tableSub->conditions);
    for (uint32_t i = 0; i < conditionsNum; i++) {
        SRTableSubCondAnalyzedT *condAnalyzed = (SRTableSubCondAnalyzedT *)DbListItem(tableSub->conditions, i);

        SRMapFilterCondT condData;
        condData.property = condAnalyzed->property;
        condData.value = condAnalyzed->value;
        condData.vertexDesc = condAnalyzed->vertexDesc;

        // Store into the list of conditions
        ret = DbAppendListItem(*listFilterConds, &condData);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            goto RELEASE;
        }
    }

    return GMERR_OK;
RELEASE:
    DbDestroyList(*listFilterConds);
    DbDynMemCtxFree(memCtx, *listFilterConds);
    return ret;
}

// Converts the data from the intermediate struct SRTableSubAnalyzedT to SRTable
static Status ConvertToSRTable(DbMemCtxT *memCtx, SRTableSubAnalyzedT *tableSub, SRTableT *srTable)
{
    DbListT *listFilterConds;
    Status ret = ConvertConditions(memCtx, tableSub, &listFilterConds);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    HpScanSubsCondDataT *subscriptionCondData =
        (HpScanSubsCondDataT *)DbDynMemCtxAlloc(memCtx, sizeof(HpScanSubsCondDataT));
    subscriptionCondData->userScanRowProc = SRMapFilter;
    subscriptionCondData->userData = listFilterConds;

    ret = SRTableCreate(tableSub->tableId, subscriptionCondData, tableSub->pathIds, srTable);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbDynMemCtxFree(memCtx, subscriptionCondData);
        return ret;
    }
    return GMERR_OK;
}

static Status AppendSrMapToList(DbListT *srTableList, DbMemCtxT *memCtx, SRTableIRPlansT *srTableIRPlans)
{
    DB_POINTER2(memCtx, srTableIRPlans);
    SRTableT srTable = {0};
    Status ret = SavePathPlans2PlanCache(memCtx, srTableIRPlans->pathIRPlans, DM_PATH_TRIG_EVENT_ONESHOT_SUB);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = ConvertToSRTable(memCtx, srTableIRPlans->table, &srTable);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = SRTableGetSavePhysicalPlans(&srTable);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return DbAppendListItem(srTableList, &srTable);
}

Status CreateAndScheduleSmoothSummary(SRMapEntryT *pSRMapEntry)
{
    PathServiceT *service = GetPathService();
    PathSummaryT *summary = NULL;
    PathSummaryParaT summaryPara = {.miniBatchTotal = 0,
        .isNormalBatch = true,
        .batchOriginId = INVALID_BATCH_ID,
        .isMergeReplace = false,
        .type = SOUTH_SMOOTH};
    Status ret = CreatePathSummary(service->historyQueue->memCtx, &summary, summaryPara);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    summary->srMapEntry = pSRMapEntry;
    ret = SchedSummary2HistoryQueue(summary);
    return ret;
}

Status CreateOneShotSubWork(const DbListT *subObjects, const DmSubscriptionT *sub)
{
    DbMemCtxT *memCtx = SRGetMemCtx();
    // Generate physical plans and store into SRTable
    // node->pathIRPlans contains a list of SRTableIRPlansT.
    DbListT *srTableList = (DbListT *)DbDynMemCtxAlloc(memCtx, sizeof(DbListT));
    if (srTableList == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to analyze path, malloc meets mistake");
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateList(srTableList, sizeof(SRTableT), memCtx);

    Status ret;
    uint32_t tableSubsNum = DbListGetItemCnt(subObjects);
    for (uint32_t i = 0; i < tableSubsNum; i++) {
        SRTableIRPlansT *srTableIRPlans = (SRTableIRPlansT *)DbListItem(subObjects, i);
        ret = AppendSrMapToList(srTableList, memCtx, srTableIRPlans);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            goto EXIT1;
        }
    }
    // Create SRMapEntry and insert into SRMap
    CataKeyT *cataKey = (CataKeyT *)DbDynMemCtxAlloc(memCtx, sizeof(CataKeyT));
    if (cataKey == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc cataKey in OneShotSubWork.");
        ret = GMERR_OUT_OF_MEMORY;
        goto EXIT1;
    }
    (void)memset_s(cataKey, sizeof(CataKeyT), 0, sizeof(CataKeyT));

    char *tempName = (char *)DbDynMemCtxAlloc(memCtx, strlen(sub->metaCommon.metaName) + 1);
    if (tempName == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc tempName for cataKey in OneShotSubWork.");
        ret = GMERR_OUT_OF_MEMORY;
        goto EXIT2;
    }
    (void)strcpy_s(tempName, strlen(sub->metaCommon.metaName) + 1, sub->metaCommon.metaName);
    CataSetKeyForLabel(cataKey, sub->metaCommon.dbId, sub->metaCommon.namespaceId, tempName);
    SRMapEntryT *pSRMapEntry;
    ret = InsertSRMap(sub->metaCommon.metaId, cataKey, srTableList, sub->subsType == SUB_ONESHOT_SLOW, &pSRMapEntry);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT3;
    }

    // For ONESHOT_FAST (smoothing), create path summary and schedule task
    if (sub->subsType == SUB_ONESHOT_FAST) {
        ret = CreateAndScheduleSmoothSummary(pSRMapEntry);
    }
    return ret;

EXIT3:
    DbDynMemCtxFree(memCtx, tempName);
EXIT2:
    DbDynMemCtxFree(memCtx, cataKey);
EXIT1:
    DbDestroyList(srTableList);
    DbDynMemCtxFree(memCtx, srTableList);
    return ret;
}

Status RemoveOneShotSubWork(const DmSubscriptionT *sub)
{
    DB_POINTER(sub);
    Status ret;
    // delete all plans generated by the one-shot sub
    uint32_t count = DbListGetItemCnt(sub->srPathIdList);
    for (uint32_t i = 0; i < count; ++i) {
        DbListT *pathList = (DbListT *)DbListItem(sub->srPathIdList, i);
        uint32_t planCnt = DbListGetItemCnt(pathList);
        for (uint32_t j = 0; j < planCnt; ++j) {
            uint32_t *pathId = (uint32_t *)DbListItem(pathList, j);
            PlanCacheKeyT planKey = (PlanCacheKeyT){.pathId = *pathId, .dmlType = DM_PATH_TRIG_EVENT_ONESHOT_SUB};
            uint32_t key = PlanCacheKey2Hash32(&planKey);
            ret = PathTrigDelPlanById(key);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
        }
    }
    ret = SRMapDrop(sub->metaCommon.metaId);
    //  rearrange if statement removing second condition when all ready
    if ((ret != GMERR_OK) && (ret != GMERR_NO_DATA)) {
        return ret;
    }
    return GMERR_OK;
}
