/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author: GQL Team
 * Create: 2024-04-07
 */
#ifndef SRV_PATH_SMOOTH_RECON_BASE_H
#define SRV_PATH_SMOOTH_RECON_BASE_H
#include "srv_data_gql_path_summary.h"
#include "srv_path_history_task_queue.h"
#ifdef __cplusplus
extern "C" {
#endif

Status GeneratePathTaskForSouthSummary(
    PathSummaryT *summary, PathHistoryQueueT *queue, bool *isLast, bool *isDropped, bool isRecon);

Status CreateOneShotSubWork(const DbListT *subObjects, const DmSubscriptionT *sub);
Status RemoveOneShotSubWork(const DmSubscriptionT *sub);

#ifdef __cplusplus
}
#endif
#endif  // SRV_PATH_SMOOTH_RECON_BASE_H
