/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author:
 * Create: 2024-04-07
 */
#include "srv_data_gql_path_sub.h"
#include "srv_data_gql_path_summary.h"
#include "cpl_gql_capi_compiler.h"
#include "srv_data_gql_path.h"
#include "dm_meta_key_oper.h"
#include "srv_sr_map.h"

static void InitPathSummaryPara4InitLoad(PathSummaryParaT *summaryPara)
{
    summaryPara->miniBatchTotal = 0;
    summaryPara->isNormalBatch = false;
    summaryPara->batchOriginId = INVALID_BATCH_ID;
    summaryPara->isMergeReplace = false;
    summaryPara->type = INIT_LOAD;
}

Status CreateInitLoadSubWork(DbMemCtxT *memCtx, const DbListT *subObjects, const DmSubscriptionT *sub)
{
    PathSummaryT *summary = NULL;
    PathSummaryParaT summaryPara = {0};
    InitPathSummaryPara4InitLoad(&summaryPara);
    PathServiceT *service = GetPathService();
    Status ret = CreatePathSummary(service->historyQueue->memCtx, &summary, summaryPara);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(
            ret, "Unable to create pathsummay for init_load event. the sub name is %s.", sub->metaCommon.metaName);
        return ret;
    }
    summary->initLoadParam = (InitLoadEventParamT *)DbDynMemCtxAlloc(summary->memCtx, sizeof(InitLoadEventParamT));
    if (summary->initLoadParam == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc memmory for initLoadParam. the sub name is %s.",
            sub->metaCommon.metaName);
        ret = GMERR_OUT_OF_MEMORY;
        goto EXIT;
    }
    DmSubscriptionT *tmpSub = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, sub->metaCommon.dbId, sub->metaCommon.namespaceId, sub->metaCommon.metaName);
    // release point: path search done; exception branch
    ret = CataGetSubsByName(&cataKey, &tmpSub, DbGetInstanceByMemCtx(memCtx));
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT;
    }
    summary->initLoadParam->sub = tmpSub;
    summary->initLoadParam->pathId = tmpSub->labelId;
    SRTableIRPlansT *srTable = DbListItem(subObjects, 0);
    IRPlanT *logPlan = *(IRPlanT **)DbListItem(srTable->pathIRPlans, 0);
    ret = SavePathPlan2PlanCache(
        memCtx, logPlan, sub->labelId, DM_PATH_TRIG_EVENT_INIT_LOAD, &summary->initLoadParam->planKey);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        (void)CataReleaseSubscription(tmpSub);
        goto EXIT;
    }
    // release point: path search done; exception branch
    ret = SchedSummary2HistoryQueue(summary);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        (void)PathTrigDelPlanById(summary->initLoadParam->planKey);
        (void)CataReleaseSubscription(tmpSub);
        goto EXIT;
    }
    return GMERR_OK;
EXIT:
    DestroyPathSummary(summary->memCtx);
    return ret;
}

Status RemoveInitLoadSubWork(const DmSubscriptionT *sub)
{
    DB_POINTER(sub);
    PlanCacheKeyT planKey = (PlanCacheKeyT){.pathId = sub->labelId, .dmlType = DM_PATH_TRIG_EVENT_INIT_LOAD};
    uint32_t key = PlanCacheKey2Hash32(&planKey);
    return PathTrigDelPlanById(key);
}

Status ConvertScanCond(DbMemCtxT *memCtx, DmPathConstraintT *constraint, DbListT **listFilterConds)
{
    DB_POINTER3(memCtx, constraint, listFilterConds);
    Status ret = GMERR_OK;
    DmVertexLabelT *vertexLabel = NULL;
    ret = CataGetVertexLabelById(DbGetInstanceByMemCtx(memCtx), constraint->vertexLabelId, &vertexLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    *listFilterConds = (DbListT *)DbDynMemCtxAlloc(memCtx, sizeof(DbListT));
    if (*listFilterConds == NULL) {
        DB_LOG_ERROR(
            GMERR_OUT_OF_MEMORY, "Unable to analyze path, malloc meets mistake. the size is %zu.", sizeof(DbListT));
        ret = GMERR_OUT_OF_MEMORY;
        goto EXIT;
    }
    DbCreateList(*listFilterConds, sizeof(SRMapFilterCondT), memCtx);
    uint32_t conditionsNum = DbListGetItemCnt(&constraint->conditions);
    for (uint32_t i = 0; i < conditionsNum; i++) {
        DmPathConditionT *cond = (DmPathConditionT *)DbListItem(&constraint->conditions, i);

        SRMapFilterCondT condData;
        DmSchemaT *schema = MEMBER_PTR_NO_CHECK(vertexLabel->metaVertexLabel, schema);
        condData.property = &schema->properties[cond->propId];
        condData.value = cond->value;
        condData.vertexDesc = vertexLabel->vertexDesc;

        // Store into the list of conditions
        ret = DbAppendListItem(*listFilterConds, &condData);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            goto EXIT1;
        }
    }

    return GMERR_OK;
EXIT1:
    DbDestroyList(*listFilterConds);
    DbDynMemCtxFree(memCtx, *listFilterConds);
    *listFilterConds = NULL;
EXIT:
    (void)CataReleaseVertexLabel(vertexLabel);
    return ret;
}

void DestroyFilterConds(DbMemCtxT *memCtx, DbListT *listFilterConds)
{
    if (listFilterConds == NULL) {
        return;
    }
    DbDestroyList(listFilterConds);
    DbDynMemCtxFree(memCtx, listFilterConds);
}

static void FulfillPathSearchTask4InitLoad(PathSearchTaskNewT *pathSearchTask, PathSummaryT *summary)
{
    DB_POINTER2(pathSearchTask, summary);
    pathSearchTask->eeMemCtx = summary->readViewSession->memCtx;
    pathSearchTask->readViewAfter = summary->readViewSession;
    pathSearchTask->readViewBefore = NULL;
    pathSearchTask->taskType = FULL_PUB;
    pathSearchTask->needSubPathNode = true;
    pathSearchTask->sub = summary->initLoadParam->sub;
    DbCreateList(&pathSearchTask->pvcTaskList, sizeof(PathPvcTaskT), pathSearchTask->eeMemCtx);
}

static Status FulfillPvcTask4InitLoad(
    PathSearchTaskNewT *pathSearchTask, PathPvcTaskT *pvcTask, DmVertexLabelT *vertexLabel)
{
    DB_POINTER3(pathSearchTask, pvcTask, vertexLabel);
    pvcTask->eeMemCtx = pathSearchTask->eeMemCtx;
    pvcTask->subEvent = DM_SUBS_EVENT_REPLACE;
    pvcTask->vertexLabel = vertexLabel;
    DbListT *subSearchTaskList = DbDynMemCtxAlloc(pvcTask->eeMemCtx, sizeof(DbListT));
    if (subSearchTaskList == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc taskList, the size is %zu.", sizeof(DbListT));
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateList(subSearchTaskList, sizeof(SubSearchTaskT), pvcTask->eeMemCtx);
    pvcTask->psTasks = subSearchTaskList;
    return GMERR_OK;
}

static BeginEndFlagE GetBeginEndFlag(DmSubscriptionT *sub)
{
    DB_POINTER(sub);
    if (sub->subMsgTypes[DM_SUBS_EVENT_INIT_BEGIN] != 0 && sub->subMsgTypes[DM_SUBS_EVENT_INIT_END] != 0) {
        return BEGINEND_PATH_TASK;
    } else if (sub->subMsgTypes[DM_SUBS_EVENT_INIT_BEGIN] != 0) {
        return BEGIN_PATH_TASK;
    } else if (sub->subMsgTypes[DM_SUBS_EVENT_INIT_END] != 0) {
        return END_PATH_TASK;
    }
    return NORMAL_PATH_TASK;
}

typedef struct SubSearchTaskParam {
    DmComplexPathInfoT *pathInfo;
    DmVertexLabelT *vertexLabel;
    PathTrigPlanCtxT *pathTrigPlanCtx;
    DbListT tupleList;
} SubSearchTaskParamT;

inline static void DestroySlotsInOpBatchEntryData(DbMemCtxT *memCtx, OpBatchEntryDataT *opBatchEntryData)
{
    for (uint32_t i = 0; i < opBatchEntryData->slotNum; ++i) {
        if (opBatchEntryData->slot[i] != NULL) {
            DbDynMemCtxFree(memCtx, opBatchEntryData->slot[i]);
        }
    }
    DbDynMemCtxFree(memCtx, opBatchEntryData->slot);
}

static Status FulfillSubSearchTask4InitLoad(
    SubSearchTaskT *subSearchTask, PathSummaryT *summary, PathPvcTaskT *pvcTask, SubSearchTaskParamT *param)
{
    DB_POINTER4(subSearchTask, summary, pvcTask, param);
    subSearchTask->pathInfo = param->pathInfo;
    subSearchTask->plan = &param->pathTrigPlanCtx->planCtx->plan;
    subSearchTask->beginEndFlag = (uint8_t)GetBeginEndFlag(summary->initLoadParam->sub);
    subSearchTask->oplog.slotNum = DbListGetItemCnt(&param->tupleList);
    if (subSearchTask->oplog.slotNum == 0) {
        subSearchTask->oplog.slot = NULL;
        return GMERR_OK;
    }
    uint32_t size = subSearchTask->oplog.slotNum * (uint32_t)sizeof(AASlotT *);
    subSearchTask->oplog.slot = (AASlotT **)DbDynMemCtxAlloc(pvcTask->eeMemCtx, size);
    if (subSearchTask->oplog.slot == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc slots of subSearchTask. the size is %u.", size);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(subSearchTask->oplog.slot, size, 0x00, size);
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < subSearchTask->oplog.slotNum; ++i) {
        subSearchTask->oplog.slot[i] = (AASlotT *)DbDynMemCtxAlloc(pvcTask->eeMemCtx, sizeof(AASlotT));
        if (subSearchTask->oplog.slot[i] == NULL) {
            DB_LOG_ERROR(
                GMERR_OUT_OF_MEMORY, "Unable to alloc memory for oplog.slot. the size is %zu.", sizeof(AASlotT));
            ret = GMERR_OUT_OF_MEMORY;
            goto EXIT5;
        }
        *subSearchTask->oplog.slot[i] = (AASlotT){0};
        HeapTupleBufT *tuple = (HeapTupleBufT *)DbListItem(&param->tupleList, i);
        ret = DmDeSerializeVertexWithMemCtx(
            pvcTask->eeMemCtx, tuple->buf, tuple->bufSize, param->vertexLabel, &subSearchTask->oplog.slot[i]->dmVertex);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            goto EXIT5;
        }
    }
    return GMERR_OK;
EXIT5:
    DestroySlotsInOpBatchEntryData(pvcTask->eeMemCtx, &subSearchTask->oplog);
    subSearchTask->oplog.slot = NULL;
    subSearchTask->oplog.slotNum = 0;
    return ret;
}

#define MAX_FETCH_RECORD_NUM 100

static Status FetchTuplesWithFilter(PathSummaryT *summary, DmComplexPathInfoT *pathInfo, DbListT *tupleList)
{
    DB_POINTER3(summary, pathInfo, tupleList);
    DmPathConstraintT *constraint = pathInfo->constraint;
    DbListT *listFilterConds;
    Status ret = ConvertScanCond(summary->memCtx, constraint, &listFilterConds);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    HpScanSubsCondDataT condData = (HpScanSubsCondDataT){
        .userScanRowProc = SRMapFilter,
        .userData = listFilterConds,
    };
    BatchedCursorBeginCfgT cfg = (BatchedCursorBeginCfgT){
        .memCtx = summary->memCtx, .tableId = constraint->vertexLabelId, .condData = &condData};
    BatchedCursorT *cursor = NULL;
    ret = BatchedCursorAllocAndInit(&cfg, &cursor);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT1;
    }
    ret = BatchedCursorOpen(cursor, summary->readViewSession);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT2;
    }
    HpFetchedAuxInfoT auxInfo = {0};
    HeapTupleBufT heapTupleBufs[MAX_FETCH_RECORD_NUM];
    while (!auxInfo.isEof) {
        ret = BatchedCursorNext(cursor, heapTupleBufs, MAX_FETCH_RECORD_NUM, &auxInfo);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            goto EXIT3;
        }
        for (uint32_t i = 0; i < auxInfo.actualFetchRowsCnt; ++i) {
            ret = DbAppendListItem(tupleList, &heapTupleBufs[i]);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                goto EXIT3;
            }
        }
    }
    BatchedCursorClose(cursor);
    BatchedCursorFree(cursor);
    DestroyFilterConds(summary->memCtx, listFilterConds);
    return GMERR_OK;

EXIT3:
    DbDestroyList(tupleList);
    BatchedCursorClose(cursor);
EXIT2:
    BatchedCursorFree(cursor);
EXIT1:
    DestroyFilterConds(summary->memCtx, listFilterConds);
    return ret;
}

void DestroySubSearchTaskParam(PathSummaryT *summary, SubSearchTaskParamT *param)
{
    DB_POINTER(param);
    if (param->pathTrigPlanCtx != NULL) {
        (void)PathTrigReleasePlanById(summary->initLoadParam->planKey);
    }
    if (param->vertexLabel != NULL) {
        (void)CataReleaseVertexLabel(param->vertexLabel);
    }
    if (DbListGetItemCnt(&param->tupleList)) {
        DbDestroyList(&param->tupleList);
    }
    if (param->pathInfo != NULL) {
        (void)CataReleaseComplexPathInfo(param->pathInfo);
    }
}

Status GenerateSubSearchTaskParam(PathSummaryT *summary, SubSearchTaskParamT *param)
{
    DmComplexPathInfoT *pathInfo = NULL;
    Status ret = CataGetComplexPathInfoById(summary->initLoadParam->pathId, &pathInfo);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Unable to get path for init load event. the sub is %s.",
            summary->initLoadParam->sub->metaCommon.metaName);
        return ret;
    }
    param->pathInfo = pathInfo;
    // 1.fetch datas from SE
    // 2.filter data by predicates of path
    DbCreateList(&param->tupleList, sizeof(HeapTupleBufT), summary->memCtx);
    ret = FetchTuplesWithFilter(summary, pathInfo, &param->tupleList);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DestroySubSearchTaskParam(summary, param);
        return ret;
    }

    // 3.generate trigger data
    // 4.generate path search task
    DmVertexLabelT *vertexLabel = NULL;
    ret = CataGetVertexLabelById(
        DbGetInstanceByMemCtx(summary->memCtx), pathInfo->constraint->vertexLabelId, &vertexLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DestroySubSearchTaskParam(summary, param);
        return ret;
    }
    param->vertexLabel = vertexLabel;
    PathTrigPlanCtxT *planCtx = NULL;
    ret = PathTrigGetPlanById(summary->initLoadParam->planKey, &planCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DestroySubSearchTaskParam(summary, param);
        return ret;
    }
    param->pathTrigPlanCtx = planCtx;
    return ret;
}

Status ProcessInitLoadSummary(PathSummaryT *summary, PathHistoryQueueT *queue)
{
    DB_POINTER2(summary, queue);
    SubSearchTaskParamT param = {0};
    Status ret = GenerateSubSearchTaskParam(summary, &param);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // 失败时内部完成资源释放
        goto RELEASE_SUB;
    }
    PathSearchTaskNewT pathSearchTask = {0};
    FulfillPathSearchTask4InitLoad(&pathSearchTask, summary);
    // generate PvcTask
    PathPvcTaskT pvcTask = {0};
    ret = FulfillPvcTask4InitLoad(&pathSearchTask, &pvcTask, param.vertexLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DestroySubSearchTaskParam(summary, &param);
        goto RELEASE_SUB;
    }
    // generate SubSearchTask
    SubSearchTaskT subSearchTask = {0};
    ret = FulfillSubSearchTask4InitLoad(&subSearchTask, summary, &pvcTask, &param);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT3_1;
    }
    // append SubSearchTask into PvcTask
    ret = DbAppendListItem(pvcTask.psTasks, &subSearchTask);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT4;
    }
    // append PvcTask into PathSearchTask
    ret = DbAppendListItem(&pathSearchTask.pvcTaskList, &pvcTask);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT5;
    }
    // 5.preOccupy sub node
    ret = QrySubPathGetNode(&pathSearchTask.subNode);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Unable to occupy sub node for init load event. the sub is %s.",
            summary->initLoadParam->sub->metaCommon.metaName);
        goto EXIT6;
    }
    // 5.run path searching
    PathSearchProcessEntry4South(queue, &pathSearchTask);
    DestroySubSearchTaskParam(summary, &param);
    return GMERR_OK;
EXIT6:
    DbDestroyList(&pathSearchTask.pvcTaskList);
EXIT5:
    DbDestroyList(pvcTask.psTasks);
EXIT4:
    DestroySlotsInOpBatchEntryData(pvcTask.eeMemCtx, &subSearchTask.oplog);
EXIT3_1:
    DbDynMemCtxFree(pvcTask.eeMemCtx, pvcTask.psTasks);
    DestroySubSearchTaskParam(summary, &param);
RELEASE_SUB:
    (void)CataReleaseSubscription(summary->initLoadParam->sub);
    return ret;
}
