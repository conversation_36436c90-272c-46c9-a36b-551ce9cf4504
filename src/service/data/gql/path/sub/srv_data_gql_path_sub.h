/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author:
 * Create: 2024-04-07
 */
#ifndef SRV_DATA_GQL_PATH_SUB_H
#define SRV_DATA_GQL_PATH_SUB_H

#include "db_mem_context.h"
#include "db_list.h"
#include "dm_meta_subscription.h"
#include "srv_data_gql_path_summary.h"
#include "srv_path_history_task_queue.h"

#ifdef __cplusplus
extern "C" {
#endif

Status CreateInitLoadSubWork(DbMemCtxT *memCtx, const DbListT *subObjects, const DmSubscriptionT *sub);
Status RemoveInitLoadSubWork(const DmSubscriptionT *sub);

Status ProcessInitLoadSummary(PathSummaryT *summary, PathHistoryQueueT *queue);

#ifdef __cplusplus
}
#endif
#endif  // SRV_DATA_GQL_PATH_SUB_H
