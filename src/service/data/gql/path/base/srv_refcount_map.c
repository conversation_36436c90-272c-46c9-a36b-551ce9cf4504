/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: implementation file for RefCountMap
 * Create: 2023-09-19
 */

#include "srv_refcount_map.h"
#include "adpt_atomic.h"

#define HASHMAP_INITIAL_CAPACITY (1024)

Status RefCountMapInit(RefCountMapT *refCountMapPtr, DbMemCtxT *memCtx)
{
    DB_POINTER2(refCountMapPtr, memCtx);
    refCountMapPtr->memCtx = memCtx;
    refCountMapPtr->refCountMap = DbDynMemCtxAlloc(memCtx, sizeof(DbOamapT));
    if (refCountMapPtr->refCountMap == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc refCountMap in RefCountMapInit");
        return GMERR_OUT_OF_MEMORY;
    }
    refCountMapPtr->batchId = 0;
    return DbOamapInit(refCountMapPtr->refCountMap, HASHMAP_INITIAL_CAPACITY, DbOamapUint32Compare, memCtx, true);
}

uint32_t GetNewBatchId(RefCountMapT *refCountMapPtr)
{
    DB_POINTER(refCountMapPtr);
    uint32_t *refCountRetPtr = NULL;
    uint32_t batchId;
    do {
        batchId = DbAtomicFetchAndAdd(&(refCountMapPtr->batchId), 1);
        refCountRetPtr = DbOamapLookupWithLock(refCountMapPtr->refCountMap, batchId, &batchId, NULL);
    } while (refCountRetPtr != NULL);
    return batchId;
}

Status RefCountMapInsert(RefCountMapT *refCountMapPtr, uint32_t batchId, uint32_t refCount)
{
    DB_POINTER(refCountMapPtr);
    uint32_t *batchIdPtr = DbDynMemCtxAlloc(refCountMapPtr->memCtx, sizeof(uint32_t));
    if (batchIdPtr == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc batchIdPtr in RefCountMapInsert");
        return GMERR_OUT_OF_MEMORY;
    }
    uint32_t *refCountPtr = DbDynMemCtxAlloc(refCountMapPtr->memCtx, sizeof(uint32_t));
    if (refCountPtr == NULL) {
        DbDynMemCtxFree(refCountMapPtr->memCtx, batchIdPtr);
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc refCountPtr in RefCountMapInsert");
        return GMERR_OUT_OF_MEMORY;
    }
    *batchIdPtr = batchId;
    *refCountPtr = refCount;
    return DbOamapInsertWithLock(refCountMapPtr->refCountMap, batchId, batchIdPtr, refCountPtr, NULL);
}

Status RefCountMapRemoveBatch(RefCountMapT *refCountMapPtr, uint32_t batchId)
{
    DB_POINTER(refCountMapPtr);
    uint32_t *batchIdPtr;
    uint32_t *refCountPtr;
    Status ret = DbOamapRemoveKvWithLock(
        refCountMapPtr->refCountMap, batchId, &batchId, (void **)&batchIdPtr, (void **)&refCountPtr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    DbDynMemCtxFree(refCountMapPtr->memCtx, batchIdPtr);
    DbDynMemCtxFree(refCountMapPtr->memCtx, refCountPtr);
    return GMERR_OK;
}

Status DecreaseAndGetRefCount(RefCountMapT *refCountMapPtr, uint32_t batchId, uint32_t *refCountPtr)
{
    DB_POINTER2(refCountMapPtr, refCountPtr);
    uint32_t *refCountRetPtr = DbOamapLookupWithLock(refCountMapPtr->refCountMap, batchId, &batchId, NULL);
    if (refCountRetPtr == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Unable to find batchId in the refCountMap");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    *refCountPtr = DbAtomicDec(refCountRetPtr);
    if (*refCountPtr == UINT32_MAX) {
        *refCountPtr = DbAtomicInc(refCountRetPtr);
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "RefCount became negative");
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

Status DecreaseRefCount(RefCountMapT *refCountMapPtr, uint32_t batchId)
{
    DB_POINTER(refCountMapPtr);
    uint32_t *refCountRetPtr = DbOamapLookupWithLock(refCountMapPtr->refCountMap, batchId, &batchId, NULL);
    if (refCountRetPtr == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Unable to find batchId in the refCountMap");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    uint32_t refCountPtr = DbAtomicDec(refCountRetPtr);
    if (refCountPtr == UINT32_MAX) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "RefCount became negative");
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

void RefCountMapDestroy(RefCountMapT *refCountMapPtr)
{
    DB_POINTER(refCountMapPtr);
    DbOamapDestroy(refCountMapPtr->refCountMap);
}

Status GetRefCount(RefCountMapT *refCountMapPtr, uint32_t batchId, uint32_t *refCountPtr)
{
    DB_POINTER2(refCountMapPtr, refCountPtr);
    uint32_t *refCountRetPtr = DbOamapLookupWithLock(refCountMapPtr->refCountMap, batchId, &batchId, NULL);
    if (refCountRetPtr == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Unable to find batchId in the refCountMap");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    *refCountPtr = *refCountRetPtr;
    return GMERR_OK;
}
