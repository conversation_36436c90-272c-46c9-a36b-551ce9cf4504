/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: implementation file for RefCountMap
 * Create: 2023-09-19
 */

#ifndef SRV_REFCOUNT_MAP_H
#define SRV_REFCOUNT_MAP_H
#include "db_hashmap.h"

#ifdef __cplusplus
extern "C" {
#endif

/*
 * RefCountMap structure stores <batchId,refCount> pairs
 */
typedef struct RefCountMap {
    DbMemCtxT *memCtx;
    DbOamapT *refCountMap;
    uint32_t batchId;
} RefCountMapT;

/*
 * @brief Create RefCountMap. RefCountMapDestroy must be called to free it.
 * @param[in] refCountMapPtr: pointer to already allocated RefCountMap structure
 * @param[in] memCtx: memory context
 * @return ok if RefCountMap is created successfully
 */
Status RefCountMapInit(RefCountMapT *refCountMapPtr, DbMemCtxT *memCtx);

/*
 * @brief Get a new batch id that doesn't already exist in the RefCountMap
 * @param[in] refCountMapPtr: pointer to initialized RefCountMap
 * @return the new batch id
 */
uint32_t GetNewBatchId(RefCountMapT *refCountMapPtr);

/*
 * @brief Insert a new <batchId, refCount> pair in the RefCountMap
 * @param[in] refCountMapPtr: pointer to initialized RefCountMap
 * @param[in] batchId: batchId to be used as key in the map
 * @param[in] refCount: refCount to be used as value in the map
 * @return ok if inserted successfully
 */
Status RefCountMapInsert(RefCountMapT *refCountMapPtr, uint32_t batchId, uint32_t refCount);

/*
 * @brief Deletes a <batchId, refCount> pair from the map and frees the pair's memory
 * @param[in] refCountMapPtr: pointer to initialized RefCountMap
 * @param[in] batchId: batchId to be removed from the map
 * @return ok if deleted successfully
 */
Status RefCountMapRemoveBatch(RefCountMapT *refCountMapPtr, uint32_t batchId);

/*
 * @brief Atomically decreases the reference count of a batch by 1 and returns the value after decreasing.
 * @param[in] refCountMapPtr: pointer to initialized RefCountMap
 * @param[in] batchId: batchId whose reference counter should be decreased
 * @param[out] refCountPtr: a pointer to some memory in order to write the returned reference count.
 * @return ok if deleted successfully
 */
Status DecreaseAndGetRefCount(RefCountMapT *refCountMapPtr, uint32_t batchId, uint32_t *refCountPtr);

/*
 * @brief Atomically decreases the reference count of a batch by 1.
 * @param[in] refCountMapPtr: pointer to initialized RefCountMap
 * @param[in] batchId: batchId whose reference counter should be decreased
 * @return ok if deleted successfully
 */
Status DecreaseRefCount(RefCountMapT *refCountMapPtr, uint32_t batchId);

/*
 * @brief Get ref count of a batch
 * @param[in] refCountMapPtr: pointer to initialized RefCountMap
 * @param[in] batchId: batchId whose reference counter should be decreased
 * @param[out] refCountPtr: a pointer to some memory in order to write the returned reference count.
 * @return ok if deleted successfully
 */
Status GetRefCount(RefCountMapT *refCountMapPtr, uint32_t batchId, uint32_t *refCountPtr);

/*
 * @brief Deletes the RefCountMap. RefCountMapRemoveBatch should be called for every batch before calling destroy
 * @param[in] refCountMapPtr: pointer to initialized RefCountMap
 * @return ok if destroyed successfully
 */
void RefCountMapDestroy(RefCountMapT *refCountMapPtr);

#ifdef __cplusplus
}
#endif

#endif  // SRV_REFCOUNT_MAP_H
