/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: service for gql
 * Author: GQL Team
 * Create: 2023-10-31
 */
#include "srv_data_utils.h"

#include "adpt_sleep.h"
#include "db_sysapp_context.h"
#include "dm_meta_pathtrigger_info.h"
#include "cpl_ir_logical_op.h"
#include "cpl_optimizer_cascades.h"

static Status GenerateExecPlanByIrPlans(DbMemCtxT *memCtx, DbListT *irPlans, PlanCtxT **execPlan)
{
    uint32_t nspId = 0;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(memCtx);
    Status ret = CataGetNamespaceIdByName(dbInstance, PUBLIC_NAMESPACE_NAME, &nspId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(
            ret, "Unable to get namespace id by name: %s when create execute plan.", PUBLIC_NAMESPACE_NAME);
        return ret;
    }
    PlanCtxT *tmpExecPlan = DbDynMemCtxAlloc(memCtx, sizeof(PlanCtxT));
    if (tmpExecPlan == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc exec plan, the size is %zu.", sizeof(PlanCtxT));
        return GMERR_OUT_OF_MEMORY;
    }
    InitPlanCtx(tmpExecPlan, memCtx, 0);
    tmpExecPlan->memCtx = memCtx;
    IRPlanT *irPhyPlan = (IRPlanT *)DbListItem(irPlans, 0);
    ret = CreateExecPlanWithMemCtx(memCtx, nspId, irPhyPlan, tmpExecPlan, MODEL_GQL);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        ReleaseLabelsInPathPlan(tmpExecPlan);
        return ret;
    }
    *execPlan = tmpExecPlan;
    return ret;
}

Status GeneratePathTrigPlanCtxForPathSearching(DbMemCtxT *memCtx, IRPlanT *pathIRPlan, PathTrigPlanCtxT **planCtx)
{
    DB_POINTER3(memCtx, pathIRPlan, planCtx);
    DbMemCtxT *planCacheMemCtx = PathTrigGetPlanCacheMemCtx();
    if (planCacheMemCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "plan cache not inited. plan cannot be created");
        return GMERR_INTERNAL_ERROR;
    }
    DbListT irPlans = {0};
    DbCreateList(&irPlans, sizeof(IRPlanT), memCtx);
    Status ret = DbAppendListItem(&irPlans, pathIRPlan);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = OptQryOptimize(memCtx, &irPlans);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbDestroyList(&irPlans);
        return ret;
    }
    DbMemCtxArgsT args = {0};
    DbMemCtxT *childMemCtx = (DbMemCtxT *)DbCreateDynMemCtx(planCacheMemCtx, false, "PLAN_CACHE_NODE", &args);
    if (childMemCtx == NULL) {
        DbDestroyList(&irPlans);
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc childMemCtx.");
        return GMERR_OUT_OF_MEMORY;
    }
    PathTrigPlanCtxT *tmpPlanCtx = (PathTrigPlanCtxT *)DbDynMemCtxAlloc(childMemCtx, sizeof(PathTrigPlanCtxT));
    if (tmpPlanCtx == NULL) {
        DbDestroyList(&irPlans);
        DbDeleteDynMemCtx(childMemCtx);
        DB_LOG_AND_SET_LASERR(
            GMERR_OUT_OF_MEMORY, "Unable to alloc plan ctx, the size is %zu.", sizeof(PathTrigPlanCtxT));
        return GMERR_OUT_OF_MEMORY;
    }
    PlanCtxT *tmpExecPlan = NULL;
    ret = GenerateExecPlanByIrPlans(childMemCtx, &irPlans, &tmpExecPlan);
    DbDestroyList(&irPlans);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbDeleteDynMemCtx(childMemCtx);
        return ret;
    }
    *tmpPlanCtx = (PathTrigPlanCtxT){.memCtx = childMemCtx, .planCtx = tmpExecPlan, .createCnt = 0, .isDeleted = false};
    *planCtx = tmpPlanCtx;
    return ret;
}

Status SavePathPlan2PlanCache(
    DbMemCtxT *memCtx, IRPlanT *irPlan, uint32_t labelId, DmPathTrigDmlE eventType, uint32_t *planKey)
{
    PathTrigPlanCtxT *pathPlanCtx = NULL;
    Status ret = GeneratePathTrigPlanCtxForPathSearching(memCtx, irPlan, &pathPlanCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    PlanCacheKeyT planCacheKey = {.pathId = labelId, .dmlType = eventType};
    uint32_t key = PlanCacheKey2Hash32(&planCacheKey);
    ret = PathTrigSavePlan(key, pathPlanCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        ReleaseLabelsInPathPlan(pathPlanCtx->planCtx);
        DbDeleteDynMemCtx(pathPlanCtx->memCtx);
    }
    if (ret == GMERR_DUPLICATE_OBJECT) {
        ret = GMERR_OK;
    }
    if (planKey != NULL) {
        *planKey = key;
    }
    return ret;
}

Status SavePathPlans2PlanCache(DbMemCtxT *memCtx, const DbListT *irPlans, DmPathTrigDmlE eventType)
{
    Status ret = GMERR_OK;
    uint32_t irPlansCount = DbListGetItemCnt(irPlans);
    IRPlanT **irPlanItems = (IRPlanT **)DbListGetItems(irPlans);
    uint32_t succCnt = 0;
    for (uint32_t i = 0; i < irPlansCount; i++) {
        IRPlanT *irPlan = irPlanItems[i];
        OpLogicalAAPathTraversalT *base = (OpLogicalAAPathTraversalT *)(void *)irPlan->root->op;
        ret = SavePathPlan2PlanCache(memCtx, irPlan, base->pathInfo->metaCommon.metaId, eventType, NULL);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            goto EXIT;
        }
        succCnt++;
    }
    return GMERR_OK;

EXIT:
    for (uint32_t i = 0; i < succCnt; ++i) {
        IRPlanT *irPlan = irPlanItems[i];
        OpLogicalAAPathTraversalT *base = (OpLogicalAAPathTraversalT *)(void *)irPlan->root->op;
        PlanCacheKeyT planKey = {.pathId = base->pathInfo->metaCommon.metaId, .dmlType = eventType};
        uint32_t key = PlanCacheKey2Hash32(&planKey);
        (void)PathTrigDelPlanById(key);
    }
    return ret;
}

Status RemovePathPlansFromPlanCache(const DbListT *pathIds, DmPathTrigDmlE eventType)
{
    DB_POINTER(pathIds);
    Status ret = GMERR_OK;
    uint32_t planCnt = DbListGetItemCnt(pathIds);
    for (uint32_t i = 0; i < planCnt; ++i) {
        uint32_t pathId = *(uint32_t *)DbListItem(pathIds, i);
        DB_UNUSED(pathId);
        PlanCacheKeyT planKey = (PlanCacheKeyT){.pathId = pathId, .dmlType = eventType};
        uint32_t key = PlanCacheKey2Hash32(&planKey);
        ret = PathTrigDelPlanById(key);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    return GMERR_OK;
}

uint32_t PlanCacheKey2Hash32(const PlanCacheKeyT *planKey)
{
    DB_POINTER(planKey);

    static_assert(offsetof(PlanCacheKeyT, pathId) == 0, "offset of pathId should be zero");
    static_assert(offsetof(PlanCacheKeyT, dmlType) == sizeof(planKey->pathId), "dmlType should directly follow pathId");

    XXH32_state_t state;
    (void)XXH32_reset(&state, DB_XXHASH_SEED);

    const void *data = planKey;
    size_t size = sizeof(planKey->pathId) + sizeof(planKey->dmlType);
    (void)XXH32_update(&state, data, size);

    return XXH32_digest(&state);
}

static BatchDmlPreAllocBufferT *g_preAllocBufferForBatchDml = NULL;

Status CreateBatchPreAllocMem(void)
{
    DbMemCtxT *appDynCtx = DbSrvGetAppDynCtx(DbGetProcGlobalId());
    if (SECUREC_UNLIKELY(appDynCtx == NULL)) {
        return GMERR_INTERNAL_ERROR;
    }
    // memCtx用途：path history memory
    // 生命周期：长进程级别
    // 释放方式：兜底清空
    // 兜底清空措施：1.下方初始化流程失败时销毁；2.pathService销毁时销毁
    DbMemCtxArgsT args = {0};
    args.dynCtxNeedRecycle = false;
    DbMemCtxT *memCtx = DbCreateDynMemCtx(appDynCtx, true, "PATH_BATCH_DML_MEM", &args);
    if (SECUREC_UNLIKELY(memCtx == NULL)) {
        return GMERR_OUT_OF_MEMORY;
    }
    g_preAllocBufferForBatchDml = (BatchDmlPreAllocBufferT *)DbDynMemCtxAlloc(memCtx, sizeof(BatchDmlPreAllocBufferT));
    if (SECUREC_UNLIKELY(g_preAllocBufferForBatchDml == NULL)) {
        DbDeleteDynMemCtx(memCtx);
        return GMERR_OUT_OF_MEMORY;
    }
    for (uint32_t i = 0; i < PRE_ALLOC_BUFFER_FOR_BATCH_DML_NUM; i++) {
        g_preAllocBufferForBatchDml->preAllocMem[i].preAllocBuffer.buffer =
            DbDynMemCtxAlloc(memCtx, PRE_ALLOC_BUFFER_FOR_BATCH_DML_SINGLE_SIZE);
        if (SECUREC_UNLIKELY(g_preAllocBufferForBatchDml == NULL)) {
            DbDeleteDynMemCtx(memCtx);
            return GMERR_OUT_OF_MEMORY;
        }
        g_preAllocBufferForBatchDml->preAllocMem[i].preAllocBuffer.bufferSize =
            PRE_ALLOC_BUFFER_FOR_BATCH_DML_SINGLE_SIZE;
    }
    return GMERR_OK;
}

BatchDmlPreAllocMemT *GetBatchDmlPreAllocMem(void)
{
    if (g_preAllocBufferForBatchDml == NULL) {
        return NULL;
    }
    DbSpinLock(&g_preAllocBufferForBatchDml->lock);
    if (g_preAllocBufferForBatchDml->usedNum == PRE_ALLOC_BUFFER_FOR_BATCH_DML_NUM) {
        DbSpinUnlock(&g_preAllocBufferForBatchDml->lock);
        return NULL;
    }
    for (uint32_t i = 0; i < PRE_ALLOC_BUFFER_FOR_BATCH_DML_NUM; i++) {
        if (!g_preAllocBufferForBatchDml->preAllocMem[i].isUsed) {
            g_preAllocBufferForBatchDml->preAllocMem[i].isUsed = true;
            g_preAllocBufferForBatchDml->usedNum++;
            DbSpinUnlock(&g_preAllocBufferForBatchDml->lock);
            g_preAllocBufferForBatchDml->preAllocMem[i].preAllocBuffer.bufferCursor =
                g_preAllocBufferForBatchDml->preAllocMem[i].preAllocBuffer.buffer;
            return g_preAllocBufferForBatchDml->preAllocMem + i;
        }
    }
    DbSpinUnlock(&g_preAllocBufferForBatchDml->lock);
    return NULL;
}

void ReleaseBatchDmlPreAllocMem(BatchDmlPreAllocMemT *preAllocMem)
{
    if (g_preAllocBufferForBatchDml == NULL || preAllocMem == NULL) {
        return;
    }
    DbSpinLock(&g_preAllocBufferForBatchDml->lock);
    preAllocMem->isUsed = false;
    g_preAllocBufferForBatchDml->usedNum--;
    DbSpinUnlock(&g_preAllocBufferForBatchDml->lock);
}
#define SESSION_TRY_ALLOC_SLEEP_TIME 1000

Status GqlPathTryAllocSession(SessionT **session)
{
    // alloc session
    uint32_t sleepCnt = 0;
    Status ret = GMERR_OK;
    SessionT *tmpSession;
    while (sleepCnt < SESSION_TRY_ALLOC_SLEEP_TIME) {
        ret = QrySessionAlloc(
            SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, DbGetInstanceByMemCtx(session->memCtx), &tmpSession);
        if (ret == GMERR_OK) {
            break;
        }
        DbUsleep(SESSION_TRY_ALLOC_SLEEP_TIME);
        sleepCnt++;
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to alloc session in MiniBatchTaskEntry.");
        return ret;
    }
    *session = tmpSession;
    return ret;
}
