/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: path data service
 * Author: GQL Team
 * Create: 2023-10-31
 */

#ifndef GMDBV5_SRV_DATA_UTILS_H
#define GMDBV5_SRV_DATA_UTILS_H

#include <xxh3.h>
#include "db_hash.h"
#include "ee_stmt_fusion.h"
#include "ee_session_interface.h"
#include "cpl_gql_compiler.h"
#include "cpl_planner.h"
#include "cpl_optimizer.h"
#include "srv_path_trigger_action.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

typedef struct PlanCacheKey {
    uint32_t pathId;
    DmPathTrigDmlE dmlType;
} PlanCacheKeyT;

uint32_t PlanCacheKey2Hash32(const PlanCacheKeyT *planKey);

Status SavePathPlan2PlanCache(
    DbMemCtxT *memCtx, IRPlanT *irPlan, uint32_t labelId, DmPathTrigDmlE eventType, uint32_t *planKey);

Status SavePathPlans2PlanCache(DbMemCtxT *memCtx, const DbListT *irPlans, DmPathTrigDmlE eventType);

Status RemovePathPlansFromPlanCache(const DbListT *pathIds, DmPathTrigDmlE eventType);

Status GeneratePathTrigPlanCtxForPathSearching(DbMemCtxT *memCtx, IRPlanT *pathIRPlan, PathTrigPlanCtxT **planCtx);

Status GenerateSaveSRPhysicalPlans(DbMemCtxT *memCtx, const DbListT *irPlans);

#define PRE_ALLOC_BUFFER_FOR_BATCH_DML_SINGLE_SIZE (200 * 1024)
#define PRE_ALLOC_BUFFER_FOR_BATCH_DML_NUM 18

typedef struct BatchDmlPreAllocMem {
    PreAllocBufferT preAllocBuffer;
    bool isUsed;
} BatchDmlPreAllocMemT;

typedef struct BatchDmlPreAllocBuffer {
    DbSpinLockT lock;
    uint32_t usedNum;  // 正在被使用的数量
    BatchDmlPreAllocMemT preAllocMem[PRE_ALLOC_BUFFER_FOR_BATCH_DML_NUM];
} BatchDmlPreAllocBufferT;

Status CreateBatchPreAllocMem(void);

BatchDmlPreAllocMemT *GetBatchDmlPreAllocMem(void);

void ReleaseBatchDmlPreAllocMem(BatchDmlPreAllocMemT *preAllocMem);

Status GqlPathTryAllocSession(SessionT **session);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* GMDBV5_SRV_DATA_UTILS_H */
