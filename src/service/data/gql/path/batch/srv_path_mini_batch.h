/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Definition and interface of mini batch in path
 * Author: GQL
 * Create: 2023-09-21
 */

#ifndef SRV_PATH_MINI_BATCH_H
#define SRV_PATH_MINI_BATCH_H

#include "ee_session_interface.h"
#include "cpl_planner.h"
#include "cpl_gql_capi_compiler.h"
#include "srv_data_utils.h"
#include "srv_data_gql_path_summary.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    DbMemCtxT *memCtx;
    uint32_t miniTaskId;
    SessionT *session;               // dml session
    PathSummaryT *summary;           // batch summary
    PathMiniSummaryT *miniSummary;   // mini summary
    uint32_t *miniBatchCntPtr;       // pointer to the mini batch counter
    uint32_t *miniBatchAliveCntPtr;  // pointer to the mini batch alive counter
    uint16_t startId;
    uint16_t endId;
    PathBatchDmlT *batchDmls;
    WorkerMgrT *workerMgr;
    uint16_t workerTaskId;
    bool isCreateSucc;
} PathMiniBatchTaskT;

#define MAX_FRAGMENT_NUM 8
typedef struct PathBatchExecPara {
    DbMemCtxT *memCtx;
    PathSummaryT *summary;
    PathMiniBatchTaskT *miniBatchTasks;
    uint16_t fragmentationIds[MAX_FRAGMENT_NUM];  // end point of each mini batch, dml pointed by end point not
                                                  // included, [, end_point)
    uint32_t miniBatchTotal;                      // total number of mini batch tasks
    uint32_t miniBatchAliveCnt;                   // cnt of mini batch tasks alive during pre-commit/commit
    uint32_t miniBatchCnt;                        // cnt for mini batch will be executed
    uint32_t batchOriginId;
    bool isNormalBatch;
    bool isMergeReplace;
    bool isReplaceInsert;
} PathBatchExecParaT;

// execute task for pre-commit
void MiniBatchTaskEntry(void *ctx, void *data);

// execute task for commit
void CommitMiniBatchTask(PathMiniBatchTaskT *task);

// init task
void InitMiniBatchTask(
    PathMiniBatchTaskT *task, PathBatchDmlT *batchDmls, PathSummaryT *summary, PathBatchExecParaT *execPara);

// create worker task
Status CreateWorkerMiniBatchTask(PathMiniBatchTaskT *task, DrtWorkerProcEntry miniBatchTaskEntry);

// destroy worker task
void DestroyWorkerMiniBatchTask(PathMiniBatchTaskT *task);

#ifdef __cplusplus
}
#endif

#endif
