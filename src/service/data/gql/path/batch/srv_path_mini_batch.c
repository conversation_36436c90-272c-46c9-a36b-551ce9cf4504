/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Implementations of mini batch in path
 * Author: GQL
 * Create: 2023-09-21
 */
#include "srv_path_mini_batch.h"

#include "adpt_sleep.h"
#include "db_sysapp_context.h"
#include "db_pre_allocated_mem.h"
#include "drt_instance.h"
#include "ee_plan.h"
#include "ee_session_interface.h"
#include "ee_session.h"
#include "ee_receiver.h"
#include "ee_plan_node.h"
#include "cpl_gql_capi_compiler.h"
#include "srv_data_gql_transaction.h"
#include "srv_data_gql_path_summary.h"
#include "cpl_optimizer_cascades.h"

void GqlInitBatchDmlStmt(DbMemCtxT *memCtx, SessionT *session, GqlQryStmtT *stmt)
{
    stmt->base.session = session;
    stmt->memCtx = memCtx;
    stmt->base.memCtx = memCtx;
    stmt->base.receiverType = GQL_BATCH_DML_RESULT;
    stmt->base.trxMemCtx = session->memCtx;
    stmt->base.needTimeoutSplit = false;
    stmt->base.needResult = true;
    stmt->base.totalParamNum = 0;
    stmt->base.subQueryList = NULL;
    stmt->base.isPrepareQry = false;
}
/**
 * 如果执行失败，内部回滚事务
 * @param session
 * @param execPlans
 * @param planCnt
 * @param miniSummary
 * @return
 */
static Status ExecutePlans(SessionT *session, PlanCtxT *execPlans, uint32_t planCnt, PathMiniSummaryT *miniSummary)
{
    GqlQryStmtT stmt = {0};
    GqlInitBatchDmlStmt(session->memCtx, session, &stmt);
    // alloc receiver to get result of executor
    Status ret = QryInitReceiver(miniSummary->memCtx, &stmt.base);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Unable to init receiver for mini batch task.");
        return ret;
    }
    // start transaction
    ret = GqlStartTransaction(session);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "unable to start transaction in MiniBatchTaskEntry.");
        return ret;
    }
    FusionModelQryStmtT *fusionStmt = (FusionModelQryStmtT *)(void *)&stmt;
    for (uint32_t i = 0; i < planCnt; i++) {
        fusionStmt->planTree = execPlans[i].plan.planTree;
        fusionStmt->trxMemCtx = fusionStmt->memCtx;
        ret = ExecutorStart(fusionStmt);
        if (ret == GMERR_OK) {
            ret = ExecutorRun(fusionStmt);
        }
        ExecutorFinish(fusionStmt);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Unable to execute one of plans in mini batch task.");
            break;
        }
        // set mini summary
        if (execPlans[i].plan.planTree->tagType == T_GQL_INSERT) {
            GqlInsertT *modifyPlan = (GqlInsertT *)(void *)execPlans[i].plan.planTree;
            ret = AddMiniSummaryInsertElement(modifyPlan, miniSummary, fusionStmt);
        } else if (execPlans[i].plan.planTree->tagType == T_GQL_REPLACE) {
            GqlReplaceT *modifyPlan = (GqlReplaceT *)(void *)execPlans[i].plan.planTree;
            ret = AddMiniSummaryElement(modifyPlan, miniSummary, fusionStmt);
        } else if (execPlans[i].plan.planTree->tagType == T_DELETE) {
            DeleteT *deletePlan = (DeleteT *)(void *)execPlans[i].plan.planTree;
            miniSummary->isReplaceOnSameTable = false;
            miniSummary->needReadViewBefore = true;
            ret = AddMiniSummaryDeleteElement(deletePlan, miniSummary, fusionStmt);
        }
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Unable to set mini summary.");
            break;
        }
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        GqlRollBackTransaction(session);
    }
    return ret;
}

Status PathQryOptimize(DbMemCtxT *memCtx, IrPlannedStmtT *irLogicalPlans, uint32_t planCnt)
{
    DB_POINTER2(memCtx, irLogicalPlans);
    DbListT plans;
    DbCreateList(&plans, sizeof(IRPlanT), memCtx);
    for (uint32_t i = 0; i < planCnt; i++) {
        Status ret = IRCheckPlan(irLogicalPlans[i].irPlan);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        ret = DbAppendListItem(&plans, irLogicalPlans[i].irPlan);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    Status ret = OptQryOptimize(memCtx, &plans);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    for (uint32_t i = 0; i < planCnt; i++) {
        irLogicalPlans[i].irPlan = (IRPlanT *)DbListItem(&plans, i);
    }
    return ret;
}

Status PathCreateExecPlan(
    DbMemCtxT *memCtx, IrPlannedStmtT *irPhysicalPlans, PlanCtxT **execPlans, uint32_t planCnt, bool needSkipOptimize)
{
    DB_POINTER2(memCtx, irPhysicalPlans);
    *execPlans = (PlanCtxT *)DbDynMemCtxAlloc(memCtx, sizeof(PlanCtxT) * planCnt);
    if (SECUREC_UNLIKELY(*execPlans == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "unable to alloc execPlan in PathCreateExecPlan.");
        return GMERR_OUT_OF_MEMORY;
    }
    uint32_t nspId = 0;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(memCtx);
    Status ret = CataGetNamespaceIdByName(dbInstance, PUBLIC_NAMESPACE_NAME, &nspId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(
            ret, "Unable to get namespace id by name: %s when create execute plan.", PUBLIC_NAMESPACE_NAME);
        return ret;
    }
    uint32_t i;
    for (i = 0; i < planCnt; i++) {
        PlanCtxT *execPlan = (*execPlans) + i;
        InitPlanCtx(execPlan, memCtx, 0);
        // 内存释放点: 异常分支ExecuteBatchDML; 执行计划后
        // optimization is skipped in the replace operation, so need to define op type.
        if (needSkipOptimize) {
            irPhysicalPlans[i].irPlan->root->op->type = IR_PHYOP_REPLACE;
        }
        ret = CreateExecPlanWithMemCtx(memCtx, nspId, irPhysicalPlans[i].irPlan, execPlan, MODEL_GQL);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    return GMERR_OK;
}

static uint32_t GetPlanCnt(PathMiniBatchDmlT *miniBatchDml)
{
    uint32_t planCnt = 0;
    uint32_t vtxLabelCnt = DbListGetItemCnt(&miniBatchDml->vertexLabelDmlList);
    for (uint32_t i = 0; i < vtxLabelCnt; i++) {
        PathMiniBatchVertexLabelDmlT *vertexLabelBatch =
            (PathMiniBatchVertexLabelDmlT *)DbListItem(&miniBatchDml->vertexLabelDmlList, i);
        if (SECUREC_LIKELY(DbListGetItemCnt(&vertexLabelBatch->replaceVertexBufList) > 0)) {
            planCnt++;
        }
        planCnt += DbListGetItemCnt(&vertexLabelBatch->delIndexKeyList);
        planCnt += DbListGetItemCnt(&vertexLabelBatch->deleteStructFilterList);
    }
    return planCnt;
}

static Status BuildExecPlans(DbMemCtxT *summaryMemCtx, DbMemCtxT *dmlPlanCtx, PathMiniBatchDmlT *miniBatchDml,
    PlanCtxT **execPlans, uint32_t planCnt)
{
    // analyze, generate logical plan
    IrPlannedStmtT *irLogicalPlans = NULL;
    Status ret = PathAnalyzeBatchDML(summaryMemCtx, dmlPlanCtx, &irLogicalPlans, miniBatchDml, planCnt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "unable to analyze batch dml.");
        return ret;
    }

    // FES only support replace and delete. If all plan is replace plan, it will skip optimization.
    bool needSkipOptimize = true;
    for (uint32_t i = 0; i < planCnt; i++) {
        IROpTypeE type = irLogicalPlans[i].irPlan->root->op->type;
        if (type != IR_LOGOP_REPLACE) {
            needSkipOptimize = false;
            break;
        }
    }
    if (!needSkipOptimize) {
        ret = PathQryOptimize(dmlPlanCtx, irLogicalPlans, planCnt);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "unable to optimize batch dml.");
            return ret;
        }
    }

    // planner, generate physical plan
    PlanCtxT *tmpPlans;
    ret = PathCreateExecPlan(dmlPlanCtx, irLogicalPlans, &tmpPlans, planCnt, needSkipOptimize);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "unable to generate execute plan for batch dml.");
        return ret;
    }
    *execPlans = tmpPlans;
    return GMERR_OK;
}

static Status SetPathMiniBatchDmlHelper(
    PathBatchDmlT *batchDmls, uint16_t startId, uint16_t endId, PathMiniBatchDmlT *miniBatchDml)
{
    DbCreateList(&miniBatchDml->vertexLabelDmlList, sizeof(PathMiniBatchVertexLabelDmlT), miniBatchDml->memCtx);
    for (uint16_t i = startId; i < endId; i++) {
        PathDmlT dml = batchDmls->dmls[i];
        Status ret = SetPathMiniBatchDml(&dml, miniBatchDml);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    return GMERR_OK;
}

inline static void GqlPathMiniBatchPreparePathMiniBatchDml(
    PathMiniBatchTaskT *task, BatchDmlPreAllocMemT *preAllocMem, PathMiniBatchDmlT *miniBatchDml)
{
    miniBatchDml->memCtx = task->memCtx;
    miniBatchDml->isInsertOnSameTable = task->batchDmls->isInsertOnSameTable;
    miniBatchDml->preAllocBuffer = &preAllocMem->preAllocBuffer;
}

Status GenerateExecPlan(DbMemCtxT *memCtx, PathMiniBatchTaskT *task, BatchDmlPreAllocMemT *preAllocMem,
    PlanCtxT **execPlans, uint32_t *planCnt)
{
    PathMiniBatchDmlT miniBatchDml;
    GqlPathMiniBatchPreparePathMiniBatchDml(task, preAllocMem, &miniBatchDml);

    Status ret = SetPathMiniBatchDmlHelper(task->batchDmls, task->startId, task->endId, &miniBatchDml);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to process mini batch in MiniBatchTaskEntry.");
        return ret;
    }

    uint32_t tmpPlanCnt = GetPlanCnt(&miniBatchDml);
    PlanCtxT *tmpExecPlans = NULL;
    ret = BuildExecPlans(memCtx, task->memCtx, &miniBatchDml, &tmpExecPlans, tmpPlanCnt);
    // if one mini batch creation is unsuccessful, continue to the next mini batch
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to build exec plans in MiniBatchTaskEntry.");
        return ret;
    }
    *execPlans = tmpExecPlans;
    *planCnt = tmpPlanCnt;
    return ret;
}

// task entry for pre-commit
void MiniBatchTaskEntry(void *ctx, void *data)
{
    DB_UNUSED(ctx);
    DB_POINTER(data);
    PathMiniBatchTaskT *task = (PathMiniBatchTaskT *)data;
    DB_POINTER2(task->summary, task->miniBatchCntPtr);
    PathSummaryT *summary = task->summary;
    BatchDmlPreAllocMemT *preAllocMem = GetBatchDmlPreAllocMem();

    uint32_t planCnt = 0;
    PlanCtxT *execPlans = NULL;
    Status ret = GenerateExecPlan(summary->memCtx, task, preAllocMem, &execPlans, &planCnt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to build exec plans in MiniBatchTaskEntry.");
        goto MARK_UNSUCCESSFUL_MINI_BATCH;
    }

    ret = GqlPathTryAllocSession(&task->session);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto MARK_UNSUCCESSFUL_MINI_BATCH;
    }
    // create mini summary
    PathMiniSummaryT *miniSummary = NULL;
    ret = CreatePathMiniSummary(
        summary->memCtx, &miniSummary, summary->isMergeReplace, task->batchDmls->isReplaceOnSameTable);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Unable to alloc mini summary for mini batch task.");
        goto RELEASE_SESSION;
    }
    // 如果失败，内部会回滚事务；如果成功，上层会统一提交，本函数不用管事务
    ret = ExecutePlans(task->session, execPlans, planCnt, miniSummary);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Unable to execute plan in MiniBatchTaskEntry.");
        DbDynMemCtxFree(summary->memCtx, miniSummary);
        goto RELEASE_SESSION;
    }
    task->miniSummary = miniSummary;
    miniSummary->preAllocMem = preAllocMem;
    goto EXIT;

RELEASE_SESSION:
    QrySessionRelease(task->session);
MARK_UNSUCCESSFUL_MINI_BATCH:
    ReleaseBatchDmlPreAllocMem(preAllocMem);
    task->isCreateSucc = false;
    DestroyWorkerMiniBatchTask(task);  // 失败的销毁，成功的复用
    (void)DbAtomicDec(task->miniBatchAliveCntPtr);
EXIT:
    (void)DbAtomicDec(task->miniBatchCntPtr);
    DbMemCtxT *memCtx = task->batchDmls->memCtx;
    if ((task->batchDmls->pBuffer != NULL) && (!task->batchDmls->isNormalBatch)) {
        DbDynMemCtxFree(memCtx, task->batchDmls->pBuffer);
    }
}

// Commit MiniBatchTask
void CommitMiniBatchTask(PathMiniBatchTaskT *task)
{
    DB_POINTER(task);
    DB_POINTER4(task->summary, task->session, task->miniSummary, task->miniBatchCntPtr);
    PathSummaryT *summary = task->summary;
    SessionT *session = task->session;
    uint32_t *miniBatchCntPtr = task->miniBatchCntPtr;

    // commit mini batch
    Status ret = GqlCommitTransaction(session);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "unable to commit transaction in CommitMiniBatchTask.");
        GqlRollBackTransaction(session);
        goto RELEASE_SESSION;
    }

    // add mini summary into summary
    uint32_t vertexLabelCnt = DbListGetItemCnt(&task->miniSummary->vertexLabelIdList);
    for (uint32_t j = 0; j < vertexLabelCnt; j++) {
        uint32_t *vertexLabelId = (uint32_t *)DbListItem(&task->miniSummary->vertexLabelIdList, j);
        DmVertexLabelT *vertexLabel = NULL;
        ret = CataGetVertexLabelById(NULL, *vertexLabelId, &vertexLabel);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Unable to get vertexlabel by id when set vertexlabel changed status.");
            goto RELEASE_SESSION;
        }
        vertexLabel->pathAttributes->isChanged = true;
        (void)CataReleaseVertexLabel(vertexLabel);
    }
    if (task->batchDmls->isReplaceInsert) {
        // 为true时不搜索
        DestroyWorkerMiniBatchTask(task);
    } else {
        task->miniSummary->workerTaskId = task->workerTaskId;  // 给path search复用
        summary->miniSummaryList[task->miniTaskId] = task->miniSummary;
    }

    goto EXIT;

RELEASE_SESSION:
    //  remove the below three lines when global read-view is done
    if (task->miniSummary->readViewSessionBefore != NULL) {
        (void)GqlCommitTransaction(task->miniSummary->readViewSessionBefore);
        QrySessionRelease(task->miniSummary->readViewSessionBefore);
        task->miniSummary->readViewSessionBefore = NULL;
    }
    task->isCreateSucc = false;
    DestroyWorkerMiniBatchTask(task);  // 失败的销毁，成功的给pathSearch复用
    (void)DbAtomicDec(task->miniBatchAliveCntPtr);
EXIT:
    // post process mini batches
    QrySessionRelease(session);
    (void)DbAtomicDec(miniBatchCntPtr);
}

void InitMiniBatchTask(
    PathMiniBatchTaskT *task, PathBatchDmlT *batchDmls, PathSummaryT *summary, PathBatchExecParaT *execPara)
{
    task->summary = summary;
    task->miniBatchCntPtr = &execPara->miniBatchCnt;
    task->miniBatchAliveCntPtr = &execPara->miniBatchAliveCnt;
    task->batchDmls = batchDmls;
    task->isCreateSucc = true;
    task->miniSummary = NULL;
    task->session = NULL;
    task->workerTaskId = DB_INVALID_ID16;
}

// create task
// release in End
Status CreateWorkerMiniBatchTask(PathMiniBatchTaskT *task, DrtWorkerProcEntry miniBatchTaskEntry)
{
    DrtInstanceT *instance = DrtGetInstance(NULL);
    if (SECUREC_UNLIKELY(instance == NULL)) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "cannot get instance when init mini batch task.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    task->workerMgr = &instance->workerMgr;
    uint16_t workerTaskId = DrtWorkerCreateTask(&instance->workerMgr, task, task, miniBatchTaskEntry);
    if (workerTaskId == DB_INVALID_ID16) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to create worker task for PathSearchTaskQueue.");
        return GMERR_OUT_OF_MEMORY;
    }
    task->workerTaskId = workerTaskId;
    return GMERR_OK;
}

// destroy task
void DestroyWorkerMiniBatchTask(PathMiniBatchTaskT *task)
{
    DB_POINTER(task);
    if (SECUREC_UNLIKELY(task->workerTaskId == DB_INVALID_ID16)) {
        return;
    }
    DrtWorkerDestroyTask(task->workerMgr, task->workerTaskId);
    task->workerTaskId = DB_INVALID_ID16;
}
