/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: head file embedded mini shared obj prepare
 * Author: wangxiangdong
 * Create: 2024-02-18
 */

#ifndef SRV_MINI_SHARED_OBJ_PREPARE_H
#define SRV_MINI_SHARED_OBJ_PREPARE_H

#include "db_rpc_msg_op.h"
#include "ee_mini_base.h"
#include "ee_mini_shared_obj_context.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef Status (*SharedObjParseFunc)(MiniRunCtxT *runCtx);

typedef enum PermissionType { READ_OPT = 0, WRITE_OPT } PermissionTypeE;

typedef struct SharedObjPrepareHandler {
    MsgOpcodeRpcE opCode;
    MiniSharedObjQryTypeE qryType;
    SharedObjParseFunc parse;
    PermissionTypeE permission;
} SharedObjPrepareHandlerT;

Status SharedObjPrepare(MiniRunCtxT *runCtx, MsgOpcodeRpcE opCode);

#ifdef __cplusplus
}
#endif
#endif /* SRV_MINI_PREPARE_H */
