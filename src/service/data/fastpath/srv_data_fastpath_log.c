/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author:
 * Create: 2023-06-15
 */
#include "srv_data_fastpath_log.h"

#include "srv_data_log.h"
#include "ee_stmt_interface.h"
#include "ee_stmt.h"
#include "ee_context.h"
#include "ee_ddl_desc.h"
#include "ee_dml_desc.h"

typedef struct {
    DmIndexKeyT *leftIndex;
    DmIndexKeyT *rightIndex;
    char *leftStr;
    char *rightStr;
} FastPathPrintDataT;

static Status QryBuildCreateVertexLabelAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryCreateVertexLabelDescT *desc = (QryCreateVertexLabelDescT *)stmt->context->entry;
    {
        QryCreateSingleVertexLabelDescT *singleDesc =
            (QryCreateSingleVertexLabelDescT *)DbListItem(&desc->vertexLabels, 0);
        if (SECUREC_UNLIKELY(singleDesc == NULL)) {
            DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "get vertexLabel when create audit resource.");
            return GMERR_INTERNAL_ERROR;
        }

        Status ret = DbAppendAuditEvtDesc(
            resource, "namespaceId: %" PRIu32 " ", singleDesc->vertexLabel->metaCommon.namespaceId);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    for (uint32_t i = 0; i < DbListGetItemCnt(&desc->vertexLabels); i++) {
        QryCreateSingleVertexLabelDescT *singleDesc =
            (QryCreateSingleVertexLabelDescT *)DbListItem(&desc->vertexLabels, i);
        if (SECUREC_UNLIKELY(singleDesc == NULL)) {
            DB_LOG_AND_SET_LASERR(
                GMERR_INTERNAL_ERROR, "get vertexLabel when create audit resource, index = %" PRIu32 ".", i);
            return GMERR_INTERNAL_ERROR;
        }

        Status ret = DbAppendAuditEvtDesc(
            resource, "type: VERTEX_LABEL name: \"%s\" ", singleDesc->vertexLabel->metaCommon.metaName);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return GMERR_OK;
}

static Status QryBuildAlterVertexLabelConfigAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryAlterVertexLabelConfigDescT *desc = (QryAlterVertexLabelConfigDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " , type: VERTEX_LABEL name: \"%s\" ",
        stmt->session->namespaceId, desc->vertexLabelName.str);
}

static Status QryBuildDuplicateVertexLabelAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryDuplicateVertexLabelDescT *desc = (QryDuplicateVertexLabelDescT *)stmt->context->entry;
    Status ret = DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " ", stmt->session->namespaceId);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbAppendAuditEvtDesc(resource, "type: VERTEX_LABEL name: \"%s\" ", desc->newLabelName.str);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static Status QryBuildAlterVertexLabelAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryAlterVertexLabelDescT *desc = (QryAlterVertexLabelDescT *)stmt->context->entry;
    DbListT *updatedVertexLabels = &desc->vertexLabelDesc.vertexLabels;
    for (uint32_t i = 0; i < DbListGetItemCnt(updatedVertexLabels); i++) {
        // QryParseVertexLabelBase 在list元素申请时，已进行参数校验，结合此处for循环条件，可保证 DbGaListGet 返回值非空
        QryCreateSingleVertexLabelDescT *singleDesc =
            (QryCreateSingleVertexLabelDescT *)DbListItem(updatedVertexLabels, i);
        Status ret = DbAppendAuditEvtDesc(
            resource, "namespaceId: %" PRIu32 " ", singleDesc->vertexLabel->metaCommon.namespaceId);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DbAppendAuditEvtDesc(
            resource, "type: VERTEX_LABEL name: \"%s\" ", singleDesc->vertexLabel->metaCommon.metaName);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status QryBuildCreateEdgeLabelAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryCreateEdgeLabelDescT *desc = (QryCreateEdgeLabelDescT *)stmt->context->entry;
    DmEdgeLabelT *edgeLabel = (DmEdgeLabelT *)DbGaListGet(&desc->edgeLabels, 0);
    if (SECUREC_UNLIKELY(edgeLabel == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "get edgeLabel when create audit resource.");
        return GMERR_INTERNAL_ERROR;
    }
    Status ret = DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " ", edgeLabel->metaCommon.namespaceId);
    if (ret != GMERR_OK) {
        return ret;
    }

    for (uint32_t i = 0; i < desc->edgeLabels.count; i++) {
        edgeLabel = (DmEdgeLabelT *)DbGaListGet(&desc->edgeLabels, i);
        if (SECUREC_UNLIKELY(edgeLabel == NULL)) {
            DB_LOG_AND_SET_LASERR(
                GMERR_INTERNAL_ERROR, "get edgeLabel when create audit resource index = %" PRIu32 ".", i);
            return GMERR_INTERNAL_ERROR;
        }
        ret = DbAppendAuditEvtDesc(resource, "type: EDGE_LABEL name: \"%s\" ", edgeLabel->metaCommon.metaName);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status QryBuildDropVertexLabelAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryDropVertexLabelDescT *desc = (QryDropVertexLabelDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: VERTEX_LABEL name: \"%s\" ",
        stmt->session->namespaceId, desc->labelName.str);
}

static Status QryBuildDegradeVertexLabelAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryDegradeVertexLabelDescT *desc = (QryDegradeVertexLabelDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: VERTEX_LABEL name: \"%s\" ",
        stmt->session->namespaceId, desc->labelName.str);
}

static Status QryBuildDropOperationAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryDropEdgeLabelDescT *desc = (QryDropEdgeLabelDescT *)stmt->context->entry;
    switch (stmt->context->type) {
        case QRY_TYPE_DROP_EDGE_LABEL:
            return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: EDGE_LABEL name: \"%s\" ",
                desc->namespaceId, desc->labelName.str);
        case QRY_TYPE_DROP_SUBSCRIPTION:
            return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: SUBSCRIPTION name: \"%s\" ",
                desc->namespaceId, desc->labelName.str);
        case QRY_TYPE_TRUNCATE_VERTEX_LABEL:
            return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: VERTEX_LABEL name: \"%s\" ",
                desc->namespaceId, desc->labelName.str);
        case QRY_TYPE_DROP_RES_POOL:
        case QRY_TYPE_UNBIND_EXTEND_RES_POOL:
        case QRY_TYPE_UNBIND_RES_POOL_TO_VERTEX_LABEL:
            return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: RESOURCE_POOL name: \"%s\" ",
                desc->namespaceId, desc->labelName.str);
        case QRY_TYPE_DROP_KV_TABLE:
        case QRY_TYPE_TRUNCATE_KV_TABLE:
            return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: KV_TABLE name: \"%s\" ",
                desc->namespaceId, desc->labelName.str);
        default:
            DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "novalid qry type when build drop operation audit log.");
            return GMERR_DATATYPE_MISMATCH;
    }
}

static Status QryBuildTruncateVertexLabelBackgroundAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryTruncateVertexLabelBackgroundT *desc = (QryTruncateVertexLabelBackgroundT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: VERTEX_LABEL name: \"%s\" ",
        desc->vertexLabel->metaCommon.namespaceId, desc->vertexLabel->metaCommon.metaName);
}

static Status QryBuildCreateSubscriptionAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryCreateSubscriptionDescT *desc = (QryCreateSubscriptionDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: SUBSCRIPTION name: \"%s\" ",
        desc->subscription->metaCommon.namespaceId, desc->subscription->metaCommon.metaName);
}

static Status QryBuildCreateResPoolAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryCreateResPoolDescT *desc = (QryCreateResPoolDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: RESOURCE_POOL name: \"%s\" ",
        desc->resConfig->metaCommon.namespaceId, desc->resConfig->metaCommon.metaName);
}

static Status QryBuildBindExtendResPoolAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryBindExtendResPoolDescT *desc = (QryBindExtendResPoolDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: RESOURCE_POOL name: \"%s\" ",
        desc->namespaceId, desc->resPoolName.str);
}

static Status QryBuildBindResPoolToVertexLabelAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryBindResPoolToTableDescT *desc = (QryBindResPoolToTableDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: RESOURCE_POOL name: \"%s\" ",
        desc->namespaceId, desc->resPoolName.str);
}

static Status QryBuildCreateNamespaceAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryCreateNamespaceDescT *desc = (QryCreateNamespaceDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "type: NAMESPACE name: \"%s\" ", desc->nsp->metaCommon.metaName);
}

static Status QryBuildDropNamespaceAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryDropNamespaceDescT *desc = (QryDropNamespaceDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "type: NAMESPACE name: \"%s\" ", desc->namespaceName.str);
}

static Status QryBuildBindNamespaceToTablespaceAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryBindNspToTspDescT *desc = (QryBindNspToTspDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "type: NAMESPACE name: \"%s\" type: TABLESPACE name: \"%s\" ",
        desc->namespaceName.str, desc->tablespaceName.str);
}

static Status QryBuildCreateTablespaceAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryCreateTablespaceDescT *desc = (QryCreateTablespaceDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "type: TABLESPACE name: \"%s\" ", desc->tsp->metaCommon.metaName);
}

static Status QryBuildDropTablespaceAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryDropTablespaceDescT *desc = (QryDropTablespaceDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "type: TABLESPACE name: \"%s\" ", desc->tablespaceName.str);
}

static Status QryBuildCreateKvTableAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryCreateKvTableDescT *desc = (QryCreateKvTableDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: KV_TABLE name: \"%s\" ",
        desc->label->metaCommon.namespaceId, desc->label->metaCommon.metaName);
}

static Status QryBuildInsertOrMergeVertex(
    const QryStmtT *stmt, const DmVertexLabelT *vertexLabel, DbAuditEvtDescT *resource)
{
    return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: VERTEX_LABEL name: \"%s\" ",
        vertexLabel->metaCommon.namespaceId, vertexLabel->metaCommon.metaName);
}

static Status QryBuildInsertVertexAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryInsertVertexDescT *desc = (QryInsertVertexDescT *)stmt->context->entry;
    return QryBuildInsertOrMergeVertex(stmt, desc->qryLabel->def.vertexLabel, resource);
}

static Status QryBuildUpdateVertexAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryUpdateVertexDescT *desc = (QryUpdateVertexDescT *)stmt->context->entry;
    char *vertexName = desc->query->qryLabel->def.vertexLabel->metaCommon.metaName;
    uint32_t namespaceId = desc->query->qryLabel->def.vertexLabel->metaCommon.namespaceId;
    return DbAppendAuditEvtDesc(
        resource, "namespaceId: %" PRIu32 " type: VERTEX_LABEL name: \"%s\"", namespaceId, vertexName);
}

static Status QryBuildDeleteVertexAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryDeleteVertexDescT *desc = (QryDeleteVertexDescT *)stmt->context->entry;
    char *vertexName = desc->query->qryLabel->def.vertexLabel->metaCommon.metaName;
    uint32_t namespaceId = desc->query->qryLabel->def.vertexLabel->metaCommon.namespaceId;
    return DbAppendAuditEvtDesc(
        resource, "namespaceId: %" PRIu32 " type: VERTEX_LABEL name: \"%s\"", namespaceId, vertexName);
}

static Status QryBuildMergeRepVertexAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryMergRepVertexDescT *desc = (QryMergRepVertexDescT *)stmt->context->entry;
    return QryBuildInsertOrMergeVertex(stmt, desc->qryLabel->def.vertexLabel, resource);
}

static Status QryBuildNoneVertexAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryNoneVertexDescT *desc = (QryNoneVertexDescT *)stmt->context->entry;
    char *vertexName = desc->qryLabel->def.vertexLabel->metaCommon.metaName;
    uint32_t namespaceId = desc->qryLabel->def.vertexLabel->metaCommon.namespaceId;
    return DbAppendAuditEvtDesc(
        resource, "namespaceId: %" PRIu32 " type: VERTEX_LABEL name: \"%s\"", namespaceId, vertexName);
}

static Status QryBuildInsertEdgeAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryInsertEdgeDescT *desc = (QryInsertEdgeDescT *)stmt->context->entry;
    QryEdgeLabelT *edgeLabel = desc->query->edgeLabel;
    return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: EDGE_LABEL name: \"%s\" ",
        edgeLabel->label->metaCommon.namespaceId, edgeLabel->label->metaCommon.metaName);
}

static Status QryBuildSetKvAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QrySetKvDescT *desc = (QrySetKvDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: KV_TABLE name: \"%s\" ",
        desc->label->def.kvLabel->metaCommon.namespaceId, desc->label->def.kvLabel->metaCommon.metaName);
}

static Status QryBuildDeleteKvAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryDeleteKvDescT *desc = (QryDeleteKvDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: KV_TABLE name: \"%s\" ",
        desc->label->def.kvLabel->metaCommon.namespaceId, desc->label->def.kvLabel->metaCommon.metaName);
}

static Status QryBuildUpdateCheckVersionAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryUpdateCheckVersionDescT *desc = (QryUpdateCheckVersionDescT *)stmt->context->entry;
    char *vertexName = desc->query->qryLabel->def.vertexLabel->metaCommon.metaName;
    uint32_t namespaceId = desc->query->qryLabel->def.vertexLabel->metaCommon.namespaceId;
    return DbAppendAuditEvtDesc(
        resource, "namespaceId: %" PRIu32 " type: VERTEX_LABEL name: \"%s\"", namespaceId, vertexName);
}

static Status QryBuildCheckReplaceAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryCheckReplaceDescT *desc = (QryCheckReplaceDescT *)stmt->context->entry;
    char *vertexName = desc->qryLabel->def.vertexLabel->metaCommon.metaName;
    uint32_t namespaceId = desc->qryLabel->def.vertexLabel->metaCommon.namespaceId;
    return DbAppendAuditEvtDesc(
        resource, "namespaceId: %" PRIu32 " type: VERTEX_LABEL name: \"%s\"", namespaceId, vertexName);
}

static Status QryBuildFetchDiffAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
#ifdef FEATURE_YANG
    if (stmt->session == NULL || stmt->session->yangTreeCtx == NULL || stmt->session->yangTreeCtx->diffCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Session or yangTreeCtx or diffCtx is NULL.");
        return GMERR_DATA_EXCEPTION;
    }
    if (stmt->session->yangTreeCtx->diffCtx->diffResultTrees.count == 0) {
        return GMERR_OK;
    }
    DmYangTreeT *diffTree = (DmYangTreeT *)DbGaListGet(&stmt->session->yangTreeCtx->diffCtx->diffResultTrees, 0);
    if (diffTree == NULL || diffTree->diffCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "diffTree or diffCtx is NULL.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    QryLabelT *qryLabel = NULL;
    Status ret = QryGetQryLabelById((QryStmtT *)stmt, diffTree->diffCtx->trailCtx->labelId, &qryLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmVertexLabelT *vertexLabel = qryLabel->def.vertexLabel;
    const char *vrtxName = vertexLabel->metaCommon.metaName;
    uint32_t vrtxId = vertexLabel->metaCommon.metaId;
    return DbAppendAuditEvtDesc(resource, "fetch diff tree key, name: %s, id: %" PRIu32 " ", vrtxName, vrtxId);
#else
    return GMERR_OK;
#endif
}

static Status QryBuildSubtreeFilterAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QrySubtreeFilterDescT *desc = (QrySubtreeFilterDescT *)stmt->context->entry;
    DmVertexLabelT *filterTree = desc->filterLabel;
    const char *vrtxName = filterTree->metaCommon.metaName;
    uint32_t vrtxId = filterTree->metaCommon.metaId;
    return DbAppendAuditEvtDesc(resource, "filter subtree, name: %s, id: %" PRIu32 " ", vrtxName, vrtxId);
}

static Status QryBuildExportDataAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryExportDataDescT *desc = (QryExportDataDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "export data, dir: %s, mode: %" PRIu32 ", algorithm %" PRIu32 " ",
        desc->exportDir.str, (uint32_t)desc->mode, (uint32_t)desc->digestAlgorithm);
}

static Status QryBuildCreateVertexLabelAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryCreateVertexLabelDescT *desc = (QryCreateVertexLabelDescT *)stmt->context->entry;
    {
        Status ret = DbAppendAuditEvtDesc(evtDesc, "appoint label name: \"%s\" ",
            (desc->appointLabelName.len > 0) ? desc->appointLabelName.str : (char *)"");
        if (ret != GMERR_OK) {
            return ret;
        }

        QryCreateSingleVertexLabelDescT *singleDesc =
            (QryCreateSingleVertexLabelDescT *)DbListItem(&desc->vertexLabels, 0);
        if (SECUREC_UNLIKELY(singleDesc == NULL)) {
            DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "get vertexLabel when create audit log.");
            return GMERR_INTERNAL_ERROR;
        }

        ret = DbAppendAuditEvtDesc(evtDesc, "config: \"%s\" ", singleDesc->vertexLabel->metaVertexLabel->configJson);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    for (uint32_t i = 0; i < desc->vertexLabels.count; i++) {
        QryCreateSingleVertexLabelDescT *singleDesc =
            (QryCreateSingleVertexLabelDescT *)DbListItem(&desc->vertexLabels, i);
        // 不可能为空指针
        if (SECUREC_UNLIKELY(singleDesc->vertexLabel == NULL)) {
            DB_LOG_AND_SET_LASERR(
                GMERR_INTERNAL_ERROR, "get vertexLabel when create audit log, index = %" PRIu32 ".", i);
            return GMERR_INTERNAL_ERROR;
        }
        Status ret = DbAppendAuditEvtDesc(
            evtDesc, "schema%" PRIu32 ": \"%s\" ", i, singleDesc->vertexLabel->metaVertexLabel->labelJson);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return GMERR_OK;
}

static Status QryBuildAlterVertexLabelConfigAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryAlterVertexLabelConfigDescT *desc = (QryAlterVertexLabelConfigDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "new max record count: \"%" PRIu64 "\" ", desc->maxRecordCount);
}

static Status QryBuildDuplicateVertexLabelAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryDuplicateVertexLabelDescT *desc = (QryDuplicateVertexLabelDescT *)stmt->context->entry;
    Status ret = DbAppendAuditEvtDesc(evtDesc, "duplicate new label name: \"%s\" ",
        (desc->newLabelName.len > 0) ? desc->newLabelName.str : (char *)"");
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbAppendAuditEvtDesc(evtDesc, "origin label name: \"%s\" ",
        (desc->originLabelName.len > 0) ? desc->originLabelName.str : (char *)"");
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static Status QryBuildAlterVertexLabelAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryAlterVertexLabelDescT *desc = (QryAlterVertexLabelDescT *)stmt->context->entry;
    DbListT *updatedVertexLabels = &desc->vertexLabelDesc.vertexLabels;
    Status ret = DbAppendAuditEvtDesc(evtDesc, "isCompatible: \"%s\" ", desc->isCompatible ? "true" : "false");
    if (ret != GMERR_OK) {
        return ret;
    }
    for (uint32_t i = 0; i < DbListGetItemCnt(updatedVertexLabels); i++) {
        // QryParseVertexLabelBase 在list元素申请时，已进行参数校验，结合此处for循环条件，可保证 DbGaListGet 返回值非空
        QryCreateSingleVertexLabelDescT *singleDesc =
            (QryCreateSingleVertexLabelDescT *)DbListItem(updatedVertexLabels, i);
        ret = DbAppendAuditEvtDesc(evtDesc, "schema: \"%s\" ", singleDesc->vertexLabel->metaVertexLabel->labelJson);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status QryBuildCreateEdgeLabelAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryCreateEdgeLabelDescT *desc = (QryCreateEdgeLabelDescT *)stmt->context->entry;
    DmEdgeLabelT *edgeLabel = (DmEdgeLabelT *)DbGaListGet(&desc->edgeLabels, 0);
    if (SECUREC_UNLIKELY(edgeLabel == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "get edgeLabel when create audit log.");
        return GMERR_INTERNAL_ERROR;
    }
    Status ret;
    for (uint32_t i = 0; i < desc->edgeLabels.count; i++) {
        edgeLabel = (DmEdgeLabelT *)DbGaListGet(&desc->edgeLabels, i);
        if (SECUREC_UNLIKELY(edgeLabel == NULL)) {
            DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "get edgeLabel when create audit log, index = %" PRIu32 ".", i);
            return GMERR_INTERNAL_ERROR;
        }
        ret = DbAppendAuditEvtDesc(evtDesc, "name%" PRIu32 ": \"%s\" ", i, edgeLabel->metaCommon.metaName);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DbAppendAuditEvtDesc(evtDesc, "source_vertex_label%" PRIu32 ": \"%s\" ", i, edgeLabel->sourceVertexName);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DbAppendAuditEvtDesc(evtDesc, "dest_vertex_label%" PRIu32 ": \"%s\" ", i, edgeLabel->destVertexName);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status QryBuildDropVertexLabelAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryDropVertexLabelDescT *desc = (QryDropVertexLabelDescT *)stmt->context->entry;
    int32_t isDropAssoc = desc->isDropAssoc ? 1 : 0;
    return DbAppendAuditEvtDesc(evtDesc, "isDropAssoc: %" PRId32 " ", isDropAssoc);
}

static Status QryBuildDegradeVertexLabelAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryDegradeVertexLabelDescT *desc = (QryDegradeVertexLabelDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "versionId: %" PRIu32 " ", desc->targetVersion);
}

static Status QryBuildCreateSubscriptionAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryCreateSubscriptionDescT *desc = (QryCreateSubscriptionDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "schema: \"%s\" ", desc->subscription->subsJson.str);
}

static Status QryBuildCreateResPoolAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryCreateResPoolDescT *desc = (QryCreateResPoolDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "schema: \"%s\" ", desc->resConfig->labelJson);
}

static Status QryBuildBindExtendResPoolAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryBindExtendResPoolDescT *desc = (QryBindExtendResPoolDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "extendPoolName: \"%s\" ", desc->resExtendPoolName.str);
}

static Status QryBuildBindResPoolToVertexLabelAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryBindResPoolToTableDescT *desc = (QryBindResPoolToTableDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(
        evtDesc, "poolName: \"%s\" labelName: \"%s\" ", desc->resPoolName.str, desc->labelName.str);
}

static Status QryBuildCreateNamespaceAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryCreateNamespaceDescT *desc = (QryCreateNamespaceDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "userName: \"%s\" ", desc->nsp->owner.str);
}

static Status QryBuildYangDqlAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    if (stmt->session->req == NULL || stmt->session->rsp == NULL) {
        return GMERR_DATA_EXCEPTION;
    }
    return DbAppendAuditEvtDesc(evtDesc, "request size: %" PRIu32 " Byte, response size: %" PRIu32 " Byte",
        FixBufGetPos(stmt->session->req), FixBufGetPos(stmt->session->rsp));
}

static Status QryBuildModifyAlarmThresholdAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryModifyAlarmThresholdDescT *desc = (QryModifyAlarmThresholdDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "type: ALARM id: %" PRIu32 "", desc->alarmId);
}

static Status QryBuildUninstallDatalogAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    Status ret = GMERR_OK;
#ifdef FEATURE_DATALOG
    QryUninstallDatalogDescT *desc = (QryUninstallDatalogDescT *)stmt->context->entry;
    ret = DbAppendAuditEvtDesc(resource, "type: dynLibName: \"%s\"", desc->fileName.str);
#endif
    return ret;
}

static Status QryBuildBeginCheckAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryBeginOrEndCheckDescT *desc = (QryBeginOrEndCheckDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: VERTEX_LABEL name: \"%s\"",
        desc->vertexLabel->metaCommon.namespaceId, desc->vertexLabel->metaCommon.metaName);
}

static Status QryBuildEndCheckAuditResource(QryStmtT *stmt, DbAuditEvtDescT *resource)
{
    QryBeginOrEndCheckDescT *desc = (QryBeginOrEndCheckDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(resource, "namespaceId: %" PRIu32 " type: VERTEX_LABEL name: \"%s\"",
        desc->vertexLabel->metaCommon.namespaceId, desc->vertexLabel->metaCommon.metaName);
}

static Status QryBuildCreateKvTableAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryCreateKvTableDescT *desc = (QryCreateKvTableDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "config: \"%s\" ", desc->label->configJson);
}

static Status QryPrintIndexKeyInner(QryStmtT *stmt, DmVertexLabelT *label, const DmIndexKeyT *indexKey, char **str)
{
    if (indexKey == NULL || indexKey->keyBuf == NULL) {
        return GMERR_OK;
    }

    Status ret;
    uint32_t length;
    DmVlIndexLabelT *indexLabel = NULL;
    ret = DmGetIndexLabelByIndex(label, indexKey->indexId, &indexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = DmIndexKeyBufGetPrintLength(indexKey->keyBuf, indexLabel, &length);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryAllocStmtMem((QryStmtT *)stmt, length, str);
    if (ret != GMERR_OK) {
        return ret;
    }

    return DmIndexKeyBufPrint(indexKey->keyBuf, indexLabel, length, *str);
}

static Status QryPrintIndexKey(QryStmtT *stmt, DmVertexLabelT *label, FastPathPrintDataT *data)
{
    Status ret = QryPrintIndexKeyInner(stmt, label, data->leftIndex, &data->leftStr);
    if (ret != GMERR_OK) {
        return ret;
    }

    return QryPrintIndexKeyInner(stmt, label, data->rightIndex, &data->rightStr);
}

Status QryBuildUpdateOrDeleteParams4AuditInner(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryLabelT *qryLabel = NULL;
    QryVertexDataT *vertexItem = NULL;
    QryVertexParamT *params = (QryVertexParamT *)stmt->execParams;

    Status ret = QryGetVertexLabelByIdx(stmt->context, 0, &qryLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    for (uint32_t i = 0; i < params->count; i++) {
        vertexItem = &params->vertexData[i];
        // vertexItem may be empty when the request is from delta merge
        if (vertexItem == NULL) {
            continue;
        }
        FastPathPrintDataT data = {0};
        data.leftIndex = vertexItem->indexKey.leftValue.value;
        data.rightIndex = vertexItem->indexKey.rightValue.value;
        ret = QryPrintIndexKey(stmt, qryLabel->def.vertexLabel, &data);
        if (ret != GMERR_OK) {
            break;
        }
        ret = DbAppendAuditEvtDesc(evtDesc, "leftIndex: \"%s\" rightIndex: \"%s\" autoOperateFlag: \"%" PRIu32 "\" ",
            data.leftStr, data.rightStr, vertexItem->autoOperateFlag);
        if (ret != GMERR_OK) {
            break;
        }
    }
    return ret;
}

static Status QryBuildUpdateVertexAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    Status ret = GMERR_OK;
    if (stmt->status == STMT_STATUS_PREPARED || stmt->status == STMT_STATUS_EXECUTED) {
        QryUpdateVertexDescT *desc = (QryUpdateVertexDescT *)stmt->context->entry;
        if (desc->query->cond != NULL) {
            ret = DbAppendAuditEvtDesc(evtDesc, "cond: \"%s\" ", desc->query->cond->condStr.str);
        }
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    if (stmt->status == STMT_STATUS_EXECUTED) {
        return QryBuildUpdateOrDeleteParams4AuditInner(stmt, evtDesc);
    }
    return ret;
}

static Status QryBuildUpdateVertexParams4Audit(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    if (stmt->status != STMT_STATUS_EXECUTED) {
        return GMERR_OK;
    }
    return QryBuildUpdateVertexAuditLog(stmt, evtDesc);
}

static Status QryBuildDeleteVertexAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    Status ret = GMERR_OK;
    if (stmt->status == STMT_STATUS_PREPARED || stmt->status == STMT_STATUS_EXECUTED) {
        QryDeleteVertexDescT *desc = (QryDeleteVertexDescT *)stmt->context->entry;
        if (desc->query->cond != NULL) {
            ret = DbAppendAuditEvtDesc(evtDesc, "cond: \"%s\" ", desc->query->cond->condStr.str);
        }
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    if (stmt->status == STMT_STATUS_EXECUTED) {
        return QryBuildUpdateOrDeleteParams4AuditInner(stmt, evtDesc);
    }
    return ret;
}

static Status QryBuildInsertEdgeAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    Status ret;
    QryInsertEdgeDescT *desc = (QryInsertEdgeDescT *)stmt->context->entry;
    QryEdgeLabelT *edgeLabel = desc->query->edgeLabel;
    QryVertexContextT *srcVertex = &desc->query->srcVertex;
    QryVertexContextT *destVertex = &desc->query->destVertex;

    FastPathPrintDataT srcData = {0};
    srcData.leftIndex = srcVertex->indexKey->leftValue.value;
    ret = QryPrintIndexKey(stmt, srcVertex->qryLabel->def.vertexLabel, &srcData);
    if (ret != GMERR_OK) {
        return ret;
    }
    FastPathPrintDataT destData = {0};
    destData.leftIndex = destVertex->indexKey->leftValue.value;
    ret = QryPrintIndexKey(stmt, destVertex->qryLabel->def.vertexLabel, &destData);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbAppendAuditEvtDesc(evtDesc, "srcName: \"%s\" srcIndex: \"%s\" destName: \"%s\" destIndex: \"%s\" ",
        edgeLabel->label->sourceVertexName, srcData.leftStr, edgeLabel->label->destVertexName, destData.leftStr);

    return ret;
}

static Status QryBuildUpdateCheckVersionAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    Status ret;
    QryUpdateCheckVersionDescT *desc = (QryUpdateCheckVersionDescT *)stmt->context->entry;

    FastPathPrintDataT data = {0};
    data.leftIndex = desc->query->indexKey->leftValue.value;
    ret = QryPrintIndexKey(stmt, desc->query->qryLabel->def.vertexLabel, &data);
    if (ret != GMERR_OK) {
        return ret;
    }

    return DbAppendAuditEvtDesc(evtDesc, "index: \"%s\" ", data.leftStr);
}

static Status QryBuildBeginCheckAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryBeginOrEndCheckDescT *desc = (QryBeginOrEndCheckDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "partition: \"%" PRIu8 "\" ", desc->partitionId);
}

static Status QryBuildEndCheckAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryBeginOrEndCheckDescT *desc = (QryBeginOrEndCheckDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(
        evtDesc, "partition: \"%" PRIu8 "\" isAbnormal: \"%" PRId8 "\" ", desc->partitionId, desc->isAbnormal);
}

#ifdef WARM_REBOOT
static Status QryBuildEndAllPartitionCheckAuditLog(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    QryBeginOrEndCheckDescT *desc = (QryBeginOrEndCheckDescT *)stmt->context->entry;
    return DbAppendAuditEvtDesc(evtDesc, "vertex label name: %s isAbnormal: \"%" PRId8 "\" ",
        desc->vertexLabel->metaCommon.metaName, desc->isAbnormal);
}
#endif

static Status QryPrintVertex(QryStmtT *stmt, const DmVertexT *vertex, char **str)
{
    uint32_t length;
    Status ret = DmVertexGetPrintLength(vertex, NULL, &length);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryAllocStmtMem((QryStmtT *)stmt, length, str);
    if (ret != GMERR_OK) {
        return ret;
    }

    return DmVertexPrint(vertex, length, *str, NULL);
}

Status QryBuildInsertVertexParams4Audit(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    if (stmt->status != STMT_STATUS_EXECUTED) {
        return GMERR_OK;
    }
    QryLabelT *qryLabel = NULL;
    Status ret = QryGetVertexLabelByIdx(stmt->context, 0, &qryLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    QryVertexParamT *insertParams = (QryVertexParamT *)stmt->execParams;
    QryVertexDataT *vertexItem = NULL;
    char *str = NULL;
    for (uint32_t i = 0; i < insertParams->count; i++) {
        vertexItem = &insertParams->vertexData[i];
        // vertexItem may be empty when the request is from delta merge
        if (vertexItem == NULL || vertexItem->vertexBuf.len == 0) {
            continue;
        }
        ret = DmDeSerialize2ExistsVertex((uint8_t *)vertexItem->vertexBuf.str, vertexItem->vertexBuf.len,
            qryLabel->vertexContainer->emptyObjNode->vertex, DmGetCheckMode());
        if (ret != GMERR_OK) {
            break;
        }
        ret = QryPrintVertex(stmt, qryLabel->vertexContainer->emptyObjNode->vertex, &str);
        if (ret != GMERR_OK) {
            break;
        }
        ret = DbAppendAuditEvtDesc(evtDesc, "vertex%" PRIu32 ": \"%s\" ", i, str);
        if (ret != GMERR_OK) {
            break;
        }
    }
    return ret;
}

Status QryBuildKVParams4Audit(QryStmtT *stmt, DbAuditEvtDescT *evtDesc)
{
    if (stmt->status != STMT_STATUS_EXECUTED) {
        return GMERR_OK;
    }
    QryKVParamT *params = (QryKVParamT *)stmt->execParams;
    return DbAppendAuditEvtDesc(evtDesc, "key: \"%s\" ", params->key.str);
}

static inline BuildAuditLogDescT *SetFastPathAuditLogDesc(
    BuildAuditLogDescT *desc, char *name, BuildAuditLogFunc resourceFunc, BuildAuditLogFunc logFunc)
{
    desc->name = name;
    desc->resourceFunc = resourceFunc;
    desc->logFunc = logFunc;
    return desc;
}

// 根据QryType获取BuildAuditLogDescT
static BuildAuditLogDescT *GetFastPathAuditLogDesc(QryTypeE qryType, BuildAuditLogDescT *desc)
{
    switch (qryType) {
        // DDL
        case QRY_TYPE_CREATE_VERTEX_LABEL:
            return SetFastPathAuditLogDesc(
                desc, "CREATE VERTEX LABEL", QryBuildCreateVertexLabelAuditResource, QryBuildCreateVertexLabelAuditLog);
        case QRY_TYPE_ALTER_VERTEX_LABEL:
            return SetFastPathAuditLogDesc(
                desc, "ALTER VERTEX LABEL", QryBuildAlterVertexLabelAuditResource, QryBuildAlterVertexLabelAuditLog);
        case QRY_TYPE_DROP_VERTEX_LABEL:
            return SetFastPathAuditLogDesc(
                desc, "DROP VERTEX LABEL", QryBuildDropVertexLabelAuditResource, QryBuildDropVertexLabelAuditLog);
        case QRY_TYPE_DROP_VERTEX_LABEL_WITH_RES:
            return SetFastPathAuditLogDesc(
                desc, "DROP VERTEX LABEL", QryBuildDropVertexLabelAuditResource, QryBuildDropVertexLabelAuditLog);
        case QRY_TYPE_DEGRADE_VERTEX_LABEL:
            return SetFastPathAuditLogDesc(desc, "DEGRADE VERTEX LABEL", QryBuildDegradeVertexLabelAuditResource,
                QryBuildDegradeVertexLabelAuditLog);
#ifdef FEATURE_EDGELABEL
        case QRY_TYPE_CREATE_EDGE_LABEL:
            return SetFastPathAuditLogDesc(
                desc, "CREATE EDGE LABEL", QryBuildCreateEdgeLabelAuditResource, QryBuildCreateEdgeLabelAuditLog);
        case QRY_TYPE_DROP_EDGE_LABEL:
            return SetFastPathAuditLogDesc(desc, "DROP EDGE LABEL", QryBuildDropOperationAuditResource, NULL);
#endif
        case QRY_TYPE_CREATE_SUBSCRIPTION:
            return SetFastPathAuditLogDesc(desc, "CREATE SUBSCRIPTION", QryBuildCreateSubscriptionAuditResource,
                QryBuildCreateSubscriptionAuditLog);
        case QRY_TYPE_DROP_SUBSCRIPTION:
            return SetFastPathAuditLogDesc(desc, "DROP SUBSCRIPTION", QryBuildDropOperationAuditResource, NULL);
        case QRY_TYPE_TRUNCATE_VERTEX_LABEL:
            return SetFastPathAuditLogDesc(desc, "TRUNCATE VERTEX LABEL", QryBuildDropOperationAuditResource, NULL);
        case QRY_TYPE_TRUNCATE_VERTEX_LABEL_BACKGROUND:
            return SetFastPathAuditLogDesc(
                desc, "TRUNCATE VERTEX LABEL BACKGROUND", QryBuildTruncateVertexLabelBackgroundAuditResource, NULL);
        case QRY_TYPE_CREATE_RES_POOL:
            return SetFastPathAuditLogDesc(
                desc, "CREATE RES POOL", QryBuildCreateResPoolAuditResource, QryBuildCreateResPoolAuditLog);
        case QRY_TYPE_DROP_RES_POOL:
            return SetFastPathAuditLogDesc(desc, "DROP RES POOL", QryBuildDropOperationAuditResource, NULL);
        case QRY_TYPE_BIND_EXTEND_RES_POOL:
            return SetFastPathAuditLogDesc(desc, "BIND EXTEND RES POOL", QryBuildBindExtendResPoolAuditResource,
                QryBuildBindExtendResPoolAuditLog);
        case QRY_TYPE_UNBIND_EXTEND_RES_POOL:
            return SetFastPathAuditLogDesc(desc, "UNBIND EXTEND RES POOL", QryBuildDropOperationAuditResource, NULL);
        case QRY_TYPE_BIND_RES_POOL_TO_VERTEX_LABEL:
            return SetFastPathAuditLogDesc(desc, "BIND RES POOL TO VERTEX LABEL",
                QryBuildBindResPoolToVertexLabelAuditResource, QryBuildBindResPoolToVertexLabelAuditLog);
        case QRY_TYPE_UNBIND_RES_POOL_TO_VERTEX_LABEL:
            return SetFastPathAuditLogDesc(
                desc, "UNBIND RES POOL TO VERTEX LABEL", QryBuildDropOperationAuditResource, NULL);
#ifdef FEATURE_NAMESPACE_ENHANCE
        case QRY_TYPE_CREATE_NAMESPACE:
            return SetFastPathAuditLogDesc(
                desc, "CREATE NAMESPACE", QryBuildCreateNamespaceAuditResource, QryBuildCreateNamespaceAuditLog);
        case QRY_TYPE_DROP_NAMESPACE:
            return SetFastPathAuditLogDesc(desc, "DROP NAMESPACE", QryBuildDropNamespaceAuditResource, NULL);
#endif
        case QRY_TYPE_CREATE_KV_TABLE:
            return SetFastPathAuditLogDesc(
                desc, "CREATE KV TABLE", QryBuildCreateKvTableAuditResource, QryBuildCreateKvTableAuditLog);
        case QRY_TYPE_DROP_KV_TABLE:
            return SetFastPathAuditLogDesc(desc, "DROP KV TABLE", QryBuildDropOperationAuditResource, NULL);
        case QRY_TYPE_TRUNCATE_KV_TABLE:
            return SetFastPathAuditLogDesc(desc, "TRUNCATE KV TABLE", QryBuildDropOperationAuditResource, NULL);
        case QRY_TYPE_BEGIN_CHECK:
            return SetFastPathAuditLogDesc(
                desc, "BEGIN CHECK", QryBuildBeginCheckAuditResource, QryBuildBeginCheckAuditLog);
        case QRY_TYPE_END_CHECK:
            return SetFastPathAuditLogDesc(desc, "END CHECK", QryBuildEndCheckAuditResource, QryBuildEndCheckAuditLog);
        case QRY_TYPE_MODIFY_ALARM_THRESHOLD:
            return SetFastPathAuditLogDesc(
                desc, "MODIFY ALARM THRESHOLD", QryBuildModifyAlarmThresholdAuditResource, NULL);
        case QRY_TYPE_UNINSTALL_DATALOG:
            return SetFastPathAuditLogDesc(desc, "UNINSTALL DATALOG", QryBuildUninstallDatalogAuditResource, NULL);
        case QRY_TYPE_CREATE_TABLESPACE:
            return SetFastPathAuditLogDesc(desc, "CREATE TABLESPACE", QryBuildCreateTablespaceAuditResource, NULL);
        case QRY_TYPE_DROP_TABLESPACE:
            return SetFastPathAuditLogDesc(desc, "DROP TABLESPACE", QryBuildDropTablespaceAuditResource, NULL);
#ifdef FEATURE_NAMESPACE_ENHANCE
        case QRY_TYPE_CLEAR_NAMESPACE:
            return SetFastPathAuditLogDesc(desc, "CLEAR NAMESPACE", QryBuildDropNamespaceAuditResource, NULL);
        case QRY_TYPE_TRUNCATE_NAMESPACE:
            return SetFastPathAuditLogDesc(desc, "TRUNCATE NAMESPACE", QryBuildDropNamespaceAuditResource, NULL);
        case QRY_TYPE_BIND_NSP_TO_TSP:
            return SetFastPathAuditLogDesc(
                desc, "BIND NAMESPACE TO TABLESPACE", QryBuildBindNamespaceToTablespaceAuditResource, NULL);
#endif
        case QRY_TYPE_ALTER_VERTEX_LABEL_CONFIG:
            return SetFastPathAuditLogDesc(desc, "ALTER VERTEX LABEL CONFIG",
                QryBuildAlterVertexLabelConfigAuditResource, QryBuildAlterVertexLabelConfigAuditLog);
        case QRY_TYPE_DUPLICATE_VERTEX_LABEL:
            return SetFastPathAuditLogDesc(desc, "DUPLICATE VERTEX LABEL", QryBuildDuplicateVertexLabelAuditResource,
                QryBuildDuplicateVertexLabelAuditLog);
#ifdef WARM_REBOOT
        case QRY_TYPE_END_ALL_PARTITION_CHECK:
            return SetFastPathAuditLogDesc(
                desc, "END ALL PARTITION CHECK", QryBuildEndCheckAuditResource, QryBuildEndAllPartitionCheckAuditLog);
#endif
        // DML
        case QRY_TYPE_INSERT_VERTEX:
            return SetFastPathAuditLogDesc(
                desc, "INSERT VERTEX", QryBuildInsertVertexAuditResource, QryBuildInsertVertexParams4Audit);
        case QRY_TYPE_UPDATE_VERTEX:
            return SetFastPathAuditLogDesc(
                desc, "UPDATE VERTEX", QryBuildUpdateVertexAuditResource, QryBuildUpdateVertexAuditLog);
        case QRY_TYPE_DELETE_VERTEX:
            return SetFastPathAuditLogDesc(
                desc, "DELETE VERTEX", QryBuildDeleteVertexAuditResource, QryBuildDeleteVertexAuditLog);
        case QRY_TYPE_DELETE_GRAPH:
            return SetFastPathAuditLogDesc(
                desc, "DELETE GRAPH WITH ERROR", QryBuildDeleteVertexAuditResource, QryBuildDeleteVertexAuditLog);
        case QRY_TYPE_REMOVE_GRAPH:
            return SetFastPathAuditLogDesc(
                desc, "REMOVE GRAPH WITHOUT ERROR", QryBuildDeleteVertexAuditResource, QryBuildDeleteVertexAuditLog);
        case QRY_TYPE_REPLACE_VERTEX:
            return SetFastPathAuditLogDesc(
                desc, "REPLACE VERTEX", QryBuildMergeRepVertexAuditResource, QryBuildInsertVertexParams4Audit);
        case QRY_TYPE_REPLACE_GRAPH:
            return SetFastPathAuditLogDesc(
                desc, "REPLACE GRAPH", QryBuildMergeRepVertexAuditResource, QryBuildInsertVertexParams4Audit);
        case QRY_TYPE_MERGE_VERTEX:
            return SetFastPathAuditLogDesc(
                desc, "MERGE VERTEX", QryBuildMergeRepVertexAuditResource, QryBuildInsertVertexParams4Audit);
        case QRY_TYPE_NONE_VERTEX:
            return SetFastPathAuditLogDesc(
                desc, "NONE VERTEX", QryBuildNoneVertexAuditResource, QryBuildUpdateVertexParams4Audit);
        case QRY_TYPE_SET_KV:
            return SetFastPathAuditLogDesc(desc, "SET KV", QryBuildSetKvAuditResource, QryBuildKVParams4Audit);
        case QRY_TYPE_DELETE_KV:
            return SetFastPathAuditLogDesc(desc, "DELETE KV", QryBuildDeleteKvAuditResource, QryBuildKVParams4Audit);
        case QRY_TYPE_INSERT_EDGE:
            return SetFastPathAuditLogDesc(
                desc, "INSERT EDGE", QryBuildInsertEdgeAuditResource, QryBuildInsertEdgeAuditLog);
        case QRY_TYPE_DELETE_EDGE:
            return SetFastPathAuditLogDesc(
                desc, "DELETE EDGE", QryBuildInsertEdgeAuditResource, QryBuildInsertEdgeAuditLog);
        case QRY_TYPE_UPDATE_CHECK_VERSION:
            return SetFastPathAuditLogDesc(desc, "UPDATE CHECK VERSION", QryBuildUpdateCheckVersionAuditResource,
                QryBuildUpdateCheckVersionAuditLog);
        case QRY_TYPE_CHECK_REPLACE:
            return SetFastPathAuditLogDesc(desc, "CHECK REPLACE VERTEX", QryBuildCheckReplaceAuditResource, NULL);
        // DQL
        case QRY_TYPE_FETCH_DIFF:
            return SetFastPathAuditLogDesc(desc, "FETCH DIFF", QryBuildFetchDiffAuditResource, QryBuildYangDqlAuditLog);
        case QRY_TYPE_SUBTREE_FILTER:
            return SetFastPathAuditLogDesc(
                desc, "SUBTREE FILTER", QryBuildSubtreeFilterAuditResource, QryBuildYangDqlAuditLog);
        case QRY_TYPE_EXPORT_DATA:
            return SetFastPathAuditLogDesc(
                desc, "EXPORT CONFIG DATA", QryBuildExportDataAuditResource, QryBuildYangDqlAuditLog);
        default:
            return SetFastPathAuditLogDesc(desc, NULL, NULL, NULL);
    }
}

void FastpathAuditLog(QryStmtT *stmt, Status status)
{
    DB_POINTER(stmt);
    if (stmt->status < STMT_STATUS_PREPARED) {
        return;
    }
    QryTypeE type = stmt->context->type;
    BuildAuditLogDescT desc;
    if (SECUREC_LIKELY(type >= QRY_TYPE_DML_BEG && type < QRY_TYPE_DML_END)) {
        if (SECUREC_LIKELY(!DbCfgGetBoolLite(DB_CFG_AUDIT_LOG_DML_ENABLE, NULL))) {
            return;
        }
        WriteAuditLog(stmt, status, DB_AUDIT_DML, GetFastPathAuditLogDesc(stmt->context->type, &desc));
    } else if (SECUREC_UNLIKELY(type >= QRY_TYPE_DDL_BEG && type < QRY_TYPE_DDL_END)) {
        if (!DbCfgGetBoolLite(DB_CFG_AUDIT_LOG_DDL_ENABLE, NULL)) {
            return;
        }
        WriteAuditLog(stmt, status, DB_AUDIT_DDL, GetFastPathAuditLogDesc(stmt->context->type, &desc));
    } else if (SECUREC_UNLIKELY(
                   type == QRY_TYPE_FETCH_DIFF || type == QRY_TYPE_SUBTREE_FILTER || type == QRY_TYPE_EXPORT_DATA)) {
        if (!DbCfgGetBoolLite(DB_CFG_AUDIT_LOG_DQL_ENABLE, NULL) || stmt->status != STMT_STATUS_EXECUTED) {
            return;
        }
        WriteAuditLog(stmt, status, DB_AUDIT_DQL, GetFastPathAuditLogDesc(stmt->context->type, &desc));
    }
    // DCL迁移到public，此处不再判断
}
