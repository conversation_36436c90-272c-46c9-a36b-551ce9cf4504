/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * Description: header file of fastpath statistic
 * Author: zhulixia
 * Create: 2021-10-13
 */
#ifndef FASTPATH_STATISTIC_H
#define FASTPATH_STATISTIC_H

#include "db_rpc_msg_op.h"
#include "ee_context.h"
#include "ee_stmt_interface.h"

#ifdef __cplusplus
extern "C" {
#endif

QryLabelT *GetVertexLabel(const QryStmtT *stmt);

void FastpathUpdatePrepareCount(const QryStmtT *stmt, Status opStatus, QryLabelT *qryLabel);
void FastpathUpdatePrepareTime(QryStmtT *stmt, QryLabelT *qryLabel);
inline static void FastpathStat4Prepare(QryStmtT *stmt, Status opStatus)
{
    QryLabelT *qryLabel = GetVertexLabel(stmt);
    if (SECUREC_UNLIKELY(qryLabel == NULL)) {
        return;
    }
    FastpathUpdatePrepareCount(stmt, opStatus, qryLabel);
    if (opStatus == GMERR_OK) {
        FastpathUpdatePrepareTime(stmt, qryLabel);
    }
}

void FastpathUpdateExecuteCount(const QryStmtT *stmt, Status opStatus, QryLabelT *qryLabel);
void FastpathUpdateExecuteTime(const QryStmtT *stmt, QryLabelT *qryLabel);
inline static void FastpathStat4Execute(const QryStmtT *stmt, Status opStatus)
{
    QryLabelT *qryLabel = GetVertexLabel(stmt);
    if (SECUREC_UNLIKELY(qryLabel == NULL)) {
        return;
    }
    FastpathUpdateExecuteCount(stmt, opStatus, qryLabel);
    if (opStatus == GMERR_OK) {
        FastpathUpdateExecuteTime(stmt, qryLabel);
    }
}

#ifdef __cplusplus
}
#endif

#endif /* FASTPATH_STATISTIC_H */
