/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author:
 * Create: 2023-06-15
 */
#ifndef GMDBV5_SRV_DATA_PREPARE_H
#define GMDBV5_SRV_DATA_PREPARE_H

#include "gmc_errno.h"
#include "ee_stmt_interface.h"
#include "ee_stmt.h"
#include "ee_context.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef Status (*FastpathParseFunc)(QryStmtT *stmt);
typedef Status (*FastpathVerifyFunc)(QryStmtT *stmt);
typedef Status (*FastpathCreatePlanFunc)(QryStmtT *stmt);

typedef struct {
    QryTypeE qryType;
    FastpathParseFunc parse;
    FastpathVerifyFunc verify;
    FastpathCreatePlanFunc createPlan;
} FastpathPrepareHandlerT;

Status PrepareImplement(QryStmtT *stmt, MsgOpcodeRpcE opCode, FastpathPrepareHandlerT *handle);
void ResetCurrentContext(QryStmtT *stmt);
uint32_t OpCodeOffset(uint32_t opCode);

#ifdef __cplusplus
}
#endif

#endif  // GMDBV5_SRV_DATA_PREPARE_H
