/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: methods for ts aging func
 * Author: Ts Team
 * Create: 2024-09-25
 */

#include "srv_data_base_proc.h"
#include "ptl_ts_utils.h"
#include "ee_systbl.h"
#include "ee_background_schedule.h"
#include "se_cstore.h"
#include "ee_time_partition_util.h"
#include "dm_data_ts.h"
#include "ee_access_method.h"

#define TS_LCM_KEY_NUM (2)

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

Status TsdbAgingDropContainer(SessionT *session, PhysicalLabelMetaT *meta, uint32_t logicalLabelId)
{
    HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = *(ShmemPtrT *)(void *)(&meta->heapShmAddr),
        .isPersistent = true,
        .isUseRsm = false,
        .instanceId = DbGetProcGlobalId()};
    Status ret = HeapLabelDrop(session->seInstance, &heapCntrAcsInfo, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "drop heap when dropping physical labels of logical label, id:%" PRIu32 ".", logicalLabelId);
        return ret;
    }
    ret = IdxDrop(session->seInstance, (uint8_t)BTREE_INDEX, *(ShmemPtrT *)(void *)(&meta->indexShmAddr));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "drop index when dropping physical labels of logical label, id:%" PRIu32 ".", logicalLabelId);
    }
    return ret;
}

Status TsdbAgingInitHeap(SessionT *session, uint32_t physicalId, PhysicalLabelMetaT *meta)
{
    //  删除heap之前初始化其内存态，针对重启后的状态
    Status ret = InitMemFieldsForPhysicalLabel(session, physicalId, *(ShmemPtrT *)(void *)(&meta->heapShmAddr));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "init mem field of heap trm before tsdb aging.");  // LCOV_EXCL_LINE
        return ret;
    }
    return ret;
}

// 超50行拆分
Status TsdbAgingOnePhysicalDropHeap(
    SessionT *session, uint32_t logicalLabelId, uint32_t physicalId, PhysicalLabelMetaT *meta)
{
    Status ret = SeTransBegin(session->seInstance, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "begin trans before dropping label:%" PRIu32, logicalLabelId);  // LCOV_EXCL_LINE
        return ret;
    }
    if ((ret = SysTableTsMapGetPhysicalLabelMetaById(session, physicalId, meta)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "get physical label meta in ts map, label id: %" PRIu32, physicalId);  // LCOV_EXCL_LINE
        (void)SeTransRollback(session->seInstance, false);
        return ret;
    }
    // 删除系统表记录放在不可回滚的资源删除之前
    if ((ret = SysTableTsMapDeleteByPhysicalId(session, physicalId)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "process physical label: %" PRIu32, physicalId);  // LCOV_EXCL_LINE
        (void)SeTransRollback(session->seInstance, false);
        return ret;
    }

    ret = TsdbAgingInitHeap(session, physicalId, meta);
    if (ret != GMERR_OK) {
        (void)SeTransRollback(session->seInstance, false);
        return ret;
    }
    // 开始释放相关资源，物理表行存资源释放与NORMAL一致，内部出错有完备的日志，资源释放出错无法回滚
    // 删除的是二级索引，且二级索引只有一个
    ret = TsdbAgingDropContainer(session, meta, logicalLabelId);
    if (ret != GMERR_OK) {
        (void)SeTransRollback(session->seInstance, false);
        return ret;
    }
    ret = SeTransCommit(session->seInstance);
    if (ret != GMERR_OK) {  // 事务提交出错，也即事务调度出现问题，不应当继续再执行删除操作
        DB_LOG_ERROR(ret, "commit transaction for dropping physical label: %" PRIu32 ".", physicalId);
        return ret;
    }
    return ret;
}

// 行存和列存的删除分为两个事务，先确保行存文件删除. 后续出错，列存不删除仅占磁盘不会导致错误
Status TsdbAgingOnePhysical(SessionT *session, uint32_t logicalLabelId, uint32_t physicalId)
{
    PhysicalDestroyMiniBatchCacheById(physicalId);
    PhysicalLabelMetaT meta = {0};
    Status ret = TsdbAgingOnePhysicalDropHeap(session, logicalLabelId, physicalId, &meta);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "drop physical label heap: %" PRIu32 ".", physicalId);  // LCOV_EXCL_LINE
        return ret;
    }

    ret = SeTransBegin(session->seInstance, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "begin trans before remove cu label:%" PRIu32, logicalLabelId);  // LCOV_EXCL_LINE
        return ret;
    }

    CuStorageKeyT cuKey = {.tblSpcId = logicalLabelId, .tblId = physicalId};
    ret = SeRemoveCuDir(session->seInstance, &cuKey);
    if (ret != GMERR_OK) {
        (void)SeTransRollback(session->seInstance, false);
        return ret;
    }
    ret = SeTransCommit(session->seInstance);
    if (ret != GMERR_OK) {  // 事务提交出错，也即事务调度出现问题，不应当继续再执行删除操作
        DB_LOG_ERROR(ret, "commit transaction for remove cu label: %" PRIu32 ".", physicalId);  // LCOV_EXCL_LINE
        return ret;
    }
    // id回收失败不影响正常运行， 内部有日志
    (void)TsLcmLabelIdRecycle(&physicalId);
    (void)TsLcmLabelIdRecycle(&meta.indexId);
    RemoveInitedPhysicalLabelIdFromMap(physicalId);
    TsRemovePhysicLabelDataCache(logicalLabelId, physicalId);
    return GMERR_OK;
}

static void *TsLcmUpdateRecordNumLabelLatch(uint64_t deleteNum, DmVertexLabelT *vertexLabel)
{
    DB_POINTER(vertexLabel);
    DmAccCheckT *accCheckAddr = vertexLabel->commonInfo->accCheckAddr;
    accCheckAddr->checkInfo->recordCnt -= deleteNum;
    return NULL;
}

static void *TsLcmUpdateRecordNum(uint64_t deleteNum, DmVertexLabelT *vertexLabel)
{
    DB_POINTER(vertexLabel);
    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;

    if (SECUREC_LIKELY(
            DmIsLabelRuMode(commonInfo->heapInfo.ccType) || DmIsLabelLatchMode(commonInfo->heapInfo.ccType))) {
        TsLcmUpdateRecordNumLabelLatch(deleteNum, vertexLabel);
        return NULL;
    }
    DbRWLatchW(&commonInfo->accCheckAddr->lock);
    TsLcmUpdateRecordNumLabelLatch(deleteNum, vertexLabel);
    DbRWUnlatchW(&commonInfo->accCheckAddr->lock);
    return NULL;
}

static uint64_t TsLcmGetRecordNumLabelLatch(DmVertexLabelT *vertexLabel)
{
    DB_POINTER(vertexLabel);
    DmAccCheckT *accCheckAddr = vertexLabel->commonInfo->accCheckAddr;
    return accCheckAddr->checkInfo->recordCnt;
}

static uint64_t TsLcmGetRecordNum(DmVertexLabelT *vertexLabel)
{
    DB_POINTER(vertexLabel);
    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;

    if (SECUREC_LIKELY(
            DmIsLabelRuMode(commonInfo->heapInfo.ccType) || DmIsLabelLatchMode(commonInfo->heapInfo.ccType))) {
        return TsLcmGetRecordNumLabelLatch(vertexLabel);
    }
    DbRWLatchW(&commonInfo->accCheckAddr->lock);
    uint64_t recordNum = TsLcmGetRecordNumLabelLatch(vertexLabel);
    DbRWUnlatchW(&commonInfo->accCheckAddr->lock);
    return recordNum;
}

static void *TsLcmSetRecordNumLabelLatch(uint64_t setNum, DmVertexLabelT *vertexLabel)
{
    DB_POINTER(vertexLabel);
    DmAccCheckT *accCheckAddr = vertexLabel->commonInfo->accCheckAddr;
    accCheckAddr->checkInfo->recordCnt = setNum;
    return NULL;
}

static void *TsLcmSetRecordNum(uint64_t setNum, DmVertexLabelT *vertexLabel)
{
    DB_POINTER(vertexLabel);
    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;

    if (SECUREC_LIKELY(
            DmIsLabelRuMode(commonInfo->heapInfo.ccType) || DmIsLabelLatchMode(commonInfo->heapInfo.ccType))) {
        TsLcmSetRecordNumLabelLatch(setNum, vertexLabel);
        return NULL;
    }
    DbRWLatchW(&commonInfo->accCheckAddr->lock);
    TsLcmSetRecordNumLabelLatch(setNum, vertexLabel);
    DbRWUnlatchW(&commonInfo->accCheckAddr->lock);
    return NULL;
}

static Status InitScanKeyForMemLabelAging(
    SessionT *session, DmVertexLabelT *memLabel, int64_t upperBound, ScanKeyT **outputScanKeys)
{
    DmTsInfoT *dmTsInfo = (DmTsInfoT *)memLabel->metaVertexLabel->extraInfo.data;
    const char *labelName = memLabel->metaCommon.metaName;

    size_t scanKeySize = TS_LCM_KEY_NUM * sizeof(ScanKeyT);
    ScanKeyT *scanKeys = DbDynMemCtxAlloc(session->memCtx, scanKeySize);
    if (scanKeys == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Init scan Key for mem table ttl. labelName=%s", labelName);
        return GMERR_OUT_OF_MEMORY;
    }

    (void)memset_s(scanKeys, scanKeySize, 0x00, scanKeySize);

    scanKeys[0].value.type = DB_DATATYPE_INT64;
    scanKeys[0].value.value.timeValue = 0;  // 时间列非负
    scanKeys[0].skop = SKOP_GE;
    scanKeys[0].propeId = dmTsInfo->timeColId;

    scanKeys[1].value.type = DB_DATATYPE_INT64;
    scanKeys[1].value.value.timeValue = upperBound;
    scanKeys[1].skop = SKOP_LE;
    scanKeys[1].propeId = dmTsInfo->timeColId;

    *outputScanKeys = scanKeys;
    return GMERR_OK;
}

static Status InitScanForMemTblTTl(
    SessionT *session, DmVertexLabelT *memLabel, ScanKeyT *scanKeys, uint32_t keysNum, IndexScanDescT **indexScanDesc)
{
    LabelBeginCfgT beginCfg = {.vertexLabel = memLabel,
        .seRunCtx = session->seInstance,
        .eeMemCtx = session->memCtx,
        .trxMemCtx = session->memCtx,
        .session = session};

    const char *labelName = memLabel->metaCommon.metaName;
    uint32_t timeoutIndexSeqNum = 0;  // 写死

    Status ret = IndexBeginScan(beginCfg, timeoutIndexSeqNum, indexScanDesc);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to init index scan desc when ttl. labelName=%s", labelName);  // LCOV_EXCL_LINE
        return ret;
    }

    if ((ret = IndexReScan(scanKeys, keysNum, INDEX_SCAN_ASCEND, *indexScanDesc)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to set index scan qualification when ttl. labelName=%s", labelName);
        IndexEndScan(*indexScanDesc);
        return ret;
    }

    return ret;
}

static Status PrepareForDeletion(
    SessionT *session, DmVertexLabelT *memLabel, DmVertexT **scanVertex, LabelModifyDescT **vertexModifyDesc)
{
    const char *labelName = memLabel->metaCommon.metaName;
    LabelBeginCfgT beginCfg = {.vertexLabel = memLabel,
        .seRunCtx = session->seInstance,
        .eeMemCtx = session->memCtx,
        .trxMemCtx = session->memCtx,
        .session = session};

    Status ret = DmCreateEmptyVertexWithMemCtx(session->memCtx, memLabel, scanVertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create scan vertexLabel. labelName=%s", labelName);  // LCOV_EXCL_LINE
        return ret;
    }

    ret = HeapLabelBeginModify(beginCfg, vertexModifyDesc);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "modify for deleting table:%s.", labelName);  // LCOV_EXCL_LINE
        DmDestroyVertex(*scanVertex);
        *scanVertex = NULL;
        return ret;
    }

    return GMERR_OK;
}

static Status CalMemTblAgingUpperBound(DmVertexLabelT *memLabel, AASlotT *slot, int64_t *outputUpperBound)
{
    DmValueT propeValue = {0};
    DmTsInfoT *dmTsInfo = (DmTsInfoT *)memLabel->metaVertexLabel->extraInfo.data;
    Status ret = AASlotGetPrope(slot, dmTsInfo->timeColId, &propeValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "diskLimit obtain the earliest time row");  // LCOV_EXCL_LINE
        return ret;
    }

    if (propeValue.type != DB_DATATYPE_TIME && propeValue.type != DB_DATATYPE_INT64) {
        ret = GMERR_INVALID_VALUE;
        DB_LOG_ERROR(ret, "diskLimit obtain value from the earliest time row: type %d", propeValue.type);
        return ret;
    }

    if (propeValue.value.timeValue < 0) {
        ret = GMERR_INVALID_VALUE;
        DB_LOG_ERROR(ret, "diskLimit obtain time value %" PRId64, propeValue.value.timeValue);  // LCOV_EXCL_LINE
        return ret;
    }

    int64_t lowerTsBound = 0;
    int64_t upperTsBound = 0;
    TsGetTimeBoundary(propeValue.value.timeValue, dmTsInfo->interval, (time_t *)&upperTsBound, (time_t *)&lowerTsBound);

    *outputUpperBound = upperTsBound;
    return ret;
}

static Status CheckTimeValueExceedIntervalForAging(
    DmVertexLabelT *memLabel, AASlotT *slot, int64_t intervalUpperBound, uint64_t *itemDelInInterval, bool *doDelete)
{
    bool isOverUpperbound = false;
    DmValueT propeValue = {0};
    DmTsInfoT *dmTsInfo = (DmTsInfoT *)memLabel->metaVertexLabel->extraInfo.data;
    Status ret = AASlotGetPrope(slot, dmTsInfo->timeColId, &propeValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "aging obtain compared time row");  // LCOV_EXCL_LINE
        return ret;
    }

    if (propeValue.type != DB_DATATYPE_TIME && propeValue.type != DB_DATATYPE_INT64) {
        ret = GMERR_INVALID_VALUE;
        DB_LOG_ERROR(ret, "aging obtain value from compared time row: type %d", propeValue.type);  // LCOV_EXCL_LINE
        return ret;
    }

    if (propeValue.value.timeValue < 0) {
        ret = GMERR_INVALID_VALUE;
        DB_LOG_ERROR(ret, "aging obtain time from compared time row: value %" PRId64, propeValue.value.timeValue);
        return ret;
    }

    isOverUpperbound = propeValue.value.timeValue >= intervalUpperBound;

    if (isOverUpperbound) {
        TsLcmUpdateRecordNum(*itemDelInInterval, memLabel);
        *doDelete = false;
    } else {
        *doDelete = true;
    }

    return ret;
}

static Status DoDeleteForMemTblAging(SessionT *session, DmVertexLabelT *memLabel, IndexScanDescT *indexScanDesc)
{
    DmVertexT *scanVertex = NULL;
    LabelModifyDescT *vertexModifyDesc = NULL;
    Status ret = PrepareForDeletion(session, memLabel, &scanVertex, &vertexModifyDesc);
    if (ret != GMERR_OK) {
        IndexEndScan(indexScanDesc);
        return ret;
    }

    AASlotT slot = {.dmVertex = scanVertex};
    uint64_t itemFound = 0;
    if ((ret = IndexGetNext(indexScanDesc, &slot)) != GMERR_OK) {
        ret = (ret == GMERR_NO_DATA) ? GMERR_OK : ret;
        goto FINISH;
    }
    int64_t upperBound = 0;
    if ((ret = CalMemTblAgingUpperBound(memLabel, &slot, &upperBound)) != GMERR_OK) {
        goto FINISH;
    }
    if ((ret = LabelDelete(vertexModifyDesc, &slot)) != GMERR_OK) {
        goto FINISH;
    }

    itemFound++;

    while ((ret = IndexGetNext(indexScanDesc, &slot)) == GMERR_OK) {
        bool doDelete = false;
        ret = CheckTimeValueExceedIntervalForAging(memLabel, &slot, upperBound, &itemFound, &doDelete);
        if (ret != GMERR_OK || !doDelete) {
            goto FINISH;
        }

        if ((ret = LabelDelete(vertexModifyDesc, &slot)) != GMERR_OK) {
            goto FINISH;
        }
        itemFound++;
    }

    ret = (ret == GMERR_NO_DATA) ? GMERR_OK : ret;

FINISH:
    DB_LOG_WARN(ret, "mem table aging delete tuples: %" PRIu64, itemFound);
    TsLcmUpdateRecordNum(itemFound, memLabel);
    IndexEndScan(indexScanDesc);
    DmDestroyVertex(scanVertex);
    LabelEndModify(vertexModifyDesc);
    return ret;
}

static Status ExecTsdbAgingFuncInternalForMemLabel(SessionT *session, DmVertexLabelT *memLabel)
{
    Status ret = GMERR_OK;

    if ((ret = SeTransBegin(session->seInstance, NULL)) != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Begin trans(mem table aging).");  // LCOV_EXCL_LINE
        return ret;
    }
    uint64_t oriRecordNum = TsLcmGetRecordNum(memLabel);
    ScanKeyT *scanKeys = NULL;

    if (oriRecordNum == 0) {
        ret = GMERR_NO_DATA;
        goto RELEASE;
    }

    ret = InitScanKeyForMemLabelAging(session, memLabel, INT64_MAX, &scanKeys);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }

    IndexScanDescT *indexScanDesc = NULL;
    if ((ret = InitScanForMemTblTTl(session, memLabel, scanKeys, TS_LCM_KEY_NUM, &indexScanDesc)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "Init index scan for mem table aging. labelName=%s", memLabel->metaCommon.metaName);
        goto RELEASE;
    }

    if ((ret = DoDeleteForMemTblAging(session, memLabel, indexScanDesc)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "Do delete for mem table aging. labelName=%s", memLabel->metaCommon.metaName);
        goto RELEASE;
    }

RELEASE:
    if (ret == GMERR_OK) {
        ret = SeTransCommit(session->seInstance);
    } else {
        TsLcmSetRecordNum(oriRecordNum, memLabel);
        (void)SeTransRollback(session->seInstance, false);
    }
    if (scanKeys != NULL) {
        DbDynMemCtxFree(session->memCtx, scanKeys);
        scanKeys = NULL;
    }
    return ret;
}

static Status ExecTsdbAgingFuncInternal(SessionT *session, DmVertexLabelT *label)
{
    if (label->metaVertexLabel->vertexLabelType == VERTEX_TYPE_NORMAL) {
        return ExecTsdbAgingFuncInternalForMemLabel(session, label);
    }

    DbListT physicalIdList = {0};
    uint32_t labelId = label->metaCommon.metaId;
    char *labelName = label->metaCommon.metaName;
    DbCreateList(&physicalIdList, sizeof(uint32_t), session->memCtx);
    Status ret = SeTransBegin(session->seInstance, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "start transaction when dropping a physical label, label:%s.", labelName);  // LCOV_EXCL_LINE
        return ret;
    }
    ret = SysTableTsMapGetIdListByLogicalId(session, labelId, &physicalIdList);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get physical id list when dropping a physical label, label: %s.", labelName);
        (void)SeTransRollback(session->seInstance, false);
        return ret;
    }
    ret = SeTransCommit(session->seInstance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "commit transaction for getting physical id list, label: %s.", labelName);  // LCOV_EXCL_LINE
        return ret;
    }
    uint32_t labelNum = DbListGetItemCnt(&physicalIdList);
    if (labelNum == 0) {
        // 需要给用户返回错误码，但不视为错误，打印info日志即可
        DB_LOG_INFO("table:%s has no physical tbl when execute ts aging func.", labelName);  // LCOV_EXCL_LINE
        return GMERR_NO_DATA;
    }
    uint32_t physicalId = *(uint32_t *)DbListItem(&physicalIdList, 0);
    return TsdbAgingOnePhysical(session, labelId, physicalId);
}

// 本函数调用者不开启事务
Status ExecTsdbAgingFunc(TsQryStmtT *stmt, SqlIrStmtT *irStmt)
{
    DB_UNUSED(stmt);
    SessionT *session = stmt->base.session;
    DmVertexLabelT *label = *(DmVertexLabelT **)DbListItem(irStmt->labelList, 0);
    LabelRWLatchT *labelLatch = (LabelRWLatchT *)GetLabelRWLatchPtrById(label->commonInfo->vertexLabelLatchId, NULL);
    if (labelLatch == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "get latchLatch during time partition scan, table name: %s",
            label->metaCommon.metaName);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    // use latch protect dql concurrency with ddl drop
    if (!LabelWLatchTimedAcquire(labelLatch, stmt->lockTimeoutUs)) {
        DB_LOG_ERROR(GMERR_LOCK_NOT_AVAILABLE, "timeout when exe aging func");  // LCOV_EXCL_LINE
        return GMERR_LOCK_NOT_AVAILABLE;
    }
    // 校验表锁的有效性，防止并发下表被删除
    Status ret = LabelLatchCheckVersion(labelLatch, label->commonInfo->vertexLabelLatchVersionId);
    if (ret != GMERR_OK) {
        LabelWLatchRelease(labelLatch);
        DB_LOG_AND_SET_LASERR(ret, "table: %s already been dropped", label->metaCommon.metaName);  // LCOV_EXCL_LINE
        return ret;
    }

    ret = ExecTsdbAgingFuncInternal(session, label);
    LabelWLatchRelease(labelLatch);
    return ret;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
