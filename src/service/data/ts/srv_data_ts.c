/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: time-series data service
 * Author: hujing lihainuo zhangtao
 * Create: 2023-02-15
 */

#include "ctype.h"
#include "db_config.h"
#include "db_sysapp_context.h"
#include "db_rpc_msg.h"
#include "db_se_trx.h"
#include "db_secure_msg_buffer.h"
#include "dm_meta_key_oper.h"
#include "dm_meta_priv.h"
#include "dm_cu_physical_label.h"
#include "cpl_ir_logical_op.h"
#include "cpl_planner.h"
#include "cpl_optimizer_cascades.h"
#include "cpl_ts_analyzer_common.h"
#include "cpl_ts_compiler.h"
#include "ee_command_fusion.h"
#include "ee_receiver.h"
#include "ee_stmt_fusion.h"
#include "ee_ser_table_receiver.h"
#include "ee_ts_trans_monitor.h"
#include "ee_time_partition_util.h"
#include "ee_background_schedule.h"
#include "ptl_ts_utils.h"
#include "ee_operate_stat.h"
#include "se_cu_data_cache.h"
#include "srv_data_service.h"
#include "srv_data_base_proc.h"
#include "db_instance.h"
#include "srv_data_ts.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

static bool g_gmdbTsLazyInitFlag = false;
static DbSpinLockT g_gmdbTsLazyInitSpinLock = {0};
static DbMemCtxT *g_tsSeDbMemCtx = NULL;

static Status TsInitSeDbMemCtx(void)
{
    DbMemCtxT *sysDynCtx = DbSrvGetSysDynCtx(DbGetProcGlobalId());
    if (sysDynCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "get sys dyn MemCtx to load sources.");  // LCOV_EXCL_LINE
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DbMemCtxArgsT args = {0};
    g_tsSeDbMemCtx = DbCreateDynMemCtx(sysDynCtx, true, "TS SE DbMemCtx", &args);
    if (g_tsSeDbMemCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "create dynamic MemCtx when load sources.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

void TsConfigChangeNotify(
    const char *configName, const DbCfgValueT *oldValue, DbCfgValueT *newValue, DbInstanceHdT dbInstance)
{
    DB_POINTER2(configName, newValue);
    if (oldValue == NULL || DbCfgValueIsEqual(oldValue, newValue)) {
        return;
    }
    // 修改ctx的配置数据，如果配置需要上锁，则修改前后需要加锁
    DbCfgEmItemIdE id = DbCfgGetIdByName(configName);
    switch (id) {
        case DB_CFG_TS_ALLOW_DISK_CLEAN:
            if (newValue->int32Val == 1) {
                Status ret = TsLcmBgCleanDiskAddNotify(DB_INVALID_ID32, 0);
                if (ret != GMERR_OK) {
                    DB_LOG_ERROR(ret, "post disk clean sem to ts background life cycle mgr during config change.");
                }
            }
            break;
        default:
            DB_LOG_WARN(GMERR_INVALID_PARAMETER_VALUE, "un support config id:%d.", (uint32_t)id);  // LCOV_EXCL_LINE
            return;
    }
    DB_LOG_INFO("update ts config ok(id:%" PRIu32 ").", (uint32_t)id);
}

static Status TsBackgroundInit(void)
{
    // TS background LCM schedule worker initialization
    Status ret = TsLcmBgWorkerInit(g_tsSeDbMemCtx, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create TS life cycle management schedule worker.");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = TsLcmLabelIdListInit(g_tsSeDbMemCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "init label id list.");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = TsLcmBgWorkerActivateAllTask();
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = DbCfgRegisterNofityFunc(DB_CFG_TS_ALLOW_DISK_CLEAN, TsConfigChangeNotify, NULL);
    if (ret != GMERR_OK) {  // 失败也不是致命错误。
        DB_LOG_ERROR(ret, "register config change notifty func.");
    }
    return GMERR_OK;
}

static Status TsInitInner(void)
{
    Status ret = TsInitSeDbMemCtx();
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "initialize g_tsSeDbMemCtx.");  // LCOV_EXCL_LINE
        return ret;
    }
    // TS SE CuColSpcCache initialization
    ret = SeInitCuColSpcCache(g_tsSeDbMemCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "initialize CuColSpcCache.");  // LCOV_EXCL_LINE
        return ret;
    }

    // TS SE CuFileSizeCache initialization
    ret = SeInitCuFileSizeCache(g_tsSeDbMemCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "initialize CuFileSizeCache.");  // LCOV_EXCL_LINE
        return ret;
    }

    ret = SeSetCuOperateMemCtx(g_tsSeDbMemCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set MemCtx.");  // LCOV_EXCL_LINE
        return ret;
    }
    // TS SE DataCache initialization
    ret = SeInitCuDataCache(g_tsSeDbMemCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "initialize CuDataCache.");  // LCOV_EXCL_LINE
        return ret;
    }

    return TsBackgroundInit();
}

static void TsUnInitInner(void)
{
    // Do not stop LCM background worker here which will be handled by DrtWorker framework
    SeUnInitCuColSpcCache();
    SeUnInitCuFileSizeCache();
    TsLcmLabelIdListUnInit();
    DbDeleteDynMemCtx(g_tsSeDbMemCtx);
    g_tsSeDbMemCtx = NULL;
}

static Status TsLazyInit(void)
{
    if (SECUREC_LIKELY(g_gmdbTsLazyInitFlag)) {
        return GMERR_OK;
    }

    DbSpinLock(&g_gmdbTsLazyInitSpinLock);
    // Concurrent requests share the same lock to make sure init consistency.
    if (g_gmdbTsLazyInitFlag) {
        DbSpinUnlock(&g_gmdbTsLazyInitSpinLock);
        return GMERR_OK;
    }
    Status ret = SeClearTempFile();
    if (ret != GMERR_OK) {
        DbSpinUnlock(&g_gmdbTsLazyInitSpinLock);
        return ret;
    }
    ret = TsInitInner();
    if (ret == GMERR_OK) {
        g_gmdbTsLazyInitFlag = true;
    }
    DbSpinUnlock(&g_gmdbTsLazyInitSpinLock);
    return ret;
}

static void TsLazyUnInit(void)
{
    // Concurrency protect for UnInit operation
    DbSpinLock(&g_gmdbTsLazyInitSpinLock);
    if (g_gmdbTsLazyInitFlag) {
        g_gmdbTsLazyInitFlag = false;
        TsUnInitInner();
    }
    DbSpinUnlock(&g_gmdbTsLazyInitSpinLock);
}

void InitTsStmt(DbMemCtxT *memCtx, SessionT *session, TsQryStmtT *stmt)
{
    stmt->base.session = session;
    stmt->base.prevSession = NULL;
    stmt->base.prevEstate = NULL;
    stmt->memCtx = memCtx;
    stmt->base.memCtx = memCtx;
    stmt->base.trxMemCtx = memCtx;
    stmt->base.receiverType = SERIALIZED_TABLE_RESULT;
    stmt->base.needResult = true;
    stmt->base.needTimeoutSplit = false;
    stmt->base.isPrepareStmt = false;
    stmt->base.isPrepareQry = false;
}

static Status TsAllocMemCtx(SessionT *session, DbMemCtxT **memCtx)
{
    DB_POINTER(session);
    DbMemCtxArgsT args = {0};
    DbMemCtxT *tmpMemCtx = DbCreateDynMemCtx(session->memCtx, true, "tsQryMemCtx", &args);
    if (tmpMemCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc tsQryMemCtx in ts");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    *memCtx = tmpMemCtx;
    return GMERR_OK;
}

static Status GetTsStmts(FixBufferT *req, TextT *sqlStmts, uint16_t *sqlType)
{
    DB_POINTER2(req, sqlStmts);
    RpcSeekFirstOpMsg(req);
    Status ret = FixBufGetTextEx(req, sqlStmts);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "parse ts sql");  // LCOV_EXCL_LINE
        return ret;
    }

    ret = SecureFixBufGetUint16(req, (uint16_t *)sqlType);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "parse ts sql type");  // LCOV_EXCL_LINE
        return ret;
    }
    return ret;
}

static void TsDestroyFusionStmt(TsQryStmtT *stmt)
{
    if (stmt->memCtx != NULL) {
        DbDeleteDynMemCtx(stmt->memCtx);
        DbAdptMallocTrim(0);
    }
    if ((stmt->base.session != NULL) && (stmt->base.session->stbTrxMemCtx != NULL)) {
        DbMemCtxReset(stmt->base.session->stbTrxMemCtx);
    }
}

static void TsFillStmtPlanTree(TsQryStmtT *stmt, PlanCtxT *execPlan)
{
    stmt->base.planTree = execPlan->plan.planTree;
    stmt->base.totalParamNum = execPlan->plan.totalParamNum;
}

static Status TsExecutePlanWithTrans(TsQryStmtT *stmt)
{
    DB_POINTER(stmt);
    return ExecutePlanWithTrx(stmt);
}

// 将vertexLabel表置为可见
static Status CommitTransaction4CreateVertexLabel(TsQryStmtT *stmt)
{
    uint32_t index = EESTMT_OFFSET(stmt->base.utilityStmt->tag);
    if (index == EESTMT_OFFSET(T_CREATE_TABLE_STMT)) {
        CreateTableStmtT *cmdReq = (void *)stmt->base.utilityStmt;
        DmVertexLabelT *vertexLabel = cmdReq->vertexLabel;
        return CataSetVertexLabelCreateStatusById(
            DbGetInstanceByMemCtx(vertexLabel->memCtx), vertexLabel->metaCommon.metaId, true);
    }
    return GMERR_OK;
}

static Status ExecuteDdlInTransaction(TsQryStmtT *stmt, SqlIrStmtT *irStmt)
{
    Status ret = StartTransaction(stmt->base.session);
    if (ret != GMERR_OK) {
        return ret;
    }
    CStateT *state = NULL;
    ret = CreateCommandState(stmt->memCtx, stmt->base.session, &state);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    state->memCtx = stmt->base.trxMemCtx;
    stmt->base.seInstance = stmt->base.session->seInstance;
    state->seInstance = stmt->base.seInstance;
    state->session = stmt->base.session;
    ret = ExecFusionCmd(state, stmt->base.utilityStmt, &stmt->base.utilityResultStmt);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
EXIT:
    if (ret != GMERR_OK) {
        RollBackTransaction(stmt->base.session);
        return ret;
    }
    ret = CommitTransaction(stmt->base.session);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CommitTransaction4CreateVertexLabel(stmt);
}

static Status GetParaValueByTypeFromBuf(FixBufferT *req, DbDataTypeE type, DmValueT *value)
{
    Status ret = GMERR_OK;
    value->type = type;
    if (type == DB_DATATYPE_INT64) {
        ret = SecureFixBufGetInt64(req, &value->value.longValue);
    } else if (type == DB_DATATYPE_STRING) {
        TextT text = {};
        ret = SecureFixBufGetTextEx(req, &text);
        if (ret != GMERR_OK) {
            return ret;
        }
        value->value.strAddr = text.str;
        value->value.length = text.len;
        value->type = DB_DATATYPE_FIXED;
    }
    return ret;
}

static Status ParseBindParaIfNeeded(TsQryStmtT *stmt, DmValueT **paraList, uint16_t *maxIndex)
{
    if (!stmt->isPrepareSql) {
        return GMERR_OK;
    }
    FixBufferT *req = stmt->base.session->req;
    Status ret = FixBufGetUint16(req, maxIndex);
    if (ret != GMERR_OK) {
        return ret;
    }
    *paraList = DbDynMemCtxAlloc(stmt->memCtx, sizeof(DmValueT) * (*maxIndex));
    if (*paraList == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc paraList for bind para for ts prepare qry.");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    uint16_t index = 0;
    uint16_t type = (uint16_t)DB_DATATYPE_CHAR;
    for (uint16_t i = 0; i < *maxIndex; i++) {
        ret = SecureFixBufGetUint16(req, &index);
        if (ret != GMERR_OK) {
            goto ERROR;
        }
        ret = SecureFixBufGetUint16(req, &type);
        if (ret != GMERR_OK) {
            goto ERROR;
        }
        ret = GetParaValueByTypeFromBuf(req, type, &(*paraList)[index]);
        if (ret != GMERR_OK) {
            goto ERROR;
        }
    }
    return ret;
ERROR:
    DbDynMemCtxFree(stmt->memCtx, *paraList);
    *paraList = NULL;
    return ret;
}

static Status TsInitParseSqlMap(TsQryStmtT *stmt, SqlParsedListT *parsedList)
{
    // 内存由TsDestroyFusionStmt兜底释放
    if (!stmt->isPrepareSql) {
        return GMERR_OK;
    }
    parsedList->indexMap = DbDynMemCtxAlloc(stmt->memCtx, sizeof(DbOamapT));
    if (parsedList->indexMap == NULL) {  // 后续的内存释放由tsQryMemCtx兜底
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc index map for ts parameter sql qry.");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    Status ret = DbOamapInit(parsedList->indexMap, 0, DbOamapPtrCompare, stmt->memCtx, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "init index map for ts prepare sql qry.");  // LCOV_EXCL_LINE
        return ret;
    }
    parsedList->nameMap = DbDynMemCtxAlloc(stmt->memCtx, sizeof(DbOamapT));
    if (parsedList->nameMap == NULL) {  // 后续的内存释放由tsQryMemCtx兜底
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc name map for ts parameter sql qry.");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    ret = DbOamapInit(parsedList->nameMap, 0, DbOamapStringCompare, stmt->memCtx, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "init name map for ts prepare sql qry.");  // LCOV_EXCL_LINE
        return ret;
    }
    return GMERR_OK;
}

static Status TsSqlPrepare(TsQryStmtT *stmt, SqlParsedListT *parsedList, char **executeSql)
{
    TextT sqlStmts = {0};
    uint16_t sqlType = 0;
    Status ret = GetTsStmts(stmt->base.session->req, &sqlStmts, &sqlType);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get ts cmd, type:%" PRIu16, sqlType);  // LCOV_EXCL_LINE
        return ret;
    }
    TsSetSqlType(stmt, (uint8_t)sqlType);

    // 执行完统一释放
    char *inputSql = (char *)DbDynMemCtxAlloc(stmt->memCtx, sqlStmts.len);
    if (inputSql == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc inputSql memory");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(inputSql, sqlStmts.len, 0, sqlStmts.len);

    errno_t err = memcpy_s(inputSql, sqlStmts.len, sqlStmts.str, strlen(sqlStmts.str));
    if (err != EOK) {
        DB_LOG_ERROR(
            GMERR_MEMORY_OPERATE_FAILED, "memcpy in ts sql prepare, str:%s, len:%" PRIu32, sqlStmts.str, sqlStmts.len);
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    // 参数化查询初始化
    ret = ParseBindParaIfNeeded(stmt, &parsedList->paraList, &parsedList->maxIndex);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = TsInitParseSqlMap(stmt, parsedList);
    if (ret != GMERR_OK) {
        return ret;
    }

    *executeSql = inputSql;
    TsSetTransMonitorSql(&stmt->monitor, *executeSql);
    return ret;
}

static Status ProcessFixedPara(DbMemCtxT *memCtx, SqlExprParaT *para, DmValueT *value)
{
    if (value->type != DB_DATATYPE_FIXED || para->planVal->value.length == 0 ||
        para->planVal->value.length <= value->value.length) {
        *(para->planVal) = *value;
        return GMERR_OK;
    }
    // realloc memory for FIXED para
    uint32_t size = para->planVal->value.length;
    // 重新申请VALUE长度
    void *ptr = DbDynMemCtxAlloc(memCtx, size);
    if (ptr == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc fixed const when process ts para.");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(ptr, size, 0, size);
    if (memcpy_s(ptr, size, value->value.strAddr, value->value.length) != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "memcpy fixed const when process ts para.");  // LCOV_EXCL_LINE
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    para->planVal->type = DB_DATATYPE_FIXED;
    para->planVal->value.strAddr = ptr;
    para->planVal->value.length = size;
    return GMERR_OK;
}

static Status ProcessParaQryBeforeDql(SqlParsedListT *parsedList)
{
    if (parsedList->maxIndex == 0) {
        return GMERR_OK;
    }
    if (DbOamapUsedSize(parsedList->indexMap) != parsedList->maxIndex) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE,
            "Number of bound:%" PRIu32 " para mismatch with the parameter qry sql: %" PRIu32,
            DbOamapUsedSize(parsedList->indexMap), parsedList->maxIndex);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    for (uint16_t i = 1; i <= parsedList->maxIndex; i++) {
        DmValueT *value = parsedList->paraList + i - 1;
        uint32_t key = (uint32_t)i;
        SqlExprParaT *para = (SqlExprParaT *)DbOamapLookup(parsedList->indexMap, i, (void *)(uintptr_t)key, NULL);
        if (para == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "The parameter of index %" PRIu32 ".", i);
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        if (para->planVal == NULL) {
            DB_LOG_AND_SET_LASERR(
                GMERR_INVALID_PARAMETER_VALUE, "The parameter of index %" PRIu32 " is created on idx key", i);
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        Status ret = ProcessFixedPara(parsedList->memCtx, para, value);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status CheckLabelOverLimitWithLog(TsQryStmtT *stmt, DmVertexLabelT *logicalLabel)
{
    Status ret = GMERR_OK;
    bool isOverLimit = false;
    ret = TsCheckLabelOverLimit(stmt->base.session, logicalLabel, &isOverLimit);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "judge whether disk limit has been reached or not");  // LCOV_EXCL_LINE
        return ret;
    } else if (isOverLimit) {
        ret = GMERR_TABLE_EXCEED_DISK_LIMIT;
        DB_LOG_ERROR(ret, "Time partition insert exceeds disk limit");  // LCOV_EXCL_LINE
        return ret;
    }
    return ret;
}

static Status ProcessDqlAndDmlCmd(TsQryStmtT *stmt, SqlIrStmtT *irStmt, DbListT *latchList, SqlParsedListT *parsedList)
{
    //  generate execution plan
    PlanCtxT executionPlan = {0};
    InitPlanCtx(&executionPlan, stmt->memCtx, 0);
    Status ret = GenerateTsExecPlan(stmt->memCtx, irStmt, &executionPlan);
    if (ret != GMERR_OK) {
        return ret;
    }
    TsFillStmtPlanTree(stmt, &executionPlan);

    // param query bind value
    ret = ProcessParaQryBeforeDql(parsedList);
    if (ret != GMERR_OK) {
        return ret;
    }

    // execute explain
    if (stmt->base.result.sqlType == EXPLAIN_SQL) {
        return TsExecExplain(&stmt->base);
    }

    // fill statement handler and acquire label read latch
    ret = AcquireLabelRLatchFromList(irStmt->labelList, latchList);
    if (ret != GMERR_OK) {
        return ret;
    }
    DB_LOG_INFO("TsServiceEntry try execute dql or dml sql, sql=%s", stmt->monitor.sql);

    NodeTagT tagType = stmt->base.planTree->tagType;
    bool isAllowDiskClean = DbCfgGetBoolLite(DB_CFG_TS_ALLOW_DISK_CLEAN, NULL);
    uint32_t labelNum = DbListGetItemCnt(irStmt->labelList);
    DmVertexLabelT *logicalLabel = NULL;

    if (tagType == T_TIME_PARTITION_INSERT && !isAllowDiskClean) {
        if (labelNum == 0) {
            ret = GMERR_UNEXPECTED_NULL_VALUE;
            DB_LOG_AND_SET_LASERR(ret, "add notify to ts background manager for label list has no label.");
            goto EXIT;
        }
        logicalLabel = *(DmVertexLabelT **)DbListItem(irStmt->labelList, labelNum - 1);
        DB_ASSERT(logicalLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_TS_LOGICAL);  // 内存表不能被insert into
        ret = CheckLabelOverLimitWithLog(stmt, logicalLabel);
        if (ret != GMERR_OK) {
            goto EXIT;
        }
    }

    // execute sql statement
    ret = TsExecutePlanWithTrans(stmt);
    TsSetOperateStat(ret, stmt, irStmt->labelList, tagType);  // 更新执行统计信息
    if (tagType != T_TIME_PARTITION_INSERT || !isAllowDiskClean) {
        goto EXIT;
    }

    // for insert_into, it's the last label, for other cases, list has one label.
    // check over limit anyway, this function has its own log

    logicalLabel = *(DmVertexLabelT **)DbListItem(irStmt->labelList, labelNum - 1);
    ret = TsBgAddNotifyByType(stmt->base.session, logicalLabel, tagType, ret);
EXIT:
    ReleaseLabelRLatchFromList(latchList);
    return ret;
}

static Status ProcessDdlCmd(TsQryStmtT *stmt, SqlIrStmtT *irStmt, DbListT *latchList)
{
    DB_LOG_INFO("TsServiceEntry try execute ddl sql, sql=%s", stmt->monitor.sql);
    // DDL command
    stmt->base.utilityStmt = (NodeT *)irStmt->utilityStmt;
    (void)DrtKeepThisWorkerAlive(NULL);
    Status ret = ExecuteDdlInTransaction(stmt, irStmt);
    if (ret == GMERR_OK) {
        ProcessPostDdlTransaction(stmt, irStmt);
        // ddl执行成功释放锁，失败在TsPrepAndProcSql统一释放
        ReleaseLabelWLatchFromList(latchList);
    }

    return ret;
}

static Status ProcessExecFuncCmd(TsQryStmtT *stmt, SqlIrStmtT *irStmt, DbListT *latchList)
{
    DB_POINTER2(stmt, irStmt);
    DB_UNUSED(latchList);
    ExecFuncStmtT *execStmt = (ExecFuncStmtT *)irStmt->utilityStmt;
    Status ret = GMERR_OK;
    if (execStmt->funcType != (int32_t)FUNC_TYPE_AGING) {
        ret = GMERR_FEATURE_NOT_SUPPORTED;
        DB_LOG_AND_SET_LASERR(ret, "only support tsdb aging now");  // LCOV_EXCL_LINE
        return ret;
    }
    DB_LOG_INFO("TsServiceEntry try execute aging sql, sql=%s", stmt->monitor.sql);
    ret = ExecTsdbAgingFunc(stmt, irStmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (TsTransTimeout(&stmt->monitor)) {
        ret = GMERR_REQUEST_TIME_OUT;
        DB_LOG_AND_SET_LASERR(ret, "ts exec aging func too long, exceed %" PRIu64 " ms, sql:%s",
            TsGetTransTimeout(&stmt->monitor), TsGetTransSql(&stmt->monitor));
        return ret;
    }
    return ret;
}

static bool IsTsDdlCmd(SqlIrStmtT *irStmt, NodeTagT cmdTag)
{
    if (cmdTag == T_TS_CREATE_TABLE_STMT || cmdTag == T_TS_DROP_TABLE_STMT || cmdTag == T_TS_ALTER_TABLE_STMT) {
        return true;
    }
    // explain执行非ddl语句失败
    if (irStmt->utilityStmt != NULL && irStmt->utilityStmt->tag != T_TS_EXEC_INTERNAL_FUNC_STMT) {
        return true;
    }
    return false;
}

static void TsReleaseExecResource(SqlIrStmtT *irStmt, NodeTagT cmdTag, Status ret)
{
    if (irStmt == NULL || irStmt->labelList == NULL) {
        return;
    }
    if (ret != GMERR_OK) {
        // 释放ddl加的latch写锁
        if (IsTsDdlCmd(irStmt, cmdTag) && irStmt->latchList != NULL) {
            ReleaseLabelWLatchFromList(irStmt->latchList);
        }
    }

    if (irStmt->labelList != NULL) {
        ReleaseLabelFromList(irStmt->labelList);
    }
}

static Status TsHandleAnalyzeFailedRes(Status analyzeRet, NodeT *parsedStmt)
{
    NodeTagT cmdTag = parsedStmt->tag;
    // only duplicate table return value needs this bool
    if (cmdTag == T_TS_CREATE_TABLE_STMT && ((SqlCreateTableStmtT *)parsedStmt)->ifNotExists &&
        analyzeRet == GMERR_DUPLICATE_TABLE) {
        return GMERR_OK;
    }

    // only duplicate table return value needs this bool
    if (cmdTag == T_TS_ALTER_TABLE_STMT && ((SqlAlterTableStmtT *)parsedStmt)->ifExists &&
        analyzeRet == GMERR_UNDEFINED_TABLE) {
        return GMERR_OK;
    }
    return analyzeRet;
}

static bool IsTsSupportExplainType(NodeTagT tag)
{
    return (tag == T_SQL_INSERT_STMT || tag == T_TS_SELECT_STMT || tag == T_TS_BULK_INSERT_STMT ||
            tag == T_TS_COPY_TO_STMT);
}

static Status TsSqlVerify(TsQryStmtT *stmt, SqlParsedListT *parsedList, char *executeSql)
{
    Status ret = GMERR_OK;
    uint32_t count = DbListGetItemCnt(&parsedList->cmdList);
    if (count > 1) {
        DB_LOG_AND_SET_LASERR(
            GMERR_FEATURE_NOT_SUPPORTED, "multi commands in one request in ts, cur sql: %s", executeSql);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    // check explain
    NodeT *parsedStmt = *(NodeT **)DbListItem(&parsedList->cmdList, 0);
    if (stmt->base.result.sqlType == EXPLAIN_SQL && !IsTsSupportExplainType(parsedStmt->tag)) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "not dml and dql in ts explain, cur sql: %s", executeSql);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    // 参数化查询只支持select语句
    if (stmt->isPrepareSql && parsedStmt->tag != T_TS_SELECT_STMT) {
        ret = GMERR_FEATURE_NOT_SUPPORTED;
        DB_LOG_AND_SET_LASERR(ret, "not select stmt in ts param qry, sql:%s", executeSql);
        return ret;
    }
    return ret;
}

static Status TsSqlExecute(TsQryStmtT *stmt, SqlIrStmtT *irStmt, char *executeSql, SqlParsedListT *parsedList)
{
    if (irStmt->utilityStmt == NULL) {
        return ProcessDqlAndDmlCmd(stmt, irStmt, irStmt->latchList, parsedList);
    }

    if (irStmt->utilityStmt->tag == T_TS_EXEC_INTERNAL_FUNC_STMT) {
        return ProcessExecFuncCmd(stmt, irStmt, irStmt->latchList);
    }

    return ProcessDdlCmd(stmt, irStmt, irStmt->latchList);
}

Status TsSqlCmdProcess(TsQryStmtT *stmt, SqlParsedListT *parsedList, char *executeSql)
{
    Status ret = TsSqlParse(stmt->memCtx, executeSql, parsedList);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Parse ts cmd, sql:%s", executeSql);  // LCOV_EXCL_LINE
        return ret;
    }

    ret = TsSqlVerify(stmt, parsedList, executeSql);
    if (ret != GMERR_OK) {
        return ret;
    }

    SqlIrStmtT *irStmt = NULL;
    NodeT *parsedStmt = *(NodeT **)DbListItem(&parsedList->cmdList, 0);
    ret = TsSqlAnalyze(stmt->base.session, stmt->memCtx, parsedStmt, &irStmt);
    if (ret != GMERR_OK) {
        TsReleaseExecResource(irStmt, parsedStmt->tag, ret);
        return TsHandleAnalyzeFailedRes(ret, parsedStmt);
    }

    ret = TsSqlExecute(stmt, irStmt, executeSql, parsedList);
    TsReleaseExecResource(irStmt, parsedStmt->tag, ret);
    return ret;
}

static Status TsPrepAndProcSql(TsQryStmtT *stmt)
{
    //  parse sql statements
    char *executeSql = NULL;
    SqlParsedListT parsedList = {0};
    Status ret = TsSqlPrepare(stmt, &parsedList, &executeSql);
    if (ret != GMERR_OK) {
        return ret;
    }
    DB_LOG_INFO("TsServiceEntry try prepare sql, sql:%s", executeSql);
    return TsSqlCmdProcess(stmt, &parsedList, executeSql);
}

static Status TsExecBulkInsert(TsQryStmtT *stmt)
{
    DB_POINTER(stmt);
    // get seek pos
    RpcSeekFirstOpMsg(stmt->base.session->req);

    // analyze, no parser for bulk insert
    SqlIrStmtT *irStmt = NULL;
    DmVertexLabelT *logicTbl = NULL;
    Status ret = TsBulkInsertAnalyze(stmt, &irStmt, &logicTbl);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = DbAppendListItem(irStmt->labelList, &logicTbl);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    PlanCtxT execPlan = {0};
    InitPlanCtx(&execPlan, stmt->memCtx, 0);
    ret = GenerateTsExecPlan(stmt->memCtx, irStmt, &execPlan);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    TsFillStmtPlanTree(stmt, &execPlan);

    LabelRWLatchT *labelLatch = NULL;
    ret = AcquireLabelRLatch(logicTbl, &labelLatch);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    bool isAllowDiskClean = DbCfgGetBoolLite(DB_CFG_TS_ALLOW_DISK_CLEAN, NULL);
    if (!isAllowDiskClean) {
        ret = CheckLabelOverLimitWithLog(stmt, logicTbl);
        if (ret != GMERR_OK) {
            LabelRLatchRelease(labelLatch);
            goto EXIT;
        }
    }
    if (DmVertexLabelIsTsMemLabel(logicTbl)) {
        ret = ExecuteMemInsert(stmt);
    } else {
        ret = TsExecPlan(&stmt->base);  // execute, no transaction for bulk insert
        TsSetOperateStat(ret, stmt, irStmt->labelList, T_TS_BULK_INSERT_STMT);
    }
    // check over limit anyway, this function has its own log
    if (isAllowDiskClean) {
        ret = TsBgAddNotifyByType(stmt->base.session, logicTbl, T_TIME_PARTITION_INSERT, ret);
    }
    LabelRLatchRelease(labelLatch);
EXIT:
    if (logicTbl != NULL) {
        (void)CataReleaseVertexLabel(logicTbl);
    }
    return ret;
}

static Status TsInitQryStmt(DrtProcCtxT *procCtx, DbMemCtxT *memCtx, TsQryStmtT *stmt)
{
    DB_POINTER2(procCtx, stmt);
    // init stmt
    SessionT *session = (SessionT *)procCtx->conn->session;
    InitTsStmt(memCtx, session, stmt);

    // init stmt session
    Status ret = PtlTsInitSessionResource(procCtx, stmt->base.session);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "init TS resource of session.");  // LCOV_EXCL_LINE
        return ret;
    }
    // set TsQryStmtT to sqlCtx for plan monitor
    session->sqlCtx->currentFusionStmt = (void *)stmt;

    // init stmt monitor
    uint32_t serverTimeoutMs = QrySessionGetServerTimeoutWithInterval(stmt->base.session);
    TsTransMonitorInit(&stmt->monitor, serverTimeoutMs);

    // init stmt receiver
    ret = QryInitReceiver(memCtx, &stmt->base);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "init TS receiver.");  // LCOV_EXCL_LINE
        return ret;
    }

    // init lock timeout
    uint32_t hungTimeUs = 0;
    if ((ret = DbGetHungTime(WORKER_HUNG_LEVEL_ONE, &hungTimeUs)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "get hung time in ts.");  // LCOV_EXCL_LINE
        return ret;
    }
    stmt->lockTimeoutUs = hungTimeUs;

    OpHeaderT *opHeader = ProtocolPeekFirstOpHeader(&procCtx->msg);
    stmt->isPrepareSql = (opHeader->opCode == MSG_OP_RPC_TS_PREPARE_EXEC) ? true : false;
    return ret;
}

static void CloseTempFileForLargeResult(Status ret, DestReceiverT *destReceiver)
{
    SerTableDestReceiverT *serTableDestReceiver = (SerTableDestReceiverT *)destReceiver;
    if (serTableDestReceiver && serTableDestReceiver->useTempFile && serTableDestReceiver->tempFile) {
        if (ret == GMERR_OK || ret == GMERR_FILE_NO_SPACE_ERROR) {
            SeBufFileCloseWithoutDelete(serTableDestReceiver->tempFile);
        } else {
            SeBufFileClose(serTableDestReceiver->tempFile);
        }
    }
}

// =============== tsdb op proc end =========================
static Status TsServiceFinish(SessionT *session, TsQryStmtT *stmt, OpHeaderT *opHeader, Status ret)
{
    Status rspRet = GMERR_OK;
    if (stmt->monitor.timeoutTime > 0 && TsTransTimeout(&stmt->monitor)) {
        ret = GMERR_REQUEST_TIME_OUT;
        DB_LOG_ERROR(ret, "ts exec too long, exceed %" PRIu64 " ms, sql:%s", TsGetTransTimeout(&stmt->monitor),
            TsGetTransSql(&stmt->monitor));
    }
    DB_LOG_INFO("TsServiceEntry request end, opCode=%" PRIu32, opHeader == NULL ? MSG_OP_RPC_NONE : opHeader->opCode);

    rspRet = opHeader ? SrvDataSendResponse(session, ret, opHeader->opCode) : SrvDataSendResponse(session, ret, 0);
    CloseTempFileForLargeResult(rspRet == GMERR_OK ? ret : rspRet, stmt->base.destReceiver);
    TsDestroyFusionStmt(stmt);
    return rspRet;
}

static Status TsHandleExeFailedRes(TsQryStmtT *stmt, OpHeaderT *opHeader, Status ret)
{
    if (ret == GMERR_FILE_NO_SPACE_ERROR) {  // 大结果集临时文件限制
        Status endRet =
            stmt->base.destReceiver->receiveEnd(stmt->base.session, stmt->base.destReceiver, 1, opHeader->opCode);
        ret = (endRet == GMERR_OK) ? ret : endRet;
    }
    return ret;
}

static Status TsServiceOperate(OpHeaderT *opHeader, SessionT *session, TsQryStmtT *stmt)
{
    if (SECUREC_UNLIKELY(SeGetStorageStatus(session->seInstance->seIns) == SE_ON_DISK_EMRGNCY)) {
        return GMERR_DATABASE_NOT_AVAILABLE;
    }
    Status ret = GMERR_OK;
    switch (opHeader->opCode) {
        case MSG_OP_RPC_TS_PREPARE_EXEC:
        case MSG_OP_RPC_TS_EXEC:
            ret = TsPrepAndProcSql(stmt);
            break;
        case MSG_OP_RPC_TS_BULK_INSERT:
            ret = TsExecBulkInsert(stmt);
            break;
        default:
            ret = GMERR_DATA_EXCEPTION;
            DB_LOG_ERROR(ret, "Client's Req opCode is %" PRIu32, opHeader->opCode);
            break;
    }
    if (ret != GMERR_OK) {
        return TsHandleExeFailedRes(stmt, opHeader, ret);
    }
    return GMERR_OK;
}

Status TsServiceEntry(DrtServiceCtxT *serviceCtx, DrtProcCtxT *procCtx)
{
    DB_POINTER5(serviceCtx, procCtx, procCtx->conn, procCtx->conn->session, procCtx->conn->auditUserInfo);
    OpHeaderT *opHeader = ProtocolPeekFirstOpHeader(&procCtx->msg);
    if (opHeader == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "No op header in ts req.");  // LCOV_EXCL_LINE
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DB_LOG_INFO("TsServiceEntry get request, opCode=%" PRIu32, opHeader->opCode);

    Status ret = RemoveTempFileLargeResult(procCtx->conn);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "clear large result temp file when a new ts request comes.");  // LCOV_EXCL_LINE
        return ret;
    }

    SessionT *session = (SessionT *)procCtx->conn->session;
    DbMemCtxT *memCtx = NULL;
    if ((ret = TsAllocMemCtx(session, &memCtx)) != GMERR_OK) {
        return ret;
    }
    TsQryStmtT stmt = {0};
    if ((ret = TsInitQryStmt(procCtx, memCtx, &stmt)) != GMERR_OK) {
        goto EXIT;
    }
    if ((ret = PtlTsVerifyRequest(procCtx, session, opHeader->opCode)) != GMERR_OK) {
        goto EXIT;
    }

    if ((ret = stmt.base.destReceiver->receivePrepare(stmt.base.destReceiver, opHeader)) != GMERR_OK) {
        goto EXIT;
    }
    ret = TsServiceOperate(opHeader, session, &stmt);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = stmt.base.destReceiver->receiveEnd(session, stmt.base.destReceiver, 1, opHeader->opCode);

EXIT:
    return TsServiceFinish(session, &stmt, opHeader, ret);
}

void TsGetOptRule(bool *ruleEnable)
{
    ruleEnable[RULE_EXTRACT_PUSH_DOWN_EXTD_ORDER_BY] = true;
    ruleEnable[RULE_EXTRACT_PUSH_DOWN_ORDER_BY] = true;
    ruleEnable[RULE_EXTRACT_PUSH_DOWN_GROUP_BY] = true;
    ruleEnable[RULE_EXTRACT_PUSH_DOWN_AGG_SYSTEM] = true;
    ruleEnable[RULE_IMPL_PROJECT] = true;
    ruleEnable[RULE_IMPL_FILTER] = true;
    ruleEnable[RULE_IMPL_BULK_INSERT] = true;
    ruleEnable[RULE_IMPL_COPY_TO] = true;
    ruleEnable[RULE_IMPL_HASH_AGG] = true;
    ruleEnable[RULE_IMPL_LIMIT] = true;
    ruleEnable[RULE_IMPL_ORDER_BY] = true;
    ruleEnable[RULE_IMPL_TIME_PARTITIONSCAN] = true;
    ruleEnable[RULE_IMPL_INSERT] = true;
    ruleEnable[RULE_IMPL_EXTEND_PROJECT] = true;

    ruleEnable[RULE_IMPL_SEQ_SCAN] = true;
    ruleEnable[RULE_IMPL_EXPR_PROJECT] = true;
    ruleEnable[RULE_IMPL_EXTEND_ORDER_BY] = true;

    ruleEnable[RULE_IMPL_BUILD] = true;
    ruleEnable[RULE_INDEXING_SCAN_FOR_SELECT] = true;
}

void TsServercieInitSrvAm(void)
{
    TsSrvAmT am = {
        .tsQueryOperateStatFunc = TsQueryOperateStatImpl,
        .serverImportTsTablesFunc = ServerImportTsTablesImpl,
    };
    TsSrvAmInit(&am);
}

SO_EXPORT_FOR_TS Status TsServiceInit(void)
{
#if defined(FEATURE_STREAM) && !defined(FEATURE_CS)
    // 暂时隔离，后续改为cmake编译不编译ts特性
    return GMERR_OK;
#endif
    TsServercieInitSrvAm();
    // 目前由于优化器未编入server端，因此此函数暂时在此调用，后续应该放到compile_init里
    OptInitOptCache(NULL);
    // for single table now, if multi table needed in the future, please change it
    bool ruleEnable[RULE_END] = {0};
    TsGetOptRule(ruleEnable);
    Status ret = OptInitOptimizer((DbMemCtxT *)DbGetTopDynMemCtx(NULL), ruleEnable);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "init optimizer");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = ModelServiceRegister(DataServiceMgrGet(), TsServiceEntry, MODEL_TS);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "register service entry function TsServiceEntry in standard model");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = TsLazyInit();
    if (ret != GMERR_OK) {
        TsLazyUnInit();
        DB_LOG_ERROR(ret, "finish time-series lazy init");  // LCOV_EXCL_LINE
    }
    return ret;
}

SO_EXPORT_FOR_TS void TsServiceUnInit(void)
{
    TsLazyUnInit();
    OptUnInitOptimizer(NULL);
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
