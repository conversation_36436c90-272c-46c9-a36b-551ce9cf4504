/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:通过leafref反查引用的节点
 * Note1: memCtx is used in short process period , we will free all memory after finishing one request
 * Author:
 * Create: 2024-07-18
 */
#include "ee_session_interface.h"
#include "ee_diff_common.h"
#include "ee_diff_fetch.h"
#include "ee_diff_generate.h"
#include "ee_yang_edit_common.h"
#include "ee_associative_array.h"
#include "ee_error_path.h"
#include "dm_meta_yang_info.h"
#include "cpl_yang_planner.h"
#include "cpl_yang_parser_ext.h"
#include "cpl_yang_verifier_ext.h"
#include "cpl_yang_analyzer_ext.h"
#include "cpl_ir_explain.h"
#include "cpl_ir_plan.h"
#include "ptl_yang_utils.h"
#include "ptl_yang_service_trim.h"
#include "ptl_yang_service_print.h"
#include "ptl_yang_service_plan.h"
#include "ptl_yang_service_validate.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

typedef struct YangReferencedPassiveItem {
    YangNodeItemT *node;
    AddrArrayT *addr;
} YangReferencedPassiveItemT;

static Status YangReferencedGenUpdateAddrForCur(
    VerifyDataCtxT *ctx, YangPassiveClauseInfoT *clauseInfo, YangPathSetT *pathSet, AddrArrayT *addr)
{
    DB_ASSERT(addr->addrNum >= clauseInfo->parentLevel);
    size_t size = sizeof(AddrArrayT) + sizeof(HpTupleAddr) * clauseInfo->parentLevel;
    AddrArrayT *addrArray = DbDynMemCtxAlloc(ctx->memCtx, size);
    if (addrArray == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "create HpTupleAddr Array, size: %zu.", size);  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    };
    addrArray->addrNum = clauseInfo->parentLevel;
    for (uint32_t i = 0; i < addrArray->addrNum; i++) {
        if (addr->addr[i] == DB_INVALID_ID64) {  // 构造的节点不要加入
            addrArray->addrNum = i;
            break;
        }
        addrArray->addr[i] = addr->addr[i];
    }

    Status ret = YangPathSetInsert(pathSet, addrArray);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(ctx->memCtx, addrArray);
    }
    return ret == GMERR_DUPLICATE_OBJECT ? GMERR_OK : ret;
}

static Status YangReferencedGenUpdateAddrForCurByIndex(
    VerifyDataCtxT *ctx, YangPassiveClauseInfoT *clauseInfo, YangPathSetT *pathSet, DmVertexT *vertex, bool *done)
{
    Status ret = GMERR_OK;
    *done = false;

    if (clauseInfo->passivePlan == NULL) {
        return GMERR_OK;
    }

    ret = ResetVerifyDataCtx(ctx, DM_YANG_VALIDATE_PASSIVE, &clauseInfo->planKey, clauseInfo->passivePlan);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Reset Verify data context.");  // LCOV_EXCL_LINE
        return ret;
    }

    DmValueT value = {};
    ret = DmGetPropeValueByNodeAndPropId(vertex, clauseInfo->filterNodeId, clauseInfo->filterPropeId, &value);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "get value, filterNodeId=%" PRIu32 ", filterPropeId=%" PRIu32 ".", clauseInfo->filterNodeId,
            clauseInfo->filterPropeId);
        // LCOV_EXCL_STOP
        return ret;
    }

    ExecPlanCtxT *eePlanCtx = GetPlanCtxByType(ctx, DM_YANG_VALIDATE_PASSIVE);
    ret = YangExecPassivePlanByIndex(ctx, pathSet, "", eePlanCtx, &value);
    if (ret != GMERR_OK) {
        return ret;
    }
    *done = true;
    return GMERR_OK;
}

static Status YangReferencedGenCurrentSetByType(VerifyDataCtxT *ctx, YangReferencedPassiveItemT passiveItem,
    DmYangValidateTypeE type, DmVertexT *vertex, uint32_t deleteLevel)
{
    YangNodeItemT *node = passiveItem.node;
    AddrArrayT *addr = passiveItem.addr;
    DbListT *clauseList = NULL;
    Status ret = YangFetchPassivePlan(type, node, &clauseList);
    if (ret == GMERR_NO_DATA) {
        return GMERR_OK;
    }

    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get clause list, labelId=%" PRIu32 ".", node->vertexLabelId);  // LCOV_EXCL_LINE
        return ret;
    }

    DbOamapT *curSet = GetCurSetHandler(ctx);
    const uint32_t clauseCount = DbListGetItemCnt(clauseList);
    for (uint32_t i = 0; i < clauseCount; i++) {
        YangPassiveClauseInfoT *clauseInfo = DbListItem(clauseList, i);  // 保证i正确不判空
        if (clauseInfo->parentLevel > deleteLevel) {
            continue;  // 共同祖先节点已经被删除，不处理
        }

        uint32_t hash = YangPlanKeyToHash32(&clauseInfo->planKey);
        YangPathSetT *pathSet = (YangPathSetT *)DbOamapLookup(curSet, hash, &clauseInfo->planKey, NULL);
        if (pathSet == NULL) {
            ret = YangInsertNewEleToSet(ctx->memCtx, curSet, &clauseInfo->planKey, false, &pathSet);
            if (ret != GMERR_OK) {
                return ret;
            }
        }

        bool done = false;
        ret = YangReferencedGenUpdateAddrForCurByIndex(ctx, clauseInfo, pathSet, vertex, &done);
        if (ret != GMERR_OK) {
            return ret;
        }

        if (done) {
            continue;
        }

        ret = YangReferencedGenUpdateAddrForCur(ctx, clauseInfo, pathSet, addr);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return GMERR_OK;
}

static Status YangReferencedGenCurrentSet(
    VerifyDataCtxT *ctx, YangNodeItemT *node, AddrArrayT *addr, DmVertexT *vertex, uint32_t deleteLevel)
{
    Status ret = GMERR_OK;
    YangReferencedPassiveItemT passiveItem = {node, addr};
    ret = YangReferencedGenCurrentSetByType(ctx, passiveItem, DM_YANG_VALIDATE_LEAFREF, vertex, deleteLevel);
    if (ret != GMERR_OK) {
        return ret;
    }

    return YangReferencedGenCurrentSetByType(
        ctx, passiveItem, DM_YANG_VALIDATE_NO_REQUIRED_LEAFREF, vertex, deleteLevel);
}

typedef struct {
    DmNodeT *self;
    DmNodeT *parent;
} NodeWithParentT;

static Status YangReferencedGenCurrentSetForNode(
    VerifyDataCtxT *ctx, NodeWithParentT node, AddrArrayT *addr, AASlotT *slot, uint32_t deleteLevel)
{
    Status ret = GMERR_OK;
    DmVertexT *vertex = slot->dmVertex;
    YangScanInfoT info = {.memCtx = ctx->memCtx, .seRunCtx = ctx->session->seInstance};
    bool visible = false;
    ret = NodeIsVisible(&info, slot, node.self, node.parent, &visible);
    if (ret != GMERR_OK || !visible) {
        return ret;
    }

    // 自己
    YangNodeItemT nodeItem = {
        .vertexLabelId = vertex->vertexDesc->labelId,
        .uniqueNodeId = node.self->nodeDesc->yangInfoDesc->uniqueNodeId,
        .propeId = DB_INVALID_UINT32,
    };
    ret = YangReferencedGenCurrentSet(ctx, &nodeItem, addr, NULL, deleteLevel);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 属性
    uint32_t propeNum = node.self->nodeDesc->recordDesc->propeNum;
    DmRecordT *record = node.self->currRecord;
    for (uint32_t i = 0; i < propeNum; i++) {
        DmPropertyInfoT *propeInfo = &(record->recordDesc->propeInfos[i]);
        if (DmIsUselessProperty(propeInfo)) {
            continue;
        }

        nodeItem.propeId = i;
        ret = YangReferencedGenCurrentSet(ctx, &nodeItem, addr, vertex, deleteLevel);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    // 孩子
    for (uint32_t i = 0; i < node.self->nodeDesc->nodeNumPerElement; i++) {
        NodeWithParentT nodeI = {
            .parent = node.self,
            .self = NULL,
        };
        ret = DmNodeGetChildNodeByIndex(node.self, i, &nodeI.self);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = YangReferencedGenCurrentSetForNode(ctx, nodeI, addr, slot, deleteLevel);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return GMERR_OK;
}

static Status YangReferencedGenCurrentSetForVertex(
    VerifyDataCtxT *ctx, AASlotT *slot, AddrArrayT *addr, uint32_t deleteLevel)
{
    Status ret = GMERR_OK;
    DmVertexT *vertex = slot->dmVertex;
    // 自己
    YangNodeItemT nodeItem = {
        .vertexLabelId = vertex->vertexDesc->labelId,
        .uniqueNodeId = DM_YANG_TREE_NODE_IS_VERTEX,
        .propeId = DB_INVALID_UINT32,
    };

    ret = YangReferencedGenCurrentSet(ctx, &nodeItem, addr, NULL, deleteLevel);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 属性
    uint32_t propeNum = vertex->record->recordDesc->propeNum;
    DmRecordT *record = vertex->record;
    for (uint32_t i = 0; i < propeNum; i++) {
        if (DmIsListYangInfo(vertex->vertexDesc->yangInfoDesc) &&
            (i == DM_YANG_ID_PROPE_SUBSCRIPT || i == DM_YANG_PID_PROPE_SUBSCRIPT)) {
            continue;
        }

        DmPropertyInfoT *propeInfo = &(record->recordDesc->propeInfos[i]);
        if (DmIsUselessProperty(propeInfo)) {
            continue;
        }

        nodeItem.propeId = i;
        ret = YangReferencedGenCurrentSet(ctx, &nodeItem, addr, vertex, deleteLevel);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    // 孩子
    for (uint32_t i = 0; i < vertex->vertexDesc->nodeNum; i++) {
        NodeWithParentT nodeI = {.parent = NULL, .self = NULL};
        ret = DmVertexGetChildNodeByIndex(vertex, i, &nodeI.self);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = YangReferencedGenCurrentSetForNode(ctx, nodeI, addr, slot, deleteLevel);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return GMERR_OK;
}

static Status YangReferencedDeleteVertexProp(VerifyDataCtxT *ctx, AASlotT *slot, uint32_t propeId, AddrArrayT *addr)
{
    Status ret = GMERR_OK;
    YangNodeItemT node = {
        .vertexLabelId = slot->dmVertex->vertexDesc->labelId,
        .uniqueNodeId = DM_YANG_TREE_NODE_IS_VERTEX,
        .propeId = propeId,
    };

    ret = YangReferencedGenCurrentSet(ctx, &node, addr, slot->dmVertex, addr->addrNum);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

static Status YangReferencedDeleteNodeProp(VerifyDataCtxT *ctx, AASlotT *slot, YangNodeItemT nodeId, AddrArrayT *addr)
{
    Status ret = GMERR_OK;
    DmNodeT *node = NULL;
    YangScanInfoT info = {.memCtx = ctx->memCtx, .seRunCtx = ctx->session->seInstance};
    ret = YangGetNodeWithNPAByUniqueId(&info, slot, nodeId.uniqueNodeId, &node);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = YangReferencedGenCurrentSet(ctx, &nodeId, addr, slot->dmVertex, addr->addrNum);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

static Status YangReferencedDeleteVertex(VerifyDataCtxT *ctx, AASlotT *slot, AddrArrayT *addr, uint32_t deleteLevel);

typedef struct YangReferencedDeleteChildCtx {
    TopoScanDescT *desc;
    DmVertexLabelT *vertexLabel;
    AASlotT *childSlot;
} YangReferencedDeleteChildCtxT;

static void YangReferencedEndDeleteChildVertex(YangReferencedDeleteChildCtxT *childCtx)
{
    if (childCtx->childSlot != NULL) {
        DeleteAASlot(childCtx->childSlot);
        childCtx->childSlot = NULL;
    }

    if (childCtx->vertexLabel != NULL) {
        (void)CataReleaseVertexLabel(childCtx->vertexLabel);
        childCtx->vertexLabel = NULL;
    }

    if (childCtx->desc != NULL) {
        TopoEndScan(childCtx->desc);
        childCtx->desc = NULL;
    }
}

static Status YangReferencedBeginDeleteChildVertex(
    YangReferencedDeleteChildCtxT *childCtx, VerifyDataCtxT *ctx, AASlotT *slot, DmEdgeLabelT *edgeLabel)
{
    Status ret = GMERR_OK;
    TopoInfoT topo = {.edgeLabel = edgeLabel, .direction = EDGE_OUT};
    ret = TopoBeginScan(ctx->memCtx, &topo, ctx->session->seInstance, &childCtx->desc);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "begin scan, edgeLabel=%s.", edgeLabel->metaCommon.metaName);  // LCOV_EXCL_LINE
        return ret;
    }

    ret = TopoReScan(childCtx->desc, slot);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set source vertex, edgeLabel=%s.", edgeLabel->metaCommon.metaName);  // LCOV_EXCL_LINE
        goto ERROR;
    }

    ret = CataGetVertexLabelById(NULL, edgeLabel->destVertexLabelId, &childCtx->vertexLabel);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "get destination vertex label, edgeLabel=%s.", edgeLabel->metaCommon.metaName);
        // LCOV_EXCL_STOP
        goto ERROR;
    }

    ret = NewAASlotWithVertexLabel(ctx->memCtx, childCtx->vertexLabel, &childCtx->childSlot);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "create aaSlot, vertexLabel=%s.", childCtx->vertexLabel->metaCommon.metaName);
        // LCOV_EXCL_STOP
        goto ERROR;
    }

    return GMERR_OK;
ERROR:
    YangReferencedEndDeleteChildVertex(childCtx);
    return ret;
}

static Status YangReferencedDoDeleteChildVertex(
    YangReferencedDeleteChildCtxT *childCtx, VerifyDataCtxT *ctx, AddrArrayT *addr, uint32_t deleteLevel)
{
    Status ret = GMERR_OK;
    while ((ret = TopoGetNext(childCtx->desc, childCtx->childSlot)) == GMERR_OK) {
        AddrArrayT *childAddr =
            DbDynMemCtxAlloc(ctx->memCtx, sizeof(AddrArrayT) + sizeof(HpTupleAddr) * (addr->addrNum + 1));
        if (childAddr == NULL) {
            // LCOV_EXCL_START
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "address array, addrNum=%" PRIu32 ".", addr->addrNum + 1);
            // LCOV_EXCL_STOP
            return GMERR_OUT_OF_MEMORY;
        }

        childAddr->addrNum = addr->addrNum + 1;
        for (uint32_t i = 0; i < addr->addrNum; i++) {
            childAddr->addr[i] = addr->addr[i];
        }
        childAddr->addr[addr->addrNum] = childCtx->childSlot->addr;
        ret = YangReferencedDeleteVertex(ctx, childCtx->childSlot, childAddr, deleteLevel);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if (ret != GMERR_NO_DATA) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "get next vertex, vertexLabel=%s.", childCtx->vertexLabel->metaCommon.metaName);
        // LCOV_EXCL_STOP
        return ret;
    }
    return GMERR_OK;
}

static Status YangReferencedDeleteChildVertex(
    VerifyDataCtxT *ctx, AASlotT *slot, DmEdgeLabelT *edgeLabel, AddrArrayT *addr, uint32_t deleteLevel)
{
    Status ret = GMERR_OK;
    YangReferencedDeleteChildCtxT childCtx = {};  // 保存临时申请的资源
    ret = YangReferencedBeginDeleteChildVertex(&childCtx, ctx, slot, edgeLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = YangReferencedDoDeleteChildVertex(&childCtx, ctx, addr, deleteLevel);
    YangReferencedEndDeleteChildVertex(&childCtx);
    return ret;
}

static Status YangReferencedDeleteNode(VerifyDataCtxT *ctx, AASlotT *slot, uint16_t uniqNodeId, AddrArrayT *addr)
{
    Status ret = GMERR_OK;
    DmVertexDescT *vertexDesc = slot->dmVertex->vertexDesc;

    NodeIterT iter = {};
    YangScanInfoT info = {.memCtx = ctx->memCtx, .seRunCtx = ctx->session->seInstance};
    ret = YangGetNodeAndParentWithNPAByUniqueId(&info, slot, uniqNodeId, &iter);
    if (ret != GMERR_OK) {
        return ret;
    }

    NodeWithParentT node = {.self = iter.node, .parent = iter.parent};
    ret = YangReferencedGenCurrentSetForNode(ctx, node, addr, slot, addr->addrNum);
    if (ret != GMERR_OK) {
        return ret;
    }

    for (uint32_t i = 0; i < vertexDesc->edgeLabelNum; i++) {
        uint32_t edgeLabelId = vertexDesc->commonInfo->relatedEdgeLabels[i].edgeLabelId;
        DmEdgeLabelT *edgeLabel = NULL;
        ret = CataGetEdgeLabelById(edgeLabelId, &edgeLabel, DbGetInstanceByMemCtx(ctx->memCtx));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get edge label, id=%" PRIu32 ".", edgeLabelId);  // LCOV_EXCL_LINE
            return ret;
        }

        // 如果当前边关联的没有sourceNode，或者关联的source nodeId不是当前要删除的node或者该node的孩子则略过
        if (edgeLabel->sourceNodePath == NULL || !DmSourceNodeIdIsInNode(edgeLabel->sourceUniqueNodeId, iter.node)) {
            (void)CataReleaseEdgeLabel(edgeLabel);
            continue;
        }

        ret = YangReferencedDeleteChildVertex(ctx, slot, edgeLabel, addr, addr->addrNum);
        if (ret != GMERR_OK) {
            (void)CataReleaseEdgeLabel(edgeLabel);
            return ret;
        }
    }

    return GMERR_OK;
}

static Status YangReferencedDeleteVertex(VerifyDataCtxT *ctx, AASlotT *slot, AddrArrayT *addr, uint32_t deleteLevel)
{
    Status ret = GMERR_OK;
    ret = YangReferencedGenCurrentSetForVertex(ctx, slot, addr, deleteLevel);
    if (ret != GMERR_OK) {
        return ret;
    }

    DmVertexDescT *vertexDesc = slot->dmVertex->vertexDesc;
    for (uint32_t i = 0; i < vertexDesc->edgeLabelNum; i++) {
        uint32_t edgeLabelId = vertexDesc->commonInfo->relatedEdgeLabels[i].edgeLabelId;
        DmEdgeLabelT *edgeLabel = NULL;
        ret = CataGetEdgeLabelById(edgeLabelId, &edgeLabel, DbGetInstanceByMemCtx(ctx->memCtx));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get edge label, id=%" PRIu32 ".", edgeLabelId);  // LCOV_EXCL_LINE
            return ret;
        }

        if (edgeLabel->sourceVertexLabelId != vertexDesc->labelId) {
            (void)CataReleaseEdgeLabel(edgeLabel);
            continue;
        }

        ret = YangReferencedDeleteChildVertex(ctx, slot, edgeLabel, addr, deleteLevel);
        if (ret != GMERR_OK) {
            (void)CataReleaseEdgeLabel(edgeLabel);
            return ret;
        }
    }

    return GMERR_OK;
}

// 递归删除的同时生成，current set，只触发leafref 所以一定在属性上
Status YangReferencedDelete(VerifyDataCtxT *ctx, AASlotT *slot, YangNodeItemT nodeId, AddrArrayT *addr)
{
    Status ret = GMERR_OK;
    if (nodeId.uniqueNodeId == DM_YANG_TREE_NODE_IS_VERTEX) {
        if (nodeId.propeId == DB_INVALID_UINT32) {  // 删除Vertex
            ret = YangReferencedDeleteVertex(ctx, slot, addr, addr->addrNum - 1);
        } else {  // 删除vertex下的属性
            ret = YangReferencedDeleteVertexProp(ctx, slot, nodeId.propeId, addr);
        }
    } else {
        if (nodeId.propeId == DB_INVALID_UINT32) {  // 删除Node
            ret = YangReferencedDeleteNode(ctx, slot, nodeId.uniqueNodeId, addr);
        } else {  // 删除Node下的属性
            ret = YangReferencedDeleteNodeProp(ctx, slot, nodeId, addr);
        }
    }
    if (ret != GMERR_OK) {
        return ret;
    }

    return YangFilterNodeInsert(ctx->filterNodeCtx, slot, nodeId.uniqueNodeId, nodeId.propeId);
}

typedef Status (*YangHandleResult)(VerifyDataCtxT *ctx, ExecPlanCtxT *eePlanCtx, AASlotT *slot, bool *done);

static Status YangGetPathForCurrent(ExecPlanCtxT *eePlanCtx, DbMemCtxT *memCtx, AddrArrayT **addr)
{
    EStateT *estate = eePlanCtx->estate;
    uint32_t currentPathId = eePlanCtx->estate->currentPathId;
    uint32_t parentLevel = currentPathId + DM_TREE_ROOT_LEVEL;  // 自己触发自己的Plan，共同组建节点就是自己

    size_t size = sizeof(AddrArrayT) + sizeof(HpTupleAddr) * parentLevel;
    AddrArrayT *addrArray = DbDynMemCtxAlloc(memCtx, sizeof(HpTupleAddr) * size);
    if (addrArray == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "HpTupleAddr Array, size is: %zu.", size);  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    };

    addrArray->addrNum = parentLevel;
    AASlotT *slot = NULL;
    for (uint32_t i = 0; i < addrArray->addrNum; i++) {
        // pathArray的顺序为从根节点到当前节点依次为0->currentPathId,所以从前往后getPathSlot
        // current节点(位置为0)在pathArray中不可信，需要从AA中获取
        if (currentPathId == i) {
            slot = AAGetCurrentPropSlot(eePlanCtx->aa);
        } else {
            slot = ExecGetPathSlot(estate, i);
        }
        DB_ASSERT(slot != NULL);
        addrArray->addr[i] = slot->addr;
    }
    *addr = addrArray;
    return GMERR_OK;
}

static Status YangGetLabelIdArrayForCurrent(ExecPlanCtxT *eePlanCtx, DbMemCtxT *memCtx, LabelIdArrayT **labels)
{
    EStateT *estate = eePlanCtx->estate;
    uint32_t currentPathId = eePlanCtx->estate->currentPathId;
    uint32_t parentLevel = currentPathId + DM_TREE_ROOT_LEVEL;  // 自己触发自己的Plan，共同组建节点就是自己

    size_t size = sizeof(LabelIdArrayT) + sizeof(uint32_t) * parentLevel;
    LabelIdArrayT *array = DbDynMemCtxAlloc(memCtx, sizeof(LabelIdArrayT) * size);
    if (array == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "HpTupleAddr Array, size is: %zu.", size);  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    };
    array->labelNum = parentLevel;
    array->labelIds = (uint32_t *)(array + 1);
    AASlotT *slot = NULL;
    for (uint32_t i = 0; i < array->labelNum; i++) {
        slot = ExecGetPathSlot(estate, i);
        array->labelIds[i] = slot->dmVertex->vertexDesc->labelId;
    }
    *labels = array;
    return GMERR_OK;
}

static Status YangReferencedGenNewSet(VerifyDataCtxT *ctx, ExecPlanCtxT *eePlanCtx)
{
    Status ret = GMERR_OK;
    DbOamapT *newSet = GetNewSetHandler(ctx);
    DbMemCtxT *memCtx = GetNewMemCtxHandler(ctx);
    uint32_t hash = YangPlanKeyToHash32(&eePlanCtx->curKey);
    YangPathSetT *pathSet = (YangPathSetT *)DbOamapLookup(newSet, hash, &eePlanCtx->curKey, NULL);
    if (pathSet == NULL) {
        ret = YangInsertNewEleToSet(memCtx, newSet, &eePlanCtx->curKey, false, &pathSet);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    AddrArrayT *addrArray = NULL;
    ret = YangGetPathForCurrent(eePlanCtx, ctx->memCtx, &addrArray);
    if (ret != GMERR_OK) {
        return ret;
    }

    for (uint32_t i = 0; i < addrArray->addrNum; i++) {
        if (addrArray->addr[i] == DB_INVALID_ID64) {
            addrArray->addrNum = i;  // 构造的节点不要加入
            break;
        }
    }

    ret = YangPathSetInsert(pathSet, addrArray);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(ctx->memCtx, addrArray);
    }
    return ret == GMERR_DUPLICATE_OBJECT ? GMERR_OK : ret;
}

// 第一次校验记录校验失败的leafref结果
static Status YangHandleReferencedResult1(VerifyDataCtxT *ctx, ExecPlanCtxT *eePlanCtx, AASlotT *slot, bool *done)
{
    if (slot->dmValue.value.intValue < 0) {
        return GMERR_OK;  // leafref校验成功
    }

    return YangReferencedGenNewSet(ctx, eePlanCtx);
}

// 第二次校验记录校验成功的leafref结果
static Status YangHandleReferencedResult2(VerifyDataCtxT *ctx, ExecPlanCtxT *eePlanCtx, AASlotT *slot, bool *done)
{
    Status ret = GMERR_OK;
    if (slot->dmValue.value.intValue >= 0) {
        return GMERR_OK;  // leafref校验失败
    }

    AddrArrayT *addrArray = NULL;
    ret = YangGetPathForCurrent(eePlanCtx, ctx->memCtx, &addrArray);
    if (ret != GMERR_OK) {
        return ret;
    }

    LabelIdArrayT *labelIds = NULL;
    ret = YangGetLabelIdArrayForCurrent(eePlanCtx, ctx->memCtx, &labelIds);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (DmIsLeafListVertexDesc(slot->dmVertex->vertexDesc)) {
        addrArray->addrNum--;
        labelIds->labelNum--;
    }

    AddrLabelIdArrayT array = {.addrs = addrArray, .labelIds = labelIds};
    ret = DbAppendListItem(&ctx->referencedList, &array);  // 注意保存的的指针
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static Status YangHandleReferencedResult3(VerifyDataCtxT *ctx, ExecPlanCtxT *eePlanCtx, AASlotT *slot, bool *done)
{
    Status ret = GMERR_OK;
    if (slot->dmValue.value.intValue < 0) {
        return GMERR_OK;  // leafref校验失败
    }

    AddrArrayT *addrArray = NULL;
    ret = YangGetPathForCurrent(eePlanCtx, ctx->memCtx, &addrArray);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 同一个Plan这里可能是重复调用了，性能会有损失
    ret = ResetVerifyDataCtx(ctx, DM_YANG_VALIDATE_REFERENCED2, &eePlanCtx->curKey, eePlanCtx->curPlan);
    if (ret != GMERR_OK) {
        return ret;
    }

    ExecPlanCtxT *eePlanCtx2 = GetPlanCtxByType(ctx, DM_YANG_VALIDATE_REFERENCED2);
    eePlanCtx2->estate->addrArray = addrArray;
    eePlanCtx2->estate->readPos = 0;
    ret = ExecReScan(eePlanCtx2->planState);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = ExecProcNode(eePlanCtx2->planState, eePlanCtx2->aa);
    if (ret == GMERR_NO_DATA) {
        goto EXIT;
    }

    AASlotT *slot2 = AAGetNextPropSlot(eePlanCtx2->aa);
    if (slot2 == NULL) {
        goto EXIT;
    }

    ret = YangHandleReferencedResult2(ctx, eePlanCtx2, slot2, NULL);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    *done = DbListGetItemCnt(&ctx->referencedList) > 0;
EXIT:
    eePlanCtx2->estate->addrArray = NULL;  // 防止指向已经释放的内存
    return ret;
}

static Status YangExecuteOneReferencedPlan(VerifyDataCtxT *verifyCtx, ExecPlanCtxT *eePlanCtx, AddrArrayT *addrArray,
    YangHandleResult handleResult, bool *done)
{
    Status ret = GMERR_OK;
    eePlanCtx->estate->addrArray = addrArray;
    eePlanCtx->estate->readPos = 0;
    ret = ExecReScan(eePlanCtx->planState);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "ExecReScan.");  // LCOV_EXCL_LINE
        return ret;
    }
    while ((ret = ExecProcNode(eePlanCtx->planState, eePlanCtx->aa)) == GMERR_OK) {
        AASlotT *slot = AAGetNextPropSlot(eePlanCtx->aa);
        if (slot == NULL) {
            break;
        }
        ret = handleResult(verifyCtx, eePlanCtx, slot, done);
        if (ret != GMERR_OK) {
            break;
        }

        if (*done) {
            break;
        }
    }

    eePlanCtx->estate->addrArray = NULL;  // 防止指向已经释放的内存
    if (ret == GMERR_NO_DATA) {
        ret = GMERR_OK;
    }
    return ret;
}

static Status YangExecuteOneReferenced(VerifyDataCtxT *verifyCtx, ExecPlanCtxT *eePlanCtx, YangPathSetT *pathSet,
    YangHandleResult handleResult, bool *done)
{
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < DbListGetItemCnt(&pathSet->list); i++) {
        AddrArrayT **addrArray = DbListItem(&pathSet->list, i);  // 一定非空
        ret = YangExecuteOneReferencedPlan(verifyCtx, eePlanCtx, *addrArray, handleResult, done);
        if (ret != GMERR_OK) {
            return ret;
        }

        if (*done) {
            break;
        }
    }

    return GMERR_OK;
}

static Status YangExecuteReferenced(VerifyDataCtxT *verifyCtx, YangHandleResult handleResult)
{
    Status ret = GMERR_OK;
    YangPlanKeyT *key = NULL;
    YangPlanT *plan = NULL;
    DbOamapT *map = GetCurSetHandler(verifyCtx);
    const DmYangValidateTypeE types[] = {DM_YANG_VALIDATE_LEAFREF, DM_YANG_VALIDATE_NO_REQUIRED_LEAFREF};
    bool done = false;
    for (uint32_t i = 0; i < sizeof(types) / sizeof(types[0]); i++) {
        uint32_t iter = 0;
        while ((ret = YangSortedPlanFetch(types[i], &iter, YangGetSortedList(types[i]), &key, &plan)) == GMERR_OK) {
            uint32_t hash = YangPlanKeyToHash32(key);
            YangPathSetT *pathSet = (YangPathSetT *)DbOamapLookup(map, hash, key, NULL);
            if (pathSet == NULL) {
                // 不是受影响的plan，执行
                continue;
            }

            ret = ResetVerifyDataCtx(verifyCtx, key->type, key, plan);
            if (ret != GMERR_OK) {
                // LCOV_EXCL_START
                DB_LOG_ERROR(ret, "Reset Verify data context: type %" PRIu32 ".", (uint32_t)key->type);
                // LCOV_EXCL_STOP
                return ret;
            }

            ExecPlanCtxT *eePlanCtx = GetPlanCtxByType(verifyCtx, key->type);
            ret = YangExecuteOneReferenced(verifyCtx, eePlanCtx, pathSet, handleResult, &done);
            if (ret != GMERR_OK) {
                return ret;
            }

            if (done) {
                break;
            }
        }

        if (ret != GMERR_NO_DATA) {
            return ret;
        }
    }
    return GMERR_OK;
}

static bool IsLegalLeafRefStack(DmFormulaStackT *curStack)
{
    // GetLeafrefPath 参数路径必须是绝对路径。
    if (!DmFormulaStackIsPath(curStack)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "leafref stack, stack is not path");  // LCOV_EXCL_LINE
        return false;
    }
    DmFormulaNodeT *topNode = DmFormulaStackGetTopNode(curStack);
    DmFormulaStackT *pathStack = topNode->dmOperator.pathStack;
    DmFormulaNodeT *pathBeginNode = DmFormulaStackGetBottomNode(pathStack);

    if (pathBeginNode->type == OPERATOR && pathBeginNode->dmOperator.type == XPATH_ROOT) {
        return true;
    }

    DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "leafref stack, path begin node is not root");
    return false;
}

static void YangInitVerifyClause(TextT *leafrefPath, YangVerifyClauseT *yangClause)
{
    DmYangClauseT dmYangClause = {0};
    dmYangClause.strLen = leafrefPath->len;
    dmYangClause.str = leafrefPath->str;
    dmYangClause.type = DM_YANG_CLAUSE_BOTTOM;

    yangClause->clause = dmYangClause;
    yangClause->key = (YangPlanKeyT){.labelId = 0, .uniqueNodeId = 0, .propeId = 0, .type = DM_YANG_VALIDATE_BOTTOM};
}

static Status YangCreateXpathPlan(YangVerifyRuleStmtT *stmt, IRPlanT *irLogPlan, YangPlanT **outPlan)
{
    YangPlanT *yangPlan = DbArenaAllocZero(stmt->arena, sizeof(YangPlanT));
    if (yangPlan == NULL) {
        // LCOV_EXCL_START
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "yangPlan, size is: %zu.", sizeof(YangPlanT));
        // LCOV_EXCL_STOP
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    YangPlannerInfoT info = {0};
    info.paraNum = 0;
    info.pathLen = 0;
    info.notFist = 0;
    info.planArena = stmt->arena;
    info.currentPathId = DB_INVALID_UINT32;
    info.argumentLevel = NULL;
    Status ret = BuildYangPlanTree(&info, irLogPlan, &yangPlan->plan.planTree);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 实际使用paraNum存在上限约束，不会导致转换数据丢失
    yangPlan->plan.totalParamNum = info.paraNum;
    yangPlan->plan.pathLen = (uint16_t)(info.pathLen + 1);  // 加一是因为depth为0时深度为1
    yangPlan->plan.currentPathId = (uint16_t)info.currentPathId;

    ret = YangDebugExplainPlan(info.planArena->memCtx, yangPlan, &stmt->currClause.key);
    if (ret != GMERR_OK) {
        return ret;
    }
    *outPlan = yangPlan;
    return GMERR_OK;
}

// 移除顶层的subplan，并把节点（Node或prope）信息取出
static YangNodeItemT YangRemoveTopSubplan(IRPlanT *logicIR)
{
    IRExprT *root = logicIR->root;

    logicIR->root = root->children[0];

    IRExprT *rightChild = root->children[1];
    YangNodeItemT nodeId = {0};
    if (rightChild == NULL) {
        return nodeId;
    } else if (rightChild->op->type == IR_ITEMOP_CHILDISH_NODE) {
        OpItemNodeT *node = (OpItemNodeT *)(void *)rightChild->op;
        nodeId.vertexLabelId = node->vertexLabelId;
        nodeId.uniqueNodeId = (uint16_t)node->uniqNodeId;
        nodeId.propeId = DB_MAX_UINT32;
    } else if (rightChild->op->type == IR_ITEMOP_PROPERTY) {
        OpItemPropertyT *p = (OpItemPropertyT *)(void *)rightChild->op;
        nodeId.vertexLabelId = p->nodeInfo.vertexLabelId;
        nodeId.uniqueNodeId = (uint16_t)p->nodeInfo.uniqNodeId;
        nodeId.propeId = p->nodeInfo.propertyId;
    } else {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Get unexpected type when remove top subPlan, rightChild op=%" PRIu32 ".",
            rightChild->op->type);
    }
    return nodeId;
}

static Status YangAnalyzeXpathOnly(YangVerifyRuleStmtT *stmt, DmFormulaStackT *formulaStack, IRPlanT **logicIR)
{
    IRPlanT *irPlan = (IRPlanT *)DbArenaAllocZero(stmt->arena, sizeof(IRPlanT));
    if (irPlan == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "yang analyzer");  // LCOV_EXCL_LINE
        return GMERR_MEMORY_OPERATE_FAILED;
    };
    Status ret = YangValidateAnalyzer(stmt, formulaStack, &(irPlan->root));
    if (ret != GMERR_OK) {
        return ret;
    }
    *logicIR = irPlan;
    return GMERR_OK;
}

static Status BuildOneReferencedPlan(
    VerifyRuleCtxT *ruleCtx, TextT *leafrefPath, YangPlanT **yangPlan, YangNodeItemT *nodeId)
{
    YangVerifyClauseT yangClause = {0};
    YangInitVerifyClause(leafrefPath, &yangClause);

    DmFormulaStackT *curStack = NULL;
    Status ret = YangParseXpath(ruleCtx->stmt, &yangClause.clause, &curStack);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "parse leafref stack, leafref str: %s", leafrefPath->str);  // LCOV_EXCL_LINE
        return ret;
    }

    if (!IsLegalLeafRefStack(curStack)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_INVALID_PARAMETER_VALUE, "Get illegal leafref stack, leafref str: %s", leafrefPath->str);
    }

    ruleCtx->stmt->currClause = yangClause;
    ret = YangVerifyXpath(ruleCtx->stmt, curStack);
    if (ret != GMERR_OK) {
        return ret;
    }

    IRPlanT *logicIR = NULL;
    ret = YangAnalyzeXpathOnly(ruleCtx->stmt, curStack, &logicIR);
    if (ret != GMERR_OK) {
        return ret;
    }

    *nodeId = YangRemoveTopSubplan(logicIR);
    PtlYangTracePlan(ruleCtx->memCtx, logicIR);
    ret = YangOptimize(ruleCtx->stmt->arena, &ruleCtx->vertexMap, logicIR);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Optimize Yang IR.");  // LCOV_EXCL_LINE
        return ret;
    }

    YANG_TRACE_MASK_LOG(YANG_TRACE_VALIDATE, "========================phyiscal===========================");
    PtlYangTracePlan(ruleCtx->memCtx, logicIR);

    ret = YangCreateXpathPlan(ruleCtx->stmt, logicIR, yangPlan);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

static Status InitVertexMap(VerifyRuleCtxT *ruleCtx)
{
    DB_POINTER3(ruleCtx, ruleCtx->stmt, ruleCtx->memCtx);
    Status ret = GMERR_OK;

    SessionT *session = YangVerifyStmtGetSession(ruleCtx->stmt);
    DmVertexLabelT *vertexLabel = NULL;
    DbOamapIteratorT iter = 0;
    uint64_t startTime = DbClockGetTsc();
    while (CataFetchVertexLabelNoSysView(&iter, GET_VERTEX_LABEL, &vertexLabel, NULL) == GMERR_OK) {
        if (DbExceedTime(startTime, EXEC_SPLIT_TIME)) {
            (void)DrtKeepThisWorkerAlive(NULL);
            startTime = DbClockGetTsc();
        }

        if (vertexLabel->metaCommon.namespaceId != session->namespaceId || !DmIsRootYangVertexLabel(vertexLabel)) {
            (void)CataReleaseVertexLabel(vertexLabel);
            continue;
        }

        uint32_t vertexLabelId = vertexLabel->metaCommon.metaId;
        ret = DbOamapInsert(&ruleCtx->vertexMap, vertexLabelId, &vertexLabel->metaCommon.metaId, vertexLabel, NULL);
        if (ret != GMERR_OK) {
            (void)CataReleaseVertexLabel(vertexLabel);  // 正常场景在RemoveVertexLabelFromList中释放表的元数据
            DB_LOG_AND_SET_LASERR(ret, "insert vertexlabel.");  // LCOV_EXCL_LINE
            return ret;
        }
    }
    return GMERR_OK;
}

static Status YangReferencedBuildInputPlan(
    VerifyRuleCtxT *ruleCtx, TextT *leafrefPath, YangPlanT **plan, YangNodeItemT *nodeId)
{
    // 解析成FormulaStack，做模型校验生成plan
    Status ret = InitVertexMap(ruleCtx);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = BuildOneReferencedPlan(ruleCtx, leafrefPath, plan, nodeId);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

static Status YangReferencedExecPlan(VerifyDataCtxT *verifyCtx, YangNodeItemT nodeId)
{
    ExecPlanCtxT *eePlanCtx = GetPlanCtxByType(verifyCtx, DM_YANG_VALIDATE_NODE_SET);
    uint64_t startTime = DbClockGetTsc();
    Status ret = GMERR_OK;
    while ((ret = ExecProcNode(eePlanCtx->planState, eePlanCtx->aa)) == GMERR_OK) {
        if (DbExceedTime(startTime, EXEC_SPLIT_TIME)) {
            (void)DrtKeepThisWorkerAlive(NULL);
            startTime = DbClockGetTsc();
        }

        AASlotT *slot = AAGetNextPropSlot(eePlanCtx->aa);
        if (slot == NULL) {
            break;
        }
        // 4. 业务逻辑（打印）
        YANG_TRACE_MASK_LOG(
            YANG_TRACE_VALIDATE, "==========================find  YangReferencedInputProc==========================");

        AddrArrayT *addr = NULL;
        ret = YangGetPathForCurrent(eePlanCtx, verifyCtx->memCtx, &addr);
        if (ret != GMERR_OK) {
            return ret;
        }

        ret = YangReferencedDelete(verifyCtx, slot, nodeId, addr);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status YangReferencedInputProc(SessionT *session, TextT *leafrefPath, VerifyDataCtxT *verifyCtx)
{
    VerifyRuleCtxT *ruleCtx = NULL;
    Status ret = PrepareVerifyRuleCtx(session, verifyCtx->memCtx, &ruleCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "prepare verify stmt.");  // LCOV_EXCL_LINE
        return ret;
    }

    YangPlanT *yangPlan = NULL;
    YangNodeItemT nodeId = {0};
    ret = YangReferencedBuildInputPlan(ruleCtx, leafrefPath, &yangPlan, &nodeId);
    if (ret != GMERR_OK) {
        goto RETURN;
    }

    ret = YangCreateFilterNodeCtx(verifyCtx->memCtx, &verifyCtx->filterNodeCtx);
    if (ret != GMERR_OK) {
        goto RETURN;
    }
    // 3. 执行plan 得到一个（多个）节点
    YangPlanKeyT curKey = {};  // 空
    ret = ResetVerifyDataCtx(verifyCtx, DM_YANG_VALIDATE_NODE_SET, &curKey, yangPlan);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(
            ret, "Reset Referenced Verify data context: type %" PRIu32 ".", (uint32_t)DM_YANG_VALIDATE_NODE_SET);
        // LCOV_EXCL_STOP
        goto RETURN;
    }

    ret = YangReferencedExecPlan(verifyCtx, nodeId);
    if (ret == GMERR_NO_DATA) {
        ret = GMERR_OK;
    }

RETURN:
    // 5. 释放临时构建的逻辑计划和执行计划
    ReleaseVerifyRuleCtx(ruleCtx);
    if (yangPlan != NULL) {
        ExecReleasePlanTreeMeta(yangPlan->plan.planTree);
    }
    return ret;
}

static int32_t YangCompareAddrArray(const void *item1, const void *item2)
{
    const AddrLabelIdArrayT *a1 = (const AddrLabelIdArrayT *)item1;
    const AddrLabelIdArrayT *a2 = (const AddrLabelIdArrayT *)item2;
    uint32_t addrNum = MIN(a1->addrs->addrNum, a2->addrs->addrNum);
    for (uint32_t i = 0; i < addrNum; i++) {
        if (a1->addrs->addr[i] != a2->addrs->addr[i]) {
            return a1->addrs->addr[i] > a2->addrs->addr[i] ? 1 : -1;
        }
    }

    if (a1->addrs->addrNum != a2->addrs->addrNum) {
        return a1->addrs->addrNum > a2->addrs->addrNum ? 1 : -1;
    }

    return 0;
}

static bool YangIsSubPath(AddrArrayT *path, AddrArrayT *subPath)
{
    if (path->addrNum < subPath->addrNum) {
        return false;
    }

    return path->addr[subPath->addrNum - 1] == subPath->addr[subPath->addrNum - 1];
}

// 按照前缀排序，然后去重
static Status YangReferencedOutputMerge(VerifyDataCtxT *verifyCtx, DbListT *list)
{
    Status ret = 0;
    DbListSort(&verifyCtx->referencedList, YangCompareAddrArray);
    AddrLabelIdArrayT *prev = NULL;
    AddrLabelIdArrayT *current = NULL;
    if (DbListGetItemCnt(&verifyCtx->referencedList) > 0) {
        current = (AddrLabelIdArrayT *)DbListItem(&verifyCtx->referencedList, 0);
        ret = DbAppendListItem(list, current);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "append addrArray to list.");  // LCOV_EXCL_LINE
            return ret;
        }
        prev = current;
    }

    for (uint32_t i = 1; i < DbListGetItemCnt(&verifyCtx->referencedList); i++) {
        current = (AddrLabelIdArrayT *)DbListItem(&verifyCtx->referencedList, i);
        // prev 是 current的子串说明是父子关系（祖先子孙关系），不返回
        if (YangIsSubPath(current->addrs, prev->addrs)) {
            continue;
        }
        ret = DbAppendListItem(list, current);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "append addrArray to list.");  // LCOV_EXCL_LINE
            return ret;
        }
        prev = current;
    }

    return GMERR_OK;
}

Status FillExistVertexByTupleAddr(HpRunHdlT heapRunHdl, DbMemCtxT *memCtx, DmVertexT *vertex, HpTupleAddr tupleAddr)
{
    TupleBufT tupleBuf = {};
    TupleBufInit(&tupleBuf, memCtx);
    Status ret = HeapFetchHpTupleBuffer(heapRunHdl, tupleAddr, &tupleBuf);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "fetch heap tuple buffer, tupleAddr=%" PRIu64 ".", (uint64_t)tupleAddr);  // LCOV_EXCL_LINE
        return ret;
    }
    TupleT tuple = TupleBufGet(&tupleBuf);
    ret = DmDeSerialize2ExistsVertex(tuple.buf, tuple.bufSize, vertex, DmGetCheckMode());
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "deserialize tuple buf, bufSize=%" PRIu32 ".", tuple.bufSize);  // LCOV_EXCL_LINE
        return ret;
    }
    TupleBufRelease(&tupleBuf);
    return GMERR_OK;
}

static Status GetVertexFromHeapByTupleAddr(
    SessionT *session, DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, HpTupleAddr tupleAddr, DmVertexT *vertex)
{
    HpRunHdlT heapRunHdl = {0};
    HeapRunCtxAllocCfgT cfg = {
        .heapShmAddr = vertexLabel->commonInfo->heapInfo.heapShmAddr,
        .seRunCtx = session->seInstance,
        .dmInfo = vertexLabel,
        .isBackGround = false,
        .isUseRsm = false,
    };

    Status ret = HeapLabelAllocAndInitRunctx(&cfg, &heapRunHdl);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "alloc heap run context, vertexLabel=%s.", vertexLabel->metaCommon.metaName);
        // LCOV_EXCL_STOP
        return ret;
    }

    ret = HeapLabelOpen(heapRunHdl, HEAP_OPTYPE_NORMALREAD, memCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "open heap when get vertex from heap.");  // LCOV_EXCL_LINE
        HeapLabelReleaseRunctx(heapRunHdl);
        return ret;
    }

    ret = FillExistVertexByTupleAddr(heapRunHdl, memCtx, vertex, tupleAddr);
    HeapLabelCloseAndResetCtx(heapRunHdl);
    HeapLabelReleaseRunctx(heapRunHdl);
    return ret;
}

static Status GetVertexByTupleAddr(
    SessionT *session, DbMemCtxT *memCtx, HpTupleAddr tupleAddr, uint32_t labelId, DmVertexT **outVertex)
{
    DmVertexLabelT *vertexLabel = NULL;
    Status ret = CataGetVertexLabelById(DbGetInstanceByMemCtx(session->memCtx), labelId, &vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get vertex label for tuple addr. vLabelId=%" PRIu32 ".", labelId);  // LCOV_EXCL_LINE
        return ret;
    }
    DmVertexT *vertex = NULL;
    ret = DmCreateEmptyVertexWithMemCtx(memCtx, vertexLabel, &vertex);
    if (ret != GMERR_OK) {
        (void)CataReleaseVertexLabel(vertexLabel);
        DB_LOG_ERROR(ret, "create empty vertex, vertexLabel=%s", vertexLabel->metaCommon.metaName);  // LCOV_EXCL_LINE
        return ret;
    }
    if (!DmIsRootYangVertexLabel(vertexLabel)) {
        ret = GetVertexFromHeapByTupleAddr(session, memCtx, vertexLabel, tupleAddr, vertex);
        if (ret != GMERR_OK) {
            (void)CataReleaseVertexLabel(vertexLabel);
            return ret;
        }
    }
    (void)CataReleaseVertexLabel(vertexLabel);

    *outVertex = vertex;
    return GMERR_OK;
}

Status YangGeneratePathStringByVertex(DbMemCtxT *memCtx, DmVertexT *dmVertex, char *pathBuf, uint32_t bufSize)
{
    DB_POINTER3(memCtx, dmVertex, pathBuf);
    // 获取父vertexLabel中的nodePath
    char *sourceNodePath = NULL;
    Status ret = QryGetSourceNodePath(memCtx, dmVertex, &sourceNodePath);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 获取父vertexLabel
    const char *labelName = dmVertex->vertexDesc->labelName;
    ret = QryGenSourceNodeAndCurrVertexPath(dmVertex, sourceNodePath, pathBuf, bufSize);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "generate current vertex path for %s", labelName);  // LCOV_EXCL_LINE
        return ret;
    }
    return GMERR_OK;
}

static Status YangBuildOneleafrefPathRes(
    SessionT *session, DbMemCtxT *memCtx, AddrLabelIdArrayT *addrLabelIdArray, char *pathBuf, uint32_t maxBufSize)
{
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < addrLabelIdArray->addrs->addrNum; i++) {
        HpTupleAddr tupleAddr = addrLabelIdArray->addrs->addr[i];
        uint32_t labelId = addrLabelIdArray->labelIds->labelIds[i];

        DmVertexT *vertex = NULL;
        ret = GetVertexByTupleAddr(session, memCtx, tupleAddr, labelId, &vertex);
        if (ret != GMERR_OK) {
            // LCOV_EXCL_START
            DB_LOG_ERROR(ret, "tuple addr get vertex, tupleAddr=%" PRIu64 ", labelId=%" PRIu32 ".", (uint64_t)tupleAddr,
                labelId);
            // LCOV_EXCL_STOP
            return ret;
        }
        ret = YangGeneratePathStringByVertex(memCtx, vertex, pathBuf, maxBufSize);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    // 不打包字段级别的路径
    return GMERR_OK;
}

static Status YangBuildleafrefPathRes(
    SessionT *session, DbMemCtxT *memCtx, DbListT *addrLabelList, LeafrefPathResultT *pathRes)
{
    // 遍历输出结果
    Status ret = GMERR_OK;
    uint32_t count = DbListGetItemCnt(addrLabelList);
    for (uint32_t i = 0; i < count; i++) {
        // 兜底释放
        size_t pathBufSize = sizeof(char) * QRY_ERROR_PATH_PAYLOAD_MAX_LEN;
        char *pathBuf = (char *)DbDynMemCtxAlloc(memCtx, pathBufSize);
        if (pathBuf == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to allocate pathBuf.");  // LCOV_EXCL_LINE
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memset_s(pathBuf, pathBufSize, 0, pathBufSize);
        AddrLabelIdArrayT *addrLabelIdArray = DbListItem(addrLabelList, i);
        // 对输出结果构造errorpath
        ret = YangBuildOneleafrefPathRes(session, memCtx, addrLabelIdArray, pathBuf, QRY_ERROR_PATH_PAYLOAD_MAX_LEN);
        if (ret != GMERR_OK) {
            return ret;
        }

        ret = DbAppendListItem(&(pathRes->paths), &pathBuf);
        if (ret != GMERR_OK) {
            // LCOV_EXCL_START
            DB_LOG_ERROR(ret, "Unable to append list item for leafref path res, pathBuf=%s", pathBuf);
            // LCOV_EXCL_STOP
            return ret;
        }
    }
    return GMERR_OK;
}

void LeafrefPathResInit(DbMemCtxT *memCtx, LeafrefPathResultT *pathRes)
{
    DbCreateList(&pathRes->paths, sizeof(char *), memCtx);
}

static Status YangReferencedOutputProc(SessionT *session, VerifyDataCtxT *verifyCtx)
{
    Status ret = GMERR_OK;
    DbListT list = {};
    DbCreateList(&list, sizeof(AddrLabelIdArrayT), verifyCtx->memCtx);
    ret = YangReferencedOutputMerge(verifyCtx, &list);
    if (ret != GMERR_OK) {
        return ret;
    }

    LeafrefPathResultT pathRes;
    LeafrefPathResInit(verifyCtx->memCtx, &pathRes);
    ret = YangBuildleafrefPathRes(session, verifyCtx->memCtx, &list, &pathRes);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = PtlYangResponseSetLeafrefPathsResult(session, &pathRes);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to set leafref paths result.");  // LCOV_EXCL_LINE
        return ret;
    }

    return GMERR_OK;
}

static Status YangReferencedAll(VerifyDataCtxT *verifyCtx)
{
    Status ret = GMERR_OK;

    // 第一遍校验
    ret = YangExecuteReferenced(verifyCtx, YangHandleReferencedResult1);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 第二遍校验
    ret = YangSwitchToNewSet(verifyCtx);
    if (ret != GMERR_OK) {
        return ret;
    }

    YangDestroyFilterNodeCtx(verifyCtx->filterNodeCtx);
    verifyCtx->filterNodeCtx = NULL;
    ret = YangExecuteReferenced(verifyCtx, YangHandleReferencedResult2);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static Status YangReferencedOne(VerifyDataCtxT *verifyCtx)
{
    Status ret = GMERR_OK;
    // 第一遍校验需要在第一遍校验循环内
    ret = YangExecuteReferenced(verifyCtx, YangHandleReferencedResult3);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

// 1.执行XPath找到指定的子树，可以有多个。
// 2.假装删除找到的子树
// 3.触发leafref增量校验，生成current set。
// 4.leafref失败的节点全部放入new set。
// 5.new set重新校验一遍leafref，成功的节点就是需要输出的节点。
// 6.输结果合并
// 7.输出字符串
Status YangServiceReferencedEntry(SessionT *session, DbMemCtxT *memCtx, void *execObj)
{
    Status ret = GMERR_OK;
    YANG_TRACE_MASK_LOG(YANG_TRACE_VALIDATE, "(INFO)YangServiceReferencedEntry");
    VerifyDataCtxT *verifyCtx = NULL;
    VerifyConfigT tmpVerifyCfg = {0};
    tmpVerifyCfg.verifyType = YANG_VERIFY_LEAFREF;  // 临时填上leafref
    ret = PrepareVerifyDataCtx(session, memCtx, &tmpVerifyCfg, &verifyCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "prepare verify Data Ctx.");  // LCOV_EXCL_LINE
        return ret;
    }
    YangWLockPlanCache(session->namespaceId);
    YangLeafrefPathParamT *param = (YangLeafrefPathParamT *)execObj;
    ret = YangReferencedInputProc(session, &param->path, verifyCtx);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    if (param->mode == LEAFREF_GET_PATH_ONE) {
        ret = YangReferencedOne(verifyCtx);
    } else {
        ret = YangReferencedAll(verifyCtx);
    }
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = YangReferencedOutputProc(session, verifyCtx);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

EXIT:
    YangWUnlockPlanCache(session->namespaceId);
    ReleaseVerifyDataCtx(verifyCtx);
    return ret;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
