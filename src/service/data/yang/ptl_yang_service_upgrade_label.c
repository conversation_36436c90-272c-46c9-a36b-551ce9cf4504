/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: yang service for upgrade labels
 * Author: zengzebin
 * Create: 2025-02-24
 */

#include "db_file.h"
#include "dm_yang_vertex.h"
#include "ee_yang_upgrade_label.h"
#include "ee_yang_edit_common.h"
#include "ptl_yang_service_upgrade_label.h"

Status QryRecProcessLabelsInFiles(RecoveryYangCtxT *yangCtx, const char *dir)
{
    Status ret = QryRecUpgradeVertexLabels(yangCtx, dir);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryVerifyRelatedEdges(yangCtx);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryRecCreateVertexLabels(yangCtx, dir);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryRecCreateEdgeLabels(yangCtx, dir);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status RecoverUpgradeLabelsFuncInner(const char *dir, DbOamapT *metaDiffMap)
{
    if (dir == NULL) {
        DB_LOG_WARN(GMERR_OK, "Null upgrade dir.");  // LCOV_EXCL_LINE
        return GMERR_OK;
    }
    if (!DbDirExist(dir)) {
        DB_LOG_WARN(GMERR_OK, "Upgrade dir %s unexist.", dir);
        return GMERR_OK;
    }

    RecoveryYangCtxT yangCtx = {0};
    Status ret = QryInitRecoveryYangCtx(&yangCtx, metaDiffMap, dir);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryRecProcessLabelsInFiles(&yangCtx, dir);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = CheckLabelDelete(&yangCtx);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = QryRecUpdateMetaInfo(&yangCtx);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    DmYangTraceAlterStructMap(&yangCtx.alterStructMap);

    // 刷数据
    ret = QryRecUpdateData(&yangCtx);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    // 删老版本的表
    ret = QryRecRemoveOldVerLabels(&yangCtx);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

EXIT:
    QryReleaseRecoveryYangCtx(&yangCtx);
    return ret;
}

Status RecoverUpgradeLabelsFuncImpl(DbInstanceHdT dbInstance, DbOamapT *metaDiffMap)
{
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(dbInstance);
    DbCfgValueT recoveryUpgDir = {0};
    Status ret = DbCfgGet(cfgHandle, DB_CFG_YANG_UPGRADE_DIR_PATH, &recoveryUpgDir);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get cfg DB_CFG_YANG_UPGRADE_DIR_PATH.");  // LCOV_EXCL_LINE
        return DbGetStatusInterErrno(ret);
    }
    if (recoveryUpgDir.str[0] == '\0') {
        DB_LOG_INFO("Upgrade dir is not configured");
        return GMERR_OK;
    }
    DB_LOG_INFO("RecoveryPath: %s\n", recoveryUpgDir.str);
    ret = RecoverUpgradeLabelsFuncInner(recoveryUpgDir.str, metaDiffMap);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

// 更新leaflist节点
Status RecoverUpdateLeafListLabelsFuncImpl(DbOamapT *metaDiffMap)
{
    SessionT *session = NULL;
    Status ret = QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, NULL, &session);
    if (ret != GMERR_OK) {
        return ret;
    }

    QryStmtT *stmt = NULL;
    ret = QryAllocStmt(session, &stmt);
    if (ret != GMERR_OK) {
        QrySessionRelease(session);
        DB_LOG_ERROR(ret, "excutor init stmt");  // LCOV_EXCL_LINE
        return ret;
    }

    ret = QryAllocCtxFromCtxMemPool(DbGetInstanceByMemCtx(stmt->session->memCtx), &stmt->context);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "init alloc ctx.");  // LCOV_EXCL_LINE
        QrySessionRelease(session);
        return ret;
    }

    void *key = NULL;
    uint32_t iter = 0;
    RecoveryLeafListCtxT *metaDiff;
    while (DbOamapFetch(metaDiffMap, &iter, &key, (void **)&metaDiff) == GMERR_OK) {
        // 获取edgeId
        ret = QryReplayMergeLeafList4Upgrade(stmt, metaDiff);
        if (ret != GMERR_OK) {
            QrySessionRelease(session);
            return ret;
        }
    }
    QrySessionRelease(session);
    return ret;
}
