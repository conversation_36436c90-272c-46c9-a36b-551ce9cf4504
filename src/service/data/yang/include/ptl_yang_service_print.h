/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: data service interface
 * Author: guozhiwu
 * Create: 2023-08-21
 */

#ifndef PTL_YANG_SERVICE_PRINT_H
#define PTL_YANG_SERVICE_PRINT_H

#include "srv_log.h"
#include "db_mem_context.h"
#include "dm_meta_basic.h"
#include "dm_meta_prop_label.h"
#include "ee_associative_array_slot.h"
#include "ptl_yang_utils.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

void PtlYangTraceDmVertex(DbMemCtxT *memCtx, AASlotT *slot, YangPlanKeyT *key);

void PtlYangTraceDiffTree(DbMemCtxT *memCtx, const DmYangTreeT *tree);

void PtlYangTraceDeltaSet(DbMemCtxT *memCtx, YangPlanKeyT *key, DbListT *value);

void PtlYangTraceUserDelete(const DmVertexT *vertex, YangPlanKeyT *key);

void PtlYangTracePlan(DbMemCtxT *memCtx, const IRPlanT *plan);

void PtlYangTraceNpaUpdate(const DmVertexT *vertex, YangPlanKeyT *key, bool isDelete);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* PTL_YANG_SERVICE_PRINT_H */
