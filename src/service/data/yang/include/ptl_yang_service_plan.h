/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: data service interface
 * Author: guo<PERSON><PERSON>
 * Create: 2023-09-21
 */

#ifndef PTL_YANG_SERVICE_PLAN_H
#define PTL_YANG_SERVICE_PLAN_H

#include "cpl_yang_common.h"
#include "cpl_yang_plangenerator_interface.h"
#include "ee_plan_state.h"
#include "ee_session_interface.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

typedef struct {
    DbMemCtxT *memCtx;
    YangVerifyRuleStmtT *stmt;   // 单个XPATH的处理上下文,生命周期是单个XPATH
    YangVerifyResultT modelRes;  // 模型校验结果
    bool isFinish;               // 生成plan失败结束标识，非parse、verify失败则停止
    DbOamapT vertexMap;
    DbOamapT edgeMap;
    DbOamapT mergeXPathMap;  // key:XPath原始字符串 value:XPath对应的PlanKey
    Yang<PERSON>lannerInfoT planner;
} VerifyRuleCtxT;

inline static bool IsVerifyRuleFinished(VerifyRuleCtxT *ctx)
{
    DB_POINTER(ctx);
    return ctx->isFinish;
}

inline static void SetVerifyRuleFinish(VerifyRuleCtxT *ctx)
{
    DB_POINTER(ctx);
    ctx->isFinish = true;
}

inline static YangVerifyClauseT *GetVerifyRuleStmtClause(VerifyRuleCtxT *ctx)
{
    DB_POINTER2(ctx, ctx->stmt);
    return &ctx->stmt->currClause;
}

// YANG表定义formula中XPath生成执行计划的入口函数
Status YangServiceCreateXpathPlanEntry(SessionT *session, DbMemCtxT *memCtx, void *execObj);

Status PrepareVerifyRuleCtx(SessionT *session, DbMemCtxT *memCtx, VerifyRuleCtxT **outCtx);

void ReleaseVerifyRuleCtx(VerifyRuleCtxT *ctx);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* PTL_YANG_SERVICE_PLAN_H */
