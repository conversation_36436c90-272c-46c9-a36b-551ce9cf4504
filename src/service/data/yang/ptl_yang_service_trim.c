/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: YANG service Trim dmVertex empty nodes, including NP container, choice, case.
 * Note1: memCtx is used in short process period , we will free all memory after finishing one request
 * Author: guozhiwu
 * Create: 2024-04-09
 */

#include "dm_yang_diff.h"
#include "ee_subtree_raw.h"
#include "ee_dml_common.h"
#include "ee_diff_common.h"
#include "ee_diff_fetch.h"
#include "ee_yang_edit_common.h"
#include "include/ptl_yang_utils.h"
#include "include/ptl_yang_service_validate.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

static Status YangTrimVertexNodeGraph(QryStmtT *stmt, QryLabelT *qryLabel, DmVertexT *vertex, uint64_t newAddr)
{
    bool vertexEmpty = false;
    QryRawVertexT rawVertex = {.isReused = true,
        .isModified = false,
        .vertex = vertex,
        .heapBuf.buf = NULL,
        .heapBuf.bufSize = 0,
        .addr = newAddr};
    Status ret = QryTrimDmVertexGraph(stmt, qryLabel, &rawVertex, &vertexEmpty);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Trim Vertex %s Graph.", qryLabel->def.vertexLabel->metaCommon.metaName);  // LCOV_EXCL_LINE
        return ret;
    }

    if (rawVertex.isModified) {
        YANG_TRACE_MASK_LOG(YANG_TRACE_VALIDATE,
            "|Validate| Trim a %s RawVertex by updating to heap: ", qryLabel->def.vertexLabel->metaCommon.metaName);
        QryTraceRawVertex(stmt, &rawVertex);
    }

    if (!vertexEmpty) {
        return GMERR_OK;
    }

    YANG_TRACE_MASK_LOG(
        YANG_TRACE_VALIDATE, "|Validate| Trim vertex %s from heap.", qryLabel->def.vertexLabel->metaCommon.metaName);
    QryTraceRawVertex(stmt, &rawVertex);

    ret = QryDeleteVertexNodeGraph(stmt, vertex, newAddr, DM_YANG_VERTEX_NODE_ID);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Delete User Root Vertex %s.", vertex->vertexDesc->labelName);  // LCOV_EXCL_LINE
        return ret;
    }

    ret = QryBackupUserRootNpaInfo(stmt, vertex, newAddr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Backup User Root Vertex %s NPA info.", vertex->vertexDesc->labelName);  // LCOV_EXCL_LINE
        return ret;
    }
    return GMERR_OK;
}

static Status IsVirtualRoot(DmVertexT *vertex, bool *isVirtualRoot)
{
    if (!DmIsRootYangVertexDesc(vertex->vertexDesc)) {
        *isVirtualRoot = false;
        return GMERR_OK;
    }
    DmValueT value = {0};
    Status ret = DmVertexGetPropeById(vertex, DM_YANG_ID_PROPE_SUBSCRIPT, &value);
    if (ret != GMERR_OK) {
        return ret;
    }
    *isVirtualRoot = (value.value.uintValue == DM_YANG_ID_PROPE_SYS_VALUE);
    return GMERR_OK;
}

static Status YangTrimEmptyNodesByDiffInner(QryStmtT *stmt, DmYangTreeT *tree)
{
    Status ret = QryYangTreeUpdateNewDmVertex(stmt, tree);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "YangTree update newVertex.");  // LCOV_EXCL_LINE
        return ret;
    }

    DmVertexT *newVertex = DmGetYangNewTreeVertex(tree);
    if (SECUREC_UNLIKELY(newVertex == NULL)) {
        return GMERR_OK;
    }

    bool isVirtualRoot = false;
    ret = IsVirtualRoot(newVertex, &isVirtualRoot);
    if (SECUREC_UNLIKELY((ret != GMERR_OK))) {
        return ret;
    }
    if (isVirtualRoot) {
        return GMERR_OK;
    }

    uint64_t newAddr = DmYangTreeGetNewestAddr(tree);

    QryLabelT *qryLabel = NULL;
    ret = QryGetQryLabelById(stmt, newVertex->vertexDesc->labelId, &qryLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "dmVertex %s get qryLabel.", newVertex->vertexDesc->labelName);  // LCOV_EXCL_LINE
        return ret;
    }

    ret = YangTrimVertexNodeGraph(stmt, qryLabel, newVertex, newAddr);
    QryYangTreeCacheNewDmVertex(stmt, qryLabel, tree);
    return ret;
}

Status YangTrimEmptyNodesByDiff(QryStmtT *stmt, VerifyDataCtxT *ctx)
{
    Status ret = GMERR_OK;
    DmDiffTreeKeyT *key = NULL;
    DmYangTreeT *diffTree = NULL;
    DbOamapIteratorT iter = 0;
    while (DbOamapFetch(&stmt->session->yangTreeCtx->diffCtx->diffFilterMap, &iter, (void **)&key,
               (void **)&diffTree) == GMERR_OK) {
        if (((diffTree->diffCtx->state != DM_DIFF_STATE_CREATE) && (diffTree->diffCtx->state != DM_DIFF_STATE_MERGE))) {
            continue;
        }
        if (!PtlYangTreeCheckNamespace(stmt->session, diffTree)) {
            continue;
        }

        if (QryExceedSplitTime(ctx->startTimestamp, ctx->timeOutThreshold)) {
            ret = GMERR_REQUEST_TIME_OUT;
            // LCOV_EXCL_START
            DB_LOG_ERROR(ret, "Trim by diff, timeOutThreshold= %" PRIu32 ".", ctx->timeOutThreshold);
            // LCOV_EXCL_STOP
            return ret;
        }

        if (stmt->qryCursor == NULL) {
            ret = QryAllocCursor(stmt);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_ERROR(ret, "alloc qryCursor.");  // LCOV_EXCL_LINE
                return ret;
            }
        }

        ret = YangTrimEmptyNodesByDiffInner(stmt, diffTree);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "diff filter tree trim vertex.");  // LCOV_EXCL_LINE
            return ret;
        }
        PtlYangCheckAndKeepAlive(stmt, PTL_YANG_CHECK_TIME_TRIM_NP_NODES);
    }

    QryCloseCursors(stmt);

    return GMERR_OK;
}

static Status YangTrimVertexList(
    QryStmtT *stmt, VerifyDataCtxT *ctx, QryEdgeLabelT *qryEdgeLabel, QryLabelT *qryLabel, QryRawVertexT *rawVertex);

static Status YangTrimVertexChildInner(
    QryStmtT *stmt, VerifyDataCtxT *verifyCtx, QryLabelT *qryLabel, QryRawVertexT *rawVertex)
{
    Status ret = GMERR_OK;
    DmVertexLabelT *vertexLabel = qryLabel->def.vertexLabel;
    // YANG场景，如果root表则没有入边，所有边都是出边；若是list表，则index=0的一定是入边且只有该一条入边，这里需要找出边的数据。
    uint32_t startIdx = DmIsRootYangVertexLabel(vertexLabel) ? 0 : 1;
    for (; startIdx < vertexLabel->commonInfo->edgeLabelNum; startIdx++) {
        uint64_t edgeAddr = 0;
        QryRawVertexT childRawVertex = {0};
        uint32_t curEdgeId = vertexLabel->commonInfo->relatedEdgeLabels[startIdx].edgeLabelId;
        ret = DmVertexGetFirstEdgeTopoAddrById(rawVertex->vertex, curEdgeId, &edgeAddr);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "get %s edge addr by edgeId %" PRIu32 ".", vertexLabel->metaCommon.metaName, curEdgeId);
            return ret;
        }

        if (edgeAddr == 0) {
            continue;
        }

        QryEdgeLabelT *qryEdgeLabel = NULL;
        ret = QryGetQryEdgeLabelById(stmt, curEdgeId, &qryEdgeLabel);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(
                ret, "get vertex %s qryEdge label by id %" PRIu32 ".", vertexLabel->metaCommon.metaName, curEdgeId);
            return ret;
        }

        DmVertexLabelT *destLabel = qryEdgeLabel->destQryLabel->def.vertexLabel;
        if (DmIsLeafListVertexLabel(destLabel)) {
            continue;
        }

        qryEdgeLabel->type = DEST_VERTEX_ADDR;
        ret = QryFetchRelatedVertex(stmt, rawVertex->vertex, qryEdgeLabel, true, &childRawVertex.vertex);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "fetch child vertex %s by parent vertex %s.", destLabel->metaCommon.metaName,
                vertexLabel->metaCommon.metaName);
            return ret;
        }

        if (childRawVertex.vertex == NULL) {
            continue;
        }

        childRawVertex.addr = qryEdgeLabel->destQryLabel->vertexAddr;
        ret = YangTrimVertexList(stmt, verifyCtx, qryEdgeLabel, qryEdgeLabel->destQryLabel, &childRawVertex);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "trim vertex list %s.", vertexLabel->metaCommon.metaName);  // LCOV_EXCL_LINE
            return ret;
        }
    }

    return GMERR_OK;
}

static Status YangTrimSingleListVertex(
    QryStmtT *stmt, VerifyDataCtxT *ctx, QryLabelT *qryLabel, QryRawVertexT *rawVertex)
{
    DB_POINTER2(stmt, qryLabel);
    Status ret = YangTrimVertexChildInner(stmt, ctx, qryLabel, rawVertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "trim list vertex %s.", qryLabel->def.vertexLabel->metaCommon.metaName);  // LCOV_EXCL_LINE
        return ret;
    }

    ret = YangTrimVertexNodeGraph(stmt, qryLabel, rawVertex->vertex, rawVertex->addr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "trim list node graph for %s.", qryLabel->def.vertexLabel->metaCommon.metaName);
        // LCOV_EXCL_STOP
        return ret;
    }

    return GMERR_OK;
}

static Status YangTrimVertexList(
    QryStmtT *stmt, VerifyDataCtxT *ctx, QryEdgeLabelT *qryEdgeLabel, QryLabelT *qryLabel, QryRawVertexT *rawVertex)
{
    DB_POINTER3(stmt, qryEdgeLabel, qryLabel);
    Status ret = GMERR_OK;
    if (QryExceedSplitTime(ctx->startTimestamp, ctx->timeOutThreshold)) {
        ret = GMERR_REQUEST_TIME_OUT;
        DB_LOG_ERROR(ret, "Trim list start, timeOutThreshold= %" PRIu32 ".", ctx->timeOutThreshold);  // LCOV_EXCL_LINE
        return ret;
    }

    ret = YangTrimSingleListVertex(stmt, ctx, qryLabel, rawVertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "trim list vertex for label %s.", qryLabel->def.vertexLabel->metaCommon.metaName);
        // LCOV_EXCL_STOP
        return ret;
    }

    QryRawVertexT current = *rawVertex;
    while (current.vertex != NULL) {
        PtlYangCheckAndKeepAlive(stmt, PTL_YANG_CHECK_TIME_TRIM_NP_NODES);
        if (QryExceedSplitTime(ctx->startTimestamp, ctx->timeOutThreshold)) {
            ret = GMERR_REQUEST_TIME_OUT;
            DB_LOG_ERROR(ret, "Trim list, timeOutThreshold= %" PRIu32 ".", ctx->timeOutThreshold);  // LCOV_EXCL_LINE
            return ret;
        }

        QryRawVertexT next = {0};
        qryEdgeLabel->type = SOURCE_VERTEX_NEXT_EDGE_ADDR;
        ret = QryFetchRelatedVertex(stmt, current.vertex, qryEdgeLabel, true, &next.vertex);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }

        if (next.vertex == NULL) {
            break;
        }

        next.addr = qryEdgeLabel->destQryLabel->vertexAddr;
        ret = YangTrimSingleListVertex(stmt, ctx, qryLabel, &next);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            // LCOV_EXCL_START
            DB_LOG_ERROR(ret, "trim list next for label %s.", qryLabel->def.vertexLabel->metaCommon.metaName);
            // LCOV_EXCL_STOP
            return ret;
        }
        current = next;
    }

    return GMERR_OK;
}

static Status YangTrimVertexRoot(QryStmtT *stmt, VerifyDataCtxT *ctx, QryLabelT *qryLabel, QryRawVertexT *rawVertex)
{
    DB_POINTER2(stmt, qryLabel);

    Status ret = YangTrimVertexChildInner(stmt, ctx, qryLabel, rawVertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "trim vertex child %s.", qryLabel->def.vertexLabel->metaCommon.metaName);  // LCOV_EXCL_LINE
        return ret;
    }

    // trim root vertex
    return YangTrimVertexNodeGraph(stmt, qryLabel, rawVertex->vertex, rawVertex->addr);
}

static bool IsValidYangRootVertexLabel(QryStmtT *stmt, DmVertexLabelT *vertexLabel)
{
    if (!DmIsRootYangVertexLabel(vertexLabel) || DmIsYangNpaVertexLabel(vertexLabel)) {
        return false;
    }

    if (stmt->session->namespaceId != vertexLabel->metaCommon.namespaceId) {
        return false;
    }

    return true;
}

static Status YangGetVertexRoot(QryStmtT *stmt, QryLabelT *qryLabel, QryRawVertexT *rawVertex)
{
    DmSubtreeT *filter = NULL;
    Status ret = DmCreateFilterSubtree(stmt->memCtx, qryLabel->desc, &filter);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "create filter subtree name=%s.", qryLabel->def.vertexLabel->metaCommon.metaName);
        // LCOV_EXCL_STOP
        return ret;
    }

    // 获取 root
    ret = QryFetchVertexByScan(stmt, qryLabel, filter->vertex, false, &rawVertex->vertex, &rawVertex->addr);
    DmDestroySubtree(filter);
    return ret;
}

Status YangTrimEmptyNodesByScan(QryStmtT *stmt, VerifyDataCtxT *ctx)
{
    Status ret = GMERR_OK;
    QryLabelT *qryLabel = NULL;
    DmVertexLabelT *vertexLabel = NULL;
    DbOamapIteratorT iter = 0;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(stmt->session->memCtx);
    while ((vertexLabel = CataFetchVertexLabelByNspId(stmt->session->namespaceId, &iter, dbInstance)) != NULL) {
        if (!IsValidYangRootVertexLabel(stmt, vertexLabel)) {
            (void)CataReleaseVertexLabel(vertexLabel);
            continue;
        }

        if (stmt->qryCursor == NULL) {
            ret = QryAllocCursor(stmt);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_AND_SET_LASERR(ret, "alloc qryCursor.");  // LCOV_EXCL_LINE
                (void)CataReleaseVertexLabel(vertexLabel);
                return ret;
            }
        }

        ret = QryGetQryLabelById(stmt, vertexLabel->metaCommon.metaId, &qryLabel);
        (void)CataReleaseVertexLabel(vertexLabel);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }

        QryRawVertexT rawVertex = {0};
        ret = YangGetVertexRoot(stmt, qryLabel, &rawVertex);
        if (SECUREC_UNLIKELY((ret != GMERR_OK))) {
            return ret;
        }

        if (rawVertex.vertex == NULL) {
            continue;
        }
        bool isVirtualRoot = false;
        ret = IsVirtualRoot(rawVertex.vertex, &isVirtualRoot);
        if (SECUREC_UNLIKELY((ret != GMERR_OK))) {
            return ret;
        }
        if (isVirtualRoot) {
            continue;
        }

        ret = YangTrimVertexRoot(stmt, ctx, qryLabel, &rawVertex);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Trim root vertex %s.", rawVertex.vertex->vertexDesc->labelName);  // LCOV_EXCL_LINE
            return ret;
        }

        PtlYangCheckAndKeepAlive(stmt, PTL_YANG_CHECK_TIME_TRIM_NP_NODES);
    }
    QryCloseCursors(stmt);

    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
