/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author:
 * Create: 2023-08-18
 */
#include "srv_data_yang.h"
#include "include/ptl_yang_data_service.h"
#include "include/ptl_yang_service_parser.h"
#include "include/ptl_yang_service_plan.h"
#include "include/ptl_yang_service_audit.h"
#include "include/ptl_yang_service_edit.h"
#include "include/ptl_yang_service_diff.h"
#include "include/ptl_yang_service_validate.h"
#include "include/ptl_yang_service_subtree.h"
#include "include/ptl_yang_service_imexport.h"
#include "include/ptl_yang_service_upgrade_label.h"
#include "include/ptl_yang_utils.h"
#include "srv_data_prepare.h"
#include "srv_data_service.h"
#include "srv_data_fastpath_log.h"
#include "srv_data_statistics.h"
#include "ptl_service_utils.h"
#include "ee_context.h"
#include "cpl_yang_planner.h"
#include "cpl_fp_parser_dml.h"
#include "dm_meta_role.h"
#include "ee_init.h"
#include "ee_cursor.h"
#include "ee_dcl_ctrl.h"
#include "ee_command.h"
#include "ee_yang_edit_common.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define YANG_SERVICE_NAME_MAX 128

typedef struct YangSrvEntry YangSrvEntryT;

typedef Status (*YangServicePrepare)(SessionT *session, YangSrvEntryT *entry);

typedef Status (*YangServiceParse)(SessionT *session, DbMemCtxT *memCtx, void **entry);

typedef Status (*YangServiceExecute)(SessionT *session, DbMemCtxT *memCtx, void *execObj);

typedef struct YangServiceFunc {
    YangServicePrepare prepare;
    YangServiceParse parse;
    YangServiceExecute execute;
} YangServiceFuncT;

struct YangSrvEntry {
    uint32_t opCode;
    QryTypeE qryType;
    YangServiceFuncT yangServiceFunc;
    QryPrivCheckerT privChecker;
};

typedef struct {
    uint8_t *freeSlotId;
    bool *needCached;
} YangContextFreeSlotT;

static Status YangServiceActiveTrxCheck(QryStmtT *stmt)
{
    TrxStateE trxState = SeTransGetState(stmt->session->seInstance);
    if (SECUREC_UNLIKELY(trxState == TRX_STATE_ABORT)) {
        DB_LOG_AND_SET_LASERR(GMERR_TRANSACTION_ROLLBACK, "YANG transaction needs rollback.");
        return GMERR_TRANSACTION_ROLLBACK;
    }

    if (QryTypeIsDML(stmt->context->type) || stmt->context->type == QRY_TYPE_SUBTREE_FILTER ||
        stmt->context->type == QRY_TYPE_FETCH_DIFF) {
        // YANG的六原语编辑、读diff和数据校验必须在开启了 “乐观+可重复读” 事务内执行
        TrxTypeE trxType = SeTransGetTrxType(stmt->session->seInstance);
        IsolationLevelE trxLevel = SeTransGetIsolationLevel(stmt->session->seInstance);
        if (trxState != TRX_STATE_ACTIVE || trxType != OPTIMISTIC_TRX || trxLevel != REPEATABLE_READ) {
            DB_LOG_ERROR(GMERR_TRANS_MODE_MISMATCH,
                "Transaction not started or mode mis-match for Yang service operation, opCode = %" PRIu32
                ", trxState = %" PRIu32 ", trxType = %" PRIu32 ", trxLevel = %" PRIu32 ".",
                (uint32_t)stmt->context->opCode, trxState, trxType, trxLevel);
            return GMERR_TRANS_MODE_MISMATCH;
        }
    }

    return GMERR_OK;
}

static Status YangPrepareStmtContext(QryStmtT *stmt, YangSrvEntryT *entry)
{
    DB_POINTER2(stmt, entry);

    Status ret = QryAllocCtxFromCtxMemPool(DbGetInstanceByMemCtx(stmt->session->memCtx), &stmt->context);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "alloc context, opCode = %" PRIu32 ".", entry->opCode);
        return ret;
    }

    QryContextT *context = stmt->context;
    context->sysPrivVersion = DB_INVALID_UINT32;
    context->objPrivVersion = DB_INVALID_UINT32;
    context->nspPrivVersion = DB_INVALID_UINT32;
    context->lastPrivPass = false;
    context->opCode = entry->opCode;
    context->type = entry->qryType;
    return GMERR_OK;
}

static Status YangServiceEntryPrepare(SessionT *session, YangSrvEntryT *entry)
{
    DB_POINTER2(session, entry);
    // 重新申请stmt的context 并设置 optype参数
    Status ret = YangPrepareStmtContext(session->currentStmt, entry);
    if (SECUREC_UNLIKELY((ret != GMERR_OK))) {
        return ret;
    }

    return QryAllocCursor(session->currentStmt);
}

static Status YangServiceBeginTrx(SessionT *session)
{
    // 检查事务状态：1、事务bort状态；2、未开启 “乐观+可重复读” 事务执行读diff和数据校验
    Status ret = YangServiceActiveTrxCheck(session->currentStmt);
    if (SECUREC_UNLIKELY((ret != GMERR_OK))) {
        return ret;
    }

    // 开启事务：理论上YANG需要用户主动开“乐观RR”事务，不能支持隐式开启事务。但是为了兼容性，这里先支持。
    // 当前导入导出，leafrefpath查询支持隐式事务操作
    QryStmtT *stmt = session->currentStmt;
    QryTypeE type = stmt->context->type;
    SeRunCtxHdT seRunCtx = session->seInstance;
    TrxStateE trxState = SeTransGetState(seRunCtx);
    if (QryTypeSupportTrans(type) && trxState == TRX_STATE_NOT_STARTED) {
        stmt->autoCommit = true;
        ret = QryExecuteBeginTrans(stmt);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }

    // 检查只读事务与DML操作是否冲突
    TrxCfgT trxCfg = {0};
    SeTransGetCfg(seRunCtx, &trxCfg);
    if (SECUREC_UNLIKELY(trxCfg.readOnly && QryTypeIsDML(type))) {
        DB_LOG_AND_SET_LASERR(GMERR_READ_ONLY_SQL_TRANSACTION, "YANG dml operation is not allowed for read-only trx.");
        return GMERR_READ_ONLY_SQL_TRANSACTION;
    }

    if (QryTypeIsDML(type)) {
        // 只有dml操作才能激活事务提交阶段的校验（避免显示开启可写事务，实际没做修改，读了很多表，最后提交产生冲突的问题）
        SeTransSetLabelModifiedActive(stmt->session->seInstance);
    }

    return GMERR_OK;
}

static Status YangServiceEndTrx(SessionT *session, Status ret)
{
    DB_POINTER(session);

    QryStmtT *stmt = session->currentStmt;
    if (SECUREC_LIKELY(stmt->autoCommit)) {
        if (SECUREC_LIKELY(ret == GMERR_OK && stmt->eof)) {
            ret = QryExecuteCommitTrans(stmt);
            stmt->autoCommit = false;
        }
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            (void)QryExecuteRollbackTrans(stmt);
            stmt->autoCommit = false;
        }
    } else {
        if (SECUREC_UNLIKELY((ret != GMERR_OK && ret != GMERR_REQUEST_TIME_OUT)) && QryTypeIsDML(stmt->context->type)) {
            // 校验超时不会设置abort，校验超时可重入
            SeTransAbort(session->seInstance);
        }
    }

    return ret;
}

static Status YangServiceValidatePrepare(SessionT *session, YangSrvEntryT *entry)
{
    Status ret = YangServiceEntryPrepare(session, entry);
    if (ret != GMERR_OK) {
        return ret;
    }
    YangPlanCacheT *planCache = YangPlanCachesGetCacheByNspId(session->namespaceId);
    if (planCache == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "get plan cache nspId=%" PRIu32 ".", session->namespaceId);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    return GMERR_OK;
}

static inline Status GetSrvEntry(
    YangSrvEntryT *entry, QryTypeE qryType, YangServiceFuncT yangServiceFunc, QryPrivCheckerT privChecker)
{
    entry->qryType = qryType;
    entry->yangServiceFunc.prepare = yangServiceFunc.prepare;
    entry->yangServiceFunc.parse = yangServiceFunc.parse;
    entry->yangServiceFunc.execute = yangServiceFunc.execute;
    entry->privChecker = privChecker;
    return GMERR_OK;
}

static Status YangServiceGetDDLEntry(uint32_t opCode, YangSrvEntryT *entry)
{
    switch (opCode) {
        case MSG_OP_RPC_YANG_VALIDATION_MODEL:
            return GetSrvEntry(entry, QRY_TYPE_YANG_VALIDATION_MODEL,
                (YangServiceFuncT){YangServiceEntryPrepare, NULL, YangServiceCreateXpathPlanEntry},
                (QryPrivCheckerT){.objType = CATA_NAMESPACE,
                    .objectPriv = DM_OBJ_CREATE_IN_NSP_PRIV,
                    .sysPriv = SELECTANY_PRIV,
                    .dbaPriv = false});
        case MSG_OP_RPC_IMPORT_DATA:
            return GetSrvEntry(entry, QRY_TYPE_IMPORT_DATA,
                (YangServiceFuncT){YangServiceEntryPrepare, YangServiceImportParser, YangServiceImportEntry},
                (QryPrivCheckerT){.objType = CATA_NAMESPACE,
                    .objectPriv = DM_OBJ_ALTER_IN_NSP_PRIV,
                    .sysPriv = DB_INVALID_ID32,
                    .dbaPriv = false});
        default:
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Un-supported YANG DDL type, opCode: %" PRIu32 ".", opCode);
            return GMERR_DATA_EXCEPTION;
    }
}

static Status YangServiceGetDMLEntry(uint32_t opCode, YangSrvEntryT *entry)
{
    switch (opCode) {
        case MSG_OP_RPC_INSERT_VERTEX:
            return GetSrvEntry(entry, QRY_TYPE_INSERT_VERTEX,
                (YangServiceFuncT){YangServiceEntryPrepare, YangServiceInsertParser, YangServiceInsertEntry},
                (QryPrivCheckerT){.objType = CATA_VERTEX_LABEL,
                    .objectPriv = DM_OBJ_INSERT_PRIV,
                    .sysPriv = INSERTANY_PRIV,
                    .dbaPriv = false});
        case MSG_OP_RPC_MERGE_VERTEX:
            return GetSrvEntry(entry, QRY_TYPE_MERGE_VERTEX,
                (YangServiceFuncT){YangServiceEntryPrepare, YangServiceMergeParser, YangServiceMergeEntry},
                (QryPrivCheckerT){.objType = CATA_VERTEX_LABEL,
                    .objectPriv = DM_OBJ_MERGE_PRIV,
                    .sysPriv = MERGEANY_PRIV,
                    .dbaPriv = false});
        case MSG_OP_RPC_REPLACE_GRAPH:
            return GetSrvEntry(entry, QRY_TYPE_REPLACE_GRAPH,
                (YangServiceFuncT){YangServiceEntryPrepare, YangServiceReplaceParser, YangServiceReplaceEntry},
                (QryPrivCheckerT){.objType = CATA_VERTEX_LABEL,
                    .objectPriv = DM_OBJ_REPLACE_PRIV,
                    .sysPriv = REPLACEANY_PRIV,
                    .dbaPriv = false});
        case MSG_OP_RPC_DELETE_GRAPH:
            return GetSrvEntry(entry, QRY_TYPE_DELETE_GRAPH,
                (YangServiceFuncT){YangServiceEntryPrepare, YangServiceDeleteParser, YangServiceDeleteEntry},
                (QryPrivCheckerT){.objType = CATA_VERTEX_LABEL,
                    .objectPriv = DM_OBJ_DELETE_PRIV,
                    .sysPriv = DELETEANY_PRIV,
                    .dbaPriv = false});
        case MSG_OP_RPC_REMOVE_GRAPH:
            return GetSrvEntry(entry, QRY_TYPE_REMOVE_GRAPH,
                (YangServiceFuncT){YangServiceEntryPrepare, YangServiceRemoveParser, YangServiceRemoveEntry},
                (QryPrivCheckerT){.objType = CATA_VERTEX_LABEL,
                    .objectPriv = DM_OBJ_DELETE_PRIV,
                    .sysPriv = DELETEANY_PRIV,
                    .dbaPriv = false});
        case MSG_OP_RPC_NONE_VERTEX:
            return GetSrvEntry(entry, QRY_TYPE_NONE_VERTEX,
                (YangServiceFuncT){YangServiceEntryPrepare, YangServiceNoneParser, YangServiceNoneEntry},
                (QryPrivCheckerT){.objType = CATA_VERTEX_LABEL,
                    .objectPriv = DM_OBJ_MERGE_PRIV,
                    .sysPriv = MERGEANY_PRIV,
                    .dbaPriv = false});
        case MSG_OP_RPC_YANG_VALIDATION:
            return GetSrvEntry(entry, QRY_TYPE_YANG_VALIDATION,
                (YangServiceFuncT){YangServiceValidatePrepare, YangServiceValidateParser, YangServiceValidateDataEntry},
                (QryPrivCheckerT){
                    .objType = CATA_NAMESPACE, .objectPriv = DM_OBJ_VALIDATION, .sysPriv = USE_PRIV, .dbaPriv = false});
        default:
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Un-supported YANG DML type, opCode: %" PRIu32 ".", opCode);
            return GMERR_DATA_EXCEPTION;
    }
}

static Status YangServiceGetDQLEntry(uint32_t opCode, YangSrvEntryT *entry)
{
    switch (opCode) {
        case MSG_OP_RPC_FETCH_DIFF:
            return GetSrvEntry(entry, QRY_TYPE_FETCH_DIFF,
                (YangServiceFuncT){YangServiceEntryPrepare, YangServiceDiffParser, YangServiceFetchDiffEntry},
                (QryPrivCheckerT){.objType = CATA_NAMESPACE,
                    .objectPriv = DM_OBJ_SELECT_PRIV,
                    .sysPriv = DB_INVALID_ID32,
                    .dbaPriv = false});
        case MSG_OP_RPC_YANG_GET_LEAFREF_PATH:
            return GetSrvEntry(entry, QRY_TYPE_GET_LEAFREF_PATH,
                (YangServiceFuncT){
                    YangServiceValidatePrepare, YangServiceGetLeafrefPathsParser, YangServiceReferencedEntry},
                (QryPrivCheckerT){.objType = CATA_NAMESPACE,
                    .objectPriv = DM_OBJ_SELECT_PRIV,
                    .sysPriv = DB_INVALID_ID32,
                    .dbaPriv = false});
        case MSG_OP_RPC_SUBTREE_FILTER:
            return GetSrvEntry(entry, QRY_TYPE_SUBTREE_FILTER,
                (YangServiceFuncT){YangServiceEntryPrepare, YangServiceSubtreeParser, YangServiceSubtreeEntry},
                (QryPrivCheckerT){.objType = CATA_NAMESPACE,
                    .objectPriv = DM_OBJ_SELECT_PRIV,
                    .sysPriv = DB_INVALID_ID32,
                    .dbaPriv = false});
        case MSG_OP_RPC_EXPORT_DATA:
            return GetSrvEntry(entry, QRY_TYPE_EXPORT_DATA,
                (YangServiceFuncT){YangServiceEntryPrepare, YangServiceExportParser, YangServiceExportEntry},
                (QryPrivCheckerT){.objType = CATA_NAMESPACE,
                    .objectPriv = DM_OBJ_SELECT_PRIV,
                    .sysPriv = DB_INVALID_ID32,
                    .dbaPriv = false});
        default:
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Un-supported YANG DQL type, opCode: %" PRIu32 ".", opCode);
            return GMERR_DATA_EXCEPTION;
    }
}

static Status YangServiceGetServiceEntry(uint32_t opCode, YangSrvEntryT *entry)
{
    DB_POINTER(entry);

    entry->opCode = opCode;
    if (YangOpCodeIsDDL(opCode)) {
        return YangServiceGetDDLEntry(opCode, entry);
    }

    if (YangOpCodeIsDML(opCode)) {
        return YangServiceGetDMLEntry(opCode, entry);
    }

    if (YangOpCodeIsDQL(opCode)) {
        return YangServiceGetDQLEntry(opCode, entry);
    }

    DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Un-supported YANG message type, opCode: %" PRIu32 ".", opCode);
    return GMERR_DATA_EXCEPTION;
}

static Status YangProcessCheckPriv(SessionT *session, YangSrvEntryT *srvEntry)
{
    if (session->isDBA) {
        return GMERR_OK;
    }

    int32_t userPolicyMode = DbCfgGetInt32Lite(DB_CFG_USER_POLICY_MODE, NULL);
    if (userPolicyMode == USER_POLICY_DISABLED) {
        return GMERR_OK;
    }

    CataRoleT *role = QryGetDbSessionRole(session->seInstance);
    if (role == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "yang privilege get role.");
        return GMERR_INTERNAL_ERROR;
    }
    session->roleId = role->metaCommon.metaId;

    // 校验 namespace 上的权限
    if (CataNspHasObjPriv(role, (uint16_t)srvEntry->privChecker.objectPriv, session->namespaceId)) {
        return GMERR_OK;
    }

    if (SECUREC_UNLIKELY(QryCheckPrivs(session->currentStmt, &srvEntry->privChecker)) == GMERR_OK) {
        return GMERR_OK;
    }

    QryExternalUserT *currentUser = &session->externalUser;
    if (userPolicyMode == USER_POLICY_ENFORCING) {
        DB_LOG_AND_SET_LASERR(GMERR_INSUFFICIENT_PRIVILEGE,
            "The current user (%s : %s : %s) for yang operation (%" PRIu32 ") lack permissions.",
            currentUser->dbUserName, currentUser->dbGroupName, currentUser->dbProcessName, srvEntry->opCode);
        return GMERR_INSUFFICIENT_PRIVILEGE;
    }

    // 宽容模式下无权限则打印 warn 日志
    DB_LOG_WARN(GMERR_INSUFFICIENT_PRIVILEGE, "user (%s : %s : %s) operation in tolerant when check yang privilege.",
        currentUser->dbUserName, currentUser->dbGroupName, currentUser->dbProcessName);
    return GMERR_OK;
}

static void YangUpdateStmtStatus(QryStmtT *stmt, DbStmtStatusE status)
{
    stmt->status = status;
}

static Status YangCheckContextReuse(QryStmtT *stmt, QryTypeE qryType, bool *isReuse)
{
    DB_POINTER(isReuse);
    *isReuse = false;
    if (stmt->status < STMT_STATUS_PREPARED || stmt->context == NULL || qryType != stmt->context->type ||
        !QryTypeIsDML(qryType)) {
        return GMERR_OK;
    }
    switch (qryType) {
        case QRY_TYPE_INSERT_VERTEX:
        case QRY_TYPE_REPLACE_GRAPH:
        case QRY_TYPE_MERGE_VERTEX:
            return QryIsReuseInsertVertexContext(stmt, isReuse);
        case QRY_TYPE_DELETE_GRAPH:
        case QRY_TYPE_REMOVE_GRAPH:
            return QryIsReuseDeleteVertexContext(stmt, isReuse);
        default:
            return GMERR_OK;
    }
}

static void YangSetContextCache(QryStmtT *stmt, YangContextFreeSlotT *qryContextFreeSlot)
{
    // 当 stmt->status < STMT_STATUS_PREPARED 的时候这个context才会不能复用。就是处于free和idle状态。
    // 如果发生了这种情况当前stmt上的context理论上应当被释放掉
    if (stmt->status >= STMT_STATUS_PREPARED) {
        return;
    }

    for (uint8_t i = 0; i < QRY_CONTEXT_MAX_CACHE_SLOT; i++) {
        // 如果缓存中有空闲的位置，记录一下
        if (stmt->contextCache[i] == NULL) {
            if (*(qryContextFreeSlot->freeSlotId) == QRY_CONTEXT_INVALID_CACHE_SLOT) {
                *(qryContextFreeSlot->freeSlotId) = i;
            }
            continue;
        }
        // 如果这个stmt当前状态异常了，那么其上次使用的context而且在缓存中，就从缓存中去除，之后由NEED_PREPARE释放
        if (stmt->context == stmt->contextCache[i]) {
            stmt->contextCache[i] = NULL;
            if (*(qryContextFreeSlot->freeSlotId) == QRY_CONTEXT_INVALID_CACHE_SLOT) {
                *(qryContextFreeSlot->freeSlotId) = i;
            }
            break;
        }
    }

    // 如果freeSlot为空说明没有可用的槽位了（槽位满了而且没有与stmt上的context匹配的缓存），需要释放一个，当前选取最后一个槽位
    if (*(qryContextFreeSlot->freeSlotId) == QRY_CONTEXT_INVALID_CACHE_SLOT) {
        // 先保存一下stmt上的context，然后释放缓存的context，在将stmt上的context恢复
        QryContextT *tmp = stmt->context;
        stmt->context = stmt->contextCache[QRY_CONTEXT_MAX_CACHE_SLOT - 1];
        QryReleaseCtx(stmt);  // 只是释放之前申请的context
        stmt->contextCache[QRY_CONTEXT_MAX_CACHE_SLOT - 1] = NULL;
        stmt->context = tmp;
        *(qryContextFreeSlot->freeSlotId) = QRY_CONTEXT_MAX_CACHE_SLOT - 1;
    }
    *(qryContextFreeSlot->needCached) = true;
}

Status YangFindContextCache(QryStmtT *stmt, QryTypeE qryType, QryContextT **availableCtx, bool *isReused)
{
    QryContextT *currentContex = stmt->context;
    FixBufferT *req = stmt->session->req;
    uint32_t oldSeekPos = FixBufGetSeekPos(req);
    for (uint8_t i = 0; i < QRY_CONTEXT_MAX_CACHE_SLOT; i++) {
        if (stmt->contextCache[i] == NULL) {
            continue;
        }
        if (stmt->contextCache[i]->type == qryType) {
            stmt->context = stmt->contextCache[i];
            Status ret = YangCheckContextReuse(stmt, qryType, isReused);
            if (ret != GMERR_OK) {
                return ret;
            }
            stmt->context = currentContex;
            if (!*isReused) {
                FixBufSeek(req, oldSeekPos);
                continue;
            } else {
                *availableCtx = stmt->contextCache[i];
                break;
            }
        }
    }
    return GMERR_OK;
}

Status YangGetContextCache(
    QryStmtT *stmt, QryTypeE qryType, YangContextFreeSlotT *qryContextFreeSlot, bool *isReused, bool *currentCtxIsFree)
{
    QryContextT *availableCtx = NULL;
    bool currentCtxisCached = false;
    // 判断当前的context有没有在缓存中，并记录一下freeslot
    uint8_t freeSlotId = QRY_CONTEXT_INVALID_CACHE_SLOT;
    QryContextT *contextCache = NULL;
    for (uint8_t i = 0; i < QRY_CONTEXT_MAX_CACHE_SLOT; i++) {
        contextCache = stmt->contextCache[i];
        if (stmt->context == contextCache) {
            currentCtxisCached = true;
        } else if (contextCache == NULL && (freeSlotId == QRY_CONTEXT_INVALID_CACHE_SLOT)) {
            freeSlotId = i;
        }
    }
    *(qryContextFreeSlot->freeSlotId) = freeSlotId;

    Status ret = YangFindContextCache(stmt, qryType, &availableCtx, isReused);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (!currentCtxisCached) {  // 如果当前context没有在缓存中，则重置stmt，并释放当前context
        QryReleaseCtxAndExecData(stmt);
        *currentCtxIsFree = true;
    } else {
        // 当前stmt上的context在缓存中，则重置stmt
        stmt->context = NULL;
        QryReleaseExecData(stmt);
    }

    if (availableCtx != NULL) {
        stmt->context = availableCtx;
        return GMERR_OK;
    }

    if (*(qryContextFreeSlot->freeSlotId) == QRY_CONTEXT_INVALID_CACHE_SLOT) {
        stmt->context = stmt->contextCache[QRY_CONTEXT_MAX_CACHE_SLOT - 1];
        QryReleaseCtx(stmt);  // 只是释放之前申请的context
        stmt->contextCache[QRY_CONTEXT_MAX_CACHE_SLOT - 1] = NULL;
        *(qryContextFreeSlot->freeSlotId) = QRY_CONTEXT_MAX_CACHE_SLOT - 1;
    }
    *(qryContextFreeSlot->needCached) = true;
    return GMERR_OK;
}

Status YangProcessPrepare(SessionT *session, DbMemCtxT *memCtx, YangSrvEntryT *srvEntry, void **execObj)
{
    QryClearInvalidCache(session);
    QryStmtT *stmt = session->currentStmt;
    FixBufferT *req = stmt->session->req;
    Status ret = GMERR_OK;
    bool isReused = false;
    uint32_t oldSeekPos = FixBufGetSeekPos(req);  // 判断复用前记录下当前的消息位置，如果不能复用需要恢复该位置
    if ((ret = YangCheckContextReuse(stmt, srvEntry->qryType, &isReused)) != GMERR_OK || isReused) {
        return ret;
    }
    FixBufSeek(req, oldSeekPos);  // 判断不可复用需要恢复消息位置
    // 如果当前使用的context是fastpath的缓存，则设置缓存保留不能被释放
    bool needCached = false;
    bool currentCtxIsFree = false;
    uint8_t freeSlotId = QRY_CONTEXT_INVALID_CACHE_SLOT;
    YangContextFreeSlotT qryContextFreeSlot = {.freeSlotId = &freeSlotId, .needCached = &needCached};
    // 当前只对以下操作进行缓存
    if (srvEntry->qryType == QRY_TYPE_INSERT_VERTEX || srvEntry->qryType == QRY_TYPE_MERGE_VERTEX) {
        // 当状态是Free以及Idle状态时需要判断是否缓存，以及缓存的槽位
        YangSetContextCache(stmt, &qryContextFreeSlot);
        if (!needCached) {
            // 如果判定未缓存情况下，需要从缓存池中获取，获取不到就找到空槽位插入缓存
            ret = YangGetContextCache(stmt, srvEntry->qryType, &qryContextFreeSlot, &isReused, &currentCtxIsFree);
            if (ret != GMERR_OK || isReused) {
                return ret;
            }
        }
    } else {
        // 如果为其他操作，需要判断当前qryContext是否在缓存中，如果在缓存中直接置为NULL，防止切换为其他操作过程中将缓存释放掉
        ResetCurrentContext(stmt);
    }

    if (!currentCtxIsFree) {
        QryReleaseCtxAndExecData(stmt);
    }
    if (srvEntry->yangServiceFunc.prepare != NULL) {
        ret = srvEntry->yangServiceFunc.prepare(session, srvEntry);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }

    if (srvEntry->yangServiceFunc.parse != NULL) {
        ret = srvEntry->yangServiceFunc.parse(session, memCtx, execObj);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    if (needCached) {
        // 此处满足 freeSlotId < QRY_CONTEXT_MAX_CACHE_SLOT
        stmt->contextCache[freeSlotId] = stmt->context;
    }
    return GMERR_OK;
}

Status YangProcessExecution(SessionT *session, DbMemCtxT *memCtx, YangSrvEntryT *srvEntry, void *execObj)
{
    QryReleaseExecData(session->currentStmt);
    Status ret = QryAllocCursor(session->currentStmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = YangServiceBeginTrx(session);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Yang process begin trans, qryType: %" PRIu32 ".", srvEntry->qryType);
        return ret;
    }
    ret = srvEntry->yangServiceFunc.execute(session, memCtx, execObj);
    if (SECUREC_UNLIKELY((ret = YangServiceEndTrx(session, ret)) != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Yang process end trans, qryType: %" PRIu32 ".", srvEntry->qryType);
        return ret;
    }
    return ret;
}

static Status YangProcessRequest(DrtProcCtxT *procCtx, DbMemCtxT *memCtx, uint32_t opCode)
{
    DB_POINTER2(procCtx, memCtx);
    YangSrvEntryT srvEntry = {0};
    Status ret = YangServiceGetServiceEntry(opCode, &srvEntry);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    SessionT *session = procCtx->conn->session;
    YangSetThreadNspId(session->namespaceId);

    session->currentStmt->lastStatTime = session->currentStmt->beginPrepareTime = DbRdtsc();
    session->currentStmt->totalTime = 0;
    void *execObj = NULL;
    ret = YangProcessPrepare(session, memCtx, &srvEntry, &execObj);
    FastpathStat4Prepare(session->currentStmt, ret);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Yang process prepare, opCode: %" PRIu32 ".", opCode);
        return ret;
    }

    YangUpdateStmtStatus(session->currentStmt, STMT_STATUS_PREPARED);

    ret = YangProcessCheckPriv(session, &srvEntry);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Yang process check priv, opCode: %" PRIu32 ".", opCode);
        return ret;
    }

    ret = YangProcessExecution(session, memCtx, &srvEntry, execObj);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Yang process execute, opCode: %" PRIu32 ".", opCode);
        return ret;
    }

    YangUpdateStmtStatus(session->currentStmt, STMT_STATUS_EXECUTED);
    FastpathStat4Execute(session->currentStmt, ret);
    YangServiceAuditLog(session->currentStmt, ret);
    SrvDataRecordLongStat(session->currentStmt, true);

    return ret;
}

static Status YangBatchProcessOneRequest(DrtProcCtxT *procCtx, DbMemCtxT *memCtx, FixBufferT *req, FixBufferT *rsp)
{
    uint32_t opCode = 0;
    Status ret = FixBufGetUint32(req, &opCode);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to get opCode when process YANG multi commands.");
        return ret;
    }

    // 校验操作类型
    if (opCode < MSG_OP_RPC_DML_BEGIN || opCode > MSG_OP_RPC_DML_END) {
        ret = GMERR_FEATURE_NOT_SUPPORTED;
        DB_LOG_AND_SET_LASERR(ret, "Currently, YANG batch only support DML operations, opCode: %" PRIu32 ".", opCode);
        return ret;
    }

    // 预留结果头
    uint32_t rspHeaderOffset;
    const uint32_t rspHeaderSize = (uint32_t)(sizeof(uint32_t) + sizeof(uint32_t));
    ret = FixBufReserveDataOffset(rsp, rspHeaderSize, &rspHeaderOffset);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to reserve data offset when process YANG multi commands.");
        return ret;
    }

    uint32_t before = FixBufGetPos(rsp);
    ret = YangProcessRequest(procCtx, memCtx, opCode);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        FixBufInitPut(rsp, rspHeaderOffset);
        return ret;
    }
    uint32_t after = FixBufGetPos(rsp);
    uint32_t *resultHeader = FixBufOffsetToAddr(rsp, rspHeaderOffset);
    resultHeader[0] = opCode;          // op code
    resultHeader[1] = after - before;  // data size

    return GMERR_OK;
}

static Status YangProcessBatchRequest(DrtProcCtxT *procCtx, DbMemCtxT *memCtx)
{
    DB_POINTER2(procCtx, memCtx);
    SessionT *session = procCtx->conn->session;
    procCtx->longOperationRecord.longestQryInfo.namespaceId = session->namespaceId;
    procCtx->longOperationRecord.isBatchOperation = true;

    FixBufferT *req = session->req;
    QryBatchSplitInfoT batchSplitInfo = {0};
    const BatchHeaderT *batchHeader = FixBufGetData(req, sizeof(BatchHeaderT));
    Status ret = PtlYangInitBatchRequest(batchHeader, session, &batchSplitInfo);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "unsucc to init yang batch request.");
        return ret;
    }

    FixBufferT *rsp = session->rsp;
    uint64_t startTime = DbClockGetTsc();
    do {
        ret = YangBatchProcessOneRequest(procCtx, memCtx, req, rsp);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            break;
        }

        batchSplitInfo.successCount += 1;
        batchSplitInfo.completedCount += 1;

        if (DbExceedTime(startTime, EXEC_SPLIT_TIME)) {
            (void)DrtKeepThisWorkerAlive(NULL);
            startTime = DbClockGetTsc();
        }

        if (SECUREC_UNLIKELY(PtlYangRequestTimeout(session))) {
            ret = GMERR_REQUEST_TIME_OUT;
            DB_LOG_AND_SET_LASERR(ret, "Exceed the request time when YANG batch operation.");
            break;
        }
    } while (batchSplitInfo.completedCount < batchSplitInfo.totalCount);

    PtlYangSetBatchResponse(session, &batchSplitInfo);
    QrySetSessionBatchExecutedFlag(session, true);
    return ret;
}

static Status DataServiceYangEntry(DrtServiceCtxT *serviceCtx, DrtProcCtxT *procCtx)
{
    DB_POINTER5(serviceCtx, procCtx, procCtx->conn, procCtx->conn->session, procCtx->conn->auditUserInfo);
    Status opStatus = GMERR_OK;
    SessionT *session = (SessionT *)procCtx->conn->session;
    Status ret = PtlYangInitSessionResource(procCtx, session);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "init YANG resource.");
        goto ERROR_0;
    }

    ret = PtlYangCheckRequest(procCtx, session);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Check YANG request.");
        goto ERROR_0;
    }

    char memCtxName[YANG_SERVICE_NAME_MAX] = {0};
    int32_t ret0 = snprintf_s(
        memCtxName, YANG_SERVICE_NAME_MAX, YANG_SERVICE_NAME_MAX - 1, "YangService_%" PRIu32 ".", procCtx->conn->id);
    if (SECUREC_UNLIKELY(ret0 < 0)) {
        ret = GMERR_FIELD_OVERFLOW;
        DB_LOG_ERROR(ret, "snprintf_s Yang Service Context Name, %d", procCtx->conn->id);
        goto ERROR_0;
    }

    DbMemCtxT *memCtx = NULL;
    ret = PtlYangAllocMemCtx(session->memCtx, memCtxName, &memCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "get YangDataService memCtx: %s", memCtxName);
        goto ERROR_0;
    }

    OpHeaderT *opHeader = ProtocolPeekFirstOpHeader(&procCtx->msg);
    if (opHeader->opCode == MSG_OP_RPC_BATCH) {
        ret = YangProcessBatchRequest(procCtx, memCtx);
    } else {
        ret = YangProcessRequest(procCtx, memCtx, opHeader->opCode);
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Process YANG request, opCode: %" PRIu32 ".", opHeader->opCode);
        goto ERROR_1;
    }

ERROR_1:
    PtlYangDeleteMemCtx(memCtx);
    memCtx = NULL;
ERROR_0:
    opStatus = SrvDataSetLastErr(ret, session);
    if (SECUREC_UNLIKELY(opStatus != GMERR_OK)) {
        return opStatus;
    }
    (void)SrvDataSendResponse(session, ret, 0);
    return ret;
}

void YangServiceUnInit(void)
{
    QryFreeYangRecycleTaskMgr();
    YangDataServiceUnInit();
}

Status RebuildPlanCacheFuncImpl(uint32_t nspId)
{
    SessionT *session = NULL;
    QryStmtT *rebuildPlanCacheStmt = NULL;
    Status ret = QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, NULL, &session);
    if (ret != GMERR_OK) {
        return ret;
    }
    session->namespaceId = nspId;
    ret = ExecutorInitStmt(session, &rebuildPlanCacheStmt);
    if (ret != GMERR_OK) {
        QrySessionRelease(session);
        return ret;
    }
    session->currentStmt = rebuildPlanCacheStmt;
    char *memCtxName = "YangServiceRebuildYangPlanCache";
    DbMemCtxT *memCtx = NULL;
    ret = PtlYangAllocMemCtx(session->memCtx, memCtxName, &memCtx);
    if (ret != GMERR_OK) {
        QryReleaseStmt(rebuildPlanCacheStmt);
        QrySessionRelease(session);
        return ret;
    }
    YangSetThreadNspId(nspId);
    YangSrvEntryT srvEntry = {0};
    ret = YangServiceGetServiceEntry(MSG_OP_RPC_YANG_VALIDATION_MODEL, &srvEntry);
    if (ret != GMERR_OK) {
        PtlYangDeleteMemCtx(memCtx);
        QryReleaseStmt(rebuildPlanCacheStmt);
        QrySessionRelease(session);
        return ret;
    }
    ret = YangServiceEntryPrepare(session, &srvEntry);
    if (ret != GMERR_OK) {
        PtlYangDeleteMemCtx(memCtx);
        QryReleaseStmt(rebuildPlanCacheStmt);
        QrySessionRelease(session);
        return ret;
    }
    ret = YangServiceCreateXpathPlanEntry(session, memCtx, NULL);
    if (ret != GMERR_OK) {
        PtlYangDeleteMemCtx(memCtx);
        QryReleaseStmt(rebuildPlanCacheStmt);
        QrySessionRelease(session);
        return ret;
    }
    PtlYangDeleteMemCtx(memCtx);
    QryReleaseStmt(rebuildPlanCacheStmt);
    QrySessionRelease(session);
    return GMERR_OK;
}

Status ValidateDataFuncImpl(SessionT *session)
{
    SessionT *currSession = session;
    YangPlanCacheT *planCache = YangPlanCachesGetCacheByNspId(currSession->namespaceId);
    // 不存在校验规则情况下，不进行校验就返回
    if (planCache == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
            "Cannot get plan cache by Nsp Id, nspId=%" PRIu32 ", no need validate.", currSession->namespaceId);
        return GMERR_OK;
    }
    YangSetThreadNspId(currSession->namespaceId);
    DbMemCtxT *memCtx = NULL;
    const char *memCtxName = "YangServiceValidate";
    Status ret = PtlYangAllocMemCtx(currSession->memCtx, memCtxName, &memCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to get YangDataService memCtx: %s", memCtxName);
        goto END;
    }
    size_t size = sizeof(VerifyConfigT);
    VerifyConfigT *cfg = (VerifyConfigT *)DbDynMemCtxAlloc(memCtx, size);
    if (cfg == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc mem for validate parser.");
        goto END;
    }
    (void)memset_s(cfg, size, 0x00, size);
    cfg->verifyType = YANG_VERIFY_ALL_FORCE;
    ret = YangServiceValidateDataEntry(currSession, memCtx, cfg);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to validate data.");
        goto END;
    }
END:
    PtlYangDeleteMemCtx(memCtx);
    memCtx = NULL;
    return ret;
}

void YangServiceFuncInit(void)
{
    YangServiceInterfaceFuncT yangServiceFunc = {
        .rebuildPlanCacheFunc = RebuildPlanCacheFuncImpl,
        .recoverUpgradeLabelsFunc = RecoverUpgradeLabelsFuncImpl,
        .recoverUpdateLeafListLabelsFunc = RecoverUpdateLeafListLabelsFuncImpl,
    };
    SetYangServiceFunc(&yangServiceFunc);
}

Status YangServiceInit(void)
{
    Status ret = YangDataServiceInit();
    if (ret != GMERR_OK) {
        return ret;
    }
    YangServiceFuncInit();
    ret = ModelServiceRegister(DataServiceMgrGet(), DataServiceYangEntry, MODEL_YANG);
    if (ret != GMERR_OK) {
        YangDataServiceUnInit();
        return ret;
    }
    ret = InitYangPlanCacheImpl();
    if (ret != GMERR_OK) {
        YangDataServiceUnInit();
        return ret;
    }
    return GMERR_OK;
}

Status ImportPlanCacheFuncImpl(SessionT *session, uint8_t *buf, uint32_t bufLen)
{
    return YangDeseriPlanCache(session->namespaceId, buf, bufLen);
}

Status ExportPlanCacheFuncImpl(SessionT *session, DbMemCtxT *memCtx, uint8_t **buf, uint32_t *len)
{
    return YangSeriPlanCache(session->namespaceId, memCtx, buf, len);
}

bool FetchPlanCacheFuncImpl(SessionT *session)
{
    return YangPlanCachesGetCacheByNspId(session->namespaceId) == NULL ? false : true;
}

void QryYangRLockPlanCacheImpl(void)
{
    YangRLockPlanCache(YangGetThreadNspId());
}

void QryYangRUnlockPlanCacheImpl(void)
{
    YangRUnlockPlanCache(YangGetThreadNspId());
}

void QryYangSetThreadNspIdImpl(uint32_t nspId)
{
    YangSetThreadNspId(nspId);
}

#if !defined(RTOSV2X) && !defined(RTOSV2)
void RegYangServiceFunctions(ImportYangFunctionsT *yangFunctions)
{
    yangFunctions->qryYangServiceImportPlanCache = ImportPlanCacheFuncImpl;
    yangFunctions->qryYangServiceExportPlanCache = ExportPlanCacheFuncImpl;
    yangFunctions->qryYangServiceFetchPlanCache = FetchPlanCacheFuncImpl;
    yangFunctions->qryYangServiceRLockPlanCache = QryYangRLockPlanCacheImpl;
    yangFunctions->qryYangServiceRUnlockPlanCache = QryYangRUnlockPlanCacheImpl;
    yangFunctions->qryYangServiceSetThreadNspId = QryYangSetThreadNspIdImpl;
}
void RegYangValidateDataFunc(ImportYangFunctionsT *yangFunctions)
{
    yangFunctions->qryYangValidateDataFunc = ValidateDataFuncImpl;
}
#endif
#ifdef __cplusplus
}
#endif /* __cplusplus */
