/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: subtree service
 * Note1: memCtx is used in short process period , we will free all memory after finishing one request
 * Author: guozhiwu
 * Create: 2025-01-22
 */

#include "ee_session_interface.h"
#include "ee_subtree.h"
#include "include/ptl_yang_service_subtree.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

Status YangServiceSubtreeEntry(SessionT *session, DbMemCtxT *memCtx, void *execObj)
{
    DB_UNUSED2(execObj, memCtx);

    YANG_TRACE_MASK_LOG(YANG_TRACE_SUBTREE, "(INFO)YangServiceSubtreeEntry");
    return QryExecuteSubtreeFilter(session->currentStmt);
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
