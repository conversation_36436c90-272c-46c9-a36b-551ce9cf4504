/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author:
 * Create: 2023-06-13
 */
#include "srv_data_base_proc.h"
#include "ee_context.h"
#include "ptl_service_utils.h"
#include "ee_error_path_base.h"
#include "ee_dml_desc.h"

void SrvDataInitResponse(const SessionT *session, FixBufferT *response, uint32_t msgHdrFlag)
{
    DB_POINTER2(session, response);
    // 重调度的任务
    if (SECUREC_UNLIKELY((msgHdrFlag & CS_FLAG_SELF_SCHEDULE) != 0)) {
        return;
    }
    DrtAllocResponse(QrySessionGetConn(session), session->req, response);
}

Status SrvDataSendResponse(SessionT *session, int32_t opStatus, uint32_t opCode)
{
    DB_POINTER(session);
#ifdef FEATURE_GQL
    // session->isSelfSchedule 表明是当前任务处于自调度状态，不返回response
    if (SECUREC_UNLIKELY(
            (session->currentStmt != NULL && !session->currentStmt->splitInfo.isLast) || session->isSelfSchedule)) {
#else
    if (SECUREC_UNLIKELY(session->currentStmt != NULL && !session->currentStmt->splitInfo.isLast)) {
#endif
        return GMERR_OK;
    }
    DrtConnectionT *conn = QrySessionGetConn(session);
    DrtSetUserOverLoadLevel(conn, QrySessionGetSubsOverLoad(session));
    return PtlServiceSendRsp(session, opStatus);
}

Status SrvDataSetLastErr(Status opStatus, SessionT *session)
{
    // 操作失败，设置LastError到应答中
    // 第二个判空意思是如果没有必要应答，比如单纯的自调度消息，就不写LastError
    if (SECUREC_UNLIKELY(opStatus != GMERR_OK && session->rsp->buf != NULL)) {
        Status ret = PtlServiceSetLastError(session->rsp);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            (void)PtlServiceSendRsp(session, opStatus);
            return ret;
        }
        // errorPath生成
        QrySetErrorPathToRsp(session->currentStmt);
    } else if (SECUREC_UNLIKELY(session->errorPath != NULL)) {
        FreeErrorPath(session);
    }
    return GMERR_OK;
}

void SrvDataRecordLongStat(QryStmtT *stmt, bool needCount)
{
    if (stmt->session->sesType == SESSION_TYPE_NO_CONN) {
        return;
    }

    DrtInstanceT *drtInstance = stmt->session->drtInst;
    if (SECUREC_UNLIKELY(drtInstance == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Get DRT instance unsucc when record long statis.");
        return;
    }

    // 不用统计
    if (SmNotLongOpStat()) {
        return;
    }
    uint64_t endTime = DbRdtsc();
    // 不分片，stmt->totalTime为0, 分片, stmt->totalTime为累计值
    if (stmt->beginPrepareTime != 0) {
        stmt->totalTime = endTime - stmt->beginPrepareTime;
    } else {
        stmt->totalTime += endTime - stmt->beginExecuteTime;
    }
    uint32_t opCode;
    // 报错退出或者执行结束需要统计
    if (needCount) {
        opCode = (uint32_t)stmt->context->opCode;
        uint32_t totalTime = (uint32_t)DbToUseconds(stmt->totalTime);
        SmUpdateLongOpExecuteStat(&drtInstance->scheMgr, totalTime, opCode);
    }
}

static Status SetCheckVersionLabelAndIndex(const void *entry, const char **name, DmVlIndexLabelT **indexLabel)
{
    const QryUpdateCheckVersionDescT *desc = (const QryUpdateCheckVersionDescT *)entry;
    *name = desc->query->qryLabel->def.vertexLabel->metaCommon.metaName;
    uint32_t indexId = desc->query->indexKey->leftValue.value->indexId;
    return DmGetIndexLabelByIndex(desc->query->qryLabel->def.vertexLabel, indexId, indexLabel);
}

static Status GetLogLabelAndIndexNameImplNonCommonUsedScene(
    QryTypeE type, const void *entry, const char **name, DmVlIndexLabelT **indexLabel)
{
    Status ret = GMERR_OK;
    switch (type) {
        case QRY_TYPE_UPDATE_CHECK_VERSION: {
            ret = SetCheckVersionLabelAndIndex(entry, name, indexLabel);
            break;
        }
        case QRY_TYPE_CHECK_REPLACE: {
            const QryCheckReplaceDescT *desc = (const QryCheckReplaceDescT *)entry;
            *name = desc->qryLabel->def.vertexLabel->metaCommon.metaName;
            break;
        }
        case QRY_TYPE_DELETE_GRAPH:
        case QRY_TYPE_REMOVE_GRAPH:
            *name = ((const QryDeleteVertexDescT *)entry)->query->qryLabel->def.vertexLabel->metaCommon.metaName;
            *indexLabel = ((const QryDeleteVertexDescT *)entry)->query->indexLabel;
            break;
        case QRY_TYPE_REPLACE_GRAPH:
            *name = ((const QryMergRepVertexDescT *)entry)->qryLabel->def.vertexLabel->metaCommon.metaName;
            break;
        case QRY_TYPE_NONE_VERTEX:
            *name = ((const QryNoneVertexDescT *)entry)->qryLabel->def.vertexLabel->metaCommon.metaName;
            *indexLabel = ((const QryNoneVertexDescT *)entry)->indexLabel;
            break;
        case QRY_TYPE_SET_KV:
            *name = ((const QrySetKvDescT *)entry)->label->def.kvLabel->metaCommon.metaName;
            break;
        case QRY_TYPE_DELETE_KV:
            *name = ((const QryDeleteKvDescT *)entry)->label->def.kvLabel->metaCommon.metaName;
            break;
        case QRY_TYPE_INSERT_EDGE:
        case QRY_TYPE_DELETE_EDGE:
            *name =
                ((const QryInsertEdgeDescT *)entry)->query->srcVertex.qryLabel->def.vertexLabel->metaCommon.metaName;
            break;
        case QRY_TYPE_YANG_VALIDATION:
            *name = NULL;
            *indexLabel = NULL;
            break;
        default:
            return GMERR_DATATYPE_MISMATCH;
    }
    return ret;
}

static Status GetLogLabelAndIndexNameImpl(
    QryTypeE type, const void *entry, const char **name, DmVlIndexLabelT **indexLabel)
{
    switch (type) {
#ifdef FEATURE_VLIVF
        case QRY_TYPE_LOAD_INDEX:
#endif
        case QRY_TYPE_INSERT_VERTEX:
            *name = ((const QryInsertVertexDescT *)entry)->qryLabel->def.vertexLabel->metaCommon.metaName;
            break;
        case QRY_TYPE_UPDATE_VERTEX:
            *name = ((const QryUpdateVertexDescT *)entry)->query->qryLabel->def.vertexLabel->metaCommon.metaName;
            *indexLabel = ((const QryUpdateVertexDescT *)entry)->query->indexLabel;
            break;
        case QRY_TYPE_DELETE_VERTEX:
            *name = ((const QryDeleteVertexDescT *)entry)->query->qryLabel->def.vertexLabel->metaCommon.metaName;
            *indexLabel = ((const QryDeleteVertexDescT *)entry)->query->indexLabel;
            break;
        case QRY_TYPE_REPLACE_VERTEX:
        case QRY_TYPE_MERGE_VERTEX:
            *name = ((const QryMergRepVertexDescT *)entry)->qryLabel->def.vertexLabel->metaCommon.metaName;
            break;
        default:
            return GetLogLabelAndIndexNameImplNonCommonUsedScene(type, entry, name, indexLabel);
    }
    return GMERR_OK;
}

void FastpathGetLogLabelAndIndexName(QryStmtT *stmt, const char **labelName, const char **indexName)
{
    DB_POINTER5(stmt, labelName, indexName, stmt->context, stmt->context->entry);
    DmVlIndexLabelT *indexLabel = NULL;
    Status ret = GetLogLabelAndIndexNameImpl(stmt->context->type, stmt->context->entry, labelName, &indexLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "Inv qry type when get log label and index name.");
        *labelName = "";
        *indexName = "";
        return;
    }

    if (indexLabel != NULL) {
        *indexName = indexLabel->indexName;
    } else {
        *indexName = "";
    }
}
void SrvDataRecordLongLog(QryStmtT *stmt, uint32_t opCode, LongOperationInfoT *longInfo)
{
    longInfo->operationCount++;
    uint64_t endTime = DbRdtsc();
    if (opCode != MSG_OP_RPC_EXEC) {
        longInfo->totalPrepareUsedTime += stmt->beginExecuteTime - stmt->beginPrepareTime;
    }
    longInfo->totalExecuteUsedTime += endTime - stmt->beginExecuteTime;
    if (endTime - stmt->beginPrepareTime <=
        longInfo->longestQryInfo.executeEndTime - longInfo->longestQryInfo.prepareStartTime) {
        return;
    }
    if (opCode != MSG_OP_RPC_EXEC) {
        longInfo->longestQryInfo.opCode = opCode;
        longInfo->longestQryInfo.prepareStartTime = stmt->beginPrepareTime;
    } else {
        longInfo->longestQryInfo.opCode =
            ((uint32_t)stmt->context->type - (uint32_t)QRY_TYPE_DML_BEG) + (uint32_t)MSG_OP_RPC_DML_BEGIN;
        longInfo->longestQryInfo.prepareStartTime = stmt->beginExecuteTime;
    }
    longInfo->longestQryInfo.executeStartTime = stmt->beginExecuteTime;
    longInfo->longestQryInfo.executeEndTime = endTime;
    FastpathGetLogLabelAndIndexName(stmt, &longInfo->longestQryInfo.tableName, &longInfo->longestQryInfo.indexName);
}
