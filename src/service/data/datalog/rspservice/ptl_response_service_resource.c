/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: resource response service impl
 * Author: GMDBV5 EE Team
 * Create: 2023-04-04
 */

#include "ee_delta_table_info_access_method.h"
#include "ptl_response_service.h"
#include "cpl_dtl_online_compiler.h"
#include "drt_data_send_channel.h"
#include "db_sysapp_context.h"

inline static void ResourceProcessSetDtlCtxOptions(DtlSessCtxT *ctx)
{
    DtlSessSetLabelIdAppend(ctx, false);
    DtlSessSetTableTypeVerify(ctx, false);
    DtlSessSetUpgradeVersionFill(ctx, false);
}

Status ResourceProcessResponse(
    DrtServiceCtxT *serviceCtx, DrtProcCtxT *procCtx, SyncInvokeStubT *stub, DrtDataSendChannelT *channel)
{
    DB_POINTER4(serviceCtx, procCtx, channel, stub);

    Status ret = GMERR_OK;
    FixBufferT *req = &procCtx->msg;
    SessionT *session = (SessionT *)procCtx->conn->session;
    QrySessionSetReq(session, req);
    OpHeaderT *opHeader = ProtocolPeekFirstOpHeader(req);
    session->opCode = opHeader->opCode;
    RpcSeekFirstOpMsg(req);
    ret = NewDtlSess(stub->memCtx, QrySessionGetServerTimeout(session), &session->datalogCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "new DtlSessCtx when resource process;receivedSerialNumber=%" PRIu32, stub->serialNumber);
        goto FINISH;
    }
    DtlSessCtxT *dtlCtx = session->datalogCtx;
    ResourceProcessSetDtlCtxOptions(dtlCtx);
    DtlDmlInfoT dmlInfo = {0};
    dmlInfo.dataMemCtx = stub->memCtx;
    DtlDmlResInfoT dmlResp = {.soId = INVALID_SO_ID};
    // parse ReqdataT list from request
    if (SECUREC_UNLIKELY((ret = DtlDmlParser(session, &dmlInfo, &dmlResp)) != GMERR_OK)) {
        DB_LOG_ERROR(
            ret, "parse ReqDataT list when resource process;receivedSerialNumber=%" PRIu32, stub->serialNumber);
        goto FINISH;
    }

    // get errcode from pubsub callback response buf
    int64_t errCode = DB_MAX_INT64;
    if (SECUREC_UNLIKELY(ret = FixBufGetInt64(req, &errCode)) != GMERR_OK) {
        DB_LOG_ERROR(ret,
            "get erCode when parse datalog pubsub callback response.Buf:(%" PRIu32 ", %" PRIu32 ", %" PRIu32 ")",
            FixBufGetTotalLength(req), FixBufGetPos(req), FixBufGetSeekPos(req));
        goto FINISH;
    }

    // construct result
    ReturnedResultT result = {0};
    result.opCode = MSG_OP_RPC_DATALOG_SUB_BATCH_RESPONSE;
    result.receivedSerialNumber = stub->serialNumber;
    result.errCode = errCode;

    // insertTupleDatas is not empty absolutely
    ReqDataT *firstTuple = (ReqDataT *)DbListItem(&dmlInfo.insertVertexList, 0);
    result.data.resourceSet.vertexLabelId = firstTuple->labelId;
    result.data.resourceSet.insertVertexList = dmlInfo.insertVertexList;

    ret = ResourceSyncCopyResult(stub, &result);
FINISH:

    if (session->datalogCtx != NULL) {
        DestroyDtlSess(stub->memCtx, session->datalogCtx);
        session->datalogCtx = NULL;
    }
    return ret;
}
