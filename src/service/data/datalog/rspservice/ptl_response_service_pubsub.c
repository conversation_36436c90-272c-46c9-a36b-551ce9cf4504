/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: pubsub response service impl
 * Author: GMDBV5 EE Team
 * Create: 2023-04-04
 */

#include "cpl_dtl_online_compiler.h"
#include "ptl_response_service.h"
#include "drt_data_send_channel.h"
#include "db_last_error.h"

Status PubsubProcessResponse(
    DrtServiceCtxT *serviceCtx, DrtProcCtxT *procCtx, SyncInvokeStubT *stub, DrtDataSendChannelT *channel)
{
    DB_POINTER4(serviceCtx, procCtx, stub, channel);

    // extract returned result
    FixBufferT *req = &procCtx->msg;
    RpcSeekFirstOpMsg(req);
    FailedIndexSetT *failedIndexes = (FailedIndexSetT *)FixBufGetData(req, sizeof(FailedIndexSetT));
    if (failedIndexes == NULL) {
        DB_LOG_WARN(GMERR_DATA_EXCEPTION,
            "receive wrong failedIndexes when pubsubProcessResponse;receivedSerialNumber=%" PRIu32, stub->serialNumber);
        return GMERR_DATA_EXCEPTION;
    }

    // get errcode from pubsub callback response buf
    int64_t errCode = DB_MAX_INT64;
    Status ret = FixBufGetInt64(req, &errCode);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret,
            "get erCode when parse datalog pubsub callback response when pubsub process.Buf:(%" PRIu32 ", %" PRIu32
            ", %" PRIu32 ")",
            FixBufGetTotalLength(req), FixBufGetPos(req), FixBufGetSeekPos(req));
        return ret;
    }

    // construct result
    ReturnedResultT result = {0};
    result.opCode = MSG_OP_RPC_DATALOG_SUB_RESPONSE;
    result.receivedSerialNumber = stub->serialNumber;
    result.data.failedIndexes = *failedIndexes;
    result.errCode = errCode;

    return PubsubSyncCopyResult(stub, &result);
}
