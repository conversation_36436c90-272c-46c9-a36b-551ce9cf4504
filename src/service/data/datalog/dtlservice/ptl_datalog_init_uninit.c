/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: ptl_datalog_init_uninit.c
 * Description:
 * Author:
 * Create: 2023-12-19
 */

#include "ptl_datalog_init_uninit.h"
#include "ptl_datalog_utils.h"
#include "ptl_datalog_privs_validator.h"
#include "ptl_datalog_table_locker.h"
#include "ee_udf_reader.h"

#ifdef __cplusplus
extern "C" {
#endif

static Status CheckKvPrivsForInitAndUninit(SessionT *session, DbMemCtxT *memCtx, DmUdfBaseT *udf)
{
    DB_POINTER3(session, memCtx, udf);

    int32_t userPolicyMode = USER_POLICY_ENFORCING;
    bool isCheckPrivs = NeedCheckPrivs(session, &userPolicyMode);
    if (!isCheckPrivs) {
        return GMERR_OK;
    }

    CataRoleT *role = NULL;
    Status ret = GetRoleBySession(session, &role);
    if (ret != GMERR_OK) {
        return ret;
    }

    DbListT kvTbls = (DbListT){0};
    DbCreateList(&kvTbls, sizeof(char *), memCtx);
    ret = DbAppendListItem(&kvTbls, &udf->accessKvName);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }

    ret = CheckDmlPrivsForkvLabels(role, &kvTbls, &session->externalUser, userPolicyMode, session->namespaceId);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }

RELEASE:
    DbDestroyList(&kvTbls);
    ReleaseRoleBySession(session, role);
    return ret;
}

static Status CheckPrivAndLockForInitAndUninit(SessionT *session, DmUdfBaseT *udf, DbMemCtxT *memCtx)
{
    Status ret = GMERR_OK;
    ret = PtlCheckUdfInvokePriv(session, udf);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (udf->accessKvName == NULL) {
        return GMERR_OK;
    }

    ret = CheckKvPrivsForInitAndUninit(session, memCtx, udf);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = LockSingleKvTableByName(session, memCtx, udf->accessKvName, SE_LOCK_MODE_S);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

/*
 *  Notice:if udf access kv,kv ref count will increase in this Func.
 *          The caller should be responsible for decrementing the ref count in the exception branch
 */
static Status BeforeCall(SessionT *session, DbMemCtxT *memCtx, DmUdfBaseT *udf, GmUdfCtxT **udfCtx)
{
    GmUdfCtxT *udfCtxTemp = NULL;
    Status ret = CheckPrivAndLockForInitAndUninit(session, udf, memCtx);
    if (ret != GMERR_OK) {
        return ret;
    }

    EStateT eState = (EStateT){0};
    eState.memCtx = memCtx;
    eState.seInstance = session->seInstance;
    eState.session = session;
    ret = NewGmUdfCtx(&eState, udf, &udfCtxTemp);
    if (ret != GMERR_OK) {
        return ret;
    }
    *udfCtx = udfCtxTemp;
    return GMERR_OK;
}

Status DtlInit(DtlInitT dtlInit, GmUdfCtxT *udfCtx)
{
    SetGmUdfStartTime(udfCtx);
    Status invokeResult = dtlInit(udfCtx);
    CheckGmUdfTimeout(udfCtx);
    if (invokeResult != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
            "init function executes unsucc. invokeResult = %" PRId32, invokeResult);
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }
    return GMERR_OK;
}

Status CallInit(LoadCtxT *loadCtx, SessionT *session, RollbackLoadCtxT *rollbackCtx)
{
    DtlInitT dtlInit = loadCtx->initFunc;
    if (dtlInit == NULL || loadCtx->loadParam->isDistribute) {
        return GMERR_OK;
    }
    DbMemCtxT *memCtx = loadCtx->memCtx;
    Status ret = GMERR_OK;
    GmUdfCtxT *udfCtx = NULL;
    DmUdfBaseT *udf = NULL;
    CataKeyT cataKey = {0};
    CataSetKeyForLabel(&cataKey, session->dbId, loadCtx->namespaceId, DTL_INIT_FUNC);
    ret = CataGetUdfByName(&cataKey, &udf, DbGetInstanceByMemCtx(session->memCtx));
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = BeforeCall(session, memCtx, udf, &udfCtx);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = DtlInit(dtlInit, udfCtx);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to execute init function.soId = %" PRId32, loadCtx->soId);
        goto EXIT;
    }
    rollbackCtx->inited = true;

EXIT:
    DtlKeepAlive();
    if (udfCtx != NULL) {
        // decrease kv ref count
        DestroyGmUdfCtx(udfCtx);
    }
    if (udf != NULL) {
        (void)CataReleaseUdf(udf);
    }
    return ret;
}

Status DtlUninit(DtlUninitT dtlUninit, GmUdfCtxT *udfCtx)
{
    SetGmUdfStartTime(udfCtx);
    Status invokeResult = dtlUninit(udfCtx);
    CheckGmUdfTimeout(udfCtx);
    if (invokeResult != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
            "Unable to uninit function executes. invokeResult = %" PRId32, invokeResult);
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }
    return GMERR_OK;
}

Status CallUninit(DtlUninitT dtlUninit, SessionT *session, DbMemCtxT *memCtx, CallUninitParamT *param)
{
    // 测试用例卸载设备上的ylog.so时，这里如果调用uninit会触发HPF的退出机制，HPF设计的时候认为public下的ylog.so是不能卸载的，
    // 这会导致设备复位，因此针对测试场景做特殊处理，不调用uninit。正常业务场景卸载一定会调用uninit。
    if (param->ignorePubsub) {
        return GMERR_OK;
    }
    Status ret = GMERR_OK;
    if (dtlUninit == NULL || param->isDistribute) {
        return ret;
    }
    GmUdfCtxT *udfCtx = NULL;
    DmUdfBaseT *udf = NULL;
    CataKeyT cataKey = {0};
    CataSetKeyForLabel(&cataKey, session->dbId, param->nspId, DTL_UNINIT_FUNC);
    ret = CataGetUdfByName(&cataKey, &udf, DbGetInstanceByMemCtx(session->memCtx));
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = BeforeCall(session, memCtx, udf, &udfCtx);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = DtlUninit(dtlUninit, udfCtx);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to execute uninit function. soId = %" PRIu32 ", nspId=%" PRIu32 ".",
            param->soId, param->nspId);
        goto EXIT;
    }

EXIT:
    DtlKeepAlive();
    if (udfCtx != NULL) {
        // decrease kv ref count
        DestroyGmUdfCtx(udfCtx);
    }
    if (udf != NULL) {
        (void)CataReleaseUdf(udf);
    }
    return ret;
}

#ifdef __cplusplus
}
#endif
