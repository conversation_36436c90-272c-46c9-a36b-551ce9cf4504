/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: load datalog hot patch program utils
 * Author: jianshuqiang
 * Create: 2025-01-02
 */

#include "ptl_datalog_hot_patch_template.h"
#include "ptl_datalog_service.h"
#include "ptl_datalog_service_common.h"
#include "ptl_datalog_privs_validator.h"
#include "ptl_datalog_table_locker.h"
#include "ptl_datalog_info_getter.h"
#include "ptl_data_service.h"
#include "ptl_datalog_utils.h"
#include "ptl_datalog_mem_estimate.h"
#include "ptl_handler.h"
#include "adpt_function_loader.h"
#include "cpl_dtl_serializer.h"
#include "cpl_opt_plancache.h"
#include "ee_plan_serializer.h"
#include "ee_plan_stmt.h"
#include "ee_upgrade_observer_template.h"
#include "ee_upgrade_subject_itf.h"
#include "ee_upgrade_scan_task_ctx.h"
#include "ee_upgrade_rollback.h"
#include "ptl_datalog_hot_patch_degrader.h"
#include "ptl_datalog_hot_patch_upgrader.h"
#include "db_config.h"
#include "dm_meta_basic_in.h"

Status ExecuteUtilityStmt(SessionT *session, DbMemCtxT *memCtx, NodeT *stmtNode)
{
    PlannedStmtT *plannedStmt = NULL;
    Status ret = NewUtilityStmt(memCtx, stmtNode, &plannedStmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    NodeT *resultStmt = NULL;
    return PtlDatalogExecEntry(session, plannedStmt, &resultStmt);
}

Status GetAlterByteCode(SessionT *session, UpgradeCtxT *upgradeCtx, LibFuncT getByteCode)
{
    upgradeCtx->alterPlanByteCode.byteCodeBuf = getByteCode(&upgradeCtx->alterPlanByteCode.bufLen, ALTER_PLAN_BUF_ID);
    if (upgradeCtx->alterPlanByteCode.byteCodeBuf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get alter plan byte code when load datalog %s.",
            upgradeCtx->baseCtx.loadParam->soName.str);
        return GMERR_DATA_EXCEPTION;
    }
    upgradeCtx->alterTableByteCode.byteCodeBuf =
        getByteCode(&upgradeCtx->alterTableByteCode.bufLen, ALTER_TABLE_BUF_ID);
    if (upgradeCtx->alterTableByteCode.byteCodeBuf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get alter table byte code when load datalog %s.",
            upgradeCtx->baseCtx.loadParam->soName.str);
        return GMERR_DATA_EXCEPTION;
    }
    upgradeCtx->alterUdfByteCode.byteCodeBuf = getByteCode(&upgradeCtx->alterUdfByteCode.bufLen, ALTER_UDF_BUF_ID);
    if (upgradeCtx->alterUdfByteCode.byteCodeBuf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get alter udf byte code when load datalog %s.",
            upgradeCtx->baseCtx.loadParam->soName.str);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

Status GetDeleteByteCode(SessionT *session, UpgradeCtxT *upgradeCtx, LibFuncT getByteCode)
{
    upgradeCtx->dropPlanByteCode.byteCodeBuf = getByteCode(&upgradeCtx->dropPlanByteCode.bufLen, DROP_PLAN_ID);
    if (upgradeCtx->dropPlanByteCode.byteCodeBuf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get drop plan byte code when load datalog %s.",
            upgradeCtx->baseCtx.loadParam->soName.str);
        return GMERR_DATA_EXCEPTION;
    }
    upgradeCtx->dropTableByteCode.byteCodeBuf = getByteCode(&upgradeCtx->dropTableByteCode.bufLen, DROP_TABLE_BUF_ID);
    if (upgradeCtx->dropTableByteCode.byteCodeBuf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get drop table byte code when load datalog %s.",
            upgradeCtx->baseCtx.loadParam->soName.str);
        return GMERR_DATA_EXCEPTION;
    }
    upgradeCtx->dropUdfByteCode.byteCodeBuf = getByteCode(&upgradeCtx->dropUdfByteCode.bufLen, DROP_UDF_BUF_ID);
    if (upgradeCtx->dropUdfByteCode.byteCodeBuf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get drop udf byte code when load datalog %s.",
            upgradeCtx->baseCtx.loadParam->soName.str);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

Status GetTableInfo4Upgrade(SessionT *session, UpgradeCtxT *upgradeCtx, LibFuncT getByteCode)
{
    // 升级的规则以及通过联调图连接的规则中，涉及的所有输入、中间、输出表
    upgradeCtx->redoTableByteCode.byteCodeBuf = getByteCode(&upgradeCtx->redoTableByteCode.bufLen, REDO_TABLE_NAMES_ID);
    if (upgradeCtx->redoTableByteCode.byteCodeBuf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get drop udf byte code when load datalog %s.",
            upgradeCtx->baseCtx.loadParam->soName.str);
        return GMERR_DATA_EXCEPTION;
    }

    // 升级后，所有table的topo排序
    upgradeCtx->topoSortTblByteCode.byteCodeBuf =
        getByteCode(&upgradeCtx->topoSortTblByteCode.bufLen, TOPO_SORT_TABLE_NAMES_ID);
    if (upgradeCtx->topoSortTblByteCode.byteCodeBuf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get topo sort table byte code when load datalog %s.",
            upgradeCtx->baseCtx.loadParam->soName.str);
        return GMERR_DATA_EXCEPTION;
    }

    // 升级时，需要升级的no heap表
    upgradeCtx->redoOutputTblByteCode.byteCodeBuf =
        getByteCode(&upgradeCtx->redoOutputTblByteCode.bufLen, REDO_OUTPUT_NO_HEAP_TABLES_ID);
    if (upgradeCtx->redoOutputTblByteCode.byteCodeBuf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "Unable to get redo no heap output tables byte code when load datalog %s.",
            upgradeCtx->baseCtx.loadParam->soName.str);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

Status GetByteCode4Upgrade(SessionT *session, UpgradeCtxT *upgradeCtx)
{
    LibFuncT getByteCode = upgradeCtx->baseCtx.getByteCode;
    // 修改资源
    Status ret = GetAlterByteCode(session, upgradeCtx, getByteCode);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 删除资源
    if ((ret = GetDeleteByteCode(session, upgradeCtx, getByteCode)) != GMERR_OK) {
        return ret;
    }

    // 获取table相关信息
    return GetTableInfo4Upgrade(session, upgradeCtx, getByteCode);
}

static Status RefreshDeseriTblInfo4Distribute(UpgradeCtxT *upgradeCtx)
{
    DatalogProgramInfoT *programInfo = (DatalogProgramInfoT *)upgradeCtx->baseCtx.programInfo;
    if (!programInfo->isDistribute) {
        return GMERR_OK;
    }

    Status ret = GMERR_OK;
    DbListT *refreshList[] = {upgradeCtx->redoTableList, upgradeCtx->newTopoSortTblList, upgradeCtx->redoOutputTblList};
    uint32_t refreshCnt = (uint32_t)ELEMENT_COUNT(refreshList);
    for (uint32_t i = 0; i < refreshCnt; i++) {
        if (refreshList[i] == NULL) {
            continue;
        }
        ret = DtlRefreshFakeExternalTblNameList(
            upgradeCtx->backgroundMemCtx, upgradeCtx->baseCtx.namespaceId, refreshList[i]);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to refresh list, index:%" PRIu32 ".", i);
            return ret;
        }
    }

    return GMERR_OK;
}

Status DeserializeTableInfo4Upgrade(SessionT *session, UpgradeCtxT *upgradeCtx)
{
    char *soPath = upgradeCtx->baseCtx.loadParam->soPath.str;
    // 反序列化需要重做的表
    Status ret = DtlDeserializeNames4Upgrade(upgradeCtx->backgroundMemCtx, upgradeCtx->redoTableByteCode.byteCodeBuf,
        upgradeCtx->redoTableByteCode.bufLen, &upgradeCtx->redoTableList);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to deserialize delete table buf, soPath %s", soPath);
        return ret;
    }

    // 反序列化所有表的topo排序
    ret = DtlDeserializeNames4Upgrade(upgradeCtx->backgroundMemCtx, upgradeCtx->topoSortTblByteCode.byteCodeBuf,
        upgradeCtx->topoSortTblByteCode.bufLen, &upgradeCtx->newTopoSortTblList);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to deserialize topo sort table buf, soPath %s", soPath);
        return ret;
    }

    // 反序列化Redo关键字指定的输出表
    ret = DtlDeserializeNames4Upgrade(upgradeCtx->backgroundMemCtx, upgradeCtx->redoOutputTblByteCode.byteCodeBuf,
        upgradeCtx->redoOutputTblByteCode.bufLen, &upgradeCtx->redoOutputTblList);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to deserialize redo no heap output table buf, soPath %s", soPath);  // LCOV_EXCL_LINE
        return ret;
    }

    if ((ret = RefreshDeseriTblInfo4Distribute(upgradeCtx)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to refresh table info list, soPath %s", soPath);  // LCOV_EXCL_LINE
        return ret;
    }

    return GMERR_OK;
}

Status DeserializeObjects4Upgrade(SessionT *session, UpgradeCtxT *upgradeCtx)
{
    Status ret = DeserializeObjects(session, upgradeCtx->backgroundMemCtx, &upgradeCtx->baseCtx);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = GetByteCode4Upgrade(session, upgradeCtx);
    if (ret != GMERR_OK) {
        return ret;
    }

    char *soPath = upgradeCtx->baseCtx.loadParam->soPath.str;
    // 反序列化修改的表
    ret = DtlDeserializeVertexLabels(upgradeCtx->baseCtx.memCtx, upgradeCtx->alterTableByteCode.byteCodeBuf,
        upgradeCtx->alterTableByteCode.bufLen, &upgradeCtx->alterTableList);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to deserialize upgrade table buf, soPath %s, bufLen %" PRIu32, soPath,
            upgradeCtx->alterTableByteCode.bufLen);
        return ret;
    }

    // 使用upgradeCtx->backgroundMemCtx内存池，在后台重做完成后一把释放
    // 反序列化修改的udf
    ret = DtlDeserializeUDFs(upgradeCtx->backgroundMemCtx, upgradeCtx->alterUdfByteCode.byteCodeBuf,
        upgradeCtx->alterUdfByteCode.bufLen, &upgradeCtx->alterUdfList);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to deserialize alter udfs, soPath %s", soPath);  // LCOV_EXCL_LINE
        return ret;
    }

    // 反序列化删除的表
    ret = DtlDeserializeNames4Upgrade(upgradeCtx->backgroundMemCtx, upgradeCtx->dropTableByteCode.byteCodeBuf,
        upgradeCtx->dropTableByteCode.bufLen, &upgradeCtx->dropTableList);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to deserialize delete table buf, soPath %s", soPath);  // LCOV_EXCL_LINE
        return ret;
    }
    // 反序列化删除的udf
    ret = DtlDeserializeNames4Upgrade(upgradeCtx->backgroundMemCtx, upgradeCtx->dropUdfByteCode.byteCodeBuf,
        upgradeCtx->dropUdfByteCode.bufLen, &upgradeCtx->dropUdfList);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to deserialize delete udfs, soPath %s", soPath);  // LCOV_EXCL_LINE
        return ret;
    }

    return DeserializeTableInfo4Upgrade(session, upgradeCtx);
}

// true: list item exists in map
uint32_t *GetTblIdIfListItemInMap(DbListT *newInputIds, DbOamapT *redoTableIdMap)
{
    uint32_t cnt = DbListGetItemCnt(newInputIds);
    for (uint32_t i = 0; i < cnt; i++) {
        uint32_t *inpId = DbListItem(newInputIds, i);
        void *key = DbOamapLookupKey(redoTableIdMap, *inpId, inpId);
        if (key != NULL) {
            return inpId;
        }
    }
    return NULL;
}

static uint32_t GetTriggerTableIdFromRule(UpgradeCtxT *upgradeCtx, DbListT *ruleInputIds)
{
    uint32_t firstInputLabelId = DB_INVALID_LABEL_ID;
    for (uint32_t i = 0; i < DbListGetItemCnt(ruleInputIds); i++) {
        uint32_t labelId = *(uint32_t *)DbListItem(ruleInputIds, i);
        for (uint32_t j = 0; j < DbListGetItemCnt(&upgradeCtx->triggerLabelIdList); j++) {
            uint32_t triggerLabelId = *(uint32_t *)DbListItem(&upgradeCtx->triggerLabelIdList, j);
            // 已经是触发表，就不用再找了，合并在一起做
            if (labelId == triggerLabelId) {
                return triggerLabelId;
            }
        }
        if (firstInputLabelId == DB_INVALID_LABEL_ID) {
            firstInputLabelId = labelId;
        }
    }
    return firstInputLabelId;
}

// 因为alterPlanList中的执行计划都是按topo序排序的，所以只需要将计划的输出放入outputMap，当判断计划的输入表在outputMap中已存在时，说明该计划为串联规则的后一条
Status GetTriggerTbIdsAndRuleNameByPlan(UpgradeCtxT *upgradeCtx, DbListT *planList, DbOamapT *ruleNameMap,
    DbOamapT *redoTableIdMap, DbOamapT *triggerTableIdMap)
{
    Status ret = GMERR_OK;
    const char *soPath = UpgradeCtxGetSoPath(upgradeCtx);
    uint32_t planCnt = planList == NULL ? 0 : DbListGetItemCnt(planList);
    uint64_t startTime = DbClockGetTsc();
    for (uint32_t i = 0; i < planCnt; i++) {
        // keep alive if cost too much times
        if (DbExceedTime(startTime, EXEC_SPLIT_TIME)) {
            (void)DrtKeepThisWorkerAlive(NULL);
            startTime = DbClockGetTsc();
        }
        PlannedStmtT *plan = DbListItem(planList, i);
        // 1、如果是merge计划，则continue
        if ((plan->planTree->tagType >= T_DELTA_MERGE_BEGIN) && (plan->planTree->tagType <= T_DELTA_MERGE_END)) {
            continue;
        }
        // 2、ruleName相同的计划，则continue，N张表join，会生成N条计划，但是只需要找到一条即可
        // 例如：D:-A,B规则会生成两条计划：1.deltaA表join orgB，投影到D；2.DeltaB表join orgA，投影到D
        uint32_t hash = DbStrToHash32(plan->ruleName);
        if (DbOamapLookupKey(ruleNameMap, hash, plan->ruleName) != NULL) {
            continue;
        }
        if ((ret = DbOamapInsert(ruleNameMap, hash, plan->ruleName, NULL, NULL)) != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "Unable to insert rule name to map;soPath=%s", soPath);  // LCOV_EXCL_LINE
            return ret;
        }
        // 3、从规则输入侧 获取输入表
        DbListT newInputIds = {0};
        DbCreateList(&newInputIds, sizeof(uint32_t), upgradeCtx->baseCtx.memCtx);
        if ((ret = DtlGetExecPlanLeafTblIds(plan->planTree, &newInputIds)) != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "Unable to get input table id in new plan;soPath=%s", soPath);
            return ret;
        }

        // 4、判断是否存在于重做表中，重做表不作为触发表，否则存在旧数据按新规则计算的问题
        if (GetTblIdIfListItemInMap(&newInputIds, redoTableIdMap) != NULL) {
            continue;
        }

        // 5、将计划中的输入表id加入triggerLabelIdList
        uint32_t *labelIdPtr = GetTblIdIfListItemInMap(&newInputIds, triggerTableIdMap);
        uint32_t labelId = labelIdPtr == NULL ? GetTriggerTableIdFromRule(upgradeCtx, &newInputIds) : *labelIdPtr;
        if (labelId == DB_INVALID_LABEL_ID) {
            continue;
        }

        if ((ret = DbAppendListItem(&upgradeCtx->triggerLabelIdList, &labelId)) != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "Unable to append label id:%" PRIu32 ", rule name:%s, soPath=%s.", labelId,
                plan->ruleName, soPath);
            return ret;
        }
        // 6、计划的ruleName加入triggerRuleNameList （rulename内存在AlterPlan4Upgrade反序列化中申请）
        if ((ret = DbAppendListItem(&upgradeCtx->triggerRuleNameList, &plan->ruleName)) != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "Unable to append rule name:%s, label id:%" PRIu32 ", soPath=%s.",
                plan->ruleName, labelId, soPath);
            return ret;
        }
        DbDestroyList(&newInputIds);
    }
    return ret;
}

static Status TransTableNameListToIdMap(DbListT *tableNameList, DbOamapT *tableIdMap, UpgradeCtxT *upgradeCtx)
{
    if (tableNameList == NULL) {
        return GMERR_OK;
    }
    uint32_t count = DbListGetItemCnt(tableNameList);
    for (uint32_t i = 0; i < count; i++) {
        char *tableName = *(char **)DbListItem(tableNameList, i);
        CataKeyT cataKey = {0};
        DmVertexLabelT *label = NULL;
        CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, upgradeCtx->baseCtx.namespaceId, tableName);
        Status ret = CataGetVertexLabelByName(DbGetInstanceByMemCtx(upgradeCtx->baseCtx.memCtx), &cataKey, &label);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret,
                "Get trigger table and rule name unsucc, get vertex label by name mistake, table name: %s.", tableName);
            return ret;
        }

        ret = DbOamapInsert(tableIdMap, label->metaCommon.metaId, &label->metaCommon.metaId, NULL, NULL);
        (void)CataReleaseVertexLabel(label);
        if (ret != GMERR_OK && ret != GMERR_DUPLICATE_OBJECT) {
            DB_LOG_AND_SET_LASERR(ret,
                "Get trigger table and rule name unsucc, unable to insert datalog "
                "redo table name map, table name: %s.",
                tableName);
            return ret;
        }
    }
    return GMERR_OK;
}

static Status InitRuleAndRedoTableMap(
    UpgradeCtxT *upgradeCtx, DbOamapT *ruleNameMap, DbOamapT *redoTableMap, DbOamapT *triggerTableMap)
{
    DbMemCtxT *memCtx = upgradeCtx->baseCtx.memCtx;
    DbListT *triggerTableList = NULL;
    const char *soPath = UpgradeCtxGetSoPath(upgradeCtx);
    Status ret = DbOamapInit(ruleNameMap, DATALOG_PROGRAM_NUM, DbOamapStringCompare, memCtx, true);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(
            ret, "Get trigger table and rule name unsucc, unable to init datalog rule name map;soPath=%s", soPath);
        return ret;
    }

    // 将redoTableList转为 map
    ret = DbOamapInit(redoTableMap, DATALOG_PROGRAM_NUM, DbOamapUint32Compare, memCtx, true);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(
            ret, "Get trigger table and rule name unsucc, unable to init datalog redo table id map;soPath=%s", soPath);
        return ret;
    }

    // 将triggerTableList转为 map
    ret = DbOamapInit(triggerTableMap, DATALOG_PROGRAM_NUM, DbOamapUint32Compare, memCtx, true);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret,
            "Get trigger table and rule name unsucc, unable to init datalog trigger table id map;soPath=%s", soPath);
        return ret;
    }

    // 离线编译工具生成的触发表，可以为空
    ByteCodeT triggerTableByteCode;
    triggerTableByteCode.byteCodeBuf =
        upgradeCtx->baseCtx.getByteCode(&triggerTableByteCode.bufLen, UPGRADE_TRIGGER_TABLE_NAME_ID);
    ret = DtlDeserializeNames4Upgrade(
        memCtx, triggerTableByteCode.byteCodeBuf, triggerTableByteCode.bufLen, &triggerTableList);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to deserialize trigger table buf, soPath %s", soPath);  // LCOV_EXCL_LINE
        return ret;
    }

    ret = TransTableNameListToIdMap(upgradeCtx->redoTableList, redoTableMap, upgradeCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to TransTableNameListToIdMap for redo table list, soPath %s", soPath);
        return ret;
    }
    ret = TransTableNameListToIdMap(triggerTableList, triggerTableMap, upgradeCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to TransTableNameListToIdMap for trigger table list, soPath %s", soPath);
        return ret;
    }

    return GMERR_OK;
}

// 根据修改的规则，获取重做的表Id，并将可以串联起来的规则归并
Status GetTriggerTblIdsInBlockMode(UpgradeCtxT *upgradeCtx)
{
    DB_POINTER(upgradeCtx);
    const char *soPath = UpgradeCtxGetSoPath(upgradeCtx);
    DbOamapT ruleNameMap = {0};
    DbOamapT redoTableIdMap = {0};
    DbOamapT triggerTableIdMap = {0};
    // ruleName map，用判断ruleName相同的执行计划。对于ruleName相同的执行计划，只重做其中一条即可。
    Status ret = InitRuleAndRedoTableMap(upgradeCtx, &ruleNameMap, &redoTableIdMap, &triggerTableIdMap);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    // 通过中间表串联的规则，只取前一条规则中的输入表
    ret = GetTriggerTbIdsAndRuleNameByPlan(
        upgradeCtx, upgradeCtx->dropPlanList, &ruleNameMap, &redoTableIdMap, &triggerTableIdMap);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(
            ret, "Get trigger table and rule name unsucc, unable to get these by drop plan;soPath=%s", soPath);
        goto EXIT;
    }
    ret = GetTriggerTbIdsAndRuleNameByPlan(
        upgradeCtx, upgradeCtx->alterPlanList, &ruleNameMap, &redoTableIdMap, &triggerTableIdMap);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(
            ret, "Get trigger table and rule name unsucc, unable to get these by alter plan;soPath=%s", soPath);
        goto EXIT;
    }

    ret = GetTriggerTbIdsAndRuleNameByPlan(
        upgradeCtx, upgradeCtx->baseCtx.planList, &ruleNameMap, &redoTableIdMap, &triggerTableIdMap);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(
            ret, "Get trigger table and rule name unsucc, unable to get these by new plan;soPath=%s", soPath);
        goto EXIT;
    }

EXIT:
    DbOamapDestroy(&redoTableIdMap);
    DbOamapDestroy(&ruleNameMap);
    return ret;
}

static Status GetIptTblFromRedoTbls(UpgradeCtxT *upgradeCtx, const char *soPath)
{
    Status ret = GMERR_OK;
    uint32_t redoTblCnt = DbListGetItemCnt(upgradeCtx->redoTableList);
    for (uint32_t i = 0; i < redoTblCnt; i++) {
        char *tableName = *(char **)DbListItem(upgradeCtx->redoTableList, i);
        CataKeyT cataKey = {0};
        DmVertexLabelT *label = NULL;
        CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, upgradeCtx->baseCtx.namespaceId, tableName);
        if ((ret = CataGetVertexLabelByName(DbGetInstanceByMemCtx(upgradeCtx->baseCtx.memCtx), &cataKey, &label)) !=
            GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret,
                "Get input table by name unsucc, get vertex label by name mistake, table name: %s, soPath=%s, ret = "
                "%" PRId32 ".",
                tableName, soPath, ret);
            return ret;
        }

        if (label->commonInfo->datalogLabelInfo->inoutType == DM_DTL_INPUT_LABEL) {
            if ((ret = DbAppendListItem(&upgradeCtx->triggerLabelIdList, &label->metaCommon.metaId)) != GMERR_OK) {
                (void)CataReleaseVertexLabel(label);
                DB_LOG_AND_SET_LASERR(ret,
                    "Get input table by name unsucc, append vertex label id to trigger label id list mistake, "
                    "soPath=%s,label name is %s.",
                    soPath, tableName);
                return ret;
            }
        }
        ret = DbAppendListItem(upgradeCtx->redoTableIdList, &label->metaCommon.metaId);
        if (ret != GMERR_OK) {
            (void)CataReleaseVertexLabel(label);
            DB_LOG_AND_SET_LASERR(ret,
                "Get input table by name unsucc, append vertex label id to redo table list mistake, soPath=%s, label "
                "name is %s.",
                soPath, tableName);
            return ret;
        }
        (void)CataReleaseVertexLabel(label);
    }
    return ret;
}

// 获取需要重做的表中的(输入表+所有的表)
Status GetTriggerTblIdsInUnblockMode(UpgradeCtxT *upgradeCtx)
{
    if (upgradeCtx->redoTableList == NULL) {
        return GMERR_OK;
    }
    const char *soPath = UpgradeCtxGetSoPath(upgradeCtx);

    if ((upgradeCtx->redoTableIdList = DbDynMemCtxAlloc(upgradeCtx->baseCtx.memCtx, sizeof(DbListT))) == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY,
            "Get input table by name unsucc, alloc memory for redo table id list mistake, soPath=%s, size = %" PRIu32
            ".",
            soPath, (uint32_t)sizeof(DbListT));
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateList(upgradeCtx->redoTableIdList, sizeof(uint32_t), upgradeCtx->baseCtx.memCtx);

    // 获取重做表中的输入表
    return GetIptTblFromRedoTbls(upgradeCtx, soPath);
}

Status GetTriggerTblIds4Redo(UpgradeCtxT *upgradeCtx, bool isBlockMode)
{
    return isBlockMode ? GetTriggerTblIdsInBlockMode(upgradeCtx) : GetTriggerTblIdsInUnblockMode(upgradeCtx);
}
