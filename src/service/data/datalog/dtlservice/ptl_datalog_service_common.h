/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: load datalog program implement
 * Author: tangjiaxin
 * Create: 2023-07-17
 */

#ifndef PTL_DATALOG_SERVICE_COMMON_H
#define PTL_DATALOG_SERVICE_COMMON_H

#include "ptl_datalog_service.h"
#include "ptl_datalog_hot_patch_template.h"

#define DATALOG_PROGRAM_NUM 4

typedef enum {
    STEP_ONE = 1,
    STEP_TWO,
    STEP_THREE,
    STEP_FOUR,
    STEP_FIVE,
    STEP_SIX,
    STEP_SEVEN,
    STEP_EIGHT,
    STEP_NINE,
    STEP_TEN,
    STEP_ELEVEN,
    STEP_TWELVE,
    STEP_THIRTEEN,
    STEP_FOURTEEN,
    STEP_BUTT
} InitStepE;

typedef enum {
    PLAN_BUF_ID = 0,
    TABLE_BUF_ID = 1,
    UDF_BUF_ID = 2,
    RESOURCE_POOL_BUF_ID = 3,
    DROP_TABLE_BUF_ID = 4,
    ALTER_TABLE_BUF_ID = 5,
    DROP_PLAN_ID = 6,
    ALTER_PLAN_BUF_ID = 7,
    DROP_UDF_BUF_ID = 8,
    ALTER_UDF_BUF_ID = 9,
    REDO_TABLE_NAMES_ID = 10,
    TOPO_SORT_TABLE_NAMES_ID = 11,
    REDO_OUTPUT_NO_HEAP_TABLES_ID = 12,
    UPGRADE_BLOCK_CONFIG_ID = 13,  // 序列化为：uint8_t blockMode[1] = {0}/{1}, len入参可以为NULL
    UPGRADE_REDO_OFF_CONFIG_ID = 14,
    UPGRADE_TRIGGER_TABLE_NAME_ID = 15,
} ByteCodeNumE;

typedef struct DropUdfCtx {
    DbMemCtxT *memCtx;
    uint32_t nspId;
    uint32_t dbId;
    uint32_t dropNum;
    char *udfName;
    DbListT *udfList;
} DropUdfCtxT;

typedef struct DropTblCtx {
    DbMemCtxT *memCtx;
    uint32_t nspId;
    uint32_t dropNum;
    DbListT *tableList;
} DropTblCtxT;

Status DeserializeObjects(SessionT *session, DbMemCtxT *memCtx, LoadCtxT *loadCtx);
Status DropTable(SessionT *session, LoadCtxT *loadCtx, int32_t lastExecutedIndex, ReusedStmtObjT *reusedObj);
Status DtlParseCreator(DbMemCtxT *memCtx, SessionT *session, char **buf);
Status CheckTablesExistOrNot(uint32_t nspId, DbListT *tableList, bool shouldExist, DbInstanceHdT dbInstance);
Status CheckTablesExistOrNotByName(uint32_t nspId, DbListT *tableList, bool shouldExist, DbInstanceHdT dbInstance);
Status CheckUdfExistOrNot(uint32_t nspId, DbListT *udfList, bool shouldExist, DbInstanceHdT dbInstance);
Status CheckUdfsExistOrNotByName(uint32_t nspId, DbListT *udfList, bool shouldExist, DbInstanceHdT dbInstance);
Status BaseContextInit(LoadCtxT *loadCtx);
void BaseContextUninit(LoadCtxT *loadCtx);
Status CheckVersionIsLegal(SessionT *session, LoadCtxT *loadCtx, RollbackLoadCtxT *rollbackCtx);
Status CheckVersionIsLegal4Upgrade(SessionT *session, LoadCtxT *loadCtx, RollbackLoadCtxT *rollbackCtx);
Status CheckHashIsLegal(LoadCtxT *loadCtx);
Status CheckHashIsLegal4Upgrade(LoadCtxT *loadCtx, DatalogProgramInfoT *programInfo);
Status CreateTable(SessionT *session, LoadCtxT *loadCtx, RollbackLoadCtxT *rollbackCtx);
Status CreateUdf(SessionT *session, LoadCtxT *loadCtx, RollbackLoadCtxT *rollbackCtx);
void DropUdfImpl(SessionT *session, DbMemCtxT *memCtx, uint32_t udfId, ReusedStmtObjT *reusedStmtObj);
Status DropUdf(SessionT *session, LoadCtxT *loadCtx, int32_t lastExecutedIndex, ReusedStmtObjT *reusedStmtObj);
Status DropResourcePool(SessionT *session, LoadCtxT *loadCtx, int32_t lastCreatedIndex, ReusedStmtObjT *reusedStmtObj);
void DropUdfImpl(SessionT *session, DbMemCtxT *memCtx, uint32_t udfId, ReusedStmtObjT *reusedStmtObj);
void DropOriginalTable(SessionT *session, DbMemCtxT *memCtx, uint32_t labelId, ReusedStmtObjT *reusedStmtObj);
void UnbindResourcePool(SessionT *session, DbMemCtxT *memCtx, DmVertexLabelT *vrtxLabel, ReusedStmtObjT *reusedStmtObj);

/**
 * @brief 构造datalogProgramInfo在map中的key
 * @param[in] soName: so名称
 * @param[in] nspId: namespace id
 * @param[out] key: 生成的key
 * @param[in] keyMaxSize: key的最大长度
 * @return 成功或错误码
 */
static inline Status MakeDtlProgramKey(const char *soName, uint32_t nspId, char *key, uint32_t keyMaxSize)
{
    DB_POINTER2(soName, key);
    if (sprintf_s(key, keyMaxSize, "%s_%" PRIu32, soName, nspId) < 0) {
        DB_LOG_AND_SET_LASERR(
            GMERR_FIELD_OVERFLOW, "sprintf when making key, soName=%s, nspId=%" PRIu32 ".", soName, nspId);
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}
DatalogProgramInfoT *FindDatalogInfoBySoNameAndNspId(const char *soName, uint32_t nspId);
Status RemoveDatalogProgramInfo(const char *soName, uint32_t nspId);

Status LoadDatalogEntry(SessionT *session);
Status UpgradeDatalogEntry(SessionT *session);
Status DegradeDatalogEntry(SessionT *session);
Status UnloadDatalogEntry(SessionT *session);
Status GetTriggerTblIdsInBlockMode(UpgradeCtxT *upgradeCtx);
Status ExecuteLoadProcess(HotPatcherT *hotPatcher, SessionT *session);

#endif
