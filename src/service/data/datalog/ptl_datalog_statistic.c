/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: implementation of service datalog statistic
 * Author: hushuang
 * Create: 2022-10-20
 */

#include "ptl_datalog_statistic.h"
#include "ee_context.h"
#include "db_config.h"
#include "dm_meta_prop_label.h"

#ifdef __cplusplus
extern "C" {
#endif

static DmDmlPerfStatT *PtlGetLabelPerfStat(DmVertexLabelT *vertexLabel, uint32_t opCode)
{
    DB_POINTER2(vertexLabel, vertexLabel->commonInfo);
    switch (opCode) {
        case MSG_OP_RPC_INSERT_VERTEX:
            return &vertexLabel->commonInfo->metaInfoAddr->dmlPerfStat[INSERT_TIME_STATIS];
        case MSG_OP_RPC_DELETE_VERTEX:
            return &vertexLabel->commonInfo->metaInfoAddr->dmlPerfStat[DELETE_TIME_STATIS];
        case MSG_OP_RPC_UPDATE_VERTEX:
            return &vertexLabel->commonInfo->metaInfoAddr->dmlPerfStat[UPDATE_TIME_STATIS];
        default:
            return NULL;
    }
}

static DmDmlOperStatT *PtlGetLabelOperStat(DmVertexLabelT *vertexLabel, uint32_t opCode)
{
    DB_POINTER2(vertexLabel, vertexLabel->commonInfo);
    switch (opCode) {
        case MSG_OP_RPC_INSERT_VERTEX:
            return &vertexLabel->commonInfo->metaInfoAddr->dmlOperStat[INSERT_TIME_STATIS];
        case MSG_OP_RPC_DELETE_VERTEX:
            return &vertexLabel->commonInfo->metaInfoAddr->dmlOperStat[DELETE_TIME_STATIS];
        case MSG_OP_RPC_UPDATE_VERTEX:
            return &vertexLabel->commonInfo->metaInfoAddr->dmlOperStat[UPDATE_TIME_STATIS];
        default:
            return NULL;
    }
}

inline static DmDmlOperStatT *PtlGetOperStatis(DmVertexLabelT *vertexLabel, uint32_t opCode)
{
    if (!DbCfgGetBoolLite(DB_CFG_DML_OPER_STAT_IS_ENABLED, NULL)) {
        return NULL;
    }
    return PtlGetLabelOperStat(vertexLabel, opCode);
}

inline static DmDmlPerfStatT *PtlGetPerfStatis(DmVertexLabelT *vertexLabel, uint32_t opCode)
{
    if (!DbCfgGetBoolLite(DB_CFG_DML_PERF_STAT_IS_ENABLED, NULL)) {
        return NULL;
    }
    return PtlGetLabelPerfStat(vertexLabel, opCode);
}

static void PtlStatisticsPrepareCnt(DmVertexLabelT *vertexLabel, Status opStatus, uint32_t opCode)
{
    DmDmlOperStatT *operStatis = PtlGetOperStatis(vertexLabel, opCode);
    if (operStatis == NULL) {
        return;
    }
    if (opStatus == GMERR_OK) {
        (void)DbAtomicFetchAndAdd64(&operStatis->successPrepareCount, 1);
    } else {
        (void)DbAtomicFetchAndAdd64(&operStatis->failPrepareCount, 1);
    }
}

static void PtlStatisticsExecuteCnt(DmVertexLabelT *vertexLabel, Status opStatus, uint32_t opCode)
{
    DmDmlOperStatT *operStatis = PtlGetOperStatis(vertexLabel, opCode);
    if (operStatis == NULL) {
        return;
    }
    if (opStatus == GMERR_OK) {
        (void)DbAtomicFetchAndAdd64(&operStatis->successExecuteCount, 1);
    } else {
        (void)DbAtomicFetchAndAdd64(&operStatis->failExecuteCount, 1);
    }
}

static void PtlStatisticsPrepareTime(DmVertexLabelT *vertexLabel, uint64_t prepareTime, uint32_t opCode)
{
    DmDmlPerfStatT *perfStatis = PtlGetPerfStatis(vertexLabel, opCode);
    if (perfStatis == NULL) {
        return;
    }
    (void)DbAtomicFetchAndAdd64(&perfStatis->totalPrepareTime, prepareTime);
    if (prepareTime > perfStatis->maxPrepareTime) {
        DbAtomicSet64(&perfStatis->maxPrepareTime, prepareTime);
    }
}

static void PtlStatisticsExecuteTime(DmVertexLabelT *vertexLabel, uint64_t beginExecuteTime, uint32_t opCode)
{
    DmDmlPerfStatT *perfStatis = PtlGetPerfStatis(vertexLabel, opCode);
    if (perfStatis == NULL) {
        return;
    }
    uint64_t currentTime = DbRdtsc();
    uint64_t usedTime = DbToUseconds(currentTime - beginExecuteTime);
    (void)DbAtomicFetchAndAdd64(&perfStatis->totalExecuteTime, usedTime);
    if (usedTime > perfStatis->maxExecuteTime) {
        DbAtomicSet64(&perfStatis->maxExecuteTime, usedTime);
    }
}

void PtlStatisticsForPrepare(
    DbInstanceHdT dbInstance, uint32_t vrtxLabelId, Status opStatus, uint64_t prepareTime, uint32_t opCode)
{
    if ((opCode >= MSG_OP_RPC_DML_BEGIN && opCode < MSG_OP_RPC_DML_END) || opCode == MSG_OP_RPC_BATCH) {
        DmVertexLabelT *vertexLabel = NULL;
        Status ret = CataGetVertexLabelById(dbInstance, vrtxLabelId, &vertexLabel);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "get vertex label by id(%" PRIu32 ") when ptl static for prepare.", vrtxLabelId);
            return;
        }
        // Subscription table does not record execution information
        if (vertexLabel->commonInfo->datalogLabelInfo->postProc != DM_DTL_SUBSCRIPTION) {
            PtlStatisticsPrepareCnt(vertexLabel, opStatus, opCode);
            if (opStatus == GMERR_OK) {
                PtlStatisticsPrepareTime(vertexLabel, prepareTime, opCode);
            }
        }
        (void)CataReleaseVertexLabel(vertexLabel);
    }
}

void PtlStatisticsForExecute(
    DbInstanceHdT dbInstance, uint32_t vrtxLabelId, Status opStatus, uint64_t beginExecuteTime, uint32_t opCode)
{
    if (!DbCfgGetBoolLite(DB_CFG_DML_PERF_STAT_IS_ENABLED, NULL) &&
        !DbCfgGetBoolLite(DB_CFG_DML_OPER_STAT_IS_ENABLED, NULL)) {
        return;
    }
    if ((opCode >= MSG_OP_RPC_DML_BEGIN && opCode < MSG_OP_RPC_DML_END) || opCode == MSG_OP_RPC_BATCH) {
        DmVertexLabelT *vertexLabel = NULL;
        Status ret = CataGetVertexLabelById(dbInstance, vrtxLabelId, &vertexLabel);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "get vertex label by id(%" PRIu32 ") when ptl static for execute.", vrtxLabelId);
            return;
        }
        PtlStatisticsExecuteCnt(vertexLabel, opStatus, opCode);
        if (opStatus == GMERR_OK) {
            PtlStatisticsExecuteTime(vertexLabel, beginExecuteTime, opCode);
        }
        (void)CataReleaseVertexLabel(vertexLabel);
    }
}

#ifdef __cplusplus
}
#endif
