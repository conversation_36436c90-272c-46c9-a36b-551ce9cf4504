/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: header file of service datalog statistic
 * Author: hushuang
 * Create: 2022-10-20
 */

#ifndef PTL_DATALOG_STATISTIC_H
#define PTL_DATALOG_STATISTIC_H

#include "dm_meta_prop_label.h"

#ifdef __cplusplus
extern "C" {
#endif

void PtlStatisticsForPrepare(
    DbInstanceHdT dbInstance, uint32_t vrtxLabelId, Status opStatus, uint64_t prepareTime, uint32_t opCode);
void PtlStatisticsForExecute(
    DbInstanceHdT dbInstance, uint32_t vrtxLabelId, Status opStatus, uint64_t beginExecuteTime, uint32_t opCode);

#ifdef __cplusplus
}
#endif

#endif /* PTL_DATALOG_STATISTIC_H */
