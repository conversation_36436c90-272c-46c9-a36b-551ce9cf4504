/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: sorted table iterator impl
 * Note1: memCtx is used in short process period , we will free all memory after finishing one request
 * Author: GMDB EE team
 * Create: 2023-3-16
 */

#include "ptl_sorted_table_iterator.h"
#include "ee_plan_stmt.h"
#include "ee_delta_table_info_access_method.h"
#include "ptl_iterator.h"
#include "ptl_datalog_utils.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

Status NewSortedTableIterator(DbMemCtxT *memCtx, DtlSessCtxT *dtlCtx, SortedTableIteratorT **iterator)
{
    DB_POINTER5(memCtx, dtlCtx, dtlCtx->sortedTableList, dtlCtx->dtlTableInfoMap, iterator);
    // memory will be freed unitied , see Note1
    SortedTableIteratorT *ite = (SortedTableIteratorT *)DbDynMemCtxAlloc(memCtx, sizeof(SortedTableIteratorT));
    if (ite == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc memory unsucc SortedTableIterator.");
        return GMERR_OUT_OF_MEMORY;
    }

    *ite = (SortedTableIteratorT){0};

    ite->iterator.tag = SORTED_TABLE_ITERATOR;
    ite->iterator.seekPos = 0;
    ite->iterator.direction = IT_FORWARD;
    ite->dtlCtx = dtlCtx;
    ite->sortedTableList = dtlCtx->sortedTableList;
    ite->tableInfoMap = dtlCtx->dtlTableInfoMap;

    *iterator = ite;
    return GMERR_OK;
}

bool SortedTableIteratorHasNext(IteratorT *iterator)
{
    DB_POINTER(iterator);
    SortedTableIteratorT *ite = (SortedTableIteratorT *)(void *)iterator;

    switch (iterator->direction) {
        case IT_FORWARD: {
            return iterator->seekPos < (int32_t)DbListGetItemCnt(ite->sortedTableList);
        }

        case IT_BACKWARD: {
            return iterator->seekPos >= 0;
        }

        default: {
            return false;
        }
    }
}

ElemT SortedTableIteratorNext(IteratorT *iterator)
{
    DB_POINTER(iterator);
    SortedTableIteratorT *ite = (SortedTableIteratorT *)(void *)iterator;

    ClassifiedRelatedTblT *tbl = (ClassifiedRelatedTblT *)DbListItem(ite->sortedTableList, (uint32_t)iterator->seekPos);

    switch (iterator->direction) {
        case IT_FORWARD: {
            iterator->seekPos++;
            break;
        }

        case IT_BACKWARD: {
            iterator->seekPos--;
            break;
        }

        default: {
        }
    }
    // tables with RW_TYPE_READ do not need to be executed
    if (tbl->relatedTable.tblType == RW_TYPE_READ) {
        return NULL;
    }

    // tables without delta data do not need to be executed
    TableInfoT *table = DbOamapLookup(ite->tableInfoMap, tbl->relatedTable.tableId, &tbl->relatedTable.tableId, NULL);
    if (table == NULL) {
        if (iterator->direction == IT_FORWARD) {
            (void)DtlStatisticEmptyCal(ite->dtlCtx, NULL, tbl->relatedTable.tableId, tbl->writeDataType);
        }
        return NULL;
    }
    // set table's turn of precedence keywords
    TableInfoSetWriteDataType(table, tbl->writeDataType);
    return table;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
