/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: table list processor impl
 * Author: GMDB EE team
 * Create: 2023-3-16
 */

#include "ptl_table_list_processor.h"
#include "cpl_planner.h"
#include "ptl_datalog_statistic.h"

#ifdef __cplusplus
extern "C" {
#endif

/*
 * impl of specific processor
 */
static inline TableListProcessorMethodT *SetTableListProcessorMethods(TableListProcessorMethodT *func,
    IteratorT *(*iterator)(TableListProcessorT *processor),
    Status (*needProcess)(TableListProcessorT *processor, TableInfoT *table, bool *need),
    Status (*process)(TableListProcessorT *processor, TableInfoT *table))
{
    func->iterator = iterator;
    func->needProcess = needProcess;
    func->process = process;
    return func;
}

static TableListProcessorMethodT *GetTableListProcessorMethods(ProcessTagT tag, TableListProcessorMethodT *func)
{
    switch (tag) {
        case FORWARD_PROCESS:
            return SetTableListProcessorMethods(func, ForwardTableListProcessorIterator,
                ForwardTableListProcessorNeedProcess, ForwardTableListProcessorProcess);
        case BACKWARD_PROCESS:
            return SetTableListProcessorMethods(func, BackwardTableListProcessorIterator,
                BackwardTableListProcessorNeedProcess, BackwardTableListProcessorProcess);
        default:
            return SetTableListProcessorMethods(func, NULL, NULL, NULL);
    }
}

inline static IteratorT *GetIterator(TableListProcessorT *processor)
{
    TableListProcessorMethodT func;
    (void)GetTableListProcessorMethods(processor->tag, &func);
    return func.iterator(processor);
}

inline static Status NeedProcess(TableListProcessorT *processor, TableInfoT *table, bool *need)
{
    TableListProcessorMethodT func;
    (void)GetTableListProcessorMethods(processor->tag, &func);
    return func.needProcess(processor, table, need);
}

inline static Status Process(TableListProcessorT *processor, TableInfoT *table)
{
    TableListProcessorMethodT func;
    (void)GetTableListProcessorMethods(processor->tag, &func);
    return func.process(processor, table);
}

inline static void UpdateTbmAndNotifyTimeCost(DtlSessCtxT *dtlCtx, uint64_t timeCost, DmDtlPostProcE postProcessType)
{
    if (postProcessType == DM_DTL_SUBSCRIPTION) {
        dtlCtx->dfxInfo.pubsubTotalTime += timeCost;
    } else if (postProcessType == DM_DTL_TBM_SHMEM) {
        dtlCtx->dfxInfo.tbmTotalTime += timeCost;
    }
}

Status ProcessEveryTable(TableListProcessorT *processor)
{
    DB_POINTER2(processor, processor->session->datalogCtx);
    DtlSessCtxT *dtlCtx = processor->session->datalogCtx;

    Status ret = GMERR_OK;
    // iterate every table
    IteratorT *iterator = GetIterator(processor);
    int32_t i = -1;
    while (IteratorHasNext(iterator)) {
        TableInfoT *table = (TableInfoT *)(void *)IteratorNext(iterator);
        if (table == NULL) {
            continue;
        }

        // judge this table need process or not
        bool need = false;
        ret = NeedProcess(processor, table, &need);
        i++;
        DTL_RUN_LINK_LOG(DTL_RUN_LINK_LOG_SERVICE,
            "%s i=%" PRId32 ", needProcess=%" PRIu32 ", writeDataType=%" PRIu32 ".", table->table->vertexLabelName, i,
            need, (uint32_t)table->writeDataType);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "judge table need process unsucc; tableName=%s,tableId=%" PRIu32 ",processTag=%" PRIu32,
                table->table->vertexLabelName, table->labelId, (uint32_t)processor->tag);
            return ret;
        }

        // no need to process this table
        if (!need) {
            continue;
        }

        if (DtlSessGetExecUpgDml(dtlCtx)) {
            ret = TableInfoModifyUpgradeVersion2Zero(table);
            if (ret != GMERR_OK) {
                return ret;
            }
        }

        // process this table
        uint64_t beginExecuteTime = DbRdtsc();
        ret = Process(processor, table);
        if (processor->tag != BACKWARD_PROCESS) {  // we DON'T take statistics for rollback procedure
            // 输出表tbm&pubsub耗时
            UpdateTbmAndNotifyTimeCost(dtlCtx, DbToUseconds(DbRdtsc() - beginExecuteTime), table->postProcessType);
        }
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "process table in iterator unsucc; name=%s,id=%" PRIu32 ",tag=%" PRIu32,
                table->table->vertexLabelName, table->labelId, (uint32_t)processor->tag);
            return ret;
        }
    }
    return ret;
}

Status ProcessorCreatePlannedStmt(const TableListProcessorT *processor, DbMemCtxT *memCtx, NodeTagT nodeTag,
    DmVertexLabelT *vertexLabel, PlannedStmtT **plannedStmt)
{
    DB_POINTER3(processor, memCtx, plannedStmt);

    PlanT *plan = NULL;
    Status ret = MergeGenerate(memCtx, nodeTag, vertexLabel, &plan);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = NewSingleStmt(memCtx, plan, plannedStmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

Status TableListProcessorGetMergePlan(
    TableListProcessorT *processor, TableInfoT *table, DbListT *planList, PlannedStmtT **plan)
{
    DB_POINTER4(processor, table, planList, plan);
    uint32_t cnt = DbListGetItemCnt(planList);
    for (uint32_t i = 0; i < cnt; i++) {
        PlannedStmtT *innerPlan = (PlannedStmtT *)DbListItem(planList, i);
        if (IsMergePlan(innerPlan)) {
            *plan = innerPlan;
            return GMERR_OK;
        }
    }

    DB_LOG_AND_SET_LASERR(
        GMERR_DATA_EXCEPTION, "find merge plan when post process;tableName=%s", table->table->vertexLabelName);
    return GMERR_DATA_EXCEPTION;
}

Status ProcessTableNeedProcess4HotPatch(TableListProcessorT *processor, TableInfoT *tableInfo, bool *need)
{
    // heap表直接返回
    if (!(*need) || tableInfo->postProcessType == DM_DTL_DB_WRITE) {
        return GMERR_OK;
    }

    DbOamapT *redoTableMap = DtlUpgradeSessCtxGetRedoNoHeapTblsMap(&processor->session->datalogCtx->dtlUpgradeSessCtx);
    char *tableName = tableInfo->table->vertexLabelName;
    uint32_t hash = DbStrToHash32(tableName);
    // 热升级场景中编译阶段指定的表在重做过程中需要推送消息
    if (DbOamapUsedSize(redoTableMap) != 0 && DbOamapLookupKey(redoTableMap, hash, tableName) == NULL) {
        *need = false;
    }

    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
