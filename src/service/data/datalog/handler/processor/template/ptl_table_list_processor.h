/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: table list processor template.
 * Author: GMDBv5 EE Team
 * Create: 2023-3-16
 */

#ifndef PTL_TABLE_LIST_PROCESSOR_H
#define PTL_TABLE_LIST_PROCESSOR_H

#include "ptl_sorted_table_iterator.h"
#include "ee_plan_stmt.h"
#include "ee_delta_table_info_access_method.h"
#include "ee_session.h"
#include "ee_session_interface.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef enum ProcessTag {
    FORWARD_PROCESS,
    BACKWARD_PROCESS,
} ProcessTagT;

/*
 * abstract table list processor holds an iterator of sorted table
 */
typedef struct TableListProcessor {
    ProcessTagT tag;      // process type , one specific impl one tag
    IteratorT *iterator;  // sorted table iterator
    SessionT *session;    // session of one conn
} TableListProcessorT;

/*
 * processor which process every table ascending
 */
typedef struct ForwardTableListProcessor {
    TableListProcessorT processor;
} ForwardTableListProcessorT;

/*
 * processor which process every table descending
 * which is used in rollback
 */
typedef struct BackwardTableListProcessor {
    TableListProcessorT processor;
} BackwardTableListProcessorT;

/*
 * interface of processor
 */
typedef struct TableListProcessorMethod {
    IteratorT *(*iterator)(TableListProcessorT *processor);
    Status (*needProcess)(TableListProcessorT *processor, TableInfoT *table, bool *need);
    Status (*process)(TableListProcessorT *processor, TableInfoT *table);
} TableListProcessorMethodT;

/*
 * ******************************** template method begin ****************************************************
 */

/*
 * abstract process every table in processor as follows:
 *  1. get iterator of tables from processor
 *  2. return table from iterator by next()
 *  3. judge table can be processed by needProcess()
 *  4. process table by process()
 */
Status ProcessEveryTable(TableListProcessorT *processor);

/*
 * ******************************** template method end ******************************************************
 */

/*
 * ******************************** basic method begin ****************************************************
 */

Status ProcessorCreatePlannedStmt(const TableListProcessorT *processor, DbMemCtxT *memCtx, NodeTagT nodeTag,
    DmVertexLabelT *vertexLabel, PlannedStmtT **plannedStmt);

Status TableListProcessorGetMergePlan(
    TableListProcessorT *processor, TableInfoT *table, DbListT *planList, PlannedStmtT **plan);

/*
 * ******************************** basic method end ******************************************************
 */

/*
 * ******************************* specific impl of processor begin ******************************************
 */
Status NewForwardTableListProcessor(
    DbMemCtxT *memCtx, SessionT *session, IteratorT *iterator, ForwardTableListProcessorT **processor);
IteratorT *ForwardTableListProcessorIterator(TableListProcessorT *processor);
Status ForwardTableListProcessorNeedProcess(TableListProcessorT *processor, TableInfoT *table, bool *need);
Status ForwardTableListProcessorProcess(TableListProcessorT *processor, TableInfoT *table);

Status NewBackwardTableListProcessor(
    DbMemCtxT *memCtx, SessionT *session, IteratorT *iterator, BackwardTableListProcessorT **processor);
IteratorT *BackwardTableListProcessorIterator(TableListProcessorT *processor);
Status BackwardTableListProcessorNeedProcess(TableListProcessorT *processor, TableInfoT *table, bool *need);
Status BackwardTableListProcessorProcess(TableListProcessorT *processor, TableInfoT *table);

Status ProcessTableNeedProcess4HotPatch(TableListProcessorT *processor, TableInfoT *tableInfo, bool *need);
/*
 * ******************************* specific impl of processor end *******************************************
 */

#ifdef __cplusplus
}
#endif

#endif  // PTL_TABLE_LIST_PROCESSOR_H
