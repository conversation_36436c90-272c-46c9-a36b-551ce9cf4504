/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: stream component
 *
 * Author: ya<PERSON><PERSON>ran
 * Create: 2025-02-05
 */
#include "srv_data_stream.h"

static StreamSrvAmT g_gmdbStreamSrvAm = {0};

void StreamSrvAmInit(StreamSrvAmT *am)
{
    g_gmdbStreamSrvAm = *am;
}

Status StreamForwardNextByAA(SessionT *session, DmVertexLabelT *streamVertexLabel, AAT *aa)
{
    if (g_gmdbStreamSrvAm.forwardHook == NULL) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "StreamForwardNextByAA.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return g_gmdbStreamSrvAm.forwardHook(session, streamVertexLabel, aa);
}
