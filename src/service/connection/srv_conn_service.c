/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: ptl_conn_service.c
 * Description: source file for connection service
 * Author:
 * Create: 2020-8-10
 */

#include "srv_conn_service.h"
#include "drt_pipe.h"
#include "adpt_string.h"
#include "ptl_data_service.h"
#include "srv_data_service.h"
#include "ptl_service_utils.h"
#include "drt_data_send_channel.h"
#include "drt_node.h"
#include "drt_pipe.h"
#include "ee_cltcache.h"
#include "ee_session_interface.h"
#include "ee_session.h"
#include "db_alarm.h"
#include "ee_sub_channels.h"
#include "srv_log.h"
#include "se_resource_session_pub.h"
#include "db_dyn_load.h"
#include "ee_check.h"
#include "srv_data_fastpath_dw_pubsub.h"
#include "db_direct_msg_ring.h"
#include "db_thread_pool_tls.h"
#include "ee_feature_import.h"
#include "db_config.h"
#include "db_stream_msg_pool.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define DEFAULT_CONNECTION_WEIGHT 1
#define DEFAULT_USER_WEIGHT 1
#define USER_POLICY_ENFORCING_MODE 2  // 强校验

typedef struct ConnPrepareResourceCtx {
    CliConnectRequestT connReq;
    CliConnectResponseT connRsp;
    DrtNodeT *node;
    FixBufferT response;
    DbMemCtxT *subOldMemCtx;
} ConnPrepareResourceCtxT;

static CliConnectResponseT ConnCliConnectRespInit(void)
{
    CliConnectResponseT connResp = {.cliId = DB_INVALID_ID16,
        .version = 0,
        .instanceId = DB_INVALID_ID16,
        .sid = DB_INVALID_ID32,
        .connId = DB_INVALID_ID16,
        .trxSlot = DB_INVALID_ID16,
        .tupleAddrMode = SE_HEAP_TUPLE_ADDR_64,
        .clientConnectByTcp = false,
        .shareMsgMgrPtr = DB_INVALID_SHMPTR,
        .serverModeShmPtr = DB_INVALID_SHMPTR,
        .dfgmtMgrShmPtr = DB_INVALID_SHMPTR,
        .flowCtrlSleepTime = {0},
        .firstWorkerHungThreshold = 0,
        .agedMgrLatchShmAddr = DB_INVALID_SHMPTR,
        .agedMgrShmTaskMapShmAddr = DB_INVALID_SHMPTR,
        .cfgShmPtr = DB_INVALID_SHMPTR,
        .dwPubSubInfoPtr = DB_INVALID_SHMPTR,
        .directMsgPoolMgrShmPtr = DB_INVALID_SHMPTR,
        .shmEntry = DB_INVALID_SHMPTR,
        .shmEntryVersion = DB_INVALID_UINT32,
        .shmNotifyMsgQue = DB_INVALID_SHMPTR,
        .shmNotifyChanCtrl = DB_INVALID_SHMPTR,
        .cmpntLibPath = "",
        .cmpntLibLoadFlag = {0}};
    return connResp;
}

static Status ConnCompleteCliConnectInner(
    const DrtConnectionT *conn, Status opStatus, FixBufferT *rsp, const CliConnectResponseT *connResp)
{
    DB_POINTER3(conn, rsp, connResp);
    if (FixBufGetPos(rsp) != MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "CONN, rsp pos: %" PRIu32 ", opStatus: %" PRId32 ", connId: %" PRIu16,
            FixBufGetPos(rsp), (int32_t)opStatus, conn->id);
        DB_ASSERT(false);
        return GMERR_INTERNAL_ERROR;
    }
    Status ret = FixBufPutData(rsp, connResp, sizeof(CliConnectResponseT));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "CONN generate cliConnect response, opStatus: %" PRId32 ", connId: %" PRIu16,
            (int32_t)opStatus, conn->id);
        return ret;
    }
    if (opStatus != GMERR_OK) {
        ret = PtlServiceSetLastError(rsp);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "CONN set lastErr, connId: %" PRIu16 ".", conn->id);
            return ret;
        }
    }
    return GMERR_OK;
}

static FixBufferT ConnPrepareRsp(ConnPrepareResourceCtxT *ctx, DrtConnectionT *conn)
{
    FixBufferT rsp = {0};
    FixBufMove(&ctx->response, &rsp);
    if (FixBufGetBuf(&rsp) != NULL) {
        FixBufInitPut(&rsp, MSG_HEADER_ALIGN_SIZE);
        Status ret = RpcReserveOpHeader(&rsp, NULL, NULL);
        if (ret != GMERR_OK) {
            FixBufRelease(&rsp);
            return rsp;
        }
        // 消息头字段赋值
        MsgHeaderT *head = RpcPeekMsgHeader(&rsp);
        head->protocolVersion = MSG_PROTOCOL_VERSION_PRIVATE;
        head->msgMagicNum = MSG_VERIFY_NUMBER;
        head->opNum = 0;
        head->extendSize = 0;
    }
    return rsp;
}

#ifdef EXPERIMENTAL_NERGC
#define TRY_TIMEOUT_COUNT 5
#define TCP_IP_REJECT 1
static Status ConnPrivFailStat(DrtConnMgrT *connMgr, DrtConnectionT *conn)
{
    DbOamapT *ipMap = &connMgr->ipBanMap.ipFailMap;
    uint32_t *failCount = (uint32_t *)DbOamapLookup(ipMap, DbStrToHash32(conn->ip), &conn->ip, NULL);
    if (failCount == NULL) {
        uint32_t *newFailCount = (uint32_t *)DB_MALLOC(sizeof(uint32_t));
        if (newFailCount == NULL) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "CONN %" PRIu16 "  alloc failCount inv", conn->id);
            return GMERR_OUT_OF_MEMORY;
        }
        *newFailCount = 1;
        return DbOamapInsert(ipMap, DbStrToHash32(conn->ip), &conn->ip, newFailCount, NULL);
    }
    (*failCount)++;
    return GMERR_OK;
}

static void ConnPrivSuccStat(DrtConnMgrT *connMgr, DrtConnectionT *conn)
{
    DbOamapT *ipMap = &connMgr->ipBanMap.ipFailMap;
    uint32_t *failCount = (uint32_t *)DbOamapLookup(ipMap, DbStrToHash32(conn->ip), &conn->ip, NULL);
    if (failCount == NULL) {
        return;
    }
    (*failCount) = 0;
    return;
}

static Status ConnTcpIpStat(DrtConnMgrT *connMgr, DrtConnectionT *conn, Status opStatus)
{
    if (connMgr->ipBanMap.retryTimeout == 0) {
        return GMERR_OK;
    } else if (opStatus == GMERR_INSUFFICIENT_PRIVILEGE) {
        return ConnPrivFailStat(connMgr, conn);
    } else if (opStatus == GMERR_OK) {
        ConnPrivSuccStat(connMgr, conn);
    }
    return GMERR_OK;
}

static Status ConnTcpIpController(DrtConnMgrT *connMgr, DrtConnectionT *conn)
{
    DbOamapT *ipMap = &connMgr->ipBanMap.ipFailMap;
    uint32_t *failCount = (uint32_t *)DbOamapLookup(ipMap, DbStrToHash32(conn->ip), &conn->ip, NULL);
    if (failCount == NULL) {
        return GMERR_OK;
    }
    if (*failCount >= TRY_TIMEOUT_COUNT) {
        DB_LOG_ERROR(
            GMERR_PROGRAM_LIMIT_EXCEEDED, "ip:%s retry exceeds 5 ban, count:%" PRId32 "", conn->ip, *failCount);
        const char *auditUserInfo = (conn != NULL ? conn->auditUserInfo : "");
        DB_LOG_AUDIT(auditUserInfo, "connection", DB_AUDIT_DCL, GMERR_PROGRAM_LIMIT_EXCEEDED,
            "ip:%s retry exceeds 5 ban, count:%" PRId32 "", conn->ip, *failCount);
        // 将失败的ip加入黑名单list
        DbQueueT *ipBanQueue = &connMgr->ipBanMap.ipBanQueue;
        BanIpT banIp = {0};
        (void)memcpy_s(banIp.ip, MAX_IP_ADDR_LEN, conn->ip, MAX_IP_ADDR_LEN);
        banIp.timeStamp = DbRdtsc();
        Status ret = DbQueuePush(ipBanQueue, &banIp);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to append node item to queue.");
            return ret;
        }
        return DbIpControl(conn->ip, TCP_IP_REJECT);
    }
    return GMERR_OK;
}
#endif

Status ConnCompleteCliConnect(DrtInstanceT *drtIns, DrtProcCtxT *procCtx, Status opStatus, ConnPrepareResourceCtxT *ctx)
{
    DB_POINTER3(procCtx, procCtx->conn, ctx);
    DrtConnectionT *conn = procCtx->conn;
    FixBufferT rsp = ConnPrepareRsp(ctx, conn);
    if (FixBufGetBuf(&rsp) == NULL) {
        DrtFreeProcCtx(procCtx);
        if (ctx->subOldMemCtx != NULL) {
            ctx->subOldMemCtx->memType == DB_SHARED_MEMORY ? DbDeleteShmemCtx(ctx->subOldMemCtx) :
                                                             DbDeleteDynMemCtx(ctx->subOldMemCtx);
            ctx->subOldMemCtx = NULL;
        }
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc response, connId: %" PRIu16 ".", conn->id);
        return GMERR_OUT_OF_MEMORY;
    }
    if (opStatus != GMERR_OK) {
        ctx->connRsp = ConnCliConnectRespInit();
    }

    Status ret = ConnCompleteCliConnectInner(conn, opStatus, &rsp, &ctx->connRsp);
    if (ret != GMERR_OK) {
        DrtFreeProcCtx(procCtx);
        DB_LOG_ERROR(ret, "CONN fill conn msg, opStatus: %" PRId32 ", connId: %" PRIu16, (int32_t)opStatus, conn->id);
    }
    ret = DrtCompleteConnect(
        drtIns, procCtx, &rsp, (int32_t)opStatus, ctx->connReq.clientType == GMC_CONN_TYPE_SUB);  // 保证有应答
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "CONN send conn rsp, opstatus: %" PRId32 ", connId: %" PRIu16, (int32_t)opStatus, conn->id);
    }
    if (ctx->subOldMemCtx != NULL) {
        ctx->subOldMemCtx->memType == DB_SHARED_MEMORY ? DbDeleteShmemCtx(ctx->subOldMemCtx) :
                                                         DbDeleteDynMemCtx(ctx->subOldMemCtx);
        ctx->subOldMemCtx = NULL;
    }
#ifdef EXPERIMENTAL_NERGC
    ret = ConnTcpIpController(&drtIns->connMgr, conn);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "CONN ban ip, opstatus: %" PRId32 ", connId: %" PRIu16 ", ip:%s", (int32_t)opStatus, conn->id,
            conn->ip);
    }
#endif
    return ret;
}

static Status CreateSubsDFXInfoMapForSubNode(DrtNodeMgrT *nodeMgr, DrtNodeT *node)
{
    DB_POINTER2(nodeMgr, node);
    if (!DrtIsSubNode(node->nodeId)) {
        return GMERR_OK;
    }
    DB_ASSERT(node->subsInfoMap == NULL);
    DbOamapT *tmpMap = (DbOamapT *)DbDynMemCtxAlloc(nodeMgr->memctx, sizeof(DbOamapT));
    if (tmpMap == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "alloc memory for sub dfx map of connection.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    Status ret = DbOamapInit(tmpMap, 1, DbOamapStringCompare, nodeMgr->memctx, true);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "alloc memory for sub dfx map of connection.");
        DbDynMemCtxFree(nodeMgr->memctx, tmpMap);
        return ret;
    }
    node->subsInfoMap = tmpMap;
    return ret;
}

Status ConnAllocNodeAndSession(CliConnectRequestT *connReq, DrtInstanceT *drtIns, DrtConnectionT *conn,
    CliConnectResponseT *connResp, DrtNodeT **drtNode)
{
    DB_POINTER5(connReq, drtIns, conn, connResp, drtNode);
    DrtNodeT *tmpNode = DrtAllocNodeWithName(&drtIns->nodeMgr, NODE_TYPE_CLIENT, connReq->connName);
    if (tmpNode == NULL) {
        DB_LOG_ERROR(GMERR_TOO_MANY_CONNECTIONS, "CONN alloc drtNode, connId: %" PRIu16 ", name: %s.", conn->id,
            (strlen(connReq->connName) == 0) ? "NULL" : connReq->connName);
        return GMERR_TOO_MANY_CONNECTIONS;
    }
    tmpNode->pid = (int32_t)connReq->pid;
    tmpNode->connId = conn->id;
    tmpNode->nodeId.flag = (uint8_t)((uint32_t)connReq->clientType & 0x1Fu);
    // map生命周期同drt node，申请成功后，在 DrtFreeNode 时释放
    Status ret = CreateSubsDFXInfoMapForSubNode(&drtIns->nodeMgr, tmpNode);
    if (ret != GMERR_OK) {
        DrtFreeNode(&drtIns->nodeMgr, tmpNode->nodeId.nodeId);
        conn->nodeId = DbInvalidNodeId(NODE_TYPE_CLIENT, conn->nodeId.flag);
        return ret;
    }
    connResp->cliId = tmpNode->nodeId.nodeId;
    connResp->version = tmpNode->nodeId.version;
    *drtNode = tmpNode;
    SessionParamT param = {.logThreshold = connReq->logThreshold,
        .rollBackThreshold = connReq->rollBackThreshold,
        .userName = connReq->userName,
        .pwd = connReq->auth};
    Status opStatus = QryAllocSessionForDrt(conn, connResp, drtIns, &param);
#ifdef EXPERIMENTAL_NERGC  // 信息清理
    param.pwd = NULL;
    (void)memset_s(connReq->userName, MAX_USER_NAME_LEN, 0, MAX_USER_NAME_LEN);
    (void)memset_s(connReq->auth, DB_RPC_CONN_AUTH_LEN, 0, DB_RPC_CONN_AUTH_LEN);
#endif
    if (opStatus != GMERR_OK) {
        if (opStatus != GMERR_INSUFFICIENT_PRIVILEGE) {
            DbAlarmUpdateFailCnt(DB_ALARM_CONNECTION_NUMBER, 1);
        }
        *drtNode = NULL;
        DrtFreeNode(&drtIns->nodeMgr, tmpNode->nodeId.nodeId);
        conn->nodeId = DbInvalidNodeId(NODE_TYPE_CLIENT, conn->nodeId.flag);
    }
    return opStatus;
}

// 该接口必定释放request
Status ConnCompleteCltStat(DrtProcCtxT *procCtx, FixBufferT *response, Status opStatus)
{
    DB_POINTER3(procCtx, procCtx->conn, response);
    DrtConnectionT *conn = procCtx->conn;
    CliStatResponseT statResponse = {0};
    statResponse.status = (int32_t)opStatus;
    if (DbIsTcp()) {
        statResponse.logCtrl = *conn->logCtrl;
    }
    Status ret = FixBufPutData(response, (uint8_t *)&statResponse, sizeof(CliStatResponseT));
    if (opStatus != GMERR_OK) {
        (void)PtlServiceSetLastError(response);
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Node put data, nodeId:%" PRIu16, conn->nodeId.nodeId);
    }
    ProtocolPeekFirstOpHeader(response)->opCode = MSG_OP_RPC_HEARTBEAT;
    ret = DrtComplete(procCtx, response, (int32_t)opStatus);  // 保证有应答
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Node send response,nodeId: %" PRIu16, conn->nodeId.nodeId);
    }
    return ret;
}

static void ConnSubBufRingClearBufContentProc(DbBufT str)
{
    FixBufferT buffer;
    if (str.buf == NULL) {
        DB_LOG_WARN(GMERR_UNEXPECTED_NULL_VALUE, "str bufwhen clear subRing.");
        return;
    }
    FixBufInit(&buffer, str.buf, str.len, str.len, 0, NULL);
    QryMsgFailDecRef(&buffer);
}

static void ConnSubBufRingClearBufProc(DbDataT data, DbBufT str)
{
    DB_POINTER(data.ptr);
    FixBufferT buffer;
    FixBufInit(&buffer, str.buf, str.len, str.len, 0, (DbMemCtxT *)data.ptr);
    FixBufRelease(&buffer);
}

#define MAX_CTRL_MSG_SIZE 200

Status ConnAllocSendChannel(DrtInstanceT *drtIns, DrtConnectionT *conn, const CliConnectRequestT *connReq)
{
    DB_POINTER4(drtIns, conn, conn->drtPipe, connReq);
    if (connReq->clientType != GMC_CONN_TYPE_SUB) {
        return GMERR_OK;
    }
    DbSendBufRingFreeCtxT freeCtx = {0};
    freeCtx.freeContentProc = ConnSubBufRingClearBufContentProc;
    freeCtx.freeBufProc = DbIsEulerEnv() ? ConnSubBufRingClearBufProc : NULL;
    freeCtx.ctx.ptr = conn->msgMemCtx;
    Status ret;
    if (!DrtLctrIsTcp(drtIns)) {
        ret = DbShmEntryAllocSndBufRing(&conn->shmEntryCtx, conn->connMgr->shmEntryMgr->shmMemCtx, freeCtx);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    DrtSendAgentT *sa = &drtIns->sendAgent;
    SaDataChannelIdT channelId = QryFindChannelIdByDrtConn(conn);
    DrtDataPlaneT *plane = SaGetDataPlane(sa, channelId.planeId);
    if (plane == NULL) {
        DB_LOG_ERROR(
            GMERR_NO_DATA, "Alloc send channel,  planeId: %" PRIu16 " connId: %" PRIu16, channelId.planeId, conn->id);
        return GMERR_NO_DATA;
    }

    DB_ASSERT(channelId.channelId == conn->id);
    DrtDataPlaneInitParamsT params = {.freeNodeBuffFun = QryMsgFailDecRef,
        .reservedMsgSize = MAX_CTRL_MSG_SIZE,
        .subMsgRingSize = connReq->subMsgRingSize};
    DrtDataSendChannelT *channel = DrtPlaneAllocChannel(plane, channelId.channelId, conn, &params);
    if (channel == NULL) {
        DB_LOG_ERROR(GMERR_NO_DATA, "Alloc connection, planeId: %" PRIu16 ", channelId: %" PRIu16, channelId.planeId,
            channelId.channelId);
        return GMERR_NO_DATA;
    }
    DrtConnFlagSetBindChannel(conn);
    return PtlServiceSwitchToSemBlockedMode(conn);
}

void ConnFreeSendChannel(DrtInstanceT *drtIns, DrtConnectionT *conn)
{
    DB_POINTER2(drtIns, conn);
    if (DrtConnFlagBindChannel(conn)) {
        SaDataChannelIdT channelId = QryFindChannelIdByDrtConn(conn);
        DrtDataPlaneT *plane = SaGetDataPlane(&drtIns->sendAgent, channelId.planeId);
        if (plane == NULL) {
            return;
        }
        DbRWSpinRLock(&plane->lock);
        DrtDataSendChannelT *channel = DrtPlaneGetChannelById(plane, channelId.channelId);
        if (channel != NULL) {
            DrtCloseDataSendChannel(channel, DrtConnAbnormalExit(conn));
        }
        DbRWSpinRUnlock(&plane->lock);
        DrtConnFlagUnsetBindChannel(conn);
    }
}

static void ConnInitShmCtxParam(DbBlockMemParamT *blockParam)
{
    DB_POINTER(blockParam);
    blockParam->isHugePage = false;
    blockParam->baseSize = BLOCK_MEM_BASE_SIZE;
    blockParam->stepSize = BLOCK_MEM_STEP_SIZE;
    blockParam->isReused = false;
    blockParam->allowBigChunk = false;
    blockParam->maxSize = (uint64_t)BLOCK_MEM_MAX_PHY_SIZE;
    blockParam->blkPoolType = BLK_CONN_TYPE;
}

static Status ConnSwitchToNewShmemCtx(
    DrtConnectionT *conn, DbMemCtxT *subsChannelTopMemCtx, ConnPrepareResourceCtxT *ctx)
{
    if (subsChannelTopMemCtx == NULL) {
        DB_LOG_ERROR(GMERR_NO_DATA, "Get subsTopMemCtx, connId %" PRIu16, conn->id);
        return GMERR_NO_DATA;
    }
    // 从订阅通道共享内存根节点上，创建新的子memctx，并进行conn msgMemCtx切换

    char ctxName[CONN_MSG_MEM_CTX_MAX_NAME_LEN] = {0};
    int32_t errCode = snprintf_s(ctxName, CONN_MSG_MEM_CTX_MAX_NAME_LEN, CONN_MSG_MEM_CTX_MAX_NAME_LEN - 1,
        "SubConn %" PRIu16 " shmem context", conn->id);
    if (errCode < 0) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW, "Format ctx name, connId: %" PRIu16, conn->id);
        return GMERR_FIELD_OVERFLOW;
    }

    char param[sizeof(DbBlockMemParamT)] = {0};
    DbBlockMemParamT *blockParam = (DbBlockMemParamT *)param;
    ConnInitShmCtxParam(blockParam);
    AlgoParamT algoParam;
    algoParam.blockParam = blockParam;
    DbMemCtxArgsT argsBlock = {0};
    argsBlock.instanceId = DbGetProcGlobalId();
    argsBlock.algoParam = &algoParam;
    argsBlock.collectAllocSizeOnThisTree = true;

    /* SubConn %u shmem context 说明
        用    途: 订阅连接共享内存通信专用memctx
        生命周期: 连接级别
        释放策略: 产生订阅消息时创建内存，客户端消费后释放
        兜底清空措施: 断连时，销毁该memctx
    */
    DbMemCtxT *subMemCtx = (DbMemCtxT *)DbCreateBlockPoolShmemCtx(subsChannelTopMemCtx, ctxName, &argsBlock);
    if (subMemCtx == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Create new subs memctx, connId %" PRIu16, conn->id);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    ctx->subOldMemCtx = conn->msgMemCtx;
    DrtConnMsgMemCtxSwitchTo(conn, subMemCtx);
    return GMERR_OK;
}

static Status ConnSwitchToNewDynMemCtx(
    DrtConnectionT *conn, DbMemCtxT *subsChannelTopMemCtx, ConnPrepareResourceCtxT *ctx)
{
    if (subsChannelTopMemCtx == NULL) {
        DB_LOG_ERROR(GMERR_NO_DATA, "Get subsTopMemCtx, connId %" PRIu16, conn->id);
        return GMERR_NO_DATA;
    }
    char ctxName[CONN_MSG_MEM_CTX_MAX_NAME_LEN] = {0};
    int32_t errCode = snprintf_s(ctxName, CONN_MSG_MEM_CTX_MAX_NAME_LEN, CONN_MSG_MEM_CTX_MAX_NAME_LEN - 1,
        "SubConn %" PRIu16 " dynmem context", conn->id);
    if (errCode < 0) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW, "Format ctx name, connId: %" PRIu16, conn->id);
        return GMERR_FIELD_OVERFLOW;
    }
    DbMemCtxArgsT args = {0};
    args.dynCtxNeedRecycle = true;
    args.collectAllocSizeOnThisTree = true;
    /* SubConn %u dynmem context 说明
        用    途: 订阅连接通信专用内存上下文
        生命周期: 连接级别
        释放策略: 消息接收时创建，发送结束后释放
        兜底清空措施: 断连时，销毁该memctx
    */
    DbMemCtxT *subMemCtx = DbCreateDynMemCtx(subsChannelTopMemCtx, true, ctxName, &args);
    if (subMemCtx == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Create new subs memctx, connId %" PRIu16, conn->id);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    ctx->subOldMemCtx = conn->msgMemCtx;
    DrtConnMsgMemCtxSwitchTo(conn, subMemCtx);
    return GMERR_OK;
}

static Status PrepareForDirectWriteSub(DrtConnectionT *conn, DrtInstanceT *drtInst, ConnPrepareResourceCtxT *ctx)
{
    if (DbCfgIsTsInstance()) {
        return GMERR_OK;
    }
    int32_t openDirectWrite = DbCfgGetDirectWriteLite();
    if (openDirectWrite == 0) {
        return GMERR_OK;
    }
    Status ret = DirectSharedMsgPoolMgrStart(drtInst);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Start direct share msgPool mgr");
        return ret;
    }
    if (ctx->connReq.clientType == GMC_CONN_TYPE_SYNC) {
        DbMemCtxT *rtTopShmCtx = (DbMemCtxT *)DbGetShmemCtxById(DB_TOP_RT_SHMCTX_ID, drtInst->instanceId);
        if (rtTopShmCtx == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Unsucc to get runtime top shmctx.");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        ShmemPtrT ptr = DbShmemCtxAlloc(rtTopShmCtx, sizeof(DwPubSubShmInfoT));
        DwPubSubShmInfoT *dwPubSubInfo = (DwPubSubShmInfoT *)DbShmPtrToAddr(ptr);
        if (SECUREC_UNLIKELY(dwPubSubInfo == NULL)) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Alloc dwSubInfo, connId %" PRIu16, conn->id);
            return GMERR_MEMORY_OPERATE_FAILED;
        };
        dwPubSubInfo->connVersion = DB_INVALID_UINT16;
        dwPubSubInfo->msgCnt = 0;
        dwPubSubInfo->flowCtrlLevel = 0;
        conn->dwPubSubInfoPtr = ptr;
    }
    return ret;
}

static Status ConnPrepareIpcResource(
    DrtConnectionT *conn, const ConnServiceT *connService, ConnPrepareResourceCtxT *ctx)
{
    Status ret = GMERR_OK;
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    ShmemPtrT msgPoolPtr = conn->connMgr->msgPoolMgr->msgPools[conn->id];
    SharedMsgPoolT *msgPool = (SharedMsgPoolT *)DbShmPtrToAddr(msgPoolPtr);
    if (msgPool == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Open Share msgPool handle");
        return GMERR_INTERNAL_ERROR;
    }
    msgPool->memCtxId = conn->msgMemCtx->ctxId;
#endif
    uint32_t ncId = DbNotifyChanIdGen((uint16_t)connService->drtInstance->instanceId, conn->id);
    if (conn->msgMemCtx->memType == DB_SHARED_MEMORY) {
        ret = DbNotifyChannelCreate(conn->msgMemCtx, ncId, &conn->notifyChannel);
    } else {
        void *rtTopShmCtx = DbGetShmemCtxById(DB_TOP_RT_SHMCTX_ID, DbGetProcGlobalId());
        if (rtTopShmCtx == NULL) {
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        ret = DbNotifyChannelCreate(rtTopShmCtx, ncId, &conn->notifyChannel);
    }
    if (ret != GMERR_OK) {
        DbNotifyChannelClear(&conn->notifyChannel);
        return ret;
    }
    ctx->connRsp.shmNotifyMsgQue = conn->notifyChannel.msgQue;
    ctx->connRsp.shmNotifyChanCtrl = conn->notifyChannel.ctrlInfo->selfPtr;
    ctx->connRsp.shareMsgMgrPtr = connService->drtInstance->drtShareMsgMgr.shareMsgMgr->shareMsgMgrPtr;
    return ret;
}

Status ConnPrepareFoSubConn(DrtConnectionT *conn, const ConnServiceT *connService, ConnPrepareResourceCtxT *ctx)
{
    DB_POINTER(connService->drtInstance);
    Status ret = GMERR_OK;
    if (ctx->connReq.clientType != GMC_CONN_TYPE_SUB) {
        return PrepareForDirectWriteSub(conn, connService->drtInstance, ctx);
    }
    DB_POINTER(conn->msgMemCtx);
    DB_ASSERT(!DrtConnFlagIsResendRsp(conn));
    FixBufRelease(&conn->rspCacheBuf);
    if (conn->msgMemCtx->memType == DB_SHARED_MEMORY) {
        ret = ConnSwitchToNewShmemCtx(conn, connService->subsChannelTopMemCtx, ctx);
    } else {
        // 动态内存，ctx缓存的response内存不能复用，此处删除，避免误用
        FixBufRelease(&ctx->response);
        if ((ret = ConnSwitchToNewDynMemCtx(conn, connService->subsChannelTopMemCtx, ctx)) == GMERR_OK) {
            ret = FixBufCreate(&ctx->response, conn->msgMemCtx, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        }
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    FixBufInit(&conn->rspCacheBuf, NULL, 0, 0, 0, conn->msgMemCtx);
    FixBufInit(&conn->drtPipe->cacheBuf, NULL, 0, 0, 0, conn->msgMemCtx);

    if (conn->connMgr->type != LCTR_TYPE_TCP) {
        ret = ConnPrepareIpcResource(conn, connService, ctx);
    } else {
        ctx->connRsp.shmNotifyMsgQue = DB_INVALID_SHMPTR;
        ctx->connRsp.shmNotifyChanCtrl = DB_INVALID_SHMPTR;
        ctx->connRsp.shareMsgMgrPtr = DB_INVALID_SHMPTR;
    }
    return ret;
}

#ifdef EXPERIMENTAL_NERGC
static Status VerifyUserLoginParam(const CliConnectRequestT *connReqPtr)
{
    // 遍历userName字符数组看有无结束符
    bool validUserNameStr = false;
    for (uint16_t i = 0; i < MAX_USER_NAME_LEN; i++) {
        if (connReqPtr->userName[i] == '\0') {
            validUserNameStr = true;
        }
    }
    if (!validUserNameStr) {
        DB_LOG_ERROR(GMERR_SYNTAX_ERROR, "user name or pwd");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    // 遍历auth字符数组看有无结束符
    bool validAuthStr = false;
    for (uint16_t i = 0; i < DB_RPC_CONN_AUTH_LEN; i++) {
        if (connReqPtr->auth[i] == '\0') {
            validAuthStr = true;
        }
    }
    if (!validAuthStr) {
        DB_LOG_ERROR(GMERR_SYNTAX_ERROR, "user name or pwd");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    size_t userNameLen = strlen(connReqPtr->userName);
    size_t pwdLen = strlen(connReqPtr->auth);
    int32_t userPolicyMode = DbCfgGetInt32Lite(DB_CFG_USER_POLICY_MODE, NULL);
    // step1：可不传userName和pwd
    if (userPolicyMode != USER_POLICY_ENFORCING_MODE) {
        if (userNameLen == 0 || pwdLen == 0) {
            return GMERR_OK;
        }
    }
    // step2：但是如果传入了userName和pwd，则不论 userPolicyMode 取值，都要传对，要校验
    if (pwdLen < NERGC_MIN_PWD_LEN || pwdLen > NERGC_MAX_PWD_LEN || userNameLen < NERGC_MIN_USER_NAME_LEN ||
        userNameLen > NERGC_MAX_USER_NAME_LEN) {
        DB_LOG_ERROR(GMERR_SYNTAX_ERROR, "user name or pwd");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}
#endif

static Status ConnCheckCliConnRequest(DrtConnectionT *conn, CliConnectRequestT *connReqPtr)
{
    if (connReqPtr->cmpntLibNameArrNum > COMPONENT_SO_MAX_NUM) {
        DB_LOG_ERROR(GMERR_PROGRAM_LIMIT_EXCEEDED,
            "Connect stage2 cmpntLibNameArrNum, connId: %" PRIu16 "peerInfo: %" PRIu32 "-%s, cmpntLibNameArrNum: %u.",
            conn->id, conn->pid, conn->auditUserInfo, connReqPtr->cmpntLibNameArrNum);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    if (connReqPtr->clientType >= GMC_CONN_TYPE_BUTT) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE,
            "Connect stage2 client type, connId: %" PRIu16 "peerInfo: %" PRIu32 "-%s, clientType: %u.", conn->id,
            conn->pid, conn->auditUserInfo, connReqPtr->clientType);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
#ifdef EXPERIMENTAL_NERGC
    Status ret = VerifyUserLoginParam(connReqPtr);
    if (ret != GMERR_OK) {
        return ret;
    }
#endif
    return GMERR_OK;
}

static Status ConnGetAndCheckCliConnRequest(DrtConnectionT *conn, DrtProcCtxT *procCtx, ConnPrepareResourceCtxT *ctx)
{
    DB_POINTER2(procCtx, ctx);
    CliConnectRequestT *connReqPtr = (CliConnectRequestT *)FixBufGetData(&procCtx->msg, sizeof(CliConnectRequestT));
    if (connReqPtr == NULL) {
        DrtFreeRequest(conn, &procCtx->msg);
        procCtx->msgHeader = NULL;
        DB_LOG_ERROR(GMERR_NO_DATA, "Connect stage2 get msg data, connId: %" PRIu16 "peerInfo: %" PRIu32 "-%s",
            conn->id, conn->pid, conn->auditUserInfo);
        return GMERR_NO_DATA;
    }
    ctx->connReq = *connReqPtr;
    FixBufMove(&procCtx->msg, &ctx->response);
    procCtx->msgHeader = NULL;
    conn->cltUnique = ctx->connReq.cltUnique;
    int32_t err = strcpy_s(conn->peerThreadName, DB_THREAD_NAME_MAX_LEN, connReqPtr->threadName);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_NO_DATA, "Connect stage2 get cli thread name, connId: %" PRIu16 "peerInfo: %" PRIu32 "-%s",
            conn->id, conn->pid, conn->auditUserInfo);
        return GMERR_FIELD_OVERFLOW;
    }
    Status ret = ConnCheckCliConnRequest(conn, connReqPtr);
    if (ret != GMERR_OK) {
        return ret;
    }
    conn->peerTid = connReqPtr->tid;
    return GMERR_OK;
}

Status ConnSetScheduleWeight(DrtInstanceT *drtIns, DrtConnectionT *conn, uint32_t connWeight)
{
    DB_POINTER(conn);
    Status ret = GMERR_OK;
    if (connWeight == DEFAULT_CONNECTION_WEIGHT) {
        return ret;
    }

    ret = DbCfgVerifyUserPolicyStrongMode();
    if (ret != GMERR_OK) {
        return ret;
    }

    if (connWeight == 0) {
        ret = GMERR_INVALID_PARAMETER_VALUE;
        DB_LOG_ERROR(ret, "connWeight = 0.");
        return ret;
    }

    uint32_t userWeight = DEFAULT_USER_WEIGHT;
    ret = QryGetUserWeight(conn, &userWeight);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get user weight.");
        return ret;
    }
    if (connWeight > userWeight) {
        ret = GMERR_INVALID_PARAMETER_VALUE;
        DB_LOG_ERROR(ret, "Request weight: %" PRIu32 " must not be greater than user weight: %" PRIu32 ".", connWeight,
            userWeight);
        return ret;
    }
    DrtSetConnWeight(drtIns, conn, connWeight);
    return GMERR_OK;
}

Status ConnInitUserProtocolPriv(DrtInstanceT *drtInst, DrtConnectionT *conn, CliConnectResponseT *connResp)
{
    int32_t userPolicyMode = DbCfgGetInt32Lite(DB_CFG_USER_POLICY_MODE, NULL);

    // TCP协议下，不允许直连读
    if (DrtLctrIsTcp(drtInst)) {
        DbShmEntrySetDirectRead(conn->shmEntryCtx.hdl, false);
        connResp->shmEntry = DB_INVALID_SHMPTR;
        connResp->shmEntryVersion = 0;
        return GMERR_OK;
    } else if (userPolicyMode == USER_POLICY_ENFORCING_MODE) {
        // 表示强authentication模式
        // 如果请求是来自工具，并且配置项允许绕过，则绕过校验
        bool isEnable = true;
        Status ret = QryGetUserProtocol(conn, PRIV_PROTOCOL_TYPE_DIRECT_READ, &isEnable);
        if (ret != GMERR_OK && ret != GMERR_UNDEFINED_TABLE) {
            // 导入白名单为group + processname时，查不到连接对应的直连读权限，先给默认允许权限
            return ret;
        }
        DbShmEntrySetDirectRead(conn->shmEntryCtx.hdl, isEnable);
    } else {
        // 非强authentication模式下默认允许直连读
        DbShmEntrySetDirectRead(conn->shmEntryCtx.hdl, true);
    }
    DrtGetConnShmEntryMeta(conn, &connResp->shmEntry, &connResp->shmEntryVersion);
    return GMERR_OK;
}

static void ConnCliReleaseResource(const ConnServiceT *connService, DrtConnectionT *conn, ConnPrepareResourceCtxT *ctx)
{
    if (DbIsTcp() && conn->logCtrl != NULL) {
        DbLogDestroyCtrlItem(conn->logCtrl);
        conn->logCtrl = NULL;
    }
    QryReleaseSessionForDrt(conn);
    if (ctx->node != NULL) {
        DrtFreeNode(&connService->drtInstance->nodeMgr, ctx->node->nodeId.nodeId);
        conn->nodeId = DbInvalidNodeId(NODE_TYPE_CLIENT, conn->nodeId.flag);
    }
    DbMemCtxT *rtTopShmCtx = (DbMemCtxT *)DbGetShmemCtxById(DB_TOP_RT_SHMCTX_ID, connService->drtInstance->instanceId);
    if (!IsShmemPtrEqual(conn->dwPubSubInfoPtr, DB_INVALID_SHMPTR) && !CheckShmPtrZero(conn->dwPubSubInfoPtr)) {
        DbShmemCtxFree(rtTopShmCtx, conn->dwPubSubInfoPtr);
        conn->dwPubSubInfoPtr = DB_INVALID_SHMPTR;
    }

    ConnFreeSendChannel(connService->drtInstance, conn);
    if (DbNotifyChannelIsValid(&conn->notifyChannel)) {
        (void)DbNotifyChannelRelease(&conn->notifyChannel);
    }
    DrtReleaseReserveConnToken(conn);
}

static void ConnFillResponseMsgSetLoadFlag(CliConnectResponseT *connRsp, CliConnectRequestT *connReq)
{
    // 根据 featureNames 配置，判断客户端可以加载哪些so
    for (uint32_t i = 0; i < connReq->cmpntLibNameArrNum; i++) {
        connRsp->cmpntLibLoadFlag[i] = DbDynLoadHasFeature(connReq->cmpntLibNameArr[i]);
    }
}

void ConnFillResponseMsg(
    CliConnectResponseT *connRsp, CliConnectRequestT *connReq, const ConnServiceT *connService, DrtConnectionT *conn)
{
    DB_POINTER3(connRsp, connReq, connService);
    ConnFillResponseMsgSetLoadFlag(connRsp, connReq);
    const char *libPath = DbDynGetFeatureLibDirPath();
    (void)memcpy_s(connRsp->cmpntLibPath, DB_MAX_PATH, libPath, strlen(libPath));

    uint32_t timeInterval[FLOW_CONTROL_TIME_INTERVAL_NUM] = {0};
    DbCfgGetFlowCtrlTimeInterval(timeInterval, FLOW_CONTROL_TIME_INTERVAL_NUM);
    for (uint32_t i = 0; i < FLOW_CONTROL_TIME_INTERVAL_NUM; i++) {
        connRsp->flowCtrlSleepTime[i] = timeInterval[i];
    }
    Status ret = DbGetHungTime(WORKER_HUNG_LEVEL_ONE, &connRsp->firstWorkerHungThreshold);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get first level hung threshold.");
        return;
    }

    connRsp->instanceId = (uint16_t)DrtGetInstanceId(NULL);
    SessionT *session = (SessionT *)conn->session;
    connRsp->rsmCtxPtr = session->rsmCtxPtr;
    if (!DrtLctrIsTcp(connService->drtInstance)) {
        connRsp->serverModeShmPtr = connService->drtInstance->serverMode->serverModeShmPtr;
        // 若缩容管理器已初始化，报文须包含其共享内存指针
        connRsp->dfgmtMgrShmPtr = DbGetDfgmtMgrShmPtr();
#ifdef FEATURE_FASTPATH
        QryAgedMgrT *agedMgr = GetQryAgedMgr();
        if (agedMgr == NULL) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Get aged manager");
            return;
        }
        connRsp->agedMgrLatchShmAddr = agedMgr->latchShmAddr;
        connRsp->agedMgrShmTaskMapShmAddr = agedMgr->shmTaskMapAddr;
#endif
        DbCfgMgrHandleT handle = DbGetCfgHandle(NULL);
        connRsp->cfgShmPtr = handle->cfgForCltShmPtr;
        connRsp->dwPubSubInfoPtr = conn->dwPubSubInfoPtr;
        connRsp->connVersion = conn->version;
        DirectSharedMsgPoolMgrT *directMsgPoolMgr = connService->drtInstance->directMsgPoolMgr;
        if (directMsgPoolMgr != NULL) {
            connRsp->directMsgPoolMgrShmPtr = directMsgPoolMgr->selfPtr;
        }
    } else {
        connRsp->logCtrl = *conn->logCtrl;
    }
}

Status ConnCheckConnName(const DrtConnectionT *conn, const CliConnectRequestT *connReq)
{
    uint16_t connId = DrtGetConnIdByNodeName(conn->connMgr->nodeMgr, connReq->connName);
    if (DbDynArrayValidId(&conn->connMgr->connArr, connId)) {
        DrtConnectionT *existConn = DrtAttachConnById(conn->connMgr, connId);
        if (existConn != NULL) {
            DB_LOG_ERROR(GMERR_PROGRAM_LIMIT_EXCEEDED,
                "Conn name %s exist, connId %" PRIu16 "; exist connId: %" PRIu16 ", flag: %" PRIu32 ", pid %" PRIu32
                ", exit: %" PRIu8,
                connReq->connName, connId, existConn->id, existConn->flag, existConn->pid, existConn->exitStatus);
            DrtDetachConnection(existConn);
        }
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    return GMERR_OK;
}

Status ConnWorkerCreateTask(const ConnServiceT *connService, DrtConnectionT *conn)
{
    SmScheProcCtxT *procCtx = connService->drtInstance->scheMgr.scheProcCtx[conn->id];
    if (procCtx == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Get procCtx, connId %" PRIu16 ".", conn->id);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    // 这里是个防呆条件，理论上，进到这里，必然是第一次申请，id必然无效
    if ((procCtx->workerTaskId == DB_INVALID_ID16) &&
        (connService->drtInstance->workerMgr.scheMode == SCHEDULE_THREAD_POOL)) {
        procCtx->workerTaskId =
            DrtWorkerCreateTask(procCtx->workerMgr, &connService->drtInstance->scheMgr, procCtx, SmScheProcWrapper);
        if (procCtx->workerTaskId == DB_INVALID_ID16) {
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    }
    return GMERR_OK;
}

static Status connTcpResource(DrtConnectionT *conn, ConnPrepareResourceCtxT *ctx)
{
    if (!DbIsTcp()) {
        return GMERR_OK;
    }
    DbLogCtrlItemT *logCtrlItem = DbLogCreateCtrlItemByStr(conn->ip);
    if (logCtrlItem == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Create log ctrl inv.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    conn->logCtrl = logCtrlItem;
#ifdef EXPERIMENTAL_NERGC
    conn->tcpSessionId = DrtGetSessionId();
    ctx->connRsp.sessionId = conn->tcpSessionId;
#endif
    return GMERR_OK;
}

static Status ConnCliPrepareResource4Connect(
    const ConnServiceT *connService, DrtConnectionT *conn, ConnPrepareResourceCtxT *ctx)
{
    Status ret = ConnSetScheduleWeight(connService->drtInstance, conn, ctx->connReq.requestWeight);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = ConnPrepareFoSubConn(conn, connService, ctx);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = ConnAllocSendChannel(connService->drtInstance, conn, &ctx->connReq);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = ConnWorkerCreateTask(connService, conn);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = PtlPrivVerifyServerMode(conn);
    if (ret != GMERR_OK) {
        return ret;
    }

    return connTcpResource(conn, ctx);
}

Status ConnCliPrepareResource(const ConnServiceT *connService, DrtConnectionT *conn, ConnPrepareResourceCtxT *ctx)
{
    DB_POINTER3(connService, conn, ctx);
    Status ret = ConnCheckConnName(conn, &ctx->connReq);
    if (ret != GMERR_OK) {
        /* 校验连接名称合法性，不属于申请连接失败 */
        goto EXIT2;
    }
    ctx->connRsp.connId = conn->id;
    ctx->connRsp.tupleAddrMode = (uint16_t)DmGetAddrMode();
    ctx->connRsp.clientConnectByTcp = DbIsTcp();
    conn->srvMemSize = ctx->connReq.srvMemCtxLimit;
    if ((ret = DrtConnCheckToken(conn, connService->drtInstance->uid, ctx->connReq.useReservedConn)) != GMERR_OK) {
        /* 校验连接token数失败，不属于申请连接失败 */
        goto EXIT2;
    }

    if ((ret = ConnAllocNodeAndSession(&ctx->connReq, connService->drtInstance, conn, &ctx->connRsp, &ctx->node)) !=
        GMERR_OK) {
        /* 校验NODE与SESSION失败，不属于申请连接失败 */
        goto EXIT2;
    }
    ret = ConnCliPrepareResource4Connect(connService, conn, ctx);
    if (ret != GMERR_OK) {
        // 申请连接失败
        goto EXIT1;
    }
    if ((ret = ConnInitUserProtocolPriv(connService->drtInstance, conn, &ctx->connRsp)) != GMERR_OK) {
        /* 权限校验失败，不属于申请连接失败 */
        goto EXIT2;
    }
#ifdef FEATURE_FASTPATH
    // 老化管理器必须在与第一个客户端建连成功前初始化，只初始化一次
    if ((ret = QryCreateAgedService()) != GMERR_OK) {
        goto EXIT2;
    }
#endif
    ConnFillResponseMsg(&ctx->connRsp, &ctx->connReq, connService, conn);
    return GMERR_OK;
EXIT1:
    DbAlarmUpdateFailCnt(DB_ALARM_CONNECTION_NUMBER, 1);
EXIT2:
    /* prepare失败，先释放当前所占的资源，避免并发场景发生的数据滞后 */
    ConnCliReleaseResource(connService, conn, ctx);
    ctx->node = NULL;
    return ret;
}

static void ResetCacheBuf(DrtConnectionT *conn)
{
    DrtConnFlagSetLargeObj(conn);
    conn->msgBuffMaxSize = MSG_LOB_BUFFER_MAX_LENGTH;
    DbMemCtxT *msgMemCtx = (DrtConnFlagIsAsyncLargeObj(conn) && DbIsHpeEnv()) ? conn->connMgr->memctx : conn->msgMemCtx;
    FixBufInit(&conn->drtPipe->cacheBuf, NULL, 0, 0, FIX_BUF_FLAG_LOB_BUFFER, msgMemCtx);
    FixBufInit(&conn->rspCacheBuf, NULL, 0, 0, FIX_BUF_FLAG_LOB_BUFFER, msgMemCtx);
}

// 该接口必将释放req
static Status ConnCliDisconnectFillMsg(
    const DrtConnectionT *conn, const CliDisconnectResponseT *disconnResp, FixBufferT *rsp, Status opStatus)
{
    Status ret = FixBufPutData(rsp, (uint8_t *)&disconnResp, sizeof(CliDisconnectResponseT));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "gen cli disconn resp, connId: %" PRIu16, conn->id);
        return ret;
    }
    if (opStatus != GMERR_OK) {
        ret = PtlServiceSetLastError(rsp);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "CONN set disconn lastErr, connId: %" PRIu16, conn->id);
            return ret;
        }
    }
    return GMERR_OK;
}

static Status ConnCompleteCliDisconnect(DrtProcCtxT *procCtx, FixBufferT *rsp, Status opStatus)
{
    DB_POINTER2(procCtx, procCtx->conn);
    DrtConnectionT *conn = procCtx->conn;
    CliDisconnectResponseT disconnResp = {0};
    disconnResp.status = (int32_t)opStatus;
    Status ret = ConnCliDisconnectFillMsg(conn, &disconnResp, rsp, opStatus);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "CONN fill disconn msg, connId: %" PRIu16, conn->id);
    }
    ret = DrtComplete(procCtx, rsp, (int32_t)opStatus);  // 保证有应答
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "CONN send disconn rsp, connId: %" PRIu16, conn->id);
    }
    return ret;
}

static void DrtLogAbnormalStat(DrtConnectionT *conn)
{
    // 判断异常的条件为，断连时如果有收发失败，则打印error日志
    // 下述打印信息采集方式参考视图DRT_COM_STAT，如视图统计方式修改请同步修改该函数
    DrtPipeT *pipe = conn->drtPipe;
    uint64_t recvFailCnt = 0;
    uint64_t sendFailCnt = 0;
    uint64_t recvCnt = 0;
    uint64_t sendCnt = 0;
    uint64_t recvSucCount = 0;
    uint64_t sendSucCount = 0;
    DrtPipeStatT *stat = &pipe->stat;
    for (uint16_t i = 0; i < DRT_PIPE_STAT_CYCLE_NUMS; i++) {
        recvCnt += stat->cycleStat[i].recvCount;
        sendCnt += stat->cycleStat[i].sendCount;
        recvSucCount += stat->cycleStat[i].recvSucCount;
        sendSucCount += stat->cycleStat[i].sendSucCount;
    }
    recvFailCnt += recvCnt - recvSucCount;
    sendFailCnt += sendCnt - sendSucCount;
    if (recvFailCnt != 0 || sendFailCnt != 0) {
        DB_LOG_WARN(GMERR_CONNECTION_EXCEPTION,
            "CONN, connId %" PRIu16 ", pid %" PRIu32 ", nodeFlag %" PRIu8 ", exitType %" PRIu8 ", recvCnt %" PRIu64
            ", recvSucCount %" PRIu64 ", sendCnt %" PRIu64 ", sendSucCount %" PRIu64
            ", within past three hours, not necessarily a problem.",
            conn->id, conn->pid, (uint8_t)conn->nodeId.flag, (uint8_t)conn->exitStatus, recvCnt, recvSucCount, sendCnt,
            sendSucCount);
    }
}

static void DrtCloseConnectionPrepare(
    ScheduleModeE scheMode, DrtConnectionT *conn, DrtProcCtxT *procCtx, FixBufferT *response)
{
    DB_POINTER3(conn, procCtx, response);
    switch (conn->exitStatus) {
        case CONN_EXIT_NONE:
        case CONN_EXIT_NORMAL:
        case CONN_EXIT_ABNORMAL_PACKET:  // 异常报文 走正常断连逻辑，why from lxch？
            DrtConnCloseNormal(conn);
            FixBufferT *request = &procCtx->msg;
            DrtAllocResponse(conn, request, response);
            break;
        case CONN_EXIT_ABNORMAL:
        case CONN_EXIT_TIMEOUT:
            DrtConnCloseAbnormal(conn);
            DB_LOG_WARN(GMERR_CONNECTION_EXCEPTION,
                "CONN, connId: %" PRIu16 ", pid: %" PRIu32 ", exitType: %" PRIu8 ", ref: %" PRIu32, conn->id, conn->pid,
                (uint8_t)conn->exitStatus, conn->ref);
            break;
        case CONN_EXIT_ABNORMAL_WITH_PREPARE_STAT:
        case CONN_EXIT_BUTT:
        default:
            break;
    }
}

static void DrtLogCloseConnection(DrtConnectionT *conn, DrtProcCtxT *procCtx, Status ret)
{
    DB_POINTER2(conn, procCtx);
    switch (conn->exitStatus) {
        case CONN_EXIT_NONE:
        case CONN_EXIT_NORMAL:
            DB_LOG_AUDIT(conn->auditUserInfo, "connection", DB_AUDIT_DCL, ret, "Disconnection succeed.");
            break;
        case CONN_EXIT_ABNORMAL:
            DB_LOG_AUDIT(conn->auditUserInfo, "connection", DB_AUDIT_DCL, ret, "Exception Disconnection succeed.");
            // auditUserInfo assigned in RaGetUserInfoFromPipe
            DB_LOG_ERROR(GMERR_CONNECTION_RESET_BY_PEER,
                "CONN, client: %s, isExist: %" PRIu8 ", connId: %" PRIu16 ", pid: %" PRIu32 ", nodeFlag: %" PRIu8
                ", exitType: %" PRIu8 "(PID may be reused).",
                (conn->auditUserInfo), DbAdptProcessIsExist(conn->pid), conn->id, conn->pid, (uint8_t)conn->nodeId.flag,
                (uint8_t)conn->exitStatus);
            break;
        case CONN_EXIT_TIMEOUT:
            DB_LOG_AUDIT(conn->auditUserInfo, "connection", DB_AUDIT_DCL, ret, "Timeout Disconnection succeed.");
            DB_LOG_ERROR(GMERR_CONNECTION_TIMED_OUT,
                "CONN, connId: %" PRIu16 ", pid: %" PRIu32 ", nodeFlag: %" PRIu8 ", exitType: %" PRIu8, conn->id,
                conn->pid, (uint8_t)conn->nodeId.flag, (uint8_t)conn->exitStatus);
            break;
        case CONN_EXIT_ABNORMAL_PACKET:  // 异常报文 走正常断连逻辑
            DB_LOG_AUDIT(
                conn->auditUserInfo, "connection", DB_AUDIT_DCL, ret, "Exception Packet Disconnection succeed.");
            DB_LOG_ERROR(GMERR_CONNECTION_EXCEPTION,
                "CONN, connId %" PRIu16 ", pid %" PRIu32 ", nodeFlag %" PRIu8 ", exitType %" PRIu8 ".", conn->id,
                conn->pid, (uint8_t)conn->nodeId.flag, (uint8_t)conn->exitStatus);
            break;
        case CONN_EXIT_ABNORMAL_WITH_PREPARE_STAT:
        case CONN_EXIT_BUTT:
        default:
            DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE,
                "CONN, connId: %" PRIu16 ", pid: %" PRIu32 ", nodeFlag: %" PRIu8 ", exitType: %" PRIu8 ".", conn->id,
                conn->pid, (uint8_t)conn->nodeId.flag, (uint8_t)conn->exitStatus);
    }
    // 判断断连时连接是否是异常的，是异常的打印error日志
    DrtLogAbnormalStat(conn);
    if (ret == GMERR_OK) {
        DB_LOG_INFO("CONN, connId: %" PRIu16 ", nodeId: %" PRIu16 ", pid: %" PRIu32 ", UserInfo: %s, nodeFlag: %" PRIu8
                    ", exitType: %" PRIu8 ".",
            conn->id, conn->nodeId.nodeId, conn->pid, conn->auditUserInfo, (uint16_t)conn->nodeId.flag,
            (uint8_t)conn->exitStatus);
    }
}

#if defined(FEATURE_TS) || defined(FEATURE_SQL)
void ConnCommonConnExitProc(DrtConnectionT *conn);
#endif

static void CloseConnection(ScheduleModeE scheMode, DrtConnectionT *conn, DrtProcCtxT *procCtx)
{
    DB_POINTER2(conn, procCtx);
    FixBufferT response = {0};
    Status ret = GMERR_OK;
#if defined(FEATURE_TS) || defined(FEATURE_SQL)
    if (DbCfgIsTsInstance()) {
        ConnCommonConnExitProc(conn);
    }
#endif
    // step1: del event, set to closing
    DrtCloseConnectionPrepare(scheMode, conn, procCtx, &response);
    // step3: ack
    if (!FixBufIsEmpty(&response)) {
        ret = ConnCompleteCliDisconnect(procCtx, &response, GMERR_OK);
        if (ret != GMERR_OK) {
            DB_LOG_AUDIT(conn->auditUserInfo, "connection", DB_AUDIT_DCL, ret,
                "Disconnect succeed but unable to send a response.");
        }
    }
    conn->setClosingTime = DbRdtsc();
    DrtLogCloseConnection(conn, procCtx, ret);
}

Status ConnCliDisconnect(const ConnServiceT *connService, const MsgHeaderT *msgHeader, DrtProcCtxT *procCtx)
{
    DB_POINTER3(connService, procCtx, procCtx->conn);
    Status ret = GMERR_OK;
    DrtConnectionT *conn = procCtx->conn;
    bool abnormalExit = (msgHeader == NULL);
    if (!abnormalExit) {
        conn->exitStatus = (uint8_t)CONN_EXIT_NORMAL;
    } else if (!DrtConnTimeoutExit(conn) && !DrtConnAbnormalPacketExit(conn)) {
        conn->exitStatus = (uint8_t)CONN_EXIT_ABNORMAL;
    }
    DrtCheckConnAlarmForceRecover(conn);
    char nodeName[MAX_CONN_NAME_LEN] = {0};
    ret = DrtGetNodeNameByNodeId(&connService->drtInstance->nodeMgr, conn->nodeId.nodeId, nodeName, MAX_CONN_NAME_LEN);
    if (ret == GMERR_OK) {
        // 后续考虑移到DrtFreeConnection，老代码暂不改动
        uint8_t nodeFlag = DrtGetFlagByNodeId(&connService->drtInstance->nodeMgr, conn->nodeId.nodeId);
        if (nodeFlag == (uint8_t)GMC_CONN_TYPE_SUB) {
            ret = QryReleaseSubsChannelRes(nodeName, NULL);
        }
    }
    DrtFreeNode(&connService->drtInstance->nodeMgr, conn->nodeId.nodeId);
    conn->nodeId = DbInvalidNodeId(NODE_TYPE_CLIENT, conn->nodeId.flag);
    QryReleaseSessionForDrt(conn);
    DrtReleaseReserveConnToken(conn);
    // 清理连接上的TLS信息
    DbCleanGlobalTlsArray(conn->threadPoolTlsArray);
    CloseConnection(connService->drtInstance->scheMgr.scheMode, conn, procCtx);
    // 需要先close conn, 再 close channel, 保证断连的resp 已经放到send queue
    ConnFreeSendChannel(connService->drtInstance, conn);
    return ret;
}

static void LogAfterConnectFailure(DrtConnectionT *conn, const char *connName, Status prepareRet, Status stage2Ret)
{
    DB_POINTER2(conn, connName);
    if (prepareRet != GMERR_OK) {
        DB_LOG_AUDIT(conn->auditUserInfo, "connection", DB_AUDIT_DCL, prepareRet,
            "Client connects unsucc due to unable alloc node or session.");
        DB_LOG_ERROR(prepareRet,
            "connect stage2 alloc drtNode or session, connId: %" PRIu16 ", peerInfo: %" PRIu32
            "-%s, conn name %s, use num: %" PRIu16 ", reserve num: %" PRIu16 ", use reserve num: %" PRIu16
            ", alloc num: %" PRIu16 ", max num: %" PRIu16 ", cur num: %" PRIu16 ", use emrg num: %" PRIu16 ".",
            conn->id, conn->pid, conn->auditUserInfo, (strlen(connName) == 0) ? "NULL" : connName,
            DbDynArrayGetItemNum(&conn->connMgr->connArr), conn->connMgr->reservedNum, conn->connMgr->useReservedNum,
            DbDynArrayGetAllocNum(&conn->connMgr->connArr), DbDynArrayGetMaxNum(&conn->connMgr->connArr),
            conn->connMgr->curNum, conn->connMgr->useEmrgNum);
    } else {
        DB_LOG_AUDIT(conn->auditUserInfo, "connection", DB_AUDIT_DCL, stage2Ret,
            "Client connects unsucc due to unable generate or send response.");
        DB_LOG_ERROR(stage2Ret, "connect stage2 send rsp, conn: %" PRIu16 "peerInfo: %" PRIu32 "-%s", conn->id,
            conn->pid, conn->auditUserInfo);
    }
}

Status ConnCreateAlarmNode(DrtConnectionT *conn)
{
    DB_POINTER(conn);
    DbAlarmIdE alarmId;
    if (DrtIsAsyncConn(conn)) {
        alarmId = DB_ALARM_ASYNC_CONN_RING;
    } else if (DrtIsSubConn(conn)) {
        alarmId = DB_ALARM_SUB_CONN_RING;
    } else {
        return GMERR_OK;  // 非async、sub类型，不用创建alarm node，返回OK
    }

    Status ret = DbCreatNonGlobalAlarmNode(alarmId, conn->id);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret,
            "connect stage3 create alarm node, alarmId: %" PRIu16 ", connType: %" PRIu8 " connId: %" PRIu16
            ", peerInfo: %" PRIu32 "-%s",
            (uint16_t)alarmId, conn->nodeId.flag, conn->id, conn->pid, conn->auditUserInfo);
        return ret;
    }
    conn->alarmInfoNode = DbGetAlarmNode(alarmId, conn->id);
    DB_POINTER(conn->alarmInfoNode);
    conn->alarmId = alarmId;
    return GMERR_OK;
}

static void LogAfterConnSucc(const DrtConnectionT *conn, Status stage2Ret, const char *connName)
{
    DB_LOG_AUDIT(conn->auditUserInfo, "connection", DB_AUDIT_DCL, stage2Ret, "Client connects succeed.");
    DB_LOG_INFO("CONN client connect succeed, connId: %" PRIu16 ", nodeId: %" PRIu16 ", node name: %s.", conn->id,
        conn->nodeId.nodeId, (strlen(connName) == 0) ? "NULL" : connName);
}

static void ConnGetCliRequestFail(DrtConnectionT *conn, Status ret)
{
    DB_LOG_AUDIT(conn->auditUserInfo, "connection", DB_AUDIT_DCL, ret,
        "Client connects unsucc due to unable get client conn request.");
    DrtUnMonitorPipe(conn->drtPipe);
    DrtSetConnStatus(conn, CONN_STATUS_CLOSING);
}

static void ConnReleaseResource(
    const ConnServiceT *connService, DrtConnectionT *conn, DrtProcCtxT *procCtx, ConnPrepareResourceCtxT *ctx)
{
    uint16_t nodeId = conn->nodeId.nodeId;
    uint16_t status = (uint16_t)conn->status;
    if (conn->nodeId.nodeId != DB_INVALID_ID16) {
        (void)ConnCliDisconnect(connService, NULL, procCtx);
    }
    FixBufRelease(&ctx->response);
    DB_LOG_ERROR(GMERR_CONNECTION_RESET_BY_PEER,
        "connect stage2, connId: %" PRIu16 ", status: %" PRIu16 ", nodeId %" PRIu16 ", peerInfo: %" PRIu32
        "-%s, ref: %" PRIu32 ".",
        conn->id, status, nodeId, conn->pid, conn->auditUserInfo, conn->ref);
}

static Status ConnCliConnect(const ConnServiceT *connService, DrtProcCtxT *procCtx)
{
    DB_POINTER4(connService, connService->drtInstance, procCtx, procCtx->conn);

    DrtConnectionT *conn = procCtx->conn;
    ConnPrepareResourceCtxT ctx = {0};
    Status prepareRet = ConnGetAndCheckCliConnRequest(conn, procCtx, &ctx);
    if (prepareRet != GMERR_OK) {
        ConnGetCliRequestFail(conn, prepareRet);
        return prepareRet;
    }
    conn->cltTimeoutMs = ctx.connReq.timeoutMs;

    ctx.connRsp = ConnCliConnectRespInit();
    if (conn->status == CONN_STATUS_PREPARE) {
        prepareRet = ConnCliPrepareResource(connService, conn, &ctx);
        conn->nodeId = ctx.node == NULL ? DbInvalidNodeId(NODE_TYPE_CLIENT, conn->nodeId.flag) : ctx.node->nodeId;
#ifdef EXPERIMENTAL_NERGC
        ConnTcpIpStat(&connService->drtInstance->connMgr, conn, prepareRet);
#endif
    } else {
        ConnReleaseResource(connService, conn, procCtx, &ctx);
        return GMERR_CONNECTION_RESET_BY_PEER;
    }

    if (ctx.connReq.clientType == GMC_CONN_TYPE_ASYNC) {
        DrtConnFlagSetAsyncObj(conn);
    }
    if (ctx.connReq.isLobConn) {
        ResetCacheBuf(conn);
    }

    // ConnCompleteCliConnect 不仅仅是回应答了，做了二阶段分发，涉及资源申请
    Status stage2Ret = ConnCompleteCliConnect(connService->drtInstance, procCtx, prepareRet, &ctx);

    const char *connName = ctx.connReq.connName;
    if (prepareRet != GMERR_OK || stage2Ret != GMERR_OK) {
        LogAfterConnectFailure(conn, connName, prepareRet, stage2Ret);
        DrtUnMonitorPipe(conn->drtPipe);
        DrtSetConnStatus(conn, CONN_STATUS_CLOSING);
        /* prepareRet成功时，没有释放资源 */
        if (prepareRet == GMERR_OK) {
            ConnCliReleaseResource(connService, conn, &ctx);
        }
        return prepareRet != GMERR_OK ? prepareRet : stage2Ret;
    }

    Status ret = ConnCreateAlarmNode(conn);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbAlarmUpdateSuccCnt(DB_ALARM_CONNECTION_NUMBER, 1);
    LogAfterConnSucc(conn, stage2Ret, connName);
    DrtConnFlagSetConnComplete(conn);
    return GMERR_OK;
}

static Status ConnCltStatRsp(DrtProcCtxT *procCtx, Status opStatus)
{
    DB_POINTER2(procCtx, procCtx->conn);
    DrtConnectionT *conn = procCtx->conn;
    // 申请、发送应答
    FixBufferT response = {0};
    FixBufferT *request = &procCtx->msg;
    DrtAllocResponse(conn, request, &response);

    Status ret = ConnCompleteCltStat(procCtx, &response, opStatus);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Send client statistics response, nodeId: %" PRIu16, conn->nodeId.nodeId);
    }
    return ret;
}

DrtConnectionT *ConnGetConnById(uint16_t connId)
{
    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    if (drtIns == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Get DRT instance worthless in heart beat proc.");
        return NULL;
    }
    DrtConnectionT *conn = DrtAttachConnById(&drtIns->connMgr, connId);
    if (conn == NULL) {
        DB_LOG_WARN_AND_SET_LASTERR(
            GMERR_NULL_VALUE_NOT_ALLOWED, "worthless to get connection by connId=%" PRId16 ".", connId);
    }
    return conn;
}

// 更新客户端上报的dfx数据
Status ConnCltStat(const ConnServiceT *connService, DrtProcCtxT *procCtx)
{
    DB_POINTER3(connService, procCtx, procCtx->conn);
    DrtConnectionT *conn = procCtx->conn;
    FixBufferT *request = &procCtx->msg;
    // 解析req数据
    CltConnDfxInfoT *cltDfxReq = (CltConnDfxInfoT *)FixBufGetData(request, sizeof(CltConnDfxInfoT));
    if (cltDfxReq == NULL) {
        DrtFreeRequest(conn, request);
        DrtFreeProcCtx(procCtx);
        DB_LOG_ERROR(GMERR_NO_DATA, "Get request data, nodeId: %" PRIu16, conn->nodeId.nodeId);
        return GMERR_NO_DATA;
    }
    DrtConnectionT *targetConn = ConnGetConnById((uint16_t)cltDfxReq->remoteId);
    // 连接可能已经被释放掉并且被其他连接申请到了，这种情况下返回OK，不再更新视图
    if (targetConn == NULL || (!DbIsTcp() && DbShmEntryGetVersion(targetConn->shmEntryCtx.hdl) != cltDfxReq->version)) {
        DB_LOG_INFO("conn may be release when update heart beat data %" PRIu16, cltDfxReq->remoteId);
        DrtFreeRequest(conn, request);
        DrtFreeProcCtx(procCtx);
        if (targetConn != NULL) {
            DrtDetachConnection(targetConn);
        }
        return GMERR_OK;
    }
    // 更新客户端dfx信息
    QryUpdateSessionCltDfxInfo(targetConn, cltDfxReq);
    // 客户端收发消息遗留问题，因此同步连接暂不发心跳应答
    uint8_t cltType = targetConn->nodeId.flag;
    CltUpdateSysView(request, cltType, targetConn);
    DrtDetachConnection(targetConn);
    if (conn->nodeId.flag == (uint8_t)GMC_CONN_TYPE_SYNC) {
        DrtFreeRequest(conn, request);
        DrtFreeProcCtx(procCtx);
        return GMERR_OK;
    }
    return ConnCltStatRsp(procCtx, GMERR_OK);
}

static Status ConnShmExpand(FixBufferT *request, DrtProcCtxT *procCtx)
{
    uint32_t usedCnt = 0;
    Status ret = FixBufGetUint32(request, &usedCnt);
    if (ret == GMERR_OK) {
        DbSessionCtxT *sessionCtx;
        if (procCtx == NULL || procCtx->conn == NULL || procCtx->conn->session == NULL) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Expand client share memory.");
            ret = GMERR_UNEXPECTED_NULL_VALUE;
        } else {
            SeGetResSessionCtx(((SessionT *)procCtx->conn->session)->seInstance, &sessionCtx);
            ret = DbShmRefArrayExpand(sessionCtx, usedCnt);
        }
    } else {
        DB_LOG_ERROR(ret, "Get fixBuf usedCnt");
    }
    // 回响应消息
    return ConnCltStatRsp(procCtx, ret);
}

// 自调度异常断链消息，是一个空消息，详见 RaGenerateSelfScheDisconnectMsg RaScheMsg
bool IsSelfScheDisconnectMsg(const DrtProcCtxT *procCtx)
{
    DB_POINTER(procCtx);
    return (procCtx->msgHeader == NULL);
}

void ConnServiceSeekOpMsg(FixBufferT *request, uint32_t opCode)
{
    DB_POINTER(request);
    if (opCode == MSG_OP_RPC_DISCONNECT) {
        return;
    }
    RpcSeekFirstOpMsg(request);
}

Status ConnServiceVerifyServerMode(DrtConnectionT *conn, uint32_t opCode)
{
    if (opCode == MSG_OP_RPC_DISCONNECT || opCode == MSG_OP_RPC_CONNECT) {
        return GMERR_OK;
    }
    // 非断连请求需要校验服务端模式，连接请求在申请session后校验权限
    return PtlPrivVerifyServerMode(conn);
}

void ConnServiceInvalidOpCode(DrtProcCtxT *procCtx)
{
    FixBufferT response = {0};
    FixBufMove(&procCtx->msg, &response);
    Status ret = DrtComplete(procCtx, &response, GMERR_DATATYPE_MISMATCH);  // 异常报文返回应答
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Connect send rsp, connId: %" PRIu16 ".", procCtx->conn->id);
    }
}

Status ConnServiceEntry(
    DrtServiceCtxT *serviceCtx, DrtProcCtxT *procCtx, uint16_t serviceId, uint32_t opCode, void *userCtx)
{
    DB_POINTER4(serviceCtx, serviceCtx->ctx, procCtx, procCtx->conn);
    MsgHeaderT *msgHeader = procCtx->msgHeader;
    FixBufferT *request = &procCtx->msg;
    DrtConnectionT *conn = procCtx->conn;
    // 此处msgHeader不为空，则request应该为正常消息，RpcPeekFirstOpHeader必须成功
    opCode = IsSelfScheDisconnectMsg(procCtx) ? MSG_OP_RPC_DISCONNECT : opCode;
    ConnServiceT *connService = (ConnServiceT *)serviceCtx->ctx;

    Status ret = ConnServiceVerifyServerMode(conn, opCode);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "connect stage2 verify server mode with op: %" PRIu32, opCode);
        DrtFreeMsg(&procCtx->msg);
        DrtFreeProcCtx(procCtx);
        return ret;
    }
    ConnServiceSeekOpMsg(request, opCode);
    CliOpInfoT cliOpInfo = {.pid = conn->pid, .tid = conn->peerTid, .opCode = MSG_OP_RPC_CONNECT};
    switch (opCode) {
        case MSG_OP_RPC_CONNECT:
            DbPrintServiceEntryThreadRunTime("begin", "ConnServiceEntry", &cliOpInfo, conn->peerThreadName);
            ret = ConnCliConnect(connService, procCtx);
            DbPrintServiceEntryThreadRunTime("end", "ConnServiceEntry", &cliOpInfo, conn->peerThreadName);
            break;
        case MSG_OP_RPC_DISCONNECT:
            ret = ConnCliDisconnect(connService, msgHeader, procCtx);
            break;
        case MSG_OP_RPC_HEARTBEAT:
            ret = ConnCltStat(connService, procCtx);
            break;
        case MSG_OP_RPC_CLT_SHM_EXPAND:
            ret = ConnShmExpand(request, procCtx);
            break;
        default:
            DB_LOG_ERROR(GMERR_DATATYPE_MISMATCH,
                "Connect service can not process opcode %" PRIu32 ", connId %" PRIu16 ".", opCode, conn->id);
            ConnServiceInvalidOpCode(procCtx);
            return GMERR_DATATYPE_MISMATCH;
    }
    return ret;
}

void ConnDestroySubPubPlanes(ConnServiceT *service)
{
    DB_POINTER(service);
    for (uint16_t i = 0; i < service->subPubPlaneNum; i++) {
        SaFreeDataPlane(&service->drtInstance->sendAgent, service->subPubplanes[i]);
        service->subPubplanes[i] = NULL;
    }
    service->subPubPlaneNum = 0;
    QrySetSubPubPlaneChannels(0, NULL, NULL);
}

// 当前只有一个data plane，因此这里每个plan上的订阅通道个数和最大连接数相同
#define MAX_SUB_PUB_CHANNELS_NUM_PER_PLANE MAX_CONN_NUM

Status ConnAllocSubPubPlanes(ConnServiceT *service)
{
    DB_POINTER2(service, service->drtInstance);

    service->subPubPlaneNum = 0;
    uint32_t maxConnNum = (uint32_t)DbCfgGetInt32Lite(DB_CFG_CONN_MAX, NULL);
    uint16_t planeNum = (uint16_t)(maxConnNum / MAX_SUB_PUB_CHANNELS_NUM_PER_PLANE);
    if (maxConnNum % MAX_SUB_PUB_CHANNELS_NUM_PER_PLANE != 0) {
        planeNum++;
    }
    planeNum = DB_MIN(planeNum, (uint16_t)MAX_DATA_PLANE_NUM);
    if (planeNum == 0) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Data plane num is zero");
        return GMERR_DATA_EXCEPTION;
    }
    uint16_t channelNumPerPlane = ((uint16_t)maxConnNum) / planeNum;
    if (maxConnNum % planeNum != 0) {
        channelNumPerPlane++;
    }
    uint16_t chanId[MAX_DATA_PLANE_NUM];
    DrtDataPlaneChannelRangeT chanRange[MAX_DATA_PLANE_NUM];
    for (uint16_t i = 0; i < planeNum; i++) {
        chanRange[i].minChannelId = (uint16_t)(channelNumPerPlane * i);
        chanRange[i].maxChannelId = (uint16_t)((chanRange[i].minChannelId + channelNumPerPlane) - 1);
        service->subPubplanes[i] = SaAllocDataPlane(&service->drtInstance->sendAgent, chanRange[i]);
        if (service->subPubplanes[i] == NULL) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "CONN alloc sub plane %" PRIu16, i);
            ConnDestroySubPubPlanes(service);
            return GMERR_OUT_OF_MEMORY;
        }
        chanId[i] = service->subPubplanes[i]->planeId;
        service->subPubPlaneNum++;
    }
    QrySetSubPubPlaneChannels(planeNum, chanId, chanRange);
    return GMERR_OK;
}

#if defined(FEATURE_TS) || defined(FEATURE_SQL)
void ConnCommonConnExitProc(DrtConnectionT *conn)
{
    char filepath[PATH_MAX];
    GetTempFileDirForLargeResult(filepath);

    uint64_t id = (conn->id == 0) ? DbThreadGetSelfId() : (uint64_t)conn->id;
    char idString[PATH_MAX] = "";
    sprintf_s(idString, sizeof(idString), "%" PRIu64, id);
    if (strcat_s(filepath, PATH_MAX, idString) != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "can not construct file path");
        return;
    }
    if (!DbFileExist(filepath)) {
        return;
    }
    (void)DbRemoveFile(filepath);
    DB_LOG_WARN(GMERR_OK, "finish remove of temp file %s.", filepath);
}
#endif

static void ConnRegLastAbnConnExitProc(DrtConnectionT *conn)
{
    // do not lock conn again
    DB_POINTER(conn);
    CataReleaseShmMetaByPid(DbGetInstanceByMemCtx(conn->msgMemCtx), conn->pid);
#if defined(FEATURE_TS) || defined(FEATURE_SQL)
    if (DbCfgIsTsInstance()) {
        ConnCommonConnExitProc(conn);
    }
#endif
}

Status ConnInitService(ConnServiceT *service, DrtInstanceT *drtInstance)
{
    DB_POINTER2(service, drtInstance);
    if (DbIsMultiInstanceEnabled()) {
        return GMERR_OK;
    }

    service->drtInstance = drtInstance;
    service->subsChannelTopMemCtx =
        (DbCommonGetEnv() == ADPT_HPE) ? drtInstance->rtMsgTopShareMemCtx : drtInstance->rtMsgTopDynMemCtx;
    DrtRegLastAbnConnExit(drtInstance, ConnRegLastAbnConnExitProc);
#if defined(FEATURE_TS) && !defined(FEATURE_STREAM)
    if (!DbCfgIsTsInstance()) {
#endif
        Status ret = ConnAllocSubPubPlanes(service);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "CONN create sub-pub data planes");
            return ret;
        }
#if defined(FEATURE_TS) && !defined(FEATURE_STREAM)
    }
#endif
    SrvRegister(SrvGetServiceMgr(), DRT_SERVICE_CONN, ConnServiceEntry, service);
    return GMERR_OK;
}

void ConnDestroyService(ConnServiceT *service)
{
    DB_POINTER(service);
    SrvUnRegister(SrvGetServiceMgr(), DRT_SERVICE_CONN);
    ConnDestroySubPubPlanes(service);
    service->drtInstance = NULL;
}
#ifdef __cplusplus
}
#endif /* __cplusplus */
