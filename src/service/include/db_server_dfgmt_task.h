/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * File Name: db_server_dfgmt_task.h
 * Description: head file for server defragment task worker
 * Create: 2021-4-19
 */

#ifndef DB_SRV_DFGMT_TASK_H
#define DB_SRV_DFGMT_TASK_H

#include "db_storage_dfgmt.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

GMDB_EXPORT Status DbSrvDfgmtTaskMgrInit(uint16_t *workerId);
// 获取dfx统计信息接口
GMDB_EXPORT Status DbSrvGetDfgmtTaskStat(MemTaskTypeE taskType, DfgmtTaskStatT *taskStat);

// for ut stub
GMDB_EXPORT void DbSrvGetDfgmtCfg(int32_t *isEnable);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
