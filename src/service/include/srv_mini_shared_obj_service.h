/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: service used for mini shared object
 * Author: liufuchenxing
 * Create: 2024-02-20
 */

#ifndef SRV_MINI_SHARED_OBJ_SERVICE_H
#define SRV_MINI_SHARED_OBJ_SERVICE_H

#include "ee_mini_base.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

Status SharedObjServiceEntry(MiniSessionT *session);
Status SharedObjRunCtxOpen(MiniSessionT *session);
void SharedObjRunCtxClose(MiniSessionT *session);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* SRV_MINI_SHARED_OBJ_SERVICE_H */
