/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: the impl for gme sql api
 * Author: SQL
 * Create: 2024-01-29
 */

#include "gme_api.h"
#include "gme_register.h"
#include "srv_emb_sql_com.h"
#include "db_server.h"
#include "srv_log.h"
#include "db_instance.h"
#include "srv_data_public.h"

#include "gme_sql_api.h"

#include <ctype.h>
#include "srv_data_service.h"
#include "ee_stmt_fusion.h"

Status EmbSqlAllocSession(DbMemCtxT *memCtx, struct TagEmbSession **embSession)
{
    Status ret;
    struct TagEmbSession *embSessionTmp = DbDynMemCtxAlloc(memCtx, sizeof(struct TagEmbSession));
    if (embSessionTmp == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    SessionT *session = NULL;
    ret = QrySessionAlloc(SESSION_TYPE_WITH_CONN, SESSION_NO_DYN_MEM_LIMIT, DbGetInstanceByMemCtx(memCtx), &session);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbOamapInit(&embSessionTmp->stmtMap, 0, DbOamapPtrCompare, memCtx, true);
    if (ret != GMERR_OK) {
        QrySessionRelease(session);
        return ret;
    }
    embSessionTmp->session = session;
    (*embSession) = embSessionTmp;
    return GMERR_OK;
}

static Status EmbSqlCreateConnect(DbInstanceT *instance, DbMemCtxT *memCtx, GmeConnT **conn)
{
    Status ret;
    GmeConnT *connTmp = DbDynMemCtxAlloc(memCtx, sizeof(GmeConnT));
    if (connTmp == NULL) {
        EMB_ERROR_LOG(GMERR_MEMORY_OPERATE_FAILED, "Embedded sql unable to alloc memory for gme connection.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    connTmp->conMemCtx = memCtx;
    ret = EmbSqlAllocSession(memCtx, &connTmp->embSession);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(memCtx, connTmp);
        EMB_ERROR_LOG(ret, "Embedded sql unable to alloc memory for emb connection.");
        return ret;
    }
    *conn = connTmp;
    return GMERR_OK;
}

void EmbSqlStmtMapDestroy(GmeConnT *conn)
{
    DB_POINTER(conn);
    DbOamapIteratorT iter = 0;
    void *key = NULL;
    EmbSqlStmtValueT *value = NULL;
    while (DbOamapFetch(&conn->embSession->stmtMap, &iter, &key, (void *)&value) == GMERR_OK) {
        Status ret = value->destroyStmt(key);
        if (ret != GMERR_OK) {
            EMB_ERROR_LOG(ret, "Embedded can not destroy stmt.");
            return;
        }
    }
    DbOamapDestroy(&conn->embSession->stmtMap);
    return;
}

static Status GmeGlobalServerStart(void)
{
    Status ret = GMERR_OK;
    DbInstanceT *instGlobal = DbGetGlobalInstance();

    if (instGlobal->status == DB_INSTANCE_ONLINE) {
        return GMERR_OK;
    }

    DbRWLatchW(&instGlobal->latch);

    if (instGlobal->status == DB_INSTANCE_ONLINE) {
        DbRWUnlatchW(&instGlobal->latch);
        return GMERR_OK;
    }

    ret = RuntimeModuleInit(instGlobal);
    if (ret != GMERR_OK) {
        DbRWUnlatchW(&instGlobal->latch);
        return ret;
    }

    ret = PublicServiceInitGlobalPart(instGlobal);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unsucc to init public service when init gloabal instance.");
        DbRWUnlatchW(&instGlobal->latch);
        return ret;
    }

    instGlobal->status = DB_INSTANCE_ONLINE;
    DbRWUnlatchW(&instGlobal->latch);
    return ret;
}

Status EmbSqlCheckPtrParas(const void *ptrs[], uint32_t count)
{
    for (uint32_t i = 0; i < count; i++) {
        if (SECUREC_LIKELY(ptrs[i] != NULL)) {
            continue;
        }
        DB_LOG_AND_SET_LASERR(
            GMERR_UNEXPECTED_NULL_VALUE, "Embedded sql the number(%u) of input point parameter is null", i);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    return GMERR_OK;
}
static Status GmeOpenPrepare(const char *cfgParameter, int flags, GmeConnT **conn)
{
    DbSetServerThreadFlag();

    const void *ptrs[] = {cfgParameter, conn};
    Status ret = EmbSqlCheckPtrParas(ptrs, ELEMENT_COUNT(ptrs));
    if (ret != GMERR_OK) {
        return ret;
    }

    DbCommonInitCfgT cfg = {
#ifdef HPE
        .env = ADPT_HPE,
#else
        .env = ADPT_RTOS_SERVER,
#endif
        .configFileName = cfgParameter,
        .isBackGround = false,
        .openMode = flags,
    };

    ret = DbInstanceMgrInit(&cfg);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = GmeGlobalServerStart();
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

static inline void SetDbServerOption(const char *cfgParameter, int flags, DbServerOptionT *newServerOption)
{
    DbServerOptionT dbServerOption = {0};
    dbServerOption.cfgFilePath = (char *)(uintptr_t)cfgParameter;
    dbServerOption.needStartServer = true;
    dbServerOption.isHmacVrf = true;
    dbServerOption.isEmbedded = true;
    dbServerOption.openMode = flags;
    *newServerOption = dbServerOption;
}

Status GmeServerStart(DbInstanceT *dbInstance, DbServerOptionT *option)
{
    if (dbInstance->status == DB_INSTANCE_ONLINE) {
        return GMERR_OK;
    }

    Status ret = DbEmbeddedServerStart(option, dbInstance);
    if (ret != GMERR_OK) {
        EMB_ERROR_LOG(ret, "Embedded unable to start server, intance:%d, ret(%d)", dbInstance->instanceId, ret);
        return ret;
    }

#ifdef FEATURE_SIMPLEREL
    ret = DbEmbeddedInitSimpleRelMgr(dbInstance);
    if (ret != GMERR_OK) {
        EMB_ERROR_LOG(ret, "Embedded unable to init simplerel mgr");
        return ret;
    }
    // 设置全局变量V1标志位
    DbSetV1Flag(true);
#endif

    dbInstance->status = DB_INSTANCE_ONLINE;
    return GMERR_OK;
}

Status GmeConnectStart(DbInstanceT *dbInstance, GmeConnT **conn)
{
    Status ret = GMERR_OK;
    int32_t maxConnNum = DbCfgGetInt32Lite(DB_CFG_CONN_MAX, dbInstance);
    if (dbInstance->connCount == maxConnNum) {
        EMB_ERROR_LOG(GMERR_CONFIGURATION_LIMIT_EXCEEDED,
            "Embbeded reaches max conn limit (%" PRIu32 "), alloc new conn unsucc.", maxConnNum);
        return GMERR_CONFIGURATION_LIMIT_EXCEEDED;
    }

    DbMemCtxArgsT args = {0};
    DbMemCtxT *memCtx =
        DbCreateDynMemCtx(((DrtInstanceT *)dbInstance->drtIns)->memCtx, true, "Embedded sql conn", &args);
    if (memCtx == NULL) {
        ret = GMERR_MEMORY_OPERATE_FAILED;
        EMB_ERROR_LOG(ret, "Embedded unable to alloc memctx for connection");
        return ret;
    }

    // 创建一个嵌入式客户端实例，如果失败，则尝试关闭之前打开的数据库服务实例
    ret = EmbSqlCreateConnect(dbInstance, memCtx, conn);
    if (ret != GMERR_OK) {
        DbDeleteDynMemCtx(memCtx);
        return ret;
    }

    dbInstance->connCount++;
    return GMERR_OK;
}

void EmbSqlCloseSession(GmeConnT *conn)
{
    if (conn->embSession->session != NULL) {
        QrySessionRelease(conn->embSession->session);
        conn->embSession->session = NULL;
        conn->embSession->affectedNum = 0;
        conn->embSession->totalAffectedNum = 0;
    }
}

Status GmeConnectEnd(DbInstanceT *dbInstance, GmeConnT *conn)
{
    EmbSqlStmtMapDestroy(conn);

    EmbSqlCloseSession(conn);
    DbDeleteDynMemCtx(conn->conMemCtx);

    dbInstance->connCount--;
    return GMERR_OK;
}

void GmeServerEnd(DbInstanceT *dbInstance)
{
    // 在存在客户端连接实例，或状态不为offline，说明不需释放服务端，或者客户端已经被释放。
    if (dbInstance->connCount != 0 || dbInstance->status != DB_INSTANCE_ONLINE) {
        return;
    }

    DbServerShutDown(dbInstance);
    dbInstance->status = DB_INSTANCE_OFFLINE;
}

Status GmeOpen(const char *cfgParameter, int flags, GmeConnT **conn)
{
    DbInstanceT *dbInstance = NULL;
    DbServerOptionT dbServerOption = {0};

    Status ret = GmeOpenPrepare(cfgParameter, flags, conn);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = DbGetOrAllocInstance(cfgParameter, &dbInstance);
    if (ret != GMERR_OK) {
        return ret;
    }
    DB_ASSERT(dbInstance != NULL);
    DbRWLatchW(&dbInstance->latch);

    SetDbServerOption(cfgParameter, flags, &dbServerOption);

    ret = GmeServerStart(dbInstance, &dbServerOption);
    if (ret != GMERR_OK) {
        EMB_ERROR_LOG(ret, "GME server cannot start (%d)", ret);
        DbRWUnlatchW(&dbInstance->latch);
        DbReleaseInstance(dbInstance);
        return ret;
    }

    ret = GmeConnectStart(dbInstance, conn);
    if (ret != GMERR_OK) {
        EMB_ERROR_LOG(ret, "GME server cannot start (%d)", ret);
        // 检查连接数，如果连接数为0, 清理server
        GmeServerEnd(dbInstance);
    }

    DbRWUnlatchW(&dbInstance->latch);
    DbReleaseInstance(dbInstance);
    return ret;
}

Status GmeClose(GmeConnT *conn)
{
    const void *ptrs[] = {conn};
    Status ret = GMERR_OK;

    ret = EmbSqlCheckPtrParas(ptrs, ELEMENT_COUNT(ptrs));
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t instanceId = conn->conMemCtx->instanceId;
    DbInstanceT *dbInstance = NULL;
    ret = DbGetInstanceById(instanceId, &dbInstance);
    if (ret != GMERR_OK) {
        EMB_ERROR_LOG(ret, "Embedded cannot get instance by id(%u) ret(%d)", instanceId, ret);
        return ret;
    }

    DbRWLatchW(&dbInstance->latch);

    ret = GmeConnectEnd(dbInstance, conn);
    if (ret != GMERR_OK) {
        DbRWUnlatchW(&dbInstance->latch);
        DbReleaseInstance(dbInstance);
        return ret;
    }

    GmeServerEnd(dbInstance);
    DbRWUnlatchW(&dbInstance->latch);
    DbReleaseInstance(dbInstance);
    return GMERR_OK;
}

static Status EmbSqlCheckConn(GmeConnT *conn)
{
    if (conn == NULL || conn->embSession == NULL || conn->embSession->session == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Embedded sql the session of conn is null.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    return GMERR_OK;
}

static Status EmbSqlCheckStr(const char *sqlStr)
{
    // 暂定语句最大长度为1024*1024
    uint32_t len = (uint32_t)strnlen(sqlStr, CLT_MAX_LABEL_LENGTH + 1);  // 避免无'\0'结尾的非法字符串
    if (len == 0) {
        DB_LOG_ERROR(GMERR_SYNTAX_ERROR, "Embedded sql the string's length(%u) is not correct", len);
        return GMERR_SYNTAX_ERROR;
    }
    if (len > CLT_MAX_LABEL_LENGTH) {
        DB_LOG_ERROR(GMERR_PROGRAM_LIMIT_EXCEEDED, "Embedded sql the string's length(%u) is not correct", len);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    uint32_t i = len - 1;
    while (i > 0 && isspace(sqlStr[i])) {
        i--;
    }
    if (i == 0) {
        DB_LOG_ERROR(GMERR_SYNTAX_ERROR, "Embedded sql the string(%s) has no actual content.", sqlStr);
        return GMERR_SYNTAX_ERROR;
    }
    return GMERR_OK;
}
static Status EmbSqlCheckConnAndStr(GmeConnT *conn, const char *sqlStr)
{
    Status ret;
    const void *ptrs[] = {conn, sqlStr};
    ret = EmbSqlCheckPtrParas(ptrs, ELEMENT_COUNT(ptrs));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = EmbSqlCheckConn(conn);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = EmbSqlCheckStr(sqlStr);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}
typedef struct EmbSqlReqInfo {
    const char *str;
    uint32_t len;
    uint8_t reserve[3];
} EmbSqlReqInfoT;

#define EMB_SQL_REQUEST_BUF_SIZE 128

// 因为是嵌入式接口，可以使用局部变量的内存，并且put的是字符串指针，而非copy的内容，所以buf空间大小只需要:
// MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE + sizeof(void *)
static inline void EmbSqlCreateRequest(FixBufferT *req, uint8_t *buf, uint32_t size)
{
    FixBufInit(req, buf, size, 0, FIX_BUF_FLAG_EXTEND_BUFFER, NULL);
}

static Status EmbSqlGenerateRequest(
    FixBufferT *req, MsgHeaderT *msgHeader, MsgOpcodeRpcE opCode, const void *data, uint32_t size)
{
    Status ret = FixBufPutData(req, msgHeader, sizeof(MsgHeaderT));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Embedded sql unable to put msg header to request buffer.");
        return ret;
    }
    OpHeaderT opHeader = {0};
    ret = FixBufPutData(req, &opHeader, sizeof(OpHeaderT));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Embedded sql unable to put op header to request buffer.");
        return ret;
    }
    if (size != 0) {
        ret = FixBufPutData(req, data, size);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Embedded sql unable to put data to request buffer.");
            return ret;
        }
    }
    uint32_t len = (req->pos - msgHeader->extendSize - MSG_HEADER_ALIGN_SIZE) - MSG_OP_HEADER_ALIGN_SIZE;
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(req), (uint32_t)opCode, len);
    return GMERR_OK;
}
static Status EmbSqlExecute(SessionT *session, const char *sqlStr, uint32_t size, MsgOpcodeRpcE opCode)
{
    uint8_t buf[EMB_SQL_REQUEST_BUF_SIZE] = {0};
    FixBufferT req = {0};
    EmbSqlCreateRequest(&req, buf, EMB_SQL_REQUEST_BUF_SIZE);
    MsgHeaderT msgHeader = {0};
    msgHeader.modelType = MODEL_STREAM;
    msgHeader.serviceId = DRT_SERVICE_EMBEDDED;
    msgHeader.opNum = 1;
    Status ret = EmbSqlGenerateRequest(&req, &msgHeader, opCode, &sqlStr, size);
    if (ret != GMERR_OK) {
        return ret;
    }
    QrySessionSetReq(session, &req);
    return EmbeddedServiceEntry(session, &msgHeader);
}
typedef struct StreamQryStmt EmbStreamStmtT;

static inline void EmbSqlDeleteStmtMemCtx(EmbStreamStmtT *sqlStmt)
{
    if (sqlStmt->memCtx != NULL) {
        DbDeleteDynMemCtx(sqlStmt->memCtx);  // 通过删除sqlStmt->memCtx的方式一把释放
    }
}

void *GmeMalloc(uint32_t size)
{
    return DB_MALLOC(size);
}

void GmeFree(void *ptr)
{
    DB_FREE(ptr);
}
static char *EmbSqlMessageDump(const char *msg)
{
    if (msg == NULL) {
        return NULL;
    }
    uint32_t msgSize = (uint32_t)strlen(msg) + 1;
    // 由用户自行通过GmeFree接口进行释放
    char *msgDump = (char *)GmeMalloc(msgSize);
    if (msgDump) {
        (void)memset_s(msgDump, msgSize, 0, msgSize);
        (void)memcpy_s(msgDump, msgSize - 1, msg, msgSize - 1);
    }
    return msgDump;
}
static void EmbSqlSetErrorMessage(Status errCode, char **errMsg)
{
    if (errMsg == NULL || errCode == GMERR_OK) {
        return;
    }
    TextT *errText = DbGetLastErrorInfo();
    const char *errMsgTemp = NULL;
    // errText->len contains '\0'
    bool isEmptyStr = (errText->len == 1);
    if (!isEmptyStr) {
        errMsgTemp = errText->str;
    } else {
        const DbErrDetailT *errDetail = DbErrGetDetail(errCode);
        errMsgTemp = ((errDetail == NULL)) ? "unknown error" : errDetail->brief;
    }
    *errMsg = EmbSqlMessageDump(errMsgTemp);
}
static Status EmbSqlExecuteReq(
    GmeConnT *conn, GmeSqlExecuteCallback callback, void *data, char **errMsg, EmbSqlReqInfoT *reqInfo)
{
    DbResetLastErrorInfo();
    Status ret = EmbSqlCheckConnAndStr(conn, reqInfo->str);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }
    SessionT *session = conn->embSession->session;
    DbSpinLock(&session->sqlCtx->lock);
    session->sqlCtx->currentFusionStmt = NULL;
    ret = EmbSqlExecute(session, reqInfo->str, sizeof(char *), MSG_OP_RPC_STREAM_EXEC);
    EmbStreamStmtT *sqlStmt = (GmeSqlStmtT *)session->sqlCtx->currentFusionStmt;
    if (sqlStmt == NULL) {
        DbSpinUnlock(&session->sqlCtx->lock);
        goto RELEASE;
    }
    EmbSqlDeleteStmtMemCtx(sqlStmt);
    session->sqlCtx->currentFusionStmt = NULL;
    DbSpinUnlock(&session->sqlCtx->lock);
RELEASE:
    EmbSqlSetErrorMessage(ret, errMsg);
    return ret;
}
Status GmeSqlExecute(GmeConnT *conn, const char *str, GmeSqlExecuteCallback callback, void *data, char **errMsg)
{
    EmbSqlReqInfoT reqInfo = {.str = str, .len = 0};
    return EmbSqlExecuteReq(conn, callback, data, errMsg, &reqInfo);
}
