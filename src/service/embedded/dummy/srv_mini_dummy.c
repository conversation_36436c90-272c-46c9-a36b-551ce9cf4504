/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: implementation for empty apis
 * Author: wenming
 * Create: 2023-12-18
 */

#include "srv_conn_service.h"
#include "srv_manager_base.h"
#include "srv_data_service.h"
#include "db_server_import.h"
#include "drt_instance.h"
#include "srv_conn_service.h"
#include "db_server_undo_purger.h"

Status ServerCreateUndoPurger(DbInstanceHdT dbInstance)
{
    return GMERR_OK;
}

Status SvServiceViewRegist(SvInstanceT *viewInst)
{
    return GMERR_OK;
}

Status SrvInitServiceMgr(DbInstanceHdT dbInstance)
{
    DrtInstanceT *drtInstance = DrtGetInstance(dbInstance);
    if (drtInstance == NULL) {
        return DbDynLoadHasFeature(COMPONENT_MINIKV) ? GMERR_OK : GMERR_UNEXPECTED_NULL_VALUE;
    }
    return GMERR_OK;
}

Status DataServiceInit(DbInstanceHdT dbInstance)
{
    return GMERR_OK;
}

Status ConnInitService(ConnServiceT *service, DrtInstanceT *drtInstance)
{
    return GMERR_OK;
}

Status DbSrvDfgmtTaskMgrInit(uint16_t *workerId)
{
    return GMERR_OK;
}

Status DbServerImportRulesAndObjects(void)
{
    return GMERR_OK;
}

void DataServiceUnInit(DbInstanceHdT dbInstance)
{
    return;
}

void SrvDestroyServiceMgr(void)
{
    return;
}

void ConnDestroyService(ConnServiceT *service)
{
    return;
}
