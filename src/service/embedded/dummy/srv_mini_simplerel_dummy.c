/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: implementation for empty apis
 * Author:
 * Create:
 */

#include "drt_instance.h"
#include "srv_conn_service.h"
#include "srv_data_service.h"
#include "srv_manager_base.h"

Status SvServiceViewRegist(SvInstanceT *viewInst)
{
    return GMERR_OK;
}

void SrvDataInitResponse(const SessionT *session, FixBufferT *response, uint32_t msgHdrFlag)
{
    return;
}

Status SrvDataSetLastErr(Status opStatus, SessionT *session)
{
    return GMERR_OK;
}

Status SrvDataSendResponse(SessionT *session, int32_t opStatus, uint32_t opCode)
{
    return GMERR_OK;
}

bool IsSelfScheDisconnectMsg(const DrtProcCtxT *procCtx)
{
    return false;
}

Status DbServerImportRulesAndObjects(void)
{
    return GMERR_OK;
}

void ConnDestroyService(ConnServiceT *service)
{
    return;
}

Status ConnInitService(ConnServiceT *service, DrtInstanceT *drtInstance)
{
    return GMERR_OK;
}
