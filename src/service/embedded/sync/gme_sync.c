/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Sync API
 * Author: wangxiangdong
 * Create: 2024-02-06
 */
#include "gme_sync.h"
#include "db_instance.h"
#include "db_log.h"
#include "de_define.h"
#include "de_equip_info.h"
#include "de_sync_pub.h"
#include "srv_mini_check.h"

static Status CheckSyncConfig(GmeSyncConfigT *syncConfig)
{
    if (syncConfig->mode >= GME_SYNC_MODE_INVALID || syncConfig->mode < GME_SYNC_MODE_UPLOAD) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Not valid sync mode");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (syncConfig->equipIds == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Not valid device ids");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (syncConfig->size != 1u) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Not valid device ids size, cloud sync only support one deviceId");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (syncConfig->callbackFunc == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Not valid callbackFunc");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

Status GmeSync(GmeConnT *conn, GmeSyncConfigT *syncConfig)
{
    Status ret = GmeCheckConn(conn);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Sync check connection go wrong");
        return ret;
    }
    ret = DeEquipIdCheck();
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = CheckSyncConfig(syncConfig);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SyncInit(conn->miniSession);
    if (ret != GMERR_OK) {
        return ret;
    }

    StatusInter innerRet = AssignEquipId(conn->miniSession);
    if (innerRet != STATUS_OK_INTER) {
        return DbGetExternalErrno(innerRet);
    }

    return DbGetExternalErrno(Sync(conn->miniSession, (SyncConfigT *)syncConfig));
}

Status GmeRegistryThreadPool(GmeConnT *conn, GmeThreadPoolT *threadPool)
{
    Status ret = GmeCheckConn(conn);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Sync register thread pool check connection go wrong");
        return ret;
    }
    ret = DeEquipIdCheck();
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    if (threadPool == NULL || threadPool->schedule == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Sync register check thread pool go wrong");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    ret = SyncInit(conn->miniSession);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbInstanceT *dbIns = conn->miniSession->sessionPool->dbInstance;
    return DbGetExternalErrno(
        RegistryThreadPool(((DeInstanceT *)dbIns->deIns)->syncMgr, (SyncThreadPoolT *)threadPool));
}

Status GmeRegistryCloudDB(GmeConnT *conn, GmeICloudDBT *iCloud)
{
    Status ret = GmeCheckConn(conn);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Sync register cloud db interface check connection go wrong");
        return ret;
    }
    ret = DeEquipIdCheck();
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    if (iCloud == NULL || iCloud->batchInsert == NULL || iCloud->query == NULL || iCloud->lock == NULL ||
        iCloud->unLock == NULL || iCloud->heartBeat == NULL || iCloud->close == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Sync register cloud db check interface function go wrong");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    ret = SyncInit(conn->miniSession);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbInstanceT *dbIns = conn->miniSession->sessionPool->dbInstance;
    return DbGetExternalErrno(RegistryCloudDB(((DeInstanceT *)dbIns->deIns)->syncMgr, (SyncICloudDBT *)iCloud));
}

Status GmeRegisterEquipId(GmeConnT *conn, GmeEquipIdGetFunc func)
{
    Status ret = GmeCheckConn(conn);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "EquipId register interface check connection go wrong");
        return ret;
    }
    return DeEquipIdRegister((EquipIdGetFuncT)func);
}
