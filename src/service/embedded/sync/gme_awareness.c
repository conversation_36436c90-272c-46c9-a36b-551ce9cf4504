/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: awareness api
 * Author: liufuchenxing
 * Create: 2024-03-12
 */
#include "gme_awareness.h"

#include "db_log.h"
#include "de_awareness_pub.h"
#include "de_equip_info.h"
#include "srv_mini_check.h"

Status GmeAwarenessWriteLog(GmeConnT *conn, const uint8_t *data, uint32_t size)
{
    Status ret = GmeCheckConn(conn);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "awareness write log check connection go wrong.");
        return ret;
    }

    ret = DeEquipIdCheck();
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    if (SECUREC_UNLIKELY(data == NULL || size == 0u)) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "awareness write log check data pointer go wrong.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    char *equipId = (char *)(uintptr_t)DeEquipIdGet();
    StatusInter retInter =
        DeOplogWrite(conn->miniSession, equipId, OPLOG_EVENT_TYPE_AWARENESS_OP, (uint8_t *)(uintptr_t)data, size);
    if (retInter != STATUS_OK_INTER) {
        return DbGetExternalErrno(retInter);
    }
    return GMERR_OK;
}

Status GmeAwarenessSubscribe(GmeConnT *conn, GmeAwarenessSubFunT func)
{
    Status ret = GmeCheckConn(conn);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "awareness subscribe check connection go wrong.");
        return ret;
    }

    ret = DeEquipIdCheck();
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    if (SECUREC_UNLIKELY(func == NULL)) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "awareness subscribe check function go wrong.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    DeInstanceT *deIns = ((DbInstanceT *)conn->miniSession->sessionPool->dbInstance)->deIns;
    return DeAwarenessSubscribe(deIns, func);
}
