/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef GRD_STORE_LIMITS_H
#define GRD_STORE_LIMITS_H

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_COLLECTION_NAME_LEN 511                // 512 - 1, include last \0
#define MAX_JSON_LEN ((1024 * 1024) - 1)           // 1024k - 1, include last \0
#define MAX_ID_STRING_LEN (900 - 1)                // max id string len, include last \0
#define MAX_GRD_KEY_LEN 1024                       // Maximum length of key in kv collection
#define MAX_GRD_VALUE_LEN (32 * 1024 * 1024)       // Maximum length of value in kv collection
#define MAX_GRD_BATCH_VALUE_LEN (4 * 1024 * 1024)  // Maximum length of value in batch kv collection
#define MAX_GRD_SHARED_OBJ_NAME_LEN 254            // 255 - 1, include last \0

#define NUM_OPTION_FIELD 3
#define MAX_OPTION_LEN 512
#define MAX_DOC_FIELD "maxdoc"
#define RD_CONFIG_JSON "{\"max_record_count\" : %u, \"mode\" : \"%s\", \"indextype\" : \"%s\"}"
#define MODE_FIELD "mode"
#define MODE_DOCUMENT_TYPE "document"
#define MODE_KV_TYPE "kv"
#define COLLECTION_INDEX_TYPE "indextype"
#define COLLECTION_INDEX_HASH "hash"
#define COLLECTION_INDEX_BTREE "btree"

#ifdef __cplusplus
}
#endif
#endif  // GRD_STORE_LIMITS_H
