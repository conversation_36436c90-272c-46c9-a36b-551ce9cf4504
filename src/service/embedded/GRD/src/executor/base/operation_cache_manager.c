/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "operation_cache_manager.h"
#include "adpt_spinlock.h"
#include "db_log.h"
#include "grd_error.h"
#include "os_posix.h"

typedef struct OpCacheManager {
    DbSpinLockT spinlock;
    volatile bool isNeedInit;
    TagLinkedListT opCachelist;
    volatile bool isAbnormal;
} OpCacheManagerT;

typedef struct OpCacheList {
    DbSpinLockT spinlock;
    uint32_t dbInstanceId;  // The same to db instance id in old db version
    TagLinkedListT linkNode;
    TagLinkedListT listHeader;
} OpCacheListT;

static OpCacheManagerT g_opCacheManager = {DB_SPINLOCK_INIT_VAL, true, {NULL, NULL}, false};

void DbOpCacheManagerInit(void)
{
    if (!g_opCacheManager.isNeedInit) {
        return;
    }
    DbSpinLock(&g_opCacheManager.spinlock);
    if (g_opCacheManager.isNeedInit) {
        DbLinkedListInit(&g_opCacheManager.opCachelist);
        g_opCacheManager.isNeedInit = false;
    }
    DbSpinUnlock(&g_opCacheManager.spinlock);
}

void DbOpCacheManagerSetAbnormal(void)
{
    DbSpinLock(&g_opCacheManager.spinlock);
    g_opCacheManager.isAbnormal = true;
    DbSpinUnlock(&g_opCacheManager.spinlock);
}

bool IsDbOpCacheManagerStateInNormal(void)
{
    return !g_opCacheManager.isAbnormal;
}

int32_t DbOpItemCreate(uint32_t dbInstanceId, OpCacheTypeE opType, const char *collectionName, OpItemT **opItem)
{
    OpItemT *item = NULL;
    int32_t ret = GRD_AllocAndInitMem(sizeof(OpItemT), 0, (void **)&item);
    if (ret != GRD_OK) {
        DB_LOG_WARN(ret, "out of memory while alloc op item");
        return GRD_FAILED_MEMORY_ALLOCATE;
    }
    char *name = NULL;
    ret = GRD_AllocAndInitMem(strlen(collectionName) + 1, 0, (void **)&name);
    if (name == NULL || ret != GRD_OK) {
        DB_LOG_WARN(ret, "out of memory while alloc op item's table name");
        (void)GRD_FreeMem(item);
        return GRD_FAILED_MEMORY_ALLOCATE;
    }
    (void)memcpy_s(name, strlen(collectionName) + 1, collectionName, strlen(collectionName));
    item->dbInstanceId = dbInstanceId;
    item->collectionName = name;
    item->opType = opType;
    *opItem = item;
    return GRD_OK;
}

int32_t DbFillDMLOpItem(const GmeNbKvItemT *key, const GmeNbKvItemT *value, OpItemT *opItem)
{
    if (key == NULL || key->data == NULL) {
        return GRD_INVALID_ARGS;
    }
    OpDMLKeyValueT *dmlKeyValue = NULL;
    int32_t ret = GRD_AllocAndInitMem(sizeof(OpDMLKeyValueT), 0, (void **)&dmlKeyValue);
    if (ret != GRD_OK) {
        DB_LOG_WARN(ret, "out of memory while alloc op item for dml");
        return ret;
    }
    char *keyData = NULL;
    ret = GRD_AllocAndInitMem(key->dataLen, 0, (void **)&keyData);
    if (ret != GRD_OK) {
        DB_LOG_WARN(ret, "out of memory while alloc op item's key for dml");
        (void)GRD_FreeMem(dmlKeyValue);
        return ret;
    }
    (void)memcpy_s(keyData, key->dataLen, key->data, key->dataLen);
    dmlKeyValue->key.data = keyData;
    dmlKeyValue->key.dataLen = key->dataLen;
    if (value != NULL && value->data != NULL) {
        char *valueData = NULL;
        ret = GRD_AllocAndInitMem(value->dataLen, 0, (void **)&valueData);
        if (ret != GRD_OK) {
            DB_LOG_WARN(ret, "out of memory while alloc op item's value for dml");
            (void)GRD_FreeMem(keyData);
            (void)GRD_FreeMem(dmlKeyValue);
            return ret;
        }
        (void)memcpy_s(valueData, value->dataLen, value->data, value->dataLen);
        dmlKeyValue->value.data = valueData;
        dmlKeyValue->value.dataLen = value->dataLen;
    }
    opItem->dmlKeyValue = dmlKeyValue;
    return GRD_OK;
}

int32_t DbFillDDLOpItem(const char *ddl, uint32_t ddlLen, OpItemT *opItem)
{
    if (ddl == NULL || ddlLen == 0) {  // Create or Drop can use null
        return GRD_OK;
    }
    OpDDLValueT *ddlValue = NULL;
    int32_t ret = GRD_AllocAndInitMem(sizeof(OpDDLValueT), 0, (void **)&ddlValue);
    if (ret != GRD_OK) {
        DB_LOG_WARN(ret, "out of memory while alloc op item for ddl");
        return ret;
    }
    char *ddlData = NULL;
    ret = GRD_AllocAndInitMem(ddlLen, 0, (void **)&ddlData);
    if (ret != GRD_OK) {
        DB_LOG_WARN(ret, "out of memory while alloc op item's value for ddl");
        (void)GRD_FreeMem(ddlValue);
        return ret;
    }
    (void)memcpy_s(ddlData, ddlLen, ddl, ddlLen);
    opItem->ddlValue = ddlValue;
    return GRD_OK;
}

static void DbFreeDMLOpItem(OpDMLKeyValueT *dmlKeyValue)
{
    if (dmlKeyValue == NULL) {
        return;
    }
    if (dmlKeyValue->key.data != NULL) {
        (void)GRD_FreeMem(dmlKeyValue->key.data);
        dmlKeyValue->key.data = NULL;
        dmlKeyValue->key.dataLen = 0;
    }
    if (dmlKeyValue->value.data != NULL) {
        (void)GRD_FreeMem(dmlKeyValue->value.data);
        dmlKeyValue->value.data = NULL;
        dmlKeyValue->value.dataLen = 0;
    }
    (void)GRD_FreeMem(dmlKeyValue);
}

static void DbFreeDDLOpItem(OpDDLValueT *ddlValue)
{
    if (ddlValue == NULL) {
        return;
    }
    if (ddlValue->ddl == NULL) {
        return;
    }
    (void)GRD_FreeMem(ddlValue->ddl);
    ddlValue->ddl = NULL;
    ddlValue->ddlLen = 0;
    (void)GRD_FreeMem(ddlValue);
}

void DbReleaseOpItem(OpItemT *opItem)
{
    if (opItem == NULL) {
        return;
    }
    if (opItem->collectionName != NULL) {
        (void)GRD_FreeMem(opItem->collectionName);
        opItem->collectionName = NULL;
    }
    if (opItem->opType == OP_DML_PUT || opItem->opType == OP_DML_DELETE) {
        DbFreeDMLOpItem(opItem->dmlKeyValue);
    } else {
        DbFreeDDLOpItem(opItem->ddlValue);
    }
    (void)GRD_FreeMem(opItem);
}

static OpCacheListT *DbOpCacheManagerSearch(uint32_t dbInstanceId)
{
    if (DbLinkedListEmpty(&g_opCacheManager.opCachelist)) {
        return NULL;
    }
    OpCacheListT *node = NULL;
    OpCacheListT *tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(node, tmp, &g_opCacheManager.opCachelist, linkNode)
    {
        if (node->dbInstanceId == dbInstanceId) {
            return node;
        }
    }
    return NULL;
}

static int32_t DbOpCacheManagerCreateIfNotExist(uint32_t dbInstanceId, OpCacheListT **opCacheList)
{
    DbSpinLock(&g_opCacheManager.spinlock);
    OpCacheListT *list = DbOpCacheManagerSearch(dbInstanceId);
    if (list != NULL) {
        *opCacheList = list;
        DbSpinUnlock(&g_opCacheManager.spinlock);
        return GRD_OK;
    }
    int32_t ret = GRD_AllocAndInitMem(sizeof(OpCacheListT), 0, (void **)&list);
    if (ret != GRD_OK) {
        DB_LOG_WARN(ret, "out of memory while alloc op cache list");
        DbSpinUnlock(&g_opCacheManager.spinlock);
        return ret;
    }
    DbSpinInit(&list->spinlock);
    list->dbInstanceId = dbInstanceId;
    DbLinkedListAppend(&g_opCacheManager.opCachelist, &list->linkNode);
    DbLinkedListInit(&list->listHeader);
    *opCacheList = list;
    DbSpinUnlock(&g_opCacheManager.spinlock);
    return GRD_OK;
}

int32_t DbOpCacheListCreate(uint32_t dbInstanceId)
{
    OpCacheListT *opCacheList = NULL;
    return DbOpCacheManagerCreateIfNotExist(dbInstanceId, &opCacheList);
}

bool IsDbOpCacheListExisted(uint32_t dbInstanceId)
{
    DbSpinLock(&g_opCacheManager.spinlock);
    OpCacheListT *list = DbOpCacheManagerSearch(dbInstanceId);
    DbSpinUnlock(&g_opCacheManager.spinlock);
    return list != NULL;
}

bool IsDbOpCacheListEmpty(uint32_t dbInstanceId)
{
    bool isEmpty = false;
    DbSpinLock(&g_opCacheManager.spinlock);
    OpCacheListT *list = DbOpCacheManagerSearch(dbInstanceId);
    if (list == NULL) {
        DbSpinUnlock(&g_opCacheManager.spinlock);
        return true;
    }
    DbSpinLock(&list->spinlock);
    isEmpty = DbLinkedListEmpty(&list->listHeader);
    DbSpinUnlock(&list->spinlock);
    DbSpinUnlock(&g_opCacheManager.spinlock);
    return isEmpty;
}

void DbOpCacheListDestroy(uint32_t dbInstanceId)
{
    DbSpinLock(&g_opCacheManager.spinlock);
    OpCacheListT *list = DbOpCacheManagerSearch(dbInstanceId);
    if (list == NULL) {
        DbSpinUnlock(&g_opCacheManager.spinlock);
        return;
    }
    DbSpinLock(&list->spinlock);
    DbLinkedListRemove(&list->linkNode);
    if (!DbLinkedListEmpty(&list->listHeader)) {
        g_opCacheManager.isAbnormal = true;  // NOTE: destroy unfinished op cache list, mark abnormal
    }
    DbSpinUnlock(&list->spinlock);
    (void)GRD_FreeMem(list);  // free after take off the manager
    DbSpinUnlock(&g_opCacheManager.spinlock);
}

static OpCacheListT *GetOpCacheListByDbInstanceId(uint32_t dbInstanceId)
{
    DbSpinLock(&g_opCacheManager.spinlock);
    OpCacheListT *list = DbOpCacheManagerSearch(dbInstanceId);
    DbSpinUnlock(&g_opCacheManager.spinlock);
    return list;
}

int32_t DbOpCachePush(OpItemT *opItem)
{
    OpCacheListT *list = GetOpCacheListByDbInstanceId(opItem->dbInstanceId);
    if (list == NULL) {
        DB_LOG_WARN(GRD_INVALID_ARGS, "invalid instance id, op cache list not exist");
        return GRD_INVALID_ARGS;
    }
    DbSpinLock(&list->spinlock);
    DbLinkedListAppend(&list->listHeader, &opItem->linkNode);
    DbSpinUnlock(&list->spinlock);
    return GRD_OK;
}

OpItemT *DbOpCacheFront(uint32_t dbInstanceId)
{
    OpCacheListT *list = GetOpCacheListByDbInstanceId(dbInstanceId);
    if (list == NULL) {
        DB_LOG_WARN(GRD_INVALID_ARGS, "invalid instance id, op cache list not exist");
        return NULL;
    }
    DbSpinLock(&list->spinlock);
    if (DbLinkedListEmpty(&list->listHeader)) {
        DbSpinUnlock(&list->spinlock);
        return NULL;
    }
    OpItemT *item = LIST_HEAD_ENTRY(&list->listHeader, OpItemT, linkNode);
    DbSpinUnlock(&list->spinlock);
    return item;
}

void DbOpCachePop(uint32_t dbInstanceId)
{
    OpCacheListT *list = GetOpCacheListByDbInstanceId(dbInstanceId);
    if (list == NULL) {
        DB_LOG_WARN(GRD_INVALID_ARGS, "invalid instance id, op cache list not exist");
        return;
    }
    DbSpinLock(&list->spinlock);
    OpItemT *item = LIST_HEAD_ENTRY(&list->listHeader, OpItemT, linkNode);
    DbLinkedListRemove(&item->linkNode);
    DbReleaseOpItem(item);
    DbSpinUnlock(&list->spinlock);
}
