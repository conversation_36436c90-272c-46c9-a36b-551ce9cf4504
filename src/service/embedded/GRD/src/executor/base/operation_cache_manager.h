/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef OPERATION_CACHE_MANAGER_H
#define OPERATION_CACHE_MANAGER_H

#include "adpt_thread.h"
#include "grd_type_export.h"
#include "gme_nb_types.h"
#include "db_linkedlist.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    OP_DML_PUT,
    OP_DML_DELETE,
    OP_DDL_CREATE,
    OP_DDL_DROP,
} OpCacheTypeE;

typedef struct OpDMLKeyValue {
    GmeNbKvItemT key;
    GmeNbKvItemT value;
} OpDMLKeyValueT;

typedef struct OpDDLValue {
    char *ddl;
    uint32_t ddlLen;
} OpDDLValueT;

typedef struct OpItem {
    TagLinkedListT linkNode;
    uint32_t dbInstanceId;
    OpCacheTypeE opType;
    char *collectionName;
    union {
        OpDDLValueT *ddlValue;
        OpDMLKeyValueT *dmlKeyValue;
    };
} OpItemT;

void DbOpCacheManagerInit(void);
void DbOpCacheManagerSetAbnormal(void);
bool IsDbOpCacheManagerStateInNormal(void);

int32_t DbOpItemCreate(uint32_t dbInstanceId, OpCacheTypeE opType, const char *collectionName, OpItemT **opItem);
int32_t DbFillDMLOpItem(const GmeNbKvItemT *key, const GmeNbKvItemT *value, OpItemT *opItem);
int32_t DbFillDDLOpItem(const char *ddl, uint32_t ddlLen, OpItemT *opItem);
void DbReleaseOpItem(OpItemT *opItem);

int32_t DbOpCacheListCreate(uint32_t dbInstanceId);
bool IsDbOpCacheListExisted(uint32_t dbInstanceId);
bool IsDbOpCacheListEmpty(uint32_t dbInstanceId);
void DbOpCacheListDestroy(uint32_t dbInstanceId);

int32_t DbOpCachePush(OpItemT *opItem);
OpItemT *DbOpCacheFront(uint32_t dbInstanceId);
void DbOpCachePop(uint32_t dbInstanceId);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // OPERATION_CACHE_MANAGER_H
