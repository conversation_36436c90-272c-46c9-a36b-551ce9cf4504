/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef GRD_OPERATION_CONTEXT_H
#define GRD_OPERATION_CONTEXT_H

#include "db_linkedlist.h"
#include "grd_type.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_NESTED_LEN 4

typedef enum ProjNodeType {
    LEAF_NODE = 0,
    INTERNAL_NODE,
    ROOT_NODE,
} ProjNodeType;

typedef enum ConditionNodeType {
    // predicate expression
    COND_PRED = 0,
    // AND expression
    COND_AND,
    // OR expression
    COND_OR,
} ConditionNodeType;

typedef enum IdGenerateType {
    // filter not contains _id
    GRD_AUTO,
    // filter contains _id
    GRD_MANUAL,
} IdGenerateType;

typedef struct FilterConditionNode {
    // GRD_COND_PRED : leaf node
    ConditionNodeType condType;
    // opr need change to list when support more operator in one field
    ExprOperator opr;
    Value value;
    // Flag for query using auto-generated _id, in this case, skipId should be true.
    bool skipId;
    // e.g value is {instock: { warehouse: 'B', qty: 15 }}
    // e.g fieldPath = {"instock", "warehouse"}
    uint32_t fieldPathLen;
    char *fieldPath[MAX_NESTED_LEN];
    int32_t childrenNum;
    int32_t level;
    struct FilterConditionNode **children;
} FilterConditionNode;

typedef struct ProjectionNode {
    ProjNodeType type;
    char *fieldName;
    int32_t level;
    TagLinkedListT children;
} ProjectionNode;

typedef struct ProjListNode {
    TagLinkedListT linkedNode;
    ProjectionNode *node;
} ProjListNode;

typedef struct UpdateDataValue {
    TagLinkedListT linkedNode;
    uint32_t fieldPathLen;
    char *fieldPath[MAX_NESTED_LEN];
    Value value;  // point to update object, no need alloc
} UpdateDataValue;

typedef struct OperationStmt {
    void *filterObject;  // filter object
    void *object;        // update or insert document
    FilterConditionNode *query;
    uint32_t viewType;  // not view : 0, view: 1
    ProjectionNode *projectionInfo;
    // value means target value, used when operationType is insert/upsert
    // when update operation, value is null, some update value is in updateData
    IdGenerateType idType;
    Value id;  // used if insert operation
    const char *originalValue;
    char *value;
    bool isValueAllocInner;
    TagLinkedListT updateData;
    UpdateDataValue *updateDataPtr;
} OperationStmt;

typedef struct OperationContext {
    char *collectionName;
    uint32_t flags;
    OperationStmt *opStmt;
} OperationContext;

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // GRD_OPERATION_CONTEXT_H
