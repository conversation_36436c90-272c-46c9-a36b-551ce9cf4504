/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef GRD_STORE_TYPE_H
#define GRD_STORE_TYPE_H

#include <stddef.h>
#include <stdint.h>
#include "gme_api.h"
#include "gme_nb_api.h"

#include "adpt_spinlock.h"
#include "adpt_thread.h"
#include "db_linkedlist.h"
#include "grd_type.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct CollectionNameNode {
    TagLinkedListT node;
    char *collectionName;      // make sure is end with \0
    int resultSetCnt;          // > 0 means has activity result set
    GRD_CollectionTypeE type;  // kv or document;
} CollectionNameNode;

struct GRD_DB {
    DbSpinLockT dbSpinLock;
    GmeConnT *conn;
    GmeNbConnT *nbConn;
    uint32_t openFlags;
    DbThreadHandle thread;
    int32_t lockFd;
    TagLinkedListT resultSetList;
};

#ifdef __cplusplus
}
#endif

#endif  // GRD_STORE_TYPE_H
