/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef GRD_PARSER_H
#define GRD_PARSER_H

#include "db_json.h"
#include "operation_context.h"
#include "operation_type.h"

#ifdef __cplusplus
extern "C" {
#endif

#define GRD_FIELD_ID_VISIBLE 1

typedef struct OperationTypeMap {
    const char *operation;
    ExprOperator opr;
} OperationTypeMap;

int32_t CreateOperationContext(char *collectionName, uint32_t flags, OperationContext **ctx);
void FreeOperationContext(OperationContext *ctx, bool isFreeStmt);
int32_t CreateOperationStmt(void *filterObject, void *docObject, OperationStmt **opStmt);

int32_t CreateFilterCondition(void *object, int32_t level, FilterConditionNode **query);
void FreeFilterCondition(FilterConditionNode *node, bool isFreeCurrentNode);

int32_t CreateUpdateDataList(void *rootObject, TagLinkedListT *updateDataList, UpdateDataValue **updateDataAddr);
void FreeUpdateDataList(TagLinkedListT *updateDataList);

int32_t CreateProjection(const char *projection, uint32_t flags, ProjectionNode **projNode, bool *viewType);
void FreeProjection(ProjectionNode *projection);

// if query is null, used for insert operator
int32_t GetUpsertOpStmt(const char *filter, const char *document, OperationStmt **opStmt);
int32_t SetUpsertStmt(OperationStmt *opStmt);

// filter operation content query/viewType/projectionInfo
int32_t GetFilterOpStmt(const char *filter, const char *projection, uint32_t flags, OperationStmt **opStmt);
// same as delete operator
// update operation content query/updateData
// delete operation content query
int32_t GetUpdateOpStmt(const char *filter, const char *update, OperationStmt **opStmt);

void FreeOperationStmt(OperationStmt *opStmt);

/**
 * @brief filter value in object in field, check if the value is satisfied with find filter condition
 * will compare all field including its child if object or value type is JSON_ARRAY or JSON_OBJECT
 * if object type including its child object is different with value, return false
 * @param[in] object means current field object node
 * @param[in] conditionNode means condition leaf node, use the fieldPath and value
 * @param[out] result is the result of the interface
 * @return Returns the result of function;
 */
int32_t FilterValueByAbsField(void *object, FilterConditionNode *conditionNode, bool *result);
/**
 * @brief update value in object in field, use for when update part field
 * value type may be all valid DbJsonTypeE type
 * if object type is same with value type, just update its value
 * if object type is different from value type, will replace the field by value
 * if object don't have the field, add one at last
 * @param[in] object means root object
 * @param[in] fieldPath means field path of the update value
 * @param[in] fieldPathLen means field path len
 * @param[in] value is new Value struct
 * @return Returns the result of function;
 */
int32_t UpdateItemValueByAbsField(
    void *object, const char *fieldPath[], uint32_t fieldPathLen, Value *value, uint32_t flags);
/**
 * @brief get value from object in field, used for get leaf node value
 * value type should be basic type, such as JSON_BOOL/JSON_NULL/JSON_NUMBER/JSON_STRING, nor return error
 * @param[in] object means parent current object node
 * @param[in] field means field from object
 * @param[out] value is Value struct
 * @return Returns the result of function;
 */
int32_t GetItemValueByField(const void *object, const char *field, Value *value);

int32_t FillOperationStmtId(void *object, OperationStmt *stmt);

int32_t IsValueMatchFilter(void *object, FilterConditionNode *currentNode, bool *result);

void ProjectValueWithRetain(void *parent, const ProjectionNode *projectionNode, bool *hasChildMatch);

void ProjectValueWithDelete(void *object, const ProjectionNode *projectionNode);
// if find all field, return its parentNodeObject
// if only find some field, return last existed node object
int32_t GetItemParentObjectFromPath(
    void *object, const char *fieldPath[], uint32_t fieldPathLen, void **parentObject, uint32_t *realPathLen);
// e.g "personInfo.name" translate to ["personInfo", "name"], used in filter split
int32_t SplitFieldAndCheck(const char *filterField, int32_t maxFieldLen, char *fieldPath[], uint32_t *fieldPathLen);
void FreeFieldPathFromIdx(char *fieldPath[], uint32_t idx, uint32_t fieldLen);

int32_t GetStringValue(const void *object, Value *value);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // GRD_PARSER_H
