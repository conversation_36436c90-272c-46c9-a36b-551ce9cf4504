/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include "parser.h"
#include <stdbool.h>
#include <stdio.h>

#include "ctype.h"
#include "db_log.h"
#include "document_check.h"
#include "grd_error.h"
#include "grd_utils.h"
#include "operation_type.h"
#include "os_posix.h"
#include "store_limits.h"

#define INITIAL_VIEW_TYPE (-1)
#define TRUE_VIEW_TYPE 1
#define FALSE_VIEW_TYPE 0

const SelectTypeMap g_selectTypeList[] = {{AND_OPERATION, LO<PERSON>CAL_TYPE}, {OR_OPERATION, LO<PERSON>CAL_TYPE},
    {EQUAL_OPERATION, COMPARISON_TYPE}, {NOT_EQUAL_OPERATION, COMPARISON_TYPE}, {GT_OPERATION, COMPARISON_TYPE},
    {GTE_OPERATION, COMPARISON_TYPE}, {LT_OPERATION, COMPARISON_TYPE}, {LTE_OPERATION, COMPARISON_TYPE},
    {EXISTS_OPERATION, ELEMENT_TYPE}, {TYPE_OPERATION, ELEMENT_TYPE}};

const OperationTypeMap g_operatorTypeList[] = {{EQUAL_OPERATION, GRD_OPR_EQ}, {NOT_EQUAL_OPERATION, GRD_OPR_NE},
    {GT_OPERATION, GRD_OPR_GT}, {GTE_OPERATION, GRD_OPR_GE}, {LT_OPERATION, GRD_OPR_LT}, {LTE_OPERATION, GRD_OPR_LE},
    {EXISTS_OPERATION, GRD_OPR_EXIST}, {TYPE_OPERATION, GRD_OPR_TYPE}};

#if defined(GRD_SUPPORT_LOGIC_OPERATION)
static ExprOperator GetExprOperator(const char *operatorStr)
{
    int32_t allTypeSize = sizeof(g_operatorTypeList) / sizeof(OperationTypeMap);
    for (int32_t index = 0; index < allTypeSize; index++) {
        if (strcmp(g_operatorTypeList[index].operation, operatorStr) == 0) {
            return g_operatorTypeList[index].opr;
        }
    }
    return GRD_INVALID;
}
#endif

// analyze the field name is which select type, like {"$and" : "value"} will return LOGICAL_TYPE
static QuerySelectType GetQuerySelectType(const char *field)
{
    uint32_t allTypeSize = sizeof(g_selectTypeList) / sizeof(SelectTypeMap);
    for (uint32_t index = 0; index < allTypeSize; index++) {
        if (strcmp(g_selectTypeList[index].operation, field) == 0) {
            return g_selectTypeList[index].type;  // second item
        }
    }
    return DEFAULT_TYPE;
}

static bool OperationTypeCheck(const void *object, QuerySelectType type, bool isTopLevel)
{
    if (object == NULL) {
        return false;
    }
    DbJsonTypeE objectType = DbJsonGetType(object);
    // operation type is default and operation
    switch (type) {
        case LOGICAL_TYPE:
            // logical type, object must be array
            return objectType == DB_JSON_ARRAY;
        case COMPARISON_TYPE:
        case ELEMENT_TYPE:
            return !isTopLevel;
        case DEFAULT_TYPE:
        default:
            break;
    }
    return true;
}

static int32_t GetBoolValue(const void *object, Value *value)
{
    bool boolValue;
    int32_t errCode = Json2GRDErrNo(DbJsonGetItemBool(object, &boolValue));
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "set obj item bool go wrong");
        return errCode;
    }
    value->value.boolValue = boolValue;
    value->length = (uint32_t)sizeof(bool);
    value->type = DB_JSON_BOOL;
    return GRD_OK;
}

static int32_t GetDoubleValue(const void *object, Value *value)
{
    double doubleValue = 0;
    int32_t errCode = Json2GRDErrNo(DbJsonGetNumberValueWithCheck(object, &doubleValue));
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "set obj item double go wrong");
        return errCode;
    }
    value->value.realValue = doubleValue;
    value->length = (uint32_t)sizeof(double);
    value->type = DB_JSON_REAL;
    return GRD_OK;
}

static int32_t AllocAndInitProjNode(char *name, ProjNodeType type, int32_t level, ProjectionNode **node)
{
    if (level > MAX_NESTED_LEN) {
        DB_LOG_ERROR(GRD_LARGE_JSON_NEST, "level > max nested len");
        return GRD_LARGE_JSON_NEST;
    }
    ProjectionNode *tmpNode = NULL;
    int32_t ret = GRD_AllocAndInitMem(sizeof(ProjectionNode), 0, (void **)&tmpNode);
    if (ret != GRD_OK) {
        DB_LOG_ERROR(ret, "wrong projNode-alloc");
        return ret;
    }
    tmpNode->type = type;
    tmpNode->fieldName = name;  // NULL name means that the node is root
    tmpNode->level = level;
    DbLinkedListInit(&(tmpNode->children));
    *node = tmpNode;
    return GRD_OK;
}

static ProjectionNode *LookUpInChildren(const ProjectionNode *parent, const char *childName)
{
    ProjListNode *cur = NULL;
    ProjListNode *tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(cur, tmp, &(parent->children), linkedNode)
    {
        if (strcmp(cur->node->fieldName, childName) == 0) {
            return cur->node;
        }
    }
    return NULL;
}

static int32_t InsertInChildren(ProjectionNode *parent, ProjectionNode *child)
{
    ProjListNode *childNode = NULL;
    int32_t ret = GRD_AllocAndInitMem(sizeof(ProjListNode), 0, (void **)&childNode);
    if (ret != GRD_OK) {
        DB_LOG_ERROR(ret, "wrong projListNode-alloc");
        return ret;
    }
    childNode->node = child;
    DbLinkedListAppend(&(parent->children), &(childNode->linkedNode));
    return GRD_OK;
}

static void FreeStmtValue(char *value, bool isValueAllocInner)
{
    if (isValueAllocInner) {
        (void)GRD_FreeMem(value);
    } else {
        DbJsonFree(value);
    }
}

static int32_t GetValueObject(Value *value, void **object)
{
    if (object == NULL || value == NULL) {
        DB_LOG_ERROR(GRD_INVALID_ARGS, "obj or value NULL");
        return GRD_INVALID_ARGS;
    }
    void *newDataObject = NULL;
    int32_t errCode = GRD_OK;
    switch (value->type) {
        case DB_JSON_NUMBER:
        case DB_JSON_INTEGER:
        case DB_JSON_REAL:
            newDataObject = DbJsonCreateReal(value->value.realValue);
            break;
        case DB_JSON_STRING:
            newDataObject = DbJsonCreateString(value->value.byteValue);
            break;
        case DB_JSON_BOOL:
        case DB_JSON_TRUE:
        case DB_JSON_FALSE:
            newDataObject = DbJsonCreateBool(value->value.boolValue);
            break;
        case DB_JSON_NULL:
            newDataObject = DbJsonCreateNull();
            break;
        case DB_JSON_ARRAY:
        case DB_JSON_OBJECT:
            newDataObject = DbJsonDuplicate(value->value.object, true);
            break;
        default:
            errCode = GRD_INVALID_JSON_TYPE;
            DB_LOG_ERROR(errCode, "invalid value type");
            break;
    }
    if (errCode != GRD_OK) {
        return errCode;
    }
    if (newDataObject == NULL) {
        DB_LOG_ERROR(GRD_FAILED_MEMORY_ALLOCATE, "wrong obj-create");
        return GRD_FAILED_MEMORY_ALLOCATE;
    }
    *object = newDataObject;
    return GRD_OK;
}

int32_t SetUpsertStmt(OperationStmt *opStmt)
{
    void *idObj = NULL;
    int32_t ret = GRD_OK;
    if (opStmt->idType == GRD_AUTO) {
        return ret;
    }
    ret = GetValueObject(&opStmt->id, &idObj);
    if (ret != GRD_OK) {
        return ret;
    }
    ret = Json2GRDErrNo(DbJsonSetItemFieldName(OBJECT_ID_FIELD_NAME, idObj));
    if (ret != GRD_OK) {
        DB_LOG_ERROR(ret, "wrong fieldName-set");
        DbJsonDelete(idObj);
        return ret;
    }
    ret = Json2GRDErrNo(DbJsonInsertItemObject(opStmt->object, 0, idObj));
    if (ret != GRD_OK) {
        DB_LOG_ERROR(ret, "insert into json obj go wrong");
        DbJsonDelete(idObj);
        return ret;
    }
    if (opStmt->value != NULL) {
        FreeStmtValue(opStmt->value, opStmt->isValueAllocInner);
    }
    ret = Json2GRDErrNo(DbJsonTranslateToString(opStmt->object, &opStmt->value));
    if (ret != GRD_OK) {
        DB_LOG_ERROR(ret, "translate to json str go wrong");
        return ret;
    }
    opStmt->isValueAllocInner = false;
    return CheckDocumentSize(opStmt->value);
}

static int32_t InitOpStmt(void *filterObj, void *docObj, const char *document, OperationStmt **opStmt)
{
    int32_t errCode = CreateOperationStmt(filterObj, docObj, opStmt);
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "wrong opStmt-create");
        return errCode;
    }
    (*opStmt)->originalValue = document;
    return errCode;
}

static int32_t CreateConditionObject(const char *filter, void **obj, FilterConditionNode **query)
{
    DbJsonT *filterObj = NULL;
    int32_t errCode = Json2GRDErrNo(DbJsonStrTransToJsonObject(filter, true, true, &filterObj));
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "translate to json obj go wrong");
        return errCode;
    }
    errCode = CreateFilterCondition(filterObj, 0, query);
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "wrong filterCondition-create");
        DbJsonDelete(filterObj);
        return errCode;
    }
    *obj = filterObj;
    return GRD_OK;
}

static int32_t GetObjectFromArray(void *currentObject, const char *fieldPathName, void **fieldObject)
{
    int32_t errCode = GRD_OK;
    if (!IsNumber(fieldPathName)) {
        return GRD_FIELD_NOT_FOUND;
    }
    errno = 0;
    int32_t index = strtol(fieldPathName, NULL, 10);  // 10 means Decimal
    if (errno != 0) {
        DB_LOG_ERROR(GRD_INVALID_ARGS, "field path not num");
        return GRD_INVALID_ARGS;
    }
    errCode = Json2GRDErrNo(DbJsonCheckArrayIndexValid(currentObject, index));
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(GRD_ARRAY_INDEX_NOT_FOUND, "array idx invalid");
        return GRD_ARRAY_INDEX_NOT_FOUND;
    }
    *fieldObject = DbJsonArrayGet(currentObject, index);
    return GRD_OK;
}

static int32_t GetItemObjectFromPath(
    void *object, FilterConditionNode *conditionNode, uint32_t startPos, void **itemObject, uint32_t *lastFieldIndex)
{
    if (object == NULL || conditionNode == NULL || itemObject == NULL) {
        DB_LOG_ERROR(GRD_INVALID_ARGS, "input args invalid");
        return GRD_INVALID_ARGS;
    }
    void *currentObject = object;
    for (uint32_t i = startPos; i < conditionNode->fieldPathLen; i++) {
        if (conditionNode->fieldPath[i] == NULL) {
            return GRD_INVALID_ARGS;
        }
        void *fieldObject = NULL;
        // fieldPath like "fieldOne.fieldTwo.5", the third field should be array
        if (DbJsonGetType(currentObject) == DB_JSON_ARRAY) {
            int32_t errCode = GetObjectFromArray(currentObject, conditionNode->fieldPath[i], &fieldObject);
            if (errCode != GRD_OK) {
                // return array object to more handle
                *itemObject = currentObject;
                return errCode;
            }
        } else {
            fieldObject = DbJsonObjectGetCaseSensitive(currentObject, conditionNode->fieldPath[i], true);
        }
        if (fieldObject == NULL) {
            DB_LOG_ERROR(GRD_FIELD_NOT_FOUND, "wrong path-find");
            return GRD_FIELD_NOT_FOUND;
        }
        currentObject = fieldObject;
        *lastFieldIndex = i;
    }
    *itemObject = currentObject;
    return GRD_OK;
}

static int32_t EqualCompareObject(void *oldObject, Value *filterValue, DbJsonTypeE objectType, bool *result)
{
    bool filterResult = false;
    int32_t errCode = GRD_OK;
    bool boolValue = false;
    double doubleValue = 0;
    char *strValue = NULL;
    uint32_t strLen = 0;
    switch (objectType) {
        case DB_JSON_BOOL:
        case DB_JSON_TRUE:
        case DB_JSON_FALSE:
            errCode = Json2GRDErrNo(DbJsonGetItemBool(oldObject, &boolValue));
            if (errCode == GRD_OK) {
                filterResult = (boolValue == filterValue->value.boolValue);
            }
            break;
        case DB_JSON_NULL:
            filterResult = true;
            break;
        case DB_JSON_NUMBER:
        case DB_JSON_INTEGER:
        case DB_JSON_REAL:
            errCode = Json2GRDErrNo(DbJsonGetNumberValueWithCheck(oldObject, &doubleValue));
            if (errCode == GRD_OK) {
                filterResult = IsDoubleEQ(doubleValue, filterValue->value.realValue);
            }
            break;
        case DB_JSON_STRING:
            errCode = Json2GRDErrNo(DbJsonGetItemString(oldObject, &strValue, &strLen));
            if (errCode == GRD_OK) {
                filterResult = !strcmp(filterValue->value.byteValue, strValue);
            }
            break;
        case DB_JSON_ARRAY:
        case DB_JSON_OBJECT:
            filterResult = DbJsonCompareTwoObjects(oldObject, filterValue->value.object);
            break;
        default:
            errCode = GRD_INVALID_JSON_TYPE;
            break;
    }
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "wrong jsonObj-cmp");
        return errCode;
    }
    *result = filterResult;
    return GRD_OK;
}

static DbJsonTypeE TransValueToJsonType(int32_t type)
{
    DbJsonTypeE jsonType = (DbJsonTypeE)type;
    switch (jsonType) {
        case DB_JSON_NUMBER:
        case DB_JSON_INTEGER:
        case DB_JSON_REAL:
            return DB_JSON_NUMBER;
        case DB_JSON_BOOL:
        case DB_JSON_TRUE:
        case DB_JSON_FALSE:
            return DB_JSON_BOOL;
        default:
            return jsonType;
    }
    return jsonType;
}

static int32_t Filter(void *oldObject, Value *filterValue, ExprOperator exprOp, bool *result)
{
    // only support eq exprOp
    if (exprOp != GRD_OPR_EQ) {
        DB_LOG_ERROR(GRD_JSON_OPERATION_NOT_SUPPORT, "not support optr");
        return GRD_JSON_OPERATION_NOT_SUPPORT;
    }
    // only handle eq exprOp
    DbJsonTypeE objectType = DbJsonGetType(oldObject);
    if (TransValueToJsonType(objectType) != TransValueToJsonType(filterValue->type)) {
        *result = false;
        return GRD_OK;
    }
    bool filterResult = false;
    int32_t errCode = EqualCompareObject(oldObject, filterValue, objectType, &filterResult);
    if (errCode != GRD_OK) {
        return errCode;
    }
    *result = filterResult;
    return GRD_OK;
}

static int32_t InnerFilterValueByAbsField(
    void *object, FilterConditionNode *conditionNode, uint32_t startPos, bool isCheckNull, bool *result);
static int32_t FilterByArray(
    void *currentArrayNode, FilterConditionNode *conditionNode, uint32_t lastIndex, bool *result)
{
    bool isFirstNode = true;
    int32_t innerCode = GRD_OK;
    bool isMatch = false;
    void *subObject = NULL;
    while (true) {
        subObject = DbJsonGetNextNode(currentArrayNode, subObject, isFirstNode);
        if (subObject == NULL) {
            break;
        }
        isFirstNode = false;
        void *targetObject = DbJsonObjectGetCaseSensitive(subObject, conditionNode->fieldPath[lastIndex + 1], true);
        if (targetObject == NULL) {
            continue;
        }
        uint32_t targetIndex = lastIndex + 2;  // lastIndex + 2 means the target field
        if (targetIndex < conditionNode->fieldPathLen) {
            innerCode = InnerFilterValueByAbsField(targetObject, conditionNode, targetIndex, false, &isMatch);
        } else if (targetIndex == conditionNode->fieldPathLen) {  // means targetObject is last node
            innerCode = Filter(targetObject, &(conditionNode->value), conditionNode->opr, &isMatch);
        } else {
            innerCode = GRD_INNER_ERR;
        }
        if (innerCode == GRD_FIELD_NOT_FOUND) {
            continue;
        }
        if (innerCode != GRD_OK) {
            return innerCode;
        }
        if (isMatch) {
            *result = true;
            return GRD_OK;
        }
    }
    *result = false;
    return GRD_OK;
}

static int32_t InnerFilterValueByAbsField(
    void *object, FilterConditionNode *conditionNode, uint32_t startPos, bool isCheckNull, bool *result)
{
    void *currentNode = NULL;
    // call by inside, parentIndex should be its parent index, such as t1.t2.t3, t2 is : [{t3 : xx}]
    // startPos is point to t3, its parent node is t2, need  to filter by array again by index 2
    uint32_t parentIndex = (startPos == 0) ? startPos : startPos - 1;
    int32_t errCode = GetItemObjectFromPath(object, conditionNode, startPos, (void **)&currentNode, &parentIndex);
    if (errCode == GRD_FIELD_NOT_FOUND && currentNode != NULL && DbJsonGetType(currentNode) == DB_JSON_ARRAY &&
        parentIndex < (conditionNode->fieldPathLen - 1) && !IsNumber(conditionNode->fieldPath[parentIndex + 1])) {
        // such as value is {t1 : {t2 : [ 2, {t3 : 1}]}}, filter is {t1.t2.t3 : 1} should be matched
        int32_t innerCode = FilterByArray(currentNode, conditionNode, parentIndex, result);
        if (innerCode != GRD_OK) {
            return innerCode;
        }
        if (*result) {
            return GRD_OK;
        }
    }
    if (isCheckNull && (errCode == GRD_FIELD_NOT_FOUND || errCode == GRD_ARRAY_INDEX_NOT_FOUND)) {
        // when object has no field and value is null, result is false
        *result = (conditionNode->value.type == DB_JSON_NULL) ? true : false;
        return GRD_OK;
    }
    if (errCode != GRD_OK) {
        return errCode;
    }
    errCode = Filter((void *)currentNode, &(conditionNode->value), conditionNode->opr, result);
    if (errCode != GRD_OK) {
        return errCode;
    }
    return errCode;
}

static void InitOperationStmt(OperationStmt *opStmt)
{
    opStmt->object = NULL;
    opStmt->query = NULL;
    opStmt->viewType = TRUE_VIEW_TYPE;
    opStmt->projectionInfo = NULL;
    opStmt->originalValue = NULL;
    opStmt->value = NULL;
    opStmt->isValueAllocInner = true;
    DbLinkedListInit(&opStmt->updateData);
    opStmt->updateDataPtr = NULL;
}

static int32_t GetCurrentItemValue(void *object, Value *value)
{
    DbJsonTypeE valueType = DbJsonGetType(object);
    switch (valueType) {
        case DB_JSON_BOOL:
        case DB_JSON_TRUE:
        case DB_JSON_FALSE:
            return GetBoolValue(object, value);
        case DB_JSON_NUMBER:
        case DB_JSON_INTEGER:
        case DB_JSON_REAL:
            return GetDoubleValue(object, value);
        case DB_JSON_STRING:
            return GetStringValue(object, value);
        case DB_JSON_NULL:
            value->type = DB_JSON_NULL;
            return GRD_OK;
        case DB_JSON_ARRAY:
        case DB_JSON_OBJECT:
            value->value.object = object;
            value->length = (uint32_t)sizeof(void *);
            value->type = (valueType == DB_JSON_OBJECT) ? DB_JSON_OBJECT : DB_JSON_ARRAY;
            return GRD_OK;
        default:
            DB_LOG_ERROR(GRD_INVALID_JSON_TYPE, "json type invalid");
            break;
    }
    return GRD_INVALID_JSON_TYPE;
}

static int32_t GetViewType(Value *value, int32_t *viewType)
{
    switch (value->type) {
        case DB_JSON_NUMBER:
        case DB_JSON_INTEGER:
        case DB_JSON_REAL:
            *viewType = !IsDoubleEQ(value->value.realValue, 0) ? TRUE_VIEW_TYPE : FALSE_VIEW_TYPE;
            break;
        case DB_JSON_BOOL:
        case DB_JSON_TRUE:
        case DB_JSON_FALSE:
            *viewType = (int32_t)value->value.boolValue;
            break;
        case DB_JSON_STRING:
            if (strcmp(value->value.byteValue, "") == 0) {
                *viewType = TRUE_VIEW_TYPE;
                return GRD_OK;
            }
            return GRD_INVALID_PROJECTION_VALUE;
        case DB_JSON_NULL:
        case DB_JSON_OBJECT:
        case DB_JSON_ARRAY:
        default:
            DB_LOG_ERROR(GRD_INVALID_PROJECTION_VALUE, "view json type invalid");
            return GRD_INVALID_PROJECTION_VALUE;
    }
    return GRD_OK;
}

static int32_t CheckFieldVal(Value *value, int32_t *viewType)
{
    int32_t tmpType = 0;
    int32_t ret = GetViewType(value, &tmpType);
    if (ret != GRD_OK) {
        DB_LOG_ERROR(ret, "view type get wrong");
        return ret;
    }
    // Check view type
    if (value->type == DB_JSON_OBJECT) {
        return GRD_OK;
    }
    // Set the value when it's the first time meet the viewType
    if (*viewType == INITIAL_VIEW_TYPE) {
        *viewType = tmpType;
        return GRD_OK;
    }
    // Otherwise, if not as the same value as previous step, return false
    if (*viewType != tmpType) {
        DB_LOG_ERROR(GRD_INVALID_PROJECTION_VALUE, "view type diff");
        return GRD_INVALID_PROJECTION_VALUE;
    }
    return GRD_OK;
}

static void FreeProjList(TagLinkedListT *projList)
{
    // the process below already covers the situation of empty dataList
    ProjListNode *curr = NULL;
    ProjListNode *tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(curr, tmp, projList, linkedNode)
    {
        FreeProjection(curr->node);
        DbLinkedListRemove(&curr->linkedNode);
        (void)GRD_FreeMem(curr);
    }
}

static int32_t FilterFieldCheck(
    const char *filterHead, const char *ch, uint32_t startIndex, bool *isFirstNum, uint32_t *numCount)
{
    if (isdigit(*ch)) {
        (*numCount)++;
    } else if (!IsFieldCharValid(*ch, false)) {
        DB_LOG_ERROR(GRD_INVALID_ARGS, "field char invalid");
        return GRD_INVALID_ARGS;
    }
    if (ch == (filterHead + startIndex) && isdigit(*ch)) {
        *isFirstNum = true;
    }
    return GRD_OK;
}

static int32_t SetFieldPath(
    const char *filterField, uint32_t totalLen, uint32_t maxFieldLen, char *fieldPath[], uint32_t fieldPos)
{
    if (totalLen <= 1) {  // include '\0'
        DB_LOG_ERROR(GRD_INVALID_ARGS, "field len invalid");
        return GRD_INVALID_ARGS;
    }
    if (fieldPos >= maxFieldLen) {
        DB_LOG_ERROR(GRD_LARGE_JSON_NEST, "field pos > max len");
        FreeFieldPathFromIdx(fieldPath, 0, fieldPos);
        return GRD_LARGE_JSON_NEST;
    }
    char *tmpField = NULL;
    int32_t errCode = GRD_AllocAndInitMem(totalLen, 0, (void **)&tmpField);
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(GRD_FAILED_MEMORY_ALLOCATE, "wrong bufalloc");
        return GRD_FAILED_MEMORY_ALLOCATE;
    }
    if (memcpy_s((char *)tmpField, totalLen, filterField, totalLen - 1) != EOK) {
        (void)GRD_FreeMem(tmpField);
        return GRD_INNER_ERR;
    }
    fieldPath[fieldPos] = tmpField;
    return GRD_OK;
}

static int32_t ParseJsonToProjNode(void *jsonObj, ProjectionNode *root, int32_t *viewType);
static int32_t ParseFieldVal(void *field, ProjectionNode *root, int32_t fieldType, uint32_t depth, int32_t *viewType)
{
    int32_t ret = GRD_OK;
    if (fieldType == DB_JSON_OBJECT) {
        // depth > 1 means it should not have object value
        if (depth > 1) {
            return GRD_INVALID_PROJECTION_VALUE;
        }
        ret = ParseJsonToProjNode(field, LIST_TAIL_ENTRY(&(root->children), ProjListNode, linkedNode)->node, viewType);
        if (ret != GRD_OK) {
            return ret;
        }
    } else {
        Value value;
        ret = GetCurrentItemValue(field, &value);
        if (ret != GRD_OK) {
            return ret;
        }
        ret = CheckFieldVal(&value, viewType);
        if (ret != GRD_OK) {
            return ret;
        }
    }
    return GRD_OK;
}

static int32_t ParseField(void *field, char *fieldsArr[], uint32_t *depth)
{
    if (field == NULL) {
        return GRD_JSON_LIB_HANDLE_FAILED;
    }
    return SplitFieldAndCheck(DbJsonGetObjectFieldName(field), MAX_NESTED_LEN, fieldsArr, depth);
}

static int32_t BuildProjTree(char *fields[], ProjectionNode *root, uint32_t depth, DbJsonTypeE valType)
{
    int32_t ret = 0;
    ProjectionNode *node = root;
    for (uint32_t i = 0; i < depth; i++) {
        char *key = fields[i];
        if (!IsFieldValid(key)) {
            (void)GRD_FreeMem(key);
            FreeFieldPathFromIdx(fields, i + 1, depth);
            return GRD_INVALID_PROJECTION_FIELD;
        }
        ProjectionNode *findValue = LookUpInChildren(node, key);
        if (findValue == NULL) {
            ProjNodeType type = INTERNAL_NODE;
            if (i + 1 >= depth && valType != DB_JSON_OBJECT) {
                type = LEAF_NODE;
            }
            ProjectionNode *child = NULL;
            ret = AllocAndInitProjNode(key, type, node->level + 1, &child);
            if (ret != GRD_OK) {
                FreeFieldPathFromIdx(fields, i, depth);
                return ret;
            }
            ret = InsertInChildren(node, child);
            if (ret != GRD_OK) {
                FreeProjection(child);
                return ret;
            }
            node = child;
            continue;
        }
        ProjectionNode *sameNameNode = findValue;
        (void)GRD_FreeMem(key);
        if (sameNameNode->type != LEAF_NODE) {
            if (i + 1 >= depth && valType != DB_JSON_OBJECT) {
                return GRD_INVALID_PROJECTION_FIELD;
            }
            node = sameNameNode;
            continue;
        }
        if (i + 1 < depth) {
            FreeFieldPathFromIdx(fields, i + 1, depth);
            return GRD_INVALID_PROJECTION_FIELD;
        }
    }
    return GRD_OK;
}

static int32_t ParseJsonToProjNode(void *jsonObj, ProjectionNode *root, int32_t *viewType)
{
    int32_t size = DbJsonGetArraySize(jsonObj);
    int32_t ret = GRD_OK;
    if (root->type != ROOT_NODE && size == 0) {
        DB_LOG_ERROR(GRD_INVALID_PROJECTION_VALUE, "type not root node or size = 0");
        return GRD_INVALID_PROJECTION_VALUE;
    }
    int32_t parentType = DbJsonGetType(jsonObj);
    for (int32_t i = 0; i < size; i++) {
        void *field = DbJsonArrayGet(jsonObj, i);
        int32_t fieldType = DbJsonGetType(field);
        char *fieldsArr[MAX_NESTED_LEN] = {};
        uint32_t depth = 0;
        ret = ParseField(field, fieldsArr, &depth);
        if (ret != GRD_OK) {
            DB_LOG_ERROR(ret, "wrong field-parse");
            return ret;
        }
        // depth is larger than 1 means it's nesting style
        if (parentType == DB_JSON_OBJECT && root->type != ROOT_NODE && depth > 1) {
            FreeFieldPathFromIdx(fieldsArr, 0, depth);
            DB_LOG_ERROR(GRD_INVALID_PROJECTION_VALUE, "parent type obj, but depth > 1");
            return GRD_INVALID_PROJECTION_VALUE;
        }
        ret = BuildProjTree(fieldsArr, root, depth, fieldType);
        if (ret != GRD_OK) {
            DB_LOG_ERROR(ret, "wrong projTree-build");
            return ret;
        }
        ret = ParseFieldVal(field, root, fieldType, depth, viewType);
        if (ret != GRD_OK) {
            DB_LOG_ERROR(ret, "wrong fieldValue-parse");
            return ret;
        }
    }
    return ret;
}

static int32_t InsertId2ProjRoot(ProjectionNode *root)
{
    int32_t ret = GRD_OK;
    ProjectionNode *child = NULL;
    // _id field can only be located at level 1
    ret = AllocAndInitProjNode(NULL, LEAF_NODE, 1, &child);
    if (ret != GRD_OK) {
        DB_LOG_ERROR(ret, "wrong projNode-alloc");
        return ret;
    }
    uint32_t fieldNameLen = strlen(OBJECT_ID_FIELD_NAME) + 1;
    ret = GRD_AllocAndInitMem(fieldNameLen, 0, (void **)&child->fieldName);
    if (ret != GRD_OK) {
        DB_LOG_ERROR(ret, "wrong idStr-alloc");
        FreeProjection(child);
        return ret;
    }
    (void)memcpy_s(child->fieldName, fieldNameLen, OBJECT_ID_FIELD_NAME, fieldNameLen);
    ret = InsertInChildren(root, child);
    if (ret != GRD_OK) {
        DB_LOG_ERROR(ret, "wrong child-insert");
        FreeProjection(child);
    }
    return ret;
}

static int32_t TryInsertId2ProjRoot(ProjectionNode *root, uint32_t flags, int32_t *viewType)
{
    int32_t ret = GRD_OK;
    // Case of empty projection, show all fields.
    if (DbLinkedListEmpty(&root->children)) {
        *viewType = FALSE_VIEW_TYPE;
        if (flags != GRD_DOC_ID_DISPLAY) {
            ret = InsertId2ProjRoot(root);
        }
        return ret;
    }
    if ((flags == GRD_DOC_ID_DISPLAY && *viewType == TRUE_VIEW_TYPE) || (flags == 0 && *viewType == FALSE_VIEW_TYPE)) {
        ret = InsertId2ProjRoot(root);
    }
    return ret;
}

static FilterConditionNode *AllocFilterConditionNode(ConditionNodeType condType, int32_t level)
{
    FilterConditionNode *queryNode = NULL;
    int32_t errCode = GRD_AllocAndInitMem(sizeof(FilterConditionNode), 0, (void **)&queryNode);
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "wrong filterConditionNode-alloc");
        return NULL;
    }
    queryNode->condType = condType;
    queryNode->fieldPathLen = 0;
    for (uint32_t i = 0; i < sizeof(queryNode->fieldPath) / sizeof(char *); i++) {
        queryNode->fieldPath[i] = NULL;
    }
    queryNode->childrenNum = 0;
    queryNode->level = level;
    queryNode->children = NULL;
    if (condType == COND_AND || condType == COND_OR) {
        queryNode->opr = GRD_INVALID;
    }
    return queryNode;
}

static int32_t CheckFieldChildNode(const void *object, int32_t level, bool *isExist)
{
    bool isExistOperator = false;
    DbJsonTypeE type = DbJsonGetType(object);
    if (type != DB_JSON_OBJECT) {
        *isExist = isExistOperator;
        return GRD_OK;
    }
    void *nodeObject = NULL;
    bool isFirstNode = true;
    while (true) {
        nodeObject = DbJsonGetNextNode(object, nodeObject, isFirstNode);
        if (nodeObject == NULL) {
            break;
        }
        isFirstNode = false;
        char *fieldName = DbJsonGetObjectFieldName(nodeObject);
        QuerySelectType fieldType = GetQuerySelectType(fieldName);
        if (fieldType == LOGICAL_TYPE) {
            return GRD_INVALID_OPERATOR;
        }
        if (fieldType != DEFAULT_TYPE) {
            isExistOperator = true;
        }
        if (isExistOperator && fieldType == DEFAULT_TYPE) {
            return GRD_INVALID_ARGS;
        }
        if (fieldType == DEFAULT_TYPE && !IsFieldValid(fieldName)) {
            return GRD_INVALID_ARGS;
        }
        if (!IsDocumentValid(nodeObject, level + 1)) {
            return GRD_INVALID_ARGS;
        }
    }
    *isExist = isExistOperator;
    return GRD_OK;
}

static int32_t ConstructDefaultNode(void *nodeObject, int32_t level, FilterConditionNode **node)
{
    if (node == NULL) {
        return GRD_INVALID_ARGS;
    }
    bool isExistOperator = false;
    int32_t errCode = CheckFieldChildNode(nodeObject, level, &isExistOperator);
    // handle operator scene when support more operator
    if (errCode != GRD_OK || isExistOperator) {
        DB_LOG_ERROR(GRD_INVALID_ARGS, "field child optr type check go wrong");
        return GRD_INVALID_ARGS;
    }
    FilterConditionNode *queryNode = AllocFilterConditionNode(COND_PRED, level);
    if (queryNode == NULL) {
        return GRD_FAILED_MEMORY_ALLOCATE;
    }
    errCode = SplitFieldAndCheck(
        DbJsonGetObjectFieldName(nodeObject), MAX_NESTED_LEN, queryNode->fieldPath, &(queryNode->fieldPathLen));
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "wrong field-split");
        goto ERROR;
    }
    errCode = GetCurrentItemValue(nodeObject, &(queryNode->value));
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "item value get wrong");
        goto ERROR;
    }
    *node = queryNode;
    return errCode;
ERROR:
    FreeFilterCondition(queryNode, true);
    return errCode;
}

static int32_t InnerCreateConFilterCond(
    void *object, int32_t level, bool primaryOperator, int32_t size, FilterConditionNode *parentNode)
{
    if (object == NULL || parentNode == NULL) {
        return GRD_INVALID_ARGS;
    }
    int32_t errCode = GRD_OK;
    void *nodeObject = NULL;
    for (int32_t i = 0; i < size; i++) {
        nodeObject = DbJsonGetNextNode(object, nodeObject, (i == 0));
        if (nodeObject == NULL) {
            return GRD_JSON_LIB_HANDLE_FAILED;
        }
        QuerySelectType type = GetQuerySelectType(DbJsonGetObjectFieldName(nodeObject));
        if (!OperationTypeCheck(nodeObject, type, (level <= 1))) {
            return GRD_INVALID_OPERATOR;
        }
        FilterConditionNode *queryNode = NULL;
        if (type == DEFAULT_TYPE) {
            errCode = ConstructDefaultNode(nodeObject, level, &queryNode);
            if (errCode != GRD_OK) {
                return errCode;
            }
        } else if (type == LOGICAL_TYPE) {
#if defined(GRD_SUPPORT_LOGIC_OPERATION)
            errCode = ConstructLogicOpNode(nodeObject, level, &queryNode);
            if (errCode != GRD_OK) {
                return errCode;
            }
#else
            return GRD_INVALID_OPERATOR;
#endif
        } else {
            return GRD_INVALID_ARGS;
        }
        parentNode->children[parentNode->childrenNum] = queryNode;
        parentNode->childrenNum++;
    }
    return errCode;
}
#if defined(GRD_SUPPORT_LOGIC_OPERATION)
static int32_t ConstructLogicOpNode(const void *object, int32_t level, FilterConditionNode **node)
{
    if (object == NULL || node == NULL) {
        return GRD_INVALID_ARGS;
    }
    DbJsonTypeE type = DbJsonGetType(object);
    if (type != DB_JSON_ARRAY) {
        return GRD_INVALID_ARGS;
    }
    int32_t size = DbJsonGetArraySize(object);
    ConditionNodeType conditionType = IsAndOperator(DbJsonGetObjectFieldName(object)) ? COND_AND : COND_OR;
    FilterConditionNode *queryNode = AllocFilterConditionNode(conditionType, level);
    if (queryNode == NULL) {
        return GRD_FAILED_MEMORY_ALLOCATE;
    }
    int32_t errCode = GRD_AllocAndInitMem(sizeof(FilterConditionNode *) * size, 0, (void **)&(queryNode->children));
    if (errCode != GRD_OK) {
        goto ERROR;
    }
    for (int32_t i = 0; i < size; i++) {
        void *nodeObject = DbJsonArrayGet(object, i);
        if (nodeObject == NULL) {
            errCode = GRD_JSON_LIB_HANDLE_FAILED;
            goto ERROR;
        }
        int32_t innerSize = DbJsonGetArraySize(nodeObject);
        if (DbJsonGetType(nodeObject) != DB_JSON_OBJECT || innerSize <= 0) {
            DB_LOG_ERROR(GRD_INVALID_ARGS, "logic op child node invalid or innerSize is invalid");
            errCode = GRD_INVALID_ARGS;
            goto ERROR;
        }
        errCode = InnerCreateConFilterCond(nodeObject, level + 1, true, innerSize, queryNode);
        if (errCode != GRD_OK) {
            goto ERROR;
        }
    }
    *node = queryNode;
    return errCode;
ERROR:
    FreeFilterCondition(queryNode, true);
    return errCode;
}
#endif

static int32_t UpdateItemValueAtObject(
    void *object, const char *fieldPath[], uint32_t fieldPathLen, uint32_t realPathLen, Value *value)
{
    void *currObject = object;
    void *leafObject = NULL;  // create the leafObject first
    int32_t errCode = GetValueObject(value, &leafObject);
    if (errCode != GRD_OK) {
        return errCode;
    }
    // not going to check if realPathLen > fieldPathLen because the function will control it
    if (realPathLen == fieldPathLen) {  // replace
        if (IsNumber(fieldPath[fieldPathLen - 1])) {
            errno = 0;
            int32_t index = strtol(fieldPath[fieldPathLen - 1], NULL, 10);  // 10 means Decimal
            if (errno != 0) {
                DbJsonDelete(leafObject);
                return GRD_INVALID_ARGS;
            }
            errCode = DbJsonReplaceItemInArray(currObject, index, leafObject);
        } else {
            errCode = DbJsonReplaceFieldItem(currObject, fieldPath[fieldPathLen - 1], leafObject);
        }
        if (errCode != GRD_OK) {
            DbJsonDelete(leafObject);
            return errCode;
        }
    } else if (realPathLen < fieldPathLen) {  // create new object
        if (DbJsonGetType(currObject) != DB_JSON_OBJECT) {
            (void)DbJsonDelete(leafObject);
            DB_LOG_ERROR(GRD_FIELD_TYPE_CONFLICT, "update: data type conflict");
            return GRD_FIELD_TYPE_CONFLICT;
        }
        for (uint32_t i = realPathLen; i < fieldPathLen; i++) {
            if (IsNumber(fieldPath[i])) {
                DbJsonDelete(leafObject);
                DB_LOG_ERROR(GRD_FIELD_TYPE_CONFLICT, "update elem of array go wrong: not exist");
                return GRD_FIELD_TYPE_CONFLICT;
            }
            void *childObject = (i == fieldPathLen - 1) ? leafObject : DbJsonCreateObject();
            errCode = Json2GRDErrNo(DbJsonObjectSetNew(currObject, fieldPath[i], childObject));
            if (errCode == GRD_OK) {
                currObject = childObject;
                continue;
            }
            // only free current new node because we will free the whole rootObject outside
            if (leafObject != childObject) {
                DbJsonDelete(leafObject);
            }
            DbJsonDelete(childObject);
            return errCode;
        }
    }
    return GRD_OK;
}

int32_t CreateOperationContext(char *collectionName, uint32_t flags, OperationContext **ctx)
{
    if (collectionName == NULL || ctx == NULL) {
        return GRD_INVALID_ARGS;
    }
    OperationContext *tmpCtx = NULL;
    int32_t errCode = GRD_AllocAndInitMem(sizeof(OperationContext), 0, (void **)&tmpCtx);
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(GRD_FAILED_MEMORY_ALLOCATE, "wrong bufalloc");
        return GRD_FAILED_MEMORY_ALLOCATE;
    }
    tmpCtx->collectionName = collectionName;
    tmpCtx->flags = flags;
    tmpCtx->opStmt = NULL;
    *ctx = tmpCtx;
    return GRD_OK;
}

void FreeOperationContext(OperationContext *ctx, bool isFreeStmt)
{
    if (ctx == NULL) {
        return;
    }
    (void)GRD_FreeMem((void *)ctx->collectionName);
    if (isFreeStmt) {
        FreeOperationStmt(ctx->opStmt);
    }
    (void)GRD_FreeMem((void *)ctx);
}

int32_t CreateOperationStmt(void *filterObject, void *docObject, OperationStmt **opStmt)
{
    if (opStmt == NULL) {
        return GRD_INVALID_ARGS;
    }
    OperationStmt *tmpOpStmt = NULL;
    int32_t errCode = GRD_AllocAndInitMem(sizeof(OperationStmt), 0, (void **)&tmpOpStmt);
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(GRD_FAILED_MEMORY_ALLOCATE, "wrong bufalloc");
        return GRD_FAILED_MEMORY_ALLOCATE;
    }
    InitOperationStmt(tmpOpStmt);
    if (filterObject != NULL) {
        errCode = FillOperationStmtId(filterObject, tmpOpStmt);
    } else {
        errCode = FillOperationStmtId(docObject, tmpOpStmt);
    }
    if (errCode != GRD_OK) {
        FreeOperationStmt(tmpOpStmt);
        return errCode;
    }
    *opStmt = tmpOpStmt;
    return GRD_OK;
}

int32_t CreateFilterCondition(void *object, int32_t level, FilterConditionNode **query)
{
    if (object == NULL || query == NULL) {
        return GRD_INVALID_ARGS;
    }
    int32_t size = DbJsonGetArraySize(object);
    // level 0
    FilterConditionNode *queryNode = AllocFilterConditionNode(COND_AND, level);
    if (queryNode == NULL) {
        return GRD_FAILED_MEMORY_ALLOCATE;
    }
    if (size == 0) {
        *query = queryNode;
        return GRD_OK;
    }
    int32_t errCode = GRD_AllocAndInitMem(sizeof(FilterConditionNode *) * size, 0, (void **)&(queryNode->children));
    if (errCode != GRD_OK) {
        goto ERROR;
    }
    errCode = InnerCreateConFilterCond(object, level + 1, true, size, queryNode);
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "wrong conditionFilter-create");
        goto ERROR;
    }
    *query = queryNode;
    return GRD_OK;
ERROR:
    FreeFilterCondition(queryNode, true);
    return errCode;
}

void FreeFilterCondition(FilterConditionNode *node, bool isFreeCurrentNode)
{
    if (node == NULL) {
        return;
    }
    for (int32_t i = 0; i < node->childrenNum; i++) {
        FreeFilterCondition(node->children[i], true);
    }
    (void)GRD_FreeMem(node->children);
    for (uint32_t j = 0; j < node->fieldPathLen && j < MAX_NESTED_LEN; j++) {
        (void)GRD_FreeMem(node->fieldPath[j]);
    }
    if (isFreeCurrentNode) {
        (void)GRD_FreeMem(node);
    }
}

int32_t CreateUpdateDataList(void *rootObject, TagLinkedListT *updateDataList, UpdateDataValue **updateDataAddr)
{
    if (rootObject == NULL || updateDataList == NULL || updateDataAddr == NULL) {
        return GRD_INVALID_ARGS;
    }
    int32_t listSize = DbJsonGetArraySize(rootObject);
    if (listSize == 0) {
        return GRD_OK;
    }
    UpdateDataValue *curr = NULL;
    UpdateDataValue *startAddr = NULL;
    int32_t errCode = GRD_AllocAndInitMem(sizeof(UpdateDataValue) * listSize, 0, (void **)&startAddr);
    if (errCode != GRD_OK) {
        return errCode;
    }
    void *object = NULL;
    for (int32_t index = 0; index < listSize; index++) {
        object = DbJsonGetNextNode(rootObject, object, (index == 0));
        if (object == NULL) {  // listSize > 0 already tells this can't be null
            errCode = GRD_INVALID_ARGS;
            goto ERROR;
        }
        curr = startAddr + index;
        DbLinkedListAppend(updateDataList, &curr->linkedNode);
        errCode = GetCurrentItemValue(object, &curr->value);
        if (errCode != GRD_OK) {
            goto ERROR;
        }
        char *field = DbJsonGetObjectFieldName(object);
        errCode = SplitFieldAndCheck(field, MAX_NESTED_LEN, curr->fieldPath, &curr->fieldPathLen);
        if (errCode != GRD_OK) {
            goto ERROR;
        }
        if (curr->fieldPathLen > 0 && curr->fieldPath[0] != NULL && !strcmp(curr->fieldPath[0], OBJECT_ID_FIELD_NAME)) {
            errCode = GRD_INVALID_ARGS;
            DB_LOG_ERROR(GRD_INVALID_ARGS, "wrong field_id-update");
            goto ERROR;
        }
    }
    *updateDataAddr = startAddr;
    return GRD_OK;
ERROR:
    FreeUpdateDataList(updateDataList);
    (void)GRD_FreeMem(startAddr);
    return errCode;
}

void FreeUpdateDataList(TagLinkedListT *updateDataList)
{
    // the process below already covers the situation of empty dataList
    UpdateDataValue *curr = NULL;
    UpdateDataValue *tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(curr, tmp, updateDataList, linkedNode)
    {
        if (curr != NULL) {
            for (uint32_t i = 0; i < curr->fieldPathLen; i++) {
                (void)GRD_FreeMem(curr->fieldPath[i]);
            }
        }
    }
    DbLinkedListInit(updateDataList);  // to make the list status empty
}

int32_t CreateProjection(const char *projection, uint32_t flags, ProjectionNode **projNode, bool *viewType)
{
    if (projection == NULL || projNode == NULL) {
        return GRD_INVALID_ARGS;
    }
    ProjectionNode *root = NULL;
    int32_t tmpViewType = INITIAL_VIEW_TYPE;
    int32_t ret = AllocAndInitProjNode(NULL, ROOT_NODE, 0, &root);  // 0 means first level
    if (ret != GRD_OK) {
        return ret;
    }
    DbJsonT *jsonObj = NULL;
    ret = Json2GRDErrNo(DbJsonStrTransToJsonObject(projection, true, true, &jsonObj));
    if (ret != GRD_OK || jsonObj == NULL) {
        FreeProjection(root);
        return ret;
    }
    ret = ParseJsonToProjNode(jsonObj, root, &tmpViewType);
    DbJsonDelete(jsonObj);
    if (ret != GRD_OK) {
        FreeProjection(root);
        return ret;
    }
    ret = TryInsertId2ProjRoot(root, flags, &tmpViewType);
    if (ret != GRD_OK) {
        FreeProjection(root);
        return ret;
    }
    *projNode = root;
    *viewType = (bool)tmpViewType;
    return GRD_OK;
}

void FreeProjection(ProjectionNode *projection)
{
    if (projection == NULL) {
        return;
    }
    FreeProjList(&(projection->children));
    (void)GRD_FreeMem(projection->fieldName);
    (void)GRD_FreeMem(projection);
}

int32_t GetUpsertOpStmt(const char *filter, const char *document, OperationStmt **opStmt)
{
    if (document == NULL || opStmt == NULL) {
        return GRD_INVALID_ARGS;
    }
    if (strlen(document) > MAX_JSON_LEN || (filter != NULL && strlen(filter) > MAX_JSON_LEN)) {
        return GRD_OVER_LIMIT;
    }
    DbJsonT *docObj = NULL;
    int32_t errCode = Json2GRDErrNo(DbJsonStrTransToJsonObject(document, false, true, &docObj));
    if (errCode != GRD_OK) {
        return errCode;
    }
    if (!IsDocumentValid(docObj, 0)) {
        DbJsonDelete(docObj);
        return GRD_INVALID_ARGS;
    }
    // if upsert doc, the filter is not NULL
    errCode = CheckIdFormat(docObj, (filter != NULL));
    if (errCode != GRD_OK) {
        DbJsonDelete(docObj);
        return errCode;
    }
    FilterConditionNode *query = NULL;
    OperationStmt *tmpOpStmt = NULL;
    void *filterObj = NULL;
    if (filter != NULL) {
        errCode = CreateConditionObject(filter, &filterObj, &query);
        if (errCode != GRD_OK) {
            DbJsonDelete(docObj);
            return errCode;
        }
    }
    errCode = InitOpStmt(filterObj, docObj, document, &tmpOpStmt);
    if (errCode != GRD_OK) {
        goto ERROR;
    }
    tmpOpStmt->filterObject = filterObj;
    tmpOpStmt->object = docObj;
    *opStmt = tmpOpStmt;
    (*opStmt)->query = query;
    return GRD_OK;
ERROR:
    FreeFilterCondition(query, true);
    DbJsonDelete(docObj);
    DbJsonDelete(filterObj);
    FreeOperationStmt(tmpOpStmt);
    return errCode;
}

int32_t GetFilterOpStmt(const char *filter, const char *projection, uint32_t flags, OperationStmt **opStmt)
{
    if (filter == NULL || opStmt == NULL || projection == NULL) {
        return GRD_INVALID_ARGS;
    }
    if (strlen(filter) > MAX_JSON_LEN || strlen(projection) > MAX_JSON_LEN) {
        return GRD_OVER_LIMIT;
    }
    void *filterObject = NULL;
    FilterConditionNode *query = NULL;
    int32_t errCode = CreateConditionObject(filter, &filterObject, &query);
    if (errCode != GRD_OK) {
        return errCode;
    }
    ProjectionNode *projectionInfo = NULL;
    bool viewType = TRUE_VIEW_TYPE;
    errCode = CreateProjection(projection, flags, &projectionInfo, &viewType);
    if (errCode != GRD_OK) {
        DbJsonDelete(filterObject);
        FreeFilterCondition(query, true);
        return errCode;
    }
    OperationStmt *tmpOpStmt = NULL;
    errCode = CreateOperationStmt(filterObject, NULL, &tmpOpStmt);
    if (errCode != GRD_OK) {
        DbJsonDelete(filterObject);
        FreeFilterCondition(query, true);
        FreeProjection(projectionInfo);
        return errCode;
    }
    tmpOpStmt->filterObject = filterObject;
    tmpOpStmt->query = query;
    tmpOpStmt->viewType = viewType;
    tmpOpStmt->projectionInfo = projectionInfo;
    *opStmt = tmpOpStmt;
    return GRD_OK;
}

int32_t GetUpdateOpStmt(const char *filter, const char *update, OperationStmt **opStmt)
{
    if (filter == NULL || opStmt == NULL || update == NULL) {
        return GRD_INVALID_ARGS;
    }
    if (strlen(filter) > MAX_JSON_LEN || strlen(update) > MAX_JSON_LEN) {
        return GRD_OVER_LIMIT;
    }
    void *filterObject = NULL;
    FilterConditionNode *query = NULL;
    int32_t errCode = CreateConditionObject(filter, &filterObject, &query);
    if (errCode != GRD_OK) {
        return errCode;
    }
    OperationStmt *tmpOpStmt = NULL;
    errCode = CreateOperationStmt(filterObject, NULL, &tmpOpStmt);
    if (errCode != GRD_OK) {
        DbJsonDelete(filterObject);
        FreeFilterCondition(query, true);
        return errCode;
    }
    tmpOpStmt->query = query;
    tmpOpStmt->filterObject = filterObject;
    DbJsonT *rootObject = NULL;
    errCode = Json2GRDErrNo(DbJsonStrTransToJsonObject(update, false, true, &rootObject));
    if (errCode != GRD_OK) {
        FreeOperationStmt(tmpOpStmt);
        return errCode;
    }
    tmpOpStmt->object = rootObject;
    errCode = CreateUpdateDataList(rootObject, &tmpOpStmt->updateData, &tmpOpStmt->updateDataPtr);
    if (errCode != GRD_OK) {
        FreeOperationStmt(tmpOpStmt);
        return errCode;
    }
    *opStmt = tmpOpStmt;
    return GRD_OK;
}

void FreeOperationStmt(OperationStmt *opStmt)
{
    if (opStmt == NULL) {
        return;
    }
    FreeFilterCondition(opStmt->query, true);
    FreeProjection(opStmt->projectionInfo);
    FreeStmtValue(opStmt->value, opStmt->isValueAllocInner);
    FreeUpdateDataList(&opStmt->updateData);
    (void)GRD_FreeMem(opStmt->updateDataPtr);
    DbJsonDelete(opStmt->filterObject);
    DbJsonDelete(opStmt->object);
    (void)GRD_FreeMem(opStmt);
}

int32_t FilterValueByAbsField(void *object, FilterConditionNode *conditionNode, bool *result)
{
    return InnerFilterValueByAbsField(object, conditionNode, 0, true, result);
}

int32_t UpdateItemValueByAbsField(
    void *object, const char *fieldPath[], uint32_t fieldPathLen, Value *value, uint32_t flags)
{
    void *currObject = NULL;
    uint32_t realPathLen = 0;
    // get the longest existing path of the object from fieldPath , which is realPathLen
    int32_t errCode = GetItemParentObjectFromPath(object, fieldPath, fieldPathLen, &currObject, &realPathLen);
    if (errCode != GRD_OK) {
        return errCode;
    }
    if (realPathLen < fieldPathLen && flags == GRD_DOC_REPLACE) {
        return GRD_FIELD_NOT_FOUND;
    }
    return UpdateItemValueAtObject(currObject, fieldPath, fieldPathLen, realPathLen, value);
}

int32_t GetItemValueByField(const void *object, const char *field, Value *value)
{
    if (value == NULL || object == NULL) {
        return GRD_INVALID_ARGS;
    }
    void *fieldObject = DbJsonObjectGetCaseSensitive(object, field, true);
    if (fieldObject == NULL) {
        return GRD_FIELD_NOT_FOUND;
    }
    return GetCurrentItemValue(fieldObject, value);
}

int32_t FillOperationStmtId(void *object, OperationStmt *stmt)
{
    if (object == NULL) {
        return GRD_INVALID_ARGS;
    }
    int32_t errCode = GetItemValueByField(object, OBJECT_ID_FIELD_NAME, &(stmt->id));
    if (errCode != GRD_OK && errCode != GRD_FIELD_NOT_FOUND) {
        return errCode;
    }
    if (errCode == GRD_FIELD_NOT_FOUND) {
        stmt->idType = GRD_AUTO;
    } else {
        stmt->idType = GRD_MANUAL;
        if (stmt->id.type != DB_JSON_STRING) {
            DB_LOG_ERROR(GRD_FIELD_TYPE_NOT_MATCH, "id type invalid");
            return GRD_FIELD_TYPE_NOT_MATCH;
        }
        // value.length is set in get item value function
        if (stmt->id.length > MAX_ID_STRING_LEN) {
            DB_LOG_ERROR(GRD_OVER_LIMIT, "id str len > max len");
            return GRD_OVER_LIMIT;
        }
    }
    return GRD_OK;
}

int32_t IsValueMatchFilter(void *object, FilterConditionNode *currentNode, bool *result)
{
    // Only support AND and PRED type currently
    if (currentNode->condType != COND_AND && currentNode->condType != COND_PRED) {
        return GRD_INVALID_OPERATOR;
    }
    // Skip _id in query
    if (currentNode->skipId) {
        *result = true;
        return GRD_OK;
    }

    bool rst;
    if (currentNode->condType == COND_PRED) {
        int32_t rstCode = FilterValueByAbsField(object, currentNode, &rst);
        if (rstCode != GRD_OK) {
            return rstCode;
        }
        *result = rst;
        return GRD_OK;
    }

    for (int32_t i = 0; i < currentNode->childrenNum; i++) {
        bool childRst;
        int32_t rstCode = IsValueMatchFilter(object, (currentNode->children)[i], &childRst);
        if (rstCode != GRD_OK) {
            return rstCode;
        }
        if (childRst == false) {
            *result = false;
            return GRD_OK;
        }
    }

    *result = true;
    return GRD_OK;
}

ProjectionNode *SearchTargetProjectionNode(const char *targetField, const ProjectionNode *projectionNode)
{
    if (targetField == NULL) {
        return NULL;
    }
    return LookUpInChildren(projectionNode, targetField);
}

void ProjectValueWithRetain(void *parent, const ProjectionNode *projectionNode, bool *hasChildMatch)
{
    void *currentJsonNode = DbJsonObjectIter(parent);
    if (currentJsonNode == NULL || projectionNode->type == LEAF_NODE) {
        *hasChildMatch = true;
        return;
    }
    bool hasMatch = false;
    while (currentJsonNode != NULL) {
        char *targetField = DbJsonGetObjectFieldName(currentJsonNode);
        ProjectionNode *targetProjectionNode = SearchTargetProjectionNode(targetField, projectionNode);
        ProjectionNode *siblingNode = DbJsonGetNext(currentJsonNode);
        // Cannot find match current with projection, remove current json node.
        if (targetProjectionNode == NULL) {
            currentJsonNode = siblingNode;
            DbJsonDeleteItemFromObject(parent, targetField);
            continue;
        }
        hasMatch = true;
        // If projection contains children and jsonTree is array or non-child, this jsonTree node should be deleted,
        // because all child fields in projection will not display.
        if ((DbJsonObjectIter(currentJsonNode) == NULL || DbJsonGetType(currentJsonNode) == DB_JSON_ARRAY) &&
            !DbLinkedListEmpty(&targetProjectionNode->children)) {
            currentJsonNode = siblingNode;
            DbJsonDeleteItemFromObject(parent, targetField);
            continue;
        }
        // If children of current jsonNode are not matching target projection node,
        // the currentJsonNode should be deleted.
        bool innerChildMatch = false;
        ProjectValueWithRetain(currentJsonNode, targetProjectionNode, &innerChildMatch);
        if (!innerChildMatch) {
            DbJsonDeleteItemFromObject(parent, targetField);
        }
        currentJsonNode = siblingNode;
    }
    *hasChildMatch = hasMatch;
}

static void InnerProjValWithDel(void *object, const TagLinkedListT *projList)
{
    ProjListNode *curr = NULL;
    ProjListNode *tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(curr, tmp, projList, linkedNode)
    {
        ProjectValueWithDelete(object, curr->node);
    }
}

// Traverse projection tree to prune the cjson tree
void ProjectValueWithDelete(void *object, const ProjectionNode *projectionNode)
{
    if (projectionNode->type == ROOT_NODE) {
        InnerProjValWithDel(object, (const TagLinkedListT *)&(projectionNode->children));
        return;
    }
    if (projectionNode->type == LEAF_NODE) {
        DbJsonDeleteItemFromObject(object, projectionNode->fieldName);
        return;
    }

    void *targetNode = DbJsonObjectGetCaseSensitive(object, projectionNode->fieldName, true);
    InnerProjValWithDel(targetNode, (const TagLinkedListT *)&(projectionNode->children));
    // All children nodes cannot be seen.
    if (DbJsonObjectIter(targetNode) == NULL) {
        DbJsonDeleteItemFromObject(object, projectionNode->fieldName);
    }
}

int32_t GetItemParentObjectFromPath(
    void *object, const char *fieldPath[], uint32_t fieldPathLen, void **parentObject, uint32_t *realPathLen)
{
    if (object == NULL || fieldPath == NULL || parentObject == NULL || realPathLen == NULL) {
        return GRD_INVALID_ARGS;
    }
    void *currentObject = object;
    uint32_t fieldLen = 0;
    for (uint32_t i = 0; i < fieldPathLen; i++) {
        if (fieldPath[i] == NULL) {
            return GRD_INVALID_ARGS;
        }
        void *fieldObject = NULL;
        if (DbJsonGetType(currentObject) == DB_JSON_ARRAY) {
            int32_t errCode = GetObjectFromArray(currentObject, fieldPath[i], &fieldObject);
            // for update, only index invalid need return invalid, otherwise try to update its field value
            if (errCode == GRD_ARRAY_INDEX_NOT_FOUND) {
                return errCode;
            }
        } else {
            fieldObject = DbJsonObjectGetCaseSensitive(currentObject, fieldPath[i], true);
        }
        if (fieldObject == NULL) {
            break;
        }
        if (fieldLen < (fieldPathLen - 1)) {
            currentObject = fieldObject;
        }
        fieldLen++;
    }
    *parentObject = currentObject;
    *realPathLen = fieldLen;
    return GRD_OK;
}

int32_t SplitFieldAndCheck(const char *filterField, int32_t maxFieldLen, char *fieldPath[], uint32_t *fieldPathLen)
{
    if (filterField == NULL || maxFieldLen <= 0) {
        return GRD_INVALID_ARGS;
    }
    uint32_t startIndex = 0;
    uint32_t endIndex = 0;
    int32_t fieldNestSize = 0;
    int32_t ret = GRD_INVALID_ARGS;
    const char *field = filterField;
    uint32_t numCount = 0;
    bool isFirstNum = false;
    for (; (*field) != '\0'; field++) {
        endIndex++;
        if (*field == '.') {
            if (numCount < (endIndex - startIndex - 1) && isFirstNum) {
                goto ERROR;
            }
            ret = SetFieldPath(filterField + startIndex, endIndex - startIndex, maxFieldLen, fieldPath, fieldNestSize);
            if (ret != GRD_OK) {
                goto ERROR;
            }
            fieldNestSize++;
            startIndex = endIndex;
            isFirstNum = false;
            numCount = 0;
        } else {
            ret = FilterFieldCheck(filterField, field, startIndex, &isFirstNum, &numCount);
            if (ret != GRD_OK) {
                goto ERROR;
            }
        }
    }
    if (endIndex++ == 0) {
        return GRD_INVALID_ARGS;
    }
    if (numCount < (endIndex - startIndex - 1) && isFirstNum) {
        ret = GRD_INVALID_ARGS;
        goto ERROR;
    }
    ret = SetFieldPath(filterField + startIndex, endIndex - startIndex, maxFieldLen, fieldPath, fieldNestSize);
    if (ret != GRD_OK) {
        goto ERROR;
    }
    fieldNestSize++;
    *fieldPathLen = fieldNestSize;
    return GRD_OK;
ERROR:
    FreeFieldPathFromIdx(fieldPath, 0, maxFieldLen);
    return ret;
}

void FreeFieldPathFromIdx(char *fieldPath[], uint32_t idx, uint32_t fieldLen)
{
    if (idx >= fieldLen) {
        return;
    }
    for (uint32_t i = idx; i < fieldLen; i++) {
        (void)GRD_FreeMem(fieldPath[i]);
        fieldPath[i] = NULL;
    }
}

int32_t GetStringValue(const void *object, Value *value)
{
    char *dstValue = NULL;
    uint32_t strLen = 0;
    int32_t errCode = DbJsonGetItemString(object, &dstValue, &strLen);
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "set obj item str go wrong");
        return errCode;
    }
    value->value.byteValue = dstValue;
    value->length = strLen;
    value->type = DB_JSON_STRING;
    return GRD_OK;
}
