/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "json_result_set.h"

#include "db_log.h"
#include "db_json.h"
#include "document_key.h"
#include "filter.h"
#include "grd_error.h"
#include "grd_utils.h"
#include "operation_type.h"
#include "os_posix.h"
#include "parser.h"

static int32_t InnerFindParametersCheck(GRD_DB *db, JsonResultSet *resultSet)
{
    if (resultSet == NULL) {
        DB_LOG_ERROR(GRD_INVALID_ARGS, "resultSet args invalid");
        return GRD_INVALID_ARGS;
    }
    if (db == NULL || (resultSet->cursor.cursor == NULL && resultSet->cursor.nbCursor == NULL) ||
        resultSet->context == NULL) {
        DB_LOG_ERROR(GRD_INVALID_ARGS, "resultSet inner args invalid");
        return GRD_INVALID_ARGS;
    }
    if (db->conn == NULL && db->nbConn == NULL) {
        DB_LOG_ERROR(GRD_INVALID_ARGS, "db connection NULL");
        return GRD_INVALID_ARGS;
    }
    // opStmt->query can be NULL, which means match all.
    // opStmt->projectionInfo is NULL for update，upsert，delete.
    OperationContext *context = resultSet->context;
    if (context->collectionName == NULL || context->opStmt == NULL) {
        DB_LOG_ERROR(GRD_INVALID_ARGS, "collection or stmt NULL");
        return GRD_INVALID_ARGS;
    }

    return GRD_OK;
}

static void ProcessJsonTreeAccordingToProjection(OperationStmt *rstSetOprStmt, void *jsonTree)
{
    ProjectionNode *projectionRoot = rstSetOprStmt->projectionInfo;
    // The NULL case means the projection root is from delete, update and upsert.
    if (projectionRoot == NULL) {
        return;
    }
    if (rstSetOprStmt->viewType) {
        bool hasChildMatch = false;
        ProjectValueWithRetain(jsonTree, projectionRoot, &hasChildMatch);
        return;
    }
    ProjectValueWithDelete(jsonTree, projectionRoot);
}

static int32_t UpdateJsonResultSet(void *object, GRD_KVItemT *key, JsonResultSet *resultSet, bool hasId, bool isGetKey)
{
    if (isGetKey) {
        if (!hasId) {  // key is alloc by rd_kernel
            char *copyKey = NULL;
            int32_t errCode = AllocAndCopyString(key->data, &copyKey);
            if (errCode != GRD_OK) {
                return errCode;
            }
            resultSet->key = copyKey;
            resultSet->keyLen = key->dataLen;
        } else {
            resultSet->key = key->data;
            resultSet->keyLen = key->dataLen;
        }
    }
    resultSet->object = object;
    return GRD_OK;
}

static int32_t SearchForTargetRecord(JsonResultSet *resultSet, GRD_KVItemT *key, GRD_KVItemT *value, void **object)
{
    DbJsonT *jsonTree = NULL;
    bool matchRst = false;
    while (true) {
        int32_t errCode = KernelKvScanMove(&resultSet->cursor, GRD_MOVE_NEXT);
        if (errCode != GRD_OK) {
            DB_LOG_ERROR(errCode, "wrong kvScan-move");
            return errCode;
        }

        errCode = KernelKvScanFetch(&resultSet->cursor, key, value);
        if (errCode != GRD_OK) {
            DB_LOG_ERROR(errCode, "wrong kvScan-fetch");
            return errCode;
        }
        // Translate to cjson tree
        errCode = Json2GRDErrNo(DbJsonStrTransToJsonObject(value->data, true, true, &jsonTree));
        if (errCode != GRD_OK) {
            DB_LOG_ERROR(errCode, "translate to json obj go wrong");
            FreeKeyAndValue(key, value);
            return errCode;
        }
        // Empty query means match all records.
        if (resultSet->context->opStmt->query == NULL) {
            matchRst = true;
            break;
        }
        errCode = IsValueMatchFilter(jsonTree, resultSet->context->opStmt->query, &matchRst);
        if (errCode != GRD_OK) {
            DB_LOG_ERROR(errCode, "wrong filter-match");
            FreeKeyAndValue(key, value);
            DbJsonDelete(jsonTree);
            return errCode;
        }
        if (matchRst) {
            break;
        }
        // Mismatch, free the key, value and jsonTree.
        FreeKeyAndValue(key, value);
        DbJsonDelete(jsonTree);
        jsonTree = NULL;
    }
    if (!matchRst) {
        return GRD_RECORD_NOT_FOUND;
    }
    *object = jsonTree;
    return GRD_OK;
}

static void SetMarkForIdFilterNode(FilterConditionNode *query)
{
    for (int32_t i = 0; i < query->childrenNum; i++) {
        FilterConditionNode *children = query->children[i];
        if (strcmp(children->fieldPath[0], OBJECT_ID_FIELD_NAME) == 0) {
            children->skipId = true;
            break;
        }
    }
}

static void ReleaseResource(DocKey *docKey, GRD_KVItemT *tmpValue, void *jsonTree)
{
    FreeDocKey(docKey);
    KernelKvFreeItem(tmpValue);
    DbJsonDelete(jsonTree);
}

static int32_t GetValueAccordingToId(
    Value *idValue, ResultSet resultSet, GRD_KVItemT *key, GRD_KVItemT *value, void **object)
{
    DocKey *docKey = NULL;
    int32_t errCode = GenDocKey(idValue, &docKey);
    if (errCode != GRD_OK) {
        return errCode;
    }
    GRD_KVItemT tmpKey = {docKey->key, strlen(docKey->key) + 1};
    GRD_KVItemT tmpValue = {0};
    JsonResultSet *jsonResultSet = (JsonResultSet *)resultSet.resultSet;
    errCode = KernelKvGet(resultSet.db, jsonResultSet->context->collectionName, &tmpKey, &tmpValue);
    if (errCode == GRD_NO_DATA) {
        FreeDocKey(docKey);
        return GRD_RECORD_NOT_FOUND;
    }
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "kv record get wrong");
        FreeDocKey(docKey);
        return errCode;
    }
    DbJsonT *jsonTree = NULL;
    errCode = Json2GRDErrNo(DbJsonStrTransToJsonObject(tmpValue.data, true, true, &jsonTree));
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "translate to json obj go wrong");
        FreeDocKey(docKey);
        KernelKvFreeItem(&tmpValue);
        return errCode;
    }
    bool matchRst = false;
    SetMarkForIdFilterNode(jsonResultSet->context->opStmt->query);
    errCode = IsValueMatchFilter(jsonTree, jsonResultSet->context->opStmt->query, &matchRst);
    if (errCode != GRD_OK || !matchRst) {
        ReleaseResource(docKey, &tmpValue, jsonTree);
        return errCode != GRD_OK ? errCode : GRD_RECORD_NOT_FOUND;
    }

    errCode = AllocAndCopyString(docKey->key, (char **)&key->data);
    if (errCode != GRD_OK) {
        ReleaseResource(docKey, &tmpValue, jsonTree);
        return errCode;
    }
    FreeDocKey(docKey);
    key->dataLen = (uint32_t)strlen(key->data) + 1;
    value->data = tmpValue.data;
    value->dataLen = tmpValue.dataLen;

    *object = jsonTree;
    return GRD_OK;
}

static int32_t AddIdToJsonTree(GRD_KVItemT *key, void *object)
{
    // Do not need to Insert _id field if it has already existed.
    if (DbJsonObjectGetCaseSensitive(object, OBJECT_ID_FIELD_NAME, true) != NULL) {
        return GRD_OK;
    }
    DocId *id = NULL;
    int32_t errorCode = ParseKey((char *)key->data, &id);
    if (errorCode != GRD_OK) {
        return errorCode;
    }
    void *idNode = DbJsonCreateString(id->value.dataBuff);
    FreeDocId(id);
    errorCode = Json2GRDErrNo(DbJsonSetItemFieldName(OBJECT_ID_FIELD_NAME, idNode));
    if (errorCode != GRD_OK) {
        DB_LOG_ERROR(errorCode, "wrong fieldName-set");
        goto ERROR;
    }

    // The automatically generate _id field is always in the first place.
    errorCode = Json2GRDErrNo(DbJsonInsertItemObject(object, 0, idNode));
    if (errorCode != GRD_OK) {
        DB_LOG_ERROR(errorCode, "wrong obj-insert");
        goto ERROR;
    }
    return GRD_OK;
ERROR:
    DbJsonDelete(idNode);
    return errorCode;
}

static bool IsFilterContainId(OperationStmt *operationStmt)
{
    if (operationStmt->idType == GRD_AUTO) {
        return false;
    }
    if (operationStmt->id.type == DB_JSON_STRING) {
        return true;
    }
    return false;
}

int32_t InnerGetJsonValue(JsonResultSet *jsonResultSet, char **value)
{
    if (jsonResultSet == NULL || value == NULL) {
        return GRD_INVALID_ARGS;
    }
    if (jsonResultSet->cursor.cursor == NULL && jsonResultSet->cursor.nbCursor == NULL) {
        DB_LOG_ERROR(GRD_INVALID_ARGS, "cursor is null when json get value");
        return GRD_INVALID_ARGS;
    }
    if (jsonResultSet->object == NULL) {
        return GRD_RESULT_SET_NOT_AVAILABLE;
    }
    char *copyValue = NULL;
    int32_t errCode = GRD_OK;
    char *finalValue = NULL;
    if (jsonResultSet->value == NULL) {
        errCode = Json2GRDErrNo(DbJsonTranslateToString(jsonResultSet->object, &finalValue));
        if (errCode != GRD_OK) {
            DB_LOG_ERROR(errCode, "wrong resultSet-Translate");
            return errCode;
        }
        if (jsonResultSet->value != NULL) {
            DbJsonFree(jsonResultSet->value);
            jsonResultSet->value = NULL;
        }
        jsonResultSet->value = finalValue;
    }
    uint32_t valueLen = (uint32_t)strlen(jsonResultSet->value);
    errCode = GRD_AllocAndInitMem(valueLen + 1, 0, (void **)&copyValue);
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "wrong value-alloc");
        return errCode;
    }

    (void)memcpy_s(copyValue, valueLen + 1, jsonResultSet->value, valueLen);
    *value = copyValue;
    return GRD_OK;
}

int32_t InnerGetJsonKey(JsonResultSet *resultSet, char **key, uint32_t *len)
{
    if (resultSet == NULL || key == NULL || len == NULL) {
        return GRD_INVALID_ARGS;
    }
    if (resultSet->key == NULL) {
        return GRD_RESULT_SET_NOT_AVAILABLE;
    }
    *key = resultSet->key;
    *len = resultSet->keyLen;
    return GRD_OK;
}

void InnerJsonFreeValue(char *value)
{
    (void)GRD_FreeMem(value);
}

void InnerFreeKeyAndValueObject(JsonResultSet *resultSet)
{
    if (resultSet == NULL) {
        return;
    }
    if (resultSet->key != NULL) {
        (void)GRD_FreeMem(resultSet->key);
        resultSet->key = NULL;
        resultSet->keyLen = 0;
    }
    if (resultSet->value != NULL) {
        DbJsonFree(resultSet->value);
        resultSet->value = NULL;
    }
    if (resultSet->object != NULL) {
        DbJsonDelete(resultSet->object);
        resultSet->object = NULL;
    }
}
static int32_t GetRecordForJsonNext(
    OperationStmt *rstSetOprStmt, ResultSet resultSet, GRD_KVItemT *key, GRD_KVItemT *value, void **jsonTree)
{
    int32_t errCode = GRD_OK;
    JsonResultSet *jsonResultSet = (JsonResultSet *)resultSet.resultSet;
    bool hasId = IsFilterContainId(rstSetOprStmt);
    if (hasId) {
        // Only allowed to use _id in next method once.
        if (jsonResultSet->usedIdQueryBefore) {
            return GRD_RECORD_NOT_FOUND;
        }
        errCode = GetValueAccordingToId(&rstSetOprStmt->id, resultSet, key, value, jsonTree);
        if (errCode != GRD_OK) {
            return errCode;
        }
        jsonResultSet->usedIdQueryBefore = true;
    } else {
        errCode = SearchForTargetRecord(jsonResultSet, key, value, jsonTree);
    }
    return errCode;
}

int32_t InnerJsonNext(ResultSet resultSet, bool isGetKey)
{
    JsonResultSet *jsonResultSet = (JsonResultSet *)resultSet.resultSet;
    if (jsonResultSet->cursor.cursor == NULL && jsonResultSet->cursor.nbCursor == NULL) {
        DB_LOG_ERROR(GRD_INVALID_ARGS, "cursor is null when json move next");
        return GRD_INVALID_ARGS;
    }
    int32_t errCode = InnerFindParametersCheck(resultSet.db, jsonResultSet);
    if (errCode != GRD_OK) {
        return errCode;
    }
    OperationStmt *rstSetOprStmt = jsonResultSet->context->opStmt;
    // reset key/value, need release key and value before call next
    InnerFreeKeyAndValueObject(jsonResultSet);

    // If filter contains "_id" field, use "_id" to get value from DB.
    // Otherwise, scan all records.
    GRD_KVItemT key, value;
    void *jsonTree = NULL;

    errCode = GetRecordForJsonNext(rstSetOprStmt, resultSet, &key, &value, &jsonTree);
    if (errCode != GRD_OK) {
        return errCode;
    }
    bool hasId = IsFilterContainId(rstSetOprStmt);
    // False value means normal query. Otherwise, indicates Update, upsert, delete methods.
    if (!isGetKey) {
        errCode = AddIdToJsonTree(&key, jsonTree);
        if (errCode != GRD_OK) {
            DbJsonDelete(jsonTree);
            goto RELEASE_RESOURCE;
        }
    }
    ProcessJsonTreeAccordingToProjection(rstSetOprStmt, jsonTree);
    errCode = UpdateJsonResultSet((void *)jsonTree, &key, jsonResultSet, hasId, isGetKey);
RELEASE_RESOURCE:
    if (!isGetKey && hasId) {
        (void)GRD_FreeMem(key.data);
        key.data = NULL;
        key.dataLen = 0;
    }
    if (!hasId) {
        KernelKvFreeItem(&key);
    }
    KernelKvFreeItem(&value);
    return errCode;
}

int32_t InnerFreeJsonResultSet(GRD_DB *db, JsonResultSet *resultSet, bool isUpdateList, bool isFreeCtx)
{
    if (resultSet->cursor.cursor == NULL && resultSet->cursor.nbCursor == NULL) {
        DB_LOG_ERROR(GRD_INVALID_ARGS, "cursor is null when json free result set");
        return GRD_INVALID_ARGS;
    }
    if (resultSet->context == NULL) {
        DB_LOG_ERROR(GRD_INVALID_ARGS, "should not change kv result set to json result set.");
        return GRD_INVALID_ARGS;
    }
    // Free Connection and cursor
    int32_t errCode = KernelKvScanEnd(db, &resultSet->cursor);
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "kv scan end go wrong");
        return errCode;
    }
    if (isUpdateList) {
        DecreaseResultSetListCount(db, resultSet->context->collectionName);
    }
    // Free OperationContext
    if (isFreeCtx) {
        FreeOperationContext(resultSet->context, true);
    }
    InnerFreeKeyAndValueObject(resultSet);

    (void)GRD_FreeMem(resultSet);
    return GRD_OK;
}

int32_t CreateJsonResultSet(GRD_DB *db, OperationContext *context, JsonResultSet **resultSet)
{
    int32_t errCode = GRD_AllocAndInitMem(sizeof(JsonResultSet), 0, (void **)resultSet);
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(GRD_FAILED_MEMORY_ALLOCATE, "fail to create json result set");
        return GRD_FAILED_MEMORY_ALLOCATE;
    }
    errCode = InnerFindDoc(db, context, *resultSet);
    if (errCode != GRD_OK) {
        return InnerFreeJsonResultSet(db, *resultSet, false, true);
    }
    return errCode;
}
