/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "update.h"

#include "db_log.h"
#include "db_json.h"
#include "document_check.h"
#include "filter.h"
#include "grd_error.h"
#include "grd_utils.h"
#include "json_result_set.h"
#include "os_posix.h"
#include "parser.h"
#include "store_limits.h"

int32_t UpdateSingleDoc(GRD_DB *db, JsonResultSet *resultSet, uint32_t flags)
{
    if (DbLinkedListEmpty(&resultSet->context->opStmt->updateData)) {
        // means nothing update, so not need to set again
        return GRD_OK;
    }
    if (resultSet->object == NULL) {
        return GRD_RESULT_SET_NOT_AVAILABLE;
    }
    void *rootObject = resultSet->object;
    int32_t errCode = GRD_OK;
    UpdateDataValue *currNode = NULL;
    LIST_FOR_EACH_ENTRY(currNode, &resultSet->context->opStmt->updateData, linkedNode)
    {
        errCode = UpdateItemValueByAbsField(
            rootObject, (const char **)(uintptr_t)currNode->fieldPath, currNode->fieldPathLen, &currNode->value, flags);
        if (errCode != GRD_OK) {
            return errCode;
        }
    }
    if (!IsDocumentValid(rootObject, 0)) {
        // we know that this is caused by GRD_LARGE_JSON_NEST
        DB_LOG_ERROR(GRD_LARGE_JSON_NEST, "update: doc invalid");
        return GRD_LARGE_JSON_NEST;
    }
    char *jsonStr = NULL;
    errCode = Json2GRDErrNo(DbJsonTranslateToString(rootObject, &jsonStr));
    if (errCode != GRD_OK) {
        return errCode;
    }
    size_t jsonStrLen = strlen(jsonStr);
    if (jsonStrLen > MAX_JSON_LEN) {
        DB_LOG_ERROR(GRD_OVER_LIMIT, "update: doc len over limit");
        DbJsonFree(jsonStr);
        return GRD_OVER_LIMIT;
    }
    GRD_KVItemT keyItem = {0};
    errCode = InnerGetJsonKey(resultSet, (char **)&keyItem.data, &keyItem.dataLen);
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "update: inner key get wrong");
        DbJsonFree(jsonStr);
        return errCode;
    }
    GRD_KVItemT valueItem = {.data = jsonStr, .dataLen = jsonStrLen + 1};
    errCode = KernelKvSet(db, resultSet->context->collectionName, &keyItem, &valueItem);
    DbJsonFree(jsonStr);
    return errCode;
}

int32_t UpdateDoc(GRD_DB *db, OperationContext *opContext)
{
    if (db == NULL || opContext == NULL) {
        DB_LOG_ERROR(GRD_INVALID_ARGS, "update doc args invalid");
        return GRD_INVALID_ARGS;
    }
    int32_t updateCount = 0;
    JsonResultSet *jsonResultSet = NULL;
    int32_t errCode = CreateJsonResultSet(db, opContext, &jsonResultSet);
    if (errCode != GRD_OK) {
        return GetErrorCategory(errCode);
    }

    ResultSet resultSet = {.db = db, .resultSet = jsonResultSet, .type = JSON_TYPE};
    errCode = InnerNext(resultSet, true);
    if (errCode != GRD_OK) {
        InnerFreeJsonResultSet(db, jsonResultSet, false, false);
        // if there is no k-v pair matched, return 0
        return (errCode == GRD_RECORD_NOT_FOUND) ? updateCount : GetErrorCategory(errCode);
    }
    errCode = UpdateSingleDoc(db, jsonResultSet, opContext->flags);
    updateCount++;
    InnerFreeJsonResultSet(db, jsonResultSet, false, false);
    // if update succeed, return the updateCount (for now it's 1)
    return (errCode == GRD_OK) ? updateCount : GetErrorCategory(errCode);
}
