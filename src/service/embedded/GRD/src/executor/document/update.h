/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef GRD_UPDATE_H
#define GRD_UPDATE_H

#include "grd_type_export.h"
#include "json_result_set.h"
#include "operation_context.h"

#ifdef __cplusplus
extern "C" {
#endif

int32_t UpdateSingleDoc(GRD_DB *db, JsonResultSet *resultSet, uint32_t flags);

int32_t UpdateDoc(GRD_DB *db, OperationContext *opContext);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // GRD_UPDATE_H
