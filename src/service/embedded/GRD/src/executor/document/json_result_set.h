/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef JSON_RESULT_SET_H
#define JSON_RESULT_SET_H

#include "gme_kv.h"
#include "kernel_adapter.h"
#include "result_set.h"
#include "operation_context.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct JsonResultSet {
    GRD_CursorT cursor;
    OperationContext *context;
    uint32_t keyLen;
    char *key;
    char *value;
    bool usedIdQueryBefore;
    void *object;
} JsonResultSet;

/* JsonResultSet interface */
int32_t InnerJsonNext(ResultSet resultSet, bool isGetKey);
int32_t InnerGetJsonValue(JsonResultSet *jsonResultSet, char **value);
int32_t InnerGetJsonKey(JsonResultSet *resultSet, char **key, uint32_t *len);
// value should be checked NULL when calling this method
void InnerJsonFreeValue(char *value);
void InnerFreeKeyAndValueObject(JsonResultSet *resultSet);
int32_t InnerFreeJsonResultSet(GRD_DB *db, JsonResultSet *resultSet, bool isUpdateList, bool isFreeCtx);
int32_t CreateJsonResultSet(GRD_DB *db, OperationContext *context, JsonResultSet **resultSet);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // JSON_RESULT_SET_H
