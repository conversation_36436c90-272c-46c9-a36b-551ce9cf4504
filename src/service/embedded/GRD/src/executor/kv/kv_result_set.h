/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef KV_RESULT_SET_H
#define KV_RESULT_SET_H

#include "gme_kv.h"
#include "grd_resultset.h"
#include "kernel_adapter.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct KvResultSet {
    GRD_CursorT cursor;
    char *collectionName;
    bool isDataNotExist;  // only if Key isNull and ScanMode Is Less,this flag will be true;
} KvResultSet;

/* KvResultSet interface */
int32_t InnerKvNext(KvResultSet *resultSet);
int32_t InnerFreeKvResultSet(GRD_DB *db, KvResultSet *resultSet, bool isUpdateList, bool isIgnoreCursorCheck);
void InnerKvFreeItem(GRD_KVItemT *item);
int32_t InnerKvPrev(KvResultSet *resultSet);
int32_t InnerKvFetch(KvResultSet *resultSet, GRD_KVItemT *key, GRD_KVItemT *value);
int32_t CreateKvResultSet(char *collectionName, KvResultSet **resultSet);
int32_t InnerGetKvSize(KvResultSet *resultSet, uint32_t *keyLen, uint32_t *valueLen);
int32_t InnerGetKvItem(KvResultSet *resultSet, void *key, void *value);
#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // KV_RESULT_SET_H
