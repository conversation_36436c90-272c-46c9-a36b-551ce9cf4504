/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "kv_result_set.h"

#include "db_log.h"
#include "document_check.h"
#include "grd_error.h"
#include "grd_resultset.h"
#include "grd_utils.h"
#include "os_posix.h"

int32_t InnerKvNext(KvResultSet *resultSet)
{
    // if key is null and scan mode is KV_SCAN_EQUAL_OR_LESS,then there will no data in the resultSet.
    if (resultSet->isDataNotExist) {
        return GRD_NO_DATA;
    }
    // cursor is created by GRD_KvScan, and cursor is private for user, so return inner error code
    if (resultSet->cursor.cursor == NULL && resultSet->cursor.nbCursor == NULL) {
        DB_LOG_ERROR(GRD_INNER_ERR, "cursor is null when kv move next");
        return GRD_INNER_ERR;
    }
    int32_t errCode = KernelKvScanMove(&resultSet->cursor, GRD_MOVE_NEXT);
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "kv scan move go wrong");
    }
    return errCode;
}

int32_t InnerKvPrev(KvResultSet *resultSet)
{
    // if key is null and scan mode is KV_SCAN_EQUAL_OR_LESS,then there will no data in the resultSet.
    if (resultSet->isDataNotExist) {
        return GRD_NO_DATA;
    }
    // cursor is created by GRD_KvScan, and cursor is private for user, so return inner error code
    if (resultSet->cursor.cursor == NULL && resultSet->cursor.nbCursor == NULL) {
        DB_LOG_ERROR(GRD_INNER_ERR, "cursor is null when kv move prev");
        return GRD_INNER_ERR;
    }
    int32_t errCode = KernelKvScanMove(&resultSet->cursor, GRD_MOVE_PREV);
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "kv scan move go wrong");
    }
    return errCode;
}

int32_t InnerFreeKvResultSet(GRD_DB *db, KvResultSet *resultSet, bool isUpdateList, bool isIgnoreCursorCheck)
{
    // cursor is created by GRD_KvScan, and cursor is private for user, so return inner error code
    if (!isIgnoreCursorCheck && resultSet->cursor.cursor == NULL && resultSet->cursor.nbCursor == NULL &&
        !resultSet->isDataNotExist) {
        DB_LOG_ERROR(GRD_INNER_ERR, "cursor is null when kv free result set");
        return GRD_INNER_ERR;
    }
    if (resultSet->collectionName == NULL) {
        DB_LOG_ERROR(GRD_INNER_ERR, "collection name is null when kv free result set");
        return GRD_INNER_ERR;
    }
    // Free Connection and cursor
    if (!resultSet->isDataNotExist) {
        int32_t errCode = KernelKvScanEnd(db, &resultSet->cursor);
        if (errCode != GRD_OK) {
            DB_LOG_ERROR(errCode, "kv scan end go wrong");
            return errCode;
        }
    }
    if (isUpdateList) {
        (void)DecreaseResultSetListCount(db, resultSet->collectionName);
    }
    (void)GRD_FreeMem((void *)resultSet->collectionName);
    (void)GRD_FreeMem(resultSet);
    return GRD_OK;
}

void InnerKvFreeItem(GRD_KVItemT *item)
{
    if (item == NULL) {
        return;
    }
    (void)GRD_FreeMem(item->data);
    item->data = NULL;
    item->dataLen = 0;
}

int32_t InnerKvFetch(KvResultSet *resultSet, GRD_KVItemT *key, GRD_KVItemT *value)
{
    // if key is null and scan mode is KV_SCAN_EQUAL_OR_LESS,then there will no data in the resultSet.
    if (resultSet->isDataNotExist) {
        return GRD_NO_DATA;
    }
    // cursor is created by GRD_KvScan, and cursor is private for user, so return inner error code
    if (resultSet->cursor.cursor == NULL && resultSet->cursor.nbCursor == NULL) {
        DB_LOG_ERROR(GRD_INNER_ERR, "cursor is null when kv fetch");
        return GRD_INNER_ERR;
    }
    int32_t errCode = KernelKvScanFetch(&resultSet->cursor, key, value);
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "kv fetch go wrong");
    }
    return errCode;
}

int32_t InnerGetKvSize(KvResultSet *resultSet, uint32_t *keyLen, uint32_t *valueLen)
{
    // if key is null and scan mode is KV_SCAN_EQUAL_OR_LESS,then there will no data in the resultSet.
    if (resultSet->isDataNotExist) {
        return GRD_NO_DATA;
    }
    // cursor is created by GRD_KvScan, and cursor is private for user, so return inner error code
    if (resultSet->cursor.cursor == NULL && resultSet->cursor.nbCursor == NULL) {
        DB_LOG_ERROR(GRD_INNER_ERR, "cursor is null when kv fetch");
        return GRD_INNER_ERR;
    }
    int32_t errCode = KernelKvGetSize(&resultSet->cursor, keyLen, valueLen);
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "kv fetch go wrong");
    }
    return errCode;
}

int32_t InnerGetKvItem(KvResultSet *resultSet, void *key, void *value)
{
    // if key is null and scan mode is KV_SCAN_EQUAL_OR_LESS,then there will no data in the resultSet.
    if (resultSet->isDataNotExist) {
        return GRD_NO_DATA;
    }
    // cursor is created by GRD_KvScan, and cursor is private for user, so return inner error code
    if (resultSet->cursor.cursor == NULL && resultSet->cursor.nbCursor == NULL) {
        DB_LOG_ERROR(GRD_INNER_ERR, "cursor is null when kv fetch");
        return GRD_INNER_ERR;
    }
    int32_t errCode = KernelKvGetItem(&resultSet->cursor, key, value);
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(errCode, "kv fetch go wrong");
    }
    return errCode;
}

int32_t CreateKvResultSet(char *collectionName, KvResultSet **resultSet)
{
    KvResultSet *kvResultSet = NULL;
    int32_t errCode = GRD_AllocAndInitMem(sizeof(KvResultSet), 0, (void **)&kvResultSet);
    if (errCode != GRD_OK) {
        DB_LOG_ERROR(GRD_FAILED_MEMORY_ALLOCATE, "fail to create kv result set");
        return GRD_FAILED_MEMORY_ALLOCATE;
    }
    kvResultSet->collectionName = collectionName;
    kvResultSet->cursor.cursor = NULL;
    kvResultSet->cursor.nbCursor = NULL;
    kvResultSet->isDataNotExist = false;
    *resultSet = kvResultSet;
    return errCode;
}
