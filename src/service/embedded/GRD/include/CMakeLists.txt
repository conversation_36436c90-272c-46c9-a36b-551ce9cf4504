set(MODULE_NAME gmgrdinclude)

add_library(${MODULE_NAME} INTERFACE)
target_include_directories(${MODULE_NAME} INTERFACE ${CMAKE_CURRENT_SOURCE_DIR})

list(APPEND GRD_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/grd_base/*.h)
list(APPEND GRD_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/grd_document/*.h)
list(APPEND GRD_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/grd_kv/*.h)
list(APPEND GRD_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/grd_shared_obj/*.h)
list(APPEND GRD_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/grd_sql/*.h)
list(APPEND GRD_PUBLIC_HEADERS_TEMP ${CMAKE_CURRENT_SOURCE_DIR}/grd_sync/*.h)


file(GLOB GMDB_PUBLIC_HEADERS ${GRD_PUBLIC_HEADERS_TEMP})

set_target_properties(${MODULE_NAME} PROPERTIES PUBLIC_HEADER "${GMDB_PUBLIC_HEADERS}")
install(TARGETS ${MODULE_NAME} PUBLIC_HEADER DESTINATION grd)
