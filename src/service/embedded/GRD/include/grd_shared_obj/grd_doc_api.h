/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef GRD_DOC_API_H
#define GRD_DOC_API_H

#include <stdbool.h>
#include <stdint.h>
#include "grd_type_export.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief register event by name.
 * @param[in] db Pointer to the db.
 * @return GRD_OK if success.
 */
GRD_API int32_t GRD_EventRegister(GRD_DB *db);

/**
 * @brief get the array data of the array belong this db.
 * @param[in] db Pointer to the db.
 * @param[in] name Name to the array.
 * @param[out] value Data in json type belong this struct type.
 * @return GRD_OK if success.
 */
GRD_API int32_t GRD_GetArray(GRD_DB *db, const char *name, char **value);

/**
 * @brief delete the doc db.
 * @param[in] db Pointer to the db.
 * @param[in] name Name to the text.
 * @param[out] value Data in json type belong this struct type.
 * @return GRD_OK if success.
 */
GRD_API int32_t GRD_GetText(GRD_DB *db, const char *name, char **value);

/**
 * @brief delete the doc db.
 * @param[in] db Pointer to the db.
 * @param[in] name Name to the map.
 * @param[out] value Data in json type belong this struct type.
 * @return GRD_OK if success.
 */
GRD_API int32_t GRD_GetMap(GRD_DB *db, const char *name, char **value);

/**
 * @brief delete the doc db.
 * @param[in] db Pointer to the db.
 * @param[in] name Name to the xmlFragment.
 * @param[out] value Data in json type belong this struct type.
 * @return GRD_OK if success.
 */
GRD_API int32_t GRD_GetXmlFragment(GRD_DB *db, const char *name, char **value);

/**
 * @brief turn on the event to start listen related event and emit function.
 * @param[in] value Value read from db.
 * @param[in] eventName Name of event.
 * @param[in] status Status to be set.
 * @return GRD_OK if success
 */
GRD_API int32_t GRD_DocSetEventStatus(GRD_DB *db, const char *eventName, bool status);

typedef int32_t (*GRD_DocEventFuncT)(uint32_t count, ...);  // Register callback func to notify top layer

/**
 * @brief Set callback function to notify event.
 * @param[in] value Value read from db.
 * @param[in] eventNotify function to callback when event has been triggered.
 * @return GRD_OK if success
 */
GRD_API int32_t GRD_DocSetCallback(GRD_DB *db, GRD_DocEventFuncT eventNotify);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // GRD_DOC_API_H
