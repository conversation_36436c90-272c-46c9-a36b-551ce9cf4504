/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Embedded simple relation bindata import api.
 * Author:
 * Create:
 */
#include "gme_simple_rel_persistence.h"
#include "dm_meta_vertex_label.h"
#include "dm_cache_basic.h"
#include "dm_data_prop_in.h"
#include "dm_custom_type.h"
#include "ee_cmd.h"
#include "ee_cmd_namespace.h"
#include "gme_simple_rel_ddl.h"
#include "se_trx.h"
#include "db_sysapp_context.h"

#define SIMPLEREL_FILE_MAX_DESC_OFFSET \
    (uint32_t)(SIMPLEREL_FILE_DB_NAME_OFFSET + DB_NAME_LEN + DB_PATH_LEN + sizeof(uint32_t))

Status SimpleRelIsV1FileNeedTransEndian(FileBufT *fileBuf, bool *isNeedTransEndian)
{
    uint8_t fileEndianType;
    Status ret = DbFileBufRead(fileBuf->fd, 0, sizeof(uint8_t), &fileEndianType);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (fileEndianType >= DB_DEFAULT_ENDIAN) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALID_BYTEORDER, "Byte order(%" PRIu8 ") is not valid.", fileEndianType);
        return VOS_ERRNO_DB_INVALID_BYTEORDER;
    }
    uint8_t currEndian = DbGetCurrEndian();
    if (fileEndianType == currEndian) {
        *isNeedTransEndian = false;
    } else {
        *isNeedTransEndian = true;
    }
    return ret;
}

Status SimpRelCalcAndSetFileHeaderOffset(FileBufT *fileBuf)
{
    // 从 tagDB_DBMS_STRU 结构体中读取 maxDesc Len
    uint32_t readDescPos = SIMPLEREL_FILE_MAX_DESC_OFFSET;
    uint32_t descLen = 0;
    Status ret = DbFileBufRead(fileBuf->fd, readDescPos, sizeof(uint32_t), (uint8_t *)&descLen);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    bool isNeedTransEndian = false;
    ret = SimpleRelIsV1FileNeedTransEndian(fileBuf, &isNeedTransEndian);
    if (ret != GMERR_OK) {
        return ret;
    }
    descLen = SimpRelTryTransEndian32(descLen, isNeedTransEndian);
    fileBuf->header.headerOffset = SIMPLEREL_DESC_INFO_OFFSET + descLen;
    return GMERR_OK;
}

Status SimpleRelGetDescFilePos(FileBufT *fileBuf, uint32_t *fileDescPos)
{
    Status ret = SimpRelCalcAndSetFileHeaderOffset(fileBuf);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get desc pos in file unsucc.");
        return ret;
    }
    *fileDescPos = sizeof(FileBufHeaderT) + sizeof(FileBatchHeaderT) + fileBuf->header.headerOffset;
    return GMERR_OK;
}

static Status SimpRelCopyNewDbDesc(DbMemCtxT *memCtx, const char *dbDescInfo, uint32_t descLen, char **newDbDesc)
{
    if (descLen == 0) {
        return GMERR_OK;
    }
    char *dbDesc = (char *)DbDynMemCtxAlloc(memCtx, descLen);
    if (dbDesc == NULL) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "alloc description unsucc, len %" PRIu32 ".", descLen);
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }
    Status ret = memcpy_s(dbDesc, descLen, dbDescInfo, descLen);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMCPY_FAILURE, "Copy description unsucc, len %" PRIu32 ".", descLen);
        DbDynMemCtxFree(memCtx, dbDesc);
        return VOS_ERRNO_DB_MEMCPY_FAILURE;
    }
    *newDbDesc = dbDesc;
    return GMERR_OK;
}

Status SimpRelSetNewNewDesc(NspCtrlT *nspCtrl, DbMemCtxT *memCtx, const char *dbDescInfo, uint32_t descLen)
{
    char *newDbDesc = NULL;
    Status ret = SimpRelCopyNewDbDesc(memCtx, dbDescInfo, descLen, &newDbDesc);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (nspCtrl->dbDesc != NULL) {
        DbDynMemCtxFree(memCtx, nspCtrl->dbDesc);
    }

    nspCtrl->dbDesc = newDbDesc;
    nspCtrl->actDescLen = descLen;
    nspCtrl->maxDescLen = descLen;
    return GMERR_OK;
}

typedef struct IdTransInfo {
    uint32_t srcId;  // 源表Id
    uint32_t dstId;  // 终表Id
} IdTransInfoT;

void SimpRelReleaseImportDataCtx(EmbSimpleRunCtxT *embCtx, SimpRelRestoreCtxT *restoreCtx, ImportDataCtxT *ctx)
{
    DbDestroyList(&ctx->batchList);
    if (ctx->memCtx != NULL) {
        DbDeleteDynMemCtx(ctx->memCtx);
    }
    if (ctx->importBuf.fd != DB_INVALID_FD) {
        DbCloseFile(ctx->importBuf.fd);
        ctx->importBuf.fd = DB_INVALID_FD;
    }
    if (!restoreCtx->isUseFileDbName) {
        // reset apiMemctx 释放
        restoreCtx->nspName = NULL;
    }
    TrxSetIsAllocNewFlag(embCtx->seRunCtx, false);
    return;
}

Status SimpRelFileBufPrepare(const char *filePath, FileBufT *fileBuf)
{
    if (!DbFileExist(filePath)) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_OPENFILE_ERROR, "import file %s not exist", filePath);
        return VOS_ERRNO_DB_OPENFILE_ERROR;
    }
    Status ret = DbFileReadPrepare(filePath, &fileBuf->fd);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SimpRelCalcAndSetFileHeaderOffset(fileBuf);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Calc and set file buf header offset unsucc.");
        return ret;
    }
    ret = DbImportFileHeader(fileBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status SimpRelImportFileBufPrepare(const char *filePath, FileBufT *fileBuf)
{
    Status ret = SimpRelFileBufPrepare(filePath, fileBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (fileBuf->header.digestAlgorithm != DM_DIGEST_ALGORITHM_NONE &&
        fileBuf->header.digestAlgorithm != DM_DIGEST_ALGORITHM_CRC_V1) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "import file degest algorithm is %" PRIu32 ","
            " not %" PRIu32 " or %" PRIu32 ".",
            (uint32_t)fileBuf->header.digestAlgorithm, DM_DIGEST_ALGORITHM_NONE, DM_DIGEST_ALGORITHM_CRC_V1);
        return GMERR_DATA_EXCEPTION;
    }

    return GMERR_OK;
}

Status SimpRelInitImportDataCtx(EmbSimpleRunCtxT *embCtx, SimpRelRestoreCtxT *restoreCtx, ImportDataCtxT *ctx)
{
    // memCtx用途：用于导入配置数据时申请内存
    // 生命周期：单消息级别
    // 释放方式：异常就近释放为主,提供接口进行兜底释放
    // 兜底清空措施：在导入返回前，调用SimpRelReleaseImportDataCtx释放
    DbMemCtxArgsT args = {};
    DbMemCtxT *topSysDynMemctx = DbSrvGetSysDynCtx(embCtx->dbInstance->instanceId);
    ctx->memCtx = DbCreateDynMemCtx(topSysDynMemctx, false, "importDataMemCtx", &args);
    if (ctx->memCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Unable to create memctx when init import ctx.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    DbExImportPerfStatInit(&ctx->perfStat);
    DbFileBufInit(ctx->memCtx, &ctx->importBuf);
    Status ret = DbOamapInit(&ctx->idTransMap, 0, DbOamapUint32Compare, ctx->memCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SimpRelImportFileBufPrepare((const char *)restoreCtx->srcPath, &ctx->importBuf);
    if (ret != GMERR_OK) {
        return ret;
    }

    DmNamespaceT *namespace = NULL;
    ret = CataGetNamespaceById(embCtx->nspCtrl->metaId, &namespace, embCtx->dbInstance);
    if (ret != GMERR_OK) {
        return ret;
    }
    ctx->vLabelCount = 0;
    ctx->namespaceId = embCtx->nspCtrl->metaId;
    ctx->tabelSpaceId = namespace->metaCommon.tablespaceId;
    ctx->srvMemCtx = embCtx->simpleRelMgr->serviceMemCtx;
    ctx->isNeedTransEndian = DbNeedSwapEndian(ctx->importBuf.header.endianType);
    ctx->isImpCheckSum = ctx->importBuf.header.isExportCheckSum;
    ctx->pfnGetTblConvHook = restoreCtx->pfnGetTblConvHook;
    DbCreateList(&ctx->batchList, sizeof(SimpTupleBufArrayT), ctx->memCtx);
    ctx->totalVertexCnt = 0;
    ctx->embCtx = embCtx;
    SimpRelTryTransHeaderEndian(&ctx->importBuf, ctx->isNeedTransEndian);
    (void)CataReleaseNamespace(embCtx->dbInstance, namespace);
    TrxSetIsAllocNewFlag(embCtx->seRunCtx, true);
    return GMERR_OK;
}

inline static void SimpRelResetVertexLabelInfo(ImportDataCtxT *ctx, DmVertexLabelT *vertexLabel)
{
    vertexLabel->metaCommon.namespaceId = ctx->namespaceId;
    vertexLabel->metaCommon.tablespaceId = ctx->tabelSpaceId;
    vertexLabel->metaCommon.metaId = 0;
    vertexLabel->commonInfo->accCheckAddr = NULL;
}

Status SimpRelImportSingleVertexLabelInner(SeRunCtxHdT seRunCtx, ImportDataCtxT *ctx, DmVertexLabelT **vLabel)
{
    uint32_t *serialLen =
        (uint32_t *)(void *)DbImportDataFromBatch(&ctx->importBuf, sizeof(uint32_t), ctx->isNeedTransEndian);
    if (serialLen == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get vertexLabel buf len for import.");
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t transSeriLen = SimpRelTryTransEndian32(*serialLen, ctx->isNeedTransEndian);

    uint8_t *buf = DbImportDataFromBatch(&ctx->importBuf, transSeriLen, ctx->isNeedTransEndian);
    if (buf == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_DATA_EXCEPTION, "Unable to get vertexLabel buf for import, len:%" PRIu32 ".", transSeriLen);
        return GMERR_DATA_EXCEPTION;
    }

    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(ctx->memCtx);
    DmVertexLabelT *vertexLabel = NULL;
    Status ret = DmDeSerializeVertexLabel2ShmCtx(dbInstance, buf, transSeriLen, &vertexLabel, ctx->isNeedTransEndian);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_UNFOLD(ret,
            "Unable to deseri vertexLabel buf. BufLen is %" PRIu32 " and Endian button is %" PRIu32 ".", transSeriLen,
            ctx->isNeedTransEndian);
        return ret;
    }
    // 重置相关变量，需要重新打开heap
    SimpRelResetVertexLabelInfo(ctx, vertexLabel);

    ret = CmdSimpCreateVertexLabel(seRunCtx, vertexLabel);
    if (ret != GMERR_OK) {
        DestroyShmVertexLabel(DmGetCataCache(dbInstance)->shmMemCtx, vertexLabel);
        return ret;
    }

    *vLabel = vertexLabel;
    return GMERR_OK;
}

Status SimpleRelImportRelationNameAndId(FileBufT *importBuf, bool isNeedTransEndian, VlabelCtrlT *vLabel)
{
    uint16_t *v1RelId = (uint16_t *)(void *)DbImportDataFromBatch(importBuf, sizeof(uint16_t), isNeedTransEndian);
    if (v1RelId == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get original v1 rel id.");
        return GMERR_DATA_EXCEPTION;
    }
    uint16_t transV1RelId = SimpRelTryTransEndian16(*v1RelId, isNeedTransEndian);

    uint16_t *checkSum = (uint16_t *)(void *)DbImportDataFromBatch(importBuf, sizeof(uint16_t), isNeedTransEndian);
    if (checkSum == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get rel checksum.");
        return GMERR_DATA_EXCEPTION;
    }
    uint16_t transChecksum = SimpRelTryTransEndian16(*checkSum, isNeedTransEndian);

    char *relationName = (char *)(void *)DbImportDataFromBatch(importBuf, DB_REL_NAME_LEN, isNeedTransEndian);
    if (relationName == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get relationName from buf.");
        return GMERR_DATA_EXCEPTION;
    }
    errno_t err = memcpy_s(vLabel->relationName, DB_REL_NAME_LEN, relationName, DB_REL_NAME_LEN);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMCPY_FAILURE, "Unable to cpy relationName meta info.");
        return VOS_ERRNO_DB_MEMCPY_FAILURE;
    }
    vLabel->checkSum = transChecksum;
    vLabel->relationId = transV1RelId;
    return GMERR_OK;
}

static Status SimpleRelCheckOriRelDefIsValid(OriginDefInfoT *oriRelDef, uint32_t transAllocSize)
{
    if (oriRelDef->ulNCols > DB_FLDOFREL_MAX) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Origin rel fld cnt %" PRIu32 ".", oriRelDef->ulNCols);
        return GMERR_DATA_EXCEPTION;
    }

    if (oriRelDef->ulNIdxs > DB_MAX_IDX_IN_REL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Origin rel idx cnt %" PRIu32 ".", oriRelDef->ulNIdxs);
        return GMERR_DATA_EXCEPTION;
    }

    // clang-format off
    uint32_t actualSize = (uint32_t)(sizeof(OriginDefInfoT) + oriRelDef->ulNCols * sizeof(DB_FIELD_DEF_STRU) +
                                    oriRelDef->ulNIdxs * sizeof(DB_INDEX_DEF_STRU));
    // clang-format on
    if (transAllocSize != actualSize) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "Origin rel def size is unexpect, expect size %" PRIu32 " and actual size %" PRIu32 ".", transAllocSize,
            actualSize);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

Status SimpleRelImportVCtrlOriginDefInfo(
    DbMemCtxT *memCtx, FileBufT *importBuf, bool isNeedTransEndian, OriginDefInfoT **originDefInfo)
{
    uint32_t *allocSize = (uint32_t *)(void *)DbImportDataFromBatch(importBuf, sizeof(uint32_t), isNeedTransEndian);
    if (allocSize == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get origin rel def alloc size.");
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t transAllocSize = SimpRelTryTransEndian32(*allocSize, isNeedTransEndian);
    OriginDefInfoT *info =
        (OriginDefInfoT *)(void *)DbImportDataFromBatch(importBuf, transAllocSize, isNeedTransEndian);
    if (info == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get origin rel def.");
        return GMERR_DATA_EXCEPTION;
    }

    Status ret = SimpleRelCheckOriRelDefIsValid(info, transAllocSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    OriginDefInfoT *newInfo = (OriginDefInfoT *)DbDynMemCtxAlloc(memCtx, transAllocSize);
    if (newInfo == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_DATA_EXCEPTION, "Unable to alloc newInfo, alloc size is %" PRIu32 "", transAllocSize);
        return GMERR_DATA_EXCEPTION;
    }

    errno_t err = memcpy_s(newInfo, transAllocSize, info, transAllocSize);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(
            VOS_ERRNO_DB_MEMCPY_FAILURE, "Unable to cpy import buf to meta info, size is %" PRIu32 "", transAllocSize);
        DbDynMemCtxFree(memCtx, newInfo);
        return VOS_ERRNO_DB_MEMCPY_FAILURE;
    }

    newInfo->pstFldLst = (DB_FIELD_DEF_STRU *)(newInfo + 1);
    newInfo->pstIdxLst = (DB_INDEX_DEF_STRU *)(newInfo->pstFldLst + newInfo->ulNCols);
    *originDefInfo = newInfo;
    return GMERR_OK;
}

Status SimpRelImportSingleLabelInner(SeRunCtxHdT seRunCtx, VlabelCtrlT *vLabel, uint32_t fldCnt, ImportDataCtxT *ctx)
{
    Status ret = GMERR_OK;
    // 是否延迟建表
    uint8_t *needLazyLoad =
        (uint8_t *)(void *)DbImportDataFromBatch(&ctx->importBuf, sizeof(uint8_t), ctx->isNeedTransEndian);
    if (needLazyLoad == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get needLazyLoad flag for import.");
        return GMERR_DATA_EXCEPTION;
    }

    ret = SimpleRelImportVCtrlOriginDefInfo(
        ctx->srvMemCtx, &ctx->importBuf, ctx->isNeedTransEndian, &(vLabel->originDefInfo));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SimpleRelCheckVCtrlDataTypes(vLabel->originDefInfo, fldCnt);
    if (ret != GMERR_OK) {
        return ret;
    }
    vLabel->needLazyLoad = *needLazyLoad;
    vLabel->directSearchCnt = 0;
    if (vLabel->needLazyLoad) {
        // 需要懒加载，提前退出
        vLabel->vertexLabel = NULL;
        vLabel->latch = NULL;
        return GMERR_OK;
    }

    // 建表
    return SimpRelLazyCreateVertexLabel(ctx->embCtx, vLabel);
}

Status SimpRelImportSingleVertexLabel(NspCtrlT *nspCtrl, SeRunCtxHdT seRunCtx, ImportDataCtxT *ctx)
{
    VlabelCtrlT vLabel = {0};
    Status ret = SimpleRelImportRelationNameAndId(&ctx->importBuf, ctx->isNeedTransEndian, &vLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint8_t *fieldCnt =
        (uint8_t *)(void *)DbImportDataFromBatch(&ctx->importBuf, sizeof(uint8_t), ctx->isNeedTransEndian);
    if (fieldCnt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get fieldCnt. Label is %s.", vLabel.relationName);
        return GMERR_DATA_EXCEPTION;
    }
    ret = SimpRelImportSingleLabelInner(seRunCtx, &vLabel, *fieldCnt, ctx);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = DbAppendListItem(&nspCtrl->vLabelCtrlList, &vLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    SeInstanceT *seIns = (SeInstanceT *)(seRunCtx->seIns);
    DbInstanceHdT dbInstance = seIns->dbInstance;
    uint32_t tmpRelAssignId = vLabel.relationId;
    return SimpleRelLabelIdRegisterOrAlloc(&nspCtrl->tableIdMgr, &tmpRelAssignId, NULL, dbInstance);
}

Status SimpRelImportVertexLabelData(NspCtrlT *nspCtrl, SeRunCtxHdT seRunCtx, ImportDataCtxT *ctx)
{
    Status ret = GMERR_OK;
    uint32_t *vertexLabelCount =
        (uint32_t *)(void *)DbImportDataFromBatch(&ctx->importBuf, sizeof(uint32_t), ctx->isNeedTransEndian);
    if (vertexLabelCount == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get vertexLabel count.");
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t transVLCount = SimpRelTryTransEndian32(*vertexLabelCount, ctx->isNeedTransEndian);
    ctx->vLabelCount = transVLCount;
    ctx->perfStat.exImportVertexLabelNum = transVLCount;
    for (uint32_t i = 0; i < ctx->vLabelCount; i++) {
        ret = SimpRelImportSingleVertexLabel(nspCtrl, seRunCtx, ctx);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status SimpleRelImportEdgeRelInfo(NspCtrlT *nspCtrl, ImportDataCtxT *ctx, EdgeCtrlT *edgeCtrl)
{
    uint32_t *fieldNum =
        (uint32_t *)(void *)DbImportDataFromBatch(&ctx->importBuf, sizeof(uint32_t), ctx->isNeedTransEndian);
    if (fieldNum == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get fieldNum.");
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t transFieldNum = SimpRelTryTransEndian32(*fieldNum, ctx->isNeedTransEndian);

    for (uint32_t i = 0; i < DB_EDGE_REL_NUM; i++) {
        uint16_t *relId =
            (uint16_t *)(void *)DbImportDataFromBatch(&ctx->importBuf, sizeof(uint16_t), ctx->isNeedTransEndian);
        if (relId == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get edge relId from buf");
            return GMERR_DATA_EXCEPTION;
        }
        uint16_t transRelId = SimpRelTryTransEndian16(*relId, ctx->isNeedTransEndian);
        edgeCtrl->relId[i] = transRelId;
        edgeCtrl->relInfo[i].fieldNum = transFieldNum;
        VlabelCtrlT *label = NULL;
        Status ret = SimpleRelGetVlabelCtrlById(&nspCtrl->vLabelCtrlList, edgeCtrl->relId[i], NULL, &label, NULL);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (label->vertexLabel == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "VertexLabel is unexpect null value when import edge.");
            return GMERR_DATA_EXCEPTION;
        }
        edgeCtrl->relInfo[i].labelId = label->vertexLabel->metaCommon.metaId;
        uint8_t *fields = (uint8_t *)DbImportDataFromBatch(
            &ctx->importBuf, sizeof(edgeCtrl->relInfo[i].fields), ctx->isNeedTransEndian);
        if (fields == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get edge relId from buf");
            return GMERR_DATA_EXCEPTION;
        }
        errno_t err = memcpy_s(edgeCtrl->relInfo[i].fields, DB_IDX_FLD_MAX, fields, DB_IDX_FLD_MAX);
        if (err != EOK) {
            DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMCPY_FAILURE, "Unable to cpy edge rel fields.");
            return VOS_ERRNO_DB_MEMCPY_FAILURE;
        }
        edgeCtrl->relInfo[i].indexId =
            GetLabelIndexByField(edgeCtrl->relInfo[i].fieldNum, edgeCtrl->relInfo[i].fields, label->originDefInfo);
        edgeCtrl->isPersistence = true;
        label->ref++;
    }
    return GMERR_OK;
}

Status SimpRelImportSingleEdge(NspCtrlT *nspCtrl, ImportDataCtxT *ctx)
{
    EdgeCtrlT edgeCtrl;
    char *edgeName = (char *)(void *)DbImportDataFromBatch(&ctx->importBuf, DB_REL_NAME_LEN, ctx->isNeedTransEndian);
    if (edgeName == NULL || edgeName[0] == '\0') {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get edge name from buf");
        return GMERR_DATA_EXCEPTION;
    }
    errno_t err = memcpy_s(edgeCtrl.edgeName, DB_REL_NAME_LEN, edgeName, DB_REL_NAME_LEN);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMCPY_FAILURE, "Unable to cpy edge name meta info.");
        return VOS_ERRNO_DB_MEMCPY_FAILURE;
    }

    Status ret = SimpleRelImportEdgeRelInfo(nspCtrl, ctx, &edgeCtrl);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = DbAppendListItem(&nspCtrl->edgeCtrlList, &edgeCtrl);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to append edge label list when import data.");
        return ret;
    }
    return GMERR_OK;
}

Status SimpRelImportEdge(NspCtrlT *nspCtrl, ImportDataCtxT *ctx)
{
    Status ret = GMERR_OK;
    uint32_t *edgeCount =
        (uint32_t *)(void *)DbImportDataFromBatch(&ctx->importBuf, sizeof(uint32_t), ctx->isNeedTransEndian);
    if (edgeCount == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get edge count.");
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t transEdgeCount = SimpRelTryTransEndian32(*edgeCount, ctx->isNeedTransEndian);

    for (uint32_t i = 0; i < transEdgeCount; i++) {
        ret = SimpRelImportSingleEdge(nspCtrl, ctx);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status SimpRelImportMetaData(ImportDataCtxT *ctx)
{
    EmbSimpleRunCtxT *embCtx = ctx->embCtx;
    NspCtrlT *nspCtrl = embCtx->nspCtrl;
    uint64_t startTime = DbClockGetTsc();
    Status ret = SimpRelImportVertexLabelData(nspCtrl, embCtx->seRunCtx, ctx);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SimpRelImportEdge(nspCtrl, ctx);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint64_t endTime = DbClockGetTsc();
    DbSetExImportTaskTime(PERIOD_META_DATA, endTime - startTime, &ctx->perfStat);
    return GMERR_OK;
}

// 本函数会直接修改 transBuf 指向内内容
void SimpleRelImportTransDataBufEndian(ImportDataCtxT *ctx, VlabelCtrlT *vLabelCtrl, uint8_t *transBuf)
{
    if (!ctx->isNeedTransEndian) {
        return;
    }
    SimpleRelTransRecordEndian(vLabelCtrl, transBuf, ctx->pfnGetTblConvHook);
}

Status SimpRelImportVertexesFromFile(ImportDataCtxT *ctx, VlabelCtrlT *vLabelCtrl, uint32_t vertexCount,
    SimpTupleBufArrayT *tupleBufArray, uint32_t bufSize)
{
    DB_POINTER3(ctx, vLabelCtrl, tupleBufArray);

    for (uint32_t i = 0; i < vertexCount; i++) {
        tupleBufArray->newTuples[i].bufSize = bufSize;
        tupleBufArray->newTuples[i].buf = DbImportDataFromBatch(&ctx->importBuf, bufSize, ctx->isNeedTransEndian);
        if (SECUREC_UNLIKELY(tupleBufArray->newTuples[i].buf == NULL)) {
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
                "Unable to read vertex buf. Export vertex num is %" PRIu32 ", buf len is %" PRIu32 ".", vertexCount,
                bufSize);
            return GMERR_DATA_EXCEPTION;
        }
        SimpleRelImportTransDataBufEndian(ctx, vLabelCtrl, tupleBufArray->newTuples[i].buf);
    }
    return GMERR_OK;
}

inline static void SimpRelFreeTupleBufArray(DbMemCtxT *memCtx, SimpTupleBufArrayT *tupleBufArray)
{
    // tupleBufArray在SimpRelReleaseImportDataCtx清理list时释放
    DbDynMemCtxFree(memCtx, tupleBufArray->newTuples);
    DbDynMemCtxFree(memCtx, tupleBufArray->batchOut);
    DbDynMemCtxFree(memCtx, tupleBufArray->newTupleAddrs);
    (void)memset_s(tupleBufArray, sizeof(SimpTupleBufArrayT), 0, sizeof(SimpTupleBufArrayT));
}

// 只有compress场景下allocTupleArray为true
Status SimpRelAllocTupleBufArray(
    DbMemCtxT *memCtx, uint32_t tupleNum, SimpTupleBufArrayT **tupleBufArray, bool allocTupleArray)
{
    DB_POINTER(tupleBufArray);
    // 内存释放点：SimpRelFreeTupleBufArray, 函数内失败不释放(delete importDataMemCtx 兜底释放)
    SimpTupleBufArrayT *arrayTmp = DbDynMemCtxAlloc(memCtx, sizeof(SimpTupleBufArrayT));
    if (SECUREC_UNLIKELY(arrayTmp == NULL)) {
        DB_LOG_ERROR(VOS_ERRNO_DB_MEMALLOCFAILURE, "Unable alloc memory for arr tank.");
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }

    HeapTupleBufT *tupleBufArr = DbDynMemCtxAlloc(memCtx, tupleNum * (sizeof(HeapTupleBufT)));
    if (SECUREC_UNLIKELY(tupleBufArr == NULL)) {
        DB_LOG_ERROR(VOS_ERRNO_DB_MEMALLOCFAILURE, "Unable alloc memory for tupleArr.");
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }

    HpBatchOutT *batchOutArr = DbDynMemCtxAlloc(memCtx, tupleNum * (sizeof(HpBatchOutT)));
    if (SECUREC_UNLIKELY(batchOutArr == NULL)) {
        DB_LOG_ERROR(VOS_ERRNO_DB_MEMALLOCFAILURE, "Unable alloc memory for batchout.");
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }

    if (allocTupleArray) {
        arrayTmp->insertNum = 0;
        arrayTmp->newTupleAddrs = DbDynMemCtxAlloc(memCtx, tupleNum * (sizeof(HpTupleAddr)));
        if (SECUREC_UNLIKELY(arrayTmp->newTupleAddrs == NULL)) {
            DB_LOG_ERROR(VOS_ERRNO_DB_MEMALLOCFAILURE, "Unable alloc memory for addrArr.");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }
    } else {
        arrayTmp->insertNum = tupleNum;
        arrayTmp->newTupleAddrs = NULL;
    }
    arrayTmp->newTuples = tupleBufArr;
    arrayTmp->batchOut = batchOutArr;
    *tupleBufArray = arrayTmp;
    return GMERR_OK;
}

void SimpRelSetRealItemNum(SimRelLabelCursorT *labelCursor, uint32_t itemNum)
{
    const uint32_t recLen = labelCursor->vertexLabel->metaVertexLabel->schema->maxRecLen;
    labelCursor->hpHandle->perfStat->realItemNum = itemNum;
    labelCursor->hpHandle->perfStat->writeBytes = itemNum * recLen;
}

void SimpRelAddRealItemNum(SimRelLabelCursorT *labelCursor, uint32_t itemNum)
{
    const uint32_t recLen = labelCursor->vertexLabel->metaVertexLabel->schema->maxRecLen;
    labelCursor->hpHandle->perfStat->realItemNum += itemNum;
    labelCursor->hpHandle->perfStat->writeBytes += itemNum * recLen;
}

Status SimpRelBatchInsertVertexes(EmbSimpleRunCtxT *embCtx, uint32_t totalCount, SimpTupleBufArrayT *tupleBufArray)
{
    DB_POINTER2(embCtx, tupleBufArray);
    SimRelLabelCursorT *labelCursor = embCtx->labelCursor;
    // heap insert
    Status ret = HeapLabelResetOpType(labelCursor->hpHandle, HEAP_OPTYPE_INSERT, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_UNFOLD(ret, "Unable to reset opType when simplerel batch insert.");
        return ret;
    }

    ret = HeapLabelInsertTupleBatch(
        labelCursor->hpHandle, tupleBufArray->insertNum, tupleBufArray->newTuples, tupleBufArray->batchOut);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_UNFOLD(ret, "Unable to batch-insert into heap.");
        return ret;
    }
    SimpRelAddRealItemNum(labelCursor, tupleBufArray->insertNum);
    return GMERR_OK;
}

inline static void SimpleRelGetActIdxAndArrInList(
    DbListT *batchList, SimpTupleBufArrayT **tupleArr, uint32_t index, uint32_t *realIdx)
{
    uint32_t batchNum = ((SimpTupleBufArrayT *)DbListItem(batchList, 0))->insertNum;
    *realIdx = index % batchNum;
    *tupleArr = DbListItem(batchList, index / batchNum);
    DB_ASSERT(*tupleArr != NULL);
}

Status SimpleRelRefreshIndexAddr(TTreeHeadT *tTreeHead, ImportDataCtxT *ctx, DbListT *batchList)
{
    uint32_t vertexNum = ctx->totalVertexCnt;
    uint32_t realIdx = 0;
    SimpTupleBufArrayT *tupleBufArray = NULL;
    for (uint32_t i = 0; i < tTreeHead->nodeCnt; ++i) {
        TTreeNodeT *tTreeNode = GetTTreeNode(tTreeHead, i);
        for (uint32_t j = 0; j < tTreeNode->ucCurIdxNum; ++j) {
            uint32_t vertexIndex = *(uint32_t *)&tTreeNode->buf[j];
            SimpleRelGetActIdxAndArrInList(batchList, &tupleBufArray, vertexIndex, &realIdx);
            if (vertexIndex >= vertexNum || tupleBufArray == NULL) {
                DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
                    "Vertex index is not match when refresh indexAddr, vertexNum is %" PRIu32
                    " and vertexIndex is %" PRIu32 ".",
                    vertexNum, vertexIndex);
                return GMERR_DATA_EXCEPTION;
            }
            tTreeNode->buf[j] = tupleBufArray->batchOut[realIdx].realBuf;
            *(uint32_t *)tTreeNode->buf[j] =
                HeapCompressTupleAddr32(tupleBufArray->batchOut[realIdx].addrOut).pageIdAndSlotId;
            SetDataInfoModify(tTreeNode, j, false);
        }
    }
    return GMERR_OK;
}

Status SimpleRelIndexInsert(IndexCtxT *idxCtx, ImportDataCtxT *ctx)
{
    // idx insert
    // 大对象数据使用hashKey.keyData存储真实数据buf，idxCtx->ttreeDataInfo.buf为数据物理addr
    // 非大对象数据idxCtx->ttreeDataInfo.buf为真实数据并且为数据物理addr，不使用hashKey.keyData
    IndexKeyT hashKey = {0};
    idxCtx->destNode = NULL_NODEID;
    SimpTupleBufArrayT *tupleBufArray = DbListItem(&ctx->batchList, 0);
    uint32_t idx = 0;
    for (uint32_t j = 0; j < ctx->totalVertexCnt; j++) {
        hashKey.keyData = tupleBufArray->newTuples[idx].buf;
        hashKey.keyLen = tupleBufArray->newTuples[idx].bufSize;
        idxCtx->ttreeDataInfo.buf = RealBuf2Info(idxCtx->isNormalIdx, tupleBufArray->batchOut[idx].realBuf);
        idxCtx->ttreeDataInfo.hasModify = false;
        idxCtx->ttreeDataInfo.addr32 = HeapCompressTupleAddr32(tupleBufArray->batchOut[idx].addrOut);
        if (idxCtx->isNormalIdx) {
            *(uint32_t *)tupleBufArray->batchOut[idx].realBuf = idxCtx->ttreeDataInfo.addr32.pageIdAndSlotId;
            SetHeadModify(tupleBufArray->batchOut[idx].realBuf, false);
        }
        Status ret = IdxInsert(idxCtx, hashKey, tupleBufArray->batchOut[idx].addrOut);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "Unable insert when import index.");
            return ret;
        }
        idx++;
        if (idx == tupleBufArray->insertNum) {
            idx = 0;
            tupleBufArray++;
        }
    }
    return GMERR_OK;
}

static ALWAYS_INLINE bool SimpRelIsNeedIdxInsert(ImportDataCtxT *ctx)
{
    bool needIdxInsert = false;
    uint32_t tmpPageId = 0;
    uint32_t idx = 0;
    SimpTupleBufArrayT *tupleBufArray = DbListItem(&ctx->batchList, 0);
    for (uint32_t i = 0; i < ctx->totalVertexCnt; ++i) {
        uint32_t pageId = tupleBufArray->batchOut[idx].addrOut & V1_PAGE_ID_MASK;
        if (pageId < tmpPageId) {
            needIdxInsert = true;
            break;
        }
        tmpPageId = pageId;
        idx++;
        if (idx == tupleBufArray->insertNum) {
            idx = 0;
            tupleBufArray++;
        }
    }
    return needIdxInsert;
}

Status SimpRelImportIdxInsertSkipBuf(IndexCtxT *idxCtx, ImportDataCtxT *ctx)
{
    Status ret = SimpleRelIndexInsert(idxCtx, ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Idx insert unsucc when import index.");
        return ret;
    }
    uint32_t *bufLen = (uint32_t *)DbImportDataFromBatch(&ctx->importBuf, sizeof(uint32_t), ctx->isNeedTransEndian);
    if (bufLen == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable get bufLen size when import index.");
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t transBufLen = SimpRelTryTransEndian32(*bufLen, ctx->isNeedTransEndian);
    uint8_t *buf = DbImportDataFromBatch(&ctx->importBuf, transBufLen, ctx->isNeedTransEndian);
    if (buf == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_DATA_EXCEPTION, "Unable get buf when import index. Size is %" PRIu32 ".", transBufLen);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

Status SimpleRelImportIndexInner(TTreeHeadT *tTreeHead, ImportDataCtxT *ctx, DbMemCtxT *sIdxMemCtx)
{
    Status ret = SimpleRelDeseriTTreeIndex(sIdxMemCtx, tTreeHead, &ctx->importBuf, ctx->isNeedTransEndian);
    if (ret != GMERR_OK) {
        TTreeFreeAllNodes(tTreeHead, sIdxMemCtx);
        return ret;
    }
    ret = SimpleRelRefreshIndexAddr(tTreeHead, ctx, &ctx->batchList);
    if (ret != GMERR_OK) {
        TTreeFreeAllNodes(tTreeHead, sIdxMemCtx);
        return ret;
    }
    return GMERR_OK;
}

Status SimpleRelDeseriImportIndex(IndexCtxT *idxCtx, ImportDataCtxT *ctx)
{
    TTreeHeadT *tTreeHead = (TTreeHeadT *)idxCtx->idxRunCtx;
    DbMemCtxT *sIdxMemCtx = GetTTreeIdxShmMemCtx(idxCtx->idxOpenCfg.seRunCtx);
    DbCreateListWithExtendSize(&tTreeHead->nodeArrList, sizeof(TTreeNodeArrT), TTREE_ARRLIST_ENTEND_SIZE,
        (DbMemCtxT *)DbGetTopDynMemCtx(DbGetInstanceByMemCtx(ctx->srvMemCtx)));
    tTreeHead->idxSearchCnt = 0;

    return SimpleRelImportIndexInner(tTreeHead, ctx, sIdxMemCtx);
}

Status SimpleRelImportAllIndex(EmbSimpleRunCtxT *embCtx, ImportDataCtxT *ctx)
{
    // 默认首次插入
    if (embCtx->labelCursor->idxNum == 0) {
        return GMERR_OK;
    }
    bool importIdxInsert = SimpRelIsNeedIdxInsert(ctx);
    // 获取export的标记 表识导出的时候是否导出了index信息
    uint8_t *isExportIdx = DbImportDataFromBatch(&ctx->importBuf, sizeof(uint8_t), ctx->isNeedTransEndian);
    if (isExportIdx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get export index info when import index.");
        return GMERR_DATA_EXCEPTION;
    }
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < embCtx->labelCursor->idxNum; ++i) {
        IndexCtxT *idxCtx = embCtx->labelCursor->idxCtx[i];
        if (*isExportIdx == 0) {
            // 未序列化索引场景
            ret = SimpleRelIndexInsert(idxCtx, ctx);
            if (ret != GMERR_OK) {
                return ret;
            }
        } else {
            if (importIdxInsert && idxCtx->idxMetaCfg.idxConstraint == NON_UNIQUE) {
                // idxInsert
                ret = SimpRelImportIdxInsertSkipBuf(idxCtx, ctx);
                if (ret != GMERR_OK) {
                    return ret;
                }
            } else {
                // deseri
                ret = SimpleRelDeseriImportIndex(idxCtx, ctx);
                if (ret != GMERR_OK) {
                    return ret;
                }
            }
        }
    }
    return GMERR_OK;
}

Status SimpleRelGetCurBatchCnt(ImportDataCtxT *ctx, uint32_t *curBatchRecCnt, bool *isFirstIn)
{
    if (*isFirstIn) {
        *isFirstIn = false;
        return GMERR_OK;
    }

    uint32_t *vertexCnt =
        (uint32_t *)(void *)DbImportDataFromBatch(&ctx->importBuf, sizeof(uint32_t), ctx->isNeedTransEndian);
    // 读取四字节vertexCnt, 本批次数据量
    if (vertexCnt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get vertex when batch import.");
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t transVertexCount = SimpRelTryTransEndian32(*vertexCnt, ctx->isNeedTransEndian);
    DbAddExImportVertexNum(transVertexCount, &ctx->perfStat);
    *curBatchRecCnt = transVertexCount;
    ctx->totalVertexCnt += transVertexCount;
    return GMERR_OK;
}

inline static Status SimpRelImportNormalVertexesFromFile(EmbSimpleRunCtxT *embCtx, ImportDataCtxT *ctx,
    VlabelCtrlT *vLabelCtrl, SimpTupleBufArrayT *batchTupleArr, uint32_t bufSize)
{
    uint32_t batchRecCnt = batchTupleArr->insertNum;
    Status ret = SimpRelImportVertexesFromFile(ctx, vLabelCtrl, batchRecCnt, batchTupleArr, bufSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 批量写入数据
    return SimpRelBatchInsertVertexes(embCtx, batchRecCnt, batchTupleArr);
}

ALWAYS_INLINE static Status SimpRelSetOneBigObjTupleBuf(ImportDataCtxT *ctx, uint32_t bufSize, HeapTupleBufT *tupleBuf)
{
    tupleBuf->bufSize = bufSize;
    tupleBuf->buf = DbDynMemCtxAlloc(ctx->memCtx, bufSize);  // delete ctx->memCtx 统一清理
    if (SECUREC_UNLIKELY(tupleBuf->buf == NULL)) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "Unable to alloc one big obj tuple when import.");
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }
    uint8_t *dstBuf = DbImportDataFromBatch(&ctx->importBuf, bufSize, ctx->isNeedTransEndian);
    if (SECUREC_UNLIKELY(dstBuf == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Data exception when import data.");
        return GMERR_DATA_EXCEPTION;
    }
    DbFastMemcpy(tupleBuf->buf, bufSize, dstBuf, bufSize);
    return GMERR_OK;
}

Status SimpRelImportBigObjVertexesFromFile(EmbSimpleRunCtxT *embCtx, ImportDataCtxT *ctx, VlabelCtrlT *vLabelCtrl,
    SimpTupleBufArrayT *batchTupleArr, uint32_t bufSize)
{
    DB_POINTER3(ctx, vLabelCtrl, batchTupleArr);
    HpRunHdlT hpHandle = embCtx->labelCursor->hpHandle;
    Status ret = HeapLabelResetOpType(hpHandle, HEAP_OPTYPE_INSERT, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_UNFOLD(ret, "Unable to reset opType when simplerel batch insert.");
        return ret;
    }

    for (uint32_t i = 0; i < batchTupleArr->insertNum; i++) {
        ret = SimpRelSetOneBigObjTupleBuf(ctx, bufSize, &batchTupleArr->newTuples[i]);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        SimpleRelImportTransDataBufEndian(ctx, vLabelCtrl, batchTupleArr->newTuples[i].buf);
        ret = HeapLabelInsertHpTupleBuffer(hpHandle, &batchTupleArr->newTuples[i], &embCtx->labelCursor->addr);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR_UNFOLD(ret, "Unable to insert data when import big obj vertex.");
            return ret;
        }
        batchTupleArr->batchOut[i].addrOut = embCtx->labelCursor->addr;
        batchTupleArr->batchOut[i].realBuf = hpHandle->compV1Info.realInsertPtr;
    }
    SimpRelAddRealItemNum(embCtx->labelCursor, batchTupleArr->insertNum);
    return GMERR_OK;
}

Status SimpRelInsertAllVertexes(
    EmbSimpleRunCtxT *embCtx, ImportDataCtxT *ctx, uint32_t vertexCount, VlabelCtrlT *vLabelCtrl)
{
    Status ret = GMERR_OK;
    // 获取bufSize
    uint32_t *bufSize =
        (uint32_t *)(void *)DbImportDataFromBatch(&ctx->importBuf, sizeof(uint32_t), ctx->isNeedTransEndian);
    if (SECUREC_UNLIKELY(bufSize == NULL)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_DATA_EXCEPTION, "Unable to read vertex buf length. Export vertex num is %" PRIu32 ".", vertexCount);
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t oneRecLen = SimpRelTryTransEndian32(*bufSize, ctx->isNeedTransEndian);
    uint32_t currRecCnt = vertexCount;
    ctx->totalVertexCnt += vertexCount;
    bool isFirstIn = true;
    // 分批场景循环读取batch内容
    do {
        ret = SimpleRelGetCurBatchCnt(ctx, &currRecCnt, &isFirstIn);
        if (ret != GMERR_OK) {
            return ret;
        }
        SimpTupleBufArrayT *tupleBufArray = NULL;
        ret = SimpRelAllocTupleBufArray(ctx->memCtx, currRecCnt, &tupleBufArray, false);
        if (ret != GMERR_OK) {
            return ret;
        }
        SeTransSetDmlHint4BatchExtendNum(embCtx->seRunCtx, currRecCnt);
        ret = embCtx->labelCursor->hpHandle->hpControl.isFixPage ?
                  SimpRelImportNormalVertexesFromFile(embCtx, ctx, vLabelCtrl, tupleBufArray, oneRecLen) :
                  SimpRelImportBigObjVertexesFromFile(embCtx, ctx, vLabelCtrl, tupleBufArray, oneRecLen);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DbAppendListItem(&ctx->batchList, tupleBufArray);
        if (ret != GMERR_OK) {
            return ret;
        }
        DbDynMemCtxFree(ctx->memCtx, tupleBufArray);  // 已经拷贝至ctx->batchList，外部需释放
    } while (SimpleRelIsBatchOn(&ctx->importBuf));
    ret = SimpleRelImportAllIndex(embCtx, ctx);
    if (ret != GMERR_OK) {
        return ret;
    }
    vLabelCtrl->hasInsert = true;
    return GMERR_OK;
}

Status SimpRelGetCheckSumByTrx(EmbSimpleRunCtxT *embCtx, VlabelCtrlT *vLabelCtrl, uint16_t *checkSum)
{
    SessionT *session = embCtx->session->session;
    SimRelLabelCursorT labelCursor = {0};
    labelCursor.memCtx = (DbMemCtxT *)embCtx->apiMemCtx;
    TrxCfgT cfg2 = {.isolationLevel = g_adptV1Instance.isolationLevel[embCtx->isTwoStage], .trxType = OPTIMISTIC_TRX};
    Status ret = SeTransBegin(session->seInstance, &cfg2);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SeTransAssignReadView(session->seInstance);
    if (ret != GMERR_OK) {
        (void)SeTransRollback(embCtx->session->session->seInstance, false);
        return ret;
    }
    ret = SimpleRelHeapOpen(NULL, embCtx->seRunCtx, labelCursor.memCtx, vLabelCtrl->vertexLabel, &labelCursor.hpHandle,
        HEAP_OPTYPE_DIRECTREAD);
    if (ret != GMERR_OK) {
        (void)SeTransRollback(embCtx->session->session->seInstance, false);
        return ret;
    }
    SimpRelTableCRCFetchInfoT tableInfo = {.recordCnt = 0,
        .crcValue = V1_CHECKSUM_DEFAULT_VAL,
        .isBigObj = labelCursor.isBigObj,
        .checkSumCfg = embCtx->nspCtrl->pstChksum,
        .isGetTableCRC = true};
    // 计算规则:非大对象场景,过前五个字节,计算真实buf内容 CRC算法: V1_DBCKSM_ADD
    ret = SimpRelGetTableCRCScan(&labelCursor, &tableInfo);
    SimpRelCloseReadCursor(&labelCursor);
    if (ret != GMERR_OK) {
        (void)SeTransRollback(session->seInstance, false);
        return ret;
    }
    ret = SeTransRollback(embCtx->session->session->seInstance, false);
    if (ret != GMERR_OK) {
        return ret;
    }
    *checkSum = tableInfo.crcValue;
    return GMERR_OK;
}

Status SimpRelInsertVertexesSingleLabel(EmbSimpleRunCtxT *embCtx, ImportDataCtxT *ctx, VlabelCtrlT *vLabelCtrl)
{
    uint32_t *vertexCount =
        (uint32_t *)(void *)DbImportDataFromBatch(&ctx->importBuf, sizeof(uint32_t), ctx->isNeedTransEndian);
    if (vertexCount == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get vertex count");
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t transVertexCount = SimpRelTryTransEndian32(*vertexCount, ctx->isNeedTransEndian);
    DbAddExImportVertexNum(transVertexCount, &ctx->perfStat);
    if (transVertexCount == 0) {
        return GMERR_OK;
    }
    SessionT *session = embCtx->session->session;
    TrxCfgT cfg = {.isolationLevel = READ_UNCOMMITTED, .trxType = DEFAULT_TRX, .isLiteTrx = true};
    Status ret = SeTransBegin(session->seInstance, &cfg);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SimpleRelPrepareDmlCursor(embCtx, vLabelCtrl);
    if (ret != GMERR_OK) {
        (void)SeTransRollback(session->seInstance, false);
        return ret;
    }

    ret = SimpRelInsertAllVertexes(embCtx, ctx, transVertexCount, vLabelCtrl);
    if (ret != GMERR_OK) {
        (void)SeTransRollback(session->seInstance, false);
        SimpRelCloseWriteCursor(embCtx->labelCursor);
        return ret;
    }
    ret = SeTransCommit(session->seInstance);
    SimpRelCloseWriteCursor(embCtx->labelCursor);
    if (ret != GMERR_OK) {
        (void)SeTransRollback(session->seInstance, false);
        return ret;
    }
    if (!embCtx->nspCtrl->isTpcDb && embCtx->nspCtrl->pstChksum != NULL && vLabelCtrl->checkSum == 0) {
        uint16_t checkSum = 0;
        ret = SimpRelGetCheckSumByTrx(embCtx, vLabelCtrl, &checkSum);
        if (ret != GMERR_OK) {
            return ret;
        }
        vLabelCtrl->checkSum = checkSum;
    }
    return GMERR_OK;
}

inline static void SimpleRelResetImportCtxBatchList(ImportDataCtxT *ctx)
{
    for (uint32_t i = 0; i < DbListGetItemCnt(&ctx->batchList); i++) {
        SimpTupleBufArrayT *bufArr = DbListItem(&ctx->batchList, i);
        SimpRelFreeTupleBufArray(ctx->memCtx, bufArr);
    }
    DbClearList(&ctx->batchList);
    ctx->totalVertexCnt = 0;
}

Status SimpRelImportData(ImportDataCtxT *ctx)
{
    EmbSimpleRunCtxT *embCtx = ctx->embCtx;
    uint64_t startTime = DbClockGetTsc();
    Status ret = SimpleRelLabelCursorAlloc(embCtx);
    if (ret != GMERR_OK) {
        return ret;
    }

    embCtx->concurentCtrl.labelState = SIMPLEREL_LOCK_TYPE_WRITE;
    for (uint32_t i = 0; i < ctx->vLabelCount; i++) {
        VlabelCtrlT *vLabelCtrl = DbListItem(&embCtx->nspCtrl->vLabelCtrlList, i);
        ret = SimpRelInsertVertexesSingleLabel(embCtx, ctx, vLabelCtrl);
        SimpleRelResetImportCtxBatchList(ctx);
        if (ret != GMERR_OK) {
            SimpleRelLabelCursorFree(embCtx);
            return ret;
        }
    }

    uint64_t endTime = DbClockGetTsc();
    DbSetExImportTaskTime(PERIOD_HEAP_DATA, endTime - startTime, &ctx->perfStat);
    return GMERR_OK;
}

Status SimpRelImportDbDesc(ImportDataCtxT *ctx)
{
    EmbSimpleRunCtxT *embCtx = ctx->embCtx;
    uint64_t startTime = DbClockGetTsc();
    uint32_t *descLen =
        (uint32_t *)(void *)DbImportDataFromBatch(&ctx->importBuf, sizeof(uint32_t), ctx->isNeedTransEndian);
    if (descLen == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get descLen.");
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t transDescLen = SimpRelTryTransEndian32(*descLen, ctx->isNeedTransEndian);
    if (transDescLen == 0) {
        return GMERR_OK;
    }
    char *desc = (char *)(void *)DbImportDataFromBatch(&ctx->importBuf, transDescLen, ctx->isNeedTransEndian);
    if (desc == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unable to get desc from batch.");
        return GMERR_DATA_EXCEPTION;
    }

    Status ret = SimpRelSetNewNewDesc(embCtx->nspCtrl, embCtx->simpleRelMgr->serviceMemCtx, desc, transDescLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint64_t endTime = DbClockGetTsc();
    DbSetExImportTaskTime(PERIOD_DB_DESC, endTime - startTime, &ctx->perfStat);
    return GMERR_OK;
}

// 从oldNsp内把资源转移到newNsp上,不会额外申请资源
static void SimpleRelCopyNspCtrl(NspCtrlT *newNsp, NspCtrlT *oldNsp)
{
    newNsp->dbDesc = oldNsp->dbDesc;
    newNsp->actDescLen = oldNsp->actDescLen;
    newNsp->maxDescLen = oldNsp->actDescLen;
    newNsp->edgeCtrlList = oldNsp->edgeCtrlList;
    newNsp->vLabelCtrlList = oldNsp->vLabelCtrlList;
    newNsp->tableIdMgr = oldNsp->tableIdMgr;
    newNsp->metaId = oldNsp->metaId;
    newNsp->loadPath = oldNsp->loadPath;
    newNsp->nspName = oldNsp->nspName;
    newNsp->lastError = oldNsp->lastError;
    newNsp->pstChksum = oldNsp->pstChksum;
    newNsp->isTpcDb = oldNsp->isTpcDb;
}

void SimRelMakeHandleInvalid(uint32_t dbId)
{
    AdptV1HdlMgrT *handlePool = &g_adptV1Instance.handleMgr;
    for (uint32_t i = 0; i < DbGaListGetCount(&handlePool->handleList); i++) {
        AdptHandleInfoT *handleInfo = (AdptHandleInfoT *)DbGaListGet(&handlePool->handleList, i);
        if (handleInfo != NULL && handleInfo->state == HANDLE_USED && handleInfo->dbId == dbId) {
            handleInfo->state = HANDLE_INVALID;
        }
    }
}

Status SimpRelImportCopyNsp(EmbSimpleRunCtxT *embCtx, const char *nspName, bool isExist)
{
    if (!isExist) {
        return GMERR_OK;
    }
    // 将新申请的nspCtrl部分内容拷贝给旧nspCtrl
    NspCtrlT *oldNsp = SimpleRelGetNspCtrlByName(&embCtx->simpleRelMgr->nspCtrlList, nspName, NULL);
    if (oldNsp == NULL) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALID_DBNAME, "DB name %s is not exist when swap db.", nspName);
        return VOS_ERRNO_DB_INVALID_DBNAME;
    }

    // 获取原有命名空间
    DmNamespaceT *nsp = NULL;
    Status ret = CataGetNamespaceById(oldNsp->metaId, &nsp, embCtx->dbInstance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Cata get nsp(%s) unsucc when copy nsp.", oldNsp->nspName);
        return ret;
    }
    (void)CataReleaseNamespace(embCtx->dbInstance, nsp);

    ret = SimpleRelClearNamespace(embCtx, oldNsp);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "clear nsp(%s) unsucc.", oldNsp->nspName);
        return ret;
    }

    ret = SimpleRelExecDropNamespace(embCtx->seRunCtx, nsp->metaCommon.metaName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to drop old namespace(%s) when import.", nspName);
        return ret;
    }

    NspCtrlT *newNsp = embCtx->nspCtrl;
    SimpleRelCopyNspCtrl(oldNsp, newNsp);
    DbDynMemCtxFree(embCtx->simpleRelMgr->serviceMemCtx, newNsp);
    SimRelMakeHandleInvalid(oldNsp->dbId);
    embCtx->nspCtrl = oldNsp;
    return GMERR_OK;
}

static void SimpRelReleaseLabelLatch(EmbSimpleRunCtxT *embCtx)
{
    while (embCtx->concurentCtrl.labelLock.count != 0) {
        uint32_t latchCount = embCtx->concurentCtrl.labelLock.count;
        LabelRWLatchT **latch = DbListItem(&embCtx->concurentCtrl.labelLock, latchCount - 1);
        LabelWLatchRelease(*latch);
        DbDelListItem(&embCtx->concurentCtrl.labelLock, latchCount - 1);
    }
}

void SimpRelRestoreClearNsp(EmbSimpleRunCtxT *embCtx, SimpRelRestoreCtxT *restoreCtx, bool isExistDb)
{
    SimpRelReleaseLabelLatch(embCtx);
    (void)SimpRelCleanAllEdges(embCtx->nspCtrl);
    (void)SimpRelCleanAllLabels(embCtx->seRunCtx, embCtx->simpleRelMgr->serviceMemCtx, embCtx->nspCtrl);
    DmNamespaceT *nsp = NULL;
    (void)CataGetNamespaceById(embCtx->nspCtrl->metaId, &nsp, embCtx->dbInstance);
    (void)CataReleaseNamespace(embCtx->dbInstance, nsp);
    (void)SimpleRelExecDropNamespace(embCtx->seRunCtx, nsp->metaCommon.metaName);
    if (embCtx->nspCtrl->loadPath != NULL && strcmp(restoreCtx->srcPath, restoreCtx->loadPath) != 0) {
        Status ret = SimpleRelCheckIsLoadPathExisted(embCtx->simpleRelMgr, restoreCtx->loadPath, false);
        if (ret == GMERR_OK) {
            (void)DbRemoveFileNoLog(embCtx->nspCtrl->loadPath);
        }
    }
    SimpleRelFreeNspCtrl(embCtx->simpleRelMgr->serviceMemCtx, embCtx->nspCtrl);
    if (!isExistDb) {
        DbDelListItem(&embCtx->simpleRelMgr->nspCtrlList, embCtx->simpleRelMgr->nspCtrlList.count - 1);
    }
}

static void SimpleRelReleaseDescCtx(FileBufT *fileBuf, DbMemCtxT *memCtx)
{
    if (fileBuf->fd != DB_INVALID_FD) {
        DbCloseFile(fileBuf->fd);
    }
    DbDeleteDynMemCtx(memCtx);
}

Status SimpRelGetDbDescPrepare(
    FileBufT *fileBuf, SimpRelGetDbInfoCtxT *getDbInfoCtx, uint32_t *descPos, uint32_t *actDescLen)
{
    Status ret = SimpleRelGetDescFilePos(fileBuf, descPos);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbFileBufRead(fileBuf->fd, *descPos, sizeof(uint32_t), (uint8_t *)actDescLen);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SimpRelCheckDescForDb(*actDescLen, (uint8_t *)getDbInfoCtx->descInfo, getDbInfoCtx->descLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status SimpRelGetDbDescFromDataFile(EmbSimpleRunCtxT *embCtx, SimpRelGetDbInfoCtxT *getDbInfoCtx)
{
    FileBufT fileBuf;
    // memCtx用途：用于导出db desc信息时申请内存
    // 生命周期：单消息级别
    // 释放方式：异常就近释放为主,提供接口进行兜底释放
    // 兜底清空措施：在导出返回前，调用SimpRelReleaseExportDataCtx释放
    DbMemCtxArgsT args = {};
    DbMemCtxT *memCtx = DbCreateDynMemCtx(embCtx->apiMemCtx, false, "exportDescInfoMemCtx", &args);
    if (memCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Unable to create memctx when get Db desc info.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    DbFileBufInit(memCtx, &fileBuf);
    Status ret = SimpRelImportFileBufPrepare(getDbInfoCtx->nspDir, &fileBuf);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    bool isNeedTransEndian = DbNeedSwapEndian(fileBuf.header.endianType);
    SimpRelTryTransHeaderEndian(&fileBuf, isNeedTransEndian);
    uint32_t descPos = 0;
    uint32_t actDescLen = 0;
    ret = SimpRelGetDbDescPrepare(&fileBuf, getDbInfoCtx, &descPos, &actDescLen);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    uint8_t *dbDesc = (uint8_t *)DbDynMemCtxAlloc(memCtx, actDescLen);
    if (dbDesc == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Unable to alloc memctx when for Db desc.");
        ret = VOS_ERRNO_DB_MEMALLOCFAILURE;
        goto EXIT;
    }

    (void)memset_s(dbDesc, actDescLen, 0x00, actDescLen);
    uint32_t seekPos = descPos + sizeof(uint32_t);
    ret = DbFileBufRead(fileBuf.fd, seekPos, actDescLen, dbDesc);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    errno_t err = memcpy_s((char *)getDbInfoCtx->descInfo, actDescLen, dbDesc, actDescLen);
    if (err != EOK) {
        ret = VOS_ERRNO_DB_MEMCPY_FAILURE;
        DB_LOG_ERROR(VOS_ERRNO_DB_MEMCPY_FAILURE, "Failure of memory copy for db description.");
        goto EXIT;
    }

    *(getDbInfoCtx->descLen) = actDescLen;
EXIT:
    SimpleRelReleaseDescCtx(&fileBuf, memCtx);
    return ret;
}

static Status RestoreCreateUuidNsp(EmbSimpleRunCtxT *embCtx, NspCtrlT *newNsp)
{
    DmNamespaceT nameSpace = {0};
    nameSpace.owner = DbStr2Text(DEFAULT_NAMESPACE_OWNER);
    char restoreNspName[DB_NAME_LEN] = {0};
    uint32_t uuid;
    Status ret = CataGenerateUuid(embCtx->dbInstance, &uuid);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to generate uuid when create restore nsp.");
        return ret;
    }
    (void)sprintf_s(restoreNspName, DB_NAME_LEN, "%u", uuid);
    nameSpace.metaCommon.metaName = restoreNspName;
    nameSpace.trxInfo.isolationLevel = g_adptV1Instance.isolationLevel[embCtx->isTwoStage];
    nameSpace.trxInfo.trxType = OPTIMISTIC_TRX;
    nameSpace.creator = DEFAULT_NAMESPACE_OWNER;
    ret = CataGetTspIdByName(PUBLIC_TABLESPACE_NAME, &nameSpace.defaultTspId, embCtx->dbInstance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to get table space when create restore nsp.");
        return ret;
    }
    if ((ret = SimpleRelExecCreateNamespace(embCtx->seRunCtx, &nameSpace)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to create restore namespace.");
        return ret;
    }
    newNsp->metaId = nameSpace.metaCommon.metaId;
    return GMERR_OK;
}

void SimprelReleaseNamespace(EmbSimpleRunCtxT *embCtx, NspCtrlT *nspCtrl)
{
    DmNamespaceT *nsp = NULL;
    (void)CataGetNamespaceById(nspCtrl->metaId, &nsp, embCtx->dbInstance);
    (void)CataReleaseNamespace(embCtx->dbInstance, nsp);
    (void)SimpleRelExecDropNamespace(embCtx->seRunCtx, nsp->metaCommon.metaName);
    SimpleRelFreeNspCtrl(embCtx->simpleRelMgr->serviceMemCtx, nspCtrl);
}

static Status SimprelRestoreCreateNamespace(EmbSimpleRunCtxT *embCtx, SimpRelRestoreCtxT *restoreCtx, bool onlyCreate)
{
    // 创建nsp但是不挂载
    NspCreateCtxT *ctx = (NspCreateCtxT *)DbDynMemCtxAlloc(embCtx->apiMemCtx, sizeof(NspCreateCtxT));
    if (ctx == NULL) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE,
            "Unable to alloc create tbl ctx, alloc size is %" PRIu32 ".", (uint32_t)sizeof(NspCreateCtxT));
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }
    (void)memset_s(ctx, sizeof(NspCreateCtxT), 0x00, sizeof(NspCreateCtxT));
    ctx->namespaceName = restoreCtx->nspName;
    ctx->loadPath = (const char *)restoreCtx->loadPath;
    ctx->nspId = DB_MAX_UINT32;
    ctx->onlyCreate = onlyCreate;
    ctx->isTpcDb = restoreCtx->isTpcDb;
    ctx->pstChecksum = restoreCtx->pstChecksum;
    Status ret = GMERR_OK;
    EmbSimpRelMgrT *simpleRelMgr = embCtx->simpleRelMgr;
    NspCtrlT *newNsp = (NspCtrlT *)DbDynMemCtxAlloc(embCtx->simpleRelMgr->serviceMemCtx, sizeof(NspCtrlT));
    if (newNsp == NULL) {
        DbDynMemCtxFree(embCtx->apiMemCtx, ctx);
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE,
            "Unable to alloc create tbl ctx, alloc size is %" PRIu32 ".", (uint32_t)sizeof(NspCreateCtxT));
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }
    (void)memset_s(newNsp, sizeof(NspCtrlT), 0x00, sizeof(NspCtrlT));
    ret = RestoreCreateUuidNsp(embCtx, newNsp);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(embCtx->simpleRelMgr->serviceMemCtx, newNsp);
        DbDynMemCtxFree(embCtx->apiMemCtx, ctx);
        return ret;
    }
    ret = SimpleRelInitAndInsertNspCtrl(ctx, simpleRelMgr, newNsp);
    if (ret != GMERR_OK) {
        SimprelReleaseNamespace(embCtx, newNsp);
        DbDynMemCtxFree(embCtx->simpleRelMgr->serviceMemCtx, newNsp);
        DbDynMemCtxFree(embCtx->apiMemCtx, ctx);
        return ret;
    }
    DbDynMemCtxFree(embCtx->apiMemCtx, ctx);
    if (onlyCreate) {
        embCtx->nspCtrl = newNsp;
    } else {
        DbDynMemCtxFree(embCtx->simpleRelMgr->serviceMemCtx, newNsp);
        embCtx->nspCtrl =
            (NspCtrlT *)DbListItem(&embCtx->simpleRelMgr->nspCtrlList, embCtx->simpleRelMgr->nspCtrlList.count - 1);
    }
    return GMERR_OK;
}

static bool IsFileChange(ImportDataCtxT *ctx, SimpRelRestoreCtxT *restoreCtx, NspCtrlT *nspCtrl)
{
    if (ctx->isNeedTransEndian) {
        return true;
    }
    if (restoreCtx->pstChecksum != NULL) {
        return true;
    }
    if (ctx->isImpCheckSum != (nspCtrl->pstChksum != NULL)) {
        return true;
    }
    return false;
}

static Status SimpleRelCopyDataFile(
    EmbSimpleRunCtxT *embCtx, ImportDataCtxT *ctx, SimpRelRestoreCtxT *restoreCtx, bool isExist)
{
    if (!restoreCtx->dbRestoreCfg->bPersistent || strcmp(restoreCtx->srcPath, restoreCtx->loadPath) == 0) {
        return GMERR_OK;
    }
    NspCtrlT *nspCtrl = isExist ? restoreCtx->nspCtrl : embCtx->nspCtrl;
    if (!IsFileChange(ctx, restoreCtx, nspCtrl)) {
        return SimpCopyFile(restoreCtx->srcPath, restoreCtx->loadPath);
    }

    return SimpleRelationDataExportNoCheck(embCtx, restoreCtx->loadPath, embCtx->nspCtrl->pstChksum != NULL);
}

void SimpleRelReloadNspCtrl(EmbSimpleRunCtxT *embCtx, SimpRelRestoreCtxT *restoreCtx, bool isExist)
{
    if (isExist == false) {
        return;
    }
    embCtx->nspCtrl = restoreCtx->nspCtrl;
}

Status SimpleRelImportPrepareNsp(EmbSimpleRunCtxT *embCtx, SimpRelRestoreCtxT *restoreCtx, bool *isExist)
{
    const char *nspName = (const char *)restoreCtx->nspName;
    NspCtrlT *tnpNspCtrl = SimpleRelGetNspCtrlByName(&embCtx->simpleRelMgr->nspCtrlList, nspName, NULL);
    // db存在，根据restoreType清理db或报错
    if (tnpNspCtrl != NULL) {
        if (restoreCtx->restoreType != DB_RESTORETYPE_REPLACE) {
            DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALID_DBNAME, "DB with the same name exists. Name is %s.", nspName);
            return VOS_ERRNO_DB_INVALID_DBNAME;
        } else {
            restoreCtx->nspCtrl = tnpNspCtrl;
            *isExist = true;
        }
        // 调用tpc的导入，需操作tpc的nsp
        if (restoreCtx->isTpcDb) {
            if (!tnpNspCtrl->isTpcDb) {
                DB_LOG_AND_SET_LASERR(
                    VOS_ERRNO_DB_INVALID_DATABASE, "DB is not tpc db when restore with tpc api. Name is %s.", nspName);
                return VOS_ERRNO_DB_INVALID_DATABASE;
            }
        }
    }
    Status ret = SimprelDetectActiveHandleOrCDB(embCtx, restoreCtx, isExist);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SimprelRestoreCreateNamespace(embCtx, restoreCtx, *isExist);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status SimpRelImport4V5(EmbSimpleRunCtxT *embCtx, SimpRelRestoreCtxT *restoreCtx, ImportDataCtxT *ctx)
{
    Status ret = SimpRelInitImportDataCtx(embCtx, restoreCtx, ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Restore init import context unsucc.");
        return ret;
    }
    if (restoreCtx->isAutoRestore && ((ctx->isImpCheckSum && restoreCtx->pstChecksum == NULL) ||
                                         (!ctx->isImpCheckSum && restoreCtx->pstChecksum != NULL))) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DBKNL_MISMATCH_IN_DBFILE_CFG, "Mismatch in DB File checksum configuration.");
        return VOS_ERRNO_DBKNL_MISMATCH_IN_DBFILE_CFG;
    }
    // 导入 DbDesc
    ret = SimpRelImportDbDesc(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Restore db desc unsucc.");
        return ret;
    }
    // 导入表
    ret = SimpRelImportMetaData(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Restore metadata unsucc.");
        return ret;
    }

    // 导入数据
    ret = SimpRelImportData(ctx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Restore heap data unsucc.");
        return ret;
    }
    return GMERR_OK;
}

static Status SimpleRelImportByFileType(EmbSimpleRunCtxT *embCtx, SimpRelRestoreCtxT *restoreCtx, ImportDataCtxT *ctx)
{
    Status ret = GMERR_OK;
    if (ctx->fileType == V5_BIN_FLAG) {
        // v5 二进制文件 导入流程
        ret = SimpRelImport4V5(embCtx, restoreCtx, ctx);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else if (ctx->fileType <= DB_LITTLE_ENDIAN) {
        // v1 二进制文件 导入流程
        ret = SimpRelImport4V1(embCtx, restoreCtx, ctx);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALID_BYTEORDER, "Bytes order(%u) of file is not valid.", ctx->fileType);
        return VOS_ERRNO_DB_INVALID_BYTEORDER;
    }
    return GMERR_OK;
}

static Status SimpleRelGetFileFlagAndDbName(DbMemCtxT *memCtx, SimpRelRestoreCtxT *restoreCtx, ImportDataCtxT *ctx)
{
    const char *loadPath = restoreCtx->srcPath;
    int32_t fd = DB_INVALID_FD;
    Status ret = DbFileReadPrepare(loadPath, &fd);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = DbFileBufRead(fd, 0, sizeof(uint8_t), &ctx->fileType);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(VOS_ERRNO_DB_READFUNCTION_ERROR, "Read file type from file unsucc.");
        ret = VOS_ERRNO_DB_READFUNCTION_ERROR;
        goto EXIT;
    }
    uint8_t isV5FileFlag = 0;
    ret = DbFileBufRead(fd, SIMP_FILE_V1_FLAG_POSITION, sizeof(uint8_t), &isV5FileFlag);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(VOS_ERRNO_DB_READFUNCTION_ERROR, "Read file flag from file unsucc.");
        ret = VOS_ERRNO_DB_READFUNCTION_ERROR;
        goto EXIT;
    }
    if (isV5FileFlag == SIMPREL_V5_FILE_FLAG) {
        ctx->fileType = V5_BIN_FLAG;
    }

    if (!restoreCtx->isUseFileDbName) {
        goto EXIT;  // 导入设置的dbName字符串长度不为0
    }

    uint8_t *dbName = DbDynMemCtxAlloc(memCtx, DB_NAME_LEN);
    if (dbName == NULL) {
        ret = VOS_ERRNO_DB_MEMALLOCFAILURE;
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "Unabel to alloc db name when import.");
        goto EXIT;
    }

    ret = DbFileBufRead(fd, SIMPLEREL_FILE_DB_NAME_OFFSET, DB_NAME_LEN, dbName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(VOS_ERRNO_DB_READFUNCTION_ERROR, "Read db name from file unsucc.");
        ret = VOS_ERRNO_DB_READFUNCTION_ERROR;
        DbDynMemCtxFree(memCtx, dbName);
        goto EXIT;
    }
    restoreCtx->nspName = (const char *)dbName;
EXIT:
    DbCloseFile(fd);
    return ret;
}

Status SimpleRelationDataImport(EmbSimpleRunCtxT *embCtx)
{
    DB_POINTER(embCtx);
    uint64_t startTime = DbClockGetTsc();
    SimpRelRestoreCtxT *restoreCtx = embCtx->apiCtx;
    if (!DbFileExist(restoreCtx->srcPath)) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_OPENFILE_ERROR, "import file %s not exist", restoreCtx->srcPath);
        return VOS_ERRNO_DB_OPENFILE_ERROR;
    }

    Status ret = GMERR_OK;
    // 非replace导入 需要校验DB持久化路径是否已被使用
    if (restoreCtx->restoreType != DB_RESTORETYPE_REPLACE) {
        ret = SimpleRelCheckIsLoadPathExisted(embCtx->simpleRelMgr, restoreCtx->loadPath, true);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    ImportDataCtxT ctx = {0};
    ret = SimpleRelGetFileFlagAndDbName(embCtx->apiMemCtx, restoreCtx, &ctx);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 导入的nsp是否存在
    bool isExist = false;
    ret = SimpleRelImportPrepareNsp(embCtx, restoreCtx, &isExist);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Restore prepare db unsucc.");
        return ret;
    }

    ret = SimpleRelImportByFileType(embCtx, restoreCtx, &ctx);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = SimpleRelCopyDataFile(embCtx, &ctx, restoreCtx, isExist);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    // 将导入数据后的nsp内容copy到nspList的旧nsp上
    ret = SimpRelImportCopyNsp(embCtx, restoreCtx->nspName, isExist);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Swap namespace unsucc.");
        goto EXIT;
    }
    uint64_t endTime = DbClockGetTsc();
    DbSetExImportTaskTime(PERIOD_TOTAL, endTime - startTime, &ctx.perfStat);
    DbImportPerfCheck(&ctx.perfStat);
    SimpRelReleaseImportDataCtx(embCtx, restoreCtx, &ctx);
    return GMERR_OK;

EXIT:
    SimpRelReleaseImportDataCtx(embCtx, restoreCtx, &ctx);
    SimpRelRestoreClearNsp(embCtx, restoreCtx, isExist);
    SimpleRelReloadNspCtrl(embCtx, restoreCtx, isExist);
    return ret;
}
