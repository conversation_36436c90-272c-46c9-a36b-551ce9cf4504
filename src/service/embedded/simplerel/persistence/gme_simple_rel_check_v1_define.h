/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: gme_simple_rel_persistence.h
 * Description: the header file of checking v1 data file implementation on embedded service.
 * Author:
 * Create:
 */
#ifndef GME_SIMP_REL_CHECK_V1_DEFINE_H
#define GME_SIMP_REL_CHECK_V1_DEFINE_H

#ifdef __cplusplus
extern "C" {
#endif

#pragma pack(4)
typedef struct tagDB_DBMS_STRU DB_DBMS_STRU;
/* Structure for version info in the backup file */
typedef struct tagDB_VER_IN_BAKFILE_STRU {
    VOS_UINT8 ucTnum;    /* Indicates the Temp version number */
    VOS_UINT8 ucVnum;    /* Indicates the 'V' number in the version string */
    VOS_UINT8 ucRnum;    /* Indicates the 'R' number in the version string */
    VOS_UINT8 ucCnum;    /* Indicates the 'C' number in the version string */
    VOS_UINT16 usSPCnum; /* Indicates the 'SPC' number in   version string */
    VOS_UINT16 usBnum;   /* Indicates the 'B' number in   version string   */
} DB_VER_IN_BAKFILE_STRU;

/* Enum to indicate whether Checksum reference is present in DB file or not */
typedef enum tagDB_CKSUM_INDICATOR_ENUM {
    DB_CKSUM_INDICATOR_NOT_PRESENT = 0, /* Indicates Checksum not present */
    DB_CKSUM_INDICATOR_PRESENT = 1,     /* Indicates Checksum is present  */
    DB_CKSUM_INDICATOR_BUTT
} DB_CKSUM_INDICATOR_ENUM;

/* Structure to denote the metainfo for the directory entries (each reflecting
   on details of some optional info like v_byte, checksum etc in the DB file)
   The presence of this structure in the DB file will be optional */
typedef struct tagDB_DIR_STRU {
    VOS_UINT32 ulNumOfDirEntry;    /* Number of directory entries */
    VOS_UINT32 ulDirEntrySize;     /* The fixed size of each directory entry */
    VOS_UINT32 ulOffsetToDirEntry; /* Start of the directory entries from the beginning of the file */
} DB_DIR_STRU;

/* Structure defining directory entries (optional) */
typedef struct tagDB_DIR_ENTRY_STRU {
    VOS_UINT8 ucType;     /* Type of the optional data */
    VOS_UINT8 aucRsvd[3]; /* 3 represent 3 reserve bytes for alignment */
    VOS_UINT32 ulOffset;  /* Offset to Optional Mgr in File */
    VOS_UINT32 ulSize;    /* Size of Optional Mgr           */
} DB_DIR_ENTRY_STRU;

/* Union used for all pointers in DB. Useful for 64bit support                */
typedef union tagDB_MEMPTR {
    VOS_VOID *pPtr;          /* Pointer to a memory block */
    VOS_UINT8 aucReserve[8]; /* 8 represent 8 reserve bytes for future use */
} DB_MEMPTR;

/* Structure to hold the Relation's checksum value */
typedef struct tagDB_RELCHECKSUM_DATA_STRU {
    DB_CKSUM_DATA_T ulRelMetaChecksum; /* Checksum value for Rel - metadata */
    DB_CKSUM_DATA_T ulRelDataChecksum; /* Checksum value for Rel - records  */
} DB_RELCHECKSUM_DATA;

/* Enum defining optional Managers type */
typedef enum TagDbDirType {
    DB_DIR_VARSEG_REL = 0,   /* Rel Specific Dir Entry */
    DB_DIR_VARSEG_DB = 1,    /* DB Specific Dir Entry */
    DB_DIR_REL_CHECKSUM = 2, /* Opt Manager type: Rel Checksum */
    DB_DIR_TYPE_BUTT = 0xFF
} DbDirTypeE;

/* Relation Field Descriptor Structure which is part of Table Space Management */
typedef struct tagDBDDL_RFLDDESTBL_STRU {
    VOS_UINT8 aucFldName[DB_FLD_NAME_LEN]; /* Field Name                    */
    VOS_UINT8 ucDataType;                  /* Field Data type                            */
    VOS_UINT8 ucIsVarField;                /* Flag to indicate this is var field or not. */
    VOS_UINT8 ucPos;                       /* Position of this var field in var field set in the relation. */
    VOS_UINT8 aucReserve[1];               /* 1 represent 1 reserve bytes for alignment */
    VOS_UINT16 usDefLen;                   /* Field Defined Length                       */
    VOS_UINT16 usStoredLen;                /* Field Stored Length                        */
    VOS_UINT32 ulDefaultValue;             /* Default value used for Wildcard feature    */
    T_OFFSET offFld;                       /* Position of the field in data seg record   */
    DB_MEMPTR ppfnCompareFunc;             /* Array of field compare function            */
} DBDDL_RFLDDESTBL_STRU;

/* Hash index segment header description header                               */
typedef struct tagDBFI_HASHSEG_STRU {
    VOS_UINT8 aucReserve[2];    /* 2 represent 2 reserve bytes for alignment */
    VOS_UINT8 ucGlobalDepth;    /* Current global depth                    */
    VOS_UINT8 ucAllocDepth;     /* Allocated depth                         */
    T_OFFSET offHashDir;        /* Offset to the hash directory            */
    T_OFFSET offHashBucketPool; /* Offset to Pool of hash buckets.         */
    T_OFFSET offOverFlowPool;   /* offset to Pool of overflow nodes.       */
    DB_MEMPTR pfnHashFunc;      /* Pointer to the hash function            */
} DBFI_HASHSEG_STRU;

/* TTree index segment description structure                                  */
typedef struct tagDBFI_TTREESEG_STRU {
    T_NODE nodeTTreeRoot;       /* Root node of the TTree                    */
    T_OFFSET offTTreeHead;      /* Offset to TTREE block                     */
    VOS_UINT32 ulUsedTTreeNode; /* Number of TTREE node used                 */
} DBFI_TTREESEG_STRU;

/* Union to specify the Hash/TTree segment header                             */
typedef union tagDBFI_INDEXSEG_UNION {
    DBFI_HASHSEG_STRU stHashSeg;   /* Hash segment header                   */
    DBFI_TTREESEG_STRU stTTreeSeg; /* TTree segment header                  */
} DBFI_INDEXSEG_UNION;

/* Index Description Structure which is part of Table Space Management        */
typedef struct tagDBDDL_IDXDESTBL_STRU {
    VOS_UINT8 aucIndexName[DB_IDX_NAME_LEN]; /* Index Name                  */
    VOS_UINT32 ulSearchCount;                /* Indicates the index search count.       */
    VOS_UINT8 ucType;                        /* Specifies the Type of the Index         */
    VOS_UINT8 ucUnique;                      /* Uniqueness flag                         */
    VOS_UINT8 ucFldNum;                      /* Number of Fields Used for the Index     */
    VOS_UINT8 ucRegFunc;                     /* Denote if index is affected by reg func */
    VOS_UINT8 ucNonSync;
    VOS_UINT8 aucReserve[3];             /* 3 represent 3 reserve bytes for alignment */
    T_FIELD afIdxFldAry[DB_IDX_FLD_MAX]; /* Field ID array of Idx fields  */
    DBFI_INDEXSEG_UNION unSegHead;       /* Union for Hash/TTree Segment header     */
} DBDDL_IDXDESTBL_STRU;

/* Structure to define Relation Optinal Mgrs Metadata info */
typedef struct tagDB_RELDIR_STRU {
    VOS_UINT32 ulNumOfDirEntry; /* Number of Mgrs              */
    VOS_UINT32 ulDirEntrySize;  /* Size of each Optional Mgr   */
} DB_RELDIR_STRU;

/* Enumeration used for table extend logic                                    */
typedef enum tagDB_TBLEXTEND_ENUM {
    DB_LINEAR_TABLE_EXTEND = 0,   /* Default table extend (linear)      */
    DB_FLEXIBLE_TABLE_EXTEND = 1, /* Flexible table extend logic        */
    DB_TABLE_EXTEND_BUTT
} DB_TBLEXTEND_ENUM;

/* Structure to store the details about table extend operation for debug      */
typedef struct tagDB_TBLDATABLK_INFO_STRU {
    VOS_UINT32 ulDbId;                     /* Database ID                        */
    VOS_UINT8 aucDBName[DB_NAME_LEN];      /* Name of corresponding database     */
    VOS_UINT8 aucRelName[DB_REL_NAME_LEN]; /* Extend table name              */
    VOS_UINT16 usTableId;                  /* Relation ID                        */
    VOS_UINT16 usExtendBlkNo;              /* Current logical extend block count */
    VOS_UINT32 ulExtendMemSize;            /* Total size of extend memory blocks */
    VOS_UINT32 ulExtendRec;                /* Extend record number               */
    VOS_UINT32 ulCurrMaxRec;               /* Current max record number          */
    VOS_UINT32 ulTotalMaxRec;              /* Total max record number of table   */
    DB_TBLEXTEND_ENUM enExtendType;        /* Extend logic set for table         */
    VOS_UINT32 ulMaxStepSize;              /* Max step size for flexible extend  */
    VOS_UINT16 usGrowthRate;               /* Growth rate of flexible extend     */
    VOS_UINT8 ucReserve[2];                /* 2 represent 2 reserve bytes for alignment */
} DB_TBLDATABLK_INFO;

/* Structure used to store all memory block                       */
typedef struct tagDB_MEMBLK_STRU {
    DB_MEMPTR pMemPtr;    /* Pointer to the memory allocated             */
    VOS_UINT32 ulFOffset; /* File offset of the corresponding block      */
    VOS_UINT32 ulBlkSize; /* Size of the Memory block allocated          */
} DB_MEMBLK_STRU;

/* Segment Header structure, Map to Common Header of the HLD                  */
typedef struct tagDBFI_SEGHEAD_STRU {
    VOS_UINT32 ulUsedElemCnt; /* Count of used block elements in all blocks   */
    VOS_UINT32 ulFreeElemCnt; /* Count of free block elements in all blocks   */
    VOS_UINT32 ulElemSize;    /* Size of Each element                         */
    VOS_UINT32 ulFreeList;    /* Index to the head of free block elements     */
} DBFI_SEGHEAD_STRU;

/* Relation Descriptor Structure which is part of Table Space Management      */
typedef struct tagDBDDL_RELDESTBL_STRU {
    VOS_UINT8 aucRelName[DB_REL_NAME_LEN]; /* Table Name                    */
    T_SIZE nCurRecMax;                     /* Max Number of Records the Table can hold   */
    T_SIZE nExtendRecNum;                  /* Number of records to be extended           */
    T_SIZE nTotalMaxRec;                   /* Maximum records allowed for the relation   */
    T_SIZE nMaxRecLen;                     /* Max Record length of the relation          */
    VOS_UINT32 ulGrowthRate;               /* Growth rate to extend data segment         */
    VOS_UINT32 ulMaxStepSize;              /* Max step size for data segment extend      */
    VOS_UINT32 ulSearchCount;              /* Indicates the direct search count.         */
    VOS_UINT32 ulNodePerIndex;             /* Number of TTREE node for the current max   */
    VOS_UINT32 ulSchemaSize;               /* Total size of Table Space                  */
    VOS_UINT16 usFirstIdx;                 /* First Index descriptor offset              */
    VOS_UINT16 usNumDIBlks;                /* Number of Data/Index Blocks                */
    VOS_UINT8 ucRegFunc;                   /* Specifies regenerate Index required or not */
    VOS_UINT8 ucFldNum;                    /* Number of Fields in the Relation           */
    VOS_UINT8 ucIdxNum;                    /* Number of Indexes for this Relation        */
    VOS_UINT8 ucCkpt;                      /* Checkpoint type if it is Normal table      */
    VOS_UINT8 ucTTreeIdxNum;               /* Number of TTREE index                      */
    VOS_UINT8 ucHashIdxNum;                /* Number of HASH index                       */
    VOS_UINT8 ucNextReAllocBlk;            /* Block ID needs to be reallocated for CDB  */
    VOS_UINT8 ucStorage;                   /* Indicates Storage location for Data        */
    DB_MEMPTR pTblRegFeatures;             /* Pointer to the manager of table features   */
    DBFI_SEGHEAD_STRU stDataSegHead;       /* Common Data segment header            */
    DB_MEMBLK_STRU stDataIdxMng;           /* Pointer to Data/Index array management */
} DBDDL_RELDESTBL_STRU;

/* Log Type Enumeration */
typedef enum tagDBTP_LOGTYPE_ENUM {
    DBTP_LOGTYPE_INSERT = 0,
    DBTP_LOGTYPE_DELETE = 1,
    DBTP_LOGTYPE_UPDATE = 2,
    DBTP_LOGTYPE_DELRDBBYCDB = 3,
    DBTP_LOGTYPE_DELCDBBITSLOT = 4,
    DBTP_LOGTYPE_ADDCDBBITSLOT = 5,
    DBTP_LOGTYPE_UPDATE_SPECIAL = 6,
    /* Different Undo types for Row in Page */
    DBTP_LOGTYPE_ROWALLOCLIST = 7, /* Row Alloc from Free List in Page   */
    DBTP_LOGTYPE_ROWALLOCCONT = 8, /* Row Alloc from Cont Memory in Page */
    DBTP_LOGTYPE_ROWFREELIST = 9,  /* Row Free to Free List in Page      */
    DBTP_LOGTYPE_ROWFREECONT = 10  /* Row Free to Cont Memory in Page    */
} DBTP_LOGTYPE_ENUM;

/* Function prototype of the table new data block notify hook                 */
typedef VOS_VOID (*DB_TBLDATABLK_NOTIFY_FUNC)(const DB_TBLDATABLK_INFO *pstDataBlkInfo);

/* Function pointer to free allocate CDB Bit block Manager */
typedef VOS_UINT32 (*TPC_ALLOCCDBBITBLKMGR)(DBDDL_RELDESTBL_STRU *pstRelDes, VOS_UINT16 usNumBlksReq);

/* Function prototype of the file Sync operation hook                         */
typedef VOS_INT32 (*DB_FSync)(FILE *pfHandle);

/* Function pointer to free CDB Bit block Manager */
typedef VOS_VOID (*TPC_FREECDBBITBLKMGR)(DBDDL_RELDESTBL_STRU *pstRelDes, VOS_BOOL bFlag);

/* Function pointer to free CDB Bit block index */
typedef DB_ERR_CODE (*TPC_FREECDBBITBLKSLOT)(
    DB_DBMS_STRU *pstDBMgr, DBDDL_RELDESTBL_STRU *pstRelDes, T_RECNO recNo, VOS_UINT8 ucCDBId);

typedef DB_ERR_CODE (*TPC_REGENERATEINDEX)(DBDDL_RELDESTBL_STRU *pstRelDes, VOS_UINT8 *pucIdxAffectList);

/* Function pointer to free CDB Table */
typedef VOS_VOID (*DB_FREECDBTABLE)(DB_DBMS_STRU *pstDBMgr, DBDDL_RELDESTBL_STRU *pstRDBRelDes);

typedef VOS_VOID (*TPC_INSERTLOG)(DBTP_LOGTYPE_ENUM enLogType, DBDDL_RELDESTBL_STRU *pstRelDes, T_RECNO recNo,
    VOS_UINT8 **ppucData, VOS_UINT32 *ulSize);

typedef VOS_UINT8 *(*TPC_EXECUTELOG)(DBTP_LOGTYPE_ENUM enLogType, DBDDL_RELDESTBL_STRU *pstRelDes, T_RECNO recNo);
typedef VOS_BOOL (*TPC_ISTPCDB)(VOS_UINT32 ulDBId, DBKNL_CPUTICK *pstTime);

typedef DBTC_OPERATION_FUNC *(*TPC_RDBVALIDFLDCOMPFUNC)(VOS_VOID);
typedef VOS_VOID (*TPC_SETFLDCOMPFUNC)(DBDDL_RELDESTBL_STRU *pstRelDes);

typedef uint64_t (*TPC_GETTPCDBSIZE)(VOS_UINT32);
typedef VOS_UINT32 (*TPC_GETRESMEMSIZE)(VOS_VOID);

/* Structure for registering CDB Bit Block operations hook functions          */
typedef struct tagDB_REGCDBBLKOPHOOK_STRU {
    TPC_ALLOCCDBBITBLKMGR pfnAllocCDBBitBlkMgr;
    TPC_FREECDBBITBLKMGR pfnFreeCDBBitBlkMgr;
    TPC_FREECDBBITBLKSLOT pfnFreeCDBBitBlkSlot;
    TPC_REGENERATEINDEX pfnReGenerateIndex;
    DB_FREECDBTABLE pfnFreeCDBTableSpace;
    TPC_INSERTLOG pfnInsertLog;
    TPC_EXECUTELOG pfnExecuteLog;
    TPC_ISTPCDB pfnIsTpcDB;
    TPC_RDBVALIDFLDCOMPFUNC pfnRDBValidFldCompFunc;
    TPC_SETFLDCOMPFUNC pfnSetFldCompFunc;
    TPC_GETTPCDBSIZE pfngetTpcDbSize;
} DB_REGCDBBLKOPHOOK_STRU;

typedef pthread_mutex_t DB_MTXLOCK_T;

/**
 * @ingroup vos_sema
 * 信号量类型定义。
 */
typedef void *VOS_SEMA_T;

/* CP Context Structure                                                       */
typedef struct tagDBCP_CONTEXT_STRU {
    T_RELATION rRelID;
    VOS_UINT8 enAccessType;
    VOS_UINT8 enWaitType;
    VOS_UINT32 ulDbId;
    VOS_UINT32 ulTaskId;
    VOS_SEMA_T stCtrlSem;
    struct tagDBCP_CONTEXT_STRU *pstNext;
} DBCP_CONTEXT_STRU;

/* Log Block Header */
typedef struct tagDBCP_LOGHEAD_STRU {
    T_OFFSET offStackTop;                  /* Offset of the next free Log Item in Buffer  */
    VOS_UINT8 aucData[sizeof(VOS_UINT32)]; /* Pointer to the data */
} DBCP_LOGHEAD_STRU;

/* Log Control Structure                                                      */
typedef struct tagDBCP_LOGCTRL_STRU {
    VOS_UINT32 ulSize;
    VOS_UINT32 ulMbdId; /* MDB Id cached for free flow */
    DBCP_LOGHEAD_STRU *pstLogHead;
    struct tagDBCP_LOGCTRL_STRU *pstNextLogBuf;
} DBCP_LOGCTRL_STRU;

/* Log Item Structure */
typedef struct tagDBTP_LOGITEM_STRU {
    DBTP_LOGTYPE_ENUM enLogType;     /* Log type - Insert,Update or Delete   */
    T_RECNO recNo;                   /* Record Number                        */
    T_SIZE nDataLen;                 /* Length of the data                   */
    DBDDL_RELDESTBL_STRU *pstRelDes; /* Relation Descriptor                  */
    VOS_UINT8 aucData[0];            /* Pointer to the data                  */
} DBTP_LOGITEM_STRU;

/* Undo Structure which is used for Tables in RSM and Reserved to handle the case of Revert RDB rec */
struct tagDBTP_REVERTOP_UNDO_STRU {
    /* Undo Entry for Alloc Flag */
    VOS_BOOL bAllocFlag;
    T_RELATION rRelNo;
    VOS_UINT8 aucRsvd[2]; /* 2 represent 2 reserve bytes for alignment */
    T_RECNO rRecId;
    /* Undo Entry for Var Data Page */
    VOS_BOOL bVarPageFlag;
    DBTP_LOGITEM_STRU stLogItem;
};

/* Structure defining operation item details(Internal) */
typedef struct tagDB_OP_STATUS_ITEM_STRU {
    VOS_SEMA_T *pstStatItemSem;
    DBKNL_CPUTICK stOpTimestamp;
    VOS_INT32 lNumofTblWritten;
    VOS_UINT32 ulTotNumofTbl;
    VOS_UINT8 enState;
    VOS_UINT8 enOpType;
    VOS_UINT8 aucReserved[2]; /* 2 represent 2 reserve bytes for alignment */
    VOS_UINT8 aucFileName[DB_PATH_LEN];
    VOS_UINT8 aucReserved1[8]; /* 8 represent 8 reserve bytes for future use */
    struct tagDB_OP_STATUS_ITEM_STRU *pstNext;
} DB_OP_STATUS_ITEM_STRU;

/* Structure defining Write operation details */
typedef struct tagDB_OP_WRITE_ITEM_STRU {
    VOS_SEMA_T stStatCtrlSem;
    VOS_UINT8 ucElementCnt;
    VOS_UINT8 aucRsvd[3]; /* 3 represent 3 reserve bytes for alignment */
    DB_OP_STATUS_ITEM_STRU *pstHead;
} DB_OP_WRITE_ITEM_STRU;

typedef struct tagDBTP_REVERTOP_UNDO_STRU DBTP_REVERTOP_UNDO_STRU;

/* CP Environment Structure                                                   */
typedef struct tagDBCP_ENV_STRU {
    VOS_UINT32 ulOpenCount;
    VOS_UINT8 ucDeleted;
    VOS_UINT8 aucReserve[1]; /* 1 represent 1 reserve bytes for alignment */
    VOS_UINT16 usWaitUpgrade;
    VOS_UINT16 usReaders;
    VOS_UINT16 usWriters;
    VOS_UINT16 usWaitReaders;
    VOS_UINT16 usWaitWriters;
    DB_MTXLOCK_T csMutex;
    VOS_SEMA_T stCtrlSem;
    VOS_UINT32 ulDBState; /* Indicates if Recovery completed */
    DBCP_CONTEXT_STRU *pstHead;
    DBCP_CONTEXT_STRU *pstTail;
    DBCP_LOGCTRL_STRU *pstLogBuffer;
    DBCP_LOGCTRL_STRU *pstRsmLogBuffer;   /* RSM Log buffer handle */
    DBTP_REVERTOP_UNDO_STRU *pstRSMLogSp; /* RSM Log buffer pointer for Special Revert RDB Flow */
    DB_OP_WRITE_ITEM_STRU stOpStatList;
} DBCP_ENV_STRU;

/* Database Management Structure                                              */
struct tagDB_DBMS_STRU {
    VOS_UINT8 ucFormat;                       /* File format                            */
    VOS_UINT8 ucDirtyFlag;                    /* DB manager is dirty or not (YES/NO)    */
    VOS_UINT8 ucCkpt;                         /* Checkpoint type (NONE/INCR/COMPLETE)   */
    VOS_UINT8 ucCkptStart;                    /* Checkpoint happen atleast once (YES/NO) */
    VOS_UINT8 aDBName[DB_NAME_LEN];           /* Database Name                          */
    VOS_UINT8 aucCheckPtDesPath[DB_PATH_LEN]; /* DB checkpoint file with path */
    VOS_UINT32 ulVersion;                     /* Version of the database                */
    VOS_UINT32 ulMaxDesLen;                   /* DB maximum descriptor length           */
    VOS_UINT32 ulActDesLen;                   /* DB actual descriptor length            */
    VOS_UINT32 ulMaxBlkSize;                  /* Used only for Buffer Read/write        */
    VOS_UINT32 ulThresholdSize;               /* Used by restore for D/I block merge    */
    VOS_UINT16 usNumofTab;                    /* Number of tables                       */
    VOS_UINT16 usTblMapSize;                  /* Max number of entries in Table Map     */
    VOS_UINT16 usFreeTblID;                   /* Free Table ID of the Table Map Manager */
    VOS_UINT8 ucIsFOConsistent;               /* Flag to know DB FO's are consistent    */
    VOS_UINT8 ucDBStorage;                    /* Indicates TableDB goes in RAM or RSM   */
    DB_MEMBLK_STRU stTblMap;                  /* Table map pointer, size & File offset  */
    VOS_UINT8 ucIsCompress;                   /* Flag to know if file compressed or not */
    VOS_UINT8 ucIsBkpProgress;                /* Flag to know backup in progress or not */
    VOS_UINT16 usCheckSum;                    /* File header checksum                   */
    /* Any new members should be added below this, also change DBHEAD_LENG_BEFCRCCHECK accordingly */
    DB_TBLDATABLK_NOTIFY_FUNC pfnNotifyHook; /* New Data block notify callback */
    DB_REGCDBBLKOPHOOK_STRU *pstCDBBlkHook;  /* CDB Bit block operatoin hook   */
    VOS_UINT8 *pucDbDesc;                    /* DB private description information     */
    VOS_UINT8 ucSlotId;                      /* Slot ID in reserve memory */
    VOS_UINT8 ucIsTPC;
    VOS_UINT8 ucIsOverflowFO; /* Flag for File Offset Overflow Detect   */
    VOS_UINT8 aucReserve1[1]; /* 1 represent 1 reserve bytes for alignment */
    VOS_VOID *pvDBModTag;     /* DB modifaction TAG. */
    VOS_UINT8 *pucVarSegMap;  /* List for Rel Optional Mgr Flag */
    DB_CHECKSUM_CFG_STRU *pstChksum;
    /* These two must be the last members                               */
    VOS_UINT8 *pucDbLastErrInfo; /* Buffer to Store Last Error Information */
    DBCP_ENV_STRU stCpEnv;       /* CP env to manage operating contexts    */
};
#pragma pack()

/* Data block Address array structure                                         */
typedef struct tagDBFI_DATABLKADDR_STRU {
    DB_MEMBLK_STRU stBlock; /* Info about the Data block                   */
    T_SIZE nRecCnt;         /* Number of records in the block              */
    T_OFFSET offRecBlk;     /* Offset to Record data of current block      */
} DBFI_DATABLKADDR_STRU;

/* Structure format in which the variable field length is interpretated.      */
typedef struct tagDB_VAR_DATATYPE_STRU {
    VOS_UINT32 ulPageID;    /* ID of the page containing the data */
    VOS_UINT32 ulRowDirIdx; /* Index of the Row directory within the page. */
} DB_VARIABLE_DATATYPE;

/* Structure for Table Map Manager                                            */
typedef struct tagDB_TABLEMAP_STRU {
    T_RELATION rTableID;       /* Table ID                                 */
    VOS_UINT8 aucReserve[6];   /* 6 represent 6 reserve bytes for alignment */
    DB_MEMBLK_STRU stTblSpace; /* Tablespace memory block information      */
} DB_TABLEMAP_STRU;

/* Structure defining persistent members of the page manager */
typedef struct tagDBMM_PAGEMGR_PERS_STRU {
    VOS_UINT32 ulMaxPgCnt;      /* PgAddr List Size */
    VOS_UINT32 ulCurPgCnt;      /* Total number of pages allocated */
    VOS_UINT32 ulPgSize;        /* Page Size including page header size. */
    VOS_UINT8 ucNumVarField;    /* Number of Var Fields in Relation */
    VOS_UINT8 aucRsvd[3];       /* 3 represent 3 reserve bytes for alignment */
    VOS_UINT32 aulPgOffsets[0]; /* Page Offset Array */
} DBMM_PAGEMGR_PERS_STRU;

/* Page header */
typedef struct tagDBMM_PAGE_HEADER {
    VOS_UINT32 ulPageID;
    VOS_UINT8 ucPageType;
    VOS_UINT8 aucReserved[3];     /* 3 represent 3 reserve bytes for alignment */
    VOS_UINT16 usNextRowDirIdx;   /*  ID of next new row to be allocated.     */
    VOS_UINT16 usFreeRowHead;     /*  ID of first free Row in page with data block associated with it. */
    VOS_UINT32 ulTotalFree;       /*  Free space available with in the page   */
    VOS_UINT32 ulFreeSpace;       /*  Indicates the continuous chunk of free memory    */
    VOS_UINT32 ulFreeSpaceOffset; /*  Offset of the free continuous data block */
    VOS_UINT8 ausRsvd[8];         /* 8 represent 8 reserve bytes for future use */
} DBMM_PAGE_HEADER;

/* Row directory structure */
typedef struct tagDBMM_ROWDIR_STRU {
    VOS_UINT32 ulOffset;        /* Offset to Var Data block      */
    VOS_UINT32 ulLen;           /* Length of Var Data block      */
    VOS_UINT16 usNextRowDirIdx; /* Next Free Row in Current Page */
    VOS_UINT8 ucState;          /* State of Row Dir              */
    VOS_UINT8 aucReserved[1];
} DBMM_ROWDIR_STRU;

typedef enum tagROW_DIR_STATE {
    /* Indicates the row directory is free. Indicate that the Row allocate from free list within a page */
    ROW_DIR_FREE = 0,
    ROW_DIR_USED, /* Indicates the row directory is used. */
    ROW_DIR_NEW,  /* Indicator that a new Row allocate from continuous chunk of memory within a Var Data Page */
    ROW_DIR_BUTT
} ROW_DIR_STATE;

void SimpRelInitCheckSumCfg(DB_CHECKSUM_CFG_STRU *checkSumCfg);
void SimpRelCheckSumCalcAdd(DB_CHECKSUM_CFG_STRU *pstChksumCfg, uint16_t *checkSum, uint8_t *buf, uint32_t bufSize);

#ifdef __cplusplus
}
#endif

#endif /* GME_SIMP_REL_CHECK_V1_DEFINE_H */
