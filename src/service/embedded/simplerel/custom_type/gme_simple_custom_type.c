/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Embedded simple relation create user definded data type
 * Author:
 * Create:
 */
#include "srv_emb_sim_com.h"
#include "dm_custom_type.h"

Status GmeSimpleRelInitDataTypeMgr(void)
{
    Status ret = DmCustomeTypeMgrInit();
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to init custom type manager.");
        return ret;
    }

    return GMERR_OK;
}

void GmeSimpleRelUnInitDataTypeMgr(void)
{
    return DmCustomeTypeMgrUnInit();
}

Status GmeSimpleRelCreateDataType(uint8_t dataTypeId, DBTC_OPERATION_FUNC *pstCmpFuncArr)
{
    Status ret = DmCheckCustomeTypeForCreate(dataTypeId);
    if (ret != GMERR_OK) {
        return ret;
    }

    DmCustomTypeT customDataTypeDef = {.dataTypeId = dataTypeId, .customCmpFunc = pstCmpFuncArr};
    ret = DmCreateCustomerType(&customDataTypeDef);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}
