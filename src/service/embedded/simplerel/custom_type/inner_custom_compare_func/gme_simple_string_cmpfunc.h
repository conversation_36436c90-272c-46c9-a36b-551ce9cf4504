/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2024. All rights reserved.
 * File Name: gme_simple_macaddr_cmpfunc.h
 * Description: Type mac address compare func header file
 * Author:
 * Create:
 */
#ifndef GME_SIMPREL_STRING_H
#define GME_SIMPREL_STRING_H

#include "gme_simple_inner_custom_common.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif /* __cplusplus */
#endif /* __cplusplus */

VOS_BOOL TPC_StrIsLike(const VOS_UINT8 *pucField, const VOS_UINT8 *pucCond, VOS_UINT16 usMaxLen);

/* String Equal */
DB_ERR_CODE TPC_STRING_Equal(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

/* String Not Equal */
DB_ERR_CODE TPC_STRING_NotEqual(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

/* String Small */
DB_ERR_CODE TPC_STRING_Small(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

/* String Small and Equal */
DB_ERR_CODE TPC_STRING_SmallEqual(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

/* String Large */
DB_ERR_CODE TPC_STRING_Large(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

/* String Large and Equal */
DB_ERR_CODE TPC_STRING_LargeEqual(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

/* String Post Fix */
DB_ERR_CODE TPC_STRING_Postfix21(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

/* String Pre Fix */
DB_ERR_CODE TPC_STRING_Prefix(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

/* String having Prefix */
DB_ERR_CODE TPC_STRING_HavePrefix(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

/* String with Prefix21 */
DB_ERR_CODE TPC_STRING_Prefix21(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

/* Strung having Postfix */
DB_ERR_CODE TPC_STRING_HavePostfix(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

/* String with no Prefix */
DB_ERR_CODE TPC_STRING_NoPrefix(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

/* String Like */
DB_ERR_CODE TPC_STRING_Like(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

/* String Not Like */
DB_ERR_CODE TPC_STRING_NotLike(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

#ifdef __cplusplus
}
#endif

#endif /* SRV_EMB_REL_COMM_H */
