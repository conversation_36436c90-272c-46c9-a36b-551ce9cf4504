/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: gme_simple_inner_custom_common.h
 * Description: Embedded simple relation inner custom common head file
 * Author:
 * Create:
 */
#ifndef GME_SIMPREL_INNERCUSTOMTYPE_H
#define GME_SIMPREL_INNERCUSTOMTYPE_H

#include "adpt_define.h"
#include "adpt_types.h"
#include "db_last_error.h"
#include "tpc_types.h"

/* Macros for Magic numbers */
#define DB_NUM_MINUS_ONE (-1)

#define DBKNL_HALF_BYTE 4
#define DBKNL_BYTE 8
#define DBKNL_TWO_BYTE 16
#define DBKNL_THREE_BYTE 24
#define DBKNL_FOUR_BYTE 32
#define DBKNL_FIVE_BYTE 40
#define DBKNL_SIX_BYTE 48
#define DBKNL_SEVEN_BYTE 56

#endif /* _GME_SIMPREL_INNERCUSTOMTYPE_H */
