/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: gme_simple_rel_dml.h
 * Description: the header file of dml implementation on embedded service.
 * Author:
 * Create:
 */
#ifndef GME_SIMP_REL_DML_H
#define GME_SIMP_REL_DML_H

#include "srv_emb_sim_com.h"

#ifdef __cplusplus
extern "C" {
#endif

Status SimpleRelationInsert(EmbSimpleRunCtxT *embCtx);
#define TTREE_UPDATE_STEP_UPDATE 1
#define TTREE_UPDATE_STEP_INSERT 2
typedef struct TTreeIdxUpdCtx {
    uint32_t idxNum;          // V1的TTree索引数量
    uint32_t ttreePos;        // 索引快速更新的树节点
    uint32_t idxMatchId;      // 可快速更新的索引ID
    HeapTupleBufT newKey;     // V1 update newKey
    HeapTupleBufT oldKey;     // V1 update oldK<PERSON>
    void *phyAddr;            // 索引记录的物理addr
    uint8_t ttreeUpdateStep;  // 更新分两段操作，先更新再插入
} TTreeIdxUpdCtxT;

#ifdef __cplusplus
}
#endif

#endif /* GME_SIMP_REL_DML_H */
