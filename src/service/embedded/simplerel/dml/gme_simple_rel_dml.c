/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Embedded simple relation dml api
 * Author:
 * Create:
 */
#include "gme_simple_rel_dml.h"
#include "gme_simple_rel_dql.h"
#include "gme_simple_rel_persistence.h"
#include "se_trx.h"
#include "se_define.h"
#include "se_trx_inner.h"
#include "se_common.h"
#include "se_heap_base.h"
#include "se_heap_utils.h"

inline static bool SimprelationIsBytesType(DmPropertySchemaT *prope)
{
    return prope->customTypeId == DBT_BYTES || prope->customTypeId == DBT_VBYTES;
}

inline static Status SimprelationCheckBytesHeadLen(const uint8_t *pBuf, const DmPropertySchemaT *prope, uint32_t offset)
{
    const uint16_t bytesHeadLen = *(const uint16_t *)(pBuf + offset);
    if (SECUREC_UNLIKELY(bytesHeadLen > prope->propeDefLen)) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDDATALEN,
            "Input length for bytes data is not correct, input len %" PRIu16 ", fldDef len %" PRIu16 ".", bytesHeadLen,
            prope->propeDefLen);
        return VOS_ERRNO_DB_INVALIDDATALEN;
    }
    return GMERR_OK;
}

static Status SimprelationCheckBytesValueIsValid(DmSchemaT *schema, uint8_t *pInsertbuf)
{
    Status ret = GMERR_OK;
    DmPropertySchemaT *properties = schema->properties;
    for (uint32_t i = 0; i < schema->propeNum; i++) {
        if (SECUREC_LIKELY(!SimprelationIsBytesType(&properties[i]))) {
            continue;
        }

        if ((ret = SimprelationCheckBytesHeadLen(pInsertbuf, &properties[i], properties[i].offset)) != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status SimprelationCheckRecLen(DmSchemaT *schema, GmcSeriT *seri)
{
    uint32_t fieldLen = schema->maxRecLen - EXTERN_DATA_BUF_LEN;
    uint32_t inputLen = ((DB_DSBUF_STRU *)seri->userData)->StdBuf.ulActLen;
    if (fieldLen != inputLen) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDRECLEN,
            "input buff len is worthless. schema len %" PRIu32 ", input buff len %" PRIu32 ".", fieldLen, inputLen);
        return VOS_ERRNO_DB_INVALIDRECLEN;
    }

    return SimprelationCheckBytesValueIsValid(schema, ((DB_DSBUF_STRU *)seri->userData)->StdBuf.pucData);
}

static void SimpleRelationTupleBufFree(DbMemCtxT *memCtx, HeapTupleBufT *heapTupleBuf)
{
    if (heapTupleBuf->buf != NULL) {
        DbDynMemCtxFree(memCtx, heapTupleBuf->buf);
        heapTupleBuf->buf = NULL;
    }
    heapTupleBuf->bufSize = 0;
}

Status SimpleRelationTupleBufPrepare(DmSchemaT *schema, DbMemCtxT *memCtx, HeapTupleBufT *heapTupleBuf)
{
    uint32_t bufSize = schema->maxRecLen;
    if (heapTupleBuf->buf == NULL || heapTupleBuf->bufSize < bufSize) {
        SimpleRelationTupleBufFree(memCtx, heapTupleBuf);
        heapTupleBuf->buf = DbDynMemCtxAlloc(memCtx, bufSize);
        if (heapTupleBuf->buf == NULL) {
            DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "unable to alloc heap tuple buff.");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }
    }
    heapTupleBuf->bufSize = bufSize;
    return GMERR_OK;
}

static Status SimpleRelationDoSerial(
    EmbSimpleRunCtxT *embCtx, SimRelLabelCursorT *labelCursor, GmcSeriT *seri, HeapTupleBufT *heapTupleBuf)
{
    // 根据结构化读写, 序列化到连续 buf (实际上, 是全部fixed字段, 简单表, 直接为v1原始数据 fixedPropeLen),
    // 普通对象额外增加5个字节存储rowid、更删标记
    DmSchemaT *schema = labelCursor->vertexLabel->metaVertexLabel->schema;
    Status ret = SimprelationCheckRecLen(schema, seri);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SimpleRelationTupleBufPrepare(schema, labelCursor->memCtx, heapTupleBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    // AdptV1Serial
    if (!labelCursor->isBigObj) {
        return (*seri->seriFunc)(seri, heapTupleBuf->buf, (void *)&ret);
    }
    return (*seri->seriFunc)(seri, heapTupleBuf->buf, NULL);
}

Status SimpleRelInitQueryCtxByIndex(
    EmbSimpleRunCtxT *embCtx, uint32_t idxId, EmbSimpleQueryCtxT *queryCtx, uint8_t *buf, SimpHpTupleInfoT *filterData)
{
    DmVertexLabelT *vertexLabel = embCtx->labelCursor->vertexLabel;
    queryCtx->scanUserData.matchedCnt = 0;
    queryCtx->scanUserData.scanFinish = false;
    queryCtx->idxCondNum = vertexLabel->metaVertexLabel->secIndexes[idxId].propeNum;
    queryCtx->scanUserData.limitCnt = DB_CHECK_REC_EXIST;
    queryCtx->needCopyResult = false;
    queryCtx->needAddIdxScanCnt = false;
    queryCtx->lastInvalidAddr.pageIdAndSlotId = DB_INVALID_UINT32;
    queryCtx->lastFetchAddr.pageIdAndSlotId = DB_INVALID_UINT32;
    DmPropertySchemaT *properties = vertexLabel->metaVertexLabel->schema->properties;
    uint8_t *realBuf = RealBuf2Info(!embCtx->labelCursor->isBigObj, buf);
    for (uint32_t i = 0; i < queryCtx->idxCondNum; i++) {
        uint32_t fildId = vertexLabel->metaVertexLabel->secIndexes[idxId].propIds[i];
        queryCtx->idxConds[i].enOp = GME_CMP_EQUAL;
        queryCtx->idxConds[i].propId = fildId;
        queryCtx->idxConds[i].propeMaxLen = properties[fildId].propeDefLen;
        // 存储数据额外偏移
        queryCtx->idxConds[i].fldOffset = properties[fildId].offset;
        // 存储自定义数据类型 ID
        queryCtx->idxConds[i].v1Type = properties[fildId].customTypeId;
        queryCtx->idxConds[i].v5Type = properties[fildId].dataType;
        queryCtx->idxConds[i].value = realBuf + queryCtx->idxConds[i].fldOffset;
    }
    queryCtx->filterData = filterData;
    queryCtx->idxStoreDataFunc = IdxScanGetMatchingCountByFilter;
    queryCtx->isBigObj = embCtx->labelCursor->isBigObj;
    return GMERR_OK;
}

Status SimpleRelCheckUniqueKeyValueIsChanged(
    EmbSimpleRunCtxT *embCtx, uint32_t idxId, uint8_t *buf, SimpHpTupleInfoT *filterData, bool *isChanged)
{
    if (filterData == NULL) {
        *isChanged = true;
        return GMERR_OK;
    }
    DmVertexLabelT *vertexLabel = embCtx->labelCursor->vertexLabel;
    DmPropertySchemaT *properties = vertexLabel->metaVertexLabel->schema->properties;
    // filterData 在IdxScanMakeDmlChangedList\SimpleRelUpdateOrDeleteProc 插入链表时已经经过偏移，不用额外偏移
    uint32_t extraOffset = !embCtx->labelCursor->isBigObj ? EXTERN_DATA_BUF_LEN : 0;
    *isChanged = false;
    for (uint32_t i = 0; i < vertexLabel->metaVertexLabel->secIndexes[idxId].propeNum; i++) {
        uint32_t fildId = vertexLabel->metaVertexLabel->secIndexes[idxId].propIds[i];
        uint32_t offset = properties[fildId].offset;
        uint8_t dir;
        VOS_BOOL bMatch = false;
        Status ret = V1ValueMatch(buf + offset + extraOffset, filterData->tupleBuf.buf + offset,
            properties[fildId].propeDefLen, GME_CMP_EQUAL, properties[fildId].customTypeId, &bMatch, &dir);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_UNFOLD(ret, "Unable to check unique key value is changed, idx name(%s).",
                vertexLabel->metaVertexLabel->secIndexes[idxId].indexName);
            return ret;
        }
        if (!bMatch) {
            *isChanged = true;
            return GMERR_OK;
        }
    }
    return GMERR_OK;
}

static Status SimpleRelCheckIdxConflict(EmbSimpleRunCtxT *embCtx, SimpHpTupleInfoT *filterData)
{
    Status status = GMERR_OK;
    EmbSimpleQueryCtxT *queryCtx = &embCtx->labelCursor->queryCtxForCheckUnique;
    EmbSimpleCondItemT idxCondArray[DB_RESTORE_INDEX_MAX_FIELD_NUM] = {0};
    queryCtx->idxConds = idxCondArray;
    for (uint32_t i = 0; i < embCtx->labelCursor->idxNum; i++) {
        IndexCtxT *secIdxCtx = embCtx->labelCursor->idxCtx[i];
        // 唯一索引查询暂不缓存,插入时再遍历
        secIdxCtx->destNode = NULL_NODEID;
        if (secIdxCtx->idxMetaCfg.idxConstraint == UNIQUE) {
            bool isChanged = false;
            status = SimpleRelCheckUniqueKeyValueIsChanged(
                embCtx, i, embCtx->labelCursor->heapTupleBuf.buf, filterData, &isChanged);
            if (status != GMERR_OK) {
                return status;
            }
            if (!isChanged) {
                continue;
            }
            status =
                SimpleRelInitQueryCtxByIndex(embCtx, i, queryCtx, embCtx->labelCursor->heapTupleBuf.buf, filterData);
            if (status != GMERR_OK) {
                return status;
            }
            bool found;
            status = IdxScan(secIdxCtx, queryCtx, &embCtx->labelCursor->addr, &found);
            if (status == GMERR_OK && queryCtx->scanUserData.matchedCnt != 0) {
                DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_KEYDUPLICATE, "unique key conflict, relName(%s), idxName(%s).",
                    embCtx->vLabelCtrl->relationName, secIdxCtx->idxOpenCfg.vlIndexLabel->indexName);
                return VOS_ERRNO_DB_KEYDUPLICATE;
            }
            if (status != GMERR_OK) {
                return status;
            }
        }
    }
    return GMERR_OK;
}

ALWAYS_INLINE Status IsTableFull(SimRelLabelCursorT *labelCursor, VlabelCtrlT *vLabelCtrl, EmbSimpleRunCtxT *embCtx)
{
    uint64_t realItemNum = labelCursor->hpHandle->perfStat->realItemNum +
                           labelCursor->hpHandle->heapTrxCtx->opStat.insertNum -
                           labelCursor->hpHandle->heapTrxCtx->opStat.deleteNum;
    if (realItemNum >= labelCursor->vertexLabel->commonInfo->heapInfo.maxVertexNum) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_DATASEGFULL,
            "Exceed the max item number unsucc, rel name : %s, max rec num : %" PRIu64 ".", vLabelCtrl->relationName,
            labelCursor->vertexLabel->commonInfo->heapInfo.maxVertexNum);
        return VOS_ERRNO_DB_DATASEGFULL;
    }
    embCtx->isForceCommit = false;
    return GMERR_OK;
}

static Status SimpleRelationIndexInsert(EmbSimpleRunCtxT *embCtx)
{
    // 参考 DwInsertSecondaryIndexes, 但是由于需要 支持字段自定义比较函数, 后续全部使用 vertex buf + 偏移逐个比较
    // 参考V1用插入数据做查重,不新增内存
    IndexKeyT hashKey = {
        .keyData = embCtx->labelCursor->heapTupleBuf.buf, .keyLen = embCtx->labelCursor->heapTupleBuf.bufSize};
    TuplePointer32T addr32 = HeapCompressTupleAddr32(embCtx->labelCursor->addr);
    uint8_t *insertBuf = embCtx->labelCursor->hpHandle->compV1Info.realInsertPtr;
    if (!embCtx->labelCursor->isBigObj) {
        *(uint32_t *)insertBuf = addr32.pageIdAndSlotId;
        SetHeadModify(insertBuf, false);
        insertBuf = insertBuf + EXTERN_DATA_BUF_LEN;
    }
    for (uint32_t i = 0; i < embCtx->labelCursor->idxNum; i++) {
        IndexCtxT *secIdxCtx = embCtx->labelCursor->idxCtx[i];
        if (SECUREC_UNLIKELY(embCtx->isRestoreDb)) {
            secIdxCtx->destNode = NULL_NODEID;
        }
        bool checkAged = true;
        secIdxCtx->ttreeDataInfo.buf = insertBuf;
        secIdxCtx->ttreeDataInfo.hasModify = false;
        secIdxCtx->ttreeDataInfo.addr32 = addr32;
        secIdxCtx->idxOpenCfg.userData = &checkAged;
        DbRWLatchW(&(((IdxBaseT *)secIdxCtx->idxRunCtx)->idxLatch));
        Status ret = IdxInsert(secIdxCtx, hashKey, embCtx->labelCursor->addr);
        DbRWUnlatchW(&(((IdxBaseT *)secIdxCtx->idxRunCtx)->idxLatch));
        secIdxCtx->idxOpenCfg.userData = NULL;
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

void SimpleRelationCalcChecksumAdd(EmbSimpleRunCtxT *embCtx, bool isBigObj, const HeapTupleBufT *heapTupleBuf)
{
    if (embCtx->isTpcOp || embCtx->nspCtrl->pstChksum == NULL) {
        return;
    }
    uint32_t bufSize = embCtx->vLabelCtrl->originDefInfo->usRecLen;
    uint8_t *buf = heapTupleBuf->buf;
    if (!isBigObj) {
        buf = buf + EXTERN_DATA_BUF_LEN;
    }
    V1_DBCKSM_ADD(embCtx->nspCtrl->pstChksum, &embCtx->vLabelCtrl->checkSum, buf, bufSize);
}

void SimpleRelationCalcChecksumDel(EmbSimpleRunCtxT *embCtx, bool isBigObj, const HeapTupleBufT *heapTupleBuf)
{
    if (embCtx->isTpcOp || embCtx->nspCtrl->pstChksum == NULL) {
        return;
    }
    uint32_t bufSize = embCtx->vLabelCtrl->originDefInfo->usRecLen;
    uint8_t *buf = heapTupleBuf->buf;
    V1_DBCKSM_DEL(embCtx->nspCtrl->pstChksum, &embCtx->vLabelCtrl->checkSum, buf, bufSize);
}

Status SimpleRelationInsert(EmbSimpleRunCtxT *embCtx)
{
    if (embCtx->isTpcOp && !embCtx->nspCtrl->isTpcDb) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALID_DATABASE, "DB is not tpc db.");
        return VOS_ERRNO_DB_INVALID_DATABASE;
    }
    SimRelLabelCursorT *labelCursor = embCtx->labelCursor;
    Status ret = IsTableFull(labelCursor, embCtx->vLabelCtrl, embCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 参考 QryExecuteInsertTupleAndIndexes 或者 DwExecInsertEntry
    ret = SimpleRelationDoSerial(embCtx, embCtx->labelCursor, embCtx->seri, &embCtx->labelCursor->heapTupleBuf);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (SECUREC_UNLIKELY(!embCtx->isRestoreDb)) {
        if ((ret = SimpleRelCheckIdxConflict(embCtx, NULL)) != GMERR_OK) {
            return ret;
        }
    }

    if ((ret = HeapLabelResetOpType(embCtx->labelCursor->hpHandle, HEAP_OPTYPE_INSERT, false)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "unable to reset heap label op type.");
        return ret;
    }

    ret = HeapLabelInsertHpTupleBuffer(labelCursor->hpHandle, &labelCursor->heapTupleBuf, &labelCursor->addr);
    if (ret != GMERR_OK) {
        if (ret == GMERR_OUT_OF_MEMORY) {
            // 0号RDB场景会强制提交一次RDB
            g_adptV1Instance.isSeOutMemory = true;
        }
        ret = (ret == GMERR_RECORD_COUNT_LIMIT_EXCEEDED) ? VOS_ERRNO_DB_DATASEGFULL : ret;
        DB_LOG_ERROR(ret, "unable to insert heap tuple buffer.");
        return ret;
    }
    ret = SimpleRelationIndexInsert(embCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    SimpleRelationCalcChecksumAdd(embCtx, labelCursor->isBigObj, &labelCursor->heapTupleBuf);
    embCtx->vLabelCtrl->hasInsert = true;
    return SimpleRelationRecordDiff(
        embCtx, embCtx->labelCursor->vertexLabel->metaCommon.metaId, embCtx->labelCursor->addr, DIFF_OPERATION_CREAT);
}

Status GmeSimpleRelationInsert(GmeConnT *conn, uint32_t nspId, uint32_t relNo, GmcSeriT *seri)
{
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    // bind api args 2 embCtx
    embCtx->opCode = SIMPLEREL_OP_TYPE_INSERT;
    embCtx->relNo = relNo;
    embCtx->seri = seri;
    embCtx->nspId = nspId;
    return SimpleRelationExecute(embCtx, SimpleRelationInsert);
}

/**
复杂查询：
条件值相等，按顺序正常放入结果集，返回false；
条件值非极值，数据不放入结果集直接丢弃，返回true；
条件值为新极值，返回false，清除旧数据
**/
bool MakeComplexResultForDML(uint8_t *dataBuf, EmbSimpleQueryCtxT *queryCtx)
{
    // 非复杂查询，直接返回
    if (queryCtx->complexOpStru.complexOpNum == 0 || queryCtx->hpTupleList.usedCount == 0) {
        return false;
    }

    bool needChange = false;
    bool needFilter = false;
    SimpHpTupleInfoT *hpBuf = (SimpHpTupleInfoT *)DbGaListGet(&queryCtx->hpTupleList, 0);
    GetComplexResultCompare(queryCtx, hpBuf->tupleBuf.buf, dataBuf, &needChange, &needFilter);

    if (needChange) {
        queryCtx->hpTupleList.usedCount = 0;
        return false;
    }
    if (needFilter) {
        return true;
    }
    return false;
}

static Status IdxScanMakeDmlChangedList(
    ScanDataCtxT *scanDataCtx, EmbSimpleQueryCtxT *pstSearchPara, SimpRelScanUserDataT *userData)
{
    if (MakeComplexResultForDML(scanDataCtx->heapTupleBuf.buf, pstSearchPara)) {
        return GMERR_OK;
    }
    SimpHpTupleInfoT *hpInfpPtr = NULL;
    Status ret = DbGaListTryUpdate(&pstSearchPara->hpTupleList, sizeof(SimpHpTupleInfoT), (void **)(&hpInfpPtr));
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Simple rel append heap info for idx update or delete worthless.");
        return ret;
    }
    hpInfpPtr->addr32 = scanDataCtx->dataInfo.addr32;
    hpInfpPtr->tupleBuf = scanDataCtx->heapTupleBuf;
    if (SECUREC_UNLIKELY(!pstSearchPara->idxCtx->isNormalIdx)) {
        // 大对象实际数据需要拷贝缓存，使用apiMemCtx，统一在SimpleRelationPrepareClear中ResetMemCtx释放
        DbMemCtxT *apiMemCtx = ((SimRelLabelCursorT *)userData->labelCursor)->apiMemCtx;
        hpInfpPtr->tupleBuf.buf = DbDynMemCtxAlloc(apiMemCtx, scanDataCtx->heapTupleBuf.bufSize);
        if (SECUREC_UNLIKELY(hpInfpPtr->tupleBuf.buf == NULL)) {
            DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "Unable to alloc mem to store dml changed list");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }
        DbFastMemcpy(hpInfpPtr->tupleBuf.buf, scanDataCtx->heapTupleBuf.bufSize, scanDataCtx->heapTupleBuf.buf,
            scanDataCtx->heapTupleBuf.bufSize);
    }
    hpInfpPtr->buf = scanDataCtx->dataInfo.buf;  // 索引上使用数据实际物理addr, 大对象为link src row addr
    hpInfpPtr->ttreePos = userData->ttreePos;
    return GMERR_OK;
}

Status SimpleRelInitQueryCtxByDML(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, IdxMatchStruT *idxMatchStru,
    SimpRelScanCtxT *scanCtx, EmbSimpleQueryCtxT *queryCtx)
{
    Status ret = GMERR_OK;
    queryCtx->needCopyResult = false;
    queryCtx->isFetchTopo = false;
    queryCtx->needAddIdxScanCnt = true;

    if (scanCtx->cond->usCondNum != 0) {
        if ((ret = SimpleRelInitQueryConds(memCtx, vertexLabel, scanCtx, idxMatchStru, queryCtx)) != GMERR_OK) {
            return ret;
        }
    }

    if (scanCtx->fldFilter != NULL && scanCtx->fldFilter->ucFieldNum != DB_FIELD_ALL) {
        ret = SimpleRelInitQueryFldFilter(memCtx, scanCtx, queryCtx);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    queryCtx->affectRows = 0;
    return GMERR_OK;
}

static Status SimpRelCheckAndInitQueryCtx4Dml(
    EmbSimpleRunCtxT *embCtx, SimpRelScanCtxT *scanCtx, EmbSimpleQueryCtxT *queryCtx, IdxMatchStruT *idxMatchStru)
{
    if (embCtx->isTpcOp && !embCtx->nspCtrl->isTpcDb) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALID_DATABASE, "DB is not tpc db.");
        return VOS_ERRNO_DB_INVALID_DATABASE;
    }
    Status ret = SimpleRelCheckScanCtx(scanCtx, embCtx->vLabelCtrl, true, embCtx->nspCtrl->isTpcDb);
    if (ret != GMERR_OK) {
        return ret;
    }

    SimRelLabelCursorT *labelCursor = embCtx->labelCursor;
    SimpleRelSelectIndex(embCtx->vLabelCtrl, scanCtx->cond, idxMatchStru, false, embCtx->nspCtrl->isTpcDb);
    SimpleRelResetQueryCtx(labelCursor->memCtx, queryCtx, scanCtx);
    ret = SimpleRelInitQueryCtxByDML(labelCursor->memCtx, labelCursor->vertexLabel, idxMatchStru, scanCtx, queryCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    queryCtx->isBigObj = labelCursor->isBigObj;
    queryCtx->scanUserData.labelCursor = embCtx->labelCursor;
    queryCtx->idxStoreDataFunc = IdxScanMakeDmlChangedList;
    return ret;
}

static Status SimpRelGetChangedDataByIndex(EmbSimpleRunCtxT *embCtx, EmbSimpleQueryCtxT *queryCtx)
{
    SimRelLabelCursorT *labelCursor = embCtx->labelCursor;
    DmVlIndexLabelT *idxLabel = &labelCursor->vertexLabel->metaVertexLabel->secIndexes[labelCursor->bestIndexId];
    IndexCtxT *idxCtx = labelCursor->idxCtx[labelCursor->bestIndexId];
    Status ret = GMERR_OK;
    if (idxCtx == NULL) {
        ret = SimpleRelOpenAndInitOneIdx(embCtx->seRunCtx, labelCursor, idxLabel, &idxCtx);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    // 唯一索引查询暂不缓存,插入时再遍历
    idxCtx->destNode = NULL_NODEID;
    bool found;
    ret = IdxScan(idxCtx, queryCtx, &labelCursor->addr, &found);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to get dml changed data by index.");
        return ret;
    }
    idxCtx->destNode = NULL_NODEID;
    return GMERR_OK;
}

static Status SimpRelInitDmlHeapScanCursor(EmbSimpleRunCtxT *embCtx, HeapScanCursorHdlT *heapCursor)
{
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false,
        .beginAddr = {.tupleAddr = BEFORE_FIRST_TUPLE_ADDR, .blockId = BEFORE_FIRST_TUPLE_BLOCK_ID},
        .maxFetchNum = 1,
        .heapDowngradeInfo = HeapLabelDowngradeInfoInit(),
        .heapLabelLatchInfoForClt = {0}};
    Status ret = HeapLabelBeginScanHpTupleBuffer(embCtx->labelCursor->hpHandle, &beginScanCfg, heapCursor);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unable to init heap scan cursor in dml operation.");
        return ret;
    }
    return GMERR_OK;
}

ALWAYS_INLINE static Status SimpleRelMakeOneDmlHeapTuple(SimpRelScanUserDataT *inputData, HpTupleAddr tupleAddr,
    HeapTupleBufT tupleBuf, HpScanSubsActT *subsequentAction, SimpHpTupleInfoT *hpInfpPtr)
{
    hpInfpPtr->addr32 = HeapCompressTupleAddr32(tupleAddr);
    if (HeapCheckIsNormalRowByBufSize(tupleBuf.bufSize)) {
        hpInfpPtr->tupleBuf = tupleBuf;
        hpInfpPtr->buf = tupleBuf.buf;
    } else {
        // 大对象实际数据需要拷贝缓存，使用apiMemCtx，统一在SimpleRelationPrepareClear中ResetMemCtx释放
        DbMemCtxT *apiMemCtx = ((SimRelLabelCursorT *)(inputData->labelCursor))->apiMemCtx;
        hpInfpPtr->tupleBuf.buf = DbDynMemCtxAlloc(apiMemCtx, tupleBuf.bufSize);
        if (SECUREC_UNLIKELY(hpInfpPtr->tupleBuf.buf == NULL)) {
            subsequentAction->isScanBreak = true;
            DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "Unable alloc mem to store update or delete buf.");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }
        DbFastMemcpy(hpInfpPtr->tupleBuf.buf, tupleBuf.bufSize, tupleBuf.buf, tupleBuf.bufSize);
        hpInfpPtr->tupleBuf.bufSize = tupleBuf.bufSize;
        hpInfpPtr->buf = ((SimRelLabelCursorT *)(inputData->labelCursor))->hpHandle->compV1Info.realInsertPtr;
    }
    hpInfpPtr->ttreePos = NULL_NODEID;
    return GMERR_OK;
}

static StatusInter SimpleRelUpdateOrDeleteProc(const HeapTupleBufAndRowInfoT *heapBuffAndRowInfo, uint32_t rowIndex,
    void *userData, HpScanSubsActT *subsequentAction)
{
    // 默认全部置为false
    subsequentAction->isRollBackScan = false;
    subsequentAction->isMatched = false;  // 始终设置为false,heapCursor不缓存
    subsequentAction->isScanBreak = false;

    SimpRelScanUserDataT *inputData = (SimpRelScanUserDataT *)userData;
    EmbSimpleQueryCtxT *queryCtx = inputData->queryCtx;
    SimRelLabelCursorT *labelCursor = (SimRelLabelCursorT *)inputData->labelCursor;
    HeapTupleBufT tupleBuf = heapBuffAndRowInfo->tupleBuf;
    tupleBuf.buf = RealBuf2Info(!labelCursor->isBigObj, heapBuffAndRowInfo->tupleBuf.buf);
    bool isMatch = false;
    Status ret = GMERR_OK;
    if (inputData->isMatch != true) {
        ret = SimpleRelMatchFilterCond(&tupleBuf, queryCtx->filterConds, queryCtx->filterCondNum, &isMatch);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        isMatch = true;
    }
    inputData->isMatch = false;
    if (!isMatch) {
        // 不满足非索引过滤条件,退出本次扫描继续扫描下一条
        return STATUS_OK_INTER;
    }
    // 数据满足过滤条件则添加到待修改链表中
    if (MakeComplexResultForDML(tupleBuf.buf, queryCtx)) {
        return STATUS_OK_INTER;
    }
    SimpHpTupleInfoT *hpInfpPtr = NULL;
    ret = DbGaListTryUpdate(&queryCtx->hpTupleList, sizeof(SimpHpTupleInfoT), (void **)(&hpInfpPtr));
    if (ret != GMERR_OK) {
        subsequentAction->isScanBreak = true;
        DB_LOG_AND_SET_LASERR(ret, "Simple rel append heap info for update worthless.");
        return ret;
    }

    return SimpleRelMakeOneDmlHeapTuple(inputData, heapBuffAndRowInfo->fetchedRowInfo.curHeapTupleAddr.tupleAddr,
        tupleBuf, subsequentAction, hpInfpPtr);
}

ALWAYS_INLINE static bool SimpleRelMatchRowFilterForUpdOrDel(uint8_t *data, void *userData)
{
    SimpRelScanUserDataT *inputData = (SimpRelScanUserDataT *)userData;
    SimRelLabelCursorT *labelCursor = (SimRelLabelCursorT *)inputData->labelCursor;
    EmbSimpleQueryCtxT *queryCtx = &labelCursor->queryCtxForUpdOrDel;
    uint32_t extOffset = !labelCursor->isBigObj ? EXTERN_DATA_BUF_LEN : 0;
    for (uint32_t i = 0; i < queryCtx->filterCondNum; i++) {
        EmbSimpleCondItemT *cond = &queryCtx->filterConds[i];
        VOS_BOOL match = false;
        VOS_UINT8 pucDir;
        Status ret = V1ValueMatch(data + cond->fldOffset + extOffset, cond->value, cond->propeMaxLen, cond->enOp,
            cond->v1Type, &match, &pucDir);
        if (ret != GMERR_OK) {  // 报错场景不过滤
            inputData->isMatch = false;
            return true;
        }
        if (match == false) {
            inputData->isMatch = false;
            return false;
        }
    }
    inputData->isMatch = true;
    return true;
}

static Status SimpRelGetChangedDataBySequence(EmbSimpleRunCtxT *embCtx, EmbSimpleQueryCtxT *queryCtx)
{
    HeapScanCursorHdlT heapCursor = NULL;
    Status ret = SimpRelInitDmlHeapScanCursor(embCtx, &heapCursor);
    if (ret != GMERR_OK) {
        return ret;
    }

    SimpRelScanUserDataT userData = {.labelCursor = embCtx->labelCursor, .queryCtx = queryCtx};
    HpScanSubsCondDataT scanSubsCondData = {.userData = &userData, .auxInfo = {{0}}};
    scanSubsCondData.userScanRowProc = SimpleRelUpdateOrDeleteProc;
    scanSubsCondData.userRowFilter = SimpleRelMatchRowFilterForUpdOrDel;
    scanSubsCondData.userResetRowFilter = SimpleRelResetMatchRowFilter;
    HpFetchedAuxInfoT auxInfo = {0};
    embCtx->vLabelCtrl->directSearchCnt++;
    ret = HeapFetchNextWithCond(embCtx->labelCursor->hpHandle, heapCursor, &scanSubsCondData, &auxInfo);
    HeapLabelEndScan(embCtx->labelCursor->hpHandle, heapCursor);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unable to fetch next heap with condition.");
        return ret;
    }
    return GMERR_OK;
}

static Status SimprelationCheckRecLenByProjection(EmbSimpleQueryCtxT *queryCtx, DmSchemaT *schema, GmcSeriT *seri)
{
    Status ret = GMERR_OK;
    DmPropertySchemaT *properties = schema->properties;
    uint32_t offset = 0;
    for (uint32_t i = 0; i < queryCtx->projectionNum; i++) {
        uint32_t propId = queryCtx->projectionPropId[i];
        if (SECUREC_UNLIKELY(SimprelationIsBytesType(&properties[propId]))) {
            if ((ret = SimprelationCheckBytesHeadLen(seri->obj, &properties[propId], offset)) != GMERR_OK) {
                return ret;
            }
        }
        offset += properties[propId].size;
    }
    if (offset != seri->bufSize) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDRECLEN,
            "input buff len is worthless. schema len %" PRIu32 ", input buff len %" PRIu32 ".", offset, seri->bufSize);
        return VOS_ERRNO_DB_INVALIDRECLEN;
    }
    return GMERR_OK;
}

static Status SimpRelBuildTupleBuf(
    EmbSimpleRunCtxT *embCtx, EmbSimpleQueryCtxT *queryCtx, const HeapTupleBufT *srcHeapBuf, HeapTupleBufT *destHeapBuf)
{
    Status ret = GMERR_OK;
    SimRelLabelCursorT *labelCursor = embCtx->labelCursor;
    DmSchemaT *schema = labelCursor->vertexLabel->metaVertexLabel->schema;
    DmPropertySchemaT *properties = schema->properties;
    ret = SimpleRelationTupleBufPrepare(schema, labelCursor->memCtx, destHeapBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t extOffset = !embCtx->labelCursor->isBigObj ? EXTERN_DATA_BUF_LEN : 0;
    errno_t err = memcpy_s(destHeapBuf->buf + extOffset, destHeapBuf->bufSize - extOffset, srcHeapBuf->buf,
        destHeapBuf->bufSize - extOffset);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMCPY_FAILURE, "unable to memcpy source heap buffer.");
        return VOS_ERRNO_DB_MEMCPY_FAILURE;
    }

    uint8_t *updBufCursor = embCtx->seri->obj;
    uint32_t propOffset, propSize;
    for (uint32_t i = 0; i < queryCtx->projectionNum; i++) {
        propOffset = properties[queryCtx->projectionPropId[i]].offset;
        propSize = properties[queryCtx->projectionPropId[i]].size;
        err = memcpy_s(destHeapBuf->buf + propOffset + extOffset, propSize, updBufCursor, propSize);
        if (SECUREC_UNLIKELY(err != EOK)) {
            DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMCPY_FAILURE, "unable to memcpy update buffer.");
            return VOS_ERRNO_DB_MEMCPY_FAILURE;
        }
        updBufCursor += propSize;
    }
    return GMERR_OK;
}

static Status SimpRelHeapUpdate(HpRunHdlT heapRunCtx, HeapTupleBufT *newTupleBuf, HpTupleAddr oldTupleAddr)
{
    HeapUpdOutPara out = {0};
    Status ret = HeapLabelUpdateWithHpTupleBuf(heapRunCtx, newTupleBuf, oldTupleAddr, &out);
    if (ret != GMERR_OK) {
        if (ret == GMERR_OUT_OF_MEMORY) {
            // 0号RDB场景会强制提交一次RDB
            g_adptV1Instance.isSeOutMemory = true;
        }
        DB_LOG_ERROR(ret, "unable to update heap with tuple buffer.");
        return ret;
    }
    return GMERR_OK;
}

// 更新拆分成两段操作，ttreeUpdateStep记录状态，第一段处理旧数据undo更新，第二段处理新数据写入
Status SimpRelSecIdxesUpdate(HpTupleAddr addr, void *ttreeIdxUpdCtxIn, void *ctx)
{
    TTreeIdxUpdCtxT *ttreeIdxUpdCtx = ttreeIdxUpdCtxIn;
    HpRunHdlT hpHandle = ctx;
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < ttreeIdxUpdCtx->idxNum; i++) {
        IndexCtxT *secIdxCtx = hpHandle->compV1Info.idxCtxArray[i];
        secIdxCtx->ttreeDataInfo.addr32 = HeapCompressTupleAddr32(addr);
        secIdxCtx->ttreeDataInfo.buf = ttreeIdxUpdCtx->phyAddr;
        secIdxCtx->updateOrDelPtr = hpHandle->compV1Info.realUpdateOrDelPtr;
        if (ttreeIdxUpdCtx->ttreeUpdateStep == TTREE_UPDATE_STEP_UPDATE) {
            if (ttreeIdxUpdCtx->idxMatchId == i) {
                secIdxCtx->ttreePos = ttreeIdxUpdCtx->ttreePos;
            } else {
                secIdxCtx->ttreePos = NULL_NODEID;
            }
            IndexUpdateInfoT indexUpdateInfo = {
                .oldIdxKey = {.keyData = ttreeIdxUpdCtx->oldKey.buf, .keyLen = ttreeIdxUpdCtx->oldKey.bufSize},
                .newIdxKey = {.keyData = ttreeIdxUpdCtx->newKey.buf, .keyLen = ttreeIdxUpdCtx->newKey.bufSize},
                .oldAddr = addr,
                .newAddr = addr,
            };
            IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
            // TTree更新返回都是OK
            ret = IdxUpdate(secIdxCtx, indexUpdateInfo, removePara);
            DB_ASSERT(ret == GMERR_OK);
        } else {
            if (hpHandle->compV1Info.masterPointer !=
                InfoBuf2Real(secIdxCtx->isNormalIdx, secIdxCtx->ttreeDataInfo.buf)) {
                // 需要更新的addr为主版本addr,传入的addr只是可见的版本addr
                secIdxCtx->ttreeDataInfo.buf = RealBuf2Info(secIdxCtx->isNormalIdx, hpHandle->compV1Info.masterPointer);
            }
            IndexKeyT newIdxKey = {.keyData = ttreeIdxUpdCtx->newKey.buf, .keyLen = ttreeIdxUpdCtx->newKey.bufSize};
            ret = IdxInsert(secIdxCtx, newIdxKey, addr);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_ERROR(ret, "worthless update insert index by tuple buf, indexId = %" PRIu32 ".", i);
                return ret;
            }
        }
    }
    ttreeIdxUpdCtx->ttreeUpdateStep++;
    return ret;
}

// delete 0 和 update 0 记录表id到relNoList
static Status SimpleRelCheckAndRecordRelNoList(EmbSimpleRunCtxT *embCtx, EmbSimpleQueryCtxT *queryCtx)
{
    if (embCtx->diffCtx != NULL && queryCtx->hpTupleList.usedCount == 0) {
        SimpleRelDiffListT *diffList =
            SimpleRelationGetDiffListById(embCtx, embCtx->labelCursor->vertexLabel->metaCommon.metaId);
        if (diffList == NULL) {
            Status ret =
                SimpleRelationAllocDiffList(embCtx, embCtx->labelCursor->vertexLabel->metaCommon.metaId, &diffList);
            if (ret != GMERR_OK) {
                return ret;
            }
            // 记录表ID，主要用于强制提交可以获取所有操作表，还用于提交回滚加表锁，因此这里需要保证顺序，不然死锁
            ret = SimpleRelationDiffInsertTblList(
                &embCtx->diffCtx->relNoList, (uint16_t)embCtx->relNo, SIMP_REL_NOLIST_STATE_WRITE);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return GMERR_OK;
}

static Status SimpRelSerial(EmbSimpleRunCtxT *embCtx, SimRelLabelCursorT *labelCursor, EmbSimpleQueryCtxT *queryCtx)
{
    // 整条数据更新,提前序列化
    if (queryCtx->projectionPropId == NULL && !queryCtx->isZeroFldFilter) {
        return SimpleRelationDoSerial(embCtx, labelCursor, embCtx->seri, &labelCursor->heapTupleBuf);
    }
    return SimprelationCheckRecLenByProjection(
        queryCtx, labelCursor->vertexLabel->metaVertexLabel->schema, embCtx->seri);
}

static Status SimpRelExecUpdate(EmbSimpleRunCtxT *embCtx, EmbSimpleQueryCtxT *queryCtx)
{
    SimRelLabelCursorT *cursor = embCtx->labelCursor;
    Status ret = HeapLabelResetOpType(cursor->hpHandle, HEAP_OPTYPE_UPDATE, false);
    if (ret != GMERR_OK) {
        return ret;
    }
    if ((ret = SimpRelSerial(embCtx, cursor, queryCtx)) != GMERR_OK) {
        return ret;
    }

    for (uint32_t i = 0; i < queryCtx->hpTupleList.usedCount && !queryCtx->isZeroFldFilter; i++) {
        SimpHpTupleInfoT *hpTupleInfo = (SimpHpTupleInfoT *)DbGaListGet(&queryCtx->hpTupleList, i);
        // 部分字段更新
        if (SECUREC_UNLIKELY(queryCtx->projectionPropId != NULL)) {
            ret = SimpRelBuildTupleBuf(embCtx, queryCtx, &hpTupleInfo->tupleBuf, &cursor->heapTupleBuf);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        if ((ret = SimpleRelCheckIdxConflict(embCtx, hpTupleInfo)) != GMERR_OK) {
            const bool isCdbOperate = embCtx->session->session->isInteractiveTrx;
            return (ret == VOS_ERRNO_DB_KEYDUPLICATE && !isCdbOperate) ? VOS_ERRNO_DB_CHGKEY : ret;
        }
        if (!cursor->isBigObj) {
            *(uint32_t *)cursor->heapTupleBuf.buf = hpTupleInfo->addr32.pageIdAndSlotId;
        }
        SimpleRelationCalcChecksumDel(embCtx, cursor->isBigObj, &hpTupleInfo->tupleBuf);
        TupleAddr tmpAddr = HeapUncompressTupleAddr32(hpTupleInfo->addr32);
        TTreeIdxUpdCtxT ttreeCtx = {
            .newKey = {.buf = cursor->heapTupleBuf.buf, .bufSize = cursor->heapTupleBuf.bufSize},
            .oldKey = {.buf = hpTupleInfo->tupleBuf.buf, .bufSize = hpTupleInfo->tupleBuf.bufSize},
            .idxMatchId = queryCtx->idxMatchStru.idxId,
            .ttreePos = hpTupleInfo->ttreePos,
            .idxNum = cursor->idxNum,
            .phyAddr = hpTupleInfo->buf,
            .ttreeUpdateStep = TTREE_UPDATE_STEP_UPDATE,
        };
        cursor->hpHandle->compV1Info.updFun = SimpRelSecIdxesUpdate;
        cursor->hpHandle->compV1Info.ttreeIdxUpdCtx = &ttreeCtx;
        cursor->hpHandle->compV1Info.idxCtxArray = cursor->idxCtx;
        // heap更新内部更新索引
        if ((ret = SimpRelHeapUpdate(cursor->hpHandle, &cursor->heapTupleBuf, tmpAddr)) != GMERR_OK) {
            return ret;
        }

        SimpleRelationCalcChecksumAdd(embCtx, cursor->isBigObj, &cursor->heapTupleBuf);
        ret = SimpleRelationRecordDiff(embCtx, cursor->vertexLabel->metaCommon.metaId, tmpAddr, DIFF_OPERATION_UPDATE);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    queryCtx->affectRows = queryCtx->hpTupleList.usedCount;
    return SimpleRelCheckAndRecordRelNoList(embCtx, queryCtx);
}

static Status SimpRelSecIdxesDelete(
    EmbSimpleRunCtxT *embCtx, EmbSimpleQueryCtxT *queryCtx, SimpHpTupleInfoT *hpTupleInfo)
{
    IdxMatchStruT *idxMatchStru = &queryCtx->idxMatchStru;
    SimRelLabelCursorT *labelCursor = embCtx->labelCursor;
    IndexKeyT hashKey = {.keyData = hpTupleInfo->tupleBuf.buf, .keyLen = hpTupleInfo->tupleBuf.bufSize};

    for (uint32_t i = 0; i < embCtx->labelCursor->idxNum; i++) {
        IndexCtxT *secIdxCtx = embCtx->labelCursor->idxCtx[i];
        secIdxCtx->destNode = NULL_NODEID;
        if (idxMatchStru->isMatch && idxMatchStru->idxId == i) {
            secIdxCtx->ttreePos = hpTupleInfo->ttreePos;
        } else {
            secIdxCtx->ttreePos = NULL_NODEID;
        }
        IndexRemoveParaT removePara = {false, false};
        secIdxCtx->ttreeDataInfo.addr32 = hpTupleInfo->addr32;
        secIdxCtx->ttreeDataInfo.buf = hpTupleInfo->buf;
        secIdxCtx->updateOrDelPtr = labelCursor->hpHandle->compV1Info.realUpdateOrDelPtr;

        Status ret = IdxDelete(secIdxCtx, hashKey, HeapUncompressTupleAddr32(hpTupleInfo->addr32), removePara);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "unable to delete index.");
            return ret;
        }
    }
    return GMERR_OK;
}

static Status SimpRelHeapDelete(HpRunHdlT heapRunCtx, HpTupleAddr tupleAddr)
{
    bool needMarkDeleteOld = HeapLabelGetIsNeedMarkDelete(heapRunCtx);
    HeapLabelSetIsNeedMarkDelete(heapRunCtx, true);
    Status ret = HeapLabelDeleteHpTuple(heapRunCtx, tupleAddr);
    HeapLabelSetIsNeedMarkDelete(heapRunCtx, needMarkDeleteOld);
    if (ret != GMERR_OK) {
        if (ret == GMERR_OUT_OF_MEMORY) {
            // 0号RDB场景会强制提交一次RDB
            g_adptV1Instance.isSeOutMemory = true;
        }
        DB_LOG_ERROR(ret, "unable to delete heap tuple buffer.");
        return ret;
    }
    return GMERR_OK;
}

static Status SimpRelExecDelete(EmbSimpleRunCtxT *embCtx, EmbSimpleQueryCtxT *queryCtx)
{
    SimRelLabelCursorT *labelCursor = embCtx->labelCursor;
    Status ret = HeapLabelResetOpType(labelCursor->hpHandle, HEAP_OPTYPE_DELETE, false);
    if (ret != GMERR_OK) {
        return ret;
    };

    for (uint32_t i = 0; i < queryCtx->hpTupleList.usedCount; i++) {
        SimpHpTupleInfoT *hpTupleInfo = (SimpHpTupleInfoT *)DbGaListGet(&queryCtx->hpTupleList, i);
        ret = SimpRelHeapDelete(labelCursor->hpHandle, HeapUncompressTupleAddr32(hpTupleInfo->addr32));
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = SimpRelSecIdxesDelete(embCtx, queryCtx, hpTupleInfo);
        if (ret != GMERR_OK) {
            return ret;
        }
        SimpleRelationCalcChecksumDel(embCtx, labelCursor->isBigObj, &hpTupleInfo->tupleBuf);
        ret = SimpleRelationRecordDiff(embCtx, embCtx->labelCursor->vertexLabel->metaCommon.metaId,
            HeapUncompressTupleAddr32(hpTupleInfo->addr32), DIFF_OPERATION_DELETE);
        if (ret != GMERR_OK) {
            return ret;
        }
        queryCtx->affectRows++;
    }
    return SimpleRelCheckAndRecordRelNoList(embCtx, queryCtx);
}

static Status SimpleRelationUpdateOrDelete(EmbSimpleRunCtxT *embCtx)
{
    uint16_t originCheckSum = embCtx->vLabelCtrl->checkSum;
    SimpRelScanCtxT *scanCtx = embCtx->apiCtx;
    EmbSimpleQueryCtxT *queryCtx = &embCtx->labelCursor->queryCtxForUpdOrDel;
    Status ret = SimpRelCheckAndInitQueryCtx4Dml(embCtx, scanCtx, queryCtx, &queryCtx->idxMatchStru);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 获取需要更新或者删除的数据
    if (queryCtx->idxMatchStru.isMatch) {
        embCtx->labelCursor->bestIndexId = queryCtx->idxMatchStru.idxId;
        ret = SimpRelGetChangedDataByIndex(embCtx, queryCtx);
    } else {
        ret = SimpRelGetChangedDataBySequence(embCtx, queryCtx);
    }
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = (embCtx->opCode == SIMPLEREL_OP_TYPE_UPDATE) ? SimpRelExecUpdate(embCtx, queryCtx) :
                                                         SimpRelExecDelete(embCtx, queryCtx);
    if (ret == GMERR_OK) {
        scanCtx->affectRows = queryCtx->affectRows;
        return GMERR_OK;
    }

EXIT:
    // 失败回滚check sum
    embCtx->vLabelCtrl->checkSum = originCheckSum;
    scanCtx->affectRows = 0;
    return ret;
}

Status GmeSimpleRelationUpdate(GmeConnT *conn, SimpRelScanCtxT *scanCtx, GmcSeriT *seri)
{
    DB_POINTER3(conn, scanCtx, seri);
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    embCtx->opCode = SIMPLEREL_OP_TYPE_UPDATE;
    embCtx->apiCtx = scanCtx;
    embCtx->nspId = scanCtx->dbId;
    embCtx->relNo = scanCtx->relId;
    embCtx->seri = seri;
    return SimpleRelationExecute(embCtx, SimpleRelationUpdateOrDelete);
}

Status GmeSimpleRelationDelete(GmeConnT *conn, SimpRelScanCtxT *scanCtx)
{
    DB_POINTER2(conn, scanCtx);
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    embCtx->opCode = SIMPLEREL_OP_TYPE_DELETE;
    embCtx->apiCtx = scanCtx;
    embCtx->nspId = scanCtx->dbId;
    embCtx->relNo = scanCtx->relId;
    return SimpleRelationExecute(embCtx, SimpleRelationUpdateOrDelete);
}

Status SimpleRelationReplayInsert(EmbSimpleRunCtxT *embCtx)
{
    embCtx->isForceCommit = true;
    return SimpleRelationInsert(embCtx);
}

Status SimpleRelationDeleteIndex(EmbSimpleRunCtxT *embCtx, SimpHpTupleInfoT *hpTupleInfo)
{
    IndexKeyT hashKey = {.keyData = hpTupleInfo->tupleBuf.buf, .keyLen = hpTupleInfo->tupleBuf.bufSize};
    for (uint32_t i = 0; i < embCtx->labelCursor->idxNum; i++) {
        IndexCtxT *secIdxCtx = embCtx->labelCursor->idxCtx[i];
        secIdxCtx->updateOrDelPtr = embCtx->labelCursor->hpHandle->compV1Info.realUpdateOrDelPtr;
        secIdxCtx->destNode = NULL_NODEID;
        IndexRemoveParaT removePara = {false, false};
        secIdxCtx->ttreeDataInfo.addr32 = hpTupleInfo->addr32;
        secIdxCtx->ttreeDataInfo.buf = hpTupleInfo->buf;
        secIdxCtx->ttreePos = NULL_NODEID;
        Status ret = IdxDelete(secIdxCtx, hashKey, HeapUncompressTupleAddr32(hpTupleInfo->addr32), removePara);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "unable to delete index.");
            return ret;
        }
    }
    return GMERR_OK;
}

static Status SimpleRelationReplayAllocTupleBuf(
    DbMemCtxT *memCtx, SimRelLabelCursorT *labelCursor, HeapTupleBufT *tupleBuf)
{
    if (!labelCursor->isBigObj) {
        return GMERR_OK;
    }

    uint32_t bufSize = labelCursor->vertexLabel->metaVertexLabel->schema->maxRecLen;
    tupleBuf->buf = DbDynMemCtxAlloc(memCtx, bufSize);
    if (tupleBuf->buf == NULL) {
        DB_LOG_AND_SET_LASERR(
            VOS_ERRNO_DB_MEMALLOCFAILURE, "Unable to alloc tupleBuf for replay delete, size %" PRIu32 ".", bufSize);
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }
    tupleBuf->bufSize = bufSize;
    return GMERR_OK;
}

inline static void SimpleRelationReplayFreeTupleBuf(
    DbMemCtxT *memCtx, SimRelLabelCursorT *labelCursor, HeapTupleBufT *tupleBuf)
{
    if (!labelCursor->isBigObj) {
        return;
    }

    DbDynMemCtxFree(memCtx, tupleBuf->buf);
}

Status SimpleRelationReplayDelete(EmbSimpleRunCtxT *embCtx)
{
    SimRelLabelCursorT *labelCursor = embCtx->labelCursor;
    uint64_t *addr = (uint64_t *)embCtx->apiCtx;
    SimpHpTupleInfoT hpTupleInfo = {0};
    hpTupleInfo.addr32 = HeapCompressTupleAddr32(*addr);
    hpTupleInfo.ttreePos = NULL_NODEID;
    Status ret = SimpleRelationReplayAllocTupleBuf(embCtx->apiMemCtx, labelCursor, &hpTupleInfo.tupleBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 尝试从存储获取最新的RDB数据记录
    ret = HeapFetchHpTupleBufferTrxInfo(labelCursor->hpHandle, *addr, &hpTupleInfo.tupleBuf);
    if (ret != GMERR_OK) {
        SimpleRelationReplayFreeTupleBuf(embCtx->apiMemCtx, labelCursor, &hpTupleInfo.tupleBuf);
        // 如果没找到数据,忽略处理下一条
        return ret == GMERR_NO_DATA ? GMERR_OK : ret;
    }
    hpTupleInfo.buf = labelCursor->hpHandle->hpControl.isFixPage ? hpTupleInfo.tupleBuf.buf + EXTERN_DATA_BUF_LEN :
                                                                   labelCursor->hpHandle->compV1Info.realInsertPtr;
    // 如果找到记录,直接删除
    ret = HeapLabelResetOpType(labelCursor->hpHandle, HEAP_OPTYPE_DELETE, false);
    if (ret != GMERR_OK) {
        SimpleRelationReplayFreeTupleBuf(embCtx->apiMemCtx, labelCursor, &hpTupleInfo.tupleBuf);
        return ret;
    };
    // 删除heap
    ret = SimpRelHeapDelete(labelCursor->hpHandle, *addr);
    if (ret != GMERR_OK) {
        SimpleRelationReplayFreeTupleBuf(embCtx->apiMemCtx, labelCursor, &hpTupleInfo.tupleBuf);
        return ret;
    }
    // 删除RDB数据的索引
    ret = SimpleRelationDeleteIndex(embCtx, &hpTupleInfo);
    SimpleRelationReplayFreeTupleBuf(embCtx->apiMemCtx, labelCursor, &hpTupleInfo.tupleBuf);
    return ret;
}

Status SimpleRelationReplayUpdate(EmbSimpleRunCtxT *embCtx)
{
    SimRelLabelCursorT *labelCursor = embCtx->labelCursor;
    uint64_t *addr = (uint64_t *)embCtx->apiCtx;
    SimpHpTupleInfoT hpTupleInfo;
    // api结束，reset apiMemCtx释放
    Status ret = SimpleRelationReplayAllocTupleBuf(embCtx->apiMemCtx, labelCursor, &hpTupleInfo.tupleBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    hpTupleInfo.addr32 = HeapCompressTupleAddr32(*addr);
    hpTupleInfo.ttreePos = NULL_NODEID;
    // 尝试从存储获取最新的RDB数据记录
    ret = HeapFetchHpTupleBufferTrxInfo(labelCursor->hpHandle, *addr, &hpTupleInfo.tupleBuf);
    if (ret != GMERR_OK) {
        // 如果没有找到记录,转换成插入动作
        embCtx->isForceCommit = true;
        return ret == GMERR_NO_DATA ? SimpleRelationInsert(embCtx) : ret;
    }
    hpTupleInfo.tupleBuf.buf += (!labelCursor->isBigObj ? EXTERN_DATA_BUF_LEN : 0);
    hpTupleInfo.buf =
        !labelCursor->isBigObj ? hpTupleInfo.tupleBuf.buf : labelCursor->hpHandle->compV1Info.realInsertPtr;
    // 更新为最新的数据
    ret = SimpleRelationDoSerial(embCtx, embCtx->labelCursor, embCtx->seri, &embCtx->labelCursor->heapTupleBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 判断唯一性
    ret = SimpleRelCheckIdxConflict(embCtx, &hpTupleInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = HeapLabelResetOpType(labelCursor->hpHandle, HEAP_OPTYPE_UPDATE, false);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (!labelCursor->isBigObj) {
        *(uint32_t *)labelCursor->heapTupleBuf.buf = hpTupleInfo.addr32.pageIdAndSlotId;
    }
    // 更新heap
    TTreeIdxUpdCtxT ttreeCtx = {
        .newKey = {.buf = labelCursor->heapTupleBuf.buf, .bufSize = labelCursor->heapTupleBuf.bufSize},
        .oldKey = {.buf = hpTupleInfo.tupleBuf.buf, .bufSize = hpTupleInfo.tupleBuf.bufSize},
        .idxMatchId = 0,
        .idxNum = labelCursor->idxNum,
        .ttreePos = NULL_NODEID,
        .phyAddr = hpTupleInfo.buf,
        .ttreeUpdateStep = TTREE_UPDATE_STEP_UPDATE,
    };
    labelCursor->hpHandle->compV1Info.updFun = SimpRelSecIdxesUpdate;
    labelCursor->hpHandle->compV1Info.ttreeIdxUpdCtx = &ttreeCtx;
    labelCursor->hpHandle->compV1Info.idxCtxArray = labelCursor->idxCtx;
    return SimpRelHeapUpdate(labelCursor->hpHandle, &labelCursor->heapTupleBuf, *addr);
}

void SimpleRelationSetReplaySeriInfo(uint32_t recLen, uint8_t *dataBuf, GmcSeriT *seri)
{
    seri->obj = dataBuf;
    seri->bufSize = recLen;
    ((DB_DSBUF_STRU *)seri->userData)->StdBuf.pucData = dataBuf;
    ((DB_DSBUF_STRU *)seri->userData)->StdBuf.ulActLen = recLen;
}

Status SimpleRelationRedoInsertOps(EmbSimpleRunCtxT *embCtx, SimpRelScanCtxT *scanCtx, GmcSeriT *seri)
{
    SimpRelSelectDiffResultT *diffCtx = scanCtx->diffCtx;
    uint32_t insertOffset = 0;
    for (uint32_t i = 0; i < diffCtx->pulInsRecNum; i++) {
        // insert操作直接插入数据,可能会因为违反唯一性报错
        SimpleRelationSetReplaySeriInfo(diffCtx->recLen, diffCtx->ppInsRecList + insertOffset, seri);
        embCtx->opCode = SIMPLEREL_OP_TYPE_REPLAY;
        embCtx->nspId = scanCtx->dbId;
        embCtx->relNo = scanCtx->relId;
        embCtx->seri = seri;
        Status ret = SimpleRelationExecute(embCtx, SimpleRelationReplayInsert);
        if (ret != GMERR_OK) {
            return ret;
        }
        insertOffset += diffCtx->recLen;
    }
    return GMERR_OK;
}

Status SimpleRelationRedoDeleteOps(EmbSimpleRunCtxT *embCtx, SimpRelScanCtxT *scanCtx, GmcSeriT *seri)
{
    SimpRelSelectDiffResultT *diffCtx = scanCtx->diffCtx;
    uint32_t index = 0;
    uint32_t deleteOffset = 0;
    for (uint32_t i = 0; i < diffCtx->pulDelRecNum; i++) {
        // 通过diffCtx->list获取,由于提取时是按序的,这里也必定顺序一致
        SimpleRelDiffListItemT *item = NULL;
        for (; index < DbGaListGetCount(diffCtx->list); index++) {
            item = (SimpleRelDiffListItemT *)DbGaListGet(diffCtx->list, index);
            if (SimpleRelationIsDeleteOp(item->state)) {
                break;
            }
        }
        if (item == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "no delete addr NO.%" PRIu32 ".", i);
            return GMERR_DATA_EXCEPTION;
        }
        SimpleRelationSetReplaySeriInfo(diffCtx->recLen, diffCtx->ppDelRecList + deleteOffset, seri);
        embCtx->apiCtx = &item->addr;
        embCtx->opCode = SIMPLEREL_OP_TYPE_REPLAY;
        embCtx->nspId = scanCtx->dbId;
        embCtx->relNo = scanCtx->relId;
        embCtx->seri = seri;
        Status ret = SimpleRelationExecute(embCtx, SimpleRelationReplayDelete);
        if (ret != GMERR_OK) {
            return ret;
        }
        deleteOffset += diffCtx->recLen;
        index++;
    }
    return GMERR_OK;
}

Status SimpleRelationRedoUpdateOps(EmbSimpleRunCtxT *embCtx, SimpRelScanCtxT *scanCtx, GmcSeriT *seri)
{
    SimpRelSelectDiffResultT *diffCtx = scanCtx->diffCtx;
    uint32_t index = 0;
    uint32_t updateOffset = 0;
    uint32_t realUpdRecNum = diffCtx->pulUpdRecNum >> 1;  // 存有新老数据,实际数量减半
    for (uint32_t i = 0; i < realUpdRecNum; i++) {
        // 通过diffCtx->list获取,由于提取时是按序的,这里也必定顺序一致
        SimpleRelDiffListItemT *item = NULL;
        for (; index < DbGaListGetCount(diffCtx->list); index++) {
            item = (SimpleRelDiffListItemT *)DbGaListGet(diffCtx->list, index);
            if (SimpleRelationIsUpdateOp(item->state)) {
                break;
            }
        }
        if (item == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "no update addr NO.%" PRIu32 ".", i);
            return GMERR_DATA_EXCEPTION;
        }
        SimpleRelationSetReplaySeriInfo(diffCtx->recLen, diffCtx->ppUpdRecList + updateOffset, seri);
        embCtx->apiCtx = &item->addr;
        embCtx->opCode = SIMPLEREL_OP_TYPE_REPLAY;
        embCtx->nspId = scanCtx->dbId;
        embCtx->relNo = scanCtx->relId;
        embCtx->seri = seri;
        Status ret = SimpleRelationExecute(embCtx, SimpleRelationReplayUpdate);
        if (ret != GMERR_OK) {
            return ret;
        }
        // 偏移新老数据
        updateOffset += (diffCtx->recLen + diffCtx->recLen);
        index++;
    }
    return GMERR_OK;
}

Status GmeSimpleRelRedoOps(GmeConnT *conn, SimpRelScanCtxT *scanCtx, GmcSeriT *seri)
{
    DB_POINTER2(conn, scanCtx);
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    if (embCtx->nspCtrl == NULL) {
        NspCtrlT *nspCtrl = SimpleRelGetNspCtrlById(&embCtx->simpleRelMgr->nspCtrlList, scanCtx->dbId, NULL);
        if (nspCtrl == NULL) {
            DB_LOG_AND_SET_LASERR(
                VOS_ERRNO_DB_INVALID_DATABASE, "Incorrect dbId(%" PRIu32 ") when operate redo.", scanCtx->dbId);
            return VOS_ERRNO_DB_INVALID_DATABASE;
        }
        embCtx->nspCtrl = nspCtrl;
    }
    // 先做update的重演
    Status ret = SimpleRelationRedoUpdateOps(embCtx, scanCtx, seri);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 再做delete的重演
    ret = SimpleRelationRedoDeleteOps(embCtx, scanCtx, seri);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 最后最insert的重演
    return SimpleRelationRedoInsertOps(embCtx, scanCtx, seri);
}
