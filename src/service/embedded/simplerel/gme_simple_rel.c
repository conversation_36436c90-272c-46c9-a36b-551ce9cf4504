/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Embedded SIMPLE REL API
 * Author:
 * Create:
 */
#include "srv_emb_sim_com.h"
#include "srv_simple_rel.h"
#include "ee_cmd.h"
#include "se_ttree_index_base_inner.h"
#include "se_trx_inner.h"
#include "se_trx_mgr.h"

#define EDGE_HANDLE_BUFLEN_DOUBEL 2  // 需要预留额外条件
#define IDX_RESERVE_BUF_LEN SIZE_K(65)
#define IDX_RESERVE_TWO_BUF_LEN (IDX_RESERVE_BUF_LEN * 2)
#define DML_LIST_RESERVE_MAX_REC_CNT (0xFFF)

Status DbFileReadPrepare(const char *filePath, int32_t *handle)
{
    uint32_t mode = O_BINARY | O_RDONLY;
    uint32_t perm = ((mode & O_CREAT) != 0) ? S_IRUSR | S_IWUSR | S_IRGRP : 0;
    Status ret = DbOpenFile(filePath, (int32_t)mode, perm, handle);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to open file(%s), os ret no: %" PRId32 ".", filePath, (int32_t)errno);
        return ret;
    }

    return GMERR_OK;
}

static void ResetEmbeddedSimpleRunCtx4Api(EmbSimpleRunCtxT *embCtx)
{
    if (SECUREC_UNLIKELY(EmbSimRelIsReplayOp(embCtx->opCode))) {
        return;
    }
    embCtx->relNo = DB_INVALID_UINT32;
    embCtx->seri = NULL;
    embCtx->apiCtx = NULL;
    embCtx->opCode = SIMPLEREL_OP_TYPE_NONE;
    embCtx->nspId = DB_INVALID_UINT32;
    embCtx->nspCtrl = NULL;
    embCtx->vLabelCtrl = NULL;
    embCtx->isCreateSavePonit = false;
    embCtx->isQuickExit = false;
    embCtx->isTpcOp = false;
    embCtx->isTwoStage = false;
}

static bool SimpRelOpCodeIsNeedInstaceWLock(SimpleRelOpE opCode)
{
    // nsp DDL write ; other read
    return EmbSimRelOpIsNspDDL(opCode);
}

static bool SimpRelOpCodeIsNeedNspWLock(SimpleRelOpE opCode)
{
    // DDL/DCL write ; DML/DQL read
    return EmbSimRelOpIsDDL(opCode) || EmbSimRelOpIsDCL(opCode);
}

inline static bool SimpRelOpCodeIsNeedCheckDbId(SimpleRelOpE opCode)
{
    // 这个范围内接口都有入参db id，需要校验db是否存在
    return (SIMPLEREL_OP_TYPE_NAMESPACE_OPEN < opCode) && (opCode < SIMPLEREL_OP_TYPE_GET_META_WITHOUT_DBID);
}

inline static bool SimpRelOpCodeIsImportDb(SimpleRelOpE opCode)
{
    // 这个范围内接口都有入参db id，需要校验db是否存在
    return (SIMPLEREL_OP_TYPE_IMPORT_DATA == opCode);
}

void SimpleRelConcurrentControlEnd(EmbSimpleRunCtxT *embCtx)
{
    if (SECUREC_UNLIKELY(EmbSimRelIsReplayOp(embCtx->opCode))) {
        return;
    }
    LabelRWLatchT **latch = DbListItem(&embCtx->concurentCtrl.labelLock, 0);
    for (uint32_t i = 0; i < embCtx->concurentCtrl.labelLock.count; i++, latch++) {
        if (embCtx->concurentCtrl.labelState == SIMPLEREL_LOCK_TYPE_WRITE) {
            LabelWLatchRelease(*latch);
        } else {
            LabelRLatchRelease(*latch);
        }
    }
    if (embCtx->concurentCtrl.nspLockState != SIMPLEREL_LOCK_TYPE_NONE && !embCtx->isRestoreDelete) {
        DB_ASSERT(embCtx->nspCtrl != NULL);
        if (embCtx->concurentCtrl.nspLockState == SIMPLEREL_LOCK_TYPE_WRITE) {
            DbRWUnlatchW(&embCtx->nspCtrl->latch);  // 在api请求结束时 DbRWUnlatchW
        } else {
            DbRWUnlatchR(&embCtx->nspCtrl->latch);  // 在api请求结束时 DbRWUnlatchR
        }
    }
    if (embCtx->concurentCtrl.instanceLockState == SIMPLEREL_LOCK_TYPE_WRITE) {
        DbRWUnlatchW(&embCtx->simpleRelMgr->latch);  // 在api请求结束时 DbRWUnlatchW
    } else if (embCtx->concurentCtrl.instanceLockState == SIMPLEREL_LOCK_TYPE_READ) {
        DbRWUnlatchR(&embCtx->simpleRelMgr->latch);  // 在api请求结束时 DbRWUnlatchR
    }
    EmbeddedSimpleLockInfoClear(&embCtx->concurentCtrl);
    return;
}

static Status SimpleRelQuickDmlOp(EmbSimpleRunCtxT *embCtx, VlabelCtrlT *vLabelCtrl, bool isUpdate)
{
    Status ret;
    embCtx->isQuickExit = true;
    SessionT *session = embCtx->session->session;
    if (session->isInteractiveTrx) {
        SeTransSetLabelModifiedActive(session->seInstance);
        SeTransCheckAndSetLabelModified(
            session->seInstance, vLabelCtrl->vertexLabel->metaCommon.metaId, TRX_VERTEXLABEL_HEAP, true);
        ret = SimpleRelationDiffInsertTblList(
            &embCtx->diffCtx->relNoList, (uint16_t)embCtx->relNo, SIMP_REL_NOLIST_STATE_WRITE);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    ret = SimpleRelCheckScanCtx(embCtx->apiCtx, vLabelCtrl, true, embCtx->nspCtrl->isTpcDb);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (!isUpdate) {
        return GMERR_OK;
    }
    return SimpleRelCheckRecLen(embCtx->apiCtx, vLabelCtrl, embCtx->seri);
}

static Status SimpleRelQuickScan(EmbSimpleRunCtxT *embCtx, VlabelCtrlT *vLabelCtrl)
{
    embCtx->isQuickExit = true;
    SessionT *session = embCtx->session->session;
    if (session->isInteractiveTrx) {
        SeTransCheckAndSetLabelModified(
            session->seInstance, vLabelCtrl->vertexLabel->metaCommon.metaId, TRX_VERTEXLABEL_HEAP, false);
        SimpleRelationDiffInsertTblList(
            &embCtx->diffCtx->relNoList, (uint16_t)embCtx->relNo, SIMP_REL_NOLIST_STATE_READ);
    }

    SimpRelScanCtxT *scanCtx = (SimpRelScanCtxT *)embCtx->apiCtx;
    Status ret = SimpleRelCheckScanCtx(scanCtx, vLabelCtrl, true, embCtx->nspCtrl->isTpcDb);
    if (ret == GMERR_OK) {
        if (scanCtx->dataStru != NULL && !scanCtx->isFetchTopo) {
            DB_BUF_STRU *bufStru = (scanCtx)->dataStru;
            bufStru->ulRecNum = 0;
            bufStru->usRecLen = vLabelCtrl->originDefInfo->usRecLen;
        }
        return VOS_ERRNO_DB_RECNOTEXIST;
    }
    return ret;
}

Status SimpleRelConcurrentOperateInner(EmbSimpleRunCtxT *embCtx)
{
    if (!EmbSimRelOpIsDQL(embCtx->opCode) && !EmbSimRelOpIsDML(embCtx->opCode)) {
        return GMERR_OK;
    }

    VlabelCtrlT *vLabelCtrl = NULL;
    Status ret = SimpleRelGetVlabelCtrlById(&embCtx->nspCtrl->vLabelCtrlList, embCtx->relNo, NULL, &vLabelCtrl, embCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    embCtx->vLabelCtrl = vLabelCtrl;
    if (EmbSimRelOpIsDQL(embCtx->opCode)) {
        embCtx->concurentCtrl.labelState = SIMPLEREL_LOCK_TYPE_READ;
        LabelRLatchAcquire(vLabelCtrl->latch);
    } else if (EmbSimRelOpIsDML(embCtx->opCode)) {
        embCtx->concurentCtrl.labelState = SIMPLEREL_LOCK_TYPE_WRITE;
        g_adptV1Instance.isModDB = true;
        LabelWLatchAcquire(vLabelCtrl->latch);
    }
    ret = DbAppendListItem(&embCtx->concurentCtrl.labelLock, &vLabelCtrl->latch);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "unable to append label lock to label lock list.");
        return ret;
    }

    if (!vLabelCtrl->hasInsert) {
        switch (embCtx->opCode) {
            case SIMPLEREL_OP_TYPE_UPDATE:
                return SimpleRelQuickDmlOp(embCtx, vLabelCtrl, true);
            case SIMPLEREL_OP_TYPE_DELETE:
                return SimpleRelQuickDmlOp(embCtx, vLabelCtrl, false);
            case SIMPLEREL_OP_TYPE_SCAN:
                return SimpleRelQuickScan(embCtx, vLabelCtrl);
            default:
                return GMERR_OK;
        }
    }
    return GMERR_OK;
}

Status SimpleRelConcurrentControlBegin(EmbSimpleRunCtxT *embCtx)
{
    if (SECUREC_UNLIKELY(EmbSimRelIsReplayOp(embCtx->opCode))) {
        return GMERR_OK;
    }
    // 首先清理所有加锁信息, 后续所有失败, 统一 SimpleRelationExecute 释放
    EmbeddedSimpleLockInfoClear(&embCtx->concurentCtrl);
    if (SimpRelOpCodeIsNeedInstaceWLock(embCtx->opCode)) {
        embCtx->concurentCtrl.instanceLockState = SIMPLEREL_LOCK_TYPE_WRITE;
        DbRWLatchW(&embCtx->simpleRelMgr->latch);  // 在api请求结束时 DbRWUnlatchW
    } else {
        embCtx->concurentCtrl.instanceLockState = SIMPLEREL_LOCK_TYPE_READ;
        DbRWLatchR(&embCtx->simpleRelMgr->latch);  // 在api请求结束时 DbRWUnlatchR
    }

    if ((embCtx->nspId != DB_MAX_UINT32) || SimpRelOpCodeIsNeedCheckDbId(embCtx->opCode)) {
        embCtx->nspCtrl = SimpleRelGetNspCtrlById(&embCtx->simpleRelMgr->nspCtrlList, embCtx->nspId, NULL);
        if (embCtx->nspCtrl == NULL) {
            DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALID_DATABASE, "DB with id (%u) not exists.", embCtx->nspId);
            return VOS_ERRNO_DB_INVALID_DATABASE;
        }
        // 元数据读接口有部分DB锁加在execute内
        if (embCtx->opCode == SIMPLEREL_OP_TYPE_NAMESPACE_CLOSE) {
            if (!DbRWLatchTryW(&embCtx->nspCtrl->latch)) {
                DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_CLOSE_NOTALLOWED, "DB is operating.", embCtx->nspId);
                SimpleRelationSetLastError(embCtx->nspCtrl, __FILE__, __LINE__, VOS_ERRNO_DB_CLOSE_NOTALLOWED,
                    (const char *)"DB is operating.");
                return VOS_ERRNO_DB_CLOSE_NOTALLOWED;
            }
            embCtx->concurentCtrl.nspLockState = SIMPLEREL_LOCK_TYPE_WRITE;
        } else if (SimpRelOpCodeIsNeedNspWLock(embCtx->opCode)) {
            embCtx->concurentCtrl.nspLockState = SIMPLEREL_LOCK_TYPE_WRITE;
            DbRWLatchW(&embCtx->nspCtrl->latch);  // 在api请求结束时 DbRWUnlatchW
        } else {
            embCtx->concurentCtrl.nspLockState = SIMPLEREL_LOCK_TYPE_READ;
            DbRWLatchR(&embCtx->nspCtrl->latch);  // 在api请求结束时 DbRWUnlatchR
        }
        if (SECUREC_UNLIKELY(embCtx->nspCtrl->openCnt == 0) && embCtx->opCode > SIMPLEREL_OP_TYPE_NAMESPACE_OPEN) {
            DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_DATABASE_NOT_OPENED, "Namespace dose not opened.");
            return VOS_ERRNO_DB_DATABASE_NOT_OPENED;
        }
        embCtx->isTwoStage = embCtx->nspCtrl->isTpcDb;
    } else if (SimpRelOpCodeIsImportDb(embCtx->opCode)) {
        embCtx->nspCtrl = SimpleRelGetNspCtrlByName(
            &embCtx->simpleRelMgr->nspCtrlList, ((SimpRelRestoreCtxT *)(embCtx->apiCtx))->nspName, NULL);
        if (embCtx->nspCtrl != NULL) {
            embCtx->concurentCtrl.nspLockState = SIMPLEREL_LOCK_TYPE_WRITE;
            DbRWLatchW(&embCtx->nspCtrl->latch);  // 在api请求结束时 DbRWUnlatchW;
        }
    }
    return SimpleRelConcurrentOperateInner(embCtx);
}

static Status SimpleRelationPrepareLabel(EmbSimpleRunCtxT *embCtx, SimRelPrepareFunc prepareFunc)
{
    if (embCtx->vLabelCtrl == NULL || embCtx->relNo != embCtx->vLabelCtrl->relationId) {
        // find vertexLabel by relNo
        VlabelCtrlT *vLabelCtrl = NULL;
        // 此处尝试懒加载
        Status ret =
            SimpleRelGetVlabelCtrlById(&embCtx->nspCtrl->vLabelCtrlList, embCtx->relNo, NULL, &vLabelCtrl, embCtx);
        if (ret != GMERR_OK) {
            return ret;
        }

        embCtx->vLabelCtrl = vLabelCtrl;
    }

    Status ret = SimpleRelLabelCursorAlloc(embCtx);
    if (ret != GMERR_OK) {
        return ret;
    }

    return prepareFunc(embCtx, embCtx->vLabelCtrl);
}

Status SimpleRelationPrepare(EmbSimpleRunCtxT *embCtx)
{
    Status ret = GMERR_OK;

    if (EmbSimRelOpIsDML(embCtx->opCode)) {
        SeTransSetLabelModifiedActive(embCtx->seRunCtx);
        ret = SimpleRelationPrepareLabel(embCtx, SimpleRelPrepareDmlCursor);
    } else if (EmbSimRelOpIsDQL(embCtx->opCode)) {
        ret = SimpleRelationPrepareLabel(embCtx, SimpleRelPrepareScanCursor);
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Prepare label worthless.");
    }
    return ret;
}

inline static void SimpleRelSetEmptyFetchConds(SimRelLabelCursorT *labelCursor)
{
    // fetch select 使用的是挂在handle上的条件，查询结束置为NULL，避免误用
    labelCursor->queryCtx.filterCondNum = 0;
    labelCursor->queryCtx.idxCondNum = 0;
    labelCursor->queryCtx.idxResumeCondNum = 0;
    labelCursor->queryCtx.filterConds = NULL;
    labelCursor->queryCtx.idxConds = NULL;
    labelCursor->queryCtx.idxResumeConds = NULL;
}

inline static void QyeryCtxFreeReservedDmlTupleList(EmbSimpleQueryCtxT *queryCtx4Dml)
{
    if (queryCtx4Dml->hpTupleList.freeFunc != NULL &&
        DbGaListGetCount(&queryCtx4Dml->hpTupleList) > DML_LIST_RESERVE_MAX_REC_CNT) {
        uint32_t nodeCount = 0;
        while ((nodeCount = DbGaListGetCount(&queryCtx4Dml->hpTupleList)) > DML_LIST_RESERVE_MAX_REC_CNT) {
            DbGaListDelete(&queryCtx4Dml->hpTupleList, nodeCount - 1);
        }
    }
}

void SimpleRelationPrepareClear(EmbSimpleRunCtxT *embCtx, Status opStatus)
{
    SimRelLabelCursorT *labelCursor = embCtx->labelCursor;
    if (labelCursor != NULL) {
        if (EmbSimRelOpIsDQL(embCtx->opCode) && labelCursor->hpHandle != NULL) {
            if (labelCursor->hpHandle->cursorNum > 0 && labelCursor->heapCursor != NULL) {
                HeapLabelEndScan(labelCursor->hpHandle, labelCursor->heapCursor);
            }
            QueryCtxFreeSortItem(&labelCursor->queryCtx.sortItem);

            labelCursor->hpHandle->hpOperation = HEAP_OPTYPE_NONE_OP;
            if (labelCursor->queryCtx.isFetchSelect) {
                SimpleRelSetEmptyFetchConds(labelCursor);
            }
        }
        if (EmbSimRelOpIsDDL(embCtx->opCode)) {
            if (embCtx->labelCursor->lastOpType == SIMPLEREL_READ) {
                SimpRelCloseReadCursor(embCtx->labelCursor);
            } else {
                SimpRelCloseWriteCursor(embCtx->labelCursor);
            }
        }
        QueryCtxFreeComplexOpRecArray(labelCursor->memCtx, &labelCursor->queryCtx);
        QueryCtxFreeComplexOpRecArray(labelCursor->memCtx, &labelCursor->queryCtxForUpdOrDel);
        QyeryCtxFreeReservedDmlTupleList(&labelCursor->queryCtxForUpdOrDel);
    }
    // some reset/clear: 释放表锁, 释放实例锁
    DbMemCtxReset(embCtx->apiMemCtx);
    ResetEmbeddedSimpleRunCtx4Api(embCtx);
}

static inline void SimpleRelationAddSavePointSeq(EmbSimpleRunCtxT *embCtx)
{
    // RDB直接返回
    if (embCtx->diffCtx == NULL) {
        return;
    }
    embCtx->diffCtx->savepointSeq++;
}

inline static void SimpleRelSetCurHeapTrxOpStat(EmbSimpleRunCtxT *embCtx)
{
    DB_POINTER(embCtx);
    SimRelLabelCursorT *labelCursor = embCtx->labelCursor;
    if (labelCursor != NULL && labelCursor->hpHandle != NULL && labelCursor->hpHandle->heapTrxCtx != NULL) {
        embCtx->writeBytes = labelCursor->hpHandle->heapTrxCtx->opStat.writeBytes;
        embCtx->deleteBytes = labelCursor->hpHandle->heapTrxCtx->opStat.deleteBytes;
    }
}

inline static void SimpleRelRollBackHeapTrxOpStat(EmbSimpleRunCtxT *embCtx)
{
    DB_POINTER(embCtx);
    SimRelLabelCursorT *labelCursor = embCtx->labelCursor;
    if (labelCursor != NULL && labelCursor->hpHandle != NULL && labelCursor->hpHandle->heapTrxCtx != NULL) {
        labelCursor->hpHandle->heapTrxCtx->opStat.writeBytes = embCtx->writeBytes;
        labelCursor->hpHandle->heapTrxCtx->opStat.deleteBytes = embCtx->deleteBytes;
    }
}

static Status SimpleRelCreateSeSavepoint(EmbSimpleRunCtxT *embCtx, SeRunCtxHdT seInstance)
{
    if (EmbSimRelOpIsDQL(embCtx->opCode)) {  // 查询操作不创建savepoint
        return GMERR_OK;
    } else if (EmbSimRelOpIsDML(embCtx->opCode) && embCtx->isZeroRDBConn) {
        g_adptV1Instance.RDBWriteCnt++;
    }

    SeTrxSavepointNameT spName = {.length = strlen("default"), .name = "default"};
    Status ret = SeTrxCreateSavepoint(seInstance, &spName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unabel to create savepoint when begin trx.");
        return ret;
    }

    SimpleRelSetCurHeapTrxOpStat(embCtx);
    embCtx->isCreateSavePonit = true;
    return GMERR_OK;
}

static Status SimpleRelPrepareAndBeginInteractiveTrx(EmbSimpleRunCtxT *embCtx, SessionT *session)
{
    Status ret = SimpleRelationPrepare(embCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SimpleRelCreateSeSavepoint(embCtx, session->seInstance);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (!EmbSimRelOpIsDQL(embCtx->opCode)) {
        // 每次序号加1，用于diff回滚
        SimpleRelationAddSavePointSeq(embCtx);
    }
    return GMERR_OK;
}

Status SimpleRelationBeginTrxInner(EmbSimpleRunCtxT *embCtx, SessionT *session)
{
    TrxCfgT cfg = {.isolationLevel = g_adptV1Instance.isolationLevel[embCtx->isTwoStage], .trxType = OPTIMISTIC_TRX};
    Status ret = SeTransBegin(session->seInstance, &cfg);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SeTransAssignReadView(session->seInstance);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status SimpleRelationPrepareAndBeginTrx(EmbSimpleRunCtxT *embCtx)
{
    Status ret = GMERR_OK;
    SessionT *session = embCtx->session->session;
    if (session->isInteractiveTrx) {
        return SimpleRelPrepareAndBeginInteractiveTrx(embCtx, session);
    }

    if (embCtx->isZeroRDBConn && embCtx->RDBTrxState == RDB_TRX_RUNNING) {
        ret = SimpleRelationPrepare(embCtx);
        if (ret != GMERR_OK) {
            return ret;
        }
        return SimpleRelCreateSeSavepoint(embCtx, session->seInstance);
    }
    ret = SimpleRelationBeginTrxInner(embCtx, session);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (embCtx->isZeroRDBConn) {
        g_adptV1Instance.RDBTrxState = RDB_TRX_RUNNING;
        g_adptV1Instance.RDBUsedDbId = embCtx->nspId;
        g_adptV1Instance.RDBUsedRelId = embCtx->relNo;
    }

    ret = SimpleRelationPrepare(embCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (embCtx->isZeroRDBConn) {
        return SimpleRelCreateSeSavepoint(embCtx, session->seInstance);
    }
    return GMERR_OK;
}

Status SimpleRelationEndZeroRdbTrx(
    EmbSimpleRunCtxT *embCtx, SessionT *session, SeTrxSavepointNameT *spName, Status excuteStatus)
{
    if ((g_adptV1Instance.isAutoCommitZeroRDB && g_adptV1Instance.isModDB) || g_adptV1Instance.commitCount > 0) {
        if (excuteStatus == GMERR_OK) {
            excuteStatus = SeTransCommit(session->seInstance);
            DB_ASSERT(excuteStatus == GMERR_OK);
        } else {
            if (!EmbSimRelOpIsDQL(embCtx->opCode) && embCtx->isCreateSavePonit) {
                (void)SeTrxRollbackToSavepoint(session->seInstance, spName);
                SimpleRelRollBackHeapTrxOpStat(embCtx);
                (void)SeTrxReleaseSavepoint(session->seInstance, spName);
            }
            Status tempRet = SeTransCommit(session->seInstance);
            DB_ASSERT(tempRet == GMERR_OK);
        }
        g_adptV1Instance.RDBTrxState = RDB_TRX_WAIT;
        embCtx->RDBTrxState = RDB_TRX_WAIT;
        g_adptV1Instance.RDBWriteCnt = 0;
        g_adptV1Instance.isNoNeedTryCommit = true;
        return excuteStatus;
    }
    g_adptV1Instance.isNoNeedTryCommit = false;
    if (EmbSimRelOpIsDQL(embCtx->opCode)) {
        return excuteStatus;
    }
    if (excuteStatus == GMERR_OK) {
        if (EmbSimRelOpIsDML(embCtx->opCode) && embCtx->apiCtx != NULL) {
            g_adptV1Instance.RDBWriteCnt += (uint16_t)((SimpRelScanCtxT *)embCtx->apiCtx)->affectRows;
        }
        return SeTrxReleaseSavepoint(session->seInstance, spName);
    } else {
        if (embCtx->isCreateSavePonit) {
            // 需要回滚diff轨迹的状态
            (void)SeTrxRollbackToSavepoint(session->seInstance, spName);
            SimpleRelRollBackHeapTrxOpStat(embCtx);
            (void)SeTrxReleaseSavepoint(session->seInstance, spName);
        }
        return excuteStatus;
    }
    return GMERR_OK;
}

Status SimpleRelationEndTrx(EmbSimpleRunCtxT *embCtx, Status excuteStatus)
{
    SessionT *session = embCtx->session->session;
    SeTrxSavepointNameT spName = {.length = strlen("default"), .name = "default"};
    if (session->isInteractiveTrx) {
        if (EmbSimRelOpIsDQL(embCtx->opCode)) {
            return excuteStatus;
        }
        if (excuteStatus == GMERR_OK && embCtx->isCreateSavePonit) {
            (void)SeTrxReleaseSavepoint(session->seInstance, &spName);
        } else {
            // 需要回滚diff轨迹的状态
            SimpleRelationRollballDiff(embCtx);
            if (embCtx->isCreateSavePonit) {
                (void)SeTrxRollbackToSavepoint(session->seInstance, &spName);
                SimpleRelRollBackHeapTrxOpStat(embCtx);
                (void)SeTrxReleaseSavepoint(session->seInstance, &spName);
            }
            return excuteStatus;
        }
    } else {
        if (embCtx->isZeroRDBConn) {
            return SimpleRelationEndZeroRdbTrx(embCtx, session, &spName, excuteStatus);
        }
        if (excuteStatus == GMERR_OK) {
            excuteStatus = SeTransCommit(session->seInstance);
            DB_ASSERT(excuteStatus == GMERR_OK);
        } else {
            (void)SeTransRollback(session->seInstance, false);
            return excuteStatus;
        }
    }
    return GMERR_OK;
}

static bool SimpleRelIsOpNeedBenginTrans(SimpleRelOpE opCode)
{
    return EmbSimRelOpIsDML(opCode) || EmbSimRelOpIsDQL(opCode);
}

// 调用参考  DataServiceEntryEmbedded  -> SqlServiceEntry / MiniServiceEntry / ProcessExecuteInner
Status SimpleRelationExecute(EmbSimpleRunCtxT *embCtx, Status (*exec)(EmbSimpleRunCtxT *embCtx))
{
    Status ret = GMERR_OK;
    Status execRet = GMERR_OK;
    execRet = ret = SimpleRelConcurrentControlBegin(embCtx);
    if (ret != GMERR_OK) {
        goto EXIT1;
    }
    if (embCtx->isQuickExit) {
        goto EXIT1;
    }
    if (SimpleRelIsOpNeedBenginTrans(embCtx->opCode)) {
        execRet = ret = SimpleRelationPrepareAndBeginTrx(embCtx);
        if (ret != GMERR_OK) {
            goto EXIT2;
        }
    }
    execRet = ret = exec(embCtx);

EXIT2:
    if (SimpleRelIsOpNeedBenginTrans(embCtx->opCode)) {
        ret = SimpleRelationEndTrx(embCtx, execRet);
    }
EXIT1:
    SimpleRelConcurrentControlEnd(embCtx);
    SimpleRelationPrepareClear(embCtx, execRet);
    return ret;
}

Status GmeSimpleRelGetWrapperMemCtx(GmeConnT *conn, DbMemCtxT **memCtx)
{
    if (conn == NULL || memCtx == NULL || conn->embSession == NULL || conn->embSession->embSimpleRunCtx == NULL) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_NULLPTR, "The parameter has null pointer when get wrapper memctx.");
        return VOS_ERRNO_DB_NULLPTR;
    }

    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    *memCtx = embCtx->simpleRelMgr->wrapperMemCtx;
    return GMERR_OK;
}

static Status SimpleRelReserveTupleBuf4IdxCtx(SimRelLabelCursorT *labelCursor, IndexCtxT *idxCtx)
{
    if (SECUREC_LIKELY(labelCursor->hpHandle->hpControl.isFixPage)) {
        return GMERR_OK;
    }

    // 大对象场景需要预留内存缓存索引中间结果 (FetchTupleBufByTTree)，比较时需要存储两条
    uint32_t size = IDX_RESERVE_TWO_BUF_LEN;
    if (size > idxCtx->tupleBuf.cap) {
        TupleBufInit(&idxCtx->tupleBuf, labelCursor->memCtx);
        Status ret = TupleBufReserve(&idxCtx->tupleBuf, size);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "Unable to alloc tuplebuf when idx open");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }
    }
    idxCtx->bigObjBuf = idxCtx->tupleBuf.buf + IDX_RESERVE_BUF_LEN;
    return GMERR_OK;
}

Status SimpleRelOpenAndInitOneIdx(
    SeRunCtxHdT seRunCtx, SimRelLabelCursorT *labelCursor, DmVlIndexLabelT *index, IndexCtxT **idxCtx)
{
    Status ret = GMERR_OK;
    // 参考 OpenSecIdx
    IndexOpenCfgT idxOpenCfg = (IndexOpenCfgT){
        .seRunCtx = seRunCtx,
        .vertex = NULL,
        .heapHandle = labelCursor->hpHandle,
        .callbackFunc = {0},
        .indexLabel = (DmIndexLabelBaseT *)index,
        .userData = NULL,
        .keyInfo = NULL,
        .needCheckIndexSatisfied = false,
        .vertexLabel = labelCursor->vertexLabel,
        .indexType = index->idxLabelBase.indexType,
    };

    if (SECUREC_UNLIKELY(*idxCtx == NULL)) {
        // indexAlloc 函数要求直连读场景传入INDEX_TYPE_MAX，防止再次Open时，之前alloc的index长度不够用
        if ((ret = IdxAlloc(seRunCtx, INDEX_TYPE_MAX, idxCtx)) != GMERR_OK) {
            DB_LOG_ERROR(ret, "Alloc idxCtx worthless when simprel open sec idx.");
            return ret;
        }
    }

    ret = IdxOpen(index->idxLabelBase.shmAddr, &idxOpenCfg, *idxCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Open idxCtx worthless when simprel open sec idx.");
        IdxRelease(*idxCtx);
        *idxCtx = NULL;
        return ret;
    }

    return SimpleRelReserveTupleBuf4IdxCtx(labelCursor, *idxCtx);
}

ALWAYS_INLINE Status SimpleRelCopyOneHandleCondValue(
    uint16_t typeId, uint8_t *srcCondBuf, uint32_t condVarLen, uint8_t *dstCondBuf)
{
    errno_t err = EOK;
    switch (typeId) {
        case DBT_STRING:
        case DBT_MIBSTR: {
            err = strncpy_s((char *)dstCondBuf, condVarLen, (char *)srcCondBuf, (condVarLen - 1));
            if (err != EOK) {
                DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_STROPER_FAILURE, "Unable to store string cond value.");
                return VOS_ERRNO_DB_STROPER_FAILURE;
            }
            dstCondBuf[condVarLen - 1] = '\0';
            break;
        }
        case DBT_BYTES:
        case DBT_VBYTES: {
            err = memcpy_s(dstCondBuf, condVarLen, srcCondBuf, *(uint16_t *)srcCondBuf + DB_BYTEHEAD_LEN);
            if (err != EOK) {
                DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMCPY_FAILURE, "Unable to store memcpy cond value.");
                return VOS_ERRNO_DB_MEMCPY_FAILURE;
            }
            break;
        }
        default: {
            err = memcpy_s(dstCondBuf, condVarLen, srcCondBuf, condVarLen);
            if (err != EOK) {
                DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMCPY_FAILURE, "Unable to memcpy cond value.");
                return VOS_ERRNO_DB_MEMCPY_FAILURE;
            }
            break;
        }
    }
    return GMERR_OK;
}

static Status SimpleRelInitOneHandleCond(
    EmbSimpleCondItemT *queryCond, DmPropertySchemaT *properties, DB_CONDITEM_STRU *pCond, uint8_t **condBufCursor)
{
    uint16_t condVarLen;
    uint8_t *condValue = NULL;
    if (SECUREC_LIKELY(properties[pCond->ucFieldId].size <= DB_ELELEN_MAX)) {
        condVarLen = DB_ELELEN_MAX;
        condValue = pCond->aucValue;
    } else {
        condVarLen = properties[pCond->ucFieldId].size;
        *(VOS_UINTPTR *)&condValue = *(VOS_UINTPTR *)pCond->aucValue;
    }

    uint16_t typeId = properties[pCond->ucFieldId].customTypeId;
    Status ret = SimpleRelCopyOneHandleCondValue(typeId, condValue, condVarLen, *condBufCursor);
    if (ret != GMERR_OK) {
        return ret;
    }

    queryCond->enOp = (GME_CMPTYPE_ENUM)pCond->enOp;
    queryCond->propId = pCond->ucFieldId;
    queryCond->fldOffset = properties[pCond->ucFieldId].offset;
    queryCond->propeMaxLen = (uint32_t)properties[pCond->ucFieldId].propeDefLen;
    queryCond->v1Type = properties[pCond->ucFieldId].customTypeId;
    queryCond->v5Type = properties[pCond->ucFieldId].dataType;
    queryCond->value = *condBufCursor;
    queryCond->valueForHandl = *condBufCursor;
    *condBufCursor += condVarLen;
    return GMERR_OK;
}

inline static void SimpleRelSetResumeIdxCondValue(DmPropertySchemaT *properties, uint32_t *propIds, uint32_t popeNum,
    uint8_t **condBufCursor, SimpRelCondItemT *resumeCond)
{
    for (uint32_t i = 0; i < popeNum; i++, resumeCond++) {
        // 其他参数在IdxUpdateResumeCond更新索引条件时添加
        resumeCond->value = *condBufCursor;
        resumeCond->valueForHandl = *condBufCursor;
        *condBufCursor += SimpRelGetCondVarLen(properties, propIds[i]);
    }
}

static Status SimpRelInitHandConds(
    DmVertexLabelT *vertexLabel, DB_COND_STRU *cond, IdxMatchStruT *idxMatchStru, SimpRelHandleCondT *handleCond)
{
    if (cond->usCondNum == 0 && !idxMatchStru->isMatch) {  // sort handle可能有索引另外添加的查询条件
        handleCond->filterCondNum = 0;
        handleCond->idxCondNum = 0;
        handleCond->isMatchedIdx = false;
        handleCond->idxResumeCondNum = 0;
        return GMERR_OK;
    }

    Status ret = GMERR_OK;
    uint8_t *condBufCursor = handleCond->allCondValueBuf;
    uint32_t filterCondNum = cond->usCondNum - idxMatchStru->idxCondNum;
    handleCond->idxConds = (EmbSimpleCondItemT *)handleCond->filterConds + filterCondNum;
    DmPropertySchemaT *properties = vertexLabel->metaVertexLabel->schema->properties;
    DB_CONDITEM_STRU *pCond = cond->aCond;
    for (uint32_t i = 0; i < cond->usCondNum; i++, pCond++) {
        // 非索引条件
        if (idxMatchStru->idxConds[i] == DB_INVALID_UINT8) {
            EmbSimpleCondItemT *queryCond = &handleCond->filterConds[handleCond->filterCondNum];
            ret = SimpleRelInitOneHandleCond(queryCond, properties, pCond, &condBufCursor);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
            handleCond->filterCondNum++;
        } else {
            EmbSimpleCondItemT *idxCond = &handleCond->idxConds[idxMatchStru->idxConds[i]];
            ret = SimpleRelInitOneHandleCond(idxCond, properties, pCond, &condBufCursor);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
            handleCond->idxCondNum++;
        }
    }

    if (idxMatchStru->isMatch) {
        DmVlIndexLabelT *secIndex = &vertexLabel->metaVertexLabel->secIndexes[idxMatchStru->idxId];
        handleCond->resumeConds = &handleCond->idxConds[handleCond->idxCondNum];
        SimpleRelSetResumeIdxCondValue(
            properties, secIndex->propIds, secIndex->propeNum, &condBufCursor, handleCond->resumeConds);
    }

    handleCond->isMatchedIdx = idxMatchStru->isMatch;
    handleCond->bestIdxId = idxMatchStru->idxId;
    handleCond->idxResumeCondNum = 0;
    handleCond->isFirstFetch = true;
    return GMERR_OK;
}

static void SimpleRelCalcNeedCondCntAndCondBufLen(DmVertexLabelT *vertexLabel, DB_COND_STRU *cond,
    IdxMatchStruT *idxMatchStru, uint32_t *condCnt, uint32_t *condBufLen)
{
    DmPropertySchemaT *properties = vertexLabel->metaVertexLabel->schema->properties;
    DB_CONDITEM_STRU *pCondItem = &cond->aCond[0];
    uint32_t needCondCnt = cond->usCondNum, condValueBufLen = 0;
    for (uint32_t i = 0; i < cond->usCondNum; i++, pCondItem++) {
        condValueBufLen += SimpRelGetCondVarLen(properties, pCondItem->ucFieldId);
    }

    if (idxMatchStru->isMatch) {
        // match到索引，多申请当前索引拥有的字段个数的条件数用于恢复查询
        DmVlIndexLabelT *secIndex = &vertexLabel->metaVertexLabel->secIndexes[idxMatchStru->idxId];
        needCondCnt += secIndex->propeNum;
        for (uint32_t i = 0; i < secIndex->propeNum; i++) {
            condValueBufLen += SimpRelGetCondVarLen(properties, secIndex->propIds[i]);
        }
    }
    *condCnt = needCondCnt;
    *condBufLen = condValueBufLen;
}

static Status SimpleRelAllocHandleConds(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, DB_COND_STRU *cond,
    IdxMatchStruT *idxMatchStru, SimpRelHandleCondT **handleCond)
{
    SimpRelHandleCondT *handleCondTmp = DbDynMemCtxAlloc(memCtx, sizeof(SimpRelHandleCondT));
    if (handleCondTmp == NULL) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "unbable to alloc filterConds");
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }
    (void)memset_s(handleCondTmp, sizeof(SimpRelHandleCondT), 0x00, sizeof(SimpRelHandleCondT));

    uint32_t needCondCnt, allCondValueBuf;
    SimpleRelCalcNeedCondCntAndCondBufLen(vertexLabel, cond, idxMatchStru, &needCondCnt, &allCondValueBuf);
    if (needCondCnt != 0) {
        handleCondTmp->filterConds = DbDynMemCtxAlloc(memCtx, sizeof(SimpRelCondItemT) * needCondCnt);
        if (handleCondTmp->filterConds == NULL) {
            DbDynMemCtxFree(memCtx, handleCondTmp);
            DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "unbable to alloc filterConds");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }

        handleCondTmp->allCondValueBuf = DbDynMemCtxAlloc(memCtx, allCondValueBuf);
        if (handleCondTmp->allCondValueBuf == NULL) {
            DbDynMemCtxFree(memCtx, handleCondTmp->filterConds);
            DbDynMemCtxFree(memCtx, handleCondTmp);
            DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "unbable to alloc all cond value buf.");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }
        (void)memset_s(handleCondTmp->allCondValueBuf, allCondValueBuf, 0x00, allCondValueBuf);
    }

    *handleCond = handleCondTmp;
    return GMERR_OK;
}

inline static Status SimpRelAllocAndInitHandleConds(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, DB_COND_STRU *cond,
    IdxMatchStruT *idxMatchStru, SimpRelHandleCondT **handleCond)
{
    Status ret = SimpleRelAllocHandleConds(memCtx, vertexLabel, cond, idxMatchStru, handleCond);
    if (ret != GMERR_OK) {
        return ret;
    }

    return SimpRelInitHandConds(vertexLabel, cond, idxMatchStru, *handleCond);
}

inline static void SimpRelSetMatchedCond(uint32_t cmpOp, uint8_t condIdx, uint8_t *matchedIdx)
{
    if (cmpOp == DB_OP_EQUAL) {
        *matchedIdx = condIdx;
        return;
    } else if (*matchedIdx == DB_INVALID_UINT8 && (cmpOp >= DB_OP_LESS) && (cmpOp <= DB_OP_LARGEREQUAL)) {
        *matchedIdx = condIdx;
    }
    return;
}

static void SimpRelMatchIndexByOrder(
    VlabelCtrlT *vLabelCtrl, uint8_t *sortItems, uint8_t sortCnt, IdxMatchStruT *idxMatchStru)
{
    DmVlIndexLabelT *secIndex = vLabelCtrl->vertexLabel->metaVertexLabel->secIndexes;
    uint32_t indexNum = vLabelCtrl->vertexLabel->metaVertexLabel->secIndexNum;
    for (uint32_t i = 0; i < indexNum; i++, secIndex++) {
        if (SECUREC_UNLIKELY(vLabelCtrl->originDefInfo->pstIdxLst[i].enIndexType == DBDDL_INDEXTYPE_HASH)) {
            continue;
        }

        uint32_t fldNumOfIdx = secIndex->propeNum;
        uint8_t matchSortCnt = 0;
        for (uint32_t fldIdx = 0; fldIdx < fldNumOfIdx; fldIdx++) {
            uint32_t fldId = secIndex->propIds[fldIdx];
            if (sortItems[fldId] != fldIdx) {
                break;
            }
            matchSortCnt++;
        }

        // 要求排序字段与索引字段顺序和数量完全相同
        if (matchSortCnt == sortCnt) {
            idxMatchStru->idxId = i;
            idxMatchStru->isMatch = true;
            break;
        }
    }
}

static void SimpleRelSelectIndexCondsByOrder(DB_SORT_STRU *sort, DB_COND_STRU *conds, IdxMatchStruT *idxMatchStru)
{
    (void)memset_s(idxMatchStru->idxConds, DB_COND_MAX, DB_INVALID_UINT8, DB_COND_MAX);
    idxMatchStru->idxCondNum = 0;

    for (uint32_t sortIdx = 0; sortIdx < sort->ucSortNum; sortIdx++) {
        uint8_t sortFldId = sort->pSortFields[sortIdx];
        uint8_t matchedIdx = DB_INVALID_UINT8;

        for (uint32_t condIdx = 0; condIdx < conds->usCondNum; condIdx++) {
            // 排序字段与条件字段相同, 且条件为索引支持的条件(EQ优先级最高), matchedIdx为条件下标
            if (sortFldId == conds->aCond[condIdx].ucFieldId) {
                SimpRelSetMatchedCond(conds->aCond[condIdx].enOp, (uint8_t)condIdx, &matchedIdx);
            }
        }

        if (matchedIdx == DB_INVALID_UINT8) {
            break;
        }

        // 记录索引上的条件id, (数组下标为条件id, 值为0xff即代表是不是索引条件)
        if (idxMatchStru->idxConds[matchedIdx] == DB_INVALID_UINT8) {
            idxMatchStru->idxConds[matchedIdx] = idxMatchStru->idxCondNum;
            idxMatchStru->idxCondNum++;
        }

        if (conds->aCond[matchedIdx].enOp != DB_OP_EQUAL) {
            break;
        }
    }
}

static Status SimpleRelSelectIndexByOrder(
    VlabelCtrlT *vLabelCtrl, DB_SORT_STRU *sort, DB_COND_STRU *conds, IdxMatchStruT *idxMatchStru)
{
    idxMatchStru->isMatch = false;
    uint32_t indexNum = vLabelCtrl->vertexLabel->metaVertexLabel->secIndexNum;
    if (indexNum > 0) {
        uint8_t sortCnt = 0;
        VOS_UINT8 sortItems[DB_FLDOFREL_MAX];  // 排序字段去重
        (void)memset_s(sortItems, DB_FLDOFREL_MAX, DB_INVALID_UINT8, DB_FLDOFREL_MAX);
        for (uint32_t i = 0; i < sort->ucSortNum; i++) {
            if (sortItems[sort->pSortFields[i]] == DB_INVALID_UINT8) {
                sortItems[sort->pSortFields[i]] = sortCnt;
                sortCnt++;
            }
        }
        SimpRelMatchIndexByOrder(vLabelCtrl, sortItems, sortCnt, idxMatchStru);
    }

    if (idxMatchStru->isMatch == false) {
        DB_LOG_AND_SET_LASERR(
            VOS_ERRNO_DB_INVALIDIDX_ON_SORTFIELD, "No TTREE index selected for the given sort fields.");
        return VOS_ERRNO_DB_INVALIDIDX_ON_SORTFIELD;
    }

    // 排序字段的序列与查询条件字段序列的前缀匹配
    SimpleRelSelectIndexCondsByOrder(sort, conds, idxMatchStru);
    return GMERR_OK;
}

static Status GetAndValidNspCtrl(DbListT *nspCtrlList, const uint32_t nspId, NspCtrlT **nspCtrl, bool isTpc)
{
    *nspCtrl = SimpleRelGetNspCtrlById(nspCtrlList, nspId, NULL);
    if ((*nspCtrl) == NULL) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALID_DATABASE, "Incorrect DB ID (%" PRIu32 ").", nspId);
        return VOS_ERRNO_DB_INVALID_DATABASE;
    }
    if (isTpc && !(*nspCtrl)->isTpcDb) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALID_DATABASE, "DB is not tpc db.");
        return VOS_ERRNO_DB_INVALID_DATABASE;
    }
    return GMERR_OK;
}

Status GmeSimpleRelCheckScanCtxAndInitHandleConds(
    GmeConnT *conn, SimpRelScanCtxT *scanCtx, DbMemCtxT *memCtx, SimpRelHandleCondT **handleCond, bool isTpc)
{
    DB_POINTER2(scanCtx, handleCond);
    Status ret = GMERR_OK;
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    embCtx->nspId = scanCtx->dbId;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(memCtx);
    EmbSimpRelMgrT *simpleRelMgr = (EmbSimpRelMgrT *)dbInstance->simpleRelMgr;
    DbRWLatchR(&simpleRelMgr->latch);
    NspCtrlT *nspCtrl = NULL;
    ret = GetAndValidNspCtrl(&simpleRelMgr->nspCtrlList, scanCtx->dbId, &nspCtrl, isTpc);
    if (ret != GMERR_OK) {
        DbRWUnlatchR(&simpleRelMgr->latch);
        return ret;
    }
    DbRWLatchR(&nspCtrl->latch);
    if (nspCtrl->openCnt == 0) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_DATABASE_NOT_OPENED, "Namespace %s dose not opened.", nspCtrl->nspName);
        ret = VOS_ERRNO_DB_DATABASE_NOT_OPENED;
        goto RELEASE1;
    }
    embCtx->nspCtrl = nspCtrl;
    VlabelCtrlT *vLabelCtrl = NULL;
    ret = SimpleRelGetVlabelCtrlById(&nspCtrl->vLabelCtrlList, scanCtx->relId, NULL, &vLabelCtrl, embCtx);
    if (ret != GMERR_OK) {
        goto RELEASE1;
    }
    LabelRLatchAcquire(vLabelCtrl->latch);
    if ((ret = SimpleRelCheckScanCtx(scanCtx, vLabelCtrl, false, isTpc)) != GMERR_OK) {
        goto RELEASE;
    }
    IdxMatchStruT idxMatchStru = {.idxId = DB_INVALID_UINT8, .idxCondNum = 0, .isMatch = false};
    if (scanCtx->sort != NULL && scanCtx->sort->ucSortNum > 0) {
        ret = SimpleRelSelectIndexByOrder(vLabelCtrl, scanCtx->sort, scanCtx->cond, &idxMatchStru);
        if (ret != GMERR_OK) {
            goto RELEASE;
        }
    } else {
        SimpleRelSelectIndex(vLabelCtrl, scanCtx->cond, &idxMatchStru, false, nspCtrl->isTpcDb);
    }

    ret = SimpRelAllocAndInitHandleConds(memCtx, vLabelCtrl->vertexLabel, scanCtx->cond, &idxMatchStru, handleCond);
RELEASE:
    LabelRLatchRelease(vLabelCtrl->latch);
RELEASE1:
    DbRWUnlatchR(&nspCtrl->latch);
    DbRWUnlatchR(&simpleRelMgr->latch);
    return ret;
}

static inline uint32_t GetEdgeRelIndex(EdgeCtrlT *edgeCtrl, uint16_t relId)
{
    return edgeCtrl->relId[0] == relId ? 0 : 1;
}

Status SimpRelInitProjectInfo(DmVertexLabelT *vertexLabel, DbMemCtxT *memCtx, const DB_FIELDFILTER_STRU *fldFilter,
    SimpRelSelectTopoResultT *result)
{
    if (fldFilter->ucFieldNum == DB_FIELD_ALL) {
        result->projectInfo = NULL;
        result->projectInfoNum = 0;
        result->projectLen = vertexLabel->metaVertexLabel->schema->maxRecLen - EXTERN_DATA_BUF_LEN;
        return GMERR_OK;
    }
    uint32_t len = 0;
    SimpRelTopoProjectInfoT *info = DbDynMemCtxAlloc(memCtx, sizeof(SimpRelTopoProjectInfoT) * fldFilter->ucFieldNum);
    if (info == NULL) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "unbable to alloc project info");
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }
    DmPropertySchemaT *properties = vertexLabel->metaVertexLabel->schema->properties;
    for (uint32_t i = 0; i < fldFilter->ucFieldNum; i++) {
        info[i].offset = properties[fldFilter->aucField[i]].offset;
        info[i].size = (uint32_t)properties[fldFilter->aucField[i]].size;
        len += info[i].size;
    }
    result->projectInfo = info;
    result->projectInfoNum = fldFilter->ucFieldNum;
    result->projectLen = len;
    return GMERR_OK;
}

void ResetResumeDataInfo(AdptTopoHandleInfoItemT *info)
{
    info->resumeDataInfo.addr32.pageIdAndSlotId = DB_INVALID_UINT32;
    info->resumeDataInfo.hasModify = false;
    info->resumeDataInfo.buf = NULL;
}

inline static uint32_t SimpRelCaclEdgeNeedBufLen(DmPropertySchemaT *properties, EdgeRelationInfoT *edgeInfo)
{
    uint32_t needBufLen = 0;
    for (uint32_t i = 0; i < edgeInfo->fieldNum; i++) {
        needBufLen += SimpRelGetCondVarLen(properties, edgeInfo->fields[i]);
    }
    return needBufLen * EDGE_HANDLE_BUFLEN_DOUBEL;
}

static Status SimpleRelAllocEdgeHandleCond(DbMemCtxT *memCtx, DmPropertySchemaT *properties,
    EdgeRelationInfoT *edgeInfo, AdptTopoHandleInfoItemT *info, SimpRelHandleCondT *handleCond)
{
    uint32_t needCondCnt = edgeInfo->fieldNum * EDGE_HANDLE_BUFLEN_DOUBEL;
    handleCond->filterConds = DbDynMemCtxAlloc(memCtx, sizeof(SimpRelCondItemT) * needCondCnt);
    if (handleCond->filterConds == NULL) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "unbable to alloc filterConds");
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }

    uint32_t needBufLen = SimpRelCaclEdgeNeedBufLen(properties, edgeInfo);
    handleCond->allCondValueBuf = DbDynMemCtxAlloc(memCtx, needBufLen);
    if (handleCond->allCondValueBuf == NULL) {
        DbDynMemCtxFree(memCtx, handleCond->filterConds);
        handleCond->filterConds = NULL;
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "unbable to alloc cond value buf");
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }

    info->preIdxConds = DbDynMemCtxAlloc(memCtx, sizeof(SimpRelTopoIdxCondT) * edgeInfo->fieldNum);
    if (info->preIdxConds == NULL) {
        DbDynMemCtxFree(memCtx, handleCond->allCondValueBuf);
        DbDynMemCtxFree(memCtx, handleCond->filterConds);
        handleCond->allCondValueBuf = NULL;
        handleCond->filterConds = NULL;
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "unbable to alloc preIdxConds");
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }
    return GMERR_OK;
}

Status SimpRelInitTopoHandleCondByEdge(VlabelCtrlT *vLabelCtrl, EdgeCtrlT *edgeCtrl, DB_EDGE_CON_STRU *edge,
    DbMemCtxT *memCtx, AdptTopoHandleInfoItemT *info)
{
    uint32_t dstIdx = GetEdgeRelIndex(edgeCtrl, edge->edgeInfo[1].relId);
    uint32_t srcidx = (dstIdx + 1) % 2;
    EdgeRelationInfoT *edgeInfo = &edgeCtrl->relInfo[dstIdx];
    EdgeRelationInfoT *srcEdgeInfo = &edgeCtrl->relInfo[srcidx];
    SimpRelHandleCondT *handleCond = &info->handleCond;
    DmPropertySchemaT *properties = vLabelCtrl->vertexLabel->metaVertexLabel->schema->properties;
    Status ret = SimpleRelAllocEdgeHandleCond(memCtx, properties, edgeInfo, info, handleCond);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint8_t *bufCursor = handleCond->allCondValueBuf;
    handleCond->idxConds = (EmbSimpleCondItemT *)handleCond->filterConds;
    SimpRelCondItemT *pIdxCond = handleCond->idxConds;
    uint32_t propIds[DB_IDX_FLD_MAX];
    for (uint32_t i = 0; i < edgeInfo->fieldNum; i++, pIdxCond++) {
        pIdxCond->enOp = GME_CMP_EQUAL;
        pIdxCond->propId = edgeInfo->fields[i];
        uint32_t size = (uint32_t)properties[edgeInfo->fields[i]].size;
        pIdxCond->fldOffset = properties[edgeInfo->fields[i]].offset;
        pIdxCond->propeMaxLen = size;
        pIdxCond->valueForHandl = bufCursor;
        bufCursor += SimpRelGetCondVarLen(properties, edgeInfo->fields[i]);
        propIds[i] = edgeInfo->fields[i];
        info->preIdxConds[i].fldOffset = properties[srcEdgeInfo->fields[i]].offset;
        info->preIdxConds[i].propeMaxLen = size;
        info->preIdxConds[i].type = (uint32_t)properties[edgeInfo->fields[i]].dataType;
        info->preIdxConds[i].typeId = properties[edgeInfo->fields[i]].customTypeId;
    }

    SimpleRelSetResumeIdxCondValue(properties, propIds, edgeInfo->fieldNum, &bufCursor, pIdxCond);
    handleCond->filterCondNum = 0;
    handleCond->idxCondNum = edgeInfo->fieldNum;
    handleCond->isMatchedIdx = true;
    handleCond->bestIdxId = edgeInfo->indexId;
    handleCond->idxResumeCondNum = 0;
    handleCond->resumeConds = pIdxCond;  // 额外条件在索引条件后面
    handleCond->isFirstFetch = true;
    return GMERR_OK;
}

void SimpRelFreeHandleInfo(DbMemCtxT *memCtx, AdptTopoHandleInfoItemT *info)
{
    if (info->handleCond.filterConds != NULL) {
        DbDynMemCtxFree(memCtx, info->handleCond.filterConds);
        info->handleCond.filterConds = NULL;
    }
    if (info->handleCond.allCondValueBuf != NULL) {
        DbDynMemCtxFree(memCtx, info->handleCond.allCondValueBuf);
        info->handleCond.allCondValueBuf = NULL;
    }
    if (info->preIdxConds != NULL) {
        DbDynMemCtxFree(memCtx, info->preIdxConds);
        info->preIdxConds = NULL;
    }
    if (info->result.projectInfo != NULL) {
        DbDynMemCtxFree(memCtx, info->result.projectInfo);
        info->result.projectInfo = NULL;
    }
}

Status SimpRelInitHandleResult(
    VlabelCtrlT *vLabelCtrl, DbMemCtxT *memCtx, DB_FIELDFILTER_STRU *fldFilter, AdptTopoHandleInfoItemT *info)
{
    Status ret = SimpRelInitProjectInfo(vLabelCtrl->vertexLabel, memCtx, fldFilter, &info->result);
    if (ret != GMERR_OK) {
        return ret;
    }

    info->result.memCtx = memCtx;
    info->result.recLen = vLabelCtrl->vertexLabel->metaVertexLabel->schema->maxRecLen - EXTERN_DATA_BUF_LEN;
    info->result.isScanFinish = true;
    DbCreateListWithExtendSize(&info->result.recList, sizeof(SimpRelTopoRecItemT), RECLIST_EXTEND_LEN, memCtx);
    return GMERR_OK;
}

static void SimpRelSetDefaultSelectHdlBeginAddr(HpTupleCombineAddrT *beginAddr)
{
    beginAddr->tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    beginAddr->blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    beginAddr->pageDeviceId = DB_INVALID_UINT32;
    beginAddr->pageBlockId = DB_INVALID_UINT32;
}

Status SimpRelInitTopoHandByEdgeInfo(VlabelCtrlT *vLabelCtrl, DbMemCtxT *memCtx, EdgeCtrlT *edgeCtrl,
    DB_EDGE_CON_STRU *edge, AdptTopoHandleInfoItemT *info)
{
    uint32_t propNum = vLabelCtrl->originDefInfo->ulNCols;
    Status ret = GMERR_OK;
    if (edge->pstFldFilter != NULL && edge->pstFldFilter->ucFieldNum != DB_FIELD_ALL) {
        ret = SimpRelCheckFldFilter(edge->pstFldFilter, propNum);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    ret = SimpRelInitHandleResult(vLabelCtrl, memCtx, edge->pstFldFilter, info);
    if (ret != GMERR_OK) {
        SimpRelFreeHandleInfo(memCtx, info);
        return ret;
    }

    ret = SimpRelInitTopoHandleCondByEdge(vLabelCtrl, edgeCtrl, edge, memCtx, info);
    if (ret != GMERR_OK) {
        SimpRelFreeHandleInfo(memCtx, info);
        return ret;
    }
    info->tableId = edge->edgeInfo[1].relId;
    ResetResumeDataInfo(info);
    SimpRelSetDefaultSelectHdlBeginAddr(&info->beginAddr);
    return GMERR_OK;
}

Status SimpRelInitTopoHandByEdge(EmbSimpleRunCtxT *embCtx, NspCtrlT *nspCtrl, DbMemCtxT *memCtx, DB_EDGE_CON_STRU *edge,
    AdptTopoHandleInfoItemT *info)
{
    for (uint32_t i = 0; i < DbListGetItemCnt(&nspCtrl->edgeCtrlList); i++) {
        EdgeCtrlT *edgeCtrl = DbListItem(&nspCtrl->edgeCtrlList, i);
        if (!isEdgeDefMatch(edgeCtrl, edge->edgeInfo)) {
            continue;
        }
        VlabelCtrlT *vLabelCtrl = NULL;
        Status ret =
            SimpleRelGetVlabelCtrlById(&nspCtrl->vLabelCtrlList, edge->edgeInfo[1].relId, NULL, &vLabelCtrl, embCtx);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(
                VOS_ERRNO_DB_INVALIDREL, "Incorrect relation ID (%" PRIu16 ").", edge->edgeInfo[1].relId);
            return VOS_ERRNO_DB_INVALIDREL;
        }
        LabelRLatchAcquire(vLabelCtrl->latch);
        ret = SimpRelInitTopoHandByEdgeInfo(vLabelCtrl, memCtx, edgeCtrl, edge, info);
        LabelRLatchRelease(vLabelCtrl->latch);
        return ret;
    }
    DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_FAILURE, "edge not exist");
    return VOS_ERRNO_DB_FAILURE;
}

Status SimpRelAllocTopoHandle(DbMemCtxT *memCtx, AdptTopoHandleInfoT **topoHandle)
{
    AdptTopoHandleInfoT *handle = (AdptTopoHandleInfoT *)DbDynMemCtxAlloc(memCtx, sizeof(AdptTopoHandleInfoT));
    if (handle == NULL) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "unbable to alloc topo handle");
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }
    uint32_t allocSize = sizeof(AdptTopoHandleInfoItemT) * (DB_PATH_EDGE_MAX_NUM + 1);
    handle->info = DbDynMemCtxAlloc(memCtx, allocSize);
    if (handle->info == NULL) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "unbable to alloc handle items");
        DbDynMemCtxFree(memCtx, handle);
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }
    (void)memset_s(handle->info, allocSize, 0x00, allocSize);
    handle->memCtx = memCtx;
    handle->currentIdx = 0;
    handle->maxNum = DB_PATH_EDGE_MAX_NUM + 1;
    *topoHandle = handle;
    return GMERR_OK;
}

void SimpRelReleaseTopoHandle(AdptTopoHandleInfoT *topoHandle)
{
    for (uint32_t i = 0; i < topoHandle->maxNum; i++) {
        SimpRelFreeHandleInfo(topoHandle->memCtx, &topoHandle->info[i]);
    }
    DbDynMemCtxFree(topoHandle->memCtx, topoHandle->info);
    DbDynMemCtxFree(topoHandle->memCtx, topoHandle);
}

Status SimpRelAllocfilterConds(VlabelCtrlT *vLabelCtrl, DbMemCtxT *memCtx, SimpRelTopoScanCtxT *scanCtx,
    IdxMatchStruT idxMatchStru, AdptTopoHandleInfoItemT *info)
{
    SimpRelHandleCondT *handleCond = &info->handleCond;
    uint32_t needCondCnt, allCondValueBuf;
    SimpleRelCalcNeedCondCntAndCondBufLen(
        vLabelCtrl->vertexLabel, scanCtx->cond, &idxMatchStru, &needCondCnt, &allCondValueBuf);
    if (needCondCnt != 0) {
        handleCond->filterConds = DbDynMemCtxAlloc(memCtx, sizeof(SimpRelCondItemT) * needCondCnt);
        if (handleCond->filterConds == NULL) {
            DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "unbable to alloc filterConds");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }

        handleCond->allCondValueBuf = DbDynMemCtxAlloc(memCtx, allCondValueBuf);
        if (handleCond->allCondValueBuf == NULL) {
            DbDynMemCtxFree(memCtx, handleCond->filterConds);
            handleCond->filterConds = NULL;
            DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "unbable to alloc all cond value buf.");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }
    }
    return GMERR_OK;
}

Status SimpRelInitSrcLabelHandle(EmbSimpleRunCtxT *embCtx, NspCtrlT *nspCtrl, SimpRelTopoScanCtxT *scanCtx,
    DbMemCtxT *memCtx, AdptTopoHandleInfoT *handle)
{
    VlabelCtrlT *vLabelCtrl = NULL;
    Status ret = SimpleRelGetVlabelCtrlById(&nspCtrl->vLabelCtrlList, scanCtx->relId, NULL, &vLabelCtrl, embCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    LabelRLatchAcquire(vLabelCtrl->latch);

    uint32_t propNum = vLabelCtrl->vertexLabel->metaVertexLabel->schema->propeNum;
    IdxMatchStruT idxMatchStru = {.idxId = DB_INVALID_UINT8, .idxCondNum = 0, .isMatch = false};
    if (scanCtx->cond != NULL && scanCtx->cond->usCondNum != 0) {
        ret = SimpRelCheckCond(scanCtx->cond, propNum, vLabelCtrl->originDefInfo->pstFldLst, nspCtrl->isTpcDb);
        if (ret != GMERR_OK) {
            goto EXIT;
        }
        SimpleRelSelectIndex(vLabelCtrl, scanCtx->cond, &idxMatchStru, false, nspCtrl->isTpcDb);
    }
    if (scanCtx->fldFilter != NULL && scanCtx->fldFilter->ucFieldNum != DB_FIELD_ALL) {
        ret = SimpRelCheckFldFilter(scanCtx->fldFilter, propNum);
        if (ret != GMERR_OK) {
            goto EXIT;
        }
    }

    AdptTopoHandleInfoItemT *info = handle->info;
    ret = SimpRelAllocfilterConds(vLabelCtrl, memCtx, scanCtx, idxMatchStru, info);
    if (ret != GMERR_OK) {
        SimpRelFreeHandleInfo(memCtx, info);
        goto EXIT;
    }

    ret = SimpRelInitHandConds(vLabelCtrl->vertexLabel, scanCtx->cond, &idxMatchStru, &info->handleCond);
    if (ret != GMERR_OK) {
        SimpRelFreeHandleInfo(memCtx, info);
        goto EXIT;
    }

    ret = SimpRelInitHandleResult(vLabelCtrl, memCtx, scanCtx->fldFilter, info);
    if (ret != GMERR_OK) {
        SimpRelFreeHandleInfo(memCtx, info);
        goto EXIT;
    }
    info->tableId = scanCtx->relId;
    ResetResumeDataInfo(info);
    SimpRelSetDefaultSelectHdlBeginAddr(&info->beginAddr);
EXIT:
    LabelRLatchRelease(vLabelCtrl->latch);
    return ret;
}

Status GmeSimpleInitTopoHandleConds(
    GmeConnT *conn, SimpRelTopoScanCtxT *scanCtx, DbMemCtxT *memCtx, AdptTopoHandleInfoT **handleCond)
{
    DB_POINTER3(conn, scanCtx, handleCond);
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    EmbSimpRelMgrT *simpleRelMgr = embCtx->simpleRelMgr;
    DbRWLatchR(&simpleRelMgr->latch);
    NspCtrlT *nspCtrl = SimpleRelGetNspCtrlById(&simpleRelMgr->nspCtrlList, scanCtx->dbId, NULL);
    if (nspCtrl == NULL) {
        DbRWUnlatchR(&simpleRelMgr->latch);
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALID_DATABASE, "Incorrect DB ID (%" PRIu32 ").", scanCtx->dbId);
        return VOS_ERRNO_DB_INVALID_DATABASE;
    }
    if (nspCtrl->openCnt == 0) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_DATABASE_NOT_OPENED, "Namespace %s dose not opened.", nspCtrl->nspName);
        DbRWUnlatchR(&simpleRelMgr->latch);
        return VOS_ERRNO_DB_DATABASE_NOT_OPENED;
    }
    DbRWLatchR(&nspCtrl->latch);
    embCtx->nspCtrl = nspCtrl;
    AdptTopoHandleInfoT *newTopoHandle = NULL;
    Status ret = SimpRelAllocTopoHandle(memCtx, &newTopoHandle);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }

    ret = SimpRelInitSrcLabelHandle(embCtx, nspCtrl, scanCtx, memCtx, newTopoHandle);
    if (ret != GMERR_OK) {
        SimpRelReleaseTopoHandle(newTopoHandle);
        goto RELEASE;
    }

    for (uint32_t i = 0; i < scanCtx->pstPath->edgeNum; i++) {
        ret =
            SimpRelInitTopoHandByEdge(embCtx, nspCtrl, memCtx, &scanCtx->pstPath->edge[i], &newTopoHandle->info[i + 1]);
        if (ret != GMERR_OK) {
            SimpRelReleaseTopoHandle(newTopoHandle);
            goto RELEASE;
        }
    }
    newTopoHandle->maxNum = scanCtx->pstPath->edgeNum + 1;
    *handleCond = newTopoHandle;
RELEASE:
    embCtx->nspCtrl = NULL;
    DbRWUnlatchR(&nspCtrl->latch);
    DbRWUnlatchR(&simpleRelMgr->latch);
    return ret;
}

Status GmeSimpleTopoHandleAddEdge(
    GmeConnT *conn, SimpRelTopoScanCtxT *scanCtx, DbMemCtxT *memCtx, AdptTopoHandleInfoT *topoHandle)
{
    DB_POINTER3(conn, scanCtx, topoHandle);
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    EmbSimpRelMgrT *simpleRelMgr = embCtx->simpleRelMgr;
    DbRWLatchR(&simpleRelMgr->latch);
    NspCtrlT *nspCtrl = SimpleRelGetNspCtrlById(&simpleRelMgr->nspCtrlList, scanCtx->dbId, NULL);
    if (nspCtrl == NULL) {
        DbRWUnlatchR(&simpleRelMgr->latch);
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALID_DATABASE, "Incorrect DB ID (%" PRIu32 ").", scanCtx->dbId);
        return VOS_ERRNO_DB_INVALID_DATABASE;
    }
    DbRWLatchR(&nspCtrl->latch);
    if (topoHandle->currentIdx == DB_PATH_EDGE_MAX_NUM) {
        DbRWUnlatchR(&nspCtrl->latch);
        DbRWUnlatchR(&simpleRelMgr->latch);
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_FAILURE, "Too many edges.");
        return VOS_ERRNO_DB_FAILURE;
    }
    embCtx->nspCtrl = nspCtrl;
    Status ret = SimpRelInitTopoHandByEdge(
        embCtx, nspCtrl, memCtx, scanCtx->edge, &topoHandle->info[topoHandle->currentIdx + 1]);
    if (ret != GMERR_OK) {
        DbRWUnlatchR(&nspCtrl->latch);
        DbRWUnlatchR(&simpleRelMgr->latch);
        return ret;
    }
    topoHandle->currentIdx++;
    DbRWUnlatchR(&nspCtrl->latch);
    DbRWUnlatchR(&simpleRelMgr->latch);
    return GMERR_OK;
}

inline static Status SimpleRelCheckRecLenInner(uint32_t expectLen, uint32_t actualLen)
{
    if (SECUREC_UNLIKELY(expectLen != actualLen)) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDRECLEN,
            "input buff len is worthless. schema len %" PRIu32 ", input buff len %" PRIu32 ".", expectLen, actualLen);
        return VOS_ERRNO_DB_INVALIDRECLEN;
    }
    return GMERR_OK;
}

inline static bool SimpleRelIsBytesType(DB_DATATYPE_ENUM_V1 type)
{
    return type == DBT_BYTES || type == DBT_VBYTES;
}

ALWAYS_INLINE static Status SimpleRelCheckBytesHeaderLen(
    const uint8_t *inputBuf, DB_DATATYPE_ENUM_V1 type, uint16_t defSize, uint32_t offset)
{
    if (SECUREC_LIKELY(!SimpleRelIsBytesType(type))) {
        return GMERR_OK;
    }

    const uint16_t bytesHeadLen = *(const uint16_t *)(inputBuf + offset);
    if (SECUREC_UNLIKELY(bytesHeadLen > defSize)) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_INVALIDDATALEN,
            "Input length for bytes data is not correct, input len %" PRIu16 ", fldDef len %" PRIu16 ".", bytesHeadLen,
            defSize);
        return VOS_ERRNO_DB_INVALIDDATALEN;
    }
    return GMERR_OK;
}

static Status SimpleRelCheckByteTypeLenIsValid(
    DB_FIELDFILTER_STRU *fldFilter, VlabelCtrlT *vLabelCtrl, uint8_t *inputBuf)
{
    Status ret = GMERR_OK;
    OriginDefInfoT *originDefInfo = vLabelCtrl->originDefInfo;
    uint32_t offset = 0;
    if (fldFilter->ucFieldNum == DB_FIELD_ALL) {
        for (uint32_t i = 0; i < originDefInfo->ulNCols; i++) {
            ret = SimpleRelCheckBytesHeaderLen(
                inputBuf, originDefInfo->pstFldLst[i].enDataType, originDefInfo->pstFldLst[i].usSize, offset);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
            offset += SimpRelGetFldStoreLen(originDefInfo->pstFldLst[i].enDataType, originDefInfo->pstFldLst[i].usSize);
        }
    } else {
        for (uint32_t i = 0; i < fldFilter->ucFieldNum; i++) {
            uint8_t fldId = fldFilter->aucField[i];
            ret = SimpleRelCheckBytesHeaderLen(
                inputBuf, originDefInfo->pstFldLst[fldId].enDataType, originDefInfo->pstFldLst[fldId].usSize, offset);
            if (SECUREC_LIKELY(ret != GMERR_OK)) {
                return ret;
            }
            offset += SimpRelGetFldStoreLen(
                originDefInfo->pstFldLst[fldId].enDataType, originDefInfo->pstFldLst[fldId].usSize);
        }
    }
    return GMERR_OK;
}

Status SimpleRelCheckRecLen(SimpRelScanCtxT *scanCtx, VlabelCtrlT *vLabelCtrl, GmcSeriT *seri)
{
    // 需先用SimpleRelCheckScanCtx校验fldFilter
    Status ret;
    uint32_t recLen = 0;
    if (scanCtx->fldFilter->ucFieldNum == DB_FIELD_ALL) {
        recLen = vLabelCtrl->originDefInfo->usRecLen;
    } else {
        for (uint32_t i = 0; i < scanCtx->fldFilter->ucFieldNum; i++) {
            uint8_t fldId = scanCtx->fldFilter->aucField[i];
            recLen += SimpRelGetFldStoreLen(vLabelCtrl->originDefInfo->pstFldLst[fldId].enDataType,
                vLabelCtrl->originDefInfo->pstFldLst[fldId].usSize);
        }
    }

    ret = SimpleRelCheckRecLenInner(recLen, seri->bufSize);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return SimpleRelCheckByteTypeLenIsValid(scanCtx->fldFilter, vLabelCtrl, seri->obj);
}

void GmeSimpleRelGetResourceInfo(GmeConnT *conn, DB_RESOURCEINFO_STRU *pstResInfo)
{
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;

    SessionT *session = embCtx->session->session;
    SeRunCtxHdT seInstance = session->seInstance;
    SeRunCtxT *seRunCtxPtr = seInstance;
    TrxT *trx = seRunCtxPtr->trx;
    uint32_t maxTrxNum = 0;
    TrxGetMaxTrxNum(trx, &maxTrxNum);
    uint32_t usedTrxNum = 0;
    TrxGetUsedTrxNum(trx, &usedTrxNum);

    pstResInfo->stTranStat.usTotTransCnt = maxTrxNum;
    pstResInfo->stTranStat.usUsedTransCnt = usedTrxNum;
    pstResInfo->stTranStat.usFreeTransCnt = maxTrxNum - usedTrxNum;

    // 计算内存开销
    uint32_t trxsShmPtrArrNum = (maxTrxNum + TRX_NUM_PER_BLOCK - 1) / TRX_NUM_PER_BLOCK;  // 向上取整
    uint32_t trxIdListSize = (uint32_t)(sizeof(TrxIdListT) + maxTrxNum * sizeof(TrxIdT));
    uint32_t trxMgrSize = (uint32_t)sizeof(TrxMgrT) + GetTrxPoolSize(maxTrxNum, trxsShmPtrArrNum) + trxIdListSize +
                          trxIdListSize;  // rwTrxIds roTrxIds trxPool

    uint32_t resSize = trxMgrSize;

    uint32_t customerMgrSize = 0;
    DmGetCustomerTypeMgrSize(&customerMgrSize);
    resSize += customerMgrSize;
    uint32_t handleMgrSize = DbGaListGetCount(&g_adptV1Instance.handleMgr.handleList) * sizeof(AdptHandleInfoT);
    resSize += handleMgrSize;
    pstResInfo->ulResSize = resSize;
}

Status GmeSimpleRelGetSortIndexDetails(
    GmeConnT *conn, uint32_t nspId, uint16_t relId, uint8_t indexId, DB_SORT_INDEX_INFO *pstSortIndexDetail)
{
    SimpSortIndexDetailT sortIndexDetail = {.indexId = indexId, .pstSortIndexDetail = pstSortIndexDetail};
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    embCtx->opCode = SIMPLEREL_OP_TYPE_GET_META_WITH_DBID;
    embCtx->apiCtx = &sortIndexDetail;
    embCtx->nspId = nspId;
    embCtx->relNo = relId;
    return SimpleRelationExecute(embCtx, SimpRelGetSortIndexDetails);
}

typedef struct SimpLastError {
    char *lastErrorBuf;
    uint32_t *lastErrorLen;
} SimpLastErrorT;

Status SimpRelGetLastError(EmbSimpleRunCtxT *embCtx)
{
    NspCtrlT *nspCtrl = embCtx->nspCtrl;
    SimpLastErrorT *lastErr = (SimpLastErrorT *)embCtx->apiCtx;
    uint32_t nspLastErrLen = strlen(nspCtrl->lastError);
    if (nspLastErrLen == 0) {
        *(lastErr->lastErrorLen) = 0;
        return GMERR_OK;
    }
    if (*(lastErr->lastErrorLen) < (nspLastErrLen + 1)) {
        *(lastErr->lastErrorLen) = nspLastErrLen + 1;
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_BUFNOTENOUGH, "The length for lastError is not enough.");
        return VOS_ERRNO_DB_BUFNOTENOUGH;
    }

    errno_t err = memcpy_s(lastErr->lastErrorBuf, *(lastErr->lastErrorLen), nspCtrl->lastError, nspLastErrLen + 1);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMCPY_FAILURE, "Unable to copy lastError.");
        return VOS_ERRNO_DB_MEMCPY_FAILURE;
    }
    *(lastErr->lastErrorLen) = nspLastErrLen + 1;
    return GMERR_OK;
}

Status GmeSimpleRelGetLastError(GmeConnT *conn, uint32_t nspId, char *lastErrorBuf, uint32_t *lastErrorLen)
{
    SimpLastErrorT lastErr = {.lastErrorBuf = lastErrorBuf, .lastErrorLen = lastErrorLen};
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    embCtx->opCode = SIMPLEREL_OP_TYPE_GET_LAST_ERROR;
    embCtx->apiCtx = &lastErr;
    embCtx->nspId = nspId;
    return SimpleRelationExecute(embCtx, SimpRelGetLastError);
}

static Status SimpRelGetDataSize(DmVertexLabelT *vertexLabel, uint32_t *dataSize)
{
    if (vertexLabel == NULL) {
        return GMERR_OK;
    }
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);
    DbCfgValueT cfgValue;
    Status ret = DbCfgGet(cfgHandle, DB_CFG_SE_PAGE_SIZE, &cfgValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t pageSize = cfgValue.int32Val * DB_KIBI;

    // 统计heap占用的page数量
    HeapPerfStatT perfStat = {0};  // 存放pageCnt
    HeapCfgStatT heapCfgStat = {0};
    uint64_t heapPageCnt = 0;
    // 聚簇容器不统计fsm和heap
    if (vertexLabel->metaVertexLabel->containerType != CONTAINER_HEAP) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "not support containerType.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = vertexLabel->commonInfo->heapInfo.heapShmAddr,
        .isPersistent = vertexLabel->metaCommon.isPersistent,
        .isUseRsm = vertexLabel->metaCommon.isUseRsm,
        .instanceId = DbGetInstanceId(DbGetInstanceByMemCtx(vertexLabel->memCtx))};
    ret = HeapLabelGetPerfStat(&heapCntrAcsInfo, &perfStat, &heapCfgStat);
    if (ret != GMERR_OK) {
        return ret;
    }
    heapPageCnt = perfStat.pageCnt;
    *dataSize = (uint32_t)(heapPageCnt * pageSize);
    return GMERR_OK;
}

static Status SimpRelGetIndexAndOtherSize(
    EmbSimpleRunCtxT *embCtx, VlabelCtrlT *vLabelCtrl, uint32_t *indexSize, uint32_t *otherSize)
{
    bool isBigObject = !HeapCheckIsNormalRowByBufSize(vLabelCtrl->vertexLabel->metaVertexLabel->schema->maxRecLen);
    uint32_t nodeSize = isBigObject ? sizeof(TTreeNodeBigObjT) : sizeof(TTreeNodeT);
    uint32_t tmpIndexSize = 0;
    uint32_t idxNum = vLabelCtrl->vertexLabel->metaVertexLabel->secIndexNum;
    for (uint32_t i = 0; i < idxNum; i++) {
        DmVlIndexLabelT *index = &vLabelCtrl->vertexLabel->metaVertexLabel->secIndexes[i];
        TTreeHeadT *ttreeIdx = (TTreeHeadT *)DbShmPtrToAddr(index->idxLabelBase.shmAddr);
        if (ttreeIdx == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
                "ttree index's shm is not right. (segid: %" PRIu32 " offset: %" PRIu32 ")",
                index->idxLabelBase.shmAddr.segId, index->idxLabelBase.shmAddr.offset);
            return GMERR_DATA_EXCEPTION;
        }
        tmpIndexSize += (ttreeIdx->nodeCnt * nodeSize);
    }

    *otherSize = idxNum * sizeof(TTreeHeadT);
    *indexSize = tmpIndexSize;
    return GMERR_OK;
}

static Status SimpRelGetTblMemUsageStats(EmbSimpleRunCtxT *embCtx)
{
    DB_MEMUSAGE_INFO_STRU *stMemInfo = (DB_MEMUSAGE_INFO_STRU *)embCtx->apiCtx;
    NspCtrlT *nspCtrl = embCtx->nspCtrl;
    VlabelCtrlT *vLabelCtrl = NULL;
    Status ret = SimpleRelGetVlabelCtrlById(&nspCtrl->vLabelCtrlList, embCtx->relNo, NULL, &vLabelCtrl, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t schemaSize = 0;
    uint32_t dataSize = 0;
    uint32_t indexSize = 0;
    uint32_t otherSize = 0;
    if (!vLabelCtrl->needLazyLoad) {
        LabelRLatchAcquire(vLabelCtrl->latch);
        schemaSize = vLabelCtrl->vertexLabel->metaVertexLabel->vlSize;

        ret = SimpRelGetDataSize(vLabelCtrl->vertexLabel, &dataSize);
        if (ret != GMERR_OK) {
            LabelRLatchRelease(vLabelCtrl->latch);
            return ret;
        }

        ret = SimpRelGetIndexAndOtherSize(embCtx, vLabelCtrl, &indexSize, &otherSize);
        if (ret != GMERR_OK) {
            LabelRLatchRelease(vLabelCtrl->latch);
            return ret;
        }
        LabelRLatchRelease(vLabelCtrl->latch);
    }
    otherSize += sizeof(VlabelCtrlT) + vLabelCtrl->originDefInfo->allocSize;
    stMemInfo->ulSchemaSize = schemaSize;
    stMemInfo->ulDataSize = dataSize;
    stMemInfo->ulIndexSize = indexSize;
    stMemInfo->ulOtherSize = otherSize;
    return GMERR_OK;
}

Status GmeSimpleRelGetTblMemUsageStats(GmeConnT *conn, uint32_t nspId, uint16_t relId, DB_MEMUSAGE_INFO_STRU *stMemInfo)
{
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    embCtx->opCode = SIMPLEREL_OP_TYPE_GET_META_WITH_DBID;
    embCtx->apiCtx = stMemInfo;
    embCtx->nspId = nspId;
    embCtx->relNo = relId;
    return SimpleRelationExecute(embCtx, SimpRelGetTblMemUsageStats);
}

static Status SimpRelGetGetDbMemSize(EmbSimpleRunCtxT *embCtx)
{
    uint32_t *pulDBSize = (uint32_t *)embCtx->apiCtx;
    uint32_t dbSize = 0;
    NspCtrlT *nspCtrl = embCtx->nspCtrl;
    for (uint32_t usTab = 0; usTab < nspCtrl->vLabelCtrlList.count; usTab++) {
        VlabelCtrlT *vLabelCtrl = DbListItem(&nspCtrl->vLabelCtrlList, usTab);
        DB_MEMUSAGE_INFO_STRU stMemInfo = {0};
        embCtx->apiCtx = &stMemInfo;
        embCtx->relNo = vLabelCtrl->relationId;
        Status ret = SimpRelGetTblMemUsageStats(embCtx);
        if (ret != GMERR_OK) {
            return ret;
        }
        dbSize += stMemInfo.ulSchemaSize + stMemInfo.ulDataSize + stMemInfo.ulIndexSize + stMemInfo.ulOtherSize;
    }
    dbSize += sizeof(NspCtrlT);
    *pulDBSize = dbSize;
    return GMERR_OK;
}

Status GmeSimpleRelGetDbMemSize(GmeConnT *conn, uint32_t nspId, uint32_t *pulDBSize)
{
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    embCtx->opCode = SUMPLEREL_OP_TYPE_GET_DB_MEM;
    embCtx->apiCtx = pulDBSize;
    embCtx->nspId = nspId;
    return SimpleRelationExecute(embCtx, SimpRelGetGetDbMemSize);
}

static Status SimpRelGetGetDbMemUsageStats(EmbSimpleRunCtxT *embCtx)
{
    DB_MEMUSAGE_INFO_STRU *pstMemInfo = (DB_MEMUSAGE_INFO_STRU *)embCtx->apiCtx;
    uint32_t dbSchemaSize = 0;
    uint32_t dbDataSize = 0;
    uint32_t dbIndexSize = 0;
    uint32_t dbOtherSize = 0;
    NspCtrlT *nspCtrl = embCtx->nspCtrl;
    for (uint32_t usTab = 0; usTab < nspCtrl->vLabelCtrlList.count; usTab++) {
        VlabelCtrlT *vLabelCtrl = DbListItem(&nspCtrl->vLabelCtrlList, usTab);
        DB_MEMUSAGE_INFO_STRU stMemInfo = {0};
        embCtx->apiCtx = &stMemInfo;
        embCtx->relNo = vLabelCtrl->relationId;
        Status ret = SimpRelGetTblMemUsageStats(embCtx);
        if (ret != GMERR_OK) {
            return ret;
        }
        dbSchemaSize += stMemInfo.ulSchemaSize;
        dbDataSize += stMemInfo.ulDataSize;
        dbIndexSize += stMemInfo.ulIndexSize;
        dbOtherSize += stMemInfo.ulOtherSize;
    }
    pstMemInfo->ulSchemaSize = dbSchemaSize;
    pstMemInfo->ulDataSize = dbDataSize;
    pstMemInfo->ulIndexSize = dbIndexSize;
    pstMemInfo->ulOtherSize = dbOtherSize + sizeof(NspCtrlT);
    return GMERR_OK;
}

Status GmeSimpleRelGetDbMemUsageStats(GmeConnT *conn, uint32_t nspId, DB_MEMUSAGE_INFO_STRU *pstMemInfo)
{
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    embCtx->opCode = SUMPLEREL_OP_TYPE_GET_DB_MEM;
    embCtx->apiCtx = pstMemInfo;
    embCtx->nspId = nspId;
    return SimpleRelationExecute(embCtx, SimpRelGetGetDbMemUsageStats);
}
