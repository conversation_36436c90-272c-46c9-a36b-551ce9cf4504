#其他依赖模块的头文件目录按需添加
include_directories(${GMDB_XXHASH_INC_PATH})
include_directories(${GMDB_CJSON_INC_PATH})
include_directories(${GMDB_JANSSON_INC_PATH})

include_sub_directories_recursively(${CMAKE_CURRENT_SOURCE_DIR})

#目录
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/boot SRC_BOOT_LIST)
if(NOT FEATURE_DAF)
    # RR事务才需要
    list(REMOVE_ITEM SRC_BOOT_LIST ${CMAKE_CURRENT_SOURCE_DIR}/boot/db_server_daf_purger.c)
endif ()
if(NOT FEATURE_STREAM)
    list(REMOVE_ITEM SRC_BOOT_LIST ${CMAKE_CURRENT_SOURCE_DIR}/boot/db_server_stream_timer.c)
endif()
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/base SRC_SERVICE_BASE_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/base/component_fmw SRC_SERVICE_BASE_LIST)
if(NOT FEATURE_STREAM)
    list(REMOVE_ITEM SRC_SERVICE_BASE_LIST ${CMAKE_CURRENT_SOURCE_DIR}/base/component_fmw/service_stream_component.c)
endif()
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data SRC_SERVICE_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/base SRC_SERVICE_LIST)
list(REMOVE_ITEM SRC_SERVICE_LIST ${CMAKE_CURRENT_SOURCE_DIR}/data/base/srv_data_service_language_utils.c)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/public SRC_SERVICE_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/fastpath SRC_SERVICE_FASTPATH_LIST)
if(NOT DIRECT_WRITE)
	list(REMOVE_ITEM SRC_SERVICE_FASTPATH_LIST ${CMAKE_CURRENT_SOURCE_DIR}/data/fastpath/srv_data_fastpath_dw_pubsub_entry.c)
    list(REMOVE_ITEM SRC_SERVICE_FASTPATH_LIST ${CMAKE_CURRENT_SOURCE_DIR}/data/fastpath/srv_data_fastpath_dw_pubsub.c)
endif()
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/datalog SRC_SERVICE_DATALOG_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/datalog/handler SRC_SERVICE_DATALOG_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/datalog/handler/iterator SRC_SERVICE_DATALOG_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/datalog/handler/processor SRC_SERVICE_DATALOG_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/datalog/handler/processor/template SRC_SERVICE_DATALOG_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/datalog/dtlservice SRC_SERVICE_DATALOG_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/datalog/rspservice SRC_SERVICE_DATALOG_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/yang SRC_SERVICE_YANG_LIST)

aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/connection SRC_CONNECTION_LIST)

aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/view SRC_SERVICE_VIEW_LIST)
list(REMOVE_ITEM SRC_SERVICE_VIEW_LIST ${CMAKE_CURRENT_SOURCE_DIR}/view/datalog_service_view.c)
list(REMOVE_ITEM SRC_SERVICE_VIEW_LIST ${CMAKE_CURRENT_SOURCE_DIR}/view/yang_service_view.c)
set(SRC_VIEW_DATALOG ${CMAKE_CURRENT_SOURCE_DIR}/view/datalog_service_view.c)
set(SRC_VIEW_YANG ${CMAKE_CURRENT_SOURCE_DIR}/view/yang_service_view.c)

aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/kv SRC_MINI_SERVICE_LIST)

aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/embedded/kv SRC_EMBEDDED_LIST)

aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/embedded/base SRC_EMBEDDED_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/embedded SRC_EMBEDDED_LIST)

if (FEATURE_MINIKV)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/embedded/boot SRC_MINI_SERVICE_LIST)
endif()

if (FEATURE_DISTRIBUTION)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/sync SRC_DISTRIBUTION_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/shared_obj SRC_DISTRIBUTION_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/embedded/sync SRC_DISTRIBUTION_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/embedded/shared_obj SRC_DISTRIBUTION_LIST)
endif()

if (FEATURE_SQL)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/embedded SRC_SERVICE_SQL_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/embedded/sql SRC_SERVICE_SQL_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/sql SRC_SERVICE_SQL_LIST)
    list(APPEND SRC_SERVICE_SQL_LIST ${CMAKE_CURRENT_SOURCE_DIR}/data/base/srv_data_service_language_utils.c)
    list(APPEND SRC_SERVICE_SQL_LIST ${CMAKE_CURRENT_SOURCE_DIR}/data/srv_data_service.c)
    set(SQL_SRCS ${SQL_SRCS} ${SRC_SERVICE_SQL_LIST} PARENT_SCOPE)
endif()

if (FEATURE_SIMPLEREL)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/embedded/simplerel SRC_SERVICE_SIMPLEREL_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/embedded/simplerel/ddl SRC_SERVICE_SIMPLEREL_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/embedded/simplerel/metadata SRC_SERVICE_SIMPLEREL_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/embedded/simplerel/dml SRC_SERVICE_SIMPLEREL_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/embedded/simplerel/dql SRC_SERVICE_SIMPLEREL_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/embedded/simplerel/custom_type SRC_SERVICE_SIMPLEREL_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/embedded/simplerel/custom_type/inner_custom_compare_func SRC_SERVICE_SIMPLEREL_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/embedded/simplerel/persistence SRC_SERVICE_SIMPLEREL_LIST)
    # 依赖SQL嵌入式GmeOpen\GmeClose能力
    list(APPEND SRC_SERVICE_SIMPLEREL_LIST ${CMAKE_CURRENT_SOURCE_DIR}/embedded/sql/srv_emb_sql_base_api.c)
endif()

if (FEATURE_GQL)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/gql SRC_SERVICE_GQL_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/gql/path SRC_SERVICE_GQL_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/gql/path/base SRC_SERVICE_GQL_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/gql/path/batch SRC_SERVICE_GQL_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/gql/path/search SRC_SERVICE_GQL_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/gql/path/smooth_recon SRC_SERVICE_GQL_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/gql/path/sub SRC_SERVICE_GQL_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/gql/path/trigger SRC_SERVICE_GQL_LIST)
    set(GQL_SRCS ${GQL_SRCS} ${SRC_SERVICE_GQL_LIST} PARENT_SCOPE)
endif()

if (FEATURE_TS)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/ts SRC_SERVICE_TS_LIST)
endif()

if (FEATURE_STREAM)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/stream SRC_SERVICE_STREAM_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/data/stream/forward SRC_SERVICE_STREAM_LIST)
    if (NOT FEATURE_CS)
        aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/embedded/stream SRC_SERVICE_STREAM_LIST)
    endif()
    list(APPEND SRC_SERVICE_LIST ${CMAKE_CURRENT_SOURCE_DIR}/data/base/srv_data_service_language_utils.c)
    set(STREAM_SRCS ${STREAM_SRCS} ${SRC_SERVICE_STREAM_LIST} PARENT_SCOPE)
endif()

#主备同步功能新加的文件，如果后续有新增，只能靠编译报错后再来此处修改解决
if (NOT FEATURE_REPLICATION)
    set(SRC_SERVICE_REPLICATION_LIST ${CMAKE_CURRENT_SOURCE_DIR}/data/fastpath/srv_data_ha.c
        ${CMAKE_CURRENT_SOURCE_DIR}/data/fastpath/srv_data_slave_dispatch.c
        ${CMAKE_CURRENT_SOURCE_DIR}/data/fastpath/srv_data_slave_replay.c)
    list(REMOVE_ITEM SRC_SERVICE_LIST ${SRC_SERVICE_REPLICATION_LIST})
    list(REMOVE_ITEM FASTPATH_SRCS ${SRC_SERVICE_REPLICATION_LIST})
    list(REMOVE_ITEM SRC_SERVICE_FASTPATH_LIST ${SRC_SERVICE_REPLICATION_LIST})
endif()

# SQL 单独编或者与 Minikv 一起编，使用 srv_mini_sql_dummy.c 和 srv_mini_sql_manager_base.c
if(FEATURE_SQL)
    set(SRC_MINI_DUMMY_LIST
        ${CMAKE_CURRENT_SOURCE_DIR}/embedded/dummy/srv_mini_sql_dummy.c 
        ${CMAKE_CURRENT_SOURCE_DIR}/embedded/dummy/srv_mini_sql_manager_base.c)
elseif(FEATURE_MINIKV)
    # Minikv 单独编，使用 srv_mini_dummy.c
    set(SRC_MINI_DUMMY_LIST ${CMAKE_CURRENT_SOURCE_DIR}/embedded/dummy/srv_mini_dummy.c)
endif()

set(SRC_SRC_MINI_BASE ${CMAKE_CURRENT_SOURCE_DIR}/boot/db_server.c)

if (FEATURE_MINIKV)
    set(SRC_DBSERVER_LIST ${SRC_SRC_MINI_BASE} ${SRC_MINI_DUMMY_LIST})
elseif(FEATURE_SIMPLEREL)
    set(SRC_DBSERVER_LIST ${CMAKE_CURRENT_SOURCE_DIR}/boot/db_server.c ${CMAKE_CURRENT_SOURCE_DIR}/boot/db_server.c
        ${CMAKE_CURRENT_SOURCE_DIR}/boot/db_server_purger.c ${CMAKE_CURRENT_SOURCE_DIR}/boot/db_server_undo_purger.c
        ${CMAKE_CURRENT_SOURCE_DIR}/boot/db_server_dfgmt_task.c ${CMAKE_CURRENT_SOURCE_DIR}/data/public/srv_data_public.c
        ${CMAKE_CURRENT_SOURCE_DIR}/data/srv_data_service.c ${CMAKE_CURRENT_SOURCE_DIR}/embedded/dummy/srv_mini_sql_manager_base.c
        ${CMAKE_CURRENT_SOURCE_DIR}/embedded/dummy/srv_mini_simplerel_dummy.c)
else()
    set(SRC_DBSERVER_LIST ${SRC_BOOT_LIST} ${SRC_SERVICE_BASE_LIST} ${SRC_SERVICE_LIST} ${SRC_CONNECTION_LIST} ${SRC_SERVICE_VIEW_LIST})
endif()

set(DATALOG_SRCS ${DATALOG_SRCS} ${SRC_SERVICE_DATALOG_LIST} ${SRC_VIEW_DATALOG} PARENT_SCOPE)

# 将fastpath service加到fastpath特性组件
set(FASTPATH_SRCS ${FASTPATH_SRCS} ${SRC_SERVICE_FASTPATH_LIST} PARENT_SCOPE)

set(YANG_SRCS ${YANG_SRCS} ${SRC_SERVICE_YANG_LIST} ${SRC_VIEW_YANG} PARENT_SCOPE)
set(MINIKV_SRCS ${MINIKV_SRCS} ${SRC_MINI_SERVICE_LIST} ${SRC_EMBEDDED_LIST} PARENT_SCOPE)
set(DISTRIBUTION_SRCS ${DISTRIBUTION_SRCS} ${SRC_DISTRIBUTION_LIST} ${SRC_EMBEDDED_LIST} PARENT_SCOPE)
set(SIMPLEREL_SRCS ${SIMPLEREL_SRCS} ${SRC_SERVICE_SIMPLEREL_LIST} PARENT_SCOPE)
SET(FIELD_GRD_SRCS ${GRD_SRC_EXECUTOR_BASE_LIST} ${GRD_SRC_EXECUTOR_DOCUMENT_LIST} ${GRD_SRC_EXECUTOR_KV_LIST}
    ${GRD_SRC_ADAPTER_LIST} ${GRD_SRC_ADAPTER_LOG_LIST} ${GRD_SRC_COMMON_BASE} ${GRD_SRC_EXECUTOR_SHARED_LIST}
    ${GRD_SRC_EXECUTOR_SYNC_LIST} ${GRD_SRC_EXECUTOR_SQL_LIST})

if (FEATURE_TS)
    set(TS_SRCS ${TS_SRCS} ${SRC_SERVICE_TS_LIST} PARENT_SCOPE)
endif()

if (MODULARBUILD)
    set(MODULE_NAME gmservice)
    add_library(${MODULE_NAME} SHARED ${SRC_DBSERVER_LIST})
    target_include_directories(${MODULE_NAME} INTERFACE ${CMAKE_CURRENT_SOURCE_DIR}/include)
    set_target_properties(${MODULE_NAME} PROPERTIES VERSION ${GMDB_PROJECT_VERSION} SOVERSION ${GMDB_MAJOR_VERSION})
    target_link_libraries(${MODULE_NAME} gmstorage gmruntime gmcompiler gmdatamodel gmexecutor)
    if (STRIP)
        separate_debug_info(${MODULE_NAME})
    endif ()
    install(TARGETS ${MODULE_NAME} LIBRARY DESTINATION lib)
else ()
    list(APPEND LIBRARIES_LIST ${SRC_DBSERVER_LIST})
    list(APPEND INCLUDE_DIRECTORIES_LIST ${CMAKE_CURRENT_SOURCE_DIR}/include)
    set(LIBRARIES_LIST ${LIBRARIES_LIST} PARENT_SCOPE)
    set(INCLUDE_DIRECTORIES_LIST ${INCLUDE_DIRECTORIES_LIST} PARENT_SCOPE)
endif()
