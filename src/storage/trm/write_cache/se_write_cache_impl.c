/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:  write cache in SE
 * Author: fanqiushi
 * Create: 2024-02-05
 */

#include "se_write_cache.h"
#include "se_write_cache_rsm_public.h"

#define PERCENTAGE_BASE 100
#define SCAL_IN_THRESHOLD 50
#define SCAL_IN_THRESHOLD_NUMERATOR 4
#define SCAL_IN_THRESHOLD_DENOMINATOR 5

void UpdateMergePage(WCachePageHeadT *mergePage, WriteCacheDescT *desc)
{
    if (desc->isWriteCacheUseRsm) {
        return SeRsmUpdateMergePage(mergePage, desc);
    }
    if (IsShmemPtrEqual(desc->mergePageAddr, desc->writePageAddr)) {
        return;
    }
    desc->mergePageAddr = mergePage->nextPageAddr;
}

void CacheRsmUndoLogForResetPage(WriteCacheDescT *desc, PageIdT currPageId)
{
    DB_POINTER(desc);
    if (!IsNeedRsmUndo(desc)) {
        return;
    }
    RsmUndoRecordT *rsmUndoRecord = &desc->srvRsmUndoRecord;
    RsmUndoOpRecordT *opRecord = RsmUndoGetNextFreeRecord(rsmUndoRecord, rsmUndoRecord->usedUndoRecNum);
    opRecord->recordType = RSM_UNDO_WRITE_CACHE_RESET_PAGE;
    opRecord->rsmWriteCacheResetPageRec.deviceId = currPageId.deviceId;
    opRecord->rsmWriteCacheResetPageRec.blockId = currPageId.blockId;
    RsmUndoMoveNextFreeRecord(rsmUndoRecord);
}

void CacheRsmUndoLogForUpdateMergeBeginOffset(WriteCacheDescT *desc, uint32_t newMergeBeginOffset)
{
    DB_POINTER(desc);
    if (!IsNeedRsmUndo(desc)) {
        return;
    }
    RsmUndoRecordT *rsmUndoRecord = &desc->srvRsmUndoRecord;
    RsmUndoOpRecordT *opRecord = RsmUndoGetNextFreeRecord(rsmUndoRecord, rsmUndoRecord->usedUndoRecNum);
    opRecord->recordType = RSM_UNDO_WRITE_CACHE_MERGE_BEGIN_OFFSET;
    opRecord->rsmWriteCacheMergeBeginOffsetRec.offset = newMergeBeginOffset;
    RsmUndoMoveNextFreeRecord(rsmUndoRecord);
}

void SeUpdatePageInfo(WCachePageHeadT *page, bool isMoveMergePage, WriteCacheDescT *desc, uint32_t endOffset)
{
    // 需更新page信息，避免此时客户端也正在修改值，故写锁
#ifndef NDEBUG
    if (desc->isWriteCacheUseRsm) {
        DbRsmKernelEntry(DB_RSM_KERNEL_WRITE_CACHE_DML);  // 修改缓存rsm，进入内核态
    }
#endif
    if (isMoveMergePage) {  // 原来是写满的页，则改成free页
        DbSpinLock(&page->lock);
        SHM_CRASHPOINT_ONE(SHM_CRASH_WC_MERGE_AFTER_PAGE_LOCK);
        CacheRsmUndoLogForResetPage(desc, page->currPageId);
        SHM_CRASHPOINT_ONE(SHM_CRASH_WC_MERGE_AFTER_LOG_RESET_PAGE);
        page->freeOffset = (uint32_t)sizeof(WCachePageHeadT);
        SHM_CRASHPOINT_ONE(SHM_CRASH_WC_MERGE_AFTER_INIT_FREE_OFFSET);
        page->mergeBeginOffset = page->freeOffset;
        SHM_CRASHPOINT_ONE(SHM_CRASH_WC_MERGE_AFTER_INIT_MERGE_BEGIN_OFFSET);
        desc->mergedReqNum = 0;
        SHM_CRASHPOINT_ONE(SHM_CRASH_WC_MERGE_AFTER_RESET_MERGED_REQ_NUM_WHEN_FREE);
        CacheSrvRsmUndoLogLeave(desc);
        SHM_CRASHPOINT_ONE(SHM_CRASH_WC_MERGE_AFTER_LEAVE_RESET_PAGE_LOG);
        DbSpinUnlock(&page->lock);
        UpdateMergePage(page, desc);
    } else {  // 原来是正在写的页，则修改mergeBeginOffset
        CacheRsmUndoLogForUpdateMergeBeginOffset(desc, endOffset);
        SHM_CRASHPOINT_ONE(SHM_CRASH_WC_MERGE_AFTER_LOG_MERGE_BEGIN_OFFSET);
        page->mergeBeginOffset = endOffset;
        SHM_CRASHPOINT_ONE(SHM_CRASH_WC_MERGE_AFTER_UPDATE_MERGE_BEGIN_OFFSET);
        desc->mergedReqNum = 0;
        SHM_CRASHPOINT_ONE(SHM_CRASH_WC_MERGE_AFTER_RESET_MERGED_REQ_NUM_WHEN_ON_WRITE);
        CacheSrvRsmUndoLogLeave(desc);
        SHM_CRASHPOINT_ONE(SHM_CRASH_WC_MERGE_AFTER_LEAVE_MERGE_BEGIN_OFFSET_LOG);
    }
#ifndef NDEBUG
    if (desc->isWriteCacheUseRsm) {
        DbRsmKernelLeave(DB_RSM_KERNEL_WRITE_CACHE_DML);  // 修改缓存rsm，离开内核态
    }
#endif
}

Status CacheAllocShmPage(DbMemCtxT *shmCtx, ShmemPtrT *outputPagePtr)
{
    DB_POINTER2(shmCtx, outputPagePtr);
    *outputPagePtr = DB_INVALID_SHMPTR;
    // 从配置项获取缓存页大小
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);
    DbCfgValueT pageSizeCfg = {0};
    Status ret = DbCfgGet(cfgHandle, DB_CFG_SE_PAGE_SIZE, &pageSizeCfg);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "worthless to get DB_CFG_SE_PAGE_SIZE.");
        return ret;
    }
    uint32_t wcPageSize = (uint32_t)pageSizeCfg.int32Val * DB_KIBI;
    ShmemPtrT newPagePtr = DbShmemCtxAlloc(shmCtx, wcPageSize);  // 申请一个新页
    if (!DbIsShmPtrValid(newPagePtr)) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED,
            "alloc newPage worthless. newPagePtr = %" PRIu32 ":%" PRIu32 "", newPagePtr.offset, newPagePtr.segId);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    // 对page head 的初始化
    WCachePageHeadT *pageHead = (WCachePageHeadT *)DbShmPtrToAddr(newPagePtr);
    if (pageHead == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "DbShmPtrToAddr newPagePtr worthless. newPagePtr = %" PRIu32 ":%" PRIu32 "", newPagePtr.offset,
            newPagePtr.segId);
        DbShmemCtxFree(shmCtx, newPagePtr);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DbSpinInit(&pageHead->lock);
    pageHead->currPageAddr = newPagePtr;
    pageHead->nextPageAddr = newPagePtr;                       // 写缓存初始只有一个页
    pageHead->freeOffset = (uint32_t)sizeof(WCachePageHeadT);  // 偏移页头部
    pageHead->mergeBeginOffset = pageHead->freeOffset;         // 初始为第一个req的偏移
    *outputPagePtr = newPagePtr;
    return GMERR_OK;
}

Status SeWriteCacheCreateShm(DbMemCtxT *shmCtx, uint32_t namespaceId, uint32_t metaId, ShmemPtrT *writeCachePtr)
{
    DB_POINTER2(shmCtx, writeCachePtr);
    ShmemPtrT newPagePtr = DB_INVALID_SHMPTR;
    Status ret = CacheAllocShmPage(shmCtx, &newPagePtr);
    if (ret != GMERR_OK) {
        return ret;
    }
    ShmemPtrT writeCacheDescPtr = DbShmemCtxAlloc(shmCtx, sizeof(WriteCacheDescT));  // 申请WriteCacheDescT结构体的内存
    if (!DbIsShmPtrValid(writeCacheDescPtr)) {
        DbShmemCtxFree(shmCtx, newPagePtr);
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "alloc shmem of WriteCacheDescT worthless. shmemPtr = %d:%d",
            writeCacheDescPtr.offset, writeCacheDescPtr.segId);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    WriteCacheDescT *writeCacheDesc = (WriteCacheDescT *)DbShmPtrToAddr(writeCacheDescPtr);
    if (writeCacheDesc == NULL) {
        DbShmemCtxFree(shmCtx, newPagePtr);
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "addr convert worthless. descShmPtr= %d:%d",
            writeCacheDescPtr.offset, writeCacheDescPtr.segId);
        DbShmemCtxFree(shmCtx, writeCacheDescPtr);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    (void)memset_s(writeCacheDesc, sizeof(WriteCacheDescT), 0, sizeof(WriteCacheDescT));
    DbSpinInit(&writeCacheDesc->reqLock);
    writeCacheDesc->namespaceId = namespaceId;
    writeCacheDesc->labelId = metaId;
    writeCacheDesc->dbInstanceId = (int32_t)DbGetProcGlobalId();
    writeCacheDesc->isWriteCacheUseRsm = false;
    writeCacheDesc->isPartition = false;  // 共享内存不感知该字段，无影响
    writeCacheDesc->checkingStatus = 0;
    writeCacheDesc->writePageAddr = newPagePtr;
    writeCacheDesc->mergePageAddr = newPagePtr;
    writeCacheDesc->pageNum = 1;
    writeCacheDesc->mergedReqNum = 0;
    *writeCachePtr = writeCacheDescPtr;
    return ret;
}

void SeWriteCacheFreeWhenInitShm(DbMemCtxT *shmCtx, ShmemPtrT writeCachePtr)
{
    WriteCacheDescT *writeCacheDesc = (WriteCacheDescT *)DbShmPtrToAddr(writeCachePtr);
    if (writeCacheDesc == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "convert writeCachePtr worthless. Ptr = %d:%d",
            writeCachePtr.offset, writeCachePtr.segId);
        return;
    }
    DbShmemCtxFree(shmCtx, writeCacheDesc->writePageAddr);
    DbShmemCtxFree(shmCtx, writeCachePtr);
}

void SeWriteCacheFreeWhenDropShm(DbMemCtxT *shmCtx, WriteCacheDescT *writeCacheDesc, ShmemPtrT writeCachePtr)
{
    ShmemPtrT beginPage = writeCacheDesc->mergePageAddr;
    WCachePageHeadT *beginPagePtr = (WCachePageHeadT *)DbShmPtrToAddr(beginPage);
    if (beginPagePtr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "convert beginPage addr worthless. beginPage = %d:%d",
            beginPage.offset, beginPage.segId);
        return;
    }
    ShmemPtrT nextPage = beginPagePtr->nextPageAddr;
    DbShmemCtxFree(shmCtx, beginPage);
    // 循环遍历每一页，释放内存
    while (!((nextPage.segId == beginPage.segId) && (nextPage.offset == beginPage.offset))) {
        ShmemPtrT tmpPage = nextPage;
        WCachePageHeadT *tmpPagePtr = (WCachePageHeadT *)DbShmPtrToAddr(tmpPage);
        if (tmpPagePtr == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "convert tmpPage worthless. tmpPage = %d:%d",
                tmpPage.offset, tmpPage.segId);
            return;
        }
        nextPage = tmpPagePtr->nextPageAddr;
        DbShmemCtxFree(shmCtx, tmpPage);
    }
    DbShmemCtxFree(shmCtx, writeCachePtr);  // 最后释放WriteCacheDescT结构体内存
}

void DoDeleteShmFreePages(
    WriteCacheDescT *desc, int32_t numToScaleIn, DbMemCtxT *shmCtx, ShmemPtrT beginPage, ShmemPtrT inputNextPage)
{
    ShmemPtrT nextPage = inputNextPage;
    ShmemPtrT prePage = beginPage;
    ShmemPtrT mergeBeginPage = desc->mergePageAddr;
    int32_t num = 0;
    while (!(beginPage.segId == nextPage.segId &&
             beginPage.offset == nextPage.offset)) {  // 从游标开始遍历，到遍历到开始页结束
        WCachePageHeadT *tmpPageHead = (WCachePageHeadT *)DbShmPtrToAddr(nextPage);
        if (tmpPageHead == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "convert nextPage worthless. nextPage = %d:%d",
                nextPage.offset, nextPage.segId);
            return;
        }
        if (mergeBeginPage.segId == nextPage.segId &&
            mergeBeginPage.offset == nextPage.offset) {  // 对于合并开始页，不处理，直接下一次循环
            prePage = nextPage;
            nextPage = tmpPageHead->nextPageAddr;
            continue;
        }
        if (IsFreePage(tmpPageHead)) {                                                  // 如果游标页是空闲页
            ShmemPtrT tmpNextPage = tmpPageHead->nextPageAddr;                          // 当前游标页的下一页
            WCachePageHeadT *prePageHead = (WCachePageHeadT *)DbShmPtrToAddr(prePage);  // 当前游标页的上一页
            if (prePageHead == NULL) {
                DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "convert prePage worthless. prePage = %d:%d",
                    prePage.offset, prePage.segId);
                return;
            }
            prePageHead->nextPageAddr = tmpNextPage;  // 当前游标页的上一页 的nextPage 指向 当前游标页的下一页
            DbShmemCtxFree(shmCtx, nextPage);  // 释放当前游标页
            nextPage = tmpNextPage;            // 游标页变成 当前游标页的下一页
            desc->pageNum--;                   // 总页数减1
            num++;                             // 释放页的个数加1
            if (num == numToScaleIn) {         // 释放了足够的空闲页了，结束本次缩容
                break;
            }
        } else {  // 如果游标页不是空闲页，游标往下走一步
            prePage = nextPage;
            nextPage = tmpPageHead->nextPageAddr;
        }
    }
}

void DeleteShmFreePages(WriteCacheDescT *desc, int32_t numToScaleIn)
{
    ShmemPtrT beginPage = desc->writePageAddr;  // 遍历开始页，从写开始页开始遍历
    WCachePageHeadT *pageHead = (WCachePageHeadT *)DbShmPtrToAddr(beginPage);
    if (pageHead == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "convert beginPage worthless. beginPage = %d:%d",
            beginPage.offset, beginPage.segId);
        return;
    }
    ShmemPtrT nextPage = pageHead->nextPageAddr;  // 遍历的游标
    if (beginPage.segId == nextPage.segId && beginPage.offset == nextPage.offset) {
        return;  // 说明当前只有一个页，则直接返回
    }
    DbMemCtxT *shmCtx = (DbMemCtxT *)DbGetShmemCtxById(DB_WRITE_CACHE_CTX_ID, DbGetProcGlobalId());
    if (shmCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "DbGetShmemCtxById DB_WRITE_CACHE_CTX_ID worthless.");
        return;
    }
    DoDeleteShmFreePages(desc, numToScaleIn, shmCtx, beginPage, nextPage);
}

void DeleteFreePages(WriteCacheDescT *desc, int32_t numToScaleIn)
{
    if (numToScaleIn <= 0) {
        return;
    }
    DB_POINTER(desc);
    if (desc->isWriteCacheUseRsm) {
        SeDeleteRsmFreePages(desc, numToScaleIn);
    } else {
        DeleteShmFreePages(desc, numToScaleIn);
    }
}

Status GetShmFreePageNum(WriteCacheDescT *desc, int32_t *freePageNum)
{
    DB_POINTER2(desc, freePageNum);
    ShmemPtrT curPageAddr = desc->writePageAddr;
    const WCachePageHeadT *pageHead = DbShmPtrToAddr(curPageAddr);
    if (pageHead == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "convert writePage worthless. beginPage = %d:%d",
            curPageAddr.offset, curPageAddr.segId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    int32_t freeNum = 0;
    if (pageHead->freeOffset == pageHead->mergeBeginOffset) {
        freeNum++;
    }
    curPageAddr = pageHead->nextPageAddr;
    ShmemPtrT mergePageAddr = desc->mergePageAddr;
    while (!IsShmemPtrEqual(curPageAddr, mergePageAddr)) {  // writePage到mergePage才可能为free，包括writePage本身
        pageHead = DbShmPtrToAddr(curPageAddr);
        if (pageHead == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "convert curPage worthless. curPage = %d:%d",
                curPageAddr.offset, curPageAddr.segId);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        freeNum++;
        curPageAddr = pageHead->nextPageAddr;
    }
    *freePageNum = freeNum;
    return GMERR_OK;
}

Status SeGetFreePageNumNoLock(WriteCacheDescT *desc, int32_t *freePageNum)
{
    DB_POINTER(desc);
    if (desc->isWriteCacheUseRsm) {
        return SeGetRsmFreePageNum(desc, freePageNum);
    } else {
        return GetShmFreePageNum(desc, freePageNum);
    }
}

Status SeGetFreePageNum(WriteCacheDescT *desc, int32_t *freePageNum)
{
    DB_POINTER(desc);
    DbSpinLock(&desc->reqLock);
    Status ret = SeGetFreePageNumNoLock(desc, freePageNum);
    DbSpinUnlock(&desc->reqLock);
    return ret;
}

uint32_t SeDoScaleInForOneWriteCache(WriteCacheDescT *desc)
{
    DB_POINTER(desc);
    int32_t num = 0;
    int32_t freePageNum = 0;
    SHM_CRASHPOINT_ONE(SHM_CRASH_WC_SCALE_IN_BEFORE);
    DbSpinLock(&desc->reqLock);
    SHM_CRASHPOINT_ONE(SHM_CRASH_WC_SCALE_IN_AFTER_REQ_LOCK);
    Status ret = SeGetFreePageNumNoLock(desc, &num);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unsucc to get free page num");
        DbSpinUnlock(&desc->reqLock);
        return freePageNum;
    }
    int32_t oriPageNums = (int32_t)desc->pageNum;
    int32_t percentage = num * PERCENTAGE_BASE / oriPageNums;
    if (percentage > SCAL_IN_THRESHOLD && oriPageNums > 1) {
        int32_t usedPageNum = oriPageNums - num;
        int32_t newTotal =
            (usedPageNum * SCAL_IN_THRESHOLD_DENOMINATOR - 1) / SCAL_IN_THRESHOLD_NUMERATOR + 1;  // 向上取整
        int32_t numToScaleIn = oriPageNums - newTotal;  // 要释放的空闲页个数
        if (numToScaleIn > 0) {
            DeleteFreePages(desc, numToScaleIn);
            freePageNum = desc->pageNum - oriPageNums;
        }
    }
    DbSpinUnlock(&desc->reqLock);
    SHM_CRASHPOINT_ONE(SHM_CRASH_WC_SCALE_IN_AFTER);
    return freePageNum;
}
