/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: se_clustered_hash_label_base.h
 * Description: clustered hash label base header
 * Author: chenjunyu
 * Create: 2022/8/15
 */

#ifndef SE_CLUSTERED_HASH_LABEL_BASE_H
#define SE_CLUSTERED_HASH_LABEL_BASE_H

#include "se_clustered_hash_label_def.h"
#include "db_bit.h"

#ifdef __cplusplus
extern "C" {
#endif

ALWAYS_INLINE static HpTupleAddr TransformChLogicRowId(ChLogicRowId addr)
{
    return *(HpTupleAddr *)(void *)&addr;
}

ALWAYS_INLINE static HpTupleAddr TransformChPhyRowId(ChPhyRowId addr)
{
    return *(HpTupleAddr *)(void *)&addr;
}

ALWAYS_INLINE static ChLogicRowId TransformTupleAddr2LogicRowId(HpTupleAddr addr)
{
    return *(ChLogicRowId *)(void *)&addr;
}

ALWAYS_INLINE static ChPhyRowId TransformTupleAddr2PhyRowId(HpTupleAddr addr)
{
    return *(ChPhyRowId *)(void *)&addr;
}

ALWAYS_INLINE static ChPhyRowId TransformLogicRowId2PhyRowId(ChLogicRowId logicRowId)
{
    return *(ChPhyRowId *)(void *)&logicRowId;
}

ALWAYS_INLINE static ChLogicRowId TransformPhyRowId2LogicRowId(ChPhyRowId phyRowId)
{
    return *(ChLogicRowId *)(void *)&phyRowId;
}

inline static uint32_t ClusteredHashGet26BitHashCode(IndexKeyT hashKey)
{
    return HtGet26BitHashCode(hashKey);
}

StatusInter ClusteredHashCopyTupleBuf(uint8_t *buf, uint32_t bufSize, void *userData);

StatusInter ClusteredHashCopyTupleBuffer(uint8_t *buf, uint32_t bufSize, void *userData);

void ClusteredHashFreeTupleByUser(DbMemCtxT *usrMemCtx, HeapTupleBufT *heapTuple);

void ClusteredHashFreeCtxTupleBuf(ChLabelRunCtxT *runCtx);

void ClusteredHashInitPageOffset(const ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx);

StatusInter ClusteredHashInitPageCtx(ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, HtHashCodeT hashCode);

StatusInter ClusteredHashInitPageCtxAndOffset(ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, HtHashCodeT hashCode);

StatusInter ClusteredHashInitPageCtxAndOffsetWithCheck(
    ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, HtHashCodeT hashCode);

void ClusteredHashPrepareTuple(ChLabelRunCtxT *runCtx, ChRowHeadT *rowHead, ChTupleT *tuple, bool isCopyRowHead);

void ClusteredHashFreeEmptyPage(ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, HashDirSegmentT dirSeg);

void ClusteredHashFreeCachedTupleBuf(ChLabelRunCtxT *runCtx);

StatusInter ClusteredHashFindHashEntry(ChLabelRunCtxT *chLabelRunCtx, ChLabelLookupCtxT *lookUpCtx);

inline static bool TupleBitMapIsSet(uint8_t *bitmap, uint32_t slot)
{
    DB_POINTER(bitmap);
    uint32_t idx = slot >> 3;   // 右移3位，除以8
    uint32_t pos = slot & 0x7;  // 对8取余
    return BitMapIsSet((uint32_t)bitmap[idx], pos);
}

inline static void TupleBitMapSet(uint8_t *bitmap, uint32_t slot)
{
    DB_POINTER(bitmap);
    uint32_t idx = slot >> 3;   // 右移3位，除以8
    uint32_t pos = slot & 0x7;  // 对8取余
    DB_ASSERT(!BitMapIsSet((uint32_t)bitmap[idx], pos) || DbCommonGetWarmReboot());
    bitmap[idx] = (uint8_t)BitMapSet((uint32_t)bitmap[idx], pos);
}

inline static void TupleBitMapUnSet(uint8_t *bitmap, uint32_t slot)
{
    DB_POINTER(bitmap);
    uint32_t idx = slot >> 3;   // 右移3位，除以8
    uint32_t pos = slot & 0x7;  // 对8取余
    DB_ASSERT(BitMapIsSet((uint32_t)bitmap[idx], pos) || DbCommonGetWarmReboot());
    bitmap[idx] = (uint8_t)BitMapUnSet((uint32_t)bitmap[idx], pos);
}

inline static uint8_t *ClusteredHashGetTupleBySlot(uint8_t *tupleHdr, uint32_t tupleSize, uint32_t slot)
{
    DB_POINTER(tupleHdr);
    return tupleHdr + (size_t)(tupleSize * slot);
}

inline static uint32_t ClusteredHashGetFixRowSize(const ChLabelRunCtxT *runCtx)
{
    DB_POINTER(runCtx);
    return runCtx->chLabel->labelCfg.fixRowDataSize;
}

inline static ChRowHeadT *ClusteredHashGetRowHeadByTupleId(
    uint8_t *tupleAddr, uint32_t tupleSize, uint16_t slotExtendSize, uint16_t tupleId)
{
    uint8_t *tupleHead = ClusteredHashGetTupleBySlot(tupleAddr, tupleSize, tupleId);
    return (ChRowHeadT *)(void *)(tupleHead + slotExtendSize);
}

#ifndef NDEBUG
inline static void ClusteredHashCheckEntry(const ClusteredHashEntryT *entry)
{
    // 被清空的slot，entry各值必定赋值为0，否则可能为踩存
    DB_ASSERT(entry->logicSlot == 0);
    DB_ASSERT(entry->phySlot == 0);
    DB_ASSERT(entry->hashCode == 0);
    DB_ASSERT(entry->version == 0);
}
#endif

inline static void ClusteredHashEntryInit(
    ClusteredHashEntryT *entry, HtHashCodeT hashCode, uint16_t tupleId, uint16_t version)
{
#ifndef NDEBUG
    ClusteredHashCheckEntry(entry);
#endif
    entry->hashCode = hashCode;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_ENTRY_INIT_HASHCODE);
    entry->logicSlot = tupleId;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_ENTRY_INIT_LOGIC_SLOT);
    entry->phySlot = tupleId;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_ENTRY_INIT_PHY_SLOT);
    entry->version = version;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_ENTRY_INIT_VERSION);
}

inline static ChLabelPageMetaDataT *ClusteredHashGetPageMetaData(uint8_t *segPageAddr)
{
    DB_POINTER(segPageAddr);
    ChLabelPageMetaDataT *pageData = ((ChLabelPageMetaDataT *)(void *)(segPageAddr));
    return pageData;
}

ALWAYS_INLINE static ChLabelPageMetaDataT *ClusteredHashGetPageMetaDataWithAddr(
    const ChLabelRunCtxT *runCtx, uint8_t *segPageAddr)
{
    DB_POINTER2(runCtx, segPageAddr);
    if (SECUREC_UNLIKELY(runCtx->labelVarInfo->upgradeVersion > 0)) {
        ChLabelPageMetaDataT *pageData = ((ChLabelPageMetaDataT *)(void *)(segPageAddr));
        return pageData;
    } else {
        DB_ASSERT(runCtx->chLabel->dashMeta.metaData.pageRowSize != 0);
        return &runCtx->chLabel->dashMeta.metaData;
    }
}

ALWAYS_INLINE static ChLabelPageMetaDataT *ClusteredHashGetPageMetaDataWithHashCode(
    ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, HtHashCodeT hashCode)
{
    DB_POINTER2(runCtx, pageCtx);
    if (SECUREC_UNLIKELY(runCtx->labelVarInfo->upgradeVersion > 0)) {
        if (pageCtx->segAddr.virtAddr == NULL) {
            (void)ClusteredHashInitPageCtx(runCtx, pageCtx, hashCode);
        }
        DB_ASSERT(pageCtx->pageMetaData.pageRowSize != 0);
        return &pageCtx->pageMetaData;
    } else {
        DB_ASSERT(runCtx->chLabel->dashMeta.metaData.pageRowSize != 0);
        return &runCtx->chLabel->dashMeta.metaData;
    }
}

inline static ChLabelPageMetaDataT *ClusteredHashGetPageMetaDataByPageCtx(ChLabelPageCtxT *pageCtx)
{
    DB_POINTER(pageCtx);
    DB_ASSERT(pageCtx->pageMetaData.pageRowSize != 0);
    return &pageCtx->pageMetaData;
}

ALWAYS_INLINE static ChLabelPageMetaDataT *ClusteredHashGetPageMetaDataByPageId(
    ChLabelPageMemRunCtxT *pageMemRunCtx, PageIdT pageAddr)
{
    DB_POINTER(pageMemRunCtx);
    uint8_t *segPageAddr;
    StatusInter ret =
        ClusteredHashGetSegPage(pageMemRunCtx->mdMgr, pageAddr, pageMemRunCtx->memMgr->base.fileId, &segPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret,
            "unable to get clustered hash seg page when fast fetch page, deviceId = %" PRIu32 ", blockId = %" PRIu32
            ".",
            pageAddr.deviceId, pageAddr.blockId);
        return NULL;
    }
    ChLabelPageMetaDataT *pageData = ((ChLabelPageMetaDataT *)(void *)(segPageAddr));
    return pageData;
}

ALWAYS_INLINE static ChLabelPageMetaDataT *ClusteredHashGetPageMetaAndAddrById(
    ChLabelPageMemRunCtxT *pageMemRunCtx, PageIdT pageAddr, uint8_t **segPageAddr)
{
    DB_POINTER(pageMemRunCtx);
    StatusInter ret =
        ClusteredHashGetSegPage(pageMemRunCtx->mdMgr, pageAddr, pageMemRunCtx->memMgr->base.fileId, segPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret,
            "unable to get clustered hash seg page when fast fetch page, deviceId = %" PRIu32 ", blockId = %" PRIu32
            ".",
            pageAddr.deviceId, pageAddr.blockId);
        return NULL;
    }
    ChLabelPageMetaDataT *pageData = ((ChLabelPageMetaDataT *)(void *)(*segPageAddr));
    return pageData;
}

inline static ChLabelPageHeaderT *ClusteredHashGetPageHeadAndPageAddrByPageId(
    ChLabelPageMemRunCtxT *memRunCtx, PageIdT pageAddr, uint8_t **segPageAddr)
{
    DB_POINTER(memRunCtx);
    StatusInter ret = ClusteredHashGetSegPage(memRunCtx->mdMgr, pageAddr, memRunCtx->memMgr->base.fileId, segPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret,
            "unable to get clustered hash seg page when fast fetch page, deviceId = %" PRIu32 ", blockId = %" PRIu32
            ".",
            pageAddr.deviceId, pageAddr.blockId);
        DB_ASSERT(false);
    }
    return ((ChLabelPageHeaderT *)(void *)(*segPageAddr - CHLABEL_PAGE_HEAD_OFFSET));
}

void ClusteredHashPageLock(ChLabelRunCtxT *runCtx, bool isRead, uint8_t *page, const PageIdT *pageAddr);

void ClusteredHashPageUnlock(ChLabelRunCtxT *runCtx, bool isRead, uint8_t *page);

void ClusteredHashInitSegPage(
    const ClusteredHashLabelT *label, ClusteredHashLabelVarInfoT *labelVarInfo, uint8_t *segPageAddr, uint32_t blockId);

inline static ChBucketMetaT *ClusteredHashGetBucketByIdx(uint8_t *bucketHdr, uint32_t bId, uint32_t bucketSize)
{
    DB_POINTER(bucketHdr);
    return (ChBucketMetaT *)(void *)(bucketHdr + (size_t)(bId * bucketSize));
}

inline static ChBucketMetaT *ClusteredHashGetBucketHdr(uint8_t *bucketHdr, uint32_t bucketSlot)
{
    DB_POINTER(bucketHdr);
    return (ChBucketMetaT *)(void *)(bucketHdr + (size_t)(bucketSlot));
}

inline static ClusteredHashEntryT *ClusteredGetEntryByBucket(ChBucketMetaT *bucket, uint32_t eId, uint32_t entrySize)
{
    DB_POINTER(bucket);
    return (ClusteredHashEntryT *)(void *)((uintptr_t)(bucket + 1) + (size_t)(eId * entrySize));
}

inline static ClusteredHashEntryT *ClusteredGetEntryByStashBucket(
    ChStashBucketMetaT *bucket, uint32_t eId, uint32_t entrySize)
{
    DB_POINTER(bucket);
    return (ClusteredHashEntryT *)(void *)((uintptr_t)(bucket + 1) + (size_t)(eId * entrySize));
}

inline static ClusteredHashEntryT *ClusteredGetStashBucketEntryByOffset(ChStashBucketMetaT *bucket, uint32_t offset)
{
    DB_POINTER(bucket);
    return (ClusteredHashEntryT *)(void *)((uintptr_t)(bucket + 1) + (size_t)(offset));
}

inline static ClusteredHashEntryT *ClusteredGetEntryByOverflowPage(
    uint8_t *firstHashEntry, uint32_t eId, uint32_t entrySize)
{
    DB_POINTER(firstHashEntry);
    return (ClusteredHashEntryT *)(void *)(firstHashEntry + (size_t)(eId * entrySize));
}

inline static bool ClusteredHashStashBucketFull(const ChStashBucketMetaT *bucket)
{
    DB_POINTER(bucket);
    return bucket->count == HASH_ENTRY_PER_STASH_BUCKET;
}

inline static uint8_t *ClusteredHashGetTupleBufByEntry(
    const ChLabelRunCtxT *runCtx, const ClusteredHashEntryT *hashEntry, uint8_t *tupleHdr, uint32_t tupleSize)
{
    DB_POINTER3(runCtx, hashEntry, tupleHdr);
    return ClusteredHashGetTupleBySlot(tupleHdr, tupleSize, hashEntry->phySlot) +
           runCtx->chLabel->labelCfg.slotExtendSize;
}

inline static void ClusteredHashEntryClear(ClusteredHashEntryT *entry)
{
    DB_POINTER(entry);
    entry->hashCode = 0;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_ENTRY_CLEAR_HASHCODE);
    entry->logicSlot = 0;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_ENTRY_CLEAR_LOGIC_SLOT);
    entry->phySlot = 0;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_ENTRY_CLEAR_PHY_SLOT);
    entry->version = 0;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_ENTRY_CLEAR_VERSION);
}

inline static bool ClusteredHashBucketFull(const DashEhLabelMetaT *dashMeta, const ChBucketMetaT *bucket)
{
    DB_POINTER2(dashMeta, bucket);
    return dashMeta->hashEntryPerNormalBucket == bucket->cnt;
}

inline static bool ClusteredHashCheckPrevBitMap(const ChBucketMetaT *bucket)
{
    DB_POINTER(bucket);
    return ((bucket->prevBitMap & DB_MAX_UINT8) != 0);
}

inline static uint32_t ClusteredHashGetOverFlowPageBlockId(ChLabelRunCtxT *runCtx, uint32_t tupleId)
{
    DB_POINTER(runCtx);
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataByPageCtx(&runCtx->pageCtx);
    // 数据存储在溢出页，那么该记录对外的tupleId一定不小于 hashEntryNumPerPage
    DB_ASSERT(tupleId >= pageData->tupleCntPerPage);

    uint32_t blockId = (tupleId - pageData->tupleCntPerPage) / pageData->maxTupleCntPerOverFlowPage;
    return blockId;
}

inline static uint16_t ClusteredHashGetOutputTupleId(ChLabelRunCtxT *ctx, uint16_t slotId)
{
    DB_POINTER(ctx);
    if (SECUREC_LIKELY(!ctx->pageCtx.needVisitOverFlowPage)) {
        return slotId;
    }
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataByPageCtx(&ctx->pageCtx);
    uint16_t tupleId = pageData->tupleCntPerPage + slotId;
    return tupleId + (uint16_t)(ctx->pageCtx.overFlowPageCtx.blockId * pageData->maxTupleCntPerOverFlowPage);
}

inline static uint16_t ClusteredHashGetInnerTupleId(ChLabelRunCtxT *ctx, uint16_t slotId)
{
    DB_POINTER(ctx);
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataByPageCtx(&ctx->pageCtx);
    if (SECUREC_LIKELY(slotId < pageData->tupleCntPerPage)) {
        return slotId;
    }
    uint16_t tupleId = (uint16_t)(slotId - pageData->tupleCntPerPage);
    return tupleId % pageData->maxTupleCntPerOverFlowPage;
}

// isOverFlowPage通常都是false，加unlikely。
inline static uint8_t *ClusteredHashTupleHdr(const ChLabelPageCtxT *pageCtx, bool isOverFlowPage)
{
    DB_POINTER(pageCtx);
    if (SECUREC_UNLIKELY(isOverFlowPage)) {
        return pageCtx->overFlowPageCtx.firstTuple;
    }
    return pageCtx->tupleHdr;
}

inline static uint8_t *ClusteredHashBitMap(const ChLabelPageCtxT *pageCtx)
{
    DB_POINTER(pageCtx);
    if (!pageCtx->needVisitOverFlowPage) {
        return pageCtx->bitmapHdr;
    }
    return pageCtx->overFlowPageCtx.bitmapHdr;
}

inline static void ClusteredHashWLockHcIndexByUpper(ChLabelRunCtxT *runCtx)
{
    DB_POINTER(runCtx);
    DB_ASSERT(runCtx->isHoldHcIndexLockByUpper);
    LabelRWLatchT *labelRWLatch = runCtx->openCfg.labelRWLatch;
    DbSessionCtxT *sessionCtx = &runCtx->seRunCtx->resSessionCtx;
    // 直连写使用 DbRWSpinWLockWithSession 方式对 hash cluster 索引加锁，与服务端不同，区别处理
    if (sessionCtx->isDirectWrite) {
        DbRWSpinWLockWithSession(
            sessionCtx, &labelRWLatch->hcLatch, &labelRWLatch->hcLatchShmPtr, LATCH_ADDR_LABEL_HCLATCH_RWLATCH_SHMEM);
    } else {
        HcWLatchAcquire(labelRWLatch);
    }
}

inline static void ClusteredHashWUnLockHcIndexByUpper(ChLabelRunCtxT *runCtx)
{
    DB_POINTER(runCtx);
    DB_ASSERT(runCtx->isHoldHcIndexLockByUpper);
    LabelRWLatchT *labelRWLatch = runCtx->openCfg.labelRWLatch;
    DbSessionCtxT *sessionCtx = &runCtx->seRunCtx->resSessionCtx;
    // 直连写使用 DbRWSpinTryWLockWithSession 方式对 hash cluster 索引加锁，与服务端不同，解锁要区别处理
    if (sessionCtx->isDirectWrite) {
        LabelWHcLatchReleaseWithSession(labelRWLatch, sessionCtx);
    } else {
        HcWLatchRelease(labelRWLatch);
    };
}

void ClusteredHashInitPageMemRunCtx(
    ClusteredHashLabelT *chLabel, PageIdT *pageAddr, ChLabelPageMemRunCtxT *pageMemRunCtx);

StatusInter ClusteredHashAllocAndInitSegPage(
    ClusteredHashLabelT *label, ChLabelPageMemRunCtxT *memRunCtx, RsmUndoRecordT *rsmUndoRec, DbMemAddrT *segPageInfo);

StatusInter ClusteredHashTableLock(ChLabelRunCtxT *runCtx, bool isRead);

void ClusteredHashTableUnLock(ChLabelRunCtxT *runCtx, bool isRead);

StatusInter ClusteredHashInitPageCtxByRowId(ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, ChLogicRowId logicRowId);

// 页看护，稳定后删除
#ifndef NDEBUG
ALWAYS_INLINE static void CheckNormalBucketBitMap(const ChBucketMetaT *bucket)
{
    DB_POINTER(bucket);
    if (DbCommonGetWarmReboot()) {
        // 恢复阶段可能存在元数据不一致，不用校验
        return;
    }
    // prevBitMap必定为allocBitMap的一个子集
    DB_ASSERT((bucket->allocBitMap & bucket->prevBitMap) == bucket->prevBitMap);
    // 计数与bitmap状态必定一致
    DB_ASSERT((bucket->stashedCnt > 0 && bucket->stashedBitMap > 0) ||
              (bucket->stashedCnt == 0 && bucket->stashedBitMap == 0));
    BitMapCheck(bucket->allocBitMap, (uint32_t)bucket->cnt);
    BitMapCheck(bucket->prevBitMap, (uint32_t)(bucket->cnt - bucket->localEntryCnt));
    BitMapCheck(bucket->allocBitMap & (~bucket->prevBitMap), (uint32_t)bucket->localEntryCnt);
    BitMapCheck(bucket->stashedBitMap, (uint32_t)bucket->stashedCnt);
}

inline static void CheckNormalAndStashBucketBitMap(const ChBucketMetaT *bucket, const ChStashBucketMetaT *stashBucket)
{
    DB_POINTER2(bucket, stashBucket);
    // stashedBitMap必定为allocBitMap的一个子集
    DB_ASSERT((bucket->stashedBitMap & stashBucket->allocBitMap) == bucket->stashedBitMap);
    DB_ASSERT(bucket->stashedCnt <= stashBucket->count);
    BitMapCheck(bucket->allocBitMap, (uint32_t)bucket->cnt);
    BitMapCheck(bucket->prevBitMap, (uint32_t)(bucket->cnt - bucket->localEntryCnt));
    BitMapCheck(bucket->allocBitMap & (~bucket->prevBitMap), (uint32_t)bucket->localEntryCnt);
    BitMapCheck(bucket->stashedBitMap, (uint32_t)bucket->stashedCnt);
    BitMapCheck(stashBucket->allocBitMap, stashBucket->count);
}
#endif

bool ChLabelFindFirstValidTuple(const uint8_t *bitmap, uint32_t bitmapSize, uint32_t startId, uint16_t *targetTupleId);

StatusInter ClusteredHashCreateArray(ClusteredHashLabelT *chLabel);

StatusInter ClusteredHashResetArray(ClusteredHashLabelT *chLabel, RsmUndoRecordT *rsmUndoRec);

StatusInter ClusteredHashDestroyArray(ClusteredHashLabelT *chLabel, RsmUndoRecordT *rsmUndoRec);

StatusInter ClusteredHashCheckEntryKey(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ClusteredHashEntryT *entry);

ClusteredHashEntryT *ClusteredHashMoveEntryOfDifferentSrc(
    const DashEhMemMetaT *dashMemMeta, ChBucketMetaT *src, uint8_t srcSlot, ChBucketMetaT *dst, bool srcMember);

ClusteredHashEntryT *ClusteredHashMoveEntryOfSameSrc(
    const DashEhMemMetaT *dashMemMeta, ChBucketMetaT *src, uint8_t srcSlot, ChBucketMetaT *dst, bool srcMember);

inline static void ChLabelBatchBegin(ChLabelRunCtxT *runCtx, bool isLockForRead)
{
    DB_POINTER(runCtx);
    runCtx->isBatchOperation = true;
}

inline static void ChLabelBatchEnd(ChLabelRunCtxT *runCtx, bool isLockForRead)
{
    DB_POINTER(runCtx);
    runCtx->isBatchOperation = false;
}

inline static __attribute__((always_inline)) void ClusteredHashInitHdr(
    const ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx)
{
    DB_POINTER3(runCtx, pageCtx, pageCtx->segAddr.virtAddr);
    const DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    uint8_t *pageAddr = pageCtx->segAddr.virtAddr;
    pageCtx->pageMetaData = *ClusteredHashGetPageMetaDataWithAddr(runCtx, pageCtx->segAddr.virtAddr);
    pageCtx->bloomFilterHdr = pageAddr + CHLABEL_PAGE_METADATA_OFFSET;
    pageCtx->bitmapHdr = pageAddr + dashMeta->bloomFilterSize + CHLABEL_PAGE_METADATA_OFFSET;
    pageCtx->normalBucketHdr = pageAddr + pageCtx->pageMetaData.bucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    pageCtx->stashBucketHdr = pageAddr + pageCtx->pageMetaData.stashBucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    pageCtx->tupleHdr = pageAddr + pageCtx->pageMetaData.tuplePos + CHLABEL_PAGE_METADATA_OFFSET;
}

inline static __attribute__((always_inline)) void ClusteredHashResetUpgradeVersion(
    ClusteredHashLabelT *label, ClusteredHashLabelVarInfoT *labelVarInfo)
{
    DB_POINTER(label);
    if (labelVarInfo->segPageCount <= 1 && labelVarInfo->upgradeVersion != 0) {
        labelVarInfo->upgradeVersion = 1;
        label->dashMeta.reverseCnt = 0;
    }
}

void ClusteredHashReleaseTuple(ChLabelRunCtxT *runCtx, ChTupleT *tuple);

HeapTupleBufT *ChLabelGetLastCompareTupleBuf(ChLabelRunHdlT chRunHdl);
TupleBufT *ChLabelGetLastCompareTupleBuffer(ChLabelRunHdlT chRunHdl);

// 对size进行32bits对齐后转换为byte
inline static uint32_t ClusteredHashAlign32Bits(uint32_t size)
{
    // uint32_t为4Byte
    static_assert(sizeof(uint32_t) == 4, "sizeof uint32 is not 4");
    // 右移5位，除以32，左移2位，乘以sizeof(uint32_t)
    return (uint32_t)(((size + DB_31BIT) >> 5) << 2);
}

uint32_t ClusteredHashGetOptimalNormalBucketCount(uint32_t availPageSize, uint32_t tupleSize, uint32_t *maxTupleSize);

uint32_t ClusteredHashGetOptimalOverFlowTupleCount(uint32_t availPageSize, uint32_t tupleSize);

void ClusteredHashSetPageMetaData(ChLabelPageMetaDataT *pageData, const ClusteredHashLabelT *label,
    ClusteredHashLabelVarInfoT *labelVarInfo, uint32_t pageRowSize);

void ChLabelSetBatchUserData(ChLabelRunHdlT chRunHdl, const ChLabelDeleteOrReplaceParaT *outData, uint32_t index,
    ChLabelBatchDeleteOrReplaceParaT *userData);

void ChLabelSetBatchUserDataForReplace(ChLabelRunHdlT chRunHdl, const ChLabelDeleteOrReplaceParaT *outData,
    uint32_t index, ChLabelBatchDeleteOrReplaceParaT *userData);

void ChLabelSetStatusMergeNodePara(
    ChLabelDeleteOrReplaceParaT *outData, const ChLabelBatchDeleteOrReplaceParaT *userData);

ChLogicRowId ChLabelGetLogicalAddrByPhysicalAddr(ChLabelRunCtxT *runCtx, ChPhyRowId physicalTupleAddr);

ChPhyRowId ChLabelGetPhysicalAddrByLogicalAddr(ChLabelRunCtxT *runCtx, ChLogicRowId logicalTupleAddr);

inline static void ClusteredHashSetStashedBit(ChBucketMetaT *bucket, uint32_t n)
{
    DB_POINTER(bucket);
#ifndef NDEBUG
    BitMapCheck(bucket->stashedBitMap, bucket->stashedCnt);
    bool bitMapIsSet = BitMapIsSet(bucket->stashedBitMap, n);
    DB_ASSERT(!bitMapIsSet);
#endif
    bucket->stashedBitMap = BitMapSet(bucket->stashedBitMap, n);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_STASHED_ALLOC_UPDATE_BITMAP);
    bucket->stashedCnt++;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_STASHED_ALLOC_UPDATE_CNT);
#ifndef NDEBUG
    BitMapCheck(bucket->stashedBitMap, bucket->stashedCnt);
#endif
}

inline static void ClusteredHashClearStashedBit(ChBucketMetaT *bucket, uint32_t n)
{
    DB_POINTER(bucket);
#ifndef NDEBUG
    BitMapCheck(bucket->stashedBitMap, bucket->stashedCnt);
    bool bitMapIsSet = BitMapIsSet(bucket->stashedBitMap, n);
    DB_ASSERT(bitMapIsSet);
#endif
    bucket->stashedBitMap = BitMapUnSet(bucket->stashedBitMap, n);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_STASHED_FREE_UPDATE_BITMAP);
    DB_ASSERT(bucket->stashedCnt > 0);
    bucket->stashedCnt--;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_STASHED_FREE_UPDATE_CNT);
#ifndef NDEBUG
    BitMapCheck(bucket->stashedBitMap, bucket->stashedCnt);
#endif
}

ALWAYS_INLINE static ClusteredHashLabelVarInfoT *ClusteredHashGetLabelVarInfo(ClusteredHashLabelT *chLabel)
{
    DB_POINTER(chLabel);
    ClusteredHashLabelVarInfoT *labelVarInfo = NULL;
    if (SECUREC_UNLIKELY(chLabel->labelCfg.isUseRsm)) {
        labelVarInfo = (ClusteredHashLabelVarInfoT *)DbShmPtrToAddr(chLabel->labelVarInfoPtr);
        if (labelVarInfo == NULL) {
            SE_ERROR(NULL_VALUE_NOT_ALLOWED_INTER,
                "Get cluster hash table var info unsucc, segid: %" PRIu32 " offset: %" PRIu32 "",
                chLabel->labelVarInfoPtr.segId, chLabel->labelVarInfoPtr.offset);
            return NULL;
        }
    } else {
        labelVarInfo = (ClusteredHashLabelVarInfoT *)(chLabel + 1);
    }
    return labelVarInfo;
}

ALWAYS_INLINE static PageIdT *ClusteredHashGetDirPageAddrs(ClusteredHashLabelVarInfoT *labelVarInfo)
{
    DB_POINTER(labelVarInfo);
    return (PageIdT *)(void *)(labelVarInfo + 1);
}

#ifdef SYS32BITS
StatusInter ClusteredHashGetAllDirPages(ClusteredHashLabelT *chLabel);
#endif

#ifdef __cplusplus
}
#endif

#endif
