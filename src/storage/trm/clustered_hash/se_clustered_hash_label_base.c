/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: se_clustered_hash_label_base.c
 * Description: clustered hash label base implementation
 * Author: zhangyoujian
 * Create: 2022/8/15
 */
#include "se_clustered_hash_label_base.h"
#include "se_clustered_hash_label_overflow_page.h"
#include "se_clustered_hash_label_dml.h"
#include "se_clustered_hash_label_upgrade.h"
#include "se_hash_index.h"
#include "se_heap_base.h"
#include "db_memcpy.h"
#include "se_daemon.h"
#include "se_log.h"
#include "se_clustered_hash_rsm.h"

#ifdef __cplusplus
extern "C" {
#endif

uint32_t ClusteredHashGetOptimalNormalBucketCount(uint32_t availPageSize, uint32_t tupleSize, uint32_t *maxTupleSize)
{
    uint32_t hashEntrySize = (uint32_t)sizeof(ClusteredHashEntryT);
    uint32_t normalBucketSize = (uint32_t)(sizeof(ChBucketMetaT) + HASH_ENTRY_PER_BUCKET * hashEntrySize);
    uint32_t stashBucketSize = (uint32_t)(sizeof(ChStashBucketMetaT) + HASH_ENTRY_PER_STASH_BUCKET * hashEntrySize);

    uint32_t maxNormalBucketCnt = (availPageSize - stashBucketSize) / normalBucketSize;
    uint32_t maxTupleCnt = 0;
    uint32_t maxBitMapSize = 0;  // byte count
    uint32_t bloomFilterSize = 0;
    /* 一个segment页的内存布局
        |                   pageBaseHead                         |
        |                   bloom filter                         |
        |                      bitmap                            |
        | bucket metadata | hashEntry |    ......    | hashEntry |
        |                     ......                             |
        | stash bucket medata | hashEntry | ...... |  hashEntry  |
        | slotExtendSize |           tuple                       |
        |                     ......                             |
    */
    // normal bucket个数不小于MIN_NORMAL_BUCKET_COUNT
    // 一个segment页中hashEntry的总个数和tuple个数不相等, tuple个数约为hashEntry个数的百分比由MAX_LOAT_FACTOR决定
    while (maxNormalBucketCnt >= MIN_NORMAL_BUCKET_COUNT) {
        maxTupleCnt =
            (uint32_t)((maxNormalBucketCnt * HASH_ENTRY_PER_BUCKET + HASH_ENTRY_PER_STASH_BUCKET) * MAX_LOAT_FACTOR);
        maxBitMapSize = (uint32_t)ClusteredHashAlign32Bits(maxTupleCnt);
        bloomFilterSize = maxBitMapSize * BLOOM_FILTER_BYTES_PER_GROUP;
        DB_ASSERT(bloomFilterSize >= maxBitMapSize && bloomFilterSize >= BLOOM_FILTER_BYTES_PER_GROUP);
        uint32_t totalSize = bloomFilterSize + maxBitMapSize + maxNormalBucketCnt * normalBucketSize + stashBucketSize +
                             maxTupleCnt * tupleSize;
        if (totalSize <= availPageSize) {
            return maxNormalBucketCnt;
        }
        maxNormalBucketCnt--;
    }
    uint32_t availTupleSize = availPageSize - (bloomFilterSize + maxBitMapSize + normalBucketSize + stashBucketSize);
    *maxTupleSize = (uint32_t)(availTupleSize / maxTupleCnt);
    return 0;
}

// 获取每个溢出页最佳tuple个数
uint32_t ClusteredHashGetOptimalOverFlowTupleCount(uint32_t availPageSize, uint32_t tupleSize)
{
    uint32_t logicTupleSize = ((uint32_t)sizeof(ClusteredHashEntryT) + tupleSize);
    uint32_t maxTupleCount = availPageSize / logicTupleSize;
    uint32_t maxBitMapSize = 0;
    while (maxTupleCount > 0) {
        maxBitMapSize = (uint32_t)ClusteredHashAlign32Bits(maxTupleCount);
        uint32_t totalSize = maxBitMapSize + maxTupleCount * logicTupleSize;
        if (totalSize <= availPageSize) {
            return maxTupleCount;
        }
        maxTupleCount--;
    }
    return 0;
}

void ClusteredHashSetPageMetaData(ChLabelPageMetaDataT *pageData, const ClusteredHashLabelT *label,
    ClusteredHashLabelVarInfoT *labelVarInfo, uint32_t pageRowSize)
{
    if (SECUREC_LIKELY(labelVarInfo->upgradeVersion == 0)) {
        *pageData = label->dashMeta.metaData;
        return;
    }
    pageData->pageRowSize = pageRowSize;
    pageData->nextPageAddr = (PageIdT){DB_INVALID_UINT32, DB_INVALID_UINT32};
    uint32_t tupleSize = SIZE_ALIGN4((label->labelCfg.slotExtendSize + sizeof(ChRowHeadT) + pageRowSize));
    uint32_t maxTupleSize = 0;
    uint32_t normalBucketCntPerPage =
        ClusteredHashGetOptimalNormalBucketCount(label->dashMeta.availPageSize, tupleSize, &maxTupleSize);
    if (normalBucketCntPerPage < MIN_NORMAL_BUCKET_COUNT) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER,
            "the number of normal bucket is less than %" PRIu32 ", hashNormalBucketNumPerPage: %" PRIu16
            ", cur tupleSize is %" PRIu32 ", max tupleSize is %" PRIu32 ".",
            MIN_NORMAL_BUCKET_COUNT, normalBucketCntPerPage, tupleSize, maxTupleSize);
        DB_ASSERT(false);
    }
    uint16_t tupleCntPerPage =
        (uint16_t)((normalBucketCntPerPage * HASH_ENTRY_PER_BUCKET + HASH_ENTRY_PER_STASH_BUCKET) * MAX_LOAT_FACTOR);
    pageData->tupleSize = tupleSize;
    pageData->bucketPos = (uint16_t)(label->dashMeta.bloomFilterSize + label->dashMeta.bitMapSize);
    uint32_t hashEntrySize = (uint32_t)sizeof(ClusteredHashEntryT);
    uint32_t normalBucketSize = (uint32_t)(sizeof(ChBucketMetaT) + HASH_ENTRY_PER_BUCKET * hashEntrySize);
    uint16_t stashBucketSize = (uint16_t)(sizeof(ChStashBucketMetaT) + HASH_ENTRY_PER_STASH_BUCKET * hashEntrySize);
    pageData->stashBucketPos = (uint16_t)(pageData->bucketPos + (uint16_t)(normalBucketSize * normalBucketCntPerPage));
    pageData->tuplePos = (uint16_t)(pageData->stashBucketPos + stashBucketSize);
    DB_ASSERT(pageData->tuplePos % BYTES_SIZE_PER_WORD == 0);
    pageData->tupleCntPerPage = tupleCntPerPage;
    pageData->hashNormalBucketNumPerPage = (uint16_t)normalBucketCntPerPage;

    uint32_t maxTupleCountPerOverFlowPage =
        ClusteredHashGetOptimalOverFlowTupleCount(label->dashMeta.availPageSize, tupleSize);
    DB_ASSERT(maxTupleCountPerOverFlowPage > 0);
    pageData->maxTupleCntPerOverFlowPage = (uint16_t)maxTupleCountPerOverFlowPage;
    pageData->firstEntryPosPerOverFlowPage = label->dashMeta.bitmapSizePerOverFlowPage;
    pageData->firstTuplePosPerOverFlowPage =
        label->dashMeta.bitmapSizePerOverFlowPage + maxTupleCountPerOverFlowPage * hashEntrySize;
}

StatusInter ClusteredHashTableLock(ChLabelRunCtxT *runCtx, bool isRead)
{
    LabelRWLatchT *labelRWLatch = runCtx->openCfg.labelRWLatch;
    uint32_t labelLatchVersionId = runCtx->openCfg.labelLatchVersionId;
    DB_POINTER(labelRWLatch);
    Status ret = LabelLatchCheckVersion(labelRWLatch, labelLatchVersionId);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret,
            "label have been dropped, labelRWLatchVersionId: %" PRIu32 ", labelLatchVersionId: %" PRIu32 ".",
            labelRWLatch->versionId, labelLatchVersionId);
        return DbGetStatusInterErrno(ret);
    }

    // 客户端直连写使用 DbRWSpinTryRLockWithSession 加锁，与服务端不同，所以此处需要区别处理
    if (runCtx->seRunCtx->resSessionCtx.isDirectWrite) {
        DbSessionCtxT *sessionCtx = &runCtx->seRunCtx->resSessionCtx;
        DbLatchT *rwLatch = &labelRWLatch->rwlatch;
        ShmemPtrT *rwLatchShmPtr = &labelRWLatch->rwlatchShmptr;
        isRead ? DbRWSpinRLockWithSession(sessionCtx, rwLatch, rwLatchShmPtr, LATCH_ADDR_LABELLATCH_RWLATCH_SHMEM) :
                 DbRWSpinWLockWithSession(sessionCtx, rwLatch, rwLatchShmPtr, LATCH_ADDR_LABELLATCH_RWLATCH_SHMEM);
    } else {
        isRead ? DbRWLatchR(&labelRWLatch->rwlatch) : DbRWLatchW(&labelRWLatch->rwlatch);
    }

    ret = LabelLatchCheckVersion(labelRWLatch, labelLatchVersionId);
    if (ret != GMERR_OK) {
        if (isRead) {
            LabelRLatchRelease(labelRWLatch);
        } else {
            LabelWLatchRelease(labelRWLatch);
        }
        DB_LOG_AND_SET_LASERR(ret,
            "label have been dropped, labelRWLatchVersionId: %" PRIu32 ", labelLatchVersionId: %" PRIu32 ".",
            labelRWLatch->versionId, labelLatchVersionId);
        return DbGetStatusInterErrno(ret);
    }
    return STATUS_OK_INTER;
}

inline void ClusteredHashTableUnLock(ChLabelRunCtxT *runCtx, bool isRead)
{
    // 客户端直连写使用 DbRWSpinTryRLockWithSession 加锁，与服务端不同，所以此处需要区别处理
    if (runCtx->seRunCtx->resSessionCtx.isDirectWrite) {
        isRead ? LabelRLatchReleaseWithSession(runCtx->openCfg.labelRWLatch, &runCtx->seRunCtx->resSessionCtx) :
                 LabelWLatchReleaseWithSession(runCtx->openCfg.labelRWLatch, &runCtx->seRunCtx->resSessionCtx);
    } else {
        isRead ? LabelRLatchRelease(runCtx->openCfg.labelRWLatch) : LabelWLatchRelease(runCtx->openCfg.labelRWLatch);
    }
}

inline __attribute__((always_inline)) void ClusteredHashPageLock(
    ChLabelRunCtxT *runCtx, bool isRead, uint8_t *page, const PageIdT *pageAddr)
{
#ifndef NDEBUG
    if (runCtx->labelVarInfo->upgradeVersion == 0) {
        runCtx->lockPage = page;
    }
#endif
    SeRunCtxT *seRunCtx = (SeRunCtxT *)runCtx->seRunCtx;
    PageHeadT *baseHead = (PageHeadT *)(void *)ClusteredHashGetPageHead(page);
    DbSessionCtxT *sessionCtx = &seRunCtx->resSessionCtx;
    LATCH_GET_START_WAITTIMES(isRead, baseHead->lock);
    // 对聚簇容器的数据页加锁, 如果是客户端侧直连读, 则使用session锁, 否则使用普通的锁,
    // 解锁操作由上层函数调用ClusteredHashPageUnlock完成
    if (sessionCtx->isDirectRead) {
        DbRWSpinRLockWithSession(sessionCtx, &baseHead->lock, (const ShmemPtrT *)pageAddr, LATCH_ADDR_PAGEID);
    } else {
        isRead ? DbRWLatchR(&baseHead->lock) : DbRWLatchW(&baseHead->lock);
    }
    LATCH_GET_END_WAITTIMES(isRead, baseHead->lock, sessionCtx->session);
}

inline __attribute__((always_inline)) void ClusteredHashPageUnlock(ChLabelRunCtxT *runCtx, bool isRead, uint8_t *page)
{
#ifndef NDEBUG
    if (runCtx->labelVarInfo->upgradeVersion == 0) {
        runCtx->lockPage = NULL;
    }
#endif
    SeRunCtxT *seRunCtx = (SeRunCtxT *)runCtx->seRunCtx;
    PageHeadT *baseHead = (PageHeadT *)(void *)ClusteredHashGetPageHead(page);
    DbSessionCtxT *sessionCtx = &seRunCtx->resSessionCtx;
    LATCH_GET_START_WAITTIMES(isRead, baseHead->lock);
    if (sessionCtx->isDirectRead) {
        DbRWSpinRUnlockWithSession(sessionCtx, &baseHead->lock);
    } else {
        isRead ? DbRWUnlatchR(&baseHead->lock) : DbRWUnlatchW(&baseHead->lock);
    }
    LATCH_GET_END_WAITTIMES(isRead, baseHead->lock, sessionCtx->session);
}

void ClusteredHashFreeCtxTupleBuf(ChLabelRunCtxT *runCtx)
{
    if (runCtx->heapTuple.buf != NULL) {
        DbDynMemCtxFree(runCtx->seRunCtx->sessionMemCtx, runCtx->heapTuple.buf);
        runCtx->heapTuple = (HeapTupleBufT){0, NULL};
    }
    runCtx->heapTupleAllocSize = 0;
}

void ClusteredHashInitSegPage(
    const ClusteredHashLabelT *label, ClusteredHashLabelVarInfoT *labelVarInfo, uint8_t *segPageAddr, uint32_t blockId)
{
    const DashEhLabelMetaT *dashMeta = &label->dashMeta;
    (void)memset_s(segPageAddr, dashMeta->availPageSize, 0, dashMeta->availPageSize);
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaData(segPageAddr);
    ClusteredHashSetPageMetaData(pageData, label, labelVarInfo, label->labelCfg.fixRowDataSize);
    uint32_t tupleCountPerPage = pageData->tupleCntPerPage;
    ChLabelPageHeaderT *pageHead = ClusteredHashGetPageHead(segPageAddr);
    pageHead->baseHead.entryUsedNum = 0;
    pageHead->baseHead.trmId = label->memMgr.base.fileId;
    pageHead->blockId = blockId;
    pageHead->freeSlotCount = (uint16_t)tupleCountPerPage;
    pageHead->freeSlotId = 0;

    uint8_t *tupleHdr = segPageAddr + pageData->tuplePos + CHLABEL_PAGE_METADATA_OFFSET;
    int32_t tupleSize = (int32_t)pageData->tupleSize;
    int32_t curSlot = -tupleSize;
    // segment页刚初始化的时候每个空闲tuple前面有个临时的slot字段, slot字段存放下一个空闲tuple的tupleId;
    ChLabelSlotId *slot = NULL;
    for (uint16_t tupleId = 0; tupleId < tupleCountPerPage; ++tupleId) {
        curSlot += tupleSize;
        slot = (ChLabelSlotId *)(void *)(tupleHdr + (uint32_t)curSlot);
        slot->nextSlotId = tupleId + 1;
    }
    slot = (ChLabelSlotId *)(void *)(tupleHdr + (uint32_t)curSlot);
    slot->nextSlotId = CLUSTERED_HASH_INVALID_SLOTID;
}

void ClusteredHashInitPageMemRunCtx(
    ClusteredHashLabelT *chLabel, PageIdT *pageAddr, ChLabelPageMemRunCtxT *pageMemRunCtx)
{
    DB_POINTER3(chLabel, pageAddr, pageMemRunCtx);
    pageMemRunCtx->trcPageCache = &chLabel->trcPageCache;
    pageMemRunCtx->memMgr = &chLabel->memMgr;
    pageMemRunCtx->mdMgr = (MdMgrT *)(void *)SeGetPageMgr((uint16_t)chLabel->labelCfg.seInstanceId);
    pageMemRunCtx->pageAddr = pageAddr;
    pageMemRunCtx->version = chLabel->memMgr.base.version;
    (void)memset_s(pageMemRunCtx->dirCache, sizeof(pageMemRunCtx->dirCache), 0, sizeof(pageMemRunCtx->dirCache));
    pageMemRunCtx->pagePtrArrCapacity = chLabel->pageAddrArrCapacity;
    pageMemRunCtx->labelId = chLabel->labelCfg.labelId;
    pageMemRunCtx->needCachePage = false;
}

StatusInter ClusteredHashAllocAndInitSegPage(
    ClusteredHashLabelT *label, ChLabelPageMemRunCtxT *memRunCtx, RsmUndoRecordT *rsmUndoRec, DbMemAddrT *segPageInfo)
{
    ClusteredHashLabelVarInfoT *labelVarInfo = ClusteredHashGetLabelVarInfo(label);
    if (labelVarInfo == NULL) {
        return NULL_VALUE_NOT_ALLOWED_INTER;
    }
    uint32_t blockId = labelVarInfo->segPageCount;
    StatusInter ret = ClusteredHashAllocSegPage(memRunCtx, rsmUndoRec, labelVarInfo->segPageCount, segPageInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret, "clustered hash alloc seg page unsucc.");
        return ret;
    }
    labelVarInfo->segPageCount++;
    ClusteredHashInitSegPage(label, labelVarInfo, segPageInfo->virtAddr, blockId);
    return STATUS_OK_INTER;
}

void ClusteredHashFreeTupleByUser(DbMemCtxT *usrMemCtx, HeapTupleBufT *heapTuple)
{
    if (heapTuple->buf != NULL) {
        DbDynMemCtxFree(usrMemCtx, heapTuple->buf);
        heapTuple->buf = NULL;
    }
    heapTuple->bufSize = 0;
}

void ChLabelFreeTupleBuf(ChLabelRunHdlT chRunHdl, HeapTupleBufT *heapTuple)
{
    // runCtx上的heapTuple只能由其他接口释放, 这里使用DB_ASSERT看护，防止EE层混用
    DB_ASSERT(&chRunHdl->heapTuple != heapTuple);
    // 如果判断buf和runCtx中的buf一致则不释放，由ClusteredHashFreeCtxTupleBuf释放
    if (heapTuple->buf == chRunHdl->heapTuple.buf && heapTuple->buf != NULL) {
        heapTuple->buf = NULL;
        heapTuple->bufSize = 0;
        return;
    }
    ClusteredHashFreeTupleByUser(chRunHdl->openCfg.usrMemCtx, heapTuple);
}

#ifndef NDEBUG
static inline void ChLabelCheckPageCtx(const ChLabelRunHdlT chRunHdl)
{
    ChLabelRunCtxT *runCtx = chRunHdl;
    if (runCtx->pageCtx.dirSeg != NULL && runCtx->labelVarInfo->upgradeVersion == 0 &&
        runCtx->pageCtx.version == runCtx->chLabel->version) {
        DB_ASSERT(runCtx->pageCtx.segAddr.pageAddr.blockId == runCtx->pageCtx.dirSeg->pageAddr.pageAddr.blockId &&
                  runCtx->pageCtx.segAddr.pageAddr.deviceId == runCtx->pageCtx.dirSeg->pageAddr.pageAddr.deviceId);
    }
}
#endif

inline Status ChLabelCheckAddr(const Handle hashClusterHd, HpTupleAddr tupleAddr, bool *isExist)
{
    *isExist = false;
    StatusInter ret = ClusteredHashFetch(hashClusterHd, TransformTupleAddr2LogicRowId(tupleAddr), NULL, NULL);
    if (ret == STATUS_OK_INTER) {
        *isExist = true;
    }
    return DbGetExternalErrno(ret);
}

inline ChLabelOpenCfgT *ChLabelGetOpenCfg(const ChLabelRunHdlT chRunHdl)
{
    return &(chRunHdl->openCfg);
}

inline void ChLabelSetOpenCfgUserData(const ChLabelRunHdlT chRunHdl, void *userData)
{
    chRunHdl->openCfg.userData = userData;
}

inline uint32_t ChLabelGetIndexId(const ChLabelRunHdlT chRunHdl)
{
    return chRunHdl->chLabel->labelCfg.indexId;
}

inline void *ChLabelGetVertex(const ChLabelRunHdlT chRunHdl)
{
    return chRunHdl->openCfg.vertex;
}

inline void ClusteredHashFreeCachedTupleBuf(ChLabelRunCtxT *runCtx)
{
    DB_POINTER(runCtx);
    HeapTupleBufT *cachedHeapTuple = &(runCtx->cachedHeapTuple);
    if (cachedHeapTuple->buf != NULL) {
        DbDynMemCtxFree(runCtx->seRunCtx->sessionMemCtx, cachedHeapTuple->buf);
        *cachedHeapTuple = (HeapTupleBufT){0, NULL};
    }
}

static HeapTupleBufT *ClusteredHashAllocCachedTupleBuf(ChLabelRunCtxT *runCtx, uint32_t bufSize)
{
    DB_POINTER(runCtx);
    // 申请内存存储数据到运行上下文中，由上层调用者调用ClusteredHashFreeCachedTupleBuf释放
    HeapTupleBufT *cachedHeapTuple = &(runCtx->cachedHeapTuple);
    cachedHeapTuple->buf = DbDynMemCtxAlloc(runCtx->seRunCtx->sessionMemCtx, bufSize);
    if (SECUREC_UNLIKELY(cachedHeapTuple->buf == NULL)) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "Clustered hash alloc cached tuple buf unsucc.");
        return NULL;
    }
    cachedHeapTuple->bufSize = bufSize;
    return cachedHeapTuple;
}

ALWAYS_INLINE HeapTupleBufT *ChLabelGetCachedTupleBuf(ChLabelRunHdlT chRunHdl, uint32_t bufSize)
{
    DB_POINTER(chRunHdl);
    if (chRunHdl->cachedHeapTuple.bufSize == bufSize) {
        return &chRunHdl->cachedHeapTuple;
    }
    ClusteredHashFreeCachedTupleBuf(chRunHdl);
    return ClusteredHashAllocCachedTupleBuf(chRunHdl, bufSize);
}

inline HeapTupleBufT *ChLabelGetLastCompareTupleBuf(ChLabelRunHdlT chRunHdl)
{
    return &chRunHdl->heapTuple;
}

inline TupleBufT *ChLabelGetLastCompareTupleBuffer(ChLabelRunHdlT chRunHdl)
{
    return chRunHdl->openCfg.tupleBuf;
}

Status ChLabelCopyTupleBufToRunCtx(ChLabelRunHdlT chRunHdl, const uint8_t *buf, uint32_t bufSize)
{
    DB_POINTER2(chRunHdl, buf);
    if (SECUREC_UNLIKELY(chRunHdl->heapTupleAllocSize < bufSize)) {
        ClusteredHashFreeCtxTupleBuf(chRunHdl);
    }
    if (SECUREC_UNLIKELY(chRunHdl->heapTuple.buf == NULL)) {
        // 申请内存存储数据到运行上下文中，由上层调用者调用ClusteredHashFreeCtxTupleBuf释放
        chRunHdl->heapTuple.buf = DbDynMemCtxAlloc(chRunHdl->seRunCtx->sessionMemCtx, bufSize);
        if (SECUREC_UNLIKELY(chRunHdl->heapTuple.buf == NULL)) {
            SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "hash cluster alloc tuple buf unsucc.");
            return GMERR_OUT_OF_MEMORY;
        }
        chRunHdl->heapTupleAllocSize = bufSize;
    }
    chRunHdl->heapTuple.bufSize = bufSize;
    DbFastMemcpy(chRunHdl->heapTuple.buf, chRunHdl->heapTupleAllocSize, buf, bufSize);
    return GMERR_OK;
}

ALWAYS_INLINE Status ChLabelFetchAndProcInplace(
    const ChLabelRunHdlT chRunHdl, HpTupleAddr tupleAddr, ChLabelReadRowProc func, void *userData)
{
    DB_POINTER3(chRunHdl, func, userData);
    StatusInter ret = ClusteredHashFetch(chRunHdl, TransformTupleAddr2LogicRowId(tupleAddr), func, userData);
    return DbGetExternalErrno(ret);
}

static void ClusteredHashGetLabelStatInner(
    ClusteredHashStatT *stat, ClusteredHashLabelT *chLabel, const SeInstanceT *seInstance)
{
    ClusteredHashLabelVarInfoT *labelVarInfo = ClusteredHashGetLabelVarInfo(chLabel);
    if (labelVarInfo == NULL) {
        return;
    }
    DashEhMemMetaT *dashMemMeta = &chLabel->memMgr.dashMemMeta;
    DashEhLabelMetaT *dashMeta = &chLabel->dashMeta;
    stat->fileId = chLabel->memMgr.base.fileId;
    stat->indexId = chLabel->labelCfg.indexId;
    stat->version = chLabel->version;
    stat->dirPageCount = labelVarInfo->dirPageCount;
    stat->segPageCount = labelVarInfo->segPageCount;
    stat->perPageSize = seInstance->seConfig.pageSize * DB_KIBI;
    stat->hashBucketNumPerPage = dashMeta->metaData.hashNormalBucketNumPerPage;
    stat->hashEntryPerBucket = dashMeta->hashEntryPerNormalBucket;
    stat->hashEntryPerStashBucket = HASH_ENTRY_PER_STASH_BUCKET;
    stat->stashBucketNum = (uint32_t)HASH_STASH_BUCKET_COUNT;
    stat->tupleNumPerOverflowPage = dashMeta->metaData.maxTupleCntPerOverFlowPage;
    stat->overflowPageCount = labelVarInfo->overflowPageCount;
    stat->pageCount = labelVarInfo->dirPageCount + labelVarInfo->segPageCount + labelVarInfo->overflowPageCount;
    stat->heapPageCount = 0;
    stat->pageSize = (uint64_t)stat->perPageSize * stat->pageCount;

    stat->dirDepth = labelVarInfo->dirDepth;
    stat->dirCap = labelVarInfo->dirCap;
    stat->fixRowDataSize = chLabel->labelCfg.fixRowDataSize;
    stat->slotExtendSize = chLabel->labelCfg.slotExtendSize;
    stat->entryUsed = labelVarInfo->tupleUsed + dashMeta->overflowPageTupleUsed;
    stat->totalEntry = labelVarInfo->segPageCount * dashMemMeta->hashEntryNumPerPage +
                       labelVarInfo->overflowPageCount * dashMeta->metaData.maxTupleCntPerOverFlowPage;
    stat->tupleUsed = labelVarInfo->tupleUsed;
    stat->totalTuple = labelVarInfo->segPageCount * dashMeta->metaData.tupleCntPerPage;
    stat->heapItemCnt = chLabel->perfStat.heapItemNum;
    stat->loadFactor = stat->totalEntry == 0 ? 0 : (double)stat->entryUsed / (double)stat->totalEntry;
    stat->tupleUsedRate = stat->totalTuple == 0 ? 0 : (double)stat->tupleUsed / (double)stat->totalTuple;
    stat->tupleUsedRateThreshold = CLUSTERED_HASH_NORMAL_SCALE_IN_THRESHOLD;
    stat->overflowPageTupleUsed = dashMeta->overflowPageTupleUsed;
    stat->usedMemSize = (uint64_t)labelVarInfo->dirCap * (uint64_t)sizeof(HashDirSegmentT) +
                        chLabel->perfStat.phyItemNum * (uint64_t)dashMemMeta->hashEntrySize;
    stat->scaleInBeginCnt = dashMemMeta->scaleInCnt;
#ifndef NDEBUG
    stat->scaleInUpgradePageCnt = dashMeta->scaleInUpgradePageCnt;
#endif
    stat->scaleInProbCnt = dashMeta->scaleInProbCnt;
    stat->scaleInPageCnt = dashMeta->scaleInPageCnt;
    stat->deleteBytes = chLabel->perfStat.deleteBytes;
    stat->writeBytes = chLabel->perfStat.writeBytes;
}

Status ClusteredHashGetLabelStat(ClusteredHashStatT *stat, ShmemPtrT shmAddr)
{
    DB_POINTER(stat);
    ClusteredHashLabelT *chLabel = DbShmPtrToAddr(shmAddr);
    if (chLabel == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "get clustered hash label unsucc. segid: %" PRIu32 " offset: %" PRIu32 "", shmAddr.segId, shmAddr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(DbGetProcGlobalId());
    if (seInstance == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get SE instance unsucc. SE instance %" PRIu16 " novalid",
            DbGetProcGlobalId());
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    (void)memset_s(stat, sizeof(ClusteredHashStatT), 0, sizeof(ClusteredHashStatT));
    ClusteredHashGetLabelStatInner(stat, chLabel, seInstance);
    return GMERR_OK;
}

Status ChLabelSetNewMaxItemNum(ShmemPtrT shmAddr, uint64_t maxItemNum, RsmUndoRecordT *rsmUndoRec)
{
    ClusteredHashLabelT *chLabel = DbShmPtrToAddr(shmAddr);
    if (chLabel == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "get clustered hash label worthless when set max_record_count. segid: %" PRIu32 " offset: %" PRIu32 "",
            shmAddr.segId, shmAddr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    ClusteredHashLabelVarInfoT *labelVarInfo = ClusteredHashGetLabelVarInfo(chLabel);
    if (labelVarInfo == NULL) {
        return NULL_VALUE_NOT_ALLOWED_INTER;
    }
    ClusteredHashRsmConfigUndoLogForMaxItemNum(rsmUndoRec, shmAddr, chLabel->labelCfg.maxItemNum);
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_ALTER_CONFIG_CHANGE_CLUSTERED_HASH_BEFORE);
    chLabel->labelCfg.maxItemNum = maxItemNum;
    labelVarInfo->maxItemNum = maxItemNum;
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_ALTER_CONFIG_CHANGE_CLUSTERED_HASH_AFTER);
    if (rsmUndoRec != NULL) {
        RsmUndoRollBackCurRecord(rsmUndoRec);
    }
    return GMERR_OK;
}

Status ChLabelGetPhyItemNum(ChLabelRunHdlT chRunHdl, uint64_t *count)
{
    DB_POINTER2(chRunHdl, count);
    ClusteredHashPerfStatT *perfStat = &chRunHdl->chLabel->perfStat;
    *count = DbAtomicGet64(&perfStat->phyItemNum);
    return GMERR_OK;
}

inline uint32_t ChLabelGetMaxRowCapacity(const ChLabelRunHdlT chRunHdl)
{
    return chRunHdl->chLabel->labelCfg.fixRowDataSize;
}

inline uint64_t ChLabelGetTrxId(const ChLabelRunHdlT chRunHdl)
{
    return chRunHdl->trxId;
}

StatusInter ClusteredHashCreateArray(ClusteredHashLabelT *chLabel)
{
    if (chLabel->isHashSwizzleArrayInit) {
        return STATUS_OK_INTER;
    }
    Status ret = DbShmArrayInit(
        &chLabel->memMgr.base.hashSwizzleArray, SWIZZLE_SHMARRAY_CAPACITY, sizeof(PageIdT), chLabel->shmemCtxId, NULL);
    if (ret != GMERR_OK) {
        SE_LAST_ERROR(ret, "clustered hash create hashSwizzleArray unsucc.");
        return DbGetStatusInterErrno(ret);
    }
    chLabel->isHashSwizzleArrayInit = true;
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashResetArray(ClusteredHashLabelT *chLabel, RsmUndoRecordT *rsmUndoRec)
{
    if (!chLabel->isHashSwizzleArrayInit) {
        return STATUS_OK_INTER;
    }
    ClusteredHashLabelVarInfoT *labelVarInfo = ClusteredHashGetLabelVarInfo(chLabel);
    if (labelVarInfo == NULL) {
        return NULL_VALUE_NOT_ALLOWED_INTER;
    }
    ChLabelPageMemRunCtxT memRunCtx = {0};
    ClusteredHashInitPageMemRunCtx(chLabel, ClusteredHashGetDirPageAddrs(labelVarInfo), &memRunCtx);
    StatusInter ret = ClusteredHashFreeAllOverFlowPage(&memRunCtx, labelVarInfo->overflowPageCount, rsmUndoRec);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "free all overFlow page unsucc.");
        return ret;
    }
    labelVarInfo->overflowPageCount = 0;
    DbShmArrayReset(&memRunCtx.memMgr->base.hashSwizzleArray);
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashDestroyArray(ClusteredHashLabelT *chLabel, RsmUndoRecordT *rsmUndoRec)
{
    if (!chLabel->isHashSwizzleArrayInit) {
        return STATUS_OK_INTER;
    }
    StatusInter ret = ClusteredHashResetArray(chLabel, rsmUndoRec);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    DbShmArrayDestroy(&chLabel->memMgr.base.hashSwizzleArray, NULL);
    chLabel->isHashSwizzleArrayInit = false;
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashKeyCompare(
    ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, IndexKeyT idxKey, const ClusteredHashEntryT *hashEntry)
{
#ifndef NDEBUG
    // slot对应的标记位必定被置位，若断言失败，可能页缓存被篡改
    bool isSet = TupleBitMapIsSet(ClusteredHashBitMap(pageCtx), hashEntry->phySlot);
    DB_ASSERT(isSet);
#endif
    // indexkey长度不可能为0
    DB_ASSERT(idxKey.keyData != 0);
    uint8_t *tupleHdr = ClusteredHashTupleHdr(&runCtx->pageCtx, pageCtx->needVisitOverFlowPage);
    ChRowHeadT *rowHead = ClusteredHashGetRowHeadByTupleId(
        tupleHdr, runCtx->pageCtx.pageMetaData.tupleSize, runCtx->chLabel->labelCfg.slotExtendSize, hashEntry->phySlot);
    ChTupleT tuple;
    ClusteredHashPrepareTuple(runCtx, rowHead, &tuple, false);
    HeapTupleBufT heapTuple = {.buf = tuple.buf, .bufSize = tuple.pos};

    int32_t cmpRet;
    bool isMatch = false;
    Status ret = runCtx->openCfg.keyCmp(runCtx, idxKey, &heapTuple, &cmpRet, &isMatch);
    if (SECUREC_UNLIKELY(ret != GMERR_OK && !SeIsFilterErrorCode(ret))) {
        return DbGetStatusInterErrno(ret);
    }
    if (isMatch) {
        // trxId的位置比较靠后，如果每次都要赋值会产生比较多的cache-miss，仅在每次找到match的时候再复制出来
        runCtx->trxId = rowHead->trxId;
        return UNIQUE_VIOLATION_INTER;
    }
    return STATUS_OK_INTER;
}

ALWAYS_INLINE StatusInter ClusteredHashCheckEntryKey(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ClusteredHashEntryT *entry)
{
    DB_POINTER3(runCtx, lookUpCtx, entry);
    StatusInter ret = ClusteredHashKeyCompare(runCtx, &runCtx->pageCtx, lookUpCtx->idxKey, entry);
    if (ret == UNIQUE_VIOLATION_INTER) {  // 发生了主键冲突, 说明找到了匹配的key
        lookUpCtx->logicRowId.hashCode = entry->hashCode;
        lookUpCtx->logicRowId.version = entry->version;
        lookUpCtx->logicRowId.tupleId = ClusteredHashGetOutputTupleId(runCtx, entry->logicSlot);
        lookUpCtx->phyRowId.hashCode = entry->hashCode;
        lookUpCtx->phyRowId.version = entry->version;
        lookUpCtx->phyRowId.tupleId = ClusteredHashGetOutputTupleId(runCtx, entry->phySlot);
        lookUpCtx->isFind = true;
        return STATUS_OK_INTER;
    }
    return ret;
}

inline static void ClusteredHashMoveEntryAndFreeSrc(ChBucketMetaT *src, uint8_t srcSlot, const ChBucketMetaT *dst,
    ClusteredHashEntryT *srcEntry, ClusteredHashEntryT *dstEntry)
{
#ifndef NDEBUG
    ClusteredHashCheckEntry(dstEntry);
#endif
    *dstEntry = *srcEntry;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_INSERT_DISPLACE_COPY);
    ClusteredHashNormalBucketFreeSlot(src, srcSlot);
    ClusteredHashEntryClear(srcEntry);
#ifndef NDEBUG
    CheckNormalBucketBitMap(src);
    CheckNormalBucketBitMap(dst);
#endif
}

ClusteredHashEntryT *ClusteredHashMoveEntryOfDifferentSrc(
    const DashEhMemMetaT *dashMemMeta, ChBucketMetaT *src, uint8_t srcSlot, ChBucketMetaT *dst, bool srcMember)
{
#ifndef NDEBUG
    CheckNormalBucketBitMap(src);
    CheckNormalBucketBitMap(dst);
    // 行搬迁，原行对应标记位必定为1
    bool isEq = BitMapIsSet((uint32_t)src->prevBitMap, srcSlot) == srcMember;
    DB_ASSERT(isEq);
#endif
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_INSERT_DISPLACE_BEFORE);
    uint8_t freeSlot = ClusteredHashNormalBucketAllocSlot(dst, !srcMember);
    ClusteredHashEntryT *dstEntry = ClusteredGetEntryByBucket(dst, freeSlot, dashMemMeta->hashEntrySize);
    ClusteredHashEntryT *srcEntry = ClusteredGetEntryByBucket(src, srcSlot, dashMemMeta->hashEntrySize);
    ClusteredHashMoveEntryAndFreeSrc(src, srcSlot, dst, srcEntry, dstEntry);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_INSERT_DISPLACE_FINISHED);
    return dstEntry;
}

ClusteredHashEntryT *ClusteredHashMoveEntryOfSameSrc(
    const DashEhMemMetaT *dashMemMeta, ChBucketMetaT *src, uint8_t srcSlot, ChBucketMetaT *dst, bool srcMember)
{
#ifndef NDEBUG
    CheckNormalBucketBitMap(src);
    CheckNormalBucketBitMap(dst);
    // 行搬迁，原行对应标记位必定为1
    bool isEq = BitMapIsSet((uint32_t)src->prevBitMap, srcSlot) == srcMember;
    DB_ASSERT(isEq);
#endif
    uint8_t freeSlot = ClusteredHashNormalBucketAllocSlot(dst, srcMember);
    ClusteredHashEntryT *dstEntry = ClusteredGetEntryByBucket(dst, freeSlot, dashMemMeta->hashEntrySize);
    ClusteredHashEntryT *srcEntry = ClusteredGetEntryByBucket(src, srcSlot, dashMemMeta->hashEntrySize);
    ClusteredHashMoveEntryAndFreeSrc(src, srcSlot, dst, srcEntry, dstEntry);
    return dstEntry;
}

bool ClusteredHashCanAddScaleInTask(ChLabelRunCtxT *runCtx)
{
    ClusteredHashPerfStatT *perfStat = &runCtx->chLabel->perfStat;
    uint64_t currentTime = (uint64_t)DbToSeconds(DbRdtsc());
    if (currentTime - perfStat->chLastDefragTime < DEFRAGMENTATION_INTERVAL) {
        return false;
    }
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    uint32_t totalTupleNum = runCtx->labelVarInfo->segPageCount * dashMeta->metaData.tupleCntPerPage;
    uint32_t usedTupleNum = runCtx->labelVarInfo->tupleUsed;
    // 整体tuple使用率低于50%才会触发缩容
    if ((double)usedTupleNum < CLUSTERED_HASH_NORMAL_SCALE_IN_THRESHOLD * (double)totalTupleNum &&
        (perfStat->deleteNum > 0 || runCtx->labelVarInfo->upgradeVersion > 0)) {
        dashMeta->scaleInProbCnt++;
        perfStat->chLastDefragTime = currentTime;
        return true;
    }
    return false;
}

// 性能路径关键函数，使用强制内联有益于性能
ALWAYS_INLINE void ClusteredHashInitPageOffset(const ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx)
{
    DB_POINTER3(runCtx, pageCtx, pageCtx->segAddr.virtAddr);
    const DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    const DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;

    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataByPageCtx(pageCtx);
    // 一个页的总bucket数必定不小于最小bucket数
    uint32_t normalBucketNumPerPage = pageData->hashNormalBucketNumPerPage;
    DB_ASSERT(normalBucketNumPerPage >= MIN_NORMAL_BUCKET_COUNT);

    uint32_t targetBucketIdx = ChLabelGetBucketIndexByHashCode(pageCtx->hashCode, dashMemMeta);
    uint32_t prevBucketIdx = targetBucketIdx == 0 ? normalBucketNumPerPage - 1 : targetBucketIdx - 1;

    uint32_t probingBucketIdx = targetBucketIdx + 1;
    uint32_t probingBucketNextIdx = probingBucketIdx + 1;

    pageCtx->targetSlot = ChLabelGetBucketSlotByIndex(targetBucketIdx, dashMeta, (uint16_t)normalBucketNumPerPage);
    pageCtx->neighborSlot = ChLabelGetBucketSlotByIndex(probingBucketIdx, dashMeta, (uint16_t)normalBucketNumPerPage);
    pageCtx->nextNeighborSlot =
        ChLabelGetBucketSlotByIndex(probingBucketNextIdx, dashMeta, (uint16_t)normalBucketNumPerPage);
    pageCtx->prevNeighborSlot = ChLabelGetBucketSlotByIndex(prevBucketIdx, dashMeta, (uint16_t)normalBucketNumPerPage);

    pageCtx->needVisitOverFlowPage = false;
}

ALWAYS_INLINE StatusInter ClusteredHashInitPageCtx(
    ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, HtHashCodeT hashCode)
{
#ifndef NDEBUG
    ChLabelCheckPageCtx(runCtx);
#endif
    uint32_t pattern = HtGetPattern(hashCode, runCtx->labelVarInfo->dirCap);
    if (pageCtx->version != runCtx->chLabel->version || pattern != pageCtx->pattern || pageCtx->dirSeg == NULL ||
        runCtx->labelVarInfo->upgradeVersion != 0) {
        HashDirSegmentT *dirSeg = ClusteredHashGetDirSegmentBySegId(&runCtx->memRunCtx, pattern);
        if (SECUREC_UNLIKELY(dirSeg == NULL)) {
            // 确保init失败后，dirSeg处于空值状态
            pageCtx->dirSeg = NULL;
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
                "unable to get clustered hash dir seg when init pagectx, pattern = %" PRIu32 ".", pattern);
#ifndef NDEBUG
            ChLabelCheckPageCtx(runCtx);
#endif
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        // 获取segment页
        uint8_t *segPageAddr = NULL;
        StatusInter ret = ClusteredHashGetSegPage(
            runCtx->memRunCtx.mdMgr, dirSeg->pageAddr.pageAddr, runCtx->chLabel->memMgr.base.fileId, &segPageAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            // 确保init失败后，dirSeg处于空值状态
            pageCtx->dirSeg = NULL;
            SE_LAST_ERROR(
                ret, "unable to get clustered hash seg page when init pagectx, pattern = %" PRIu32 ".", pattern);
#ifndef NDEBUG
            ChLabelCheckPageCtx(runCtx);
#endif
            return ret;
        }
        pageCtx->dirSeg = dirSeg;
        pageCtx->segAddr = (DbMemAddrT){segPageAddr, pageCtx->dirSeg->pageAddr.pageAddr};
        pageCtx->version = runCtx->chLabel->version;
        pageCtx->pattern = pattern;
    }
    pageCtx->hashCode = hashCode;
    ClusteredHashInitHdr(runCtx, pageCtx);
#ifndef NDEBUG
    ChLabelCheckPageCtx(runCtx);
#endif
    return STATUS_OK_INTER;
}

ALWAYS_INLINE StatusInter ClusteredHashInitPageCtxAndOffset(
    ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, HtHashCodeT hashCode)
{
    StatusInter ret = ClusteredHashInitPageCtx(runCtx, pageCtx, hashCode);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    ClusteredHashInitPageOffset(runCtx, pageCtx);
    return STATUS_OK_INTER;
}

ALWAYS_INLINE StatusInter ClusteredHashInitPageCtxAndOffsetWithCheck(
    ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, HtHashCodeT hashCode)
{
    StatusInter ret = ClusteredHashInitPageCtx(runCtx, pageCtx, hashCode);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    ret = ClusteredHashCheckPageRowSize(runCtx, runCtx->pageCtx.dirSeg, &runCtx->pageCtx.segAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    ClusteredHashInitPageOffset(runCtx, pageCtx);
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashCopyTupleBuf(uint8_t *buf, uint32_t bufSize, void *userData)
{
    ChTupleDataT *tupleData = (ChTupleDataT *)userData;
    // 上层保证已对该memctx赋值
    DB_ASSERT(tupleData->usrMemCtx != NULL);
    // 申请内存存储旧数据，由上层调用者调用ChLabelFreeTupleBuf释放
    tupleData->heapTuple->buf = DbDynMemCtxAlloc(tupleData->usrMemCtx, bufSize);
    if (tupleData->heapTuple->buf == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "hash cluster alloc tuple buf unsucc.");
        return OUT_OF_MEMORY_INTER;
    }
    tupleData->heapTuple->bufSize = bufSize;
    DbFastMemcpy(tupleData->heapTuple->buf, bufSize, buf, bufSize);
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashCopyTupleBuffer(uint8_t *buf, uint32_t bufSize, void *userData)
{
    TupleBufT *tupleBuf = (TupleBufT *)userData;
    return DbGetStatusInterErrno(TupleBufPut(tupleBuf, bufSize, buf));
}

ALWAYS_INLINE static StatusInter ClusteredHashFastFetchNormalPage(
    ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, ChLogicRowId logicRowId, uint8_t **pageAddr)
{
    DB_POINTER2(runCtx, pageAddr);
#ifndef NDEBUG
    ChLabelCheckPageCtx(runCtx);
#endif
    ClusteredHashLabelVarInfoT *labelVarInfo = runCtx->labelVarInfo;
    uint32_t pattern = HtGetPattern(logicRowId.hashCode, labelVarInfo->dirCap);
    if (pageCtx->version != runCtx->chLabel->version || pattern != pageCtx->pattern || pageCtx->dirSeg == NULL ||
        pageCtx->segAddr.virtAddr == NULL || runCtx->labelVarInfo->upgradeVersion > 0) {
        HashDirSegmentT *dirSeg = ClusteredHashGetDirSegmentBySegId(&runCtx->memRunCtx, pattern);
        if (SECUREC_UNLIKELY(dirSeg == NULL)) {
            // 确保init失败后，dirSeg处于空值状态
            pageCtx->dirSeg = NULL;
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
                "unable to get clustered hash dir seg when fast fetch page, pattern = %" PRIu32 ".", pattern);
#ifndef NDEBUG
            ChLabelCheckPageCtx(runCtx);
#endif
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        // 获取segment页
        uint8_t *segPageAddr = NULL;
        StatusInter ret = ClusteredHashGetSegPage(
            runCtx->memRunCtx.mdMgr, dirSeg->pageAddr.pageAddr, runCtx->chLabel->memMgr.base.fileId, &segPageAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            // 确保init失败后，dirSeg处于空值状态
            pageCtx->dirSeg = NULL;
            SE_LAST_ERROR(
                ret, "unable to get clustered hash seg page when fast fetch page, pattern = %" PRIu32 ".", pattern);
#ifndef NDEBUG
            ChLabelCheckPageCtx(runCtx);
#endif
            return ret;
        }
        DB_ASSERT(labelVarInfo->segPageCount != 0);
        *pageAddr = segPageAddr;
        pageCtx->segAddr = (DbMemAddrT){segPageAddr, dirSeg->pageAddr.pageAddr};
        pageCtx->version = runCtx->chLabel->version;
        pageCtx->pattern = pattern;
        pageCtx->dirSeg = dirSeg;
        pageCtx->hashCode = logicRowId.hashCode;
    } else {
        *pageAddr = pageCtx->segAddr.virtAddr;
    }
#ifndef NDEBUG
    ChLabelCheckPageCtx(runCtx);
#endif
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashInitPageCtxByRowId(ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, ChLogicRowId logicRowId)
{
    StatusInter ret;
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataWithHashCode(runCtx, pageCtx, logicRowId.hashCode);
    ClusteredHashLabelVarInfoT *labelVarInfo = runCtx->labelVarInfo;
    if (SECUREC_UNLIKELY(logicRowId.tupleId >= pageData->tupleCntPerPage && labelVarInfo->upgradeVersion == 0)) {
        pageCtx->pageMetaData = *pageData;
        uint32_t blockId = ClusteredHashGetOverFlowPageBlockId(runCtx, logicRowId.tupleId);
        DbMemAddrT pageAddr;
        ret = ClusteredHashGetOverFlowPage(&runCtx->memRunCtx, blockId, labelVarInfo->overflowPageCount, &pageAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "Unable to overflow page by blockId :%" PRIu32, blockId);
            return ret;
        }
        ClusteredHashInitOverFlowPageCtx(runCtx, pageCtx, logicRowId.hashCode, pageAddr);
        return STATUS_OK_INTER;
    }
    pageCtx->needVisitOverFlowPage = false;
    uint8_t *pageAddr = NULL;
    ret = ClusteredHashFastFetchNormalPage(runCtx, pageCtx, logicRowId, &pageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(
            ret, "Unable to init pageCtx by rowId (%" PRIu32 ", %" PRIu32 ")", logicRowId.hashCode, logicRowId.tupleId);
        return ret;
    }
    DB_POINTER(pageAddr);
    pageCtx->segAddr.virtAddr = pageAddr;
    ClusteredHashInitHdr(runCtx, pageCtx);
    return STATUS_OK_INTER;
}

void ClusteredHashPrepareTuple(ChLabelRunCtxT *runCtx, ChRowHeadT *rowHead, ChTupleT *tuple, bool isCopyRowHead)
{
    uint32_t fixRowSize = rowHead->totalBufSize;
    if (isCopyRowHead) {
        tuple->buf = (uint8_t *)(rowHead);
        tuple->pos = (uint32_t)sizeof(ChRowHeadT) + fixRowSize;
        tuple->bufSize = (uint32_t)sizeof(ChRowHeadT) + fixRowSize;
    } else {
        tuple->buf = (uint8_t *)(rowHead + 1);
        tuple->pos = fixRowSize;
        tuple->bufSize = fixRowSize;
    }
    tuple->isAlloc = false;
}

void ClusteredHashReleaseTuple(ChLabelRunCtxT *runCtx, ChTupleT *tuple)
{
    if (tuple->isAlloc && tuple->buf != NULL) {
        DbDynMemCtxFree(runCtx->seRunCtx->sessionMemCtx, tuple->buf);
    }
    *tuple = (ChTupleT){0};
}

inline void ChLabelSetBatchUserDataForReplace(ChLabelRunHdlT chRunHdl, const ChLabelDeleteOrReplaceParaT *outData,
    uint32_t index, ChLabelBatchDeleteOrReplaceParaT *userData)
{
    userData->retPara[index].isFind = outData->isFind;
    userData->retPara[index].isAged = outData->isAged;
    userData->retPara[index].isNeedUpdateViewNum = outData->isNeedUpdateViewNum;
    userData->retPara[index].deleteMark = outData->deleteMark;
    userData->retPara[index].isDrop = outData->isDrop;
#if !defined(NDEBUG) && defined(SYS32BITS)
    // arm32长稳，tupleAddr非4字节对齐可能导致bus error
    (void)memcpy_s(&userData->retPara[index].tupleAddr, sizeof(HpTupleAddr), &outData->tupleAddr, sizeof(HpTupleAddr));
#else
    userData->retPara[index].tupleAddr = outData->tupleAddr;
#endif
    if (outData->isFind) {
        userData->existCount++;
    }
}

void ChLabelSetBatchUserData(ChLabelRunHdlT chRunHdl, const ChLabelDeleteOrReplaceParaT *outData, uint32_t index,
    ChLabelBatchDeleteOrReplaceParaT *userData)
{
    userData->retPara[index].isFind = outData->isFind;
    userData->retPara[index].isAged = outData->isAged;
    userData->retPara[index].isNeedUpdateViewNum = outData->isNeedUpdateViewNum;
    userData->retPara[index].deleteMark = outData->deleteMark;
    userData->retPara[index].isDrop = outData->isDrop;
    userData->retPara[index].tupleAddr = outData->tupleAddr;
    if (outData->isFind) {
        HeapTupleBufT *oldTuple = ChLabelGetLastCompareTupleBuf(chRunHdl);
        if (oldTuple == NULL) {
            DB_LOG_WARN(GMERR_NULL_VALUE_NOT_ALLOWED, "ChLabelSetBatchUserData: oldTuple is null.");
            return;
        }
        DB_ASSERT(userData->oldBufArray[index].bufSize >= oldTuple->bufSize);
        errno_t err = memcpy_s(userData->oldBufArray[index].buf, oldTuple->bufSize, oldTuple->buf, oldTuple->bufSize);
        DB_ASSERT(err == EOK);
        userData->existCount++;
    }
}

inline void ChLabelSetStatusMergeNodePara(
    ChLabelDeleteOrReplaceParaT *outData, const ChLabelBatchDeleteOrReplaceParaT *userData)
{
    outData->insertStatusMergeNode = userData->insertStatusMergeNode;
    outData->updateStatusMergeNode = userData->updateStatusMergeNode;
}

Status ChLabelGetKeyCount(ChLabelRunHdlT chRunHdl, IndexKeyT idxKey, uint64_t *count)
{
    DB_POINTER2(chRunHdl, count);
    HpTupleAddr tupleAddr;
    bool isFind = false;
    *count = 0;
    Status ret = ChLabelLookUp(chRunHdl, idxKey, &tupleAddr, &isFind);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (isFind) {
        *count = 1;
    }
    return GMERR_OK;
}

static ChLogicRowId ClusteredHashGetLogicalRowIdProc(ChLabelRunCtxT *runCtx, ChPhyRowId phyRowId)
{
    HtHashCodeT hashCode = phyRowId.hashCode;
    ChLabelPageCtxT *pageCtx = &runCtx->pageCtx;
    StatusInter ret = ClusteredHashInitPageCtx(runCtx, pageCtx, hashCode);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret, "unable to init pagectx when get logical rowid, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return TransformTupleAddr2LogicRowId(HEAP_INVALID_ADDR);
    }
    ChLabelLookupCtxT lookUpCtx =
        ClusteredHashInitLookUpCtxByRowId(hashCode, phyRowId.version, phyRowId.tupleId, false, false);
    ret = ClusteredHashFindEntryInPageChain(runCtx, &lookUpCtx);
#ifndef NDEBUG
    DB_ASSERT(!lookUpCtx.notExistsInBloomFilter || !lookUpCtx.isFind);
#endif
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret,
            "unable to get physical addr of %" PRIu32 " %" PRIu32 " in page, labelId: %" PRIu32 ", indexId: %" PRIu32,
            hashCode, phyRowId.tupleId, runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return TransformTupleAddr2LogicRowId(HEAP_INVALID_ADDR);
    }
    if (!lookUpCtx.isFind || !TupleBitMapIsSet(pageCtx->bitmapHdr, lookUpCtx.phyRowId.tupleId)) {
        return TransformTupleAddr2LogicRowId(HEAP_INVALID_ADDR);
    }
    return lookUpCtx.logicRowId;
}

static ChPhyRowId ClusteredHashGetPhysicalRowIdProc(ChLabelRunCtxT *runCtx, ChLogicRowId logicRowId)
{
    HtHashCodeT hashCode = logicRowId.hashCode;
    ChLabelPageCtxT *pageCtx = &runCtx->pageCtx;
    StatusInter ret = ClusteredHashInitPageCtx(runCtx, pageCtx, hashCode);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret, "unable to init pagectx when get physical rowid, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return TransformTupleAddr2PhyRowId(HEAP_INVALID_ADDR);
    }
    ChLabelLookupCtxT lookUpCtx =
        ClusteredHashInitLookUpCtxByRowId(hashCode, logicRowId.version, logicRowId.tupleId, true, false);
    ret = ClusteredHashFindEntryInPageChain(runCtx, &lookUpCtx);
#ifndef NDEBUG
    DB_ASSERT(!lookUpCtx.notExistsInBloomFilter || !lookUpCtx.isFind);
#endif
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret,
            "unable to get logical addr of %" PRIu32 " %" PRIu32 " in page, labelId: %" PRIu32 ", indexId: %" PRIu32,
            hashCode, logicRowId.tupleId, runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return TransformTupleAddr2PhyRowId(HEAP_INVALID_ADDR);
    }
    if (!lookUpCtx.isFind || !TupleBitMapIsSet(pageCtx->bitmapHdr, lookUpCtx.phyRowId.tupleId)) {
        return TransformTupleAddr2PhyRowId(HEAP_INVALID_ADDR);
    }
    return lookUpCtx.phyRowId;
}

inline ChPhyRowId ChLabelGetPhysicalAddrByLogicalAddr(ChLabelRunCtxT *runCtx, ChLogicRowId logicalTupleAddr)
{
    DB_POINTER(runCtx);
    ChLabelPageMetaDataT *pageData =
        ClusteredHashGetPageMetaDataWithHashCode(runCtx, &runCtx->pageCtx, logicalTupleAddr.hashCode);
    if (SECUREC_LIKELY((!runCtx->labelVarInfo->hasScaledIn || logicalTupleAddr.tupleId >= pageData->tupleCntPerPage) &&
                       runCtx->labelVarInfo->upgradeVersion == 0)) {
        // 如果未进行过缩容，或者这个rowId属于溢出页，那么逻辑和物理addr相同
        return TransformLogicRowId2PhyRowId(logicalTupleAddr);
    }
    return ClusteredHashGetPhysicalRowIdProc(runCtx, logicalTupleAddr);
}

inline ChLogicRowId ChLabelGetLogicalAddrByPhysicalAddr(ChLabelRunCtxT *runCtx, ChPhyRowId physicalTupleAddr)
{
    DB_POINTER(runCtx);
    ChLabelPageMetaDataT *pageData =
        ClusteredHashGetPageMetaDataWithHashCode(runCtx, &runCtx->pageCtx, physicalTupleAddr.hashCode);
    if (SECUREC_LIKELY((!runCtx->labelVarInfo->hasScaledIn || physicalTupleAddr.tupleId >= pageData->tupleCntPerPage) &&
                       runCtx->labelVarInfo->upgradeVersion == 0)) {
        // 如果未进行过缩容，或者这个rowId属于溢出页，那么逻辑和物理addr相同
        return TransformPhyRowId2LogicRowId(physicalTupleAddr);
    }
    return ClusteredHashGetLogicalRowIdProc(runCtx, physicalTupleAddr);
}

#ifdef SYS32BITS
ALWAYS_INLINE StatusInter ClusteredHashGetAllDirPages(ClusteredHashLabelT *chLabel)
{
    DB_POINTER(chLabel);
    ClusteredHashLabelVarInfoT *labelVarInfo = ClusteredHashGetLabelVarInfo(chLabel);
    if (SECUREC_UNLIKELY(labelVarInfo == NULL)) {
        DB_LOG_ERROR(GMERR_NULL_VALUE_NOT_ALLOWED, "can not get label var info.");
        return NULL_VALUE_NOT_ALLOWED_INTER;
    }
    ChLabelPageMemRunCtxT memRunCtx = {0};
    ClusteredHashInitPageMemRunCtx(chLabel, ClusteredHashGetDirPageAddrs(labelVarInfo), &memRunCtx);
    uint8_t *dirPageAddr = NULL;
    uint32_t dirPageCnt = labelVarInfo->dirPageCount;
    StatusInter ret = STATUS_OK_INTER;
    for (uint32_t i = 0; i < dirPageCnt; i++) {
        ret = ClusteredHashGetDirPage(&memRunCtx, i, &dirPageAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            DB_LOG_ERROR(ret, "can not get dir page, i: %" PRIu32 ", dirPageCnt: %" PRIu32 ".", i, dirPageCnt);
            return ret;
        }
    }
    return STATUS_OK_INTER;
}
#endif

#ifdef __cplusplus
}
#endif
