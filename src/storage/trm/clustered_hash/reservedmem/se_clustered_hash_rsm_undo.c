/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 聚簇容器 rsm undo 接口实现
 * Author:
 * Create: 2024-05-17
 */

#include "se_clustered_hash_rsm_undo.h"
#include "se_clustered_hash_label_base.h"
#include "se_clustered_hash_label_upgrade.h"
#include "se_clustered_hash_access_dm.h"
#include "se_clustered_hash_label_dml.h"
#include "se_clustered_hash_label_stat.h"
#include "db_memcpy.h"

static void ClusteredHashCheckAllDirPage(ChLabelRunCtxT *chRunCtx)
{
    // 校验所有dir页状态，这里必定存在
    uint32_t dirPageCount = chRunCtx->labelVarInfo->dirPageCount;
    StatusInter ret;
    uint8_t *dirPageAddr = NULL;
    for (uint32_t i = 0; i < dirPageCount; i++) {
        ret = ClusteredHashGetDirPage(&chRunCtx->memRunCtx, i, &dirPageAddr);
        if (ret != STATUS_OK_INTER) {
            SE_LAST_ERROR(ret, "Unable to get dir page.");
            DB_ASSERT(false);
            return;
        }
    }
}

static void ClusteredHashGetSlot(
    ChLabelRunCtxT *chRunCtx, uint8_t *segPageAddr, HtHashCodeT hashCode, uint32_t *targetSlot, uint32_t *neighborSlot)
{
    const DashEhMemMetaT *dashMemMeta = &chRunCtx->chLabel->memMgr.dashMemMeta;
    DashEhLabelMetaT *dashMeta = &chRunCtx->chLabel->dashMeta;
    ChLabelPageMetaDataT *pageMetaData = ClusteredHashGetPageMetaDataWithAddr(chRunCtx, segPageAddr);
    uint32_t targetBucketIdx = ChLabelGetBucketIndexByHashCode(hashCode, dashMemMeta);
    *targetSlot = ChLabelGetBucketSlotByIndex(targetBucketIdx, dashMeta, pageMetaData->hashNormalBucketNumPerPage);
    *neighborSlot =
        ChLabelGetBucketSlotByIndex(targetBucketIdx + 1, dashMeta, pageMetaData->hashNormalBucketNumPerPage);
}

static void ClusteredHashUpdateFreeSlotList(uint8_t *tupleHdr, const RsmUndoChInsertRec *record,
    ChLabelPageHeaderT *pageHead, ChLabelPageMetaDataT *pageMetaData)
{
    // 将页头的 freeSlotId 链恢复
    ChLabelSlotId *tupleSlot =
        (ChLabelSlotId *)(void *)ClusteredHashGetTupleBySlot(tupleHdr, pageMetaData->tupleSize, record->freeSlotId);
    DB_ASSERT(pageHead->freeSlotId == record->nextFreeSlotId);
    tupleSlot->nextSlotId = pageHead->freeSlotId;
    pageHead->freeSlotId = record->freeSlotId;
    pageHead->freeSlotCount = record->freeSlotCount;
}

static bool ClusteredHashCheckAndInsertUndoNormalBucket(
    ChLabelRunCtxT *chRunCtx, uint8_t *segPageAddr, const RsmUndoChInsertRec *record, uint32_t slot, bool isTarget)
{
    const DashEhMemMetaT *dashMemMeta = &chRunCtx->chLabel->memMgr.dashMemMeta;
    DashEhLabelMetaT *dashMeta = &chRunCtx->chLabel->dashMeta;
    ChLabelPageHeaderT *pageHead = ClusteredHashGetPageHead(segPageAddr);
    ChLabelPageMetaDataT *pageMetaData = ClusteredHashGetPageMetaDataWithAddr(chRunCtx, segPageAddr);
    uint8_t *bucketHdr = segPageAddr + pageMetaData->bucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    uint8_t *tupleHdr = segPageAddr + pageMetaData->tuplePos + CHLABEL_PAGE_METADATA_OFFSET;
    uint8_t *bitmapHdr = segPageAddr + dashMeta->bloomFilterSize + CHLABEL_PAGE_METADATA_OFFSET;
    ChBucketMetaT *bucket = ClusteredHashGetBucketHdr(bucketHdr, slot);
    for (uint32_t i = 0; i < HASH_ENTRY_PER_BUCKET; i++) {
        if (!BitMapIsSet(bucket->allocBitMap, i)) {
            continue;
        }
        ClusteredHashEntryT *entry = ClusteredGetEntryByBucket(bucket, i, dashMemMeta->hashEntrySize);
        if (entry->hashCode != 0 && entry->hashCode != record->hashCode) {
            continue;
        }
        uint64_t targetBucketMetaData = record->targetBucketMetaData;
        uint64_t neighborBucketMetaData = record->neighborBucketMetaData;
        if (pageHead->freeSlotId == record->nextFreeSlotId) {
            // 清理entry数据
            ClusteredHashEntryClear(entry);
            *bucket = isTarget ? *(ChBucketMetaT *)(void *)&targetBucketMetaData :
                                 *(ChBucketMetaT *)(void *)&neighborBucketMetaData;
            TupleBitMapUnSet(bitmapHdr, record->freeSlotId);
            ClusteredHashUpdateFreeSlotList(tupleHdr, record, pageHead, pageMetaData);
            chRunCtx->labelVarInfo->tupleUsed = record->tupleUsed;
            return true;
        } else {
            // 已经申请entry，但是没有申请tuple slot
            ClusteredHashEntryClear(entry);
            *bucket = isTarget ? *(ChBucketMetaT *)(void *)&targetBucketMetaData :
                                 *(ChBucketMetaT *)(void *)&neighborBucketMetaData;
            return true;
        }
    }
    return false;
}

static bool ClusteredHashCheckAndInsertUndoStashBucket(
    ChLabelRunCtxT *chRunCtx, uint8_t *segPageAddr, const RsmUndoChInsertRec *record, uint32_t targetSlot)
{
    DashEhLabelMetaT *dashMeta = &chRunCtx->chLabel->dashMeta;
    ChLabelPageHeaderT *pageHead = ClusteredHashGetPageHead(segPageAddr);
    ChLabelPageMetaDataT *pageMetaData = ClusteredHashGetPageMetaDataWithAddr(chRunCtx, segPageAddr);
    uint8_t *tupleHdr = segPageAddr + pageMetaData->tuplePos + CHLABEL_PAGE_METADATA_OFFSET;
    uint8_t *bitmapHdr = segPageAddr + dashMeta->bloomFilterSize + CHLABEL_PAGE_METADATA_OFFSET;
    uint8_t *stashBucketHdr = segPageAddr + pageMetaData->stashBucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    ChStashBucketMetaT *stashBucket = (ChStashBucketMetaT *)(void *)stashBucketHdr;
    for (uint32_t i = 0; i < HASH_ENTRY_PER_STASH_BUCKET; i++) {
        if (!BitMapIsSet(stashBucket->allocBitMap, i)) {
            continue;
        }
        ClusteredHashEntryT *entry =
            ClusteredGetEntryByStashBucket(stashBucket, i, chRunCtx->chLabel->memMgr.dashMemMeta.hashEntrySize);
        if (entry->hashCode != 0 && entry->hashCode != record->hashCode) {
            continue;
        }
        uint64_t stashedBucketMetaData = record->stashedBucketMetaData;
        if (pageHead->freeSlotId == record->nextFreeSlotId) {
            // 清理entry数据
            ClusteredHashEntryClear(entry);
            *stashBucket = *(ChStashBucketMetaT *)(void *)&stashedBucketMetaData;
            // 恢复target bucket信息
            uint64_t targetBucketMetaData = record->targetBucketMetaData;
            uint8_t *bucketHdr = segPageAddr + pageMetaData->bucketPos + CHLABEL_PAGE_METADATA_OFFSET;
            ChBucketMetaT *targetBucket = ClusteredHashGetBucketHdr(bucketHdr, targetSlot);
            *targetBucket = *(ChBucketMetaT *)(void *)&targetBucketMetaData;
            TupleBitMapUnSet(bitmapHdr, record->freeSlotId);
            ClusteredHashUpdateFreeSlotList(tupleHdr, record, pageHead, pageMetaData);
            chRunCtx->labelVarInfo->tupleUsed = record->tupleUsed;
            return true;
        } else {
            // 已经申请entry，但是没有申请tuple slot
            ClusteredHashEntryClear(entry);
            *stashBucket = *(ChStashBucketMetaT *)(void *)&stashedBucketMetaData;
            return true;
        }
    }
    return false;
}

void ClusteredHashRsmUndoInsert(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    TrxT *trx = (TrxT *)ctx->trx;
    ChLabelRunCtxT *chRunCtx = TrxGetClusteredHashHandle(trx, opRecord->labelId);
    if (chRunCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get chRunCtx unsucc, labelId:%" PRIu32 "", opRecord->labelId);
        DB_ASSERT(false);
        return;
    }
    // 获取 undolog
    const RsmUndoChInsertRec *record = &opRecord->rsmChInsertRec;
    // 获取目标数据页
    ClusteredHashLabelVarInfoT *labelVarInfo = chRunCtx->labelVarInfo;
    uint32_t pattern = HtGetPattern(record->hashCode, labelVarInfo->dirCap);
    HashDirSegmentT *dirSeg = ClusteredHashGetDirSegmentBySegId(&chRunCtx->memRunCtx, pattern);
    if (SECUREC_UNLIKELY(dirSeg == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Unable to get clustered hash dir seg = %" PRIu32 ".", pattern);
        DB_ASSERT(false);
        return;
    }
    uint8_t *segPageAddr = NULL;
    StatusInter ret = ClusteredHashGetSegPage(
        chRunCtx->memRunCtx.mdMgr, dirSeg->pageAddr.pageAddr, chRunCtx->chLabel->memMgr.base.fileId, &segPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret, "Unable to get clustered hash seg page when init pagectx, pattern = %" PRIu32 ".", pattern);
        DB_ASSERT(false);
        return;
    }
    uint32_t targetSlot;
    uint32_t neighborSlot;
    ClusteredHashGetSlot(chRunCtx, segPageAddr, record->hashCode, &targetSlot, &neighborSlot);
    // 探测target bucket
    if (ClusteredHashCheckAndInsertUndoNormalBucket(chRunCtx, segPageAddr, record, targetSlot, true)) {
        ClusteredHashCheckAllDirPage(chRunCtx);
        return;
    }
    // 探测neibor bucket
    if (ClusteredHashCheckAndInsertUndoNormalBucket(chRunCtx, segPageAddr, record, neighborSlot, false)) {
        ClusteredHashCheckAllDirPage(chRunCtx);
        return;
    }
    // 探测stash bucket
    (void)ClusteredHashCheckAndInsertUndoStashBucket(chRunCtx, segPageAddr, record, targetSlot);
    ClusteredHashCheckAllDirPage(chRunCtx);
}

void ClusteredHashRsmUndoInsertDisplace(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    TrxT *trx = (TrxT *)ctx->trx;
    ChLabelRunCtxT *chRunCtx = TrxGetClusteredHashHandle(trx, opRecord->labelId);
    if (chRunCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get chRunCtx unsucc, labelId:%" PRIu32 "", opRecord->labelId);
        return;
    }
    // 获取 undolog
    RsmUndoChInsertDisplaceRec record = opRecord->rsmChInsertDisplaceRec;
    // 获取目标数据页
    const DashEhMemMetaT *dashMemMeta = &chRunCtx->chLabel->memMgr.dashMemMeta;
    ClusteredHashEntryT entry = {
        .hashCode = record.hashCode,
        .logicSlot = record.logicSlot,
        .phySlot = record.phySlot,
        .version = record.version,
    };
    uint32_t pattern = HtGetPattern(entry.hashCode, chRunCtx->labelVarInfo->dirCap);
    HashDirSegmentT *dirSeg = ClusteredHashGetDirSegmentBySegId(&chRunCtx->memRunCtx, pattern);
    if (SECUREC_UNLIKELY(dirSeg == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Unable to get clustered hash dir seg = %" PRIu32 ".", pattern);
        return;
    }
    uint8_t *segPageAddr = NULL;
    StatusInter ret = ClusteredHashGetSegPage(
        chRunCtx->memRunCtx.mdMgr, dirSeg->pageAddr.pageAddr, chRunCtx->chLabel->memMgr.base.fileId, &segPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret, "Unable to get clustered hash seg page when init pagectx, pattern = %" PRIu32 ".", pattern);
        return;
    }
    ChLabelPageMetaDataT *pageMetaData = ClusteredHashGetPageMetaDataWithAddr(chRunCtx, segPageAddr);
    uint8_t *bucketHdr = segPageAddr + pageMetaData->bucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    ChBucketMetaT *srcBucket = ClusteredHashGetBucketHdr(bucketHdr, record.srcBucketSlot);
    *srcBucket = *(ChBucketMetaT *)(void *)&record.srcBucketMetaData;
    ChBucketMetaT *dstBucket = ClusteredHashGetBucketHdr(bucketHdr, record.dstBucketSlot);
    *dstBucket = *(ChBucketMetaT *)(void *)&record.dstBucketMetaData;
    for (uint32_t i = 0; i < HASH_ENTRY_PER_BUCKET; i++) {
        if (!BitMapIsSet(dstBucket->allocBitMap, i)) {
            ClusteredHashEntryT *tmpEntry = ClusteredGetEntryByBucket(dstBucket, i, dashMemMeta->hashEntrySize);
            ClusteredHashEntryClear(tmpEntry);
        }
    }
    ClusteredHashEntryT *srcEntry =
        ClusteredGetEntryByBucket(srcBucket, record.srcSlot, chRunCtx->chLabel->memMgr.dashMemMeta.hashEntrySize);
    *srcEntry = entry;
}

static void ClusteredHashExpandInitUndo(ChLabelRunCtxT *chRunCtx, const RsmUndoChExpandRec *record, uint32_t labelId)
{
    // 两种情况：1) 还没有申请新页
    //          2) 已经申请新页（不保证所有页已经申请，由底层对账保证最后一个新页是否回收）
    // 这时候新申请的页都还没有挂载到dir下，只是放在expandCtx中，直接释放新页
    // 1 获取新页信息
    PageIdT pageAddr = {.deviceId = record->newDeviceId, .blockId = record->newBlockId};
    if (!DbIsPageIdValid(pageAddr)) {
        return;
    }
    uint8_t *segPageAddr = NULL;
    StatusInter ret = ClusteredHashGetSegPage(
        chRunCtx->memRunCtx.mdMgr, pageAddr, chRunCtx->chLabel->memMgr.base.fileId, &segPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        // 说明还没有申请新页，直接返回
        return;
    }
    // 2 判断是否成链
    // 3 释放新页
    ChLabelPageMetaDataT *newMetaData = ClusteredHashGetPageMetaDataWithAddr(chRunCtx, segPageAddr);
    do {
        if (ClusteredHashFreeOneSegPage(&chRunCtx->memRunCtx, NULL, pageAddr) != STATUS_OK_INTER) {
            SE_LAST_ERROR(INTERNAL_ERROR_INTER, "Free segPage unsucc, labelId:%" PRIu32 "", labelId);
            return;
        }
        if (SECUREC_LIKELY(ClusteredHashIsInvalidPageId(newMetaData->nextPageAddr))) {
            break;
        }
        pageAddr = newMetaData->nextPageAddr;
        newMetaData = ClusteredHashGetPageMetaDataByPageId(&chRunCtx->memRunCtx, pageAddr);
    } while (true);
}

typedef struct ChLabelRsmExpandUndoCtx {
    ChLabelPageMetaDataT *newMetaData;
    ChLabelPageMetaDataT *oldMetaData;
    uint8_t *newTupleHdr;
    uint8_t *oldTupleHdr;
    uint8_t *oldBloomFilterHdr;
    uint8_t *oldBitmapHdr;
    uint8_t *newBitmapHdr;
    uint32_t bucketSize;
    uint32_t hashEntrySize;
} ChLabelRsmExpandUndoCtxT;

static void ClusteredHashExpandUndoRecoverNormalBucketInfo(ChBucketMetaT *oldBk, ChBucketMetaT *newBk)
{
    oldBk->allocBitMap |= newBk->allocBitMap;
    oldBk->cnt = 0;
    uint32_t allocBitMap = oldBk->allocBitMap;
    while (allocBitMap > 0) {
        uint32_t entrySlot = (uint32_t)ffs((int32_t)allocBitMap);
        entrySlot--;
        allocBitMap = BitMapUnSet(allocBitMap, entrySlot);
        oldBk->cnt++;
    }
    oldBk->prevBitMap |= newBk->prevBitMap;
    uint32_t prevCnt = 0;
    uint32_t prevBitMap = oldBk->prevBitMap;
    while (prevBitMap > 0) {
        uint32_t entrySlot = (uint32_t)ffs((int32_t)prevBitMap);
        entrySlot--;
        prevBitMap = BitMapUnSet(prevBitMap, entrySlot);
        prevCnt++;
    }
    oldBk->localEntryCnt = (uint8_t)(oldBk->cnt - prevCnt);
    oldBk->stashedBitMap |= newBk->stashedBitMap;
    oldBk->stashedCnt = 0;
    uint32_t stashedBitMap = oldBk->stashedBitMap;
    while (stashedBitMap > 0) {
        uint32_t entrySlot = (uint32_t)ffs((int32_t)stashedBitMap);
        entrySlot--;
        stashedBitMap = BitMapUnSet(stashedBitMap, entrySlot);
        oldBk->stashedCnt++;
    }
}

static void ClusteredHashExpandUndoRecoverNormalBucket(
    ChLabelRunCtxT *chRunCtx, uint8_t *oldSegPageAddr, uint8_t *newSegPageAddr, ChLabelRsmExpandUndoCtxT *undoCtx)
{
    DashEhMemMetaT *dashMemMeta = &chRunCtx->chLabel->memMgr.dashMemMeta;
    DashEhLabelMetaT *dashMeta = &chRunCtx->chLabel->dashMeta;
    uint8_t *newBucketHdr = newSegPageAddr + undoCtx->newMetaData->bucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    uint8_t *oldBucketHdr = oldSegPageAddr + undoCtx->oldMetaData->bucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    for (uint32_t i = 0; i < undoCtx->newMetaData->hashNormalBucketNumPerPage; i++) {
        ChBucketMetaT *newBk = ClusteredHashGetBucketByIdx(newBucketHdr, i, undoCtx->bucketSize);
        ChBucketMetaT *oldBk = ClusteredHashGetBucketByIdx(oldBucketHdr, i, undoCtx->bucketSize);
        for (uint32_t k = 0; k < HASH_ENTRY_PER_BUCKET; k++) {
            ClusteredHashEntryT *newEntry = ClusteredGetEntryByBucket(newBk, k, undoCtx->hashEntrySize);
            ClusteredHashEntryT *oldEntry = ClusteredGetEntryByBucket(oldBk, k, undoCtx->hashEntrySize);
            if (BitMapIsSet(newBk->allocBitMap, k) || BitMapIsSet(oldBk->allocBitMap, k)) {
                // 新页存在 或者 旧页存在，更新布隆过滤器
                ClusteredHashSetBloomFilter(
                    newEntry->hashCode, undoCtx->oldBloomFilterHdr, dashMeta->bloomFilterSize, dashMemMeta);
            }
            if (!BitMapIsSet(newBk->allocBitMap, k) || BitMapIsSet(oldBk->allocBitMap, k)) {
                // 新页不存在 或者 旧页存在，可以skip
                continue;
            }
            // 新页存在，旧页不存在的数据，需要拷贝到旧页
            *oldEntry = *newEntry;
            ChRowHeadT *newRowHead = ClusteredHashGetRowHeadByTupleId(undoCtx->newTupleHdr,
                undoCtx->newMetaData->tupleSize, chRunCtx->chLabel->labelCfg.slotExtendSize, newEntry->phySlot);
            ChRowHeadT *oldRowHead = ClusteredHashGetRowHeadByTupleId(undoCtx->oldTupleHdr,
                undoCtx->oldMetaData->tupleSize, chRunCtx->chLabel->labelCfg.slotExtendSize, newEntry->phySlot);
            *oldRowHead = *newRowHead;
            DbFastMemcpy((uint8_t *)(oldRowHead + 1), newRowHead->totalBufSize, (uint8_t *)(newRowHead + 1),
                newRowHead->totalBufSize);
            TupleBitMapSet(undoCtx->oldBitmapHdr, newEntry->phySlot);
        }
        ClusteredHashExpandUndoRecoverNormalBucketInfo(oldBk, newBk);
    }
}

static void ClusteredHashExpandUndoRecoverStashBucket(
    ChLabelRunCtxT *chRunCtx, uint8_t *oldSegPageAddr, uint8_t *newSegPageAddr, ChLabelRsmExpandUndoCtxT *undoCtx)
{
    DashEhMemMetaT *dashMemMeta = &chRunCtx->chLabel->memMgr.dashMemMeta;
    DashEhLabelMetaT *dashMeta = &chRunCtx->chLabel->dashMeta;
    uint8_t *newStashBkHdr = newSegPageAddr + undoCtx->newMetaData->stashBucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    uint8_t *oldStashBkHdr = oldSegPageAddr + undoCtx->oldMetaData->stashBucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    ChStashBucketMetaT *newStashBk = (ChStashBucketMetaT *)(void *)newStashBkHdr;
    ChStashBucketMetaT *oldStashBk = (ChStashBucketMetaT *)(void *)oldStashBkHdr;
    for (uint32_t i = 0; i < HASH_ENTRY_PER_STASH_BUCKET; i++) {
        ClusteredHashEntryT *newEntry = ClusteredGetEntryByStashBucket(newStashBk, i, undoCtx->hashEntrySize);
        ClusteredHashEntryT *oldEntry = ClusteredGetEntryByStashBucket(oldStashBk, i, undoCtx->hashEntrySize);
        if (BitMapIsSet(newStashBk->allocBitMap, i) || BitMapIsSet(oldStashBk->allocBitMap, i)) {
            // 新页存在 或者 旧页存在，更新布隆过滤器
            ClusteredHashSetBloomFilter(
                newEntry->hashCode, undoCtx->oldBloomFilterHdr, dashMeta->bloomFilterSize, dashMemMeta);
        }
        if (!BitMapIsSet(newStashBk->allocBitMap, i) || BitMapIsSet(oldStashBk->allocBitMap, i)) {
            // 新页不存在 或者 旧页存在，可以skip
            continue;
        }
        // 新页存在，旧页不存在的数据，需要拷贝到旧页
        *oldEntry = *newEntry;
        ChRowHeadT *newRowHead = ClusteredHashGetRowHeadByTupleId(undoCtx->newTupleHdr, undoCtx->newMetaData->tupleSize,
            chRunCtx->chLabel->labelCfg.slotExtendSize, newEntry->phySlot);
        ChRowHeadT *oldRowHead = ClusteredHashGetRowHeadByTupleId(undoCtx->oldTupleHdr, undoCtx->oldMetaData->tupleSize,
            chRunCtx->chLabel->labelCfg.slotExtendSize, newEntry->phySlot);
        *oldRowHead = *newRowHead;
        DbFastMemcpy((uint8_t *)(oldRowHead + 1), newRowHead->totalBufSize, (uint8_t *)(newRowHead + 1),
            newRowHead->totalBufSize);
        TupleBitMapSet(undoCtx->oldBitmapHdr, newEntry->phySlot);
    }
    oldStashBk->allocBitMap |= newStashBk->allocBitMap;
    oldStashBk->count = 0;
    uint32_t allocBitMap = oldStashBk->allocBitMap;
    while (allocBitMap > 0) {
        uint32_t entrySlot = (uint32_t)ffs((int32_t)allocBitMap);
        entrySlot--;
        allocBitMap = BitMapUnSet(allocBitMap, entrySlot);
        oldStashBk->count++;
    }
}

static void ClusteredHashExpandUndoInitUndoCtx(
    ChLabelRunCtxT *chRunCtx, uint8_t *oldSegPageAddr, uint8_t *newSegPageAddr, ChLabelRsmExpandUndoCtxT *undoCtx)
{
    DashEhLabelMetaT *dashMeta = &chRunCtx->chLabel->dashMeta;
    undoCtx->newMetaData = ClusteredHashGetPageMetaDataWithAddr(chRunCtx, newSegPageAddr);
    undoCtx->oldMetaData = ClusteredHashGetPageMetaDataWithAddr(chRunCtx, oldSegPageAddr);
    undoCtx->newTupleHdr = newSegPageAddr + undoCtx->newMetaData->tuplePos + CHLABEL_PAGE_METADATA_OFFSET;
    undoCtx->oldTupleHdr = oldSegPageAddr + undoCtx->oldMetaData->tuplePos + CHLABEL_PAGE_METADATA_OFFSET;
    undoCtx->newBitmapHdr = newSegPageAddr + dashMeta->bloomFilterSize + CHLABEL_PAGE_METADATA_OFFSET;
    undoCtx->oldBitmapHdr = oldSegPageAddr + dashMeta->bloomFilterSize + CHLABEL_PAGE_METADATA_OFFSET;
    undoCtx->oldBloomFilterHdr = oldSegPageAddr + CHLABEL_PAGE_METADATA_OFFSET;
    undoCtx->bucketSize = dashMeta->bucketSize;
    undoCtx->hashEntrySize = chRunCtx->chLabel->memMgr.dashMemMeta.hashEntrySize;
}

static StatusInter ClusteredHashExpandUndoGetNextPage(
    ChLabelRunCtxT *chRunCtx, uint8_t **oldSegPageAddr, uint8_t **newSegPageAddr, ChLabelRsmExpandUndoCtxT *undoCtx)
{
    StatusInter ret = ClusteredHashGetSegPage(chRunCtx->memRunCtx.mdMgr, undoCtx->oldMetaData->nextPageAddr,
        chRunCtx->chLabel->memMgr.base.fileId, oldSegPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret,
            "unable to get clustered hash seg page when fetch old page, deviceId = %" PRIu32 ", blockId = %" PRIu32
            ", labelId: %" PRIu32 ", indexId: %" PRIu32,
            undoCtx->oldMetaData->nextPageAddr.deviceId, undoCtx->oldMetaData->nextPageAddr.blockId,
            chRunCtx->chLabel->labelCfg.labelId, chRunCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    ret = ClusteredHashGetSegPage(chRunCtx->memRunCtx.mdMgr, undoCtx->newMetaData->nextPageAddr,
        chRunCtx->chLabel->memMgr.base.fileId, newSegPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret,
            "unable to get clustered hash seg page when fetch new page, deviceId = %" PRIu32 ", blockId = %" PRIu32
            ", labelId: %" PRIu32 ", indexId: %" PRIu32,
            undoCtx->newMetaData->nextPageAddr.deviceId, undoCtx->newMetaData->nextPageAddr.blockId,
            chRunCtx->chLabel->labelCfg.labelId, chRunCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    return STATUS_OK_INTER;
}

static void ClusteredHashExpandUndoRecoverOldPages(ChLabelRunCtxT *chRunCtx, const RsmUndoChExpandRec *record,
    uint8_t *oldSegPageAddr, uint8_t *newSegPageAddr, uint32_t labelId)
{
    PageIdT newPageAddr = {.deviceId = record->newDeviceId, .blockId = record->newBlockId};
    ChLabelRsmExpandUndoCtxT undoCtx = {0};
    StatusInter ret;
    do {
        ClusteredHashExpandUndoInitUndoCtx(chRunCtx, oldSegPageAddr, newSegPageAddr, &undoCtx);
        // 遍历新页数据
        ClusteredHashExpandUndoRecoverNormalBucket(chRunCtx, oldSegPageAddr, newSegPageAddr, &undoCtx);
        ClusteredHashExpandUndoRecoverStashBucket(chRunCtx, oldSegPageAddr, newSegPageAddr, &undoCtx);
        ChLabelPageHeaderT *pageHead = ClusteredHashGetPageHead(oldSegPageAddr);
        pageHead->freeSlotCount = 0;
        pageHead->freeSlotId = CLUSTERED_HASH_INVALID_SLOTID;
        int32_t curSlot = -(int32_t)undoCtx.oldMetaData->tupleSize;
        for (uint32_t slotId = 0; slotId < undoCtx.oldMetaData->tupleCntPerPage; ++slotId) {
            curSlot += (int32_t)undoCtx.oldMetaData->tupleSize;
            if (TupleBitMapIsSet(undoCtx.oldBitmapHdr, slotId)) {
                continue;
            }
            pageHead->freeSlotCount++;
            ChLabelSlotId *freeSlot = (ChLabelSlotId *)(void *)(undoCtx.oldTupleHdr + (DB_UINTPTR)(uint32_t)curSlot);
            freeSlot->nextSlotId = pageHead->freeSlotId;
            pageHead->freeSlotId = (uint16_t)slotId;
        }
        if (SECUREC_LIKELY(ClusteredHashIsInvalidPageId(undoCtx.newMetaData->nextPageAddr))) {
            break;
        }
        ret = ClusteredHashExpandUndoGetNextPage(chRunCtx, &oldSegPageAddr, &newSegPageAddr, &undoCtx);
        if (ret != STATUS_OK_INTER) {
            return;
        }
        PageIdT freePageAddr = newPageAddr;
        newPageAddr = undoCtx.newMetaData->nextPageAddr;
        undoCtx.newMetaData = ClusteredHashGetPageMetaDataByPageId(&chRunCtx->memRunCtx, newPageAddr);
        // 释放新页
        // 恢复流程不设置rsmUndo
        if (ClusteredHashFreeOneSegPage(&chRunCtx->memRunCtx, NULL, freePageAddr) != STATUS_OK_INTER) {
            SE_LAST_ERROR(INTERNAL_ERROR_INTER, "Free segPage unsucc, labelId:%" PRIu32 "", labelId);
            DB_ASSERT(false);
            return;
        }
    } while (true);
    // 恢复流程不设置rsmUndo
    if (ClusteredHashFreeOneSegPage(&chRunCtx->memRunCtx, NULL, newPageAddr) != STATUS_OK_INTER) {
        SE_LAST_ERROR(INTERNAL_ERROR_INTER, "Free segPage unsucc, labelId:%" PRIu32 "", labelId);
        DB_ASSERT(false);
        return;
    }
}

static void ClusteredHashExpandAllocSegPageUndo(
    ChLabelRunCtxT *chRunCtx, const RsmUndoChExpandRec *record, uint32_t labelId)
{
    // 已经申请完新页，还没完成数据分裂，需要遍历新页数据，插入旧页中，然后释放新页
    // 恢复dirSeg内容
    HashDirSegmentT *dirSeg = ClusteredHashGetDirSegmentBySegId(&chRunCtx->memRunCtx, record->oldPattern);
    if (SECUREC_UNLIKELY(dirSeg == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Unable to get clustered hash dir seg, pattern = %" PRIu32 ".",
            record->oldPattern);
        DB_ASSERT(false);
        return;
    }
    dirSeg->pattern = record->oldPattern;
    dirSeg->segDepth = record->oldDirSegDepth;
    // 分别获取新页和旧页
    PageIdT oldPageAddr = {.deviceId = record->oldDeviceId, .blockId = record->oldBlockId};
    PageIdT newPageAddr = {.deviceId = record->newDeviceId, .blockId = record->newBlockId};
    uint8_t *oldSegPageAddr = NULL;
    uint8_t *newSegPageAddr = NULL;
    StatusInter ret = ClusteredHashGetSegPage(
        chRunCtx->memRunCtx.mdMgr, oldPageAddr, chRunCtx->chLabel->memMgr.base.fileId, &oldSegPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get old segPage unsucc, labelId:%" PRIu32 "", labelId);
        DB_ASSERT(false);
        return;
    }
    ret = ClusteredHashGetSegPage(
        chRunCtx->memRunCtx.mdMgr, newPageAddr, chRunCtx->chLabel->memMgr.base.fileId, &newSegPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get new segPage unsucc, labelId:%" PRIu32 "", labelId);
        DB_ASSERT(false);
        return;
    }
    // 遍历新页数据，将其插入旧页中，并释放新页
    ClusteredHashExpandUndoRecoverOldPages(chRunCtx, record, oldSegPageAddr, newSegPageAddr, labelId);
}

static void ClusteredHashExpandDirUndo(ChLabelRunCtxT *chRunCtx, const RsmUndoChExpandRec *record, uint32_t labelId);
static void ClusteredHashExpandSplitBucketUndo(
    ChLabelRunCtxT *chRunCtx, const RsmUndoChExpandRec *record, uint32_t labelId)
{
    // 已经完成数据分裂，但是还没有刷新dir或扩容dir
    // 1 获取对应dirSeg
    ClusteredHashLabelVarInfoT *labelVarInfo = chRunCtx->labelVarInfo;
    uint32_t pattern = HtGetPattern(record->hashCode, labelVarInfo->dirCap);
    HashDirSegmentT *dirSeg = ClusteredHashGetDirSegmentBySegId(&chRunCtx->memRunCtx, pattern);
    if (SECUREC_UNLIKELY(dirSeg == NULL)) {
        SE_LAST_ERROR(
            UNEXPECTED_NULL_VALUE_INTER, "Unable to get clustered hash dir seg, pattern = %" PRIu32 ".", pattern);
        DB_ASSERT(false);
        return;
    }
    // 2 判断是否走刷新dir还是走扩容dir
    if (dirSeg->segDepth <= labelVarInfo->dirDepth) {
        UpdateParaT updatePara = {
            .segDepth = dirSeg->segDepth,
            .dirDepth = labelVarInfo->dirDepth,
            .dirCapacity = labelVarInfo->dirCap,
            .segId = pattern,
            .segIdOld = pattern,
        };
        ChLabelExpandCtxT expandCtx = {0};
        uint32_t patternSpan = 1u << record->oldDirSegDepth;
        uint32_t newPattern = record->hashCode % patternSpan;
        expandCtx.newDir.pattern = newPattern + patternSpan;
        expandCtx.newDir.segDepth = dirSeg->segDepth;
        expandCtx.newDir.pageAddr.pageAddr.deviceId = record->newDeviceId;
        expandCtx.newDir.pageAddr.pageAddr.blockId = record->newBlockId;
        ClusteredHashFreshDirectory(chRunCtx, &expandCtx, updatePara, *dirSeg);
    } else {
        ClusteredHashExpandDirUndo(chRunCtx, record, labelId);
    }
}

static void ClusteredHashExpandDirWithoutNewPageUndo(
    ChLabelRunCtxT *chRunCtx, const RsmUndoChExpandRec *record, ChLabelExpandCtxT *expandCtx, uint32_t labelId)
{
    DashEhMemMetaT *dashMemMeta = &chRunCtx->chLabel->memMgr.dashMemMeta;
    ClusteredHashLabelVarInfoT *labelVarInfo = chRunCtx->labelVarInfo;
    uint32_t oldDirCap = record->oldDirCap;
    uint32_t newDirCap = oldDirCap << 1;
    uint32_t hashSegNumPerPage = dashMemMeta->hashSegNumPerPage;
    uint32_t newSegId = record->oldPattern + oldDirCap;
    StatusInter ret;
    for (uint32_t i = oldDirCap; i < newDirCap; i++) {
        uint32_t dirPageId = i / hashSegNumPerPage;
        uint32_t dirSlotId = i % hashSegNumPerPage;
        uint8_t *dirPageAddr = NULL;
        ret = ClusteredHashGetDirPage(&chRunCtx->memRunCtx, dirPageId, &dirPageAddr);
        if (ret != STATUS_OK_INTER) {
            DB_ASSERT(false);
            return;
        }
        HashDirSegmentT *newSeg = (HashDirSegmentT *)(void *)(dirPageAddr + dirSlotId * sizeof(HashDirSegmentT));
        // 不是扩展的dir，获取对应的oldDir
        if (i != newSegId) {
            dirPageId = (i - oldDirCap) / hashSegNumPerPage;
            dirSlotId = (i - oldDirCap) % hashSegNumPerPage;
            ret = ClusteredHashGetDirPage(&chRunCtx->memRunCtx, dirPageId, &dirPageAddr);
            if (ret != STATUS_OK_INTER) {
                DB_ASSERT(false);
                return;
            }
            *newSeg = *(HashDirSegmentT *)(void *)(dirPageAddr + dirSlotId * sizeof(HashDirSegmentT));
        } else {
            *newSeg = expandCtx->newDir;
        }
    }
    labelVarInfo->dirDepth = record->oldDirDepth + 1;
    labelVarInfo->dirCap = newDirCap;
    chRunCtx->chLabel->version++;
    // 处理旧页数据
    ret = ClusteredHashProcOldPageChain(chRunCtx);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "Unable to proc old page, labelId = %" PRIu32 ".", labelId);
        DB_ASSERT(false);
        return;
    }
}

static void ClusteredHashExpandDirWithNewPageUndo(ChLabelRunCtxT *chRunCtx, const RsmUndoChExpandRec *record,
    ChLabelExpandCtxT *expandCtx, uint32_t newDirCap, uint32_t labelId)
{
    ClusteredHashLabelVarInfoT *labelVarInfo = chRunCtx->labelVarInfo;
    StatusInter ret;
    if (labelVarInfo->dirDepth == record->oldDirDepth) {
        // 不考虑已经申请新页后还没更新memRunCtx.pageAddr情况，由对账处理
        for (uint32_t i = record->oldDirPageCnt; i <= labelVarInfo->dirPageCount; ++i) {
            if (!DbIsPageIdValid(chRunCtx->memRunCtx.pageAddr[i])) {
                continue;
            }
            // 说明已经申请了新页，释放掉
            uint8_t *dirPageAddr = NULL;
            ret = ClusteredHashGetDirPage(&chRunCtx->memRunCtx, i, &dirPageAddr);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                SE_LAST_ERROR(ret, "get dir page unsucc, labelId = %" PRIu32 ".", labelId);
                return;
            }
            ChLabelPageHeaderT *pageHead = ClusteredHashGetPageHead(dirPageAddr);
            Status tmpRet = ChFreePageToMd(&chRunCtx->memRunCtx, pageHead->baseHead.addr, NULL);
            if (SECUREC_UNLIKELY(tmpRet != GMERR_OK)) {
                SE_LAST_ERROR(DbGetInternalErrno(tmpRet), "Free dir page unsucc, labelId = %" PRIu32 ".", labelId);
                return;
            }
        }
        labelVarInfo->dirPageCount = record->oldDirPageCnt;
        // 增加约束恢复阶段不申请新页，直接回滚
        expandCtx->newPageCtx.segAddr.pageAddr = expandCtx->newDir.pageAddr.pageAddr;
        ret = ClusteredHashGetSegPage(chRunCtx->memRunCtx.mdMgr, expandCtx->newPageCtx.segAddr.pageAddr,
            chRunCtx->chLabel->memMgr.base.fileId, &expandCtx->newPageCtx.segAddr.virtAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_LAST_ERROR(ret, "get seg page unsucc, labelId = %" PRIu32 ".", labelId);
            return;
        }
        ClusteredHashRollBackSegmentSplit(chRunCtx, expandCtx);
    } else {
        labelVarInfo->dirCap = newDirCap;
        chRunCtx->chLabel->version++;
        // 已经完成扩容，还没处理旧页 或者 已经处理完旧页
        // 重复处理旧页数据不影响
        ret = ClusteredHashProcOldPageChain(chRunCtx);
        if (ret != STATUS_OK_INTER) {
            SE_LAST_ERROR(ret, "Unable to proc old page, labelId = %" PRIu32 ".", labelId);
            DB_ASSERT(false);
            return;
        }
    }
}

static StatusInter ClusteredHashInitPageCtxWithPattern(
    ChLabelRunCtxT *chRunCtx, ChLabelPageCtxT *pageCtx, uint32_t pattern)
{
    pageCtx->dirSeg = ClusteredHashGetDirSegmentBySegId(&chRunCtx->memRunCtx, pattern);
    if (SECUREC_UNLIKELY(pageCtx->dirSeg == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "unable to get clustered hash dir seg when init pagectx, pattern = %" PRIu32 ".", pattern);
        DB_ASSERT(false);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    // 获取segment页
    uint8_t *segPageAddr = NULL;
    StatusInter ret = ClusteredHashGetSegPage(chRunCtx->memRunCtx.mdMgr, pageCtx->dirSeg->pageAddr.pageAddr,
        chRunCtx->chLabel->memMgr.base.fileId, &segPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret, "unable to get clustered hash seg page when init pagectx, pattern = %" PRIu32 ".", pattern);
        DB_ASSERT(false);
        return ret;
    }
    pageCtx->segAddr = (DbMemAddrT){segPageAddr, pageCtx->dirSeg->pageAddr.pageAddr};
    pageCtx->version = chRunCtx->chLabel->version;
    pageCtx->pattern = pattern;
    ClusteredHashInitHdr(chRunCtx, pageCtx);
    return STATUS_OK_INTER;
}

static void ClusteredHashExpandDirUndo(ChLabelRunCtxT *chRunCtx, const RsmUndoChExpandRec *record, uint32_t labelId)
{
    // 走扩容dir，有4种情况:
    // 1) 可能还没开始扩容 2) 已经开始扩容，还没完成扩容 3) 已经完成扩容，还没处理旧页 4) 已经处理完旧页
    StatusInter ret = ClusteredHashInitPageCtxWithPattern(chRunCtx, &chRunCtx->pageCtx, record->oldPattern);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "Unable to init clustered hash pageCtx, hashCode = %" PRIu32 ".", record->hashCode);
        DB_ASSERT(false);
        return;
    }
    ChLabelExpandCtxT expandCtx = {0};
    uint32_t patternSpan = 1u << record->oldDirSegDepth;
    uint32_t newPattern = record->hashCode % patternSpan;
    expandCtx.newDir.pageAddr.pageAddr = (PageIdT){record->newDeviceId, record->newBlockId};
    expandCtx.newDir.segDepth = record->oldDirSegDepth + 1;
    expandCtx.newDir.pattern = newPattern + patternSpan;
    DashEhMemMetaT *dashMemMeta = &chRunCtx->chLabel->memMgr.dashMemMeta;
    uint32_t hashSegNumPerPage = dashMemMeta->hashSegNumPerPage;
    uint32_t newDirCap = record->oldDirCap << 1;
    uint32_t newTotalDirPageCnt = (newDirCap + hashSegNumPerPage - 1) / hashSegNumPerPage;
    if (newTotalDirPageCnt == record->oldDirPageCnt) {
        // 说明no need申请新页
        ClusteredHashExpandDirWithoutNewPageUndo(chRunCtx, record, &expandCtx, labelId);
    } else {
        ClusteredHashExpandDirWithNewPageUndo(chRunCtx, record, &expandCtx, newDirCap, labelId);
    }
}

void ClusteredHashRsmUndoExpand(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    TrxT *trx = (TrxT *)ctx->trx;
    ChLabelRunCtxT *chRunCtx = TrxGetClusteredHashHandle(trx, opRecord->labelId);
    if (chRunCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get chRunCtx unsucc, labelId:%" PRIu32 "", opRecord->labelId);
        DB_ASSERT(false);
        return;
    }
    const RsmUndoChExpandRec *record = &opRecord->rsmChExpandRec;
    switch (record->expandType) {
        case CLUSTERED_HASH_EXPAND_INIT: {
            ClusteredHashExpandInitUndo(chRunCtx, record, opRecord->labelId);
            chRunCtx->labelVarInfo->segPageCount = record->segPageCnt;
            break;
        }
        case CLUSTERED_HASH_EXPAND_ALLOC_SEGPAGE: {
            ClusteredHashExpandAllocSegPageUndo(chRunCtx, record, opRecord->labelId);
            chRunCtx->labelVarInfo->segPageCount = record->segPageCnt;
            break;
        }
        case CLUSTERED_HASH_SPILIT_BUCKET: {
            ClusteredHashExpandSplitBucketUndo(chRunCtx, record, opRecord->labelId);
            break;
        }
        case CLUSTERED_HASH_EXPAND_DIR: {
            ClusteredHashExpandDirUndo(chRunCtx, record, opRecord->labelId);
            break;
        }
        case CLUSTERED_HASH_EXPAND_FINISHED:
            // 已经完成扩容流程
            break;
        default:
            DB_ASSERT(false);
            break;
    }
    ClusteredHashCheckAllDirPage(chRunCtx);
}

static void ClusteredHashRsmUndoFreshDir(ChLabelRunCtxT *chRunCtx, const RsmUndoChMulVersionExpandRec *record,
    uint32_t curSegDepth, uint32_t dirPattern, uint32_t dirSegDepth)
{
    uint32_t halfDirCap = 1 << (curSegDepth - 1);
    if (curSegDepth == chRunCtx->labelVarInfo->dirDepth) {
        if (!record->isNeedUpdateOtherDir) {
            return;
        }
        uint32_t smallSegId;
        uint32_t largeSegId;
        if (dirPattern > halfDirCap) {
            smallSegId = dirPattern - halfDirCap;
            largeSegId = dirPattern % chRunCtx->labelVarInfo->dirCap;
        } else {
            smallSegId = dirPattern;
            largeSegId = (dirPattern + halfDirCap) % chRunCtx->labelVarInfo->dirCap;
        }
        HashDirSegmentT *smallSeg = ClusteredHashGetDirSegmentBySegId(&chRunCtx->memRunCtx, smallSegId);
        DB_ASSERT(smallSeg != NULL);
        if (smallSeg->segDepth != dirSegDepth) {
            return;
        }
        smallSeg->pageAddr.pageAddr.deviceId = record->deviceId;
        smallSeg->pageAddr.pageAddr.blockId = record->blockId;
        HashDirSegmentT *largeSeg = ClusteredHashGetDirSegmentBySegId(&chRunCtx->memRunCtx, largeSegId);
        DB_ASSERT(largeSeg != NULL);
        largeSeg->pageAddr.pageAddr.deviceId = record->deviceId;
        largeSeg->pageAddr.pageAddr.blockId = record->blockId;
    } else {
        if (dirPattern > halfDirCap) {
            ClusteredHashRsmUndoFreshDir(chRunCtx, record, curSegDepth + 1, dirPattern - halfDirCap, dirSegDepth);
            ClusteredHashRsmUndoFreshDir(chRunCtx, record, curSegDepth + 1, dirPattern, dirSegDepth);
        } else {
            ClusteredHashRsmUndoFreshDir(chRunCtx, record, curSegDepth + 1, dirPattern, dirSegDepth);
            ClusteredHashRsmUndoFreshDir(chRunCtx, record, curSegDepth + 1, halfDirCap + dirPattern, dirSegDepth);
        }
    }
}

void ClusteredHashRsmUndoMulVersionExpand(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    TrxT *trx = (TrxT *)ctx->trx;
    ChLabelRunCtxT *chRunCtx = TrxGetClusteredHashHandle(trx, opRecord->labelId);
    if (chRunCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get chRunCtx unsucc, labelId:%" PRIu32 "", opRecord->labelId);
        return;
    }
    const RsmUndoChMulVersionExpandRec *record = &opRecord->rsmChMulVersionExpandRec;
    // 获取hashcode对应dirSeg
    ClusteredHashLabelVarInfoT *labelVarInfo = chRunCtx->labelVarInfo;
    uint32_t pattern = HtGetPattern(record->hashCode, labelVarInfo->dirCap);
    HashDirSegmentT *dirSeg = ClusteredHashGetDirSegmentBySegId(&chRunCtx->memRunCtx, pattern);
    if (SECUREC_UNLIKELY(dirSeg == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Unable to get clustered hash dir seg = %" PRIu32 ".", pattern);
        DB_ASSERT(false);
        return;
    }
    labelVarInfo->segPageCount = record->segPageCnt;
    // 修改dirSeg指向
    dirSeg->pageAddr.pageAddr.deviceId = record->deviceId;
    dirSeg->pageAddr.pageAddr.blockId = record->blockId;
    // 其他指向segPage相同的dir也指向旧页
    ClusteredHashRsmUndoFreshDir(chRunCtx, record, dirSeg->segDepth, dirSeg->pattern, dirSeg->segDepth);
    ClusteredHashStatInsertFailed(chRunCtx);
    ClusteredHashCheckAllDirPage(chRunCtx);
}

static bool ClusteredHashCheckAndUpdateUndoNormalBucket(
    ChLabelRunCtxT *chRunCtx, uint8_t *segPageAddr, const RsmUndoChUpdateRec *record, uint32_t slot)
{
    ChLogicRowId logicRowId = TransformTupleAddr2LogicRowId(record->logicRowId);
    const DashEhMemMetaT *dashMemMeta = &chRunCtx->chLabel->memMgr.dashMemMeta;
    ChLabelPageMetaDataT *pageMetaData = ClusteredHashGetPageMetaDataWithAddr(chRunCtx, segPageAddr);
    uint8_t *bucketHdr = segPageAddr + pageMetaData->bucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    uint8_t *tupleHdr = segPageAddr + pageMetaData->tuplePos + CHLABEL_PAGE_METADATA_OFFSET;
    ChBucketMetaT *bucket = ClusteredHashGetBucketHdr(bucketHdr, slot);
    for (uint32_t i = 0; i < HASH_ENTRY_PER_BUCKET; i++) {
        if (!BitMapIsSet(bucket->allocBitMap, i)) {
            continue;
        }
        ClusteredHashEntryT *entry = ClusteredGetEntryByBucket(bucket, i, dashMemMeta->hashEntrySize);
        if (entry->hashCode == logicRowId.hashCode && entry->logicSlot == logicRowId.tupleId &&
            entry->version == logicRowId.version) {
            // tupleBufPtr 依赖子rsmMemCtx统一释放
            const HeapTupleBufT tupleBuf = {.bufSize = record->bufSize, .buf = DbRsmPtrToAddr(record->tupleBufPtr)};
            ChRowHeadT *rowHead = ClusteredHashGetRowHeadByTupleId(
                tupleHdr, pageMetaData->tupleSize, chRunCtx->chLabel->labelCfg.slotExtendSize, entry->phySlot);
            DbFastMemcpy((uint8_t *)rowHead, tupleBuf.bufSize, tupleBuf.buf, tupleBuf.bufSize);
            // 更新布隆过滤器
            uint8_t *bloomFilterHdr = segPageAddr + CHLABEL_PAGE_METADATA_OFFSET;
            ClusteredHashSetBloomFilter(
                entry->hashCode, bloomFilterHdr, chRunCtx->chLabel->dashMeta.bloomFilterSize, dashMemMeta);
            return true;
        }
    }
    return false;
}

static bool ClusteredHashCheckAndUpdateUndoStashBucket(
    ChLabelRunCtxT *chRunCtx, uint8_t *segPageAddr, const RsmUndoChUpdateRec *record)
{
    ChLogicRowId logicRowId = TransformTupleAddr2LogicRowId(record->logicRowId);
    ChLabelPageMetaDataT *pageMetaData = ClusteredHashGetPageMetaDataWithAddr(chRunCtx, segPageAddr);
    uint8_t *tupleHdr = segPageAddr + pageMetaData->tuplePos + CHLABEL_PAGE_METADATA_OFFSET;
    uint8_t *stashBucketHdr = segPageAddr + pageMetaData->stashBucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    ChStashBucketMetaT *stashBucket = (ChStashBucketMetaT *)(void *)stashBucketHdr;
    for (uint32_t i = 0; i < HASH_ENTRY_PER_STASH_BUCKET; i++) {
        if (!BitMapIsSet(stashBucket->allocBitMap, i)) {
            continue;
        }
        ClusteredHashEntryT *entry =
            ClusteredGetEntryByStashBucket(stashBucket, i, chRunCtx->chLabel->memMgr.dashMemMeta.hashEntrySize);
        if (entry->hashCode == logicRowId.hashCode && entry->logicSlot == logicRowId.tupleId &&
            entry->version == logicRowId.version) {
            {
                // tupleBufPtr 依赖子rsmMemCtx统一释放
                const HeapTupleBufT tupleBuf = {.bufSize = record->bufSize, .buf = DbRsmPtrToAddr(record->tupleBufPtr)};
                ChRowHeadT *rowHead = ClusteredHashGetRowHeadByTupleId(
                    tupleHdr, pageMetaData->tupleSize, chRunCtx->chLabel->labelCfg.slotExtendSize, entry->phySlot);
                DbFastMemcpy((uint8_t *)rowHead, tupleBuf.bufSize, tupleBuf.buf, tupleBuf.bufSize);
                // 更新布隆过滤器
                uint8_t *bloomFilterHdr = segPageAddr + CHLABEL_PAGE_METADATA_OFFSET;
                ClusteredHashSetBloomFilter(entry->hashCode, bloomFilterHdr,
                    chRunCtx->chLabel->dashMeta.bloomFilterSize, &chRunCtx->chLabel->memMgr.dashMemMeta);
                return true;
            }
        }
    }
    return false;
}

static bool ClusteredHashRsmUpdateUndo(ChLabelRunCtxT *chRunCtx, uint8_t *segPageAddr, const RsmUndoChUpdateRec *record)
{
    ChLogicRowId logicRowId = TransformTupleAddr2LogicRowId(record->logicRowId);
    uint32_t targetSlot;
    uint32_t neighborSlot;
    ClusteredHashGetSlot(chRunCtx, segPageAddr, logicRowId.hashCode, &targetSlot, &neighborSlot);
    // 探测target bucket
    if (ClusteredHashCheckAndUpdateUndoNormalBucket(chRunCtx, segPageAddr, record, targetSlot)) {
        return true;
    }
    // 探测neibor bucket
    if (ClusteredHashCheckAndUpdateUndoNormalBucket(chRunCtx, segPageAddr, record, neighborSlot)) {
        return true;
    }
    // 探测stash bucket
    return ClusteredHashCheckAndUpdateUndoStashBucket(chRunCtx, segPageAddr, record);
}

static bool ClusteredHashUpdateUndoNormalBucket(
    ChLabelRunCtxT *chRunCtx, uint8_t *segPageAddr, const RsmUndoChUpdateRec *record, uint32_t slot, bool isPrev)
{
    ChLogicRowId logicRowId = TransformTupleAddr2LogicRowId(record->logicRowId);
    const DashEhMemMetaT *dashMemMeta = &chRunCtx->chLabel->memMgr.dashMemMeta;
    DashEhLabelMetaT *dashMeta = &chRunCtx->chLabel->dashMeta;
    ChLabelPageHeaderT *pageHead = ClusteredHashGetPageHead(segPageAddr);
    ChLabelPageMetaDataT *pageMetaData = ClusteredHashGetPageMetaDataWithAddr(chRunCtx, segPageAddr);
    uint8_t *bucketHdr = segPageAddr + pageMetaData->bucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    uint8_t *tupleHdr = segPageAddr + pageMetaData->tuplePos + CHLABEL_PAGE_METADATA_OFFSET;
    uint8_t *bitmapHdr = segPageAddr + dashMeta->bloomFilterSize + CHLABEL_PAGE_METADATA_OFFSET;
    ChBucketMetaT *bucket = ClusteredHashGetBucketHdr(bucketHdr, slot);
    if (bucket->cnt == HASH_ENTRY_PER_BUCKET) {
        return false;
    }
    uint8_t freeEntrySlot = ClusteredHashNormalBucketAllocSlot(bucket, isPrev);
    ClusteredHashEntryT *freeEntry = ClusteredGetEntryByBucket(bucket, freeEntrySlot, dashMemMeta->hashEntrySize);
    uint16_t freeSlotId = pageHead->freeSlotId;
    ChLabelSlotId *tupleSlot =
        (ChLabelSlotId *)(void *)ClusteredHashGetTupleBySlot(tupleHdr, pageMetaData->tupleSize, freeSlotId);
    pageHead->freeSlotId = (uint16_t)tupleSlot->nextSlotId;
    pageHead->freeSlotCount--;
    chRunCtx->labelVarInfo->tupleUsed++;
    TupleBitMapSet(bitmapHdr, freeSlotId);
    // tupleBufPtr 依赖子rsmMemCtx统一释放
    const HeapTupleBufT tupleBuf = {.bufSize = record->bufSize, .buf = DbRsmPtrToAddr(record->tupleBufPtr)};
    ChRowHeadT *rowHead = ClusteredHashGetRowHeadByTupleId(
        tupleHdr, pageMetaData->tupleSize, chRunCtx->chLabel->labelCfg.slotExtendSize, freeSlotId);
    DbFastMemcpy((uint8_t *)rowHead, tupleBuf.bufSize, tupleBuf.buf, tupleBuf.bufSize);
    ClusteredHashEntryInit(freeEntry, logicRowId.hashCode, freeSlotId, logicRowId.version);
    freeEntry->logicSlot = logicRowId.tupleId;
    // 更新布隆过滤器
    uint8_t *bloomFilterHdr = segPageAddr + CHLABEL_PAGE_METADATA_OFFSET;
    ClusteredHashSetBloomFilter(logicRowId.hashCode, bloomFilterHdr, dashMeta->bloomFilterSize, dashMemMeta);
    return true;
}

static bool ClusteredHashUpdateUndoStashBucket(
    ChLabelRunCtxT *chRunCtx, uint8_t *segPageAddr, const RsmUndoChUpdateRec *record)
{
    ChLogicRowId logicRowId = TransformTupleAddr2LogicRowId(record->logicRowId);
    const DashEhMemMetaT *dashMemMeta = &chRunCtx->chLabel->memMgr.dashMemMeta;
    DashEhLabelMetaT *dashMeta = &chRunCtx->chLabel->dashMeta;
    ChLabelPageHeaderT *pageHead = ClusteredHashGetPageHead(segPageAddr);
    ChLabelPageMetaDataT *pageMetaData = ClusteredHashGetPageMetaDataWithAddr(chRunCtx, segPageAddr);
    uint8_t *bitmapHdr = segPageAddr + dashMeta->bloomFilterSize + CHLABEL_PAGE_METADATA_OFFSET;
    uint8_t *tupleHdr = segPageAddr + pageMetaData->tuplePos + CHLABEL_PAGE_METADATA_OFFSET;
    uint8_t *stashBucketHdr = segPageAddr + pageMetaData->stashBucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    ChStashBucketMetaT *stashBucket = (ChStashBucketMetaT *)(void *)stashBucketHdr;
    if (stashBucket->count == HASH_ENTRY_PER_STASH_BUCKET) {
        return false;
    }
    uint8_t entrySlot = ClusteredHashStashBucketAllocSlot(stashBucket);
    ClusteredHashEntryT *entry = ClusteredGetEntryByStashBucket(stashBucket, entrySlot, dashMemMeta->hashEntrySize);
    uint16_t freeSlotId = pageHead->freeSlotId;
    ChLabelSlotId *tupleSlot =
        (ChLabelSlotId *)(void *)ClusteredHashGetTupleBySlot(tupleHdr, pageMetaData->tupleSize, freeSlotId);
    pageHead->freeSlotCount--;
    pageHead->freeSlotId = (uint16_t)tupleSlot->nextSlotId;
    chRunCtx->labelVarInfo->tupleUsed++;
    TupleBitMapSet(bitmapHdr, freeSlotId);
    // tupleBufPtr 依赖子rsmMemCtx统一释放
    const HeapTupleBufT tupleBuf = {.bufSize = record->bufSize, .buf = DbRsmPtrToAddr(record->tupleBufPtr)};
    ChRowHeadT *rowHead = ClusteredHashGetRowHeadByTupleId(
        tupleHdr, pageMetaData->tupleSize, chRunCtx->chLabel->labelCfg.slotExtendSize, freeSlotId);
    DbFastMemcpy((uint8_t *)rowHead, tupleBuf.bufSize, tupleBuf.buf, tupleBuf.bufSize);
    ClusteredHashEntryInit(entry, logicRowId.hashCode, freeSlotId, logicRowId.version);
    entry->logicSlot = logicRowId.tupleId;
    // 更新布隆过滤器
    uint8_t *bloomFilterHdr = segPageAddr + CHLABEL_PAGE_METADATA_OFFSET;
    ClusteredHashSetBloomFilter(logicRowId.hashCode, bloomFilterHdr, dashMeta->bloomFilterSize, dashMemMeta);
    return true;
}

static bool ClusteredHashUpdateUndoEntry(
    ChLabelRunCtxT *chRunCtx, uint8_t *segPageAddr, const RsmUndoChUpdateRec *record)
{
    ChLogicRowId logicRowId = TransformTupleAddr2LogicRowId(record->logicRowId);
    uint32_t targetSlot;
    uint32_t neighborSlot;
    ClusteredHashGetSlot(chRunCtx, segPageAddr, logicRowId.hashCode, &targetSlot, &neighborSlot);
    if (ClusteredHashUpdateUndoNormalBucket(chRunCtx, segPageAddr, record, targetSlot, false)) {
        return true;
    }
    if (ClusteredHashUpdateUndoNormalBucket(chRunCtx, segPageAddr, record, neighborSlot, true)) {
        return true;
    }
    return ClusteredHashUpdateUndoStashBucket(chRunCtx, segPageAddr, record);
}

static void ClusteredHashRsmMulVersionUpdateUndo(ChLabelRunCtxT *chRunCtx, const RsmUndoChUpdateRec *record)
{
    // 升降级存在先删除后插入逻辑，有rsm undo保证回滚
    ChLogicRowId logicRowId = TransformTupleAddr2LogicRowId(record->logicRowId);
    uint32_t pattern = HtGetPattern(logicRowId.hashCode, chRunCtx->labelVarInfo->dirCap);
    HashDirSegmentT *dirSeg = ClusteredHashGetDirSegmentBySegId(&chRunCtx->memRunCtx, pattern);
    if (SECUREC_UNLIKELY(dirSeg == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Unable to get clustered hash dir seg = %" PRIu32 ".", pattern);
        return;
    }
    PageIdT targetPageAddr = dirSeg->pageAddr.pageAddr;
    uint8_t *segPageAddr = NULL;
    StatusInter ret = ClusteredHashGetSegPage(
        chRunCtx->memRunCtx.mdMgr, targetPageAddr, chRunCtx->chLabel->memMgr.base.fileId, &segPageAddr);
    DB_ASSERT(ret == STATUS_OK_INTER);
    uint8_t *tmpSegPageAddr = segPageAddr;
    ChLabelPageMetaDataT *newMetaData = ClusteredHashGetPageMetaDataWithAddr(chRunCtx, tmpSegPageAddr);
    ChLabelPageMetaDataT *tmpMetaData = newMetaData;
    do {
        if (ClusteredHashRsmUpdateUndo(chRunCtx, tmpSegPageAddr, record)) {
            return;
        }
        if (SECUREC_LIKELY(ClusteredHashIsInvalidPageId(tmpMetaData->nextPageAddr))) {
            break;
        }
        targetPageAddr = tmpMetaData->nextPageAddr;
        tmpMetaData = ClusteredHashGetPageMetaDataByPageId(&chRunCtx->memRunCtx, targetPageAddr);
        ret = ClusteredHashGetSegPage(
            chRunCtx->memRunCtx.mdMgr, targetPageAddr, chRunCtx->chLabel->memMgr.base.fileId, &tmpSegPageAddr);
        DB_ASSERT(ret == STATUS_OK_INTER);
    } while (true);
    do {
        if (ClusteredHashUpdateUndoEntry(chRunCtx, segPageAddr, record)) {
            return;
        }
        if (SECUREC_LIKELY(ClusteredHashIsInvalidPageId(newMetaData->nextPageAddr))) {
            break;
        }
        targetPageAddr = newMetaData->nextPageAddr;
        newMetaData = ClusteredHashGetPageMetaDataByPageId(&chRunCtx->memRunCtx, targetPageAddr);
        ret = ClusteredHashGetSegPage(
            chRunCtx->memRunCtx.mdMgr, targetPageAddr, chRunCtx->chLabel->memMgr.base.fileId, &segPageAddr);
        DB_ASSERT(ret == STATUS_OK_INTER);
    } while (true);
    DB_ASSERT(false);
}

void ClusteredHashRsmUndoUpdate(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    TrxT *trx = (TrxT *)ctx->trx;
    ChLabelRunCtxT *chRunCtx = TrxGetClusteredHashHandle(trx, opRecord->labelId);
    if (chRunCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get chRunCtx unsucc, labelId:%" PRIu32 "", opRecord->labelId);
        DB_ASSERT(false);
        return;
    }
    // 获取 undolog
    const RsmUndoChUpdateRec *record = &opRecord->rsmChNormalUpdateRec;
    if (!record->isMulVersion) {
        // 获取目标数据页
        PageIdT targetPageAddr = {.deviceId = record->deviceId, .blockId = record->blockId};
        uint8_t *segPageAddr = NULL;
        StatusInter ret = ClusteredHashGetSegPage(
            chRunCtx->memRunCtx.mdMgr, targetPageAddr, chRunCtx->chLabel->memMgr.base.fileId, &segPageAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_LAST_ERROR(ret, "Unable to get clustered hash seg page, labelId = %" PRIu32 ".", opRecord->labelId);
            DB_ASSERT(false);
            return;
        }
        (void)ClusteredHashRsmUpdateUndo(chRunCtx, segPageAddr, record);
    } else {
        ClusteredHashRsmMulVersionUpdateUndo(chRunCtx, record);
    }
    ClusteredHashCheckAllDirPage(chRunCtx);
}

static bool ClusteredHashCheckNormalBucket(
    ChLabelRunCtxT *chRunCtx, uint8_t *segPageAddr, const RsmUndoChDeleteRec *record, uint32_t slot, bool isPrev)
{
    ChLogicRowId logicRowId = TransformTupleAddr2LogicRowId(record->logicRowId);
    const DashEhMemMetaT *dashMemMeta = &chRunCtx->chLabel->memMgr.dashMemMeta;
    ChLabelPageMetaDataT *pageMetaData = ClusteredHashGetPageMetaDataWithAddr(chRunCtx, segPageAddr);
    uint8_t *bucketHdr = segPageAddr + pageMetaData->bucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    ChBucketMetaT *bucket = ClusteredHashGetBucketHdr(bucketHdr, slot);
    for (uint32_t i = 0; i < HASH_ENTRY_PER_BUCKET; i++) {
        ClusteredHashEntryT *entry = ClusteredGetEntryByBucket(bucket, i, dashMemMeta->hashEntrySize);
        if (entry->hashCode == logicRowId.hashCode && entry->logicSlot == logicRowId.tupleId &&
            entry->version == logicRowId.version && entry->phySlot == record->phyTupleId) {
            uint64_t bucketMetaData = record->bucketMetaData;
            *bucket = *(ChBucketMetaT *)(void *)&bucketMetaData;
            return true;
        }
        if (!BitMapIsSet(bucket->allocBitMap, i)) {
            ClusteredHashEntryClear(entry);
        }
    }
    return false;
}

static bool ClusteredHashCheckStashBucket(
    ChLabelRunCtxT *chRunCtx, uint8_t *segPageAddr, const RsmUndoChDeleteRec *record)
{
    ChLogicRowId logicRowId = TransformTupleAddr2LogicRowId(record->logicRowId);

    ChLabelPageMetaDataT *pageMetaData = ClusteredHashGetPageMetaDataWithAddr(chRunCtx, segPageAddr);
    uint8_t *stashBucketHdr = segPageAddr + pageMetaData->stashBucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    ChStashBucketMetaT *stashBucket = (ChStashBucketMetaT *)(void *)stashBucketHdr;
    for (uint32_t i = 0; i < HASH_ENTRY_PER_STASH_BUCKET; i++) {
        ClusteredHashEntryT *entry =
            ClusteredGetEntryByStashBucket(stashBucket, i, chRunCtx->chLabel->memMgr.dashMemMeta.hashEntrySize);
        if (entry->hashCode == logicRowId.hashCode && entry->logicSlot == logicRowId.tupleId &&
            entry->version == logicRowId.version && entry->phySlot == record->phyTupleId) {
            uint64_t bucketMetaData = record->bucketMetaData;
            *stashBucket = *(ChStashBucketMetaT *)(void *)&bucketMetaData;

            const DashEhMemMetaT *dashMemMeta = &chRunCtx->chLabel->memMgr.dashMemMeta;
            DashEhLabelMetaT *dashMeta = &chRunCtx->chLabel->dashMeta;
            uint8_t *bucketHdr = segPageAddr + pageMetaData->bucketPos + CHLABEL_PAGE_METADATA_OFFSET;
            uint32_t targetBucketIdx = ChLabelGetBucketIndexByHashCode(logicRowId.hashCode, dashMemMeta);
            uint32_t targetSlot =
                ChLabelGetBucketSlotByIndex(targetBucketIdx, dashMeta, pageMetaData->hashNormalBucketNumPerPage);
            ChBucketMetaT *targetBucket = ClusteredHashGetBucketHdr(bucketHdr, targetSlot);
            uint64_t targetBucketMetaData = record->targetBucketMetaData;
            *targetBucket = *(ChBucketMetaT *)(void *)&targetBucketMetaData;
            return true;
        }
        if (!BitMapIsSet(stashBucket->allocBitMap, i)) {
            ClusteredHashEntryClear(entry);
        }
    }
    return false;
}

static bool ClusteredHashCheckDeleteEntry(
    ChLabelRunCtxT *chRunCtx, uint8_t *segPageAddr, const RsmUndoChDeleteRec *record)
{
    ChLogicRowId logicRowId = TransformTupleAddr2LogicRowId(record->logicRowId);
    uint32_t targetSlot;
    uint32_t neighborSlot;
    ClusteredHashGetSlot(chRunCtx, segPageAddr, logicRowId.hashCode, &targetSlot, &neighborSlot);
    // 探测target bucket
    if (ClusteredHashCheckNormalBucket(chRunCtx, segPageAddr, record, targetSlot, false)) {
        return true;
    }
    // 探测neibor bucket
    if (ClusteredHashCheckNormalBucket(chRunCtx, segPageAddr, record, neighborSlot, true)) {
        return true;
    }
    // 探测stash bucket
    return ClusteredHashCheckStashBucket(chRunCtx, segPageAddr, record);
}

static bool ClusteredHashDeleteUndoNormalBucket(
    ChLabelRunCtxT *chRunCtx, uint8_t *segPageAddr, const RsmUndoChDeleteRec *record, uint32_t slot, bool isPrev)
{
    ChLogicRowId logicRowId = TransformTupleAddr2LogicRowId(record->logicRowId);
    const DashEhMemMetaT *dashMemMeta = &chRunCtx->chLabel->memMgr.dashMemMeta;
    DashEhLabelMetaT *dashMeta = &chRunCtx->chLabel->dashMeta;
    ChLabelPageMetaDataT *pageMetaData = ClusteredHashGetPageMetaDataWithAddr(chRunCtx, segPageAddr);
    ChLabelPageHeaderT *pageHead = ClusteredHashGetPageHead(segPageAddr);
    uint8_t *tupleHdr = segPageAddr + pageMetaData->tuplePos + CHLABEL_PAGE_METADATA_OFFSET;
    uint8_t *bitmapHdr = segPageAddr + dashMeta->bloomFilterSize + CHLABEL_PAGE_METADATA_OFFSET;
    uint8_t *bucketHdr = segPageAddr + pageMetaData->bucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    ChBucketMetaT *bucket = ClusteredHashGetBucketHdr(bucketHdr, slot);
    if (bucket->cnt == HASH_ENTRY_PER_BUCKET) {
        return false;
    }
    uint8_t freeEntrySlot = ClusteredHashNormalBucketAllocSlot(bucket, isPrev);
    ClusteredHashEntryT *freeEntry = ClusteredGetEntryByBucket(bucket, freeEntrySlot, dashMemMeta->hashEntrySize);
    freeEntry->hashCode = logicRowId.hashCode;
    freeEntry->version = logicRowId.version;
    freeEntry->logicSlot = logicRowId.tupleId;
    freeEntry->phySlot = record->phyTupleId;
    // 判断tuple是否要恢复
    if (record->freeSlotCount != pageHead->freeSlotCount) {
        // tupleBufPtr 依赖子rsmMemCtx统一释放
        const HeapTupleBufT tupleBuf = {.bufSize = record->bufSize, .buf = DbShmPtrToAddr(record->tupleBufPtr)};
        pageHead->freeSlotId = (uint16_t)record->freeSlotId;
        pageHead->freeSlotCount = (uint16_t)record->freeSlotCount;
        chRunCtx->labelVarInfo->tupleUsed = record->tupleUsed;
        ChRowHeadT *rowHead = ClusteredHashGetRowHeadByTupleId(
            tupleHdr, pageMetaData->tupleSize, chRunCtx->chLabel->labelCfg.slotExtendSize, record->phyTupleId);
        rowHead->trxId = record->trxId;
        rowHead->totalBufSize = record->bufSize;
        DbFastMemcpy((uint8_t *)(rowHead + 1), tupleBuf.bufSize, tupleBuf.buf, tupleBuf.bufSize);
        TupleBitMapSet(bitmapHdr, record->phyTupleId);
    }
    return true;
}

static void ClusteredHashDeleteUndoStashBucket(
    ChLabelRunCtxT *chRunCtx, uint8_t *segPageAddr, const RsmUndoChDeleteRec *record)
{
    ChLogicRowId logicRowId = TransformTupleAddr2LogicRowId(record->logicRowId);
    DashEhLabelMetaT *dashMeta = &chRunCtx->chLabel->dashMeta;
    const DashEhMemMetaT *dashMemMeta = &chRunCtx->chLabel->memMgr.dashMemMeta;
    ChLabelPageHeaderT *pageHead = ClusteredHashGetPageHead(segPageAddr);
    ChLabelPageMetaDataT *pageMetaData = ClusteredHashGetPageMetaDataWithAddr(chRunCtx, segPageAddr);
    uint8_t *tupleHdr = segPageAddr + pageMetaData->tuplePos + CHLABEL_PAGE_METADATA_OFFSET;
    uint8_t *bitmapHdr = segPageAddr + dashMeta->bloomFilterSize + CHLABEL_PAGE_METADATA_OFFSET;
    uint8_t *stashBucketHdr = segPageAddr + pageMetaData->stashBucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    ChStashBucketMetaT *stashBucket = (ChStashBucketMetaT *)(void *)stashBucketHdr;
    DB_ASSERT(stashBucket->count != HASH_ENTRY_PER_STASH_BUCKET);
    uint8_t entrySlot = ClusteredHashStashBucketAllocSlot(stashBucket);
    ClusteredHashEntryT *entry = ClusteredGetEntryByStashBucket(stashBucket, entrySlot, dashMemMeta->hashEntrySize);
    entry->logicSlot = logicRowId.tupleId;
    entry->phySlot = record->phyTupleId;
    entry->hashCode = logicRowId.hashCode;
    entry->version = logicRowId.version;
    // 判断tuple是否要恢复
    if (record->freeSlotCount != pageHead->freeSlotCount) {
        // tupleBufPtr 依赖子rsmMemCtx统一释放
        const HeapTupleBufT tupleBuf = {.bufSize = record->bufSize, .buf = DbShmPtrToAddr(record->tupleBufPtr)};
        pageHead->freeSlotCount = (uint16_t)record->freeSlotCount;
        pageHead->freeSlotId = (uint16_t)record->freeSlotId;
        chRunCtx->labelVarInfo->tupleUsed = record->tupleUsed;
        ChRowHeadT *rowHead = ClusteredHashGetRowHeadByTupleId(
            tupleHdr, pageMetaData->tupleSize, chRunCtx->chLabel->labelCfg.slotExtendSize, record->phyTupleId);
        rowHead->trxId = record->trxId;
        rowHead->totalBufSize = record->bufSize;
        DbFastMemcpy((uint8_t *)(rowHead + 1), tupleBuf.bufSize, tupleBuf.buf, tupleBuf.bufSize);
        TupleBitMapSet(bitmapHdr, record->phyTupleId);
    }
}

static void ClusteredHashDeleteUndoEntry(
    ChLabelRunCtxT *chRunCtx, uint8_t *segPageAddr, const RsmUndoChDeleteRec *record)
{
    ChLogicRowId logicRowId = TransformTupleAddr2LogicRowId(record->logicRowId);
    uint32_t targetSlot;
    uint32_t neighborSlot;
    ClusteredHashGetSlot(chRunCtx, segPageAddr, logicRowId.hashCode, &targetSlot, &neighborSlot);
    if (ClusteredHashDeleteUndoNormalBucket(chRunCtx, segPageAddr, record, targetSlot, false)) {
        return;
    }
    if (ClusteredHashDeleteUndoNormalBucket(chRunCtx, segPageAddr, record, neighborSlot, true)) {
        return;
    }
    ClusteredHashDeleteUndoStashBucket(chRunCtx, segPageAddr, record);
}

void ClusteredHashRsmUndoDelete(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    TrxT *trx = (TrxT *)ctx->trx;
    ChLabelRunCtxT *chRunCtx = TrxGetClusteredHashHandle(trx, opRecord->labelId);
    if (chRunCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get chRunCtx unsucc, labelId:%" PRIu32 "", opRecord->labelId);
        DB_ASSERT(false);
        return;
    }
    // 获取 undolog
    const RsmUndoChDeleteRec *record = &opRecord->rsmChNormalDeleteRec;
    // 获取目标数据页
    PageIdT targetPageAddr = {.deviceId = record->deviceId, .blockId = record->blockId};
    uint8_t *segPageAddr = NULL;
    StatusInter ret = ClusteredHashGetSegPage(
        chRunCtx->memRunCtx.mdMgr, targetPageAddr, chRunCtx->chLabel->memMgr.base.fileId, &segPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret, "Unable to get clustered hash seg page, labelId = %" PRIu32 ".", opRecord->labelId);
        DB_ASSERT(false);
        return;
    }
    if (ClusteredHashCheckDeleteEntry(chRunCtx, segPageAddr, record)) {
        ClusteredHashCheckAllDirPage(chRunCtx);
        return;
    }
    ClusteredHashDeleteUndoEntry(chRunCtx, segPageAddr, record);
    ClusteredHashCheckAllDirPage(chRunCtx);
}

static void ClusteredHashGetDirSeg(ChLabelRunCtxT *chRunCtx, const RsmUndoChRecyclePageRec *record,
    HashDirSegmentT **dirSeg, HashDirSegmentT *otherSeg)
{
    *dirSeg = ClusteredHashGetDirSegmentBySegId(&chRunCtx->memRunCtx, record->oldPagePattern);
    DB_ASSERT(*dirSeg != NULL);
    otherSeg->segDepth = record->segDepth;
    otherSeg->pattern = record->newPagePattern;
    otherSeg->pageAddr.pageAddr.deviceId = record->deviceId;
    otherSeg->pageAddr.pageAddr.blockId = record->blockId;
}

static void ClusteredHashRecyclePageUndo(ChLabelRunCtxT *chRunCtx, const RsmUndoChRecyclePageRec *record)
{
    HashDirSegmentT *dirSeg = NULL;
    HashDirSegmentT otherSeg;
    ClusteredHashGetDirSeg(chRunCtx, record, &dirSeg, &otherSeg);
    // 不用尝试获取数据页，默认已经释放，其他情况由资源对账保证
    ClusteredHashFixSegAfterRecycle(chRunCtx, dirSeg, &otherSeg);
}

static void ClusteredHashRecycleUpdateDirUndo(ChLabelRunCtxT *chRunCtx, const RsmUndoChRecyclePageRec *record)
{
    HashDirSegmentT *dirSeg = NULL;
    HashDirSegmentT otherSeg;
    ClusteredHashGetDirSeg(chRunCtx, record, &dirSeg, &otherSeg);
    dirSeg->segDepth = otherSeg.segDepth;
    ClusteredHashFixSegAfterRecycle(chRunCtx, dirSeg, &otherSeg);
}

void ClusteredHashRsmUndoRecyclePage(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    TrxT *trx = (TrxT *)ctx->trx;
    ChLabelRunCtxT *chRunCtx = TrxGetClusteredHashHandle(trx, opRecord->labelId);
    if (chRunCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get chRunCtx unsucc, labelId:%" PRIu32 "", opRecord->labelId);
        DB_ASSERT(false);
        return;
    }
    const RsmUndoChRecyclePageRec *record = &opRecord->rsmChNormalRecyclePageRec;
    switch (record->recycleType) {
        case CLUSTERED_HASH_RECYCLE_PAGE: {
            ClusteredHashRecyclePageUndo(chRunCtx, record);
            break;
        }
        case CLUSTERED_HASH_RECYCLE_UPDATE_DIR: {
            ClusteredHashRecycleUpdateDirUndo(chRunCtx, record);
            break;
        }
        case CLUSTERED_HASH_RECYCLE_FINISHED:
            break;
        default:
            DB_ASSERT(false);
            break;
    }
    chRunCtx->labelVarInfo->segPageCount = record->segPageCnt - 1;
    ClusteredHashCheckAllDirPage(chRunCtx);
}

void ClusteredHashRsmUndoScaleInDir(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    TrxT *trx = (TrxT *)ctx->trx;
    ChLabelRunCtxT *chRunCtx = TrxGetClusteredHashHandle(trx, opRecord->labelId);
    if (chRunCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get chRunCtx unsucc, labelId:%" PRIu32 "", opRecord->labelId);
        DB_ASSERT(false);
        return;
    }
    const RsmUndoChScaleInDirRec *record = &opRecord->rsmChNormalScaleInDirRec;
    // 判断是否已经缩容完成
    ClusteredHashLabelVarInfoT *labelVarInfo = chRunCtx->labelVarInfo;
    if (record->dirDepth > labelVarInfo->dirDepth) {
        ClusteredHashCheckAllDirPage(chRunCtx);
        return;
    }
    // 继续进行缩容
    ChLabelPageMemRunCtxT *pageMemRunCtx = &chRunCtx->memRunCtx;
    uint32_t dirPageCount = record->dirPageCount;
    for (uint32_t i = record->freePageBeginId; i < dirPageCount; i++) {
        // dir页释放由对账保证
        pageMemRunCtx->pageAddr[i] = SE_INVALID_PAGE_ADDR;
    }
    labelVarInfo->dirPageCount = record->freePageBeginId;
    labelVarInfo->dirCap = record->dirCap >> 1;
    labelVarInfo->dirDepth = record->dirDepth - 1;
    ClusteredHashCheckAllDirPage(chRunCtx);
}

static void ClusteredHashRecycleMulPageFirstUndo(
    ChLabelRunCtxT *chRunCtx, const RsmUndoChRecycleMulPageRec *record, uint32_t labelId)
{
    ClusteredHashLabelVarInfoT *labelVarInfo = chRunCtx->labelVarInfo;
    DashEhMemMetaT *dashMemMeta = &chRunCtx->chLabel->memMgr.dashMemMeta;
    if (labelVarInfo->segPageCount == record->segPageCnt - 1) {
        // 说明页回收已经完成
        return;
    }
    // 探测并修改dirPage指向
    uint8_t *dirPageAddr = NULL;
    uint32_t dirPageCnt = labelVarInfo->dirPageCount;
    uint32_t hashSegNumPerPage = dashMemMeta->hashSegNumPerPage;
    for (uint32_t i = 0; i < dirPageCnt; i++) {
        StatusInter ret = ClusteredHashGetDirPage(&chRunCtx->memRunCtx, i, &dirPageAddr);
        DB_ASSERT(ret == STATUS_OK_INTER);
        for (uint32_t segSlotId = 0; segSlotId < hashSegNumPerPage; segSlotId++) {
            HashDirSegmentT *seg = (HashDirSegmentT *)(void *)(dirPageAddr + segSlotId * sizeof(HashDirSegmentT));
            if (seg->pageAddr.pageAddr.deviceId == record->targetDeviceId &&
                seg->pageAddr.pageAddr.blockId == record->targetBlockId) {
                // 指向被释放的页，需要修改指向
                seg->pageAddr.pageAddr.deviceId = record->nextDeviceId;
                seg->pageAddr.pageAddr.blockId = record->nextBlockId;
            }
        }
    }
    // 目标页可能还没有回收，不处理，由对账处理
    labelVarInfo->segPageCount = record->segPageCnt - 1;
}

static void ClusteredHashRecycleMulPageMiddleUndo(
    ChLabelRunCtxT *chRunCtx, const RsmUndoChRecycleMulPageRec *record, uint32_t labelId)
{
    ClusteredHashLabelVarInfoT *labelVarInfo = chRunCtx->labelVarInfo;
    if (labelVarInfo->segPageCount == record->segPageCnt - 1 || record->prevDeviceId == DB_INVALID_UINT32 ||
        record->prevBlockId == DB_INVALID_UINT32) {
        // 说明页回收已经完成，或者还没有开始回收目标空页，不进行回收
        return;
    }
    PageIdT prevPageAddr = {.deviceId = record->prevDeviceId, .blockId = record->prevBlockId};
    ChLabelPageMetaDataT *prevPageMetaData = ClusteredHashGetPageMetaDataByPageId(&chRunCtx->memRunCtx, prevPageAddr);
    prevPageMetaData->nextPageAddr.deviceId = record->nextDeviceId;
    prevPageMetaData->nextPageAddr.blockId = record->nextBlockId;
    // 目标页可能还没有回收，不处理，由对账处理
    labelVarInfo->segPageCount = record->segPageCnt - 1;
}

static void ClusteredHashRecycleMulPageLastUndo(
    ChLabelRunCtxT *chRunCtx, const RsmUndoChRecycleMulPageRec *record, uint32_t labelId)
{
    ClusteredHashLabelVarInfoT *labelVarInfo = chRunCtx->labelVarInfo;
    if (labelVarInfo->segPageCount == record->segPageCnt - 1 || record->prevDeviceId == DB_INVALID_UINT32 ||
        record->prevBlockId == DB_INVALID_UINT32) {
        // 说明页回收已经完成，或者还没有开始回收目标空页，不进行回收
        return;
    }
    PageIdT prevPageAddress = {.deviceId = record->prevDeviceId, .blockId = record->prevBlockId};
    ChLabelPageMetaDataT *prevPageMetaData =
        ClusteredHashGetPageMetaDataByPageId(&chRunCtx->memRunCtx, prevPageAddress);
    prevPageMetaData->nextPageAddr = SE_INVALID_PAGE_ADDR;
    // 目标页可能还没有回收，不处理，由对账处理
    labelVarInfo->segPageCount = record->segPageCnt - 1;
}

void ClusteredHashRsmUndoRecycleMulPage(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    TrxT *trx = (TrxT *)ctx->trx;
    ChLabelRunCtxT *chRunCtx = TrxGetClusteredHashHandle(trx, opRecord->labelId);
    if (chRunCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get chRunCtx unsucc, labelId:%" PRIu32 "", opRecord->labelId);
        return;
    }
    const RsmUndoChRecycleMulPageRec *record = &opRecord->rsmChNormalRecycleMulPageRec;
    switch (record->recycleType) {
        case CLUSTERED_HASH_RECYCLE_MUL_PAGE_FIRST: {
            ClusteredHashRecycleMulPageFirstUndo(chRunCtx, record, opRecord->labelId);
            break;
        }
        case CLUSTERED_HASH_RECYCLE_MUL_PAGE_MIDDLE: {
            ClusteredHashRecycleMulPageMiddleUndo(chRunCtx, record, opRecord->labelId);
            break;
        }
        case CLUSTERED_HASH_RECYCLE_MUL_PAGE_LAST: {
            ClusteredHashRecycleMulPageLastUndo(chRunCtx, record, opRecord->labelId);
            break;
        }
        default:
            DB_ASSERT(false);
            break;
    }
    ClusteredHashCheckAllDirPage(chRunCtx);
}

void ClusteredHashRsmUndoScaleInFreeEmptyPage(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    TrxT *trx = (TrxT *)ctx->trx;
    ChLabelRunCtxT *chRunCtx = TrxGetClusteredHashHandle(trx, opRecord->labelId);
    if (chRunCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get chRunCtx unsucc, labelId:%" PRIu32 "", opRecord->labelId);
        return;
    }
    const RsmUndoChScaleInFreeEmptyRec *record = &opRecord->rsmUndoChScaleInFreeEmptyRec;
    HashDirSegmentT *dirSegA = ClusteredHashGetDirSegmentBySegId(&chRunCtx->memRunCtx, record->patternA);
    HashDirSegmentT *dirSegB = ClusteredHashGetDirSegmentBySegId(&chRunCtx->memRunCtx, record->patternB);
    if (dirSegA == dirSegB) {
        // 说明缩容完成，直接返回
        return;
    }
    if (record->patternA > record->patternB) {
        dirSegA->pattern = record->patternB;
    }
    dirSegA->segDepth = record->segDepth - 1;
    *dirSegB = *dirSegA;
    chRunCtx->labelVarInfo->segPageCount = record->segPageCount - 1;
    ClusteredHashCheckAllDirPage(chRunCtx);
}

static void ClusteredHashScaleInUndo(
    ChLabelRunCtxT *chRunCtx, PageIdT srcPageAddr, PageIdT dstPageAddr, ShmemPtrT segPagePtr)
{
    uint8_t *srcSegPageAddr = NULL;
    uint8_t *dstSegPageAddr = NULL;
    StatusInter ret = ClusteredHashGetSegPage(
        chRunCtx->memRunCtx.mdMgr, srcPageAddr, chRunCtx->chLabel->memMgr.base.fileId, &srcSegPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret, "Unable to get clustered hash src seg page, labelId: %" PRIu32 ", indexId: %" PRIu32,
            chRunCtx->chLabel->labelCfg.labelId, chRunCtx->chLabel->labelCfg.indexId);
        DB_ASSERT(false);
        return;
    }
    ret = ClusteredHashGetSegPage(
        chRunCtx->memRunCtx.mdMgr, dstPageAddr, chRunCtx->chLabel->memMgr.base.fileId, &dstSegPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret, "Unable to get clustered hash dst seg page, labelId: %" PRIu32 ", indexId: %" PRIu32,
            chRunCtx->chLabel->labelCfg.labelId, chRunCtx->chLabel->labelCfg.indexId);
        DB_ASSERT(false);
        return;
    }
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance((uint16_t)chRunCtx->chLabel->labelCfg.seInstanceId);
    if (seInstance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Get storage instance unsucc, seInstanceId: %" PRIu32,
            chRunCtx->chLabel->labelCfg.seInstanceId);
        DB_ASSERT(false);
        return;
    }
    uint32_t pageSize = seInstance->seConfig.pageSize * DB_KIBI;
    // segPagePtr 依赖子rsmMemCtx统一释放
    uint8_t *srcPageBackup = DbShmPtrToAddr(segPagePtr);
    DB_ASSERT(srcPageBackup != NULL);
    uint8_t *dstPageBackup = srcPageBackup + pageSize;
    errno_t err1 = memcpy_s((uint8_t *)(srcSegPageAddr - CHLABEL_PAGE_HEAD_OFFSET), pageSize, srcPageBackup, pageSize);
    errno_t err2 = memcpy_s((uint8_t *)(dstSegPageAddr - CHLABEL_PAGE_HEAD_OFFSET), pageSize, dstPageBackup, pageSize);
    if (err1 != EOK || err2 != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "Clustered hash vertical scale in undo memcpy unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            chRunCtx->chLabel->labelCfg.labelId, chRunCtx->chLabel->labelCfg.indexId);
        DB_ASSERT(false);
    }
}

void ClusteredHashRsmUndoHorizontalScaleIn(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    TrxT *trx = (TrxT *)ctx->trx;
    ChLabelRunCtxT *chRunCtx = TrxGetClusteredHashHandle(trx, opRecord->labelId);
    if (chRunCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get chRunCtx unsucc, labelId:%" PRIu32 "", opRecord->labelId);
        return;
    }
    const RsmUndoChHorizontalScaleInRec *record = &opRecord->rsmUndoChScaleInHorizontalRec;
    PageIdT srcPageAddr = (PageIdT){record->srcDeviceId, record->srcBlockId};
    PageIdT dstPageAddr = (PageIdT){record->dstDeviceId, record->dstBlockId};
    ClusteredHashScaleInUndo(chRunCtx, srcPageAddr, dstPageAddr, record->segPagePtr);
}

static void ClusteredHashRsmUndoUpdateDir(ChLabelRunCtxT *chRunCtx, const RsmUndoChMulVersionScaleInRec *record)
{
    // 由对账保证数据页的释放
    chRunCtx->labelVarInfo->segPageCount = record->segPageCount - record->freePageCnt;
    uint32_t patternSpan = 1u << (record->segDepth - 1);
    HashDirSegmentT *minSeg = ClusteredHashGetDirSegmentBySegId(&chRunCtx->memRunCtx, record->srcPattern % patternSpan);
    if (minSeg->segDepth != record->segDepth) {
        return;
    }
    HashDirSegmentT srcDirSeg = {
        .segDepth = record->segDepth,
        .pattern = record->srcPattern,
        .pageAddr = {.pageAddr = (PageIdT){record->srcDeviceId, record->srcBlockId}},
    };
    HashDirSegmentT dstDirSeg = {
        .segDepth = record->segDepth,
        .pattern = record->dstPattern,
        .pageAddr = {.pageAddr = (PageIdT){record->dstDeviceId, record->dstBlockId}},
    };
    ClusteredHashFixSegAfterRecycle(chRunCtx, &srcDirSeg, &dstDirSeg);
}

static void ClusteredHashRsmUndoScaleInRollback(ChLabelRunCtxT *chRunCtx, const RsmUndoChMulVersionScaleInRec *record)
{
    HashDirSegmentT srcDirSegment = {
        .segDepth = record->segDepth,
        .pattern = record->srcPattern,
        .pageAddr = {.pageAddr = (PageIdT){record->srcDeviceId, record->srcBlockId}},
    };
    HashDirSegmentT dstDirSegment = {
        .segDepth = record->segDepth,
        .pattern = record->dstPattern,
        .pageAddr = {.pageAddr = (PageIdT){record->dstDeviceId, record->dstBlockId}},
    };
    PageIdT dstTargetPageAddr = (PageIdT){record->targetDeviceId, record->targetBlockId};
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance((uint16_t)chRunCtx->chLabel->labelCfg.seInstanceId);
    if (seInstance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Get storage instance unsucc, seInstanceId: %" PRIu32,
            chRunCtx->chLabel->labelCfg.seInstanceId);
        DB_ASSERT(false);
        return;
    }
    uint32_t pageSize = seInstance->seConfig.pageSize * DB_KIBI;
    ClusteredHashMergePageChainRollBack(chRunCtx, &srcDirSegment, &dstDirSegment, dstTargetPageAddr, pageSize);
    chRunCtx->labelVarInfo->segPageCount = record->segPageCount;
}

void ClusteredHashRsmUndoMulVersionScaleIn(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    TrxT *trx = (TrxT *)ctx->trx;
    ChLabelRunCtxT *chRunCtx = TrxGetClusteredHashHandle(trx, opRecord->labelId);
    if (chRunCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get chRunCtx unsucc, labelId:%" PRIu32 "", opRecord->labelId);
        return;
    }
    const RsmUndoChMulVersionScaleInRec *record = &opRecord->rsmUndoChMulVersionScaleInRec;
    switch (record->type) {
        case CLUSTERED_HASH_SCALE_IN_INIT:
        case CLUSTERED_HASH_SCALE_IN_PART_SUCC:
        case CLUSTERED_HASH_SCALE_IN_ROLLBACK: {
            ClusteredHashRsmUndoScaleInRollback(chRunCtx, record);
            break;
        }
        case CLUSTERED_HASH_SCALE_IN_SUCC: {
            ClusteredHashRsmUndoUpdateDir(chRunCtx, record);
            break;
        }
        case CLUSTERED_HASH_SCALE_IN_FINISHED: {
            // 已经处理结束，直接返回
            break;
        }
        default:
            DB_ASSERT(false);
            break;
    }
    ClusteredHashCheckAllDirPage(chRunCtx);
}

void ClusteredHashRsmUndoVerticalScaleIn(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    TrxT *trx = (TrxT *)ctx->trx;
    ChLabelRunCtxT *chRunCtx = TrxGetClusteredHashHandle(trx, opRecord->labelId);
    if (chRunCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get chRunCtx unsucc, labelId:%" PRIu32 "", opRecord->labelId);
        return;
    }
    const RsmUndoChVerticalScaleInRec *record = &opRecord->rsmUndoChScaleInVerticalRec;
    PageIdT srcPageAddr = (PageIdT){record->srcDeviceId, record->srcBlockId};
    PageIdT dstPageAddr = (PageIdT){record->dstDeviceId, record->dstBlockId};
    ClusteredHashScaleInUndo(chRunCtx, srcPageAddr, dstPageAddr, record->segPagePtr);
}

void ClusteredHashConfigUndoForMaxItemNum(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    DB_UNUSED(ctx);
    const RsmUndoContainerConfigRec *record = &opRecord->rsmUndoContainerConfigRec;
    ClusteredHashLabelT *chLabel = DbShmPtrToAddr(record->shmAddr);
    if (chLabel == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "get clustered hash label worthless when set max_record_count. segid: %" PRIu32 " offset: %" PRIu32 "",
            record->shmAddr.segId, record->shmAddr.offset);
        return;
    }
    chLabel->labelCfg.maxItemNum = record->maxItemNum;
}
