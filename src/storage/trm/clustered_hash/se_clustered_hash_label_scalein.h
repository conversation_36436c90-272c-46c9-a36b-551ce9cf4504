/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: se_clustered_hash_label_scalein.h
 * Description: clustered hash label scalein header
 * Author: lujiahao
 * Create: 2024/2/23
 */

#ifndef SE_CLUSTERED_HASH_LABEL_SCALEIN_H
#define SE_CLUSTERED_HASH_LABEL_SCALEIN_H

#include "se_clustered_hash_label_base.h"

#ifdef __cplusplus
extern "C" {
#endif

bool ClusteredHashSegPairCanScaleIn(ChLabelRunCtxT *runCtx, const ChLabelPageMetaDataT *pageDataA,
    uint8_t **segPageAddrA, const ChLabelPageMetaDataT *pageDataB, uint8_t **segPageAddrB);

bool ClusteredHashSegPairCanScaleIn4MulVersion(ChLabelRunCtxT *runCtx, const ChLabelPageMetaDataT *pageDataA,
    uint8_t **segPageAddrA, const ChLabelPageMetaDataT *pageDataB, uint8_t **segPageAddrB);

bool ClusteredHashSegmentTryMerge(ChLabelRunCtxT *runCtx, ChLabelSegmentPageT *segmentA, ChLabelSegmentPageT *segmentB,
    const ChLabelPageMetaDataT *pageDataA, const ChLabelPageMetaDataT *pageDataB);

void ClusteredHashSegmentMerge4MulVersion(ChLabelRunCtxT *runCtx, ChLabelSegmentPageT *segmentA,
    ChLabelSegmentPageT *segmentB, ChLabelPageMetaDataT pageDataA, ChLabelPageMetaDataT pageDataB);

void ClusteredHashEmptySegmentMerge(
    ChLabelRunCtxT *runCtx, ChLabelSegmentPageT *segmentA, ChLabelSegmentPageT *segmentB);

StatusInter ClusteredHashGetSegPair(
    ChLabelRunCtxT *runCtx, ChLabelSegmentPageT *segmentA, ChLabelSegmentPageT *segmentB, uint32_t index);

#ifdef __cplusplus
}
#endif

#endif
