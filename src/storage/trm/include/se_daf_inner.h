/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author:
 * Create: 2024-04-15
 */
#ifndef SE_DAF_INNER_H
#define SE_DAF_INNER_H
#include "se_daf.h"

#ifdef __cplusplus
extern "C" {
#endif
struct IdxDeleteActions {  // 用于DAF延迟删除操作，batchNum=1时为单点删除
    TagLinkedListT node;
    TrxIdT commitTs;  // commit ID, 也是事务提交时刻的逻辑时钟
    uint32_t batchNum;
    HeapDmIndexIterT idxIter;
    uint32_t labelId;  // heap 的labelId
    DmLabelTypeE labelType;
    IndexKeyT *idxKey;
    HpTupleAddr *addr;  // array nums = batchNum
};

typedef struct DafFunc {
    bool (*isDafMgrInited)(void);
    StatusInter (*dafCreateActionCollection)(TrxT *trx);
    void (*dafCommitActionCollection)(TrxT *trx);
    void (*dafCommit)(TrxT *trx);
    void (*dafRollbackActions)(TrxT *trx);
    Status (*idxDelayDelete)(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr);
    Status (*idxDelayBatchDelete)(IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum);
    bool (*dafPurgerMain)(DafPurgerRunCtxHdlT purgerRunCtx);
} DafFuncT;

struct DafMgr {
    DbMemCtxT *memCtx;
    DbSpinLockT lock;
    TagLinkedListT dafActionList;  // // a linked list of Deferred Actions, e.g.IdxDeleteActionsT
    bool isInit;
};

void DafSetAmFunc(const DafFuncT *const am);
SO_EXPORT StatusInter DafMgrInit(DbMemCtxT *memCtx);
SO_EXPORT void DafMgrUnInit(void);

// purger
typedef struct DafPurgerRunCtx {
    SeRunCtxT *seCtx;         // 存储实例
    DbMemCtxT *memCtx;        // purge过程使用的内存
    DbListT vertexLabelList;  // 保存每轮purge获取的label，用于释放
    DbListT kvLabelList;      // 保存每轮purge获取的label，用于释放
    DbListT edgeLabelList;    // 保存每轮purge获取的label，用于释放
    DbListT vertexList;       // 保存每轮purger为提交申请的vertx，用于释放
    UndoPurgerCfgT cfg;       // 逻辑相同，复用一下
    uint64_t startTime;       // 每轮开始时间
} DafPurgerRunCtxT;

#ifdef __cplusplus
}
#endif
#endif  // SE_DAF_INNER_H
