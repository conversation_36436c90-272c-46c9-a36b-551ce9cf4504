/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: se_clustered_hash_access_dm.h
 * Description: clustered Hash Storage Level API
 * Author: wenming
 * Create: 2023/06/01
 */

#ifndef SE_CLUSTERED_HASH_INNER_H
#define SE_CLUSTERED_HASH_INNER_H

#include "se_clustered_hash.h"
#include "se_hash_common.h"
#include "se_hash_index.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct TagChDmDetailT {
    void *dmInfo;
    uint32_t labelId;
} ChLabelDmDetailT;

typedef struct TagClusteredHashRowHead ChRowHeadT;

typedef struct TagClusteredHashTupleT ChTupleT;

typedef struct TagChLogicRowId {
    HtHashCodeT hashCode;
    uint16_t version;
    uint16_t tupleId;
} ChLogicRowId;

typedef struct TagChPhyRowId {
    HtHashCodeT hashCode;
    uint16_t version;
    uint16_t tupleId;
} ChPhyRowId;

void ClusteredHashPrepareTuple(ChLabelRunHdlT runCtx, ChRowHeadT *rowHead, ChTupleT *tuple, bool isCopyRowHead);

// undo依赖此接口
StatusInter ClusteredHashUpdateByRowId(
    ChLabelRunHdlT runCtx, ChLogicRowId logicRowId, const HeapTupleBufT *tupleBuf, bool isRollBack);

void ClusteredHashReleaseTuple(ChLabelRunHdlT runCtx, ChTupleT *tuple);

ChLabelDmDetailT ClusteredHashGetDmDetailInfo(ChLabelRunHdlT chRunHdl);

void ClusteredHashGetDmNextSndKeyInfo(ChLabelRunHdlT chRunHdl, HeapDmIndexIterT *dmSndKeyInfo);

bool ClusteredHashCanAddScaleInTask(ChLabelRunHdlT runCtx);

void ClusteredHashReleaseTuple(ChLabelRunHdlT runCtx, ChTupleT *tuple);

#ifdef __cplusplus
}
#endif
#endif
