/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: declaration of vlivf
 * Author:
 * Create: 2024-9-14
 */

#ifndef SE_VLIVF_INDEX_H
#define SE_VLIVF_INDEX_H

#include "se_define.h"
#include "se_index.h"
#include "adpt_types.h"
#include "se_instance.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

Status VlIvfIndexCreate(SeRunCtxT *seRunCtx, IndexMetaCfgT idxCfg, ShmemPtrT *idxShmAddr);
Status VlIvfIndexDrop(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr);
Status VlIvfIndexOpen(IndexCtxT *idxCtx);
void VlIvfIndexClose(IndexCtxT *idxCtx);
Status VlIvfIndexBeginScan(IndexCtxT *idxCtx, IndexScanCfgT cfg, IndexScanItrT *iter);
Status VlIvfIndexScan(IndexCtxT *idxCtx, IndexScanItrT iter, HpTupleAddr *addr, bool *isFound);
void VlIvfIndexEndScan(const IndexCtxT *idxCtx, IndexScanItrT iter);
Status VlIvfIndexInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr dataId);
void VlIvfGetCtxSize(size_t *ctxSize, size_t *iterSize);
Status VlIvfIndexLoad(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr dataId);
Status VlIvfIndexDelete(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr dataId, IndexRemoveParaT para);
Status VlIvfIndexUndoInsert(IndexCtxT *idxCtx, IndexKeyT key, HpTupleAddr rowId);
Status VlIvfIndexUndoRemove(IndexCtxT *idxCtx, IndexKeyT key, HpTupleAddr rowId);

#ifdef __cplusplus
}
#endif  // __cplusplus

#endif  // SE_VLIVF_INDEX_H
