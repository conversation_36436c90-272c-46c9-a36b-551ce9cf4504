/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: implementation of vlivf
 * Author:
 * Create: 2024-9-14
 */

#include "se_vlivf_index.h"
#include "se_vlivf_cluster.h"
#include "dm_data_basic.h"
#include "dm_data_index.h"
#include "db_vector_distance.h"
#include "db_vector_quantization.h"
#include "se_page_mgr.h"
#include "se_index_inner.h"
#include "db_similarity_priority_queue.h"

typedef struct VlIvfIdxRunCtx {
    VlIvfClusMgrT clusMgr;
    PageIdT metaPageId;
    PageIdT entryPageL0;
    PageIdT oodEntry;
    float oodThreshold;
    float scanRatio;
    float candiRatio;
    uint8_t oodMetric;
    bool buildFinish;
} VlIvfIdxRunCtxT;

Status VlIvfIndexOpen(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    VlIvfIdxRunCtxT *runCtx = (VlIvfIdxRunCtxT *)idxCtx->idxRunCtx;
    PageMgrT *pageMgr = idxCtx->idxOpenCfg.seRunCtx->pageMgr;
    DbSessionCtxT *resSessionCtx = &(idxCtx->idxOpenCfg.seRunCtx->resSessionCtx);
    runCtx->metaPageId = *(PageIdT *)&idxCtx->idxShmAddr;
    idxCtx->isAcquireLockByTryOnce = true;
    VlIvfIdxMetaPageT *metaPage = NULL;
    StatusInter statusInner = VlIvfGetMetaPage(resSessionCtx, pageMgr, runCtx->metaPageId, &metaPage, false);
    if (statusInner != STATUS_OK_INTER) {
        return DbGetExternalErrno(statusInner);
    }
    VlIvfIdxInitClusMgr(&runCtx->clusMgr, pageMgr, idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx, metaPage, resSessionCtx);
    runCtx->entryPageL0 = metaPage->entryPageL0;
    runCtx->oodThreshold = metaPage->oodThreshold;
    runCtx->oodEntry = metaPage->oodEntry;
    runCtx->buildFinish = metaPage->loadProc.buildFinish;
    runCtx->scanRatio = metaPage->scanRatio;
    runCtx->candiRatio = metaPage->candiRatio;
    runCtx->oodMetric = metaPage->oodMetric;
    VlIvfLeavePage(resSessionCtx, pageMgr, runCtx->metaPageId, &metaPage->pageHead, false, false);
    return GMERR_OK;
}

Status VlIvfPrioQuePutItem(uint8_t *item, uint8_t *data, uint32_t dataLen)
{
    *(VlIvfAddrU *)item = *(VlIvfAddrU *)data;
    return GMERR_OK;
}

DbSimilPrioQueT *VlIvfCreateSimilPrioQue(VlIvfIdxRunCtxT *runCtx, DbSimilPrioQueT *oldQue, uint32_t cap)
{
    DbSimilPrioQueFuncsT funcs = {
        .initItem = NULL, .resetItem = NULL, .releaseItem = NULL, .putItem = VlIvfPrioQuePutItem};
    return DbReCreateSimilPrioQue(oldQue, cap, runCtx->clusMgr.memctx, sizeof(VlIvfAddrU), funcs);
}

void VlIvfIndexClose(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    VlIvfIdxRunCtxT *runCtx = (VlIvfIdxRunCtxT *)idxCtx->idxRunCtx;
    runCtx->metaPageId = SE_INVALID_PAGE_ADDR;
}

static inline uint32_t VlIvfCalcLastLevelCandidateCentroidsNumber(VlIvfIdxRunCtxT *runCtx)
{
    uint32_t prevLevelCentroids = 1;
    for (uint8_t i = 0; i < runCtx->clusMgr.centInfo.nLevel; i++) {
        prevLevelCentroids = prevLevelCentroids * runCtx->clusMgr.centInfo.nCent[i] * runCtx->scanRatio;
    }
    return prevLevelCentroids + 1;
}

typedef struct VlIvfIdxScanIter {
    uint32_t pos;
    uint32_t nCandidate;
    HpTupleAddr *candidate;
} VlIvfIdxScanIterT;

Status VlIvfInitScanIter(VlIvfIdxScanIterT *iter, DbMemCtxT *memctx, uint32_t nCandidate)
{
    iter->nCandidate = nCandidate;
    uint32_t size = iter->nCandidate * sizeof(HpTupleAddr);
    iter->candidate = (HpTupleAddr *)DbDynMemCtxAlloc(memctx, size);
    if (iter->candidate == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc memory %u bytes for HpTupleAddr go wrong.", size);
        return GMERR_OUT_OF_MEMORY;
    }
    iter->pos = 0;
    return GMERR_OK;
}

void VlIvfFreeScanIter(VlIvfIdxScanIterT *iter, DbMemCtxT *memctx)
{
    if (iter->candidate != NULL) {
        DbDynMemCtxFree(memctx, iter->candidate);
        iter->candidate = NULL;
    }
}

typedef struct VlIvfScanStat {
    VlIvfIdxRunCtxT *runCtx;
    VlIvfIdxScanIterT *scanIter;
    DbSimilPrioQueT *que;
    PageIdT *entryPages;
    uint32_t nCentCandi;
    uint8_t *keyData;
} VlIvfScanStatT;

Status VlIvfInitScanStat(VlIvfScanStatT *stat, VlIvfIdxRunCtxT *runCtx, IndexScanCfgT *cfg, VlIvfIdxScanIterT *iter)
{
    stat->runCtx = runCtx;
    stat->scanIter = iter;
    stat->nCentCandi = VlIvfCalcLastLevelCandidateCentroidsNumber(runCtx);
    uint32_t size = stat->nCentCandi * sizeof(PageIdT);
    stat->entryPages = (PageIdT *)DbDynMemCtxAlloc(runCtx->clusMgr.memctx, size);
    if (stat->entryPages == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc memory %u bytes for entryPageids go wrong.", size);
        return GMERR_OUT_OF_MEMORY;
    }
    uint32_t cap = DB_MAX(stat->nCentCandi, iter->nCandidate);
    stat->que = VlIvfCreateSimilPrioQue(runCtx, NULL, cap);
    if (stat->que == NULL) {
        DbDynMemCtxFree(runCtx->clusMgr.memctx, stat->entryPages);
        return GMERR_OUT_OF_MEMORY;
    }
    stat->keyData = cfg->leftKey->keyData;
    return GMERR_OK;
}

void VlIvfFreeScanStat(VlIvfScanStatT *stat)
{
    DbDynMemCtxFree(stat->runCtx->clusMgr.memctx, stat->entryPages);
    DbDestroySimilPrioQue(stat->que);
}

void VlIvfScanStatProcess(VlIvfScanStatT *stat)
{
    uint32_t nEntry = 1;
    VlIvfClusMgrT *clusMgr = &stat->runCtx->clusMgr;
    VlIvfCentInfoT *centInfo = &clusMgr->centInfo;
    stat->entryPages[0] = stat->runCtx->entryPageL0;
    for (uint8_t i = 0; i < centInfo->nLevel; i++) {
        uint32_t nCandi = nEntry * centInfo->nCent[i] * stat->runCtx->scanRatio;
        stat->que = VlIvfCreateSimilPrioQue(stat->runCtx, stat->que, nCandi);
        for (uint32_t j = 0; j < nEntry; j++) {
            StatusInter statusInter = VlIvfScanCluster(clusMgr, stat->entryPages[j], stat->que, NULL, stat->keyData);
            if (statusInter != STATUS_OK_INTER) {
                DB_LOG_AND_SET_LASERR(DbGetExternalErrno(statusInter), "Scan level %u, no.%u cluster go wrong.", i, j);
            }
        }
        for (uint32_t j = 0; j < stat->que->currentCount; j++) {
            DbSimilPrioQueItemT *queItem = DbSimilPrioQueGetItem(stat->que, j);
            stat->entryPages[j] = ((VlIvfAddrU *)queItem->item)->entry;
        }
        nEntry = stat->que->currentCount;
    }
    stat->entryPages[nEntry] = stat->runCtx->oodEntry;
    nEntry++;
    stat->que = VlIvfCreateSimilPrioQue(stat->runCtx, stat->que, stat->scanIter->nCandidate);
    for (uint32_t j = 0; j < nEntry; j++) {
        StatusInter statusInter = VlIvfScanCluster(clusMgr, stat->entryPages[j], stat->que, NULL, stat->keyData);
        if (statusInter != STATUS_OK_INTER) {
            DB_LOG_AND_SET_LASERR(DbGetExternalErrno(statusInter), "Scan no.%u cluster go wrong.", j);
        }
        VlIvfClusSegEntryPageT *clusEntryPage = NULL;
        statusInter = VlIvfGetClusSegEntryPage(clusMgr, stat->entryPages[j], &clusEntryPage, true);
        if (statusInter != STATUS_OK_INTER) {
            DB_LOG_AND_SET_LASERR(DbGetExternalErrno(statusInter), "Get cluster entry page for scanNum go wrong.");
        }
        clusEntryPage->clusPage.scanNum++;
        VlIvfLeavePage(clusMgr->resSessionCtx, clusMgr->pageMgr, stat->entryPages[j], &clusEntryPage->clusPage.pageHead,
            true, true);
    }
    for (uint32_t i = 0; i < stat->que->currentCount; i++) {
        DbSimilPrioQueItemT *queItem = DbSimilPrioQueGetItem(stat->que, i);
        stat->scanIter->candidate[i] = ((VlIvfAddrU *)queItem->item)->hpAddr;
    }
    stat->scanIter->nCandidate = stat->que->currentCount;
}

Status VlIvfIndexBeginScan(IndexCtxT *idxCtx, IndexScanCfgT cfg, IndexScanItrT *iter)
{
    DB_POINTER(idxCtx);
    if (cfg.rightKey->keyLen != 0) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Right key is not support in vlivf index.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    VlIvfIdxRunCtxT *runCtx = (VlIvfIdxRunCtxT *)idxCtx->idxRunCtx;
    if (!runCtx->buildFinish) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Index building has not been completed.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    VlIvfIdxScanIterT *scanIter = (VlIvfIdxScanIterT *)(runCtx + 1);
    Status ret = VlIvfInitScanIter(scanIter, runCtx->clusMgr.memctx, cfg.scanLmit * runCtx->candiRatio);
    if (ret != GMERR_OK) {
        return ret;
    }

    VlIvfScanStatT scanStat;
    ret = VlIvfInitScanStat(&scanStat, runCtx, &cfg, scanIter);
    if (ret != GMERR_OK) {
        VlIvfFreeScanIter(scanIter, runCtx->clusMgr.memctx);
        return ret;
    }

    VlIvfScanStatProcess(&scanStat);
    VlIvfFreeScanStat(&scanStat);
    *iter = scanIter;
    return GMERR_OK;
}

Status VlIvfIndexScan(IndexCtxT *idxCtx, IndexScanItrT iter, HpTupleAddr *addr, bool *isFound)
{
    DB_POINTER(idxCtx);
    VlIvfIdxScanIterT *scanIter = (VlIvfIdxScanIterT *)iter;
    IndexOpenCfgT *idxCfg = &idxCtx->idxOpenCfg;
    *isFound = false;
    while (scanIter->pos < scanIter->nCandidate) {
        bool isExist = false;
        HpTupleAddr hpAddr = scanIter->candidate[scanIter->pos];
        scanIter->pos++;
        Status ret = idxCfg->callbackFunc.addrCheckAndFetch(idxCtx, idxCfg->heapHandle, hpAddr, &isExist);
        if (ret == GMERR_OK && isExist) {
            *addr = hpAddr;
            *isFound = true;
            break;
        }
    }
    return GMERR_OK;
}

void VlIvfIndexEndScan(const IndexCtxT *idxCtx, IndexScanItrT iter)
{
    DB_POINTER(idxCtx);
    VlIvfIdxRunCtxT *runCtx = (VlIvfIdxRunCtxT *)idxCtx->idxRunCtx;
    VlIvfIdxScanIterT *scanIter = (VlIvfIdxScanIterT *)iter;
    VlIvfFreeScanIter(scanIter, runCtx->clusMgr.memctx);
}

bool VlIvfInsertCheckIsOod(VlIvfIdxRunCtxT *runCtx, float disToCentL2, float disToCentIp)
{
    if (runCtx->oodMetric == DB_VECTOR_METRIC_IP) {
        return VlIvfCheckIsOod(runCtx->oodThreshold, disToCentIp);
    } else {
        return VlIvfCheckIsOod(runCtx->oodThreshold, disToCentL2);
    }
}

Status VlIvfIndexInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr dataId)
{
    DB_POINTER(idxCtx);
    VlIvfIdxRunCtxT *runCtx = (VlIvfIdxRunCtxT *)idxCtx->idxRunCtx;

    if (!runCtx->buildFinish) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Index building has not been completed.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    PageIdT entry = runCtx->entryPageL0;
    DbSimilPrioQueT *que = NULL;
    DbSimilPrioQueT *oodQue = NULL;
    float disToCentL2 = FLT_MAX;
    float disToCentIp = -FLT_MAX;
    uint8_t tmpMetric = runCtx->clusMgr.vecInfo.metric;
    runCtx->clusMgr.vecInfo.metric = (uint8_t)DB_VECTOR_METRIC_L2;
    for (uint8_t i = 0; i < runCtx->clusMgr.centInfo.nLevel; i++) {
        que = VlIvfCreateSimilPrioQue(runCtx, que, 1);
        if (que == NULL) {
            return GMERR_OUT_OF_MEMORY;
        }

        if (runCtx->oodMetric == DB_VECTOR_METRIC_IP) {
            oodQue = VlIvfCreateSimilPrioQue(runCtx, oodQue, 1);
            if (oodQue == NULL) {
                DbDestroySimilPrioQue(que);
                return GMERR_OUT_OF_MEMORY;
            }
        }

        StatusInter statusInter = VlIvfScanCluster(&runCtx->clusMgr, entry, que, oodQue, idxKey.keyData);
        if (statusInter != STATUS_OK_INTER) {
            DbDestroySimilPrioQue(que);
            DbDestroySimilPrioQue(oodQue);
            return DbGetExternalErrno(statusInter);
        }

        DbSimilPrioQueItemT *queItem = DbSimilPrioQueGetItem(que, 0);
        entry = ((VlIvfAddrU *)queItem->item)->entry;
        disToCentL2 = queItem->similarity;

        if (runCtx->oodMetric == DB_VECTOR_METRIC_IP) {
            DbSimilPrioQueItemT *oodQueItem = DbSimilPrioQueGetItem(oodQue, 0);
            disToCentIp = oodQueItem->similarity;
        }
    }
    DbDestroySimilPrioQue(que);
    DbDestroySimilPrioQue(oodQue);

    runCtx->clusMgr.vecInfo.metric = tmpMetric;
    if (VlIvfInsertCheckIsOod(runCtx, disToCentL2, disToCentIp)) {
        entry = runCtx->oodEntry;
    }
    VlIvfClusTupleT clusTuple = {.addr.hpAddr = dataId, .disToCent = disToCentL2, .keyData = idxKey.keyData};
    StatusInter statusInter = VlIvfInsertCluster(&runCtx->clusMgr, &clusTuple, entry);
    return DbGetExternalErrno(statusInter);
}

void VlIvfGetCtxSize(size_t *ctxSize, size_t *iterSize)
{
    DB_POINTER2(ctxSize, iterSize);
    *ctxSize = sizeof(VlIvfIdxRunCtxT);
    *iterSize = sizeof(VlIvfIdxScanIterT);
}

void VlIvfUpdateLoadProc(VlIvfIdxMetaPageT *metaPage, VlIvfLoadProcT *loadProc, VlIvfCentInfoT *centInfo)
{
    loadProc->path.clusPath[loadProc->path.level]++;
    loadProc->path.clusPath[loadProc->path.level] %= centInfo->nCent[loadProc->path.level];
    if (loadProc->path.clusPath[loadProc->path.level] != 0) {
        return;
    }
    bool nextLevel = true;
    uint8_t addLevel = 0;
    for (uint8_t i = 0; i < loadProc->path.level; i++) {
        if (loadProc->path.clusPath[i] + 1 < centInfo->nCent[i]) {
            nextLevel = false;
            addLevel = i;
        }
    }
    if (!nextLevel) {
        loadProc->path.clusPath[addLevel]++;
        for (uint8_t i = addLevel + 1; i < loadProc->path.level; i++) {
            loadProc->path.clusPath[i] = 0;
        }
        return;
    }
    loadProc->path.level++;
    if (loadProc->path.level == centInfo->nLevel) {
        loadProc->buildFinish = true;
        metaPage->idxBase.isConstructed = 1;
        return;
    }
    for (uint8_t i = 0; i <= loadProc->path.level; i++) {
        loadProc->path.clusPath[i] = 0;
    }
}

Status VlIvfIndexLoad(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr dataId)
{
    DB_POINTER(idxCtx);
    VlIvfIdxRunCtxT *runCtx = (VlIvfIdxRunCtxT *)idxCtx->idxRunCtx;
    DbSessionCtxT *resSessionCtx = runCtx->clusMgr.resSessionCtx;
    PageMgrT *pageMgr = runCtx->clusMgr.pageMgr;
    if (runCtx->buildFinish) {
        return GMERR_OK;
    }
    VlIvfIdxMetaPageT *metaPage = NULL;
    StatusInter statusInner = VlIvfGetMetaPage(resSessionCtx, pageMgr, runCtx->metaPageId, &metaPage, true);
    if (statusInner != STATUS_OK_INTER) {
        return DbGetExternalErrno(statusInner);
    }

    VlIvfClusTupleT clusTuple;
    clusTuple.keyData = (uint8_t *)DbDynMemCtxAlloc(runCtx->clusMgr.memctx, runCtx->clusMgr.vecInfo.itemSize);
    if (clusTuple.keyData == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc memory for keydata go wrong.");
        VlIvfLeavePage(resSessionCtx, pageMgr, runCtx->metaPageId, &metaPage->pageHead, true, false);
        return GMERR_OUT_OF_MEMORY;
    }
    VlIvfLoadProcT *loadProc = &metaPage->loadProc;
    PageIdT entryPageId = runCtx->entryPageL0;
    float disToCent = FLT_MAX;
    for (uint8_t i = 0; i < loadProc->path.level; i++) {
        statusInner = VlIvfGetClusterTupleById(&runCtx->clusMgr, &clusTuple, entryPageId, loadProc->path.clusPath[i]);
        if (statusInner != STATUS_OK_INTER) {
            DbDynMemCtxFree(runCtx->clusMgr.memctx, clusTuple.keyData);
            VlIvfLeavePage(resSessionCtx, pageMgr, runCtx->metaPageId, &metaPage->pageHead, true, false);
            return DbGetExternalErrno(statusInner);
        }
        entryPageId = clusTuple.addr.entry;
        uint8_t tmpMetric = runCtx->clusMgr.vecInfo.metric;
        runCtx->clusMgr.vecInfo.metric = (uint8_t)DB_VECTOR_METRIC_L2;
        disToCent =
            VlIvfGetVecDis(&runCtx->clusMgr.vecInfo, clusTuple.keyData, idxKey.keyData, runCtx->clusMgr.vecInfo.metric);
        runCtx->clusMgr.vecInfo.metric = tmpMetric;
    }

    DbDynMemCtxFree(runCtx->clusMgr.memctx, clusTuple.keyData);
    clusTuple.disToCent = disToCent;
    clusTuple.keyData = idxKey.keyData;
    statusInner = VlIvfUpdateClusterKeyDataById(
        &runCtx->clusMgr, &clusTuple, entryPageId, loadProc->path.clusPath[loadProc->path.level]);
    if (statusInner != STATUS_OK_INTER) {
        VlIvfLeavePage(resSessionCtx, pageMgr, runCtx->metaPageId, &metaPage->pageHead, true, false);
        return DbGetExternalErrno(statusInner);
    }
    VlIvfUpdateLoadProc(metaPage, loadProc, &metaPage->centInfo);
    VlIvfLeavePage(resSessionCtx, pageMgr, runCtx->metaPageId, &metaPage->pageHead, true, true);
    return GMERR_OK;
}

Status VlIvfIndexDelete(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr dataId, IndexRemoveParaT para)
{
    DB_POINTER2(idxCtx, idxKey.keyData);
    return GMERR_OK;
}

Status VlIvfIndexUndoInsert(IndexCtxT *idxCtx, IndexKeyT key, HpTupleAddr rowId)
{
    DB_POINTER2(idxCtx, key.keyData);
    return GMERR_OK;
}

Status VlIvfIndexUndoRemove(IndexCtxT *idxCtx, IndexKeyT key, HpTupleAddr rowId)
{
    DB_POINTER2(idxCtx, key.keyData);
    return GMERR_OK;
}

StatusInter uint32ArrayToString(uint32_t *array, uint32_t size, char *result, uint32_t resultSize)
{
    uint32_t currentLength = 0;

    for (uint32_t i = 0; i < size; ++i) {
        char buffer[UINT32_TO_STR_BUFFER_LEN];

        int written = snprintf_s(buffer, sizeof(buffer), sizeof(buffer) - 1, "%u", array[i]);
        if (written < 0) {
            DB_LOG_AND_SET_LASERR(FIELD_OVERFLOW_INTER, "String length exceeds, truncating.");
            return FIELD_OVERFLOW_INTER;
        }

        errno_t err = strcat_s(result, resultSize, buffer);
        if (err != EOK) {
            DB_LOG_AND_SET_LASERR(FIELD_OVERFLOW_INTER, "Unable to strcat when convert uint32 to string.");
            return FIELD_OVERFLOW_INTER;
        }
        currentLength += written;

        if (i < size - 1) {
            if (currentLength + 1 >= resultSize) {
                DB_LOG_AND_SET_LASERR(FIELD_OVERFLOW_INTER, "String length exceeds, truncating.");
                return FIELD_OVERFLOW_INTER;
            }
            err = strcat_s(result, resultSize, ",");
            if (err != EOK) {
                DB_LOG_AND_SET_LASERR(FIELD_OVERFLOW_INTER, "Unable to strcat when convert uint32 to string.");
                return FIELD_OVERFLOW_INTER;
            }
            currentLength++;
        }
    }
    return STATUS_OK_INTER;
}

StatusInter VlIvfIdxReadVectorSetStat(VlIvfClusterStatisticsT *nowCluster, float distanceMin, float intervalSize,
    int *disToCentDistribution, uint32_t disToCentArrayLength)
{
    uint32_t vectorNum = nowCluster->vectorNum;
    int written = 0;
    // get the distribution of distance
    for (uint32_t i = 0; i < disToCentArrayLength; ++i) {
        uint32_t len = sizeof(nowCluster->disToCentNumStr[i]);
        char *disStr = nowCluster->disToCentNumStr[i];
        if (vectorNum == 0) {
            written = snprintf_s(disStr, len, len - 1, "[N/A, N/A), N/A");
        } else if (vectorNum == 1) {
            if (i == 0) {
                written = snprintf_s(disStr, len, len - 1, "[%.2f, %.2f], %d", distanceMin + i * intervalSize,
                    distanceMin + (i + 1) * intervalSize, disToCentDistribution[i]);
            } else {
                written = snprintf_s(disStr, len, len - 1, "[N/A, N/A), N/A");
            }
        } else {
            if (i == disToCentArrayLength - 1) {
                written = snprintf_s(disStr, len, len - 1, "[%.2f, %.2f], %d", distanceMin + i * intervalSize,
                    distanceMin + (i + 1) * intervalSize, disToCentDistribution[i]);
            } else {
                written = snprintf_s(disStr, len, len - 1, "[%.2f, %.2f), %d", distanceMin + i * intervalSize,
                    distanceMin + (i + 1) * intervalSize, disToCentDistribution[i]);
            }
        }
        if (written < 0) {
            DB_LOG_AND_SET_LASERR(FIELD_OVERFLOW_INTER, "String length exceeds, truncating.");
            return FIELD_OVERFLOW_INTER;
        }
    }
    return STATUS_OK_INTER;
}

StatusInter VlIvfIdxReadVector(
    VlIvfClusMgrT *clusMgr, PageIdT entryPage, VlIvfIndexStatisticsT *stat, uint32_t *clusViewInfoIdx)
{
    // has get vector entry page
    float distanceMin = FLT_MAX;
    float distanceMax = FLT_MIN;
    float intervalSize = 0.0f;
    int disToCentDistribution[DM_MAX_DISTANCE_RANGE_NUM] = {0};
    // Traverse twice. Obtain the maximum and minimum values for the first time
    for (int iter = 0; iter < NUM_ITERRATION_VECTOR_PAGE; ++iter) {
        if (stat->cluster[*clusViewInfoIdx].vectorNum == 0) {
            // none of vector in a cluster
            break;
        }
        if (iter != 0) {
            intervalSize = (distanceMax - distanceMin) / (DM_MAX_DISTANCE_RANGE_NUM * 1.0f);
        }
        PageIdT pageId = entryPage;
        while (DbIsPageIdValid(pageId)) {
            VlIvfClusPageT *clusPage = NULL;
            StatusInter statusInnter = VlIvfGetClusPage(clusMgr, pageId, &clusPage, false);
            if (statusInnter != STATUS_OK_INTER) {
                DB_LOG_AND_SET_LASERR(
                    DbGetExternalErrno(statusInnter), "Get cluster page for release cluster go wrong.");
                return statusInnter;
            }
            // for all the tuple in one VlIvfClusPageT
            for (int i = 0; i < clusPage->nItemsInPage; ++i) {
                // tuple == vector
                VlIvfTupleT *tuple = VlIvfGetTupleFromPage(&clusPage->pageHead, i, clusMgr->vecInfo.itemSize);
                if (iter == 0) {
                    // first time
                    distanceMin = DB_MIN(distanceMin, tuple->disToCent);
                    distanceMax = DB_MAX(distanceMax, tuple->disToCent);
                } else {
                    // second time
                    uint32_t index = (uint32_t)((tuple->disToCent - distanceMin) / intervalSize);
                    if (index == DM_MAX_DISTANCE_RANGE_NUM)
                        index = (DM_MAX_DISTANCE_RANGE_NUM - 1);  // boundary values in the last interval
                    disToCentDistribution[index]++;
                }
            }
            // next page
            PageIdT nextPageId = clusPage->nextPage;
            VlIvfLeavePage(clusMgr->resSessionCtx, clusMgr->pageMgr, pageId, &clusPage->pageHead, false, false);
            pageId = nextPageId;
        }
    }
    if (stat->cluster[*clusViewInfoIdx].vectorNum == 0)
        distanceMax = 0.0f;
    stat->cluster[*clusViewInfoIdx].clusterRadius = distanceMax;
    return VlIvfIdxReadVectorSetStat(
        &stat->cluster[*clusViewInfoIdx], distanceMin, intervalSize, disToCentDistribution, DM_MAX_DISTANCE_RANGE_NUM);
}

StatusInter VlIvfIdxReadClusterSetStat(VlIvfClusMgrT *clusMgr, VlIvfClusPageT *clusPage, VlIvfIndexStatisticsT *stat,
    uint32_t *clusViewInfoIdx, bool isOod)
{
    StatusInter statusInnter = STATUS_OK_INTER;
    // for all the tuple in one VlIvfClusPageT
    for (int i = 0; i < clusPage->nItemsInPage; ++i) {
        // tuple == centorid
        VlIvfTupleT *tuple = VlIvfGetTupleFromPage(&clusPage->pageHead, i, clusMgr->vecInfo.itemSize);
        // get the entry page for a cluster centroid
        VlIvfClusSegEntryPageT *vectorEntryPage = NULL;
        // inner pageId
        PageIdT tuplePageId = tuple->addr.entry;
        statusInnter = VlIvfGetClusSegEntryPage(clusMgr, tuplePageId, &vectorEntryPage, false);
        if (statusInnter != STATUS_OK_INTER) {
            DB_LOG_AND_SET_LASERR(
                DbGetExternalErrno(statusInnter), "Get vector entry page for release cluster go wrong.");
            return statusInnter;
        }
        statusInnter = uint32ArrayToString(vectorEntryPage->path.clusPath,
            sizeof(vectorEntryPage->path.clusPath) / sizeof(uint32_t), stat->cluster[*clusViewInfoIdx].path,
            sizeof(stat->cluster[*clusViewInfoIdx].path));
        if (statusInnter != STATUS_OK_INTER) {
            VlIvfLeavePage(clusMgr->resSessionCtx, clusMgr->pageMgr, tuplePageId, &vectorEntryPage->clusPage.pageHead,
                false, false);
            DB_LOG_AND_SET_LASERR(DbGetExternalErrno(statusInnter), "Convert uint32 array to string go wrong.");
            return statusInnter;
        }
        // current level
        stat->cluster[*clusViewInfoIdx].pageNum = vectorEntryPage->nSegPage;
        stat->cluster[*clusViewInfoIdx].vectorNum = vectorEntryPage->clusSize;
        stat->cluster[*clusViewInfoIdx].scanNum = vectorEntryPage->clusPage.scanNum;
        stat->cluster[*clusViewInfoIdx].level = vectorEntryPage->path.level;
        stat->cluster[*clusViewInfoIdx].isOod = isOod;

        // pageCount = metaPage count + clusterPage count + vectorPage count
        stat->pageCount += vectorEntryPage->nSegPage;

        // usedMemsize =  cluster used memory + vector used memory
        stat->usedMemSize +=
            vectorEntryPage->nSegPage * sizeof(VlIvfClusPageT) + vectorEntryPage->clusSize * sizeof(VlIvfTupleT);

        // read vector
        statusInnter = VlIvfIdxReadVector(clusMgr, tuplePageId, stat, clusViewInfoIdx);
        if (statusInnter != STATUS_OK_INTER) {
            VlIvfLeavePage(clusMgr->resSessionCtx, clusMgr->pageMgr, tuplePageId, &vectorEntryPage->clusPage.pageHead,
                false, false);
            DB_LOG_AND_SET_LASERR(DbGetExternalErrno(statusInnter), "Get vector for view cluster go wrong.");
            return statusInnter;
        }

        *clusViewInfoIdx += 1;
        VlIvfLeavePage(
            clusMgr->resSessionCtx, clusMgr->pageMgr, tuplePageId, &vectorEntryPage->clusPage.pageHead, false, false);
    }
    return statusInnter;
}

StatusInter VlIvfIdxReadCluster(
    VlIvfClusMgrT *clusMgr, PageIdT entryPage, VlIvfIndexStatisticsT *stat, uint32_t *clusViewInfoIdx, bool isOod)
{
    // get cluster entry page
    VlIvfClusSegEntryPageT *clusEntryPage = NULL;
    StatusInter ret = VlIvfGetClusSegEntryPage(clusMgr, entryPage, &clusEntryPage, false);
    if (ret != STATUS_OK_INTER) {
        DB_LOG_AND_SET_LASERR(DbGetExternalErrno(ret), "Get cluster entry page for view cluster go wrong.");
        return ret;
    }
    // pageCount = metaPage count + clusterPage count + vectorPage count
    stat->pageCount += clusEntryPage->nSegPage;

    PageIdT pageId = entryPage;
    while (DbIsPageIdValid(pageId)) {
        VlIvfClusPageT *clusPage = NULL;
        StatusInter statusInnter = VlIvfGetClusPage(clusMgr, pageId, &clusPage, false);
        if (statusInnter != STATUS_OK_INTER) {
            DB_LOG_AND_SET_LASERR(DbGetExternalErrno(ret), "Get cluster page for view cluster go wrong.");
            return statusInnter;
        }
        statusInnter = VlIvfIdxReadClusterSetStat(clusMgr, clusPage, stat, clusViewInfoIdx, isOod);
        if (statusInnter != STATUS_OK_INTER) {
            VlIvfLeavePage(clusMgr->resSessionCtx, clusMgr->pageMgr, pageId, &clusPage->pageHead, false, false);
            DB_LOG_AND_SET_LASERR(DbGetExternalErrno(ret), "Get cluster page for view cluster go wrong.");
            return statusInnter;
        }
        // next page
        PageIdT nextPageId = clusPage->nextPage;
        VlIvfLeavePage(clusMgr->resSessionCtx, clusMgr->pageMgr, pageId, &clusPage->pageHead, false, false);
        pageId = nextPageId;
    }

    VlIvfLeavePage(
        clusMgr->resSessionCtx, clusMgr->pageMgr, entryPage, &clusEntryPage->clusPage.pageHead, false, false);
    return STATUS_OK_INTER;
}

StatusInter VlIvfIdxReadOod(
    VlIvfClusMgrT *clusMgr, PageIdT entryPage, VlIvfIndexStatisticsT *stat, uint32_t *clusViewInfoIdx)
{
    // get cluster entry page
    VlIvfClusSegEntryPageT *clusEntryPage = NULL;
    StatusInter ret = VlIvfGetClusSegEntryPage(clusMgr, entryPage, &clusEntryPage, false);
    if (ret != STATUS_OK_INTER) {
        DB_LOG_AND_SET_LASERR(DbGetExternalErrno(ret), "Get cluster entry page for ood cluster go wrong.");
        return ret;
    }

    // current level
    stat->cluster[*clusViewInfoIdx].pageNum = clusEntryPage->nSegPage;
    stat->cluster[*clusViewInfoIdx].vectorNum = clusEntryPage->clusSize;
    stat->cluster[*clusViewInfoIdx].scanNum = clusEntryPage->clusPage.scanNum;
    stat->cluster[*clusViewInfoIdx].level = clusEntryPage->path.level;
    stat->cluster[*clusViewInfoIdx].isOod = true;

    // ood just one entry for all vector, so don't to read tuple in a VlIvfClusPageT
    // read vector
    ret = VlIvfIdxReadVector(clusMgr, entryPage, stat, clusViewInfoIdx);
    if (ret != STATUS_OK_INTER) {
        VlIvfLeavePage(
            clusMgr->resSessionCtx, clusMgr->pageMgr, entryPage, &clusEntryPage->clusPage.pageHead, false, false);
        DB_LOG_AND_SET_LASERR(DbGetExternalErrno(ret), "Get vector for ood cluster go wrong.");
        return ret;
    }

    *clusViewInfoIdx += 1;
    // pageCount = metaPage count + clusterPage count + vectorPage count
    stat->pageCount += clusEntryPage->nSegPage;

    // usedMemsize =  cluster used memory + vector used memory
    stat->usedMemSize +=
        clusEntryPage->nSegPage * sizeof(VlIvfClusPageT) + clusEntryPage->clusSize * sizeof(VlIvfTupleT);

    VlIvfLeavePage(
        clusMgr->resSessionCtx, clusMgr->pageMgr, entryPage, &clusEntryPage->clusPage.pageHead, false, false);
    return STATUS_OK_INTER;
}

/**
 * @brief Get the statistics of the vlivf index.
 * @param[in] idxShmAddr   the address of the index (meta page)
 * @param[out] idxStat  the index statistics
 */
Status VlIvfIndexStatView(ShmemPtrT idxShmAddr, uint16_t instanceId, IndexStatisticsT *idxStat)
{
    // adapt to other system view
    if (idxStat->vlivfIndex.cluster == NULL) {
        return GMERR_OK;
    }
    // read page manager
    PageMgrT *pageMgr = SeGetPageMgr(instanceId);
    if (pageMgr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "VL-IVF: StatView: null pageMgr.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    // seInstance has judge by SeGetPageMgr
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(instanceId);

    // read the meta page
    VlIvfIdxMetaPageT *metaPage = NULL;
    const PageIdT metaPageId = *(PageIdT *)(&(idxShmAddr));
    StatusInter interErrno = VlIvfGetMetaPage(NULL, pageMgr, metaPageId, &metaPage, false);
    if (interErrno != STATUS_OK_INTER) {
        return DbGetExternalErrno(interErrno);
    }

    // read cluster meta
    VlIvfClusMgrT clusMgr;
    VlIvfIdxInitClusMgr(&clusMgr, pageMgr, seInstance->seServerMemCtx, metaPage, NULL);

    VlIvfIndexStatisticsT *stat = &(idxStat->vlivfIndex);
    // mem statistics
    uint32_t pageSize = seInstance->seConfig.pageSize * DB_KIBI;

    // get the statistics
    // warning: cluster maybe > 500
    uint32_t clusterIdx = 0;

    stat->pageCount = 0;
    // read normal cluster
    // metaPage->entryPageL0
    interErrno = VlIvfIdxReadCluster(&clusMgr, metaPage->entryPageL0, stat, &clusterIdx, false);
    if (interErrno != STATUS_OK_INTER) {
        VlIvfLeavePage(NULL, pageMgr, metaPageId, &metaPage->pageHead, false, false);
        return DbGetExternalErrno(interErrno);
    }
    // read ood cluster
    interErrno = VlIvfIdxReadOod(&clusMgr, metaPage->oodEntry, stat, &clusterIdx);
    if (interErrno != STATUS_OK_INTER) {
        VlIvfLeavePage(NULL, pageMgr, metaPageId, &metaPage->pageHead, false, false);
        return DbGetExternalErrno(interErrno);
    }
    // cluster num
    stat->clusterSize = clusterIdx;

    // applied memory
    stat->totalMemorySize = pageSize * stat->pageCount;

    // vectorSum = vector + cluster
    uint32_t vectorSum = stat->clusterSize;
    for (uint32_t i = 0; i < stat->clusterSize; ++i) {
        vectorSum += stat->cluster[i].vectorNum;
    }

    uint32_t ClusSegEntryPageNum = 2;

    // usedMemsize =  cluster used memory + vector used memory + page used memory
    stat->usedMemSize += vectorSum * metaPage->vecInfo.itemSize +
                         ClusSegEntryPageNum * (uint64_t)sizeof(VlIvfClusSegEntryPageT) +
                         stat->clusterSize * (uint64_t)sizeof(VlIvfClusPageT) + (uint64_t)sizeof(VlIvfIdxMetaPageT);

    // leave page
    VlIvfLeavePage(NULL, pageMgr, metaPageId, &metaPage->pageHead, false, false);
    return GMERR_OK;
}

void VlIvfIndexAmInit(void)
{
    IdxFuncT vlIvfIndexFuncHandle = IdxEmptyIdxFunc();
    vlIvfIndexFuncHandle.idxCreate = VlIvfIndexCreate;
    vlIvfIndexFuncHandle.idxDrop = VlIvfIndexDrop;
    vlIvfIndexFuncHandle.idxOpen = VlIvfIndexOpen;
    vlIvfIndexFuncHandle.idxClose = VlIvfIndexClose;
    vlIvfIndexFuncHandle.idxInsert = VlIvfIndexInsert;
    vlIvfIndexFuncHandle.idxDelete = VlIvfIndexDelete;
    vlIvfIndexFuncHandle.idxBeginScan = VlIvfIndexBeginScan;
    vlIvfIndexFuncHandle.idxScan = VlIvfIndexScan;
    vlIvfIndexFuncHandle.idxEndScan = VlIvfIndexEndScan;
    vlIvfIndexFuncHandle.idxUndoInsert = VlIvfIndexUndoInsert;
    vlIvfIndexFuncHandle.idxUndoRemove = VlIvfIndexUndoRemove;
    vlIvfIndexFuncHandle.idxGetCtxSize = VlIvfGetCtxSize;
    vlIvfIndexFuncHandle.idxLoad = VlIvfIndexLoad;
    vlIvfIndexFuncHandle.idxStatView = VlIvfIndexStatView;
    IdxAmFuncRegister((uint8_t)VLIVF_INDEX, &vlIvfIndexFuncHandle);
}
