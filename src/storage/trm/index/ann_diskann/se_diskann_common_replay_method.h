/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: declaration of diskann replay functions
 * Author: wanyi
 * Create: 2024-02-18
 */

#ifndef SE_DISKANN_COMMON_REPLAY_METHOD_H
#define SE_DISKANN_COMMON_REPLAY_METHOD_H

#include "db_internal_error.h"
#include "se_diskann_types.h"

#ifdef __cplusplus
extern "C" {
#endif  // __cplusplus

typedef struct ReplayArgs {
    uint64_t trxId;
    uint32_t size;
    PageHeadT *pageHead;
    uint8_t *data;
    SeInstanceHdT seInstance;
} ReplayArgsT;

StatusInter DiskAnnIndexSetEdgeReplay(ReplayArgsT *arg);
StatusInter DiskAnnIndexSetNodeReplay(ReplayArgsT *arg);
StatusInter DiskAnnIndexSetTagReplay(ReplayArgsT *arg);
StatusInter DiskAnnIndexInitMetaReplay(ReplayArgsT *arg);
StatusInter DiskAnnIndexMetaAppendReplay(ReplayArgsT *arg);
StatusInter DiskAnnIndexResetMetaReplay(ReplayArgsT *arg);
StatusInter DiskAnnIndexSetNewSlaveReplay(ReplayArgsT *arg);
StatusInter DiskAnnIndexSetNextSlaveIdReplay(ReplayArgsT *arg);
StatusInter DiskAnnIndexSetPreSlaveReplay(ReplayArgsT *arg);
StatusInter DiskAnnIndexRemoveFromNbsReplay(ReplayArgsT *arg);
StatusInter DiskAnnIndexSetFrozensReplay(ReplayArgsT *arg);
StatusInter DiskAnnIndexSetPageHeadReplay(ReplayArgsT *arg);
StatusInter DiskAnnIndexSetDiskAnnNodeHdrReplay(ReplayArgsT *arg);
StatusInter DiskAnnIndexSetMetaReplay(ReplayArgsT *arg);
StatusInter DiskAnnIndexSetDelNodeListHdrReplay(ReplayArgsT *arg);
StatusInter DiskAnnIndexResetNodeReplay(ReplayArgsT *arg);
StatusInter DiskAnnIndexAddToNodeListReplay(ReplayArgsT *arg);

#ifdef __cplusplus
}
#endif  // __cplusplus
#endif  // SE_DISKANN_REPLAY_H
