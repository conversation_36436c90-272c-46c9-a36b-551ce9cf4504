/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: declartion of diskann ctxt
 * Author: wanyi
 * Create: 2024-02-19
 */

#ifndef SE_DISKANN_STORAGE_ADAPTER_H
#define SE_DISKANN_STORAGE_ADAPTER_H

#include "dm_data_annvector.h"
#include "se_diskann_filter.h"
#include "se_diskann_types.h"
#include "se_index.h"

#ifdef __cplusplus
extern "C" {
#endif  // __cplusplus

/**
 * @brief 根据关系表的数据，生成索引表
 * @param[in] adpt
 * @param[in] data
 * @return indicating success or failure
 */
Status DiskAnnLoadData(DiskAnnCtxT *adpt, uint64_t dataId, void *vector, DiskAnnAddrT *outId);

/* --------------- 元数据相关接口 ---------------- */
/**
 * @brief 创建元数据页并返回metaPageId, 若已创建，则直接返回SE_DISKANN_ALREADY_EXIST。
 * @return indicating success or failure
 */
Status DiskAnnInitMetaData(DiskAnnCtxT *adpt, DiskAnnPageIdT *metaPageId);
/**
 * @brief 加载元数据信息到适配层句柄里
 * @return indicating success or failure
 */
Status DiskAnnLoadMetaData(DiskAnnCtxT *adpt, const DiskAnnPageIdT *metaPageId);
/**
 * @brief 释放所有数据
 * @return indicating success or failure
 */
Status DiskAnnFreeData(DiskAnnCtxT *adpt, const DiskAnnPageIdT *metaPageId, bool shouldFreeMeta);

/* --------------- 针对重复节点的接口 ---------------- */
Status DiskAnnGetMasterNode(DiskAnnCtxT *adpt, const DiskAnnAddrT nodeId, DiskAnnAddrT *masterId);
Status DiskAnnAddSlaveNode(
    DiskAnnCtxT *adpt, const DiskAnnAddrT masterId, const DiskAnnAddrT slaveId, bool *addedAsSlave);

/* --------------- 距离计算的相关接口 ---------------- */
/**
 * @brief 计算两个节点之间的距离
 * @param[in] vertexId1
 * @param[in] vertexId2
 * @param[out] res
 * @return indicating success or failure
 */
Status GetDistBetweenVertexs(
    DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId1, const DiskAnnAddrT vertexId2, double *res);

/**
 * @brief 计算指定节点到指定向量的精确距离，并携带vertex节点的信息
 * @param[in] adpt
 * @param[in] vertexId1
 * @param[in] searchParams 如果传入的值为NULL， 那么就以accurate distance计算。否则使用PQ加速计算。
 * @param[out] distance
 * @param[out] out
 * @return indicating success or failure
 */
Status GetAccuDistWithVertexInfoFromVec(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId1,
    DiskAnnSearchParamsT *searchParams, double *distance, DiskAnnVertexT *out);

/**
 * @brief 计算指定节点到指定向量的精确距离
 * @param[in] vertexId1
 * @param[in] vertexId2
 * @param[out] res
 * @return indicating success or failure
 */
Status GetAccuDistFromVec(
    DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId1, AnnVecT *queryVector, const double vecSqrSum, double *distance);

// 根据码本，生成Pq后的向量
Status GenPqVector(DiskAnnCtxT *adpt);

/* --------------- 节点状态相关接口 ---------------- */
bool IsVertexDeleted(DiskAnnVertexT *node);
Status DiskAnnIsDeleted(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId, bool *isDeleted);  // 判断结点是否已被标记删除
Status DiskAnnIsMaster(
    DiskAnnCtxT *adpt, const DiskAnnAddrT masterId, bool *isMaster);  // 判断节点是否是主节点（重复节点链表头）
Status DataIdGet(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId, uint64_t *dataId);  // 获取节点的dataIdl
Status SqrSumGet(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId, double *sqrSum);    // 获取节点的sqrSum属性
Status VectorGet(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId, void **vecs);       // 获取节点的vector数组
Status VectorGetWithDecode(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId, void **vecs);
Status MarkDeleted(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId);  // 把节点的isDelete字段标记为true
Status DiskAnnSetFrozens(DiskAnnCtxT *adpt, DiskAnnPageIdT metaPageId);
Status DiskAnnRenewMeta(DiskAnnCtxT *adpt, void (*metaOperation)(DiskAnnCtxT *adpt, DiskAnnMetaT *meta));
// 从存储层中获得vertexId的节点信息，并组装成DiskAnnNode/DiskAnnVertex/DiskAnnEdge返回
Status DiskAnnNodeReset(DiskAnnCtxT *adpt, DiskAnnAddrT vertexId);
Status DiskAnnNodeGet(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId, DiskAnnNodeT *node);
Status DiskAnnVertexGet(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId, bool requireVec, DiskAnnVertexT **out);
Status DiskAnnEdgeGet(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId, DiskAnnEdgeT **outEdge);
// 将DiskAnnNode信息写入存储层,如果没有设置vertexId, 那么会回填到node->vertexId字段中
Status DiskAnnNodeSet(DiskAnnCtxT *adpt, DiskAnnNodeT *node);
// 只将Edge相关信息写入存储层
Status DiskAnnEdgeSet(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId, const DiskAnnEdgeT *edge);
Status DiskAnnEdgeInit(DiskAnnCtxT *adpt, DiskAnnEdgeT **outEdge);
void DiskAnnEdgeFree(DiskAnnCtxT *adpt, DiskAnnEdgeT **outEdge);
void DiskAnnNodeFree(DiskAnnCtxT *adpt, DiskAnnNodeT **node);
void DiskAnnVertexFree(DiskAnnCtxT *adpt, DiskAnnVertexT **vertex);

Status DiskAnnInitDelNodeListByPageId(DiskAnnCtxT *adpt, DiskAnnPageIdT *pageId, RedoRunCtxT *redoCtx);
Status DiskAnnDelNodeListAdd(DiskAnnCtxT *adpt, DiskAnnAddrT vertexId);
Status DiskAnnDelNodeListDelte(DiskAnnCtxT *adpt, int32_t *fakeDelNodeChg);  // 物理删除列表中所有ID代表的节点

Status ConnectSlaveNeighbors(DiskAnnCtxT *ctxt, DiskAnnAddrT vertexId);

/* --------------- 邻居节点相关接口 ---------------- */
Status IsNeighbor(const DiskAnnAddrT vertexId, const uint32_t potentialNeighborId, bool *isNeighbor);
Status GetNeighborInfo(const DiskAnnAddrT vertexId, DiskannNeighborInfoT *neighborInfo);

// 初始化存储适配层句柄, idxCfg可以为空，为空时用于Drop操作单纯提供PageMgr的上下文。
Status DiskAnnStorageAdapterInit(const IndexMetaCfgT *idxCfg, uint16_t instanceId, DiskAnnCtxT **newAdapter);
void DiskAnnStorageAdapterFree(DiskAnnCtxT *adpt, uint16_t instanceId);

#ifdef __cplusplus
}
#endif  // __cplusplus
#endif  // SE_DISKANN_STORAGE_ADAPTER_H
