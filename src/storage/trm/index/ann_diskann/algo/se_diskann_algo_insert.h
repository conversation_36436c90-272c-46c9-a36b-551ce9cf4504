/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: declartion of diskann insert operation
 * Author: wanyi
 * Create: 2024-02-19
 */

#ifndef SE_DISKANN_ALGO_INSERT_H
#define SE_DISKANN_ALGO_INSERT_H

#include "se_diskann_filter.h"
#include "se_diskann_storage.h"
#include "se_diskann_types.h"
#include "se_vectorarray.h"

#ifdef __cplusplus
extern "C" {
#endif  // __cplusplus

Status DiskAnnAlgoInsertInner(
    DiskAnnCtxT *adpt, VisitedFilterT *vertexFilter, const DiskAnnAddrT vertexId, void *insertVector);
Status DiskAnnReInsertNode(DiskAnnCtxT *adpt, DiskAnnAddrT vertexId);
Status RemoveFromCandidatesPool(const DiskAnnAddrT vertexId, VectorArrayT *pool);

#ifdef __cplusplus
}
#endif  // __cplusplus
#endif  // SE_DISKANN_ALGO_INSERT_H
