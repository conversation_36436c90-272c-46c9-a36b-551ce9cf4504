/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: declaration of diskann build operation
 * Author: wanyi
 * Create: 2024-02-19
 */

#ifndef SE_DISKANN_ALGO_BUILD_H
#define SE_DISKANN_ALGO_BUILD_H

#include "se_diskann_storage.h"
#include "se_diskann_types.h"
#include "se_vectorarray.h"

#ifdef __cplusplus
extern "C" {
#endif  // __cplusplus

Status BuildRandGraph(DiskAnnCtxT *adpt, const DiskAnnAddrT *vertexIds, const uint32_t vertexNum);
Status BuildMainGraph(
    DiskAnnCtxT *adpt, const DiskAnnAddrT *vertexIds, VectorArrayT *vectorArr, const uint32_t numsToLoad);
Status GenFloatFrozenPoints(DiskAnnCtxT *adpt, DiskAnnPageIdT metaPageId, float *ref);
Status GenFloat16FrozenPoints(DiskAnnCtxT *adpt, DiskAnnPageIdT metaPageId, float16 *ref);

#ifdef __cplusplus
}
#endif  // __cplusplus
#endif  // SE_DISKANN_ALGO_BUILD_H
