/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: implementation of diskann insert operation
 * Author: wanyi
 * Create: 2024-02-19
 */

#include "se_diskann_algo_insert.h"

#include "se_ann_utils.h"
#include "se_diskann_algo.h"
#include "se_diskann_algo_robust_prune.h"
#include "se_diskann_algo_search.h"
#include "se_diskann_utils.h"

#define SLAVE_DIS_THRESHOLD (1e-9)

static Status GetCloesetNb(
    DiskAnnCtxT *adpt, NeighborMonotonicQueueT *nmq, DiskAnnAddrT vertexId, NeighborT **closestNb)
{
    NeighborT *res = NULL;
    for (uint32_t i = 0; i < NmqGetSize(nmq); i++) {
        if (nmq->data[i].distance > SLAVE_DIS_THRESHOLD) {
            break;
        }

        if ((!VertextIdEquals(&vertexId, &nmq->data[i].id))) {
            bool isMaster = false;
            Status ret = DiskAnnIsMaster(adpt, nmq->data[i].id, &isMaster);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "|SE-DISKANN| |GetCloesetNb| Unable to get valid master");
                return ret;
            }
            if (isMaster) {
                res = &(nmq->data[i]);
                break;
            }
        }
    }
    *closestNb = res;
    return GMERR_OK;
}

static Status FindClosetVertexAndHandleDuplicateVertex(
    DiskAnnCtxT *adpt, NeighborMonotonicQueueT *nmq, const DiskAnnAddrT vertexId, bool *addedAsSlave)
{
    NeighborT *closestNb = NULL;
    Status ret = GetCloesetNb(adpt, nmq, vertexId, &closestNb);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |FindClosetVertexAndHandleDuplicateVertex| Unable to GetCloesetNb");
        return ret;
    }
    if (closestNb != NULL) {
        /* try to insert it as a slave */
        ret = DiskAnnAddSlaveNode(adpt, closestNb->id, vertexId, addedAsSlave);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |FindClosetVertexAndHandleDuplicateVertex| Unable to add slave node");
            return ret;
        }
    }
    return GMERR_OK;
}

Status RemoveFromCandidatesPool(const DiskAnnAddrT vertexId, VectorArrayT *pool)
{
    DB_POINTER(pool);
    for (uint32_t i = 0; i < VectorArrayGetLength(pool); i++) {
        if (IsVertexEqual(vertexId, *(DiskAnnAddrT *)(GetArrVal(pool, i)))) {
            NeighborT invalidNeighbor = {.id = SE_INVALID_VERTEX_ID, .distance = -1, .expanded = true};
            Status ret = VectorArraySet(pool, i, (const void *)&invalidNeighbor);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "|SE-DISKANN| |RemoveFromCandidatesPool| Unable to VectorArraySet");
                return ret;
            }
        }
    }
    return GMERR_OK;
}

static Status TryGetEdgeDist(DiskAnnCtxT *adpt, DiskAnnAddrT centerId, DiskAnnEdgeT *edge)
{
    DB_POINTER2(adpt, edge);
    if (adpt->meta.reserveDist || adpt->meta.acceleratePrune) {
        return GMERR_OK;
    }
    if (edge->distance != NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "|SE-DISKANN| distance should be NULL in not reserve dist scene");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    Status ret =
        SeAnnAllocAndInitItem(adpt->memCtx, adpt->meta.outDegree * sizeof(float), 0, (uint8_t **)&(edge->distance));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |InsertToNbsEdge| Unable to DiskAnnVertexGet");
        return ret;
    }
    DiskAnnVertexT *centerVertex = NULL;
    ret = DiskAnnVertexGet(adpt, centerId, true, &centerVertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |InsertToNbsEdge| Unable to DiskAnnVertexGet");
        SeAnnFreeItem(adpt->memCtx, (uint8_t **)&(edge->distance));
        return ret;
    }
    AnnVecT vector = {.type = adpt->meta.dataType, .dim = adpt->meta.dim, .items = centerVertex->vecs};
    for (uint32_t i = 0; i < edge->numNeighbor; i++) {
        double dist = 0.0;
        ret = GetAccuDistFromVec(adpt, edge->nexts[i], &vector, centerVertex->sqrSum, &dist);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |NeedRemoveFarthestNb| Unable to GetAccuDistFromVec");
            SeAnnFreeItem(adpt->memCtx, (uint8_t **)&(edge->distance));
            DiskAnnVertexFree(adpt, &centerVertex);
            return ret;
        }
        edge->distance[i] = (float)(dist);
    }
    DiskAnnVertexFree(adpt, &centerVertex);
    return GMERR_OK;
}

static Status VertexTryAddOneNb(
    DiskAnnCtxT *adpt, const DiskAnnAddrT centerId, DiskAnnVertexT *newNb, const double distToNewNb)
{
    DB_POINTER(newNb);
    // 获取中心节点的边信息
    DiskAnnEdgeT *edge = NULL;
    Status ret = DiskAnnEdgeGet(adpt, centerId, &edge);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |VertexTryAddOneNb| Unable to DiskAnnEdgeGet");
        return ret;
    }
    ret = TryGetEdgeDist(adpt, centerId, edge);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |VertexTryAddOneNb| Unable to TryGetEdgeDist");
        DiskAnnEdgeFree(adpt, &edge);
        return ret;
    }
    // 鲁棒剪枝，寻找需要替换的节点
    bool needRenewEdge = false;
    ret = VertexPruneLastNb(adpt, edge, newNb, distToNewNb, &needRenewEdge);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |VertexTryAddOneNb| Unable to VertexPruneLastNb");
        DiskAnnEdgeFree(adpt, &edge);
        return ret;
    }
    // 更新边的信息
    ret = DiskAnnEdgeSet(adpt, centerId, edge);
    DiskAnnEdgeFree(adpt, &edge);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |VertexTryAddOneNb| Unable to DiskAnnEdgeSet");
    }
    return ret;
}

static Status InsertToNbsEdge(DiskAnnCtxT *adpt, const DiskAnnAddrT newVertexId, VectorArrayT *prunedLists)
{
    DB_POINTER2(adpt, prunedLists);
    DiskAnnVertexT *newVertex = NULL;
    Status ret = DiskAnnVertexGet(adpt, newVertexId, true, &newVertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |InsertToNbsEdge| Unable to DiskAnnVertexGet");
        return ret;
    }
    for (uint32_t i = 0; i < VectorArrayGetLength(prunedLists); i++) {
        NeighborT *nb = (NeighborT *)(GetArrVal(prunedLists, i));
        if (nb == NULL) {
            ret = GMERR_UNEXPECTED_NULL_VALUE;
            DB_LOG_ERROR(ret, "|SE-DISKANN| nb should not be null while saturate insert to nbs edge");
            goto INSERT_TO_NB_EXIT;
        }
        if (VertextIdEquals(&nb->id, &newVertexId)) {
            continue;
        }
        double dist = 0.0;
        AnnVecT vecs = {.type = adpt->meta.dataType, .dim = adpt->meta.dim, .items = newVertex->vecs};
        ret = GetAccuDistFromVec(adpt, nb->id, &vecs, newVertex->sqrSum, &dist);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |InsertToNbsEdge| Unable to GetAccuDistFromVec");
            goto INSERT_TO_NB_EXIT;
        }
        ret = VertexTryAddOneNb(adpt, nb->id, newVertex, dist);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |InsertToNbsEdge| Unable to VertexTryAddOneNb");
            goto INSERT_TO_NB_EXIT;
        }
    }
INSERT_TO_NB_EXIT:
    DiskAnnVertexFree(adpt, &newVertex);
    return ret;
}

static Status GetPrunedList(DiskAnnCtxT *adpt, VisitedFilterT *vertexFilter, DiskAnnAddrT vertexId, void *insertVector,
    VectorArrayT **toPrunePool)
{
    AnnVecT vec = {.type = adpt->meta.dataType, .dim = adpt->meta.dim, .items = insertVector};
    VecIndexTaskTypeE flag = vertexFilter == NULL ? INSERT_TASK : BUILD_TASK;
    DiskAnnSearchParamsT searchParams = {0};
    searchParams.queryVector = &vec;
    GreedySearchCtxT context = {0};
    Status ret = ConstructGreedySearchContext(adpt, vertexId, &searchParams, flag, &context);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| Unable construct greedy search context while inner insert");
        return ret;
    }
    VectorArrayT *tmpPoolToPrune = NULL;
    NeighborMonotonicQueueT *candidatesQueue = NULL;
    ret = IterateToFixedPoint(&context, vertexFilter, &candidatesQueue, &tmpPoolToPrune);
    DestroyGreedySearchContext(&context);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnAlgoInsertInner| Unable to IterateToFixedPoint");
        return ret;
    }
    // 从优先队列中取出最近的节点，并处理重复的vector
    bool addedAsSlave = false;
    ret = FindClosetVertexAndHandleDuplicateVertex(adpt, candidatesQueue, vertexId, &addedAsSlave);
    NmqFree(adpt->memCtx, &candidatesQueue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnAlgoInsertInner| Unable to FindCloset");
        VectorArrayFree(adpt->memCtx, tmpPoolToPrune);
        return ret;
    }
    if (addedAsSlave) {
        VectorArrayFree(adpt->memCtx, tmpPoolToPrune);
        return DiskAnnDelRateUpdate(adpt, 0, 1);  // 删除0条，插入1条
    }
    // 在VertexPruneNeighbors逻辑中会略去相同VertexId的节点。
    ret = RemoveFromCandidatesPool(vertexId, tmpPoolToPrune);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnAlgoInsertInner| Unable to RemoveFromCandidatesPool");
        VectorArrayFree(adpt->memCtx, tmpPoolToPrune);
        return ret;
    }
    *toPrunePool = tmpPoolToPrune;
    return GMERR_OK;
}

Status DiskAnnAlgoInsertInner(
    DiskAnnCtxT *adpt, VisitedFilterT *vertexFilter, const DiskAnnAddrT vertexId, void *insertVector)
{
    VectorArrayT *poolToPrune = NULL;
    Status ret = GetPrunedList(adpt, vertexFilter, vertexId, insertVector, &poolToPrune);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnAlgoInsertInner| Unable to VertexPruneNeighbors");
        return ret;
    }
    if (poolToPrune == NULL) {
        // poolToPrune == NULL说明是重复节点
        return GMERR_OK;
    }
    // 鲁棒剪枝
    VectorArrayT *prunedLists = NULL;
    ret = VertexPruneNeighbors(adpt, vertexId, poolToPrune, &prunedLists);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnAlgoInsertInner| Unable to VertexPruneNeighbors");
        goto INSERT_EXIT;
    }
    // 将vertexId作为新的节点，尝试插入vertexId的邻居的邻居节点列表中
    ret = InsertToNbsEdge(adpt, vertexId, prunedLists);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnAlgoInsertInner| Unable to InsertToNbsEdge");
        goto INSERT_EXIT;
    }
    ret = DiskAnnDelRateUpdate(adpt, 0, 1);  // 删除0条，插入1条
INSERT_EXIT:
    // 释放资源
    VectorArrayFree(adpt->memCtx, poolToPrune);
    VectorArrayFree(adpt->memCtx, prunedLists);
    return ret;
}

Status DiskAnnReInsertNode(DiskAnnCtxT *adpt, DiskAnnAddrT vertexId)
{
    DB_POINTER(adpt);
    Status ret = DiskAnnNodeReset(adpt, vertexId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnReInsertNode| Unable to DiskAnnNodeReset");
        return ret;
    }
    void *vecs = NULL;
    ret = VectorGet(adpt, vertexId, &vecs);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnReInsertNode| Unable to VectorGet");
        return ret;
    }
    return DiskAnnAlgoInsertInner(adpt, NULL, vertexId, vecs);
}
