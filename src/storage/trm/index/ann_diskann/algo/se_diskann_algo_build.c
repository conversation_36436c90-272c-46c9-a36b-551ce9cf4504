/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author: wanyi
 * Create: 2024-02-08
 */

#include "se_diskann_algo_build.h"

#include "se_ann_utils.h"
#include "se_diskann_algo_insert.h"
#include "se_diskann_algo_robust_prune.h"
#include "se_diskann_utils.h"

#define INVALID_NB_NUMS 0xFFFFFFFF

static uint32_t FullConnVertexes(
    const DiskAnnAddrT *vertexArr, const uint32_t idxInVertexArr, const uint32_t vertexArrSize, DiskAnnAddrT *nbs)
{
    uint32_t numNbs = 0u;
    for (uint32_t i = 0u; i < vertexArrSize; i++) {
        if (i == idxInVertexArr) {
            continue;
        }
        nbs[numNbs] = vertexArr[i];
        numNbs++;
    }
    return numNbs;
}

static bool IsInArray(const uint32_t target, const uint32_t *array, const uint32_t arrSize)
{
    for (uint32_t i = 0; i < arrSize; i++) {
        if (target == array[i]) {
            return true;
        }
    }
    return false;
}

// 生成[0, threshold - 1]范围内的随机整数,并确保返回的随机数不等于exception
static uint32_t GenRandIdxExcept(const uint32_t *exceptions, const uint32_t exceptionSize, const uint32_t threshold)
{
    uint32_t randNum = 0;
    do {
        randNum = (uint32_t)rand() % threshold;
    } while (IsInArray(randNum, exceptions, exceptionSize));
    return randNum;
}

static uint32_t RandConnVertexes(DiskAnnCtxT *adpt, const DiskAnnAddrT *vertexArr, const uint32_t idxInVertexArr,
    const uint32_t vertexArrSize, DiskAnnAddrT *nbs)
{
    uint32_t threshold = vertexArrSize;
    uint32_t *nbIdxArr = NULL;
    Status ret =
        SeAnnAllocAndInitItem(adpt->memCtx, (adpt->meta.outDegree + 1) * sizeof(uint32_t), 0, (uint8_t **)&nbIdxArr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |RandConnVertexes| Unable to SeAnnAllocAndInitItem");
        return INVALID_NB_NUMS;
    }
    nbIdxArr[0] = idxInVertexArr;
    for (uint16_t i = 0; i < adpt->meta.outDegree; i++) {
        nbIdxArr[i + 1] = GenRandIdxExcept(nbIdxArr, i + 1, threshold);
        nbs[i] = vertexArr[nbIdxArr[i + 1]];
    }
    SeAnnFreeItem(adpt->memCtx, (uint8_t **)&nbIdxArr);
    return adpt->meta.outDegree;
}

static uint32_t ConnVertexes(DiskAnnCtxT *adpt, const DiskAnnAddrT *vertexArr, const uint32_t idxInVertexArr,
    const uint32_t vertexArrSize, DiskAnnAddrT *nbs)
{
    return vertexArrSize < (uint32_t)(adpt->meta.outDegree + 1) ?
               FullConnVertexes(vertexArr, idxInVertexArr, vertexArrSize, nbs) :
               RandConnVertexes(adpt, vertexArr, idxInVertexArr, vertexArrSize, nbs);
}

static Status PutNbsToCandidatesPool(
    DiskAnnCtxT *adpt, DiskAnnAddrT vertexId, const DiskAnnAddrT *nbs, uint32_t numNbs, VectorArrayT **pool)
{
    DB_POINTER3(adpt, nbs, pool);
    VectorArrayT *tmpPool = NULL;
    Status ret = VectorArrayInit(adpt->memCtx, adpt->meta.outDegree, sizeof(NeighborT), &tmpPool);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |PutNbsToCandidatesPool| Unable to GetDistBetweenVertexs");
        return ret;
    }
    for (uint32_t i = 0; i < numNbs; i++) {
        if (IsVertexEqual(nbs[i], vertexId)) {
            DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE,
                "|SE-DISKANN| |PutNbsToCandidatesPool| A vertex's neighbor should not be itself");
            VectorArrayFree(adpt->memCtx, tmpPool);
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        NeighborT nb = {0};
        nb.id = nbs[i];
        ret = GetDistBetweenVertexs(adpt, vertexId, nbs[i], &nb.distance);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |PutNbsToCandidatesPool| Unable to GetDistBetweenVertexs");
            VectorArrayFree(adpt->memCtx, tmpPool);
            return ret;
        }
        ret = VectorArrayAppend(adpt->memCtx, &nb, &tmpPool);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |PutNbsToCandidatesPool| Unable to VectorArrayAppend");
            VectorArrayFree(adpt->memCtx, tmpPool);
            return ret;
        }
    }
    *pool = tmpPool;
    return GMERR_OK;
}

static Status GenNbsForVertex(DiskAnnCtxT *adpt, DiskAnnAddrT vertexId, uint32_t idxInVertexArr,
    const DiskAnnAddrT *vertexArr, const uint32_t vertexArrSize)
{
    // 将集合里的所有点互相连接到出度数量
    DiskAnnAddrT *nbs = NULL;
    Status ret = SeAnnAllocAndInitItem(adpt->memCtx, adpt->meta.outDegree * sizeof(DiskAnnAddrT), 0, (uint8_t **)&nbs);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |GenNbsForVertex| SeAnnAllocAndInitItem meets unexpected things");
        return ret;
    }
    uint32_t numNbs = ConnVertexes(adpt, vertexArr, idxInVertexArr, vertexArrSize, nbs);
    if (numNbs == INVALID_NB_NUMS) {
        // 内部有打印日志
        SeAnnFreeItem(adpt->memCtx, (uint8_t **)&nbs);
        return GMERR_INTERNAL_ERROR;
    }
    // 把当前点的所有邻居节点加入候选池
    VectorArrayT *pool = NULL;
    ret = PutNbsToCandidatesPool(adpt, vertexId, nbs, numNbs, &pool);
    SeAnnFreeItem(adpt->memCtx, (uint8_t **)&nbs);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |GenNbsForVertex| PutNbsToCandidatesPool meets unexpected things");
        return ret;
    }
    // 剪枝
    VectorArrayT *prunedLists = NULL;
    ret = VertexPruneNeighbors(adpt, vertexId, pool, &prunedLists);
    VectorArrayFree(adpt->memCtx, pool);
    // 注意！prunedLists只是作为一个占位的入参，是为了复用VertexPruneNeighbors
    VectorArrayFree(adpt->memCtx, prunedLists);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |GenNbsForVertex| VertexPruneNeighbors meets unexpected things");
    }
    SeAnnFreeItem(adpt->memCtx, (uint8_t **)&nbs);
    return ret;
}

// RegularGraph指的是随机连接或全连接的图
Status BuildRandGraph(DiskAnnCtxT *adpt, const DiskAnnAddrT *vertexIds, const uint32_t vertexNum)
{
    for (uint32_t i = 0; i < vertexNum; i++) {
        // 注意！vertexIds是所有节点的数组。在开启了并行构建后,
        // vertexNum不再是所有节点数组的大小,而是该线程负责构建的节点的数量
        Status ret = GenNbsForVertex(adpt, vertexIds[i], i, vertexIds, vertexNum);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |BuildRandGraph| GenNbsForVertex meets unexpected things");
            return ret;
        }
    }
    return GMERR_OK;
}

Status BuildMainGraph(
    DiskAnnCtxT *adpt, const DiskAnnAddrT *vertexIds, VectorArrayT *vectorArr, const uint32_t numsToLoad)
{
    DB_POINTER2(adpt, vertexIds);
    VisitedFilterT *vertexFilter = NULL;
    Status ret = VisitedFilterInit(adpt->memCtx, &vertexFilter);  // init vertexFilter
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnIndexOpen| Unable to VisitedFilterInit");
        return ret;
    }
    for (uint32_t i = 0; i < numsToLoad; i++) {
        ret = DiskAnnAlgoInsertInner(adpt, vertexFilter, vertexIds[i], GetArrVal(vectorArr, i));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |BuildMainGraph| Unable to DiskAnnAlgoInsert vertex: idx |%d|", i);
            return ret;
        }
    }
    VisitedFilterRelease(&vertexFilter, adpt->memCtx);
    return GMERR_OK;
}

Status GenFloatFrozenPoints(DiskAnnCtxT *adpt, DiskAnnPageIdT metaPageId, float *ref)
{
    DB_POINTER(adpt);
    void *randFrozenVectors = NULL;
    Status ret = CalFloatFrozensByRef(adpt, ref, &randFrozenVectors);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |GenFrozenPoints| Unable to CalFloatFrozensByRef");
        return ret;
    }
    for (uint32_t i = 0; i < adpt->meta.numFrozens; i++) {
        DiskAnnAddrT vertexId = SE_INVALID_VERTEX_ID;
        ret = DiskAnnLoadData(adpt, 0, randFrozenVectors + i * adpt->meta.rawVecDim, &vertexId);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |GenFrozenPoints| Unable to DiskAnnLoadData");
            SeAnnFreeItem(adpt->memCtx, (uint8_t **)&randFrozenVectors);
            return ret;
        }
        adpt->meta.frozens[i] = vertexId;  // frozens的空间在Open时已经开辟
    }
    SeAnnFreeItem(adpt->memCtx, (uint8_t **)&randFrozenVectors);
    ret = DiskAnnSetFrozens(adpt, metaPageId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "|SE-DISKANN| |GenFrozenPoints| Unable to DiskAnnSetFrozens");
    }
    return ret;
}

Status GenFloat16FrozenPoints(DiskAnnCtxT *adpt, DiskAnnPageIdT metaPageId, float16 *ref)
{
    DB_POINTER(adpt);
    void *randFrozenVectors = NULL;
    Status ret = CalFloat16FrozensByRef(adpt, ref, &randFrozenVectors);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |GenFrozenPoints| Unable to CalFloat16FrozensByRef");
        return ret;
    }
    for (uint32_t i = 0; i < adpt->meta.numFrozens; i++) {
        DiskAnnAddrT vertexId = SE_INVALID_VERTEX_ID;
        ret = DiskAnnLoadData(adpt, 0, randFrozenVectors + i * adpt->meta.rawVecDim, &vertexId);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |GenFrozenPoints| Unable to DiskAnnLoadData");
            SeAnnFreeItem(adpt->memCtx, (uint8_t **)&randFrozenVectors);
            return ret;
        }
        adpt->meta.frozens[i] = vertexId;  // frozens的空间在Open时已经开辟
    }
    SeAnnFreeItem(adpt->memCtx, (uint8_t **)&randFrozenVectors);
    ret = DiskAnnSetFrozens(adpt, metaPageId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "|SE-DISKANN| |GenFrozenPoints| Unable to DiskAnnSetFrozens");
    }
    return ret;
}
