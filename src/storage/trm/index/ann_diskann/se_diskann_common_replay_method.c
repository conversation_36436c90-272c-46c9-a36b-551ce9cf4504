/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: implementation of diskann replay functions
 * Author: wanyi
 * Create: 2024-02-18
 */

#include "se_diskann_common_replay_method.h"
#include "se_database.h"
#include "se_diskann_utils.h"

#define MASTERID_OFFSET (sizeof(uint32_t))
#define PRE_SLAVEID_OFFSET (MASTERID_OFFSET + sizeof(DiskAnnAddrT))
#define NEXT_SLAVEID_OFFSET (PRE_SLAVEID_OFFSET + sizeof(DiskAnnAddrT))

StatusInter DiskAnnIndexSetEdgeReplay(ReplayArgsT *arg)
{
    DB_UNUSED(arg);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

StatusInter DiskAnnIndexSetNodeReplay(ReplayArgsT *arg)
{
    DB_UNUSED(arg);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

StatusInter DiskAnnIndexSetTagReplay(ReplayArgsT *arg)
{
    DB_UNUSED(arg);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

// LCOV_EXCL_BR_START
StatusInter DiskAnnIndexInitMetaReplay(ReplayArgsT *arg)
{
    DB_UNUSED(arg);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

StatusInter DiskAnnIndexMetaAppendReplay(ReplayArgsT *arg)
{
    DB_UNUSED(arg);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

StatusInter DiskAnnIndexResetMetaReplay(ReplayArgsT *arg)
{
    DB_UNUSED(arg);
    return GMERR_FEATURE_NOT_SUPPORTED;
}
// LCOV_EXCL_BR_STOP

StatusInter DiskAnnIndexSetNewSlaveReplay(ReplayArgsT *arg)
{
    DB_UNUSED(arg);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

StatusInter DiskAnnIndexSetNextSlaveIdReplay(ReplayArgsT *arg)
{
    DB_UNUSED(arg);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

StatusInter DiskAnnIndexSetPreSlaveReplay(ReplayArgsT *arg)
{
    DB_UNUSED(arg);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

// LCOV_EXCL_BR_START
StatusInter DiskAnnIndexRemoveFromNbsReplay(ReplayArgsT *arg)
{
    DB_UNUSED(arg);
    return GMERR_FEATURE_NOT_SUPPORTED;
}
// LCOV_EXCL_BR_STOP

StatusInter DiskAnnIndexSetFrozensReplay(ReplayArgsT *arg)
{
    DB_UNUSED(arg);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

// LCOV_EXCL_BR_START
StatusInter DiskAnnIndexSetPageHeadReplay(ReplayArgsT *arg)
{
    DB_UNUSED(arg);
    return GMERR_FEATURE_NOT_SUPPORTED;
}
// LCOV_EXCL_BR_STOP

StatusInter DiskAnnIndexSetDiskAnnNodeHdrReplay(ReplayArgsT *arg)
{
    DB_UNUSED(arg);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

StatusInter DiskAnnIndexSetMetaReplay(ReplayArgsT *arg)
{
    DB_UNUSED(arg);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

StatusInter DiskAnnIndexResetNodeReplay(ReplayArgsT *arg)
{
    DB_UNUSED(arg);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

StatusInter DiskAnnIndexAddToNodeListReplay(ReplayArgsT *arg)
{
    DB_UNUSED(arg);
    return GMERR_FEATURE_NOT_SUPPORTED;
}
