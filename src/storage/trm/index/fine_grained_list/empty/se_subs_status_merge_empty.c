/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: empty impl for driver statecollapse subscribe and notify APIs.
 * Author:
 * Create: 2024-8-22
 */

#include "se_subs_status_merge.h"
#include "db_se_heap_serialization.h"
#include "se_define.h"

inline bool IsStatusMergeSubsCursorNode(const StatusMergeNodeBaseT *node)
{
    return false;
}

DbMemCtxT *GetSubsStmgListShmCtx(void)
{
    return NULL;
}

Status CreateStatusMergeSubDataNode4DirectWrite(ShmemPtrT *nodePtr)
{
    return GMERR_OK;
}

Status CreateStatusMergeSubCursorNode(ShmemPtrT *nodePtr)
{
    return GMERR_OK;
}

Status CreateStatusMergeSubDataNode(ShmemPtrT *nodePtr)
{
    return GMERR_OK;
}

void LogStatusMergeBaseNode(const StatusMergeNodeBaseT *node, const char *prefix, Status ret)
{
    return;
}

Status BatchCreateStatusMergeSubDataNode(ShmemPtrT *ptrArray, uint32_t batchNum)
{
    return GMERR_OK;
}

inline void ReleaseStatusMergeSubNode(ShmemPtrT nodePtr)
{
    return;
}

Status CreateStatusMergeSub(ShmemPtrT *listPtr)
{
    return GMERR_OK;
}

void ReleaseStatusMergeSub(ShmemPtrT listPtr)
{
    return;
}

inline void DbServerInsertRow(ShmemPtrT subMergeList, ShmemPtrT newPtr)
{
    return;
}

void DbServerUpdateRow(ShmemPtrT subMergeList, ShmemPtrT updatePtr)
{
    return;
}

inline Status DbServerCreateIncSub(ShmemPtrT subMergeList, ShmemPtrT subCursor)
{
    return GMERR_OK;
}

inline Status DbServerCreateFullSub(ShmemPtrT subMergeList, ShmemPtrT subCursor)
{
    return GMERR_OK;
}

inline Status DbServerDeleteSub(ShmemPtrT subMergeList, ShmemPtrT subCursor, bool isZombie)
{
    return GMERR_OK;
}

Status DbServerZombieRetire(ShmemPtrT subMergeList, ShmemPtrT subCursor)
{
    return GMERR_OK;
}

inline Status DbCltReadOne(
    StatusMergeListT *stmgList, ShmemPtrT subCursor, TupleAddr *tupleAddr, uint32_t *manipuShmMark)
{
    return GMERR_OK;
}

inline bool DbServerNeedGcCheck(ShmemPtrT subMergeList, ShmemPtrT *nxtPtr)
{
    return false;
}

inline Status DbServerDeleteDataNode(ShmemPtrT subMergeList, ShmemPtrT nodePtr)
{
    return GMERR_OK;
}

inline Status DbGcAdvanceOnePos(ShmemPtrT subMergeList, ShmemPtrT gcCursor)
{
    return GMERR_OK;
}

StmgNodeTypeE DbServerGetPrevNodeType(ShmemPtrT subMergeList, ShmemPtrT subCursor)
{
    return NODE_TYPE_BUTT;
}

StmgNodeTypeE DbServerGetNextNodeType(ShmemPtrT subMergeList, ShmemPtrT subCursor)
{
    return NODE_TYPE_BUTT;
}

Status DbServerClearListStep(ShmemPtrT subMergeList)
{
    return GMERR_OK;
}

inline void DbServerClearRetire(ShmemPtrT subMergeList)
{
    return;
}

inline void DbServerDebugerRemoveAllNodeToRetire(ShmemPtrT subMergeList)
{
    return;
}

inline void DbServerDebugerClearRetire(ShmemPtrT subMergeList)
{
    return;
}

Status DbServerGetDataStats(ShmemPtrT subMergeListPtr, ShmemPtrT subCursor, uint64_t *beforeCnt, uint64_t *afterCnt)
{
    return GMERR_OK;
}

Status SeCheckNodeAndModifyTuple(StatusMergeDefragRunCtx *statusMergeCtx, bool needChangeTuple, bool *canDefrage)
{
    return GMERR_OK;
}

Status DbMarkUnusedSubCursor(ShmemPtrT nodePtr)
{
    return GMERR_OK;
}

Status DbRecoverUnusedSubCursor(ShmemPtrT nodePtr)
{
    return GMERR_OK;
}

bool DbCheckSubCursorUnused(ShmemPtrT nodePtr)
{
    return false;
}
