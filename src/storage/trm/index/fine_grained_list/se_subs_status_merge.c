/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: header file for driver statecollapse subscribe and notify APIs.
 * Author: qing<PERSON>hu bob dylen
 * Create: 2022/11/21
 * Notes: NA
 */

#include "se_subs_status_merge.h"
#include "se_subs_fine_grained_list.h"
#include "se_hazard_pointer.h"
#include "db_se_heap_serialization.h"
#include "se_define.h"
#include "se_hash_common.h"

#define STMG_CURSOR_CHECK_RETRY_TIME 200
#define STMG_CURSOR_CHECK_RETRY_INTERVAL (10 * USECONDS_IN_MSECOND)

StmgNodeTypeE GetStmgNodeType(
    const StatusMergeListT *statusMergeList, ShmemPtrT nodePtr, const StatusMergeNodeBaseT *node)
{
    StmgNodeTypeE type = SUB;
    if (IsShmemPtrEqual(nodePtr, statusMergeList->headDummy)) {
        type = HEAD;
    } else if (IsShmemPtrEqual(nodePtr, statusMergeList->tailDummy)) {
        type = TAIL;
    } else if (IsShmemPtrEqual(nodePtr, statusMergeList->retireHeadDummy)) {
        type = RH;
    } else if (IsShmemPtrEqual(nodePtr, statusMergeList->retireTailDummy)) {
        type = RT;
    } else if (IsShmemPtrEqual(nodePtr, statusMergeList->garbageCollector)) {
        type = GC;
    } else if (node->tuple != 0) {
        type = DATA;
    }
    return type;
}

inline bool IsStatusMergeSubsCursorNode(const StatusMergeNodeBaseT *node)
{
    return node->tuple == HEAP_INVALID_ADDR;
}

// 注意线程安全
DbMemCtxT *GetSubsStmgListShmCtx(void)
{
    static DbMemCtxT *stMgPubSubShmMemCtx = NULL;
    if (stMgPubSubShmMemCtx != NULL) {
        return stMgPubSubShmMemCtx;
    }
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(DbGetProcGlobalId());
    if (SECUREC_UNLIKELY(seInstance == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| se instance is novalid");
        return NULL;
    }

    stMgPubSubShmMemCtx = DbGetShmemCtxById(seInstance->stMgPubSubShmMemCtxId, DbGetProcGlobalId());
    if (SECUREC_UNLIKELY(stMgPubSubShmMemCtx == NULL)) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| pubsub shm ctx is novalid");
    }
    return stMgPubSubShmMemCtx;
}

inline static void InitStatusMergeSubNodeBase(StatusMergeNodeBaseT *node)
{
    node->prev = DB_INVALID_UINT64;
    node->next = DB_INVALID_UINT64;
    node->tuple = DB_INVALID_UINT64;
    node->nodeStatus = NODE_LIVE;
    node->isWaiting = false;
    DbSpinInit(&node->spinLock);
}

DbMemCtxT *GetSubsStmgListShmCtx4DirectWrite(void)
{
    DbMemCtxT *stMgPubSubShmMemCtx = NULL;
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(DbGetProcGlobalId());
    if (SECUREC_UNLIKELY(seInstance == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| se instance is novalid in direct write.");
        return NULL;
    }

    stMgPubSubShmMemCtx = DbGetShmemCtxById(seInstance->stMgPubSubShmMemCtxId, DbGetProcGlobalId());
    if (SECUREC_UNLIKELY(stMgPubSubShmMemCtx == NULL)) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| pubsub shm ctx is novalid in direct write.");
    }
    return stMgPubSubShmMemCtx;
}

Status CreateStatusMergeSubDataNode4DirectWrite(ShmemPtrT *nodePtr)
{
    DbMemCtxT *shmCtx = GetSubsStmgListShmCtx4DirectWrite();
    if (SECUREC_UNLIKELY(shmCtx == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| se instance is novalid in direct write.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *nodePtr = DbShmemCtxAlloc(shmCtx, sizeof(StatusMergeDataNodeT));
    StatusMergeDataNodeT *node = DbShmPtrToAddr(*nodePtr);
    if (SECUREC_UNLIKELY(node == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| nodePtr is novalid in direct write.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    InitStatusMergeSubNodeBase(&node->baseNode);
    return GMERR_OK;
}

Status CreateStatusMergeSubCursorNode(ShmemPtrT *nodePtr)
{
    DbMemCtxT *shmCtx = GetSubsStmgListShmCtx();
    if (SECUREC_UNLIKELY(shmCtx == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| se instance is novalid");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *nodePtr = DbShmemCtxAlloc(shmCtx, sizeof(StatusMergeCursorNodeT));
    if (IsShmemPtrEqual(*nodePtr, DB_INVALID_SHMPTR)) {
        return GMERR_OUT_OF_MEMORY;
    }
    StatusMergeCursorNodeT *node = GetCursorNodeAddr(*nodePtr);
    if (SECUREC_UNLIKELY(node == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| nodePtr is novalid");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    InitStatusMergeSubNodeBase(&node->baseNode);
    node->baseNode.tuple = HEAP_INVALID_ADDR;
    DbSpinInit(&node->lock);
    return GMERR_OK;
}

Status CreateStatusMergeSubDataNode(ShmemPtrT *nodePtr)
{
    DbMemCtxT *shmCtx = GetSubsStmgListShmCtx();
    if (SECUREC_UNLIKELY(shmCtx == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| se instance is novalid");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *nodePtr = DbShmemCtxAlloc(shmCtx, sizeof(StatusMergeDataNodeT));
    StatusMergeDataNodeT *node = DbShmPtrToAddr(*nodePtr);
    if (SECUREC_UNLIKELY(node == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| nodePtr is novalid");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    InitStatusMergeSubNodeBase(&node->baseNode);
    return GMERR_OK;
}

// 日志打印则为问题，且会导致故障扩散！！！已知缩容会触发，后续修改
void LogStatusMergeBaseNode(const StatusMergeNodeBaseT *node, const char *prefix, Status ret)
{
    DB_LOG_ERROR(ret,
        "%s, prev:%" PRIu64 ", next:%" PRIu64 ", tuple:%" PRIu64 ", status:%" PRIu8 ", lock: %" PRIu32
        ", isWaiting: %" PRIu8 "",
        prefix, node->prev, node->next, node->tuple, node->nodeStatus, node->spinLock.lock, (uint8_t)node->isWaiting);
}

Status BatchCreateStatusMergeSubDataNode(ShmemPtrT *ptrArray, uint32_t batchNum)
{
#ifndef FEATURE_MINIKV
    DbMemCtxT *shmCtx = GetSubsStmgListShmCtx();
    if (SECUREC_UNLIKELY(shmCtx == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| se get stmg list ctx");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    Status ret = DbShmemCtxBatchAlloc(shmCtx, sizeof(StatusMergeDataNodeT), ptrArray, batchNum);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "|SE-PUBSUB| batch alloc stmg node");
        return ret;
    }
    for (uint32_t index = 0; index < batchNum; ++index) {
        StatusMergeDataNodeT *node = DbShmPtrToAddr(ptrArray[index]);
        // 任意一个转换失败，认为申请操作失败，no进行释放处理
        if (SECUREC_UNLIKELY(node == NULL)) {
            DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| nodePtr index: %" PRIu32 "", index);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        InitStatusMergeSubNodeBase(&node->baseNode);
    }
#endif
    return GMERR_OK;
}

inline void ReleaseStatusMergeSubNode(ShmemPtrT nodePtr)
{
    DbMemCtxT *shmCtx = GetSubsStmgListShmCtx();
    if (SECUREC_UNLIKELY(shmCtx == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| se instance is novalid");
        return;
    }
    DbShmemCtxFree(shmCtx, nodePtr);
}

inline static void InitList(ShmemPtrT headPtr, ShmemPtrT tailPtr)
{
    StatusMergeNodeBaseT *head = DbShmPtrToAddr(headPtr);
    if (head != NULL) {
        head->next = ShmemPtr2Uint64(tailPtr);
    }
    StatusMergeNodeBaseT *tail = DbShmPtrToAddr(tailPtr);
    if (tail != NULL) {
        tail->prev = ShmemPtr2Uint64(headPtr);
    }
}

Status AllocAllNode(StatusMergeListT *list)
{
    DB_POINTER(list);
    DbMemCtxT *shmCtx = GetSubsStmgListShmCtx();
    if (SECUREC_UNLIKELY(shmCtx == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| se instance is novalid");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    Status ret = CreateStatusMergeSubDataNode(&list->headDummy);
    if (ret != GMERR_OK) {
        goto EXIT1;
    }
    ret = CreateStatusMergeSubDataNode(&list->tailDummy);
    if (ret != GMERR_OK) {
        goto EXIT2;
    }
    ret = CreateStatusMergeSubDataNode(&list->retireHeadDummy);
    if (ret != GMERR_OK) {
        goto EXIT3;
    }
    ret = CreateStatusMergeSubDataNode(&list->retireTailDummy);
    if (ret != GMERR_OK) {
        goto EXIT4;
    }
    ret = CreateStatusMergeSubCursorNode(&list->garbageCollector);
    if (ret != GMERR_OK) {
        goto EXIT5;
    }
    return GMERR_OK;

EXIT5:
    DbShmemCtxFree(shmCtx, list->retireTailDummy);
EXIT4:
    DbShmemCtxFree(shmCtx, list->retireHeadDummy);
EXIT3:
    DbShmemCtxFree(shmCtx, list->tailDummy);
EXIT2:
    DbShmemCtxFree(shmCtx, list->headDummy);
EXIT1:
    DB_LOG_AND_SET_LASERR(ret, "|SE-PUBSUB| init linked list unsucc");
    return ret;
}

Status CreateStatusMergeSub(ShmemPtrT *listPtr)
{
    DbMemCtxT *shmCtx = GetSubsStmgListShmCtx();
    if (SECUREC_UNLIKELY(shmCtx == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| se instance is novalid");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *listPtr = DbShmemCtxAlloc(shmCtx, sizeof(StatusMergeListT));
    StatusMergeListT *list = (StatusMergeListT *)DbShmPtrToAddr(*listPtr);
    if (SECUREC_UNLIKELY(list == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| list addr is novalid");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    Status ret = AllocAllNode(list);
    if (ret != GMERR_OK) {
        goto EXIT1;
    }

    InitList(list->headDummy, list->tailDummy);
    FineGrainedListPushHead(list, list->garbageCollector);
    InitList(list->retireHeadDummy, list->retireTailDummy);

    ret = HpInitHpArray(shmCtx, &list->hpArrayPtr);
    if (ret != GMERR_OK) {
        goto EXIT2;
    }
    return GMERR_OK;

EXIT2:
    DbShmemCtxFree(shmCtx, list->headDummy);
    DbShmemCtxFree(shmCtx, list->tailDummy);
    DbShmemCtxFree(shmCtx, list->retireHeadDummy);
    DbShmemCtxFree(shmCtx, list->retireTailDummy);
    DbShmemCtxFree(shmCtx, list->garbageCollector);
EXIT1:
    DbShmemCtxFree(shmCtx, *listPtr);
    *listPtr = DB_INVALID_SHMPTR;
    return ret;
}

void ReleaseStatusMergeSub(ShmemPtrT listPtr)
{
    DbMemCtxT *shmCtx = GetSubsStmgListShmCtx();
    // 保证后台线程不清理
    if (SECUREC_UNLIKELY(shmCtx == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| se instance is novalid");
        return;
    }

    StatusMergeListT *list = DbShmPtrToAddr(listPtr);
    if (SECUREC_UNLIKELY(list == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| list addr is novalid");
        return;
    }

    DbShmemCtxFree(shmCtx, list->headDummy);
    DbShmemCtxFree(shmCtx, list->tailDummy);
    DbShmemCtxFree(shmCtx, list->retireHeadDummy);
    DbShmemCtxFree(shmCtx, list->retireTailDummy);
    DbShmemCtxFree(shmCtx, list->hpArrayPtr);
    DbShmemCtxFree(shmCtx, listPtr);
}

inline void DbServerInsertRow(ShmemPtrT subMergeList, ShmemPtrT newPtr)
{
    StatusMergeListT *statusMergeList = DbShmPtrToAddr(subMergeList);
    if (SECUREC_UNLIKELY(statusMergeList == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "Can not convert Address To Arm 32 Address Addr SegId %" PRIu32 " offset %" PRIu32 ".", subMergeList.segId,
            subMergeList.offset);
        return;
    }

    FineGrainedListPushTail(statusMergeList, newPtr);
}

void DbServerUpdateRow(ShmemPtrT subMergeList, ShmemPtrT updatePtr)
{
    StatusMergeListT *statusMergeList = DbShmPtrToAddr(subMergeList);
    if (SECUREC_UNLIKELY(statusMergeList == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "Can not convert Address To Arm 32 Address Addr SegId %" PRIu32 " offset %" PRIu32 ".", subMergeList.segId,
            subMergeList.offset);
        return;
    }

    Status ret = FineGrainedListRemoveFromList(statusMergeList, updatePtr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-PUBSUB|When Updata data node, but other thread remove it from list");
        return;
    }
#ifndef NDEBUG
    StatusMergeNodeBaseT *updateNode = (StatusMergeNodeBaseT *)DbShmPtrToAddr(updatePtr);
    if (updateNode != NULL) {
        DB_ASSERT(updateNode->nodeStatus == (uint8_t)NODE_LIVE);
    }
#endif
    FineGrainedListPushTail(statusMergeList, updatePtr);
}

inline Status DbServerCreateIncSub(ShmemPtrT subMergeList, ShmemPtrT subCursor)
{
    StatusMergeListT *statusMergeList = DbShmPtrToAddr(subMergeList);
    if (SECUREC_UNLIKELY(statusMergeList == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "Can not convert Address To Arm 32 Address Addr SegId %" PRIu32 " offset %" PRIu32 ".", subMergeList.segId,
            subMergeList.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    FineGrainedListPushTail(statusMergeList, subCursor);
    return GMERR_OK;
}

inline Status DbServerCreateFullSub(ShmemPtrT subMergeList, ShmemPtrT subCursor)
{
    StatusMergeListT *statusMergeList = DbShmPtrToAddr(subMergeList);
    if (SECUREC_UNLIKELY(statusMergeList == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "Can not convert Address To Arm 32 Address Addr SegId %" PRIu32 " offset %" PRIu32 ".", subMergeList.segId,
            subMergeList.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    return FineGrainedListAddFullSubs(statusMergeList, statusMergeList->garbageCollector, subCursor);
}

inline Status DbServerDeleteSub(ShmemPtrT subMergeList, ShmemPtrT subCursor, bool isZombie)
{
    StatusMergeListT *statusMergeList = DbShmPtrToAddr(subMergeList);
    if (SECUREC_UNLIKELY(statusMergeList == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "Can not convert Address To Arm 32 Address Addr SegId %" PRIu32 " offset %" PRIu32 ".", subMergeList.segId,
            subMergeList.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    return FineGrainedListDeleteFromList(statusMergeList, subCursor, isZombie);
}

Status DbServerZombieRetire(ShmemPtrT subMergeList, ShmemPtrT subCursor)
{
    StatusMergeListT *statusMergeList = DbShmPtrToAddr(subMergeList);
    if (SECUREC_UNLIKELY(statusMergeList == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "Can not convert Address To Arm 32 Address Addr SegId %" PRIu32 " offset %" PRIu32 ".", subMergeList.segId,
            subMergeList.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    StatusMergeNodeBaseT *node = GetSubsNodeAddr(subCursor);
    if (SECUREC_UNLIKELY(node == NULL)) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Trans shmptr to addr unsucc,segId:%" PRIu32 ", offset:%" PRIu32,
            subCursor.segId, subCursor.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if ((NodeStatusE)node->nodeStatus != NODE_REMOVED) {
        DB_LOG_ERROR(
            GMERR_INTERNAL_ERROR, "Should not retire none removed stmg node. node status:%d", node->nodeStatus);
        return GMERR_INTERNAL_ERROR;
    }

    FineGrainedListPushToRetireList(statusMergeList, subCursor);
    return GMERR_OK;
}

inline Status DbCltReadOne(
    StatusMergeListT *stmgList, ShmemPtrT subCursor, TupleAddr *tupleAddr, uint32_t *manipuShmMark)
{
    return FineGrainedListRemoveAdvanceOnePos(stmgList, subCursor, tupleAddr, true, manipuShmMark);
}

// gc
/**
 * @brief: 是否需要gc过程的检测函数，分为以下三种情况
 *         case 1: gcNode -> subNode(tailDummy) 遇到订阅节点或者尾节点，no do gc
 *         case 2: gcNode -> recNode -> subNode(tailDummy) 下一个数据节点后边紧跟订阅节点或者尾节点，no do gc
 *         case 3: gcNode -> recNode -> recNode -> ... 连续两个数据节点， need gc
 *         ！！！case 2调整为订阅节点非消费状态，do gc; 通过消费状态判断gc，避免gc消费并发问题
 *         ！！！case 3调整为gcNode->recNode->tailDummy同样gc
 * @param params [in]: gcPtr, gc节点的共享内存addr，用来获取之后的节点
 * @param params [out]: nxtPtr, gc节点之后一个节点的共享内存addr
 * @return: bool类型，表示当前任务是否需要gc
 */
inline bool DbServerNeedGcCheck(ShmemPtrT subMergeList, ShmemPtrT *nxtPtr)
{
    DB_POINTER(nxtPtr);
    StatusMergeListT *statusMergeList = DbShmPtrToAddr(subMergeList);
    if (SECUREC_UNLIKELY(statusMergeList == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "Can not convert Address To Arm 32 Address Addr SegId %" PRIu32 " offset %" PRIu32 ".", subMergeList.segId,
            subMergeList.offset);
        return false;
    }
    return FineGrainedListNeedGcCheck(statusMergeList, statusMergeList->garbageCollector, nxtPtr);
}

inline Status DbServerDeleteDataNode(ShmemPtrT subMergeList, ShmemPtrT nodePtr)
{
    StatusMergeListT *statusMergeList = DbShmPtrToAddr(subMergeList);
    if (SECUREC_UNLIKELY(statusMergeList == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "Can not convert Address To Arm 32 Address Addr SegId %" PRIu32 " offset %" PRIu32 ".", subMergeList.segId,
            subMergeList.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    return FineGrainedListDeleteFromList(statusMergeList, nodePtr, false);
}

inline Status DbGcAdvanceOnePos(ShmemPtrT subMergeList, ShmemPtrT gcCursor)
{
    StatusMergeListT *list = DbShmPtrToAddr(subMergeList);
    if (SECUREC_UNLIKELY(list == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "Can not convert Address To Arm 32 Address Addr SegId %" PRIu32 " offset %" PRIu32 ".", subMergeList.segId,
            subMergeList.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    TupleAddr addr = 0;
    return FineGrainedListRemoveAdvanceOnePos(list, gcCursor, &addr, false, NULL);
}

StmgNodeTypeE DbServerGetPrevNodeType(ShmemPtrT subMergeList, ShmemPtrT subCursor)
{
    // 调用处加了catalog锁保证此时list和cursor必然存在
    StatusMergeListT *list = DbShmPtrToAddr(subMergeList);
    if (SECUREC_UNLIKELY(list == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| Merge list addr is novalid");
        return NODE_TYPE_BUTT;
    }
    StatusMergeCursorNodeT *node = GetCursorNodeAddr(subCursor);
    if (SECUREC_UNLIKELY(node == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| Cursor node addr is novalid");
        return NODE_TYPE_BUTT;
    }

    // 加锁
    Status ret = FineGrainedListLockPrevAndNext(list, subCursor);
    if (ret != GMERR_OK) {
        StmgNodeTypeE invalid = NODE_TYPE_BUTT;
        return invalid;
    }

    ShmemPtrT prevNodePtr = Uint64ToShmemPtr(node->baseNode.prev);
    StatusMergeNodeBaseT *prevNode = GetSubsNodeAddr(prevNodePtr);
    if (SECUREC_UNLIKELY(prevNode == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| prevNode addr is novalid");
        FineGrainedListUnlockPrevAndNext(subCursor);
        return NODE_TYPE_BUTT;
    }
    StmgNodeTypeE prevType = GetStmgNodeType(list, prevNodePtr, prevNode);

    // 解锁
    FineGrainedListUnlockPrevAndNext(subCursor);

    return prevType;
}

StmgNodeTypeE DbServerGetNextNodeType(ShmemPtrT subMergeList, ShmemPtrT subCursor)
{
    // 调用处加了catalog锁保证此时list和cursor必然存在
    StatusMergeListT *list = DbShmPtrToAddr(subMergeList);
    if (SECUREC_UNLIKELY(list == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| list addr is novalid");
        return NODE_TYPE_BUTT;
    }
    StatusMergeCursorNodeT *node = GetCursorNodeAddr(subCursor);
    if (SECUREC_UNLIKELY(node == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| node addr is novalid");
        return NODE_TYPE_BUTT;
    }

    // 加锁
    Status ret = FineGrainedListLockPrevAndNext(list, subCursor);
    if (ret != GMERR_OK) {
        StmgNodeTypeE invalid = NODE_TYPE_BUTT;
        return invalid;
    }

    ShmemPtrT nextNodePtr = Uint64ToShmemPtr(node->baseNode.next);
    StatusMergeNodeBaseT *nextNode = GetSubsNodeAddr(nextNodePtr);
    if (SECUREC_UNLIKELY(nextNode == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| nextNode addr is novalid");
        FineGrainedListUnlockPrevAndNext(subCursor);
        return NODE_TYPE_BUTT;
    }
    StmgNodeTypeE nextType = GetStmgNodeType(list, nextNodePtr, nextNode);

    // 解锁
    FineGrainedListUnlockPrevAndNext(subCursor);

    return nextType;
}

Status DbServerClearListStep(ShmemPtrT subMergeList)
{
    StatusMergeListT *list = DbShmPtrToAddr(subMergeList);
    if (SECUREC_UNLIKELY(list == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "Can not convert Address To Arm 32 Address Addr SegId %" PRIu32 " offset %" PRIu32 ".", subMergeList.segId,
            subMergeList.offset);
        return GMERR_DATA_EXCEPTION;
    }
    DbMemCtxT *shmCtx = GetSubsStmgListShmCtx();
    if (SECUREC_UNLIKELY(shmCtx == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| stmg sub shm ctx is novalid");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    ShmemPtrT popedPtr = PopNodeNoLock(list);
    if (!DbIsShmPtrValid(popedPtr)) {
        // 清理retire list
        DbServerDebugerClearRetire(subMergeList);
        ReleaseStatusMergeSub(subMergeList);
        return GMERR_NO_DATA;
    }
    DbShmemCtxFree(shmCtx, popedPtr);
    return GMERR_OK;
}

inline void DbServerClearRetire(ShmemPtrT subMergeList)
{
    StatusMergeListT *list = DbShmPtrToAddr(subMergeList);
    if (SECUREC_UNLIKELY(list == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "Can not convert Address To Arm 32 Address Addr SegId %" PRIu32 " offset %" PRIu32 ".", subMergeList.segId,
            subMergeList.offset);
        return;
    }
    DbMemCtxT *shmCtx = GetSubsStmgListShmCtx();
    if (SECUREC_UNLIKELY(shmCtx == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| se instance is novalid");
        return;
    }
    HpScanRecordArrayAndRetireList(list, shmCtx, FineGrainedListRetireListConcurrentFilter);
}

inline void DbServerDebugerRemoveAllNodeToRetire(ShmemPtrT subMergeList)
{
    StatusMergeListT *list = DbShmPtrToAddr(subMergeList);
    if (SECUREC_UNLIKELY(list == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| list addr is novalid");
        return;
    }
    FineGrainedListRemoveAllNodeToRetire(list);
}

inline void DbServerDebugerClearRetire(ShmemPtrT subMergeList)
{
    DbMemCtxT *shmCtx = GetSubsStmgListShmCtx();
    if (SECUREC_UNLIKELY(shmCtx == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| se instance is novalid");
        return;
    }
    StatusMergeListT *list = DbShmPtrToAddr(subMergeList);
    if (SECUREC_UNLIKELY(list == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-PUBSUB| list addr is novalid");
        return;
    }
    FineGrainedListClearRetire(list, shmCtx);
}

inline static bool IsTravelListTimeout(uint64_t startTime)
{
    uint64_t endTime = DbRdtsc();
    uint64_t costTime = DbToMseconds(endTime - startTime);
    return costTime >= (uint64_t)TRAVEL_LIST_TIME_MAX;
}

Status DbServerGetDataStats(ShmemPtrT subMergeListPtr, ShmemPtrT subCursor, uint64_t *beforeCnt, uint64_t *afterCnt)
{
    StatusMergeListT *subMergeList = DbShmPtrToAddr(subMergeListPtr);
    if (SECUREC_UNLIKELY(subMergeList == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "|SE-PUBSUB| list addr is novalid, SegId %" PRIu32 " offset %" PRIu32 ".", subMergeListPtr.segId,
            subMergeListPtr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    bool beforeSubCursor = true;
    ShmemPtrT curNodePtr = subMergeList->headDummy, nextNodePtr = DB_INVALID_SHMPTR;
    StatusMergeNodeBaseT *curNode = GetSubsNodeAddr(subMergeList->headDummy), *nextNode = NULL;
    if (SECUREC_UNLIKELY(curNode == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "|SE-PUBSUB| head addr is novalid, SegId %" PRIu32 " offset %" PRIu32 ".", subMergeList->headDummy.segId,
            subMergeList->headDummy.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    uint64_t beforeDataCnt = 0, afterDataCnt = 0;
    DbSpinLock(&curNode->spinLock);
    uint64_t startTime = DbRdtsc();
    while (!IsShmemPtrEqual(curNodePtr, subMergeList->tailDummy)) {
        if (IsShmemPtrEqual(curNodePtr, subCursor)) {
            beforeSubCursor = false;
        }
        if (GetStmgNodeType(subMergeList, curNodePtr, curNode) == DATA) {
            beforeSubCursor ? beforeDataCnt++ : afterDataCnt++;
        }

        bool lockSuccess = FineGrainedListLockNext(curNode, curNodePtr, &nextNode, &nextNodePtr);
        if (!lockSuccess) {
            DbSpinUnlock(&curNode->spinLock);
            return GMERR_INTERNAL_ERROR;
        }

        DbSpinUnlock(&curNode->spinLock);
        curNode = nextNode;
        curNodePtr = nextNodePtr;

        if (IsTravelListTimeout(startTime)) {
            DbSpinUnlock(&curNode->spinLock);
            return GMERR_REQUEST_TIME_OUT;
        }
    }

    DbSpinUnlock(&curNode->spinLock);

    *beforeCnt = beforeSubCursor ? 0 : beforeDataCnt;
    *afterCnt = beforeSubCursor ? 0 : afterDataCnt;
    return GMERR_OK;
}

Status SeGetNodeByBuffer(
    uint8_t *vertexBuf, DmVertexLabelT *vertexLabel, StatusMergeNodeBaseT **node, StatusMergeListT **statusMergeList)
{
    DB_POINTER2(vertexBuf, vertexLabel);
    // 通过buffer找到对应链表节点
    DmValueT propeValue;
    propeValue.type = DB_DATATYPE_UINT64;
    DmVertexBufGetSysPrope(STATUS_MERGE_NODE_ADDR, vertexBuf, &propeValue, vertexLabel->vertexDesc);
    ShmemPtrT ptr = Uint64ToShmemPtr(propeValue.value.ulongValue);
    if (IsShmemPtrEqual(ptr, DB_INVALID_SHMPTR)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Stmg node address is unsound.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    // 转换成node
    *node = DbShmPtrToAddr(ptr);
    if (*node == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "nodePtr is unsound, segId: %" PRIu32 ", offset: %" PRIu32, ptr.segId,
            ptr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    // 用node做判断,判断之前拿到subMergeList
    ShmemPtrT subMergeList = vertexLabel->commonInfo->statusMergeList;
    *statusMergeList = DbShmPtrToAddr(subMergeList);
    if (*statusMergeList == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE,
            "Can not convert Address To Arm 32 Address Addr SegId %" PRIu32 " offset %" PRIu32 ".", subMergeList.segId,
            subMergeList.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    return GMERR_OK;
}

// 校验能否缩容，在doublecheck时同时更改tupleID
bool SePubSubNodeDefrageCheckAndModify(
    HpTupleAddr newAddr, StatusMergeNodeBaseT *curNode, StatusMergeListT *statusMergeList, bool needChangeTuple)
{
    DB_POINTER2(statusMergeList, curNode);
    bool canDefrage = false;
    uint64_t tailDummyU64 = ShmemPtr2Uint64(statusMergeList->tailDummy);
    DbSpinLock(&curNode->spinLock);
    // 当前节点是sub节 --> 不可能出现,该节点是根据heap解析来的,应为数据节点
    if (IsStatusMergeSubsCursorNode(curNode) || curNode->next == tailDummyU64) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Status merge defrage ptr addr is novalid.");
        goto UNLOCK_CUR;
    }
    uint64_t currNextU64 = curNode->next;
    ShmemPtrT currNextPtr = Uint64ToShmemPtr(currNextU64);
    StatusMergeNodeBaseT *currNxtNode = DbShmPtrToAddr(currNextPtr);
    // defrage的下一个是Null,报错返回 --> 数据节点的下一个不为Null,至少是tailNode
    if (currNxtNode == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Status merge currNextPtr is null.");
        goto UNLOCK_CUR;
    }
    DbSpinLock(&currNxtNode->spinLock);
    // defrage的下一个是sub,或下下一个是尾节点,不满足缩容条件
    if (IsStatusMergeSubsCursorNode(currNxtNode) || currNxtNode->next == tailDummyU64) {
        goto UNLOCK_ALL;
    }
    canDefrage = true;
    curNode->isWaiting = true;  // 提交后再置为false
    if (needChangeTuple && canDefrage) {
        curNode->tuple = newAddr;
    }
UNLOCK_ALL:
    DbSpinUnlock(&currNxtNode->spinLock);
UNLOCK_CUR:
    DbSpinUnlock(&curNode->spinLock);
    return canDefrage;
}

Status SeCheckNodeAndModifyTuple(StatusMergeDefragRunCtx *statusMergeCtx, bool needChangeTuple, bool *canDefrage)
{
    DB_POINTER(statusMergeCtx);
    HpTupleAddr addr = statusMergeCtx->addr;
    uint8_t *vertexBuf = statusMergeCtx->vertexBuf;
    DmVertexLabelT *vertexLabel = statusMergeCtx->vertexLabel;
    if (!DmIsLabelSupportStatusMerge(vertexLabel)) {
        *canDefrage = true;
        return GMERR_OK;
    }
    StatusMergeNodeBaseT *node = NULL;
    StatusMergeListT *statusMergeList = NULL;
    // 先通过heap找到node，当前新heap存在cursor上
    Status ret = SeGetNodeByBuffer(vertexBuf, vertexLabel, &node, &statusMergeList);
    if (ret != GMERR_OK) {
        *canDefrage = false;
        return ret;
    }
    statusMergeCtx->curNode = node;
    // 改之前再做一次校验,防止有新的sub节点打破了缩容条件,check步骤和改node内容的步骤应该在同一把锁里
    *canDefrage = SePubSubNodeDefrageCheckAndModify(addr, statusMergeCtx->curNode, statusMergeList, needChangeTuple);
    return ret;
}

Status DbMarkUnusedSubCursor(ShmemPtrT nodePtr)
{
    StatusMergeCursorNodeT *subCursor = GetCursorNodeAddr(nodePtr);
    if (SECUREC_UNLIKELY(subCursor == NULL)) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Trans shmptr to addr unsucc,segId:%" PRIu32 ", offset:%" PRIu32,
            nodePtr.segId, nodePtr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DbSpinLock(&subCursor->lock);
    if (subCursor->baseNode.nodeStatus == NODE_LIVE) {
        subCursor->baseNode.nodeStatus = NODE_UNUSED;
    }
    DbSpinUnlock(&subCursor->lock);
    return GMERR_OK;
}

Status DbRecoverUnusedSubCursor(ShmemPtrT nodePtr)
{
    StatusMergeCursorNodeT *subCursor = GetCursorNodeAddr(nodePtr);
    if (SECUREC_UNLIKELY(subCursor == NULL)) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Trans shmptr to addr unsucc,segId:%" PRIu32 ", offset:%" PRIu32,
            nodePtr.segId, nodePtr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    Status ret = GMERR_OK;
    DbSpinLock(&subCursor->lock);
    if (subCursor->baseNode.nodeStatus == NODE_UNUSED) {
        subCursor->baseNode.nodeStatus = NODE_LIVE;
    } else if (subCursor->baseNode.nodeStatus != NODE_LIVE) {
        ret = GMERR_INTERNAL_ERROR;
        DB_LOG_ERROR(ret, "Sub cursor unexpected status %d, segId:%" PRIu32 ", offset:%" PRIu32,
            subCursor->baseNode.nodeStatus, nodePtr.segId, nodePtr.offset);
    }
    DbSpinUnlock(&subCursor->lock);
    return ret;
}

bool DbCheckSubCursorUnused(ShmemPtrT nodePtr)
{
    StatusMergeNodeBaseT *node = GetSubsNodeAddr(nodePtr);
    if (SECUREC_UNLIKELY(node == NULL)) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Trans shmptr to addr unsucc,segId:%" PRIu32 ", offset:%" PRIu32,
            nodePtr.segId, nodePtr.offset);
        return false;
    }

    // 订阅节点给该节点加额外的锁，防止异常断连和客户端读取冲突
    StatusMergeCursorNodeT *cursorNode = GetCursorNodeAddr(nodePtr);
    if (SECUREC_UNLIKELY(cursorNode == NULL)) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Trans shmptr to addr unsucc,segId:%" PRIu32 ", offset:%" PRIu32,
            nodePtr.segId, nodePtr.offset);
        return false;
    }

    DbSpinLock(&cursorNode->lock);
    uint32_t tryTimes = STMG_CURSOR_CHECK_RETRY_TIME;
    while (tryTimes != 0) {
        if (cursorNode->baseNode.nodeStatus == NODE_LIVE) {
            DbSpinUnlock(&cursorNode->lock);
            DbUsleep(STMG_CURSOR_CHECK_RETRY_INTERVAL);  // 10ms
            tryTimes--;
        } else {
            break;
        }
        DbSpinLock(&cursorNode->lock);
    }
    DbSpinUnlock(&cursorNode->lock);
    return tryTimes > 0;
}
