/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: implementation of B-tree's insert
 * Author: pengweijun
 * Create: 2024-03-14
 */

#include "se_btree_index.h"
#include "se_btree_redo_am.h"
#include "se_buffer_pool_priority_recycle.h"
#include "se_heap_base.h"
#include "se_recovery.h"
#include "se_undo.h"
#include "se_redo_inner.h"

/*
 * the para used to locate the key's position for codecheck
 */
typedef struct BTreePositionPara {
    uint8_t *page;
    BTreeKeyT *targetKey;
    StatusInter ret;  // BTreeLowerBound内部BTreeGetKey返回的错误码
} BTreePositionParaT;

#define ROOT_LEVEL_IF_WAS_A_LEAF (1)  // the root was a leaf, after split root, level == 1

// stack size for split nodes
// Btree一次分裂最大操作页个数为3h+3 h为层数 因此Btree的最大高度限制为（最多允许操作的页面个数-3）/3
static inline uint16_t BtreeGetHeightLimit(SeInstanceT *seIns)
{
    return ((RedoGetAtomicPageCapacity(seIns->redoMgr) - 3) / 3);
}

static StatusInter BtreeCheckRedoLimit(BTreeCursorT *cursor, uint64_t splitNeedPageCount)
{
    SeInstanceT *seInstance = (SeInstanceT *)cursor->idxCtx->idxOpenCfg.seRunCtx->seIns;
    if (seInstance == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "BTree: seIns check");
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    // splitNeedPageCount代表一次分裂可能需要新申请的节点个数
    // 分裂完后的层数应当等于splitNeedPageCount，因此只需要控制分裂时预申请的节点个数即可控制Btree的层数
    if (splitNeedPageCount >= BtreeGetHeightLimit(seInstance)) {
        SE_LAST_ERROR(CONFIGURATION_LIMIT_EXCEEDED_INTER, "BTree: Height exceed limits:%" PRIu64, splitNeedPageCount);
        return CONFIGURATION_LIMIT_EXCEEDED_INTER;
    }
    uint32_t pageSize = (uint32_t)seInstance->seConfig.pageSize * DB_KIBI;
    uint32_t estimatedRedoLogSize = (uint32_t)splitNeedPageCount * pageSize;
    if (!RedoIsPriBufEnough(cursor->redoCtx, estimatedRedoLogSize)) {
        // Redo private buf is not enough, estimateRedoLogSize
        SE_LAST_ERROR(
            CONFIGURATION_LIMIT_EXCEEDED_INTER, "BTree: priBuf not enough Size:%" PRIu32, estimatedRedoLogSize);
        return CONFIGURATION_LIMIT_EXCEEDED_INTER;
    }
    return STATUS_OK_INTER;
}
static StatusInter BTreePesLatchCouplingInit(BTreeCursorT *cursor)
{
    StatusInter ret =
        BTreeGetPage(cursor->pageMgr, cursor->rootAddr.pageId, HIGHEST_PRIORITY_PAGE, &cursor->rootAddr.page, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: GetPageId:%" PRIu32 ", %" PRIu32, cursor->rootAddr.pageId.deviceId,
            cursor->rootAddr.pageId.blockId);
        return ret;
    }
    uint8_t *rootPage = cursor->rootAddr.page;
    BTreePageWLatch(cursor->idxCtx, rootPage);
    // the tree height
    const uint8_t height = (uint8_t)(BTreeGetNodeHdr(rootPage)->level + 1);
    // "ancAddr" records the addresses of the ancestors that are latched;
    // "ancSize" is the number of such nodes
    // "ancAddr" AND "ancSize" release when operation is done
    void *sessionMemCtx = cursor->idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx;
    // Corresponds to members ancAddr, ancSlot, and ancExistTarget in BTreeCursorT.
    size_t size = (sizeof(BTreePageAddrT) + sizeof(int16_t) + sizeof(bool)) * height;
    uint8_t *buffer = DbDynMemCtxAlloc(sessionMemCtx, size);
    if (buffer == NULL) {
        BTreePageWUnlatch(cursor->idxCtx, rootPage);
        SeLeavePage(cursor->pageMgr, cursor->rootAddr.pageId, false);
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER,
            "BTree: unable to alloc memCtx when pesLatch coupling, alloc size: %" PRIu64 "", (uint64_t)size);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    BTreePageAddrT *ancAddr = (BTreePageAddrT *)(void *)buffer;
    cursor->ancSlot = (int16_t *)(void *)(buffer + sizeof(BTreePageAddrT) * height);
    cursor->ancExistTarget = (bool *)(void *)(buffer + (sizeof(BTreePageAddrT) + sizeof(int16_t)) * height);
    // thus far, the root is the only latched ancestor
    ancAddr[0].pageId = cursor->rootAddr.pageId;
    ancAddr[0].page = rootPage;
    cursor->ancSize = 1;
    cursor->ancAddr = ancAddr;
    return STATUS_OK_INTER;
}

static StatusInter BTreeCmpKeys(
    IndexCtxT *idxCtx, BTreePositionParaT *para, int16_t slotId, DmIndexKeyCmpInfoT *cmpInfo)
{
    BTreeKeyT paraKey = {0};
    uint32_t prefixPropeNum = BTreeGetIndexPropeNum(cmpInfo->indexLabel, cmpInfo->dataType);
    StatusInter ret = BTreeGetKey(idxCtx, para->page, slotId, prefixPropeNum, false, &paraKey);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "Get key, slot:%" PRId16, slotId);
        return ret;
    }
    BTreeCmpKeysWithCtx(idxCtx, para->targetKey, &paraKey, cmpInfo);
    BTreeFreeKeyDataAsBigKey(idxCtx, &paraKey);
    return STATUS_OK_INTER;
}

static int16_t BTreeSearchMidKey(IndexCtxT *idxCtx, BTreePositionParaT *para, int16_t high, DmIndexKeyCmpInfoT *cmpInfo)
{
    int16_t low = 0, up = high;
    while (low <= up) {
        const int16_t mid = low + (up - low) / 2;
        StatusInter ret = BTreeCmpKeys(idxCtx, para, mid, cmpInfo);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "BTreeCmpKeys in search midKey cmp");
            para->ret = ret;
            return -1;
        }
        if (cmpInfo->cmpResult == DM_INDEX_KEY_LT && mid < up) {
            up = mid;
        } else if (cmpInfo->cmpResult == DM_INDEX_KEY_GT || cmpInfo->cmpResult == DM_INDEX_KEY_BUTT) {
            low = (int16_t)(mid + 1);
        } else {
            return mid;
        }
    }
    return low;
}

static inline int16_t BTreeLowerBound(
    IndexCtxT *idxCtx, BTreePositionParaT *para, int16_t high, DmIndexKeyCmpInfoT *cmpInfo)
{
    return BTreeSearchMidKey(idxCtx, para, high, cmpInfo);
}

static inline int16_t BTreeDecideChild(
    IndexCtxT *idxCtx, BTreePositionParaT *para, BTreeNodeHdrT *nodeHdr, DmIndexKeyCmpInfoT *cmpInfo)
{
    // an internal node necessarily has at least one entry
    DB_ASSERT(nodeHdr->numSlots >= 1);
    // nodeHdr->numSlots - 2 is the last but one slot
    int16_t hight = (int16_t)(nodeHdr->numSlots - 2);
    if (SECUREC_UNLIKELY(hight < 0)) {
        return 0;
    }
    return BTreeLowerBound(idxCtx, para, hight, cmpInfo);
}

static inline int16_t BTreeDecidePosition(
    IndexCtxT *idxCtx, BTreePositionParaT *para, BTreeNodeHdrT *leafHdr, DmIndexKeyCmpInfoT *cmpInfo)
{
    DB_ASSERT(leafHdr->numSlots >= 0);
    int16_t hight = (int16_t)(leafHdr->numSlots - 1);
    if (SECUREC_UNLIKELY(hight < 0)) {
        // there is no data, insert slot-0, treat compare result as greater than
        cmpInfo->cmpResult = DM_INDEX_KEY_GT;
        return 0;
    }
    return BTreeLowerBound(idxCtx, para, hight, cmpInfo);
}

StatusInter BTreeUpdateCursorInOptLatchCoupling(
    BTreeCursorT *cursor, uint8_t *curPage, BTreeNodeHdrT *curNodeHdr, DmIndexKeyCmpInfoT *cmpInfo, PageIdT curPageId)
{
    cursor->state = CURSOR_VALID;
    cursor->curAddr.pageId = curPageId;
    cursor->curAddr.page = curPage;
    BTreePositionParaT para = {.page = curPage, .targetKey = &cursor->key, .ret = STATUS_OK_INTER};
    int16_t newSlotId = BTreeDecidePosition(cursor->idxCtx, &para, curNodeHdr, cmpInfo);
    if (SECUREC_UNLIKELY(newSlotId < 0)) {
        // when update cursor in optLatch coupling
        SE_ERROR(para.ret, "decide position slotId");
        return para.ret;
    }
    cursor->slot = newSlotId;
    cursor->cmpResultIsEq = (cmpInfo->cmpResult == DM_INDEX_KEY_EQ);
    return STATUS_OK_INTER;
}

StatusInter UpdateCurPage(
    BTreeCursorT *cursor, int16_t slot, uint8_t **curPage, PageIdT *curPageId, BTreeEntryHdrT *entryHdr)
{
    BTreeNodeHdrT *curNodeHdr = BTreeGetNodeHdr(*curPage);
    // the current node becomes the new parent
    PageIdT parentPageId = *curPageId;
    uint8_t *parentPage = *curPage;
    // the child becomes the new current node
#if !defined(NDEBUG) && defined(SYS32BITS)
    // 时序arm32长稳，entryHdr不是4字节对齐可能导致bus error
    (void)memcpy_s(curPageId, sizeof(entryHdr->childPageId), &entryHdr->childPageId, sizeof(entryHdr->childPageId));
#else
    *curPageId = entryHdr->childPageId;
#endif

    DB_ASSERT(DbIsPageIdValid(*curPageId));
    // release the latch of the parent, as required by the latch coupling protocol
    uint32_t curNodeLevel = (uint32_t)curNodeHdr->level - 1;
    BTreePageRUnlatch(cursor->idxCtx, parentPage);
    SeLeavePage(cursor->pageMgr, parentPageId, false);
    StatusInter ret = BTreeGetPage(cursor->pageMgr, *curPageId, curNodeLevel, curPage, false);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "GetPage when update curPage.");
    }
    return ret;
}

static StatusInter RUnLatchAndWLatchPage(BTreeCursorT *cursor, BTreeNodeHdrT *curNodeHdr, uint8_t **curPage)
{
    PageIdT curPageId = ((PageHeadT *)*curPage)->addr;
    uint8_t level = curNodeHdr->level;
    BTreePageRUnlatch(cursor->idxCtx, *curPage);
    SeLeavePage(cursor->pageMgr, curPageId, false);
    StatusInter ret = BTreeGetPage(cursor->pageMgr, curPageId, level, curPage, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: GetPage when latch");
        return ret;
    }
    BTreePageWLatch(cursor->idxCtx, *curPage);
    return STATUS_OK_INTER;
}

/**
 * @brief Starting from the root, use optimistic latch coupling to locate the key `cursor->key`.
 * @details In latch coupling, before latching a node, one must hold a latch on its parent.
 *          However, for the root, it can be latched directly. In optimistic latch coupling,
 *          all internal nodes are latched using R-latches, while, depending on the operation
 *          type (e.g., insertion, lookup), the leaf can be latched using either an R-latch or a W-latch.
 *          After acquiring a latch on the child, the latch on the parent can be released immediately.
 *          `BTreeOptLatchCoupling` starts from the root and locates the leaf that may contain
 *          the given key `cursor->key`. In this process, optimistic latch coupling is used.
 * @param[in] wLatch     whether the leaf should be latched using a W-latch
 * @param[in,out] cursor the cursor; `cursor->key` is the search key; as output, `cursor->curAddr` is the leaf found
 */
// 目的是为了拿到叶子节点，并且把叶子节点存到cursor->curAddr中。并且get + latch
StatusInter BTreeOptLatchCoupling(BTreeCursorT *cursor, bool wLatch)
{
    uint8_t *curPage = NULL;
    PageIdT curPageId = cursor->rootAddr.pageId;
    // start from the root, level is highest
    StatusInter ret = BTreeGetPage(cursor->pageMgr, curPageId, HIGHEST_PRIORITY_PAGE, &curPage, false);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "BTree: GetPage when latch coupling");
        return ret;
    }
    BTreePositionParaT para = {.page = NULL, .targetKey = &cursor->key, .ret = STATUS_OK_INTER};
    DmIndexKeyCmpInfoT cmpInfo = BTreeGetInitKeyCmpInfo(cursor, true);
    for (;;) {
        BTreeNodeHdrT *curNodeHdr = BTreeGetNodeHdr(curPage);
        // by default, a R-latch is used
        BTreePageRLatch(cursor->idxCtx, curPage);
        // if current node is a leaf and it asks for a W-Latch
        if (curNodeHdr->level == 0 && wLatch) {
            ret = RUnLatchAndWLatchPage(cursor, curNodeHdr, &curPage);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                // RUnLatchAndWLatchPage已经释放完毕了页锁
                return ret;
            }
            curNodeHdr = BTreeGetNodeHdr(curPage);
            // between `BTreePageRUnlatch` and `BTreePageWLatch` above, the role of the node may change from
            // a leaf to an internal node; hence, we cannot make assumption that an internal node is always
            // R-latched; we need to note down the latch mode of the node such that we can choose appropriate
            // functions to unlatch the node
        }
        para.page = curPage;
        if (curNodeHdr->level > 0) {
            const int16_t slot = BTreeDecideChild(cursor->idxCtx, &para, curNodeHdr, &cmpInfo);
            if (SECUREC_UNLIKELY(slot < 0)) {
                UnLatchPageAndLeave(curPageId, curPage, false, false, cursor->pageMgr, cursor->idxCtx);
                SE_ERROR(para.ret, "BTree: decide child slot.");
                return para.ret;  // Getkey Failed when decide position.
            }
            BTreeEntryHdrT *entryHdr = NULL;
            ret = BTreeGetEntryHdr(curPage, slot, &entryHdr);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                SE_ERROR(ret, "BTree: Get entryHdr");
                UnLatchPageAndLeave(curPageId, curPage, false, false, cursor->pageMgr, cursor->idxCtx);
                return ret;
            }

            ret = UpdateCurPage(cursor, slot, &curPage, &curPageId, entryHdr);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                // 内部处理异常
                return ret;
            }
        } else {
            ret = BTreeUpdateCursorInOptLatchCoupling(cursor, curPage, curNodeHdr, &cmpInfo, curPageId);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                UnLatchPageAndLeave(curPageId, curPage, wLatch, false, cursor->pageMgr, cursor->idxCtx);
            }
            break;
        }
    }
    return ret;
}

/**
 * @brief The free size that a node has after compaction.
 * @param[in] nodePage  the page corresponding to the node
 */
StatusInter BTreePageGetFreeSize(uint8_t *nodePage, uint16_t *freeSize)
{
    PageHeadT *pageHdr = (PageHeadT *)(void *)nodePage;
    BTreeNodeHdrT *nodeHdr = BTreeGetNodeHdr(nodePage);

    // for non-leaf nodes, the content of the last entry is immaterial;
    // therefore, it (except the entry header and slot) can be eliminated to make room
    uint16_t lastSize = 0;
    if (nodeHdr->level > 0) {
        // for debug only
        DB_ASSERT(nodeHdr->numSlots > 0);
        // the size of the last key
        BTreeEntryHdrT *entryHdr = NULL;
        StatusInter ret = BTreeGetEntryHdr(nodePage, nodeHdr->numSlots - 1, &entryHdr);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "BTree: get entryHdr.");
            return ret;
        }
        BTreeEntryHdrT *lastEntryHdr = NULL;
        ret = BTreeGetEntryHdr(nodePage, nodeHdr->numSlots - 1, &lastEntryHdr);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "BTree: get lastEntryHdr.");
            return ret;
        }
        if (lastEntryHdr->isBigKey) {
            lastSize = 0;
        } else {
            lastSize = lastEntryHdr->size;
        }
    }
    *freeSize = pageHdr->freeSize + lastSize;
    return STATUS_OK_INTER;
}

/**
 * @brief Test whether the node is safe under insertion, if it has enough space to hold a new key.
 *        when pageSize is 8k, the maxLen of keySize is 1024 byte.
 *        (0, 511)    直接裸数据存储
 *        [512, 1024] 存储的是溢出页的PageId
 * @param[in] nodePage  the page corresponding to the node
 */
static StatusInter BTreeInsertIsNodeFreeSizeEnough(uint8_t *nodePage, bool *freeSizeEnough)
{
    PageHeadT *pageHdr = (PageHeadT *)(void *)nodePage;
    // if the key is bigKey, only need sizeof(PageIdT) for save BTreeFlowPage's pageId
    // so the maximum key size for not-bigKey is (minimum bigkey's size - 1).
    // In the minikv scenario, the length of the reserved encryption field is considered,
    // so need add SeGetPageTailReservedSize()
    uint32_t pageSize = pageHdr->endPos + 1 + SeGetPageTailReservedSize();

    uint16_t freeSize = 0;
    StatusInter ret = BTreePageGetFreeSize(nodePage, &freeSize);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: unable to get free size when check size enough.");
        return ret;
    }

    // the max length of key in btree is MAX_BTREE_KEY_LENGTH
    uint32_t maximumKeyLenForNotBigKey = DB_MIN(MAX_BTREE_KEY_LENGTH, BTreeMinimumBigKeyLen(pageSize)) - 1;
    *freeSizeEnough = freeSize >= BTreeCalculateEntrySize(false, maximumKeyLenForNotBigKey);
    return STATUS_OK_INTER;
}

/**
 * @brief Test whether the node is safe under deletion, i.e., if it has at least two entries.
 * @param[in] nodePage  the page corresponding to the node
 */
static inline bool BTreeDeleteIsNodeNearEmpty(uint8_t *nodePage)
{
    BTreeNodeHdrT *nodeHdr = BTreeGetNodeHdr(nodePage);
    return nodeHdr->numSlots > 1;
}

static StatusInter IsNodeSafe(uint8_t *nodePage, bool isInsert, bool *isNodeSafe)
{
    StatusInter ret = STATUS_OK_INTER;
    if (isInsert) {
        ret = BTreeInsertIsNodeFreeSizeEnough(nodePage, isNodeSafe);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "BTree: check size of node");
            return ret;
        }
    } else {
        *isNodeSafe = BTreeDeleteIsNodeNearEmpty(nodePage);
    }
    return ret;
}

static void BTreeUpdateCursor(BTreeCursorT *cursor)
{
    cursor->curAddr = cursor->ancAddr[cursor->ancSize - 1];
    cursor->slot = cursor->ancSlot[cursor->ancSize - 1];
    cursor->state = CURSOR_VALID;
}

static void BTreeCleanPesLatchCouplingRes(BTreeCursorT *cursor)
{
    // alloc mem in BTreePesLatchCouplingInit
    if (cursor->ancAddr == NULL) {
        return;
    }
    for (uint32_t i = 0; i < cursor->ancSize; i++) {
        PageIdT childPageId = cursor->ancAddr[i].pageId;
        BTreePageWUnlatch(cursor->idxCtx, cursor->ancAddr[i].page);
        SeLeavePage(cursor->pageMgr, childPageId, false);
    }
    void *sessionMemCtx = cursor->idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx;
    DbDynMemCtxFree(sessionMemCtx, cursor->ancAddr);
    cursor->ancAddr = NULL;
    cursor->ancSlot = NULL;
    cursor->ancExistTarget = NULL;
}
static void UnlatchAncestorNode(BTreeCursorT *cursor, size_t ancSize, BTreePageAddrT *ancAddr)
{
    for (size_t i = 0; i < ancSize - 1; ++i) {
        BTreePageWUnlatch(cursor->idxCtx, ancAddr[i].page);
        SeLeavePage(cursor->pageMgr, ancAddr[i].pageId, false);
    }
}

// for big method BTreePesLatchCoupling
static StatusInter UpdateStatIfNodeSafe(
    BTreeCursorT *cursor, bool insert, uint8_t *curNodePage, BTreePageAddrT *ancAddr, size_t *ancSize)
{
    // a node is safe means that modifications in this subtree do not propagate to higher nodes;
    // for insertion, a node is safe means that it does not need to be split
    bool isNodeSafe = false;
    StatusInter ret = IsNodeSafe(curNodePage, insert, &isNodeSafe);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: check node safe.");
        return ret;
    }
    if (isNodeSafe && cursor->opType == CURSOR_INSERT) {
        // higher nodes can be safely unlatched
        UnlatchAncestorNode(cursor, *ancSize, ancAddr);
        ancAddr[0] = ancAddr[*ancSize - 1];
        *ancSize = 1;
    }
    return STATUS_OK_INTER;
}

// for big method BTreePesLatchCoupling
typedef struct FindIntermediateNodeParam {
    int16_t childSlot;
    uint8_t *curNodePage;
} FindIntermediateNodeParamT;

static StatusInter FindIntermediateNodeInPesLatchCoupling(BTreeCursorT *cursor, FindIntermediateNodeParamT *findParam)
{
    int16_t childSlot = findParam->childSlot;
    cursor->ancSlot[cursor->ancSize - 1] = childSlot;
    BTreeEntryHdrT *entryHdr = NULL;
    StatusInter ret = BTreeGetEntryHdr(findParam->curNodePage, childSlot, &entryHdr);
    if (ret != STATUS_OK_INTER) {
        // when pessimistic latch coupling
        SE_ERROR(ret, "BTree: get entryHdr");
        return ret;
    }
#if !defined(NDEBUG) && defined(SYS32BITS)
    // 时序arm32长稳，entryHdr不是4字节对齐可能导致bus error
    PageIdT childPageId = SE_INVALID_PAGE_ADDR;
    (void)memcpy_s(&childPageId, sizeof(PageIdT), &entryHdr->childPageId, sizeof(PageIdT));
#else
    const PageIdT childPageId = entryHdr->childPageId;
#endif
    // push the address of child node into latchedNodes
    cursor->ancAddr[cursor->ancSize].pageId = childPageId;
    ++cursor->ancSize;

    BTreeNodeHdrT *curNodeHdr = BTreeGetNodeHdr(findParam->curNodePage);
    ret = BTreeGetPage(cursor->pageMgr, childPageId, (uint32_t)curNodeHdr->level - 1,
        &cursor->ancAddr[cursor->ancSize - 1].page, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "choose child get page");
        --cursor->ancSize;  // page没有拿到 不用leave这个页 把计数减回去
        return ret;
    }
    // latch the node
    BTreePageWLatch(cursor->idxCtx, cursor->ancAddr[cursor->ancSize - 1].page);
    return STATUS_OK_INTER;
}

// split for big method `BTreePesLatchCoupling`
// this is an internal node, decide the subtree to visit
static StatusInter PesLatchIntermediateNodeChooseChild(
    BTreeCursorT *cursor, BTreePositionParaT *para, DmIndexKeyCmpInfoT *cmpInfo)
{
    uint8_t *curNodePage = para->page;
    BTreeNodeHdrT *curNodeHdr = BTreeGetNodeHdr(curNodePage);
    const int16_t childSlot = BTreeDecideChild(cursor->idxCtx, para, curNodeHdr, cmpInfo);
    StatusInter ret = STATUS_OK_INTER;
    if (childSlot < 0) {
        ret = para->ret;
        SE_ERROR(ret, "decide child slot.");
        return ret;
    }
    // whether para.targetKey equal childSlot key, which is stored in cmpInfo->cmpResult
    cursor->ancExistTarget[cursor->ancSize - 1] = (cmpInfo->cmpResult == DM_INDEX_KEY_EQ);
    FindIntermediateNodeParamT findParam = {.childSlot = childSlot, .curNodePage = curNodePage};
    return FindIntermediateNodeInPesLatchCoupling(cursor, &findParam);
}
/**
 * @brief Starting from the root, use pessimistic latch coupling to locate the key `cursor->key`.
 * @details Unlike the optimistic counterpart, in pessimistic latch coupling, W-latches are always used.
 *          In addition, after acquiring a latch on a node, only if it is safe (that is, modifications within
 *          the subtree rooted at the node cannot propagate to higher nodes), the latches on its ancestors
 *          can be released. This is the second main difference with optimistic latch coupling.
 *          Therefore, it is possible more than two nodes hold latches at the same time. In the worst
 *          case, all the nodes from the root to the located leaf are W-latched.
 *          `BTreePesLatchCoupling` starts from the root and locates the leaf that may contain
 *          the given key `cursor->key`. In this process, pessimistic latch coupling is used.
 * @param[in] insert     if true, the operation is an insertion; otherwise, it is an deletion
 * @param[in,out] cursor the cursor; `cursor->key` is the search key; as output, `cursor->curAddr` is the
 *                leaf found and `cursor->ancAddr` are the latched ancestors
 */
StatusInter BTreePesLatchCoupling(BTreeCursorT *cursor, bool insert)
{
    // init 'ancAddr' and 'ancSlot' by rootPage
    StatusInter ret = BTreePesLatchCouplingInit(cursor);
    if (ret != STATUS_OK_INTER) {
        // 这里失败只意味着BTreePesLatchCouplingInit申请内存失败，内部已经释放资源
        // 如果BTreePesLatchCouplingInit存在其他失败原因，请在这里补充释放申请的资源
        return ret;
    }
    BTreePositionParaT para = {.page = NULL, .targetKey = &cursor->key, .ret = STATUS_OK_INTER};
    DmIndexKeyCmpInfoT cmpInfo = BTreeGetInitKeyCmpInfo(cursor, true);
    for (;;) {
        uint8_t *curNodePage = cursor->ancAddr[cursor->ancSize - 1].page;
        BTreeNodeHdrT *curNodeHdr = BTreeGetNodeHdr(curNodePage);
        ret = UpdateStatIfNodeSafe(cursor, insert, curNodePage, cursor->ancAddr, &cursor->ancSize);
        if (ret != STATUS_OK_INTER) {
            // when pessimistic latch coupling
            SE_ERROR(ret, "BTree: update stat.");
            ret = para.ret;
            goto EXIT;
        }
        para.page = curNodePage;
        // this is an internal node, decide the subtree to visit
        if (curNodeHdr->level > 0) {
            ret = PesLatchIntermediateNodeChooseChild(cursor, &para, &cmpInfo);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "intermediate choose child");
                goto EXIT;
            }
        } else {
            cursor->ancSlot[cursor->ancSize - 1] = BTreeDecidePosition(cursor->idxCtx, &para, curNodeHdr, &cmpInfo);
            if (cursor->ancSlot[cursor->ancSize - 1] < 0) {
                ret = para.ret;
                // decide position less than 0 when pessimistic latch coupling
                SE_ERROR(ret, "BTree: decide position");
                goto EXIT;
            }
            // whether para.targetKey equal childSlot key, which is stored in cmpInfo->cmpResult
            cursor->ancExistTarget[cursor->ancSize - 1] = (cmpInfo.cmpResult == DM_INDEX_KEY_EQ);
            break;
        }
    }
    BTreeUpdateCursor(cursor);
    return STATUS_OK_INTER;
EXIT:
    BTreeCleanPesLatchCouplingRes(cursor);
    return ret == STATUS_OK_INTER ? DATA_EXCEPTION_VALUE_OUT_OF_RANGE : ret;
}

/**
 * @brief Write undo log for insertion.
 * @param[in] idxCtx        the index context
 * @param[in] key           the key inserted
 * @param[in] rollbackMode  can be either 0 or 1; 0: rollback by mark deletion; 1: rollback by physical deletion
 * @param[in] isRollback    whether this function is called in a rollback process
 */
static StatusInter BTreeWriteUndoInsert(
    IndexCtxT *idxCtx, IndexKeyT *insertKey, HpTupleAddr rowId, uint32_t rollbackMode, bool isRollback)
{
    if (isRollback) {
        return STATUS_OK_INTER;
    }
    UndoRowOpInfoT undoInfo = {0};
    undoInfo.isRetained = false;
    undoInfo.resType = TRX_RES_BTREE;
    undoInfo.opType = TRX_OP_INSERT;
    // we do not use `rowTrxId`; thus use it to store the index ID
    undoInfo.rowTrxId = idxCtx->idxMetaCfg.indexId;
    undoInfo.rowRollPtr = rollbackMode;
    undoInfo.containerAddr = *(uint64_t *)(&(idxCtx->idxShmAddr));
    undoInfo.rowId = rowId;
    undoInfo.rowSize = insertKey->keyLen;
    undoInfo.rowBuf = insertKey->keyData;
    undoInfo.labelType = idxCtx->idxOpenCfg.heapHandle->dmDetail.labelType;
    undoInfo.labelId = idxCtx->idxOpenCfg.heapHandle->heapCfg.labelId;
    undoInfo.isPersistent = idxCtx->idxOpenCfg.heapHandle->heapCfg.isPersistent;
    SeRunCtxT *seRunCtx = idxCtx->idxOpenCfg.seRunCtx;
    return TrxUndoReportRowOperation(seRunCtx->undoCtx, seRunCtx->trx, &undoInfo, NULL);
}

/**
 * @brief Allocate `allocCnt` pages and latch them.
 * @param[in] allocCnt  the number of pages to allocate
 * @param[in] newNodes  the info of the allocated pages are stored in `newNodes[1...allocCnt]`
 * @param[in] cursor    for obtaining `pageMgr` and `spaceId`
 */
static StatusInter BTreeSplitAllocBlock(BTreeCursorT *cursor, size_t allocCnt, PreAllocSplitNodeT *newNodes)
{
    const uint32_t spaceId = cursor->idxCtx->idxMetaCfg.tableSpaceIndex;
    uint32_t trmId = cursor->trmId;
    // allocate blocks
    for (size_t i = 1; i <= allocCnt; ++i) {
        newNodes[i].addr.pageId = SE_INVALID_PAGE_ADDR;
        AllocPageParamT allocPageParam = {.spaceId = spaceId,
            .trmId = trmId,
            .labelId = RSM_INVALID_LABEL_ID,
            .dbInstance = cursor->redoCtx->redoMgr->seIns->dbInstance,
            .labelRsmUndo = NULL};  // index不使用rsmUndo
        StatusInter interErrno = SeAllocPage(cursor->pageMgr, &allocPageParam, &newNodes[i].addr.pageId);
        if (interErrno != STATUS_OK_INTER) {
            // unable to alloc page when split alloc block
            SE_ERROR(interErrno, "BTree: Alloc page, spaceId:%" PRIu32, spaceId);
            for (size_t j = 1; j < i; ++j) {
                FreePageParamT freePageParam = {.spaceId = spaceId,
                    .addr = newNodes[j].addr.pageId,
                    .dbInstance = cursor->redoCtx->redoMgr->seIns->dbInstance,
                    .labelRsmUndo = NULL};
                SeInitCachePagePara(&freePageParam.cachePagePara);
                (void)SeFreePage(cursor->pageMgr, &freePageParam);
            }
            return interErrno;
        }
    }

    // read the blocks
    for (size_t i = 1; i <= allocCnt; ++i) {
        // just initialize, so the priority of pages set to LOWEST_PRIORITY_PAGE
        StatusInter interErrno =
            BTreeGetPage(cursor->pageMgr, newNodes[i].addr.pageId, 0, &newNodes[i].addr.page, true);
        if (interErrno != STATUS_OK_INTER) {
            SE_ERROR(interErrno, "BTree: getpage when splitAllocBlock.");
            for (size_t j = 1; j < i; ++j) {
                BTreePageWUnlatch(cursor->idxCtx, newNodes[j].addr.page);
                SeLeavePage(cursor->pageMgr, newNodes[j].addr.pageId, false);
                FreePageParamT freePageParamRead = {.spaceId = spaceId,
                    .addr = newNodes[j].addr.pageId,
                    .dbInstance = cursor->redoCtx->redoMgr->seIns->dbInstance,
                    .labelRsmUndo = NULL};
                SeInitCachePagePara(&freePageParamRead.cachePagePara);
                (void)SeFreePage(cursor->pageMgr, &freePageParamRead);
            }
            return interErrno;
        }
        // while we unable to get page, revert alloc page in newNodes
        BTreePageWLatch(cursor->idxCtx, newNodes[i].addr.page);
        newNodes[i].isUsed = false;
    }

    return STATUS_OK_INTER;
}

static bool TryDecidePosToNextNode(const BTreeCursorT *cursor, uint8_t *nextPage, int16_t *movePos,
    RedistributeNodeT *redistributeNode, bool *splitRight)
{
    const int16_t curNodeNumSlots = BTreeGetNodeHdr(cursor->curAddr.page)->numSlots;
    // insertEntrySize是要插入的key对应的Entry占的空间大小
    const size_t insertEntrySize = cursor->key.keySize + sizeof(uint16_t) + sizeof(BTreeEntryHdrT);
    // 要插入的key本应该插入到当前结点的slot位置
    const int16_t insertPos = cursor->slot;
    size_t moveSize = 0;
    // next页最大的剩余空间
    uint16_t nextPageFreeSize = ((PageHeadT *)(void *)nextPage)->freeSize;
    // cur页最大的剩余空间
    uint16_t curPageFreeSize = ((PageHeadT *)(void *)cursor->curAddr.page)->freeSize;
    // 从当前页的最后一个slot开始向前遍历 直到原本要插入的key应该在的slot位置
    for (int16_t i = curNodeNumSlots - 1; i >= insertPos; --i) {
        BTreeEntryHdrT *entryHdr = NULL;
        StatusInter ret = BTreeGetEntryHdr(cursor->curAddr.page, i, &entryHdr);
        if (ret != STATUS_OK_INTER) {
            // unable to get entryHdr when decide pos to next node
            SE_ERROR(ret, "BTree: Get entryHdr.");
            return ret;
        }
        moveSize += entryHdr->size + sizeof(uint16_t) + sizeof(BTreeEntryHdrT);
        // next页能否放下 不能放下返回分配失败 不能向next页进行移动
        if (moveSize > nextPageFreeSize) {
            redistributeNode->mergeToNext = false;
            *splitRight = false;
            return STATUS_OK_INTER;
        }
        // cur页空间扩大后能否插入 能插入就决定截断点
        if (curPageFreeSize + moveSize >= insertEntrySize) {
            *movePos = i;
            *splitRight = false;
            return STATUS_OK_INTER;
        }
    }
    // 把要插入的key之后的所有key都挪过去以后cur页空间仍然不够 尝试把要插入的key也挪过去
    moveSize += insertEntrySize;
    if (moveSize <= nextPageFreeSize) {
        *movePos = insertPos;
        *splitRight = true;
        return STATUS_OK_INTER;
    }

    redistributeNode->mergeToNext = false;
    *splitRight = false;
    return STATUS_OK_INTER;
}

static StatusInter BTreeRedistributeNext(BTreeCursorT *cursor, RedistributeNodeT *redistributeNode)
{
    BTreeNodeHdrT *nodeHdr = BTreeGetNodeHdr(cursor->curAddr.page);
    PageIdT nextPageId = nodeHdr->nextPageId;
    uint8_t *nextPageData = cursor->nextAddr.page;
    // 初始化为无效值
    int16_t transferPoint = BTREE_INVALID_SLOT_ID;
    // 决定从当前页搬到next页的数据截断点以及新数据插入的位置
    bool right = false;
    StatusInter ret = TryDecidePosToNextNode(cursor, nextPageData, &transferPoint, redistributeNode, &right);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Decide pos when redistribute next");
        return ret;
    }
    if (!redistributeNode->mergeToNext) {
        return STATUS_OK_INTER;
    }

    // transfer the larger entries from the old node to nextNode
    const int16_t oldNodeNumSlots = nodeHdr->numSlots;
    // pre append to next node page, thus the beginning insert slots seq is 0.
    TransInitArgsT transInitArgs = {false, nodeHdr->level, oldNodeNumSlots - transferPoint};
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    BTreeRedoForTransferInsertInit(nextPageId, transInitArgs, 0, redoCtx);
    ret = BTreeTransfer(cursor, transferPoint, oldNodeNumSlots, cursor->curAddr.page, nextPageData);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Transfer when redistribute next");
        return ret;
    }
    BTreeRedoForDeleteRange(cursor->curAddr.pageId, transferPoint, oldNodeNumSlots, redoCtx);
    ++nodeHdr->version;

    // insert the cursor->key after redistribute
    BTreeEntryHdrT newEntryHdr = InitializeEntryHdr(&cursor->key, 0, SE_INVALID_PAGE_ADDR);
    PageIdT bigKeyPageId = SE_INVALID_PAGE_ADDR;
    ret = ChangeKeyData4BigKeyIfNeed(cursor, &bigKeyPageId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Change keyData when redistribute next");
        return ret;
    }
    if (newEntryHdr.isBigKey) {
        cursor->key.keyData = (uint8_t *)&bigKeyPageId;
    }
    if (right) {
        BTreeInsertToNode(0, &cursor->key, &newEntryHdr, nextPageData);
        BTreeRedoForInsert(nextPageId, 0, &cursor->key, &newEntryHdr, redoCtx);
    } else {
        BTreeInsertToNode(cursor->slot, &cursor->key, &newEntryHdr, cursor->curAddr.page);
        BTreeRedoForInsert(cursor->curAddr.pageId, cursor->slot, &cursor->key, &newEntryHdr, redoCtx);
    }
    if (newEntryHdr.isBigKey) {
        cursor->key.keyData = NULL;
    }

    return STATUS_OK_INTER;
}

static StatusInter BtreeUpdateCursor4SplitNextAndPreMain(
    BTreeCursorT *cursor, BTreePageAddrT *lastKeyChangePageAddr, BTreeKeyT *lastKey)
{
    BTreeNodeHdrT *leafNodeHdr = BTreeGetNodeHdr(lastKeyChangePageAddr->page);
    uint32_t propeNum =
        BTreeGetIndexPropeNum(cursor->idxCtx->idxOpenCfg.vlIndexLabel, cursor->idxCtx->idxMetaCfg.keyDataType);
    StatusInter ret =
        BTreeGetKey(cursor->idxCtx, lastKeyChangePageAddr->page, leafNodeHdr->numSlots - 1, propeNum, false, lastKey);
    if (ret != STATUS_OK_INTER) {
        // unable to get key when update cursor for split next and pre,
        SE_ERROR(ret, "Get key, pageId:%" PRIu32 ", %" PRIu32 ", slot: %" PRId16,
            lastKeyChangePageAddr->pageId.deviceId, lastKeyChangePageAddr->pageId.blockId, leafNodeHdr->numSlots - 1);
        return ret;
    }
    cursor->key = *lastKey;
    // move cursor to the parent
    cursor->curAddr = cursor->ancAddr[cursor->ancSize - 2];  // 2 here means move to second floor
    cursor->slot = cursor->ancSlot[cursor->ancSize - 2];     // 2 here means move choose the slot in curAddr
    return STATUS_OK_INTER;
}

static StatusInter ChooseSlotWithChildPageId(
    uint8_t *nodePage, const BTreeNodeHdrT *nodeHdr, PageIdT targetPageId, int16_t *removeSlot)
{
    // an internal node necessarily has at least one entry
    int16_t high = nodeHdr->numSlots;
    for (int16_t low = 0; low < high; ++low) {
        BTreeEntryHdrT *entryHdr = NULL;
        StatusInter ret = BTreeGetEntryHdr(nodePage, low, &entryHdr);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Get entryHdr when choose slot.");
            return ret;
        }
#if !defined(NDEBUG) && defined(SYS32BITS)
        PageIdT childPageId;
        (void)memcpy_s(&childPageId, sizeof(PageIdT), &entryHdr->childPageId, sizeof(PageIdT));
        if (DbIsPageIdEqual(childPageId, targetPageId)) {
#else
        if (DbIsPageIdEqual(entryHdr->childPageId, targetPageId)) {
#endif
            *removeSlot = low;
            return STATUS_OK_INTER;
        }
    }
    // not found matched slot, thus return invalid slot id
    *removeSlot = BTREE_INVALID_SLOT_ID;
    return STATUS_OK_INTER;
}

static StatusInter CheckNodeSizeUpdateParentNode(
    UpdateParentNodeParamsT *updateMaxValParam, BTreePageAddrT parentAddr, int16_t removeSlot)
{
    const size_t requiredSize = updateMaxValParam->targetKey.keySize + sizeof(uint16_t) + sizeof(BTreeEntryHdrT);
    PageHeadT *curPageHdr = (PageHeadT *)(void *)(parentAddr.page);
    BTreeNodeHdrT *curNodeHdr = BTreeGetNodeHdr(parentAddr.page);
    // try to insert key after delete the key in parent node with same childPageId
    BTreeEntryHdrT *removeEntryHdr = NULL;
    StatusInter ret = BTreeGetEntryHdr(parentAddr.page, removeSlot, &removeEntryHdr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Get removeHdr when update parent.");
        return ret;
    }

    BTreeEntryHdrT *entryHdr = NULL;
    ret = BTreeGetEntryHdr(parentAddr.page, curNodeHdr->numSlots - 1, &entryHdr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Get entryHdr when update parent.");
        return ret;
    }
    uint16_t lastSize = entryHdr->size;  // same BTreePageGetFreeSize
    const size_t eraseSize = sizeof(uint16_t) + sizeof(BTreeEntryHdrT) + removeEntryHdr->size;
    if (requiredSize > (curPageHdr->freeSize + eraseSize + lastSize)) {
        // need split parent node when update parent node value.
        DB_LOG_INFO("BTree: need split parent node");
        return STATUS_OK_INTER;
    }
    return STATUS_OK_INTER;
}

static StatusInter UpdateParentNodeVal(BTreeCursorT *cursor, BTreePageAddrT parentAddr,
    UpdateParentNodeParamsT *updateMaxValParam, int16_t *removeSlot, bool *finishSplit)
{
    *finishSplit = false;
    BTreeNodeHdrT *parentNodeHdr = BTreeGetNodeHdr(parentAddr.page);
    StatusInter ret =
        ChooseSlotWithChildPageId(parentAddr.page, parentNodeHdr, updateMaxValParam->targetPageId, removeSlot);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Choose slot when update parentNode.");
        return ret;
    }

    if (*removeSlot == BTREE_INVALID_SLOT_ID) {
        return STATUS_OK_INTER;
    }

    ret = CheckNodeSizeUpdateParentNode(updateMaxValParam, parentAddr, *removeSlot);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    ret = BTreeDeleteEntry(cursor, parentAddr.pageId, parentAddr.page, *removeSlot);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Delete entry when update parentNode");
        return ret;
    }
    BTreeEntryHdrT newEntryHdr = InitializeEntryHdr(&updateMaxValParam->targetKey, 0, updateMaxValParam->targetPageId);
    PageIdT bigKeyPageId = SE_INVALID_PAGE_ADDR;
    ret = ChangeKeyData4BigKeyIfNeed(cursor, &bigKeyPageId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Change keyData when update parentNode");
        return ret;
    }
    // 代表是有效的大key，把大key的pageId塞到key.keyData 随后在BTreeInsertToNode一起move到页上data位置
    if (DbIsPageIdValid(bigKeyPageId)) {
        updateMaxValParam->targetKey.keyData = (uint8_t *)&bigKeyPageId;
    }
    BTreeInsertToNode(*removeSlot, &updateMaxValParam->targetKey, &newEntryHdr, parentAddr.page);
    // write redo log
    BTreeRedoForInsert(parentAddr.pageId, *removeSlot, &updateMaxValParam->targetKey, &newEntryHdr, cursor->redoCtx);
    if (newEntryHdr.isBigKey) {
        updateMaxValParam->targetKey.keyData = NULL;
    }
    *finishSplit = true;
    return STATUS_OK_INTER;
}

static StatusInter BTreeSplitNodeForNextAndPre(BTreeCursorT *cursor)
{
    int16_t splitPoint = 0;
    bool assignRight = false;
    StatusInter ret = BTreeDecideSplitPoint(cursor, &splitPoint, &assignRight);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Decide split point when for next.");
        return ret;
    }

    BTreeInitPage(false, cursor->nextAddr.page);

    BTreeNodeHdrT *newNodeHdr = BTreeGetNodeHdr(cursor->nextAddr.page);
    BTreeNodeHdrT *oldNodeHdr = BTreeGetNodeHdr(cursor->curAddr.page);
    newNodeHdr->level = oldNodeHdr->level;

    const int16_t oldNodeNumSlots = oldNodeHdr->numSlots;
    // transfer the larger entries from the old node to newly allocated node
    BTreeRedoForTransferInit(
        cursor->nextAddr.pageId, false, newNodeHdr->level, oldNodeNumSlots - splitPoint, SeGetCurRedoCtx());
    ret = BTreeTransfer(cursor, splitPoint, oldNodeNumSlots, cursor->curAddr.page, cursor->nextAddr.page);
    if (ret != STATUS_OK_INTER) {
        // Transfer when for next
        SE_ERROR(ret, "BTree: transfer");
        return ret;
    }
    BTreeRedoForDeleteRange(cursor->curAddr.pageId, splitPoint, oldNodeNumSlots, SeGetCurRedoCtx());
    ++oldNodeHdr->version;
    return STATUS_OK_INTER;
}

static StatusInter UpdateParentNode(BTreeCursorT *cursor, const BTreePageAddrT *updateParentAddrLeft,
    const BTreePageAddrT *updateParentAddrRight, UpdateParentNodeParamsT *updateMaxValParam, bool *finishSplit)
{
    BTreePageAddrT updateParentAddr = *updateParentAddrLeft;
    int16_t removeSlot = 0;
    StatusInter ret = UpdateParentNodeVal(cursor, updateParentAddr, updateMaxValParam, &removeSlot, finishSplit);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: update parent node value.");
        return ret;
    }
    if (removeSlot == BTREE_INVALID_SLOT_ID && !*finishSplit) {
        updateParentAddr = *updateParentAddrRight;
        ret = UpdateParentNodeVal(cursor, updateParentAddr, updateMaxValParam, &removeSlot, finishSplit);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "BTree: update parentNodeVal");
            return ret;
        }
    }
    if (removeSlot == BTREE_INVALID_SLOT_ID) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "BTree: inv removeSlot.");
        return DATA_EXCEPTION_INTER;
    }
    return STATUS_OK_INTER;
}

inline static void BTreeFreeKeyDataAndResetCursorKeyData(BTreeCursorT *cursor, BTreeKeyT *tmpKey)
{
    BTreeFreeKeyDataAsBigKey(cursor->idxCtx, tmpKey);
    cursor->key.keyData = NULL;
}

static StatusInter UpdateCursorAfterSplit(
    BTreeCursorT *cursor, PageIdT *oldChildPageId, PageIdT *newChildPageId, size_t i, BTreeKeyT *retKey)
{
    BTreeNodeHdrT *nodeHdr = BTreeGetNodeHdr(cursor->curAddr.page);
    if (oldChildPageId != NULL) {
        *oldChildPageId = cursor->curAddr.pageId;
    }
    uint32_t propeNum =
        BTreeGetIndexPropeNum(cursor->idxCtx->idxOpenCfg.vlIndexLabel, cursor->idxCtx->idxMetaCfg.keyDataType);
    StatusInter ret = BTreeGetKey(cursor->idxCtx, cursor->curAddr.page, nodeHdr->numSlots - 1, propeNum, false, retKey);
    if (ret != STATUS_OK_INTER) {
        // Get key when update cursor after split
        SE_ERROR(ret, "BTree: Get key, pageId:%" PRIu32 ", %" PRIu32 ", slot:%" PRId16, cursor->curAddr.pageId.deviceId,
            cursor->curAddr.pageId.blockId, nodeHdr->numSlots - 1);
        return ret;
    }
    // move cursor to the parent
    cursor->key = *retKey;
    cursor->curAddr = cursor->ancAddr[i - 1];
    cursor->slot = cursor->ancSlot[i - 1];
    *newChildPageId = cursor->nextAddr.pageId;
    return STATUS_OK_INTER;
}

static void UpdateTwoLeafLinkInSplitRoot(BTreePageAddrT *prevAddr, BTreePageAddrT *nextAddr)
{
    BTreeNodeHdrT *rightChildHdr = BTreeGetNodeHdr(nextAddr->page);
    rightChildHdr->prevPageId = prevAddr->pageId;
    rightChildHdr->nextPageId = SE_INVALID_PAGE_ADDR;

    // write redo log
    BTreeRedoForUpdateLeafLink(
        nextAddr->pageId, rightChildHdr->prevPageId, rightChildHdr->nextPageId, SeGetCurRedoCtx());

    BTreeNodeHdrT *leftChildHdr = BTreeGetNodeHdr(prevAddr->page);
    leftChildHdr->prevPageId = SE_INVALID_PAGE_ADDR;
    leftChildHdr->nextPageId = nextAddr->pageId;
    BTreeRedoForUpdateLeafLink(prevAddr->pageId, leftChildHdr->prevPageId, leftChildHdr->nextPageId, SeGetCurRedoCtx());
}

static StatusInter TransferDataToNewNodesInSplitRoot(BTreeCursorT *cursor, int16_t splitPoint)
{
    BTreeNodeHdrT *rootHdr = BTreeGetNodeHdr(cursor->curAddr.page);
    BTreeNodeHdrT *rightChildHdr = BTreeGetNodeHdr(cursor->nextAddr.page);
    const int16_t rootNumSlots = rootHdr->numSlots;

    // write redo log; `TransferInit` only writes part of the redo record; the rest will be written in `BTreeTransfer`
    BTreeRedoForTransferInit(
        cursor->nextAddr.pageId, false, rightChildHdr->level, rootNumSlots - splitPoint, SeGetCurRedoCtx());
    // transfer right parts
    StatusInter ret = BTreeTransfer(cursor, splitPoint, rootNumSlots, cursor->curAddr.page, cursor->nextAddr.page);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Transfer right when split root");
        return ret;
    }

    BTreeNodeHdrT *leftChildHdr = BTreeGetNodeHdr(cursor->prevAddr.page);
    // write redo log; `TransferInit` only writes part of the redo record; the rest will be written in `BTreeTransfer`
    BTreeRedoForTransferInit(cursor->prevAddr.pageId, false, leftChildHdr->level, splitPoint, SeGetCurRedoCtx());
    // then, transfer left parts
    ret = BTreeTransfer(cursor, 0, splitPoint, cursor->curAddr.page, cursor->prevAddr.page);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Transfer left when split root");
        return ret;
    }

    // write the redo log for the root node, which becomes empty now
    BTreeRedoForDeleteRange(cursor->curAddr.pageId, 0, rootNumSlots, SeGetCurRedoCtx());

    // update level after delete range for root node(the level should be equal while the version is same)
    rootHdr->level += 1;
    // the root was a leaf; in this case, the two new children (which are leaves) need to be linked
    // we should update link after BTreeRedoForTransferInit which will `BTreeInitPage` in replay
    if (rootHdr->level == ROOT_LEVEL_IF_WAS_A_LEAF) {
        UpdateTwoLeafLinkInSplitRoot(&cursor->prevAddr, &cursor->nextAddr);
    }
    // the root has been modified, update its version
    ++rootHdr->version;
    return STATUS_OK_INTER;
}

/**
 * @brief This function is used to insert entry (of child) to the root after root split.
 * @param[in] cursor     the cursor
 * @param[in] childAddr  the address of the child
 * @param[in] slot       the slot to insert the entry
 * @param[in] rowId       the slot to insert the entry
 */
StatusInter BTreeInsertToRootAfterSplitRoot(
    BTreeCursorT *cursor, BTreePageAddrT childAddr, int16_t slot, PageIdT bigKeyPageId)
{
    // get the entry header of the last entry, which will be posted on the root
    int16_t slotId = BTreeGetNodeHdr(childAddr.page)->numSlots - 1;
    BTreeEntryHdrT *lastEntryHdr = NULL;
    StatusInter ret = BTreeGetEntryHdr(childAddr.page, slotId, &lastEntryHdr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Get entryHdr when insert root.");
        return ret;
    }

    // set `cursor->key` to point to the last entry
    if (!DbIsPageIdValid(bigKeyPageId)) {
        cursor->key.keyData = (uint8_t *)(lastEntryHdr + 1);
    } else {
        cursor->key.keyData = (uint8_t *)&bigKeyPageId;
    }
    cursor->key.isBigKey = lastEntryHdr->isBigKey;
    cursor->key.keySize = lastEntryHdr->size;
    cursor->key.rowId = lastEntryHdr->rowId;

    // construct the entry header for the new entry
    BTreeEntryHdrT insertEntryHdr = *lastEntryHdr;
    insertEntryHdr.childPageId = childAddr.pageId;
    insertEntryHdr.isDeleted = 0;

    // insert the entry
    ret = BTreeInsertToNode(slot, &cursor->key, &insertEntryHdr, cursor->curAddr.page);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Insert node when insert root.");
        return ret;
    }

    // note that we use `TransferAppend` log here since we use transfer log to recover;
    // refer to function `BTreeInitRootAfterSplitRoot`
    BTreeRedoForTransferAppend(&cursor->key, &insertEntryHdr, cursor->redoCtx);

    if (lastEntryHdr->isBigKey) {
        cursor->key.keyData = NULL;
    }
    // update statistics
    cursor->stat.occupiedMemorySizeChange +=
        (uint32_t)BTreeCalculateEntrySize(cursor->key.isBigKey, cursor->key.keySize);
    return ret;
}

/**
 * @brief This function is used in `BTreeSplitRoot` to initialize the root after split.
 * @param[in] cursor         the cursor
 * @param[in] rootHdr        the node header of the root
 */
static StatusInter BTreeInitRootAfterSplitRoot(BTreeCursorT *cursor, BTreeNodeHdrT *rootHdr)
{
    PageIdT leftBigKeyPageId = SE_INVALID_PAGE_ADDR;
    PageIdT rightBigKeyPageId = SE_INVALID_PAGE_ADDR;
    StatusInter interErrno = BTreeGetBigKeyPageIds(cursor, &leftBigKeyPageId, &rightBigKeyPageId);
    if (interErrno != STATUS_OK_INTER) {
        // unable to get bk page id after split root.
        SE_ERROR(interErrno, "BTree: Get bigKey pageId");
        return interErrno;
    }

    // write redo log; will add 2 entries to the root
    BTreeRedoForTransferInit(cursor->curAddr.pageId, true, rootHdr->level, 2, cursor->redoCtx);

    // add the entry to the left child to the root; `cursor->prevAddr` is the address of the left child
    interErrno = BTreeInsertToRootAfterSplitRoot(cursor, cursor->prevAddr, 0, leftBigKeyPageId);
    if (interErrno != STATUS_OK_INTER) {
        // unable to insert left root when init root after split root.
        SE_ERROR(interErrno, "BTree: Insert left root");
        return interErrno;
    }

    // add the entry to the right child to the root; `cursor->nextAddr` is the address of the right child
    interErrno = BTreeInsertToRootAfterSplitRoot(cursor, cursor->nextAddr, 1, rightBigKeyPageId);
    if (interErrno != STATUS_OK_INTER) {
        // unable to insert right root when init root after split root.
        SE_ERROR(interErrno, "BTree: Insert right root");
    }
    return interErrno;
}

static StatusInter BTreeSplitRootForDistribute(
    const PageIdT newChildPageId, BTreeCursorT *cursor, const BTreeKeyT *lastKey, int16_t removeSlot)
{
    int16_t splitPoint = 0;
    bool assignRight = false;
    StatusInter ret = BTreeDecideSplitPoint(cursor, &splitPoint, &assignRight);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: decide split point");
        return ret;
    }

    BTreeInitPage(false, cursor->prevAddr.page);
    BTreeInitPage(false, cursor->nextAddr.page);

    BTreeNodeHdrT *rootHdr = BTreeGetNodeHdr(cursor->curAddr.page);
    BTreeNodeHdrT *leftChildHdr = BTreeGetNodeHdr(cursor->prevAddr.page);
    BTreeNodeHdrT *rightChildHdr = BTreeGetNodeHdr(cursor->nextAddr.page);
    leftChildHdr->level = rightChildHdr->level = rootHdr->level;

    PageIdT oldChildPageId = SE_INVALID_PAGE_ADDR;
    if (rootHdr->level > 0) {
        BTreeEntryHdrT *entryHdr = NULL;
        ret = BTreeGetEntryHdr(cursor->curAddr.page, removeSlot, &entryHdr);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "BTree: get entryHdr point");
            return ret;
        }
        oldChildPageId = entryHdr->childPageId;
    }

    ret = TransferDataToNewNodesInSplitRoot(cursor, splitPoint);
    if (ret != STATUS_OK_INTER) {
        // unable to transfer data when split root for distribute.
        SE_ERROR(ret, "BTree: transfer data");
        return ret;
    }

    bool finishSplit = false;
    UpdateParentNodeParamsT updateMaxValParam = {.targetKey = *lastKey, .targetPageId = oldChildPageId};
    ret = UpdateParentNode(cursor, &cursor->prevAddr, &cursor->nextAddr, &updateMaxValParam, &finishSplit);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: update parent node.");
        return ret;
    }

    // reset the root after split. insert two child nodes
    return BTreeInitRootAfterSplitRoot(cursor, rootHdr);
}

/**
 * @brief This function is used in `BTreeSplitRoot` to insert the new entry after split.
 * The parameters are as those in `BTreeSplitRoot`.
 */
static StatusInter BTreeInsertToChildAfterSplitRoot(
    BTreeCursorT *cursor, BTreeNodeHdrT *rootHdr, PageIdT oldChildPageId, bool assignRight, int16_t splitPoint)
{
    BTreeEntryHdrT newEntryHdr = InitializeEntryHdr(&cursor->key, 0, SE_INVALID_PAGE_ADDR);
    if (rootHdr->level != ROOT_LEVEL_IF_WAS_A_LEAF) {
        newEntryHdr.childPageId = oldChildPageId;
    }
    PageIdT bigKeyPageId = SE_INVALID_PAGE_ADDR;
    StatusInter ret = ChangeKeyData4BigKeyIfNeed(cursor, &bigKeyPageId);
    if (ret != STATUS_OK_INTER) {
        // unable to change key data when insert to child after split root
        SE_ERROR(ret, "Change keyData");
        return ret;
    }
    if (newEntryHdr.isBigKey) {
        cursor->key.keyData = (uint8_t *)&bigKeyPageId;
    }
    int16_t insertSlot = cursor->slot;
    uint8_t *insertPage = cursor->prevAddr.page;
    PageIdT insertPageId = cursor->prevAddr.pageId;
    if (assignRight) {
        insertSlot = cursor->slot - splitPoint;
        insertPage = cursor->nextAddr.page;
        insertPageId = cursor->nextAddr.pageId;
    }
    BTreeInsertToNode(insertSlot, &cursor->key, &newEntryHdr, insertPage);
    BTreeRedoForInsert(insertPageId, insertSlot, &cursor->key, &newEntryHdr, cursor->redoCtx);
    if (newEntryHdr.isBigKey) {
        cursor->key.keyData = NULL;
    }
    cursor->stat.occupiedMemorySizeChange +=
        (uint32_t)BTreeCalculateEntrySize(cursor->key.isBigKey, cursor->key.keySize);
    return STATUS_OK_INTER;
}

static StatusInter RedoForTransferInSplitRoot(BTreeCursorT *cursor, int16_t splitPoint, BTreeNodeHdrT *rootHdr,
    BTreeNodeHdrT *leftChildHdr, BTreeNodeHdrT *rightChildHdr)
{
    // 先把numSlots记下来 否则后面transfer完就会发生改变了
    const int16_t numSlots = rootHdr->numSlots;
    // write redo log; `TransferInit` only writes part of the redo record; the rest will be written in `BTreeTransfer`
    BTreeRedoForTransferInit(
        cursor->nextAddr.pageId, false, rightChildHdr->level, rootHdr->numSlots - splitPoint, cursor->redoCtx);
    StatusInter ret = BTreeTransfer(cursor, splitPoint, rootHdr->numSlots, cursor->curAddr.page, cursor->nextAddr.page);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Transfer right part");
        return ret;
    }
    // write redo log; `TransferInit` only writes part of the redo record; the rest will be written in `BTreeTransfer`
    BTreeRedoForTransferInit(cursor->prevAddr.pageId, false, leftChildHdr->level, splitPoint, cursor->redoCtx);
    ret = BTreeTransfer(cursor, 0, splitPoint, cursor->curAddr.page, cursor->prevAddr.page);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Transfer remain part");
        return ret;
    }
    // write the redo log for the root node, which becomes empty now
    BTreeRedoForDeleteRange(cursor->curAddr.pageId, 0, numSlots, cursor->redoCtx);
    // the root has been modified, update its version
    ++rootHdr->version;
    return STATUS_OK_INTER;
}

static StatusInter BTreeSplitRoot(const PageIdT newChildPageId, BTreeCursorT *cursor)
{
    int16_t splitPoint = 0;
    bool assignRight = false;
    StatusInter ret = BTreeDecideSplitPoint(cursor, &splitPoint, &assignRight);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: DecideSplitPoint unsuc.");
        return ret;
    }

    BTreeInitPage(false, cursor->prevAddr.page);
    BTreeInitPage(false, cursor->nextAddr.page);
    // update statistics; "2" for the new two children of the root
    cursor->stat.occupiedMemorySizeChange += (uint32_t)((sizeof(PageHeadT) + sizeof(BTreeNodeHdrT)) * 2);
    BTreeNodeHdrT *rootHdr = BTreeGetNodeHdr(cursor->curAddr.page);
    BTreeNodeHdrT *leftChildHdr = BTreeGetNodeHdr(cursor->prevAddr.page);
    BTreeNodeHdrT *rightChildHdr = BTreeGetNodeHdr(cursor->nextAddr.page);
    leftChildHdr->level = rightChildHdr->level = rootHdr->level;

    // `rootHdr->level != BTREE_LEAF_LEVEL` means the root was not a leaf;
    // in this case, we need to set correctly the pointer to child; specifically, here we set
    // the slot to point to the new child; the pointer to old child shall be inserted later
    PageIdT oldChildPageId = SE_INVALID_PAGE_ADDR;
    if (rootHdr->level != BTREE_LEAF_LEVEL) {
        BTreeEntryHdrT *entryHdr = NULL;
        ret = BTreeGetEntryHdr(cursor->curAddr.page, cursor->slot, &entryHdr);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "BTree: get entryHdr when BTreeSplitRoot.");
            return ret;
        }
        oldChildPageId = entryHdr->childPageId;
        entryHdr->childPageId = newChildPageId;
        // write redo log
        BTreeRedoForUpdateChildId(cursor->curAddr.pageId, cursor->slot, newChildPageId, cursor->redoCtx);
    }
    ret = RedoForTransferInSplitRoot(cursor, splitPoint, rootHdr, leftChildHdr, rightChildHdr);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    // the root was a leaf; in this case, the two new children (which are leaves) need to be linked
    if (rootHdr->level == BTREE_LEAF_LEVEL) {
        leftChildHdr->prevPageId = SE_INVALID_PAGE_ADDR;
        leftChildHdr->nextPageId = cursor->nextAddr.pageId;
        rightChildHdr->prevPageId = cursor->prevAddr.pageId;
        rightChildHdr->nextPageId = SE_INVALID_PAGE_ADDR;
        // write redo log
        BTreeRedoForUpdateLeafLink(
            cursor->nextAddr.pageId, rightChildHdr->prevPageId, rightChildHdr->nextPageId, cursor->redoCtx);
        BTreeRedoForUpdateLeafLink(
            cursor->prevAddr.pageId, leftChildHdr->prevPageId, leftChildHdr->nextPageId, cursor->redoCtx);
    }

    // update root's level
    rootHdr->level += 1;

    // after split, insert the entry, which should have been inserted into the root, to the child
    ret = BTreeInsertToChildAfterSplitRoot(cursor, rootHdr, oldChildPageId, assignRight, splitPoint);
    if (ret != STATUS_OK_INTER) {
        // unable to insert to child when split root.
        SE_ERROR(ret, "BTree: insert to child.");
        return ret;
    }
    // reset the root after split
    return BTreeInitRootAfterSplitRoot(cursor, rootHdr);
}

static StatusInter BTreeSplitRoot4SplitNextAndPreMain(BTreeCursorT *cursor, PreAllocSplitNodeT *newNodes,
    PageIdT newChildPageId, const BTreeKeyT *lastKey, int16_t removeSlot)
{
    // for convenience, we use `cursor->prevAddr` and `cursor->nextAddr` to
    // keep the addresses of the two new children of the root
    StatusInter ret = STATUS_OK_INTER;
    cursor->prevAddr = newNodes[cursor->ancSize - 1].addr;
    cursor->nextAddr = newNodes[cursor->ancSize].addr;
    newNodes[cursor->ancSize - 1].isUsed = true;
    newNodes[cursor->ancSize].isUsed = true;
    if (cursor->ancSize == 2) {  // 2 here means if current ancSize is 2, need to split root with update
        if ((ret = BTreeSplitRootForDistribute(newChildPageId, cursor, lastKey, removeSlot)) != STATUS_OK_INTER) {
            SE_ERROR(ret, "BTree: SplitRoot4Merge: Split root");
        }
        return ret;
    }
    ret = BTreeSplitRoot(newChildPageId, cursor);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: SplitRoot4Merge: Split root");
    }
    return ret;
}

static StatusInter BTreeInsertRoot4SplitNextAndPreMain(
    BTreeCursorT *cursor, PageIdT newChildPageId, PageIdT oldChildPageId)
{
    // otherwise, it is enough to simply insert the key to the node
    BTreeEntryHdrT *entryHdr = NULL;
    StatusInter ret = BTreeGetEntryHdr(cursor->curAddr.page, cursor->slot, &entryHdr);
    if (ret != STATUS_OK_INTER) {
        // unable to get entry when insert root for split next and pre
        SE_LAST_ERROR(ret, "BTree: Get entry.");
        return ret;
    }
    entryHdr->childPageId = newChildPageId;
    // write redo log
    BTreeRedoForUpdateChildId(cursor->curAddr.pageId, cursor->slot, newChildPageId, SeGetCurRedoCtx());

    BTreeEntryHdrT newEntryHdr = InitializeEntryHdr(&cursor->key, 0, oldChildPageId);
    PageIdT bigKeyPageId = SE_INVALID_PAGE_ADDR;
    ret = ChangeKeyData4BigKeyIfNeed(cursor, &bigKeyPageId);
    if (ret != STATUS_OK_INTER) {
        // unable to change key data when insert root for split next or pre
        SE_ERROR(ret, "BTree: change key data");
        return ret;
    }
    if (newEntryHdr.isBigKey) {
        cursor->key.keyData = (uint8_t *)&bigKeyPageId;
    }
    BTreeInsertToNode(cursor->slot, &cursor->key, &newEntryHdr, cursor->curAddr.page);
    // write redo log
    BTreeRedoForInsert(cursor->curAddr.pageId, cursor->slot, &cursor->key, &newEntryHdr, SeGetCurRedoCtx());
    if (newEntryHdr.isBigKey) {
        cursor->key.keyData = NULL;
    }
    return STATUS_OK_INTER;
}

static StatusInter SplitNextAndPreNode(
    BTreeCursorT *cursor, BTreeKeyT lastKey, PageIdT oldChildPageId, PageIdT newChildPageId, size_t ancIndex)
{
    StatusInter ret = STATUS_OK_INTER;
    // 2 means if current level is 2(h is 3), need to update key, but key can't insert in node, need to split parent
    if (ancIndex == cursor->ancSize - 2) {
        ret = BTreeSplitNodeForNextAndPre(cursor);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Split node next or pre");
            return ret;
        }
        UpdateParentNodeParamsT updateMaxValParam = {.targetKey = lastKey, .targetPageId = oldChildPageId};
        bool finishSplit = false;
        ret = UpdateParentNode(cursor, &cursor->curAddr, &cursor->nextAddr, &updateMaxValParam, &finishSplit);
    } else {
        ret = BTreeSplitNode(newChildPageId, NULL, cursor);
    }
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTreeSplitNextAndPreMain go wrong");
        return ret;
    }
    return STATUS_OK_INTER;
}

static StatusInter BTreeSplitNextAndPreMain(BTreeCursorT *cursor, PreAllocSplitNodeT *newNodes, bool splitRoot,
    BTreePageAddrT *lastKeyChangePageAddr, PageIdT rightPageId)
{
    BTreeKeyT lastKey = {0};
    BTreeKeyT tmpKey = {0};
    PageIdT newChildPageId = rightPageId;
    PageIdT oldChildPageId = lastKeyChangePageAddr->pageId;
    StatusInter ret = BtreeUpdateCursor4SplitNextAndPreMain(cursor, lastKeyChangePageAddr, &lastKey);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    bool finishSplit = false;
    int16_t removeSlot = 0;
    UpdateParentNodeParamsT updateMaxValParam = {.targetKey = lastKey, .targetPageId = oldChildPageId};
    ret = UpdateParentNodeVal(cursor, cursor->curAddr, &updateMaxValParam, &removeSlot, &finishSplit);
    if (ret != STATUS_OK_INTER || finishSplit) {
        goto EXIT;
    }

    for (size_t i = cursor->ancSize - 2; i > 0; --i) {
        cursor->nextAddr = newNodes[i].addr;
        newNodes[i].isUsed = true;
        ret = SplitNextAndPreNode(cursor, lastKey, oldChildPageId, newChildPageId, i);
        if (ret != STATUS_OK_INTER) {
            goto EXIT;
        }
        BTreeFreeKeyDataAndResetCursorKeyData(cursor, &tmpKey);
        // set cursor->key to the key to be posted on the parent
        ret = UpdateCursorAfterSplit(cursor, &oldChildPageId, &newChildPageId, i, &tmpKey);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "BTreeSplitNextAndPreMain go wrong after split");
            goto EXIT;
        }
    }

    // the top latched node is the root and it needs to be split
    if (splitRoot) {
        ret = BTreeSplitRoot4SplitNextAndPreMain(cursor, newNodes, newChildPageId, &lastKey, removeSlot);
        goto EXIT;
    }

    ret = BTreeInsertRoot4SplitNextAndPreMain(cursor, newChildPageId, oldChildPageId);
EXIT:
    BTreeFreeKeyDataAndResetCursorKeyData(cursor, &lastKey);
    BTreeFreeKeyDataAndResetCursorKeyData(cursor, &tmpKey);
    return ret;
}

static StatusInter MergeToNextNode(
    BTreeCursorT *cursor, PreAllocSplitNodeT *newNodes, bool splitRoot, RedistributeNodeT *redistributeNode)
{
    // 获取要插入的结点的下一个结点的页逻辑addr与虚拟addr指针
    BTreeNodeHdrT *nodeHdr = BTreeGetNodeHdr(cursor->curAddr.page);
    PageIdT nextPageId = nodeHdr->nextPageId;
    // only leaf page has next page ,so level is always 0
    StatusInter interErrno = BTreeGetPage(cursor->pageMgr, nextPageId, 0, &(cursor->nextAddr.page), true);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "GetPage when move to nxtNode.");
        return interErrno;
    }
    // 把要插入的节点的一部分数据移动到下一个节点
    interErrno = BTreeRedistributeNext(cursor, redistributeNode);
    if (interErrno != STATUS_OK_INTER || !redistributeNode->mergeToNext) {
        SeLeavePage(cursor->pageMgr, nextPageId, false);
        return interErrno;
    }
    // update the parent node and start split.
    BTreePageAddrT lastKeyChangePageAddr = {.pageId = cursor->curAddr.pageId, .page = cursor->curAddr.page};
    interErrno = BTreeSplitNextAndPreMain(cursor, newNodes, splitRoot, &lastKeyChangePageAddr, nextPageId);
    if (interErrno != STATUS_OK_INTER) {
        // unable to split when merge to next
        SE_ERROR(interErrno, "BTree: split.");
        SeLeavePage(cursor->pageMgr, nextPageId, false);
        return interErrno;
    }
    SeLeavePage(cursor->pageMgr, nextPageId, true);
    redistributeNode->mergeFinished = true;
    return interErrno;
}

static void CleanSplitNode(BTreeCursorT *cursor, PreAllocSplitNodeT *newNodes, size_t allocCnt)
{
    const uint32_t spaceId = cursor->idxCtx->idxMetaCfg.tableSpaceIndex;
    for (size_t i = 1; i <= allocCnt; ++i) {
        if (newNodes[i].addr.page == NULL) {
            continue;
        }
        BTreePageWUnlatch(cursor->idxCtx, newNodes[i].addr.page);
        SeLeavePage(cursor->pageMgr, newNodes[i].addr.pageId, true);

        if (!newNodes[i].isUsed) {
            FreePageParamT freePageParam = {.spaceId = spaceId,
                .addr = newNodes[i].addr.pageId,
                .dbInstance = cursor->redoCtx->redoMgr->seIns->dbInstance,
                .labelRsmUndo = NULL};
            SeInitCachePagePara(&freePageParam.cachePagePara);
            StatusInter interErrno = SeFreePage(cursor->pageMgr, &freePageParam);
            if (interErrno != STATUS_OK_INTER) {
                SE_ERROR(interErrno, "BTree: free page");
                // no need return err, we should continue free other pages.
            }
        }
    }
}

static StatusInter CalculateSplitPreSize(const BTreeCursorT *cursor, int16_t *splitPoint,
    RedistributeNodeT *redistributeNode, uint8_t *prePage, bool *splitRight)
{
    PageHeadT *curPageHdr = (PageHeadT *)(void *)cursor->curAddr.page;
    size_t requiredSize = cursor->key.keySize + sizeof(uint16_t) + sizeof(BTreeEntryHdrT);
    const int16_t insertPos = cursor->slot;
    size_t moveSize = 0;
    uint16_t prePageFreeSize = ((PageHeadT *)(void *)prePage)->freeSize;
    for (int16_t i = 0; i < insertPos; ++i) {
        BTreeEntryHdrT *entryHdr = NULL;
        StatusInter ret = BTreeGetEntryHdr(cursor->curAddr.page, i, &entryHdr);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "BTree: get entryHdr.");
            return ret;
        }
        moveSize += entryHdr->size + sizeof(uint16_t) + sizeof(BTreeEntryHdrT);
        if (curPageHdr->freeSize + moveSize >= requiredSize && moveSize <= prePageFreeSize) {
            *splitPoint = i + 1;
            *splitRight = true;
            return STATUS_OK_INTER;
        } else if (curPageHdr->freeSize + moveSize >= requiredSize && moveSize > prePageFreeSize) {
            redistributeNode->mergeToPre = false;
            *splitRight = false;
            return STATUS_OK_INTER;
        }
    }

    moveSize += requiredSize;
    if (moveSize <= prePageFreeSize) {
        *splitPoint = insertPos;
        *splitRight = false;
        return STATUS_OK_INTER;
    }

    redistributeNode->mergeToPre = false;
    *splitRight = false;
    return STATUS_OK_INTER;
}

StatusInter BTreeTransferToPrePage(BTreeCursorT *cursor, int16_t low, int16_t high, uint8_t *srcPage, uint8_t *destPage)
{
    int16_t beginInsertSlot = BTreeGetNodeHdr(destPage)->numSlots;
    PageHeadT *srcPageHdr = (PageHeadT *)(void *)srcPage;
    BTreeNodeHdrT *srcNodeHdr = BTreeGetNodeHdr(srcPage);
    // we transfer part key of srcNodeHdr
    if (!(0 <= low && low <= high && high <= srcNodeHdr->numSlots)) {
        SE_LAST_ERROR(INTERNAL_ERROR_INTER, "BTree: slot nums exceed");
        return INTERNAL_ERROR_INTER;
    }
    StatusInter interErrno = STATUS_OK_INTER;
    uint32_t propeNum =
        BTreeGetIndexPropeNum(cursor->idxCtx->idxOpenCfg.vlIndexLabel, cursor->idxCtx->idxMetaCfg.keyDataType);
    for (int16_t i = low; i < high; ++i) {
        BTreeEntryHdrT *entryHdr = NULL;
        interErrno = BTreeGetEntryHdr(srcPage, i, &entryHdr);
        if (interErrno != STATUS_OK_INTER) {
            SE_ERROR(interErrno, "BTree: get entryHdr");
            return interErrno;
        }
        BTreeKeyT moveKey = {0};
        interErrno = BTreeGetKey(cursor->idxCtx, srcPage, i, propeNum, true, &moveKey);
        if (interErrno != STATUS_OK_INTER) {
            // unable to get key when transfer to pre page
            SE_ERROR(interErrno, "Get key, pageId:%" PRIu32 ", %" PRIu32 ", slot: %" PRId16, srcPageHdr->addr.deviceId,
                srcPageHdr->addr.blockId, i);
            return interErrno;
        }
        BTreeInsertToNode(beginInsertSlot + i - low, &moveKey, entryHdr, destPage);
        srcPageHdr->freeSize += (uint16_t)(moveKey.keySize + sizeof(BTreeEntryHdrT) + sizeof(uint16_t));
        BTreeRedoForTransferAppend(&moveKey, entryHdr, SeGetCurRedoCtx());
    }

    srcNodeHdr->leftBoundary = (uint16_t)(srcPageHdr->endPos - sizeof(uint16_t));
    // after transfer left range
    for (int16_t i = high; i < srcNodeHdr->numSlots; ++i) {
        if (srcNodeHdr->slots[i] < srcNodeHdr->leftBoundary) {
            srcNodeHdr->leftBoundary = srcNodeHdr->slots[i];
        }
    }

    int16_t needMoveSlots = srcNodeHdr->numSlots - high;
    // the size of the part of the slot array that needs to be moved
    const size_t moveSize = sizeof(uint16_t) * needMoveSlots;
    (void)memmove_s(srcNodeHdr->slots + low, moveSize, srcNodeHdr->slots + high, moveSize);

    srcNodeHdr->numSlots = needMoveSlots + low;
    return interErrno;
}

static StatusInter BTreeRedistributePre(BTreeCursorT *cursor, RedistributeNodeT *redistributeNode)
{
    BTreeNodeHdrT *nodeHdr = BTreeGetNodeHdr(cursor->curAddr.page);
    PageIdT opPageId = nodeHdr->prevPageId;
    uint8_t *opPageData = cursor->prevAddr.page;
    int16_t splitPoint = 0;
    bool right = false;
    StatusInter ret = CalculateSplitPreSize(cursor, &splitPoint, redistributeNode, opPageData, &right);
    if (ret != STATUS_OK_INTER) {
        // unable to cal split pre size when redistribute pre
        SE_ERROR(ret, "BTree: cal split preSize");
        return ret;
    }
    if (!redistributeNode->mergeToNext && !redistributeNode->mergeToPre) {
        return STATUS_OK_INTER;
    }

    // Here has decided the splitPoint, the position from 0 to splitPoint.
    BTreeNodeHdrT *preNodeHdr = BTreeGetNodeHdr(opPageData);
    TransInitArgsT transInitArgs = {false, nodeHdr->level, splitPoint};
    BTreeRedoForTransferInsertInit(opPageId, transInitArgs, preNodeHdr->numSlots, SeGetCurRedoCtx());

    // transfer the nodes from the old node to pre node BTreeRedoForTransferInsertInit
    ret = BTreeTransferToPrePage(cursor, 0, splitPoint, cursor->curAddr.page, opPageData);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: unable to transfer pre when redistribute pre");
        return ret;
    }
    // BTreeRedoForDeleteLeftRange write redo log for delete left range
    BTreeRedoForDeleteLeftRange(cursor->curAddr.pageId, 0, splitPoint, SeGetCurRedoCtx());
    ++nodeHdr->version;

    BTreeEntryHdrT newEntryHdr = InitializeEntryHdr(&cursor->key, 0, SE_INVALID_PAGE_ADDR);
    PageIdT bigKeyPageId = SE_INVALID_PAGE_ADDR;
    ret = ChangeKeyData4BigKeyIfNeed(cursor, &bigKeyPageId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: unable to change key data when redistribute pre");
        return ret;
    }
    if (newEntryHdr.isBigKey) {
        cursor->key.keyData = (uint8_t *)&bigKeyPageId;
    }
    if (right) {
        BTreeInsertToNode(cursor->slot - splitPoint, &cursor->key, &newEntryHdr, cursor->curAddr.page);
        BTreeRedoForInsert(
            cursor->curAddr.pageId, cursor->slot - splitPoint, &cursor->key, &newEntryHdr, SeGetCurRedoCtx());
    } else {
        // the preNodeHdr->numSlots maybe change after BTreeInsertToNode in prePage
        int16_t insertSlotPos = preNodeHdr->numSlots + cursor->slot - splitPoint;
        BTreeInsertToNode(insertSlotPos, &cursor->key, &newEntryHdr, opPageData);
        BTreeRedoForInsert(opPageId, insertSlotPos, &cursor->key, &newEntryHdr, SeGetCurRedoCtx());
    }
    if (newEntryHdr.isBigKey) {
        cursor->key.keyData = NULL;
    }

    return ret;
}

static StatusInter MergeToPreNode(
    BTreeCursorT *cursor, PreAllocSplitNodeT *newNodes, bool splitRoot, RedistributeNodeT *redistributeNode)
{
    // 获取要插入的结点的前一个结点的页逻辑addr与虚拟addr指针
    BTreeNodeHdrT *nodeHdr = BTreeGetNodeHdr(cursor->curAddr.page);
    PageIdT prePageId = nodeHdr->prevPageId;
    // only leaf page has prev page, so level is always 0
    StatusInter interErrno = BTreeGetPage(cursor->pageMgr, prePageId, 0, &(cursor->prevAddr.page), true);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "BTree: unable to getPage when move to pre node.");
        return interErrno;
    }
    // 把要插入的节点的一部分数据移动到前一个节点
    interErrno = BTreeRedistributePre(cursor, redistributeNode);
    if (interErrno != STATUS_OK_INTER || !redistributeNode->mergeToPre) {
        SeLeavePage(cursor->pageMgr, prePageId, false);
        return interErrno;
    }
    // update the parent node and start split.
    BTreePageAddrT lastKeyChangePageAddr = {.pageId = prePageId, .page = cursor->prevAddr.page};
    interErrno = BTreeSplitNextAndPreMain(cursor, newNodes, splitRoot, &lastKeyChangePageAddr, cursor->curAddr.pageId);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "BTree: unable to split when merge to pre.");
        SeLeavePage(cursor->pageMgr, prePageId, false);
        return interErrno;
    }
    SeLeavePage(cursor->pageMgr, prePageId, true);
    redistributeNode->mergeFinished = true;
    return interErrno;
}

/**
 * @brief Set stat for BTreeSplit.
 * @param[in,out] cursor  the cursor
 * @param[in] splitRoot   whether the root is split
 * @param[in] newNodeCnt  the number of newly allocated nodes
 */
static void BTreeSplitSetStat(BTreeCursorT *cursor, bool splitRoot, size_t newNodeCnt, bool isMergeToPreOrNext)
{
    // update statistics
    cursor->stat.recordCountChange = 1;
    if (splitRoot && newNodeCnt) {
        cursor->stat.treeHeightChange = 1;  // since the root was split, the height increases by 1
    }
    // `newNodeCnt` is the number of nodes newly allocated
    cursor->stat.totalNodeCountChange = (uint32_t)newNodeCnt;

    // if `newNodeCnt > 0`, leaf split must have happened; there are two cases:
    // (1) the original tree contained only one node (i.e., the root); after split, the # of leaves becomes 2
    // (2) otherwise, the # of leaves increases by 1
    if (newNodeCnt > 0) {
        cursor->stat.leafCountChange = 1;
    }
    // 如果是数据的前后移动 叶子节点一定没有新增 把leafCountChange改回0
    if (isMergeToPreOrNext) {
        cursor->stat.leafCountChange = 0;
    }
    // all remaining new nodes are internal nodes
    cursor->stat.internalNodeCountChange = (uint32_t)newNodeCnt - cursor->stat.leafCountChange;
    // split happened
    if (newNodeCnt > 0) {
        cursor->stat.splitCountChange = 1;
    }
}

static StatusInter AllocAndInitNewNodes(BTreeCursorT *cursor, PreAllocSplitNodeT **newNodes)
{
    SeInstanceT *seInstance = (SeInstanceT *)cursor->idxCtx->idxOpenCfg.seRunCtx->seIns;
    if (seInstance == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "BTree: seInstance");
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    uint32_t newNodesSize = BtreeGetHeightLimit(seInstance) * (uint32_t)sizeof(PreAllocSplitNodeT);
    PreAllocSplitNodeT *tmpNewNodes =
        DbDynMemCtxAlloc(cursor->idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx, newNodesSize);
    if (tmpNewNodes == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "BTree: alloc new nodes.");
        return MEMORY_OPERATE_FAILED_INTER;
    }
    (void)memset_s(tmpNewNodes, newNodesSize, 0, newNodesSize);
    *newNodes = tmpNewNodes;
    return STATUS_OK_INTER;
}

static void UpdateUsedCntAndStat(BTreeCursorT *cursor, RedistributeNodeT *redistributeNode,
    PreAllocSplitNodeT *newNodes, const bool splitRoot, size_t allocCnt)
{
    if (redistributeNode->mergeFinished) {
        size_t usedCnt = 0;
        for (size_t i = 1; i <= allocCnt; ++i) {
            if (newNodes[i].isUsed) {
                usedCnt++;
            }
        }
        // update statistics
        BTreeSplitSetStat(cursor, splitRoot, usedCnt, true);
    }
}

static StatusInter RedistributeNextOrPre(BTreeCursorT *cursor, RedistributeNodeT *redistributeNode)
{
    bool freeSizeEnough = false;
    StatusInter interErrno = BTreeInsertIsNodeFreeSizeEnough(cursor->ancAddr[0].page, &freeSizeEnough);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "BTree: unable to check size enough");
        return interErrno;
    }
    const bool splitRoot = BTreeIsRoot(cursor->ancAddr[0].page) && !freeSizeEnough;

    // leafNode doesn't need to split, so allocCnt = cursor->ancSize - 2
    size_t allocCnt = cursor->ancSize - 2;
    if (splitRoot) {
        allocCnt += 2;  // '2' here is the two nodes for splitting the root
    }

    // the number of newNodes is cursor->ancSize - 1 + 2, leafNode doesn't need to split.
    interErrno = BtreeCheckRedoLimit(cursor, allocCnt);
    if (interErrno != STATUS_OK_INTER) {
        return interErrno;
    }

    PreAllocSplitNodeT *newNodes = NULL;
    interErrno = AllocAndInitNewNodes(cursor, &newNodes);
    if (interErrno != STATUS_OK_INTER) {
        return interErrno;
    }

    // allocate new nodes
    interErrno = BTreeSplitAllocBlock(cursor, allocCnt, newNodes);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "BTree: unable to alloc block");
        DbDynMemCtxFree(cursor->idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx, newNodes);
        return interErrno;
    }

    if (redistributeNode->mergeToNext && !redistributeNode->mergeFinished) {
        // save the last key in current leafNode and the pageId of nextPage.
        interErrno = MergeToNextNode(cursor, newNodes, splitRoot, redistributeNode);
        if (interErrno != STATUS_OK_INTER) {
            goto EXIT;
        }
    }

    if (redistributeNode->mergeToPre && !redistributeNode->mergeFinished) {
        interErrno = MergeToPreNode(cursor, newNodes, splitRoot, redistributeNode);
        if (interErrno != STATUS_OK_INTER) {
            goto EXIT;
        }
    }
    UpdateUsedCntAndStat(cursor, redistributeNode, newNodes, splitRoot, allocCnt);
EXIT:
    // free resources
    CleanSplitNode(cursor, newNodes, allocCnt);
    DbDynMemCtxFree(cursor->idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx, newNodes);
    return STATUS_OK_INTER;
}

// release ancAddr and ancSlot
void BTreeReleaseAncestor(BTreeCursorT *cursor)
{
    if (cursor->ancAddr == NULL) {
        return;
    }
    DbDynMemCtxFree(cursor->idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx, cursor->ancAddr);
    cursor->ancAddr = NULL;
    cursor->ancSlot = NULL;
}

void BTreePesLatchCleanRes(BTreeCursorT *cursor, bool isCompleted)
{
    for (size_t i = 0; i < cursor->ancSize; ++i) {
        BTreePageWUnlatch(cursor->idxCtx, cursor->ancAddr[i].page);
        SeLeavePage(cursor->pageMgr, cursor->ancAddr[i].pageId, isCompleted);
    }
    BTreeReleaseAncestor(cursor);
    cursor->curAddr.page = NULL;
    cursor->state = CURSOR_INVALID;
}

static StatusInter CheckNodeFreeSizeEnough(
    BTreeCursorT *cursor, PageIdT opPageId, size_t requiredSize, bool *mergeToNode)
{
    if (DbIsPageIdValid(opPageId)) {
        uint8_t *opPage = NULL;
        // will leave page, so level is not important, set to 0
        StatusInter ret = BTreeGetPage(cursor->pageMgr, opPageId, 0, &opPage, false);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "BTree: getPage");
            return ret;
        }
        PageHeadT *opPageHdr = (PageHeadT *)(void *)opPage;
        if (opPageHdr->freeSize >= requiredSize) {
            *mergeToNode = true;
        }
        SeLeavePage(cursor->pageMgr, opPageId, false);
    }
    return STATUS_OK_INTER;
}

static void CheckNodesInOneParent(BTreeCursorT *cursor, RedistributeNodeT *redistributeNode)
{
    // Check whether two nodes are under the same parent node. the redistributeNode can be
    // (true, true)(true, false)(false, true)(false, false)
    if ((!redistributeNode->mergeToNext && !redistributeNode->mergeToPre)) {
        return;
    }
    // 2 means current level is penultimate level
    BTreePageAddrT ancAddr = cursor->ancAddr[cursor->ancSize - 2];
    int16_t ancSlot = cursor->ancSlot[cursor->ancSize - 2];
    BTreeNodeHdrT *ancNodeHdr = BTreeGetNodeHdr(ancAddr.page);
    if (redistributeNode->mergeToNext && (ancSlot >= 0 && ancSlot < ancNodeHdr->numSlots - 1)) {
        redistributeNode->mergeToPre = false;
    } else if (redistributeNode->mergeToPre && (ancSlot > 0 && ancSlot <= ancNodeHdr->numSlots - 1)) {
        redistributeNode->mergeToNext = false;
    } else {
        redistributeNode->mergeToNext = false;
        redistributeNode->mergeToPre = false;
    }
}

static StatusInter CheckMemorySufficientToMerge(BTreeCursorT *cursor, RedistributeNodeT *redistributeNode)
{
    BTreeNodeHdrT *nodeHdr = BTreeGetNodeHdr(cursor->curAddr.page);
    const size_t requiredSize = cursor->key.keySize + sizeof(uint16_t) + sizeof(BTreeEntryHdrT);
    StatusInter ret =
        CheckNodeFreeSizeEnough(cursor, nodeHdr->nextPageId, requiredSize, &redistributeNode->mergeToNext);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "check next node free size.");
        return ret;
    }
    ret = CheckNodeFreeSizeEnough(cursor, nodeHdr->prevPageId, requiredSize, &redistributeNode->mergeToPre);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "check pre node free size.");
        return ret;
    }
    // check the curNode, preNode or nextNode whether in same parent node, if not in same parent node,
    // it needs to reset redistributeNode. the vale of redistributeNode can be (true, true), (true, false),
    // (false, true), (false, false)
    CheckNodesInOneParent(cursor, redistributeNode);
    return STATUS_OK_INTER;
}

/**
 * @brief Update the statistics according to `cursor`.
 * @param[in] cursor  the cursor
 * @param[in] metaPageId  the page ID of the meta page (used for redo logging)
 * @param[in,out] meta  the meta information
 */
static void BTreeInsertUpdateStat(const BTreeCursorT *cursor, const PageIdT metaPageId, BTreeT *meta)
{
    meta->stat.bTreeIndex.recordCount += cursor->stat.recordCountChange;
    DB_ASSERT(cursor->stat.treeHeightChange <= 1);  // for debug only
    meta->stat.bTreeIndex.treeHeight += cursor->stat.treeHeightChange;

    BTreeMemStatT *memStat = &meta->stat.bTreeIndex.memStat;
    memStat->totalNodeCount += cursor->stat.totalNodeCountChange;
    memStat->leafCount += cursor->stat.leafCountChange;
    memStat->internalNodeCount += cursor->stat.internalNodeCountChange;
    memStat->totalMemorySize = memStat->pageSize * (uint64_t)memStat->totalNodeCount;
    memStat->occupiedMemorySize += cursor->stat.occupiedMemorySizeChange;
    memStat->bigKeyPageCount += cursor->stat.bigKeyPageCountChange;
    DB_ASSERT(memStat->totalNodeCount == memStat->leafCount + memStat->internalNodeCount);  // for debug only

    BTreePerfStatT *perfStat = &meta->stat.bTreeIndex.perfStat;
    // if the control flow enters this function, that means the insertion is successful
    perfStat->insertCount += 1;
    DB_ASSERT(cursor->stat.splitCountChange <= 1);  // for debug only
    perfStat->splitCount += cursor->stat.splitCountChange;
    perfStat->coalesceCount += cursor->stat.coalesceCountChange;

    BTreeRedoForUpdateStatistics(metaPageId, &meta->stat.bTreeIndex, cursor->redoCtx);
}

/**
 * @brief Actions to release resource in insert op.
 */
static inline Status BTreeInsertUnLachAndLeaveMeta(
    BTreeCursorT *cursor, BTreeT *meta, StatusInter interErrno, PageIdT metaPageId)
{
    if (cursor->rootAddr.page != NULL) {
        BTreeIndexWUnlatch(cursor->idxCtx, meta);
        SeLeavePage(cursor->pageMgr, metaPageId, interErrno != STATUS_OK_INTER);
        cursor->rootAddr.page = NULL;
    }
    return DbGetExternalErrno(interErrno);
}

static StatusInter BTreeInsertCheckUniqueness(IndexCtxT *idxCtx, BTreeCursorT *cursor, bool isRollback)
{
    StatusInter interErrno = STATUS_OK_INTER;
    if (cursor->isUnique && !isRollback) {
        interErrno = BTreeCheckUniqueness(idxCtx, cursor);
        if (interErrno != STATUS_OK_INTER) {
            BTreePageWUnlatch(cursor->idxCtx, cursor->curAddr.page);
            SeLeavePage(cursor->pageMgr, cursor->curAddr.pageId, false);
            SE_WARN(interErrno, "check uniqueness when insert");
            return interErrno;
        }
    }
    return interErrno;
}

static StatusInter BTreeDoCompareEqualKey(IndexCtxT *idxCtx, BTreeCursorT *cursor, bool isRollback)
{
    BTreeEntryHdrT *keyHdr = NULL;
    StatusInter ret = BTreeGetEntryHdr(cursor->curAddr.page, cursor->slot, &keyHdr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get entryHdr when compare equal key.");
        return ret;
    }
    if (keyHdr->isDeleted) {
        // delete then undo remove，unmark the entry
        keyHdr->isDeleted = 0;
        keyHdr->trxId = 0;
        keyHdr->rowId = cursor->key.rowId;
        // write redo log
        BTreeRedoForUnmark(cursor->curAddr.pageId, cursor->slot, keyHdr->rowId, 0, cursor->redoCtx);
        return STATUS_OK_INTER;
    }
    SE_LAST_ERROR(INTERNAL_ERROR_INTER, "BTree: TryInsert: existing key.");
    return INTERNAL_ERROR_INTER;
}

/**
 * @brief Try to insert the key `cursor->key` to the leaf of address `cursor->curAddr`.
 * @param[in] cursor      the cursor
 * @param[in] isRollback  whether it is in a rollback process
 * @param[out] split      whether split is needed in order to insert the key;
 *                        if split is needed, the leaf remains intact
 */
static StatusInter BTreeTryInsert(IndexCtxT *idxCtx, BTreeCursorT *cursor, bool isRollback, bool *split)
{
    if (cursor->cmpResultIsEq) {
        return BTreeDoCompareEqualKey(idxCtx, cursor, isRollback);
    }
    // the size required in order to insert the key into the leaf: the space includes
    // the key itself, the entry header and the slot
    const size_t requiredSize = BTreeCalculateEntrySize(cursor->key.isBigKey, cursor->key.keySize);
    uint16_t freeSize = 0;
    StatusInter ret = BTreePageGetFreeSize(cursor->curAddr.page, &freeSize);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Get free size");
        return ret;
    }
    // the page does not have sufficient space
    if (freeSize < requiredSize) {
        *split = true;
        return STATUS_OK_INTER;
    }

    BTreeEntryHdrT newEntryHdr = InitializeEntryHdr(&cursor->key, 0, SE_INVALID_PAGE_ADDR);
    PageIdT bigKeyPageId = SE_INVALID_PAGE_ADDR;
    ret = ChangeKeyData4BigKeyIfNeed(cursor, &bigKeyPageId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Change key data");
        return ret;
    }
    if (newEntryHdr.isBigKey) {
        cursor->key.keyData = (uint8_t *)&bigKeyPageId;
    }
    BTreeInsertToNode(cursor->slot, &cursor->key, &newEntryHdr, cursor->curAddr.page);
    BTreeRedoForInsert(cursor->curAddr.pageId, cursor->slot, &cursor->key, &newEntryHdr, cursor->redoCtx);
    if (newEntryHdr.isBigKey) {
        cursor->key.keyData = NULL;
    }
    // update statistics
    cursor->stat.recordCountChange = 1;
    cursor->stat.occupiedMemorySizeChange += (uint32_t)requiredSize;

    return STATUS_OK_INTER;
}

/**
 * @brief Insert the key `cursor->key` to the node of address `cursor->curAddr`.
 * @param cursor  the cursor
 * @param newChildPageId  the page ID of the new child; applicable only for non-leaf nodes
 */
static StatusInter BTreeInsertEntry(BTreeCursorT *cursor, PageIdT newChildPageId)
{
    BTreeNodeHdrT *nodeHdr = BTreeGetNodeHdr(cursor->curAddr.page);
    PageIdT oldChildPageId = SE_INVALID_PAGE_ADDR;
    StatusInter ret = STATUS_OK_INTER;
    // we are inserting an entry to a non-leaf node
    if (nodeHdr->level > 0) {
        BTreeEntryHdrT *entryHdr = NULL;
        ret = BTreeGetEntryHdr(cursor->curAddr.page, cursor->slot, &entryHdr);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Get entryHdr");
            return ret;
        }
        oldChildPageId = entryHdr->childPageId;
        entryHdr->childPageId = newChildPageId;
        // write redo log
        BTreeRedoForUpdateChildId(cursor->curAddr.pageId, cursor->slot, newChildPageId, cursor->redoCtx);
    }

    // generate the corresponding entry header
    BTreeEntryHdrT newEntryHdr =
        InitializeEntryHdr(&cursor->key, 0, nodeHdr->level != 0 ? oldChildPageId : SE_INVALID_PAGE_ADDR);
    PageIdT bigKeyPageId = SE_INVALID_PAGE_ADDR;
    ret = ChangeKeyData4BigKeyIfNeed(cursor, &bigKeyPageId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Change key data");
        return ret;
    }
    if (newEntryHdr.isBigKey) {
        cursor->key.keyData = (uint8_t *)&bigKeyPageId;
    }

    // we store the free size before inserting the key; it will be useful when we update statistics
    size_t oldFreeSize = ((PageHeadT *)(void *)cursor->curAddr.page)->freeSize;

    BTreeInsertToNode(cursor->slot, &cursor->key, &newEntryHdr, cursor->curAddr.page);
    // write redo log
    BTreeRedoForInsert(cursor->curAddr.pageId, cursor->slot, &cursor->key, &newEntryHdr, cursor->redoCtx);

    if (newEntryHdr.isBigKey) {
        cursor->key.keyData = NULL;
    }
    // the free size after insertion
    size_t newFreeSize = ((PageHeadT *)(void *)cursor->curAddr.page)->freeSize;

    // update statistics
    cursor->stat.occupiedMemorySizeChange += (uint32_t)(oldFreeSize - newFreeSize);
    return STATUS_OK_INTER;
}

static StatusInter BTreeSplitTopLatchedNode(
    BTreeCursorT *cursor, PreAllocSplitNodeT *newNodes, PageIdT newChildPageId, bool *splitRoot, size_t *newNodeIdx)
{
    uint16_t freeSize = 0;
    StatusInter ret = BTreePageGetFreeSize(cursor->curAddr.page, &freeSize);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Get free size when split main.");
        return ret;
    }
    // the top latched node is the root and it needs to be split
    if (BTreeIsRoot(cursor->curAddr.page) &&
        freeSize < BTreeCalculateEntrySize(cursor->key.isBigKey, cursor->key.keySize)) {
        // for convenience, we use `cursor->prevAddr` and `cursor->nextAddr` to
        // keep the addresses of the two new children of the root
        cursor->prevAddr = newNodes[*newNodeIdx].addr;
        *newNodeIdx = *newNodeIdx + 1;
        cursor->nextAddr = newNodes[*newNodeIdx].addr;
        *newNodeIdx = *newNodeIdx + 1;
        ret = BTreeSplitRoot(newChildPageId, cursor);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "BTreeSplitRoot in BTreeSplitMain");
            return ret;
        }

        // the root is split and thus we set *splitRoot* to true
        *splitRoot = true;
    } else {
        // otherwise, it is enough to simply insert the key to the node
        ret = BTreeInsertEntry(cursor, newChildPageId);
    }
    return ret;
}

/**
 * @brief This function is used in `BTreeSplit` to do the split.
 * @param[in] cursor        the cursor
 * @param[in] newNodes      the array of the addresses of new nodes
 * @param[in] nextLeafAddr  the address of the next leaf
 * @param[out] splitRoot    whether the root is split
 * @param[out] usedCnt      the leaf had been used
 * @return the actual number of new nodes used
 */
static StatusInter BTreeSplitMain(
    BTreeCursorT *cursor, PreAllocSplitNodeT *newNodes, BTreePageAddrT *nextLeafAddr, bool *splitRoot, size_t *usedCnt)
{
    // set `*splitRoot` to true only if the root actually splits
    // there is no enough space to insert the key to the node
    StatusInter ret = STATUS_OK_INTER;
    *splitRoot = false;
    *usedCnt = 0;
    PageIdT newChildPageId = SE_INVALID_PAGE_ADDR;
    BTreeKeyT tmpKey = {0};
    // `newNodeIdx` is the index of the next new node; it starts from 1 because `newNodes[0]` is invalid
    size_t newNodeIdx = 1;
    for (size_t i = cursor->ancSize - 1; i > 0; --i) {
        cursor->nextAddr = newNodes[newNodeIdx].addr;
        uint16_t freeSize = 0;
        ret = BTreePageGetFreeSize(cursor->curAddr.page, &freeSize);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "BTree: Get free size.");
            goto EXIT;
        }
        bool notEnoughSize = freeSize < BTreeCalculateEntrySize(cursor->key.isBigKey, cursor->key.keySize);
        if (notEnoughSize) {
            // split the node and then insert the key
            ret = BTreeSplitNode(newChildPageId, nextLeafAddr, cursor);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "BTreeSplit in loop, size:%" PRIu32 "", notEnoughSize);
                goto EXIT;
            }
            // move to the next new block
            ++newNodeIdx;
        } else {
            // just insert the key
            ret = BTreeInsertEntry(cursor, newChildPageId);
            // no need to split more; just return
            goto EXIT;
        }
        // set cursor->key to the key to be posted on the parent
        BTreeFreeKeyDataAndResetCursorKeyData(cursor, &tmpKey);
        if ((ret = UpdateCursorAfterSplit(cursor, NULL, &newChildPageId, i, &tmpKey)) != STATUS_OK_INTER) {
            goto EXIT2;
        }
    }

    ret = BTreeSplitTopLatchedNode(cursor, newNodes, newChildPageId, splitRoot, &newNodeIdx);
EXIT:
    BTreeFreeKeyDataAndResetCursorKeyData(cursor, &tmpKey);
EXIT2:
    *usedCnt = newNodeIdx - 1;
    return ret;
}

static void CleanSplitNodeSource(BTreeCursorT *cursor, PreAllocSplitNodeT *newNodes, size_t allocCnt, size_t usedCnt)
{
    uint32_t spaceId = cursor->idxCtx->idxMetaCfg.tableSpaceIndex;
    // the new nodes in the range [1, usedCnt] are used
    for (size_t i = 1; i <= allocCnt; ++i) {
        BTreePageWUnlatch(cursor->idxCtx, newNodes[i].addr.page);
        SeLeavePage(cursor->pageMgr, newNodes[i].addr.pageId, i <= usedCnt);
        // the remaining nodes are not used, so we need free
        if (i > usedCnt) {
            FreePageParamT freePageParam = {.spaceId = spaceId,
                .addr = newNodes[i].addr.pageId,
                .dbInstance = cursor->redoCtx->redoMgr->seIns->dbInstance,
                .labelRsmUndo = NULL};
            SeInitCachePagePara(&freePageParam.cachePagePara);
            (void)SeFreePage(cursor->pageMgr, &freePageParam);
        }
    }
}
static StatusInter GetAndWLatchNextPage(BTreeCursorT *cursor, BTreePageAddrT *nextLeafAddr)
{
    BTreeNodeHdrT *nodeHdr = BTreeGetNodeHdr(cursor->curAddr.page);
    if (DbIsPageIdValid(nodeHdr->nextPageId)) {
        nextLeafAddr->pageId = nodeHdr->nextPageId;
        StatusInter interErrno = BTreeGetPage(cursor->pageMgr, nextLeafAddr->pageId, 0, &nextLeafAddr->page, true);
        if (interErrno != STATUS_OK_INTER) {
            SE_ERROR(interErrno, "BTree: unable to getPage.");
            return interErrno;
        }
        BTreePageWLatch(cursor->idxCtx, nextLeafAddr->page);
    }
    return STATUS_OK_INTER;
}

static StatusInter BTreeCalculateNeedPageCntAndCheck(BTreeCursorT *cursor, size_t *tryAllocCount)
{
    // `allocCnt` is the number of new nodes to allocate; all nodes in the range [1, `cursor->ancSize` - 1]
    // are not safe (i.e., almost full); therefore, they *may* need to be split
    bool freeSizeEnough = false;
    StatusInter interErrno = BTreeInsertIsNodeFreeSizeEnough(cursor->ancAddr[0].page, &freeSizeEnough);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "BTree: check free size is enough.");
        return interErrno;
    }

    size_t allocCnt = cursor->ancSize - 1;
    // does the root need to be split in the worst case?
    if (BTreeIsRoot(cursor->ancAddr[0].page) && !freeSizeEnough) {
        allocCnt += 2;  // '2' here is the two nodes for splitting the root
    }
    *tryAllocCount = allocCnt;
    return BtreeCheckRedoLimit(cursor, allocCnt);
}
/**
 * @brief All nodes that need to be split are W-latched and recorded in `cursor`; split them in this function.
 * @param[in] cursor  the cursor
 */
static StatusInter BTreeSplit(BTreeCursorT *cursor)
{
    DB_ASSERT(cursor->ancSize >= 1);  // for debug only

    // the nodes in cursor->ancAddr may need to be split; therefore, in the worst case,
    // we need to allocate (cursor->ancSize + 2) new nodes, where 2 is for splitting the root
    void *sessionMemCtx = cursor->idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx;
    size_t mallocNodeSize = (cursor->ancSize + 2) * sizeof(PreAllocSplitNodeT);
    PreAllocSplitNodeT *newNodes = DbDynMemCtxAlloc(sessionMemCtx, mallocNodeSize);
    if (newNodes == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "BTree: alloc memCtx, size:%" PRIu64, (uint64_t)mallocNodeSize);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    (void)memset_s(newNodes, mallocNodeSize, 0, mallocNodeSize);
    size_t allocCnt = 0;
    StatusInter interErrno = BTreeCalculateNeedPageCntAndCheck(cursor, &allocCnt);
    if (interErrno != STATUS_OK_INTER) {
        DbDynMemCtxFree(sessionMemCtx, newNodes);
        return interErrno;
    }
    BTreePageAddrT nextLeafAddr = {.pageId = SE_INVALID_PAGE_ADDR, .page = NULL};
    // if `cursor->ancSize` > 1, the leaf of address `cursor->curAddr` is not safe (almost full)
    // and needs to be split; in that case, the linked list of leaves need to be updated;
    // `nextLeafAddr` is used to store the address of the successor of the current leaf
    if (cursor->ancSize > 1) {
        interErrno = GetAndWLatchNextPage(cursor, &nextLeafAddr);
        if (interErrno != STATUS_OK_INTER) {
            DbDynMemCtxFree(sessionMemCtx, newNodes);
            return interErrno;
        }
    }
    // pre-allocate new nodes; we do so because if allocation fails during the split process, there is no easy
    // way to rollback the split process; therefore, we allocate enough nodes in advance;
    // *** it is better to provide a reservation function which reserves enough space in disk so that
    // *** we can defer the allocation of blocks and the allocation will always succeed
    interErrno = BTreeSplitAllocBlock(cursor, allocCnt, newNodes);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "BTree: alloc block when split");
        DbDynMemCtxFree(sessionMemCtx, newNodes);
        if (nextLeafAddr.page != NULL) {
            BTreePageWUnlatch(cursor->idxCtx, nextLeafAddr.page);
            SeLeavePage(cursor->pageMgr, nextLeafAddr.pageId, false);
        }
        return interErrno;
    }

    // do the split; `usedCnt` is the number of new nodes actually used;
    // `splitRoot` indicates whether the root is split
    bool splitRoot = false;
    size_t usedCnt = 0;
    interErrno = BTreeSplitMain(cursor, newNodes, &nextLeafAddr, &splitRoot, &usedCnt);
    // free resources
    if (nextLeafAddr.page != NULL) {
        BTreePageWUnlatch(cursor->idxCtx, nextLeafAddr.page);

        // if `usedCnt > 0`, that means the leaf is split and `nextLeaf` must have been modified (the leaf link);
        // otherwise, `nextLeaf` remains unchanged
        SeLeavePage(cursor->pageMgr, nextLeafAddr.pageId, usedCnt > 0);
    }

    CleanSplitNodeSource(cursor, newNodes, allocCnt, usedCnt);

    DbDynMemCtxFree(sessionMemCtx, newNodes);
    // update statistics
    BTreeSplitSetStat(cursor, splitRoot, usedCnt, false);  // when BTreeSplitMain go wrong the usedCnt will equal to 0.
    return interErrno;
}

/**
 * @brief Insert the key `cursor->key` to the tree.
 * @param[in] cursor  `cursor->key` is the key to insert
 */
static StatusInter BTreePesInsert(BTreeCursorT *cursor)
{
    // all nodes that may need to be split are latched after calling `BTreePesLatchCoupling`
    StatusInter interErrno = BTreePesLatchCoupling(cursor, true);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "pesLatch coupling when pes Insert.");
        return interErrno;
    }

    // check the free size in pre or next node is enough to push one node
    RedistributeNodeT redistributeNode = {.mergeToPre = false, .mergeToNext = false, .mergeFinished = false};
    interErrno = CheckMemorySufficientToMerge(cursor, &redistributeNode);
    if (interErrno != STATUS_OK_INTER) {
        BTreePesLatchCleanRes(cursor, false);
        SE_ERROR(interErrno, "check memory sufficient pes Insert.");
        return interErrno;
    }
    if (redistributeNode.mergeToNext || redistributeNode.mergeToPre) {
        interErrno = RedistributeNextOrPre(cursor, &redistributeNode);
        if (interErrno != STATUS_OK_INTER) {
            BTreePesLatchCleanRes(cursor, false);
            SE_ERROR(interErrno, "redistribute when pes Insert.");
            return interErrno;
        }
    }
    if (!redistributeNode.mergeFinished && !redistributeNode.mergeToNext && !redistributeNode.mergeToPre) {
        // split and insert
        interErrno = BTreeSplit(cursor);
    }

    // clean resources
    BTreePesLatchCleanRes(cursor, true);

    return interErrno;
}

/**
 * @brief Try to insert the key `cursor->key` to the tree assuming no split is needed.
 * @param[in] cursor      the cursor
 * @param[in] isRollback  whether it is called in a rollback process
 * @param[out] split      whether split is needed in order to insert the key
 */
static StatusInter BTreeOptInsert(IndexCtxT *idxCtx, BTreeCursorT *cursor, bool isRollback, bool *split)
{
    // try to insert the key
    StatusInter ret = BTreeTryInsert(idxCtx, cursor, isRollback, split);
    BTreePageWUnlatch(cursor->idxCtx, cursor->curAddr.page);
    // if `*split` is true, the page is not modified;
    // if `ret` does not equal `STATUS_OK_INTER`, the page is also not modified
    SeLeavePage(cursor->pageMgr, cursor->curAddr.pageId, (!*split && ret == STATUS_OK_INTER));
    cursor->curAddr.page = NULL;
    cursor->state = CURSOR_INVALID;

    return ret;
}
static StatusInter LocatePositionAndCheckUniqueness(IndexCtxT *idxCtx, BTreeCursorT *cursor, bool isRollback)
{
    StatusInter interErrno = BTreeOptLatchCoupling(cursor, true);
    if (interErrno != STATUS_OK_INTER) {
        return interErrno;
    }
    return BTreeInsertCheckUniqueness(idxCtx, cursor, isRollback);
}
/**
 * @brief Internal function to insert a (`key`, `rowId`) pair to the tree.
 * @param[in] idxCtx     the index runtime context
 * @param[in] insertKey  the key to insert
 * @param[in] rowId      the heap tuple ID
 * @param[in] isRollback whether the function is called in a rollback process
 */
Status BTreeInsert(IndexCtxT *idxCtx, IndexKeyT insertKey, HpTupleAddr rowId, bool isRollback)
{
    BTreeCursorT cursor;
    StatusInter undoErrno = STATUS_OK_INTER;
    BTreeCursorSet(&cursor, idxCtx, insertKey, CURSOR_INSERT);
    // read the meta page
    const PageIdT metaPageId = *(PageIdT *)(&idxCtx->idxShmAddr);
    uint8_t *metaPage = NULL;
    StatusInter interErrno = BTreeGetPage(cursor.pageMgr, metaPageId, HIGHEST_PRIORITY_PAGE, &metaPage, true);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "BTree: getPage");
        return DbGetExternalErrno(interErrno);
    }
    BTreeT *meta = BTreeGetTreeHdr(metaPage);
    BTreeIndexWLatch(idxCtx, meta);
    cursor.key.rowId = rowId;
    cursor.trmId = meta->trmId;
    cursor.rootAddr.page = metaPage;
    interErrno = LocatePositionAndCheckUniqueness(idxCtx, &cursor, isRollback);
    if (interErrno != STATUS_OK_INTER) {
        goto ERR_LEAVE_RESOURCE;
    }
    bool split = false;
    // optimistic insertion; in index latch mode, optimistic insertion seems to bring no benefits
    interErrno = BTreeOptInsert(idxCtx, &cursor, isRollback, &split);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "BTree: unable to opt insert.");
        goto ERR_UNDO_LOG_INSERT;
    }

    // if `split` is true, it means optimistic insertion failed and node split is necessary;
    // therefore, we need to do a pessimistic insertion which is of higher cost
    if (split) {
        interErrno = BTreePesInsert(&cursor);
        if (interErrno != STATUS_OK_INTER) {
            SE_ERROR(interErrno, "BTree: unable to pes insert.");
            goto ERR_UNDO_LOG_INSERT;
        }
    }
    // update statistics
    BTreeInsertUpdateStat(&cursor, metaPageId, meta);

ERR_UNDO_LOG_INSERT:
    // write undo log; '1' means rollback by physical deletion
    undoErrno = BTreeWriteUndoInsert(idxCtx, &insertKey, rowId, 1, isRollback);
    if (undoErrno != STATUS_OK_INTER) {
        BTreeInsertUnLachAndLeaveMeta(&cursor, meta, interErrno, metaPageId);
        // 回滚BTreeIndexUndoInsert确保不失败（不返回这个异常），即如果回滚失败了不作其他操作
        BTreeIndexUndoInsert(idxCtx, insertKey, rowId);
        // 如果是插入异常则优先返回插入异常错误
        if (interErrno != STATUS_OK_INTER) {
            return DbGetExternalErrno(interErrno);
        }
        // 否则返回写undo日志错误
        SE_ERROR(undoErrno, "BTree: undoLog");
        return DbGetExternalErrno(undoErrno);
    }

ERR_LEAVE_RESOURCE:
    return BTreeInsertUnLachAndLeaveMeta(&cursor, meta, interErrno, metaPageId);
}

/**
 * @brief Insert a (`key`, `rowId`) pair to the tree.
 * @param[in] idxCtx     the index runtime context
 * @param[in] insertKey  the key to insert
 * @param[in] rowId      the heap tuple ID
 */
SO_EXPORT Status BTreeIndexInsert(IndexCtxT *idxCtx, IndexKeyT insertKey, HpTupleAddr rowId)
{
    DB_POINTER(idxCtx);
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    // 原子操作在外层开启，防止BTreeIndexUndoRemove调用BTreeInsert导致的原子操作嵌套
    RedoLogBegin(redoCtx);
    Status ret = BTreeInsert(idxCtx, insertKey, rowId, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "BTree: Unable to insert.");
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    return DbGetExternalErrno(RedoLogEnd(redoCtx, true));
}

SO_EXPORT Status BTreeIndexUndoRemove(IndexCtxT *idxCtx, IndexKeyT key, HpTupleAddr rowId)
{
    DB_POINTER2(idxCtx, key.keyData);
    return BTreeInsert(idxCtx, key, rowId, true);
}
