/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: declaration of some basic structs and functions for B-tree
 * Author: zhangyikai
 * Create: 2022-10-31
 */
#ifndef SE_BTREE_INDEX_BASE_INNER_H
#define SE_BTREE_INDEX_BASE_INNER_H

#include "se_redo.h"
#include "se_index_inner.h"
#include "dm_data_index.h"
#include "se_page_mgr.h"
#include "se_btree_index_base.h"
#include "db_last_error.h"
#include "se_index_common.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

// the maximum key length allowed
#define MAX_BTREE_KEY_LENGTH SE_IDX_MAX_KEY_LENGTH

// all leaves are at level 0; internal nodes have levels > 0
#define BTREE_LEAF_LEVEL 0

#define BTREE_INVALID_SLOT_ID (-1)

// Size of pages occupied by a large key.
#define DEVICE_NUMBER_FOR_BIGKEY (16)

#define BTREE_VALID_MAGIC_NUM 0xf379acbd    // 内存有效的魔术字(创建时标记)
#define BTREE_INVALID_MAGIC_NUM 0x4258adfc  // 内存无效的魔术字(释放时标记)

/**
 * `BTreeT` contains information about the tree.
 * Each B-tree contains exactly one `BTreeT`, which resides in the meta page.
 */
typedef struct BTree {
    IdxBaseT idxBase;       ///< base information
    uint32_t trmId;         ///< corresponding file ID for persistence
    uint32_t magicNum;      ///< magic number, check whether the the meta page is valid
    IndexStatisticsT stat;  ///< the statistics information about the tree
    // 非终端场景预留字段
#ifndef HARMONY_OS
    uint8_t reserve[32];
#endif
} BTreeT;

typedef struct RedistributeNode {
    bool mergeToPre;
    bool mergeToNext;
    bool mergeFinished;
} RedistributeNodeT;

/**
 * `BTreeNodeHdrT` is the header of a B-tree node.
 */
typedef struct BTreeNodeHdr {
    uint64_t version;    ///< node version
    PageIdT prevPageId;  ///< applicable only to leaves; the address of the predecessor in the leaf list
    PageIdT nextPageId;  ///< applicable only to leaves; the address of the successor in the leaf list

    int16_t numSlots;       ///< the number of slots in this node; we use `int16_t` instead of `uint16_t`
                            ///< because we use slot IDs of type `int16_t` s.t. arithmetic does not underflow
    uint16_t leftBoundary;  ///< it is guaranteed that all entries have offset >= leftBoundary inside page
    uint8_t level;          ///< the level in which this node resides; leaves are in level 0;
                            ///< the level number of a parent is one plus that of children
    uint8_t isRoot : 1;     ///< whether it is a root node
    uint8_t reserved : 7;   ///< reserved field
    // 非终端场景预留字段
#ifndef HARMONY_OS
    uint8_t reserve[8];
#endif
    uint16_t slots[];  ///< the offsets of data entries within the page
} BTreeNodeHdrT;

/**
 * `BTreeEntryHdrT` is the header of a B-tree entry.
 */
struct BTreeEntryHdr {
    union {
        uint64_t trxId;       ///< only applicable to leaves; the last transaction modifying this entry
        PageIdT childPageId;  ///< only applicable to internal nodes; pointer to child node
    };
    HpTupleAddr rowId;        /**< address of the tuple in the heap; it is also used for non-unique
                                    index to distinguish duplicate keys in both leaves and internal nodes */
    uint16_t size;            ///< the size of the key
    uint16_t isDeleted : 1;   ///< a flag for mark deletion
    uint16_t isBigKey : 1;    ///< a flag for mark the key is big or not
    uint16_t reserved1 : 13;  ///< reserved field
    // the following three members are used only in compaction
    uint16_t isActive : 1;   ///< a flag used in page compaction
    uint16_t prevActivePos;  ///< the position of the previous active entry
    int16_t slotId;          ///< the corresponding slot ID of this entry
};

/**
 * The key in current implementation is of the form (key, rowId).
 */
struct BTreeKey {
    uint8_t *keyData;  ///< the key
    uint8_t isBigKey;
    uint32_t keySize;         ///< the size of the key
    uint32_t actualSize;      ///< the size of actual big key size
    uint32_t prefixPropeNum;  ///< prefix property number
    HpTupleAddr rowId;        ///< the address of the corresponding tuple
};

/**
 * The address information about a page.
 */
typedef struct {
    PageIdT pageId;  ///< logical address
    uint8_t *page;   ///< physical address
} BTreePageAddrT;

typedef struct PreAllocSplitNode {
    BTreePageAddrT addr;
    bool isUsed;
} PreAllocSplitNodeT;

typedef struct NodeFreePara {
    BTreePageAddrT parentNode;  // 要释放的节点的父节点
    PageIdT childPageId;        // 要释放的节点
    int16_t slot;               // 要释放的节点在父节点中对应的slot
} NodeFreeParaT;

typedef enum { CURSOR_INSERT, CURSOR_DELETE, CURSOR_LOOKUP, CURSOR_SCAN } BTreeOpTypeE;

typedef enum { CURSOR_INVALID, CURSOR_VALID } BTreeCursorStateE;

enum {
    INDEX_CTX_KEY_LEFT_KEY,
    INDEX_CTX_KEY_RIGHT_KEY,
    INDEX_CTX_KEY_CURRENT_KEY,
    INDEX_CTX_KEY_NUM,
};

/**
 * The statistics of B-tree within an operation (e.g., insertion and deletion)
 */
typedef struct {
    uint32_t recordCountChange;         ///< how many records are affected (inserted or deleted)
    uint32_t treeHeightChange;          ///< how the tree height is affected
    uint32_t totalNodeCountChange;      ///< how many nodes are affected (created or freed)
    uint32_t leafCountChange;           ///< how many leaves are affected
    uint32_t coalesceCountChange;       ///< how many coalesce operations are performed(include borrow and merge)
    uint32_t internalNodeCountChange;   ///< how many internal nodes are affected
    uint32_t occupiedMemorySizeChange;  ///< how the occupied memory size changes (increase by or decrease by xxx)
    uint32_t splitCountChange;          ///< how many splits have been triggered
    uint32_t freeCountChange;           ///< how many empty node collections have been triggered
    int32_t bigKeyPageCountChange;      ///< how many big key page are affected
} BTreeStatPerOpT;

typedef enum {
    KEY_COMPARE_TYPE_FULL_MATCH,    // 全匹配比较
    KEY_COMPARE_TYPE_PREFIX_MATCH,  // 前缀匹配比较
    KEY_COMPARE_TYPE_BUTT
} KeyCompareTypeE;

/**
 * A cursor keeps information about the node being visited, including its ancestors, the path
 * from the root to it, the type of the operation, etc.
 */
typedef struct BTreeCursor {
    BTreeCursorStateE state;  ///< state of the cursor
    bool isUnique;            ///< whether the index is unique
    BTreeOpTypeE opType;      ///< operation type
    BTreeKeyT key;            ///< the key
    BTreePageAddrT rootAddr;  ///< the address of the root
    BTreePageAddrT curAddr;   ///< the address of the node currently being visited; usually is a leaf
    int16_t slot;             ///< the slot being visited
    BTreePageAddrT prevAddr;  ///< the predecessor of the current node
    BTreePageAddrT nextAddr;  ///< the successor of the current node
    uint64_t pageVersion;     ///< the version of the page pointed by the cursor
    // ancestor information
    BTreePageAddrT *ancAddr;  ///< an array of ancestors of the current node
    int16_t *ancSlot;         ///< an array of slot IDs, each corresponding to one in `ancAddr`
    bool *ancExistTarget;     ///< an array of bool value, whether every layer exist targe deleting node
    size_t ancSize;           ///< the size of the ancestor array
    // SE-related information
    IndexCtxT *idxCtx;     ///< the index context
    RedoRunCtxT *redoCtx;  ///< redo runtime context
    PageMgrT *pageMgr;     ///< page manager
    uint32_t trmId;        ///< fileId for persistence
    uint32_t spaceId;      ///< required in delete or drop free page
    // statistics
    BTreeStatPerOpT stat;  ///< the statistics information
    // nullDisable
    uint64_t nullDisableBitMap;  ///< the null disable bitmap of the index prope for scan,
                                 ///< 0x5 means prop_0 and prop_2 is null disabled
                                 ///< the bits is up to DM_MAX_KEY_PROPE_NUM
    bool cmpResultIsEq;          ///< use cmpResult from BTreeDecidePosition for BTreeTryInsert
    IdxScanModeE scanMode;
    BTreeKeyT bigKeyCache;  ///< record src big key info when try update big key
} BTreeCursorT;

/**
 * `BTreeScanItrT` is an iterator for scanning the B-tree.
 */
typedef struct {
    IndexScanDirectionE scanDirect;  ///< ascending or descending
    BTreeKeyT leftKey;               ///< the left end of the scan range
    BTreeKeyT rightKey;              ///< the right end of the scan range
    bool isLeftClosed;               ///< whether the left end is closed
    bool isRightClosed;              ///< whether the right end is closed
    bool compareLeftClosed;          ///< whether the left end is closed in comparison
    bool compareRightClosed;         ///< whether the right end is closed in comparison
    BTreeCursorT cursor;             ///< the cursor
    IndexRangeTypeE rangeType;       ///< [L, R], [L, R), (L, R], (L, R)
    bool isScanMove;                 ///< whether execute move after scanBegin
    IdxScanModeE scanMode;
} BTreeScanItrT;

typedef struct DmIndexKeyCmpInfo {
    bool cmpRowId;
    bool found;
    bool outOfRange;
    bool cmpEof;
    DmIndexKeyCmpResultE cmpResult;
    DmVlIndexLabelT *indexLabel;
    uint64_t nullDisableBitMap;  ///< the null disable bitmap of the index prope for scan,
                                 ///< 0x5 means prop_0 and prop_2 is null disabled
                                 ///< the bits is up to DM_MAX_KEY_PROPE_NUM
    uint64_t rowId;
    uint32_t dataType;
} DmIndexKeyCmpInfoT;

typedef void (*IdxCompareKeyFuncT)(const BTreeKeyT *key1, const BTreeKeyT *key2, DmIndexKeyCmpInfoT *cmpInfo);

/**
 * Given the meta page, get the tree header (`BTreeT`).
 * @param[in] metaPage the address of the page holding the root
 * @return the tree header
 */
static inline BTreeT *BTreeGetTreeHdr(uint8_t *metaPage)
{
    return (BTreeT *)(void *)(metaPage + sizeof(PageHeadT));
}

static inline PageIdT BTreeGetBigKeyPageId(BTreeEntryHdrT *entryHdr)
{
    return *(PageIdT *)(uint8_t *)(entryHdr + 1);
}

/**
 * Given a page holding a node of the tree, get the corresponding node header (`BTreeNodeHdrT`).
 * @param[in] nodePage the address of the page holding the node
 * @return the node header
 */
static inline BTreeNodeHdrT *BTreeGetNodeHdr(uint8_t *nodePage)
{
    // the `beginPos` member of `PageHeadT` is used to record the offset of `BTreeNodeHdrT`
    // within the page in the current implementation
    const uint16_t nodeHdrOffset = ((PageHeadT *)(void *)nodePage)->beginPos;
    return (BTreeNodeHdrT *)(void *)(nodePage + nodeHdrOffset);
}

/**
 * Given a page holding a node of the tree and an integer `slotId`, get the `slotId`-th entry of the node.
 * @param[in] nodePage the address of the page holding the node
 * @param[in] slotId   the slot ID
 * @return the header of the `slotId`-th entry
 */
StatusInter BTreeGetEntryHdr(uint8_t *nodePage, int16_t slotId, BTreeEntryHdrT **entryHdr);

StatusInter CopyBtreeKey(BTreeKeyT *dstKey, BTreeKeyT *srcKey);

/**
 * Given a page holding a node of the tree and an integer `slotId`, get the `slotId`-th key of the node.
 * @param[in] idxCtx     the index runtime context
 * @param[in] nodePage   the address of the page holding the node
 * @param[in] slotId     the slot ID
 * @param[in] dataType   datatype of key
 * @param[in] isTransfer the key for transfer operation
 * @return the `slotId`-th key
 */
StatusInter BTreeGetKey(
    IndexCtxT *idxCtx, uint8_t *nodePage, int16_t slotId, uint32_t prefixPropeNum, bool isTransfer, BTreeKeyT *getKey);

/**
 * @brief Calculate the size required in order to insert a key of size `keySize` to a page.
 * @param[in] keySize  the size of the key
 */
static inline size_t BTreeCalculateEntrySize(uint8_t isBigKey, size_t keySize)
{
    // `sizeof(uint16_t)` is the space required by a slot
    // `sizeof(BTreeEntryHdrT)` is the space required by an entry header
    if (isBigKey) {
        // `sizeof(PageIdT)` is the space required by bigKey's pageId
        return sizeof(uint16_t) + sizeof(BTreeEntryHdrT) + sizeof(PageIdT);
    }
    return keySize + sizeof(uint16_t) + sizeof(BTreeEntryHdrT);
}

/**
 * @brief Whether a node is the root.
 * @param[in] page  the page corresponding to the node of interest
 */
static inline bool BTreeIsRoot(uint8_t *page)
{
    const BTreeNodeHdrT *nodeHdr = BTreeGetNodeHdr(page);
    return nodeHdr->isRoot;
}

/**
 * @brief Get the size of the contiguous free space immediately after the slot array.
 * @param[in] nodePage  the page of interest
 */
static inline uint16_t BTreePageGetContiguousFreeSize(uint8_t *nodePage)
{
    BTreeNodeHdrT *nodeHdr = BTreeGetNodeHdr(nodePage);
    uint16_t size = (uint16_t)sizeof(BTreeNodeHdrT) + (uint16_t)(nodeHdr->numSlots) * sizeof(uint16_t);
    size += (uint16_t)((uint8_t *)(void *)nodeHdr - nodePage);
    // when implemented correctly, the following assert should always be true
    DB_ASSERT(size <= nodeHdr->leftBoundary);
    return (uint16_t)(nodeHdr->leftBoundary - size);
}

static inline void InitKeyCmpInfoWithDefault(DmIndexKeyCmpInfoT *cmpInfo)
{
    cmpInfo->found = false;
    cmpInfo->outOfRange = false;
    cmpInfo->cmpEof = false;
    cmpInfo->cmpResult = DM_INDEX_KEY_BUTT;
}

static inline DmIndexKeyCmpInfoT BTreeGetInitKeyCmpInfo(const BTreeCursorT *cursor, bool cmpRowId)
{
    DmIndexKeyCmpInfoT cmpInfo;
    cmpInfo.cmpRowId = cmpRowId;
    cmpInfo.indexLabel = cursor->idxCtx->idxOpenCfg.vlIndexLabel;
    cmpInfo.nullDisableBitMap = cursor->nullDisableBitMap;
    cmpInfo.dataType = cursor->idxCtx->idxMetaCfg.keyDataType;
    InitKeyCmpInfoWithDefault(&cmpInfo);
    return cmpInfo;
}

static inline void BTreeCursorInit(IndexCtxT *idxCtx, BTreeCursorT *cursor)
{
    (void)memset_s(cursor, sizeof(BTreeCursorT), 0, sizeof(BTreeCursorT));
    BTreeIndexCtxT *treeCtx = (BTreeIndexCtxT *)(void *)idxCtx->idxRunCtx;
    cursor->pageMgr = treeCtx->pageMgr;
    cursor->isUnique = (bool)(idxCtx->idxMetaCfg.idxConstraint != NON_UNIQUE);
    cursor->rootAddr.pageId = *(PageIdT *)(&(idxCtx->idxShmAddr));
    cursor->idxCtx = idxCtx;
    cursor->scanMode = IDX_SCAN_BEGIN;
}

static inline void BTreeCmpRowId(HpTupleAddr rowId1, HpTupleAddr rowId2, DmIndexKeyCmpInfoT *cmpInfo)
{
    if (rowId1 < rowId2) {
        cmpInfo->cmpResult = DM_INDEX_KEY_LT;
    } else if (rowId1 > rowId2) {
        cmpInfo->cmpResult = DM_INDEX_KEY_GT;
    } else {
        cmpInfo->cmpResult = DM_INDEX_KEY_EQ;
    }
}

static inline bool BTreeCmpIsBoundKey(const BTreeKeyT *key1, const BTreeKeyT *key2, DmIndexKeyCmpInfoT *cmpInfo)
{
    // a key with `keySize` being 0 is considered to be the minimum;
    // a key with `keySize` being `DB_INVALID_UINT32` is considered to be the maximum
    if (key1->keySize == 0 || key2->keySize == DB_INVALID_UINT32) {
        cmpInfo->cmpResult = DM_INDEX_KEY_LT;
        return true;
    }
    if (key1->keySize == DB_INVALID_UINT32 || key2->keySize == 0) {
        cmpInfo->cmpResult = DM_INDEX_KEY_GT;
        return true;
    }
    return false;
}

void BTreeCmpKeysInner(const BTreeKeyT *key1, const BTreeKeyT *key2, DmIndexKeyCmpInfoT *cmpInfo);

void BTreeCmpKeysWithCtx(IndexCtxT *idxCtx, const BTreeKeyT *key1, const BTreeKeyT *key2, DmIndexKeyCmpInfoT *cmpInfo);

// --------------------------------------------------------
// the following 4 functions are used to latch an page
// --------------------------------------------------------
static inline bool CheckIsNeedLatch(IndexCtxT *idxCtx, uint8_t *page)
{
    DB_POINTER2(idxCtx, page);
    // we don't need lock avoid to dead lock (BTreeIndexWLatch or BTreeIndexRLatch) in meta page scene
    return !IndexIsLabelLatchMode(idxCtx) && ((PageHeadT *)(void *)page)->beginPos == (uint32_t)sizeof(PageHeadT);
}

static inline void BTreePageWLatch(IndexCtxT *idxCtx, uint8_t *page)
{
    if (!CheckIsNeedLatch(idxCtx, page)) {
        return;
    }
    DbRWLatchW(&((PageHeadT *)(void *)page)->lock);
}

static inline void BTreePageWUnlatch(IndexCtxT *idxCtx, uint8_t *page)
{
    if (!CheckIsNeedLatch(idxCtx, page)) {
        return;
    }
    DbRWUnlatchW(&((PageHeadT *)(void *)page)->lock);
}

static inline void BTreePageRLatch(IndexCtxT *idxCtx, uint8_t *page)
{
    if (!CheckIsNeedLatch(idxCtx, page)) {
        return;
    }
    DbRWLatchR(&((PageHeadT *)(void *)page)->lock);
}

static inline void BTreePageRUnlatch(IndexCtxT *idxCtx, uint8_t *page)
{
    // 为了防止重复解锁导致进程崩溃的信息，在解锁的入口处进行判断，如果是未加锁的状态，则直接返回，避免进程崩溃，作为兜底措施。
    if (!CheckIsNeedLatch(idxCtx, page)) {
        return;
    }
    DbLatchT *latch = &((PageHeadT *)(void *)page)->lock;
    if (latch->latchMode == DB_LATCH_IDLE) {
        return;
    }
    DbRWUnlatchR(&((PageHeadT *)(void *)page)->lock);
}

/**
 * @brief Get the scan iterator associated with `idxCtx`.
 * @param[in] idxCtx  the index runtime context
 */
static inline IndexScanItrT BTreeIndexGetIterator(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    return (uint8_t *)idxCtx + sizeof(IndexCtxT) + sizeof(BTreeIndexCtxT);
}

// ----------------------------------------------------------------
// the following 4 functions are used to latch the *whole index*
// by acquring the page latch of the meta page
// ----------------------------------------------------------------
static inline void BTreeIndexWLatch(IndexCtxT *idxCtx, BTreeT *meta)
{
    DB_POINTER2(idxCtx, meta);
    if (IndexIsLabelLatchMode(idxCtx)) {
        return;
    }
    // the meta information is stored next to the page header
    PageHeadT *header = (PageHeadT *)(void *)meta - 1;
    DbRWLatchW(&header->lock);
}

static inline void BTreeIndexWUnlatch(IndexCtxT *idxCtx, BTreeT *meta)
{
    DB_POINTER2(idxCtx, meta);
    if (IndexIsLabelLatchMode(idxCtx)) {
        return;
    }
    // the meta information is stored next to the page header
    PageHeadT *header = (PageHeadT *)(void *)meta - 1;
    DbRWUnlatchW(&header->lock);
}

static inline void BTreeIndexRLatch(IndexCtxT *idxCtx, BTreeT *meta)
{
    DB_POINTER2(idxCtx, meta);
    if (IndexIsLabelLatchMode(idxCtx)) {
        return;
    }
    // the meta information is stored next to the page header
    PageHeadT *header = (PageHeadT *)(void *)meta - 1;
    DbRWLatchR(&header->lock);
}

static inline void BTreeIndexRUnlatch(IndexCtxT *idxCtx, BTreeT *meta)
{
    DB_POINTER2(idxCtx, meta);
    if (IndexIsLabelLatchMode(idxCtx)) {
        return;
    }
    // the meta information is stored next to the page header
    PageHeadT *header = (PageHeadT *)(void *)meta - 1;
    DbRWUnlatchR(&header->lock);
}

/**
 * @brief Unlatch and leave the page `cursor->curAddr.page`.
 * @param[in] wLatch     whether the page is W-latched
 * @param[in,out] cursor the cursor; the resulting cursor is set to invalid
 */
static inline void BTreeCursorLeavePage(BTreeCursorT *cursor, bool wLatch)
{
    if (wLatch) {
        BTreePageWUnlatch(cursor->idxCtx, cursor->curAddr.page);
    } else {
        BTreePageRUnlatch(cursor->idxCtx, cursor->curAddr.page);
    }
    SeLeavePage(cursor->pageMgr, cursor->curAddr.pageId, wLatch);
    cursor->curAddr.page = NULL;
    cursor->state = CURSOR_INVALID;
}

static inline bool BTreeIsCmpEof(DmIndexKeyCmpInfoT *cmpInfo, Status errCode)
{
    return cmpInfo->found || cmpInfo->outOfRange || errCode != GMERR_OK;
}
/**
 * @brief 获取索引的属性字段数目
 * @param[in] indexLabel 索引的label参数
 * @param[in] keyDataType 索引key类型
 * @return 属性数 0表明索引为空
 */
static inline uint32_t BTreeGetIndexPropeNum(const DmVlIndexLabelT *indexLabel, uint32_t keyDataType)
{
    // SE_INDEX_DATATYPE_BLOB类型的key属性个数固定为1
    if (keyDataType == SE_INDEX_DATATYPE_BLOB) {
        return (indexLabel == NULL) ? 0 : 1;
    }

    return (indexLabel == NULL) ? 0 : indexLabel->propeNum;
}

static inline void UnLatchPageAndLeave(
    PageIdT pageId, uint8_t *page, bool wLatch, bool isChange, PageMgrT *pageMgr, IndexCtxT *idxCtx)
{
    if (wLatch) {
        BTreePageWUnlatch(idxCtx, page);
    } else {
        BTreePageRUnlatch(idxCtx, page);
    }
    SeLeavePage(pageMgr, pageId, isChange);
}
/**
 * @brief Set the cursor according to `idxCtx`.
 * @param[in] idxCtx      the index runtime context
 * @param[in] key         the related key
 * @param[in,out] cursor  the cursor
 */
void BTreeCursorSet(BTreeCursorT *cursor, IndexCtxT *idxCtx, IndexKeyT key, BTreeOpTypeE type);

StatusInter ChangeKeyData4BigKeyIfNeed(BTreeCursorT *cursor, PageIdT *bigKeyPageId);

StatusInter BTreeOptLatchCoupling(BTreeCursorT *cursor, bool wLatch);

StatusInter BTreeGetPage(PageMgrT *pageMgr, PageIdT pageId, uint32_t level, uint8_t **page, bool isWrite);

StatusInter BTreeCheckUniqueness(IndexCtxT *idxCtx, BTreeCursorT *cursor);

static inline void BTreeFreeKeyDataAsBigKey(IndexCtxT *idxCtx, BTreeKeyT *freeKey)
{
    if (freeKey->isBigKey) {
        DbDynMemCtxFree(idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx, freeKey->keyData);
        freeKey->keyData = NULL;
    }
}

StatusInter BTreeGetBigKeyPageIds(BTreeCursorT *cursor, PageIdT *leftBigKeyPageId, PageIdT *rightBigKeyPageId);

StatusInter BTreeDeleteBigKey(BTreeCursorT *cursor, BTreeEntryHdrT *entryHdr);

static inline uint32_t BTreeMinimumBigKeyLen(uint32_t pageSize)
{
    return ((pageSize) / DEVICE_NUMBER_FOR_BIGKEY);
}

static inline bool BTreeCheckLenIsBigKey(uint32_t pageSize, uint32_t keyLen)
{
    return ((keyLen) >= BTreeMinimumBigKeyLen(pageSize));
}

typedef struct UpdateParentNodeParams {
    BTreeKeyT targetKey;
    PageIdT targetPageId;
} UpdateParentNodeParamsT;
StatusInter BTreePageGetFreeSize(uint8_t *nodePage, uint16_t *freeSize);
StatusInter BTreeTransfer(BTreeCursorT *cursor, int16_t low, int16_t high, uint8_t *srcPage, uint8_t *destPage);

StatusInter BTreeTransferToPrePage(
    BTreeCursorT *cursor, int16_t low, int16_t high, uint8_t *srcPage, uint8_t *destPage);
StatusInter BTreeDecideSplitPoint(const BTreeCursorT *cursor, int16_t *splitPoint, bool *assignRight);

BTreeEntryHdrT InitializeEntryHdr(const BTreeKeyT *key, uint16_t isDeleted, PageIdT childPageId);

StatusInter BTreeSplitNode(const PageIdT newChildPageId, BTreePageAddrT *nextLeafAddr, BTreeCursorT *cursor);

StatusInter FreeChildNodeAndModifyParentNode(
    RedoRunCtxT *redoCtx, PageMgrT *pageMgr, uint32_t spaceId, uint16_t level, NodeFreeParaT para);

StatusInter BTreePesLatchCoupling(BTreeCursorT *cursor, bool insert);
void BTreePesLatchCleanRes(BTreeCursorT *cursor, bool isCompleted);
StatusInter BTreeDeleteEntry(BTreeCursorT *cursor, PageIdT pageId, uint8_t *page, int16_t slot);

Status BTreeSetExtendKey(IndexCtxT *idxCtx, IndexKeyT key, uint32_t offset, uint8_t *value, uint32_t len);

Status BTreeDelete(IndexCtxT *idxCtx, IndexKeyT deleteKey, HpTupleAddr rowId, IndexRemoveParaT para, bool isRollback);
void BTreeDeleteUpdateStat(const BTreeCursorT *cursor, const PageIdT metaPageId, BTreeT *meta);
StatusInter BTreeDeleteSlot(IndexCtxT *idxCtx, BTreeCursorT *cursor, IndexRemoveParaT *para, bool isRollback);
#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
