/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: se_persistence_hash_index.c
 * Description: Implementation of Hash Index
 * Author: huangboxin
 * Create: 2024/3/25
 */

#include "se_persistence_hash_index.h"

#include "db_memcpy.h"
#include "se_hash_redo_am.h"
#include "se_persistence_hash_common.h"
#include "se_persistence_hash_index_stash_page.h"
#include "se_index.h"
#include "se_page_mgr.h"
#include "se_trx_inner.h"

typedef struct ExpandHashPara {
    HtHashCodeT hashCode;
    uint32_t segId;
} ExpandHashParaT;

typedef struct SegmentPage {
    HashDirSegmentT *segment;
    PageHeadT *page;
    uint32_t pattern;
} SegmentPageT;

void HashCalcPagePtrArrCapacity(const SeInstanceT *seInstance, uint32_t *arrCapacity)
{
    DB_POINTER2(seInstance, arrCapacity);
    // 1. get config options from file
    uint32_t pageSizeInKB = seInstance->seConfig.pageSize;
    uint32_t seMaxMemInKB = seInstance->seConfig.maxSeMem;

    // 2. calc array capacity
    uint32_t upperbound = seMaxMemInKB;
    DB_ASSERT(pageSizeInKB != 0);
    uint32_t dbMaxSegPageNum = (upperbound + pageSizeInKB - 1) / pageSizeInKB;
    uint32_t pageContentSize = (uint32_t)(pageSizeInKB * (uint32_t)DB_KIBI - (uint32_t)HASH_PAGE_HEAD_SIZE);
    uint32_t hashSegNumPerPage = (uint32_t)(pageContentSize / sizeof(HashDirSegmentT));
    DB_ASSERT(hashSegNumPerPage != 0);
    // arrcapacity means the max dir page nums in se mem
    *arrCapacity = ((dbMaxSegPageNum + hashSegNumPerPage - 1) * DOUBLE_SIZE / hashSegNumPerPage);
}

uint8_t *HashGetPageBySeg(const IndexCtxT *idxCtx, const HashDirSegmentT *seg, PageIdT *pageAddr, PageOptionE option)
{
    DB_POINTER4(idxCtx, idxCtx->idxRunCtx, seg, pageAddr);
    HashPageInfoT pageInfo = SE_INVALID_HASH_PAGE_INFO;
    Status ret = GMERR_OK;
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx;
    ret = HtGetPage(memRunCtx, seg->pageAddr.pageAddr, option, &pageInfo);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "|SE-Hash|hash table get mem page unsucc,|%" PRIu32 ",%" PRIu32 "|", seg->pageAddr.blockId,
            seg->pageAddr.pageId);
        return NULL;
    }

    // leave page outside
    *pageAddr = pageInfo.pageAddr;
    return pageInfo.virtAddr;
}

inline static PageHeadT *HtGetPageHead(uint8_t *segPageAddr)
{
    DB_POINTER(segPageAddr);
    return ((PageHeadT *)(void *)(segPageAddr - HASH_PAGE_HEAD_SIZE));
}

void HashPageLock(const IndexCtxT *idxCtx, uint8_t *page, bool isWrite, const PageIdT *pageAddr)
{
    DB_POINTER2(idxCtx, page);
    SeRunCtxT *seRunCtx = idxCtx->idxOpenCfg.seRunCtx;
    DB_POINTER(seRunCtx);
    DbSessionCtxT *ctx = &seRunCtx->resSessionCtx;
    PageHeadT *baseHead = HtGetPageHead(page);
    LATCH_GET_START_WAITTIMES(isWrite, baseHead->lock);
    if (ctx->isDirectRead || ctx->isDirectWrite) {
        DB_POINTER(pageAddr);
        if (SECUREC_UNLIKELY(isWrite)) {
            DbRWSpinWLockWithSession(ctx, &baseHead->lock, (const ShmemPtrT *)pageAddr, LATCH_ADDR_PAGEID);
        } else {
            DbRWSpinRLockWithSession(ctx, &baseHead->lock, (const ShmemPtrT *)pageAddr, LATCH_ADDR_PAGEID);
        }
    } else {
        if (isWrite) {
            DbRWLatchW(&baseHead->lock);
        } else {
            DbRWLatchR(&baseHead->lock);
        }
    }
    LATCH_GET_END_WAITTIMES((bool)!isWrite, baseHead->lock, ctx->session);
}

void HashPageUnLock(const IndexCtxT *idxCtx, uint8_t *page, bool isWrite)
{
    DB_POINTER2(idxCtx, page);
    SeRunCtxT *seRunCtx = idxCtx->idxOpenCfg.seRunCtx;
    DB_POINTER(seRunCtx);
    DbSessionCtxT *ctx = &seRunCtx->resSessionCtx;
    PageHeadT *baseHead = HtGetPageHead(page);
    if (ctx->isDirectRead || ctx->isDirectWrite) {
        if (isWrite) {
            DbRWSpinWUnlockWithSession(ctx, &baseHead->lock);
        } else {
            DbRWSpinRUnlockWithSession(ctx, &baseHead->lock);
        }
    } else {
        if (isWrite) {
            DbRWUnlatchW(&baseHead->lock);
        } else {
            DbRWUnlatchR(&baseHead->lock);
        }
    }
}

#ifndef NDEBUG
static void HashBucketCheck(const IndexCtxT *idxCtx, uint8_t *pageBody, uint32_t targetSlot)
{
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    uint32_t leftCount = ccehMeta->leftBucketCount * ccehMeta->splitIndex;
    uint32_t hashMaxProbeLen = ccehMeta->leftBucketCount;
    DB_ASSERT(leftCount < ccehMeta->hashEntryNumPerPage);
    if (targetSlot < leftCount) {
        if (targetSlot % ccehMeta->leftBucketCount != 0) {
            return;
        }
    } else {
        uint32_t rightCount = targetSlot - leftCount;
        if (rightCount % (ccehMeta->leftBucketCount - 1) != 0) {
            return;
        }
        --hashMaxProbeLen;
    }

    // probeLen后的entry数据，都不应该属于targetSlot，否则可能导致漏扫数据
    HashEntryBaseT *hashEntryBase = GetHashEntryBase(ht, pageBody, targetSlot);
    uint32_t probeLen = hashEntryBase->probeLen;
    for (uint32_t i = probeLen + 1; i < hashMaxProbeLen; i++) {
        uint32_t slot = HashGetSlot(targetSlot, i);
        TupleAddr addr = HEAP_INVALID_ADDR;
        HashEntryBaseT *currentEntryBase = GetHashEntryBaseAndAddr(ht, pageBody, slot, &addr);
        // addr与hashcode保持一致，要么都无效，要么都有效
        if (addr == HEAP_INVALID_ADDR) {
            DB_ASSERT(currentEntryBase->hashCode == 0);
            continue;
        }
        DB_ASSERT(currentEntryBase->hashCode != 0);
        uint32_t bucketSlot = HashGetTargetSlot(idxCtx, currentEntryBase->hashCode);
        // probelen之后entry slot必定不属于targetSlot，否则可能会导致数据丢失
        DB_ASSERT(ht->memMgr.ccehMeta.hashEntryNumPerPage != 0);
        DB_ASSERT((bucketSlot % ht->memMgr.ccehMeta.hashEntryNumPerPage) !=
                  (targetSlot % ht->memMgr.ccehMeta.hashEntryNumPerPage));
    }
}

static void HashTableCheckPageEntry(IndexCtxT *idxCtx, uint8_t *pageBody)
{
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    for (uint32_t i = 0; i < ht->memMgr.ccehMeta.hashEntryNumPerPage; i++) {
        DB_ASSERT(ccehMeta->leftBucketCount != 0);  // 创建索引时能够保证该值不为0
        // 表明为bucket的第一个entry，校验该bucket的probeLen准确性
        HashBucketCheck(idxCtx, pageBody, i);
    }
}
#endif

static Status updateDirDepthInMeta(HashMemRunCtxT *memRunCtx, const PageIdT metaPageId, const uint32_t dirDepth)
{
    uint8_t *metaPage = NULL;
    StatusInter interErrno = SeGetPage(memRunCtx->pageMgr, metaPageId, (uint8_t **)&metaPage, ENTER_PAGE_WRITE, true);
    if (interErrno != STATUS_OK_INTER) {
        DB_LOG_ERROR(DbGetExternalErrno(interErrno), "|Hash| GetPage when update dir depth: unable.");
        return DbGetExternalErrno(interErrno);
    }
    HashTableMetaT *htMeta = (HashTableMetaT *)((PageHeadT *)metaPage + 1);
    htMeta->dirDepth = dirDepth;
    HashRedoForSetMetaDepth(metaPageId, htMeta->dirDepth, SeGetCurRedoCtx());
    SeLeavePage(memRunCtx->pageMgr, metaPageId, true);
    return DbGetExternalErrno(interErrno);
}

static uint8_t *HashGetHashDirSegPageWithLock(
    IndexCtxT *idxCtx, uint32_t segId, uint32_t segIdOld, bool isWrite, PageIdT *pageAddr)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    DB_ASSERT(ccehMeta->hashSegNumPerPage != 0u);
    uint32_t dirPageIdOld = segIdOld / ccehMeta->hashSegNumPerPage;
    uint32_t dirPageId = segId / ccehMeta->hashSegNumPerPage;
    HashPageInfoT pageInfo = SE_INVALID_HASH_PAGE_INFO;
    Status ret;
    PageOptionE option = isWrite ? ENTER_PAGE_WRITE : ENTER_PAGE_NORMAL;
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx;
    if (dirPageId < ccehMeta->dirPageCount) {
        ret = HtGetDirPage(memRunCtx, dirPageId, option, &pageInfo);
        if (ret != GMERR_OK) {
            return NULL;
        }
    } else {
        ret = HtAllocPageForDir(memRunCtx, dirPageId, &pageInfo);
        if (ret != GMERR_OK) {
            return NULL;
        }
        ccehMeta->dirPageCount++;
    }
    if (dirPageIdOld != dirPageId) {
        HashPageLockTL(idxCtx, pageInfo.virtAddr, isWrite, &pageInfo.pageAddr);
    }
    *pageAddr = pageInfo.pageAddr;
    return pageInfo.virtAddr;
}

static Status PageAddrArrayExpand(
    IndexCtxT *idxCtx, HashMemRunCtxT *memRunCtx, HashTableT *ht, uint32_t oldSize, uint32_t newSize)
{
    SeInstanceT *se = idxCtx->idxOpenCfg.seRunCtx->seIns;
    DbMemCtxT *htMemCtx = (DbMemCtxT *)DbGetShmemCtxById(se->hashIndexShmMemCtxId, se->instanceId);
    if (htMemCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "|SE-Hash| HCIN: hash's mem context is wrong");
        return GMERR_INTERNAL_ERROR;
    }

    ShmemPtrT newPageArrShmPtr;
    PageIdT *newPageArrAddr = (PageIdT *)SeShmAlloc(htMemCtx, newSize * (uint32_t)sizeof(PageIdT), &newPageArrShmPtr);
    if (newPageArrAddr == NULL || !DbIsShmPtrValid(newPageArrShmPtr)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "unable to alloc page array, size: %" PRIu32 ", index id: %" PRIu32,
            newSize, ht->idxBase.indexCfg.indexId);
        return GMERR_OUT_OF_MEMORY;
    }

    // copy
    for (uint32_t i = 0; i < oldSize; i++) {
        newPageArrAddr[i] = memRunCtx->pageId[i];
    }

    for (uint32_t i = oldSize; i < newSize; ++i) {
        newPageArrAddr[i] = SE_INVALID_PAGE_ADDR;
    }
    // free and refresh
    if (DbIsShmPtrValid(ht->pageAddr)) {
        DbShmemCtxFree(htMemCtx, ht->pageAddr);
    }
    ht->pageAddr = newPageArrShmPtr;
    memRunCtx->pageId = newPageArrAddr;
    memRunCtx->pagePtrArrCapacity = newSize;
    return GMERR_OK;
}

static Status HashAllocPageForDir(HashMemRunCtxT *memRunCtx, HashTableT *ht, uint32_t count)
{
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    HashPageInfoT pageInfo = SE_INVALID_HASH_PAGE_INFO;
    PageIdT nextPageId = SE_INVALID_PAGE_ADDR;
    uint32_t totalCount = ccehMeta->dirPageCount + count;
    DB_ASSERT(totalCount == memRunCtx->pagePtrArrCapacity);
    DB_ASSERT(ccehMeta->dirPageCount > 0);
    uint32_t curBlockId = totalCount - 1;
    // 从后往前申请，这样在申请该页时便知道nextPageId
    for (uint32_t i = 0; i < count; i++) {
        Status ret = HtAllocPageForDir(memRunCtx, curBlockId, &pageInfo);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to pre alloc hash dir seg page, allocCount = %" PRIu32, count);
            HashFreeHashDirPage(ht, i);
            return ret;
        }
        pageInfo.head->nextPageId = nextPageId;
        nextPageId = pageInfo.pageAddr;
        HashRedoForSetDirNextPageId(pageInfo.pageAddr, nextPageId, SeGetCurRedoCtx());
        HtLeavePage(memRunCtx, &pageInfo);
        --curBlockId;
    }

    Status ret = HtGetDirPage(memRunCtx, curBlockId, ENTER_PAGE_WRITE, &pageInfo);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to get dir page, blockId: %u", curBlockId);
        HashFreeHashDirPage(ht, count);
    }
    pageInfo.head->nextPageId = nextPageId;
    HashRedoForSetDirNextPageId(pageInfo.pageAddr, nextPageId, SeGetCurRedoCtx());
    HtLeavePage(memRunCtx, &pageInfo);
    ccehMeta->dirPageCount = totalCount;
    return ret;
}

static Status HashPreAllocHashDirSegPage(IndexCtxT *idxCtx, uint32_t *pageAllocCount, HashDirSegmentT *segOld)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    DB_ASSERT(ccehMeta->hashSegNumPerPage != 0u);

    uint32_t oldCapacity = ccehMeta->dir.dirCap;
    uint32_t newCapacity = 1u << (ccehMeta->dir.dirDepth + 1);  // new capacity of directory entries
    DB_ASSERT(oldCapacity * DOUBLE_SIZE == newCapacity);
    uint32_t newDirPageCount = (newCapacity - 1u) / ccehMeta->hashSegNumPerPage + 1u;
    // 局部深度小于全局深度，不申请新dirPage
    if (segOld->segDepth < ccehMeta->dir.dirDepth || newDirPageCount - ccehMeta->dirPageCount == 0) {
        *pageAllocCount = 0;
        return GMERR_OK;
    }

    *pageAllocCount = newDirPageCount - ccehMeta->dirPageCount;
    Status ret = GMERR_OK;
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx;
    if (*pageAllocCount > 0) {
        ret = PageAddrArrayExpand(idxCtx, memRunCtx, ht, ccehMeta->dirPageCount, newDirPageCount);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    ret = HashAllocPageForDir(memRunCtx, ht, *pageAllocCount);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to pre alloc dir page, count: %u", *pageAllocCount);
        return ret;
    }

    DB_ASSERT(memRunCtx->pagePtrArrCapacity == ccehMeta->dirPageCount);
    return GMERR_OK;
}

static void UnLockIfNeed(IndexCtxT *idxCtx, uint8_t *dirSegPage, uint32_t i, uint32_t segId, bool isWrite)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    DB_ASSERT(ht->memMgr.ccehMeta.hashSegNumPerPage != 0u);
    uint32_t dirPageIdOld = segId / ht->memMgr.ccehMeta.hashSegNumPerPage;
    uint32_t dirPageId = i / ht->memMgr.ccehMeta.hashSegNumPerPage;
    if (dirPageIdOld != dirPageId) {
        HashPageUnLockTL(idxCtx, dirSegPage, isWrite);
    }
}

static Status HashGetSegmentPageByHashKey(
    IndexCtxT *idxCtx, HtHashCodeT hashCode, PageOptionE option, HashPageInfoT *pageInfo)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t pattern = HtGetPattern(hashCode, ht->memMgr.ccehMeta.dir.dirCap);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    DB_ASSERT(ccehMeta->hashSegNumPerPage != 0u);
    uint32_t blockId = pattern / ccehMeta->hashSegNumPerPage;  // dir数组下标
    HashPageInfoT dirPageInfo = SE_INVALID_HASH_PAGE_INFO;
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx;
    Status ret = HtGetDirPage(memRunCtx, blockId, ENTER_PAGE_NORMAL, &dirPageInfo);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Unable to get dir page, blockId: %u", blockId);
        return ret;
    }

    uint32_t dirSlotId = pattern % ccehMeta->hashSegNumPerPage;
    PageIdT segPageId = ((HashDirSegmentT *)dirPageInfo.virtAddr)[dirSlotId].pageAddr.pageAddr;
    SeLeavePage(memRunCtx->pageMgr, dirPageInfo.pageAddr, false);

    ret = HtGetPage(memRunCtx, segPageId, option, pageInfo);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "|SE-Hash|hash table get mem page unsucc,|%" PRIu32 ",%" PRIu32 "|", segPageId.deviceId,
            segPageId.blockId);
    }

    return ret;
}
#ifndef NDEBUG
inline static void HashCheckReservedEntry(HashTableT *ht, uint32_t probeIdx, const HashLookupParaT *para)
{
    for (uint32_t i = probeIdx; i < para->hashMaxProbeLen; i++) {
        uint32_t slot = HashGetSlot(para->targetSlot, i);
        TupleAddr addr = HEAP_INVALID_ADDR;
        HashEntryBaseT *currentEntryBase = GetHashEntryBaseAndAddr(ht, para->pageBody, slot, &addr);
        DB_ASSERT(
            addr == HEAP_INVALID_ADDR ? currentEntryBase->hashCode == 0 : currentEntryBase->hashCode != para->hashCode);
    }
}
#endif

static Status HashLookupHashEntryProc(IndexCtxT *idxCtx, HashLookupParaT *para)
{
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    HashEntryBaseT *targetEntry = GetHashEntryBase(ht, para->pageBody, para->targetSlot);
    const uint32_t probeLen = HashEntryGetProbeLen(targetEntry);  // get probe length from first entry
    uint32_t probeIdx = 0;
    if (para->isLookupOldRecord) {
        idxCtx->oldRowId = HEAP_INVALID_ADDR;  // 如果要查找旧记录，先将 oldRowId 初始化为 0
    }
    // 取等号不会越界，probeLen的最大取值是maxProbeLen-1
    for (; probeIdx <= probeLen; ++probeIdx) {
        uint32_t slot = HashGetSlot(para->targetSlot, probeIdx);
        HashEntryBaseT *currentEntryBase = GetHashEntryBaseAndAddr(ht, para->pageBody, slot, &para->addr);
        if (para->addr == HEAP_INVALID_ADDR || currentEntryBase->hashCode != para->hashCode) {
            continue;
        }
        HtHashLookupParaT htLookupPara = {.entry = currentEntryBase, .isMatch = false, .isSelfDel = false};
        Status ret = HashSegmentLookupCmp(idxCtx, para, &htLookupPara);
        if (ret != GMERR_OK || (htLookupPara.isMatch && !htLookupPara.isSelfDel)) {
            return ret;
        }
    }
    uint32_t maxProbeLen = para->hashMaxProbeLen;
    HashEntryBaseT *lastEntryBase = GetHashEntryBase(ht, para->pageBody, (para->targetSlot + maxProbeLen) - 1);
    if (HashEntryBaseIsSetStashPageFlag(lastEntryBase)) {
        IdxStashPageRlock(idxCtx, LATCH_ADDR_STASH_PAGE_SHMEM);
        Status ret = HashStashPageLookup(idxCtx, para);
        IdxStashPageRUnlock(idxCtx);
        return ret;
    }
#ifndef NDEBUG
    HashCheckReservedEntry(ht, probeIdx, para);
#endif
    return GMERR_NO_DATA;
}

// used in HashIndexDelete && HashExpandHashSegment
// this func will narrow down probe length if needed
static void HashEntryUpdateProbeLen(
    const IndexCtxT *idxCtx, const HashTableT *ht, uint8_t *pageBody, uint32_t targetSlot)
{
    DB_POINTER3(idxCtx, ht, pageBody);
    HashEntryBaseT *targetEntry = GetHashEntryBase(ht, pageBody, targetSlot);
    uint32_t newProbeLen = targetEntry->probeLen;
    uint32_t realTargetSlot = targetSlot;
    if (realTargetSlot >= ht->memMgr.ccehMeta.hashEntryNumPerPage) {
        realTargetSlot -= ht->memMgr.ccehMeta.hashEntryNumPerPage;
    }
    while (newProbeLen > 0) {
        uint32_t currentSlot = targetSlot + newProbeLen;
        if (currentSlot >= ht->memMgr.ccehMeta.hashEntryNumPerPage) {
            currentSlot -= ht->memMgr.ccehMeta.hashEntryNumPerPage;
        }
        HashEntryBaseT *currentEntryBase = GetHashEntryBase(ht, pageBody, currentSlot);
        uint32_t currentTargetSlot = HashGetTargetSlot(idxCtx, currentEntryBase->hashCode);
        if (currentTargetSlot >= ht->memMgr.ccehMeta.hashEntryNumPerPage) {
            currentTargetSlot -= ht->memMgr.ccehMeta.hashEntryNumPerPage;
        }
        if (currentTargetSlot == realTargetSlot) {
            break;
        }
        --newProbeLen;
    }
    targetEntry->probeLen = (HtHashCodeT)(newProbeLen & HASH_MAX_PROBE_LEN);
    PageIdT pageId = ((PageHeadT *)(pageBody - HASH_PAGE_HEAD_SIZE))->addr;
    HashRedoForSetSegmentEntryProbeLen(pageId, targetSlot, targetEntry->probeLen, SeGetCurRedoCtx());
}

static void HashEraseEntry(
    const IndexCtxT *idxCtx, HashTableT *ht, const HashLookupParaT *para, HashRemoveParaT *rmPara)
{
    rmPara->entry->hashCode = 0u;
    rmPara->entry->isDeleted = 0u;
    if (rmPara->needUpdateProbeLen) {
        HashEntryUpdateProbeLen(idxCtx, ht, para->pageBody, para->targetSlot);
    }
    SetHashEntryTpAddr(ht->heapTupleAddrMode, rmPara->entry, HEAP_INVALID_ADDR);
    HashEntryT *entry = (HashEntryT *)rmPara->entry;
    PageIdT pageId = ((PageHeadT *)(para->pageBody - HASH_PAGE_HEAD_SIZE))->addr;
    HashRedoForSetSegmentEntry(pageId, rmPara->slotId, entry, SeGetCurRedoCtx());
}

static void HashRemoveFoundHashEntry(IndexCtxT *idxCtx, const HashLookupParaT *para, HashRemoveParaT *rmPara)
{
    rmPara->probeEnd = true;
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint64_t entryVersion = GetHashEntryVerion(ht, rmPara->entry);
    if (para->isGC && !para->isMarkDelete) {       // GC回收, OCC
        DB_ASSERT(rmPara->entry->isDeleted == 0);  // 多版本下不会标记删除
        // GC 线程回收多版本的情况下，比较保证entryVersion 是已提交的版本
        if (SeTransIsCommit(idxCtx->idxOpenCfg.seRunCtx, entryVersion)) {
            HashEraseEntry(idxCtx, ht, para, rmPara);
        } else {
            rmPara->probeEnd = false;
        }
    } else if (para->isGC && para->isMarkDelete) {  // GC回收，PCC
        if (rmPara->entry->isDeleted == 1) {
            HashEraseEntry(idxCtx, ht, para, rmPara);
        }
    } else if (para->isErase && !para->isMarkDelete) {
        // 多版本的回滚 必须保证entryVersion 和本次操作的version 一致
        uint64_t selfVersion = SeTransGetTrxId(idxCtx->idxOpenCfg.seRunCtx);
        if (entryVersion != selfVersion) {
            rmPara->probeEnd = false;
        } else {
            HashEraseEntry(idxCtx, ht, para, rmPara);
        }
    } else if (para->isErase && para->isMarkDelete) {
        if (idxCtx->idxMetaCfg.indexMultiVersionType == INDEX_PCC_RR_TYPE) {
            if (rmPara->entry->isDeleted == 0) {
                HashEraseEntry(idxCtx, ht, para, rmPara);
            }
        } else {
            // 非多版本可以直接回滚, RC模式下不会存在isDeleted=1的数据
            HashEraseEntry(idxCtx, ht, para, rmPara);
        }
    } else {
        if (para->isMarkDelete) {
            rmPara->entry->isDeleted = 1u;
        } else {
            HashRemoveHashEntrySetOldAddr(idxCtx, rmPara, ht, entryVersion);
        }
    }
}

inline static bool NeedContinueProbe(
    HashLookupParaT *para, HashEntryBaseT *currentEntryBase, bool isMultiVersion, TupleAddr addr)
{
    return addr == HEAP_INVALID_ADDR || currentEntryBase->hashCode != para->hashCode ||
           (!isMultiVersion && addr != para->addr);
}

inline static void InitHashRemovePara(HashRemoveParaT *rmPara)
{
    rmPara->entry = NULL;
    rmPara->entryIdx = DB_INVALID_UINT32;
    rmPara->needUpdateProbeLen = false;
    rmPara->probeEnd = true;
}

inline static Status TryToRemoveFromStashPage(
    IndexCtxT *idxCtx, HashTableT *ht, HashLookupParaT *para, HashRemoveParaT *rmPara)
{
    DbRWLatchW(&ht->idxBase.stashPageLatch);
    Status ret = HashStashPageRemove(idxCtx, para, rmPara);
    DbRWUnlatchW(&ht->idxBase.stashPageLatch);
    return ret;
}

#define RM_HASH_ENTRY_LOG \
    "hash delete no data, indexId %" PRIu32 ", rowId: %" PRIu64 ", hashcode %" PRIu32 ", probeLen %" PRIu32 "."

static Status HashRemoveHashEntryProc(IndexCtxT *idxCtx, HashLookupParaT *para)
{
    DB_POINTER(idxCtx);
    HashRemoveParaT rmPara;
    InitHashRemovePara(&rmPara);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    bool isMultiVersion = HashIndexIsMultiVersion(ht);
    HashEntryBaseT *targetEntry = GetHashEntryBase(ht, para->pageBody, para->targetSlot);
    const uint32_t probeLen = HashEntryGetProbeLen(targetEntry);  // get probe length from first entry
    uint32_t probeIdx = 0;
    // 取等号不会越界，probeLen的最大取值是maxProbeLen-1
    for (; probeIdx <= probeLen; ++probeIdx) {
        uint32_t slot = HashGetSlot(para->targetSlot, probeIdx);
        TupleAddr addr = HEAP_INVALID_ADDR;
        HashEntryBaseT *currentEntryBase = GetHashEntryBaseAndAddr(ht, para->pageBody, slot, &addr);
        if (NeedContinueProbe(para, currentEntryBase, isMultiVersion, addr)) {
            continue;
        }
        rmPara.entry = currentEntryBase;
        rmPara.slotId = slot;
        rmPara.needUpdateProbeLen = (probeIdx == probeLen);
        if (addr != para->addr) {
            continue;
        }
        HashRemoveFoundHashEntry(idxCtx, para, &rmPara);
        if (rmPara.probeEnd) {
            return GMERR_OK;
        }
    }
    uint32_t maxProbeLen = para->hashMaxProbeLen;
    HashEntryBaseT *lastEntryBase = GetHashEntryBase(ht, para->pageBody, (para->targetSlot + maxProbeLen) - 1);
    if (HashEntryBaseIsSetStashPageFlag(lastEntryBase)) {
        return TryToRemoveFromStashPage(idxCtx, ht, para, &rmPara);
    }
#ifndef NDEBUG
    HashCheckReservedEntry(ht, probeIdx, para);
#endif
    DB_LOG_INFO(RM_HASH_ENTRY_LOG, ht->idxBase.indexCfg.indexId, para->addr, para->hashCode, probeLen);
    return GMERR_NO_DATA;
}

inline static Status HashFindHashEntrySlotBySlot(IndexCtxT *idxCtx, HashLookupParaT *para)
{
    DB_POINTER(idxCtx);
    return para->isRemove ? HashRemoveHashEntryProc(idxCtx, para) : HashLookupHashEntryProc(idxCtx, para);
}

static Status HashFindHashEntryInner(IndexCtxT *idxCtx, HashLookupParaT *para)
{
    // 只需要把leavePage需要的pageAddr传出来即可，其他东西还是存在runctx里，保证并发
    DB_POINTER2(idxCtx, para);
    PageOptionE option = para->isRemove ? ENTER_PAGE_WRITE : ENTER_PAGE_NORMAL;
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx;
    HashPageInfoT pageInfo = SE_INVALID_HASH_PAGE_INFO;
    Status ret = HashGetSegmentPageByHashKey(idxCtx, para->hashCode, option, &pageInfo);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-Hash| get segment by hashcode wrong, hashCode: %lu", (uint64_t)para->hashCode);
        return ret;
    }

    para->pageBody = pageInfo.virtAddr;
    para->pageHead = (PageHeadT *)(void *)pageInfo.virtAddr - 1;
    AUTO_HASH_PAGE_LOCK_TL(idxCtx, pageInfo.virtAddr, para->isRemove, &pageInfo.pageAddr,
        (ret = HashFindHashEntrySlotBySlot(idxCtx, para)));
    SeLeavePage(memRunCtx->pageMgr, pageInfo.pageAddr, para->isRemove);
    return ret;
}

static Status HashFindHashEntry(IndexCtxT *idxCtx, HashLookupParaT *para)
{
    DB_POINTER(idxCtx);
    Status ret;
    uint32_t lockConflictTryCnt = 0;
    uint64_t begin = DbRdtsc();
    ContinueTryLockMethodT lockMethod = {.continueTryLock = HashRLockLLBase, .continueTryUnlock = HashRUnlockLLBase};
    do {
        AUTO_HASH_RLOCK_TL(idxCtx, (ret = HashFindHashEntryInner(idxCtx, para)));
    } while (IdxContinueTryBatched(idxCtx, &lockConflictTryCnt, ret, begin, lockMethod));
    return ret;
}

Status HashIndexLookup(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr *addr, bool *isFound)
{
    DB_POINTER3(idxCtx, idxCtx->idxHandle, isFound);
    *isFound = false;
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    DB_POINTER(idxCtx->idxOpenCfg.callbackFunc.keyCmp);
    DB_ASSERT(ht->memMgr.ccehMeta.hashEntryNumPerPage != 0u);
    HtHashCodeT hashCode = HtGetHashCode(idxKey);
    HashLookupParaT para = {.isUndo = false,
        .isRemove = false,
        .isGC = false,
        .isErase = false,
        .isMarkDelete = false,
        .isLookupOldRecord = idxCtx->isLookupOldRecord,
        .addr = HEAP_INVALID_ADDR,
        .hashKey = idxKey,
        .hashCode = hashCode,
        .targetSlot = HashGetTargetSlot(idxCtx, hashCode),
        .pageBody = NULL,
        .pageHead = NULL,
        .hashMaxProbeLen = HashGetHashMaxProbeLen(idxCtx, hashCode)};
    Status ret = HashFindHashEntry(idxCtx, &para);
    if (SECUREC_LIKELY(ret == GMERR_OK)) {
        if (addr != NULL) {
            *addr = para.addr;
        }
        *isFound = true;
    } else if (ret == GMERR_NO_DATA) {  // not find is normal
        ret = GMERR_OK;
    }
    return ret;
}

Status HashIndexBatchLookup(IndexCtxT *idxCtx, IndexKeyT idxKey[], uint32_t batchNum, IdxBatchLookupParaT *para)
{
    DB_POINTER4(idxCtx, idxKey, para, para->categorizeFunc);
    const IdxCategorizeFunc categorizeFunc = para->categorizeFunc;
    Status ret = GMERR_OK;
    HpTupleAddr addr;
    bool isFound = false;
    HashIndexBatchBegin(idxCtx, false);
    for (uint32_t i = 0; i < batchNum; i++) {
        ret = HashIndexLookup(idxCtx, idxKey[i], &addr, &isFound);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_AND_SET_LASERR(ret, "|SE-Hash| lookup wrong, idxIdx:%" PRIu32, idxCtx->idxMetaCfg.indexId);
            break;
        }
        ret = categorizeFunc(
            idxCtx, para->iter, TupleAddr2IdxTupleOrIter(addr), i, isFound ? IDX_IS_TUPLE_ADDR : IDX_IS_NOT_FOUND);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_AND_SET_LASERR(ret, "|SE-Hash| categorize unsucc, idxIdx:%" PRIu32, idxCtx->idxMetaCfg.indexId);
            break;
        }
    }
    HashIndexBatchEnd(idxCtx, false);
    return ret;
}

static void HashSetSegmentEntry(HashTableT *ht, HashPageInfoT *pageInfo, uint16_t slotId, const HashEntryT *entry)
{
    HashEntryT *dest = GetHashEntry(ht, pageInfo->virtAddr, slotId);
    *dest = *entry;
    HashRedoForSetSegmentEntry(pageInfo->pageAddr, slotId, dest, SeGetCurRedoCtx());
}

// used in HashIndexInsert && HashExpandHashSegment
// this func will enlarge probe length if needed
static void HashEntrySetProbeLen(const HashTableT *ht, HashPageInfoT *pageInfo, uint32_t slotId, uint32_t probeLen)
{
    DB_POINTER2(ht, pageInfo);
    DB_ASSERT(probeLen < HASH_MAX_PROBE_LEN);
    HashEntryBaseT *hashEntryBase = GetHashEntryBase(ht, pageInfo->virtAddr, slotId);
    hashEntryBase->probeLen = probeLen;
    HashRedoForSetSegmentEntryProbeLen(pageInfo->pageAddr, slotId, hashEntryBase->probeLen, SeGetCurRedoCtx());
}

static void HashInsert2EmptySlot(HashTableT *ht, HashPageInfoT *pageInfo, HtInsertProbeCtxT *prob,
    const HashInsertParaT *htInsertPara, uint32_t probeLen)
{
    DB_POINTER3(ht, pageInfo, htInsertPara);
    HashEntryT entry = *GetHashEntry(ht, pageInfo->virtAddr, prob->emptyEntryIdx);
    entry.hashEntryBase.hashCode = htInsertPara->hashCode;
    entry.hashEntryBase.isDeleted = 0;
    entry.addr = htInsertPara->addr;
    HashSetSegmentEntry(ht, pageInfo, prob->emptyEntryIdx, &entry);
    HashEntrySetProbeLen(ht, pageInfo, htInsertPara->targetSlot, probeLen);
}

inline static void HashUpdateCollisionCount(HashTableT *ht, HtInsertProcResT *res, uint32_t probeLen)
{
    DB_POINTER2(ht, res);
    if (res->isFirstCollision && probeLen != 0u) {
        (void)DbAtomicInc64(&ht->hashCollisionCnt);
        res->isFirstCollision = false;
    }
}

inline static bool HtHashCodeDisableRehash(const HashTableT *ht, HashEntryBaseT *entry, HtHashCodeT insertHashCode)
{
    HtHashCodeT mask = ((1llu << SE_HASHCODE_BIT_SIZE) - 1llu);
    return (((HtHashCodeT)entry->hashCode & mask) == ((HtHashCodeT)insertHashCode & mask));
}

static Status HashCheckUniqueAndFindEmptySlot(IndexCtxT *idxCtx, const HashInsertParaT *para, uint8_t *pageBody,
    HtInsertProcResT *res, HtInsertProbeCtxT *probeCtx)
{
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t probeLen = 0;
    uint32_t similarHashCodeCnt = 0;
    HashEntryBaseT *targetEntry = GetHashEntryBase(ht, pageBody, para->targetSlot);
    for (; probeLen < probeCtx->maxProbeLen; ++probeLen) {
        TupleAddr addr = HEAP_INVALID_ADDR;
        HashEntryBaseT *entry = GetHashEntryBaseAndAddr(ht, pageBody, HashGetSlot(para->targetSlot, probeLen), &addr);
        bool isCurrentEmpty = (addr == HEAP_INVALID_ADDR);
        if (!probeCtx->foundSegEmptySlot && isCurrentEmpty) {  // 说明这个位置是空的
            probeCtx->foundSegEmptySlot = true;
            probeCtx->emptyEntryIdx = para->targetSlot + probeLen;
            probeCtx->emptyEntry = entry;
        }
#ifndef NDEBUG
        DB_ASSERT(addr == HEAP_INVALID_ADDR ? entry->hashCode == 0 : true);
#endif
        if (!isCurrentEmpty && HtHashCodeDisableRehash(ht, entry, para->hashCode)) {
            similarHashCodeCnt++;
        }
        // check key uniqueness
        if (!isCurrentEmpty && probeLen <= targetEntry->probeLen && entry->hashCode == para->hashCode) {
            res->needExpand = false;  // 插入前 *needExpand 先置为 false
            Status ret = HashSegmentInsertCmp(idxCtx, para, entry, addr, res);
            if (SECUREC_UNLIKELY(res->needExpand)) {  // 继续探测情况下，ret必定为OK
                DB_ASSERT(ret == GMERR_OK);
                continue;
            }
            probeCtx->probeEnd = true;
            return ret;
        }
        if (probeLen >= targetEntry->probeLen && probeCtx->foundSegEmptySlot) {
            break;
        }
    }
    probeCtx->similarHashCodeCnt = similarHashCodeCnt;
    probeCtx->probeLen = probeLen;
    probeCtx->lastEntry = GetHashEntryBase(ht, pageBody, (para->targetSlot + probeCtx->maxProbeLen) - 1);
    // 有stashPage标记，需要查找stashPage确保唯一性
    // 探测到的所有hashCode都相同，需要查找stashPage中的emptySlot
    probeCtx->needProbeStashPage = (bool)(HashEntryBaseIsSetStashPageFlag(probeCtx->lastEntry) ||
                                          (probeCtx->similarHashCodeCnt == probeCtx->maxProbeLen));
    return GMERR_OK;
}

static void InitInsertProbeCtx(const HashTableT *ht, const HashInsertParaT *para, HtInsertProbeCtxT *probeCtx)
{
    probeCtx->emptyEntry = NULL;
    probeCtx->emptyEntryIdx = DB_INVALID_UINT32;
    probeCtx->probeLen = 0;
    probeCtx->maxProbeLen = ht->memMgr.ccehMeta.leftBucketCount;
    if (para->targetSlot >= ht->memMgr.ccehMeta.leftBucketCount * ht->memMgr.ccehMeta.splitIndex) {
        --probeCtx->maxProbeLen;
    }
    probeCtx->similarHashCodeCnt = 0;
    probeCtx->stashPageHead = NULL;
    probeCtx->stashPageEntryIdx = 0;
    probeCtx->lastEntry = NULL;
    probeCtx->foundSegEmptySlot = false;
    probeCtx->foundStashSlot = false;
    probeCtx->needProbeStashPage = false;
    probeCtx->probeEnd = false;
}

/*
 * This function will do one of following actions:
 * 1. insert a key successfully if not violate uniqueness
 * 2. return wrong if inserted key violates uniqueness constraint
 * 3. when trx rollback, clear deletion mark
 * This func shall consider maintain following variable carefully:
 * NOTE probeLen to record current slot max probe length
 * NOTE needExpand to indicate whether need to expand segment at caller
 * NOTE hashCollisionCnt to record collision cnt
 */
static Status HashCheckUniqueAndInsertEmptySlot(
    IndexCtxT *idxCtx, HashInsertParaT *para, HashPageInfoT *pageInfo, HtInsertProcResT *res)
{
    DB_POINTER4(idxCtx, para, res, idxCtx->idxHandle);
    res->needExpand = true;
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    DB_ASSERT(ht->memMgr.ccehMeta.hashEntryNumPerPage != 0u);
    HtInsertProbeCtxT probeCtx;
    InitInsertProbeCtx(ht, para, &probeCtx);
    Status ret = HashCheckUniqueAndFindEmptySlot(idxCtx, para, pageInfo->virtAddr, res, &probeCtx);
    if (ret != GMERR_OK || probeCtx.probeEnd) {
        return ret;
    }
    if (SECUREC_UNLIKELY(probeCtx.needProbeStashPage)) {
        ret = HashStashPageCheckUniqueAndFindSlot(idxCtx, para, res, &probeCtx);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "unable to find slot in stash page");
        }

        return ret;
    }

    if (probeCtx.foundSegEmptySlot) {  // 找到空位，并且需要插入，则插入
        HashInsert2EmptySlot(ht, pageInfo, &probeCtx, para, probeCtx.probeLen);
        res->needExpand = false;
    }

    HashUpdateCollisionCount(ht, res, probeCtx.probeLen);
    return ret;
}

static void HashUpdateDirSegMap(IndexCtxT *idxCtx, UpdateParaT updatePara, HashDirSegmentT seg0, HashDirSegmentT seg1);
static void HashUpdateDirSegMapAgain(
    IndexCtxT *idxCtx, UpdateParaT updatePara, HashDirSegmentT seg0, HashDirSegmentT seg1)
{
    uint32_t halfCap = updatePara.dirCapacity >> 1;
    UpdateParaT updateParaAgain = {.segDepth = updatePara.segDepth + 1u,
        .dirDepth = updatePara.dirDepth,
        .dirCapacity = halfCap,
        .segId = updatePara.segId - halfCap,
        .segIdOld = updatePara.segIdOld};
    DB_ASSERT(updatePara.dirCapacity != 0u);
    if ((updatePara.segId % updatePara.dirCapacity) >= halfCap) {
        HashUpdateDirSegMap(idxCtx, updateParaAgain, seg0, seg1);
        updateParaAgain.segId = updatePara.segId;
        HashUpdateDirSegMap(idxCtx, updateParaAgain, seg0, seg1);
    } else {
        updateParaAgain.segId = updatePara.segId;
        HashUpdateDirSegMap(idxCtx, updateParaAgain, seg0, seg1);
        updateParaAgain.segId = updatePara.segId + halfCap;
        HashUpdateDirSegMap(idxCtx, updateParaAgain, seg0, seg1);
    }
}

typedef struct IndexCtxGetSegPara {
    IndexCtxT *idxCtx;
    uint32_t segId;
    uint32_t segIdOld;
} IndexCtxGetSegParaT;

// 该接口内部上锁，配合UnLockIfNeed使用
// option为write
static Status GetSegBySegIdWithLock(
    IndexCtxGetSegParaT *ctxPara, uint8_t **dirSegPage, HashDirSegmentT **seg, PageIdT *pageAddr)
{
    DB_POINTER2(ctxPara, ctxPara->idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(ctxPara->idxCtx->idxHandle);

    DB_ASSERT(ht->memMgr.ccehMeta.hashSegNumPerPage != 0u);

    // leave outside when unlock
    *dirSegPage = HashGetHashDirSegPageWithLock(ctxPara->idxCtx, ctxPara->segId, ctxPara->segIdOld, 1, pageAddr);
    if (SECUREC_UNLIKELY(*dirSegPage == NULL)) {
        return GMERR_INTERNAL_ERROR;
    }

    *seg = (HashDirSegmentT *)(void *)(*dirSegPage + (ctxPara->segId % ht->memMgr.ccehMeta.hashSegNumPerPage) *
                                                         sizeof(HashDirSegmentT));
    return GMERR_OK;
}

static void UpdateDirSegRightMap(
    IndexCtxT *idxCtx, UpdateParaT updatePara, HashDirSegmentT seg0, HashDirSegmentT seg1, uint32_t halfCap)
{
    uint8_t *dirSegPage = NULL;
    HashDirSegmentT *seg = NULL;
    PageIdT pageAddr = SE_INVALID_PAGE_ADDR;
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx;
    uint32_t pairSeg = updatePara.segId - halfCap;
    IndexCtxGetSegParaT ctxPara = {idxCtx, pairSeg, updatePara.segIdOld};
    Status ret = GetSegBySegIdWithLock(&ctxPara, &dirSegPage, &seg, &pageAddr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "unable to get seg by pair seg when update dir.");
        return;
    }
    *seg = seg0;
    UnLockIfNeed(idxCtx, dirSegPage, pairSeg, updatePara.segIdOld, 1);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint16_t slotId = pairSeg % ht->memMgr.ccehMeta.hashSegNumPerPage;
    HashRedoForSetSegment(pageAddr, slotId, &seg0, SeGetCurRedoCtx());
    SeLeavePage(memRunCtx->pageMgr, pageAddr, true);

    pageAddr = SE_INVALID_PAGE_ADDR;
    IndexCtxGetSegParaT ctxPara2 = {idxCtx, updatePara.segId, updatePara.segIdOld};
    ret = GetSegBySegIdWithLock(&ctxPara2, &dirSegPage, &seg, &pageAddr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "unable to get seg by update seg when update dir.");
        return;
    }
    *seg = seg1;
    UnLockIfNeed(idxCtx, dirSegPage, updatePara.segId, updatePara.segIdOld, 1);
    slotId = updatePara.segId % ht->memMgr.ccehMeta.hashSegNumPerPage;
    HashRedoForSetSegment(pageAddr, slotId, &seg1, SeGetCurRedoCtx());
    SeLeavePage(memRunCtx->pageMgr, pageAddr, true);
}

static void UpdateDirSegLeftMap(
    IndexCtxT *idxCtx, UpdateParaT updatePara, HashDirSegmentT seg0, HashDirSegmentT seg1, uint32_t halfCap)
{
    uint8_t *dirSegPage = NULL;
    HashDirSegmentT *seg = NULL;
    PageIdT pageAddr = SE_INVALID_PAGE_ADDR;
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx;
    IndexCtxGetSegParaT ctxPara = {idxCtx, updatePara.segId, updatePara.segIdOld};
    Status ret = GetSegBySegIdWithLock(&ctxPara, &dirSegPage, &seg, &pageAddr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "unable to get seg by update seg when update dir.");
        return;
    }
    *seg = seg0;
    UnLockIfNeed(idxCtx, dirSegPage, updatePara.segId, updatePara.segIdOld, 1);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint16_t slotId = updatePara.segId % ht->memMgr.ccehMeta.hashSegNumPerPage;
    HashRedoForSetSegment(pageAddr, slotId, &seg0, SeGetCurRedoCtx());
    SeLeavePage(memRunCtx->pageMgr, pageAddr, true);

    uint32_t pairSeg = updatePara.segId + halfCap;
    pageAddr = SE_INVALID_PAGE_ADDR;
    IndexCtxGetSegParaT ctxPara2 = {idxCtx, pairSeg, updatePara.segIdOld};
    ret = GetSegBySegIdWithLock(&ctxPara2, &dirSegPage, &seg, &pageAddr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "unable to get seg by pair seg when update dir.");
        return;
    }
    *seg = seg1;
    UnLockIfNeed(idxCtx, dirSegPage, pairSeg, updatePara.segIdOld, 1);
    slotId = pairSeg % ht->memMgr.ccehMeta.hashSegNumPerPage;
    HashRedoForSetSegment(pageAddr, slotId, &seg1, SeGetCurRedoCtx());
    SeLeavePage(memRunCtx->pageMgr, pageAddr, true);
}

static void HashUpdateDirSegMap(IndexCtxT *idxCtx, UpdateParaT updatePara, HashDirSegmentT seg0, HashDirSegmentT seg1)
{
    DB_POINTER(idxCtx);
    uint32_t depthDiff = updatePara.dirDepth - updatePara.segDepth;
    DB_ASSERT(updatePara.dirCapacity != 0u);
    if (depthDiff == 0u) {
        uint32_t halfCap = updatePara.dirCapacity >> 1;
        if ((updatePara.segId % updatePara.dirCapacity) >= halfCap) {
            UpdateDirSegRightMap(idxCtx, updatePara, seg0, seg1, halfCap);
        } else {
            UpdateDirSegLeftMap(idxCtx, updatePara, seg0, seg1, halfCap);
        }
    } else {
        HashUpdateDirSegMapAgain(idxCtx, updatePara, seg0, seg1);
    }
}

static Status HashUpdateSegmentByOldSegment(
    HashDirectoryHeadT *oldDir, IndexCtxT *idxCtx, uint32_t newSegId, uint32_t oldSegId)
{
    PageIdT pageAddrNew = SE_INVALID_PAGE_ADDR;
    uint8_t *dirSegPageNew = HashGetHashDirSegPageWithLock(idxCtx, newSegId, oldSegId, 1, &pageAddrNew);
    if (SECUREC_UNLIKELY(dirSegPageNew == NULL)) {
        DB_LOG_ERROR(
            GMERR_OUT_OF_MEMORY, "dir double size unsucc when get new dir seg page, seg id = %" PRId32 ".", oldSegId);
        return GMERR_OUT_OF_MEMORY;
    }
    PageIdT pageAddrOld = SE_INVALID_PAGE_ADDR;
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx;
    uint8_t *dirSegPageOld =
        HashGetHashDirSegPageWithLock(idxCtx, newSegId - oldDir->dirCap, oldSegId, 0, &pageAddrOld);
    if (SECUREC_UNLIKELY(dirSegPageOld == NULL)) {
        DB_LOG_ERROR(
            GMERR_OUT_OF_MEMORY, "dir double size unsucc when get old dir seg page, seg id = %" PRId32 ".", oldSegId);
        UnLockIfNeed(idxCtx, dirSegPageNew, newSegId, oldSegId, 1);
        SeLeavePage(memRunCtx->pageMgr, pageAddrNew, true);
        return GMERR_OUT_OF_MEMORY;
    }
    uint32_t segSlotIdNew = newSegId % ccehMeta->hashSegNumPerPage;
    HashDirSegmentT *segNew = (HashDirSegmentT *)(void *)(dirSegPageNew + segSlotIdNew * sizeof(HashDirSegmentT));
    uint32_t segSlotIdOld = (newSegId - oldDir->dirCap) % ccehMeta->hashSegNumPerPage;
    HashDirSegmentT *segOld = (HashDirSegmentT *)(void *)(dirSegPageOld + segSlotIdOld * sizeof(HashDirSegmentT));
    *segNew = *segOld;
    UnLockIfNeed(idxCtx, dirSegPageNew, newSegId, oldSegId, 1);
    UnLockIfNeed(idxCtx, dirSegPageOld, newSegId - oldDir->dirCap, oldSegId, 0);
    HashRedoForSetSegment(pageAddrNew, segSlotIdNew, segNew, SeGetCurRedoCtx());
    SeLeavePage(memRunCtx->pageMgr, pageAddrNew, true);
    SeLeavePage(memRunCtx->pageMgr, pageAddrOld, false);
    return GMERR_OK;
}

static Status HashDirDoubleSize(HashDirectoryHeadT *oldDir, IndexCtxT *idxCtx, uint32_t segId, HashDirSegmentT expNew)
{
    DB_POINTER2(oldDir, idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    DB_ASSERT(ccehMeta->hashSegNumPerPage != 0u);
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx;

    for (uint32_t i = oldDir->dirCap; i < (oldDir->dirCap * DOUBLE_SIZE); ++i) {
        if (i == (segId + oldDir->dirCap)) {
            PageIdT pageAddr = SE_INVALID_PAGE_ADDR;
            uint8_t *dirSegPageNew = HashGetHashDirSegPageWithLock(idxCtx, i, segId, 1, &pageAddr);
            if (SECUREC_UNLIKELY(dirSegPageNew == NULL)) {
                DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
                    "unable to double dir size when get segid new page, seg id = %" PRId32 ".", segId);
                return GMERR_OUT_OF_MEMORY;
            }
            uint32_t segSlotId = i % ccehMeta->hashSegNumPerPage;
            HashDirSegmentT *segNew = (HashDirSegmentT *)(void *)(dirSegPageNew + segSlotId * sizeof(HashDirSegmentT));
            *segNew = expNew;
            UnLockIfNeed(idxCtx, dirSegPageNew, i, segId, 1);
            HashRedoForSetSegment(pageAddr, segSlotId, segNew, SeGetCurRedoCtx());
            SeLeavePage(memRunCtx->pageMgr, pageAddr, true);
        } else {
            Status ret = HashUpdateSegmentByOldSegment(oldDir, idxCtx, i, segId);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "update segment by old segment wrong");
                return ret;
            }
        }
    }
    ht->htVersion++;
    oldDir->dirDepth++;
    uint32_t capacity = 1u << oldDir->dirDepth;
    oldDir->dirCap = capacity;
    DB_ASSERT(ccehMeta->dirPageCount ==
              (capacity - 1u) / ccehMeta->hashSegNumPerPage + 1u);  // dirPageCount已经在预分配中修改
    return GMERR_OK;
}

static Status HashSegInit(IndexCtxT *idxCtx, HashDirSegmentT *seg, uint32_t segDepth, HashPageInfoT *pageInfo)
{
    // valid seg pointer
    DB_POINTER2(idxCtx, seg);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);

    seg->segDepth = segDepth;
    seg->pattern = 0u;
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx;
    Status ret = HtAllocPage(memRunCtx, pageInfo);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc hash page");
        return ret;
    }
    seg->pageAddr.pageAddr = pageInfo->pageAddr;
    uint8_t *hashEntry = pageInfo->virtAddr;
    HashEntryT invalidHashEntry = {.hashEntryBase = {0}, .addr = HEAP_INVALID_ADDR};
    HashPageLockTL(idxCtx, hashEntry, true, &pageInfo->pageAddr);
    for (uint32_t i = 0; i < ht->memMgr.ccehMeta.hashEntryNumPerPage; ++i) {
        SetHashEntryBySlot(ht, hashEntry, i, invalidHashEntry);
    }
    HashPageUnLockTL(idxCtx, hashEntry, true);
    return GMERR_OK;
}

static void HashExpandRehash(
    IndexCtxT *idxCtx, HashPageInfoT pageInfo, const HashDirSegmentT *oldSeg, uint8_t *oldPageBody)
{
    DB_POINTER3(idxCtx, oldSeg, oldPageBody);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    // rehashing
    HashEntryT invalidHashEntry = {.hashEntryBase = {0}, .addr = HEAP_INVALID_ADDR};
    HtHashCodeT newPageMask = (HtHashCodeT)1 << oldSeg->segDepth;
    DbFastMemcpy(pageInfo.virtAddr, ht->contentSize, oldPageBody, ht->contentSize);
    for (uint32_t idx = 0; idx < ht->memMgr.ccehMeta.hashEntryNumPerPage; ++idx) {
        HashEntryBaseT *hashEntryBase = GetHashEntryBase(ht, oldPageBody, idx);
        TupleAddr addr =
            HeapUncompressTupleAddr((uint8_t *)((uintptr_t)hashEntryBase + sizeof(HtHashCodeT)), ht->heapTupleAddrMode);
        if (addr == HEAP_INVALID_ADDR) {
            continue;
        }
        invalidHashEntry.hashEntryBase.probeLen = hashEntryBase->probeLen;
        // 当前数据属于旧页
        if ((hashEntryBase->hashCode & newPageMask) == 0u) {
            // 清理新页数据
            SetHashEntryBySlot(ht, pageInfo.virtAddr, idx, invalidHashEntry);
        } else {
            // 清理旧页数据
            *hashEntryBase = invalidHashEntry.hashEntryBase;
            SetHashEntryTpAddr(ht->heapTupleAddrMode, hashEntryBase, invalidHashEntry.addr);
        }
    }
#ifndef NDEBUG
    HashTableCheckPageEntry(idxCtx, pageInfo.virtAddr);
    HashTableCheckPageEntry(idxCtx, oldPageBody);
#endif
    ht->htVersion++;
    ht->memMgr.ccehMeta.segPageCount++;
}

static Status HashExpandHashSegment(
    IndexCtxT *idxCtx, HashDirSegmentT *expand, const HashDirSegmentT *oldSeg, uint8_t *oldPageBody)
{
    DB_POINTER4(idxCtx, expand, oldSeg, oldPageBody);
    HashPageInfoT pageInfo = SE_INVALID_HASH_PAGE_INFO;
    if (HashSegInit(idxCtx, expand, oldSeg->segDepth + 1u, &pageInfo) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "segment init unsucc when expand hash segment");
        return GMERR_OUT_OF_MEMORY;
    }

    // rehashing
    Status ret = GMERR_OK;
    AUTO_HASH_PAGE_LOCK_TL(
        idxCtx, pageInfo.virtAddr, true, &pageInfo.pageAddr, (HashExpandRehash(idxCtx, pageInfo, oldSeg, oldPageBody)));
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    HashRedoForCopyPageData(pageInfo.pageAddr, ht->contentSize, pageInfo.virtAddr, SeGetCurRedoCtx());
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx;
    SeLeavePage(memRunCtx->pageMgr, pageInfo.pageAddr, true);
    return ret;
}

static void HashUpdateSegDepthAndPattern(HtHashCodeT hashCode, HashDirSegmentT *oldSeg, HashDirSegmentT *newSeg)
{
    uint32_t patternSpan = 1u << oldSeg->segDepth;
    DB_ASSERT(patternSpan != 0u);
    oldSeg->segDepth += 1u;
    oldSeg->pattern = (uint32_t)(hashCode % ((HtHashCodeT)patternSpan));
    newSeg->pattern = oldSeg->pattern + patternSpan;
}

static Status HashUpdateDirectory(
    IndexCtxT *idxCtx, HashDirSegmentT *segOld, ExpandHashParaT para, HashDirSegmentT newSeg, uint32_t *count)
{
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    HashDirectoryHeadT *dir = &(ccehMeta->dir);
    if (segOld->segDepth - 1u < dir->dirDepth) {
        UpdateParaT updatePara = {
            .segDepth = segOld->segDepth,
            .dirDepth = dir->dirDepth,
            .dirCapacity = dir->dirCap,
            .segId = para.segId,  // 当前segT在所有segT中的index
            .segIdOld = para.segId,
        };
        HashUpdateDirSegMap(idxCtx, updatePara, *segOld, newSeg);
    } else {
        Status ret = HashDirDoubleSize(dir, idxCtx, para.segId, newSeg);
        DB_ASSERT(ret == GMERR_OK);  // 由于预分配了页，理论上HashDirDoubleSize一定能成功
        (*count)++;
        ret = updateDirDepthInMeta(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, ht->metaPageId, dir->dirDepth);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to update dir depth when expand.");
            return ret;
        }
    }
    return GMERR_OK;
}

static Status HashExpandUpdateSegAndDirImpl(IndexCtxT *idxCtx, ExpandHashParaT para, uint8_t *dirPage, uint32_t *count)
{
    DB_POINTER3(idxCtx, dirPage, count);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    DB_ASSERT(ccehMeta->hashSegNumPerPage != 0u);
    uint64_t dirSlotIdOld = para.segId % ccehMeta->hashSegNumPerPage;
    HashDirSegmentT *segOld = (HashDirSegmentT *)(void *)(dirPage + dirSlotIdOld * sizeof(HashDirSegmentT));
    PageIdT oldSegPageAddr = SE_INVALID_PAGE_ADDR;
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx;

    uint8_t *oldSegPageHeader = HashGetPageBySeg(idxCtx, segOld, &oldSegPageAddr, ENTER_PAGE_WRITE);
    if (SECUREC_UNLIKELY(oldSegPageHeader == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "get segment page wrong, old slot id:%" PRIu64, dirSlotIdOld);
        return GMERR_OUT_OF_MEMORY;
    }

    // 预分配double size需要的页
    uint32_t preAllocCount = 0;
    Status ret = HashPreAllocHashDirSegPage(idxCtx, &preAllocCount, segOld);
    if (ret != GMERR_OK) {
        SeLeavePage(memRunCtx->pageMgr, oldSegPageAddr, true);
        return ret;
    }

    HashPageLockTL(idxCtx, oldSegPageHeader, true, &oldSegPageAddr);
    HashDirSegmentT newSeg = {0};
    ret = HashExpandHashSegment(idxCtx, &newSeg, segOld, oldSegPageHeader);  // expand hash seg and rehash
    if (ret != GMERR_OK) {
        HashPageUnLockTL(idxCtx, oldSegPageHeader, true);
        SeLeavePage(memRunCtx->pageMgr, oldSegPageAddr, true);
        HashFreeHashDirPage(ht, preAllocCount);
        DB_ASSERT(ccehMeta->dirPageCount == (ccehMeta->dir.dirCap - 1u) / ccehMeta->hashSegNumPerPage + 1u);
        DB_LOG_ERROR(ret, "expand hash segment unsucc");
        return ret;
    }

    HashUpdateSegDepthAndPattern(para.hashCode, segOld, &newSeg);
    ret = HashUpdateDirectory(idxCtx, segOld, para, newSeg, count);
    DB_ASSERT(ccehMeta->dirPageCount == (ccehMeta->dir.dirCap - 1u) / ccehMeta->hashSegNumPerPage + 1u);
    HashPageUnLockTL(idxCtx, oldSegPageHeader, true);
    HashRedoForCopyPageData(oldSegPageAddr, ht->contentSize, oldSegPageHeader, SeGetCurRedoCtx());
    SeLeavePage(memRunCtx->pageMgr, oldSegPageAddr, true);
    return ret;
}

static Status HashExpandUpdateSegAndDir(IndexCtxT *idxCtx, ExpandHashParaT para, uint32_t *count)
{
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx;
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t blockId = para.segId / ht->memMgr.ccehMeta.hashSegNumPerPage;
    PageIdT dirPageId = memRunCtx->pageId[blockId];
    uint8_t *page = NULL;
    Status ret = SeGetPage(memRunCtx->pageMgr, dirPageId, &page, ENTER_PAGE_WRITE, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "could not get dir page: |%u %u|", dirPageId.deviceId, dirPageId.blockId);
        return ret;
    }

    uint8_t *pageBody = page + HASH_PAGE_HEAD_SIZE;
    ret = HashExpandUpdateSegAndDirImpl(idxCtx, para, pageBody, count);
    SeLeavePage(memRunCtx->pageMgr, dirPageId, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "expand dir and segment wrong");
    }
    return ret;
}

static Status HashExpandHashTable(
    const HtInsertProcResT *res, IndexCtxT *idxCtx, HtHashCodeT hashCode, uint32_t *expandCount)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    if (SECUREC_UNLIKELY(ht->idxBase.validCode != HASH_INDEX_VALID_CODE)) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "Hash table's run context is novalid");
        return GMERR_INTERNAL_ERROR;
    }
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    if (SECUREC_UNLIKELY(res->dirCap < ccehMeta->dir.dirCap)) {
        return GMERR_OK;
    }
    DB_ASSERT(res->dirCap == ccehMeta->dir.dirCap);  // 此时没有其他线程触发扩容
    SeRunCtxT *seRunCtxPtr = idxCtx->idxOpenCfg.seRunCtx;
    if (SECUREC_UNLIKELY(seRunCtxPtr == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Cant find se runctx ptr when expand hash table");
        return GMERR_OUT_OF_MEMORY;
    }
    uint32_t segId = HtGetPattern(hashCode, ccehMeta->dir.dirCap);
    ExpandHashParaT expandPara = {.hashCode = hashCode, .segId = segId};
    return HashExpandUpdateSegAndDir(idxCtx, expandPara, expandCount);
}

static Status HashTrySegInsertOnce(IndexCtxT *idxCtx, HashInsertParaT *para, HtInsertProcResT *res)
{
    DB_POINTER3(idxCtx, para, idxCtx->idxRunCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    res->dirCap = ht->memMgr.ccehMeta.dir.dirCap;
    HashPageInfoT pageInfo = SE_INVALID_HASH_PAGE_INFO;
    Status ret = HashGetSegmentPageByHashKey(idxCtx, para->hashCode, ENTER_PAGE_WRITE, &pageInfo);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get segment page by hashcode wrong");
        return ret;
    }

    PageIdT *pageAddr = &pageInfo.pageAddr;
    uint8_t *pageBody = pageInfo.virtAddr;
    // atomic set the entries other than; make sure hashEntryT is 16bytes aligned first
    AUTO_HASH_PAGE_LOCK_TL(
        idxCtx, pageBody, true, pageAddr, (ret = HashCheckUniqueAndInsertEmptySlot(idxCtx, para, &pageInfo, res)));
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx;
    SeLeavePage(memRunCtx->pageMgr, *pageAddr, true);
    return ret;
}

static Status HashTrySegInsert(IndexCtxT *idxCtx, HashInsertParaT *para, HtInsertProcResT *res)
{
    DB_POINTER2(idxCtx, para);
    Status ret;
    uint32_t lockConflictTryCnt = 0;
    uint64_t begin = DbRdtsc();
    ContinueTryLockMethodT lockMethod = {.continueTryLock = HashRLockLLBase, .continueTryUnlock = HashRUnlockLLBase};
    do {
        AUTO_HASH_RLOCK_TL(idxCtx, (ret = HashTrySegInsertOnce(idxCtx, para, res)));
    } while (IdxContinueTryBatched(idxCtx, &lockConflictTryCnt, ret, begin, lockMethod));
    return ret;
}

ALWAYS_INLINE static HashInsertParaT HashInitInsertPara(
    const IndexCtxT *idxCtx, const HashTableT *ht, IndexKeyT hashKey, const TupleAddr addr, bool isUndo)
{
    HtHashCodeT hashCode = HtGetHashCode(hashKey);
    uint32_t slot = HashGetTargetSlot(idxCtx, hashCode);
    HashInsertParaT insertPara = {.addr = addr,
        .hashKey = hashKey,
        .version = DB_INVALID_TRX_ID,
        .hashCode = hashCode,
        .targetSlot = slot,
        .lastAddr = HEAP_INVALID_ADDR,
        .isUndo = isUndo,
        .isMultiVersion = false};
    return insertPara;
}

static Status HashInsertInternal(IndexCtxT *idxCtx, IndexKeyT hashKey, const TupleAddr addr, bool isUndo)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t expandCount = 0;
    HtInsertProcResT res = {true, false, 0, 0};
    HashInsertParaT insertPara = HashInitInsertPara(idxCtx, ht, hashKey, addr, isUndo);
    Status ret = GMERR_INSUFFICIENT_RESOURCES;
    // retry after segment split
    (void)DbAtomicInc64(&ht->hashInsertCnt);  // 有insert就计数+1，不考虑是否成功插入
    while (expandCount <= HASH_ONE_INSERT_EXPAND_LIMITED) {
        ret = HashTrySegInsert(idxCtx, &insertPara, &res);
        if (!res.needExpand) {
            break;
        }
        AUTO_HASH_WLOCK_TL(idxCtx, (ret = HashExpandHashTable(&res, idxCtx, insertPara.hashCode, &expandCount)));
        if (ret == GMERR_OUT_OF_MEMORY) {
            DB_LOG_AND_SET_LASERR(ret, "Unable to expand hash table, indexId %" PRIu32, idxCtx->idxMetaCfg.indexId);
            break;
        }
        if (SECUREC_UNLIKELY(expandCount > HASH_ONE_INSERT_EXPAND_LIMITED)) {
            DB_LOG_AND_SET_LASERR(GMERR_INSUFFICIENT_RESOURCES,
                "|SE-CCEH|Expand exceed %" PRIu32 " times in a insert, indexId %" PRIu32, expandCount,
                idxCtx->idxMetaCfg.indexId);
            ret = GMERR_INSUFFICIENT_RESOURCES;  // unlikely in this branch
            break;
        }
    }

    return ret;
}

static Status HashIndexInsertImpl(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    DB_POINTER2(idxCtx, idxKey.keyData);
    uint8_t cmpRes = (uint8_t)INDEX_FILTER_SATISFIED;
    Status ret = IdxCheckFilterSatisfied(idxCtx, idxKey, &cmpRes);
    if (cmpRes == (uint8_t)INDEX_FILTER_UNSATISFIED || ret != GMERR_OK) {
        return ret;
    }

    if (SECUREC_UNLIKELY(idxCtx->idxOpenCfg.callbackFunc.keyCmp == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "|SE-Hash| HUIU: hash comparing unsucc");
        return GMERR_INTERNAL_ERROR;
    }
    ret = HashInsertInternal(idxCtx, idxKey, addr, false);
    if (SECUREC_UNLIKELY(ret == GMERR_LOCK_NOT_AVAILABLE)) {
        DB_SET_LASTERR(ret, "Hash acquire trx lock unsucc");
    }
    return ret;
}

Status HashIndexInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    Status ret = HashIndexInsertImpl(idxCtx, idxKey, addr);
    if (ret != GMERR_OK) {
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    return DbGetExternalErrno(RedoLogEnd(redoCtx, true));
}

Status HashIndexBatchInsert(IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum)
{
    DB_POINTER3(idxCtx, idxKey, addr);
    Status ret = GMERR_OK;
    HashIndexBatchBegin(idxCtx, true);
    idxCtx->isBatch = true;
    for (uint32_t i = 0; i < batchNum; ++i) {
        ret = HashIndexInsert(idxCtx, idxKey[i], addr[i].addrOut);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_AND_SET_LASERR(ret,
                "|SE-IDX|Hash index batch insert unsucc, i %" PRIu32 ",batch num %" PRIu32 ", indexId %" PRIu32, i,
                batchNum, idxCtx->idxMetaCfg.indexId);
            break;
        }
    }
    idxCtx->isBatch = false;
    HashIndexBatchEnd(idxCtx, true);
    return ret;
}

// 走到delete说明找到一个当前隔离级别可见的数据
static Status HashIndexDeleteImpl(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr, IndexRemoveParaT removePara)
{
    DB_POINTER3(idxCtx, idxCtx->idxHandle, idxKey.keyData);
    uint8_t cmpRes = (uint8_t)INDEX_FILTER_SATISFIED;
    Status ret = IdxCheckFilterSatisfied(idxCtx, idxKey, &cmpRes);
    if (cmpRes == (uint8_t)INDEX_FILTER_UNSATISFIED || ret != GMERR_OK) {
        return ret;
    }

    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    DB_POINTER(idxCtx->idxOpenCfg.callbackFunc.keyCmp);
    DB_ASSERT(ht->memMgr.ccehMeta.hashEntryNumPerPage != 0u);
    HtHashCodeT hashCode = HtGetHashCode(idxKey);
    HashLookupParaT para = {
        .isUndo = false,
        .isRemove = true,
        .isGC = removePara.isGc,
        .isErase = removePara.isErase,
        .isMarkDelete = !HashIndexIsMultiVersion(ht),
        .addr = addr,
        .hashKey = idxKey,
        .hashCode = hashCode,
        .targetSlot = HashGetTargetSlot(idxCtx, hashCode),
    };
    ret = HashFindHashEntry(idxCtx, &para);
    if (ret == GMERR_NO_DATA) {
        ret = GMERR_OK;
    }
    return ret;
}

Status HashIndexDelete(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr, IndexRemoveParaT removePara)
{
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    if (redoCtx == NULL || removePara.isGc) {
        return HashIndexDeleteImpl(idxCtx, idxKey, addr, removePara);
    }
    RedoLogBegin(redoCtx);
    Status ret = HashIndexDeleteImpl(idxCtx, idxKey, addr, removePara);
    if (ret != GMERR_OK) {
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    return DbGetExternalErrno(RedoLogEnd(redoCtx, true));
}

Status HashIndexBatchDelete(
    IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum, IndexRemoveParaT removePara)
{
    DB_POINTER3(idxCtx, idxKey, addr);
    Status ret = GMERR_OK;
    HashIndexBatchBegin(idxCtx, true);
    for (uint32_t i = 0; i < batchNum; ++i) {
        ret = HashIndexDelete(idxCtx, idxKey[i], addr[i].addrOut, removePara);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret,
                "|SE-IDX|Hash index batch delete unsucc, i %" PRIu32 ",batch num %" PRIu32 ", indexId %" PRIu32, i,
                batchNum, idxCtx->idxMetaCfg.indexId);
            break;
        }
    }
    HashIndexBatchEnd(idxCtx, true);
    return ret;
}

Status HashIndexBatchUpdate(
    IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo[], uint32_t batchNum, IndexRemoveParaT removePara)
{
    DB_POINTER2(idxCtx, updateInfo);
    Status ret = GMERR_OK;
    HashIndexBatchBegin(idxCtx, true);
    for (uint32_t i = 0; i < batchNum; ++i) {
        ret = HashIndexDelete(idxCtx, updateInfo[i].oldIdxKey, updateInfo[i].oldAddr, removePara);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret,
                "|SE-IDX|Hash index batch update unsucc when delete, i %" PRIu32 ",batch num %" PRIu32
                ", indexId %" PRIu32,
                i, batchNum, idxCtx->idxMetaCfg.indexId);
            break;
        }
        ret = HashIndexInsert(idxCtx, updateInfo[i].newIdxKey, updateInfo[i].newAddr);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret,
                "|SE-IDX|Hash index batch update unsucc when insert, i %" PRIu32 ",batch num %" PRIu32
                ", indexId %" PRIu32,
                i, batchNum, idxCtx->idxMetaCfg.indexId);
            break;
        }
    }
    HashIndexBatchEnd(idxCtx, true);
    return ret;
}

Status HashIndexUndoInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    DB_POINTER3(idxCtx, idxCtx->idxHandle, idxKey.keyData);
    uint8_t cmpRes = (uint8_t)INDEX_FILTER_SATISFIED;
    Status ret = IdxCheckFilterSatisfied(idxCtx, idxKey, &cmpRes);
    if (cmpRes == (uint8_t)INDEX_FILTER_UNSATISFIED || ret != GMERR_OK) {
        return ret;
    }
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    DB_POINTER(idxCtx->idxOpenCfg.callbackFunc.keyCmp);
    DB_ASSERT(ht->memMgr.ccehMeta.hashEntryNumPerPage != 0u);
    HtHashCodeT hashCode = HtGetHashCode(idxKey);
    HashLookupParaT para = {
        .isUndo = true,
        .isRemove = true,
        .isGC = false,
        .isErase = true,
        .isMarkDelete = !HashIndexIsMultiVersion(ht),
        .addr = addr,
        .hashKey = idxKey,
        .hashCode = hashCode,
        .targetSlot = HashGetTargetSlot(idxCtx, hashCode),
    };
    ret = HashFindHashEntry(idxCtx, &para);
    if (ret == GMERR_NO_DATA) {
        ret = GMERR_OK;
    }
    return ret;
}

Status HashIndexUndoRemove(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    DB_POINTER2(idxCtx, idxKey.keyData);
    if (idxCtx->idxOpenCfg.callbackFunc.keyCmp == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "|SE-Hash| HUIU: hash comparing unsucc");
        return GMERR_INTERNAL_ERROR;
    }
    uint8_t cmpRes = (uint8_t)INDEX_FILTER_SATISFIED;
    Status ret = IdxCheckFilterSatisfied(idxCtx, idxKey, &cmpRes);
    if (cmpRes == (uint8_t)INDEX_FILTER_UNSATISFIED || ret != GMERR_OK) {
        return ret;
    }
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    // OCC 下删除不会标记，所以删除回滚的时候不用进行任何操作。
    if (HashIndexIsMultiVersion(ht)) {
        return GMERR_OK;
    }
    return HashInsertInternal(idxCtx, idxKey, addr, true);
}

Status HashIndexGetKeyCount(IndexCtxT *idxCtx, IndexKeyT idxKey, uint64_t *count)
{
    DB_POINTER3(idxCtx, idxKey.keyData, count);
    *count = 0;
    bool isFound = false;
    Status ret = HashIndexLookup(idxCtx, idxKey, NULL, &isFound);
    if (ret != GMERR_OK || !isFound) {
        return ret;
    }
    *count = 1;
    return GMERR_OK;
}
