/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: se_persistence_hash_index_ddl.c
 * Description: Implementation of Hash Index
 * Author: huangboxin
 * Create: 2024/3/25
 */

#include "db_utils.h"
#include "se_buffer_pool_inner.h"
#include "se_hash_redo_am.h"
#include "se_page_mgr.h"
#include "se_persistence_hash_index.h"
#include "se_persistence_hash_index_stash_page.h"
#include "db_inter_process_rwlatch.h"

#define HASH_TRUNCATE_PAGE_CACHE_NUM 2
#define MV_ENTRY_PER_BUCKET 3
#define HASH_DEFAULT_DEPTH 2
#define MAX_PRELOAD_LIMIT (0.8)

uint32_t HashGetTargetSlot(const IndexCtxT *idxCtx, HtHashCodeT hashCode)
{
    // 因为必须保证bucket数量是n的倍数，每个bucket中包含的entry数量不是均分的，前splitCount个bucket中的entry的数量为
    // leftBucketCount，后（1 << ccehMeta->hashSegBits) - splitCount个bucket的entry数量为leftBucketCount - 1
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    uint32_t bucketIndex = hashCode >> (SE_HASHCODE_BIT_SIZE - ccehMeta->hashSegBits);
    DB_ASSERT(bucketIndex < (uint32_t)(1 << ccehMeta->hashSegBits));
    if (bucketIndex < ccehMeta->splitIndex) {  // 前半部分
        DB_ASSERT(bucketIndex * ccehMeta->leftBucketCount < ccehMeta->hashEntryNumPerPage);
        return bucketIndex * ccehMeta->leftBucketCount;
    }

    // 后半部分
    uint32_t leftCount = ccehMeta->splitIndex * ccehMeta->leftBucketCount;
    uint32_t targetSlot = leftCount + (bucketIndex - ccehMeta->splitIndex) * (ccehMeta->leftBucketCount - 1);
    DB_ASSERT(targetSlot < ccehMeta->hashEntryNumPerPage);

    return targetSlot;
}

static Status HashMemRunCtxInit(HashMemRunCtxInitParaT *initPara, HashMemRunCtxT *memRunCtx)
{
    DB_POINTER2(initPara->memMgr, memRunCtx);
    memRunCtx->memMgr = initPara->memMgr;
    memRunCtx->pageId = (PageIdT *)DbShmPtrToAddr(initPara->pageAddr);
    memRunCtx->pagePtrArrCapacity = initPara->dirPageCaps;
    memRunCtx->trcPageCache = initPara->trcPageCache;

    if (memRunCtx->pageMgr == NULL) {
        SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance((uint16_t)memRunCtx->memMgr->seInstanceId);
        DB_POINTER(seInstance);
        memRunCtx->pageMgr = (PageMgrT *)seInstance->pageMgr;
    }
    return memRunCtx->pageMgr == NULL ? GMERR_UNEXPECTED_NULL_VALUE : GMERR_OK;
}

// start from data
static Status HashCreateInitInsAndCtx(uint16_t instanceId, SeInstanceT **seInstance, DbMemCtxT **htMemCtx)
{
    DB_POINTER(htMemCtx);
    *seInstance = (SeInstanceT *)SeGetInstance(instanceId);
    if (*seInstance == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|SE-Hash| HCIN: hash's SE instance is wrong");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *htMemCtx = (DbMemCtxT *)DbGetShmemCtxById((*seInstance)->hashIndexShmMemCtxId, instanceId);
    if (*htMemCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "|SE-Hash| HCIN: hash's mem context is wrong");
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

static void InitHtMemMgr(HashTableT *newHt, const SeInstanceT *seInstance, const IndexMetaCfgT *idxCfg)
{
    newHt->memMgr.fileId = 0;
    newHt->memMgr.seInstanceId = seInstance->instanceId;
    newHt->memMgr.tableSpaceIndex = idxCfg->tableSpaceIndex;
    newHt->memMgr.allocFirst = true;
    newHt->memMgr.isVertexUseRsm = idxCfg->isVertexUseRsm;
}

static void InitMVPageInfo(HashTableT *newHt)
{
    newHt->mvPageInfo.reserve = 0;
    newHt->mvPageInfo.mvPageUsedCnt = 0;
    newHt->mvPageInfo.curMVPageBlockId = DB_INVALID_UINT16;
    DbSpinInit(&newHt->mvPageInfo.lock);
}

static void InitHashTableMetaForPersitence(HashTableMetaT *htMeta, SeInstanceT *se, const IndexMetaCfgT *idxCfg)
{
    htMeta->dirDepth = HASH_DEFAULT_DEPTH;  // 初始化深度为2，总的页数为1（meta) + 1(dir) + segment(4) = 6
    htMeta->idxBase.indexCfg = *idxCfg;
    htMeta->magicNumber = 0;
    htMeta->version = 0;
    htMeta->dirPageId = SE_INVALID_PAGE_ADDR;
    htMeta->stashPageId = SE_INVALID_PAGE_ADDR;
    DbRWLatchInit(&(htMeta->idxBase.idxLatch));
    htMeta->idxBase.shmemCtxId = se->hashIndexShmMemCtxId;
    DbRWLatchInit(&htMeta->idxBase.stashPageLatch);
    IdxMarkUnConstructed(&htMeta->idxBase);
    htMeta->idxBase.validCode = HASH_INDEX_VALID_CODE;
}

static Status AllocAndSetPageArrMem(
    SeInstanceT *se, const PageIdT firstDirPageId, HashMemRunCtxT *memRunCtx, HashTableT *ht)
{
    DbMemCtxT *htMemCtx = (DbMemCtxT *)DbGetShmemCtxById(se->hashIndexShmMemCtxId, se->instanceId);
    if (htMemCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "|SE-Hash| HCIN: hash's mem context is wrong");
        return GMERR_INTERNAL_ERROR;
    }

    uint32_t pageArrSize = ht->memMgr.ccehMeta.dirPageCount;
    PageIdT *pageAddr = (PageIdT *)SeShmAlloc(htMemCtx, pageArrSize * (uint32_t)sizeof(PageIdT), &ht->pageAddr);
    if (pageAddr == NULL || !DbIsShmPtrValid(ht->pageAddr)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "unable to alloc page array, size: %" PRIu32 ", index id: %" PRIu32,
            pageArrSize, ht->idxBase.indexCfg.indexId);
        return GMERR_OUT_OF_MEMORY;
    }

    if (!DbIsPageIdValid(firstDirPageId) || memRunCtx == NULL) {  // 初始化
        for (uint32_t i = 0; i < pageArrSize; i++) {
            pageAddr[i] = SE_INVALID_PAGE_ADDR;
        }
        return GMERR_OK;
    }

    // 从文件中读取
    uint32_t i = 0;
    pageAddr[0] = firstDirPageId;
    PageIdT nextPage = SE_INVALID_PAGE_ADDR;
    while (i < pageArrSize - 1 && DbIsPageIdValid(pageAddr[i])) {
        Status ret = HtGetNextPageId(memRunCtx, pageAddr[i], &nextPage);
        if (ret != GMERR_OK) {
            SE_ERROR(ret, "|Hash| get next page of this page(%" PRIu32 ", %" PRIu32 ") in pageList wrong",
                pageAddr[i].blockId, pageAddr[i].deviceId);
            return ret;
        }
        ++i;
        pageAddr[i] = nextPage;
    }

    // 实际目录页和计算出来的对不上，前一个条件少页
    if (i != pageArrSize - 1) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "|Hash| hash dir count wrong: %u", pageArrSize);
        return GMERR_INTERNAL_ERROR;
    }

    return GMERR_OK;
}

static Status HashSegConstructor(HashTableT *newHt, HashDirSegmentT *seg)
{
    DB_POINTER(newHt);
    CcehMemMetaT *ccehMeta = &newHt->memMgr.ccehMeta;
    HashMemRunCtxT memRunCtx = {0};
    HashMemRunCtxInitParaT initPara = {0};
    GetHashMemRunCtxInitPara(newHt, &initPara);
    Status ret = HashMemRunCtxInit(&initPara, &memRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "|SE-Hash| unable to init mem runctx, indexId %" PRIu32 ".", newHt->idxBase.indexCfg.indexId);
        return ret;
    }

    HashPageInfoT pageInfo = SE_INVALID_HASH_PAGE_INFO;
    ret = HtAllocPage(&memRunCtx, &pageInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    seg->pageAddr.pageAddr = pageInfo.pageAddr;
    (void)memset_s(pageInfo.virtAddr, newHt->contentSize, 0x00, newHt->contentSize);
    ccehMeta->segPageCount++;
    PageHeadT *segPage = pageInfo.head;
    DbRWLatchW(&segPage->lock);
    uint8_t *segPageEntry = pageInfo.virtAddr;
    HashEntryT invalidHashEntry = {.hashEntryBase = {0}, .addr = HEAP_INVALID_ADDR};
    for (uint32_t i = 0; i < newHt->memMgr.ccehMeta.hashEntryNumPerPage; ++i) {
        SetHashEntryBySlot(newHt, segPageEntry, i, invalidHashEntry);
    }
    DbRWUnlatchW(&segPage->lock);
    HashRedoForSegmentPageInit(pageInfo.pageAddr, SeGetCurRedoCtx());
    HtLeavePage(&memRunCtx, &pageInfo);
    return GMERR_OK;
}

static Status GenFreePageParam(HashMemRunCtxT *memRunCtx, HashPageInfoT *pageInfo, FreePageParamT *freePageParam)
{
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(memRunCtx->memMgr->seInstanceId);
    if (seInstance == NULL) {
        SE_ERROR(
            INVALID_PARAMETER_VALUE_INTER, "storage instance %" PRIu16 " novalid", memRunCtx->memMgr->seInstanceId);
        return INVALID_PARAMETER_VALUE_INTER;
    }
    freePageParam->spaceId = memRunCtx->memMgr->tableSpaceIndex;
    freePageParam->addr = pageInfo->pageAddr;
    freePageParam->dbInstance = seInstance->dbInstance;
    freePageParam->labelRsmUndo = NULL;
    SeInitCachePagePara(&freePageParam->cachePagePara);
    return GMERR_OK;
}

static Status HashInitDirSegPage(
    HashTableT *ht, HashInitDirSegPageParaT para, uint32_t *segPageId, PageHeadT *dirPageHead)
{
    DB_POINTER(segPageId);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    HashMemRunCtxT memRunCtx = {0};
    HashMemRunCtxInitParaT initPara = {0};
    GetHashMemRunCtxInitPara(ht, &initPara);
    Status ret = HashMemRunCtxInit(&initPara, &memRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "|SE-Hash| unable to init mem runctx");
        return ret;
    }

    HashPageInfoT pageInfo = SE_INVALID_HASH_PAGE_INFO;
    ret = HtAllocPageForDir(&memRunCtx, para.blockId, &pageInfo);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-Hash| unable to alloc dir page");
        return ret;
    }

    ccehMeta->dirPageCount++;
    dirPageHead = pageInfo.head;
    dirPageHead->addr = pageInfo.pageAddr;
    DbInterProcRWLatchW(&dirPageHead->lock);
    HashDirSegmentT *seg = (HashDirSegmentT *)pageInfo.virtAddr;
    FreePageParamT freePageParam = {0};
    ret = GenFreePageParam(&memRunCtx, &pageInfo, &freePageParam);
    // 往DirPage中填入SegmentT
    for (uint32_t j = 0; j < para.initDirBlockRemain; j++) {
        seg->pattern = *segPageId;
        seg->segDepth = para.dirDepth;
        ret = HashSegConstructor(ht, seg);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-Hash| unable to constructor seg page");
            for (uint32_t k = j; k < ccehMeta->hashSegNumPerPage; k++) {
                *seg = InvalidHashDirSegment();
                seg++;
            }
            SeLeavePage(memRunCtx.pageMgr, pageInfo.pageAddr, true);
            (void)SeFreePage(memRunCtx.pageMgr, &freePageParam);
            DbRWUnlatchW(&dirPageHead->lock);
            return ret;
        }
        HashRedoForSetSegment(pageInfo.pageAddr, j, seg, SeGetCurRedoCtx());
        seg++;
        (*segPageId)++;
    }
    DbInterProcRWUnlatchW(&dirPageHead->lock);
    SeLeavePage(memRunCtx.pageMgr, pageInfo.pageAddr, true);
    return GMERR_OK;
}

static Status InitEveryDirPageForPersistence(
    HashTableT *ht, uint32_t initDirBlockCnt, HashInitDirSegPageParaT para, uint32_t *segPageId)
{
    PageHeadT *dirPageHeadNode = NULL;
    for (uint32_t i = 0; i < initDirBlockCnt; i++) {
        // current page head
        PageHeadT *dirPageHead = NULL;
        para.blockId = i;
        Status ret = HashInitDirSegPage(ht, para, segPageId, dirPageHead);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(
                ret, "|SE-Hash| init dir seg page unsucessful, i:%" PRIu32 ", seg page id:%" PRIu32, i, *segPageId);
            return ret;
        }
        if (i > 0) {
            // construct dir page list
            dirPageHeadNode->nextPageId = dirPageHead->addr;
        }
        // record prev node
        dirPageHeadNode = dirPageHead;
    }
    if (dirPageHeadNode) {
        dirPageHeadNode->nextPageId = SE_INVALID_PAGE_ADDR;
    }
    return GMERR_OK;
}

static Status HashInitFullDirSeg(HashTableT *ht, uint32_t initDirBlockCnt, uint32_t dirDepth, uint32_t *segPageId)
{
    DB_POINTER(ht);

    HashInitDirSegPageParaT para = {0};
    para.initDirBlockRemain = ht->memMgr.ccehMeta.hashSegNumPerPage;
    para.dirDepth = dirDepth;

    // 初始化每一个Dir页
    return InitEveryDirPageForPersistence(ht, initDirBlockCnt, para, segPageId);
}

static Status HashDirConstructor(HashTableT *newHt, uint32_t dirDepth)  // save to page
{
    DB_POINTER(newHt);
    uint32_t capacity = (1u << dirDepth);
    // set page 0 dir head page
    CcehMemMetaT *ccehMeta = &newHt->memMgr.ccehMeta;
    DB_ASSERT(ccehMeta->hashSegNumPerPage > 0);
    uint32_t initDirBlockCnt = capacity / ccehMeta->hashSegNumPerPage;  // 需要的Dir页个数（向下取整）
    uint32_t initDirBlockRemain = capacity % ccehMeta->hashSegNumPerPage;  // 剩余的segmentT存放在多余的一个Dir页中
    uint32_t indexId = newHt->idxBase.indexCfg.indexId;
    uint32_t segPageId = 0u;
    Status ret = HashInitFullDirSeg(newHt, initDirBlockCnt, dirDepth, &segPageId);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "|SE-Hash| unable to init full dirSeg, indexId %" PRIu32 ", dir blk cnt:%" PRIu32,
            indexId, initDirBlockCnt);
        return ret;
    }
    // set page 1 segment pagesegDepth
    if (initDirBlockRemain != 0u) {
        HashInitDirSegPageParaT para = {0};
        para.initDirBlockRemain = initDirBlockRemain;
        para.blockId = initDirBlockCnt;
        para.dirDepth = dirDepth;
        PageHeadT *dirPageHead = NULL;
        ret = HashInitDirSegPage(newHt, para, &segPageId, dirPageHead);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "|SE-Hash| unable to init dirSeg, indexId %" PRIu32 ".", indexId);
            return ret;
        }
    }
    return GMERR_OK;
}

static Status HashFreeSegPagesInner(
    HashTableT *ht, HashMemRunCtxT *memRunCtx, HashPageInfoT *pageInfo, uint32_t *freeSegCount)
{
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    HashDirectoryHeadT *dir = &(ccehMeta->dir);
    for (uint32_t segSlotId = 0; segSlotId < ccehMeta->hashSegNumPerPage; segSlotId++) {
        DEBUG_EXTRAMSG(EXTRAMSG_LOG_INDEX,
            "|Hash-dmsg| freeSegPage indexId %" PRIu32 ", hashSegNumPerPage %" PRIu32 ", segSlotId %" PRIu32
            ", freeSegCount %" PRIu32 ", dir dirCap %" PRIu32 ".",
            ht->idxBase.indexCfg.indexId, ccehMeta->hashSegNumPerPage, segSlotId, *freeSegCount, dir->dirCap);
        if (*freeSegCount >= dir->dirCap) {
            return GMERR_PROGRAM_LIMIT_EXCEEDED;
        }
        HashDirSegmentT *seg = (HashDirSegmentT *)(void *)(pageInfo->virtAddr + segSlotId * sizeof(HashDirSegmentT));
        DEBUG_EXTRAMSG(EXTRAMSG_LOG_INDEX,
            "|Hash-dmsg| freeSegPage try to indexId %" PRIu32 ", pattern %" PRIu32 ", segDepth %" PRIu32
            ", pageId %" PRIu32 ", blockId %" PRIu32 ".",
            ht->idxBase.indexCfg.indexId, seg->pattern, seg->segDepth, seg->pageAddr.pageId, seg->pageAddr.blockId);

        if (!DbIsPageIdValid(seg->pageAddr.pageAddr)) {
            continue;
        }
        Status ret = HtFreePage(memRunCtx, seg->pageAddr.pageAddr);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-Hash|Unable to free hash page|%" PRIu32 ",%" PRIu32 ",%" PRIu32 "|",
                ht->memMgr.fileId, seg->pageAddr.pageId, seg->pageAddr.blockId);
        }
        (*freeSegCount)++;
    }
    return GMERR_OK;
}

static void HashFreeSegPages(HashTableT *ht)
{
    DB_POINTER(ht);
    ht->htVersion++;
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    uint32_t freeSegCount = 0;
    HashPageInfoT pageInfo = SE_INVALID_HASH_PAGE_INFO;

    HashMemRunCtxT memRunCtx = {0};
    HashMemRunCtxInitParaT initPara = {0};
    GetHashMemRunCtxInitPara(ht, &initPara);
    Status ret = HashMemRunCtxInit(&initPara, &memRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "|SE-Hash| unable to init mem runctx, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return;
    }
    for (uint32_t i = 0; i < ccehMeta->dirPageCount; i++) {
        ret = HtGetDirPage(&memRunCtx, i, ENTER_PAGE_WRITE, &pageInfo);
        DEBUG_EXTRAMSG(EXTRAMSG_LOG_INDEX,
            "|Hash-dmsg| HtGetDirPage indexId %" PRIu32 ", ret = %" PRId32 ", dirPageCnt %" PRIu32 ", i %" PRIu32 ".",
            ht->idxBase.indexCfg.indexId, (int32_t)ret, ccehMeta->dirPageCount, i);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            ccehMeta->segPageCount = 0;
            DB_LOG_ERROR(ret,
                "|INDEX|Hash index get dir page unsucc, i:%" PRIu32 ", dir page count:%" PRIu32
                ", seg page count:%" PRIu32,
                i, ccehMeta->dirPageCount, ccehMeta->segPageCount);
            return;
        }
        ret = HashFreeSegPagesInner(ht, &memRunCtx, &pageInfo, &freeSegCount);
        SeLeavePage((&memRunCtx)->pageMgr, pageInfo.pageAddr, true);
        if (ret != GMERR_OK) {
            break;
        }
    }
    ccehMeta->segPageCount = 0u;
    return;
}

static void FreePageArrMem(SeRunCtxHdT seRunCtx, HashTableT *ht)
{
    SeInstanceT *seInstance = NULL;
    DbMemCtxT *htMemCtx = NULL;
    Status ret = HashCreateInitInsAndCtx(seRunCtx->instanceId, &seInstance, &htMemCtx);
    if (ret != GMERR_OK) {
        return;
    }
    if (DbIsShmPtrValid(ht->pageAddr)) {
        DbShmemCtxFree(htMemCtx, ht->pageAddr);
        ht->pageAddr = DB_INVALID_SHMPTR;
    }
}

static Status HashCreateHashTableForPersistence(SeRunCtxHdT seRunCtx, HashTableT *newHt, SeInstanceT *se)
{
    DB_POINTER2(newHt, se);
    // 申请目录页数组内存
    Status ret = AllocAndSetPageArrMem(se, SE_INVALID_PAGE_ADDR, NULL, newHt);
    if (ret != GMERR_OK) {
        return ret;
    }

    CcehMemMetaT *ccehMeta = &newHt->memMgr.ccehMeta;
    DB_ASSERT(ccehMeta->hashSegNumPerPage > 0);
    DB_ASSERT(ccehMeta->hashEntryNumPerPage > 0);
    ret = HashDirConstructor(newHt, ccehMeta->dir.dirDepth);
    DEBUG_EXTRAMSG(EXTRAMSG_LOG_INDEX, "|Hash-dmsg| HashDirConstructor indexId %" PRIu32 ", ret = %" PRId32 ".",
        newHt->idxBase.indexCfg.indexId, ret);
    if (ret != GMERR_OK) {
        HashFreeSegPages(newHt);
        HashFreeHashDirPage(newHt, ccehMeta->dirPageCount);
        FreePageArrMem(seRunCtx, newHt);
        DB_LOG_ERROR(ret, "|SE-Hash| Unable to construct hashtable dir, depth:%" PRIu32, ccehMeta->dir.dirDepth);
        return ret;
    }

    ccehMeta->dirPageCount = (ccehMeta->dir.dirCap - 1u) / ccehMeta->hashSegNumPerPage + 1u;
    ccehMeta->segPageCount = ccehMeta->dir.dirCap;  // 初始化dirCap即为segPageCount
    // 初始化时，将缩容参数置0，之后每次缩容成功后再置0，缩容时间片用完不置0
    ccehMeta->dirNextPageId = 0;
    ccehMeta->dirNextItemIdx = 0;
    return GMERR_OK;
}

static void InitHashTableBaseInfo(HashTableT *ht, SeInstanceT *se, const HashTableMetaT *htMeta)
{
    ht->idxBase = htMeta->idxBase;
    DB_ASSERT((se->seConfig.pageSize * DB_KIBI) > HASH_PAGE_HEAD_SIZE);
    DbRWLatchInit(&(ht->idxBase.idxLatch));
    DbRWLatchInit(&(ht->idxBase.stashPageLatch));
    // page struct
    // +-----------+---------+----------+
    // | PageHeadT | content | reserved |
    // +-----------+---------+----------+
    ht->contentSize = se->seConfig.pageSize * DB_KIBI - HASH_PAGE_HEAD_SIZE - DB_PAGE_RESERVED_LENGTH;
    ht->metaPageId = SE_INVALID_PAGE_ADDR;
    ht->heapTupleAddrMode = SE_HEAP_TUPLE_ADDR_64;
    ht->stashPageId = htMeta->stashPageId;
    ht->pageAddr = DB_INVALID_SHMPTR;
    ht->htAddr = DB_INVALID_SHMPTR;
    ht->hashLinklistShmMemCtxId = se->hashLinklistShmMemCtxId;
    ht->stashPageNum = 0;
    ht->pageAddrArrCapacity = 0;  // 持久化模式该值无效
    ht->pageAddrArrCapBits = 0;   // 持久化模式该值无效
    ht->heapTupleAddrMode = SE_HEAP_TUPLE_ADDR_64;
    InitMVPageInfo(ht);
}

static void InitHashTableMemMeta(HashTableT *ht, SeInstanceT *se, uint32_t depth)
{
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    ccehMeta->dir.dirDepth = depth;
    ccehMeta->dir.dirCap = 1 << depth;
    ccehMeta->hashSegNumPerPage = (uint32_t)(ht->contentSize / sizeof(HashDirSegmentT));
    uint32_t rowAddrSize = HashGetAddrSize(ht->heapTupleAddrMode);
    ccehMeta->hashEntrySize = sizeof(HtHashCodeT) + rowAddrSize;
    ccehMeta->hashEntryMetaSize = ccehMeta->hashEntrySize;
    ccehMeta->hashEntryNumPerPage = ht->contentSize / ccehMeta->hashEntrySize;
    uint32_t bucketSize = CACHELINE_SIZE * 2;                      // 一个bucket预计含有2个cacheline
    ccehMeta->hashSegBits = DbLog2(ht->contentSize / bucketSize);  // bucket的数量需要用多少个位表示
    uint32_t bucketCount = 1 << ccehMeta->hashSegBits;             // 总共有多少个bucket
    ccehMeta->leftBucketCount = ccehMeta->hashEntryNumPerPage / bucketCount;  // 平均每个bucket中有多少个entry
    ccehMeta->splitIndex = ccehMeta->hashEntryNumPerPage % bucketCount;       // 前面splitIndex个bucket数量会多1
    ccehMeta->leftBucketCount = ccehMeta->splitIndex != 0 ? (ccehMeta->leftBucketCount + 1) : ccehMeta->leftBucketCount;
    ccehMeta->dirPageCount = (ccehMeta->dir.dirCap - 1u) / ccehMeta->hashSegNumPerPage + 1u;
    DB_ASSERT(ccehMeta->leftBucketCount > 1);
}

static void InitHashTable(HashTableT *ht, SeInstanceT *se, const HashTableMetaT *htMeta, const IndexMetaCfgT *idxCfg)
{
    DB_POINTER4(se, idxCfg, ht, htMeta);
    InitHashTableBaseInfo(ht, se, htMeta);
    InitHtMemMgr(ht, se, idxCfg);
    InitHashTableMemMeta(ht, se, htMeta->dirDepth);
}

static Status HashIndexCreateForPersistenceImpl(
    SeRunCtxHdT seRunCtx, SeInstanceT *se, const IndexMetaCfgT *idxCfg, ShmemPtrT *idxShmAddr)
{
    PageMgrT *pageMgr = se->pageMgr;
    DB_POINTER(pageMgr);

    // 1.申请meta页
    PageIdT metaPageId = SE_INVALID_PAGE_ADDR;
    AllocPageParamT allocPageParam = {.spaceId = idxCfg->tableSpaceIndex,
        .trmId = 0,
        .labelId = RSM_INVALID_LABEL_ID,
        .dbInstance = se->dbInstance,
        .labelRsmUndo = NULL};  // index不使用rsmUndo
    StatusInter interErrno = SeAllocPage(pageMgr, &allocPageParam, &metaPageId);
    if (interErrno != STATUS_OK_INTER) {
        DB_LOG_AND_SET_LASERR(DbGetExternalErrno(interErrno), "|Hash| Alloc meta page: unable.");
        return DbGetExternalErrno(interErrno);
    }

    // 2.获取meta页
    PageHeadT *metaPage = NULL;
    interErrno = SeGetPage(pageMgr, metaPageId, (uint8_t **)&metaPage, ENTER_PAGE_WRITE, true);
    FreePageParamT freePageParam = {
        .spaceId = idxCfg->tableSpaceIndex, .addr = metaPageId, .dbInstance = se->dbInstance, .labelRsmUndo = NULL};
    SeInitCachePagePara(&freePageParam.cachePagePara);
    if (interErrno != STATUS_OK_INTER) {
        // AllocPage返回成功的情况下，立刻get不应该失败，如果失败就是有bug
        DB_LOG_AND_SET_LASERR(DbGetExternalErrno(interErrno), "|Hash| GetPage: unable.");
        (void)SeFreePage(pageMgr, &freePageParam);
        return DbGetExternalErrno(interErrno);
    }

    // 3.初始化持久化meta页数据
    HashTableMetaT *htMeta = (HashTableMetaT *)(void *)(metaPage + 1);
    InitHashTableMetaForPersitence(htMeta, se, idxCfg);

    // 4.初始化hash结构数据
    HashTableT newHt = {};
    InitHashTable(&newHt, se, htMeta, idxCfg);

    // 5.创建持久化hash
    Status ret = HashCreateHashTableForPersistence(seRunCtx, &newHt, se);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create hash table go wrong");
        (void)SeLeavePage(pageMgr, metaPageId, false);
        (void)SeFreePage(pageMgr, &freePageParam);
        return ret;
    }

    // 6.设置持久化元数据目录页
    PageIdT *dirPageId = (PageIdT *)DbShmPtrToAddr(newHt.pageAddr);
    htMeta->dirPageId = dirPageId[0];
    HashRedoForUpdateMeta(metaPageId, htMeta, SeGetCurRedoCtx());
    IdxConstructedFinish(&htMeta->idxBase);
    SeLeavePage(pageMgr, metaPageId, true);
    *idxShmAddr = *(ShmemPtrT *)(&(metaPageId));
    return GMERR_OK;
}

static Status HashIndexCreateForPersistence(SeRunCtxHdT seRunCtx, IndexMetaCfgT idxCfg, ShmemPtrT *idxShmAddr)
{
    DB_POINTER(idxShmAddr);
    *idxShmAddr = DB_INVALID_SHMPTR;

    // Alloc HashTable Meta Page
    SeInstanceT *seInstance = SeGetInstance(seRunCtx->instanceId);
    if (seInstance == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "|BTree| Create: null instance.");
        return GMERR_INTERNAL_ERROR;
    }

    return HashIndexCreateForPersistenceImpl(seRunCtx, seInstance, &idxCfg, idxShmAddr);
}

Status HashIndexCreate(SeRunCtxHdT seRunCtx, IndexMetaCfgT idxCfg, ShmemPtrT *idxShmAddr)
{
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    Status ret = HashIndexCreateForPersistence(seRunCtx, idxCfg, idxShmAddr);
    if (ret != GMERR_OK) {
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    return DbGetExternalErrno(RedoLogEnd(redoCtx, true));
}

static void HashFreeSegmentInDirPage(PageMgrT *pageMgr, const HashTableMetaT *htMeta, PageIdT dirPageId,
    uint32_t hashSegNumPerPage, DbInstanceHdT dbInstance)
{
    uint32_t tableSpaceIndex = htMeta->idxBase.indexCfg.tableSpaceIndex;
    uint32_t dirCap = 1 << htMeta->dirDepth;
    uint32_t segIndex = htMeta->segIndex;
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    for (uint32_t i = 0; i < hashSegNumPerPage && segIndex + i < dirCap; ++i) {
        RedoLogBegin(redoCtx);
        PageHeadT *dirPage = NULL;
        Status ret = DbGetExternalErrno(SeGetPage(pageMgr, dirPageId, (uint8_t **)&dirPage, ENTER_PAGE_WRITE, true));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret,
                "|Hash| read dir page wrong while free segment page|deviceId:%" PRIu32 ", blockId:%" PRIu32 "|",
                dirPageId.deviceId, dirPageId.blockId);
            (void)RedoLogEnd(redoCtx, false);
            return;
        }

        HashDirSegmentT *seg = (HashDirSegmentT *)(dirPage + 1) + i;
        if (DbIsPageIdValid(seg->pageAddr.pageAddr) && ((segIndex + i) >> seg->segDepth) == 0) {
            FreePageParamT freePageParam = {.spaceId = tableSpaceIndex,
                .addr = seg->pageAddr.pageAddr,
                .dbInstance = dbInstance,
                .labelRsmUndo = NULL};
            SeInitCachePagePara(&freePageParam.cachePagePara);
            ret = DbGetExternalErrno(SeFreePage(pageMgr, &freePageParam));
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "|Hash| free segment page wrong |deviceId:%" PRIu32 ", blockId:%" PRIu32 "|",
                    seg->pageAddr.pageAddr.deviceId, seg->pageAddr.pageAddr.blockId);
                // 这里不返回错误，继续往后走
            }

            seg->pageAddr.pageAddr = SE_INVALID_PAGE_ADDR;
            HashRedoForSetSegment(dirPageId, i, seg, redoCtx);
            SeLeavePage(pageMgr, dirPageId, true);
            ret = DbGetExternalErrno(RedoLogEnd(redoCtx, true));
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "|Hash| write redo log wrong");
                // 这里不返回错误，继续往后走
            }
        } else {
            SeLeavePage(pageMgr, dirPageId, false);
            (void)RedoLogEnd(redoCtx, false);
        }
    }
}

static void HashFreeSegmentInFirstDirPage(SeInstanceT *se, PageMgrT *pageMgr, PageIdT metaPageId)
{
    PageHeadT *metaPage = NULL;
    Status ret = DbGetExternalErrno(SeGetPage(pageMgr, metaPageId, (uint8_t **)&metaPage, ENTER_PAGE_NORMAL, false));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret,
            "|Hash| read meta page wrong while free directory page|deviceId:%" PRIu32 ", blockId:%" PRIu32 "|",
            metaPageId.deviceId, metaPageId.blockId);
        return;
    }

    HashTableMetaT *htMeta = (HashTableMetaT *)(metaPage + 1);
    PageIdT dirPageId = htMeta->dirPageId;
    if (!DbIsPageIdValid(dirPageId)) {
        SeLeavePage(pageMgr, metaPageId, false);
        return;
    }

    uint32_t hashSegNumPerPage = (se->seConfig.pageSize * DB_KIBI - sizeof(PageHeadT)) / sizeof(HashDirSegmentT);
    HashFreeSegmentInDirPage(pageMgr, htMeta, dirPageId, hashSegNumPerPage, se->dbInstance);
    SeLeavePage(pageMgr, metaPageId, false);
}

static void HashFreeDirectoryPage(SeInstanceT *se, PageMgrT *pageMgr, PageIdT metaPageId)
{
    PageIdT nextPageId = SE_INVALID_PAGE_ADDR;
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    do {
        HashFreeSegmentInFirstDirPage(se, pageMgr, metaPageId);
        RedoLogBegin(redoCtx);
        // 因为buffer pool页面修改有上限，此处将directory删除拆分原子性，且为了支持可重入，一次原子操作为：
        // 删除stash页，将meta页中的stashPageId指向刚删除的下一页
        // 且redo不能嵌套，SeGetPage获取一个预写的页，必须在redo begin开启才能获取，因此需要频繁获取meta页
        PageHeadT *metaPage = NULL;
        Status ret = DbGetExternalErrno(SeGetPage(pageMgr, metaPageId, (uint8_t **)&metaPage, ENTER_PAGE_WRITE, true));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret,
                "|Hash| get meta page wrong while free directory page|deviceId:%" PRIu32 ", blockId:%" PRIu32 "|",
                metaPageId.deviceId, metaPageId.blockId);
            (void)RedoLogEnd(redoCtx, false);
            break;
        }

        HashTableMetaT *htMeta = (HashTableMetaT *)(metaPage + 1);
        PageIdT dirPageId = htMeta->dirPageId;
        if (!DbIsPageIdValid(dirPageId)) {
            // directory页无效，no need free
            (void)SeLeavePage(pageMgr, metaPageId, false);
            (void)RedoLogEnd(redoCtx, false);
            break;
        }

        uint32_t dirCap = 1 << htMeta->dirDepth;
        PageHeadT *dirPage = NULL;
        ret = DbGetExternalErrno(SeGetPage(pageMgr, dirPageId, (uint8_t **)&dirPage, ENTER_PAGE_NORMAL, false));
        if (ret != GMERR_OK) {
            // 获取directory页失败，将meta页的directory指针指向无效，提交redo
            htMeta->dirPageId = SE_INVALID_PAGE_ADDR;
            htMeta->segIndex = dirCap;
            HashRedoForFreeDirPage(metaPageId, htMeta->dirPageId, htMeta->segIndex, redoCtx);
            SeLeavePage(pageMgr, metaPageId, true);
            (void)RedoLogEnd(redoCtx, true);
            break;
        }

        // 获取directory页成功，获取directory的next页，释放当前directory页，并将meta中的指针指向next页
        nextPageId = dirPage->nextPageId;
        htMeta->dirPageId = nextPageId;
        uint32_t hashSegNumPerPage = (se->seConfig.pageSize * DB_KIBI - sizeof(PageHeadT)) / sizeof(HashDirSegmentT);
        htMeta->segIndex += hashSegNumPerPage;
        htMeta->segIndex = htMeta->segIndex > dirCap ? dirCap : htMeta->segIndex;
        HashRedoForFreeDirPage(metaPageId, htMeta->dirPageId, htMeta->segIndex, redoCtx);
        uint32_t tableSpaceIndex = htMeta->idxBase.indexCfg.tableSpaceIndex;
        SeLeavePage(pageMgr, metaPageId, false);
        SeLeavePage(pageMgr, dirPageId, false);
        FreePageParamT freePageParam = {
            .spaceId = tableSpaceIndex, .addr = dirPageId, .dbInstance = se->dbInstance, .labelRsmUndo = NULL};
        SeInitCachePagePara(&freePageParam.cachePagePara);
        (void)SeFreePage(pageMgr, &freePageParam);
        (void)RedoLogEnd(redoCtx, true);
    } while (DbIsPageIdValid(nextPageId));
}

static void HashFreeStashPage(SeInstanceT *se, PageMgrT *pageMgr, PageIdT metaPageId)
{
    PageIdT nextPageId = SE_INVALID_PAGE_ADDR;
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    do {
        RedoLogBegin(redoCtx);
        // 因为buffer pool页面修改有上限，此处将stash删除拆分原子性，且为了支持可重入，一次原子操作为：
        // 删除stash页，将meta页中的stashPageId指向刚删除的下一页
        // 且redo不能嵌套，SeGetPage获取一个预写的页，必须在redo begin开启才能获取，因此需要频繁获取meta页
        PageHeadT *metaPage = NULL;
        Status ret = DbGetExternalErrno(SeGetPage(pageMgr, metaPageId, (uint8_t **)&metaPage, ENTER_PAGE_WRITE, true));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret,
                "|Hash| get meta page wrong while free stash page|deviceId:%" PRIu32 ", blockId:%" PRIu32 "|",
                metaPageId.deviceId, metaPageId.blockId);
            (void)RedoLogEnd(redoCtx, false);
            break;
        }

        HashTableMetaT *htMeta = (HashTableMetaT *)(metaPage + 1);
        PageIdT stashPageId = htMeta->stashPageId;
        if (!DbIsPageIdValid(stashPageId)) {
            // stash页无效，no need free
            (void)SeLeavePage(pageMgr, metaPageId, false);
            (void)RedoLogEnd(redoCtx, false);
            break;
        }

        PageHeadT *stashPage = NULL;
        ret = DbGetExternalErrno(SeGetPage(pageMgr, stashPageId, (uint8_t **)&stashPage, ENTER_PAGE_NORMAL, false));
        if (ret != GMERR_OK) {
            // 获取stash页失败，将meta页的stash指针指向无效，提交redo
            htMeta->stashPageId = SE_INVALID_PAGE_ADDR;
            HashRedoForSetMetaStashPage(metaPageId, htMeta->stashPageId, redoCtx);
            SeLeavePage(pageMgr, metaPageId, true);
            (void)RedoLogEnd(redoCtx, true);
            break;
        }

        // 获取stash页成功，获取stash的next页，释放当前stash页，并将meta中的指针指向next页
        nextPageId = ((HtStashPageHeadT *)(stashPage + 1))->nextPageId;
        htMeta->stashPageId = nextPageId;
        uint32_t tableSpaceIndex = htMeta->idxBase.indexCfg.tableSpaceIndex;
        HashRedoForSetMetaStashPage(metaPageId, htMeta->stashPageId, redoCtx);
        SeLeavePage(pageMgr, metaPageId, false);
        FreePageParamT freePageParam = {
            .spaceId = tableSpaceIndex, .addr = stashPageId, .dbInstance = se->dbInstance, .labelRsmUndo = NULL};
        SeInitCachePagePara(&freePageParam.cachePagePara);
        (void)SeFreePage(pageMgr, &freePageParam);
        (void)RedoLogEnd(redoCtx, true);
    } while (DbIsPageIdValid(nextPageId));
}

Status HashIndexDrop(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    PageMgrT *pageMgr = SeGetPageMgr(seRunCtx->instanceId);
    if (pageMgr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|Hash| Drop: null pagemgr.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    SeInstanceT *seInstance = SeGetInstance(seRunCtx->instanceId);
    if (seInstance == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "|BTree| Drop: null instance.");
        return GMERR_INTERNAL_ERROR;
    }

    PageIdT metaPageId = *(PageIdT *)(&(idxShmAddr));
    // free directory page(include segment page)
    HashFreeDirectoryPage(seInstance, pageMgr, metaPageId);
    // free stash page
    HashFreeStashPage(seInstance, pageMgr, metaPageId);

    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    uint8_t *metaPage = NULL;
    // get meta page
    Status ret = DbGetExternalErrno(SeGetPage(pageMgr, metaPageId, &metaPage, ENTER_PAGE_WRITE, true));
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "|Hash| GetPage when drop: unable.");
        return ret;
    }

    HashTableMetaT *htMeta = (HashTableMetaT *)((PageHeadT *)metaPage + 1);
    uint32_t tableSpaceIndex = htMeta->idxBase.indexCfg.tableSpaceIndex;
    htMeta->idxBase.validCode = 0u;
    SeLeavePage(pageMgr, metaPageId, false);
    FreePageParamT freePageParam = {
        .spaceId = tableSpaceIndex, .addr = metaPageId, .dbInstance = seInstance->dbInstance, .labelRsmUndo = NULL};
    SeInitCachePagePara(&freePageParam.cachePagePara);
    // free meta page
    ret = DbGetExternalErrno(SeFreePage(pageMgr, &freePageParam));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to free meta page when drop |deviceId:%" PRIu32 ", blockId:%" PRIu32 "|",
            metaPageId.deviceId, metaPageId.blockId);
    }

    (void)RedoLogEnd(redoCtx, true);
    return ret;
}

void HashFreeHashDirPage(HashTableT *ht, uint32_t freeCount)
{
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    HashMemRunCtxT memRunCtx = {0};
    HashMemRunCtxInitParaT initPara = {0};
    GetHashMemRunCtxInitPara(ht, &initPara);
    Status ret = HashMemRunCtxInit(&initPara, &memRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "|SE-Hash| unable to init mem runctx, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return;
    }
    // 申请是从后往前申请的，从后往前释放dir page与pageId[]
    for (uint32_t i = 0; i < freeCount; i++) {
        uint32_t dirId = (memRunCtx.pagePtrArrCapacity - i) - 1;
        PageIdT addr = memRunCtx.pageId[dirId];
        memRunCtx.pageId[dirId] = SE_INVALID_PAGE_ADDR;
        ret = HtFreePage(&memRunCtx, addr);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to free pre alloced hash dir seg page, blockId=%" PRIu32 ", deviceId=%" PRIu32,
                addr.blockId, addr.deviceId);
            DB_ASSERT(0);  // 刚刚申请成功的内存free一定成功
        }
        memRunCtx.pageId[dirId] = SE_INVALID_PAGE_ADDR;
    }
    DB_ASSERT(ccehMeta->dirPageCount > freeCount);
    ccehMeta->dirPageCount -= freeCount;
}

static Status HashInitRunCtx(IndexCtxT *idxCtx, HashTableT *ht)
{
    DB_POINTER(idxCtx);
    /* When the HASH index accesses the HEAP, HEAP will apply for transaction lock of row
     * and the latch of the index page is held in the meantime, which may lead to deadlocks
     * caused by interdependency between transaction locks and index page latches.
     * To avoid this case, set isAcquireLockByTryOnce as true,
     * and HEAP will not wait if lock conflict applying for row's transaction locks. */
    idxCtx->isAcquireLockByTryOnce = true;
    idxCtx->batchLocked = false;
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx;
    memRunCtx->pageMgr = idxCtx->idxOpenCfg.seRunCtx->pageMgr;

    HashMemRunCtxInitParaT initPara = {0};
    GetHashMemRunCtxInitPara(ht, &initPara);
    return HashMemRunCtxInit(&initPara, memRunCtx);
}

static Status HashAllocHashTableInMem(const uint16_t instanceId, SeInstanceT *seInstance, HashTableT **newHt,
    DbMemCtxT **htMemCtx, ShmemPtrT *newHtShmAddr)
{
    *htMemCtx = (DbMemCtxT *)DbGetShmemCtxById((seInstance)->hashIndexShmMemCtxId, instanceId);
    if (*htMemCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "|SE-Hash| HCIN: hash's mem context is wrong");
        return GMERR_INTERNAL_ERROR;
    }

    *newHt = SeShmAlloc(*htMemCtx, (uint32_t)(sizeof(HashTableT)), newHtShmAddr);
    if (*newHt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INSUFFICIENT_RESOURCES, "|SE-Hash| HC: unable to alloc new hashtable indexId");
        return GMERR_INSUFFICIENT_RESOURCES;
    }
    return GMERR_OK;
}

static Status InitHashTableWithPerisistenceData(HashTableT *ht, SeInstanceT *se, DbMemCtxT *htMemCtx, IndexCtxT *idxCtx)
{
    DB_POINTER4(ht, se, htMemCtx, idxCtx);
    // 1.获取meta页数据
    PageMgrT *pageMgr = (PageMgrT *)se->pageMgr;
    DB_POINTER(pageMgr);
    const PageIdT metaPageId = *(PageIdT *)(&idxCtx->idxShmAddr);
    uint8_t *metaPage = NULL;
    StatusInter interErrno = SeGetPage(pageMgr, metaPageId, (uint8_t **)&metaPage, ENTER_PAGE_NORMAL, false);
    if (interErrno != STATUS_OK_INTER) {
        DB_LOG_AND_SET_LASERR(DbGetExternalErrno(interErrno), "|Hash| GetPage when open: unable.");
        return DbGetExternalErrno(interErrno);
    }

    // 2.根据meta页初始化
    HashTableMetaT *htMeta = (HashTableMetaT *)(void *)(metaPage + sizeof(PageHeadT));
    IndexMetaCfgT *idxCfg = &idxCtx->idxMetaCfg;
    InitHashTable(ht, se, htMeta, idxCfg);
    ht->metaPageId = metaPageId;

    // 3.获取directory数组
    HashMemRunCtxInitParaT initPara;
    GetHashMemRunCtxInitPara(ht, &initPara);
    HashMemRunCtxInit(&initPara, &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx);
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx;
    Status ret = AllocAndSetPageArrMem(se, htMeta->dirPageId, memRunCtx, ht);
    if (ret != GMERR_OK) {
        SeLeavePage(pageMgr, metaPageId, false);
        return ret;
    }

    // 4.获取stash页面数量
    uint32_t stashPageCount = 0;
    ret = CalPageListCountByFirstStashPage(memRunCtx, htMeta->stashPageId, &stashPageCount);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Hash Index|calculate stash page num unsucc: %" PRIu32, ret);
        SeLeavePage(pageMgr, metaPageId, false);
        return ret;
    }
    ht->stashPageNum = stashPageCount;
    SeLeavePage(pageMgr, metaPageId, false);
    return GMERR_OK;
}

Status HashIndexOpen(IndexCtxT *idxCtx)
{
    SeInstanceT *se = idxCtx->idxOpenCfg.seRunCtx->seIns;

    // 1.申请hash表结构内存
    HashTableT *ht = NULL;
    ShmemPtrT newHtShmAddr = {0};
    DbMemCtxT *htMemCtx = NULL;
    Status ret = HashAllocHashTableInMem(se->instanceId, se, &ht, &htMemCtx, &newHtShmAddr);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "|SE-Hash| open ht mem unsucc, indexId %" PRIu16 ".", se->instanceId);
        return ret;
    }

    // 2.根据持久化数据初始化hash表结构
    ret = InitHashTableWithPerisistenceData(ht, se, htMemCtx, idxCtx);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "InitHashTableWithPerisistenceData go wrong");
        DbShmemCtxFree(htMemCtx, newHtShmAddr);
        return ret;
    }
    ht->htAddr = newHtShmAddr;

    // 3.初始化memRunCtx
    ret = HashInitRunCtx(idxCtx, ht);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "HashInitRunCtx go wrong");
        DbShmemCtxFree(htMemCtx, newHtShmAddr);
        return ret;
    }

    // 4.更新MemRunCtx，pageArr内存在open时候申请，需要刷新memRunCtx对应值
    ((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx.pageId = (PageIdT *)DbShmPtrToAddr(ht->pageAddr);
    DB_ASSERT(((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx.pageId != NULL);
    // Save Runtime HashTableT to idxCtx->idxHandle, and it will be used for other hash index api expect drop
    idxCtx->idxHandle = (IdxBaseT *)(void *)ht;
    return GMERR_OK;
}

void HashIndexClose(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    if (ht == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "|SE-Hash| HC: hashtable is wrong when close");
        return;
    }
    DbRWLatchW(&ht->idxBase.idxLatch);
    HashMemRunCtxT memRunCtx = {0};
    HashMemRunCtxInitParaT initPara = {0};
    GetHashMemRunCtxInitPara(ht, &initPara);
    Status ret = HashMemRunCtxInit(&initPara, &memRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbRWUnlatchW(&ht->idxBase.idxLatch);
        DB_LOG_ERROR(ret, "|SE-Hash| unable to init mem runctx, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return;
    }
    void *htMemCtx = DbGetShmemCtxById(ht->idxBase.shmemCtxId, memRunCtx.memMgr->seInstanceId);
    if (htMemCtx == NULL) {
        DbRWUnlatchW(&ht->idxBase.idxLatch);
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "|SE-Hash| HCIN: hash's mem context is wrong, shmId:%" PRIu32,
            ht->idxBase.shmemCtxId);
        return;
    }
    if (DbIsShmPtrValid(ht->pageAddr)) {
        DbShmemCtxFree(htMemCtx, ht->pageAddr);
    }

    ht->idxBase.validCode = 0u;
    DbRWUnlatchW(&ht->idxBase.idxLatch);
    DbShmemCtxFree(htMemCtx, ht->htAddr);
}

static inline void HashIndexGetCtxSize(size_t *ctxSize, size_t *iterSize)
{
    *ctxSize = sizeof(HashIndexCtxT);
    *iterSize = sizeof(HashIteratorT);
}

static Status PreLoadDirPages(HashTableT *ht, HashMemRunCtxT *memRunCtx, const uint32_t loadLimit)
{
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    DB_ASSERT(ccehMeta->hashSegNumPerPage != 0u);
    DB_ASSERT(ccehMeta->dirPageCount > 0);
    uint32_t preLoadCount = 0;
    for (uint32_t i = 0; i < ccehMeta->dirPageCount && preLoadCount <= loadLimit; i++) {
        PageIdT dirPageAddr = memRunCtx->pageId[i];
        HashPageInfoT dirPageInfo = SE_INVALID_HASH_PAGE_INFO;
        Status ret = HtGetPage(memRunCtx, dirPageAddr, ENTER_PAGE_NORMAL, &dirPageInfo);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unsucc to preload the %" PRIu32 " dir page.", i);
            return ret;
        }
        SeLeavePage(memRunCtx->pageMgr, dirPageAddr, false);
        preLoadCount++;
    }
    return GMERR_OK;
}

Status HashIndexPreload(IndexCtxT *idxCtx, void *userData)
{
    DB_UNUSED(userData);

    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    DB_POINTER(ht);
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx;

    BufpoolMgrT *bufPoolPageMgr = (BufpoolMgrT *)memRunCtx->pageMgr;
    uint32_t loadLimit = (uint32_t)(bufPoolPageMgr->bufPool->capacity * MAX_PRELOAD_LIMIT);

    return PreLoadDirPages(ht, memRunCtx, loadLimit);
}

void HashIndexAMInit(void)
{
    IdxFuncT hashIndexFuncHandle = IdxEmptyIdxFunc();
    hashIndexFuncHandle.idxCreate = HashIndexCreate;
    hashIndexFuncHandle.idxDrop = HashIndexDrop;
    hashIndexFuncHandle.idxOpen = HashIndexOpen;
    hashIndexFuncHandle.idxClose = HashIndexClose;
    hashIndexFuncHandle.idxInsert = HashIndexInsert;
    hashIndexFuncHandle.idxBatchInsert = HashIndexBatchInsert;
    hashIndexFuncHandle.idxDelete = HashIndexDelete;
    hashIndexFuncHandle.idxBatchDelete = HashIndexBatchDelete;
    hashIndexFuncHandle.idxUpdate = HashIndexUpdate;
    hashIndexFuncHandle.idxBatchUpdate = HashIndexBatchUpdate;
    hashIndexFuncHandle.idxLookup = HashIndexLookup;
    hashIndexFuncHandle.idxBatchLookup = HashIndexBatchLookup;
    hashIndexFuncHandle.idxBeginScan = HashIndexBeginScan;
    hashIndexFuncHandle.idxScan = HashIndexScan;
    hashIndexFuncHandle.idxSetDirection = HashIndexSetDirection;
    hashIndexFuncHandle.idxUndoInsert = HashIndexUndoInsert;
    hashIndexFuncHandle.idxUndoRemove = HashIndexUndoRemove;
    hashIndexFuncHandle.idxUndoUpdate = HashIndexUpdate;
    hashIndexFuncHandle.idxGetCtxSize = HashIndexGetCtxSize;
    hashIndexFuncHandle.idxPreload = HashIndexPreload;
    hashIndexFuncHandle.idxTableLoad = NULL;
    IdxAmFuncRegister((uint8_t)HASH_INDEX, &hashIndexFuncHandle);
}
