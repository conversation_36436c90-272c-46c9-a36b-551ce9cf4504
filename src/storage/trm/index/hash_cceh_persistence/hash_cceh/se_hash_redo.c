/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: implementation of cceh redo
 * Author: xiejianming
 * Create: 2024-2-23
 */

#include "se_hash_redo_am.h"
#include "se_hash_replay.h"
#include "se_persistence_hash_index.h"

void HashRedoForUpdateMetaImpl(PageIdT pageId, HashTableMetaT *meta, RedoRunCtxT *redoCtx)
{
    RedoLogWrite(redoCtx, REDO_OP_HASH_SET_META, &pageId, (uint8_t *)meta, sizeof(HashTableMetaT));
}

void HashRedoForSetSegmentImpl(PageIdT pageId, uint16_t slotId, HashDirSegmentT *seg, RedoRunCtxT *redoCtx)
{
    RedoLogWrite(redoCtx, REDO_OP_HASH_SET_SEGMENT, &pageId, (uint8_t *)&slotId, sizeof(uint16_t));
    RedoLogAppend(redoCtx, (uint8_t *)seg, sizeof(HashDirSegmentT));
}

void HashRedoForSegmentPageInitImpl(PageIdT pageId, RedoRunCtxT *redoCtx)
{
    RedoLogWrite(redoCtx, REDO_OP_HASH_SEGMENT_PAGE_INIT, &pageId, NULL, 0);
}

void HashRedoForFreeDirPageImpl(PageIdT pageId, PageIdT dirPageId, uint32_t segIndex, RedoRunCtxT *redoCtx)
{
    RedoLogWrite(redoCtx, REDO_OP_HASH_FREE_DIR_PAGE, &pageId, (uint8_t *)&dirPageId, sizeof(PageIdT));
    RedoLogAppend(redoCtx, (uint8_t *)&segIndex, sizeof(uint32_t));
}

void HashRedoForSetMetaStashPageImpl(PageIdT pageId, PageIdT stashPageId, RedoRunCtxT *redoCtx)
{
    RedoLogWrite(redoCtx, REDO_OP_HASH_SET_META_STASH_PAGE, &pageId, (uint8_t *)&stashPageId, sizeof(PageIdT));
}

void HashRedoForSetSegmentEntryImpl(PageIdT pageId, uint16_t slotId, HashEntryT *entry, RedoRunCtxT *redoCtx)
{
    RedoLogWrite(redoCtx, REDO_OP_HASH_SET_SEGMENT_ENTRY, &pageId, (uint8_t *)&slotId, sizeof(uint16_t));
    RedoLogAppend(redoCtx, (const uint8_t *)entry, sizeof(HashEntryT));
}

void HashRedoForSetDirNextPageIdImpl(PageIdT pageId, PageIdT nextPageId, RedoRunCtxT *redoCtx)
{
    RedoLogWrite(redoCtx, REDO_OP_HASH_SET_DIR_NEXT_PAGE_ID, &pageId, (uint8_t *)&nextPageId, sizeof(PageIdT));
}

void HashRedoForSetMetaDepthImpl(PageIdT pageId, uint32_t depth, RedoRunCtxT *redoCtx)
{
    RedoLogWrite(redoCtx, REOD_OP_HASH_SET_META_DEPTH, &pageId, (uint8_t *)&depth, sizeof(uint32_t));
}

void HashRedoForStashPageInitImpl(PageIdT pageId, HtStashPageHeadT *head, RedoRunCtxT *redoCtx)
{
    RedoLogWrite(redoCtx, REDO_OP_HASH_STASH_PAGE_INIT, &pageId, (uint8_t *)head, sizeof(HtStashPageHeadT));
}

void HashRedoForSetStashEntryImpl(PageIdT pageId, uint16_t slotId, HashEntryT *entry, RedoRunCtxT *redoCtx)
{
    RedoLogWrite(redoCtx, REDO_OP_HASH_SET_STASH_ENTRY, &pageId, (uint8_t *)&slotId, sizeof(uint16_t));
    RedoLogAppend(redoCtx, (const uint8_t *)entry, sizeof(HashEntryT));
}

void HashRedoForSetSegmentEntryProbeLenImpl(PageIdT pageId, uint16_t slotId, uint32_t probeLen, RedoRunCtxT *redoCtx)
{
    RedoLogWrite(redoCtx, REDO_OP_HASH_SET_SEGMENT_ENTRY_PROBELEN, &pageId, (uint8_t *)&slotId, sizeof(uint16_t));
    RedoLogAppend(redoCtx, (uint8_t *)&probeLen, sizeof(uint32_t));
}

void HashRedoForSegmentPageMarkDeleteImpl(PageIdT pageId, uint16_t slotId, RedoRunCtxT *redoCtx)
{
    RedoLogWrite(redoCtx, REDO_OP_HASH_SEGMENT_PAGE_MARK_DELETE, &pageId, (uint8_t *)&slotId, sizeof(uint16_t));
}

void HashRedoForStashPageMarkDeleteImpl(PageIdT pageId, uint16_t slotId, RedoRunCtxT *redoCtx)
{
    RedoLogWrite(redoCtx, REDO_OP_HASH_STASH_PAGE_MARK_DELETE, &pageId, (uint8_t *)&slotId, sizeof(uint16_t));
}

void HashRedoForSetStashPrevPageIdImpl(PageIdT pageId, PageIdT prevPageId, RedoRunCtxT *redoCtx)
{
    RedoLogWrite(redoCtx, REDO_OP_HASH_SET_STASH_PREV_PAGE_ID, &pageId, (uint8_t *)&prevPageId, sizeof(PageIdT));
}

void HashRedoForSetStashBitMapImpl(PageIdT pageId, uint16_t slotId, bool set, RedoRunCtxT *redoCtx)
{
    RedoLogWrite(redoCtx, REDO_OP_HASH_SET_STASH_BIT_MAP, &pageId, (uint8_t *)&slotId, sizeof(uint16_t));
    RedoLogAppend(redoCtx, (uint8_t *)&set, sizeof(bool));
}

void HashRedoForSetStashProbeLenImpl(PageIdT pageId, uint32_t probeLen, RedoRunCtxT *redoCtx)
{
    RedoLogWrite(redoCtx, REDO_OP_HASH_SET_STASH_PROBELEN, &pageId, (uint8_t *)&probeLen, sizeof(uint32_t));
}

void HashRedoForCopyPageDataImpl(PageIdT pageId, uint32_t len, uint8_t *data, RedoRunCtxT *redoCtx)
{
    RedoLogWrite(redoCtx, REDO_OP_HASH_COPY_PAGE_DATA, &pageId, (uint8_t *)&len, sizeof(uint32_t));
    RedoLogAppend(redoCtx, (uint8_t *)data, len);
}

static StatusInter HashRedoReplayFuncRegister(SeInstanceHdT seIns)
{
    REG_REPLAY_FUNC(seIns, REDO_OP_HASH_SET_META, HashIndexUpdateMetaReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_HASH_SET_SEGMENT, HashIndexSetSegmentReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_HASH_SEGMENT_PAGE_INIT, HashIndexSegmentPageInitReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_HASH_FREE_DIR_PAGE, HashIndexFreeDirPageReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_HASH_SET_META_STASH_PAGE, HashIndexSetMetaStashPageReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_HASH_SET_SEGMENT_ENTRY, HashIndexSetSegmentEntryReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_HASH_SET_DIR_NEXT_PAGE_ID, HashIndexSetDirNextPageIdReplay);
    REG_REPLAY_FUNC(seIns, REOD_OP_HASH_SET_META_DEPTH, HashIndexSetMetaDepthReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_HASH_STASH_PAGE_INIT, HashIndexStashPageInitReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_HASH_SET_STASH_ENTRY, HashIndexSetStashEntryReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_HASH_SET_SEGMENT_ENTRY_PROBELEN, HashIndexSetSegmentEntryProbeLenReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_HASH_SEGMENT_PAGE_MARK_DELETE, HashIndexSegmentPageMarkDeleteReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_HASH_STASH_PAGE_MARK_DELETE, HashIndexStashPageMarkDeleteReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_HASH_SET_STASH_PREV_PAGE_ID, HashIndexSetStashPrevPageIdReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_HASH_SET_STASH_BIT_MAP, HashIndexSetStashBitMapReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_HASH_SET_STASH_PROBELEN, HashIndexSetStashProbeLenReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_HASH_COPY_PAGE_DATA, HashIndexCopyPageDataReplay);
    return STATUS_OK_INTER;
}

void HashIndexRedoAmInit(SeInstanceHdT seIns)
{
    HashIndexRedoAmT redoAm = {HashRedoForUpdateMetaImpl, HashRedoForSetSegmentImpl, HashRedoForSegmentPageInitImpl,
        HashRedoForFreeDirPageImpl, HashRedoForSetMetaStashPageImpl, HashRedoForSetSegmentEntryImpl,
        HashRedoForSetDirNextPageIdImpl, HashRedoForSetMetaDepthImpl, HashRedoForStashPageInitImpl,
        HashRedoForSetStashEntryImpl, HashRedoForSetSegmentEntryProbeLenImpl, HashRedoForSegmentPageMarkDeleteImpl,
        HashRedoForStashPageMarkDeleteImpl, HashRedoForSetStashPrevPageIdImpl, HashRedoForSetStashBitMapImpl,
        HashRedoForSetStashProbeLenImpl, HashRedoForCopyPageDataImpl};

    SetHashIndexRedoAmFunc(&redoAm);
    HashRedoReplayFuncRegister(seIns);
}
