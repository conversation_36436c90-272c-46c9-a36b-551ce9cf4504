/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: multiversion page for art. MV is short for MultiVersion
 * Author: gaohaiyang
 * Create: 2024-03-11
 */

#include "se_art_multiversion_page.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MV_PAGE_USED_WARN_NUM 10  // 一个32KB的页大约可以容纳511个 ArtMVNodeT ，sizeof(ArtMVNodeT)=64Byte

typedef struct {
    uint8_t hashMVEntryUsedNum;  // 每个node上已经使用的 mv entry
    uint8_t reserve;
    uint16_t hashMVEntryNumPerNode;
    ArtMVNodeT curNode;   // 当前节点
    ArtMVNodeT preNode;   // 前一个节点
    ArtMVNodeT nextNode;  // 后一个节点
    uint8_t mvEntry[];    // 每个entry数据结构为 ArtChainNodeT
} ArtMVNodeAddrT;

typedef struct {
    uint16_t blockId;                // 所在blockId
    uint16_t nodeCntPerVersionPage;  // node个数
    uint16_t usedNodeCnt;            // 只要被alloc出去就是used
    uint16_t freeListNodeCnt;        // free list node数量
    uint16_t allocNodeCursor;  // 申请node的cursor，每次申请从此处开始，减少页内遍历个数，加速查找空闲node
    ArtMVNodeT freeNodeListHeader;
    uint8_t nodeArray[];  // node结构： ArtMVNodeT
} ArtMVPageHeaderT;

inline static ArtMVPageHeaderT *GetArtMVPageHeadByAddr(uint8_t *pageVirtualAddr)
{
    DB_POINTER(pageVirtualAddr);
    return (ArtMVPageHeaderT *)(void *)pageVirtualAddr;
}

inline static ArtMVNodeAddrT *GetArtMVNode(ArtMVPageHeaderT *pageHeader, uint16_t nodeIdx)
{
    DB_POINTER(pageHeader);
    return (ArtMVNodeAddrT *)(void *)(pageHeader->nodeArray + (size_t)(nodeIdx * (uint16_t)CACHELINE_SIZE));
}

inline static ArtChainNodeT *GetArtChainNode(ArtMVNodeAddrT *nodeAddr, uint32_t entrySize, uint16_t entryIdx)
{
    DB_POINTER(nodeAddr);
    return (ArtChainNodeT *)(void *)(nodeAddr->mvEntry + (size_t)(entryIdx * (uint16_t)entrySize));
}

void ArtInitSlotByChainNode(ArtRunChildSlotT *childSlot, const ArtChainNodeT *chainNode)
{
    InitChildSlot(childSlot);
    childSlot->childMeta.slotType = ART_ROWID;
    childSlot->slot.rowId = chainNode->rowId;
    childSlot->childMeta.delFlag = chainNode->delFlag;
}

inline static void InitArtChainNode(ArtChainNodeT *node)
{
    (void)memset_s(node, sizeof(ArtChainNodeT), 0, sizeof(ArtChainNodeT));
    node->rowId = HEAP_INVALID_ADDR;
    node->delFlag = 0;
}

static void InitArtMVNode(ArtMVNodeAddrT *nodeAddr, uint16_t blockId, uint16_t nodeIdx)
{
    DB_POINTER(nodeAddr);
    uint32_t mvEntrySize = (uint32_t)sizeof(ArtChainNodeT);
    nodeAddr->curNode.blockId = blockId;
    nodeAddr->curNode.offset = nodeIdx;
    nodeAddr->nextNode = INVALID_ART_MV_NODE;
    nodeAddr->preNode = INVALID_ART_MV_NODE;

    nodeAddr->hashMVEntryUsedNum = 0;
    nodeAddr->reserve = 0;
    nodeAddr->hashMVEntryNumPerNode = (uint16_t)((CACHELINE_SIZE - sizeof(ArtMVNodeAddrT)) / mvEntrySize);
    for (uint16_t i = 0; i < nodeAddr->hashMVEntryNumPerNode; i++) {
        ArtChainNodeT *tmpNode = GetArtChainNode(nodeAddr, mvEntrySize, i);
        InitArtChainNode(tmpNode);
    }
}

static void InitArtMVPage(ArtMVPageHeaderT *mvPage, uint32_t pageSize, uint16_t blockId)
{
    DB_POINTER(mvPage);
    mvPage->nodeCntPerVersionPage =
        (uint16_t)((pageSize - sizeof(ArtMVPageHeaderT) - sizeof(PageHeadT)) / CACHELINE_SIZE);
    mvPage->blockId = blockId;
    mvPage->freeListNodeCnt = 0;
    mvPage->usedNodeCnt = 0;
    mvPage->allocNodeCursor = 0;
    mvPage->freeNodeListHeader = INVALID_ART_MV_NODE;
    for (uint16_t i = 0; i < mvPage->nodeCntPerVersionPage; i++) {
        ArtMVNodeAddrT *tmpMVNode = GetArtMVNode(mvPage, i);
        InitArtMVNode(tmpMVNode, blockId, i);
    }
}

static ArtMVPageHeaderT *AllocMVPage(ArtRunningCtxT *artRunCtx)
{
    DB_POINTER3(artRunCtx, artRunCtx->art, artRunCtx->userCtx.cbUserCtx);
    ArtTreeT *art = artRunCtx->art;
    uint32_t tableSpaceIdx = artRunCtx->userCtx.cbUserCtx->idxMetaCfg.tableSpaceIndex;
    uint16_t blockId = DB_INVALID_UINT16;
    uint8_t *pageBeginAddr = NULL;
    Status ret = ArtAllocMVPage(&artRunCtx->memRunCtx, tableSpaceIdx, &blockId, &pageBeginAddr);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "ArtMVPage, alloc new mv page, table space index %" PRIu32, tableSpaceIdx);
        return NULL;
    }
    art->mvPageInfo.mvPageUsedCnt++;
    if (art->mvPageInfo.mvPageUsedCnt >= MV_PAGE_USED_WARN_NUM) {
        DB_LOG_WARN(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "ArtMVPage, index id %" PRIu32 " use %" PRIu32 " mv page",
            art->indexId, art->mvPageInfo.mvPageUsedCnt);
    }
    ArtMVPageHeaderT *newPageHeader = GetArtMVPageHeadByAddr(pageBeginAddr);
    InitArtMVPage(newPageHeader, artRunCtx->art->artMemMgr.pageSize, blockId);
    return newPageHeader;
}

static void FreeMVPage(ArtRunningCtxT *artRunCtx, uint16_t blockId)
{
    DB_POINTER(artRunCtx);
    Status ret =
        ArtFreeMVPageById(&artRunCtx->memRunCtx, artRunCtx->userCtx.cbUserCtx->idxMetaCfg.tableSpaceIndex, blockId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "ArtMVPage, free mv page, blockId: %" PRIu16, blockId);
    } else {
        artRunCtx->art->mvPageInfo.mvPageUsedCnt--;
        if (blockId == artRunCtx->art->mvPageInfo.curMVPageBlockId) {
            artRunCtx->art->mvPageInfo.curMVPageBlockId = DB_INVALID_UINT16;
        }
    }
}

inline static bool ArtChainNodeIsFree(const ArtChainNodeT *chainNode)
{
    DB_POINTER(chainNode);
    return (chainNode->rowId == HEAP_INVALID_ADDR);
}

static ArtMVPageHeaderT *GetArtMVPageHeaderById(ArtMemMgrRunCtxT *memRunCtx, uint16_t blockId, PageIdT pageId)
{
    DB_POINTER(memRunCtx);
    // blockId 和 pageId 二选一，有且仅有一个参数为有效值
    if (blockId == DB_INVALID_UINT16 && !DbIsPageIdValid(pageId)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_INTERNAL_ERROR, "ArtMVPage, blockId and pageId both not valid, check func bracktrace.");
        return NULL;
    }
    if (blockId != DB_INVALID_UINT16 && DbIsPageIdValid(pageId)) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "ArtMVPage, blockId and pageId both valid, check func bracktrace.");
        return NULL;
    }
    uint8_t *pageBeginAddr = NULL;
    Status ret;
    if (blockId != DB_INVALID_UINT16) {
        ret = GetArtMVPageByBlockId(memRunCtx, blockId, &pageBeginAddr);
    } else {
        ret = GetArtMVPageByPageId(memRunCtx, pageId, &pageBeginAddr);
    }
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret,
            "ArtMVPage, get mv page head by id, blockId: %" PRIu16 ", pacgeId:(%" PRIu32 ", %" PRIu32 ")", blockId,
            pageId.blockId, pageId.deviceId);
        return NULL;
    }
    return GetArtMVPageHeadByAddr(pageBeginAddr);
}

inline static ArtMVNodeAddrT *GetArtMVNodeAddr(ArtMemMgrRunCtxT *memRunCtx, ArtMVNodeT mvNode)
{
    DB_POINTER(memRunCtx);
    if (mvNode.blockId == DB_INVALID_UINT16) {
        return NULL;
    }
    ArtMVPageHeaderT *pageHeader = GetArtMVPageHeaderById(memRunCtx, mvNode.blockId, SE_INVALID_PAGE_ADDR);
    if (pageHeader == NULL) {
        return NULL;
    }
    return (ArtMVNodeAddrT *)(void *)(pageHeader->nodeArray + (size_t)(mvNode.offset * (uint16_t)CACHELINE_SIZE));
}

Status GetFreeArtMVNodeInFreeList(ArtMemMgrRunCtxT *memRunCtx, ArtMVPageHeaderT *pageHeader, ArtMVNodeT *freeNode)
{
    DB_POINTER3(memRunCtx, pageHeader, freeNode);
    ArtMVNodeT listHeader = pageHeader->freeNodeListHeader;
    // 空闲链表没有数据
    if (ArtMVNodeIsInvalid(&listHeader)) {
        return GMERR_NO_DATA;
    }
    // 有数据，取头结点并更新空闲链表
    ArtMVNodeAddrT *nodeAddr = GetArtMVNodeAddr(memRunCtx, listHeader);
    if (nodeAddr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "ArtMVPage, get node from free list (%" PRIu16 ",%" PRIu32 ")", listHeader.blockId, listHeader.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    pageHeader->freeNodeListHeader = nodeAddr->nextNode;
    pageHeader->freeListNodeCnt--;
    *freeNode = listHeader;
    return GMERR_OK;
}

Status AllocArtMVNodeFromFreeList(ArtMemMgrRunCtxT *memRunCtx, ArtMVNodeT *newNode)
{
    DB_POINTER2(memRunCtx, newNode);
    DbShmArrayIteratorT iter;
    for (DbShmArrayInitIterator(&iter, &(memRunCtx->artMemMgr->artMVPageArray)); DbShmArrayIteratorValid(&iter);
         DbShmArrayIteratorNext(&iter)) {
        // for循环 DbShmArrayIteratorValid 已对item进行有效性判断
        ArtMVPageDescT *pageDesc = (ArtMVPageDescT *)DbShmArrayIteratorGetItem(&iter);
        PageIdT pageId = pageDesc->pageId;

        ArtMVPageHeaderT *pageHeader = GetArtMVPageHeaderById(memRunCtx, DB_INVALID_UINT16, pageId);
        if (pageHeader == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
                "ArtMVPage, get mv page header iterative, blockId: %" PRIu32 ", pageId:(%" PRIu32 ",%" PRIu32 ")",
                iter.itemId, pageId.blockId, pageId.deviceId);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        Status ret = GetFreeArtMVNodeInFreeList(memRunCtx, pageHeader, newNode);
        // 没找到则继续找
        if (ret == GMERR_NO_DATA) {
            continue;
        }
        // 出错则返回
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret,
                "ArtMVPage, get node from free list:(%" PRIu16 ",%" PRIu16 "). blockId: %" PRIu16 ", usedCnt: %" PRIu16,
                pageHeader->freeNodeListHeader.blockId, pageHeader->freeNodeListHeader.offset, pageHeader->blockId,
                pageHeader->usedNodeCnt);
            return ret;
        }
        // 找到也返回
        return GMERR_OK;
    }
    return GMERR_NO_DATA;
}

Status GetFreeArtMVNodeInPage(ArtMemMgrRunCtxT *memRunCtx, uint16_t blockId, ArtMVNodeT *newNode)
{
    DB_POINTER2(memRunCtx, newNode);
    ArtMVPageHeaderT *pageHeader = GetArtMVPageHeaderById(memRunCtx, blockId, SE_INVALID_PAGE_ADDR);
    if (pageHeader == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE,
            "ArtMVPage, get page header by blockId: %" PRIu16 ", nodeAddr: (%" PRIu16 ", %" PRIu16 ")", blockId,
            newNode->blockId, newNode->offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (pageHeader->usedNodeCnt > pageHeader->nodeCntPerVersionPage) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "ArtMVPage, page node statistics, %" PRIu16 " is greater than %" PRIu16, pageHeader->usedNodeCnt,
            pageHeader->nodeCntPerVersionPage);
        return GMERR_DATA_EXCEPTION;
    }
    // 判断有无空闲node，没有也属于正常情况，即页上所有node均在使用
    if (pageHeader->usedNodeCnt == pageHeader->nodeCntPerVersionPage) {
        return GMERR_NO_DATA;
    }
    // 有空闲node，获取空闲node
    ArtMVNodeT freeNode = {.blockId = blockId, .offset = pageHeader->allocNodeCursor};
    if (GetArtMVNodeAddr(memRunCtx, freeNode) == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "ArtMVPage, get new node addr, node info: (%" PRIu16 ",%" PRIu16 ")", freeNode.blockId, freeNode.blockId);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    *newNode = freeNode;
    // 更新页头信息
    pageHeader->allocNodeCursor++;
    pageHeader->usedNodeCnt++;
    return GMERR_OK;
}

Status AllocArtMVNodeFromNewPage(ArtRunningCtxT *artRunCtx, ArtMVNodeT *newNodeAddr)
{
    DB_POINTER2(artRunCtx, newNodeAddr);
    ArtMVPageHeaderT *pageHeader = AllocMVPage(artRunCtx);
    if (pageHeader == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "ArtMVPage, try to alloc new page.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    Status ret = GetFreeArtMVNodeInPage(&artRunCtx->memRunCtx, pageHeader->blockId, newNodeAddr);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "ArtMVPage, alloc node from new page, blockId:%" PRIu16, pageHeader->blockId);
        FreeMVPage(artRunCtx, pageHeader->blockId);
        return ret;
    }
    artRunCtx->art->mvPageInfo.curMVPageBlockId = pageHeader->blockId;
    return GMERR_OK;
}

Status AllocArtMVNodeFromCurrentPage(ArtMemMgrRunCtxT *memRunCtx, uint16_t curBlockId, ArtMVNodeT *newNode)
{
    DB_POINTER2(memRunCtx, newNode);
    if (curBlockId == DB_INVALID_UINT16) {
        return GMERR_NO_DATA;
    }
    Status ret = GetFreeArtMVNodeInPage(memRunCtx, curBlockId, newNode);
    // 发生错误
    if (ret != GMERR_NO_DATA && ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "ArtMVPage, alloc node from current page, blockId:%" PRIu32, curBlockId);
    }
    return ret;
}

static Status AllocArtMVNode(ArtRunningCtxT *artRunCtx, ArtMVNodeT *newNode)
{
    DB_POINTER2(artRunCtx, newNode);
    ArtMemMgrRunCtxT *memRunCtx = &artRunCtx->memRunCtx;
    ArtMVNodeT tmpNode = INVALID_ART_MV_NODE;
    // step1:遍历向各页上的空闲链表申请
    Status ret = AllocArtMVNodeFromFreeList(memRunCtx, &tmpNode);
    // 出错
    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        DB_LOG_AND_SET_LASERR(ret, "ArtMVPage, alloc mv node from free list of exist page.");
        return ret;
    }
    // 申请成功
    if (ret == GMERR_OK) {
        *newNode = tmpNode;
        return ret;
    }
    // 没有数据, GMERR_NO_DATA

    // step2:向当前页上剩余空间申请
    uint16_t curMVPageBlockId = artRunCtx->art->mvPageInfo.curMVPageBlockId;
    ret = AllocArtMVNodeFromCurrentPage(memRunCtx, curMVPageBlockId, &tmpNode);
    // 出错
    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        DB_LOG_AND_SET_LASERR(ret, "ArtMVPage, alloc mv node from free space of exist page.");
        return ret;
    }
    // 申请成功
    if (ret == GMERR_OK) {
        *newNode = tmpNode;
        return ret;
    }
    // 没有数据, GMERR_NO_DATA

    // step3：申请新页，从新页上申请，新页新node理论上一定有空间，因此返回 GMERR_NO_DATA 也是错误
    ret = AllocArtMVNodeFromNewPage(artRunCtx, &tmpNode);
    // 出错
    if (ret != GMERR_OK) {
        ret = (ret == GMERR_NO_DATA ? GMERR_MEMORY_OPERATE_FAILED : ret);
        DB_LOG_AND_SET_LASERR(ret, "ArtMVPage, alloc mv node form new page");
        return ret;
    }
    // 申请成功
    *newNode = tmpNode;
    return GMERR_OK;
}

void SetArtChainNode(ArtChainNodeT *dstChainNode, ArtChainNodeT *srcChainNode)
{
    *dstChainNode = *srcChainNode;
    DB_ASSERT(dstChainNode->rowId != HEAP_INVALID_ADDR);
}

void InsertArtChainNode2ArtMVNode(ArtRunningCtxT *artRunCtx, ArtMVNodeAddrT *nodeAddr, ArtChainNodeT *srcChainNode)
{
    DB_ASSERT(nodeAddr->hashMVEntryNumPerNode >= 1);
    bool insertTail = !artRunCtx->art->mvPageInfo.insertFromHeader;
    ArtChainNodeT *dstChainNode;
    bool found = false;
    // 尾插：新数据插到尾巴，即数组序号大的位置，因此需要正序遍历数组
    if (insertTail) {
        for (uint16_t i = 0; i < nodeAddr->hashMVEntryNumPerNode; i++) {
            dstChainNode = GetArtChainNode(nodeAddr, sizeof(ArtChainNodeT), i);
            if (ArtChainNodeIsFree(dstChainNode)) {
                found = true;
                break;
            }
        }
    } else {
        // 头插：新数据插到头，即数组序号小的位置，因此需要逆序遍历数组
        for (int16_t i = (int16_t)(nodeAddr->hashMVEntryNumPerNode - 1); i >= 0; i--) {
            dstChainNode = GetArtChainNode(nodeAddr, sizeof(ArtChainNodeT), (uint16_t)i);
            if (ArtChainNodeIsFree(dstChainNode)) {
                found = true;
                break;
            }
        }
    }
    if (!found) {
        return;
    }
    SetArtChainNode(dstChainNode, srcChainNode);
    nodeAddr->hashMVEntryUsedNum++;
}

bool SameArtMVNode(ArtMVNodeT nodeA, ArtMVNodeT nodeB)
{
    return (nodeA.blockId == nodeB.blockId && nodeA.offset == nodeB.offset);
}

// 第一次插入时候，需要将原来的节点插入多版本，再将本次插入的节点插入多版本
Status Insert2MVChainForFirst(ArtRunningCtxT *artRunCtx, ArtInsertKeyToSlotParaT *insertPara)
{
    // rowId一定不同
    DB_ASSERT(insertPara->insertKeyToChildSlotPara.childSlot.slot.rowId != insertPara->artValue.rowId);
    ArtMVNodeT newNode = INVALID_ART_MV_NODE;
    Status ret = AllocArtMVNode(artRunCtx, &newNode);
    if (ret != GMERR_OK) {
        return ret;
    }
    ArtMVNodeAddrT *newMVNodeAddr = GetArtMVNodeAddr(&artRunCtx->memRunCtx, newNode);
    // AllocArtMVNode 已经进判空
    DB_ASSERT(newMVNodeAddr != NULL);
    DB_ASSERT(newMVNodeAddr->hashMVEntryUsedNum == 0);

    ArtChainNodeT oldChainNode = {.delFlag = insertPara->insertKeyToChildSlotPara.childSlot.childMeta.delFlag,
        .rowId = insertPara->insertKeyToChildSlotPara.childSlot.slot.rowId};
    InsertArtChainNode2ArtMVNode(artRunCtx, newMVNodeAddr, &oldChainNode);
    DB_ASSERT(newMVNodeAddr->hashMVEntryUsedNum == 1);
    // mv node按照一个cacheline申请，因此此时一定能容纳2个chain node
    DB_ASSERT(newMVNodeAddr->hashMVEntryUsedNum < newMVNodeAddr->hashMVEntryNumPerNode);

    ArtChainNodeT newChainNode = {.delFlag = 0, .rowId = insertPara->artValue.rowId};
    InsertArtChainNode2ArtMVNode(artRunCtx, newMVNodeAddr, &newChainNode);
    // 设置为链头节点
    newMVNodeAddr->preNode = newNode;
    newMVNodeAddr->nextNode = INVALID_ART_MV_NODE;
    // 挂上版本链
    insertPara->insertKeyToChildSlotPara.childSlot.childMeta.slotType = (uint8_t)ART_CHAIN_ROWID;
    insertPara->insertKeyToChildSlotPara.childSlot.slot.rowIdChainPtr = newNode;

    return GMERR_OK;
}

void InsertArtMVNode2ArtMVChain(ArtRunningCtxT *artRunCtx, ArtMVNodeAddrT *newNodeAddr,
    ArtMVNodeAddrT *mvChainHeaderNodeAddr, ArtMVNodeAddrT *mvChainTailNodeAddr, ArtInsertKeyToSlotParaT *insertPara)
{
    bool headerInsert = artRunCtx->art->mvPageInfo.insertFromHeader;
    ArtMVNodeT newNode = newNodeAddr->curNode;
    if (headerInsert) {
        newNodeAddr->nextNode = mvChainHeaderNodeAddr->curNode;
        newNodeAddr->preNode = mvChainHeaderNodeAddr->preNode;
        mvChainHeaderNodeAddr->preNode = newNode;
        // 更新链头
        insertPara->insertKeyToChildSlotPara.childSlot.slot.rowIdChainPtr = newNode;
    } else {
        newNodeAddr->preNode = mvChainTailNodeAddr->curNode;
        newNodeAddr->nextNode = INVALID_ART_MV_NODE;
        DB_ASSERT((ArtMVNodeIsInvalid(&mvChainTailNodeAddr->nextNode)));
        mvChainTailNodeAddr->nextNode = newNode;
        mvChainHeaderNodeAddr->preNode = newNode;
    }
}

static Status CheckVisibilityForEachChainNode(
    ArtRunningCtxT *artRunCtx, ArtMVNodeAddrT *mvNodeAddr, uint16_t chainNodeIdx, bool isLpm, bool *findNode)
{
    ArtChainNodeT *chainNode = GetArtChainNode(mvNodeAddr, sizeof(ArtChainNodeT), chainNodeIdx);
    if (ArtChainNodeIsFree(chainNode)) {
        return GMERR_OK;
    }
    // 可见性判断
    int32_t cmp;
    bool isFound = false;
    Status cmpRet;
    if (isLpm) {
        cmpRet = artRunCtx->userCtx.cbUserCtx->idxOpenCfg.callbackFunc.addrCheckAndFetch(
            artRunCtx->userCtx.cbUserCtx, artRunCtx->art->heapRunHdl, chainNode->rowId, &isFound);
    } else {
        cmpRet = artRunCtx->userCtx.cbUserCtx->idxOpenCfg.callbackFunc.keyCmp(
            artRunCtx->userCtx.cbUserCtx, artRunCtx->userCtx.cbUserCtx->idxKey, chainNode->rowId, &cmp, &isFound);
    }
    *findNode = false;
    if (cmpRet != GMERR_OK && !SeIsFilterErrorCode(cmpRet)) {
        return cmpRet;
    }

    if ((isLpm && isFound) || (!isLpm && cmpRet != GMERR_NO_DATA)) {
        *findNode = true;
    }
    return GMERR_OK;
}

static Status CheckVisibilityForEachMVNode(
    ArtRunningCtxT *artCtx, ArtRunChildSlotT *expandSlot, bool isLpm, bool *canInsertToChain)
{
    DbSpinLock(&artCtx->art->mvPageInfo.lock);
    ArtMVNodeAddrT *curNodeAddr = GetArtMVNodeAddr(&artCtx->memRunCtx, expandSlot->slot.rowIdChainPtr);
    if (curNodeAddr == NULL) {
        *canInsertToChain = false;
        DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    while (curNodeAddr != NULL) {
        for (uint16_t i = 0; i < curNodeAddr->hashMVEntryNumPerNode; i++) {
            bool found = false;
            Status ret = CheckVisibilityForEachChainNode(artCtx, curNodeAddr, i, isLpm, &found);
            if (ret != GMERR_OK) {
                DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
                return ret;
            }
            if (found) {
                *canInsertToChain = false;
                DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
                return GMERR_OK;
            }
        }
        // 下一个 ArtMVNode
        ArtMVNodeT nextNode = curNodeAddr->nextNode;
        // 遍历结束
        if (ArtMVNodeIsInvalid(&nextNode)) {
            DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
            return GMERR_OK;
        }
        curNodeAddr = GetArtMVNodeAddr(&artCtx->memRunCtx, nextNode);
        if (curNodeAddr == NULL) {
            *canInsertToChain = false;
            DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
    }
    return GMERR_OK;
}

Status ArtCanInsertToChain(ArtRunningCtxT *artCtx, ArtRunChildSlotT *expandSlot, bool isLpm, bool *canInsertToChain)
{
    DB_POINTER3(expandSlot, artCtx, canInsertToChain);
    *canInsertToChain = true;
    if (expandSlot->childMeta.slotType == (uint8_t)ART_ROWID) {
        int32_t cmp;
        bool isFound = false;
        Status cmpRet = GMERR_OK;
        cmpRet = isLpm ? artCtx->userCtx.cbUserCtx->idxOpenCfg.callbackFunc.addrCheckAndFetch(
                             artCtx->userCtx.cbUserCtx, artCtx->art->heapRunHdl, expandSlot->slot.rowId, &isFound) :
                         artCtx->userCtx.cbUserCtx->idxOpenCfg.callbackFunc.keyCmp(artCtx->userCtx.cbUserCtx,
                             artCtx->userCtx.cbUserCtx->idxKey, expandSlot->slot.rowId, &cmp, &isFound);
        if (cmpRet != GMERR_OK && !SeIsFilterErrorCode(cmpRet)) {
            return cmpRet;
        }
        if ((isLpm && isFound) || (!isLpm && cmpRet != GMERR_NO_DATA)) {
            *canInsertToChain = false;
        }
        return GMERR_OK;
    }
    if (expandSlot->childMeta.slotType == (uint8_t)ART_CHAIN_ROWID) {
        return CheckVisibilityForEachMVNode(artCtx, expandSlot, isLpm, canInsertToChain);
    }
    DB_ASSERT(false);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status TryFindNode2Insert(ArtRunningCtxT *artRunCtx, ArtMVNodeAddrT *mvChainHeaderNodeAddr,
    ArtMVNodeAddrT *mvChainTailNodeAddr, ArtInsertKeyToSlotParaT *insertPara, ArtMVNodeAddrT **dstMVNodeAddr)
{
    bool scanFromHeader = artRunCtx->art->mvPageInfo.insertFromHeader;
    ArtMVNodeAddrT *curNodeAddr = scanFromHeader ? mvChainHeaderNodeAddr : mvChainTailNodeAddr;
    ArtMVNodeT nodeFound = INVALID_ART_MV_NODE;  // 记录可用的node
    while (curNodeAddr != NULL) {
        // 有空位就是找到
        if (curNodeAddr->hashMVEntryUsedNum < curNodeAddr->hashMVEntryNumPerNode) {
            nodeFound = curNodeAddr->curNode;
            break;
        }
        // 没空位，看下一个
        ArtMVNodeT nextNode = scanFromHeader ? curNodeAddr->nextNode : curNodeAddr->preNode;
        // 看下是否遍历结束
        bool finish =
            scanFromHeader ? ArtMVNodeIsInvalid(&nextNode) : SameArtMVNode(nextNode, mvChainTailNodeAddr->curNode);
        if (finish) {
            break;
        } else {
            curNodeAddr = GetArtMVNodeAddr(&artRunCtx->memRunCtx, nextNode);
            if (curNodeAddr == NULL) {
                return GMERR_UNEXPECTED_NULL_VALUE;
            }
        }
    }
    // 没找到
    if (ArtMVNodeIsInvalid(&nodeFound)) {
        return GMERR_NO_DATA;
    }
    // 找到
    *dstMVNodeAddr = GetArtMVNodeAddr(&artRunCtx->memRunCtx, nodeFound);
    DB_ASSERT(*dstMVNodeAddr != NULL);
    DB_ASSERT((*dstMVNodeAddr)->hashMVEntryNumPerNode >= 1);
    return GMERR_OK;
}

static void InitNewChainNode(ArtChainNodeT *node2Insert, const ArtInsertKeyToSlotParaT *insertPara)
{
    node2Insert->rowId = insertPara->artValue.rowId;
}

typedef struct {
    bool scanFromHeader;
    ArtMVNodeAddrT *mvChainHeaderNodeAddr;
    ArtRunChildSlotT *findSlot;       // 入参，可能有字段会被更新
    ArtMVNodeAddrT *foundMVNodeAddr;  // 出参
    uint16_t foundChainNodeIndex;     // 出参
} ArtMVChainScanParaT;

static Status FindChainNodeFromMVNodeByRowId(
    ArtMVNodeAddrT *curNodeAddr, ArtMVChainScanParaT *scanPara, TupleAddr expectRowId)
{
    if (scanPara->scanFromHeader) {
        for (uint16_t i = 0; i < curNodeAddr->hashMVEntryNumPerNode; i++) {
            ArtChainNodeT *tmpNode = GetArtChainNode(curNodeAddr, sizeof(ArtChainNodeT), i);
            if (tmpNode->rowId == expectRowId) {
                scanPara->foundMVNodeAddr = curNodeAddr;
                scanPara->foundChainNodeIndex = i;
                return GMERR_OK;
            }
        }
    } else {
        for (int16_t i = curNodeAddr->hashMVEntryNumPerNode - 1; i >= 0; i--) {
            ArtChainNodeT *tmpNode = GetArtChainNode(curNodeAddr, sizeof(ArtChainNodeT), (uint16_t)i);
            if (tmpNode->rowId == expectRowId) {
                scanPara->foundMVNodeAddr = curNodeAddr;
                scanPara->foundChainNodeIndex = (uint16_t)i;
                return GMERR_OK;
            }
        }
    }
    return GMERR_NO_DATA;
}

Status MVChainScan(ArtRunningCtxT *artCtx, ArtValueT artValue, ArtMVChainScanParaT *scanPara)
{
    ArtMVNodeAddrT *mvChainHeaderNodeAddr = scanPara->mvChainHeaderNodeAddr;
    // 链表尾结点
    ArtMVNodeT mvChainTailNode = mvChainHeaderNodeAddr->preNode;
    ArtMVNodeAddrT *mvChainTailNodeAddr = GetArtMVNodeAddr(&artCtx->memRunCtx, mvChainTailNode);
    if (mvChainTailNodeAddr == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    // 上次访问的节点非法，则按照顺序从头或尾开始遍历
    ArtMVNodeAddrT *curNodeAddr = scanPara->scanFromHeader ? mvChainHeaderNodeAddr : mvChainTailNodeAddr;
    TupleAddr expectRowId = artValue.rowId;
    while (curNodeAddr != NULL) {
        Status ret = FindChainNodeFromMVNodeByRowId(curNodeAddr, scanPara, expectRowId);
        if (ret == GMERR_OK) {
            return GMERR_OK;
        }
        DB_ASSERT(GMERR_NO_DATA);
        // 当前节点没找到
        ArtMVNodeT nextNode = scanPara->scanFromHeader ? curNodeAddr->nextNode : curNodeAddr->preNode;
        bool finish = scanPara->scanFromHeader ? ArtMVNodeIsInvalid(&nextNode) :
                                                 SameArtMVNode(nextNode, mvChainTailNodeAddr->curNode);
        if (finish) {
            return GMERR_NO_DATA;
        } else {
            curNodeAddr = GetArtMVNodeAddr(&artCtx->memRunCtx, nextNode);
            if (curNodeAddr == NULL) {
                return GMERR_UNEXPECTED_NULL_VALUE;
            }
        }
    }
    return GMERR_NO_DATA;
}

static Status InitArtMVChainScanPara(
    ArtMVChainScanParaT *scanPara, ArtMemMgrRunCtxT *memRunCtx, ArtRunChildSlotT *findSlot, bool scanFromHeader)
{
    scanPara->mvChainHeaderNodeAddr = GetArtMVNodeAddr(memRunCtx, findSlot->slot.rowIdChainPtr);
    if (scanPara->mvChainHeaderNodeAddr == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    scanPara->findSlot = findSlot;
    scanPara->scanFromHeader = scanFromHeader;
    scanPara->foundMVNodeAddr = NULL;
    scanPara->foundChainNodeIndex = DB_INVALID_UINT16;
    return GMERR_OK;
}

#ifndef NDEBUG
static void NoSameAddrInMVChain(ArtRunningCtxT *artRunCtx, ArtInsertKeyToSlotParaT *insertPara)
{
    ArtMVChainScanParaT scanPara;
    InitArtMVChainScanPara(&scanPara, &artRunCtx->memRunCtx, &insertPara->insertKeyToChildSlotPara.childSlot, true);
    Status ret = MVChainScan(artRunCtx, insertPara->artValue, &scanPara);
    DB_ASSERT(ret != GMERR_OK);
    DB_ASSERT(scanPara.foundMVNodeAddr == NULL);
    DB_ASSERT(scanPara.foundChainNodeIndex == DB_INVALID_UINT16);
}
#endif

// 插入前，需要调用 ArtCanInsertToChain 判断是否可以插入版本链
Status ArtMVChainInsertProc(ArtRunningCtxT *artRunCtx, ArtInsertKeyToSlotParaT *insertPara)
{
    uint8_t slotType = insertPara->insertKeyToChildSlotPara.childSlot.childMeta.slotType;
    ArtMVNodeT mvChainHeaderNode = insertPara->insertKeyToChildSlotPara.childSlot.slot.rowIdChainPtr;
    Status ret = GMERR_OK;
    if (slotType == (uint8_t)ART_ROWID) {
        ret = Insert2MVChainForFirst(artRunCtx, insertPara);
        return ret;
    }
    DB_ASSERT(slotType == (uint8_t)ART_CHAIN_ROWID);
    DB_ASSERT((!ArtMVNodeIsInvalid(&mvChainHeaderNode)));
    ArtMVNodeAddrT *mvChainHeaderNodeAddr = GetArtMVNodeAddr(&artRunCtx->memRunCtx, mvChainHeaderNode);
    if (mvChainHeaderNodeAddr == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    // 链表尾结点
    ArtMVNodeT mvChainTailNode = mvChainHeaderNodeAddr->preNode;
    ArtMVNodeAddrT *mvChainTailNodeAddr = GetArtMVNodeAddr(&artRunCtx->memRunCtx, mvChainTailNode);
    if (mvChainTailNodeAddr == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
#ifndef NDEBUG
    NoSameAddrInMVChain(artRunCtx, insertPara);
#endif
    // 遍历得到第一个可以插入的node
    ArtMVNodeAddrT *dstNodeAddr = NULL;
    ret = TryFindNode2Insert(artRunCtx, mvChainHeaderNodeAddr, mvChainTailNodeAddr, insertPara, &dstNodeAddr);
    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        return ret;
    }
    // 找到，插入
    ArtChainNodeT node2Insert;
    InitNewChainNode(&node2Insert, insertPara);
    if (ret == GMERR_OK) {
        InsertArtChainNode2ArtMVNode(artRunCtx, dstNodeAddr, &node2Insert);
    } else {
        // 没找到，尝试申请新node后插入
        ArtMVNodeT newNode = INVALID_ART_MV_NODE;
        ret = AllocArtMVNode(artRunCtx, &newNode);
        if (ret != GMERR_OK) {
            return ret;
        }

        ArtMVNodeAddrT *newNodeAddr = GetArtMVNodeAddr(&artRunCtx->memRunCtx, newNode);
        DB_ASSERT(newNodeAddr != NULL);
        InsertArtChainNode2ArtMVNode(artRunCtx, newNodeAddr, &node2Insert);
        InsertArtMVNode2ArtMVChain(artRunCtx, newNodeAddr, mvChainHeaderNodeAddr, mvChainTailNodeAddr, insertPara);
    }
    return ret;
}

Status ArtMVChainInsert(ArtRunningCtxT *artRunCtx, ArtInsertKeyToSlotParaT *insertPara)
{
    DbSpinLock(&artRunCtx->art->mvPageInfo.lock);
    Status ret = ArtMVChainInsertProc(artRunCtx, insertPara);
    DbSpinUnlock(&artRunCtx->art->mvPageInfo.lock);
    return ret;
}

void PutArtMVChain2FreeList(
    ArtRunningCtxT *artRunCtx, ArtRunChildSlotT *artSlot, ArtMVNodeAddrT *headerNodeAddr, ArtMVNodeAddrT *node2Free)
{
    DB_POINTER4(artRunCtx, artSlot, headerNodeAddr, node2Free);
    ArtMVPageHeaderT *pageHeader =
        GetArtMVPageHeaderById(&artRunCtx->memRunCtx, node2Free->curNode.blockId, SE_INVALID_PAGE_ADDR);
    if (pageHeader == NULL) {
        return;
    }
    bool headerNode = SameArtMVNode(node2Free->curNode, headerNodeAddr->curNode);
    // 要释放的是头结点
    if (headerNode) {
        // 链表就一个节点
        if (SameArtMVNode(node2Free->preNode, headerNodeAddr->curNode)) {
            // 断开版本链
            artSlot->childMeta.slotType = (uint8_t)ART_ROWID;
            artSlot->slot.rowId = HEAP_INVALID_ADDR;
        } else {
            ArtMVNodeT newHeader = node2Free->nextNode;
            ArtMVNodeAddrT *newHeaderAddr = GetArtMVNodeAddr(&artRunCtx->memRunCtx, newHeader);
            if (newHeaderAddr == NULL) {
                return;
            }
            // 链头更新
            newHeaderAddr->preNode = headerNodeAddr->preNode;
            // 维护版本链
            artSlot->slot.rowIdChainPtr = newHeader;
        }
    } else {
        //  要释放的是尾结点
        if (ArtMVNodeIsInvalid(&node2Free->nextNode)) {
            ArtMVNodeT newTail = node2Free->preNode;
            ArtMVNodeAddrT *newTailAddr = GetArtMVNodeAddr(&artRunCtx->memRunCtx, newTail);
            if (newTailAddr == NULL) {
                return;
            }
            headerNodeAddr->preNode = newTail;
            newTailAddr->nextNode = INVALID_ART_MV_NODE;
        } else {
            // 释放的是中间节点
            ArtMVNodeAddrT *leftNodeAddr = GetArtMVNodeAddr(&artRunCtx->memRunCtx, node2Free->preNode);
            ArtMVNodeAddrT *rightNodeAddr = GetArtMVNodeAddr(&artRunCtx->memRunCtx, node2Free->nextNode);
            if (leftNodeAddr == NULL || rightNodeAddr == NULL) {
                return;
            }
            leftNodeAddr->nextNode = rightNodeAddr->curNode;
            rightNodeAddr->preNode = leftNodeAddr->curNode;
        }
    }
    // 重置
    InitArtMVNode(node2Free, node2Free->curNode.blockId, node2Free->curNode.offset);
    // 头插进空闲链表上
    node2Free->nextNode = pageHeader->freeNodeListHeader;
    pageHeader->freeNodeListHeader = node2Free->curNode;
    pageHeader->freeListNodeCnt++;
    // 释放页
    if (pageHeader->freeListNodeCnt == pageHeader->usedNodeCnt) {
        FreeMVPage(artRunCtx, pageHeader->blockId);
    }
}

static bool MVNodeCanBeFree(ArtMVNodeAddrT *mvNodeAddr)
{
    for (uint16_t i = 0; i < mvNodeAddr->hashMVEntryNumPerNode; i++) {
        ArtChainNodeT *chainNode = GetArtChainNode(mvNodeAddr, sizeof(ArtChainNodeT), i);
        if (!ArtChainNodeIsFree(chainNode)) {
            return false;
        }
    }
    return true;
}

void RemoveArtChainNodeFromArtMVNode(ArtRunningCtxT *artRunCtx, ArtMVChainScanParaT *scanPara)
{
    ArtChainNodeT *chainNode =
        GetArtChainNode(scanPara->foundMVNodeAddr, sizeof(ArtChainNodeT), scanPara->foundChainNodeIndex);
    DB_ASSERT(chainNode != NULL);
    // 删除chain node
    chainNode->rowId = HEAP_INVALID_ADDR;
    chainNode->delFlag = 0;
    scanPara->foundMVNodeAddr->hashMVEntryUsedNum--;
}

static void RemoveMVNodeFromMVChain(ArtRunningCtxT *artCtx, ArtMVChainScanParaT *scanPara)
{
    ArtMVNodeAddrT *headerNodeAddr = scanPara->mvChainHeaderNodeAddr;
    // 链表尾结点
    ArtMVNodeT mvChainTailNode = headerNodeAddr->preNode;
    ArtMVNodeAddrT *mvChainTailNodeAddr = GetArtMVNodeAddr(&artCtx->memRunCtx, mvChainTailNode);
    if (mvChainTailNodeAddr == NULL) {
        return;
    }
    DB_ASSERT((ArtMVNodeIsInvalid(&mvChainTailNodeAddr->nextNode)));
    // 获得node的个数
    uint32_t nodeCnt = 0;
    ArtMVNodeAddrT *curNodeAddr = headerNodeAddr;
    while (curNodeAddr != NULL) {
        nodeCnt++;
        ArtMVNodeT nextNode = curNodeAddr->nextNode;
        // 下一个节点非法，说明当前节点是尾节点
        if (ArtMVNodeIsInvalid(&nextNode)) {
            break;
        }
        curNodeAddr = GetArtMVNodeAddr(&artCtx->memRunCtx, nextNode);
        if (curNodeAddr == NULL) {
            return;
        }
    }
    // 从尾开始，遍历每个mvNode，查看是否可以释放
    curNodeAddr = mvChainTailNodeAddr;
    uint32_t visitedCnt = 0;
    while (curNodeAddr != NULL) {
        ArtMVNodeT nextNode = curNodeAddr->preNode;
        if (MVNodeCanBeFree(curNodeAddr)) {
            PutArtMVChain2FreeList(artCtx, scanPara->findSlot, scanPara->mvChainHeaderNodeAddr, curNodeAddr);
        }
        visitedCnt++;
        if (visitedCnt == nodeCnt) {
            return;
        }
        curNodeAddr = GetArtMVNodeAddr(&artCtx->memRunCtx, nextNode);
        if (curNodeAddr == NULL) {
            return;
        }
    }
}

static Status MVChainScanRemove(
    ArtRunningCtxT *artRunCtx, ArtRunChildSlotT *findSlot, ArtValueT artValue, bool scanFromHeader)
{
    ArtMVChainScanParaT scanPara;
    Status ret = InitArtMVChainScanPara(&scanPara, &artRunCtx->memRunCtx, findSlot, scanFromHeader);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = MVChainScan(artRunCtx, artValue, &scanPara);
    // 扫描发生错误
    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        return ret;
    }
    // 找到chain node，删除
    if (ret == GMERR_OK) {
        RemoveArtChainNodeFromArtMVNode(artRunCtx, &scanPara);
    }
    // 尝试回收版本链
    RemoveMVNodeFromMVChain(artRunCtx, &scanPara);

    return GMERR_OK;
}

// GC删除，调用者保证版本链存在
// GC删除，先删除老版本数据，后删除新版本数据；头插法，老数据在链表尾；尾插法，老数据在链表头
Status ArtMVChainRemove(ArtRunningCtxT *artCtx, ArtRunChildSlotT *findSlot, ArtValueT artValue)
{
    DB_ASSERT(findSlot->childMeta.slotType == (uint8_t)ART_CHAIN_ROWID);
    bool scanFromHeader = !artCtx->art->mvPageInfo.insertFromHeader;
    DbSpinLock(&artCtx->art->mvPageInfo.lock);
    Status ret = MVChainScanRemove(artCtx, findSlot, artValue, scanFromHeader);
    DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "ArtMVPage, remove rowId-%" PRIu64 ", scanFromHeader is %" PRIu8 "", (uint64_t)artValue.rowId,
            scanFromHeader);
    }
    return ret;
}

// undo删除：先删除最新版本，后删除旧版本；头插法，最新版本在链表头；尾插法，最新数据在链表尾
Status ArtMVChainUndoInsert(ArtRunningCtxT *artCtx, ArtRunChildSlotT *findSlot, ArtValueT artValue)
{
    bool scanFromHeader = artCtx->art->mvPageInfo.insertFromHeader;
    DbSpinLock(&artCtx->art->mvPageInfo.lock);
    Status ret = MVChainScanRemove(artCtx, findSlot, artValue, scanFromHeader);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "ArtMVPage, undo insert rowId-%" PRIu64 ", scanFromHeader is %" PRIu8 "",
            (uint64_t)artValue.rowId, scanFromHeader);
    }
    DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
    return ret;
}

Status ArtNeedDelFromChain(ArtRunningCtxT *artCtx, ArtRunChildSlotT *findSlot, ArtValueT artValue,
    IndexRemoveParaT rmPara, ArtDeleteParaT *deletePara)
{
    DbSpinLock(&artCtx->art->mvPageInfo.lock);
    ArtMVNodeAddrT *curNodeAddr = GetArtMVNodeAddr(&artCtx->memRunCtx, findSlot->slot.rowIdChainPtr);
    if (curNodeAddr == NULL) {
        DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    while (curNodeAddr != NULL) {
        for (uint16_t i = 0; i < curNodeAddr->hashMVEntryNumPerNode; i++) {
            ArtChainNodeT *chainNode = GetArtChainNode(curNodeAddr, sizeof(ArtChainNodeT), i);
            if (ArtChainNodeIsFree(chainNode)) {
                continue;
            }
            ArtRunChildSlotT slotTry2Del;
            ArtInitSlotByChainNode(&slotTry2Del, chainNode);
            ArtKeyValueDataT keyVData = {.artValue = artValue};
            Status ret = artCtx->artCbs->artDeleteFunc(artCtx, &slotTry2Del, keyVData, rmPara, deletePara);
            if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
                DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
                return ret;
            }
            chainNode->delFlag = slotTry2Del.childMeta.delFlag;
            if (deletePara->needDelete) {
                DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
                return GMERR_OK;
            }
        }

        // 下一个 ArtMVNode
        ArtMVNodeT nextNode = curNodeAddr->nextNode;
        // 遍历结束
        if (ArtMVNodeIsInvalid(&nextNode)) {
            DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
            return GMERR_OK;
        }
        curNodeAddr = GetArtMVNodeAddr(&artCtx->memRunCtx, nextNode);
        if (curNodeAddr == NULL) {
            DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
    }
    DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
    return GMERR_OK;
}

Status ArtFindChainNode(
    ArtRunningCtxT *artCtx, ArtMVNodeT mvChainHeader, ArtValueT artValue, ArtChainNodeT **foundSlot, bool *found)
{
    DbSpinLock(&artCtx->art->mvPageInfo.lock);
    ArtMVNodeAddrT *currNodeAddr = GetArtMVNodeAddr(&artCtx->memRunCtx, mvChainHeader);
    if (currNodeAddr == NULL) {
        DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    while (currNodeAddr != NULL) {
        for (uint16_t i = 0; i < currNodeAddr->hashMVEntryNumPerNode; i++) {
            ArtChainNodeT *chainNode = GetArtChainNode(currNodeAddr, sizeof(ArtChainNodeT), i);
            // 空节点
            if (ArtChainNodeIsFree(chainNode)) {
                continue;
            }
            // 找到rowId相同的节点
            if (chainNode->rowId == artValue.rowId) {
                *found = true;
                *foundSlot = chainNode;
                DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
                return GMERR_OK;
            }
        }
        // 下一个 ArtMVNode
        ArtMVNodeT nextNode = currNodeAddr->nextNode;
        // 遍历结束
        if (ArtMVNodeIsInvalid(&nextNode)) {
            DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
            return GMERR_NO_DATA;
        }
        currNodeAddr = GetArtMVNodeAddr(&artCtx->memRunCtx, nextNode);
        if (currNodeAddr == NULL) {
            DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
    }
    DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
    return GMERR_NO_DATA;
}

ArtChainNodeT *ArtGetChainNode(ArtRunningCtxT *artCtx, ArtChainNodeInfoT nodeInfo)
{
    DbSpinLock(&artCtx->art->mvPageInfo.lock);
    ArtMVPageHeaderT *pageHeader =
        GetArtMVPageHeaderById(&artCtx->memRunCtx, nodeInfo.mvNodeLocator.blockId, SE_INVALID_PAGE_ADDR);
    ArtMVNodeAddrT *mvNodeAddr = GetArtMVNode(pageHeader, nodeInfo.mvNodeLocator.offset);
    ArtChainNodeT *chainNode = GetArtChainNode(mvNodeAddr, sizeof(ArtChainNodeT), nodeInfo.chainNodeIndex);
    DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
    return chainNode;
}

Status ArtDumpAllChainNode2Arr(DbDynArrayT *dstArr, ArtRunningCtxT *artCtx, ArtMVNodeT mvChainHeader)
{
    DbSpinLock(&artCtx->art->mvPageInfo.lock);
    ArtMVNodeAddrT *currNodeAddr = GetArtMVNodeAddr(&artCtx->memRunCtx, mvChainHeader);
    if (currNodeAddr == NULL) {
        DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    while (currNodeAddr != NULL) {
        for (uint16_t i = 0; i < currNodeAddr->hashMVEntryNumPerNode; i++) {
            ArtChainNodeT *chainNode = GetArtChainNode(currNodeAddr, sizeof(ArtChainNodeT), i);
            // 空节点
            if (ArtChainNodeIsFree(chainNode)) {
                continue;
            }
            uint32_t itemId;
            HpTupleAddr *tmpAddr = (HpTupleAddr *)DbDynArrayAllocItem(dstArr, &itemId);
            if (tmpAddr == NULL) {
                DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
                DB_LOG_ERROR(
                    GMERR_OUT_OF_MEMORY, "ArtMVPage, unable to dump art expanded slot to arr, itemId:%" PRIu32, itemId);
                return GMERR_OUT_OF_MEMORY;
            }
            *tmpAddr = chainNode->rowId;
        }
        // 下一个 ArtMVNode
        ArtMVNodeT nextNode = currNodeAddr->nextNode;
        // 遍历结束
        if (ArtMVNodeIsInvalid(&nextNode)) {
            DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
            return GMERR_OK;
        }
        currNodeAddr = GetArtMVNodeAddr(&artCtx->memRunCtx, nextNode);
        if (currNodeAddr == NULL) {
            DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
    }
    DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
    return GMERR_OK;
}

// 从头遍历版本链，对每个有效的chainNode进行keycmp，当前仅供lpm索引使用
void ArtLookupAddrWithChainSlot(ArtRunningCtxT *artCtx, ArtMVNodeT mvChainHeader, bool *isFound, HpTupleAddr *addr)
{
    DbSpinLock(&artCtx->art->mvPageInfo.lock);
    ArtMVNodeAddrT *currNodeAddr = GetArtMVNodeAddr(&artCtx->memRunCtx, mvChainHeader);
    if (currNodeAddr == NULL) {
        DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
        return;
    }
    while (currNodeAddr != NULL) {
        for (uint16_t i = 0; i < currNodeAddr->hashMVEntryNumPerNode; i++) {
            ArtChainNodeT *chainNode = GetArtChainNode(currNodeAddr, sizeof(ArtChainNodeT), i);
            // 空节点
            if (ArtChainNodeIsFree(chainNode)) {
                continue;
            }
            int32_t cmp;
            Status cmpRet = artCtx->userCtx.cbUserCtx->idxOpenCfg.callbackFunc.keyCmp(
                artCtx->userCtx.cbUserCtx, artCtx->userCtx.cbUserCtx->idxKey, chainNode->rowId, &cmp, isFound);
            if (cmpRet != GMERR_OK && !SeIsFilterErrorCode(cmpRet)) {
                *addr = HEAP_INVALID_ADDR;
                *isFound = false;
                DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
                return;
            }
            if (*isFound) {
                DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
                return;
            }
        }

        // 下一个 ArtMVNode
        ArtMVNodeT nextNode = currNodeAddr->nextNode;
        // 遍历结束
        if (ArtMVNodeIsInvalid(&nextNode)) {
            DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
            return;
        }
        currNodeAddr = GetArtMVNodeAddr(&artCtx->memRunCtx, nextNode);
        if (currNodeAddr == NULL) {
            DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
            return;
        }
    }
    DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
}

Status ArtChainLoadKeyAndCompare(
    ArtRunningCtxT *artCtx, ArtKeyDataT keyData, ArtMVNodeT mvChainHeader, ArtKeyDataT *existKey)
{
    DbSpinLock(&artCtx->art->mvPageInfo.lock);
    ArtMVNodeAddrT *currNodeAddr = GetArtMVNodeAddr(&artCtx->memRunCtx, mvChainHeader);
    if (currNodeAddr == NULL) {
        DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    while (currNodeAddr != NULL) {
        for (uint16_t i = 0; i < currNodeAddr->hashMVEntryNumPerNode; i++) {
            ArtChainNodeT *chainNode = GetArtChainNode(currNodeAddr, sizeof(ArtChainNodeT), i);
            // 空节点
            if (ArtChainNodeIsFree(chainNode)) {
                continue;
            }
            Status ret = ArtLoadKey(artCtx, chainNode->rowId, NULL, existKey);
            if (ret == GMERR_OK) {  // 找到链上唯一存在的数据
                DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
                return GMERR_OK;
            }
            if (ret != GMERR_NO_DATA) {
                DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
                DB_LOG_ERROR(ret, "ArtMVPage, art load key, rowId:%" PRIu64, chainNode->rowId);
                return ret;
            }
            DB_ASSERT(GMERR_NO_DATA);
        }

        // 下一个 ArtMVNode
        ArtMVNodeT nextNode = currNodeAddr->nextNode;
        // 遍历结束
        if (ArtMVNodeIsInvalid(&nextNode)) {
            DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
            return GMERR_NO_DATA;
        }
        currNodeAddr = GetArtMVNodeAddr(&artCtx->memRunCtx, nextNode);
        if (currNodeAddr == NULL) {
            DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
    }
    DbSpinUnlock(&artCtx->art->mvPageInfo.lock);
    return GMERR_NO_DATA;
}

#ifdef __cplusplus
}
#endif
