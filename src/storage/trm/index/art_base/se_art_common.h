
/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: se_art_common.h
 * Description: head file of se art common
 * Author: wangsheng
 * Create: 2021-4-25
 */

#ifndef SE_ART_COMMON_H
#define SE_ART_COMMON_H

#include "adpt_types.h"
#include "se_page.h"
#include "se_opt_lock.h"

#ifdef __cplusplus
extern "C" {
#endif

#define ART_NODE4_CAPACITY 4
#define ART_NODE16_CAPACITY 16
#define ART_NODE48_CAPACITY 48
#define ART_NODE256_CAPACITY 256
#define ART_MAX_PREFIX_LEN 8u  // ART 最大公共前缀长度
#define ART_INVALID_NODE_PTR ART_MEM_NULL_NODE_PTR

typedef enum ArtNodeType {
    ART_NODE_TYPE_4 = 0,
    ART_NODE_TYPE_16,
    ART_NODE_TYPE_48,
    ART_NODE_TYPE_256,
    ART_INVALID_NODE_TYPE,
} ArtNodeTypeE;

typedef enum ArtSlotType {
    ART_INVALID_SLOT = 0,
    ART_ROWID,
    ART_CHILD_ADDR,
    ART_LPM_ARR_ADDR,
    ART_CHAIN_ROWID,
} ArtSlotTypeE;  // ART 节点的槽位信息

typedef enum ArtInnerStatus {
    ART_SUCCESS = 0,
    ART_NODE_MERGE,
    ART_DELETE_SINGLE_CHILDNODE_ALL,
    ART_DELETE_SINGLE_CHILDNODE_WITH_PREFIXSLOT,
    ART_FETCH_NEXT_END,
    ART_DATA_EXCEPTION_INVALID_INNERNODE,
    ART_FIND_PARENT_CONTINUE,
    ART_FIND_PARENT_STOP
} ArtInnerStatusT;

typedef union ArtNodePtr {
    struct {
        uint32_t pageId;
        uint16_t blockId : 15;
        uint16_t isDeleted : 1;
        uint16_t offset;  // page must not bigger than 64 KB
    };
    uint8_t *addr;
} ArtNodePtrT;

typedef struct ArtKeyData {
    uint8_t *keyBuf;
    uint32_t keyLen;
} ArtKeyDataT;

typedef struct ArtChildMeta {
    __attribute__((aligned(1))) uint8_t slotType : 3;  // value, child addr, lpm addr
    uint8_t maskLen : 4;                               // ip mask len
    uint8_t delFlag : 1;                               // delete flag for child value slot
} ArtChildMetaT;

static_assert(sizeof(ArtChildMetaT) == 1, "ArtChildMetaT size should be 1 byte");

typedef struct ArtNodeMeta {
    uint8_t type : 3;
    uint8_t prefixLen : 5;
} ArtNodeMetaT;

/*
 * art 节点类型
 * 当前共有4中节点类型: node4, node16, node48, node256
 * 所有的节点由两部分组成： 该节点类型基本信息 + 槽位信息
 * #1 节点类型基本信息，该部分在静态编译时就已经确定，其中 childMeta 主要保存后面槽位的元信息
 * #2 槽位信息，该部分为实际节点存储的value，主要有三种类型：
 *    #2.1 指向下一层孩子节点的逻辑addr，占用4字节
 *    #2.2 指向记录的 Tuple address， 占用4字节
 *    #2.3 指向lpm数组的虚拟指针， 占用8字节，该类型只针对lpm索引使用
 * 每个槽位只保存三种中的一种，为了降低内存开销，同时为了节点类型的通用性，内存大小主要根据索引类型动态申请，
 * 其内存空间紧接在基本节点后面：
 *   +---------------------------------------+
 *   | +--------------------   ------------+ |
 *   | | head+keys+childMeta| | value slot | |
 *   | +--------------------   ------------+ |
 *   +---------------------------------------+
 */

#pragma pack(4)

typedef struct ArtNode {
#ifdef ART_CONTAINER
    OptLockT optLock;
    DbMemCtxT *memCtx;  // node属于哪个memCtx
#endif
    ArtNodeMetaT meta;
    ArtChildMetaT prefixChildMeta;
    uint16_t numChildren;                // numChildren 的范围是 [0-256]，所以使用uint8 是不够的
    uint8_t prefix[ART_MAX_PREFIX_LEN];  // 依旧使用定长的前缀长度 4
} ArtNodeT;

typedef struct ArtNode4 {
    ArtNodeT nodeHead;
    uint8_t keys[ART_NODE4_CAPACITY];
    ArtChildMetaT childMeta[ART_NODE4_CAPACITY];
    uint8_t childSlots[];
} ArtNode4T;

inline static ArtNode4T *ArtCastNodeAsNode4(ArtNodeT *node)
{
    DB_POINTER(node);
    DB_ASSERT(node->meta.type == (uint8_t)ART_NODE_TYPE_4);
    return (ArtNode4T *)(void *)node;
}

typedef struct ArtNode16 {
    ArtNodeT nodeHead;
    uint8_t keys[ART_NODE16_CAPACITY];
    ArtChildMetaT childMeta[ART_NODE16_CAPACITY];
    uint8_t childSlots[];
} ArtNode16T;

inline static ArtNode16T *ArtCastNodeAsNode16(ArtNodeT *node)
{
    DB_POINTER(node);
    DB_ASSERT(node->meta.type == (uint8_t)ART_NODE_TYPE_16);
    return (ArtNode16T *)(void *)node;
}

typedef struct ArtNode48 {
    ArtNodeT nodeHead;
    uint8_t keys[ART_NODE256_CAPACITY];
    ArtChildMetaT childMeta[ART_NODE48_CAPACITY];
    uint8_t childSlots[];
} ArtNode48T;

inline static ArtNode48T *ArtCastNodeAsNode48(ArtNodeT *node)
{
    DB_POINTER(node);
    DB_ASSERT(node->meta.type == (uint8_t)ART_NODE_TYPE_48);
    return (ArtNode48T *)(void *)node;
}

#define ART_NODE48_INVALID_KEY ((uint8_t)ART_NODE48_CAPACITY)

inline static bool ArtNode48KeyIsValid(uint8_t key)
{
    return key < ART_NODE48_INVALID_KEY;
}

typedef struct ArtNode256 {
    ArtNodeT nodeHead;
    ArtChildMetaT childMeta[ART_NODE256_CAPACITY];
    uint8_t childSlots[];
} ArtNode256T;

inline static ArtNode256T *ArtCastNodeAsNode256(ArtNodeT *node)
{
    DB_POINTER(node);
    DB_ASSERT(node->meta.type == (uint8_t)ART_NODE_TYPE_256);
    return (ArtNode256T *)(void *)node;
}

#pragma pack()

uint32_t ArtLongestCommonPrefix(
    const uint8_t *prefix, int32_t curPrefixLen, ArtKeyDataT keyData, uint32_t pos, int32_t *cmpRes);

inline static bool IsArtChildSlotValid(ArtChildMetaT childMeta)
{
    return childMeta.slotType != (uint8_t)ART_INVALID_SLOT;
}

inline static bool IsValidSlotMeta(ArtChildMetaT childMeta)
{
    return ((childMeta.slotType == (uint8_t)ART_ROWID) || (childMeta.slotType == (uint8_t)ART_LPM_ARR_ADDR) ||
            (childMeta.slotType == (uint8_t)ART_CHAIN_ROWID));
}

inline static bool IsChildMetaChildAddrType(ArtChildMetaT childMeta)
{
    return childMeta.slotType == (uint8_t)ART_CHILD_ADDR;
}

inline static bool IfHavePrefixSlot(const ArtNodeT *nodeHead)
{
    DB_POINTER(nodeHead);
    return IsArtChildSlotValid(nodeHead->prefixChildMeta);
}

#ifdef __cplusplus
}
#endif

#endif  // __SE_ART_COMMON_H__
