/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: art multi version interface; MV is short for multiversion
 * Author: gaohaiyang
 * Create: 2024-03-11
 */
#ifndef SE_ART_MULTIVERSION_VERSION_PAGE_H
#define SE_ART_MULTIVERSION_VERSION_PAGE_H

#include "se_art.h"
#include "se_index.h"
#include "db_dyn_array.h"

#ifdef __cplusplus
extern "C" {
#endif

Status ArtMVChainInsert(ArtRunningCtxT *artRunCtx, ArtInsertKeyToSlotParaT *insertPara);

Status ArtMVChainRemove(ArtRunningCtxT *artCtx, ArtRunChildSlotT *findSlot, ArtValueT artValue);

Status ArtMVChainUndoInsert(ArtRunningCtxT *artCtx, ArtRunChildSlotT *findSlot, ArtValueT artValue);

Status ArtFindChainNode(
    ArtRunningCtxT *artCtx, ArtMVNodeT mvChainHeader, ArtValueT artValue, ArtChainNodeT **foundSlot, bool *found);

Status ArtCanInsertToChain(ArtRunningCtxT *artCtx, ArtRunChildSlotT *expandSlot, bool isLpm, bool *canInsertToChain);

Status ArtNeedDelFromChain(ArtRunningCtxT *artCtx, ArtRunChildSlotT *findSlot, ArtValueT artValue,
    IndexRemoveParaT rmPara, ArtDeleteParaT *deletePara);

inline static bool ArtMVNodeIsInvalid(const ArtMVNodeT *mvNode)
{
    return ((mvNode)->blockId == DB_INVALID_UINT16 && (mvNode)->offset == DB_INVALID_UINT16);
}

typedef struct {
    ArtMVNodeT mvNodeLocator;
    uint16_t chainNodeIndex;
} ArtChainNodeInfoT;

#define INVALID_ART_MV_NODE ((ArtMVNodeT){DB_INVALID_UINT16, DB_INVALID_UINT16})

#define INVALID_ART_CHAIN_NODE_INFO ((ArtChainNodeInfoT){INVALID_ART_MV_NODE, DB_INVALID_UINT16})

ArtChainNodeT *ArtGetChainNode(ArtRunningCtxT *artCtx, ArtChainNodeInfoT nodeInfo);

Status ArtDumpAllChainNode2Arr(DbDynArrayT *dstArr, ArtRunningCtxT *artCtx, ArtMVNodeT mvChainHeader);

void ArtLookupAddrWithChainSlot(ArtRunningCtxT *artCtx, ArtMVNodeT mvChainHeader, bool *isFound, HpTupleAddr *addr);

Status ArtChainLoadKeyAndCompare(
    ArtRunningCtxT *artCtx, ArtKeyDataT keyData, ArtMVNodeT mvChainHeader, ArtKeyDataT *existKey);

// 将chainNode组装为一个childSlot
void ArtInitSlotByChainNode(ArtRunChildSlotT *childSlot, const ArtChainNodeT *chainNode);

#ifdef __cplusplus
}
#endif
#endif  // __SE_ART_MULTIVERSION_VERSION_PAGE_H__
