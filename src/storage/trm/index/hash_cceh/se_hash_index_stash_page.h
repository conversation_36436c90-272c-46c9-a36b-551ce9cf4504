/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: se_hash_index stash_page.h
 * Description: hash index stash page interfaces
 * Author:
 * Create: 2022/12/05
 */

#ifndef SE_HASH_INDEX_STASH_PAGE_H
#define SE_HASH_INDEX_STASH_PAGE_H

#include "se_heap_base.h"
#include "se_hash_common.h"

#ifdef __cplusplus
extern "C" {
#endif

#define HASH_STASH_PAGE_FLAG 1
#define STASH_PAGE_MAX_NUM 10
#define HASH_STATISTICS_HASH_CODE_NUM 15
#define MAX_TOTAL_LOG_TEXT_LEN 1024
#define MAX_SINGLE_LOG_TEXT_LEN 128

inline static void HashEntryBaseSetStashPageFlag(HashEntryBaseT *lastEntryBase)
{
    // 问题：cceh insert时，如果在探测2个cacheline后，发现需要插入的hashcode与cacheline上所有hashcode相同
    // 如果继续分裂，因为hashcode相同，结果会散列到同一个page上，最后在分裂5次(HASH_ONE_INSERT_EXPAND_LIMITED)后报错
    // 处理：在cacheline最后一个entry打上标记位，用于标记hash冲突溢出。维护一个stashPage，将溢出的hashcode插入到stashPage
    lastEntryBase->probeLen = HASH_STASH_PAGE_FLAG;
}

inline static bool HashEntryBaseIsSetStashPageFlag(const HashEntryBaseT *lastEntryBase)
{
    return lastEntryBase->probeLen == HASH_STASH_PAGE_FLAG;
}

inline static HtStashPageHeadT *HashGetStashPageHeadByAddr(uint8_t *stashPageAddr)
{
    return (HtStashPageHeadT *)(void *)stashPageAddr;
}

// start entry实际位置需要偏移bitmap
inline static uint8_t *HashStashPageGetStartEntry(HtStashPageHeadT *head)
{
    return head->hashEntry + head->bitmapArrSize * sizeof(uint8_t);
}

inline static HashEntryBaseT *HashStashPageGetHashEntryBase(HtStashPageHeadT *head, uint32_t entryIdx)
{
    return (HashEntryBaseT *)(void *)(HashStashPageGetStartEntry(head) + (size_t)entryIdx * head->hashEntrySize);
}

inline static HashEntryBaseT *HashStashPageGetHashEntryBaseAndAddr(
    HashTableT *ht, HtStashPageHeadT *head, uint32_t entryIdx, TupleAddr *addr)
{
    HashEntryBaseT *hashEntryBase = HashStashPageGetHashEntryBase(head, entryIdx);
    uint8_t *addrBegin = (uint8_t *)((uintptr_t)hashEntryBase + sizeof(HtHashCodeT));
    *addr = HeapUncompressTupleAddr(addrBegin, ht->heapTupleAddrMode);
    return hashEntryBase;
}

inline static bool HashIsLastCachelineEntryWithStashPageFlag(
    HashEntryBaseT hashEntryBase, uint32_t hashEntriesPerCacheline, uint32_t idx)
{
    return ((idx % hashEntriesPerCacheline) == (hashEntriesPerCacheline - 1) &&
            (hashEntryBase.probeLen == HASH_STASH_PAGE_FLAG));
}

inline static void HashSetLastEntryStashPageFlag(HashTableT *ht, PageHeadT *segmentPage, uint32_t idx)
{
    uint8_t *pageBody = (uint8_t *)(segmentPage + 1);
    HashEntryBaseT *currentEntryBase = GetHashEntryBase(ht, pageBody, idx);
    HashEntryBaseSetStashPageFlag(currentEntryBase);
}

void IdxStashPageRlock(const IndexCtxT *idxCtx, LatchAddrTypeE latchAddrType);
void IdxStashPageRUnlock(const IndexCtxT *idxCtx);

Status HashStashPageInsert(
    IndexCtxT *idxCtx, HashInsertParaT *para, HtInsertProcResT *res, HtInsertProbeCtxT *probeCtx);

Status HashStashPageLookupWithoutOldRecord(IndexCtxT *idxCtx, HashLookupParaT *para);

Status HashStashPageLookupWithOldRecord(
    IndexCtxT *idxCtx, HashLookupParaT *para, bool *foundNewAddr, TupleAddr *newAddr);

Status HashStashPageRemove(IndexCtxT *idxCtx, const HashLookupParaT *para, HashRemoveParaT *rmPara);

Status HashStashPageDrop(HashTableT *ht, HashMemRunCtxT *memRunCtx);

Status HashStashPageCheckUniqueAndFindSlot(
    IndexCtxT *idxCtx, HashInsertParaT *para, HtInsertProcResT *res, HtInsertProbeCtxT *probeCtx);

Status HashGetStashPageHeadByPageId(HashMemRunCtxT *memRunCtx, uint32_t pageId, HtStashPageHeadT **stashPageHead);

void HashStashPageAbnormalStatistics(
    IndexCtxT *idxCtx, HashInsertParaT *para, uint8_t *pageBody, const HtInsertProbeCtxT *probeCtx);

#ifdef __cplusplus
}
#endif
#endif  // __SE_HASH_INDEX_STASH_PAGE_H__
