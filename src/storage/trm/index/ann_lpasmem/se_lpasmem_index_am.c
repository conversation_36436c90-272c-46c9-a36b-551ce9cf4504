/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: implementation of lpasmem interface to executor
 * Author: wanyi
 * Create: 2024-02-19
 */
#include <dlfcn.h>
#include "se_lpasmem_index.h"

#include "se_ann_index_inner.h"

#include <sys/time.h>
#include <time.h>

#define LIB_LPASMEM_SO_NAME "liblpasmem.so"

Status LpasMemIndexBuildEmpty(IndexCtxT *idxCtx, VecBuildStateParaT *buildStatePara)
{
    DB_UNUSED2(idxCtx, buildStatePara);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status LpasMemIndexUndoBuildEmpty(ShmemPtrT idxShmAddr, uint16_t instanceId)
{
    DB_UNUSED2(idxShmAddr, instanceId);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

AnnFuncT g_lpasIndexAnnFunc = {
    .annIdxBuild = LpasMemIndexBuildEmpty,
    .annIdxUndoBuild = LpasMemIndexUndoBuildEmpty,
};

Status LpasMemIndexBuild(IndexCtxT *idxCtx, VecBuildStateParaT *buildStatePara)
{
    return g_lpasIndexAnnFunc.annIdxBuild(idxCtx, buildStatePara);
}

Status LpasMemIndexUndoBuild(ShmemPtrT idxShmAddr, uint16_t instanceId)
{
    return g_lpasIndexAnnFunc.annIdxUndoBuild(idxShmAddr, instanceId);
}

Status LpasMemIndexCreateEmpty(SeRunCtxT *seRunCtx, IndexMetaCfgT idxCfg, ShmemPtrT *idxShmAddr)
{
    DB_UNUSED3(seRunCtx, idxCfg, idxShmAddr);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status LpasMemIndexMarkDropEmpty(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    DB_UNUSED2(seRunCtx, idxShmAddr);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

void LpasMemIndexDropEmpty(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    DB_UNUSED2(seRunCtx, idxShmAddr);
    return;
}

Status LpasMemIndexOpenEmpty(IndexCtxT *idxCtx)
{
    DB_UNUSED(idxCtx);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

void LpasMemIndexCloseEmpty(IndexCtxT *idxCtx)
{
    DB_UNUSED(idxCtx);
    return;
}

Status LpasMemIndexInsertEmpty(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr dataId)
{
    DB_UNUSED3(idxCtx, idxKey, dataId);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status LpasMemIndexDeleteEmpty(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr dataId, IndexRemoveParaT para)
{
    DB_UNUSED4(idxCtx, idxKey, dataId, para);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status LpasMemIndexBeginScanEmpty(IndexCtxT *idxCtx, IndexScanCfgT scanCfg, IndexScanItrT *iter)
{
    DB_UNUSED3(idxCtx, scanCfg, iter);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status LpasMemIndexScanEmpty(IndexCtxT *idxCtx, IndexScanItrT iter, HpTupleAddr *addr, bool *isFound)
{
    DB_UNUSED4(idxCtx, iter, addr, isFound);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

void LpasMemIndexEndScanEmpty(const IndexCtxT *idxCtx, IndexScanItrT iter)
{
    DB_UNUSED2(idxCtx, iter);
    return;
}

Status LpasMemIndexUndoInsertEmpty(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr)
{
    DB_UNUSED3(idxCtx, idxKey, addr);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status LpasMemIndexUndoRemoveEmpty(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr)
{
    DB_UNUSED3(idxCtx, idxKey, addr);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

void LpasMemGetCtxSizeEmpty(size_t *ctxSize, size_t *iterSize)
{
    DB_UNUSED2(ctxSize, iterSize);
    return;
}

Status LpasMemIndexTableLoadEmpty(IndexCtxT *idxCtx)
{
    DB_UNUSED(idxCtx);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

IdxFuncT g_lpasIndexFunc = {
    .idxCreate = LpasMemIndexCreateEmpty,
    .idxDrop = LpasMemIndexMarkDropEmpty,
    .idxCommitDrop = LpasMemIndexDropEmpty,
    .idxTruncate = NULL,
    .idxCommitTruncate = NULL,
    .idxOpen = LpasMemIndexOpenEmpty,
    .idxClose = LpasMemIndexCloseEmpty,
    .idxInsert = LpasMemIndexInsertEmpty,
    .idxBatchInsert = NULL,
    .idxDelete = LpasMemIndexDeleteEmpty,
    .idxBatchDelete = NULL,
    .idxUpdate = NULL,
    .idxBatchUpdate = NULL,
    .idxLookup = NULL,
    .idxBatchLookup = NULL,
    .idxBeginScan = LpasMemIndexBeginScanEmpty,
    .idxScan = LpasMemIndexScanEmpty,
    .idxEndScan = LpasMemIndexEndScanEmpty,
    .idxSetDirection = NULL,
    .idxUndoInsert = LpasMemIndexUndoInsertEmpty,
    .idxUndoRemove = LpasMemIndexUndoRemoveEmpty,
    .idxUndoUpdate = NULL,
    .idxGetKeyCount = NULL,
    .idxStatView = NULL,
    .idxGetPageSize = NULL,
    .idxScaleIn = NULL,
    .idxGetEstimateMemSize = NULL,
    .idxGetCtxSize = LpasMemGetCtxSizeEmpty,
    .idxPreload = NULL,
    .idxTableLoad = LpasMemIndexTableLoadEmpty,
};

Status LpasMemIndexCreate(SeRunCtxT *seRunCtx, IndexMetaCfgT idxCfg, ShmemPtrT *idxShmAddr)
{
    return g_lpasIndexFunc.idxCreate(seRunCtx, idxCfg, idxShmAddr);
}

Status LpasMemIndexMarkDrop(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    return g_lpasIndexFunc.idxDrop(seRunCtx, idxShmAddr);
}

void LpasMemIndexDrop(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    return g_lpasIndexFunc.idxCommitDrop(seRunCtx, idxShmAddr);
}

Status LpasMemIndexOpen(IndexCtxT *idxCtx)
{
    return g_lpasIndexFunc.idxOpen(idxCtx);
}

void LpasMemIndexClose(IndexCtxT *idxCtx)
{
    return g_lpasIndexFunc.idxClose(idxCtx);
}

Status LpasMemIndexInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr dataId)
{
    return g_lpasIndexFunc.idxInsert(idxCtx, idxKey, dataId);
}

Status LpasMemIndexDelete(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr dataId, IndexRemoveParaT para)
{
    return g_lpasIndexFunc.idxDelete(idxCtx, idxKey, dataId, para);
}

Status LpasMemIndexBeginScan(IndexCtxT *idxCtx, IndexScanCfgT scanCfg, IndexScanItrT *iter)
{
    return g_lpasIndexFunc.idxBeginScan(idxCtx, scanCfg, iter);
}

Status LpasMemIndexScan(IndexCtxT *idxCtx, IndexScanItrT iter, HpTupleAddr *addr, bool *isFound)
{
    return g_lpasIndexFunc.idxScan(idxCtx, iter, addr, isFound);
}

void LpasMemIndexEndScan(const IndexCtxT *idxCtx, IndexScanItrT iter)
{
    return g_lpasIndexFunc.idxEndScan(idxCtx, iter);
}

Status LpasMemIndexUndoInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr)
{
    return g_lpasIndexFunc.idxUndoInsert(idxCtx, idxKey, addr);
}

Status LpasMemIndexUndoRemove(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr)
{
    return g_lpasIndexFunc.idxUndoRemove(idxCtx, idxKey, addr);
}

void LpasMemGetCtxSize(size_t *ctxSize, size_t *iterSize)
{
    return g_lpasIndexFunc.idxGetCtxSize(ctxSize, iterSize);
}

Status LpasMemIndexTableLoad(IndexCtxT *idxCtx)
{
    return g_lpasIndexFunc.idxTableLoad(idxCtx);
}

typedef void *(*FunctionPointer)(void);

void *loadLibrary(const char *libName)
{
    void *handle = dlopen(libName, RTLD_NOW | RTLD_GLOBAL);
    if (handle == NULL) {
        char *errorStr = dlerror();
        DB_LOG_ERROR(
            GMERR_LOAD_THIRD_PARTY_LIBRARY_FAILED, "dlopen %s: %s", libName, (errorStr != NULL) ? errorStr : "NULL");
    }
    return handle;
}

FunctionPointer loadSymbol(void *handle, const char *symName)
{
    FunctionPointer func = (FunctionPointer)dlsym(handle, symName);
    if (func == NULL) {
        char *errorStr = dlerror();
        DB_LOG_ERROR(
            GMERR_GET_THIRD_PARTY_FUNCTION_FAILED, "dlsym %s: %s", symName, (errorStr != NULL) ? errorStr : "NULL");
    }
    return func;
}

void LpasMemIndexAnnLoadFunc(void)
{
    void *handle = loadLibrary(LIB_LPASMEM_SO_NAME);
    if (handle == NULL) {
        DB_LOG_ERROR(GMERR_LOAD_THIRD_PARTY_LIBRARY_FAILED, "load library %s unsucc", LIB_LPASMEM_SO_NAME);
        return;
    }

    const char *symNames[] = {"LpasMemIndexBuildAlgo", "LpasMemIndexUndoBuildAlgo"};

    FunctionPointer *funcPointers[] = {
        (FunctionPointer *)&g_lpasIndexAnnFunc.annIdxBuild, (FunctionPointer *)&g_lpasIndexAnnFunc.annIdxUndoBuild};

    FunctionPointer tmpFuncs[ELEMENT_COUNT(funcPointers)];

    for (size_t i = 0; i < ELEMENT_COUNT(symNames); ++i) {
        tmpFuncs[i] = loadSymbol(handle, symNames[i]);
        if (tmpFuncs[i] == NULL) {
            DB_LOG_ERROR(GMERR_GET_THIRD_PARTY_FUNCTION_FAILED, "load symbol %s unsucc", symNames[i]);
            return;
        }
    }

    for (size_t i = 0; i < ELEMENT_COUNT(funcPointers); ++i) {
        *funcPointers[i] = tmpFuncs[i];
    }
}

void LpasMemIndexLoadFunc(void)
{
    void *handle = loadLibrary(LIB_LPASMEM_SO_NAME);
    if (handle == NULL) {
        DB_LOG_ERROR(GMERR_LOAD_THIRD_PARTY_LIBRARY_FAILED, "load library %s unsucc", LIB_LPASMEM_SO_NAME);
        return;
    }
    const char *symNames[] = {"LpasMemIndexCreateAlgo", "LpasMemIndexMarkDropAlgo", "LpasMemIndexDropAlgo",
        "LpasMemIndexOpenAlgo", "LpasMemIndexCloseAlgo", "LpasMemIndexInsertAlgo", "LpasMemIndexDeleteAlgo",
        "LpasMemIndexBeginScanAlgo", "LpasMemIndexScanAlgo", "LpasMemIndexEndScanAlgo", "LpasMemIndexUndoInsertAlgo",
        "LpasMemIndexUndoRemoveAlgo", "LpasMemGetCtxSizeAlgo", "LpasMemIndexTableLoadAlgo"};

    FunctionPointer *funcPointers[] = {(FunctionPointer *)&g_lpasIndexFunc.idxCreate,
        (FunctionPointer *)&g_lpasIndexFunc.idxDrop, (FunctionPointer *)&g_lpasIndexFunc.idxCommitDrop,
        (FunctionPointer *)&g_lpasIndexFunc.idxOpen, (FunctionPointer *)&g_lpasIndexFunc.idxClose,
        (FunctionPointer *)&g_lpasIndexFunc.idxInsert, (FunctionPointer *)&g_lpasIndexFunc.idxDelete,
        (FunctionPointer *)&g_lpasIndexFunc.idxBeginScan, (FunctionPointer *)&g_lpasIndexFunc.idxScan,
        (FunctionPointer *)&g_lpasIndexFunc.idxEndScan, (FunctionPointer *)&g_lpasIndexFunc.idxUndoInsert,
        (FunctionPointer *)&g_lpasIndexFunc.idxUndoRemove, (FunctionPointer *)&g_lpasIndexFunc.idxGetCtxSize,
        (FunctionPointer *)&g_lpasIndexFunc.idxTableLoad};

    FunctionPointer tmpFuncs[ELEMENT_COUNT(funcPointers)];

    for (size_t i = 0; i < ELEMENT_COUNT(symNames); ++i) {
        tmpFuncs[i] = loadSymbol(handle, symNames[i]);
        if (tmpFuncs[i] == NULL) {
            DB_LOG_ERROR(GMERR_GET_THIRD_PARTY_FUNCTION_FAILED, "load symbol %s unsucc", symNames[i]);
            return;
        }
    }

    for (size_t i = 0; i < ELEMENT_COUNT(funcPointers); ++i) {
        *funcPointers[i] = tmpFuncs[i];
    }
}

void LpasMemIndexAmInit(void)
{
    IdxFuncT LpasMemIndexFuncHandle = {
        .idxCreate = LpasMemIndexCreate,
        .idxDrop = LpasMemIndexMarkDrop,
        .idxCommitDrop = LpasMemIndexDrop,
        .idxTruncate = NULL,
        .idxCommitTruncate = NULL,
        .idxOpen = LpasMemIndexOpen,
        .idxClose = LpasMemIndexClose,
        .idxInsert = LpasMemIndexInsert,
        .idxBatchInsert = NULL,
        .idxDelete = LpasMemIndexDelete,
        .idxBatchDelete = NULL,
        .idxUpdate = NULL,
        .idxBatchUpdate = NULL,
        .idxLookup = NULL,
        .idxBatchLookup = NULL,
        .idxBeginScan = LpasMemIndexBeginScan,
        .idxScan = LpasMemIndexScan,
        .idxEndScan = LpasMemIndexEndScan,
        .idxSetDirection = NULL,
        .idxUndoInsert = LpasMemIndexUndoInsert,
        .idxUndoRemove = LpasMemIndexUndoRemove,
        .idxUndoUpdate = NULL,
        .idxGetKeyCount = NULL,
        .idxStatView = NULL,
        .idxGetPageSize = NULL,
        .idxScaleIn = NULL,
        .idxGetEstimateMemSize = NULL,
        .idxGetCtxSize = LpasMemGetCtxSize,
        .idxPreload = NULL,
        .idxTableLoad = LpasMemIndexTableLoad,
    };
    LpasMemIndexLoadFunc();
    IdxAmFuncRegister((uint8_t)LPASMEM_INDEX, &LpasMemIndexFuncHandle);
}

void LpasMemIndexAnnFuncInit(void)
{
    AnnFuncT LpasMemIndexAnnFunc = {
        .annIdxBuild = LpasMemIndexBuild,
        .annIdxUndoBuild = LpasMemIndexUndoBuild,
    };
    LpasMemIndexAnnLoadFunc();
    AnnFuncRegister((uint8_t)LPASMEM_INDEX, &LpasMemIndexAnnFunc);
}
