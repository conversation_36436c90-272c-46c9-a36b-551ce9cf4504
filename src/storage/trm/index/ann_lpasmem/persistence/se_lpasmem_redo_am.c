/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: implementation of diskann redo
 * Author:
 * Create:
 */
#include <dlfcn.h>
#include "se_lpasmem_redo_am.h"
#include "se_define.h"
#include "se_replay.h"

#define LIB_LPASMEM_SO_NAME "liblpasmem.so"

#define PAGE_REPLAY_SYMBOL "LpasMemIndexChangePageReplay"

void LpasMemRedoChangePageFuncImpl(
    const LpasMemRedoParamT *redoParam, const LpasMemEdgeT *edge, uint16_t pqVecDim, RedoRunCtxT *redoCtx)
{
    return;
}

RedoLogReplayFuncT g_lpasIndexPageReplay = NULL;

typedef void *(*FunctionPointer)(void);

FunctionPointer loadSymbol(void *handle, const char *symName)
{
    FunctionPointer func = (FunctionPointer)dlsym(handle, symName);
    if (func == NULL) {
        char *errorStr = dlerror();
        DB_LOG_ERROR(
            GMERR_GET_THIRD_PARTY_FUNCTION_FAILED, "dlsym %s: %s", symName, (errorStr != NULL) ? errorStr : "NULL");
    }
    return func;
}

void *loadLibrary(const char *libName)
{
    void *handle = dlopen(libName, RTLD_NOW | RTLD_GLOBAL);
    if (handle == NULL) {
        char *errorStr = dlerror();
        DB_LOG_ERROR(
            GMERR_LOAD_THIRD_PARTY_LIBRARY_FAILED, "dlopen %s: %s", libName, (errorStr != NULL) ? errorStr : "NULL");
    }
    return handle;
}

void LpasMemIndexLoadPageReplay(void)
{
    void *handle = loadLibrary(LIB_LPASMEM_SO_NAME);
    if (handle == NULL) {
        DB_LOG_ERROR(GMERR_LOAD_THIRD_PARTY_LIBRARY_FAILED, "load library %s unsucc", LIB_LPASMEM_SO_NAME);
        return;
    }

    RedoLogReplayFuncT redoFunc = (RedoLogReplayFuncT)loadSymbol(handle, PAGE_REPLAY_SYMBOL);
    if (redoFunc == NULL) {
        DB_LOG_ERROR(GMERR_GET_THIRD_PARTY_FUNCTION_FAILED, "load symbol %s unsucc", PAGE_REPLAY_SYMBOL);
        return;
    }
    g_lpasIndexPageReplay = redoFunc;
}

static StatusInter LpasMemRedoReplayFuncRegister(SeInstanceHdT seIns)
{
    REG_REPLAY_FUNC(seIns, REDO_OP_LPASMEM_CHANGE, g_lpasIndexPageReplay);
    return STATUS_OK_INTER;
}

void LpasMemIndexRedoAmInit(SeInstanceHdT seIns)
{
    LpasMemIndexRedoAmT redoAm = {.redoChangePageFunc = LpasMemRedoChangePageFuncImpl};
    LpasMemIndexRedoSetAmFunc(&redoAm);
    LpasMemIndexLoadPageReplay();
    LpasMemRedoReplayFuncRegister(seIns);
}
