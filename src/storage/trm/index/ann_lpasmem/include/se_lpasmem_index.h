/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: declaration of lpasmem type
 * Author: wanyi
 * Create: 2024-02-19
 */

#ifndef SE_LPASMEM_INDEX_H
#define SE_LPASMEM_INDEX_H

#include "se_ann_index.h"
#include "se_index_inner.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct VecLUResult {  // 向量查询结果
    uint32_t size;            // dataId的数量
    uint32_t *dataId;         // 查询得到向量Id数组
} VecLUResultT;

typedef struct LpasMemIndexAddrInfo {
    PageIdT slotId;
    uint32_t offset;
} LpasMemIndexAddrInfoT;

Status LpasMemIndexCreate(SeRunCtxT *seRunCtx, IndexMetaCfgT idxCfg, ShmemPtrT *idxShmAddr);
void LpasMemIndexDrop(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr);
Status LpasMemIndexMarkDrop(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr);
Status LpasMemIndexOpen(IndexCtxT *idxCtx);
void LpasMemIndexClose(IndexCtxT *idxCtx);
Status LpasMemIndexBuild(IndexCtxT *idxCtx, VecBuildStateParaT *buildStatePara);
Status LpasMemIndexUndoBuild(ShmemPtrT idxShmAddr, uint16_t instanceId);
Status LpasMemIndexBeginScan(IndexCtxT *idxCtx, IndexScanCfgT scanCfg, IndexScanItrT *iter);
Status LpasMemIndexScan(IndexCtxT *idxCtx, IndexScanItrT iter, HpTupleAddr *addr, bool *isFound);
void LpasMemIndexEndScan(const IndexCtxT *idxCtx, IndexScanItrT iter);
Status LpasMemIndexUndoInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr);
Status LpasMemIndexTableLoad(IndexCtxT *idxCtx);
Status LpasMemIndexUndoRemove(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr);

#ifdef __cplusplus
}
#endif  // __cplusplus
#endif  // SE_LPASMEM_EXPORT_H
