/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2024. All rights reserved.
 * File Name: se_hac_common.h
 * Description: hac common
 * Author: lijianchuan
 * Create: 2022/10/17
 */
#ifdef FEATURE_HAC
#ifndef SE_HAC_COMMON_H
#define SE_HAC_COMMON_H

#include "adpt_spinlock.h"
#include "db_list.h"
#include "se_define.h"
#include "se_index_inner.h"
#include "se_log.h"
#ifdef ACCELERATOR_MODE
#include "db_accelerator_pub.h"
#endif
#include "adpt_hac_driver.h"

#define ACCELERATOR_MAX_POOL_NUM 4      // 最大锁池数，非最终值
#define ACCELERATOR_MAX_LATCH_NUM 4096  // 锁池数最大锁数，非最终值
#define MULTI_HASH_MIN_SIZE 8u          // 最小单元8，hashSize只能是整数倍

typedef struct HacLogicAddress {
    uint32_t blockId;   // 4B [0,HAC_MAX_BLOCK_NUM-1]
    uint32_t bucketId;  // 4B [0,HAC_BLOCK_BUCKET_NUM-1]
} HacLogicAddressT;

typedef struct HacBucket {        // 固定桶的头部，不同桶类型都使用相同桶头部
    HacLogicAddressT nextBucket;  // 8B
    uint64_t reserved[15];        // 预留字节补齐2cacheline
} HacBucketT;

#define HAC_INVALID_LOGICADDR ((HacLogicAddressT){DB_INVALID_UINT32, DB_INVALID_UINT32})

#define HAC_BLOCK_BUCKET_NUM 1024  // 内存管理模块HacMemMgr的每个block包含的桶数，必须为8的倍数
#define HAC_BLOCK_SHIFT_BITS 10    // 2 << 10 = 1024
#define HASH_BUCKET_SIZE sizeof(HacBucketT)  // 桶大小固定2个cacheline
static_assert(HASH_BUCKET_SIZE == 128, "Hash bucket size is 128B");
#define HAC_MAX_BLOCK_NUM 64  // 内存管理模块HacMemMgr的最大block数，包含加速器的0-31，软件的32-63，非最终值
#define ACCELERATOR_MAX_BLOCK_NUM 32  // 加速器的最大block数
#define ACCELERATOR_BITS_PER_BYTE 8  // 一个字节有8位，用来计算多少字节的bitmap够表示ACCELERATOR_BLOCK_BUCKET_NUM
#define ACCELERATOR_BITMAP_SIZE (HAC_BLOCK_BUCKET_NUM / ACCELERATOR_BITS_PER_BYTE)
// 内存管理模块HacMemMgr的每个block的大小（含bitmap）
#define ACCELERATOR_BLOCK_SIZE ((HAC_BLOCK_BUCKET_NUM * HASH_BUCKET_SIZE) + ACCELERATOR_BITMAP_SIZE)
#define HAC_BLOCK_SIZE (HAC_BLOCK_BUCKET_NUM * HASH_BUCKET_SIZE)

#define SV_MULTI_HASH_INDEX_TIMEOUT_COUNT 5  // multi hash 视图超时次数

#define HAC_INVALID_SHMPTR DB_INVALID_SHMPTR

#ifdef __cplusplus
extern "C" {
#endif

inline static bool IsHacLogicAddrValid(HacLogicAddressT logicAddr)
{
    return logicAddr.blockId < HAC_MAX_BLOCK_NUM && logicAddr.bucketId < HAC_BLOCK_BUCKET_NUM;
}

inline static bool IsHacLogicAddrValidForAcc(HacLogicAddressT logicAddr)
{
    return logicAddr.blockId < ACCELERATOR_MAX_BLOCK_NUM && logicAddr.bucketId < HAC_BLOCK_BUCKET_NUM;
}

typedef enum HacMode {
    DISABLE_HAC = 0,                  // 硬件卸载不开启
    ENABLE_HAC_WITH_ACCELERATOR = 1,  // 使能硬件卸载，加速器模式
    ENABLE_HAC_WITH_SOFTWARE = 2,     // 软件模式，用于软件验证硬件卸载
} HacModeE;

typedef enum HacBlockType {
    HAC_BLOCK_UN_ALLOCATED = 0,  // 未分配
    HAC_BLOCK_ALLOCATED,         // 已分配待注入硬件使用
    HAC_BLOCK_USED,              // 已注入硬件使用
    HAC_BLOCK_FULL_USED,         // 软件专用，标记block全占用
    BLOCK_TYPE_END
} HacBlockTypeE;

// 操作类型
typedef enum HacOpCodeType {
    HAC_NOP = 0b000,                   // NOP（No operator）
    HAC_HASH_INSERT_OP = 0b001,        // hachash插入
    HAC_HASH_LOOKUP_OP = 0b010,        // hachash查询
    HAC_HASH_DELETE_OP = 0b011,        // hachash删除
    HAC_MIX_OP = 0b100,                // mix模式（软件暂不使用）
    HAC_MULTI_HASH_INSERT_OP = 0b101,  // multihash插入
    HAC_COPY_OP = 0b110,               // copy（软件暂不使用）
    HAC_MULTI_HASH_DELETE_OP = 0b111,  // multihash删除
    HAC_OP_MASK = 0b111,               // 末三位作为opCode掩码
} HacOpCodeTypeE;

// keycmp类型，这里的keycmp是软件说法，说明发生索引冲突后（仅hachash）进行keycmp的方式
typedef enum HacCmpType {
    HAC_KEY_COMPARE_BY_CALLBACK = 0b00000,    // 通过硬化keycmp比较key
    HAC_KEY_COMPARE_BY_TUPLE_ADDR = 0b01000,  // 通过比较tupleAddr的结果来比较key
    HAC_KEY_COMPARE_BY_SOFTWARE = 0b10000,    // 通过软件keycmp比较key，逃生方案
    HAC_NO_KEY_COMPARE = 0b11000,             // 不比较key，性能优化方案
} HacCmpTypeE;

// 拷贝类型
typedef enum HacCopyType {
    HAC_COPY_TYPE_FALSE = 0b000000,  // 不拷贝
    HAC_COPY_TYPE_TRUE = 0b100000,   // 拷贝，仅限软件查询
} HacCopyTypeE;

// batchtype: uint8
// reserved 7:6 | HacCopyType 5 | HacCmpType 4:3 | HacOpCodeType 2:0
typedef enum HacBatchType {
    // 0|00|001, 比较key判断冲突, 适用于一般HacHash插入场景
    HAC_HASH_INSERT = HAC_HASH_INSERT_OP + HAC_KEY_COMPARE_BY_CALLBACK + HAC_COPY_TYPE_FALSE,
    // 0|01|001, 比较tuple判断冲突, 适用于相同Key的MultiHash插入场景
    HAC_HASH_INSERT_COMPARE_TUPLE = HAC_HASH_INSERT_OP + HAC_KEY_COMPARE_BY_TUPLE_ADDR + HAC_COPY_TYPE_FALSE,
    // 0|01|011, 比较tuple判断冲突, 适用于一般HacHash删除场景和相同Key退化的MultiHash删除场景
    HAC_HASH_DELETE = HAC_HASH_DELETE_OP + HAC_KEY_COMPARE_BY_TUPLE_ADDR + HAC_COPY_TYPE_FALSE,
    // 0|01|111, 比较key判断冲突, 适用于一般MultiHash删除场景
    MULTI_HASH_DELETE = HAC_MULTI_HASH_DELETE_OP + HAC_KEY_COMPARE_BY_TUPLE_ADDR + HAC_COPY_TYPE_FALSE,
    // 0|10|001, 软件做key冲突判断, 适用于一般HacHash插入逃生场景
    HAC_HASH_INSERT_KEYCMP_BY_SOFT = HAC_HASH_INSERT_OP + HAC_KEY_COMPARE_BY_SOFTWARE + HAC_COPY_TYPE_FALSE,
    // 0|10|010, 软件做key冲突判断, 适用于一般HacHash查询逃生场景
    HAC_HASH_LOOKUP_KEYCMP_BY_SOFT = HAC_HASH_LOOKUP_OP + HAC_KEY_COMPARE_BY_SOFTWARE + HAC_COPY_TYPE_FALSE,
    // 0|11|001, 不比较key冲突，适用于HacHash主键插入场景
    HAC_HASH_INSERT_NO_KEYCMP = HAC_HASH_INSERT_OP + HAC_NO_KEY_COMPARE + HAC_COPY_TYPE_FALSE,
    // 0|11|101, 不比较key冲突, 适用于一般MultiHash插入场景
    MULTI_HASH_INSERT = HAC_MULTI_HASH_INSERT_OP + HAC_NO_KEY_COMPARE + HAC_COPY_TYPE_FALSE,
    // 1|00|010, 需要tuplebuf内存拷贝, 适用于一般HacHash查询场景
    HAC_HASH_LOOKUP = HAC_HASH_LOOKUP_OP + HAC_KEY_COMPARE_BY_CALLBACK + HAC_COPY_TYPE_TRUE,
} HacBatchTypeE;

typedef struct HacStatistics {
    uint32_t hacHashIdxCnt;        // hachash索引数目
    uint32_t multiHashIdxCnt;      // multihash索引数目
    uint64_t batchSoftwareCnt;     // 软件处理批量次数
    uint64_t batchHacCnt;          // 硬件处理批量次数
    uint64_t batchHacSyncCnt;      // 硬件同步批请求次数
    uint64_t batchHacAsyncCnt;     // 硬件异步批请求次数
    uint64_t batchExcapedCnt;      // 回调逃生次数
    uint64_t hacRqstCnt;           // 向加速器请求的总操作数
    uint64_t hacRspWarnCnt;        // 向加速器请求的提示性错误次数
    uint64_t hacRspNormalErrCnt;   // 向加速器请求的可回滚性错误次数
    uint64_t hacRspSpecialErrCnt;  // 向加速器请求的定位级错误次数
    uint8_t allocBlockCnt;         // 挂链占用的总block块数 [0,63]
    uint8_t allocHacBlockCnt;      // 挂链分配给硬件的的block块数 [0,31]
} HacStatisticsT;

typedef struct HacBlockMgr {
    HacBlockTypeE type;  // 当前block的状态，状态类型见HacBlockTypeE的定义
    ShmemPtrT block;  // 含大小为1024bit的bitmap，为节省内存底噪，将bitmap放在block前面，申请的时候一同申请，
                      // bitmap用来管理block的1024个buffer的状态，0为空闲，1为使用中
} HacBlockMgrT;

typedef struct HacMemMgr {
    DbSpinLockT hacMemLock;  // 内存管理模块的锁，申请释放时使用，控制并发
    HacBlockMgrT blockMgr[HAC_MAX_BLOCK_NUM];
} HacMemMgrT;

typedef struct HacMgr {
    // 当前驱动向加速器发送报文与从加速器接收报文并非原子操作，不可重入，需要发送报文锁和接收报文锁来保证并发
    DbSpinLockT sendMsgLock;
    DbSpinLockT receiveMsgLock;
    volatile uint32_t tokenCnt;   // 需要原子操作，对齐
    ShmemPtrT lockShm;            // 软件模拟锁，共享内存
    uint32_t hacCtxId;            // Hac上下文的唯一Id
    uint32_t pageSize;            // 全局的pageSize
    uint32_t chunkCntPerDev;      // memdata提供的每个device的chunk数，主线的memdata需要使用，预留
    uint32_t hashMaxMemSize;      // 最大使用内存
    uint32_t multiHashBucketCnt;  // multiHash一级桶数量
    ConcurrencyControlE ccType;   // 全局并发控制类型
    uint32_t tupleAddrSize;       // 根据tupleAddrMode存在4B、8B两种情况
    HacDriverTupleAddrModeE tupleAddrMode;  // 定义Hac全局参数,tupleAddr模式是否为32bits
    HacModeE hacMode;
    // 以下参数主要做硬化keycmp使用，会传给硬件
    uint8_t tupleShiftBit;
    uint8_t tupleShiftBit32;
    uint8_t addrShiftBit;
    uint8_t pageSizeShiftBit;
    uint32_t devMaxCount;
    ShmemPtrT hacMemMgr;  // 硬件加速器挂链的内存管理模块，直连读/直连写也要用，必须是共享内存addr
    HacStatisticsT hacStats;
} HacMgrT;

typedef struct HacMgrCtx {
    HacMgrT *hacMgr;
    DbMemCtxT *hacMemCtx;  // 硬件加速器挂链的内存管理模块使用的DbMemCtxT
    HacMemMgrT *hacMemMgr;
} HacMgrCtxT;

HacMgrT *GetHacMgr(void);
bool HacIsTupleMode64(void);
bool HacIsAcceleratorMode(void);
bool IsHacInitialized(void);
DbMemCtxT *GetHacMemCtx(void);
HacMemMgrT *GetHacMemMgr(void);

inline static HacStatisticsT *HacGetStats(void)
{
    return &GetHacMgr()->hacStats;
}

typedef struct {
    uint64_t addr;
    uint64_t len : 16;
    uint64_t reserved : 48;
} HacReqMessageT;

typedef struct {
    uint32_t ack : 2;
    uint32_t reserved1 : 1;
    uint32_t maxExtendedListLen : 4;             // 操作最长挂链
    uint32_t addedOrDeletedExtendedListNum : 1;  // 0:挂链数、1:回收链数
    uint32_t batchRes : 8;                       // 批应答，0：OK
    uint32_t extendedListNum : 8;                // 挂链数目变化
    uint32_t hashCollisionCnt : 8;               // hash冲突计数
    uint32_t srcId;
    uint32_t reqId;
    uint32_t reserved2;
} HacRespMessageT;

typedef Status (*IdxLookupFunc)(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr *addr, bool *isFound);
typedef Status (*IdxInsertOrDeleteFunc)(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr, bool isInsert);

typedef struct HacCtx {
    DmIndexTypeE idxType;
    HacBatchTypeE hacType;
    DbListT list;
    uint32_t batchNum;      // 批量个数
    uint32_t reqBodyLen;    // 请求报文体长度
    uint32_t respBodyLen;   // 应答报文体长度
    uint32_t callId;        // 不同线程批量的唯一Id
    uint32_t reqNum;        // 请求个数，批读有效
    uint32_t queryIdx;      // 在软件批量查询时标记是第几个查询，便于定位tuplesAddr的位置
    uint32_t tupleAddrLen;  // heap tuple addr总长度
    uint16_t tupleSize;     // 单个heap tuple大小
    uint32_t iteratorLen;   // 迭代器分配总长度
    uint32_t iteratorSize;  // 单个迭代器长度
    uint8_t *reqBodyAddr;   // 请求报文体addr
    uint8_t *respBodyAddr;  // 应答报文体addr
    IndexCtxT *indexCtx;
    uint8_t *tuplesAddr;    // 批读的拷贝tuple首addr
    uint8_t *iteratorAddr;  // 迭代器addr，MultiHash批量查询用
    DbMemCtxT *memCtx;
    IndexKeyT *idxKey;
    HpBatchOutT *addr;
    bool isSameSecEntry;
    bool isInsert;
    HacReqMessageT reqMes;
    HacRespMessageT respMes;
    IdxLookupFunc idxLookupFunc;
    IdxInsertOrDeleteFunc idxInsertOrDeleteFunc;
    IdxBatchLookupParaT *para;
} HacCtxT;

typedef enum HacStatusInternal {
    // common error
    HAC_SUCCESS = 0,

    // 索引操作错误
    HAC_ERROR_INDEX_UNIQUE_VIOLATION = 0x40,    // 插入key冲突错误
    HAC_WARNING_LOOKUP_NO_DATA = 0x41,          // 查询无数据警告
    HAC_WARNING_DELETE_NO_DATA = 0x42,          // 删除无数据警告
    HAC_WARNING_INSERT_CONFLICT = 0x43,         // 插入Hash冲突警告(在keycmpbyhac=false时出现)
    HAC_WARNING_LOOKUP_SINGLE_CONFLICT = 0x44,  // 查询单条数据Hash冲突警告(在keycmpbyhac=false时出现)
    HAC_WARNING_LOOKUP_MULTI_CONFLICT = 0x45,   // 查到多条数据Hash冲突警告(在keycmpbyhac=false时出现)

    // 需要额外处理的失败
    HAC_ERROR_LIST_ADDR_INVALID = 0x50,  // 挂链addr非法，不在硬件失败的范围，需要定位
    HAC_ERROR_EXCEED_LIST_MAX = 0x51,  // 挂链时，冲突率长度超过预定义值，此冲突出现后建议软件接管，该表不再与硬件交互
    HAC_ERROR_INSERT_LIST_FAILED = 0x52,
    //	申请挂链内存失败（硬件管理最大内存达到上限），单次插入失败，软件接管，索引不再与硬件交互
    HAC_ERROR_DOUBLE_FREE_MEMORY = 0x53,  // 重复释放内存，需要定位
    HAC_ERROR_FREE_LIST_FAILED = 0x54,    // 释放addr不合法，非DB场景

    // 处理流程的无效值，需要定位
    HAC_ERROR_INVALID_DEVICEID = 0x60,  // 无效deviceId
    HAC_ERROR_INVALID_BLOCKID = 0x61,   // 无效blockId
    HAC_ERROR_INVALID_SLOTID = 0x62,    // 无效slotId
    HAC_ERROR_INVALID_VIRADDR = 0x63,   // 无效devAddr
    HAC_ERROR_INVALID_SEGNUM = 0x64,    // 无效segnum
    HAC_ERROR_INVALID_TUPLELEN = 0x65,  // 无效tuple长度

    // 加解锁错误
    HAC_ERROR_LOCK_FAILED = 0x70,    //	申请锁失败(超时)
    HAC_ERROR_UNLOCK_FAILED = 0x71,  //	释放锁失败，但操作成功完成

    // 其他错误
    HAC_WARNING_WATERMARK_SHORT = 0x80,  //	剩余内存低于水线
    HAC_ERROR_OPERATION_TIMEOUT = 0x81,  //	操作超时

    HAC_ERROR_UNINTI = 0xFE,  // 初始化状态
    HAC_ERROR_OTHER = 0xFF,   // 未定义错误，加速器空转
} HacStatusInterE;

Status HacOpen(IndexCtxT *idxCtx, DbMemCtxT *memCtx);

void HacClose(IndexCtxT *idxCtx);

// 向加速器注入一块内存
Status HacInjectMemory(HacMgrT *hacMgr);

// 从加速器回收一块内存
void HacRecycleMemory(HacMgrT *hacMgr);

typedef struct MultiHashSecPara {
    IndexKeyT *idxKey;
    HpBatchOutT *addr;
    bool isSameSecEntry;
    bool isInsert;
} MultiHashSecParaT;

// 报文头部强制4字节对齐
#pragma pack(4)
typedef struct HacHashReqHeader {
    uint32_t srcId;  // srcId和ReqId用于标识唯一软硬通信请求
    uint32_t reqId;
    uint32_t batchType : 8;
    uint32_t optNum : 8;
    uint32_t keySize : 8;
    uint32_t reserved : 8;
    uint64_t responseAddr;  // resp body虚拟addr
    uint64_t hashCtxAddr;
} HacHashReqHeaderT;

typedef struct MultiHashReqHeader {
    uint32_t srcId;  // srcId和ReqId用于标识唯一软硬通信请求
    uint32_t reqId;
    uint32_t batchType : 8;
    uint32_t optNum : 8;
    uint32_t keySize : 8;
    uint32_t reserved : 8;
    uint64_t responseAddr;  // resp body虚拟addr
} MultiHashReqHeaderT;
#pragma pack()

Status HacInitTupleAddr(HacCtxT *hacCtx);

Status HacInitIteratorAddr(HacCtxT *hacCtx);

inline static IndexScanItrT HacGetScanItrHd(HacCtxT *hacCtx)
{
    return (IndexScanItrT)(hacCtx->iteratorAddr + (DB_UINTPTR)hacCtx->queryIdx * hacCtx->iteratorSize);
}

inline static HacCtxT *HacCastCtxAsHacCtx(IndexCtxT *indexCtx)
{
    DB_POINTER2(indexCtx, indexCtx->idxRunCtx);
    return (HacCtxT *)(void *)indexCtx->idxRunCtx;
}

SO_EXPORT StatusInter HacMgrCtxInit(SeInstanceT *seInsPtr, DbMemCtxT *memCtx);

Status CltHacMgrCtxInit(uint16_t instanceId);

Status HacHashBatchExecute(HacCtxT *hacCtx, uint32_t batchNum);

// 当前multihash二级表只有insert和delete两个场景，这里整合成一个接口
Status MultiHashBatchExecuteByHac(HacCtxT *hacCtx, DbListT *list);

Status HacHashGetRespMsgForWrite(HacCtxT *hacCtx, IndexKeyT idxKey[], HpBatchOutT addr[]);
Status MultiHashGetRespMsgForWrite(HacCtxT *hacCtx);

inline static Status HacHashGetMsgAndReturn(IndexCtxT *idxCtx)
{
    HacCtxT *hacCtx = HacCastCtxAsHacCtx(idxCtx);
    return HacHashGetRespMsgForWrite(hacCtx, hacCtx->idxKey, hacCtx->addr);
}

inline static Status MultiHashGetMsgAndReturn(IndexCtxT *idxCtx)
{
    HacCtxT *hacCtx = HacCastCtxAsHacCtx(idxCtx);
    return MultiHashGetRespMsgForWrite(hacCtx);
}

inline static void *HashGetShmAddr(ShmemPtrT shmPtr)
{
    return DbShmPtrToAddr(shmPtr);
}

inline static void *HashGetShmAddrAlign(ShmemPtrT shmPtr)
{
    return DbShmPtrToAddrAlign(shmPtr, CACHELINE_SIZE);
}

inline static void *HashAllocShmAddr(DbMemCtxT *memCtx, uint32_t size, ShmemPtrT *shmPtr)
{
    return SeShmAlloc(memCtx, size, shmPtr);
}

inline static void *HashAllocShmAddrAlign(DbMemCtxT *memCtx, uint32_t size, ShmemPtrT *shmPtr)
{
    return SeShmAllocAlign(memCtx, size, shmPtr, CACHELINE_SIZE);
}

inline static void HashShmAddrFree(DbMemCtxT *memCtx, ShmemPtrT shmPtr)
{
    DbShmemCtxFree(memCtx, shmPtr);
}

inline static void *HashAllocDynAddr(DbMemCtxT *memCtx, uint32_t size)
{
    return DbDynMemCtxAlloc(memCtx, size);
}

inline static void HashFreeDynAddr(DbMemCtxT *memCtx, void *ptr)
{
    DbDynMemCtxFree(memCtx, ptr);
}

inline static bool HashIsShmValid(ShmemPtrT shmPtr)
{
    return DbIsShmPtrValid(shmPtr);
}

void AccLatchR(const IndexCtxT *idxCtx, uint32_t latchPoolId, uint32_t latchId);

void AccUnlatchR(const IndexCtxT *idxCtx, uint32_t latchPoolId, uint32_t latchId);

void AccLatchW(uint32_t latchPoolId, uint32_t latchId);

void AccUnlatchW(uint32_t latchPoolId, uint32_t latchId);

bool AccLatchTryW(uint32_t latchPoolId, uint32_t latchId);

Status HacSendMsg(HacCtxT *hacCtx, uint32_t reqLen);

Status HacReceiveMsg(HacCtxT *hacCtx);

#define HAC_MAX_KEY_SEGMENT 4  // 当前硬化最大支持4个segment段

typedef struct IdxDmKeyInfo {
    uint16_t segOffsets;
    uint16_t segLengths;
} IdxDmKeyInfoT;

#pragma pack(4)
typedef struct IdxHacHash {
    ShmemPtrT bucketShmPtr;
    uint32_t entryNum;
    uint32_t expandNum;
    HacBucketT *bucketHead;  // 存储索引数据的虚拟Addr，用于硬件快速定位
    uint32_t hashSize;       // 当前的hashSize,表示有对应数量的HacBucketT
    uint16_t lockOffset;
    uint16_t reserved2;
} IdxHacHashT;

typedef struct HacKeyCmpCtx {  // 卸载后固定的结构
    // 以下为Heap相关数据
    uint16_t rowBegin;
    uint16_t fixHeadSize;
    uint16_t rowCnt;
    uint16_t slotExtendSize;
    uint16_t rawRowSize;
    uint16_t oneRowSize;
    // 以下为Dm相关数据
    uint8_t nullInfoBytes;
    uint8_t keySegNum;
    uint8_t dmOffset;
    uint8_t reserved;
    IdxDmKeyInfoT keyInfo[HAC_MAX_KEY_SEGMENT];
} HacKeyCmpCtxT;
#pragma pack()

typedef union HacHashIndexStatistics {
    struct {
        uint32_t scaleInCnt;  // 扩容次数
        bool isKeyCmpCtxInit;
        bool isKeyCmpByHac;
    } hacHash;
    struct {
        uint32_t bucketUsed;  // 一级索引已使用的桶数量
    } multiHash;
} HacHashIndexStatisticsT;

typedef struct HacHashTable {
    IdxBaseT idxBase;           // 索引固定头部
    IdxHacHashT hashHac;        // 必须在固定头部跟固定的hac结构
    HacKeyCmpCtxT keyCmpCtx;    // 硬化keycmp上下文
    uint64_t hashCollisionCnt;  // 硬件hash索引冲突次数
    uint64_t hashInsertCnt;     // hash索引插入次数
    uint32_t extendListNum;     // hac挂链数
    uint32_t minSize;           // 初始化的hashSize
    uint32_t maxSize;           // 最大的hashSize
    HacHashIndexStatisticsT stats;
} HacHashTableT;

typedef struct SecHashTable {
    IdxHacHashT hashHac;  // 头部固定的hac结构
} SecHashTableT;

inline static HacHashTableT *HacCastIdxAsHashTable(IdxBaseT *idxBase)
{
    DB_POINTER(idxBase);
    return (HacHashTableT *)(void *)idxBase;
}

typedef struct MultiHashDMLPara {  // 批量操作使用
    SecHashTableT *secHash;
    bool isInsert;  // 是否是插入，否则是删除
} MultiHashDMLParaT;

Status HacKeyCmp(HacKeyCmpCtxT *keyCmpCtx, IndexKeyT indexKey, uint64_t addr, HeapTupleBufT hpBuf, bool *match);

// block bitmap begin

#define MAX_POS_PER_UINT64 (63)     // uint64_t下标0-63,用来取和64的余数，位运算性能更优
#define OFFSET_PER_BITMAP_UNIT (6)  // 用来取和64的除数，位运算性能更优

inline static void HacGetBucketBitMapPos(uint32_t buckeId, uint32_t *idx, uint32_t *offset)
{
    DB_ASSERT(buckeId < HAC_BLOCK_BUCKET_NUM);
    *idx = buckeId >> OFFSET_PER_BITMAP_UNIT;                       // 取和64的除数
    *offset = MAX_POS_PER_UINT64 - (buckeId & MAX_POS_PER_UINT64);  // 取和64的余数
}

inline static void HacSetBucketBitMapPos(void *block, uint32_t buckeId)
{
    DB_ASSERT(block != NULL);
    DB_ASSERT(buckeId < HAC_BLOCK_BUCKET_NUM);

    uint32_t idx = 0;
    uint32_t offset = 0;
    HacGetBucketBitMapPos(buckeId, &idx, &offset);
    uint64_t *bitmap = (uint64_t *)block;
    bitmap[idx] |= (uint64_t)((uint64_t)1 << offset);
}

inline static void HacResetBucketBitMapPos(void *block, uint32_t buckeId)
{
    DB_ASSERT(block != NULL);
    DB_ASSERT(buckeId < HAC_BLOCK_BUCKET_NUM);

    uint32_t idx = 0;
    uint32_t offset = 0;
    HacGetBucketBitMapPos(buckeId, &idx, &offset);
    uint64_t *bitmap = (uint64_t *)block;
    bitmap[idx] &= (uint64_t)(~((uint64_t)1 << offset));
}

inline static bool HacIsBucketUsed(void *block, uint32_t buckeId)
{
    DB_ASSERT(block != NULL);
    DB_ASSERT(buckeId < HAC_BLOCK_BUCKET_NUM);

    uint32_t idx = 0;
    uint32_t offset = 0;
    HacGetBucketBitMapPos(buckeId, &idx, &offset);
    uint64_t *bitmap = (uint64_t *)block;
    uint64_t val = bitmap[idx] & (uint64_t)((uint64_t)1 << offset);
    return (val != 0) ? true : false;
}

inline static bool HacIsBlockNotUsed(void *block)
{
    DB_ASSERT(block != NULL);

    // 取和64的除数，代表由多少个uint64_t表示，向上取整
    uint32_t count = ((HAC_BLOCK_BUCKET_NUM - 1) >> OFFSET_PER_BITMAP_UNIT) + 1;
    uint64_t *bitmap = (uint64_t *)block;
    for (uint32_t i = 0; i < count; ++i) {
        if (bitmap[i] != 0) {
            return false;
        }
    }

    return true;
}

// 从加速器申请一块128B内存
HacLogicAddressT HacAllocMemFromAccelerator(void);

// 想加速器回收一块128B内存
void HacFreeMemToAccelerator(HacLogicAddressT addr);

// block bitmap end

// 获取执行硬件加速的授权，在满足一定条件下才能走硬件加速
bool GetHacBatchExectueToken(void);

// 释放执行硬件加速的授权,需要与GetHacBatchExectueToken绑定使用
void ReleaseHacBatchExectueToken(void);

Status HashSecHtCheckExpand(IndexCtxT *idxCtx, IdxHacHashT *hacHash);

#ifdef __cplusplus
}
#endif

#endif
#endif
