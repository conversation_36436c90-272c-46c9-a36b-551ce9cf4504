/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2024. All rights reserved.
 * File Name: se_hac_hash_common.c
 * Description: hac hash common interface
 * Author: lijianchuan
 * Create: 2022/11/15
 */

#ifdef FEATURE_HAC
#include "se_hac_hash_common.h"
// 硬件卸载场景当前必须在armv8的机器下编译，因此本文件一定存在
#include <arm_acle.h>

#define HASH_SCALE_OUT_FILL 70u  // >=70%填充率时可以扩容
#define HASH_SCALE_IN_FILL 25u   // <=25%填充率时可以缩容
#define HASH_FULL_FILL 100u      // 100%
#define CRC_PRIME32_1 0x85EBCA77u
#define CRC_PRIME32_2 0xC2B2AE3Du

typedef struct HacAddrCtx {
    uint32_t slotNumPerBucket;
    HacHashCodeT (*getHacHashCode)(HacBucketT *bucket, uint32_t index);
    HacTupleAddrT (*getHacTupleAddr)(HacBucketT *bucket, uint32_t index);
    TupleAddr (*getTupleAddr)(HacBucketT *bucket, uint32_t index);
    void (*insertEntry)(HacBucketT *bucket, uint32_t index, uint32_t hashCode, TupleAddr addr);
    void (*insertHacTupleAddr)(HacBucketT *bucket, uint32_t index, uint32_t hashCode, HacTupleAddrT addr);
    void (*deleteEntry)(HacBucketT *bucket, uint32_t index);
    void (*truncateTable)(DbMemCtxT *memCtx, HacHashTableT *table, HacBucketT *bucket);
    uint32_t (*getSecHashCode)(TupleAddr addr);
} HacAddrCtxT;

inline static HacHashCodeT GetHacHashCode32bits(HacBucketT *bucket, uint32_t index)
{
    HacHashBucket32bitsT *bucket32 = (void *)bucket;
    return bucket32->hacHashCode[index];
}

inline static HacHashCodeT GetHacHashCode64bits(HacBucketT *bucket, uint32_t index)
{
    HacHashBucket64bitsT *bucket64 = (void *)bucket;
    return bucket64->hacHashCode[index];
}

inline static HacTupleAddrT GetHacTupleAddr32bits(HacBucketT *bucket, uint32_t index)
{
    HacHashBucket32bitsT *bucket32 = (void *)bucket;
    return (HacTupleAddrT){.tupleAddr32bits = bucket32->tupleAddr[index]};
}

inline static HacTupleAddrT GetHacTupleAddr64bits(HacBucketT *bucket, uint32_t index)
{
    HacHashBucket64bitsT *bucket64 = (void *)bucket;
    return (HacTupleAddrT){.tupleAddr64bits = bucket64->tupleAddr[index]};
}

inline static TupleAddr GetTupleAddr32bits(HacBucketT *bucket, uint32_t index)
{
    HacHashBucket32bitsT *bucket32 = (void *)bucket;
    return HeapUncompressTupleAddr32(bucket32->tupleAddr[index]);
}

inline static TupleAddr GetTupleAddr64bits(HacBucketT *bucket, uint32_t index)
{
    HacHashBucket64bitsT *bucket64 = (void *)bucket;
    return bucket64->tupleAddr[index];
}

static inline void HashInsertEntry64bits(HacBucketT *bucket, uint32_t index, uint32_t hashCode, TupleAddr addr)
{
    HacHashBucket64bitsT *bucket64 = (void *)bucket;
    bucket64->hacHashCode[index].hashCode = hashCode;
    bucket64->hacHashCode[index].presence = true;
    bucket64->tupleAddr[index] = addr;
}

static inline void HashInsertEntry32bits(HacBucketT *bucket, uint32_t index, uint32_t hashCode, TupleAddr addr)
{
    HacHashBucket32bitsT *bucket32 = (void *)bucket;
    bucket32->hacHashCode[index].hashCode = hashCode;
    bucket32->hacHashCode[index].presence = true;
    bucket32->tupleAddr[index] = HeapCompressTupleAddr32(addr);
}

static inline void HashInsertHacTupleAddr64bits(
    HacBucketT *bucket, uint32_t index, uint32_t hashCode, HacTupleAddrT addr)
{
    HacHashBucket64bitsT *bucket64 = (void *)bucket;
    bucket64->hacHashCode[index].hashCode = hashCode;
    bucket64->hacHashCode[index].presence = true;
    bucket64->tupleAddr[index] = addr.tupleAddr64bits;
}

static inline void HashInsertHacTupleAddr32bits(
    HacBucketT *bucket, uint32_t index, uint32_t hashCode, HacTupleAddrT addr)
{
    HacHashBucket32bitsT *bucket32 = (void *)bucket;
    bucket32->hacHashCode[index].hashCode = hashCode;
    bucket32->hacHashCode[index].presence = true;
    bucket32->tupleAddr[index] = addr.tupleAddr32bits;
}

inline static void HashDeleteEntry64bits(HacBucketT *bucket, uint32_t index)
{
    HacHashBucket64bitsT *bucket64 = (void *)bucket;
    bucket64->hacHashCode[index].presence = false;
}

inline static void HashDeleteEntry32bits(HacBucketT *bucket, uint32_t index)
{
    HacHashBucket32bitsT *bucket32 = (void *)bucket;
    bucket32->hacHashCode[index].presence = false;
}

static void HacHashExtendBucketTruncate32bits(DbMemCtxT *memCtx, HacHashTableT *table, HacBucketT *bucket)
{
    HacHashBucket32bitsT *hBucket = (void *)bucket;
    for (uint32_t i = 0u; i < HAC_HASH_ENTRY_PER_BUCKET_32_BITS; i++) {
        hBucket->hacHashCode[i].presence = false;
    }

    HacBucketT *nextBucket = HashGetExtendBucket(hBucket->nextBucket);
    if (nextBucket != NULL) {
        HacHashExtendBucketTruncate32bits(memCtx, table, nextBucket);
    }
}

static void HacHashExtendBucketTruncate64bits(DbMemCtxT *memCtx, HacHashTableT *table, HacBucketT *bucket)
{
    HacHashBucket64bitsT *hBucket = (void *)bucket;
    for (uint32_t i = 0u; i < HAC_HASH_ENTRY_PER_BUCKET_64_BITS; i++) {
        hBucket->hacHashCode[i].presence = false;
    }

    HacBucketT *nextBucket = HashGetExtendBucket(hBucket->nextBucket);
    if (nextBucket != NULL) {
        HacHashExtendBucketTruncate64bits(memCtx, table, nextBucket);
    }
}

uint32_t HashGetSecHashCode32bits(TupleAddr addr)
{
    TuplePointer32T addr32 = HeapCompressTupleAddr32(addr);
    return DbHash32((const uint8_t *)(const void *)&addr32, sizeof(TuplePointer32T)) & HASH_CODE_MASK;
}

uint32_t HashGetSecHashCode64bits(TupleAddr addr)
{
    return DbHash32((const uint8_t *)&addr, sizeof(TupleAddr)) & HASH_CODE_MASK;
}

inline static void HacHashResetMetaData(HacHashTableT *ht)
{
    ht->hashHac.bucketShmPtr = HAC_INVALID_SHMPTR;
    ht->hashHac.bucketHead = NULL;
    ht->hashHac.hashSize = 0u;
    ht->hashHac.entryNum = 0u;
    ht->hashInsertCnt = 0u;
    ht->hashCollisionCnt = 0u;
    ht->extendListNum = 0u;
}

static const HacAddrCtxT g_gmdbHacAddrCtx[] = {
    [HAC_DRIVER_TUPLE_ADDR_WIDTH_32BIT] =
        {
            .slotNumPerBucket = HAC_HASH_ENTRY_PER_BUCKET_32_BITS,
            .getHacHashCode = GetHacHashCode32bits,
            .getHacTupleAddr = GetHacTupleAddr32bits,
            .getTupleAddr = GetTupleAddr32bits,
            .insertEntry = HashInsertEntry32bits,
            .insertHacTupleAddr = HashInsertHacTupleAddr32bits,
            .deleteEntry = HashDeleteEntry32bits,
            .truncateTable = HacHashExtendBucketTruncate32bits,
            .getSecHashCode = HashGetSecHashCode32bits,
        },
    [HAC_DRIVER_TUPLE_ADDR_WIDTH_64BIT] =
        {
            .slotNumPerBucket = HAC_HASH_ENTRY_PER_BUCKET_64_BITS,
            .getHacHashCode = GetHacHashCode64bits,
            .getHacTupleAddr = GetHacTupleAddr64bits,
            .getTupleAddr = GetTupleAddr64bits,
            .insertEntry = HashInsertEntry64bits,
            .insertHacTupleAddr = HashInsertHacTupleAddr64bits,
            .deleteEntry = HashDeleteEntry64bits,
            .truncateTable = HacHashExtendBucketTruncate64bits,
            .getSecHashCode = HashGetSecHashCode64bits,
        },
};

inline static const HacAddrCtxT *GetHacAddrCtx(void)
{
    return &g_gmdbHacAddrCtx[GetHacMgr()->tupleAddrMode];
}

inline uint32_t HtGetSecHashCode(TupleAddr addr)
{
    const HacAddrCtxT *addrCtx = GetHacAddrCtx();
    return addrCtx->getSecHashCode(addr);
}

static inline void HashInsertEntryOnBucket(HacBucketT *bucket, uint32_t index, uint32_t hashCode, TupleAddr addr)
{
    const HacAddrCtxT *addrCtx = GetHacAddrCtx();
    addrCtx->insertEntry(bucket, index, hashCode, addr);
}

inline static uint32_t CalcTableCapacity(void)
{
    const HacAddrCtxT *addrCtx = GetHacAddrCtx();
    uint32_t entryPerBucket = addrCtx->slotNumPerBucket;
    uint32_t maxCap = ((GetHacMgr()->hashMaxMemSize * DB_KIBI) / entryPerBucket) * DB_KIBI;
    return maxCap >= HAC_HASH_MAX_SIZE ? HAC_HASH_MAX_SIZE : HashGetRealCap(maxCap);
}

void HashTableTruncate(DbMemCtxT *memCtx, IdxHacHashT *hashHac, HacHashTableT *table)
{
    HacBucketT *srcBucket = (HacBucketT *)HashGetShmAddrAlign(hashHac->bucketShmPtr);
    if (srcBucket == NULL) {  // 空表直接返回
        return;
    }
    const HacAddrCtxT *addrCtx = GetHacAddrCtx();
    for (uint32_t i = 0; i < hashHac->hashSize; i++) {
        addrCtx->truncateTable(memCtx, table, srcBucket + i);
    }
    hashHac->entryNum = 0u;
}

// 未被初始化，则加锁，double check
static bool HashTableWLockIfNotConstructed(HacHashTableT *ht)
{
    IdxBaseT *idxBase = &ht->idxBase;
    if (IdxIsConstructed(idxBase)) {
        return true;
    }
    DbRWLatchW(&idxBase->idxLatch);
    if (IdxIsConstructed(idxBase)) {
        DbRWUnlatchW(&idxBase->idxLatch);
        return true;
    }
    return false;
}

static Status HashCreateInitNewHt(const SeInstanceT *seInstance, const IndexMetaCfgT *idxCfg, HacHashTableT *newHt)
{
    DB_POINTER3(seInstance, idxCfg, newHt);
    newHt->maxSize = CalcTableCapacity();

    newHt->idxBase.indexCfg = *idxCfg;
    newHt->idxBase.shmemCtxId = seInstance->hacShmMemCtxId;
    newHt->idxBase.validCode = HAC_HASH_INDEX_VALID_CODE;
    newHt->idxBase.isConstructed = 0u;
    DbRWLatchInit(&newHt->idxBase.idxLatch);

    HacHashResetMetaData(newHt);
    newHt->minSize = 0u;
    return GMERR_OK;
}

Status HacHashIdxCreateCommon(SeRunCtxT *seRunCtx, IndexMetaCfgT idxCfg, ShmemPtrT *idxShmAddr)
{
    DB_POINTER(idxShmAddr);
    if (idxCfg.isUseClusteredHash) {  // 硬件卸载的索引和clusterHash不兼容
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "|Se-Hac|: hac don't support clusterHash");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    *idxShmAddr = HAC_INVALID_SHMPTR;
    SeInstanceT *seInstance = SeGetInstance(seRunCtx->instanceId);
    if (seInstance == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-Hac|: SE instance is null");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DbMemCtxT *memCtx = DbGetShmemCtxById(seInstance->hacShmMemCtxId, seRunCtx->instanceId);
    if (memCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "|Se-Hac|: Hash's mem context is null");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    ShmemPtrT newHtShmAddr = HAC_INVALID_SHMPTR;
    uint32_t hacTableSize = (uint32_t)sizeof(HacHashTableT);
    HacHashTableT *newHt = (HacHashTableT *)HashAllocShmAddr(memCtx, hacTableSize, &newHtShmAddr);
    if (newHt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "|Se-Hac|: unable to alloc new hashtable");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(newHt, hacTableSize, 0, hacTableSize);

    Status ret = HashCreateInitNewHt(seInstance, &idxCfg, newHt);
    if (ret != GMERR_OK) {
        HashShmAddrFree(memCtx, newHtShmAddr);
        return ret;
    }

    // 方便查看HacHash、MultiHash是否启用
    DB_LOG_INFO("|Se-Hac|:%" PRIu8 " Create finished", (uint8_t)idxCfg.idxType);
    newHt->hashHac.lockOffset = (uint16_t)HtGetHashCodeWithShmPtr(newHtShmAddr);
    *idxShmAddr = newHtShmAddr;
    return GMERR_OK;
}

Status HacHashIdxDropCommon(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr, HacHashTableFuncT dropFunc)
{
    HacHashTableT *ht = (HacHashTableT *)HashGetShmAddr(idxShmAddr);
    if (ht == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "hashtable is null");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DbRWLatchW(&ht->idxBase.idxLatch);

    bool noNeedDrop = false;
    Status ret = HashCheckValid(ht, &noNeedDrop);
    if (noNeedDrop) {
        DbRWUnlatchW(&ht->idxBase.idxLatch);
        return ret;
    }

    DbMemCtxT *memCtx = DbGetShmemCtxById(ht->idxBase.shmemCtxId, seRunCtx->instanceId);
    if (memCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "hash's mem context is null");
        DbRWUnlatchW(&ht->idxBase.idxLatch);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    dropFunc(memCtx, ht);
    HacHashResetMetaData(ht);
    ht->idxBase.validCode = 0u;
    DbRWUnlatchW(&ht->idxBase.idxLatch);
    HashShmAddrFree(memCtx, idxShmAddr);
    return GMERR_OK;
}

Status HacHashIdxTruncateCommon(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr, HacHashTableFuncT truncateFunc)
{
    HacHashTableT *ht = (HacHashTableT *)HashGetShmAddr(idxShmAddr);
    if (ht == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "hashtable is null");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DbRWLatchW(&ht->idxBase.idxLatch);

    bool noNeedTruncate = false;
    Status ret = HashCheckValid(ht, &noNeedTruncate);
    if (noNeedTruncate) {
        DbRWUnlatchW(&ht->idxBase.idxLatch);
        return ret;
    }

    DbMemCtxT *memCtx = DbGetShmemCtxById(ht->idxBase.shmemCtxId, seRunCtx->instanceId);
    if (memCtx == NULL) {
        DbRWUnlatchW(&ht->idxBase.idxLatch);
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "hash's mem context is null");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    truncateFunc(memCtx, ht);
    ht->hashHac.entryNum = 0u;
    ht->hashInsertCnt = 0u;
    ht->hashCollisionCnt = 0u;

    DbRWUnlatchW(&ht->idxBase.idxLatch);
    return GMERR_OK;
}

void HacHashIdxCloseCommon(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    HacHashUnInitRunCtx(idxCtx);
    HacClose(idxCtx);
}

Status HacHashIdxOpenCommon(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    HacHashInitRunCtx(idxCtx);
    Status ret = HacOpen(idxCtx, idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx);
    if (ret != GMERR_OK) {
        HacHashUnInitRunCtx(idxCtx);
        return ret;
    }

    ret = HacConstructHtInOpen(idxCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to open hash index.");
        HacClose(idxCtx);
        HacHashUnInitRunCtx(idxCtx);
        return ret;
    }
    return GMERR_OK;
}

static void HacInitKeyCmpCtx(HeapHacInfoT *hacInfo, DmIndexKeyBufInfoT *indexKeyInfo, HacKeyCmpCtxT *keyCmpCtx)
{
    keyCmpCtx->oneRowSize = hacInfo->oneRowSize;
    keyCmpCtx->rawRowSize = hacInfo->rawRowSize;
    keyCmpCtx->rowBegin = hacInfo->rowBegin;
    keyCmpCtx->fixHeadSize = hacInfo->fixRowHead;
    keyCmpCtx->rowCnt = hacInfo->rowCnt;
    keyCmpCtx->slotExtendSize = hacInfo->slotExtendSize;

    keyCmpCtx->keySegNum = (uint8_t)indexKeyInfo->segmentNum;
    keyCmpCtx->nullInfoBytes = indexKeyInfo->nullInfoBytes;
    for (uint32_t i = 0; i < indexKeyInfo->segmentNum; i++) {
        keyCmpCtx->keyInfo[i] = (IdxDmKeyInfoT){
            .segOffsets = indexKeyInfo->segmentOffsets[i], .segLengths = indexKeyInfo->segmentLengths[i]};
    }
}

// 第一次open的时候vertex可能未初始化,需要在dml操作时二次确认
Status HashInitKeyCmpCtx(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    if (!idxCtx->isKeyCmpByHac) {
        return GMERR_OK;
    }
    if (SECUREC_LIKELY(ht->stats.hacHash.isKeyCmpCtxInit)) {
        idxCtx->isKeyCmpByHac = ht->stats.hacHash.isKeyCmpByHac;
        return GMERR_OK;
    }
    ht->stats.hacHash.isKeyCmpCtxInit = true;
    if (idxCtx->idxOpenCfg.callbackFunc.getHacInfo == NULL) {
        idxCtx->isKeyCmpByHac = false;
        DB_LOG_WARN(GMERR_FEATURE_NOT_SUPPORTED, "|Se-Hac|: getHacInfo is null");
        return GMERR_OK;  // ut场景允许没有getHacInfo函数，这里把idxCtx->isKeyCmpByHac置位false即可，不必报错
    }
    HeapHacInfoT hacInfo = idxCtx->idxOpenCfg.callbackFunc.getHacInfo(idxCtx->idxOpenCfg.heapHandle);
    if (!hacInfo.isFixPage) {  // 不支持非定长表，但是可以正常运行
        idxCtx->isKeyCmpByHac = false;
        DB_LOG_WARN(GMERR_FEATURE_NOT_SUPPORTED, "|Se-Hac|: accelerator only support fixpage");
        return GMERR_OK;
    }
    DmIndexKeyBufInfoT *indexKeyInfo = NULL;
    Status ret = DmVertexGetIndexKeyInfo(idxCtx->idxOpenCfg.vertex, idxCtx->idxMetaCfg.indexId, &indexKeyInfo);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Se-Hac|: unable to get index key info");
        return ret;
    }
    if (indexKeyInfo->segmentNum > HAC_MAX_KEY_SEGMENT) {
        idxCtx->isKeyCmpByHac = false;
        DB_LOG_WARN(GMERR_FEATURE_NOT_SUPPORTED, "|Se-Hac|: accelerator only support at most four segments key");
        return GMERR_OK;
    }
    ht->stats.hacHash.isKeyCmpByHac = true;
    HacKeyCmpCtxT *keyCmpCtx = &ht->keyCmpCtx;
    DmGetVertexOffsetInSimpleVertex(idxCtx->idxOpenCfg.vertex, &keyCmpCtx->dmOffset);
    HacInitKeyCmpCtx(&hacInfo, indexKeyInfo, keyCmpCtx);
    return GMERR_OK;
}

uint32_t HashGetExpectSize(uint32_t hashCap)
{
    const HacAddrCtxT *addrCtx = GetHacAddrCtx();
    uint32_t bucketCap = hashCap / addrCtx->slotNumPerBucket;
    return bucketCap < HAC_HASH_MIN_SIZE ? HAC_HASH_MIN_SIZE : HashGetRealCap(bucketCap);
}

static void HashInitInFirstOpen(IndexCtxT *idxCtx, HacHashTableT *ht, uint32_t hashCap)
{
    uint32_t expectSize =
        idxCtx->idxMetaCfg.idxType == HAC_HASH_INDEX ? HashGetExpectSize(hashCap) : GetHacMgr()->multiHashBucketCnt;
    ht->minSize = expectSize > ht->maxSize ? ht->maxSize : expectSize;
}

uint32_t HashGetExpandSize(uint32_t tableSize)
{
    const HacAddrCtxT *addrCtx = GetHacAddrCtx();
    uint32_t entryPerBucket = addrCtx->slotNumPerBucket;
    return tableSize < HASH_FULL_FILL ? tableSize * entryPerBucket * HASH_SCALE_OUT_FILL / HASH_FULL_FILL :
                                        (tableSize * entryPerBucket / HASH_FULL_FILL) * HASH_SCALE_OUT_FILL;
}

static Status HacHashCreateHashTable(HacHashTableT *ht)
{
    DB_POINTER(ht);
    DB_ASSERT(ht->minSize != 0);
    DbMemCtxT *memCtx = DbGetShmemCtxById(ht->idxBase.shmemCtxId, DbGetProcGlobalId());
    if (memCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "|SE-HH|: hash's mem context is null");
        return GMERR_INTERNAL_ERROR;
    }
    uint32_t tableSize = ht->minSize * HASH_BUCKET_SIZE;
    ShmemPtrT bucketShm = HAC_INVALID_SHMPTR;
    HacBucketT *bucketHead = (HacBucketT *)HashAllocShmAddrAlign(memCtx, tableSize, &bucketShm);
    if (bucketHead == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "|SE-HH|: Alloc HacHash Table unsucc");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(bucketHead, tableSize, 0, tableSize);
    for (uint32_t i = 0; i < ht->minSize; i++) {
        (bucketHead + i)->nextBucket = HAC_INVALID_LOGICADDR;
    }
    ht->hashHac.hashSize = ht->minSize;
    ht->hashHac.bucketHead = bucketHead;
    ht->hashHac.bucketShmPtr = bucketShm;
    ht->hashHac.expandNum = HashGetExpandSize(ht->minSize);
    return GMERR_OK;
}

static Status MultiHashCreateHashTable(HacHashTableT *ht)
{
    DB_POINTER(ht);
    DbMemCtxT *memCtx = DbGetShmemCtxById(ht->idxBase.shmemCtxId, DbGetProcGlobalId());
    if (memCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "|SE-MH|: hash's mem context is null");
        return GMERR_INTERNAL_ERROR;
    }
    ShmemPtrT bucketShm = HAC_INVALID_SHMPTR;
    uint32_t tableSize = ht->minSize * (uint32_t)sizeof(ShmemPtrT);

    ShmemPtrT *bucketHead = (ShmemPtrT *)HashAllocShmAddrAlign(memCtx, tableSize, &bucketShm);
    if (bucketHead == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "|SE-MH|: Alloc MultiHash Table unsucc");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(bucketHead, tableSize, 0, tableSize);
    for (uint32_t i = 0; i < ht->minSize; i++) {
        bucketHead[i] = HAC_INVALID_SHMPTR;
    }
    ht->hashHac.hashSize = ht->minSize;
    ht->hashHac.bucketShmPtr = bucketShm;
    return GMERR_OK;
}

Status HacConstructHtInOpen(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    DB_POINTER(ht);
    IdxBaseT *idxBase = &ht->idxBase;
    if (HashTableWLockIfNotConstructed(ht)) {
        return GMERR_OK;
    }
    if (ht->minSize == 0) {
        HashInitInFirstOpen(idxCtx, ht, idxBase->indexCfg.indexCap);
    }
    Status ret =
        idxCtx->idxMetaCfg.idxType == HAC_HASH_INDEX ? HacHashCreateHashTable(ht) : MultiHashCreateHashTable(ht);
    if (ret != GMERR_OK) {
        DbRWUnlatchW(&idxBase->idxLatch);
        return ret;
    }
    DB_LOG_DBG_INFO("|Se-Hac|: Construct in open, indexId: %" PRIu32 "", idxBase->indexCfg.indexId);
    IdxConstructedFinish(idxBase);
    DbRWUnlatchW(&idxBase->idxLatch);
    return GMERR_OK;
}

// 硬件做keycmp的条件，满足条件（isKeyCmpByHac）且为批量
static inline bool IsHacKeyCmp(IndexCtxT *idxCtx)
{
    return idxCtx->isKeyCmpByHac && idxCtx->batchLocked;
}

Status HashKeyCmp(IndexCtxT *idxCtx, IndexKeyT indexKey, HpTupleAddr addr, bool *match)
{
    Status ret = GMERR_OK;
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    HacCtxT *hacCtx = HacCastCtxAsHacCtx(idxCtx);
    bool isHacKeyCmp = IsHacKeyCmp(idxCtx);
    if (isHacKeyCmp && hacCtx->tuplesAddr != NULL) {  // 批读会提前分配内存
        HeapTupleBufT hpTupleBuf = (HeapTupleBufT){0};
        hpTupleBuf.bufSize = ht->keyCmpCtx.rawRowSize;
        hpTupleBuf.buf = hacCtx->tuplesAddr + (DB_UINTPTR)hacCtx->queryIdx * hacCtx->tupleSize;
        ret = HacKeyCmp(&ht->keyCmpCtx, indexKey, addr, hpTupleBuf, match);
    } else {
        if (isHacKeyCmp) {  // 批写场景需要自行申请内存
            HeapTupleBufT hpTupleBuf = (HeapTupleBufT){0};
            hpTupleBuf.bufSize = ht->keyCmpCtx.rawRowSize;
            DB_ASSERT(hpTupleBuf.bufSize == hacCtx->tupleSize);
            hpTupleBuf.buf = DbDynMemCtxAlloc(idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx, hpTupleBuf.bufSize);
            if (hpTupleBuf.buf == NULL) {
                DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "|Se-Hac|: alloc momory unsucc");
                return GMERR_OUT_OF_MEMORY;
            }
            ret = HacKeyCmp(&ht->keyCmpCtx, indexKey, addr, hpTupleBuf, match);
            DbDynMemCtxFree(idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx, hpTupleBuf.buf);
            if (ret != GMERR_OK && !SeIsFilterErrorCode(ret)) {  // 若no_data也提前返回
                return ret;
            }
            // 若返回ok了，这里不能返回，硬化回调函数没有进行老化、订阅场景的标记删除判断，需要重新调用keycmp判断
        }
        if (idxCtx->idxOpenCfg.callbackFunc.keyCmp == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "|Se-Hac|: Hash comparing unsucc");
            return GMERR_INTERNAL_ERROR;
        }
        int32_t cmpRet = 0;
        ret = idxCtx->idxOpenCfg.callbackFunc.keyCmp(idxCtx, indexKey, addr, &cmpRet, match);
        if (ret != GMERR_OK && !SeIsFilterErrorCode(ret)) {
            return ret;
        }
    }
    return GMERR_OK;
}

void HashExtendBucketDrop(HacHashTableT *table, HacBucketT *bucket)
{
    HacLogicAddressT logicAddr = bucket->nextBucket;
    HacBucketT *nextBucket = HashGetExtendBucket(logicAddr);
    if (nextBucket != NULL) {
        (void)DbAtomicDec(&table->extendListNum);
        HashExtendBucketDrop(table, nextBucket);
        HashFreeExtendBucket(logicAddr);
    }
}

void HashTableDrop(DbMemCtxT *memCtx, IdxHacHashT *hashHac, HacHashTableT *table)
{
    HacBucketT *srcBucket = (HacBucketT *)HashGetShmAddrAlign(hashHac->bucketShmPtr);
    if (srcBucket == NULL) {
        return;
    }
    HacMemMgrT *hacMemMgr = GetHacMemMgr();
    DbSpinLock(&hacMemMgr->hacMemLock);
    for (uint32_t i = 0; i < hashHac->hashSize; i++) {
        HashExtendBucketDrop(table, srcBucket + i);
    }
    DbSpinUnlock(&hacMemMgr->hacMemLock);
    HashShmAddrFree(memCtx, hashHac->bucketShmPtr);
}

void MultiHashSecHashDrop(DbMemCtxT *memCtx, HacHashTableT *table, ShmemPtrT bucket)
{
    if (HashIsShmValid(bucket)) {
        SecHashTableT *secHac = (SecHashTableT *)HashGetShmAddrAlign(bucket);
        if (secHac == NULL) {
            DB_LOG_AND_SET_LASERR(UNEXPECTED_NULL_VALUE_INTER, "Get hash's secHac unsucc");
            return;
        }
        HashTableDrop(memCtx, &secHac->hashHac, table);
    }
}

void MultiHashTableDrop(DbMemCtxT *memCtx, HacHashTableT *table)
{
    IdxHacHashT *hashHac = &table->hashHac;
    if (!HashIsShmValid(hashHac->bucketShmPtr)) {
        return;
    }
    ShmemPtrT *srcBucket = (ShmemPtrT *)HashGetShmAddrAlign(hashHac->bucketShmPtr);
    for (uint32_t i = 0; i < hashHac->hashSize; i++) {
        MultiHashSecHashDrop(memCtx, table, srcBucket[i]);
    }
    HashShmAddrFree(memCtx, hashHac->bucketShmPtr);
}

// 唯一性校验与在bucket上查询空slot槽位，两个功能只需一次遍历，故放在一起
Status HashCheckUniqueAndFindEmptyEntryOnBucket(
    HashDMLParaT *para, HacBucketT *bucket, HacBucketT **selBucket, uint32_t *index)
{
    Status ret = GMERR_OK;
    bool isMatch = false;
    const HacAddrCtxT *addrCtx = GetHacAddrCtx();
    for (uint32_t i = 0u; i < addrCtx->slotNumPerBucket; i++) {
        HacHashCodeT hacHashCode = addrCtx->getHacHashCode(bucket, i);
        if (!hacHashCode.presence) {
            if (*index == INVALID_ENTRY_INDEX) {
                *index = i;
                *selBucket = bucket;
            }
            continue;
        }
        if (para->hashCode != hacHashCode.hashCode) {
            continue;
        }
        TupleAddr addr = addrCtx->getTupleAddr(bucket, i);
        if (para->idxCtx->idxMetaCfg.idxType == MULTI_HASH_INDEX) {  // MultiHash第二层插入
            isMatch = para->addr == addr;
        } else {  // HacHash
            ret = HashKeyCmp(para->idxCtx, para->hashKey, addr, &isMatch);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        if (isMatch) {
            DB_LOG_AND_SET_LASERR(GMERR_UNIQUE_VIOLATION, "|Se-Hac|: Same addr exist in other slot.");
            return GMERR_UNIQUE_VIOLATION;
        }
    }
    return GMERR_OK;
}

void HashFindEmptyEntryOnBucket(HacBucketT *bucket, HacBucketT **selBucket, uint32_t *index)
{
    const HacAddrCtxT *addrCtx = GetHacAddrCtx();
    for (uint32_t i = 0u; i < addrCtx->slotNumPerBucket; i++) {
        HacHashCodeT hacHashCode = addrCtx->getHacHashCode(bucket, i);
        if (!hacHashCode.presence) {
            *index = i;
            *selBucket = bucket;
            break;
        }
    }
}

// 软件多一个bitmap
HacBucketT *HacHashGetBucket(void *block, uint32_t bucketId, bool isHacBucket)
{
    HacBucketT *bucket = isHacBucket ? block : block + ACCELERATOR_BITMAP_SIZE;
    return &bucket[bucketId];
}

void HacHashUseBucket(
    void *block, uint32_t blockId, uint32_t bucketId, HacBucketT **selectBucket, HacLogicAddressT *logicAddr)
{
    DB_POINTER3(block, selectBucket, logicAddr);
    if (blockId >= HAC_MAX_BLOCK_NUM || bucketId >= HAC_BLOCK_BUCKET_NUM) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE,
            "|Se-Hac|: Unsucc to reset bit pos, (blockId: %" PRIu32 ", bucketId: %" PRIu32 ").", blockId, bucketId);
        return;
    }
    HacBucketT *bucket = HacHashGetBucket(block, bucketId, false);
    (void)memset_s(bucket, HASH_BUCKET_SIZE, 0, HASH_BUCKET_SIZE);
    bucket->nextBucket = HAC_INVALID_LOGICADDR;
    *selectBucket = bucket;
    logicAddr->blockId = blockId;
    logicAddr->bucketId = bucketId;
    // 将bitmap中该桶的位置设为1
    HacSetBucketBitMapPos(block, bucketId);
}

bool HacHashUseEmptyBucket(void *block, uint32_t blockId, HacBucketT **selectBucket, HacLogicAddressT *logicAddr)
{
    for (uint32_t bucketId = 0; bucketId < HAC_BLOCK_BUCKET_NUM; ++bucketId) {
        // bitmap中该桶的位置为0，该桶未使用
        if (!HacIsBucketUsed(block, bucketId)) {
            // 分配该桶出去
            HacHashUseBucket(block, blockId, bucketId, selectBucket, logicAddr);
            return true;
        }
    }

    return false;
}

Status HacHashUseEmptyBlock(HacMemMgrT *hacMemMgr, uint32_t firstAllocated, uint32_t firstUnAllocated,
    HacBucketT **selectBucket, HacLogicAddressT *logicAddr)
{
    // 待使用的block第一次用，第一个0号桶就可以用
    if (firstAllocated != DB_INVALID_UINT32) {
        ShmemPtrT blockPtr = hacMemMgr->blockMgr[firstAllocated].block;
        void *block = HashGetShmAddrAlign(blockPtr);
        if (SECUREC_UNLIKELY(block == NULL)) {
            DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED,
                "|Se-Hac|: Unsucc to get block addr by shmPtr, (segId: %" PRIu32 ", offset: %" PRIu32 ").",
                blockPtr.segId, blockPtr.offset);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        // 分配该桶出去
        HacHashUseBucket(block, firstAllocated, 0, selectBucket, logicAddr);
        hacMemMgr->blockMgr[firstAllocated].type = HAC_BLOCK_USED;
    } else if (firstUnAllocated != DB_INVALID_UINT32) {  // 没有已申请的空闲bucket，申请新的block
        ShmemPtrT blockShmPtr;
        void *block = HashAllocShmAddrAlign(GetHacMemCtx(), ACCELERATOR_BLOCK_SIZE, &blockShmPtr);
        if (block == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "|Se-Hac|: Alloc block unsucc");
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memset_s(block, ACCELERATOR_BLOCK_SIZE, 0, ACCELERATOR_BLOCK_SIZE);
        HacBucketT *bucket = (HacBucketT *)(block + ACCELERATOR_BITMAP_SIZE);
        for (uint32_t i = 0u; i < HAC_BLOCK_BUCKET_NUM; i++) {
            bucket[i].nextBucket = HAC_INVALID_LOGICADDR;
        }

        hacMemMgr->blockMgr[firstUnAllocated].type = HAC_BLOCK_ALLOCATED;
        hacMemMgr->blockMgr[firstUnAllocated].block = blockShmPtr;
        // 分配该bucket第一个0号桶出去
        HacHashUseBucket(block, firstUnAllocated, 0, selectBucket, logicAddr);
        hacMemMgr->blockMgr[firstUnAllocated].type = HAC_BLOCK_USED;
    } else {
        // 报错 配置的block个数都已用完
        DB_LOG_AND_SET_LASERR(GMERR_RESTRICT_VIOLATION, "|Se-Hac|: Block exceed max restriction");
        return GMERR_RESTRICT_VIOLATION;
    }

    ++HacGetStats()->allocBlockCnt;
    return GMERR_OK;
}

Status HashNewExtendBucketInBlock(HacMemMgrT *hacMemMgr, HacBucketT **selectBucket, HacLogicAddressT *logicAddr)
{
    bool find = false;
    uint32_t firstUnAllocated = DB_INVALID_UINT32;
    uint32_t firstAllocated = DB_INVALID_UINT32;
    // 按顺序遍历一遍所有block，优先使用HAC_BLOCK_USED状态的block。
    for (uint32_t i = 0u; i < HAC_MAX_BLOCK_NUM; i++) {
        // 记录第一个未分配的，以备需要申请新的block时用
        if (hacMemMgr->blockMgr[i].type == HAC_BLOCK_UN_ALLOCATED) {
            firstUnAllocated = (firstUnAllocated == DB_INVALID_UINT32) ? i : firstUnAllocated;
            // 记录第一个已分配待注入硬件使用的，优先使用在使用中的，减少碎片内存
        } else if (hacMemMgr->blockMgr[i].type == HAC_BLOCK_ALLOCATED) {
            firstAllocated = (firstAllocated == DB_INVALID_UINT32) ? i : firstAllocated;
        } else if (hacMemMgr->blockMgr[i].type == HAC_BLOCK_USED) {
            void *block = HashGetShmAddrAlign(hacMemMgr->blockMgr[i].block);
            if (block == NULL) {
                DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED,
                    "|Se-Hac|: Unsucc to get block addr by shmPtr when new bucket, (segId: %" PRIu32
                    ", offset: %" PRIu32 ").",
                    hacMemMgr->blockMgr[i].block.segId, hacMemMgr->blockMgr[i].block.offset);
                return GMERR_MEMORY_OPERATE_FAILED;
            }
            find = HacHashUseEmptyBucket(block, i, selectBucket, logicAddr);
            if (find) {
                break;
            } else {
                hacMemMgr->blockMgr[i].type = HAC_BLOCK_FULL_USED;
            }
        }
    }
    // 如没有HAC_BLOCK_USED状态的block可使用，则优先使用已分配暂未使用的block，如没有则分配新block使用
    if (!find) {
        Status ret = HacHashUseEmptyBlock(hacMemMgr, firstAllocated, firstUnAllocated, selectBucket, logicAddr);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "|Se-Hac|: Use empty block unsucc");
            return ret;
        }
    }

    return GMERR_OK;
}

Status HacAllocExtendBucket(HacMemMgrT *hacMemMgr, HacBucketT **selectBucket, HacLogicAddressT *logicAddr)
{
    HacLogicAddressT logicAddrAlloc = HacAllocMemFromAccelerator();
    if (!IsHacLogicAddrValidForAcc(logicAddrAlloc)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE,
            "|Se-Hac|: Illegal hac logic address, (blockId: %" PRIu32 ", bucketId: %" PRIu32 ").",
            logicAddrAlloc.blockId, logicAddrAlloc.bucketId);
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    // 拿到的是逻辑addr，需要根据已缓存的内存基址做转换
    ShmemPtrT blockPtr = hacMemMgr->blockMgr[logicAddrAlloc.blockId].block;
    void *block = DbShmPtrToAddrAlign(blockPtr, HAC_BLOCK_SIZE);  // 按128KB对齐解析
    if (SECUREC_UNLIKELY(block == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED,
            "|Se-Hac|: Unsucc to get block addr by shmPtr, (segId: %" PRIu32 ", offset: %" PRIu32 ").", blockPtr.segId,
            blockPtr.offset);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    HacBucketT *bucket = HacHashGetBucket(block, logicAddrAlloc.bucketId, true);
    (void)memset_s(bucket, HASH_BUCKET_SIZE, 0, HASH_BUCKET_SIZE);
    bucket->nextBucket = HAC_INVALID_LOGICADDR;
    *selectBucket = bucket;
    logicAddr->blockId = logicAddrAlloc.blockId;
    logicAddr->bucketId = logicAddrAlloc.bucketId;
    return GMERR_OK;
}

// 挂链,新建一个bucket
Status HashNewExtendBucket(HacHashTableT *ht, HacBucketT **selectBucket, HacLogicAddressT *logicAddr)
{
    HacMemMgrT *hacMemMgr = GetHacMemMgr();
    DbSpinLock(&hacMemMgr->hacMemLock);
    Status ret = HacIsAcceleratorMode() ? HacAllocExtendBucket(hacMemMgr, selectBucket, logicAddr) :
                                          HashNewExtendBucketInBlock(hacMemMgr, selectBucket, logicAddr);
    DbSpinUnlock(&hacMemMgr->hacMemLock);

    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Se-Hac|: Alloc extend bucket unsucc");
        return ret;
    }

    (void)DbAtomicInc(&ht->extendListNum);
    return GMERR_OK;
}

HacBucketT *HashGetExtendBucket(HacLogicAddressT logicAddr)
{
    if (!IsHacLogicAddrValid(logicAddr)) {
        return NULL;
    }

    HacMemMgrT *hacMemMgr = GetHacMemMgr();
    ShmemPtrT blockPtr = hacMemMgr->blockMgr[logicAddr.blockId].block;
    bool isHacBucket = HacIsAcceleratorMode() && IsHacLogicAddrValidForAcc(logicAddr);
    void *block = isHacBucket ? DbShmPtrToAddrAlign(blockPtr, HAC_BLOCK_SIZE) : HashGetShmAddrAlign(blockPtr);
    if (block == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED,
            "|Se-Hac|: Unsucc to get block addr by shmPtr when get bucket, (segId: %" PRIu32 ", offset: %" PRIu32 ").",
            blockPtr.segId, blockPtr.offset);
        return NULL;
    }
    return HacHashGetBucket(block, logicAddr.bucketId, isHacBucket);
}

void HashFreeExtendBucket(HacLogicAddressT logicAddr)
{
    if (!IsHacLogicAddrValid(logicAddr)) {
        return;
    }
    HacMemMgrT *hacMemMgr = GetHacMemMgr();

    if (HacIsAcceleratorMode() && logicAddr.blockId < ACCELERATOR_MAX_BLOCK_NUM) {
        DbSpinLock(&hacMemMgr->hacMemLock);  // 加速器模式涉及调用驱动，对驱动接口加解锁
        HacFreeMemToAccelerator(logicAddr);
        DbSpinUnlock(&hacMemMgr->hacMemLock);
    } else {
        ShmemPtrT blockPtr = hacMemMgr->blockMgr[logicAddr.blockId].block;
        void *block = HashGetShmAddrAlign(blockPtr);
        if (block == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED,
                "|Se-Hac|: Unsucc to get block addr by shmPtr when free bucket, (segId: %" PRIu32 ", offset: %" PRIu32
                ").",
                blockPtr.segId, blockPtr.offset);
            return;
        }
        HacResetBucketBitMapPos(block, logicAddr.bucketId);
        // 删除本bucket后，把block的HAC_BLOCK_FULL_USED状态退化成HAC_BLOCK_USED
        hacMemMgr->blockMgr[logicAddr.blockId].type = HAC_BLOCK_USED;
    }
}

void HashCleanAllExtendBucket(void)
{
    for (uint32_t i = 0u; i < HAC_MAX_BLOCK_NUM; i++) {
        for (uint32_t j = 0u; j < HAC_BLOCK_BUCKET_NUM; j++) {
            HacLogicAddressT logicAddr = {i, j};
            HashFreeExtendBucket(logicAddr);
        }
    }
}

static Status HashTraversalCheckUniqueAndFindEmptyEntry(
    HashDMLParaT *para, HacBucketT *curBucket, HacBucketT **lastBucket, HacBucketT **selectBucket, uint32_t *index)
{
    Status ret = GMERR_OK;
    do {  // 遍历bucket槽和挂的链
        ret = HashCheckUniqueAndFindEmptyEntryOnBucket(para, curBucket, selectBucket, index);
        if (ret != GMERR_OK) {
            return ret;
        }
        *lastBucket = curBucket;
        curBucket = HashGetExtendBucket(curBucket->nextBucket);
    } while (curBucket != NULL);
    return ret;
}

static void HashTraversalFindEmptyEntry(
    HacBucketT *curBucket, HacBucketT **lastBucket, HacBucketT **selectBucket, uint32_t *index)
{
    do {  // 不用校验冲突，找到空的slot槽后停止遍历
        HashFindEmptyEntryOnBucket(curBucket, selectBucket, index);
        if (*index != INVALID_ENTRY_INDEX) {
            break;
        }
        *lastBucket = curBucket;
        curBucket = HashGetExtendBucket(curBucket->nextBucket);
    } while (curBucket != NULL);
}

static Status HashCheckUniqueAndFindEmptyEntry(
    HashDMLParaT *para, HacBucketT *startBucket, HacBucketT **selectBucket, uint32_t *index)
{
    HacBucketT *curBucket = startBucket;
    HacBucketT *lastBucket = startBucket;
    Status ret = GMERR_OK;
    uint32_t firstEmptyEntry = INVALID_ENTRY_INDEX;
    if (para->idxCtx->idxMetaCfg.idxType == MULTI_HASH_INDEX || para->idxCtx->hasLookup) {
        HashTraversalFindEmptyEntry(curBucket, &lastBucket, selectBucket, &firstEmptyEntry);
    } else {
        ret = HashTraversalCheckUniqueAndFindEmptyEntry(para, curBucket, &lastBucket, selectBucket, &firstEmptyEntry);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    if (firstEmptyEntry == INVALID_ENTRY_INDEX) {
        DB_ASSERT(*selectBucket == NULL);
        // 新建一个bucket,挂链在lastBucket后
        HacHashTableT *ht = HacCastIdxAsHashTable(para->idxCtx->idxHandle);
        ret = HashNewExtendBucket(ht, selectBucket, &lastBucket->nextBucket);
        if (ret != GMERR_OK) {
            return ret;
        }
        firstEmptyEntry = 0u;
    }
    *index = firstEmptyEntry;

    return GMERR_OK;
}

static inline void HacHashUpdateCollisionCount(
    const HacBucketT *bucket, const HacBucketT *bucket2Insert, const uint32_t insertIndex, uint64_t *pHashCollisionCnt)
{
    DB_POINTER(pHashCollisionCnt);
    if (bucket != bucket2Insert || insertIndex != 0u) {
        (void)DbAtomicInc64(pHashCollisionCnt);
    }
}

// 插入，找到一个空槽位
Status HashInsertMatchEntry(HashDMLParaT *para)
{
    HacBucketT *bucket = HashGetBucketByHashCode(para->hashHac, para->hashCode);
    HacBucketT *bucket2Insert = NULL;
    uint32_t insertIndex = INVALID_ENTRY_INDEX;
    Status ret = HashCheckUniqueAndFindEmptyEntry(para, bucket, &bucket2Insert, &insertIndex);
    if (ret != GMERR_OK) {
        return ret;
    }
    HashInsertEntryOnBucket(bucket2Insert, insertIndex, para->hashCode, para->addr);
    if (para->idxCtx->idxMetaCfg.idxType == HAC_HASH_INDEX) {
        HacHashTableT *ht = HacCastIdxAsHashTable(para->idxCtx->idxHandle);
        HacHashUpdateCollisionCount(bucket, bucket2Insert, insertIndex, &ht->hashCollisionCnt);
    }
    return GMERR_OK;
}

static Status HashExpandInsertEntry(
    HacHashTableT *table, uint32_t hashCode, HacTupleAddrT addr, HacBucketT **bucket, uint32_t *index)
{
    DB_POINTER2(bucket, index);
    const HacAddrCtxT *addrCtx = GetHacAddrCtx();
    uint32_t tupleIndex = (*index) % addrCtx->slotNumPerBucket;
    HacBucketT *curBucket = *bucket;

    if (*index > 0 && tupleIndex == 0) {  // 需要扩链
        Status ret = HashNewExtendBucket(table, &curBucket, &curBucket->nextBucket);
        if (ret != GMERR_OK) {
            return ret;
        }
        *bucket = curBucket;
    }
    addrCtx->insertHacTupleAddr(curBucket, tupleIndex, hashCode, addr);
    (*index)++;
    return GMERR_OK;
}

Status HashExpandCurBucket(HacHashTableT *table, HacBucketT *srcBucket, HashReHashParaT *para, uint32_t mask)
{
    Status ret = GMERR_OK;
    const HacAddrCtxT *addrCtx = GetHacAddrCtx();
    for (uint32_t i = 0u; i < addrCtx->slotNumPerBucket; i++) {
        HacHashCodeT hacHashCode = addrCtx->getHacHashCode(srcBucket, i);
        if (!hacHashCode.presence) {
            continue;
        }
        // 对应位为1则放在bucket2,对应位为0放在bucket1
        uint32_t hashCode = hacHashCode.hashCode;
        HacTupleAddrT addr = addrCtx->getHacTupleAddr(srcBucket, i);
        if (hashCode & mask) {
            ret = HashExpandInsertEntry(table, hashCode, addr, &para->dstBucket2, &para->bucketIndex2);
        } else {
            ret = HashExpandInsertEntry(table, hashCode, addr, &para->dstBucket1, &para->bucketIndex1);
        }
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status HashExpandBucket(
    HacHashTableT *table, HacBucketT *srcBucket, HacBucketT *dstBucket1, HacBucketT *dstBucket2, uint32_t mask)
{
    HacBucketT *curBucket = srcBucket;
    HashReHashParaT para = {.dstBucket1 = dstBucket1, .dstBucket2 = dstBucket2, .bucketIndex1 = 0u, .bucketIndex2 = 0u};
    Status ret = GMERR_OK;
    do {
        ret = HashExpandCurBucket(table, curBucket, &para, mask);
        if (ret != GMERR_OK) {
            return ret;
        }
        curBucket = HashGetExtendBucket(curBucket->nextBucket);
    } while (curBucket != NULL);
    return GMERR_OK;
}

Status HashTableExpand(IndexCtxT *idxCtx, IdxHacHashT *hacHash)
{
    HacMemMgrT *hacMemMgr = GetHacMemMgr();
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);

    DbMemCtxT *memCtx = DbGetShmemCtxById(ht->idxBase.shmemCtxId, DbGetProcGlobalId());
    if (memCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "|Se-Hac|: hash's mem context is null");
        return GMERR_INTERNAL_ERROR;
    }
    uint32_t hashSize = hacHash->hashSize;
    uint32_t bucketSum = hashSize << 1u;  // 只支持double扩容
    uint32_t tableSize = bucketSum * (uint32_t)HASH_BUCKET_SIZE;
    ShmemPtrT bucketShm = HAC_INVALID_SHMPTR;
    HacBucketT *dstBucket = (HacBucketT *)HashAllocShmAddrAlign(memCtx, tableSize, &bucketShm);
    if (dstBucket == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "|Se-Hac|: Alloc HacHash Table when expand unsucc");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(dstBucket, tableSize, 0, tableSize);
    for (uint32_t i = 0; i < bucketSum; i++) {
        (dstBucket + i)->nextBucket = HAC_INVALID_LOGICADDR;
    }
    HacBucketT *srcBucket = HashGetShmAddrAlign(hacHash->bucketShmPtr);
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < hashSize; i++) {
        ret = HashExpandBucket(ht, srcBucket + i, dstBucket + i, dstBucket + i + hashSize, hashSize);
        if (ret != GMERR_OK) {
            DbSpinLock(&hacMemMgr->hacMemLock);
            for (uint32_t j = 0; j <= i; j++) {
                HashExtendBucketDrop(ht, dstBucket + j);
                HashExtendBucketDrop(ht, dstBucket + j + hashSize);
            }
            DbSpinUnlock(&hacMemMgr->hacMemLock);
            HashShmAddrFree(memCtx, bucketShm);
            return ret;
        }
    }
    HashTableDrop(memCtx, hacHash, ht);
    hacHash->hashSize = bucketSum;
    hacHash->expandNum = HashGetExpandSize(hacHash->hashSize);
    hacHash->bucketShmPtr = bucketShm;
    hacHash->bucketHead = dstBucket;

    return GMERR_OK;
}

static Status HashSearchMatchEntryOnBucket(HashDMLParaT *para, HacBucketT *bucket)
{
    Status ret = GMERR_OK;
    bool isMatch = false;
    const HacAddrCtxT *addrCtx = GetHacAddrCtx();
    for (uint32_t i = 0u; i < addrCtx->slotNumPerBucket; i++) {
        HacHashCodeT hacHashCode = addrCtx->getHacHashCode(bucket, i);
        if (!hacHashCode.presence) {
            continue;
        }
        TupleAddr addr = addrCtx->getTupleAddr(bucket, i);
        if (para->idxCtx->idxMetaCfg.idxType == MULTI_HASH_INDEX) {  // MultiHash第二层查询
            if (para->addr == addr) {                                // 验证addr即可
                return GMERR_OK;
            }
        } else if (para->hashCode == hacHashCode.hashCode) {  // hachash需要验证hashCode
            ret = HashKeyCmp(para->idxCtx, para->hashKey, addr, &isMatch);
            if (ret != GMERR_OK) {
                return ret;
            }
            if (isMatch) {
                para->addr = addr;
                return GMERR_OK;
            }
        }
    }
    return GMERR_NO_DATA;
}

// 删除加遍历，查看是否删除后bucket为空
static Status HashDeleteMatchEntryOnBucket(HashDMLParaT *para, HacBucketT *bucket)
{
    const HacAddrCtxT *addrCtx = GetHacAddrCtx();
    for (uint32_t i = 0u; i < addrCtx->slotNumPerBucket; i++) {
        HacHashCodeT hacHashCode = addrCtx->getHacHashCode(bucket, i);
        if (!hacHashCode.presence) {
            continue;
        }

        if (para->hashCode != hacHashCode.hashCode || para->addr != addrCtx->getTupleAddr(bucket, i)) {
            continue;
        }
        // 由于允许存入相同的addr，这里删除一个匹配的addr后就直接返回
        addrCtx->deleteEntry(bucket, i);
        return GMERR_OK;
    }
    return GMERR_NO_DATA;
}

Status HashSearchMatchEntry(HashDMLParaT *para)
{
    HacBucketT *curBucket = HashGetBucketByHashCode(para->hashHac, para->hashCode);
    Status ret = GMERR_OK;
    do {
        ret = HashSearchMatchEntryOnBucket(para, curBucket);
        if (ret != GMERR_NO_DATA) {
            if (ret != GMERR_OK) {
                DB_LOG_AND_SET_LASERR(ret, "|Se-Hac|: index is unavailable.");
            }
            return ret;
        }
        curBucket = HashGetExtendBucket(curBucket->nextBucket);
    } while (curBucket != NULL);

    return GMERR_NO_DATA;
}

Status HashDeleteMatchEntry(HashDMLParaT *para)
{
    HacBucketT *curBucket = HashGetBucketByHashCode(para->hashHac, para->hashCode);
    Status ret = GMERR_OK;
    do {
        ret = HashDeleteMatchEntryOnBucket(para, curBucket);
        if (ret == GMERR_OK) {
            return ret;
        }
        curBucket = HashGetExtendBucket(curBucket->nextBucket);
    } while (curBucket != NULL);

    return GMERR_NO_DATA;
}

Status HashFetchOnBucket(IndexCtxT *idxCtx, MultiHashIteratorT *iter, TupleAddr *addr, bool *isFound)
{
    bool isMatch = false;
    Status ret = GMERR_OK;
    const HacAddrCtxT *addrCtx = GetHacAddrCtx();
    while (iter->index < addrCtx->slotNumPerBucket) {
        HacHashCodeT hacHashCode = addrCtx->getHacHashCode(iter->curBucket, iter->index);
        if (hacHashCode.presence) {
            TupleAddr tupleAddr = addrCtx->getTupleAddr(iter->curBucket, iter->index);
            ret = HashKeyCmp(idxCtx, iter->idxKey, tupleAddr, &isMatch);
            if (ret != GMERR_OK) {
                return ret;
            }
            if (isMatch) {
                *isFound = true;
                *addr = tupleAddr;
                iter->index++;
                return GMERR_OK;
            }
        }
        iter->index++;
    }
    return GMERR_NO_DATA;
}

Status HashFetchNext(IndexCtxT *idxCtx, MultiHashIteratorT *iter, TupleAddr *addr, bool *isFound)
{
    if (iter->secBucketHead == NULL) {
        iter->isEOF = true;
        return GMERR_OK;
    }
    IdxHacHashT *hashHac = &iter->hashTable->hashHac;
    Status ret = GMERR_OK;
    while (iter->bucketIndex < hashHac->hashSize) {
        do {
            uint32_t lockOffset = HashGetLockOffset(iter->bucketIndex, hashHac);
            HashBucketLock(idxCtx, lockOffset, true);
            ret = HashFetchOnBucket(idxCtx, iter, addr, isFound);
            HashBucketUnlock(idxCtx, lockOffset, true);
            if (ret == GMERR_OK) {
                return ret;
            }
            // 为防止iter->curBucket失效，在删除的时候暂不回收空的bucket
            iter->curBucket = HashGetExtendBucket(iter->curBucket->nextBucket);
            iter->index = 0u;
        } while (iter->curBucket != NULL);
        iter->bucketIndex++;
        iter->curBucket = iter->secBucketHead + iter->bucketIndex;
    }
    iter->isEOF = true;
    return GMERR_OK;
}

Status HashCheckExpand(IndexCtxT *idxCtx, IdxHacHashT *hacHash)
{
    if (SECUREC_LIKELY(hacHash->entryNum < hacHash->expandNum)) {
        return GMERR_OK;
    }
    if (!HashTableTryWLock(idxCtx)) {  // trylock失败，不扩容
        return GMERR_OK;
    }
    Status ret = GMERR_OK;
    while (hacHash->entryNum >= hacHash->expandNum) {  // doublecheck；启动时可能一个批需要多次扩容
        ret = HashTableExpand(idxCtx, hacHash);
        if (ret != GMERR_OK) {
            break;
        }
    }
    HashTableWUnlock(idxCtx);
    return ret;
}

Status HashSecHtCheckExpand(IndexCtxT *idxCtx, IdxHacHashT *hacHash)
{
    if (SECUREC_LIKELY(hacHash->entryNum < hacHash->expandNum)) {
        return GMERR_OK;
    }
    if (!MultiHashExpandTryWLock(idxCtx)) {  // trylock失败，不扩容
        return GMERR_OK;
    }
    Status ret = GMERR_OK;
    while (hacHash->entryNum >= hacHash->expandNum) {  // doublecheck；启动时可能一个批需要多次扩容
        ret = HashTableExpand(idxCtx, hacHash);
        if (ret != GMERR_OK) {
            break;
        }
    }
    MultiHashTableWUnlock(idxCtx);
    return ret;
}

static bool IsHashNeedScaleIn(IndexCtxT *idxCtx, IdxHacHashT *hacHash)
{
    const HacAddrCtxT *addrCtx = GetHacAddrCtx();
    uint32_t entryPerBucket = addrCtx->slotNumPerBucket;
    uint32_t threshold =
        hacHash->hashSize < HASH_FULL_FILL ?
            hacHash->hashSize * entryPerBucket * HASH_SCALE_IN_FILL / HASH_FULL_FILL :   // 防止乘积太小舍入
            (hacHash->hashSize * entryPerBucket / HASH_FULL_FILL) * HASH_SCALE_IN_FILL;  // 防止乘积太大溢出
    if (hacHash->entryNum > threshold) {                                                 // 未达缩容阈值
        return false;
    }
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    return hacHash->hashSize > ht->minSize;  // 缩容不能小于预置内存下限
}

Status HashMergeCurBucket(HacHashTableT *table, HacBucketT *srcBucket, HashMergeParaT *para)
{
    Status ret = GMERR_OK;
    const HacAddrCtxT *addrCtx = GetHacAddrCtx();
    for (uint32_t i = 0u; i < addrCtx->slotNumPerBucket; i++) {
        HacHashCodeT hacHashCode = addrCtx->getHacHashCode(srcBucket, i);
        if (!hacHashCode.presence) {
            continue;
        }
        uint32_t hashCode = hacHashCode.hashCode;
        ret = HashExpandInsertEntry(
            table, hashCode, addrCtx->getHacTupleAddr(srcBucket, i), &para->dstBucket, &para->bucketIndex);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status HashMergeBucket(HacHashTableT *table, HacBucketT *srcBucket[], uint32_t numBucket, HacBucketT *dstBucket)
{
    Status ret = GMERR_OK;
    HashMergeParaT para = {.dstBucket = dstBucket, .bucketIndex = 0u};
    for (uint32_t i = 0; i < numBucket; i++) {
        do {
            ret = HashMergeCurBucket(table, srcBucket[i], &para);
            if (ret != GMERR_OK) {
                return ret;
            }
            srcBucket[i] = HashGetExtendBucket(srcBucket[i]->nextBucket);
        } while (srcBucket[i] != NULL);
    }
    return GMERR_OK;
}

Status HashTableScaleIn(IndexCtxT *idxCtx, IdxHacHashT *hacHash)
{
    HacMemMgrT *hacMemMgr = GetHacMemMgr();
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);

    DbMemCtxT *memCtx = DbGetShmemCtxById(ht->idxBase.shmemCtxId, DbGetProcGlobalId());
    if (memCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "|Se-Hac|: hash's mem context is null");
        return GMERR_INTERNAL_ERROR;
    }
    uint32_t hashSize = hacHash->hashSize;
    uint32_t bucketSum = hashSize >> 1u;  // 只支持half缩容
    uint32_t tableSize = bucketSum * (uint32_t)HASH_BUCKET_SIZE;
    ShmemPtrT bucketShm = HAC_INVALID_SHMPTR;
    HacBucketT *dstBucket = (HacBucketT *)HashAllocShmAddrAlign(memCtx, tableSize, &bucketShm);
    if (dstBucket == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "|Se-Hac|: Alloc HacHash Table when scalein unsucc");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(dstBucket, tableSize, 0, tableSize);
    for (uint32_t i = 0; i < bucketSum; i++) {
        (dstBucket + i)->nextBucket = HAC_INVALID_LOGICADDR;
    }
    HacBucketT *srcBucket = HashGetShmAddrAlign(hacHash->bucketShmPtr);
    Status ret = GMERR_OK;
    uint32_t numBucket = 1 << 1u;  // 当前half缩容仅支持2个bucket
    for (uint32_t i = 0; i < bucketSum; i++) {
        HacBucketT *srcBuckets[] = {srcBucket + i, srcBucket + i + bucketSum};
        ret = HashMergeBucket(ht, srcBuckets, numBucket, dstBucket + i);
        if (ret != GMERR_OK) {
            DbSpinLock(&hacMemMgr->hacMemLock);
            for (uint32_t j = 0; j <= i; j++) {
                HashExtendBucketDrop(ht, dstBucket + j);
            }
            DbSpinUnlock(&hacMemMgr->hacMemLock);
            HashShmAddrFree(memCtx, bucketShm);
            return ret;
        }
    }
    HashTableDrop(memCtx, hacHash, ht);
    hacHash->hashSize = bucketSum;
    hacHash->bucketShmPtr = bucketShm;
    hacHash->bucketHead = dstBucket;
    hacHash->expandNum = HashGetExpandSize(hacHash->hashSize);
    DB_LOG_DBG_INFO("|Se-Hac|: Scain-In finished");

    return GMERR_OK;
}

Status HashCheckScaleIn(IndexCtxT *idxCtx, IdxHacHashT *hacHash, IndexScaleInCfgT *idxScaleCfg)
{
    Status ret = GMERR_OK;
    DB_LOG_DBG_INFO("|Se-Hac|: Scain-In Check");
    while (IsHashNeedScaleIn(idxCtx, hacHash)) {  // 多次缩容情况
        ret = HashTableScaleIn(idxCtx, hacHash);
        if (ret != GMERR_OK || IdxExceedSplitTime(idxScaleCfg)) {
            break;
        }
    }
    if (ret == GMERR_OK) {
        HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
        ht->stats.hacHash.scaleInCnt++;
    }
    return ret;
}

void HacMemMgrScaleIn(void)
{
    HacMemMgrT *hacMemMgr = GetHacMemMgr();
    for (uint32_t i = 0; i < HAC_MAX_BLOCK_NUM; ++i) {
        if (hacMemMgr->blockMgr[i].type == HAC_BLOCK_USED || hacMemMgr->blockMgr[i].type == HAC_BLOCK_ALLOCATED) {
            void *block = HashGetShmAddrAlign(hacMemMgr->blockMgr[i].block);
            if (block == NULL) {
                DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED,
                    "|Se-Hac|: Unsucc to get block addr by shmPtr when scale in, (segId: %" PRIu32 ", offset: %" PRIu32
                    ").",
                    hacMemMgr->blockMgr[i].block.segId, hacMemMgr->blockMgr[i].block.offset);
                return;
            }
            if (HacIsBlockNotUsed(block)) {
                DbSpinLock(&hacMemMgr->hacMemLock);
                HashShmAddrFree(GetHacMemCtx(), hacMemMgr->blockMgr[i].block);
                hacMemMgr->blockMgr[i].type = HAC_BLOCK_UN_ALLOCATED;
                hacMemMgr->blockMgr[i].block = DB_INVALID_SHMPTR;
                DbSpinUnlock(&hacMemMgr->hacMemLock);
                --HacGetStats()->allocBlockCnt;
            }
        }
    }
}

Status HashGetEstimateMemSize(IndexMetaCfgT idxMetaCfg, uint64_t count, uint32_t keyLen, uint64_t *size)
{
    DB_POINTER(size);
    uint32_t minSize = HashGetExpectSize(idxMetaCfg.indexCap);  // 两种索引的估计内存可以近似认为一致
    uint32_t maxSize = CalcTableCapacity();
    *size = (uint64_t)DB_MIN(minSize, maxSize) * HASH_BUCKET_SIZE;
    return GMERR_OK;
}

void HashTableUnlock(const IndexCtxT *idxCtx, bool isReadOperation)
{
    ConcurrencyControlE ccType = GetHacMgr()->ccType;
    if (SECUREC_UNLIKELY(ccType == CONCURRENCY_CONTROL_LABEL_LATCH)) {
        return;
    }
    if (isReadOperation || ccType == CONCURRENCY_CONTROL_NORMAL) {
        DbSessionCtxT *sessionCtx = &((SeRunCtxT *)idxCtx->idxOpenCfg.seRunCtx)->resSessionCtx;
        if (sessionCtx->isDirectRead) {
            DbRWSpinRUnlockWithSession(sessionCtx, &idxCtx->idxHandle->idxLatch);
        } else {
            DbRWUnlatchR(&idxCtx->idxHandle->idxLatch);
        }
    } else {
        DbRWUnlatchW(&idxCtx->idxHandle->idxLatch);
    }
}

// 大表锁不加锁
// 正常事务场景读写均加索引读锁，通过加桶的读写锁保证并发
void HashTableLock(const IndexCtxT *idxCtx, bool isReadOperation)
{
    ConcurrencyControlE ccType = GetHacMgr()->ccType;
    if (SECUREC_UNLIKELY(ccType == CONCURRENCY_CONTROL_LABEL_LATCH)) {
        return;
    }
    if (isReadOperation || ccType == CONCURRENCY_CONTROL_NORMAL) {
        DbSessionCtxT *sessionCtx = &((SeRunCtxT *)idxCtx->idxOpenCfg.seRunCtx)->resSessionCtx;
        if (idxCtx->idxOpenCfg.seRunCtx->resSessionCtx.isDirectRead) {
            ShmemPtrT latchAddr = idxCtx->idxShmAddr;
            GET_MEMBER_SHMPTR(latchAddr, offsetof(IdxBaseT, idxLatch));
            // 复用hash的视图标识符LATCH_ADDR_HASH_UNIQUE_INDEX_SHMEM
            DbRWSpinRLockWithSession(
                sessionCtx, &idxCtx->idxHandle->idxLatch, &latchAddr, LATCH_ADDR_HASH_UNIQUE_INDEX_SHMEM);
        } else {
            DbRWLatchR(&idxCtx->idxHandle->idxLatch);
        }
    } else {
        DbRWLatchW(&idxCtx->idxHandle->idxLatch);
    }
}

void HashTableLockForBatch(IndexCtxT *idxCtx, bool isReadOperation)
{
    switch (GetHacMgr()->ccType) {
        case CONCURRENCY_CONTROL_READ_UNCOMMIT:  // 当前轻量化事务的批操作一定是写事务，因此可以开启batchLocked
            if (isReadOperation) {
                DbRWLatchR(&idxCtx->idxHandle->idxLatch);
            } else {
                DbRWLatchW(&idxCtx->idxHandle->idxLatch);
            }
            idxCtx->batchLocked = true;
            break;
        case CONCURRENCY_CONTROL_NORMAL:
            DbRWLatchR(&idxCtx->idxHandle->idxLatch);
            break;
        case CONCURRENCY_CONTROL_LABEL_LATCH:
            idxCtx->batchLocked = true;
            break;
        case CONCURRENCY_CONTROL_INVALID:
        default:
            break;
    };
}

void HashTableUnlockForBatch(IndexCtxT *idxCtx, bool isReadOperation)
{
    switch (GetHacMgr()->ccType) {
        case CONCURRENCY_CONTROL_READ_UNCOMMIT:
            if (isReadOperation) {
                DbRWUnlatchR(&idxCtx->idxHandle->idxLatch);
            } else {
                DbRWUnlatchW(&idxCtx->idxHandle->idxLatch);
            }
            idxCtx->batchLocked = false;
            break;
        case CONCURRENCY_CONTROL_NORMAL:
            DbRWUnlatchR(&idxCtx->idxHandle->idxLatch);
            break;
        case CONCURRENCY_CONTROL_LABEL_LATCH:
            idxCtx->batchLocked = false;
            break;
        case CONCURRENCY_CONTROL_INVALID:
        default:
            break;
    };
}

// like xxhash avalanche
inline static uint32_t HacCrcAvalanche(uint32_t hash)
{
    uint32_t newHash = hash ^ (hash >> DB_15BIT);
    newHash *= CRC_PRIME32_1;
    newHash ^= (newHash >> DB_13BIT);
    newHash *= CRC_PRIME32_2;
    return newHash ^ (newHash >> DB_16BIT);
}

// 循环左移
inline static uint32_t HacRotateLeftShift(uint32_t input, uint32_t leftShift)
{
    return (input << leftShift) | (input >> (DB_32BIT - leftShift));
}

// seed种子仍使用db现有的DB_XXHASH_SEED
// 以特殊的31B key为例说明
// ---------------------------------------------------------------------
// |                |                |                |        |    |  |
// |       8B       |       8B       |       8B       |   4B   | 2B |1B|
// |                |                |                |        |    |  |
// ---------------------------------------------------------------------
//         |                |                |           |       |    |
//      crc32d_1          crc32d_1       crc32d_2    crc32w  crc32h crc32b
// 对每个字节位都进行了相关计算，保证每个字节的公平性
uint32_t HacCrcHash32(const uint8_t *key, uint32_t len, uint32_t seed)
{
#if defined(__aarch64__) || defined(__arm64__)
    const uint8_t *keyCursor = key;
    uint32_t leftLen = len;
    uint32_t hashCode = len + CRC_PRIME32_2 + seed;
    while (leftLen >= DB_16BIT) {
        hashCode = __crc32d(hashCode ^ (hashCode >> DB_13BIT), *((const uint64_t *)(const void *)keyCursor));
        keyCursor += DB_8BIT;
        leftLen -= DB_8BIT;
    }
    if (leftLen >= DB_8BIT) {
        hashCode ^= __crc32d(hashCode, *((const uint64_t *)(const void *)keyCursor));
        keyCursor += DB_8BIT;
        leftLen -= DB_8BIT;
    }
    if ((leftLen & DB_4BIT) != 0) {
        hashCode += __crc32w(HacRotateLeftShift(hashCode, DB_3BIT), *((const uint32_t *)(const void *)keyCursor));
        keyCursor += DB_4BIT;
    }
    if ((leftLen & DB_2BIT) != 0) {
        hashCode += __crc32h(HacRotateLeftShift(hashCode, DB_27BIT), *((const uint16_t *)(const void *)keyCursor));
        keyCursor += DB_2BIT;
    }
    if ((leftLen & DB_1BIT) != 0) {
        hashCode += __crc32b(HacRotateLeftShift(hashCode, DB_19BIT), *keyCursor);
    }
    return HacCrcAvalanche(hashCode + seed);
#else
    // 硬件卸载不支持在非arm架构下编译
#error "unsupported cpu arch"
#endif
}

#endif
