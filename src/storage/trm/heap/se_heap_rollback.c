/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Heap 模块存储引擎回滚相关函数
 * Author:
 * Create: 2023/03/31
 */
#include "se_heap_trx_acc_inner.h"

#include "se_heap_utils.h"
#include "se_undo.h"
#include "se_trx_mgr.h"
#include "se_heap_stats.h"
#include "se_heap_fetch.h"
#include "se_heap_slice_row.h"
#include "se_heap_page.h"
#include "se_log.h"
#include "se_heap_redo_am_inner.h"
#include "se_redo.h"

#include "se_heap_rollback_common.h"
#include "se_heap_am_inner.h"

void HeapUnlinkRollPtrUpdRollPtr(HeapRunCtxT *ctx, PageHeadT *pageHead, HpRowHeadPtrT *rowHeadPtr,
    const UndoRowOpInfoT *rowOpInfo, uint64_t *prevRollPtr)
{
    if (ctx->hpControl.isFixPage) {
        if (HeapPageIsNormalRow(rowHeadPtr->rowState)) {
            *prevRollPtr = rowHeadPtr->normalFixRowHead->trxInfo.rollPtr;
            rowHeadPtr->normalFixRowHead->trxInfo.rollPtr = rowOpInfo->rowRollPtr;
        } else if (HeapPageIsLinkSrcRow(rowHeadPtr->rowState)) {
            *prevRollPtr = rowHeadPtr->linkSrcFixRowHead->trxInfo.rollPtr;
            rowHeadPtr->linkSrcFixRowHead->trxInfo.rollPtr = rowOpInfo->rowRollPtr;
        } else {
            // novalid heap fix page row state
            SE_LAST_ERROR(DATA_EXCEPTION_INTER, "TrxId %" PRIu64 ", rollPtr %" PRIu64 "", rowOpInfo->rowTrxId,
                rowOpInfo->rowRollPtr);
            DB_ASSERT(false);
        }
    } else if (HeapPageIsNormalRow(rowHeadPtr->rowState)) {
        *prevRollPtr = rowHeadPtr->normalRowHead->trxInfo.rollPtr;
        rowHeadPtr->normalRowHead->trxInfo.rollPtr = rowOpInfo->rowRollPtr;
    } else if (HeapPageIsLinkSrcRow(rowHeadPtr->rowState)) {
        *prevRollPtr = rowHeadPtr->linkRowHead->srcTrxInfo.rollPtr;
        rowHeadPtr->linkRowHead->srcTrxInfo.rollPtr = rowOpInfo->rowRollPtr;
    } else {
        // inv heap row state
        SE_LAST_ERROR(
            DATA_EXCEPTION_INTER, "trxId %" PRIu64 ", rollPtr %" PRIu64 "", rowOpInfo->rowTrxId, rowOpInfo->rowRollPtr);
        DB_ASSERT(false);
    }
    HeapRedoForRowHead(ctx, pageHead, REDO_OP_UNLINK_ROLLPTR, (uint8_t *)rowHeadPtr->rowState - (uint8_t *)pageHead,
        HEAP_PAGE_ROW_MAX_MGR_LEN);
}

static StatusInter UnlinkTargetRollPtr(HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo, uint64_t targetRollPtr,
    const UndoRowOpInfoT *rowOpInfo, bool isOldRowCommitted)
{
    HpRowHeadPtrT rowHeadPtr;
    PageHeadT *baseHead = NULL;
    uint64_t prevRollPtr;
    if (targetRollPtr == fetchRowInfo->rowTrxInfo.rollPtr) {
        // 要移除的 targetRollPtr 就是当前行上的rollPtr, 则修改指向下一行
        // 同时还原行占用空间为真实大小，包含乐观和悲观的场景
        rowHeadPtr = fetchRowInfo->srcRowInfo.rowHeadPtr;
        baseHead = fetchRowInfo->srcRowInfo.pageHeadPtr.pageHead;
        if (!ctx->hpControl.isFixPage && isOldRowCommitted) {
            HVPageHeadT *pageHead = fetchRowInfo->srcRowInfo.pageHeadPtr.varPageHead;
            HeapVarRowClearReserveSize(ctx, rowHeadPtr, pageHead, pageHead->baseHead.pageHead.freeSize, false);
        }
        HeapUnlinkRollPtrUpdRollPtr(ctx, baseHead, &rowHeadPtr, rowOpInfo, &prevRollPtr);
    } else {
        // 遍历undo版本链, 移除指定版本
        // 需要维护rollPtr字段与buf（行头）里的rollPtr
        UndoTupleCombineAddrT undoTuple = {0};
        StatusInter ret = RowUndoGetUnlinkTargetRecord(
            ctx->seRunCtx->undoCtx, fetchRowInfo->rowTrxInfo.rollPtr, rowOpInfo, targetRollPtr, &undoTuple);
        if (ret != STATUS_OK_INTER) {
            DB_ASSERT(false);
            return ret;
        }
        rowHeadPtr.rowState = (HpRowStateT *)undoTuple.tuple.data;
        baseHead = undoTuple.pageHead;
        HeapUnlinkRollPtrUpdRollPtr(ctx, baseHead, &rowHeadPtr, rowOpInfo, &prevRollPtr);
        RowUndoReleaseTargetRecord(ctx->seRunCtx->undoCtx, &undoTuple);
    }
    DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP,
        "Remove RollPtr %" PRIu64 " from version chain, prevRollPtr: %" PRIu64 ", nextRollPtr: %" PRIu64 "",
        targetRollPtr, prevRollPtr, rowOpInfo->rowRollPtr);
    return STATUS_OK_INTER;
}

StatusInter HeapUnlinkRollPtrImpl(
    HeapRunCtxT *ctx, HpTupleAddr heapTupleAddr, uint64_t targetRollPtr, const UndoRowOpInfoT *rowOpInfo)
{
    DB_POINTER2(ctx, rowOpInfo);
    TrxT *trx = (TrxT *)ctx->seRunCtx->trx;
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, NULL);
    fetchRowInfo.isCanAccessMarkDelRow = true;
    fetchRowInfo.isOnlyReadSrcRow = true;
    fetchRowInfo.srcRowInfo.itemPtr = *(HpItemPointerT *)&heapTupleAddr;
    fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;
    DB_ASSERT(ctx->hpOperation == HEAP_OPTYPE_UNDO_PURGER);
    HeapSetPageLatchActionForWrite(ctx);
    HeapSetLockActionBeforeGcUnlinkRollPtr(ctx);
    StatusInter ret = HeapFetchRow(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
        return ret;
    }
    bool isOldRowCommitted = false;
    ret = TrxMgrCheckTrxIsCommitted(trx->trxMgr, rowOpInfo->rowTrxId, &isOldRowCommitted);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
        return ret;
    }
    bool isRowRollPtrValid = UndoCheckRollPtrMagicNum(rowOpInfo->rowRollPtr);
    if (!isRowRollPtrValid) {
        SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
        SE_LAST_ERROR(OBJECT_NOT_IN_PREREQUISITE_STATE_INTER,
            "label %" PRIu32 " try unlink mid version data in version chain, targetRollPtr %" PRIu64
            ", masterRollPtr %" PRIu64 ", trxId %" PRIu64 ", rowRollPtr %" PRIu64 ", trxId %" PRIu64 "",
            ctx->heapCfg.labelId, targetRollPtr, fetchRowInfo.rowTrxInfo.rollPtr, fetchRowInfo.rowTrxInfo.trxId,
            rowOpInfo->rowRollPtr, rowOpInfo->rowTrxId);
        return INTERNAL_ERROR_INTER;
    }
    ret = UnlinkTargetRollPtr(ctx, &fetchRowInfo, targetRollPtr, rowOpInfo, isOldRowCommitted);
    // baseHead 可能是heap数据页也可能是undo页
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Unlink rollPtr trxId:%" PRIu64 " rollPtr:%" PRIu64, rowOpInfo->rowTrxId, rowOpInfo->rowRollPtr);
        return ret;
    }
    return STATUS_OK_INTER;
}

void HeapFixPageRollback2LinkSrcRow(HeapRunCtxT *ctx, HpRowHeadPtrT oldRowInLog, HpPageFetchRowInfo *fetchCurRowInfo)
{
    DB_POINTER2(ctx, fetchCurRowInfo);
    DB_ASSERT(fetchCurRowInfo->srcRowInfo.rowType == HEAP_FIX_ROW_NORMAL_ROW ||
              fetchCurRowInfo->srcRowInfo.rowType == HEAP_FIX_ROW_LINK_SRC_ROW);
    bool isNeedDelCurrentLinkDst = false;
    HpItemPointerT currentDstPrt = {0};
    bool rollBackNormalRow = false;
    if (fetchCurRowInfo->srcRowInfo.rowType == HEAP_FIX_ROW_LINK_SRC_ROW) {  // 前后都是 跳转行
        // 如果记录了undo日志, link行更新就失败了(原来的行内容没有变动). 回滚时, 不能删除这个 linkDst 行.
        currentDstPrt = fetchCurRowInfo->srcRowInfo.rowHeadPtr.linkSrcFixRowHead->linkItemPtr;
        // addr一样, 不应该删除. 否则应该删除
        isNeedDelCurrentLinkDst = !HeapIsSameAddr(currentDstPrt, oldRowInLog.linkSrcFixRowHead->linkItemPtr);
    } else {
        rollBackNormalRow = true;
    }
    /* 要回滚到的版本, 是一个link行, 它空间是最小的. 直接覆盖就可以了. */
    errno_t err = memcpy_s(fetchCurRowInfo->srcRowInfo.rowHeadPtr.linkSrcFixRowHead, sizeof(HpLinkSrcFixRowHead),
        oldRowInLog.linkSrcFixRowHead, sizeof(HpLinkSrcFixRowHead));
    DB_ASSERT(err == EOK);
    HeapRedoForRollback(
        ctx, fetchCurRowInfo, rollBackNormalRow, (uint8_t *)oldRowInLog.linkRowHead, sizeof(HpLinkRowHeadT));

    if (isNeedDelCurrentLinkDst) {
        HeapCleanLinkDstRow(ctx, currentDstPrt);
    }
}

void HeapVarPageRollback2LinkSrcRow(
    HeapRunCtxT *ctx, HpRowHeadPtrT oldRowInLog, HpPageFetchRowInfo *fetchCurRowInfo, bool isTrxCommitted)
{
    DB_POINTER2(ctx, fetchCurRowInfo);
    DB_ASSERT(fetchCurRowInfo->srcRowInfo.rowType == HEAP_VAR_ROW_NORMAL_ROW ||
              fetchCurRowInfo->srcRowInfo.rowType == HEAP_VAR_ROW_LINK_SRC_ROW);
    bool isNeedDelCurrentLinkDst = false;
    bool isMasterVersionMarkDeleted = fetchCurRowInfo->srcRowInfo.rowHeadPtr.rowState->isDeleted;
    HpItemPointerT currentDstPrt = {0};
    PageSizeT rollbackReserveSize = 0;
    if (fetchCurRowInfo->srcRowInfo.rowType == HEAP_VAR_ROW_LINK_SRC_ROW) {  // 前后都是 跳转行
        // 如果记录了undo日志, link行更新就失败了(原来的行内容没有变动). 回滚时, 不能删除这个 linkDst 行.
        currentDstPrt = fetchCurRowInfo->srcRowInfo.rowHeadPtr.linkRowHead->linkItemPtr;
        rollbackReserveSize = fetchCurRowInfo->srcRowInfo.rowHeadPtr.linkRowHead->rollbackReserveSize;
        // addr一样, 不应该删除. 否则应该删除
        isNeedDelCurrentLinkDst = !HeapIsSameAddr(currentDstPrt, oldRowInLog.linkRowHead->linkItemPtr);
    } else {
        // 删除(覆盖)当前行即可，是否需要回收该内存取决于rollBack的src行是否commit,
        // 是的话则回收内存在下HeapVarRowClearReserveSize
        rollbackReserveSize = fetchCurRowInfo->srcRowInfo.rowHeadPtr.normalRowHead->rollbackReserveSize;
    }

    /* 要回滚到的版本, 是一个link行, 它空间是最小的. 直接覆盖就可以了. */
    errno_t err = memcpy_s(fetchCurRowInfo->srcRowInfo.rowHeadPtr.linkRowHead, sizeof(HpLinkRowHeadT),
        oldRowInLog.linkRowHead, sizeof(HpLinkRowHeadT));
    DB_ASSERT(err == EOK);
    HVPageHeadT *pageHead = fetchCurRowInfo->srcRowInfo.pageHeadPtr.varPageHead;
    // 回滚跳转行，还原行占用空间为0，同时涵盖乐观和悲观的情况
    if (isTrxCommitted) {
        // rollBack src 是commit, 则回收内存并更新FSM
        HeapVarRowClearReserveSize(
            ctx, fetchCurRowInfo->srcRowInfo.rowHeadPtr, pageHead, pageHead->baseHead.pageHead.freeSize, false);
    } else {
        fetchCurRowInfo->srcRowInfo.rowHeadPtr.linkRowHead->rollbackReserveSize = rollbackReserveSize;  // 重新刷新回去
    }
    HeapRedoForRowHead(ctx, &pageHead->baseHead.pageHead, REDO_OP_ROLLBACK,
        (uint8_t *)fetchCurRowInfo->srcRowInfo.rowHeadPtr.rowState - (uint8_t *)pageHead, HEAP_PAGE_ROW_MAX_MGR_LEN);
    if (isNeedDelCurrentLinkDst && !isMasterVersionMarkDeleted) {
        // masterVersion是删除的状态时，它的内存可能是已回滚事务的内存，此时不应该去访问dstRow
        HeapCleanLinkDstRow(ctx, currentDstPrt);
    }
}

StatusInter HeapRollback2LinkSrcRowImpl(
    HeapRunCtxT *ctx, HpRowHeadPtrT oldRowInLog, HpPageFetchRowInfo *fetchCurRowInfo, bool isTrxCommitted)
{
    DB_POINTER2(ctx, fetchCurRowInfo);
    if (ctx->hpControl.isFixPage) {
        HeapFixPageRollback2LinkSrcRow(ctx, oldRowInLog, fetchCurRowInfo);
    } else {
        HeapVarPageRollback2LinkSrcRow(ctx, oldRowInLog, fetchCurRowInfo, isTrxCommitted);
    }
    return STATUS_OK_INTER;  // 与内存态接口统一
}

StatusInter HeapFetchRowForRollback(
    HeapRunCtxT *ctx, HpItemPointerT itemPtr, uint64_t targetRollPtr, HpPageRowOpInfoT *opInfo)
{
    (void)HeapLabelResetOpType(ctx, HEAP_OPTYPE_UPDATE, false);
    HeapSetIsNeedRowLock(ctx, false);  // 回滚流程不加事务锁
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    // 这里需要读取主版本，检查targetRollptr是否在主版本上，因此要isCanAccessMarkDelRow为true
    fetchRowInfo->isCanAccessMarkDelRow = true;
    fetchRowInfo->srcRowInfo.itemPtr = itemPtr;
    fetchRowInfo->curRowInfo = &fetchRowInfo->srcRowInfo;
    HeapSetPageLatchActionForWrite(ctx);
    HeapSetOperationForUpdate(ctx);
    if (SeTransGetTrxType(ctx->seRunCtx) == OPTIMISTIC_TRX) {
        fetchRowInfo->targetRollPtr = targetRollPtr;
        HeapSetLockActionBeforeRollBackUpdate(ctx);
        ctx->hpControl.trxContrl.isNeedReadView = true;
    } else {
        // 悲观RR时，主版本必定是本事务产生的，直接读取行内容, 要访问dstRow
        fetchRowInfo->canAccessLinkDst = true;
        fetchRowInfo->isOnlyReadSrcRow = false;
    }
    return HeapFetchRow(ctx, opInfo);
}

static StatusInter HeapRollBackInVersion(
    HeapRunCtxT *ctx, uint64_t targetRollPtr, UndoRowOpInfoT *rowOpInfo, HpPageRowOpInfoT *opInfo)
{
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    StatusInter ret;
    if (targetRollPtr == fetchRowInfo->rowTrxInfo.rollPtr) {
        // 要移除的 targetRollPtr 就是当前行上的rollPtr, 则修改指向下一行
        ret = HeapRollbackInMasterVersion(ctx, rowOpInfo, opInfo);
        if (ret != STATUS_OK_INTER) {
            // heap update rollback in masterVersion unsucc
            SE_ERROR(ret, "rollback in masterVersion. labelId:%" PRIu32 " trxId:%" PRIu64, rowOpInfo->labelId,
                ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
        }
    } else {
        ret = HeapUpdateRollbackInVersionChain(ctx, fetchRowInfo, rowOpInfo, targetRollPtr);
    }
    return ret;
}

StatusInter HeapUpdateRollbackWithOptimisticOrReReadTrx(
    HeapRunCtxT *ctx, HpItemPointerT itemPtr, uint64_t targetRollPtr)
{
    DB_POINTER(ctx);
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    HpPageAllocRowInfoT pageAllocRowInfo = EmptyHpPageAllocRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, &pageAllocRowInfo);
    // 读取当前事务的版本，if rollback or not.
    StatusInter ret = HeapFetchRowForRollback(ctx, itemPtr, targetRollPtr, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        goto EXIT;
    }
    UndoRowOpInfoT rowOpInfo = {0};
    ret = HeapRepReadReReadRollBackInfo(ctx, targetRollPtr, &rowOpInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        goto EXIT;
    }
    HpRowHeadPtrT oldRowInLog = {.rowState = (HpRowStateT *)rowOpInfo.rowBuf};
    bool needCleanLob = fetchRowInfo.curRowInfo->rowType == HEAP_VAR_ROW_LINK_DST_SLICE_DIR_ROW &&
                        !HeapIsSameAddr(fetchRowInfo.curRowInfo->itemPtr, oldRowInLog.linkRowHead->linkItemPtr);
    // 更新后是大对象，且不是undo日志刚写就失败
    if (needCleanLob) {
        HpSliceRowDirT *dirRow = (HpSliceRowDirT *)(fetchRowInfo.curRowInfo->rowHeadPtr.sliceDirRowHead + 1);
        HpItemPointerT lastItemPtr = dirRow->prevItemPtr;
        SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
        if ((ret = HeapUpdateCleanForLob(ctx, fetchRowInfo.curRowInfo->itemPtr, lastItemPtr)) != STATUS_OK_INTER) {
            return ret;
        }
    }
    // 复用fetchRowInfo，重置curRowInfo为srcRowInfo; 重新fetch一下
    if (SeTransGetTrxType(ctx->seRunCtx) == OPTIMISTIC_TRX) {
        (void)HeapLabelResetOpType(ctx, HEAP_OPTYPE_UNDO_PURGER, false);
    }
    HeapSetLockActionBeforeRollBackUpdate(ctx);
    opInfo.fetchRowInfo->curRowInfo = &fetchRowInfo.srcRowInfo;
    // 读取主版本，用于回滚版本链
    ret = HeapFetchRow(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        goto EXIT;
    }
    ret = HeapRepReadReReadRollBackInfo(ctx, targetRollPtr, &rowOpInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        goto EXIT;
    }
    if (SeTransGetTrxType(ctx->seRunCtx) == PESSIMISTIC_TRX) {
        ret = HeapRollbackPrepareWithPessimisticReReadTrx(ctx, &fetchRowInfo, &rowOpInfo, targetRollPtr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            goto EXIT;
        }
    }
    ret = HeapRollBackInVersion(ctx, targetRollPtr, &rowOpInfo, &opInfo);
EXIT:
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    return ret;
}

StatusInter HeapUpdateRollbackWithPessimisticTrx(
    HeapRunCtxT *ctx, HpItemPointerT itemPtr, const UndoRowOpInfoT *rowOpInfo)
{
    DB_POINTER3(ctx, rowOpInfo, rowOpInfo->rowBuf);
    RowTrxInfoT txInfo = RowTrxInfoCreate(rowOpInfo->rowTrxId, rowOpInfo->rowRollPtr);
    HeapSetTrxInfoForRollBack(ctx, txInfo);
    DB_ASSERT(rowOpInfo->isDeleted == false);  // 悲观事务下，因为有事务锁，旧版本的IsDeleted一定为false
    ctx->rollBackInfo.oldIsDeleted = rowOpInfo->isDeleted;

    TrxT *trx = (TrxT *)ctx->seRunCtx->trx;
    HpRowStateT *oldRowStateInLog = (HpRowStateT *)rowOpInfo->rowBuf;
    StatusInter ret = STATUS_OK_INTER;
    HpRowHeadPtrT oldRowInLog = {.rowState = NULL};
    oldRowInLog.rowState = oldRowStateInLog;
    bool oldRowIsNormalRow = HeapPageIsNormalRow(oldRowStateInLog);
    if (oldRowIsNormalRow) {
        HeapTupleBufT tupleBuf = {0};
        HeapUpdateRollBackGetOldBuf(ctx, &oldRowInLog, rowOpInfo->rowSize, &tupleBuf);
        ret = HeapUpdateWithinRedo(ctx, &tupleBuf, &itemPtr, NULL);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            // 极限情况下, 如果存储空间满了, 并且此时有一个事务需要回滚, 但是它的历史记录比当前的大,
            // update rollback to normal row unsucc
            SE_ERROR(ret, "labelId:%" PRIu32 " trxId:%" PRIu64, rowOpInfo->labelId,
                ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
        }
        goto EXIT;
    }
    DB_ASSERT(rowOpInfo->rowSize == sizeof(HpLinkRowHeadT) || rowOpInfo->rowSize == sizeof(HpLinkSrcFixRowHead));
    bool isOldRowCommitted = false;
    ret = TrxMgrCheckTrxIsCommitted(trx->trxMgr, rowOpInfo->rowTrxId, &isOldRowCommitted);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        goto EXIT;
    }
    ret = HeapUpdateRollback2LinkSrcRow(ctx, oldRowInLog, itemPtr, isOldRowCommitted);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        // 极限情况下, 如果存储空间满了, 并且此时有一个事务需要回滚, 但是它的历史记录比当前的大, 出现回滚失败.
        // update rollback to link src row unsucc.
        SE_ERROR(
            ret, "labelId: %" PRIu32 " trxId: %" PRIu64, rowOpInfo->labelId, ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
    }
EXIT:
    return ret;
}

/*
为什么要传入targetRollPtr ?
乐观下可能有多个事务在操作同一行，因此当前回滚的事务是不一定在masterVersion上
eg:
                     trx3写的undo    trx2写的undo    trx1写的undo
-----------------    ------------    -----------    -----------
| masterVersion |--->| undoLog2 |--->| undoLog1|--->| undoLog0|
-----------------    ------------    -----------    -----------
    trx3的修改         trx2的修改      trx1的修改    trx0插入的版本

假设当前场景是要回滚trx2的修改, 当前处理的是trx2写的undo, 此时targetRollPtr是trx2写的undo的addr（undoLog1的addr）：

需要读出masterVersion，将上面的rollPtr（undoLog2的addr）与targetRollPtr进行比较，在此场景下发现不相同，
开始遍历版本链，找到undoLog2里面记录的rollPtr（undoLog1的addr）与targetRollPtr比较，在此场景下就匹配成功了
接下来将undoLog1里面的内容写入到undoLog2中（undoLog2里的内容是本次要回滚的，需要丢掉），即完成了本次回滚。
当前reserveSize可以保证在由大更新为小时使用较大的空间作为预留，不会有undoLog2无法容纳下undoLog1的情况。
*/
StatusInter HeapUpdateRollbackImpl(
    HeapRunCtxT *ctx, HpItemPointerT itemPtr, uint64_t targetRollPtr, const UndoRowOpInfoT *rowOpInfo)
{
    // rollBack阶段要回滚row
    // 悲观事务下，因为有事务锁，一定在主版本上
    // 乐观事务下，需要先加上页latch，然后判断是否在主版本，不在再去版本链去找
    DB_POINTER(ctx);

    if (SeTransGetTrxType(ctx->seRunCtx) == OPTIMISTIC_TRX ||
        SeTransGetIsolationLevel(ctx->seRunCtx) == REPEATABLE_READ) {
        return HeapUpdateRollbackWithOptimisticOrReReadTrx(ctx, itemPtr, targetRollPtr);
    } else {
        // 悲观事务可以直接删除主版本
        StatusInter ret = HeapUpdateCommitOrRollbackCleanForLobImpl(ctx, itemPtr);
        if (ret != STATUS_OK_INTER) {
            // delete lob wrong in update rollback
            SE_ERROR(ret, "ItemPtr %" PRIu32 ", %" PRIu32, itemPtr.pageId, itemPtr.slotId);
            return ret;
        }
        return HeapUpdateRollbackWithPessimisticTrx(ctx, itemPtr, rowOpInfo);
    }
}

static void HeapPessimisticMarkDeleteRollBack(
    HeapRunCtxT *ctx, const UndoRowOpInfoT *rowOpInfo, HpPageRowOpInfoT *opInfo)
{
    RowTrxInfoT txInfo = {{0}};
    txInfo.rollPtr = rowOpInfo->rowRollPtr;
    RowSetTrxId(&txInfo, rowOpInfo->rowTrxId);
    HeapSetTrxInfoForRollBack(ctx, txInfo);
    ctx->rollBackInfo.oldIsDeleted = rowOpInfo->isDeleted;
    DB_ASSERT(rowOpInfo->isDeleted == false);  // 悲观事务下，因为有事务锁，旧版本的IsDeleted一定为false
    // this will update the trxId and rollPtr of master version
    (void)HeapLabelResetOpType(ctx, HEAP_OPTYPE_DELETE, false);
    // Normal模式下: HeapClearRowMarkDelete (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
    HeapRecoverFunc(ctx, opInfo);
}

/*
为什么要传入targetRollPtr ?
乐观下可能有多个事务在操作同一行，因此当前回滚的事务是不一定在masterVersion上（见HeapUpdateRollback函数的示意图）

不用加redo原子操作，在undo 上层已开启
*/
StatusInter HeapMarkDeleteRollBackImpl(
    HeapRunCtxT *ctx, HpItemPointerT itemPtr, uint64_t targetRollPtr, const UndoRowOpInfoT *rowOpInfo)
{
    DB_POINTER(ctx);
    /* 标记删除回滚 */
    DB_ASSERT(ctx->hpOperation <= HEAP_OPTYPE_MODIFY);
    DB_ASSERT(((TrxT *)ctx->seRunCtx->trx)->base.state == TRX_STATE_ROLLBACK);
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    HpPageAllocRowInfoT pageAllocRowInfo = EmptyHpPageAllocRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, &pageAllocRowInfo);
    fetchRowInfo.srcRowInfo.itemPtr = itemPtr;
    fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;
    fetchRowInfo.isCanAccessMarkDelRow = true;  // 允许访问 被标记为删除的行
    fetchRowInfo.isOnlyReadSrcRow = true;
    fetchRowInfo.targetRollPtr = targetRollPtr;
    HeapSetPageLatchActionForWrite(ctx);
    HeapSetOperationForMarkDeleteRollBack(ctx);
    // Normal模式下: HeapSetLockActionBeforeRollBackDelete (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
    HeapSetLockFunc(ctx, ctx->hpControl.trxContrl.hpAmIndex);
    // 查询, 获取当前 row 的信息
    StatusInter ret = HeapFetchRow(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        goto EXIT;
    }
    TrxTypeE trxType = SeTransGetTrxType(ctx->seRunCtx);
    UndoRowOpInfoT newRowOpInfo = {0};
    if (SeTransGetIsolationLevel(ctx->seRunCtx) == REPEATABLE_READ) {
        ret = HeapRepReadReReadRollBackInfo(ctx, targetRollPtr, &newRowOpInfo);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            goto EXIT;
        }
    } else {
        newRowOpInfo = *rowOpInfo;
    }
    if (trxType == OPTIMISTIC_TRX) {
        HeapMarkDeleteRollBackWithOptimisticTrx(targetRollPtr, &fetchRowInfo, ctx, &newRowOpInfo, &opInfo);
    } else {
        DB_ASSERT(trxType == PESSIMISTIC_TRX);  // 存储层处理时只有乐观、悲观两种枚举情况
        HeapPessimisticMarkDeleteRollBack(ctx, &newRowOpInfo, &opInfo);
    }
    DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP,
        "Rollback Delete RollPtr %" PRIu64 " from version chain, prevRollPtr: %" PRIu64 ", nextRollPtr: %" PRIu64 "",
        targetRollPtr, fetchRowInfo.rowTrxInfo.rollPtr, newRowOpInfo.rowRollPtr);

    HeapStatMarkDeleteFailed(ctx);
EXIT:
    /* 释放 pageSorter 持有的latch */
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    return ret;
}
