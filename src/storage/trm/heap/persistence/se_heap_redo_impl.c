/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: heap 操作写redo的实现
 *
 */

#include "se_heap_redo_am_inner.h"
#include "se_heap_persist_inner.h"
#include "se_redo.h"
#include "se_log.h"
#include "se_heap_access.h"

void HeapRedoForFixPageInitImpl(PageIdT pageAddr, const HFPageCfgT *pageCfg)
{
    RedoLogFixPageInitT fixPageInitLog = {.pageCfg = *pageCfg};
    RedoLogWrite(
        SeGetCurRedoCtx(), REDO_OP_FIX_PAGE_INIT, &pageAddr, (uint8_t *)&fixPageInitLog, sizeof(RedoLogFixPageInitT));
}

void HeapRedoForSchemaChangeImpl(HFPageHeadT *pageHead, HFPageCfgT *pageCfg, uint8_t type)
{
    RedoLogSchemaChangeT schemaChangeLog = {.pageCfg = *pageCfg, .type = type};
    RedoLogWrite(SeGetCurRedoCtx(), REDO_OP_SCHEMA_CHANGE, &pageHead->baseHead.pageHead.addr,
        (uint8_t *)&schemaChangeLog, sizeof(RedoLogSchemaChangeT));
}

void HeapRedoForVarPageInitImpl(PageIdT *addr, PageSizeT slotExtendSize)
{
    RedoLogVarPageInitT logRec = {.slotExtendSize = slotExtendSize};
    RedoLogWrite(SeGetCurRedoCtx(), REDO_OP_VAR_PAGE_INIT, addr, (uint8_t *)&logRec, sizeof(RedoLogVarPageInitT));
}

void HeapRedoForFullPageImpl(HeapRunCtxT *ctx, HVPageHeadT *pageHead)
{
    RedoFullPageLogT logRec;
    logRec.labelId = ctx->heapCfg.labelId;
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogWrite(redoCtx, REDO_OP_PAGE_COMPRESS, &pageHead->baseHead.pageHead.addr, (uint8_t *)&logRec,
        sizeof(RedoFullPageLogT));
}

static uint32_t HeapGetRowHeapSizeByRowType(HpPageRowTypeE rowType)
{
    uint32_t size = 0;
    switch (rowType) {
        case HEAP_VAR_ROW_NORMAL_ROW:
            size = sizeof(HpNormalRowHeadT);
            break;
        case HEAP_VAR_ROW_LINK_SRC_ROW:
        case HEAP_VAR_ROW_LINK_DST_ROW:
        case HEAP_VAR_ROW_LINK_DST_SLICE_DIR_ROW:
            size = sizeof(HpLinkRowHeadT);
            break;
        case HEAP_FIX_ROW_NORMAL_ROW:
        case HEAP_FIX_ROW_LINK_DST_ROW:
            size = sizeof(HpNormalFixRowHead);
            break;
        case HEAP_FIX_ROW_LINK_SRC_ROW:
            size = HeapFixPageGetLinkSrcRowSize();
            break;
        case HEAP_VAR_ROW_SLICE_SUB_ROW:
            size = sizeof(HpSliceSubRowHeadT);
            break;
        case HEAP_INVALID_ROW_TYPE:
        default:
            DB_ASSERT(false);
            break;
    }
    return size;
}

StatusInter HeapRedoForInsertOrUpdateLobRowImpl(HpRunHdlT heapRunHdl, HpPageRowOpInfoT *opInfo)
{
    HeapRunCtxT *ctx = heapRunHdl;
    HpPageAllocRowInfoT *allocRowInfo = opInfo->allocRowInfo;
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    PageIdT pageId = DeserializePageId(ctx->seRunCtx->pageMgr, allocRowInfo->newRowInfo.itemPtr.pageId);
    uint32_t rowHeadSize = HeapGetRowHeapSizeByRowType(allocRowInfo->newRowInfo.rowType);
    if (rowHeadSize == 0) {
        SE_LAST_ERROR(
            INT_ERR_HEAP_UNEXPECT_ERROR, "record redoLog with rowType %" PRIu32, allocRowInfo->newRowInfo.rowType);
        return INT_ERR_HEAP_UNEXPECT_ERROR;
    }
    HVPageHeadT *varPageHead = allocRowInfo->newRowInfo.pageHeadPtr.varPageHead;
    HeapRedoAllocLobRowT log = {.freePosEnd = varPageHead->freePosEnd,
        .slotDirEnd = varPageHead->slotDirEnd,
        .freeSlotCnt = varPageHead->freeSlotCnt,
        .slotCnt = varPageHead->slotCnt,
        .freeSlotHead = varPageHead->freeSlotHead,
        .entryUsedNum = varPageHead->baseHead.pageHead.entryUsedNum,
        .freeSize = varPageHead->baseHead.pageHead.freeSize,
        .endPos = varPageHead->baseHead.pageHead.endPos,
        .pageState = varPageHead->baseHead.pageHead.pageState,
        .maxModifyRowOffset = varPageHead->maxModifyRowOffset,
        .slot = *allocRowInfo->newRowInfo.slot,
        .slotOffset = allocRowInfo->newRowInfo.slotOffset,
        .dataSize = allocRowInfo->storeSize,  // 记录整行的数据，包括行头
        .rowType = allocRowInfo->newRowInfo.rowType};

    RedoLogWrite(redoCtx, REDO_HEAP_ALLOC_LOB_ROW, &pageId, (uint8_t *)&log, sizeof(HeapRedoAllocLobRowT));
    RedoLogAppend(redoCtx, (uint8_t *)allocRowInfo->newRowInfo.rowHeadPtr.rowState, log.dataSize);
    return STATUS_OK_INTER;
}

void HeapRedoForBindDstRowImpl(HpRunHdlT heapRunHdl, HeapRowInfoT *srcRowInfo)
{
    HeapRunCtxT *ctx = heapRunHdl;
    HpLinkRowHeadT *rowHead = (HpLinkRowHeadT *)srcRowInfo->rowHeadPtr.rowState;
    HeapRedoBindDstRowT log = {
        .rowOffset = (uint16_t)((uint8_t *)rowHead - (uint8_t *)srcRowInfo->pageHeadPtr.pageHead),
        .freeSize = srcRowInfo->pageHeadPtr.pageHead->freeSize,
        .rowHead = *rowHead};
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    PageIdT pageId = DeserializePageId(ctx->seRunCtx->pageMgr, srcRowInfo->itemPtr.pageId);
    RedoLogWrite(redoCtx, REDO_HEAP_BIND_DST_ROW, &pageId, (uint8_t *)&log, sizeof(HeapRedoBindDstRowT));
}

void HeapRedoForBindSliceSubRowToDirRowImpl(HpRunHdlT heapRunHdl, HpItemPointerT dirRowPtr, HpSliceRowDirT *dirRow)
{
    HeapRunCtxT *ctx = heapRunHdl;
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    PageIdT pageId = DeserializePageId(ctx->seRunCtx->pageMgr, dirRowPtr.pageId);
    HeapRedoBindSliceSubRowT log = {.sliceRowNum = dirRow->sliceRowNum,
        .subRowPtr = dirRow->sliceRowEntries[dirRow->sliceRowNum - 1].sliceSubRowPtr,
        .dirRowSlotId = (uint16_t)dirRowPtr.slotId,
        .totalSize = dirRow->totalSize};
    RedoLogWrite(redoCtx, REDO_HEAP_BIND_SUB_ROW, &pageId, (uint8_t *)&log, sizeof(HeapRedoBindSliceSubRowT));
}

void HeapRedoForUpdateDirRowListPtrImpl(
    HpRunHdlT heapRunHdl, RedoLogTypeE type, HpItemPointerT originPtr, HpItemPointerT listPtr)
{
    HeapRunCtxT *ctx = heapRunHdl;
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    HeapRedoUpdateListPtrT log = {
        .slotId = originPtr.slotId,
        .listRowAddr = listPtr,
    };
    PageIdT pageId = DeserializePageId(ctx->seRunCtx->pageMgr, originPtr.pageId);
    RedoLogWrite(redoCtx, type, &pageId, (uint8_t *)&log, sizeof(HeapRedoUpdateListPtrT));
}

void HeapRedoForDelSliceSubRowImpl(HpRunHdlT heapRunHdl, HeapRowInfoT *dirRowInfo)
{
    HeapRunCtxT *ctx = heapRunHdl;
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    HpLinkRowHeadT *rowHead = (HpLinkRowHeadT *)dirRowInfo->rowHeadPtr.rowState;
    HpSliceRowDirT *dirRow = (HpSliceRowDirT *)(void *)(rowHead + 1);
    HeapRedoDelSubRowT log = {.rowOffset = (uint16_t)((uint8_t *)dirRow - (uint8_t *)dirRowInfo->pageHeadPtr.pageHead),
        .sliceRowNum = dirRow->sliceRowNum,
        .totalSize = dirRow->totalSize};
    PageIdT pageId = DeserializePageId(ctx->seRunCtx->pageMgr, dirRowInfo->itemPtr.pageId);
    RedoLogWrite(redoCtx, REDO_HEAP_DIR_ROW_UPDATE_SUB_NUM, &pageId, (uint8_t *)&log, sizeof(HeapRedoDelSubRowT));
}

void HeapRedoForInsertImpl(HpRunHdlT heapRunHdl, HpItemPointerT *addr, uint32_t redoOpType, HpPageRowOpInfoT *opInfo)
{
    HeapRunCtxT *ctx = heapRunHdl;
    HpPageAllocRowInfoT *allocRowInfo = opInfo->allocRowInfo;

    const uint8_t *buf = allocRowInfo->buf;
    uint32_t bufSize = allocRowInfo->bufSize;

    HeapInsertRedoLogT logRec;
    logRec.labelId = ctx->heapCfg.labelId;
    logRec.slotId = (uint16_t)addr->slotId;
    logRec.flags = opInfo->flags;
    logRec.markDeletedState = allocRowInfo->markDeletedState;
    logRec.trxInfo = allocRowInfo->newTrxInfo;
    logRec.rollbackReserveSize = allocRowInfo->newRowInfo.rollbackReserveSize;

    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    PageIdT pageId = DeserializePageId(ctx->seRunCtx->pageMgr, addr->pageId);
    RedoLogWrite(redoCtx, redoOpType, &pageId, (uint8_t *)&logRec, sizeof(HeapInsertRedoLogT));
    RedoLogAppend(redoCtx, buf, bufSize);
}

void HeapRedoForUpdateImpl(HeapRedoUpdateParams *params)
{
    const HeapRunCtxT *ctx = params->heapRunCtx;
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();

    HpPageAllocRowInfoT *allocRowInfo = (HpPageAllocRowInfoT *)(void *)params->opInfo->allocRowInfo;
    uint32_t bufSize = 0;
    const uint8_t *buf = NULL;

    HeapUpdateRedoLogT logRec;
    logRec.labelId = ctx->heapCfg.labelId;
    logRec.needBuf = params->needBuf;
    // allocRowInfo->flags 可能包含 partialUpdate 信息
    logRec.flags = params->opInfo->flags | allocRowInfo->flags;
    logRec.slotId = (uint16_t)params->addr.slotId;
    logRec.oldStoreSize = params->opInfo->updInPageOldStoreSize;
    logRec.markDeletedState = allocRowInfo->markDeletedState;
    logRec.trxInfo = allocRowInfo->newTrxInfo;
    if (params->needBuf) {
        logRec.refAddr = params->opInfo->fetchRowInfo->srcRowInfo.itemPtr;
        buf = allocRowInfo->buf;
        bufSize = allocRowInfo->bufSize;
        // 在 HeapVarPageInitLinkSrcRow 恢复时不用
        logRec.rollbackReserveSize = allocRowInfo->newRowInfo.rollbackReserveSize;
    } else {
        logRec.refAddr = params->opInfo->allocRowInfo->newRowInfo.itemPtr;
        logRec.bufSize = allocRowInfo->bufSize;  // 只在 src row head恢复时使用
    }

    PageIdT pageId = DeserializePageId(ctx->seRunCtx->pageMgr, params->addr.pageId);
    RedoLogWrite(redoCtx, params->redoOpType, &pageId, (uint8_t *)&logRec, sizeof(HeapUpdateRedoLogT));
    // 某些更新情形只是修改了row head 某些字段
    if (params->needBuf) {
        RedoLogAppend(redoCtx, buf, bufSize);
    }
}

void HeapRedoForDeleteImpl(HeapRedoDeleteParams *params)
{
    const HeapRunCtxT *ctx = params->heapRunCtx;
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();

    HeapDeleteRedoLogT logRec = {.labelId = ctx->heapCfg.labelId,
        .size = sizeof(HeapDeleteRedoLogT),
        .slotId = (uint16_t)params->addr.slotId,
        .flags = params->flags,
        .trxInfo = params->trxInfo};

    PageIdT pageId = DeserializePageId(ctx->seRunCtx->pageMgr, params->addr.pageId);
    RedoLogWrite(redoCtx, params->redoOpType, &pageId, (uint8_t *)&logRec, sizeof(HeapDeleteRedoLogT));
}

void HeapRedoForCreateImpl(PageIdT heapAddr, HeapT *heap)
{
    RedoRunCtxT *redoRunCtx = SeGetCurRedoCtx();
    RedoLogWrite(redoRunCtx, REDO_OP_HEAP_CREATE, &heapAddr, (uint8_t *)heap, sizeof(HeapT) - sizeof(LfsMgrT));
}

void HeapRedoForModifyImpl(uint8_t *buf, uint32_t size, PageIdT *addr, uint16_t offset)
{
    RedoPageMdLogT pmLog;
    pmLog.logType = REDO_OP_HEAP_MODIFY;

    pmLog.offset = offset;
    pmLog.dataSize = size;
    RedoPageMdLogWrite(SeGetCurRedoCtx(), addr, &pmLog, buf);
}

StatusInter HeapRedoForUnlinkRollPtrImpl(PageHeadT *pageHead, uint64_t *rollPtrAddr)
{
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoPageMdLogT pmLog;
    pmLog.offset = GetOffsetInPage((uintptr_t)pageHead, (uintptr_t)rollPtrAddr, pageHead->endPos);
    pmLog.dataSize = sizeof(uint64_t);
    RedoPageMdLogWrite(redoCtx, &pageHead->addr, &pmLog, (uint8_t *)rollPtrAddr);
    return STATUS_OK_INTER;
}

void HeapRedoForRollbackImpl(
    HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchCurRowInfo, bool rollbackNormalRow, uint8_t *buf, uint32_t bufSize)
{
    HpItemPointerT itemPtr = fetchCurRowInfo->srcRowInfo.itemPtr;
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();

    HeapRollbackRedoLogT logRec;
    logRec.labelId = ctx->heapCfg.labelId;
    logRec.rollbackNormalRow = rollbackNormalRow;
    logRec.slotId = (uint16_t)itemPtr.slotId;
    logRec.oldStoreSize = HeapPageGetMinRowSize((PageSizeT)fetchCurRowInfo->bufSize, HEAP_VAR_ROW_NORMAL_ROW);

    PageIdT pageId = DeserializePageId(ctx->seRunCtx->pageMgr, itemPtr.pageId);
    RedoLogWrite(redoCtx, REDO_OP_ROLLBACK, &pageId, (uint8_t *)&logRec, sizeof(HeapUpdateRedoLogT));
    RedoLogAppend(redoCtx, buf, bufSize);
}

void HeapRedoForBatchInsertImpl(
    HeapRunCtxT *ctx, PageIdT pageId, uint64_t rollPtr, HpPageGroupRowInfoT *groupRow, uint32_t fsmAvailSize)
{
    HeapBatchInsertRedoLogT logRec = {.trxId = ((TrxT *)ctx->seRunCtx->trx)->base.trxId,
        .rollPtr = rollPtr,
        .slotExtendSize = ctx->heapCfg.slotExtendSize,
        .availSize = fsmAvailSize,
        .groupSize = groupRow->groupSize,
        .isFix = ctx->hpControl.isFixPage};

    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogWrite(redoCtx, REDO_OP_BATCH_INSERT, &pageId, (uint8_t *)&logRec, sizeof(HeapBatchInsertRedoLogT));

    for (uint32_t i = 0; i < groupRow->groupSize; ++i) {
        uint32_t bufSize = groupRow->tuples[i].bufSize;
        RedoLogAppend(redoCtx, (uint8_t *)&bufSize, sizeof(uint32_t));
        RedoLogAppend(redoCtx, groupRow->tuples[i].buf, groupRow->tuples[i].bufSize);
    }
}

void HeapRedoForRowHeadImpl(HeapRunCtxT *ctx, PageHeadT *pageHead, uint32_t type, uint32_t offset, uint32_t size)
{
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoPageMdLogT pmLog;
    pmLog.logType = type;

    pmLog.offset = offset;
    pmLog.dataSize = size;
    RedoPageMdLogWrite(redoCtx, &pageHead->addr, &pmLog, ((uint8_t *)pageHead + offset));
}

SO_EXPORT_FOR_TS void HeapRedoAmInit(SeInstanceT *seIns)
{
    HeapRedoAmT heapRedoAm = {
        HeapRedoForFixPageInitImpl,
        HeapRedoForSchemaChangeImpl,
        HeapRedoForVarPageInitImpl,
        HeapRedoForFullPageImpl,
        HeapRedoForInsertImpl,
        HeapRedoForBatchInsertImpl,
        HeapRedoForUpdateImpl,
        HeapRedoForDeleteImpl,
        HeapRedoForCreateImpl,
        HeapRedoForModifyImpl,
        HeapRedoForRollbackImpl,
        HeapRedoForRowHeadImpl,
        HeapRedoForInsertOrUpdateLobRowImpl,
        HeapRedoForBindSliceSubRowToDirRowImpl,
        HeapRedoForBindDstRowImpl,
        HeapRedoForUpdateDirRowListPtrImpl,
        HeapRedoForDelSliceSubRowImpl,
    };
    HeapRedoSetAmStruct(&heapRedoAm);
    HeapReplayFuncRegister(seIns);
}
