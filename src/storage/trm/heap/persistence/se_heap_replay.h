/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: Heap 回放接口
 */
#ifndef SE_HEAP_REPLAY_H
#define SE_HEAP_REPLAY_H

#include "se_replay.h"
#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */
StatusInter HeapInsertReplay(RedoReplayArgsT *arg);
StatusInter HeapBatchInsertReplay(RedoReplayArgsT *arg);
StatusInter HeapUpdateReplay(RedoReplayArgsT *arg);
StatusInter HeapDeleteReplay(RedoReplayArgsT *arg);
StatusInter HeapFixPageInitReplay(RedoReplayArgsT *arg);
StatusInter HeapFixPageUpgradeReplay(RedoReplayArgsT *arg);
StatusInter HeapVarPageInitReplay(RedoReplayArgsT *arg);
StatusInter HeapCreateReplay(RedoReplayArgsT *arg);
StatusInter HeapModifyReplay(RedoReplayArgsT *arg);
StatusInter HeapFullPageReplay(RedoReplayArgsT *arg);
StatusInter HeapRollbackReplay(RedoReplayArgsT *arg);
#ifdef __cplusplus
}
#endif /* __cplusplus */
#endif
