/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: heap 回放redo的实现
 *
 */

#include "se_heap_replay.h"
#include "se_heap_redo_am_inner.h"
#include "se_heap_persist_inner.h"
#include "se_heap.h"
#include "se_heap_access.h"
#include "se_log.h"
#include "se_heap_am_inner.h"

typedef struct TagVarPageUpdateReplayCtx {
    HpPageAllocRowInfoT *allocRowInfo;
    HpItemPointerT *itemPointer;
    uint32_t flags;
    uint32_t oldStoreSize;
    HpItemPointerT refAddr;
    bool needBuf;
} VarPageUpdateReplayCtx;

StatusInter ReplayHeapVarPageAllocRow(
    HpPageAllocRowInfoT *allocRowInfo, HpItemPointerT itemPtr, PageSizeT oldStoreSize, uint32_t flags)
{
    HeapRowInfoT *newRowInfo = &allocRowInfo->newRowInfo;
    HVPageHeadT *pageHead = newRowInfo->pageHeadPtr.varPageHead;
    newRowInfo->rowHeadPtr.rowState =
        (HpRowStateT *)(void *)((uint8_t *)pageHead + HeapVarPageGetNewRowOffset(pageHead, allocRowInfo->storeSize));

    if (IsHeapFlagSet(flags, F_HEAP_INSERT_NEW_SLOT)) {
        HeapPageInitRowByRowType(NULL, allocRowInfo, newRowInfo->rowHeadPtr);
        bool isNewAllocSlot;
        HeapVarPageInsertSlot(pageHead, allocRowInfo->storeSize, newRowInfo, &isNewAllocSlot);
    } else {
        HpRowSlotT *srcSlot = NULL;
        PageSizeT slotOffset;
        StatusInter ret = HeapVarPageGetSlotBySlotIdImpl(pageHead, itemPtr.slotId, &slotOffset, &srcSlot);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        newRowInfo->itemPtr = itemPtr;
        newRowInfo->slot = srcSlot;
        HeapPageInitRowByRowType(NULL, allocRowInfo, newRowInfo->rowHeadPtr);
        HeapVarPageUpdateSlot(NULL, pageHead, newRowInfo->slot, oldStoreSize, allocRowInfo->storeSize);
    }
    return STATUS_OK_INTER;
}

void ReplayHeapFixPageAllocRow(HpPageAllocRowInfoT *allocRowInfo)
{
    (void)HeapFixPageAllocRow(
        allocRowInfo->newRowInfo.pageHeadPtr.fixPageHead, &allocRowInfo->newRowInfo.rowHeadPtr.normalFixRowHead);
    HeapPageInitRowByRowType(NULL, allocRowInfo, allocRowInfo->newRowInfo.rowHeadPtr);
}

StatusInter HeapInsertReplay(RedoReplayArgsT *arg)
{
    HeapInsertRedoLogT *rec = (HeapInsertRedoLogT *)(void *)arg->data;

    void *buf = arg->data + sizeof(HeapInsertRedoLogT);
    uint32_t bufSize = arg->size - sizeof(HeapInsertRedoLogT);
    HpItemPointerT itemPointer = {.pageId = arg->pageHead->addr.blockId, .slotId = rec->slotId};

    HpPageAllocRowInfoT allocRowInfo = {0};
    allocRowInfo.buf = buf;
    allocRowInfo.bufSize = bufSize;
    allocRowInfo.newTrxInfo = rec->trxInfo;
    allocRowInfo.markDeletedState = rec->markDeletedState;
    if (IsHeapFlagSet(rec->flags, F_HEAP_MODIFY_FIX_ROW)) {
        allocRowInfo.newRowInfo.pageHeadPtr.fixPageHead = (HFPageHeadT *)(void *)arg->pageHead;
        HeapAllocRowInfoInit(&allocRowInfo, HEAP_FIX_ROW_NORMAL_ROW, false);
        ReplayHeapFixPageAllocRow(&allocRowInfo);
        return STATUS_OK_INTER;
    } else {
        allocRowInfo.newRowInfo.rollbackReserveSize = rec->rollbackReserveSize;
        HeapAllocRowInfoInit(&allocRowInfo, HEAP_VAR_ROW_NORMAL_ROW, true);
        allocRowInfo.newRowInfo.pageHeadPtr.varPageHead = (HVPageHeadT *)(void *)arg->pageHead;
        return ReplayHeapVarPageAllocRow(&allocRowInfo, itemPointer, 0, rec->flags);
    }
}

StatusInter ReplayHeapVarPageDeleteRow(HVPageHeadT *page, const HpItemPointerT *itemPointer)
{
    PageSizeT slotOffset;
    HpRowSlotT *slot = NULL;
    StatusInter ret = HeapVarPageGetSlotBySlotIdImpl(page, (PageSizeT)itemPointer->slotId, &slotOffset, &slot);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    HeapRowInfoT dstRowInfo;
    dstRowInfo.itemPtr = *itemPointer;
    dstRowInfo.slotOffset = slotOffset;
    dstRowInfo.slot = slot;
    dstRowInfo.pageHeadPtr.varPageHead = page;
    HeapVarPageDeleteRow(NULL, &dstRowInfo);

    return STATUS_OK_INTER;
}

static inline HpRowHeadPtrT GetRowHeadPtr(HVPageHeadT *pageHead, uint32_t slotId)
{
    HpRowStateT *rowState = HeapVarPageGetRowStateBySlotId(pageHead, slotId);
    DB_ASSERT(rowState != NULL);
    HpRowHeadPtrT rowHead;
    rowHead.rowState = rowState;
    return rowHead;
}

// 非固定页更新
StatusInter ReplayUpdateForVarPage(
    HeapUpdateRedoLogT *rec, HpItemPointerT *itemPointer, HpPageAllocRowInfoT *allocRowInfo)
{
    StatusInter ret = STATUS_OK_INTER;
    PageSizeT oldStoreSize = (PageSizeT)rec->oldStoreSize;
    HVPageHeadT *pageHead = allocRowInfo->newRowInfo.pageHeadPtr.varPageHead;
    uint32_t flags = rec->flags;

    if (IsHeapFlagSet(flags, F_HEAP_UPDATE_NORMAL_ROW_INPLACE)) {
        HpRowHeadPtrT rowHead = GetRowHeadPtr(pageHead, itemPointer->slotId);
        HeapAllocRowInfoInit(allocRowInfo, HEAP_VAR_ROW_NORMAL_ROW, false);
        HeapVarPageInitNormalRowImpl(NULL, rowHead.normalRowHead, allocRowInfo);
    } else if (IsHeapFlagSet(flags, F_HEAP_UPDATE_NORMAL_ROW_INPAGE)) {
        HeapAllocRowInfoInit(allocRowInfo, HEAP_VAR_ROW_NORMAL_ROW, false);
        allocRowInfo->newRowInfo.itemPtr = *itemPointer;
        allocRowInfo->newRowInfo.pageHeadPtr.varPageHead = pageHead;

        HeapMoveRow(allocRowInfo, itemPointer->slotId, allocRowInfo->storeSize - oldStoreSize, oldStoreSize);
    } else if (IsHeapFlagSet(flags, F_HEAP_UPDATE_NORMAL_ROW_ACROSS_PAGE)) {
        if (rec->needBuf) {
            // 恢复新分配的页的row
            HeapAllocRowInfoInit(allocRowInfo, HEAP_VAR_ROW_LINK_DST_ROW, true);
            // 让src row能够赋给src RowHead
            allocRowInfo->sliceRowInfo.linkSrcRowPtr = rec->refAddr;
            ret = ReplayHeapVarPageAllocRow(allocRowInfo, *itemPointer, oldStoreSize, flags);
        } else {
            HpRowHeadPtrT rowHead = GetRowHeadPtr(pageHead, itemPointer->slotId);
            DB_ASSERT(HeapPageIsNormalRow((const HpRowStateT *)rowHead.rowState));
            // 让dst row能够赋给src RowHead
            allocRowInfo->newRowInfo.itemPtr = rec->refAddr;
            HeapVarPageInitLinkSrcRowImpl(NULL, rowHead.linkRowHead, allocRowInfo, true);
        }
    } else if (IsHeapFlagSet(flags, F_HEAP_UPDATE_LINKROW_TO_LINK)) {
        if (rec->needBuf) {
            HeapAllocRowInfoInit(allocRowInfo, HEAP_VAR_ROW_LINK_DST_ROW, true);
            // 让src row能够赋给src RowHead
            allocRowInfo->sliceRowInfo.linkSrcRowPtr = rec->refAddr;
            ret = ReplayHeapVarPageAllocRow(allocRowInfo, *itemPointer, oldStoreSize, flags);
        } else {
            HpRowHeadPtrT rowHead = GetRowHeadPtr(pageHead, itemPointer->slotId);
            // 让dst row能够赋给src RowHead
            allocRowInfo->newRowInfo.itemPtr = rec->refAddr;
            HeapVarPageInitLinkSrcRowImpl(NULL, rowHead.linkRowHead, allocRowInfo, true);
        }
    }
    return ret;
}

static inline HpRowHeadPtrT HeapFixPageGetRowHead(HFPageHeadT *pageHead, PageSizeT slotId)
{
    HpRowHeadPtrT fixRowHead;
    PageSizeT slotOffset;
    StatusInter ret = HeapFixPageGetRowBySlotIdImpl(pageHead, slotId, &fixRowHead.normalFixRowHead, &slotOffset);
    DB_ASSERT(ret == STATUS_OK_INTER);
    return fixRowHead;
}

void ReplayHeapUpdateFixRow(HeapUpdateRedoLogT *rec, HpItemPointerT *itemPointer, HpPageAllocRowInfoT *allocRowInfo)
{
    uint32_t flags = rec->flags;
    HFPageHeadT *pageHead = allocRowInfo->newRowInfo.pageHeadPtr.fixPageHead;
    if (IsHeapFlagSet(flags, F_HEAP_UPDATE_FIX_ROW_INPLACE)) {
        HpRowHeadPtrT rowHead = HeapFixPageGetRowHead(pageHead, (PageSizeT)itemPointer->slotId);
        HeapFixPageInitNormalRowImpl(rowHead.normalFixRowHead, allocRowInfo);
    } else if (IsHeapFlagSet(flags, F_HEAP_UPDATE_FIX_ROW_ACROSS_PAGE)) {
        if (rec->needBuf) {
            // 恢复 dst row
            HeapAllocRowInfoInit(allocRowInfo, HEAP_FIX_ROW_LINK_DST_ROW, true);
            ReplayHeapFixPageAllocRow(allocRowInfo);
        } else {
            // 恢复 src page
            HpRowHeadPtrT rowHead = HeapFixPageGetRowHead(pageHead, (PageSizeT)itemPointer->slotId);
            allocRowInfo->newRowInfo.itemPtr = rec->refAddr;
            HeapFixPageInitLinkSrcRowImpl(rowHead.linkSrcFixRowHead, allocRowInfo);
        }
    }
}

StatusInter HeapUpdateReplay(RedoReplayArgsT *arg)
{
    StatusInter ret = STATUS_OK_INTER;
    HeapUpdateRedoLogT *rec = (HeapUpdateRedoLogT *)(void *)arg->data;

    void *buf = arg->data + sizeof(HeapUpdateRedoLogT);
    uint32_t bufSize = arg->size - sizeof(HeapUpdateRedoLogT);
    HpItemPointerT itemPointer = {.pageId = arg->pageHead->addr.blockId, .slotId = rec->slotId};

    HpPageAllocRowInfoT allocRowInfo = {0};  // 重要：保持所有值默认是0

    // 后期应该改为使用新的struct而不是 HpPageAllocRowInfoT
    // 来做恢复，HpPageAllocRowInfoT结构太大，只有少数字段需要写进页，一致性维护成本高
    if (rec->needBuf) {
        allocRowInfo.buf = buf;
        allocRowInfo.bufSize = bufSize;
    } else {
        allocRowInfo.bufSize = rec->bufSize;
    }

    allocRowInfo.newTrxInfo = rec->trxInfo;
    allocRowInfo.markDeletedState = rec->markDeletedState;
    if (IsHeapFlagSet(rec->flags, F_HEAP_MODIFY_FIX_ROW)) {
        allocRowInfo.newRowInfo.pageHeadPtr.fixPageHead = (HFPageHeadT *)(void *)arg->pageHead;
        ReplayHeapUpdateFixRow(rec, &itemPointer, &allocRowInfo);
    } else {
        allocRowInfo.newRowInfo.rollbackReserveSize = rec->rollbackReserveSize;
        allocRowInfo.newRowInfo.pageHeadPtr.varPageHead = (HVPageHeadT *)(void *)arg->pageHead;
        ret = ReplayUpdateForVarPage(rec, &itemPointer, &allocRowInfo);
    }
    return ret;
}

StatusInter ReplayDeleteForVarPage(HeapDeleteRedoLogT *rec, HpItemPointerT *itemPointer, HVPageHeadT *pageHead)
{
    uint32_t flags = rec->flags;
    StatusInter ret = STATUS_OK_INTER;
    if (IsHeapFlagSet(flags, F_HEAP_MARK_DELETE)) {
        HpRowHeadPtrT rowHead = GetRowHeadPtr(pageHead, itemPointer->slotId);
        if (IsHeapFlagSet(flags, F_HEAP_MARK_DELETE_NORMAL_VAR_ROW)) {
            rowHead.normalRowHead->rowState.isDeleted = true;
            rowHead.normalRowHead->trxInfo = rec->trxInfo;
        } else {
            rowHead.linkRowHead->rowState.isDeleted = true;
            rowHead.linkRowHead->srcTrxInfo = rec->trxInfo;
        }
    } else {
        ret = ReplayHeapVarPageDeleteRow(pageHead, itemPointer);
    }
    return ret;
}

void ReplayDeleteHeapFixPage(HeapDeleteRedoLogT *rec, HpItemPointerT *itemPointer, HFPageHeadT *pageHead)
{
    uint32_t flags = rec->flags;
    if (IsHeapFlagSet(flags, F_HEAP_MARK_DELETE)) {
        HpRowHeadPtrT rowHead = HeapFixPageGetRowHead(pageHead, (PageSizeT)itemPointer->slotId);
        if (IsHeapFlagSet(flags, F_HEAP_MARK_DELETE_NORMAL_FIX_ROW)) {
            rowHead.normalFixRowHead->rowState.isDeleted = true;
            rowHead.normalRowHead->trxInfo = rec->trxInfo;
        } else {
            rowHead.linkSrcFixRowHead->rowState.isDeleted = true;
            rowHead.linkSrcFixRowHead->trxInfo = rec->trxInfo;
        }
    } else {
        HeapFixPageFreeRow(pageHead, (PageSizeT)itemPointer->slotId);
    }
}

StatusInter HeapDeleteReplay(RedoReplayArgsT *arg)
{
    StatusInter ret = STATUS_OK_INTER;
    HeapDeleteRedoLogT *rec = (HeapDeleteRedoLogT *)(void *)arg->data;

    HpItemPointerT itemPointer = {.pageId = arg->pageHead->addr.blockId, .slotId = rec->slotId};

    if (IsHeapFlagSet(rec->flags, F_HEAP_MODIFY_FIX_ROW)) {
        ReplayDeleteHeapFixPage(rec, &itemPointer, (HFPageHeadT *)(void *)arg->pageHead);
    } else {
        ret = ReplayDeleteForVarPage(rec, &itemPointer, (HVPageHeadT *)(void *)arg->pageHead);
    }
    return ret;
}

StatusInter HeapFixPageInitReplay(RedoReplayArgsT *arg)
{
    RedoLogFixPageInitT *rec = (RedoLogFixPageInitT *)(void *)arg->data;
    HFPageCfgT fixPageCfg = rec->pageCfg;
    HeapFixPageInitPage((HFPageHeadT *)(void *)arg->pageHead, &fixPageCfg);
    return STATUS_OK_INTER;
}

StatusInter HeapSchemaChangeReplay(RedoReplayArgsT *arg)
{
    RedoLogSchemaChangeT *rec = (RedoLogSchemaChangeT *)(void *)arg->data;
    HFPageCfgT fixPageCfg = rec->pageCfg;
    HFPageHeadT *pageHead = (HFPageHeadT *)(void *)arg->pageHead;
    rec->type == HEAP_FIX_UPGRADE ? HeapFixPageUpgradeInner(pageHead, &fixPageCfg) :
                                    HeapFixPageDowngradeInner(pageHead, &fixPageCfg);
    return STATUS_OK_INTER;
}

StatusInter HeapVarPageInitReplay(RedoReplayArgsT *arg)
{
    RedoLogVarPageInitT *rec = (RedoLogVarPageInitT *)(void *)arg->data;
    HeapVarPageInitPage((HVPageHeadT *)(void *)arg->pageHead, rec->slotExtendSize);
    return STATUS_OK_INTER;
}

StatusInter HeapCreateReplay(RedoReplayArgsT *arg)
{
    HeapT *heap = (HeapT *)(void *)((uint8_t *)arg->pageHead + sizeof(PageHeadT));
    SePageHeadSetType(arg->pageHead, PERSISTENCE_PAGE_TYPE_HEAP_META);
    errno_t err = memset_s((void *)heap, sizeof(HeapT), 0, sizeof(HeapT));
    DB_ASSERT(err == EOK);

    (void)memcpy_s(heap, sizeof(HeapT) - sizeof(LfsMgrT), arg->data, sizeof(HeapT) - sizeof(LfsMgrT));
    return STATUS_OK_INTER;
}

StatusInter HeapRollbackReplay(RedoReplayArgsT *arg)
{
    HeapRollbackRedoLogT *rec = (HeapRollbackRedoLogT *)(void *)arg->data;
    PageHeadT *pageHead = arg->pageHead;

    void *buf = arg->data + sizeof(HeapRollbackRedoLogT);
    uint32_t bufSize = arg->size - sizeof(HeapRollbackRedoLogT);
    HpItemPointerT itemPointer = {.pageId = arg->pageHead->addr.blockId, .slotId = rec->slotId};

    HpRowStateT *rowState = HeapVarPageGetRowStateBySlotId((HVPageHeadT *)(void *)arg->pageHead, itemPointer.slotId);
    DB_ASSERT(rowState != NULL);
    HpRowHeadPtrT rowHead;
    rowHead.rowState = rowState;
    if (rec->rollbackNormalRow) {
        pageHead->freeSize += (uint16_t)(rec->oldStoreSize - sizeof(HpLinkRowHeadT));
    } else {
        errno_t err = memcpy_s(rowHead.linkRowHead, sizeof(HpLinkRowHeadT), buf, bufSize);
        if (err != EOK) {
            // unable to memory copy buffer when replay rollback heap
            SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "memcpy, buffer size(%" PRIu32 ").", bufSize);
            return MEMORY_OPERATE_FAILED_INTER;
        }
    }
    return STATUS_OK_INTER;
}

StatusInter HeapFullPageReplay(RedoReplayArgsT *arg)
{
    SeInstanceT *seIns = arg->seInstance;
    if (seIns == NULL) {
        SE_ERROR(INVALID_PARAMETER_VALUE_INTER, "storage instance inv");
        return INVALID_PARAMETER_VALUE_INTER;
    }
    HVPageHeadT *pageHead = (HVPageHeadT *)(void *)arg->pageHead;
    HeapCompressNodeT *sortSlotDir =
        DbDynMemCtxAlloc((DbMemCtxT *)seIns->seServerMemCtx, sizeof(HeapCompressNodeT) * pageHead->slotCnt);
    if (sortSlotDir == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "malloc for page compress");
        return MEMORY_OPERATE_FAILED_INTER;
    }
    // 回放时再做一次压缩
    DoVarPageCompress((HVPageHeadT *)(void *)arg->pageHead, sortSlotDir);
    DbDynMemCtxFree(seIns->seServerMemCtx, sortSlotDir);
    return STATUS_OK_INTER;
}

StatusInter ConstructHpPageGroupRowInfo(
    DbMemCtxT *dynMemCtx, RedoReplayArgsT *arg, HpPageGroupRowInfoT *groupRow, HeapTupleBufT **heapTuples)
{
    HeapBatchInsertRedoLogT *rec = (HeapBatchInsertRedoLogT *)(void *)arg->data;
    uint8_t *buf = (uint8_t *)(arg->data + sizeof(HeapBatchInsertRedoLogT));
    groupRow->groupSize = rec->groupSize;
    groupRow->maxRowSize = 0u;
    groupRow->batchOut = DbDynMemCtxAlloc(dynMemCtx, sizeof(BatchOutT) * rec->groupSize);
    if (groupRow->batchOut == NULL) {
        SE_ERROR(OUT_OF_MEMORY_INTER, "alloc batchOut obj");
        return OUT_OF_MEMORY_INTER;
    }
    groupRow->newRowInfo = DbDynMemCtxAlloc(dynMemCtx, sizeof(HeapRowInfoT) * rec->groupSize);
    if (groupRow->newRowInfo == NULL) {
        DbDynMemCtxFree(dynMemCtx, groupRow->batchOut);
        SE_ERROR(OUT_OF_MEMORY_INTER, "alloc new row info");
        return OUT_OF_MEMORY_INTER;
    }
    HeapTupleBufT *tuples = DbDynMemCtxAlloc(dynMemCtx, sizeof(HeapTupleBufT) * rec->groupSize);
    if (tuples == NULL) {
        DbDynMemCtxFree(dynMemCtx, groupRow->batchOut);
        DbDynMemCtxFree(dynMemCtx, groupRow->newRowInfo);
        SE_ERROR(OUT_OF_MEMORY_INTER, "alloc heap buf");
        return OUT_OF_MEMORY_INTER;
    }
    for (uint32_t i = 0; i < rec->groupSize; ++i) {
        tuples[i].buf = buf + sizeof(uint32_t);
        tuples[i].bufSize = *(uint32_t *)(void *)buf;
        buf += (sizeof(uint32_t) + tuples[i].bufSize);
    }
    groupRow->tuples = tuples;
    *heapTuples = tuples;
    return STATUS_OK_INTER;
}

StatusInter HeapBatchInsertReplay(RedoReplayArgsT *arg)
{
    SeInstanceT *seIns = arg->seInstance;
    if (seIns == NULL) {
        SE_ERROR(INVALID_PARAMETER_VALUE_INTER, "storage instance inv");
        return INVALID_PARAMETER_VALUE_INTER;
    }
    DbMemCtxT *dynMemCtx = seIns->seServerMemCtx;
    HeapBatchInsertRedoLogT *rec = (HeapBatchInsertRedoLogT *)(void *)arg->data;
    HpPageGroupRowInfoT groupRow;
    HeapTupleBufT *tuples = NULL;
    StatusInter ret = ConstructHpPageGroupRowInfo(dynMemCtx, arg, &groupRow, &tuples);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    PageMgrT *pageMgr = (PageMgrT *)seIns->pageMgr;
    uint32_t pageId = SerializePageId(pageMgr, arg->pageHead->addr);

    if (rec->isFix) {
        HFPageHeadT *fixPageHead = (HFPageHeadT *)(void *)arg->pageHead;
        HeapFixPageInsertBatchTuples(rec->rollPtr, rec->trxId, pageId, fixPageHead, &groupRow);
    } else {
        HVPageHeadT *varPageHead = (HVPageHeadT *)(void *)arg->pageHead;
        HeapVarPageInsertBatchTuples(rec->slotExtendSize, rec->rollPtr, rec->trxId, pageId, varPageHead, &groupRow);
    }
    DbDynMemCtxFree(dynMemCtx, tuples);
    DbDynMemCtxFree(dynMemCtx, groupRow.batchOut);
    DbDynMemCtxFree(dynMemCtx, groupRow.newRowInfo);
    return STATUS_OK_INTER;
}

StatusInter HeapInsertOrUpdateForLobReplay(RedoReplayArgsT *arg)
{
    HeapRedoAllocLobRowT *rec = (HeapRedoAllocLobRowT *)(void *)arg->data;
    HVPageHeadT *pageHead = (HVPageHeadT *)arg->pageHead;
    // 恢复页头
    pageHead->freePosEnd = rec->freePosEnd;
    pageHead->slotDirEnd = rec->slotDirEnd;
    pageHead->freeSlotCnt = rec->freeSlotCnt;
    pageHead->slotCnt = rec->slotCnt;
    pageHead->freeSlotHead = rec->freeSlotHead;
    pageHead->baseHead.pageHead.entryUsedNum = rec->entryUsedNum;
    pageHead->baseHead.pageHead.freeSize = rec->freeSize;
    pageHead->baseHead.pageHead.endPos = rec->endPos;
    pageHead->baseHead.pageHead.pageState = rec->pageState;
    pageHead->maxModifyRowOffset = rec->maxModifyRowOffset;

    HpRowSlotT *slot = (HpRowSlotT *)((uint8_t *)pageHead + rec->slotOffset);
    *slot = rec->slot;  // 恢复slot

    // 恢复整行
    uint8_t *rowHead = (uint8_t *)pageHead + PAGE_GET_ROW_REAL_OFFSET(slot->offset);
    errno_t err = memcpy_s(rowHead, rec->dataSize, (uint8_t *)(rec + 1), rec->dataSize);
    if (err != EOK) {
        SE_ERROR(MEMORY_OPERATE_FAILED_INTER, "copy row head, offset:%" PRIu16, slot->offset);
        return MEMORY_OPERATE_FAILED_INTER;
    }

    return STATUS_OK_INTER;
}

StatusInter HeapBindDstRowReplay(RedoReplayArgsT *arg)
{
    HeapRedoBindDstRowT *log = (HeapRedoBindDstRowT *)arg->data;
    PageHeadT *pageHead = arg->pageHead;
    pageHead->freeSize = log->freeSize;
    HpLinkRowHeadT *rowHead = (HpLinkRowHeadT *)((uint8_t *)pageHead + log->rowOffset);
    *rowHead = log->rowHead;
    return STATUS_OK_INTER;
}

StatusInter HeapBindSliceSubRowToDirRowReplay(RedoReplayArgsT *arg)
{
    HeapRedoBindSliceSubRowT *rec = (HeapRedoBindSliceSubRowT *)arg->data;
    uint32_t slotId = rec->dirRowSlotId;
    uint64_t offset = (uint64_t)sizeof(HVPageHeadT) + (slotId * sizeof(HpRowSlotT));
    HpRowSlotT *slot = (HpRowSlotT *)(void *)((uint8_t *)arg->pageHead + offset);
    HpLinkRowHeadT *rowHead = (HpLinkRowHeadT *)HeapVarPageGetRowStateImpl((HVPageHeadT *)(void *)arg->pageHead, slot);
    HpSliceRowDirT *dirRow = (HpSliceRowDirT *)(void *)(rowHead + 1);
    dirRow->sliceRowNum = rec->sliceRowNum;
    dirRow->totalSize = rec->totalSize;
    dirRow->sliceRowEntries[dirRow->sliceRowNum - 1].sliceSubRowPtr = rec->subRowPtr;
    return STATUS_OK_INTER;
}

StatusInter HeapUpdateDirRowNextListPtrReplay(RedoReplayArgsT *arg)
{
    HeapRedoUpdateListPtrT *log = (HeapRedoUpdateListPtrT *)(void *)arg->data;
    HVPageHeadT *varPageHead = (HVPageHeadT *)arg->pageHead;
    HpLinkRowHeadT *sliceDirRowHead = (HpLinkRowHeadT *)HeapVarPageGetRowStateBySlotId(varPageHead, log->slotId);
    HpSliceRowDirT *dirRow = (HpSliceRowDirT *)(void *)(sliceDirRowHead + 1);
    dirRow->nextItemPtr = log->listRowAddr;
    return STATUS_OK_INTER;
}

StatusInter HeapUpdateDirRowPrevListPtrReplay(RedoReplayArgsT *arg)
{
    HeapRedoUpdateListPtrT *log = (HeapRedoUpdateListPtrT *)(void *)arg->data;
    HVPageHeadT *varPageHead = (HVPageHeadT *)arg->pageHead;
    HpLinkRowHeadT *sliceDirRowHead = (HpLinkRowHeadT *)HeapVarPageGetRowStateBySlotId(varPageHead, log->slotId);
    HpSliceRowDirT *dirRow = (HpSliceRowDirT *)(void *)(sliceDirRowHead + 1);
    dirRow->prevItemPtr = log->listRowAddr;
    return STATUS_OK_INTER;
}

StatusInter HeapDeleteSubRowReplay(RedoReplayArgsT *arg)
{
    HeapRedoDelSubRowT *log = (HeapRedoDelSubRowT *)arg->data;
    HpSliceRowDirT *dirRow = (HpSliceRowDirT *)((uint8_t *)arg->pageHead + log->rowOffset);
    dirRow->sliceRowNum = log->sliceRowNum;
    dirRow->totalSize = log->totalSize;
    return STATUS_OK_INTER;
}

StatusInter HeapReplayFuncRegister(SeInstanceT *seIns)
{
    REG_REPLAY_FUNC(seIns, REDO_OP_INSERT, HeapInsertReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_REPLACE_INSERT, HeapInsertReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_UPDATE, HeapUpdateReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_UPDATE_EDGE, HeapUpdateReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_UPDATE_LINKSRC, HeapUpdateReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_REPLACE_UPDATE, HeapUpdateReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_DELETE, HeapDeleteReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_MARK_DELETE, HeapDeleteReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_HEAP_CREATE, HeapCreateReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_FIX_PAGE_INIT, HeapFixPageInitReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_SCHEMA_CHANGE, HeapSchemaChangeReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_VAR_PAGE_INIT, HeapVarPageInitReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_PAGE_COMPRESS, HeapFullPageReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_ROLLBACK, HeapRollbackReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_BATCH_INSERT, HeapBatchInsertReplay);
    REG_REPLAY_FUNC(seIns, REDO_HEAP_ALLOC_LOB_ROW, HeapInsertOrUpdateForLobReplay);
    REG_REPLAY_FUNC(seIns, REDO_HEAP_BIND_DST_ROW, HeapBindDstRowReplay);
    REG_REPLAY_FUNC(seIns, REDO_HEAP_BIND_SUB_ROW, HeapBindSliceSubRowToDirRowReplay);
    REG_REPLAY_FUNC(seIns, REDO_HEAP_UPDATE_DIR_NEXT_PTR, HeapUpdateDirRowNextListPtrReplay);
    REG_REPLAY_FUNC(seIns, REDO_HEAP_UPDATE_DIR_PREV_PTR, HeapUpdateDirRowPrevListPtrReplay);
    REG_REPLAY_FUNC(seIns, REDO_HEAP_DIR_ROW_UPDATE_SUB_NUM, HeapDeleteSubRowReplay);
    return STATUS_OK_INTER;
}
