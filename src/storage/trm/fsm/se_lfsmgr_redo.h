/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: lfs redo接口头文件，属于base中的lfs模块
 * Create: 2023-7-27
 */
#ifndef SE_LFSMGR_REDO_H
#define SE_LFSMGR_REDO_H

#include "db_internal_error.h"
#include "se_lfsmgr.h"
#include "se_lfsmgr_redo_am.h"
#include "se_redo.h"

#ifdef __cplusplus
extern "C" {
#endif

extern FsmRedoAmFunc g_lfsRedoAm;
static inline FsmRedoAmFunc *FsmGetRedoAmFunc(void)
{
    return &g_lfsRedoAm;
}

// 非持久化表的fsm不记录日志
static inline void FsmRedoLogBegin(RedoRunCtxT *redoCtx, bool isPersistent)
{
    if (!isPersistent) {
        return;
    }
    RedoLogBegin(redoCtx);
}

static inline StatusInter FsmRedoLogEnd(RedoRunCtxT *redoCtx, bool isPersistent)
{
    if (!isPersistent) {
        return STATUS_OK_INTER;
    }
    return RedoLogEnd(redoCtx, true);
}

static inline void LfsRedoForMgrInit(LfsMgrT *mgr)
{
    FsmRedoAmFunc *fsmRedoAm = FsmGetRedoAmFunc();
    if (fsmRedoAm->initMgrFunc != NULL && mgr->cfgInfo.isPersistent) {
        fsmRedoAm->initMgrFunc(mgr);
    }
}

static inline void LfsRedoForMgrFragment(LfsMgrT *mgr)
{
    FsmRedoAmFunc *fsmRedoAm = FsmGetRedoAmFunc();
    if (fsmRedoAm->fragmentMgrFunc != NULL && mgr->cfgInfo.isPersistent) {
        fsmRedoAm->fragmentMgrFunc(mgr);
    }
}

static inline void LfsRedoForSlot(
    PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t fsmPageIdx, uint32_t slotId, FsmListNodeT *slot)
{
    FsmRedoAmFunc *fsmRedoAm = FsmGetRedoAmFunc();
    if (fsmRedoAm->updateSlotFunc != NULL && mgr->cfgInfo.isPersistent) {
        fsmRedoAm->updateSlotFunc(pageMgr, mgr, fsmPageIdx, slotId, slot);
    }
}

static inline void LfsRedoForMgrPageCnt(LfsMgrT *mgr)
{
    FsmRedoAmFunc *fsmRedoAm = FsmGetRedoAmFunc();
    if (fsmRedoAm->updateMgrPageCntFunc != NULL && mgr->cfgInfo.isPersistent) {
        fsmRedoAm->updateMgrPageCntFunc(mgr);
    }
}

static inline StatusInter LfsRedoForGetBlockByExistSlot(PageMgrT *pageMgr, LfsMgrT *mgr,
    FsmUpdatedSlotInfoT *unlinkedSlotInfo, bool reUseUpperPage, PageIdT dataPageId, uint32_t slotIdx)
{
    FsmRedoAmFunc *fsmRedoAm = FsmGetRedoAmFunc();
    if (fsmRedoAm->getBlockByExistSlotFunc != NULL && mgr->cfgInfo.isPersistent) {
        return fsmRedoAm->getBlockByExistSlotFunc(pageMgr, mgr, unlinkedSlotInfo, reUseUpperPage, dataPageId, slotIdx);
    }
    return STATUS_OK_INTER;
}

static inline void LfsRedoForExtentFsmSlot(PageMgrT *pageMgr, LfsMgrT *mgr, bool allocNewFsmPage,
    FsmSlotParaT *slotPara, PageAddrT addr, FsmPageHeadT *fsmPageHead)
{
    FsmRedoAmFunc *fsmRedoAm = FsmGetRedoAmFunc();
    if (fsmRedoAm->getBlockByExtentFsmSlotFunc != NULL && mgr->cfgInfo.isPersistent) {
        fsmRedoAm->getBlockByExtentFsmSlotFunc(pageMgr, mgr, allocNewFsmPage, slotPara, addr, fsmPageHead);
    }
}

static inline StatusInter LfsRedoForSetBlockFreeSpace(PageMgrT *pageMgr, LfsMgrT *mgr, FsmUpdatedSlotInfoT *targetSlot,
    uint32_t newListId, bool relink, FsmListT *oldLinkFsmList)
{
    FsmRedoAmFunc *fsmRedoAm = FsmGetRedoAmFunc();
    if (fsmRedoAm->setBlockFreeSpaceFunc != NULL && mgr->cfgInfo.isPersistent) {
        return fsmRedoAm->setBlockFreeSpaceFunc(pageMgr, mgr, targetSlot, newListId, relink, oldLinkFsmList);
    }
    return STATUS_OK_INTER;
}

static inline void LfsRedoForReleaseUpperPage(
    PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t fsmPageIdx, uint32_t slotId, FsmListNodeT *slot)
{
    FsmRedoAmFunc *fsmRedoAm = FsmGetRedoAmFunc();
    if (fsmRedoAm->releaseUpperPageFunc != NULL && mgr->cfgInfo.isPersistent) {
        fsmRedoAm->releaseUpperPageFunc(pageMgr, mgr, fsmPageIdx, slotId, slot);
    }
}

static inline void LfsRedoModifyFsmPageTable(
    LfsMgrT *mgr, FsmPageType fsmPageType, PageIdT curLfsPageAddr, uint32_t pageIdx, PageIdT tableOrFsmPageId)
{
    FsmRedoAmFunc *fsmRedoAm = FsmGetRedoAmFunc();
    if (fsmRedoAm->modifyFsmPageTableFunc != NULL && mgr->cfgInfo.isPersistent) {
        fsmRedoAm->modifyFsmPageTableFunc(mgr, (uint32_t)fsmPageType, curLfsPageAddr, pageIdx, tableOrFsmPageId);
    }
}

static inline void LfsRedoForAllocNewPageTable(
    LfsMgrT *mgr, FsmPageType fsmPageType, PageIdT curLfsPageAddr, uint32_t tableSlotIdx, PageIdT tablePageId)
{
    FsmRedoAmFunc *fsmRedoAm = FsmGetRedoAmFunc();
    if (fsmRedoAm->allocNewPageTableFunc != NULL && mgr->cfgInfo.isPersistent) {
        fsmRedoAm->allocNewPageTableFunc(mgr, (uint32_t)fsmPageType, curLfsPageAddr, tableSlotIdx, tablePageId);
    }
}

#ifdef __cplusplus
}
#endif

#endif
