/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: header file for GMDB label free space manager
 * Author: SE Team
 * Create: 2020-8-10
 */

#ifndef SE_LFSMGR_H
#define SE_LFSMGR_H

#include "adpt_types.h"
#include "adpt_spinlock.h"
#include "db_shm_array.h"
#include "se_define.h"
#include "se_dfx.h"
#include "se_heap.h"
#include "se_memdata.h"
#include "se_capacity_def_inner.h"
#include "se_persistcap_fsm.h"

#ifdef __cplusplus
extern "C" {
#endif

/*
 * 变量解释：
 * table page 为存储多级页表的数据结构，其中每一个 slot 用于存储下一级页表页或者 fsm 页的 pageId
 * table page idx 表示该级多级页表在单表中的全局 id
 * fsm page idx 表示 fsm 页在单表中的全局 id
 * table slotId 指向一张多级页表中目标下一级页表或 fsm 页的 pageId 的存储位置
 * fsm page slotId 指向 fsm 页中目标数据页的 pageId 的存储位置
 */

#define SHMARRAY_LFS_CAPACITY 20  // every extent can record about 40k pageDescs i.e. 40k pages
#define FSM_INVALID_MAX_ROW_SIZE 0u

/*
 * 以下计算均为4K页场景下的，如果pageSize > 4KB，所能存储的数据量也就更多，可以满足单表最大存储1TB的场景
 * pageSize = 4K, maxSlotPerFsmPage = 199, maxRowRawSizeInPage =
 * 3800（3800来源：4K页条件下，参照函数HeapGetMaxRowRawSize） allFsmPageNum * maxSlotPerFsmPage * maxRowRawSizeInPage
 * = 1.04TB
 * 6 * 176.07GB = 1056.40GB
 * 176.07GB = (二级页存储fsm page数量：pageNumInLvFsmPage * maxSlotPerFsmTablePage) * maxSlotPerFsmPage *
 maxRowRawSizeInPage = (以4K页为例)500 * 500 * 199 * 3800 B
*/
#define MIN_FSM_PAGE_NUMS_IN_MGR 350u
#define MIN_PAGE_SIZE 4u  // unit: K

typedef enum FsmPageTypeInternal {
    FSM_PAGE_ID_IN_MGR = 0,
    FSM_PAGE_ID_IN_PAGE_TABLE,
    LV1_FSM_PAGE_TABLE_ID,
    LV2_FSM_PAGE_TABLE_ID
} FsmPageType;

#define LFS_INVALID_PAGE_IDX 0xFFFFFFFF  // 目前FSM页还达不到这么多，可以用来做无效值，只针对fsm的pageIdx
#define SLOT_IN_MGR_VIRTUAL_FSM_PAGE_IDX 0xFFFFFFFE  // 仅用于标记mgr中slot的idx
#define LFS_INVALID_SLOT_ID 0x1FFF
#define LFS_INVALID_LIST_ID 0x3F

typedef enum TagFsmListIdE {
    FSM_LIST_ID_0 = 0,  // 链表的范围是左闭右开的
    FSM_LIST_ID_1,
    FSM_LIST_ID_2,
    FSM_LIST_ID_3,
    FSM_LIST_ID_4,
    FSM_LIST_ID_5,
    FSM_LIST_ID_6,
    FSM_LIST_ID_MAX = FSM_LIST_ID_6
} FsmListIdE;

typedef struct BlockInfo {
    uint32_t freeSize;    // freeSize of the block
    uint32_t maxRowSize;  // maxRowSize of the block
    bool relink;          // whether to relink this block in FSM list
    bool leavePage;       // whether to leave the block
} BlockInfoT;

typedef struct FsmPageDesc {
    PageIdT pageAddr;  //  mapping from FSM page id to FSM page id
} FsmPageDescT;

typedef struct TagLfsMgrMemFieldT {
    uint32_t lastUpperPageId;
    uint8_t *lastUpperPageAddr;
    FsmListT fsmList[FSM_VAR_LIST_COUNT];
} LfsMgrMemFieldT;

inline static uint32_t LfsGetBlockId(PageHeadT *page)
{
    return ((FsmDataPageHeadT *)page)->slotIdx;
}
typedef struct TagPageAddrT {
    uint32_t blockId;
    uint32_t pageId;
} PageAddrT;

typedef struct PageAddrInfo {
    PageAddrT pageAddr;
    union {
        uint64_t padding;
        PageHeadT *pageHead;
    };
} PageAddrInfoT;

typedef struct FsmSlotPara {
    FsmListNodeT *slot;
    uint32_t fsmPageIdx;
    uint32_t slotId;
    DbInstanceHdT dbInstance;
} FsmSlotParaT;

typedef struct FsmUpdatedSlotInfo {
    FsmListNodeT oldSlot;  // 保存slot修改前的副本
    FsmSlotParaT slotPara;
} FsmUpdatedSlotInfoT;

typedef struct FsmBlockUpdatePara {
    uint32_t freeSize;
    uint32_t maxRowSize;
    bool releasePage;
} FsmBlockUpdateParaT;

inline static PageAddrInfoT EmptyPageAddrInfoT(void)
{
    return (PageAddrInfoT){{0}, {0}};
}

StatusInter LfsGetFsmPageId(PageMgrT *pageMgr, const LfsMgrT *mgr, uint32_t fsmPageIdx, PageIdT *pageId);
inline static uint16_t GetLv2TablePageIdx(
    uint32_t fsmPageIdNumsInMgr, uint32_t maxSlotPerFsmTablePage, uint32_t fsmPageIdx)
{
    DB_ASSERT(fsmPageIdx >= fsmPageIdNumsInMgr);
    uint16_t lv2TablePageIdx =
        (uint16_t)((fsmPageIdx - fsmPageIdNumsInMgr) / (maxSlotPerFsmTablePage * maxSlotPerFsmTablePage));
    if (lv2TablePageIdx >= MAX_LV2_FSM_PAGE_NUMS) {
        SE_LAST_ERROR(FATAL_ERROR_INTER, "(LFS) fsmPageIdx is overflow");
        DB_ASSERT(false);
    }
    return lv2TablePageIdx;
}

inline static uint16_t GetLv2PageTableSlotId(
    uint32_t fsmPageIdNumsInMgr, uint32_t maxSlotPerFsmTablePage, uint32_t fsmPageIdx)
{
    uint16_t lv2TablePageIdx = GetLv2TablePageIdx(fsmPageIdNumsInMgr, maxSlotPerFsmTablePage, fsmPageIdx);
    DB_ASSERT(fsmPageIdx >= fsmPageIdNumsInMgr);
    uint16_t lv2TableSlotId = (uint16_t)((fsmPageIdx - fsmPageIdNumsInMgr) / maxSlotPerFsmTablePage);
    if (lv2TablePageIdx > 0) {
        lv2TableSlotId = (uint16_t)((fsmPageIdx - fsmPageIdNumsInMgr) %
                                    (maxSlotPerFsmTablePage * maxSlotPerFsmTablePage) / maxSlotPerFsmTablePage);
    }
    return lv2TableSlotId;
}

inline static uint16_t GetLv1PageTableSlotId(
    uint32_t fsmPageIdNumsInMgr, uint32_t maxSlotPerFsmTablePage, uint32_t fsmPageIdx)
{
    uint16_t lv2TablePageIdx = GetLv2TablePageIdx(fsmPageIdNumsInMgr, maxSlotPerFsmTablePage, fsmPageIdx);
    DB_ASSERT(fsmPageIdx >= fsmPageIdNumsInMgr);
    uint16_t lv1TableSlotId = (uint16_t)((fsmPageIdx - fsmPageIdNumsInMgr) % maxSlotPerFsmTablePage);
    if (lv2TablePageIdx > 0) {
        lv1TableSlotId = (uint16_t)((fsmPageIdx - fsmPageIdNumsInMgr) %
                                    (maxSlotPerFsmTablePage * maxSlotPerFsmTablePage) % maxSlotPerFsmTablePage);
    }
    return lv1TableSlotId;
}

/**
 * @brief Create labelFreeSpaceManager
 * @param mgr  pointer to labelFreeSpaceManager
 * @param cfg  pointer to labelFreeSpace Config
 */
SO_EXPORT void LfsMgrInit(LfsMgrT *mgr, const LfsCfgT *cfg);

/**
 * @brief reset labelFreeSpaceManager, free all fsm pages and reset meta info of labelFreeSpaceManager
 * @param mgr  pointer to labelFreeSpaceManager
 */
StatusInter LfsMgrReset(PageMgrT *pageMgr, PageIdT fsmMgrPageId, uint32_t offset);

/**
 * @brief destroy labelFreeSpaceManager
 * @param mgr  pointer to labelFreeSpaceManager
 * @return int32_t destroy successful or unsucce
 */
StatusInter LfsMgrDestroy(PageMgrT *pageMgr, PageIdT fsmMgrPageId, uint32_t offset);

typedef struct TagFsmRunCtxCfg {
    PageMgrT *pageMgr;
    PageIdT fsmAddr;
    uint32_t offset;
    bool directAccess;
    bool enableCache;
    bool enableLatch;  // 和 LfsCfgT.needLock 含义一致
    bool enableRedo;
} FsmRunCtxCfgT;

struct TagFsmRunCtx {
    SeRunCtxT *seRunCtx;
    PageMgrT *pageMgr;
    LfsMgrT *fsmMgrPtr;
    PageIdT fsmMgrAddr;
    uint32_t offset;
    bool enableCache;
    bool enableRedo;
    bool enableLatch;
    bool directAccess;
};

typedef struct TagFsmRunCtx FsmRunCtxT;

SO_EXPORT StatusInter FsmAllocRunCtx(SeRunCtxT *seRunCtx, FsmRunCtxT **fsmRunCtx);

SO_EXPORT StatusInter FsmInitRunCtx(const FsmRunCtxCfgT *fsmCtxCfg, FsmRunCtxT *fsmRunCtx);

SO_EXPORT void FsmFreeRunCtx(FsmRunCtxT **fsmRunCtx);

/**
 * @brief search avail page this match requireSize in label
 *        would remove this page from FreeSpaceMapping
 *        [NOTE] need to relink this page in FSM list
 * @param mgr           pointer to labelFreeSpaceManager
 * @param requireSize   require size
 * @param useCachePage  whether to use cache page(直连写场景为false)
 * @param [out]isNewBlock tell whether is new block
 * @param [out]addrInfo page vir addr, page shmem addr and logic addr, not null
 * @return uint32_t of matched block
 */
StatusInter LfsGetAvailBlock(
    FsmRunCtxT *fsmRunCtx, uint32_t requireSize, bool *isNewBlock, PageAddrInfoT *addrInfo, DbInstanceHdT dbInstance);

/**
 * @brief search a max free size block from FreeSpaceMapping, if not found, return a new free block
 *        would remove this page from FreeSpaceMapping
 *        [NOTE] no need to relink this page in FSM list
 * @param mgr   pointer to labelFreeSpaceManager
 * @param useCachePage  whether to use cache page(直连写场景为false)
 * @param [out]isNewBlock tell whether is new block
 * @param [out]addrInfo page vir addr, page shmem addr and logic addr, not null
 * @return int32_t create successful or unsucce
 */
StatusInter LfsGetMaxFreeSizeBlock(
    FsmRunCtxT *fsmRunCtx, bool *isNewBlock, PageAddrInfoT *addrInfo, DbInstanceHdT dbInstance);

/**
 * @brief set freeSize of block in FreeSpaceMapping
 * @param mgr  pointer to labelFreeSpaceManager
 * @param block   ID of the block
 * @param freeSize freeSize of the block
 * @param maxRowSize maxRowSize of the block
 * @param relink whether to relink this block in FSM list
 * @param useCachePage  whether to use cache page(直连写场景为false)
 * @return int32_t update successful or unsucce
 */
SO_EXPORT StatusInter LfsSetBlockFreeSpace(FsmRunCtxT *fsmRunCtx, uint8_t *dataPage, BlockInfoT blockInfo);

/**
 * @brief try to get block addr
 *        will NOT remove block from FreeSpaceMapping List
 *        if this block is totally free and has been freed to memdata, throw error, instead of allocing again
 * @param mgr  pointer to labelFreeSpaceManager
 * @param block  ID of the block
 * @param addr[output]  address of this block
 * @return success or failure
 */
StatusInter LfsTryGetSpecificBlock(FsmRunCtxT *fsmRunCtx, BlockNumber blockId, PageAddrT *addr);

/**
 * @brief get block count of data page
 * @param mgr  pointer to the labelFreeSpaceManager
 * @return uint32_t block count of data page
 */
StatusInter LfsGetBlockCnt(FsmRunCtxT *fsmRunCtx, uint32_t *blockCnt);

/**
 * @brief 获取实际占用的 page 的数量
 * @param mgr  pointer to the labelFreeSpaceManager
 * @return uint32_t count of data page
 */
StatusInter LfsGetRealBlockCnt(FsmRunCtxT *fsmRunCtx, uint32_t *cnt);

/**
 * @brief get status information of FSM
 * @param mgr: pointer to the labelFreeSpaceManager
 * @param fsmStat : pointer to FsmStat
 * @return int32_t get successful or unsucce
 */
StatusInter LfsGetFsmStat(LfsMgrT *mgr, FsmStat *fsmStat);

/**
 * @brief get a block to defragment
 * @param mgr: pointer to the labelFreeSpaceManager
 * @param addr[out] : address of the block
 * @return int32_t get successful or unsucce
 */
StatusInter LfsGetFragmentedBlock(FsmRunCtxT *fsmRunCtx, PageAddrT *addr);

/**
 * @brief release all free block
 * @param mgr: pointer to the labelFreeSpaceManager
 * @param useCachePage  whether to use cache page(直连写场景为false)
 * @return int32_t get successful or unsucce
 */
StatusInter LfsReleaseAllFreeBlock(FsmRunCtxT *fsmRunCtx);

StatusInter HeapGetPageWithLoad(PageMgrT *mgr, PageIdT pageId, uint8_t **page);
void HeapLeavePageWithLoad(PageMgrT *mgr, PageIdT pageId);
StatusInter LfsLoadTableScanAllBlock(PageMgrT *pageMgr, PageIdT fsmAddr, uint32_t offset, LfsMgrT *lfsMgr);

// LfsFreeAllFsmPage
StatusInter LfsTableLoadScanAllFsmPage(PageMgrT *pageMgr, PageIdT fsmAddr, uint32_t offset);
/**
 * @brief release all block
 * @param mgr: pointer to the labelFreeSpaceManager
 * @return int32_t get successful or unsucce
 */
StatusInter LfsReleaseAllBlock(PageMgrT *pageMgr, PageIdT fsmMgrPageId, uint32_t offset);

void LfsMgrInitMemVars(LfsMgrT *mgr);
void LfsMgrResetMemVars(LfsMgrT *mgr, LfsMgrMemFieldT *memData);
void LfsMgrRestoreMemVars(LfsMgrT *mgr, LfsMgrMemFieldT *memData);
StatusInter LfsGetTotalFreeSizeForStat(LfsMgrT *fsmMgr, uint64_t *totalFreeSize, PageMgrT *pageMgr);
StatusInter LfsGetTotalFreeSize(FsmRunCtxT *fsmRunCtx, uint64_t *totalFreeSize);
bool LfsCheckIsExtent(PageMgrT *pageMgr, uint32_t *extent);

StatusInter LfsGetExtent(FsmRunCtxT *fsmRunCtx, PageAddrInfoT *addrArrInfos);
StatusInter LfsFreeExtent(FsmRunCtxT *fsmRunCtx, PageAddrInfoT *dataPages);

StatusInter FsmGetAvailSize(FsmRunCtxT *fsmRunCtx, uint32_t *size);
uint32_t FsmGetPageSize(FsmRunCtxT *fsmRunCtx);
StatusInter LfsGetFreeSizeInFsmPage(LfsMgrT *mgr, PageMgrT *pageMgr, uint32_t fsmPageIdx, uint64_t *freeSize);

// 抽离公共接口避免重复代码
static inline uint64_t LfsGetAllSlotDataFreeSize(LfsMgrT *fsmMgr)
{
    uint64_t freeSize = 0;
    uint32_t usedSlotCntInMgr = DB_MIN(fsmMgr->maxBlockCnt, FSM_SLOT_NUM_IN_MGR);
    for (uint32_t i = 0; i < usedSlotCntInMgr; i++) {
        if (fsmMgr->slotArr[i].dataPageId != SE_ALLOC_NEW_PAGE_ID) {
            freeSize += (uint64_t)fsmMgr->slotArr[i].freeSize;
        }
    }
    return freeSize;
}

#ifdef __cplusplus
}
#endif

#endif  // SE_LFSMGR_H
