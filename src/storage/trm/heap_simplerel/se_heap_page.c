/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: Heap page的接口实现
 * Author: panghaisheng
 * Create: 2020-09-01
 */

#include "se_heap_page.h"
#include "se_heap_mem_inner.h"
#include "se_heap_slice_row.h"
#include "se_heap_addr.h"
#include "db_memcpy.h"
#include "se_heap_hc_inner.h"

#include "se_heap_page_common.h"

#ifdef __cplusplus
extern "C" {
#endif

void HeapFixPageInitLinkDstRowImpl(HpLinkDstFixRowHead *rowHead, const HpPageAllocRowInfoT *allocRowInfo)
{
    HeapResetRowState(&rowHead->rowState);
    rowHead->rowState.isLinkDst = true;
    rowHead->magicNum = HEAP_ROW_MAGIC_NUM;
    rowHead->size = (PageSizeT)allocRowInfo->bufSize;
    HeapSetLinkRowAddr4FixLinkDst(rowHead, allocRowInfo->sliceRowInfo.linkSrcRowPtr);
    rowHead->trxId = allocRowInfo->newTrxInfo.trxId;
    // 此处因为性能原因使用GMDB项目自己实现的DbFastMemcpy而非memcpy_s，需要保证内存复制合法。
    DbFastMemcpy((uint8_t *)(rowHead + 1), allocRowInfo->bufSize, allocRowInfo->buf, allocRowInfo->bufSize);
}

void HeapVarPageInitSliceSubRowImpl(
    const HeapRunCtxT *ctx, HpSliceSubRowHeadT *rowHead, const HpPageAllocRowInfoT *allocRowInfo)
{
    DB_UNUSED(ctx);  // 保留后面做事务
    HeapResetRowState(&rowHead->rowState);
    rowHead->rowState.isSliceSub = true;
    rowHead->magicNum = HEAP_ROW_MAGIC_NUM;
    rowHead->sliceDirRowPtr = allocRowInfo->sliceRowInfo.sliceDirRowPtr;
    rowHead->sliceIdx = allocRowInfo->sliceRowInfo.curSliceRowIdx;
    rowHead->trxId = allocRowInfo->newTrxInfo.trxId;
    DB_ASSERT(allocRowInfo->bufSize < DB_MAX_UINT16);
    rowHead->size = (PageSizeT)allocRowInfo->bufSize;
    // 此处因为性能原因使用GMDB项目自己实现的DbFastMemcpy而非memcpy_s，需要保证内存复制合法。
    DbFastMemcpy((uint8_t *)(rowHead + 1), rowHead->size, allocRowInfo->buf, allocRowInfo->bufSize);
}

StatusInter HeapMdGetMemPageImpl(
    const HeapRunCtxT *ctx, const HpItemPointerT *itemPointer, PageHeadT **pageHead, void *recyArg, PageIdT *pageAddr)
{
    DB_POINTER3(ctx, itemPointer, pageHead);
    DB_ASSERT(((PageMgrT *)ctx->seRunCtx->pageMgr)->type == SE_MEMDATA);
    PageIdT deserializedPageId =
        MdDeserializePageId((MdMgrT *)ctx->seRunCtx->pageMgr, itemPointer->pageId, ctx->heapCfg.isUseRsm);
    StatusInter ret =
        MdGetPage(ctx->seRunCtx->pageMgr, deserializedPageId, (uint8_t **)pageHead, ENTER_PAGE_NORMAL, true);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "get page(%" PRIu32 "). labelId %" PRIu32 "", itemPointer->pageId, ctx->heapCfg.labelId);
    }
    *pageAddr = deserializedPageId;
    return ret;
}

static StatusInter HeapGetCachePage(HeapRunCtxT *ctx, PageHeadT **cachePageHead)
{
    DB_POINTER(cachePageHead);
    if (!ctx->runtimeInfo->cachePageAvail) {
        // 页缓存不可用为正常现象
        return STATUS_OK_INTER;
    }
    if (!ctx->seRunCtx->resSessionCtx.isDirectWrite && ctx->runtimeInfo->cachePageAddr.pageHead != NULL) {
        // C/S写且pageHead有效，直接使用
        *cachePageHead = ctx->runtimeInfo->cachePageAddr.pageHead;
        return STATUS_OK_INTER;
    }
    MdMgrT *md = (MdMgrT *)ctx->seRunCtx->pageMgr;
    PageAddrT pageAddr = ctx->runtimeInfo->cachePageAddr.pageAddr;
    PageHeadT *pageHead;
    PageIdT pageId = MdDeserializePageId(md, pageAddr.pageId, ctx->heapCfg.isUseRsm);
    StatusInter ret = MdGetPage(md, pageId, (uint8_t **)&pageHead, ENTER_PAGE_NORMAL, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get cachePage %" PRIu32, pageAddr.pageId);
        return ret;
    }
    *cachePageHead = pageHead;
    return STATUS_OK_INTER;
}

StatusInter HeapFsmGetPageBySize(HeapRunCtxT *ctx, PageSizeT requireSize, HeapPageAllocCtx *heapPageAllocCtx)
{
    DB_POINTER2(ctx, heapPageAllocCtx);
    LfsOpInfoT lfsOpInfo = {.mgr = ctx->fsmMgr,
        .md = ctx->seRunCtx->pageMgr,
        .rsmUndoRec = NULL,
        .useCachePage = !ctx->seRunCtx->resSessionCtx.isDirectWrite,
        .needReleasePage = false};
    StatusInter ret = STATUS_OK_INTER;
    if (ctx->hpControl.accessMethodType == HEAP_ACCESS_METHOD_NORMAL || HeapUseLiteTrxMethod(ctx)) {
        PageHeadT *cachePageHead = NULL;
        ret = HeapGetCachePage(ctx, &cachePageHead);
        /* 碎片整理线程是否可以使用这个缓存页？
         * 可以，碎片整理的策略是将最稀疏的页面上的数据搬迁到其他页上
         * 缓存页设置为不会作为搬出页，缓存页只能作为搬入页
         * 若本次搬迁可以写入缓存页，一样达到了整理的效果
         * 若不能，则整理算法保证了接下来必然能从FSM中获得一个可搬入页
         * 最坏情况下，这一页的整理效果可能不会达到完美情况，但是FSM中剩余的其他页能达到完美情况 */
        if (ret == STATUS_OK_INTER && cachePageHead != NULL) {
            if (ctx->hpControl.isFixPage && cachePageHead->freeSize >= requireSize) {
                // 缓存的页空间足够使用，将该页取下来给本次写使用
                heapPageAllocCtx->maxRowSize = ctx->runtimeInfo->cachePageMaxRowSize;
                heapPageAllocCtx->pageHeadPtr.pageHead = cachePageHead;
                heapPageAllocCtx->pageInfo = ctx->runtimeInfo->cachePageAddr.pageAddr;
                ctx->runtimeInfo->cachePageAddr.pageHead = NULL;
                ctx->runtimeInfo->cachePageAvail = false;
                return STATUS_OK_INTER;
            }
            /* 无法使用缓存页时，本次写从FSM中另申请页使用，并在归还时再考虑是否刷新缓存
             * 这样做的考虑是：1.保证每个页被一个插入者独占 2.并发情况下该缓存页也许还能给其他写者用 */
        }
    }

    PageAddrInfoT addrInfo = EmptyPageAddrInfoT();
    DbInstanceHdT dbInstance = ((SeInstanceT *)(ctx->seRunCtx->seIns))->dbInstance;
    ret = LfsGetAvailBlock(&lfsOpInfo, requireSize, &heapPageAllocCtx->isNewBlock, &addrInfo, dbInstance);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "get block, size %" PRIu16 " labelId %" PRIu32 "", requireSize, ctx->heapCfg.labelId);
        return ret;
    }

    heapPageAllocCtx->maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
    heapPageAllocCtx->pageInfo = addrInfo.pageAddr;
    heapPageAllocCtx->pageHeadPtr.pageHead = (PageHeadT *)addrInfo.pageHead;
    return STATUS_OK_INTER;
}

StatusInter HeapFsmGetMaxFreeSizeOrCachedPage(HeapRunCtxT *ctx, HeapPageAllocCtx *heapPageAllocCtx)
{
    DB_POINTER2(ctx, heapPageAllocCtx);
    StatusInter ret;
    if (ctx->hpControl.accessMethodType == HEAP_ACCESS_METHOD_NORMAL || HeapUseLiteTrxMethod(ctx)) {
        PageHeadT *cachePageHead = NULL;
        ret = HeapGetCachePage(ctx, &cachePageHead);
        if (ret == STATUS_OK_INTER && cachePageHead != NULL) {
            heapPageAllocCtx->maxRowSize = ctx->runtimeInfo->cachePageMaxRowSize;
            heapPageAllocCtx->pageHeadPtr.pageHead = cachePageHead;
            heapPageAllocCtx->pageInfo = ctx->runtimeInfo->cachePageAddr.pageAddr;
            ctx->runtimeInfo->cachePageAddr.pageHead = NULL;
            ctx->runtimeInfo->cachePageAvail = false;
            return STATUS_OK_INTER;
        }
    }
    LfsOpInfoT lfsOpInfo = {.mgr = ctx->fsmMgr,
        .md = ctx->seRunCtx->pageMgr,
        .rsmUndoRec = NULL,
        .useCachePage = !ctx->seRunCtx->resSessionCtx.isDirectWrite,
        .needReleasePage = false};
    PageAddrInfoT addrInfo = EmptyPageAddrInfoT();
    DbInstanceHdT dbInstance = ((SeInstanceT *)(ctx->seRunCtx->seIns))->dbInstance;
    ret = LfsGetMaxFreeSizeBlock(&lfsOpInfo, &heapPageAllocCtx->isNewBlock, &addrInfo, dbInstance);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "lfs get maxFreeSize block. labelId %" PRIu32 "", ctx->heapCfg.labelId);
        return ret;
    }

    heapPageAllocCtx->maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
    heapPageAllocCtx->pageInfo = addrInfo.pageAddr;
    heapPageAllocCtx->pageHeadPtr.pageHead = (PageHeadT *)addrInfo.pageHead;
    return STATUS_OK_INTER;
}

inline static StatusInter HeapPageReadFromPartUpdateUndo(
    HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo, UndoTupleCombineAddrT *undoTuple)
{
    // 部分更新支持RU/RC/SER, 只有RC会跑到这里, 不会在undoLog上进行修改（RR才有合并更新）
    // fetchRowInfo->rowTrxInfo目前用于读流程时: 获取可见版本; 在读取跳转行/分片行时进行校验
    // 此时已经获得可见版本了，后续需要读取完整的部分更新后的整行内容，所以此处不对fetchRowInfo->rowTrxInfo进行修改
    // 因此不获取PartUpdateUndoLog中的src行头信息，undoTuple中不保存src row的行头信息
    // 因为缺少行头信息，此处不校验行类型等，默认UndoLog中读出的内容是正确的
    fetchRowInfo->partDateFromUndo.partDataLen = undoTuple->partDataLen;
    fetchRowInfo->partDateFromUndo.offsetOfRawData = undoTuple->offsetOfRawData;
    fetchRowInfo->partDateFromUndo.partDataBuf = undoTuple->partData;
    fetchRowInfo->isReadFromPrtUpdUndo = true;
    fetchRowInfo->isReadFromUndo = true;
    return STATUS_OK_INTER;
}

ALWAYS_INLINE static StatusInter RowUndoGetVisibleTupleWhenSelfDeleted(HeapRunCtxT *ctx, const ReadViewT *readView,
    HpPageFetchRowInfo *fetchRowInfo, RowTrxInfoT *rowTrxInfo, UndoTupleCombineAddrT *undoTuple)
{
    DB_ASSERT(!fetchRowInfo->isToGetOldestVisibleBuf);  // 两个标记位不可能同时为true
    fetchRowInfo->isSelfDelBuf = true;
    HpPageFetchRowInfoInitState(fetchRowInfo);  // 重置fetch的row信息
    StatusInter ret = RowUndoGetVisibleTuple(ctx->seRunCtx->undoCtx, readView, rowTrxInfo->rollPtr, undoTuple, false);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "readUndo, pageId:%" PRIu32 ", slotId:%" PRIu32, fetchRowInfo->srcRowInfo.itemPtr.pageId,
            fetchRowInfo->srcRowInfo.itemPtr.slotId);
        return ret;
    } else if (undoTuple->tuple.len == 0 || undoTuple->tuple.data == NULL) {
        SE_LAST_ERROR(NO_DATA_HEAP_ITEM_NOT_EXIST, "readUndo, pageId: %" PRIu32 ", slotId: %" PRIu32,
            fetchRowInfo->srcRowInfo.itemPtr.pageId, fetchRowInfo->srcRowInfo.itemPtr.slotId);
        DB_ASSERT(false);  // 标记删除往前读，一定会有一个可见版本
        return NO_DATA_HEAP_ITEM_NOT_EXIST;
    }
    return ret;
}

StatusInter HeapPageReadUndoImpl(HeapRunCtxT *ctx, const ReadViewT *readView, HpPageFetchRowInfo *fetchRowInfo)
{
    DB_POINTER2(ctx, fetchRowInfo);
    UndoTupleCombineAddrT undoTuple = {0};
    HpPageFetchRowFlag oldFetchRowFlag;
    HpPageFetchRowInfoSaveState(fetchRowInfo, &oldFetchRowFlag);
    HpPageFetchRowInfoInitState(fetchRowInfo);  // 重置fetch的row信息
    StatusInter ret = RowUndoGetVisibleTuple(ctx->seRunCtx->undoCtx, readView, fetchRowInfo->rowTrxInfo.rollPtr,
        &undoTuple, fetchRowInfo->isToGetOldestVisibleBuf);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "readUndo, pageId:%" PRIu32 ", slotId:%" PRIu32, fetchRowInfo->srcRowInfo.itemPtr.pageId,
            fetchRowInfo->srcRowInfo.itemPtr.slotId);
        return ret;
    } else if ((undoTuple.tuple.len == 0 || undoTuple.tuple.data == NULL) && !undoTuple.isTargetPrtUpdUndo) {
        // 第一次插入的行，没有历史记录（事务1插入了没提交，其他事务开启扫描的情况）
        return NO_DATA_HEAP_ITEM_NOT_EXIST;
    } else if (undoTuple.isNextToMaster) {
        // isNextToMaster为true时，isTargetPrtUpdUndo肯定为true
        HpPageFetchRowInfoRestoreState(fetchRowInfo, &oldFetchRowFlag);
        return HeapPageReadFromPartUpdateUndo(ctx, fetchRowInfo, &undoTuple);
    }
    /* 从undo 里面获取到 历史版本的行, 重新更新一下获取到行对应的事务信息 */
    HeapRowInfoT *curRowInfo = fetchRowInfo->curRowInfo;
    curRowInfo->rowHeadPtr.rowState = (HpRowStateT *)undoTuple.tuple.data;
    if (curRowInfo->rowHeadPtr.rowState->isDeleted && !fetchRowInfo->isCanAccessMarkDelRow) {
        RowTrxInfoT rowTrxInfo = HeapRowGetTrxInfo(ctx, curRowInfo);
        if (fetchRowInfo->isCanAccessSelfDeletedBuf && RowGetTrxId(&rowTrxInfo) == readView->creatorTrxId) {
            // getDiff场景需要感知: 如果是本事务自己删除的，则可以读取，但标记删除的可能是其他事务的内容
            // 需要继续去版本链中读第二个可见版本
            ret = RowUndoGetVisibleTupleWhenSelfDeleted(ctx, readView, fetchRowInfo, &rowTrxInfo, &undoTuple);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
            curRowInfo = fetchRowInfo->curRowInfo;
            curRowInfo->rowHeadPtr.rowState = (HpRowStateT *)undoTuple.tuple.data;
        } else {
            return NO_DATA_HEAP_ITEM_NOT_EXIST;
        }
    }
    if (ctx->hpControl.isFixPage) {
        ret = HeapFixPageReadUndo(curRowInfo, fetchRowInfo);
    } else {
        ret = HeapVarPageReadUndo(curRowInfo, fetchRowInfo);
    }
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    if (undoTuple.isTargetPrtUpdUndo) {
        DB_ASSERT(!undoTuple.isNextToMaster);
        return HeapPageReadFromPartUpdateUndo(ctx, fetchRowInfo, &undoTuple);
    }
    // 流程校验看护，要么获取了buf，且bufSize小于undoTuple的长度，要么就是跳转行还没获取buf
    DB_ASSERT((fetchRowInfo->isGetBuf && fetchRowInfo->bufSize < undoTuple.tuple.len) || !fetchRowInfo->isGetBuf);
    fetchRowInfo->isReadFromUndo = true;
    return STATUS_OK_INTER;
}

void HeapDeleteRecoveryHCInfo(HeapRunCtxT *ctx, HeapRowInfoT *srcRowInfo)
{}

static StatusInter LfsSetBlockFreeSpaceExperimental(
    HeapRunCtxT *ctx, PageHeadT *pageBaseHead, bool isDeleteAllRow, uint32_t freeSize)
{
    bool isRelink = false;
    LfsOpInfoT lfsOpInfo = {.mgr = ctx->fsmMgr,
        .md = ctx->seRunCtx->pageMgr,
        .rsmUndoRec = ((TrxT *)(ctx->seRunCtx->trx))->liteTrx.rsmUndoRec,
        .useCachePage = !ctx->seRunCtx->resSessionCtx.isDirectWrite,
        .needReleasePage = ((SeInstanceT *)(ctx->seRunCtx->seIns))->seConfig.enableReleaseDevice,

        .dbInstance = ((SeInstanceT *)(ctx->seRunCtx->seIns))->dbInstance};
    if (ctx->hpControl.accessMethodType == HEAP_ACCESS_METHOD_NORMAL || HeapUseLiteTrxMethod(ctx)) {
        PageHeadT *cachePageHead = NULL;
        StatusInter ret = HeapGetCachePage(ctx, &cachePageHead);
        /* 若本次删除操作的是heap的缓存页，且页上所有记录都被清空，则把该缓存页归还给LFS, 否则只更新freeSize */
        if (isDeleteAllRow && ret == STATUS_OK_INTER && cachePageHead == pageBaseHead) {
            ctx->runtimeInfo->cachePageAvail = false;
            ctx->runtimeInfo->cachePageAddr = EmptyPageAddrInfoT();
            ctx->runtimeInfo->cachePageFreeSize = 0;
            ctx->runtimeInfo->cachePageMaxRowSize = FSM_INVALID_MAX_ROW_SIZE;
            isRelink = true;
        }
    }
    uint32_t freeSizeWithReserve = HeapGetFreeReserveSize(ctx, pageBaseHead, freeSize);
    return LfsSetBlockFreeSpace(
        &lfsOpInfo, (uint8_t *)pageBaseHead, freeSizeWithReserve, FSM_INVALID_MAX_ROW_SIZE, isRelink);
}

StatusInter HeapSetblockFreeSizeAfterDeleteImpl(HeapRunCtxT *ctx, HeapRowInfoT *delRowInfo)
{
    DB_POINTER2(ctx, delRowInfo);
    PageHeadT *pageBaseHead = delRowInfo->pageHeadPtr.pageHead;
    uint32_t freeSize = pageBaseHead->freeSize;
    bool isDeleteAllRow = false;
    // 如果所有slot都被删除，把该页作为完全空闲页归还给lfs
    // lfs下次再分配这个页的时候，无论页空间有没有被重新申请分配，都会当作新页处理
    if (!RowIsFixPageRow(delRowInfo->rowType)) {
        // 变长heap流程，释放所有缓存的slot，作为空闲页归还
        HVPageHeadT *pageHead = (HVPageHeadT *)delRowInfo->pageHeadPtr.varPageHead;
        DB_ASSERT(pageHead->slotCnt >= pageHead->freeSlotCnt);
        if (pageHead->slotCnt == pageHead->freeSlotCnt) {
            freeSize = ctx->fsmMgr->cfgInfo.availSize;
            isDeleteAllRow = true;
        }
    } else {
        HFPageHeadT *pageHead = (HFPageHeadT *)delRowInfo->pageHeadPtr.fixPageHead;
        if (pageHead->pageCfg.rowCnt == pageHead->freeSlotCnt) {
            // 全部删空的场景，预期freeSize的维护正确（pageReserveSize因为表结构升级造成的无法利用的部分）
            DB_ASSERT(freeSize == (ctx->fsmMgr->cfgInfo.pageSize -
                                      (pageHead->pageCfg.rowBegin + pageHead->pageCfg.pageReserveSize)));
            // 升降级流程，页的freeSize可能不等于Heap的配置的availSize
            PageSizeT oneRowSizeWithSlotExtend = HeapFixGetOneRowSizeWithSlotExtend(&pageHead->pageCfg);
            freeSize = oneRowSizeWithSlotExtend * pageHead->freeSlotCnt;
            isDeleteAllRow = true;
        } else if (pageHead->pageCfg.rawRowSize != ctx->heap->regularInfo.fixPageCfg.rawRowSize) {
            // 当前页的rowSize配置跟Heap配置不一样，freeSize设置为0，避免影响fsm分配
            HeapFixPageSetUpgNotSatisfy(ctx, pageHead);
            freeSize = 0;
        }
    }
    return LfsSetBlockFreeSpaceExperimental(ctx, pageBaseHead, isDeleteAllRow, freeSize);
}

StatusInter HeapPageDeleteRowImpl(HeapRunCtxT *ctx, HeapRowInfoT *delRowInfo)
{
    DB_POINTER2(ctx, delRowInfo);
    if (ctx->hpControl.isFixPage) {
        HeapFixPageDeleteRow(ctx, delRowInfo);
        // 物理删除, 检查升级
        (void)HeapFixPageUpgrade(ctx, delRowInfo->pageHeadPtr.fixPageHead);
    } else {
        HeapVarPageDeleteRow(ctx, delRowInfo);
    }
    // 下面更新 fsm 的信息， 此时应该还在heap的page latch里
    return HeapSetblockFreeSizeAfterDeleteImpl(ctx, delRowInfo);
}

HOT_FUN_READ StatusInter HeapFetchGetPageImpl(HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo)
{
    DB_POINTER2(ctx, fetchRowInfo);
    StatusInter ret;
    HeapRowInfoT *curRowInfo = fetchRowInfo->curRowInfo;
    if (ctx->pageCache.lastPageHeadPtr != NULL &&
        ctx->pageCache.lastPageLogicPtr.pageId == curRowInfo->itemPtr.pageId &&
        ctx->pageCache.cacheVersion == ctx->runtimeInfo->cacheVersion) {
        curRowInfo->pageHeadPtr.pageHead = ctx->pageCache.lastPageHeadPtr;
        curRowInfo->pageAddr = curRowInfo->pageHeadPtr.pageHead->addr;
        // 这里由于DQL和DML流程的并发，pageCache里的页可能会被释放，在释放页时会首先将pageHead->addr置为无效值
        // 非法的pageAddr如果不加以校验拦截，在后面的取页、加锁等流程使用会出问题
        if (SECUREC_UNLIKELY(curRowInfo->pageAddr.deviceId == SE_INVALID_DEVICE_ID ||
                             curRowInfo->pageAddr.blockId == SE_INVALID_BLOCK_ID)) {
            return NO_DATA_HEAP_PAGE_NOT_EXIST;
        }
    } else {
        ret = HeapMdGetMemPageImpl(
            ctx, &curRowInfo->itemPtr, &curRowInfo->pageHeadPtr.pageHead, NULL, &curRowInfo->pageAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "get page(%" PRIu32 ", %" PRIu32 ")", ctx->heapCfg.heapTrmId, curRowInfo->itemPtr.pageId);
            return ret;
        }
        ctx->pageCache.lastPageLogicPtr = curRowInfo->itemPtr;
        ctx->pageCache.lastPageHeadPtr = curRowInfo->pageHeadPtr.pageHead;
        ctx->pageCache.cacheVersion = ctx->runtimeInfo->cacheVersion;
        // 如果是扫描，那么此处应该触发了跨页，把扫描会用到的blockId置为无效
        ctx->pageCache.lastPageBlockId = SE_INVALID_BLOCK_ID;
    }
    if (ctx->pageCache.hcFirstPageHeadPtr == NULL) {
        ctx->pageCache.hcFirstPageHeadPtr = curRowInfo->pageHeadPtr.pageHead;
    }
    return HeapIsFreeLatchUnderLabel(ctx) ? HeapLabelLatchCheckPage(ctx, curRowInfo) :
                                            HeapPageAddLatch(ctx, curRowInfo);
}

HOT_FUN_READ StatusInter HeapFetchTryGetPageImpl(HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo)
{
    return HeapFetchGetPageImpl(ctx, fetchRowInfo);
}

void HeapReleasePageLiteTrx(HeapRunCtxT *ctx, HeapPageAllocCtx *heapPageAllocCtx, LfsOpInfoT *lfsOpInfo,
    PageSizeT maxRowSize, uint16_t freeSize)
{
    DB_POINTER3(ctx, heapPageAllocCtx, lfsOpInfo);
    uint32_t freeSizeWithReserve = 0;
    PageHeadT *cachePageHead = NULL;
    StatusInter ret = HeapGetCachePage(ctx, &cachePageHead);
    /* 若当前无缓存页，或缓存页的freeSize不如当前页多，则缓存当前页，否则释放当前页 */
    if (ret != STATUS_OK_INTER) {
        // 获取缓存页失败，不修改缓存页
        SE_ERROR(ret, "get cachePage, isUseRsm:%" PRIu32, (uint32_t)ctx->heapCfg.isUseRsm);
        freeSizeWithReserve = HeapGetFreeReserveSize(ctx, heapPageAllocCtx->pageHeadPtr.pageHead, freeSize);
        ret = LfsSetBlockFreeSpace(
            lfsOpInfo, (uint8_t *)heapPageAllocCtx->pageHeadPtr.pageHead, freeSizeWithReserve, maxRowSize, true);
        if (ret != STATUS_OK_INTER) {
            // 当前页归还FSM失败，内存无法使用
            SE_ERROR(ret, "release page, labelId:%" PRIu32, ctx->heapCfg.labelId);
        }
        return;
    } else if (cachePageHead != NULL) {
        if (cachePageHead->freeSize >= freeSize) {
            // 不修改cache页
            freeSizeWithReserve = HeapGetFreeReserveSize(ctx, heapPageAllocCtx->pageHeadPtr.pageHead, freeSize);
            ret = LfsSetBlockFreeSpace(
                lfsOpInfo, (uint8_t *)heapPageAllocCtx->pageHeadPtr.pageHead, freeSizeWithReserve, maxRowSize, true);
            if (ret != STATUS_OK_INTER) {
                // 当前页归还FSM失败，内存无法使用
                SE_ERROR(ret, "release page, labelId:%" PRIu32, ctx->heapCfg.labelId);
            }
            return;
        } else {
            // 释放cache页
            freeSizeWithReserve = HeapGetFreeReserveSize(ctx, cachePageHead, cachePageHead->freeSize);
            ret = LfsSetBlockFreeSpace(
                lfsOpInfo, (uint8_t *)cachePageHead, freeSizeWithReserve, ctx->runtimeInfo->cachePageMaxRowSize, true);
            if (ret != STATUS_OK_INTER) {
                // cache页归还FSM失败，内存无法使用
                SE_ERROR(ret, "release page, labelId:%" PRIu32, ctx->heapCfg.labelId);
            }
            // 缓存当前页
        }
    }
    // 缓存当前页
    if (!ctx->seRunCtx->resSessionCtx.isDirectWrite) {
        // pageHead只记录服务端缓存
        ctx->runtimeInfo->cachePageAddr.pageHead = heapPageAllocCtx->pageHeadPtr.pageHead;
    } else {
        // 客户端通过pageAddr获取缓存页，不能直接使用pageHead，这里直接将pageHead置空，防止误用
        ctx->runtimeInfo->cachePageAddr.pageHead = NULL;
    }
    ctx->runtimeInfo->cachePageAddr.pageAddr = heapPageAllocCtx->pageInfo;
    ctx->runtimeInfo->cachePageMaxRowSize = maxRowSize;
    ctx->runtimeInfo->cachePageAvail = true;
}

StatusInter HeapReleasePage(HeapRunCtxT *ctx, HeapPageAllocCtx *heapPageAllocCtx, PageSizeT rowSize, uint16_t freeSize)
{
    DB_POINTER2(ctx, heapPageAllocCtx);
    LfsOpInfoT lfsOpInfo = {.mgr = ctx->fsmMgr,
        .md = ctx->seRunCtx->pageMgr,
        .rsmUndoRec = ((TrxT *)(ctx->seRunCtx->trx))->liteTrx.rsmUndoRec,
        .useCachePage = !ctx->seRunCtx->resSessionCtx.isDirectWrite,
        .needReleasePage = false};
    PageSizeT maxRowSize = DB_MAX(rowSize, heapPageAllocCtx->maxRowSize);

    if (ctx->hpControl.accessMethodType == HEAP_ACCESS_METHOD_NORMAL || HeapUseLiteTrxMethod(ctx)) {
        HeapReleasePageLiteTrx(ctx, heapPageAllocCtx, &lfsOpInfo, maxRowSize, freeSize);
        return STATUS_OK_INTER;
    }
    uint32_t freeSizeWithReserve = HeapGetFreeReserveSize(ctx, heapPageAllocCtx->pageHeadPtr.pageHead, freeSize);
    StatusInter ret = LfsSetBlockFreeSpace(
        &lfsOpInfo, (uint8_t *)heapPageAllocCtx->pageHeadPtr.pageHead, freeSizeWithReserve, maxRowSize, true);
    if (ret != STATUS_OK_INTER) {
        // 当前页归还FSM失败，内存无法使用，内存态不处理该错误，不返回该错误码
        SE_ERROR(ret, "release page, labelId:%" PRIu32, ctx->heapCfg.labelId);
    }
    return STATUS_OK_INTER;
}

// 性能关键路径，使用强制inline有益于性能
StatusInter HeapInitAllocRowInfoImpl(
    const HeapRunCtxT *ctx, const uint8_t *buf, uint32_t bufSize, HpPageAllocRowInfoT *allocRowInfo)
{
    DB_POINTER3(ctx, buf, allocRowInfo);
    if (SECUREC_UNLIKELY(bufSize > ctx->heap->regularInfo.maxRowSize)) {
        SE_LAST_ERROR(PROGRAM_LIMIT_EXCEEDED_HEAP_MAX_ROW_SIZE,
            "bufSize:%" PRIu32 ", maxRowSize:%" PRIu32 ", labelID:%" PRIu32, bufSize, ctx->heap->regularInfo.maxRowSize,
            ctx->heapCfg.labelId);
        return PROGRAM_LIMIT_EXCEEDED_HEAP_MAX_ROW_SIZE;
    }
    allocRowInfo->buf = (const void *)buf;
    allocRowInfo->bufSize = bufSize;
    allocRowInfo->slotExtendSize = ctx->heapCfg.slotExtendSize;
    allocRowInfo->isUndoOperationOk = false;
    allocRowInfo->undoBypassType = UNDO_OP_NORMAL;
    return STATUS_OK_INTER;
}

#ifdef __cplusplus
}
#endif
