/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: 解耦 heap 与 事务
 * Author: baiyang
 * Create: 2021-05-20
 */

#include "se_heap_trx_acc.h"
#include "se_heap_trx_acc_inner.h"

#include "se_heap_addr.h"
#include "se_heap_slice_row.h"
#include "se_heap_mem_inner.h"
#include "se_heap_utils.h"
#include "se_log.h"
#include "se_undo.h"
#include "db_memcpy.h"
#include "se_heap_am_inner.h"

#ifdef __cplusplus
extern "C" {
#endif

void HeapAcquireRowLockForInsertFailedClear(HpRunHdlT heapRunHdl, HpPageRowOpInfoHdlT hpPageRowOpInfoHdl)
{
    StatusInter ret;
    HeapRunCtxT *ctx = heapRunHdl;
    HpPageRowOpInfoT *opInfo = hpPageRowOpInfoHdl;
    HpPageAllocRowInfoT *pageAllocRowInfo = opInfo->allocRowInfo;
    if (ctx->hpControl.isNeedRowLock) {
        SeLockMgrLockReleaseLastNewLock(
            ctx->seRunCtx);  // 写undo失败, 释放上次新加的锁; (如果是加锁失败, 调用也没有问题)
        ctx->pageCache.lastRowLockMode = SE_LOCK_MODE_MAX;
    }
    DB_ASSERT(pageAllocRowInfo->newRowInfo.isNeedCheckBlockIdAndPageState == false);  // 此处一定不是扫描场景
    if (opInfo->allocRowInfo->sliceRowInfo.isSliceRow) {
        HeapClearSliceRow4Failed(ctx, opInfo, false);
        SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
        return;
    }
    DB_ASSERT(ctx->hpControl.isPageReadOnly == false);
    // newRowInfo的isNeedCheckBlockId应该为false，itemBlockId不会使用，所以外部不设置也没有问题，若后续相关逻辑修改请注意
    ret = HeapPageAddLatch(ctx, &pageAllocRowInfo->newRowInfo);
    DB_ASSERT(ret == STATUS_OK_INTER);  // 当前是没有其他page, 单独加一个page, 一定不会失败
    ret = HeapPageDeleteRowImpl(ctx, &pageAllocRowInfo->newRowInfo);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "clear insert, labelId:%" PRIu32, ctx->heapCfg.labelId);
    }
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
}

StatusInter HeapUndoLogForInsert(
    HpRunHdlT heapRunHdl, uint32_t undoOpType, HpPageRowOpInfoHdlT hpPageRowOpInfoHdl, uint64_t *newRollPtr)
{
    DB_POINTER(heapRunHdl);
    StatusInter ret;
    HeapRunCtxT *ctx = heapRunHdl;
    UndoRowOpTypeE opType = (UndoRowOpTypeE)undoOpType;
    HpPageRowOpInfoT *opInfo = hpPageRowOpInfoHdl;
    HpPageAllocRowInfoT *pageAllocRowInfo = opInfo->allocRowInfo;
    // 记录undo
    UndoRowOpInfoT rowOpInfo = {0};
    rowOpInfo.isRetained = false;
    rowOpInfo.isDeleted = false;
    rowOpInfo.isPersistent = ctx->heapCfg.isPersistent;
    rowOpInfo.resType = TRX_RES_HEAP;
    rowOpInfo.labelType = HeapGetLabelTypeByTupleType(ctx->heapCfg.tupleType);
    rowOpInfo.opType = opType;
    rowOpInfo.rowTrxId = DB_INVALID_TRX_ID;
    rowOpInfo.rowRollPtr = UNDO_INVALID_ROLLPTR;
    rowOpInfo.labelId = ctx->heapCfg.labelId;
    rowOpInfo.rowId = *(uint64_t *)&pageAllocRowInfo->newRowInfo.itemPtr;
    rowOpInfo.rowSize = 0;
    rowOpInfo.rowBuf = NULL;
    ret = TrxUndoReportRowOperation(ctx->seRunCtx->undoCtx, (TrxT *)ctx->seRunCtx->trx, &rowOpInfo, newRollPtr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret,
            "insert record undo. {pageId: %" PRIu32 ", slotId: %" PRIu32 "}, labelId:%" PRIu32 ", trxId:%" PRIu64,
            pageAllocRowInfo->newRowInfo.itemPtr.pageId, pageAllocRowInfo->newRowInfo.itemPtr.slotId,
            ctx->heapCfg.labelId, ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
    }
    return ret;
}

static uint16_t GetRowHeadSizeForUndoLogPrtUpd(HpPageRowTypeE rowType)
{
    switch (rowType) {
        case HEAP_VAR_ROW_NORMAL_ROW:
            return sizeof(HpNormalRowHeadT);
        case HEAP_VAR_ROW_LINK_SRC_ROW:
            return sizeof(HpLinkRowHeadT);
        case HEAP_FIX_ROW_NORMAL_ROW:
            return sizeof(HpNormalFixRowHead);
        case HEAP_FIX_ROW_LINK_SRC_ROW:
            return sizeof(HpLinkSrcFixRowHead);
        default:
            DB_ASSERT(false);
            SE_LAST_ERROR(DATA_EXCEPTION_INTER, "inval. row type when undo partial update.");
            return 0;
    }
}

StatusInter HeapLiteUndoLogForUpdate(
    HpRunHdlT heapRunHdl, uint32_t undoOpType, HpPageRowOpInfoHdlT hpPageRowOpInfoHdl, uint64_t *newRollPtr)
{
    DB_POINTER2(heapRunHdl, hpPageRowOpInfoHdl);
    HeapRunCtxT *ctx = heapRunHdl;
    DB_ASSERT(undoOpType == TRX_OP_UPDATE || undoOpType == TRX_OP_UPDATE_PART ||
              (undoOpType == TRX_OP_DELETE && ctx->heapCfg.isUseRsm));
    return STATUS_OK_INTER;
}

StatusInter HeapUndoReportForUpdOrDel(
    HpRunHdlT heapRunHdl, UndoRowOpTypeE opType, HpPageRowOpInfoHdlT hpPageRowOpInfoHdl, uint64_t *newRollPtr)
{
    TrxT *trx = (TrxT *)heapRunHdl->seRunCtx->trx;
    HpPageFetchRowInfo *fetchRowInfo = hpPageRowOpInfoHdl->fetchRowInfo;
    UndoRowOpInfoT rowOpInfo = {0};
    rowOpInfo.isRetained = (trx->trx.base.isolationLevel == REPEATABLE_READ);
    rowOpInfo.isSkipUndoIndex = heapRunHdl->hpControl.isSkipUndoIndex;
    rowOpInfo.isPersistent = heapRunHdl->heapCfg.isPersistent;
    rowOpInfo.resType = TRX_RES_HEAP;
    rowOpInfo.labelType = HeapGetLabelTypeByTupleType(heapRunHdl->heapCfg.tupleType);
    rowOpInfo.opType = opType;
    rowOpInfo.labelId = heapRunHdl->heapCfg.labelId;
    rowOpInfo.rowTrxId = RowGetTrxId(&fetchRowInfo->rowTrxInfo);
    rowOpInfo.rowRollPtr = fetchRowInfo->rowTrxInfo.rollPtr;
    // undo 日志, 只记录首行的信息. 不记录跳转dst行/分片行的内容.
    HeapRowInfoT *srcRowInfo = &fetchRowInfo->srcRowInfo;
    rowOpInfo.rowId = *(uint64_t *)(void *)&srcRowInfo->itemPtr;
    rowOpInfo.savePointId = trx->trx.base.savePointId;
    rowOpInfo.isDeleted = srcRowInfo->rowHeadPtr.rowState->isDeleted;
    rowOpInfo.runCtx = (HpRunHdlT)heapRunHdl;
    if (SECUREC_LIKELY(opType != TRX_OP_UPDATE_PART)) {
        rowOpInfo.bufSize = (uint16_t)fetchRowInfo->bufSize;
        rowOpInfo.rowSize = HeapPageGetMinRowSize(srcRowInfo->rawRowSize, srcRowInfo->rowType);
        rowOpInfo.rowBuf = (uint8_t *)srcRowInfo->rowHeadPtr.rowState;
    } else {
        HpPageAllocRowInfoT *allocRowInfo = hpPageRowOpInfoHdl->allocRowInfo;
        rowOpInfo.srcRowHeadSize = GetRowHeadSizeForUndoLogPrtUpd(srcRowInfo->rowType);
        rowOpInfo.srcRowHead = (uint8_t *)srcRowInfo->rowHeadPtr.rowState;
        // rowSize记录（src行头+部分更新内容）的长度，即存到undo页的内容
        rowOpInfo.rowSize = (uint16_t)HEAP_CALC_ALIGN_SIZE(rowOpInfo.srcRowHeadSize + allocRowInfo->bufSize);
        // partDataBuf记录heap行的被更新的部分旧内容（可能是大对象的分片中的）
        rowOpInfo.bufSize = (uint16_t)allocRowInfo->bufSize;  // bufSize记录部分更新的长度
        rowOpInfo.partDataBuf = (uint8_t *)fetchRowInfo->buf + allocRowInfo->offsetOfRawData;
        rowOpInfo.offsetOfRawData = allocRowInfo->offsetOfRawData;
    }
    // 乐观事务计算Undo预留的内存，用于在版本链上的回滚
    if (TrxGetTrxType(trx) == OPTIMISTIC_TRX) {
        rowOpInfo.rollbackReserveSize =
            HeapPageGetMinRowSize(fetchRowInfo->srcRowInfo.rollbackReserveSize, fetchRowInfo->srcRowInfo.rowType);
    }
    StatusInter ret = TrxUndoReportRowOperation(heapRunHdl->seRunCtx->undoCtx, trx, &rowOpInfo, newRollPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "trx(%" PRIu64 ") heap opType %" PRIu32 " undo, labelId:%" PRIu32, trx->base.trxId,
            (uint32_t)opType, heapRunHdl->heapCfg.labelId);
    }
    // 真正更新或删除的数据为undo链新增物理addr. 非大对象:更新或删除原数据buf; 大对象:更新或删除前数据link src行
    heapRunHdl->compV1Info.realUpdateOrDelPtr = ((SeUndoCtxT *)heapRunHdl->seRunCtx->undoCtx)->newPtr;
    if (SECUREC_LIKELY(srcRowInfo->rowType == HEAP_FIX_ROW_NORMAL_ROW)) {
        heapRunHdl->compV1Info.masterPointer = rowOpInfo.rowBuf + sizeof(HpNormalFixRowHead);
    } else {
        heapRunHdl->compV1Info.masterPointer = rowOpInfo.rowBuf;
    }
    return ret;
}

StatusInter HeapUndoLogForUpdOrDel(
    HpRunHdlT heapRunHdl, uint32_t undoOpType, HpPageRowOpInfoHdlT hpPageRowOpInfoHdl, uint64_t *newRollPtr)
{
    DB_POINTER(heapRunHdl);
    TrxT *trx = (TrxT *)heapRunHdl->seRunCtx->trx;
    if (SECUREC_LIKELY(trx->base.state != TRX_STATE_ACTIVE)) {
        return STATUS_OK_INTER;
    }
    UndoRowOpTypeE opType = (UndoRowOpTypeE)undoOpType;
    DB_ASSERT(
        (opType == TRX_OP_UPDATE && heapRunHdl->hpControl.isSkipUndoIndex) || !heapRunHdl->hpControl.isSkipUndoIndex);
    HpPageFetchRowInfo *fetchRowInfo = hpPageRowOpInfoHdl->fetchRowInfo;
    if (trx->base.trxType == PESSIMISTIC_TRX) {
        // 满足以下4个条件，就可以节省一次undo记录
        // 1. masterVersion上的版本是本事务写的; 2.前一版本undoRec的savePointId与当前trx的savePointId相同;
        // 3. 执行的是边的update操作（为啥del操作不能优化？需要del的undo来处理isDeleted标记位，必须得记）
        // 普通的update也不能优化，索引的回滚需要依赖中间版本，不然索引entry会有残留
        // 4. 当前fetch的行必须是normal行（linkSrc行（包含大对象），都需要依赖undo去回收oldDstRow）
        if (RowGetTrxId(&fetchRowInfo->rowTrxInfo) == trx->base.trxId && opType == TRX_OP_UPDATE &&
            heapRunHdl->hpControl.isSkipUndoIndex && !fetchRowInfo->is2LinkDstRow) {
            bool isByPassToInsertUndo = false;
            bool isUndoByPass = RowUndoCheckSavePointId(heapRunHdl->seRunCtx->undoCtx, fetchRowInfo->rowTrxInfo.rollPtr,
                trx->trx.base.savePointId, &isByPassToInsertUndo);
            if (isUndoByPass) {
                *newRollPtr = fetchRowInfo->rowTrxInfo.rollPtr;
                // 合并undo记录，在乐观下会影响DFX统计，需要设置标记位外面特殊处理
                hpPageRowOpInfoHdl->allocRowInfo->undoBypassType =
                    isByPassToInsertUndo ? UNDO_OP_BYPASS_TO_INSERT_RECORD : UNDO_OP_BYPASS_TO_UPDATE_RECORD;
                return STATUS_OK_INTER;
            }
        }
    } else {
        HpTrxPrevVersionRowInfoT *prevVersionRowInfo = &fetchRowInfo->prevVersionRowInfo;
        // 满足以下3个条件，就可以节省一次undo记录
        // 1. 非本事务首次操作本行; 2.前一版本undoRec的savePointId与当前trx的savePointId相同;
        // 3. 执行的是update操作（为啥del操作不能优化？需要del的undo来处理isDeleted标记位，必须得记）
        // 普通的update优化了，需要去处理索引的中间版本，不然索引entry会有残留，以及linkSrc行（包含大对象）的oldDstRow
        if (prevVersionRowInfo->hasPrevVersion && opType == TRX_OP_UPDATE &&
            prevVersionRowInfo->prevSavePointId == trx->trx.base.savePointId) {
            hpPageRowOpInfoHdl->allocRowInfo->undoBypassType = prevVersionRowInfo->isInsertUndoLogType ?
                                                                   UNDO_OP_BYPASS_TO_INSERT_RECORD :
                                                                   UNDO_OP_BYPASS_TO_UPDATE_RECORD;
            *newRollPtr = prevVersionRowInfo->isOnMasterVersion ? fetchRowInfo->rowTrxInfo.rollPtr :
                                                                  prevVersionRowInfo->undoRollPtr;
            return STATUS_OK_INTER;
        }
    }
    // 正常记录undo
    return HeapUndoReportForUpdOrDel(heapRunHdl, opType, hpPageRowOpInfoHdl, newRollPtr);
}

void HeapClearRowMarkDelete(HpRunHdlT heapRunHdl, HpPageRowOpInfoHdlT hpPageRowOpInfoHdl)
{
    HeapRunCtxT *ctx = heapRunHdl;
    HpPageRowOpInfoT *opInfo = hpPageRowOpInfoHdl;
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    HpRowHeadPtrT rowHeadPtr = fetchRowInfo->srcRowInfo.rowHeadPtr;
    DB_ASSERT(rowHeadPtr.rowState->isDeleted || ctx->hpControl.isRsmRecovery);  // 有可能刚记完undo就掉电还没改标记位
    rowHeadPtr.rowState->isDeleted = ctx->rollBackInfo.oldIsDeleted;
    HpRowStateT *rowState = rowHeadPtr.rowState;
    RowTrxInfoT *trxInfo = NULL;
    if (ctx->hpControl.isFixPage) {
        if (HeapPageIsNormalRow(rowState)) {
            trxInfo = &rowHeadPtr.normalFixRowHead->trxInfo;
        } else if (HeapPageIsLinkSrcRow(rowState)) {
            trxInfo = &rowHeadPtr.linkSrcFixRowHead->trxInfo;
        } else {
            DB_ASSERT(false);  // 不可能走到该场景
        }
    } else if (HeapPageIsNormalRow(rowState)) {
        trxInfo = &rowHeadPtr.normalRowHead->trxInfo;
    } else if (HeapPageIsLinkSrcRow(rowState)) {
        trxInfo = &rowHeadPtr.linkRowHead->srcTrxInfo;
    } else {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "inv row type: %" PRId32 ", labelId:%" PRIu32,
            (int32_t)fetchRowInfo->srcRowInfo.rowType, ctx->heapCfg.labelId);
        DB_ASSERT(false);  // 不可能走到该场景
        return;
    }
    DB_ASSERT(trxInfo != NULL);  // 根据上文不可能为NULL
    trxInfo->rollPtr = ctx->rollBackInfo.oldRollPtr;
    RowSetTrxId(trxInfo, ctx->rollBackInfo.oldTrxId);
}

// heap容器针对不同隔离级别事务类型的钩子函数集合
// 在编码阶段初始化, 不涉及并发初始化, 赋值后只读, 不涉及并发读写
const HeapAmForTrxT g_gmdbHeapTrxAmForGroup[(uint32_t)HEAP_ACCESS_METHOD_NUM][(uint32_t)HEAP_TRX_AM_MAX_TYPE] = {
    // HEAP_ACCESS_METHOD_NORMAL
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_INSERT].setLockFunc = NULL,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_INSERT].acquireRowLockFunc = HeapAcquireRowLockForInsert,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_INSERT].logUndoFunc = HeapUndoLogForInsert,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_INSERT].saveTrxInfo = HeapPrepareTrxInfoForInsert,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_INSERT].recoverFunc = HeapAcquireRowLockForInsertFailedClear,

    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_DELETE].setLockFunc = HeapSetLockActionBeforeDelete,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_DELETE].acquireRowLockFunc = HeapAcquireRowLockOnFetch,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_DELETE].logUndoFunc = HeapUndoLogForUpdOrDel,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_DELETE].saveTrxInfo = HeapPrepareTrxInfoForUpdOrDel,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_DELETE].recoverFunc = NULL,

    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_MARK_DELETE].setLockFunc = HeapSetLockActionBeforeMarkDelete,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_MARK_DELETE].acquireRowLockFunc = HeapAcquireRowLockOnFetch,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_MARK_DELETE].logUndoFunc = HeapUndoLogForUpdOrDel,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_MARK_DELETE].saveTrxInfo = HeapPrepareTrxInfoForUpdOrDel,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_MARK_DELETE].recoverFunc = NULL,

    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].setLockFunc = HeapSetLockActionBeforeRollBackDelete,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].acquireRowLockFunc = HeapAcquireRowLockOnFetch,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].logUndoFunc = NULL,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].saveTrxInfo = HeapPrepareTrxInfoForUpdOrDel,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].recoverFunc = HeapClearRowMarkDelete,

    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_UPDATE].setLockFunc = HeapSetLockActionBeforeUpdate,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_UPDATE].acquireRowLockFunc = HeapAcquireRowLockOnFetch,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_UPDATE].logUndoFunc = HeapUndoLogForUpdOrDel,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_UPDATE].saveTrxInfo = HeapPrepareTrxInfoForUpdOrDel,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_UPDATE].recoverFunc = NULL,

    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_NORMALREAD].setLockFunc = HeapSetLockActionBeforeFetchRow,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_NORMALREAD].acquireRowLockFunc = HeapAcquireRowLockOnFetch,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_NORMALREAD].logUndoFunc = NULL,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_NORMALREAD].saveTrxInfo = NULL,
    [HEAP_ACCESS_METHOD_NORMAL][HEAP_TRX_AM_NORMALREAD].recoverFunc = NULL,

    // HEAP_ACCESS_METHOD_LITE(HEAP_TABLE_LOCK、HEAP_READ_UNCOMMIT)
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_INSERT].setLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_INSERT].acquireRowLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_INSERT].logUndoFunc = HeapLiteUndoLogForInsert,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_INSERT].saveTrxInfo = HeapLitePrepareTrxInfo,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_INSERT].recoverFunc = HeapAcquireRowLockForInsertFailedClear,

    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_DELETE].setLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_DELETE].acquireRowLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_DELETE].logUndoFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_DELETE].saveTrxInfo = NULL,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_DELETE].recoverFunc = NULL,

    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_MARK_DELETE].setLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_MARK_DELETE].acquireRowLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_MARK_DELETE].logUndoFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_MARK_DELETE].saveTrxInfo = NULL,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_MARK_DELETE].recoverFunc = NULL,

    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].setLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].acquireRowLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].logUndoFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].saveTrxInfo = NULL,
    // 保留内存场景，重启阶段恢复后台缩容线程的事务时，无法区分是否是后台事务，此处也进行赋值
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].recoverFunc = HeapClearRowMarkDelete,

    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_UPDATE].setLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_UPDATE].acquireRowLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_UPDATE].logUndoFunc = HeapLiteUndoLogForUpdate,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_UPDATE].saveTrxInfo = HeapLitePrepareTrxInfo,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_UPDATE].recoverFunc = NULL,

    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_NORMALREAD].setLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_NORMALREAD].acquireRowLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_NORMALREAD].logUndoFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_NORMALREAD].saveTrxInfo = NULL,
    [HEAP_ACCESS_METHOD_LITE][HEAP_TRX_AM_NORMALREAD].recoverFunc = NULL,

    // HEAP_ACCESS_METHOD_LITE_BG(HEAP_TABLE_LOCK、HEAP_READ_UNCOMMIT的后台线程，不上行锁，记正常undo)
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_INSERT].setLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_INSERT].acquireRowLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_INSERT].logUndoFunc = HeapUndoLogForInsert,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_INSERT].saveTrxInfo = HeapPrepareTrxInfoForInsert,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_INSERT].recoverFunc = HeapAcquireRowLockForInsertFailedClear,

    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_DELETE].setLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_DELETE].acquireRowLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_DELETE].logUndoFunc = HeapUndoLogForUpdOrDel,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_DELETE].saveTrxInfo = HeapPrepareTrxInfoForUpdOrDel,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_DELETE].recoverFunc = NULL,

    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_MARK_DELETE].setLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_MARK_DELETE].acquireRowLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_MARK_DELETE].logUndoFunc = HeapUndoLogForUpdOrDel,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_MARK_DELETE].saveTrxInfo = HeapPrepareTrxInfoForUpdOrDel,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_MARK_DELETE].recoverFunc = NULL,

    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].setLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].acquireRowLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].logUndoFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].saveTrxInfo = NULL,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].recoverFunc = HeapClearRowMarkDelete,

    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_UPDATE].setLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_UPDATE].acquireRowLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_UPDATE].logUndoFunc = HeapUndoLogForUpdOrDel,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_UPDATE].saveTrxInfo = HeapPrepareTrxInfoForUpdOrDel,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_UPDATE].recoverFunc = NULL,

    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_NORMALREAD].setLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_NORMALREAD].acquireRowLockFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_NORMALREAD].logUndoFunc = NULL,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_NORMALREAD].saveTrxInfo = NULL,
    [HEAP_ACCESS_METHOD_LITE_BG][HEAP_TRX_AM_NORMALREAD].recoverFunc = NULL,

    // HEAP_ACCESS_METHOD_RSM_LITE_BG(HEAP_TABLE_LOCK、HEAP_READ_UNCOMMIT的后台线程，不上行锁，rsm，记轻量化undo)
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_INSERT].setLockFunc = NULL,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_INSERT].acquireRowLockFunc = NULL,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_INSERT].logUndoFunc = HeapLiteUndoLogForInsert,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_INSERT].saveTrxInfo = HeapLitePrepareTrxInfo,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_INSERT].recoverFunc = HeapAcquireRowLockForInsertFailedClear,

    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_DELETE].setLockFunc = NULL,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_DELETE].acquireRowLockFunc = NULL,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_DELETE].logUndoFunc = NULL,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_DELETE].saveTrxInfo = HeapLitePrepareTrxInfo,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_DELETE].recoverFunc = NULL,

    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_MARK_DELETE].setLockFunc = NULL,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_MARK_DELETE].acquireRowLockFunc = NULL,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_MARK_DELETE].logUndoFunc = HeapLiteUndoLogForUpdate,  // 复用更新流程
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_MARK_DELETE].saveTrxInfo = HeapLitePrepareTrxInfo,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_MARK_DELETE].recoverFunc = NULL,

    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].setLockFunc = NULL,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].acquireRowLockFunc = NULL,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].logUndoFunc = NULL,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].saveTrxInfo = NULL,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_MARK_DELETE_ROLLBACK].recoverFunc = HeapClearRowMarkDelete,

    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_UPDATE].setLockFunc = NULL,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_UPDATE].acquireRowLockFunc = NULL,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_UPDATE].logUndoFunc = NULL,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_UPDATE].saveTrxInfo = NULL,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_UPDATE].recoverFunc = NULL,

    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_NORMALREAD].setLockFunc = NULL,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_NORMALREAD].acquireRowLockFunc = NULL,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_NORMALREAD].logUndoFunc = NULL,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_NORMALREAD].saveTrxInfo = NULL,
    [HEAP_ACCESS_METHOD_RSM_LITE_BG][HEAP_TRX_AM_NORMALREAD].recoverFunc = NULL};

#ifdef __cplusplus
}
#endif
