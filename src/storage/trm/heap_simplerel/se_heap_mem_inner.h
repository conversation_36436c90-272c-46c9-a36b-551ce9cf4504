/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: Heap 模块存储引擎的内部接口和结构体
 * Author: panghaisheng
 * Create: 2020-8-12
 */

#ifndef SE_HEAP_MEM_INNER_H
#define SE_HEAP_MEM_INNER_H

#include "se_heap_utils.h"

#ifdef __cplusplus
extern "C" {
#endif
#define HEAP_OPERATION_BLOCK_SLEEP_TIME 1000  // unit: us
#define HEAP_SCAN_CURSOR_TRY_ALLOC_TIME 10

static inline void HeapPageUpdatePageStat(HVPageHeadT *pageHead, HpRowStateT *rowHead)
{
    PageSizeT modifyRowOffset = (uint16_t)((uintptr_t)rowHead - (uintptr_t)pageHead);
    if (modifyRowOffset > pageHead->maxModifyRowOffset) {
        pageHead->maxModifyRowOffset = modifyRowOffset;
    }
}

inline static StatusInter HeapGetAndCheck(ShmemPtrT addr, HeapT **heap)
{
    *heap = (HeapT *)DbShmPtrToAddr(addr);
    if (SECUREC_UNLIKELY(*heap == NULL)) {
        SE_LAST_ERROR(
            INVALID_PARAMETER_VALUE_INTER, "heap, (segid: %" PRIu32 " offset: %" PRIu32 ")", addr.segId, addr.offset);
        return INVALID_PARAMETER_VALUE_INTER;
    }
    if (HeapIsWrongMagicNum(*heap)) {  // 检查该heap是否已被删除
        SE_LAST_ERROR(INT_ERR_HEAP_INVALID_PARAMETER, "heap magic %" PRIu32, (*heap)->magicNum);
        return INT_ERR_HEAP_INVALID_PARAMETER;
    } else {
        return STATUS_OK_INTER;
    }
}

#ifdef __cplusplus
}
#endif

#endif  // SE_HEAP_H
