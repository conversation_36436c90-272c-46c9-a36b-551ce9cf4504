/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Heap Fetch相关接口
 * Author: panghaisheng
 * Create: 2022-07-27
 */
#ifndef SE_HEAP_VAR_H
#define SE_HEAP_VAR_H

#include "se_heap_inner.h"

#ifdef __cplusplus
extern "C" {
#endif

void HeapVarPageInitNewBlock(HeapRunCtxT *ctx, HVPageHeadT *pageHead);

SO_EXPORT void HeapVarPageInitPage(HVPageHeadT *pageHead, uint16_t slotExtendSize);

SO_EXPORT_FOR_TS HpRowStateT *HeapVarPageGetRowState(HVPageHeadT *pageHead, const HpRowSlotT *itemSlot);

void HeapVarPageInitRowForInsertTuple(HpNormalRowHeadT *rowHead, const HeapTupleBufT *tuple, RowTrxInfoT txInfo);

SO_EXPORT_FOR_TS void HeapVarPageInitNormalRow(
    const HeapRunCtxT *ctx, HpNormalRowHeadT *rowHead, const HpPageAllocRowInfoT *allocRowInfo);

SO_EXPORT_FOR_TS void HeapVarPageInitLinkSrcRow(
    const HeapRunCtxT *ctx, HpLinkRowHeadT *rowHead, const HpPageAllocRowInfoT *allocRowInfo, bool isKeepRowReserve);

SO_EXPORT_FOR_TS void HeapVarPageInitLinkDstRow(
    const HeapRunCtxT *ctx, HpLinkRowHeadT *rowHead, const HpPageAllocRowInfoT *allocRowInfo);

StatusInter HeapAllocVarRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo);

StatusInter HeapAllocVarPageBySize(
    HeapRunCtxT *ctx, HeapPageAllocCtx *heapPageAllocCtx, PageSizeT requireSize, bool *isCompressed);

StatusInter HeapVarPageAllocRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo);

SO_EXPORT void HeapVarPageInsertSlot(
    HVPageHeadT *pageHead, PageSizeT storeSize, HeapRowInfoT *newRowInfo, bool *isNewAllocSlot);

void HeapVarPageFreeRow(HeapRowInfoT *delRowInfo, uint32_t fsmAvailSize);

SO_EXPORT void HeapVarPageDeleteRow(HeapRunCtxT *ctx, HeapRowInfoT *delRowInfo);

SO_EXPORT_FOR_TS StatusInter HeapDeleteLinkDstRow(
    HeapRunCtxT *ctx, HeapRowInfoT *linkDstRowInfo, bool isSliceRows, bool isKeepOtherPageLatch);

void HeapVarPageDeleteSlot(HVPageHeadT *pageHead, PageSizeT tupleStoreSize, HpRowSlotT *slot, PageSizeT slotOffset);

SO_EXPORT void HeapVarPageUpdateSlot(
    const HeapRunCtxT *ctx, HVPageHeadT *pageHead, HpRowSlotT *slot, PageSizeT oldStoreSize, PageSizeT newStoreSize);

StatusInter HeapUpdateVarRowWithOptimisticTrx(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo);

void HeapUpdateVarRowToLinkSrcSetInfo(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, bool isKeepRowReserve);

SO_EXPORT_FOR_TS StatusInter HeapVarPageGetSlotBySlotId(
    HVPageHeadT *pageHead, uint32_t rowId, PageSizeT *slotOffset, HpRowSlotT **itemSlot);

StatusInter HeapVarPageConfirmSize(
    HeapRunCtxT *ctx, HVPageHeadT *pageHead, PageSizeT requireSize, bool *isCompressed, bool *isSizeEnough);

StatusInter HeapVarPageCompressRow(HeapRunCtxT *ctx, HVPageHeadT *pageHead);

StatusInter HeapVarPageCompress(HeapRunCtxT *ctx, HVPageHeadT *pageHead);

StatusInter HeapVarRowFetch(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo);

StatusInter HeapVarRowRefetch(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo);

StatusInter HeapFetchVarRowInPage(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo);

StatusInter HeapFetchBufferForLinkDstRow(
    HeapRunCtxT *ctx, HpItemPointerT linkItemPtr, RowTrxIdT srcTrxId, HeapTupleBufT *tupleBuf);

inline static bool HeapPageIsLinkSrcRow(const HpRowStateT *rowState)
{
    return (rowState->isLinkSrc && !rowState->isLinkDst);
}

inline static bool HeapPageIsLinkDstRow(const HpRowStateT *rowState)
{
    return (!rowState->isLinkSrc && rowState->isLinkDst);
}

inline static bool HeapPageIsSliceSubRow(const HpRowStateT *rowState)
{
    return rowState->isSliceSub;
}

inline static PageSizeT HeapPageGetLinkSrcRowSize(void)
{
    return sizeof(HpLinkRowHeadT);
}

inline static HpRowStateT *HeapVarPageGetRowStateByRowOffset(HVPageHeadT *pageHead, PageSizeT realOffset)
{
    DB_POINTER(pageHead);
    return (HpRowStateT *)((uintptr_t)pageHead + realOffset);
}

inline static PageSizeT HeapVarPageGetNewRowOffset(const HVPageHeadT *pageHead, PageSizeT rowStoreSize)
{
    DB_ASSERT(pageHead->freePosEnd > rowStoreSize);
    return (PageSizeT)(pageHead->freePosEnd - rowStoreSize);
}

inline static void HeapVarPageSetRowOffset(HVPageHeadT *pageHead, HpRowSlotT *slot, PageSizeT rowStoreSize)
{
    DB_POINTER2(pageHead, slot);
    slot->isFree = false;
    PageSizeT rowOffset = HeapVarPageGetNewRowOffset(pageHead, rowStoreSize);
    slot->offset = PAGE_GET_ROW_LOGIC_OFFSET(rowOffset);
    DB_ASSERT(pageHead->freePosEnd != 0);
    pageHead->freePosEnd = rowOffset;
    HEAP_ASSERT_ALIGNED(pageHead->freePosEnd);
    DB_ASSERT(pageHead->freePosEnd >= pageHead->slotDirEnd);
}

inline static PageSizeT HeapVarPageGetContinueFreeSize(const HVPageHeadT *pageHead)
{
    DB_ASSERT(pageHead->freePosEnd >= pageHead->slotDirEnd);
    return (PageSizeT)(pageHead->freePosEnd - pageHead->slotDirEnd);
}

inline static PageSizeT HeapVarPageGetOneSlotSize(uint16_t slotExtendSize)
{
    return (PageSizeT)(sizeof(HpRowSlotT) + slotExtendSize);
}

// heap 的 var page 管理开销只有页头, 剩下的长度应该就是一行的最大长度(包含行头、slot、slotExtend, 一个page只存储一行)
inline static PageSizeT HeapVarPageGetMaxMgrInfoLen(uint16_t slotExtendSize)
{
    return (PageSizeT)(HEAP_ALIGN_SIZE_OF(HVPageHeadT));
}

// 将变长点heap的slot id换算成slot offset
inline static __attribute__((always_inline)) PageSizeT HeapVarPageGetSlotIdBySlotOffset(
    PageSizeT offset, uint16_t slotExtendSize)
{
    DB_ASSERT(offset >= HEAP_ALIGN_SIZE_OF(HVPageHeadT));
    PageSizeT logicSlotOffset = (uint16_t)(offset - HEAP_ALIGN_SIZE_OF(HVPageHeadT));
    PageSizeT oneSlotSize = HeapVarPageGetOneSlotSize(slotExtendSize);  // 保证了下面的除数必不为0
    DB_ASSERT(logicSlotOffset % oneSlotSize == 0);
    return logicSlotOffset / oneSlotSize;
}

inline static PageSizeT HeapVarPageExtendSlotDir(HVPageHeadT *pageHead)
{
    DB_ASSERT(pageHead->slotDirEnd != 0);
    PageSizeT oneSlotSize = HeapVarPageGetOneSlotSize(pageHead->slotExtendSize);
    pageHead->baseHead.pageHead.freeSize = (uint16_t)(pageHead->baseHead.pageHead.freeSize - oneSlotSize);
    pageHead->slotCnt++;
    PageSizeT newSlotOffset = pageHead->slotDirEnd;
    pageHead->slotDirEnd = (uint16_t)(pageHead->slotDirEnd + oneSlotSize);

    uint8_t *extendStart = (uint8_t *)((uintptr_t)pageHead + newSlotOffset + sizeof(HpRowSlotT));
    errno_t ret = memset_s(extendStart, pageHead->slotExtendSize, 0, pageHead->slotExtendSize);
    DB_ASSERT(ret == EOK);
    return newSlotOffset;
}

inline static void HeapSetFetchRowInfoWithLinkDstRow(HpPageFetchRowInfo *fetchRowInfo)
{
    HpLinkRowHeadT *linkRowHead = fetchRowInfo->curRowInfo->rowHeadPtr.linkRowHead;
    fetchRowInfo->curRowInfo->rawRowSize = linkRowHead->dstInfo.size;
    fetchRowInfo->isGetBuf = true;
    fetchRowInfo->bufSize = linkRowHead->dstInfo.size;
    fetchRowInfo->buf = linkRowHead + 1;
}

inline static void HeapSetFetchRowInfoWithNormalRow(HpPageFetchRowInfo *fetchRowInfo)
{
    HpNormalRowHeadT *normalRowHead = fetchRowInfo->curRowInfo->rowHeadPtr.normalRowHead;
    fetchRowInfo->curRowInfo->rawRowSize = normalRowHead->size;
    fetchRowInfo->curRowInfo->rollbackReserveSize = normalRowHead->rollbackReserveSize;
    fetchRowInfo->isGetBuf = true;
    fetchRowInfo->bufSize = normalRowHead->size;
    fetchRowInfo->buf = normalRowHead + 1;
    fetchRowInfo->srcRowInfo.isMarkDeleted = normalRowHead->rowState.isDeleted;
}

void HeapSetFetchRowInfoWithLinkSrcRow(HpPageFetchRowInfo *fetchRowInfo);

SO_EXPORT_FOR_TS StatusInter HeapVarPageReadPrevVersion(const HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo);

SO_EXPORT_FOR_TS void HeapVarPageInitLinkDstSliceDirRow(
    const HeapRunCtxT *ctx, HpLinkRowHeadT *rowHead, const HpPageAllocRowInfoT *allocRowInfo);

void HeapVarRowClearReserveSize(
    HeapRunCtxT *ctx, HpRowHeadPtrT rowHeadPtr, HVPageHeadT *pageHead, uint16_t oldFreeSize, bool isNeedMoveRsmUndo);

#ifdef __cplusplus
}
#endif

#endif  // SE_HEAP_FETCH_H
