/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: Extension of heap operation to process slice row
 * Author: panghaisheng
 * Create: 2021-10-15
 */

#ifndef SE_HEAP_SLICE_ROW_H
#define SE_HEAP_SLICE_ROW_H

#include "se_heap_inner.h"

#ifdef __cplusplus
extern "C" {
#endif

StatusInter HeapInsertSliceRows(HeapRunCtxT *ctx, HpPageRowOpInfoT *sliceRowOpInfo);

void HeapInsertFreeMemInNormalPro(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo);

void HeapClearSliceRow4Failed(HeapRunCtxT *ctx, HpPageRowOpInfoT *sliceDirRowOpInfo, bool isUpdateFlow);

SO_EXPORT_FOR_TS void HeapSliceRowInfoFreeDirMem(HeapRunCtxT *ctx, HpPageSliceRowInfoT *sliceRowInfo);

SO_EXPORT_FOR_TS StatusInter HeapFetchSliceRows(HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchDirRowInfo);

StatusInter HeapDeleteSliceDirAndSubRows(HeapRunCtxT *ctx, HeapRowInfoT *sliceDirRowInfo);

StatusInter HeapUpdateSliceRows(HeapRunCtxT *ctx, HpPageRowOpInfoT *sliceRowOpInfo);

StatusInter HeapUpdateAllocSliceRows(HeapRunCtxT *ctx, HpPageRowOpInfoT *sliceRowOpInfo);

StatusInter HeapRealDeleteForLob(HeapRunCtxT *ctx, HpItemPointerT firstDirRow, HpItemPointerT lastDirRow);

StatusInter HeapInsertForLob(HeapRunCtxT *ctx, const uint8_t *buf, uint32_t bufSize, HpItemPointerT *itemPtr);

StatusInter HeapUpdateForLob(HeapRunCtxT *ctx, const HeapTupleBufT *tupleBuf, HpPageRowOpInfoT *opInfo);

StatusInter HeapUpdateCommitOrRollbackCleanForLob(HeapRunCtxT *ctx, HpItemPointerT itemPtr);

StatusInter HeapUpdateCleanForLob(HeapRunCtxT *ctx, HpItemPointerT firstItemPtr, HpItemPointerT lastItemPtr);

StatusInter HeapUpdatePartialSliceSubRows(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo);

StatusInter HeapUpdatePartialFetchSliceRowBuf(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo);

inline static bool HeapFetchSliceRowIsNeedTry(const HpPageFetchRowInfo *fetchDirRowInfo)
{
    // 如果是由于搜索过程中, 失败, 应该重试. 其他比如内存申请失败, 内存破坏等情况, 不应该重试.
    return fetchDirRowInfo->sliceRowCursor.isSubRowSearchFailed;
}

inline static bool HeapIsTupleNeedSlice(const HeapRunCtxT *ctx, uint32_t bufSize)
{
    return bufSize > ctx->heapCfg.maxRowRawSizeInPage;
}

ALWAYS_INLINE static void HeapFetchClearSliceDir(HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchDirRowInfo)
{
    HpPageSliceRowCursorT *sliceRowCursor = &fetchDirRowInfo->sliceRowCursor;
    if (sliceRowCursor->sliceDir) {
        DbDynMemCtxFree(ctx->seRunCtx->sessionMemCtx, sliceRowCursor->sliceDir);
    }
    sliceRowCursor->sliceDir = NULL;
}

ALWAYS_INLINE static void HeapFetchClearTempSliceMem(HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchDirRowInfo)
{
    HeapFetchClearSliceDir(ctx, fetchDirRowInfo);
    if (fetchDirRowInfo->lobBuf) {
        DbDynMemCtxFree(ctx->usrMemCtx, fetchDirRowInfo->lobBuf);
    }
    fetchDirRowInfo->buf = NULL;
    fetchDirRowInfo->lobBuf = NULL;
}

void HeapDirRowGetPrevOrNextDirPtr(HpRowHeadPtrT rowHeadPtr, HpItemPointerT *prevPtr, HpItemPointerT *nextPtr);

inline static void HeapSetFetchRowInfoWithSliceSubRow(HpPageFetchRowInfo *fetchRowInfo)
{
    HpSliceSubRowHeadT *subRowHead = fetchRowInfo->curRowInfo->rowHeadPtr.sliceSubRowHead;
    fetchRowInfo->curRowInfo->rawRowSize = subRowHead->size;
    fetchRowInfo->isGetBuf = true;
    fetchRowInfo->bufSize = subRowHead->size;
    fetchRowInfo->buf = subRowHead + 1;
}

// sliceDir中存储的大小是该目录行所包含的分片行的总大小，而行头中存储的大小是大对象的总大小
// 在大对象只有一个目录行时，两者应该相等
// 在大对象有多个目录行时，行头中记录的大小应该大于sliceDir中记录的大小
inline static void HeapSetFetchRowInfoWithLinkDstSliceDirRow(HpPageFetchRowInfo *fetchRowInfo)
{
    fetchRowInfo->isSliceRows = true;
    fetchRowInfo->isGetBuf = true;
    HpLinkRowHeadT *linkRowHead = fetchRowInfo->curRowInfo->rowHeadPtr.linkRowHead;
    fetchRowInfo->curRowInfo->rawRowSize = linkRowHead->dstInfo.size;
    HpSliceRowDirT *sliceDir = (HpSliceRowDirT *)(linkRowHead + 1);
    fetchRowInfo->sliceRowCursor.totalSize = sliceDir->totalSize;
    fetchRowInfo->bufSize = linkRowHead->bufSize;
    DB_ASSERT(fetchRowInfo->bufSize >= sliceDir->totalSize);
    fetchRowInfo->buf = NULL;  // 真正的buf 需要继续解析获取
}

#ifdef __cplusplus
}
#endif

#endif  // SE_HEAP_SLICE_ROW_H
