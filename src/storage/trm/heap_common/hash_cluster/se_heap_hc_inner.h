/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2024. All rights reserved.
 * File Name: se_heap_hc_inner.h
 * Description: Heap interfaces for hash cluster
 * Author: linchunbo
 * Create: 2021-11-11
 */

#ifndef SE_HEAP_HC_INNER_H
#define SE_HEAP_HC_INNER_H

#include "se_heap_base.h"
#include "se_heap_hc.h"

#ifdef __cplusplus
extern "C" {
#endif

PageSizeT HeapGetHcSlotSpace(uint16_t hcIndexNum);

void HeapSetHeapLastPageHead(HeapRunCtxT *ctx, HpBatchOutT singleBatchOut);

void HeapHcSetIndexCache(HeapRunCtxT *ctx, uint8_t **bucketPageHeadPtr);

void HeapSetHcLastPageHead(HeapRunCtxT *ctx, HpBatchOutT singleBatchOut);

StatusInter HeapFetchRowHcPtr(HeapRunCtxT *ctx, HpTupleAddr tupleAddr, uint32_t hcIndex, HeapHcPtrT **hcPtr);

StatusInter HeapHcListPrefetch4Insert(
    HeapRunCtxT *ctx, HcInsertPrefetchCtxT *prefetchCtx, uint32_t hcIndex, TupleAddr oldHeadAddr);

StatusInter HeapHcListPrefetch4Delete(HeapRunCtxT *ctx, HcDeletePrefetchCtxT *prefetchCtx, uint32_t hcIndex);

void HeapHcListDeleteWithPrefetch(
    HeapRunCtxT *ctx, HcDeletePrefetchCtxT *prefetchCtx, uint32_t hcIndex, HpHcDeleteInfoT *deleteInfo);

void HeapHcListInsertWithPrefetch(
    HeapRunCtxT *ctx, HcInsertPrefetchCtxT *prefetchCtx, uint32_t hcIndex, HpTupleAddr oldHeadAddr);

StatusInter HeapHcListInsert(
    HeapRunCtxT *ctx, const HpTupleAddr insertTupleAddr, const HpTupleAddr headTupleAddr, uint32_t hcIndex);

StatusInter HeapHcListInsertCheck(HeapRunCtxT *ctx, const HpTupleAddr insertTupleAddr, uint32_t hcIndex);

StatusInter HeapHcListDelete(
    HeapRunCtxT *ctx, const HpTupleAddr deleteTupleAddr, uint32_t hcIndex, HpHcDeleteInfoT *deleteInfo);

StatusInter HeapHcListGetKeyCount(
    HpRunHdlT heapRunHdl, HpTupleAddr currTupleAddr, uint32_t hcIndex, HpHcScanCtxT *scanCtx, uint64_t *count);

StatusInter HeapHcIterDumpAddrs(HeapRunCtxT *ctx, HpTupleAddr startAddr, uint32_t hcIndex, HashClusterIterT *hcIter);

void HeapHcPageSortReset(HeapRunCtxT *ctx);

void HeapHcRecoveryHcPtr(HeapRunCtxT *ctx, HeapHcPtrT **hcPtr);  // 只定义于HEAP_MEM

// se_heap_hc.c合一现阶段新增接口，不应被不在se_heap_hc的函数调用
SO_EXPORT_FOR_TS StatusInter HeapHcGetPage(HeapRunCtxT *ctx, HpItemPointerT itemPtr, PageHeadT **pageHead);

#ifdef __cplusplus
}
#endif

#endif
