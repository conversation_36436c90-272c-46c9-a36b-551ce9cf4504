/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Heap 碎片整理统计与任务添加
 * Author: SE Team
 * Create: 2022-03-31
 */
#include "se_heap_stats.h"
#include "se_log.h"
#include "se_daemon.h"
#include "se_subs_status_merge.h"
#include "dm_meta_kv_label.h"
#include "dm_data_prop_seri.h"
#include "db_table_space.h"

#ifdef __cplusplus
extern "C" {
#endif

void SeGetDefragConfAndTimeCond(void *label, DmLabelTypeE labelType, bool *enableDefrag)
{
    DB_POINTER2(label, enableDefrag);
    *enableDefrag = false;
    if (labelType == VERTEX_LABEL) {
        DmVertexLabelT *vertexLabel = (DmVertexLabelT *)label;
        VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
        *enableDefrag = commonInfo->heapInfo.needDefragmentation;
        if (!*enableDefrag) {
            return;
        }
    } else if (labelType == KV_TABLE) {
        DmKvLabelT *kvLabel = (DmKvLabelT *)label;
        *enableDefrag = kvLabel->needDefragmentation;
        if (!*enableDefrag) {
            return;
        }
    } else {  // 不应该走到这个分支
        DB_ASSERT(false);
    }
}

// 碎片整理, 将任务放入后台线程
void SeDfgmtAddTask(uint32_t labelId, uint32_t spaceId, bool isReleasePage)
{
    if (isReleasePage) {
        DB_ASSERT(spaceId != DB_INVALID_TABLE_SPACE_INDEX);
    }
    DfgmtTaskParaT para = {.labelId = labelId, .spaceId = spaceId, .isReleasePage = isReleasePage};
    Status ret = DbCreateDfgmtTask(MEM_COMPACT_LABEL, &para);
    DB_LOG_INFO("create dfgmt: %" PRId32 ", labelId: %" PRIu32 ", spaceId: %" PRIu32 ", isReleasePage: %" PRIu32, ret,
        labelId, spaceId, isReleasePage);
}

#ifdef __cplusplus
}
#endif
