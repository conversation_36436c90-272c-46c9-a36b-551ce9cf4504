/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Heap 接口fetch 相关 实现
 * Author: panghaisheng
 * Create: 2022-7-27
 */
#include "adpt_sleep.h"
#include "se_heap_access_inner.h"
#include "se_heap_access_dm.h"
#include "se_heap_fetch.h"
#include "se_heap_trx_acc_inner.h"
#include "se_heap_slice_row.h"
#include "se_heap_utils.h"
#include "se_heap_addr.h"
#include "se_log.h"
#include "db_crash_debug.h"
#include "db_hash.h"
#include "se_trx_mgr.h"
#include "se_page_recorder.h"

#ifdef __cplusplus
extern "C" {
#endif

// 出于兼容性考虑的原因不能改这些结构体布局
#ifdef FEATURE_PERSISTENCE
#ifdef NDEBUG
static_assert(sizeof(HFPageHeadT) == 96,  // 看护结构体不能随意改动，96为release下结构体大小
    "HFPageHeadT in release should be fixed size, otherwise affect compatibility");
#else
static_assert(sizeof(HFPageHeadT) == 112,  // 看护结构体不能随意改动，112为debug下结构体大小
    "HFPageHeadT in release should be fixed size, otherwise affect compatibility");
#endif
#endif

// 行头结构体看护
static_assert(sizeof(HpRowStateT) == 1,  // 看护heap行头结构体不能随意改动，为当前结构体大小
    "HpRowStateT should be fixed size, otherwise causes incompatibility");
static_assert(sizeof(HpNormalFixRowHead) == 24,  // 看护heap行头结构体不能随意改动，24为当前结构体大小
    "HpNormalFixRowHead should be fixed size, otherwise causes incompatibility");
static_assert(sizeof(HpLinkSrcFixRowHead) == 32,  // 看护heap行头结构体不能随意改动，32为当前结构体大小
    "HpLinkSrcFixRowHead should be fixed size, otherwise causes incompatibility");
static_assert(sizeof(HpLinkDstFixRowHead) == 24,  // 看护heap行头结构体不能随意改动，24为当前结构体大小
    "HpLinkDstFixRowHead should be fixed size, otherwise causes incompatibility");
static_assert(sizeof(HpNormalRowHeadT) == 24,  // 看护heap行头结构体不能随意改动，24为当前结构体大小
    "HpNormalRowHeadT should be fixed size, otherwise causes incompatibility");

static_assert(sizeof(HpSliceSubRowHeadT) == 24,  // 看护heap行头结构体不能随意改动，24为当前结构体大小
    "HpSliceSubRowHeadT should be fixed size, otherwise causes incompatibility");

#if (defined(FEATURE_SQL) && !defined(FEATURE_SIMPLEREL))
// 行头 终端 (不区分64 32)强制8字节对齐，
static_assert(sizeof(HpLinkRowHeadT) == 40,  // 看护heap行头结构体不能随意改动，40为当前结构体大小
    "HpLinkRowHeadT should be fixed size, otherwise causes incompatibility");
#else
// 行头，非终端（数通，光启） (不区分64 32)4字节对齐
static_assert(sizeof(HpLinkRowHeadT) == 36,  // 看护heap行头结构体不能随意改动，36为当前结构体大小
    "HpLinkRowHeadT should be fixed size, otherwise causes incompatibility");
#endif

static inline void HeapCheckMode(HeapRunCtxT *ctx, const HpPageFetchRowInfo *fetchRowInfo)
{
    // 在脏读情况下, 可能查询到未关联 linkDst 的 linkSrc. 当前分析, 应该只有大对象分片有这个状态:
    // 事务A刚刚插入了linkSrc(大对象分片), 但是linkDst和各个subRows还没有插入; 此时, 其他事务扫描到该 linkSrc,
    // 则它的addr无效
#ifndef NDEBUG
    bool flag = !HeapIsOnNormalTrxLockMode(ctx) && fetchRowInfo->isSliceRows;
    if (!flag) {
        DB_ASSERT(false);
    }
#endif
}

HOT_FUN_RC StatusInter HeapFetchRowWithRCLevel(
    HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo, const HpRowStateT *rowState, TrxIdT rowTrxId, bool isDirectRead)
{
    // 临时生成ReadView
    ReadViewT readView;
    readView.isOpen = false;
    readView.activeTrxIdsShm = DB_INVALID_SHMPTR;
    readView.activeTrxIdsTmp = NULL;
    StatusInter ret = ReadViewPrepareForRc(ctx->seRunCtx, &readView, isDirectRead);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        fetchRowInfo->isGetBuf = false;
        SE_ERROR(ret,
            "RC prepare readview, labelId:%" PRIu32 ", rowId:(%" PRIu32 ",%" PRIu32 "), rec trx:(%" PRIu64
            "), directRead: %" PRIu8 "",
            ctx->heapCfg.labelId, fetchRowInfo->srcRowInfo.itemPtr.pageId, fetchRowInfo->srcRowInfo.itemPtr.slotId,
            rowTrxId, isDirectRead);
        return ret;
    }

    // 判断主版本是否可见
    ret = ReadViewIsTrxVisible(&readView, rowTrxId);
    if (SECUREC_UNLIKELY(ret == MEMORY_OPERATE_FAILED_INTER)) {
        fetchRowInfo->isGetBuf = false;
        SE_LAST_ERROR(ret,
            "RC activeTrxIdsShm transfer, labelId:%" PRIu32 ", rowId:(%" PRIu32 ",%" PRIu32 "), rec trx:(%" PRIu64
            "), directRead: %" PRIu8 "",
            ctx->heapCfg.labelId, fetchRowInfo->srcRowInfo.itemPtr.pageId, fetchRowInfo->srcRowInfo.itemPtr.slotId,
            rowTrxId, isDirectRead);
        ReadViewCloseForRc(ctx->seRunCtx, &readView);
        return ret;
    }
    if (ret == STATUS_OK_INTER) {
        // 主版本可见，判断是否标记删除，或者 isCanAccessMarkDelRow 标记
        if (!rowState->isDeleted || fetchRowInfo->isCanAccessMarkDelRow) {
            ReadViewCloseForRc(ctx->seRunCtx, &readView);
            return STATUS_OK_INTER;
        }
    } else {
        // masterVersion不可见的情况下，可以继续读版本链
        // 写事务走到这里一定加上了事务锁（但有可能是insert操作刚插入的一行（先插入再加事务锁），因此此时也是不可访问的）
        // 直连读场景，直接读即可
#ifndef FEATURE_SIMPLEREL
        if (ctx->hpOperation == HEAP_OPTYPE_NORMALREAD || isDirectRead) {
#endif
            // 访问最近一个提交版本
            ret = HeapPageReadUndo(ctx, &readView, fetchRowInfo);
            ReadViewCloseForRc(ctx->seRunCtx, &readView);
            return ret;
#ifndef FEATURE_SIMPLEREL
        }
#endif
    }
    ReadViewCloseForRc(ctx->seRunCtx, &readView);
    fetchRowInfo->isGetBuf = false;  // 之前置为true, 如果不可见/或是已删除的状态, 需要置为false;
    return NO_DATA_HEAP_ITEM_NOT_EXIST;
}

static inline StatusInter HeapFetchReadNextVersion(HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo, TrxT *trx)
{
    // getDiff场景需要感知: 如果是本事务自己删除的，则可以读取，但标记删除的可能是其他事务的内容
    // 需要继续去版本链中读第一个可见版本
    fetchRowInfo->isSelfDelBuf = true;
    StatusInter ret = HeapPageReadUndo(ctx, &trx->trx.base.readView, fetchRowInfo);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "readUndo, pageId:%" PRIu32 ", slotId:%" PRIu32, fetchRowInfo->srcRowInfo.itemPtr.pageId,
            fetchRowInfo->srcRowInfo.itemPtr.slotId);
        DB_ASSERT(false);  // 标记删除往前读，一定会有一个可见版本
    }
    return ret;
}

StatusInter HeapFetchRowWithRRLevel(
    HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo, const HpRowStateT *rowState, TrxIdT rowTrxId)
{
    TrxT *trx = (TrxT *)ctx->seRunCtx->trx;
    ReadViewT *readView = &trx->trx.base.readView;
    StatusInter ret = ReadViewIsTrxVisible(readView, rowTrxId);
    if (SECUREC_UNLIKELY(ret == MEMORY_OPERATE_FAILED_INTER)) {
        SE_LAST_ERROR(ret, "FetchRowWithRR: activeTrxIdsShm transfer, trx:%" PRIu64 ", label:%" PRIu32, trx->base.trxId,
            ctx->heapCfg.labelId);
        return ret;
    }
    if (!fetchRowInfo->isToGetOldestVisibleBuf) {
        bool isVisibleOrNoNeedReadView = (ret == STATUS_OK_INTER) || !ctx->hpControl.trxContrl.isNeedReadView;
        if (isVisibleOrNoNeedReadView) {
            if (fetchRowInfo->isCanAccessMarkDelRow || !rowState->isDeleted) {
                // 乐观事务下，有可能ret = NO_DATA 且 isNeedReadView = false
                // 时走到这里，之所以使其返回ok，是为了读到主版本，写进undo, 使版本链串起来
                return STATUS_OK_INTER;
            }
            if (fetchRowInfo->isCanAccessSelfDeletedBuf && rowState->isDeleted && rowTrxId == readView->creatorTrxId) {
                return HeapFetchReadNextVersion(ctx, fetchRowInfo, trx);
            }
            fetchRowInfo->isGetBuf = false;  // 之前置为true, 如果不可见, 需要置为false;
            return NO_DATA_HEAP_ITEM_NOT_EXIST;
        }
    } else {
        // 如果masterVersion是可见版本，且不是本事务编辑的，就可以读
        if (ret == STATUS_OK_INTER && rowTrxId != readView->creatorTrxId) {
            if (fetchRowInfo->isCanAccessMarkDelRow || !rowState->isDeleted) {
                return STATUS_OK_INTER;
            }
            fetchRowInfo->isGetBuf = false;  // 之前置为true, 如果不可见, 需要置为false;
            return NO_DATA_HEAP_ITEM_NOT_EXIST;
        }
    }

    // 不可见的情况下, 读取历史版本
    return HeapPageReadUndo(ctx, &trx->trx.base.readView, fetchRowInfo);
}

static inline StatusInter CheckSelfEditOrCommitted(TrxMgrT *trxMgr, TrxIdT localTrxId, TrxIdT rowTrxId, bool *checkOk)
{
    if (localTrxId == rowTrxId) {
        *checkOk = true;
        return STATUS_OK_INTER;
    }
    return TrxMgrCheckTrxIsCommitted(trxMgr, rowTrxId, checkOk);
}

StatusInter HeapFetchRowWithSILevel(
    HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo, const HpRowStateT *rowState, TrxIdT rowTrxId)
{
    TrxT *trx = (TrxT *)ctx->seRunCtx->trx;
    // 读主版本，判断主版本是否是本事务或已提交版本
    bool isSelfEditOrCommitted = false;
    StatusInter ret =
        CheckSelfEditOrCommitted(ctx->seRunCtx->trxMgr, trx->base.trxId, rowTrxId, &isSelfEditOrCommitted);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    DB_ASSERT(isSelfEditOrCommitted);

    if (fetchRowInfo->isCanAccessMarkDelRow || !rowState->isDeleted) {
        return STATUS_OK_INTER;
    }
    fetchRowInfo->isGetBuf = false;  // 之前置为true, 如果不可见, 需要置为false;
    return NO_DATA_HEAP_ITEM_NOT_EXIST;
}

HOT_FUN_READ StatusInter HeapFetchRowCheckReadView(
    HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, const HpRowStateT *rowState)
{
    DB_POINTER3(ctx, opInfo, rowState);
    if (ctx->hpControl.accessMethodType == HEAP_ACCESS_METHOD_LITE ||
        ctx->hpControl.ccType == CONCURRENCY_CONTROL_READ_COMMIT_LABEL_LATCH) {
        // 轻量化事务和RC表锁, 直接访问 master version
        return STATUS_OK_INTER;
    }
    if (ctx->hpControl.accessMethodType == HEAP_ACCESS_METHOD_LITE_BG ||
        ctx->hpControl.accessMethodType == HEAP_ACCESS_METHOD_RSM_LITE_BG) {
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
        // 后台线程事务, 直接访问 master version，判断是否删除
        if (opInfo->fetchRowInfo->isCanAccessMarkDelRow || !rowState->isDeleted) {
            return STATUS_OK_INTER;
        }
        opInfo->fetchRowInfo->isGetBuf = false;
        return NO_DATA_HEAP_ITEM_NOT_EXIST;
#else
        return STATUS_OK_INTER;
#endif
    }
    if (ctx->hpOperation == HEAP_OPTYPE_UNDO_PURGER) {
        // undo pure 场景, 直接访问 master version
        return STATUS_OK_INTER;
    }
    // 下面是读已提交; (重复读的流程, 后面需要再添加分支)
    DB_ASSERT(ctx->hpControl.accessMethodType == HEAP_ACCESS_METHOD_NORMAL);
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    TrxIdT rowTrxId = HeapRowGetTrxId(&fetchRowInfo->rowTrxInfo);
    TrxT *trx = (TrxT *)ctx->seRunCtx->trx;
    // 非轻量化、未开事务的直连读场景，按照RC隔离级别去读
    if (ctx->hpOperation == HEAP_OPTYPE_DIRECTREAD && trx->base.state == TRX_STATE_NOT_STARTED) {
        return HeapFetchRowWithRCLevel(ctx, fetchRowInfo, rowState, rowTrxId, true);
    }
    IsolationLevelE isolationLevel = TrxGetIsolationLevel(trx);
    if (isolationLevel == READ_COMMITTED || fetchRowInfo->isReadLatestCommittedBuf) {
        return HeapFetchRowWithRCLevel(ctx, fetchRowInfo, rowState, rowTrxId, false);
    } else if (isolationLevel == REPEATABLE_READ) {
        return HeapFetchRowWithRRLevel(ctx, fetchRowInfo, rowState, rowTrxId);
    } else if (isolationLevel == SERIALIZABLE) {
        return HeapFetchRowWithSILevel(ctx, fetchRowInfo, rowState, rowTrxId);
    } else {
        return INVALID_PARAMETER_VALUE_INTER;
    }
}

#ifdef FEATURE_SIMPLEREL
// 前一版本addr存在ctx->buf，目标版本存targetPointer
void RowUndoGetFixRowTargetNeighbor(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    if (fetchRowInfo->rowTrxInfo.rollPtr != ctx->compV1Info.targetRollPtr) {
        UndoTupleCombineAddrT undoTupleInfo = {0};
        RowUndoFindTarget(
            ctx->seRunCtx->undoCtx, fetchRowInfo->rowTrxInfo.rollPtr, ctx->compV1Info.targetRollPtr, &undoTupleInfo);
        HpNormalFixRowHead *fixRowHead = (HpNormalFixRowHead *)(void *)undoTupleInfo.tuple.data;
        if (fixRowHead != NULL) {
            fetchRowInfo->curRowInfo->rawRowSize = fixRowHead->size;
            fetchRowInfo->isGetBuf = true;
            fetchRowInfo->buf = fixRowHead + 1;
            fetchRowInfo->bufSize = fixRowHead->size;
        }
    }
    FixRowUndoGetTargetPtr(ctx->seRunCtx->undoCtx, ctx->compV1Info.targetRollPtr, &ctx->compV1Info.targetPointer);
}

// 前一版本addr存在ctx->buf，目标版本存targetPointer
void RowUndoGetVarRowTargetNeighbor(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    if (fetchRowInfo->rowTrxInfo.rollPtr != ctx->compV1Info.targetRollPtr) {
        UndoTupleCombineAddrT undoTupleInfo = {0};
        RowUndoFindTarget(
            ctx->seRunCtx->undoCtx, fetchRowInfo->rowTrxInfo.rollPtr, ctx->compV1Info.targetRollPtr, &undoTupleInfo);
        HpLinkRowHeadT *linkRowHead = (HpLinkRowHeadT *)(void *)undoTupleInfo.tuple.data;
        if (linkRowHead != NULL) {
            fetchRowInfo->curRowInfo->rowHeadPtr.linkRowHead = linkRowHead;
        }
    }
    ValRowUndoGetTargetPtr(ctx->seRunCtx->undoCtx, ctx->compV1Info.targetRollPtr, &ctx->compV1Info.targetPointer);
}

HOT_FUN_READ StatusInter HeapFetchFixRowSetRowInfo(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    HpPageFetchRowInfoInitState(fetchRowInfo);
    HpRowHeadPtrT rowHeadPtr = fetchRowInfo->curRowInfo->rowHeadPtr;
    HpRowStateT *rowState = rowHeadPtr.rowState;
    DB_ASSERT(HeapPageIsNormalRow(rowState));
    // normal 行 和 linkSrc是外部行, 需要检查事务可见性
    fetchRowInfo->curRowInfo->rowType = HEAP_FIX_ROW_NORMAL_ROW;
    fetchRowInfo->rowTrxInfo = rowHeadPtr.normalFixRowHead->trxInfo;
    HeapSetFetchRowInfoWithFixNormalRow(fetchRowInfo);
    StatusInter ret = HeapFetchRowCheckReadView(ctx, opInfo, rowState);

    if (ctx->hpControl.isReadTargetNeighbor) {
        RowUndoGetFixRowTargetNeighbor(ctx, opInfo);
    }
    return ret;
}

#else
HOT_FUN_READ StatusInter HeapFetchFixRowSetRowInfo(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    StatusInter ret = INT_ERR_HEAP_INVALID_PARAMETER;
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    HpPageFetchRowInfoInitState(fetchRowInfo);
    HpRowHeadPtrT rowHeadPtr = fetchRowInfo->curRowInfo->rowHeadPtr;
    HpRowStateT *rowState = rowHeadPtr.rowState;
    // normal 行 和 linkSrc是外部行, 需要检查事务可见性
    if (SECUREC_LIKELY(HeapPageIsNormalRow(rowState))) {
        fetchRowInfo->curRowInfo->rowType = HEAP_FIX_ROW_NORMAL_ROW;
        fetchRowInfo->rowTrxInfo = rowHeadPtr.normalFixRowHead->trxInfo;
        HeapSetFetchRowInfoWithFixNormalRow(fetchRowInfo);
        ret = HeapFetchRowCheckReadView(ctx, opInfo, rowState);
    } else if (HeapPageIsLinkSrcRow(rowState)) {
        fetchRowInfo->curRowInfo->rowType = HEAP_FIX_ROW_LINK_SRC_ROW;
        fetchRowInfo->rowTrxInfo = rowHeadPtr.linkSrcFixRowHead->trxInfo;
        HeapSetFetchRowInfoWithFixLinkSrcRow(fetchRowInfo);
        ret = HeapFetchRowCheckReadView(ctx, opInfo, rowState);
    } else if (HeapPageIsLinkDstRow(rowState)) {  // 下面是内部的行信息 (linkDst 行), 只要检查控制参数, 判断是否可见
        if (fetchRowInfo->canAccessLinkDst) {
            fetchRowInfo->is2LinkDstRow = true;
            fetchRowInfo->curRowInfo->rowType = HEAP_FIX_ROW_LINK_DST_ROW;
            HeapSetFetchRowInfoWithFixLinkDstRow(fetchRowInfo);
            return STATUS_OK_INTER;
        } else {  // 扫描场景，当扫描到目的迁移行，直接返回该行不存在
            return NO_DATA_HEAP_ITEM_NOT_EXIST;
        }
    }
    if (fetchRowInfo->isFindPrevVersionInfo && ret == STATUS_OK_INTER) {
        DB_ASSERT(fetchRowInfo->curRowInfo->rowType != HEAP_FIX_ROW_LINK_DST_ROW);
        ret = HeapFixPageReadPrevVersion(ctx, fetchRowInfo);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "get prev-version, labelId:%" PRIu32 ", pageId:%" PRIu32 ", slotId:%" PRIu32,
                ctx->heapCfg.labelId, fetchRowInfo->srcRowInfo.itemPtr.pageId, fetchRowInfo->srcRowInfo.itemPtr.slotId);
        }
    }
    return ret;
}
#endif

inline static StatusInter HeapFetchFixRowInPage(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    HeapRowInfoT *curRowInfo = opInfo->fetchRowInfo->curRowInfo;
    StatusInter ret = HeapFixPageGetRowBySlotId(curRowInfo->pageHeadPtr.fixPageHead, curRowInfo->itemPtr.slotId,
        &curRowInfo->rowHeadPtr.normalFixRowHead, &curRowInfo->slotOffset);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    return HeapFetchFixRowSetRowInfo(ctx, opInfo);
}

HOT_FUN_READ StatusInter HeapReLocateFixLinkSrcAfterReLatch(
    HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, bool *isPageChange)
{
    DB_POINTER3(ctx, opInfo, isPageChange);
    // 跨页触发 reLatch 时，要重新访问linkSrc, 避免这段时间, linkSrc更新;
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    HeapRowInfoT *srcRowInfo = &fetchRowInfo->srcRowInfo;

    // 由于relatch, src行可能被删除
    StatusInter ret = HeapFixPageGetRowBySlotId(srcRowInfo->pageHeadPtr.fixPageHead, srcRowInfo->itemPtr.slotId,
        &srcRowInfo->rowHeadPtr.normalFixRowHead, &srcRowInfo->slotOffset);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    if (ctx->hpControl.trxContrl.isRowLockExist) {
        return STATUS_OK_INTER;  // 如果加了行锁, row里面的内容就在本次fetch流程不会变化;
    }

    // 没有加行锁, src page 重新加解锁; 而且当前在访问 dst page, 接下来, 重新访问 src row, 检查内容 是否变更
    HpItemPointerT oldDstItemPtr = fetchRowInfo->dstRowInfo.itemPtr;
    fetchRowInfo->canAccessLinkDst = false;                // 这次是判断 src行, 这里不应该访问 dst
    fetchRowInfo->curRowInfo = &fetchRowInfo->srcRowInfo;  // 下面访问 src 行
    ret = HeapFetchFixRowSetRowInfo(ctx, opInfo);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    if (fetchRowInfo->isGetBuf) {  // 如果转变为normal行，则可以直接获取数据
        SePgLatchSorterRemovePage(
            ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, oldDstItemPtr.pageId);  // 释放旧的linkDst的锁
        return STATUS_OK_INTER;
    }

    // 重新访问成功, 但是没有获取到buf, 说明 还是跳转行
    DB_ASSERT(fetchRowInfo->is2LinkDstRow);
    // 更新itemPtr为新的linkDstPtr, 再重新访问 dst的page
    fetchRowInfo->curRowInfo = &fetchRowInfo->dstRowInfo;
    fetchRowInfo->curRowInfo->itemPtr = fetchRowInfo->nextItemPtr;
    fetchRowInfo->canAccessLinkDst = true;  // 恢复原来读取的 linkDst 的场景

    // 如果 dst 行的page发生变化, 需要释放旧的page, 重新访问 linkDst
    if (fetchRowInfo->nextItemPtr.pageId != oldDstItemPtr.pageId) {  // linkDst发生了变更
        SePgLatchSorterRemovePage(
            ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, oldDstItemPtr.pageId);  // 释放旧的linkDst的锁
        *isPageChange = true;
        return STATUS_OK_INTER;
    }
    // 还是访问 原来的page, 继续访问
    return STATUS_OK_INTER;
}

HOT_FUN_READ StatusInter HeapLocateLinkDstFixRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    if (!HeapIsValidAddr(fetchRowInfo->nextItemPtr)) {
        HeapCheckMode(ctx, fetchRowInfo);
        return NO_DATA_HEAP_ITEM_NOT_EXIST;
    }
    StatusInter ret;
    uint32_t tryTimes = 0;
    const uint32_t maxRetry = 10;  // 加上重试上限，避免并发问题导致死循.
    for (; tryTimes < maxRetry; tryTimes++) {
        // 设置访问 dstRow, 继续访问
        fetchRowInfo->canAccessLinkDst = true;
        fetchRowInfo->curRowInfo = &fetchRowInfo->dstRowInfo;
        fetchRowInfo->curRowInfo->itemPtr = fetchRowInfo->nextItemPtr;

        ret = HeapFetchGetPage(ctx, fetchRowInfo);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }

        // 跨页访问, 可能有reLatch. 检查一下 主行(linkSrc)是否有变化
        bool isSrcRowReLatch = SePgLatchSorterIsPageReLatch(&ctx->pageSorter, fetchRowInfo->srcRowInfo.itemPtr.pageId);
        if (SECUREC_UNLIKELY(isSrcRowReLatch)) {
            // 清除 reLatch标记, 然后重新访问linkSrc, 看看发生改变
            SePgLatchSorterClearPageReLatchState(&ctx->pageSorter, fetchRowInfo->srcRowInfo.itemPtr.pageId);
            bool isPageChange = false;
            ret = HeapReLocateFixLinkSrcAfterReLatch(ctx, opInfo, &isPageChange);
            if (ret != STATUS_OK_INTER || fetchRowInfo->isGetBuf) {
                return ret;
            }
            if (isPageChange) {  // page改变, 需要重新访问
                continue;
            }
        }
        break;
    }
    if (tryTimes == maxRetry) {
        // 重试达到循环上限,未获取到数据
        DB_LOG_WARN(GMERR_NO_DATA, "labelId:%" PRIu32 ", fetch tuple's dstRow, pageId:%" PRIu32 ", slotId:%" PRIu32,
            ctx->heapCfg.labelId, opInfo->fetchRowInfo->srcRowInfo.itemPtr.pageId,
            opInfo->fetchRowInfo->srcRowInfo.itemPtr.slotId);
        return NO_DATA_HEAP_ITEM_NOT_EXIST;
    }
    // 从page中获取 linkDstRow
    ret = HeapFetchFixRowInPage(ctx, opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret,
            "labelId:%" PRIu32 ", fetch link dst-fixRow, pageId:%" PRIu32 ", slotId:%" PRIu32 ", ret:%" PRIu32,
            ctx->heapCfg.labelId, fetchRowInfo->curRowInfo->itemPtr.pageId, fetchRowInfo->curRowInfo->itemPtr.slotId,
            (uint32_t)ret);
    }
    return ret;
}

static inline void HeapSetFetchRowTrxInfoAndType(
    HpPageFetchRowInfo *fetchRowInfo, HpPageRowTypeE rowType, RowTrxInfoT trx)
{
    fetchRowInfo->curRowInfo->rowType = rowType;
    fetchRowInfo->rowTrxInfo = trx;
}

static StatusInter FetchVarLinkDstRowAndCheckCondition(HpPageFetchRowInfo *fetchRowInfo, HpRowStateT *rowState)
{
    // 下面是内部的行信息 (linkDst 行, 分片子行), 只要检查控制参数, 判断是否可见
    if (fetchRowInfo->canAccessLinkDst) {
        fetchRowInfo->is2LinkDstRow = true;
        if (SECUREC_UNLIKELY(rowState->isSliceHead)) {
            fetchRowInfo->curRowInfo->rowType = HEAP_VAR_ROW_LINK_DST_SLICE_DIR_ROW;
            HeapSetFetchRowInfoWithLinkDstSliceDirRow(fetchRowInfo);
        } else {
            fetchRowInfo->curRowInfo->rowType = HEAP_VAR_ROW_LINK_DST_ROW;
            HeapSetFetchRowInfoWithLinkDstRow(fetchRowInfo);
        }
        return STATUS_OK_INTER;
    } else {  // 扫描场景，当扫描到目的迁移行，直接返回该行不存在
        return NO_DATA_HEAP_ITEM_NOT_EXIST;
    }
}

HOT_FUN_READ StatusInter HeapFetchVarRowSetRowInfo(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    StatusInter ret = INT_ERR_HEAP_INVALID_PARAMETER;
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    HpPageFetchRowInfoInitState(fetchRowInfo);
    HpRowHeadPtrT rowHeadPtr = fetchRowInfo->curRowInfo->rowHeadPtr;
    HpRowStateT *rowState = rowHeadPtr.rowState;
    // normal 行 和 linkSrc是外部行, 需要检查事务可见性
    if (SECUREC_LIKELY(HeapPageIsNormalRow(rowState))) {
        DB_ASSERT(rowHeadPtr.normalRowHead->magicNum == HEAP_ROW_MAGIC_NUM);
        HeapSetFetchRowTrxInfoAndType(fetchRowInfo, HEAP_VAR_ROW_NORMAL_ROW, rowHeadPtr.normalRowHead->trxInfo);
        HeapSetFetchRowInfoWithNormalRow(fetchRowInfo);
        ret = HeapFetchRowCheckReadView(ctx, opInfo, rowState);
    } else if (HeapPageIsLinkSrcRow(rowState)) {
        DB_ASSERT(rowHeadPtr.linkRowHead->magicNum == HEAP_ROW_MAGIC_NUM);
        HeapSetFetchRowTrxInfoAndType(fetchRowInfo, HEAP_VAR_ROW_LINK_SRC_ROW, rowHeadPtr.linkRowHead->srcTrxInfo);
        HeapSetFetchRowInfoWithLinkSrcRow(fetchRowInfo);
        ret = HeapFetchRowCheckReadView(ctx, opInfo, rowState);
#ifdef FEATURE_SIMPLEREL
        if (ctx->hpControl.isReadTargetNeighbor && ret == STATUS_OK_INTER) {
            RowUndoGetVarRowTargetNeighbor(ctx, opInfo);
            HeapSetFetchRowInfoWithLinkSrcRow(fetchRowInfo);  // 重新设置当前row info
        }
        ctx->compV1Info.realInsertPtr = fetchRowInfo->curRowInfo->rowHeadPtr.rowState;
#endif
    } else if (HeapPageIsLinkDstRow(rowState)) {
        DB_ASSERT(rowHeadPtr.linkRowHead->magicNum == HEAP_ROW_MAGIC_NUM);
        return FetchVarLinkDstRowAndCheckCondition(fetchRowInfo, rowState);
    } else if (HeapPageIsSliceSubRow(rowState)) {
        if (fetchRowInfo->canAccessSubRow) {
            fetchRowInfo->curRowInfo->rowType = HEAP_VAR_ROW_SLICE_SUB_ROW;
            HeapSetFetchRowInfoWithSliceSubRow(fetchRowInfo);
            return STATUS_OK_INTER;
        } else {  // 扫描场景，当扫描到分片行，直接返回该行不存在
            return NO_DATA_HEAP_ITEM_NOT_EXIST;
        }
    }
    DB_ASSERT(HeapPageIsNormalRow(rowState) || HeapPageIsLinkSrcRow(rowState) || HeapPageIsLinkDstRow(rowState) ||
              HeapPageIsSliceSubRow(rowState));
#ifndef FEATURE_SIMPLEREL
    if (fetchRowInfo->isFindPrevVersionInfo && ret == STATUS_OK_INTER) {
        DB_ASSERT(fetchRowInfo->curRowInfo->rowType != HEAP_VAR_ROW_LINK_DST_ROW);
        ret = HeapVarPageReadPrevVersion(ctx, fetchRowInfo);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "get prev-version, labelId:%" PRIu32 ", pageId:%" PRIu32 ", slotId:%" PRIu32,
                ctx->heapCfg.labelId, fetchRowInfo->srcRowInfo.itemPtr.pageId, fetchRowInfo->srcRowInfo.itemPtr.slotId);
        }
    }
#endif
    return ret;
}

HOT_FUN_READ StatusInter HeapReLocateLinkSrcAfterReLatch(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, bool *isPageChange)
{
    DB_POINTER3(ctx, opInfo, isPageChange);
    // 跨页触发 reLatch 时，要重新访问linkSrc, 避免这段时间, linkSrc更新;
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    HeapRowInfoT *srcRowInfo = &fetchRowInfo->srcRowInfo;

    // 1. src行可能被删除，需要重新读取slot验证内容是否还有效.
    // 2. 页可能被释放后重新分配，页重新初始化后slotDir会跟原来不一致，需要校验offset是否还有效
    StatusInter ret = HeapVarPageGetSlotBySlotId(
        srcRowInfo->pageHeadPtr.varPageHead, srcRowInfo->itemPtr.slotId, &srcRowInfo->slotOffset, &srcRowInfo->slot);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    // 目前主要是重新获取 rowHeadPtr 这个字段; 因为页内压缩, 可能导致该row的偏移变化
    srcRowInfo->rowHeadPtr.rowState = HeapVarPageGetRowState(srcRowInfo->pageHeadPtr.varPageHead, srcRowInfo->slot);

    if (ctx->hpControl.trxContrl.isRowLockExist) {
        return STATUS_OK_INTER;  // 如果加了行锁, row里面的内容就在本次fetch流程不会变化;
    }

    // 没有加行锁, src page 重新加解锁; 而且当前在访问 dst page, 接下来, 重新访问 src row, 检查内容 是否变更
    HpItemPointerT oldDstItemPtr = fetchRowInfo->dstRowInfo.itemPtr;
    fetchRowInfo->canAccessLinkDst = false;                // 这次是判断 src行, 这里不应该访问 dst
    fetchRowInfo->curRowInfo = &fetchRowInfo->srcRowInfo;  // 下面访问 src 行
    ret = HeapFetchVarRowSetRowInfo(ctx, opInfo);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    if (fetchRowInfo->isGetBuf) {  // 如果转变为normal行，则可以直接获取数据
        SePgLatchSorterRemovePage(
            ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, oldDstItemPtr.pageId);  // 释放旧的linkDst的锁
        return STATUS_OK_INTER;
    }

    // 重新访问成功, 但是没有获取到buf, 说明 还是跳转行
    DB_ASSERT(fetchRowInfo->is2LinkDstRow);
    // 更新itemPtr为新的linkDstPtr, 再重新访问 dst的page
    fetchRowInfo->canAccessLinkDst = true;  // 恢复原来读取的 linkDst 的场景
    fetchRowInfo->curRowInfo = &fetchRowInfo->dstRowInfo;
    fetchRowInfo->curRowInfo->itemPtr = fetchRowInfo->nextItemPtr;

    // 如果 dst 行的page发生变化, 需要释放旧的page, 重新访问 linkDst
    if (fetchRowInfo->nextItemPtr.pageId != oldDstItemPtr.pageId) {  // linkDst发生了变更
        SePgLatchSorterRemovePage(
            ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, oldDstItemPtr.pageId);  // 释放旧的linkDst的锁
        *isPageChange = true;
        return STATUS_OK_INTER;
    }
    // 还是访问 原来的page, 继续访问
    return STATUS_OK_INTER;
}

StatusInter HeapLocateLinkDstVarRowCheck(
    HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo, uint32_t fetchTryTimes, bool isSrcRelatch, StatusInter ret)
{
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        HpRowStateT *rowState = fetchRowInfo->curRowInfo->rowHeadPtr.rowState;
        uint8_t state = rowState == NULL ? DB_INVALID_UINT8 : *(uint8_t *)rowState;
        DB_ASSERT(rowState != NULL);
        SE_ERROR(ret,
            "fetch link dst-varRow, labelId:%" PRIu32 ", pageId:%" PRIu32 ", slotId:%" PRIu32 ", rowState:%" PRIu32
            ", fetchTryTimes:%" PRIu32 ", isSrcRelatch:%" PRIu32 ", ret:%" PRId32,
            ctx->heapCfg.labelId, fetchRowInfo->curRowInfo->itemPtr.pageId, fetchRowInfo->curRowInfo->itemPtr.slotId,
            state, fetchTryTimes, (uint32_t)isSrcRelatch, ret);
        return ret;
    } else {
        // check一下读出来的目的行是否真的是目的行
        if (!HeapPageIsLinkDstRow(fetchRowInfo->curRowInfo->rowHeadPtr.rowState)) {
            SE_ERROR(INT_ERR_HEAP_UNEXPECT_ERROR,
                "labelId:%" PRIu32 ", link dst-varRow type inv, pageId:%" PRIu32 ", slotId:%" PRIu32
                ", rowState:%" PRIu32 ", fetchTryTimes:%" PRIu32 ", isSrcRelatch:%" PRIu32,
                ctx->heapCfg.labelId, fetchRowInfo->curRowInfo->itemPtr.pageId,
                fetchRowInfo->curRowInfo->itemPtr.slotId, *(uint8_t *)fetchRowInfo->curRowInfo->rowHeadPtr.rowState,
                fetchTryTimes, (uint32_t)isSrcRelatch);
            DB_ASSERT(false);  // 此处预期读出来的应该是dst-row
            return INT_ERR_HEAP_UNEXPECT_ERROR;
        } else {
            if (ctx->hpControl.isRsmRecovery) {
                return STATUS_OK_INTER;  // 恢复阶段不校验
            }
            RowTrxIdT srcTrxId = fetchRowInfo->srcRowInfo.rowHeadPtr.linkRowHead->srcTrxInfo.trxId;
            RowTrxIdT curTrxId = fetchRowInfo->curRowInfo->rowHeadPtr.linkRowHead->dstInfo.trxId;
#ifdef FEATURE_SIMPLEREL  // v1 场景 undo purger 不校验事务id
            if (ctx->compV1Info.isFetchByTTree) {
                return STATUS_OK_INTER;
            }
            if (!fetchRowInfo->srcRowInfo.rowHeadPtr.rowState->isDeleted &&
                (!CheckRowTrxIdIsEqual(srcTrxId, curTrxId) && ctx->hpOperation != HEAP_OPTYPE_UNDO_PURGER)) {
#else
            if (!fetchRowInfo->srcRowInfo.rowHeadPtr.rowState->isDeleted && !CheckRowTrxIdIsEqual(srcTrxId, curTrxId)) {
#endif
                SE_ERROR(INT_ERR_HEAP_UNEXPECT_ERROR,
                    "labelId:%" PRIu32 ", link dst-varRow trxId inv, pageId:%" PRIu32 ", slotId:%" PRIu32
                    ", rowState:%" PRIu32 ", fetchTryTimes:%" PRIu32 ", isSrcRelatch:%" PRIu32,
                    ctx->heapCfg.labelId, fetchRowInfo->curRowInfo->itemPtr.pageId,
                    fetchRowInfo->curRowInfo->itemPtr.slotId, *(uint8_t *)fetchRowInfo->curRowInfo->rowHeadPtr.rowState,
                    fetchTryTimes, (uint32_t)isSrcRelatch);
                DB_ASSERT(false);  // 预期dstRow上的trxId与srcRow上的trxId能匹配上
                return INT_ERR_HEAP_UNEXPECT_ERROR;
            }
        }
        return STATUS_OK_INTER;
    }
}

HOT_FUN_READ StatusInter HeapLocateLinkDstVarRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    if (!HeapIsValidAddr(fetchRowInfo->nextItemPtr)) {
        // 在脏读情况下, 可能查询到未关联 linkDst 的 linkSrc. 当前分析, 应该只有大对象分片有这个状态:
        // 事务A刚刚插入了linkSrc(大对象分片), 但是linkDst和各个subRows还没有插入; 此时, 其他事务扫描到该 linkSrc,
        // 则它的addr无效
        DB_ASSERT(!HeapIsOnNormalTrxLockMode(ctx) && fetchRowInfo->isSliceRows);
        return NO_DATA_HEAP_ITEM_NOT_EXIST;
    }
    StatusInter ret;
    uint32_t tryTimes = 0;
    const uint32_t maxRetry = 10;  // 加上重试上限，避免并发问题导致死循.
    bool isSrcRowReLatch;
    for (; tryTimes < maxRetry; tryTimes++) {
        // 设置访问 dstRow, 继续访问
        fetchRowInfo->curRowInfo = &fetchRowInfo->dstRowInfo;
        fetchRowInfo->curRowInfo->itemPtr = fetchRowInfo->nextItemPtr;
        fetchRowInfo->canAccessLinkDst = true;

        ret = HeapFetchGetPage(ctx, fetchRowInfo);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            DB_LOG_WARN(ret, "get page, labelId:%" PRIu32 ", pageId:%" PRIu32 ", slotId:%" PRIu32, ctx->heapCfg.labelId,
                fetchRowInfo->curRowInfo->itemPtr.pageId, fetchRowInfo->curRowInfo->itemPtr.slotId);
            return ret;
        }

        // 跨页访问, 可能有reLatch. 检查一下 主行(linkSrc)是否有变化
        isSrcRowReLatch = SePgLatchSorterIsPageReLatch(&ctx->pageSorter, fetchRowInfo->srcRowInfo.itemPtr.pageId);
        if (SECUREC_UNLIKELY(isSrcRowReLatch)) {
            // 清除 reLatch标记, 然后重新访问linkSrc, 看看发生改变
            SePgLatchSorterClearPageReLatchState(&ctx->pageSorter, fetchRowInfo->srcRowInfo.itemPtr.pageId);
            bool isPageChange = false;
            ret = HeapReLocateLinkSrcAfterReLatch(ctx, opInfo, &isPageChange);
            if (ret != STATUS_OK_INTER || fetchRowInfo->isGetBuf) {
                return ret;
            }
            if (isPageChange) {  // page改变, 需要重新访问
                continue;
            }
        }
        break;
    }
    if (tryTimes == maxRetry) {
        // 重试达到循环上限,未获取到数据
        DB_LOG_WARN(GMERR_NO_DATA, "fetch tuple's dstRow, labelId:%" PRIu32 ", pageId:%" PRIu32 ", slotId:%" PRIu32,
            ctx->heapCfg.labelId, opInfo->fetchRowInfo->srcRowInfo.itemPtr.pageId,
            opInfo->fetchRowInfo->srcRowInfo.itemPtr.slotId);
        return NO_DATA_HEAP_ITEM_NOT_EXIST;
    }
    // 从page中获取 linkDstRow
    ret = HeapFetchVarRowInPage(ctx, opInfo);
    return HeapLocateLinkDstVarRowCheck(ctx, fetchRowInfo, tryTimes, isSrcRowReLatch, ret);
}

HOT_FUN_READ StatusInter HeapFetchFixLinkRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, bool canAccessLinkDst)
{
    DB_POINTER2(ctx, opInfo);
    StatusInter ret;
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    uint32_t tryTimes = 0;
    const uint32_t maxRetry = 10;  // 加上重试上限，避免并发问题导致死循.
    for (; tryTimes < maxRetry; tryTimes++) {
        ret = HeapLocateLinkDstFixRow(ctx, opInfo);
        if (ret == NO_DATA_HEAP_PAGE_NOT_EXIST) {
            /* 访问dst行时, 发现page不存在
             * 因为发生relatch时，src和dst页的latch都会被释放，有可能被其他并发修改
             * 此时，src和dst页都可能不存在（例如记录被delete），因此直接从linkSrc行直接重新访问
             */
            SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
            fetchRowInfo->curRowInfo = &opInfo->fetchRowInfo->srcRowInfo;  // 从 srcRow 开始查询
            fetchRowInfo->canAccessLinkDst = canAccessLinkDst;
            // 重新访问src page, 并加上对应的锁
            ret = HeapFetchGetPage(ctx, fetchRowInfo);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                return ret;
            }
            // 重新访问src行
            ret = HeapFetchFixRowInPage(ctx, opInfo);
            if (SECUREC_LIKELY(fetchRowInfo->isGetBuf || ret != STATUS_OK_INTER)) {
                return ret;  // 说明src行已经被修改，发生了变化，fetch完成
            }
            // 仍然存在dst行
            DB_ASSERT(fetchRowInfo->is2LinkDstRow);
            if (fetchRowInfo->isOnlyReadSrcRow) {
                return STATUS_OK_INTER;
            }
            continue;  // 重新访问dst行
        }
        break;  // 获取到了fix row.
    }
    if (tryTimes == maxRetry) {
        // 重试达到循环上限,未获取到数据
        DB_LOG_WARN(GMERR_NO_DATA, "fetch tuple, labelId:%" PRIu32 ", pageId:%" PRIu32 ", slotId:%" PRIu32,
            ctx->heapCfg.labelId, opInfo->fetchRowInfo->srcRowInfo.itemPtr.pageId,
            opInfo->fetchRowInfo->srcRowInfo.itemPtr.slotId);
        return NO_DATA_HEAP_ITEM_NOT_EXIST;
    }
    return ret;
}

HOT_FUN_READ StatusInter HeapFetchVarLinkRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, bool canAccessLinkDst)
{
    DB_POINTER2(ctx, opInfo);
    StatusInter resInter;
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    uint32_t tryTimes = 0;
    const uint32_t maxRetry = 10;  // 加上重试上限，避免并发问题导致死循.
    for (; tryTimes < maxRetry; tryTimes++) {
        resInter = HeapLocateLinkDstVarRow(ctx, opInfo);
        if (resInter == NO_DATA_HEAP_PAGE_NOT_EXIST) {
            /* 访问dst行时, 发现page不存在
             * 因为发生relatch时，src和dst页的latch都会被释放，有可能被其他并发修改
             * 此时，src和dst页都可能不存在（例如记录被delete），因此直接从linkSrc行直接重新访问
             */
            SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
            fetchRowInfo->curRowInfo = &opInfo->fetchRowInfo->srcRowInfo;  // 从 srcRow 开始查询
            fetchRowInfo->canAccessLinkDst = canAccessLinkDst;
            // 重新访问src page, 并加上对应的锁
            resInter = HeapFetchGetPage(ctx, fetchRowInfo);
            if (SECUREC_UNLIKELY(resInter != STATUS_OK_INTER)) {
                return resInter;
            }
            // 重新访问src行
            resInter = HeapFetchVarRowInPage(ctx, opInfo);
            if (SECUREC_LIKELY(fetchRowInfo->isGetBuf || resInter != STATUS_OK_INTER)) {
                return resInter;  // 说明src行已经被修改，发生了变化，fetch完成
            }
            // 仍然存在dst行
            DB_ASSERT(fetchRowInfo->is2LinkDstRow);
            if (fetchRowInfo->isOnlyReadSrcRow) {
                return STATUS_OK_INTER;
            }
            continue;  // 重新访问dst行
        }
        break;  // 获取到了var row
    }
    if (tryTimes == maxRetry) {
        // 重试达到循环上限,未获取到数据
        DB_LOG_WARN(GMERR_NO_DATA, "fetch tuple, labelId:%" PRIu32 ", pageId:%" PRIu32 ", slotId:%" PRIu32,
            ctx->heapCfg.labelId, opInfo->fetchRowInfo->srcRowInfo.itemPtr.pageId,
            opInfo->fetchRowInfo->srcRowInfo.itemPtr.slotId);
        return NO_DATA_HEAP_ITEM_NOT_EXIST;
    }
    return resInter;
}

HOT_FUN_READ StatusInter HeapFixedRowFetch(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    StatusInter ret = HeapFetchFixRowInPage(ctx, opInfo);
    if (SECUREC_LIKELY(opInfo->fetchRowInfo->isGetBuf || ret != STATUS_OK_INTER)) {
        /* 若GetBuf，说明该行不用跳转即可读到数据，当前有可能是下列情况：
         * 是normal行，读master version或读版本链
         * 是src行，但是不能读master version，从版本链里读了可见版本
         * 是dst行，且能直接读出dst行内容（当前无此情况）
         */
        return ret;
    }

    // 访问成功, 但是没有获取到buf, 该行是link src行，且读master version
    DB_ASSERT(opInfo->fetchRowInfo->is2LinkDstRow);
    if (SECUREC_LIKELY(opInfo->fetchRowInfo->isOnlyReadSrcRow)) {
        return STATUS_OK_INTER;
    }
    bool canAccessLinkDst = opInfo->fetchRowInfo->canAccessLinkDst;
    // 进入跳转行的访问流程
    return HeapFetchFixLinkRow(ctx, opInfo, canAccessLinkDst);
}

HOT_FUN_READ StatusInter HeapFetchVarRowInPage(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    HpRowSlotT *slot = NULL;
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    HeapRowInfoT *curRowInfo = fetchRowInfo->curRowInfo;
    HVPageHeadT *pageHead = curRowInfo->pageHeadPtr.varPageHead;
    PageSizeT slotOffset;
    StatusInter ret = HeapVarPageGetSlotBySlotId(pageHead, curRowInfo->itemPtr.slotId, &slotOffset, &slot);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    curRowInfo->slotOffset = slotOffset;
    curRowInfo->slot = slot;
    curRowInfo->rowHeadPtr.rowState = HeapVarPageGetRowState(pageHead, slot);
    return HeapFetchVarRowSetRowInfo(ctx, opInfo);
}

StatusInter HeapVarRowRefetch(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    // 根据slot, 重新定位到页内的行.
    HVPageHeadT *pageHead = fetchRowInfo->srcRowInfo.pageHeadPtr.varPageHead;
    fetchRowInfo->srcRowInfo.rowHeadPtr.rowState = HeapVarPageGetRowState(pageHead, fetchRowInfo->srcRowInfo.slot);
    return HeapFetchVarRowSetRowInfo(ctx, opInfo);
}

HOT_FUN_READ StatusInter HeapVarRowFetch(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    bool canAccessLinkDst = opInfo->fetchRowInfo->canAccessLinkDst;

    // fetch var流程里的首次fetch，fetch上层传入的addr，这次fetch里不会发生任何跳转（除了在版本链里读可见版本）
    StatusInter ret = HeapFetchVarRowInPage(ctx, opInfo);
    if (SECUREC_LIKELY(opInfo->fetchRowInfo->isGetBuf || ret != STATUS_OK_INTER)) {
        /* 若GetBuf，说明该行不用跳转即可读到数据，当前有可能是下列情况：
         * 是normal行，读master version或读版本链
         * 是src行，但是不能读master version，从版本链里读了可见版本
         * 是dst行，且能直接读出dst行内容（当前无此情况）
         * 是sub行，且能直接读出sub行内容（当前无此情况）
         */
        return ret;
    }

    // 访问成功, 但是没有获取到buf, 该行是link src行，且读master version
    DB_ASSERT(opInfo->fetchRowInfo->is2LinkDstRow);
    if (opInfo->fetchRowInfo->isOnlyReadSrcRow) {
        return STATUS_OK_INTER;
    }
    if (opInfo->fetchRowInfo->targetRollPtr != UNDO_INVALID_ROLLPTR) {
        // masterVersion是删除的状态时，它的内存可能是已回滚事务的内存，此时不应该去访问dstRow
        if (opInfo->fetchRowInfo->srcRowInfo.rowHeadPtr.rowState->isDeleted) {
            return STATUS_OK_INTER;
        }
    }

    // 进入跳转行的访问流程
    return HeapFetchVarLinkRow(ctx, opInfo, canAccessLinkDst);
}

static StatusInter HeapFetchPrtUpdUndoVersion(HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo)
{
    if (fetchRowInfo->lobBuf == NULL) {
        // 非大对象行的情况, 获取部分更新后的buf的内容
        fetchRowInfo->lobBuf = DbDynMemCtxAlloc(ctx->usrMemCtx, fetchRowInfo->bufSize);
        if (fetchRowInfo->lobBuf == NULL) {
            SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc buf unsucc, size:%" PRIu32 ", labelId:%" PRIu32,
                fetchRowInfo->bufSize, ctx->heapCfg.labelId);
            return OUT_OF_MEMORY_INTER;
        }
        errno_t err = memcpy_s(fetchRowInfo->lobBuf, fetchRowInfo->bufSize, fetchRowInfo->buf, fetchRowInfo->bufSize);
        if (err != EOK) {
            SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "copy buf data unsucc, labelId:%" PRIu32, ctx->heapCfg.labelId);
            DbDynMemCtxFree(ctx->usrMemCtx, fetchRowInfo->lobBuf);
            fetchRowInfo->lobBuf = NULL;
            return MEMORY_OPERATE_FAILED_INTER;
        }
    }
    // 回填部分旧的内容
    HeapRowInfoFromPrtUpdUndoT *partDateFromUndo = &fetchRowInfo->partDateFromUndo;
    errno_t err = memcpy_s((uint8_t *)fetchRowInfo->lobBuf + partDateFromUndo->offsetOfRawData,
        partDateFromUndo->partDataLen, partDateFromUndo->partDataBuf, partDateFromUndo->partDataLen);
    if (err != EOK) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "restore buf data unsucc, labelId:%" PRIu32, ctx->heapCfg.labelId);
        DbDynMemCtxFree(ctx->usrMemCtx, fetchRowInfo->lobBuf);
        fetchRowInfo->lobBuf = NULL;
        return MEMORY_OPERATE_FAILED_INTER;
    }
    fetchRowInfo->buf = fetchRowInfo->lobBuf;
    return STATUS_OK_INTER;
}

HOT_FUN_READ static inline StatusInter HeapFetchBufferPrepare(
    HeapRunCtxT *ctx, const HpPageRowOpInfoT *opInfo, bool *isNeedRetry)
{
    *isNeedRetry = false;
    if (SECUREC_LIKELY(!opInfo->fetchRowInfo->isSliceRows)) {
        if (SECUREC_UNLIKELY(opInfo->fetchRowInfo->isReadFromPrtUpdUndo)) {
            return HeapFetchPrtUpdUndoVersion(ctx, opInfo->fetchRowInfo);
        }
        return STATUS_OK_INTER;
    }

    StatusInter ret = HeapFetchSliceRows(ctx, opInfo->fetchRowInfo);
    if (ret != STATUS_OK_INTER && HeapFetchSliceRowIsNeedTry(opInfo->fetchRowInfo)) {
        // 只有分片读取场景, 由于直连读等场景 和 并发写入, 可能搜索分片过程 发生变化, 导致版本不一致.
        *isNeedRetry = true;
    }

    if (opInfo->fetchRowInfo->isReadFromPrtUpdUndo && ret == STATUS_OK_INTER) {
        ret = HeapFetchPrtUpdUndoVersion(ctx, opInfo->fetchRowInfo);
    }

    return ret;
}

#ifndef FEATURE_SIMPLEREL
HOT_FUN_READ StatusInter HeapFetchFixRowSetRowInfoLite(HpPageRowOpInfoT *opInfo)
{
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    HpPageFetchRowInfoInitState(fetchRowInfo);
    HpRowHeadPtrT rowHeadPtr = fetchRowInfo->curRowInfo->rowHeadPtr;
    HpRowStateT *rowState = rowHeadPtr.rowState;
    if (SECUREC_UNLIKELY(!rowState->isExist)) {
        return NO_DATA_HEAP_ITEM_NOT_EXIST;
    }
    // normal 行 和 linkSrc是外部行, 需要检查事务可见性
    if (SECUREC_LIKELY(HeapPageIsNormalRow(rowState))) {
        fetchRowInfo->rowTrxInfo = rowHeadPtr.normalFixRowHead->trxInfo;
        fetchRowInfo->curRowInfo->rowType = HEAP_FIX_ROW_NORMAL_ROW;
        HeapSetFetchRowInfoWithFixNormalRow(fetchRowInfo);
    } else if (HeapPageIsLinkSrcRow(rowState)) {
        fetchRowInfo->curRowInfo->rowType = HEAP_FIX_ROW_LINK_SRC_ROW;
        fetchRowInfo->rowTrxInfo = rowHeadPtr.linkSrcFixRowHead->trxInfo;
        HeapSetFetchRowInfoWithFixLinkSrcRow(fetchRowInfo);
    } else if (HeapPageIsLinkDstRow(rowState)) {  // 下面是内部的行信息 (linkDst 行), 只要检查控制参数, 判断是否可见
        if (fetchRowInfo->canAccessLinkDst) {
            fetchRowInfo->is2LinkDstRow = true;
            fetchRowInfo->curRowInfo->rowType = HEAP_FIX_ROW_LINK_DST_ROW;
            HeapSetFetchRowInfoWithFixLinkDstRow(fetchRowInfo);
            return STATUS_OK_INTER;
        } else {
            return NO_DATA_HEAP_ITEM_NOT_EXIST;
        }
    }
    return STATUS_OK_INTER;
}

HOT_FUN_READ StatusInter HeapFetchFixRowLite(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    StatusInter ret = HeapFetchFixRowSetRowInfoLite(opInfo);
    if (SECUREC_LIKELY(opInfo->fetchRowInfo->isGetBuf || ret != STATUS_OK_INTER)) {
        return ret;
    }

    DB_ASSERT(opInfo->fetchRowInfo->is2LinkDstRow);
    if (SECUREC_LIKELY(opInfo->fetchRowInfo->isOnlyReadSrcRow)) {
        return STATUS_OK_INTER;
    }
    // 进入跳转行的访问流程
    return HeapFetchFixLinkRow(ctx, opInfo, opInfo->fetchRowInfo->canAccessLinkDst);
}

// fixrow性能考虑 后续fetch过程中不用访问页头。
HOT_FUN_READ StatusInter HeapFetchGetPageFix(HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo)
{
    StatusInter ret;
    HeapRowInfoT *curRowInfo = fetchRowInfo->curRowInfo;
    if (ctx->pageCache.lastPageLogicPtr.pageId == curRowInfo->itemPtr.pageId &&
        ctx->pageCache.lastPageHeadPtr != NULL && ctx->pageCache.cacheVersion == ctx->runtimeInfo->cacheVersion) {
        curRowInfo->pageHeadPtr.pageHead = ctx->pageCache.lastPageHeadPtr;
        if (ctx->pageCache.hcFirstPageHeadPtr == NULL) {
            ctx->pageCache.hcFirstPageHeadPtr = curRowInfo->pageHeadPtr.pageHead;
        }
        curRowInfo->pageAddr = curRowInfo->pageHeadPtr.pageHead->addr;
    } else {
        ret =
            HeapMdGetMemPage(ctx, &curRowInfo->itemPtr, &curRowInfo->pageHeadPtr.pageHead, NULL, &curRowInfo->pageAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "get page(%" PRIu32 ", %" PRIu32 ")", ctx->heapCfg.heapTrmId, curRowInfo->itemPtr.pageId);
            return ret;
        }
        ctx->pageCache.lastPageLogicPtr = curRowInfo->itemPtr;
        ctx->pageCache.lastPageHeadPtr = curRowInfo->pageHeadPtr.pageHead;
        if (ctx->pageCache.hcFirstPageHeadPtr == NULL) {
            ctx->pageCache.hcFirstPageHeadPtr = curRowInfo->pageHeadPtr.pageHead;
        }
        ctx->pageCache.lastPageBlockId = SE_INVALID_BLOCK_ID;
        ctx->pageCache.cacheVersion = ctx->runtimeInfo->cacheVersion;
    }

    return HeapPageAddLatch(ctx, curRowInfo);
}

static inline PageSizeT HeapFixGetRowHeadOffsetWithPageInfo(const StaticPageInfoT pageInfo, uint32_t slotId)
{
    return (PageSizeT)slotId * pageInfo.oneRowOffset + pageInfo.firstRowHeadOffset;
}

HOT_FUN_READ StatusInter HeapFixPageGetRowHeadLite(
    HFPageHeadT *pageHead, StaticPageInfoT pageInfo, uint32_t slotId, HpNormalFixRowHead **rowHead)
{
    DB_POINTER2(pageHead, rowHead);
    if (SECUREC_UNLIKELY(slotId >= pageInfo.rowCnt)) {
        SE_LAST_ERROR(
            NO_DATA_HEAP_ITEM_OFFSET_INVALID, "fix rowId:%" PRIu32 ", rowCnt:%" PRIu16, slotId, pageInfo.rowCnt);
        return NO_DATA_HEAP_ITEM_OFFSET_INVALID;
    }
    *rowHead = (HpNormalFixRowHead *)((uintptr_t)pageHead + HeapFixGetRowHeadOffsetWithPageInfo(pageInfo, slotId));
    return STATUS_OK_INTER;
}

// isPageReadOnly为真 因此不调用HeapRunCtxGetHeap
HOT_FUN_READ StatusInter HeapFetchRowLite(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    StatusInter ret = STATUS_OK_INTER;
    if (ctx->hpControl.isFixPage) {
        // 第一次fetch表或发生过表升降级或非优化场景MainStoreVertexLookUpHashOpt调用 将页头信息写在缓存中
        HeapRowInfoT *curRowInfo = opInfo->fetchRowInfo->curRowInfo;
        if (SECUREC_UNLIKELY(!ctx->staticPageInfo.isUseCache || ctx->staticPageInfo.rowCnt == 0 ||
                             ctx->pageCache.lastPageLogicPtr.pageId != curRowInfo->itemPtr.pageId)) {
            // 将pageHead信息写在ctx缓存中, 避免重复读取
            ret = HeapFetchGetPageFix(ctx, opInfo->fetchRowInfo);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                return ret;
            }
            HFPageHeadT *pageHead = curRowInfo->pageHeadPtr.fixPageHead;
            ctx->staticPageInfo.rowCnt = pageHead->pageCfg.rowCnt;
            ctx->staticPageInfo.oneRowOffset = pageHead->pageCfg.oneRowSize + pageHead->pageCfg.slotExtendSize;
            ctx->staticPageInfo.firstRowHeadOffset = pageHead->pageCfg.rowBegin + pageHead->pageCfg.slotExtendSize;
        } else {
#ifndef NDEBUG
            ret = HeapFetchGetPageFix(ctx, opInfo->fetchRowInfo);
            DB_ASSERT(ret == STATUS_OK_INTER);
            HFPageHeadT *pageHead = curRowInfo->pageHeadPtr.fixPageHead;
            DB_ASSERT(ctx->pageCache.lastPageHeadPtr != NULL);
            DB_ASSERT(ctx->staticPageInfo.rowCnt == pageHead->pageCfg.rowCnt);
            DB_ASSERT(
                ctx->staticPageInfo.oneRowOffset == pageHead->pageCfg.oneRowSize + pageHead->pageCfg.slotExtendSize);
            DB_ASSERT(ctx->staticPageInfo.firstRowHeadOffset ==
                      pageHead->pageCfg.rowBegin + pageHead->pageCfg.slotExtendSize);
#endif
            curRowInfo->pageHeadPtr.pageHead = ctx->pageCache.lastPageHeadPtr;
        }
        // 直接从缓存中读 避免访问页头发生cache miss
        ret = HeapFixPageGetRowHeadLite(curRowInfo->pageHeadPtr.fixPageHead, ctx->staticPageInfo,
            curRowInfo->itemPtr.slotId, &curRowInfo->rowHeadPtr.normalFixRowHead);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
        return HeapFetchFixRowLite(ctx, opInfo);
    }

    ret = HeapFetchGetPage(ctx, opInfo->fetchRowInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    return HeapVarRowFetch(ctx, opInfo);
}

HOT_FUN_READ StatusInter HeapFetchLite(
    HeapRunCtxT *ctx, HpItemPointerT itemPtr, const HeapPageReadRowProc func, void *userData)
{
    StatusInter ret;
    ctx->hpControl.isPageReadOnly = true;
    ctx->hpControl.trxContrl.hpAmIndex = HEAP_TRX_AM_NORMALREAD;
    bool isNeedRetry;
    for (uint32_t tryFetchCnt = 0; tryFetchCnt < MAX_TRY_FETCH_LOOP; tryFetchCnt++) {
        HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
        fetchRowInfo.srcRowInfo.itemPtr = itemPtr;
        fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;
        HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, NULL);

        ret = HeapFetchRowLite(ctx, &opInfo);
        if (SECUREC_LIKELY(fetchRowInfo.isGetBuf)) {
            DB_ASSERT(ret == STATUS_OK_INTER);
            ret = HeapFetchBufferPrepare(ctx, &opInfo, &isNeedRetry);
            if (SECUREC_LIKELY(ret == STATUS_OK_INTER && func)) {
                HpReadRowInfoT readRowInfo = {.trxId = RowGetTrxId(&fetchRowInfo.rowTrxInfo),
                    .buf = fetchRowInfo.buf,
                    .bufSize = fetchRowInfo.bufSize};
                ret = func(&readRowInfo, userData);
            }
        }
        SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
        HeapFetchClearTempSliceMem(ctx, opInfo.fetchRowInfo);
        if (SECUREC_LIKELY(!isNeedRetry)) {
            break;
        }
    }

    return ret;
}

#endif

/* 一个heap的addr, 本函数最多获取到 linkDst, 对于大对象, 只是获取到了 sliceDir! 没有获取到各个分片的buffer */
HOT_FUN_READ StatusInter HeapFetchRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    // Normal模式下: HeapAcquireRowLockOnFetch (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
    StatusInter ret = HeapAcquireRowLockFunc(ctx, HEAP_TRX_AM_NORMALREAD, opInfo->fetchRowInfo->srcRowInfo.itemPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

#ifndef ENABLE_HEAP_MEM
    // 仅用于原子范围并发控制，无直接使用, 仅heap使用
    HeapT *heap = NULL;
    if (!ctx->hpControl.isPageReadOnly && !ctx->enableCacheHeap) {
        ret = HeapRunCtxGetHeap(ctx, true, &heap);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
#endif

    ret = HeapFetchGetPage(ctx, opInfo->fetchRowInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        goto RELEASE;
    }

    if (ctx->hpControl.isFixPage) {
        ret = HeapFixedRowFetch(ctx, opInfo);
    } else {
        ret = HeapVarRowFetch(ctx, opInfo);
    }
RELEASE:
#ifndef ENABLE_HEAP_MEM
    if (heap != NULL) {
        HeapRunCtxLeaveHeap(ctx, false);
    }
#endif
    return ret;
}

inline static bool IsSelfDeletedBuf(HpPageFetchRowInfo *fetchRowInfo)
{
    DB_POINTER(fetchRowInfo);
    if (fetchRowInfo->isCanAccessSelfDeletedBuf) {
        if (fetchRowInfo->isSelfDelBuf) {
            return true;
        }
    }
    return false;
}

inline static StatusInter HeapFetchPrintErrlog(
    HeapRunCtxT *ctx, HpItemPointerT itemPtr, StatusInter ret, uint32_t tryFetchCnt, bool isGetBuf)
{
    if (SECUREC_LIKELY(ret == STATUS_OK_INTER)) {
        return ret;
    }
    if (((TrxT *)ctx->seRunCtx->trx)->base.state == TRX_STATE_ROLLBACK ||
        ((TrxT *)ctx->seRunCtx->trx)->base.state == TRX_STATE_COMMITTED) {
        SE_ERROR(ret,
            "fetch, ret:%" PRIu32 ", labelId:%" PRIu32 ", itemPtr(%" PRIu32 ",%" PRIu32 "), isGetBuf:%" PRIu32,
            (uint32_t)ret, ctx->heapCfg.labelId, itemPtr.pageId, itemPtr.slotId, (uint32_t)isGetBuf);
    }
    if (ret != INT_ERR_HEAP_LOB_DATA_INCOMPLETE && tryFetchCnt >= MAX_TRY_FETCH_LOOP) {
        SE_ERROR(NO_DATA_HEAP_ITEM_NOT_EXIST,
            "fetch much, ret:%" PRIu32 ", labelId:%" PRIu32 ", itemPtr(%" PRIu32 ",%" PRIu32 ")", (uint32_t)ret,
            ctx->heapCfg.labelId, itemPtr.pageId, itemPtr.slotId);
        return NO_DATA_HEAP_ITEM_NOT_EXIST;
    }
    return ret;
}

#ifdef FEATURE_SIMPLEREL
HOT_FUN_READ StatusInter HeapFetchSliceRowByLinkRowHead(
    HeapRunCtxT *ctx, HpItemPointerT itemPtr, HpLinkRowHeadT *rowHead, const HeapPageReadRowProc func, void *userData)
{
    DB_POINTER2(ctx, rowHead);
    DB_ASSERT(rowHead->rowState.isLinkSrc && rowHead->magicNum == HEAP_ROW_MAGIC_NUM);
    bool isNeedRetry = false;
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;
    fetchRowInfo.curRowInfo->rowHeadPtr.rowState = (void *)rowHead;
    HeapSetFetchRowTrxInfoAndType(&fetchRowInfo, HEAP_VAR_ROW_LINK_SRC_ROW, rowHead->srcTrxInfo);
    HeapSetFetchRowInfoWithLinkSrcRow(&fetchRowInfo);

    HeapSetPageLatchActionForRead(ctx);
    HeapSetOperationForFetch(ctx);
    HeapSetLockFunc(ctx, HEAP_TRX_AM_NORMALREAD);
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, NULL);
    StatusInter ret = HeapFetchVarLinkRow(ctx, &opInfo, true);
    if (SECUREC_LIKELY(fetchRowInfo.isGetBuf)) {
        DB_ASSERT(ret == STATUS_OK_INTER);
        ret = HeapFetchBufferPrepare(ctx, &opInfo, &isNeedRetry);
        if (SECUREC_LIKELY(ret == STATUS_OK_INTER && func)) {
            HpReadRowInfoT readRowInfo = {.trxId = RowGetTrxId(&fetchRowInfo.rowTrxInfo),
                .buf = fetchRowInfo.buf,
                .bufSize = fetchRowInfo.bufSize,
                .isGetSelfDeletedBuf = IsSelfDeletedBuf(&fetchRowInfo)};
            ret = func(&readRowInfo, userData);
        }
    }
    HeapFetchClearTempSliceMem(ctx, opInfo.fetchRowInfo);
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    return HeapFetchPrintErrlog(ctx, itemPtr, ret, 1, fetchRowInfo.isGetBuf);
}

static StatusInter HeapFetchLinkRowHeadInner(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    StatusInter ret = HeapFetchGetPage(ctx, opInfo->fetchRowInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    return HeapFetchVarRowInPage(ctx, opInfo);
}

HOT_FUN_READ StatusInter HeapFetchLinkRowHead(
    HeapRunCtxT *ctx, HpItemPointerT itemPtr, const HeapPageReadRowProc func, void *userData)
{
    DB_POINTER2(ctx, ctx->seRunCtx);
    StatusInter ret;
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, NULL);
    fetchRowInfo.srcRowInfo.itemPtr = itemPtr;
    fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;

    HeapSetPageLatchActionForRead(ctx);
    HeapSetOperationForFetch(ctx);
    HeapSetLockFunc(ctx, HEAP_TRX_AM_NORMALREAD);

    ret = HeapFetchLinkRowHeadInner(ctx, &opInfo);
    if (ret == STATUS_OK_INTER || ret == NO_DATA_HEAP_ITEM_NOT_EXIST) {  // 允许获取不可见数据
        DB_ASSERT(fetchRowInfo.curRowInfo->rowHeadPtr.rowState->isLinkSrc);
        HpReadRowInfoT readRowInfo = {.trxId = RowGetTrxId(&fetchRowInfo.rowTrxInfo),
            .buf = (void *)fetchRowInfo.curRowInfo->rowHeadPtr.rowState,  // 获取link src row head
            .bufSize = fetchRowInfo.bufSize,
            .isGetSelfDeletedBuf = IsSelfDeletedBuf(&fetchRowInfo)};
        ret = func(&readRowInfo, userData);
    }

    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    HeapFetchClearTempSliceMem(ctx, opInfo.fetchRowInfo);
    return HeapFetchPrintErrlog(ctx, itemPtr, ret, 1, fetchRowInfo.isGetBuf);
}

HOT_FUN_READ StatusInter HeapFetchFixedPageFreeSlotInfo(HpRunHdlT heapRunHdl, uint32_t compressPageId,
    PageAddrT *addrOut, uint32_t *freeSlotCnt, HFPageHeadT **compPageHead)
{
    uint32_t blockCnt = LfsGetBlockCnt(heapRunHdl->fsmMgr);
    for (uint32_t i = 0; i < blockCnt; ++i) {
        PageAddrT addr;
        StatusInter ret = LfsTryGetSpecificBlock(heapRunHdl->seRunCtx->pageMgr, heapRunHdl->fsmMgr, i, false, &addr);
        if (ret != STATUS_OK_INTER) {
            if (ret == INT_ERR_LFS_BLOCK_HAS_RELEASED) {
                continue;
            }
            return ret;
        }
        // 获取页
        HpItemPointerT itemPointer = SE_INVALID_HEAP_ITEM_POINTER;
        itemPointer.pageId = addr.pageId;
        HpPageHeadPtrT hfPageHead;
        PageIdT currPageId = SE_INVALID_PAGE_ADDR;
        ret = HeapMdGetMemPage(heapRunHdl, &itemPointer, &hfPageHead.pageHead, NULL, &currPageId);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Get page unsucc when fetch page compress info.");
            return ret;
        }
        *freeSlotCnt += hfPageHead.fixPageHead->freeSlotCnt;
        if (addr.pageId == compressPageId) {
            addrOut->blockId = addr.blockId;
            addrOut->pageId = addr.pageId;
            *compPageHead = hfPageHead.fixPageHead;
        }
    }
    return STATUS_OK_INTER;
}

static void HeapSetSimpleCompressInfo(HpRunHdlT heapRunHdl, HeapFetchedCompressInfoT *pageCompInfo)
{
    pageCompInfo->releasePageCnt = heapRunHdl->fsmMgr->releasedPageCnt;
}

static StatusInter HeapFetchCompressStageOneInfo(HpRunHdlT heapRunHdl, HeapFetchedCompressInfoT *pageCompInfo)
{
    // 一阶段压缩信息统计
    // 1. 优先从fsm高级链表中获取used状态待释放的页的pageId
    uint32_t compressPageId;
    StatusInter ret = LfsTryGetCompressPage(heapRunHdl->seRunCtx->mdMgr, heapRunHdl->fsmMgr, &compressPageId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Get compress page unsucc.");
        return ret;
    }
    uint32_t freeSlotCnt = 0;  // 可压缩数
    PageAddrT addrOut;
    HFPageHeadT *compPageHead = NULL;  // 待释放页
    // 2. 统计页的空闲情况
    ret = HeapFetchFixedPageFreeSlotInfo(heapRunHdl, compressPageId, &addrOut, &freeSlotCnt, &compPageHead);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Get compress page free slot info unsucc.");
        return ret;
    }
    freeSlotCnt -= compPageHead->freeSlotCnt;
    pageCompInfo->compressCnt = freeSlotCnt;
    pageCompInfo->freeCond = addrOut.blockId;
    pageCompInfo->freePageId = addrOut.pageId;
    // 更新一阶段可用于压缩的 free cnt 数量
    pageCompInfo->pageUseCnt = compPageHead->pageCfg.rowCnt - compPageHead->freeSlotCnt;
    pageCompInfo->fixPageRowCnt = compPageHead->pageCfg.rowCnt;
    return STATUS_OK_INTER;
}

HOT_FUN_READ StatusInter HeapCompressStageThree(HpRunHdlT heapRunHdl)
{
    MdMgrT *md = heapRunHdl->seRunCtx->mdMgr;
    LfsMgrT *mgr = heapRunHdl->fsmMgr;
    DbInstanceHdT dbInstance = ((SeInstanceT *)(heapRunHdl->seRunCtx->seIns))->dbInstance;
    return LfsMgrPageMigrate(md, mgr, dbInstance);
}

static StatusInter HeapFetchCompressStageTwoInfo(HpRunHdlT heapRunHdl, HeapFetchedCompressInfoT *pageCompInfo)
{
    // 二阶段压缩信息统计,统计所有页的device信息,找最空闲deviceId
    uint32_t freeDeviceId = 0;
    uint32_t fullDeviceId = 0;  // 记录最满的非满deviceId
    uint32_t secPageId = 0;
    StatusInter ret = LfsTryGetCompressDeviceId(heapRunHdl, &freeDeviceId, &fullDeviceId, &secPageId);
    if (ret != STATUS_OK_INTER) {
        if (ret == INT_ERR_INVALID_DEV_ID) {
            // 二阶段压缩结束
            pageCompInfo->isCompressEnd = true;
            return STATUS_OK_INTER;
        }
        SE_ERROR(ret, "Get compress page device info unsucc.");
        return ret;
    }
    if (freeDeviceId == fullDeviceId) {
        if (freeDeviceId < DEV_CACHE_CAPACITY) {
            // 二阶段压缩结束
            pageCompInfo->isCompressEnd = true;
            return STATUS_OK_INTER;
        }
        for (uint32_t i = 0; i < DEV_CACHE_CAPACITY; ++i) {
            uint32_t freeChunk = DevNodeGetFreeChunkCnt(((MdMgrT *)(heapRunHdl->seRunCtx->mdMgr))->devMgr, i);
            if (freeChunk > 0) {
                break;
            }
            if (i == DEV_CACHE_CAPACITY - 1) {
                pageCompInfo->isCompressEnd = true;
                return STATUS_OK_INTER;
            }
        }
    }

    // 获取页
    HpItemPointerT itemPointer = SE_INVALID_HEAP_ITEM_POINTER;
    itemPointer.pageId = secPageId;
    HFPageHeadT *hfPageHead = NULL;
    PageIdT currPageId = SE_INVALID_PAGE_ADDR;

    ret = HeapMdGetMemPage(heapRunHdl, &itemPointer, (PageHeadT **)&hfPageHead, NULL, &currPageId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Get page unsucc when fetch page by device Id.");
        return ret;
    }
    // 其他页可压缩数量,二阶段统一设置为一整页大小,确保可以正常压缩
    pageCompInfo->freePageId = secPageId;
    pageCompInfo->freeCond = hfPageHead->baseHead.slotIdx;
    pageCompInfo->pageUseCnt = hfPageHead->pageCfg.rowCnt - hfPageHead->freeSlotCnt;
    return STATUS_OK_INTER;
}

HOT_FUN_READ StatusInter HeapFetchPageCompressInfo(
    HpRunHdlT heapRunHdl, bool isGetDetail, HeapFetchedCompressInfoT *pageCompInfo)
{
    DB_POINTER2(heapRunHdl, pageCompInfo);
    // 非fixPage类型或者used页小于等于1,不压缩直接返回
    if (!heapRunHdl->hpControl.isFixPage || LfsGetUsedBlockCnt(heapRunHdl->fsmMgr) <= 1) {
        pageCompInfo->compressCnt = 0;
        return STATUS_OK_INTER;
    }
    HeapSetSimpleCompressInfo(heapRunHdl, pageCompInfo);
    if (!isGetDetail) {
        return STATUS_OK_INTER;
    }
    if (!pageCompInfo->isAllocNewPage) {
        return HeapFetchCompressStageOneInfo(heapRunHdl, pageCompInfo);
    }
    return HeapFetchCompressStageTwoInfo(heapRunHdl, pageCompInfo);
}
#endif

HOT_FUN_READ StatusInter HeapFetch(
    HeapRunCtxT *ctx, HpItemPointerT itemPtr, const HeapPageReadRowProc func, void *userData)
{
    DB_POINTER2(ctx, ctx->seRunCtx);
    StatusInter ret;
// 轻量化事务直连读性能优化路径
#ifndef FEATURE_SIMPLEREL
    if (HeapUseLiteTrxMethod(ctx)) {
        return HeapFetchLite(ctx, itemPtr, func, userData);
    }
#endif
    uint32_t tryFetchCnt = 0;
    bool isNeedRetry = false;
    HpPageFetchRowInfo fetchRowInfo;
    for (; tryFetchCnt < MAX_TRY_FETCH_LOOP; tryFetchCnt++) {
        fetchRowInfo = EmptyHpPageFetchRowInfo();
        HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, NULL);
        fetchRowInfo.srcRowInfo.itemPtr = itemPtr;
        fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;
        fetchRowInfo.isToGetOldestVisibleBuf = ctx->hpControl.isToGetOldestVisibleBuf;
        fetchRowInfo.isCanAccessSelfDeletedBuf = ctx->hpControl.isCanAccessSelfDeletedBuf;
        fetchRowInfo.isReadLatestCommittedBuf = ctx->hpControl.isReadLatestCommittedBuf;
        HeapSetPageLatchActionForRead(ctx);
        HeapSetOperationForFetch(ctx);
        // Normal模式下: HeapSetLockActionBeforeFetchRow (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
        HeapSetLockFunc(ctx, HEAP_TRX_AM_NORMALREAD);
        ret = HeapFetchRow(ctx, &opInfo);
        if (SECUREC_LIKELY(fetchRowInfo.isGetBuf)) {
            DB_ASSERT(ret == STATUS_OK_INTER);
            ret = HeapFetchBufferPrepare(ctx, &opInfo, &isNeedRetry);
            if (SECUREC_LIKELY(ret == STATUS_OK_INTER && func)) {
                HpReadRowInfoT readRowInfo = {.trxId = RowGetTrxId(&fetchRowInfo.rowTrxInfo),
                    .buf = fetchRowInfo.buf,
                    .bufSize = fetchRowInfo.bufSize,
                    .isGetSelfDeletedBuf = IsSelfDeletedBuf(&fetchRowInfo)};
                ret = func(&readRowInfo, userData);
            }
        }
        SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
        HeapFetchClearTempSliceMem(ctx, opInfo.fetchRowInfo);
        if (SECUREC_LIKELY(!isNeedRetry)) {
            break;  // 一般读取, 只处理一次.
        }
    }
    return HeapFetchPrintErrlog(ctx, itemPtr, ret, tryFetchCnt, fetchRowInfo.isGetBuf);
}

StatusInter HeapBatchFetch(HeapRunCtxT *ctx, HpItemPointerT *itemPtrs, uint32_t count,
    const HeapPageReadBatchRowsProc func, void *userData, void **rows)
{
    DB_POINTER5(ctx, ctx->seRunCtx, itemPtrs, userData, rows);
    if (SECUREC_UNLIKELY(!ctx->hpControl.isFixPage || ctx->offset == NULL)) {
        return INTERNAL_ERROR_INTER;
    }
    // fetch数据
    ctx->hpControl.isPageReadOnly = true;
    ctx->isTryGetPage = true;
    SePageRecorderT pageRecorder = {0};
    StatusInter ret = SePageRecorderInit(&pageRecorder, ctx->seRunCtx->sessionMemCtx, sizeof(PageRecorderItemT), count);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    uint32_t index = 0;
    while (index < count) {
        for (uint32_t i = index; i < count; i++) {
            HpPageFetchRowInfo fetchRowInfo = {0};
            fetchRowInfo.srcRowInfo.itemPtr = itemPtrs[i];
            fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;
            ret = HeapFetchTryGetPage(ctx, &fetchRowInfo);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                if (ret == NO_DATA_BUFFER_POOL_IS_FULL) {
                    break;
                }
                SePageRecorderReleasePage(&pageRecorder, ctx->pageMgr);
                return ret;
            }
            HeapRowInfoT *curRowInfo = fetchRowInfo.curRowInfo;
            HpNormalFixRowHead *fixRowHead = (HpNormalFixRowHead *)((uintptr_t)curRowInfo->pageHeadPtr.fixPageHead +
                                                                    ctx->offset[curRowInfo->itemPtr.slotId]);
            rows[i] = (void *)(fixRowHead + 1);
            // 使用pagerecorder记录pageId
            ret = SePageRecorderAddPage(
                &pageRecorder, curRowInfo->pageAddr, (uint8_t *)(void *)curRowInfo->pageHeadPtr.fixPageHead);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                SePageRecorderReleasePage(&pageRecorder, ctx->pageMgr);
                return ret;
            }
        }
        // 调用回调方法，计算向量距离
        func(rows, userData, index, index + pageRecorder.pageNum);
        index += pageRecorder.pageNum;
        // leave page 通过pagerecorder统一leave page
        SePageRecorderReleasePage(&pageRecorder, ctx->pageMgr);
    }
    return STATUS_OK_INTER;
}

inline StatusInter HeapLabelFetchHpTupleInner(
    const HpRunHdlT heapRunHdl, HpTupleAddr heapTupleAddr, HeapTupleT *heapTuple)
{
    DB_POINTER2(heapRunHdl, heapTuple);
    HpAmFetchTupleT hpFmtTupInfo = {
        .heapRunHdl = heapRunHdl,
        .heapTuple = heapTuple,
    };
    return HeapFetch(
        heapRunHdl, *(HpItemPointerT *)(void *)&heapTupleAddr, HeapLabelDeSerialHpTuple, (void *)&hpFmtTupInfo);
}

StatusInter HeapFetchNormalTpBufWithRollPtr(HeapRunCtxT *ctx, HeapTupleBufT *heapTupleBuf, HpRowHeadPtrT *rowHeadPtr)
{
    DB_POINTER3(ctx, heapTupleBuf, rowHeadPtr);
    uint8_t *src;
    if (ctx->hpControl.isFixPage) {
        src = (uint8_t *)(rowHeadPtr->normalFixRowHead + 1);
        heapTupleBuf->bufSize = rowHeadPtr->normalFixRowHead->size;
    } else {
        src = (uint8_t *)(rowHeadPtr->normalRowHead + 1);
        heapTupleBuf->bufSize = rowHeadPtr->normalRowHead->size;
    }
    // 申请内存将共享内存数据拷贝一份, 由上层调用者调用UnlinkHeapFreeTupleBuf释放
    heapTupleBuf->buf = DbDynMemCtxAlloc(ctx->usrMemCtx, heapTupleBuf->bufSize);
    if (heapTupleBuf->buf == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc:%" PRIu32 " in copy tuple", heapTupleBuf->bufSize);
        return OUT_OF_MEMORY_INTER;
    }
    errno_t err = memcpy_s(heapTupleBuf->buf, heapTupleBuf->bufSize, src, heapTupleBuf->bufSize);
    if (err != EOK) {
        DbDynMemCtxFree(ctx->usrMemCtx, heapTupleBuf->buf);
        heapTupleBuf->buf = NULL;
        heapTupleBuf->bufSize = 0;
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "cpy");
        return MEMORY_OPERATE_FAILED_INTER;
    }
    return STATUS_OK_INTER;
}

static StatusInter HeapFetchTpBufWithRowHeadPtr(HeapRunCtxT *ctx, HpFetchInfoWithRollPtrT *tupleBufInfo,
    HpRowHeadPtrT *rowHeadPtr, UndoTupleCombineAddrT *undoTuple)
{
    StatusInter ret;
    HeapTupleBufT *heapTupleBuf = tupleBufInfo->heapTupleBuf;
    if (HeapPageIsNormalRow(rowHeadPtr->rowState)) {
        ret = HeapFetchNormalTpBufWithRollPtr(ctx, heapTupleBuf, rowHeadPtr);
        return ret;
    }
    bool isLinkSrcRow = HeapPageIsLinkSrcRow(rowHeadPtr->rowState);
    DB_ASSERT(isLinkSrcRow);
    HpItemPointerT linkItemPtr =
        ctx->hpControl.isFixPage ? rowHeadPtr->linkSrcFixRowHead->linkItemPtr : rowHeadPtr->linkRowHead->linkItemPtr;
    RowTrxIdT srcTrxId = ctx->hpControl.isFixPage ? rowHeadPtr->linkSrcFixRowHead->trxInfo.trxId :
                                                    rowHeadPtr->linkRowHead->srcTrxInfo.trxId;
    // 访问dst前, src所在页就可以释放掉了
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    ret = HeapFetchBufferForLinkDstRow(ctx, linkItemPtr, srcTrxId, heapTupleBuf);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        // noteb: 有可能由于对象比较大, 内存申请不到, 导致获取失败.
        // 该函数当前用于事务提交时, 旧索引回收, 由此导致的失败, 后续考虑 延迟回收 或者 内存整理时的扫描进行消除.
        SE_ERROR(ret, "get buf in commit clean, labelId %" PRIu32 "", ctx->heapCfg.labelId);
    }
    return ret;
}

StatusInter HeapFetchBufferForLinkDstRow(
    HeapRunCtxT *ctx, HpItemPointerT linkItemPtr, RowTrxIdT srcTrxId, HeapTupleBufT *tupleBuf)
{
    DB_POINTER2(ctx, tupleBuf);
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, NULL);

    fetchRowInfo.srcRowInfo.itemPtr = linkItemPtr;

    fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;
    HpPageFetchRowInfoInitCtrlFlag(&fetchRowInfo);
    fetchRowInfo.canAccessLinkDst = true;
    HeapSetPageLatchActionForRead(ctx);
    HeapSetLockActionBeforeRollBackUpdate(ctx);  // commit 和 rollback 都是一样的加锁行为.
    // 先查询, 获取 当前 row的信息
    StatusInter ret = HeapFetchRow(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
        DB_ASSERT(false);  // 不应该出现. 需要定位
        return ret;
    }
    DB_ASSERT(fetchRowInfo.srcRowInfo.rowType == HEAP_FIX_ROW_LINK_DST_ROW ||
              fetchRowInfo.srcRowInfo.rowType == HEAP_VAR_ROW_LINK_DST_ROW ||
              fetchRowInfo.srcRowInfo.rowType == HEAP_VAR_ROW_LINK_DST_SLICE_DIR_ROW);
    // 拷贝事务版本, 用于后面校验 (分行 也有校验)
    fetchRowInfo.rowTrxInfo.trxId = HeapFetchBufferGetTrxId(ctx, fetchRowInfo);

    if (fetchRowInfo.isSliceRows) {
        ret = HeapFetchSliceRows(ctx, &fetchRowInfo);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
            return ret;
        }
        tupleBuf->bufSize = fetchRowInfo.bufSize;
        tupleBuf->buf = fetchRowInfo.lobBuf;
        HeapFetchClearSliceDir(ctx, &fetchRowInfo);
    } else {
        bool isSameTrxId = HeapRowIsSameTrxId(srcTrxId, fetchRowInfo.rowTrxInfo.trxId);
        DB_ASSERT(isSameTrxId);
        tupleBuf->bufSize = fetchRowInfo.bufSize;
        // 申请一块内存临时拷贝共享内存数据,
        // 由上层调用者调用HeapCommitHistoryBufferRelease或者UnlinkHeapFreeTupleBuf释放
        tupleBuf->buf = DbDynMemCtxAlloc(ctx->usrMemCtx, fetchRowInfo.bufSize);
        if (tupleBuf->buf == NULL) {
            SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
            SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc(%" PRIu32 ") in copy tuple", fetchRowInfo.bufSize);
            return OUT_OF_MEMORY_INTER;
        }
        errno_t err = memcpy_s(tupleBuf->buf, fetchRowInfo.bufSize, fetchRowInfo.buf, fetchRowInfo.bufSize);
        DB_ASSERT(err == EOK);
    }
    // 操作结束后, 释放过程中涉及的page
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    return STATUS_OK_INTER;
}

StatusInter HeapFetchTpBufWithRollPtr(HeapRunCtxT *ctx, UndoRowOpInfoT *rowOpInfo, HpPageFetchRowInfo *fetchRowInfo,
    HpFetchInfoWithRollPtrT *tupleBufInfo)
{
    DB_POINTER4(ctx, rowOpInfo, fetchRowInfo, tupleBufInfo);
    StatusInter ret;
    HpRowHeadPtrT rowHeadPtr;
    UndoTupleCombineAddrT undoTuple = {0};
    if (tupleBufInfo->targetRollPtr == fetchRowInfo->rowTrxInfo.rollPtr) {
        rowHeadPtr = fetchRowInfo->srcRowInfo.rowHeadPtr;
    } else {
        ret = RowUndoFetchHpBufFromTargetRecord(ctx->seRunCtx->undoCtx, fetchRowInfo->rowTrxInfo.rollPtr, rowOpInfo,
            tupleBufInfo->targetRollPtr, &undoTuple);
        if (ret != STATUS_OK_INTER || undoTuple.tuple.data == NULL) {
            SE_ERROR(ret, "get target record buf. labelId %" PRIu32 "", ctx->heapCfg.labelId);
            return ret;
        }
        rowHeadPtr.rowState = (HpRowStateT *)undoTuple.tuple.data;
    }
    ret = HeapFetchTpBufWithRowHeadPtr(ctx, tupleBufInfo, &rowHeadPtr, &undoTuple);
    RowUndoReleaseTargetRecord(ctx->seRunCtx->undoCtx, &undoTuple);
    return ret;
}

/*
为什么要FetchWithRollPtr ?
这里是乐观事务场景，为了拿到更新后的key，与更新前的key进行比较，看是否发生了变化，避免误删其他事务的索引entry
eg:
                     trx3写的undo    trx2写的undo      trx1写的undo
-----------------    ------------    -----------       ------------
| masterVersion |--->| undoLog2 |--->| undoLog1 |----->| undoLog0 |
-----------------    ------------    -----------       ------------
    trx3的修改         trx2的修改    trx1的修改(更新)  trx0插入的版本

假设当前场景是trx3、trx2在trx1提交后才开始的事务，因此trx0插入的版本是没人读的旧版本
这里gc线程来回收旧版本, 当前处理的是trx1写的undo, targetRollPtr是trx1写的undo的addr（undoLog0的addr）：

需要读出masterVersion，将上面的rollPtr（undoLog2的addr）与targetRollPtr进行比较，在此场景下发现不相同，
开始遍历版本链，找到undoLog2里面记录的rollPtr（undoLog1的addr）与targetRollPtr比较，也没匹配成功，
继续查找，找到undoLog1里面记录的rollPtr（undoLog0的addr）与targetRollPtr比较，成功匹配。
此时，就读取undoLog1上的内容，就能读出更新后的值。
*/
StatusInter HeapFetchWithRollPtr(
    HeapRunCtxT *ctx, HpTupleAddr heapTupleAddr, UndoRowOpInfoT *rowOpInfo, HpFetchInfoWithRollPtrT *tupleBufInfo)
{
    DB_POINTER3(ctx, rowOpInfo, tupleBufInfo);
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, NULL);
    fetchRowInfo.srcRowInfo.itemPtr = *(HpItemPointerT *)&heapTupleAddr;
    fetchRowInfo.isOnlyReadSrcRow = true;
    fetchRowInfo.isCanAccessMarkDelRow = true;
    fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;
    DB_ASSERT(ctx->hpOperation == HEAP_OPTYPE_UNDO_PURGER);
    HeapSetPageLatchActionForRead(ctx);
    HeapSetLockActionBeforeGcUnlinkRollPtr(ctx);
    StatusInter ret = HeapFetchRow(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
        SE_ERROR(ret, "target rollPtr get record buf. labelId %" PRIu32 "", ctx->heapCfg.labelId);
        return ret;
    }

    ret = HeapFetchTpBufWithRollPtr(ctx, rowOpInfo, &fetchRowInfo, tupleBufInfo);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "target rollPtr get record buf. labelId %" PRIu32, ctx->heapCfg.labelId);
    }

    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    return ret;
}

StatusInter HeapFindUnlinkCutoffWithTargetTrxIds(
    HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo, TrxIdListT *targetTrxIds, uint64_t *targetRollPtr)
{
    DB_POINTER3(ctx, fetchRowInfo, targetRollPtr);
    StatusInter ret;
    if (TrxIdListLookup(targetTrxIds, RowGetTrxId(&fetchRowInfo->rowTrxInfo)) &&
        !fetchRowInfo->srcRowInfo.rowHeadPtr.rowState->isDeleted) {  // 更新的undoRec才能批量回收
        *targetRollPtr = fetchRowInfo->rowTrxInfo.rollPtr;
    } else {
        ret = RowUndoFetchFromTargetRecordWithTargetTrxIds(
            ctx->seRunCtx->undoCtx, fetchRowInfo->rowTrxInfo.rollPtr, targetTrxIds, targetRollPtr);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "get target record buf. labelId %" PRIu32 "", ctx->heapCfg.labelId);
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

StatusInter HeapFindUnlinkCutoff(
    HeapRunCtxT *ctx, HpTupleAddr heapTupleAddr, TrxIdListT *targetTrxIds, uint64_t *targetRollPtr)
{
    DB_POINTER3(ctx, targetTrxIds, targetRollPtr);
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, NULL);
    fetchRowInfo.srcRowInfo.itemPtr = *(HpItemPointerT *)&heapTupleAddr;
    fetchRowInfo.isOnlyReadSrcRow = true;
    fetchRowInfo.isCanAccessMarkDelRow = true;
    fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;
    DB_ASSERT(ctx->hpOperation == HEAP_OPTYPE_UNDO_PURGER);
    HeapSetPageLatchActionForWrite(ctx);
    HeapSetLockActionBeforeGcUnlinkRollPtr(ctx);
    StatusInter ret = HeapFetchRow(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
        SE_ERROR(ret, "target rollPtr get record buf. labelId %" PRIu32 "", ctx->heapCfg.labelId);
        return ret;
    }

    ret = HeapFindUnlinkCutoffWithTargetTrxIds(ctx, &fetchRowInfo, targetTrxIds, targetRollPtr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "target rollPtr get record buf. labelId %" PRIu32, ctx->heapCfg.labelId);
    }

    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    return ret;
}

static inline void HeapCommitFetchHistoryNormalFixBuffer(HeapTupleBufT *tupleBuf, HpRowHeadPtrT *oldRowInLog)
{
    tupleBuf->buf = (uint8_t *)(oldRowInLog->normalFixRowHead + 1);
    tupleBuf->bufSize = oldRowInLog->normalFixRowHead->size;
}

StatusInter HeapCommitFetchHistoryBuffer(
    HeapRunCtxT *ctx, const UndoRowOpInfoT *rowOpInfo, HeapTupleBufT *tupleBuf, bool *isAllocBuf)
{
    DB_POINTER4(ctx, rowOpInfo, tupleBuf, isAllocBuf);
    HpRowHeadPtrT oldRowInLog = {.rowState = NULL};
    oldRowInLog.rowState = (HpRowStateT *)rowOpInfo->rowBuf;
    *isAllocBuf = false;
    if (HeapPageIsNormalRow(oldRowInLog.rowState)) {
        if (ctx->hpControl.isFixPage) {
            HeapCommitFetchHistoryNormalFixBuffer(tupleBuf, &oldRowInLog);
#ifdef FEATURE_SIMPLEREL
            ctx->compV1Info.undoReclaimPhyAddr = tupleBuf->buf;
#endif
            return STATUS_OK_INTER;
        }
        tupleBuf->buf = (uint8_t *)(oldRowInLog.normalRowHead + 1);
        tupleBuf->bufSize = oldRowInLog.normalRowHead->size;
#ifdef FEATURE_SIMPLEREL
        ctx->compV1Info.undoReclaimPhyAddr = tupleBuf->buf;
#endif
        return STATUS_OK_INTER;
    }

    tupleBuf->buf = NULL;
    *isAllocBuf = true;  // 后续获取到的buffer, 是 临时申请出来的, 需要外部使用完后进行释放.
    bool isLinkSrcRow = HeapPageIsLinkSrcRow(oldRowInLog.rowState);
    DB_ASSERT(isLinkSrcRow);
    DB_ASSERT(rowOpInfo->rowSize == sizeof(HpLinkSrcFixRowHead) || rowOpInfo->rowSize == sizeof(HpLinkRowHeadT));
    HpItemPointerT linkItemPtr =
        ctx->hpControl.isFixPage ? oldRowInLog.linkSrcFixRowHead->linkItemPtr : oldRowInLog.linkRowHead->linkItemPtr;
#ifdef FEATURE_SIMPLEREL
    ctx->compV1Info.undoReclaimPhyAddr =
        ctx->hpControl.isFixPage ? (void *)oldRowInLog.linkSrcFixRowHead : (void *)oldRowInLog.linkRowHead;
#endif
    RowTrxIdT srcTrxId = ctx->hpControl.isFixPage ? oldRowInLog.linkSrcFixRowHead->trxInfo.trxId :
                                                    oldRowInLog.linkRowHead->srcTrxInfo.trxId;
    // 根据linkSrc中指向的 linkDst 获取buffer.
    StatusInter ret = HeapFetchBufferForLinkDstRow(ctx, linkItemPtr, srcTrxId, tupleBuf);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        // noteb: 有可能由于对象比较大, 内存申请不到, 导致获取失败.
        // 该函数当前用于事务提交时, 旧索引回收, 由此导致的失败, 后续考虑 延迟回收 或者 内存整理时的扫描进行消除.
        SE_ERROR(ret, "commit clean in get buffer, labelId %" PRIu32 "", ctx->heapCfg.labelId);
    }
    return ret;
}

static inline StatusInter HeapFetchByPagePrepare(HeapRunCtxT *ctx, HpItemPointerT *itemPtrArray, uint32_t rowNum)
{
    StatusInter ret = STATUS_OK_INTER;
    HeapSetPageLatchActionForRead(ctx);
    HeapSetOperationForFetch(ctx);
    // Normal模式下: HeapSetLockActionBeforeFetchRow (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
    HeapSetLockFunc(ctx, HEAP_TRX_AM_NORMALREAD);

    for (uint32_t i = 0; i < rowNum; ++i) {
        // Normal模式下: HeapAcquireRowLockOnFetch (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
        ret = HeapAcquireRowLockFunc(ctx, HEAP_TRX_AM_NORMALREAD, itemPtrArray[i]);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
    }
    return ret;
}

static inline void InitFetchRowInfo(HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo, HpItemPointerT itemPtr)
{
    *fetchRowInfo = EmptyHpPageFetchRowInfo();
    fetchRowInfo->srcRowInfo.itemPtr = itemPtr;
    fetchRowInfo->curRowInfo = &fetchRowInfo->srcRowInfo;
    fetchRowInfo->isToGetOldestVisibleBuf = ctx->hpControl.isToGetOldestVisibleBuf;
    fetchRowInfo->isCanAccessSelfDeletedBuf = ctx->hpControl.isCanAccessSelfDeletedBuf;
    fetchRowInfo->isReadLatestCommittedBuf = ctx->hpControl.isReadLatestCommittedBuf;
}

static inline StatusInter HeapFetchByPageBufferPrepare(HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo)
{
    StatusInter ret = STATUS_OK_INTER;
    bool isNeedRetry = false;
    uint32_t tryFetchCnt = 0;
    do {
        isNeedRetry = false;
        if (SECUREC_LIKELY(!fetchRowInfo->isSliceRows && !fetchRowInfo->isReadFromPrtUpdUndo)) {
            return STATUS_OK_INTER;
        }

        if (!fetchRowInfo->isSliceRows) {
            DB_ASSERT(fetchRowInfo->isReadFromPrtUpdUndo);
            return HeapFetchPrtUpdUndoVersion(ctx, fetchRowInfo);
        }

        // 大对象行的获取过程中，会释放src行、dst行所在页，否则读取分片行时会出现relatch
        ret = HeapFetchSliceRows(ctx, fetchRowInfo);
        // 只有分片读取场景, 由于直连读等场景 和 并发写入, 可能搜索分片过程 发生变化, 导致版本不一致.
        if (ret != STATUS_OK_INTER && HeapFetchSliceRowIsNeedTry(fetchRowInfo)) {
            // 需要重试时，由于src行、dst行的页锁都释放了，要重新读取src行、dst行
            HpItemPointerT itemPtr = fetchRowInfo->srcRowInfo.itemPtr;
            InitFetchRowInfo(ctx, fetchRowInfo, itemPtr);
            HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(fetchRowInfo, NULL);
            ret = HeapFetchGetPage(ctx, opInfo.fetchRowInfo);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
            ret = ctx->hpControl.isFixPage ? HeapFixedRowFetch(ctx, &opInfo) : HeapVarRowFetch(ctx, &opInfo);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
            DB_ASSERT(fetchRowInfo->isGetBuf);
            isNeedRetry = true;
            tryFetchCnt++;
        }
    } while (isNeedRetry && tryFetchCnt < MAX_TRY_FETCH_LOOP);

    if (ret == STATUS_OK_INTER) {
        // 获取大对象行数据成功后，已经leave了src页和dst页，此时需要重新get src页
        fetchRowInfo->curRowInfo = &fetchRowInfo->srcRowInfo;
        ret = HeapFetchGetPage(ctx, fetchRowInfo);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    } else {
        SE_ERROR(ret, "fetch largeObj, labelId:%" PRIu32 ", itemPtr(%" PRIu32 ",%" PRIu32 "). tryFetchCnt:%" PRIu32 "",
            ctx->heapCfg.labelId, fetchRowInfo->srcRowInfo.itemPtr.pageId, fetchRowInfo->srcRowInfo.itemPtr.slotId,
            tryFetchCnt);
        return ret;
    }

    if (fetchRowInfo->isReadFromPrtUpdUndo) {
        ret = HeapFetchPrtUpdUndoVersion(ctx, fetchRowInfo);
    }

    return ret;
}

static inline StatusInter HeapFetchRowAfterGetPage(HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo,
    HpItemPointerT itemPtr, PageHeadT *targetPageHead, PageIdT targetPageAddr)
{
    InitFetchRowInfo(ctx, fetchRowInfo, itemPtr);
    fetchRowInfo->srcRowInfo.pageHeadPtr.pageHead = targetPageHead;
    fetchRowInfo->srcRowInfo.pageAddr = targetPageAddr;
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(fetchRowInfo, NULL);

    StatusInter ret;
    if (ctx->hpControl.isFixPage) {
        ret = HeapFixedRowFetch(ctx, &opInfo);
    } else {
        ret = HeapVarRowFetch(ctx, &opInfo);
    }
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER || !fetchRowInfo->isGetBuf)) {
        SE_ERROR(ret,
            "fetch batch row by page, labelId:%" PRIu32 ", itemPtr(%" PRIu32 ",%" PRIu32 "), isGetBuf:%" PRIu32 ".",
            ctx->heapCfg.labelId, fetchRowInfo->srcRowInfo.itemPtr.pageId, fetchRowInfo->srcRowInfo.itemPtr.slotId,
            (uint32_t)fetchRowInfo->isGetBuf);
        return ret != STATUS_OK_INTER ? ret : INTERNAL_ERROR_INTER;
    }

    DB_ASSERT(fetchRowInfo->isGetBuf);
    ret = HeapFetchByPageBufferPrepare(ctx, fetchRowInfo);
    return ret;
}

// 由使用者保证，itemPtrArray中的行，pageId相同
StatusInter HeapFetchBatchHpTupleBufferByPageInner(
    HeapRunCtxT *ctx, HpItemPointerT *itemPtrArray, uint32_t rowNum, BatchFetchByPageParaT *para)
{
    StatusInter ret = HeapFetchByPagePrepare(ctx, itemPtrArray, rowNum);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    fetchRowInfo.srcRowInfo.itemPtr = itemPtrArray[0];
    fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;

    // 获取页，里面会获取页锁（持久化场景，会open页）
    ret = HeapFetchGetPage(ctx, &fetchRowInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        goto RELEASE;
    }

    // 保存页信息（leave 页之前，不会换出，指针有效）
    PageHeadT *targetPageHead = fetchRowInfo.srcRowInfo.pageHeadPtr.pageHead;
    PageIdT targetPageAddr = fetchRowInfo.srcRowInfo.pageAddr;

    //  1. 获取跳转行的过程中，如果发生relatch，期间会有一段时间释放了src页锁
    //     由于并发，src指向的dst可能会有变化，或者src页被删空，所以需要重新获取src页，重新读取数据.
    //     重新获取src页前会释放表锁并且leave页，这个过程中，页有可能被换出, 所以要check并且更新 targetPageHead
    //  2. 大对象行获取数据过程中，也会leave src页
    for (uint32_t i = 0; i < rowNum; ++i) {
        if (itemPtrArray[i].pageId != itemPtrArray[0].pageId) {
            SE_ERROR(INTERNAL_ERROR_INTER, "fetch batch buf by page.");
            DB_ASSERT(false);
        }
        if (SECUREC_UNLIKELY(fetchRowInfo.srcRowInfo.pageHeadPtr.pageHead != targetPageHead)) {
            // 此时src行对应的页的页锁肯定存在，并且页也处于open状态
            DB_ASSERT(targetPageAddr.deviceId == fetchRowInfo.srcRowInfo.pageAddr.deviceId &&
                      targetPageAddr.blockId == fetchRowInfo.srcRowInfo.pageAddr.blockId);
            targetPageHead = fetchRowInfo.srcRowInfo.pageHeadPtr.pageHead;
        }
        ret = HeapFetchRowAfterGetPage(ctx, &fetchRowInfo, itemPtrArray[i], targetPageHead, targetPageAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            goto RELEASE;
        }

        if (para->func) {
            ret = DbGetStatusInterErrno(TupleBufPut(para->cacheTupleBuf, fetchRowInfo.bufSize, fetchRowInfo.buf));
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                goto RELEASE;
            }
            HeapReadOneRowInfoByBatchT oneRowInfo = {.batchNum = rowNum, .index = i, .tupleBuf = para->cacheTupleBuf};
            ret = DbGetStatusInterErrno(para->func(&oneRowInfo, para->userData));
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                goto RELEASE;
            }
        }
        HeapFetchClearTempSliceMem(ctx, &fetchRowInfo);
        // 不释放target页
        SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, itemPtrArray[i].pageId);
    }
RELEASE:
    HeapFetchClearTempSliceMem(ctx, &fetchRowInfo);
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    return ret;
}

#ifdef __cplusplus
}
#endif
