/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Heap for mini kv
 * Author:
 * Create: 2023-12-22
 */

#include "se_heap_utils.h"
#include "db_hash.h"
#include "db_crash_debug.h"
#include "adpt_sleep.h"
#include "se_undo.h"
#include "se_heap_stats.h"
#include "se_heap_trx_acc.h"
#include "se_heap_access_dm.h"
#include "se_log.h"
#include "dm_data_kv.h"
#include "dm_data_minikv.h"

#ifdef FEATURE_MINIKV
Status HeapAmMiniKvSerial(const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT deSrlObjHdl, HeapTupleBufT *serialBuf)
{
    DB_UNUSED(heapRunHdl);
    DmKvT *kv = deSrlObjHdl.handle;
    return DmSerializeMiniKv(&kv->base, (uint8_t **)&serialBuf->buf, &serialBuf->bufSize);
}

void HeapAmMiniKvFreeSerialBuf(const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT deSrlObjHdl, HeapTupleBufT *serialBuf)
{
    DB_UNUSED(heapRunHdl);
    DmKvT *kv = deSrlObjHdl.handle;
    DbDynMemCtxFree(kv->base.memCtx, serialBuf->buf);
    serialBuf->buf = NULL;
}

Status HeapAmMiniKvDeSerial(const HpRunHdlT heapRunHdl, HeapConstBufT *serialBuf, HeapDeSrlObjHdlT *deSrlObjHdl)
{
    deSrlObjHdl->handle = NULL;
    void *usrMemCtx = HeapAmGetUsrMemCtx(heapRunHdl);
    DmMiniBaseLabelT *kvTable = HeapAmGetDmInfo(heapRunHdl);

    DmObjectT *obj = NULL;
    // obj申请的内存，释放在ctx销毁或者函数 DmDestroyKv 中
    Status ret = DmCreateEmptyMiniKv(usrMemCtx, kvTable, &obj);
    if (ret != GMERR_OK) {
        return ret;
    }

    TextT serializedBuf = {.str = (char *)serialBuf->constBuf, .len = serialBuf->bufSize};
    ret = DmDeserializeMiniKv(obj, &serializedBuf);
    if (ret != GMERR_OK) {
        DmDestroyMiniKv(obj);
        return ret;
    }
    deSrlObjHdl->handle = obj;
    return GMERR_OK;
}

Status HeapAmMiniKvFreeDeSerialObj(const HpRunHdlT heapRunHdl, HeapDeSrlObjHdlT deSrlObjHdl)
{
    DB_UNUSED(heapRunHdl);
    DmDestroyMiniKv(deSrlObjHdl.handle);
    deSrlObjHdl.handle = NULL;
    return GMERR_OK;
}

Status HeapAmMiniKvExtraKeyByKvBuf(const HpRunHdlT heapRunHdl, HeapDeSrlObjHdlT emptyObjHdl, HeapConstBufT *serialBuf,
    uint32_t indexId, HeapTupleBufT *buf)
{
    DB_UNUSED(heapRunHdl);
    DB_UNUSED(emptyObjHdl);
    DB_UNUSED(indexId);
    DmGetKeyFromMiniKvBuf((uint8_t *)serialBuf->constBuf, (uint8_t **)&buf->buf, &buf->bufSize);
    return GMERR_OK;
}

void HeapAmMiniKvGetCmpKeyBuf(const HpRunHdlT heapRunHdl, HeapDeSrlObjHdlT emptyObjHdl, HeapTupleBufT *buf)
{
    DB_UNUSED(heapRunHdl);
    DB_UNUSED(emptyObjHdl);
}

Status HeapAmMiniKvExtraKeyByKvObj(const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT deSrlObjHdl, uint32_t indexId,
    uint8_t **keyBuf, uint32_t *length)
{
    DB_UNUSED(heapRunHdl);
    DB_UNUSED(indexId);
    DmKvT *kvObj = deSrlObjHdl.handle;
    *keyBuf = kvObj->keyBuf;
    *length = kvObj->keyLen;
    return GMERR_OK;
}

void HeapAmMiniKvGetDmDetailInfo(const HpRunHdlT heapRunHdl, HeapDmDetailT *dmDetail)
{
    DmMiniKvTableT *kvTable = (DmMiniKvTableT *)HeapAmGetDmInfo(heapRunHdl);
    dmDetail->heapShmAddr = *(ShmemPtrT *)&kvTable->base.pageAddr;
    dmDetail->dmInfo = kvTable;
    dmDetail->dmUuid = kvTable->base.metaCommon.metaId;
    dmDetail->labelType = KV_TABLE;
}

void HeapAmMiniKvGetDmPriKeyInfo(const HpRunHdlT heapRunHdl, HeapDmIndexIterT *dmPriKeyInfo)
{
    DmMiniBaseLabelT *label = HeapAmGetDmInfo(heapRunHdl);
    DmMiniKvTableT *kvTable = (DmMiniKvTableT *)label;
    dmPriKeyInfo->iterId = 0;
    dmPriKeyInfo->dmUuid = kvTable->index->indexId;
    dmPriKeyInfo->dmIndex = kvTable->index;
    dmPriKeyInfo->idxShmAddr = *(ShmemPtrT *)&kvTable->index->idxTupleAddr;
    dmPriKeyInfo->indexType = (uint32_t)kvTable->index->indexType;
    dmPriKeyInfo->isIterInited = true;
    dmPriKeyInfo->isGet = true;
    dmPriKeyInfo->isUniq = true;
    dmPriKeyInfo->needCheckIndexSatisfied = false;
}

void HeapAmMiniKvGetDmNextSndKeyFun(const HpRunHdlT heapRunHdl, HeapDmIndexIterT *dmSndKeyInfo)
{
    // Kv table doesn't have second key.
    DB_UNUSED(heapRunHdl);
    dmSndKeyInfo->isIterInited = true;
    dmSndKeyInfo->isGet = false;
}

Status HeapAmMiniKvIsKeyPropeIncludeNull(const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT deSrlObjHdl,
    uint32_t iterId, const HeapTupleBufT *tupleBuf, bool *isKeyPropeIncludeNull)
{
    // kv表没有二级索引，直接返回false即可
    *isKeyPropeIncludeNull = false;
    return GMERR_OK;
}

void HeapAmMiniKvInitDmIndexInfoByIdx(
    const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT emptyObjHdl, HeapDmIndexIterT *dmKeyInfo, uint32_t *labelId)
{
    DB_UNUSED(heapRunHdl);
    DB_UNUSED(emptyObjHdl);
    DB_UNUSED(dmKeyInfo);
    DB_UNUSED(labelId);
    SE_LAST_ERROR(INTERNAL_ERROR_INTER, "Not supported operate");
    DB_ASSERT(false);  // 不可能走到该场景
}

// heap容器针对minikv table类型的表与数据buf序列化和反序列化有关钩子函数的结合
// 在编码阶段赋值, 不涉及并发初始化，初始化后只读，不涉及并发读写
const HeapAmForDmT g_gmdbHeapAmForMiniKv = {
    HeapAmMiniKvSerial,
    HeapAmMiniKvFreeSerialBuf,
    HeapAmMiniKvDeSerial,
    HeapAmMiniKvFreeDeSerialObj,
    HeapAmMiniKvExtraKeyByKvBuf,
    HeapAmMiniKvExtraKeyByKvBuf,
    HeapAmMiniKvGetCmpKeyBuf,
    HeapAmMiniKvExtraKeyByKvObj,
    HeapAmMiniKvExtraKeyByKvObj,
    HeapAmMiniKvGetDmDetailInfo,
    HeapAmMiniKvGetDmPriKeyInfo,
    HeapAmMiniKvGetDmNextSndKeyFun,
    HeapAmMiniKvIsKeyPropeIncludeNull,
    HeapAmMiniKvInitDmIndexInfoByIdx,
};
#endif
