/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: Heap 存储Page的接口和结构体
 * Author: panghaisheng
 * Create: 2020-09-01
 */
#ifndef SE_HEAP_PAGE_H
#define SE_HEAP_PAGE_H

#include "se_heap_fixed.h"
#include "se_heap_var.h"

#ifdef __cplusplus
extern "C" {
#endif

SO_EXPORT void HeapPageInitRowByRowType(
    const HeapRunCtxT *ctx, HpPageAllocRowInfoT *allocRowInfo, HpRowHeadPtrT rowHead);

SO_EXPORT PageSizeT HeapPageGetMinRowSize(PageSizeT bufSize, HpPageRowTypeE rowType);

StatusInter HeapPageDeleteRow(HeapRunCtxT *ctx, HeapRowInfoT *delRowInfo);

SO_EXPORT_FOR_TS StatusInter HeapSetblockFreeSizeAfterDelete(HeapRunCtxT *ctx, HeapRowInfoT *delRowInfo);

inline static uint32_t GetHeapPageAvailSize(const HeapT *heap, PageTotalSizeT pageSize)
{
    // 获取一行的最大size，var---slot+slotExtend+rowHead+buf，fix---slotExtend+rowHead+buf
    DB_POINTER(heap);
    uint32_t availSize;
    if (heap->constInfo.pageType == HEAP_FIX_LEN_ROW_PAGE) {
        availSize = pageSize - HeapFixPageGetMgrInfoLen(heap->regularInfo.fixPageCfg);
    } else {
        availSize = pageSize - HeapVarPageGetMaxMgrInfoLen(heap->constInfo.slotExtendSize);
    }
    // 终端场景需要保留页若干字节用于加密
    return availSize - SeGetPageTailReservedSize();
}

SO_EXPORT_FOR_TS StatusInter HeapPageReadUndo(
    HeapRunCtxT *ctx, const ReadViewT *readView, HpPageFetchRowInfo *fetchRowInfo);

void HeapLatchPageWhenAllocingRow(HeapRunCtxT *ctx, const HeapPageAllocCtx *heapPageAllocCtx);

SO_EXPORT_FOR_TS StatusInter HeapInitAllocRowInfo(
    const HeapRunCtxT *ctx, const uint8_t *buf, uint32_t bufSize, HpPageAllocRowInfoT *allocRowInfo);

inline static StatusInter HeapLabelLatchCheckPage(const HeapRunCtxT *ctx, const HeapRowInfoT *curRowInfo)
{
    PageHeadT *page = curRowInfo->pageHeadPtr.pageHead;
    if (!DbIsPageIdValid(curRowInfo->pageAddr) || page->trmId != ctx->heapCfg.heapTrmId) {
        return NO_DATA_HEAP_PAGE_NOT_EXIST;
    }
    if (curRowInfo->isNeedCheckBlockIdAndPageState) {
        if (curRowInfo->itemBlockId != LfsGetBlockId(page) || page->pageState != PAGE_USING) {
            return NO_DATA_HEAP_PAGE_NOT_EXIST;
        }
    }
    return STATUS_OK_INTER;
}

StatusInter HeapPageAddLatch(HeapRunCtxT *ctx, const HeapRowInfoT *curRowInfo);

inline static PageSizeT HeapPageGetRowReserveSize(HpRowHeadPtrT rowHead)
{
    PageSizeT rollbackReserveSize = INVALID_OFFSET16;
    if (HeapPageIsNormalRow(rowHead.rowState)) {
        rollbackReserveSize = rowHead.normalRowHead->rollbackReserveSize;
    } else if (HeapPageIsLinkSrcRow(rowHead.rowState)) {
        rollbackReserveSize = rowHead.linkRowHead->rollbackReserveSize;
    } else {
        // 其他行不涉及该场景，不可能走到这里
        DB_ASSERT(false);
    }
    // 当src行rollbackReserveSize为0时，占用空间应该是sizeof(HpLinkRowHeadT)的大小，因此需要取MAX
    return (PageSizeT)DB_MAX(
        HEAP_CALC_ALIGN_SIZE(sizeof(HpNormalRowHeadT) + rollbackReserveSize), (PageSizeT)sizeof(HpLinkRowHeadT));
}

SO_EXPORT_FOR_TS HOT_FUN_READ StatusInter HeapFetchGetPage(HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo);

SO_EXPORT_FOR_TS HOT_FUN_READ StatusInter HeapFetchTryGetPage(HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo);

StatusInter HeapFsmGetPageBySize(HeapRunCtxT *ctx, PageSizeT requireSize, HeapPageAllocCtx *heapPageAllocCtx);

StatusInter HeapFsmGetMaxFreeSizeOrCachedPage(HeapRunCtxT *ctx, HeapPageAllocCtx *heapPageAllocCtx);

SO_EXPORT_FOR_TS StatusInter HeapMdGetMemPage(
    const HeapRunCtxT *ctx, const HpItemPointerT *itemPointer, PageHeadT **pageHead, void *recyArg, PageIdT *pageAddr);

StatusInter HeapReleasePage(HeapRunCtxT *ctx, HeapPageAllocCtx *heapPageAllocCtx, PageSizeT rowSize, uint16_t freeSize);

uint32_t HeapGetFreeReserveSize(HeapRunCtxT *ctx, PageHeadT *pageHead, uint32_t freeSize);

inline static void HeapSetFreeSizeWhenUpgNotSatisfy(HFPageHeadT *pageHead)
{
    PageSizeT oneRowSizeWithSlotExtend = HeapFixGetOneRowSizeWithSlotExtend(&pageHead->pageCfg);
    pageHead->baseHead.pageHead.freeSize = (uint16_t)(pageHead->freeSlotCnt * oneRowSizeWithSlotExtend);
    // 恢复不能升级状态时，需要先设置freeSize再刷新标记位，即使执行到此处退出，重启恢复时因为标记位还会重新刷新freeSize
    COMPILER_BARRIER;
    SHM_CRASHPOINT_ONE(SHM_CRASH_FH_SET_UPG);
    pageHead->isUpgNotSatisfy = false;
}

void HeapVarPageAllocNewSlotWithRsmUndo(HeapRunCtxT *ctx, uint32_t pageId, HVPageHeadT *pageHead);

inline static void HeapFixPageSetUpgNotSatisfy(HeapRunCtxT *ctx, HFPageHeadT *pageHead)
{
    pageHead->isUpgNotSatisfy = true;
    // 设置不能升级状态时，需要先设置标记位再刷新freeSize，即使执行到此处退出，重启恢复时因为标记位还会重新刷新freeSize
    COMPILER_BARRIER;
    SHM_CRASHPOINT_ONE(SHM_CRASH_FH_SET_NOT_UPG);
    pageHead->baseHead.pageHead.freeSize = 0;  // 不能升级, freeSize 设置为0, fsm根据此值调整到full链表中
}

static inline void HeapResetRowState(HpRowStateT *rowState)
{
    bool isExist = rowState->isExist;
    *rowState = (HpRowStateT){0};
    rowState->isExist = isExist;
}

StatusInter HeapSetPrevVersionInfoInMasterVersion(
    const HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo, HpRowStateT *rowState, HpPageTypeE pageType);

StatusInter HeapReadPrevVersionInVersionChain(
    const HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo, HpPageTypeE pageType);

void HeapDeleteRecoveryHCInfo(HeapRunCtxT *ctx, HeapRowInfoT *srcRowInfo);

#ifdef __cplusplus
}
#endif

#endif  // SE_HEAP_PAGE_H
