/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 * Description: Heap Fix 接口声明
 * Author: liuche
 * Create: 2024-07-04
 */

#ifndef SE_HEAP_FIXED_H
#define SE_HEAP_FIXED_H

#include "se_heap_inner.h"

#ifdef __cplusplus
extern "C" {
#endif

SO_EXPORT PageSizeT HeapFixPageAllocRow(HFPageHeadT *pageHead, HpNormalFixRowHead **rowHead);

SO_EXPORT_FOR_TS void HeapFixPageInitNormalRow(HpNormalFixRowHead *rowHead, const HpPageAllocRowInfoT *allocRowInfo);

SO_EXPORT_FOR_TS void HeapFixPageInitLinkSrcRow(HpLinkSrcFixRowHead *rowHead, const HpPageAllocRowInfoT *allocRowInfo);

SO_EXPORT_FOR_TS bool HeapFixPageIsRowExist(HpNormalFixRowHead *rowHead);

bool HeapConfirmFixPageSize(HeapRunCtxT *ctx, HeapPageAllocCtx *heapPageAllocCtx, uint32_t requireSize);

StatusInter HeapFixedRowInsert(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo);

StatusInter HeapFixedRowUpdate(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo);

StatusInter HeapFixedRowFetch(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo);

SO_EXPORT void HeapFixPageFreeRow(HFPageHeadT *pageHead, PageSizeT slotId);

void HeapFixPageDeleteRow(HeapRunCtxT *ctx, HeapRowInfoT *delRowInfo);

StatusInter HeapFixedRowSizeReset(HeapCntrAcsInfoT *heapCntrAcsInfo, PageSizeT fixRowSize);

void HeapFixedRowInitCfg(PageTotalSizeT pageSize, HeapT *heap);

StatusInter HeapLabelUpgradeFixRowSizeCheck(
    SeInstanceT *seInstance, HpPageTypeE pageType, PageSizeT oldFixRowSize, PageSizeT newFixRowSize);

StatusInter HeapFixedRowUpgradeInitCfg(PageTotalSizeT pageSize, HeapT *heap);

void HeapFixedRowDowngradeInitCfg(HeapT *heap, PageSizeT fixRowSize);

StatusInter HeapDowngradeFetchOnePage(HeapScanCursorT *cursor);

StatusInter HeapFixPageUpgrade(HeapRunCtxT *ctx, HFPageHeadT *pageHead);

StatusInter HeapFixPageDowngrade(HeapRunCtxT *ctx, HFPageHeadT *pageHead);

// wait for merge

SO_EXPORT void HeapFixPageInitPage(HFPageHeadT *pageHead, const HFPageCfgT *fixPageCfg);

void HeapFixPageInitNewBlock(HeapRunCtxT *ctx, HFPageHeadT *pageHead);

void HeapMarkDeleteModiFixRowInfo(HpPageFetchRowInfo *fetchRowInfo, HpPageRowOpInfoT *opInfo);

SO_EXPORT_FOR_TS StatusInter HeapFixPageGetRowBySlotId(
    HFPageHeadT *pageHead, uint32_t slotId, HpNormalFixRowHead **rowHead, PageSizeT *slotOffset);

StatusInter HeapUpdateFixedRowWithOptimisticTrx(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo);

static inline bool HeapIsLatestVersionFixPage(uint32_t rawRowSizeInHeapT, const HFPageHeadT *pageHead)
{
    return rawRowSizeInHeapT == pageHead->pageCfg.rawRowSize;
}

static inline PageSizeT HeapFixPageGetLinkSrcRowSize(void)
{
    return sizeof(HpLinkSrcFixRowHead);
}

inline static PageSizeT HeapFixPageGetMgrInfoLen(HFPageCfgT fixPageCfg)
{
    return fixPageCfg.rowBegin;  // pageHead起始位置到rowBegin的位置都属于管理结构，不包含slotExtend的部分
}

static inline void HeapSetFetchRowInfoWithFixLinkDstRow(HpPageFetchRowInfo *fetchRowInfo)
{
    HpLinkDstFixRowHead *linkRowHead = fetchRowInfo->curRowInfo->rowHeadPtr.linkDstFixRowHead;
    fetchRowInfo->curRowInfo->rawRowSize = linkRowHead->size;
    fetchRowInfo->isGetBuf = true;
    fetchRowInfo->buf = linkRowHead + 1;
    fetchRowInfo->bufSize = linkRowHead->size;
}

inline static void HeapSetFetchRowInfoWithFixNormalRow(HpPageFetchRowInfo *fetchRowInfo)
{
    HpNormalFixRowHead *fixRowHead = fetchRowInfo->curRowInfo->rowHeadPtr.normalFixRowHead;
    PageSizeT size = fixRowHead->size;
    fetchRowInfo->buf = fixRowHead + 1;
    fetchRowInfo->bufSize = size;
    fetchRowInfo->isGetBuf = true;
    fetchRowInfo->curRowInfo->rawRowSize = size;
}

SO_EXPORT_FOR_TS void HeapSetFetchRowInfoWithFixLinkSrcRow(HpPageFetchRowInfo *fetchRowInfo);

SO_EXPORT_FOR_TS StatusInter HeapFixPageReadPrevVersion(const HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo);

StatusInter HeapFixPageReadFirstUndoData(HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo);

void HeapFixPageIsExistSetBit(HpNormalFixRowHead *rowHead);

inline static void HeapFixPageIsExistClearBit(HpNormalFixRowHead *rowHead)
{
    DB_POINTER(rowHead);
    rowHead->rowState.isExist = false;
}

#ifdef __cplusplus
}
#endif

#endif  // SE_HEAP_FIXED_H
