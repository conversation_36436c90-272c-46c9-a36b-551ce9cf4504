/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Heap stats共有部分。该.h只能由se_heap_stats.c include。若后续特性开发需要暴露其中某个函数，
 *              请声明于目前于另一个.h文件内并include哪一个。
 * Author: Peng <PERSON>
 * Create: 2024-09-03
 */

#ifndef SE_HEAP_STATS_COMMON_H
#define SE_HEAP_STATS_COMMON_H

#ifdef __cplusplus
extern "C" {
#endif

inline static void HeapStatForOperationPerf(
    HeapPerfStatT *stat, HpPerfOpTypeE type, StatusInter result, uint64_t rowNum, uint64_t cyclesSum)
{
#ifdef SE_HEAP_PERF
    if (result == STATUS_OK_INTER) {
        stat->opStat[type].cnt += rowNum;
        stat->opStat[type].minCost = DB_MIN(cyclesSum, stat->opStat[type].minCost);
        stat->opStat[type].maxCost = DB_MAX(cyclesSum, stat->opStat[type].maxCost);
        stat->opStat[type].totalCost += cyclesSum;
        return;
    }
    stat->opStat[type].failCnt++;
#endif
}

Status HeapLabelGetLockStat(HeapRunCtxAllocCfgT *heapRunCtxAllocCfg, DbMemCtxT *usrMemCtx, LockStatT *heapLockStat)
{
    DB_POINTER2(heapRunCtxAllocCfg, heapLockStat);

    // 视图查询heap的事务锁情况, 为了避免影响事务锁的统计, 这里使用直连读的方式
    HpRunHdlT heapRunHdl = NULL;
    Status ret = HeapLabelAllocAndInitRunctx(heapRunCtxAllocCfg, &heapRunHdl);
    if (ret != GMERR_OK) {
        return ret;
    }
    HeapLabelOpenForDirectRead(heapRunHdl, usrMemCtx);
    LockStatT *lockStat = HeapRunCtxOpenLockStat(heapRunHdl, true);  // 只读方式打开, 不会触发统计内存申请
    if (lockStat) {
        *heapLockStat = *lockStat;
    }
    HeapLabelResetCtx(heapRunHdl);
    HeapLabelReleaseRunctx(heapRunHdl);
    return GMERR_OK;
}

/**
 * 只有使用try模式且申请相同lockId的锁成功才使用该函数，是为了在折叠try lock正确统计信息
 *      1. 首次申请行锁失败，最后成功获得行锁成功
 *      2. 首次行锁升级表锁失败，最后成功获得行锁成功
 *      3. 首次申请行锁失败，最后成功行锁升级表锁成功
 *      4. 首次行锁升级表锁失败，最后行锁升级表锁成功
 * ps: 如果要统计同一批try
 * lock最后的申请，那还得考虑连续失败下内部申请锁逻辑的变化十分复杂，所以简化为除了成功外，只记录第一次失败
 */
void LockStatHandleTrySameLockSuccess(LockAcqInfoT *lockAcqInfo, LockStatT *lockStat)
{
    DB_POINTER2(lockAcqInfo, lockStat);

    // 申请相同的lockId，且申请成功，那意味着上一次申请应该是失败的，回滚掉上次的计数，认为这一批try lock申请成功了
    DB_ASSERT(lockAcqInfo->firstLockType < SE_LOCK_TYPE_NUM);
    DB_ASSERT(lockAcqInfo->firstLockMode < SE_LOCK_ACQ_TYPE_NUM);
    LockConflictStatT *firstLockStat =
        &(lockStat->lockConflictStat[lockAcqInfo->firstLockType][lockAcqInfo->firstLockMode]);
    // 行锁升级表锁，第一次应该就记录在表锁统计里
    DB_ASSERT(firstLockStat->conflictAcqFail > 0);
    firstLockStat->conflictAcqFail--;
    if (lockAcqInfo->firstAcqType == (uint8_t)SE_LOCK_ACQ_S_UPD_X ||
        lockAcqInfo->firstAcqType == (uint8_t)SE_LOCK_ACQ_I_UPD_X ||
        lockAcqInfo->firstAcqType == (uint8_t)SE_LOCK_ACQ_IS_UPD_IX) {  // 第一次是锁升级
        DB_ASSERT(firstLockStat->lockEscalationFail > 0);
        DB_ASSERT(firstLockStat->lockEscalationCount > 0);
        firstLockStat->lockEscalationFail--;
        firstLockStat->lockEscalationCount--;
    }
    DB_ASSERT(firstLockStat->trxLockAcquireCount > 0);
    DB_ASSERT(firstLockStat->conflictCount > 0);
    firstLockStat->trxLockAcquireCount--;
    firstLockStat->conflictCount--;
    lockAcqInfo->isLockConfit = true;  // 这一批try lock第一次失败，表示曾有冲突，后续成功仍认为发生冲突
}

void LockStatUpdateWhenAcqLabelLock(const LockStatT *tmpLockStat, LockStatT *heapLockStat)
{
    heapLockStat->tupleLockEscalateToLabelLock += tmpLockStat->tupleLockEscalateToLabelLock;
    for (uint32_t i = 0; i < (uint32_t)SE_LOCK_TYPE_NUM; i++) {
        for (uint32_t j = 0; j < (uint32_t)SE_LOCK_ACQ_TYPE_NUM; j++) {
            heapLockStat->lockConflictStat[i][j].trxLockAcquireCount +=
                tmpLockStat->lockConflictStat[i][j].trxLockAcquireCount;
            heapLockStat->lockConflictStat[i][j].conflictCount += tmpLockStat->lockConflictStat[i][j].conflictCount;
            heapLockStat->lockConflictStat[i][j].lockEscalationCount +=
                tmpLockStat->lockConflictStat[i][j].lockEscalationCount;
            heapLockStat->lockConflictStat[i][j].lockEscalationFail +=
                tmpLockStat->lockConflictStat[i][j].lockEscalationFail;
            heapLockStat->lockConflictStat[i][j].conflictAcqFail += tmpLockStat->lockConflictStat[i][j].conflictAcqFail;
            heapLockStat->lockConflictStat[i][j].timeout += tmpLockStat->lockConflictStat[i][j].timeout;
            heapLockStat->lockConflictStat[i][j].deadlockRollback +=
                tmpLockStat->lockConflictStat[i][j].deadlockRollback;
        }
    }
}

void LockStatUpdate(LockStatT *lockStat, LockAcqInfoT *lockAcqInfo, StatusInter status)
{
    DB_POINTER2(lockStat, lockAcqInfo);
    if (lockAcqInfo->isTrySameLock) {
        if (status != STATUS_OK_INTER) {
            return;  // 如果本次是try lock同一把锁，表示这一批try lock还是失败，只记录第一次，后续的不重复记录
        }
        // 那表明这一批try lock最后成功了，需要回滚掉第一次的统计
        LockStatHandleTrySameLockSuccess(lockAcqInfo, lockStat);
    }
    DB_ASSERT(lockAcqInfo->lockType < SE_LOCK_TYPE_NUM);
    DB_ASSERT(lockAcqInfo->lastLockMode < SE_LOCK_ACQ_TYPE_NUM);
    LockConflictStatT *lockConflictStat =
        &(lockStat->lockConflictStat[lockAcqInfo->lockType][lockAcqInfo->lastLockMode]);
    if (lockAcqInfo->lastAcqType != (uint8_t)SE_LOCK_ACQ_IS_EXIST) {
        lockConflictStat->trxLockAcquireCount++;
    }
    if (lockAcqInfo->isLockConfit) {  // 发生锁冲突
        lockConflictStat->conflictCount++;
        if (status != STATUS_OK_INTER) {
            lockConflictStat->conflictAcqFail++;  // 发生锁冲突后失败
        }
    }
    if (lockAcqInfo->lastAcqType == (uint8_t)SE_LOCK_ACQ_S_UPD_X ||
        lockAcqInfo->lastAcqType == (uint8_t)SE_LOCK_ACQ_I_UPD_X ||
        lockAcqInfo->lastAcqType == (uint8_t)SE_LOCK_ACQ_IS_UPD_IX) {  // 发生锁升级
        lockConflictStat->lockEscalationCount++;
        if (status != STATUS_OK_INTER) {
            lockConflictStat->lockEscalationFail++;  // 锁升级失败
        }
    }
    if (status == LK_NO_AVA_WAIT_TOO_LONG) {  // 锁超时
        lockConflictStat->timeout++;
    }
    if (status == LK_NO_AVA_DEAD_LOCK) {  // 因死锁回滚
        DB_ASSERT(lockAcqInfo->isDeadLockVictim);
        lockConflictStat->deadlockRollback++;
    }
    if (lockAcqInfo->isTupleLockEscalateToLabel) {
        lockStat->tupleLockEscalateToLabelLock++;  // 行锁升级表锁
    }
}

#ifdef __cplusplus
}
#endif

#endif
