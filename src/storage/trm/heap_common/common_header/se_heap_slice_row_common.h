/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Heap batch共有部分。该.h只能由se_heap_batch.c include。若后续特性开发需要暴露其中某个函数，
 *              请声明于目前于另一个.h文件内并include哪一个。
 * Author: Peng Luo
 * Create: 2024-09-03
 */

#ifndef SE_HEAP_SLICE_ROW_COMMON_H
#define SE_HEAP_SLICE_ROW_COMMON_H

#ifdef __cplusplus
extern "C" {
#endif

void HeapAllocRowInfoInitForSliceSub(const HeapRunCtxT *ctx, HpPageAllocRowInfoT *pageAllocRowInfo, bool isLastSliceRow)
{
    DB_POINTER2(ctx, pageAllocRowInfo);

    HeapAllocRowInfoInitHelper(
        pageAllocRowInfo, HEAP_VAR_ROW_SLICE_SUB_ROW, (PageSizeT)pageAllocRowInfo->bufSize, true);
}

StatusInter HeapSliceUpdateDirRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *sliceDirRowOpInfo)
{
    DB_POINTER2(ctx, sliceDirRowOpInfo);

    HeapRowInfoT *sliceDirRowInfo = &sliceDirRowOpInfo->allocRowInfo->newRowInfo;
    StatusInter ret = HeapPageAddLatch(ctx, sliceDirRowInfo);
    if (ret != STATUS_OK_INTER) {  // 没有加成功. 不用释放
        return ret;
    }
    // 重新加锁, 根据slot, 重新定位到页内的行.
    HVPageHeadT *pageHead = sliceDirRowInfo->pageHeadPtr.varPageHead;
    sliceDirRowInfo->rowHeadPtr.rowState = HeapVarPageGetRowState(pageHead, sliceDirRowInfo->slot);
    // 把各个分片的信息, 设置到 slice dir row 中. 注意: 下面的操作不应失败, 否则回滚流程需要重新考虑!
    HeapVarPageInitLinkDstSliceDirRow(ctx, sliceDirRowInfo->rowHeadPtr.linkRowHead, sliceDirRowOpInfo->allocRowInfo);
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    return STATUS_OK_INTER;
}

inline void HeapInsertFreeMemInNormalPro(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    HeapSliceRowInfoFreeDirMem(ctx, &opInfo->allocRowInfo->sliceRowInfo);
}

StatusInter HeapFetchOneSliceSubRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *subRowOpInfo)
{
    DB_POINTER2(ctx, subRowOpInfo);

    HpPageFetchRowInfo *fetchSubRowInfo = subRowOpInfo->fetchRowInfo;
    fetchSubRowInfo->curRowInfo = &fetchSubRowInfo->srcRowInfo;
    StatusInter ret = HeapFetchGetPage(ctx, subRowOpInfo->fetchRowInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    return HeapFetchVarRowInPage(ctx, subRowOpInfo);
}

HpItemPointerT HeapGetSliceSubItemPtr(HeapRowInfoT *sliceDirRowInfo, uint32_t sliceIdx)
{
    DB_POINTER(sliceDirRowInfo);

    /* 每次都通过slot定位, 避免reLatch过程中, 其他线程发生 页面整理 */
    sliceDirRowInfo->rowHeadPtr.rowState =
        HeapVarPageGetRowState(sliceDirRowInfo->pageHeadPtr.varPageHead, sliceDirRowInfo->slot);
    HpLinkRowHeadT *sliceDirRowHead = sliceDirRowInfo->rowHeadPtr.sliceDirRowHead;
    HpSliceRowDirT *sliceDir = (HpSliceRowDirT *)(sliceDirRowHead + 1);
    return sliceDir->sliceRowEntries[sliceIdx].sliceSubRowPtr;
}

StatusInter HeapDeleteSliceDirAndSubRows(HeapRunCtxT *ctx, HeapRowInfoT *sliceDirRowInfo)
{
    DB_POINTER2(ctx, sliceDirRowInfo);

    /* 分片删除过程中, linkSrc行已经没有了. 为了确保删除成功, 不申请内存保存 sliceDir, 那么需要访问 linkDst 所在的page
     * 每次删除的时候, 最多两个page持有, 一个linkDst, 一个是分片所在的页, 可能发生 reLatch, 所以要注意addr的重入和访问
     */
    StatusInter ret;
    HpLinkRowHeadT *sliceDirRowHead = sliceDirRowInfo->rowHeadPtr.sliceDirRowHead;
    HpSliceRowDirT *sliceDir = (HpSliceRowDirT *)(sliceDirRowHead + 1);
    uint32_t sliceRowNum = sliceDir->sliceRowNum;  // 缓存下来, 避免reLatch导致内存addr无效.
    uint32_t failedCnt = 0;
    for (uint32_t i = 0; i < sliceRowNum; i++) {
        HpItemPointerT sliceSubRowPtr = HeapGetSliceSubItemPtr(sliceDirRowInfo, i);
        ret = HeapDeleteInnerRow(ctx, sliceSubRowPtr, NULL);
        if (ret != STATUS_OK_INTER) {
            failedCnt++;
            SE_ERROR(ret, "Del slice subRow (%" PRIu32 "), labelId %" PRIu32, i, ctx->heapCfg.labelId);
            // 不返回, 尽量继续删除.
        }
    }
    // 全部 分片删除完后, 最后删除 linkDst(sliceDir) 行.
    ret = HeapDeleteInnerRow(ctx, sliceDirRowInfo->itemPtr, NULL);
    if (ret != STATUS_OK_INTER) {
        failedCnt++;
        SE_ERROR(ret, "Del slice dirRow labelId %" PRIu32, ctx->heapCfg.labelId);
    }

    if (failedCnt > 0) {
        SE_ERROR(INT_ERR_HEAP_UNEXPECT_ERROR, "Del slice dirRow & subs, unsucc %" PRIu32 " of %" PRIu32, failedCnt,
            sliceRowNum);
        return INT_ERR_HEAP_UNEXPECT_ERROR;
    }
    return STATUS_OK_INTER;
}

#ifdef __cplusplus
}
#endif

#endif
