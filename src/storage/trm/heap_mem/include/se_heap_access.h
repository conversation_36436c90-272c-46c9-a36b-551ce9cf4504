/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: Heap access method 的内部提供的接口和结构
 * Author: panghaisheng
 * Create: 2020-8-12
 */
#ifndef SE_HEAP_ACCESS_H
#define SE_HEAP_ACCESS_H

#include "se_heap.h"

#ifdef __cplusplus
extern "C" {
#endif

// to be deprecated
typedef struct TagHeapAccessMethodBatchFetchTupleBufsT {
    void *usrMemCtx;
    HeapTupleBufAndRowInfoT *heapTupleBuf;
} HpAmBatchFetchTpBufsT;

typedef struct TagHeapAccessMethodScannedTupleBufsT {
    TupleBufT *bufs;  // 指向上层传入的 TupleBufT
    HeapFetchedRowInfoT *tupleBufWithRowInfo;
} HpAmScannedFetchTpBufsT;

typedef struct RsmHeapContainerInfo {
    PageSizeT fixRowSize;  // pageType = HEAP_FIX_LEN_ROW_PAGE 时需要保存下来
    uint16_t reserve;
    uint32_t heapTrmId;
    uint32_t tableSpaceId;
    uint32_t tableSpaceIndex;
    uint64_t maxItemNum;
} RsmHeapContainerInfoT;

SO_EXPORT StatusInter HeapLabelScanCopyHpTupleBuf(const HeapTupleBufAndRowInfoT *heapBuffAndRowInfo, uint32_t rowIndex,
    void *userData, HpScanSubsActT *subsequentAction);

SO_EXPORT StatusInter HeapScanCopyHpTupleBuf(const HeapTupleBufAndRowInfoT *heapBuffAndRowInfo, uint32_t rowIndex,
    void *userData, HpScanSubsActT *subsequentAction);

#ifdef __cplusplus
}
#endif

#endif  // SE_HEAP_ACCESS_H
