/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Heap 模块存储引擎回滚相关函数
 * Author:
 * Create: 2023/03/31
 */
#include "se_heap_trx_acc_inner.h"

#include "se_heap_utils.h"
#include "se_undo.h"
#include "se_trx_mgr.h"
#include "se_heap_stats.h"
#include "se_heap_fetch.h"
#include "se_heap_fetch.h"
#include "se_heap_rsm_undo.h"
#include "se_log.h"

#include "se_heap_rollback_common.h"
#include "se_heap_am_inner.h"

void HeapUnlinkRollPtrUpdRollPtr(const HeapRunCtxT *ctx, HpRowHeadPtrT *rowHeadPtr, const UndoRowOpInfoT *rowOpInfo)
{
    if (ctx->hpControl.isFixPage) {
        if (HeapPageIsNormalRow(rowHeadPtr->rowState)) {
            rowHeadPtr->normalFixRowHead->trxInfo.rollPtr = rowOpInfo->rowRollPtr;
        } else if (HeapPageIsLinkSrcRow(rowHeadPtr->rowState)) {
            rowHeadPtr->linkSrcFixRowHead->trxInfo.rollPtr = rowOpInfo->rowRollPtr;
        } else {
            SE_LAST_ERROR(DATA_EXCEPTION_INTER,
                "inv fix page row state, trxid %" PRIu64 ", rollPtr %" PRIu64 ", labelId %" PRIu32, rowOpInfo->rowTrxId,
                rowOpInfo->rowRollPtr, ctx->heapCfg.labelId);
            DB_ASSERT(false);
        }
    } else if (HeapPageIsNormalRow(rowHeadPtr->rowState)) {
        rowHeadPtr->normalRowHead->trxInfo.rollPtr = rowOpInfo->rowRollPtr;
    } else if (HeapPageIsLinkSrcRow(rowHeadPtr->rowState)) {
        rowHeadPtr->linkRowHead->srcTrxInfo.rollPtr = rowOpInfo->rowRollPtr;
    } else {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "inv row state, trxid %" PRIu64 ", rollPtr %" PRIu64 ", labelId %" PRIu32,
            rowOpInfo->rowTrxId, rowOpInfo->rowRollPtr, ctx->heapCfg.labelId);
        DB_ASSERT(false);
    }
}

StatusInter HeapUnlinkRollPtrImpl(
    HeapRunCtxT *ctx, HpTupleAddr heapTupleAddr, uint64_t targetRollPtr, const UndoRowOpInfoT *rowOpInfo)
{
    DB_POINTER2(ctx, rowOpInfo);
    TrxT *trx = (TrxT *)ctx->seRunCtx->trx;
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, NULL);
    fetchRowInfo.isCanAccessMarkDelRow = true;
    fetchRowInfo.isOnlyReadSrcRow = true;
    fetchRowInfo.srcRowInfo.itemPtr = *(HpItemPointerT *)&heapTupleAddr;
    fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;
    DB_ASSERT(ctx->hpOperation == HEAP_OPTYPE_UNDO_PURGER);
    HeapSetPageLatchActionForWrite(ctx);
    HeapSetLockActionBeforeGcUnlinkRollPtr(ctx);
    StatusInter ret = HeapFetchRow(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
        return ret;
    }
    bool isOldRowCommitted = TrxMgrIsTrxCommitted(trx->trxMgr, rowOpInfo->rowTrxId);
    bool isRowRollPtrValid = UndoCheckRollPtrMagicNum(rowOpInfo->rowRollPtr);
    DB_ASSERT(isRowRollPtrValid);  // 预期此时应该没有下一个版本
    HpRowHeadPtrT rowHeadPtr;
    if (targetRollPtr == fetchRowInfo.rowTrxInfo.rollPtr) {
        // 要移除的 targetRollPtr 就是当前行上的rollPtr, 则修改指向下一行
        // 同时还原行占用空间为真实大小，包含乐观和悲观的场景
        rowHeadPtr = fetchRowInfo.srcRowInfo.rowHeadPtr;
        if (!ctx->hpControl.isFixPage && isOldRowCommitted) {
            HVPageHeadT *pageHead = fetchRowInfo.srcRowInfo.pageHeadPtr.varPageHead;
            // 非保留内存不涉及rsmUndo(轻量化事务才支持保留内存，此处是正常事务的逻辑)，最后一个参数传false
            HeapVarRowClearReserveSize(ctx, rowHeadPtr, pageHead, pageHead->baseHead.pageHead.freeSize, false);
        }
    } else {
        // 遍历undo版本链, 移除指定版本
        // 需要维护rollPtr字段与buf（行头）里的rollPtr
        UndoTupleCombineAddrT undoTuple = {0};
        ret = RowUndoGetUnlinkTargetRecord(
            ctx->seRunCtx->undoCtx, fetchRowInfo.rowTrxInfo.rollPtr, rowOpInfo, targetRollPtr, &undoTuple);
        if (ret != STATUS_OK_INTER) {
            DB_ASSERT(false);
            SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
            return ret;
        }
        rowHeadPtr.rowState = (HpRowStateT *)undoTuple.tuple.data;
    }
    HeapUnlinkRollPtrUpdRollPtr(ctx, &rowHeadPtr, rowOpInfo);
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    return STATUS_OK_INTER;
}

StatusInter HeapFixPageRollbackMemcpyLinkSrc(
    HeapRunCtxT *ctx, HpRowHeadPtrT oldRowInLog, HpPageFetchRowInfo *fetchCurRowInfo, bool isNeedDelCurrentLinkDst)
{
    if (!ctx->heapCfg.isUseRsm) {
        errno_t err = memcpy_s(fetchCurRowInfo->srcRowInfo.rowHeadPtr.linkSrcFixRowHead, sizeof(HpLinkSrcFixRowHead),
            oldRowInLog.linkSrcFixRowHead, sizeof(HpLinkSrcFixRowHead));
        DB_ASSERT(err == EOK);
        return STATUS_OK_INTER;
    }
    StatusInter ret = HeapRsmUndoLogForFixLinkSrcRollback(ctx, fetchCurRowInfo->srcRowInfo.itemPtr,
        fetchCurRowInfo->srcRowInfo.pageHeadPtr.fixPageHead->pageCfg.oneRowSize,  // 直接拷贝槽位的大小
        (uint8_t *)fetchCurRowInfo->srcRowInfo.rowHeadPtr.rowState);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "rollbakc, labelId:%" PRIu32 ", pageId:%" PRIu32 ", slotId:%" PRIu32, ctx->heapCfg.labelId,
            fetchCurRowInfo->srcRowInfo.itemPtr.pageId, fetchCurRowInfo->srcRowInfo.itemPtr.slotId);
        return ret;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_FH_UPD_ROLLBACK_TO_SRC_ROW_BEFORE);
    /* 要回滚到的版本, 是一个link行, 它空间是最小的. 直接覆盖就可以了. */
    errno_t err = memcpy_s(fetchCurRowInfo->srcRowInfo.rowHeadPtr.linkSrcFixRowHead, sizeof(HpLinkSrcFixRowHead),
        oldRowInLog.linkSrcFixRowHead, sizeof(HpLinkSrcFixRowHead));
    DB_ASSERT(err == EOK);
    SHM_CRASHPOINT_ONE(SHM_CRASH_FH_UPD_ROLLBACK_TO_SRC_ROW_AFTER);
    if (!isNeedDelCurrentLinkDst) {  // no need删除dst时，直接删除rsmUndo即可
        HeapRsmUndoLogMovePrevRecord(ctx);
    }
    // 如果需要删除dst，需要保证src与dst的修改一致性，此处的rsmUndo将在HeapFixPageDeleteDstRow中删除
    return STATUS_OK_INTER;
}

StatusInter HeapFixPageRollback2LinkSrcRow(
    HeapRunCtxT *ctx, HpRowHeadPtrT oldRowInLog, HpPageFetchRowInfo *fetchCurRowInfo)
{
    DB_POINTER3(ctx, ctx->heap, fetchCurRowInfo);
    DB_ASSERT(fetchCurRowInfo->srcRowInfo.rowType == HEAP_FIX_ROW_NORMAL_ROW ||
              fetchCurRowInfo->srcRowInfo.rowType == HEAP_FIX_ROW_LINK_SRC_ROW);
    bool isNeedDelCurrentLinkDst = false;
    HpItemPointerT currentDstPrt = {0};
    if (fetchCurRowInfo->srcRowInfo.rowType == HEAP_FIX_ROW_LINK_SRC_ROW) {  // 前后都是 跳转行
        // 如果记录了undo日志, link行更新就失败了(原来的行内容没有变动). 回滚时, 不能删除这个 linkDst 行.
        currentDstPrt = fetchCurRowInfo->srcRowInfo.rowHeadPtr.linkSrcFixRowHead->linkItemPtr;
        // addr一样, 不应该删除. 否则应该删除
        isNeedDelCurrentLinkDst = !HeapIsSameAddr(currentDstPrt, oldRowInLog.linkSrcFixRowHead->linkItemPtr);
    }
    StatusInter ret = HeapFixPageRollbackMemcpyLinkSrc(ctx, oldRowInLog, fetchCurRowInfo, isNeedDelCurrentLinkDst);
    if (ret != STATUS_OK_INTER) {
        // 失败的场景只有保留内存申请rsmUndo失败
        return ret;
    }
    if (isNeedDelCurrentLinkDst) {
        HeapCleanLinkDstRow(ctx, currentDstPrt);
    }
    return STATUS_OK_INTER;
}

StatusInter HeapVarPageRollbackMemcpyLinkSrc(HeapRunCtxT *ctx, HpRowHeadPtrT oldRowInLog,
    HpPageFetchRowInfo *fetchCurRowInfo, PageSizeT rollbackReserveSize, bool isNeedDelCurrentLinkDst)
{
    HVPageHeadT *pageHead = fetchCurRowInfo->srcRowInfo.pageHeadPtr.varPageHead;
    bool isCurSrcRow = fetchCurRowInfo->srcRowInfo.rowType == HEAP_VAR_ROW_LINK_SRC_ROW;
    uint32_t memcpySize =
        isCurSrcRow ?
            sizeof(HpLinkRowHeadT) :
            HeapPageGetMinRowSize(fetchCurRowInfo->srcRowInfo.rowHeadPtr.normalRowHead->size, HEAP_VAR_ROW_NORMAL_ROW);
    StatusInter ret = HeapRsmUndoLogForVarLinkSrcRollback(ctx, fetchCurRowInfo->srcRowInfo.itemPtr, memcpySize,
        (uint8_t *)fetchCurRowInfo->srcRowInfo.rowHeadPtr.rowState, pageHead->baseHead.pageHead.freeSize);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "rollback, labelId:%" PRIu32 ", pageId:%" PRIu32 ", slotId:%" PRIu32, ctx->heapCfg.labelId,
            fetchCurRowInfo->srcRowInfo.itemPtr.pageId, fetchCurRowInfo->srcRowInfo.itemPtr.slotId);
        return ret;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_VH_UPD_ROLLBACK_TO_SRC_ROW_BEFORE);
    errno_t err = memcpy_s(fetchCurRowInfo->srcRowInfo.rowHeadPtr.linkRowHead, sizeof(HpLinkRowHeadT),
        oldRowInLog.linkRowHead, sizeof(HpLinkRowHeadT));
    DB_ASSERT(err == EOK);
    DB_ASSERT(fetchCurRowInfo->srcRowInfo.rowHeadPtr.linkRowHead->magicNum == HEAP_ROW_MAGIC_NUM);
    // 回滚跳转行，还原行占用空间为0，此处肯定是悲观（轻量化）的情况
    // rollBack src 是commit, 则回收内存并更新FSM
    // 此处不回退rsmUndo，等下面判断完是否需要删除dst行时再看要不要回退
    HeapVarRowClearReserveSize(
        ctx, fetchCurRowInfo->srcRowInfo.rowHeadPtr, pageHead, pageHead->baseHead.pageHead.freeSize, false);
    SHM_CRASHPOINT_ONE(SHM_CRASH_VH_UPD_ROLLBACK_TO_SRC_ROW_AFTER);
    if (!isNeedDelCurrentLinkDst) {  // no need删除dst时，直接删除rsmUndo即可
        HeapRsmUndoLogMovePrevRecord(ctx);
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_VH_UPD_ROLLBACK_TO_SRC_ROW_FINISH);
    // 如果需要删除dst，需要保证src与dst的修改一致性，此处的rsmUndo将在HeapVarPageDeleteDstRow中删除
    return STATUS_OK_INTER;
}

StatusInter HeapVarPageRollback2LinkSrcRow(
    HeapRunCtxT *ctx, HpRowHeadPtrT oldRowInLog, HpPageFetchRowInfo *fetchCurRowInfo, bool isTrxCommitted)
{
    DB_POINTER3(ctx, ctx->heap, fetchCurRowInfo);
    DB_ASSERT(fetchCurRowInfo->srcRowInfo.rowType == HEAP_VAR_ROW_NORMAL_ROW ||
              fetchCurRowInfo->srcRowInfo.rowType == HEAP_VAR_ROW_LINK_SRC_ROW);
    StatusInter ret;
    bool isNeedDelCurrentLinkDst = false;
    bool isMasterVersionMarkDeleted = fetchCurRowInfo->srcRowInfo.rowHeadPtr.rowState->isDeleted;  // 乐观下有可能为true
    HpItemPointerT currentDstPrt = {0};
    PageSizeT rollbackReserveSize = 0;
    if (fetchCurRowInfo->srcRowInfo.rowType == HEAP_VAR_ROW_LINK_SRC_ROW) {  // 前后都是 跳转行
        // 如果记录了undo日志, link行更新就失败了(原来的行内容没有变动). 回滚时, 不能删除这个 linkDst 行.
        currentDstPrt = fetchCurRowInfo->srcRowInfo.rowHeadPtr.linkRowHead->linkItemPtr;
        rollbackReserveSize = fetchCurRowInfo->srcRowInfo.rowHeadPtr.linkRowHead->rollbackReserveSize;
        // addr一样, 不应该删除. 否则应该删除
        isNeedDelCurrentLinkDst = !HeapIsSameAddr(currentDstPrt, oldRowInLog.linkRowHead->linkItemPtr);
    } else {
        // 删除(覆盖)当前行即可，是否需要回收该内存取决于rollBack的src行是否commit,
        // 是的话则回收内存在下HeapVarRowClearReserveSize
        rollbackReserveSize = fetchCurRowInfo->srcRowInfo.rowHeadPtr.normalRowHead->rollbackReserveSize;
    }
    if (!ctx->heapCfg.isUseRsm) {
        /* 要回滚到的版本, 是一个link行, 它空间是最小的. 直接覆盖就可以了. */
        errno_t err = memcpy_s(fetchCurRowInfo->srcRowInfo.rowHeadPtr.linkRowHead, sizeof(HpLinkRowHeadT),
            oldRowInLog.linkRowHead, sizeof(HpLinkRowHeadT));
        DB_ASSERT(err == EOK);
        // 回滚跳转行，还原行占用空间为0，同时涵盖乐观和悲观的情况
        if (isTrxCommitted) {
            // rollBack src 是commit, 则回收内存并更新FSM
            // 非保留内存不涉及rsmUndo，最后一个参数传false
            HVPageHeadT *pageHead = fetchCurRowInfo->srcRowInfo.pageHeadPtr.varPageHead;
            HeapVarRowClearReserveSize(
                ctx, fetchCurRowInfo->srcRowInfo.rowHeadPtr, pageHead, pageHead->baseHead.pageHead.freeSize, false);
        } else {
            // 重新刷新回去
            fetchCurRowInfo->srcRowInfo.rowHeadPtr.linkRowHead->rollbackReserveSize = rollbackReserveSize;
        }
    } else {
        DB_ASSERT(isTrxCommitted);  // 保留内存只支持轻量化事务，一定是提交版本
        ret = HeapVarPageRollbackMemcpyLinkSrc(
            ctx, oldRowInLog, fetchCurRowInfo, rollbackReserveSize, isNeedDelCurrentLinkDst);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    if (isNeedDelCurrentLinkDst && !isMasterVersionMarkDeleted) {
        // masterVersion是删除的状态时，它的内存可能是已回滚事务的内存，此时不应该去访问dstRow
        HeapCleanLinkDstRow(ctx, currentDstPrt);
    }
    return STATUS_OK_INTER;
}

StatusInter HeapRollback2LinkSrcRowImpl(
    HeapRunCtxT *ctx, HpRowHeadPtrT oldRowInLog, HpPageFetchRowInfo *fetchCurRowInfo, bool isTrxCommitted)
{
    DB_POINTER3(ctx, ctx->heap, fetchCurRowInfo);
    if (ctx->hpControl.isFixPage) {
        return HeapFixPageRollback2LinkSrcRow(ctx, oldRowInLog, fetchCurRowInfo);
    } else {
        return HeapVarPageRollback2LinkSrcRow(ctx, oldRowInLog, fetchCurRowInfo, isTrxCommitted);
    }
}

StatusInter HeapUpdateRollbackWithOptimisticOrReReadTrx(
    HeapRunCtxT *ctx, HpItemPointerT itemPtr, uint64_t targetRollPtr)
{
    DB_POINTER(ctx);
    HeapSetIsNeedRowLock(ctx, false);  // 回滚流程不加事务锁
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    HpPageAllocRowInfoT pageAllocRowInfo = EmptyHpPageAllocRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, &pageAllocRowInfo);
    // 这里需要读取主版本，检查targetRollptr是否在主版本上，因此要isCanAccessMarkDelRow为true
    fetchRowInfo.isCanAccessMarkDelRow = true;
    fetchRowInfo.srcRowInfo.itemPtr = itemPtr;
    fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;
    HeapSetPageLatchActionForWrite(ctx);
    HeapSetOperationForUpdate(ctx);
    HeapSetLockActionBeforeRollBackUpdate(ctx);

    if (SeTransGetTrxType(ctx->seRunCtx) == OPTIMISTIC_TRX) {
        (void)HeapLabelResetOpType(ctx, HEAP_OPTYPE_UNDO_PURGER, false);
        DB_ASSERT(ctx->hpOperation == HEAP_OPTYPE_UNDO_PURGER);
        fetchRowInfo.targetRollPtr = targetRollPtr;
    } else {
        // 悲观RR时，主版本必定是本事务产生的，直接读取行内容, 要访问dstRow
        fetchRowInfo.canAccessLinkDst = true;
        fetchRowInfo.isOnlyReadSrcRow = false;
    }

    StatusInter ret = HeapFetchRow(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        goto EXIT;
    }
    UndoRowOpInfoT rowOpInfo = {0};
    ret = HeapRepReadReReadRollBackInfo(ctx, targetRollPtr, &rowOpInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        goto EXIT;
    }

    if (SeTransGetTrxType(ctx->seRunCtx) == PESSIMISTIC_TRX) {
        ret = HeapRollbackPrepareWithPessimisticReReadTrx(ctx, &fetchRowInfo, &rowOpInfo, targetRollPtr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            goto EXIT;
        }
    }

    if (targetRollPtr == fetchRowInfo.rowTrxInfo.rollPtr) {
        ret = HeapRollbackInMasterVersion(ctx, &rowOpInfo, &opInfo);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "masterVersion heap update rollback. labelId:%" PRIu32 " trxId:%" PRIu64, rowOpInfo.labelId,
                ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
        }
    } else {
        ret = HeapUpdateRollbackInVersionChain(ctx, &fetchRowInfo, &rowOpInfo, targetRollPtr);
    }
EXIT:
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    return ret;
}

StatusInter HeapUpdateRollbackWithPessimisticTrx(
    HeapRunCtxT *ctx, HpItemPointerT itemPtr, const UndoRowOpInfoT *rowOpInfo)
{
    DB_POINTER3(ctx, rowOpInfo, rowOpInfo->rowBuf);
    RowTrxInfoT txInfo = RowTrxInfoCreate(rowOpInfo->rowTrxId, rowOpInfo->rowRollPtr);
    HeapSetTrxInfoForRollBack(ctx, txInfo);
    DB_ASSERT(rowOpInfo->isDeleted == false);  // 悲观事务下，因为有事务锁，旧版本的IsDeleted一定为false
    ctx->rollBackInfo.oldIsDeleted = rowOpInfo->isDeleted;

    StatusInter ret = STATUS_OK_INTER;

    TrxT *trx = (TrxT *)ctx->seRunCtx->trx;
    HpRowStateT *oldRowStateInLog = (HpRowStateT *)rowOpInfo->rowBuf;
    HpRowHeadPtrT oldRowInLog = {.rowState = NULL};
    oldRowInLog.rowState = oldRowStateInLog;
    DB_ASSERT(oldRowInLog.normalRowHead->magicNum == HEAP_ROW_MAGIC_NUM);

    if (rowOpInfo->opType == TRX_OP_UPDATE_PART) {
        HeapTupleBufT tuple = {.buf = rowOpInfo->rowBuf + rowOpInfo->srcRowHeadSize, .bufSize = rowOpInfo->bufSize};
        ret = HeapUpdatePartialImpl(ctx, &tuple, &itemPtr, rowOpInfo->offsetOfRawData);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "partial update row rollback. labelId: %" PRIu32 " trxId: %" PRIu64, rowOpInfo->labelId,
                ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
        }
        goto EXIT;
    }

    bool oldRowIsNormalRow = HeapPageIsNormalRow(oldRowStateInLog);
    if (oldRowIsNormalRow) {
        HeapTupleBufT tupleBuf = {0};
        HeapUpdateRollBackGetOldBuf(ctx, &oldRowInLog, rowOpInfo->rowSize, &tupleBuf);
        HeapUpdOutPara out = {.isUndoBypass = NULL};
        ret = HeapUpdateImpl(ctx, &tupleBuf, &itemPtr, &out);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            // 保留内存申请rsmUndo可能会失败，返回出去切换逃生内存
            // 当场切换会持有页锁去等待 控制逃生内存串行使用的锁，造成死锁
            SE_ERROR(ret, "normal row update rollback. labelId: %" PRIu32 " trxId: %" PRIu64, rowOpInfo->labelId,
                ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
        }
        goto EXIT;
    }
    DB_ASSERT(rowOpInfo->rowSize == sizeof(HpLinkRowHeadT) || rowOpInfo->rowSize == sizeof(HpLinkSrcFixRowHead));
    bool isOldRowCommitted = false;
    ret = TrxMgrCheckTrxIsCommitted(trx->trxMgr, rowOpInfo->rowTrxId, &isOldRowCommitted);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        goto EXIT;
    }
    ret = HeapUpdateRollback2LinkSrcRow(ctx, oldRowInLog, itemPtr, isOldRowCommitted);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        // 保留内存申请rsmUndo可能会失败，返回出去切换逃生内存
        // 当场切换会持有页锁去等待 控制逃生内存串行使用的锁，造成死锁
        SE_ERROR(ret, "link src row update rollback. labelId: %" PRIu32 " trxId: %" PRIu64, rowOpInfo->labelId,
            ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
    }
EXIT:
    if (ret != OUT_OF_MEMORY_INTER) {
        // 保留内存申请rsmUndo可能会失败，返回出去切换逃生内存
        // 当场切换会持有页锁去等待 控制逃生内存串行使用的锁，造成死锁, 等切换完毕后第二次进入再维护DFX
    }
    return ret;
}

/*
为什么要传入targetRollPtr ?
乐观下可能有多个事务在操作同一行，因此当前回滚的事务是不一定在masterVersion上
eg:
                     trx3写的undo    trx2写的undo    trx1写的undo
-----------------    ------------    -----------    -----------
| masterVersion |--->| undoLog2 |--->| undoLog1|--->| undoLog0|
-----------------    ------------    -----------    -----------
    trx3的修改         trx2的修改      trx1的修改    trx0插入的版本

假设当前场景是要回滚trx2的修改, 当前处理的是trx2写的undo, 此时targetRollPtr是trx2写的undo的addr（undoLog1的addr）：

需要读出masterVersion，将上面的rollPtr（undoLog2的addr）与targetRollPtr进行比较，在此场景下发现不相同，
开始遍历版本链，找到undoLog2里面记录的rollPtr（undoLog1的addr）与targetRollPtr比较，在此场景下就匹配成功了
接下来将undoLog1里面的内容写入到undoLog2中（undoLog2里的内容是本次要回滚的，需要丢掉），即完成了本次回滚。
当前reserveSize可以保证在由大更新为小时使用较大的空间作为预留，不会有undoLog2无法容纳下undoLog1的情况。
*/
StatusInter HeapUpdateRollbackImpl(
    HeapRunCtxT *ctx, HpItemPointerT itemPtr, uint64_t targetRollPtr, const UndoRowOpInfoT *rowOpInfo)
{
    // rollBack阶段要回滚row
    // 悲观事务下，因为有事务锁，一定在主版本上
    // 乐观事务下，需要先加上页latch，然后判断是否在主版本，不在再去版本链去找
    DB_POINTER(ctx);
    if (SeTransGetTrxType(ctx->seRunCtx) == OPTIMISTIC_TRX ||
        SeTransGetIsolationLevel(ctx->seRunCtx) == REPEATABLE_READ) {
        return HeapUpdateRollbackWithOptimisticOrReReadTrx(ctx, itemPtr, targetRollPtr);
    } else {
        return HeapUpdateRollbackWithPessimisticTrx(ctx, itemPtr, rowOpInfo);
    }
}

static StatusInter HeapPessimisticMarkDeleteRollBack(
    HeapRunCtxT *ctx, uint64_t targetRollPtr, const UndoRowOpInfoT *rowOpInfo, HpPageRowOpInfoT *opInfo)
{
    StatusInter ret = STATUS_OK_INTER;
    UndoRowOpInfoT newRowOpInfo = {0};
    if (SeTransGetIsolationLevel(ctx->seRunCtx) == REPEATABLE_READ) {
        ret = HeapRepReadReReadRollBackInfo(ctx, targetRollPtr, &newRowOpInfo);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
    } else {
        newRowOpInfo = *rowOpInfo;
    }
    RowTrxInfoT txInfo = {{0}};
    txInfo.rollPtr = newRowOpInfo.rowRollPtr;
    RowSetTrxId(&txInfo, newRowOpInfo.rowTrxId);
    HeapSetTrxInfoForRollBack(ctx, txInfo);
    ctx->rollBackInfo.oldIsDeleted = newRowOpInfo.isDeleted;
    DB_ASSERT(newRowOpInfo.isDeleted == false);  // 悲观事务下，因为有事务锁，旧版本的IsDeleted一定为false
    // this will update the trxId and rollPtr of master version
    (void)HeapLabelResetOpType(ctx, HEAP_OPTYPE_DELETE, false);
    // Normal模式下: HeapClearRowMarkDelete (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
    HeapRecoverFunc(ctx, opInfo);
    return STATUS_OK_INTER;
}

/*
为什么要传入targetRollPtr ?
乐观下可能有多个事务在操作同一行，因此当前回滚的事务是不一定在masterVersion上（见HeapUpdateRollback函数的示意图）
*/
StatusInter HeapMarkDeleteRollBackImpl(
    HeapRunCtxT *ctx, HpItemPointerT itemPtr, uint64_t targetRollPtr, const UndoRowOpInfoT *rowOpInfo)
{
    DB_POINTER(ctx);
    /* 标记删除回滚 */
    DB_ASSERT(ctx->hpOperation <= HEAP_OPTYPE_MODIFY);
    DB_ASSERT(((TrxT *)ctx->seRunCtx->trx)->base.state == TRX_STATE_ROLLBACK);
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    HpPageAllocRowInfoT pageAllocRowInfo = EmptyHpPageAllocRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, &pageAllocRowInfo);
    fetchRowInfo.srcRowInfo.itemPtr = itemPtr;
    fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;
    fetchRowInfo.isCanAccessMarkDelRow = true;  // 允许访问 被标记为删除的行
    fetchRowInfo.targetRollPtr = targetRollPtr;
    HeapSetPageLatchActionForWrite(ctx);
    HeapSetOperationForMarkDeleteRollBack(ctx);
    // Normal模式下: HeapSetLockActionBeforeRollBackDelete (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
    HeapSetLockFunc(ctx, ctx->hpControl.trxContrl.hpAmIndex);
    // 查询, 获取当前 row 的信息
    StatusInter ret = HeapFetchRow(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        goto EXIT;
    }
    TrxTypeE trxType = SeTransGetTrxType(ctx->seRunCtx);
    UndoRowOpInfoT newRowOpInfo = {0};
    if (trxType == OPTIMISTIC_TRX) {
        ret = HeapRepReadReReadRollBackInfo(ctx, targetRollPtr, &newRowOpInfo);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            goto EXIT;
        }
        HeapMarkDeleteRollBackWithOptimisticTrx(targetRollPtr, &fetchRowInfo, ctx, &newRowOpInfo, &opInfo);
    } else {
        DB_ASSERT(trxType == PESSIMISTIC_TRX);  // 存储层处理时只有乐观、悲观两种枚举情况
        ret = HeapPessimisticMarkDeleteRollBack(ctx, targetRollPtr, rowOpInfo, &opInfo);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            goto EXIT;
        }
    }

    HeapStatMarkDeleteFailed(ctx);
EXIT:
    /* 释放 pageSorter 持有的latch */
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    return ret;
}
