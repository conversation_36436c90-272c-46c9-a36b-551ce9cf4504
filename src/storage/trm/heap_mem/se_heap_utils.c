/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: Heap 模块内部一些辅助函数
 * Author: yuanjincheng
 * Create: 2021-10-13
 */
#include "se_heap_utils.h"
#include "se_heap_mem_inner.h"
#include "db_hash.h"
#include "db_crash_debug.h"
#include "db_rsm_kernel.h"
#include "adpt_sleep.h"
#include "se_undo.h"
#include "se_heap_stats.h"
#include "se_heap_trx_acc.h"
#include "se_heap_access_dm.h"
#include "se_log.h"

#include "se_heap_utils_common.h"
#include "se_heap_am_inner.h"

#ifdef __cplusplus
extern "C" {
#endif

StatusInter HeapAllocRunctxImpl(SeRunCtxHdT seRunCtxHd, HeapRunCtxT **ctx)
{
    DB_POINTER2(seRunCtxHd, ctx);
    SeRunCtxT *seRunCtx = seRunCtxHd;
    // 申请heap容器的运行上下文, 在HeapReleaseRunctx中配对释放
    HeapRunCtxT *newCtx = DbDynMemCtxAlloc(seRunCtx->sessionMemCtx, sizeof(HeapRunCtxT));
    if (SECUREC_UNLIKELY(newCtx == NULL)) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc heap ctx");
        return OUT_OF_MEMORY_INTER;
    }
    *newCtx = EmptyHeapRunCtxT();
    newCtx->seRunCtx = seRunCtx;
    *ctx = newCtx;
    return STATUS_OK_INTER;
}

static void HeapOpenInitHpControl(
    HeapControlT *hpControl, HeapRunCtxT *ctx, const HeapRunCtxAllocCfgT *cfg, const HeapT *heap)
{
    DB_POINTER4(hpControl, ctx, cfg, heap);
    /* HeapAllocRunctx后不会变的 */
    hpControl->ccType = heap->constInfo.ccType;
    if (DmIsLabelLatchMode(hpControl->ccType) || hpControl->ccType == CONCURRENCY_CONTROL_READ_UNCOMMIT) {
        if (cfg->isBackGround) {
            hpControl->accessMethodType =
                (ctx->heapCfg.isUseRsm) ? HEAP_ACCESS_METHOD_RSM_LITE_BG : HEAP_ACCESS_METHOD_LITE_BG;  // 后台缩容线程
        } else {
            hpControl->accessMethodType = HEAP_ACCESS_METHOD_LITE;  // 大表锁模式、RU模式共用一套钩子函数
        }
    } else {
        hpControl->accessMethodType = HEAP_ACCESS_METHOD_NORMAL;
    }

    hpControl->isSkipUndoIndex = false;
    hpControl->isToGetOldestVisibleBuf = false;
    hpControl->isCanAccessSelfDeletedBuf = false;
    hpControl->isBackGround = cfg->isBackGround;
    hpControl->isFixPage = heap->constInfo.pageType == HEAP_FIX_LEN_ROW_PAGE;
    hpControl->isNeedRowLock = HeapIsOnNormalTrxLockMode(ctx);
#ifdef FEATURE_GQL
    // 无行锁不用事务锁
    hpControl->isNeedRowLock = TrxSkipRowLock(ctx->seRunCtx) ? false : hpControl->isNeedRowLock;
#endif
    // 乐观下也不用事务锁
    hpControl->isNeedRowLock = heap->constInfo.trxType == OPTIMISTIC_TRX ? false : hpControl->isNeedRowLock;
    // 串行化下不用加行锁
    hpControl->isNeedRowLock = heap->constInfo.isolation == SERIALIZABLE ? false : hpControl->isNeedRowLock;
    // 串行化下是否需要加事务表锁
    hpControl->isLabelLockSerializable = heap->constInfo.isLabelLockSerializable;
    /* 在heap流程中内部控制字段 */
    hpControl->isPageReadOnly = 0;
    hpControl->isRsmRecovery = false;
    hpControl->trxContrl = EmptyHeapTrxControl();
    hpControl->trxContrl.heapAmForTrxFun = g_gmdbHeapTrxAmForGroup[(uint32_t)hpControl->accessMethodType];
}

static inline void HeapInitNewRunCtxSetPageCache(HeapRunCtxT *newCtx)
{
    *(uint64_t *)&newCtx->pageCache.lastPageLogicPtr = DB_INVALID_UINT64;
    newCtx->pageCache.lastPageHeadPtr = NULL;
    *(uint64_t *)&newCtx->pageCache.lastRowLogicPtr = DB_INVALID_UINT64;
    *(uint64_t *)&newCtx->pageCache.hcLastPageLogicPtr = DB_INVALID_UINT64;
    newCtx->pageCache.hcLastPageHeadPtr = NULL;
    newCtx->pageCache.hcLastPageShmAddr = DB_INVALID_SHMPTR;
    *(uint64_t *)&newCtx->pageCache.hcLastRowLogicPtr = DB_INVALID_UINT64;
    newCtx->pageCache.lastRowLockMode = SE_LOCK_MODE_MAX;
    newCtx->pageCache.lastPageBlockId = SE_INVALID_BLOCK_ID;
    newCtx->pageCache.cacheVersion = DB_INVALID_UINT32;
}

StatusInter HeapInitNewRunCtx(HeapT *heap, HeapRunCtxT *newCtx, const HeapRunCtxAllocCfgT *cfg, HeapJumpT *heapJump)
{
    /* HeapAllocRunctx后不会变的 */
    newCtx->seRunCtx = cfg->seRunCtx;
    newCtx->heapJump = heapJump;
    newCtx->pageMgr = newCtx->seRunCtx->pageMgr;
    DB_ASSERT(newCtx->pageMgr->type == SE_MEMDATA);
    newCtx->enableCache = true;
    newCtx->heapCfg = heap->constInfo;
    newCtx->runtimeInfoShmPtr = heap->runtimeInfoShmPtr;
    newCtx->runtimeInfo = DbShmPtrToAddr(heap->runtimeInfoShmPtr);
    if (SECUREC_UNLIKELY(newCtx->runtimeInfo == NULL)) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "init HeapRunCtx, segid:%" PRIu32 " offset:%" PRIu32,
            heap->runtimeInfoShmPtr.segId, heap->runtimeInfoShmPtr.offset);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    newCtx->perfStatShmPtr = heap->perfStatShmPtr;
    if (DbIsShmPtrValid(heap->perfStatShmPtr)) {
        newCtx->perfStat = DbShmPtrToAddr(heap->perfStatShmPtr);
        if (SECUREC_UNLIKELY(newCtx->perfStat == NULL)) {
            SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "init HeapRunCtx perfStat, segid:%" PRIu32 " offset:%" PRIu32,
                heap->perfStatShmPtr.segId, heap->perfStatShmPtr.offset);
            return MEMORY_OPERATE_FAILED_INTER;
        }
    }
    newCtx->heap = heap;
    newCtx->fsmMgr = &heap->fsm;
    newCtx->lockStatShmPtr = heap->lockStatShmPtr;
    newCtx->lockStat = NULL;
    newCtx->dmDetail.dmInfo = cfg->dmInfo;
    if (heap->constInfo.tupleType == HEAP_TUPLE_TYPE_VERTEX) {
        newCtx->heapAmForDmFun = &g_gmdbHeapAmForVertex;
    } else {
        newCtx->heapAmForDmFun = &g_gmdbHeapAmForKv;
    }
    newCtx->heapAmForDmFun->getDmDetailFun(newCtx, &newCtx->dmDetail);
    newCtx->heapShmAddr = heapJump == NULL ? cfg->heapShmAddr : heapJump->heapShmAddr;
    newCtx->heapJumpShmAddr = cfg->heapShmAddr;

    /* 需要通过reset来复用的(事务生命周期内的) */
    newCtx->hpOperation = HEAP_OPTYPE_MAX_TYPE;
    newCtx->cursorNum = 0;
    newCtx->heapTrxCtx = NULL;

    HeapInitNewRunCtxSetPageCache(newCtx);
    newCtx->staticPageInfo = (StaticPageInfoT){0};
    newCtx->pageSorter = EmptySePgLatchSorter();

    newCtx->rollBackInfo.oldTrxId = DB_INVALID_TRX_ID;
    newCtx->rollBackInfo.oldRollPtr = DB_INVALID_UINT64;
    newCtx->rollBackInfo.oldIsDeleted = false;
    newCtx->containerType = CONTAINER_HEAP;
    return STATUS_OK_INTER;
}

StatusInter HeapInitRunCtx(HeapRunCtxT *newCtx, const HeapRunCtxAllocCfgT *cfg, bool isTry)
{
    DB_POINTER2(newCtx, cfg);
    HeapJumpT *heapJump = (HeapJumpT *)DbShmPtrToAddr(cfg->heapShmAddr);
    if (SECUREC_UNLIKELY(heapJump == NULL)) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "heapJump inv in init, segid: %" PRIu32 " offset: %" PRIu32,
            cfg->heapShmAddr.segId, cfg->heapShmAddr.offset);
        return INVALID_PARAMETER_VALUE_INTER;
    }
    if (SECUREC_UNLIKELY(!heapJump->isInit)) {
        if (isTry) {
            return NO_DATA_HEAP_NOT_CREATED;  // 尝试初始化runctx，不触发heap创建
        } else {
            StatusInter ret = HeapLazyInit(heapJump);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
        }
    }
    HeapT *heap = NULL;
    StatusInter ret = HeapGetAndCheck(heapJump->heapShmAddr, &heap);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
#ifdef FEATURE_GQL
    TrxT *trx = (TrxT *)cfg->seRunCtx->trx;
    // 无行锁的悲观可重复读只能使用定长页
    if (trx->base.trxType == PESSIMISTIC_TRX && trx->trx.base.isolationLevel == REPEATABLE_READ &&
        trx->base.skipRowLockPessimisticRR && heap->constInfo.pageType != HEAP_FIX_LEN_ROW_PAGE) {
        SE_LAST_ERROR(INT_ERR_HEAP_INVALID_PARAMETER,
            "pessimistic repeatable read without row lock trx cannot op var page %" PRIu32, heap->magicNum);
        return INT_ERR_HEAP_INVALID_PARAMETER;
    }
#endif
    ret = HeapInitNewRunCtx(heap, newCtx, cfg, heapJump);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    HeapOpenInitHpControl(&newCtx->hpControl, newCtx, cfg, heap);
    return STATUS_OK_INTER;
}

StatusInter HeapInitRsmRunCtx(HeapRunCtxT *newCtx, const HeapRunCtxAllocCfgT *cfg)
{
    DB_POINTER2(newCtx, cfg);
    HeapT *heap = NULL;
    StatusInter ret = HeapGetAndCheck(cfg->heapShmAddr, &heap);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    ret = HeapInitNewRunCtx(heap, newCtx, cfg, NULL);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    HeapOpenInitHpControl(&newCtx->hpControl, newCtx, cfg, heap);
    return STATUS_OK_INTER;
}

static StatusInter HeapInitTrxCntrCtx(HeapRunCtxT *newCtx, TrxT *trx, bool *isFirstOpenHeap)
{
    DB_POINTER3(newCtx, trx, isFirstOpenHeap);
    SeTrxContainerCtxT *trxCntrCtx = NULL;
    HeapDmDetailT *dmDetail = HeapAmGetDmDetailInfo(newCtx);
    StatusInter ret = TrxStoreContainerCtx(trx, dmDetail->dmUuid, &trxCntrCtx, isFirstOpenHeap);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    newCtx->heapTrxCtx = &trxCntrCtx->heapTrxCtx;
    return STATUS_OK_INTER;
}

StatusInter HeapLabelInitRunHdl(HeapTrxCtxT *heapTrxCtx, const TrxT *trx, ShmemPtrT heapShmAddr, void *dmInfo)
{
    // 初始化事务容器上下文信息
    heapTrxCtx->ctxHead.type = TRX_HEAP_HANDLE;
    heapTrxCtx->heapShmAddr = heapShmAddr;
    TrxSetContainerIsUsed((SeTrxContainerCtxT *)(void *)heapTrxCtx);
    // HeapHandle
    heapTrxCtx->ctxHead.needOpenHeapHdl = false;
    if (heapTrxCtx->heapRunHdl != NULL) {
        // vertexLabel可能发生了变化，这里每次都刷新一下
        heapTrxCtx->heapRunHdl->dmDetail.dmInfo = dmInfo;
        heapTrxCtx->heapRunHdl->usrMemCtx = trx->trxPeriodMemCtx;
        return STATUS_OK_INTER;
    }
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.heapShmAddr = heapShmAddr,
        .seRunCtx = trx->seRunCtx,
        .dmInfo = dmInfo,
        .isBackGround = trx->base.isBackGround,
        .isUseRsm = ((DmMetaCommonT *)dmInfo)->isUseRsm};
    Status status = HeapLabelAllocAndInitRunctxImpl(&heapRunCtxAllocCfg, &heapTrxCtx->heapRunHdl);
    if (SECUREC_UNLIKELY(status != GMERR_OK)) {
        SE_ERROR(DbGetStatusInterErrno(status),
            "trxId:%" PRIu64 ", Slot(%" PRIu16 ") HeapOpen(%" PRIu32 ") alloc & init", trx->base.trxId,
            trx->base.trxSlot, heapTrxCtx->ctxHead.id);
        TrxSetContainerNotUsed((SeTrxContainerCtxT *)(void *)heapTrxCtx);
        return DbGetStatusInterErrno(status);
    }
    if (trx->base.isRecovery) {
        HeapLabelSetRsmRecoveryFlag(heapTrxCtx->heapRunHdl, true);
    }
    // 中间产生的内存, 可以由 事务周期内存 (提交或者回滚时)兜底清空
    heapTrxCtx->heapRunHdl->usrMemCtx = trx->trxPeriodMemCtx;
    return STATUS_OK_INTER;
}

StatusInter HeapOpenSaveTrxInfoAndAcquireLockImpl(HeapRunCtxT *ctx)
{
    SeRunCtxT *seRunCtx = ctx->seRunCtx;
    TrxT *trx = (TrxT *)seRunCtx->trx;
    if (trx->base.state == TRX_STATE_NOT_STARTED) {
        SE_LAST_ERROR(TRANS_MODE_MISMATCH_INTER, "inv trx(%" PRIu64 ") state %" PRIu32 ".", trx->base.trxId,
            (uint32_t)trx->base.state);
        return TRANS_MODE_MISMATCH_INTER;
    }
    bool isFirstOpenHeap;
    // 初始化事务容器上下文信息
    StatusInter ret = HeapInitTrxCntrCtx(ctx, trx, &isFirstOpenHeap);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    // 可能因为表降级的原因，labelId不会变（事务性容器会复用），但是vertexLabel发生了变化，这里每次都刷新一下
    ctx->heapTrxCtx->dmInfo = ctx->dmDetail.dmInfo;
    if (isFirstOpenHeap || ctx->heapTrxCtx->ctxHead.needOpenHeapHdl) {
        if (ctx->hpOperation != HEAP_OPTYPE_NONE_OP) {
            Status tmpRet = SeTransSetLabelModified(seRunCtx, ctx->heapCfg.labelId,
                (ctx->heapCfg.tupleType == HEAP_TUPLE_TYPE_VERTEX) ? TRX_VERTEXLABEL_HEAP : TRX_KVLABEL_HEAP,
                (ctx->hpOperation < HEAP_OPTYPE_MODIFY));
            if (tmpRet != GMERR_OK) {
                return DbGetStatusInterErrno(tmpRet);
            }
        }
        if (ctx->hpOperation == HEAP_OPTYPE_UNDO_PURGER) {
            ctx->heapTrxCtx->heapRunHdl = ctx;
        }
        ret = HeapLabelInitRunHdl(ctx->heapTrxCtx, trx, ctx->heapJumpShmAddr, ctx->heapTrxCtx->dmInfo);
#ifndef NDEBUG
        if (ret == STATUS_OK_INTER) {
            if (ctx->heap->constInfo.isUseRsm) {
                DbRsmKernelEntry(DB_RSM_KERNEL_HEAP_DML);
            }
        }
#endif
        return ret;
    } else {
        // EE层不会缓存一个事务内的多个RunCtx，因此会重复HeapOpen
        if (ctx->hpOperation != HEAP_OPTYPE_NONE_OP) {
            // 非none操作的情况需要check一下，之前这个表有没有被执行过none，
            // 如果有, isFirstOpenHeap就会是false，这里需要重新设置一下该表的冲突域
            Status tmpRet = SeTransCheckAndSetLabelModified(seRunCtx, ctx->heapCfg.labelId,
                (ctx->heapCfg.tupleType == HEAP_TUPLE_TYPE_VERTEX) ? TRX_VERTEXLABEL_HEAP : TRX_KVLABEL_HEAP,
                (ctx->hpOperation < HEAP_OPTYPE_MODIFY));
            return DbGetStatusInterErrno(tmpRet);
        }
#ifndef NDEBUG
        if (ctx->heap->constInfo.isUseRsm) {
            DbRsmKernelEntry(DB_RSM_KERNEL_HEAP_DML);
        }
#endif
    }

    return STATUS_OK_INTER;
}

bool HeapOpenCheckTrxInfoIsOkImpl(HeapRunCtxT *ctx, TrxT *trx)
{
    DB_POINTER2(ctx, trx);
    if (!HeapOpenCheckCCTypeIsOk(ctx, trx)) {
        return false;
    }
    if (trx->trx.base.isolationLevel == REPEATABLE_READ) {
        if (ctx->heapCfg.isolation != REPEATABLE_READ) {
            return false;
        }
        // RR支持与悲观/乐观搭配使用，对于purge线程来说，因为拿不到表信息且事务类型混用无风险，因此不校验
        return (ctx->heapCfg.trxType == trx->base.trxType || trx->base.isBackGround);
    } else if (trx->trx.base.isolationLevel == SERIALIZABLE) {
        if (ctx->hpControl.isLabelLockSerializable &&
            !SeIsLabelLocked(ctx->seRunCtx, ctx->heapCfg.labelId)) {  // 可串行化场景，检查表项是否加了事务锁
            SE_LAST_ERROR(INTERNAL_ERROR_INTER,
                "Label not locked in serializable, labelId: %" PRIu32 ", trxId:%" PRIu64, ctx->heapCfg.labelId,
                trx->base.trxId);
            return false;
        }
    }
    return true;
}

// 只用于事务容器的HeapOpen
StatusInter HeapOpenByCntrImpl(HeapRunCtxT *ctx, HpOpTypeE opType, SeTrxContainerCtxT *trxCntrCtx)
{
    DB_POINTER2(ctx, trxCntrCtx);
    DB_ASSERT(ctx->hpOperation == HEAP_OPTYPE_MAX_TYPE);  // 不支持重入
    HeapT *heap = (HeapT *)ctx->heap;
    if (SECUREC_UNLIKELY(HeapIsWrongMagicNum(heap))) {  // 检查该heap是否已被删除
        SE_LAST_ERROR(INT_ERR_HEAP_INVALID_PARAMETER, "heap %" PRIu32, heap->magicNum);
        return INT_ERR_HEAP_INVALID_PARAMETER;
    }
    ctx->hpOperation = opType;
    ctx->heapTrxCtx = &trxCntrCtx->heapTrxCtx;
    return STATUS_OK_INTER;
}

void HeapSetLastPageHeadInvalidImpl(HeapRunCtxT *ctx)
{
    DB_POINTER(ctx);
    ctx->pageCache.lastPageHeadPtr = NULL;
    ctx->pageCache.lastPageLogicPtr.pageId = 0;
    ctx->pageCache.lastPageLogicPtr.slotId = 0;
    ctx->pageCache.hcLastPageHeadPtr = NULL;
    ctx->pageCache.hcLastPageLogicPtr.pageId = 0;
    ctx->pageCache.hcLastPageLogicPtr.slotId = 0;
    ctx->pageCache.cacheVersion = DB_INVALID_UINT32;
}

void HeapReleaseRunctxImpl(HeapRunCtxT *ctx)
{
    if (SECUREC_UNLIKELY(ctx == NULL || ctx->seRunCtx == NULL || ctx->seRunCtx->sessionMemCtx == NULL)) {
        if (ctx == NULL) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "NULL handle in Heap release");
            DB_ASSERT(false);  // 不可重入函数，使用assert方便问题定位
            return;
        }
        if (ctx->seRunCtx == NULL) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "NULL seRunCtx in Heap release");
            DB_ASSERT(false);  // 不可重入函数，使用assert方便问题定位
            return;
        }
        if (ctx->seRunCtx->sessionMemCtx == NULL) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "NULL sessionMemCtx in Heap release");
            DB_ASSERT(false);  // 不可重入函数，使用assert方便问题定位
            return;
        }
    }
    // 内存释放前, 特意设置一些无效值, 用于野指针定位
    void *sessionMemCtx = ctx->seRunCtx->sessionMemCtx;
    ctx->seRunCtx = NULL;
    ctx->cursorNum = HEAP_INVALID_CURSOR_NUM;
    DbDynMemCtxFree(sessionMemCtx, ctx);
}

Status HeapResetOpTypeImpl(HeapRunCtxT *ctx, HpOpTypeE opType, bool isDefragmentationOrRecovery)
{
    DB_POINTER(ctx);
    bool ret = HeapIsWrongMagicNum(ctx->heap);
    DB_ASSERT(!ret);
    if (ctx->hpOperation == opType) {
        return GMERR_OK;
    }
    if (!isDefragmentationOrRecovery) {  // 非压缩线程
        DB_ASSERT(ctx->cursorNum == 0);  // 变更操作, 扫描句柄应该结束
    }

    DB_ASSERT(opType != HEAP_OPTYPE_NONE_OP);               // 不应该切换到none操作
    DB_ASSERT(opType != HEAP_OPTYPE_DIRECTREAD);            // 直连读不应该变更
    DB_ASSERT(ctx->hpOperation != HEAP_OPTYPE_DIRECTREAD);  // 直连读不应该变更
    if (ctx->hpOperation == HEAP_OPTYPE_NONE_OP) {
        // 只允许none操作可以升级到写操作
        Status tmpRet = SeTransCheckAndSetLabelModified(ctx->seRunCtx, ctx->heapCfg.labelId,
            (ctx->heapCfg.tupleType == HEAP_TUPLE_TYPE_VERTEX) ? TRX_VERTEXLABEL_HEAP : TRX_KVLABEL_HEAP,
            (opType < HEAP_OPTYPE_MODIFY));
        if (tmpRet != GMERR_OK) {
            return tmpRet;
        }
        ctx->hpOperation = opType;
        return GMERR_OK;
    }
    // 读操作升级到写操作不允许(可能会导致事务性容器中没有该表的信息)
    DB_ASSERT(!(ctx->hpOperation > HEAP_OPTYPE_MODIFY && opType < HEAP_OPTYPE_MODIFY));
    ctx->hpOperation = opType;
    return GMERR_OK;
}

void HeapCloseTrxCntrCtxImpl(HeapTrxCtxT *heapTrxCtx)
{
    // 事务性容器 Heap
    DB_POINTER(heapTrxCtx);
    heapTrxCtx->opStat = (TrxHeapStatT){
        .isBytesChange = false,
        .reserve = {0},
        .insertNum = 0,
        .deleteNum = 0,
        .writeBytes = 0,
        .deleteBytes = 0,
    };
    heapTrxCtx->ctxHead.isGetLabelXLock = false;
    if (TrxContainerIsOpen((SeTrxContainerCtxT *)heapTrxCtx)) {
        // 只有当前事务提交open过才能使用此处的addr，否则很有可能是HeapDrop的无效addr
        HeapRunCtxReset(heapTrxCtx->heapRunHdl);
    }
#ifndef NDEBUG
    // 有可能是heapOpen内存不足就回滚了，此时heapRunHdl为空，也不用退出内核态
    if (heapTrxCtx->heapRunHdl != NULL && heapTrxCtx->heapRunHdl->heap->constInfo.isUseRsm) {
        DbRsmKernelLeave(DB_RSM_KERNEL_HEAP_DML);
    }
#endif
}

inline static bool HeapLabelNotNeedTrxLock(const HeapT *heap)
{
    DB_POINTER(heap);
    // 轻量化事务下，不用加事务锁
    return DmIsLabelLiteTrx(heap->constInfo.ccType);
}

Status HeapLabelKeyXLockAcquireImpl(HpRunHdlT heapRunHdl, uint32_t labelId, const uint8_t *keyData, uint32_t keyLen)
{
    DB_POINTER2(heapRunHdl, keyData);
    DB_POINTER2(heapRunHdl->seRunCtx, heapRunHdl->heap);
    HeapT *heap = heapRunHdl->heap;
    if (HeapLabelNotNeedTrxLock(heap) || SeTransGetTrxType(heapRunHdl->seRunCtx) == OPTIMISTIC_TRX) {
        return GMERR_OK;
    }

    uint32_t hashCode = DbHash32(keyData, keyLen);
    // 理论上存在把某一key和某一行，或把多个key，映射成同一lockid的情况，但概率非常小，即使真的发生了，按正常冲突处理即可
    SeLockIdentifierT lockId = SeLockSetIdForKey(heapRunHdl->seRunCtx->instanceId, labelId, hashCode);
    StatusInter ret = SeLockMgrLockAcquire(heapRunHdl->seRunCtx, &lockId, SE_LOCK_MODE_X, false);
    HeapRunCtxUpdateLockStatImpl(heapRunHdl, &lockId, ret);
    return DbGetExternalErrno(ret);
}

#ifdef __cplusplus
}
#endif
