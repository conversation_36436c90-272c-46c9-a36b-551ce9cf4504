/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: Extension of heap operation to process slice row
 * Author: panghaisheng
 * Create: 2021-10-15
 */
#include "se_heap_slice_row.h"
#include "se_heap_addr.h"
#include "se_heap_trx_acc.h"
#include "se_heap_mem_inner.h"
#include "se_heap_page.h"
#include "se_log.h"

#include "se_heap_slice_row_common.h"
#include "se_heap_am_inner.h"

#ifdef __cplusplus
extern "C" {
#endif

void HeapAllocRowInfoInitForSliceLinkSrc(HpPageAllocRowInfoT *pageAllocRowInfo)
{
    DB_POINTER(pageAllocRowInfo);

    // 对于slice 的link src, bufSize是 0
    HeapAllocRowInfoInitHelper(pageAllocRowInfo, HEAP_VAR_ROW_LINK_SRC_ROW, 0, true);
}

void HeapAllocRowInfoInitForSliceDir(HpPageAllocRowInfoT *pageAllocRowInfo)
{
    DB_POINTER(pageAllocRowInfo);

    // 对于slice dir, buf是保存变长目录, 它的长度是 分片数 * 分片; sliceDirMemLen 在初始化的时候, 校验过,
    // 不会超过一个page
    HeapAllocRowInfoInitHelper(pageAllocRowInfo, HEAP_VAR_ROW_LINK_DST_SLICE_DIR_ROW,
        (PageSizeT)pageAllocRowInfo->sliceRowInfo.sliceDirMemLen, true);
}

static StatusInter HeapSliceDirRowInfoInit(HeapRunCtxT *ctx, HpPageSliceRowInfoT *sliceRowInfo, uint32_t bufSize)
{
    DB_POINTER2(ctx, sliceRowInfo);

    _Static_assert(SE_HEAP_INVALID_ADDR == 0,
        "Implicit dependency, the previous process memset to 0 indicates addr is invalid, so no reset is needed here");
    sliceRowInfo->isSliceRow = true;
    DB_ASSERT(ctx->heapCfg.maxRowRawSizeInPage != 0);
    sliceRowInfo->sliceRowNum = (bufSize + ctx->heapCfg.maxRowRawSizeInPage - 1) / ctx->heapCfg.maxRowRawSizeInPage;
    sliceRowInfo->curSliceRowIdx = 0;
    sliceRowInfo->sliceDirMemLen =
        (uint32_t)(sizeof(HpSliceRowDirT) + (sizeof(HpSliceRowDirEntryT) * sliceRowInfo->sliceRowNum));
    if (sliceRowInfo->sliceDirMemLen > ctx->heapCfg.maxRowRawSizeInPage) {
        SE_LAST_ERROR(PROGRAM_LIMIT_EXCEEDED_HEAP_MAX_ROW_SIZE, "sliceDirMemLen:%" PRIu32 " max:%" PRIu16,
            sliceRowInfo->sliceDirMemLen, ctx->heapCfg.maxRowRawSizeInPage);
        return PROGRAM_LIMIT_EXCEEDED_HEAP_MAX_ROW_SIZE;
    }
    // 申请大对象分片目录内存, 在HeapSliceRowInfoFreeDirMem中统一释放
    sliceRowInfo->sliceDir = DbDynMemCtxAlloc(ctx->seRunCtx->sessionMemCtx, sliceRowInfo->sliceDirMemLen);
    if (sliceRowInfo->sliceDir == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc sliceDir");
        return OUT_OF_MEMORY_INTER;
    }
    sliceRowInfo->sliceDir->prevItemPtr = SE_INVALID_HEAP_ITEM_POINTER;
    sliceRowInfo->sliceDir->nextItemPtr = SE_INVALID_HEAP_ITEM_POINTER;
    sliceRowInfo->sliceDir->totalSize = 0;  // 初始化为0, 目录行 更新的时候, 记得设置!
    sliceRowInfo->sliceDir->sliceRowNum = 0;  // 初始化为0, 表示后面都无效. 目录行 更新的时候, 记得设置!
    return STATUS_OK_INTER;
}

void HeapSliceRowInfoFreeDirMemImpl(HeapRunCtxT *ctx, HpPageSliceRowInfoT *sliceRowInfo)
{
    DB_POINTER2(ctx, sliceRowInfo);

    if (sliceRowInfo->sliceDir) {
        DbDynMemCtxFree(ctx->seRunCtx->sessionMemCtx, sliceRowInfo->sliceDir);
        sliceRowInfo->sliceDir = NULL;
    }
}

StatusInter HeapSliceAllocSubRows(
    HeapRunCtxT *ctx, const HpPageRowOpInfoT *sliceDirRowOpInfo, HpPageSliceRowInfoT *sliceRowInfo)
{
    DB_POINTER3(ctx, sliceDirRowOpInfo, sliceRowInfo);

    StatusInter ret;
    HpSliceRowDirEntryT *sliceRowEntries = sliceRowInfo->sliceDir->sliceRowEntries;
    uint32_t bufOffset = 0;
    PageSizeT sliceBufLen = (PageSizeT)ctx->heapCfg.maxRowRawSizeInPage;
    PageSizeT requireSize = (PageSizeT)ctx->fsmMgr->cfgInfo.availSize;
    HpPageAllocRowInfoT sliceSubRowInfo = EmptyHpPageAllocRowInfo();
    HpPageRowOpInfoT sliceSubRowOpInfo = InitHpPageRowOpInfo(NULL, &sliceSubRowInfo);
    DB_ASSERT(sliceBufLen < requireSize);
    bool isLastSliceRow = false;
    for (; sliceRowInfo->curSliceRowIdx < sliceRowInfo->sliceRowNum; sliceRowInfo->curSliceRowIdx++) {
        if (bufOffset + ctx->heapCfg.maxRowRawSizeInPage >= sliceDirRowOpInfo->allocRowInfo->bufSize) {
            DB_ASSERT(sliceDirRowOpInfo->allocRowInfo->bufSize > bufOffset);
            DB_ASSERT(sliceRowInfo->curSliceRowIdx == sliceRowInfo->sliceRowNum - 1);
            sliceBufLen = (PageSizeT)(sliceDirRowOpInfo->allocRowInfo->bufSize - bufOffset);
            isLastSliceRow = true;
        }
        ret = HeapInitAllocRowInfoImpl(
            ctx, sliceDirRowOpInfo->allocRowInfo->buf + bufOffset, sliceBufLen, &sliceSubRowInfo);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
            SE_ERROR(ret, "init sliceSubRow, labelId %" PRIu32, ctx->heapCfg.labelId);
            return ret;
        }
        sliceSubRowInfo.newTrxInfo = sliceDirRowOpInfo->allocRowInfo->newTrxInfo;
        HeapAllocRowInfoInitForSliceSub(ctx, &sliceSubRowInfo, isLastSliceRow);

        ret = HeapAllocVarRow(ctx, &sliceSubRowOpInfo);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
            SE_ERROR(ret, "alloc sliceSubRow, labelId %" PRIu32, ctx->heapCfg.labelId);
            return ret;
        }
        sliceRowEntries[sliceRowInfo->curSliceRowIdx].sliceSubRowPtr = sliceSubRowInfo.newRowInfo.itemPtr;
        // 释放当前页, 偏移到下一个分行的内容
        SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
        bufOffset += sliceBufLen;
    }
    sliceRowInfo->sliceDir->sliceRowNum = sliceRowInfo->sliceRowNum;  // 目录行 待更新.
    sliceRowInfo->sliceDir->totalSize = sliceDirRowOpInfo->allocRowInfo->bufSize;
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    return STATUS_OK_INTER;
}

StatusInter HeapSliceAllocDirAndSubRows(HeapRunCtxT *ctx, HpPageRowOpInfoT *sliceRowOpInfo)
{
    DB_POINTER2(ctx, sliceRowOpInfo);

    HpPageAllocRowInfoT *allocRowInfo = sliceRowOpInfo->allocRowInfo;
    HpPageSliceRowInfoT *sliceRowInfo = &allocRowInfo->sliceRowInfo;
    // 申请 linkDst 行, 用来保存分片目录
    HeapAllocRowInfoInitForSliceDir(allocRowInfo);
    StatusInter ret =
        HeapAllocVarRow(ctx, sliceRowOpInfo);  // 此时, 并没有各个分片的信息在 目录行中(空间足够, 但是分片数是0)
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "alloc sliceDirRow, labelId %" PRIu32, ctx->heapCfg.labelId);
        return ret;
    }
    sliceRowInfo->sliceDirRowPtr = allocRowInfo->newRowInfo.itemPtr;
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);

    // 申请分行, 并记录; 前面的分行, 按照每个页面最大的 size 进行申请. 最后一个按需申请
    ret = HeapSliceAllocSubRows(ctx, sliceRowOpInfo, sliceRowInfo);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "insert sliceSubRow, labelId %" PRIu32 "", ctx->heapCfg.labelId);
        return ret;
    }
    // 重新进入 目录行的page, 将各个新申请的分行addr, 更新到 目录行上.
    ret = HeapSliceUpdateDirRow(ctx, sliceRowOpInfo);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "sliceSub insert update sliceDirRow, labelId %" PRIu32, ctx->heapCfg.labelId);
    }
    return ret;
}

StatusInter HeapSliceDirBindLinkSrc(
    HeapRunCtxT *ctx, HeapRowInfoT *linSrcRowInfo, const HpPageRowOpInfoT *sliceRowOpInfo, bool isUpdate)
{
    DB_POINTER3(ctx, linSrcRowInfo, sliceRowOpInfo);

    StatusInter ret = HeapIsFreeLatchUnderLabel(ctx) ? HeapLabelLatchCheckPage(ctx, linSrcRowInfo) :
                                                       HeapPageAddLatch(ctx, linSrcRowInfo);
    if (ret != STATUS_OK_INTER) {  // 没有加成功. 不用释放
        return ret;
    }

    // 重新获取一下 linkSrc 的行头, 更新跳转信息
    linSrcRowInfo->rowHeadPtr.rowState =
        HeapVarPageGetRowStateImpl(linSrcRowInfo->pageHeadPtr.varPageHead, linSrcRowInfo->slot);
    // pageHead的freeSize不变，若为更新场景则保留行预留空间
    HeapVarPageInitLinkSrcRowImpl(ctx, linSrcRowInfo->rowHeadPtr.linkRowHead, sliceRowOpInfo->allocRowInfo, isUpdate);
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    return STATUS_OK_INTER;
}

/* 失败情况下, 手动删除一下各个分片 */
void HeapClearSliceRow4Failed(HeapRunCtxT *ctx, HpPageRowOpInfoT *sliceDirRowOpInfo, bool isUpdateFlow)
{
    DB_POINTER2(ctx, sliceDirRowOpInfo);

    StatusInter ret;
    HpPageSliceRowInfoT *sliceRowInfo = &sliceDirRowOpInfo->allocRowInfo->sliceRowInfo;

    if (sliceRowInfo->sliceDir) {
        for (uint32_t i = 0; i < sliceRowInfo->curSliceRowIdx; i++) {
            ret = HeapDeleteInnerRowImpl(ctx, sliceRowInfo->sliceDir->sliceRowEntries[i].sliceSubRowPtr, NULL);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "clear slice:%" PRIu32 ", labelId %" PRIu32, i, ctx->heapCfg.labelId);
                DB_ASSERT(false);  // debug版本看看, 正常情况不应该出现
            }
        }
        // 最后释放临时申请的内存
        HeapSliceRowInfoFreeDirMemImpl(ctx, sliceRowInfo);
        // 内存故障的重入场景, 更加容易发现问题. 其实这里不设置也可以
        sliceRowInfo->curSliceRowIdx = HEAP_INVALID_MAGIC_NUM;
    }

    if (HeapIsValidAddr(sliceRowInfo->sliceDirRowPtr)) {
        ret = HeapDeleteInnerRowImpl(ctx, sliceRowInfo->sliceDirRowPtr, NULL);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "clear sliceDir, labelId %" PRIu32, ctx->heapCfg.labelId);
            DB_ASSERT(false);  // debug版本看看, 正常情况不应该出现
        }
        HeapSetInvalidAddr(&sliceRowInfo->sliceDirRowPtr);  // 置空, 避免重复释放 & 清理
    }

    if (isUpdateFlow) {
        return;  // 更新场景, 原来的行(linkSrc)的行, 不清理. 有undo负责恢复
    }

    // 插入场景, 由heap清理新增的linkSrc行
    if (HeapIsValidAddr(sliceRowInfo->linkSrcRowPtr)) {
        ret = HeapDeleteInnerRowImpl(ctx, sliceRowInfo->linkSrcRowPtr, NULL);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "clear sliceDir, labelId %" PRIu32, ctx->heapCfg.labelId);
            DB_ASSERT(false);  // debug版本看看, 正常情况不应该出现
        }
        HeapSetInvalidAddr(&sliceRowInfo->linkSrcRowPtr);  // 置空, 避免重复释放 & 清理
    }
}

StatusInter HeapInsertSliceRows(HeapRunCtxT *ctx, HpPageRowOpInfoT *sliceRowOpInfo)
{
    DB_POINTER2(ctx, sliceRowOpInfo);
    HpPageAllocRowInfoT *allocRowInfo = sliceRowOpInfo->allocRowInfo;
    HpPageSliceRowInfoT *sliceRowInfo = &allocRowInfo->sliceRowInfo;

    StatusInter ret = HeapSliceDirRowInfoInit(ctx, sliceRowInfo, allocRowInfo->bufSize);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    // 先申请一个 linkSrc 行.
    HeapAllocRowInfoInitForSliceLinkSrc(allocRowInfo);
    ret = HeapAllocVarRow(ctx, sliceRowOpInfo);  // 此时, 并没有各个分片的信息在 目录行中.
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "alloc sliceDirRow, labelId %" PRIu32 "", ctx->heapCfg.labelId);
        return ret;
    }
    sliceRowInfo->linkSrcRowPtr = allocRowInfo->newRowInfo.itemPtr;  // 设置 linkSrc 信息到 slice上下文
    HeapRowInfoT linSrcRowInfo = allocRowInfo->newRowInfo;  // 完全拷贝 linkSrc 的信息, 用于后面申请到 linkDst 后更新.

    // 申请 linkDst 和 分片
    ret = HeapSliceAllocDirAndSubRows(ctx, sliceRowOpInfo);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "alloc sliceDirRow & subRows, labelId %" PRIu32 "", ctx->heapCfg.labelId);
        return ret;
    }
    ret = HeapSliceDirBindLinkSrc(ctx, &linSrcRowInfo, sliceRowOpInfo, false);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "bind sliceDir 2 linkSrcRow, labelId %" PRIu32 "", ctx->heapCfg.labelId);
        return ret;
    }
    // 此时的 newRowInfo 指向的是 linkDts, 由于外部都是用 linkSrc的, 所以要还原一下.
    allocRowInfo->newRowInfo = linSrcRowInfo;
    return STATUS_OK_INTER;
}

StatusInter HeapUpdateSliceRows(HeapRunCtxT *ctx, HpPageRowOpInfoT *sliceRowOpInfo)
{
    DB_POINTER2(ctx, sliceRowOpInfo);
    /* 流程 + 失败回滚分析:
     * 1. 申请linkDst (指向linkSrc, 但是linkSrc未关联linkDst).
     * 2. 申请分行.
     * 3. 把分行addr, 更新到 linkDst(sliceDir)上.
     * 失败回滚分析A: 前面流程失败, 直接 删除各个分片 + linkDst(目录行) 即可
     * 4. 修改linkSrc, 使其指向 linkDst(目录行)
     * 失败回滚分析B: 如果修改成功, 则由undo恢复; 如果没有修改成功, 则不影响原来的 tuple. (复用失败回滚分析A)
     */
    HpPageAllocRowInfoT *allocRowInfo = sliceRowOpInfo->allocRowInfo;
    HeapRowInfoT *linSrcRowInfo = &sliceRowOpInfo->fetchRowInfo->srcRowInfo;
    HpPageSliceRowInfoT *sliceRowInfo = &allocRowInfo->sliceRowInfo;

    StatusInter ret = HeapSliceDirRowInfoInit(ctx, sliceRowInfo, allocRowInfo->bufSize);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    sliceRowInfo->linkSrcRowPtr = linSrcRowInfo->itemPtr;  // 设置 linkSrc 信息到 slice上下文
    // 申请 linkDst 和 分片
    ret = HeapSliceAllocDirAndSubRows(ctx, sliceRowOpInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    ret = HeapSliceDirBindLinkSrc(ctx, linSrcRowInfo, sliceRowOpInfo, true);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    HeapSliceRowInfoFreeDirMemImpl(ctx, sliceRowInfo);
    return ret;
}

StatusInter HeapUpdateAllocSliceRows(HeapRunCtxT *ctx, HpPageRowOpInfoT *sliceRowOpInfo)
{
    // 该函数只在乐观下使用，用于提前申请出分片行，避免触发relatch造成乐观的并发问题
    DB_POINTER2(ctx, sliceRowOpInfo);
    HpPageAllocRowInfoT *allocRowInfo = sliceRowOpInfo->allocRowInfo;
    HpPageSliceRowInfoT *sliceRowInfo = &allocRowInfo->sliceRowInfo;
    HeapRowInfoT *linSrcRowInfo = &sliceRowOpInfo->fetchRowInfo->srcRowInfo;

    StatusInter ret = HeapSliceDirRowInfoInit(ctx, sliceRowInfo, allocRowInfo->bufSize);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    sliceRowInfo->linkSrcRowPtr = linSrcRowInfo->itemPtr;  // 设置 linkSrc 信息到 slice上下文
    // 申请 linkDst 和 分片
    return HeapSliceAllocDirAndSubRows(ctx, sliceRowOpInfo);
}

StatusInter HeapFetchSliceSubRows(
    HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchDirRowInfo, HpPageSliceRowCursorT *sliceRowCursor)
{
    DB_POINTER3(ctx, fetchDirRowInfo, sliceRowCursor);

    StatusInter ret;
    sliceRowCursor->isSubRowSearchFailed = false;
    HpSliceRowDirT *sliceDir = sliceRowCursor->sliceDir;
    uint32_t bufOffset = 0;
    HpPageFetchRowInfo fetchSubRowInfo = EmptyHpPageFetchRowInfo();
    HpPageRowOpInfoT subRowOpInfo = InitHpPageRowOpInfo(&fetchSubRowInfo, NULL);
    fetchSubRowInfo.canAccessSubRow = true;
    fetchSubRowInfo.curRowInfo = &fetchSubRowInfo.srcRowInfo;
    HeapRowInfoT *curSubRowInfo = fetchSubRowInfo.curRowInfo;
    for (uint32_t i = 0; i < sliceDir->sliceRowNum; i++) {
        fetchSubRowInfo.srcRowInfo.itemPtr = sliceDir->sliceRowEntries[i].sliceSubRowPtr;
        ret = HeapFetchOneSliceSubRow(ctx, &subRowOpInfo);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            sliceRowCursor->isSubRowSearchFailed = true;
            return ret;
        }
        if (!fetchSubRowInfo.isGetBuf || curSubRowInfo->rowType != HEAP_VAR_ROW_SLICE_SUB_ROW ||
            !HeapRowIsSameTrxId(curSubRowInfo->rowHeadPtr.sliceSubRowHead->trxId, fetchDirRowInfo->rowTrxInfo.trxId)) {
            sliceRowCursor->isSubRowSearchFailed = true;
            return NO_DATA_HEAP_ITEM_NOT_EXIST;
        }
        errno_t err = memcpy_s(fetchDirRowInfo->lobBuf + bufOffset, sliceDir->totalSize - bufOffset,
            fetchSubRowInfo.buf, fetchSubRowInfo.bufSize);
        if (err != EOK) {
            SE_LAST_ERROR(INT_ERR_HEAP_UNEXPECT_ERROR, "cpy, labelId:%" PRIu32, ctx->heapCfg.labelId);
            return INT_ERR_HEAP_UNEXPECT_ERROR;
        }
        bufOffset += fetchSubRowInfo.bufSize;
        // 当前 页的 sub row 拷贝完后, 先解锁了 (由于 pageSorter 里面暂时不支持太多的 page)
        SePgLatchSorterRemovePage(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, curSubRowInfo->itemPtr.pageId);
    }

    // 全部拷贝完后, 检查一下大小, 是否完整
    if (bufOffset != sliceDir->totalSize) {
        SE_LAST_ERROR(INT_ERR_HEAP_UNEXPECT_ERROR, "inconsist sliceRow, labelId:%" PRIu32, ctx->heapCfg.labelId);
        return INT_ERR_HEAP_UNEXPECT_ERROR;
    }
    fetchDirRowInfo->isGetBuf = true;
    fetchDirRowInfo->buf = fetchDirRowInfo->lobBuf;
    return STATUS_OK_INTER;
}

StatusInter HeapFetchSliceRowsImpl(HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchDirRowInfo)
{
    DB_POINTER2(ctx, fetchDirRowInfo);

    StatusInter ret;
    HeapRowInfoT *curDirRowInfo = fetchDirRowInfo->curRowInfo;
    HpPageSliceRowCursorT *sliceRowCursor = &fetchDirRowInfo->sliceRowCursor;
    sliceRowCursor->isSubRowSearchFailed = false;
    // 申请内存用于临时存储分片目录, 调用HeapFetchClearSliceDir释放
    sliceRowCursor->sliceDir = DbDynMemCtxAlloc(ctx->seRunCtx->sessionMemCtx, curDirRowInfo->rawRowSize);
    if (sliceRowCursor->sliceDir == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc sliceDir, labelId:%" PRIu32, ctx->heapCfg.labelId);
        ret = OUT_OF_MEMORY_INTER;
        goto FAILED_CLEAR;
    }

    // 保存分片目录
    HpLinkRowHeadT *sliceDirRowHead = curDirRowInfo->rowHeadPtr.sliceDirRowHead;
    HpSliceRowDirT *sliceDir = (HpSliceRowDirT *)(sliceDirRowHead + 1);
    errno_t err = memcpy_s(sliceRowCursor->sliceDir, curDirRowInfo->rawRowSize, sliceDirRowHead + 1,
        sizeof(HpSliceRowDirT) + (sliceDir->sliceRowNum * sizeof(HpSliceRowDirEntryT)));
    if (err != EOK) {
        ret = INT_ERR_HEAP_UNEXPECT_ERROR;
        SE_ERROR(ret, "inconsist sliceDirRow, labelId:%" PRIu32, ctx->heapCfg.labelId);
        goto FAILED_CLEAR;
    }
    sliceDir = sliceRowCursor->sliceDir;  // 后续就使用临时内存的 目录信息, 不再使用page.
    fetchDirRowInfo->bufSize = sliceDir->totalSize;
    // 申请一块内存用于临时拷贝大对象数据, 在HeapFetchClearTempSliceMem释放
    fetchDirRowInfo->lobBuf = DbDynMemCtxAlloc(ctx->usrMemCtx, sliceDir->totalSize);  // noteb: 这里拷贝性能不好.
    if (fetchDirRowInfo->lobBuf == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "lob alloc buf, labelId:%" PRIu32, ctx->heapCfg.labelId);
        ret = OUT_OF_MEMORY_INTER;
        goto FAILED_CLEAR;
    }

    // 释放掉 linkSrc 和 linkDst!!! 后续的流程注意, 不能再直接使用页上的addr
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    ret = HeapFetchSliceSubRows(ctx, fetchDirRowInfo, sliceRowCursor);
FAILED_CLEAR:
    if (ret != STATUS_OK_INTER) {
        HeapFetchClearTempSliceMem(ctx, fetchDirRowInfo);
    }
    return ret;
}

StatusInter HeapUpdateCommitOrRollbackCleanForLobImpl(HeapRunCtxT *ctx, HpItemPointerT itemPtr)
{
    DB_UNUSED(ctx);
    DB_UNUSED(itemPtr);
    return STATUS_OK_INTER;
}

StatusInter HeapUpdateCleanForLob(HeapRunCtxT *ctx, HpItemPointerT firstItemPtr, HpItemPointerT lastItemPtr)
{
    DB_UNUSED(ctx);
    DB_UNUSED(firstItemPtr);
    DB_UNUSED(lastItemPtr);
    return STATUS_OK_INTER;
}

StatusInter HeapUpdatePartialFetchSliceRowBuf(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    DB_ASSERT(fetchRowInfo->isGetBuf);
    DB_ASSERT(fetchRowInfo->buf == NULL);

    // 计算部分更新的内容所在的分片, 此时dstRowInfo保存的是sliceDir行的信息
    HpLinkRowHeadT *dirRowHead = fetchRowInfo->dstRowInfo.rowHeadPtr.sliceDirRowHead;
    HpSliceRowDirT *sliceDir = (HpSliceRowDirT *)(dirRowHead + 1);
    uint32_t targetSliceRowIndex = opInfo->allocRowInfo->offsetOfRawData / ctx->heapCfg.maxRowRawSizeInPage;
    // 目前仅支持系统字段，一定在首分片，使用DB_ASSERT维护
    DB_ASSERT(targetSliceRowIndex == 0);

    HpPageFetchRowInfo fetchSubRowInfo = EmptyHpPageFetchRowInfo();
    HpPageRowOpInfoT subRowOpInfo = InitHpPageRowOpInfo(&fetchSubRowInfo, NULL);
    fetchSubRowInfo.canAccessSubRow = true;
    fetchSubRowInfo.curRowInfo = &fetchSubRowInfo.srcRowInfo;
    fetchSubRowInfo.srcRowInfo.itemPtr = sliceDir->sliceRowEntries[targetSliceRowIndex].sliceSubRowPtr;
    StatusInter ret = HeapFetchOneSliceSubRow(ctx, &subRowOpInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    if (!fetchSubRowInfo.isGetBuf || fetchSubRowInfo.curRowInfo->rowType != HEAP_VAR_ROW_SLICE_SUB_ROW ||
        !HeapRowIsSameTrxId(
            fetchSubRowInfo.curRowInfo->rowHeadPtr.sliceSubRowHead->trxId, opInfo->fetchRowInfo->rowTrxInfo.trxId)) {
        return NO_DATA_HEAP_ITEM_NOT_EXIST;
    }

    // 跨页访问, 可能有reLatch.
    // relatch过程中，可能会有页面整理之类的情况发生，src行、SliceDir行在页内的offset会变化
    // 重新读取src行信息和sliceDir行信息
    bool isSrcRowReLatch = SePgLatchSorterIsPageReLatch(&ctx->pageSorter, fetchRowInfo->srcRowInfo.itemPtr.pageId);
    if (SECUREC_UNLIKELY(isSrcRowReLatch)) {
        SePgLatchSorterClearPageReLatchState(&ctx->pageSorter, fetchRowInfo->srcRowInfo.itemPtr.pageId);
        fetchRowInfo->curRowInfo = &fetchRowInfo->srcRowInfo;
        ret = HeapFetchGetPage(ctx, opInfo->fetchRowInfo);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
        ret = HeapVarRowFetch(ctx, opInfo);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Re-acquire src & dir row. pageId:%" PRIu32 ", slotId:%" PRIu32,
                fetchRowInfo->curRowInfo->itemPtr.pageId, fetchRowInfo->curRowInfo->itemPtr.slotId);
            return ret;
        }
    }
    fetchRowInfo->buf = fetchSubRowInfo.buf;
    return ret;
}

static StatusInter HeapUpdatePartialSliceSubRowsHelper(
    HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, HpPageSliceRowCursorT *sliceRowCursor, uint32_t targetSliceRowIndex)
{
    StatusInter ret;
    HpPageAllocRowInfoT *allocRowInfo = opInfo->allocRowInfo;
    HpSliceRowDirT *sliceDir = sliceRowCursor->sliceDir;
    HpPageFetchRowInfo fetchSubRowInfo = EmptyHpPageFetchRowInfo();
    HpPageRowOpInfoT subRowOpInfo = InitHpPageRowOpInfo(&fetchSubRowInfo, NULL);
    fetchSubRowInfo.canAccessSubRow = true;
    fetchSubRowInfo.curRowInfo = &fetchSubRowInfo.srcRowInfo;
    HeapRowInfoT *curSubRowInfo = fetchSubRowInfo.curRowInfo;
    // 更新过程中，可能会有其他事务读，此时由于事务信息不一致，会读取失败
    // 由于目前部分更新只用于标记删除，标记删除过程中，其他事务无法读取该行符合逻辑
    for (uint32_t i = 0; i < sliceDir->sliceRowNum; i++) {
        fetchSubRowInfo.srcRowInfo.itemPtr = sliceDir->sliceRowEntries[i].sliceSubRowPtr;
        ret = HeapFetchOneSliceSubRow(ctx, &subRowOpInfo);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
        if (!fetchSubRowInfo.isGetBuf || curSubRowInfo->rowType != HEAP_VAR_ROW_SLICE_SUB_ROW ||
            !HeapRowIsSameTrxId(
                curSubRowInfo->rowHeadPtr.sliceSubRowHead->trxId, opInfo->fetchRowInfo->rowTrxInfo.trxId)) {
            return NO_DATA_HEAP_ITEM_NOT_EXIST;
        }
        HpSliceSubRowHeadT *rowHead = curSubRowInfo->rowHeadPtr.sliceSubRowHead;
        if (i == targetSliceRowIndex) {
            errno_t err = memcpy_s(((uint8_t *)(rowHead + 1) + allocRowInfo->offsetOfRawData), allocRowInfo->bufSize,
                allocRowInfo->buf, allocRowInfo->bufSize);
            if (err != EOK) {
                return MEMORY_OPERATE_FAILED_INTER;
            }
        }
        // 更新事务信息
        rowHead->trxId = allocRowInfo->newTrxInfo.trxId;
        // 释放该分片行页的page latch (pageSorter 暂时不支持太多的 page)
        SePgLatchSorterRemovePage(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, curSubRowInfo->itemPtr.pageId);
    }
    return STATUS_OK_INTER;
}

StatusInter HeapUpdatePartialSliceSubRows(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    HpPageAllocRowInfoT *allocRowInfo = opInfo->allocRowInfo;
    HeapRowInfoT *srcRowInfo = &fetchRowInfo->srcRowInfo;
    HeapRowInfoT *dstRowInfo = &fetchRowInfo->dstRowInfo;

    DB_ASSERT(srcRowInfo->rowHeadPtr.rowState->isLinkSrc);
    DB_ASSERT(dstRowInfo->rowHeadPtr.rowState->isSliceHead);

    StatusInter ret = STATUS_OK_INTER;

    // 后续有可能relatch, src行和dst行在页内的位置可能会变化，先修改事务相关信息
    srcRowInfo->rowHeadPtr.linkRowHead->srcTrxInfo = allocRowInfo->newTrxInfo;
    dstRowInfo->rowHeadPtr.linkRowHead->dstInfo.trxId = allocRowInfo->newTrxInfo.trxId;

    // 目前部分更新只考虑系统字段，所以要更新的内容只会在第一个分片的前面，不涉及更新内容跨分片的场景
    // 因此 offsetOfRawData 也是针对第一个分片的偏移, 使用DB_ASSERT维护
    uint32_t targetSliceRowIndex = opInfo->allocRowInfo->offsetOfRawData / ctx->heapCfg.maxRowRawSizeInPage;
    DB_ASSERT(targetSliceRowIndex == 0);

    // 申请内存用于临时存储分片目录, 调用HeapFetchClearSliceDir释放
    HpPageSliceRowCursorT *sliceRowCursor = &fetchRowInfo->sliceRowCursor;
    sliceRowCursor->sliceDir = DbDynMemCtxAlloc(ctx->seRunCtx->sessionMemCtx, dstRowInfo->rawRowSize);
    if (sliceRowCursor->sliceDir == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc sliceDir, labelId:%" PRIu32, ctx->heapCfg.labelId);
        ret = OUT_OF_MEMORY_INTER;
        goto FAILED_CLEAR;
    }

    HpLinkRowHeadT *sliceDirRowHead = dstRowInfo->rowHeadPtr.sliceDirRowHead;
    HpSliceRowDirT *sliceDir = (HpSliceRowDirT *)(sliceDirRowHead + 1);
    errno_t err = memcpy_s(sliceRowCursor->sliceDir, dstRowInfo->rawRowSize, sliceDirRowHead + 1,
        sizeof(HpSliceRowDirT) + (sliceDir->sliceRowNum * sizeof(HpSliceRowDirEntryT)));
    if (err != EOK) {
        SE_LAST_ERROR(INT_ERR_HEAP_UNEXPECT_ERROR, "backup sliceDirRow, labelId:%" PRIu32, ctx->heapCfg.labelId);
        goto FAILED_CLEAR;
    }
    // 下面流程可能有relatch, 不能再直接使用页上的addr
    // 后续就使用临时内存中的目录信息, 不再使用dst行中的信息
    ret = HeapUpdatePartialSliceSubRowsHelper(ctx, opInfo, sliceRowCursor, targetSliceRowIndex);
FAILED_CLEAR:
    HeapFetchClearSliceDir(ctx, fetchRowInfo);
    return ret;
}

#ifdef __cplusplus
}
#endif
