/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 看护持久化页面关键结构体，page
 * Author: yang<PERSON><PERSON><PERSON>
 * Create: 2024-9-24
 */

#ifndef SE_PAGE_PERSIST_UPGRADE_H
#define SE_PAGE_PERSIST_UPGRADE_H

#include "db_rwlatch.h"
#include "se_common.h"

#ifdef __cplusplus
extern "C" {
#endif

#define DB_PAGE_HEAD_RESERVED_SIZE 3
// 持久化关键结构体对齐： (不区分64/32位平台)强制8字节对齐
#pragma pack(8)

typedef enum PersistencePageTypeE {
    // 首次申请初始化
    PERSISTENCE_PAGE_TYPE_NOT_USED = 0,  // PageHeadT
    // 被释放之后
    PERSISTENCE_PAGE_TYPE_FREE,  // PageHeadT

    // heap 管理页
    PERSISTENCE_PAGE_TYPE_HEAP_META,  // PageHeadT + HeapT
    // heap定长，变长
    PERSISTENCE_PAGE_TYPE_HEAP_FIXED_ROW,  // HFPageHeadT(FsmDataPageHeadT-PageHeadT)
    PERSISTENCE_PAGE_TYPE_HEAP_VAR_ROW,    // HVPageHeadT(FsmDataPageHeadT-PageHeadT)

    // fixed heap
    PERSISTENCE_PAGE_TYPE_FIXED_META,  // PageHeadT + FixedHeapT
    // fixed heap 数据页
    PERSISTENCE_PAGE_TYPE_FIXED_HEAP_DATA,  // FixedHeapPageHeadT(FsmDataPageHeadT-PageHeadT)

    // fsm页
    PERSISTENCE_PAGE_TYPE_FSM,  // FsmPageHeadT(PageHeadT)
    // 页表的页。一级页表，二级页表
    PERSISTENCE_PAGE_TYPE_FSM_PAGE_TABLE,  // FsmPageTableT(PageHeadT)

    // space 管理页
    PERSISTENCE_PAGE_TYPE_SPACE_MANAGER,  // PageHeadT + SpaceHeadT

    // undo:Rseg, undo seg, undo页
    PERSISTENCE_PAGE_TYPE_UNDO_RSEG,  // RSegPageHeaderT(PageHeadT)
    PERSISTENCE_PAGE_TYPE_UNDO_SEG,   // UndoPageHeaderT(PageHeadT)
    PERSISTENCE_PAGE_TYPE_UNDO,       // seg扩展页，UndoPageHeaderT(PageHeadT)

    // btree:管理页，非管理页，大key溢出页
    PERSISTENCE_PAGE_TYPE_BTREE_META,     // PageHeadT + BTreeT
    PERSISTENCE_PAGE_TYPE_BTREE_NODE,     // PageHeadT + BTreeNodeHdrT
    PERSISTENCE_PAGE_TYPE_BTREE_BIG_KEY,  // PageHeadT + raw data

    PERSISTENCE_PAGE_TYPE_DISKANN_META,
    PERSISTENCE_PAGE_TYPE_LPASMEM_META,

    PERSISTENCE_PAGE_TYPE_CORE_CTRL,
    PERSISTENCE_PAGE_TYPE_SPACE_CTRL,
    PERSISTENCE_PAGE_TYPE_FILE_CTRL,
    PERSISTENCE_PAGE_TYPE_DEVICE_CTRL,
    PERSISTENCE_PAGE_TYPE_EXTEND_CTRL,
    PERSISTENCE_PAGE_TYPE_NUM,
} PersistencePageTypeE;

// PageHeadT pageType当前用6bit表示UndoTrxResTypeE，如果超出范围需要重新审视
static_assert(PERSISTENCE_PAGE_TYPE_NUM <= (1 << 6), "persistence page type should be represented in 6 bits");

typedef struct TagPageHeadT {
    DbLatchT lock;  // 加锁控制页内数据的读写并发访问
    uint16_t checkSum;
    uint16_t beginPos;
    uint16_t endPos;    // 终端页尾预留32字节；非终端开启防篡改时，页尾记录32字节页摘要
    uint16_t freeSize;  // 包含删除的行的空间，可以用来加速垃圾清理
    uint32_t trmId;     // 页所属的容器Id,对应容器内的fileId语义
    PageIdT addr;
    PageIdT nextPageId;     // 为持久化预留
    uint64_t lsn;           // 为持久化预留
    uint32_t entryUsedNum;  // for primary index: the entry number that has been occupied
    // pageState: memdata分配给上层后，有可能上层还未初始化，就抢先被扫描线程加上页锁，从而访问到未初始化的页;
    // PAGE_UNINIT--未初始化，PAGE_USING--上层已初始化，加页latch时，pageState必须为PAGE_USING
    uint8_t pageState : 1;
    uint8_t isRsmUsing : 1;  // 保留内存使用，恢复时用于判断该页是否可以被回收
    uint8_t pageType : 6;
#ifdef FEATURE_PERSISTENCE
    // pageState 被压缩的页此变量会被打上True
    bool isCompressed;
    uint16_t pageCompressedSize;  // 页压缩后的大小
    uint32_t crcCheckSum;
    uint8_t isEncrypted;  // 是否加密标志，默认值为0，如果非0，则需要从页未取出hmac和iv进行解密
    uint8_t reserve[DB_PAGE_HEAD_RESERVED_SIZE];
#ifndef NDEBUG
    PageIdT permanentAddr;
#endif
#else
    uint8_t reserve[DB_PAGE_HEAD_RESERVED_SIZE];
#endif
} PageHeadT;
#pragma pack()

static inline void SePageHeadSetType(PageHeadT *pageHead, PersistencePageTypeE pageType)
{
    pageHead->pageType = pageType;
}

#ifdef __cplusplus
}
#endif

#endif
