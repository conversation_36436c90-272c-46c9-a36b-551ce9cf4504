/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: buffer pool
 * Author:
 * Create: 2023-2023
 * Notes:
 */

#include "se_buffer_pool_table_recycle.h"
#include "se_buffer_pool_inner.h"

#define TABLE_ID_STR_LEN 11

typedef struct BufGetDescByTableArgs {
    bool needChkTimeout;
    bool reTry;
} BufGetDescByTableArgsT;

uint32_t BufTableMapGetTableIdHash(uint32_t tableId)
{
    char tableStr[TABLE_ID_STR_LEN] = {0};
    // UINT32_MAX is 4294967295, request size less than TABLE_ID_STR_LEN
    (void)sprintf_s(tableStr, TABLE_ID_STR_LEN, "%" PRIu32, tableId);
    return DbStrToHash32(tableStr);
}

uint32_t DbOamapKeyCmpForTable(const void *key1, const void *key2)
{
    DB_POINTER2(key1, key2);
    return ((uintptr_t)key1 == (uintptr_t)key2);
}

uint32_t BufTableMapGetPageCount(void *ctx, void *tableMap, uint32_t tableId)
{
    DB_UNUSED(ctx);
    uint32_t hash = BufTableMapGetTableIdHash(tableId);
    uint8_t *key = (uint8_t *)(uintptr_t)tableId;
    return (uint32_t)(uintptr_t)DbOamapLookup((DbOamapT *)tableMap, hash, key, NULL);
}

StatusInter BufTableMapIncPageCount(void *ctx, void *tableMapPtr, uint32_t tableId)
{
    DB_UNUSED(ctx);
    uint32_t hash = BufTableMapGetTableIdHash(tableId);
    uint8_t *key = (uint8_t *)(uintptr_t)tableId;
    uint32_t pageCount = TABLE_PAGES_MIN;
    DbOamapT *tableMap = (DbOamapT *)tableMapPtr;
    void *value = DbOamapRemove(tableMap, hash, key);
    if (value != NULL) {
        pageCount += (uint32_t)(uintptr_t)value;
    }
    Status res = DbOamapInsert(tableMap, hash, key, (uint8_t *)(uintptr_t)pageCount, NULL);
    if (res != GMERR_OK) {
        return OUT_OF_MEMORY_INTER;
    }
    return STATUS_OK_INTER;
}

void BufTableMapDecPageCount(void *ctx, void *tableMapPtr, uint32_t tableId)
{
    DB_UNUSED(ctx);
    uint32_t hash = BufTableMapGetTableIdHash(tableId);
    uint8_t *key = (uint8_t *)(uintptr_t)tableId;
    DbOamapT *tableMap = (DbOamapT *)tableMapPtr;
    uint32_t pageCount = (uint32_t)(uintptr_t)DbOamapRemove(tableMap, hash, key);
    pageCount--;
    if (pageCount == 0) {
        return;
    }
    Status res = DbOamapInsert(tableMap, hash, key, (uint8_t *)(uintptr_t)pageCount, NULL);
    DB_ASSERT(res == GMERR_OK);
}

void BufTableMapClearTableInfo(void *ctx, void *tableMap, uint32_t tableId)
{
    DB_UNUSED(ctx);
    uint32_t hash = BufTableMapGetTableIdHash(tableId);
    uint8_t *key = (uint8_t *)(uintptr_t)tableId;
    (void)DbOamapRemove((DbOamapT *)tableMap, hash, key);
}

StatusInter BufpoolEnqueueByTable(BufpoolMgrT *mgr, PageOptionE option, void *enqueArg)
{
    DB_UNUSED(option);
    BufEnqueArgT *arg = (BufEnqueArgT *)enqueArg;
    TableRecyArgT *tableRecyArg = (TableRecyArgT *)arg->recyArg;
    BufPoolT *bufPool = GetBufpoolByPageId(mgr, arg->pageId);
    BufBucketT *bucket = arg->bufDesc->attachedBucket;

    DbRWSpinWLock(&bucket->lock);
    bool recyArgNull = (tableRecyArg == NULL);
    if (!recyArgNull && tableRecyArg->recyType == SE_RECYCLE_TABLE && tableRecyArg->flag == DB_RECYCLE_TABLE_ADD) {
        arg->bufDesc->tableId = tableRecyArg->tableId;
    } else {
        arg->bufDesc->tableId = DB_INVALID_ID32;
    }
    DbRWSpinWUnlock(&bucket->lock);

    MemUtilsT *memUtils = mgr->memUtils;
    if (!arg->bufDesc->isResident) {
        BufLruAdd(&bufPool->list[arg->bufDesc->listId], arg->bufDesc, BUF_ADD_HEAD);
    }
    if (!recyArgNull) {
        if (tableRecyArg->recyType == SE_RECYCLE_TABLE && tableRecyArg->flag == DB_RECYCLE_TABLE_DROP) {
            (*mgr->tableMapFns.tableMapClearFn)(memUtils, (void *)bufPool->tableMap, tableRecyArg->tableId);
        } else if (tableRecyArg->recyType == SE_RECYCLE_TABLE && tableRecyArg->flag == DB_RECYCLE_TABLE_ADD) {
            (void)(*mgr->tableMapFns.tableMapIncPageCntFn)(memUtils, (void *)bufPool->tableMap, arg->bufDesc->tableId);
        }
    }
    return STATUS_OK_INTER;
}

static bool IsPageCountSatisfied(
    MemUtilsT *memTransCtx, const TableMapFnsT *tableMapFns, DbOamapT *tableMap, uint32_t tableId, bool reTry)
{
    if (reTry || tableId == DB_INVALID_ID32) {
        return true;
    }
    uint32_t pageCount = (*tableMapFns->tableMapGetPageCntFn)(memTransCtx, (void *)tableMap, tableId);
    if (pageCount == 0) {
        return true;  // tableId not exist in map, it has been removed
    }
    return pageCount > TABLE_PAGES_MIN;
}

static bool AbleToRecycle(BufpoolMgrT *mgr, BufPoolT *bufPool, BufDescT *item, bool needChkTimeout, bool reTry)
{
    return (BufDescCanRecycleWhileChunkRecycle(mgr, bufPool, item) && BufChkTimeout(item, needChkTimeout) &&
            IsPageCountSatisfied(mgr->memUtils, &mgr->tableMapFns, bufPool->tableMap, item->tableId, reTry));
}

StatusInter BufGetDescFromListByPageCount(BufpoolMgrT *mgr, BufPoolT *bufPool, BufDescLinkedListT *list,
    BufGetDescByTableArgsT bufGetDescByTableArgs, BufDescT **bufDesc)
{
    bool needChkTimeout = bufGetDescByTableArgs.needChkTimeout;
    bool reTry = bufGetDescByTableArgs.reTry;
    BufDescT *shift;
    BufBucketT *bucket;
    uint32_t i;

    MemUtilsT *memUtils = mgr->memUtils;
    BufDescT *item = BUF_DESC_LIST_TAIL_ENTRY(list);
    for (i = 0; i < list->count; i++) {
        if (!AbleToRecycle(mgr, bufPool, item, needChkTimeout, reTry)) {
            // move page to the list head
            shift = item;
            item = BUF_DESC_PREV_ENTRY(item);
            BufLruShift(list, shift);
            continue;
        }

        if (!item->attachedBucket) {
            break;
        }

        bucket = item->attachedBucket;
        DbRWSpinWLock(&bucket->lock);
        // Check again in bucket lock.
        if (!AbleToRecycle(mgr, bufPool, item, needChkTimeout, reTry)) {
            DbRWSpinWUnlock(&bucket->lock);
            item = BUF_DESC_PREV_ENTRY(item);
            continue;
        }

        BufRemoveFromBucket(bucket, item);
        DbRWSpinWUnlock(&bucket->lock);
        break;
    }

    if (i == list->count) {
        *bufDesc = NULL;
        return STATUS_OK_INTER;
    }

    if (item->tableId != DB_INVALID_ID32) {  // Decrease page counter after eliminate the page
        (*mgr->tableMapFns.tableMapDecPageCntFn)(memUtils, (void *)bufPool->tableMap, item->tableId);
    }

    // 找到适合淘汰的Desc
    // 当页被换出时，需要把页面信息同步到压缩区
    StatusInter ret = BufLruRemoveAndCompression(mgr, list, item);
    *bufDesc = item;
    return ret;
}

StatusInter BufpoolRecycleByTable(
    BufpoolMgrT *mgr, BufGetDescArgsT *args, void *recyArg, bool reTry, BufDescT **bufDesc)
{
    DB_UNUSED(recyArg);
    BufDescT *item = NULL;
    uint32_t option = args->options;
    bool isScanOp = (option & ENTER_PAGE_SCAN) != 0;
    BufPoolT *bufPool = GetBufpoolByPageId(mgr, args->pageId);
    DbSpinLock(&bufPool->lock);
    StatusInter ret = STATUS_OK_INTER;
    if (isScanOp) {
        BufGetDescByTableArgsT bufGetDescByTableArgs = {.needChkTimeout = false, .reTry = reTry};
        if (BufChkScanLimit(bufPool)) {
            ret = BufGetDescFromListByPageCount(
                mgr, bufPool, &bufPool->list[LRU_LIST_STATS_SCAN], bufGetDescByTableArgs, &item);
        } else {
            ret = BufGetDescFromListByPageCount(
                mgr, bufPool, &bufPool->list[LRU_LIST_NORMAL], bufGetDescByTableArgs, &item);
            if (ret == STATUS_OK_INTER && item == NULL) {
                ret = BufGetDescFromListByPageCount(
                    mgr, bufPool, &bufPool->list[LRU_LIST_STATS_SCAN], bufGetDescByTableArgs, &item);
            }
        }
    } else {
        BufGetDescByTableArgsT bufGetDescByTableArgs = {.needChkTimeout = true, .reTry = reTry};
        ret = BufGetDescFromListByPageCount(
            mgr, bufPool, &bufPool->list[LRU_LIST_STATS_SCAN], bufGetDescByTableArgs, &item);
        if (ret == STATUS_OK_INTER && item == NULL) {
            bufGetDescByTableArgs.needChkTimeout = false;
            ret = BufGetDescFromListByPageCount(
                mgr, bufPool, &bufPool->list[LRU_LIST_NORMAL], bufGetDescByTableArgs, &item);
        }
    }
    DbSpinUnlock(&bufPool->lock);
    if (ret == STATUS_OK_INTER) {
        *bufDesc = item;
    }
    return ret;
}

StatusInter BufPreProcessForTable(BufpoolMgrT *mgr, void *recyArg, BufDescT *bufDesc)
{
    BufPoolT *bufPool = mgr->bufPool;
    if (recyArg != NULL && ((TableRecyArgT *)recyArg)->recyType == SE_RECYCLE_TABLE &&
        ((TableRecyArgT *)recyArg)->flag == DB_RECYCLE_TABLE_DROP) {
        DbSpinLock(&bufPool->lock);
        MemUtilsT *memUtils = mgr->memUtils;
        (*mgr->tableMapFns.tableMapClearFn)(memUtils, (void *)bufPool->tableMap, ((TableRecyArgT *)recyArg)->tableId);
        DbSpinUnlock(&bufPool->lock);
    }
    return STATUS_OK_INTER;
}
