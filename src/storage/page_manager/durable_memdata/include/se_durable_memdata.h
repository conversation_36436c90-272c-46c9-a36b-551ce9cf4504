/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: durable memdata page manager
 * Author: zhangfengyu
 * Create: 2022-10-12
 */

#ifndef SE_DURABLE_MEMDATA_H
#define SE_DURABLE_MEMDATA_H

#include "adpt_types.h"
#include "se_define.h"
#include "se_page_desc.h"
#include "se_device.h"
#include "se_dev_node.h"

#ifdef __cplusplus
extern "C" {
#endif
typedef struct TagDumemMgrT {
    PageMgrT base;
    uint32_t pageSize;
    uint32_t pageCntPerDev;
    SeInstanceT *seIns;
    DeviceMgrT *devMgr;
    BufDescT *pageDescArray;
} DumemMgrT;

static inline BufDescT *DumemGetPageDesc(DumemMgrT *mgr, PageIdT addr)
{
    uint32_t descIdx = addr.deviceId * mgr->pageCntPerDev + addr.blockId;
    return &mgr->pageDescArray[descIdx];
}

static inline void DumemInitPageDesc(DumemMgrT *mgr, PageIdT *addr, uint8_t *page)
{
    BufDescT *pageDesc = DumemGetPageDesc(mgr, *addr);
    pageDesc->pageId = *addr;
    pageDesc->page = page;
}

static ALWAYS_INLINE PageMgrT *DumemSpaceGetDuMemMgr(SeInstanceT *seIns)
{
    if (seIns->duMemMgr != NULL) {
        return (PageMgrT *)seIns->duMemMgr;
    } else {
        return (PageMgrT *)seIns->pageMgr;
    }
}

SO_EXPORT StatusInter DumemInit(SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx);
SO_EXPORT StatusInter DumemCreatePageMgr(SeInstanceT *seIns, DbMemCtxT *memCtx, PageMgrT **pageMgr);
SO_EXPORT StatusInter DumemAllocPage(DumemMgrT *mgr, AllocPageParamT *allocPageParam, PageIdT *addr);
SO_EXPORT StatusInter DumemFreePage(DumemMgrT *mgr, FreePageParamT *freePageParam);

SO_EXPORT StatusInter DumemAllocExtent(DumemMgrT *mgr, uint32_t spaceId, uint32_t trmId, PageIdT *extentId);
SO_EXPORT StatusInter DumemFreeExtent(DumemMgrT *mgr, uint32_t spaceId, PageIdT *extentId);

SO_EXPORT StatusInter DumemGetPage(DumemMgrT *mgr, PageIdT addr, uint8_t **page, PageOptionE option, bool isWrite);
SO_EXPORT StatusInter DumemGetPageInit(
    DumemMgrT *mgr, uint32_t trmId, PageIdT addr, PageOptionE option, uint8_t **page);
SO_EXPORT void DumemLeavePage(DumemMgrT *pageMgr, PageIdT pageAddr, bool isChanged);
SO_EXPORT void DumemDestroyPageMgr(SeInstanceT *seIns, PageMgrT *pageMgr);
SO_EXPORT StatusInter DumemGetStat(SeInstanceT *seIns, DumemStatT *dumemStat);

#ifdef __cplusplus
}
#endif

#endif
