/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: rsm check 函数
 * Author:
 * Create: 2024-06-12
 */

#include "db_rsm_check.h"
#include "se_rsm_check.h"
#include "se_rsm_tablespace_am.h"

void DbRsmCheckBlockMgrMemory(RsmCheckCtxT *ctx)
{
    RsmSpaceCheckAndFreeUnusedEntries();
}

void SeSetRsmCheckBlockMgrMemoryHandle(void)
{
    static bool isSet = false;
    if (isSet) {
        return;
    }
    isSet = true;

    const RsmCheckHandleFuncConfT conf = {
        .type = RSM_CHECK_BLOCK_MGR_MEM,
        .func = DbRsmCheckBlockMgrMemory,
    };

    DbRsmCheckSetHandleFunc(conf);
}
