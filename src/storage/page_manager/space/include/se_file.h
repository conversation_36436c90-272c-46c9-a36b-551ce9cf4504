/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Create: 2023-6
 */

#ifndef SE_FILE_H
#define SE_FILE_H

#include "db_file.h"
#include "db_internal_error.h"
#include "se_instance.h"

#ifdef __cplusplus
extern "C" {
#endif

#define SE_FILE_TIMEOUT_MS (100)
typedef struct Buf {
    void *buf;
    uint32_t size;
} BufT;

void SeFileAlarmUpdate(Status operateRet);

StatusInter SeFileOpen(const char *name, int32_t flags, int32_t *handle);

void SeFileClose(int32_t *handle);

StatusInter SeFileRemove(const char *name);

StatusInter SeFileRead(int32_t handle, int64_t offset, BufT bufData);

StatusInter SeFileWrite(SeInstanceT *seIns, int32_t handle, int64_t offset, BufT bufData);

StatusInter SeFileCreate(const char *name, int32_t flags, int32_t *handle);

// Open an empty file, otherwise create it
StatusInter SeFileOpenCreate(const char *name, int32_t flags, int32_t *handle);

StatusInter SeFileConstructPath(const char *path, const char *fileName, char *buff, uint32_t buffSize);

StatusInter SeFileFullPath(const char *path, char *realPath, size_t maxLen);

#ifdef __cplusplus
}
#endif

#endif
