/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: wenming
 * Create: 2023/07/26
 */

#ifndef SE_UNDO_REPLAY_H
#define SE_UNDO_REPLAY_H

#include "se_replay.h"
#include "se_undo_inner.h"
#include "se_persistcap_undo_replay.h"

#ifdef __cplusplus
extern "C" {
#endif

StatusInter UndoReplayFuncRegister(SeInstanceT *seIns);
StatusInter UndoReplayGeneralLog(RedoReplayArgsT *replayArgs);
StatusInter UndoReplayRecordLog(RedoReplayArgsT *replayArgs);
StatusInter UndoReplayRsegPageInit(RedoReplayArgsT *replayArgs);
StatusInter UndoReplaySegPageInit(RedoReplayArgsT *replayArgs);
StatusInter UndoReplayUndoPageInit(RedoReplayArgsT *replayArgs);
StatusInter UndoReplayUndoLogHeaderCreate(RedoReplayArgsT *replayArgs);
StatusInter UndoReplayTrxUndoRetainCachedLogInit(RedoReplayArgsT *replayArgs);

StatusInter UndoReplayUpdateSegTruncateState(RedoReplayArgsT *replayArgs);

#ifdef __cplusplus
}
#endif
#endif  // SE_UNDO_REPLAY_H
