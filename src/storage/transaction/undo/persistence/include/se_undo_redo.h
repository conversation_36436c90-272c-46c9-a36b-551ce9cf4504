/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: wenming
 * Create: 2023/07/26
 */

#ifndef SE_UNDO_REDO_H
#define SE_UNDO_REDO_H

#include "se_redo.h"
#include "se_undo_inner.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct TagUndoRedoHandle {
    void (*undoWriteGeneralRedoLog)(RedoRunCtxT *redoCtx, uint8_t *page, size_t offset, size_t dataSize);
    StatusInter (*undoWriteRecordRedoLog)(
        RedoRunCtxT *redoCtx, UndoPageHeaderT *page, uint32_t offset, uint32_t dataSize);
    void (*undoWriteRedoLogForRsegPageInit)(RedoRunCtxT *redoCtx, PageIdT rsegAddr, uint32_t fileId);
    void (*undoWriteRedoLogForSegPageInit)(RedoRunCtxT *redoCtx, PageIdT rsegAddr, UndoTypeE type);
    void (*undoWriteRedoLogForUndoPageInit)(
        RedoRunCtxT *redoCtx, PageIdT pageAddr, uint16_t pageType, PageIdT hdrPageAddr);
    void (*undoWriteRedoLogForUndoLogHeaderCreate)(RedoRunCtxT *redoCtx, PageIdT rsegAddr, TrxIdT trxId);
    void (*undoWriteRedoLogForTrxUndoRetainCachedLogInit)(
        RedoRunCtxT *redoCtx, PageIdT pageAddr, uint32_t logHdrOffset, uint64_t trxId, UndoTypeE type);
    void (*undoWriteRedoLogForSegTruncateFinalize)(RedoRunCtxT *redoCtx, UndoPageHeaderT *undoPage, TrxUndoLogT *undo);
} UndoRedoHandleT;

SO_EXPORT StatusInter UndoRedoHandleInit(SeInstanceT *seIns);

extern UndoRedoHandleT g_gmdbUndoRedoHandler;

inline static void UndoSetRedoHandle(UndoRedoHandleT *handle)
{
    g_gmdbUndoRedoHandler = *handle;
}

inline static void UndoWriteGeneralRedoLog(RedoRunCtxT *redoCtx, uint8_t *page, size_t offset, size_t dataSize)
{
    if (g_gmdbUndoRedoHandler.undoWriteGeneralRedoLog != NULL) {
        g_gmdbUndoRedoHandler.undoWriteGeneralRedoLog(redoCtx, page, offset, dataSize);
    }
}
inline static StatusInter UndoWriteRecordRedoLog(
    RedoRunCtxT *redoCtx, UndoPageHeaderT *page, uint32_t freePos, uint32_t recSize)
{
    if (g_gmdbUndoRedoHandler.undoWriteRecordRedoLog != NULL) {
        return g_gmdbUndoRedoHandler.undoWriteRecordRedoLog(redoCtx, page, freePos, recSize);
    }
    return STATUS_OK_INTER;
}
inline static void UndoWriteRedoLogForRsegPageInit(RedoRunCtxT *redoCtx, PageIdT rsegAddr, uint32_t fileId)
{
    if (g_gmdbUndoRedoHandler.undoWriteRedoLogForRsegPageInit != NULL) {
        g_gmdbUndoRedoHandler.undoWriteRedoLogForRsegPageInit(redoCtx, rsegAddr, fileId);
    }
}
inline static void UndoWriteRedoLogForSegPageInit(RedoRunCtxT *redoCtx, PageIdT rsegAddr, UndoTypeE type)
{
    if (g_gmdbUndoRedoHandler.undoWriteRedoLogForSegPageInit != NULL) {
        g_gmdbUndoRedoHandler.undoWriteRedoLogForSegPageInit(redoCtx, rsegAddr, type);
    }
}
inline static void UndoWriteRedoLogForUndoPageInit(
    RedoRunCtxT *redoCtx, PageIdT pageAddr, uint16_t pageType, PageIdT hdrPageAddr)
{
    if (g_gmdbUndoRedoHandler.undoWriteRedoLogForUndoPageInit != NULL) {
        g_gmdbUndoRedoHandler.undoWriteRedoLogForUndoPageInit(redoCtx, pageAddr, pageType, hdrPageAddr);
    }
}
inline static void UndoWriteRedoLogForUndoLogHeaderCreate(RedoRunCtxT *redoCtx, PageIdT rsegAddr, TrxIdT trxId)
{
    if (g_gmdbUndoRedoHandler.undoWriteRedoLogForUndoLogHeaderCreate != NULL) {
        g_gmdbUndoRedoHandler.undoWriteRedoLogForUndoLogHeaderCreate(redoCtx, rsegAddr, trxId);
    }
}
inline static void UndoWriteRedoLogForTrxUndoRetainCachedLogInit(
    RedoRunCtxT *redoCtx, PageIdT pageAddr, uint32_t logHdrOffset, uint64_t trxId, UndoTypeE type)
{
    if (g_gmdbUndoRedoHandler.undoWriteRedoLogForTrxUndoRetainCachedLogInit != NULL) {
        g_gmdbUndoRedoHandler.undoWriteRedoLogForTrxUndoRetainCachedLogInit(
            redoCtx, pageAddr, logHdrOffset, trxId, type);
    }
}
inline static void UndoWriteRedoLogForSegTruncateFinalize(
    RedoRunCtxT *redoCtx, UndoPageHeaderT *undoPage, TrxUndoLogT *undo)
{
    if (g_gmdbUndoRedoHandler.undoWriteRedoLogForSegTruncateFinalize != NULL) {
        g_gmdbUndoRedoHandler.undoWriteRedoLogForSegTruncateFinalize(redoCtx, undoPage, undo);
    }
}
#ifdef __cplusplus
}
#endif
#endif  // SE_UNDO_REDO_H
