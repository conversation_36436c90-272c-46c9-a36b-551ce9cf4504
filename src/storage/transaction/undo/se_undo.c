/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2021. All rights reserved.
 * Description: Undo log implementation
 * Author: wang<PERSON><PERSON>
 * Create: 2020/10/19
 */
#include <securec.h>
#include "db_dyn_load.h"
#include "se_dfx.h"
#include "se_undo_inner.h"
#include "se_trx_mgr.h"
#include "db_config.h"
#include "adpt_atomic.h"
#include "db_table_space.h"
#include "se_space_inner.h"
#include "se_undo_addr.h"
#include "se_undo_trx_resource.h"
#include "se_log.h"
#include "se_undo_redo.h"
#include "db_inter_process_rwlatch.h"

#include "se_database.h"
#include "se_heap_page.h"

// 内存态和持久化态都用到了
SO_EXPORT_FOR_TS UndoRedoHandleT g_gmdbUndoRedoHandler = {0};

#define UNDO_PERCENT 100

Status GetUndoSpacePageLimit(SeInstanceT *seIns, uint32_t *undoSpacePageCntLimit)
{
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(seIns->dbInstance);
    DbCfgValueT maxUndoSpaceVal = {0};
    DbCfgValueT pageSizeVal = {0};
    Status retStatus = DbCfgGet(cfgHandle, DB_CFG_MAX_UNDO_SPACE_SIZE, &maxUndoSpaceVal);
    if (retStatus != GMERR_OK) {
        return retStatus;
    }
    retStatus = DbCfgGet(cfgHandle, DB_CFG_SE_PAGE_SIZE, &pageSizeVal);
    if (retStatus != GMERR_OK) {
        return retStatus;
    }
    if (pageSizeVal.int32Val == 0) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "(UNDO) page size is 0");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    // max_undo_space_size / page_size
    uint32_t maxUndoSpace = (uint32_t)maxUndoSpaceVal.int32Val * DB_MEBI;
    uint32_t pageSize = (uint32_t)pageSizeVal.int32Val * DB_KIBI;
    *undoSpacePageCntLimit = (uint32_t)(maxUndoSpace / pageSize);
    return GMERR_OK;
}

StatusInter UndoRedoInit(SeInstanceT *seIns)
{
    typedef StatusInter (*UndoPersistenceInitT)(SeInstanceT * seIns);
    UndoPersistenceInitT undoPersistenceInitFunc =
        (UndoPersistenceInitT)DbDynLoadGetFunc(COMPONENT_PERSISTENCE, "undo_redo");
    if (undoPersistenceInitFunc == NULL) {
        DB_LOG_INFO("undo persistence feature off");
        return STATUS_OK_INTER;
    }
    return undoPersistenceInitFunc(seIns);
}

StatusInter UndoSpaceCreate(SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx)
{
    DB_POINTER2(seIns, seTopShmMemCtx);
    DbShmCtxIdE ctxId = DB_HUGETLB_SHMCTX_ID;
    DbMemCtxT *tlbMemctx = (DbMemCtxT *)DbGetShmemCtxById(ctxId, seIns->instanceId);
    if (tlbMemctx == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER,
            "(UNDO) USC: get tlb memctx. Args(instanceId:%" PRIu32 ", ctxId:%" PRId32 ")", seIns->instanceId,
            (int32_t)ctxId);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    // 从tlbMemctx中申请共享内存承载undo space结构体, 依赖对应memCtx在server进程退出时销毁
    UndoSpaceT *undoSpace = SeShmAlloc(tlbMemctx, sizeof(UndoSpaceT), &seIns->undoSpaceShm);
    if (undoSpace == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "(UNDO) USC: alloc undo space instance");
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_s(undoSpace, sizeof(UndoSpaceT), 0, sizeof(UndoSpaceT));

    DbRWSpinInit(&undoSpace->rwLock);
    undoSpace->meta.seInstanceId = seIns->seConfig.instanceId;
    undoSpace->meta.numRsegs = DEFAULT_RSEG_NUM;
    undoSpace->meta.undoPageReuseLimit = UndoPageReuseLimit(seIns->seConfig.pageSize);
    undoSpace->meta.maxRowSize = SeGetUndoPageMaxRecordSize(seIns->seConfig.pageSize * DB_KIBI);
    undoSpace->meta.undoSpaceId = DB_UNDO_TABLE_SPACE_INDEX;
    undoSpace->meta.usedPageCnt = 0u;
    undoSpace->shmMemCtxId = seIns->seTopShmMemCtxId;
    undoSpace->rsegShmPtr = DB_INVALID_SHMPTR;

    StatusInter ret = STATUS_OK_INTER;
    PageIdT rsegPageId = SE_INVALID_PAGE_ADDR;

    if (SeGetStorageStatus(seIns) == SE_ON_DISK_CREATE || SeGetStorageStatus(seIns) == SE_ON_DISK_BACK_UP_SCHEMA) {
        undoSpace->meta.undoFileId = SeGetNewTrmId(seIns);
        ret = UndoSpaceCreateRseg(seIns, seTopShmMemCtx, undoSpace, &rsegPageId);
    } else {
        ret = UndoSpaceLoadRseg(seIns, seTopShmMemCtx, undoSpace, &rsegPageId);
    }
    if (ret != STATUS_OK_INTER) {
        DbShmemCtxFree(tlbMemctx, seIns->undoSpaceShm);
        seIns->undoSpaceShm = DB_INVALID_SHMPTR;
        return ret;
    }

    DB_LOG_INFO("undo space created (fileId: %" PRIu32 ", rseg dev: %" PRIu32 ", rseg blk: %" PRIu32 ")",
        undoSpace->meta.undoSpaceId, rsegPageId.deviceId, rsegPageId.blockId);
    return STATUS_OK_INTER;
}

StatusInter UndoResetRsegMemCache(SeInstanceT *seIns)
{
    DB_POINTER(seIns);
    UndoSpaceT *undoSpace = DbShmPtrToAddr(seIns->undoSpaceShm);
    if (undoSpace == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "(UNDO) USO: inv shm addr for undo space (segid: %" PRIu32 " offset: %" PRIu32 ")",
            seIns->undoSpaceShm.segId, seIns->undoSpaceShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    TrxRsegT *rseg = (TrxRsegT *)DbShmPtrToAddr(undoSpace->rsegShmPtr);
    if (rseg == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "inv rseg shmAddr (%" PRIu32 ",%" PRIu32 ")",
            undoSpace->rsegShmPtr.segId, undoSpace->rsegShmPtr.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    UndoCleanUpCache(seIns, rseg);

    return STATUS_OK_INTER;
}

StatusInter UndoInitRseg(SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx)
{
    UndoSpaceT *undoSpace = DbShmPtrToAddr(seIns->undoSpaceShm);
    if (undoSpace == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "(UNDO) USO: inv shm addr for undo space (segid: %" PRIu32 " offset: %" PRIu32 ")",
            seIns->undoSpaceShm.segId, seIns->undoSpaceShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    PageIdT rsegPageId = SE_INVALID_PAGE_ADDR;
    if (SeGetStorageStatus(seIns) == SE_ON_DISK_CREATE || SeGetStorageStatus(seIns) == SE_ON_DISK_BACK_UP_SCHEMA) {
        undoSpace->meta.undoFileId = SeGetNewTrmId(seIns);
        return UndoSpaceCreateRseg(seIns, seTopShmMemCtx, undoSpace, &rsegPageId);
    } else {
        return UndoSpaceLoadRseg(seIns, seTopShmMemCtx, undoSpace, &rsegPageId);
    }
}

StatusInter UndoSpaceOpen(ShmemPtrT undoSpaceIns, SeRunCtxT *seCtx, SeUndoCtxT **undoCtx)
{
    DB_POINTER2(seCtx, undoCtx);
    UndoSpaceT *undoSpace = DbShmPtrToAddr(undoSpaceIns);
    if (undoSpace == NULL) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER,
            "(UNDO) USO: inv shm addr for undo space (segid: %" PRIu32 " offset: %" PRIu32 ")", undoSpaceIns.segId,
            undoSpaceIns.offset);
        return DATA_EXCEPTION_INTER;
    }
    // 申请undo的运行上下文内存, 在事务结束时调用UndoSpaceClose配对释放
    SeUndoCtxT *newCtx = DbDynMemCtxAlloc(seCtx->sessionMemCtx, sizeof(SeUndoCtxT));
    if (newCtx == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_MEM_FAILED, "(UNDO) USO: alloc mem for run ctx");
        return OUT_OF_MEMORY_MEM_FAILED;
    }

    (void)memset_s(newCtx, sizeof(SeUndoCtxT), 0, sizeof(SeUndoCtxT));

    newCtx->seCtx = seCtx;
    newCtx->undoSpace = undoSpace;
    newCtx->pageMgr = seCtx->pageMgr;
    if (newCtx->pageMgr == NULL) {
        DbDynMemCtxFree(seCtx->sessionMemCtx, newCtx);
        newCtx = NULL;
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "(UNDO) USO: inv pointer for memdata manager");
        return DATA_EXCEPTION_INTER;
    }
    newCtx->enableCache = ((SeInstanceT *)seCtx->seIns)->storageType == SE_MEMDATA;
    newCtx->redoCtx = SeGetCurRedoCtx();

    *undoCtx = newCtx;
    return STATUS_OK_INTER;
}

void UndoSpaceClose(SeUndoCtxT *undoCtx)
{
    DB_POINTER3(undoCtx, undoCtx->undoSpace, undoCtx->seCtx);
    DbDynMemCtxFree(undoCtx->seCtx->sessionMemCtx, undoCtx);
    undoCtx = NULL;
}

StatusInter TrxUndoLogHeaderCreate(PageMgrT *pageMgr, UndoPageHeaderT *undoPage, TrxIdT trxId, uint32_t *logOffset)
{
    DB_POINTER(undoPage);
    // read free space pos
    uint32_t freePos = undoPage->freeStartPos;
    uint32_t newFreePos = (uint32_t)(freePos + sizeof(UndoLogHeaderT));

    UndoSegHeaderT *segHdr = (UndoSegHeaderT *)(undoPage + 1);
    DB_ASSERT(segHdr->magic == UNDO_SEG_HEADER_MAGIC);
    segHdr->state = TRX_UNDO_ACTIVE;

    uint16_t prevLogOffset = segHdr->lastLogHdr;
    if (SECUREC_UNLIKELY(segHdr->lastLogHdr != 0u)) {
        UndoLogHeaderT *prevLog = UndoGetLogHdr(undoPage, segHdr->lastLogHdr);
        DB_ASSERT(prevLog->magic == UNDO_LOG_HEADER_MAGIC);
        prevLog->nextLogHdr = (uint16_t)freePos;
    }
    segHdr->lastLogHdr = (uint16_t)freePos;

    // create a undo log header
    UndoLogHeaderT *logHdr = UndoGetLogHdr(undoPage, freePos);
    logHdr->magic = UNDO_LOG_HEADER_MAGIC;
    logHdr->purgeHandleFlag = false;
    logHdr->recordStartPtr = (UndoRecPtrT){{
        .pageId = SerializePageId(pageMgr, undoPage->baseHead.addr),
        .offset = newFreePos,
    }};
    logHdr->unclearPos = logHdr->recordStartPtr;
    logHdr->prevLogHdr = prevLogOffset;
    logHdr->nextLogHdr = 0u;
    logHdr->trxId = trxId;
    logHdr->trxCmtTs = DB_INVALID_TRX_ID;
    logHdr->historyNode.prev = UNDO_INVALID_ROLLPTR;
    logHdr->historyNode.prevPage = UNDO_INVALID_PAGE;
    logHdr->historyNode.next = UNDO_INVALID_ROLLPTR;
    logHdr->historyNode.nextPage = UNDO_INVALID_PAGE;

    RW_LATCH_WLATCH_RETURN_IF_FAILED(&undoPage->baseHead.lock);
    undoPage->undoLogCnt++;
    DbInterProcRWUnlatchW(&undoPage->baseHead.lock);
    undoPage->freeStartPos = newFreePos;
    undoPage->baseHead.freeSize = (uint16_t)(undoPage->baseHead.freeSize - (uint16_t)(sizeof(UndoLogHeaderT)));
    DB_ASSERT(SeGetPageTotalSize(&undoPage->baseHead) - undoPage->freeStartPos == undoPage->baseHead.freeSize);
    *logOffset = freePos;
    return STATUS_OK_INTER;
}

static TrxUndoLogT *TrxUndoLogMemObjCreate(const SeUndoCtxT *undoCtx)
{
    SeInstanceT *seIns = undoCtx->seCtx->seIns;
    // 申请一块动态内存存放 undo 信息, 在TrxUndoLogMemObjFree中配对释放
    TrxUndoLogT *undo = DbDynMemCtxAlloc(seIns->seServerMemCtx, sizeof(TrxUndoLogT));
    if (undo == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "(UNDO) alloc memory for trx undoLog");
        return NULL;
    }
    return undo;
}

static void TrxUndoLogMemObjInit(
    SeUndoCtxT *undoCtx, TrxRsegT *rseg, const TrxUndoLogArgsT *undoArgs, TrxUndoLogT **undo)
{
    (*undo)->rseg = rseg;
    (*undo)->usedPageCnt = 1u;
    (*undo)->id = (uint16_t)undoArgs->slotId;
    (*undo)->type = undoArgs->type;
    (*undo)->isRetainedCache = undoArgs->isRetainedCache;
    (*undo)->state = TRX_UNDO_ACTIVE;
    (*undo)->trxId = undoArgs->trxId;
    (*undo)->logHdrOffset = undoArgs->hdrOffset;
    (*undo)->recCnt = 0u;
    (*undo)->next = NULL;
    (*undo)->rollPtr = UNDO_INVALID_ROLLPTR;
    UndoSetSegHdr(undoCtx, *undo, undoArgs->hdrPageId, undoArgs->segHdrPage);
    UndoSetLastUndoPage(undoCtx, *undo, undoArgs->hdrPageId, undoArgs->segHdrPage);
}

void TrxUndoLogMemObjFree(const SeUndoCtxT *undoCtx, TrxUndoLogT *undo)
{
    if (undo == NULL) {
        return;
    }
    SeInstanceT *seIns = undoCtx->seCtx->seIns;
    DbDynMemCtxFree(seIns->seServerMemCtx, undo);
    undo = NULL;
}

StatusInter TrxRsegFindFreeSlot(RSegPageHeaderT *rsegHdr, RSegPageSlotT **slot, uint32_t *slotId)
{
    RSegPageSlotT *undoSegSlots = (RSegPageSlotT *)(rsegHdr + 1);
    for (uint32_t i = 0; i < rsegHdr->numUndoSegs; ++i) {
        if (!DbIsPageIdValid(undoSegSlots[i])) {
            *slot = &undoSegSlots[i];
            *slotId = i;
            return STATUS_OK_INTER;
        }
    }
    return RESTRICT_VIOLATION_INTER;
}

StatusInter TrxUndoInitPage(UndoPageHeaderT *pageHdr, UndoTypeE type, PageIdT segPageAddr)
{
    pageHdr->magic = UNDO_PAGE_HEADER_MAGIC;
    pageHdr->undoRecCnt = 0u;
    pageHdr->undoLogCnt = 0u;
    pageHdr->pageType = (uint16_t)type;
    pageHdr->baseHead.beginPos = sizeof(UndoPageHeaderT);
    pageHdr->freeStartPos = (uint32_t)sizeof(UndoPageHeaderT);
    pageHdr->baseHead.freeSize = (uint16_t)(SeGetPageTotalSize(&pageHdr->baseHead) - pageHdr->freeStartPos);
    // 如果是seg页，会TrxUndoSegPageInit再次设置
    SePageHeadSetType(&pageHdr->baseHead, PERSISTENCE_PAGE_TYPE_UNDO);
    UndoInitPageNode(pageHdr);
    pageHdr->pageNode.segPageId = segPageAddr;
    uint8_t *ptr = (uint8_t *)pageHdr;
    errno_t err = memset_s(ptr + pageHdr->freeStartPos, pageHdr->baseHead.freeSize, 0, pageHdr->baseHead.freeSize);
    if (err != EOK) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "init undo page memset, ER: (%" PRIi32 "), bufSize: %" PRIu32, err,
            pageHdr->baseHead.freeSize);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    return STATUS_OK_INTER;
}

StatusInter UndoPageAddCheck(SeUndoCtxT *undoCtx)
{
    uint32_t undoSpacePageLimit = 0u;
    uint32_t currPageCnt = DbAtomicGet(&(undoCtx->undoSpace->meta.usedPageCnt));
    Status retStatus = GetUndoSpacePageLimit(undoCtx->seCtx->seIns, &undoSpacePageLimit);
    if (SECUREC_UNLIKELY(retStatus != GMERR_OK)) {
        return DbGetStatusInterErrno(retStatus);
    }
    if (currPageCnt >= undoSpacePageLimit) {
        SE_LAST_ERROR(RESTRICT_VIOLATION_INTER, "currPageCnt(%" PRIu32 "), pageLimit(%" PRIu32 ")", currPageCnt,
            undoSpacePageLimit);
        return RESTRICT_VIOLATION_INTER;
    }
    return STATUS_OK_INTER;
}

StatusInter UndoBlockFreeByAddr(const SeUndoCtxT *undoCtx, PageIdT pageId)
{
    DB_POINTER2(undoCtx, undoCtx->pageMgr);
    // Undo lfs has been configured to automatically release free page
    // so just need to set free size = availSize
    // NOTE: Undo only alloc and release the whole page, so do NOT need to care about exact free size
    FreePageParamT freePageParam = {.spaceId = undoCtx->undoSpace->meta.undoSpaceId,
        .addr = pageId,
        .dbInstance = DbGetInstanceByMemCtx((DbMemCtxT *)undoCtx->seCtx->sessionMemCtx),
        .labelRsmUndo = NULL};
    SeInitCachePagePara(&freePageParam.cachePagePara);
    return SeFreePage(undoCtx->pageMgr, &freePageParam);
}

static StatusInter TrxUndoCreate(SeUndoCtxT *undoCtx, TrxRsegT *rseg, TrxUndoLogArgsT undoArgs, TrxUndoLogT **undo)
{
    *undo = TrxUndoLogMemObjCreate(undoCtx);
    if (*undo == NULL) {
        // maybe out of memory
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "(UNDO) TUC: create undo log");
        return OUT_OF_MEMORY_INTER;
    }
    // read the rseg header page
    uint32_t id = 0;
    PageIdT segAddr = SE_INVALID_PAGE_ADDR;
    StatusInter ret = TrxUndoSegAlloc(undoCtx, rseg, true, &segAddr, &id);
    if (ret != STATUS_OK_INTER) {
        // maybe out of memory
        SE_ERROR(ret, "(UNDO) TUC: create undo segment");
        TrxUndoLogMemObjFree(undoCtx, *undo);
        *undo = NULL;
        return ret;
    }

    ret = TrxUndoSegInit(undoCtx, id, segAddr, &undoArgs);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "(UNDO) TUC: init undo segment");
        (void)TrxUndoSegFree(undoCtx, rseg, segAddr, id);
        TrxUndoLogMemObjFree(undoCtx, *undo);
        *undo = NULL;
        return ret;
    }
    TrxUndoLogMemObjInit(undoCtx, rseg, &undoArgs, undo);
    return STATUS_OK_INTER;
}

static StatusInter TrxUndoCreateWithSegPage(
    SeUndoCtxT *undoCtx, TrxRsegT *rseg, TrxUndoLogArgsT undoArgs, PageIdT segAddr, TrxUndoLogT **undo)
{
    *undo = TrxUndoLogMemObjCreate(undoCtx);
    if (*undo == NULL) {
        // maybe out of memory
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "(UNDO) TUC: create undo log");
        return OUT_OF_MEMORY_INTER;
    }
    // read the rseg header page
    uint32_t id = 0;
    StatusInter ret = TrxUndoSegAlloc(undoCtx, rseg, false, &segAddr, &id);
    if (ret != STATUS_OK_INTER) {
        // maybe out of memory
        SE_ERROR(ret, "(UNDO) TUC WithSegPage: create undo segment");
        TrxUndoLogMemObjFree(undoCtx, *undo);
        *undo = NULL;
        return ret;
    }

    ret = TrxUndoSegInit(undoCtx, id, segAddr, &undoArgs);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "(UNDO) TUC WithSegPage: init undo segment");
        TrxUndoLogMemObjFree(undoCtx, *undo);
        *undo = NULL;
        return ret;
    }

    TrxUndoLogMemObjInit(undoCtx, rseg, &undoArgs, undo);
    return STATUS_OK_INTER;
}

StatusInter TrxUndoFree(SeUndoCtxT *undoCtx, const TrxRsegT *rseg, TrxUndoLogT *undo)
{
    if (undo->state != TRX_UNDO_TO_FREE) {
        SE_LAST_ERROR(OBJECT_NOT_IN_PREREQUISITE_STATE_INTER, "inv undo state: %" PRIu32, undo->state);
        return OBJECT_NOT_IN_PREREQUISITE_STATE_INTER;
    }
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    StatusInter ret = TrxUndoSegFree(undoCtx, rseg, undo->segHdrAddr, undo->id);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "TrxUndoFree end redo log. (deviceId: %" PRIu32 ", blockId: %" PRIu32 ")",
            undo->segHdrAddr.deviceId, undo->segHdrAddr.blockId);
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    ret = RedoLogEnd(redoCtx, true);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    // free the undo memory object
    TrxUndoLogMemObjFree(undoCtx, undo);
    DecrUndoSpacePageCnt(undoCtx, 1u);
    return STATUS_OK_INTER;
}

static StatusInter TrxUndoAllocUndoSegment(SeUndoCtxT *undoCtx, TrxT *trx, UndoTypeE type)
{
    DB_POINTER2(undoCtx, trx);
    // 复用UndoCache
    StatusInter ret = TrxUndoTryReuseCached(undoCtx, trx, type);
    if (ret == STATUS_OK_INTER) {
        return ret;
    }

    // 新创建undo
    TrxUndoLogT *undo = NULL;
    TrxUndoLogArgsT undoArgs = {0};
    undoArgs.type = type;
    undoArgs.trxId = trx->base.trxId;
    undoArgs.isRetainedCache = false;
    ret = TrxUndoCreate(undoCtx, trx->trx.base.rseg, undoArgs, &undo);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    if (type == TRX_UNDO_NORMAL) {
        trx->trx.base.normalUndo = undo;
    } else if (type == TRX_UNDO_RETAINED) {
        trx->trx.base.retainedUndo = undo;
    }
    return STATUS_OK_INTER;
}

StatusInter UndoCreateRetainedCache(SeUndoCtxT *undoCtx, TrxRsegT *rseg, PageIdT segAddr)
{
    TrxUndoLogT *undo = NULL;
    // 创建UndoLog的参数，为缓存undo页时，不创建UndoLog，将对应的值设置为无效
    TrxUndoLogArgsT undoArgs = {0};
    undoArgs.type = TRX_UNDO_RETAINED;
    undoArgs.trxId = 0lu;
    undoArgs.isRetainedCache = true;
    StatusInter ret = STATUS_OK_INTER;
#ifdef FEATURE_SIMPLEREL
    undoCtx->isUndoAllocDefault = true;
#endif
    if (DbIsPageIdEqual(segAddr, SE_INVALID_PAGE_ADDR)) {
        ret = TrxUndoCreate(undoCtx, rseg, undoArgs, &undo);
    } else {
        ret = TrxUndoCreateWithSegPage(undoCtx, rseg, undoArgs, segAddr, &undo);
    }
#ifdef FEATURE_SIMPLEREL
    undoCtx->isUndoAllocDefault = false;
#endif
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "(UNDO) UndoCreateRetainedCache: Create retained undoLog cache");
        // DML流程中内存不足以创建一个新缓存页时，返回OK保障本次记录undo操作执行成功，同时记录WARN级别日志用于说明
        if (DbIsInsufficientMemoryErr(ret) && DbIsPageIdEqual(segAddr, SE_INVALID_PAGE_ADDR)) {
            DB_LOG_WARN(GMERR_OK, "(UNDO) INFO: UndoCreateRetainedCache: Return OK without cache");
            return STATUS_OK_INTER;
        }
        return ret;
    }
    undo->state = TRX_UNDO_CACHED;
    DbSpinLock(&rseg->lock);
    UndoListPushFront(&rseg->retainedUndoCached, undo);
    DbSpinUnlock(&rseg->lock);

    return STATUS_OK_INTER;
}

StatusInter AssignUndoIfNeeded(SeUndoCtxT *undoCtx, TrxT *trx, UndoTypeE undoType, TrxUndoLogT **undoOut)
{
    DB_POINTER3(undoCtx, trx, undoOut);

    if (undoType == TRX_UNDO_NORMAL && trx->trx.base.normalUndo != NULL) {
        *undoOut = trx->trx.base.normalUndo;
        return STATUS_OK_INTER;
    }
    if (undoType == TRX_UNDO_RETAINED && trx->trx.base.retainedUndo != NULL) {
        *undoOut = trx->trx.base.retainedUndo;
        return STATUS_OK_INTER;
    }

    StatusInter ret = TrxUndoAllocUndoSegment(undoCtx, trx, undoType);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(
            ret, "assign normal undo, trxId: %" PRIu64 ", undoType: %" PRId8 "", trx->base.trxId, (int8_t)undoType);
        return ret;
    }

    // 申请undo段的时候，如果retainedUndoCached为空，就给retainedUndoCached挂一个页
    if (trx->trx.base.rseg->retainedUndoCached == NULL && TrxGetIsolationLevel(trx) >= REPEATABLE_READ) {
        ret = UndoCreateRetainedCache(undoCtx, trx->trx.base.rseg, SE_INVALID_PAGE_ADDR);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret,
                "Undo create retained cache when alloc undo segment, trxId: %" PRIu64 ", undoType: %" PRId8 "",
                trx->base.trxId, (int8_t)undoType);
            return ret;
        }
    }

    *undoOut = undoType == TRX_UNDO_NORMAL ? trx->trx.base.normalUndo : trx->trx.base.retainedUndo;
    return STATUS_OK_INTER;
}

StatusInter UndoPreAlloc(SeRunCtxT *seRunCtx, UndoTypeE undoType)
{
    SeUndoCtxT *undoCtx = (SeUndoCtxT *)seRunCtx->undoCtx;
    TrxT *trx = (TrxT *)seRunCtx->trx;
    TrxUndoLogT *undo = NULL;
    return AssignUndoIfNeeded(undoCtx, trx, undoType, &undo);
}

uint64_t UndoGetDefaultRollPtr(const TrxT *trx)
{
    if (TrxIsLiteTrx(trx)) {
        // 轻量化事务不支持savePoint
        return UNDO_INVALID_ROLLPTR;
    }
    return *(uint64_t *)&(UndoRecPtrT){
        .magicNum = UNDO_SAVEPOINT_MAGIC,
        .savePointId = trx->trx.base.savePointId,
    };
}

bool UndoCheckRollPtrMagicNum(uint64_t rowRollPtr)
{
    if (rowRollPtr == UNDO_INVALID_ROLLPTR) {
        // 轻量化事务不支持savePoint, 注意此处不能校验TrxIsLiteTrx，轻量化事务的后台缩容线程不满足此条件
        return true;
    }
    // 如果rowRollPtr等于全F也是符合预期的，同时也满足魔术字magic == UNDO_SAVEPOINT_MAGIC
    UndoRecPtrT *undoRecPtr = (UndoRecPtrT *)&rowRollPtr;
    return (undoRecPtr->magicNum == UNDO_SAVEPOINT_MAGIC);
}

StatusInter UndoFetchRecordReadOnly(PageMgrT *pageMgr, uint64_t recAddr, UndoPageHeaderT **pageHdr, uint8_t **rec)
{
    PageIdT pageAddr = UndoFetchRecordPageAddr(pageMgr, recAddr);
    if (!DbIsPageIdValid(pageAddr)) {
        *rec = NULL;
        if (*pageHdr != NULL) {
            SeLeavePage(pageMgr, (*pageHdr)->baseHead.addr, false);
            *pageHdr = NULL;
        }
        SE_ERROR(INTERNAL_ERROR_INTER, "undo fetch record with inv pageId");
        return INTERNAL_ERROR_INTER;
    }
    // 需要的记录并不在这个页上，重新获取页
    if (*pageHdr != NULL && !DbIsPageIdEqual((*pageHdr)->baseHead.addr, pageAddr)) {
        SeLeavePage(pageMgr, (*pageHdr)->baseHead.addr, false);
        *pageHdr = NULL;
    }
    if (*pageHdr == NULL) {
        StatusInter ret = SeGetPage(pageMgr, pageAddr, (uint8_t **)pageHdr, ENTER_PAGE_NORMAL, false);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "undo fetch record, rollPtr: %" PRIu64, recAddr);
            *rec = NULL;
            return ret;
        }
    }
    *rec = UndoGetLogRec(*pageHdr, ((UndoRecPtrT *)&recAddr)->offset);
    return STATUS_OK_INTER;
}

StatusInter UndoFetchRecordWrite(PageMgrT *pageMgr, uint64_t recAddr, UndoPageHeaderT **pageHdr, uint8_t **rec)
{
    PageIdT pageAddr = UndoFetchRecordPageAddr(pageMgr, recAddr);
    if (!DbIsPageIdValid(pageAddr)) {
        *rec = NULL;
        return INTERNAL_ERROR_INTER;
    }
    StatusInter ret = SeGetPage(pageMgr, pageAddr, (uint8_t **)pageHdr, ENTER_PAGE_NORMAL, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "undo fetch record, rollPtr: %" PRIu64, recAddr);
        return ret;
    }
    *rec = UndoGetLogRec(*pageHdr, ((UndoRecPtrT *)&recAddr)->offset);
    return STATUS_OK_INTER;
}

// 找到本事务记录的undo里的savePointId与undo的类型
StatusInter UndoGetPrevVersionUndoLogTypeAndSavePointId(
    const SeUndoCtxT *undoCtx, uint64_t nextRollPtr, bool *isInsertUndoLogType, uint32_t *savePointId)
{
    DB_POINTER2(undoCtx, savePointId);
    UndoRecPtrT *savePointInfo = (UndoRecPtrT *)(void *)&nextRollPtr;
    if (savePointInfo->magicNum == UNDO_SAVEPOINT_MAGIC) {
        // 魔术字符合，表示没有下一条undoLog, 是insert类型的undo
        *isInsertUndoLogType = true;
        *savePointId = savePointInfo->savePointId;
        return STATUS_OK_INTER;
    } else {
        *isInsertUndoLogType = false;
        UndoPageHeaderT *undoPage = NULL;
        UndoModifyRecHdrT *curRec = NULL;
        StatusInter ret = UndoFetchRecordReadOnly(undoCtx->pageMgr, nextRollPtr, &undoPage, (uint8_t **)&curRec);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "fetch get prev version, rollPtr: %" PRIu64, nextRollPtr);
            return ret;
        }
        if (curRec == NULL) {
            DB_ASSERT(curRec != NULL);  // 看护作用，nextRollPtr为有效值，理论上curRec不为空，debug辅助发现问题
            SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "fetch get prev version, rollPtr: %" PRIu64, nextRollPtr);
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        *savePointId = curRec->savePointId;
        SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
        return STATUS_OK_INTER;
    }
}

void UndoReadRecordTuple(const SeUndoCtxT *undoCtx, UndoRecBaseHdrT *srcRec, UndoTupleInfoT *undoTuple);

static void UndoReadRecordPrtUpdTuple(
    const SeUndoCtxT *undoCtx, UndoRecBaseHdrT *srcRec, UndoTupleCombineAddrT *undoTuple)
{
    DB_POINTER3(undoCtx, srcRec, undoTuple);
    UndoPrtUpdHeapRecHdrT *updPrtRec = (UndoPrtUpdHeapRecHdrT *)(void *)srcRec;
    undoTuple->isTargetPrtUpdUndo = true;
    undoTuple->partDataLen = updPrtRec->updRec.bufSize;
    undoTuple->offsetOfRawData = updPrtRec->offsetOfRawData;
    undoTuple->partData = ((uint8_t *)(updPrtRec + 1)) + updPrtRec->srcRowHeadSize;
    DB_ASSERT(((HpNormalRowHeadT *)(updPrtRec + 1))->magicNum == HEAP_ROW_MAGIC_NUM);
}

StatusInter RowUndoGetUnlinkTargetRecord(const SeUndoCtxT *undoCtx, uint64_t masterRollPtr,
    const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr, UndoTupleCombineAddrT *undoTuple)
{
    DB_POINTER(rowOpInfo);
    // traverse the version chain from the next(old) version of the master version
    UndoPageHeaderT *undoPage = NULL;
    UndoModifyRecHdrT *curRec = NULL;
    uint64_t tarRollPtr = masterRollPtr;
    StatusInter ret = UndoFetchRecordReadOnly(undoCtx->pageMgr, masterRollPtr, &undoPage, (uint8_t **)&curRec);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    while (curRec != NULL && !RowUndoIsValidRollPtr(curRec->rollPtr)) {
        DB_ASSERT(curRec->base.labelId == rowOpInfo->labelId);
        DB_ASSERT(curRec->rowid == rowOpInfo->rowId);
        uint64_t preRollPtr = curRec->rollPtr;

        if (curRec->rollPtr == targetRollPtr) {
            SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
            break;
        }
        // fetch next tuple in the version chain
        tarRollPtr = curRec->rollPtr;
        ret = UndoFetchRecordReadOnly(undoCtx->pageMgr, tarRollPtr, &undoPage, (uint8_t **)&curRec);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "find next tuple in version chain");
            return ret;
        }
        if (curRec == NULL) {
            SE_ERROR(INTERNAL_ERROR_INTER, "find next tuple in version chain");
            return INTERNAL_ERROR_INTER;
        }
        // 理论上获取的下一条，不应该跟上一条的rollptr相同，否则会陷入死循环
        DB_ASSERT(preRollPtr != curRec->rollPtr);
    }
    // 这时候已经找到了需要unlink的record，需要重新用write的方式get这个页
    DB_ASSERT(tarRollPtr != UNDO_INVALID_ROLLPTR);
    ret = UndoFetchRecordWrite(undoCtx->pageMgr, tarRollPtr, &undoPage, (uint8_t **)&curRec);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "curRec is inv");
        return ret;
    }
    // unlink the undo record from the version chain
    curRec->rollPtr = rowOpInfo->rowRollPtr;
    size_t offset = (size_t)((uintptr_t)curRec - (uintptr_t)undoPage) + offsetof(UndoModifyRecHdrT, rollPtr);
    // write redo when unlink the redo record.
    UndoWriteGeneralRedoLog(SeGetCurRedoCtx(), (uint8_t *)undoPage, offset, sizeof(uint64_t));
    DB_ASSERT(rowOpInfo->opType == TRX_OP_UPDATE || rowOpInfo->opType == TRX_OP_UPDATE_PART ||
              rowOpInfo->opType == TRX_OP_DELETE);
    UndoReadRecordTuple(undoCtx, &curRec->base, &undoTuple->tuple);
    undoTuple->pageHead = &undoPage->baseHead;
    return STATUS_OK_INTER;
}

StatusInter RowUndoFetchHpBufFromTargetRecord(SeUndoCtxT *undoCtx, uint64_t masterRollPtr,
    const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr, UndoTupleCombineAddrT *undoTuple)
{
    DB_POINTER3(undoCtx, rowOpInfo, undoTuple);
    // traverse the version chain from the next(old) version of the master version
    UndoPageHeaderT *undoPage = NULL;
    UndoModifyRecHdrT *curRec = NULL;
    StatusInter ret = UndoFetchRecordReadOnly(undoCtx->pageMgr, masterRollPtr, &undoPage, (uint8_t **)&curRec);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "fetch undo rec");
        return ret;
    }
    while (curRec != NULL && !RowUndoIsValidRollPtr(curRec->rollPtr)) {
        DB_ASSERT(curRec->base.labelId == rowOpInfo->labelId);
        DB_ASSERT(curRec->rowid == rowOpInfo->rowId);

        if (curRec->rollPtr == targetRollPtr) {
            // unlink the undo record from the version chain
            DB_ASSERT(rowOpInfo->opType == TRX_OP_UPDATE);  // 上层保证了只能update流程才能走进该函数，防止误用
            UndoReadRecordTuple(undoCtx, &curRec->base, &undoTuple->tuple);
            undoTuple->pageHead = &undoPage->baseHead;
            break;
        }
        // fetch next tuple in the version chain
        ret = UndoFetchRecordReadOnly(undoCtx->pageMgr, curRec->rollPtr, &undoPage, (uint8_t **)&curRec);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "fetch undo rec");
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

StatusInter RowUndoSetRecycled(SeUndoCtxT *undoCtx, uint64_t targetRollPtr)
{
    // 已经完成unlink了，不加heap的页latch，直接访问这个rec也不会有并发了
    UndoPageHeaderT *undoPage = NULL;
    UndoModifyRecHdrT *curRec = NULL;
    StatusInter ret = UndoFetchRecordWrite(undoCtx->pageMgr, targetRollPtr, &undoPage, (uint8_t **)&curRec);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "curRec is inv.");
        return ret;
    }
    DB_ASSERT(curRec != NULL);
    curRec->isRecycled = true;
    size_t offset = (size_t)((uintptr_t)curRec - (uintptr_t)undoPage) + offsetof(UndoModifyRecHdrT, isRecycled);
    UndoWriteGeneralRedoLog(SeGetCurRedoCtx(), (uint8_t *)undoPage, offset, sizeof(bool));
    SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, true);
    return STATUS_OK_INTER;
}

StatusInter RowUndoGetLastVersionFromStartRollPtr(SeUndoCtxT *undoCtx, uint64_t startRollPtr, uint64_t *targetRollPtr,
    UndoRowOpInfoT *targetRowInfo, UndoPageHeaderT **undoPage)
{
    // 获取从startRollPtr开始的这条版本链的最后一个版本的addr和内容(有可能startRollPtr就是最后一个版本)
    UndoPageHeaderT *cursor = NULL;
    UndoModifyRecHdrT *curRec = NULL;
    *targetRollPtr = startRollPtr;
    StatusInter ret = UndoFetchRecordReadOnly(undoCtx->pageMgr, startRollPtr, &cursor, (uint8_t **)&curRec);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    while (curRec != NULL && !RowUndoIsValidRollPtr(curRec->rollPtr)) {
        *targetRollPtr = curRec->rollPtr;
        // fetch next tuple in the version chain
        ret = UndoFetchRecordReadOnly(undoCtx->pageMgr, curRec->rollPtr, &cursor, (uint8_t **)&curRec);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        DB_ASSERT(curRec != NULL);
    }
    ret = UndoReadRowOpInfoByRecBuf(undoCtx, &curRec->base, targetRowInfo);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "undo readInfo from record buf");
        SeLeavePage(undoCtx->pageMgr, cursor->baseHead.addr, false);
    } else {
        *undoPage = cursor;
    }
    return ret;
}

StatusInter RowUndoGetNextVersionFromStartRollPtr(
    SeUndoCtxT *undoCtx, uint64_t startRollPtr, uint64_t *targetRollPtr, bool *isRecycleFinish)
{
    // 获取从startRollPtr开始的下一个版本的addr和内容
    UndoPageHeaderT *undoPage = NULL;
    UndoModifyRecHdrT *curRec = NULL;
    StatusInter ret = UndoFetchRecordReadOnly(undoCtx->pageMgr, startRollPtr, &undoPage, (uint8_t **)&curRec);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    if (curRec != NULL && !RowUndoIsValidRollPtr(curRec->rollPtr)) {
        *targetRollPtr = curRec->rollPtr;
    }
    // 等于则表示没有下一条了
    *isRecycleFinish = (*targetRollPtr == startRollPtr);
    SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
    return STATUS_OK_INTER;
}

StatusInter RowUndoFetchFromTargetRecordWithTargetTrxIds(
    SeUndoCtxT *undoCtx, uint64_t masterRollPtr, TrxIdListT *targetTrxIds, uint64_t *targetRollPtr)
{
    DB_POINTER3(undoCtx, targetTrxIds, targetRollPtr);
    // traverse the version chain from the next(old) version of the master version
    UndoPageHeaderT *undoPage = NULL;
    UndoModifyRecHdrT *curRec = NULL;
    StatusInter ret = UndoFetchRecordReadOnly(undoCtx->pageMgr, masterRollPtr, &undoPage, (uint8_t **)&curRec);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    while (curRec != NULL && !RowUndoIsValidRollPtr(curRec->rollPtr)) {
        if (TrxIdListLookup(targetTrxIds, curRec->trxId) && !curRec->isMarkDeleted) {  // 更新的undoRec才能批量回收
            *targetRollPtr = curRec->rollPtr;
            break;
        }
        // fetch next tuple in the version chain
        ret = UndoFetchRecordReadOnly(undoCtx->pageMgr, curRec->rollPtr, &undoPage, (uint8_t **)&curRec);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
    return ret;
}

StatusInter RowUndoFetchRowOpInfo(SeUndoCtxT *undoCtx, uint64_t masterRollPtr, uint64_t *targetRollPtr)
{
    DB_POINTER(undoCtx);
    // traverse the version chain from the next(old) version of the master version
    UndoPageHeaderT *undoPage = NULL;
    UndoModifyRecHdrT *curRec = NULL;
    StatusInter ret = UndoFetchRecordReadOnly(undoCtx->pageMgr, masterRollPtr, &undoPage, (uint8_t **)&curRec);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    if (curRec == NULL) {
        DB_ASSERT(false);  // 内部维护错了才会有这种问题
        SE_ERROR(INTERNAL_ERROR_INTER, "get record buf, masterRollPtr:%" PRIu64, masterRollPtr);
        return INTERNAL_ERROR_INTER;
    }
    DB_ASSERT(curRec->base.opType == TRX_OP_UPDATE);
    *targetRollPtr = masterRollPtr;
    SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
    return STATUS_OK_INTER;
}

bool RowUndoCheckSavePointId(
    SeUndoCtxT *undoCtx, uint64_t masterRollPtr, uint32_t targetSavePointId, bool *isByPassToInsertUndo)
{
    DB_POINTER(undoCtx);
    UndoRecPtrT *savePointInfo = (UndoRecPtrT *)&masterRollPtr;
    if (savePointInfo->magicNum == UNDO_SAVEPOINT_MAGIC) {
        if (isByPassToInsertUndo != NULL) {
            *isByPassToInsertUndo = true;
        }
        return savePointInfo->savePointId == targetSavePointId;
    }
    uint32_t savePointId = DB_INVALID_UINT32;
    UndoPageHeaderT *undoPage = NULL;
    UndoModifyRecHdrT *curRec = NULL;
    (void)UndoFetchRecordReadOnly(undoCtx->pageMgr, masterRollPtr, &undoPage, (uint8_t **)&curRec);
    if (curRec != NULL) {
        savePointId = curRec->savePointId;
    }
    SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
    return savePointId == targetSavePointId;
}

// 返回值含义
// 1. STATUS_OK_INTER ：可见
// 2. NO_DATA_INTER ：不可见
// 3. MEMORY_OPERATE_FAILED_INTER ：activeTrxIdsShm共享内存转换错误
StatusInter RowUndoIsUndoRecVisible(
    SeUndoCtxT *undoCtx, const ReadViewT *readView, UndoModifyRecHdrT *rec, bool isGetOldestVisibleVersion)
{
    DB_POINTER3(undoCtx, undoCtx->seCtx->trxMgr, rec);
    // 情况1：readView是空, 表示此时需要访问最新的已提交的版本，未提交版本按照不可见处理
    // 情况2：isGetOldestVisibleVersion为true，此时目的是找到可见版本中，非本事务编辑的第一条UndoRec；
    // 因此本事务编辑的UndoRec直接按照不可见处理，当前只有RR+乐观场景会走到这里
    if (readView == NULL) {
        return TrxMgrIsRwTrxActiveWithSessionLock(undoCtx->seCtx->trxMgr, undoCtx->seCtx, rec->trxId) ? NO_DATA_INTER :
                                                                                                        STATUS_OK_INTER;
    } else if (isGetOldestVisibleVersion && rec->trxId == readView->creatorTrxId) {
        return NO_DATA_INTER;
    }
    // 剩余情况，正常校验可见性即可
    StatusInter ret = ReadViewIsTrxVisible(readView, rec->trxId);
    if (SECUREC_UNLIKELY(ret == MEMORY_OPERATE_FAILED_INTER)) {
        SE_LAST_ERROR(ret,
            "RowUndoIsUndoRecVisible: activeTrxIdsShm transfer, rec trx:(%" PRIu64 "), label:(%" PRIu32 ")", rec->trxId,
            rec->base.labelId);
        return ret;
    }
    return ret;
}

static StatusInter RowUndoGetVisibleTuplePage(
    SeUndoCtxT *undoCtx, UndoRecPtrT *recPtr, UndoPageHeaderT **undoPage, bool isGetOldestVisibleVersion)
{
    PageIdT pageAddr = DeserializePageId(undoCtx->pageMgr, recPtr->pageId);
    UndoPageHeaderT *targetPage = NULL;
    StatusInter ret = SeGetPage(undoCtx->pageMgr, pageAddr, (uint8_t **)&targetPage, ENTER_PAGE_NORMAL, false);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "(UNDO) RUGVT: undo page from memdata(deviceId: %" PRIu32 ", blockId: %" PRIu32 ")",
            pageAddr.deviceId, pageAddr.blockId);
        return ret;
    }

    if (targetPage->magic != UNDO_PAGE_HEADER_MAGIC) {
        SE_LAST_ERROR(INTERNAL_ERROR_INTER,
            "undoPage.magic inv, magicNum: %" PRIu32 ", isGetOldestVisibleVersion: %" PRIu8, targetPage->magic,
            (uint8_t)isGetOldestVisibleVersion);
        DB_ASSERT(false);
        SeLeavePage(undoCtx->pageMgr, targetPage->baseHead.addr, false);
        return INTERNAL_ERROR_INTER;
    }
    *undoPage = targetPage;
    return STATUS_OK_INTER;
}

StatusInter RowUndoGetVisibleTuple(SeUndoCtxT *undoCtx, const ReadViewT *readView, uint64_t rollPtr,
    UndoTupleCombineAddrT *undoTuple, bool isGetOldestVisibleVersion)
{
    DB_POINTER4(undoCtx, undoCtx->seCtx, undoCtx->undoSpace, undoTuple);
    *undoTuple = (UndoTupleCombineAddrT){0};
    uint64_t rollPtrValue = rollPtr;
    UndoModifyRecHdrT *preRec = NULL;
    UndoPageHeaderT *undoPage = NULL;
    // if the master version is not visible, we will call this func to get a visible version
    while (!RowUndoIsValidRollPtr(rollPtrValue)) {
        UndoRecPtrT *recPtr = (UndoRecPtrT *)&rollPtrValue;
        StatusInter ret = RowUndoGetVisibleTuplePage(undoCtx, recPtr, &undoPage, isGetOldestVisibleVersion);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }

        // 悲观事务下，读版本链前master version所在页latch加读锁
        // 1. 可以确保版本链没有新版本写入。
        // 2. 回滚修改版本链信息也需要先对页加写锁
        // 乐观事务下，读版本链前master version所在页latch加读锁
        // 1. 可以确保版本链没有新版本写入。
        // 2. 回滚该记录的事务页需要加页写锁。
        // 3. 回滚一条记录导致该记录更新，同样需要对所在数据页先加写锁
        // 综上所述，遍历版本链时可以不加锁控制并发
        UndoModifyRecHdrT *rec = (UndoModifyRecHdrT *)UndoGetLogRec(undoPage, (uint32_t)recPtr->offset);
        if (rec->base.resType != (uint8_t)(TRX_RES_HEAP) && rec->base.resType != (uint8_t)(TRX_RES_TOPO)) {
            SE_ERROR(INTERNAL_ERROR_INTER, "unexpected undo resType: %" PRIu8 ", isGetOldestVisibleVersion: %" PRIu8,
                rec->base.resType, (uint8_t)isGetOldestVisibleVersion);
            SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
            return INTERNAL_ERROR_INTER;
        }
        // 当前undo记录不可见，就需要continue来找下一条
        ret = RowUndoIsUndoRecVisible(undoCtx, readView, rec, isGetOldestVisibleVersion);
        if (SECUREC_UNLIKELY(ret == MEMORY_OPERATE_FAILED_INTER)) {
            SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
            return ret;
        } else if (ret == NO_DATA_INTER) {
            preRec = rec;
            rollPtrValue = rec->rollPtr;
            SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
            continue;
        }
        if (rec->base.opType == (uint8_t)(TRX_OP_UPDATE_PART)) {
            UndoReadRecordPrtUpdTuple(undoCtx, &rec->base, undoTuple);
            // 需要读取前一个undoLog中的内容(如果有)
            if (preRec == NULL) {
                // 部分更新不支持持久化，不考虑SeLeavePage, 直接返回
                undoTuple->isNextToMaster = true;
                return STATUS_OK_INTER;
            } else {
                // 不可能有两个部分更新的undoLog相邻，前一个undoLog继续往下读取内容
                rec = preRec;
            }
        } else if (rec->base.opType != (uint8_t)(TRX_OP_DELETE) && rec->base.opType != (uint8_t)(TRX_OP_UPDATE)) {
            SE_ERROR(INTERNAL_ERROR_INTER, "unexpected undo opType: %" PRIu8 ", isGetOldestVisibleVersion: %" PRIu8,
                rec->base.opType, (uint8_t)isGetOldestVisibleVersion);
            SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
            return INTERNAL_ERROR_INTER;
        }
        // 读取对应undo record的实际内容
        UndoReadRecordTuple(undoCtx, &rec->base, &undoTuple->tuple);
        undoTuple->pageHead = &undoPage->baseHead;
        break;
    }
    return STATUS_OK_INTER;
}

static inline void UndoGetNextRec(UndoModifyRecHdrT *rec, SeUndoCtxT *undoCtx, UndoPageHeaderT *undoPage,
    UndoModifyRecHdrT **preRec, uint64_t *rollPtrValue)
{
    (*preRec) = rec;
    *rollPtrValue = rec->rollPtr;
    SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
}

StatusInter UndoGetMergeableRec(
    SeUndoCtxT *undoCtx, const ReadViewT *readView, uint64_t rollPtrValue, UndoTupleCombineAddrT *undoTuple)
{
    DB_POINTER4(undoCtx, undoCtx->seCtx, undoCtx->undoSpace, undoTuple);
    UndoModifyRecHdrT *preRec = NULL;
    UndoPageHeaderT *undoPage = NULL;
    // if the master version is not visible, we will call this func to get a visible version
    while (!RowUndoIsValidRollPtr(rollPtrValue)) {
        UndoRecPtrT *recPtr = (UndoRecPtrT *)&rollPtrValue;
        StatusInter ret = RowUndoGetVisibleTuplePage(undoCtx, recPtr, &undoPage, false);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }

        // 悲观事务下，读版本链前master version所在页latch加读锁
        // 1. 可以确保版本链没有新版本写入。
        // 2. 回滚修改版本链信息也需要先对页加写锁
        // 乐观事务下，读版本链前master version所在页latch加读锁
        // 1. 可以确保版本链没有新版本写入。
        // 2. 回滚该记录的事务页需要加页写锁。
        // 3. 回滚一条记录导致该记录更新，同样需要对所在数据页先加写锁
        // 综上所述，遍历版本链时可以不加锁控制并发
        UndoModifyRecHdrT *rec = (UndoModifyRecHdrT *)UndoGetLogRec(undoPage, (uint32_t)recPtr->offset);
        DB_ASSERT(rec->base.resType == (uint8_t)(TRX_RES_HEAP) || rec->base.resType == (uint8_t)(TRX_RES_TOPO));
        // 当前undo记录不可见，就需要continue来找下一条
        ret = RowUndoIsUndoRecVisible(undoCtx, readView, rec, false);
        if (SECUREC_UNLIKELY(ret == MEMORY_OPERATE_FAILED_INTER)) {
            SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
            return ret;
        } else if (ret == NO_DATA_INTER) {
            UndoGetNextRec(rec, undoCtx, undoPage, &preRec, &rollPtrValue);
            continue;
        }

        if (rec->trxId != ((TrxT *)undoCtx->seCtx->trx)->base.trxId) {  // 遍历到更新前的版本则说明没有可优化的undo rec
            SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
            return STATUS_OK_INTER;
        }
        if (rec->base.opType != (uint8_t)(TRX_OP_DELETE) && rec->base.opType != (uint8_t)(TRX_OP_UPDATE)) {
            SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
            return INTERNAL_ERROR_INTER;
        }
        bool isInsertUndoLogType = false;
        uint32_t savePointId = 0;
        // 从前一版本中获取该undo对应的save point
        UndoGetPrevVersionUndoLogTypeAndSavePointId(undoCtx, rec->rollPtr, &isInsertUndoLogType, &savePointId);
        if (isInsertUndoLogType) {  // heap数据是本事务插入的,该undo rec不可合并
            SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
            break;
        }
        if (SeTrxFindSavepointById(undoCtx->seCtx, savePointId)) {  // savepoint 存在就继续查找下一个
            UndoGetNextRec(rec, undoCtx, undoPage, &preRec, &rollPtrValue);
            continue;
        }

        // 找到一个可被优化的undo record, 读取对应undo record的实际内容
        UndoReadRecordTuple(undoCtx, &rec->base, &undoTuple->tuple);
        undoTuple->pageHead = &undoPage->baseHead;
        break;
    }
    return STATUS_OK_INTER;
}

void RowUndoFindTarget(SeUndoCtxT *undoCtx, uint64_t rollPtr, uint64_t targetRollPtr, UndoTupleCombineAddrT *undoTuple)
{
    DB_POINTER4(undoCtx, undoCtx->seCtx, undoCtx->undoSpace, undoTuple);
    StatusInter ret = STATUS_OK_INTER;
    UndoPageHeaderT *undoPage = NULL;
    uint64_t loopRollPtr = rollPtr;
    while (!RowUndoIsValidRollPtr(loopRollPtr)) {
        UndoRecPtrT *recPtr = (UndoRecPtrT *)&loopRollPtr;
        ret = RowUndoGetVisibleTuplePage(undoCtx, recPtr, &undoPage, false);
        if (ret != STATUS_OK_INTER) {
            return;
        }
        UndoModifyRecHdrT *rec = (UndoModifyRecHdrT *)UndoGetLogRec(undoPage, (uint32_t)recPtr->offset);
        UndoUpdHeapRecHdrT *updRec = (UndoUpdHeapRecHdrT *)(void *)&rec->base;
        if (targetRollPtr == rec->rollPtr) {
            undoTuple->tuple.data = (uint8_t *)(updRec + 1);
            SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
            return;
        }
        loopRollPtr = rec->rollPtr;
        SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
    }
}

void FixRowUndoGetTargetPtr(SeUndoCtxT *undoCtx, uint64_t rollPtr, void **data)
{
    DB_POINTER3(undoCtx, undoCtx->seCtx, undoCtx->undoSpace);
    uint64_t rollPtrValue = rollPtr;
    UndoPageHeaderT *undoPage = NULL;
    if (!RowUndoIsValidRollPtr(rollPtrValue)) {
        UndoRecPtrT *recPtr = (UndoRecPtrT *)&rollPtrValue;
        StatusInter ret = RowUndoGetVisibleTuplePage(undoCtx, recPtr, &undoPage, false);
        if (ret != STATUS_OK_INTER) {
            return;
        }
        UndoModifyRecHdrT *rec = (UndoModifyRecHdrT *)UndoGetLogRec(undoPage, (uint32_t)recPtr->offset);
        UndoUpdHeapRecHdrT *updRec = (UndoUpdHeapRecHdrT *)(void *)&rec->base;
        *data = (uint8_t *)(updRec + 1) + sizeof(HpNormalFixRowHead);
        SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
    }
}

void ValRowUndoGetTargetPtr(SeUndoCtxT *undoCtx, uint64_t rollPtr, void **data)
{
    DB_POINTER3(undoCtx, undoCtx->seCtx, undoCtx->undoSpace);
    uint64_t rollPtrValue = rollPtr;
    UndoPageHeaderT *undoPage = NULL;
    if (!RowUndoIsValidRollPtr(rollPtrValue)) {
        UndoRecPtrT *recPtr = (UndoRecPtrT *)&rollPtrValue;
        StatusInter ret = RowUndoGetVisibleTuplePage(undoCtx, recPtr, &undoPage, false);
        if (ret != STATUS_OK_INTER) {
            return;
        }
        UndoModifyRecHdrT *rec = (UndoModifyRecHdrT *)UndoGetLogRec(undoPage, (uint32_t)recPtr->offset);
        UndoUpdHeapRecHdrT *updRec = (UndoUpdHeapRecHdrT *)(void *)&rec->base;
        *data = (uint8_t *)(updRec + 1);
        SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
    }
}

static StatusInter TrxUndoAllocNewUndoPage(SeUndoCtxT *undoCtx, UndoPageHeaderT *hdrPage, TrxUndoLogT *undo)
{
    PageIdT newPageAddr = SE_INVALID_PAGE_ADDR;
    AllocPageParamT allocPageParam = {.spaceId = undoCtx->undoSpace->meta.undoSpaceId,
        .trmId = undoCtx->undoSpace->meta.undoFileId,
        .labelId = RSM_INVALID_LABEL_ID,
        .dbInstance = DbGetInstanceByMemCtx((DbMemCtxT *)(undoCtx->seCtx->sessionMemCtx)),
        .labelRsmUndo = NULL};  // UNDO不使用rsmUndo
    StatusInter ret = SeAllocPage(undoCtx->pageMgr, &allocPageParam, &newPageAddr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "undo get new page");
        return ret;
    }

    UndoPageHeaderT *newPage = NULL;
    FreePageParamT freePageParam = {.spaceId = undoCtx->undoSpace->meta.undoSpaceId,
        .addr = newPageAddr,
        .dbInstance = DbGetInstanceByMemCtx((DbMemCtxT *)undoCtx->seCtx->sessionMemCtx),
        .labelRsmUndo = NULL};
    SeInitCachePagePara(&freePageParam.cachePagePara);
    ret = SeGetPage(undoCtx->pageMgr, newPageAddr, (uint8_t **)&newPage, ENTER_PAGE_NORMAL, true);
    if (ret != STATUS_OK_INTER) {
        (void)SeFreePage(undoCtx->pageMgr, &freePageParam);
        return ret;
    }

    // init new undo page header
    ret = TrxUndoInitPage(newPage, hdrPage->pageType, hdrPage->baseHead.addr);
    if (ret != STATUS_OK_INTER) {
        SeLeavePage(undoCtx->pageMgr, newPageAddr, false);
        (void)SeFreePage(undoCtx->pageMgr, &freePageParam);
        return ret;
    }

    UndoWriteRedoLogForUndoPageInit(SeGetCurRedoCtx(), newPageAddr, hdrPage->pageType, hdrPage->baseHead.addr);

    UndoSegHeaderT *segHdr = (UndoSegHeaderT *)(hdrPage + 1);
    DB_ASSERT(segHdr->magic == UNDO_SEG_HEADER_MAGIC);
    UndoSetPagePrevNode(undoCtx, newPage, segHdr->pageList.tailPageId, segHdr->pageList.tailPage);
    UndoSetPageListTail(undoCtx, segHdr, newPage->baseHead.addr, newPage);

    // update undo meta
    UndoSetLastUndoPage(undoCtx, undo, newPageAddr, newPage);
    SeLeavePage(undoCtx->pageMgr, newPageAddr, true);
    return STATUS_OK_INTER;
}

static StatusInter TrxUndoPageAdd(SeUndoCtxT *undoCtx, TrxUndoLogT *undo)
{
    StatusInter ret = UndoPageAddCheck(undoCtx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    // get undo seg header page
    UndoPageHeaderT *hdrPage = NULL;
    ret = UndoGetSegHdrPage(undoCtx, undo, true, &hdrPage);
    if (hdrPage == NULL || ret != STATUS_OK_INTER) {
        ret = (ret == STATUS_OK_INTER) ? UNEXPECTED_NULL_VALUE_INTER : ret;
        SE_LAST_ERROR(ret, "(UNDO) TUPA: get the undo segment hdr page(deviceId %" PRIu32 ", blockId %" PRIu32 ")",
            undo->segHdrAddr.deviceId, undo->segHdrAddr.blockId);
        return ret;
    }
    ret = TrxUndoAllocNewUndoPage(undoCtx, hdrPage, undo);
    if (ret != STATUS_OK_INTER) {
        UndoLeaveSegHdrPage(undoCtx->pageMgr, undo, false);
        return ret;
    }
    UndoLeaveSegHdrPage(undoCtx->pageMgr, undo, true);
    undo->usedPageCnt++;
    IncrUndoSpacePageCnt(undoCtx);
    return ret;
}

StatusInter TrxUndoPageWriteRecord(
    SeUndoCtxT *undoCtx, UndoPageHeaderT *pageHdr, const UndoRowOpInfoT *rowOpInfo, uint32_t *offset)
{
    DB_ASSERT(rowOpInfo->resType < TRX_RES_MAX);
    uint32_t freeSize = (uint32_t)(SeGetPageTotalSize(&pageHdr->baseHead) - pageHdr->freeStartPos);
    uint32_t freePos = pageHdr->freeStartPos;
    uint8_t *ptr = ((uint8_t *)((uintptr_t)pageHdr + pageHdr->freeStartPos));
    uint32_t recSize = 0;
    // 一个page容纳一条undo日志的情况下, pageSize - SE_UNDO_DATA_IN_PAGE_RESERVED, 应该就是一个日志最大的长度
    _Static_assert(SE_UNDO_DATA_IN_PAGE_RESERVED > UNDO_PAGE_MAX_MGR_INFO_LEN,
        "undo页的管理空间保留");  // 为解耦, 前者在存储能力定义中, 没有直接引用后者的计算公式. 因此在这里通过编译约束.
    // construct an undo record
    StatusInter ret = UndoGetTrxResUndoRecordOps(rowOpInfo->resType)->recEncodeFunc(rowOpInfo, freeSize, ptr, &recSize);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        if (ret == INSUFF_RES_UNDO_PAGE_NOT_ENOUGH_SPACE) {
            *offset = 0u;
            return STATUS_OK_INTER;
        }
        SE_ERROR(ret, "write undo record (resType %" PRIu8 ", opType %" PRIu8 ")", (uint8_t)(rowOpInfo->resType),
            (uint8_t)(rowOpInfo->opType));
        return ret;
    }

    // write footer
    uint32_t *footer = (uint32_t *)((uintptr_t)ptr + recSize - UNDO_REC_FOOTER_SIZE);
    *footer = recSize;

    // update page header info
    uint32_t newFree = freePos + recSize;
    ret = DbInterProcRWLatchW(&pageHdr->baseHead.lock);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    pageHdr->undoRecCnt++;
    DbInterProcRWUnlatchW(&pageHdr->baseHead.lock);
    DB_ASSERT(pageHdr->baseHead.freeSize >= recSize);
    pageHdr->baseHead.freeSize = (uint16_t)(pageHdr->baseHead.freeSize - (uint16_t)recSize);
    pageHdr->freeStartPos = newFree;
    ret = UndoWriteRecordRedoLog(SeGetCurRedoCtx(), pageHdr, freePos, recSize);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "write undo record");
        return ret;
    }
    *offset = freePos;
    if (HeapCheckIsNormalRowByBufSize(rowOpInfo->bufSize)) {
        undoCtx->newPtr = ptr + sizeof(UndoUpdHeapRecHdrT) + sizeof(HpNormalFixRowHead);
    } else {  // v1大对象数据为link src行（HpLinkRowHeadT）
        undoCtx->newPtr = ptr + sizeof(UndoUpdHeapRecHdrT);
    }
    return STATUS_OK_INTER;
}

// 写入undo record，如果是第一条record，且seg页头放不下，需要更新recordStartPos表示第一页无数据
static void TrxUndoLogSetRecPosIfNeed(
    PageMgrT *pageMgr, RedoRunCtxT *redoCtx, TrxUndoLogT *undo, UndoPageHeaderT *undoPage)
{
    if (!DbIsPageIdEqual(undo->segHdrAddr, undoPage->baseHead.addr)) {
        return;
    }
    UndoLogHeaderT *logHdr = UndoGetLogHdr(undoPage, undo->logHdrOffset);
    DB_ASSERT((uint32_t)(logHdr->recordStartPtr.offset) + undoPage->baseHead.freeSize <= DB_MAX_UINT16);
    // 当前页的空闲开始位置等于recordStartPos即表明该页未写入任何record
    if (logHdr->recordStartPtr.offset == undoPage->freeStartPos) {
        // 走到这里时，SegPage装不下一条undolog，并申请了一个“新”页
        // 而这个新页的第一条起始位置必定是页头后的第一个位置，所以这里不再重复地把新页get出来
        // 如果修改TrxUndoInitPage中对freeStartPos的赋值，一定要记得修改这里！
        uint32_t pageId = SerializePageId(pageMgr, undo->lastPageAddr);
        (void)UndoSetPtr(&logHdr->recordStartPtr, pageId, (uint32_t)sizeof(UndoPageHeaderT));
        size_t offset = (size_t)undo->logHdrOffset + offsetof(UndoLogHeaderT, recordStartPtr);
        UndoWriteGeneralRedoLog(redoCtx, (uint8_t *)undoPage, offset, sizeof(UndoRecPtrT));
        (void)UndoSetPtr(&logHdr->unclearPos, pageId, (uint16_t)sizeof(UndoPageHeaderT));
        offset = (size_t)undo->logHdrOffset + offsetof(UndoLogHeaderT, unclearPos);
        UndoWriteGeneralRedoLog(redoCtx, (uint8_t *)undoPage, offset, sizeof(UndoRecPtrT));
    }
}

static StatusInter TrxUndoExpandLog(SeUndoCtxT *undoCtx, TrxUndoLogT *undo, UndoPageHeaderT *undoPage)
{
    // extend one undo page to the undo segment
    StatusInter ret = TrxUndoPageAdd(undoCtx, undo);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    UndoSetPageNextNode(undoCtx, undoPage, undo->lastPageAddr, undo->lastUndoPage);

    // Undo Log所在页头没有成功写入任意record，需要重新设置recordStartPos
    TrxUndoLogSetRecPosIfNeed(undoCtx->pageMgr, SeGetCurRedoCtx(), undo, undoPage);
    return STATUS_OK_INTER;
}

static StatusInter TrxUndoSetRowOperationPtr(SeUndoCtxT *undoCtx, TrxUndoLogT *undo, uint32_t offset, uint64_t *rollPtr)
{
    UndoRecPtrT recPtr = UndoRecPtrInit();
    uint32_t pageId = SerializePageId(undoCtx->pageMgr, undo->lastPageAddr);
    StatusInter ret = UndoSetPtr(&recPtr, pageId, offset);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    if (rollPtr != NULL) {
        *rollPtr = *((uint64_t *)&recPtr);
    }
    undo->rollPtr = *((uint64_t *)&recPtr);
    return STATUS_OK_INTER;
}

StatusInter TrxUndoReportRowOperation(
    SeUndoCtxT *undoCtx, TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t *rollPtr)
{
    DB_POINTER3(undoCtx, trx, rowOpInfo);
    // assign undo segment based on undo type
    UndoTypeE undoType = rowOpInfo->isRetained ? TRX_UNDO_RETAINED : TRX_UNDO_NORMAL;

    if ((rowOpInfo->rowBuf != NULL) && (rowOpInfo->rowSize > undoCtx->undoSpace->meta.maxRowSize)) {
        SE_LAST_ERROR(RESTRICT_VIOLATION_INTER,
            "(UNDO) TURRO: too large undo record. (rowSize %" PRIu32 ", resType %" PRIu32 ", opType %" PRIu32 ")",
            rowOpInfo->rowSize, (uint32_t)(rowOpInfo->resType), (uint32_t)(rowOpInfo->opType));
        return RESTRICT_VIOLATION_INTER;
    }

    // assign a undo log to trx if not assigned yet
    TrxUndoLogT *undo = NULL;
    StatusInter ret = AssignUndoIfNeeded(undoCtx, trx, undoType, &undo);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "undo is NULL");
        return ret;
    }
    uint32_t offset;
    UndoPageHeaderT *undoPage = NULL;
    do {
        // get the latest block number
        // 不论往最后一个页写log的时候成不成功，我们都会修改这个页，所以这里写true
        undoPage = NULL;
        ret = UndoGetLastUndoPage(undoCtx, undo, true, &undoPage);
        if (undoPage == NULL || ret != STATUS_OK_INTER) {
            ret = (ret == STATUS_OK_INTER) ? UNEXPECTED_NULL_VALUE_INTER : ret;
            SE_LAST_ERROR(ret, "get last undoPage.");
            return ret;
        }

        // write a undo record to the undo log
        ret = TrxUndoPageWriteRecord(undoCtx, undoPage, rowOpInfo, &offset);
        if (ret != STATUS_OK_INTER) {
            break;
        }

        if (offset == 0u) {
            ret = TrxUndoExpandLog(undoCtx, undo, undoPage);
            if (ret != STATUS_OK_INTER) {
                break;
            }
            // 进入拓展流程，下一次GetLastPage就不是这个页了，需要leave当前的lastpage
            SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, true);
        } else {
            undo->recCnt++;
            ret = TrxUndoSetRowOperationPtr(undoCtx, undo, offset, rollPtr);
        }
    } while (offset == 0u);
    SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, ret == STATUS_OK_INTER);
    return ret;
}

static StatusInter TrxUndoRetainTryClearRseg(const SeUndoCtxT *undoCtx, const TrxT *trx, const TrxUndoLogT *undo)
{
    if (undo->state == TRX_UNDO_CACHED) {
        return STATUS_OK_INTER;
    }
    RSegPageHeaderT *rsegHdr = NULL;
    StatusInter ret = UndoGetRsegPage(undoCtx, trx->trx.base.rseg, true, &rsegHdr);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "get rsegPage");
        return ret;
    }
    DB_ASSERT(rsegHdr->magic == UNDO_RSEG_HEADER_MAGIC);
    ret = TrxRsegClearNthSlot(undoCtx, rsegHdr, undo->id);
    if (ret != STATUS_OK_INTER) {
        UndoLeaveRsegPage(undoCtx->pageMgr, trx->trx.base.rseg, false);
        SE_ERROR(ret, "TrxUndo retain try clear rseg");
        return ret;
    }
    rsegHdr->historyMaxTrxId = DB_MAX(rsegHdr->historyMaxTrxId, trx->base.commitTs);
    UndoWriteGeneralRedoLog(
        SeGetCurRedoCtx(), (uint8_t *)rsegHdr, offsetof(RSegPageHeaderT, historyMaxTrxId), sizeof(uint64_t));
    UndoLeaveRsegPage(undoCtx->pageMgr, trx->trx.base.rseg, true);
    return STATUS_OK_INTER;
}

StatusInter TrxUndoRetainedAssignCommitTimeStamp(
    const SeUndoCtxT *undoCtx, const TrxT *trx, const TrxUndoLogT *undo, RedoRunCtxT *redoCtx)
{
    UndoPageHeaderT *segHdr = NULL;
    StatusInter ret = UndoGetSegHdrPage(undoCtx, undo, true, &segHdr);
    if (segHdr == NULL || ret != STATUS_OK_INTER) {
        ret = (ret == STATUS_OK_INTER) ? UNEXPECTED_NULL_VALUE_INTER : ret;
        SE_LAST_ERROR(ret, "unable to get segHdrPage.");
        return ret;
    }
    UndoLogHeaderT *logHdr = UndoGetLogHdr(segHdr, undo->logHdrOffset);
    // set trx commit timestamp to the undo log hdr
    logHdr->trxCmtTs = trx->base.commitTs;
    DB_ASSERT(!logHdr->purgeHandleFlag);
    size_t offset = undo->logHdrOffset + offsetof(UndoLogHeaderT, trxCmtTs);
    UndoWriteGeneralRedoLog(redoCtx, (uint8_t *)segHdr, offset, sizeof(uint64_t));
    UndoLeaveSegHdrPage(undoCtx->pageMgr, undo, true);
    return STATUS_OK_INTER;
}

// 根据undo状态清理回滚段上slot，以及更新undo log header的commit ts使purger可以进行回收
StatusInter TrxUndoRetainedCleanupInner(
    const SeUndoCtxT *undoCtx, const TrxT *trx, const TrxUndoLogT *undo, RedoRunCtxT *redoCtx)
{
    StatusInter ret = TrxUndoRetainTryClearRseg(undoCtx, trx, undo);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    return TrxUndoRetainedAssignCommitTimeStamp(undoCtx, trx, undo, redoCtx);
}

StatusInter TrxUndoRetainedSetPurgeHandleFlag(const SeUndoCtxT *undoCtx, const TrxUndoLogT *undo)
{
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    UndoPageHeaderT *segHdr = NULL;
    StatusInter ret = UndoGetSegHdrPage(undoCtx, undo, true, &segHdr);
    if (segHdr == NULL || ret != STATUS_OK_INTER) {
        ret = (ret == STATUS_OK_INTER) ? UNEXPECTED_NULL_VALUE_INTER : ret;
        SE_LAST_ERROR(ret, "unable to get segHdrPage.");
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    UndoLogHeaderT *logHdr = UndoGetLogHdr(segHdr, undo->logHdrOffset);
    // set purgeHandle-Flag to the undo log hdr
    DB_ASSERT(!logHdr->purgeHandleFlag);
    logHdr->purgeHandleFlag = true;
    // UndoLogHeaderT里的位域字段相对位置若变了，需要改这里
    size_t offset = (size_t)undo->logHdrOffset + offsetof(UndoLogHeaderT, unclearPos) + sizeof(UndoRecPtrT);
    UndoWriteGeneralRedoLog(redoCtx, (uint8_t *)segHdr, offset, sizeof(uint32_t));
    UndoLeaveSegHdrPage(undoCtx->pageMgr, undo, true);
    return RedoLogEnd(redoCtx, true);
}

static void TrxUndoEmptyHistListPushBack(
    SeUndoCtxT *undoCtx, RSegPageHeaderT *rsegHdr, UndoPageHeaderT *segHdr, UndoRecPtrT logHdrPtr)
{
    UndoSetHistListHead(undoCtx, rsegHdr, *(uint64_t *)&logHdrPtr, segHdr);
    UndoSetHistListTail(undoCtx, rsegHdr, *(uint64_t *)&logHdrPtr, segHdr);
}

static StatusInter TrxUndoNonEmptyHistListPushBack(
    PageMgrT *pageMgr, SeUndoCtxT *undoCtx, RSegPageHeaderT *rsegHdr, UndoPageHeaderT *segHdr, UndoRecPtrT logHdrPtr)
{
    // append to the tail of the history list
    UndoRecPtrT *tailPtr = (UndoRecPtrT *)&rsegHdr->historyList.tailPtr;
    PageIdT pageAddr = DeserializePageId(pageMgr, tailPtr->pageId);
    UndoPageHeaderT *tailPage = NULL;
    StatusInter ret = SeGetPage(pageMgr, pageAddr, (uint8_t **)&tailPage, ENTER_PAGE_NORMAL, true);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "(UNDO) HLPB: get undo page");
        return ret;
    }
    // set the next pointer of tail
    UndoSetNextHistNode(undoCtx, tailPage, tailPtr->offset, *(uint64_t *)&logHdrPtr, segHdr);
    // set the prev pointer of new log header
    UndoSetPrevHistNode(undoCtx, segHdr, logHdrPtr.offset, rsegHdr->historyList.tailPtr, rsegHdr->historyList.tailPage);
    // update list tail points to the new log header
    UndoSetHistListTail(undoCtx, rsegHdr, *(uint64_t *)&logHdrPtr, segHdr);
    SeLeavePage(pageMgr, tailPage->baseHead.addr, true);
    return STATUS_OK_INTER;
}

static StatusInter TrxUndoHistListPushBack(SeUndoCtxT *undoCtx, const TrxUndoLogT *undo, RSegPageHeaderT *rsegHdr)
{
    // Add the update undo to history list
    // read the undo log header first
    UndoPageHeaderT *segHdr = NULL;
    StatusInter ret = UndoGetSegHdrPage(undoCtx, undo, true, &segHdr);
    if (segHdr == NULL || ret != STATUS_OK_INTER) {
        ret = (ret == STATUS_OK_INTER) ? UNEXPECTED_NULL_VALUE_INTER : ret;
        SE_LAST_ERROR(ret, "unable to get rsegPage.");
        return ret;
    }
    DB_ASSERT(segHdr->pageType == (uint16_t)(TRX_UNDO_RETAINED));

    UndoRecPtrT logHdrPtr = UndoRecPtrInit();
    uint32_t pageId = SerializePageId(undoCtx->pageMgr, segHdr->baseHead.addr);
    ret = UndoSetPtr(&logHdrPtr, pageId, undo->logHdrOffset);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        UndoLeaveSegHdrPage(undoCtx->pageMgr, undo, false);
        return ret;
    }

    if (rsegHdr->historyList.headPtr == UNDO_INVALID_ROLLPTR) {
        TrxUndoEmptyHistListPushBack(undoCtx, rsegHdr, segHdr, logHdrPtr);
    } else {
        ret = TrxUndoNonEmptyHistListPushBack(undoCtx->pageMgr, undoCtx, rsegHdr, segHdr, logHdrPtr);
    }
    UndoLeaveSegHdrPage(undoCtx->pageMgr, undo, ret == STATUS_OK_INTER);
    return ret;
}

// 把undo log添加到历史链表，由于commitTs初始化为DB_INVALID_TRX_ID，purger即使扫描到也无法清理
StatusInter TrxUndoAddToHistory(const TrxT *trx, const TrxUndoLogT *undo)
{
    DB_POINTER2(trx, undo);
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();

    RSegPageHeaderT *rsegHdr = NULL;
    StatusInter ret = UndoGetRsegPage(trx->seRunCtx->undoCtx, trx->trx.base.rseg, true, &rsegHdr);
    if (rsegHdr == NULL || ret != STATUS_OK_INTER) {
        ret = (ret == STATUS_OK_INTER) ? UNEXPECTED_NULL_VALUE_INTER : ret;
        SE_LAST_ERROR(ret, "unable to get rsegPage.");
        return ret;
    }

    DB_ASSERT(rsegHdr->magic == UNDO_RSEG_HEADER_MAGIC);
    // Add the update undo to history list
    ret = DbInterProcRWLatchW(&rsegHdr->baseHead.lock);
    if (ret != STATUS_OK_INTER) {
        UndoLeaveRsegPage(trx->seRunCtx->pageMgr, trx->trx.base.rseg, false);
        return ret;
    }

    ret = TrxUndoHistListPushBack(trx->seRunCtx->undoCtx, undo, rsegHdr);
    if (ret != STATUS_OK_INTER) {
        DbInterProcRWUnlatchW(&rsegHdr->baseHead.lock);
        UndoLeaveRsegPage(trx->seRunCtx->pageMgr, trx->trx.base.rseg, false);
        return ret;
    }

    // update history length
    rsegHdr->historyLen += 1u;
    UndoWriteGeneralRedoLog(redoCtx, (uint8_t *)rsegHdr, offsetof(RSegPageHeaderT, historyLen), sizeof(uint32_t));
    DbInterProcRWUnlatchW(&rsegHdr->baseHead.lock);
    UndoLeaveRsegPage(trx->seRunCtx->pageMgr, trx->trx.base.rseg, ret == STATUS_OK_INTER);

    return STATUS_OK_INTER;
}

// Commit all operations when transaction commits
StatusInter TrxUndoNormalCleanup(SeUndoCtxT *undoCtx, TrxT *trx, TrxUndoLogT *undo)
{
    StatusInter ret;
    trx->trx.base.normalUndo = NULL;

    ret = TrxUndoCommitOperations(undoCtx, trx, undo);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "commit operations(trxId %" PRIu64 ")", trx->base.trxId);
        return ret;
    }

    ret = TrxUndoSegTruncate(undoCtx, trx, undo);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "truncate update undo segment(trxId %" PRIu64 ")", trx->base.trxId);
        return ret;
    }

    if (undo->state == TRX_UNDO_CACHED) {
        // cache the undo segment for reuse
        DbSpinLock(&trx->trx.base.rseg->lock);
        UndoListPushFront(&trx->trx.base.rseg->normalUndoCached, undo);
        DbSpinUnlock(&trx->trx.base.rseg->lock);
    } else {
        return TrxUndoFree(undoCtx, trx->trx.base.rseg, undo);
    }

    return STATUS_OK_INTER;
}

StatusInter TrxUndoRetainedCleanup(const SeUndoCtxT *undoCtx, TrxT *trx, TrxUndoLogT *undo)
{
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    StatusInter ret = TrxUndoRetainedCleanupInner(undoCtx, trx, undo, redoCtx);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "add to history list, trxId: %" PRIu64 ", undo:(type: %" PRIu8 ", trxId: %" PRIu64 ")",
            trx->base.trxId, (uint8_t)(undo->type), undo->trxId);
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    ret = TrxUndoCommitOrRollbackFinish(redoCtx, undoCtx, undo);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Commit Or Rollback Mark finish, trxId: %" PRIu64 ", undo:(type: %" PRIu8 ", trxId: %" PRIu64 ")",
            trx->base.trxId, (uint8_t)(undo->type), undo->trxId);
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    return RedoLogEnd(redoCtx, true);
}

void TrxUndoRetainedCacheOrPurge(const SeUndoCtxT *undoCtx, TrxT *trx, TrxUndoLogT *undo)
{
    trx->trx.base.retainedUndo = NULL;
    if (undo->state == TRX_UNDO_CACHED) {
        DbSpinLock(&trx->trx.base.rseg->lock);
        UndoListPushFront(&trx->trx.base.rseg->retainedUndoCached, undo);
        DbSpinUnlock(&trx->trx.base.rseg->lock);
    } else {
        DB_ASSERT(undo->state == TRX_UNDO_TO_PURGE);
        // just free the memory obj
        // we have already linked the undo log to history list
        TrxUndoLogMemObjFree(undoCtx, undo);
    }
}

StatusInter TrxUndoMarkCommitOrRollbackStart(
    const SeUndoCtxT *undoCtx, TrxUndoLogT *undo, UndoStateE state, bool *canCache)
{
    DB_POINTER2(undoCtx, undo);
    // get the undo seg header page
    UndoPageHeaderT *undoPage = NULL;
    StatusInter ret = UndoGetSegHdrPage(undoCtx, undo, true, &undoPage);
    if (undoPage == NULL || ret != STATUS_OK_INTER) {
        ret = (ret == STATUS_OK_INTER) ? UNEXPECTED_NULL_VALUE_INTER : ret;
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "Commit Or Rollback Start: undo segment hdr page, trxId: %" PRIu64 ", undo:(type: %" PRIu8
            ", trxId: %" PRIu64 ")",
            undo->trxId, (uint8_t)(undo->type), undo->trxId);
        return ret;
    }

    // 在这里判断一下这个log是不是可以cache
    if (undoPage->freeStartPos < undoCtx->undoSpace->meta.undoPageReuseLimit) {
        *canCache = true;
    }

    // set the state according to undo log type
    UndoSegHeaderT *segHeader = (UndoSegHeaderT *)(undoPage + 1);
    DB_ASSERT(segHeader->magic == UNDO_SEG_HEADER_MAGIC);
    segHeader->state = (uint16_t)state;
    size_t offset = sizeof(UndoPageHeaderT) + offsetof(UndoSegHeaderT, state);
    UndoWriteGeneralRedoLog(SeGetCurRedoCtx(), (uint8_t *)undoPage, offset, sizeof(uint16_t));
    // 因为回滚是从后往前的，所以需要把LogHeader的unCommittedPos改成最末尾
    if (state == TRX_UNDO_ROLLBACK) {
        UndoPageHeaderT *lastPage = NULL;
        ret = UndoGetLastUndoPage(undoCtx, undo, false, &lastPage);
        if (lastPage == NULL || ret != STATUS_OK_INTER) {
            ret = (ret == STATUS_OK_INTER) ? UNEXPECTED_NULL_VALUE_INTER : ret;
            SE_LAST_ERROR(ret, "unable to get last undoPage.");
            UndoLeaveSegHdrPage(undoCtx->pageMgr, undo, true);
            return ret;
        }
        UndoLogHeaderT *logHdr = UndoGetLogHdr(undoPage, undo->logHdrOffset);
        uint32_t lastLogOffset = lastPage->freeStartPos;
        SeLeavePage(undoCtx->pageMgr, lastPage->baseHead.addr, false);
        uint32_t pageId = SerializePageId(undoCtx->pageMgr, undo->lastPageAddr);
        (void)UndoSetPtr(&logHdr->unclearPos, pageId, lastLogOffset);
        offset = (size_t)undo->logHdrOffset + offsetof(UndoLogHeaderT, unclearPos);
        UndoWriteGeneralRedoLog(SeGetCurRedoCtx(), (uint8_t *)undoPage, offset, sizeof(UndoRecPtrT));
    }
    UndoLeaveSegHdrPage(undoCtx->pageMgr, undo, true);
    return STATUS_OK_INTER;
}

StatusInter TrxUndoCommitOrRollbackStart(
    const SeUndoCtxT *undoCtx, TrxUndoLogT *undo, UndoStateE state, IsolationLevelE level)
{
    bool canCache = false;
    StatusInter ret = TrxUndoMarkCommitOrRollbackStart(undoCtx, undo, state, &canCache);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    // 由于Seg页内的状态信息需要更细粒度的区分，但是undolog内的已经满足粒度要求，这里在Commit/Rollback开始到结束
    // 的时间段内会出现undoLog和page上的state不一致
    if (state == TRX_UNDO_COMMIT) {
        if (level >= REPEATABLE_READ) {
            if (undo->usedPageCnt == 1 && canCache) {
                undo->state = TRX_UNDO_CACHED;
            } else if (undo->type == TRX_UNDO_NORMAL) {
                undo->state = TRX_UNDO_TO_FREE;
            } else {
                undo->state = TRX_UNDO_TO_PURGE;
            }
        } else {
            // in RC, all undo segments will be truncated after trx commit, then the undo seg can be cached
            undo->state = TRX_UNDO_CACHED;
        }
    }
    return STATUS_OK_INTER;
}

StatusInter TrxUndoCommitOrRollbackFinish(RedoRunCtxT *redoCtx, const SeUndoCtxT *undoCtx, TrxUndoLogT *undo)
{
    DB_POINTER2(undoCtx, undo);
    // get the undo seg header page
    UndoPageHeaderT *undoPage = NULL;
    StatusInter ret = UndoGetSegHdrPage(undoCtx, undo, true, &undoPage);
    if (undoPage == NULL || ret != STATUS_OK_INTER) {
        ret = (ret == STATUS_OK_INTER) ? UNEXPECTED_NULL_VALUE_INTER : ret;
        SE_LAST_ERROR(ret,
            "Commit Or Rollback Finish: undo segment hdr page, trxId: %" PRIu64 ", undo:(type: %" PRIu8
            ", trxId: %" PRIu64 ")",
            undo->trxId, (uint8_t)(undo->type), undo->trxId);
        return ret;
    }

    // set the state according to undo log type
    UndoSegHeaderT *segHeader = (UndoSegHeaderT *)(undoPage + 1);
    DB_ASSERT(segHeader->magic == UNDO_SEG_HEADER_MAGIC);
    if (segHeader->state == (uint16_t)TRX_UNDO_ROLLBACK) {
        if (undo->usedPageCnt == 1 && undoPage->freeStartPos < undoCtx->undoSpace->meta.undoPageReuseLimit) {
            undo->state = TRX_UNDO_CACHED;
        } else {
            undo->state = TRX_UNDO_TO_FREE;  // 理论上也走不到这里
            SE_ERROR(INTERNAL_ERROR_INTER, "set undo state");
            UndoLeaveSegHdrPage(undoCtx->pageMgr, undo, true);
            return INTERNAL_ERROR_INTER;
        }
    }
    TrxT *trx = (TrxT *)undoCtx->seCtx->trx;
    DB_ASSERT(trx->base.commitTs > segHeader->historyMaxTrxId);
    segHeader->historyMaxTrxId = trx->base.commitTs;
    size_t offset = sizeof(UndoPageHeaderT) + offsetof(UndoSegHeaderT, historyMaxTrxId);
    UndoWriteGeneralRedoLog(redoCtx, (uint8_t *)undoPage, offset, sizeof(uint64_t));
    segHeader->state = (uint16_t)undo->state;
    offset = sizeof(UndoPageHeaderT) + offsetof(UndoSegHeaderT, state);
    UndoWriteGeneralRedoLog(redoCtx, (uint8_t *)undoPage, offset, sizeof(uint16_t));
    UndoLeaveSegHdrPage(undoCtx->pageMgr, undo, true);
    return STATUS_OK_INTER;
}

inline bool UndoPageIsEnd(const UndoPageHeaderT *pageHdr, uint32_t offset)
{
    return offset >= pageHdr->freeStartPos;
}

StatusInter TrxUndoCleanupOnRollback(SeUndoCtxT *undoCtx, TrxT *trx, TrxUndoLogT *undo)
{
    trx->trx.base.retainedUndo = NULL;
    StatusInter ret = TrxUndoSegTruncate(undoCtx, trx, undo);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(
            ret, "truncate undo seg(trxId: %" PRIu64 ", type: %" PRIu8 ")", trx->base.trxId, (uint8_t)(undo->type));
        return ret;
    }

    if (undo->state == TRX_UNDO_CACHED) {  // usage ratio below threshold
        DbSpinLock(&trx->trx.base.rseg->lock);
        if (undo->type == TRX_UNDO_NORMAL) {
            UndoListPushFront(&trx->trx.base.rseg->normalUndoCached, undo);
        } else {
            UndoListPushFront(&trx->trx.base.rseg->retainedUndoCached, undo);
        }
        DbSpinUnlock(&trx->trx.base.rseg->lock);
    }
    return STATUS_OK_INTER;
}

StatusInter TrxUndoOnCommit(SeUndoCtxT *undoCtx, TrxT *trx)
{
    DB_POINTER2(undoCtx, trx);
    if (trx->trx.base.normalUndo == NULL && trx->trx.base.retainedUndo == NULL) {
        return STATUS_OK_INTER;
    }
    StatusInter ret = STATUS_OK_INTER;

    if (trx->trx.base.normalUndo != NULL) {
        TrxUndoLogT *undo = trx->trx.base.normalUndo;
        trx->trx.base.normalUndo = NULL;
        ret = TrxUndoNormalCleanup(undoCtx, trx, undo);
        if (ret != STATUS_OK_INTER && SeGetStorageStatus(trx->seRunCtx->seIns) == SE_ON_DISK_EMRGNCY) {
            return ret;
        }
    }

    if (trx->trx.base.retainedUndo != NULL) {
        TrxUndoLogT *undo = trx->trx.base.retainedUndo;
        if (undo->recCnt != 0) {
            ret = TrxUndoRetainedCleanup(undoCtx, trx, undo);
        } else {  // 没有recCnt等价于回滚到头的处理
            ret = TrxUndoCleanupOnRollback(undoCtx, trx, undo);
        }
    }
    return ret;
}

StatusInter TrxUndoOnRollback(SeUndoCtxT *undoCtx, TrxT *trx)
{
    DB_POINTER(trx);
    // if trx is readonly, just return directly
    if (trx->base.readOnly) {
        return STATUS_OK_INTER;
    }
    StatusInter ret = STATUS_OK_INTER;

    // rollback retained undo before normal undo
    if (trx->trx.base.retainedUndo != NULL) {
        ret = TrxUndoRollbackOperations(trx, trx->trx.base.retainedUndo, UNDO_ROLLBACK_ALL);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        ret = TrxUndoCleanupOnRollback(undoCtx, trx, trx->trx.base.retainedUndo);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }

    if (trx->trx.base.normalUndo != NULL) {
        ret = TrxUndoRollbackOperations(trx, trx->trx.base.normalUndo, UNDO_ROLLBACK_ALL);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        ret = TrxUndoCleanupOnRollback(undoCtx, trx, trx->trx.base.normalUndo);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }

    // 最后再置空，让视图有机会统计到回滚中的undo record数量
    trx->trx.base.retainedUndo = NULL;
    trx->trx.base.normalUndo = NULL;
    return STATUS_OK_INTER;
}

Status UndoLogGetMemStat(SeRunCtxHdT seRunCtx, UndoStatT *undoStat)
{
    DB_POINTER2(seRunCtx, undoStat);

    undoStat->rollbackPageNum = 0u;
    undoStat->insertUndoSize = 0u;
    undoStat->updateUndoSize = 0u;
    undoStat->deleteUndoSize = 0u;

    SeUndoCtxT *undoCtx = (SeUndoCtxT *)seRunCtx->undoCtx;
    undoStat->undologPageNum = undoCtx->undoSpace->meta.usedPageCnt;
    undoStat->pageSize = SIZE_K(((SeInstanceT *)seRunCtx->seIns)->seConfig.pageSize);
    undoStat->totalUndoSize = undoStat->undologPageNum * undoStat->pageSize;

    uint32_t memUsedSize = undoStat->insertUndoSize + undoStat->updateUndoSize + undoStat->deleteUndoSize;
    undoStat->memUnusedSize = (uint32_t)(undoStat->totalUndoSize - memUsedSize);
    if (undoStat->totalUndoSize == 0u) {
        undoStat->memUnusedSize = 0u;
    } else {
        undoStat->memUtilizationRatio = memUsedSize / (undoStat->totalUndoSize / UNDO_PERCENT);
    }

    return GMERR_OK;
}

// 由上层做并发控制
// 读取指定undo record中保存的buf
SECTION_FUN_OPTIMISTICTRX void UndoReadRecordTuple(
    const SeUndoCtxT *undoCtx, UndoRecBaseHdrT *srcRec, UndoTupleInfoT *undoTuple)
{
    DB_POINTER3(undoCtx, srcRec, undoTuple);
    UndoUpdHeapRecHdrT *updRec = (UndoUpdHeapRecHdrT *)(void *)srcRec;
    undoTuple->len = updRec->rowLen;
    undoTuple->isSkipUndoIndex = updRec->isSkipUndoIndex;
    undoTuple->opType = updRec->modBase.base.opType;
    undoTuple->savePointId = updRec->modBase.savePointId;
    undoTuple->rollPtr = updRec->modBase.rollPtr;
    if (SECUREC_LIKELY(srcRec->opType != (uint8_t)TRX_OP_UPDATE_PART)) {
        undoTuple->data = (uint8_t *)(updRec + 1);
    } else {
        // 悲观undo链清理时，才可能跑进来
        undoTuple->data = (uint8_t *)((UndoPrtUpdHeapRecHdrT *)(void *)srcRec + 1);
    }
}

// 读取指定undo recod的实际内容
SECTION_FUN_OPTIMISTICTRX StatusInter UndoReadRowOpInfoByRecBuf(
    const SeUndoCtxT *undoCtx, UndoRecBaseHdrT *rec, UndoRowOpInfoT *rowInfo)
{
    DB_POINTER3(undoCtx, rec, rowInfo);
    return UndoGetTrxResUndoRecordOps(rec->resType)->recDecodeFunc((uint8_t *)rec, rowInfo);
}

SECTION_FUN_OPTIMISTICTRX StatusInter UndoReadRowOpInfoByRollPtr(
    const SeUndoCtxT *undoCtx, uint64_t rollPtr, UndoRowOpInfoT *rowInfo)
{
    DB_POINTER2(undoCtx, rowInfo);
    UndoRecPtrT recPtr = *(UndoRecPtrT *)&rollPtr;
    PageIdT pageAddr = DeserializePageId(undoCtx->pageMgr, recPtr.pageId);
    UndoPageHeaderT *undoPage = NULL;
    StatusInter ret = SeGetPage(undoCtx->pageMgr, pageAddr, (uint8_t **)&undoPage, ENTER_PAGE_NORMAL, false);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "(UNDO) RUGVT: undo page from memdata(deviceId: %" PRIu32 ", blockId: %" PRIu32 ")",
            pageAddr.deviceId, pageAddr.blockId);
        return ret;
    }
    DB_ASSERT(undoPage->magic == UNDO_PAGE_HEADER_MAGIC);
    UndoRecBaseHdrT *rec = (UndoRecBaseHdrT *)UndoGetLogRec(undoPage, recPtr.offset);
    ret = UndoReadRowOpInfoByRecBuf(undoCtx, rec, rowInfo);
    SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
    return ret;
}

// 更新时使用mergeRowInfo覆盖更新，不对被更新undo的类型等做修改
SECTION_FUN_OPTIMISTICTRX void UndoMergeRowInfo(UndoRowOpInfoT *dstRowInfo, const UndoRowOpInfoT *mergeRowInfo)
{
    DB_POINTER(dstRowInfo);
    dstRowInfo->isDeleted = mergeRowInfo->isDeleted;
    dstRowInfo->rowSize = mergeRowInfo->rowSize;
    dstRowInfo->rowBuf = mergeRowInfo->rowBuf;
    dstRowInfo->rowRollPtr = mergeRowInfo->rowRollPtr;
    dstRowInfo->rowTrxId = mergeRowInfo->rowTrxId;
}

// 需要更新源undo rec上一些信息，便于遍历找可见版本
inline static SECTION_FUN_OPTIMISTICTRX void UndoUpdateSrcRow(UndoModifyRecHdrT *srcRec, const UndoRowOpInfoT *rowInfo)
{
    srcRec->rollPtr = rowInfo->rowRollPtr;
    srcRec->trxId = rowInfo->rowTrxId;
}

// 尝试利用目前正在使用的空间进行更新
SECTION_FUN_OPTIMISTICTRX StatusInter UndoUpdateRowInPlace(
    const SeUndoCtxT *undoCtx, UndoPageHeaderT *pageHdr, UndoModifyRecHdrT *srcRec, const UndoRowOpInfoT *rowInfo)
{
    UndoRecBaseHdrT *recBuf = &srcRec->base;
    // 尝试在原有的空间更新，保持recBuf->size不变，以免与回滚流程有并发问题
    // 其余字段，即使不对也没有关系，在回滚时，会有加上页latch然后重读的步骤
    StatusInter ret = UndoGetTrxResUndoRecordOps(recBuf->resType)
                          ->recRollBackFunc(rowInfo, (uint8_t *)pageHdr, recBuf->size, (uint8_t *)recBuf);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    UndoUpdateSrcRow(srcRec, rowInfo);
    size_t offset = (size_t)((uintptr_t)srcRec - (uintptr_t)pageHdr) + offsetof(UndoModifyRecHdrT, rollPtr);
    UndoWriteGeneralRedoLog(SeGetCurRedoCtx(), (uint8_t *)pageHdr, offset, sizeof(uint64_t));

    offset = (size_t)((uintptr_t)srcRec - (uintptr_t)pageHdr) + offsetof(UndoModifyRecHdrT, trxId);
    UndoWriteGeneralRedoLog(SeGetCurRedoCtx(), (uint8_t *)pageHdr, offset, sizeof(uint64_t));
    return STATUS_OK_INTER;
}

// 更新undo行
SECTION_FUN_OPTIMISTICTRX StatusInter UndoUpdateRow(SeUndoCtxT *undoCtx, UndoPageHeaderT *undoPage,
    UndoRecBaseHdrT *targetRec, const UndoRowOpInfoT *rowInfo, bool isRollBack)
{
    if (targetRec->resType != (uint8_t)rowInfo->resType) {
        // target undo resType is different from undo row resType
        SE_ERROR(DATA_EXCEPTION_INTER, "different undo resType");
        return DATA_EXCEPTION_INTER;
    }
    // 找到保存实际内容的空间
    UndoModifyRecHdrT *srcRec = (UndoModifyRecHdrT *)(void *)targetRec;
    StatusInter ret = UndoUpdateRowInPlace(undoCtx, undoPage, srcRec, rowInfo);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "undo update record page in place");
        // 回滚场景reserveSize机制保证undo在原有的空间更新必定成功，从而避免额外申请内存的过程
        DB_ASSERT(!isRollBack);
    }
    return ret;
}

// 更新的内容只允许有trxId, buf，合并两个UndoRowOpInfoT，并序列化保存
SECTION_FUN_OPTIMISTICTRX StatusInter UndoUpdateRecord(
    SeUndoCtxT *undoCtx, uint64_t targetRollPtr, const UndoRowOpInfoT *rowInfo, bool isRollBack)
{
    DB_POINTER2(undoCtx, rowInfo);
    DB_ASSERT(targetRollPtr != UNDO_INVALID_ROLLPTR);
    UndoPageHeaderT *undoPage = NULL;
    UndoRecBaseHdrT *targetRec = NULL;
    StatusInter ret = UndoFetchRecordWrite(undoCtx->pageMgr, targetRollPtr, &undoPage, (uint8_t **)&targetRec);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get target rec.");
        return ret;
    }
    // 只允许更新删除操作的undo rec进行修改
    if ((UndoRowOpTypeE)targetRec->opType != TRX_OP_DELETE && (UndoRowOpTypeE)targetRec->opType != TRX_OP_UPDATE) {
        SE_ERROR(INTERNAL_ERROR_INTER, "unexpected undo optype: opType: %" PRIu8, targetRec->opType);
        return INTERNAL_ERROR_INTER;
    }
    UndoRowOpInfoT targetRowInfo = {0};
    ret = UndoReadRowOpInfoByRecBuf(undoCtx, targetRec, &targetRowInfo);
    if (ret != STATUS_OK_INTER) {
        SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, false);
        SE_ERROR(ret, "undo read row info of record");
        return ret;
    }
    if (isRollBack) {
        // 合并需要更新的信息
        UndoMergeRowInfo(&targetRowInfo, rowInfo);
        // 使用最新的rowInfo进行覆盖更新
        ret = UndoUpdateRow(undoCtx, undoPage, targetRec, &targetRowInfo, isRollBack);
    } else {
        DB_ASSERT(targetRowInfo.rowSize >= rowInfo->rowSize);
        DB_ASSERT(targetRowInfo.isDeleted == rowInfo->isDeleted);
        DB_ASSERT(targetRowInfo.isSkipUndoIndex == rowInfo->isSkipUndoIndex);
        DB_ASSERT(targetRowInfo.opType == rowInfo->opType);
        DB_ASSERT(targetRowInfo.labelId == rowInfo->labelId);
        DB_ASSERT(targetRowInfo.labelType == rowInfo->labelType);
        DB_ASSERT(targetRowInfo.rowTrxId == rowInfo->rowTrxId);
        DB_ASSERT(targetRowInfo.rowRollPtr == rowInfo->rowRollPtr);
        DB_ASSERT(targetRowInfo.rowId == rowInfo->rowId);
        DB_ASSERT(targetRowInfo.savePointId == rowInfo->savePointId);
        ret = UndoUpdateRow(undoCtx, undoPage, targetRec, rowInfo, isRollBack);
    }

    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "update undo record, rec opType: %" PRIu32 ", resType: %" PRIu32, (uint32_t)targetRec->opType,
            (uint32_t)targetRec->resType);
    }
    SeLeavePage(undoCtx->pageMgr, undoPage->baseHead.addr, ret == STATUS_OK_INTER);
    return ret;
}

// 与终端对齐
// 上层调用需要对undo record整条版本链进行加锁
// undo record前一个版本在undo中，在undo做回滚操作，更新其他undo record
// 合并undoLog的场景，也是往版本链上写，isRollBack为false
SECTION_FUN_OPTIMISTICTRX StatusInter UndoWriteRecInVersionChain(
    SeUndoCtxT *undoCtx, uint64_t masterRollPtr, const UndoRowOpInfoT *rbRowOpInfo, uint64_t rbRollPtr, bool isRollBack)
{
    DB_POINTER2(undoCtx, rbRowOpInfo);
    if ((rbRowOpInfo->opType != TRX_OP_UPDATE) && (rbRowOpInfo->opType != TRX_OP_DELETE)) {
        SE_ERROR(INTERNAL_ERROR_INTER, "unexpected undo optype.");
        return INTERNAL_ERROR_INTER;
    }
    StatusInter ret = INT_ERR_UNDO_REC_NOT_FOUND;
    UndoPageHeaderT *pageHdr = NULL;
    // traverse the version chain from the next(old) version of the master version
    UndoModifyRecHdrT *curRec = NULL;
    uint64_t targetRollPtr = masterRollPtr;
    ret = UndoFetchRecordReadOnly(undoCtx->pageMgr, masterRollPtr, &pageHdr, (uint8_t **)&curRec);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    while (curRec != NULL && !RowUndoIsValidRollPtr(curRec->rollPtr)) {
        DB_ASSERT(curRec->base.labelId == rbRowOpInfo->labelId);
        DB_ASSERT(curRec->rowid == rbRowOpInfo->rowId);

        // 找到目标版本的上一个版本
        if (curRec->rollPtr == rbRollPtr) {
            // 使用回滚的事务的undo rec信息更新curRec
            // 合并undoLog的场景, 直接写入版本链，复用此函数
            SeLeavePage(undoCtx->pageMgr, pageHdr->baseHead.addr, false);
            ret = UndoUpdateRecord(undoCtx, targetRollPtr, rbRowOpInfo, isRollBack);
            break;
        }
        // fetch next tuple in the version chain
        targetRollPtr = curRec->rollPtr;
        ret = UndoFetchRecordReadOnly(undoCtx->pageMgr, targetRollPtr, &pageHdr, (uint8_t **)&curRec);
        if (ret != STATUS_OK_INTER) {
            break;
        }
    }
    if (ret == INT_ERR_UNDO_REC_NOT_FOUND) {
        UndoRecPtrT *itemPtr = (UndoRecPtrT *)(void *)&rbRollPtr;
        SE_ERROR(ret, "find rollptr(%" PRIu32 ", %" PRIu32 ") in undo rollback.", itemPtr->pageId, itemPtr->offset);
    }
    return ret;
}
