/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: header file for lite transaction
 * Author: chendechen
 * Create: 2021-11-13
 */

#ifndef SE_TRX_LITE_H
#define SE_TRX_LITE_H

#include "se_undo_pub.h"
#include "dm_meta_res_col_pool.h"
#include "se_trx.h"
#include "se_instance.h"
#include "db_internal_error.h"
#include "se_common.h"

#ifdef __cplusplus
extern "C" {
#endif

/* undo record format
 * In lite transaction, there are only insert and update undo records.
 * Delete operation physically delete an tuple without reporting delete undo record, assuming no failure and rollback
 * will happen.
 */

#define UNDO_LITE_INIT_TUPLE_NUM 1
#define UNDO_LITE_INIT_RECORD_NUM 2
#define TRX_LITE_UNDO_EXTEND_STEP 50

typedef struct UndoLiteInsertRecord {
    uint64_t trxId;
    uint64_t rowId;
} UndoLiteInsRec;

typedef struct AdvancedPtr {
    ShmemPtrT shmPtr;  // 用于获取首addr，放置开头保证拷贝时有效性
    void *ptr;         // 用于高性能访问
} AdvancedPtrT;

static inline AdvancedPtrT APtrNULL(void)
{
    AdvancedPtrT ptr = {.shmPtr = DB_INVALID_SHMPTR, .ptr = NULL};
    return ptr;
}

static inline bool APtrIsNULL(AdvancedPtrT ptr)
{
    return ptr.ptr == NULL;
}

static inline bool APtrIsValid(AdvancedPtrT ptr)
{
    return DbIsShmPtrValid(ptr.shmPtr);
}

static inline bool APtrIsEqual(AdvancedPtrT leftPtr, AdvancedPtrT rightPtr)
{
    return leftPtr.ptr == rightPtr.ptr;
}

static inline AdvancedPtrT APtrOffset(AdvancedPtrT ptr, uint32_t offset)
{
    ShmemPtrT shmPtr = {.offset = ptr.shmPtr.offset + offset, .segId = ptr.shmPtr.segId};
    AdvancedPtrT targetPtr = {.shmPtr = shmPtr, .ptr = (void *)((uint8_t *)(ptr.ptr) + offset)};
    return targetPtr;
}

static inline void *APtrGetAccessPtr(AdvancedPtrT ptr)
{
    return ptr.ptr;
}

static inline ShmemPtrT APtrGetShmPtr(AdvancedPtrT ptr)
{
    return ptr.shmPtr;
}

static inline AdvancedPtrT APtrMemCtxAlloc(DbMemCtxT *memCtx, uint32_t allocSize, bool isUseRsm)
{
    AdvancedPtrT ptr = APtrNULL();
    ptr.ptr = isUseRsm ? DbRsmMemCtxAlloc(memCtx, allocSize, &ptr.shmPtr) : DbDynMemCtxAlloc(memCtx, allocSize);
    return ptr;
}

static inline void APtrMemCtxFree(DbMemCtxT *memCtx, AdvancedPtrT ptr)
{
    if (APtrIsValid(ptr)) {
        return DbShmemCtxFree(memCtx, APtrGetShmPtr(ptr));
    }

    if (!APtrIsNULL(ptr)) {
        return DbDynMemCtxFree(memCtx, APtrGetAccessPtr(ptr));
    }
}

typedef struct UndoLiteUpdateRecord {
    bool isSkipUndoIndex;
    bool isUseBatchAllocMem;
    uint8_t reserve;
    uint32_t tupleLen;  // length of the updated tuple buffer
    uint64_t trxId;
    uint64_t rowId;            // tuple address of the updated tuple
    AdvancedPtrT tupleBuffer;  // uint8_t * old tuple buffer
} UndoLiteUpdRec;

typedef struct UndoLiteUpdatePartRecord {
    uint16_t srcRowHeadSize;
    uint16_t partDataLen;  // 目前部分更新不支持超过一个页的内容的更新
    uint32_t offsetOfRawData;
    uint32_t tupleLen;  // length of the updated tuple buffer
    uint64_t trxId;
    uint64_t rowId;            // tuple address of the updated tuple
    AdvancedPtrT tupleBuffer;  // 行头 + 被更新掉的部分旧内容
} UndoLiteUpdPartRec;

typedef struct UndoLiteResColRecord {
    uint32_t resCount;  // 每条记录最多绑定4个资源列，记录用了几个资源列
    uint32_t poolLabelId[DM_RES_COL_MAX_COUNT];
    uint64_t resIdArr[DM_RES_COL_MAX_COUNT];
    uint32_t operLabelId[DM_RES_COL_MAX_COUNT];
} UndoLiteResColRec;

typedef struct UndoLiteOperationRecord {
    UndoRowOpTypeE recordType;
    uint32_t labelId;
    union {
        UndoLiteInsRec insertRec;
        UndoLiteUpdRec updateRec;
        UndoLiteUpdPartRec updatePartRec;
        UndoLiteResColRec resColRec;
    } record;
} UndoLiteOpRecord;

typedef struct UndoLiteRecordsNode {
    bool isAllocBatchTupleMem;
    uint8_t reserved[3];
    uint32_t allocTupleSize;
    uint32_t usedTupleSize;
    uint32_t reserved2;
    AdvancedPtrT pre;              // UndoLiteRecordsNodeT *
    AdvancedPtrT next;             // UndoLiteRecordsNodeT *
    uint32_t curPos;               // 该node中使用到的pos
    uint32_t nodeSize;             // recordArr的大小
    AdvancedPtrT batchAllocTuple;  // uint8_t *
    UndoLiteOpRecord recordArr[];
} UndoLiteRecordsNodeT;

typedef struct UndoLiteRecord {
    bool allowExtendUndoLog;  // 针对范围更新, 允许进行扩展recordExtendArr
    bool hadSetTupleNum;      // “主动设置tupleNum” 的不可重入标志
    bool isInited;            // 是否已经初始化, 用于在TrxLiteUndoLogInit区别是否重入函数
    bool isAllocFromList;
    bool hasResColUndoLog;  // 一个tuple最多包含4个资源列字段,用于标记是否中间某资源列出错,回滚时需把已申请的进行释放+
                            // 同时也表示是否需要为资源池的record先进行一次初始化
    bool isChLabel;         // 记录的是否为聚簇容器的undo log
    bool trxCommitFlag;  // 保留内存场景使用，当设置此标记位后，如果发生异常重启，恢复时继续提交该事务，否则执行回滚
    bool isRsmRecoverying;  // 保留内存场景使用，当设置为true时，表示是处理重启前的undo；否则是处理重启后的undo
    uint32_t tupleNum;         // 记录批量操作中操作的总行数
    uint32_t usedUndoRecNum;   // 实际使用到的位置
    uint32_t totalUndoRecNum;  // 长度为tupleNum * 2 (一个insert undo会对应一个资源列 undo)
    uint32_t extendStepSize;   // 扩展的步长
    UndoLiteOpRecord recordArr[UNDO_LITE_INIT_RECORD_NUM];
    AdvancedPtrT lastNodePtr;          // UndoLiteRecordsNodeT *
    AdvancedPtrT recordExtendNodeArr;  // UndoLiteRecordsNodeT *
                                       // 链表，第一次初始化时
                                       // 直接按(totalUndoRecNum - UNDO_LITE_INIT_RECORD_NUM)来扩展
} UndoLiteRecordT;

StatusInter TrxLiteUndoLogBatchAllocTupleInit(TrxT *trx, uint32_t tupleLen);

SO_EXPORT_FOR_TS StatusInter TrxLiteUndoLogExtend(TrxT *trx);

StatusInter TrxLiteUndoLogExtendBySize(TrxT *trx, uint32_t size);

StatusInter TrxLiteUndoLogInit(TrxT *trx);

SO_EXPORT_FOR_TS AdvancedPtrT TrxLiteAllocBuf(TrxT *trx, uint32_t bufLength);

void SeTransSetDmlUndoLiteSize(SeRunCtxHdT seRunCtx, uint32_t batchNum, bool allowExtendUndoLog);

void SeTransSetDmlUndoLiteExtendSize(SeRunCtxHdT seRunCtx, uint32_t batchNum);

void *TrxLiteUndoLiteRecRsmAlloc(ShmemPtrT *shmPtr);

void TrxLiteSetUndoLiteRec(TrxT *trx, UndoLiteRecordT *undoLiteRec, bool isRecovery);

void UndoLiteRecInit(TrxT *trx);

inline static UndoLiteOpRecord *TrxLiteGetRecordFromNode(UndoLiteRecordsNodeT *node, uint32_t arrPos)
{
    DB_POINTER(node);
    DB_ASSERT(arrPos < node->nodeSize);
    return &node->recordArr[arrPos];
}

inline static void TrxLiteMoveNextFreeRecord(UndoLiteRecordT *undoLiteRec)
{
    undoLiteRec->usedUndoRecNum++;
    if (undoLiteRec->isAllocFromList) {
        UndoLiteRecordsNodeT *lastNodePtr = (UndoLiteRecordsNodeT *)APtrGetAccessPtr(undoLiteRec->lastNodePtr);
        lastNodePtr->curPos++;
        undoLiteRec->isAllocFromList = false;
    }
}

inline static UndoLiteOpRecord *TrxLiteGetNextFreeRecord(UndoLiteRecordT *undoLiteRec, uint32_t arrPos)
{
    DB_POINTER(undoLiteRec);
    DB_ASSERT(arrPos < undoLiteRec->totalUndoRecNum);
    if (SECUREC_UNLIKELY(arrPos >= UNDO_LITE_INIT_RECORD_NUM)) {
        undoLiteRec->isAllocFromList = true;
        UndoLiteRecordsNodeT *lastNodePtr = (UndoLiteRecordsNodeT *)APtrGetAccessPtr(undoLiteRec->lastNodePtr);
        return TrxLiteGetRecordFromNode(lastNodePtr, lastNodePtr->curPos);
    } else {
        undoLiteRec->isAllocFromList = false;
        return &undoLiteRec->recordArr[arrPos];
    }
}

#ifdef __cplusplus
}
#endif

#endif  // SE_TRX_LITE_H
