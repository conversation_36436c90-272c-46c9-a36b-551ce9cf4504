/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:  write cache rsm in SE
 * Author:
 * Create: 2024-04-29
 */
#ifndef SE_WRITE_CACHE_RSM_PUBLIC_H
#define SE_WRITE_CACHE_RSM_PUBLIC_H

#include "se_write_cache.h"

#ifdef __cplusplus
extern "C" {
#endif

SO_EXPORT void SeDeleteRsmFreePages(WriteCacheDescT *desc, int32_t numToScaleIn);

SO_EXPORT Status SeGetRsmFreePageNum(WriteCacheDescT *desc, int32_t *freePageNum);

SO_EXPORT WCachePageHeadT *SeGetRsmPageWithMgr(WriteCacheDescT *writeCacheDesc, MdMgrT *mdMgr, PageIdT pageId);

SO_EXPORT WCachePageHeadT *SeGetRsmPage(WriteCacheDescT *writeCacheDesc, PageIdT pageId);

SO_EXPORT bool SeRsmIsNextPageEqualCurrPage(const WCachePageHeadT *page);

SO_EXPORT bool SeRsmIsNextPageEqualMergePage(const WriteCacheDescT *writeCacheDesc, const WCachePageHeadT *page);

SO_EXPORT void SeRsmSetWritePage(WriteCacheDescT *desc, const WCachePageHeadT *page);

SO_EXPORT void SeRsmUpdateMergePage(const WCachePageHeadT *mergePage, WriteCacheDescT *desc);

#ifdef __cplusplus
}
#endif
#endif /* SE_WRITE_CACHE_RSM_PUBLIC_H */
