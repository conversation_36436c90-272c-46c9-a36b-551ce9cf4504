/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Deferred Action Framework
 * Author:
 * Create: 2024-04-15
 */
#ifndef SE_DAF_H
#define SE_DAF_H

#include "se_trx.h"
#include "dm_meta_basic.h"
#include "se_daemon.h"

#ifdef __cplusplus
extern "C" {
#endif
typedef struct IdxDeleteActions IdxDeleteActionsT;
typedef struct DafMgr DafMgrT;

StatusInter DafInit(DbMemCtxT *memCtx);

void DafUnInit(void);

bool IsDafMgrInited(void);

/**
 * 用于判断事务是否支持Daf
 * 事务开启时判断是否需要创建事务上的Daf链表
 * @param trx
 * @return
 */
bool TrxIsDafEnable(const TrxT *trx);

/**
 * 用于判断单个索引是否支持Daf
 * 支持所有需要索引标记删除再真实删除的事务（RC、RR、串行化）
 * Daf当前仅支持内存模式下使用，适配可以进入延迟删除逻辑的索引类型包含HacHash、MultiHash、Btree、及其他非唯一索引。
 * @param trx 事务类型
 * @param indexType 索引类型
 * @param isUnique 是否唯一
 * @return
 */
bool TrxIsDafEnableForIndex(const TrxT *trx, const DmIndexTypeE indexType, const bool isUnique);

bool SeIsDafEnableForIndex(
    const SeRunCtxHdT seRunCtx, const HpRunHdlT hpHandle, const DmIndexTypeE indexType, const bool isUnique);

/**
 * 为事务创建DafActionCollection
 * @param trx
 * @return
 */
StatusInter DafCreateActionCollection(TrxT *trx);

/**
 * 记录单索引删除操作记录到trx上
 * @param idxCtx
 * @param idxKey
 * @param addr
 * @return
 */
Status IdxDelayDelete(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr);

/**
 * 记录批量删除操作记录到trx上
 * @param idxCtx
 * @param idxKey
 * @param addr
 * @param batchNum
 * @return
 */
Status IdxDelayBatchDelete(IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum);

/**
 * 事务提交时提交待清理的Daf操作
 * @param trx
 */
void DafCommitActionCollection(TrxT *trx);

void DafCommit(TrxT *trx);

void DafRollbackActions(TrxT *trx);

// purger

typedef struct DafPurgerRunCtx *DafPurgerRunCtxHdlT;

/**
 * 触发DafAction，删除索引（Purger使用）
 * @param trx
 * @param actions
 */
void DafExecuteIndexCommitActionOnPurger(DafPurgerRunCtxHdlT purgerCtx, IdxDeleteActionsT *action);

// 只有在后台Purger线程停止才会调用，一般来说就是进程都结束了。
void DafPurgerCtxDestroy(DafPurgerRunCtxHdlT purgerCtx);

void DafPurgerCtxRegister(DafPurgerRunCtxHdlT purgerCtx);

Status DafPurgerCtxInit(DafPurgerRunCtxHdlT *purgerCtx, const UndoPurgerCfgT *purgerCfg);

bool DafPurgerMain(DafPurgerRunCtxHdlT purgerCtx, uint64_t currTime, uint16_t workerId);

#ifdef __cplusplus
}
#endif
#endif  // SE_DAF_H
