/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: header file for driver statecollapse subscribe and notify APIs.
 * Author: q<PERSON><PERSON><PERSON> <PERSON> Floyd
 * Create: 2022/11/21
 * Notes: NA
 */

#ifndef SE_SUBS_STATUS_MERGE_H
#define SE_SUBS_STATUS_MERGE_H

#include "dm_data_basic.h"
#include "adpt_types.h"
#include "adpt_spinlock.h"
#include "db_se_heap_serialization.h"
#include "db_mem_context.h"
#include "dm_data_prop_seri.h"
#include "se_access_pub.h"
#include "adpt_spinlock.h"

#ifdef __cplusplus
extern "C" {
#endif

// 链表遍历超时时间
#define TRAVEL_LIST_TIME_MAX (5 * 1000)

inline static uint64_t DB_CONCATE_U32(uint32_t high, uint32_t low)
{
    return ((uint64_t)(((uint64_t)(high) << UINT32_SIZE) | (low)));
}

#define DB_INVALID_SHMPTR_U64 DB_INVALID_UINT64
#define STATUS_MERGE_NODE_LOG_LEN 128

inline static uint64_t ShmemPtr2Uint64(ShmemPtrT ptr)
{
    return DB_CONCATE_U32(ptr.offset, ptr.segId);
}

inline static ShmemPtrT Uint64ToShmemPtr(uint64_t u64)
{
    return (ShmemPtrT){.offset = DB_HIGH_32BIT(u64), .segId = DB_LOW_32BIT(u64)};
}

typedef enum {
    NODE_LIVE,         // 在链表上活跃
    NODE_MARKRETIRED,  // 死亡但是还在链表上
    NODE_REMOVED,      // 从链表中
    NODE_DELETED,      // 保证没有任何方式能够从链表的头节点访问到，马上可以free掉
    NODE_UNUSED        // 订阅节点专用，表明客户端缓存已移除订阅节点，可以删除
} NodeStatusE;

typedef enum {
    HEAD,  // 链表头
    TAIL,  // 链表尾
    RH,    // 回收头
    RT,    // 回收尾
    GC,    // GC节点
    DATA,  // 数据节点
    SUB,   // 订阅节点
    NODE_TYPE_BUTT
} StmgNodeTypeE;

typedef struct {
    uint64_t prev;  // ShmemPtr转为uint64
    uint64_t next;  // ShmemPtr转为uint64
    TupleAddr tuple;
    DbSpinLockT spinLock;
    uint8_t nodeStatus;
    bool isWaiting;
    bool isConsuming;  // 订阅节点消费状态，控制gc和客户端读取, spinLock并发保护
} StatusMergeNodeBaseT;
DB_CHECK_ALIGN_ATOMIC64_MEMBER(StatusMergeNodeBaseT, prev);

typedef struct {
    StatusMergeNodeBaseT baseNode;
} StatusMergeDataNodeT;

typedef struct {
    StatusMergeNodeBaseT baseNode;
    DbSpinLockT lock;  // 给subCursor节点和gc节点使用，防止gc和创建全量订阅冲突，异常断连和客户端读取冲突
} StatusMergeCursorNodeT;

typedef struct {
    ShmemPtrT headDummy;
    ShmemPtrT tailDummy;
    ShmemPtrT garbageCollector;

    ShmemPtrT retireHeadDummy;
    ShmemPtrT retireTailDummy;
    ShmemPtrT threadMapArray;
    ShmemPtrT hpArrayPtr;
    DbSpinLockT threadMapLock;
} StatusMergeListT;

// 合并订阅缩容过程上下文
typedef struct TagStatusMergeDefragRunCtx {
    bool migrateEof;
    bool canDefrage;   // 状态合并订阅支持缩容引入
    HpTupleAddr addr;  // 元组的逻辑addr
    uint8_t *vertexBuf;
    DmVertexLabelT *vertexLabel;
    StatusMergeNodeBaseT *curNode;
} StatusMergeDefragRunCtx;

bool IsStatusMergeSubsCursorNode(const StatusMergeNodeBaseT *node);

DbMemCtxT *GetSubsStmgListShmCtx(void);

Status CreateStatusMergeSubDataNode4DirectWrite(ShmemPtrT *nodePtr);

Status CreateStatusMergeSubCursorNode(ShmemPtrT *nodePtr);

Status CreateStatusMergeSubDataNode(ShmemPtrT *nodePtr);

void LogStatusMergeBaseNode(const StatusMergeNodeBaseT *node, const char *prefix, Status ret);

Status BatchCreateStatusMergeSubDataNode(ShmemPtrT *ptrArray, uint32_t batchNum);

void ReleaseStatusMergeSubNode(ShmemPtrT nodePtr);

Status CreateStatusMergeSub(ShmemPtrT *listPtr);

Status SeCheckNodeAndModifyTuple(StatusMergeDefragRunCtx *statusMergeCtx, bool needChangeTuple, bool *canDefrage);

void ReleaseStatusMergeSub(ShmemPtrT listPtr);

static inline StatusMergeNodeBaseT *GetSubsNodeAddr(ShmemPtrT ptr)
{
    return (StatusMergeNodeBaseT *)DbShmPtrToAddr(ptr);
}

static inline StatusMergeCursorNodeT *GetCursorNodeAddr(ShmemPtrT ptr)
{
    return (StatusMergeCursorNodeT *)DbShmPtrToAddr(ptr);
}

// DDL
Status DbServerCreateIncSub(ShmemPtrT subMergeList, ShmemPtrT subCursor);

Status DbServerCreateFullSub(ShmemPtrT subMergeList, ShmemPtrT subCursor);

Status DbServerDeleteSub(ShmemPtrT subMergeList, ShmemPtrT subCursor, bool isZombie);

Status DbServerZombieRetire(ShmemPtrT subMergeList, ShmemPtrT subCursor);

Status DbMarkUnusedSubCursor(ShmemPtrT nodePtr);

Status DbRecoverUnusedSubCursor(ShmemPtrT nodePtr);

bool DbCheckSubCursorUnused(ShmemPtrT nodePtr);

// DML
void DbServerInsertRow(ShmemPtrT subMergeList, ShmemPtrT newPtr);

void DbServerUpdateRow(ShmemPtrT subMergeList, ShmemPtrT updatePtr);

// DQL
Status DbCltReadOne(StatusMergeListT *stmgList, ShmemPtrT subCursor, TupleAddr *tupleAddr, uint32_t *manipuShmMark);

// gc
bool DbServerNeedGcCheck(ShmemPtrT subMergeList, ShmemPtrT *nxtPtr);
Status DbServerDeleteDataNode(ShmemPtrT subMergeList, ShmemPtrT nodePtr);
Status DbGcAdvanceOnePos(ShmemPtrT subMergeList, ShmemPtrT gcCursor);
Status DbServerClearListStep(ShmemPtrT subMergeList);

// Release
void DbServerClearRetire(ShmemPtrT subMergeList);

// TEST And DEBUGER
typedef enum StMgListPrint { RETIRE_PRINT, LIST_PRINT } StMgListPrintE;

void DbServerDebugerRemoveAllNodeToRetire(ShmemPtrT subMergeList);

void DbServerDebugerClearRetire(ShmemPtrT subMergeList);

inline static void SetStatusMergeSubDataNodeTuple(ShmemPtrT nodePtr, TupleAddr tuple)
{
    StatusMergeNodeBaseT *dataNode = (StatusMergeNodeBaseT *)DbShmPtrToAddr(nodePtr);
    if (dataNode == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "nodePtr is novalid");
        return;
    }
    dataNode->tuple = tuple;
}

StmgNodeTypeE DbServerGetPrevNodeType(ShmemPtrT subMergeList, ShmemPtrT subCursor);
StmgNodeTypeE DbServerGetNextNodeType(ShmemPtrT subMergeList, ShmemPtrT subCursor);

Status DbServerGetDataStats(ShmemPtrT subMergeListPtr, ShmemPtrT subCursor, uint64_t *beforeCnt, uint64_t *afterCnt);

// TEMP UNUSED
uint32_t PubSubGetPid(ShmemPtrT subMergeList);

void DbServerClearRetire(ShmemPtrT subMergeList);

/*
此处node不加锁原因：
1.若加锁，合并订阅消费Node时需要短暂的放锁（否则会死锁），但放锁操作会导致合并订阅消费流程出错。
2.不加node锁可以保证正确性。所有改node上数据（tupleAddr,
isWaiting标记位）的操作都在后台缩容流程，无其他修改处，可以不加锁。
*/
static inline void SeHandleStatusMergeNode(StatusMergeNodeBaseT *curNode)
{
    curNode->isWaiting = false;
}

/**
 * 设置节点消费状态，并发上使用StatusMergeNodeBaseT的spinlock，主要控制客户端读流程
 * 和gc流程并发，可以复用baseNode的status，考虑到结构体有padding，以及status过多不容易控制
 */
static inline void SeCursorNodeSetConsuming(StatusMergeNodeBaseT *cursorNode, bool isConsuming)
{
    DB_POINTER(cursorNode);
    cursorNode->isConsuming = isConsuming;
}

static inline bool SeCursorNodeIsConsuming(StatusMergeNodeBaseT *cursorNode)
{
    DB_POINTER(cursorNode);
    return cursorNode->isConsuming;
}

typedef void (*StMgListRetireListCallback)(
    StatusMergeListT *statusMergeList, DbMemCtxT *ctx, uint64_t hpPtrArr[], uint32_t len);

#ifdef __cplusplus
}
#endif

#endif
