/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: se_define.h该头文件主要定义存储引擎内的一些结构体定义
 * Author: yangenle
 * Create: 2020-8-12
 */
#ifndef SE_DEFINE_H
#define SE_DEFINE_H

#include "adpt_types.h"
#include "db_mem_context.h"
#include "se_instance.h"
#include "adpt_spinlock.h"
#include "db_resource_session_pub.h"
#include "db_internal_error.h"
#include "db_inter_process_mgr.h"
#include "db_se_heap_serialization.h"
#include "db_inter_process_lock.h"
#include "db_inter_process_rwlatch.h"
#include "db_inter_process_rwlock.h"
#include "adpt_memory.h"
#include "se_page.h"
#include "se_recovery.h"
#include "se_redo.h"

#ifdef __cplusplus
extern "C" {
#endif

#define SE_INSTANCE_MAGIC_NUM 0xface22face22face
#define HOT_FUN_READ __attribute__((section(".text.hot.StorageRead")))
#define SECTION_FUN_OPTIMISTICTRX __attribute__((section(".text.hot.OptimisticTrx")))

#define HOT_FUN_RC __attribute__((section(".text.hot.StorageReadCommit")))

#define AUTO_DB_RW_LATCH_WLL(idxCtx, latch, op)                \
    do {                                                       \
        bool isLabelLatchMode = IndexIsLabelLatchMode(idxCtx); \
        DbRWLatchWLL(isLabelLatchMode, latch);                 \
        op;                                                    \
        DbRWUnlatchWLL(isLabelLatchMode, latch);               \
    } while (0)

typedef uint32_t BlockNumber;

typedef struct TagSeLinkListEnds {  // 链表的两头
    uint32_t head;
    uint32_t tail;
} SeListEndsT;

typedef struct FileMapMgr FileMapMgrT;

/*
 * 非线程安全的上下文
 */
typedef struct TagSeRunCtxT {
    bool isInited;
    bool isPersistence;       // 是否持久化模式，用于性能优化
    uint16_t instanceId;      // the instance id of storage
    uint32_t trxLockTimeOut;  // 连接级别的事务锁超时时间（ms）
    DbSessionCtxT resSessionCtx;
    void *sessionMemCtx;  // session (dynamic) memory ctx
    void *reserveMemCtx;  // 紧急场景下的备用 memory ctx，默认赋值为 seEscapeMemCtx
    void *seIns;          // the storage instance of handle, SeInstanceT
    void *trxMgr;
    void *trx;
    void *undoCtx;
    void *redoCtx;
    void *pageMgr;
    void *mdMgr;  // 内存模式下(mdMgr == pageMgr)
    void *lockAqcPool;
    void *lockNotifyPool;
    void *lockTable;
    void *rsmCtx;  // 保留内存rsmem ctx
    void *activeTrxIds;
    uint32_t trxIdsSize;
    bool ddlRegistered;
    bool swapCtx;
} SeRunCtxT;

inline static void DbSpinLockLL(bool isLabelLatchMode, DbSpinLockT *spinLock)
{
    if (SECUREC_LIKELY(!isLabelLatchMode)) {
        DbSpinLockOwner(DB_SPINLOCK_OWNER, spinLock);
    }
}

inline static bool DbSpinTryLockLL(bool isLabelLatchMode, DbSpinLockT *spinLock)
{
    if (SECUREC_LIKELY(!isLabelLatchMode)) {
        return DbSpinTryLockOwner(DB_SPINLOCK_OWNER, spinLock);
    }
    return true;
}

inline static void DbSpinUnlockLL(bool isLabelLatchMode, DbSpinLockT *spinLock)
{
    if (SECUREC_LIKELY(!isLabelLatchMode)) {
        DbSpinUnlock(spinLock);
    }
}

inline static void DbRWLatchRLL(bool isLabelLatchMode, DbLatchT *latch)
{
    if (!isLabelLatchMode) {
        DbRWLatchR(latch);
    }
}

inline static void DbRWUnlatchRLL(bool isLabelLatchMode, DbLatchT *latch)
{
    if (!isLabelLatchMode) {
        DbRWUnlatchR(latch);
    }
}

inline static void DbRWLatchWLL(bool isLabelLatchMode, DbLatchT *latch)
{
    if (!isLabelLatchMode) {
        DbRWLatchW(latch);
    }
}

inline static void DbRWUnlatchWLL(bool isLabelLatchMode, DbLatchT *latch)
{
    if (!isLabelLatchMode) {
        DbRWUnlatchW(latch);
    }
}

typedef struct TagCkptCtxT CkptCtxT;
typedef struct TagBufpoolMgrT BufpoolMgrT;
typedef struct TagSpcCompressMgrT SpcCompressMgrT;

#pragma pack(4)
typedef struct TagSeTrmIdCtrl {
    DbSpinLockT lock;
    uint32_t trmId;
} SeTrmIdCtrlT;

typedef struct TagSeInstanceT {
    uint64_t magicNum;
    uint32_t seTopShmMemCtxId;
    uint32_t dataMemMgrCtxId;
    uint32_t heapShmMemCtxId;
    uint32_t seAppShmMemCtxId;
    uint32_t clusteredHashShmMemCtxId;
#ifdef ART_CONTAINER
    uint32_t clusteredArtShmMemCtxId;
#endif
    uint32_t hashIndexShmMemCtxId;
    uint32_t hashLinklistShmMemCtxId;
    uint32_t indexArtShmMemCtxId;
#ifdef FEATURE_SIMPLEREL
    uint32_t indexTTreeShmMemCtxId;
#endif
    uint32_t hcShmMemCtxId;
    uint32_t chainedHashShmMemCtxId;
    uint32_t chainedHashShmMemCtxIdForConflict;
#ifdef FEATURE_HAC
    uint32_t hacShmMemCtxId;
#endif
    uint32_t fixedHeapShmMemCtxId;
    uint32_t stMgPubSubShmMemCtxId;
    uint16_t instanceId;
    bool isLockMgrInited;
    bool undoPurgerStarted;
    bool dafPurgerStarted;
    bool isUpgrading;  // 标记当前是否处于原地升级状态
    uint8_t rsmMigrationVersion;
    uint8_t reserve;
    ShmemPtrT devMgrShm;
    ShmemPtrT rsmBlockMgrShm;
    ShmemPtrT spaceMgrShm;
    ShmemPtrT rsmSpaceMgrShm;
    ShmemPtrT devMemCtxShm;
    ShmemPtrT trxMgrShm;
    ShmemPtrT undoSpaceShm;
    ShmemPtrT lockTblShmPtr;
    ShmemPtrT resColMgrShm;
    ShmemPtrT lockMgrShm;
    ShmemPtrT pageDescArray;
    ShmemPtrT ckptCtxShm;
    ShmemPtrT redoCtxShm;
    ShmemPtrT coreCtrlCtxShm;
    ShmemPtrT bufPoolShm;
    SeTrmIdCtrlT trmIdCtrl;
    void *seServerMemCtx;          // dynamic memory context private to the storage engine instance, only use in server!
    void *seEscapeMemCtx;          // 极限场景下回滚和提交预留内存, 用作逃生通道
    void *undoPurgerCtx;           // undo purger ctx
    void *dafPurgerCtx;            // daf purger ctx
    DbSpinLockT lockMgrInitLatch;  // 保护isLockMgrInited并发更新，控制一个存储实例仅创建一次锁池
    DbSpinLockT purgerStartLatch;  // 保护undoPurgerStarted并发更新，控制一个存储实例仅启动一个purger线程
    DbSpinLockT dafPurgerStartLatch;  // 保护dafPurgerStarted并发更新，控制一个存储实例仅启动一个purger线程
    StorageTypeE storageType;
    RecoveryStateE recoveryState;
    SeConfigT seConfig;
    RedoMgrT *redoMgr;
    CkptCtxT *ckptCtx;
    BufpoolMgrT *bufpoolMgr;
    StDatabaseT *db;
    DbInstanceHdT dbInstance;
    void *pageMgr;
    void *mdMgr;     // 内存模式下(mdMgr == pageMgr)
    void *duMemMgr;  // 不为null时表示当前为bufferpool与dumemdata双持久化形态(此时pageMgr为bufpool)，其他情况下为null。
    MemUtilsT memUtils;  // ckpt、redo、coreCtrlPage等使用，minKv共享内存模式为fileMapMemCtx, 其它为seTopMemCtx
    FileMapMgrT *fileMapMgr;  // 非终端不会使用
    DbSpinlockFnT seLockFn;
    InterProcRWLatchUtilT utils;
    DbInterProcSpinRWLockFnT seRWLockFn;
    bool needVfd;
    void *vfdMgr;
#ifdef FEATURE_LPASMEM
    DbOamapT *lpasIndexMap;
    void *adpt;
#endif
    void *loadTableMgr;
    void *trxLatchCtrl;
} SeInstanceT;
#pragma pack()
// 带锁的变量，需要字节对齐
static_assert((offsetof(SeInstanceT, trmIdCtrl) % sizeof(uint32_t)) == 0,
    "field trmIdCtrl in SeInstanceT must be aligned by 4 bytes");
static_assert(
    (offsetof(SeTrmIdCtrlT, lock) % sizeof(uint32_t)) == 0, "field lock in SeTrmIdCtrlT must be aligned by 4 bytes");
static_assert((offsetof(SeInstanceT, lockMgrInitLatch) % sizeof(uint32_t)) == 0,
    "field lockMgrInitLatch in SeInstanceT must be aligned by 4 bytes");
static_assert((offsetof(SeInstanceT, purgerStartLatch) % sizeof(uint32_t)) == 0,
    "field purgerStartLatch in SeInstanceT must be aligned by 4 bytes");
static_assert((offsetof(SeInstanceT, dafPurgerStartLatch) % sizeof(uint32_t)) == 0,
    "field dafPurgerStartLatch in SeInstanceT must be aligned by 4 bytes");

static inline StatusInter SeInitTrmIdCtrl(SeInstanceT *seIns, uint32_t trmId)
{
    SeTrmIdCtrlT *ctrl = &seIns->trmIdCtrl;
    DbSpinInit(&ctrl->lock);
    ctrl->trmId = trmId + 1;  // 加1后再赋值，保证从新的id开始分配
    return STATUS_OK_INTER;
}

/* *
 * @brief 存储桩接口, 获取一个fileId(自增)
 */
SO_EXPORT_FOR_TS uint32_t SeGetNewTrmId(SeInstanceT *seIns);

static inline void SeSaveMaxTrmId(SeInstanceT *seIns, uint32_t trmId)
{
    SeTrmIdCtrlT *ctrl = &seIns->trmIdCtrl;
    ctrl->trmId = DB_MAX(trmId, ctrl->trmId);
}

StatusInter SeInitWithConfig(SeInstanceT *seIns, const SeConfigT *config);

StorageTypeE SeGetStorageType(void);

void SeSetMiniKvFeature(bool hasFeature);
SO_EXPORT_FOR_TS bool SeHasMiniKvFeature(void);

SO_EXPORT_FOR_TS StatusInter SeRegisterReplayFunc(SeInstanceT *seInsPtr);

static inline bool SeInsMagicNumIsValid(const SeInstanceT *seIns)
{
    return seIns->magicNum == SE_INSTANCE_MAGIC_NUM;
}

#ifdef __cplusplus
}
#endif

#endif
