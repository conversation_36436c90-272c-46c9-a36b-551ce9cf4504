/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: lfs redo接口头文件，属于base中的lfs模块
 * Create: 2023-7-27
 */
#ifndef SE_LFSMGR_REDO_AM_H
#define SE_LFSMGR_REDO_AM_H

#include "se_define.h"
#include "se_lfsmgr.h"

#ifdef __cplusplus
extern "C" {
#endif
typedef struct FsmUpdatedSlotInfo FsmUpdatedSlotInfoT;
typedef struct FsmSlotPara FsmSlotParaT;
typedef struct FsmPageHead FsmPageHeadT;
typedef void (*InitMgrFunc)(LfsMgrT *mgr);
typedef void (*FragmentMgrFunc)(LfsMgrT *mgr);
typedef void (*UpdateSlotFunc)(
    PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t fsmPageIdx, uint32_t slotId, FsmListNodeT *slot);
typedef void (*UpdateMgrPageCntFunc)(LfsMgrT *mgr);
typedef StatusInter (*GetBlockByExistSlotFunc)(PageMgrT *pageMgr, LfsMgrT *mgr, FsmUpdatedSlotInfoT *unlinkedSlotInfo,
    bool reUseUpperPage, PageIdT dataPageId, uint32_t slotIdx);
typedef void (*GetBlockByExtentFsmSlotFunc)(PageMgrT *pageMgr, LfsMgrT *mgr, bool allocNewFsmPage,
    FsmSlotParaT *slotPara, PageAddrT addr, FsmPageHeadT *fsmPageHead);
typedef StatusInter (*SetBlockFreeSpaceFunc)(PageMgrT *pageMgr, LfsMgrT *mgr, FsmUpdatedSlotInfoT *targetSlot,
    uint32_t newListId, bool relink, FsmListT *oldLinkFsmList);
typedef void (*ReleaseUpperPageFunc)(
    PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t fsmPageIdx, uint32_t slotId, FsmListNodeT *slot);
typedef void (*ModifyFsmPageTableFunc)(
    LfsMgrT *mgr, uint32_t fsmPageType, PageIdT curLfsPageAddr, uint32_t pageIdx, PageIdT tableOrFsmPageId);
typedef void (*AllocNewPageTableFunc)(
    LfsMgrT *mgr, uint32_t fsmPageType, PageIdT curLfsPageAddr, uint32_t tableSlotIdx, PageIdT tablePageId);
typedef struct {
    InitMgrFunc initMgrFunc;
    FragmentMgrFunc fragmentMgrFunc;
    UpdateSlotFunc updateSlotFunc;
    UpdateMgrPageCntFunc updateMgrPageCntFunc;
    GetBlockByExistSlotFunc getBlockByExistSlotFunc;
    GetBlockByExtentFsmSlotFunc getBlockByExtentFsmSlotFunc;
    SetBlockFreeSpaceFunc setBlockFreeSpaceFunc;
    ReleaseUpperPageFunc releaseUpperPageFunc;
    ModifyFsmPageTableFunc modifyFsmPageTableFunc;
    AllocNewPageTableFunc allocNewPageTableFunc;
} FsmRedoAmFunc;

void LfsRedoInit(SeInstanceT *seIns);
SO_EXPORT_FOR_TS void FsmSetRedoAmFunc(const FsmRedoAmFunc *am);

#ifdef __cplusplus
}
#endif

#endif
