/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: heap redo am公开调用接口
 */
#ifndef SE_HEAP_REDO_AM_H
#define SE_HEAP_REDO_AM_H
#include "se_define.h"
#include "se_heap_base.h"
#include "se_heap_page.h"
#include "se_heap_batch.h"
#include "se_redo.h"
#ifdef __cplusplus
extern "C" {
#endif

typedef struct TagRedoUpdateParams {
    const HeapRunCtxT *heapRunCtx;
    HpItemPointerT addr;
    uint32_t redoOpType;
    HpPageRowOpInfoT *opInfo;
    bool needBuf;  // 有时page变更并不要 buf数据
} HeapRedoUpdateParams;

typedef struct TagRedoDeleteParams {
    const HeapRunCtxT *heapRunCtx;
    HpItemPointerT addr;
    uint32_t redoOpType;
    uint32_t flags;
    RowTrxInfoT trxInfo;
} HeapRedoDeleteParams;

typedef struct StHeapRedoAm {
    void (*redoForFixPageInitFunc)(PageIdT pageAddr, const HFPageCfgT *pageCfg);
    void (*redoForSchemaChangeFunc)(HFPageHeadT *pageHead, HFPageCfgT *pageCfg, uint8_t type);
    void (*redoForVarPageInitFunc)(PageIdT *addr, PageSizeT slotEntendSize);
    void (*redoForFullPageFunc)(HeapRunCtxT *ctx, HVPageHeadT *pageHead);
    void (*redoForInsertFunc)(HeapRunCtxT *ctx, HpItemPointerT *addr, uint32_t redoOpType, HpPageRowOpInfoT *opInfo);
    void (*redoForBatchInsertFunc)(
        HeapRunCtxT *ctx, PageIdT pageId, uint64_t rollPtr, HpPageGroupRowInfoT *groupRow, uint32_t fsmAvailSize);
    void (*redoForUpdateFunc)(HeapRedoUpdateParams *params);
    void (*redoForDeleteFunc)(HeapRedoDeleteParams *params);
    void (*redoForCreateFunc)(PageIdT heapAddr, HeapT *heap);
    void (*redoForModifyFunc)(uint8_t *buf, uint32_t bufSize, PageIdT *addr, uint16_t offset);
    void (*redoForRollbackImplFunc)(
        HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchCurRowInfo, bool rollbackNormalRow, uint8_t *buf, uint32_t bufSize);
    void (*redoForRowHeadFunc)(HeapRunCtxT *ctx, PageHeadT *pageHead, uint32_t type, uint32_t offset, uint32_t size);
    StatusInter (*redoForInsertOrUpdateLobRowFunc)(HpRunHdlT heapRunHdl, HpPageRowOpInfoT *opInfo);
    void (*redoForBindSliceSubRowToDirRowFunc)(HpRunHdlT heapRunHdl, HpItemPointerT dirRowPtr, HpSliceRowDirT *dirRow);
    void (*redoForBindDstRowFunc)(HpRunHdlT heapRunHdl, HeapRowInfoT *srcRowInfo);
    void (*redoForUpdateDirRowListPtrFunc)(
        HpRunHdlT heapRunHdl, RedoLogTypeE type, HpItemPointerT originPtr, HpItemPointerT listPtr);
    void (*redoForDelSliceSubRowFunc)(HpRunHdlT heapRunHdl, HeapRowInfoT *dirRowInfo);
} HeapRedoAmT;

void HeapRedoInit(SeInstanceT *seIns);
SO_EXPORT_FOR_TS void HeapRedoSetAmStruct(HeapRedoAmT *am);

#ifdef __cplusplus
}
#endif

#endif
