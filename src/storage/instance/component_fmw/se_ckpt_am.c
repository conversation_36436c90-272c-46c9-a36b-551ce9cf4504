/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: provide checkpoint am interface
 */

#include "db_dyn_load.h"
#include "se_define.h"
#include "se_ckpt.h"
#include "se_ckpt_inner.h"

static CkptAmFuncT g_ckptAm = {0};
typedef void (*CkptAmInitFunc)(void);

void CkptSetAmFunc(const CkptAmFuncT *ckptAmFunc)
{
    DB_POINTER(ckptAmFunc);
    g_ckptAm = *ckptAmFunc;
}

Status CkptInit(SeInstanceT *seIns)
{
    CkptAmInitFunc ckptAmInitFunc = (CkptAmInitFunc)DbDynLoadGetFunc(COMPONENT_PERSISTENCE, "checkpoint");
    if (ckptAmInitFunc == NULL) {
        DB_LOG_INFO("checkpoint off");
        return GMERR_OK;
    }
    ckptAmInitFunc();
    StatusInter ret = g_ckptAm.ckptInitFunc(seIns);
    return DbGetExternalErrno(ret);
}

// wait:是否要等ckpt执行完后再返回  timeoutMs:最多等待时间, unit:ms(0表示一直等待)
Status CkptTrigger(SeInstanceT *seInsPtr, CkptModeT mode, bool wait, uint32_t timeoutMs)
{
    if (g_ckptAm.ckptTriggerImplFunc == NULL) {
        return GMERR_OK;
    }

    StatusInter ret = g_ckptAm.ckptTriggerImplFunc(seInsPtr, mode, wait, timeoutMs);
    return DbGetExternalErrno(ret);
}

void CkptDestroy(SeInstanceT *seInsPtr)
{
    if (g_ckptAm.ckptDestroyFunc == NULL || seInsPtr->ckptCtx == NULL) {
        return;
    }

    g_ckptAm.ckptDestroyFunc(seInsPtr);
}

void CkptEnableDigestUpdate(SeInstanceT *seInsPtr)
{
    if (g_ckptAm.ckptEnableDigestUpdateFunc == NULL) {
        return;
    }
    g_ckptAm.ckptEnableDigestUpdateFunc(seInsPtr);
}

Status CkptBindCpuSet(SeInstanceT *seInsPtr, void *cpuSet)
{
    if (g_ckptAm.ckptBindCpuSetFunc == NULL) {
        return GMERR_OK;
    }
    return g_ckptAm.ckptBindCpuSetFunc(seInsPtr, cpuSet);
}
