/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description:
 * Author:
 * Create: 2022.9.27
 */

#include "db_dyn_load.h"
#include "se_define.h"
#include "se_redo.h"
static RedoAmFuncT g_redoAm = {0};

typedef void (*RedoInitFuncType)(SeInstanceT *seIns);

StatusInter RedoInit(SeInstanceHdT seIns)
{
    RedoInitFuncType redoInitFunc = (RedoInitFuncType)DbDynLoadGetFunc(COMPONENT_PERSISTENCE, "redo");
    if (redoInitFunc == NULL) {
        DB_LOG_INFO("feat:persistence redo off");
        return STATUS_OK_INTER;
    }
    redoInitFunc(seIns);
    StatusInter ret = RedoMgrCreate(seIns);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    return RedoReplayFuncRegister(seIns);
}

StatusInter RedoMgrCreate(SeInstanceHdT seIns)
{
    if (g_redoAm.redoMgrCreateFunc != NULL) {
        return g_redoAm.redoMgrCreateFunc(seIns);
    }
    return STATUS_OK_INTER;
}

StatusInter RedoMgrLoadFiles(SeInstanceHdT seIns)
{
    if (g_redoAm.redoMgrLoadFilesFunc != NULL) {
        return g_redoAm.redoMgrLoadFilesFunc(seIns);
    }
    return STATUS_OK_INTER;
}

void RedoMgrUnloadFiles(SeInstanceHdT seIns)
{
    if (g_redoAm.redoMgrUnloadFilesFunc != NULL) {
        g_redoAm.redoMgrUnloadFilesFunc(seIns);
    }
}

Status RedoBindCpuSet(RedoMgrT *redoMgr, void *cpuSet)
{
    if (g_redoAm.redoBindCpuSetFunc != NULL) {
        return g_redoAm.redoBindCpuSetFunc(redoMgr, cpuSet);
    }
    return GMERR_OK;
}

const RedoStatT *RedoGetStat(RedoMgrT *redoMgr)
{
    if (g_redoAm.redoGetStatFunc != NULL) {
        return g_redoAm.redoGetStatFunc(redoMgr);
    }
    return NULL;
}

void RedoSetAmFunc(const RedoAmFuncT *redoCompent)
{
    DB_POINTER(redoCompent);
    g_redoAm = *redoCompent;
}

ALWAYS_INLINE void RedoLogBegin(RedoRunCtxT *redoCtx)
{
    if (g_redoAm.redoLogBeginFunc != NULL) {
        g_redoAm.redoLogBeginFunc(redoCtx);
    }
}

ALWAYS_INLINE StatusInter RedoLogEnd(RedoRunCtxT *redoCtx, bool isLogCommit)
{
    if (g_redoAm.redoLogEndFunc != NULL) {
        return g_redoAm.redoLogEndFunc(redoCtx, isLogCommit);
    }
    return STATUS_OK_INTER;
}

ALWAYS_INLINE StatusInter CtrlRedoLogBegin(RedoRunCtxT *redoCtx)
{
    if (g_redoAm.ctrlRedoLogBeginFunc != NULL) {
        return g_redoAm.ctrlRedoLogBeginFunc(redoCtx);
    }
    return STATUS_OK_INTER;
}

ALWAYS_INLINE StatusInter CtrlRedoLogEnd(RedoRunCtxT *redoCtx, bool isLogCommit)
{
    if (g_redoAm.ctrlRedoLogEndFunc != NULL) {
        return g_redoAm.ctrlRedoLogEndFunc(redoCtx, isLogCommit);
    }
    return STATUS_OK_INTER;
}

ALWAYS_INLINE void SeSetCurRedoCtx(RedoRunCtxT *redoCtx)
{
    if (g_redoAm.seSetCurRedoCtxFunc != NULL) {
        g_redoAm.seSetCurRedoCtxFunc(redoCtx);
    }
}

ALWAYS_INLINE void SeSetCurRedoCtxForTls(void *tlsValue)
{
    RedoRunCtxT *redoCtx = (RedoRunCtxT *)tlsValue;
    if (g_redoAm.seSetCurRedoCtxFunc != NULL) {
        g_redoAm.seSetCurRedoCtxFunc(redoCtx);
    }
}

ALWAYS_INLINE RedoRunCtxT *SeGetCurRedoCtx(void)
{
    if (g_redoAm.seGetCurRedoCtxFunc != NULL) {
        return g_redoAm.seGetCurRedoCtxFunc();
    }
    return NULL;
}

StatusInter RedoCtxCreate(RedoMgrT *redoMgr, RedoRunCtxT **redoCtx)
{
    if (g_redoAm.redoCtxCreateFunc != NULL) {
        return g_redoAm.redoCtxCreateFunc(redoMgr, redoCtx);
    }
    return STATUS_OK_INTER;
}

void RedoCtxRelease(RedoRunCtxT *redoCtx)
{
    if (g_redoAm.redoCtxReleaseFunc != NULL) {
        g_redoAm.redoCtxReleaseFunc(redoCtx);
    }
}

StatusInter RedoFlushOnTrxCommit(RedoMgrT *redoMgr)
{
    if (g_redoAm.redoFlushOnTrxCommitFunc != NULL) {
        return g_redoAm.redoFlushOnTrxCommitFunc(redoMgr);
    }
    return STATUS_OK_INTER;
}

StatusInter RedoFlushToCurrPoint(RedoMgrT *redoMgr)
{
    if (g_redoAm.redoFlushToCurrPointFunc != NULL) {
        return g_redoAm.redoFlushToCurrPointFunc(redoMgr);
    }
    return STATUS_OK_INTER;
}

StatusInter RedoMgrDestroy(RedoMgrT *redoMgr)
{
    if (g_redoAm.redoMgrDestroyFunc != NULL) {
        return g_redoAm.redoMgrDestroyFunc(redoMgr);
    }
    return STATUS_OK_INTER;
}

void RedoRunCtxSetTrxId(RedoRunCtxT *redoCtx, uint64_t trxId)
{
    if (g_redoAm.redoRunCtxSetTrxIdFunc != NULL) {
        g_redoAm.redoRunCtxSetTrxIdFunc(redoCtx, trxId);
    }
}

void RedoLogWrite(RedoRunCtxT *redoCtx, int32_t type, const PageIdT *addr, const uint8_t *data, uint32_t size)
{
    if (g_redoAm.redoLogWriteFunc != NULL) {
        g_redoAm.redoLogWriteFunc(redoCtx, type, addr, data, size);
    }
}

void RedoLogAppend(RedoRunCtxT *redoCtx, const uint8_t *data, uint32_t size)
{
    if (g_redoAm.redoLogAppendFunc != NULL) {
        g_redoAm.redoLogAppendFunc(redoCtx, data, size);
    }
}

StatusInter RedoReplayFuncRegister(SeInstanceHdT seIns)
{
    if (g_redoAm.redoReplayRegisterFunc != NULL) {
        return g_redoAm.redoReplayRegisterFunc(seIns);
    }
    return STATUS_OK_INTER;
}

void RedoSetEnable(RedoMgrT *redoMgr, bool enable)
{
    if (g_redoAm.redoSetEnableRegisterFunc != NULL) {
        g_redoAm.redoSetEnableRegisterFunc(redoMgr, enable);
    }
}

SeInstanceHdT RedoGetSeInstance(RedoRunCtxT *redoCtx)
{
    if (g_redoAm.redoGetSeInstance != NULL) {
        return g_redoAm.redoGetSeInstance(redoCtx);
    }
    return NULL;
}

bool RedoIsPriBufEnough(RedoRunCtxT *redoCtx, uint32_t requiredSize)
{
    if (g_redoAm.redoIsPriBufEnoughFunc != NULL) {
        return g_redoAm.redoIsPriBufEnoughFunc(redoCtx, requiredSize);
    }
    return true;
}

uint32_t RedoLogWriteRequiredSize(uint32_t dataSize)
{
    if (g_redoAm.redoLogWriteRequiredSizeFunc != NULL) {
        return g_redoAm.redoLogWriteRequiredSizeFunc(dataSize);
    }
    return 0;
}

uint32_t RedoBufRemainSize(RedoRunCtxT *redoCtx)
{
    if (g_redoAm.redoLogRemainSizeFunc != NULL) {
        return g_redoAm.redoLogRemainSizeFunc(redoCtx);
    }
    return DB_MAX_UINT32;
}
