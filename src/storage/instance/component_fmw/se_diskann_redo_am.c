/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: implementation of diskann redo operations
 * Author: wanyi
 * Create: 2024-02-08
 */

#include "se_diskann_redo_am.h"
#include "db_dyn_load.h"

static DiskAnnIndexRedoAmT g_diskannRedoAm = {0};

void DiskAnnIndexRedoSetAmFunc(const DiskAnnIndexRedoAmT *am)
{
    DB_POINTER(am);
    g_diskannRedoAm = *am;
}

void DiskAnnIndexRedoInit(SeInstanceHdT seIns)
{
    // check whether diskann is enabled
    if (DbDynLoadGetFunc("trm", "diskann") != NULL) {
        void (*diskAnnIndexRedoAmInit)(SeInstanceHdT seIns) =
            DbDynLoadGetFunc(COMPONENT_PERSISTENCE, "diskann_index_redo");
        // check whether the persistence feature of B-tree is enabled
        if (diskAnnIndexRedoAmInit == NULL) {
            DB_LOG_DEBUG("diskann idx redo feature off");
            return;
        }
        diskAnnIndexRedoAmInit(seIns);
    }
}

void DiskAnnRedoForSetEdge(
    const DiskAnnCtxT *ctxt, const DiskAnnRedoUndoParamT *redpParam, const DiskAnnEdgeT *edge, RedoRunCtxT *redoCtx)
{
    if (g_diskannRedoAm.redoSetEdgeFunc != NULL) {
        g_diskannRedoAm.redoSetEdgeFunc(ctxt, redpParam, edge, redoCtx);
    }
}

void DiskAnnRedoForSetNode(const DiskAnnCtxT *ctxt, const DiskAnnNodeT *node, RedoRunCtxT *redoCtx)
{
    if (g_diskannRedoAm.redoSetNodeFunc != NULL) {
        g_diskannRedoAm.redoSetNodeFunc(ctxt, node, redoCtx);
    }
}

void DiskAnnRedoForSetFlag(const DiskAnnCtxT *ctxt, DiskAnnAddrT vertexId, uint32_t flag, RedoRunCtxT *redoCtx)
{
    if (g_diskannRedoAm.redoSetFlagFunc != NULL) {
        g_diskannRedoAm.redoSetFlagFunc(ctxt, vertexId, flag, redoCtx);
    }
}

void DiskAnnRedoForInitMeta(
    const DiskAnnCtxT *ctxt, DiskAnnPageIdT metaPageId, const DiskAnnMetaT *meta, RedoRunCtxT *redoCtx)
{
    if (g_diskannRedoAm.redoInitMetaFunc != NULL) {
        g_diskannRedoAm.redoInitMetaFunc(ctxt, metaPageId, meta, redoCtx);
    }
}

void DiskAnnRedoForResetMeta(
    const DiskAnnCtxT *ctxt, DiskAnnPageIdT metaPageId, const DiskAnnMetaT *meta, RedoRunCtxT *redoCtx)
{
    if (g_diskannRedoAm.redoResetMetaFunc != NULL) {
        g_diskannRedoAm.redoResetMetaFunc(ctxt, metaPageId, meta, redoCtx);
    }
}

void DiskAnnRedoForSetNewSlave(const DiskAnnCtxT *ctxt, const DiskAnnVertexT *vertex, RedoRunCtxT *redoCtx)
{
    if (g_diskannRedoAm.redoSetNewSlaveFunc != NULL) {
        g_diskannRedoAm.redoSetNewSlaveFunc(ctxt, vertex, redoCtx);
    }
}

void DiskAnnRedoForSetNextSlaveId(
    const DiskAnnCtxT *ctxt, DiskAnnAddrT curVertexId, DiskAnnAddrT nextSlaveId, RedoRunCtxT *redoCtx)
{
    if (g_diskannRedoAm.redoSetNextSlaveIdFunc != NULL) {
        g_diskannRedoAm.redoSetNextSlaveIdFunc(ctxt, curVertexId, nextSlaveId, redoCtx);
    }
}

void DiskAnnRedoForSetPreSlaveId(
    const DiskAnnCtxT *ctxt, DiskAnnAddrT curVertexId, DiskAnnAddrT preSlaveId, RedoRunCtxT *redoCtx)
{
    if (g_diskannRedoAm.redoSetPreSlaveIdFunc != NULL) {
        g_diskannRedoAm.redoSetPreSlaveIdFunc(ctxt, curVertexId, preSlaveId, redoCtx);
    }
}

void DiskAnnRedoForRemoveFromNbs(const DiskAnnCtxT *ctxt, const DiskAnnRedoUndoParamT *redoParam, uint32_t idxToRemove,
    uint32_t lastIdxInNbs, RedoRunCtxT *redoCtx)
{
    if (g_diskannRedoAm.redoRemoveFromNbsFunc != NULL) {
        g_diskannRedoAm.redoRemoveFromNbsFunc(ctxt, redoParam, idxToRemove, lastIdxInNbs, redoCtx);
    }
}

void DiskAnnRedoForSetFrozens(const DiskAnnCtxT *ctxt, DiskAnnPageIdT metaPageId, DiskAnnAddrT *frozens,
    uint16_t numFrozens, RedoRunCtxT *redoCtx)
{
    if (g_diskannRedoAm.redoSetFrozensFunc != NULL) {
        g_diskannRedoAm.redoSetFrozensFunc(ctxt, metaPageId, frozens, numFrozens, redoCtx);
    }
}

void DiskAnnRedoForSetPageHead(const DiskAnnCtxT *ctxt, DiskAnnPageIdT pageId, uint8_t *pageHead, RedoRunCtxT *redoCtx)
{
    if (g_diskannRedoAm.redoSetPageHeadFunc != NULL) {
        g_diskannRedoAm.redoSetPageHeadFunc(ctxt, pageId, pageHead, redoCtx);
    }
}

void DiskAnnRedoForSetDiskAnnNodeHdr(
    const DiskAnnCtxT *ctxt, DiskAnnPageIdT pageId, uint8_t *pageHead, RedoRunCtxT *redoCtx)
{
    if (g_diskannRedoAm.redoSetDiskAnnNodeHdrFunc != NULL) {
        g_diskannRedoAm.redoSetDiskAnnNodeHdrFunc(ctxt, pageId, pageHead, redoCtx);
    }
}

void DiskAnnRedoForSetMeta(const DiskAnnCtxT *ctxt, RedoRunCtxT *redoCtx)
{
    if (g_diskannRedoAm.redoSetMeta != NULL) {
        g_diskannRedoAm.redoSetMeta(ctxt, redoCtx);
    }
}

void DiskAnnRedoForResetNode(
    const DiskAnnCtxT *ctxt, DiskAnnAddrT vertexId, uint32_t vertexTypeSize, size_t edgeSize, RedoRunCtxT *redoCtx)
{
    if (g_diskannRedoAm.redoResetNode != NULL) {
        g_diskannRedoAm.redoResetNode(ctxt, vertexId, vertexTypeSize, edgeSize, redoCtx);
    }
}

void DiskAnnRedoForAddToNodeList(
    const DiskAnnCtxT *ctxt, DiskAnnAddrT delAddr, DiskAnnAddrT delVertexId, uint16_t nodeSize, RedoRunCtxT *redoCtx)
{
    if (g_diskannRedoAm.redoAddToNodeList != NULL) {
        g_diskannRedoAm.redoAddToNodeList(ctxt, delAddr, delVertexId, nodeSize, redoCtx);
    }
}
