/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: se_index.c
 * Description: storage index access method source file
 * Author: pengfengbin
 * Create: 2021/4/18
 */

#include "db_dyn_load.h"
#include "se_index_inner.h"
#include "se_heap_hc.h"
#include "se_chained_hash_index.h"
#include "db_timer.h"
#include "se_log.h"
#include "se_daf.h"
#include "dm_data_index_sql.h"

static IdxFuncT g_gmdbIndexMgr[(int)INDEX_TYPE_MAX];

static IdxKeyCmpFunc g_recoveryIdxKeyCmp;

typedef struct IndexAmInitCtx {
    size_t directReadIdxRunCtxSize;
    size_t directReadIdxIterSize;
    // 用途：索引延迟初始化，控制只有一个线程在绑定索引，包括全绑定和单索引绑定
    DbSpinLockT isIndexInitting;
    bool allIndexInited;
} IndexAmInitCtxT;

IndexAmInitCtxT g_indexInitCtx = {
    .directReadIdxRunCtxSize = 0,
    .directReadIdxIterSize = 0,
    .allIndexInited = false,
    .isIndexInitting = DB_SPINLOCK_INIT_VAL,
};

typedef struct IdxFeatureMap {
    char *name;
} IdxFeatureMapT;

static const IdxFeatureMapT g_idxFeatMap[] = {
    [NODE_MEMBER_INDEX] = {NULL},
    [HASH_INDEX] = {"hash_index"},
    [HASH_LINKLIST_INDEX] = {"hash_linklist"},
    [ART_INDEX_LOCAL] = {"art_sorted"},
    [ART_INDEX_HASHCLUSTER] = {"art_hash_cluster"},
    [LPM4_INDEX] = {"lpm4"},
    [LPM6_INDEX] = {"lpm6"},
    [HASHCLUSTER_INDEX] = {"hash_cluster"},
    [CHAINED_HASH_INDEX] = {"chained_hash"},
    [LIST_LOCALHASH_INDEX] = {"list_local_hash"},
    [BTREE_INDEX] = {"btree"},
#ifdef FEATURE_SIMPLEREL
    [TTREE_INDEX] = {"ttree"},
#endif
    [DISKANN_INDEX] = {"diskann"},
    [LPASMEM_INDEX] = {"lpasmem"},
#ifdef FEATURE_HAC
    [HAC_HASH_INDEX] = {"hac_hash"},
    [MULTI_HASH_INDEX] = {"multi_hash"},
#endif
#ifdef FEATURE_VLIVF
    [VLIVF_INDEX] = {"vlivf"},
#endif
};

static PageMgrT *IdxGetPageMgr(SeRunCtxT *seRunCtx, DmIndexTypeE indexType, PageIdT pageId)
{
    SeInstanceT *seIns = seRunCtx->seIns;
    if (seIns->duMemMgr == NULL) {
        // 有可能是直连写的情景，所以使用seRunCtx
        return (PageMgrT *)seRunCtx->pageMgr;
    }
    // 恢复阶段，重建索引流程也走bufferpool即可
    if (DbRecoveryDoing(seIns)) {
        return (PageMgrT *)seRunCtx->pageMgr;
    }
    if (indexType == LPASMEM_INDEX) {
        return seIns->duMemMgr;
    }
    return SeGetPageMgrByPageId(seIns, pageId);
}

// 根据是否支持拿页，idxShmAddr意义不一样
static Status IdxShmAddrToHandleAddr(PageMgrT *pageMgr, ShmemPtrT idxShmAddr, bool isSupportPage, IdxBaseT **idxHandle)
{
    if (isSupportPage) {
        if (pageMgr == NULL) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "pageMgr");
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        uint8_t *page = NULL;
        PageIdT pageId = *(PageIdT *)(&(idxShmAddr));
        if (SECUREC_UNLIKELY(!DbIsPageIdValid(pageId))) {
            DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "to idxBase, inv pageId");
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        StatusInter ret = SeGetPage(pageMgr, pageId, &page, ENTER_PAGE_NORMAL, false);
        if (ret != STATUS_OK_INTER) {
            DB_LOG_ERROR(
                ret, "get page (deviceId : %" PRIu32 ", blockId: %" PRIu32 ")", pageId.deviceId, pageId.blockId);
            return DbGetExternalErrno(ret);
        }
        *idxHandle = (IdxBaseT *)(page + sizeof(PageHeadT));
    } else {
        // 其他不支持页方式拿页索引，通过共享内存addr转化
        *idxHandle = (IdxBaseT *)DbShmPtrToAddr(idxShmAddr);
        if (SECUREC_UNLIKELY(*idxHandle == NULL)) {
            DB_LOG_ERROR(GMERR_NULL_VALUE_NOT_ALLOWED, "idxHandle inval");
            return GMERR_NULL_VALUE_NOT_ALLOWED;
        }
    }
    return GMERR_OK;
}

static inline const char *GetFeatureNameIndexBelongs(DmIndexTypeE indexType)
{
#ifdef FEATURE_HAC
    if (indexType >= HAC_HASH_INDEX && indexType <= MULTI_HASH_INDEX) {
        return COMPONENT_HAC;
    }
#endif
#ifdef TS_MULTI_INST
    if (indexType == BTREE_INDEX) {
        return COMPONENT_BTREE;
    }
#endif
    return COMPONENT_TRM;
}

typedef void (*IndexAmInitFuncT)(void);

void IdxAmInit(void)
{
    DbSpinLock(&g_indexInitCtx.isIndexInitting);
    if (g_indexInitCtx.allIndexInited) {
        DbSpinUnlock(&g_indexInitCtx.isIndexInitting);
        return;
    }
    size_t maxIdxRunCtxSize = g_indexInitCtx.directReadIdxRunCtxSize;
    size_t maxIdxIterSize = g_indexInitCtx.directReadIdxIterSize;
    size_t tmpIdxRunCtxSize = 0;
    size_t tmpIdxIterSize = 0;
    uint32_t featureMapSize = sizeof(g_idxFeatMap) / sizeof(IdxFeatureMapT);
    for (uint32_t i = 0; i < featureMapSize; ++i) {
        char *featureName = g_idxFeatMap[i].name;
        // 可能初始配置为空
        if (featureName == NULL) {
            DB_LOG_INFO("trm idx %d feat name null", i);
            continue;
        }
        // 该索引可能被注册过
        if (g_gmdbIndexMgr[i].idxGetCtxSize != NULL) {
            DB_LOG_INFO("trm idx %d already bind", i);
            continue;
        }
        IndexAmInitFuncT func = DbDynLoadGetFunc(GetFeatureNameIndexBelongs(i), featureName);
        if (func == NULL) {
            DB_LOG_INFO("trm idx %d feat off", i);
            continue;
        }
        func();
        DB_ASSERT(g_gmdbIndexMgr[i].idxGetCtxSize != NULL);
        g_gmdbIndexMgr[i].idxGetCtxSize(&tmpIdxRunCtxSize, &tmpIdxIterSize);
        maxIdxRunCtxSize = DB_MAX(maxIdxRunCtxSize, tmpIdxRunCtxSize);
        maxIdxIterSize = DB_MAX(maxIdxIterSize, tmpIdxIterSize);
    }
    g_indexInitCtx.allIndexInited = true;
    g_indexInitCtx.directReadIdxRunCtxSize = maxIdxRunCtxSize;
    g_indexInitCtx.directReadIdxIterSize = maxIdxIterSize;
    DbSpinUnlock(&g_indexInitCtx.isIndexInitting);
}

static void IdxAmInitType(uint8_t indexType)
{
    // 没有在g_idxFeatMap中，说明是暂不支持
    if (SECUREC_UNLIKELY(indexType >= ELEMENT_COUNT(g_idxFeatMap))) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "init idx:%d not support", indexType);
        return;
    }
    if (g_gmdbIndexMgr[indexType].idxGetCtxSize) {
        return;
    }

    IndexAmInitFuncT func = DbDynLoadGetFunc(GetFeatureNameIndexBelongs(indexType), g_idxFeatMap[indexType].name);
    if (func == NULL) {
        DB_LOG_INFO("trm idx %" PRIu8 " feat off", indexType);
        return;
    }
    DbSpinLock(&g_indexInitCtx.isIndexInitting);
    func();
    size_t tmpIdxRunCtxSize = 0;
    size_t tmpIdxIterSize = 0;
    g_gmdbIndexMgr[indexType].idxGetCtxSize(&tmpIdxRunCtxSize, &tmpIdxIterSize);
    g_indexInitCtx.directReadIdxRunCtxSize = DB_MAX(g_indexInitCtx.directReadIdxRunCtxSize, tmpIdxRunCtxSize);
    g_indexInitCtx.directReadIdxIterSize = DB_MAX(g_indexInitCtx.directReadIdxIterSize, tmpIdxIterSize);
    DbSpinUnlock(&g_indexInitCtx.isIndexInitting);
}

static inline Status IdxTypeCheck(uint8_t indexType, const char *funcName)
{
    if (SECUREC_UNLIKELY(indexType >= ELEMENT_COUNT(g_gmdbIndexMgr))) {
        DB_LOG_AND_SET_LASERR(
            GMERR_RESTRICT_VIOLATION, "get %s func, idxType %d exceeds arrayNum", funcName, (int)indexType);
        return GMERR_RESTRICT_VIOLATION;
    }
    return GMERR_OK;
}

void IdxAmFuncRegister(uint8_t indexType, const IdxFuncT *const idxFunc)
{
    Status ret = IdxTypeCheck(indexType, "idx am register");
    if (ret != GMERR_OK) {
        return;
    }
    g_gmdbIndexMgr[indexType] = *idxFunc;
}

bool SeIndexIsSupportTableLoad(DmIndexTypeE indexType)
{
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    const IdxFuncT *idxFunc = &g_gmdbIndexMgr[indexType];
    if (SECUREC_UNLIKELY(idxFunc->idxTableLoad == NULL)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "(Idx) idx type %" PRIu8 " not supported TableLoad op", indexType);
        return false;
    }
    return true;
}

Status IdxCreate(SeRunCtxHdT seRunCtx, IndexMetaCfgT idxMetaCfg, ShmemPtrT *idxShmAddr)
{
    uint8_t indexType = (uint8_t)idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    if (g_gmdbIndexMgr[indexType].idxCreate == NULL) {
        DB_LOG_INFO("create wanna load idx type: %" PRIu8, indexType);
        IdxAmInitType(indexType);
        // 根据索引类型初始化索引函数，二次校验是否组件化注册成功
        if (g_gmdbIndexMgr[indexType].idxCreate == NULL) {
            DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "create type: %" PRIu8 " not loaded", indexType);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
    }
    DB_POINTER(idxShmAddr);
    Status ret = g_gmdbIndexMgr[indexType].idxCreate(seRunCtx, idxMetaCfg, idxShmAddr);
    DEBUG_EXTRAMSG(EXTRAMSG_LOG_INDEX, "IdxCreate type %" PRIu8 ", ret=%" PRId32, indexType, ret);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create idx, idx type: %" PRIu8, indexType);
    } else {
        DB_LOG_INFO("create idx, Id:%" PRIu32 ", type: %" PRIu8, idxMetaCfg.indexId, indexType);
    }
    return ret;
}

Status IdxDrop(SeRunCtxHdT seRunCtx, uint8_t indexType, ShmemPtrT idxShmAddr)
{
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    if (g_gmdbIndexMgr[indexType].idxDrop == NULL) {
        DB_LOG_INFO("idx type %" PRIu8 " not loaded when drop", indexType);
        IdxAmInitType(indexType);
        if (g_gmdbIndexMgr[indexType].idxDrop == NULL) {
            DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "idx type: %" PRIu8 " load unsucc", indexType);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
    }
    DB_POINTER(seRunCtx);
    return g_gmdbIndexMgr[indexType].idxDrop(seRunCtx, idxShmAddr);
}

void IdxCommitDrop(SeRunCtxHdT seRunCtx, uint8_t indexType, ShmemPtrT idxShmAddr)
{
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    if (g_gmdbIndexMgr[indexType].idxCommitDrop == NULL) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "drop type:%" PRIu8 " not loaded", indexType);
        return;
    }
    DB_POINTER(seRunCtx);
    g_gmdbIndexMgr[indexType].idxCommitDrop(seRunCtx, idxShmAddr);
}

Status IdxTruncate(SeRunCtxHdT seRunCtx, uint8_t indexType, ShmemPtrT idxShmAddr)
{
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    if (g_gmdbIndexMgr[indexType].idxTruncate == NULL) {
        DB_LOG_INFO("idx type: %" PRIu8 " not loaded when truncate", indexType);
        IdxAmInitType(indexType);
        if (g_gmdbIndexMgr[indexType].idxTruncate == NULL) {
            DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "idx type: %" PRIu8 " load unsucc", indexType);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
    }
    return g_gmdbIndexMgr[indexType].idxTruncate(seRunCtx, idxShmAddr);
}

void IdxCommitTruncate(SeRunCtxHdT seRunCtx, uint8_t indexType, ShmemPtrT idxShmAddr)
{
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    if (g_gmdbIndexMgr[indexType].idxCommitTruncate == NULL) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "truncate type:%" PRIu8 " not loaded", indexType);
        return;
    }
    g_gmdbIndexMgr[indexType].idxCommitTruncate(seRunCtx, idxShmAddr);
}

Status IdxAlloc(SeRunCtxHdT seRunCtxPtr, DmIndexTypeE indexType, IndexCtxT **idxCtx)
{
    DB_POINTER2(seRunCtxPtr, idxCtx);
    size_t idxRunCtxSize = 0;
    size_t idxIterSize = 0;
    if (indexType >= (DmIndexTypeE)ELEMENT_COUNT(g_gmdbIndexMgr)) {
        // 只有直连读/Undo Commit场景下会走入该分支，而直连读/Undo Commit indexType入参必须为INDEX_TYPE_MAX
        DB_ASSERT(indexType == INDEX_TYPE_MAX);
        // 这个时候需要全量绑定索引组件化接口
        if (!g_indexInitCtx.allIndexInited) {
            IdxAmInit();
        }
        idxRunCtxSize = g_indexInitCtx.directReadIdxRunCtxSize;
        idxIterSize = g_indexInitCtx.directReadIdxIterSize;
    } else {
        if (g_gmdbIndexMgr[indexType].idxGetCtxSize == NULL) {
            DB_LOG_INFO("alloc wanna load idx type: %" PRIu8 "", (uint8_t)indexType);
            // 根据索引类型初始化索引函数，二次校验是否组件化注册成功
            IdxAmInitType((uint8_t)indexType);
            if (g_gmdbIndexMgr[indexType].idxGetCtxSize == NULL) {
                DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "alloc type: %" PRIu8 " has not loaded", (uint8_t)indexType);
                return GMERR_FEATURE_NOT_SUPPORTED;
            }
        }
        g_gmdbIndexMgr[indexType].idxGetCtxSize(&idxRunCtxSize, &idxIterSize);
    }
#ifndef FEATURE_SIMPLEREL  // V1 TTree索引为0, 使用的是TTreeHeadT
    DB_ASSERT(idxRunCtxSize != 0);
    DB_ASSERT(idxIterSize != 0);
#endif
    size_t allocSize = sizeof(IndexCtxT) + idxRunCtxSize + idxIterSize;
    /* IdxRelease释放 */
    IndexCtxT *idxCtxTmp = DbDynMemCtxAlloc(seRunCtxPtr->sessionMemCtx, allocSize);

    if (SECUREC_UNLIKELY(idxCtxTmp == NULL)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_OUT_OF_MEMORY, "Unable to alloc index run context, size:%" PRIu32 "", (uint32_t)allocSize);
        return GMERR_OUT_OF_MEMORY;
    }
    idxCtxTmp->isLookupOldRecord = false;
    idxCtxTmp->idxRunCtx = (IndexRunCtxT *)(idxCtxTmp + 1);
    (void)memset_s(idxCtxTmp->idxRunCtx, idxRunCtxSize + idxIterSize, 0, idxRunCtxSize + idxIterSize);
    idxCtxTmp->idxOpenCfg.seRunCtx = seRunCtxPtr;
    idxCtxTmp->idxAllocType = (uint8_t)indexType;
    idxCtxTmp->idxMetaCfg.idxType = indexType;
    idxCtxTmp->hasOpened = false;
    *idxCtx = idxCtxTmp;
    return GMERR_OK;
}

Status IdxOpenInner(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    uint8_t indexType = (uint8_t)(idxCtx->idxMetaCfg.idxType);
    if (indexType == NODE_MEMBER_INDEX || indexType >= ELEMENT_COUNT(g_gmdbIndexMgr)) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "unable to open index type:%" PRIu8, indexType);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    Status ret = g_gmdbIndexMgr[indexType].idxOpen(idxCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "open wrong, idx type: %" PRIu8, indexType);
    } else {
        idxCtx->hasOpened = true;  // open成功，置为true；close时置为false
    }
    return ret;
}

// 读索引内容前需先调用该接口
// 返回GMERR_NO_DATA说明直连读场景，但索引未被open，索引没数据
inline static Status IdxTryOpenBeforeRead(IndexCtxT *idxCtx)
{
    DB_POINTER2(idxCtx, idxCtx->idxOpenCfg.seRunCtx);
    if (SECUREC_LIKELY(idxCtx->constructedWhenOpen)) {
        return GMERR_OK;
    }
#if !defined(FEATURE_HASH)
    // 终端场景不涉及直连读
    DB_ASSERT(idxCtx->idxOpenCfg.seRunCtx->resSessionCtx.isDirectRead);
#endif
    Status ret = IdxIsConstructed(idxCtx->idxHandle) ? IdxOpenInner(idxCtx) : GMERR_NO_DATA;
    idxCtx->constructedWhenOpen = ret == GMERR_OK;
    return ret;
}

static bool IsIdxSupportPage(uint8_t indexType)
{
    bool isSupportPage = (indexType == (uint8_t)BTREE_INDEX) || (indexType == (uint8_t)DISKANN_INDEX) ||
                         (indexType == (uint8_t)LPASMEM_INDEX);
#ifdef FEATURE_VLIVF
    isSupportPage |= (indexType == (uint8_t)VLIVF_INDEX);
#endif
    return isSupportPage;
}

bool IsIdxSupportPersist(uint8_t indexType)
{
    // like DmIndexLabelNoNeedRebuild
    return indexType == BTREE_INDEX || DmIndexLabelIsVectorIndex((DmIndexTypeE)indexType);
}

// 大函数拆分for IdxOpen
static inline void IdxLeaveHandlePage(bool isSupportPage, PageMgrT *pageMgr, ShmemPtrT idxShmAddr)
{
    if (isSupportPage) {
        SeLeavePage(pageMgr, *(PageIdT *)(&(idxShmAddr)), false);
    }
}

Status IdxOpen(ShmemPtrT idxShmAddr, const IndexOpenCfgT *idxCfg, IndexCtxT *idxCtx)
{
    DB_POINTER3(idxCtx, idxCtx->idxRunCtx, idxCfg);
    SeRunCtxT *seRunCtxPtr = idxCfg->seRunCtx;
    // 优先按照上层传递的indexType来打开，因为allocType可能是INDEX_TYPE_MAX
    uint8_t indexType = (uint8_t)idxCfg->indexType;
    DB_ASSERT(indexType <= (uint8_t)INDEX_TYPE_MAX);

    uint8_t allocType = idxCtx->idxAllocType;
    // 对照和allocType不一致，则mismatch
    if (indexType != allocType && allocType != (uint8_t)INDEX_TYPE_MAX) {
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, " IdxType try open mismatch with idx type alloc.");
        return GMERR_DATATYPE_MISMATCH;
    }

    // 部分索引是页方式存储tupleAddr，需要通过get/leave page方式来使用idxShmAddr
    bool isSupportPage = IsIdxSupportPage(indexType);
    PageIdT pageId = *(PageIdT *)(&(idxShmAddr));
    PageMgrT *pageMgr = IdxGetPageMgr(seRunCtxPtr, indexType, pageId);

    IdxBaseT *idxHandle = NULL;
    Status ret = IdxShmAddrToHandleAddr(pageMgr, idxShmAddr, isSupportPage, &idxHandle);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "get idx virtual addr when open idx.");
        return ret;
    }
    if (SECUREC_UNLIKELY(idxCtx->hasOpened)) {
        DB_LOG_WARN_AND_SET_LASTERR(
            GMERR_DATATYPE_MISMATCH, "Idx already opened, %" PRIu32 ".", idxHandle->indexCfg.indexId);
    }
    if (idxHandle->indexCfg.idxType > ELEMENT_COUNT(g_gmdbIndexMgr)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_CORRUPTION, "Idx: %" PRIu8 " type, data file corrupt.", indexType);
        IdxLeaveHandlePage(isSupportPage, pageMgr, idxShmAddr);
        return GMERR_DATA_CORRUPTION;
    }
    IndexRunCtxT *runCtx = idxCtx->idxRunCtx;
    *idxCtx = (IndexCtxT){0};
    idxCtx->idxRunCtx = runCtx;
    idxCtx->idxShmAddr = idxShmAddr;
    idxCtx->idxOpenCfg = *idxCfg;
    idxCtx->idxHandle = idxHandle;
    DbSessionCtxT *ctx = &seRunCtxPtr->resSessionCtx;

    idxCtx->idxMetaCfg = idxHandle->indexCfg;
    idxCtx->idxAllocType = allocType;
    bool isConstructed = IdxIsConstructed(idxHandle);
    // 后文没有再使用handle的地方，需要leave页
    IdxLeaveHandlePage(isSupportPage, pageMgr, idxShmAddr);
    if ((ctx->isDirectRead && !idxCfg->openDirectWrite) && !isConstructed) {
        return GMERR_OK;
    }
    idxCtx->constructedWhenOpen = true;
    idxCtx->isInsert = false;

    if ((ret = IdxOpenInner(idxCtx)) != GMERR_OK) {
        return ret;
    }
    return ret;
}

inline void IdxSetIsLookupOldRecord(IndexCtxT *idxCtx, bool isLookupOldRecord)
{
    DB_POINTER(idxCtx);
    idxCtx->isLookupOldRecord = isLookupOldRecord;
}

void IdxClose(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    if (!idxCtx->hasOpened) {
        return;
    }

    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));

    DB_ASSERT(g_gmdbIndexMgr[indexType].idxGetCtxSize != NULL);
    size_t idxRunCtxSize = 0;
    size_t idxIterSize = 0;
    g_gmdbIndexMgr[indexType].idxGetCtxSize(&idxRunCtxSize, &idxIterSize);

    if (idxCtx->constructedWhenOpen) {
        g_gmdbIndexMgr[indexType].idxClose(idxCtx);
    }

    (void)memset_s(idxCtx->idxRunCtx, idxRunCtxSize + idxIterSize, 0, idxRunCtxSize + idxIterSize);
    idxCtx->hasOpened = false;
}

void IdxRelease(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    SeRunCtxT *seRunCtxPtr = idxCtx->idxOpenCfg.seRunCtx;
    DB_ASSERT(seRunCtxPtr);
    // 释放后，外部函数不再访问，未置NULL，无UAF风险
    DbDynMemCtxFree(seRunCtxPtr->sessionMemCtx, idxCtx);
}

ALWAYS_INLINE static bool IndexKeyLenValid(IndexCtxT *idxCtx, uint8_t indexType, uint8_t nullInfoBytes, uint32_t len)
{
#ifdef FEATURE_SIMPLEREL
    if (indexType == TTREE_INDEX) {
        return true;  // 兼容v1 TTree 索引不限制key长度
    }
#endif
#if defined(FEATURE_DISKANN) || defined(FEATURE_LPASMEM)
    if (indexType == DISKANN_INDEX || indexType == LPASMEM_INDEX) {
        DbDataTypeE dataType = DmIndexLabelGetVectorDataType(idxCtx->idxOpenCfg.vlIndexLabel);
        if (DmDataTypeIsFloatVector(dataType)) {
            return len <= DM_MAX_FLOATVECTOR_INDEX_KEY_SIZE + nullInfoBytes;
        } else {
            return len <= DM_MAX_FLOAT16VECTOR_INDEX_KEY_SIZE + nullInfoBytes;
        }
    }
    return len <= SE_IDX_MAX_KEY_LENGTH + nullInfoBytes;
#else
    return len <= SE_IDX_MAX_KEY_LENGTH + nullInfoBytes;
#endif
}

Status IdxPreload(IndexCtxT *idxCtx, void *userData)
{
    DB_POINTER(idxCtx);
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    const IdxFuncT *idxFunc = &g_gmdbIndexMgr[indexType];
    if (SECUREC_UNLIKELY(idxFunc->idxPreload == NULL)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "(Idx) idx type %" PRIu8 " not supported IdxPreload op", indexType);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    return idxFunc->idxPreload(idxCtx, userData);
}

Status IdxTableLoad(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    const IdxFuncT *idxFunc = &g_gmdbIndexMgr[indexType];
    if (SECUREC_UNLIKELY(idxFunc->idxTableLoad == NULL)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "(Idx) idx type %" PRIu8 " not supported TableLoad op", indexType);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    return idxFunc->idxTableLoad(idxCtx);
}

Status IdxLoad(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr)
{
    DB_POINTER(idxCtx);
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    const IdxFuncT *idxFunc = &g_gmdbIndexMgr[indexType];
    if (SECUREC_UNLIKELY(idxFunc->idxLoad == NULL)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "(Idx) idx type %" PRIu8 " not supported IdxLoad op", indexType);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    return idxFunc->idxLoad(idxCtx, idxKey, addr);
}

Status IdxLookup(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr *addr, bool *isFound)
{
    DB_POINTER3(idxCtx, addr, isFound);
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    *isFound = false;
    Status ret = IdxTryOpenBeforeRead(idxCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret == GMERR_NO_DATA ? GMERR_OK : ret;
    }

    if (SECUREC_UNLIKELY(!IndexKeyLenValid(idxCtx, indexType, idxCtx->idxMetaCfg.nullInfoBytes, idxKey.keyLen))) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "indexLen exceeds max keyLen");
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }

    const IdxFuncT *idxFunc = &g_gmdbIndexMgr[indexType];
    if (SECUREC_UNLIKELY(idxFunc->idxLookup == NULL)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "(Index) idx type %" PRIu8 " not supported lookup", indexType);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    ret = idxFunc->idxLookup(idxCtx, idxKey, addr, isFound);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // 索引内部不应该把该错误码传出；排查点：case1.内部未屏蔽；2.内部误用；
        DB_ASSERT(ret != GMERR_NO_DATA);
        uint32_t indexId = idxCtx->idxMetaCfg.indexId;
        DB_LOG_ERROR(ret, "lookup, idx type: %" PRIu8 ", idx id: %" PRIu32 "", indexType, indexId);
    }
    return ret;
}

inline static Status IdxBatchLookupCategorizeProc(IndexCtxT *idxCtx, uint32_t batchNum, IdxBatchLookupParaT *para)
{
    const IdxCategorizeFunc categorizeFunc = para->categorizeFunc;
    for (uint32_t i = 0; i < batchNum; i++) {
        Status ret = categorizeFunc(idxCtx, para->iter, TupleAddr2IdxTupleOrIter(DB_INVALID_ID64), i, IDX_IS_NOT_FOUND);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status IdxBatchLookup(IndexCtxT *idxCtx, IndexKeyT idxKey[], uint32_t batchNum, IdxBatchLookupParaT *para)
{
    DB_POINTER3(idxCtx, idxKey, para);
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));

    if (SECUREC_UNLIKELY(batchNum == 0)) {
        return GMERR_OK;
    }

    Status ret = IdxTryOpenBeforeRead(idxCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret == GMERR_NO_DATA ? IdxBatchLookupCategorizeProc(idxCtx, batchNum, para) : ret;
    }
    const IdxFuncT *idxFunc = &g_gmdbIndexMgr[indexType];
    if (SECUREC_UNLIKELY(idxFunc->idxBatchLookup == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "(Idx) idx type %" PRIu8 " not supported lookup", indexType);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    ret = idxFunc->idxBatchLookup(idxCtx, idxKey, batchNum, para);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // 索引内部不应该把该错误码传出；排查点：case1.内部未屏蔽；2.内部误用；
        DB_ASSERT(ret != GMERR_NO_DATA);
        uint32_t indexId = idxCtx->idxMetaCfg.indexId;
        DB_LOG_ERROR(ret, "batch lookup, idx type: %" PRIu8 ", idx id: %" PRIu32 "", indexType, indexId);
    }
    return ret;
}

inline static bool IndexIsAbnormalStatus(Status ret)
{
    return ret != GMERR_UNIQUE_VIOLATION && ret != GMERR_PRIMARY_KEY_VIOLATION && ret != GMERR_LOCK_NOT_AVAILABLE;
}

Status IdxInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr)
{
    DB_POINTER(idxCtx);
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    if (SECUREC_UNLIKELY(!IndexKeyLenValid(idxCtx, indexType, idxCtx->idxMetaCfg.nullInfoBytes, idxKey.keyLen))) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "idxLen exceeds max keyLen");
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    DB_ASSERT(!idxCtx->idxOpenCfg.seRunCtx->resSessionCtx.isDirectRead);
    idxCtx->idxKey = idxKey;
    idxCtx->isInsert = true;
    Status ret = g_gmdbIndexMgr[indexType].idxInsert(idxCtx, idxKey, addr);
    idxCtx->isInsert = false;
    IDX_DML_EXTRAMSG("Insert", idxKey, addr, idxCtx->idxMetaCfg.indexId, indexType);
    if (SECUREC_LIKELY(ret == GMERR_OK)) {
        return GMERR_OK;
    }
    uint32_t indexId = idxCtx->idxMetaCfg.indexId;
    if (IndexIsAbnormalStatus(ret)) {
        DB_LOG_ERROR(ret, " insert, idx type: %" PRIu8 ", id: %" PRIu32 "", indexType, indexId);
    } else {
        DB_LOG_WARN(ret, " insert, idx type: %" PRIu8 ", id: %" PRIu32 "", indexType, indexId);
    }
    return ret;
}

Status IdxBatchInsert(IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum)
{
    DB_POINTER3(idxCtx, idxKey, addr);
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));

    if (SECUREC_UNLIKELY(batchNum == 0)) {
        return GMERR_OK;
    }

    uint32_t keyLenCache = 0;
    for (register uint32_t i = 0; i < batchNum; i++) {
        if (idxKey[i].keyLen <= keyLenCache) {
            continue;
        }
        if (SECUREC_UNLIKELY(
                !IndexKeyLenValid(idxCtx, indexType, idxCtx->idxMetaCfg.nullInfoBytes, idxKey[i].keyLen))) {
            DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "idxLen exceeds max keyLen");
            return GMERR_PROGRAM_LIMIT_EXCEEDED;
        }
        keyLenCache = idxKey[i].keyLen;
    }
    DB_ASSERT(!idxCtx->idxOpenCfg.seRunCtx->resSessionCtx.isDirectRead);
    idxCtx->isInsert = true;
    Status ret = g_gmdbIndexMgr[indexType].idxBatchInsert(idxCtx, idxKey, addr, batchNum);
    idxCtx->isInsert = false;
    return ret;
}

inline static bool IdxIsDafEnable(IndexCtxT *idxCtx)
{
    return SeIsDafEnableForIndex(idxCtx->idxOpenCfg.seRunCtx, idxCtx->idxOpenCfg.heapHandle, idxCtx->idxMetaCfg.idxType,
        idxCtx->idxMetaCfg.idxConstraint != NON_UNIQUE);
}

Status IdxDelete(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr, IndexRemoveParaT removePara)
{
    DB_POINTER(idxCtx);
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));

    if (SECUREC_UNLIKELY(!IndexKeyLenValid(idxCtx, indexType, idxCtx->idxMetaCfg.nullInfoBytes, idxKey.keyLen))) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "idxLen exceeds max keyLen");
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    DB_ASSERT(!idxCtx->idxOpenCfg.seRunCtx->resSessionCtx.isDirectRead);
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
    if (IdxIsDafEnable(idxCtx)) {
        if (!removePara.isErase && !removePara.isGc) {
            // 延迟标记删除，在正常事务标记删除时走DAF逻辑，在硬件加速场景减少一次软硬通信开销
            return IdxDelayDelete(idxCtx, idxKey, addr);
        } else if (removePara.isGc) {
            return GMERR_OK;
        }
    }
#endif
    Status ret = g_gmdbIndexMgr[indexType].idxDelete(idxCtx, idxKey, addr, removePara);
    IDX_DML_EXTRAMSG("Delete", idxKey, addr, idxCtx->idxMetaCfg.indexId, indexType);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // 索引内部不应该把该错误码传出；排查点：case1.内部未屏蔽；2.内部误用；
        DB_ASSERT(ret != GMERR_NO_DATA);
        uint32_t indexId = idxCtx->idxMetaCfg.indexId;
        DB_LOG_ERROR(ret, "del, idx type: %" PRIu8 ", id: %" PRIu32 "", indexType, indexId);
    }
    return ret;
}

Status IdxBatchDelete(
    IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum, IndexRemoveParaT removePara)
{
    DB_POINTER3(idxCtx, idxKey, addr);
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));

    if (SECUREC_UNLIKELY(batchNum == 0)) {
        return GMERR_OK;
    }

    for (register uint32_t i = 0; i < batchNum; i++) {
        if (SECUREC_UNLIKELY(
                !IndexKeyLenValid(idxCtx, indexType, idxCtx->idxMetaCfg.nullInfoBytes, idxKey[i].keyLen))) {
            DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, " idxLen exceeds max keyLen");
            return GMERR_PROGRAM_LIMIT_EXCEEDED;
        }
    }
    DB_ASSERT(!idxCtx->idxOpenCfg.seRunCtx->resSessionCtx.isDirectRead);
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
    if (IdxIsDafEnable(idxCtx)) {
        if (!removePara.isErase && !removePara.isGc) {
            // 延迟标记删除，在正常事务标记删除时走DAF逻辑，在硬件加速场景减少一次软硬通信开销
            return IdxDelayBatchDelete(idxCtx, idxKey, addr, batchNum);
        } else if (removePara.isGc) {
            return GMERR_OK;
        }
    }
#endif
    Status ret = g_gmdbIndexMgr[indexType].idxBatchDelete(idxCtx, idxKey, addr, batchNum, removePara);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // 索引内部不应该把该错误码传出；排查点：case1.内部未屏蔽；2.内部误用；
        DB_ASSERT(ret != GMERR_NO_DATA);
        uint32_t indexId = idxCtx->idxMetaCfg.indexId;
        DB_LOG_ERROR(ret, "batch del, idx type: %" PRIu8 ", idx id: %" PRIu32 "", indexType, indexId);
    }
    return ret;
}
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
Status IdxUpdateForDaf(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo, IndexRemoveParaT removePara)
{
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    Status ret;
    // 延迟标记删除，在正常事务标记删除时走DAF逻辑，在硬件加速场景减少一次软硬通信开销
    if (SECUREC_UNLIKELY(!removePara.isErase && IdxIsDafEnable(idxCtx))) {
        ret = IdxDelayDelete(idxCtx, updateInfo.oldIdxKey, updateInfo.oldAddr);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            uint32_t indexId = idxCtx->idxMetaCfg.indexId;
            DB_LOG_ERROR(ret, "idx delay del, idx type: %" PRIu8 ", id: %" PRIu32 "", indexType, indexId);
            return ret;
        }
        idxCtx->isInsert = true;
        ret = g_gmdbIndexMgr[indexType].idxInsert(idxCtx, updateInfo.newIdxKey, updateInfo.newAddr);
    } else {
        idxCtx->isInsert = true;
        ret = g_gmdbIndexMgr[indexType].idxUpdate(idxCtx, updateInfo, removePara);
    }
    return ret;
}
#endif

Status IdxUpdate(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo, IndexRemoveParaT removePara)
{
    DB_POINTER(idxCtx);
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    idxCtx->idxKey = updateInfo.newIdxKey;
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
    Status ret = IdxUpdateForDaf(idxCtx, updateInfo, removePara);
#else
    idxCtx->isInsert = true;
    Status ret = g_gmdbIndexMgr[indexType].idxUpdate(idxCtx, updateInfo, removePara);
#endif
    idxCtx->isInsert = false;
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        uint32_t indexId = idxCtx->idxMetaCfg.indexId;
        DB_LOG_ERROR(ret, "update, idx type: %" PRIu8 ", id: %" PRIu32 "", indexType, indexId);
    }
    return ret;
}

#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
Status IdxBatchUpdateForDaf(
    IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo[], uint32_t batchNum, IndexRemoveParaT removePara)
{
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    Status ret;
    // 延迟标记删除，在正常事务标记删除时走DAF逻辑，在硬件加速场景减少一次软硬通信开销
    if (SECUREC_UNLIKELY(!removePara.isErase && IdxIsDafEnable(idxCtx))) {
        IndexKeyT *newIdxKey =
            DbDynMemCtxAlloc(idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx, sizeof(IndexKeyT) * batchNum);
        HpBatchOutT *batchOut =
            DbDynMemCtxAlloc(idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx, sizeof(HpBatchOutT) * batchNum);
        for (uint32_t i = 0; i < batchNum; ++i) {
            ret = IdxDelayDelete(idxCtx, updateInfo[i].oldIdxKey, updateInfo[i].oldAddr);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DbDynMemCtxFree(idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx, newIdxKey);
                DbDynMemCtxFree(idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx, batchOut);
                uint32_t indexId = idxCtx->idxMetaCfg.indexId;
                DB_LOG_ERROR(ret, "idx delay del, idx type: %" PRIu8 ", id: %" PRIu32 "", indexType, indexId);
                return ret;
            }
            newIdxKey[i] = updateInfo[i].newIdxKey;
            batchOut[i].addrOut = updateInfo[i].newAddr;
        }
        idxCtx->isInsert = true;
        ret = g_gmdbIndexMgr[indexType].idxBatchInsert(idxCtx, newIdxKey, batchOut, batchNum);
        DbDynMemCtxFree(idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx, newIdxKey);
        DbDynMemCtxFree(idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx, batchOut);
    } else {
        idxCtx->isInsert = true;
        ret = g_gmdbIndexMgr[indexType].idxBatchUpdate(idxCtx, updateInfo, batchNum, removePara);
    }
    return ret;
}
#endif

Status IdxBatchUpdate(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo[], uint32_t batchNum, IndexRemoveParaT removePara)
{
    DB_POINTER2(idxCtx, updateInfo);
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    if (SECUREC_UNLIKELY(batchNum == 0)) {
        return GMERR_OK;
    }
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
    Status ret = IdxBatchUpdateForDaf(idxCtx, updateInfo, batchNum, removePara);
#else
    idxCtx->isInsert = true;
    Status ret = g_gmdbIndexMgr[indexType].idxBatchUpdate(idxCtx, updateInfo, batchNum, removePara);
#endif
    idxCtx->isInsert = false;
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        uint32_t indexId = idxCtx->idxMetaCfg.indexId;
        DB_LOG_ERROR(ret, "batch del, idx type: %" PRIu8 ", id: %" PRIu32 "", indexType, indexId);
    }
    return ret;
}

Status IdxEndBatchModify(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    if (g_gmdbIndexMgr[indexType].idxEndBatchModify == NULL) {
        uint32_t indexId = idxCtx->idxMetaCfg.indexId;
        DB_LOG_AND_SET_LASERR(
            GMERR_FEATURE_NOT_SUPPORTED, " EndBatchModify, idx type: %" PRIu8 ", id: %" PRIu32 "", indexType, indexId);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return g_gmdbIndexMgr[indexType].idxEndBatchModify(idxCtx);
}

SEC_IDX_READ_FUNC Status IdxBeginScan(IndexCtxT *idxCtx, IndexScanCfgT scanCfg, IndexScanItrT *iter)
{
    DB_POINTER2(idxCtx, iter);

    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));

    Status ret = IdxTryOpenBeforeRead(idxCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret == GMERR_NO_DATA ? GMERR_OK : ret;
    }

    if (SECUREC_UNLIKELY(scanCfg.leftKey != NULL && !IndexKeyLenValid(idxCtx, indexType,
                                                        idxCtx->idxMetaCfg.nullInfoBytes, scanCfg.leftKey->keyLen))) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "indexLen exceeds max keyLen");
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    if (SECUREC_UNLIKELY(scanCfg.rightKey != NULL && !IndexKeyLenValid(idxCtx, indexType,
                                                         idxCtx->idxMetaCfg.nullInfoBytes, scanCfg.rightKey->keyLen))) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "indexLen exceeds max keyLen");
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    ret = g_gmdbIndexMgr[indexType].idxBeginScan(idxCtx, scanCfg, iter);
    IDX_DML_EXTRAMSG("Begin Scan Left", *scanCfg.leftKey, 0, idxCtx->idxMetaCfg.indexId, indexType);
    IDX_DML_EXTRAMSG("Begin Scan Right", *scanCfg.rightKey, 0, idxCtx->idxMetaCfg.indexId, indexType);
    if (SECUREC_UNLIKELY(ret != GMERR_OK && ret != GMERR_ANN_INDEX_SCAN_DONE)) {
        // 索引内部不应该把该错误码传出；排查点：case1.内部未屏蔽；2.内部误用；
        DB_ASSERT(ret != GMERR_NO_DATA);
        uint32_t indexId = idxCtx->idxMetaCfg.indexId;
        DB_LOG_ERROR(ret, "begin scan, idx type: %" PRIu8 ", id: %" PRIu32 "", indexType, indexId);
    }
    return ret;
}

SEC_IDX_READ_FUNC Status IdxScan(IndexCtxT *idxCtx, IndexScanItrT iter, HpTupleAddr *addr, bool *isFound)
{
    DB_POINTER3(idxCtx, addr, isFound);

    if (!idxCtx->constructedWhenOpen) {
        *isFound = false;
        return GMERR_OK;
    }

    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    Status ret = g_gmdbIndexMgr[indexType].idxScan(idxCtx, iter, addr, isFound);
    if (SECUREC_UNLIKELY(ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE)) {
        // 索引内部不应该把该错误码传出；排查点：case1.内部未屏蔽；2.内部误用；
        DB_ASSERT(ret != GMERR_NO_DATA);
        uint32_t indexId = idxCtx->idxMetaCfg.indexId;
        DB_LOG_ERROR(ret, "scan, idx type: %" PRIu8 ", id: %" PRIu32, indexType, indexId);
    }
    return ret;
}

ALWAYS_INLINE void IdxEndScan(IndexCtxT *idxCtx, IndexScanItrT iter)
{
    DB_POINTER2(idxCtx, iter);
    if (!idxCtx->constructedWhenOpen) {
        return;
    }
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
#ifndef NDEBUG
    IndexScanItrT iterPtr = IdxGetIterator(idxCtx);
    DB_ASSERT(iterPtr == iter);
#endif
    if (g_gmdbIndexMgr[indexType].idxEndScan != NULL) {
        g_gmdbIndexMgr[indexType].idxEndScan(idxCtx, iter);
    }
}

Status IdxScanItrSetDirection(IndexCtxT *idxCtx, IndexScanItrT iter, IndexScanDirectionE scanDirection)
{
    if (idxCtx == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "idxCtx");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    if (indexType >= ELEMENT_COUNT(g_gmdbIndexMgr)) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "type:%d", indexType);
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    if (g_gmdbIndexMgr[indexType].idxSetDirection == NULL) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Idx SetDirection: idx:(%d)", indexType);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    Status ret = g_gmdbIndexMgr[indexType].idxSetDirection(idxCtx, iter, scanDirection);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set direction");
    }
    return ret;
}

Status IdxUndoInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr)
{
    DB_POINTER(idxCtx);
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));

    if (SECUREC_UNLIKELY(!IndexKeyLenValid(idxCtx, indexType, idxCtx->idxMetaCfg.nullInfoBytes, idxKey.keyLen))) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "indexLen exceeds max keyLen");
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    idxCtx->idxKey = idxKey;
    Status ret = g_gmdbIndexMgr[indexType].idxUndoInsert(idxCtx, idxKey, addr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        uint32_t indexId = idxCtx->idxMetaCfg.indexId;
        DB_LOG_ERROR(ret, "undo insert wrong, idx type: %" PRIu8 ", idx id:  %" PRIu32 "", indexType, indexId);
    }
    return ret;
}

Status IdxUndoRemove(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr)
{
    DB_POINTER(idxCtx);
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
    if (IdxIsDafEnable(idxCtx)) {
        // DAF场景下，no need to do anything
        return GMERR_OK;
    }
#endif
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));

    if (SECUREC_UNLIKELY(!IndexKeyLenValid(idxCtx, indexType, idxCtx->idxMetaCfg.nullInfoBytes, idxKey.keyLen))) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, " idxLen exceeds max keyLen");
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    idxCtx->idxKey = idxKey;
    Status ret = g_gmdbIndexMgr[indexType].idxUndoRemove(idxCtx, idxKey, addr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        uint32_t indexId = idxCtx->idxMetaCfg.indexId;
        DB_LOG_ERROR(ret, "undo remove, idx type: %" PRIu8 ", idx id:  %" PRIu32 "", indexType, indexId);
    }
    return ret;
}

Status IdxUndoUpdate(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo, IndexRemoveParaT removePara)
{
    DB_POINTER(idxCtx);
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
    if (IdxIsDafEnable(idxCtx)) {
        // DAF场景下，只要删除新插入的索引
        DB_ASSERT(removePara.isErase);
        return g_gmdbIndexMgr[indexType].idxDelete(idxCtx, updateInfo.oldIdxKey, updateInfo.oldAddr, removePara);
    }
#endif
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    idxCtx->idxKey = updateInfo.newIdxKey;
    idxCtx->isInsert = true;
    Status ret = g_gmdbIndexMgr[indexType].idxUndoUpdate(idxCtx, updateInfo, removePara);
    idxCtx->isInsert = false;
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        uint32_t indexId = idxCtx->idxMetaCfg.indexId;
        DB_LOG_WARN(ret, "undo update, idx type: %" PRIu8 ", idx id:  %" PRIu32 "", indexType, indexId);
    }
    return ret;
}

Status IdxGetKeyCount(IndexCtxT *idxCtx, IndexKeyT idxKey, uint64_t *count)
{
    DB_POINTER2(idxCtx, count);
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    *count = 0;
    Status ret = IdxTryOpenBeforeRead(idxCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret == GMERR_NO_DATA ? GMERR_OK : ret;
    }

    if (SECUREC_UNLIKELY(!IndexKeyLenValid(idxCtx, indexType, idxCtx->idxMetaCfg.nullInfoBytes, idxKey.keyLen))) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "idxLen exceeds max keyLen");
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }

    if (SECUREC_UNLIKELY(g_gmdbIndexMgr[indexType].idxGetKeyCount == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
            "(Index) index type %" PRIu8 " not supported get key count opertation", indexType);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    idxCtx->idxKey = idxKey;
    ret = g_gmdbIndexMgr[indexType].idxGetKeyCount(idxCtx, idxKey, count);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // 索引内部不应该把该错误码传出；排查点：case1.内部未屏蔽；2.内部误用；
        DB_ASSERT(ret != GMERR_NO_DATA);
        uint32_t indexId = idxCtx->idxMetaCfg.indexId;
        DB_LOG_ERROR(ret, "get key count wrong, idx type: %" PRIu8 ", idx id:  %" PRIu32 "", indexType, indexId);
    }
    return ret;
}

Status IdxStatView(DmIndexTypeE indexType, ShmemPtrT idxShmAddr, uint16_t instanceId, IndexStatisticsT *idxStat)
{
    DB_POINTER(idxStat);
    DB_ASSERT((uint8_t)indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    DmIndexTypeE type = indexType;
    if (indexType == ART_INDEX_HASHCLUSTER) {
        idxStat->artIndex.isHashcluster = true;
        type = ART_INDEX_LOCAL;
    } else {
        idxStat->artIndex.isHashcluster = false;
    }
    if (g_gmdbIndexMgr[type].idxStatView == NULL) {
        DB_LOG_INFO("statview wanna load idx type: %" PRIu8 "", (uint8_t)type);
        IdxAmInitType((uint8_t)type);
        // 根据索引类型初始化索引函数，二次校验是否组件化注册成功
        if (g_gmdbIndexMgr[type].idxStatView == NULL) {
            DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "statview type: %" PRIu8 " has not loaded", (uint8_t)type);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
    }

    Status ret = g_gmdbIndexMgr[type].idxStatView(idxShmAddr, instanceId, idxStat);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "stat view wrong, idx type: %" PRIu8, (uint8_t)type);
    }
    return ret;
}

Status IdxViewGetPageSize(uint8_t indexType, ShmemPtrT idxShmAddr, uint16_t instanceId, uint64_t *idxPageSize)
{
    DB_POINTER(idxPageSize);
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    Status ret = g_gmdbIndexMgr[indexType].idxGetPageSize(idxShmAddr, instanceId, idxPageSize);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get page size wrong, idx type: %" PRIu8, indexType);
    }
    return ret;
}

Status IdxScaleIn(IndexCtxT *idxCtx, IndexScaleInCfgT *idxScaleCfg)
{
    DB_POINTER2(idxCtx, idxScaleCfg);
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    if (g_gmdbIndexMgr[indexType].idxScaleIn == NULL) {
        DB_LOG_DEBUG("(Index) index type %" PRIu8 " not supported scale-in opertation", indexType);
        return GMERR_OK;
    }
    Status ret = g_gmdbIndexMgr[indexType].idxScaleIn(idxCtx, idxScaleCfg);
    if (ret != GMERR_OK) {
        uint32_t indexId = idxCtx->idxMetaCfg.indexId;
        DB_LOG_ERROR(ret, "scale in wrong, idx type: %" PRIu8 ", idx id:  %" PRIu32 "", indexType, indexId);
    }
    return ret;
}

Status IdxGetEstimateMemSize(
    uint16_t instanceId, IndexMetaCfgT idxMetaCfg, uint64_t count, uint32_t keyLen, uint64_t *size)
{
    DB_POINTER(size);
    uint8_t indexType = (uint8_t)idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));
    if (g_gmdbIndexMgr[indexType].idxGetEstimateMemSize == NULL) {
        DB_LOG_INFO("est load idx type: %" PRIu8 "", indexType);
        IdxAmInitType(indexType);
        // 根据索引类型初始化索引函数，二次校验是否组件化注册成功
        if (g_gmdbIndexMgr[indexType].idxGetEstimateMemSize == NULL) {
            DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "estimate type: %" PRIu8 " not loaded", indexType);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
    }

    Status ret = g_gmdbIndexMgr[indexType].idxGetEstimateMemSize(instanceId, idxMetaCfg, count, keyLen, size);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get estimate memsize, idx type: %" PRIu8, indexType);
    }
    return ret;
}
#ifdef FEATURE_LPASMEM
Status IdxSetExtendKey(IndexCtxT *idxCtx, IndexKeyT idxKey, uint32_t offset, uint8_t *value, uint32_t len)
{
    DB_POINTER2(idxCtx, value);
    uint8_t indexType = (uint8_t)idxCtx->idxMetaCfg.idxType;
    DB_ASSERT(indexType < ELEMENT_COUNT(g_gmdbIndexMgr));

    Status ret = IdxTryOpenBeforeRead(idxCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret == GMERR_NO_DATA ? GMERR_OK : ret;
    }

    if (SECUREC_UNLIKELY(!IndexKeyLenValid(idxCtx, indexType, idxCtx->idxMetaCfg.nullInfoBytes, idxKey.keyLen))) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "indexLen exceeds max keyLen");
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }

    const IdxFuncT *idxFunc = &g_gmdbIndexMgr[indexType];
    if (SECUREC_UNLIKELY(idxFunc->idxSetExtendKey == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
            "(Index) idx type %" PRIu8 " not supported set extend key", indexType);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    ret = idxFunc->idxSetExtendKey(idxCtx, idxKey, offset, value, len);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        uint32_t indexId = idxCtx->idxMetaCfg.indexId;
        DB_LOG_ERROR(ret, "set extend key, idx type: %" PRIu8 ", idx id: %" PRIu32 "", indexType, indexId);
    }
    return ret;
}
#endif

ALWAYS_INLINE IndexScanItrT IdxGetIterator(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    DB_ASSERT(g_gmdbIndexMgr[idxCtx->idxMetaCfg.idxType].idxGetCtxSize != NULL);
    size_t idxRunCtxSize = 0;
    size_t idxIterSize = 0;
    g_gmdbIndexMgr[idxCtx->idxMetaCfg.idxType].idxGetCtxSize(&idxRunCtxSize, &idxIterSize);
    return (IndexScanItrT)(void *)((uintptr_t)idxCtx + sizeof(IndexCtxT) + idxRunCtxSize);
}

#define SE_HT_SHM_CTX_BASE_SIZE (256 * DB_KIBI)
#define SE_HT_SHM_CTX_STEP_SIZE (1 * DB_MEBI)

#define SE_HT_LINKLIST_SHM_CTX_BASE_SIZE (64 * DB_KIBI)
#define SE_HT_LINKLIST_SHM_CTX_STEP_SIZE (1 * DB_MEBI)

#define SE_HC_SHM_CTX_BASE_SIZE (1 * DB_MEBI)
#define SE_HC_SHM_CTX_STEP_SIZE (10 * DB_MEBI)

#define SE_CHAINED_HASH_SHM_CTX_BASE_SIZE (64 * DB_KIBI)
#define SE_CHAINED_HASH_SHM_CTX_STEP_SIZE (1 * DB_MEBI)

#define SE_CHAINED_HASH_SHM_CTX_FOR_CONFLICT_BASE_SIZE (64 * DB_KIBI)
#define SE_CHAINED_HASH_SHM_CTX_FOR_CONFLICT_STEP_SIZE (1 * DB_MEBI)

#define SE_ART_SHM_CTX_BASE_SIZE (512 * DB_KIBI)
#define SE_ART_SHM_CTX_STEP_SIZE (2 * DB_MEBI)

#define SE_TTREE_SHM_CTX_BASE_SIZE (512 * DB_KIBI)
#define SE_TTREE_SHM_CTX_STEP_SIZE (2 * DB_MEBI)

#define SE_SUBS_STMG_SHM_CTX_FOR_CONFLICT_BASE_SIZE (1 * DB_MEBI)
#define SE_SUBS_STMG_SHM_CTX_FOR_STEP_SIZE (2 * DB_MEBI)

#ifdef FEATURE_HAC
#define SE_HAC_SHM_CTX_BASE_SIZE (256 * DB_KIBI)
#define SE_HAC_SHM_CTX_STEP_SIZE (1 * DB_MEBI)
#endif

#define SE_INDEX_MAX_SHM_MEM_CTX_NUM (20)

typedef struct {
    void *idxShmMemCtxs[SE_INDEX_MAX_SHM_MEM_CTX_NUM];
    uint32_t idxShmMemCtxNum;
} IndexShmMemCtxMgrT;

typedef struct IndexShmemCtxInitParam {
    uint32_t ctxId;
    uint32_t baseSize;
    uint32_t stepSize;
    const char *ctxName;
} IndexShmemCtxInitParamT;

StatusInter GetHugePageFromCfg(SeInstanceT *seIns, bool *isHugePage)
{
    DbCfgValueT value = {0};
    Status ret = DbCfgGet(DbGetCfgHandle(seIns->dbInstance), DB_CFG_HUGE_PAGE, &value);
    if (ret != GMERR_OK) {
        return DbGetStatusInterErrno(ret);
    }
    *isHugePage = value.int32Val != 0;
    return STATUS_OK_INTER;
}

void IndexShmMemCtxFree(IndexShmMemCtxMgrT *idxShmMemCtxMgr)
{
    DB_POINTER(idxShmMemCtxMgr);
    for (uint32_t i = 0u; i < idxShmMemCtxMgr->idxShmMemCtxNum; i++) {
        if (idxShmMemCtxMgr->idxShmMemCtxs[i] != NULL) {
            DbDeleteShmemCtx((DbMemCtxT *)idxShmMemCtxMgr->idxShmMemCtxs[i]);
        }
    }
}

void UpdateMaxSizeOfShmemCtxParam(uint32_t instanceId, DbBlockMemParamT *blockParam)
{
    DB_POINTER(blockParam);
    // limitSize为memCtx通过配置参数计算的内存上限，创建的时候maxSize不能超过该值。
    uint64_t limitSize = blockParam->baseSize + (uint64_t)blockParam->stepSize * (DbMemCtxGetBlockPoolMaxSegNum() - 1);
    limitSize = DB_MIN(UINT32_MAX, limitSize);  // 参数数值不超过uint32
    uint64_t maxSize = DbGetTopShmemMaxSize(instanceId);
    blockParam->maxSize = (uint64_t)DB_MIN(maxSize, limitSize);
}

void InitSeIndexShmemCtxParamBase(
    uint16_t instanceId, DbBlockMemParamT *blockParam, uint32_t baseSize, uint32_t stepSize, bool isReused)
{
    DB_POINTER(blockParam);
    blockParam->baseSize = baseSize;
    blockParam->stepSize = stepSize;
    blockParam->isReused = isReused;
    blockParam->allowBigChunk = true;
    UpdateMaxSizeOfShmemCtxParam(instanceId, blockParam);
}

/* 说明
1.  SeClusteredHashShmMemCtx
    用    途: 聚簇容器共享内存申请使用
    生命周期: 长进程级别
    释放策略: 兜底清空
    兜底清空措施: 依赖上层seTopShmMemCtx, server退出时销毁

2.  SeHashFreelistShmMemCtx
    用    途: hashlink索引共享内存申请使用
    生命周期: 长进程级别
    释放策略: 兜底清空
    兜底清空措施: 依赖上层seTopShmMemCtx, server退出时销毁

3.	SeHcShmMemCtx
    用    途: hashcluster索引共享内存申请使用
    生命周期: 长进程级别
    释放策略: 兜底清空
    兜底清空措施: 依赖上层seTopShmMemCtx, server退出时销毁

4.	SeArtShmMemCtx
    用    途: 以ART为底座的索引共享内存申请使用
    生命周期: 长进程级别
    释放策略: 兜底清空
    兜底清空措施: 依赖上层seTopShmMemCtx, server退出时销毁

5.	SeHashShmMemCtx
    用    途: CCEH索引共享内存申请使用
    生命周期: 长进程级别
    释放策略: 兜底清空
    兜底清空措施: 依赖上层seTopShmMemCtx, server退出时销毁

6.	SeChainedHashShmMemCtx
    用    途: ChainHash索引共享内存申请使用
    生命周期: 长进程级别
    释放策略: 兜底清空
    兜底清空措施: 依赖上层seTopShmMemCtx, server退出时销毁

7.	SeCHShmMemCtxForConflict
    用    途: ChainHash冲突节点内存使用
    生命周期: 长进程级别
    释放策略: 兜底清空
    兜底清空措施: 依赖上层seTopShmMemCtx, server退出时销毁
*/

StatusInter IndexCreateIndexShmemCtx(SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx, DbMemCtxArgsT *indexShmMemCtxArgs,
    DbBlockMemParamT *blockParam, IndexShmMemCtxMgrT *idxShmMemCtxMgr)
{
    DB_POINTER5(seIns, seTopShmMemCtx, indexShmMemCtxArgs, blockParam, idxShmMemCtxMgr);
    IndexShmemCtxInitParamT shmCtxInitParam[] = {
        {DB_SE_CLUSTERED_HASH_SHM_CTX_ID, SE_HT_SHM_CTX_BASE_SIZE, SE_HT_SHM_CTX_STEP_SIZE, "SeClusteredHashShmMemCtx"},
#ifdef ART_CONTAINER
        {DB_SE_ART_CONTAINER_SHM_CTX_ID, SE_ART_SHM_CTX_BASE_SIZE, SE_ART_SHM_CTX_STEP_SIZE, "SeArtContainerShmMemCtx"},
#endif
        {DB_SE_HASH_LINKLIST_SHM_CTX_ID, SE_HT_LINKLIST_SHM_CTX_BASE_SIZE, SE_HT_LINKLIST_SHM_CTX_STEP_SIZE,
            "SeHashFreelistShmMemCtx"},
        {DB_SE_HC_SHM_CTX_ID, SE_HC_SHM_CTX_BASE_SIZE, SE_HC_SHM_CTX_STEP_SIZE, "SeHcShmMemCtx"},
        {DB_SE_INDEX_ART_SHM_CTX_ID, SE_ART_SHM_CTX_BASE_SIZE, SE_ART_SHM_CTX_STEP_SIZE, "SeArtShmMemCtx"},
#ifdef FEATURE_SIMPLEREL
        {DB_SE_INDEX_TTREE_SHM_CTX_ID, SE_TTREE_SHM_CTX_BASE_SIZE, SE_TTREE_SHM_CTX_STEP_SIZE, "SeTTreeShmMemCtx"},
#endif
        {DB_SE_HASH_INDEX_SHM_CTX_ID, SE_HT_SHM_CTX_BASE_SIZE, SE_HT_SHM_CTX_STEP_SIZE, "SeHashShmMemCtx"},
        {DB_SE_CHAINED_HASH_SHM_CTX_ID, SE_CHAINED_HASH_SHM_CTX_BASE_SIZE, SE_CHAINED_HASH_SHM_CTX_STEP_SIZE,
            "SeChainedHashShmMemCtx"},
        {DB_SE_CHAINED_HASH_SHM_CTX_FOR_CONFLICT_ID, SE_CHAINED_HASH_SHM_CTX_FOR_CONFLICT_BASE_SIZE,
            SE_CHAINED_HASH_SHM_CTX_FOR_CONFLICT_STEP_SIZE, "SeCHShmMemCtxForConflict"},
        {DB_SE_STMG_PUBSUB_SHMCTX_ID, SE_SUBS_STMG_SHM_CTX_FOR_CONFLICT_BASE_SIZE, SE_SUBS_STMG_SHM_CTX_FOR_STEP_SIZE,
            "SeStMgPubSubMemCtx"},
#ifdef FEATURE_HAC
        {DB_SE_INDEX_HAC_SHM_CTX_ID, SE_HAC_SHM_CTX_BASE_SIZE, SE_HAC_SHM_CTX_STEP_SIZE, "SeHacMemCtx"},
#endif
    };

    static const uint32_t maxParamNum = (uint32_t)ELEMENT_COUNT(shmCtxInitParam);

    StatusInter errorRet = OBJ_NOT_PREREQUISITE_SE_INVALID_POINTER;
    void **idxShmMemCtxs = idxShmMemCtxMgr->idxShmMemCtxs;
    uint32_t *idxShmMemCtxNum = &idxShmMemCtxMgr->idxShmMemCtxNum;
    for (uint32_t i = 0; i < maxParamNum; ++i) {
        indexShmMemCtxArgs->ctxId = shmCtxInitParam[i].ctxId;
        InitSeIndexShmemCtxParamBase(
            seIns->instanceId, blockParam, shmCtxInitParam[i].baseSize, shmCtxInitParam[i].stepSize, true);
        void *idxShmMemCtx =
            DbCreateBlockPoolShmemCtx(seTopShmMemCtx, shmCtxInitParam[i].ctxName, (void *)indexShmMemCtxArgs);
        if (idxShmMemCtx == NULL) {
            return errorRet;
        }
        idxShmMemCtxs[(*idxShmMemCtxNum)++] = idxShmMemCtx;
    }
    seIns->clusteredHashShmMemCtxId = DB_SE_CLUSTERED_HASH_SHM_CTX_ID;
#ifdef ART_CONTAINER
    seIns->clusteredArtShmMemCtxId = DB_SE_ART_CONTAINER_SHM_CTX_ID;
#endif
    seIns->hashLinklistShmMemCtxId = DB_SE_HASH_LINKLIST_SHM_CTX_ID;
    seIns->hcShmMemCtxId = DB_SE_HC_SHM_CTX_ID;
    seIns->indexArtShmMemCtxId = DB_SE_INDEX_ART_SHM_CTX_ID;
#ifdef FEATURE_SIMPLEREL
    seIns->indexTTreeShmMemCtxId = DB_SE_INDEX_TTREE_SHM_CTX_ID;
#endif
    seIns->hashIndexShmMemCtxId = DB_SE_HASH_INDEX_SHM_CTX_ID;
    seIns->chainedHashShmMemCtxId = DB_SE_CHAINED_HASH_SHM_CTX_ID;
    seIns->chainedHashShmMemCtxIdForConflict = DB_SE_CHAINED_HASH_SHM_CTX_FOR_CONFLICT_ID;
    seIns->stMgPubSubShmMemCtxId = DB_SE_STMG_PUBSUB_SHMCTX_ID;
#ifdef FEATURE_HAC
    seIns->hacShmMemCtxId = DB_SE_INDEX_HAC_SHM_CTX_ID;
#endif
    return STATUS_OK_INTER;
}

StatusInter IndexSeShmMemCreate(SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx)
{
    DB_POINTER2(seIns, seTopShmMemCtx);

    DbMemCtxArgsT indexShmMemCtxArgs = {0};
    indexShmMemCtxArgs.instanceId = seIns->seConfig.instanceId;
    // Use private memory context setting
    char param[sizeof(DbBlockMemParamT)] = {0};
    DbBlockMemParamT *blockParam = (DbBlockMemParamT *)param;
    StatusInter ret = GetHugePageFromCfg(seIns, &blockParam->isHugePage);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    blockParam->blkPoolType = BLK_NORMAL;
    blockParam->allowBigChunk = true;
    AlgoParamT algoParam;
    algoParam.blockParam = blockParam;
    indexShmMemCtxArgs.algoParam = &algoParam;
    IndexShmMemCtxMgrT idxShmMemCtxMgr = {0};

    ret = IndexCreateIndexShmemCtx(seIns, seTopShmMemCtx, &indexShmMemCtxArgs, blockParam, &idxShmMemCtxMgr);
    DB_ASSERT(idxShmMemCtxMgr.idxShmMemCtxNum < SE_INDEX_MAX_SHM_MEM_CTX_NUM);
    if (ret != STATUS_OK_INTER) {
        IndexShmMemCtxFree(&idxShmMemCtxMgr);
    }
    return ret;
}

void IdxDmlExtraMsg(const char *format, IndexKeyT key, TupleAddr addr, uint32_t idxId, uint8_t idxType)
{
    char buf[DB_DEFAULT_LOG_SIZE] = {0};
    int32_t err = snprintf_s(buf, DB_DEFAULT_LOG_SIZE, DB_DEFAULT_LOG_SIZE - 1,
        "(IDX DEBUG) %s, rowId:%" PRIu64 ", indexId %" PRIu32 ", indexType %" PRIu8 ". ", format, addr, idxId,
        (uint8_t)idxType);
    if (err < 0) {
        return;
    }
    for (uint32_t i = 0; i < key.keyLen; i++) {
        char tmpStr[DB_DEFAULT_LOG_SIZE] = {0};
        err = snprintf_s(tmpStr, DB_DEFAULT_LOG_SIZE, DB_DEFAULT_LOG_SIZE - 1, "(%" PRIu8 ") ", key.keyData[i]);
        if (err < 0) {
            break;
        }
        err = strcat_s(buf, DB_DEFAULT_LOG_SIZE, tmpStr);
        if (err != EOK) {
            break;
        }
    }
    DEBUG_EXTRAMSG(EXTRAMSG_LOG_INDEX, "%s", buf);
}

void IdxAmUninit(void)
{
    uint32_t indexNum = sizeof(g_gmdbIndexMgr) / sizeof(IdxFuncT);
    for (uint32_t i = 0; i < indexNum; i++) {
        (void)memset_s(&g_gmdbIndexMgr[i], sizeof(IdxFuncT), 0, sizeof(IdxFuncT));
    }
}

void IdxRecoveryCallBackFuncRegister(IdxKeyCmpFunc keyCmpCallback)
{
    g_recoveryIdxKeyCmp = keyCmpCallback;
}

IdxKeyCmpFunc IdxGetRecoveryCallBackFunc(void)
{
    return g_recoveryIdxKeyCmp;
}

SeHpTupleAddrMode IdxGetTupleAddrMode(const SeInstanceT *seIns, bool isUseRsm)
{
    return isUseRsm ? seIns->seConfig.rsmHeapTupleAddrMode : seIns->seConfig.heapTupleAddrMode;
}

IdxFuncT IdxEmptyIdxFunc(void)
{
    IdxFuncT func = {
        .idxCreate = NULL,
        .idxDrop = NULL,
        .idxCommitDrop = NULL,
        .idxTruncate = NULL,
        .idxCommitTruncate = NULL,
        .idxOpen = NULL,
        .idxClose = NULL,
        .idxInsert = NULL,
        .idxBatchInsert = NULL,
        .idxDelete = NULL,
        .idxBatchDelete = NULL,
        .idxUpdate = NULL,
        .idxBatchUpdate = NULL,
        .idxLookup = NULL,
        .idxBatchLookup = NULL,
        .idxBeginScan = NULL,
        .idxScan = NULL,
        .idxEndScan = NULL,
        .idxSetDirection = NULL,
        .idxUndoInsert = NULL,
        .idxUndoRemove = NULL,
        .idxUndoUpdate = NULL,
        .idxGetKeyCount = NULL,
        .idxStatView = NULL,
        .idxGetPageSize = NULL,
        .idxScaleIn = NULL,
        .idxGetEstimateMemSize = NULL,
        .idxGetCtxSize = NULL,
        .idxPreload = NULL,
        .idxTableLoad = NULL,
        .idxLoad = NULL,
    };
    return func;
}
