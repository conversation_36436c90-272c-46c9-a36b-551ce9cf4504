/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description:
 * Author:
 * Create:
 */
#include "se_rsm_block_inner.h"
#include "db_dyn_load.h"
#include "db_log.h"

#ifdef FEATURE_RSMEM
extern const RsmBlockAmT g_gmdbRsmBlockAm;
#else
const RsmBlockAmT g_gmdbRsmBlockAm = {0};
#endif

StatusInter RsmBlockInit(SeInstanceHdT seIns, DbMemCtxT *seTopShmMemCtx, bool isRecovery)
{
    if (!isRecovery) {
        return RsmBlockCreate(seIns, seTopShmMemCtx);
    } else {
        return RsmBlockRecovery(seIns);
    }
}

StatusInter RsmBlockCreate(SeInstanceHdT seIns, DbMemCtxT *seTopShmMemCtx)
{
    if (g_gmdbRsmBlockAm.rsmBlockCreate == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    return g_gmdbRsmBlockAm.rsmBlockCreate(seIns, seTopShmMemCtx);  // RsmBlockCreateImpl
}

StatusInter RsmBlockRecovery(SeInstanceHdT seIns)
{
    if (g_gmdbRsmBlockAm.rsmBlockRecovery == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    return g_gmdbRsmBlockAm.rsmBlockRecovery(seIns);  // RsmBlockRecoveryImpl
}

StatusInter RsmBlockDestroy(SeInstanceHdT seIns, DbMemCtxT *seTopShmMemCtx)
{
    if (g_gmdbRsmBlockAm.rsmBlockDestroy == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    g_gmdbRsmBlockAm.rsmBlockDestroy(seIns, seTopShmMemCtx);  // RsmBlockDestroyImpl
    return STATUS_OK_INTER;
}

StatusInter RsmBlockUpdReserveDevice(RsmBlkMgrHdlT rsmBlockMgr, uint32_t devCnt)
{
    if (g_gmdbRsmBlockAm.rsmBlockUpdReserveDevice == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    return g_gmdbRsmBlockAm.rsmBlockUpdReserveDevice(rsmBlockMgr, devCnt);  // RsmBlockUpdReserveDeviceImpl
}

StatusInter RsmBlockAllocDevice(RsmBlkMgrHdlT rsmBlockMgr, uint32_t tableSpaceId, uint16_t spaceId, uint32_t *devSize)
{
    if (g_gmdbRsmBlockAm.rsmBlockAllocDevice == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    // RsmBlockAllocDeviceImpl
    return g_gmdbRsmBlockAm.rsmBlockAllocDevice(rsmBlockMgr, tableSpaceId, spaceId, devSize);
}

StatusInter RsmBlockUnreserveDevice(RsmBlkMgrHdlT rsmBlockMgr, uint32_t devCnt)
{
    if (g_gmdbRsmBlockAm.rsmBlockUnreserveDevice == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    g_gmdbRsmBlockAm.rsmBlockUnreserveDevice(rsmBlockMgr, devCnt);  // RsmBlockUnreserveDeviceImpl
    return STATUS_OK_INTER;
}

StatusInter RsmBlockReturnAllDevice(RsmBlkMgrHdlT rsmBlockMgr, uint32_t tableSpaceId)
{
    if (g_gmdbRsmBlockAm.rsmBlockReturnDevice == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    g_gmdbRsmBlockAm.rsmBlockReturnDevice(rsmBlockMgr, tableSpaceId);  // RsmBlockReturnAllDeviceImpl
    return STATUS_OK_INTER;
}

StatusInter RsmBlockReturnLastDevice(RsmBlkMgrHdlT rsmBlockMgr, uint32_t rsmBlockId, uint32_t tableSpaceId)
{
    if (g_gmdbRsmBlockAm.rsmBlockReturnLastDevice == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    g_gmdbRsmBlockAm.rsmBlockReturnLastDevice(rsmBlockMgr, rsmBlockId, tableSpaceId);  // RsmBlockReturnLastDeviceImpl
    return STATUS_OK_INTER;
}

StatusInter RsmBlockAllocEntry(
    RsmBlkMgrHdlT rsmBlockMgr, uint32_t tableSpaceId, uint32_t labelId, AllocEntryInfoT *allocEntryInfo)
{
    if (g_gmdbRsmBlockAm.rsmBlockAllocEntry == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    // RsmBlockAllocEntryImpl
    uint8_t *devPtr = g_gmdbRsmBlockAm.rsmBlockAllocEntry(rsmBlockMgr, tableSpaceId, labelId, allocEntryInfo);
    if (devPtr == NULL) {
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    return STATUS_OK_INTER;
}

StatusInter RsmBlockFreeEntry(RsmBlkMgrHdlT rsmBlockMgr, PageIdT addr)
{
    if (g_gmdbRsmBlockAm.rsmBlockFreeEntry == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    g_gmdbRsmBlockAm.rsmBlockFreeEntry(rsmBlockMgr, addr);  // RsmBlockFreeEntryImpl
    return STATUS_OK_INTER;
}

StatusInter RsmBlockCompressTsp(RsmBlkMgrHdlT rsmBlockMgr, uint32_t tableSpaceId, uint32_t rsmBlockId, uint32_t labelId,
    CompressResultT *compressResult)
{
    if (g_gmdbRsmBlockAm.rsmBlockCompressTsp == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    // RsmBlockCompressTspImpl
    return g_gmdbRsmBlockAm.rsmBlockCompressTsp(rsmBlockMgr, tableSpaceId, rsmBlockId, labelId, compressResult);
}

StatusInter RsmBlockCompressTspCheck(RsmBlkMgrHdlT rsmBlockMgr, uint32_t rsmBlockId, CompressCheckInfoT *checkInfo)
{
    if (g_gmdbRsmBlockAm.rsmBlockCompressTspCheck == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    // RsmBlockCompressTspCheckImpl
    return g_gmdbRsmBlockAm.rsmBlockCompressTspCheck(rsmBlockMgr, rsmBlockId, checkInfo);
}

StatusInter RsmBlockGetEntry(RsmBlkMgrHdlT rsmBlockMgr, PageIdT addr, uint8_t **page)
{
    DB_POINTER(page);
    if (g_gmdbRsmBlockAm.rsmBlockGetEntry == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    *page = g_gmdbRsmBlockAm.rsmBlockGetEntry(rsmBlockMgr, addr);  // RsmBlockGetEntryImpl
    if (*page == NULL) {
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    return STATUS_OK_INTER;
}

StatusInter RsmBlockGetMaxDevCnt(RsmBlkMgrHdlT rsmBlockMgr, uint32_t *maxDevCount)
{
    DB_POINTER(maxDevCount);
    if (g_gmdbRsmBlockAm.rsmBlockGetMaxDevCnt == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    *maxDevCount = g_gmdbRsmBlockAm.rsmBlockGetMaxDevCnt(rsmBlockMgr);  // RsmBlockGetMaxDevCntImpl;
    return STATUS_OK_INTER;
}

StatusInter RsmBlockGetRsmSubBlockCntInRsmBlock(RsmBlockMgrT *rsmBlockMgr, uint32_t *rsmSubBlockCntInRsmBlock)
{
    DB_POINTER(rsmSubBlockCntInRsmBlock);
    if (g_gmdbRsmBlockAm.rsmBlockGetRsmSubBlockCntInRsmBlockImpl == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    // RsmBlockGetRsmSubBlockCntInRsmBlockImpl
    *rsmSubBlockCntInRsmBlock = g_gmdbRsmBlockAm.rsmBlockGetRsmSubBlockCntInRsmBlockImpl(rsmBlockMgr);
    return STATUS_OK_INTER;
}

StatusInter RsmBlockGetFreeDeviceCnt(RsmBlkMgrHdlT rsmBlockMgr, uint32_t *freeDevCount)
{
    DB_POINTER(freeDevCount);
    if (g_gmdbRsmBlockAm.rsmBlockGetFreeDeviceCnt == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    *freeDevCount = g_gmdbRsmBlockAm.rsmBlockGetFreeDeviceCnt(rsmBlockMgr);  // RsmBlockGetFreeDeviceCntImpl
    return STATUS_OK_INTER;
}

StatusInter RsmBlockGetCurRsmBlockCnt(RsmBlockMgrT *rsmBlockMgr, uint32_t *curRsmBlockCnt)
{
    DB_POINTER(curRsmBlockCnt);
    if (g_gmdbRsmBlockAm.rsmBlockGetCurRsmBlockCnt == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    *curRsmBlockCnt = g_gmdbRsmBlockAm.rsmBlockGetCurRsmBlockCnt(rsmBlockMgr);  // RsmBlockGetCurRsmBlockCntImpl
    return STATUS_OK_INTER;
}

StatusInter RsmBlockGetDevCntInRsmBlock(RsmBlockMgrT *rsmBlockMgr, uint32_t *deviceCntInRsmBlock)
{
    DB_POINTER(deviceCntInRsmBlock);
    if (g_gmdbRsmBlockAm.rsmBlockGetDevCntInRsmBlock == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    *deviceCntInRsmBlock =
        g_gmdbRsmBlockAm.rsmBlockGetDevCntInRsmBlock(rsmBlockMgr);  // RsmBlockGetDevCntInRsmBlockImpl
    return STATUS_OK_INTER;
}

StatusInter RsmBlockCheckAddr(RsmBlkMgrHdlT rsmBlockMgr, PageIdT addr)
{
    if (g_gmdbRsmBlockAm.rsmBlockCheckAddr == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    return g_gmdbRsmBlockAm.rsmBlockCheckAddr(rsmBlockMgr, addr);  // RsmBlockCheckAddrImpl
}

RsmUndoRecordT *RsmBlockGetRsmUndoRec(RsmBlkMgrHdlT rsmBlockMgr)
{
    if (g_gmdbRsmBlockAm.rsmBlockGetRsmUndoRec == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return NULL;
    }
    // RsmBlockGetRsmUndoRecImpl
    return g_gmdbRsmBlockAm.rsmBlockGetRsmUndoRec(rsmBlockMgr);
}

void RsmBlockSetIsRecovery(RsmBlkMgrHdlT rsmBlockMgr, bool isRecovery)
{
    if (g_gmdbRsmBlockAm.rsmBlockSetIsRecovery == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return;
    }
    // RsmBlockSetIsRecoveryImpl
    return g_gmdbRsmBlockAm.rsmBlockSetIsRecovery(rsmBlockMgr, isRecovery);
}

void RsmBlockUndoAllocBlock(RsmBlkMgrHdlT rsmBlockMgr, uint32_t rsmBlkId, uint32_t freeRsmBlockCnt)
{
    if (g_gmdbRsmBlockAm.rsmBlockUndoAllocBlock == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return;
    }
    // RsmBlockUndoAllocBlockImpl
    g_gmdbRsmBlockAm.rsmBlockUndoAllocBlock(rsmBlockMgr, rsmBlkId, freeRsmBlockCnt);
}

void RsmBlockUndoAllocDev(RsmBlkMgrHdlT rsmBlockMgr, PageIdT addr, uint32_t curDeviceCnt,
    uint32_t firstFreeRsmSubBlockId, uint32_t newFirstFreeRsmSubBlockId)
{
    if (g_gmdbRsmBlockAm.rsmBlockUndoAllocDev == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return;
    }
    // RsmBlockUndoAllocDevImpl
    g_gmdbRsmBlockAm.rsmBlockUndoAllocDev(
        rsmBlockMgr, addr, curDeviceCnt, firstFreeRsmSubBlockId, newFirstFreeRsmSubBlockId);
}

void RsmBlockUndoFreeDev(RsmBlkMgrHdlT rsmBlockMgr, const RsmUndoBlkMgrFreeDevRec *record)
{
    if (g_gmdbRsmBlockAm.rsmBlockUndoFreeDev == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return;
    }
    // RsmBlockUndoFreeDevImpl
    g_gmdbRsmBlockAm.rsmBlockUndoFreeDev(rsmBlockMgr, record);
}

void RsmBlockUndoReserveDev(RsmBlkMgrHdlT rsmBlockMgr, uint32_t freeDevCount)
{
    if (g_gmdbRsmBlockAm.rsmBlockUndoReserveDev == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return;
    }
    // RsmBlockUndoReserveDevImpl
    g_gmdbRsmBlockAm.rsmBlockUndoReserveDev(rsmBlockMgr, freeDevCount);
}

void RsmBlockUndoReuseDev(RsmBlkMgrHdlT rsmBlockMgr, PageIdT addr, uint32_t unBindDeviceCnt)
{
    if (g_gmdbRsmBlockAm.rsmBlockUndoReuseDev == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return;
    }
    // RsmBlockUndoReuseDevImpl
    g_gmdbRsmBlockAm.rsmBlockUndoReuseDev(rsmBlockMgr, addr, unBindDeviceCnt);
}

void RsmBlockUndoAllocEntry(
    RsmBlkMgrHdlT rsmBlockMgr, PageIdT addr, uint32_t isNewPage, uint32_t rsmDevId, uint32_t freeRsmSubBlkCnt)
{
    if (g_gmdbRsmBlockAm.rsmBlockUndoAllocEntry == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return;
    }
    // RsmBlockUndoAllocEntryImpl
    g_gmdbRsmBlockAm.rsmBlockUndoAllocEntry(rsmBlockMgr, addr, isNewPage, rsmDevId, freeRsmSubBlkCnt);
}

void RsmBlockUndoFreeEntry(RsmBlkMgrHdlT rsmBlockMgr, PageIdT addr, uint32_t rsmDevId, uint32_t freeRsmSubBlkCnt)
{
    if (g_gmdbRsmBlockAm.rsmBlockUndoFreeEntry == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return;
    }
    // RsmBlockUndoFreeEntryImpl
    g_gmdbRsmBlockAm.rsmBlockUndoFreeEntry(rsmBlockMgr, addr, rsmDevId, freeRsmSubBlkCnt);
}

void RsmBlockUndoMemcpyEntry(RsmBlkMgrHdlT rsmBlockMgr, const RsmUndoBlkMgrMemcpyEntryRec *record)
{
    if (g_gmdbRsmBlockAm.rsmBlockUndoMemcpyEntry == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return;
    }
    // RsmBlockUndoMemcpyEntryImpl
    g_gmdbRsmBlockAm.rsmBlockUndoMemcpyEntry(rsmBlockMgr, record);
}

StatusInter RsmBlockGetStat(
    RsmBlkMgrHdlT rsmBlockMgr, uint32_t rsmBlockId, DbMemCtxT *dynamicMemCtx, RsmBlockInfoT **rsmBlockStat)
{
    if (g_gmdbRsmBlockAm.rsmBlockGetStat == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    // RsmBlockGetStatImpl
    return g_gmdbRsmBlockAm.rsmBlockGetStat(rsmBlockMgr, rsmBlockId, dynamicMemCtx, rsmBlockStat);
}

bool RsmBlockGetNextUnusedEntry(RsmBlkMgrHdlT rsmBlockMgr, FreeEntryInfoT *entryInfo)
{
    if (g_gmdbRsmBlockAm.rsmBlockGetNextUnusedEntry == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return false;
    }
    // RsmBlockGetNextUnusedEntryImpl
    return g_gmdbRsmBlockAm.rsmBlockGetNextUnusedEntry(rsmBlockMgr, entryInfo);
}

StatusInter RsmBlockRebuildRsmSpace(RsmBlkMgrHdlT rsmBlockMgr, RsmBlockRebuildRsmSpaceFunc func,
    RsmSpacebuildRecoveryMgrFunc buildRecoveryMgrFunc, Handle rsmSpaceMgr)
{
    if (g_gmdbRsmBlockAm.rsmBlockRebuildRsmSpace == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    // RsmBlockRebuildRsmSpaceImpl
    return g_gmdbRsmBlockAm.rsmBlockRebuildRsmSpace(rsmBlockMgr, func, buildRecoveryMgrFunc, rsmSpaceMgr);
}

Status RsmBlockMigration(RsmBlkMgrHdlT rsmBlockMgr, const char *path)
{
    if (g_gmdbRsmBlockAm.rsmBlockMigration == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    // RsmBlockMigrationImpl
    return g_gmdbRsmBlockAm.rsmBlockMigration(rsmBlockMgr, path);
}

void RsmBlockUndoMigration(RsmBlkMgrHdlT rsmBlockMgr, const char *path)
{
    if (g_gmdbRsmBlockAm.rsmBlockUndoMigration == NULL) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "rsm block");
        return;
    }
    // RsmBlockUndoMigrationImpl
    g_gmdbRsmBlockAm.rsmBlockUndoMigration(rsmBlockMgr, path);
}
