/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: The implementation of the B-tree redo interface
 * Author: <PERSON><PERSON>
 * Create: 2022-10-19
 */

#include "se_btree_redo_am.h"
#include "db_dyn_load.h"
#include "db_log.h"

static BTreeIndexRedoAmT g_bTreeRedoAm = {0};

void SeBTreeSetRedoAmFunc(const BTreeIndexRedoAmT *am)
{
    DB_POINTER(am);
    g_bTreeRedoAm = *am;
}

void BTreeIndexRedoInit(SeInstanceHdT seIns)
{
    // check whether B-tree is enabled
    if (DbDynLoadGetFunc(COMPONENT_BTREE, "btree") != NULL) {
        void (*btreeRedoAminitFunc)(SeInstanceHdT seIns) = DbDynLoadGetFunc(COMPONENT_PERSISTENCE, "btree_index_redo");
        // check whether the persistence feature of B-tree is enabled
        if (btreeRedoAminitFunc == NULL) {
            DB_LOG_WARN(GMERR_OK, "btree idx redo off");
            return;
        }
        btreeRedoAminitFunc(seIns);
    }
}

void BTreeRedoForCreate(PageIdT pageId, struct BTree *treeHdr, RedoRunCtxT *redoCtx)
{
    if (g_bTreeRedoAm.redoCreateFunc != NULL) {
        g_bTreeRedoAm.redoCreateFunc(pageId, treeHdr, redoCtx);
    }
}

void BTreeRedoForUpdateStatistics(PageIdT pageId, BTreeIndexStatisticsT *treeIdxStatistics, RedoRunCtxT *redoCtx)
{
    if (g_bTreeRedoAm.redoUpdateStatisticsFunc != NULL) {
        g_bTreeRedoAm.redoUpdateStatisticsFunc(pageId, treeIdxStatistics, redoCtx);
    }
}

void BTreeRedoForInitNode(PageIdT pageId, bool isRoot, RedoRunCtxT *redoCtx)
{
    if (g_bTreeRedoAm.redoInitNodeFunc != NULL) {
        g_bTreeRedoAm.redoInitNodeFunc(pageId, isRoot, redoCtx);
    }
}

void BTreeRedoForUnmark(PageIdT pageId, int16_t slot, HpTupleAddr rowId, uint64_t trxId, RedoRunCtxT *redoCtx)
{
    if (g_bTreeRedoAm.redoUnmarkFunc != NULL) {
        g_bTreeRedoAm.redoUnmarkFunc(pageId, slot, rowId, trxId, redoCtx);
    }
}

void BTreeRedoForInsert(
    PageIdT pageId, int16_t slot, struct BTreeKey *key, struct BTreeEntryHdr *entryHdr, RedoRunCtxT *redoCtx)
{
    if (g_bTreeRedoAm.redoInsertFunc != NULL) {
        g_bTreeRedoAm.redoInsertFunc(pageId, slot, key, entryHdr, redoCtx);
    }
}

void BTreeRedoForTransferInit(PageIdT pageId, bool isRoot, uint8_t level, int16_t count, RedoRunCtxT *redoCtx)
{
    if (g_bTreeRedoAm.redoTransferInitFunc != NULL) {
        g_bTreeRedoAm.redoTransferInitFunc(pageId, isRoot, level, count, redoCtx);
    }
}

void BTreeRedoForTransferAppend(struct BTreeKey *key, struct BTreeEntryHdr *entryHdr, RedoRunCtxT *redoCtx)
{
    if (g_bTreeRedoAm.redoTransferAppendFunc != NULL) {
        g_bTreeRedoAm.redoTransferAppendFunc(key, entryHdr, redoCtx);
    }
}

void BTreeRedoForUpdateChildId(PageIdT pageId, int16_t slot, PageIdT newChildId, RedoRunCtxT *redoCtx)
{
    if (g_bTreeRedoAm.redoUpdateChildIdFunc != NULL) {
        g_bTreeRedoAm.redoUpdateChildIdFunc(pageId, slot, newChildId, redoCtx);
    }
}

void BTreeRedoForUpdateLeafLink(PageIdT pageId, PageIdT newPrevLeafId, PageIdT newNextLeafId, RedoRunCtxT *redoCtx)
{
    if (g_bTreeRedoAm.redoUpdateLeafLinkFunc != NULL) {
        g_bTreeRedoAm.redoUpdateLeafLinkFunc(pageId, newPrevLeafId, newNextLeafId, redoCtx);
    }
}

void BTreeRedoForDeleteRange(PageIdT pageId, int16_t beginSlot, int16_t endSlot, RedoRunCtxT *redoCtx)
{
    if (g_bTreeRedoAm.redoDeleteRangeFunc != NULL) {
        g_bTreeRedoAm.redoDeleteRangeFunc(pageId, beginSlot, endSlot, redoCtx);
    }
}

void BTreeRedoForMarkDelete(PageIdT pageId, int16_t slot, RedoRunCtxT *redoCtx)
{
    if (g_bTreeRedoAm.redoMarkDeleteFunc != NULL) {
        g_bTreeRedoAm.redoMarkDeleteFunc(pageId, slot, redoCtx);
    }
}

void BTreeRedoForDelete(PageIdT pageId, int16_t slot, RedoRunCtxT *redoCtx)
{
    if (g_bTreeRedoAm.redoDeleteFunc != NULL) {
        g_bTreeRedoAm.redoDeleteFunc(pageId, slot, redoCtx);
    }
}

void BTreeRedoForDeleteLeftRange(PageIdT pageId, int16_t beginSlot, int16_t endSlot, RedoRunCtxT *redoCtx)
{
    if (g_bTreeRedoAm.redoDeleteLeftRangeFunc != NULL) {
        g_bTreeRedoAm.redoDeleteLeftRangeFunc(pageId, beginSlot, endSlot, redoCtx);
    }
    // in memory scenario, we don't need redo log, thus treat as normal
}

void BTreeRedoForTransferInsertInit(
    PageIdT pageId, TransInitArgsT transInitArgs, int16_t beginInsertSlot, RedoRunCtxT *redoCtx)
{
    if (g_bTreeRedoAm.redoTransferInsertInitFunc != NULL) {
        g_bTreeRedoAm.redoTransferInsertInitFunc(pageId, transInitArgs, beginInsertSlot, redoCtx);
    }
    // in memory scenario, we don't need redo log, thus treat as normal
}

void BTreeRedoForInsertBigKey(
    PageIdT pageId, uint32_t freeSize, uint8_t *keyData, uint32_t bigKeySize, RedoRunCtxT *redoCtx)
{
    if (g_bTreeRedoAm.redoInsertBigKeyFunc != NULL) {
        g_bTreeRedoAm.redoInsertBigKeyFunc(pageId, freeSize, keyData, bigKeySize, redoCtx);
    }
}

void BTreeRedoForFreeMeta(PageIdT pageId, RedoRunCtxT *redoCtx)
{
    if (g_bTreeRedoAm.redoFreeMeta != NULL) {
        g_bTreeRedoAm.redoFreeMeta(pageId, redoCtx);
    }
}
